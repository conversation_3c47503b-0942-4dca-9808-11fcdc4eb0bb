<?xml version="1.0" encoding="UTF-8"?>
<serviceCatalog
        xmlns="http://service.sankuai.com/1.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://service.sankuai.com/1.0.0
            http://pixel.sankuai.com/repository/releases/com/meituan/apidoc/servicecatalog/1.0.0/servicecatalog-1.0.0.xsd">
    <!-- Service description -->

    <serviceDescs>
        <serviceDesc>
            <appkey>com.sankuai.dzu.tpbase.dztgdetailweb</appkey>
            <name>到综团购详情Web项目</name>
            <description>提供到综团购C端各个站点团购详情页新样式的后端接口，包括不限于主接口、优惠模块等等。</description>
            <scenarios>双平台C端各个站点，包括不限于点评APP、美团APP、小程序、H5页面等等</scenarios>
            <interfaceDescs>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.dianping.mobile.mapi.dztgdetail.action.app.DealImageTextDetailAction</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.dianping.mobile.mapi.dztgdetail.action.app.DzDealBaseAction</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.dianping.mobile.mapi.dztgdetail.action.app.DealPromoModuleAction</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.dianping.mobile.mapi.dztgdetail.action.app.DzGoodReviewAction</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.dianping.mobile.mapi.dztgdetail.action.app.UnifiedActivityModule</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.dianping.mobile.mapi.dztgdetail.action.app.UnifiedGetCouponAction</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.dianping.mobile.mapi.dztgdetail.action.app.UnifiedModuleExtraAction</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.dianping.mobile.mapi.dztgdetail.action.app.UnifiedMoreDealsAction</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.dianping.mobile.mapi.dztgdetail.action.app.UnifiedShopReviewAction</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.dianping.mobile.mapi.dztgdetail.action.h5.DealImageTextDetailH5Action</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.dianping.mobile.mapi.dztgdetail.action.h5.DealPromoModuleH5Action</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.dianping.mobile.mapi.dztgdetail.action.h5.DzDealBaseH5Action</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.dianping.mobile.mapi.dztgdetail.action.h5.DzGoodReviewH5Action</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.dianping.mobile.mapi.dztgdetail.action.h5.UnifiedGetCouponH5Action</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.dianping.mobile.mapi.dztgdetail.action.app.PreSaleCountDownAction</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.dianping.mobile.mapi.dztgdetail.action.app.CollaborativeAction</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.dianping.mobile.mapi.dztgdetail.action.app.DealBaseAction</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.dianping.mobile.mapi.dztgdetail.action.app.GetMtPoiListAction</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.dianping.mobile.mapi.dztgdetail.action.app.ServiceGuaranteeQueryAction</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.dianping.mobile.mapi.dztgdetail.action.app.DealMerchantAdditionalAction</class>
                </interfaceDesc>
            </interfaceDescs>
        </serviceDesc>
    </serviceDescs>

</serviceCatalog>
