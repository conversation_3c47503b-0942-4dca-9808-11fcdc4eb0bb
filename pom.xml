<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.dianping</groupId>
    <artifactId>mapi-dztgdetail-web</artifactId>
    <version>1.0.1</version>
    <packaging>war</packaging>
    <name>mapi-dztgdetail-web</name>
    <properties>
        <env>qa</env>
        <project.encoding>UTF-8</project.encoding>
        <java.src.version>1.8</java.src.version>
        <java.target.version>1.8</java.target.version>
        <deal.common.version>2.1.7</deal.common.version>
        <product-detail-gateway-api.version>2.0.3-SNAPSHOT</product-detail-gateway-api.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>inf-bom</artifactId>
                <version>1.13.1</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>4.1.114.Final</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>servlet-api</artifactId>
                <version>2.5</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.13</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.poi</groupId>
                <artifactId>poi-brand-api</artifactId>
                <version>1.8.19</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.meituan.dataapp</groupId>
                <artifactId>recapilib</artifactId>
                <version>0.2.2.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.meituan.service.mobile.prometheus</groupId>
                <artifactId>deal-client</artifactId>
                <version>1.6.4.25</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>beautycontent.creator.api</artifactId>
                <version>0.0.21</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>pay-common</artifactId>
                <version>1.2.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.gmkt</groupId>
                <artifactId>gmkt-activity-api</artifactId>
                <version>2.2.35</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dp.arts</groupId>
                <artifactId>arts-client</artifactId>
                <version>ES_0.4.22</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.facebook.swift</groupId>
                <artifactId>swift-annotations</artifactId>
                <version>0.23.1-mt-RC5</version>
            </dependency>
            <dependency>
                <groupId>com.facebook.swift</groupId>
                <artifactId>swift-codec</artifactId>
                <version>0.23.1-mt-RC5</version>
            </dependency>
            <dependency>
                <groupId>com.facebook.swift</groupId>
                <artifactId>swift-generator</artifactId>
                <version>0.23.1-mt-RC5</version>
            </dependency>
            <dependency>
                <groupId>com.facebook.swift</groupId>
                <artifactId>swift-idl-parser</artifactId>
                <version>0.23.1-mt-RC5</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.5</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>4.4.13</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j-impl</artifactId>
                <version>2.17.1</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan</groupId>
                <artifactId>mt-service-http</artifactId>
                <version>1.3.22</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan</groupId>
                <artifactId>poros-high-level-client</artifactId>
                <version>0.9.22_ES7</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>4.3.17.RELEASE</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.squirrel</groupId>
                <artifactId>squirrel-async-client</artifactId>
                <version>1.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.swallow</groupId>
                <artifactId>swallow-consumerclient</artifactId>
                <version>0.8.11.9</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.swallow</groupId>
                <artifactId>swallow-producerclient</artifactId>
                <version>0.8.11.9</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.tuangou</groupId>
                <artifactId>dztg-bjwrapper-api</artifactId>
                <version>1.1.10</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-api</artifactId>
                <version>4.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-calcite</artifactId>
                <version>4.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-dao</artifactId>
                <version>4.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-ds-monitor-client</artifactId>
                <version>4.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-tool</artifactId>
                <version>4.1.1</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-framework-bom</artifactId>
                <version>4.3.17.RELEASE</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-bom</artifactId>
                <version>2.17.1</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>deal-bom</artifactId>
                <version>1.0.21</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>20.0</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty</artifactId>
                <version>3.10.6.Final</version>
            </dependency>
            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>3.18.1-GA</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>3.8.0</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>3.8.0</version>
            </dependency>
            <dependency>
                <groupId>org.xerial.snappy</groupId>
                <artifactId>snappy-java</artifactId>
                <version>1.1.2.6</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.rhino</groupId>
                <artifactId>rhino-client</artifactId>
                <version>1.8.0-RC3</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.rhino</groupId>
                <artifactId>rhino-cluster-limiter</artifactId>
                <version>1.8.0-RC3</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.rhino</groupId>
                <artifactId>rhino-extend</artifactId>
                <version>1.8.0-RC3</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83_noneautotype</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>csc-access-facade-api</artifactId>
                <version>0.0.66</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.mockito</groupId>
                        <artifactId>mockito-all</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>deal-detail-api</artifactId>
                <version>2.0.3</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>lion-facade</artifactId>
                <version>2.2.5</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.nib.mkt</groupId>
                <artifactId>common-base</artifactId>
                <version>2.13.70</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai.nibmktproxy</groupId>
                <artifactId>query-client</artifactId>
                <version>1.0.7</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.meituan.mdp.boot</groupId>
                        <artifactId>mdp-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai.oceanus.http</groupId>
                <artifactId>oceanus-http</artifactId>
                <version>1.2.11</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>ts-settle-common-api</artifactId>
                <version>0.1.3</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.deal</groupId>
                <artifactId>deal-voucher-query-api</artifactId>
                <version>1.0.5</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.douhu</groupId>
                <artifactId>douhu-absdk</artifactId>
                <version>2.10.0</version>
            </dependency>
            <!--卡团队接口-->
            <dependency>
                <groupId>com.sankuai.dzcard</groupId>
                <artifactId>dzcard-navigation-api</artifactId>
                <version>0.0.33</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dzcard</groupId>
                <artifactId>dzcard-fulfill-api</artifactId>
                <version>0.0.21</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dealuser</groupId>
                <artifactId>price-display-api</artifactId>
                <version>0.0.183</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.nib.mkt</groupId>
                <artifactId>promotion-api</artifactId>
                <version>2.0.25</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.beauty</groupId>
                <artifactId>beauty-tagsearch-api</artifactId>
                <version>1.1.9</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.deal</groupId>
                <artifactId>product-shelf-query-api</artifactId>
                <version>1.9.5</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-annotations</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-databind</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.meituan.mdp.boot</groupId>
                        <artifactId>mdp-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.meituan.mdp.boot</groupId>
                        <artifactId>mdp-boot-starter-log</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai.swan.udqs</groupId>
                <artifactId>Swan-udqs-api</artifactId>
                <version>1.6.0</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.mpmkt.coupon</groupId>
                <artifactId>mkt-coupon-execute-api</artifactId>
                <version>0.1.3</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>5.1.49</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dz</groupId>
                <artifactId>product-detail-gateway-api</artifactId>
                <version>${product-detail-gateway-api.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <!--&lt;!&ndash;单测生成 上线的时候记得注释掉 &ndash;&gt;-->
        <!--<groupId>com.sankuai.testapi</groupId>-->
        <!--<artifactId>testapi-sdk</artifactId>-->
        <!--<version>1.0.8</version>-->
        <!--<groupId>com.dianping.mobile</groupId>-->
        <!--<artifactId>mapi-testut-api</artifactId>-->
        <!--<version>5.5.16</version>-->
        <dependency>
            <groupId>com.sankuai.dz</groupId>
            <artifactId>product-detail-gateway-api</artifactId>
        </dependency>
        <!--团购导航栏搜索模块-->
        <dependency>
            <groupId>com.meituan.mdp</groupId>
            <artifactId>dzviewscene-sug-merger-api</artifactId>
            <version>0.0.1</version>
        </dependency>
        <!--团购次卡先用后付-->
        <dependency>
            <groupId>com.sankuai.fincreditpay</groupId>
            <artifactId>bnpl-client</artifactId>
            <version>1.0.24</version>
        </dependency>
        <!--团购次卡先用后付-->
        <!--团购导航栏搜索模块-->
        <dependency>
            <groupId>com.meituan.mdp</groupId>
            <artifactId>dzviewscene-sug-merger-api</artifactId>
            <version>0.0.1</version>
        </dependency>
        <!--团购导航栏搜索模块-->
        <!--保洁买约一体,白名单门店查询接口-->
        <dependency>
            <groupId>com.sankuai.lifeevent</groupId>
            <artifactId>reserve-rpc-api</artifactId>
            <version>1.0.28</version>
        </dependency>
        <!--保洁买约一体,白名单门店查询接口-->

        <!--解决每次发布时出现的NoClassFound问题-->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>4.1.67.Final</version>
        </dependency>
        <!--无人台球自助开台-->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>joy-general-api</artifactId>
            <version>0.0.68</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-detail-calc-api</artifactId>
            <version>1.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.servicecatalog</groupId>
            <artifactId>api-annotations</artifactId>
            <version>1.0.7</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>athena-inf-cache</artifactId>
            <version>0.0.16</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <!--Common Begin-->
        <dependency>
            <groupId>com.dianping.vc</groupId>
            <artifactId>dztheme-deal-api</artifactId>
            <version>1.1.9</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.general.product</groupId>
                    <artifactId>client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.8.1</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.16.8</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.oceanus.http</groupId>
            <artifactId>oceanus-http</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>mt-service-http</artifactId>
        </dependency>
        <!--Common End-->
        <dependency>
            <groupId>com.sankuai.nibpt</groupId>
            <artifactId>nibpt-union-log</artifactId>
            <version>0.0.2</version>
        </dependency>
        <!--Log Begin-->
        <dependency>
            <groupId>com.meituan.inf</groupId>
            <artifactId>xmd-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.inf</groupId>
            <artifactId>xmd-common-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.log</groupId>
            <artifactId>scribe-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.2</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.vc</groupId>
            <artifactId>vc-threadpool-client</artifactId>
            <version>0.1.3</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-1.2-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-web</artifactId>
            <scope>runtime</scope>
        </dependency>
        <!--Log End-->
        <!--Platform Begin-->
        <dependency>
            <groupId>com.dianping.dpsf</groupId>
            <artifactId>dpsf-net</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.lion</groupId>
            <artifactId>lion-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.cat</groupId>
            <artifactId>cat-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace-http</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.pearl</groupId>
            <artifactId>pearl</artifactId>
            <version>5.3.24</version>
        </dependency>
        <!--Deal Begin-->
        <dependency>
            <groupId>com.sankuai.general.product</groupId>
            <artifactId>client</artifactId>
            <version>1.0.57</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpproduct.message</groupId>
            <artifactId>mpproduct-message-common</artifactId>
            <version>1.0.53</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-base-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-idmapper-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-publish-category-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-attribute-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-stock-query-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-sales-display-api</artifactId>
            <version>2.1.4</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-detail-api</artifactId>
            <version>2.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-sales-common-api</artifactId>
            <version>2.1.4</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.spt</groupId>
            <artifactId>spt-common-api</artifactId>
            <version>1.0.4</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-shop-api</artifactId>
            <version>2.3.20</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter-bean-copy</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter-config</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter-log</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter-rhino</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter-runtime</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter-trace</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-struct-query-api</artifactId>
            <version>1.0.5</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-common-util</artifactId>
            <version>${deal.common.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-common-api</artifactId>
            <version>2.2.6</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-sale-api</artifactId>
            <version>1.7</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-book-api</artifactId>
            <version>1.0.13</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>dentistry-book-api</artifactId>
            <version>1.0.58</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-tag-query-api</artifactId>
            <version>1.0.14</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-style-api</artifactId>
            <version>1.1.28</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-style-spi</artifactId>
            <version>1.1.7</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tuangou</groupId>
            <artifactId>dztg-bjwrapper-api</artifactId>
            <version>1.1.10</version>
        </dependency>
        <!--Deal End-->
        <!--Trade Begin-->
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>trade-general-reserve-api</artifactId>
            <version>0.0.6</version>
        </dependency>
        <!--Trade End-->
        <!--Biz Begin-->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>account-api</artifactId>
            <version>4.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.wpt.user.merge</groupId>
            <artifactId>user-merge-query-api</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>account-common-validation</artifactId>
            <version>2.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>account-validation-api</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.piccentercloud</groupId>
            <artifactId>piccenter-display-api</artifactId>
            <version>0.2.31</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-gis-api</artifactId>
            <version>0.4.48</version>
        </dependency>
        <!--门店映射关系-->
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-relation-service-api</artifactId>
            <version>1.1.10</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>geoinfo-api</artifactId>
            <version>1.7.7</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.dppoi</groupId>
            <artifactId>dp-poi-api</artifactId>
            <version>0.2.6</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-shopcateprop-api</artifactId>
            <version>0.6.0</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.gmkt</groupId>
            <artifactId>gmkt-ml-api</artifactId>
            <version>1.0.12</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.gmkt</groupId>
            <artifactId>gmkt-common</artifactId>
            <version>1.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.dp.arts</groupId>
            <artifactId>arts-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.gmkt</groupId>
            <artifactId>gmkt-activity-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.gmkt</groupId>
            <artifactId>gmkt-event-datapools-api</artifactId>
            <version>2.1.19</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.merchantcard</groupId>
            <artifactId>timescard-exposure-api</artifactId>
            <version>0.1.9</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.credit</groupId>
            <artifactId>credit-cxutil</artifactId>
            <version>2.1.5</version>
        </dependency>
        <!--立减标签服务-->
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>pay-promo-display-api</artifactId>
            <version>0.2.16.1_jdk7</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>pay-promo-common</artifactId>
            <version>1.1.61</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>pay-promo-reception-api</artifactId>
            <version>0.0.9</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>pay-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>pay-promo-rule-api</artifactId>
            <version>0.1.9</version>
            <exclusions>
                <exclusion>
                    <groupId>javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--拼团-->
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-aggregate-api</artifactId>
            <version>1.8.69</version>
        </dependency>
        <!--红包分享-->
        <dependency>
            <groupId>com.dianping.gm</groupId>
            <artifactId>gmm-share-red-packet-c-api</artifactId>
            <version>0.0.3</version>
        </dependency>
        <!--返券-->
        <dependency>
            <groupId>com.dianping.dp</groupId>
            <artifactId>gm-bonus-exposure-api</artifactId>
            <version>0.1.9</version>
        </dependency>
        <!--抵用券-->
        <dependency>
            <groupId>com.dianping.tgc</groupId>
            <artifactId>tgc-process-remote</artifactId>
            <version>0.1.11</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>unified-coupon-manage-api</artifactId>
            <version>0.0.42</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tgc</groupId>
            <artifactId>tgc-open-remote</artifactId>
            <version>0.1.63</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.spc</groupId>
                    <artifactId>spc-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mafka</groupId>
                    <artifactId>mafka-client_2.9</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.spc</groupId>
            <artifactId>spc-common</artifactId>
            <version>0.8.2</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--折扣卡接口-->
        <dependency>
            <groupId>com.dianping.dp</groupId>
            <artifactId>gm-marketing-member-card-api</artifactId>
            <version>0.1.78</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.merchantcard</groupId>
            <artifactId>discountcard-navigation-api</artifactId>
            <version>0.0.9</version>
        </dependency>
        <!--UGC评价模块-->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>review-api</artifactId>
            <version>5.9.6</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>ugc-review-api</artifactId>
            <version>3.3.42</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>user-base-remote</artifactId>
            <version>2.0.6</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>user-remote</artifactId>
            <version>2.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.wpt.user.retrieve</groupId>
            <artifactId>retrieve-api</artifactId>
            <version>1.2.12</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>ugc-act-report-read-api</artifactId>
            <version>0.1.3</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.cip</groupId>
            <artifactId>cip-growth-mana-api</artifactId>
            <version>1.0.6</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>ugc-proxy-api</artifactId>
            <version>1.7.1</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.mobile</groupId>
                    <artifactId>mapi-shell</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>vip-remote</artifactId>
            <version>0.3.35</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>ugc-pic-api</artifactId>
            <version>1.0.38</version>
        </dependency>
        <!--在线咨询链接服务-->
        <dependency>
            <groupId>com.sankuai.dzim</groupId>
            <artifactId>cliententry-api</artifactId>
            <version>0.0.31</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tuangou</groupId>
            <artifactId>dzim-general-api</artifactId>
            <version>1.3.25</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-volume-query-api</artifactId>
            <version>2.0.5</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.swan.udqs</groupId>
            <artifactId>Swan-api</artifactId>
            <version>1.0.1-release</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>tpfun-product-api</artifactId>
            <version>2.9.16</version>
        </dependency>
        <!--Biz End-->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-core</artifactId>
            <version>1.3</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>lion-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nib.mkt</groupId>
            <artifactId>common-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nibmktproxy</groupId>
            <artifactId>query-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>ts-settle-common-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.thoughtworks.xstream</groupId>
                    <artifactId>xstream</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.jboss.netty</groupId>
                    <artifactId>netty</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.curator</groupId>
                    <artifactId>curator-framework</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-voucher-query-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>product-shelf-query-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.douhu</groupId>
            <artifactId>douhu-absdk</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.scala-lang</groupId>
                    <artifactId>scala-library</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mafka</groupId>
                    <artifactId>mafka-client_2.9</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzcard</groupId>
            <artifactId>dzcard-navigation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzcard</groupId>
            <artifactId>dzcard-fulfill-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dealuser</groupId>
            <artifactId>price-display-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-jetty</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.beauty</groupId>
            <artifactId>beauty-tagsearch-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.swan.udqs</groupId>
            <artifactId>Swan-udqs-api</artifactId>
        </dependency>
        <!--电话查询收归-->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>dp-400phone-api</artifactId>
            <version>2.4.35</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--sinai-->
        <dependency>
            <groupId>com.meituan.service.mobile.poi</groupId>
            <artifactId>sinai.client</artifactId>
            <version>3.1.13</version>
            <exclusions>
                <exclusion>
                    <groupId>org.scala-lang</groupId>
                    <artifactId>scala-library</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>tp-deal-data-api</artifactId>
            <version>1.6.4</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.mobile.message</groupId>
            <artifactId>groupdeal</artifactId>
            <version>0.1.85</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.mobile.prometheus</groupId>
            <artifactId>deal-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>org.springframework.jms</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-asm</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.beauty</groupId>
            <artifactId>beauty-deal-api</artifactId>
            <version>1.0.5</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.mobile.message</groupId>
            <artifactId>recommend</artifactId>
            <version>2.51-JDK7</version>
            <exclusions>
                <exclusion>
                    <groupId>org.json</groupId>
                    <artifactId>json</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.dataapp</groupId>
            <artifactId>recapilib</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.json</groupId>
                    <artifactId>json</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--poi上门服务属性-->
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-biz-api</artifactId>
            <version>0.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-brand-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.clr</groupId>
            <artifactId>clr-content-process-gateway-thrift</artifactId>
            <version>0.0.42</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmctmvacommon</groupId>
            <artifactId>mpmctmvacommon-resource</artifactId>
            <version>0.0.3</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-open-data-api</artifactId>
            <version>1.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.zdc</groupId>
            <artifactId>zdc-apply-api</artifactId>
            <version>0.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.sinai</groupId>
            <artifactId>sinai-api</artifactId>
            <version>1.0.53</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.merchantcard</groupId>
            <artifactId>beautycard-navigation-api</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.beautycontent</groupId>
            <artifactId>security-api</artifactId>
            <version>0.1.19</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.beautycontent</groupId>
            <artifactId>dzhealth-medical-api</artifactId>
            <version>1.0.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai</groupId>
                    <artifactId>beautycontent.store.api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sankuai.beautycontent</groupId>
                    <artifactId>common-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sankuai.fbi</groupId>
                    <artifactId>fbi-adapter-for-nest</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.beautycontent</groupId>
            <artifactId>tattoo-api</artifactId>
            <version>1.0.3</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai</groupId>
                    <artifactId>beautycontent.store.api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpproduct.tagservice</groupId>
            <artifactId>mpproduct-tagservice-cost-effective-sdk</artifactId>
            <version>1.0.1-RC1</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nibscp.common</groupId>
            <artifactId>flow-identify-sdk</artifactId>
            <version>1.0.18</version>
        </dependency>
        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
            <version>5.2.6</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tuangou</groupId>
            <artifactId>dztg-usercenter-api</artifactId>
            <version>1.9.9</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.general</groupId>
            <artifactId>martgeneral-recommend-api</artifactId>
            <version>1.7.6</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmctmember</groupId>
            <artifactId>mpmctmember-process-common</artifactId>
            <version>0.0.10</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter-log</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sankuai.mdp</groupId>
                    <artifactId>mdp-boot-initializr-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmctmember</groupId>
            <artifactId>mpmctmember-process-thrift</artifactId>
            <version>0.0.15</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter-log</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sankuai.mdp</groupId>
                    <artifactId>mdp-boot-initializr-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmctcontent</groupId>
            <artifactId>mpmctcontent-query-thrift</artifactId>
            <version>0.4.5</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmctcontent</groupId>
            <artifactId>mpmctcontent-application-thrift</artifactId>
            <version>0.1.24</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>technician-vc-api</artifactId>
            <version>1.14.98</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.technician</groupId>
            <artifactId>technician-common-api</artifactId>
            <version>1.0.65</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.inf.leaf</groupId>
            <artifactId>leaf-idl</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.corehr</groupId>
            <artifactId>holiday-sdk</artifactId>
            <version>1.0.2-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.github.heqiao2010</groupId>
            <artifactId>lunar</artifactId>
            <version>1.5</version>
        </dependency>
        <!--为了确保正常的测试和模拟行为，建议按照正确的顺序引入mockito-inline和powermock-api-mockito2库。-->
        <!--正确的引入顺序应该是先引入mockito-inline，再引入powermock-api-mockito2。这样可以确保内联mock maker能够正确加载并支持PowerMock的功能。-->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>3.8.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>3.8.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <!--<version>2.0.0</version>-->
            <version>2.0.7</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mdp</groupId>
            <artifactId>dzrank-scenes-api</artifactId>
            <version>0.0.26</version>
            <exclusions>
                <exclusion>
                    <groupId>org.eclipse.jetty</groupId>
                    <artifactId>jetty-servlet</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.eclipse.jetty</groupId>
                    <artifactId>jetty-servlets</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.eclipse.jetty.websocket</groupId>
                    <artifactId>websocket-servlet</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpclient</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mortbay.jasper</groupId>
                    <artifactId>apache-el</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.aspectj</groupId>
                    <artifactId>aspectjweaver</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-actuator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-actuator-autoconfigure</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-autoconfigure</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-actuator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-jetty</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-json</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter-bean-copy</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter-config</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter-log</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter-rhino</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter-runtime</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter-trace</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.servicecatalog</groupId>
                    <artifactId>api-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nib.price.operation</groupId>
            <artifactId>price-operation-api</artifactId>
            <version>1.0.25</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpproduct.idservice</groupId>
            <artifactId>idservice-api</artifactId>
            <version>1.4.3</version>
        </dependency>
        <!--价格带查询接口依赖包-->
        <dependency>
            <groupId>com.sankuai.tpfun</groupId>
            <artifactId>sku-operation-api</artifactId>
            <version>0.0.20</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.technician</groupId>
            <artifactId>technician-biz-api</artifactId>
            <version>2.8.12</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.hotel.login.authenticate</groupId>
            <artifactId>login-authenticate-api</artifactId>
            <version>1.0.22</version>
        </dependency>
        <!--海马平台-->
        <dependency>
            <groupId>com.dianping.haima</groupId>
            <artifactId>haima-client</artifactId>
            <version>1.1.16</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.leads</groupId>
            <artifactId>leads-count-thrift</artifactId>
            <version>0.0.10</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.athena</groupId>
            <artifactId>athena-stability-client</artifactId>
            <version>0.0.12</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.clr</groupId>
            <artifactId>clr-content-process-thrift</artifactId>
            <version>0.0.71</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sankuai.mpmctstock</groupId>
                    <artifactId>mpmctstock-core-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sankuai.clr</groupId>
                    <artifactId>clr-content-core-thrift</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sankuai.leads</groupId>
                    <artifactId>clrbase-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.clr</groupId>
            <artifactId>clr-content-core-thrift</artifactId>
            <version>0.0.65</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.clr</groupId>
                    <artifactId>clr-content-core-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sankuai.mpmctstock</groupId>
                    <artifactId>mpmctstock-core-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sankuai.leads</groupId>
                    <artifactId>clrbase-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sankuai.reservation</groupId>
                    <artifactId>reservation-thrift</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.nibscp.common</groupId>
            <artifactId>scp-common-enum</artifactId>
            <version>3.5.35</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzrtc</groupId>
            <artifactId>dzrtc-privatelive-biz-api</artifactId>
            <version>1.0.79</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.scrm</groupId>
            <artifactId>scrm-core-api</artifactId>
            <version>2.0.8</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mlive.goods.trade</groupId>
            <artifactId>mlive-goods-trade-api</artifactId>
            <version>1.0.14</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-jetty</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sankuai.mlive.goods</groupId>
                    <artifactId>mlive-goods-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.it.sso</groupId>
            <artifactId>sso-java-sdk</artifactId>
            <version>2.5.8</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzim</groupId>
            <artifactId>message-spi</artifactId>
            <version>0.0.7</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzim</groupId>
            <artifactId>message-common</artifactId>
            <version>0.0.81</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmctmetadataeav</groupId>
            <artifactId>metadataeav-process-thrift</artifactId>
            <version>1.1.3</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mafka</groupId>
                    <artifactId>kafka-clients</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mafka</groupId>
                    <artifactId>mafka-kafka_2.9.2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sankuai.mafka</groupId>
                    <artifactId>mafka-push-server-sdk</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mafka</groupId>
                    <artifactId>mafka-client_2.9</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>beautycontent.creator.api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mafka</groupId>
                    <artifactId>kafka-clients</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mafka</groupId>
                    <artifactId>mafka-kafka_2.9.2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sankuai.mafka</groupId>
                    <artifactId>mafka-push-server-sdk</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mafka</groupId>
                    <artifactId>mafka-client_2.9</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mktplay</groupId>
            <artifactId>mkt-play-center-client</artifactId>
            <version>1.0.4</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.zdc</groupId>
            <artifactId>zdc-tag-apply-api</artifactId>
            <version>1.3.10</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.squirrel</groupId>
            <artifactId>squirrel-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.squirrel</groupId>
            <artifactId>squirrel-async-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.waimai</groupId>
            <artifactId>waimai-thrift-tools</artifactId>
            <version>1.9.6.0.9</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nibpt</groupId>
            <artifactId>nibpt-transparent-validator</artifactId>
            <version>1.0.7</version>
        </dependency>
        <!--sig spring aop，依赖 sig core 模块-->
        <dependency>
            <groupId>com.sankuai.guardian</groupId>
            <artifactId>sig-botdefender-adapter</artifactId>
            <version>1.1.13</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.guardian</groupId>
            <artifactId>sig-botdefender-core</artifactId>
            <version>1.1.13</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.guardian</groupId>
            <artifactId>sig-botdefender-adapter-spring-aop</artifactId>
            <version>1.1.13</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmkt.coupon</groupId>
            <artifactId>mkt-coupon-execute-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.charity.merchant.main</groupId>
            <artifactId>charity-merchant-main-sdk</artifactId>
            <version>3.3.24</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nib.mkt</groupId>
            <artifactId>promotion-tag-server-api</artifactId>
            <version>0.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>dztheme-shop-api</artifactId>
            <version>0.0.161</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>general-unified-search-api</artifactId>
            <version>1.8.15</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>pay-order-service</artifactId>
            <version>2.6.0</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>pay-order-common</artifactId>
            <version>2.6.0</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.nibtp</groupId>
            <artifactId>trade-client</artifactId>
            <version>1.10.42</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.deal</groupId>
            <artifactId>dealadapter-client</artifactId>
            <version>1.0.15</version>
        </dependency>
        <dependency>
          <groupId>com.dianping</groupId>
          <artifactId>gis-api</artifactId>
          <version>1.3.27</version>
        </dependency>
        <dependency>
          <groupId>com.meituan.service.mobile</groupId>
          <artifactId>groupgeo</artifactId>
          <version>0.4.3.19</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.map.maf</groupId>
            <artifactId>openplatform-dependency</artifactId>
            <version>1.1.32</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-distance-common</artifactId>
            <version>1.0.26</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-rgc-api</artifactId>
            <version>0.1.38</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.interest</groupId>
            <artifactId>interest-core-thrift</artifactId>
            <version>0.0.23</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.feitianplus.data.onedata</groupId>
            <artifactId>data-onedata-api</artifactId>
            <version>1.0.37</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-feature-api</artifactId>
            <version>0.1.39</version>
        </dependency>

        <!-- 标品主题/泛商品主题 接入 -->
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>dztheme-generalproduct-api</artifactId>
            <version>1.0.42</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping.gmkt</groupId>
                    <artifactId>gmkt-activity-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 召回标品中推荐课程 -->
        <dependency>
            <groupId>com.sankuai.fbi</groupId>
            <artifactId>faas-wed-api</artifactId>
            <version>1.0.8</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.gmkt</groupId>
            <artifactId>gmkt-scene-engine-api</artifactId>
            <version>2.0.8</version>
        </dependency>
        <!-- 智能客服 -->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>csc-access-facade-api</artifactId>
        </dependency>
        <!-- 批量查询搭售黑名单 -->
        <dependency>
            <groupId>com.sankuai.mppack.product</groupId>
            <artifactId>mppack-product-client</artifactId>
            <version>1.1.41</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.spt</groupId>
            <artifactId>spt-statequery-api</artifactId>
            <version>1.0.4</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.inf</groupId>
            <artifactId>kms-pangolin-sdk</artifactId>
            <version>0.14.0</version>
        </dependency>
        <dependency>
            <groupId>midas-baymax</groupId>
            <artifactId>baymax-ad-count</artifactId>
            <version>1.1.3</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dz</groupId>
            <artifactId>mapi-dztgdeatail-api</artifactId>
            <version>1.0.0</version>
        </dependency>
    </dependencies>
    <build>
        <finalName>mapi-dztgdetail-web</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.3</version>
                <configuration>
                    <source>${java.src.version}</source>
                    <target>${java.target.version}</target>
                    <encoding>${project.encoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>2.1-alpha-1</version>
                <configuration>
                    <warName>${project.artifactId}-${env}-${project.version}</warName>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.meituan.mdp.maven.plugins</groupId>
                <artifactId>mdp-doc-maven-plugin</artifactId>
                <version>*******-STANDARD-RC4</version>
                <executions>
                    <execution>
                        <id>doc-verify</id>
                        <phase>package</phase>
                        <goals>
                            <goal>verify</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>test</id>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                    </resource>
                    <resource>
                        <directory>src/main/profiles/test</directory>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>product</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                    </resource>
                    <resource>
                        <directory>src/main/profiles/product</directory>
                    </resource>
                </resources>
            </build>
        </profile>
    </profiles>
</project>
