#!/bin/bash
AppName="com.sankuai.dzu.tpbase.dztgdetailweb" # appkey
TimeStamp=`date '+%Y%m%d%H%M%S'` # 时间戳

# 在启动tomcat之前重置setenv.sh脚本
sudo chown sankuai.sankuai /usr/local/tomcat/bin/
#   用户根据需求在run.sh中做如下配置的其中一种
#   补充或修改某些参数:

#简化读取机器环境部分
ENV=`grep 'env' /data/webapps/appenv |grep -v 'deployenv'| cut -d'=' -f 2|sed 's/^[ \t]*//g'`
if [ "$ENV" = "test" ];then
		# 当存在JAVA_TOOL_OPTIONS参数，且JAVA_TOOL_OPTIONS参数包含jacocoagent时，将JAVA_TOOL_OPTIONS参数写入setenv.sh
		if [ ! -z "$JAVA_TOOL_OPTIONS" ] && [[ "$JAVA_TOOL_OPTIONS" =~ "jacocoagent" ]]; then
          echo 'CATALINA_OPTS="$CATALINA_OPTS -XX:MetaspaceSize=512m -XX:MaxMetaspaceSize=512m ' "$JAVA_TOOL_OPTIONS" '"' > /usr/local/tomcat/bin/setenv.sh
    else
    # 其它情况仍引入原JaCoCo
          echo 'CATALINA_OPTS="$CATALINA_OPTS -XX:MetaspaceSize=512m -XX:MaxMetaspaceSize=512m -javaagent:/opt/meituan/jacocoagent.jar=output=tcpserver,port=6300,address=*,includes=com/dianping/*:com/sankuai/*:com/meituan/*:com/dp/*"' > /usr/local/tomcat/bin/setenv.sh
    fi
else
    echo 'CATALINA_OPTS="$CATALINA_OPTS -XX:MetaspaceSize=512m -XX:MaxMetaspaceSize=512m -XX:MaxTenuringThreshold=15"' > /usr/local/tomcat/bin/setenv.sh
fi

#   完全覆盖:
# echo 'mem_maxsize=$(($(free -m|grep "Mem"|awk '"'"'{print $2}'"'"')*65/100))' > /usr/local/tomcat/bin/setenv.sh # 动态获取内存大小的例子
# echo 'CATALINA_OPTS="-Xms${mem_maxsize}m -Xmx${mem_maxsize}m ..."' >> /usr/local/tomcat/bin/setenv.sh
# 添加执行权限
chmod 755 /usr/local/tomcat/bin/setenv.sh

war_path="/data/webapps/$AppName/releases/$TimeStamp/$AppName.war"
mkdir -p -m 755 $war_path # 创建war包目录
ln -snf /data/webapps/$AppName/releases/$TimeStamp /opt/meituan/apps/$AppName/current
unzip -o -q -d /data/webapps/$AppName/releases/$TimeStamp/$AppName.war /data/webapps/$AppName/target/*.war # 解压war包至目标目录以便tomcat启动查找
sudo chown -R sankuai:sankuai /usr/local/tomcat/conf/Catalina/localhost/
sed -i 's/app_name/com.sankuai.plus.ci.java/g'  /usr/local/tomcat/conf/Catalina/localhost/ROOT.xml # 指定tomcat启动目录
sudo sed -i "s/'tomcat_start_timeout': 180/'tomcat_start_timeout': 600/g" /etc/init.d/tomcat # 替换tomcat启动时间，从180s -> 600s

sudo /etc/init.d/tomcat forcestop # 停止现有tomcat进程
if [ $? -ne 0 ]; then
    echo "failed"
    exit 1
fi
sudo /etc/init.d/tomcat forcestart # 启动tomcat进程
if [ $? -ne 0 ]; then
    echo "failed"
    exit 1
fi
tail -f /data/applogs/tomcat/catalina.out # 防止该进程退出。进程通过daemontools托管，如果进程结束，那么daemontools会以前服务启动失败，会重启服务，导致部署失败、