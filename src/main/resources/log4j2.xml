<?xml version="1.0" encoding="UTF-8"?>
<configuration status="info">
    <appenders>
        <!--默认按天&按512M文件大小切分日志，默认最多保留30个日志文件，非阻塞模式-->
        <XMDFile name="infoAppender" fileName="info.log" sizeBasedTriggeringSize="512M" rolloverMax="30">
            <Filters>
                <ThresholdFilter level="warn" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </XMDFile>
        <XMDFile name="warnAppender" fileName="warn.log" sizeBasedTriggeringSize="512M" rolloverMax="30">
            <Filters>
                <ThresholdFilter level="error" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </XMDFile>
        <XMDFile name="errorAppender" fileName="error.log" sizeBasedTriggeringSize="512M" rolloverMax="30">
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
        </XMDFile>
        <!--日志远程上报-->
        <CatAppender name="catAppender"/>
        <AsyncScribe name="ScribeAsyncAppender" blocking="false">
            <!--远程日志默认使用appkey作为日志名(app.properties文件中的app.name字段)，也可自定义scribeCategory属性，scribeCategory优先级高于appkey-->
            <LcLayout/>
        </AsyncScribe>
    </appenders>
    <loggers>
        <!--远程日志，代码中通过 LoggerFactory.getLogger("scribe") 获取-->
        <logger name="scribe" level="info" additivity="false">
            <appender-ref ref="ScribeAsyncAppender"/>
        </logger>
        <!-- 屏蔽 kafka 的内部日志 -->
        <logger name="com.meituan.kafka.producer.SyncProducer" level="info" additivity="false"/>
        <logger name="com.meituan.kafka.client.ClientUtils$" level="info" additivity="false"/>
        <logger name="com.meituan.kafka.utils.VerifiableProperties" level="info" additivity="false"/>
        <!-- 屏蔽 pigeon 的 info 日志 -->
        <logger name="com.dianping.pigeon.registry.RegistryManager" level="info" additivity="false"/>
        <logger name="com.dianping.pigeon.remoting.invoker.listener.ClusterListenerManager" level="info" additivity="false"/>
        <logger name="com.dianping.pigeon.registry.mns.MnsChangeListener" level="info" additivity="false"/>
        <logger name="com.dianping.pigeon.registry.listener.DefaultServiceChangeListener" level="info" additivity="false"/>
        <root level="info">
            <appender-ref ref="infoAppender"/>
            <appender-ref ref="warnAppender"/>
            <appender-ref ref="errorAppender"/>
            <appender-ref ref="catAppender"/>
            <!--如果加了下面这一行，则代表所有日志都输出到日志中心-->
            <appender-ref ref="ScribeAsyncAppender"/>
        </root>
    </loggers>
</configuration>
