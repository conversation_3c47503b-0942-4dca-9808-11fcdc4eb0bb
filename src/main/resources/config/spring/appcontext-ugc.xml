<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="
    http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd">

    <!--UGC 评价统计-->
    <bean id="reviewDealGroupServiceV2Future" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="ReviewService.ReviewDealGroupService"/>
        <property name="interfaceName" value="com.dianping.reviewremote.remote.ReviewDealGroupServiceV2"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="200"/>
    </bean>

    <bean id="mtReviewQueryServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="UGCReviewService.MTReviewQueryService"/>
        <property name="interfaceName" value="com.dianping.ugc.review.remote.mt.MTReviewQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="2000"/>
    </bean>

    <bean id="mtReviewQueryServiceFutureV2" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="UGCReviewService.MTReviewQueryServiceV2"/>
        <property name="interfaceName" value="com.dianping.ugc.review.remote.mt.MTReviewQueryServiceV2"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="2000"/>
    </bean>

    <bean id="reviewServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="ReviewService.ReviewService"/>
        <property name="interfaceName" value="com.dianping.reviewremote.remote.ReviewServiceV2"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="2000"/>
    </bean>

    <bean id="reviewAuthorizeQueryServiceV2Future"
          class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="ReviewService.ReviewAuthorizeQueryService"/>
        <property name="interfaceName"
                  value="com.dianping.review.professional.authorize.ReviewAuthorizeQueryServiceV2"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="200"/>
    </bean>

    <bean id="browseDataReadServiceFuture" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="com.dianping.act.report.read.service.BrowseDataReadService"/>
        <property name="iface" value="com.dianping.act.report.read.service.BrowseDataReadService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="future"/>
        <property name="timeout" value="150"/>
    </bean>

    <bean id="shopReviewSearchServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="search.arts.biz.shopreview"/>
        <property name="interfaceName" value="com.dp.arts.client.SearchService"/>
        <property name="timeout" value="1000"/>
        <property name="callType" value="future"/>
    </bean>

    <bean id="userBaseFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/userBaseService/userService_2.0.0"/>
        <property name="interfaceName" value="com.dianping.userremote.base.service.UserService"/>
        <property name="timeout" value="500"/>
        <property name="callType" value="future"/>
    </bean>

    <bean id="cipGrowthServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/cip/growth/mana/cipGrowthManaService_2.0.0"/>
        <property name="interfaceName" value="com.dianping.cip.growth.mana.api.service.CipGrowthManaService"/>
        <property name="timeout" value="300"/>
        <property name="callType" value="future"/>
    </bean>

    <bean id="dpReviewUserInfoServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="UGCProxyService.DpReviewUserInfoService"/>
        <property name="interfaceName" value="com.dianping.ugc.proxyService.remote.dp.DpReviewUserInfoService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="vipUserServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/vipService/vipUserService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.vipremote.service.VIPUserService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="200"/>
    </bean>

    <bean id="reviewStatisticsServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/reviewService/reviewStatisticsService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.reviewremote.remote.ReviewStatisticsService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="200"/>
    </bean>

    <bean id="shopRemoteServiceSync" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="UGCReviewService.MTReviewQueryService"/>
        <property name="interfaceName" value="com.dianping.shopremote.remote.ShopService"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="200"/>
    </bean>

    <!--包装服务-->
    <bean id="userWrapperServiceSync" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/tuangou/bjwrapper/userWrapperService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.tuangou.dztg.bjwrapper.api.UserWrapperService"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="200"/>
    </bean>

    <bean id="userWrapperServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/tuangou/bjwrapper/userWrapperService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.tuangou.dztg.bjwrapper.api.UserWrapperService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="200"/>
    </bean>

    <bean id="mtReviewPicServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="UGCPicService.MtReviewPicService"/>
        <property name="interfaceName" value="com.dianping.ugc.pic.remote.service.MtReviewPicService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="200"/>
    </bean>

    <bean id="mtReviewVideoServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="UGCPicService.VideoService"/>
        <property name="interfaceName" value="com.dianping.ugc.pic.remote.service.VideoService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="200"/>
    </bean>

    <bean id="distributionRpcFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.sankuai.dzrtc.dzrtc.privatelive.biz.api.rpc.DistributionRpcService"/>
        <property name="interfaceName" value="com.sankuai.dzrtc.dzrtc.privatelive.biz.api.rpc.DistributionRpcService"/>
        <property name="timeout" value="500"/>
        <property name="callType" value="future"/>
    </bean>

</beans>