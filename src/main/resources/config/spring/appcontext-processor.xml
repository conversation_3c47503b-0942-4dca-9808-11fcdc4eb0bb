<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean name="dealConsumerProcessor" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealConsumerProcessor"/>
<!--    <bean name="mapperProcessor" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.MapperProcessor"/>-->
    <bean name="memberExclusiveProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.MemberExclusiveProcessor"/>
    <bean name="dealGroupProcessor" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealGroupProcessor"/>
    <bean name="promoProcessor" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.PromoProcessor"/>
    <bean name="promoExposureProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.PromoExposureProcessor"/>
    <bean name="discountCardProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DiscountCardProcessor"/>
    <bean name="timesCardProcessor" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.TimesCardProcessor"/>
    <bean name="dealActivityProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealActivityProcessor"/>
    <bean name="dealActivityPreProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealActivityPreProcessor"/>
    <bean name="refundRatioProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.RefundRatioProcessor"/>
    <bean name="stockSalesProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.StockSalesProcessor"/>
    <bean name="productSaleProcessor" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.ProductSaleProcessor"/>
    <bean name="unifiedModuleProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.UnifiedModuleProcessor"/>
    <bean name="previewBestShopProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.PreviewBestShopProcessor"/>
    <bean name="bestShopProcessor" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.BestShopProcessor"/>
    <bean name="drivingShopProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DrivingShopProcessor"/>
    <bean name="skuModuleProcessor" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.SkuModuleProcessor"/>
    <bean name="shopBookProcessor" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.ShopBookProcessor"/>
    <bean name="dealBuilderProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor"/>

    <bean name="pinPoolProcessor" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.PinPoolProcessor"/>
    <bean name="promoPreProcessor" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.PromoPreProcessor"/>
    <bean name="bonusExposureProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.BonusExposureProcessor"/>
    <bean name="promoPostProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.PromoPostProcessor"/>
    <bean name="voucherProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.VoucherProcessor"/>
    <bean name="promoMergeProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.PromoMergeProcessor"/>
    <bean name="poiShopCategoryProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.PoiShopCategoryProcessor"/>
    <bean name="dealSalesPlatformProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealSalesPlatformProcessor"/>
    <bean name="dealGroupCustomerProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealGroupCustomerProcessor"/>

    <bean name="dealDetailProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealDetailProcessor"/>
    <bean name="extraStyleProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.ExtraStyleProcessor"/>
    <bean name="dealStyleProcessor" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealStyleProcessor"/>
    <bean name="wxNameQueryProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.WxNameQueryProcessor"/>
    <bean name="shareModuleProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.ShareModuleProcessor"/>
    <bean name="moreDealModuleProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.MoreDealModuleProcessor"/>
    <bean name="adsModuleProcessor" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AdsModuleProcessor"/>
    <bean name="displayControlProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DisplayControlProcessor"/>

    <bean name="dealSkuProcessor" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealSkuProcessor"/>

    <bean name="beautyAdaptor" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.BeautyAdaptor"/>
    <bean name="dealGiftAdaptor" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.DealGiftAdaptor"/>
    <bean name="beautyNailAdaptor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.BeautyNailAdaptor"/>
    <bean name="ktvStructureAdaptor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.KtvStructureAdaptor"/>
    <bean name="disposeDetailAdaptor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.DisposeDetailAdaptor"/>

    <bean name="dealGroupReserveInfoProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealGroupReserveInfoProcessor"/>

    <bean name="CommissionProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.CommissionProcessor"/>

    <bean name="JoyDiscountCardProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.JoyDiscountCardProcessor"/>
    <bean name="PriceDisplayProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.PriceDisplayProcessor"/>
    <bean name="dealShopPhoneProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealShopPhoneProcessor"/>
    <bean name="dealRelatedBehaviorProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealRelatedBehaviorProcessor"/>
    <bean name="dealGroupFeatureProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealGroupFeatureProcessor"/>

    <bean name="queryCenterProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.QueryCenterProcessor"/>

    <bean name="sinaiProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.SinaiProcessor"/>
    <bean name="timeStockPlanProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.TimeStockPlanProcessor"/>
    <bean name="rhinoLimitProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.RhinoLimitProcessor"/>
    <bean name="highlightsProcessor"
            class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.HighlightsProcessor"/>

    <bean name="couponProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.CouponProcessor"/>
    <bean name="cardStyleProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.CardStyleProcessor"/>
    <bean name="dealIdMapToPTIDProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealIdMapToPTIDProcessor"/>
    <bean name="guaranteeQueryProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.GuaranteeQueryProcessor"/>
    <bean name="dealExhibitInfoProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealExhibitInfoProcessor"/>
    <bean name="featureProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.feature.FeatureProcess"/>
    <bean name="digestQueryProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DigestQueryProcessor"/>
    <bean name="digestQueryV2Processor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DigestQueryV2Processor"/>
    <bean name="resvStatusProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.ResvStatusProcessor"/>
    <bean name="userIdMapperProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.UserIdMapperProcessor"/>
    <bean name="memberPriceProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.MemberPriceProcessor"/>
    <bean name="parallelDzCardProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.ParallelDzCardProcessor"/>
    <bean name="parallelNewUserProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.ParallelNewUserProcessor"/>
    <bean name="pageSourceProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.PageSourceProcessor"/>
    <bean name="channelSaleStatusProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.ChannelSaleStatusProcessor"/>
    <bean name="eduShortClassOrderInfoProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.EduShortClassOrderInfoProcessor"/>
    <bean name="eduTeacherAndVideoProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.EduTeacherAndVideoProcessor"/>
    <bean name="eduTrailAuditionNumProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.EduTrailAuditionNumProcessor"/>
    <bean name="beautyTattoConfigDataProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.BeautyTattoConfigDataProcessor"/>
    <bean name="shopTagsProcessor" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.shop.ShopTagsProcessor"/>
    <bean name="fitnessCrossDealProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.FitnessCrossDealProcessor"/>
    <bean name="userAccountInfoProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.UserAccountInfoProcessor"/>
    <bean name="leInsuranceAgreementProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.LEInsuranceAgreementProcessor"/>
    <bean name="contentTagProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.ContentTagProcessor"/>
    <bean name="selfServiceBilliardsProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.SelfServiceBilliardsProcessor"/>
    <bean name="leadsInfoProcessor" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.LeadsInfoProcessor"/>
    <bean name="weddingLeadsInfoProcessor" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.WeddingLeadsInfoProcessor"/>
    <bean name="specialValueProcessor" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.SpecialValueProcessor"/>
    <bean name="preOrderDealHandler" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.PreOrderDealHandler"/>
    <bean name="ActivityWindowDealConfigProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.ActivityWindowDealConfigProcessor"/>
    <bean name="DealVRProcessor" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealVRProcessor"/>
    <bean name="springFestivalQueryProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.SpringFestivalQueryProcessor"/>
    <bean name="rcfSnapshotCacheDataProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.RcfSnapshotCacheDataProcessor"/>
    <bean name="userAndProductCreditPayProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.handler.UserAndProductCreditPayProcessor"/>
    <bean name="productDetailCommonModuleProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.productdetail.ProductDetailCommonModuleProcessor"/>
    <bean name="productStructDetailConfigProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.productdetail.ProductStructDetailConfigProcessor"/>
    <bean name="naviSearchProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.NavBarSearchProcessor"/>
    <bean name="productDetailTradeModuleProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.productdetail.ProductDetailTradeModuleProcessor"></bean>
    <!--团单主要信息-->
    <!--负责团单基础信息或者一些后续接口依赖的前置数据聚合-->
    <bean name="dealBaseQueryHandler" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.ProcessHandler">
        <property name="processorList">
            <list>
                <ref bean="queryCenterProcessor"/>
                <ref bean="rhinoLimitProcessor"/>
                <ref bean="mapperCacheProcessor"/>
                <ref bean="CommissionProcessor"/>
                <ref bean="dealGroupProcessor"/>
                <ref bean="dealSalesPlatformProcessor"/>
                <ref bean="bestShopProcessor"/>
                <ref bean="previewBestShopProcessor"/>
                <ref bean="poiShopCategoryProcessor"/>
                <ref bean="dealStyleProcessor"/>
                <ref bean="dealActivityPreProcessor"/>
                <ref bean="cardStyleProcessor"/>
                <ref bean="JoyDiscountCardProcessor"/>
                <ref bean="dealIdMapToPTIDProcessor"/>
                <ref bean="dealSkuProcessor"/>
            </list>
        </property>
        <property name="logName" value="singleQuery"/>
    </bean>

    <!--团单展示相关的信息-->
    <bean name="dealOtherQueryHandler" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.ProcessHandler">
        <property name="processorList">
            <list>
                <ref bean="sinaiProcessor"/>
                <ref bean="shopBookProcessor"/>
                <ref bean="stockSalesProcessor"/>
                <ref bean="PriceDisplayProcessor"/>
                <ref bean="timesCardProcessor"/>
                <ref bean="pinPoolProcessor"/>
                <ref bean="couponProcessor"/>
                <ref bean="dealActivityProcessor"/>
                <ref bean="dealShopPhoneProcessor"/>
                <ref bean="dealGroupCustomerProcessor"/>
                <ref bean="drivingShopProcessor"/>
                <ref bean="leadsInfoProcessor"/>
                <ref bean="weddingLeadsInfoProcessor"/>
                <ref bean="specialValueProcessor"/>
                <ref bean="preOrderDealHandler"/>
                <ref bean="skuModuleProcessor"/>
                <ref bean="dealDetailProcessor"/>
                <ref bean="shareModuleProcessor"/>
                <ref bean="moreDealModuleProcessor"/>
                <ref bean="adsModuleProcessor"/>
                <ref bean="beautyAdaptor"/>
                <ref bean="beautyNailAdaptor"/>
                <ref bean="ktvStructureAdaptor"/>
                <ref bean="disposeDetailAdaptor"/>
                <ref bean="displayControlProcessor"/>
                <ref bean="dealGroupReserveInfoProcessor"/>
                <ref bean="dealGroupFeatureProcessor"/>
                <ref bean="timeStockPlanProcessor"/>
                <ref bean="memberExclusiveProcessor"/>
                <ref bean="eduShortClassOrderInfoProcessor"/>
                <ref bean="highlightsProcessor"/>
                <ref bean="extraStyleProcessor"/>
                <ref bean="dealRelatedBehaviorProcessor"/>
                <ref bean="guaranteeQueryProcessor"/>
                <ref bean="dealExhibitInfoProcessor"/>
                <ref bean="channelSaleStatusProcessor"/>
                <ref bean="eduTeacherAndVideoProcessor"/>
                <ref bean="eduTrailAuditionNumProcessor"/>
                <ref bean="fitnessCrossDealProcessor"/>
            </list>
        </property>
        <property name="logName" value="singleQuery"/>
    </bean>

    <!--团单组装处理器-->
    <bean name="dealBuilderHandler" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.ProcessHandler">
        <property name="processorList">
            <list>
                <ref bean="dealBuilderProcessor"/>
            </list>
        </property>
        <property name="logName" value="singleDealBuilder"/>
    </bean>

    <!--团单优惠先序模块数据-->
    <bean name="promoPreModuleHandler" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.ProcessHandler">
        <property name="processorList">
            <list>
                <ref bean="promoPreProcessor"/>
            </list>
        </property>
    </bean>
    <!--团单优惠模块数据-->
    <bean name="promoModuleHandler" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.ProcessHandler">
        <property name="processorList">
            <list>
                <ref bean="promoProcessor"/> <!--立减-->
                <ref bean="bonusExposureProcessor"/><!--返礼-->
                <ref bean="voucherProcessor"/><!--抵用券-->
                <ref bean="promoExposureProcessor"/><!--优惠提前曝光-->
            </list>
        </property>
    </bean>
    <!--团单优惠模块构建器-->
    <bean name="promoPostModuleHandler" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.ProcessHandler">
        <property name="processorList">
            <list>
                <ref bean="promoPostProcessor"/>
            </list>
        </property>
    </bean>


    <!--团详rcf优化，相关的prcessor及新的handler-->
    <bean name="parallDealStyleProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.style.ParallDealStyleProcessor"/>
    <bean name="parallDealGroupProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.style.ParallDealGroupProcessor"/>
    <bean name="parallBeautyAdaptor"
          class="com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.style.ParallBeautyAdaptor"/>
<!--    <bean name="parallMapperProcessor"-->
<!--          class="com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.style.ParallMapperProcessor"/>-->
    <bean name="priceDealStyleProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.basic.PriceDealStyleProcessor"/>
    <bean name="mapperCacheProcessor" class="com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.basic.MapperCacheProcessor"/>
    <bean name="pricePinPoolProcessor" class="com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.basic.PricePinPoolProcessor"/>
    <bean name="parallDealBuilderProcessor" class="com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor"/>
    <bean name="dealBuilderInfoProcessor" class="com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.basic.DealBuilderInfoProcessor"/>
    <bean name="parallBestShopProcessor" class="com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.basic.ParallBestShopProcessor"/>
    <bean name="sVIPMapperCacheProcessor" class="com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.basic.SVIPMapperCacheProcessor"/>

     <!--并行团单组装处理器-->
    <bean name="parallDealBuilderHandler" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.ProcessHandler">
        <property name="processorList">
            <list>
                <ref bean="parallDealBuilderProcessor"/>
            </list>
        </property>
        <property name="logName" value="parallDealBuilder"/>
    </bean>

    <!--团单样式加载链路-->
    <bean name="dealStyleBaseQueryHandler" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.ProcessHandler">
        <property name="processorList">
            <list>
                <ref bean="queryCenterProcessor"/>
                <ref bean="sVIPMapperCacheProcessor"/>
                <ref bean="mapperCacheProcessor"/>
                <ref bean="parallBestShopProcessor"/>
                <ref bean="parallDealGroupProcessor"/>
                <ref bean="parallDealStyleProcessor"/>
                <ref bean="dealActivityPreProcessor" />
                <ref bean="cardStyleProcessor"/>
            </list>
        </property>
        <property name="logName" value="styleCfQuery"/>
    </bean>

    <bean name="privateLiveProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.mtlive.PrivateLiveProcessor" />
    <bean name="privateLivePassParamProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.biz.processor.mtlive.PrivateLivePassParamProcessor" />
    <!--团单样式加载链路-->
    <bean name="dealStyleOtherQueryHandler" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.ProcessHandler">
        <property name="processorList">
            <list>
                <ref bean="parallBeautyAdaptor"/>
                <ref bean="extraStyleProcessor"/>
                <ref bean="privateLiveProcessor"/>
                <ref bean="privateLivePassParamProcessor"/>
                <ref bean="leInsuranceAgreementProcessor"/>
                <ref bean="productStructDetailConfigProcessor"/>
            </list>
        </property>
        <property name="logName" value="styleCfOtherQuery"/>
    </bean>

    <!--优惠信息加载链路-->
    <bean name="dealPromoBaseQueryHandler" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.ProcessHandler">
        <property name="processorList">
            <list>
                <ref bean="queryCenterProcessor"/>
                <ref bean="sVIPMapperCacheProcessor"/>
                <ref bean="mapperCacheProcessor"/>
                <ref bean="parallDealGroupProcessor"/>
                <ref bean="parallBestShopProcessor" />
                <ref bean="cardStyleProcessor"/>
            </list>
        </property>
        <property name="logName" value="promoCfQuery"/>
    </bean>


    <!--优惠信息加载链路-->
    <bean name="dealPromoOtherQueryHandler" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.ProcessHandler">
        <property name="processorList">
            <list>
                <ref bean="couponProcessor"/>
            </list>
        </property>
        <property name="logName" value="promoCfOtherQuery"/>
    </bean>

    <!--团单价格加载链路-->
    <bean name="dealPriceQueryHandler" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.ProcessHandler">
        <property name="processorList">
            <list>
                <ref bean="queryCenterProcessor"/>
                <ref bean="rhinoLimitProcessor"/>
                <ref bean="priceDealStyleProcessor"/>
                <ref bean="userIdMapperProcessor"/>
                <ref bean="userAccountInfoProcessor"/><!--process无耗时，后续链路在get-->
                <ref bean="dealGroupProcessor"/><!--process依赖queryCenterProcessor，如果走查询中心就没有网络耗时-->
                <ref bean="dealSalesPlatformProcessor"/><!--process依赖queryCenterProcessor，如果走查询中心就没有网络耗时-->
                <ref bean="previewBestShopProcessor"/><!--process依赖queryCenterProcessor，预览单几乎没有流量-->
                <ref bean="dealActivityPreProcessor"/>
                <ref bean="cardStyleProcessor"/> <!--依赖dealGroupProcessor，需要channelDTO-->
                <ref bean="dealIdMapToPTIDProcessor"/>
                <ref bean="sVIPMapperCacheProcessor"/><!--走缓存，prepare耗时，需要尽可能排在后面-->
                <ref bean="mapperCacheProcessor"/><!--走缓存，prepare耗时，需要尽可能排在后面-->
                <ref bean="CommissionProcessor"/><!--prepare依赖mapperCacheProcessor-->
                <ref bean="parallBestShopProcessor"/><!--prepare依赖mapperCacheProcessor，process依赖queryCenterProcessor-->
                <ref bean="poiShopCategoryProcessor"/><!--process依赖parallBestShopProcessor-->
                <ref bean="dealSkuProcessor"/><!--process依赖dealGroupProcessor、poiShopCategoryProcessor、queryCenterProcessor，无网络耗时 -->
            </list>
        </property>
        <property name="logName" value="priceCfQuery"/>
    </bean>

    <!--团单价格加载链路-->
    <bean name="dealPriceOtherQueryHandler" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.ProcessHandler">
        <property name="processorList">
            <list>
                <ref bean="pageSourceProcessor"/>
                <ref bean="sinaiProcessor"/>
                <ref bean="shopBookProcessor"/>
                <ref bean="stockSalesProcessor"/>
                <ref bean="productSaleProcessor"/>
                <ref bean="memberPriceProcessor"/><!--查询商家会员，重复查询暂无性能问题，后续迁移到权益台-->
                <ref bean="parallelNewUserProcessor"/><!--查询新客-->
                <ref bean="parallelDzCardProcessor"/><!--查询权益台-->
                <ref bean="PriceDisplayProcessor"/>
                <ref bean="timesCardProcessor"/>
                <ref bean="pricePinPoolProcessor"/>
                <ref bean="dealActivityProcessor"/>
                <ref bean="dealShopPhoneProcessor"/>
                <ref bean="dealGroupCustomerProcessor"/>
                <ref bean="drivingShopProcessor"/>
                <ref bean="leadsInfoProcessor"/>
                <ref bean="weddingLeadsInfoProcessor"/>
                <ref bean="specialValueProcessor"/>
                <ref bean="preOrderDealHandler"/>
                <ref bean="ActivityWindowDealConfigProcessor"/>
                <ref bean="skuModuleProcessor"/>
                <ref bean="dealDetailProcessor"/>
                <ref bean="wxNameQueryProcessor"/>
                <ref bean="shareModuleProcessor"/>
                <ref bean="moreDealModuleProcessor"/>
                <ref bean="adsModuleProcessor"/>
                <ref bean="beautyAdaptor"/>
                <ref bean="beautyNailAdaptor"/>
                <ref bean="ktvStructureAdaptor"/>
                <ref bean="disposeDetailAdaptor"/>
                <ref bean="displayControlProcessor"/>
                <ref bean="dealGroupReserveInfoProcessor"/>
                <ref bean="dealGroupFeatureProcessor"/>
                <ref bean="timeStockPlanProcessor"/>
                <ref bean="memberExclusiveProcessor"/>
                <ref bean="dealConsumerProcessor"/>
                <ref bean="eduShortClassOrderInfoProcessor"/>
                <ref bean="highlightsProcessor"/>
                <ref bean="dealRelatedBehaviorProcessor"/>
                <ref bean="guaranteeQueryProcessor"/>
                <ref bean="dealBuilderInfoProcessor"/>
                <ref bean="dealExhibitInfoProcessor"/>
                <ref bean="featureProcessor"/>
                <ref bean="digestQueryProcessor"/>
                <ref bean="digestQueryV2Processor"/>
                <ref bean="resvStatusProcessor"/>
                <ref bean="channelSaleStatusProcessor"/>
                <ref bean="eduTeacherAndVideoProcessor"/>
                <ref bean="eduTrailAuditionNumProcessor"/>
                <ref bean="dealGiftAdaptor"/>
                <ref bean="beautyTattoConfigDataProcessor"/>
                <ref bean="shopTagsProcessor"/>
                <ref bean="unifiedModuleProcessor"/>
                <ref bean="fitnessCrossDealProcessor"/>
                <ref bean="contentTagProcessor"/>
                <ref bean="selfServiceBilliardsProcessor"/>
                <ref bean="DealVRProcessor"/>
                <ref bean="springFestivalQueryProcessor"/>
                <ref bean="rcfSnapshotCacheDataProcessor"/>
                <ref bean="userAndProductCreditPayProcessor"/>
                <ref bean="productDetailCommonModuleProcessor"/>
                <ref bean="productDetailTradeModuleProcessor"/>
                <ref bean="naviSearchProcessor"/>
            </list>
        </property>
        <property name="logName" value="priceCfOtherQuery"/>
    </bean>

</beans>
