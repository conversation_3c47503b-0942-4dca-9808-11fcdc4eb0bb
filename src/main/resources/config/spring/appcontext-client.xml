<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="
    http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd">

    <!--账号解析服务-->
    <bean id="userAccountServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/userAccountService/userAccountService_2.0.0"/>
        <property name="interfaceName" value="com.dianping.account.UserAccountService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="areaCommonServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/com.dianping.poi.areacommon.AreaCommonService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.poi.areacommon.AreaCommonService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="areaCommonService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/com.dianping.poi.areacommon.AreaCommonService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.poi.areacommon.AreaCommonService"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="rpcMergeQueryService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy" lazy-init="false">
        <property name="serviceInterface"
                  value="com.sankuai.wpt.user.merge.query.thrift.message.UserMergeQueryService"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="remoteAppkey" value="com.sankuai.mtusercenter.merge.query"/>
        <property name="timeout" value="1000"/>
    </bean>

    <bean id="rpcUserMergeQueryServiceFuture" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy" lazy-init="false">
        <property name="serviceInterface" value="com.sankuai.wpt.user.merge.query.thrift.message.UserMergeQueryService"/>
        <property name="remoteAppkey" value="com.sankuai.mtusercenter.merge.query"/>
        <property name="timeout" value="200"/>
        <property name="nettyIO" value="true"/>
        <property name="async" value="true"/>
    </bean>

    <!--poiid映射服务-->
    <bean id="poiRelationServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.dianping.poi.relation.service.api.PoiRelationService"/>
        <property name="interfaceName" value="com.dianping.poi.relation.service.api.PoiRelationService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="1000"/>
    </bean>

    <!--poiid映射服务-->
    <bean id="poiRelationService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.dianping.poi.relation.service.api.PoiRelationService"/>
        <property name="interfaceName" value="com.dianping.poi.relation.service.api.PoiRelationService"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="200"/>
    </bean>

    <bean id="queryCenterDealGroupQueryService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.general.product.query.center.client.service.DealGroupQueryService"/> <!-- 接口名 -->
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.productuser.query.center"/>  <!-- 目标Server Appkey  -->
        <property name="filterByServiceName" value="true"/>  <!-- 通过服务名称进行服务发现 -->
        <property name="timeout" value="1500"/>
    </bean>

    <bean id="queryCenterDealGroupQueryServiceFuture"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.general.product.query.center.client.service.DealGroupQueryService"/> <!-- 接口名 -->
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.productuser.query.center"/>  <!-- 目标Server Appkey  -->
        <property name="filterByServiceName" value="true"/>  <!-- 通过服务名称进行服务发现 -->
        <property name="timeout" value="1500"/>
        <property name="async" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="promoDisplayServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/payPromoDisplayService/PromoDisplayService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.pay.promo.display.api.PromoDisplayService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="productDetailBarServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/discountCardService/ProductDetailBarService_1.0.0"/>
        <property name="interfaceName" value="com.sankuai.merchantcard.discountcard.ProductDetailBarService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="pinFacadeServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/tpfunService/pingfacadeservice_1.0.0"/>
        <property name="interfaceName" value="com.dianping.tpfun.product.api.sku.pintuan.PinFacadeService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="bonusExposureQueryServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/bonusExposureService/BonusExposureService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.gm.bonus.exposure.api.service.BonusExposureQueryService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <!--抵用券-->
    <bean id="couponComponentQueryServiceFuture"
          class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url"
                  value="http://service.dianping.com/tgcOpenService/v2/tgcGetCouponComponentQueryService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.tgc.open.v2.TGCGetCouponComponentQueryService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>
    <bean id="merchantCouponIssueServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/tgcProcessService/merchantCouponIssueService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.tgc.process.MerchantCouponIssueService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="unifiedCouponListServiceSync" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url"
                  value="http://service.dianping.com/unifiedCouponListRemoteService/UnifiedCouponListService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.unified.coupon.manage.api.UnifiedCouponListService"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="500"/>
    </bean>

    <!--营销次卡-->
    <bean id="timesCardNavigationFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url"
                  value="http://service.dianping.com/TimesCardNavigationService/TimesCardNavigationService_1.0.0"/>
        <property name="interfaceName" value="com.sankuai.merchantcard.timescard.exposure.TimesCardNavigationService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="2000"/>
    </bean>

    <bean id="productDetailPageCommonModuleSpiService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.sankuai.dzshoppingguide.ProductDetailPageCommonModuleSpiService" />
        <property name="interfaceName" value="com.sankuai.dz.product.detail.gateway.spi.service.ProductDetailPageService" />
        <property name="timeout" value="5000" />
        <property name="serialize" value="hessian" />
        <property name="callType" value="future" />
    </bean>

    <!--到综IM咨询平台-->
    <bean id="clientEntryServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.sankuai.dzim.cliententry.ClientEntryService"/>
        <property name="interfaceName" value="com.sankuai.dzim.cliententry.ClientEntryService"/>
        <property name="timeout" value="500"/>
        <property name="callType" value="future"/>
    </bean>

    <!--太平洋智能客服-->
    <bean id="cscAccessInServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.dianping.csc.center.engine.access.service.AccessInService" />
        <property name="interfaceName" value="com.dianping.csc.center.engine.access.service.AccessInService" />
        <property name="callType" value="future" />
        <property name="timeout" value="1000" />
    </bean>

    <bean id="payPromoMergeRemoteServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url"
                  value="http://service.dianping.com/payPromoReceptionQueryRemoteService/PayPromoMergeService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.pay.promo.reception.service.PayPromoMergeService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="poiShopCategoryQueryServiceFuture"
          class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url"
                  value="http://service.dianping.com/poi-shopcateprop-service/poiShopCategoryQueryService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.poi.shopcateprop.api.service.PoiShopCategoryQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="accumulateSaleQueryServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url"
                  value="com.dianping.deal.volume.query.api.AccumSaleQueryService"/>
        <property name="interfaceName" value="com.dianping.deal.volume.query.api.AccumSaleQueryService"/>
        <property name="timeout" value="200"/>
        <property name="callType" value="future"/>
    </bean>

    <bean id="SwanQueryService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.sankuai.swan.udqs.api.SwanQueryService"/>
        <property name="interfaceName" value="com.sankuai.swan.udqs.api.SwanQueryService"/>
        <property name="timeout" value="1000"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
    </bean>

    <!--团购交易预约-->
    <bean id="shopAndProductReserveStatusServiceFuture"
          class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.sankuai.trade.general.reserve.service.ShopAndProductReserveStatusService"/>
        <property name="interfaceName"
                  value="com.sankuai.trade.general.reserve.service.ShopAndProductReserveStatusService"/>
        <property name="timeout" value="500"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="future"/>
    </bean>

    <bean id="poiPhoneServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/dpTollPhoneService/poiPhoneService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.generic.entrance.poiphone.api.PoiPhoneService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="1000"/>
    </bean>


    <bean id="beautySearchServiceFuture" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="beauty.tag.search.beautySearchService"/>
        <property name="iface" value="com.dianping.beauty.tag.service.BeautySearchService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean name="drivingPoiQueryService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/ZDCApplyService/drivingPoiQueryService_1.0.0"/>
        <property name="interfaceName" value="com.sankuai.zdc.apply.api.DrivingPoiQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="200"/>
    </bean>

    <bean name="drivingPoiQueryServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/ZDCApplyService/drivingPoiQueryService_1.0.0"/>
        <property name="interfaceName" value="com.sankuai.zdc.apply.api.DrivingPoiQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="200"/>
    </bean>

    <bean id="sinaiDpPoiService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.sinai.data.api.service.DpPoiService"/> <!-- 接口名 -->
        <property name="remoteAppkey" value="com.sankuai.sinai.data.query"/>  <!-- 目标Server Appkey  -->
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>  <!-- 通过服务名称进行服务发现 -->
        <property name="timeout" value="200"/>
    </bean>

    <bean id="sinaiDpPoiServiceFuture" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.sinai.data.api.service.DpPoiService"/> <!-- 接口名 -->
        <property name="remoteAppkey" value="com.sankuai.sinai.data.query"/>  <!-- 目标Server Appkey  -->
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>  <!-- 通过服务名称进行服务发现 -->
        <property name="timeout" value="500"/>
        <property name="async" value="true"/>
    </bean>

    <bean id="sinaiMtPoiService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.sinai.data.api.service.MtPoiService"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="remoteAppkey" value="com.sankuai.sinai.data.query"/>
        <property name="filterByServiceName" value="true"/>
        <property name="timeout" value="200"/>
    </bean>

    <bean id="sinaiMtPoiServiceFuture" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.sinai.data.api.service.MtPoiService"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="remoteAppkey" value="com.sankuai.sinai.data.query"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
        <property name="timeout" value="500"/>
        <property name="async" value="true"/>
    </bean>

    <bean id="mapOpenApiService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.map.open.platform.api.MapOpenApiService"/> <!-- 接口名 -->
        <property name="remoteAppkey" value="com.sankuai.apigw.map.facadecenter"/>  <!-- 目标Server Appkey  -->
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>  <!-- 通过服务名称进行服务发现 -->
        <property name="timeout" value="1000"/>
    </bean>

    <bean id="openDataService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.dianping.poi.open.service.OpenDataService"/>
        <property name="interfaceName" value="com.dianping.poi.open.service.OpenDataService"/>
        <property name="timeout" value="200"/>
    </bean>

    <bean id="favorServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/userService/favorService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.userremote.service.collection.FavorService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="200"/>
    </bean>

    <!--poi商户品牌-->
    <bean id="forBusinessBrandServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url"
                  value="http://service.dianping.com/com.dianping.poi.serivce.ForBusinessBrandService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.poi.serivce.ForBusinessBrandService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="beautyNailService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="beauty.deal.beautyNailService"/>
        <property name="interfaceName" value="com.dianping.beauty.deal.service.BeautyNailService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>

    <bean id="beautyNailServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="beauty.deal.beautyNailService"/>
        <property name="interfaceName" value="com.dianping.beauty.deal.service.BeautyNailService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="1000"/>
    </bean>

    <bean id="displayControlServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.sankuai.beautycontent.security.displaycontrol.api.DisplayControlService"/>
        <property name="interfaceName"
                  value="com.sankuai.beautycontent.security.displaycontrol.api.DisplayControlService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="rpcGroupDealService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="remoteAppkey" value="mobile.groupdeal"/>
        <property name="serviceInterface" value="com.meituan.service.mobile.message.group.deal.RPCGroupDealService"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="nettyIO" value="true"/>
        <property name="timeout" value="2000"/>
    </bean>

    <bean id="dealThriftClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="remoteAppkey" value="mobile.prometheus"/>
        <property name="serviceInterface"
                  value="com.meituan.service.mobile.prometheus.common.message.dealserver.DealThriftService"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="nettyIO" value="true"/>
        <property name="timeout" value="2000"/>
    </bean>

    <bean id="recommendService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="remoteAppkey" value="com.sankuai.mobile.recsys.recommend.otherscene"/>
        <property name="serviceInterface" value="com.meituan.service.mobile.message.recommend.RPCRecommendService"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="nettyIO" value="true"/>
        <property name="timeout" value="2000"/>
    </bean>

    <bean id="nibMemberProductQryThriftServiceFuture"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="remoteAppkey" value="com.sankuai.mpmctmember.process"/>
        <property name="serviceInterface"
                  value="com.sankuai.mpmctmember.process.thrift.nib.user.api.NibMemberProductQryThriftService"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="nettyIO" value="true"/>
        <property name="async" value="true"/>
        <property name="timeout" value="2000"/>
    </bean>

    <bean id="nibMemberProductQryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="remoteAppkey" value="com.sankuai.mpmctmember.process"/>
        <property name="serviceInterface"
                  value="com.sankuai.mpmctmember.process.thrift.nib.user.api.NibMemberProductQryThriftService"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="nettyIO" value="true"/>
        <property name="timeout" value="2000"/>
    </bean>

    <bean id="rpcUserBatchRetrieveService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy" lazy-init="false">
        <property name="serviceInterface"
                  value="com.sankuai.wpt.user.retrieve.thrift.message.RpcUserBatchRetrieveService"/>
        <property name="remoteAppkey" value="com.sankuai.mtusercenter.info.batchretrieve"/>
        <property name="remoteServerPort" value="8081"/>
        <property name="timeout" value="500"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="ppCostEffectiveTagService"
          class="com.sankuai.mpproduct.tagservice.effective.service.impl.PpCostEffectiveTagServiceImpl"/>

    <bean id="createOrderPageUrlService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url"
                  value="http://service.dianping.com/tuangou/dztgUsercenterService/createOrderPageUrlService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.tuangu.dztg.usercenter.api.CreateOrderPageUrlService"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="2000"/>
    </bean>

    <bean id="createOrderPageUrlServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url"
                  value="http://service.dianping.com/tuangou/dztgUsercenterService/createOrderPageUrlService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.tuangu.dztg.usercenter.api.CreateOrderPageUrlService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="2000"/>
    </bean>

    <bean id="beautyCardExposureService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.sankuai.beautycard.navigation.api.service.BeautyCardExposureService"/>
        <property name="interfaceName" value="com.sankuai.beautycard.navigation.api.service.BeautyCardExposureService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="1000"/>
    </bean>

    <bean id="generalRecommendService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/martgeneral/recommend_1.0.0"/>
        <property name="interfaceName" value="com.dianping.martgeneral.recommend.api.service.RecommendService"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="2000"/>
    </bean>

    <bean id="recommendServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/martgeneral/recommend_1.0.0"/>
        <property name="interfaceName" value="com.dianping.martgeneral.recommend.api.service.RecommendService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="2000"/>
    </bean>

    <bean id="rankLabelQuery" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.sankuai.mdp.dzrank.scenes.api.RankLabelQuery"/>
        <property name="interfaceName" value="com.sankuai.mdp.dzrank.scenes.api.RankLabelQuery"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="1000"/>
    </bean>

    <bean id="coordTransferService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/coordinateService/coordinateService_1.0.1"/>
        <property name="interfaceName" value="com.dianping.geo.map.CoordTransferService"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="300"/>
    </bean>

    <bean id="sinaiWrapperServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/tuangou/bjwrapper/sinaiWrapperService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.tuangou.dztg.bjwrapper.api.SinaiWrapperService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="mtHolidayServiceFuture" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.holiday.thrift.iface.MtHolidayService"/> <!-- 接口名 -->
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.corehr.holiday"/>  <!-- 目标Server Appkey  -->
        <property name="nettyIO" value="true"/>
        <property name="async" value="true"/>
        <property name="filterByServiceName" value="true"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="idService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.mpproduct.idservice.api.service.IdService"/> <!-- 接口名 -->
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.mpproduct.idservice"/>  <!-- 目标Server Appkey  -->
        <property name="filterByServiceName" value="true"/>  <!-- 通过服务名称进行服务发现 -->
        <property name="timeout" value="500"/>
        <property name="nettyIO" value="true"/>
        <property name="async" value="true"/>
    </bean>

    <bean id="guaranteeQueryService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.nib.price.operation.api.guarantee.guide.standard.service.GuaranteeQueryService"/> <!-- 接口名 -->
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.priceoperation.service"/>  <!-- 目标Server Appkey  -->
        <property name="filterByServiceName" value="true"/>  <!-- 通过服务名称进行服务发现 -->
        <property name="timeout" value="500"/>
        <property name="nettyIO" value="true"/>
        <property name="async" value="true"/>
    </bean>

    <!-- 到综团购主题查询服务 -->
    <bean id="dealProductBizQueryServiceFuture"
          class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.sankuai.dztheme.deal.dealProductBizQueryService"/>
        <property name="interfaceName" value="com.sankuai.dztheme.deal.operatorpage.DealProductBizQueryService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="2000"/>
    </bean>
    <bean id="dealDetailPageGWServiceFuture" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.mpmctcontent.application.thrift.api.content.DealDetailPageGWService"/> <!-- 接口名 -->
        <property name="remoteAppkey" value="com.sankuai.mpmctcontent.application"/>  <!-- 目标Server Appkey  -->
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>  <!-- 通过服务名称进行服务发现 -->
        <property name="timeout" value="1000"/>
        <property name="async" value="true"/>
    </bean>

    <bean id="digestQueryServiceFuture" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.mpmctcontent.query.thrift.api.digest.DigestQueryService"/> <!-- 接口名 -->
        <property name="remoteAppkey" value="com.sankuai.mpmctcontent.query"/>  <!-- 目标Server Appkey  -->
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>  <!-- 通过服务名称进行服务发现 -->
        <property name="timeout" value="1000"/>
        <property name="async" value="true"/>
    </bean>

    <bean id="wedPhotoCaseServiceFuture" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.mpmctcontent.application.thrift.api.content.WedPhotoCaseService"/> <!-- 接口名 -->
        <property name="remoteAppkey" value="com.sankuai.mpmctcontent.application"/>  <!-- 目标Server Appkey  -->
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>  <!-- 通过服务名称进行服务发现 -->
        <property name="timeout" value="2000"/>
        <property name="async" value="true"/>
    </bean>

    <bean id="priceRangeQueryServiceFuture" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy">
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="timeout" value="3000"/>
        <property name="remoteAppkey" value="com.sankuai.productuser.operation.systemservice"/>
        <property name="serviceInterface"
                  value="com.sankuai.tpfun.skuoperationapi.price.service.PriceRangeQueryService"/>
        <property name="remoteServerPort" value="8410"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="nettyIO" value="true"/>
        <property name="async" value="true"/>
    </bean>

    <bean id="dealProductServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.sankuai.dztheme.dealproduct.DealProductService" />
        <property name="interfaceName" value="com.sankuai.dztheme.deal.DealProductService" />
        <property name="timeout" value="1000" />
        <property name="serialize" value="hessian" />
        <property name="callType" value="future" />
    </bean>

    <bean id="NewLeadsCountService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.leads.count.thrift.api.NewLeadsCountService"/> <!-- 接口名 -->
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.leads.count"/>  <!-- 目标Server Appkey  -->
        <property name="timeout" value="1500"/>
    </bean>

    <bean id="LeadsQueryService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.clr.content.process.thrift.api.LeadsQueryService"/> <!-- 接口名 -->
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.leads.content.process"/>  <!-- 目标Server Appkey  -->
        <property name="timeout" value="3000"/>
    </bean>

    <bean id="leadsQueryGatewayServiceFuture" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.clr.content.process.gateway.thrift.api.LeadsQueryGatewayService"/> <!-- 接口名 -->
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.leads.content.process"/>  <!-- 目标Server Appkey  -->
        <property name="timeout" value="1500" />
        <property name="async" value="true"/>
    </bean>

    <bean id="shopBookInfoProcessService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.clr.content.process.thrift.api.ShopBookInfoProcessService"/> <!-- 接口名 -->
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.leads.content.process"/>  <!-- 目标Server Appkey  -->
        <property name="timeout" value="2000" />
        <property name="async" value="true"/>
    </bean>

    <bean id="ResvQueryService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.clr.content.process.thrift.api.ResvQueryService"/> <!-- 接口名 -->
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.leads.content.process"/>  <!-- 目标Server Appkey  -->
        <property name="timeout" value="1500"/>
    </bean>

    <bean id="flowEntryWxMaterialService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.sankuai.dz.srcm.flow.service.FlowEntryWxMaterialService"/>
        <property name="interfaceName" value="com.sankuai.dz.srcm.flow.service.FlowEntryWxMaterialService"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="2000"/>
    </bean>

    <bean id="metaDataModelLoadService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.metadataeav.process.api.metadatamodel.MetaDataModelLoadService"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="remoteAppkey" value="com.sankuai.mpmctmetadataeav.process"/>
        <property name="timeout" value="1500"/>
    </bean>
    <bean id="goodsAllowSellingTService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.mlive.goods.trade.api.tservice.GoodsAllowSellingTService"/> <!-- 接口名 -->
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.mlive.goods.trade"/>  <!-- 目标Server Appkey  -->
        <property name="timeout" value="1500"/>
    </bean>

    <!--私域直播-->
    <bean id="liveRoomRpcServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.sankuai.dzrtc.dzrtc.privatelive.biz.api.service.liveroomadmin.LiveRoomRpcService"/>
        <property name="interfaceName" value="com.sankuai.dzrtc.dzrtc.privatelive.biz.api.service.liveroomadmin.LiveRoomRpcService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="1500"/>
    </bean>
    <!--到店礼-->
    <bean id="contentProcessServiceFuture" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.clr.content.process.thrift.api.ContentProcessService"/> <!-- 接口名 -->
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.leads.content.process"/>  <!-- 目标Server Appkey  -->
        <property name="timeout" value="1500"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
        <property name="async" value="true"/>
    </bean>

    <!--https://km.sankuai.com/collabpage/2154893612-->
    <bean id="playCenterServiceFuture" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.mktplay.center.mkt.play.center.client.PlayCenterService"/>
        <property name="timeout" value="500"/>
        <property name="appKey" value="com.sankuai.dztheme.generalproduct"/>
        <property name="remoteAppkey" value="com.sankuai.mktplay.center"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
        <property name="async" value="true"/>
    </bean>
    <bean id="playCenterService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.mktplay.center.mkt.play.center.client.PlayCenterService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="remoteAppkey" value="com.sankuai.mktplay.center"/>
        <property name="remoteUniProto" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <!--丽人纹绣配置数据获取-->
    <bean id="encyclopediaService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.sankuai.beautycontent.function.tattoo.api.EncyclopediaService" /><!-- 服务全局唯一的标识url，必须是服务接口类名，可选配置 -->
        <property name="interfaceName" value="com.sankuai.beautycontent.function.tattoo.api.EncyclopediaService" /><!-- 接口名称，必须设置 -->
        <property name="timeout" value="2000" /><!-- 超时时间，毫秒，默认1000，建议自己设置 -->
        <property name="callType" value="future" /><!-- 调用方式，sync/future/callback/oneway，默认sync，可不设置 -->
        <property name="serialize" value="thrift" /><!-- 序列化，hessian/thrift，默认hessian，可不设置 -->
        <property name="remoteAppKey" value="com.sankuai.beautycontent.function" /><!-- 2.10.3及以上版本有效，服务提供方的appkey，若调用Mtthrift提供的服务时，必须要填写！-->
    </bean>

    <bean id="contentSearchService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.mpmctcontent.query.thrift.api.search.ContentSearchService"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="remoteAppkey" value="com.sankuai.mpmctcontent.query"/>
        <property name="timeout" value="5000"/>
    </bean>
    <bean id="contentSearchServiceFuture" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.mpmctcontent.query.thrift.api.search.ContentSearchService"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="remoteAppkey" value="com.sankuai.mpmctcontent.query"/>
        <property name="timeout" value="1000"/>
        <property name="async" value="true"/>
    </bean>
    <bean id="metaInfoServiceFuture" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.mpmctcontent.query.thrift.api.meta.MetaInfoService"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="remoteAppkey" value="com.sankuai.mpmctcontent.query"/>
        <property name="timeout" value="1000"/>
        <property name="async" value="true"/>
    </bean>
    <bean id="poiTagDisplayRPCServiceFuture"
          class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/ZDCTagApplyService/poiTagDisplayRPCService_1.0.0" />
        <property name="interfaceName" value="com.sankuai.zdc.tag.apply.api.PoiTagDisplayRPCService" />
        <property name="timeout" value="1500" />
        <property name="serialize" value="hessian" />
        <property name="callType" value="future" />
    </bean>
    <bean id="poiTagDisplayRPCService"
          class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/ZDCTagApplyService/poiTagDisplayRPCService_1.0.0" />
        <property name="interfaceName" value="com.sankuai.zdc.tag.apply.api.PoiTagDisplayRPCService" />
        <property name="timeout" value="1500" />
        <property name="serialize" value="hessian" />
        <property name="callType" value="sync" />
    </bean>

    <bean id="memberCardQueryService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/dzcardFulFillService/memberCardQueryService_1.0.0" />
        <property name="interfaceName" value="com.sankuai.dzcard.fulfill.api.MemberCardQueryService" />
        <property name="timeout" value="1000" />
        <property name="serialize" value="hessian" />
        <property name="callType" value="sync" />
    </bean>
    <bean id="unifiedCouponInfoService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.sankuai.com/mpmkt/coupon/execute/UnifiedCouponInfoService_1.0.0" />
        <property name="interfaceName" value="com.dianping.unified.coupon.manage.api.UnifiedCouponInfoService" />
        <property name="timeout" value="1000" />
        <property name="serialize" value="hessian" />
        <property name="callType" value="sync" />
    </bean>
    <bean id="meituanUserServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/meituanUserService/meituanUserService_2.0.0" />
        <property name="interfaceName" value="com.dianping.account.MeituanUserService" />
        <property name="timeout" value="500" />
        <property name="serialize" value="hessian" />
        <property name="callType" value="future" />
    </bean>

    <bean id="welfareDocFacade" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.charity.merchant.main.sdk.WelfareDocFacade"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="timeout" value="1000"/>
        <property name="remoteAppkey" value="com.sankuai.fsp.charity.merchantmain"/>
        <property name="remoteServerPort" value="9005"/>
        <property name="nettyIO" value="true"/>
    </bean>
    <bean id ="activityShelfQueryServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.dianping.product.shelf.query.api.ActivityShelfQueryService" />
        <property name="interfaceName" value="com.dianping.product.shelf.query.api.ActivityShelfQueryService" />
        <property name="timeout" value="1000" />
        <property name="serialize" value="hessian" />
        <property name="callType" value="future" />
    </bean>

    <bean id="getUnifiedOrderService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/orderService/query/getUnifiedOrderService_1.0.0"/>
        <property name="iface" value="com.dianping.pay.order.service.query.GetUnifiedOrderService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="2000"/>
    </bean>

    <bean id="dzThemeShopService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.sankuai.dztheme.shop.service.DzThemeShopService" />
        <property name="interfaceName" value="com.sankuai.dztheme.shop.service.DzThemeShopService" />
        <property name="timeout" value="2000" />
        <property name="serialize" value="hessian" />
        <property name="callType" value="sync" />
    </bean>

    <bean id="generalProductShopSearchServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.dianping.general.unified.search.api.productshopsearch.GeneralProductShopSearchService" />
        <property name="interfaceName" value="com.dianping.general.unified.search.api.productshopsearch.GeneralProductShopSearchService" />
        <property name="timeout" value="2000" />
        <property name="serialize" value="hessian" />
        <property name="callType" value="future" />
    </bean>

    <bean id="generalProductShopSearchService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
            <property name="url" value="com.dianping.general.unified.search.api.productshopsearch.GeneralProductShopSearchService" />
            <property name="interfaceName" value="com.dianping.general.unified.search.api.productshopsearch.GeneralProductShopSearchService" />
            <property name="timeout" value="2000" />
            <property name="serialize" value="hessian" />
            <property name="callType" value="sync" />
    </bean>

    <bean id="iMagicalPromotionMemberTagService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
              destroy-method="destroy">
            <property name="timeout" value="1000"/>
            <property name="retryRequest" value="false"/>
            <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
            <property name="remoteAppkey" value="com.sankuai.mpmktproxy.promotiontag"/>
            <property name="filterByServiceName" value="true"/>
            <property name="serviceInterface"
                      value="com.sankuai.nib.mkt.promotion.tag.server.api.service.IMagicalMemberTagService"/>
            <property name="nettyIO" value="true"/>
            <property name="async" value="false"/>
    </bean>

    <bean id="dpCityInfoService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
         <property name="serviceName" value="http://service.dianping.com/gisService/cityInfoService_1.0.0"/>
         <property name="iface" value="com.dianping.gis.remote.service.CityInfoService"/>
         <property name="serialize" value="hessian"/>
         <property name="callMethod" value="sync"/>
         <property name="timeout" value="1000"/>
    </bean>

    <bean id="geoThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
    	<property name="maxActive" value="30"/>
        <property name="minIdle" value="1"/>
        <property name="maxIdle" value="10"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="true"/>
        <property name="testWhileIdle" value="true"/>
    </bean>

    <bean id="groupgeoServiceRPCClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="geoThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.meituan.service.mobile.group.geo.thrift.message.RPCGroupGeoService"/> <!-- service接口名 -->
        <property name="timeout" value="3000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/> <!-- 客户端appkey，如果同时也是server的话，建议复用 -->
        <property name="remoteAppkey" value="com.meituan.service.mobile.groupgeo"/>  <!-- 服务端appkey，由服务方提供 -->
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="geoThriftLoader" class="com.meituan.service.mobile.group.geo.loader.impl.GeoInfoThriftLoader">
        <property name="rPCGroupGeoServiceIface" ref="groupgeoServiceRPCClient"/>
    </bean>

    <bean id="mtCityService" class="com.meituan.service.mobile.group.geo.service.CityService"
          init-method="init" destroy-method="destroy">
        <property name="loader" ref="geoThriftLoader"/>
    </bean>

    <bean id="medicalDealConsumeRecordQueryService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.sankuai.beautycontent.dzhealth.medical.dealgroup.consumerecord.api.query.MedicalDealConsumeRecordQueryService"/>
        <property name="interfaceName" value="com.sankuai.beautycontent.dzhealth.medical.dealgroup.consumerecord.api.query.MedicalDealConsumeRecordQueryService"/>
        <property name="callType" value="sync"/>
        <property name="serialize" value="thrift" />
        <property name="timeout" value="1500"/>
        <property name="remoteAppKey" value="com.sankuai.beautycontent.function"/>
    </bean>

    <bean id="cycleSaleQueryServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.dianping.deal.volume.query.api.CycleSaleQueryService"/>
        <property name="interfaceName" value="com.dianping.deal.volume.query.api.CycleSaleQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="1500"/>
    </bean>

    <bean id="orderVerifySnapshotService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
              destroy-method="destroy">
            <property name="timeout" value="1000"/>
            <property name="retryRequest" value="false"/>
            <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
            <property name="remoteAppkey" value="com.sankuai.web.deal.adapter"/>
            <property name="filterByServiceName" value="true"/>
            <property name="serviceInterface"
                      value="com.sankuai.web.dealadapter.service.OrderVerifySnapshotService"/>
            <property name="nettyIO" value="true"/>
            <property name="async" value="false"/>
    </bean>

    <bean id="shopBookInfoQueryService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="timeout" value="1000"/>
        <property name="retryRequest" value="false"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="remoteAppkey" value="com.sankuai.spt.statequery"/>
        <property name="filterByServiceName" value="true"/>
        <property name="serviceInterface"
                  value="com.sankuai.spt.statequery.api.service.ShopBookInfoQueryService"/>
        <property name="nettyIO" value="true"/>
        <property name="async" value="false"/>
    </bean>

    <bean id="baseStateQueryService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="timeout" value="1000"/>
        <property name="retryRequest" value="false"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="remoteAppkey" value="com.sankuai.spt.statequery"/>
        <property name="filterByServiceName" value="true"/>
        <property name="serviceInterface"
                  value="com.sankuai.spt.statequery.api.service.BaseStateQueryService"/>
        <property name="nettyIO" value="true"/>
        <property name="async" value="false"/>
    </bean>

    <bean id="dealPageLayoutServiceFuture"
          class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName"
                  value="com.dianping.deal.style.DealPageLayoutService"/>
        <property name="iface" value="com.dianping.deal.style.DealPageLayoutService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="dealPageLayoutService"
          class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName"
                  value="com.dianping.deal.style.DealPageLayoutService"/>
        <property name="iface" value="com.dianping.deal.style.DealPageLayoutService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="dealDetailStyleFlashService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="com.dianping.deal.style.DealDetailStyleFlashService"/>
        <property name="iface" value="com.dianping.deal.style.DealDetailStyleFlashService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="future"/>
        <property name="timeout" value="500"/>
    </bean>


    <bean id="reserveConfigQueryService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="remoteAppkey" value="com.sankuai.lifeevent.faas.life"/>
        <property name="serviceInterface"
                  value="com.sankuai.fbi.lifeevent.reserverpcapi.service.ReserveConfigQueryService"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="nettyIO" value="true"/>
        <property name="timeout" value="2000"/>
    </bean>

    <bean id="reserveConfigQueryServiceFuture" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="remoteAppkey" value="com.sankuai.lifeevent.faas.life"/>
        <property name="serviceInterface"
                  value="com.sankuai.fbi.lifeevent.reserverpcapi.service.ReserveConfigQueryService"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="nettyIO" value="true"/>
        <property name="timeout" value="2000"/>
        <property name="async" value="true"/>
    </bean>

    <bean id="dealDetailStyleFlashServiceSync" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="com.dianping.deal.style.DealDetailStyleFlashService"/>
        <property name="iface" value="com.dianping.deal.style.DealDetailStyleFlashService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="500"/>
    </bean>
    <bean id="dealBffCacheQueryService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.dianping.deal.bff.cache.DealBffCacheQueryService"/>
        <property name="interfaceName" value="com.dianping.deal.bff.cache.DealBffCacheQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="subjectConfigServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.dianping.product.shelf.query.api.SubjectConfigManageService"/>
        <property name="interfaceName" value="com.dianping.product.shelf.query.api.SubjectConfigManageService"/>
        <property name="timeout" value="1000"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="future"/>
    </bean>

    <bean id="interestCalculateServiceFuture" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.interest.core.thrift.remote.api.InterestCalculateService"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="remoteAppkey" value="com.sankuai.interest.core"/>
        <property name="remoteServerPort" value="8710"/>
        <property name="timeout" value="500"/>
        <property name="async" value="true"/>
    </bean>


    <bean id="mtRgcServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/com.dianping.poi.mtRgcService.MtRgcService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.poi.mtRgcService.MtRgcService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="1500"/>
    </bean>

    <bean id="dpRgcServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/com.dianping.poi.dpRgcService.RgcService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.poi.dpRgcService.RgcService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="1500"/>
    </bean>

    <!--    入参为点评id -->
    <bean id="poiAccountDisplayTagBizServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.dianping.poi.feature.api.service.business.PoiAccountDisplayTagBizService" />
        <property name="interfaceName" value="com.dianping.poi.feature.api.service.business.PoiAccountDisplayTagBizService" />
        <property name="timeout" value="1000" />
        <property name="serialize" value="hessian" />
        <property name="callType" value="future" />
    </bean>
    <!--    入参为美团id -->
    <bean id="mTPoiAccountDisplayTagBizServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.dianping.poi.feature.api.service.business.MTPoiAccountDisplayTagBizService" />
        <property name="interfaceName" value="com.dianping.poi.feature.api.service.business.MTPoiAccountDisplayTagBizService" />
        <property name="timeout" value="1000" />
        <property name="serialize" value="hessian" />
        <property name="callType" value="future" />
    </bean>

    <bean id="queryDataSyncServiceFuture" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.feitianplus.data.onedata.api.thrift.service.QueryDataSyncService"/>
        <property name="timeout" value="1500"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="remoteAppkey" value="com.sankuai.feitianplus.data.onedata"/>
        <property name="nettyIO" value="true"/>
        <property name="async" value="true"/>
    </bean>

    <bean id="SpuThemeQueryServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.sankuai.dztheme.spuproduct.SpuThemeQueryService" />
        <property name="interfaceName" value="com.sankuai.dztheme.spuproduct.SpuThemeQueryService" />
        <property name="timeout" value="1000" />
        <property name="serialize" value="hessian" />
        <property name="callType" value="future" />
    </bean>

    <bean id="salesDisplayQueryServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.dianping.deal.sales.display.api.service.SalesDisplayQueryService"/>
        <property name="interfaceName" value="com.dianping.deal.sales.display.api.service.SalesDisplayQueryService"/>
        <property name="timeout" value="1000"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="future"/>
    </bean>


    <bean id="CrossCatRecommendServiceFuture" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.fbi.faas.wed.api.CrossCatRecommendService"/> <!-- 接口名 -->
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.lifeevent.faas.wed"/>  <!-- 目标Server Appkey  -->
        <property name="timeout" value="1500" />
        <property name="async" value="true"/>
    </bean>

    <bean id="packBlackListServiceFuture" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.mppack.product.client.supply.service.PackBlackListService"/>
        <property name="timeout" value="1500"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="remoteAppkey" value="com.sankuai.pack.product"/>
        <property name="nettyIO" value="true"/>
        <property name="async" value="true"/>
    </bean>

    <bean id="dealGroupSnapshotQueryService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.general.product.query.center.client.service.DealGroupSnapshotQueryService"/> <!-- 接口名 -->
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.productuser.query.center"/>  <!-- 目标Server Appkey  -->
        <property name="filterByServiceName" value="true"/>  <!-- 通过服务名称进行服务发现 -->
        <property name="timeout" value="1500"/>
        <property name="async" value="false"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean name="productDetailPageTypeServiceSync" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.sankuai.dz.product.detail.gateway.ProductDetailPageTypeService"/>
        <property name="interfaceName" value="com.sankuai.dz.product.detail.gateway.api.page.type.ProductDetailPageTypeService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="500"/>
    </bean>


    <bean id="productDetailPageTradeModuleSpiService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.sankuai.dzshoppingguide.ProductDetailPageTradeModuleSpiService" />
        <property name="interfaceName" value="com.sankuai.dz.product.detail.gateway.spi.service.ProductDetailPageService" />
        <property name="timeout" value="5000" />
        <property name="serialize" value="hessian" />
        <property name="callType" value="future" />
    </bean>

    <bean id="sugMergerService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.sankuai.dzviewscene.sug.merger.SugMergerService" />
        <property name="interfaceName" value="com.sankuai.dzviewscene.sug.merger.service.api.SugMergerService" />
        <property name="timeout" value="5000" />
        <property name="serialize" value="hessian" />
        <property name="callType" value="future" />
    </bean>

    <bean id="productDetailRcfCacheService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.sankuai.dz.product.detail.gateway.ProductDetailRcfCacheService" />
        <property name="interfaceName" value="com.sankuai.dz.product.detail.gateway.api.cache.ProductDetailRcfCacheService" />
        <property name="timeout" value="500" />
        <property name="serialize" value="hessian" />
        <property name="callType" value="sync" />
    </bean>

</beans>
