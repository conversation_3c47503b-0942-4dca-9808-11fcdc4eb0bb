<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean name="dealFlashQueryCenterProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.flash.DealFlashQueryCenterProcessor"/>
    <!--团详rcf优化，团购详情闪开接口-->
    <bean name="dealDetailStructFlashProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.flash.DealDetailStructFlashProcessor"/>
    <bean name="dealModuleDetailFlashProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.flash.DealModuleDetailFlashProcessor"/>
    <bean name="dealStyleFlashProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.flash.DealStyleFlashProcessor"/>
    <bean name="dealLayoutProcessor"
          class="com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.flash.DealLayoutProcessor"/>

    <!--团购详情闪开链路-->
    <bean name="dealDetailFlashHandler" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.ProcessHandler">
        <property name="processorList">
            <list>
                <ref bean="dealFlashQueryCenterProcessor"/>
                <ref bean="dealStyleFlashProcessor"/>
                <ref bean="dealDetailStructFlashProcessor"/>
                <ref bean="dealModuleDetailFlashProcessor"/>
            </list>
        </property>
        <property name="logName" value="dealDetailFlashHandler"/>
    </bean>

    <!--团购详情闪开链路-->
    <bean name="dealDetailFlashOtherHandler" class="com.dianping.mobile.mapi.dztgdetail.biz.processor.ProcessHandler">
        <property name="processorList">
            <list>
                <ref bean="dealLayoutProcessor"/>
            </list>
        </property>
        <property name="logName" value="dealDetailFlashOtherHandler"/>
    </bean>

</beans>