<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:pigeon="http://code.dianping.com/schema/pigeon" xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-2.5.xsd
    http://code.dianping.com/schema/pigeon http://code.dianping.com/schema/pigeon/pigeon-service-2.0.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

    <context:component-scan base-package="com.dianping.mobile" />
    <context:component-scan base-package="com.sankuai.hotel.login.authenticate.api"/>
    <context:annotation-config />

    <pigeon:annotation/>
    <aop:config proxy-target-class="true">
        <!-- 你的AOP切面配置 -->
    </aop:config>
    <bean id="flowDyeSDKAutoConfiguration" class="com.sankuai.nibscp.common.flow.identify.bootstrap.FlowDyeSDKAutoConfiguration"/>
    <!-- 激活 SDK -->
    <bean class="com.sankuai.nibpt.transparentvalidator.EnableTransparentValidatorConfiguration">
        <property name="needWhitelistConfig" value="true"/>
    </bean>
</beans>