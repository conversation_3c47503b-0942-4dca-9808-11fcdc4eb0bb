<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="ItemBrowseProducer" class="com.meituan.mafka.client.bean.MafkaProducer" init-method="start" destroy-method="close">
        <property name="namespace" value="pingtai"/>
        <property name="appkey" value="com.sankuai.dpsearch.realtime.baselog"/>
        <property name="topic" value="dpsearch.realtime.itembrowse"/>
    </bean>

    <bean id="trafficDiffProducer" class="com.meituan.mafka.client.bean.MafkaProducer" init-method="start" destroy-method="close">
        <property name="namespace" value="daozong"/>
        <property name="appkey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="topic" value="dz.product.detail.traffic"/>
    </bean>

</beans>