<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:pigeon="http://code.dianping.com/schema/pigeon"
       xsi:schemaLocation="
    http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
    http://code.dianping.com/schema/pigeon http://code.dianping.com/schema/pigeon/pigeon-service-2.0.xsd"
       default-autowire="byName">

    <bean id="dealGroupBaseServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/tuangou/dealService/dealGroupBaseService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.deal.base.DealGroupBaseService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="dealBaseFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/tuangou/dealService/dealBaseService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.deal.base.DealBaseService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="dealReceiptQueryServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/tuangou/dealService/DealReceiptQueryServiceImpl_2.0.1"/>
        <property name="interfaceName" value="com.dianping.deal.base.DealReceiptQueryService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="dealIdMapperServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/dealIdMapperService/dealIdMapperService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.deal.idmapper.api.DealIdMapperService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="dealIdMapperService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/dealIdMapperService/dealIdMapperService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.deal.idmapper.api.DealIdMapperService"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="dealGroupPublishCategoryQueryServiceFuture"
          class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url"
                  value="http://service.dianping.com/deal-publish-category-service/dealGroupPublishCategoryQueryService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.deal.publishcategory.DealGroupPublishCategoryQueryService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="tagQueryService"
          class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url"
                  value="com.dianping.deal.tag.TagQueryService"/>
        <property name="interfaceName" value="com.dianping.deal.tag.TagQueryService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="dealGroupPublishCategoryQueryService"
          class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url"
                  value="http://service.dianping.com/deal-publish-category-service/dealGroupPublishCategoryQueryService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.deal.publishcategory.DealGroupPublishCategoryQueryService"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="dealStockQueryServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/tuangou/dealStockService/dealStockQueryService_2.0.0"/>
        <property name="interfaceName" value="com.dianping.deal.stock.DealStockQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="dealGroupAttributeGetServiceFuture"
          class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url"
                  value="http://service.dianping.com/dealAttributeService/dealGroupAttributeGetService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.deal.attribute.service.DealGroupAttributeGetService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="dealGroupDetailServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/tuangou/dealService/dealGroupDetailService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.deal.detail.DealGroupDetailService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="800"/>
    </bean>

    <!-- 团单团购详情结构化数据-->
    <bean id="dealGroupStructedDetailServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/tuangou/dealService/dealGroupStructedDetailService_1.0.0"/><!-- 服务全局唯一的标识url，必须设置 -->
        <property name="interfaceName" value="com.dianping.deal.detail.DealGroupStructedDetailService" /><!-- 接口名称，必须设置 -->
        <property name="timeout" value="500" /><!-- 超时时间，毫秒，建议自己设置 -->
        <property name="callType" value="future" /><!-- 调用方式，sync/future/callback/oneway，默认sync，可不设置 -->
    </bean>

    <bean id="dealGroupDzxServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.dianping.deal.style.DealGroupDzxService"/>
        <property name="interfaceName" value="com.dianping.deal.style.DealGroupDzxService"/>
        <property name="timeout" value="500" />
        <property name="callType" value="future" />
        <property name="timeoutRetry" value="true" />
        <property name="retries" value="3" />
    </bean>

    <bean id="beautyStructureServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="beauty.deal.beautyStructureService"/>
        <property name="interfaceName" value="com.dianping.beauty.deal.service.BeautyStructureService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="onlineTechQueryServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.sankuai.technician.info.online.service.OnlineTechQueryService"/>
        <property name="interfaceName" value="com.sankuai.technician.info.online.service.OnlineTechQueryService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="ktvServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/tpfunService/ktvProductService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.tpfun.product.api.ktv.KTVService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="1000"/>
    </bean>



    <!-- 防飞单 -->
    <bean id="dealGroupAntiFleeOrderServiceFuture"
          class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url"
                  value="http://service.dianping.com/tuangou/dealService/dealGroupAntiFleeOrderService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.deal.detail.DealGroupAntiFleeOrderService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="productSceneSalesDisplayServiceFuture"
          class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.dianping.deal.sales.display.api.service.ProductSceneSalesDisplayService"/>
        <property name="interfaceName"
                  value="com.dianping.deal.sales.display.api.service.ProductSceneSalesDisplayService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="dealGroupBestShopQueryServiceFuture"
          class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url"
                  value="http://service.dianping.com/tuangou/dealShopService/dealGroupBestShopQueryService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.deal.shop.DealGroupBestShopQueryService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="dealGroupBestShopQueryService"
          class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url"
                  value="http://service.dianping.com/tuangou/dealShopService/dealGroupBestShopQueryService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.deal.shop.DealGroupBestShopQueryService"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="dealShopQueryServiceFuture"
          class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url"
                  value="http://service.dianping.com/tuangou/dealShopService/dealShopQueryService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.deal.shop.DealShopQueryService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="dealBookQueryServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.dianping.deal.book.api.DealBookQueryService"/>
        <property name="interfaceName" value="com.dianping.deal.book.api.DealBookQueryService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="dealStyleServiceFuture" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/dealStyleService/dealStyleService_1.0.0"/>
        <property name="iface" value="com.dianping.deal.style.DealStyleService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="extraStyleServiceFuture" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/dealExtraStyleService/dealExtraStyleService_1.0.0"/>
        <property name="iface" value="com.dianping.deal.style.spi.ExtraStyleService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="prometheusWrapperServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/tuangou/bjwrapper/prometheusWrapperService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.tuangou.dztg.bjwrapper.api.PrometheusWrapperService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="3000"/>
    </bean>

    <bean id="bookProductServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/dentistryBookService/bookProductService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.dentistry.book.BookProductService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="dealGroupShopFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/tuangou/dealShopService/dealGroupShopService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.deal.shop.DealGroupShopService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="dealGroupShopService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/tuangou/dealShopService/dealGroupShopService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.deal.shop.DealGroupShopService"/>
        <property name="timeout" value="1000"/>
    </bean>

    <bean id="shopOnlineDealGroupServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.dianping.deal.shop.ShopOnlineDealGroupService"/>
        <property name="interfaceName" value="com.dianping.deal.shop.ShopOnlineDealGroupService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="dealActivityQueryServiceFuture" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/gmkt_activity_service/DealActivityQueryService_0.0.1" />
        <property name="iface" value="com.dianping.gmkt.activity.api.service.DealActivityQueryService" />
        <property name="serialize" value="hessian" />
        <property name="callMethod" value="future" />
        <property name="timeout" value="500" />
    </bean>

    <bean id="structuredModuleQueryServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.dianping.deal.struct.query.api.StructuredModuleQueryService"/>
        <property name="interfaceName" value="com.dianping.deal.struct.query.api.StructuredModuleQueryService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <!-- 结算价服务 -->
    <bean id="dealGroupVoucherQueryService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.dianping.deal.voucher.query.api.DealGroupVoucherQueryService"/>
        <property name="interfaceName" value="com.dianping.deal.voucher.query.api.DealGroupVoucherQueryService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="dealGroupCustomerQueryService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.dianping.deal.voucher.query.api.DealGroupCustomerQueryService"/>
        <property name="interfaceName" value="com.dianping.deal.voucher.query.api.DealGroupCustomerQueryService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="dealShopQueryService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/tuangou/dealShopService/dealShopQueryService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.deal.shop.DealShopQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>

    <bean id="dealDetailStructuredQueryServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.dianping.deal.struct.query.api.DealDetailStructuredQueryService"/>
        <property name="interfaceName" value="com.dianping.deal.struct.query.api.DealDetailStructuredQueryService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="tagQueryServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.dianping.deal.tag.TagQueryService"/>
        <property name="interfaceName" value="com.dianping.deal.tag.TagQueryService"/>
        <property name="callType" value="future"/>
        <property name="serialize" value="hessian"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="shopSceneSalesDisplayServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.dianping.deal.sales.display.api.service.ShopSceneSalesDisplayService"/>
        <property name="interfaceName" value="com.dianping.deal.sales.display.api.service.ShopSceneSalesDisplayService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="dealGroupThirdPartyServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/tuangou/dealService/dealGroupThirdPartyService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.deal.detail.DealGroupThirdPartyService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="productSaleQueryServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.dianping.deal.sale.api.service.ProductSaleQueryService"/>
        <property name="interfaceName" value="com.dianping.deal.sale.api.service.ProductSaleQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="1000"/>
    </bean>

    <!-- sinai -->
    <import resource="classpath:poi-client-server.xml"/>

    <bean id="localAppKey4Sinai" class= "java.lang.String">
        <constructor-arg value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
    </bean>

    <!--商户在线预约属性-->
    <bean id="bookStatusGatewayService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.clr.content.process.gateway.thrift.api.BookStatusGatewayService"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="remoteAppkey" value="com.sankuai.leads.content.process"/>
        <property name="timeout" value="1000" />
    </bean>

    <!--商户在线预约属性-->
    <bean id="bookStatusGatewayServiceFuture" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.clr.content.process.gateway.thrift.api.BookStatusGatewayService"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="remoteAppkey" value="com.sankuai.leads.content.process"/>
        <property name="timeout" value="1000" />
        <property name="async" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="resvLeadsEntranceAfterTradeGatewayServiceFuture" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.clr.content.process.gateway.thrift.common.user.trade.ResvLeadsEntranceAfterTradeGatewayService"/>
        <property name="remoteAppkey" value="com.sankuai.leads.content.process"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="timeout" value="1000" />
        <property name="async" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>


    <!-- poi上门属性 -->
    <bean id="poiBizServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.dianping.poi.biz.api.service.PoiBizService"/>
        <property name="interfaceName" value="com.dianping.poi.biz.api.service.PoiBizService"/>
        <property name="timeout" value="500"/>
        <property name="callType" value="future" />
    </bean>

    <!-- poi上门属性 -->
    <bean id="poiBizService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.dianping.poi.biz.api.service.PoiBizService"/>
        <property name="interfaceName" value="com.dianping.poi.biz.api.service.PoiBizService"/>
        <property name="timeout" value="1000"/>
        <property name="cluster" value="failover" />
        <property name="timeoutRetry" value="true" />
        <property name="retries" value="3" />
    </bean>

    <bean id="techShopSearchService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.dianping.technician.service.shopsearch.TechShopSearchService"/>
        <property name="interfaceName" value="com.dianping.technician.service.shopsearch.TechShopSearchService"/>
        <property name="timeout" value="3000"/>
        <property name="cluster" value="failover" />
        <property name="timeoutRetry" value="true" />
        <property name="retries" value="2" />
    </bean>

    <bean id="techGoodsBindService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.dianping.technician.service.eme.TechGoodsBindService"/>
        <property name="interfaceName" value="com.dianping.technician.service.eme.TechGoodsBindService"/>
        <property name="timeout" value="3000"/>
        <property name="cluster" value="failover" />
        <property name="timeoutRetry" value="true" />
        <property name="retries" value="2" />
    </bean>

    <bean id="secKillServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/gmkt_activity_service/SecKillService_0.0.1"/>
        <property name="interfaceName" value="com.dianping.gmkt.activity.api.service.SecKillService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="1000"/>
    </bean>

    <bean id="contentFusion2CService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.mpmctcontent.query.thrift.api.ContentFusion2CService"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="remoteAppkey" value="com.sankuai.mpmctcontent.query"/>
        <property name="timeout" value="1000"/>
        <property name="async" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>


    <bean id="contentFusion2C4UrlService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.mpmctcontent.query.thrift.api.ContentFusion2CService"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="remoteAppkey" value="com.sankuai.mpmctcontent.query"/>
        <property name="timeout" value="1000"/>
    </bean>


    <bean id="dealDetailPageGWService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.mpmctcontent.application.thrift.api.content.DealDetailPageGWService" />
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb" />
        <property name="remoteAppkey" value="com.sankuai.mpmctcontent.application" />
        <property name="timeout" value="1000" />
        <property name="async" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>
    <bean id="bizHourForecastService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"  init-method="init">
        <property name="url" value="http://service.dianping.com/com.dianping.poi.bizhour.bizHourForecastService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.poi.bizhour.BizHourForecastService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>

    <pigeon:service id="dealUrlReplaceService"
                    interface="com.sankuai.dztheme.deal.DealUrlReplaceService"
                    ref="dealUrlReplaceServiceImpl"
                    url="com.sankuai.dztheme.deal.dztgdetail.DealUrlReplaceService"/>

    <pigeon:service id="dealMessageUnitService" interface="com.sankuai.dzim.message.spi.MessageUnitSpiService"
                    ref="dealMessageUnitServiceImpl"
                    url="com.sankuai.dztheme.deal.dztgdetail.DealMessageUnitService"/>

    <pigeon:service id="dealSkuSelectService" interface="com.dianping.deal.style.DealSkuSelectService"
                    ref="dealSkuSelectServiceImpl"
                    url="com.sankuai.dztheme.deal.dztgdetail.DealSkuSelectService"/>

    <bean id="dealSkuServiceFuture" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.sankuai.dztheme.deal.DealSkuService"/>
        <property name="interfaceName" value="com.sankuai.dztheme.deal.DealSkuService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="1000"/>
    </bean>

    <bean id="dealSkuService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.sankuai.dztheme.deal.DealSkuService"/>
        <property name="interfaceName" value="com.sankuai.dztheme.deal.DealSkuService"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="3000"/>
    </bean>

    <bean id="eduTechnicianVideoQueryFacade" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="com.sankuai.beautycontent.creator.application.edu.technicianVideo.facade.EduTechnicianVideoQueryFacade"/>
        <property name="interfaceName" value="com.sankuai.beautycontent.creator.application.edu.technicianVideo.facade.EduTechnicianVideoQueryFacade"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="100"/>
    </bean>

    <!-- SelfHelpBilliardService.queryAutoOpenTable   -->
    <bean id="selfHelpBilliardService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.dianping.joygeneral.api.thirdpart.SelfHelpBilliardService"/>
        <property name="interfaceName" value="com.dianping.joygeneral.api.thirdpart.SelfHelpBilliardService"/>
        <property name="callType" value="future"/>
        <property name="timeout" value="1000"/>
    </bean>

    <bean id="bnplAccessThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.fincreditpay.bnpl.client.access.thrift.IBNPLAccessThriftService"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="remoteAppkey" value="com.sankuai.fincreditpay.bnpl"/>
        <property name="timeout" value="1000"/>
        <property name="async" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

</beans>