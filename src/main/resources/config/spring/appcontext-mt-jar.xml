<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd"
       default-autowire="byName" default-init-method="init">

    <!-- profile 配置文件-->
    <import resource="classpath:config/appcontext-mt-import.xml"/>
    <import resource="classpath:geo-client/city-service.xml"/>

    <!-- 新升级prometheus 1.5.0后需要加一个key标示客户端 -->
    <bean id="prometheusClientKey" class="java.lang.String" scope="prototype">
        <constructor-arg value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
    </bean>

</beans>