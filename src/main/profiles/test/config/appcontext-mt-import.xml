<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd"
       default-autowire="byName" default-init-method="init">

    <!-- deal-client-test -->
    <import resource="classpath*:prometheus-client-service-test.xml"/>

    <bean id="mtSSOFilter" class="com.sankuai.it.sso.sdk.spring.FilterFactoryBean">
        <!-- 必须配置，以下二者需先到开放平台(http://open.sankuai.com)申请接入SSO后颁发，
             对应企平开放平台的AppKey和AppSecret -->
        <!--   ##根据实际情况指定接入SSO线下(取值test)或线上(取值prod)环境。
              ##请不要接入SSO的dev和staging环境，不提供稳定服务##
              默认不需要配置，SDK将根据客户端环境自动对齐，
              即dev、test对齐到SSO线下环境，staging和prod对齐到SSO线上环境
          -->
        <!-- 线上
        <property name="accessEnv" value="prod"/>
        <property name="clientId" value="9641f13a57"/>
        <property name="secret" value="60a6a739c5314ad6ac33638e5b441350"/>-->

        <!-- 线下-->
        <property name="accessEnv" value="test"/>
        <property name="clientId" value="e4a25828af"/>
        <property name="secret" value="ab64f8246b7747d8b790521c2460b76b"/>

        <!-- 需要 SSO 检查的 Url 配置, 多个以逗号分隔，允许换行
             单独配 includedUriList，includedUriList 以外的链接都不检查sso登录
                 单独配 excludedUriList，excludedUriList 以外的链接都会检查sso登录
                 includedUriList，excludedUriList 都有的时候，仅includedUriList有效，匹配路径{**} -->
        <property name="includedUriList" value="/includedUriListDO_NOT_USE_THIS_NOT_EXIST_URL"/>
        <!--        <property name="excludedUriList" value="/static/**,/octo/checkAlive"/>-->

    </bean>
</beans>