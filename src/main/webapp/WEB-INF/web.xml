<!DOCTYPE web-app PUBLIC
        "-//Sun Microsystems, Inc.//DTD Web Application 2.3//EN"
        "http://java.sun.com/dtd/web-app_2_3.dtd" >

<web-app>
    <display-name>Mapi DztgDetail Web Application</display-name>

    <!-- spring xml config files -->
    <context-param>
        <param-name>contextConfigLocation</param-name>
        <param-value>
            classpath*:config/spring/appcontext-*.xml
            classpath:config/spring/common/appcontext-picasso.xml
        </param-value>
    </context-param>

    <!--由Spring载入的Log4j配置文件位置 -->
    <context-param>
        <param-name>log4jConfigLocation</param-name>
        <param-value>classpath:log4j2.xml</param-value>
    </context-param>

    <!--Spring默认刷新Log4j配置文件的间隔,单位为毫秒 -->
    <context-param>
        <param-name>log4jRefreshInterval</param-name>
        <param-value>60000</param-value>
    </context-param>
    <filter>
        <filter-name>mtSSOFilter</filter-name>
        <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
        <init-param>
            <param-name>targetFilterLifecycle</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>
    <filter>
        <filter-name>InfFilter</filter-name>
        <filter-class>com.sankuai.oceanus.http.filter.InfFilter</filter-class>
        <init-param>
            <param-name>limit</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>
    <filter>
        <filter-name>cat-filter</filter-name>
        <filter-class>com.dianping.cat.servlet.CatFilter</filter-class>
    </filter>
    <filter>
        <filter-name>url-qps-filter</filter-name>
        <filter-class>com.dianping.deal.common.filter.UrlQpsFilter</filter-class>
    </filter>
    <filter>
        <filter-name>springFilter</filter-name>
        <filter-class>org.springframework.web.filter.RequestContextFilter</filter-class>
    </filter>
    <!-- 全链路压测 -->
    <filter>
        <filter-name>MtraceFilter</filter-name>
        <filter-class>com.meituan.mtrace.http.TraceFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>mtSSOFilter</filter-name>
        <!-- 将所有URI交给SSO过滤器管理 -->
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>InfFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>MtraceFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>cat-filter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>url-qps-filter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>springFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <listener>
        <listener-class>org.springframework.web.util.Log4jConfigListener</listener-class>
    </listener>
    <listener>
        <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
    </listener>
    <listener>
        <listener-class>com.dianping.cat.servlet.CatListener</listener-class>
    </listener>

    <servlet>
        <servlet-name>PearlServlet</servlet-name>
        <servlet-class>com.sankuai.pearl.framework.servlet.PearlServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PearlServlet</servlet-name>
        <url-pattern>*.bin</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>PearlServlet</servlet-name>
        <url-pattern>*.json</url-pattern>
    </servlet-mapping>

    <welcome-file-list>
        <welcome-file>index.jsp</welcome-file>
    </welcome-file-list>

</web-app>
