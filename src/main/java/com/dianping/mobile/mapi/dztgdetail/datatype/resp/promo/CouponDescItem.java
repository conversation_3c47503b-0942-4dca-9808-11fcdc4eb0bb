package com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ComBtn;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/12/13.
 */
@TypeDoc(description = "抵用券描述项")
@MobileDo(id = 0xae78)
public class CouponDescItem implements Serializable {
    /**
     * 商家券：券批次ID；平台券：券码ID，多个按照逗号隔开
     */
    @MobileField(key = 0xc7b5)
    private String unifiedcoupongroupids;

    @FieldDoc(description = "抵用券id")
    @MobileField(key = 0x2488)
    private int couponGroupId;

    @FieldDoc(description = "抵用券加密ID")
    @MobileField(key = 0x8625)
    private String encryptCouponGroupId;

    @FieldDoc(description = "名称，限【团购】指定商品")
    @MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "领取后8天有效")
    @MobileField(key = 0xfebf)
    private String desc;

    @FieldDoc(description = "抵用券金额")
    @MobileField(key = 0xfbe2)
    private String amount;

    @FieldDoc(description = "抵用券金额描述")
    @MobileField(key = 0x7c03)
    private String amountDesc;

    @FieldDoc(description = "领券状态 0：可领取 1：已领取")
    @MobileField(key = 0x53f)
    private int status;

    @FieldDoc(description = "按钮")
    @MobileField(key = 0x8ad0)
    private ComBtn button;

    @FieldDoc(description = "已领取Icon url")
    @MobileField(key = 0xd9b8)
    private String issuedIconUrl;

    @FieldDoc(description = "团单id")
    @MobileField(key = 0x9a1c)
    private String dealGroupId;

    public int getCouponGroupId() {
        return couponGroupId;
    }

    public void setCouponGroupId(int couponGroupId) {
        this.couponGroupId = couponGroupId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getAmountDesc() {
        return amountDesc;
    }

    public void setAmountDesc(String amountDesc) {
        this.amountDesc = amountDesc;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public ComBtn getButton() {
        return button;
    }

    public void setButton(ComBtn button) {
        this.button = button;
    }

    public String getIssuedIconUrl() {
        return issuedIconUrl;
    }

    public void setIssuedIconUrl(String issuedIconUrl) {
        this.issuedIconUrl = issuedIconUrl;
    }

    public String getEncryptCouponGroupId() {
        return encryptCouponGroupId;
    }

    public void setEncryptCouponGroupId(String encryptCouponGroupId) {
        this.encryptCouponGroupId = encryptCouponGroupId;
    }

    public String getUnifiedcoupongroupids() {
        return unifiedcoupongroupids;
    }

    public void setUnifiedcoupongroupids(String unifiedcoupongroupids) {
        this.unifiedcoupongroupids = unifiedcoupongroupids;
    }

    public String getDealGroupId() {
        return dealGroupId;
    }

    public void setDealGroupId(String dealGroupId) {
        this.dealGroupId = dealGroupId;
    }
}
