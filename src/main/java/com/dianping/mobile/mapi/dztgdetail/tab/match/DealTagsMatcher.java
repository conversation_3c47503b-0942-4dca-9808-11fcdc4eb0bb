package com.dianping.mobile.mapi.dztgdetail.tab.match;

import com.dianping.cat.Cat;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/19
 */
@Service
public class DealTagsMatcher extends AbstractMatcher<Long> implements DealTagsBasedMatcher {

    @Override
    public List<Long> dealTagsMatched() {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.tab.match.DealTagsMatcher.dealTagsMatched()");
        throw new UnsupportedOperationException("DealTagsMatcher中不支持dealTagsMatched()");
    }
}
