package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.flash;

import com.dianping.cat.Cat;
import com.dianping.deal.style.dto.laout.DealPageLayoutConfigDTO;
import com.dianping.deal.style.dto.laout.DealPageLayoutDTO;
import com.dianping.deal.style.dto.laout.DealPageLayoutQueryRequest;
import com.dianping.deal.style.dto.laout.DealPageLayoutQueryResponse;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsFlashDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FlashDealCtx;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.fetcher.DealLayoutFetcher;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: guangyujie
 * @Date: 2024/10/25 10:49
 */
@Slf4j
public class DealLayoutProcessor extends AbsFlashDealProcessor {

    @Resource
    private DealLayoutFetcher dealLayoutFetcher;

    @Override
    public void prepare(FlashDealCtx ctx) {
        DealPageLayoutQueryRequest request = new DealPageLayoutQueryRequest();
        request.setMtDealGroupId(ctx.getMtId());
        request.setUserId(ctx.getEnvCtx().getUserId());
        request.setDealCategoryId(ctx.getDealGroupDTO().getCategory().getCategoryId());
        request.setClientType(ctx.getEnvCtx().getDztgClientTypeEnum().getCode());
        request.setDeviceHeight(ctx.getDeviceHeight());
        request.setSource(ctx.getRequestSource());
        request.setAppVersion(ctx.getEnvCtx().getVersion());
        ctx.getFutureCtx().setDealPageLayoutFuture(dealLayoutFetcher.preQueryLayout(request));
    }

    @Override
    public void process(FlashDealCtx ctx) {
        try {
            DealPageLayoutQueryResponse response = (DealPageLayoutQueryResponse) ctx.getFutureCtx().getDealPageLayoutFuture().get();
            if ( Objects.isNull(response) || !response.isSuccess()){
                throw new IllegalArgumentException("查不到布局信息与闪开控制变量");
            }
            DealPageLayoutDTO dealPageLayoutDTO = response.getLayout();
            if (dealPageLayoutDTO == null || dealPageLayoutDTO.getConfig() == null) {
                throw new IllegalArgumentException("查不到布局信息与闪开控制变量");
            }
            if (!dealPageLayoutDTO.getConfig().isRender()) {
                Cat.logEvent("DealLayoutProcessor", "notRender");
            } else if (CollectionUtils.isEmpty(dealPageLayoutDTO.getComponents())) {
                Cat.logEvent("DealLayoutProcessor", "emptyComponents");
            }
            ctx.setLayoutConfig(dealPageLayoutDTO.getConfig());
            ctx.setLayoutComponents(dealPageLayoutDTO.getComponents());
        } catch (Exception e) {
            Cat.logEvent("DealLayoutProcessor", "error");
            log.error("DealLayoutProcessor.process", e);
            ctx.setLayoutConfig(DealPageLayoutConfigDTO.getDefaultConfig());
        }
    }

}
