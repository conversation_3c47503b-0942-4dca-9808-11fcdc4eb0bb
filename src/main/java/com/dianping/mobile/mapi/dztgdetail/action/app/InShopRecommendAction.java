package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedRecommendReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedDeals;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedRecommendVO;
import com.dianping.mobile.mapi.dztgdetail.facade.RelatedRecommendFacade;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2024/10/23
 */
@InterfaceDoc(displayName = "同店推荐查询接口",
        type = "restful",
        description = "查询同店店推荐商品",
        scenarios = "查询同店店推荐商品",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "zhengjie27"
)
@Controller("general/platform/dztgdetail/inshoprecommend.bin")
@Action(url = "inshoprecommend.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class InShopRecommendAction extends AbsAction<RelatedRecommendReq> {
    @Resource
    private RelatedRecommendFacade relatedRecommendFacade;

    @Override
    protected IMobileResponse validate(RelatedRecommendReq request, IMobileContext iMobileContext) {
        IdUpgradeUtils.processProductIdForRelatedRecommendReq(request, "inshoprecommend.bin");
        if(request == null || request.getDealGroupId() <= 0){
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "inshoprecommend.bin",
            displayName = "查询到综团单推荐团单信息",
            description = "查询到综团单推荐团单信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "inshoprecommend.bin请求参数",
                            type = DealBaseReq.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "result", description = "团购picasso数据",type = RelatedDeals.class)},
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    ),
            }
    )
    @Override
    @CryptMethod
    protected IMobileResponse execute(RelatedRecommendReq request, IMobileContext iMobileContext) {
        try {
            EnvCtx envCtx = initEnvCtxV2(iMobileContext);
            RelatedRecommendVO result = relatedRecommendFacade.getInShopRecommendResult(request, envCtx);
            if(result != null){
                return new CommonMobileResponse(result);
            }
        } catch (Exception e) {
            logger.error("getInShopRecommendResult.bin error",e);
        }
        return new CommonMobileResponse(new RelatedRecommendVO());
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}