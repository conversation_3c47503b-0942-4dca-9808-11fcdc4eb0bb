package com.dianping.mobile.mapi.dztgdetail.action;

import com.dianping.account.dto.VirtualBindUserInfoDTO;
import com.dianping.cat.Cat;
import com.dianping.mobile.framework.action.DefaultAction;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.dianping.mobile.framework.datatypes.token.UserStatusResult;
import com.dianping.mobile.gatekeeper.api.model.protocol.UserInfo;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.UserAccountWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Cookies;
import com.dianping.mobile.mapi.dztgdetail.common.constants.H5params;
import com.dianping.mobile.mapi.dztgdetail.common.constants.TracerContextConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MpAppIdEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.AppCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.util.useragent.UserAgentUtils;
import com.maoyan.mtrace.Tracer;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.pearl.framework.constant.RequestConstants;
import com.sankuai.pearl.framework.context.MobileContextAdaptor;
import com.sankuai.pearl.framework.enums.MiniProgramType;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.Cookie;
import java.util.Locale;
import java.util.Objects;

import static com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum.*;

public abstract class AbsAction<T extends IMobileRequest> extends DefaultAction<T> {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private UserAccountWrapper userAccountWrapper;

    /**
     * 用于APP环境下转换EnvCtx，其他站点适用的话，可能存在部分数据无法获取的情况，比如userid、clienttype
     * 不要再使用initEnvCtx了，这个方法会导致validToken调用两次，且在点评侧会把mtUserId错误地设置为dpUserId
     */
    protected EnvCtx initEnvCtxWithOutUserInfo(IMobileContext appCtx) {
        EnvCtx result = new EnvCtx();

        if (appCtx == null) {
            return result;
        }

        result.setClientType(AppCtxHelper.getAppClientType(appCtx));
        DztgClientTypeEnum dztgClientTypeEnum = getDztgClientTypeEnum(appCtx, false);
        result.setDztgClientTypeEnum(dztgClientTypeEnum);

        String mpAppId = appCtx.getRequest().getHeader("mpAppId");
        String mpSource = appCtx.getRequest().getHeader("mpSource");
        result.setMpAppId(mpAppId);
        result.setMpSource(mpSource);
        result.setRequestURI(appCtx.getRequest().getRequestURI());
        result.setUserAgent(appCtx.getUserAgent());
        result.setUserIp(appCtx.getUserIp());
        result.setUuid(appCtx.getHeader().getUuid());
        setOpenId(result, appCtx);
        result.setHarmony(isPureHarmony(appCtx));
        result.setAppDeviceId(appCtx.getAppDeviceId());
        result.setAppId(appCtx.getAppId());
        result.setMtsiFlag(MapUtils.isEmpty(appCtx.getHeaders()) ? StringUtils.EMPTY : appCtx.getHeaders().get("mtsi-flag"));

        if (result.isExternal()) {
            String hostAppVersion = null;
            if (DztgClientTypeEnum.MEITUAN_MAP_APP.equals(dztgClientTypeEnum)) {
                hostAppVersion = appCtx.getVersion();
            } else {
                hostAppVersion = appCtx.getRequest().getHeader("hostAppVersion");
            }
            String appDeviceId = appCtx.getAppDeviceId();

            result.setVersion(hostAppVersion);

            result.setUnionId(appDeviceId);

            if (appCtx.isDianpingClient()) {
                result.setDpId(appDeviceId);
            } else {
                result.setUuid(appDeviceId);
            }
            // 第三方平台： 开店宝侧、阿波罗侧，设置APPversion
            if (THIRD_PLATFORM.equals(dztgClientTypeEnum)) {
                result.setThirdDztgClientTypeEnum(getThirdDztgClientTypeEnum(appCtx));
                result.setVersion(appCtx.isIOS() ? "11.4.10" : "11.7.10");
                // 第三方登录 进行SSO登录鉴权
                validateSSOStatus(appCtx);
            }
        } else {
            result.setVersion(appCtx.getVersion());
            if (appCtx.getHeader() != null) {
                result.setUnionId(appCtx.getHeader(RequestConstants.PRAGMA_UNIONID));
                result.setDpId(appCtx.getHeader().getDpid());
                result.setUuid(appCtx.getHeader().getUuid());
            }
        }
        // 设置一些参数到traceContext
        putContextForEnvCtx(result, appCtx);
        return result;
    }
    /**
     * 用于APP环境下转换EnvCtx，其他站点适用的话，可能存在部分数据无法获取的情况，比如userid、clienttype
     * 不要再使用initEnvCtx了，这个方法会导致validToken调用两次，且在点评侧会把mtUserId错误地设置为dpUserId
     */
    protected EnvCtx initEnvCtxV2(IMobileContext appCtx) {
        EnvCtx result = new EnvCtx();

        if (appCtx == null) {
            return result;
        }

        result.setClientType(AppCtxHelper.getAppClientType(appCtx));
        DztgClientTypeEnum dztgClientTypeEnum = getDztgClientTypeEnum(appCtx, false);
        result.setDztgClientTypeEnum(dztgClientTypeEnum);

        String mpAppId = appCtx.getRequest().getHeader("mpAppId");
        String mpSource = appCtx.getRequest().getHeader("mpSource");
        result.setMpAppId(mpAppId);
        result.setMpSource(mpSource);
        result.setRequestURI(appCtx.getRequest().getRequestURI());
        result.setUserAgent(appCtx.getUserAgent());
        result.setUserIp(appCtx.getUserIp());

        result.setUuid(appCtx.getHeader().getUuid());
        setOpenId(result, appCtx);
        result.setHarmony(isPureHarmony(appCtx));
        result.setAppDeviceId(appCtx.getAppDeviceId());
        result.setAppId(appCtx.getAppId());
        result.setMtsiFlag(MapUtils.isEmpty(appCtx.getHeaders()) ? StringUtils.EMPTY : appCtx.getHeaders().get("mtsi-flag"));
        if (result.isExternal()) {
            String hostAppVersion = null;
            if (DztgClientTypeEnum.MEITUAN_MAP_APP.equals(dztgClientTypeEnum)) {
                hostAppVersion = appCtx.getVersion();
            } else {
                hostAppVersion = appCtx.getRequest().getHeader("hostAppVersion");
            }
            String appDeviceId = appCtx.getAppDeviceId();

            result.setVersion(hostAppVersion);

            result.setUnionId(appDeviceId);

            if (appCtx.isDianpingClient()) {
                result.setDpId(appDeviceId);
            } else {
                result.setUuid(appDeviceId);
            }
            // 处理beam app
            if (UserAgentUtils.isBeamApp(appCtx.getUserAgent())) {
                result.setDztgClientTypeEnum(DztgClientTypeEnum.BEAM_APP);
                result.setUnionId(getUnionIdFromBeamApp(appCtx));
                String token = getNewToken(appCtx);
                VirtualBindUserInfoDTO userInfo = userAccountWrapper.loadUserByToken(token);
                if (userInfo != null) {
                    result.setMtUserId(userInfo.getMtid() != null ? userInfo.getMtid() : 0);
                    renewUserInfo(appCtx, result.getMtUserId());
                }
            }
            // 第三方平台： 开店宝侧、阿波罗侧，设置APPversion
            if (THIRD_PLATFORM.equals(dztgClientTypeEnum)) {
                result.setThirdDztgClientTypeEnum(getThirdDztgClientTypeEnum(appCtx));
                result.setVersion(appCtx.isIOS() ? "11.4.10" : "11.7.10");
                // 第三方登录 进行SSO登录鉴权
                validateSSOStatus(appCtx);
            }
        } else {
            result.setVersion(appCtx.getVersion());

            if (appCtx.getHeader() != null) {
                result.setUnionId(appCtx.getHeader(RequestConstants.PRAGMA_UNIONID));
                result.setDpId(appCtx.getHeader().getDpid());
                result.setUuid(appCtx.getHeader().getUuid());
            }
        }

        long userId = appCtx.getUserId();
        if (result.isMt()) {
            result.setMtUserId(userId);
        } else {
            result.setDpUserId(userId);
        }

        result.setPragmaToken(appCtx.getRequest().getHeader("pragma-token"));
        // 设置一些参数到traceContext
        putContextForEnvCtx(result, appCtx);
        return result;
    }

    /**
     * 用于APP环境下转换EnvCtx，其他站点适用的话，可能存在部分数据无法获取的情况，比如userid、clienttype
     * 不要再使用了，这个服务会导致validToken调用两次，且在点评侧会把mtUserId错误地设置为dpUserId
     * 请使用initEnvCtxV2
     */
    @Deprecated
    protected EnvCtx initEnvCtx(IMobileContext appCtx) {
        EnvCtx result = new EnvCtx();

        if (appCtx == null) {
            return result;
        }

        result.setClientType(AppCtxHelper.getAppClientType(appCtx));
        DztgClientTypeEnum dztgClientTypeEnum = getDztgClientTypeEnum(appCtx, false);
        result.setDztgClientTypeEnum(dztgClientTypeEnum);

        String mpAppId = appCtx.getRequest().getHeader("mpAppId");
        String mpSource = appCtx.getRequest().getHeader("mpSource");
        result.setMpAppId(mpAppId);
        result.setMpSource(mpSource);
        result.setRequestURI(appCtx.getRequest().getRequestURI());
        result.setUserAgent(appCtx.getUserAgent());
        result.setUserIp(appCtx.getUserIp());

        result.setUuid(appCtx.getHeader().getUuid());
        setOpenId(result, appCtx);
        result.setHarmony(isPureHarmony(appCtx));
        result.setAppDeviceId(appCtx.getAppDeviceId());
        result.setAppId(appCtx.getAppId());
        result.setMtsiFlag(MapUtils.isEmpty(appCtx.getHeaders()) ? StringUtils.EMPTY : appCtx.getHeaders().get("mtsi-flag"));
        if (result.isExternal()) {
            String hostAppVersion = null;
            if(DztgClientTypeEnum.MEITUAN_MAP_APP.equals(dztgClientTypeEnum)) {
                hostAppVersion = appCtx.getVersion();
            } else {
                hostAppVersion = appCtx.getRequest().getHeader("hostAppVersion");
            }
            String appDeviceId = appCtx.getAppDeviceId();

            result.setVersion(hostAppVersion);

            result.setUnionId(appDeviceId);

            if (appCtx.isDianpingClient()) {
                result.setDpId(appDeviceId);
            } else {
                result.setUuid(appDeviceId);
            }
            // 处理beam app
            if (UserAgentUtils.isBeamApp(appCtx.getUserAgent())) {
                result.setDztgClientTypeEnum(DztgClientTypeEnum.BEAM_APP);
                result.setUnionId(StringUtils.isBlank(result.getUnionId()) ? appCtx.getHeader(RequestConstants.PRAGMA_UNIONID) : result.getUnionId());
                String token = getNewToken(appCtx);
                VirtualBindUserInfoDTO userInfo = userAccountWrapper.loadUserByToken(token);
                if (userInfo != null) {
                    result.setMtUserId(userInfo.getMtid() != null ? userInfo.getMtid() : 0);
                    renewUserInfo(appCtx, result.getMtUserId());
                }
            }
            // 第三方平台： 开店宝侧、阿波罗侧，设置APPversion
            if (THIRD_PLATFORM.equals(dztgClientTypeEnum)){
                result.setThirdDztgClientTypeEnum(getThirdDztgClientTypeEnum(appCtx));
                result.setVersion(appCtx.isIOS() ? "11.4.10" : "11.7.10");
                // 第三方登录 进行SSO登录鉴权
                validateSSOStatus(appCtx);
            }
        } else {
            result.setVersion(appCtx.getVersion());

            if (appCtx.getHeader() != null) {
                result.setUnionId(appCtx.getHeader(RequestConstants.PRAGMA_UNIONID));
                result.setDpId(appCtx.getHeader().getDpid());
                result.setUuid(appCtx.getHeader().getUuid());
            }
        }

        if (appCtx.getUserStatus() != null) {//这里有问题，这个废弃方法会额外调用一次validToken，应该替换成getUserId
            long userId = appCtx.getUserId();
            result.setDpUserId(appCtx.getUserStatus().getUserId());
            //这里有bug，如果是点评侧，userId是点评userId，但是下面的判断条件会导致美团userId被set成点评userId
            result.setMtUserId(userId == 0 ? appCtx.getUserStatus().getMtUserId() : userId);
        }
        // 设置一些参数到traceContext
        putContextForEnvCtx(result, appCtx);
        return result;
    }

    public void putContextForEnvCtx(EnvCtx envCtx, IMobileContext appCtx) {
        if (Objects.isNull(envCtx) || Objects.isNull(appCtx) || Objects.isNull(appCtx.getRequest())) {
            return;
        }
        try {
            // 用户ID
            String userId = String.valueOf(envCtx.isMt() ? envCtx.getMtUserId() : envCtx.getDpUserId());//已确认判断平台后再使用
            if (StringUtils.isNotBlank(userId)) {
                Tracer.putContext(TracerContextConstants.USER_ID_STR, userId);
            }
            // 客户端名称
            if (Objects.nonNull(envCtx.getDztgClientTypeEnum())) {
                Tracer.putContext(TracerContextConstants.DZTG_CLIENT_TYPE, envCtx.getDztgClientTypeEnum().name());
            }
            // 接口名
            if (StringUtils.isNotBlank(envCtx.getRequestURI())) {
                Tracer.putContext(TracerContextConstants.API_NAME, envCtx.getRequestURI());
            }
            // unionId
            if (StringUtils.isNotBlank(envCtx.getUnionId())) {
                Tracer.putContext(TracerContextConstants.UNION_ID, envCtx.getUnionId());
            }
            // pageSource
            if (StringUtils.isNotBlank(appCtx.getRequest().getParameter("pagesource"))) {
                Tracer.putContext(TracerContextConstants.PAGE_SOURCE, appCtx.getRequest().getParameter("pagesource"));
            }
        } catch (Exception e) {
            log.error("[AbsAction] putContextForEnvCtx err", e);
        }
    }

    /**
     * SSO登录鉴权
     * @param appCtx
     */
    public void validateSSOStatus(IMobileContext appCtx){
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.action.AbsAction.validateSSOStatus(com.dianping.mobile.framework.datatypes.IMobileContext)");
        try{
            // 获取sso 登录信息
            User user = UserUtils.getUser(getNewToken(appCtx));
            if (Objects.nonNull(appCtx.getUserStatus())
                    && 0 == appCtx.getUserStatus().getUserId()
                    && Objects.nonNull(user)){
               renewUserStatus(appCtx, user.getId());
            }
            if (appCtx.getUserId() <= 0 && Objects.nonNull(user) && appCtx instanceof MobileContextAdaptor){
                // 如果没有 userid，设置userid
               renewUserInfo(appCtx, user.getId());
            }
        }catch (Exception e){
            logger.error("[AbsAction] validateSSOStatus err", e);
        }
    }

    /**
     * 更新UserStatus
     * @param appCtx
     * @param userId
     */
    private void renewUserStatus(IMobileContext appCtx, long userId){
        UserStatusResult userStatus = new UserStatusResult();
        userStatus.setUserId(userId);
        appCtx.setUserStatus(userStatus);
    }

    /**
     * 更新userInfo
     * @param appCtx
     * @param userId
     */
    private void renewUserInfo(IMobileContext appCtx, long userId){
        // 如果没有 userid，设置userid
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(userId);
        ((MobileContextAdaptor) appCtx).getProtocolContext().getClientInfo().setUserInfo(userInfo);
    }

    public static boolean isPureHarmony(IMobileContext appCtx) {
        String userAgent = appCtx.getRequest().getHeader("user-agent");
        if (StringUtils.isBlank(userAgent)) {
            return false;
        }
        return userAgent.toLowerCase(Locale.ROOT).contains("openharmony");
    }

    private DztgClientTypeEnum getDztgClientTypeEnum(IMobileContext appCtx, boolean isFromH5) {
        if(isFromH5) {
            if(appCtx.isMeituanClient()) {
                return DztgClientTypeEnum.MEITUAN_FROM_H5;
            } else if(appCtx.isDianpingClient()) {
                return DztgClientTypeEnum.DIANPING_FROM_H5;
            } else {
                return DztgClientTypeEnum.UNKNOWN;
            }
        }
        String mpAppId = appCtx.getRequest().getHeader("mpAppId");
        if(appCtx.getAppId() == 396) {
            return DztgClientTypeEnum.MEITUAN_MAP_APP;
        }
        if(appCtx.getMiniProgramType() == MiniProgramType.KuaiShou || MpAppIdEnum.MT_KUAISHOU_MINIPROGRAM.getMpAppId().equals(mpAppId)) {
            return DztgClientTypeEnum.MEITUAN_KUAISHOU_MINIAPP;
        }
        if(MpAppIdEnum.MT_WEIXIN_MINIPROGRAM.getMpAppId().equals(mpAppId)) {
            return DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP;
        }
        if(MpAppIdEnum.DP_WEIXIN_MINIPROGRAM.getMpAppId().equals(mpAppId)) {
            return DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP;
        }
        if(MpAppIdEnum.XIUYU_WANWU_MINIPROGRAM.getMpAppId().equals(mpAppId)) {
            return DztgClientTypeEnum.MEITUAN_WANWU_MINIAPP;
        }
        if(MpAppIdEnum.DP_BAIDUMAP_MINIPROGRAM.getMpAppId().equals(mpAppId)) {
            return DztgClientTypeEnum.DIANPING_BAIDUMAP_MINIAPP;
        }
        // 私域直播小程序
        if (MpAppIdEnum.MT_LIVE_WEIXIN_MINIPROGRAM.getMpAppId().equals(mpAppId)) {
            return DztgClientTypeEnum.MEITUAN_LIVE_WEIXIN_MINIAPP;
        }
        // 私域直播提单小程序
        if (MpAppIdEnum.MT_LIVE_ORDER_WEIXIN_MINIPROGRAM.getMpAppId().equals(mpAppId)) {
            return DztgClientTypeEnum.MEITUAN_LIVE_ORDER_WEIXIN_MINIAPP;
        }
        // beam app
        if (UserAgentUtils.isBeamApp(appCtx.getUserAgent())) {
            return DztgClientTypeEnum.BEAM_APP;
        }
        if(appCtx.isMeituanClient()) {
            return DztgClientTypeEnum.MEITUAN_APP;
        }
        if(appCtx.isDianpingClient()) {
            return DztgClientTypeEnum.DIANPING_APP;
        }
        // 判断是否为第三方平台 开店宝、阿波罗侧
        if (UserAgentUtils.isDpmerchant(appCtx.getUserAgent())
                || UserAgentUtils.isApollo(appCtx.getUserAgent())){
            return DztgClientTypeEnum.THIRD_PLATFORM;
        }
        return DztgClientTypeEnum.UNKNOWN;
    }

    private DztgClientTypeEnum getThirdDztgClientTypeEnum(IMobileContext appCtx) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.action.AbsAction.getThirdDztgClientTypeEnum(com.dianping.mobile.framework.datatypes.IMobileContext)");
        // 判断是否为 开店宝侧
        if (UserAgentUtils.isDpmerchant(appCtx.getUserAgent())){
            return DPMERCHANT;
        }
        // 判断是否为 阿波罗侧
        if (UserAgentUtils.isApollo(appCtx.getUserAgent())){
            return APOLLO;
        }
        return DztgClientTypeEnum.UNKNOWN;
    }

    /**
     * 用于非APP环境，比如M站、小程序等站点转换EnvCtx
     *
     * @param context
     * @param needUserId 是否需要解析用户ID
     * @return
     */
    protected EnvCtx initEnvCtxFromH5(IMobileContext context, boolean needUserId) {
        EnvCtx envCtx = initEnvCtx(context);
        envCtx.setFromH5(true);
        DztgClientTypeEnum dztgClientTypeEnum = getDztgClientTypeEnum(context, true);
        envCtx.setDztgClientTypeEnum(dztgClientTypeEnum);

        String clientTypeStr = context.getParameter(H5params.CLIENT_TYPE);
        if (StringUtils.isNotBlank(clientTypeStr)) {
            envCtx.setClientType(NumberUtils.toInt(clientTypeStr));
        }
        if (!needUserId) {
            return envCtx;
        }
        String token = getToken(context);
        VirtualBindUserInfoDTO userInfo = userAccountWrapper.loadUserByToken(token);
        if (userInfo != null) {
            envCtx.setMtUserId(userInfo.getMtid() != null ? userInfo.getMtid() : 0);
            envCtx.setDpUserId(userInfo.getDpid() != null ? userInfo.getDpid() : 0);
        }
        return envCtx;
    }

    protected String getToken(IMobileContext context) {
        String token = context.getParameter(H5params.TOKEN);
        if (StringUtils.isBlank(token)) {
            token = getCookieValue(context, Cookies.TOKEN);
        }
        if (StringUtils.isBlank(token)) {
            token = getCookieValue(context, Cookies.DPER);
        }
        if (StringUtils.isBlank(token)) {
            token = getCookieValue(context, Cookies.TOKEN2);
        }
        if (StringUtils.isBlank(token)) {
            token = getCookieValue(context, Cookies.OOPS);
        }
        return token;
    }

    protected String getCookieValue(IMobileContext context, String cookieName) {
        if (context == null || context.getRequest() == null) {
            return null;
        }
        Cookie[] cookies = context.getRequest().getCookies();
        if (ArrayUtils.isEmpty(cookies)) {
            return null;
        }
        for (Cookie cookie : cookies) {
            if (cookie.getName().equals(cookieName)) {
                return cookie.getValue();
            }
        }
        return null;
    }

    private String getNewToken(IMobileContext context) {
        /* token = new token || header.token || context.token */
        String token = context.getHeader().getNewToken();

        if (org.apache.commons.lang.StringUtils.isBlank(token)) {
            token = context.getHeader().getToken();
        }

        if (org.apache.commons.lang.StringUtils.isBlank(token)) {
            token = context.getParameter("token");
        }
        // 取cookie中的token
        if (StringUtils.isBlank(token)) {
            token = getCookieValue(context, Cookies.TOKEN);
        }
        return token;
    }

    private String getUnionIdFromBeamApp(IMobileContext context) {
        String unionId = context.getAppDeviceId();
        if (StringUtils.isBlank(unionId)) {
            unionId = context.getHeader(RequestConstants.PRAGMA_UNIONID);
        }
        if (StringUtils.isBlank(unionId)) {
            unionId = getCookieValue(context, "_lxsdk_cuid");
        }
        return unionId;
    }

    // 获取微信小程序openid明文
    // 美小: openid是明文，openIdCipher是密文
    // 点小: openidplt是明文，openId是密文
    public static String getOpenId(IMobileContext iMobileContext) {
        return iMobileContext.isMeituanClient() ? iMobileContext.getRequest().getHeader("openid")
                : iMobileContext.getRequest().getHeader("openidplt");
    }

    // 获取微信小程序openid密文
    public static String getOpenIdCipher(IMobileContext iMobileContext) {
        return iMobileContext.isMeituanClient() ? iMobileContext.getRequest().getHeader("openidcipher")
                : iMobileContext.getRequest().getHeader("openid");
    }

    // 设置微信小程序的openid
    public void setOpenId(EnvCtx result, IMobileContext appCtx){
        if (!MiniProgramType.WeChat.equals(appCtx.getMiniProgramType())) {
            return;
        }
        result.setOpenId(getOpenId(appCtx));
        result.setOpenIdCipher(getOpenIdCipher(appCtx));
    }
}
