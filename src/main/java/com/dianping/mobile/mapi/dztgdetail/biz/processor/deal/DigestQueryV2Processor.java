package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DigestQueryWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.DigestInfoDTO;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.sankuai.mpmctcontent.query.thrift.dto.digest.DigestInfoItemDTO;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;
import java.util.concurrent.Future;

/**
 *
 */
public class DigestQueryV2Processor extends AbsDealProcessor {
    @Autowired
    private DigestQueryWrapper digestQueryWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        return 506 == ctx.getCategoryId();
    }

    @Override
    public void prepare(DealCtx ctx) {
        Future usePlatformHeadPicFuture = digestQueryWrapper.getUsePlatformHeadPicFuture(ctx);
        ctx.getFutureCtx().setUsePlatformHeadPicFuture(usePlatformHeadPicFuture);
    }

    @Override
    public void process(DealCtx ctx) {
        if (Objects.nonNull(ctx.getFutureCtx().getUsePlatformHeadPicFuture())){
            Future future = ctx.getFutureCtx().getUsePlatformHeadPicFuture();

            DigestInfoItemDTO digestInfoItemDTO = digestQueryWrapper.getDigestInfoItemDTO(future, ctx.getMtLongShopId());
            String dataJson = digestInfoItemDTO.getData();
            JSONObject dataJSONObject = JSON.parseObject(dataJson);
            if (Objects.nonNull(dataJSONObject)) {
                Boolean usePlatformHeadPic = dataJSONObject.getBoolean("usePlatformHeadPic");
                ctx.setUsePlatformHeadPic(usePlatformHeadPic);
            }
        }
    }


}
