package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DealMenuTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MtTerm;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RedeemTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.MtMenu;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.MtMenuDetail;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.MtPriceCalendar;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by guozhengyao on 16/4/21.
 */
@Slf4j
public class DealFieldHelper {

    //特殊的空格
    private static final String SPECIAL_SPACE = "\u00A0";
    //普通的空格
    private static final String SPACE = " ";

    public static int getRedeemTypeFromHowUse(String howuse) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.helper.DealFieldHelper.getRedeemTypeFromHowUse(java.lang.String)");
        try {
            return RedeemTypeEnum.getRedeemTypeEnum(new JSONObject(howuse).optString("key")).getType();
        } catch (JSONException e) {
            log.error("getRedeemTypeFromHowUse has error", e);
        }
        return RedeemTypeEnum.DEFAULT.getType();
    }

    public static List<MtMenu> getMenusFromJson(String menu, int redeemType) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.helper.DealFieldHelper.getMenusFromJson(java.lang.String,int)");
        if (StringUtils.isBlank(menu)) {
            return null;
        }
        List<MtMenu> result = Lists.newArrayList();
        try {
            //将menu无用的两级JsonArray格式全部提出装入menuStrList
            List<String> menuStrList = Lists.newArrayList();
            JSONArray jsonArray = new JSONArray(menu);
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONArray subArray = jsonArray.getJSONArray(i);
                for (int j = 0; j < subArray.length(); j++) {
                    menuStrList.add(subArray.optString(j));
                }
            }

            if (CollectionUtils.isEmpty(menuStrList)) {
                return null;
            }

            //用于标记正在给result中第几个menu添加detail或者tips
            int index = -1;
            for (String menuStr : menuStrList) {
                JSONObject object = new JSONObject(menuStr);
                String content = object.optString("content");
                int type = object.optInt("subtype");
                if (type == DealMenuTypeEnum.TITLE.getType() || type == DealMenuTypeEnum.SUBTITLE.getType()) {
                    //每遇到一个title和subTitle就新增一个menu，此后的detail,tip都添加在这个menu中
                    MtMenu mtMenu = new MtMenu();
                    mtMenu.setTitle(content);
                    result.add(mtMenu);
                    index++;
                } else if (type == DealMenuTypeEnum.DETAIL.getType()) {
                    MtMenu mtMenu = null;
                    if (result.size() > index && index >= 0) {
                        mtMenu = result.get(index);
                    } else {
                        mtMenu = new MtMenu();
                        result.add(mtMenu);
                        index++;
                    }

                    MtMenuDetail mtMenuDetail = new MtMenuDetail();
                    mtMenuDetail.setContent(content);
                    mtMenuDetail.setTotalPrice("¥" + object.optString("total"));
                    mtMenuDetail.setSpecification(object.optString("specification"));

                    List<MtMenuDetail> menuDetailList = mtMenu.getMenuDetails() == null ?
                            new ArrayList<MtMenuDetail>() : mtMenu.getMenuDetails();
                    menuDetailList.add(mtMenuDetail);
                    mtMenu.setMenuDetails(menuDetailList);
                } else if (type == DealMenuTypeEnum.TIP.getType()) {
                    MtMenu mtMenu = null;
                    if (result.size() > index && index >= 0) {
                        mtMenu = result.get(index);
                    } else {
                        mtMenu = new MtMenu();
                        result.add(mtMenu);
                        index++;
                    }
                    List<String> tips = mtMenu.getTips() == null ? new ArrayList<>() : mtMenu.getTips();
                    //文案中可能存在特殊空格，需要替换后，才可以进行isBlank的判断
                    String contentStr = replaceSpecialSpace(content);
                    if (StringUtils.isNotBlank(contentStr)) {
                        tips.add(contentStr.trim());
                        mtMenu.setTips(tips);
                    }
                }
            }
        } catch (Exception e) {
            log.error("getMenusFromJson has error!", e);
        }
        if (redeemType == RedeemTypeEnum.THIRD_PARTY.getType() && result.size() > 0) {
            result.get(result.size() - 1).setHint(
                    "温馨提示：本单需要到第三方网站兑换后才能使用，建议在电脑上进行操作");
        }
        return result;
    }

    public static List<MtPriceCalendar> getPriceCalendarFromJson(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        try {
            List<MtPriceCalendar> result = Lists.newArrayList();
            JSONArray jsonResult = new JSONArray(str);

            for (int i = 0; i < jsonResult.length(); i++) {
                JSONObject object = jsonResult.getJSONObject(i);
                MtPriceCalendar mtPriceCalendar = new MtPriceCalendar();
                mtPriceCalendar.setId(object.optInt("id"));
                mtPriceCalendar.setDesc(object.optString("desc"));
                mtPriceCalendar.setEndTime(new Date(object.optLong("endtime")));
                mtPriceCalendar.setStartTime(new Date(object.optLong("starttime")));
                mtPriceCalendar.setPrice(object.optDouble("price"));
                mtPriceCalendar.setCanBuyPrice(object.optDouble("canbuyprice"));
                mtPriceCalendar.setBuyPrice(object.optDouble("buyprice"));
                mtPriceCalendar.setDealId(object.optInt("dealid"));
                mtPriceCalendar.setType(object.optInt("type"));

                Object rangeJSONArray = object.opt("range");
                JSONArray jsonContent = new JSONArray(rangeJSONArray.toString());
                List<String> rangeList = Lists.newArrayList();
                for (int j = 0; j < jsonContent.length(); j++) {
                    rangeList.add(jsonContent.optString(j));
                }
                mtPriceCalendar.setRange(rangeList);
                result.add(mtPriceCalendar);
            }
            return result;
        } catch (JSONException e) {
            log.error("getPriceCalendarFromJson has error", e);
        }
        return null;
    }

    public static List<MtTerm> genMtTermListFromTermStr(String terms) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.helper.DealFieldHelper.genMtTermListFromTermStr(java.lang.String)");
        if (StringUtils.isBlank(terms)) {
            return Lists.newArrayList();
        }
        List<MtTerm> list = Lists.newArrayList();
        try {
            JSONArray jsonArray = new JSONArray(terms);
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                MtTerm mtTerm = new MtTerm();
                mtTerm.setTitle((String) jsonObject.get("title"));
                Object contentJSONArray = jsonObject.get("content");
                JSONArray jsonContent = new JSONArray(contentJSONArray.toString());
                List<String> contents = Lists.newArrayList();
                for (int j = 0; j < jsonContent.length(); j++) {
                    contents.add((String) jsonContent.get(j));
                }
                mtTerm.setContents(contents);
                list.add(mtTerm);
            }
            return list;
        } catch (Exception e) {
            log.error("genJSONArrayWithString has failed:" + terms, e);
        }
        return Lists.newArrayList();
    }

    //替换String存在的特殊空格
    private static String replaceSpecialSpace(String str) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.helper.DealFieldHelper.replaceSpecialSpace(java.lang.String)");
        if (StringUtils.isBlank(str)) {
            return str;
        }
        return str.replaceAll(SPECIAL_SPACE, SPACE);
    }
}
