package com.dianping.mobile.mapi.dztgdetail.button.joy;

import com.alibaba.fastjson.JSON;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.SaleStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.helper.BuyButtonHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.PriceHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class NewIdlePromoButtonBuilder extends AbstractPriceServiceButtonBuilder {

    @Override
    protected PriceDisplayDTO getPrice(DealCtx context) {
        PriceDisplayDTO promoPrice = context.getPriceContext().getIdlePromoPrice();
        if (promoPrice == null || CollectionUtils.isEmpty(promoPrice.getUsedPromos())) {
            return null;
        }

        if (BuyButtonHelper.isDaoGua(promoPrice.getPrice(), context)) {
            log.debug("团单id={},闲时立减倒挂,闲时立减={},常规价格={}", context.getDealId4P(), JSON.toJSONString(promoPrice), JSON.toJSONString(PriceHelper.getNormalPrice(context)));
            return null;
        }

        return promoPrice;
    }

    @Override
    protected void afterBuild(DealCtx context, DealBuyBtn button) {
        button.setDetailBuyType(BuyBtnTypeEnum.IDLE_DEAL.getCode());
        String title = getPrice(context).getUsedPromos().get(0).getConsumeTimeDesc() + "可用";
        String url = UrlHelper.getIdleHoursBuyUrl(context, context.getMtCityId());
        button.setRedirectUrl(url);

        //若是预热单，更新title&&设置按钮的售卖状态
        if(StringUtils.isNotBlank(context.getSaleStatus())){
            button.setSaleStatus(context.getSaleStatus());
            button.setBtnTitle(getSaleStatusButtonTitle(context));
        }else{
            button.setBtnTitle(getPromoButtonTitle(context, title));
        }

        buildBtnEnable(context, button);

        if (context.getPreButton() == null) {
            context.getBuyBar().setBuyType(DealBuyBar.BuyType.IDLE_PROMO.type);
        }
    }

    private void buildBtnEnable(DealCtx context, DealBuyBtn button) {
        if(StringUtils.isNotBlank(context.getSaleStatus())){
            if(context.getSaleStatus().equals(SaleStatusEnum.SNAP_UP_NOW.saleStatusName)){
                button.setBtnEnable(true);
            }else{
                button.setBtnEnable(false);
            }
        }
    }
}
