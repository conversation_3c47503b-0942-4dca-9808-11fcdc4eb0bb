package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DzDealSkuSelectRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.SkuSelectReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.Response;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.sku.DzAttrDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.sku.SalesAttrToSkuBasicInfoDO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.sku.SkuBasicInfoDO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.sku.SkuSalesAttrDO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.sku.SkuSalesAttrExt;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.sku.SkuSalesAttrValueDO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.sku.SkuSalesAttrWithPicDO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.sku.SkuSelectorVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.sku.SkuSelectorWithPicVO;
import com.google.common.collect.Sets;
import com.sankuai.dztheme.deal.DealSkuService;
import com.sankuai.dztheme.deal.req.SkuOptionRequest;
import com.sankuai.dztheme.deal.res.AttrValueDTO;
import com.sankuai.dztheme.deal.res.DealSkuOptionDTO;
import com.sankuai.dztheme.deal.res.SkuAttrDTO;
import com.sankuai.dztheme.deal.res.SkuAttrMetaDTO;
import com.sankuai.dztheme.deal.res.SkuItemDTO;
import com.sankuai.general.product.query.center.client.builder.model.DealBasicInfoBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupIdBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealStockBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.general.product.query.center.client.enums.AttrSubjectEnum;
import com.sankuai.general.product.query.center.client.enums.AttrTypeEnum;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Component
public class SkuSelectorFacade {

    @Autowired
    private QueryCenterWrapper queryCenterWrapper;

    @Autowired
    @Qualifier("dealSkuService")
    private DealSkuService dealSkuService;

    public Response<SkuSelectorWithPicVO> querySkuSelectorWithPic(DzDealSkuSelectRequest request, EnvCtx envCtx) throws TException {
        SkuOptionRequest skuOptionRequest = new SkuOptionRequest();
        skuOptionRequest.setDealGroupId(Long.parseLong(request.getDealgroupid()));
        skuOptionRequest.setDealGroupIdType(envCtx.isMt() ? IdTypeEnum.MT.getCode() : IdTypeEnum.DP.getCode());
        skuOptionRequest.setShopId(NumberUtils.toLong(request.getShopid()));
        // 调pigeon接口取数
        DealSkuOptionDTO dealSkuOptionDTO = dealSkuService.querySkuOptions(skuOptionRequest);
        if (dealSkuOptionDTO == null || CollectionUtils.isEmpty(dealSkuOptionDTO.getSkuItemList()) || CollectionUtils.isEmpty(dealSkuOptionDTO.getSkuAttrMetaList())) {
            return Response.createSuccessResponse(new SkuSelectorWithPicVO());
        }

        // 数据映射 DealSkuOptionDTO -> SkuSelectorWithPicVO
        // sku列表映射 SkuItemDTO -> SalesAttrToSkuBasicInfoDO
        List<SkuItemDTO> skuItemList = dealSkuOptionDTO.getSkuItemList();
        List<SalesAttrToSkuBasicInfoDO> salesAttrToSkuBasicInfoDOList = new ArrayList<>();
        for (SkuItemDTO skuItemDTO : skuItemList) {
            SalesAttrToSkuBasicInfoDO salesAttrToSkuBasicInfoDO = buildSalesAttrToSkuBasicInfo(skuItemDTO, envCtx);
            if (salesAttrToSkuBasicInfoDO == null) {
                return Response.createErrorTipResponse("数据格式错误", null);
            }
            salesAttrToSkuBasicInfoDOList.add(salesAttrToSkuBasicInfoDO);
        }
        // spu元信息映射 SkuAttrMetaDTO -> SkuSalesAttrWithPicDO
        List<SkuAttrMetaDTO> skuAttrMetaDTOList = dealSkuOptionDTO.getSkuAttrMetaList();
        List<SkuSalesAttrWithPicDO> skuSalesAttrWithPicDOlist = new ArrayList<>();
        for (SkuAttrMetaDTO skuAttrMetaDTO : skuAttrMetaDTOList) {
            SkuSalesAttrWithPicDO skuSalesAttrWithPicDO = buildSkuSalesAttrWithPic(skuAttrMetaDTO);
            if (skuSalesAttrWithPicDO == null) {
                return Response.createErrorTipResponse("数据格式错误", null);
            }
            skuSalesAttrWithPicDOlist.add(skuSalesAttrWithPicDO);
        }
        SkuSelectorWithPicVO skuSelectorWithPicVO = new SkuSelectorWithPicVO();
        skuSelectorWithPicVO.setSalesAttrToSkuBasicInfo(salesAttrToSkuBasicInfoDOList);
        skuSelectorWithPicVO.setSkuSalesAttrInfo(skuSalesAttrWithPicDOlist);
        return Response.createSuccessResponse(skuSelectorWithPicVO);
    }

    public SalesAttrToSkuBasicInfoDO buildSalesAttrToSkuBasicInfo(SkuItemDTO skuItemDTO, EnvCtx envCtx) {
        List<DzAttrDo> dzAttrDos = new ArrayList<>();
        if (CollectionUtils.isEmpty(skuItemDTO.getSkuAttrList())) {
            return null;
        }
        for (SkuAttrDTO skuAttrDTO : skuItemDTO.getSkuAttrList()) {
            DzAttrDo dzAttrDo = new DzAttrDo();
            dzAttrDo.setCnName(skuAttrDTO.getAttrCnName());
            dzAttrDo.setName(skuAttrDTO.getAttrName());
            dzAttrDo.setValue(skuAttrDTO.getAttrValueDTO().getCode());
            // 属性id值
            dzAttrDo.setAttrId(skuAttrDTO.getAttrValueDTO().getId());
            dzAttrDos.add(dzAttrDo);
        }

        SkuBasicInfoDO skuBasicInfoDO = new SkuBasicInfoDO();
        skuBasicInfoDO.setSkuId(skuItemDTO.getSkuId());
        skuBasicInfoDO.setStock(envCtx.isMt() ? skuItemDTO.getMtStock() : skuItemDTO.getDpStock());
        skuBasicInfoDO.setSkuHeadPic(skuItemDTO.getSkuHeadPic());
        skuBasicInfoDO.setAttrList(dzAttrDos);

        SalesAttrToSkuBasicInfoDO salesAttrToSkuBasicInfoDO = new SalesAttrToSkuBasicInfoDO();
        salesAttrToSkuBasicInfoDO.setSkuBasicInfo(skuBasicInfoDO);
        salesAttrToSkuBasicInfoDO.setSalesAttrInfo(skuItemDTO.getSkuCode());
        salesAttrToSkuBasicInfoDO.setSalesAttrInfoKey(skuItemDTO.getSalesAttrInfoKey());

        return salesAttrToSkuBasicInfoDO;
    }

    public SkuSalesAttrWithPicDO buildSkuSalesAttrWithPic(SkuAttrMetaDTO skuAttrMetaDTO) {
        List<SkuSalesAttrValueDO> skuSalesAttrValueDOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(skuAttrMetaDTO.getOptionValueList())) {
            return null;
        }
        for (AttrValueDTO attrValueDTO : skuAttrMetaDTO.getOptionValueList()) {
            SkuSalesAttrValueDO skuSalesAttrValueDO = new SkuSalesAttrValueDO();
            skuSalesAttrValueDO.setCode(attrValueDTO.getCode());
            skuSalesAttrValueDO.setDesc(attrValueDTO.getDesc());
            // 属性id值
            skuSalesAttrValueDO.setAttrId(attrValueDTO.getId());
            skuSalesAttrValueDOS.add(skuSalesAttrValueDO);
        }


        SkuSalesAttrWithPicDO skuSalesAttrWithPicDO = new SkuSalesAttrWithPicDO();
        skuSalesAttrWithPicDO.setSkuSalesAttrName(skuAttrMetaDTO.getAttrName());
        skuSalesAttrWithPicDO.setSkuSalesAttrCnName(skuAttrMetaDTO.getAttrCnName());
        skuSalesAttrWithPicDO.setSkuSalesAttrDesc(skuAttrMetaDTO.getAttrDesc());
        skuSalesAttrWithPicDO.setAttrType(skuAttrMetaDTO.getAttrType());
        skuSalesAttrWithPicDO.setSkuSalesAttrValues(skuSalesAttrValueDOS);
        skuSalesAttrWithPicDO.setSkuSalesAttrExt(buildSkuSalesAttrExt(skuAttrMetaDTO.getSkuSalesAttrExt()));
        return skuSalesAttrWithPicDO;
    }

    public SkuSalesAttrExt buildSkuSalesAttrExt(com.sankuai.dztheme.deal.res.SkuSalesAttrExt skuSalesAttrExt) {
        if ( Objects.isNull(skuSalesAttrExt)) {
            return null;
        }
        SkuSalesAttrExt skuSalesAttrExtDO = new SkuSalesAttrExt();
        skuSalesAttrExtDO.setUrl(skuSalesAttrExt.getUrl());
        skuSalesAttrExtDO.setDesc(skuSalesAttrExt.getDesc());
        return skuSalesAttrExtDO;
    }

    public Response<SkuSelectorVO> querySkuSelector(SkuSelectReq request, EnvCtx envCtx) throws TException {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.SkuSelectorFacade.querySkuSelector(SkuSelectReq,EnvCtx)");
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet(Long.parseLong(request.getDealgroupid())), envCtx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP)
                .dealGroupId(DealGroupIdBuilder.builder().all())
                .dealBasicInfo(DealBasicInfoBuilder.builder().status())
                .dealStock(DealStockBuilder.builder().all())
                .attrsByType(AttrSubjectEnum.DEAL, AttrTypeEnum.SALE_PROP)
                .build();

        DealGroupDTO dealGroupDTO = queryCenterWrapper.getDealGroupDTO(queryCenterWrapper.preDealGroupDTO(queryByDealGroupIdRequest));

        List<SalesAttrToSkuBasicInfoDO> salesAttrToSkuBasicInfo = new ArrayList<>();
        Map<String, List<String>> temp = new LinkedHashMap<>();//为了保证顺序
        dealGroupDTO.getDeals()
                .stream().filter(dealGroupDealDTO -> dealGroupDealDTO.getBasic().getStatus() == 1)
                .forEach(dealGroupDealDTO -> {
                    salesAttrToSkuBasicInfo.add(buildSalesAttrToSkuBasicInfoDO(dealGroupDealDTO, envCtx.isMt()));

                    if (CollectionUtils.isNotEmpty(dealGroupDealDTO.getAttrs())) {
                        dealGroupDealDTO.getAttrs().stream()
                                .filter(attrDTO -> attrDTO != null && CollectionUtils.isNotEmpty(attrDTO.getValue()))
                                .forEach(attrDTO -> {
                                    List<String> values = new ArrayList<>();
                                    if (temp.containsKey(attrDTO.getCnName())) {
                                        values = temp.get(attrDTO.getCnName());
                                    } else {
                                        temp.put(attrDTO.getCnName(), values);
                                    }
                                    if (!values.contains(attrDTO.getValue().get(0))) {
                                        values.add(attrDTO.getValue().get(0));
                                    }
                                });
                    }

                });
        List<SkuSalesAttrDO> skuSalesAttrInfo = buildSkuSalesAttrInfo(temp);

        SkuSelectorVO skuSelectorVO = new SkuSelectorVO();
        skuSelectorVO.setSalesAttrToSkuBasicInfo(salesAttrToSkuBasicInfo);
        skuSelectorVO.setSkuSalesAttrInfo(skuSalesAttrInfo);

        return Response.createSuccessResponse(skuSelectorVO);
    }

    private SalesAttrToSkuBasicInfoDO buildSalesAttrToSkuBasicInfoDO(DealGroupDealDTO dealGroupDealDTO, boolean isMt) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.SkuSelectorFacade.buildSalesAttrToSkuBasicInfoDO(com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO,boolean)");
        SalesAttrToSkuBasicInfoDO salesAttrToSkuBasicInfoDO = new SalesAttrToSkuBasicInfoDO();
        SkuBasicInfoDO skuBasicInfo = new SkuBasicInfoDO();
        skuBasicInfo.setSkuId(dealGroupDealDTO.getDealId().toString());
        skuBasicInfo.setStock(isMt ? dealGroupDealDTO.getStock().getMtRemain() : dealGroupDealDTO.getStock().getDpRemain());
        salesAttrToSkuBasicInfoDO.setSkuBasicInfo(skuBasicInfo);

        if (CollectionUtils.isNotEmpty(dealGroupDealDTO.getAttrs())) {
            skuBasicInfo.setAttrList(
                    dealGroupDealDTO.getAttrs().stream()
                            .filter(attrDTO -> attrDTO != null && CollectionUtils.isNotEmpty(attrDTO.getValue()))
                            .map(attrDTO -> {
                                DzAttrDo dzAttrDo = new DzAttrDo();
                                dzAttrDo.setCnName(attrDTO.getCnName());
                                dzAttrDo.setName(attrDTO.getName());
                                dzAttrDo.setValue(attrDTO.getValue().get(0));
                                return dzAttrDo;
                            }).collect(Collectors.toList()));

            salesAttrToSkuBasicInfoDO.setSalesAttrInfo(
                    dealGroupDealDTO.getAttrs().stream()
                            .filter(attrDTO -> attrDTO != null && CollectionUtils.isNotEmpty(attrDTO.getValue()))
                            .map(attrDTO -> attrDTO.getValue().get(0)).collect(Collectors.joining("_")));
        }

        return salesAttrToSkuBasicInfoDO;
    }

    private List<SkuSalesAttrDO> buildSkuSalesAttrInfo(Map<String, List<String>> temp) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.SkuSelectorFacade.buildSkuSalesAttrInfo(java.util.Map)");
        List<SkuSalesAttrDO> skuSalesAttrDOList = new ArrayList<>();
        if (MapUtils.isEmpty(temp)) {
            return skuSalesAttrDOList;
        }
        temp.forEach((key, value) -> {
            SkuSalesAttrDO skuSalesAttrDO = new SkuSalesAttrDO();
            skuSalesAttrDO.setSkuSalesAttrCnName(key);
            skuSalesAttrDO.setSkuSalesAttrValues(value);
            skuSalesAttrDOList.add(skuSalesAttrDO);
        });

        return skuSalesAttrDOList;
    }
}
