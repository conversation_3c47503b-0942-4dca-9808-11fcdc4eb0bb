package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DztgMoreDealModule;

public class MoreDealModuleProcessor extends AbsDealProcessor {

    @Override
    public void prepare(DealCtx ctx) {

    }

    @Override
    public void process(DealCtx ctx) {
        if(ctx.isMt()) {
            DztgMoreDealModule moreDealModule = new DztgMoreDealModule();
            if(ctx.getChannelDTO() != null && ctx.getChannelDTO().getChannelDTO() != null) {
                moreDealModule.setBuCode(ctx.getChannelDTO().getChannelDTO().getChannelId());
            }
            if(ctx.getChannelDTO() != null) {
                moreDealModule.setPublishCategoryId(ctx.getChannelDTO().getCategoryId());
            }
            ctx.setDztgMoreDealModule(moreDealModule);
        }
    }
}
