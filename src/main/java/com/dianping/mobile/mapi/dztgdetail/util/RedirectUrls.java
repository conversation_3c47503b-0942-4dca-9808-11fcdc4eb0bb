package com.dianping.mobile.mapi.dztgdetail.util;


import com.dianping.cat.Cat;
import com.dianping.deal.detail.enums.PlatformEnum;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.feature.model.FeatureDetailDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.LayerConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.LeadsDealBarConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class RedirectUrls {

    /**
     * 适用商户列表
     */
    private static final String MT_APP_SHOP_LIST = "imeituan://www.meituan.com/gc/branchlist?frompage=1&dealid=%s";

    private static final String MT_PREFIX_URL = "imeituan://www.meituan.com/mrn?";
    private static final String DP_PREFIX_URL = "dianping://mrn?";
    private static final String MT_RESV_MIDDLE_URL = "mtShopId=%s&mtDealGroupId=%s&title=%s";
    private static final String DP_RESV_MIDDLE_URL = "dpShopId=%s&dpDealGroupId=%s&title=%s";
    /**
     * 展示场景，2为团详
     */
    private static final int DEAL_DETAIL_SCENE = 2;

    public static String getMtAppShopList(int mtDealId) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.util.RedirectUrls.getMtAppShopList(int)");
        return String.format(MT_APP_SHOP_LIST, mtDealId);
    }

    public static String filterOrderUrlSkuId(DealCtx ctx, String orderUrl) {
        if (StringUtils.isBlank(orderUrl) || Objects.isNull(ctx)) {
            return "";
        }
        if (DealUtils.isNewWearableNailDeal(ctx) && orderUrl.contains("skuid=&")) {
            return orderUrl.replace("skuid=&", "") + "&need_exhibit_skuid=1";
        }
        return orderUrl;
    }

    public static String filterOrderUrlSkuId(DealGroupPBO dealGroupPBO, String orderUrl) {
        if (StringUtils.isBlank(orderUrl) || Objects.isNull(dealGroupPBO)) {
            return "";
        }
        if (DealUtils.isNewWearableNailDeal(dealGroupPBO) && orderUrl.contains("skuid=&")) {
            return orderUrl.replace("skuid=&", "") + "&need_exhibit_skuid=1";
        }
        return orderUrl;
    }

    public static void setPerformanceGuaranteeUrls(DealCtx ctx, EnvCtx envCtx, List<LayerConfig> layerConfigs) {
        if (Objects.isNull(ctx) || Objects.isNull(envCtx) || CollectionUtils.isEmpty(layerConfigs)
                || CollectionUtils.isEmpty(ctx.getShopTagFeatures())) {
            return;
        }
        List<Long> guaranteeEnumIds = ctx.getShopTagFeatures().stream()
                .map(featureDetailDTO -> getGuaranteeJumpId(featureDetailDTO))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        String guaranteeIP = convertList2String(guaranteeEnumIds);
        layerConfigs.forEach(layerConfig -> setPerformanceGuaranteeUrl(ctx, envCtx, layerConfig, guaranteeIP));
    }

    private static void setPerformanceGuaranteeUrl(DealCtx ctx, EnvCtx envCtx, LayerConfig layerConfig, String guaranteeIP) {
        if (Objects.isNull(layerConfig)) {
            return;
        }
        // 拼接基础跳链
        String baseJumpUrl = getBaseJumpUrl(envCtx, layerConfig);
        if (StringUtils.isBlank(baseJumpUrl)) {
            return;
        }
        int platform = envCtx.isMt() ? PlatformEnum.MEI_TUAN.getType() : PlatformEnum.DIAN_PING.getType();
        Long shopId = envCtx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId();
        String urlParam = String.format("displayScene=%s&platform=%s&shopId=%s&guaranteeIp=%s", DEAL_DETAIL_SCENE, platform, shopId, guaranteeIP);
        String jumpUrl = appendParamToUrl(baseJumpUrl, urlParam);
        // 生成跳链
        setJumpUrl(envCtx, layerConfig, jumpUrl);
    }

    private static Long getGuaranteeJumpId(FeatureDetailDTO featureDetailDTO) {
        if (Objects.isNull(featureDetailDTO) || Objects.isNull(featureDetailDTO.getId())) {
            return null;
        }
        return featureDetailDTO.getGuaranteeJumpId();
    }

    private static String getBaseJumpUrl(EnvCtx envCtx, LayerConfig layerConfig) {
        if (envCtx.isWxMini() && StringUtils.isNotBlank(layerConfig.getMiniJumpUrl())) {
            return layerConfig.getMiniJumpUrl();
        }
        if (StringUtils.isBlank(layerConfig.getJumpUrl())) {
            return null;
        }
        // 如果是App端，需要拼接上MRN前缀
        String prefixUrl = envCtx.isMt() ? MT_PREFIX_URL : DP_PREFIX_URL;
        return String.format("%s%s", prefixUrl, layerConfig.getJumpUrl());
    }

    private static void setJumpUrl(EnvCtx envCtx, LayerConfig layerConfig, String jumpUrl) {
        if (envCtx.isWxMini() && StringUtils.isNotBlank(layerConfig.getMiniJumpUrl())) {
            layerConfig.setMiniJumpUrl(jumpUrl);
        } else {
            layerConfig.setJumpUrl(jumpUrl);
        }
    }

    private static String convertList2String(List<?> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return StringUtils.join(list, ",");
    }

    public static String appendParamToUrl(String url, String paramExp) {
        if (StringUtils.isBlank(url) || StringUtils.isBlank(paramExp)) {
            return url;
        }
        StringBuilder sb = new StringBuilder(url);
        if (StringUtils.contains(url, "?")) {
            sb.append("&");
            sb.append(paramExp);
        } else {
            sb.append("?");
            sb.append(paramExp);
        }
        return sb.toString();
    }

    /**
     * 构造留资预约弹窗按钮跳链
     * @param ctx
     * @param leadsDealBarConfig
     * @return
     */
    public static String buildResvPopBtnUrl(DealCtx ctx, LeadsDealBarConfig leadsDealBarConfig) {
        if (Objects.isNull(ctx) || Objects.isNull(leadsDealBarConfig) || Objects.isNull(leadsDealBarConfig.getResvBtnTitle())) {
            return StringUtils.EMPTY;
        }
        try {
            String jumpPrefix = ctx.isMt() ? leadsDealBarConfig.getMtAppJumpPrefix() : leadsDealBarConfig.getDpAppJumpPrefix();
            String middleUrlTemplate = ctx.isMt() ? MT_RESV_MIDDLE_URL : DP_RESV_MIDDLE_URL;
            String title = leadsDealBarConfig.getResvBtnTitle();
            if (ctx.isHitSpecialValueDeal() && StringUtils.isNotBlank(leadsDealBarConfig.getSpecialValueResvPopTitle())) {
                title = leadsDealBarConfig.getSpecialValueResvPopTitle();
            }
            String titleEncode = URLEncoder.encode(title, StandardCharsets.UTF_8.name());
            String middleUrl = String.format(middleUrlTemplate, ctx.getLongPoiId4PFromResp(), ctx.getDealId4P(), titleEncode);

            String resvPrefixUrl = appendParamToUrl(jumpPrefix, leadsDealBarConfig.getResvJumpUrlPrefix());
            String resvUrl = appendParamToUrl(resvPrefixUrl, middleUrl);
            return appendParamToUrl(resvUrl, leadsDealBarConfig.getResvJumpUrlSuffix());
        } catch (Exception e) {
            log.error("RedirectUrls buildResvPopBtnUrl error", e);
        }
        return StringUtils.EMPTY;
    }
}
