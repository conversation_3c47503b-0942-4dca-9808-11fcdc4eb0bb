package com.dianping.mobile.mapi.dztgdetail.util;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class MapComparator {

    /**
     * 深度比较两个 Map 的内容是否一致（忽略指定字段）
     * @param map1 第一个 Map
     * @param map2 第二个 Map
     * @param ignoreKeys 需要忽略的键集合（支持嵌套路径，如 "parent.child.key"）
     * @return 如果所有非忽略键值对深度相等则返回 true，否则返回 false
     */
    public static boolean deepCompare(Map<?, ?> map1, Map<?, ?> map2, Set<String> ignoreKeys, String event, String field, boolean logDiff) {
        // 空忽略集合处理
        if (ignoreKeys == null) ignoreKeys = Collections.emptySet();

        // 1. 检查是否为同一对象或均为 null
        if (map1 == map2) return true;
        if (map1 == null || map2 == null) return false;

        // 2. 检查键集合是否一致（排除忽略键）
        for(String ignoreKey : ignoreKeys) {
            map1.remove(ignoreKey);
            map2.remove(ignoreKey);
        }
        return deepCompare(map1, map2, event, field, logDiff);
    }

    /**
     * 深度比较两个 Map 的内容是否一致
     * @param map1 第一个 Map
     * @param map2 第二个 Map
     * @return 如果所有键值对深度相等则返回 true，否则返回 false
     */
    public static boolean deepCompare(Map<?, ?> map1, Map<?, ?> map2, String event, String field, boolean logDiff) {
        // 1. 检查是否为同一对象或均为 null
        if (map1 == map2) return true;
        if (map1 == null || map2 == null) return false;

        // 2. 检查键集合是否一致
        if (!map1.keySet().equals(map2.keySet())) return false;

        // 3. 遍历所有键，递归比较值
        for (Object key : map1.keySet()) {
            Object value1 = map1.get(key);
            Object value2 = map2.get(key);

            if (!deepEquals(value1, value2)) {
                if (logDiff) {
                    Cat.logEvent(event, String.format("%s_%s ", field,"Url_diff_part"));
                    log.warn("diff mess:{} diff_part:{}, oldValue:{} newValue:{}", String.format("%s_%s ", field,"Url_diff_part"), key, JSON.toJSONString(map1), JSON.toJSONString(map2));
                }
                return false;
            }
        }

        return true;
    }

    /**
     * 递归深度比较两个对象
     */
    private static boolean deepEquals(Object obj1, Object obj2) {
        // 处理 null 值
        if (obj1 == null && obj2 == null) return true;
        if (obj1 == null || obj2 == null) return false;

        // 相同对象快速返回
        if (obj1 == obj2) return true;

        // 处理 Map 类型
        if (obj1 instanceof Map && obj2 instanceof Map) {
            return deepCompare((Map<?, ?>) obj1, (Map<?, ?>) obj2, null, null, false);
        }

        // 处理 List/Set 集合（顺序敏感）
        if (obj1 instanceof Collection && obj2 instanceof Collection) {
            Collection<?> col1 = (Collection<?>) obj1;
            Collection<?> col2 = (Collection<?>) obj2;

            // 转换为 List 进行顺序敏感比较
            return deepEquals(
                col1.stream().collect(Collectors.toList()),
                col2.stream().collect(Collectors.toList())
            );
        }

        // 处理数组
        if (obj1.getClass().isArray() && obj2.getClass().isArray()) {
            return arrayDeepEquals(obj1, obj2);
        }

        // 默认使用 equals 比较
        return Objects.deepEquals(obj1, obj2);
    }

    /**
     * 深度比较数组内容
     */
    private static boolean arrayDeepEquals(Object arr1, Object arr2) {
        // 基本类型数组处理
        if (arr1 instanceof byte[] && arr2 instanceof byte[]) {
            return Arrays.equals((byte[]) arr1, (byte[]) arr2);
        } else if (arr1 instanceof short[] && arr2 instanceof short[]) {
            return Arrays.equals((short[]) arr1, (short[]) arr2);
        } else if (arr1 instanceof int[] && arr2 instanceof int[]) {
            return Arrays.equals((int[]) arr1, (int[]) arr2);
        } else if (arr1 instanceof long[] && arr2 instanceof long[]) {
            return Arrays.equals((long[]) arr1, (long[]) arr2);
        } else if (arr1 instanceof float[] && arr2 instanceof float[]) {
            return Arrays.equals((float[]) arr1, (float[]) arr2);
        } else if (arr1 instanceof double[] && arr2 instanceof double[]) {
            return Arrays.equals((double[]) arr1, (double[]) arr2);
        } else if (arr1 instanceof char[] && arr2 instanceof char[]) {
            return Arrays.equals((char[]) arr1, (char[]) arr2);
        } else if (arr1 instanceof boolean[] && arr2 instanceof boolean[]) {
            return Arrays.equals((boolean[]) arr1, (boolean[]) arr2);
        } else {
            // 对象数组使用 deepEquals
            return Arrays.deepEquals((Object[]) arr1, (Object[]) arr2);
        }
    }

}
