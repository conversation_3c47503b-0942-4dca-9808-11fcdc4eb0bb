package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.gmkt.activity.api.dto.BatchQueryDealActivityRequest;
import com.dianping.gmkt.activity.api.dto.DealActivityDTO;
import com.dianping.gmkt.activity.api.dto.Version;
import com.dianping.gmkt.activity.api.enums.ExposeChannel;
import com.dianping.gmkt.activity.api.enums.ExposureDateKeyEnum;
import com.dianping.gmkt.activity.api.enums.PriceStrengthTimeEnum;
import com.dianping.gmkt.activity.api.enums.RequestSource;
import com.dianping.gmkt.activity.enums.AppPlatform;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.CreateOrderPageUrlBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealActivityWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.button.joy.PopOverlayButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.SaleStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ShoppingCartStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PurchaseMessageRemindInfo;
import com.dianping.mobile.mapi.dztgdetail.entity.DealRepurchaseConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.PurchaseMessageConfig;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.utils.FaultToleranceUtils;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.NewBuyBarHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.PromoHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper;
import com.dianping.mobile.mapi.dztgdetail.util.*;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.CouponAssignStatusEnum;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.rule.buy.DealGroupBuyRuleDTO;
import com.sankuai.mlive.goods.trade.api.model.GoodsSellingInfoDTO;
import com.sankuai.swan.udqs.api.QueryData;
import com.sankuai.swan.udqs.api.Result;
import com.sankuai.swan.udqs.api.SwanParam;
import com.sankuai.swan.udqs.api.SwanQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Future;

import static com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum.NORMAL_DEAL;
import static com.dianping.mobile.mapi.dztgdetail.common.enums.StyleTypeEnum.SHOPPING_CART;
import static com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper.buildPromotionChannel;

/**
 * @Author: <EMAIL>
 * @Date: 2024/6/9
 */
@Component
@Slf4j
public class BuyBarBuilderService {
    private static final String SWAN_QUERY_BIZ_KEY_BY_DATE = "batch_query_for_productprice_tjck_by_date";
    private static final String SWAN_QUERY_BIZ_KEY = "batch_query_for_productprice_tjck";
    private static final String SWAN_QUERY_BIZ_KEY_USER_PURCHASE_INFO = "dpapp_trade_user_purchase_product_info_d";
    private static final Integer SWAN_QUERY_BIZ_TYPE_ID = 1011;
    private static final Integer SWAN_QUERY_BIZ_TYPE_ID_1096 = 1096;
    private static final String RESULT_MAP_KEY_PRICE_30D = "minSalesPrice30d";
    private static final String RESULT_MAP_KEY_PRICE_60D = "minSalesPrice60d";
    private static final String RESULT_MAP_KEY_PRICE_90D = "minSalesPrice90d";
    public static final int LIMIT_ONE = 1;

    @Autowired
    private DealActivityWrapper dealActivityWrapper;
    @Resource
    private SwanQueryService swanQueryService;
    @Autowired
    private DouHuBiz douHuBiz;

    public void buildBuyBar(DealCtx ctx, DealGroupPBO result) {
        DealBuyBar buildBuyBar = NewBuyBarHelper.build(ctx);
        // 直播间、非直播间渠道进入直播渠道品团详页处理，有开关
        buildBuyBarByMLiveInfo(ctx,  buildBuyBar);
        // 足疗洗浴覆盖URL，有开关
        massageAndBathOverrideUrl(ctx, buildBuyBar, result);
        // 可复购货架治理
        buildBuyBarByPurchaseLimit(ctx, buildBuyBar, result);
        // 特团拼团底bar链接替换
        if (CollectionUtils.isNotEmpty(buildBuyBar.getBuyBtns())) {
            CostEffectivePinTuanUtils.updateDirectBuyBarForPinTuan(buildBuyBar.getBuyBtns());
        }
        if(ReassuredRepairUtil.isTagPresent(ctx) && CollectionUtils.isNotEmpty(buildBuyBar.getBuyBtns())){
            buildBuyBar.getBuyBtns().get(0).setPriceStr("");
        }
        if (buildBuyBar == null || ctx == null
                || !isInWhite(ctx.getCategoryId())
                || CollectionUtils.isEmpty(buildBuyBar.getBuyBtns())
                || buildBuyBar.getStyleType() == SHOPPING_CART.code) {
            result.setBuyBar(buildBuyBar);
            return;
        }

        //0元预约定制化逻辑
        if (ctx.isFreeDeal()){
            result.setBuyBar(buildBuyBar);
            return;
        }

        try {
            DealBuyBtn dealBuyBtn = buildBuyBar.getBuyBtns().get(buildBuyBar.getBuyBtns().size() - 1);
            BigDecimal markPrice = ctx.getDealGroupBase().getMarketPrice();
            dealBuyBtn.setMarketPrice(NumberFormat.getInstance().format(markPrice));
            Future activityFuture = dealActivityWrapper.prepareDealActivity(buildBatchQueryDealActivityReq(ctx));
            List<DealActivityDTO> dealActivityDTOS = dealActivityWrapper.queryDealActivity(activityFuture, ctx);
            if (CollectionUtils.isEmpty(dealActivityDTOS)) {
                result.setBuyBar(buildBuyBar);
                return;
            }
            DealActivityDTO selectedDealActivity = dealActivityDTOS.get(0);
            if (selectedDealActivity == null || selectedDealActivity.getPriceStrengthTime() == 0) {
                result.setBuyBar(buildBuyBar);
                return;
            }
            PriceStrengthTimeEnum priceDay = PriceStrengthTimeEnum.findByCode(selectedDealActivity.getPriceStrengthTime());
            Date priceAbilityDate = selectedDealActivity.getDateMap() == null ? null : selectedDealActivity.getDateMap().get(ExposureDateKeyEnum.PRICE_COMPARE_DATE.getKey());
            BigDecimal minPrice = queryPriceAbility(ctx.getDpId(), priceDay, priceAbilityDate);
            if (minPrice == null) {
                result.setBuyBar(buildBuyBar);
                return;
            }
            dealBuyBtn.setMinPrice(NumberFormat.getInstance().format(minPrice));
            if (minPrice.compareTo(new BigDecimal(dealBuyBtn.getPriceStr())) > 0) {
                result.setMinPriceDesc(String.format("近%s低价", priceDay.getDesc()));
                dealBuyBtn.setMinPriceDesc(String.format("近%s低价", priceDay.getDesc()));
            }
        } catch (Exception e) {
            log.error("buildMinPrice", e);
        }

        result.setBuyBar(buildBuyBar);
    }


    /**
     * 直播间、非直播间渠道进入直播渠道品团详页处理-购买按钮、加入购物车、购物车浮标处理
     * 有Lion开关
     * @param ctx
     * @param buyBar
     */
    public void buildBuyBarByMLiveInfo(DealCtx ctx, DealBuyBar buyBar) {
        if (!LionConfigUtils.getmliveSwitch()){
            return;
        }
        // 私域直播间有单独的算价逻辑
        if (ctx.isMtLiveMinApp()) {
            return;
        }
        if (Objects.isNull(buyBar)
                || CollectionUtils.isEmpty(buyBar.getBuyBtns())
                || Objects.isNull(buyBar.getBuyBtns())){
            return;
        }
        List<DealBuyBtn> buyBtns = buyBar.getBuyBtns();
        /**
         * 直播间:
         * 1.用户从直播间进入团详页：此时各底Bar类型均不会出现购物车场景
         * 2.商品状态=未开售时，“马上抢”/“立即抢购”按钮置灰并修改文案为“待开抢”
         */
        if (RequestSourceEnum.LIVE_STREAM.getSource().equals(ctx.getRequestSource()) || ctx.getMLiveId() > 0){
            // 直播间场景 隐藏 购物车浮标
            buyBar.setStyleType(0);
            buyBtns.forEach(e->{
                // 立即抢购按钮
                //商品状态=未开售时，“马上抢”/“立即抢购”按钮置灰并修改文案为“待开抢”
                if (notMeetSellingTime(ctx.getDealGroupBase())
                        || ( !allowSelling(ctx.getMliveSellingInfo()) && NORMAL_DEAL.getCode() == e.getDetailBuyType()) ){
                    e.setBtnTitle("待开抢");
                    e.setBtnEnable(Boolean.FALSE);
                    e.setAddShoppingCartStatus(ShoppingCartStatusEnum.NONE.code);
                }
            });
        }else {
            //  非直播间: 商品类型为 仅直播渠道品
            if (ctx.getMLiveChannel()){
                buyBtns.forEach(e->{
                    if (NORMAL_DEAL.getCode() == e.getDetailBuyType()){
                        //  1.隐藏“加入购物车”按钮及购物车浮标
                        //  2.“立即抢购”按钮置灰
                        //  3.修改按钮文案为“直播专享”
                        e.setBtnTitle("直播专享");
                        e.setBtnEnable(Boolean.FALSE);
                        e.setAddShoppingCartStatus(ShoppingCartStatusEnum.NONE.code);
                        buyBar.setStyleType(0);
                    }
                });
            } else if (Objects.nonNull(ctx.getDealGroupBase())
                    && ctx.getDealGroupBase().getBeginDate() != null
                    && ctx.getDealGroupBase().getBeginDate().compareTo(new Date()) > 0) {
                //  非直播间 商品状态=未开售
                buyBtns.forEach(e->{
                    if (NORMAL_DEAL.getCode() == e.getDetailBuyType()) {
                        // 1.隐藏“加入购物车”按钮及购物车浮标，
                        // 2.“马上抢”/“立即抢购”按钮置灰
                        // 3.修改按钮文案为“待开抢”
                        e.setBtnTitle("待开抢");
                        e.setBtnEnable(Boolean.FALSE);
                        e.setAddShoppingCartStatus(ShoppingCartStatusEnum.NONE.code);
                        buyBar.setStyleType(0);
                    }
                });

            }
        }
    }


    /**
     * 足疗、洗浴，交易跳链覆盖，有lion开关
     *
     * @param ctx
     * @param buildBuyBar
     * @param result
     */
    public void massageAndBathOverrideUrl(DealCtx ctx, DealBuyBar buildBuyBar, DealGroupPBO result) {
        if(PopOverlayButtonBuilder.getPopOverlaySwitch()){
            return;
        }
        int clientType = ctx.getEnvCtx().getClientType();
        boolean isApp = ClientTypeEnum.isMtMainApp(clientType) || ClientTypeEnum.isDpMainApp(clientType);
        // 替换URL（限制 足疗商品303、洗浴商品304 类目、限制 点评app、美团app）
        // lion 开关，可以关闭跳链复制逻辑
        if (Objects.nonNull(ctx) && (ctx.getCategoryId() == 303 || ctx.getCategoryId() == 304) && CollectionUtils.isNotEmpty(buildBuyBar.getBuyBtns()) && isApp && Lion.getBoolean("com.sankuai.dzu.tpbase.dztgdetailweb", "com.sankuai.dzu.tpbase.dztgdetailweb.massageAndBathOrderUrlSwitch", false) && Objects.nonNull(result) && Objects.nonNull(result.getSkuModule()) && StringUtils.isNotBlank(result.getSkuModule().getUrl())) {
            // 只修改最后一个团购类型的按钮
            DealBuyBtn onlyDealBuyBtn = null;
            for (DealBuyBtn dealBuyBtn : buildBuyBar.getBuyBtns()) {
                if (Objects.nonNull(dealBuyBtn) && dealBuyBtn.getDetailBuyType() == NORMAL_DEAL.getCode()) {
                    onlyDealBuyBtn = dealBuyBtn;
                }
            }
            if (Objects.isNull(onlyDealBuyBtn)) {
                return;
            }
            // 填充 promotionChannel 字段
            onlyDealBuyBtn.setRedirectUrl(result.getSkuModule().getUrl());
            putPromotionChannelParamIfNeeded(ctx, onlyDealBuyBtn);
            // 填充 券信息，以自动领券
            putOtherPromotionChannel(ctx, onlyDealBuyBtn);
            // 填充价惠密文
            onlyDealBuyBtn.setRedirectUrl(UrlHelper.addPriceCipher(ctx, onlyDealBuyBtn.getRedirectUrl()));
        }
    }

    public void buildBuyBarByPurchaseLimit(DealCtx ctx, DealBuyBar buyBar, DealGroupPBO result) {
        // 丽人三美 美容美发美甲 501=美发 502=美甲/美睫 503=美容 且城市为 成都、武汉
        // 判断城市和行业
        if(!enableRepurchase(ctx) ){
            return;
        }
        //  实验 mt：exp00327  dp：exp003275
        //  a：目前线上规则
        //  b：①团详置灰 ②不做相似品类推荐
        //  c：①团详置灰 ②做相似品类推荐
        String expResult = getExpResult(ctx);
        if ("a".equals(expResult)){
            return;
        }
        if (Objects.isNull(buyBar)
                || CollectionUtils.isEmpty(buyBar.getBuyBtns())
                || Objects.isNull(buyBar.getBuyBtns())){
            return;
        }
        List<DealBuyBtn> buyBtns = buyBar.getBuyBtns();
        boolean needPreOrder = DealCtxHelper.isPreOrderDeal(ctx);
        // 如果是限制购买次数的团单，且 用户下单未退款的次数 超过限制次数
        if (userOrderCountOverPurchaseLimit(ctx)){
            buyBtns.forEach(e->{
                if (NORMAL_DEAL.getCode() == e.getDetailBuyType()){
                    //  1.“立即抢购”按钮置灰
                    //  2.修改按钮文案为“已达购买次数上限”
                    e.setBtnTitle(needPreOrder ? "已达预订次数上限" : "已达购买次数上限");
                    e.setBtnEnable(Boolean.FALSE);
                    e.setSaleStatus(SaleStatusEnum.PURCHASE_LIMIT.saleStatusName);
                    e.setPurchaseMessage(buildPurchaseMessage());
                    e.setPriceStr(null);
                }
            });
        }
    }

    public boolean isInWhite(int category) {
        List<Integer> categoryIds =  LionConfigUtils.getDealBtnPriceStrengthTagCategoryIds();
        return categoryIds.contains(category);
    }

    public BigDecimal queryPriceAbility(int dealGroupId, PriceStrengthTimeEnum priceStrengthTime, Date priceAbilityDate) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.queryPriceAbility(int,com.dianping.gmkt.activity.api.enums.PriceStrengthTimeEnum,java.util.Date)");
        try {
            SwanParam swanParam = new SwanParam();
            Map<String, Object> map = new HashMap<>();
            map.put("productId", dealGroupId);
            map.put("productType", "团购");
            String bizKey = SWAN_QUERY_BIZ_KEY;
            if (priceAbilityDate != null) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String priceAbilityDateStr = dateFormat.format(priceAbilityDate);
                map.put("queryDate", priceAbilityDateStr);
                bizKey = SWAN_QUERY_BIZ_KEY_BY_DATE;
            }
            swanParam.setRequestParams(com.google.common.collect.Lists.newArrayList(map));
            Result<QueryData> result = swanQueryService.queryByKey(SWAN_QUERY_BIZ_TYPE_ID, bizKey, swanParam);
            if (result == null || !result.isIfSuccess() || result.getData() == null
                    || CollectionUtils.isEmpty(result.getData().getResultSet())) {
                return null;
            }
            Double minPrice = null;
            for (Map<String, Object> objectMap : result.getData().getResultSet()) {
                if (priceStrengthTime == PriceStrengthTimeEnum.THIRTY_DAY) {
                    minPrice = objectMap.get(RESULT_MAP_KEY_PRICE_30D) == null ? null : (Double) objectMap.get(RESULT_MAP_KEY_PRICE_30D);
                } else if (priceStrengthTime == PriceStrengthTimeEnum.SIXTY_DAY) {
                    minPrice = objectMap.get(RESULT_MAP_KEY_PRICE_60D) == null ? null : (Double) objectMap.get(RESULT_MAP_KEY_PRICE_60D);
                } else if (priceStrengthTime == PriceStrengthTimeEnum.NINETY_DAY) {
                    minPrice = objectMap.get(RESULT_MAP_KEY_PRICE_90D) == null ? null : (Double) objectMap.get(RESULT_MAP_KEY_PRICE_90D);
                }
            }
            return BigDecimal.valueOf(minPrice).setScale(2, BigDecimal.ROUND_HALF_UP);
        } catch (Exception e) {
            log.error("queryPriceAbility", e);
            FaultToleranceUtils.addException("queryByKey", e);
        }
        return null;
    }

    private BatchQueryDealActivityRequest buildBatchQueryDealActivityReq(DealCtx ctx) {
        BatchQueryDealActivityRequest request = new BatchQueryDealActivityRequest();
        if (ctx.isMt()) {
            request.setMtDealIds(com.google.common.collect.Lists.newArrayList(ctx.getMtId()));
            request.setMtCity(ctx.getMtCityId());
            request.setUserIdL(ctx.getEnvCtx().getMtUserId());//已确认判断平台后再使用
            request.setAppPlatform(AppPlatform.MT);
        } else {
            request.setDpDealIds(com.google.common.collect.Lists.newArrayList(ctx.getDpId()));
            request.setDpCity(ctx.getDpCityId());
            request.setUserIdL(ctx.getEnvCtx().getDpUserId());
            request.setAppPlatform(AppPlatform.DP);
        }
        request.setSource(RequestSource.TuanDetail);
        request.setVersion(new Version(ctx.getEnvCtx().getVersion()));
        request.setChannel(ExposeChannel.App.code);
        return request;
    }


    /**
     * 判断是否达到售卖时间
     * @param dealGroupBase
     * @return
     */
    public Boolean notMeetSellingTime(DealGroupBaseDTO dealGroupBase){
        return  Objects.nonNull(dealGroupBase)
                && dealGroupBase.getBeginDate() != null
                && dealGroupBase.getBeginDate().compareTo(new Date()) > 0;
    }


    /**
     * 判断是否可售
     * @param sellingInfo
     * @return
     */
    public Boolean allowSelling(GoodsSellingInfoDTO sellingInfo){
        if (Objects.isNull(sellingInfo) ){
            return Boolean.TRUE;
        }else {
            return sellingInfo.isAllowSelling();
        }

    }


    /**
     * 填充 promotionChannel 字段
     *
     * @param ctx
     * @param onlyDealBuyBtn
     * @return
     */
    public void putPromotionChannelParamIfNeeded(DealCtx ctx, DealBuyBtn onlyDealBuyBtn) {
        if (StringUtils.isEmpty(onlyDealBuyBtn.getRedirectUrl())) {
            return;
        }
        Map<String, String> pageSource2OrderPromotionChannel = Lion.getMap(LionConstants.APP_KEY, LionConstants.PAGESOURCE_TO_ORDER_PROMOTIONChANNEL, String.class, Collections.emptyMap());
        String promotionChannel = buildPromotionChannel(ctx, pageSource2OrderPromotionChannel);
        // 渠道专属立减 提单页链接增加参数
        if (ctx.getPriceContext().isHasExclusiveDeduction()) {
            if (StringUtils.isBlank(promotionChannel)) {
                promotionChannel = "1";
            }
            onlyDealBuyBtn.setRedirectUrl(onlyDealBuyBtn.getRedirectUrl() + "&promotionchannel=" + promotionChannel);
        } else if (StringUtils.isNotBlank(promotionChannel)) {
            onlyDealBuyBtn.setRedirectUrl(onlyDealBuyBtn.getRedirectUrl() + "&promotionchannel=" + promotionChannel);
        }
    }

    private void putOtherPromotionChannel(DealCtx context, DealBuyBtn onlyDealBuyBtn) {
        List<Map<String, Object>> tradeNeedMapList = new ArrayList<>();
        handleGovernmentConsumeCouponPromo(context, tradeNeedMapList);
        handleCouponPurchaseCouponPromos(context, tradeNeedMapList);
        if (CollectionUtils.isEmpty(tradeNeedMapList)) {
            return;
        }
        String appendUrlArg = "&otherpromptinfo=" + GsonUtils.toJsonString(tradeNeedMapList);
        if (CreateOrderPageUrlBiz.isNotSupportBtnType(onlyDealBuyBtn)) {
            return;
        }
        if (StringUtils.isNotBlank(onlyDealBuyBtn.getRedirectUrl())) {
            onlyDealBuyBtn.setRedirectUrl(onlyDealBuyBtn.getRedirectUrl() + appendUrlArg);
        }
    }


    /**
     * 复购货架 配置信息
     * enableSwitch：总开关
     * allPass：全行业放量开关，默认false
     * categoryIds：适用行业的类目id, 如果为空则区分行业
     * mtCityIds ：适用美团的城市id，如果为空则不区分城市
     * dpCityIds：适用的点评城市id，如果为空则去区分城市
     * 优先级：
     * enableSwitch > allPass > categoryIds &&（mtCityIds || dpCityIds）> （mtCityIds || dpCityIds）|| categoryIds
     *
     * @param ctx
     * @return
     */
    private boolean enableRepurchase(DealCtx ctx){
        return LionConfigUtils.enableRepurchase(ctx.getCategoryId(), ctx.isMt(), ctx.isMt()?ctx.getMtCityId():ctx.getDpCityId());
    }

    private boolean enableRepurchase(int categoryId){
        DealRepurchaseConfig repurchaseConfig = LionConfigUtils.getRepurchaseConfig();
        if (Objects.nonNull(repurchaseConfig) && CollectionUtils.isNotEmpty(repurchaseConfig.getCategoryIds())){
            List<Integer> categoryIds = repurchaseConfig.getCategoryIds();
            return categoryIds.contains(categoryId);
        }
        return Boolean.FALSE;
    }

    private String getExpResult(DealCtx ctx) {
        ModuleAbConfig abConfig = getRepurchaseShelfAbConfig(ctx);
        return douHuBiz.getExpResult(abConfig);
    }

    /**
     * 判断是否超出购买限制
     * @param ctx
     * @return
     */
    public boolean userOrderCountOverPurchaseLimit(DealCtx ctx){
        if (!meetAppVersion(ctx)){
            // 没有达到app版本号（美团>12.20.400，点评>11.17.0）
            return Boolean.FALSE;
        }
        DealGroupDTO dealGroup = ctx.getDealGroupDTO();
        if (Objects.nonNull(dealGroup) && Objects.nonNull(dealGroup.getRule()) && Objects.nonNull(dealGroup.getRule().getBuyRule())){
            DealGroupBuyRuleDTO buyRuleDTO = dealGroup.getRule().getBuyRule();
            // swan的userordercount = maxPerUser
            return isPurchaseLimit(buyRuleDTO) && (getUserOrderCount(ctx) >= buyRuleDTO.getMaxPerUser());
        }
        return Boolean.FALSE;
    }

    public PurchaseMessageRemindInfo buildPurchaseMessage(){
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.buildPurchaseMessage()");
        Map<String, PurchaseMessageConfig> messageConfigMap = LionConfigUtils.getPurchaseBtnMessageConfig();
        PurchaseMessageConfig messageConfig = messageConfigMap.get("purchaseMessage");
        PurchaseMessageRemindInfo purchaseMessage;
        if (Objects.nonNull(messageConfig)){
            purchaseMessage = buildPurchaseMessageRemindInfo(messageConfig.getType(),
                    messageConfig.getTitle(),
                    messageConfig.getContent(),
                    messageConfig.getConfirmBtnTxt());
        }else {
            purchaseMessage = buildPurchaseMessageRemindInfo("dialog",
                    "已达购买次数上限",
                    "您的历史购买次数已达该商品限制购买次数，可重新选购相似商品",
                    "我知道了");
        }
        return purchaseMessage;
    }

    private void handleGovernmentConsumeCouponPromo(DealCtx context, List<Map<String, Object>> tradeNeedMapList) {
        PromoDTO governmentConsumeCouponPromoDTO = Optional.ofNullable(PromoHelper.getGovernmentConsumeCoupon(context))
                .filter(CollectionUtils::isNotEmpty).orElse(new ArrayList<>()).stream().findFirst().orElse(null);
        if (governmentConsumeCouponPromoDTO == null) {
            return;
        }
        Map<String, Object> map = getTradeNeedMap(governmentConsumeCouponPromoDTO, 1);
        if (org.apache.commons.collections4.MapUtils.isNotEmpty(map)) {
            tradeNeedMapList.add(map);
        }
    }

    private void handleCouponPurchaseCouponPromos(DealCtx context, List<Map<String, Object>> tradeNeedMapList) {
        PromoDTO couponPurchasePromoDTO = Optional.ofNullable(PromoHelper.getCouponPurchase(context))
                .filter(CollectionUtils::isNotEmpty).orElse(new ArrayList<>()).stream().findFirst().orElse(null);
        if (couponPurchasePromoDTO == null) {
            return;
        }
        Map<String, Object> map = getTradeNeedMap(couponPurchasePromoDTO, 2);
        if (org.apache.commons.collections4.MapUtils.isNotEmpty(map)) {
            tradeNeedMapList.add(map);
        }
    }

    private ModuleAbConfig getRepurchaseShelfAbConfig(DealCtx ctx) {
        if (!enableRepurchase(ctx.getCategoryId())) {
            return null;
        }
        // 综团比价AB实验上报
        String module = ctx.isMt() ? "MtRepurchaseShelfExp" : "DpRepurchaseShelfExp";;
        if (ctx.isMt()){
            return douHuBiz.getAbByUuId(ctx.getEnvCtx().getUuid(), module, ctx.isMt());
        }else {
            return douHuBiz.getAbExpResultByUuidAndDpid(ctx, module);
        }
    }

    private Map<String, Object> getTradeNeedMap(PromoDTO item, int sceneType) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.getTradeNeedMap(com.sankuai.dealuser.price.display.api.model.PromoDTO,int)");
        Map<String, Object> map = new HashMap<>();
        map.put("receiptBatchId", item.getCouponGroupId());
        if (item.getCouponAssignStatus().equals(CouponAssignStatusEnum.ASSIGNED.getCode())) {
            map.put("receiptCode", item.getIdentity().getPromoId());
        }
        map.put("sceneType", sceneType);
        return map;
    }

    public PurchaseMessageRemindInfo buildPurchaseMessageRemindInfo(String type, String title, String content, String confirmBtnTxt){
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.buildPurchaseMessageRemindInfo(java.lang.String,java.lang.String,java.lang.String,java.lang.String)");
        PurchaseMessageRemindInfo remindInfo = new PurchaseMessageRemindInfo();
        remindInfo.setType(type);
        remindInfo.setTitle(title);
        remindInfo.setContent(content);
        remindInfo.setConfirmBtnTxt(confirmBtnTxt);
        return remindInfo;
    }


    /**
     * 判断是否达到 对应的app版本号
     * @param ctx
     * @return
     */
    public boolean meetAppVersion(DealCtx ctx){
        if (ctx.isMt()){
            // 美团侧
            if (VersionUtils.isGreatEqualThan(ctx.getEnvCtx().getVersion(), "12.20.400")){
                return Boolean.TRUE;
            }
        }else {
            // 点评侧
            if (VersionUtils.isGreatEqualThan(ctx.getEnvCtx().getVersion(), "11.17.0")){
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }


    /**
     * 判断是否限制购买次数
     * @param buyRuleDTO
     * @return
     */
    public boolean isPurchaseLimit(DealGroupBuyRuleDTO buyRuleDTO){
        if (buyRuleDTO.getMaxPerUser() >= LIMIT_ONE){
            // 限制购买次数
            return Boolean.TRUE;
        }
        // 不限制次数
        return Boolean.FALSE;
    }

    public int getUserOrderCount(DealCtx ctx){
        SwanParam swanParam = new SwanParam();
        Map<String, Object> map = new HashMap<>();
        map.put("platform", ctx.isMt() ? 2 : 1);
        map.put("dealgroupid", ctx.isMt() ? ctx.getMtId(): ctx.getDpId());
        map.put("userid", ctx.isMt() ? ctx.getEnvCtx().getMtUserId(): ctx.getEnvCtx().getDpUserId());//已确认判断平台后再使用
        String bizKey = SWAN_QUERY_BIZ_KEY_USER_PURCHASE_INFO;
        swanParam.setRequestParams(Lists.newArrayList(map));
        Result<QueryData> result = swanQueryService.queryByKey(SWAN_QUERY_BIZ_TYPE_ID_1096, bizKey, swanParam);
        return getUserOrderCountFromResult(result);
    }

    public int getUserOrderCountFromResult(Result<QueryData> result){
        if (result == null || !result.isIfSuccess() || result.getData() == null
                || CollectionUtils.isEmpty(result.getData().getResultSet())) {
            return 0;
        }
        Map<String, Object> resultMap = result.getData().getResultSet().get(0);
        String valueStr = (String) resultMap.get("value");
        if (StringUtils.isBlank(valueStr) || Objects.isNull(JSONObject.parseObject(valueStr).get("userordercount"))){
            return 0;
        }
        String userOrderCountStr = (String) JSONObject.parseObject(valueStr).get("userordercount");
        if (!StringUtils.isNumeric(userOrderCountStr)){
            return 0;
        }
        int userOrderCount =  Integer.parseInt(userOrderCountStr);
        return userOrderCount;
    }
}
