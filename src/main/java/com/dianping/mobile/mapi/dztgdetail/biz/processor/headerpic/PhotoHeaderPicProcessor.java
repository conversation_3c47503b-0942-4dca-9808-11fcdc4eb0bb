package com.dianping.mobile.mapi.dztgdetail.biz.processor.headerpic;

import com.dianping.mobile.mapi.dztgdetail.common.enums.ImageScaleEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.ExhibitContentDTO;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023-08-30-15:56
 */
//用于摄影行业实现
@Component("photoHeaderPicProcessor")
@Slf4j
public class PhotoHeaderPicProcessor extends AbstractHeaderPicProcessor {
    @Override
    public void fillPicScale(DealCtx ctx, List<ContentPBO> result, DealGroupPBO dealGroupPBO) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        // 如果团单不是指定品类，则直接返回
        if (!LionConfigUtils.hasCustomHeaderPic("photo", ctx.getCategoryId())) {
            return;
        }
        // 判定是否有款式
        boolean showExhibit = matchShowExhibit(ctx);

        // 不满足有款式条件时，下发团购头图信息即可
        if (!showExhibit) {
            result.forEach(contentPBO -> {
                contentPBO.setScale(ImageScaleEnum.SIXTEEN_TO_NINE.getScale());
            });
            dealGroupPBO.setExhibitContents(null);
            return;
        }
        // 有款式
        result.forEach(contentPBO -> {
            contentPBO.setScale(ImageScaleEnum.THREE_TO_FOUR.getScale());
        });
        // 获取头图&款式展示规则配置
        assembleHeaderPicAndExhibitInfo(ctx, dealGroupPBO);
        return;
    }

    @Override
    public boolean matchShowExhibit(DealCtx ctx) {
        // 1. 判断团购有无关联案例, 确认数据源，以及个性写真和孕婴童摄影是否有区别
        // 方案1：从attr里拿contentRelation解析，需要在lion配置里加上这个key
        // 方案2：从案例提供的新接口获取，需要先填充到ctx中，最好用这个，保持一致
        int categoryId = ctx.getCategoryId();
        boolean showExhibit = false;
        ExhibitContentDTO exhibitContentDTO = ctx.getExhibitContentDTO();
        int recordCount = Optional.ofNullable(exhibitContentDTO).map(ExhibitContentDTO::getRecordCount).orElse(0);
        if (recordCount > 0) {
            showExhibit = true;
        }
        return showExhibit;
    }

}
