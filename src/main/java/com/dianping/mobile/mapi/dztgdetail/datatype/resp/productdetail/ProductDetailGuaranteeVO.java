package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.FeaturesLayer;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.FloatingLayer.enums.FloatingLayerOpenTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.Guarantee.CommonGuaranteeInstructionsBarVO;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-11
 * @desc 商品详情页保障模块
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ProductDetailGuaranteeVO extends CommonGuaranteeInstructionsBarVO {

    @FieldDoc(description = "浮层打开类型，1-打开浮层，2-跳转到详情页")
    @MobileDo.MobileField(key = 0x7498)
    private int floatingLayerOpenType;

    @FieldDoc(description = "浮层信息")
    @MobileDo.MobileField(key = 0x6f65)
    private FeaturesLayer floatingLayer;
}
