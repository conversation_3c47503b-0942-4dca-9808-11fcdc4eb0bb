package com.dianping.mobile.mapi.dztgdetail.button.joy;

import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
import com.dianping.mobile.mapi.dztgdetail.helper.BuyButtonHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;

public class JoyAssembleDealButtonBuilder extends AbstractButtonBuilder {

    @Override
    public void doBuild(DealCtx context, ButtonBuilderChain chain) {
        if (BuyButtonHelper.isValidPinPool(context)) {
            if (context.getPreButton() == null) {
                context.getBuyBar().setBuyType(DealBuyBar.BuyType.PINPOOL.type);
            }
            context.addButton(DealBuyHelper.getJoyAssembleDealButton(context));
        }
        chain.build(context);
    }
}
