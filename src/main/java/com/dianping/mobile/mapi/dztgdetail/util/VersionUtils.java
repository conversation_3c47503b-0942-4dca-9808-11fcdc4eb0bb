package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.deal.common.util.Version;
import org.apache.commons.lang.StringUtils;

public class VersionUtils {

    private static int compare(String versionA, String versionB){
        return Version.compareTo(new Version(versionA), new Version(versionB));
    }

    public static boolean isGreatEqualThan(String versionA,String versionB) {
        if (StringUtils.isBlank(versionA) || StringUtils.isBlank(versionB)) {
            return false;
        }

        return compare(versionA, versionB) >= 0;
    }

    public static boolean isLessAndEqualThan(String versionA, String versionB) {
        if (StringUtils.isBlank(versionA) || StringUtils.isBlank(versionB)) {
            return false;
        }
        return compare(versionA, versionB) <= 0;
    }

    public static boolean isLessThanOrEqualTo(String versionA, String versionB) {
        // 检查版本字符串是否为空
        if (StringUtils.isBlank(versionA) || StringUtils.isBlank(versionB)) {
            return false;
        }
        // 比较版本
        return compare(versionA, versionB) <= 0; // 当 versionA 小于或等于 versionB 时返回 true
    }

}