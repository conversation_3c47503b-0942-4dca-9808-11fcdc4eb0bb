package com.dianping.mobile.mapi.dztgdetail.button.memberfree;

import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BannerTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBanner;
import com.dianping.mobile.mapi.dztgdetail.entity.MemberFreeConfig;
import com.dianping.mobile.mapi.dztgdetail.helper.PriceHelper;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-05-05
 * @desc
 */
public class MemberFreeBannerBuilder extends AbstractButtonBuilder {
    @Override
    protected void doBuild(DealCtx context, ButtonBuilderChain chain) {
        MemberFreeConfig config = LionConfigUtils.getMemberFreeConfig();
        // 构建会员免单banner
        if (isMemberFreeDeal(context)) {
            DealBuyBanner dealBuyBanner = new DealBuyBanner();
            dealBuyBanner.setShow(true);
            dealBuyBanner.setBannerType(BannerTypeEnum.COMMON_TYPE.getType());
            dealBuyBanner.setContent(config.getMemberFreeText());
            context.getBuyBar().setBuyBanner(dealBuyBanner);
        }
        chain.build(context);
    }

    private boolean isMemberFreeDeal(DealCtx context) {
        PriceDisplayDTO normalPrice = PriceHelper.getNormalPrice(context);
        BigDecimal dealPrice = Optional.of(normalPrice)
                .map(PriceDisplayDTO::getPrice)
                .orElse(BigDecimal.ZERO);
        BigDecimal expectedFinalPrice = new BigDecimal("0.01");
        return Objects.equals(RequestSourceEnum.ZDXHYMD.getSource(), context.getRequestSource()) && expectedFinalPrice.compareTo(dealPrice) == 0;
    }
}
