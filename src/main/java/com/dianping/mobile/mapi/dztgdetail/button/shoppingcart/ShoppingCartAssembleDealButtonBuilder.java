package com.dianping.mobile.mapi.dztgdetail.button.shoppingcart;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PriceRuleModule;
import com.dianping.mobile.mapi.dztgdetail.helper.BuyButtonHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.PriceHelper;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;

import static com.dianping.mobile.mapi.dztgdetail.common.enums.ShoppingCartStatusEnum.*;

/**
 * <AUTHOR>
 * @date 2023/5/15
 */
public class ShoppingCartAssembleDealButtonBuilder extends AbstractButtonBuilder {

    @Override
    public void doBuild(DealCtx context, ButtonBuilderChain chain) {
        if (BuyButtonHelper.isValidPinPool(context)) {
            if(DealBuyHelper.xiYuShowMarketPrice(context) || DealBuyHelper.joyShowMarketPrice(context)){
                DealBuyBtn joyAssembleDealButton = DealBuyHelper.getJoyAssembleDealButton(context);
                joyAssembleDealButton.setAddShoppingCartStatus(NONE.code);
                joyAssembleDealButton.setPriceRuleModule(buildPriceRuleModule(context));

                //购物车button文案
                joyAssembleDealButton.setBtnTitle("立即拼团");
                setBtnTag(joyAssembleDealButton, context);

                context.addButton(joyAssembleDealButton);
            }else {
                DealBuyBtn timesCardButton = DealBuyHelper.getAssembleDealButton(context);
                timesCardButton.setAddShoppingCartStatus(NONE.code);
                timesCardButton.setPriceRuleModule(buildPriceRuleModule(context));

                //购物车button文案
                timesCardButton.setBtnTitle("立即拼团");
                setBtnTag(timesCardButton, context);

                context.addButton(timesCardButton);
            }
        }

        chain.build(context);
    }

    private void setBtnTag(DealBuyBtn button, DealCtx context) {
        PinProductBrief pinProductBrief = context.getPinProductBrief();
        if(pinProductBrief == null){
            return;
        }

        Integer pinPersonNum = pinProductBrief.getPinPersonNum();
        String pinPriceStr = PriceHelper.dropLastZero(pinProductBrief.getPrice().setScale(2, RoundingMode.CEILING));
        String btnTagPreStr= String.format("%s人团", pinPersonNum);

        PriceDisplayDTO normalPrice = context.getPriceContext().getNormalPrice();
        if (normalPrice != null && normalPrice.getMarketPrice() != null) {
            String btnTag = btnTagPreStr + "共省￥" + normalPrice.getMarketPrice().subtract(new BigDecimal(pinPriceStr)).multiply(new BigDecimal(pinPersonNum))
                    .setScale(1, RoundingMode.CEILING).stripTrailingZeros().toPlainString();

            button.setBtnTag(btnTag);
        }
    }

    private PriceRuleModule buildPriceRuleModule(DealCtx context) {
        PriceRuleModule priceRuleModule = new PriceRuleModule();
        priceRuleModule.setPriceRuleType(BuyBtnTypeEnum.PINTUAN.getCode());
        priceRuleModule.setPriceRuleTags(buildPriceRuleTag(context));
        priceRuleModule.setPromoDesc(buildPromoDesc(context));
        return priceRuleModule;
    }

    private List<String> buildPriceRuleTag(DealCtx context) {
        List<String> priceRuleTags = Lists.newArrayList();

        PinProductBrief pinProductBrief = context.getPinProductBrief();
        String priceRuleTitle = String.format("%s人拼团", pinProductBrief.getPinPersonNum());
        String pricePerPerson = "￥" + PriceHelper.dropLastZero(pinProductBrief.getPrice().setScale(2, RoundingMode.CEILING)) + "/人";

        priceRuleTags.add(priceRuleTitle);
        priceRuleTags.add(pricePerPerson);

        return priceRuleTags;
    }

    private String buildPromoDesc(DealCtx context) {
        PriceDisplayDTO normalPrice = context.getPriceContext().getNormalPrice();
        String priceStr = PriceHelper.dropLastZero(context.getPinProductBrief().getPrice().setScale(2, RoundingMode.CEILING));
        if (normalPrice != null && normalPrice.getMarketPrice() != null) {
            return "每人省￥" + normalPrice.getMarketPrice().subtract(new BigDecimal(priceStr))
                    .setScale(1, RoundingMode.CEILING).stripTrailingZeros().toPlainString();
        }

        return "";
    }
}
