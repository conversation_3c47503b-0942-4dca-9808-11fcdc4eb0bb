package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.annotation.MobileRequest.Param;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.sankuai.sig.botdefender.adapter.annotation.DecryptedField;
import lombok.Data;

import java.io.Serializable;

import static com.sankuai.sig.botdefender.core.enums.AssetIdType.SHOP_UUID;


/**
 * <AUTHOR>
 * @date 16/11/2022
 * @time 11:43
 */

@TypeDoc(description = "serviceguaranteequery.bin接口请求参数")

@MobileRequest
@Data
public class ServiceguaranteequeryRequest implements IMobileRequest, Serializable {

    @FieldDoc(description = "站点：APP可以不传，其他站点必须传，值参见后端枚举")
    @Param(name = "clienttype", required = false)
    private Integer clienttype;

    @FieldDoc(description = "shopuuid")
    @Param(name = "shopuuid", required = false)
    private String shopuuid;
    @Param(name = "shopuuidEncrypt")
    @DecryptedField(targetFieldName = "shopuuid", assetIdType = SHOP_UUID)
    private String shopuuidEncrypt;

    @FieldDoc(description = "门店id")
    @Param(name = "shopidstr", required = true)
    private String shopidstr;
    @Param(name = "shopidstrEncrypt")
    @DecryptedField(targetFieldName = "shopidstr")
    private String shopidstrEncrypt;

    @Deprecated
    @FieldDoc(description = "团单id，原int类型")
    @Param(name = "dealgroupId", required = true)
    private Integer dealgroupId;

    @FieldDoc(description = "团单id，string类型")
    @Param(name = "stringdealgroupId")
    private String stringDealgroupId;
}
