package com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler.flatten;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler.flatten.DealModuleFlattenProcessor;
import com.dianping.mobile.mapi.dztgdetail.rcf.enums.ModuleType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;


/**
 * <AUTHOR>
 * @create 2024/12/25 11:16
 */
@Slf4j
@Component("detailist_type1")
public class DealListType1FlattenHandler implements DealModuleFlattenProcessor {

    public static final String TYPE_3_1 = "type_3_1";
    public static final String TYPE_3_2 = "type_3_2";
    public static final String TYPE_3_3 = "type_3_3";
    public static final String TYPE_3_4 = "type_3_4";
    public static final String TYPE_3_5 = "type_3_5";
    public static final String TYPE_3_6 = "type_3_6";
    public static final String TYPE_3_6_1 = "type_3_6_1";

    @Override
    public ModuleType getType() {
        return ModuleType.detailist_type1;
    }

    @Override
    public void flattenModule(JSONArray rcfSkuGroupsModule1Flatten, JSONObject module) {
        JSONArray skuGroupsModel1 = (JSONArray) module.get("skuGroupsModel1");
        if (Objects.isNull(skuGroupsModel1) || skuGroupsModel1.isEmpty()){
            return;
        }
        for (int i = 0; i < skuGroupsModel1.size(); i++) {
            JSONObject skuGroupsModel = (JSONObject) skuGroupsModel1.get(i);
            if (Objects.isNull(skuGroupsModel)){
                continue;
            }
            String title = (String) skuGroupsModel.get("title");
            if (StringUtils.isNotBlank(title)){
                JSONObject result = new JSONObject();
                result.put("key", TYPE_3_1);
                result.put("title", title);
                rcfSkuGroupsModule1Flatten.add(result);
            }
            JSONArray dealSkuList = (JSONArray) skuGroupsModel.get("dealSkuList");
            if (dealSkuList == null || dealSkuList.isEmpty()){
                continue;
            }
            for (int j = 0; j < dealSkuList.size(); j++) {
                JSONObject dealSku = (JSONObject) dealSkuList.get(j);
                // 处理 detailist_type1_dealSkuTitle
                flattenDealSkuTitle(dealSku, rcfSkuGroupsModule1Flatten);
                JSONArray items = (JSONArray) dealSku.get("items");
                if (items != null && !items.isEmpty()){
                    // 处理 standard_service_info_v1_3layer_dealSkuItem
                    flattenDealSkuItem(items, rcfSkuGroupsModule1Flatten);
                }
            }
        }
    }

    private String getRightText(JSONObject dealSku){
        if (Objects.isNull(dealSku)){
            return null;
        }
        String copies = (String) dealSku.get("copies");
        String price = (String) dealSku.get("price");
        if (StringUtils.isBlank(copies) && StringUtils.isBlank(price)){
            return null;
        }
        if (StringUtils.isNotBlank(copies) && StringUtils.isNotBlank(price)){
            return copies + " | " + price;
        }
        return StringUtils.isNotBlank(copies) ? copies : price;
    }

    private void flattenDealSkuTitle(JSONObject dealSku, JSONArray rcfSkuGroupsModule1Flatten){
        String title = (String) dealSku.get("title");
        String rightText = getRightText(dealSku);
        JSONObject result = new JSONObject();
        result.put("key", TYPE_3_2);
        result.put("title", title);
        result.put("rightText", rightText);
        rcfSkuGroupsModule1Flatten.add(result);
    }
    private void flattenDealSkuItem(JSONArray items, JSONArray rcfSkuGroupsModule1Flatten){
        if (Objects.isNull(items)){
            return;
        }
        for (int i = 0; i < items.size(); i++) {
            JSONObject item = (JSONObject) items.get(i);
            if (Objects.isNull(item)){
                continue;
            }
            String name = (String) item.get("name");
            String value = (String) item.get("value");
            Integer type = (Integer) item.get("type");
            type = Objects.isNull(type) ? -1 : type;
            Object picValues = item.get("picValues");

            if (0 == type){
                JSONObject result = new JSONObject();
                result.put("key", TYPE_3_3);
                result.put("name", name);
                result.put("value", value);
                rcfSkuGroupsModule1Flatten.add(result);
                continue;
            }

            if (1 == type){
                JSONObject result = new JSONObject();
                result.put("key", TYPE_3_4);
                result.put("name", name);
                result.put("picValues", picValues);
                rcfSkuGroupsModule1Flatten.add(result);
                continue;
            }

            if (3 == type || 4 == type){
                JSONObject result = new JSONObject();
                result.put("key", TYPE_3_5);
                result.put("name", name);
                result.put("picValues", picValues);
                rcfSkuGroupsModule1Flatten.add(result);
                continue;
            }

            if (2 == type || 5 == type || 6 == type){
                flattenAttrValues(item, rcfSkuGroupsModule1Flatten);
            }
        }
    }

    private void flattenAttrValues(JSONObject item, JSONArray rcfSkuGroupsModule1Flatten){
        if (Objects.isNull(item)){
            return;
        }
        JSONArray valueAttrs = (JSONArray) item.get("valueAttrs");
        if (valueAttrs == null || valueAttrs.isEmpty()){
            return;
        }
        String name = (String) item.get("name");
        for (int i = 0; i < valueAttrs.size(); i++) {
            JSONObject valueAttr = (JSONObject) valueAttrs.get(i);
            JSONArray info = (JSONArray) valueAttr.get("info");
            JSONArray values = (JSONArray) valueAttr.get("values");
            if (i == 0){
                JSONObject result = new JSONObject();
                result.put("key", TYPE_3_6);
                result.put("name", name);
                result.put("attrIndex", i+1);
                result.put("attrName", getValueAttrsByIndex(valueAttrs, 0, "name"));
                result.put("attrRightText", joinValueAttrInfo(info));
                rcfSkuGroupsModule1Flatten.add(result);
                continue;
            }
            if (i > 0){
                JSONObject result = new JSONObject();
                result.put("key", TYPE_3_6);
                result.put("attrIndex", i+1);
                result.put("attrName", valueAttr.get("name"));
                result.put("attrRightText", joinValueAttrInfo(info));
                rcfSkuGroupsModule1Flatten.add(result);
            }
            flattenValueAttrsValues(values, rcfSkuGroupsModule1Flatten);
        }
    }

    private void flattenValueAttrsValues(JSONArray values, JSONArray rcfSkuGroupsModule1Flatten){
        if (Objects.isNull(values) || values.isEmpty()){
            return;
        }
        for (int i = 0; i < values.size(); i++) {
            JSONObject valueItem = (JSONObject) values.get(i);
            String name = (String) valueItem.get("name");
            String value = (String) valueItem.get("value");
            JSONObject result = new JSONObject();
            result.put("key", TYPE_3_6_1);
            result.put("desc", String.format("%s: %s", name, value));
            rcfSkuGroupsModule1Flatten.add(result);
        }
    }

    private String joinValueAttrInfo(JSONArray info){
        if (Objects.isNull(info) || info.isEmpty()){
            return null;
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < info.size(); i++) {
            String value = (String) info.get(i);
            if (StringUtils.isNotBlank(value)){
                stringBuilder.append(value);
                stringBuilder.append(",");
            }
        }
        return stringBuilder.length() > 0 ? stringBuilder.substring(0,stringBuilder.length()-2) : stringBuilder.toString();
    }

    private String getValueAttrsByIndex(JSONArray valueAttrs, int index, String key){
        if (Objects.isNull(valueAttrs) || valueAttrs.isEmpty()){
            return null;
        }
        JSONObject valueAttr = (JSONObject) valueAttrs.get(index);
        return (String) valueAttr.get(key);
    }
}
