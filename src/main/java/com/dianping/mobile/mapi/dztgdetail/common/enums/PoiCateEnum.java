package com.dianping.mobile.mapi.dztgdetail.common.enums;

/**
 * Description:Poi一级分类
 * Author: lijie
 * Version: 0.0.1
 * Created: 16/6/18 下午4:57
 */
public enum PoiCateEnum {
    TRAINING(20285,"training");
    private int value;
    private String name;

    PoiCateEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
