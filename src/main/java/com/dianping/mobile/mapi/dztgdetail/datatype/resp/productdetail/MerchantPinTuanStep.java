package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-07
 * @desc
 */
@Data
@MobileDo(id = 0x1ae)
public class MerchantPinTuanStep implements Serializable {
    /**
     * 拼团步骤
     */
    @MobileField(key = 0xe23d)
    private List<MerchantPinTuanStepItem> items;
}
