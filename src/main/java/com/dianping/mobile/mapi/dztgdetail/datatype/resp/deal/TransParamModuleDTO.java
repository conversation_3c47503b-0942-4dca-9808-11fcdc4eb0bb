package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <EMAIL>
 * @Date: 2024/3/14
 */
@TypeDoc(description = "特团拼团透传字段信息")
@MobileDo(id = 0x180b)
@Data
public class TransParamModuleDTO implements Serializable {
    @FieldDoc(description = "场景类型")
    @MobileDo.MobileField(key = 0x92f5)
    private Integer sceneType;

    @FieldDoc(description = "拼团活动ID")
    @MobileDo.MobileField(key = 0x7d22)
    private String pintuanActivityId;

    @FieldDoc(description = "拼团ID")
    @MobileDo.MobileField(key = 0x7e46)
    private String orderGroupId;
}
