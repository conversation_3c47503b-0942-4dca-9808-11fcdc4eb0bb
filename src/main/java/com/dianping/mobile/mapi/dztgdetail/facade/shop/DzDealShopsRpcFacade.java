package com.dianping.mobile.mapi.dztgdetail.facade.shop;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.shop.ShopListContext;
import com.sankuai.dz.api.request.DealShopsRequest;
import com.sankuai.dz.api.response.DealShopIdResponse;
import com.sankuai.dz.product.detail.gateway.spi.enums.PageResponseCodeEnum;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/**
 * @Author: zhangyuan103
 * @Date: 2025/6/3
 */
@Component
public class DzDealShopsRpcFacade extends AbsDzDealShopsFacade<DealShopsRequest, DealShopIdResponse> {

    @Override
    public ShopListContext initShopListContext(DealShopsRequest req, EnvCtx envCtx) {
        ShopListContext shopListContext = new ShopListContext();
        shopListContext.setOrderId(req.getOrderId());
        shopListContext.setMt(envCtx.isMt());
        shopListContext.setNeedLog(needLog(req.getDealGroupId()));
        shopListContext.setGpsCityId(req.getGpsCityId());
        shopListContext.setEnvCtx(envCtx);
        shopListContext.setEntryPage(req.getEntryPage());
        shopListContext.setUserRelation(getUserRelation(envCtx));
        shopListContext.setDealGroupId(req.getDealGroupId());
        shopListContext.setCityId(req.getCityId());
        shopListContext.setMmcuse(req.getMmcuse());
        shopListContext.setShopId(req.getShopId());
        shopListContext.setPageNum(req.getPageNum());
        shopListContext.setPageSize(req.getPageSize());
        shopListContext.setLat(req.getUserLat());
        shopListContext.setLng(req.getUserLng());
        shopListContext.setWxVersion(envCtx.isWxMini() ? req.getVersion() : "");
        return shopListContext;
    }

    @Override
    public DealShopIdResponse fillShopInfo(ShopListContext shopListContext, CompletableFuture<Void> recallShopIdTasks) {
        DealShopIdResponse dealShopIdResponse = new DealShopIdResponse();
        dealShopIdResponse.setShopIds(shopListContext.getShopIds());
        dealShopIdResponse.setTotalCount((int) shopListContext.getTotalCount());
        dealShopIdResponse.setCode(PageResponseCodeEnum.SUCCESS.getCode());
        return dealShopIdResponse;
    }

}
