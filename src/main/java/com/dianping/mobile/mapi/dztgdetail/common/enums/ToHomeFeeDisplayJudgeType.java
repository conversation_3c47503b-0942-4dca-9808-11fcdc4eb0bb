package com.dianping.mobile.mapi.dztgdetail.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/7/15
 **/
public enum ToHomeFeeDisplayJudgeType {
    /**
     * 商品类型字段无值、有值但非「一口价」
     */
    HasNoValueOrNotFixedPrice(1, "商品类型字段无值、有值但非「一口价」"),
    /**
     * 商品类型字段有值且等于上门费
     */
    HasValueAndEqualToHomeFee(2, "商品类型字段有值且等于上门费");

    @Getter
    private int code;
    @Getter
    private String msg;
    ToHomeFeeDisplayJudgeType(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
