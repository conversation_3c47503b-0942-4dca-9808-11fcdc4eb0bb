package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.car;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.PicItemVO;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/12/6 5:41 下午
 */
@MobileDo(id = 0x5205)
public class CarItemVO implements Serializable {
    /**
     * 值描述
     */
    @MobileDo.MobileField(key = 0x31f6)
    private String valueDesc;

    /**
     * 值
     */
    @MobileDo.MobileField(key = 0x97dd)
    private String value;

    /**
     * 标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private PicItemVO title;

    public String getValueDesc() {
        return valueDesc;
    }

    public void setValueDesc(String valueDesc) {
        this.valueDesc = valueDesc;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public PicItemVO getTitle() {
        return title;
    }

    public void setTitle(PicItemVO title) {
        this.title = title;
    }
}
