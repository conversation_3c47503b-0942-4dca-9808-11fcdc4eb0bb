package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.healthExamination;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/24 5:41 下午
 */
@MobileDo(id = 0x8efa)
public class HealthExaminationItemGroupModelVO implements Serializable {
    /**
     * 项目组描述
     */
    @MobileDo.MobileField(key = 0xfebf)
    private String desc;

    /**
     * 项目列表
     */
    @MobileDo.MobileField(key = 0x987a)
    private List<HealthExaminationItemModelVO> itemList;

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public List<HealthExaminationItemModelVO> getItemList() {
        return itemList;
    }

    public void setItemList(List<HealthExaminationItemModelVO> itemList) {
        this.itemList = itemList;
    }
}