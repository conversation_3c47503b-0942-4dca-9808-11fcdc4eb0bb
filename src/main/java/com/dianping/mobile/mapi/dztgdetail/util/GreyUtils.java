package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.lion.facade.LionFacade;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.extend.config.PromoDetailConfig;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.extend.config.PromoDetailConfigDto;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.ShowMarketPriceConfig;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
public class GreyUtils {

    private static Gson gson = new Gson();

    private static boolean isShowMarketPriceDeal(DealCtx ctx) {
        return showXiYuPriceDeal(ctx) || showQinZiPriceDeal(ctx) || showMarketPriceDeal(ctx);
    }

    private static boolean isShowMarketPricePoi(DealCtx dealCtx) {
        return showXiYuMarketPricePoi(dealCtx) || showQinZiMarketPricePoi(dealCtx) || showMarketPricePoi(dealCtx);
    }

    public static boolean isShowMarketPrice(DealCtx ctx) {
        return isXiYuShowMarketPrice(ctx) || isJoyShowMarketPrice(ctx);
    }

    public static boolean isShowMarketPriceCategory(DealCtx ctx) {
        return (isShowMarketPriceDeal(ctx) && isShowMarketPricePoi(ctx))
                || isJoyShowMarketPriceCategory(ctx);
    }

    // 不止是洗浴团单，是使用洗浴样式的团单
    public static boolean isXiYuShowMarketPrice(DealCtx ctx) {
        return showXiYuMarketPrice(ctx) || showQinziMarketPrice(ctx) || showMarketPrice(ctx);
    }

    public static boolean isShoppingCartCategory(DealCtx dealCtx) {
        int categoryId = dealCtx.getCategoryId();
        if (dealCtx.getCategoryId() == 0) {
            return false;
        }

        Set<Integer> shoppingCartCategoryIds = LionFacade
                .getSet(LionConstants.SHOPPING_CART_CATEGORY_IDS, Integer.TYPE, Collections.emptySet());
        if (CollectionUtils.isEmpty(shoppingCartCategoryIds)) {
            return false;
        }

        return shoppingCartCategoryIds.contains(categoryId);
    }

    public static boolean isShoppingCartNewCategory(DealCtx dealCtx) {
        int categoryId = dealCtx.getCategoryId();
        if (dealCtx.getCategoryId() == 0) {
            return false;
        }

        Set<Integer> shoppingCartCategoryIds = LionFacade
                .getSet(LionConstants.SHOPPING_CART_NEW_CATEGORY_IDS, Integer.TYPE, Collections.emptySet());
        if (CollectionUtils.isEmpty(shoppingCartCategoryIds)) {
            return false;
        }

        return shoppingCartCategoryIds.contains(categoryId);
    }

    private static boolean showXiYuMarketPrice(DealCtx ctx) {
        return showXiYuPriceDeal(ctx) && showXiYuMarketPricePoi(ctx) && xiYuShowMarketPriceAB(ctx);
    }

    private static boolean showQinziMarketPrice(DealCtx ctx) {
        return showQinZiPriceDeal(ctx) && showQinZiMarketPricePoi(ctx) && (qinziPlayShowMarketPriceAB(ctx) || ctx.isPreSale());
    }

    private static boolean showMarketPrice(DealCtx ctx) {
        return showMarketPriceDeal(ctx) && showMarketPricePoi(ctx);
    }

    private static boolean showMarketPriceDeal(DealCtx ctx) {
        if (ctx.getChannelDTO() == null) {
            return false;
        }
        int categoryId = ctx.getChannelDTO().getCategoryId();
        Set<Integer> marketCategoryIds = LionFacade
                .getSet(LionConstants.MarketPrice_CATEGORY_IDS, Integer.TYPE, Collections.emptySet());
        if (CollectionUtils.isEmpty(marketCategoryIds)) {
            return false;
        }
        return marketCategoryIds.contains(categoryId);
    }

    private static boolean showMarketPricePoi(DealCtx dealCtx) {
        Set<Integer> marketABTestPoiCategories = LionFacade.getSet(LionConstants.MarketPrice_POI_CATEGORY_IDS,
                Integer.TYPE, Collections.emptySet());

        return CollectionUtils.isNotEmpty(dealCtx.getPoiBackCategoryIds())
                && !Sets.intersection(marketABTestPoiCategories, dealCtx.getPoiBackCategoryIds()).isEmpty();
    }

    public static boolean showQinZiPriceDeal(DealCtx ctx) {
        if (ctx.getChannelDTO() == null) {
            return false;
        }
        int categoryId = ctx.getChannelDTO().getCategoryId();
        Set<Integer> xiYuCategoryIds = LionFacade
                .getSet(LionConstants.QIN_ZI_CATEGORY_IDS, Integer.TYPE, Collections.emptySet());
        if (CollectionUtils.isEmpty(xiYuCategoryIds)) {
            return false;
        }
        return xiYuCategoryIds.contains(categoryId);
    }

    public static boolean showZuLiaoPriceDeal(DealCtx ctx) {
        if (ctx.getChannelDTO() == null) {
            return false;
        }
        int categoryId = ctx.getChannelDTO().getCategoryId();

        Set<Integer> zuLiaoCategoryIds = LionFacade
                .getSet(LionConstants.ZU_LIAO_CATEGORY_IDS, Integer.TYPE, Collections.emptySet());
        if (CollectionUtils.isEmpty(zuLiaoCategoryIds)) {
            return false;
        }

        return zuLiaoCategoryIds.contains(categoryId);
    }

    public static boolean showQinZiMarketPricePoi(DealCtx dealCtx) {
        Set<Integer> joyCardABTestPoiCategories = LionFacade.getSet(LionConstants.QIN_ZI_SHOP_CATEGORY_IDS,
                Integer.TYPE, Collections.emptySet());

        return CollectionUtils.isNotEmpty(dealCtx.getPoiBackCategoryIds())
                && !Sets.intersection(joyCardABTestPoiCategories, dealCtx.getPoiBackCategoryIds()).isEmpty();

    }

    private static boolean qinziPlayShowMarketPriceAB(DealCtx ctx) {
        String key = ctx.isMt() ? "MTQinziShowMarketPrice" : "DPQinziShowMarketPrice";
        String expResult = getExpResult(ctx, key);
        if (expResult != null) {
            return expResult.contains("_a");
        }
        return false;
    }

    public static boolean showXiYuPriceDeal(DealCtx ctx) {
        if (ctx.getChannelDTO() == null) {
            return false;
        }
        int categoryId = ctx.getChannelDTO().getCategoryId();
        Set<Integer> xiYuCategoryIds = LionFacade
                .getSet(LionConstants.XI_YU_CATEGORY_IDS, Integer.TYPE, Collections.emptySet());
        if (CollectionUtils.isEmpty(xiYuCategoryIds)) {
            return false;
        }
        return xiYuCategoryIds.contains(categoryId);
    }

    public static boolean showXiYuMarketPricePoi(DealCtx dealCtx) {
        Set<Integer> joyCardABTestPoiCategories = LionFacade.getSet(LionConstants.XI_YU_SHOP_CATEGORY_IDS,
                Integer.TYPE, Collections.emptySet());

        return CollectionUtils.isNotEmpty(dealCtx.getPoiBackCategoryIds())
                && !Sets.intersection(joyCardABTestPoiCategories, dealCtx.getPoiBackCategoryIds()).isEmpty();

    }

    public static boolean showZuLiaoMarketPricePoi(DealCtx dealCtx) {
        Set<Integer> zuLiaoPoiCategories = LionFacade.getSet(LionConstants.ZU_LIAO_SHOP_CATEGORY_IDS,
                Integer.TYPE, Collections.emptySet());

        if (CollectionUtils.isEmpty(zuLiaoPoiCategories)) {
            return false;
        }

        return CollectionUtils.isNotEmpty(dealCtx.getPoiBackCategoryIds())
                && !Sets.intersection(zuLiaoPoiCategories, dealCtx.getPoiBackCategoryIds()).isEmpty();
    }

    private static boolean xiYuShowMarketPriceAB(DealCtx ctx) {
        String key = ctx.isMt() ? "MTXiYuShowMarketPrice" : "DPXiYuShowMarketPrice";
        String expResult = getExpResult(ctx, key);
        if (expResult != null) {
            return expResult.contains("_a");
        }
        return false;
    }

    public static boolean zuLiaoShowMarketPriceAB(DealCtx ctx) {
        String key = ctx.isMt() ? "MTZuLiaoShowMarketPrice" : "DPZuLiaoShowMarketPrice";
        String expResult = getExpResult(ctx, key);
        if (expResult != null) {
            return expResult.contains("_a") || expResult.contains("_b");
        }
        return false;
    }


    private static String getExpResult(DealCtx ctx, String key) {
        List<ModuleAbConfig> moduleAbConfigs = ctx.getModuleAbConfigs();
        if (moduleAbConfigs != null) {
            for (ModuleAbConfig abConfig : moduleAbConfigs) {
                if (abConfig == null) {
                    continue;
                }
                if (key.equals(abConfig.getKey())) {
                    if (CollectionUtils.isNotEmpty(abConfig.getConfigs()) && abConfig.getConfigs().get(0) != null) {
                        return abConfig.getConfigs().get(0).getExpResult();
                    }
                }
            }
        }
        return null;
    }

    public static boolean needAtmosphereBarAndGeneralPromoDetail(DealCtx ctx) {
        int categoryId = ctx.getCategoryId();

        PromoDetailConfig promoDetailConfig = gson.fromJson(Lion.get("com.sankuai.dzu.tpbase.dztgdetailweb.promoDetailConfig"), new com.google.common.reflect.TypeToken<PromoDetailConfig>() {
        }.getType());
        if (promoDetailConfig == null) {
            return false;
        }

        Map<Integer, PromoDetailConfigDto> dealPublishCategoryId2PromoDetailConfigDtoMap = promoDetailConfig.getDealPublishCategoryId2PromoDetailConfigDtoMap();
        if (MapUtils.isEmpty(dealPublishCategoryId2PromoDetailConfigDtoMap) || !dealPublishCategoryId2PromoDetailConfigDtoMap.containsKey(categoryId)) {
            return false;
        }

        return true;
    }

    public static boolean isQueryCenterGreyBatch1(Integer dealGroupId) {
        try {
            if (dealGroupId == null) {
                return false;
            }
            Integer greyRatio = Lion.getInt("com.sankuai.dzu.tpbase.dztgdetailweb", "com.sankuai.dzu.tpbase.dztgdetailweb.queryCenter.greyRatio.batch1", 0);
            // ratio = 50, 尾号00～49，则走查询中心
            if (dealGroupId % 100 < greyRatio) {
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("intergrateWithQueryCenterBatch1 error, ", e);
        }
        return false;
    }

    public static boolean isQueryCenterGreyBatch2(Integer dealGroupId) {
        try {
            if (dealGroupId == null) {
                return false;
            }
            Integer greyRatio = Lion.getInt("com.sankuai.dzu.tpbase.dztgdetailweb", "com.sankuai.dzu.tpbase.dztgdetailweb.queryCenter.greyRatio.batch2", 0);
            // ratio = 50, 尾号00～49，则走查询中心
            if (dealGroupId % 100 < greyRatio) {
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("intergrateWithQueryCenterBatch2 error, ", e);
        }
        return false;
    }

    public static boolean isQueryCenterGreyBatch3(Integer dealGroupId) {
        try {
            if (dealGroupId == null) {
                return false;
            }
            Integer greyRatio = Lion.getInt("com.sankuai.dzu.tpbase.dztgdetailweb", "com.sankuai.dzu.tpbase.dztgdetailweb.queryCenter.greyRatio.batch3", 0);
            // ratio = 50, 尾号00～49，则走查询中心
            if (dealGroupId % 100 < greyRatio) {
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("intergrateWithQueryCenterBatch3 error, ", e);
        }
        return false;
    }

    public static boolean enableQueryCenterForMainApi(DealCtx dealCtx) {
        try {
            if (dealCtx == null) {
                return false;
            }
            // 灰度走查询中心，并且查询中心未出现异常
            return dealCtx.isUseQueryCenter() && !dealCtx.isQueryCenterHasError();
        } catch (Exception e) {
            log.error("enableQueryCenterForMainApi error, ", e);
        }
        return false;
    }

    public static boolean isJoyShowMarketPrice(DealCtx ctx) {
        List<ShowMarketPriceConfig> showMarketPriceConfigs = Lion.getList(LionConstants.MarketPrice_Show_Config, ShowMarketPriceConfig.class);
        if (CollectionUtils.isEmpty(showMarketPriceConfigs)) {
            return false;
        }
        for (ShowMarketPriceConfig showMarketPriceConfig : showMarketPriceConfigs) {
            if (hitDealGroupCategory(showMarketPriceConfig.getDealGroupCategoryIdS(), ctx)
                    && hitPoiCategory(showMarketPriceConfig.getPoiCategoryIdS(), ctx)
                    && hitAB(showMarketPriceConfig.getDpABKey(), showMarketPriceConfig.getMtABKey(), ctx)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否是券后信息透传样式的底bar，影响了1. 横幅展示券信息 2. 按钮根据券的领取状态修改文案 3. 价格区根据券的领取状态修改文案
     *
     * @param ctx
     * @return
     */
    public static boolean isCouponBar(DealCtx ctx) {
        if (ctx.getChannelDTO() == null) {
            return false;
        }
        int categoryId = ctx.getChannelDTO().getCategoryId();
        Set<Integer> couponCategoryIds = LionFacade
                .getSet(LionConstants.COUPON_BAR_CATEGORY_IDS, Integer.TYPE, Collections.emptySet());
        if (CollectionUtils.isEmpty(couponCategoryIds)) {
            return false;
        }
        return couponCategoryIds.contains(categoryId);
    }

    public static boolean isBeautyBianMeiCoupon(DealCtx ctx) {
        if (ctx.getChannelDTO() == null) {
            return false;
        }
        int categoryId = ctx.getChannelDTO().getCategoryId();
        Set<Integer> beautyBianMeiCouponIds = LionFacade
                .getSet(LionConstants.BEAUTY_BIANMEI_COUPON_CATEGORY_IDS, Integer.TYPE, Collections.emptySet());
        if (CollectionUtils.isEmpty(beautyBianMeiCouponIds)) {
            return false;
        }
        return beautyBianMeiCouponIds.contains(categoryId);
    }

    public static boolean isJoyShowMarketPriceCategory(DealCtx ctx) {
        List<ShowMarketPriceConfig> showMarketPriceConfigs = Lion.getList(LionConstants.MarketPrice_Show_Config, ShowMarketPriceConfig.class);
        if (CollectionUtils.isEmpty(showMarketPriceConfigs)) {
            return false;
        }
        for (ShowMarketPriceConfig showMarketPriceConfig : showMarketPriceConfigs) {
            if (hitDealGroupCategory(showMarketPriceConfig.getDealGroupCategoryIdS(), ctx)
                    && hitPoiCategory(showMarketPriceConfig.getPoiCategoryIdS(), ctx)) {
                return true;
            }
        }
        return false;
    }

    private static boolean hitAB(String dpABKey, String mtABKey, DealCtx ctx) {
        String key = ctx.isMt() ? mtABKey : dpABKey;
        if (StringUtils.isEmpty(key)) {
            return true;
        }
        String expResult = getExpResult(ctx, key);
        if (expResult != null) {
            return expResult.contains("_a");
        }
        return false;
    }

    public static boolean hitPoiCategory(Set<Integer> poiCategoryIdS, DealCtx ctx) {
        if (CollectionUtils.isEmpty(poiCategoryIdS)) {
            return true;
        }
        return CollectionUtils.isNotEmpty(ctx.getPoiBackCategoryIds())
                && !Sets.intersection(poiCategoryIdS, ctx.getPoiBackCategoryIds()).isEmpty();
    }

    public static boolean hitDealGroupCategory(List<Integer> dealGroupCategoryIdS, DealCtx ctx) {
        if (ctx.getChannelDTO() == null) {
            return false;
        }
        int categoryId = ctx.getChannelDTO().getCategoryId();
        if (CollectionUtils.isEmpty(dealGroupCategoryIdS)) {
            return true;
        }
        return dealGroupCategoryIdS.contains(categoryId);
    }

    public static boolean isCouponBagCategory(DealCtx ctx) {
        List<ShowMarketPriceConfig> showMarketPriceConfigs = Lion.getList(LionConstants.COUPON_BAG_CATEGORY_IDS, ShowMarketPriceConfig.class);
        if (CollectionUtils.isEmpty(showMarketPriceConfigs)) {
            return false;
        }
        for (ShowMarketPriceConfig showMarketPriceConfig : showMarketPriceConfigs) {
            if (hitDealGroupCategory(showMarketPriceConfig.getDealGroupCategoryIdS(), ctx)) {
                return true;
            }
        }
        return false;
    }

    public static boolean isShowCouponBag(DealCtx ctx) {
        List<ShowMarketPriceConfig> showMarketPriceConfigs = Lion.getList(LionConstants.COUPON_BAG_CATEGORY_IDS, ShowMarketPriceConfig.class);
        if (CollectionUtils.isEmpty(showMarketPriceConfigs)) {
            return false;
        }
        for (ShowMarketPriceConfig showMarketPriceConfig : showMarketPriceConfigs) {
                if (hitDealGroupCategory(showMarketPriceConfig.getDealGroupCategoryIdS(), ctx)) {
                    if(hitBA(showMarketPriceConfig.getDpABKey(), showMarketPriceConfig.getMtABKey(), ctx)) {
                        return true;
                    } else {
                        return hitBA("DpBeautyCouponBag","MTBeautyCouponBag",ctx);
                    }
                }
            }
        return false;
    }

    public static boolean showWarmUpBanner(DealCtx dealCtx) {
        List<Integer> list = Lion.getList(LionConstants.APP_KEY, LionConstants.WARM_UP_BANNER_CATEGORIES, Integer.class, new ArrayList<>());
        return CollectionUtils.isNotEmpty(list) && list.contains(dealCtx.getCategoryId());
    }

    public static boolean hitMemberLogic(DealCtx dealCtx) {
        if(Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.hit.member.exclusive", Boolean.FALSE)) {
            Cat.logEvent("hitMemberLogic", "hitMemberLogic");
            return true;
        }

        if (!(StringUtils.isNotEmpty(dealCtx.getMrnVersion()) && VersionUtils.isGreatEqualThan(dealCtx.getMrnVersion(), "0.5.2"))) {
            return false;
        }
        // 美团小程序， 点评小程序支持逻辑开关，默认打开，上线前需配置相关值为false，灰度放开
        if (dealCtx.getEnvCtx().isMtMiniApp() && !Lion.getBoolean(LionConstants.APP_KEY, LionConstants.MALL_MEMBER_MT_MINI_APP_SWITCH, true)) {
            return false;
        }
        if (dealCtx.getEnvCtx().isDpMiniApp() && !Lion.getBoolean(LionConstants.APP_KEY, LionConstants.MALL_MEMBER_DP_MINI_APP_SWITCH, true)) {
            return false;
        }
        List<Integer> dealGroupIdWhiteList = Lion.getList(LionConstants.APP_KEY, LionConstants.MALL_MEMBER_WHITE_DEAL_GROUP_IDS, Integer.class, new ArrayList<>());
        if (CollectionUtils.isNotEmpty(dealGroupIdWhiteList) && dealGroupIdWhiteList.contains(dealCtx.getMtId())) {
            return true;
        }

        List<Integer> list = Lion.getList(LionConstants.APP_KEY, LionConstants.MEMBER_CATEGORIES, Integer.class, new ArrayList<>());
        return CollectionUtils.isNotEmpty(list) && list.contains(dealCtx.getCategoryId());
    }

    public static boolean isMemberExclusive(DealCtx dealCtx) {
        return hitMemberLogic(dealCtx) && dealCtx.isMemberExclusive();
    }

    private static boolean hitBA(String dpABKey, String mtABKey, DealCtx ctx) {
        String key = ctx.isMt() ? mtABKey : dpABKey;
        if (StringUtils.isEmpty(key)) {
            return true;
        }
        String expResult = getExpResult(ctx, key);
        if (expResult != null) {
            return expResult.contains("_b") || expResult.contains("b");
        }
        return false;
    }

    public static boolean hitShoppingCartAB(String dpABKey, String mtABKey, DealCtx ctx) {
        String key = ctx.isMt() ? mtABKey : dpABKey;
        if (StringUtils.isEmpty(key)) {
            return false;
        }
        String expResult = getExpResult(ctx, key);
        if (expResult != null) {
            return expResult.contains("b");
        }
        return false;
    }

    public static boolean hitNewShoppingCartAB(String dpABKey, String mtABKey, DealCtx ctx) {
        String key = ctx.isMt() ? mtABKey : dpABKey;
        if (StringUtils.isEmpty(key)) {
            return false;
        }
        String expResult = getExpResult(ctx, key);
        if (expResult != null) {
            return expResult.contains("b");
        }
        return false;
    }

    public static boolean hitCouponBarABC(String dpABKey, String mtABKey, DealCtx ctx) {
        String key = ctx.isMt() ? mtABKey : dpABKey;
        if (StringUtils.isEmpty(key)) {
            return false;
        }
        String expResult = getExpResult(ctx, key);
        if (expResult != null) {
            return expResult.contains("c");
        }
        return false;
    }


    public static boolean isEnableNewReserve() {
        Boolean isEnableNewReserve = LionFacade.get(LionConstants.NEW_RESERVE_ENABLE, Boolean.class);
        if (isEnableNewReserve == null) {
            return false;
        }
        return isEnableNewReserve;
    }

    public static String getNewReserveMrnVersion() {
        return Lion.getString(Environment.getAppName(), LionConstants.NEW_RESERVE_MRN_VERSION);
    }


    public static Map<String, String> getHighlightsStyleType() {
        return Lion.getMap(Environment.getAppName(), LionConstants.HIGHLIGHTS_STYLE_TYPE, String.class);
    }

    public static Map<String, String> getHighlightsIdentify() {
        return Lion.getMap(Environment.getAppName(), LionConstants.HIGHLIGHTS_IDENTIFY, String.class);
    }


    public static boolean judgeMemberPriceCategory(DealCtx ctx) {
        if (ctx.getChannelDTO() == null) {
            return false;
        }
        int categoryId = ctx.getChannelDTO().getCategoryId();
        Set<Integer> memberPriceCategoryIds = LionFacade
                .getSet(LionConstants.MEMBER_PRICE_CATEGORY_IDS, Integer.TYPE, Collections.emptySet());
        if (CollectionUtils.isEmpty(memberPriceCategoryIds)) {
            return false;
        }
        return memberPriceCategoryIds.contains(categoryId);
    }
}
