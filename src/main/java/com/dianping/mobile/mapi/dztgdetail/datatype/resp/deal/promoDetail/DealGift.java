package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/4/17 2:26 PM
 */
@Data
@MobileDo(id = 0x5e3)
public class DealGift implements Serializable {
    /**
     * 主标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * 活动时间描述
     */
    @MobileDo.MobileField(key = 0x12d8)
    private String timeDesc;

    /**
     * 份数
     */
    @MobileDo.MobileField(key = 0xf240)
    private int couponNum;

    /**
     * 商品标签描述
     */
    @MobileDo.MobileField(key = 0xc857)
    private String productTag;

    /**
     * 左上角浮标
     */
    @MobileDo.MobileField(key = 0xad6c)
    private String specificTag;

    /**
     * 左上角icon浮标
     */
    @MobileDo.MobileField(key = 0xa5fd)
    private String leftIconTag;

    /**
     * 主图
     */
    @MobileDo.MobileField(key = 0x1fa2)
    private String thumbnail;

    /**
     * 活动ID
     */
    @MobileDo.MobileField(key = 0xe91)
    private long activityId;

    /**
     * 新客活动指定前缀
     */
    @MobileDo.MobileField(key = 0x359b)
    private String customerActivityPrefix;
}
