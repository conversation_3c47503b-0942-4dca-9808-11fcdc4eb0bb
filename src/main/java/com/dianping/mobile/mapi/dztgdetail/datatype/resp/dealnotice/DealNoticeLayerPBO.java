package com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealnotice;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.Guarantee;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/9/30 14:47
 * https://mobile.sankuai.com/studio/model/info/39393
 */
@Data
@MobileDo(id = 0xef55)
public class DealNoticeLayerPBO implements Serializable {
    /**
     * 模块名
     */
    @MobileDo.MobileField(key = 0xb308)
    private String moduleName;
    /**
     * 展示信息
     */
    @MobileDo.MobileField(key = 0xe628)
    private List<String> displayValues;
    /**
     * 标签
     */
    @MobileDo.MobileField(key = 0x342f)
    private List<Guarantee> tags;

    /**
     * 浮层信息:包含链接等
     */
    @MobileDo.MobileField(key = 0xd34e)
    private GeneralLayerConfig layer;
}
