package com.dianping.mobile.mapi.dztgdetail.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 *
 *         enableSwitch：总开关
 *
 *         allPass：全行业放量开关，默认false
 *
 *         categoryIds：适用行业的类目id, 如果为空则区分行业
 *
 *         mtCityIds ：适用美团的城市id，如果为空则不区分城市
 *
 *         dpCityIds：适用的点评城市id，如果为空则去区分城市
 *         enablePriceItemList > 比价助手开关
 *
 *         优先级：
 *
 *         enableSwitch > allPass > categoryIds 、mtCityIds、dpCityIds
 *         enableSwitch > enablePriceItemList
 * @create 2024/4/21 21:18
 */
@Data
public class DealRepurchaseConfig {
    /**
     * 总开关
     */
    private boolean enableSwitch;
    /**
     * 全行业推广
     */
    private boolean allPass;
    /**
     * 在推广的行业类目id
     */
    private List<Integer> categoryIds;
    /**
     * 在推广的mt城市id
     */
    private List<Integer> mtCityIds;
    /**
     * 在推广的dp城市id
     */
    private List<Integer> dpCityIds;

    /**
     * 跨店推荐开关
     */
    private boolean enablePriceItemList;
}
