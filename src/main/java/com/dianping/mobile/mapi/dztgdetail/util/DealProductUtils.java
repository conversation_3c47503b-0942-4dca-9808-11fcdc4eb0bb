package com.dianping.mobile.mapi.dztgdetail.util;

import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDisplayShopCheckDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupShopRelationCheckResultDTO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class DealProductUtils {

    public static List<Long> getDpDisplayShopIds(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO == null
                || dealGroupDTO.getDisplayShopInfo() == null
                || dealGroupDTO.getDisplayShopInfo().getDpDisplayShopIds() == null) {
            return Lists.newArrayList();
        }
        return dealGroupDTO.getDisplayShopInfo().getDpDisplayShopIds();
    }

    public static boolean checkShopNoExist(DealGroupDTO dealGroupDTO, Long shopId, boolean mt) {
        return !checkShopExist(dealGroupDTO, shopId, mt);
    }

    public static boolean checkShopExist(DealGroupDTO dealGroupDTO, Long shopId, boolean mt) {
        if (dealGroupDTO == null) {
            return false;
        }
        DealGroupDisplayShopCheckDTO displayShopCheckResult = dealGroupDTO.getDisplayShopCheckResult();
        if (displayShopCheckResult == null) {
            return false;
        }
        List<DealGroupShopRelationCheckResultDTO> relationCheckResultDTOS;
        if (mt) {
            relationCheckResultDTOS = displayShopCheckResult.getMtDisplayShopCheckResult();
        } else {
            relationCheckResultDTOS = displayShopCheckResult.getDpDisplayShopCheckResult();
        }
        if (CollectionUtils.isNotEmpty(relationCheckResultDTOS)) {
            List<DealGroupShopRelationCheckResultDTO> collected = relationCheckResultDTOS.stream()
                    .filter(Objects::nonNull)
                    .filter(k -> Boolean.TRUE.equals(k.getRelationCheckSuccess()))
                    .filter(k -> Objects.equals(shopId, k.getShopId()))
                    .collect(Collectors.toList());
            return CollectionUtils.isNotEmpty(collected);
        }
        return false;
    }
}
