package com.dianping.mobile.mapi.dztgdetail.availabletime.impl;

import com.dianping.mobile.mapi.dztgdetail.availabletime.AvailableTimeStrategy;
import com.dianping.mobile.mapi.dztgdetail.common.enums.AvailableTimeStrategyEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.dianping.mobile.mapi.dztgdetail.helper.AvailableTimeHelper.ALL_DAY;
import static com.dianping.mobile.mapi.dztgdetail.helper.AvailableTimeHelper.PARTIAL_TIME;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/12/6 15:14
 */
@Slf4j
@Service
public class KTVAvailableTimeStrategy implements AvailableTimeStrategy {
    @Override
    public String getAvailableTime(DealCtx dealCtx) {
        long mtDealGroupId = Optional.ofNullable(dealCtx).map(DealCtx::getDealGroupDTO)
                .map(DealGroupDTO::getMtDealGroupId).orElse(0L);
        try {
            List<ServiceProjectAttrDTO> attrs = Optional.ofNullable(dealCtx).map(DealCtx::getDealGroupDTO)
                    .map(DealGroupDTO::getServiceProject).map(DealGroupServiceProjectDTO::getMustGroups)
                    .orElse(Collections.emptyList()).stream().filter(Objects::nonNull)
                    .flatMap(group -> Optional.ofNullable(group.getGroups()).orElse(Collections.emptyList()).stream())
                    .filter(Objects::nonNull)
                    .flatMap(
                            project -> Optional.ofNullable(project.getAttrs()).orElse(Collections.emptyList()).stream())
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(attrs)) {
                return StringUtils.EMPTY;
            }
            String startTime = attrs.stream().filter(attr -> attr.getAttrName().equals("start_time")).findFirst()
                    .map(ServiceProjectAttrDTO::getAttrValue).orElse(null);
            String endTime = attrs.stream().filter(attr -> attr.getAttrName().equals("end_time")).findFirst()
                    .map(ServiceProjectAttrDTO::getAttrValue).orElse(null);
            if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
                return StringUtils.EMPTY;
            }
            // 这个24:00的时间非常地奇怪,对应的上单里面的次日00:00
            if ("00:00".equals(startTime) && "23:59".equals(endTime)
                    || "00:00".equals(startTime) && "24:00".equals(endTime)) {
                return ALL_DAY;
            } else {
                return PARTIAL_TIME;
            }
        } catch (Exception e) {
            log.error("KTVAvailableTimeStrategy error,mtDealGroupId:{}", mtDealGroupId, e);
        }
        return StringUtils.EMPTY;
    }

    @Override
    public AvailableTimeStrategyEnum getStrategyType() {
        return AvailableTimeStrategyEnum.KTV_STRATEGY;
    }
}
