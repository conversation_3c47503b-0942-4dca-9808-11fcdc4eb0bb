package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.atmosphere;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-01-03
 * @desc 图片模型
 */
@Data
@MobileDo(id = 0xb41a)
public class ImageVO implements Serializable {

    @FieldDoc(description = "图片高")
    @MobileDo.MobileField(key = 0x261f)
    private int height;

    @FieldDoc(description = "图片宽")
    @MobileDo.MobileField(key = 0x2b78)
    private int width;

    @FieldDoc(description = "图片链接")
    @MobileDo.MobileField(key = 0xc56e)
    private String url;
}
