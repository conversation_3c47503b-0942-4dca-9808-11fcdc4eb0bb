package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.backgroud.BottomBarBackgroundVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.rich.content.RichContentVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2025/3/16 15:07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ButtonTopTagVO implements Serializable {

    /**
     * 内容
     */
    private List<RichContentVO> content;

    /**
     * 背景
     */
    private BottomBarBackgroundVO background;

    /**
     * 1=左上角,2=右上角
     */
    private int position;

}