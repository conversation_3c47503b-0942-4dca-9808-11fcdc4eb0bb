package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-10-09
 * @desc 团单简要信息返回值
 */
@TypeDoc(description = "团单简要信息展示层对象")
@Data
@MobileDo(id = 0xf1f408b3)
public class DealTinyInfoVO {
    @FieldDoc(description = "团单Id")
    @MobileDo.MobileField(key = 0x9a1c)
    private Integer dealGroupId;

    @FieldDoc(description = "团单标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "团单副标题")
    @MobileDo.MobileField(key = 0xd894)
    private String subTitle;

    @FieldDoc(description = "年销量，已售1万")
    @MobileDo.MobileField(key = 0xa2c0)
    private String saleTag;

    @FieldDoc(description = "1:1头图")
    @MobileDo.MobileField(key = 0x30ed)
    private String headPic;

    @FieldDoc(description = "价格力标签")
    @MobileDo.MobileField(key = 0x2617)
    private List<String> pricePowerTag;

    @FieldDoc(description = "折扣，如5.0折")
    @MobileDo.MobileField(key = 0x6509)
    private String discount;

    @FieldDoc(description = "到手价")
    @MobileDo.MobileField(key = 0xe949)
    private String finalPrice;

    @FieldDoc(description = "门市价（划线价）")
    @MobileDo.MobileField(key = 0x6242)
    private String marketPrice;

    @FieldDoc(description = "skuId，到综团单的skuId就是点评团单Id")
    @MobileDo.MobileField(key = 0xf59e)
    private Integer skuId;

    @FieldDoc(description = "提单页")
    @MobileDo.MobileField(key = 0x60b)
//    @EncryptedLinkField(queries = "shopid")
    private String directBuyJumpUrl;

    @FieldDoc(description = "团单二级类目")
    @MobileDo.MobileField(key = 0x33fe)
    private Integer categoryId;

    @FieldDoc(description = "按钮文案")
    @MobileDo.MobileField(key = 0xa5e3)
    private String btnText;
}
