package com.dianping.mobile.mapi.dztgdetail.common.enums;

/**
 * deal展示类型枚举类
 * <p/>
 * Created by ou<PERSON> on 14-6-19.
 */
public enum DealShowTypeEnum {
    // 一般，即未特殊指定的deal展示形式
    NORMAL("normal"),
    // 带订金的婚纱摄影单
    WEDDING("wedding"),
    //酒店
    HOTEL("hotel");
    private String name;

    private DealShowTypeEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
