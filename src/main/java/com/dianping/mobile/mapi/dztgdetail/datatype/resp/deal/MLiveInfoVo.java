package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: z<PERSON><PERSON><PERSON><EMAIL>
 * @Date: 2024/3/14
 */
@TypeDoc(description = "价保浮层信息")
@MobileDo(id = 0xba58)
@Data
public class MLiveInfoVo implements Serializable {
    @FieldDoc(description = "直播ID")
    @MobileDo.MobileField(key = 0x5842)
    private Long mLiveId;

    @FieldDoc(description = "是否为直播渠道专享商品 0-否 1-是")
    @MobileDo.MobileField(key = 0x8d2f)
    private boolean channelIdentity;

    @FieldDoc(description = "直播侧商品类型")
    @MobileDo.MobileField(key = 0xd7f5)
    private String goodsTypeId;
}
