package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.DecryptedField;
import lombok.Data;

import java.io.Serializable;

@Data
@TypeDoc(description = "团单SKU选择模块请求参数")
@MobileRequest
public class DzDealSkuSelectRequest implements IMobileRequest, Serializable {
    /**
     * 门店id
     */
    @MobileRequest.Param(name = "shopid")
    private String shopid;
    @MobileRequest.Param(name = "shopidEncrypt")
    @DecryptedField(targetFieldName = "shopid")
    private String shopidEncrypt;

    /**
     *
     */
    @MobileRequest.Param(name = "dealgroupid", required = true)
    private String dealgroupid;

    public String getShopid() {
        return shopid;
    }

    public void setShopid(String shopid) {
        this.shopid = shopid;
    }

    public String getDealgroupid() {
        return dealgroupid;
    }

    public void setDealgroupid(String dealgroupid) {
        this.dealgroupid = dealgroupid;
    }
}