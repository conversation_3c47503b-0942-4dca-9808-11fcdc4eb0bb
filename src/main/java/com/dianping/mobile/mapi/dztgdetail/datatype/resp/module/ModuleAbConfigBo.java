package com.dianping.mobile.mapi.dztgdetail.datatype.resp.module;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

import java.io.Serializable;
import java.util.List;

@MobileDo(id = 0x80ce)
public class ModuleAbConfigBo implements Serializable {

    @FieldDoc(description = "模块名称")
    @MobileField(key = 0x9e5e)
    private String key;

    @FieldDoc(description = "ab测试")
    @MobileField(key = 0xddc1)
    private List<AbConfigBo> configs;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public List<AbConfigBo> getConfigs() {
        return configs;
    }

    public void setConfigs(List<AbConfigBo> configs) {
        this.configs = configs;
    }
}