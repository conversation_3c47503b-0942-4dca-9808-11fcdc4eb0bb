package com.dianping.mobile.mapi.dztgdetail.datatype.context;

import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dzcard.navigation.api.dto.CardQualifyEventIdDTO;
import lombok.Data;

import java.io.Serializable;

@Data
public class PriceContext implements Serializable {

    private boolean newUser;//折扣卡，只有buttonBuilder用了，可以放到二阶段
    private boolean dcCardMemberDay;//折扣卡，只有buttonBuilder用了，可以放到二阶段
    private CardQualifyEventIdDTO dcCardMemberCard;//折扣卡，只有buttonBuilder、dealBuilder、PriceDisplayWrapper的process中用了
//    private ShopMemberCardCtx shopMemberCardCtx;//商家会员相关，暂时没人用后续替代商家会员返回值

    // 是否包含渠道专属立减
    private boolean hasExclusiveDeduction;

    private boolean isZuLiaoButtonNewStyle =false;

    private PriceDisplayDTO dcCardMemberPrice;//折扣卡

    private PriceDisplayDTO idlePromoPrice;
    private PriceDisplayDTO normalPrice;
    private boolean isMemberNormalPrice;
    private PriceDisplayDTO costEffectivePrice;

    private PriceDisplayDTO dealPromoPrice;
    private PriceDisplayDTO originDealPromoPrice;
    private boolean isMemberPromoDealPrice;
    private PriceDisplayDTO atmosphereBarAndGeneralPromoDetailPrice;

    private String normalPriceCipher;
    private String dealPromoPriceCipher;
    private boolean isDisplayInflateCoupon;
}
