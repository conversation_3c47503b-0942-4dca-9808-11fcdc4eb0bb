package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.cat.Cat;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.exception.DealPriceException;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
@Slf4j
public class PriceHelper {

    private static final DecimalFormat PRICE_FORMAT = new DecimalFormat("0.##");

    public static String priceFormat(double price){
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.helper.PriceHelper.priceFormat(double)");
        return PRICE_FORMAT.format(price);
    }


    //整数的情况下，去掉小数位
    public static String format(BigDecimal price) {
        if (new BigDecimal(price.intValue()).compareTo(price) == 0) { //判断是否为整数
            BigDecimal newPrice = price.setScale(0, RoundingMode.FLOOR);
            return newPrice.toString();
        }
        return price.toString();
    }

    //去掉小数位
    public static String formatPrice(BigDecimal price) {
        return price.stripTrailingZeros().toPlainString();
    }

    public static String dropLastZero(BigDecimal price) {
        if (Objects.isNull(price)) {
            throw new DealPriceException("price is null");
        }
        //判断是否为整数
        if (new BigDecimal(price.intValue()).compareTo(price) == 0) {
            BigDecimal newPrice = price.setScale(0, RoundingMode.FLOOR);
            return newPrice.toString();
        }
        DecimalFormat df = new DecimalFormat("0.##");
        return df.format(price);
    }

    /**
     * 计算折扣率（保留一位小数 + 向上取整）
     */
    public static String calcDiscountRate(BigDecimal dealGroupPrice, BigDecimal promoPrice) {
        if (dealGroupPrice == null || promoPrice == null
                || dealGroupPrice.compareTo(BigDecimal.ZERO) == 0
                || promoPrice.compareTo(BigDecimal.ZERO) == 0
                || promoPrice.compareTo(dealGroupPrice) == 0
                || dealGroupPrice.compareTo(promoPrice) < 0) {
            return null;
        }
        return promoPrice.divide(dealGroupPrice, 2, RoundingMode.CEILING)
                .multiply(new BigDecimal(10))
                .stripTrailingZeros()
                .toPlainString();
    }

    public static PriceDisplayDTO getNormalPrice(DealCtx context) {
        PriceDisplayDTO normalPrice = context.getPriceContext().getNormalPrice();

        BigDecimal dealGroupPrice = Optional.ofNullable(context.getDealGroupBase())
                .map(DealGroupBaseDTO::getDealGroupPrice).orElse(BigDecimal.ZERO);
        BigDecimal marketPrice = Optional.ofNullable(context.getDealGroupBase()).map(DealGroupBaseDTO::getMarketPrice)
                .orElse(BigDecimal.ZERO);
        if (normalPrice == null) {
            normalPrice = new PriceDisplayDTO();
            normalPrice.setPrice(dealGroupPrice);
            normalPrice.setMarketPrice(marketPrice);
        }

        if (context.getEnvCtx().isExternalAndNoScene()) {
            normalPrice.setPrice(dealGroupPrice);
        }

        return normalPrice;
    }

    public static PriceDisplayDTO getDealPromoPrice(DealCtx context) {
        PriceDisplayDTO dealPromoPrice = context.getPriceContext().getDealPromoPrice();

        if (dealPromoPrice == null) {
            dealPromoPrice = new PriceDisplayDTO();
            dealPromoPrice.setPrice(context.getDealGroupBase().getDealGroupPrice());
            dealPromoPrice.setMarketPrice(context.getDealGroupBase().getMarketPrice());
        }

        if (context.getEnvCtx().isExternalAndNoScene()) {
            dealPromoPrice.setPrice(context.getDealGroupBase().getDealGroupPrice());
        }

        return dealPromoPrice;
    }

    /**
     * 获取团购优惠价（根据是否市场价逻辑判断取市场优惠价还是普通优惠价）
     * @param context
     * @return
     */
    public static PriceDisplayDTO getMarketOrNormalPromoPrice(DealCtx context) {
        PriceDisplayDTO dealPromoPrice;
        if (GreyUtils.isShowMarketPrice(context)) {
            dealPromoPrice = context.getPriceContext().getDealPromoPrice();
        } else {
            dealPromoPrice = context.getPriceContext().getNormalPrice();
        }

        if (dealPromoPrice == null) {
            dealPromoPrice = new PriceDisplayDTO();
            dealPromoPrice.setPrice(context.getDealGroupBase().getDealGroupPrice());
            dealPromoPrice.setMarketPrice(context.getDealGroupBase().getMarketPrice());
        }

        if (context.getEnvCtx().isExternalAndNoScene()) {
            dealPromoPrice.setPrice(context.getDealGroupBase().getDealGroupPrice());
        }

        return dealPromoPrice;
    }

    /**
     * 使用"?"隐藏价格第n位，可隐藏多位
     * @param price
     * @return
     */
    public static String hidePriceWithQuestionMark(String price, Set<Integer> posSet) {
        if (StringUtils.isBlank(price) || CollectionUtils.isEmpty(posSet)) {
            return price;
        }
        // 价格小于10直接设置为?
        try {
            String priceStr = price.replace(",", "");
            double priceValue = Double.parseDouble(priceStr);
            if (priceValue < 10) {
                return "?";
            }
        } catch (NumberFormatException e) {
            log.error("[PriceHelper] hidePriceWithQuestionMark error", e);
            return price;
        }
        StringBuilder res = new StringBuilder(price);
        int numCnt = 0;
        for (int i = 0; i < price.length(); i++)  {
            char c = price.charAt(i);
            if (c != '.' && c != ',') {
                numCnt++;
                // 需要隐藏的位置，设置为"?"
                if (posSet.contains(numCnt)) {
                    res.setCharAt(i, '?');
                }
            }
        }
        return res.toString();
    }
}
