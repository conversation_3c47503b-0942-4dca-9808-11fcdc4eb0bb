package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.dianping.mobile.framework.annotation.MobileRequest.Param;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.DecryptedField;
import lombok.Data;

import java.io.Serializable;

import static com.sankuai.sig.botdefender.core.enums.AssetIdType.SHOP_UUID;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2023/7/26
 * @since mapi-dztgdetail-web
 */
@Data
@TypeDoc(description = "团单搭售请求参数")
@MobileRequest
public class DzdealbundinfoReq implements IMobileRequest, Serializable {

    /**
     * 商户UUID
     */
    @Param(name = "shopuuid")
    private String shopuuid;
    @Param(name = "shopuuidEncrypt")
    @DecryptedField(targetFieldName = "shopuuid", assetIdType = SHOP_UUID)
    private String shopuuidEncrypt;

    /**
     * 商户id
     */
    @Param(name = "poiid")
    private Long poiid;
    @Param(name = "poiidEncrypt")
    @DecryptedField(targetFieldName = "poiid")
    private String poiidEncrypt;

    /**
     * 站点：APP可以不传，值参见后端枚举
     */
    @Param(name = "clienttype")
    private Integer clienttype;

    /**
     * 用户经度（ 只接受火星坐标系gcj02）
     */
    @Param(name = "userlng")
    private Double userlng;

    /**
     * 城市id
     */
    @Param(name = "cityid", required = true)
    private Integer cityid;

    /**
     * @Deprecated use stringDealgroupid instead
     * 团单id，原int类型
     */
    @Deprecated
    @Param(name = "dealgroupid", required = true)
    private Integer dealgroupid;

    /**
     * 团单id，string类型
     */
    @Param(name = "stringdealgroupid")
    private String stringDealGroupId;

    /**
     * 用户纬度（只接受火星坐标系gcj02）
     */
    @Param(name = "userlat")
    private Double userlat;

}


