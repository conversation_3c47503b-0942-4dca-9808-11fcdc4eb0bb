package com.dianping.mobile.mapi.dztgdetail.datatype.resp.other;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/6/21.
 */
@MobileDo(name = "Pair")
public class Pair implements Serializable {
    private static final long serialVersionUID = 0L;

    @MobileDo.MobileField(key = 0x91b)
    private String id;

    //值同id，配合前端改造所增字段
    private String iD;

    @MobileDo.MobileField(key = 0xee8f)
    private String name;

    @FieldDoc(description = "概述")
    @MobileDo.MobileField(key = 0xd894)
    private String subTitle;

    @MobileDo.MobileField(key = 0x372)
    private int type;

    @MobileDo.MobileField(key = 0x71ae)
    private boolean isUsed;

    @MobileDo.MobileField(key = 0x820f)
    private String userDate;

    @FieldDoc(description = "类型")
    @MobileDo.MobileField(key = 0x9e5e)
    private String key;

    public Pair() {
    }

    public Pair(String id, String name, int type) {
        this.id = id;
        this.iD = id;
        this.name = name;
        this.type = type;
        this.key = id;
    }

    public static Pair of(String id, String name, int type) {
        return new Pair(id, name, type);
    }

    public Pair(String id, String iD, String name, Integer type, String key) {
        this.id = id;
        this.iD = iD;
        this.name = name;
        this.type = type;
        this.key = key;
    }

    @JsonProperty("id")
    public String getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(String id) {
        this.id = id;
    }

    @JsonProperty("iD")
    public String getID() {
        return iD;
    }

    @JsonProperty("iD")
    public void setID(String iD) {
        this.iD = iD;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public boolean isUsed() {
        return isUsed;
    }

    public void setUsed(boolean used) {
        isUsed = used;
    }

    public String getUserDate() {
        return userDate;
    }

    public void setUserDate(String userDate) {
        this.userDate = userDate;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

}