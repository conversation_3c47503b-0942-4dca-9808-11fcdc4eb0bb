package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

@MobileDo(id = 0x93de)
public class DzDealbaseGreyResult implements Serializable {
    /**
    * 是否走新链路查询,true为新链路
    */
    @MobileDo.MobileField(key = 0x85bd)
    private boolean greyResult;

    public boolean getGreyResult() {
        return greyResult;
    }

    public void setGreyResult(boolean greyResult) {
        this.greyResult = greyResult;
    }
}
