package com.dianping.mobile.mapi.dztgdetail.common.constants;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Created by yangquan02 on 19/12/11.
 */
public class Cons {

    public static final int JOY_BAR = 312;
    public static final int JOY_TEA = 318;
    public static final String SEPARATOR = ",";

    // 商家作弊相关
    public static final int ATTRS_B_CHEAT = 11050001;
    // 商家刷单，针对ATTR:11050001，TASK: MT-6745
    public static final String CHEAT_SCALPING = "1";
    public static final int ATTRS_BOOKING_PHONE = 552;

    public static final String MONEY_SYMBOL = "¥";
    public static final int DP_ONLY = 1;

    public static final String DEVICE_TOKEN = "device_token";
    public static final String STR_SEPARATOR_SEMICOLON = ";";
    public static final String STR_SEPARATOR_COMMA = ",";
    public static final String STR_SEPARATOR_DOT = ".";

    // http参数名或uri中的属性名
    public static final String HTTPPARAM_UTM_COTENT = "utm_content";
    public static final String HTTPPARAM_UTM_MEDIUM = "utm_medium";
    public static final String HTTPPARAM_UTM_TERM = "utm_term";
    public static final String HTTPPARAM_VERSION_NAME = "version_name";
    public static final String HTTPPARAM_UTM_CAMPAIGN = "utm_campaign";
    public static final String HTTPPARAM_UUID = "uuid";
    public static final String HTTPPARAM_CI = "ci";

    public static final int ATTRS_ROOM_INFO = 38;
    public static final int ATTRS_ROOM_NAMES = 9994;
    public static final int ATTRS_CATE = 9995;
    // 2：住宿时间类型；13：是否代金券；11:deal不可用日期;999880 酒店大客标签 38:住宿类型;999890酒店团购直连信息
    public static final int ATTRID_HOTEL_ROOM = 1;
    public static final int ATTRS_TIME_TYPE = 2;
    public static final int ATTRS_DEAL_NOT_VALID = 11;
    public static final int ATTRS_IS_COUPON = 13;
    public static final int ATTRS_ONLINE_ORDER = 33;
    public static final int ATTRS_HOTEL_TAG = 999880;
    public static final int ATTRS_HOTEL_ZL = 999890;
    public static final int ATTRS_BOOKING_INFO = 58;

    public static final String PROMOTION_TAG_COLOR = "#FF8000";



    //旅游出发地和目的地
    public static final int ATTRS_TRAVEL_FROMTO_TYPE = 252;
    public static final int ATTRS_TRAVEL_PRODUCT_TYPE = 575;
    //是否是跟团游
    public static final int ATTRS_TRAVEL_GTY_INFO = 11020003;
    //旅游酒+景deal
    public static final int ATTRS_TRAVEL_JJ_INFO = 11020004;

    //属性值
    public static final int ATTRS_VALUE_HOTEL_COUPON_CATE = 1000320;

    //新婚纱摄影
    public static final int ATTRS_NEW_WEDDING_PHOTO = 999886;
    //旅游单子是否需要预约
    public static final int ATTRS_NEED_ORDER = 999887;

    //综合品类属性560，985，986，987
    public static final int ATTRS_BEAUTY_HAIR = 560;//丽人  值为“剪发”“烫/染”"护理"等
    public static final int ATTRS_CAR_SEAT = 985;//汽车 “S”“F”“A” 对应五座、七座、全部
    public static final int ATTRS_CAR_SERVICETIME = 986;//汽车 服务时长
    public static final int ATTRS_CAR_WAX = 987;//汽车 打蜡品牌

    public static final int ATTRS_BEAUTY_HAIR_NAME = 81;//美发套餐名称
    public static final int ATTRS_BEAUTY_HAIR_HULI = 11060001;//是否含护理
    public static final int ATTRS_BEAUTY_HAIR_FXS_LEVEL = 11060002;//发型师级别
    public static final int ATTRS_BEAUTY_HAIR_BRAND = 11060003;//美发品牌
    public static final int ATTRS_BEAUTY_HAIR_BRAND_OTHER = 11060004;//其他美发品牌


    public static final Set<Integer> DEAL_OPTION_ATTRS = Collections.unmodifiableSet(new HashSet<Integer>() {{
        add(ATTRID_HOTEL_ROOM);
        add(ATTRS_TIME_TYPE);
        add(ATTRS_DEAL_NOT_VALID);
        add(ATTRS_IS_COUPON);
        add(ATTRS_ROOM_INFO);
        add(ATTRS_HOTEL_TAG);
        add(ATTRS_NEED_ORDER);
        add(ATTRS_NEW_WEDDING_PHOTO);
        add(ATTRS_HOTEL_ZL);
        add(ATTRS_TRAVEL_FROMTO_TYPE);
        add(ATTRS_TRAVEL_PRODUCT_TYPE);
        add(ATTRS_TRAVEL_GTY_INFO);

        add(ATTRS_BEAUTY_HAIR_NAME);
        add(ATTRS_BEAUTY_HAIR_HULI);
        add(ATTRS_BEAUTY_HAIR_FXS_LEVEL);
        add(ATTRS_BEAUTY_HAIR_BRAND);
        add(ATTRS_BEAUTY_HAIR_BRAND_OTHER);

        add(ATTRS_BEAUTY_HAIR);
        add(ATTRS_CAR_SEAT);
        add(ATTRS_CAR_SERVICETIME);
        add(ATTRS_CAR_WAX);
    }});

    public static final String WARM_UP_DEAL = "WARMUP_DEAL";

    public static final String FOLD_STYLE = "fold_style";// 折叠样式

    public static final String MALL_FOOD_POI_DEAL_DETAIL_KEY = "mall_food";

    public static final String MALL_FOOD_POI_DEAL_DETAIL_EXTRA_INFO = "newtuandeal";

    public static final String CARD_STYLE = "card_style";// 卡片样式
    public static final String CARD_STYLE_V2 = "card_style_v2";// 卡片V2样式（框架改版）


    public static List<String> COUPON_ALLEVIATE_1_ABKEYS = Lists.newArrayList("MtCouponAlleviate1Exp",
            "DpCouponAlleviate1Exp");

    public static List<String> COUPON_ALLEVIATE_2_ABKEYS = Lists.newArrayList("MtCouponAlleviate2Exp",
            "DpCouponAlleviate2Exp");

    public static List<String> EXPRESS_OPTIMIZE_AB_KEYS = Lists.newArrayList("MtExpressOptimizeExp",
            "DpExpressOptimizeExp");

    public static List<String> CREDIT_PAY_AB_KEYS = Lists.newArrayList("MtCreditPayExp");

}
