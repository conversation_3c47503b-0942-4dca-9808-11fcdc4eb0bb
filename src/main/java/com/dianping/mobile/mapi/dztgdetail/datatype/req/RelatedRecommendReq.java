package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.DecryptedField;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <EMAIL>
 * @Date: 2024/10/24
 */
@Data
@TypeDoc(description = "相关推荐入参")
@MobileRequest
public class RelatedRecommendReq implements IMobileRequest, Serializable {
    @Deprecated
    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，原int类型")
    @MobileRequest.Param(name = "dealGroupId")
    private Integer dealGroupId;

    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，string类型")
    @MobileRequest.Param(name = "stringDealGroupId")
    private String stringDealGroupId;

    @FieldDoc(description = "门店id", rule = "美团平台为美团商户ID，点评平台为点评商户ID")
    @MobileRequest.Param(name = "shopIdStr")
    private String shopIdStr;
    @MobileRequest.Param(name = "shopIdStrEncrypt")
    @DecryptedField(targetFieldName = "shopIdStr")
    private String shopIdStrEncrypt;

    @FieldDoc(description = "城市ID", rule = "美团平台为美团城市ID，点评平台为点评城市ID，优先给到首页城市ID（非用户地理位置城市）")
    @MobileRequest.Param(name = "cityId")
    private Integer cityId;

    @FieldDoc(description = "用户经度(注意app和小程序经纬度类型不同)")
    @MobileRequest.Param(name = "userLng")
    private Double userLng;

    @FieldDoc(description = "用户纬度(注意app和小程序经纬度类型不同)")
    @MobileRequest.Param(name = "userLat")
    private Double userLat;

    @FieldDoc(description = "开始位置")
    @MobileRequest.Param(name = "start")
    private Integer start;

    @FieldDoc(description = "数量")
    @MobileRequest.Param(name = "limit")
    private Integer limit;

    @FieldDoc(description = "用户定位城市ID")
    @MobileRequest.Param(name = "gpsCityId")
    private Integer gpsCityId;

    @FieldDoc(description = "实验信息")
    @MobileRequest.Param(name = "expInfo")
    private String expInfo;
}
