package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.cat.Cat;
import com.dianping.credit.cxutil.OutCxutil;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.UnifiedCouponWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.CouponCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedGetCouponReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.coupon.UnifiedGetCouponModule;
import com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.Future;

/**
 * Created by zuomlin on 2018/12/13.
 */
@Component
public class UnifiedCouponFacade {

    @Autowired
    private UnifiedCouponWrapper unifiedCouponWrapper;

    public UnifiedGetCouponModule queryUnifiedGetCouponModule(UnifiedGetCouponReq request, EnvCtx envCtx, IMobileContext iMobileContext) throws Exception {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.UnifiedCouponFacade.queryUnifiedGetCouponModule(UnifiedGetCouponReq,EnvCtx,IMobileContext)");
        CouponCtx couponCtx = initDealCtx(request, envCtx);
        couponCtx.setContext(iMobileContext);
        if (couponCtx.getEnvCtx().isMainWeb()) {
            String cx = OutCxutil.getInstance().getCXstring(iMobileContext.getRequest());
            couponCtx.setCx(cx);
        }


        List<UnifiedCouponDTO> unifiedCouponDTOS = unifiedCouponWrapper.queryIssuedCouponList(envCtx, request.getCouponGroupId());
        if (CollectionUtils.isNotEmpty(unifiedCouponDTOS) && unifiedCouponDTOS.get(0).getCouponGroupDTO() != null) {
            UnifiedGetCouponModule unifiedGetCouponModule = new UnifiedGetCouponModule();
            unifiedGetCouponModule.setSuccess(true);
            unifiedGetCouponModule.setMessage("您已经领过啦");
            return unifiedGetCouponModule;
        }
        Future unifiedCouponFuture = unifiedCouponWrapper.preMerchantCouponIssue(couponCtx);
        String unifiedCouponResult = unifiedCouponWrapper.getMerchantCouponIssue(unifiedCouponFuture);

        return buildUnifiedGetCouponModule(couponCtx, unifiedCouponResult, request.getCouponGroupId());
    }

    private UnifiedGetCouponModule buildUnifiedGetCouponModule(CouponCtx couponCtx, String unifiedResult, String couponGroupId) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.UnifiedCouponFacade.buildUnifiedGetCouponModule(com.dianping.mobile.mapi.dztgdetail.datatype.context.CouponCtx,java.lang.String,java.lang.String)");
        UnifiedGetCouponModule unifiedGetCouponModule = new UnifiedGetCouponModule();
        if (StringUtils.isEmpty(unifiedResult)) {
            unifiedGetCouponModule.setMessage("领券失败");
            return unifiedGetCouponModule;
        }
        unifiedGetCouponModule.setSuccess(true);
        unifiedGetCouponModule.setMessage("领券成功");
        unifiedGetCouponModule.setCouponGroupId(couponGroupId);
        if (couponCtx.getEnvCtx().isMainWeb() || couponCtx.getEnvCtx().isMainWX()) {
            unifiedGetCouponModule.setUrl(couponCtx.getEnvCtx().isMt() ?
                    UrlHelper.getCouponMtWebUrl(unifiedResult) : UrlHelper.getCouponDpWebUrl(unifiedResult));
        } else {
            unifiedGetCouponModule.setUrl(unifiedResult);
        }
        return unifiedGetCouponModule;
    }

    private CouponCtx initDealCtx(UnifiedGetCouponReq request, EnvCtx envCtx) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.UnifiedCouponFacade.initDealCtx(UnifiedGetCouponReq,EnvCtx)");
        CouponCtx couponCtx = new CouponCtx(envCtx);
        if (envCtx.isMt()) {
            couponCtx.setMtId(request.getDealGroupId());
            couponCtx.setMtCityId(request.getCityId());
            couponCtx.setMtShopIdLong(request.getShopIdLong());
        } else {
            couponCtx.setDpCityId(request.getCityId());
            couponCtx.setDpId(request.getDealGroupId());
            couponCtx.setDpShopIdLong(request.getShopIdLong());
        }
        couponCtx.setCouponGroupId(request.getCouponGroupId());
        couponCtx.setCx(request.getCx());
        couponCtx.setRiskToken(request.getRiskToken());
        return couponCtx;
    }
}
