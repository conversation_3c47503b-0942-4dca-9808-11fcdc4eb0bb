package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: <EMAIL>
 * @Date: 2023/10/24
 */
@TypeDoc(description = "保障信息，支持复杂结构")
@MobileDo(id = 0xb25b)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Guarantee implements Serializable {
    @FieldDoc(description = "保障信息名")
    @MobileDo.MobileField(key = 0x451b)
    private String text;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @FieldDoc(description = "保障信息icon信息")
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;

    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    @FieldDoc(description = "保障信息icon信息，宽度")
    @MobileDo.MobileField(key = 0x4864)
    private int iconWidth;

    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    @FieldDoc(description = "保障信息icon信息，高度")
    @MobileDo.MobileField(key = 0x75c3)
    private int iconHeight;

    @FieldDoc(description = "保障信息展示类型，0-纯文本，1-高亮")
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @FieldDoc(description = "不同样式对应的值，比如高亮下发对应颜色值")
    @MobileDo.MobileField(key = 0x1b3a)
    private String style;
}
