package com.dianping.mobile.mapi.dztgdetail.datatype.resp.shop;


import lombok.Data;

/**
 * @Author: zhangyuan103
 * @Date: 2025/6/3
 * @Description: 适用门店页面配置
 */
@Data
public class PageConfig {
    // 订前
    private String dealPageName;
    // 订后
    private String orderPageName;
    // 订后预约
    private String bookingPageName;
    // 订中
    private String inOrderPageName;
    private String magicalTagUrl;
    private String mtPlanId;
    private String dpPlanId;
    // 订后
    private String tips;
    // 订后-预约
    private String afterOrderBookingTips;
    // 订中
    private String inOrderTips;
    // 版本控制信息
    private int version;
    // 一品多态(先囤后订)场景: 在线预订文案
    private String onlineText;
    // 一品多态(先囤后订)场景: 电话预订文案
    private String phoneText;
}
