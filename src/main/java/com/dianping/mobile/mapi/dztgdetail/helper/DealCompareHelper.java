package com.dianping.mobile.mapi.dztgdetail.helper;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ShopPBO;
import com.dianping.mobile.mapi.dztgdetail.util.ObjectCompareUtils;
import com.dianping.shopremote.remote.dto.ShopUuidDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2023/8/21
 * @since mapi-dztgdetail-web
 */
@Slf4j
public class DealCompareHelper {

    public static final String COMPARE_TYPE = "DealCompare";

    public static void compare(DealGroupPBO newResp, DealGroupPBO oldResp) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.helper.DealCompareHelper.compare(DealGroupPBO,DealGroupPBO)");
        try {
            if(newResp == null || oldResp == null) {
                return;
            }

            Cat.logEvent(COMPARE_TYPE, "deal.diff");
            boolean result = differ(newResp, oldResp);
            if(!result) {
                Cat.logEvent(COMPARE_TYPE, "deal.diff.fail");
            }
        } catch (Exception e) {
            Cat.logEvent(COMPARE_TYPE, "deal.diff.error");
            Cat.logError(e);
        }
    }

    private static boolean differ(DealGroupPBO newDeal, DealGroupPBO oldDeal) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.helper.DealCompareHelper.differ(DealGroupPBO,DealGroupPBO)");
        int dealId = oldDeal.getDpId() != 0? oldDeal.getDpId():oldDeal.getMtId();
        try {
            return differBuyBar(newDeal, oldDeal,dealId) &&
            differSaleDesc(newDeal, oldDeal,dealId) &&
            differPrice(newDeal, oldDeal,dealId) &&
            differPriceDesc(newDeal, oldDeal,dealId) &&
            differPriceModel(newDeal, oldDeal,dealId) &&
            differModuleConfig(newDeal, oldDeal,dealId) &&
                    differDpDealId(newDeal, oldDeal, dealId) &&
                    differFeatures(newDeal.getFeatures(), oldDeal.getFeatures(), dealId) &&
                    differShopInfo(newDeal.getShop(), oldDeal.getShop(), dealId);
        } catch (Exception e) {
            log.info("compareDeal:dealId:{}",dealId,e);
            return false;
        }
    }

    private static boolean differBuyBar(DealGroupPBO newDeal, DealGroupPBO oldDeal, int dealId) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.helper.DealCompareHelper.differBuyBar(DealGroupPBO,DealGroupPBO,int)");
        try {
            ObjectCompareUtils.compareObject(newDeal.getBuyBar(), oldDeal.getBuyBar());
        } catch (Exception e) {
            Cat.logEvent(COMPARE_TYPE, "buyBarDiff");
            log.error(String.format("compareBuyBar:dealId:%s,newDeal.BuyBar:%s, oldDeal.BuyBar:%s", dealId, JSONObject.toJSONString(newDeal.getBuyBar()),JSONObject.toJSONString(oldDeal.getBuyBar())));
            return false;
        }
        return true;
    }

    private static boolean differSaleDesc(DealGroupPBO newDeal, DealGroupPBO oldDeal, int dealId) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.helper.DealCompareHelper.differSaleDesc(DealGroupPBO,DealGroupPBO,int)");
        try {
            ObjectCompareUtils.compareString(newDeal.getSaleDescStr(), oldDeal.getSaleDescStr());
        } catch (Exception e) {
            Cat.logEvent(COMPARE_TYPE, "saleDescDiff");
            log.error(String.format("compareSaleDesc:dealId:%s,newDeal.saleDesc:%s, oldDeal.saleDesc:%s", dealId, newDeal.getSaleDescStr(),oldDeal.getSaleDescStr()));
            return false;
        }
        return true;
    }

    private static boolean differDpDealId(DealGroupPBO newDeal, DealGroupPBO oldDeal, int dealId) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.helper.DealCompareHelper.differDpDealId(DealGroupPBO,DealGroupPBO,int)");
        try {
            ObjectCompareUtils.compareString(String.valueOf(newDeal.getDpId()), String.valueOf(oldDeal.getDpId()));
        } catch (Exception e) {
            Cat.logEvent(COMPARE_TYPE, "dpDealIdDiff");
            log.error(String.format("compareDpDealId:dealId:%s,newDeal.dealId:%s, oldDeal.dealid:%s", dealId, newDeal.getDpId(), oldDeal.getDpId()));
            return false;
        }
        return true;
    }

    private static boolean differPrice(DealGroupPBO newDeal, DealGroupPBO oldDeal, int dealId) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.helper.DealCompareHelper.differPrice(DealGroupPBO,DealGroupPBO,int)");
        try {
            ObjectCompareUtils.compareString(newDeal.getDisplayPrice(), oldDeal.getDisplayPrice());
        } catch (Exception e) {
            Cat.logEvent(COMPARE_TYPE, "priceDiff");
            log.error(String.format("comparePrice:dealId:%s,newDeal.Price:%s, oldDeal.Price:%s", dealId, newDeal.getDisplayPrice(),oldDeal.getDisplayPrice()));
            return false;
        }
        return true;
    }

    private static boolean differPriceDesc(DealGroupPBO newDeal, DealGroupPBO oldDeal, int dealId) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.helper.DealCompareHelper.differPriceDesc(DealGroupPBO,DealGroupPBO,int)");
        try {
            ObjectCompareUtils.compareString(newDeal.getDisplayPriceDesc(), oldDeal.getDisplayPriceDesc());
        } catch (Exception e) {
            Cat.logEvent(COMPARE_TYPE, "priceDescDiff");
            log.error(String.format("comparePriceDesc:dealId:%s,newDeal.PriceDesc:%s, oldDeal.PriceDesc:%s", dealId, newDeal.getDisplayPriceDesc(),oldDeal.getDisplayPriceDesc()));
            return false;
        }
        return true;
    }

    private static boolean differPriceModel(DealGroupPBO newDeal, DealGroupPBO oldDeal, int dealId) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.helper.DealCompareHelper.differPriceModel(DealGroupPBO,DealGroupPBO,int)");
        try {
            ObjectCompareUtils.compareObject(newDeal.getPriceDisplayModuleDo(), oldDeal.getPriceDisplayModuleDo());
        } catch (Exception e) {
            Cat.logEvent(COMPARE_TYPE, "priceModuleDiff");
            log.error(String.format("comparePriceModule:dealId:%s,newDeal.PriceModule:%s, oldDeal.PriceModule:%s", dealId, JSONObject.toJSONString(newDeal.getPriceDisplayModuleDo()),JSONObject.toJSONString(oldDeal.getPriceDisplayModuleDo())));
            return false;
        }
        return true;
    }

    private static boolean differModuleConfig(DealGroupPBO newDeal, DealGroupPBO oldDeal, int dealId) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.helper.DealCompareHelper.differModuleConfig(DealGroupPBO,DealGroupPBO,int)");
        try {
            if(newDeal.getModuleConfigsModule() != null && oldDeal.getModuleConfigsModule()!=null) {
                ObjectCompareUtils.compareList(newDeal.getModuleConfigsModule().getModuleConfigs(), oldDeal.getModuleConfigsModule().getModuleConfigs());
                ObjectCompareUtils.compareString(newDeal.getModuleConfigsModule().getKey(), oldDeal.getModuleConfigsModule().getKey());
                ObjectCompareUtils.compareString(newDeal.getModuleConfigsModule().getExtraInfo(), oldDeal.getModuleConfigsModule().getExtraInfo());
            }
        } catch (Exception e) {
            Cat.logEvent(COMPARE_TYPE, "moduleConfigDiff");
            log.error(String.format("compareModuleConfig:dealId:%s,newDeal.compareModuleConfig:%s, oldDeal.compareModuleConfig:%s", dealId, JSONObject.toJSONString(newDeal.getModuleConfigsModule()),JSONObject.toJSONString(oldDeal.getModuleConfigsModule())));
            return false;
        }
        return true;
    }

    public static boolean differDpShopId(long newShopId, long oldShopId, int dealId) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.helper.DealCompareHelper.differDpShopId(long,long,int)");
        try {
            ObjectCompareUtils.compareNum(newShopId, oldShopId);
        } catch (Exception e) {
            Cat.logEvent(COMPARE_TYPE, "dpShopIdDiff");
            log.error(String.format("compareDpShopId:dealId:%s,newDeal.dpShopId:%s, oldDeal.dpShopId:%s", dealId, newShopId,oldShopId));
            return false;
        }
        return true;
    }

    public static boolean differDpCityId(int newCityId, int oldCityId, int dealId) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.helper.DealCompareHelper.differDpCityId(int,int,int)");
        try {
            ObjectCompareUtils.compareNum(newCityId, oldCityId);
        } catch (Exception e) {
            Cat.logEvent(COMPARE_TYPE, "dpCityIdDiff");
            log.error(String.format("compareDpCityId:dealId:%s,newDeal.dpCityId:%s, oldDeal.dpCityId:%s", dealId, newCityId,oldCityId));
            return false;
        }
        return true;
    }

    public static boolean differDpDealId(int newDealId, int oldDealId, int dealId) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.helper.DealCompareHelper.differDpDealId(int,int,int)");
        try {
            ObjectCompareUtils.compareNum(newDealId, oldDealId);
        } catch (Exception e) {
            Cat.logEvent(COMPARE_TYPE, "dpDealIdDiff");
            log.error(String.format("compareDpDealId:dealId:%s,newDeal.dpDealId:%s, oldDeal.dpDealId:%s", dealId, newDealId,oldDealId));
            return false;
        }
        return true;
    }

    public static boolean differShopUUidDTO(ShopUuidDTO newShopUuid, ShopUuidDTO oldShopUuidId, int dealId) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.helper.DealCompareHelper.differShopUUidDTO(com.dianping.shopremote.remote.dto.ShopUuidDTO,com.dianping.shopremote.remote.dto.ShopUuidDTO,int)");
        try {
            ObjectCompareUtils.compareObject(newShopUuid, oldShopUuidId);
        } catch (Exception e) {
            Cat.logEvent(COMPARE_TYPE, "dpShopUuidDiff");
            log.error(String.format("compareDpShopUuid:dealId:%s,newDeal.ShopUuid:%s, oldDeal.ShopUuid:%s", dealId, newShopUuid,oldShopUuidId));
            return false;
        }
        return true;
    }

    public static boolean differFeatures(List<String> newFeatures, List<String> oldFeatures, int dealId) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.helper.DealCompareHelper.differFeatures(java.util.List,java.util.List,int)");
        try {
            ObjectCompareUtils.compareList(newFeatures, oldFeatures);
        } catch (Exception e) {
            Cat.logEvent(COMPARE_TYPE, "featuresDiff");
            log.error(String.format("compareFeatures:dealId:%s,newDeal.Features:%s, oldDeal.Features:%s", dealId, newFeatures,oldFeatures));
            return false;
        }
        return true;
    }

    public static boolean differShopInfo(ShopPBO newShopInfo, ShopPBO oldShopInfo, int dealId) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.helper.DealCompareHelper.differShopInfo(ShopPBO,ShopPBO,int)");
        try {
            ObjectCompareUtils.compareObject(newShopInfo, oldShopInfo);
        } catch (Exception e) {
            Cat.logEvent(COMPARE_TYPE, "ShopInfoDiff");
            log.error(String.format("compareShopInfo:dealId:%s,newDeal.ShopInfo:%s, oldDeal.ShoInfo:%s", dealId, JSONObject.toJSONString(newShopInfo), JSONObject.toJSONString(oldShopInfo)));
            return false;
        }
        return true;
    }

}
