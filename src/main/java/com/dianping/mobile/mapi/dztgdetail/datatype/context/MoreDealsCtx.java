package com.dianping.mobile.mapi.dztgdetail.datatype.context;

import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import lombok.Data;


/**
 * 查询团单信息的环境变量
 */
@Data
public class MoreDealsCtx {

    public MoreDealsCtx(EnvCtx ctx) {
        if (ctx != null) {
            this.envCtx = ctx;
        }
    }

    private EnvCtx envCtx = new EnvCtx();
    private IMobileContext context;

    private int dpId; //点评团单ID
    private int mtId; //美团团单ID
    private DealGroupChannelDTO channel;

    private int dpCityId;
    private int mtCityId;

    @Deprecated
    private int mtShopId;
    private long mtShopIdLong;
    @Deprecated
    private int dpShopId;
    private long dpShopIdLong;
    private String token;

    private double lat;
    private double lng;

    public boolean isMt() {
        return this.envCtx.isMt();
    }

}
