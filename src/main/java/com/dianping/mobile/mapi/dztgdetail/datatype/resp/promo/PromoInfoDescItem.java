package com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * Created by <PERSON><PERSON><PERSON> on 2018/12/13.
 */
@TypeDoc(description = "优惠信息描述项")
@MobileDo(id = 0x8961)
@Data
@Accessors(chain = true)
public class PromoInfoDescItem implements Serializable {

    @FieldDoc(description = "样式类型，0：普通文案型(有textJL取textJL，否则取text) 1：标签型（取text，以标签样式展示）")
    @MobileField(key = 0x1b3a)
    private int style;

    @FieldDoc(description = "描述文案，e.g. 新客下单减10元，每人可享1次")
    @MobileField(key = 0x451b)
    private String text;

}
