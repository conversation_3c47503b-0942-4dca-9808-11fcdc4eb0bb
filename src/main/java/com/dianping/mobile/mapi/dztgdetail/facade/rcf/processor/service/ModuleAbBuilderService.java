package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.DealCategoryFactory;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.DealDouHuUtil;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.DealRepurchaseConfig;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.TimesDealUtil;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.MIGRATE_CATEGORY;

/**
 * @Author: <EMAIL>
 * @Date: 2024/6/8
 */
@Component
@Slf4j
public class ModuleAbBuilderService {
    @Resource
    private DealCategoryFactory dealCategoryFactory;
    @Autowired
    private DouHuBiz douHuBiz;
    @Resource
    private DouHuService douHuService;

    public List<ModuleAbConfig> getModuleAbConfigs(DealCtx ctx) {
        List<ModuleAbConfig> moduleAbConfigs = ctx.getModuleAbConfigs();
        if (moduleAbConfigs == null) {
            moduleAbConfigs = new ArrayList<>();
        }
        if (ctx.getCategoryId() == 303) {
            ModuleAbConfig abConfig = getZuliaoAbConfig(ctx);
            if (abConfig != null) {
                moduleAbConfigs.add(abConfig);
            }
        }

        ModuleAbConfig migrateAbConfig = getMigrateAbConfig(ctx);
        if (migrateAbConfig != null) {
            moduleAbConfigs.add(migrateAbConfig);
        }

        ModuleAbConfig shoppingCartAbConfig = getShoppingCartAbConfig(ctx);
        if (shoppingCartAbConfig != null) {
            moduleAbConfigs.add(shoppingCartAbConfig);
        }

        ModuleAbConfig shoppingCartNewAbConfig = getShoppingCartNewAbConfig(ctx);
        if (shoppingCartNewAbConfig != null) {
            moduleAbConfigs.add(shoppingCartNewAbConfig);
        }

        ModuleAbConfig couponBarConfig = getCouponBarAbcConfig(ctx);
        if (couponBarConfig != null) {
            moduleAbConfigs.add(couponBarConfig);
        }

        ModuleAbConfig petStyleConfig = getPetNewStyleAbConfig(ctx);
        if (petStyleConfig != null) {
            moduleAbConfigs.add(petStyleConfig);
        }

        ModuleAbConfig cardStyleAbConfig = ctx.getCardStyleAbConfig();
        if (cardStyleAbConfig != null) {
            moduleAbConfigs.add(cardStyleAbConfig);
        }

        ModuleAbConfig cardStyleAbV2Config = ctx.getCardStyleAbV2Config();
        if (Objects.nonNull(cardStyleAbV2Config)) {
            moduleAbConfigs.add(cardStyleAbV2Config);
        }

        ModuleAbConfig overNightAbConfig = DealDouHuUtil.getOverNightDouHuSk(ctx);
        if (Objects.nonNull(overNightAbConfig)) {
            moduleAbConfigs.add(overNightAbConfig);
        }
        ModuleAbConfig comparePriceAbConfig = douHuService.getAbResultForComparePriceAssistant(ctx);
        if (Objects.nonNull(comparePriceAbConfig)) {
            moduleAbConfigs.add(comparePriceAbConfig);
        }
        ModuleAbConfig hotNailModuleAbConfig = getHotNailModuleAbConfig(ctx);
        if (Objects.nonNull(hotNailModuleAbConfig)) {
            moduleAbConfigs.add(hotNailModuleAbConfig);
        }
        ModuleAbConfig moduleAbConfig = dealCategoryFactory.getModuleAbConfig(ctx.getEnvCtx(), (long) ctx.getCategoryId());
        if (moduleAbConfig != null){
            moduleAbConfigs.add(moduleAbConfig);
        }
        if (Objects.nonNull(ctx.getShowReservationAbConfig())) {
            moduleAbConfigs.add(ctx.getShowReservationAbConfig());
        }
        ModuleAbConfig repurchaseShelfAbConfig = getRepurchaseShelfAbConfig(ctx);
        if (Objects.nonNull(repurchaseShelfAbConfig)){
            moduleAbConfigs.add(repurchaseShelfAbConfig);

        }
        ModuleAbConfig similarDealModuleAbConfig = getSimilarDealModuleAbConfig(ctx.getEnvCtx(), (long) ctx.getCategoryId());
        if (similarDealModuleAbConfig != null){
            moduleAbConfigs.add(similarDealModuleAbConfig);
        }
        ModuleAbConfig sameShopPriceStyle = getCompareSameShopPriceStyleAbConfig(ctx);
        if (Objects.nonNull(sameShopPriceStyle)) {
            moduleAbConfigs.add(sameShopPriceStyle);
        }
        ModuleAbConfig glassGlassDealDetailAbConfig = getGlassGlassDealDetailAbConfig(ctx);
        if (glassGlassDealDetailAbConfig != null){
            moduleAbConfigs.add(glassGlassDealDetailAbConfig);
        }

        ModuleAbConfig tattooGuideAbConfig = getTattooGuideAbTestConfig(ctx);
        if (Objects.nonNull(tattooGuideAbConfig)) {
            moduleAbConfigs.add(tattooGuideAbConfig);
        }
        ModuleAbConfig timesDealAbConfig = getTimesDealAbConfig(ctx);
        if (Objects.nonNull(timesDealAbConfig)) {
            moduleAbConfigs.add(timesDealAbConfig);
        }

        ModuleAbConfig glassesExhibitAbConfig = ctx.getGlassesExhibitAbConfig();
        if (Objects.nonNull(glassesExhibitAbConfig)) {
            moduleAbConfigs.add(glassesExhibitAbConfig);
        }

        ModuleAbConfig couponAlleviate1AbConfig = getAbConfig(ctx, "MtCouponAlleviate1Exp", "DpCouponAlleviate1Exp");
        if (Objects.nonNull(couponAlleviate1AbConfig)) {
            moduleAbConfigs.add(couponAlleviate1AbConfig);
        }

        // 团购次卡C端表达优化
        ModuleAbConfig expressOptimizeAbConfig = getAbConfig(ctx, "MtExpressOptimizeExp", "DpExpressOptimizeExp");
        if (Objects.nonNull(expressOptimizeAbConfig)) {
            moduleAbConfigs.add(expressOptimizeAbConfig);
        }

        // RCF快照
        ModuleAbConfig nativeDealDetailAbConfig = douHuService.getNativeDealDetailAbTestResult(ctx.getEnvCtx());
        if (Objects.nonNull(nativeDealDetailAbConfig)) {
            moduleAbConfigs.add(nativeDealDetailAbConfig);
        }
        // 维修二段支付AB实验
        ModuleAbConfig repairPayAbConfig = douHuService.getRepairPayAbTestResult(ctx.getEnvCtx());
        if (Objects.nonNull(repairPayAbConfig)) {
            moduleAbConfigs.add(repairPayAbConfig);
        }
        // 神券感知强化
        ModuleAbConfig magicCouponEnhancementAbConfig = douHuService.getMagicCouponEnhancementAbTestResult(ctx.getEnvCtx());
        if (ctx.isHasSuperCouponScene() && Objects.nonNull(magicCouponEnhancementAbConfig)) {
            moduleAbConfigs.add(magicCouponEnhancementAbConfig);
        }
        // 顶部搜索框AB实验
        ModuleAbConfig searchNavBarAbConfig = douHuService.getNavbarSearchAbTestResult(ctx.getEnvCtx());
        if (Objects.nonNull(searchNavBarAbConfig)) {
            moduleAbConfigs.add(searchNavBarAbConfig);
        }
        return moduleAbConfigs;
    }

    private ModuleAbConfig getAbConfig(DealCtx ctx, String mtKey, String dpKey) {
        return douHuBiz.getAbExpResult(ctx, ctx.isMt() ? mtKey : dpKey);
    }

    // 足疗货架团购按钮直跳下单页ab测试
    private ModuleAbConfig getZuliaoAbConfig(DealCtx ctx) {
        String module = ctx.isMt() ? "MTZuLiaoHuoJiaDirectPurchase" : "DPZuLiaoHuoJiaDirectPurchase";
        return douHuBiz.getAbByUnionId(ctx.getEnvCtx().getUnionId(), module, ctx.isMt());
    }


    // 页面迁移实验
    private ModuleAbConfig getMigrateAbConfig(DealCtx ctx) {
        List<Integer> categoryIds = Lion.getList(MdpContextUtils.getAppKey(), MIGRATE_CATEGORY, Integer.class);
        if (!ctx.getEnvCtx().judgeMainApp()) {
            return null;
        }
        if (!categoryIds.contains(ctx.getCategoryId())) {
            return null;
        }
        String module = String.format("migrate_%s_%d", ctx.isMt() ? "mt" : "dp", ctx.getCategoryId());
        return douHuBiz.getAbByUnionId(ctx.getEnvCtx().getUnionId(), module, ctx.isMt());
    }

    private ModuleAbConfig getShoppingCartAbConfig(DealCtx ctx) {
        return douHuBiz.getAbByUnionId(ctx.getEnvCtx().getUnionId(), "MTShoppingCartBuyBar", ctx.isMt());
    }

    private ModuleAbConfig getShoppingCartNewAbConfig(DealCtx ctx) {
        return douHuBiz.getAbByUnionId(ctx.getEnvCtx().getUnionId(), "MTShoppingCartBuyBarNew", ctx.isMt());
    }

    private ModuleAbConfig getCouponBarAbcConfig(DealCtx ctx) {
        String module = ctx.isMt() ? "MTCouponBar" : "DPCouponBar";
        return douHuBiz.getAbcByUnionId(ctx.getEnvCtx().getUnionId(), module, ctx.isMt());
    }

    private ModuleAbConfig getPetNewStyleAbConfig(DealCtx ctx) {
        if (ctx.getCategoryId() == 1701 || ctx.getCategoryId() == 1702){
            String module = ctx.isMt() ? "MTPetNewStyle" : "DPPetNewStyle";
            return douHuBiz.getAbcByUnionId(ctx.getEnvCtx().getUnionId(), module, ctx.isMt());
        }
        return null;
    }

    /**
     * 获取热门款式模块Ab实验结果
     * @param ctx 团单上下文
     * @return AB实验结果
     */
    public ModuleAbConfig getHotNailModuleAbConfig(DealCtx ctx) {
        EnvCtx envCtx = ctx.getEnvCtx();
        long shopIdL = envCtx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId();
        if (LionConfigUtils.isHotNailModuleBlackShop(shopIdL, envCtx.isMt())) {
            return null;
        }
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        if (Objects.isNull(dealGroupDTO)) {
            return null;
        }
        DealGroupCategoryDTO category = dealGroupDTO.getCategory();
        if (Objects.isNull(category)) {
            return null;
        }
        // 判断团单categoryId是否在展示类目中
        int categoryId = Optional.ofNullable(category.getCategoryId()).map(Long::intValue).orElse(0);
        if (!LionConfigUtils.allowDisplayHotNailModule(categoryId, category.getServiceType())) {
            return null;
        }
        // 热门款式AB实验上报
        String module = ctx.isMt() ? "MtHotNailModule" : "DpHotNailModule";
        return douHuBiz.getAbByUnionId(ctx.getEnvCtx().getUnionId(), module, ctx.isMt());
    }

    private ModuleAbConfig getRepurchaseShelfAbConfig(DealCtx ctx) {
        if (!enableRepurchase(ctx.getCategoryId())) {
            return null;
        }
        // 综团比价AB实验上报
        String module = ctx.isMt() ? "MtRepurchaseShelfExp" : "DpRepurchaseShelfExp";;
        if (ctx.isMt()){
            return douHuBiz.getAbByUuId(ctx.getEnvCtx().getUuid(), module, ctx.isMt());
        }else {
            return douHuBiz.getAbExpResultByUuidAndDpid(ctx, module);
        }
    }

    public ModuleAbConfig getSimilarDealModuleAbConfig(EnvCtx envCtx, long categoryId){
        return dealCategoryFactory.getSimilarDealModuleAbConfig(envCtx, categoryId);
    }

    // 同店比货模块AB实验
    private ModuleAbConfig getCompareSameShopPriceStyleAbConfig(DealCtx ctx) {
        return douHuService.getCompareSameShopPriceStyleAbConfigByDealCtx(ctx);
    }

    private ModuleAbConfig getGlassGlassDealDetailAbConfig(DealCtx ctx) {
        if(ctx.getCategoryId() != 406){
            return null;
        }
        String module = ctx.isMt() ? "MtGlassDealDetailExp" : "DpGlassDealDetailExp";;
        return douHuBiz.getAbByCityIdAndUuidAndDpId(ctx.getCityId4P(), ctx, module);
    }

    private ModuleAbConfig getTattooGuideAbTestConfig(DealCtx ctx){
        if (512 != ctx.getCategoryId()){
            return null;
        }
        String unionId = ctx.getEnvCtx().getUnionId();
        boolean isMt = ctx.isMt();
        Map<String, String> lionMap = Lion.getMap(LionConstants.APP_KEY,
                LionConstants.DOUHU_MODULE_EXP_CONFIG, String.class, Collections.emptyMap());
        String moduleName = isMt ? "MtBeautyTattooGuide" : "DpBeautyTattooGuide";
        String expId = lionMap.get(moduleName);
        if (org.apache.commons.lang3.StringUtils.isBlank(expId)) {
            return null;
        }
        return douHuBiz.getAbByUnionIdAndExpId(unionId, expId, moduleName, isMt);
    }

    private boolean enableRepurchase(int categoryId){
        DealRepurchaseConfig repurchaseConfig = LionConfigUtils.getRepurchaseConfig();
        if (Objects.nonNull(repurchaseConfig) && CollectionUtils.isNotEmpty(repurchaseConfig.getCategoryIds())){
            List<Integer> categoryIds = repurchaseConfig.getCategoryIds();
            return categoryIds.contains(categoryId);
        }
        return Boolean.FALSE;
    }

    private ModuleAbConfig getTimesDealAbConfig(DealCtx ctx) {
        if (!TimesDealUtil.isTimesDeal(ctx.getDealGroupDTO())) {
            return null;
        }
        return dealCategoryFactory.getTimesDealModuleAbConfig(ctx.getEnvCtx(), (long) ctx.getCategoryId());
    }
}
