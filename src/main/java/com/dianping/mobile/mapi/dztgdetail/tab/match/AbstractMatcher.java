package com.dianping.mobile.mapi.dztgdetail.tab.match;

import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

public class AbstractMatcher<T> implements Matcher<T> {
    @Override
    public boolean match(List<T> a, List<T> b) {
        if (CollectionUtils.isEmpty(a) || CollectionUtils.isEmpty(b)) {
            return false;
        }

        List<T> intercourse = a.stream().filter(b::contains).collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(intercourse);
    }
}
