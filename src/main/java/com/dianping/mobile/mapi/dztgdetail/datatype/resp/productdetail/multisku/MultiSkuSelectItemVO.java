package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.multisku;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <EMAIL>
 * @Date: 2025/2/25
 */
@Data
@TypeDoc(description = "多sku选择列表项")
@MobileDo(id = 0xb7a)
public class MultiSkuSelectItemVO implements Serializable {
    @FieldDoc(description = "商品ID")
    @MobileDo.MobileField(key = 0x9724)
    private long productId;

    @FieldDoc(description = "skuId")
    @MobileDo.MobileField(key = 0xf59e)
    private long skuId;

    @FieldDoc(description = "sku商品类型，1-可用时段sku")
    @MobileDo.MobileField(key = 0x696c)
    private int skuType;

    @FieldDoc(description = "标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "多sku富文本信息")
    @MobileDo.MobileField(key = 0xae0c)
    private MultiSkuSelectRichTextVO multiSkuSelectRichTextVO;

    @FieldDoc(description = "是否选中")
    @MobileDo.MobileField(key = 0xb59e)
    private boolean selected;
}
