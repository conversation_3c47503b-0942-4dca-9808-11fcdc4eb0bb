package com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created by wa<PERSON><PERSON><PERSON> on 16/4/20.
 */
@MobileDo(id = 0x6ed9)
@Data
public class MtPriceCalendar implements Serializable {

    @MobileField(key = 0x91b)
    private int id;
    @MobileField
    private Date startTime;
    @MobileField
    private Date endTime;
    @MobileField
    private String desc;
    @MobileField
    private double price;
    @MobileField
    private double canBuyPrice;
    @MobileField
    private List<String> Range;
    @MobileField
    private int dealId;
    @MobileField
    private double buyPrice;
    @MobileField
    private int type;

}
