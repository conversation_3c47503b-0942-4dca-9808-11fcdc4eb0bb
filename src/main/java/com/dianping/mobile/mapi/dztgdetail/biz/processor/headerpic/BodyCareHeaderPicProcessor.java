package com.dianping.mobile.mapi.dztgdetail.biz.processor.headerpic;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ImageScaleEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * @Author: zheng<PERSON><EMAIL>
 * @Date: 2023/9/23
 */
@Component("bodycareHeaderPicProcessor")
@Slf4j
public class BodyCareHeaderPicProcessor extends AbstractHeaderPicProcessor {

    @Override
    public void fillPicScale(DealCtx ctx, List<ContentPBO> result, DealGroupPBO dealGroupPBO) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }

        // 如果团单不是指定品类，则直接返回
        if (!LionConfigUtils.hasCustomHeaderPic("beauty", ctx.getCategoryId())) {
            return;
        }

        ContentPBO contentPBO = result.stream().filter(c -> StringUtils.isNotBlank(c.getVideoUrl())).findFirst().orElse(null);
        boolean hasVideo = Objects.nonNull(contentPBO) ? true : false;

        if (hasVideo) {
            result.forEach(c -> c.setScale(contentPBO.getScale()));
            return;
        }

        result.forEach(c -> c.setScale(ImageScaleEnum.SIXTEEN_TO_NINE.getScale()));
        return;
    }

    @Override
    public boolean matchShowExhibit(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.headerpic.BodyCareHeaderPicProcessor.matchShowExhibit(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        return false;
    }
}
