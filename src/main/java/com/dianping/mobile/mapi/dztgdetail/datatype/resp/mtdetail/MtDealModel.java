package com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created by wa<PERSON><PERSON><PERSON> on 16/3/17.
 */
@MobileDo(name = "MTDealBase")
@Data
public class MtDealModel implements Serializable {
    @MobileField(key = 0x93b)
    private int id;
    @MobileField(key = 0x7b7b)
    private String stid;
    @MobileField(key = 0x9ba7)
    private String channel;
    @MobileField(key = 0x5eec)
    private String slug;
    @MobileField(key = 0x8b0)
    private int dt;
    @MobileField(key = 0xef70)
    private String cate;
    @MobileField(key = 0xd65e)
    private String subCate;
    @MobileField(key = 0x13e5)
    private List<Integer> frontPoiCates;
    @MobileField(key = 0xd2f6)
    private boolean lightning;
    @MobileField(key = 0xb61b)
    private int ctype;
    @MobileField(key = 0x7610)
    private String brandName;
    @MobileField(key = 0xb13a)
    private int solds;
    @MobileField(key = 0x22b2)
    private String soldStr;
    @MobileField(key = 0x2820)
    private int status;
    @MobileField(key = 0xd0b6)
    private int state;
    @MobileField(key = 0x1c19)
    private Date couponBeginTime;
    @MobileField(key = 0x9df0)
    private Date couponEndTime;
    @MobileField(key = 0x52a8)
    private Date start;
    @MobileField(key = 0x144)
    private Date end;
    @MobileField(key = 0x640d)
    private String imgurl;
    @MobileField(key = 0x7c03)
    private double originalPrice;
    @MobileField(key = 0x24ae)
    private String squareimgurl;
    @MobileField(key = 0x36e9)
    private String title;
    @MobileField(key = 0xc3d2)
    private String mtitle;
    @MobileField(key = 0xb588)
    private String smstitle;
    @MobileField(key = 0x82bc)
    private String announcementTitle;
    @MobileField(key = 0xe065)
    private String coupontitle;
    @MobileField(key = 0xc5b5)
    private double price;
    @MobileField(key = 0x11a1)
    private double deposit;
    @MobileField(key = 0x1f2e)
    private List<MtPriceCalendar> priceCalendars;
    @MobileField(key = 0x4dac)
    private String range;
    @MobileField(key = 0x5ff3)
    private int appleLottery;
    @MobileField(key = 0x1534)
    private String mealcount;
    @MobileField(key = 0x79b7)
    private int nobooking;
    @MobileField(key = 0xc65a)
    private int festcanuse;
    @Deprecated
    @MobileField(key = 0x32fe)
    private List<Integer> poiids;
    @MobileField(key = 0xce0a)
    private List<String> poiIdsStr;
    @MobileField(key = 0x477b)
    private String tag;
    @MobileField(key = 0x8828)
    private String mname;
    @MobileField(key = 0xc626)
    private String murl;
    @MobileField(key = 0x7c44)
    private String iUrl;
    @MobileField(key = 0xc77f)
    private String tips;
    @MobileField(key = 0x5da8)
    private String terms;
    @MobileField(key = 0xfb31)
    private String menuDigest;
    @MobileField(key = 0x207)
    private String howuse;
    @MobileField(key = 0x994)
    private String notice;
    @MobileField(key = 0x2937)
    private String pitchHtml;
    @MobileField(key = 0x14dc)
    private String ktvPlan;
    @MobileField(key = 0x945f)
    private String showType;
    @MobileField(key = 0x2747)
    private double canbuyprice;
    @MobileField(key = 0x92b9)
    private String bookingphone;
    @MobileField(key = 0xc30e)
    private String bookinginfo;
    @MobileField(key = 0x6fa3)
    private int dealType;
    @MobileField(key = 0x2266)
    private String hotelroomname;
    @MobileField(key = 0x6001)
    private boolean isappointonline;
    @MobileField(key = 0x599a)
    private int rdcount;
    @MobileField(key = 0xa3a2)
    private String mlls;
    @MobileField(key = 0x93c7)
    private List<MtPromotionInfo> mtPromotionInfos;
    @MobileField(key = 0x77a0)
    private double campaignPrice;
    @MobileField(key = 0x3f4e)
    private String campaignStrategy;
    @MobileField(key = 0x1f8)
    private MtRatingModel ratingModel;
    @MobileField(key = 0x4c3b)
    private List<MtSecurityInfoItem> securityInfoItems;
    @MobileField(key = 0x66c9)
    private List<MtIcon> icons;
    @MobileField(key = 0x20a5)
    private String optionalAttrs;
    @MobileField(key = 0xf25)
    private boolean availableToday;
    @MobileField(key = 0x73be)
    private MtOptionalAttrs mtOptionalAttrs;
    @MobileField(key = 0x5011)
    private String BranchLocations;
    private int refund;

}
