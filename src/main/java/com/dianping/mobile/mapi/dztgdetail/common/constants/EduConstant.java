package com.dianping.mobile.mapi.dztgdetail.common.constants;


import com.google.common.collect.Lists;

import java.util.List;

public class EduConstant {
    public static final String ATTR_KEY_COURSE_TRIAL = "course_trial";

    /**
     * 线上教育-横幅文案
     */
    public static final String ONLINE_COURSE_BUTTON_BANNER = "点击下方“在线试听”可以免费试听%s课时哦～";

    /**
     * 线上教育-试听课的跳转链接
     */
    public static final String dpTrialClassJumpUrlTemplate = "dianping://mrn?mrn_biz=gcbu&mrn_entry=mrn-le-edutrialcourse&mrn_component=mrn-le-edutrialcourse&dealid=%s&shopid=%s&shopuuid=%s";
    public static final String mtTrialClassJumpUrlTemplate = "imeituan://www.meituan.com/mrn?mrn_biz=gcbu&mrn_entry=mrn-le-edutrialcourse&mrn_component=mrn-le-edutrialcourse&dealid=%s&shopid=%s";

    /**
     * 线上教育-横幅文案颜色
     */
    public static final String EDU_ONLINE_BANNER_TEXT_COLOR = "#FF4B10";

    /**
     * 线上教育-横幅背景颜色
     */
    public static final List<String> EDU_ONLINE_BANNER_BACKGROUND_COLOR = Lists.newArrayList("#FFF1EC", "#FFF1EC");

    /**
     * 0元规划
     */
    public static final List<Long> SERVICE_ZERO_PLAN_TYPE_ID_LIST = Lists.newArrayList(0L, 123034L);

    /**
     * 集训营和集训营
     */
    public static final List<Long> SERVICE_COURSE_OR_CAMP_TYPE_ID_LIST = Lists.newArrayList(0L, 136025L);
    /**
     * 职业教育-考公考编
     */
    public static final List<Long> EXAM_CIVIL_SERVICE_TYPE_ID_LIST = Lists.newArrayList(139004L,134023L);

    /**
     * 寄宿
     */
    public static final List<Long> SERVICE_DORM_TYPE_ID_LIST = Lists.newArrayList(0L, 128025L);

    /**
     * 正价课、集训营按钮上方文案前半段
     */
    public static final String VOCATIONAL_COURSE_BUTTON_BANNER_PREFIX = "点击下方“预约试听”免费试听%s课时";


    /**
     * 正价课、集训营按钮上方文案后半段
     */
    public static final String VOCATIONAL_COURSE_BUTTON_BANNER_SUFFiX = "，已有%s人预约~";

    public static final String SHORT_CLASS_BUTTON_BANNER_PREFIX = "点击下方“预约试听”免费到店试听";

}