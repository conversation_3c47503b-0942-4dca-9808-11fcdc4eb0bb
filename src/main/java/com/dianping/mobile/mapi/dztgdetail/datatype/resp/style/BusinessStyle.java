package com.dianping.mobile.mapi.dztgdetail.datatype.resp.style;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-11-16
 * @desc 业务场景样式
 */
@TypeDoc(description = "业务场景样式")
@Data
@MobileDo(id = 0x2135)
public class BusinessStyle implements Serializable {
    /**
     * 样式名称
     */
    @MobileDo.MobileField(key = 0xa04b)
    private String styleName;

    public BusinessStyle(String styleName) {
        this.styleName = styleName;
    }
}
