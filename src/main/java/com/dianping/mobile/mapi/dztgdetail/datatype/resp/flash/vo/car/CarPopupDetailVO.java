package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.car;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.CommonAttrsVO;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/6 5:44 下午
 */
@MobileDo(id = 0x7afe)
public class CarPopupDetailVO implements Serializable {
    /**
     * 弹窗参数信息
     */
    @MobileDo.MobileField(key = 0x96aa)
    private List<CarParamVO> paramInfo;

    /**
     * 弹窗流程
     */
    @MobileDo.MobileField(key = 0x11e)
    private CommonAttrsVO flow;

    /**
     * 弹窗属性列表
     */
    @MobileDo.MobileField(key = 0x612f)
    private List<CommonAttrsVO> attrs;

    /**
     * 标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * 描述
     */
    @MobileDo.MobileField(key = 0xfebf)
    private String desc;

    public List<CarParamVO> getParamInfo() {
        return paramInfo;
    }

    public void setParamInfo(List<CarParamVO> paramInfo) {
        this.paramInfo = paramInfo;
    }

    public CommonAttrsVO getFlow() {
        return flow;
    }

    public void setFlow(CommonAttrsVO flow) {
        this.flow = flow;
    }

    public List<CommonAttrsVO> getAttrs() {
        return attrs;
    }

    public void setAttrs(List<CommonAttrsVO> attrs) {
        this.attrs = attrs;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}