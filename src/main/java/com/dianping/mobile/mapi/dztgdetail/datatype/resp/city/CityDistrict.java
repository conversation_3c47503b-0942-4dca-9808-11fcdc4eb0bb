package com.dianping.mobile.mapi.dztgdetail.datatype.resp.city;

import lombok.Data;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;

import java.io.Serializable;

/**
 * 城市区县
 */
@Data
public class CityDistrict implements Serializable {
    /**
     * 城市名
     */
    @MobileField
    private String cityName;

    /**
     * 城市id
     */
    @MobileField
    private int cityId;

    /**
     * 首字母
     */
    @MobileField
    private String firstLetter;
}
