package com.dianping.mobile.mapi.dztgdetail.datatype.context;

import lombok.Data;

/**
 * 查询团单信息的环境变量
 */
@Data
public class ShopReviewCtx {

    public ShopReviewCtx(EnvCtx ctx) {
        if (ctx != null) {
            this.envCtx = ctx;
        }
    }

    private EnvCtx envCtx = new EnvCtx();
    private FutureCtx futureCtx = new FutureCtx();

    private int dpId; //点评团单ID
    private int mtId; //美团团单ID

    private int dpCityId;
    private int mtCityId;

    @Deprecated
    private int mtShopId;
    private long mtLongShopId;
    @Deprecated
    private int dpShopId;

    private long dpLongShopId;
    private String dpShopUuid;
    private String token;

    public boolean isMt() {
        return this.envCtx.isMt();
    }

    public int getDpShopId() {
        return dpShopId;
    }

    @Deprecated
    public void setDpShopId(int dpShopId) {
        this.dpShopId = dpShopId;
    }

    public long getDpLongShopId() {
        if(dpLongShopId != 0) {
            return dpLongShopId;
        } else if(dpShopId != 0) {
            return dpShopId;
        }
        return dpLongShopId;
    }

    public void setDpLongShopId(long dpLongShopId) {
        this.dpLongShopId = dpLongShopId;
    }

}
