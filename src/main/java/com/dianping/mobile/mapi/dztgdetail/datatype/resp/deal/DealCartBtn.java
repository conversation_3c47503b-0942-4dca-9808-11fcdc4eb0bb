package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/12/13
 */
@TypeDoc(description = "会员价加购物车")
@MobileDo(id = 0x9a19)
@Data
public class DealCartBtn implements Serializable {
    @FieldDoc(description = "会员价信息")
    @MobileDo.MobileField(key = 0xfe42)
    private DealCartMemberPrice memberPriceInfo;
}
