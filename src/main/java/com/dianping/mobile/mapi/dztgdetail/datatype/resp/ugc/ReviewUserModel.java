package com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/12/13.
 */
@TypeDoc(description = "用户信息")
@MobileDo(id = 0xbd91)
@Data
public class ReviewUserModel implements Serializable {

    @FieldDoc(description = "用户ID")
    @MobileField(key = 0x6dfc)
    private long userIdL;

    @FieldDoc(description = "用户等级")
    @MobileField(key = 0x7553)
    private String userLevel;

    @FieldDoc(description = "用户头像")
    @MobileField(key = 0x6d6b)
    private String avatar;

    @FieldDoc(description = "用户名字")
    @MobileField(key = 0xcec)
    private String userName;

    @FieldDoc(description = "用户跳转地址")
    @MobileField(key = 0x8d3b)
    private String detailUrl;

    @FieldDoc(description = "VIP图标")
    @MobileField(key = 0x39af)
    private String vipIcon;
}
