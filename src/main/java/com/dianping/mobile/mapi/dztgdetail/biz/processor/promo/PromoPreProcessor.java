package com.dianping.mobile.mapi.dztgdetail.biz.processor.promo;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.Future;

public class PromoPreProcessor extends AbsDealProcessor {

    @Autowired
    private DealGroupWrapper dealGroupWrapper;

    @Autowired
    private MapperWrapper mapperWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        return true;
    }

    @Override
    public void prepare(DealCtx ctx) {
        if (ctx.getEnvCtx().isMt() && ctx.getMtId() > 0) {
            Future dealIdMapperFuture = dealGroupWrapper.preDpDealGroupId(ctx.getMtId());
            Future cityIdMapperFuture = mapperWrapper.preDpCityByMtCity(ctx.getMtCityId());

            ctx.setDpId(dealGroupWrapper.getDpDealGroupId(dealIdMapperFuture));
            ctx.setDpCityId(mapperWrapper.getDpCityByMtCity(cityIdMapperFuture));
        }
        ctx.getFutureCtx().setDealGroupFuture(dealGroupWrapper.preDealGroupBase(ctx.getDpId()));
        ctx.getFutureCtx().setChannelFuture(dealGroupWrapper.preDealGroupChannelById(ctx.getDpId()));
    }

    @Override
    public void process(DealCtx ctx) {
        ctx.setDealGroupBase(dealGroupWrapper.getFutureResult(ctx.getFutureCtx().getDealGroupFuture()));
        ctx.setChannelDTO(dealGroupWrapper.getFutureResult(ctx.getFutureCtx().getChannelFuture()));
    }
}
