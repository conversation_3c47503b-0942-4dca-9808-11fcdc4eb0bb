package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ShopTagWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.LEInsuranceAgreementEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.google.common.collect.Lists;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Future;

@Slf4j
public class LEInsuranceAgreementProcessor extends AbsDealProcessor {

    @Resource
    private ShopTagWrapper shopTagWrapper;

    private static final String BIZ_CODE = "DP_DAOZONG_DEAL_DETAIL";

    @Override
    public boolean isEnable(DealCtx ctx) {
        if (ctx.getDealGroupDTO() == null || ctx.getDealGroupDTO().getCategory() == null || ctx.getDealGroupDTO().getCategory().getServiceTypeId() == null) {
            return false;
        }
        List<Long> targetServiceTypeIds = Lion.getList(Environment.getAppName(), LionConstants.LE_INSURANCE_AGREEMENT_CATEGORY, Long.class, Lists.newArrayList(650L, 311L, 312L, 313L, 671L));
        Long serviceTypeId = ctx.getDealGroupDTO().getCategory().getServiceTypeId();
        return targetServiceTypeIds.contains(serviceTypeId);
    }

    @Override
    public void prepare(DealCtx ctx) {
        Future future = shopTagWrapper.preGetDpShopTags(ctx.getDpLongShopId());
        ctx.getFutureCtx().setLeShopTagFuture(future);
    }

    @Override
    public void process(DealCtx ctx) {
        if (Objects.isNull(ctx.getFutureCtx()) || Objects.isNull(ctx.getFutureCtx().getLeShopTagFuture())) {
            return;
        }
        Map<Long, List<DisplayTagDto>> dpShopId2TagsMap = shopTagWrapper.getShopId2TagsMap(ctx.getFutureCtx().getLeShopTagFuture());

        if (MapUtils.isEmpty(dpShopId2TagsMap)) {
            return;
        }
        List<DisplayTagDto> displayTagDtos = dpShopId2TagsMap.get(ctx.getDpLongShopId());
        if (CollectionUtils.isEmpty(displayTagDtos)) {
            return;
        }
        for (DisplayTagDto tag : displayTagDtos) {
            if (tag.getTagId() == LEInsuranceAgreementEnum.SAFE_WASHING.getTagId()) {
                ctx.setLEInsuranceAgreementEnum(LEInsuranceAgreementEnum.SAFE_WASHING);
                return;
            } else if (tag.getTagId() == LEInsuranceAgreementEnum.SAFE_CLEANING.getTagId()) {
                ctx.setLEInsuranceAgreementEnum(LEInsuranceAgreementEnum.SAFE_CLEANING);
                return;
            } else if (tag.getTagId() == LEInsuranceAgreementEnum.SAFE_REPAIR.getTagId()) {
                ctx.setLEInsuranceAgreementEnum(LEInsuranceAgreementEnum.SAFE_REPAIR);
                return;
            }else if (tag.getTagId() == LEInsuranceAgreementEnum.CLEANING_SELF_OWN_PRODUCT.getTagId()) {
                ctx.setLEInsuranceAgreementEnum(LEInsuranceAgreementEnum.CLEANING_SELF_OWN_PRODUCT);
                return;
            }
        }
    }
}
