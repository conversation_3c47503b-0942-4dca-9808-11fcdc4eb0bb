package com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.extend.impl;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.extend.PromoDetailHandler;
import com.dianping.mobile.mapi.dztgdetail.common.enums.PromoDetailEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBestPromoDetail;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ServiceGuarantee.DealBestPromoDetailDTO;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Component
public class MerchantCouponsPromoDetailHandler implements PromoDetailHandler {

    @Override
    public PromoDetailEnum getPromoDetailEnum() {
        return PromoDetailEnum.merchantCoupons;
    }

    @Override
    public DealBestPromoDetailDTO getDealBestPromoDetail(PriceDisplayDTO priceDisplayDTO) {
        DealBestPromoDetail promoDetail = new DealBestPromoDetail();
        promoDetail.setPromoName(PromoDetailEnum.merchantCoupons.getName());

        BigDecimal amount = new BigDecimal(0);
        StringBuilder promoDesc = new StringBuilder();

        for (PromoDTO promoDTO : priceDisplayDTO.getUsedPromos()) {
            if (promoDTO.getIdentity() != null && promoDTO.getAmount() != null && promoDTO.getIdentity().getSourceType() == 2) {
                int promoType = promoDTO.getIdentity().getPromoType();
                if (!isReductionPromo(promoType)) {
                    amount = amount.add(promoDTO.getAmount());
                    promoDesc.append(promoDTO.getExtendDesc());
                }
            }
        }

        if (amount.compareTo(new BigDecimal(0)) == 0) {
            return null;
        }

        promoDetail.setPromoAmount(String.valueOf(amount));

        if(StringUtils.isNotBlank(promoDesc.toString())){
            promoDetail.setPromoDesc(promoDesc.toString());
        }

        DealBestPromoDetailDTO dealBestPromoDetailDTO=new DealBestPromoDetailDTO();
        dealBestPromoDetailDTO.setDealBestPromoDetail(promoDetail);
        dealBestPromoDetailDTO.setPromoAmount(amount);

        return dealBestPromoDetailDTO;
    }

    private boolean isReductionPromo(int promoType) {
        return promoType == PromoTypeEnum.NORMAL_PROMO.getType() || promoType == PromoTypeEnum.IDLE_PROMO.getType() || promoType == PromoTypeEnum.THRESHOLD.getType();
    }
}
