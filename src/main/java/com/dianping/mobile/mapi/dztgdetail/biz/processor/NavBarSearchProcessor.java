package com.dianping.mobile.mapi.dztgdetail.biz.processor;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.NavBarSearchWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiShopCategoryWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.NavBarSearchModuleVO;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.sug.merger.service.enums.PlatformEnum;
import com.sankuai.dzviewscene.sug.merger.service.request.ClientEnv;
import com.sankuai.dzviewscene.sug.merger.service.request.SugMergerBehaviorInfoDTO;
import com.sankuai.dzviewscene.sug.merger.service.request.SugMergerRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;

@Slf4j
public class NavBarSearchProcessor extends AbsDealProcessor {

    @Autowired
    private NavBarSearchWrapper navBarSearchWrapper;

    @Resource
    private PoiShopCategoryWrapper poiShopCategoryWrapper;

    @Resource
    private DouHuService douHuService;

    private static final List<String> SHOW_NAV_BAR_SEARCH_EXP_RESULT = Lists.newArrayList("c", "d");

    @Override
    public boolean isEnable(DealCtx ctx) {
        // 美团App
        return ctx.isMt() && ctx.getEnvCtx().judgeMainApp();
    }

    @Override
    public void prepare(DealCtx ctx) {
        try {
            boolean show = showSearchNavBar(ctx);
            if (!show) {
                return;
            }
            Integer shopFrontLeafCategoryId = getMtFrontLeafCategoryId(ctx);
            ctx.setShopFrontLeafCateId(shopFrontLeafCategoryId);
            SugMergerRequest sugMergerRequest = buildSugMergerRequest(ctx);
            if (sugMergerRequest == null) {
                return;
            }
            Future future = navBarSearchWrapper.prepareNaviSearch(sugMergerRequest);
            ctx.getFutureCtx().setNavBarSearchFuture(future);
        } catch (Exception e) {
            log.error("prepare naviSearch failed", e);
        }
    }

    @Override
    public void process(DealCtx ctx) {
        NavBarSearchModuleVO navBarSearchModuleVO = navBarSearchWrapper.queryNaviSearch(ctx);
        if (navBarSearchModuleVO == null) {
            return;
        }
        boolean showFixText = showFixSearchTextInNavBar(ctx);
        // 展示固定搜索词
        if (showFixText || StringUtils.isBlank(navBarSearchModuleVO.getText())) {
            String fixText = LionConfigUtils.getSearchNavBarFixText();
            navBarSearchModuleVO.setText(fixText);

            // 设置默认跳转URL
            if (StringUtils.isBlank(navBarSearchModuleVO.getJumpUrl())) {
                String defaultJumpUrl = buildDefaultSearchJumpUrl(ctx, fixText);
                navBarSearchModuleVO.setJumpUrl(defaultJumpUrl);
            }
        }
        ctx.setNavBarSearchModuleVO(navBarSearchModuleVO);
    }

    /**
     * 构建请求对象
     */
    private SugMergerRequest buildSugMergerRequest(DealCtx ctx) {
        Integer shopFrontLeafCateId = ctx.getShopFrontLeafCateId();
        if (shopFrontLeafCateId == null) {
            // 如果无法获取类目ID，则不发送请求
            return null;
        }
        SugMergerRequest request = new SugMergerRequest();
        SugMergerBehaviorInfoDTO behaviorInfo = new SugMergerBehaviorInfoDTO();
        behaviorInfo.setSugScene("poi_deal_search_page_words");
        request.setSugMergerBehaviorInfoDTO(behaviorInfo);

        // 设置客户端环境
        ClientEnv clientEnv = new ClientEnv();
        clientEnv.setUuid(ctx.getEnvCtx().getUuid());
        clientEnv.setPlatform(ctx.isMt()? PlatformEnum.MT.code : PlatformEnum.DP.code);
        clientEnv.setUserid(String.valueOf(ctx.getUserId4P()));
        clientEnv.setCityId(ctx.getCityId4P());
        clientEnv.setLng(ctx.getUserlng());
        clientEnv.setLat(ctx.getUserlat());
        request.setClientEnv(clientEnv);

        // 复用已有的extension或创建新的
        Map<String, String> extension = new ConcurrentHashMap<>(request.getExtension() != null ?
                request.getExtension() : new HashMap<>());
        extension.put("dealId", String.valueOf(ctx.getDealId4P()));
        extension.put("mtFrontLeafCateId", String.valueOf(shopFrontLeafCateId)); // 使用已获取的categoryId
        request.setExtension(extension);
        return request;
    }

    /**
     * 构建默认搜索跳转URL
     */
    private String buildDefaultSearchJumpUrl(DealCtx ctx, String fixText) {
        StringBuilder urlBuilder = new StringBuilder();
        urlBuilder.append("imeituan://www.meituan.com/mrn?mrn_biz=gcbu&mrn_entry=searchpage&mrn_component=mrn-gc-searchstart");

        Integer shopFrontLeafCateId = ctx.getShopFrontLeafCateId();
        if (shopFrontLeafCateId != null && shopFrontLeafCateId > 0) {
            urlBuilder.append("&categoryid=").append(shopFrontLeafCateId);
        }
        // 不添加默认类目ID
        urlBuilder.append("&source=deal");
        // 添加搜索提示词
        String hintword = fixText;
        try {
            hintword = java.net.URLEncoder.encode(hintword, "UTF-8");
        } catch (Exception e) {
            log.error("URL encode failed", e);
        }
        urlBuilder.append("&hintword=").append(hintword);
        urlBuilder.append("&resulttype=tuan");
        urlBuilder.append("&tuansource=72");
        return urlBuilder.toString();
    }

    private boolean showFixSearchTextInNavBar(DealCtx ctx) {
        ModuleAbConfig moduleAbConfig = douHuService.getNavbarSearchAbTestResult(ctx.getEnvCtx());
        if (Objects.isNull(moduleAbConfig)) {
            return false;
        }
        return moduleAbConfig.getConfigs()
                .stream()
                .anyMatch(abConfig -> StringUtils.equals(abConfig.getExpResult(), "d"));
    }

    private boolean showSearchNavBar(DealCtx ctx) {
        ModuleAbConfig moduleAbConfig = douHuService.getNavbarSearchAbTestResult(ctx.getEnvCtx());
        if (Objects.isNull(moduleAbConfig)) {
            return false;
        }
        return moduleAbConfig.getConfigs()
                .stream()
                .anyMatch(abConfig -> SHOW_NAV_BAR_SEARCH_EXP_RESULT.contains(abConfig.getExpResult()));
    }

    /**
     * 获取类目ID
     * @param ctx 上下文
     * @return 类目ID，如果获取不到则返回null
     */
    private Integer getMtFrontLeafCategoryId(DealCtx ctx) {
        // 首先尝试从shopCategoryIds中获取
        Long shopId = ctx.getLongPoiId4PFromReq() > 0 ? ctx.getLongPoiId4PFromReq() : ctx.getLongPoiId4PFromResp();
        if (shopId <= 0) {
            return null;
        }
        List<Integer> shopCategoryIds = poiShopCategoryWrapper.queryMtShopFrontLeafCategoryIds(shopId);
        return Optional.ofNullable(shopCategoryIds)
                .orElse(Lists.newArrayList())
                .stream()
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
    }
}
