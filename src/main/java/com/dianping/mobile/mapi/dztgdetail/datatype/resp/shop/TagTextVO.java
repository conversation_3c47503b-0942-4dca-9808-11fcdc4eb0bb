package com.dianping.mobile.mapi.dztgdetail.datatype.resp.shop;

import lombok.Data;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;

import java.io.Serializable;

/**
 * 文本标签字段
 */
@Data
public class TagTextVO implements Serializable {
    /**
     * 左边距
     */
    @MobileField(key = 0x793d)
    private double paddingLeft;

    /**
     * 右边距
     */
    @MobileField(key = 0x5207)
    private double paddingRight;

    /**
     * 文本字体大小
     */
    @MobileField(key = 0xfee3)
    private double textSize;

    /**
     * 文本颜色
     */
    @MobileField(key = 0xeead)
    private String textColor;

    /**
     * 文本信息
     */
    @MobileField(key = 0xb84b)
    private String tagText;
}
