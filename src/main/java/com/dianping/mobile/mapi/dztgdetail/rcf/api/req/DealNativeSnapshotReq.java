package com.dianping.mobile.mapi.dztgdetail.rcf.api.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.DecryptedField;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: guangyujie
 * @Date: 2024/11/16 15:09
 */
@Data
@TypeDoc(description = "团单native快照接口请求参数")
@MobileRequest
public class DealNativeSnapshotReq implements IMobileRequest, Serializable {

    @Deprecated
    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，原int类型")
    @MobileRequest.Param(name = "dealgroupid")
    private Integer dealGroupId;

    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，string类型")
    @MobileRequest.Param(name = "stringdealgroupid")
    private String stringDealGroupId;

    @FieldDoc(description = "商户ID", rule = "美团平台为美团商户ID，点评平台为点评商户ID")
    @MobileRequest.Param(name = "poiid", shopuuid = "shopuuid")
    private long poiId;

    @FieldDoc(description = "商户ID加密串", rule = "只有点评有效")
    @MobileRequest.Param(name = "poiidEncrypt")
    @DecryptedField(targetFieldName = "poiId")
    private String poiidEncrypt;

    @FieldDoc(description = "选择城市")
    @MobileRequest.Param(name = "cityid")
    private int cityId;

    @FieldDoc(description = "定位城市")
    @MobileRequest.Param(name = "gpscityid")
    private int gpsCityId;

    @FieldDoc(description = "用户经度(只接受火星坐标系gcj02)")
    @MobileRequest.Param(name = "userlng")
    private double userLng;

    @FieldDoc(description = "用户纬度(只接受火星坐标系gcj02)")
    @MobileRequest.Param(name = "userlat")
    private double userLat;

    @FieldDoc(description = "请求来源")
    @MobileRequest.Param(name = "pagesource")
    private String pageSource;

    @FieldDoc(description = "APP版本")
    @MobileRequest.Param(name = "appversion")
    private String appVersion;

    @FieldDoc(description = "MRN版本")
    @MobileRequest.Param(name = "mrnversion")
    private String mrnVersion;

    @FieldDoc(description = "设备高度")
    @MobileRequest.Param(name = "deviceheight")
    private Double deviceHeight;

    @FieldDoc(description = "设备宽度")
    @MobileRequest.Param(name = "devicewidth")
    private Double deviceWidth;

    @FieldDoc(description = "设备宽度")
    @MobileRequest.Param(name = "statusbarheight")
    private Double statusBarHeight;

    @FieldDoc(description = "新团详参数")
    @MobileRequest.Param(name = "pageType")
    private String pageType;

}
