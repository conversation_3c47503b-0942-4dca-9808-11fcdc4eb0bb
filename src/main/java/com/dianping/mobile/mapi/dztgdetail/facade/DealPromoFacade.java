package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.ProcessHandler;
import com.dianping.mobile.mapi.dztgdetail.common.ShopUuidUtils;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealPromoModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo.DealPromoModule;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * Created by zuomlin on 2018/12/13.
 */
@Component
public class DealPromoFacade {

    @Autowired
    @Qualifier("promoPreModuleHandler")
    private ProcessHandler<DealCtx> promoPreModuleHandler;

    @Autowired
    @Qualifier("promoModuleHandler")
    private ProcessHandler<DealCtx> promoModuleHandler;

    @Autowired
    @Qualifier("promoPostModuleHandler")
    private ProcessHandler<DealCtx> promoPostModuleHandler;

    public DealPromoModule queryDealPromoModule(DealPromoModuleReq request, EnvCtx envCtx) throws Exception {
        DealCtx dealCtx = initDealCtx(request, envCtx);

        //负责团单基础信息或者一些后续接口依赖的前置数据聚合
        promoPreModuleHandler.preThenProc(dealCtx);

        //立减必须传团单价格所以前置必须调DealBase 参数：dealGroupId分平台
        //拼团只用点评dealGroupId 参数：dpDealGroupId, cityId分平台, userId分平台
        //红包分享的接口的dealGroupId是分平台的么, 点评传点评id, 美团传美团id？ 参数：dealGroupId未知, platform
        //返券接口只用点评dealGroupId 参数：dpDealGroupId, dpUserId, platform

        promoModuleHandler.preThenProc(dealCtx);

        promoPostModuleHandler.preThenProc(dealCtx);

        return dealCtx.getDealPromoModuleResult();
    }

    private DealCtx initDealCtx(DealPromoModuleReq request, EnvCtx envCtx) {
        DealCtx dealCtx = new DealCtx(envCtx);
        if (envCtx.isMt()) {
            dealCtx.setMtId(request.getDealGroupId());
            dealCtx.setMtCityId(request.getCityId());
            dealCtx.setMtShopId(request.getShopId());
            dealCtx.setMtLongShopId(request.getShopIdLong());
        } else {
            dealCtx.setDpId(request.getDealGroupId());
            dealCtx.setDpCityId(request.getCityId());
            dealCtx.setDpShopId(request.getShopId());
            dealCtx.setDpLongShopId(request.getShopIdLong());
        }
        return dealCtx;
    }
}
