package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: zhen<PERSON><PERSON><EMAIL>
 * @Date: 2024/10/30
 */
@Data
@TypeDoc(description = "推荐项目模型")
@MobileDo(id = 0xbe04)
public class RecommendItemDTO implements Serializable {
    @FieldDoc(description = "项目类型，1-团单，2-商户 3-标品")
    @MobileDo.MobileField(key = 0xfac2)
    private Integer itemType;

    @FieldDoc(description = "推荐团单信息")
    @MobileDo.MobileField(key = 0x8b60)
    private RelatedDealPBO dealPBO;

    @FieldDoc(description = "推荐门店信息")
    @MobileDo.MobileField(key = 0x96d3)
    private RelatedShopPBO shopPBO;

    @FieldDoc(description = "广告数据上报模型")
    @MobileDo.MobileField(key = 0x7a92)
    private AdReportDTO adReportDTO;
}
