package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.struct;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by yangquan02 on 20/2/21.
 */
@MobileDo(id = 0x1b78)
@Data
public class ProcessItemDo implements Serializable {

    @MobileDo.MobileField(key = 0x24cc)
    private String title;
    @MobileDo.MobileField(key = 0xc4d0)
    private String duration;
    @MobileDo.MobileField(key = 0xe23d)
    private List<String> items;

}
