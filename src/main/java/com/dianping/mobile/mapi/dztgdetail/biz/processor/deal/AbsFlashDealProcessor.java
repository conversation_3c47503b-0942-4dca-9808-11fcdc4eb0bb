package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.Processor;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FlashDealCtx;
import com.dianping.mobile.mapi.dztgdetail.entity.ProcessorConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.UrlProcessorDztgClient;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

public abstract class AbsFlashDealProcessor implements Processor<FlashDealCtx> {

    protected Logger logger = LoggerFactory.getLogger(AbsFlashDealProcessor.class);

    @Override
    public boolean isEnd(FlashDealCtx ctx) {
        return ctx.isEnd();
    }

    @Override
    public boolean isEnable(FlashDealCtx ctx) {
        return Boolean.TRUE;
    }

    @Override
    public boolean matchDztgClient(FlashDealCtx ctx, Processor<FlashDealCtx> processor) {
        return true;
    }

    @Override
    public List<Integer> getConfigUrlDztgClient(FlashDealCtx ctx) {
        return Collections.emptyList();
    }



}
