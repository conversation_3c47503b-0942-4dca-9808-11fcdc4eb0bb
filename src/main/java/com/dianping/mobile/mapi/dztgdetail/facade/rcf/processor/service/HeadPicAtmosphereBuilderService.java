package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.atmosphere.HeadPicAtmosphere;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-01-06
 * @desc 头图氛围处理服务
 */
@Component
@Slf4j
public class HeadPicAtmosphereBuilderService {
    @Resource
    private GuaranteeBuilderService guaranteeBuilderService;

    public HeadPicAtmosphere build(DealCtx ctx) {
      if (guaranteeBuilderService.hasSafeImplantTag(ctx) || DealCtxHelper.hitAssuredImplantShopProductTag(ctx, LionConfigUtils.getInsuranceInfoDetailConfig())) {
            return LionConfigUtils.getSafeImplantHeadPicAtmosphere();
        }
        return null;
    }
}
