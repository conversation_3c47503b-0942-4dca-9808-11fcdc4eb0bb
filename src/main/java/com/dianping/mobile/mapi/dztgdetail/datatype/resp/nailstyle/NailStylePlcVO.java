package com.dianping.mobile.mapi.dztgdetail.datatype.resp.nailstyle;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-01-09
 * @desc 沉浸页款式Plc
 */
@Data
@MobileDo(id = 0x1160)
public class NailStylePlcVO implements Serializable {
    @FieldDoc(description = "热门主题跳链")
    @MobileDo.MobileField(key = 0x9802)
    private String moreStyleUrl;

    @FieldDoc(description = "更多同主题款式icon")
    @MobileDo.MobileField(key = 0x145c)
    private String moreStyleIcon;

    @FieldDoc(description = "更多同主题款式")
    @MobileDo.MobileField(key = 0x1f07)
    private String moreStyleText;

    @FieldDoc(description = "款式主题")
    @MobileDo.MobileField(key = 0x4921)
    private String styleTheme;

    @FieldDoc(description = "热门款式小图链接")
    @MobileDo.MobileField(key = 0x1cde)
    private String nailStyleUrl;
}
