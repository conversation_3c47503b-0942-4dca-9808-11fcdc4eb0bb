package com.dianping.mobile.mapi.dztgdetail.button.normal;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MemberExclusiveWrapper;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.button.joy.AbstractPriceServiceButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.common.enums.SaleStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ShoppingCartStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.StyleTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.PriceHelper;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import static com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum.MEMBER;

/**
 * @Desc 会员专属团单底部bar构建
 * <AUTHOR>
 * @Date 2023/5/26 11:44
 */
@Slf4j
public class MemberExclusiveButtonBuilder extends AbstractPriceServiceButtonBuilder {

    private static final String MEMBER_PRICE_TITLE = "会员价";

    @Override
    protected void doBuild(DealCtx context, ButtonBuilderChain chain) {
        boolean memberAllowedClient = context.getEnvCtx().isMemberAllowedClient();
        // 非商家会员客户端白名单
        if (!memberAllowedClient) {
            return;
        }

        if (!GreyUtils.isMemberExclusive(context)) {
            return;
        }

        boolean isMember = MemberExclusiveWrapper.isMember(context);
        boolean isWarmUp = isWarmUp(context);
        DealBuyBtn canNotBuyButton = DealBuyHelper.getCanNotBuyButton(context);
        if (canNotBuyButton != null && !isWarmUp) {
            context.addButton(canNotBuyButton);
            chain.interrupt();
            return;
        }

        //如果是非会员，且如商品为预热/定时释放团单时，非会员时底部仍然展示“领取会员”按钮
        //如果是会员，且如商品为预热/定时释放团单时，已预热单状态为准
        DealBuyBtn button = buildBaseButton(context);
        // 增加购物车按钮的判断
        if (DealBuyHelper.isShoppingCart(context)){
            context.getBuyBar().setStyleType(StyleTypeEnum.SHOPPING_CART.code);
            button.setAddShoppingCartStatus(ShoppingCartStatusEnum.ADD_SHOPPING_CART.code);
        }
        if (isMember) {
            if (isWarmUp) {
                buildWarmUpSaleStatus(context, button);
            }
            context.addButton(button);
        } else {
            button.setDetailBuyType(MEMBER.getCode());
            button.setBtnTitle(MEMBER_PRICE_TITLE);
            // 如果是小程序的话,这个地方的逻辑有调整,会员专属的也需要跳转到提单页里面
            if (!(context.getEnvCtx().isMiniApp() && LionConfigUtils.memberCardMiniProgramSwitch())) {
                button.setRedirectUrl(context.getMemberInfoRespDTO().getMemberPageUrl());
            }
            context.addButton(button);
        }
        chain.build(context);
    }

    private void buildWarmUpSaleStatus(DealCtx context, DealBuyBtn button) {
        buildBtnEnable(context,button);
        buildSaleStatus(context,button);

    }
    private void buildBtnEnable(DealCtx context, DealBuyBtn button) {
        if(StringUtils.isNotBlank(context.getSaleStatus())){
            if(context.getSaleStatus().equals(SaleStatusEnum.SNAP_UP_NOW.saleStatusName)){
                button.setBtnEnable(true);
            }else{
                button.setBtnEnable(false);
            }
        }
    }

    private void buildSaleStatus(DealCtx context, DealBuyBtn button) {
        //若是预热单，更新title&&设置按钮的售卖状态
        if(StringUtils.isNotBlank(context.getSaleStatus())){
            button.setSaleStatus(context.getSaleStatus());
            button.setBtnTitle(SaleStatusEnum.getDesc(context.getSaleStatus()));
            if(context.getSaleStatus().equals(SaleStatusEnum.SNAP_UP_NOW.saleStatusName)){
                button.setBtnTitle("会员价抢购");
            }
        }
    }

    private DealBuyBtn buildBaseButton(DealCtx context){
        DealBuyBtn button = buildOriginButton(context, MEMBER_PRICE_TITLE);
        button.setPriceStr(formatPrice(getPrice(context).getPrice()));
        button.setBtnIcons(Lists.newArrayList());
        return button;
    }

    public boolean isWarmUp(DealCtx context){
        return context != null && StringUtils.isNotBlank(context.getSaleStatus());
    }

    @Override
    protected PriceDisplayDTO getPrice(DealCtx context) {
        return PriceHelper.getNormalPrice(context);
    }

}