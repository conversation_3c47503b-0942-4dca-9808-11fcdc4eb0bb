package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Cons;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealFields;
import com.google.gson.Gson;
import com.meituan.service.mobile.group.geo.bean.CityInfo;
import com.meituan.service.mobile.prometheus.model.DealModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.json.JSONArray;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 15/4/21 10:17
 */
@Slf4j
public class DealFieldTransferHelper {

    private static class AvailabilityAttr {
        int useDay;
        String weekday;
        String startDate;
        String endDate;
        String specialCondition;
    }

    public static boolean isAvailableToday(String attr, Date date) {
        if (StringUtils.isBlank(attr)) {
            return true;
        }
        try {
            Gson gson = new Gson();
            AvailabilityAttr availability = gson.fromJson(attr, AvailabilityAttr.class);
            if (availability.useDay == 1) {
                return true;
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd|u");
            String formatedDate = sdf.format(date);
            String dateStr = formatedDate.substring(0, 10);
            String weekDay = formatedDate.substring(11, 12);
            String startDates[] = null;
            String endDates[] = null;
            if (StringUtils.isNotBlank(availability.startDate)) {
                startDates = availability.startDate.split(",");
                endDates = availability.endDate.split(",");
            }
            boolean bContained = false;
            if (StringUtils.isNotBlank(availability.weekday)) {
                if (availability.weekday.contains(weekDay)) {
                    bContained = true;
                }
            }
            if (!bContained && startDates != null && startDates.length > 0) {
                for (int i = 0; i < startDates.length; i++) {
                    if (dateStr.compareTo(startDates[i]) >= 0 && dateStr.compareTo(endDates[i]) <= 0) {
                        bContained = true;
                    }
                }
            }
            if (availability.useDay == 0) {
                return !bContained;
            }
            return true;
        } catch (Exception ex) {
            log.error("DealFieldTransferHelper.isAvailableToday error", ex);
        }
        return true;
    }

    /**
     * 该逻辑的需求文档 http://wiki.sankuai.com/pages/viewpage.action?pageId=101711126
     */
    public static String genHotelRoomName(DealModel dealModel) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.helper.DealFieldTransferHelper.genHotelRoomName(com.meituan.service.mobile.prometheus.model.DealModel)");
        Map<Integer, String> attrsMap = dealModel.getAttrs();
        if (attrsMap == null || attrsMap.size() == 0) {
            return Strings.EMPTY;
        }
        //房间信息
        String roomInfo = attrsMap.get(Cons.ATTRS_ROOM_INFO);
        //房型
        String roomNames = attrsMap.get(Cons.ATTRS_ROOM_NAMES);
        //代金券品类
        String cate = attrsMap.get(Cons.ATTRS_CATE);
        //如果是酒店代金券(9995属性值为1000320)，返回mtitle
        if (String.valueOf(Cons.ATTRS_VALUE_HOTEL_COUPON_CATE).equals(cate)) {
            return dealModel.getMtitle();
        }
        //如果酒店信息不完整则说明不是酒店返回空
        if (StringUtils.isBlank(roomInfo) || StringUtils.isBlank(roomNames)) {
            return Strings.EMPTY;
        }
        try {
            JSONObject infoJson = new JSONObject(roomInfo);
            String type = infoJson.getString("key");
            StringBuilder sb = new StringBuilder();
            //钟点房
            if ("HR".equals(type)) {
                if (!infoJson.has("label") || !infoJson.getJSONObject("label").has("hourCount")) {
                    return dealModel.getMtitle();
                }
                String hourCount = infoJson.getJSONObject("label").getString("hourCount");
                JSONArray names = new JSONArray(roomNames);
                int length = names.length();
                if (length > 2) {
                    sb.append("入住");
                    sb.append(hourCount);
                    sb.append("小时，");
                    sb.append(names.getJSONObject(0).getString("value"));
                    sb.append("等房型");
                    sb.append(length);
                    sb.append("选1");
                } else if (length > 1) {
                    sb.append("入住");
                    sb.append(hourCount);
                    sb.append("小时，");
                    sb.append(names.getJSONObject(0).getString("value"));
                    sb.append("/");
                    sb.append(names.getJSONObject(1).getString("value"));
                    sb.append("2选1");
                } else {
                    sb.append("入住");
                    sb.append(hourCount);
                    sb.append("小时，");
                    sb.append(names.getJSONObject(0).getString("value"));
                }
            }
            //全日房
            else {
                if (!infoJson.has("label") || !infoJson.getJSONObject("label").has("number")) {
                    return dealModel.getMtitle();
                }
                String number = infoJson.getJSONObject("label").getString("number");
                JSONArray names = new JSONArray(roomNames);
                int length = names.length();
                if (Integer.parseInt(number) > 1) {
                    sb.append("入住");
                    sb.append(number);
                    sb.append("晚，");
                }
                if (length > 2) {
                    sb.append(names.getJSONObject(0).getString("value"));
                    sb.append("等房型");
                    sb.append(length);
                    sb.append("选1");
                } else if (length > 1) {
                    sb.append(names.getJSONObject(0).getString("value"));
                    sb.append("/");
                    sb.append(names.getJSONObject(1).getString("value"));
                    sb.append("2选1");
                } else if (length == 1) {
                    sb.append(names.getJSONObject(0).getString("value"));
                }
            }
            if (StringUtils.isBlank(sb.toString())) {
                return dealModel.getMtitle();
            } else {
                return sb.toString();
            }
        } catch (Exception e) {
            log.error("genHotelRoomName json error", e);
            return dealModel.getMtitle();
        }
    }

    /**
     * 生产新的rangeName
     */
    public static String produceRangeName(List<Integer> cityIds, CityInfo currentCity, String basicRangeName) {
        List<Integer> cityIdsList = uniqCityIds(cityIds);
        // 如果为空，返回已有的rangeName
        if (CollectionUtils.isEmpty(cityIdsList)) {
            return basicRangeName;
        }
        // 没有当前城市且cityIds>1
        if (currentCity == null) {
            if (cityIdsList.size() > 1) {
                return "多城市";
            } else {
                return basicRangeName;
            }
        }
        // 全国单
        if (cityIdsList.contains(DealFields.GLOBAL_CITY_ID)) {
            return "全国";
        }
        // 北京等
        if (cityIdsList.size() > 1) {
            return currentCity.getName() + "等";
        }
        return basicRangeName;
    }


    private static List<Integer> uniqCityIds(List<Integer> cityIds) {
        if (CollectionUtils.isEmpty(cityIds)) {
            return Collections.emptyList();
        }
        Set<Integer> set = new HashSet<Integer>();
        for (Integer each : cityIds) {
            if (each <= 0) {
                continue;
            }
            set.add(each);
        }
        List<Integer> list = new ArrayList<Integer>();
        list.addAll(set);
        return list;
    }
}