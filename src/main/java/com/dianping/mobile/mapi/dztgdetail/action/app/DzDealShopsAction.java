package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DzDealShopsRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.shop.DealGroupShopVO;
import com.dianping.mobile.mapi.dztgdetail.facade.shop.DzDealShopsHttpFacade;
import com.dianping.mobile.mapi.dztgdetail.util.AntiCrawlerUtils;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * @Author: zhangyuan103
 * @Date: 2024/8/13
 */
@InterfaceDoc(displayName = "到综团购适用门店列表查询接口",
        type = "restful",
        description = "到综团购适用门店列表查询接口(由团详页和爱车订详页跳转来), 包括门店id、门店名称、门店地址、商户电话数组、距离、商户详情页、标签信息、电话图标、定位图标",
        scenarios = "该接口仅适用于双平台APP站点和小程序站点的团购适用门店列表页(由团详页和爱车订详页跳转来)",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "zhangyuan103"
)
@Controller("general/platform/dztgdetail/dzdealshops.bin")
@Action(url = "dzdealshops.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class DzDealShopsAction extends AbsAction<DzDealShopsRequest> {
    @Resource
    DzDealShopsHttpFacade dzDealShopsHttpFacade;

    @Override
    protected IMobileResponse validate(DzDealShopsRequest request, IMobileContext iMobileContext) {
        if (request == null || request.getDealGroupId() == null || request.getDealGroupId() <= 0 ||
                request.getCityId() == null || request.getCityId() < 0 ||
                request.getPageNum() == null || request.getPageSize() == null || request.getPageSize() > 20 ||
                request.getPageSize() <= 0 || request.getPageNum() <= 0) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "dzdealshops.bin",
            displayName = "到综团购适用门店列表查询接口",
            description = "到综团购适用门店列表查询接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "dzdealshops.bin请求参数",
                            type = DzDealShopsRequest.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "result", description = "团购picasso数据", type = DealGroupShopVO.class)},
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    ),
            }
    )
    @Override
    @CryptMethod
    protected IMobileResponse execute(DzDealShopsRequest request, IMobileContext iMobileContext) {
        try {
            EnvCtx envCtx = initEnvCtxV2(iMobileContext);
            if (AntiCrawlerUtils.hide(iMobileContext)) {
                return new CommonMobileResponse(Resps.NoDataResp);
            }

            DealGroupShopVO result = dzDealShopsHttpFacade.execute(request, envCtx);
            if (result == null) {
                return new CommonMobileResponse(Resps.NoDataResp);
            }
            return new CommonMobileResponse(result);
        } catch (Exception e) {
            logger.error("dzdealshops.bin error", e);
        }
        return Resps.SYSTEM_ERROR;
    }


    @Override
    protected List<ClientInfoRule> getRule() {
        return Collections.emptyList();
    }
}