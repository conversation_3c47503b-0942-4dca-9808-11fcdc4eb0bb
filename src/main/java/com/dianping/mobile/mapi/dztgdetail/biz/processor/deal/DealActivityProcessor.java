package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;


import com.dianping.gmkt.activity.api.dto.BatchQueryDealActivityRequest;
import com.dianping.gmkt.activity.api.dto.DealActivityDTO;
import com.dianping.gmkt.activity.api.dto.Version;
import com.dianping.gmkt.activity.api.enums.ExposeChannel;
import com.dianping.gmkt.activity.api.enums.RequestSource;
import com.dianping.gmkt.activity.enums.AppPlatform;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealActivityWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.concurrent.Future;

public class DealActivityProcessor extends AbsDealProcessor {

    @Autowired
    private DealActivityWrapper dealActivityWrapper;

    private final static String DEAL_DETAIL_DISPLAY_SWITCH = "dealDetailLabelDisplaySwitch";

    @Override
    public boolean isEnable(DealCtx ctx) {
        return ctx.getEnvCtx().isMainApp();
    }

    @Override
    public void prepare(DealCtx ctx) {
        Future activityFuture = dealActivityWrapper.prepareDealActivity(buildBatchQueryDealActivityReq(ctx, RequestSource.DealDetail_QualityDeal));
        ctx.getFutureCtx().setDealActivityFuture(activityFuture);

        Future dealTitleActivityFuture = dealActivityWrapper.prepareDealActivity(buildBatchQueryDealActivityReq(ctx, RequestSource.ProductDetail_PromoTitle));
        ctx.getFutureCtx().setDealTitleActivityFuture(dealTitleActivityFuture);
    }

    @Override
    public void process(DealCtx ctx) {
        List<DealActivityDTO> dealActivityDTOS = dealActivityWrapper.queryDealActivity(ctx.getFutureCtx().getDealActivityFuture(), ctx);
        if (CollectionUtils.isNotEmpty(dealActivityDTOS)) {
            DealActivityDTO selectedDealActivity = dealActivityDTOS.get(0);
            for (DealActivityDTO dealActivityDTO : dealActivityDTOS) {
                if (dealActivityDTO == null || CollectionUtils.isEmpty(dealActivityDTO.getUrls()) || !dealActivityDTO.isEnable()) {
                    continue;
                }
                selectedDealActivity = dealActivityDTO;
            }
            ctx.setChoicestDealActivityDTO(selectedDealActivity);
        }

        List<DealActivityDTO> dealTitleActivityDTOS = dealActivityWrapper.queryDealActivity(ctx.getFutureCtx().getDealTitleActivityFuture(), ctx);
        if(CollectionUtils.isNotEmpty(dealTitleActivityDTOS)) {
            for(DealActivityDTO dealActivityDTO : dealTitleActivityDTOS) {
                if(dealActivityDTO !=null && MapUtils.isNotEmpty(dealActivityDTO.getSwitchMap()) && dealActivityDTO.getSwitchMap().containsKey(DEAL_DETAIL_DISPLAY_SWITCH) &&  dealActivityDTO.getSwitchMap().get(DEAL_DETAIL_DISPLAY_SWITCH)) {
                    ctx.setDealTitleActivityDTO(dealActivityDTO);
                    break;
                }
            }
        }
    }

    private BatchQueryDealActivityRequest buildBatchQueryDealActivityReq(DealCtx ctx, RequestSource source) {
        BatchQueryDealActivityRequest request = new BatchQueryDealActivityRequest();
        if (ctx.isMt()) {
            request.setMtDealIds(Lists.newArrayList(ctx.getMtId()));
            request.setMtCity(ctx.getMtCityId());
            request.setUserIdL(ctx.getEnvCtx().getMtUserId());//已确认判断平台后再使用
            request.setAppPlatform(AppPlatform.MT);
        } else {
            request.setDpDealIds(Lists.newArrayList(ctx.getDpId()));
            request.setDpCity(ctx.getDpCityId());
            request.setUserIdL(ctx.getEnvCtx().getDpUserId());
            request.setAppPlatform(AppPlatform.DP);
        }
        request.setSource(source);
        request.setVersion(new Version(ctx.getEnvCtx().getVersion()));
        request.setChannel(ExposeChannel.App.code);
        return request;
    }
}
