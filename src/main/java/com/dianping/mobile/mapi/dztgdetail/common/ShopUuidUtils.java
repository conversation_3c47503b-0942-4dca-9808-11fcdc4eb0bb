package com.dianping.mobile.mapi.dztgdetail.common;

import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ShopIdUuidDTO;
import com.dianping.mobile.mapi.dztgdetail.util.RedisClientUtils;
import com.dianping.shopremote.remote.dto.ShopUuidDTO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.cache2.CacheClient;
import com.sankuai.athena.inf.cache2.CacheClientConfig;
import com.sankuai.athena.inf.cache2.CacheKey;
import com.sankuai.athena.inf.cache2.loader.DataLoader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Random;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Slf4j
public final class ShopUuidUtils {

    private ShopUuidUtils() {
    }

    private static final String SHOP_ID_BlACK_LIST_KEY = Environment.getAppName() + ".shop.id.black.list";
    private static final String SHOP_ID_RETAIN_KEY = Environment.getAppName() + ".shop.id.retain";
    private static final String CAT_EVENT_TYPE = "ShopIdToUuid";
    private static final Pattern SHOP_ID_PATTERN = Pattern.compile("(?<=shopid=)(\\w+)");
    private static final String REDIS_DEAL_MAPPER_SHOP_UUID = "mapper_shop_uuid";

    private static CacheClient cacheClient = RedisClientUtils.getRedisCacheClient();

    private static TypeReference<ShopUuidDTO> shopUuidMapperCacheTypeReference = new TypeReference<ShopUuidDTO>() {};

    private static final int CACHE_EXPIRE_TIME = 1296000;

    private static final int CACHE_REFRESH_TIME = 0;

    private static final String MAPPER_CACHE_KEY = "mapperCacheError";

    public static ShopUuidDTO getUuidDtoById(Long shopId) {
        if (shopId == null || shopId <= 0) {
            return null;
        }
        try {
            ShopIdUuidDTO uuidDtoByIdForLong = ShopUuidUtilsV2.getUuidDtoByIdForLong(shopId);
            return convertToShopUuidDTO(uuidDtoByIdForLong);
        } catch (Exception e) {
            Cat.logEvent(CAT_EVENT_TYPE, "RemoteException", "1", e.getMessage());
            log.error("get shop uuid failed for [{}]", shopId, e);
            return null;
        }
    }

    public static Long getShopIdByUuid(String uuid) {
        if(uuid == null) {
            return 0L;
        }
        try {
            if (Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.ShopUuidUtilsV2.enable", false)) {
                return ShopUuidUtilsV2.getShopIdByUuid(uuid);
            }
            return ShopUuidUtilsV2.getShopIdByUuid(uuid);
        } catch (Exception e) {
            Cat.logEvent(CAT_EVENT_TYPE, "RemoteException", "1", e.getMessage());
            log.error("get shopid failed for [{}]", uuid, e);
            return 0L;
        }
    }

    @Deprecated
    public static String getUuidById(Integer shopId) {
        ShopUuidDTO uuidDto = getUuidDtoById(Long.valueOf(shopId));
        if (uuidDto != null) {
            String uuid = uuidDto.getShopUuid();
            if (StringUtils.isBlank(uuid)) {
                Cat.logEvent(CAT_EVENT_TYPE, "RemoteReturnBlank", "1", "");
            }
            return uuid == null ? "" : uuid;
        }
        return "";
    }

    public static String getUuidByIdLong(Long shopId) {
        try {
            if (Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.ShopUuidUtilsV2.enable", false)) {
                return ShopUuidUtilsV2.getUuidById(shopId);
            }
        } catch (Exception e) {
            Cat.logEvent(CAT_EVENT_TYPE, "RemoteException", "1", e.getMessage());
            log.error("get shop uuid failed for [{}]", shopId, e);
            return "";
        }
        ShopUuidDTO uuidDto = getUuidDtoById(shopId);
        if (uuidDto != null) {
            String uuid = uuidDto.getShopUuid();
            if (StringUtils.isBlank(uuid)) {
                Cat.logEvent(CAT_EVENT_TYPE, "RemoteReturnBlank", "1", "");
            }
            return uuid == null ? "" : uuid;
        }
        return "";
    }


    public static String getUuidById(Long shopId) {
        try {
            if (Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.ShopUuidUtilsV2.enable", false)) {
                return ShopUuidUtilsV2.getUuidById(shopId);
            }
        } catch (Exception e) {
            Cat.logEvent(CAT_EVENT_TYPE, "RemoteException", "1", e.getMessage());
            log.error("get shop uuid failed for [{}]", shopId, e);
            return "";
        }
        ShopUuidDTO uuidDto = getUuidDtoById(shopId);
        if (uuidDto != null) {
            String uuid = uuidDto.getShopUuid();
            if (StringUtils.isBlank(uuid)) {
                Cat.logEvent(CAT_EVENT_TYPE, "RemoteReturnBlank", "1", "");
            }
            return uuid == null ? "" : uuid;
        }
        return "";
    }

    public static String filterUrl(String url) {
        if (StringUtils.isBlank(url) || url.contains("shopuuid")) {
            return url;
        }

        Matcher m = SHOP_ID_PATTERN.matcher(url);
        if (m.find()) {
            long shopId = Long.parseLong(m.group(1));

            String dpShopUuid = getUuidById(shopId);
            if (StringUtils.isNotBlank(dpShopUuid)) {
                url += "&shopuuid=" + dpShopUuid;

                if (!retainShopId(shopId)) {
                    url = m.replaceAll("0");
                }
            }
        }

        return url;
    }

    public static String filterEncodedUrl(String url) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.common.ShopUuidUtils.filterEncodedUrl(java.lang.String)");
        if (StringUtils.isBlank(url)) {
            return url;
        }

        try {
            url = URLDecoder.decode(url, StandardCharsets.UTF_8.name());
            url = filterUrl(url);
            url = URLEncoder.encode(url, StandardCharsets.UTF_8.name());
        } catch (UnsupportedEncodingException e) {
            log.error("[filterEncodedUrl] failed", e);
        }
        return url;
    }

    @Deprecated
    public static boolean retainShopId(Integer shopId) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.common.ShopUuidUtils.retainShopId(java.lang.Integer)");
        Long longId = shopId == null ? null : shopId.longValue();
        return retainShopId(longId);
    }

    @Deprecated
    public static boolean retainShopId(Long shopId) {
        if (inBlackList(shopId)) {
            return false;
        }
        return Lion.getBooleanValue(SHOP_ID_RETAIN_KEY, true);
    }

    private static boolean inBlackList(Long shopId) {
        if (shopId == null || shopId <= 0) {
            return false;
        }
        List<Long> blackList = Lion.getList(SHOP_ID_BlACK_LIST_KEY, Long.class, Collections.emptyList());
        return blackList.contains(shopId);
    }

    private static ShopUuidDTO fetchUuidDtoById(long dpShopId) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.common.ShopUuidUtils.fetchUuidDtoById(long)");
        ShopUuidDTO shopUuidDTO;
        try {
            shopUuidDTO = getUuidFromCache(dpShopId).get();
        } catch (Exception e) {
            log.error("[ShopUuidUtils] fetchUuidDtoById err", e);
            Cat.logEvent(MAPPER_CACHE_KEY, "shopUuidError");
            ShopIdUuidDTO uuidDtoByIdForLong = ShopUuidUtilsV2.getUuidDtoByIdForLong(dpShopId);
            return convertToShopUuidDTO(uuidDtoByIdForLong);
        }
        return shopUuidDTO;
    }

    private static CompletableFuture<ShopUuidDTO> getUuidFromCache(long dpShopId) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.common.ShopUuidUtils.getUuidFromCache(long)");
        CacheKey cacheKey = new CacheKey(REDIS_DEAL_MAPPER_SHOP_UUID, dpShopId);
        DataLoader<ShopUuidDTO> dataLoader = key -> {
            if(key == null) {
                return CompletableFuture.completedFuture(null);
            }
            return CompletableFuture.completedFuture(getUuidDtoById(dpShopId));
        };
        return cacheClient.asyncGetReadThrough(cacheKey,shopUuidMapperCacheTypeReference, dataLoader, CACHE_EXPIRE_TIME + new Random().nextInt(300) , CACHE_REFRESH_TIME);
    }

    private static ShopUuidDTO convertToShopUuidDTO(ShopIdUuidDTO shopIdUuidDTO) {
        if ( Objects.isNull(shopIdUuidDTO)) {
            return new ShopUuidDTO();
        }
        ShopUuidDTO shopUuidDTO = new ShopUuidDTO();
        shopUuidDTO.setShopUuid(shopIdUuidDTO.getShopUuid());
        shopUuidDTO.setShopId(shopIdUuidDTO.getShopId());
        return shopUuidDTO;
    }
}
