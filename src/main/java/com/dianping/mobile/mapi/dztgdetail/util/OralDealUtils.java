package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.meituan.mdp.boot.starter.pigeon.util.MdpEnvUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTagNameEnum;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.APP_KEY;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.MEDICAL_SAFE_TREAT_ZDC_TAGID;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.MedicalConstant.*;

public class OralDealUtils {
    public static boolean isSafeDenture(DealCtx ctx){
        //当前团详页面携带的门店（shopid）是否命中「安心补牙」POI标签，并且团单是否命中[安心补牙]保障标签
        Long safeDentureTag = getSafeTreatZdcId(SAFE_DENTURE_ZDC_TAG);
        if (safeDentureTag == null){
            return false;
        }
        return hasDisplayPOITag(ctx,safeDentureTag) && hasGuaranteeTagCode(ctx,GuaranteeTagNameEnum.ANXIN_MEDICAL_FILL_GUARANTEE.getCode());
    }

    public static boolean isSafeImplant(DealCtx ctx) {
        //当前团详页面携带的门店（shopid）是否命中「安心种植」POI标签，并且团单是否命中[安心种植]保障标签
        Long safeImplantTag = getSafeTreatZdcId(SAFE_IMPLANT_ZDC_TAG);
        if (safeImplantTag == null){
            return false;
        }
        return hasDisplayPOITag(ctx,safeImplantTag) && hasGuaranteeTagCode(ctx, GuaranteeTagNameEnum.ANXIN_MEDICAL_IMPLANT_GUARANTEE.getCode());
    }

    public static boolean hasDisplayPOITag(DealCtx ctx, Long tagId) {
        if (tagId == null){
            return false;
        }
        Map<Long, List<DisplayTagDto>> dpShopId2TagsMap = ctx.getDpShopId2TagsMap();
        if (MapUtils.isEmpty(dpShopId2TagsMap)) {
            return false;
        }
        List<DisplayTagDto> displayTagDtos = dpShopId2TagsMap.get(ctx.getDpLongShopId());
        if (CollectionUtils.isEmpty(displayTagDtos)) {
            return false;
        }
        return displayTagDtos.stream().anyMatch(tag -> tagId.equals(tag.getTagId()));
    }

    private static boolean hasGuaranteeTagCode(DealCtx ctx, Integer guaranteeTagCode){
        if (CollectionUtils.isEmpty(ctx.getSafeMedicalTag())){
            return false;
        }
        return ctx.getSafeMedicalTag().contains(guaranteeTagCode);
    }

    public static String buildSafeTreatOralJumpUrl(Long shopId4Platform,Integer platform, String guaranteeIp,String originJumpUrl){
        if (StringUtils.isEmpty(originJumpUrl) || shopId4Platform == null || platform == null || guaranteeIp == null){
            return StringUtils.EMPTY;
        }
        //displayScene展示场景，2标识从团详页跳转，此处写死
        return originJumpUrl+ "&shopId="+shopId4Platform+"&platform="+platform+"&guaranteeIp="+guaranteeIp+"&displayScene="+DISPLAY_SCENE_TEETH;

    }


    public static Long getSafeTreatZdcId(String key){
        Map<String, Long> safeTreatZdcMap = Lion.getMap(APP_KEY, MEDICAL_SAFE_TREAT_ZDC_TAGID, Long.class, new HashMap<>());
        if (MapUtils.isEmpty(safeTreatZdcMap)){
            return null;
        }
        return safeTreatZdcMap.get(key);
    }

}
