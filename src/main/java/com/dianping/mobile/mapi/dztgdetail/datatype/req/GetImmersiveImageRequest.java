package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.DecryptedField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-08-24
 */
@Data
@TypeDoc(description = "查看全部头图入参")
@MobileRequest
public class GetImmersiveImageRequest implements IMobileRequest, Serializable {
    @Deprecated
    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，原int类型")
    @MobileRequest.Param(name = "dealGroupId", required = true)
    private Integer dealGroupId;

    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，原int类型")
    @MobileRequest.Param(name = "stringDealGroupId")
    private String stringDealGroupId;

    @FieldDoc(description = "分页起始，从0开始")
    @MobileRequest.Param(name = "start", required = true)
    private Integer start;

    @FieldDoc(description = "分页大小")
    @MobileRequest.Param(name = "limit", required = true)
    private Integer limit;

    @FieldDoc(description = "商铺ID")
    @MobileRequest.Param(name = "shopId")
    private Long shopId;
    @MobileRequest.Param(name = "shopIdEncrypt")
    @DecryptedField(targetFieldName = "shopId")
    private String shopIdEncrypt;

    @FieldDoc(description = "筛选项信息，单选，来源于getimmersivefilter.bin接口selectValue字段")
    @MobileRequest.Param(name = "selectValue")
    private String selectValue;

    @FieldDoc(description = "团单类型", rule = "点评团单:1；美团团单：2")
    private Integer dealGroupType;

    @FieldDoc(description = "团单二级类目ID")
    private Integer categoryId;

    @FieldDoc(description = "使用场景", rule = "self: 本团购参考款式; recommend: 更多推荐款式; order：订单详情页调用; 不填默认是：self")
    @MobileRequest.Param(name = "sceneCode")
    private String sceneCode;

    @FieldDoc(description = "城市id", rule = "美团平台为美团城市ID，点评平台为点评城市ID")
    @MobileRequest.Param(name = "cityId")
    private Integer cityId;

    @FieldDoc(description = "用户经度(注意app和小程序经纬度类型不同)")
    @MobileRequest.Param(name = "userLng")
    private Double userLng;

    @FieldDoc(description = "用户纬度(注意app和小程序经纬度类型不同)")
    @MobileRequest.Param(name = "userLat")
    private Double userLat;

    @FieldDoc(description = "页面来源")
    @MobileRequest.Param(name = "pageSource")
    private String pageSource;

    @FieldDoc(description = "扩展信息，根据不同pageSource可能有不同扩展信息")
    @MobileRequest.Param(name = "extParam")
    private String extParam;

    @FieldDoc(description = "款式ID")
    @MobileRequest.Param(name = "infoContentId")
    private Long infoContentId;

    private Long mtDealGroupId;
    private Long dpDealGroupId;
}
