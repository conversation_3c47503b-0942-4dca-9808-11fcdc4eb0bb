package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedModuleExtraReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedMoreDealsReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleExtraDTO;
import com.dianping.mobile.mapi.dztgdetail.facade.UnifiedModuleExtraFacade;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.meituan.servicecatalog.api.annotations.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

import static com.dianping.mobile.mapi.dztgdetail.util.AntiCrawlerUtils.antiUnauthenticLogin;

/**
 * Created by zuomlin on 2018/12/13.
 */
@InterfaceDoc(displayName = "团购详情更多定制化模块请求统一接口",
        type = "restful",
        description = "团购详情更多定制化模块请求统一接口，当团购详情通用模块无法满足自定义需求时，接口满足自定义",
        scenarios = "团购详情更多定制化模块请求统一接口，当团购详情通用模块无法满足自定义需求时，接口满足自定义",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "yangquan02"
)
@Controller("general/platform/dztgdetail/unifiedmoduleextra.bin")
@Action(url = "unifiedmoduleextra.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class UnifiedModuleExtraAction extends AbsAction<UnifiedModuleExtraReq> {

    @Autowired
    private UnifiedModuleExtraFacade unifiedModuleExtraFacade;

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "unifiedmoduleextra.bin",
            displayName = "团购详情更多定制化模块请求统一接口",
            description = "团购详情更多定制化模块请求统一接口，当团购详情通用模块无法满足自定义需求时，接口满足自定义",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "unifiedmoduleextra.bin请求参数",
                            type = UnifiedMoreDealsReq.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "result", description = "用户评论", type = UnifiedModuleExtraReq.class)},
            restExampleUrl = "http://mapi.51ping.com/general/platform/dztgdetail/unifiedmoduleextra.bin?",
            restExamplePostData = "{}",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    ),
            }
    )
    @Override
    protected IMobileResponse validate(UnifiedModuleExtraReq request, IMobileContext iMobileContext) {
        IdUpgradeUtils.processProductIdForUnifiedModuleExtraReq(request, "unifiedmoduleextra.bin");
        if (request == null || request.getDealGroupId() <= 0) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @Override
    protected IMobileResponse execute(UnifiedModuleExtraReq request, IMobileContext iMobileContext) {
        try {
            EnvCtx envCtx = initEnvCtx(iMobileContext);
            // 拦截没有授权的登录
            antiUnauthenticLogin(iMobileContext);
            ModuleExtraDTO result = unifiedModuleExtraFacade.queryUnifiedModuleExtraDTO(request, envCtx, iMobileContext);
            if (result == null || CollectionUtils.isEmpty(result.getModuleConfigDos())) {
                return Resps.NoDataResp;
            }
            return new CommonMobileResponse(result);
        } catch (Exception e) {
            logger.error("unifiedmoduleextra.bin failed, params: request={}, context ={}", request, iMobileContext);
        }
        return Resps.SYSTEM_ERROR;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
