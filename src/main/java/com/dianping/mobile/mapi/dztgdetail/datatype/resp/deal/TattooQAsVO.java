package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@TypeDoc(description = "纹绣问答返回视图模块")
@MobileDo(id = 0x27b4)
public class TattooQAsVO implements Serializable {

    @FieldDoc(description = "模块标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "问答列表")
    @MobileDo.MobileField(key = 0xb462)
    private List<TattooQAModel> qas;

    @FieldDoc(description = "额外信息")
    @MobileDo.MobileField(key = 0x143f)
    private String extraInfo;

    @FieldDoc(description = "额外信息icon")
    @MobileDo.MobileField(key = 0xc7dd)
    private String extraInfoIcon;
}
