package com.dianping.mobile.mapi.dztgdetail.datatype.context;

import com.dianping.account.dto.MeituanUserInfoDTO;
import com.dianping.account.dto.UserAccountDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.enums.FitnessCrossIdentityEnum;
import com.dianping.technician.common.api.domain.TechnicianResp;
import com.google.common.util.concurrent.SettableFuture;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.beautycontent.creator.application.edu.technicianVideo.dto.EduTechnicianVideoListForCDTO;
import com.sankuai.clr.content.process.thrift.dto.leads.resp.BatchQueryLeadsInfoRespDTO;
import com.sankuai.leads.count.thrift.dto.resp.QueryLeadsSalesRespDTO;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.resp.GetGrouponMemberDiscountInfoRespDTO;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.resp.GetGrouponMemberInfoRespDTO;
import com.sankuai.nibmktproxy.queryclient.proxy.PromotionResponse;
import com.sankuai.technician.info.online.dto.OnlineTechWithAttrsDTO;
import lombok.Data;

import java.util.List;
import java.util.concurrent.Future;

@Data
public class FutureCtx {

    private Future dealGroupFuture;
    private Future channelFuture;
    private Future timesCardFuture;
    private Future discountFuture;
    private Future promoFuture;
    private Future promoWithTimeFuture;
    private Future discountPromoFuture;
    private Future promoMergeFuture;
    private Future dealActivityFuture;
    private Future dealTitleActivityFuture;
    private Future dealPreActivitiesFuture;
    private Future antiFleeOrderFuture;
    private Future attrFuture;
    private Future saleFuture;
    private Future shopSaleFuture;
    private Future stockFuture;
    private Future dpIdFuture;
    private Future orderUrlFuture;
    private Future dpShopIdFuture;

    private Future styleFuture;
    private Future extraStyleFuture;
    private Future dzxFuture;
    private Future mtDealDtoListFuture;
    private Future mtDealDtoListFutureParall;
    private Future dealGroupDetailFuture;
    private Future dealGroupStructedDetailFuture;
    private Future dealStockListFuture;
    private Future favorFuture;
    private Future beautyStructureFuture;
    private Future beautyNailAttributeFuture;
    private Future beautyNailFuture4Title;
    private Future beautyNailFuture4Detail;
    private Future ktvStructDealDetailFuture;

    private SettableFuture dealGroupBuyingResvEntranceFuture;

    private Future pinProductBriefFuture;
    private Future bonusExposureFuture;
    private Future voucherFuture;
    private Future bestShopFuture;
    private Future fastBestShopFuture;
    private Future drivingShopFuture;
    private Future imFuture;
    private Future csFuture;
    private Future bookFuture;
    private SettableFuture shopCategoryFuture;

    private SettableFuture dpPoiDtoFuture;
    private Future mtPoiDTOFuture;
//    private Future dealBookFuture;
    private Future preSaleFuture;
    private Future judgeDpIdReserveOnlineFuture;
    private Future dealGroupThirdPartyFuture;
    private Future poiPhoneFuture;
    private Future customerFuture;
    private Future previewShopIdFuture;
    private Future sinaiDpPoiFuture;
    private Future poiBizAttrFuture;
    private Future bookStatusFuture;
    private Future mtShowTypeDTOFuture;
    private Future exhibitInfoFuture;
    private Future contentSearchFuture;
    private Future contentTagFuture;
    private Future exhibitInfoFilterFuture;
    private Future sortedExhibitInfoFuture;
    private Future exhibitTagInfoFuture;
    private Future skuOptionsFuture;
    private Future digestFuture;
    private Future topExhibitInfoFuture;
    private Future attributeKeyFuture;
    private Future oldCaseInfoFuture;
    private Future usePlatformHeadPicFuture;

    /**
     * 创建对应接口对应的future对象
     */

    private Future<?> commissionFuture;
    private Future<?> promotionFuture;

    // 价格服务相关
    private Future<?> joyDiscountCardPriceFuture;
    private Future<?> idlePromoPriceFuture;
    private Future<?> beautyCouponBagPromoPriceFuture;
    private Future<?> normalPriceFuture;
    private Future<?> memberNormalPriceFuture;
    private Future<?> dealPromoPriceFuture;
    private Future<?> shopMemberPromoPriceFuture;//商家会员
    private Future<?> atmosphereBarAndGeneralPromoDetailFuture;
    private Future<?> costEffectivePriceFuture;

    private Future<?> dzCardFuture;//权益台
    private Future<?> userStateFuture;//新老客

    private Future<PromotionResponse> promoProxyFuture;

    private Future displayControlFuture;

    private Future relatedShopsFuture;
    private Future dealReceiptFuture;
    private Future relatedBehaviorFuture;

    private Future singleDealGroupDtoFuture;
    private Future timeStockFuture;
    private Future<GetGrouponMemberInfoRespDTO> memberInfoRespDTOFuture;
    private Future<GetGrouponMemberDiscountInfoRespDTO> memberDiscountInfoRespDTOFuture;

    /**
     * 营销券信息
     */
    private Future excludeProxyCouponListFuture;

    private Future dealDetailStructuredFuture;

    /**
     * 美甲款式信息
     */
    private Future beautyNailStyleFuture;
    /**
     * 商场场内店 门店详情信息
     */
    private List<Future> shoppingMallDpPoiDTOFutureList;
    /**
     * 业务id转平台id
     */
    private Future bizProductIdToPtIdFuture;
    /**
     * 价保服务信息
     */
    private Future guaranteeQueryFuture;

    private Future additionalDealGroupDtoFuture;

    /**
     * 团购主题
     */
    private Future dealThemeFuture;

    /**
     * 基础映射转换future
     */
    private Future UserIdMapperFuture;

    /**
     * 教育的在线老师信息
     */
    private Future<TechnicianResp<List<OnlineTechWithAttrsDTO>>> eduOnlineTeacherInfoFuture;

    /**
     * 私域直播future
     */
    private Future<?> privateLiveFuture;
    private Future<?> privateLiveDistributionInfoFuture;


    private Future contentProcessFuture;


    /**
     * 教育老师视频列表
     */
    private Future<RemoteResponse<EduTechnicianVideoListForCDTO>> eduTechnicianVideoListFuture;

    /**
     * 预约试听人数
     */
    private Future<QueryLeadsSalesRespDTO> queryLeadsSalesRespDTOFuture;

    /**
     * 赠品相关
     */
    private Future<?> playFuture;

    /**
     * 玩乐活动相关
     */
    private Future<?> playActivityFuture;

    /**
     * 丽人纹绣配置数据
     */
    private Future<?> beautyTattooFuture;

    /**
     * 预约试听资格相关
     */
    private Future<BatchQueryLeadsInfoRespDTO> queryLeadsInfoRespDTOFuture;

    /**
     * 门店标签future
     */
    private Future<?> shopTagFuture;

    /**
     * 健身通相关
     */
    private Future<Boolean> fitnessCrossAvailableCouponFuture;
    private Future<FitnessCrossIdentityEnum> fitnessCrossIdentityEnumFuture;
    private Future<String> fitnessCrossOrderPageUrlFuture;
    private Future<UserAccountDTO> dpUserAccountDTOFuture;
    private Future<MeituanUserInfoDTO> mtUserAccountDTOFuture;

    /**
     * LE门店标签future
     */
    private Future<?> leShopTagFuture;

    /**
     * 台球自助开台
     */
    private Future<?> autoOpenTableFuture;

    /**
     * 团购次卡先用后付
     */
    private Future<?> userAndProductCreditPayFuture;

    /**
     * 留资信息future
     */
    private Future leadsInfoFuture;

    /**
     * 超值特惠团购Future
     */
    private Future specialValueFuture;
    /**
     * 商家平均接单时间Future
     */
    private Future<?> shopAvgOrderTimeFuture;
    private Future<?> preOrderSaleFuture;

    /**
     * 超值特惠团购属性 Future*
     */
    private Future activityDealConfigFuture;

    /**
     * 门店权益Future
     */
    private Future shopInterestFuture;

    /**
     * VR信息Future
     */
    private Future vrInfoFuture;
    /**
     * 春节不打烊
     */
    private Future springFestivalTagsFuture;
    /**
     * 商户预约信息
     */
    private Future shopBookInfoFuture;
    /**
     * 私域直播-团详分享微信昵称
     */
    private Future distributionRpcFuture;
    /**
     * 家政强预订Future
     */
    private Future preOrderDealRpcFuture;

    /**
     * 统一团详通用模块
     */
    private Future commomModuleFuture;

    /**
     * 新团详交易模块Future
     */
    private Future<?> productDetailTradeModuleFuture;

    /**
     * 导航栏搜索模块Future
     */
    private Future<?> navBarSearchFuture;

    /**
     * 团购关联商户数
     */
    private Future<?> dealShopQtyBySearchFuture;

    /**
     * 商品销量Future
     */
    private Future<?> productSaleFuture;

}
