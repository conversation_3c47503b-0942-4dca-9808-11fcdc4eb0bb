package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.struct;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;
import java.util.List;

@TypeDoc(description = "到综团单结构化属性数据")
@MobileDo(id = 0x39f6)
public class DealDetailStructAttrDo implements Serializable {

    @FieldDoc(description = "属性名称")
    @MobileField(key = 0x491e)
    private String attrName;

    @FieldDoc(description = "属性值")
    @MobileField(key = 0x9ddc)
    private List<String> attrValues;

    @FieldDoc(description = "属性图标url")
    @MobileField(key = 0x3c48)
    private String icon;

    /**
     * @see com.dianping.mobile.api.dztg.datatypes.enums.StructAttrStyle
     */
    @FieldDoc(description = "属性样式",rule = "@see com.dianping.mobile.api.dztg.datatypes.enums.StructAttrStyle")
    @MobileField(key = 0x1b3a)
    private int style;

    public String getAttrName() {
        return attrName;
    }

    public void setAttrName(String attrName) {
        this.attrName = attrName;
    }

    public List<String> getAttrValues() {
        return attrValues;
    }

    public void setAttrValues(List<String> attrValues) {
        this.attrValues = attrValues;
    }

    public int getStyle() {
        return style;
    }

    public void setStyle(int style) {
        this.style = style;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }
}