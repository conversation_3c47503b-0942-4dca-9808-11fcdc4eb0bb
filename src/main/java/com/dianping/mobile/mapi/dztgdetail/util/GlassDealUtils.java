package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.entity.GlassProductTagRuleConfig;
import com.meituan.mdp.boot.starter.pigeon.util.MdpEnvUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;

import org.jetbrains.annotations.Nullable;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.APP_KEY;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.GLASS_GENUINE_GUARANTEE_CONFIG;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.MedicalConstant.*;

public class GlassDealUtils {

    public static boolean isSafePtometry(DealCtx ctx){
        //当前团详页面携带的门店（shopid）是否命中「安心配镜」POI标签，若命中则展示标识，若不命中则不展示此标识
        Long safePrometryTagForEnv = MdpEnvUtils.isTestEnv() ? SAFE_PTOMETRY_TAGID_TEST : SAFE_PTOMETRY_TAGID;
        return hasDisplayTag(ctx,safePrometryTagForEnv);
    }

    public static boolean isGenuineGuarantee(DealCtx ctx) {
        Long gunuineGuaranteeTagForEnv = MdpEnvUtils.isTestEnv() ? GUNUINE_GUARANTEE_TAGID_TEST : GUNUINE_GUARANTEE_TAGID;
        if (!hasDisplayTag(ctx, gunuineGuaranteeTagForEnv)) {
            return false;
        }
        return hasAuthorizedShopTag(ctx);
    }

    public static boolean hasDisplayTag(DealCtx ctx, Long tagId) {
        Map<Long, List<DisplayTagDto>> dpShopId2TagsMap = ctx.getDpShopId2TagsMap();
        if (MapUtils.isEmpty(dpShopId2TagsMap)) {
            return false;
        }
        List<DisplayTagDto> displayTagDtos = dpShopId2TagsMap.get(ctx.getDpLongShopId());
        if (CollectionUtils.isEmpty(displayTagDtos)) {
            return false;
        }
        return displayTagDtos.stream().anyMatch(tag -> tagId.equals(tag.getTagId()));
    }

    public static boolean hasAuthorizedShopTag(DealCtx ctx) {
        if (ctx == null || ctx.getDpShopId2TagsMap() == null || ctx.getDealGroupDTO() == null) {
            return false;
        }
        Set<Long> shopTagIds = getShopTagIds(ctx);
        if (shopTagIds == null) return false;
        Set<Long> productTagIds = getProductTagIds(ctx);
        if (productTagIds == null) return false;
        List<GlassProductTagRuleConfig> glassProductTagRuleConfigs = Lion.getList(APP_KEY,GLASS_GENUINE_GUARANTEE_CONFIG, GlassProductTagRuleConfig.class);
        if (glassProductTagRuleConfigs == null) {
            return false;
        }
        return glassProductTagRuleConfigs.stream().anyMatch(config ->
                shopTagIds.contains(config.getShopTag()) &&
                        productTagIds.contains(config.getHitProductTag()) &&
                        !productTagIds.contains(config.getMissProductTag()));
    }

    private static Set<Long> getProductTagIds(DealCtx ctx) {
        List<DealGroupTagDTO> tags = ctx.getDealGroupDTO().getTags();
        if (tags == null) {
            return null;
        }
        Set<Long> productTagIds = tags.stream().map(DealGroupTagDTO::getId).collect(Collectors.toSet());
        return productTagIds;
    }

    private static Set<Long> getShopTagIds(DealCtx ctx) {
        List<DisplayTagDto> displayTagDtos = ctx.getDpShopId2TagsMap().get(ctx.getDpLongShopId());
        if (CollectionUtils.isEmpty(displayTagDtos)) {
            return null;
        }
        Set<Long> shopTagIds = displayTagDtos.stream().map(DisplayTagDto::getTagId).collect(Collectors.toSet());
        return shopTagIds;
    }

    public static String getBizParam(DealCtx ctx) {
        if (ctx == null || ctx.getDpShopId2TagsMap() == null || ctx.getDealGroupDTO() == null) {
            return null;
        }
        Set<Long> shopTagIds = getShopTagIds(ctx);
        Set<Long> productTagIds = getProductTagIds(ctx);
        if (shopTagIds == null || productTagIds == null) {
            return null;
        }
        List<GlassProductTagRuleConfig> glassProductTagRuleConfigs = Lion.getList(APP_KEY, GLASS_GENUINE_GUARANTEE_CONFIG, GlassProductTagRuleConfig.class);
        if (glassProductTagRuleConfigs == null) {
            return null;
        }
        String result = glassProductTagRuleConfigs.stream()
                .filter(config -> shopTagIds.contains(config.getShopTag()) && productTagIds.contains(config.getHitProductTag()) && !productTagIds.contains(config.getMissProductTag()))
                .map(config -> String.valueOf(config.getBizParam()))
                .collect(Collectors.joining(","));
        return result.isEmpty() ? null : result;
    }
}
