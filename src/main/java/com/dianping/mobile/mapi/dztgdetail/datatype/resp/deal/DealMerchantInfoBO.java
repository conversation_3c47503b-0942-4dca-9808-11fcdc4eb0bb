package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: huqi
 * @Date: 2020/3/12 3:39 下午
 */
@Data
@TypeDoc(description = "到综团详商家附加信息模型")
public class DealMerchantInfoBO implements Serializable {
    /**
     * 公益商家
     */
    @MobileDo.MobileField(key = 0x8d0c)
    private WelfareMerchantBO welfare;

}
