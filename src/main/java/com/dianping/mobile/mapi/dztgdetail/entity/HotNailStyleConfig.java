package com.dianping.mobile.mapi.dztgdetail.entity;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ActionTextVO;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-01-05
 * @desc 热门款式模块配置项
 */
@Data
public class HotNailStyleConfig {
    private String title;
    private String subTitle;
    /**
     * 主题样式下查看全部文案
     */
    private String moreStyleTextA;
    /**
     * 标签样式下查看全部文案
     */
    private String moreStyleTextB;
    private String moreStyleIcon;
    private String mtMoreStyleUrl;
    private String dpMoreStyleUrl;
    private ActionTextVO actionText;
    private String bgImgUrl;
    /**
     * 款式封面拼图的数量
     */
    private Integer styleCount;
    /**
     * 款式标签展示数量
     */
    private Integer tagCount;
}
