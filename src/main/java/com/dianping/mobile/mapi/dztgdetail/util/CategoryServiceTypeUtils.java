package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.apache.commons.lang.StringUtils;

import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2024/12/24
 */
public class CategoryServiceTypeUtils {

    /**
     * 获取类目和serviceType拼接后的key
     * @param ctx
     * @param connectSymbol
     * @return
     */
    public static String getCateKey(DealCtx ctx, String connectSymbol) {
        DealGroupDTO dealGroup = ctx.getDealGroupDTO();
        if (dealGroup == null) {
            return StringUtils.EMPTY;
        }
        DealGroupCategoryDTO category = dealGroup.getCategory();
        Long categoryId = category.getCategoryId();
        String serviceType = category.getServiceType();
        if (Objects.isNull(category) || categoryId<= 0) {
            return StringUtils.EMPTY;
        }

        return StringUtils.isNotEmpty(serviceType) ? categoryId + connectSymbol + serviceType : String.valueOf(categoryId);
    }
}
