package com.dianping.mobile.mapi.dztgdetail.button.edu;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.EducationDealAttrUtils;
import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.constants.EduConstant;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BannerTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBanner;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper;
import com.dianping.mobile.mapi.dztgdetail.util.EduDealUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;


public class EduFreeTrialButtonBuilder extends AbstractButtonBuilder {
    @Override
    protected void doBuild(DealCtx context, ButtonBuilderChain chain) {
        if (EduDealUtils.isEduOnlineCourseDeal(context) && EduDealUtils.getLegalVideoNum(context) > 0) {
            addButtonForOnlineTrial(context);
        }
        // 正价课，集训营 预约试听
        if (EduDealUtils.isVocationalEduCourseOrCamp(context.getDealGroupDTO()) && EducationDealAttrUtils.hasFreeAudition(context.getDealGroupDTO())) {
            addButtonForBookingTrial(context);
        }

        // 短期课
        if (EduDealUtils.checkShortClassHasButton(context)) {
            addButtonForBookingTrial(context);
        }
        //职业培训，非考公考编
        if (EduDealUtils.isVocationTraining(context) && !EduDealUtils.isCivilExam(context.getDealGroupDTO()) && EduDealUtils.getLegalVideoNum(context) > 0) {
            addButtonForOnlineTrial(context);
        }
        chain.build(context);
    }

    private static void addButtonForBookingTrial(DealCtx context) {
        DealBuyBtn dealBuyBtn = new DealBuyBtn(true, "预约试听");
        dealBuyBtn.setDetailBuyType(BuyBtnTypeEnum.TRIAL_CLASS.getCode());
        dealBuyBtn.setRedirectUrl(UrlHelper.getOrderUrl(context));
        context.addButton(dealBuyBtn);
        bannerBuild(context);
    }

    private static void addButtonForOnlineTrial(DealCtx context) {
        DealBuyBtn dealBuyBtn = new DealBuyBtn(true, "在线试听");
        dealBuyBtn.setDetailBuyType(BuyBtnTypeEnum.TRIAL_CLASS.getCode());
        if (context.isMt()) {
            dealBuyBtn.setRedirectUrl(String.format(EduConstant.mtTrialClassJumpUrlTemplate, context.getMtId(), context.getMtLongShopId()));
        } else {
            dealBuyBtn.setRedirectUrl(String.format(EduConstant.dpTrialClassJumpUrlTemplate, context.getDpId(), context.getDpLongShopId(), context.getDpShopUuid()));
        }
        context.addButton(dealBuyBtn);
        bannerBuild(context);
    }


    private static void bannerBuild(DealCtx context) {
        DealBuyBanner banner = new DealBuyBanner();
        leadTextBuild(context, banner);
        banner.setShow(StringUtils.isNotEmpty(banner.getLeadText()));
        banner.setBannerType(BannerTypeEnum.BeautyCouponBag.getType());
        banner.setLeadTextColor(EduConstant.EDU_ONLINE_BANNER_TEXT_COLOR);
        banner.setBackGroundColor(Lists.newArrayList(EduConstant.EDU_ONLINE_BANNER_BACKGROUND_COLOR));
        context.getBuyBar().setBuyBanner(banner);
    }

    public static void leadTextBuild(DealCtx context, DealBuyBanner banner) {
        if (EduDealUtils.isEduOnlineCourseDeal(context) && EduDealUtils.getLegalVideoNum(context) > 0) {
            banner.setLeadText(String.format(EduConstant.ONLINE_COURSE_BUTTON_BANNER, EduDealUtils.getLegalVideoNum(context)));
        }

        if (EduDealUtils.isVocationalEduCourseOrCamp(context.getDealGroupDTO())) {
            int num = EduDealUtils.getTrialPeopleNum(context);
            String trailClassNum = EducationDealAttrUtils.getTrailClassNums(context.getDealGroupDTO());
            if (StringUtils.isEmpty(trailClassNum)) {
                return;
            }
            StringBuilder leadText = new StringBuilder(String.format(EduConstant.VOCATIONAL_COURSE_BUTTON_BANNER_PREFIX, trailClassNum));
            if (num > 0) {
                leadText.append(String.format(EduConstant.VOCATIONAL_COURSE_BUTTON_BANNER_SUFFiX, num));
            }
            banner.setLeadText(leadText.toString());
        }

        if (EduDealUtils.checkShortClassHasButton(context)) {
            StringBuilder leadText = new StringBuilder(String.format(EduConstant.SHORT_CLASS_BUTTON_BANNER_PREFIX));
            int num = EduDealUtils.getTrialPeopleNum(context);
            if (num > 5) {
                leadText.append(String.format(EduConstant.VOCATIONAL_COURSE_BUTTON_BANNER_SUFFiX, num));
            }
            banner.setLeadText(leadText.toString());
        }

    }
}
