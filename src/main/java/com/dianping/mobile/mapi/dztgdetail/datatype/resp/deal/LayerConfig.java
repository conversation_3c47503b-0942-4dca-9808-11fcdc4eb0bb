package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

@TypeDoc(description = "价保浮层信息")
@MobileDo(id = 0x30c1)
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class LayerConfig implements Serializable {
    @FieldDoc(description = "价保标签")
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;

    @FieldDoc(description = "价保标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "价保跳链")
    @MobileDo.MobileField(key = 0x774e)
    private String jumpUrl;

    @FieldDoc(description = "价保描述")
    @MobileDo.MobileField(key = 0xfebf)
    private String desc;

    /**
     * 目前已有类型可见：com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.LayerConfigTypeEnum
     * 新增了类型，请维护到LayerConfigTypeEnum中
     */
    @FieldDoc(description = "价保类型 1-退改协议，2-价保，3-买贵必赔，4-游乐险")
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;

    @FieldDoc(description = "信息展示类型，0-纯文本，1-高亮")
    @MobileDo.MobileField(key = 0x2e2a)
    private int textType;

    @FieldDoc(description = "不同样式对应的值，比如高亮下发对应颜色值")
    @MobileDo.MobileField(key = 0xddfb)
    private String textStyle;

    @FieldDoc(description = "小程序跳链")
    @MobileDo.MobileField(key = 0xf6a0)
    private String miniJumpUrl;
}
