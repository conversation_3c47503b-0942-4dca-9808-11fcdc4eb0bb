package com.dianping.mobile.mapi.dztgdetail.datatype.resp.other;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 模型见
 * https://km.sankuai.com/collabpage/**********#b-e65d69ccb6cd461c861ec723996e22ad
 */
@Data
public class DigestUgcPicDetail implements Serializable {
    private String picUrl;
    private Double aestheticScore;
    private long reviewId;
    private int reviewType;
    private List<Integer> tags;
}
