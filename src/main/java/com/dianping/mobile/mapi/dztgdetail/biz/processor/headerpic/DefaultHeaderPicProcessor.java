package com.dianping.mobile.mapi.dztgdetail.biz.processor.headerpic;

import com.dianping.cat.Cat;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ContentType;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2023/8/20
 */
@Component("defaultHeaderPicProcessor")
@Slf4j
public class DefaultHeaderPicProcessor extends AbstractHeaderPicProcessor {

    @Override
    public void fillPicScale(DealCtx ctx, List<ContentPBO> result, DealGroupPBO dealGroupPBO) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }

        // 读取行业头图尺寸配置，如果没有定制尺寸，则走兜底逻辑16:9
        String scale = LionConfigUtils.getCategoryHeaderScale(ctx.getCategoryId());
        result.forEach(contentPBO -> {
            if (contentPBO.getType() == ContentType.PIC.getType() || contentPBO.getType() == ContentType.VIDEO.getType()) {
               contentPBO.setScale(scale);
            }
        });

        return;
    }

    @Override
    public boolean matchShowExhibit(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.biz.processor.headerpic.DefaultHeaderPicProcessor.matchShowExhibit(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        return false;
    }
}

