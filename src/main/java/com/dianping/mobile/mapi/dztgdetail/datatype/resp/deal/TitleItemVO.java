package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/8/7 10:47
 */
@Data
@TypeDoc(description = "团购详情页标题区组件模型")
@MobileDo(id = 0xd040)
public class TitleItemVO implements Serializable {
    @FieldDoc(description = "组件文案")
    @MobileDo.MobileField(key = 0x451b)
    private String text;

    @FieldDoc(description = "前缀icon的URL地址")
    @MobileDo.MobileField(key = 0xb548)
    private String preIcon;
}
