package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MemberExclusiveWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.SwitchUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.cache2.CacheClient;
import com.sankuai.athena.inf.cache2.CacheClientConfig;
import com.sankuai.athena.inf.cache2.CacheKey;
import com.sankuai.athena.inf.cache2.loader.DataLoader;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.resp.GetGrouponMemberInfoRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;
import java.util.Random;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * @Desc
 * <AUTHOR>
 * @Date 2023/5/29 16:26
 */
@Slf4j
public class MemberExclusiveProcessor extends AbsDealProcessor{

    /**
     * 会员专属团单已扩展到除了商场行业的其他行业。 该key由于历史原因命名成MALL_开头，这里不做变更
     */
    public static final String MALL_MEMBER_DEAL = "MALL_MEMBER_DEAL";
    @Autowired
    private MemberExclusiveWrapper memberExclusiveWrapper;

    private static final String REDIS_DEAL_MAPPER_MEMBER_EXCLUSIVE = "mapper_deal_member_exclusive";

    private static final int CACHE_EXPIRE_TIME = 1296000;

    private static final int CACHE_REFRESH_TIME = 0;

    private static TypeReference<Boolean> memberExclusiveCacheTypeReference = new TypeReference<Boolean>() {};


    private static CacheClientConfig cacheClientConfig = new CacheClientConfig("redis-dealgroup");

    private static CacheClient cacheClient = AthenaInf.getCacheClient(cacheClientConfig);

    @Override
    public boolean isEnable(DealCtx ctx) {
        return GreyUtils.hitMemberLogic(ctx);
    }

    @Override
    public void prepare(DealCtx ctx) {
        if (!GreyUtils.hitMemberLogic(ctx)) {
            return;
        }

        boolean isCache = SwitchUtils.isMemberExclusiveEnable();
        ctx.setMemberExclusive(isCache?getMemberExclusive(ctx):memberExclusiveWrapper.isMemberExclusive(ctx));
        if (ctx.isMemberExclusive()) {
            ctx.getFutureCtx().setMemberInfoRespDTOFuture(memberExclusiveWrapper.getMallDealGroupMemberInfo(ctx));
            if (ctx.getDealExtraTypes() == null) {
                ctx.setDealExtraTypes(Lists.newArrayList(MALL_MEMBER_DEAL));
            } else {
                ctx.getDealExtraTypes().add(MALL_MEMBER_DEAL);
            }
        }

    }

    public boolean getMemberExclusive(DealCtx ctx) {
        boolean isMemberExclusive;
        try {
            isMemberExclusive = Optional.ofNullable(getMemberExclusiveFromCache(ctx).get()).orElse(false);
        } catch (Exception e) {
            Cat.logError(e);
            Cat.logEvent(REDIS_DEAL_MAPPER_MEMBER_EXCLUSIVE, "getMemberExclusiveError");
            return memberExclusiveWrapper.isMemberExclusive(ctx);
        }
        return isMemberExclusive;
    }

    private CompletableFuture<Boolean> getMemberExclusiveFromCache(DealCtx ctx) {
        boolean isMt = ctx.isMt();
        long productId = ctx.isMt() ? (long) ctx.getMtId() : (long) ctx.getDpId();
        CacheKey cacheKey = new CacheKey(REDIS_DEAL_MAPPER_MEMBER_EXCLUSIVE, isMt, productId);
        DataLoader<Boolean> dataLoader = key -> {
            if(key == null) {
                return CompletableFuture.completedFuture(null);
            }
            return CompletableFuture.completedFuture(memberExclusiveWrapper.isMemberExclusive(ctx));
        };
        return cacheClient.asyncGetReadThrough(cacheKey,memberExclusiveCacheTypeReference, dataLoader, CACHE_EXPIRE_TIME + new Random().nextInt(300) , CACHE_REFRESH_TIME);
    }

    @Override
    public void process(DealCtx ctx) {
        if (!ctx.isMemberExclusive()) {
            return;
        }
        GetGrouponMemberInfoRespDTO memberInfoRespDTO = null;
        try {
            memberInfoRespDTO = ctx.getFutureCtx().getMemberInfoRespDTOFuture().get(2, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("MemberExclusiveProcessor, getMemberInfoRespDTOFuture error", e);
        }
        ctx.setMemberInfoRespDTO(memberInfoRespDTO);
    }
}
