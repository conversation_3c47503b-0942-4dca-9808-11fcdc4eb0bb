package com.dianping.mobile.mapi.dztgdetail.button.mtlive;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MtLiveSaleStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.helper.PriceHelper;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-12-25
 * @desc 美团美播小程序不可购买状态按钮构造器
 */
public class MtLiveCanNotBuyButtonBuilder extends AbstractButtonBuilder {
    /**
     * 不可购买状态集合
     */
    private static final MtLiveSaleStatusEnum canBuyStatus = MtLiveSaleStatusEnum.SNAP_UP_NOW;
    
    @Override
    protected void doBuild(DealCtx context, ButtonBuilderChain chain) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.button.mtlive.MtLiveCanNotBuyButtonBuilder.doBuild(DealCtx,ButtonBuilderChain)");
        MtLiveSaleStatusEnum mtLiveSaleStatusEnum = context.getMtLiveSaleStatusEnum();
        // 如果是美团美播小程序，则更新按钮的售卖状态和title
        if (context.isMtLiveMinApp() && mtLiveSaleStatusEnum != canBuyStatus) {
            context.setCanNotBuy(true);
            PriceDisplayDTO mtLivePrice = PriceHelper.getNormalPrice(context);
            // 如果是售卖结束，按钮文案改为已下架
            if (mtLiveSaleStatusEnum == MtLiveSaleStatusEnum.END_OF_SALE) {
                mtLiveSaleStatusEnum = MtLiveSaleStatusEnum.OUT_OF_STOCK;
            }
            DealBuyBtn button = new DealBuyBtn(false, mtLiveSaleStatusEnum.getStatusDesc());
            button.setSaleStatus(context.getMtLiveSaleStatusEnum().getStatusName());
            button.setPriceStr(formatPrice(mtLivePrice.getPrice()));
            button.setBtnText(String.format("￥%s %s", mtLivePrice.getPrice(), mtLiveSaleStatusEnum.getStatusDesc()));
            button.setBtnIcons(Lists.newArrayList());
            button.setBtnDesc(StringUtils.EMPTY);
            button.setBtnTag(buildBtnTag(mtLivePrice));
            context.addButton(button);
            chain.interrupt();
            return;
        }
        chain.build(context);
    }

    private String buildBtnTag(PriceDisplayDTO normalPrice) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.button.mtlive.MtLiveCanNotBuyButtonBuilder.buildBtnTag(com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO)");
        if (CollectionUtils.isEmpty(normalPrice.getUsedPromos())) {
            return StringUtils.EMPTY;
        }
        BigDecimal totalReducePromo = new BigDecimal(0);
        for (PromoDTO promoDTO : normalPrice.getUsedPromos()) {
            if (Objects.isNull(promoDTO) || Objects.isNull(promoDTO.getAmount())) {
                continue;
            }
            if (promoDTO.getIdentity().getPromoType() == PromoTypeEnum.NORMAL_PROMO.getType()) {
                totalReducePromo = totalReducePromo.add(promoDTO.getAmount());
            }
        }
        return totalReducePromo.compareTo(BigDecimal.ZERO) > 0 ? String.format("省¥%s",
                totalReducePromo.stripTrailingZeros().toPlainString()) : null;
    }
}
