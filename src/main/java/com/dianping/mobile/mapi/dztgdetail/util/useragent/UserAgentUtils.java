package com.dianping.mobile.mapi.dztgdetail.util.useragent;


import com.dianping.cat.Cat;
import com.sankuai.hotel.login.authenticate.api.common.ClientType;
import com.sankuai.hotel.login.authenticate.api.helper.DefaultUserAgentParser;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.sankuai.hotel.login.authenticate.api.common.ClientType.*;


/**
 * <AUTHOR>
 * @create 2023/11/23 16:19
 */
@UtilityClass
@Slf4j
public class UserAgentUtils {
    private static DefaultUserAgentParser userAgentParser = new DefaultUserAgentParser();

    private static final Pattern USERAGENT_BEAM_ANDROID_REGEX = Pattern.compile(
            "beam/com\\.meituan\\.android\\.beam/([0-9\\.]+)\\s*App/[^/]+/([0-9\\.]+)\\s*(.*)",
            2
    );

    private static final Pattern USERAGENT_BEAM_IOS_REGEX = Pattern.compile(
            ".*Beam/([0-9\\\\.]+).*",
            2
    );

    public static ClientType getClientType(String userAgent) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.util.useragent.UserAgentUtils.getClientType(java.lang.String)");
        if (StringUtils.isBlank(userAgent)){
            return null;
        }
        String ua = userAgent.toLowerCase();
        for (ClientType client : userAgentParser.getKeys()) {
            Pattern pattern = userAgentParser.getPattern(client);
            Matcher matcher = pattern.matcher(ua);
            if (matcher.find() && matcher.groupCount() == 3) {
                return client;
            }
        }
        return null;
    }

    /**
     * 判断是否是 开店宝侧登录
     * @param userAgent
     * @return
     */
    public Boolean isDpmerchant(String userAgent){
        if (StringUtils.isBlank(userAgent)){
            return Boolean.FALSE;
        }
        String ua = userAgent.toLowerCase();
        for (ClientType client : userAgentParser.getKeys()) {
            Pattern pattern = userAgentParser.getPattern(client);
            Matcher matcher = pattern.matcher(ua);
            if (matcher.find() && matcher.groupCount() == 3) {
                if (DPMERCHANT_ANDROID.equals(client)
                        || DPMERCHANT_IPHONE.equals(client)
                        || WED_MERCHANT_IPHONE.equals(client)
                        || WED_MERCHANT_ANDROID.equals(client)
                        || MERCHANTPOS_ANDROID.equals(client)
                        || MERCHANTPOS_IPHONE.equals(client)
                        || MERCHANTHOBBIT_ANDROID.equals(client)
                        || OSSMERCHANT_ANDROID.equals(client)
                        || OSSMERCHANT_IOS.equals(client)
                ){
                    return Boolean.TRUE;
                }
            }
        }
        return Boolean.FALSE;
    }


    public Boolean isApollo(String userAgent){
        if (StringUtils.isBlank(userAgent)){
            return Boolean.FALSE;
        }
        String ua = userAgent.toLowerCase();
        for (ClientType client : userAgentParser.getKeys()) {
            Pattern pattern = userAgentParser.getPattern(client);
            Matcher matcher = pattern.matcher(ua);
            if (matcher.find() && matcher.groupCount() == 3) {
                if (CRMAPP_IPHONE.equals(client)
                        || CRMAPP_ANDROID.equals(client)
                ){
                    return Boolean.TRUE;
                }
            }
        }
        return Boolean.FALSE;
    }

    public boolean isBeamApp(String userAgent) {
        if (StringUtils.isBlank(userAgent)) {
            return false;
        }
        Matcher androidMatcher = USERAGENT_BEAM_ANDROID_REGEX.matcher(userAgent);
        Matcher iosMatcher = USERAGENT_BEAM_IOS_REGEX.matcher(userAgent);
        return androidMatcher.find() || iosMatcher.find();
    }
}
