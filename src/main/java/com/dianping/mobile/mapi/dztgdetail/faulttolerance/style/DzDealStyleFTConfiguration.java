package com.dianping.mobile.mapi.dztgdetail.faulttolerance.style;

import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.req.DealBaseContextRequest;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.style.cleaner.DzDealStyleReportCleaner;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.style.executor.DzDealStyleExecutor;
import com.sankuai.athena.stability.faulttolerance.FaultToleranceConfiguration;
import com.sankuai.athena.stability.faulttolerance.core.Executor;
import com.sankuai.athena.stability.faulttolerance.mirror.MirrorConfiguration;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023-12-06
 * @desc 团详样式降级配置
 */
@Service
public class DzDealStyleFTConfiguration extends FaultToleranceConfiguration<DealBaseContextRequest, CommonMobileResponse> {
    @Resource
    private DzDealStyleExecutor dzDealStyleExecutor;

    @Resource
    private DzDealStyleReportCleaner dzDealStyleReportCleaner;

    @Override
    public String getFtName() {
        return "dzDealStyle";
    }

    @Override
    public Executor<DealBaseContextRequest, CommonMobileResponse> getMainExecutor() {
        return dzDealStyleExecutor;
    }

    @Override
    public MirrorConfiguration getMirrorConfiguration() {
        return dzDealStyleReportCleaner;
    }
}
