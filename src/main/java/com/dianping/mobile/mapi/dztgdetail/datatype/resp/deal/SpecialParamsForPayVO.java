package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2024/12/4 15:08
 */
@Data
@MobileDo(id = 0x727f)
public class SpecialParamsForPayVO implements Serializable {

    /**
     * 订单实付金额（价格预期和主接口 buyBar.buyBtns[0].priceStr 相同），单位为 分,  int 格式
     */
    @MobileDo.MobileField(key = 0xe0fc)
    private int payFeeCent;

    /**
     * 1级类目，string 格式
     */
    @MobileDo.MobileField(key = 0xc9cd)
    private String firstCategoryId;

    /**
     * 商品属性结束时间，格式是毫秒时间戳，string 格式
     */
    @MobileDo.MobileField(key = 0x6cbd)
    private String endTimestamp;

    /**
     * 商品属性开始时间，毫秒时间戳，string 格式
     */
    @MobileDo.MobileField(key = 0xbcfe)
    private String startTimestamp;

}