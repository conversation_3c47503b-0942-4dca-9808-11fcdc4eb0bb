package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.FeaturesLayer;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.LayerConfig;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 美团安心改
 */
public class ReassuredRepairUtil {

    //美团安心改标签
    public static final Long LABEL_ID = 100218044L;

    //判断当前团单是否为美团安心改
    public static boolean isTagPresent(DealCtx ctx) {
        return ctx.getDealGroupDTO() != null && CollectionUtils.isNotEmpty(ctx.getDealGroupDTO().getTags())
                && ctx.getDealGroupDTO().getTags().stream()
                .map(DealGroupTagDTO::getId)
                .collect(Collectors.toList())
                .contains(LABEL_ID);
    }

    public static void buildReassuredRepairLayerConfig(DealCtx ctx, LayerConfig layerConfig, FeaturesLayer featuresLayer) {
        if (ReassuredRepairUtil.isTagPresent(ctx)) {
            List<ServiceProjectAttrDTO> serviceProjectAttrs = Optional.of(ctx)
                    .map(DealCtx::getDealGroupDTO)
                    .map(DealGroupDTO::getServiceProject)
                    .map(DealGroupServiceProjectDTO::getMustGroups)
                    .filter(mustGroups -> !mustGroups.isEmpty())
                    .map(mustGroups -> mustGroups.get(0))
                    .map(MustServiceProjectGroupDTO::getGroups)
                    .filter(groups -> !groups.isEmpty())
                    .map(groups -> groups.get(0))
                    .map(ServiceProjectDTO::getAttrs)
                    .orElse(Collections.emptyList());

            Map<String, ServiceProjectAttrDTO> attrMap = serviceProjectAttrs.stream()
                    .collect(Collectors.toMap(ServiceProjectAttrDTO::getAttrName, Function.identity()));
            if (layerConfig.getType() == 9 && attrMap.containsKey("zhichiyufujinketui")) {
                ServiceProjectAttrDTO zhichiyufujinketui = attrMap.get("zhichiyufujinketui");
                if ("是".equals(zhichiyufujinketui.getAttrValue())) {
                    featuresLayer.getLayerConfigs().add(layerConfig);
                }
            } else if (layerConfig.getType() == 10 && attrMap.containsKey("spuCategory")) {
                ServiceProjectAttrDTO spuCategory = attrMap.get("spuCategory");
                ServiceProjectAttrDTO specialPrice = attrMap.get("specialPrice");
                if (spuCategory.getAttrValue().equals("是") && specialPrice != null) {
                    layerConfig.setDesc(layerConfig.getDesc().replace("${str}", specialPrice.getAttrValue()));
                    featuresLayer.getLayerConfigs().add(layerConfig);
                }
            } else if (layerConfig.getType() == 11 && attrMap.containsKey("is_special")) {
                ServiceProjectAttrDTO special = attrMap.get("is_special");
                if (!special.getAttrValue().equals("否")) {
                    layerConfig.setTitle(layerConfig.getTitle().replace("${str}", special.getAttrValue()));
                    layerConfig.setDesc(layerConfig.getDesc().replace("${str}", special.getAttrValue()));
                    featuresLayer.getLayerConfigs().add(layerConfig);
                }
            }
        }
    }

}
