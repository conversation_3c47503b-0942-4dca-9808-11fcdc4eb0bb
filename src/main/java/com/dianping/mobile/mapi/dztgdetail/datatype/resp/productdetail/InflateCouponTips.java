package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-28
 */
@Data
@TypeDoc(description = "神券提示条")
@MobileDo(id = 0xd464)
public class InflateCouponTips implements Serializable {

    @FieldDoc(description = "最佳优惠中的神券信息")
    @MobileField(key = 0x9dfb)
    private CouponItemInfoDTO magicCoupon;

    @FieldDoc(description = "背景图链接")
    @MobileField(key = 0xcb3f)
    private String backgroundImageUrl;

    @FieldDoc(description = "按钮跳链")
    @MobileField(key = 0x3c13)
    private String buttonClickUrl;

    @FieldDoc(description = "引导膨胀/购买券文案")
    @MobileField(key = 0x451b)
    private List<TextDisplayInfo> text;

    @FieldDoc(description = "神券感知增强引导类型")
    @MobileField(key = 0xe3d5)
    private String couponGuideType;

    @FieldDoc(description = "吸底条")
    @MobileField(key = 0x59cb)
    private SuckBottomBanner suckBottomBanner;

    @FieldDoc(description = "按钮图标")
    @MobileField(key = 0xfb92)
    private String buttonIcon;

    @FieldDoc(description = "按钮文案")
    @MobileField(key = 0xe221)
    private String buttonText;

    @FieldDoc(description = "神券按钮文案，支持多文案")
    @MobileField(key = 0x9290)
    private List<String> buttonTextList;
}