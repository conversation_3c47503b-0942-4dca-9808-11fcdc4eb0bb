package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.struct.common.dto.Resp;
import com.dianping.deal.struct.query.api.entity.dto.*;
import com.dianping.deal.tag.dto.TbTagDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.enums.ExaminerSupernatantEnum;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.HealthCertificateExaminerHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.utils.ExaminerUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.BaseDisplayItemVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailDisplayUnitVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailSpecificModuleVO;
import com.dianping.mobile.mapi.dztgdetail.entity.PhysicalCheckItemBaseInfo;
import com.dianping.mobile.mapi.dztgdetail.entity.PhysicalCheckTypeTag2CheckItemTags;
import com.dianping.mobile.mapi.dztgdetail.entity.PhysicalExamCheckItemDetailConfig;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.HealthCertificateExaminerHandler.*;

@Service
public class SpecificModuleHandler_401 implements DealDetailSpecificModuleHandler {

    private static final int SHOW_CHECK_ITEMS_MAX_NUM = 12;

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private DealGroupWrapper dealGroupWrapper;

    @Autowired
    private QueryCenterWrapper queryCenterWrapper;

    @Autowired
    private HealthCertificateExaminerHandler healthCertificateExaminerHandler;

    @Override
    public String identity() {
        return "401";
    }

    @Override
    public void handle(SpecificModuleCtx ctx) {
        ctx.setResult(buildResult(ctx));
    }

    public static final String CERTIFICATE_TYPE = "出证类型";
    public static final String CERTIFICATE_TIME= "出证时间";
    public static final String CERTIFICATE_MODE= "出证方式";
    public static List<String> HEALTH_CERTIFICATE_LIST = Lists.newArrayList(CERTIFICATE_TYPE, CERTIFICATE_TIME, CERTIFICATE_MODE);


    private DealDetailSpecificModuleVO buildResult(SpecificModuleCtx context) {
        String attrName = getAttrName(context.getExtraInfo());
        if (StringUtils.isNotBlank(attrName)) {

            //如果命中了体检的健康证检查，那么走新的逻辑，否则的话，走通用的体检
            if (HEALTH_CERTIFICATE_LIST.contains(attrName)) {
                return buildHealthCertificateModule(context, attrName);
            }

            //走体检通用
            ExaminerSupernatantEnum examinerSupernatantEnum = ExaminerUtils.disposeExtra(attrName);
            if (examinerSupernatantEnum != null && examinerSupernatantEnum != ExaminerSupernatantEnum.INSPECTION_ITEMS) {
                return buildSuitableCrowdResult(context, examinerSupernatantEnum);
            }
        }
        //默认走重点检查项查询，维持原有逻辑
        return buildResultDefault(context);
    }

    private DealDetailSpecificModuleVO buildHealthCertificateModule(SpecificModuleCtx context, String attrName) {
        //查一遍，Context里的值不全
        DealGroupDTO dealGroupDTO = queryDealInfo(context);
        if (dealGroupDTO == null) {
            return null;
        }

        List<BaseDisplayItemVO> displayItemList = Lists.newArrayList();
        DealDetailDisplayUnitVO unit = getDealDetailDisplayUnitVO(attrName, displayItemList);
        DealDetailSpecificModuleVO result = buildModuleVOAndFillItems(unit);

        switch (attrName) {
            //出证类型
            case CERTIFICATE_TYPE:{
                List<String> certificateDesc = healthCertificateExaminerHandler.buildCertificateDesc(dealGroupDTO.getAttrs());
                displayItemList.add(buildDisplayItem("出证类型",certificateDesc.stream().collect(Collectors.joining("、"))));
                break;
            }
            //出证时间
            case CERTIFICATE_TIME:{
                //出证类型
                Optional<List<String>> reportType = healthCertificateExaminerHandler.initReportTypes(dealGroupDTO.getAttrs(), ATTR_PHYSICAL_EXAMINATION_REPORT_TYPE);
                //出证时间
                Optional<String> resultTime = healthCertificateExaminerHandler.findAttrString(dealGroupDTO.getAttrs(), ATTR_PHYSICAL_EXAMINATION_GET_RESULT_TIME);
                Optional<String> physicalTime = healthCertificateExaminerHandler.findAttrString(dealGroupDTO.getAttrs(), ATTR_PHYSICAL_CARD_CHECKOUT_TIME);
                //出证类型是必选的
                if (reportType.isPresent()){
                    String type = String.join("、", reportType.get());
                    resultTime.ifPresent(e -> displayItemList.add(buildDisplayItem(type, elecCardTimeMap.getOrDefault(e, "一周及以上"))));
                }
                physicalTime.ifPresent(e -> {
                    if (physicalCardTimeMap.get(e) != null) {
                        displayItemList.add(buildDisplayItem("实体卡", physicalCardTimeMap.get(e)));
                    }
                });
                break;
            }
            case CERTIFICATE_MODE:{
                //自定义标题
                unit.setTitle("出证方式");
                Optional<List<String>> paperMethod = healthCertificateExaminerHandler.findAttrList(dealGroupDTO.getAttrs(), ATTR_PAPER_REPORT_MODE);
                Optional<String> elecMethod= healthCertificateExaminerHandler.findAttrString(dealGroupDTO.getAttrs(), ATTR_ELECTRONIC_CERTIFICATE_INQUIRY_METHOD);
                Optional<String> physicalMethod = healthCertificateExaminerHandler.findAttrString(dealGroupDTO.getAttrs(), ATTR_PHYSICAL_CARD_COLLECTION_METHOD);
                //在仅纸质报告下：电子证出证方式不展示
                if (!isOnlyPaperReport(dealGroupDTO)) {
                    elecMethod.ifPresent(e -> displayItemList.add(buildDisplayItem("电子证", e)));
                }
                String paperMethodDisplay = paperMethod.map(methods -> methods.toString().replaceAll("[\\[\\]]", ""))
                        .orElse("请咨询商家");
                //在有纸质报告的情况下，出证方式展示纸质报告
                //在有纸质报告的情况下，出证方式展示纸质报告
                List<String> reportTypes = healthCertificateExaminerHandler.initReportTypes(dealGroupDTO.getAttrs(), ATTR_PHYSICAL_EXAMINATION_REPORT_TYPE).orElse(null);
                if(CollectionUtils.isNotEmpty(reportTypes) && reportTypes.contains("纸质报告")){
                    displayItemList.add(buildDisplayItem("纸质报告", paperMethodDisplay));
                }
                physicalMethod.ifPresent(e -> displayItemList.add(buildDisplayItem("实体卡", e)));
                break;
            }
            default:{
                return null;
            }
        }
        return result;
    }

    private Boolean isOnlyPaperReport(DealGroupDTO dealGroupDTO){
        //一般不为null，必须项
        List<String> reportTypes = healthCertificateExaminerHandler.initReportTypes(dealGroupDTO.getAttrs(), ATTR_PHYSICAL_EXAMINATION_REPORT_TYPE).orElse(null);
        return CollectionUtils.isNotEmpty(reportTypes) && reportTypes.size() == 1 && reportTypes.contains("纸质报告");
    }
    private DealDetailSpecificModuleVO buildModuleVOAndFillItems(DealDetailDisplayUnitVO unit) {

        List<DealDetailDisplayUnitVO> units = new ArrayList<>();
        units.add(unit);

        DealDetailSpecificModuleVO result = new DealDetailSpecificModuleVO();
        result.setUnits(units);

        return result;
    }

    private static DealDetailDisplayUnitVO getDealDetailDisplayUnitVO(String attrName, List<BaseDisplayItemVO> displayItemList) {
        DealDetailDisplayUnitVO unit = new DealDetailDisplayUnitVO();
        unit.setTitle(attrName);
        unit.setDisplayItems(displayItemList);
        //这个是和前端约定的，用于区分浮层样式
        unit.setType("service");
        return unit;
    }

    BaseDisplayItemVO buildDisplayItem(String name, String detail) {
        BaseDisplayItemVO itemVO = new BaseDisplayItemVO();
        itemVO.setName(name);
        itemVO.setDetail(detail);
        return itemVO;
    }



    private DealDetailSpecificModuleVO buildSuitableCrowdResult(SpecificModuleCtx context, ExaminerSupernatantEnum supernatantEnum) {
        DealGroupDTO dealGroupDTO = queryDealInfo(context);
        if (dealGroupDTO == null) {
            return null;
        }

        List<BaseDisplayItemVO> displayItemList = null;
        if (supernatantEnum == ExaminerSupernatantEnum.SUITABLE_CROWD) {
            displayItemList = getDisplay4SuitableCrowd(dealGroupDTO);
        } else if (supernatantEnum == ExaminerSupernatantEnum.SERVICE_HIGHLIGHTS) {
            displayItemList = getDisplay4ServiceHighlights(dealGroupDTO);
        } else if (supernatantEnum == ExaminerSupernatantEnum.SPECIAL_CROWD) {
            displayItemList = getDisplay4SpecialCrowd(dealGroupDTO);
        }

        DealDetailDisplayUnitVO unit = getDealDetailDisplayUnitVO(supernatantEnum.getName(), displayItemList);

        List<DealDetailDisplayUnitVO> units = new ArrayList<>();
        units.add(unit);

        DealDetailSpecificModuleVO result = new DealDetailSpecificModuleVO();
        result.setUnits(units);

        return result;
    }

    private List<BaseDisplayItemVO> getDisplay4SpecialCrowd(DealGroupDTO dealGroupDTO) {
        if (CollectionUtils.isEmpty(dealGroupDTO.getAttrs())) {
            return Lists.newArrayList();
        }

        List<BaseDisplayItemVO> displayItemVOS = new ArrayList<>();
        List<String> suitableList = dealGroupDTO.getAttrs().stream().filter(attr ->
                Objects.equals("suitable_crowd", attr.getName())).findFirst().map(AttrDTO::getValue).map(ExaminerUtils::sortSpecialCrowdList).orElse(Lists.newArrayList());

        BaseDisplayItemVO baseDisplayItemVO = new BaseDisplayItemVO();
        baseDisplayItemVO.setName("特色人群");
        baseDisplayItemVO.setDetail(suitableList.stream().collect(Collectors.joining(",")));
        displayItemVOS.add(baseDisplayItemVO);
        return displayItemVOS;

    }

    private List<BaseDisplayItemVO> getDisplay4ServiceHighlights(DealGroupDTO dealGroupDTO) {
        if (CollectionUtils.isEmpty(dealGroupDTO.getAttrs())) {
            return Lists.newArrayList();
        }
        List<BaseDisplayItemVO> displayItemVOS = new ArrayList<>();
        List<String> additionalList = dealGroupDTO.getAttrs().stream().filter(attr ->
                Objects.equals("px_additional_service", attr.getName())).findFirst().map(AttrDTO::getValue).map(ExaminerUtils::sortHighlights).orElse(Lists.newArrayList());
        BaseDisplayItemVO baseDisplayItemVO = new BaseDisplayItemVO();
        baseDisplayItemVO.setName("服务亮点");
        baseDisplayItemVO.setDetail(additionalList.stream().collect(Collectors.joining(",")));
        displayItemVOS.add(baseDisplayItemVO);
        return displayItemVOS;
    }

    private List<BaseDisplayItemVO> getDisplay4SuitableCrowd(DealGroupDTO dealGroupDTO) {

        if (CollectionUtils.isEmpty(dealGroupDTO.getAttrs())) {
            return Lists.newArrayList();
        }
        List<BaseDisplayItemVO> displayItemVOS = new ArrayList<>();
        List<String> sexList = dealGroupDTO.getAttrs().stream().filter(attr ->
                Objects.equals("checkup_sex", attr.getName())).findFirst().map(AttrDTO::getValue).orElse(null);
        List<String> ageList = dealGroupDTO.getAttrs().stream().filter(attr ->
                Objects.equals("px_suitable_age", attr.getName())).findFirst().map(AttrDTO::getValue).map(ExaminerUtils::transAge).orElse(null);
        List<String> suitableList = dealGroupDTO.getAttrs().stream().filter(attr ->
                Objects.equals("suitable_crowd", attr.getName())).findFirst().map(AttrDTO::getValue).map(ExaminerUtils::sortSpecialCrowdList).orElse(null);

        if (CollectionUtils.isNotEmpty(sexList)) {
            String sex = sexList.get(0);
            BaseDisplayItemVO baseDisplayItemVO = new BaseDisplayItemVO();
            baseDisplayItemVO.setName("性别");
            baseDisplayItemVO.setDetail(Objects.equals("通用", sex) ? "男女通用" : sex);
            displayItemVOS.add(baseDisplayItemVO);

        }
        if (CollectionUtils.isNotEmpty(ageList)) {
            String age = "";
            if (ageList.contains("老年") && ageList.contains("中年") && ageList.contains("青年")) {
                age = "全年龄（18岁以上）";
            } else if (ageList.contains("老年") && ageList.contains("中年")) {
                age = "中老年";
            } else if (ageList.contains("中年") && ageList.contains("青年")) {
                age = "青中年";
            } else {
                age = ageList.get(0);
            }
            BaseDisplayItemVO baseDisplayItemVO = new BaseDisplayItemVO();
            baseDisplayItemVO.setName("年龄");
            baseDisplayItemVO.setDetail(age);
            displayItemVOS.add(baseDisplayItemVO);
        }
        if (CollectionUtils.isNotEmpty(suitableList)) {
            BaseDisplayItemVO baseDisplayItemVO = new BaseDisplayItemVO();
            baseDisplayItemVO.setName("特色人群");
            baseDisplayItemVO.setDetail(suitableList.stream().collect(Collectors.joining(",")));
            displayItemVOS.add(baseDisplayItemVO);
        }
        return displayItemVOS;
    }

    protected DealGroupDTO queryDealInfo(SpecificModuleCtx context) {
        QueryByDealGroupIdRequest request = ExaminerUtils.getExaminerRequest(context.getDpDealGroupId(), IdTypeEnum.DP);
        Future singleDealGroupDtoFuture = queryCenterWrapper.preDealGroupDTO(request);
        try {
            return queryCenterWrapper.getDealGroupDTO(singleDealGroupDtoFuture);
        } catch (Exception e) {
            logger.error("SpecificModuleHandler_401 queryDealInfo error SpecificModuleCtx:{}", JSON.toJSONString(context), e);
        }
        return null;
    }

    private DealDetailSpecificModuleVO buildResultDefault(SpecificModuleCtx context) {
        List<BaseDisplayItemVO> displayItemList = getDisplayItemList(context.getDpDealGroupId());

        DealDetailDisplayUnitVO unit = new DealDetailDisplayUnitVO();
        unit.setTitle("重点检查项");
        unit.setDisplayItems(displayItemList);

        List<DealDetailDisplayUnitVO> units = new ArrayList<>();
        units.add(unit);

        DealDetailSpecificModuleVO result = new DealDetailSpecificModuleVO();
        result.setUnits(units);

        return result;
    }

    private String getAttrName(String extraInfo) {
        if (StringUtils.isEmpty(extraInfo)) {
            return "";
        }
        try {
            JSONObject infoJson = new JSONObject(extraInfo);
            return infoJson.getString("examinationAttrName");
        } catch (Exception e) {
        }
        return "";
    }

    private List<BaseDisplayItemVO> getDisplayItemList(Integer dpDealGroupId) {
        Future tagFuture = dealGroupWrapper.preTags(dpDealGroupId);
        Future detailFuture = dealGroupWrapper.preDealDetailInfo(dpDealGroupId);

        Map<Long, List<TbTagDTO>> tagMap = dealGroupWrapper.getFutureResult(tagFuture);
        Resp<DealDetailDto> detailDtoResp = dealGroupWrapper.getFutureResult(detailFuture);

        List<Long> currentDealGroupTagList = getCurrentDealGroupTagList(tagMap, dpDealGroupId);
        Map<Long, List<SkuAttrItemDto>> productCategoryId2SkuAttrMap = buildProductCategoryId2SkuAttrMap(detailDtoResp);

        List<Long> checkItemTagIds = getCheckItemTagIds(currentDealGroupTagList);

        Map<Long, PhysicalExamCheckItemDetailConfig> checkItemTagId2Content = getTagId2DetailConfig();
        Map<String, PhysicalCheckItemBaseInfo> checkItemTagId2BaseInfo = getCheckItemTagId2BaseInfo();

        List<BaseDisplayItemVO> checkItemDisplayVOs = new ArrayList<>();
        for (Long checkItemTagId : checkItemTagIds) {
            BaseDisplayItemVO checkItemDisplayVO = buildCheckItemDisplayVO(checkItemTagId, checkItemTagId2BaseInfo, productCategoryId2SkuAttrMap, checkItemTagId2Content);
            checkItemDisplayVOs.add(checkItemDisplayVO);
        }
        return checkItemDisplayVOs;
    }

    private List<Long> getCheckItemTagIds(List<Long> currentDealGroupTagList) {
        List<PhysicalCheckTypeTag2CheckItemTags> configs = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb", "com.sankuai.dzu.tpbase.checktypetagid.2.checkitemtagids", PhysicalCheckTypeTag2CheckItemTags.class, Collections.emptyList());
        for (PhysicalCheckTypeTag2CheckItemTags config : configs) {
            if (config != null && (config.getCheckTypeTagId() == -1 || currentDealGroupTagList.contains(config.getCheckTypeTagId()))) {
                return pickTopCheckItemTagIds(config.getCheckItemTagIds(), currentDealGroupTagList, SHOW_CHECK_ITEMS_MAX_NUM);
            }
        }
        return Collections.emptyList();
    }

    private List<Long> pickTopCheckItemTagIds(List<Long> checkItemTagIds, List<Long> currentDealGroupTagList, int maxNum) {
        if (CollectionUtils.isEmpty(checkItemTagIds) || CollectionUtils.isEmpty(currentDealGroupTagList)) {
            return Collections.emptyList();
        }
        List<Long> topCheckItemTagIds = new ArrayList<>();
        for (Long tag : checkItemTagIds) {
            if (currentDealGroupTagList.contains(tag) && topCheckItemTagIds.size() < maxNum) {
                topCheckItemTagIds.add(tag);
            }
        }
        return topCheckItemTagIds;
    }

    private Map<String, PhysicalCheckItemBaseInfo> getCheckItemTagId2BaseInfo() {
        return Lion.getMap("com.sankuai.dzu.tpbase.dztgdetailweb", "com.sankuai.dzu.tpbase.checkitemtagid.2.checkitembaseinfo", PhysicalCheckItemBaseInfo.class, Collections.emptyMap());
    }

    private Map<Long, PhysicalExamCheckItemDetailConfig> getTagId2DetailConfig() {
        List<PhysicalExamCheckItemDetailConfig> configList = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.tagid.2.checkitemdetail.map", PhysicalExamCheckItemDetailConfig.class, new ArrayList<>());
        Map<Long, PhysicalExamCheckItemDetailConfig> configMap = new HashMap<>();
        for (PhysicalExamCheckItemDetailConfig config : configList) {
            configMap.put(config.getTagId(), config);
        }
        return configMap;
    }

    private BaseDisplayItemVO buildCheckItemDisplayVO(long tagId, Map<String, PhysicalCheckItemBaseInfo> checkItemTagId2BaseInfo, Map<Long, List<SkuAttrItemDto>> productCategoryId2SkuAttrMap, Map<Long, PhysicalExamCheckItemDetailConfig> checkItemTagId2Content) {
        BaseDisplayItemVO baseDisplayItemVO = new BaseDisplayItemVO();
        PhysicalCheckItemBaseInfo physicalCheckItemBaseInfo = checkItemTagId2BaseInfo.get(String.valueOf(tagId));
        if (physicalCheckItemBaseInfo != null) {
            baseDisplayItemVO.setName(physicalCheckItemBaseInfo.getName());
            baseDisplayItemVO.setIcon(physicalCheckItemBaseInfo.getIcon());
            baseDisplayItemVO.setDetail(physicalCheckItemBaseInfo.getDetail());

            List<String> values = getCheckItemValuesByTagId(tagId, productCategoryId2SkuAttrMap, checkItemTagId2Content);
            baseDisplayItemVO.setValues(values);
            baseDisplayItemVO.setDesc(getCheckItemDescByTagId(values));
        }
        return baseDisplayItemVO;
    }

    private String getCheckItemDescByTagId(List<String> values) {
        if (CollectionUtils.isEmpty(values)) {
            return "";
        }
        return values.size() + "项";
    }

    private List<String> getCheckItemValuesByTagId(long tagId, Map<Long, List<SkuAttrItemDto>> productCategoryId2SkuAttrMap,
                                                   Map<Long, PhysicalExamCheckItemDetailConfig> tagId2DetailConfig) {
        List<String> checkItemValueList = new ArrayList<>();
        PhysicalExamCheckItemDetailConfig detailConfig = tagId2DetailConfig.get(tagId);
        if (detailConfig == null) {
            return new ArrayList<>();
        }
        Map<String, List<String>> productCategoryId2DetailListMap = detailConfig.getProductCategoryId2DetailListMap();
        if (productCategoryId2DetailListMap == null) {
            return new ArrayList<>();
        }

        for (Map.Entry<Long, List<SkuAttrItemDto>> entry : productCategoryId2SkuAttrMap.entrySet()) {
            Long productCategory = entry.getKey();
            List<String> allConfigList = productCategoryId2DetailListMap.get(String.valueOf(productCategory));
            if (allConfigList == null) {
                continue;
            }
            List<SkuAttrItemDto> attrItemDtos = productCategoryId2SkuAttrMap.get(productCategory);
            LinkedHashSet<String> currentValueList = getCurrentValueList(attrItemDtos);

            for (String configValue : allConfigList) {
                if (currentValueList.contains(configValue)) {
                    checkItemValueList.add(configValue);
                }
            }
        }
        return checkItemValueList;
    }

    private LinkedHashSet<String> getCurrentValueList(List<SkuAttrItemDto> skuAttrItemDtos) {
        if (skuAttrItemDtos == null) {
            return new LinkedHashSet<>();
        }
        LinkedHashSet<String> currentValueList = new LinkedHashSet<>();
        for (SkuAttrItemDto skuAttrItemDto : skuAttrItemDtos) {
            String[] attrValueStrList = org.apache.commons.lang3.StringUtils.split(skuAttrItemDto.getAttrValue(), "、");
            currentValueList.addAll(Arrays.asList(attrValueStrList));
        }
        return currentValueList;
    }

    private List<Long> getCurrentDealGroupTagList(Map<Long, List<TbTagDTO>> tagMap, int dpDealGroupId) {
        if (MapUtils.isEmpty(tagMap)) {
            return new ArrayList<>();
        }
        List<TbTagDTO> tbTagDTOList = tagMap.get((long) dpDealGroupId);
        List<Long> result = new ArrayList<>();
        for (TbTagDTO tbTagDTO : tbTagDTOList) {
            if (tbTagDTO == null) {
                continue;
            }
            result.add(tbTagDTO.getTagId());
        }
        return result;
    }

    private Map<Long, List<SkuAttrItemDto>> buildProductCategoryId2SkuAttrMap(Resp<DealDetailDto> resp) {
        Map<Long, List<SkuAttrItemDto>> productCategoryId2SkuAttrMap = new HashMap<>();

        if (resp == null || !resp.isSuccess() || resp.getContent() == null) {
            return productCategoryId2SkuAttrMap;
        }

        DealDetailDto content = resp.getContent();

        if (content.getSkuUniStructuredDto() == null) {
            return productCategoryId2SkuAttrMap;
        }

        if (CollectionUtils.isNotEmpty(content.getSkuUniStructuredDto().getMustGroups())) {
            for (MustSkuItemsGroupDto mustGroup : content.getSkuUniStructuredDto().getMustGroups()) {
                List<SkuItemDto> skuItemDtos = mustGroup.getSkuItems();
                if (CollectionUtils.isEmpty(skuItemDtos)) {
                    continue;
                }
                for (SkuItemDto skuItemDto : skuItemDtos) {
                    long productCategoryId = skuItemDto.getProductCategory();
                    if (productCategoryId2SkuAttrMap.containsKey(productCategoryId)) {
                        List<SkuAttrItemDto> attrItemDtos = productCategoryId2SkuAttrMap.get(productCategoryId);
                        if (attrItemDtos == null) {
                            attrItemDtos = new ArrayList<>();
                        }
                        attrItemDtos.addAll(skuItemDto.getAttrItems());
                    } else {
                        List<SkuAttrItemDto> attrItemDtos = new ArrayList<>();
                        attrItemDtos.addAll(skuItemDto.getAttrItems());
                        productCategoryId2SkuAttrMap.put(productCategoryId, attrItemDtos);
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(content.getSkuUniStructuredDto().getOptionalGroups())) {
            for (OptionalSkuItemsGroupDto optionalGroup : content.getSkuUniStructuredDto().getOptionalGroups()) {
                List<SkuItemDto> skuItemDtos = optionalGroup.getSkuItems();
                if (CollectionUtils.isEmpty(skuItemDtos)) {
                    continue;
                }
                for (SkuItemDto skuItemDto : skuItemDtos) {
                    long productCategoryId = skuItemDto.getProductCategory();
                    if (productCategoryId2SkuAttrMap.containsKey(productCategoryId)) {
                        List<SkuAttrItemDto> attrItemDtos = productCategoryId2SkuAttrMap.get(productCategoryId);
                        if (attrItemDtos == null) {
                            attrItemDtos = new ArrayList<>();
                        }
                        attrItemDtos.addAll(skuItemDto.getAttrItems());
                    } else {
                        List<SkuAttrItemDto> attrItemDtos = new ArrayList<>();
                        attrItemDtos.addAll(skuItemDto.getAttrItems());
                        productCategoryId2SkuAttrMap.put(productCategoryId, attrItemDtos);
                    }
                }
            }
        }

        return productCategoryId2SkuAttrMap;
    }
}