package com.dianping.mobile.mapi.dztgdetail.biz.processor.shop;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.feature.model.FeatureDetailDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ShopTagWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ShopCategoryEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @date 2024-05-06
 * @desc 门店标签处理逻辑
 */
public class ShopTagsProcessor extends AbsDealProcessor {
    @Resource
    private ShopTagWrapper shopTagWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        long dpShopIdL = ctx.getDpLongShopId();
        if ( Objects.isNull(dealGroupDTO) || dpShopIdL <= 0) {
            return false;
        }
        return LionConfigUtils.getShopIDToTagIDConfig(dealGroupDTO.getCategory()) || enablePerformanceGuarantee(ctx)
                || DealCtxHelper.isPreOrderDeal(ctx);
    }

    private boolean enablePerformanceGuarantee(DealCtx ctx) {
        EnvCtx envCtx = ctx.getEnvCtx();
        boolean isNative = Objects.nonNull(envCtx) && envCtx.isNative();
        // 当前仅开放native端
        return isNative && LionConfigUtils.getPerformanceGuaranteeSwitch(ctx.getDealGroupDTO().getCategory());
    }

    @Override
    public void prepare(DealCtx ctx) {
        Future future = shopTagWrapper.preGetDpShopTags(ctx.getDpLongShopId());
        ctx.getFutureCtx().setShopTagFuture(future);
        if (DealCtxHelper.isPreOrderDeal(ctx)) {
            Future shopOrderTimeFuture = shopTagWrapper.preGetShopAvgOrderTime(ctx);
            ctx.getFutureCtx().setShopAvgOrderTimeFuture(shopOrderTimeFuture);
        }
    }

    @Override
    public void process(DealCtx ctx) {
        if (Objects.isNull(ctx.getFutureCtx())) {
            return;
        }
        // 处理通用门店标签
        processUniverseShopTag(ctx);
        // 预订团单保障标签
        processPreOrderShopTag(ctx);
    }

    private void processUniverseShopTag(DealCtx ctx) {
        if (Objects.isNull(ctx.getFutureCtx().getShopTagFuture())) {
            return;
        }
        Map<Long, List<DisplayTagDto>> dpShopId2TagsMap =
                shopTagWrapper.getShopId2TagsMap(ctx.getFutureCtx().getShopTagFuture());
        ctx.setDpShopId2TagsMap(dpShopId2TagsMap);
        // 穿戴甲门店
        if (LionConfigUtils.getShopIDToTagIDConfig(ctx.getDealGroupDTO().getCategory())) {
            ShopCategoryEnum shopCategoryEnum = shopTagWrapper.getWearableNailShopTag(ctx.getDpLongShopId(), dpShopId2TagsMap);
            ctx.setShopCategoryEnum(shopCategoryEnum);
        }
        // 履约保障标签
        if (LionConfigUtils.getPerformanceGuaranteeSwitch(ctx.getDealGroupDTO().getCategory())) {
            List<FeatureDetailDTO> tagFeaturesList = shopTagWrapper.getPerformanceGuaranteeTags(ctx.getDealGroupDTO().getCategory(), ctx.getDpLongShopId(), dpShopId2TagsMap);
            ctx.setShopTagFeatures(tagFeaturesList);
        }
    }

    private void processPreOrderShopTag(DealCtx ctx) {
        if (Objects.isNull(ctx.getFutureCtx().getShopAvgOrderTimeFuture())) {
            return;
        }
        ctx.setPreOrderFeatDetails(shopTagWrapper.getPreOrderFeatDetail(ctx.getFutureCtx().getShopAvgOrderTimeFuture(), ctx.getDpLongShopId()));
    }
}
