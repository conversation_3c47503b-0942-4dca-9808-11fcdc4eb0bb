package com.dianping.mobile.mapi.dztgdetail.common.constants;

import com.google.common.collect.ImmutableMap;

import java.util.Map;

public class MedicalConstant {
    //展示场景：2:从团详页跳转
    public static final String DISPLAY_SCENE_TEETH = "2";

    //安心医·补牙保障Ip
    public static final String GUARANTEEIP_DENTURE = "3";

    //安心医·种植保障Ip
    public static final String GUARANTEEIP_IMPANT = "2";

    public static final String GLASS_ASSURED_DP_JUMPURL = "dianping://gcmrn?mrn_biz=gcbu&mrn_entry=mrn-beauty-guarantee-detail-page&mrn_component=guarantee-detail-page&moduleKey=shop_glasses_assured";

    public static final String GLASS_ASSURED_MT_JUMPURL = "imeituan://www.meituan.com/gc/mrn?mrn_biz=gcbu&mrn_entry=mrn-beauty-guarantee-detail-page&mrn_component=guarantee-detail-page&moduleKey=shop_glasses_assured";

    //安心配镜tagId
    public static final Long SAFE_PTOMETRY_TAGID = 21151L;
    public static final Long SAFE_PTOMETRY_TAGID_TEST = 7405L;

    public static final Long GUNUINE_GUARANTEE_TAGID = 15676L;

    public static final Long GUNUINE_GUARANTEE_TAGID_TEST = 7406L;

    public static final String TEETH_GUARANTEE_DP_JUMPURL = "dianping://mrn?mrn_biz=gcbu&mrn_entry=mrn-common-guarantee-page&mrn_component=common-guarantee-page";

    public static final String TEETH_GUARANTEEP_MT_JUMPURL = "imeituan://www.meituan.com/mrn?mrn_biz=gcbu&mrn_entry=mrn-common-guarantee-page&mrn_component=common-guarantee-page";

    public static final String SAFE_DENTURE_ZDC_TAG = "safeDenture";
    public static final String SAFE_IMPLANT_ZDC_TAG = "safeImplant";
    public static final Map<String, String> SAFE_DENTURE_RULE_CONFIG = ImmutableMap.of(
            "materialShedFreeRefillDurationType","materialShedFreeRefillDuration",
            "postoperativeFreeReviewServiceDurationType","postoperativeFreeReviewServiceDuration",
            "postoperativeFreeReviewServiceCountType","postoperativeFreeReviewServiceCount",
            "shedFreeReplantGuaranteeDurationType","shedFreeReplantGuaranteeDuration",
            "implantQualityGuaranteeDurationType","implantQualityGuaranteeDuration"
    );
}
