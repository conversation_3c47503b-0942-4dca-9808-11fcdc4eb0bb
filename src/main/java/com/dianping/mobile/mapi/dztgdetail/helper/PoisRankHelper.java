package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.entity.DealBranchesParam;
import com.dianping.mobile.mapi.dztgdetail.entity.SortCxt;
import com.dianping.mobile.mapi.dztgdetail.util.DoubleUtils;
import com.dianping.mobile.mapi.dztgdetail.util.ListUtils;
import com.meituan.service.mobile.sinai.client.model.PoiModel;
import com.meituan.service.mobile.sinai.client.model.PoiModelL;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * poi rank 操作类.
 * 主要涉及排序、分页等
 *
 * <AUTHOR>
 * @version 1.0
 * @created 2014年6月26日
 */
public class PoisRankHelper {
    // 评分
    public static final String SORT_TYPE_RATING = "rating";
    // 距离
    public static final String SORT_TYPE_DISTANCE = "distance";

    public static List<PoiModelL> pick(List<PoiModelL> poiModelList, boolean show, DealBranchesParam param) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.helper.PoisRankHelper.pick(java.util.List,boolean,com.dianping.mobile.mapi.dztgdetail.entity.DealBranchesParam)");
        List<PoiModelL> picked = new ArrayList<>();
        if (show && CollectionUtils.isNotEmpty(poiModelList)) {
            String sort = param.getSort();
            double lat = param.getLat();
            double lng = param.getLng();
            int cityId = param.getCityId();
            int offset = param.getOffset();
            int limit = param.getLimit();
            boolean onlyCurCityPOIs = param.isOnlyCurCityPOIs();
            // sort
            if (SORT_TYPE_DISTANCE.equals(sort) && DoubleUtils.isNotZero(lat) && DoubleUtils.isNotZero(lng)) {
                DistancePoiComparator<PoiModelL> comparator = new DistancePoiComparator<>(lat, lng);
                poiModelList.sort(comparator);
            } else if (SORT_TYPE_RATING.equals(sort)) {
                RatingPoiComparator<PoiModelL> comparator = new RatingPoiComparator<>();
                poiModelList.sort(comparator);
            }
            // showType filter
            if (StringUtils.isNotEmpty(param.getFilter())) {
                poiModelList.removeIf(model -> model.getShowType().equals(param.getFilter()));
            }
            if (cityId == 0) {
                onlyCurCityPOIs = false;
            }
            if (onlyCurCityPOIs) {
                int end = Math.min((offset + limit), poiModelList.size());
                int begin = offset + 1;
                int count = 0;
                if (begin > end) {
                    throw new ArrayIndexOutOfBoundsException("Parameter 'offset' is too large");
                }
                for (PoiModelL poi : poiModelList) {
                    if (null != poi.getCityIds()) {
                        if (poi.getCityIds().contains(cityId)) {
                            count++;
                            if (count >= begin && count <= end) {
                                picked.add(poi);
                            }
                        }
                    }
                }
            } else {
                // 按offset-limit取
                List<PoiModelL> lastList = ListUtils.subList(poiModelList, offset, limit);

                if (CollectionUtils.isNotEmpty(lastList)) {
                    picked = lastList;
                }
            }
        }
        return picked;
    }

    public static List<PoiModelL> pick(List<PoiModelL> poiModelList, SortCxt sortCxt) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.helper.PoisRankHelper.pick(java.util.List,com.dianping.mobile.mapi.dztgdetail.entity.SortCxt)");
        if (CollectionUtils.isEmpty(poiModelList) || sortCxt == null) {
            return null;
        }
        sortPoiModels(poiModelList, sortCxt);
        // 按offset-limit取
        return ListUtils.subList(poiModelList, sortCxt.getOffset(), sortCxt.getLimit());
    }

    public static void sortPoiModels(List<PoiModelL> poiModels, SortCxt sortCxt) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.helper.PoisRankHelper.sortPoiModels(java.util.List,com.dianping.mobile.mapi.dztgdetail.entity.SortCxt)");
        if (SORT_TYPE_DISTANCE.equals(sortCxt.getSortStrategy()) && sortCxt.hasLatLng()) {
            DistancePoiComparator<PoiModelL> comparator = new DistancePoiComparator<>(sortCxt.getLat(), sortCxt.getLng());
            poiModels.sort(comparator);
            return;
        }
        //兜底排序策略
        poiModels.sort(new CityRatePoiComparator(sortCxt.getMtCityId()));
    }

    static class CityRatePoiComparator implements Comparator<PoiModelL> {
        private int targetMtCityId;

        public CityRatePoiComparator(int mtCityId) {
            this.targetMtCityId = mtCityId;
        }

        @Override
        public int compare(PoiModelL first, PoiModelL second) {
            int firstCityScore = first.getCityId() == targetMtCityId ? 1 : 0; //同城得1分，异地得0分
            int secondCityScore = second.getCityId() == targetMtCityId ? 1 : 0;
            int diffCityScore = firstCityScore - secondCityScore;
            //同城情况下，比较评价
            if (firstCityScore == secondCityScore) {
                double diffDistanceScore = first.getAvgscore() - second.getAvgscore();
                if (BigDecimal.valueOf(diffDistanceScore).compareTo(BigDecimal.ZERO) == 0) {
                    return 0;
                }
                return diffDistanceScore > 0 ? -1 : 1;
            }
            if (diffCityScore == 0) {
                return 0;
            }
            return diffCityScore > 0 ? -1 : 1;
        }
    }

    /**
     * 评分比较器.
     *
     * @param <T>
     */
    static class RatingPoiComparator<T> implements Comparator<T> {

        @Override
        public int compare(T first, T second) {
            if (first instanceof PoiModelL) {
                double diff = ((PoiModelL) first).getAvgscore() - ((PoiModelL) second).getAvgscore();
                if (diff > 0) {
                    return -1;
                }
                if (diff < 0) {
                    return 1;
                }
                return 0;
            }
            throw new UnsupportedOperationException();
        }
    }

    /**
     * 距离比较器.
     *
     * @param <T>
     */
    static class DistancePoiComparator<T> implements Comparator<T> {

        private double userLat;
        private double userLng;

        public DistancePoiComparator(double userLat, double userLng) {
            this.userLat = userLat;
            this.userLng = userLng;
        }

        @Override
        public int compare(T first, T second) {
            if (first instanceof PoiModelL) {
                double distanceFirst = distance(userLat, userLng, ((PoiModelL) first).getLatitude(), ((PoiModelL) first).getLongitude());
                double distanceSecond = distance(userLat, userLng, ((PoiModelL) second).getLatitude(), ((PoiModelL) second).getLongitude());
                double diff = distanceFirst - distanceSecond;
                if (diff < 0) {
                    return -1;
                }
                if (diff > 0) {
                    return 1;
                }
                return 0;
            }
            throw new UnsupportedOperationException();
        }
    }

    private static double distance(double lat1, double lng1, double lat2, double lng2) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.helper.PoisRankHelper.distance(double,double,double,double)");
        // 1) 计算三个参数
        // 经度差值
        double dx = lng1 - lng2;
        // 纬度差值
        double dy = lat1 - lat2;
        // 平均纬度
        double b = (lat1 + lat2) / 2.0;
        // 2) 计算东西方向距离和南北方向距离(单位：米)，东西距离采用三阶多项式，南北采用一阶多项式即可
        // 东西距离
        double lx = (0.05 * b * b * b - 19.16 * b * b + 47.13 * b + 110966) * dx;
        // 南北距离
        double ly = (17 * b + 110352) * dy;
        // 3) 用平面的矩形对角距离公式计算总距离
        return Math.sqrt(lx * lx + ly * ly);
    }
}