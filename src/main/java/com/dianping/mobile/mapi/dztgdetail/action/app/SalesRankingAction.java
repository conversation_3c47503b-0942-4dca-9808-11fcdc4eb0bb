package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.cat.Cat;
import com.dianping.gmkt.activity.api.dto.board.BoardRecordDTO;
import com.dianping.gmkt.activity.api.enums.BoardMaterialTypeEnum;
import com.dianping.gmkt.activity.api.enums.BoardRequestSourceEnum;
import com.dianping.gmkt.activity.api.request.BoardRecordRequest;
import com.dianping.gmkt.activity.api.response.BoardRecordResponse;
import com.dianping.gmkt.activity.api.service.BoardService;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.action.DefaultAction;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.SaleRankingRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.SalesRankingVO;
import com.dianping.mobile.mapi.dztgdetail.helper.AppCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.dianping.pigeon.remoting.invoker.config.annotation.Reference;
import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Controller;

import java.util.List;

/**
 * <AUTHOR>
 * 2020/6/17 3:56 下午
 */

@InterfaceDoc(displayName = "到综团单销量榜单APP查询接口",
        type = "restful",
        description = "查询到综团单所在的销量榜单",
        scenarios = "该接口仅适用于双平台APP站点的团购详情页，其他以到综团购为纬度的页面如需使用请咨询项目owner",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/salesranking.bin",
        authors = "jiangyouchong"
)
@Slf4j
@Controller("salesranking.bin")
@Action(url = "salesranking.bin")
public class SalesRankingAction extends DefaultAction<SaleRankingRequest> {

    @Reference(url = "http://service.dianping.com/gmkt_activity_service/BoardService_0.0.1", timeout = 500)
    private BoardService boardService;

    @Override
    protected IMobileResponse validate(SaleRankingRequest request, IMobileContext context) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.action.app.SalesRankingAction.validate(com.dianping.mobile.mapi.dztgdetail.datatype.req.SaleRankingRequest,com.dianping.mobile.framework.datatypes.IMobileContext)");
        IdUpgradeUtils.processProductIdForSaleRankingRequest(request, "salesranking.bin");
        if (request == null || request.getDealgroupid() <= 0) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @Override
    protected IMobileResponse execute(SaleRankingRequest request, IMobileContext context) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.action.app.SalesRankingAction.execute(com.dianping.mobile.mapi.dztgdetail.datatype.req.SaleRankingRequest,com.dianping.mobile.framework.datatypes.IMobileContext)");

        try {
            BoardRecordRequest boardRequest = buildRequest(request, context);

            BoardRecordResponse boardResponse = boardService.queryBoardRecord(boardRequest);

            SalesRankingVO result = buildResult(request, boardResponse);

            if (result == null) {
                return Resps.NoDataResp;
            }

            return new CommonMobileResponse(result);
        } catch (Exception e) {
            log.error("query deal sales ranking failed, req [{}]", request, e);
            return Resps.SYSTEM_ERROR;
        }
    }

    private SalesRankingVO buildResult(SaleRankingRequest request, BoardRecordResponse boardResponse) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.action.app.SalesRankingAction.buildResult(SaleRankingRequest,BoardRecordResponse)");
        if (boardResponse == null || MapUtils.isEmpty(boardResponse.getBoardRecordMap())
                || !boardResponse.getBoardRecordMap().containsKey(request.getDealgroupid())) {
            log.debug("deal [{}] has no sales ranking, ranking response : [{}]",
                    request.getDealgroupid(), boardResponse);
            return null;
        }

        SalesRankingVO result = new SalesRankingVO();
        BoardRecordDTO boardRecord = boardResponse.getBoardRecordMap().get(request.getDealgroupid());
        result.setRankingText(boardRecord.getDisplayText());
        result.setRankingUrl(boardRecord.getJumpUrl());
        return result;
    }

    private BoardRecordRequest buildRequest(SaleRankingRequest request, IMobileContext context) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.action.app.SalesRankingAction.buildRequest(SaleRankingRequest,IMobileContext)");
        BoardRecordRequest boardRequest = new BoardRecordRequest();
        boardRequest.setMaterialIds(Lists.newArrayList(request.getDealgroupid()));
        boardRequest.setType(BoardMaterialTypeEnum.DEAL.getType());
        boardRequest.setShopId(request.getShopid());
        boardRequest.setShopIdL(request.getShopIdLong());
        boardRequest.setRequestSource(BoardRequestSourceEnum.DEAL_DETAIL.getCode());
        boardRequest.setPlatform(AppCtxHelper.isMeituanClient(context) ? 2 : 1);
        return boardRequest;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.action.app.SalesRankingAction.getRule()");
        return null;
    }
}
