package com.dianping.mobile.mapi.dztgdetail.tab;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

import com.dianping.cat.Cat;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.struct.query.api.entity.dto.DealDetailDto;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.BaseData;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.BaseLoadParam;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.DealTab;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.DealTabHolder;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.SourceDataHolder;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Service
public class AllRelateDeals extends AbstractRelateDeals {

    @Override
    public List<Integer> identifyByPublishCategory() {
        return Lists.newArrayList();
    }

    @Override
    protected SourceDataHolder getSourceDataHolder() {
        return new AllDealSourceDataHolder();
    }

    @Override
    public void loadBeforeRelate(BaseLoadParam param, SourceDataHolder holder) {
        super.loadBeforeRelate(param, holder);
    }

    @Override
    protected List<Integer> getRelatedDpDealGroupIds(BaseData baseData, SourceDataHolder holder) {
        return baseData.getDpDealGroupIds();
    }

    @Override
    public void loadAfterRelate(BaseLoadParam param, SourceDataHolder holder) {
        AllDealSourceDataHolder allHolder = (AllDealSourceDataHolder) holder;

        super.loadAfterRelate(param, holder);
        Future dealAttrFuture = dealGroupWrapper.preGetDealGroupAttrs(param.getBaseData().getRelatedDpDealGroupIds(), dealAttrsToLoadAfterRelate());
        List<Future> dealStructFuture = dealGroupWrapper.preBatchQueryDealDetailInfo(param.getBaseData().getRelatedDpDealGroupIds());

        allHolder.setDealBaseMap(dealGroupWrapper.getBatchQueryDealGroupBaseResult(allHolder.getDealBaseFutures()));
        allHolder.setDealSaleMap(dealStockSaleWrapper.getShopSceneSalesDisplay(allHolder.getDealSaleFutures()));
        allHolder.setDealAttrMap(dealGroupWrapper.getDealGroupAttrs(dealAttrFuture));
        allHolder.setDealStructMap(dealGroupWrapper.getBatchQueryDealDetailInfoResult(dealStructFuture));
    }

    @Override
    protected DealTabHolder doListRelatedDealTabs(BaseData baseData, SourceDataHolder holder) {
        AllDealSourceDataHolder attrHolder = (AllDealSourceDataHolder) holder;
        Map<Integer, DealGroupBaseDTO> dealBaseMap = attrHolder.getDealBaseMap();
        Map<Integer, List<AttributeDTO>> dealAttrMap = attrHolder.getDealAttrMap();
        Map<Integer, DealDetailDto> dealDetailMap = attrHolder.getDealStructMap();

        if (MapUtils.isEmpty(dealBaseMap) || MapUtils.isEmpty(dealAttrMap) || MapUtils.isEmpty(dealDetailMap)) {
            return null;
        }

        int currentDpDealGroupId = baseData.getCurrentDpGroupId();
        if (!dealBaseMap.containsKey(currentDpDealGroupId)
                || !dealAttrMap.containsKey(currentDpDealGroupId)
                || !dealDetailMap.containsKey(currentDpDealGroupId)) {
            return null;
        }

        List<DealTab> relatedDealTabs = new ArrayList<>();
        DealTab currentTab = null;
        for (Map.Entry<Integer, DealGroupBaseDTO> entry: dealBaseMap.entrySet()) {
            Integer dpDealGroupId = entry.getKey();
            DealTab dealTab = buildDealTab(dpDealGroupId, holder);
            if (dealTab == null) {
                continue;
            }

            if (dpDealGroupId == currentDpDealGroupId) {
                currentTab = dealTab;
            } else {
                relatedDealTabs.add(dealTab);
            }
        }

        if (currentTab == null) {
            return null;
        }
        DealTabHolder dealTabHolder = new DealTabHolder();
        dealTabHolder.setCurrentTab(currentTab);
        dealTabHolder.setRelatedTabs(relatedDealTabs);
        return dealTabHolder;
    }

    protected List<String> dealAttrsToLoadAfterRelate() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.tab.AllRelateDeals.dealAttrsToLoadAfterRelate()");
        return Lists.newArrayList();
    }

    protected DealTab buildDealTab(Integer dpDealGroupId, SourceDataHolder holder) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.tab.AllRelateDeals.buildDealTab(java.lang.Integer,com.dianping.mobile.mapi.dztgdetail.tab.bo.SourceDataHolder)");
        return null;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    protected static class AllDealSourceDataHolder extends SourceDataHolder {
        private Map<Integer, List<AttributeDTO>> dealAttrMap;
        private Map<Integer, DealDetailDto> dealStructMap;
        private Map<Integer, PriceDisplayDTO> dealNormalPriceMap;
    }

}
