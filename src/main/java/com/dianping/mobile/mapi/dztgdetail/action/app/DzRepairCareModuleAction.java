package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DzRepairCareModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DzRepairCareModule;
import com.dianping.mobile.mapi.dztgdetail.facade.repaircare.DzRepairCareFacade;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.HttpMethod;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.builder.ReflectionToStringBuilder;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * Created by liumu on 2025/01/23.
 */
@InterfaceDoc(displayName = "到综团详页安心修模块接口",
        type = "restful",
        description = "查询到综团详页安心修模块接口",
        scenarios = "仅试用于到综团详页开通了安心修的团单",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/dzrepaircare.bin",
        authors = "liumu"
)
@Controller("general/platform/dztgdetail/dzrepaircare.bin")
@Action(url = "dzrepaircare.bin", httpType = "get")
@Slf4j
public class DzRepairCareModuleAction extends AbsAction<DzRepairCareModuleReq> {

    @Resource
    private DzRepairCareFacade dzRepairCareFacade;

    @Resource
    private DealGroupWrapper dealGroupWrapper;

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "dzrepaircare.bin",
            displayName = "到综团详页安心修模块接口",
            description = "查询到综团详页安心修模块接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "dzrepaircare.bin请求参数",
                            type = DzRepairCareModuleAction.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "result", description = "安心修模块数据", type = DzRepairCareModule.class)},
            restExampleUrl = "http://mapi.dianping.com/general/platform/dztgdetail/dzrepaircare.bin?" +
                    "dealgroupid=200139713",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    ),
            }
    )
    @Override
    protected IMobileResponse validate(DzRepairCareModuleReq request, IMobileContext iMobileContext) {
        IdUpgradeUtils.processProductIdForDzRepairCareModuleReq(request, "dzrepaircare.bin");
        if (request == null || request.getDealgroupid() <= 0) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @Override
    @CryptMethod
    protected IMobileResponse execute(DzRepairCareModuleReq request, IMobileContext iMobileContext) {
        try {
            EnvCtx envCtx = initEnvCtxV2(iMobileContext);
            int dpDealGroupId = envCtx.isMt() ? dealGroupWrapper.getDpDealGroupId(request.getDealgroupid()) : request.getDealgroupid();
            DzRepairCareModule module = dzRepairCareFacade.getDzRepairCareModule(dpDealGroupId);
            if (Objects.isNull(module)) {
                return Resps.NoDataResp;
            }
            return new CommonMobileResponse(module);
        } catch (Exception e) {
            logger.error(String.format("dzrepaircare.bin failed, params: request=%s, context=%s",
                    ReflectionToStringBuilder.toString(request), ReflectionToStringBuilder.toString(iMobileContext)), e);
        }
        return Resps.SYSTEM_ERROR;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }

}
