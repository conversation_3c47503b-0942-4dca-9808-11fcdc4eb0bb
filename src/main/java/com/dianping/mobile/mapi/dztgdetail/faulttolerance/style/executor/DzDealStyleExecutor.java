package com.dianping.mobile.mapi.dztgdetail.faulttolerance.style.executor;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.mobile.framework.base.datatypes.StatusCode;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.biz.service.cellarservice.DealStyleCacheBizService;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealFlashReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.Response;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealStyleBO;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.facade.DealQueryParallFacade;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.req.DealBaseContextRequest;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.utils.FaultToleranceUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.sankuai.athena.stability.faulttolerance.core.Executor;
import com.sankuai.athena.stability.faulttolerance.tracer.ExceptionTracer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-12-06
 * @desc 团详样式处理器
 */
@Component
@Slf4j
public class DzDealStyleExecutor implements Executor<DealBaseContextRequest, CommonMobileResponse> {
    @Resource
    private DealQueryParallFacade dealQueryParallFacade;

    @Resource
    private DealStyleCacheBizService dealStyleCacheBizService;

    @Override
    public CommonMobileResponse execute(DealBaseContextRequest context) {
        try {
            ExceptionTracer.start();
            CommonMobileResponse response = getExecuteResult(context.getRequest(), context.getEnvCtx());
            StatusCode statusCode = response.getStatusCode();
            // 根据返回的状态码和响应数据，触发容错处理
            FaultToleranceUtils.triggerFaultTolerantByUnExpectResult("dzDealStyle", statusCode.getCode(), response.getData());
            return response;
        } finally {
            ExceptionTracer.end();
        }
    }

    public CommonMobileResponse getExecuteResult(DealBaseReq request, EnvCtx envCtx) {
        Response<DealStyleBO> response;
        try {
            response = dealQueryParallFacade.queryDealGroupStyle(request, envCtx);
            // 异步将写缓存
            if (LionConfigUtils.hitDealDetailFlashCache(request.getDealgroupid())){
                saveResultToCache(request, response);
            }
            return new CommonMobileResponse(response.getResult());
        } catch (Exception e) {
            Cat.logError("dzdealstyle.bin error", e);
        }
        return Resps.SYSTEM_ERROR;
    }
    
    public void saveResultToCache(DealBaseReq request, Response<DealStyleBO> response){
        try{
            DealFlashReq flashReq = dealStyleCacheBizService.convertToDealFlashReq(request);
            dealStyleCacheBizService.saveOrUpdate(flashReq, cacheValueFilter(response.getResult()),"general/platform/dztgdetail/dzdealstyle.bin", "团详样式缓存结果打点上报");
        }catch (Exception e){
            log.error("团详快接口，写缓存失败，saveResultToCache error:{}", e);
        }
    }

    /**
     * 过滤无需缓存的字段
     * @param result
     * @return
     */
    public DealStyleBO cacheValueFilter(DealStyleBO result){
        if (Objects.nonNull(result) && Objects.nonNull(result.getModuleConfigsModule())){
            String dealStyleJson = JSON.toJSONString(result);
            DealStyleBO newDealStyle = JSON.parseObject(dealStyleJson, DealStyleBO.class);
            newDealStyle.getModuleConfigsModule().setModuleAbConfigs(null);
            return newDealStyle;
        }
        return result;
    }
}
