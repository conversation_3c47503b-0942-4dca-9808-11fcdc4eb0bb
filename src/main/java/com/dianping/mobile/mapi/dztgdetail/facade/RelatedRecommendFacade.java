package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.mobile.mapi.dztgdetail.biz.service.recommend.CrossShopRecommendHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.service.recommend.InShopRecommendHandler;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.RelatedRecommendCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedRecommendReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedRecommendVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Author: <EMAIL>
 * @Date: 2024/10/23
 */
@Service
public class RelatedRecommendFacade {
    @Resource
    private CrossShopRecommendHandler crossShopRecommendHandler;
    @Resource
    private InShopRecommendHandler inShopRecommendHandler;

    public RelatedRecommendVO getCrossShopRecommendResult(RelatedRecommendReq req, EnvCtx envCtx) {
        return crossShopRecommendHandler.doExecute(RelatedRecommendCtx.builder().req(req).envCtx(envCtx).build());
    }

    public RelatedRecommendVO getInShopRecommendResult(RelatedRecommendReq req, EnvCtx envCtx) {
        return inShopRecommendHandler.doExecute(RelatedRecommendCtx.builder().req(req).envCtx(envCtx).build());
    }
}
