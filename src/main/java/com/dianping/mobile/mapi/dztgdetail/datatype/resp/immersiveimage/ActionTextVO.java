package com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-08-28
 */
@Setter
@Getter
@TypeDoc(description = "查看更多热门款式交互动作引导语")
@MobileDo(id = 0x120f)
public class ActionTextVO implements Serializable {
    @FieldDoc(description = "热门款式频道页跳链")
    @MobileDo.MobileField(key = 0x774e)
    private String jumpUrl;

    @FieldDoc(description = "释放时icon")
    @MobileDo.MobileField(key = 0x7192)
    private String releaseIcon;

    @FieldDoc(description = "释放时引导语")
    @MobileDo.MobileField(key = 0x8963)
    private String releaseText;

    @FieldDoc(description = "查看更多热门款式icon")
    @MobileDo.MobileField(key = 0xcce5)
    private String holdIcon;

    @FieldDoc(description = "查看更多热门款式引导语")
    @MobileDo.MobileField(key = 0xd7ce)
    private String holdText;
}
