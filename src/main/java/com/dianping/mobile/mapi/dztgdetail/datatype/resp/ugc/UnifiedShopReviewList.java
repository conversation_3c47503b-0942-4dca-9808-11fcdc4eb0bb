package com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/12/13.
 */
@TypeDoc(description = "用户评论数据")
@MobileDo(id = 0x50fb)
@Data
public class UnifiedShopReviewList implements Serializable {

    @FieldDoc(description = "评论头部标题")
    @MobileField(key = 0x3bc8)
    private String topTitle;

    @FieldDoc(description = "头部标题附加信息")
    @MobileField(key = 0x4dad)
    private String appendTitle;

    @FieldDoc(description = "评论总数")
    @MobileField(key = 0xb3c7)
    private int recordCount;

    @FieldDoc(description = "查看全部点评跳转链接")
    @MobileField(key = 0x8283)
    private String redirectUrl;

    @FieldDoc(description = "评论底部标题")
    @MobileField(key = 0xe1f9)
    private String bottomTitle;

    @FieldDoc(description = "是否展示底部查看全部网友评论")
    @MobileField(key = 0xf353)
    private boolean displayBottom;

    @FieldDoc(description = "评论信息")
    @MobileField(key = 0x7cea)
    private List<ReviewDetailDO> reviewDetailList;

    @FieldDoc(description = "评价标签列表")
    @MobileField(key = 0x5665)
    private List<ReviewTagDO> reviewTagList;
}
