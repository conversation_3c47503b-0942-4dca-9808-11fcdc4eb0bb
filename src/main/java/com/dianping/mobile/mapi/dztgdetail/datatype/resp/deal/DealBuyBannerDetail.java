package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 2020/3/17 4:45 下午
 */
@Data
@MobileDo
public class DealBuyBannerDetail implements Serializable {
    /**
     * 减后价格
     */
    @MobileDo.MobileField(key = 0xb716)
    private String price;
    /**
     *
     */
    @MobileDo.MobileField(key = 0x6242)
    private String marketPrice;
    /**
     * 浮层内容，jsonlabel格式
     */
    @MobileDo.MobileField(key = 0xcce)
    private List<String> content;
    /**
     * 跳转链接
     */
    @MobileDo.MobileField(key = 0x8283)
    private String redirectUrl;
    /**
     * 按钮文案
     */
    @MobileDo.MobileField(key = 0xe221)
    private String buttonText = "立即购买";
}
