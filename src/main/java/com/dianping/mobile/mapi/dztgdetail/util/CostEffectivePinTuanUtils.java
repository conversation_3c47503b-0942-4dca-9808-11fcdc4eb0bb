package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.lion.client.util.CollectionUtils;
import com.dianping.mobile.mapi.dztgdetail.common.constants.PinTuanConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.StyleTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.sankuai.inf.octo.mns.model.HostEnv;
import com.sankuai.inf.octo.mns.util.ProcessInfoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2024/4/23
 */
@Slf4j
public class CostEffectivePinTuanUtils {
    private static final String PIN_TUAN_SCHEMA = ProcessInfoUtil.getHostEnv() == HostEnv.DEV || ProcessInfoUtil.getHostEnv() == HostEnv.TEST
            ? "http://awp.hfe.st.meituan.com/dfe/duo-page/group-pintuan-result/web/index.html"
            : "https://awp.meituan.com/dfe/duo-page/group-pintuan-result/web/index.html";
    public static final Map<Integer, Boolean> COST_EFFECTIVE_PIN_TUAN_BUTTON_STYLE = new HashMap<Integer, Boolean>() {
        {
            put(StyleTypeEnum.PINTUAN_WAITING_TWO_BUTTONS.code, true);
            put(StyleTypeEnum.PINTUAN_WEAK_ACTIVE_TWO_BUTTONS.code, true);
            put(StyleTypeEnum.PINTUAN_ENHANCE_PASSIVE_JOIN_SINGLE_BUTTON.code, true);
            put(StyleTypeEnum.PINTUAN_ENHANCE_SINGLE_BUTTON.code, true);
        }
    };

    public static final Map<String, Boolean> COST_EFFECTIVE_PINTUAN_TITLE = new HashMap<String, Boolean>(){
        {
            put(PinTuanConstants.SINGLE_BUY, true);
            put(PinTuanConstants.DIRECT_BUY, true);
        }
    };

    /**
     * 1. 是否展示优惠条信息
     * 2. 不同底bar
     */
    public static boolean isCePinTuaScene(DealCtx ctx) {
        return ctx.getCostEffectivePinTuan().isCePinTuanScene();
    }

    /**
     * 是否已开团，是则展示拼团样式，否则不展示
     * @return
     */
    public static boolean pinTuanOpened(DealCtx ctx) {
        return ctx.getCostEffectivePinTuan().isCePinTuanScene()
                && ctx.getCostEffectivePinTuan().isPinTuanOpened();
    }

    /**
     * 是否主态
     * @param ctx
     * @return
     */
    public static boolean activePinTuan(DealCtx ctx) {
        return pinTuanOpened(ctx)
                && ctx.getCostEffectivePinTuan().isActivePinTuan();
    }

    /**
     * 是否为强化样式 前提是有拼团优惠
     * @param ctx
     * @return
     */
    public static boolean enhancedStyle(DealCtx ctx) {
        return Objects.nonNull(ctx.getCostEffectivePinTuan().getSceneType())
                && ctx.getCostEffectivePinTuan().getSceneType() == 56;
    }

    public static boolean inPinTuan(DealCtx ctx) {
        return pinTuanOpened(ctx)
                && ctx.getCostEffectivePinTuan().isInPinTuan();
    }

    public static Integer getNeedMemberCount(DealCtx ctx) {
        Integer needMemberCount = ctx.getCostEffectivePinTuan().getHelpSuccCountMin() - ctx.getCostEffectivePinTuan().getHasHelpCount();
        return Objects.isNull(needMemberCount) ? 0 : needMemberCount;
    }

    public static String getWxShareJumpUrl(DealCtx context) {
        String result = StringUtils.EMPTY;
        String pinTuanUrl = String.format("%s?orderGroupId=%s&scene=share_page", PIN_TUAN_SCHEMA, context.getCostEffectivePinTuan().getShareToken());
        try {
            result = String.format("/index/pages/h5/h5?weburl=%s&f_openId=1&f_userId=1&f_token=1&f_userId=1&f_ci=1&f_pos=1&barcol=FF112E&barfcol=FFFFFF&title=特价团购拼团",
                    URLEncoder.encode(pinTuanUrl, "UTF-8"));
        } catch (Exception e) {
            log.error("getShareJumpUrl error", e);
        }
        return result;
    }

    public static String getPinTuanResultPageUrl(DealCtx ctx) {
        return DztgClientTypeEnum.MEITUAN_APP.equals(ctx.getEnvCtx().getDztgClientTypeEnum())
                ? String.format("imeituan://www.meituan.com/mrn?mrn_biz=meishi&mrn_entry=group-pintuan-result&mrn_component=main&orderGroupId=%s&scene=share_page", ctx.getCostEffectivePinTuan().getShareToken())
                : String.format("http://awp.meituan.com/dfe/duo-page/group-pintuan-result/web/index.html?orderGroupId=xxx&scene=share_page", ctx.getCostEffectivePinTuan().getShareToken());
    }

    public static void updateDirectBuyBarForPinTuan(List<DealBuyBtn> buyButtons) {
        if (CollectionUtils.isEmpty(buyButtons)) {
            return;
        }
        // 剔除拼团后的第一个底bar
        DealBuyBtn firstBtnExceptPinTuan = buyButtons.stream().filter(btn -> Objects.nonNull(btn.getPriceRuleModule()) && btn.getPriceRuleModule().getPriceRuleType() != BuyBtnTypeEnum.COST_EFFECTIVE_PINTUAN.getCode()).findFirst().orElse(null);

        buyButtons.stream().filter(btn -> Objects.nonNull(btn.getPriceRuleModule()) && btn.getPriceRuleModule().getPriceRuleType() == BuyBtnTypeEnum.COST_EFFECTIVE_PINTUAN.getCode())
                .forEach(btn -> updateNormalButton(btn, firstBtnExceptPinTuan));
    }


    /**
     * 将非特团拼团的第一个按钮对应属性赋值到直接购买按钮上
     * @param btn
     * @param firstBtnExceptPinTuan
     * @return
     */
    private static DealBuyBtn updateNormalButton(DealBuyBtn btn, DealBuyBtn firstBtnExceptPinTuan) {
        if (Objects.isNull(firstBtnExceptPinTuan)) {
            return btn;
        }

        // 直接购买底bar的价格和跳链使用非拼团的第一个底bar数据
        if (COST_EFFECTIVE_PINTUAN_TITLE.containsKey(btn.getBtnTitle())) {
            btn.setPriceStr(firstBtnExceptPinTuan.getPriceStr());
            btn.setRedirectUrl(firstBtnExceptPinTuan.getRedirectUrl());
            return btn;
        }

        // 拼团购买和直接购买按钮是否展示浮层保持一致
        if (firstBtnExceptPinTuan.getRedirectUrl().contains("mrn_component=dealsubmitorderpage-popup") && StringUtils.isNotBlank(btn.getRedirectUrl()) && !btn.getRedirectUrl().contains("mrn_component=dealsubmitorderpage-popup")) {
            btn.setRedirectUrl(btn.getRedirectUrl().replaceAll("mrn_component=dealsubmitorderpage", "mrn_component=dealsubmitorderpage-popup"));
        }
        return btn;
    }

    public static boolean isCostEffectivePinTuanButtonStyle(DealCtx ctx) {
        if (Objects.isNull(ctx.getResult()) || Objects.isNull(ctx.getResult().getBuyBar())) {
            return false;
        }
        return COST_EFFECTIVE_PIN_TUAN_BUTTON_STYLE.containsKey(ctx.getResult().getBuyBar().getStyleType());
    }
}
