package com.dianping.mobile.mapi.dztgdetail.biz;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.DecryptVO;
import com.dianping.poi.open.common.Result;
import com.dianping.poi.open.constants.RequestIdType;
import com.dianping.poi.open.service.OpenDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/10/28
 */
@Component
@Slf4j
public class DecryptBiz {

    @Autowired
    private OpenDataService openDataService;

    public DecryptVO decrypt(String encryptedStr, boolean isMt) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.biz.DecryptBiz.decrypt(java.lang.String,boolean)");
        Result result = null;

        try {
            result = openDataService.getBatchShopIdByOpenShopId(
                    "89e1a8d62672466a93624d8e340b9d16",
                    Collections.singletonList(encryptedStr),
                    isMt ? RequestIdType.MT_ID.getType() : RequestIdType.DP_ID.getType());
        } catch (Exception e) {
            log.error("invoke OpenDataService.getBatchShopIdByOpenShopId err", e);
        }

        if (result == null || !result.isSuccess() || result.getData() == null) {
            return null;
        }

        Map<String, Long> map = (Map<String, Long>) result.getData();

        if (MapUtils.isEmpty(map)) {
            return null;
        }

        if (map.containsKey(encryptedStr)) {
            DecryptVO vo = new DecryptVO();

            //此种情况说明encryptedStr非加密字符串，所以原值返回
            if (map.get(encryptedStr) == null) {
                vo.setDecryptedStr(encryptedStr);
            } else {
                vo.setDecryptedStr(String.valueOf(map.get(encryptedStr)));
            }

            return vo;
        }

        return null;
    }
}
