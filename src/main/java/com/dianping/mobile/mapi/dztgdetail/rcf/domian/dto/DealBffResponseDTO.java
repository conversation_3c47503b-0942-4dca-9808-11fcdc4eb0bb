package com.dianping.mobile.mapi.dztgdetail.rcf.domian.dto;

import com.dianping.deal.bff.cache.enums.RcfDealBffInterfaceEnum;
import lombok.Getter;

import java.util.Map;

/**
 * @Author: guangyujie
 * @Date: 2024/11/26 19:46
 */
@Getter
public class DealBffResponseDTO {

    private final Map<RcfDealBffInterfaceEnum, Object> bffResponse;

    public DealBffResponseDTO(Map<RcfDealBffInterfaceEnum, Object> bffResponse) {
        this.bffResponse = bffResponse;
    }

    @SuppressWarnings("unchecked")
    public <T> T getBffResponse(RcfDealBffInterfaceEnum interfaceEnum) {
        return (T) bffResponse.get(interfaceEnum);
    }

}
