package com.dianping.mobile.mapi.dztgdetail.datatype.resp.more;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/12/13.
 */
@TypeDoc(description = "更多模块信息")
@MobileDo(id = 0x2154)
@Data
public class UnifiedMoreItemList implements Serializable {

    @FieldDoc(description = "标题")
    @MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "副标题")
    @MobileField(key = 0xd894)
    private String subTitle;

    @FieldDoc(description = "模块基础数据")
    @MobileField(key = 0x987a)
    private List<MoreItemDTO> itemList;

}
