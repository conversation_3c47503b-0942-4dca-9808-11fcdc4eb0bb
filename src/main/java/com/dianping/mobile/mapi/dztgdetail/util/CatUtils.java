package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-01-10-9:33 PM
 */
@Slf4j
public class CatUtils {

    public static final String SALES_CONFUSION_NUM = "110";
    public static final String SALES_CONFUSION = "SalesConfusion";

    public static void metric4SalesConfusion(EnvCtx envCtx, boolean isMt) {
        if (StringUtils.isBlank(envCtx.getMtsiFlag()) || !envCtx.getMtsiFlag().equals(SALES_CONFUSION_NUM)) {
            return;
        }
        Map<String, String> metricTags = Maps.newHashMap();
        metricTags.put("platform", isMt ? "MT" : "DP");
        metricTags.put("client", envCtx.getDztgClientTypeEnum().getDesc());
        Cat.logMetricForCount(SALES_CONFUSION, metricTags);
    }

    public static void reportDealClientType(String interfaceName, EnvCtx envCtx, Integer categoryId, Integer dealGroupId, String pageSource) {
        try {
            List<Integer> nibCategoryIds = LionConfigUtils.getNibCategoryIds();
            int dealCategoryId = Objects.isNull(categoryId) ? 0 : categoryId;
            if (nibCategoryIds.contains(dealCategoryId)) {
                return;
            }
            String clientTypeName = envCtx.getDztgClientTypeEnum().name();
            String eventData = String.format("%s-%s-%s", clientTypeName, categoryId, pageSource);
            Cat.logEvent(interfaceName, eventData);
            log.info("非到综团单, 美团团单ID={}, 环境参数={}, 渠道={}", dealGroupId, envCtx, pageSource);
        } catch (Exception e) {
            log.error("reportDealClientType error", e);
        }
    }
}
