package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.account.MeituanUserService;
import com.dianping.account.UserAccountService;
import com.dianping.account.dto.MeituanUserInfoDTO;
import com.dianping.account.dto.UserAccountDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.util.concurrent.Future;

@Slf4j
public class UserAccountInfoProcessor extends AbsDealProcessor {

    /**
     * 点评用户通用服务
     */
    @Resource
    @Qualifier("userAccountServiceFuture")
    private UserAccountService userAccountServiceFuture;

    /**
     * 美团用户通用服务
     */
    @Resource
    @Qualifier("meituanUserServiceFuture")
    private MeituanUserService meituanUserServiceFuture;

    @Override
    public void prepare(DealCtx ctx) {
        long userId = ctx.isMt() ? ctx.getEnvCtx().getMtUserId() : ctx.getEnvCtx().getDpUserId();//已确认判断平台后再使用
        if (ctx.isMt()) {
            ctx.getFutureCtx().setMtUserAccountDTOFuture(getMobileByMtUserId(userId));
        } else {
            ctx.getFutureCtx().setDpUserAccountDTOFuture(getMobileByDpUserId(userId));
        }
    }

    @Override
    public void process(DealCtx ctx) {
    }

    /**
     * 根据点评用户id获取手机号码
     */
    @SuppressWarnings("unchecked")
    public Future<UserAccountDTO> getMobileByDpUserId(long userId) {
        if (userId <= 0) {
            return null;
        }
        try {
            userAccountServiceFuture.loadById(userId);
            return (Future<UserAccountDTO>) FutureFactory.getFuture();
        } catch (Exception e) {
            log.error("FitnessCrossDealProcessor.getMobileByDpUserId fail, userId is [{}], e is ", userId, e);
            return null;
        }
    }

    /**
     * 根据美团用户id获取手机号码
     */
    @SuppressWarnings("unchecked")
    public Future<MeituanUserInfoDTO> getMobileByMtUserId(long userId) {
        if (userId <= 0) {
            return null;
        }
        try {
            meituanUserServiceFuture.loadUserByMTUid(userId);
            return (Future<MeituanUserInfoDTO>) FutureFactory.getFuture();
        } catch (Exception e) {
            log.error("FitnessCrossDealProcessor.getMobileByMtUserId fail, userId is [{}], e is ", userId, e);
            return null;
        }
    }

}
