package com.dianping.mobile.mapi.dztgdetail.availabletime;

import com.dianping.mobile.mapi.dztgdetail.availabletime.impl.DefaultAvailableTimeStrategy;
import com.dianping.mobile.mapi.dztgdetail.availabletime.impl.KTVAvailableTimeStrategy;
import com.dianping.mobile.mapi.dztgdetail.availabletime.impl.WineBarAvailableTimeStrategy;
import com.dianping.mobile.mapi.dztgdetail.common.enums.AvailableTimeStrategyEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class AvailableTimeStrategyFactory implements InitializingBean {
    @Autowired
    private List<AvailableTimeStrategy> strategyList;

    private static final Map<AvailableTimeStrategyEnum, AvailableTimeStrategy> AVAILABLE_TIME_STRATEGY_MAP = Maps
            .newEnumMap(AvailableTimeStrategyEnum.class);

    public AvailableTimeStrategy getStrategy(DealCtx dealCtx) {
        AvailableTimeStrategyEnum availableTimeStrategyType = AvailableTimeStrategyEnum
                .valueOf(dealCtx.getCategoryId());
        AvailableTimeStrategy availableTimeStrategy = AVAILABLE_TIME_STRATEGY_MAP.get(availableTimeStrategyType);
        if (availableTimeStrategy == null) {
            return AVAILABLE_TIME_STRATEGY_MAP.get(AvailableTimeStrategyEnum.DEFAULT_STRATEGY);
        }
        return availableTimeStrategy;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        strategyList.forEach(strategy -> AVAILABLE_TIME_STRATEGY_MAP.put(strategy.getStrategyType(), strategy));
    }
}