package com.dianping.mobile.mapi.dztgdetail.biz.processor.headerpic;

import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2023/8/20
 */
public interface HeaderPicProcessor {
    void fillPicScale(DealCtx ctx, List<ContentPBO> result, DealGroupPBO dealGroupPBO);
}
