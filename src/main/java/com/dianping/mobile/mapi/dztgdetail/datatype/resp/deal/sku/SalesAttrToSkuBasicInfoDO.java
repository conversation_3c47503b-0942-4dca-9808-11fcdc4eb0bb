package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.sku;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

import java.io.Serializable;

@MobileDo(id = 0xa2eb)
public class SalesAttrToSkuBasicInfoDO implements Serializable {
    /**
     * sku基础信息
     */
    @MobileDo.MobileField(key = 0xd145)
    private SkuBasicInfoDO skuBasicInfo;

    /**
     * 销售属性组合
     */
    @MobileDo.MobileField(key = 0x3352)
    private String salesAttrInfo;


    @FieldDoc(description = "销售属性组合（带属性ID）")
    @MobileDo.MobileField(key = 0xb673)
    private String salesAttrInfoKey;

    public SkuBasicInfoDO getSkuBasicInfo() {
        return skuBasicInfo;
    }

    public void setSkuBasicInfo(SkuBasicInfoDO skuBasicInfo) {
        this.skuBasicInfo = skuBasicInfo;
    }

    public String getSalesAttrInfo() {
        return salesAttrInfo;
    }

    public void setSalesAttrInfo(String salesAttrInfo) {
        this.salesAttrInfo = salesAttrInfo;
    }

    public String getSalesAttrInfoKey() {
        return salesAttrInfoKey;
    }

    public void setSalesAttrInfoKey(String salesAttrInfoKey) {
        this.salesAttrInfoKey = salesAttrInfoKey;
    }
}
