package com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.extend.impl;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.extend.PromoDetailHandler;
import com.dianping.mobile.mapi.dztgdetail.common.enums.PromoDetailEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBestPromoDetail;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ServiceGuarantee.DealBestPromoDetailDTO;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Component
public class PlatformSubsidiesPromoDetailHandler implements PromoDetailHandler {

    @Override
    public PromoDetailEnum getPromoDetailEnum() {
        return PromoDetailEnum.platformSubsidies;
    }

    @Override
    public DealBestPromoDetailDTO getDealBestPromoDetail(PriceDisplayDTO priceDisplayDTO) {
        DealBestPromoDetail promoDetail = new DealBestPromoDetail();
        promoDetail.setPromoName(PromoDetailEnum.platformSubsidies.getName());

        BigDecimal amount = new BigDecimal(0);

        for (PromoDTO promoDTO : priceDisplayDTO.getUsedPromos()) {
            if (promoDTO.getIdentity() != null && promoDTO.getAmount() != null && promoDTO.getIdentity().getSourceType() != 2) {
                amount = amount.add(promoDTO.getAmount());
            }
        }

        if (amount.compareTo(new BigDecimal(0)) == 0) {
            return null;
        }

        promoDetail.setPromoAmount(String.valueOf(amount));

        DealBestPromoDetailDTO dealBestPromoDetailDTO = new DealBestPromoDetailDTO();
        dealBestPromoDetailDTO.setDealBestPromoDetail(promoDetail);
        dealBestPromoDetailDTO.setPromoAmount(amount);

        return dealBestPromoDetailDTO;
    }
}
