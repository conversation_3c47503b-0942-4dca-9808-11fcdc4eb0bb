package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.style.dto.DealGroupDzxInfo;
import com.dianping.deal.style.dto.StyleAbConfig;
import com.dianping.deal.style.dto.StyleExp;
import com.dianping.deal.style.dto.StyleResponse;
import com.dianping.deal.style.enums.DzxPlatform;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealStyleWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.MttgVersion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealStyles;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BeautySubType;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DealAttrCons;
import com.dianping.mobile.mapi.dztgdetail.common.enums.KeyEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.PoiSubCate;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.*;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import com.dianping.mobile.mapi.dztgdetail.util.DealStyleUtil;
import com.dianping.mobile.mapi.dztgdetail.util.SwitchUtils;
import com.dianping.tuangou.dztg.bjwrapper.api.dto.MtDealDto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;

public class DealStyleProcessor extends AbsDealProcessor{
    private static final List<String> GC_DEFAULT_STYLE_LIST = com.google.common.collect.Lists.newArrayList("scenic","travel","travel_selected","food","default");

    @Autowired
    private DealStyleWrapper dealStyleWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        return ctx.getEnvCtx() != null && !ctx.getEnvCtx().isFromH5();
    }

    @Override
    public void prepare(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealStyleProcessor.prepare(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        if(ctx.isMt()) {
            Future styleFuture = dealStyleWrapper.getStyleFutureForMt(ctx);
            Future dzxFuture = dealStyleWrapper.prepareGetDealGroupDzxInfo(ctx.getMtId(), ctx.getEnvCtx().isAndroid() ? DzxPlatform.MT_APP_ANDROID.type : DzxPlatform.MT_APP_IOS.type);
            Future mtDealDtoListFuture = dealStyleWrapper.preMtDealDtoList(Lists.newArrayList(ctx.getMtId()));

            ctx.getFutureCtx().setStyleFuture(styleFuture);
            ctx.getFutureCtx().setDzxFuture(dzxFuture);
            ctx.getFutureCtx().setMtDealDtoListFuture(mtDealDtoListFuture);
        } else {
            Future dpStyleFuture = dealStyleWrapper.getStyleFutureForDp(ctx);
            ctx.getFutureCtx().setStyleFuture(dpStyleFuture);
        }
    }

    @Override
    public void process(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealStyleProcessor.process(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        ctx.setStyleResponse(dealStyleWrapper.getFutureResult(ctx.getFutureCtx().getStyleFuture()));
        ctx.setDealGroupDzxInfo(dealStyleWrapper.getFutureResult(ctx.getFutureCtx().getDzxFuture()));
        ctx.setMtDealDtoList(dealStyleWrapper.getFutureResult(ctx.getFutureCtx().getMtDealDtoListFuture()));

        if(ctx.isMt()) {
            MTTemplateKey mtTemplateKey = buildTemplateKey(ctx);
            ctx.setMtTemplateKey(mtTemplateKey);
        } else {
            DealStyle dealStyle = getDealStyle(ctx);
            ctx.setDealStyle(dealStyle);
        }
    }

    public MTTemplateKey buildTemplateKey(DealCtx dealCtx) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealStyleProcessor.buildTemplateKey(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        if (SwitchUtils.isAllNotDzx()) {
            return getDegradeTemplateKey();
        }

        boolean isAndroid = dealCtx.getEnvCtx().isAndroid();
        DealGroupDzxInfo dzxInfo = dealCtx.getDealGroupDzxInfo();

        if (dzxInfo == null || !dzxInfo.isDzx()) {
            return getDegradeTemplateKey();
        }

        List<MtDealDto> mtDealDtoList = dealCtx.getMtDealDtoList();
        MtDealDto dealModel = CollectionUtils.isEmpty(mtDealDtoList) ? null : mtDealDtoList.get(0);
        if (dealModel == null) {
            return getDefaultTemplateKey(dzxInfo.getDpDealGroupId(), dzxInfo.isDzx());
        }

        if (StringUtils.isEmpty(dealCtx.getReqChannel())) {
            dealCtx.setReqChannel(dealModel.getChannel());
        }

        MTTemplateKey templateKeyDo = new MTTemplateKey();
        templateKeyDo.setDzx(dzxInfo.isDzx());
        templateKeyDo.setDpOrder(isDpOrder(dzxInfo.getDpDealGroupId(), templateKeyDo.isDzx()));

        String key = generateKey(
                dealCtx,
                dealModel.getDt(),
                dealModel.getFrontPoiCates(), //丽人的subId=0时，需要根据poiCateId去区分
                dealCtx.getEnvCtx().getVersion(),
                dealCtx.getReqChannel(),
                isAndroid,
                dealCtx.isExternal()
        );

        StyleResponse styleResponse = dealCtx.getStyleResponse();
        templateKeyDo.setKey(styleResponse != null && StringUtils.isNotBlank(styleResponse.getStyle()) ? styleResponse.getStyle() : key);

        if (styleResponse != null && CollectionUtils.isNotEmpty(styleResponse.getStyleAbConfigs())) {
            List<ModuleAbConfigDo> moduleAbConfigDos = new ArrayList<>();

            for (StyleAbConfig config : styleResponse.getStyleAbConfigs()) {

                ModuleAbConfigDo moduleAbConfigDo = new ModuleAbConfigDo();
                moduleAbConfigDo.setKey(config.getKey());
                List<AbConfigDo> abConfigDos  = new ArrayList<>();

                if (StringUtils.isNotBlank(config.getKey()) && CollectionUtils.isNotEmpty(config.getStyleExps())) {
                    for (StyleExp exp : config.getStyleExps()) {
                        AbConfigDo abConfigBo = new AbConfigDo(exp.getExpId(), exp.getExpResult(), exp.getExpBiInfo());
                        abConfigDos.add(abConfigBo);
                    }
                }

                moduleAbConfigDo.setConfigs(abConfigDos);
                moduleAbConfigDos.add(moduleAbConfigDo);

            }

            templateKeyDo.setModuleAbConfigs(moduleAbConfigDos);
        }

        return templateKeyDo;
    }

    private String generateKey(
            DealCtx dealCtx, int dt, List cateIds, String ver, String channel, boolean isAndroid, boolean isExternal) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealStyleProcessor.generateKey(DealCtx,int,List,String,String,boolean,boolean)");

        String key = KeyEnum.DEAFAULT.getChannel();

        try {

            MttgVersion version = new MttgVersion(ver);
            KeyEnum keyEnum = KeyEnum.findBgEnum(channel, version, isAndroid);

            if (keyEnum.equals(KeyEnum.BEAUTY) || keyEnum.equals(KeyEnum.BEAUTY_DEFAULT)) {
                key = generateBeautyKey(dt, cateIds, keyEnum);
            } else if (keyEnum.equals(KeyEnum.CHILDREN)) {
                key = generateChildrenKey(dealCtx);
            } else if (keyEnum.equals(KeyEnum.ENTERTAINMENT)) {
                key = generateEntertainmentKey(dt);
            } else if (keyEnum.equals(KeyEnum.MEDICINE)) {
                key = KeyEnum.DEAFAULT.getChannel();
            } else {
                key = keyEnum.getChannel();
            }

        } catch (Exception e) {
            logger.error("MTDetailTemplateFacadeImpl.generateKey error", e);
            return key;
        }


        if (isExternal) {
            //外部请求（如快手小程序）支持的样式
            List<String> configs = Lion.getList("mapi-mttgdetail-web.external.support.styles", String.class, new ArrayList<String>());
            if (CollectionUtils.isNotEmpty(configs) && !configs.contains(key)) {
                key =  KeyEnum.DEAFAULT.getChannel();
            }
        }

        return key;
    }

    private String generateBeautyKey(int subId, List cateIds, KeyEnum keyEnum) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealStyleProcessor.generateBeautyKey(int,java.util.List,com.dianping.mobile.mapi.dztgdetail.common.enums.KeyEnum)");
        if (keyEnum.equals(KeyEnum.BEAUTY)) {
            keyEnum = KeyEnum.DEAFAULT;
        }

        String key = keyEnum.getChannel();

        if (CollectionUtils.isNotEmpty(cateIds)) {
            if (!cateIds.contains(PoiSubCate.BEAUTYHAIR.getValue()) && !cateIds.contains(PoiSubCate.BEAUTYNAIL.getValue())) {
                key = keyEnum.getChannel();
            } else if (cateIds.contains(PoiSubCate.BEAUTYHAIR.getValue())) {
                key = PoiSubCate.BEAUTYHAIR.getName();
            } else if (cateIds.contains(PoiSubCate.BEAUTYNAIL.getValue())) {
                key = PoiSubCate.BEAUTYNAIL.getName();
            }
        } else {
            BeautySubType beautySubType = BeautySubType.findSubTypeName(subId);

            if (beautySubType == BeautySubType.BEAUTIFYHAIR || beautySubType == BeautySubType.BEAUTIFYHAIRDP || beautySubType == BeautySubType.BEAUTIFYHAIRDP1) {
                key = BeautySubType.BEAUTIFYHAIR.getSubTypeName();
            } else if (beautySubType == BeautySubType.BEAUTIFYNAILORHANDS || beautySubType == BeautySubType.BEAUTIFYNAILOREYELASH) {
                key = BeautySubType.BEAUTIFYNAILOREYELASH.getSubTypeName();
            } else {
                key = keyEnum.getChannel();
            }
        }

        return key;
    }

    private String generateEntertainmentKey(int dt) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealStyleProcessor.generateEntertainmentKey(int)");
        if (dt == 48 || dt == 49 || dt == 5303) {
            return KeyEnum.JOY_MASSAGE.getChannel();
        }
        return KeyEnum.DEAFAULT.getChannel();
    }

    private String generateChildrenKey(DealCtx dealCtx) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealStyleProcessor.generateChildrenKey(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        try {
            List<AttributeDTO> attrs = dealCtx.getAttrs();
            if(attrs == null) {
                return KeyEnum.DEAFAULT.getChannel();
            }
            for (AttributeDTO attributeDTO : attrs) {
                if (attributeDTO.getName().equals(DealAttrCons.CATEGORY)) {
                    if (attributeDTO.getValue().contains(DealAttrCons.CHILDREN_EDU)
                            || attributeDTO.getValue().contains(DealAttrCons.CHILDREN_PHOTO)
                            || attributeDTO.getValue().contains(DealAttrCons.CHILDREN_PREGNANT_PHOTO)) {
                        return KeyEnum.CHILDREN_PHOTOEDUTG.getChannel();
                    }
                }
            }
        } catch (Exception e) {
            logger.error("MTDetailTemplateFacadeImpl.generateChildrenKey error", e);
            return KeyEnum.DEAFAULT.getChannel();
        }
        return KeyEnum.DEAFAULT.getChannel();
    }

    private MTTemplateKey getDegradeTemplateKey() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealStyleProcessor.getDegradeTemplateKey()");
        MTTemplateKey templateKeyDo = new MTTemplateKey();
        templateKeyDo.setKey(KeyEnum.DEAFAULT.getChannel());
        templateKeyDo.setDzx(false);
        templateKeyDo.setDpOrder(false);
        templateKeyDo.setExtraInfo("newtuandeal");
        templateKeyDo.setDegrade(true);
        return templateKeyDo;
    }

    private MTTemplateKey getDefaultTemplateKey(int dpDealGroupId, boolean isDzx) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealStyleProcessor.getDefaultTemplateKey(int,boolean)");
        MTTemplateKey templateKeyDo = new MTTemplateKey();
        templateKeyDo.setKey(KeyEnum.DEAFAULT.getChannel());
        templateKeyDo.setDzx(isDzx);
        templateKeyDo.setExtraInfo("newtuandeal");
        templateKeyDo.setDpOrder(isDpOrder(dpDealGroupId, isDzx));
        templateKeyDo.setDegrade(true);
        return templateKeyDo;
    }

    private boolean isDpOrder(int dpDealGroupId, boolean isDzx) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealStyleProcessor.isDpOrder(int,boolean)");
        //应对极端服务挂掉情况，全切回美团交易流程
        return (isDzx) && !SwitchUtils.isAllNotDpOrder() && dpDealGroupId > 0;
    }

    public DealStyle getDefaultDealStyle(DealCtx dealCtx) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealStyleProcessor.getDefaultDealStyle(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        List<AttributeDTO> attributes = dealCtx.getAttrs();

        DealStyleRequest request = new DealStyleRequest();
        request.setCategoryValues(AttributeUtils.getAttributeValues("category", attributes));
        request.setSelectValues(AttributeUtils.getAttributeValues("isselecteddeal", attributes));
        request.setVersion(dealCtx.getEnvCtx().getVersion());
        DealStyle result = transfer(DealStyleUtil.getDealStyle(request));

        // 如果是以下的DealStyle，走gcdefault
        List<String> gcdefaultDealStyles = Lion.getList("mapi-tgdetail-web.unsupported.deal.config", String.class,
                GC_DEFAULT_STYLE_LIST);
        if(result != null && gcdefaultDealStyles.contains(result.getModuleKey())) {
            result = new DealStyle("gcdefault");
        }

        return result;
    }

    private DealStyle transfer(DealStyle dealStyle) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealStyleProcessor.transfer(com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DealStyle)");
        if (dealStyle == null) {
            return null;
        }
        return new DealStyle(dealStyle.getModuleKey());
    }

    public DealStyle getDealStyle(DealCtx dealCtx) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealStyleProcessor.getDealStyle(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        DealStyle defaultValue = getDefaultDealStyle(dealCtx);

        String style = null;
        StyleResponse styleResponse = dealCtx.getStyleResponse();

        if (styleResponse == null || org.apache.commons.lang3.StringUtils.isBlank(styleResponse.getStyle())) {
            return defaultValue;
        } else {
            style = styleResponse.getStyle();

            if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(style, DealStyles.TRAVEL.getModuleKey()) &&
                    org.apache.commons.lang3.StringUtils.equalsIgnoreCase(defaultValue.getModuleKey(), DealStyles.TRAVEL_SELECTED.getModuleKey())) {

                return defaultValue;
            }

            if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(style, DealStyles.HOTEL.getModuleKey()) &&
                    org.apache.commons.lang3.StringUtils.equalsIgnoreCase(defaultValue.getModuleKey(), DealStyles.HOTEL_SELECTED.getModuleKey())) {

                return defaultValue;
            }

            // 如果是以下的DealStyle，走gcdefault
            List<String> gcdefaultDealStyles = Lion.getList("mapi-tgdetail-web.unsupported.deal.config", String.class,
                    GC_DEFAULT_STYLE_LIST);
            if(gcdefaultDealStyles.contains(style)) {
                style = new DealStyle("gcdefault").getModuleKey();
            }

            DealStyle dealStyle = new DealStyle(style);

            if (CollectionUtils.isNotEmpty(styleResponse.getStyleAbConfigs())) {
                List<ModuleAbConfigBo> moduleAbConfigBos = new ArrayList<>();

                for (StyleAbConfig config : styleResponse.getStyleAbConfigs()) {

                    ModuleAbConfigBo moduleAbConfigBo = new ModuleAbConfigBo();
                    moduleAbConfigBo.setKey(config.getKey());
                    List<AbConfigBo> abConfigBos  = new ArrayList<>();

                    if (org.apache.commons.lang3.StringUtils.isNotBlank(config.getKey()) && CollectionUtils.isNotEmpty(config.getStyleExps())) {
                        for (StyleExp exp : config.getStyleExps()) {
                            AbConfigBo abConfigBo = new AbConfigBo(exp.getExpId(), exp.getExpResult(), exp.getExpBiInfo());
                            abConfigBos.add(abConfigBo);
                        }
                    }

                    moduleAbConfigBo.setConfigs(abConfigBos);
                    moduleAbConfigBos.add(moduleAbConfigBo);

                }

                dealStyle.setModuleAbConfigs(moduleAbConfigBos);
            }

            return dealStyle;
        }
    }
}
