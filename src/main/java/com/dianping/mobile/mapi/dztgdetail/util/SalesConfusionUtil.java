package com.dianping.mobile.mapi.dztgdetail.util;


import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;

import java.util.List;


public class SalesConfusionUtil {


    /**
     * 根据配置的规则区间话展示销量
     */
    public static String getSectionSales(int sales) {
        if (sales <= 0) {
            return null;
        }

        List<SalesSectionRule> salesSectionRules = Lion.getList(LionConstants.APP_KEY,
                LionConstants.SALE_GENERAL_SECTION_RULE_CONFIG, SalesSectionRule.class);

        int sectionSales = sales;
        for (SalesSectionRule salesSectionRule : salesSectionRules) {
            if (sales >= salesSectionRule.getRange()[0] && sales < salesSectionRule.getRange()[1]) {
                int temp = sales / salesSectionRule.getStep();
                if (temp > 0) {
                    sectionSales = temp * salesSectionRule.getStep();
                }
                if (sectionSales >= 10000) {
                    return sectionSales/10000.0 + "万+";
                }
                return sectionSales + "+";
            }
        }
        return String.valueOf(sales);
    }

    /**
     * 销量区间化规则
     */
    static class SalesSectionRule{
        // 销量范围
        private int[] range;
        // 模糊处理的步长。步长为100，如真实销量234，区间化展示为200+；
        private int step;

        public int[] getRange() {
            return range;
        }

        public void setRange(int[] range) {
            this.range = range;
        }

        public int getStep() {
            return step;
        }

        public void setStep(int step) {
            this.step = step;
        }
    }

}
