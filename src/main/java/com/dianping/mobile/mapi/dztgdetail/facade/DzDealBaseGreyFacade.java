package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseGreyReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DzDealbaseGreyResult;
import com.dianping.mobile.mapi.dztgdetail.entity.DzDealbaseGreyConfig;
import com.dianping.mobile.mapi.dztgdetail.exception.QueryCenterResultException;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupCategoryBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Random;
import java.util.Set;

@Service
@Slf4j
public class DzDealBaseGreyFacade {
    @Autowired
    private DealGroupWrapper dealGroupWrapper;

    @Autowired
    @Qualifier("queryCenterDealGroupQueryService")
    private DealGroupQueryService queryCenterDealGroupQueryService;

    public DzDealbaseGreyResult queryGreyResult(DealBaseGreyReq request, EnvCtx envCtx) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.DzDealBaseGreyFacade.queryGreyResult(DealBaseGreyReq,EnvCtx)");
        DzDealbaseGreyResult result = new DzDealbaseGreyResult();

        DzDealbaseGreyConfig greyConfig =  Lion.getBean("com.sankuai.dzu.tpbase.dztgdetailweb",
                    "com.sankuai.dzu.tpbase.dztgdetailweb.dzdealbase.grey.configs",
                    DzDealbaseGreyConfig.class);

        if(greyConfig != null && greyConfig.isAllPass()) {
            result.setGreyResult(true);
            return result;
        }

        int dealGroupId = request.getDealgroupid();
        // 存在白名单
        if(greyConfig != null && CollectionUtils.isNotEmpty(greyConfig.getDealGroupIdWhiteList())) {
            String prefix = envCtx.isMt() ? "mt" : "dp";
            String id = prefix + dealGroupId;

            if(greyConfig.getDealGroupIdWhiteList().contains(id)) {
                result.setGreyResult(true);
                return result;
            }
        }

        QueryDealGroupListResponse queryDealGroupListResponse = null;
        Set<Long> set = new HashSet<>();
        set.add(Long.valueOf(request.getDealgroupid()));
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(set, envCtx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP)
                .category(DealGroupCategoryBuilder.builder().all())
                .build();
        try {
            queryDealGroupListResponse = queryCenterDealGroupQueryService.queryByDealGroupIds(queryByDealGroupIdRequest);
        } catch (TException e) {
            throw new QueryCenterResultException("queryCenter throws exception", e);
        }

        if(queryDealGroupListResponse == null) {
            throw new QueryCenterResultException("queryCenter returns null, request : " + GsonUtils.toJsonString(queryByDealGroupIdRequest));
        }

        if(queryDealGroupListResponse.getCode() != 0) {
            throw new QueryCenterResultException("queryCenter not success, request : " + GsonUtils.toJsonString(queryByDealGroupIdRequest));
        }

        if(CollectionUtils.isEmpty(queryDealGroupListResponse.getData().getList())) {
            throw new QueryCenterResultException("queryDealGroupListResponse.getData().getList() is empty, request : " + GsonUtils.toJsonString(queryByDealGroupIdRequest));
        }

        if(queryDealGroupListResponse.getData().getList().get(0) == null) {
            throw new QueryCenterResultException("queryDealGroupListResponse.getData().getList().get(0) is null, request : " + GsonUtils.toJsonString(queryByDealGroupIdRequest));
        }

        DealGroupDTO dealGroupDTO = queryDealGroupListResponse.getData().getList().get(0);

        int publishCategoryId = Math.toIntExact(dealGroupDTO.getCategory() == null ? 0L : dealGroupDTO.getCategory().getCategoryId());
        if(greyConfig != null && greyConfig.getCategoryId2GreyRatioMap() != null) {
            if(greyConfig.getCategoryId2GreyRatioMap().containsKey(publishCategoryId)) {
                int greyRatio = greyConfig.getCategoryId2GreyRatioMap().get(publishCategoryId);
                if(isGreyTrue(dealGroupId, greyRatio)) {
                    result.setGreyResult(true);
                    return result;
                }
            }
        }

        return result;
    }

    /**
     * 返回true则导流到新接口
     * @param greyRatio
     * @return
     */
    private boolean isGreyTrue(int dealGroupId, int greyRatio) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.facade.DzDealBaseGreyFacade.isGreyTrue(int,int)");
        int random = dealGroupId % 100;
        return random < greyRatio;
    }

}