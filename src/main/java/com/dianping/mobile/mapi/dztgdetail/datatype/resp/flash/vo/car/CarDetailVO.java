package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.car;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/6 5:48 下午
 */
@MobileDo(id = 0x1ad9)
public class CarDetailVO implements Serializable {
    /**
     * 爱车团详组件列表
     */
    @MobileDo.MobileField(key = 0x7934)
    private List<CarDetailModuleVO> moduleList;

    public List<CarDetailModuleVO> getModuleList() {
        return moduleList;
    }

    public void setModuleList(List<CarDetailModuleVO> moduleList) {
        this.moduleList = moduleList;
    }
}
