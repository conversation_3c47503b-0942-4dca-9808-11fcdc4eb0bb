package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.general.product.query.center.client.builder.model.DealTimeStockBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.Cons.*;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.*;

/**
 * <AUTHOR>
 * @date 2023/4/5
 */
public class TimeStockPlanProcessor extends AbsDealProcessor {

    @Autowired
    private QueryCenterWrapper queryCenterWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        List<Integer> list = Lion.getList(APP_KEY, TIME_STOCK_PLAN_QUERY_CATEGORIES, Integer.class, new ArrayList<>());
        return CollectionUtils.isNotEmpty(list) && list.contains(ctx.getCategoryId()) && DealAttrHelper.isWarmUpDeal(ctx.getAttrs());
    }

    @Override
    public void prepare(DealCtx ctx) {
        if(ctx.getDealExtraTypes() == null){
            ctx.setDealExtraTypes(Lists.newArrayList());
        }
        ctx.getDealExtraTypes().add(WARM_UP_DEAL);

        int dealGroupId = ctx.isMt() ? ctx.getMtId() : ctx.getDpId();
        QueryByDealGroupIdRequest request = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet((long)dealGroupId), ctx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP)
                .dealTimeStock(DealTimeStockBuilder.builder().all())
                .build();
        Future future = queryCenterWrapper.preDealGroupDTO(request);
        ctx.getFutureCtx().setTimeStockFuture(future);
    }

    @Override
    public void process(DealCtx ctx) {
        DealGroupDTO dealGroupDTO = null;
        try {
            dealGroupDTO = queryCenterWrapper.getDealGroupDTO(ctx.getFutureCtx().getTimeStockFuture());
        } catch (Exception e) {
            logger.error("queryCenterWrapper.getDealGroupDTO error,", e);
            ctx.setQueryCenterHasError(true);
        }
        if(dealGroupDTO != null && CollectionUtils.isNotEmpty(dealGroupDTO.getDeals())){
            ctx.setDealTimeStockDTO(dealGroupDTO.getDeals().get(0).getDealTimeStockDTO());
        }
    }

}
