package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo.CouponDescItem;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2023/7/26
 */
@Data
@TypeDoc(description = "多买多省搭售卡片信息")
@MobileDo(id = 0xedf0)
public class BuyMoreSaveMoreCardVO implements Serializable {

    @FieldDoc(description = "组合Id")
    @MobileDo.MobileField(key = 0xa66)
    private String combinationId;

    @FieldDoc(description = "搭售品类型：0-餐团、1-综团、2-标品")
    @MobileDo.MobileField(key = 0x7a22)
    private int bindingProductType;

    @FieldDoc(description = "主品类型：0-餐团、1-综团、2-标品")
    @MobileDo.MobileField(key = 0xc159)
    private int mainProductType;

    @FieldDoc(description = "搭售商品来源：1-算法组品，2-运营组品")
    @MobileDo.MobileField(key = 0x6764)
    private int bindingSource;

    @Deprecated
    @FieldDoc(description = "组合卡片id")
    @MobileDo.MobileField(key = 0xae37)
    private int cardId;

    @FieldDoc(description = "主品团单id")
    @MobileDo.MobileField(key = 0x9c82)
    private int mainDealId;

    @FieldDoc(description = "搭售品团单id")
    @MobileDo.MobileField(key = 0x6fb9)
    private int bindingDealId;

    @FieldDoc(description = "组合卡片标题")
    @MobileDo.MobileField(key = 0xd1ed)
    private String cardTitle;

    @FieldDoc(description = "主商品头图")
    @MobileDo.MobileField(key = 0xee99)
    private String mainDealHeaderImage;

    @FieldDoc(description = "搭售商品头图")
    @MobileDo.MobileField(key = 0x31d3)
    private String bindingDealHeaderImage;

    @FieldDoc(description = "搭售品主标题")
    @MobileDo.MobileField(key = 0xef1b)
    private String bindingDealTitle;

    @FieldDoc(description = "搭售品副标题")
    @MobileDo.MobileField(key = 0x9ec4)
    private String bindingDealSubTitle;

    @FieldDoc(description = "主品到手价")
    @MobileDo.MobileField(key = 0x851a)
    private String mainDealPrice;

    @FieldDoc(description = "当前商品tag")
    @MobileDo.MobileField(key = 0x6484)
    private String mainDealTag;

    @FieldDoc(description = "搭售品到手价")
    @MobileDo.MobileField(key = 0x601f)
    private String bindingDealPrice;

    @FieldDoc(description = "组合价格")
    @MobileDo.MobileField(key = 0x4089)
    private String cardPrice;

    @FieldDoc(description = "组合价格文案")
    @MobileDo.MobileField(key = 0x6ea6)
    private String cardPriceText;

    @FieldDoc(description = "销量")
    @MobileDo.MobileField(key = 0x4f8e)
    private String sales;

    @FieldDoc(description = "搭售品跳链")
    @MobileDo.MobileField(key = 0x6b23)
//    @EncryptedLinkField(queries = {"poiid"})
    private String bindingDealJumpUrl;

    @FieldDoc(description = "优惠力度")
    @MobileDo.MobileField(key = 0xbfe)
    private String discountPriceDesc;

    @FieldDoc(description = "下单按钮文案")
    @MobileDo.MobileField(key = 0xdc07)
    private String buyButtonText;

    @FieldDoc(description = "跳转提单页链接")
    @MobileDo.MobileField(key = 0x4d40)
//    @EncryptedLinkField(queries = {"shopid"})
    private String buyButtonJumpUrl;

    @FieldDoc(description = "待领券信息")
    @MobileDo.MobileField(key = 0x1ddb)
    private List<CouponDescItem> couponDescItems;
}
