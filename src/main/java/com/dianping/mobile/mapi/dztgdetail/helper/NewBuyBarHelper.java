package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.button.*;
import com.dianping.mobile.mapi.dztgdetail.button.beauty.BeautyNormalButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.coupon.BeautyBianMeiCouponBannerBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.coupon.CouponBannerBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.edu.EduFreeTrialButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.joy.*;
import com.dianping.mobile.mapi.dztgdetail.button.leadsdeal.LeadsDealBannerBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.leadsdeal.LeadsDealButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.memberfree.MemberFreeBannerBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.mtlive.MtLiveCanNotBuyButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.mtlive.MtLiveMiniAppButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.normal.ConsumeVoucherButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.normal.MemberExclusiveBannerBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.normal.MemberExclusiveButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.normal.OdpSourceButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.pintuan.CostEffectivePinTuanBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.shoppingcart.ShoppingButtonLocationSorter;
import com.dianping.mobile.mapi.dztgdetail.button.shoppingcart.ShoppingCartAssembleDealButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.shoppingcart.ShoppingCartButtonFilter;
import com.dianping.mobile.mapi.dztgdetail.button.shoppingcart.ShoppingCartIdlePromoButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.shoppingcart.ShoppingCartMemberCardButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.shoppingcart.ShoppingCartMemberPriceBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.shoppingcart.ShoppingCartNormalButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.shoppingcart.ShoppingCartTimesCardButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.weddingleadsdeal.WeddingLeadsDealButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.common.constants.CatEvents;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBarCallSceneEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.StyleTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.EduDealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LogUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;


public class NewBuyBarHelper {

    private static final ButtonBuilderChain DEFAULT_BUILDER_CHAIN = ButtonBuilderFactory
            .newButtonBuilderChain(buildDefaultChainConfig());
    private static final ButtonBuilderChain BEAUTY_BUILDER_CHAIN = ButtonBuilderFactory
            .newButtonBuilderChain(buildBeautyChainConfig());
    private static final ButtonBuilderChain JOY_BUILDER_CHAIN = ButtonBuilderFactory
            .newButtonBuilderChain(buildJoyChainConfig());
    private static final ButtonBuilderChain XiYu_BUILDER_CHAIN = ButtonBuilderFactory
            .newButtonBuilderChain(buildXiYuChainConfig());
    private static final ButtonBuilderChain JoyMarket_BUILDER_CHAIN = ButtonBuilderFactory
            .newButtonBuilderChain(buildJoyMarketChainConfig());
    private static final ButtonBuilderChain ODP_BUILDER_CHAIN = ButtonBuilderFactory
            .newButtonBuilderChain(buildOdpChainConfig());
    private static final ButtonBuilderChain EDU_SPECIAL_BUILDER_CHAIN = ButtonBuilderFactory
            .newButtonBuilderChain(buildEduChainConfig());
    private static final ButtonBuilderChain FITNESS_CROSS_BUILDER_CHAIN = ButtonBuilderFactory
            .newButtonBuilderChain(buildFitnessCrossChainConfig());
    /**
     * 美团美播小程序购买按钮构造器
     */
    private static final ButtonBuilderChain MT_LIVE_MINIAPP_BUILDER_CHAIN =
            ButtonBuilderFactory.newButtonBuilderChain(buildMtLiveMiniAppChainConfig());
    private static final ButtonBuilderChain ZU_LIAO_BUILDER_CHAIN = ButtonBuilderFactory
            .newButtonBuilderChain(buildZuLiaoChainConfig());
    private static final ButtonBuilderChain SHOPPING_CART_BUILDER_CHAIN = ButtonBuilderFactory
            .newButtonBuilderChain(buildShoppingCartChainConfig());
    private static final ButtonBuilderChain MEMBER_EXCLUSIVE_CHAIN = ButtonBuilderFactory
            .newButtonBuilderChain(buildMemberExclusiveChainConfig());

    private static final ButtonBuilderChain ZERO_VACCINE_BUILDER_CHAIN = ButtonBuilderFactory
            .newButtonBuilderChain(buildZeroVaccineChainConfig());
    private static final ButtonBuilderChain FREE_DEAL_BUILDER_CHAIN = ButtonBuilderFactory
            .newButtonBuilderChain(buildFreeDealChainConfig());
    private static final ButtonBuilderChain PRE_PAY_DEAL_BUILDER_CHAIN = ButtonBuilderFactory
            .newButtonBuilderChain(buildPrePayDealChainConfig());
    private static final ButtonBuilderChain LEADS_DEAL_BUILDER_CHAIN = ButtonBuilderFactory
            .newButtonBuilderChain(buildLeadsDealChainConfig());
    private static final ButtonBuilderChain WEDDING_LEADS_DEAL_BUILDER_CHAIN = ButtonBuilderFactory
            .newButtonBuilderChain(buildWeddingLeadsDealChainConfig());

    public static DealBuyBar build(DealCtx dealCtx) {
        BuyBarCallSceneEnum sceneEnum = BuyBarCallSceneEnum.DEFAULT;
        // 预览团单直接返回
        if (DealUtils.isPreviewDeal(dealCtx)) {
            return dealCtx.getBuyBar();
        } else if (DealBuyHelper.isWeddingLeadsDeal(dealCtx)) {
            WEDDING_LEADS_DEAL_BUILDER_CHAIN.build(dealCtx);
            sceneEnum = BuyBarCallSceneEnum.WEDDING_LEADS_DEAL;
        } else if (DealBuyHelper.isMtLiveMiniApp(dealCtx)) {
            MT_LIVE_MINIAPP_BUILDER_CHAIN.build(dealCtx);
            sceneEnum = BuyBarCallSceneEnum.MT_LIVE_MINIAPP;
        } else if (DealBuyHelper.isPrePayDeal(dealCtx)) {
            PRE_PAY_DEAL_BUILDER_CHAIN.build(dealCtx);
            sceneEnum = BuyBarCallSceneEnum.PREPAY_DEAL;
        } else if (DealBuyHelper.isLeadsDeal(dealCtx)) {
            LEADS_DEAL_BUILDER_CHAIN.build(dealCtx);
            sceneEnum = BuyBarCallSceneEnum.LEADS_DEAL;
        }
        // 健身通场景，跟其他所有场景的按钮互斥，并且不会影响其他场景的按钮
        else if (DealBuyHelper.isFitnessCrossDeal(dealCtx)) {
            FITNESS_CROSS_BUILDER_CHAIN.build(dealCtx);
            sceneEnum = BuyBarCallSceneEnum.FITNESS_CROSS;
        }
        //0元预约场景
        else if (DealBuyHelper.isFreeDeal(dealCtx)) {
            //疫苗--0元预约
            if (dealCtx.getCategoryId() == 1611) {
                ZERO_VACCINE_BUILDER_CHAIN.build(dealCtx);
            } else {
                FREE_DEAL_BUILDER_CHAIN.build(dealCtx);
            }
            sceneEnum = BuyBarCallSceneEnum.FREE_DEAL;
        } else if (DealBuyHelper.isOdpRequestSource(dealCtx)) {
            // 分销的优先级较高
            ODP_BUILDER_CHAIN.build(dealCtx);
            sceneEnum = BuyBarCallSceneEnum.ODP;
        } else if(DealBuyHelper.isMemberExclusive(dealCtx)){
            MEMBER_EXCLUSIVE_CHAIN.build(dealCtx);
            sceneEnum = BuyBarCallSceneEnum.MEMBER_EXCLUSIVE;
        } else if(DealBuyHelper.isShoppingCart(dealCtx)){
            dealCtx.getBuyBar().setStyleType(StyleTypeEnum.SHOPPING_CART.code);
            SHOPPING_CART_BUILDER_CHAIN.build(dealCtx);
            sceneEnum = BuyBarCallSceneEnum.SHOPPING_CART;
        } else if (EduDealUtils.hasFreeTrialButton(dealCtx)) {
            EDU_SPECIAL_BUILDER_CHAIN.build(dealCtx);
            dealCtx.getBuyBar().setStyleType(StyleTypeEnum.NORMAL.code);
            modifyEduButtonsForSpecialDeal(dealCtx);
            sceneEnum = BuyBarCallSceneEnum.EDU_SPECIAL;
        } else if (DealBuyHelper.xiYuShowMarketPrice(dealCtx)) {
            XiYu_BUILDER_CHAIN.build(dealCtx);
            sceneEnum = BuyBarCallSceneEnum.XIYU_BUILDER;
        } else if (DealBuyHelper.joyShowMarketPrice(dealCtx)) {
            JoyMarket_BUILDER_CHAIN.build(dealCtx);
            sceneEnum = BuyBarCallSceneEnum.JOY_MARKET;
        } else if (DealBuyHelper.isShowZuLiaoMarketPrice(dealCtx)) {
            //足疗ZU_LIAO_BUILDER_CHAIN的优先级比JOY_BUILDER_CHAIN优先级高
            ZU_LIAO_BUILDER_CHAIN.build(dealCtx);
            sceneEnum = BuyBarCallSceneEnum.ZU_LIAO;
        } else if (DealBuyHelper.isJoy(dealCtx)) {
            JOY_BUILDER_CHAIN.build(dealCtx);
            sceneEnum = BuyBarCallSceneEnum.JOY;
        } else if (DealBuyHelper.isDetailCouponFusion(dealCtx)) {
            BEAUTY_BUILDER_CHAIN.build(dealCtx);
            sceneEnum = BuyBarCallSceneEnum.BEAUTY;
        } else {
            DEFAULT_BUILDER_CHAIN.build(dealCtx);
            sceneEnum = BuyBarCallSceneEnum.DEFAULT;
        }
        // 底bar调用场景打点
        logMetricForBuyBar(sceneEnum, dealCtx);
        return dealCtx.getBuyBar();
    }

    public static void logMetricForBuyBar(BuyBarCallSceneEnum sceneEnum, DealCtx ctx) {
        Map<String, String> tags = Maps.newHashMap();
        tags.put("sceneName", sceneEnum.getName());
        buildLogMetricForBuyBarTag(ctx, tags);
        Cat.logMetricForCount(CatEvents.BUY_BAR_CALL_SCENE, tags);
        LogUtils.info("dealDetailBuyBar sceneId:{}, sceneEnum:{}, dealId:{}, categoryId:{}",
                sceneEnum.getId(), sceneEnum.getName(), ctx.isMt() ? ctx.getMtId() : ctx.getDpId(), ctx.getCategoryId());
    }

    public static void buildLogMetricForBuyBarTag(DealCtx ctx, Map<String, String> tags) {
        List<String> categoryStrList = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb.bottom.bar.log.category.config", String.class);
        if (CollectionUtils.isEmpty(categoryStrList)) {
            return;
        }
        String categoryStr = Optional.ofNullable(ctx.getDealGroupDTO()).map(DealGroupDTO::getCategory).map(category -> String.format("%s:%s", category.getCategoryId(), category.getServiceTypeId())).orElse(null);
        if (categoryStrList.contains(categoryStr)) tags.put("category", categoryStr);
    }

    public static void modifyEduButtonsForSpecialDeal(DealCtx dealCtx) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.helper.NewBuyBarHelper.modifyEduButtonsForSpecialDeal(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        if (dealCtx == null) {
            return;
        }

        if(EduDealUtils.isShortClass(dealCtx) && dealCtx.getPriceContext() != null && dealCtx.getPriceContext().getDealPromoPrice() != null) {
            BigDecimal marketPrice = dealCtx.getPriceContext().getDealPromoPrice().getMarketPrice();
            BigDecimal finalPrice = dealCtx.getPriceContext().getDealPromoPrice().getMarketPrice();
            if (marketPrice != null && finalPrice != null && marketPrice.compareTo(finalPrice) == 0) {
                if (dealCtx.getBuyBar() != null && CollectionUtils.isNotEmpty(dealCtx.getBuyBar().getBuyBtns())) {
                    DealBuyBtn dealBuyBtn = dealCtx.getBuyBar().getBuyBtns().get(dealCtx.getBuyBar().getBuyBtns().size() - 1);
                    dealBuyBtn.setBtnIcons(null);
                    dealBuyBtn.setBtnTag(null);
                    dealBuyBtn.setBtnDesc(null);
                }
            }
        }
        DealGroupDTO dealGroupDTO = dealCtx.getDealGroupDTO();
        // 0元规划隐藏价格
        if (EduDealUtils.isVocationalEduPlan(dealGroupDTO)) {
            setEduOnlineDealBuyButton(dealCtx, dealCtx.getBuyBar());
        }

        if (dealCtx.getBuyBar() != null &&
                CollectionUtils.isNotEmpty(dealCtx.getBuyBar().getBuyBtns()) &&
                dealCtx.getBuyBar().getBuyBtns().size() > 1) {
            dealCtx.getBuyBar().setStyleType(StyleTypeEnum.CAPSULE.code);
        }
    }

    static void setEduOnlineDealBuyButton(DealCtx dealCtx, DealBuyBar buyBar) {
        if (dealCtx == null || buyBar == null || CollectionUtils.isEmpty(buyBar.getBuyBtns())) {
            return;
        }
        deletePriceInfo(dealCtx, buyBar);
        setBlockMsg(dealCtx, buyBar);
    }

    private static void deletePriceInfo(DealCtx dealCtx, DealBuyBar buyBar) {
        if (isFromWX(dealCtx)) {
            return;
        }
        for (DealBuyBtn buyBtn : buyBar.getBuyBtns()) {
            buyBtn.setPriceStr(null);
            buyBtn.setPricePostfix(null);
        }
    }

    private static void setBlockMsg(DealCtx dealCtx, DealBuyBar buyBar) {
        if (!isFromWX(dealCtx)) {
            return;
        }
        // 小程序拦截下单
        for (DealBuyBtn buyBtn : buyBar.getBuyBtns()) {
            buyBtn.setBlockMsg("为保证您的用户体验，请至“美团/点评”APP购买");
        }
    }

    private static boolean isFromWX(DealCtx dealCtx) {
        if (dealCtx.getEnvCtx() == null) {
            return false;
        }
        DztgClientTypeEnum clientType = dealCtx.getEnvCtx().getDztgClientTypeEnum();
        return DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP.equals(clientType) || DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP.equals(clientType);
    }

    private static BuilderConfig buildButtonConfig(String builderName, List<BuyBtnTypeEnum> inclusiveTypes) {
        BuilderConfig config = new BuilderConfig();
        config.setBuilderName(builderName);
        if (inclusiveTypes == null) {
            config.setExclusiveAll(true);
        } else if (inclusiveTypes.isEmpty()) {
            config.setInclusiveAll(true);
        } else {
            List<ButtonStateConfig> stateConfigs = Lists.newArrayList();
            for (BuyBtnTypeEnum inclusiveType : inclusiveTypes) {
                ButtonStateConfig buttonStateConfig = new ButtonStateConfig();
                buttonStateConfig.setButtonType(inclusiveType);
                stateConfigs.add(buttonStateConfig);
            }
            config.setInclusiveType(stateConfigs);
        }

        return config;
    }

    private static BuilderChainConfig buildDefaultChainConfig() {
        BuilderChainConfig config = new BuilderChainConfig();
        config.setMaxButtonSize(2);
        config.setBuilderConfigs(Lists.newArrayList());

        // 预热banner
        config.getBuilderConfigs().add(buildButtonConfig(WarmUpBannerBuilder.class.getName(), Lists.newArrayList()));

        // 不可购买
        config.getBuilderConfigs().add(buildButtonConfig(CanNotBuyButtonBuilder.class.getName(), null));

        // 特团拼团 该场景无排序builder，且限定size，所以前置
        config.getBuilderConfigs().add(buildButtonConfig(CostEffectivePinTuanBuilder.class.getName(), Lists.newArrayList()));

        // 商户有卡，用户没卡
        BuilderConfig a = buildButtonConfig(JoyDiscountCardButtonBuilder.class.getName(), null);
        a.setSkipOnStatues(Lists.newArrayList(ButtonStateEnum.HOLD));
        config.getBuilderConfigs().add(a);

        // 拼团
        config.getBuilderConfigs().add(buildButtonConfig(AssembleDealButtonBuilder.class.getName(), null));
        // 次卡
        config.getBuilderConfigs().add(buildButtonConfig(TimesCardButtonBuilder.class.getName(), null));

        // 用户已经持有会员卡
        BuilderConfig c = buildButtonConfig(JoyDiscountCardButtonBuilder.class.getName(),
                Lists.newArrayList(BuyBtnTypeEnum.PINTUAN, BuyBtnTypeEnum.TIMES_CARD));
        c.setBuildOnStatues(Lists.newArrayList(ButtonStateEnum.HOLD));
        config.getBuilderConfigs().add(c);

        // 闲时优惠
        BuilderConfig f = buildButtonConfig(NewIdlePromoButtonBuilder.class.getName(), null);
        config.getBuilderConfigs().add(f);

        // 团购价, 用户不是会员
        BuilderConfig d = buildButtonConfig(BeautyNormalButtonBuilder.class.getName(), Lists.newArrayList());
        ButtonStateConfig ds = new ButtonStateConfig();
        ds.setButtonType(BuyBtnTypeEnum.MEMBER_CARD);
        ds.setButtonStates(Lists.newArrayList(ButtonStateEnum.HOLD));
        d.setExclusiveType(Lists.newArrayList(ds));
        config.getBuilderConfigs().add(d);

        // 闲时优惠banner
        BuilderConfig e = buildButtonConfig(NewIdlePromoBannerBuilder.class.getName(), Lists.newArrayList());
        config.getBuilderConfigs().add(e);

        // 券信息横幅
        config.getBuilderConfigs().add(buildButtonConfig(CouponBannerBuilder.class.getName(), Lists.newArrayList()));
        config.getBuilderConfigs().add(buildButtonConfig(MemberFreeBannerBuilder.class.getName(), Lists.newArrayList()));
        // 右边按钮样式调整
        BuilderConfig h = buildButtonConfig(RightButtonStyleBuilder.class.getName(), Lists.newArrayList());
        config.getBuilderConfigs().add(h);

        addConfig2Chain(ConsumeVoucherButtonBuilder.class, config);

        // 购物车按钮浮层，会员价信息展示
        addConfig2Chain(ShoppingCartMemberPriceBuilder.class, config);
        // 提单浮层
        addConfig2Chain(PopOverlayButtonBuilder.class, config);
        return config;
    }

    public static BuilderChainConfig buildBeautyChainConfig() {
        BuilderChainConfig config = new BuilderChainConfig();
        config.setMaxButtonSize(2);
        config.setBuilderConfigs(Lists.newArrayList());

        // 不可购买
        config.getBuilderConfigs().add(buildButtonConfig(CanNotBuyButtonBuilder.class.getName(), null));
        // 特团拼团
        config.getBuilderConfigs().add(buildButtonConfig(CostEffectivePinTuanBuilder.class.getName(), null));

//        // 商户有卡，用户没卡
//        BuilderConfig a = buildButtonConfig(BeautyMemberCardButtonBuilder.class.getName(), null);
//        a.setSkipOnStatues(Lists.newArrayList(ButtonStateEnum.HOLD));
//        config.getBuilderConfigs().add(a);

        // 拼团
        config.getBuilderConfigs().add(buildButtonConfig(AssembleDealButtonBuilder.class.getName(), null));
        // 次卡
        config.getBuilderConfigs().add(buildButtonConfig(TimesCardButtonBuilder.class.getName(), null));

        // 闲时优惠
        BuilderConfig f = buildButtonConfig(NewIdlePromoButtonBuilder.class.getName(), null);
        config.getBuilderConfigs().add(f);

        // 团购价, 用户不是会员
        BuilderConfig d = buildButtonConfig(BeautyNormalButtonBuilder.class.getName(), Lists.newArrayList());
        ButtonStateConfig ds = new ButtonStateConfig();
        ds.setButtonType(BuyBtnTypeEnum.MEMBER_CARD);
        ds.setButtonStates(Lists.newArrayList(ButtonStateEnum.HOLD));
        d.setExclusiveType(Lists.newArrayList(ds));
        config.getBuilderConfigs().add(d);

        // 闲时优惠banner
        BuilderConfig e = buildButtonConfig(NewIdlePromoBannerBuilder.class.getName(), Lists.newArrayList());
        config.getBuilderConfigs().add(e);

        // 券信息横幅
        config.getBuilderConfigs().add(buildButtonConfig(CouponBannerBuilder.class.getName(), Lists.newArrayList()));
        config.getBuilderConfigs().add(buildButtonConfig(MemberFreeBannerBuilder.class.getName(), Lists.newArrayList()));
        // 右边按钮样式调整
        BuilderConfig h = buildButtonConfig(RightButtonStyleBuilder.class.getName(), Lists.newArrayList());
        config.getBuilderConfigs().add(h);

        addConfig2Chain(ConsumeVoucherButtonBuilder.class, config);
        // 购物车按钮浮层，会员价信息展示
        addConfig2Chain(ShoppingCartMemberPriceBuilder.class, config);

        return config;
    }

    public static BuilderChainConfig buildJoyChainConfig() {
        BuilderChainConfig config = new BuilderChainConfig();
        config.setMaxButtonSize(2);
        config.setBuilderConfigs(Lists.newArrayList());

        // 不可购买
        config.getBuilderConfigs().add(buildButtonConfig(CanNotBuyButtonBuilder.class.getName(), null));
        // 特团拼团
        config.getBuilderConfigs().add(buildButtonConfig(CostEffectivePinTuanBuilder.class.getName(), null));

        // 用户持有会员卡
        BuilderConfig c = buildButtonConfig(JoyDiscountCardButtonBuilder.class.getName(),
                Lists.newArrayList(BuyBtnTypeEnum.JOY_CARD));
        c.setBuildOnStatues(Lists.newArrayList(ButtonStateEnum.HOLD));
        config.getBuilderConfigs().add(c);

        // 商户有会员卡，用户没有
        BuilderConfig h = buildButtonConfig(JoyDiscountCardButtonBuilder.class.getName(), null);
        h.setSkipOnStatues(Lists.newArrayList(ButtonStateEnum.HOLD));
        h.setBuildOnNewUser(false);
        config.getBuilderConfigs().add(h);
        // 商户有两种卡，用户都没持有，新客玩乐卡，老客折扣卡    结束

        // 商户有会员卡，用户没有
        BuilderConfig l = buildButtonConfig(JoyDiscountCardButtonBuilder.class.getName(), null);
        l.setSkipOnStatues(Lists.newArrayList(ButtonStateEnum.HOLD));
        config.getBuilderConfigs().add(l);
        // 商户只有一张卡  结束

        // 次卡
        config.getBuilderConfigs().add(buildButtonConfig(TimesCardButtonBuilder.class.getName(), null));

        // 拼团
        config.getBuilderConfigs().add(buildButtonConfig(AssembleDealButtonBuilder.class.getName(), null));

        // 闲时优惠
        BuilderConfig f = buildButtonConfig(NewIdlePromoButtonBuilder.class.getName(), null);
        config.getBuilderConfigs().add(f);

        // 团购价, 用户不是会员
        BuilderConfig d = buildButtonConfig(NewNormalButtonBuilder.class.getName(), Lists.newArrayList());
        ButtonStateConfig ds = new ButtonStateConfig();
        ds.setButtonType(BuyBtnTypeEnum.MEMBER_CARD);
        ds.setButtonStates(Lists.newArrayList(ButtonStateEnum.HOLD));
        ButtonStateConfig ds1 = new ButtonStateConfig();
        ds1.setButtonType(BuyBtnTypeEnum.JOY_CARD);
        ds1.setButtonStates(Lists.newArrayList(ButtonStateEnum.HOLD));
        d.setExclusiveType(Lists.newArrayList(ds, ds1));
        config.getBuilderConfigs().add(d);

        // 闲时优惠banner
        BuilderConfig e = buildButtonConfig(NewIdlePromoBannerBuilder.class.getName(), Lists.newArrayList());
        config.getBuilderConfigs().add(e);

        // 券信息横幅
        config.getBuilderConfigs().add(buildButtonConfig(CouponBannerBuilder.class.getName(), Lists.newArrayList()));
        config.getBuilderConfigs().add(buildButtonConfig(MemberFreeBannerBuilder.class.getName(), Lists.newArrayList()));
        // 玩乐卡和会员卡排序
        BuilderConfig j = buildButtonConfig(ButtonLocationSorter.class.getName(), Lists.newArrayList());
        config.getBuilderConfigs().add(j);

        // 玩乐卡和会员卡排序
        BuilderConfig m = buildButtonConfig(NewCarIconAndTitleAdaptBuilder.class.getName(), Lists.newArrayList());
        config.getBuilderConfigs().add(m);

        // 右边按钮样式调整
        BuilderConfig n = buildButtonConfig(RightButtonStyleBuilder.class.getName(), Lists.newArrayList());
        config.getBuilderConfigs().add(n);

        addConfig2Chain(ConsumeVoucherButtonBuilder.class, config);
        // 购物车按钮浮层，会员价信息展示
        addConfig2Chain(ShoppingCartMemberPriceBuilder.class, config);
        // 提单浮层
        addConfig2Chain(PopOverlayButtonBuilder.class, config);
        return config;
    }

    public static BuilderChainConfig buildXiYuChainConfig() {
        BuilderChainConfig config = new BuilderChainConfig();
        config.setMaxButtonSize(2);
        config.setBuilderConfigs(Lists.newArrayList());

        // 预热banner
        config.getBuilderConfigs().add(buildButtonConfig(WarmUpBannerBuilder.class.getName(), Lists.newArrayList()));

        // 不可购买
        config.getBuilderConfigs().add(buildButtonConfig(CanNotBuyButtonBuilder.class.getName(), null));
        // 特团拼团
        config.getBuilderConfigs().add(buildButtonConfig(CostEffectivePinTuanBuilder.class.getName(), null));

        // 用户持有会员卡
        BuilderConfig c = buildButtonConfig(JoyDiscountCardButtonBuilder.class.getName(),
                Lists.newArrayList(BuyBtnTypeEnum.JOY_CARD));
        c.setBuildOnStatues(Lists.newArrayList(ButtonStateEnum.HOLD));
        config.getBuilderConfigs().add(c);

        // 商户有会员卡，用户没有
        BuilderConfig h = buildButtonConfig(JoyDiscountCardButtonBuilder.class.getName(), null);
        h.setSkipOnStatues(Lists.newArrayList(ButtonStateEnum.HOLD));
        h.setBuildOnNewUser(false);
        config.getBuilderConfigs().add(h);
        // 商户有两种卡，用户都没持有，新客玩乐卡，老客折扣卡    结束

        // 商户有会员卡，用户没有
        BuilderConfig l = buildButtonConfig(JoyDiscountCardButtonBuilder.class.getName(), null);
        l.setSkipOnStatues(Lists.newArrayList(ButtonStateEnum.HOLD));
        config.getBuilderConfigs().add(l);
        // 商户只有一张卡  结束

        // 次卡
        config.getBuilderConfigs().add(buildButtonConfig(JoyTimesCardButtonBuilder.class.getName(), null));

        // 拼团
        config.getBuilderConfigs().add(buildButtonConfig(JoyAssembleDealButtonBuilder.class.getName(), null));

        // 闲时优惠
        BuilderConfig f = buildButtonConfig(NewIdlePromoButtonBuilder.class.getName(), null);
        config.getBuilderConfigs().add(f);

        // 团购价, 用户不是会员
        BuilderConfig d = buildButtonConfig(MarketPriceNormalButtonBuilder.class.getName(), Lists.newArrayList());
        ButtonStateConfig ds = new ButtonStateConfig();
        ds.setButtonType(BuyBtnTypeEnum.MEMBER_CARD);
        ds.setButtonStates(Lists.newArrayList(ButtonStateEnum.HOLD));
        ButtonStateConfig ds1 = new ButtonStateConfig();
        ds1.setButtonType(BuyBtnTypeEnum.JOY_CARD);
        ds1.setButtonStates(Lists.newArrayList(ButtonStateEnum.HOLD));
        d.setExclusiveType(Lists.newArrayList(ds, ds1));
        config.getBuilderConfigs().add(d);

        // 闲时优惠banner
        BuilderConfig e = buildButtonConfig(NewIdlePromoBannerBuilder.class.getName(), Lists.newArrayList());
        config.getBuilderConfigs().add(e);

        //变美神券横幅
        config.getBuilderConfigs().add(buildButtonConfig(BeautyBianMeiCouponBannerBuilder.class.getName(), Lists.newArrayList()));

        // 券信息横幅
        config.getBuilderConfigs().add(buildButtonConfig(CouponBannerBuilder.class.getName(), Lists.newArrayList()));
        config.getBuilderConfigs().add(buildButtonConfig(MemberFreeBannerBuilder.class.getName(), Lists.newArrayList()));
        // 玩乐卡和会员卡排序
        BuilderConfig j = buildButtonConfig(ButtonLocationSorter.class.getName(), Lists.newArrayList());
        config.getBuilderConfigs().add(j);

        // 玩乐卡和会员卡排序
        BuilderConfig m = buildButtonConfig(NewCarIconAndTitleAdaptBuilder.class.getName(), Lists.newArrayList());
        config.getBuilderConfigs().add(m);

        // 右边按钮样式调整
        BuilderConfig n = buildButtonConfig(RightButtonStyleBuilder.class.getName(), Lists.newArrayList());
        config.getBuilderConfigs().add(n);

        addConfig2Chain(ConsumeVoucherButtonBuilder.class, config);

        // 购物车按钮浮层，会员价信息展示
        addConfig2Chain(ShoppingCartMemberPriceBuilder.class, config);
        // 提单浮层
        addConfig2Chain(PopOverlayButtonBuilder.class, config);
        return config;
    }

    public static BuilderChainConfig buildJoyMarketChainConfig() {
        BuilderChainConfig config = new BuilderChainConfig();
        config.setMaxButtonSize(2);
        config.setBuilderConfigs(Lists.newArrayList());

        // 不可购买
        config.getBuilderConfigs().add(buildButtonConfig(CanNotBuyButtonBuilder.class.getName(), null));
        // 特团拼团
        config.getBuilderConfigs().add(buildButtonConfig(CostEffectivePinTuanBuilder.class.getName(), null));

        // 用户持有会员卡
        BuilderConfig c = buildButtonConfig(JoyDiscountCardButtonBuilder.class.getName(),
                Lists.newArrayList(BuyBtnTypeEnum.JOY_CARD));
        c.setBuildOnStatues(Lists.newArrayList(ButtonStateEnum.HOLD));
        config.getBuilderConfigs().add(c);

        // 商户有会员卡，用户没有
        BuilderConfig h = buildButtonConfig(JoyDiscountCardButtonBuilder.class.getName(), null);
        h.setSkipOnStatues(Lists.newArrayList(ButtonStateEnum.HOLD));
        h.setBuildOnNewUser(false);
        config.getBuilderConfigs().add(h);
        // 商户有两种卡，用户都没持有，新客玩乐卡，老客折扣卡    结束

        // 商户有会员卡，用户没有
        BuilderConfig l = buildButtonConfig(JoyDiscountCardButtonBuilder.class.getName(), null);
        l.setSkipOnStatues(Lists.newArrayList(ButtonStateEnum.HOLD));
        config.getBuilderConfigs().add(l);
        // 商户只有一张卡  结束

        // 次卡
        config.getBuilderConfigs().add(buildButtonConfig(JoyTimesCardButtonBuilder.class.getName(), null));

        // 拼团
        config.getBuilderConfigs().add(buildButtonConfig(JoyAssembleDealButtonBuilder.class.getName(), null));

        // 闲时优惠
        BuilderConfig f = buildButtonConfig(NewIdlePromoButtonBuilder.class.getName(), null);
        config.getBuilderConfigs().add(f);

        // 团购价, 用户不是会员
        BuilderConfig d = buildButtonConfig(MarketPriceNormalButtonBuilder.class.getName(), Lists.newArrayList());
        ButtonStateConfig ds = new ButtonStateConfig();
        ds.setButtonType(BuyBtnTypeEnum.MEMBER_CARD);
        ds.setButtonStates(Lists.newArrayList(ButtonStateEnum.HOLD));
        ButtonStateConfig ds1 = new ButtonStateConfig();
        ds1.setButtonType(BuyBtnTypeEnum.JOY_CARD);
        ds1.setButtonStates(Lists.newArrayList(ButtonStateEnum.HOLD));
        d.setExclusiveType(Lists.newArrayList(ds, ds1));
        config.getBuilderConfigs().add(d);

        // 闲时优惠banner
        BuilderConfig e = buildButtonConfig(NewIdlePromoBannerBuilder.class.getName(), Lists.newArrayList());
        config.getBuilderConfigs().add(e);

        // 券信息横幅
        config.getBuilderConfigs().add(buildButtonConfig(CouponBannerBuilder.class.getName(), Lists.newArrayList()));
        config.getBuilderConfigs().add(buildButtonConfig(MemberFreeBannerBuilder.class.getName(), Lists.newArrayList()));
        // 玩乐卡和会员卡排序
        BuilderConfig j = buildButtonConfig(ButtonLocationSorter.class.getName(), Lists.newArrayList());
        config.getBuilderConfigs().add(j);

        // 玩乐卡和会员卡排序
        BuilderConfig m = buildButtonConfig(NewCarIconAndTitleAdaptBuilder.class.getName(), Lists.newArrayList());
        config.getBuilderConfigs().add(m);

        // 右边按钮样式调整
        BuilderConfig n = buildButtonConfig(RightButtonStyleBuilder.class.getName(), Lists.newArrayList());
        config.getBuilderConfigs().add(n);

        addConfig2Chain(ConsumeVoucherButtonBuilder.class, config);
        // 购物车按钮浮层，会员价信息展示
        addConfig2Chain(ShoppingCartMemberPriceBuilder.class, config);

        return config;
    }

    private static BuilderChainConfig buildOdpChainConfig() {
        BuilderChainConfig config = new BuilderChainConfig();
        config.setMaxButtonSize(1);
        config.setBuilderConfigs(Lists.newArrayList());

        config.getBuilderConfigs().add(buildButtonConfig(MemberFreeBannerBuilder.class.getName(), null));
        config.getBuilderConfigs().add(buildButtonConfig(CanNotBuyButtonBuilder.class.getName(), null));
        config.getBuilderConfigs().add(buildButtonConfig(OdpSourceButtonBuilder.class.getName(), null));
        addConfig2Chain(ConsumeVoucherButtonBuilder.class, config);
        // 购物车按钮浮层，会员价信息展示
        addConfig2Chain(ShoppingCartMemberPriceBuilder.class, config);
        return config;
    }


    private static BuilderChainConfig buildMtLiveMiniAppChainConfig() {
        BuilderChainConfig config = new BuilderChainConfig();
        config.setMaxButtonSize(1);
        config.setBuilderConfigs(Lists.newArrayList());
        // 私域直播底部横幅，暂时隐藏
        // config.getBuilderConfigs().add(buildButtonConfig(MtLiveMiniAppBannerBuilder.class.getName(), Lists.newArrayList()));
        // 美团美播小程序购买按钮
        config.getBuilderConfigs().add(buildButtonConfig(MtLiveCanNotBuyButtonBuilder.class.getName(), Lists.newArrayList()));
        config.getBuilderConfigs().add(buildButtonConfig(MtLiveMiniAppButtonBuilder.class.getName(), Lists.newArrayList()));
        return config;
    }


    private static BuilderChainConfig buildMemberExclusiveChainConfig() {
        BuilderChainConfig config = new BuilderChainConfig();
        config.setMaxButtonSize(1);
        config.setBuilderConfigs(Lists.newArrayList());

        config.getBuilderConfigs().add(buildButtonConfig(MemberExclusiveBannerBuilder.class.getName(), null));
        config.getBuilderConfigs().add(buildButtonConfig(MemberFreeBannerBuilder.class.getName(), null));
        config.getBuilderConfigs().add(buildButtonConfig(MemberExclusiveButtonBuilder.class.getName(), null));

        return config;
    }
    private static BuilderChainConfig buildZeroVaccineChainConfig() {
        BuilderChainConfig config = new BuilderChainConfig();
        config.setMaxButtonSize(1);
        config.setBuilderConfigs(Lists.newArrayList());

        config.getBuilderConfigs().add(buildButtonConfig(MemberFreeBannerBuilder.class.getName(), null));
        config.getBuilderConfigs().add(buildButtonConfig(CanNotResvButtonBuilder.class.getName(), null));
        config.getBuilderConfigs().add(buildButtonConfig(ResvButtonBuilder.class.getName(), null));

        return config;
    }

    private static BuilderChainConfig buildFreeDealChainConfig() {
        BuilderChainConfig config = new BuilderChainConfig();
        config.setMaxButtonSize(1);
        config.setBuilderConfigs(Lists.newArrayList());
        config.getBuilderConfigs().add(buildButtonConfig(FreeDealButtonBuilder.class.getName(), null));
        return config;
    }

    private static BuilderChainConfig buildPrePayDealChainConfig() {
        BuilderChainConfig config = new BuilderChainConfig();
        config.setMaxButtonSize(1);
        config.setBuilderConfigs(Lists.newArrayList());
        config.getBuilderConfigs().add(buildButtonConfig(PrePayDealButtonBuilder.class.getName(), null));
        return config;
    }


    public static BuilderChainConfig buildZuLiaoChainConfig() {
        BuilderChainConfig config = new BuilderChainConfig();
        config.setMaxButtonSize(3);
        config.setBuilderConfigs(Lists.newArrayList());

        // 不可购买
        config.getBuilderConfigs().add(buildButtonConfig(CanNotBuyButtonBuilder.class.getName(), null));
        // 特团拼团
        config.getBuilderConfigs().add(buildButtonConfig(CostEffectivePinTuanBuilder.class.getName(), null));

        // 用户持有会员卡
        BuilderConfig c = buildButtonConfig(JoyDiscountCardButtonBuilder.class.getName(),
                Lists.newArrayList(BuyBtnTypeEnum.JOY_CARD));
        c.setBuildOnStatues(Lists.newArrayList(ButtonStateEnum.HOLD));
        config.getBuilderConfigs().add(c);

        // 商户有会员卡，用户没有
        BuilderConfig h = buildButtonConfig(JoyDiscountCardButtonBuilder.class.getName(), null);
        h.setSkipOnStatues(Lists.newArrayList(ButtonStateEnum.HOLD));
        h.setBuildOnNewUser(false);
        config.getBuilderConfigs().add(h);
        // 商户有两种卡，用户都没持有，新客玩乐卡，老客折扣卡    结束

        // 商户有会员卡，用户没有
        BuilderConfig l = buildButtonConfig(JoyDiscountCardButtonBuilder.class.getName(), null);
        l.setSkipOnStatues(Lists.newArrayList(ButtonStateEnum.HOLD));
        config.getBuilderConfigs().add(l);
        // 商户只有一张卡  结束

        // 次卡
        config.getBuilderConfigs().add(buildButtonConfig(TimesCardButtonBuilder.class.getName(), Lists.newArrayList()));

        // 拼团
        config.getBuilderConfigs().add(buildButtonConfig(AssembleDealButtonBuilder.class.getName(), null));

        // 闲时优惠
        BuilderConfig f = buildButtonConfig(NewIdlePromoButtonBuilder.class.getName(), null);
        config.getBuilderConfigs().add(f);

        // 团购价
        BuilderConfig d = buildButtonConfig(NewNormalButtonBuilder.class.getName(), Lists.newArrayList());
        config.getBuilderConfigs().add(d);

        // 闲时优惠banner
        BuilderConfig e = buildButtonConfig(NewIdlePromoBannerBuilder.class.getName(), Lists.newArrayList());
        config.getBuilderConfigs().add(e);

        // 券信息横幅
        config.getBuilderConfigs().add(buildButtonConfig(CouponBannerBuilder.class.getName(), Lists.newArrayList()));
        config.getBuilderConfigs().add(buildButtonConfig(MemberFreeBannerBuilder.class.getName(), Lists.newArrayList()));
        //会员卡、次卡、拼团、闲时优惠、团购PK，进行排序和出按钮逻辑
        BuilderConfig q = buildButtonConfig(ButtonLocationPricePkSorter.class.getName(), Lists.newArrayList());
        config.getBuilderConfigs().add(q);

        addConfig2Chain(ConsumeVoucherButtonBuilder.class, config);
        // 购物车按钮浮层，会员价信息展示
        addConfig2Chain(ShoppingCartMemberPriceBuilder.class, config);
        // 提单浮层
        addConfig2Chain(PopOverlayButtonBuilder.class, config);
        return config;
    }

    private static BuilderChainConfig buildShoppingCartChainConfig() {
        BuilderChainConfig config = new BuilderChainConfig();
        config.setMaxButtonSize(Lion.getInt(LionConstants.APP_KEY, LionConstants.SHOPPING_CART_BUILDER_BUTTON_SIZE, 5));
        config.setBuilderConfigs(Lists.newArrayList());

        // 预热banner
        config.getBuilderConfigs().add(buildButtonConfig(WarmUpBannerBuilder.class.getName(), Lists.newArrayList()));

        // 不可购买
        config.getBuilderConfigs().add(buildButtonConfig(CanNotBuyButtonBuilder.class.getName(), null));

        // 用户持有会员卡
        BuilderConfig a = buildButtonConfig(ShoppingCartMemberCardButtonBuilder.class.getName(),
                Lists.newArrayList());
        config.getBuilderConfigs().add(a);

        // 次卡
        config.getBuilderConfigs().add(buildButtonConfig(ShoppingCartTimesCardButtonBuilder.class.getName(), Lists.newArrayList()));

        // 拼团
        config.getBuilderConfigs().add(buildButtonConfig(ShoppingCartAssembleDealButtonBuilder.class.getName(), Lists.newArrayList()));

        // 闲时优惠
        BuilderConfig d = buildButtonConfig(ShoppingCartIdlePromoButtonBuilder.class.getName(), Lists.newArrayList());
        config.getBuilderConfigs().add(d);

        // 团购价
        BuilderConfig e = buildButtonConfig(ShoppingCartNormalButtonBuilder.class.getName(), Lists.newArrayList());
        config.getBuilderConfigs().add(e);

        // 特团拼团
        config.getBuilderConfigs().add(buildButtonConfig(CostEffectivePinTuanBuilder.class.getName(), Lists.newArrayList()));

        // 底Bar过滤
        BuilderConfig f = buildButtonConfig(ShoppingCartButtonFilter.class.getName(), Lists.newArrayList());
        config.getBuilderConfigs().add(f);

        // 价格规格排序，顺序：单次团购 > 休娱会员卡(若有) > 次卡(若有) > 限时特惠(若有) > 拼团(若有)
        config.getBuilderConfigs().add(buildButtonConfig(ShoppingButtonLocationSorter.class.getName(), Lists.newArrayList()));

        //变美神券横幅
        config.getBuilderConfigs().add(buildButtonConfig(BeautyBianMeiCouponBannerBuilder.class.getName(), Lists.newArrayList()));

        // 券信息横幅
        config.getBuilderConfigs().add(buildButtonConfig(CouponBannerBuilder.class.getName(), Lists.newArrayList()));
        config.getBuilderConfigs().add(buildButtonConfig(MemberFreeBannerBuilder.class.getName(), Lists.newArrayList()));
        //展示样式调整
        addConfig2Chain(ConsumeVoucherButtonBuilder.class, config);
        // 购物车按钮浮层，会员价信息展示
        addConfig2Chain(ShoppingCartMemberPriceBuilder.class, config);
        // 提单浮层
        addConfig2Chain(PopOverlayButtonBuilder.class, config);
        return config;
    }

    public static BuilderChainConfig buildEduChainConfig() {
        BuilderChainConfig config = buildXiYuChainConfig();
        config.getBuilderConfigs().add(0, buildButtonConfig(EduFreeTrialButtonBuilder.class.getName(), Lists.newArrayList()));
        return config;
    }


    private static void addConfig2Chain(Class<? extends ButtonBuilder> clazz, BuilderChainConfig config) {
        BuilderConfig h = buildButtonConfig(clazz.getName(), Lists.newArrayList());
        config.getBuilderConfigs().add(h);
    }

    /**
     * 构建健身通按钮责任链
     */
    public static BuilderChainConfig buildFitnessCrossChainConfig() {
        BuilderChainConfig config = new BuilderChainConfig();
        config.setBuilderConfigs(Lists.newArrayList());
        config.getBuilderConfigs().add(buildButtonConfig(MemberFreeBannerBuilder.class.getName(), Lists.newArrayList()));
        config.getBuilderConfigs().add(buildButtonConfig(FitnessCrossButtonBuilder.class.getName(), Lists.newArrayList()));
        return config;
    }

    /**
     * 同时展示预约和购买按钮团单底bar责任链
     * @return
     */
    private static BuilderChainConfig buildLeadsDealChainConfig() {
        BuilderChainConfig config = new BuilderChainConfig();
        config.setMaxButtonSize(2);
        config.setBuilderConfigs(Lists.newArrayList());
        config.getBuilderConfigs().add(buildButtonConfig(LeadsDealBannerBuilder.class.getName(), null));
        config.getBuilderConfigs().add(buildButtonConfig(MemberFreeBannerBuilder.class.getName(), null));
        config.getBuilderConfigs().add(buildButtonConfig(LeadsDealButtonBuilder.class.getName(), null));
        return config;
    }

    /**
     * 结婚业务下同时展示预约、咨询、购买按钮团单底bar责任链
     * @return
     */
    private static BuilderChainConfig buildWeddingLeadsDealChainConfig() {
        BuilderChainConfig config = new BuilderChainConfig();
        config.setMaxButtonSize(3);
        config.setBuilderConfigs(Lists.newArrayList());
        config.getBuilderConfigs().add(buildButtonConfig(MemberFreeBannerBuilder.class.getName(), null));
        config.getBuilderConfigs().add(buildButtonConfig(WeddingLeadsDealButtonBuilder.class.getName(), null));
        return config;
    }

}
