package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.ServiceguaranteequeryRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ServiceGuarantee.*;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.google.gson.reflect.TypeToken;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.service.DpPoiService;
import com.sankuai.sinai.data.api.service.MtPoiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ServiceGuaranteeQueryFacade {

    private static final List<String> DP_POI_FIELDS = Arrays.asList("shopId", "cityId");

    private static final List<String> MT_POI_FIELDS = Arrays.asList("mtPoiId", "dpCityId");

    public static final Integer DEFAULT_CITY_ID = -100;

    @Autowired
    private DpPoiService sinaiDpPoiService;

    @Autowired
    private MtPoiService sinaiMtPoiService;

    public ServiceGuaranteeDTO queryServiceGuaranteeDTO(ServiceguaranteequeryRequest request, EnvCtx envCtx) throws TException {
        List<Integer> serviceGuaranteeWhitelist = Lion.getList(Environment.getAppName(), "com.sankuai.dzu.tpbase.dztgdetailweb.serviceguarantee.whitelist", Integer.class);
        if (!serviceGuaranteeWhitelist.contains(request.getDealgroupId())) {
            return new ServiceGuaranteeDTO();
        }

        Long shopId = Long.valueOf(request.getShopidstr());

        String cityId2ServiceGuaranteeDtoJson = Lion.get("com.sankuai.dzu.tpbase.dztgdetailweb.serviceguarantee.cityconfiguration");
        Map<Integer, ServiceGuaranteeDTO> serviceGuaranteeDTOMap = parseServiceGuaranteeDTOJsonMap(cityId2ServiceGuaranteeDtoJson);

        if (MapUtils.isEmpty(serviceGuaranteeDTOMap)) {
            return new ServiceGuaranteeDTO();
        }

        if (!envCtx.isMt()) {
            return getServiceGuaranteeDTOByDpShopId(shopId, serviceGuaranteeDTOMap);

        } else {
            return getServiceGuaranteeDTOByMtShopId(shopId, serviceGuaranteeDTOMap);
        }
    }

    private Map<Integer, ServiceGuaranteeDTO> parseServiceGuaranteeDTOJsonMap(String json) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.ServiceGuaranteeQueryFacade.parseServiceGuaranteeDTOJsonMap(java.lang.String)");
        return GsonUtils.fromJsonString(json, new TypeToken<Map<Integer, ServiceGuaranteeDTO>>() {}.getType());
    }

    private ServiceGuaranteeDTO getServiceGuaranteeDTOByDpShopId(Long shopId, Map<Integer, ServiceGuaranteeDTO> serviceGuaranteeDTOMap) throws TException {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.ServiceGuaranteeQueryFacade.getServiceGuaranteeDTOByDpShopId(java.lang.Long,java.util.Map)");

        DpPoiRequest poiRequest = new DpPoiRequest();
        poiRequest.setShopIds(Collections.singletonList(shopId));
        poiRequest.setFields(DP_POI_FIELDS);

        Optional<DpPoiDTO> dpPoiDTO = Optional.ofNullable(sinaiDpPoiService.findShopsByShopIds(poiRequest))
                .map(dpPoiDTOList -> dpPoiDTOList.stream()
                        .collect(Collectors.toMap(DpPoiDTO::getShopId, a -> a))
                        .get(shopId));
        if (!dpPoiDTO.isPresent() || dpPoiDTO.get().getCityId()==null) {
            return new ServiceGuaranteeDTO();
        }

        ServiceGuaranteeDTO serviceGuaranteeDTO = serviceGuaranteeDTOMap.get(dpPoiDTO.get().getCityId());
        if (serviceGuaranteeDTO==null) {
            return serviceGuaranteeDTOMap.get(DEFAULT_CITY_ID);
        }

        return serviceGuaranteeDTO;
    }

    private ServiceGuaranteeDTO getServiceGuaranteeDTOByMtShopId(Long shopId, Map<Integer, ServiceGuaranteeDTO> serviceGuaranteeDTOMap) throws TException {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.ServiceGuaranteeQueryFacade.getServiceGuaranteeDTOByMtShopId(java.lang.Long,java.util.Map)");

        List<Long> mtShopIds = new ArrayList<>();
        mtShopIds.add(shopId);

        Optional<MtPoiDTO> mtPoiDTO = Optional.ofNullable(sinaiMtPoiService.findPoisById(mtShopIds, MT_POI_FIELDS))
                .map(poiDTOMap -> poiDTOMap.get(shopId));

        if (!mtPoiDTO.isPresent() || mtPoiDTO.get().getDpCityId()==null) {
            return new ServiceGuaranteeDTO();
        }

        ServiceGuaranteeDTO serviceGuaranteeDTO = serviceGuaranteeDTOMap.get(mtPoiDTO.get().getDpCityId());
        if (serviceGuaranteeDTO==null) {
            return  serviceGuaranteeDTOMap.get(DEFAULT_CITY_ID);
        }

        return serviceGuaranteeDTO;
    }
}
