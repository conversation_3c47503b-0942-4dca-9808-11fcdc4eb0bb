package com.dianping.mobile.mapi.dztgdetail.button.joy;

import com.alibaba.fastjson.JSON;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonStateEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtnIcon;
import com.dianping.mobile.mapi.dztgdetail.helper.*;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dealuser.price.display.api.model.PromoIdentity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class JoyDiscountCardButtonBuilder extends AbstractPriceServiceButtonBuilder {

    @Override
    protected PriceDisplayDTO getPrice(DealCtx context) {
        PriceDisplayDTO promoPrice = context.getPriceContext().getDcCardMemberPrice();
        if (promoPrice == null || memberCardPromoNotExist(promoPrice, context)) {
            return null;
        }

        if (BuyButtonHelper.isDaoGua(promoPrice.getPrice(), context)) {
            log.debug("团单id={},会员卡倒挂,会员卡={},常规价格={}", context.getDealId4P(), JSON.toJSONString(promoPrice), JSON.toJSONString(PriceHelper.getNormalPrice(context)));
            return null;
        }

        return promoPrice;
    }

    private boolean memberCardPromoNotExist(PriceDisplayDTO promoPrice, DealCtx context) {
        if (CollectionUtils.isEmpty(promoPrice.getUsedPromos())) {
            return true;
        }

        List<PromoDTO> usedPromos = promoPrice.getUsedPromos();
        if (context.getPriceContext().isZuLiaoButtonNewStyle()) {
            /**
             * 融合卡场景，会返回普通团购优惠+会员优惠，所以usedPromos会大于1。需要判断是否含有会员优惠，再构建会员button。
             */
            List<Integer> memberCardPromoTypeList = usedPromos.stream().filter(Objects::nonNull)
                    .map(PromoDTO::getIdentity).filter(Objects::nonNull).map(PromoIdentity::getPromoType)
                    .filter(promoType -> promoType.equals(PromoTypeEnum.MEMBER_DAY.getType())
                            || promoType.equals(PromoTypeEnum.DISCOUNT_CARD.getType()))
                    .collect(Collectors.toList());

            return CollectionUtils.isEmpty(memberCardPromoTypeList);
        }

        return false;
    }

    @Override
    protected void afterBuild(DealCtx context, DealBuyBtn button) {
        button.setDetailBuyType(BuyBtnTypeEnum.MEMBER_CARD.getCode());
        if (context.getPreButton() == null) {
            context.getBuyBar().setBuyType(DealBuyBar.BuyType.DISCOUNTCARD.type);
        }
        String url = UrlHelper.getMemberCardBuyUrl(context);
        button.setRedirectUrl(url);
        if (isHoldState(context)) {
            button.setState(ButtonStateEnum.HOLD);

            String title = getMemberCardTitle(context);
            DealBuyBtnIcon cardIcon = DealBuyHelper.getCardIcon(title, context.getPriceContext().isDcCardMemberDay());
            button.getBtnIcons().add(0, cardIcon);
        }

        //样式不参与AB，只替换颜色
        if (GreyUtils.showZuLiaoPriceDeal(context) && GreyUtils.showZuLiaoMarketPricePoi(context)){
            if (button.getBtnIcons().get(0)!=null){
                DealBuyBtnIcon dealBuyBtnIcon = button.getBtnIcons().get(0);
                DealBuyBtnIcon cardIcon=DealBuyHelper.getEntertainmentCardIcon(dealBuyBtnIcon.getTitle());
                button.getBtnIcons().remove(0);
                button.getBtnIcons().add(0,cardIcon);
            }
        }

        /**
         * DealBuyBtnIcon的促销文案要AB
         */
        if (context.getPriceContext().isZuLiaoButtonNewStyle()) {
            String shortPromoTag = context.getPriceContext().getDcCardMemberPrice().getShortPromoTag();
            button.setBtnTag(shortPromoTag);
            DealBuyBtnIcon cardIcon=DealBuyHelper.getEntertainmentCardIcon(shortPromoTag);
            button.setBtnIcons(Lists.newArrayList(cardIcon));
        }

        if (DealBuyHelper.isCouponBar(context)) {
            button.setBtnTitle(CouponBarHelper.getCouponBtnTitle(context));
        }

        //如果用户领取了足疗折扣卡（包括会员日），则文案是会员价
        if (isHoldState(context)) {
            button.setBtnTitle(getPromoButtonTitle(context, "会员价"));
        }
    }

    @Override
    protected boolean isHoldState(DealCtx context) {
        return CardHelper.holdCard(context.getPriceContext().getDcCardMemberCard());
    }


}
