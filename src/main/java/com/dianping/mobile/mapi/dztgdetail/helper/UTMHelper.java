package com.dianping.mobile.mapi.dztgdetail.helper;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by wa<PERSON><PERSON><PERSON> on 17/6/20.
 */
public class UTMHelper {

    private static final Pattern UTM_CAMPAIGN_PATTERN = Pattern.compile("^.*A([^A-Z]+?)[A-Z].*");

    public static String convertUTMCampaign2App(String utmCampaign) {
        String app = StringUtils.EMPTY;
        Matcher matcher = UTM_CAMPAIGN_PATTERN.matcher(utmCampaign);
        if (matcher.find()) {
            app = matcher.group(1);
        }
        return app;
    }
}
