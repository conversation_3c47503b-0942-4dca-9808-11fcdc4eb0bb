package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonStateEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.BuyBarShareInfo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo.CouponDescItem;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@TypeDoc(description = "团单购买按钮")
@MobileDo(id = 0xf29f)
@Data
public class DealBuyBtn implements Serializable {

    public DealBuyBtn(boolean btnEnable) {
        this.btnEnable = btnEnable;
    }

    public DealBuyBtn(boolean btnEnable, String btnTitle) {
        this.btnEnable = btnEnable;
        this.btnTitle = btnTitle;
    }

    @FieldDoc(description = "按钮是否可用")
    @MobileField(key = 0x51dd)
    private boolean btnEnable;

    @FieldDoc(description = "按钮标题")
    @MobileField(key = 0x3050)
    private String btnTitle;

    @FieldDoc(description = "按钮描述")
    @MobileField(key = 0x6f4e)
    private String btnDesc;

    @FieldDoc(description = "按钮标题")
    @MobileField(key = 0x764e)
    private String btnTag;

    @FieldDoc(description = "String类型的价格（不包含￥）")
    @MobileField(key = 0x9df1)
    private String priceStr;

    @FieldDoc(description = "最低价文案")
    @MobileField(key = 0x85fd)
    private String minPriceDesc;

    @FieldDoc(description = "前n天最低价格")
    @MobileField(key = 0x93b7)
    private String minPrice;

    @FieldDoc(description = "市场价，最高价")
    @MobileField(key = 0x6242)
    private String marketPrice;

    @FieldDoc(description = "价格前缀")
    @MobileField(key = 0x3a08)
    private String pricePrefix;

    @FieldDoc(description = "价格后缀")
    @MobileField(key = 0x3a08)
    private String pricePostfix;

    @FieldDoc(description = "跳转链接：按钮不可点击时无该值")
    @MobileField(key = 0x8283)
//    @EncryptedLinkField(queries = {"shopid"})
    private String redirectUrl;

    /**
     * @see com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum
     */
    @FieldDoc(description = "用于识别是团购还是次卡")
    @MobileField(key = 0xd1d2)
    private int detailBuyType;

    @FieldDoc(description = "按钮图标列表")
    @MobileField(key = 0x270)
    private List<DealBuyBtnIcon> btnIcons;

    @FieldDoc(description = "自动领取的优惠券信息")
    @MobileField(key = 0x7764)
    private CouponDescItem coupon;

    /**
     * 按钮对应的状态
     */
    private ButtonStateEnum state;

    @FieldDoc(description = "按钮文案")
    @MobileField(key = 0xa5e3)
    private String btnText;

    @FieldDoc(description = "按钮售卖状态")
    @MobileField(key = 0xdbeb)
    private String saleStatus;

    /**
     * @see com.dianping.mobile.mapi.dztgdetail.common.enums.ShoppingCartStatusEnum
     */
    @FieldDoc(description = "加入购物车按钮状态")
    @MobileField(key = 0xc463)
    private int addShoppingCartStatus = 0;

    @FieldDoc(description = "购物车价格规格模块")
    @MobileField(key = 0xec0e)
    private PriceRuleModule priceRuleModule;

    @FieldDoc(description = "购物车跳转链接")
    @MobileField(key = 0xcbd9)
    private String shoppingCartRedirectUrl;

    @FieldDoc(description = "按钮副标题")
    @MobileField(key = 0xcff6)
    private String btnSubTitle;

    @FieldDoc(description = "拦截弹窗信息")
    @MobileField(key = 0xdbd7)
    private String blockMsg;

    @FieldDoc(description = "购买提示弹窗")
    @MobileField(key = 0xe61a)
    private PurchaseMessageRemindInfo purchaseMessage;

    @FieldDoc(description = "外部跳链，如拼团场景的拼团结果页跳链")
    @MobileField(key = 0xe2ae)
    private String externalJumpUrl;

    @FieldDoc(description = "拼团结束时间时间戳")
    @MobileField(key = 0xddb1)
    private Long countDownTs;

    @FieldDoc(description = "底bar分享信息")
    @MobileField(key = 0x4093)
    private BuyBarShareInfo buyBarShareInfo;

    @FieldDoc(description = "是否使用电话")
    @MobileField(key = 0xa60)
    private boolean usePhone;

    @FieldDoc(description = "商户电话")
    @MobileField(key = 0x6536)
    private List<String> phoneNos;
}
