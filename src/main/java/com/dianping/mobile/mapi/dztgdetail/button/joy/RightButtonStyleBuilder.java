package com.dianping.mobile.mapi.dztgdetail.button.joy;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;

public class RightButtonStyleBuilder extends AbstractButtonBuilder {

    @Override
    public void build(DealCtx context, ButtonBuilderChain chain) {
        adaptIconTitle(context);
        chain.build(context);
    }

    private void adaptIconTitle(DealCtx context) {

        // 只有一个按钮， 不用处理
        if (context.getButtonCount() <= 1 || context.getCostEffectivePinTuan().isCePinTuanScene()) {
            return;
        }

        DealBuyHelper.convertToDoubleButtonStyle(context, context.getPreButton());
    }

    @Override
    protected void doBuild(DealCtx context, ButtonBuilderChain chain) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.button.joy.RightButtonStyleBuilder.doBuild(DealCtx,ButtonBuilderChain)");

    }
}
