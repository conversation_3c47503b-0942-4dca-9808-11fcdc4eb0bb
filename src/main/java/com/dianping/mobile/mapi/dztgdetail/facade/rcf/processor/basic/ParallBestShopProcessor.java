package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.basic;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.deal.common.enums.GpsType;
import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.deal.shop.dto.BestShopFastReq;
import com.dianping.deal.shop.dto.BestShopReq;
import com.dianping.deal.shop.dto.BestShopSimpleDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperCacheWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.exception.BestShopNotEqualException;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.exception.ShopIdMapperException;
import com.dianping.mobile.mapi.dztgdetail.util.DealProductUtils;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LogUtils;
import com.dianping.mobile.mapi.dztgdetail.util.PreviewDealGroupUtils;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;

@Slf4j
public class ParallBestShopProcessor extends AbsDealProcessor {

    @Autowired
    private DealGroupWrapper dealGroupWrapper;
    @Autowired
    private MapperCacheWrapper mapperCacheWrapper;

    public final static String BEST_SHOP_GREY = "bestShopGrey";

    public final static String FAST_BEST_SHOP = "FAST_BEST_SHOP";

    /**
     * 若是预览单，则关闭
     * @param ctx context
     * @return boolean
     */
    @Override
    public boolean isEnable(DealCtx ctx) {
        return !PreviewDealGroupUtils.isPreviewDianpingDealGroup(ctx);
    }

    private boolean switchAllToFastBestShop() {
        return Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.fast.best.shop.switcher", false);
    }

    private boolean isNeedDiff() {
        return Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.fast.best.shop.diff.switcher", true);
    }

    @Override
    public void prepare(DealCtx ctx) {
        if (!GreyUtils.enableQueryCenterForMainApi(ctx)) {
            throw new IllegalArgumentException("非查询中心链路不能使用该bestShop逻辑");
        }
        if (switchAllToFastBestShop()) {
            doFastBestShopPre(ctx);
            if (isNeedDiff()) {
                doBestShopWithoutShopIdPre(ctx);
            }
        } else {
            doBestShopPre(ctx);
        }
    }

    private void doFastBestShopPre(DealCtx ctx) {
        BestShopFastReq req = buildFastBestShopReq(ctx);
        Future shopFuture = dealGroupWrapper.preDealGroupBestShopFastly(req);
        ctx.getFutureCtx().setFastBestShopFuture(shopFuture);
    }

    private void doBestShopWithoutShopIdPre(DealCtx ctx) {
        BestShopReq req = buildBestShopReqWithoutShopId(ctx);
        Future shopFuture = dealGroupWrapper.preDealGroupBestShop(req);
        ctx.getFutureCtx().setBestShopFuture(shopFuture);
    }

    private void doBestShopPre(DealCtx ctx) {
        BestShopReq req = buildBestShopReq(ctx);
        Future shopFuture = dealGroupWrapper.preDealGroupBestShop(req);
        ctx.getFutureCtx().setBestShopFuture(shopFuture);
    }

    private BestShopFastReq buildFastBestShopReq(DealCtx ctx) {
        BestShopFastReq bestShopFastReq = new BestShopFastReq();
        bestShopFastReq.setDpDealGroupId(ctx.getDpId());

        bestShopFastReq.setUserGcjLat(ctx.getUserlat());
        bestShopFastReq.setUserGcjLng(ctx.getUserlng());
        bestShopFastReq.setClientType(ctx.getEnvCtx().getClientType());
        bestShopFastReq.setGeoDpCityId(ctx.getDpGpsCityId());

        bestShopFastReq.setDpCityId(ctx.getDpCityId());
        return bestShopFastReq;
    }

    private BestShopReq buildBestShopReq(DealCtx ctx) {
        BestShopReq shopReq = buildBestShopReqWithoutShopId(ctx);
        if (ctx.isMt()) {
            shopReq.setShopId(ctx.getMtLongShopId());
        } else {
            shopReq.setShopId(ctx.getDpLongShopId());
        }
        return shopReq;
    }

    private BestShopReq buildBestShopReqWithoutShopId(DealCtx ctx) {
        BestShopReq shopReq = new BestShopReq();
        if (ctx.isMt()) {
            shopReq.setDealGroupId(ctx.getMtId());
            shopReq.setCityId(ctx.getMtCityId());
        } else {
            shopReq.setDealGroupId(ctx.getDpId());
            shopReq.setCityId(ctx.getDpCityId());
        }
        shopReq.setLat(ctx.getUserlat());
        shopReq.setLng(ctx.getUserlng());
        shopReq.setGpsType(GpsType.GCJ02.getType());
        shopReq.setClientType(ctx.getEnvCtx().getClientType());
        return shopReq;
    }

    @Override
    public void process(DealCtx ctx) {
        List<Long> relatedShops = getDisplayShopIds(ctx.getDealGroupDTO());
        if (ctx.getFutureCtx().getFastBestShopFuture() != null) {
            processNewBestShop(ctx, relatedShops);
        } else {
            processOldBestShop(ctx, relatedShops);
        }
    }

    /**
     * 三种情情况：
     * 1-入口带了来源门店id且来源门店id和团购绑定，直接使用来源门店id，后续需要再组装bestShop对象
     * 2-入口带了来源门店id但来源门店id和团购不绑定，则重新请求，入参不指定门店
     * 3-入口没带来源门店id，直接使用请求结果
     */
    private void processNewBestShop(DealCtx ctx, List<Long> relatedShops) {
        if (isNeedDiff()) {
            new Thread(() -> compare(ctx)).start();
        }
        //默认后续再组装bestShop对象
        ctx.setNeedFillBestShop(true);
        BestShopSimpleDTO bestShopSimpleDTO = null;
        if (ctx.getDpLongShopId() > 0) {
            // todo xiangrui 门店判断
            if (DealProductUtils.checkShopNoExist(ctx.getDealGroupDTO(), ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId(), ctx.isMt())
                    && !relatedShops.contains(ctx.getDpLongShopId())) {
                Cat.logEvent(FAST_BEST_SHOP, "shopId_not_bind");
                // 有来源门店，但是来源门店和团单不绑定，则重新选择最佳门店
                bestShopSimpleDTO = dealGroupWrapper.getFutureResult(ctx.getFutureCtx().getFastBestShopFuture());
            } else {
                // 有来源门店，且来源门店和团单绑定，直接使用来源门店id
                Cat.logEvent(FAST_BEST_SHOP, "shopId_bind");
            }
        } else {
            // 没有来源门店id,直接选择最佳门店
            Cat.logEvent(FAST_BEST_SHOP, "shopId_empty");
            Cat.logEvent("SHOP_EMPTY_REASON", String.format("%s-%s", ctx.getEnvCtx().getClientType(), ctx.getRequestSource()));
            bestShopSimpleDTO = dealGroupWrapper.getFutureResult(ctx.getFutureCtx().getFastBestShopFuture());
        }
        if (bestShopSimpleDTO != null) {
            ctx.setDpLongShopId(bestShopSimpleDTO.getDpShopId());
            long mtShopId = mapperCacheWrapper.fetchMtShopId(bestShopSimpleDTO.getDpShopId());
            ctx.setMtLongShopId(mtShopId);
        }
        doCheckBestShopResult(ctx, relatedShops);
        ctx.setBestShopResp(initBestShopModel(ctx));
        LogUtils.info("[ParallBestShopProcessor] prepare. processNewBestShop userId={} response={}",
                ctx.getUserId4P(), String.format("dpShopId:%s;mtShopId:%s", ctx.getDpLongShopId(), ctx.getMtLongShopId()));
    }

    /**
     * 做兜底检查和数据填充，如果点评和美团门店都没就取适用门店第一个，如果缺失点评或者美团门店就做id转换（一般不存在）
     */
    private void doCheckBestShopResult(DealCtx ctx, List<Long> relatedShops) {
        if (ctx.getDpLongShopId() <= 0 && ctx.getMtLongShopId() <= 0) {
            //如果点评和美团门店Id都<=0，则走兜底取适用门店第一个
            Cat.logEvent(FAST_BEST_SHOP, "all_shopId_result_empty");
            // todo xiangrui 可以接受截断的使用
            if (CollectionUtils.isNotEmpty(relatedShops)) {
                ctx.setDpLongShopId(relatedShops.get(0));
            }
            if (ctx.getDpLongShopId() <= 0) {
                //如果适用门店也是空则报错
                Cat.logEvent(FAST_BEST_SHOP, "related_shopIds_empty");
            } else {
                long mtShopId = mapperCacheWrapper.fetchMtShopId(ctx.getDpLongShopId());
                if (mtShopId <= 0) {
                    Cat.logEvent(FAST_BEST_SHOP, "related_mtShopId_is_null");
                }
                ctx.setMtLongShopId(mtShopId);
            }
        } else if (ctx.getDpLongShopId() > 0 && ctx.getMtLongShopId() <= 0) {
            //如果有点评门店Id，但缺失美团门店Id（一般不可能出现）
            Cat.logEvent(FAST_BEST_SHOP, "mt_shopId_result_empty");
            long mtShopId = mapperCacheWrapper.fetchMtShopId(ctx.getDpLongShopId());
            if (mtShopId <= 0) {
                Cat.logEvent(FAST_BEST_SHOP, "related_mtShopId_is_null");
                log.error("mtShopId is null,dpShopId:{}", ctx.getDpLongShopId(), new ShopIdMapperException("mtShopId is null,dpShopId:" + ctx.getDpLongShopId()));
            }
            ctx.setMtLongShopId(mtShopId);
        } else if (ctx.getDpLongShopId() <= 0 && ctx.getMtLongShopId() > 0) {
            //如果有美团门店Id，但缺失点评门店Id（一般不可能出现）
            Cat.logEvent(FAST_BEST_SHOP, "dp_shopId_result_empty");
            long dpShopId = mapperCacheWrapper.fetchDpShopId(ctx.getMtLongShopId());
            if (dpShopId <= 0) {
                Cat.logEvent(FAST_BEST_SHOP, "related_dpShopId_is_null");
                log.error("dpShopId is null,mtShopId:{}", ctx.getMtLongShopId(), new ShopIdMapperException("dpShopId is null,mtShopId:" + ctx.getMtLongShopId()));
            }
            ctx.setDpLongShopId(dpShopId);
        }
    }

    private void compare(DealCtx ctx) {
        BestShopSimpleDTO bestShopSimpleDTO = dealGroupWrapper.getFutureResult(ctx.getFutureCtx().getFastBestShopFuture());
        if (bestShopSimpleDTO == null) {
            Cat.logEvent("BestShopCompare", "bestShopSimpleDTO is null");
            return;
        }
        BestShopDTO bestShopDTO = dealGroupWrapper.getFutureResult(ctx.getFutureCtx().getBestShopFuture());
        if (bestShopDTO == null) {
            Cat.logEvent("BestShopCompare", "bestShopDTO is null");
            return;
        }
        if (bestShopDTO.getDpShopId() != bestShopSimpleDTO.getDpShopId()) {
            Cat.logEvent("BestShopCompare", "shopId not equals");
            log.error("BestShopCompare not equals,fastReq:{},oldReq:{}",
                    JSON.toJSONString(buildFastBestShopReq(ctx)),
                    JSON.toJSONString(buildBestShopReqWithoutShopId(ctx)),
                    new BestShopNotEqualException("最佳门店dp门店ID不一致")
            );
            return;
        }
        Cat.logEvent("BestShopCompare", "equals");
    }

    /**
     * 三种情情况：
     * 1-入口带了来源门店id且来源门店id和团购绑定，直接使用来源门店id，后续需要再组装bestShop对象
     * 2-入口带了来源门店id但来源门店id和团购不绑定，则重新请求，入参不指定门店
     * 3-入口没带来源门店id，直接使用请求结果
     */
    private void processOldBestShop(DealCtx ctx, List<Long> relatedShops) {
        BestShopDTO bestShop;
        if (ctx.getDpLongShopId() > 0) {
            if (DealProductUtils.checkShopNoExist(ctx.getDealGroupDTO(), ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId(), ctx.isMt())
                    && !relatedShops.contains(ctx.getDpLongShopId())) {
                Cat.logEvent(BEST_SHOP_GREY, "shopId_not_bind");
                // 有来源门店，但是来源门店和团单不绑定，则重新请求，入参不指定门店
                Future shopFuture = dealGroupWrapper.preDealGroupBestShop(buildBestShopReqWithoutShopId(ctx));
                bestShop = dealGroupWrapper.getFutureResult(shopFuture);
            } else {
                Cat.logEvent(BEST_SHOP_GREY, "shopId_bind");
                // 有来源门店，且来源门店和团单绑定，直接使用来源门店id，后续需要再组装bestShop对象
                bestShop = initBestShopModel(ctx);
                ctx.setNeedFillBestShop(true);
            }
        } else {
            // 没有来源门店id,可以直接使用请求结果
            Cat.logEvent(BEST_SHOP_GREY, "shopId_empty");
            Cat.logEvent("SHOP_EMPTY_REASON", String.format("%s-%s", ctx.getEnvCtx().getClientType(), ctx.getRequestSource()));
            bestShop = dealGroupWrapper.getFutureResult(ctx.getFutureCtx().getBestShopFuture());
        }
        LogUtils.info("[ParallBestShopProcessor] prepare. getDealGroupBestShop userId={} response={}",
                ctx.getUserId4P(), JsonCodec.encode(bestShop));
        ctx.setBestShopResp(bestShop);

        if (ctx.getLongPoiId4PFromReq() > 0) {
            return;
        }
        if (bestShop == null) {
            return;
        }
        ctx.setDpLongShopId(bestShop.getDpShopId());
        ctx.setMtLongShopId(bestShop.getMtShopId());
    }

    private BestShopDTO initBestShopModel(DealCtx ctx) {
        BestShopDTO bestShop = new BestShopDTO();
        //只填充shopId，别的后续填充
        bestShop.setDpShopId(ctx.getDpLongShopId());
        bestShop.setMtShopId(ctx.getMtLongShopId());
        return bestShop;
    }

    private List<Long> getDisplayShopIds(DealGroupDTO dealGroupDTO) {
        if(dealGroupDTO == null || dealGroupDTO.getDisplayShopInfo() == null || dealGroupDTO.getDisplayShopInfo().getDpDisplayShopIds() == null) {
            return Collections.emptyList();
        }
        return dealGroupDTO.getDisplayShopInfo().getDpDisplayShopIds();
    }

}
