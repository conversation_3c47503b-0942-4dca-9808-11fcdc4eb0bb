package com.dianping.mobile.mapi.dztgdetail.button;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.entity.FreeDealConfig;
import com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper;

import java.util.Objects;

public class FreeDealButtonBuilder extends AbstractButtonBuilder {

    @Override
    protected void doBuild(DealCtx context, ButtonBuilderChain chain) {
        FreeDealConfig freeDealConfig = context.getFreeDealConfig();
        if (Objects.isNull(freeDealConfig)) {
            return;
        }
        boolean isShow = context.getEnvCtx().isNative() || (context.getEnvCtx().isWxMini() && !Lion.getBoolean("com.sankuai.dzu.tpbase.dztgdetailweb", "mini.app.jump.booking.forbidden", true));
        if (!isShow) {
            DealBuyBtn btn = new DealBuyBtn(false, freeDealConfig.getEmptyButtonText());
            btn.setAddShoppingCartStatus(0);
            btn.setDetailBuyType(BuyBtnTypeEnum.RESV_DEAL.getCode());
            context.addButton(btn);
        } else {
            DealBuyBtn btn = new DealBuyBtn(true, freeDealConfig.getButtonText());
            btn.setAddShoppingCartStatus(0);
            btn.setDetailBuyType(BuyBtnTypeEnum.RESV_DEAL.getCode());
            btn.setRedirectUrl(UrlHelper.getFreeDealUrl(context, freeDealConfig));
            context.addButton(btn);
        }
        chain.build(context);
    }
}
