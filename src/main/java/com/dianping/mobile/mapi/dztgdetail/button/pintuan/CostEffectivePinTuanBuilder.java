package com.dianping.mobile.mapi.dztgdetail.button.pintuan;

import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.constants.PinTuanConstants;
import com.dianping.mobile.mapi.dztgdetail.common.constants.PlusIcons;
import com.dianping.mobile.mapi.dztgdetail.common.enums.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.BuyBarShareInfo;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.ImageHelper;
import com.dianping.mobile.mapi.dztgdetail.util.CostEffectivePinTuanUtils;
import com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil;
import com.dianping.mobile.mapi.dztgdetail.util.NumbersUtils;
import com.dianping.mobile.mapi.dztgdetail.util.richText.RichText;
import com.dianping.mobile.mapi.dztgdetail.util.richText.RichTextUtil;
import com.dianping.mobile.mapi.dztgdetail.util.richText.TextStyle;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2024/4/24
 */
@Slf4j
public class CostEffectivePinTuanBuilder extends AbstractButtonBuilder {

    @Override
    protected void doBuild(DealCtx context, ButtonBuilderChain chain) {
        if (CostEffectivePinTuanUtils.isCePinTuaScene(context)) {
            buildButtonBanner(context);
            buildButton(context);
        }
        chain.build(context);
    }

    /**
     * 构建底bar
     * @param ctx
     */
    private void buildButton(DealCtx ctx) {
        if (CostEffectivePinTuanUtils.enhancedStyle(ctx)) {
            // 强化样式
            buildEnhancedButton(ctx);
        } else {
            // 弱化样式
            buildWeakButton(ctx);
        }
    }

    private void buildEnhancedButton(DealCtx ctx) {
        if (notInPinTuan(ctx)) { // 不在拼团中
            buildEnhancedNotInPinTuan(ctx);
        } else { // 在拼团中
            if (CostEffectivePinTuanUtils.activePinTuan(ctx)) { // 主态
                buildEnhancedActivePinTuan(ctx);
            } else { // 客态
                if (CostEffectivePinTuanUtils.inPinTuan(ctx)) { // 已参团
                    buildEnhancedActivePinTuan(ctx);
                } else {    // 未参团
                    buildPassivePinTuan(ctx);
                }

            }
        }
    }

    private void buildWeakButton(DealCtx ctx) {
        if (notInPinTuan(ctx)) { // 不在拼团中
            buildWeakNotInPinTuan(ctx);
        } else { // 在拼团中
            if (CostEffectivePinTuanUtils.activePinTuan(ctx)) { // 主态
                buildWeakActivePinTuan(ctx);
            } else { // 客态
                if (CostEffectivePinTuanUtils.inPinTuan(ctx)) { // 已参团
                    buildEnhancedActivePinTuan(ctx);
                } else {    // 未参团
                    buildPassivePinTuan(ctx);
                }
            }
        }
    }

    private PriceRuleModule buildPriceRuleModule() {
        PriceRuleModule priceRuleModule = new PriceRuleModule();
        priceRuleModule.setPriceRuleType(BuyBtnTypeEnum.COST_EFFECTIVE_PINTUAN.getCode());
        return priceRuleModule;
    }

    public void buildWeakActivePinTuan(DealCtx context) {
        PriceDisplayDTO normalPrice = context.getPriceContext().getNormalPrice();
        if (normalPrice == null || !stateMatch(context)) {
            return;
        }

        // 直接购买按钮
        DealBuyBtn rightButton = new DealBuyBtn(true, PinTuanConstants.DIRECT_BUY);
        rightButton.setPriceStr(formatPrice(normalPrice.getPrice()));
        rightButton.setRedirectUrl(getRedirectUrl(context, false));
        rightButton.setDetailBuyType(BuyBtnTypeEnum.NORMAL_DEAL.getCode());
        rightButton.setAddShoppingCartStatus(ShoppingCartStatusEnum.NONE.code);
        rightButton.setPriceRuleModule(buildPriceRuleModule());
        context.addButton(rightButton);

        // 拼团中按钮
        PriceDisplayDTO pintuanPrice = context.getPriceContext().getCostEffectivePrice();
        DealBuyBtn leftButton = new DealBuyBtn(true, PinTuanConstants.PINTUAN_PROCESSING);
        leftButton.setPriceStr(formatPrice(pintuanPrice.getPrice()));
        leftButton.setExternalJumpUrl(context.getEnvCtx().getDztgClientTypeEnum().equals(DztgClientTypeEnum.MEITUAN_APP) ? getPinTuanResultUrl(context) : CostEffectivePinTuanUtils.getWxShareJumpUrl(context));
        leftButton.setDetailBuyType(BuyBtnTypeEnum.COST_EFFECTIVE_PINTUAN.getCode());
        leftButton.setBtnIcons(buildWeakActiveBtnIcons(context));
        leftButton.setAddShoppingCartStatus(ShoppingCartStatusEnum.NONE.code);
        leftButton.setBuyBarShareInfo(getBuyBarShareInfo(context));
        leftButton.setPriceRuleModule(buildPriceRuleModule());
        leftButton.setCountDownTs(context.getCostEffectivePinTuan().getExpireTime());
        context.addButton(leftButton);

        // 构建拼团成员信息
        PinTuanMemberModule memberModule = new PinTuanMemberModule();
        int textSize = 11;
        RichText.TextItem defaultTextItem = new RichText.TextItem();
        defaultTextItem.setTextsize(textSize);
        defaultTextItem.setTextstyle(TextStyle.BOLD.getStyle());
        defaultTextItem.setTextcolor("#222222");

        RichText richText=new RichText();
        richText.getTextItemList().add(RichTextUtil.buildTextItem("还差", defaultTextItem));
        richText.getTextItemList().add(RichTextUtil.buildTextItem(String.valueOf(CostEffectivePinTuanUtils.getNeedMemberCount(context)), defaultTextItem));
        richText.getTextItemList().add(RichTextUtil.buildTextItem("人成团", defaultTextItem));
        memberModule.setGeneralInfo(richText.toString());
        memberModule.setAvatars(context.getCostEffectivePinTuan().getAvatars());
        context.getBuyBar().setPinTuanMemberModule(memberModule);
        context.getBuyBar().setStyleType(StyleTypeEnum.PINTUAN_WEAK_ACTIVE_TWO_BUTTONS.code);
    }

    private String getPinTuanResultUrl(DealCtx context) {
        return String.format("imeituan://www.meituan.com/mrn?mrn_biz=meishi&mrn_entry=group-pintuan-result&mrn_component=main&orderGroupId=%s&scene=share_page",
                context.getCostEffectivePinTuan().getShareToken());
    }

    private BuyBarShareInfo getBuyBarShareInfo(DealCtx context) {
        BuyBarShareInfo shareInfo = new BuyBarShareInfo();
        shareInfo.setTitle(context.getCostEffectivePinTuan().getPinTuanPassParamConfig().getTitle());
        shareInfo.setJumpUrl(CostEffectivePinTuanUtils.getWxShareJumpUrl(context));
        shareInfo.setBackground(context.getCostEffectivePinTuan().getPinTuanPassParamConfig().getBackground());
        shareInfo.setDealImage(getDealImage(context));
        shareInfo.setTemplateId(context.getCostEffectivePinTuan().getPinTuanPassParamConfig().getTemplateId());
        return shareInfo;
    }

    private String getDealImage(DealCtx context) {
        DealGroupBaseDTO dealGroupBaseDTO = context.getDealGroupBase();
        String defaultPic = dealGroupBaseDTO.getDefaultPic();
        int[] imageSize  = convertWidthHeight(AppImageSize.MINI_PROGRAM_SQUARE_SHARE.width, AppImageSize.MINI_PROGRAM_SQUARE_SHARE.height);
        if (imageSize != null) {
            return ImageHelper.formatWithoutWatermark(defaultPic, imageSize[0], imageSize[1], true);
        } else {
            return ImageHelper.formatWithoutWatermark(defaultPic, AppImageSize.MEDIUM.width, AppImageSize.MEDIUM.height, true);
        }
    }

    /**
     * 输入图片宽高，高不变，宽高比5:4，输出新的宽高
     * @param width
     * @param height
     * @return
     */
    public int[] convertWidthHeight(int width, int height) {
        if (!NumbersUtils.greaterThanZero(width) || !NumbersUtils.greaterThanZero(height)) {
            return null;
        }
        int newWidth = (int) (height * 5.0 / 4.0);
        int newHeight = height;
        if(newWidth > width) {
            newWidth = width;
            newHeight = (int) (width * 4.0 / 5.0);
        }
        return new int[]{newWidth, newHeight};
    }

    private void buildWeakNotInPinTuan(DealCtx context) {
        PriceDisplayDTO normalPrice = context.getPriceContext().getNormalPrice();
        if (normalPrice == null || !stateMatch(context)) {
            return;
        }

        // 直接购买按钮
        DealBuyBtn rightButton = new DealBuyBtn(true, PinTuanConstants.DIRECT_BUY);
        rightButton.setPriceStr(formatPrice(normalPrice.getPrice()));
        rightButton.setRedirectUrl(getRedirectUrl(context, false));
        rightButton.setDetailBuyType(BuyBtnTypeEnum.NORMAL_DEAL.getCode());
        rightButton.setAddShoppingCartStatus(ShoppingCartStatusEnum.NONE.code);
        rightButton.setPriceRuleModule(buildPriceRuleModule());
        context.addButton(rightButton);

        // 拼团购买按钮
        PriceDisplayDTO pinTuanPrice = context.getPriceContext().getCostEffectivePrice();
        DealBuyBtn leftButton = new DealBuyBtn(true, getPinTuanBtnTitle(context));
        leftButton.setPriceStr(formatPrice(pinTuanPrice.getPrice()));
        leftButton.setRedirectUrl(getRedirectUrl(context, true));
        leftButton.setDetailBuyType(BuyBtnTypeEnum.COST_EFFECTIVE_PINTUAN.getCode());
        leftButton.setBtnIcons(buildBtnIcons(context, true));
        leftButton.setAddShoppingCartStatus(ShoppingCartStatusEnum.NONE.code);
        leftButton.setPriceRuleModule(buildPriceRuleModule());
        context.addButton(leftButton);
        context.getBuyBar().setStyleType(StyleTypeEnum.PINTUAN_WAITING_TWO_BUTTONS.code);
    }

    private boolean notInPinTuan(DealCtx ctx) {
        // 未开团 || （已开团&&团满）
        return !CostEffectivePinTuanUtils.pinTuanOpened(ctx)
                || (CostEffectivePinTuanUtils.pinTuanOpened(ctx) && CostEffectivePinTuanUtils.getNeedMemberCount(ctx) <= 0);
    }

    public void buildPassivePinTuan(DealCtx context) {
        // 参与拼团底bar
        DealBuyBtn rightButton = new DealBuyBtn(true, PinTuanConstants.JOIN_PIN_TUAN);
        rightButton.setPriceStr(StringUtils.EMPTY);
        rightButton.setRedirectUrl(getRedirectUrl(context, true));
        rightButton.setDetailBuyType(BuyBtnTypeEnum.COST_EFFECTIVE_PINTUAN.getCode());
        rightButton.setBtnIcons(Lists.newArrayList());
        rightButton.setAddShoppingCartStatus(ShoppingCartStatusEnum.NONE.code);
        rightButton.setPriceRuleModule(buildPriceRuleModule());
        rightButton.setCountDownTs(context.getCostEffectivePinTuan().getExpireTime());
        context.addButton(rightButton);

        // 构建拼团成员信息
        PinTuanMemberModule memberModule = new PinTuanMemberModule();
        int textSize = 11;
        RichText.TextItem defaultTextItem = new RichText.TextItem();
        defaultTextItem.setTextsize(textSize);
        defaultTextItem.setTextstyle(TextStyle.BOLD.getStyle());
        defaultTextItem.setTextcolor("#222222");

        RichText.TextItem highlightItem = new RichText.TextItem();
        highlightItem.setTextcolor("#FF2727");
        highlightItem.setTextsize(textSize);
        highlightItem.setTextstyle(TextStyle.BOLD.getStyle());

        RichText richText=new RichText();
        richText.getTextItemList().add(RichTextUtil.buildTextItem("还差", defaultTextItem));
        richText.getTextItemList().add(RichTextUtil.buildTextItem(String.valueOf(CostEffectivePinTuanUtils.getNeedMemberCount(context)), highlightItem));
        richText.getTextItemList().add(RichTextUtil.buildTextItem("人拼团成功", defaultTextItem));
        memberModule.setGeneralInfo(richText.toString());
        memberModule.setAvatars(context.getCostEffectivePinTuan().getAvatars());
        context.getBuyBar().setPinTuanMemberModule(memberModule);
        context.getBuyBar().setStyleType(StyleTypeEnum.PINTUAN_ENHANCE_PASSIVE_JOIN_SINGLE_BUTTON.code);
    }

    /**
     * 主态+拼团中
     * @param context
     */
    private void buildEnhancedActivePinTuan(DealCtx context) {
        PriceDisplayDTO pinTuanPrice = context.getPriceContext().getCostEffectivePrice();
        if (pinTuanPrice == null || !stateMatch(context)) {
            return;
        }

        // 构建分享底bar
        DealBuyBtn rightButton = new DealBuyBtn(true, context.getCostEffectivePinTuan().isLimitNewCustomJoin() ? PinTuanConstants.INVITE_NEW_USER : PinTuanConstants.INVITE_FRIEND_USER);
        rightButton.setPriceStr(StringUtils.EMPTY);
        rightButton.setRedirectUrl(CostEffectivePinTuanUtils.getPinTuanResultPageUrl(context));   // 进入拼团结果页
        rightButton.setDetailBuyType(BuyBtnTypeEnum.COST_EFFECTIVE_PINTUAN.getCode());
        rightButton.setBtnIcons(Lists.newArrayList());
        rightButton.setAddShoppingCartStatus(ShoppingCartStatusEnum.NONE.code);
        rightButton.setCountDownTs(context.getCostEffectivePinTuan().getExpireTime());
        rightButton.setBuyBarShareInfo(getBuyBarShareInfo(context));
        rightButton.setPriceRuleModule(buildPriceRuleModule());
        context.addButton(rightButton);

        // 构建拼团成员信息
        PinTuanMemberModule memberModule = new PinTuanMemberModule();
        int textSize = 11;
        RichText.TextItem defaultTextItem = new RichText.TextItem();
        defaultTextItem.setTextsize(textSize);
        defaultTextItem.setTextstyle(TextStyle.BOLD.getStyle());
        defaultTextItem.setTextcolor("#222222");

        RichText.TextItem highlightItem = new RichText.TextItem();
        highlightItem.setTextcolor("#FF2727");
        highlightItem.setTextsize(textSize);
        highlightItem.setTextstyle(TextStyle.BOLD.getStyle());

        RichText richText=new RichText();
        richText.getTextItemList().add(RichTextUtil.buildTextItem("已参与拼团，还差", defaultTextItem));
        richText.getTextItemList().add(RichTextUtil.buildTextItem(String.valueOf(CostEffectivePinTuanUtils.getNeedMemberCount(context)), highlightItem));
        richText.getTextItemList().add(RichTextUtil.buildTextItem("人成团", defaultTextItem));
        memberModule.setGeneralInfo(richText.toString());
        memberModule.setAvatars(context.getCostEffectivePinTuan().getAvatars());
        context.getBuyBar().setPinTuanMemberModule(memberModule);
        context.getBuyBar().setStyleType(StyleTypeEnum.PINTUAN_ENHANCE_SINGLE_BUTTON.code);
    }

    /**
     * 强化+不在拼团中
     * @param context
     */
    private void buildEnhancedNotInPinTuan(DealCtx context) {
        PriceDisplayDTO pinTuanPrice = context.getPriceContext().getCostEffectivePrice();
        if (pinTuanPrice == null || !stateMatch(context)) {
            return;
        }

        // 拼团购买底bar
        DealBuyBtn rightButton = new DealBuyBtn(true, getPinTuanBtnTitle(context));
        rightButton.setPriceStr(Objects.nonNull(pinTuanPrice.getPrice()) ? formatPrice(pinTuanPrice.getPrice()) : StringUtils.EMPTY);
        rightButton.setRedirectUrl(getRedirectUrl(context, true));
        rightButton.setDetailBuyType(BuyBtnTypeEnum.COST_EFFECTIVE_PINTUAN.getCode());
        rightButton.setBtnIcons(buildBtnIcons(context, false));
        rightButton.setAddShoppingCartStatus(ShoppingCartStatusEnum.NONE.code);
        rightButton.setPriceRuleModule(buildPriceRuleModule());
        context.addButton(rightButton);

        // 直接购买底bar
        PriceDisplayDTO normalPrice = context.getPriceContext().getNormalPrice();
        DealBuyBtn leftButton = new DealBuyBtn(true, PinTuanConstants.SINGLE_BUY);
        leftButton.setPriceStr(Objects.nonNull(normalPrice) && Objects.nonNull(normalPrice.getPrice()) ? formatPrice(normalPrice.getPrice()) : StringUtils.EMPTY);
        leftButton.setRedirectUrl(getRedirectUrl(context, false));
        leftButton.setDetailBuyType(BuyBtnTypeEnum.NORMAL_DEAL.getCode());
        leftButton.setAddShoppingCartStatus(ShoppingCartStatusEnum.NONE.code);
        leftButton.setPriceRuleModule(buildPriceRuleModule());
        context.addButton(leftButton);
        context.getBuyBar().setStyleType(StyleTypeEnum.PINTUAN_WAITING_TWO_BUTTONS.code);
    }

    private List<DealBuyBtnIcon> buildBtnIcons(DealCtx ctx, boolean isLeft) {
        List<DealBuyBtnIcon> icons = Lists.newArrayList();
        // 新用户团无tips，直接返回
        if (ctx.getCostEffectivePinTuan().isLimitNewCustomJoin()) {
            return icons;
        }

        DealBuyBtnIcon buttonIcon = DealBuyHelper.getPinTuanTipsIcon(PinTuanConstants.PINTUAN_TIPS_ICON, isLeft);
        icons.add(buttonIcon);
        return icons;
    }

    private List<DealBuyBtnIcon> buildWeakActiveBtnIcons(DealCtx ctx) {
        List<DealBuyBtnIcon> icons = Lists.newArrayList();
        // 新用户团无tips，直接返回
//        if (ctx.getCostEffectivePinTuan().isLimitNewCustomJoin()) {
//            return icons;
//        }

        return DealBuyHelper.getWeakActivePinTuanTipsIcon(ctx);
    }

    private String getPinTuanBtnTitle(DealCtx ctx) {
        return ctx.getCostEffectivePinTuan().getGroupSuccCountMin() + PinTuanConstants.PINTUAN_TITLE_SUFFIX;
    }

    /**
     * 构建底bar横条
     * @param ctx
     */
    private void buildButtonBanner(DealCtx ctx) {
        if (CostEffectivePinTuanUtils.enhancedStyle(ctx)) {
            DealBuyBanner banner = new DealBuyBanner();
            banner.setBannerType(BannerTypeEnum.COMMON_TYPE.getType());
            banner.setShow(true);
            banner.setBackGroundColor(Lists.newArrayList("#FFF3F0", "#FFF3F0"));
            String newUserText = ctx.getCostEffectivePinTuan().isLimitNewCustomJoin() ? "新用户" : "好友";
            String bannerText = "邀请" + newUserText + "，一起拼团购买，享超低价！";
            banner.setContent(JsonLabelUtil.buildCostEffectivePinTuanBannerJson(bannerText));
            banner.setIconUrl(PlusIcons.COST_EFFECTIVE_PINTUAN_BANNER_ICON);
            banner.setCountDownTs(ctx.getCostEffectivePinTuan().getExpireTime());
            ctx.getBuyBar().setBuyBanner(banner);
        }
    }

    /**
     * 获取拼团场景跳链
     * @param ctx
     * @return
     */
    public String getRedirectUrl(DealCtx ctx, boolean isPinTuan) {
        DealBuyBtn buyBtn = buildOriginButton(ctx, StringUtils.EMPTY);
        String redirectUrl = buyBtn.getRedirectUrl();

        // 非拼团购买按钮直接返回，无需拼接额外参数
        if (!isPinTuan) {
            return redirectUrl;
        }
        if (StringUtils.isNotBlank(ctx.getCostEffectivePinTuan().getShareToken())) {
            redirectUrl += "&orderGroupId=" + ctx.getCostEffectivePinTuan().getShareToken();
        }

        if (ctx.getCostEffectivePinTuan().getPromotionId() > 0) {
            redirectUrl += "&promotionId=" + ctx.getCostEffectivePinTuan().getPromotionId();
        }

        if (StringUtils.isNotBlank(ctx.getCostEffectivePinTuan().getPinTuanActivityId())) {
            redirectUrl += "&pintuanActivityId=" + ctx.getCostEffectivePinTuan().getPinTuanActivityId();
        }

        redirectUrl += "&groupType=" + 2;
        return redirectUrl;
    }
}
