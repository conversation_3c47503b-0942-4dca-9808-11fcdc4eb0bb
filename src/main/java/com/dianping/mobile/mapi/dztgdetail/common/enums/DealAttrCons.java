package com.dianping.mobile.mapi.dztgdetail.common.enums;

import java.util.Arrays;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/6/22.
 */
public class DealAttrCons {
    // name
    //免预约要判断如下三个，每个团大只会有如下两个key中的任何一个，有一个是需要预约的就需要
    public static final String RESERVATION = "reservation_is_needed_or_not";
    public static final String RESERVATION_2 = "reservation_is_needed_or_not_2"; //"是"与"否"
    public static final String RESERVATION_3 = "reservation_is_needed_or_not_3";
    public static final String CATEGORY = "category";

    public static final String TORT = "tort";

    public static final String TORT_VALUE = "1";

    // value
    public static final String VALUE_NO = "否";
    public static final String VALUE_YES = "是";

    public static final String CHILDREN_PHOTO = "9017"; //亲子摄影
    public static final String CHILDREN_EDU = "9016"; //幼儿教育
    public static final String CHILDREN_PREGNANT_PHOTO = "10023";//孕妇写真

    //美甲美睫
    public static final String DEALDETAIL_BEAUTYTABLE_NEW = "dealdetail_beautytable_new";
    public static final String TAKE_TIME = "take-time";
    public static final String HOLD_TIME = "holdtime";
    public static final String PRE_SALE_TAG = "preSaleTag";
    public static final List<String> REQUIRE_ATTRIBUTES = Arrays.asList(
            RESERVATION,RESERVATION_2,RESERVATION_3,CATEGORY,TORT,PRE_SALE_TAG
    );

    public static final List<String> CATEGORY_LIST = Arrays.asList(
            CATEGORY
    );

    public static final List<String> BEAUTY_NAIL_LIST = Arrays.asList(
            DEALDETAIL_BEAUTYTABLE_NEW,
            TAKE_TIME,
            HOLD_TIME
    );

    // 关联spuid信息
    public static final String MTSS_REF_SPU_ID = "mtss_ref_spu_id";

    // spu类型
    public static final String SPU_SCENE_TYPE = "spuSceneType";
}