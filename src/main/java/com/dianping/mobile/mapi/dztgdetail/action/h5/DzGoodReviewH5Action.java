package com.dianping.mobile.mapi.dztgdetail.action.h5;

import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GoodReviewReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.GoodReviewPBO;
import com.dianping.mobile.mapi.dztgdetail.facade.GoodReviewFacade;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.meituan.servicecatalog.api.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

@InterfaceDoc(displayName = "到综团单好评度信息H5查询接口",
        type = "restful",
        description = "查询团单好评度信息：双平台来自不同的UGC服务，整个接口填充的数据也根据平台有所不同。",
        scenarios = "该接口仅适用于双平台APP站点的团购详情页及其他以到综团购为纬度的页面",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "qian.wang.sh"
)
@Controller("general/platform/dztgdetail/dzgoodreview.json")
@Action(url = "dzgoodreview.json", protocol = ReqProtocol.REST)
public class DzGoodReviewH5Action extends AbsAction<GoodReviewReq> {

    @Autowired
    private GoodReviewFacade goodReviewFacade;

    @Override
    protected IMobileResponse validate(GoodReviewReq request, IMobileContext context) {
        IdUpgradeUtils.processProductIdForGoodReviewReq(request, "dzgoodreview.json");
        if (request.getDealgroupid() <= 0) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "dzgoodreview.json",
            displayName = "查询到综团单好评度信息H5接口",
            description = "查询到综团单好评度信息：双平台来自不同的UGC服务，整个接口填充的数据也根据平台有所不同",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "dzgoodreview.json请求参数",
                            type = GoodReviewReq.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "result", description = "到综好评度picasso数据", type = GoodReviewPBO.class)},
            restExampleUrl = "https://mapi.51ping.com/general/platform/dztgdetail/dzgoodreview.json?dealgroupid=200153707",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    ),
            }
    )
    @Override
    protected IMobileResponse execute(GoodReviewReq request, IMobileContext context) {
        EnvCtx envCtx = initEnvCtxFromH5(context, false);
        GoodReviewPBO result =  goodReviewFacade.queryGoodReview(request, envCtx, context);
        return result != null ? new CommonMobileResponse(result) : Resps.SYSTEM_ERROR;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
