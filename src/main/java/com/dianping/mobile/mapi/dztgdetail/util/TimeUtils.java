package com.dianping.mobile.mapi.dztgdetail.util;

import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * Created by yangquan02 on 19/2/21.
 */
@Slf4j
public class TimeUtils {

    private static ThreadLocal<SimpleDateFormat> SIMPLE_DAY_FORMAT = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));
    private static ThreadLocal<SimpleDateFormat> SIMPLE_DAY_DOT_FORMAT = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy.MM.dd"));

    private static ThreadLocal<SimpleDateFormat> SIMPLE_MINUTE_DOT_FORMAT = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy.MM.dd HH:mm"));

    public static String getHourDateString(Date date) {
        long duration = (new Date().getTime() - date.getTime()) / (1000 * 60);
        String saleText = "";
        //时间段设置为24小时内，小于1小时情况下自动转换成多少分钟前有人购买过
        if (duration <= 0) {
            saleText = "1分钟前";
        } else if (duration < 60) {
            saleText = duration + "分钟前";
        } else if (duration < (24 * 60)) {
            saleText = duration / 60 + "小时前";
        }
        return saleText;
    }

    public static String getDateString(Date date) {
        if (date == null) {
            return "";
        }

        long time = date.getTime();
        time = time / 1000;

        Calendar cal = Calendar.getInstance();

        long now = cal.getTimeInMillis() / 1000;
        if (now - time < 60) {
            return "刚刚";
        }
        if (now - time < 3600) {
            return ((now - time) / 60) + "分钟前";
        }
        if (now - time < 86400) {
            return ((now - time) / 3600) + "小时前";
        }
        //若24小时≤X＜48小时——“1天前”
        //若48小时≤X＜72小时——“2天前”
        //若72小时≤X＜96小时——“3天前”
        if (now - time < 86400*4) {
            return ((now - time) / 86400) + "天前";
        }

        int nowYear = cal.get(Calendar.YEAR);

        cal.setTime(date);

        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH);
        int monthDay = cal.get(Calendar.DAY_OF_MONTH);

        if (year < nowYear) {
            return year + "年" + (month + 1) + "月" + monthDay + "日";
        }

        return (month + 1) + "月" + monthDay + "日";
    }

    public static String buildVideoTime(long time) {
        int seconds = (int) time % 60;
        int minutes = (int) time / 60;

        String second = seconds < 10 ? "0" + seconds : "" + seconds;
        String minute = minutes < 10 ? "0" + minutes : "" + minutes;

        return minute + ":" + second;
    }

    public static String convertDate2DayString(Date date) {
        if (date == null) {
            return null;
        }
        try {
            return SIMPLE_DAY_FORMAT.get().format(date);
        } catch (Exception e) {
            log.error("TimeUtils.convertString2Date error, date: {}", date, e);
        }
        return null;
    }

    public static String convertDate2DayDotString(Date date) {
        if (date == null) {
            return null;
        }
        try {
            return SIMPLE_DAY_DOT_FORMAT.get().format(date);
        } catch (Exception e) {
            log.error("TimeUtils.convertString2Date error, date: {}", date, e);
        }
        return null;
    }

    /**
     * 获取几天后
     */
    public static Date getFewDaysLaterBeginTime(Date date, int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DATE,calendar.get(Calendar.DATE) + days);
        return calendar.getTime();
    }

    public static Date getFewDaysBeforeDate(Date date, int days) {
        return getFewDaysLaterBeginTime(date, -days);
    }

    public static String convertDate2MinuteDotString(Date date) {
        if (date == null) {
            return null;
        }
        try {
            return SIMPLE_MINUTE_DOT_FORMAT.get().format(date);
        } catch (Exception e) {
            log.error("TimeUtils.convertDate2MinuteDotString error, date: {}", date, e);
        }
        return null;
    }
}
