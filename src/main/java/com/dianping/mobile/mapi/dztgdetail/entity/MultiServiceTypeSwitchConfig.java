package com.dianping.mobile.mapi.dztgdetail.entity;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 多服务类型的开关配置
 *
 * @auther: liweilong06
 * @date: 2023/12/28 7:27 下午
 */
@Data
public class MultiServiceTypeSwitchConfig implements Serializable {

    private boolean allServiceType;

    private List<String> serviceTypes;

    public boolean isMultiSku(String serviceType) {
        if (allServiceType) {
            return true;
        }
        return CollectionUtils.isNotEmpty(serviceTypes) && serviceTypes.contains(serviceType);
    }

}
