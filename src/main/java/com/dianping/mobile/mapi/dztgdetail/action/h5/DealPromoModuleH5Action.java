package com.dianping.mobile.mapi.dztgdetail.action.h5;

import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.base.datatypes.HttpCode;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealPromoModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo.DealPromoModule;
import com.dianping.mobile.mapi.dztgdetail.facade.DealPromoFacade;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.builder.ReflectionToStringBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

/**
 * Created by zuomlin on 2018/12/13.
 */
@InterfaceDoc(displayName = "到综团单促销模块H5接口",
        type = "restful",
        description = "查询到综团购模块促销信息接口：包括立减、红包分享、拼团、抵用券等等。",
        scenarios = "该接口仅适用于双平台APP站点的团购详情页的团单促销模块，其他以团购详情页维度请咨询项目owner",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "yangquan02"
)
@Controller("general/platform/dztgdetail/dealpromomodule.json")
@Action(url = "dealpromomodule.json", httpType = "get", protocol = ReqProtocol.REST)
public class DealPromoModuleH5Action extends AbsAction<DealPromoModuleReq> {

    @Autowired
    private DealPromoFacade dealPromoFacade;

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "dealpromomodule.json",
            displayName = "到综团单促销模块H5接口",
            description = "查询到综团购模块促销信息接口：包括立减、红包分享、拼团、抵用券等等。如果团单没有优惠，则后端不返回数据。",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "dealpromomodule.json请求参数",
                            type = DealPromoModuleReq.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "result", description = "团购picasso返回数据", type = DealPromoModule.class)},
            restExampleUrl = "http://mapi.51ping.com/general/platform/dztgdetail/dealpromomodule.json?" +
                    "dealgroupid=200139713&shopid=123&cityid=1&token=12344&clienttype=10010",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    ),
            }
    )
    @Override
    @CryptMethod
    protected IMobileResponse validate(DealPromoModuleReq request, IMobileContext iMobileContext) {
        IdUpgradeUtils.processProductIdForDealPromoModuleReq(request, "dealpromomodule.json");
        if (request == null || request.getDealGroupId() <= 0) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @Override
    protected IMobileResponse execute(DealPromoModuleReq request, IMobileContext iMobileContext) {
        try {
            EnvCtx envCtx = initEnvCtxFromH5(iMobileContext, true);
            envCtx.setClientType(request.getClientType());

            DealPromoModule dealPromoModule = dealPromoFacade.queryDealPromoModule(request, envCtx);
            if (dealPromoModule == null || dealPromoModule.getConcisePromoInfo() == null ||
                    CollectionUtils.isEmpty(dealPromoModule.getConcisePromoInfo().getPromoInfoItems())) {
                return new CommonMobileResponse(Resps.NoDataResp, HttpCode.HTTPOK);
            }
            return new CommonMobileResponse(dealPromoModule);
        } catch (Exception e) {
            logger.error(String.format("dealpromomodule.bin failed, params: request=%s, context=%s",
                    ReflectionToStringBuilder.toString(request), ReflectionToStringBuilder.toString(iMobileContext)), e);
        }
        return Resps.SYSTEM_ERROR;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
