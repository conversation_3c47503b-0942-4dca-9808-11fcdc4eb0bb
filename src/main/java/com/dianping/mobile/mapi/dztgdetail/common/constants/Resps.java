package com.dianping.mobile.mapi.dztgdetail.common.constants;

import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.base.datatypes.SimpleMsg;
import com.dianping.mobile.framework.base.datatypes.StatusCode;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import org.apache.commons.lang3.StringUtils;

public class Resps {

    public static final CommonMobileResponse PARAM_ERROR = new CommonMobileResponse(
            new SimpleMsg("提示", "参数非法", StatusCode.ACTIONEXCEPTION));

    public static final CommonMobileResponse SYSTEM_ERROR = new CommonMobileResponse(
            new SimpleMsg("提示", "系统异常，请稍后再试", StatusCode.ACTIONEXCEPTION));

    public static final CommonMobileResponse SYSTEM_BUSY = new CommonMobileResponse(
            new SimpleMsg("提示", "系统繁忙，请稍后再试", StatusCode.ACTIONEXCEPTION));

    public static final CommonMobileResponse COUPON_FAIL = new CommonMobileResponse(
            new SimpleMsg("提示", "领券失败", StatusCode.ACTIONEXCEPTION));

    public static final CommonMobileResponse USER_ERROR = new CommonMobileResponse(
            new SimpleMsg("提示", "用户未登录", StatusCode.ACTIONEXCEPTION));

    public static final CommonMobileResponse NoDataResp =
            new CommonMobileResponse(new SimpleMsg("提示", "无数据", StatusCode.ACTIONEXCEPTION));

    public static final CommonMobileResponse SERVER_ERROR = CommonMobileResponse.simpleMsg("提示", "服务器错误", StatusCode.ERROR);

    public static final CommonMobileResponse PRODUCT_ERROR = new CommonMobileResponse(
            new SimpleMsg("提示", "商品信息异常，请联系接口提供方协助排查", StatusCode.ACTIONEXCEPTION));

    public static CommonMobileResponse rhinoLimitError() {
        try {
            String rhinoLimitErrorText = Lion.getString(Environment.getAppName(), "com.sankuai.dzu.tpbase.dztgdetailweb.rhinoLimitErrorText");
            if (StringUtils.isEmpty(rhinoLimitErrorText)) {
                rhinoLimitErrorText = "系统繁忙，请稍后再试";
            }
            return new CommonMobileResponse(new SimpleMsg("提示", rhinoLimitErrorText, StatusCode.ACTIONEXCEPTION));
        } catch (Exception e) {
            Cat.logError(e);
            return SYSTEM_ERROR;
        }
    }

}
