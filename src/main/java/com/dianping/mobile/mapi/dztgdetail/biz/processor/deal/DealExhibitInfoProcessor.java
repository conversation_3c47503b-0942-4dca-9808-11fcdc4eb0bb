package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ContentSearchWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DigestQueryWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ImmersiveImageWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ReviewWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ExhibitImageItemVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImageTagVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImageUrlVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.*;
import com.dianping.mobile.mapi.dztgdetail.entity.*;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.util.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.mpmctcontent.application.thrift.dto.content.common.PicDTO;
import com.sankuai.mpmctcontent.application.thrift.dto.wed.WedPhotoCaseInfoDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.search.SearchDetailResponseDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;

import java.net.URLDecoder;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2023/8/29
 */
public class DealExhibitInfoProcessor extends AbsDealProcessor {

    @Autowired
    private ImmersiveImageWrapper immersiveImageWrapper;
    @Autowired
    private ContentSearchWrapper contentSearchWrapper;
    @Autowired
    private DigestQueryWrapper digestQueryWrapper;
    @Autowired
    private ReviewWrapper reviewWrapper;


    @Override
    public boolean isEnable(DealCtx ctx) {
        return LionConfigUtils.callExhibitInfo(ctx.getCategoryId());
    }

    @Override
    public void prepare(DealCtx ctx) {
        // 新款式接口
        if (LionConfigUtils.useNewExhibitCategoryIds(ctx.getCategoryId())) {
            QueryExhibitImageParam param = buildQueryExhibitImageParam(ctx);
            param.setShopId(ctx.getMtLongShopId()); // 新款式接口入参只接受mtShopId
            ctx.setQueryExhibitImageParam(param);
            ctx.getFutureCtx().setContentSearchFuture(contentSearchWrapper.getDealGroupStyleImageName(param));
        }

        String exhibitItemId = GsonUtils.getParamFromMapJson(ctx.getRequestExtParam(), "exhibitItemId", String.class);
        // 三级团购类目为美甲且入口为营销入口，则调用指定款式置顶接口
        if (isBeautyNailCubeEntry(ctx, exhibitItemId)) {
            Future sortedExhibitInfoFuture = immersiveImageWrapper.preGetSortedImmersiveImageWithOldId((long) ctx.getMtId(), exhibitItemId);
            Future exhibitTagInfoFuture = immersiveImageWrapper.preGetExhibitMetaTagsInfo();
            ctx.getFutureCtx().setSortedExhibitInfoFuture(sortedExhibitInfoFuture);
            ctx.getFutureCtx().setExhibitTagInfoFuture(exhibitTagInfoFuture);
            return;
        }

        //使用attr中的id查询款式信息查询
        if (LionConfigUtils.useAttributeKeyStyleCategoryIds(ctx.getCategoryId())) {
            List<String> ids = getPhotoStyleIds(ctx);
            Future attributeKeyFuture = digestQueryWrapper.getExhibitInfoFuture(ctx, ids);
            ctx.getFutureCtx().setAttributeKeyFuture(attributeKeyFuture);
            return;
        }

        if (LionConfigUtils.useAttributeKeyOldCaseCategoryIds(ctx.getCategoryId())) {
            List<String> ids = getOldCaseIds(ctx);
            Future oldCaseInfoFuture = digestQueryWrapper.getOldExhibitInfoFuture(ctx, ids);
            ctx.getFutureCtx().setOldCaseInfoFuture(oldCaseInfoFuture);
        }
        // 款式组件进入团详 504、910 个性写真、民族服饰 查询置顶款式信息，
        if (RequestSourceEnum.STYLE.getSource().equals(ctx.getRequestSource())) {
            Future topExhibitInfoFuture = digestQueryWrapper.getExhibitInfoFuture(ctx);
            ctx.getFutureCtx().setTopExhibitInfoFuture(topExhibitInfoFuture);
        }
        QueryExhibitImageParam param = buildQueryExhibitImageParam(ctx);
        Future exhibitInfoFuture = immersiveImageWrapper.preGetImmersiveImage(param, ctx.getEnvCtx());
        ctx.getFutureCtx().setExhibitInfoFuture(exhibitInfoFuture);
    }

    public QueryExhibitImageParam buildQueryExhibitImageParam(DealCtx ctx) {
        QueryExhibitImageParam param = QueryExhibitImageParam.builder()
                .categoryId(ctx.getCategoryId())
                .dpDealGroupId(ctx.getDpId())
                .start(0)
                .limit(10)
                .shopId(ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId())
                .serviceType(DealUtils.getServiceType(ctx))
                .infoContentId(ctx.getInfoContentId())
                .mtDealGroupId(ctx.getMtId())
                .build();
        // 如果是新穿戴甲，则查询33条款式数据
        Map<String, String> wearableNailStyleMap = LionConfigUtils.getWearableNailStyleConfig();
        if (isWearableNail(wearableNailStyleMap, ctx.getCategoryId(), DealUtils.getDealGroupServiceType(ctx))
                && DealUtils.isNewWearableNailDeal(ctx)) {
            Integer limit = Integer.parseInt(wearableNailStyleMap.getOrDefault("limit", "33"));
            param.setLimit(limit);
        }
        return param;
    }

    private boolean isWearableNail(Map<String, String> wearableNailStyleMap, int categoryId, String serviceType) {
        int allowCategoryId = Integer.parseInt(wearableNailStyleMap.getOrDefault("categoryId", "0"));
        String allowServiceType = wearableNailStyleMap.getOrDefault("serviceType", "");
        return Objects.equals(categoryId, allowCategoryId) && Objects.equals(serviceType, allowServiceType);
    }

    private boolean isBeautyNailCubeEntry(DealCtx ctx, String exhibitItemId) {
        // Lion开关控制是否启用新接口
        return LionConfigUtils.enableExhibitSourceBeautyNailTop() && DealUtils.isBeautyNailServiceTypeDeal(ctx) && DealCtxHelper.isCubeExhibitNail(ctx) && StringUtils.isNotBlank(exhibitItemId);
    }

    @Override
    public void process(DealCtx ctx) {
        Map<String, String> wearableNailStyleMap = LionConfigUtils.getWearableNailStyleConfig();
        String serviceType = DealUtils.getDealGroupServiceType(ctx);
        Map<String, ExhibitTextConfig> categoryExhibitTitleMap = Lion.getMap(LionConstants.APP_KEY, LionConstants.CATEGORY_EXHIBIT_TITLE_CONFIG, ExhibitTextConfig.class, Collections.EMPTY_MAP);
        if (MapUtils.isEmpty(categoryExhibitTitleMap)
                || !categoryExhibitTitleMap.containsKey(String.valueOf(ctx.getCategoryId()))) {
            return;
        }
        String exhibitTitleKey;
        String wearableNailServiceType = wearableNailStyleMap.getOrDefault("serviceType", "");
        if (Objects.equals(serviceType, wearableNailServiceType)) {
            exhibitTitleKey = String.format("%s.%s", ctx.getCategoryId(), serviceType);
        } else {
            exhibitTitleKey = String.valueOf(ctx.getCategoryId());
        }
        ExhibitTextConfig exhibitTextConfig = categoryExhibitTitleMap.get(exhibitTitleKey);
        // 从商家提供接口中查询款式信息
        if (LionConfigUtils.useNewExhibitCategoryIds(ctx.getCategoryId())) {
            handleNewExhibitCategory(ctx, exhibitTextConfig);
            return;
        }
        // 使用attr中去除id查询款式信息
        if (LionConfigUtils.useAttributeKeyStyleCategoryIds(ctx.getCategoryId())) {
            handleAttributeKeyStyleCategory(ctx, exhibitTextConfig);
            return;
        }

        if (LionConfigUtils.useAttributeKeyOldCaseCategoryIds(ctx.getCategoryId())) {
            handleOldCaseInfoCategory(ctx, exhibitTextConfig);
            return;
        }

        if (Objects.isNull(ctx.getFutureCtx().getExhibitInfoFuture()) && Objects.isNull(ctx.getFutureCtx().getSortedExhibitInfoFuture())) {
            return;
        }
        Long serviceTypeId = DealUtils.getDealGroupServiceTypeId(ctx);
        ImmersiveImageVO immersiveImageVO = getImmersiveImageData(ctx, serviceType, serviceTypeId, wearableNailStyleMap);
        if (Objects.isNull(immersiveImageVO)) {
            return;
        }

        ExhibitContentDTO exhibitContentDTO = new ExhibitContentDTO();
        exhibitContentDTO.setItems(immersiveImageVO.getItems());
        exhibitContentDTO.setTitle(exhibitTextConfig.getTitle());
        exhibitContentDTO.setItemDisplayStyle(immersiveImageVO.getItemDisplayStyle());
        exhibitContentDTO.setRecordCount(immersiveImageVO.getRecordCount());
        exhibitContentDTO.setShowAllText(exhibitTextConfig.getShowAllText());
        // 右箭头
        exhibitContentDTO.setArrowIcon(exhibitTextConfig.getArrowIcon());
        // 美甲类型
        exhibitContentDTO.setServiceTypeId(serviceTypeId);
        // 跳链
        String jumpUrlTemplate = ctx.isMt() ? exhibitTextConfig.getMtJumpUrl() : exhibitTextConfig.getDpJumpUrl();
        if (StringUtils.isNotBlank(jumpUrlTemplate)) {
            int dealGroupId = ctx.isMt() ? ctx.getMtId() : ctx.getDpId();
            long shopId = ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId();
            String jumpUrl = String.format(jumpUrlTemplate, dealGroupId, shopId);
            exhibitContentDTO.setJumpUrl(jumpUrl);
        }
        // 如果是新穿戴甲，主接口不返回常驻标签
        if (DealUtils.isNewWearableNailDeal(ctx)) {
            immersiveImageVO.getItems().forEach(itemVO -> {
                List<ImageTagVO> tags = itemVO.getTags().stream().filter(tag -> tag.getStyle() == 0)
                        .collect(Collectors.toList());
                itemVO.setTags(tags);
            });
            exhibitContentDTO.setRecordCount(immersiveImageVO.getRecordCount());
            String showText = String.format(exhibitTextConfig.getShowAllText(), immersiveImageVO.getRecordCount());
            exhibitContentDTO.setShowAllText(showText);
        }
        //判断是否要将跳链替换为新版(根据前端版本+指定类目)
        exhibitContentDTO = immersiveImageWrapper.jumpUrlToShopPage(ctx, exhibitContentDTO);
        addTopExhibitInfo(ctx, exhibitContentDTO);
        ctx.setExhibitContentDTO(exhibitContentDTO);
    }

    public void addTopExhibitInfo(DealCtx ctx, ExhibitContentDTO exhibitContentDTO) {
        if (!RequestSourceEnum.STYLE.getSource().equals(ctx.getRequestSource())) {
            // 只有从款式组件进入才有款式置顶
            return;
        }
        if (Objects.isNull(exhibitContentDTO) || CollectionUtils.isEmpty(exhibitContentDTO.getItems())) {
            return;
        }
        DigestMaterialInfoPhotoDTO materialInfoPhotoDTO = digestQueryWrapper.getSafeExhibitInfo(ctx.getFutureCtx().getTopExhibitInfoFuture(), String.valueOf(ctx.getInfoContentId()));
        ExhibitImageItemVO exhibitImageItemVO = convertTOExhibitImageItemVO(materialInfoPhotoDTO, exhibitContentDTO);
        if (Objects.nonNull(exhibitImageItemVO)) {
            List<ExhibitImageItemVO> items = exhibitContentDTO.getItems();
            items = items.stream().filter(e -> !String.valueOf(ctx.getInfoContentId()).equals(e.getItemId())).collect(Collectors.toList());
            items.add(0, exhibitImageItemVO);
            exhibitContentDTO.setItems(items);

        }
    }

    public ExhibitImageItemVO convertTOExhibitImageItemVO(DigestMaterialInfoPhotoDTO materialInfoPhotoDTO, ExhibitContentDTO exhibitContentDTO) {
        if (Objects.isNull(materialInfoPhotoDTO)) {
            return null;
        }
        ExhibitImageItemVO exhibitImageItemVO = new ExhibitImageItemVO();
        if (Objects.nonNull(exhibitContentDTO) && CollectionUtils.isNotEmpty(exhibitContentDTO.getItems())) {
            exhibitImageItemVO = exhibitContentDTO.getItems().get(0);
        }
        exhibitImageItemVO.setTags(getTags(materialInfoPhotoDTO));
        exhibitImageItemVO.setUrls(convertTOImageUrlVO(materialInfoPhotoDTO, getImageBestScale(exhibitContentDTO.getItems().get(0))));
        exhibitImageItemVO.setName(materialInfoPhotoDTO.getCaseName());
        exhibitImageItemVO.setItemId(String.valueOf(materialInfoPhotoDTO.getInfoContentId()));
        return exhibitImageItemVO;
    }

    public List<ImageTagVO> getTags(DigestMaterialInfoPhotoDTO materialInfoPhotoDTO) {
        List<String> photographyStyle = materialInfoPhotoDTO.getPhotographyStyle();
        if (CollectionUtils.isEmpty(photographyStyle)) {
            return null;
        }
        List<ImageTagVO> tags = Lists.newArrayList();
        photographyStyle.forEach(tag -> {
            ImageTagVO tagVO = new ImageTagVO();
            tagVO.setName(tag);
            tags.add(tagVO);
        });
        return tags;
    }

    public List<ImageTagVO> getOldCaseTags(WedPhotoCaseInfoDTO wedPhotoCaseInfoDTO) {
        List<String> photographyStyle = wedPhotoCaseInfoDTO.getPhotoStyle();
        if (CollectionUtils.isEmpty(photographyStyle)) {
            return null;
        }
        List<ImageTagVO> tags = Lists.newArrayList();
        photographyStyle.forEach(tag -> {
            ImageTagVO tagVO = new ImageTagVO();
            tagVO.setName(tag);
            tags.add(tagVO);
        });
        return tags;
    }

    public String getImageBestScale(ExhibitImageItemVO exhibitImageItemVO) {
        // 获取已有款式的 比例
        if (Objects.nonNull(exhibitImageItemVO)
                && CollectionUtils.isNotEmpty(exhibitImageItemVO.getUrls())
                && Objects.nonNull(exhibitImageItemVO.getUrls().get(0))) {
            return exhibitImageItemVO.getUrls().get(0).getImageBestScale();
        }
        return "3:4";
    }

    public List<ImageUrlVO> convertTOImageUrlVO(DigestMaterialInfoPhotoDTO materialInfoPhotoDT, String imageBestScale) {
        List<CasePicsDTO> casePics = materialInfoPhotoDT.getCasePics();

        List<ImageUrlVO> urls = Lists.newArrayList();
        // 设置款式封面
        ImageUrlVO cover = getStyleCover(materialInfoPhotoDT.getCaseCover(), imageBestScale);
        if (Objects.nonNull(cover)) {
            urls.add(cover);
        }
        if (CollectionUtils.isNotEmpty(casePics)) {
            casePics.forEach(casePicsDTO -> {
                ImageUrlVO imageUrlVO = ImageUrlVO.builder()
                        .url(casePicsDTO.getPicUrl())
                        .height(casePicsDTO.getPicHeight())
                        .width(casePicsDTO.getPicWidth())
                        .imageBestScale(imageBestScale)
                        .type(1)
                        .picSource(1).build();
                urls.add(imageUrlVO);
            });
        }
        return urls;
    }

    private ImageUrlVO getStyleCover(CaseCoverDTO caseCover, String imageBestScale) {
        if (Objects.isNull(caseCover)) {
            return null;
        }
        return ImageUrlVO.builder()
                .url(caseCover.getCoverUrl())
                .height(caseCover.getCoverHeight())
                .width(caseCover.getCoverWidth())
                .imageBestScale(imageBestScale)
                .type(1)
                .picSource(1).build();
    }

    public ImmersiveImageVO getImmersiveImageData(DealCtx ctx, String serviceType, Long serviceTypeId, Map<String, String> wearableNailStyleMap) {
        String exhibitItemId = GsonUtils.getParamFromMapJson(ctx.getRequestExtParam(), "exhibitItemId", String.class);
        // 三级团购类目为美甲且入口为营销入口，则获取排序后款式结果
        if (isBeautyNailCubeEntry(ctx, exhibitItemId)) {
            return getImmersiveImageWithTopItem(ctx);
        }
        // 查询穿戴甲款式
        if (isWearableNail(wearableNailStyleMap, ctx.getCategoryId(), serviceType)) {
            return getImmersiveImageWithDeals(ctx, serviceTypeId);
        }
        // 查询非穿戴甲款式
        return getImmersiveImageWithoutDeals(ctx);
    }

    /**
     * 查询穿戴甲款式数据
     *
     * @param ctx deal context
     *            serviceTypeId 服务类型id
     * @return immersive image vo
     */
    private ImmersiveImageVO getImmersiveImageWithDeals(DealCtx ctx, Long serviceTypeId) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealExhibitInfoProcessor.getImmersiveImageWithDeals(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx,java.lang.Long)");
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        List<DealGroupDealDTO> deals = dealGroupDTO.getDeals();
        int dealGroupId = ctx.isMt() ? ctx.getMtId() : ctx.getDpId();
        long shopId = ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId();
        DealGroupBasicDTO dealGroupBasicDTO = dealGroupDTO.getBasic();
        Integer status = Objects.isNull(dealGroupBasicDTO) ? -1 : dealGroupBasicDTO.getStatus();
        return immersiveImageWrapper.getImmersiveImageWithDeals(deals, ctx.getFutureCtx().getExhibitInfoFuture(),
                ctx.getCategoryId(), serviceTypeId, ctx.isMt(), (long) dealGroupId, shopId, status);
    }

    /**
     * 查询非穿戴甲数据
     *
     * @param ctx deal context
     * @return immersive image vo
     */
    private ImmersiveImageVO getImmersiveImageWithoutDeals(DealCtx ctx) {
        return immersiveImageWrapper.getImmersiveImage(ctx.getFutureCtx().getExhibitInfoFuture(),
                ctx.getCategoryId(), ctx.isMt());
    }

    /**
     * 查询指定款式置顶的款式数据，非穿戴甲
     *
     * @param ctx deal context
     * @return immersive image vo
     */
    private ImmersiveImageVO getImmersiveImageWithTopItem(DealCtx ctx) {
        return immersiveImageWrapper.getImmersiveImageWithTopItem(ctx.getFutureCtx().getSortedExhibitInfoFuture(),
                ctx.getFutureCtx().getExhibitTagInfoFuture(), ctx.getCategoryId(), ctx.isMt());
    }


    /**
     * 获取款式id列表（只有当style存在来源时去读取置顶的infoContentId）
     *
     * @param ctx
     * @return
     */
    public List<String> getPhotoStyleIds(DealCtx ctx) {
        // 获取图片风格ID
        List<String> ids = DealExhibitInfoProcessorUtils.getPhotoStyle(ctx.getAttrs());
        // 如果没有找到任何ID，返回空列表
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        // 检查请求来源是否为 STYLE
        if (RequestSourceEnum.STYLE.getSource().equals(ctx.getRequestSource()) && ctx.getInfoContentId() != null) {
            String infoContentId = ctx.getInfoContentId().toString();
            // 如果ids中包含infoContentId，则将其移到列表的开头
            if (ids.remove(infoContentId)) {
                ids.add(0, infoContentId);
            }
        }
        // 返回最多10个ID
        return ids.size() > 10 ? ids.subList(0, 10) : ids;
    }

    /**
     * 获取老案例id列表
     *
     * @param ctx
     * @return
     */
    public List<String> getOldCaseIds(DealCtx ctx) {
        // 获取图片风格ID
        List<String> ids = DealExhibitInfoProcessorUtils.getPhotoStyle(ctx.getAttrs());
        // 如果没有找到任何ID，返回空列表
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        // 返回最多10个ID
        return ids.size() > 10 ? ids.subList(0, 10) : ids;
    }



    /**
     * 获取款式列表
     *
     * @param ctx
     * @param ids
     * @return
     */
    public ExhibitContentDTO getExhibitInfo(DealCtx ctx, List<String> ids) {
        ExhibitContentDTO exhibitContentDTO = new ExhibitContentDTO();
        List<ExhibitImageItemVO> exhibitImageItemVOS = new ArrayList<>();
        int categoryId = ctx.getCategoryId();
        // 获取展品图像配置
        ExhibitImageConfig exhibitImageConfig = contentSearchWrapper.getExhibitImageConfig(categoryId);
        // 遍历 ids 列表，处理每个非空项
        for (String item : ids) {
            if (StringUtils.isNotBlank(item)) {
                DigestMaterialInfoPhotoDTO digestMaterialInfoPhotoDTO = digestQueryWrapper.getSafeExhibitInfo(ctx.getFutureCtx().getAttributeKeyFuture(), item);
                ExhibitImageItemVO exhibitImageItemVO = attrDataConvertExhibImageItemVo(digestMaterialInfoPhotoDTO, exhibitImageConfig);
                if (Objects.nonNull(exhibitImageItemVO)) {
                    exhibitImageItemVOS.add(exhibitImageItemVO);
                }
            }
        }
        // 设置记录数和展示样式
        exhibitContentDTO.setRecordCount(DealExhibitInfoProcessorUtils.getPhotoStyle(ctx.getAttrs()).size());
        exhibitContentDTO.setItemDisplayStyle(exhibitImageConfig != null ? exhibitImageConfig.getItemDisplayStyle() : 0);
        exhibitContentDTO.setItems(exhibitImageItemVOS);
        return exhibitContentDTO;
    }

    /**
     * 获取老版款式列表
     *
     * @param ctx
     * @param ids
     * @return
     */
    public ExhibitContentDTO getOldCaseInfo(DealCtx ctx, List<String> ids) {
        ExhibitContentDTO exhibitContentDTO = new ExhibitContentDTO();
        List<ExhibitImageItemVO> exhibitImageItemVOS = new ArrayList<>();
        int categoryId = ctx.getCategoryId();
        // 获取展品图像配置
        ExhibitImageConfig exhibitImageConfig = contentSearchWrapper.getExhibitImageConfig(categoryId);
        // 遍历 ids 列表，处理每个非空项
        for (String item : ids) {
            if (StringUtils.isNotBlank(item)) {
                WedPhotoCaseInfoDTO wedPhotoCaseInfoDTO = digestQueryWrapper.getSafeOldCaseInfo(ctx.getFutureCtx().getOldCaseInfoFuture(), item);
                ExhibitImageItemVO exhibitImageItemVO = oldCaseInfoConvertExhibitImageItemVo(wedPhotoCaseInfoDTO, exhibitImageConfig);
                if (Objects.nonNull(exhibitImageItemVO)) {
                    exhibitImageItemVOS.add(exhibitImageItemVO);
                }
            }
        }
        // 设置记录数和展示样式
        exhibitContentDTO.setRecordCount(DealExhibitInfoProcessorUtils.getPhotoStyle(ctx.getAttrs()).size());
        exhibitContentDTO.setItemDisplayStyle(exhibitImageConfig != null ? exhibitImageConfig.getItemDisplayStyle() : 0);
        exhibitContentDTO.setItems(exhibitImageItemVOS);
        return exhibitContentDTO;
    }


    public ExhibitImageItemVO attrDataConvertExhibImageItemVo(DigestMaterialInfoPhotoDTO materialInfoPhotoDTO, ExhibitImageConfig exhibitImageConfig ) {
        if (Objects.isNull(materialInfoPhotoDTO)) {
            return null;
        }
        ExhibitImageItemVO exhibitImageItemVO = new ExhibitImageItemVO();
        exhibitImageItemVO.setTags(getTags(materialInfoPhotoDTO));
        exhibitImageItemVO.setUrls(convertTOImageUrlCalculateProportionVO(materialInfoPhotoDTO, exhibitImageConfig.getDefaultImageBestScale()));
        exhibitImageItemVO.setName(materialInfoPhotoDTO.getCaseName());
        exhibitImageItemVO.setItemId(String.valueOf(materialInfoPhotoDTO.getInfoContentId()));
        return exhibitImageItemVO;
    }

    public ExhibitImageItemVO oldCaseInfoConvertExhibitImageItemVo(WedPhotoCaseInfoDTO wedPhotoCaseInfoDTO, ExhibitImageConfig exhibitImageConfig ) {
        if (Objects.isNull(wedPhotoCaseInfoDTO)) {
            return null;
        }
        ExhibitImageItemVO exhibitImageItemVO = new ExhibitImageItemVO();
        exhibitImageItemVO.setTags(getOldCaseTags(wedPhotoCaseInfoDTO));
        exhibitImageItemVO.setUrls(convertCaseInfoTOImageUrlCalculateProportionVO(wedPhotoCaseInfoDTO, exhibitImageConfig.getDefaultImageBestScale()));
        exhibitImageItemVO.setName(wedPhotoCaseInfoDTO.getCaseName());
        exhibitImageItemVO.setItemId(String.valueOf(wedPhotoCaseInfoDTO.getCaseId()));
        return exhibitImageItemVO;
    }


    public List<ImageUrlVO> convertTOImageUrlCalculateProportionVO(DigestMaterialInfoPhotoDTO materialInfoPhotoDT, String imageBestScale) {
        List<CasePicsDTO> casePics = materialInfoPhotoDT.getCasePics();
        List<ImageUrlVO> urls = Lists.newArrayList();
        // 设置款式封面
        ImageUrlVO cover = getStyleCover(materialInfoPhotoDT.getCaseCover(), imageBestScale);
        if (Objects.nonNull(cover)) {
            urls.add(cover);
        }
        if (CollectionUtils.isNotEmpty(casePics)) {
            casePics.forEach(casePicsDTO -> {
                ImageUrlVO imageUrlVO =  contentSearchWrapper.getImmersiveImageUrls(casePicsDTO.getPicUrl(), (long) casePicsDTO.getPicWidth(), (long) casePicsDTO.getPicHeight(),imageBestScale);
                urls.add(imageUrlVO);
            });
        }
        return urls;
    }

    public List<ImageUrlVO> convertCaseInfoTOImageUrlCalculateProportionVO(WedPhotoCaseInfoDTO wedPhotoCaseInfoDTO, String imageBestScale) {
        List<PicDTO> casePics = wedPhotoCaseInfoDTO.getCasePics();
        List<ImageUrlVO> urls = Lists.newArrayList();
        // 老案例没有封面图片
        if (CollectionUtils.isNotEmpty(casePics)) {
            casePics.forEach(picDTO -> {
                ImageUrlVO imageUrlVO =  contentSearchWrapper.getImmersiveImageUrls(picDTO.getUrl(), picDTO.getWidth() == null ? 0L : (long) picDTO.getWidth(),
                        picDTO.getHeight() == null ? 0L : (long) picDTO.getHeight(), imageBestScale);
                urls.add(imageUrlVO);
            });
        }
        return urls;
    }

    public void handleNewExhibitCategory(DealCtx ctx, ExhibitTextConfig exhibitTextConfig) {
        SearchDetailResponseDTO responseDTO = contentSearchWrapper.getFutureResult(ctx.getFutureCtx().getContentSearchFuture());
        ImmersiveImageVO immersiveImageVO = contentSearchWrapper.getImmersiveImageVO(responseDTO, null, ctx.getCategoryId(), ctx.getQueryExhibitImageParam());
        if (Objects.isNull(immersiveImageVO)) {
            return;
        }
        ExhibitContentDTO exhibitContentDTO = createExhibitContentDTO(immersiveImageVO, exhibitTextConfig);
        exhibitContentDTO = immersiveImageWrapper.jumpUrlToShopPage(ctx, exhibitContentDTO);
        ctx.setExhibitContentDTO(exhibitContentDTO);
    }

    public void handleAttributeKeyStyleCategory(DealCtx ctx, ExhibitTextConfig exhibitTextConfig) {
        List<String> ids = getPhotoStyleIds(ctx);
        ExhibitContentDTO exhibitContentDTO = getExhibitInfo(ctx, ids);
        exhibitContentDTO.setTitle(exhibitTextConfig.getTitle());
        exhibitContentDTO.setShowAllText(exhibitTextConfig.getShowAllText());
        exhibitContentDTO = immersiveImageWrapper.jumpUrlToShopPage(ctx, exhibitContentDTO);
        ctx.setExhibitContentDTO(exhibitContentDTO);
    }

    public void handleOldCaseInfoCategory(DealCtx ctx, ExhibitTextConfig exhibitTextConfig) {
        List<String> ids = getOldCaseIds(ctx);
        ExhibitContentDTO exhibitContentDTO = getOldCaseInfo(ctx, ids);
        exhibitContentDTO.setTitle(exhibitTextConfig.getTitle());
        exhibitContentDTO.setShowAllText(exhibitTextConfig.getShowAllText());
        exhibitContentDTO = immersiveImageWrapper.jumpUrlToOldCaseListPage(ctx, exhibitContentDTO);
        ctx.setExhibitContentDTO(exhibitContentDTO);
    }

    public ExhibitContentDTO createExhibitContentDTO(ImmersiveImageVO immersiveImageVO, ExhibitTextConfig exhibitTextConfig) {
        ExhibitContentDTO exhibitContentDTO = new ExhibitContentDTO();
        exhibitContentDTO.setItems(immersiveImageVO.getItems());
        exhibitContentDTO.setTitle(exhibitTextConfig.getTitle());
        exhibitContentDTO.setItemDisplayStyle(immersiveImageVO.getItemDisplayStyle());
        exhibitContentDTO.setRecordCount(immersiveImageVO.getRecordCount());
        exhibitContentDTO.setShowAllText(exhibitTextConfig.getShowAllText());
        return exhibitContentDTO;
    }

}