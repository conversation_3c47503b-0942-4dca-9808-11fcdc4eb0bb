package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.style;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.google.common.collect.Sets;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupIdBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.concurrent.Future;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2023/8/16
 * @since mapi-dztgdetail-web
 */
@Deprecated
public class ParallMapperProcessor extends AbsDealProcessor {

    @Autowired
    private QueryCenterWrapper queryCenterWrapper;

    @Autowired
    private DealGroupWrapper dealGroupWrapper;

    @Autowired
    private MapperWrapper mapperWrapper;

    //目前只有美团的请求，才需要做双平台ID的映射
    @Override
    public boolean isEnable(DealCtx ctx) {
        return ctx.getEnvCtx().isMt() && ctx.getMtId() > 0;
    }

    @Override
    public void prepare(DealCtx ctx) {
        if(GreyUtils.enableQueryCenterForMainApi(ctx)) {
            prepareByQueryCenter(ctx);
        } else {
            Future dealIdMapperFuture =  dealGroupWrapper.preDpDealGroupId(ctx.getMtId());
            int dpId = dealGroupWrapper.getDpDealGroupId(dealIdMapperFuture);
            ctx.setDpId(dpId);
        }
    }

    @Override
    public void process(DealCtx ctx) {
        QueryDealGroupListResponse dpIdResp = mapperWrapper.getFutureResult(ctx.getFutureCtx().getDpIdFuture());
        List<DealGroupDTO> dealGroupDTOList = dpIdResp.getData().getList();
        if (!dealGroupDTOList.isEmpty()) {
            ctx.setDpId(dealGroupDTOList.get(0).getDpDealGroupIdInt());
        } else {
            Cat.logEvent("IndexOutOfBounds", "dealGroupDTOList is empty");
        }
        ctx.setDpLongShopId(mapperWrapper.getDpShopIdByMtShopIdLong(ctx.getFutureCtx().getDpShopIdFuture()));
    }

    private void prepareByQueryCenter(DealCtx ctx) {
        int dealGroupId = ctx.isMt() ? ctx.getMtId() : ctx.getDpId();
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet((long)dealGroupId), ctx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP)
                .dealGroupId(DealGroupIdBuilder.builder().dpDealGroupId())
                .build();

        Future dpIdFuture = queryCenterWrapper.preDealGroupDTO(queryByDealGroupIdRequest);
        ctx.getFutureCtx().setDpIdFuture(dpIdFuture);


        Future shopIdMapperFuture = mapperWrapper.preDpShopIdByMtShopId(ctx.getMtLongShopId());
        ctx.getFutureCtx().setDpShopIdFuture(shopIdMapperFuture);

    }

}
