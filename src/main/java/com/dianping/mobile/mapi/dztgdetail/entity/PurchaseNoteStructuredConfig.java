package com.dianping.mobile.mapi.dztgdetail.entity;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Date: 2024/1/3
 */
@Data
public class PurchaseNoteStructuredConfig {
    /**
     *  灰度开关，0-关闭，1-灰度中，2-全量
     */
    private int grayLevel;

    /**
     * 客户端类型，1-美团app，2-美小，4-点评app，5-点小
     */
    private List<Integer> enableClientType;

    /**
     * 团单白名单
     */
    private List<Integer> dealGroupIds;

    /**
     * 类目实验配置
     */
    private Map<String, String> category2ExpId;

    /**
     * 是否请求非结构化购买须知内容，true-请求，false-不请求
     */
    private boolean requestUnStructuredContent;

    /**
     * 非结构化团单三级类目列表
     */
    private List<String> unstructuredServiceType;
}
