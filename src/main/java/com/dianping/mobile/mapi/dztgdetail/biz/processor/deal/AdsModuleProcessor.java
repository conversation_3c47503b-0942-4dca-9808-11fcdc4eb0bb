package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.base.dto.DealBaseDTO;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.stock.dto.ProductStock;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealStockWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealSelect;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DztgAdsModule;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.tuangou.dztg.bjwrapper.api.dto.MtDealDto;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.StockDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

public class AdsModuleProcessor extends AbsDealProcessor{
    @Autowired
    private DealStockWrapper dealStockWrapper;

    @Override
    public void prepare(DealCtx ctx) {
        if(GreyUtils.enableQueryCenterForMainApi(ctx)) {
            return;
        } else {
            DealGroupBaseDTO dealGroup = ctx.getDealGroupBase();
            if(dealGroup == null || dealGroup.getDeals() == null) {
                return;
            }
            List<Integer> dealIds = Lists.newArrayList();
            for(DealBaseDTO deal : dealGroup.getDeals()) {
                if(deal == null) {
                    continue;
                }
                dealIds.add(deal.getDealId());
            }
            Future dealStockListFuture = dealStockWrapper.preDealStocks(dealIds);
            ctx.getFutureCtx().setDealStockListFuture(dealStockListFuture);
        }
    }

    @Override
    public void process(DealCtx ctx) {
        if(GreyUtils.enableQueryCenterForMainApi(ctx)) {
            processByQueryCenter(ctx);
        } else {
            Map<Integer, ProductStock> dealStockMap = dealStockWrapper.getFutureResult(ctx.getFutureCtx().getDealStockListFuture());

            if(ctx.isMt()) {
                List<MtDealDto> mtDealDtoList = ctx.getMtDealDtoList();
                MtDealDto mtDealDto = CollectionUtils.isEmpty(mtDealDtoList) ? null : mtDealDtoList.get(0);

                // 广告模块
                DztgAdsModule adsModule = new DztgAdsModule();
                adsModule.setShopId(ctx.getMtLongShopId());
                adsModule.setMtDealGroupId(ctx.getMtId());
                ctx.setDztgAdsModule(adsModule);
                if(mtDealDto != null) {
                    adsModule.setChannel(mtDealDto.getChannel());
                    adsModule.setFrontPoiCates(mtDealDto.getFrontPoiCates());
                }
            } else {
                DztgAdsModule adsModule = new DztgAdsModule();

                DealGroupBaseDTO dealGroupBaseDTO = ctx.getDealGroupBase();
                List<DealSelect> dealList = Lists.newArrayList();
                if(dealGroupBaseDTO != null && CollectionUtils.isNotEmpty(dealGroupBaseDTO.getDeals()) && MapUtils.isNotEmpty(dealStockMap)){
                    for (DealBaseDTO dealBaseDto : dealGroupBaseDTO.getDeals()) {
                        // 为了用于销量/库存计算，已隐藏的套餐也会返回，所以要过滤掉
                        if (dealBaseDto.getDealStatus() == 0) {
                            continue;
                        }
                        DealSelect dealSelect = formatDealSelectNew(dealBaseDto, dealStockMap.get(dealBaseDto.getDealId()));
                        dealList.add(dealSelect);
                    }
                }
                long dealId = dealList.size() == 1 ? dealList.get(0).getId() : -1;
                List<AttributeDTO> attributeDtoList = ctx.getAttrs();
                List<Integer> frontCategoryIds = AttributeUtils.getCategoryIds(attributeDtoList);
                adsModule.setDealID(dealId);
                adsModule.setCategoryIds(frontCategoryIds);

                ctx.setDztgAdsModule(adsModule);
            }
        }
    }

    private void processByQueryCenter(DealCtx ctx) {

        Map<Integer, StockDTO> dealStockMap = getDealStockMap(ctx.getDealGroupDTO());

        if(ctx.isMt()) {
            List<MtDealDto> mtDealDtoList = ctx.getMtDealDtoList();
            MtDealDto mtDealDto = CollectionUtils.isEmpty(mtDealDtoList) ? null : mtDealDtoList.get(0);

            // 广告模块
            DztgAdsModule adsModule = new DztgAdsModule();
            adsModule.setShopId(ctx.getMtLongShopId());
            adsModule.setMtDealGroupId(ctx.getMtId());
            ctx.setDztgAdsModule(adsModule);
            if(mtDealDto != null) {
                adsModule.setChannel(mtDealDto.getChannel());
                adsModule.setFrontPoiCates(mtDealDto.getFrontPoiCates());
            }
        } else {
            DztgAdsModule adsModule = new DztgAdsModule();

            DealGroupBaseDTO dealGroupBaseDTO = ctx.getDealGroupBase();
            List<DealSelect> dealList = Lists.newArrayList();
            if(dealGroupBaseDTO != null && CollectionUtils.isNotEmpty(dealGroupBaseDTO.getDeals()) && MapUtils.isNotEmpty(dealStockMap)){
                for (DealBaseDTO dealBaseDto : dealGroupBaseDTO.getDeals()) {
                    // 为了用于销量/库存计算，已隐藏的套餐也会返回，所以要过滤掉
                    if (dealBaseDto.getDealStatus() == 0) {
                        continue;
                    }
                    DealSelect dealSelect = formatDealSelectNew(dealBaseDto, dealStockMap.get(dealBaseDto.getDealId()));
                    dealList.add(dealSelect);
                }
            }
            long dealId = dealList.size() == 1 ? dealList.get(0).getId() : -1;
            List<AttributeDTO> attributeDtoList = ctx.getAttrs();
            List<Integer> frontCategoryIds = AttributeUtils.getCategoryIds(attributeDtoList);
            adsModule.setDealID(dealId);
            adsModule.setCategoryIds(frontCategoryIds);

            ctx.setDztgAdsModule(adsModule);
        }
    }

    private Map<Integer, StockDTO> getDealStockMap(DealGroupDTO dealGroupDTO) {
        if(dealGroupDTO == null || CollectionUtils.isEmpty(dealGroupDTO.getDeals())) {
            return new HashMap<>();
        }
        Map<Integer, StockDTO> map = new HashMap<>();
        for(DealGroupDealDTO dealDTO : dealGroupDTO.getDeals()) {
            map.put(dealDTO.getDealIdInt(), dealDTO.getStock());
        }
        return map;
    }

    public DealSelect formatDealSelectNew(DealBaseDTO dealBaseDto, ProductStock stockDto) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AdsModuleProcessor.formatDealSelectNew(com.dianping.deal.base.dto.DealBaseDTO,com.dianping.deal.stock.dto.ProductStock)");
        DealSelect dealSelect = new DealSelect();
        if (dealBaseDto != null) {
            dealSelect.setId(dealBaseDto.getDealId());
            dealSelect.setTitle(dealBaseDto.getShortTitle());
            dealSelect.setGroupTitle(StringUtils.EMPTY);
            boolean deliver = dealBaseDto.getDeliverType() == 1;
            dealSelect.setDeliver(deliver);
            dealSelect.setDealType(deliver ? 2 : 1);
            dealSelect.setProvideInvoice(dealBaseDto.isProvideInvoice() ? 1 : 0);
            dealSelect.setPrice(dealBaseDto.getPrice());
        }

        if (stockDto != null) {
            dealSelect.setCount(stockDto.getDpSales());
            dealSelect.setMaxJoin(stockDto.getDpTotal());
            dealSelect.setStatus(stockDto.isDpSoldOut() ? DealSelect.DEAL_STATUS_SOLDOUT : DealSelect.DEAL_STATUS_AVAILABLE);
        }
        return dealSelect;
    }

    public DealSelect formatDealSelectNew(DealBaseDTO dealBaseDto, StockDTO stockDto) {
        DealSelect dealSelect = new DealSelect();
        if (dealBaseDto != null) {
            dealSelect.setId(dealBaseDto.getDealId());
            dealSelect.setTitle(dealBaseDto.getShortTitle());
            dealSelect.setGroupTitle(StringUtils.EMPTY);
            boolean deliver = dealBaseDto.getDeliverType() == 1;
            dealSelect.setDeliver(deliver);
            dealSelect.setDealType(deliver ? 2 : 1);
            dealSelect.setProvideInvoice(dealBaseDto.isProvideInvoice() ? 1 : 0);
            dealSelect.setPrice(dealBaseDto.getPrice());
        }

        if (stockDto != null) {
            dealSelect.setCount(stockDto.getDpSales());
            dealSelect.setMaxJoin(stockDto.getDpTotal());
            if(stockDto.getIsDpSoldOut() != null) {
                dealSelect.setStatus(stockDto.getIsDpSoldOut() ? DealSelect.DEAL_STATUS_SOLDOUT : DealSelect.DEAL_STATUS_AVAILABLE);
            }
        }
        return dealSelect;
    }
}
