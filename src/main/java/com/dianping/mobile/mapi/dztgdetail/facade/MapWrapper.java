package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.util.GeoUtil;
import com.dianping.poi.distance.dto.GeoPoint;
import com.sankuai.map.open.platform.api.MapOpenApiService;
import com.sankuai.map.open.platform.api.regeo.RegeoRequest;
import com.sankuai.map.open.platform.api.regeo.RegeoResponse;
import com.sankuai.map.open.platform.api.regeo.Regeocode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;


/**
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class MapWrapper {
    @Resource
    private MapOpenApiService.Iface mapOpenApiService;

    private static final String MAP_SERVICE_KEY_LION = "com.sankuai.dzu.tpbase.dztgdetailweb.map.service.key";

    private static final String MAP_SERVICE_KEY_DEFAULT = "m683419fb67c4ff89043807af09a2cdt";

    public String getUserAddress(boolean isDp, Double lng, Double lat) {
        if (isDp) {
            GeoPoint geoPoint = GeoUtil.transformGeo84To02(lng, lat);
            if (geoPoint == null) {
                return null;
            }
            lng = geoPoint.getLng();
            lat = geoPoint.getLat();
        }
        RegeoRequest regeoRequest = new RegeoRequest();
        //配置lion，线上线下不一样
        regeoRequest.setKey(Lion.getString(LionConstants.APP_KEY, MAP_SERVICE_KEY_LION, MAP_SERVICE_KEY_DEFAULT));
        regeoRequest.setLocation(new BigDecimal(String.valueOf(lng)).setScale(6, BigDecimal.ROUND_HALF_UP) + "," + new BigDecimal(String.valueOf(lat)).setScale(6, BigDecimal.ROUND_HALF_UP));
        try {
            RegeoResponse regeoResponse = mapOpenApiService.regeo(regeoRequest);
            if (regeoResponse == null || regeoResponse.status != 200 || CollectionUtils.isEmpty(regeoResponse.getRegeocode())) {
                return "";
            }
            Regeocode regeocode = regeoResponse.getRegeocode().stream().findFirst().orElse(new Regeocode());
            return regeocode.getFormatted_address();
        } catch (Exception e) {
            log.error("getUserAddress, lng:{}, lat:{}, exception", lng, lat, e);
            return "";
        }
    }
}
