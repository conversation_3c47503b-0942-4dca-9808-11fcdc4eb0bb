package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/15 3:45 下午
 */
@MobileDo(id = 0xd24c)
public class DealDetailStructAttrGroupVO implements Serializable {

    /**
     * 属性内容
     */
    @MobileDo.MobileField(key = 0xcce)
    private String content;

    /**
     * 属性列表
     */
    @MobileDo.MobileField(key = 0xbf2e)
    private List<CommonAttrsVO> attrList;


    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<CommonAttrsVO> getAttrList() {
        return attrList;
    }

    public void setAttrList(List<CommonAttrsVO> attrList) {
        this.attrList = attrList;
    }

}
