package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @description 体检团单详情页使用的合规信息，查询上单客户名称使用的接口
 * @since 2022/01/20 16:00
 **/
public class DealGroupCustomerProcessor extends AbsDealProcessor {

    @Resource
    private DealGroupWrapper dealGroupWrapper;

    private static final Logger LOGGER = LoggerFactory.getLogger(DealGroupCustomerProcessor.class);

    @Override
    public boolean isEnable(DealCtx ctx) {
        long dpShopId = ctx.getDpLongShopId();
        List<Long> grayShopids = Lion.getList(LionConstants.HEGUI_NOTICE_SHOPID_WHITELIST, Long.class, new ArrayList<>());

        int publishCategoryId = ctx.getCategoryId();
        return (!grayShopids.isEmpty() && grayShopids.contains(dpShopId)) && (publishCategoryId == 401);
    }

    @Override
    public void prepare(DealCtx ctx) {
        if(GreyUtils.enableQueryCenterForMainApi(ctx)) {
            return;
        } else {
            Future customerFuture = dealGroupWrapper.preCustomerDTO(ctx.getDpId());
            ctx.getFutureCtx().setCustomerFuture(customerFuture);
        }
    }

    @Override
    public void process(DealCtx ctx) {
        if(GreyUtils.enableQueryCenterForMainApi(ctx)) {
            if(ctx.getDealGroupDTO() != null && ctx.getDealGroupDTO().getCustomer() != null) {
                long customerId = ctx.getDealGroupDTO().getCustomer().getOriginCustomerId();
                ctx.setCustomerId(customerId);
            }
        } else {
            long customerId = dealGroupWrapper.getCustomerId(ctx.getDpId());
            ctx.setCustomerId(customerId);
        }
    }

}
