package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.config.InsuranceConfigDTO;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.TimesDealUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.dto.TypeHierarchyView;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/25
 */
public class DealCtxHelper {

    public static boolean isOdpSource(DealCtx ctx) {
        return RequestSourceEnum.ODP.getSource().equals(ctx.getRequestSource());
    }

    public static boolean isCubeExhibitNail(DealCtx ctx) {
        return RequestSourceEnum.CUBE_EXHIBIT_NAIL.getSource().equals(ctx.getRequestSource());
    }

    public static boolean isPreOrderDeal(DealCtx ctx) {
        if (Objects.isNull(ctx) || Objects.isNull(ctx.getEnvCtx())) {
            return false;
        }
        EnvCtx envCtx = ctx.getEnvCtx();
        // 当前仅作用于美团APP、点评APP
        // 标识和二级类目决定是否为强预订团单
        return envCtx.judgeMainApp()
                && RequestSourceEnum.PRE_ORDER_DEAL.getSource().equals(ctx.getRequestSource()) && LionConfigUtils.hitPreOrderDeal(ctx);
    }

    // 跑路赔 prd：团购次卡、且非连续包月
    public static boolean hitCompensationForRunningAway(DealCtx ctx){
        return TimesDealUtil.isTimesDeal(ctx.getDealGroupDTO())
                && !TimesDealUtil.isMonthlySubscription(ctx)
                && LionConfigUtils.hitCompensationForRunningAwayCategory(getPoiSecBackCateId(ctx));
    }

    /**
     * 获取商户后台类目树
     * @param ctx
     * @return
     */
    public static List<Integer> getPoiBackCateIdList(DealCtx ctx) {
        if (ctx.isMt()){
            MtPoiDTO mtPoiDTO = ctx.getMtPoiDTO();
            if (Objects.nonNull(mtPoiDTO) && CollectionUtils.isNotEmpty(mtPoiDTO.getTypeHierarchy())){
                // mt 拼装后台类目信息
                List<Integer> backCateIdList = mtPoiDTO.getTypeHierarchy()
                        .stream().map(TypeHierarchyView::getId).collect(Collectors.toList());
                Collections.reverse(backCateIdList);
                return backCateIdList;
            }
        }else {
            DpPoiDTO dpPoiDTO = ctx.getDpPoiDTO();
            // dp拼装后台类目信息
            if (Objects.nonNull(dpPoiDTO) && CollectionUtils.isNotEmpty(dpPoiDTO.getBackMainCategoryPath())) {
                List<Integer> backCateIdList = dpPoiDTO.getBackMainCategoryPath().stream()
                        .map(DpPoiBackCategoryDTO::getCategoryId).collect(Collectors.toList());
                return backCateIdList;
            }
        }
        return new ArrayList<>();
    }

    /**
     * 获取商户后台二级类目id
     * @param ctx
     * @return
     */
    public static int getPoiSecBackCateId(DealCtx ctx) {
        List<Integer> poiBackCateIdList = getPoiBackCateIdList(ctx);
        return CollectionUtils.size(poiBackCateIdList) > 1 ? poiBackCateIdList.get(1) : 0;
    }

    /**
     * 判断是否是放心种
     * @param ctx
     * @return
     */
    public static boolean isAssuredImplant(DealCtx ctx, InsuranceConfigDTO insuranceConfigDTO) {
        Boolean usePlatformHeadPic = ctx.getUsePlatformHeadPic();
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        DealGroupCategoryDTO category = dealGroupDTO.getCategory();
        // 查询商品标
        List<DealGroupTagDTO> dealGroupTagDTOList = dealGroupDTO.getTags();
        Set<Long> productTags = getProductTagIds(dealGroupTagDTOList);

        if (MapUtils.isEmpty(ctx.getDpShopId2TagsMap()) || CollectionUtils.isEmpty(ctx.getDpShopId2TagsMap().get(ctx.getDpLongShopId()))){
            return false;
        }
        // 门店标
        List<DisplayTagDto> displayTagDTOList = ctx.getDpShopId2TagsMap().get(ctx.getDpLongShopId());
        Set<Long> shopTags = getShopTagIds(displayTagDTOList);

        // 符合三个条件才能使用
        return Objects.nonNull(usePlatformHeadPic) && usePlatformHeadPic
                && checkByProductTag(insuranceConfigDTO, category, productTags)
                && checkByShopTag(insuranceConfigDTO, category, shopTags);
    }

    /**
     * 判断是否命中 放心种 的门店标和商品标
     * @param ctx
     * @param insuranceConfigDTO
     * @return
     */
    public static boolean hitAssuredImplantShopProductTag(DealCtx ctx, InsuranceConfigDTO insuranceConfigDTO) {
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        DealGroupCategoryDTO category = dealGroupDTO.getCategory();
        // 查询商品标
        List<DealGroupTagDTO> dealGroupTagDTOList = dealGroupDTO.getTags();
        Set<Long> productTags = getProductTagIds(dealGroupTagDTOList);

        // 门店标
        if (MapUtils.isEmpty(ctx.getDpShopId2TagsMap()) || CollectionUtils.isEmpty(ctx.getDpShopId2TagsMap().get(ctx.getDpLongShopId()))){
            return false;
        }
        List<DisplayTagDto> displayTagDTOList = ctx.getDpShopId2TagsMap().get(ctx.getDpLongShopId());
        Set<Long> shopTags = getShopTagIds(displayTagDTOList);

        // 命中 放心种 “门店标”和“商品标”
        return checkByProductTag(insuranceConfigDTO, category, productTags)
                && checkByShopTag(insuranceConfigDTO, category, shopTags);
    }

    // 检查商品标
    public static boolean checkByProductTag(InsuranceConfigDTO insuranceConfigDTO, DealGroupCategoryDTO productCategory, Set<Long> productTagIds) {
        if (Objects.isNull(insuranceConfigDTO) || MapUtils.isEmpty(insuranceConfigDTO.getProductTagConfigMap())) {
            return false;
        }
        List<Long> hitByCategory = getHitByCategory(insuranceConfigDTO.getProductTagConfigMap(), productCategory);
        // 如果当前类目没找到配置，默认的不检测当前配置，直接通过
        if (CollectionUtils.isEmpty(hitByCategory)) {
            return false;
        }
        // 查询到了当前类目配置了标签规则，并且与商品的标签存在任意一个匹配的
        return CollectionUtils.containsAny(hitByCategory, ObjectUtils.defaultIfNull(productTagIds, Lists.newArrayList()));
    }

    // 检查门店标
    public static boolean checkByShopTag(InsuranceConfigDTO insuranceConfigDTO, DealGroupCategoryDTO productCategory, Set<Long> shopTags) {
        if (Objects.isNull(insuranceConfigDTO) || MapUtils.isEmpty(insuranceConfigDTO.getShopTagConfigMap())) {
            return false;
        }
        // 如果当前类目没找到配置，默认的不简单当前配置，直接通过
        List<Long> hitByCategory = getHitByCategory(insuranceConfigDTO.getShopTagConfigMap(), productCategory);
        if (CollectionUtils.isEmpty(hitByCategory)) {
            return false;
        }
        // 查询到了当前类目配置了标签规则，并且与商品的标签存在任意一个匹配的
        return CollectionUtils.containsAny(hitByCategory, ObjectUtils.defaultIfNull(shopTags, Lists.newArrayList()));
    }


    public static Set<Long> getProductTagIds(List<DealGroupTagDTO> tags) {
        if (CollectionUtils.isEmpty(tags)) {
            return Sets.newHashSet();
        }
        return tags.stream()
                .filter(Objects::nonNull)
                .map(DealGroupTagDTO::getId).collect(Collectors.toSet());
    }

    public static Set<Long> getShopTagIds(List<DisplayTagDto> shopDisplayTagDtoList) {
        if (CollectionUtils.isEmpty(shopDisplayTagDtoList)) {
            return Sets.newHashSet();
        }
        return shopDisplayTagDtoList.stream()
                .filter(Objects::nonNull)
                .map(DisplayTagDto::getTagId)
                .collect(Collectors.toSet());
    }

    private static <T> T getHitByCategory(Map<String, T> map, DealGroupCategoryDTO productCategory) {
        if (productCategory == null || MapUtils.isEmpty(map)) {
            return null;
        }
        String categoryId = String.valueOf(productCategory.getCategoryId());
        String key = String.format("%s.%s", categoryId, productCategory.getServiceTypeId());
        if (map.containsKey(key)) {
            return map.get(key);
        } else if (map.containsKey(categoryId)) {
            return map.get(categoryId);
        }
        return null;
    }

}
