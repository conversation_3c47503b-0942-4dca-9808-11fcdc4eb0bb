package com.dianping.mobile.mapi.dztgdetail.datatype.resp.module;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;
import java.util.List;

@MobileDo(id = 0xfb2a)
public class DztgModuleAbConfig implements Serializable {
    /**
    * 打点信息
    */
    @JsonIgnore
    @MobileField(key = 0xddc1)
    private List<DztgAbConfig> configs;

    /**
    * 模块名字
    */
    @MobileField(key = 0x9e5e)
    private String key;

    public List<DztgAbConfig> getConfigs() {
        return configs;
    }

    public void setConfigs(List<DztgAbConfig> configs) {
        this.configs = configs;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }
}