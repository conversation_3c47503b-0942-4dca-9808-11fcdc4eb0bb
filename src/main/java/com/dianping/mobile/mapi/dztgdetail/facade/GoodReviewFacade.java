package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.biz.aspect.UrlDztgClientCheckAnnotation;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DetailTagService;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GoodReviewReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedModuleExtraReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.GoodReviewPBO;
import com.dianping.mobile.mapi.dztgdetail.exception.QueryCenterResultException;
import com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.dianping.reviewremote.remote.dto.ReviewStarDistributionDTO;
import com.dianping.ugc.review.remote.dto.ReviewCount;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupCategoryBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Future;


@Component
@Slf4j
public class GoodReviewFacade {

    @Autowired
    private ReviewWrapper reviewWrapper;
    @Autowired
    private MapperWrapper mapperWrapper;
    @Resource
    private SwanWrapper swanWrapper;
    @Resource
    private DealGroupWrapper dealGroupWrapper;
    @Autowired
    @Qualifier("queryCenterDealGroupQueryService")
    private DealGroupQueryService queryCenterDealGroupQueryService;

    @Resource
    private DouHuBiz douHuBiz;
    @Resource
    private DouHuService douHuService;
    @Resource
    private DetailTagService detailTagService;

    private static final int HYD_CATEGORY = 502;
    private static final int SWAN_BIZ_TYPE = 1070;
    private static final String SWAN_QUERY_KEY = "liren_meijia_huanyuandu_product_d";
    private static final String DEAL_NOTICE = "团购须知";
    private static final String DEAL_REVIEW = "网友评价";
    private static final String GOOD_REVIEW = "goodreview";
    private static final String NO_REVIEW = "noreview";

    @UrlDztgClientCheckAnnotation
    public GoodReviewPBO queryGoodReview(GoodReviewReq req, EnvCtx envCtx, IMobileContext iMobileContext) {
        if(GreyUtils.isQueryCenterGreyBatch1(req.getDealgroupid())) {
            try {
                return queryGoodReviewFromQueryCenter(req, envCtx, iMobileContext);
            } catch (Exception e) {
                 log.error("queryGoodReviewFromQueryCenter error, ", e);
            }
        }
        if (envCtx.isMt()) {
            return queryMtGoodReview(req, envCtx);
        }

        return queryDpGoodReview(req, envCtx);
    }
    /**
     * 判断是否需要出现一句话评价
     * 一句话评价出现的条件：1.当"网友评价"Tab位于最上方时，需要隐藏一句话评价
     * @param req
     * @param envCtx
     * @param iMobileContext
     * @return
     */
    private Boolean hideGoodReview(GoodReviewReq req, EnvCtx envCtx, IMobileContext iMobileContext){
        try {
            if (detailTagService.reviewIsTopTab(convertToUnifiedModuleExtraReq(req), envCtx, iMobileContext)){
                // 团详框架改版, "网友评价tap"在"团购详情tap"上方，此时隐藏一句话评价
                return Boolean.TRUE;
            }
        } catch (Exception e) {
            log.error("queryUnifiedModuleExtraDTO error", e);
        }
        return  Boolean.FALSE;// 不隐藏
    }

    private UnifiedModuleExtraReq convertToUnifiedModuleExtraReq(GoodReviewReq req){
        UnifiedModuleExtraReq unifiedModuleExtraReq = new UnifiedModuleExtraReq();
        unifiedModuleExtraReq.setDealGroupId(req.getDealgroupid());
        unifiedModuleExtraReq.setExpResults("");
        unifiedModuleExtraReq.setCityId(-1);
        unifiedModuleExtraReq.setMrnVersion(req.getMrnVersion());
        return unifiedModuleExtraReq;
    }
    private GoodReviewPBO queryGoodReviewFromQueryCenter(GoodReviewReq req, EnvCtx envCtx, IMobileContext iMobileContext) throws TException {
        QueryDealGroupListResponse queryDealGroupListResponse = null;
        Set<Long> set = new HashSet<>();
        set.add(Long.valueOf(req.getDealgroupid()));
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(set, envCtx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP)
                .category(DealGroupCategoryBuilder.builder().categoryId())
                .build();

        queryDealGroupListResponse = queryCenterDealGroupQueryService.queryByDealGroupIds(queryByDealGroupIdRequest);

        if(queryDealGroupListResponse == null) {
            throw new QueryCenterResultException("queryCenter returns null, request : " + GsonUtils.toJsonString(queryByDealGroupIdRequest));
        }

        if(queryDealGroupListResponse.getCode() != 0) {
            throw new QueryCenterResultException("queryCenter not success, request : " + GsonUtils.toJsonString(queryByDealGroupIdRequest));
        }

        if(CollectionUtils.isEmpty(queryDealGroupListResponse.getData().getList())) {
            throw new QueryCenterResultException("queryDealGroupListResponse.getData().getList() is empty, request : " + GsonUtils.toJsonString(queryByDealGroupIdRequest));
        }

        if(queryDealGroupListResponse.getData().getList().get(0) == null) {
            throw new QueryCenterResultException("queryDealGroupListResponse.getData().getList().get(0) is null, request : " + GsonUtils.toJsonString(queryByDealGroupIdRequest));
        }

        DealGroupDTO dealGroupDTO = queryDealGroupListResponse.getData().getList().get(0);
        Long publishCategory;
        if (dealGroupDTO.getCategory() != null) {
            publishCategory = dealGroupDTO.getCategory().getCategoryId();
        } else {
            publishCategory = 0L;
        }

        int intPublishCategory = publishCategory == null ? 0 : publishCategory.intValue();

        // 团详框架改版AB实验，判断是否需要隐藏
        ModuleAbConfig moduleAbConfig = douHuService.enableCardStyleV2(envCtx, intPublishCategory, req.getMrnVersion());
        Boolean cardStyleV2 = douHuService.hitEnableCardStyleV2(moduleAbConfig);
        if (cardStyleV2){
            // 团详框架改版，如果"网友评价"Tab处于tab页顶部 需要隐藏 "一句话评价"
            if (hideGoodReview(req, envCtx, iMobileContext)){
                return new GoodReviewPBO();
            }
        }

        if (envCtx.isMt()) {
            return queryMtGoodReview(req, envCtx, intPublishCategory);
        }

        return queryDpGoodReview(req, envCtx, intPublishCategory);

    }


    public GoodReviewPBO queryDpGoodReview(GoodReviewReq req, EnvCtx envCtx) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.GoodReviewFacade.queryDpGoodReview(GoodReviewReq,EnvCtx)");
        GoodReviewPBO result = new GoodReviewPBO();
        Future categoryFuture = dealGroupWrapper.preDealGroupPublishCategoryById(req.getDealgroupid());
        Future reviewStarFuture = reviewWrapper.preReviewStarByReferIds(req.getDealgroupid());

        int categoryId = dealGroupWrapper.getFutureResult(categoryFuture);
        ReviewStarDistributionDTO reviewStar = reviewWrapper.getReviewStar(reviewStarFuture);
        if (reviewStar == null) {
            return result;
        }
        int goodReview = reviewStar.getStar4Count() + reviewStar.getFourHalfStarCount() + reviewStar.getStar5Count();
        boolean isHydSwitch = isHydDouHuMatch(envCtx.getUnionId(), false);

        result.setGoodReviewRatio(getReviewRatio(goodReview, reviewStar.getReviewCount()));

        if (reviewStar.getReviewCount() != 0) {
            result.setTotalReviewDesc(getTotalReviewDesc(reviewStar.getReviewCount(), categoryId, req.getDealgroupid(), isHydSwitch, "DP"));
            result.setRedirectUrl(UrlHelper.getGoodReviewUrl(req.getDealgroupid(), envCtx.getClientType()));
        }

        return result;
    }

    public GoodReviewPBO queryDpGoodReview(GoodReviewReq req, EnvCtx envCtx, int categoryId) {
        GoodReviewPBO result = new GoodReviewPBO();
        Future reviewStarFuture = reviewWrapper.preReviewStarByReferIds(req.getDealgroupid());

        ReviewStarDistributionDTO reviewStar = reviewWrapper.getReviewStar(reviewStarFuture);
        if (reviewStar == null) {
            return result;
        }
        int goodReview = reviewStar.getStar4Count() + reviewStar.getFourHalfStarCount() + reviewStar.getStar5Count();
        boolean isHydSwitch = isHydDouHuMatch(envCtx.getUnionId(), false);

        result.setGoodReviewRatio(getReviewRatio(goodReview, reviewStar.getReviewCount()));

        if (reviewStar.getReviewCount() != 0) {
            result.setTotalReviewDesc(getTotalReviewDesc(reviewStar.getReviewCount(), categoryId, req.getDealgroupid(), isHydSwitch, "DP"));
            result.setRedirectUrl(UrlHelper.getGoodReviewUrl(req.getDealgroupid(), envCtx.getClientType()));
        }

        result.setReviewPhrase(getReviewPhrase(req.getPoiid(), req.getDealgroupid()));
        result.setUserIcons(getUserIcons());
        return result;
    }

    private static String getReviewRatio(int haoping, int totalcount) {
        if (totalcount <= 0) {
            return Strings.EMPTY;
        }
        double ret = haoping * 100.0 / totalcount;
        return Math.round(ret) + "%";
    }

    public GoodReviewPBO queryMtGoodReview(GoodReviewReq req, EnvCtx envCtx) {
        GoodReviewPBO result = new GoodReviewPBO();

        int dpDealGroupId = dealGroupWrapper.getDpDealGroupId(req.getDealgroupid());
        Future categoryFuture = dealGroupWrapper.preDealGroupPublishCategoryById(dpDealGroupId);
        Future mtReviewFuture = reviewWrapper.preReviewCountByDealIds(req.getDealgroupid());

        Integer categoryId = dealGroupWrapper.getFutureResult(categoryFuture);
        if (categoryId == null) {
            return result;
        }
        ReviewCount mtReviewCount = reviewWrapper.getReviewCount(req.getDealgroupid(), mtReviewFuture);
        if (mtReviewCount == null) {
            return result;
        }
        boolean isHydSwitch = isHydDouHuMatch(envCtx.getUnionId(), true);

        // 团单评价数量为0时隐藏评价数量以及评价跳链
        if (mtReviewCount.getAll() != 0) {
            result.setTotalReviewDesc(getTotalReviewDesc(mtReviewCount.getAll(), categoryId, req.getDealgroupid(), isHydSwitch, "MT"));
            result.setRedirectUrl(UrlHelper.getGoodReviewUrl(req.getDealgroupid(), envCtx.getClientType()));
        }

        int avgRate = mtReviewCount.getAvgRate();
        if (avgRate > 0) {
            result.setReviewScore(avgRate * 1.0 / 10);
        }

        String dpPoiId = getDpPoiIdByMtPoiId(req.getPoiid());
        result.setReviewPhrase(getReviewPhrase(dpPoiId, req.getDealgroupid()));
        result.setUserIcons(getUserIcons());
        return result;
    }

    public GoodReviewPBO queryMtGoodReview(GoodReviewReq req, EnvCtx envCtx, int categoryId) {
        GoodReviewPBO result = new GoodReviewPBO();
        Future mtReviewFuture = reviewWrapper.preReviewCountByDealIds(req.getDealgroupid());

        ReviewCount mtReviewCount = reviewWrapper.getReviewCount(req.getDealgroupid(), mtReviewFuture);
        if (mtReviewCount == null) {
            return result;
        }
        // 统计好评数量 40、45、50分为好评
        int goodReviewCount = mtReviewCount.getStars().get(50) + mtReviewCount.getStars().get(40) + mtReviewCount.getStars().get(45) ;
        String reviewRatio = getReviewRatio(goodReviewCount, mtReviewCount.getAll());
        result.setGoodReviewRatio(reviewRatio);
        boolean isHydSwitch = isHydDouHuMatch(envCtx.getUnionId(), true);
        if (mtReviewCount.getAll() != 0) {
            result.setTotalReviewDesc(getTotalReviewDesc(mtReviewCount.getAll(), categoryId, req.getDealgroupid(), isHydSwitch, "MT"));
            result.setRedirectUrl(UrlHelper.getGoodReviewUrl(req.getDealgroupid(), envCtx.getClientType()));
        }
        int avgRate = mtReviewCount.getAvgRate();
        if (avgRate > 0) {
            result.setReviewScore(avgRate * 1.0 / 10);
        }


        String dpPoiId = getDpPoiIdByMtPoiId(req.getPoiid());
        result.setReviewPhrase(getReviewPhrase(dpPoiId, req.getDealgroupid()));
        result.setUserIcons(getUserIcons());
        return result;
    }

    private String getTotalReviewDesc(int count, int categoryId, int dealGroupId, boolean isHydSwitch, String platform) {
        if (categoryId == HYD_CATEGORY && isHydSwitch) {
            int hydCount = getHydCount(dealGroupId, platform);
            if (hydCount == 0) {
                return String.format("共%s个消费评价", count);
            }
            int finalHydCount = Math.min(count, hydCount);
            return String.format("共%s个评价，%s个提及还原度", count, finalHydCount);
        }
        return String.format("共%s个消费评价", count);
    }

    private int getHydCount(int dealGroupId, String platform) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("product_id", dealGroupId);
        params.put("platform", platform);
        Map<String, Object> swanResult = swanWrapper.swanQuery(SWAN_BIZ_TYPE, SWAN_QUERY_KEY, params);
        if (MapUtils.isEmpty(swanResult)) {
            return 0;
        }
        return (int) swanResult.getOrDefault("hyd_review_cnt", 0);
    }

    private boolean isHydDouHuMatch(String unionId, boolean isMt) {
        ModuleAbConfig moduleAbConfig = douHuBiz.getAbByUnionId(unionId, isMt ? "MtBeautyUgcCountExp": "DpBeautyUgcCountExp", isMt);
        if (moduleAbConfig != null && CollectionUtils.isNotEmpty(moduleAbConfig.getConfigs())) {
            String expResult = moduleAbConfig.getConfigs().get(0).getExpResult();
            return expResult.endsWith("a");
        }
        return false;
    }

    String getDpPoiIdByMtPoiId(String mtPoiId) {
        if(!StringUtils.isNumeric(mtPoiId)) {
            return "";
        }
        Long dpPoiId = mapperWrapper.getDpByMtShopId(Long.valueOf(mtPoiId));
        if(dpPoiId == null) {
            return "";
        }
        return String.valueOf(dpPoiId);
    }

    private String getReviewPhrase(String dpShopId, Integer dealGroupId) {
        if(StringUtils.isBlank(dpShopId)) {
            return null;
        }
        List<String> reviewPhraseList = swanWrapper.getReviewPhraseList(dpShopId);
        if (reviewPhraseList == null || reviewPhraseList.isEmpty()) {
            return null;
        }
        int index = 0;
        if (dealGroupId != null) {
            index = dealGroupId % reviewPhraseList.size();
        }
        return '"' + reviewPhraseList.get(index) + '"' ;
    }

    private List<String> getUserIcons() {
        List<String> allUserIcons = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb.review.user.icons",
                String.class, Lists.newArrayList("https://p0.meituan.net/travelcube/34c81dbcfc46b64d8148edfe8d7a8d5e7896.png"));
        List<String> randomUserIcons = new ArrayList<>();
        if (allUserIcons != null && !allUserIcons.isEmpty()) {
            int randomIndex = (int) (Math.random() * allUserIcons.size());
            randomUserIcons.add(allUserIcons.get(randomIndex));
        }
        return randomUserIcons;
    }

}
