package com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail;

import com.dianping.cat.Cat;
import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.common.datatypes.ResultList;
import lombok.Data;

/**
 * Description:
 * Author: lijie
 * Version: 0.0.1
 * Created: 16/5/10 下午3:56
 */
@MobileDo(id = 0xa21e)
@Data
public class ViewListDo extends ResultList<ViewItemDo> {

    @Override
    public void setRecordCount(int recordCount) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.ViewListDo.setRecordCount(int)");
        super.setRecordCount(recordCount);
    }

    @Override
    public void setStartIndex(int startIndex) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.ViewListDo.setStartIndex(int)");
        super.setStartIndex(startIndex);
    }

    @Override
    public void setIsEnd(boolean isEnd) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.ViewListDo.setIsEnd(boolean)");
        super.setIsEnd(isEnd);
    }

}
