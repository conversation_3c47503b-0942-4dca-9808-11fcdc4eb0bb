package com.dianping.mobile.mapi.dztgdetail.biz.processor.productdetail;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.dianping.mobile.mapi.dztgdetail.util.CommonModuleUtil;
import com.dianping.mobile.mapi.dztgdetail.util.DealVersionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class ProductStructDetailConfigProcessor extends AbsDealProcessor {

    public static final String MT_STRUCT_DETAIL_KEY = "dealdetail_gc_packagedetail";
    public static final String DP_STRUCT_DETAIL_KEY = "tuandeal_gc_packagedetail";
    public static final String NEW_STRUCT_DETAIL_VALUE = "unified_structured_module";

    @Override
    public void prepare(DealCtx ctx) {
    }

    @Override
    public void process(DealCtx ctx) {
        List<String> moduleKeys = CommonModuleUtil.getModuleKeys(ctx.getDealGroupDTO(), ctx.getEnvCtx());
        if (DealVersionUtils.isOldMetaVersion(ctx.getDealGroupDTO(), LionConstants.COMMON_MODULE_CALL_VERSION_CONFIG)) {
            return;
        }
        if (CollectionUtils.isEmpty(moduleKeys) || !moduleKeys.contains("module_detail_structured_detail")) {
            return;
        }
        // 老团详使用统一商品的结构化团详
        replaceStructDetailConfig(ctx);
    }

    private void replaceStructDetailConfig(DealCtx ctx) {
        // 将ModuleConfigs中key=STRUCT_DETAIL_KEY替换成NEW_STRUCT_DETAIL_VALUE，如果不存在key=STRUCT_DETAIL_KEY，则追加
        if (ctx.getModuleConfigsModule() == null) {
            return;
        }

        List<ModuleConfigDo> moduleConfigs = ctx.getModuleConfigsModule().getModuleConfigs();
        if (moduleConfigs == null) {
            moduleConfigs = new ArrayList<>();
            ctx.getModuleConfigsModule().setModuleConfigs(moduleConfigs);
        }

        // 查找是否存在STRUCT_DETAIL_KEY的配置
        String structKey = getStructDetailKey(ctx);
        boolean found = false;
        for (ModuleConfigDo config : moduleConfigs) {
            if (structKey.equals(config.getKey())) {
                // 找到key，更新value
                config.setValue(NEW_STRUCT_DETAIL_VALUE);
                found = true;
                break;
            }
        }

        // 如果没找到，追加新的配置
        if (!found) {
            ModuleConfigDo newConfig = new ModuleConfigDo();
            newConfig.setKey(structKey);
            newConfig.setValue(NEW_STRUCT_DETAIL_VALUE);
            moduleConfigs.add(newConfig);
        }
    }

    private String getStructDetailKey(DealCtx ctx) {
        return ctx.isMt() ? MT_STRUCT_DETAIL_KEY : DP_STRUCT_DETAIL_KEY;
    }
}
