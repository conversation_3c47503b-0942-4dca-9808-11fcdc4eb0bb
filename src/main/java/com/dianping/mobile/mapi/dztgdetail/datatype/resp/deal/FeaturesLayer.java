package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@TypeDoc(description = "保障浮层")
@MobileDo(id = 0x4721)
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class FeaturesLayer implements Serializable {

    @FieldDoc(description = "浮层标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "价保标签")
    @MobileDo.MobileField(key = 0xcf30)
    private String priceProtectionTag;

    @FieldDoc(description = "价保项")
    @MobileDo.MobileField(key = 0x479f)
    private List<LayerConfig> layerConfigs;
}
