package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealStyles;
import com.dianping.mobile.mapi.dztgdetail.common.constants.ScrumDealConstants;
import com.dianping.mobile.mapi.dztgdetail.common.constants.TuanCommonConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DealStyle;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DealStyleRequest;
import com.dianping.scrum.util.LionConfigUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DealStyleUtil {
    private static final String VERSION_BUYBARDOWN = "7.9.6";
    private static final Version VERSION_DOWN = new Version(VERSION_BUYBARDOWN);
    private static final Version VERSION_EDU = new Version("8.0.8");
    private static final Version VERSION_GC = new Version("8.1.4");
    private static final Version VERSION_BABY = new Version("8.1.6");
    private static final Version VERSION_FOOD = new Version("9.0.0");
    private static final Version VERSION_FLOWER = new Version("9.0.2");
    private static final Version VERSION_WEDDING = new Version("9.2.6");
    private static final Version VERSION_BACK_ROOM = new Version("9.0.8");

    @Deprecated
    public static DealStyle getDealStyle(List<Integer> categoryIds, String versionNo) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.util.DealStyleUtil.getDealStyle(java.util.List,java.lang.String)");
        return getDealStyle(categoryIds, isBuyBarDownVersion(versionNo));
    }

    @Deprecated
    public static DealStyle getDealStyle(List<Integer> categoryIds, String versionNo, boolean isBuyBarDown) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.util.DealStyleUtil.getDealStyle(java.util.List,java.lang.String,boolean)");
        return getDealStyle(categoryIds, isBuyBarDown || isBuyBarDownVersion(versionNo));
    }

    private static boolean isBuyBarDownVersion(String versionNo) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.util.DealStyleUtil.isBuyBarDownVersion(java.lang.String)");
        if (StringUtils.isNotBlank(versionNo)
                && (compareTo(new Version(versionNo), VERSION_DOWN) >= 0)) {
            return true;
        }
        return false;
    }

    private static boolean isBuyBarDown(Version version) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.util.DealStyleUtil.isBuyBarDown(com.dianping.mobile.mapi.dztgdetail.util.Version)");
        return compareTo(version, VERSION_DOWN) >= 0;
    }

    @Deprecated
    public static DealStyle getDealStyle(List<Integer> categoryIds, boolean isBuyBarDown) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.util.DealStyleUtil.getDealStyle(java.util.List,boolean)");
        if (CollectionUtils.isEmpty(categoryIds)) {
            return null;
        }

        DealStyle dealStyle;
        if (categoryIds.contains(TuanCommonConstants.CATEGORY_ID_SCENIC_TICKET_SINGLE)
                || categoryIds.contains(TuanCommonConstants.CATEGORY_ID_SCENIC_TICKET_UNION)) {
            dealStyle = new DealStyle("scenic");
        } else if (categoryIds.contains(TuanCommonConstants.CATEGORY_ID_HOTEL)) {
            dealStyle = new DealStyle("hotel");
        } else if (isKtv(categoryIds)) {
            dealStyle = new DealStyle("ktv");
        } else if (isFun(categoryIds)) {
            dealStyle = new DealStyle("fun");
        } else if (categoryIds.contains(TuanCommonConstants.CATEGORY_ID_TRAVEL)) {
            dealStyle = new DealStyle("travel");
        } else if (categoryIds.contains(ScrumDealConstants.CATEGORY_ATTRIBUTE_BEAUTY_NAIL)) {
            dealStyle = new DealStyle("beauty_nail");
        } else if (categoryIds.contains(TuanCommonConstants.CATEGORY_ID_BEAUTY)) {
            dealStyle = new DealStyle("beauty");
        } else if (categoryIds.contains(TuanCommonConstants.CATEGORY_ID_CLOTHES)) {
            dealStyle = new DealStyle("clothes");
        } else {
            dealStyle = new DealStyle("default");
        }

        if (isBuyBarDown && StringUtils.equals(dealStyle.getModuleKey(), "default")) {
            dealStyle.setModuleKey(dealStyle.getModuleKey() + "_bardown");  //立即购买按钮吸底
        }

        detailDegrade(dealStyle);
        return dealStyle;
    }

    public static DealStyle getDealStyle(DealStyleRequest request) {
        if (request == null || CollectionUtils.isEmpty(request.getCategoryValues())) {
            return DealStyles.DEFAULT_STYLE;
        }
        Version version = new Version(request.getVersion());
        List<String> categoryIds = request.getCategoryValues();
        if (categoryIds.contains(TuanCommonConstants.CATEGORY_VALUE_SCENIC_TICKET_SINGLE)
                || categoryIds.contains(TuanCommonConstants.CATEGORY_VALUE_SCENIC_TICKET_UNION)) {
            return DealStyles.SCENIC;
        } else if (categoryIds.contains(TuanCommonConstants.CATEGORY_VALUE_TRAVEL)) {
            return isSelected(request.getSelectValues()) ? DealStyles.TRAVEL_SELECTED : DealStyles.TRAVEL;
        } else if (categoryIds.contains(TuanCommonConstants.CATEGORY_VALUE_HOTEL)) {
            return isSelected(request.getSelectValues()) ? DealStyles.HOTEL_SELECTED : DealStyles.HOTEL;
        } else if (hasKtv(categoryIds)) {
            return DealStyles.KTV;
        } else if (hasFun(categoryIds) && compareTo(version, VERSION_GC) < 0) {
            return DealStyles.FUN;
        } else if (categoryIds.contains(TuanCommonConstants.CATEGORY_VALUE_FUN_MSG)
                && compareTo(version, VERSION_BABY) >= 0) {
            return DealStyles.FUN_MSG;
        } else if (categoryIds.contains(ScrumDealConstants.CATEGORY_KEY_BEAUTY_NAIL) &&
                compareTo(version, VERSION_EDU) >= 0) {
            return DealStyles.BEAUTY_NAIL;
        } else if (categoryIds.contains(ScrumDealConstants.CATEGORY_KEY_BEAUTY)) {
            return DealStyles.BEAUTY;
        } else if (categoryIds.contains(TuanCommonConstants.CATEGORY_VALUE_CLOTHES)) {
            return DealStyles.CLOTHES;
        } else if (categoryIds.contains(ScrumDealConstants.CATEGORY_KEY_EDU) &&
                compareTo(version, VERSION_EDU) >= 0) {
            return DealStyles.EDUCATION;
        } else if (interceptNotEmpty(categoryIds, TuanCommonConstants.BABY_EDUS) &&
                compareTo(version, VERSION_BABY) >= 0) {
            return DealStyles.BABY_PHOTO;
        } else if (categoryIds.contains(TuanCommonConstants.CATEGORY_VALUE_FLOWER) &&
                compareTo(version, VERSION_FLOWER) >= 0) {
            return DealStyles.FLOWER;
        } else if (categoryIds.contains(TuanCommonConstants.CATEGORY_VALUE_BACK_ROOM) &&
                compareTo(version, VERSION_BACK_ROOM) >= 0) {
            return DealStyles.BACK_ROOM;
        } else if (hasWedding(categoryIds) && compareTo(version, VERSION_WEDDING) >= 0) {
            return DealStyles.WEDDING;
        } else if (categoryIds.contains(ScrumDealConstants.CATEGORY_KEY_FOOD) &&
                compareTo(version, VERSION_FOOD) >= 0) {
            return DealStyles.FOOD;
        } else if(compareTo(version, VERSION_GC) >= 0 && !categoryIds.contains(ScrumDealConstants.CATEGORY_KEY_FOOD)) {
            return DealStyles.GC_DEFAULT;
        } else {
            return request.isBarDown() || isBuyBarDown(version) ?
                    DealStyles.DEFAULT_DOWN : DealStyles.DEFAULT_STYLE;
        }
    }

    private static boolean isSelected(List<String> values) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.util.DealStyleUtil.isSelected(java.util.List)");
        return values != null && values.contains("1");
    }

    private static boolean isKtv(List<Integer> categoryIds) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.util.DealStyleUtil.isKtv(java.util.List)");
        return categoryIds.contains(TuanCommonConstants.CATEGORY_ID_KTV);
    }

    private static boolean hasWedding(List<String> categoryIds) {
        if (categoryIds.contains(TuanCommonConstants.CATEGORY_VALUE_WEDDING)) {
            if (categoryIds.contains(TuanCommonConstants.CATEGORY_VALUE_PHOTO_WRITING)
                    || categoryIds.contains(TuanCommonConstants.CATEGORY_VALUE_WEDDING_FEAST)) {
                return false;
            }
            return true;
        }
        return false;
    }

    private static boolean hasKtv(List<String> categoryIds) {
        return categoryIds.contains(TuanCommonConstants.CATEGORY_VALUE_KTV);
    }

    private static boolean isFun(List<Integer> categoryIds) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.util.DealStyleUtil.isFun(java.util.List)");
        String funCategoryStr = LionConfigUtils.getProperty("tpfun-product-service.tgFunCategories", "");
        if (StringUtils.isNotBlank(funCategoryStr)) {
            List<String> funCategories = Arrays.asList(StringUtils.split(funCategoryStr, ","));
            for (int categoryId : categoryIds) {
                if (funCategories.contains(String.valueOf(categoryId))) {
                    return true;
                }
            }
        }
        return false;
    }

    private static List<String> funCategoryValues = Collections.EMPTY_LIST;
    private static String funCategoryLion = StringUtils.EMPTY;

    private static boolean hasFun(List<String> categoryIds) {
        for (String fun : getFunCategoryValues()) {
            if (categoryIds.contains(fun)) {
                return true;
            }
        }
        return false;
    }

    private static List<String> getFunCategoryValues() {
        String lion = LionConfigUtils.getProperty("tpfun-product-service.tgFunCategories");
        if (StringUtils.isEmpty(lion)) {
            return Collections.EMPTY_LIST;
        }
        if (!lion.equals(funCategoryLion)) {
            funCategoryValues = Arrays.asList(lion.split(","));
            funCategoryLion = lion;
        }
        return funCategoryValues;
    }

    private static void detailDegrade(DealStyle dealStyle) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.util.DealStyleUtil.detailDegrade(com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DealStyle)");
        String lion = LionConfigUtils.getProperty("mapi-tgdetail-web.degrade.dealstyles");
        if (lion.contains("," + dealStyle.getModuleKey() + ",")) {
            dealStyle.setModuleKey(dealStyle.getModuleKey() + "_degrade");
        }
    }

    private static <T> boolean interceptNotEmpty(List<T> a, List<T> b) {
        if(CollectionUtils.isEmpty(a) || CollectionUtils.isEmpty(b)) {
            return false;
        }
        for(T aa : a){
            for(T bb : b) {
                if(aa.equals(bb)) {
                    return true;
                }
            }
        }
        return false;
    }

    public static int compareTo(String versionA, String versionB) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.util.DealStyleUtil.compareTo(java.lang.String,java.lang.String)");
        if (StringUtils.isBlank(versionA)) {
            return -1;
        } else if (StringUtils.isBlank(versionB)) {
            return 1;
        }
        return compareTo(new Version(versionA), new Version(versionB));
    }

    /**
     * return 1,versionA>versionB
     * return 0,versionA=versionB
     * return -1,versionA<versionB
     *
     * @param versionA
     * @param versionB
     * @return
     */
    public static int compareTo(Version versionA, Version versionB) {
        if (versionA == null) {
            return -1;
        } else if (versionB == null) {
            return 1;
        }
        int[] versionASplit = versionA.getVersionNum();
        int[] versionBSplit = versionB.getVersionNum();

        if (versionASplit.length != versionBSplit.length) {
            if (versionASplit.length > versionBSplit.length) {
                for (int l = 0; l < versionBSplit.length; l++) {
                    if (versionASplit[l] > versionBSplit[l]) {
                        return 1;
                    } else if (versionASplit[l] < versionBSplit[l]) {
                        return -1;
                    } else {
                        continue;
                    }
                }
                for (int i = versionASplit.length - 1; i >= versionBSplit.length; i--) {
                    if (versionASplit[i] != 0) {
                        return 1;
                    }
                }
                return 0;
            } else {
                for (int h = 0; h < versionASplit.length; h++) {
                    if (versionASplit[h] > versionBSplit[h]) {
                        return 1;
                    } else if (versionASplit[h] < versionBSplit[h]) {
                        return -1;
                    } else {
                        continue;
                    }
                }
                for (int j = versionBSplit.length - 1; j >= versionASplit.length; j--) {
                    if (versionBSplit[j] != 0) {
                        return -1;
                    }
                }
                return 0;
            }
        } else {
            for (int k = 0; k < versionASplit.length; k++) {
                if (versionASplit[k] > versionBSplit[k]) {
                    return 1;
                } else if (versionASplit[k] < versionBSplit[k]) {
                    return -1;
                } else {
                    continue;
                }
            }
            return 0;
        }
    }
}