package com.dianping.mobile.mapi.dztgdetail.helper;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/6
 */
@Slf4j
public class UserInfoHelper {

    private static String MT_DEFAULT_PIC = "https://p0.meituan.net/ingee/4e78589c0aa6132682faae3f0438c8a4298832.png";
    private static List<String> DP_DEFAULT_PICS = Lists.newArrayList(
            "https://p0.meituan.net/ingee/0c8c3a9425dd6e925b58864216b72a9c5130.png",
            "https://p0.meituan.net/ingee/28d143c6b991efe82d332ab41c17447c5169.png",
            "https://p0.meituan.net/ingee/3b124eb461454533afdf26364875a6c14735.png",
            "https://p0.meituan.net/ingee/47497a6f9f64e55c05e36e84795db2e14425.png",
            "https://p0.meituan.net/ingee/3dffe21f14eb5ccc7e4a86e4cde3bbfe4564.png",
            "https://p0.meituan.net/ingee/5d38058564b68ea7a77853bcd411f3168555.png",
            "https://p0.meituan.net/ingee/6f62de75bc482b5fc37fea7225cd25913555.png",
            "https://p0.meituan.net/ingee/23e2d6fad1a266bf63a1d6937849de847814.png",
            "https://p0.meituan.net/ingee/0d2c0b166f1202df4ca31e30a61b897e5132.png",
            "https://p0.meituan.net/ingee/107b98d4191fe3ff0bb0ceb3a66bbeab6226.png",
            "https://p0.meituan.net/ingee/e96f0cdf0600a89c4664d61bc71a2c0a3861.png",
            "https://p0.meituan.net/ingee/7584889a36e13388ebc8b18a6a9224ff8928.png",
            "https://p0.meituan.net/ingee/f2bcbab297f21e00cbbcb5ee9c05d6d67085.png",
            "https://p0.meituan.net/ingee/75a2292cab22fd2959730687d3968ab58004.png",
            "https://p0.meituan.net/ingee/f40be21df9ca069e901af5d466ad434e6192.png"
    );

    public static String getDefaultAvatarUrl(boolean isMt, String userName) {
        if (isMt) {
            return MT_DEFAULT_PIC;
        }
        if (userName == null) {
            return DP_DEFAULT_PICS.get(0);
        }
        try {
            int index = Math.abs(userName.hashCode()) % DP_DEFAULT_PICS.size();
            return DP_DEFAULT_PICS.get(index);
        } catch (Exception e) {
            log.error("getDefaultPic exception, userName: {}", userName, e);
        }
        return DP_DEFAULT_PICS.get(0);
    }

}
