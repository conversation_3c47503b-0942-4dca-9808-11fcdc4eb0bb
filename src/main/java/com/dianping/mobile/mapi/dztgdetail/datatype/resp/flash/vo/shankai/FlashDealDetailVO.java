package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.shankai;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.struct.DealDetailStructModuleDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.DealModuleDetailVO;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2024/10/17 18:58
 */
@Data
@MobileDo(id = 0x6b7e)
public class FlashDealDetailVO implements Serializable {
    @FieldDoc(description = "定制样式团购详情")
    @MobileDo.MobileField(key = 0x3867)
    private DealModuleDetailVO dealModuleDetail;

    @FieldDoc(description = "通用样式团购详情")
    @MobileDo.MobileField(key = 0xb020)
    private DealDetailStructModuleDo dealDetailStruct;
}
