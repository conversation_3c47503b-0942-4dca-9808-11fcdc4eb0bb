package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DzDealMerchantInfoRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.Response;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealMerchantInfoBO;
import com.dianping.mobile.mapi.dztgdetail.facade.DealMerchantAdditionFacade;
import com.dianping.mobile.mapi.dztgdetail.util.LogUtils;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.HttpMethod;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.List;

@InterfaceDoc(displayName = "到综团单商家附加信息展示接口",
        type = "restful",
        description = "查询到综公益商家等",
        scenarios = "该接口适用于双平台APP站点的提单页规格选择模块",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "suqiulin"
)
@Controller("general/platform/dztgdetail/dzdealmerchantadditon.bin")
@Action(url = "dzdealmerchantadditon.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class DealMerchantAdditionalAction extends AbsAction<DzDealMerchantInfoRequest> {

    @Resource
    private DealMerchantAdditionFacade merchantAdditionFacade;

    @Override
    protected IMobileResponse validate(DzDealMerchantInfoRequest request, IMobileContext iMobileContext) {
        if (request == null || StringUtils.isEmpty(request.getDealId()) || StringUtils.isEmpty(request.getShopId())) {
            LogUtils.warn(null, "[DealMerchantAdditionalAction] validate request={}, iMobileContext={}",
                    JsonCodec.encode(request), JsonCodec.encode(iMobileContext));
            return new CommonMobileResponse("参数非法");
        }
        //新api第一次上线，万一有什么问题直接返回null，稳定以后可以删掉这几行
        boolean pass = Lion.getBoolean(LionConstants.APP_KEY, LionConstants.MERCHANT_API_CONTROL, true);
        if (!pass) {
            return new CommonMobileResponse("API接口未开放");
        }
        return null;
    }

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "dzdealmerchantadditon.bin",
            displayName = "查询到综团单规格选择信息。",
            description = "查询到综团单规格选择信息。",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "dzdealmerchantadditon.bin请求参数",
                            type = DzDealMerchantInfoRequest.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "result", description = "团详商家附加信息", type = DealMerchantInfoBO.class)},
            restExampleUrl = "http://mapi.51ping.com/general/platform/dztgdetail/dzdealmerchantadditon.bin?" +
                    "dealid=947452590&shopid=&csecpkgname=com.meituan.imeituan-beta&csecplatform=2&csecversion=1.0.15&csecversionname=12.20.403",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    ),
            }
    )
    @Override
    @CryptMethod
    protected IMobileResponse execute(DzDealMerchantInfoRequest request, IMobileContext iMobileContext) {
        EnvCtx envCtx = initEnvCtx(iMobileContext);
        try {
            Response<DealMerchantInfoBO> response = merchantAdditionFacade.buildMerchantAddition(request, envCtx);
            return new CommonMobileResponse(response.getResult());
        } catch (Exception e) {
            logger.error("dzdealmerchantadditon.bin error", e);
        }
        return Resps.SYSTEM_ERROR;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
