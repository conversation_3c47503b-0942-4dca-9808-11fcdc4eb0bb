package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PromoWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.SkuWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MiniProgramSceneEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.PromoHelper;
import com.dianping.pay.promo.common.enums.ProductType;
import com.dianping.pay.promo.display.api.dto.Product;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.pay.promo.display.api.dto.QueryPromoDisplayRequest;
import com.dianping.tpfun.product.api.sku.common.enums.FunChannel;
import com.dianping.tpfun.product.api.sku.common.enums.FunClientType;
import com.dianping.tpfun.product.api.sku.common.enums.FunPlatform;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.dianping.tpfun.product.api.sku.pintuan.request.GetPinProductBriefReq;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Iterator;
import java.util.Map;

/**
 * Created by zuomlin on 2018/12/17.
 */
public class PinPoolProcessor extends AbsDealProcessor {

    private static final int PIN_POOL_PROMO_TEMPLATE_ID = 226;

    @Autowired
    private SkuWrapper skuWrapper;

    @Autowired
    private PromoWrapper promoWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        boolean isMainApp = ctx.getEnvCtx().isMainApp() && !ctx.isExternal();
        boolean isExternalAndEnabled = ctx.getEnvCtx().isExternalAndEnabled(MiniProgramSceneEnum.PINTUAN.getPromoScene());
        return isMainApp || isExternalAndEnabled;
    }

    @Override
    public void prepare(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.PinPoolProcessor.prepare(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        Map<Integer, Integer> dealGroupId2PinProductIdMap = skuWrapper.getPinProductIdByDealGroupIds(Lists.newArrayList(ctx.getDpId()));
        if (MapUtils.isEmpty(dealGroupId2PinProductIdMap)) {
            return;
        }
        GetPinProductBriefReq request = new GetPinProductBriefReq();
        request.setPinProductIds(Lists.newArrayList(dealGroupId2PinProductIdMap.values()));
        if (ctx.isMt()) {
            request.setCityId(ctx.getMtCityId());
            request.setFunChannel(FunChannel.MT.code);
            request.setUserId(ctx.getEnvCtx().getMtUserId());//已确认判断平台后再使用
        } else {
            request.setFunChannel(FunChannel.DP.code);
            request.setCityId(ctx.getDpCityId());
            request.setUserId(ctx.getEnvCtx().getDpUserId());
        }
        if (ctx.getEnvCtx().isMainApp()) {
            request.setClientVersion(ctx.getEnvCtx().getVersion());
            request.setFunClientType(FunClientType.NATIVE.code);
        } else if (ctx.getEnvCtx().isMainWX()) {
            request.setFunClientType(FunClientType.MINIPROGRAM.code);
        } else {
            request.setFunClientType(FunClientType.M.code);
        }
        if (ctx.getEnvCtx().isAndroid()) {
            request.setFunPlatform(FunPlatform.ANDROID.code);
        } else {
            request.setFunPlatform(FunPlatform.IPHONE.code);
        }
        //分平台透传门店id, 获取拼团下单页url
        request.setLongShopId(ctx.getLongPoiId4PFromReq());
        ctx.getFutureCtx().setPinProductBriefFuture(skuWrapper.prepareBatchGetPinProductBrief(request));
    }

    @Override
    public void process(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.PinPoolProcessor.process(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        Map<Integer, PinProductBrief> pinProductId2BriefMap = skuWrapper.batchGetPinProductBrief(ctx.getFutureCtx().getPinProductBriefFuture());
        if (MapUtils.isEmpty(pinProductId2BriefMap) || CollectionUtils.isEmpty(pinProductId2BriefMap.values())) {
            return;
        }
        Iterator<PinProductBrief> iterator = pinProductId2BriefMap.values().iterator();
        if (iterator.hasNext()) {
            ctx.setPinProductBrief(iterator.next());
        }
        if (ctx.getPinProductBrief() != null && ctx.getPinProductBrief().getPrice() != null && ctx.getPinProductBrief().getPrice().doubleValue() > 0) {
            PromoDisplayDTO promoDisplayDTO = promoWrapper.getPromoDisplayDTO(buildTimesPromoReq(ctx.getPinProductBrief(), ctx));
            ctx.setPinPoolPromoDesc(PromoHelper.getPromoDisplayDTODesc(promoDisplayDTO));
            ctx.setPinPoolPromoAmount(PromoHelper.getPromoDisplayDTOAmount(promoDisplayDTO));
        }
    }

    private QueryPromoDisplayRequest buildTimesPromoReq(PinProductBrief pinProductBrief, DealCtx dealCtx) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.PinPoolProcessor.buildTimesPromoReq(PinProductBrief,DealCtx)");
        Product product = new Product();
        product.setProductId(pinProductBrief.getItemId());
        product.setPrice(pinProductBrief.getPrice());

        QueryPromoDisplayRequest request = new QueryPromoDisplayRequest();
        request.setProduct(product);
        request.setClientVersion(dealCtx.getEnvCtx().getVersion());
        request.setTemplateID(PIN_POOL_PROMO_TEMPLATE_ID); //商户页模板
        request.setPlatform(dealCtx.getEnvCtx().toPayPlatformCode());
        if (dealCtx.isMt()) {
            request.setCityId(dealCtx.getMtCityId());
            request.setUserId(dealCtx.getEnvCtx().getMtUserId());//已确认判断平台后再使用
            request.setDpId(dealCtx.getEnvCtx().getUuid());
            request.setProductType(ProductType.mt_generalPinTuan.value);
        } else {
            request.setCityId(dealCtx.getDpCityId());
            request.setUserId(dealCtx.getEnvCtx().getDpUserId());
            request.setDpId(dealCtx.getEnvCtx().getDpId());
            request.setProductType(ProductType.generalPinTuan.value);
        }
        return request;
    }
}
