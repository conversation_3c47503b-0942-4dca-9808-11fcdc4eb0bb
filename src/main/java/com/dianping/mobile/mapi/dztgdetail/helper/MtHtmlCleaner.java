package com.dianping.mobile.mapi.dztgdetail.helper;

import com.google.common.collect.ImmutableList;
import org.apache.commons.lang.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Converts a block of HTML to plain-text, keeping the original format as possible.
 * <AUTHOR>
 * <AUTHOR>
 *
 */
public class MtHtmlCleaner {
	
    /*
     * 以下HTML标签过滤后面增加一个换行
     */
    private static final ImmutableList<String> BLOCK_TAGS = ImmutableList.of("address", "blockquote", "div", "dl", "fieldset", "form", "h1", "h2",
            "h3", "h4", "h5", "h6", "hr", "noscript", "ol", "p", "pre", "table", "ul", "tr", "thead");
	
	/*
	 * 以下HTML标签过滤后面增加一个空格
	 */
    private static final ImmutableList<String> spacingTags = ImmutableList.of("td");
	
	private static final char LINE_FEED = '\n';
	
	public static String clean(String html) {
		return clean(html, false);
	}
	
	public static String clean(String html, boolean containsImg) {
		if (StringUtils.isEmpty(html)) {
			return StringUtils.EMPTY;
		}
		
		html = Pattern.compile("\\r|\\n|\\t", Pattern.CASE_INSENSITIVE | Pattern.MULTILINE).matcher(html).replaceAll(StringUtils.EMPTY); //去除除了空格外的空白字符
		StringBuilder sb = new StringBuilder();
		append(Jsoup.parse(html), sb, containsImg);
		String retStr = Pattern.compile("(\\n\\s*){3,}", Pattern.CASE_INSENSITIVE | Pattern.MULTILINE).matcher(sb.toString()).replaceAll("\n\n"); //去除多余换行
        return filterDescription(retStr);
	}
	
	private static void append(Node node, StringBuilder sb, boolean containsImg) {
		if (node instanceof TextNode) {
			sb.append(((TextNode) node).text());
		} else if (node instanceof Element) {
			String tag = ((Element) node).tagName();
			if (tag.equals("img") && containsImg) {
				String src = StringUtils.isNotEmpty(node.attr("src")) ? node.attr("src") : node.attr("lazy-src");
				if (StringUtils.isNotEmpty(src)) sb.append("<img>" + src + "</img>");
			} else if (tag.equals("br")) {
				sb.append(LINE_FEED);
			} else if (BLOCK_TAGS.contains(tag)) {
				if (!inNewline(sb)) sb.append(LINE_FEED);
				for (Node subNode : node.childNodes()) append(subNode, sb, containsImg);
				if (!inNewline(sb)) sb.append(LINE_FEED);
			} else {
				for (Node subNode : node.childNodes()) append(subNode, sb, containsImg);
				if (spacingTags.contains(tag)) sb.append(' ');
			}
		}
	}
	
	private static boolean inNewline(StringBuilder sb) {
		return sb.length() == 0 || sb.charAt(sb.length() - 1) == '\n';
	}

    /*
    * 调整老版详情中错落的样式--“总价值团购价”部分
    */
    private static String filterDescription(String text){
        if(StringUtils.isNotBlank(text)){
            text = text.replace("总价值\n团购价", "总价值 团购价\n");
            Pattern pattern = Pattern.compile("([\\s\\S]*)(总价值 团购价\n)([\\s\\S]*)");
            Matcher matcher = pattern.matcher(text);
            if(matcher.find() && matcher.groupCount() >= 3 && org.apache.commons.lang3.StringUtils.isNotBlank(matcher.group(3))){
                String toReplaceStr = matcher.group(3);
                String replaceStr = matcher.group(3).replaceFirst("\n", " ");
                return text.replace(toReplaceStr, replaceStr);
            }
        }
        return text;
    }
}