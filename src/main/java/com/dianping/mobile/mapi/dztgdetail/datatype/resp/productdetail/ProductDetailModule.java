package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.multisku.MultiSkuSelectVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-07
 * @desc
 */
@Data
@MobileDo(id = 0x810f648c)
public class ProductDetailModule implements Serializable {
    /**
     * 多SKU选择模块
     */
    @MobileField(key = 0x39d6)
    private MultiSkuSelectVO multiSkuSelectVO;

    /**
     * 商品分类和属性信息模型
     */
    @MobileField(key = 0xdaba)
    private List<InventoryDetail> dealInventory;

    /**
     * 结构化详情
     */
    @MobileField(key = 0x8a19)
    private List<StructuredDetail> structDetail;

    /**
     * 优惠浮层
     */
    @MobileField(key = 0x5cb1)
    private ModulePriceDiscountDetail promoDetail;

    /**
     * 商品氛围条
     */
    @MobileField(key = 0x6b44)
    private ProductAtmosphereModuleVO productAtmosphere;

    /**
     * 商品价格
     */
    @MobileField(key = 0x8a3e)
    private ProductPriceModuleVO productPrice;
}
