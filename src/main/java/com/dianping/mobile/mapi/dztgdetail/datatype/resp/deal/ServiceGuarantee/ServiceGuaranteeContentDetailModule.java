package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ServiceGuarantee;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.util.List;
import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 16/11/2022
 * @time 11:43
 * 模型描述：服务保障内容详情模块
 * 移动之家链接: https://mobile.sankuai.com/studio/model/info/29814
 */
@MobileDo(id = 0xe6ae)
@Data
public class ServiceGuaranteeContentDetailModule implements Serializable {

    /**
     * 服务保障内容详情模块标题
     */
    @MobileField(key = 0x1764)
    private String moduleTitle;

    /**
     * 服务保障内容详情模块icon的url
     */
    @MobileField(key = 0xf39b)
    private String iconUrl;

    /**
     * 服务保障内容详情模块的文本列表
     */
    @MobileField(key = 0xa0d2)
    private List<ContentText> contentDetailTexts;
}
