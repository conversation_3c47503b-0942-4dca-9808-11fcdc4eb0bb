package com.dianping.mobile.mapi.dztgdetail.facade;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.deal.sales.common.datatype.ProductParam;
import com.dianping.deal.sales.common.datatype.SalesDisplayDTO;
import com.dianping.deal.sales.common.datatype.SalesDisplayRequest;
import com.dianping.deal.sales.common.enums.SalesPlatform;
import com.dianping.lion.client.Lion;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.CreateOrderPageUrlBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealSkuProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.*;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.constants.SaleConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BindingSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.BuyMoreSaveMoreCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.BuyMoreSaveMoreReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.BuyMoreSaveMoreCardVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.BuyMoreSaveMoreVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo.CouponDescItem;
import com.dianping.mobile.mapi.dztgdetail.entity.CombinationDealInfo;
import com.dianping.mobile.mapi.dztgdetail.entity.SkuSummary;
import com.dianping.mobile.mapi.dztgdetail.helper.PromoHelper;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.NumbersUtils;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dealuser.price.display.api.enums.CouponAssignStatusEnum;
import com.sankuai.dealuser.price.display.api.enums.ExtPriceTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.TyingSaleTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PriceResponse;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dealuser.price.display.api.model.TyingSaleDTO;
import com.sankuai.dztheme.deal.dto.DealProductDTO;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.general.product.query.center.client.builder.model.*;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.DealGroupStatusEnum;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.util.DealGroupUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.Validate;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2023/7/25
 */
@Component
@Slf4j
public class BuyMoreSaveMoreFacade {
    public static final String MAIN_DEAL_KEY = "productId_a";
    public static final String BINDING_DEAL_KEY = "productId_b";
    private static final String MT_DETAIL_URL_TEMPLATE = "imeituan://www.meituan.com/gc/deal/detail?did=%d&poiid=%s";
    private static final String DP_DETAIL_URL_TEMPLATE = "dianping://tuandeal?shopid=%s&id=%d";
    public static final String QI_TA_SERVICE_TYPE = "其他";
    public static final String TAO_CAN_TEXT = "套餐";

    private static final List<Integer> DEAL_GROUP_VALID_STATUS_LIST = Arrays.asList(
            DealGroupStatusEnum.VISIBLE_ONLINE.getCode(),
            DealGroupStatusEnum.NOT_VISIBLE_ONLINE.getCode()
    );

    @Autowired
    private QueryCenterWrapper queryCenterWrapper;
    @Autowired
    private DealStockSaleWrapper dealStockSaleWrapper;
    @Autowired
    private DouHuBiz douHuBiz;
    @Autowired
    private RecommendServiceWrapper recommendServiceWrapper;
    @Autowired
    private CreateOrderPageUrlBiz createOrderPageUrlBiz;
    @Autowired
    private PriceDisplayWrapper priceDisplayWrapper;
    @Autowired
    private DzDealThemeWrapper dzDealThemeWrapper;

    @Resource
    private MultiSkuExpBiz multiSkuExpBiz;

    @Resource
    private PoiShopCategoryWrapper poiShopCategoryWrapper;

    @Resource
    private DealGroupWrapper dealGroupWrapper;

    @Resource
    private MapperWrapper mapperWrapper;
    @Resource
    private PackBlackListWrapper packBlackListWrapper;

    /**
     * 到综副标题-团购主题-计划ID
     */
    private static final String DZ_DEAL_SUB_TITLE_PLAN_ID = "10002434";
    private static final int MODULE_LIMIT = 10;

    private static final List<String> EXP_PASS_RESULT = Lists.newArrayList("c", "e");

    public BuyMoreSaveMoreVO getRecommendCombineDeal(BuyMoreSaveMoreReq req, EnvCtx envCtx) {
        // 参数校验
        checkRequest(req);
        Long shopId = NumberUtils.toLong(req.getPoiidStr());

        // 结构初始化
        BuyMoreSaveMoreCtx ctx = new BuyMoreSaveMoreCtx();
        initContext(req, ctx, envCtx);

        // 非app来源直接返回 或者 非白名单渠道直接返回
        if (!isMainApp(envCtx) || !isWhiteTrafficFlag(req)) {
            return null;
        }

        // 非首屏请求直接返回
        if (req.getStart() != null && req.getStart() > 0) {
            return null;
        }

        // 模块整体过AB实验
        BuyMoreSaveMoreVO buyMoreSaveMoreVO = new BuyMoreSaveMoreVO();
        AbConfig abConfig = getAbStrategy(envCtx);
        buyMoreSaveMoreVO.setExpBiInfo(Optional.ofNullable(abConfig).map(AbConfig::getExpBiInfo).orElse(""));
        if (abConfig == null || !EXP_PASS_RESULT.contains(abConfig.getExpResult())) {
            return buyMoreSaveMoreVO;
        }

        // 多买多省三期：运营品+算法品
        List<CombinationDealInfo> combinationAfterMerge = getCombination(ctx);
        if (CollectionUtils.isEmpty(combinationAfterMerge)) {
            return buyMoreSaveMoreVO;
        }
        List<CombinationDealInfo> combinationDealInfos = combinationAfterMerge.subList(0, Math.min(MODULE_LIMIT, combinationAfterMerge.size()));

        //搭售黑名单过滤
        Long mtShopId = envCtx.isDp() ? mapperWrapper.getMtShopIdByDpShopIdLong(shopId) : shopId;
        List<Future> futures = packBlackListWrapper.prePackBlackLis(combinationDealInfos, mtShopId );
        if (CollectionUtils.isNotEmpty(futures)) {
            Map<String, Boolean> packBlackMap = packBlackListWrapper.getPackBlackMap(futures);
            combinationDealInfos = combinationDealInfos.stream()
                    .filter(combinationDealInfo -> !packBlackMap.getOrDefault(combinationDealInfo.getMainDealId() + "_" + combinationDealInfo.getBindingDealId(), false))
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(combinationDealInfos)) {
            return buyMoreSaveMoreVO;
        }

        // 提取商品id
        Set<Integer> mainDealIds = CollectionUtils.emptyIfNull(combinationDealInfos).stream().map(CombinationDealInfo::getMainDealId).collect(Collectors.toSet());
        Set<Integer> bindingDealIds = CollectionUtils.emptyIfNull(combinationDealInfos).stream().map(CombinationDealInfo::getBindingDealId).collect(Collectors.toSet());
        List<Integer> combinationDealIds = Lists.newArrayList();
        combinationDealIds.addAll(mainDealIds);
        combinationDealIds.addAll(bindingDealIds);

        // 商品基本信息
        Future dealGroupFuture = getDealGroupFuture(envCtx, combinationDealIds);
        // 销量
        Future preStocksFuture = getStocksFuture(envCtx, ctx, combinationDealIds);
        // 报价信息
        Future<PriceResponse<Map<Long, List<PriceDisplayDTO>>>> priceResponseFuture = priceDisplayWrapper.getBuyMoreSaveMorePriceInfo(req.getCityId(), envCtx, shopId, combinationDealInfos, req);
        // 搭售商品副标题
        List<Integer> dealIds = Lists.newArrayList(bindingDealIds);
        DealProductRequest dealProductRequest = buildDealProductRequest(envCtx, dealIds, shopId);
        Future preDealSubTitleFuture = dzDealThemeWrapper.preQueryDealSubTitle(dealProductRequest);
        // 商品基本信息
        List<DealGroupDTO> dealGroupDTOs = queryCenterWrapper.getDealGroupDTOs(dealGroupFuture);
        dealGroupDTOs = filterDealGroupDTOS(dealGroupDTOs, ctx.getEnvCtx().isMt());
        if (CollectionUtils.isEmpty(dealGroupDTOs)) {
            return buyMoreSaveMoreVO;
        }
        Map<Integer, DealGroupDTO> dealIdDealGroupDTOMap = CollectionUtils.emptyIfNull(dealGroupDTOs).stream().collect(Collectors.toMap(dealGroupDTO -> envCtx.isMt() ? dealGroupDTO.getMtDealGroupId().intValue() : dealGroupDTO.getDpDealGroupId().intValue(), Function.identity(), (o1, o2) -> o1));

        // 销量
        Map<ProductParam, SalesDisplayDTO> salesDisplayDTOMap = dealStockSaleWrapper.getFutureResult(preStocksFuture);
        Map<Integer, SalesDisplayDTO> productId2SaleMap = new HashMap<>();
        if (MapUtils.isNotEmpty(salesDisplayDTOMap)) {
            for (Map.Entry<ProductParam, SalesDisplayDTO> entry : salesDisplayDTOMap.entrySet()) {
                productId2SaleMap.put((int) entry.getKey().getProductGroupId(), entry.getValue());
            }
        }
        Map<Integer, PriceDisplayDTO> dealIdPriceDisplayMap = priceDisplayWrapper.getProductMap(priceResponseFuture);
        String priceSecretInfo = priceDisplayWrapper.getPriceSecretInfo(priceResponseFuture);
        if (MapUtils.isEmpty(dealIdPriceDisplayMap)) {
            return buyMoreSaveMoreVO;
        }
        // Map<dealId, PriceDisplayDTO>
        Map<Integer, BigDecimal> dealIdPriceMap = Maps.newHashMap();
        // 获取所有团单及对应价格信息
        dealIdPriceDisplayMap.forEach((key, value) -> {
            dealIdPriceMap.put(key, value.getPrice());
        });
        ctx.setDealIdPriceMap(dealIdPriceMap);
        // 搭售商品 副标题
        DealProductResult dealProductResult = dzDealThemeWrapper.getFutureResult(preDealSubTitleFuture);
        Map<Integer, List<String>> productId2SubTitleMap;
        if (Objects.nonNull(dealProductResult) && CollectionUtils.isNotEmpty(dealProductResult.getDeals())) {
           productId2SubTitleMap = dealProductResult.getDeals().stream()
                    .collect(Collectors.toMap(DealProductDTO::getProductId, DealProductDTO::getProductTags));
        } else {
            productId2SubTitleMap = Maps.newHashMap();
        }
        // 构造搭售品和CombinationDealInfo map
        Map<Integer, CombinationDealInfo> bindingDealCombinationInfoMap = combinationDealInfos.stream().collect(Collectors.toMap(CombinationDealInfo::getBindingDealId, Function.identity(),(e1,e2)->e1));
        // 获取主品对应PriceDisplayDTO
        PriceDisplayDTO priceDisplayDTO = dealIdPriceDisplayMap.get(mainDealIds.iterator().next());
        buildDealCombinationInfoPrice(priceDisplayDTO, bindingDealCombinationInfoMap);
        ctx.setBindingDealCombinationInfoMap(bindingDealCombinationInfoMap);

        boolean floatLayer = multiSkuExpBiz.showLayer(ctx.getShopCategoryIds()) || (ctx.getSkuSummary() != null && ctx.getSkuSummary().getWithSaleAttr())
                || new DealSkuProcessor().showLayerAsCategoryAndServiceType(dealIdDealGroupDTOMap.get(req.getDealGroupId()));
        // 获取交易跳链 Map<搭售品Id，跳链>
        Map<Integer, String> bindingDealIdUrlMap = createOrderPageUrlBiz.buildBuyMoreSaveMoreUrl(ctx, shopId,
                bindingDealCombinationInfoMap, mainDealIds.iterator().next(), floatLayer, priceSecretInfo);

        ctx.setDealGroupDTOMap(dealIdDealGroupDTOMap);
        ctx.setProductId2SaleMap(productId2SaleMap);
        ctx.setDealOrderPageUrlMap(bindingDealIdUrlMap);

        List<BuyMoreSaveMoreCardVO> cardVOS = Lists.newArrayList();
        Map<String, String> textMap = Lion.getMap(LionConstants.APP_KEY,
                LionConstants.BUY_MORE_SAVE_MORE_TEXT_MAP, String.class, Collections.emptyMap());
        combinationDealInfos.forEach(combinationDealInfo -> {
            BuyMoreSaveMoreCardVO cardVO = buildCardVO(ctx, combinationDealInfo, dealIdPriceDisplayMap, textMap, productId2SubTitleMap);
            if (Objects.nonNull(cardVO)) {
                cardVOS.add(cardVO);
            }
        });
        buyMoreSaveMoreVO.setCardList(cardVOS);

        if (req.getSourceType() == 1) { // 团详页
            buyMoreSaveMoreVO.setDealModuleTitle(textMap.get("dealModuleTitle"));
            int size = buyMoreSaveMoreVO.getCardList().size();
            int showItemSize = Lion.getInt(LionConstants.BUY_MORE_SAVE_MORE_SHOW_ITEM_SIZE);
            buyMoreSaveMoreVO.setCardList(size > showItemSize ? buyMoreSaveMoreVO.getCardList().subList(0, showItemSize) : buyMoreSaveMoreVO.getCardList());
            if (size > showItemSize) {
                buyMoreSaveMoreVO.setFloatLayerEntranceTitle(textMap.get("floatLayerEntranceTitle"));
            }
        } else {    // 浮层页
            buyMoreSaveMoreVO.setFloatLayerTitle(textMap.get("floatLayerTitle"));
            // 产品要求不做分页，只展示第一页
            buyMoreSaveMoreVO.setIsEnd(true);
        }
        return buyMoreSaveMoreVO;
    }

    private boolean isWhiteTrafficFlag(BuyMoreSaveMoreReq req) {
        Map<String, String> pageSource2OrderTrafficFlag = Lion.getMap(LionConstants.APP_KEY, LionConstants.PAGESOURCE_TO_ORDER_TRAFFICFLAG, String.class, Collections.emptyMap());
        String trafficFlag = pageSource2OrderTrafficFlag.get(req.getSource());
        return StringUtils.isBlank(trafficFlag) || LionConfigUtils.getBuyMoreTrafficFlagWhiteList().contains(trafficFlag);
    }

    public void buildDealCombinationInfoPrice(PriceDisplayDTO priceDisplayDTO, Map<Integer, CombinationDealInfo> bindingDealCombinationInfoMap){
        if (Objects.nonNull(priceDisplayDTO)) {
            CollectionUtils.emptyIfNull(priceDisplayDTO.getExtPrices()).forEach(extPriceDisplayDTO -> {
                if (extPriceDisplayDTO.getExtPriceType() != ExtPriceTypeEnum.Tying_Sale.getType()) {
                    return;
                }
                TyingSaleDTO tyingSaleDTO = CollectionUtils.emptyIfNull(extPriceDisplayDTO.getTyingSaleDTOS()).stream().filter(t -> TyingSaleTypeEnum.DEAL_GROUP.getType() == t.getTyingSaleType()).collect(Collectors.toList()).stream().findFirst().orElse(new TyingSaleDTO());
                int bindingDealId = (int) tyingSaleDTO.getTyingSaleProductId();
                if (bindingDealId == 0 || !bindingDealCombinationInfoMap.containsKey(bindingDealId)) {
                    return;
                }
                bindingDealCombinationInfoMap.get(bindingDealId).setExtPrice(extPriceDisplayDTO.getExtPrice());
                bindingDealCombinationInfoMap.get(bindingDealId).setExtPricePromoAmount(extPriceDisplayDTO.getExtPricePromoAmount());
                bindingDealCombinationInfoMap.get(bindingDealId).setTyingSaleSkuId(tyingSaleDTO.getTyingSaleSkuId());
            });
        }
    }
    public List<DealGroupDTO> filterDealGroupDTOS(List<DealGroupDTO> dealGroupDTOs, boolean isMt) {
        if (CollectionUtils.isEmpty(dealGroupDTOs)) {
            return Lists.newArrayList();
        }
        List<DealGroupDTO> filteredDealGroupDTOS = Lists.newArrayList();
        List<Integer> dealStatusConfigList = Lion.getList(LionConstants.BUY_MORE_SAVE_MORE_DEAL_STATUS_CONFIG, Integer.class, Collections.emptyList());
        for (DealGroupDTO dealGroupDTO : dealGroupDTOs) {
            // 商品状态过滤
            if (!dealStatusConfigList.contains(dealGroupDTO.getBasic().getStatus())) {
                continue;
            }

            // 售卖期过滤
            Date beginSaleDate = DealGroupUtils.convertString2Date(dealGroupDTO.getBasic().getBeginSaleDate());
            Date endSaleDate = DealGroupUtils.convertString2Date(dealGroupDTO.getBasic().getEndSaleDate());
            long now = System.currentTimeMillis();
            if (now < beginSaleDate.getTime() || now > endSaleDate.getTime()) {
                continue;
            }

            // 库存过滤
            if (isMt) {
                if (Objects.nonNull(dealGroupDTO.getStock().getIsMtSoldOut()) && dealGroupDTO.getStock().getIsMtSoldOut()) {
                    continue;
                }
            } else {
                if (Objects.nonNull(dealGroupDTO.getStock().getIsDpSoldOut()) && dealGroupDTO.getStock().getIsDpSoldOut()) {
                    continue;
                }
            }

            filteredDealGroupDTOS.add(dealGroupDTO);
        }
        return filteredDealGroupDTOS;
    }

    private BuyMoreSaveMoreCardVO buildCardVO(BuyMoreSaveMoreCtx ctx, CombinationDealInfo combinationDealInfo,
                                              Map<Integer, PriceDisplayDTO> dealIdPriceDisplayMap,
                                              Map<String, String> textMap, Map<Integer, List<String>> productId2SubTitleMap) {
        BuyMoreSaveMoreCardVO cardVO = new BuyMoreSaveMoreCardVO();

        Map<Integer, DealGroupDTO> dealGroupDTOMap = ctx.getDealGroupDTOMap();
        Map<Integer, SalesDisplayDTO> productId2SaleMap = ctx.getProductId2SaleMap();
        Map<Integer, String> dealOrderPageUrlMap = ctx.getDealOrderPageUrlMap();
        Map<Integer, CombinationDealInfo> bindingDealCombinationInfoMap = ctx.getBindingDealCombinationInfoMap();

        Integer mainDealId = combinationDealInfo.getMainDealId();
        Integer bindingDealId = combinationDealInfo.getBindingDealId();
        if (!dealGroupDTOMap.containsKey(mainDealId) || !dealGroupDTOMap.containsKey(bindingDealId) || !bindingDealCombinationInfoMap.containsKey(bindingDealId)) {
            return null;
        }
        cardVO.setCardTitle(getCardTitle(dealGroupDTOMap, mainDealId, bindingDealId));
        cardVO.setCombinationId(combinationDealInfo.getItemId());
        cardVO.setMainDealId(combinationDealInfo.getMainDealId());
        cardVO.setBindingDealId(combinationDealInfo.getBindingDealId());
        // 搭售来源
        cardVO.setBindingSource(combinationDealInfo.getBindingSource());
        // 品类型
        cardVO.setMainProductType(1);
        cardVO.setBindingProductType(1);
        // 头图
        cardVO.setMainDealHeaderImage(dealGroupDTOMap.get(mainDealId).getImage().getDefaultPicPath());
        cardVO.setBindingDealHeaderImage(dealGroupDTOMap.get(bindingDealId).getImage().getDefaultPicPath());
        // 标题
        cardVO.setBindingDealTitle(dealGroupDTOMap.get(bindingDealId).getBasic().getTitle());
        // 搭售商品副标题
        List<String> productTags = productId2SubTitleMap.getOrDefault(bindingDealId, Lists.newArrayList());
        String subTitle = Joiner.on("·").join(productTags);
        cardVO.setBindingDealSubTitle(subTitle);
        // 到手价
        Map<Integer, BigDecimal> dealIdPriceMap = ctx.getDealIdPriceMap();
        if (dealIdPriceMap.containsKey(mainDealId) && dealIdPriceMap.containsKey(bindingDealId)) {
            cardVO.setMainDealPrice(textMap.get("rmb") + dealIdPriceMap.get(mainDealId).stripTrailingZeros().toPlainString());
            cardVO.setBindingDealPrice(textMap.get("rmb") + dealIdPriceMap.get(bindingDealId).stripTrailingZeros().toPlainString());
        }
        // 当前商品标签tag
        cardVO.setMainDealTag(textMap.get("mainDealTag"));
        // 组合价&优惠价
        if (Objects.isNull(bindingDealCombinationInfoMap.get(bindingDealId).getExtPrice())) {
            return null;
        }
        cardVO.setCardPriceText(textMap.get("cardPriceText"));
        cardVO.setCardPrice(bindingDealCombinationInfoMap.get(bindingDealId).getExtPrice().stripTrailingZeros().toPlainString());
        BigDecimal extPricePromoAmount = bindingDealCombinationInfoMap.get(bindingDealId).getExtPricePromoAmount();
        cardVO.setDiscountPriceDesc(Objects.isNull(extPricePromoAmount) || extPricePromoAmount.compareTo(BigDecimal.valueOf(0)) <= 0 ? "" : textMap.get("discountPriceDesc") + bindingDealCombinationInfoMap.get(bindingDealId).getExtPricePromoAmount().stripTrailingZeros().toPlainString());
        // 销量
        SalesDisplayDTO salesDisplayDTO = productId2SaleMap.get(bindingDealId);
        if (salesDisplayDTO != null) {
            cardVO.setSales(salesDisplayDTO.getSalesTag());
        }
        // 团详链接
        if (ctx.getEnvCtx().isMt()) {
            cardVO.setBindingDealJumpUrl(String.format(MT_DETAIL_URL_TEMPLATE, bindingDealId, ctx.getReq().getPoiidStr()));
        } else {
            cardVO.setBindingDealJumpUrl(String.format(DP_DETAIL_URL_TEMPLATE, ctx.getReq().getPoiidStr(), bindingDealId));
        }
        cardVO.setBuyButtonText(textMap.get("buyButtonText"));
        // 如果没有对应跳链信息，则直接返回
        if (!dealOrderPageUrlMap.containsKey(bindingDealId)) {
            return null;
        }
        cardVO.setBuyButtonJumpUrl(dealOrderPageUrlMap.get(bindingDealId));

        //主品优惠信息
        PriceDisplayDTO mainPriceDisplayDTOS = dealIdPriceDisplayMap.get(combinationDealInfo.getMainDealId());
        //搭售品优惠信息
        PriceDisplayDTO bundPriceDisplayDTOS = dealIdPriceDisplayMap.get(combinationDealInfo.getBindingDealId());
        ArrayList<PriceDisplayDTO> priceDisplayDTOS = Lists.newArrayList(mainPriceDisplayDTOS,bundPriceDisplayDTOS);
        List<CouponDescItem> couponDescItems = priceDisplayDTOS.stream().map(dto -> {
            List<PromoDTO> couponPromos = dto.getUsedPromos();
            // 政府消费券
            List<PromoDTO> governmentCouponPromos = dto.getUsedPromos().stream()
                    .filter(promoDTO -> promoDTO != null && promoDTO.getIdentity() != null && promoDTO.getIdentity().getPromoType() == PromoTypeEnum.GOVERNMENT_CONSUME_COUPON.getType())
                    .collect(Collectors.toList());

            governmentCouponPromos.stream().findFirst().ifPresent(item -> {
                Map<String, Object> map = getTradeNeedMap(item);
                if (MapUtils.isNotEmpty(map) && !cardVO.getBuyButtonJumpUrl().contains("otherpromptinfo")) {
                    try {
                        String appendUrlArg = "&otherpromptinfo=" + URLEncoder.encode(GsonUtils.toJsonString(map), StandardCharsets.UTF_8.name());
                        cardVO.setBuyButtonJumpUrl(cardVO.getBuyButtonJumpUrl() + appendUrlArg);
                    } catch (UnsupportedEncodingException e) {
                        log.error("encode has error", e);
                    }
                }
            });

            if(CollectionUtils.isEmpty(couponPromos)) {
                return null;
            }

            return buildCouponItem(couponPromos, dto.getIdentity().getProductId());
        }).filter(Objects::nonNull).flatMap(List::stream).collect(Collectors.toList());
        cardVO.setCouponDescItems(couponDescItems);
        return cardVO;
    }

    private List<CombinationDealInfo> getCombination(BuyMoreSaveMoreCtx ctx) {
        // 1.获取运营品
        String recommendJson = recommendServiceWrapper.getRecommendCombineDealV2(ctx, 1, MODULE_LIMIT);
        List<CombinationDealInfo> combinationFromOperation = convertToCombinationDealInfo(recommendJson);

        // 2.获取算法品
        List<RecommendDTO> recommendDTOS = recommendServiceWrapper.getRecommendCombineDealV3(ctx);
        List<CombinationDealInfo> combinationFromAlgorithm = convertToCombinationDealInfo(recommendDTOS);

        // 3.merge（运营品优先+去重）
        if (CollectionUtils.isEmpty(combinationFromOperation)) {
            return combinationFromAlgorithm;
        }
        if (CollectionUtils.isEmpty(combinationFromAlgorithm)) {
            return combinationFromOperation;
        }

        combinationFromOperation.addAll(combinationFromAlgorithm);
        return removeDuplicates(combinationFromOperation);
    }

    // 去重，LinkedHashMap可保证插入顺序
    private List<CombinationDealInfo> removeDuplicates(List<CombinationDealInfo> combinationFromOperation) {
        Map<Integer, CombinationDealInfo> map = new LinkedHashMap<>();

        for (CombinationDealInfo info : combinationFromOperation) {
            map.putIfAbsent(info.getBindingDealId(), info);
        }

        return new ArrayList<>(map.values());
    }

    private Map<String, Object> getTradeNeedMap(PromoDTO item) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.BuyMoreSaveMoreFacade.getTradeNeedMap(com.sankuai.dealuser.price.display.api.model.PromoDTO)");
        Map<String, Object> map = new HashMap<>();
        map.put("receiptBatchId", item.getCouponGroupId());
        if (item.getCouponAssignStatus().equals(CouponAssignStatusEnum.ASSIGNED.getCode())) {
            map.put("receiptCode", item.getIdentity().getPromoId());
        }
        map.put("sceneType", 1);
        return map;
    }

    private List<CouponDescItem> buildCouponItem(List<PromoDTO> couponPromos, int productId) {
        List<CouponDescItem> coupon = Lists.newArrayList();
        for (PromoDTO couponPromo : couponPromos){
            if(couponCanAssign(couponPromo) || govermentConsumeCouponCanAssign(couponPromo)) {
                CouponDescItem item = new CouponDescItem();
                item.setCouponGroupId((int) couponPromo.getIdentity().getPromoId());
                item.setUnifiedcoupongroupids(String.valueOf(couponPromo.getIdentity().getPromoId()));
                item.setDealGroupId(String.valueOf(productId));
                coupon.add(item);
            }
        }
        return coupon;
    }

    private boolean couponCanAssign(PromoDTO couponPromo) {
        return couponPromo.getIdentity().getPromoType() == PromoTypeEnum.COUPON.getType() && PromoHelper.canAssign(couponPromo);
    }

    private boolean govermentConsumeCouponCanAssign(PromoDTO couponPromo) {
        return couponPromo.getIdentity().getPromoType() == PromoTypeEnum.GOVERNMENT_CONSUME_COUPON.getType() && couponPromo.getCouponAssignStatus() != CouponAssignStatusEnum.ASSIGNED.getCode();
    }

    private String getCardTitle(Map<Integer, DealGroupDTO> dealGroupDTOMap, Integer mainDealId, Integer bindingDealId) {
        if (!dealGroupDTOMap.containsKey(mainDealId)
                || !dealGroupDTOMap.containsKey(bindingDealId)
                || StringUtils.isBlank(dealGroupDTOMap.get(mainDealId).getCategory().getServiceType())
                || StringUtils.isBlank(dealGroupDTOMap.get(bindingDealId).getCategory().getServiceType())) {
            return StringUtils.EMPTY;
        }

        String cardTitle = StringUtils.EMPTY;
        String mainDealServiceType = dealGroupDTOMap.get(mainDealId).getCategory().getServiceType();
        String bindingDealServiceType = dealGroupDTOMap.get(bindingDealId).getCategory().getServiceType();
        if (mainDealServiceType.equals(bindingDealServiceType)) {
            if (QI_TA_SERVICE_TYPE.equals(mainDealServiceType)) {
                // 两个都是"其他"
                return cardTitle;
            } else {
                // 主品和搭售品三级类目相同
                cardTitle = mainDealServiceType + TAO_CAN_TEXT;
            }
        } else if (QI_TA_SERVICE_TYPE.equals(mainDealServiceType) || QI_TA_SERVICE_TYPE.equals(bindingDealServiceType)) {
            // 主品和搭售品有一个是其他
            cardTitle = (mainDealServiceType + bindingDealServiceType).replace(QI_TA_SERVICE_TYPE, StringUtils.EMPTY) + TAO_CAN_TEXT;
        } else {
            cardTitle = dealGroupDTOMap.get(mainDealId).getCategory().getServiceType()
                    + "+"
                    + dealGroupDTOMap.get(bindingDealId).getCategory().getServiceType();
        }

        return cardTitle.replace("套餐套餐", TAO_CAN_TEXT);
    }

    private boolean isMainApp(EnvCtx envCtx) {
        return DztgClientTypeEnum.MEITUAN_APP.equals(envCtx.getDztgClientTypeEnum())
                || DztgClientTypeEnum.DIANPING_APP.equals(envCtx.getDztgClientTypeEnum());
    }

    public List<CombinationDealInfo> convertToCombinationDealInfo(List<RecommendDTO> recommendDTOS) {
        List<CombinationDealInfo> combinationDealInfos = Lists.newArrayList();
        if (CollectionUtils.isEmpty(recommendDTOS)) {
            return combinationDealInfos;
        }

        recommendDTOS.forEach(recommendDTO -> {
            CombinationDealInfo combinationDealInfo = new CombinationDealInfo();
            Map<String, Object> bizDataMap = recommendDTO.getBizData();
            // 如果map为空或者不包含主品和搭售品id，直接跳过
            if (MapUtils.isEmpty(bizDataMap) || !bizDataMap.containsKey(MAIN_DEAL_KEY) || !bizDataMap.containsKey(BINDING_DEAL_KEY)) {
                return;
            }
            // 剔除无效推荐数据
            if (!String.valueOf(bizDataMap.get("is_valid")).equals("1")) {
                return;
            }
            combinationDealInfo.setItemId(recommendDTO.getItem());
            combinationDealInfo.setMainDealId(NumberUtils.toInt(String.valueOf(bizDataMap.get(MAIN_DEAL_KEY))));
            combinationDealInfo.setBindingDealId(NumberUtils.toInt(String.valueOf(bizDataMap.get(BINDING_DEAL_KEY))));
            combinationDealInfo.setBindingSource(BindingSourceEnum.ALGORITHM.getValue());
            combinationDealInfos.add(combinationDealInfo);
        });
        return combinationDealInfos;
    }

    private AbConfig getAbStrategy(EnvCtx envCtx) {
        ModuleAbConfig moduleAbConfig = douHuBiz.getAbExpResult(envCtx, envCtx.isMt() ? "jointBuyUnify_MT" : "jointBuyUnify_DP");
        if (moduleAbConfig == null || CollectionUtils.isEmpty(moduleAbConfig.getConfigs())) {
            return null;
        }
        return moduleAbConfig.getConfigs().get(0);
    }

    public List<CombinationDealInfo> convertToCombinationDealInfo(String json){
        List<CombinationDealInfo> combinationDealInfos = Lists.newArrayList();
        if (StringUtils.isBlank(json)){
            return combinationDealInfos;
        }
        try{
            JSONObject jsonObject = JSONObject.parseObject(json);
            if (Objects.isNull(jsonObject) || Objects.isNull(jsonObject.get("sortedResult"))){
                return combinationDealInfos;
            }
            List<Map<String, String>> sortedResult = (List<Map<String, String>>) jsonObject.get("sortedResult");
            sortedResult.forEach(e->{
                CombinationDealInfo combinationDealInfo = new CombinationDealInfo();
                String bizDataStr = JSONObject.toJSONString(e.get("bizData"));
                Map<String, String> bizData = JSONObject.parseObject(bizDataStr, Map.class);
                // 剔除无效推荐数据
                if (!String.valueOf(bizData.get("is_valid")).equals("1")) {
                    return;
                }
                // 兜底逻辑 只返回同店数据
                // isSameShop： 1 - 同店、0 - 跨店,见红色部分  2同店&跨店
                if (org.apache.commons.lang3.math.NumberUtils.toInt(bizData.get("isSameShop")) != 1 ){
                    return;
                }
                combinationDealInfo.setItemId(bizData.get("combinationId"));
                combinationDealInfo.setMainDealId(org.apache.commons.lang3.math.NumberUtils.toInt(String.valueOf(bizData.get(MAIN_DEAL_KEY))));
                combinationDealInfo.setBindingDealId(org.apache.commons.lang3.math.NumberUtils.toInt(String.valueOf(bizData.get(BINDING_DEAL_KEY))));
                combinationDealInfo.setBindingSource(BindingSourceEnum.MANUAL.getValue());
                combinationDealInfos.add(combinationDealInfo);
            });
            return combinationDealInfos;
        }catch (Exception e){
            log.error("com.dianping.mobile.mapi.dztgdetail.facade.BuyMoreSaveMoreFacade.convertToCombinationDealInfo(java.lang.String) error:", e);
            return combinationDealInfos;
        }
    }

    private Future getStocksFuture(EnvCtx envCtx, BuyMoreSaveMoreCtx ctx, List<Integer> dealGroupIds) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.BuyMoreSaveMoreFacade.getStocksFuture(EnvCtx,BuyMoreSaveMoreCtx,List)");
        if (Objects.isNull(ctx.getReq().getCityId())) {
            return null;
        }
        List<ProductParam> productParamList = dealGroupIds.stream()
                .map(dealGroupId -> ProductParam.productScene(dealGroupId, ctx.getReq().getCityId()))
                .collect(Collectors.toList());
        SalesDisplayRequest multiRequest = SalesDisplayRequest.multiQuery(productParamList);
        multiRequest.setPlatform(envCtx.isMt() ? SalesPlatform.MT.getValue() : SalesPlatform.DP.getValue());
        Map<String, String> extra = Maps.newHashMap();
        //透传销量区间差异化，反爬标识
        extra.put(SaleConstants.MTSI_FLAG, ctx.getEnvCtx().getMtsiFlag());
        extra.put(SaleConstants.CONFUSION_FLAG, SaleConstants.GENERAL_SECTION);
        multiRequest.setExtra(extra);
        return dealStockSaleWrapper.preUnifiedStocksFuture(multiRequest);
    }

    private Future getDealGroupFuture(EnvCtx envCtx, List<Integer> dealGroupIds) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.BuyMoreSaveMoreFacade.getDealGroupFuture(com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx,java.util.List)");
        Set<Long> dealGroupIdSet = dealGroupIds.stream().map(Integer::longValue).collect(Collectors.toSet());
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(dealGroupIdSet, envCtx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP)
                .basicInfo(DealGroupBasicInfoBuilder.builder().all())
                .image(DealGroupImageBuilder.builder().all())
                .category(DealGroupCategoryBuilder.builder().all())
                .dealGroupStock(DealGroupStockBuilder.builder().all())
                .region(DealGroupRegionBuilder.builder().all())
                .build();
        return queryCenterWrapper.preDealGroupDTO(queryByDealGroupIdRequest);
    }

    private void checkRequest(BuyMoreSaveMoreReq req) {
        Validate.isTrue(req.getSourceType() == 1 || req.getSourceType() == 2);
        Validate.notEmpty(req.getPoiidStr());
        Validate.notNull(req.getLimit());
        Validate.notNull(req.getStart());
    }

    private void initContext(BuyMoreSaveMoreReq req, BuyMoreSaveMoreCtx ctx, EnvCtx envCtx) {
        ctx.setReq(req);
        ctx.setEnvCtx(envCtx);
        // 1. 查询门店后台类目
        Long shopId = NumberUtils.toLong(req.getPoiidStr());
        Set<Integer> shopCategoryIds = poiShopCategoryWrapper.queryShopCategoryIds(shopId, envCtx.isMt());
        ctx.setShopCategoryIds(shopCategoryIds);

        // 2. 查询多SKU销售属性
        SkuSummary skuSummary = getDealGroupSkuSummary(req.getDealGroupId(), envCtx.isMt());
        ctx.setSkuSummary(skuSummary);
    }

    public SkuSummary getDealGroupSkuSummary(Integer dealGroupId, boolean isMt) {
        if (NumbersUtils.lessThanAndEqualZero(dealGroupId)) {
            return null;
        }
        try {
            int dpDealGroupId = dealGroupId;
            if (isMt) {
                dpDealGroupId = dealGroupWrapper.getDpDealGroupId(dealGroupId);
            }
            return multiSkuExpBiz.getSkuSummaryFromCacheWithFailOver(dpDealGroupId);
        } catch (Exception e) {
            log.error("[BuyMoreSaveMoreFacade] getDealGroupSkuSummary err={}, dealGroupId={}, isMt={}", e,
                    dealGroupId, isMt);
            return null;
        }
    }

    public DealProductRequest buildDealProductRequest(EnvCtx ctx, List<Integer> dealIds, Long shopId) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.BuyMoreSaveMoreFacade.buildDealProductRequest(com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx,java.util.List,java.lang.Long)");
        DealProductRequest request = new DealProductRequest();
        request.setProductIds(dealIds);
        request.setPlanId(DZ_DEAL_SUB_TITLE_PLAN_ID);
        // 扩展参数
        Map<String, Object> extParams = Maps.newHashMap();
        extParams.put("dealIds", dealIds);
        extParams.put("clientType", ctx.isMt() ? VCClientTypeEnum.MT_APP.getCode() : VCClientTypeEnum.DP_APP.getCode());
        extParams.put("platform", ctx.isMt() ? VCPlatformEnum.MT.getType() : VCPlatformEnum.DP.getType());
        extParams.put("deviceId", ctx.getAppDeviceId());
        extParams.put("unionId", ctx.getUnionId());
        extParams.put("shopIdForLong", shopId);
        request.setExtParams(extParams);
        return request;
    }
}