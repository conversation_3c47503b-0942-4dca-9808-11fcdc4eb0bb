package com.dianping.mobile.mapi.dztgdetail.helper;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.Objects;

@Slf4j
public class DateHelper {

    /**
     * 判断是否在时间范围内
     * @param startDateStr
     * @param endDateStr
     * @return
     */
    public static boolean isCurrentDateInRange(String startDateStr, String endDateStr) {
        try {
            //使用ISO日期格式, 解析输入的日期字符串
            LocalDate startDate = convertToLocalDate(parseDate(startDateStr));
            LocalDate endDate = convertToLocalDate(parseDate(endDateStr));
            if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
                return false;
            }
            // 获取当前日期
            LocalDate currentDate = LocalDate.now();

            // 检查当前日期是否在输入的日期区间内
            return !currentDate.isBefore(startDate) && !currentDate.isAfter(endDate);
        } catch (Exception e) {
            // 如果输入的日期格式不正确，抛出异常或返回false
            log.error("Invalid date format. Please use ISO format (yyyy-MM-dd), startDateStr={}, endDateStr={}", startDateStr, endDateStr, e);
            return false;
        }
    }

    /**
     * 将字符串转换为时间，兼容 yyyy-MM-dd 和 yyyy-MM-dd HH:mm:ss 格式
     *
     * @param dateStr 日期字符串
     * @return 转换后的 Date 对象
     * @throws ParseException 如果字符串格式不匹配
     */
    public static Date parseDate(String dateStr){
        // 判断dateStr格式，符合哪种日期格式
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        String pattern = "yyyy-MM-dd";
        if (dateStr.contains(":")) {
            pattern = "yyyy-MM-dd HH:mm:ss";
        }
        Date date = null;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            sdf.setLenient(false); // 严格模式，避免无效日期（如 2023-02-30）
            date = sdf.parse(dateStr);
            return date;
        } catch (ParseException e) {
            // 当前格式解析失败，继续尝试下一种格式
            log.error("日期格式解析失败，error:", e);
        }
        // 如果所有格式都解析失败，返回null
        return null;
    }

    /**
     * 将 java.util.Date 转换为 java.time.LocalDate
     *
     * @param date 需要转换的 Date 对象
     * @return 转换后的 LocalDate 对象
     */
    public static LocalDate convertToLocalDate(Date date) {
        if (Objects.isNull(date)) {
            return null;
        }
        // 1. 将 Date 转换为 Instant
        Instant instant = date.toInstant();
        // 2. 使用默认时区将 Instant 转换为 ZonedDateTime
        // 3. 从 ZonedDateTime 中提取 LocalDate
        return instant.atZone(ZoneId.systemDefault()).toLocalDate();
    }
}
