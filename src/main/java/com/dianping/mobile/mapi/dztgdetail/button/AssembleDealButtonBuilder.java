package com.dianping.mobile.mapi.dztgdetail.button;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
import com.dianping.mobile.mapi.dztgdetail.helper.BuyButtonHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;

public class AssembleDealButtonBuilder extends AbstractButtonBuilder {

    @Override
    public void doBuild(DealCtx context, ButtonBuilderChain chain) {
        if (BuyButtonHelper.isValidPinPool(context)) {
            if (context.getPreButton() == null) {
                context.getBuyBar().setBuyType(DealBuyBar.BuyType.PINPOOL.type);
            }
            context.addButton(DealBuyHelper.getAssembleDealButton(context));
        }
        chain.build(context);
    }
}
