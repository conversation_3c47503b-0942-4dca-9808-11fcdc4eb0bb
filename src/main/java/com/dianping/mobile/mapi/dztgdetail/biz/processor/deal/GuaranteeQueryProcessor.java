package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils.isAnXinXueCategoryIds;
import static com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTypeEnum.ANXIN_EXERCISE_GUARANTEE_CONTINUOUS_MONTHLY;
import static com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTypeEnum.ANXIN_EXERCISE_GUARANTEE_NOT_CONTINUOUS_MONTHLY;
import static com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTypeEnum.ANXIN_LEARNING_GUARANTEE;
import static com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTypeEnum.ANXIN_MEDICAL_GUARANTEE;
import static com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTypeEnum.BEST_PRICE_GUARANTEE;
import static com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTypeEnum.PRICE_PROTECTION;
import static com.sankuai.nib.price.operation.common.guarantee.enums.ObjectTypeEnum.PRODUCT;
import static com.sankuai.nib.price.operation.common.guarantee.enums.ReturnModeEnum.PRIORITY_SORT_ONE_GROUP_BY_GUARANTEE_TYPE;

import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Future;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.GuaranteeQueryWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.nib.price.operation.api.common.dto.SessionContextDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.ChannelDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.GuaranteeObjectQueryDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.GuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.TagDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.UserInfoDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.QueryTagOptionDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.request.BatchQueryGuaranteeTagRequest;
import com.sankuai.nib.price.operation.common.guarantee.enums.ChannelNoEnum;
import com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTagNameEnum;
import com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTypeEnum;
import com.sankuai.nib.price.operation.common.guarantee.enums.PlatformEnum;
import com.sankuai.nib.price.operation.common.guarantee.enums.QueryExtKeyEnum;
import com.sankuai.nib.price.operation.common.guarantee.enums.TerminalTypeEnum;
import com.sankuai.nib.sp.common.enums.Owner;
import com.sankuai.nib.sp.common.enums.TradeType;

public class GuaranteeQueryProcessor extends AbsDealProcessor {

    @Autowired
    GuaranteeQueryWrapper guaranteeQueryWrapper;

    /**
     * 安心医标签
     */
    private static final Set<Integer> SAFE_MEDICAL_TAGS = Sets.newHashSet(
            GuaranteeTagNameEnum.ANXIN_MEDICAL_IMPLANT_GUARANTEE.getCode(),
            GuaranteeTagNameEnum.ANXIN_MEDICAL_FILL_GUARANTEE.getCode());

    /**
     * 口腔-放心种植标签
     */
    private static final Set<Integer> SAFE_IMPLANT_TAGS = Sets.newHashSet(
            GuaranteeTagNameEnum.REASSURING_DENTAL_IMPLANT_3_YEARS_GUARANTEE.getCode(),
            GuaranteeTagNameEnum.REASSURING_DENTAL_IMPLANT_5_YEARS_GUARANTEE.getCode());

    @Override
    public boolean isEnable(DealCtx ctx) {
        return true;
    }

    @Override
    public void prepare(DealCtx ctx) {
        BatchQueryGuaranteeTagRequest request = buildRequest(ctx);
        SessionContextDTO sessionContextDTO = buildSessionContextDTO(ctx);
        Future priceProtectionFuture = guaranteeQueryWrapper.preGetGuaranteeTagDTOs(sessionContextDTO, request);
        ctx.getFutureCtx().setGuaranteeQueryFuture(priceProtectionFuture);
    }
    private SessionContextDTO buildSessionContextDTO(DealCtx ctx){
        SessionContextDTO sessionContext = new SessionContextDTO();
        sessionContext.setOwner(Owner.NIB_GENERAL.getValue());
        // 判断是否是教育行业
        if (isAnXinXueCategoryIds(ctx.getCategoryId())){
            sessionContext.setTradeType(TradeType.COUNT_CARD.getCode());
        }else {
            // 原有逻辑
            sessionContext.setTradeType(TradeType.GROUPBUY_PAY.getCode());
        }

        return sessionContext;
    }

    private BatchQueryGuaranteeTagRequest buildRequest(DealCtx ctx) {
        BatchQueryGuaranteeTagRequest request = new BatchQueryGuaranteeTagRequest();
        request.setObjects(getObjects(ctx));
        request.setGuaranteeTypes(getGuaranteeTypes());
        request.setUserInfo(getUserInfoDTO(ctx));
        request.setQueryTagOption(getQueryTagOption());
        return request;
    }

    public Set<GuaranteeObjectQueryDTO> getObjects(DealCtx ctx) {
        if (ctx.getPtId() == null) {
            return new HashSet<>();
        }
        Set<GuaranteeObjectQueryDTO> objects = new HashSet<>();
        GuaranteeObjectQueryDTO object = new GuaranteeObjectQueryDTO();
        object.setObjectId(String.valueOf(ctx.getPtId()));
        object.setObjectType(PRODUCT.getCode());
        objects.add(object);
        object.setExt(getExt(ctx));
        return objects;
    }

    private Map<String, String> getExt(DealCtx ctx) {
        //齿科
        if (ctx.getCategoryId() == 506){
            Map<String, String> ext = new HashMap<>();
            ext.put(QueryExtKeyEnum.POI_ID.getCode(), String.valueOf(ctx.getMtLongShopId()));
            return ext;
        }
        return Maps.newHashMap();
    }

    private Set<Integer> getGuaranteeTypes() {
        Set<Integer> guaranteeTypes = new HashSet<>();
        guaranteeTypes.add(PRICE_PROTECTION.getCode());
        guaranteeTypes.add(BEST_PRICE_GUARANTEE.getCode());
        // 安心医、放心种植用的都是 ANXIN_MEDICAL_GUARANTEE
        guaranteeTypes.add(ANXIN_MEDICAL_GUARANTEE.getCode());
        guaranteeTypes.add(ANXIN_LEARNING_GUARANTEE.getCode());
        // 安心练需求
        guaranteeTypes.add(ANXIN_EXERCISE_GUARANTEE_CONTINUOUS_MONTHLY.getCode());
        guaranteeTypes.add(ANXIN_EXERCISE_GUARANTEE_NOT_CONTINUOUS_MONTHLY.getCode());
        return guaranteeTypes;
    }

    public UserInfoDTO getUserInfoDTO(DealCtx ctx) {
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        ChannelDTO channelDTO = new ChannelDTO();

        PlatformEnum platformEnum;
        if (ctx.isMt()) {
            platformEnum = PlatformEnum.MT_PLATFORM;
        } else {
            platformEnum = PlatformEnum.DP_PLATFORM;
        }
        channelDTO.setPlatform(platformEnum.getCode());

        TerminalTypeEnum terminalTypeEnum;
        if (ctx.getEnvCtx().judgeMainApp()) {
            terminalTypeEnum = TerminalTypeEnum.APP;
        } else if (ctx.getEnvCtx().isWxMini()) {
            terminalTypeEnum = TerminalTypeEnum.APPLETS;
        } else if (ctx.getEnvCtx().isMainWeb()) {
            terminalTypeEnum = TerminalTypeEnum.PC;
        } else {
            terminalTypeEnum = TerminalTypeEnum.MOBILE;
        }
        channelDTO.setTerminalType(terminalTypeEnum.getCode());

        ChannelNoEnum channelNoEnum;
        if (RequestSourceEnum.LIVE_STREAM.getSource().equals(ctx.getRequestSource())) {
            channelNoEnum = ChannelNoEnum.LIVE_STREAMING;
        } else {
            channelNoEnum = ChannelNoEnum.UNKNOWN;
        }
        channelDTO.setChannelNo(channelNoEnum.getCode());

        userInfoDTO.setChannel(channelDTO);
        return userInfoDTO;
    }

    public QueryTagOptionDTO getQueryTagOption(){
        QueryTagOptionDTO queryTagOptionDTO=new QueryTagOptionDTO();
        queryTagOptionDTO.setReturnMode(PRIORITY_SORT_ONE_GROUP_BY_GUARANTEE_TYPE.getCode());
        return queryTagOptionDTO;
    }

    @Override
    public void process(DealCtx ctx) {
        List<ObjectGuaranteeTagDTO> objectGuaranteeTagDTOS = guaranteeQueryWrapper.getGuaranteeTagDTOs(ctx.getFutureCtx().getGuaranteeQueryFuture());
        if (CollectionUtils.isNotEmpty(objectGuaranteeTagDTOS)) {
            // 价保
            ObjectGuaranteeTagDTO priceProtectionInfo = objectGuaranteeTagDTOS.stream().filter(Objects::nonNull).filter(o -> o.getPriceProtectionTag() != null).findFirst().orElse(null);
            ctx.setPriceProtectionInfo(priceProtectionInfo);
            // 买贵必赔
            ObjectGuaranteeTagDTO bestPriceGuaranteeInfo = objectGuaranteeTagDTOS.stream().filter(Objects::nonNull).filter(o -> o.getBestPriceGuaranteeTagDTO() != null).findFirst().orElse(null);
            ctx.setBestPriceGuaranteeInfo(bestPriceGuaranteeInfo);
            // 安心医
            processSafeMedicalTag(ctx, objectGuaranteeTagDTOS);
            //安心学
            processAnXinXue(ctx, objectGuaranteeTagDTOS);
            // 放心种植
            processSafeImplantTag(ctx, objectGuaranteeTagDTOS);
            // 安心练
            processAnXinExercise(ctx, objectGuaranteeTagDTOS);
        }
    }

    public void processSafeMedicalTag(DealCtx ctx, List<ObjectGuaranteeTagDTO> objectGuaranteeTagDTOS) {
        List<Integer> anXinMedicalGuaranteeInfo = objectGuaranteeTagDTOS.stream()
                .filter(Objects::nonNull)
                .filter(o -> Objects.equals(ANXIN_MEDICAL_GUARANTEE.getCode(),o.getGuaranteeType()))
                .findFirst()
                .map(ObjectGuaranteeTagDTO::getGuaranteeTag)
                .map(GuaranteeTagDTO::getGuaranteeTagNames)
                .orElse(Collections.emptyList());
        ctx.setSafeMedicalTag(anXinMedicalGuaranteeInfo);
    }

    public void processSafeImplantTag(DealCtx ctx, List<ObjectGuaranteeTagDTO> guaranteeTagDTOS) {
        if (Objects.isNull(guaranteeTagDTOS)) {
            return;
        }
        Optional<TagDTO> safeImplantTagOpt = guaranteeTagDTOS.stream()
                .filter(Objects::nonNull)
                .map(ObjectGuaranteeTagDTO::getTags)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(tag -> SAFE_IMPLANT_TAGS.contains(tag.getCode()))
                .findFirst();
        ctx.setSafeImplantTag(safeImplantTagOpt.orElse(null));
    }

    public void processAnXinXue(DealCtx ctx, List<ObjectGuaranteeTagDTO> objectGuaranteeTagDTOS){
        if (CollectionUtils.isNotEmpty(objectGuaranteeTagDTOS)) {
            //安心学
            List<TagDTO> anXinXueGuaranteeInfo = objectGuaranteeTagDTOS.stream()
                    .filter(Objects::nonNull)
                    .findFirst()
                    .map(ObjectGuaranteeTagDTO::getTags)
                    .orElse(Collections.emptyList());
            int tagCode = anXinXueGuaranteeInfo.stream()
                    .filter(Objects::nonNull)
                    .filter(tagDTO->GuaranteeTagNameEnum.ANXIN_LEARNING_GUARANTEE.getCode() == tagDTO.getCode())
                    .findFirst().map(TagDTO::getCode)
                    .orElse(-1);
            if (GuaranteeTagNameEnum.ANXIN_LEARNING_GUARANTEE.getCode() == tagCode) {
                ctx.setAnXinXue(true);
            }
        }
    }

    public void processAnXinExercise(DealCtx ctx, List<ObjectGuaranteeTagDTO> objectGuaranteeTagDTOS){
        boolean isAnXinExercise = Optional.ofNullable(objectGuaranteeTagDTOS).orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .map(ObjectGuaranteeTagDTO::getGuaranteeType)
                .filter(Objects::nonNull)
                .anyMatch(GuaranteeTypeEnum::isAnXinExerciseGuarantee);

        ctx.setAnXinExercise(isAnXinExercise);
    }
}
