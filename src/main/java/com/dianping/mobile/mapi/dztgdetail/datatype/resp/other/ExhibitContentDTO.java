package com.dianping.mobile.mapi.dztgdetail.datatype.resp.other;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ExhibitImageItemVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2023/8/29
 */
@Data
@TypeDoc(description = "款式信息")
@MobileDo(id = 0x138036ff)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ExhibitContentDTO implements Serializable {
    @FieldDoc(description = "小标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "展示样式，1-单图展示；2-多图展示")
    @MobileDo.MobileField(key = 0x4736)
    private Integer itemDisplayStyle;

    @FieldDoc(description = "款式详情信息")
    @MobileDo.MobileField(key = 0xe23d)
    private List<ExhibitImageItemVO> items;

    @FieldDoc(description = "查看全部文案")
    @MobileDo.MobileField(key = 0xb075)
    private String showAllText;

    @FieldDoc(description = "记录总数")
    @MobileDo.MobileField(key = 0xb3c7)
    private Integer recordCount;

    @FieldDoc(description = "点击右侧箭头的跳链")
    @MobileDo.MobileField(key = 0x774e)
    private String jumpUrl;

    @FieldDoc(description = "款式缩略图右侧箭头")
    @MobileDo.MobileField(key = 0x3af1)
    private String arrowIcon;

    @FieldDoc(description = "服务类型Id, 123027表示穿戴甲")
    @MobileDo.MobileField(key = 0x4fd9)
    private long serviceTypeId;

    /**
     * 从0.5.14版本之后开始使用
     */
    @FieldDoc(description = "新版查看全部，使用商家侧页面")
    @MobileDo.MobileField(key = 0x3a2c)
    private String merchantJumpUrl;

    /**
     * 从0.5.14版本之后开始使用
     */
    @FieldDoc(description = "新版头图跳链，使用商家侧页面")
    @MobileDo.MobileField(key = 0x3145)
    private String merchantHeadPictureJumpUrl;

    /**
     * 从0.5.14版本之后开始使用
     */
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    @FieldDoc(description = "是否新样式，使用商家侧页面")
    @MobileDo.MobileField(key = 0x4f4)
    private boolean merchantStyle;

    @FieldDoc(description = "图片找相似标识 0-不支持 1-居中搜索")
    @MobileDo.MobileField(key = 0x1620)
    private int similarSearchType;

}
