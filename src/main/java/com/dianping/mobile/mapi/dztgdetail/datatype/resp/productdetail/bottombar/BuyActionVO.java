package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.action.enums.OpenTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.enums.BottomBarActionTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/3/16 15:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class BuyActionVO extends BottomBarActionDataVO {

    private final int actionType = BottomBarActionTypeEnum.BUY.getCode();

    /**
     * 打开类型
     * redirect=直接跳转
     * modal=打开浮层
     *
     * @see OpenTypeEnum
     */
    private String openType;

    /**
     * 跳转链接
     */
    private String url;

    /**
     * 购买前批量发券
     */
    private List<String> coupon;

    public BuyActionVO(OpenTypeEnum openType, String url) {
        this.openType = openType.name();
        this.url = url;
    }

    public BuyActionVO(OpenTypeEnum openType, String url, Set<Long> couponGroupIds) {
        this.openType = openType.name();
        this.url = url;
        if (CollectionUtils.isNotEmpty(couponGroupIds)) {
            this.coupon = couponGroupIds.stream()
                    .filter(Objects::nonNull)
                    .map(String::valueOf)
                    .collect(Collectors.toList());
        }
    }

}