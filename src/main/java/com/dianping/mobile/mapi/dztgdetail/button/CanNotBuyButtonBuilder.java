package com.dianping.mobile.mapi.dztgdetail.button;

import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import org.apache.commons.lang3.StringUtils;

import com.dianping.mobile.mapi.dztgdetail.common.enums.SaleStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;

public class CanNotBuyButtonBuilder extends AbstractButtonBuilder {

    @Override
    public void doBuild(DealCtx context, ButtonBuilderChain chain) {
        DealBuyBtn canNotBuyButton = DealBuyHelper.getCanNotBuyButton(context);

        if (canNotBuyButton != null) {
            context.setCanNotBuy(true);

            //若是预热单，更新按钮的售卖状态和title
            if(StringUtils.isNotBlank(context.getSaleStatus()) && !context.getSaleStatus().equals(SaleStatusEnum.SNAP_UP_NOW.saleStatusName)){
                canNotBuyButton.setSaleStatus(context.getSaleStatus());
                canNotBuyButton.setBtnTitle(getSaleStatusButtonTitle(context));
            }

            context.addButton(canNotBuyButton);
            chain.interrupt();
            return;
        }
        chain.build(context);
    }
}
