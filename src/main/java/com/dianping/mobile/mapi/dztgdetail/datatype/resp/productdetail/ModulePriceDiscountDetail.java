package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PromoDetailModule;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/3/3 15:00
 */
@Data
@MobileDo(id = 0x63b9)
public class ModulePriceDiscountDetail extends AbstractModuleVO {

    @FieldDoc(description = "优惠栏模块")
    @MobileField(key = 0xd48c)
    private List<PromotionBarModule> promotionBar;

    @FieldDoc(description = "优惠明细")
    @MobileField(key = 0xc10c)
    private PromoDetailModule promoDetails;


    @FieldDoc(description = "优惠浮层模块")
    @MobileField(key = 0x4dcd)
    private PromotionPopUpModule promotionPopUpModule;

    @FieldDoc(description = "模块名")
    @MobileField(key = 0xb308)
    private String moduleName;

    private String moduleKeyExtend;

    @Override
    public String getModuleKey() {
        return "module_price_discount_detail";
    }
}