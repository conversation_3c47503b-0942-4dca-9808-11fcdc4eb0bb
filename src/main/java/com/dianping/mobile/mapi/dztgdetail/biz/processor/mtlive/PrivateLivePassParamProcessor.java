package com.dianping.mobile.mapi.dztgdetail.biz.processor.mtlive;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PrivateLiveWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.common.ResponseDTO;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.LiveRoomDistributionInfo;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.Future;


/**
 * <AUTHOR>
 */
public class PrivateLivePassParamProcessor extends AbsDealProcessor {

    @Resource
    private PrivateLiveWrapper privateLiveWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        return ctx.isMtLiveMinApp();
    }

    @Override
    public void prepare(DealCtx ctx) {
        DealBaseReq req = ctx.getDealBaseReq();
        if (Objects.isNull(req)) {
            return;
        }
        Future future = privateLiveWrapper.preQueryLiveDistributionInfo(req.getPrivateLiveId());
        ctx.getFutureCtx().setPrivateLiveDistributionInfoFuture(future);
    }

    @Override
    public void process(DealCtx ctx) {
        try{
            ResponseDTO<LiveRoomDistributionInfo> responseDTO = privateLiveWrapper.getFutureResult(ctx.getFutureCtx().getPrivateLiveDistributionInfoFuture());
            if (responseDTO != null && responseDTO.isSuccess() && Objects.nonNull(responseDTO.getData())){
                LiveRoomDistributionInfo liveRoomDistributionInfo = responseDTO.getData();
                ctx.setLiveRoomDistributionInfo(liveRoomDistributionInfo);
            }
        }catch (Exception e) {
            logger.error("PrivateLivePassParamProcessor.process error:", e);
        }
    }
}
