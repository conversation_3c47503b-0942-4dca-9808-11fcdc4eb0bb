package com.dianping.mobile.mapi.dztgdetail.common.enums;

public interface MtDetailProductType {

    /**
     * 常规单（默认的商品类型）
     */
    int PRODUCT_TYPE_NORMAL = 1;
    /**
     * 抽奖单
     */
    int PRODUCT_TYPE_LOTTERY = 2;
    /**
     * 秒杀单
     */
//    int PRODUCT_TYPE_SECKILL = 3;
    /**
     * 储值卡
     */
    int PRODUCT_TYPE_PREPAIDCARD = 4;
    /**
     *  老的储值卡
     */
    int PRODUCT_TYPE_OLD_PREPAIDCARD = -4;
    /**
     *  实物商品
     */
    int PRODUCT_TYPE_GOODS = -1;
    /**
     *  酒店商品
     */
    int PRODUCT_TYPE_HOTEL = 5;
    /**
     * 旅游门票
     */
    int PRODUCT_TYPE_TRIPTICKET = 6;
}