package com.dianping.mobile.mapi.dztgdetail.rcf.repository.switcher;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.common.util.Version;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgPlatformEnum;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.constant.DealRcfNativeSnapshotConstant;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.switcher.dto.DealRcfSwitcherResult;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;

/**
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2024/11/18 19:20
 */
@Component
public class DealRcfSwitcherService {

    public static DealRcfConfigDTO CONFIG_LOCAL_CACHE = new DealRcfConfigDTO();

    private static void parse(String json) {
        CONFIG_LOCAL_CACHE = JSON.parseObject(json, DealRcfConfigDTO.class);
    }

    static {
        parse(Lion.getStringValue(DealRcfNativeSnapshotConstant.CONFIG_LION_KEY));
        Lion.addConfigListener(DealRcfNativeSnapshotConstant.CONFIG_LION_KEY, configEvent -> parse(configEvent.getValue()));
    }

    public DealRcfSwitcherResult get(final long dealCategoryId,
                                     final long serviceTypeId,
                                     final DztgClientTypeEnum clientType,
                                     final long userId,
                                     final String appVersion,
                                     final String mrnVersion) {
        if (!CONFIG_LOCAL_CACHE.isValidClientType(clientType)) {
            return DealRcfSwitcherResult.getDefaultConfig("INVALID_CLIENT_TYPE");
        }
        if (!CONFIG_LOCAL_CACHE.isValidAppVersion(appVersion, clientType)) {
            return DealRcfSwitcherResult.getDefaultConfig("INVALID_APP_VERSION");
        }
        if (!CONFIG_LOCAL_CACHE.isValidMrnVersion(mrnVersion, clientType)) {
            return DealRcfSwitcherResult.getDefaultConfig("INVALID_MRN_VERSION");
        }
        if (CONFIG_LOCAL_CACHE.hitBlackList(dealCategoryId, serviceTypeId)) {
            return DealRcfSwitcherResult.getDefaultConfig("INVALID_CATEGORY");
        }
        boolean isRender;
        boolean isReport;
        boolean isUserInWhiteList = CONFIG_LOCAL_CACHE.isUserInWhiteList(userId, clientType.getPlatform());
        if (isUserInWhiteList) {
            isRender = true;
            isReport = true;
        }else{
            isRender = CONFIG_LOCAL_CACHE.isRender(dealCategoryId);
            isReport = CONFIG_LOCAL_CACHE.isReport(dealCategoryId);
        }
        return new DealRcfSwitcherResult(isRender, isReport, "INVALID_CATEGORY");
    }

    @Data
    public static class DealRcfConfigDTO {

        /**
         * 适用的端
         *
         * @see DztgClientTypeEnum
         */
        private Set<Integer> clientTypeFilter;

        /**
         * 团单三级类目黑名单开关
         */
        private boolean blackServiceTypeIdFilter;
        /**
         * 团单三级类目黑名单列表
         */
        private Set<Long> blackServiceTypeIdList;

        /**
         * 团单二级类目黑名单总开关
         */
        private boolean blackDealCategoryFilter;
        /**
         * 团单二级类目黑名单列表
         */
        private Set<Long> blackDealCategoryList;



        /**
         * 是否需要闪开的category白名单总开关
         */
        private boolean dealCategoryFilter;

        /**
         * 闪开白名单
         */
        private Set<Long> dealCategoryList;

        /**
         * 是否需要上报的category白名单总开关
         */
        private boolean dealCategoryReportFilter;

        /**
         * 上报白名单
         */
        private Set<Long> dealCategoryReportList;

        /**
         * 点评用户Id白名单
         */
        private Set<Long> dpUserIdList;

        /**
         * 美团用户Id白名单
         */
        private Set<Long> mtUserIdList;

        private Map<Integer, String> appVersionFilter;
        private Map<Integer, String> mrnVersionFilter;

        public boolean isValidClientType(final DztgClientTypeEnum clientType) {
            if (CollectionUtils.isEmpty(clientTypeFilter)) {
                return false;
            }
            return clientTypeFilter.contains(clientType.getCode());
        }

        public boolean isRender(final long dealCategoryId) {
            if (dealCategoryFilter) {
                return CollectionUtils.isNotEmpty(dealCategoryList) && dealCategoryList.contains(dealCategoryId);
            }
            return true;
        }

        public boolean isReport(final long dealCategoryId) {
            if (dealCategoryReportFilter) {
                return CollectionUtils.isNotEmpty(dealCategoryReportList) && dealCategoryReportList.contains(dealCategoryId);
            }
            return true;
        }

        public boolean isUserInWhiteList(final long userId,
                                         final DztgPlatformEnum platform) {
            if (userId <= 0) {
                return false;
            }
            if (platform == DztgPlatformEnum.MT) {
                return CollectionUtils.isNotEmpty(mtUserIdList) && mtUserIdList.contains(userId);
            } else if (platform == DztgPlatformEnum.DP) {
                return CollectionUtils.isNotEmpty(dpUserIdList) && dpUserIdList.contains(userId);
            }
            return false;
        }

        public boolean isValidAppVersion(final String appVersion,
                                         final DztgClientTypeEnum clientType) {
            if (MapUtils.isEmpty(appVersionFilter)) {
                return true;
            }
            String appVersionThreshold = appVersionFilter.get(clientType.getCode());
            if (StringUtils.isBlank(appVersionThreshold)) {
                return true;
            }
            return Version.compareTo(appVersion, appVersionThreshold) >= 0;
        }

        public boolean isValidMrnVersion(final String mrnVersion,
                                         final DztgClientTypeEnum clientType) {
            if (MapUtils.isEmpty(mrnVersionFilter)) {
                return true;
            }
            String mrnVersionThreshold = mrnVersionFilter.get(clientType.getCode());
            if (StringUtils.isBlank(mrnVersionThreshold)) {
                return true;
            }
            return Version.compareTo(mrnVersion, mrnVersionThreshold) >= 0;
        }

        /**
         * 是否命中三级类目黑名单
         * @param serviceTypeId
         * @return
         */
        public boolean hitServiceTypeIdBlackList(long serviceTypeId) {
            if (blackServiceTypeIdFilter){
                return CollectionUtils.isNotEmpty(blackServiceTypeIdList) && blackServiceTypeIdList.contains(serviceTypeId);
            }
            return false;
        }

        /**
         * 是否命中二级类目黑名单
         * @param dealCategoryId
         * @return
         */
        public boolean hitCategoryBlackList(long dealCategoryId) {
            if (blackDealCategoryFilter){
                return CollectionUtils.isNotEmpty(blackDealCategoryList) && blackDealCategoryList.contains(dealCategoryId);
            }
            return false;
        }

        /**
         * 判断是否命中黑名单
         * @param dealCategoryId
         * @param serviceTypeId
         * @return
         */
        public boolean hitBlackList(long dealCategoryId, long serviceTypeId){
            return hitCategoryBlackList(dealCategoryId)
                    || hitServiceTypeIdBlackList(serviceTypeId);
        }
    }

}
