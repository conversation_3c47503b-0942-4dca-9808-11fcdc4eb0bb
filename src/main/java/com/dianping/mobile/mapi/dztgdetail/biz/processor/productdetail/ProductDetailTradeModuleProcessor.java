package com.dianping.mobile.mapi.dztgdetail.biz.processor.productdetail;

import com.alibaba.fastjson.JSONObject;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperCacheWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ProductDetailWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.util.DealSkuUtils;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.TimesDealUtil;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.dz.product.detail.gateway.api.bff.enums.PageConfigSceneEnum;
import com.sankuai.dz.product.detail.gateway.api.bff.request.PageConfigRoutingKey;
import com.sankuai.dz.product.detail.gateway.spi.dto.CustomParam;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.GpsCoordinateTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericProductDetailPageResponse;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Future;

import static com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.DP_APP;
import static com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.DP_XCX;
import static com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.MT_APP;
import static com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.MT_XCX;
import static com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.UNKNOWN;

/**
 * <AUTHOR>
 * @date 2025-04-07
 * @desc
 */
@Slf4j
public class ProductDetailTradeModuleProcessor extends AbsDealProcessor {

    private static final Set<String> MAGIC_COUPON_ENHANCEMENT_MODULE_KEYS = Sets.newHashSet("module_detail_deal_atmosphere_price_sale_bar", "module_detail_deal_price_sale_bar", "module_price_discount_detail", "module_detail_deal_bottom_bar");
    private static final Set<String> COUNTRY_SUBSIDIES_MODULE_KEYS = Sets.newHashSet("module_detail_deal_atmosphere_price_sale_bar", "module_detail_deal_multi_sku_select", "module_price_discount_detail", "module_detail_deal_bottom_bar");
    private static final Set<String> MONTHLY_SUBSCRIPTION_MODULE_KEYS = Sets.newHashSet("module_detail_deal_price_sale_bar", "module_detail_deal_bottom_bar");

    @Resource
    private ProductDetailWrapper productDetailWrapper;
    @Autowired
    private MapperCacheWrapper mapperCacheWrapper;

    @Resource
    private DouHuService douHuService;

    private static final List<String> EXP_RESULTS = Lists.newArrayList("c","d");

    @Override
    public boolean isEnable(DealCtx ctx) {
        try {
            boolean hasSuperCouponScene = hasSuperCouponScene(ctx);
            ctx.setHasSuperCouponScene(hasSuperCouponScene);
            boolean countrySubsidiesProduct = isCountrySubsidiesProduct(ctx);
            ctx.setCountrySubsidiesProduct(countrySubsidiesProduct);
            // (属于神券感知氛围场景 + 在实验组) or 国补商品 or 连续包月
            return showMagicCouponEnhancement(ctx) || countrySubsidiesProduct || isMonthlySubscription(ctx);
        } catch (Exception e) {
            log.error("ProductDetailTradeModuleProcessor isEnable error", e);
            return false;
        }
    }

    public boolean isMonthlySubscription(DealCtx ctx) {
        if (Objects.isNull(ctx.getDealGroupDTO())) {
            return false;
        }
        List<String> serviceTypeIds = LionConfigUtils.getMonthlySubscriptionCategoryList(String.valueOf(ctx.getCategoryId()));
        if (CollectionUtils.isEmpty(serviceTypeIds)) {
            return false;
        }
        Long serviceTypeId = ctx.getDealGroupDTO().getCategory().getServiceTypeId();
        return (ctx.getEnvCtx().getDztgClientTypeEnum() == DztgClientTypeEnum.MEITUAN_APP ||
                ctx.getEnvCtx().getDztgClientTypeEnum() == DztgClientTypeEnum.DIANPING_APP ) &&
                serviceTypeIds.contains(String.valueOf(serviceTypeId)) &&
                TimesDealUtil.isMonthlySubscription(ctx);
    }

    private boolean showMagicCouponEnhancement(DealCtx ctx) {
        boolean hasSuperCouponScene = hasSuperCouponScene(ctx);
        // (属于神券感知氛围场景 + 在实验组)
        return hasSuperCouponScene && isHitExp(ctx);
    }

    /**
     * 神券感知覆盖场景
     * 美团APP + 不在黑名单城市 + 不是次卡 + 不是预约留资团单 + 非国家补贴类目 + 非屏蔽渠道 + 相似团单 +  足疗多SKU
     * @param ctx 团单上下文
     * @return 是否满足神券感知覆盖场景的条件
     */
    private boolean hasSuperCouponScene(DealCtx ctx) {
        VCClientTypeEnum clientTypeEnum = ctx.isMt() ? VCClientTypeEnum.MT_APP : VCClientTypeEnum.DP_APP;
        String pageSource =  StringUtils.isEmpty(matchPageSource(ctx.getRequestSource())) ? "" : matchPageSource(ctx.getRequestSource());
        String cityId = String.valueOf(ctx.getMtCityId());
        String keyStr = String.format("%s-%s-%s", ctx.getDealId4P(), pageSource, StringUtils.isEmpty(cityId) ? "0" : cityId);
        boolean isSimilarDeal = mapperCacheWrapper.fetchSimilarDealId(clientTypeEnum.getCode(), keyStr);

        return ctx.getEnvCtx().getDztgClientTypeEnum() == DztgClientTypeEnum.MEITUAN_APP
                && !isBlackCity(ctx)
                && !TimesDealUtil.isMultiTimesCard(ctx)
                && !isLeadDeal(ctx)
                && !LionConfigUtils.isCountrySubsidyDealServiceTypeId(ctx.getDealGroupDTO())
                && !isHidePageSource(ctx)
                && !isSimilarDeal
                && !isMassageMultipleSku(ctx);
    }

    /**
     * 足疗多SKU
     * @return
     */
    private boolean isMassageMultipleSku(DealCtx ctx) {
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        if (Objects.isNull(dealGroupDTO) || CollectionUtils.isEmpty(dealGroupDTO.getDeals())) {
            return false;
        }
        // 有效skuId过滤
        List<DealGroupDealDTO> dealDTOS = DealSkuUtils.getValidDealSkuList(dealGroupDTO.getDeals());
        return CollectionUtils.isNotEmpty(dealDTOS);
    }

    @Override
    public void prepare(DealCtx ctx) {
        try {
            // 构造请求体
            ProductDetailPageRequest request = buildProductDetailPageRequest(ctx);
            if (Objects.isNull(request)) {
                return;
            }
            Future<?> future = productDetailWrapper.preQueryProductDetailTradeModule(request);
            ctx.getFutureCtx().setProductDetailTradeModuleFuture(future);
        } catch (Exception e) {
            log.error("ProductDetailTradeModuleProcessor prepare error, ctx:{}", ctx, e);
        }
    }

    @Override
    public void process(DealCtx ctx) {
        if (Objects.isNull(ctx.getFutureCtx().getProductDetailTradeModuleFuture())) {
            return;
        }
        String json = productDetailWrapper.getFutureResult(ctx.getFutureCtx().getProductDetailTradeModuleFuture());
        if (StringUtils.isBlank(json)) {
            return;
        }
        GenericProductDetailPageResponse response = JSONObject.parseObject(json, GenericProductDetailPageResponse.class);
        ctx.setProductDetailTradeModuleResponse(response);
    }

    private ProductDetailPageRequest buildProductDetailPageRequest(DealCtx ctx) {
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        // 强卡二级类目，如果没有二级类目，则不请求新接口
        if (Objects.isNull(dealGroupDTO) || Objects.isNull(dealGroupDTO.getCategory())
                || Objects.isNull(dealGroupDTO.getCategory().getCategoryId())) {
            return null;
        }
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        EnvCtx envCtx = ctx.getEnvCtx();
        boolean isMt = envCtx.isMt();
        // regionid、regionVersion、regionType
        request.setCityId(ctx.getCityId4P());
        request.setCityLat(ctx.getCityLatitude());
        request.setCityLng(ctx.getCityLongitude());
        ClientTypeEnum clientTypeEnum = getClientType(envCtx);
        request.setClientType(clientTypeEnum.getCode());
        request.setCx(ctx.getCx());
        request.setGpsCityId(ctx.getGpsCityId());
        request.setGpsCoordinateType(GpsCoordinateTypeEnum.GCJ02.getCode());
        // mrn version
        ShepherdGatewayParam shepherdGatewayParam = new ShepherdGatewayParam();
        shepherdGatewayParam.setMrnVersion(ctx.getMrnVersion());
        shepherdGatewayParam.setCsecversionname(ctx.getWxVersion());
        shepherdGatewayParam.setUnionid(envCtx.getUnionId());
        if (isMt) {
            shepherdGatewayParam.setDeviceId(envCtx.getUuid());
        } else {
            shepherdGatewayParam.setDeviceId(envCtx.getDpId());
        }
        shepherdGatewayParam.setMtUserId(envCtx.getMtUserId());
        shepherdGatewayParam.setDpUserId(envCtx.getDpUserId());
        shepherdGatewayParam.setMtVirtualUserId(envCtx.getMtVirtualUserId());
        shepherdGatewayParam.setDpVirtualUserId(envCtx.getDpVirtualUserId());
        shepherdGatewayParam.setAppVersion(envCtx.getVersion());
        shepherdGatewayParam.setMobileOSType("ios");
        request.setShepherdGatewayParam(shepherdGatewayParam);
        request.setProductId(ctx.getDealId4P());
        request.setProductType(ProductTypeEnum.DEAL.getCode());
        request.setUserLat(ctx.getUserlat());
        request.setUserLng(ctx.getUserlng());
        request.setPoiId(ctx.getLongPoiId4PFromResp());
        request.setCityLat(ctx.getCityLatitude());
        request.setCityLng(ctx.getCityLongitude());
        request.setPageSource(ctx.getRequestSource());
        CustomParam customParam = new CustomParam();
        // 流量标识
        if (showMagicCouponEnhancement(ctx)) {
            request.setSkuId(StringUtils.isNotBlank(ctx.getSkuId()) ? Long.parseLong(ctx.getSkuId()) : 0L);
            customParam.addParam("flowFlag", "magicCouponEnhancement");
            request.setModuleKeys(MAGIC_COUPON_ENHANCEMENT_MODULE_KEYS);
        } else if (isCountrySubsidiesProduct(ctx)) {
            long selectedSkuId = getSkuId(ctx);
            request.setSkuId(selectedSkuId);
            customParam.addParam("flowFlag", "countrySubsidies");
            request.setModuleKeys(COUNTRY_SUBSIDIES_MODULE_KEYS);
        } else if (TimesDealUtil.isMonthlySubscription(ctx)) {
            long selectedSkuId = getSkuId(ctx);
            request.setSkuId(selectedSkuId);
            customParam.addParam("flowFlag", "monthlySubscription");
            request.setModuleKeys(MONTHLY_SUBSCRIPTION_MODULE_KEYS);
        }
        if (CollectionUtils.isEmpty(request.getModuleKeys())) {
            return null;
        }
        PageConfigRoutingKey pageConfigRoutingKey = new PageConfigRoutingKey();
        pageConfigRoutingKey.setProductType(ProductTypeEnum.DEAL.getCode());
        pageConfigRoutingKey.setScene(PageConfigSceneEnum.OldProductDetail.name());
        DealGroupCategoryDTO category = dealGroupDTO.getCategory();
        if (Objects.nonNull(category.getCategoryId())) {
            customParam.addParam("productSecondCategoryId", String.valueOf(category.getCategoryId()));
            pageConfigRoutingKey.setProductSecondCategoryId(Math.toIntExact(category.getCategoryId()));
        }
        if (Objects.nonNull(category.getServiceTypeId())) {
            customParam.addParam("productThirdCategoryId", String.valueOf(category.getServiceTypeId()));
            pageConfigRoutingKey.setProductThirdCategoryId(Math.toIntExact(category.getServiceTypeId()));
        }
        if (StringUtils.isNotBlank(ctx.getMmcPkgVersion())) {
            customParam.addParam("mmcpkgversion", ctx.getMmcPkgVersion());
        }
        request.setCustomParam(customParam);
        request.setPageConfigRoutingKey(pageConfigRoutingKey);
        return request;
    }

    private long getSkuId(DealCtx ctx) {
        DealBaseReq dealBaseReq = ctx.getDealBaseReq();
        if (StringUtils.isNotBlank(dealBaseReq.getSkuId())) {
            return Long.parseLong(dealBaseReq.getSkuId());
        }
        return 0L;
    }

    private ClientTypeEnum getClientType(EnvCtx envCtx) {
        if (envCtx.getDztgClientTypeEnum() == DztgClientTypeEnum.MEITUAN_APP) {
            return MT_APP;
        } else if (envCtx.getDztgClientTypeEnum() == DztgClientTypeEnum.DIANPING_APP) {
            return DP_APP;
        } else if (envCtx.getDztgClientTypeEnum() == DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP) {
            return MT_XCX;
        } else if (envCtx.getDztgClientTypeEnum() == DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP) {
            return DP_XCX;
        }
        return UNKNOWN;
    }

    private boolean isBlackCity(DealCtx ctx) {
        List<Integer> cityBlackList = LionConfigUtils.getMagicCouponEnhancementCityBlackList(ctx.isMt());
        if (CollectionUtils.isEmpty(cityBlackList)) {
            return false;
        }
        int cityId = ctx.getCityId4P();
        if (cityId <= 0) {
            return false;
        }
        return cityBlackList.contains(cityId);
    }

    private boolean isLeadDeal(DealCtx ctx) {
        return DealUtils.isLeadsDeal(ctx) || DealUtils.isWeddingLeadsDeal(ctx)
                || LionConfigUtils.isLeadsDealCateGoryId(ctx.getCategoryId());
    }

    // 是否是屏蔽的渠道
    private boolean isHidePageSource(DealCtx ctx) {
        return LionConfigUtils.isHidePageSource(ctx.getRequestSource());
    }

    // 命中实验组展示逻辑
    private boolean isHitExp(DealCtx ctx) {
        if (!ctx.isMt()) {
            return false;
        }
        ModuleAbConfig moduleAbConfig = douHuService.getMagicCouponEnhancementAbTestResult(ctx.getEnvCtx());
        return Objects.nonNull(moduleAbConfig) && CollectionUtils.isNotEmpty(moduleAbConfig.getConfigs())
                && EXP_RESULTS.contains(moduleAbConfig.getConfigs().get(0).getExpResult());
    }

    /**
     * 与线上 dealfilterlist接口的pagesource传参逻辑保持一致
     * @param pageSource
     * @return
     */
    private String matchPageSource(String pageSource){
        if ("guess".equals(pageSource) || "style".equals(pageSource)) {
            return pageSource;
        }else {
            return "shelf";
        }
    }

    private boolean isCountrySubsidiesProduct(DealCtx ctx) {
        EnvCtx envCtx = ctx.getEnvCtx();
        String clientTypeName = envCtx.getDztgClientTypeEnum().name();
        boolean isCountrySubsidiesClientType = LionConfigUtils.isCountrySubsidyClientType(clientTypeName);
        boolean isCountrySubsidesProduct = LionConfigUtils.isCountrySubsidyDealServiceTypeId(ctx.getDealGroupDTO());
        // 总开关
        boolean enableCountrySubsidyDegradeSwitch = LionConfigUtils.isEnableCountrySubsidyDegradeSwitch();
        return isCountrySubsidiesClientType && isCountrySubsidesProduct && !enableCountrySubsidyDegradeSwitch;
    }
}
