package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.shankai;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2024/10/24 11:08
 */
@Data
@MobileDo(id = 0x7260)
public class DealLayoutConfigVO implements Serializable {
    /**
     * 是否上报首屏数据
     */
    @MobileDo.MobileField(key = 0x9d78)
    private boolean report;

    /**
     * 是否渲染骨架屏
     */
    @MobileDo.MobileField(key = 0x89dd)
    private boolean render;

}
