package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.LayerConfig;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024-07-08-11:28
 */
public class DealLayerUtils {

    public static List<LayerConfig> getTradeAssuranceDealLayers(DealCtx ctx, Map<Long, LayerConfig> tagId2ConfigMap) {
        List<DealGroupTagDTO> tags = ctx.getDealGroupDTO().getTags();
        if (CollectionUtils.isEmpty(tags)) {
            return Lists.newArrayList();
        }
        Map<Long, String> tagMap = tags.stream().collect(Collectors.toMap(DealGroupTagDTO::getId, DealGroupTagDTO::getTagName));
        if (MapUtils.isEmpty(tagId2ConfigMap)) {
            return Lists.newArrayList();
        }
        return tagId2ConfigMap.entrySet().stream()
                .map(entry -> {
                    Long tagId = entry.getKey();
                    if (tagId > 0 && tagMap.containsKey(tagId)) {
                        LayerConfig layerConfig = entry.getValue();
                        if (StringUtils.isEmpty(layerConfig.getTitle())) {
                            layerConfig.setTitle(tagMap.get(tagId));
                        }
                        return layerConfig;
                    }
                    return null;
                }).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
