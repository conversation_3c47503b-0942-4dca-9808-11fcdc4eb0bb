package com.dianping.mobile.mapi.dztgdetail.facade.rcf.grey;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2023/8/7
 * @since dzviewscene-productshelf-home
 */
@Getter
@AllArgsConstructor
public abstract class AbstractGrayConfigContext implements GrayConfigContext {

    List<String> grayUnionIds = Lists.newArrayList();

    public void addUnionId(String unionId) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.grey.AbstractGrayConfigContext.addUnionId(java.lang.String)");
        grayUnionIds.add(unionId);
    }
}
