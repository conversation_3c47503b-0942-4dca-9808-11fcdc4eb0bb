package com.dianping.mobile.mapi.dztgdetail.datatype.resp.shop;

import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ShopTagTypeEnum;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: zhangyuan103
 * @Date: 2024/8/13
 */
@Data
@AllArgsConstructor
@TypeDoc(description = "门店图标")
public class ShopIcon implements Serializable {

    /**
     * 标签类型
     * @see ShopTagTypeEnum
     */
    @MobileField
    private int type;

    /**
     * 标签文字
     */
    @MobileField
    private String text;
}