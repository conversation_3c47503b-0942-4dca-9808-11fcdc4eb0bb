package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

@Component
public class DealDetailSpecificModuleHandlerContainer implements ApplicationListener<ContextRefreshedEvent> {

    @Autowired
    ApplicationContext context;

    private Map<String, DealDetailSpecificModuleHandler> handlerMap;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        Map<String, DealDetailSpecificModuleHandler> map = context.getBeansOfType(DealDetailSpecificModuleHandler.class);
        Collection<DealDetailSpecificModuleHandler> handlers = map.values();
        handlerMap = new HashMap<>();

        for (DealDetailSpecificModuleHandler handler : handlers) {
            handlerMap.put(handler.identity(), handler);
        }
    }

    public DealDetailSpecificModuleHandler getHandler(String identity) {
        return handlerMap.get(identity);
    }

}