package com.dianping.mobile.mapi.dztgdetail.biz.processor.imagetext.strategy.impl;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.imagetext.strategy.AbstractImageTextDetailStrategy;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ImageTextStrategyEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ContentDetailPBO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-12-11
 * @desc 折叠图片文字详情策略
 */
@Component
public class CollapseImageTextDetailStrategyImpl extends AbstractImageTextDetailStrategy {
    @Override
    public ImageTextStrategyEnum getStrategyName() {
        return ImageTextStrategyEnum.COLLAPSE;
    }

    @Override
    public ContentDetailPBO getContentDetail(DealGroupDTO dealGroupDTO, List<ContentPBO> contents, int foldThreshold) {
        ContentDetailPBO contentDetail = new ContentDetailPBO();
        contentDetail.setTitle("图文详情");
        contentDetail.setContents(contents);
        /*
          fold = false：图文详情展开
          fold = true：图文详情折叠
          foldThreshold：contents size < foldThreshold时会忽略fold的值直接全部展开且无"收起"按钮
         */
        contentDetail.setFold(true);
        contentDetail.setFoldThreshold(foldThreshold);
        return contentDetail;
    }
}
