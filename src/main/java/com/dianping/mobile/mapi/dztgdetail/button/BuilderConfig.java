package com.dianping.mobile.mapi.dztgdetail.button;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode(exclude = "maxButtonSize")
public class BuilderConfig implements Serializable {
    /**
     * 按钮构建器类名
     */
    private String builderName;
    /**
     * 最大按钮数量
     */
    private int maxButtonSize;
    /**
     * 指定可以共存的按钮类型
     */
    private List<ButtonStateConfig> inclusiveType;
    /**
     * 指定不共存的按钮类型
     */
    private List<ButtonStateConfig> exclusiveType;
    /**
     * 和其他所有类型不共存
     */
    private boolean exclusiveAll;
    /**
     * 可以和其他所有类型共存
     */
    private boolean inclusiveAll;
    /**
     * 当前上下文处于某种状态的时候才构建
     */
    private List<ButtonStateEnum> buildOnStatues;
    /**
     * 当前上下文处于某种状态的时候跳过
     */
    private List<ButtonStateEnum> skipOnStatues;
    /**
     * 是否仅新客的时候构建，null表示不区分，false表示老客构建，true表示新客构建
     */
    private Boolean buildOnNewUser;
}
