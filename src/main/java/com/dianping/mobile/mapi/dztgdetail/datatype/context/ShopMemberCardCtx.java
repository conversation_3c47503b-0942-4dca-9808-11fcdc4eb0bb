//package com.dianping.mobile.mapi.dztgdetail.datatype.context;
//
//import com.sankuai.dzcard.navigation.api.enums.MemberUserRuleEnum;
//import com.sankuai.mpmctmember.query.thrift.dto.MemberInterestDetailDTO;
//import lombok.Data;
//
///**
// * @Author: guangyujie
// * @Date: 2024/8/29 15:34
// */
//@Data
//public class ShopMemberCardCtx {
//
//    /**
//     * 会员卡Id
//     */
//    private long memberCardId;
//
//    /**
//     * 会员通适用人群
//     * 1:全部会员(对应会员身份)
//     * 2:品牌新会员(对应新会员身份)
//     *
//     * @see MemberUserRuleEnum
//     */
//    private long memberUserRule;
//
//    /**
//     * 是否是商家会员
//     */
//    private boolean shopMember;
//
//    /**
//     * 是否是新会员，newShopMember = true则shopMember = true
//     */
//    private boolean newShopMember;
//
//    /**
//     * 该团单是否只供新会员购买
//     */
//    private boolean isDealOnlyForNewMember;
//
//    /**
//     * 商家会员原始请求结果
//     */
//    private MemberInterestDetailDTO originalResult;
//
//}
