package com.dianping.mobile.mapi.dztgdetail.button.weddingleadsdeal;

import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.StyleTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtnIcon;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.sankuai.sig.botdefender.core.crypt.utils.SigCryptUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Collections;
import java.util.Objects;


public class WeddingLeadsDealButtonBuilder extends AbstractButtonBuilder {
    private final static String WEDDING_DEALS_IM_TITLE = "在线咨询";
    private final static String WEDDING_DEALS_BOOK_TITLE = "立即预约";
    private final static String SPECIAL_DEALS_BOOK_TITLE = "获取底价";
    private final static String WEDDING_DEALS_BUY_TITLE = "抢购";
    private final static String BUY_ICON = "https://p0.meituan.net/lefeimg/791043cff9a958c67a9a1810f5f20a001312.png";
    private final static String MRN_COMPONENT_DEAL_LEADS = "dealleads";
    private final static String MRN_COMPONENT_SPECIAL_DEAL_LEADS = "huidealleads";
    private final static String SOURCE_DEAL_LEADS = "925";
    private final static String SOURCE_SPECIAL_DEAL_LEADS = "926";
    private final static String RESV_DP_JUMP_URL = "dianping://mrn?mrn_biz=gc&mrn_entry=vg-mrn-reserve&mrn_component=%s&isTransparent=true&hideLoading=true" +
            "&mrn_transparent=true&mrn_hideloading=true&mrn_hideNextNavBar=true&dpShopId=%s&source=%s&dpShopIdEncrypt=%s&mrn_min_version=0.0.521&referId=%s&referType=12";
    private final static String RESV_MT_JUMP_URL = "imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=vg-mrn-reserve&mrn_component=%s&" +
            "isTransparent=true&hideLoading=true&mrn_transparent=true&mrn_hideloading=true&mrn_hideNextNavBar=true&mtShopId=%s&source=%s&mtShopIdEncrypt=%s&mrn_min_version=0.0.521&referId=%s&referType=12";

    @Override
    protected void doBuild(DealCtx context, ButtonBuilderChain chain) {
        buildButton(context);
        chain.build(context);
    }

    private void buildButton(DealCtx context) {
        // 判断是否为指定poi后台类目的特惠团单，若是则返回[在线咨询、获取低价]按钮
        boolean isSpecialValueWithPoiCategory = DealUtils.isWeddingSpecialWithPoiCategory(context);
        // 限制于前端，如果商家有预约权益，顺序是[在线咨询、立即预约、抢购]
        // 如果商家没有预约权益，顺序是[立即预约、在线咨询、抢购]
        if (context.isHasBookBenefit()) {
            // 咨询按钮构造
            buildIMBtn(context);
            // 预约按钮构造
            buildResvBtn(context, isSpecialValueWithPoiCategory);
        } else {
            // 空的预约按钮构造
            buildNullBtn(context);
            // 咨询按钮构造
            buildIMBtn(context);
        }
        // 购买按钮构造
        if (!isSpecialValueWithPoiCategory) {
            buildBuyBtn(context);
        }
        // 设置styleType
        context.getBuyBar().setStyleType(StyleTypeEnum.WEDDING_BUY_BUTTON_REPLACE_SHARE.code);
    }

    private void buildIMBtn(DealCtx context) {
        DealBuyBtn btn = new DealBuyBtn(true, WEDDING_DEALS_IM_TITLE);
        btn.setDetailBuyType(BuyBtnTypeEnum.RESV_DEAL.getCode());
        btn.setRedirectUrl(context.getImUrl());
        context.addButton(btn);
    }

    private void buildNullBtn(DealCtx context) {
        DealBuyBtn btn = new DealBuyBtn(false);
        context.addButton(btn);
    }

    private void buildResvBtn(DealCtx context, boolean isWeddingSpecialValueDeal) {
        DealBuyBtn btn = new DealBuyBtn(true,
                isWeddingSpecialValueDeal ? SPECIAL_DEALS_BOOK_TITLE : WEDDING_DEALS_BOOK_TITLE);
        btn.setDetailBuyType(BuyBtnTypeEnum.RESV_DEAL.getCode());
        btn.setRedirectUrl(isWeddingSpecialValueDeal ?
                getResvUrl(context, MRN_COMPONENT_SPECIAL_DEAL_LEADS, SOURCE_SPECIAL_DEAL_LEADS) :
                getResvUrl(context, MRN_COMPONENT_DEAL_LEADS, SOURCE_DEAL_LEADS));
        context.addButton(btn);
    }

    // 跳链中 组件参数(component): huidealleads(特惠团购)、dealleads(正常团购)
    // source: 926(特惠团购)、925(正常团购)
    private String getResvUrl(DealCtx context, String component, String source) {
        return context.getEnvCtx().isDp() ?
                String.format(RESV_DP_JUMP_URL, component, context.getDpLongShopId(), source, SigCryptUtils.encryptPoiId(context.getDpLongShopId()), context.getDpId()) :
                String.format(RESV_MT_JUMP_URL, component, context.getMtLongShopId(), source, SigCryptUtils.encryptPoiId(context.getMtLongShopId()), context.getMtId());
    }

    private void buildBuyBtn(DealCtx context) {
        DealBuyBtn canNotBuyButton = DealBuyHelper.getCanNotBuyButton(context);
        // 若为不可售卖状态，则不构造购买按钮
        if (Objects.nonNull(canNotBuyButton)) {
            return;
        }
        // 提单页跳链不存在，则不构造购买按钮
        String buyUrl = getBuyUrl(context);
        if (StringUtils.isEmpty(buyUrl)) {
            return;
        }
        DealBuyBtn btn = new DealBuyBtn(true, WEDDING_DEALS_BUY_TITLE);
        btn.setDetailBuyType(BuyBtnTypeEnum.NORMAL_DEAL.getCode());
        // 跳转链接可能会在主流程中被替换
        btn.setRedirectUrl(buyUrl);
        btn.setBtnIcons(Collections.singletonList(new DealBuyBtnIcon(BUY_ICON, 2)));
        context.addButton(btn);
    }

    private String getBuyUrl(DealCtx context) {
        if (Objects.isNull(context.getSkuModule()) || StringUtils.isEmpty(context.getSkuModule().getUrl())) {
            return StringUtils.EMPTY;
        }
        return context.getSkuModule().getUrl();
    }
}