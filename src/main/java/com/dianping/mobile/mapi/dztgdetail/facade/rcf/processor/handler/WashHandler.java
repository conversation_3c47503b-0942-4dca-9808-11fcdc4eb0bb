package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.handler;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/11/7 14:54
 */
@Service
@Slf4j
public class WashHandler implements BaseReserveMaintenanceHandler {
    private static final String MT_KEY = "MtWashReserveExp";
    private static final String DP_KEY = "DpWashReserveExp";
    private static final List<String> WASH_RESERVE_AB_KEYS = Lists.newArrayList(MT_KEY, DP_KEY);

    /**
     * 洗涤
     * 
     * @return
     */
    @Override
    public int getDealSecondCategory() {
        return 407;
    }

    @Override
    public String getExpName(boolean isMt) {
        return isMt ? MT_KEY : DP_KEY;
    }

    @Override
    public List<String> getAbKeys() {
        return WASH_RESERVE_AB_KEYS;
    }
}
