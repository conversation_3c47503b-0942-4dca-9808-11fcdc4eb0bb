package com.dianping.mobile.mapi.dztgdetail.mq.dto.api.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.enums.*;
import com.sankuai.dz.product.detail.gateway.spi.exception.ProductDetailRequestIllegalException;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * @Author: guangyujie
 * @Date: 2025/2/10 11:10
 */
@Data
@TypeDoc(description = "聚合接口前端请求入参")
public class UnifiedPageRequest implements Serializable {

    @FieldDoc(description = "客户端类型，具体看枚举", requiredness = Requiredness.REQUIRED,
            type = ClientTypeEnum.class)
    public int clientType;

    @FieldDoc(description = "指定屏幕区域", rule = "默认返回全屏", requiredness = Requiredness.OPTIONAL, type = PageRegionEnum.class)
    public int pageRegion;

    @FieldDoc(description = "指定模块请求", rule = "优先级高于pageRegion", requiredness = Requiredness.OPTIONAL,
            typeName = "参考https://km.sankuai.com/collabpage/2695513940，暂时用文档维护，后续会用系统维护")
    public String moduleKeys;

    @Deprecated
    @FieldDoc(description = " 网关层解析参数信息，后续参数不由Shepherd网关写入，由本服务自行从header计算", rule = "别用继承，继承容易造成参数的冲突", requiredness = Requiredness.REQUIRED)
    public ShepherdGatewayParam shepherdGatewayParam;

    @FieldDoc(description = "统一商品Id", rule = "for后续团泛融合，优先使用", requiredness = Requiredness.OPTIONAL)
    public long unifiedProductId;

    @FieldDoc(description = "统一SkuId", rule = "for后续团泛融合，优先使用", requiredness = Requiredness.OPTIONAL)
    public long unifiedSkuId;

    @FieldDoc(description = "商品id", rule = "根据productType传对应商品id，团购id区分平台，泛商品id不区分", requiredness = Requiredness.REQUIRED)
    public long productId;

    @FieldDoc(description = "skuId", rule = "根据productType传对应商品id，团购id区分平台，泛商品id不区分", requiredness = Requiredness.OPTIONAL)
    public long skuId;

    @FieldDoc(description = "商品类型，具体看枚举", requiredness = Requiredness.REQUIRED,
            type = ProductTypeEnum.class)
    public int productType;

    @FieldDoc(description = "门店id", rule = "区分平台", requiredness = Requiredness.OPTIONAL)
    public long poiId;

    @FieldDoc(description = "门店id加密串", rule = "区分平台", requiredness = Requiredness.OPTIONAL)
    public String poiIdEncrypt;

    @FieldDoc(description = "首页选择城市id", rule = "区分平台", requiredness = Requiredness.OPTIONAL)
    public int cityId;

    @FieldDoc(description = "定位城市id", rule = "区分平台", requiredness = Requiredness.OPTIONAL)
    public int gpsCityId;

    @FieldDoc(description = "用户定位经度", rule = "坐标系类型参考gpsCoordinateType", requiredness = Requiredness.OPTIONAL)
    public double userLng;

    @FieldDoc(description = "用户定位纬度", rule = "坐标系类型参考gpsCoordinateType", requiredness = Requiredness.OPTIONAL)
    public double userLat;

    @FieldDoc(description = "选择城市定位经度", rule = "坐标系类型参考gpsCoordinateType", requiredness = Requiredness.OPTIONAL)
    public double cityLng;

    @FieldDoc(description = "选择城市定位纬度", rule = "坐标系类型参考gpsCoordinateType", requiredness = Requiredness.OPTIONAL)
    public double cityLat;

    @FieldDoc(description = "定位类型，具体看枚举", requiredness = Requiredness.REQUIRED,
            type = GpsCoordinateTypeEnum.class)
    public int gpsCoordinateType;

    @FieldDoc(description = "渠道", requiredness = Requiredness.OPTIONAL,
            typeName = "参考https://km.sankuai.com/collabpage/2418267775，暂时用文档维护，后续会用系统维护")
    public String pageSource;

    @FieldDoc(description = "业务定制参数", requiredness = Requiredness.OPTIONAL,
            typeName = "参考https://km.sankuai.com/collabpage/2695177049，暂时用文档维护，后续会用系统维护")
    public String customParam;

    @FieldDoc(description = "HTTP Headers", requiredness = Requiredness.OPTIONAL)
    public Map<String, String> headerMap;

    @FieldDoc(description = "HTTP Cookies", requiredness = Requiredness.OPTIONAL)
    public Map<String, String> cookieMap;

    @FieldDoc(description = "用户token", requiredness = Requiredness.OPTIONAL)
    public String token;

    @FieldDoc(description = "是否测试", requiredness = Requiredness.OPTIONAL)
    public boolean debugTest;

    @FieldDoc(description = "价格一致性加密串", requiredness = Requiredness.REQUIRED, typeName="https://km.sankuai.com/collabpage/1830735597")
    protected String pricecipher;

    @FieldDoc(description = "诚信字段", requiredness = Requiredness.REQUIRED)
    protected String cx;

    @FieldDoc(description = "小程序版本号", requiredness = Requiredness.REQUIRED)
    protected String csecversionname;

    @FieldDoc(description = "mrn版本号", requiredness = Requiredness.REQUIRED)
    protected String mrnversion;

    @JsonIgnore
    public void checkParam() {
        if (shepherdGatewayParam == null) {
            throw new ProductDetailRequestIllegalException("shepherdGatewayParam is null");
        }
        if (productId <= 0) {
            throw new ProductDetailRequestIllegalException("productId is null");
        }
        if (!ProductTypeEnum.containsCode(productType)) {
            throw new ProductDetailRequestIllegalException("productType is Illegal");
        }
    }


    @JsonIgnore
    private ClientTypeEnum clientTypeEnum;
    @JsonIgnore
    public ClientTypeEnum getClientTypeEnum() {
        if (clientTypeEnum != null) {
            return clientTypeEnum;
        }
        clientTypeEnum = ClientTypeEnum.fromCode(clientType);
        return clientTypeEnum;
    }

    @JsonIgnore
    public PlatformEnum getPlatformEnum() {
        return getClientTypeEnum().getPlatform();
    }

}
