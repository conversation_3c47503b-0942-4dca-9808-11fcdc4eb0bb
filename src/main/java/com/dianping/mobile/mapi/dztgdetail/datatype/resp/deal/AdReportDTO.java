package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: zheng<PERSON><EMAIL>
 * @Date: 2024/11/24
 */
@Data
@TypeDoc(description = "广告埋点模型")
@MobileDo(id = 0x762b)
public class AdReportDTO implements Serializable {
    @FieldDoc(description = "灵犀埋点数据")
    @MobileDo.MobileField(key = 0x9922)
    private String adLx;


    @FieldDoc(description = "请求id")
    @MobileDo.MobileField(key = 0xe701)
    private String adRequestId;

    @FieldDoc(description = "计费埋点数据")
    @MobileDo.MobileField(key = 0xfa37)
    private String adFeedback;
}
