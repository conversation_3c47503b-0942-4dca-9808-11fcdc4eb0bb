package com.dianping.mobile.mapi.dztgdetail.biz;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.GroupDealWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiClientWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.PoiConsts;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.MtPoiModel;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.ViewItemDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.ViewListDo;
import com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam;
import com.dianping.mobile.mapi.dztgdetail.entity.SortCxt;
import com.dianping.mobile.mapi.dztgdetail.helper.PoiModelHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.PoisRankHelper;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.meituan.service.mobile.sinai.client.model.PoiModelL;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description:
 * Author: lijie
 * Version: 0.0.1
 * Created: 16/5/18 下午7:54
 */
@Component
public class DealDetailBizStrategy {

    @Resource
    private GroupDealWrapper groupDealWrapper;

    @Resource
    private PoiClientWrapper poiClientWrapper;

    private static final int THRESHOLD = 20;

    public ViewListDo getMtPoiList(int dealId, boolean isOnlyCurCityPois, MtCommonParam mtCommonParam) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.DealDetailBizStrategy.getMtPoiList(int,boolean,com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam)");
        //显示全国的分店
        List<Long> allPoiIds = groupDealWrapper.listPoiIdByDId(dealId, THRESHOLD);
        List<PoiModelL> allPoiModel = poiClientWrapper.listPoiL(allPoiIds, PoiConsts.defaultPoiFields);
        if (CollectionUtils.isEmpty(allPoiModel)) {
            return null;
        }
        List<PoiModelL> pickedPoiModels = PoisRankHelper.pick(allPoiModel, new SortCxt(mtCommonParam));
        List<ViewItemDo> viewItemDos = Lists.transform(pickedPoiModels, new Function<PoiModelL, ViewItemDo>() {
            @Override
            public ViewItemDo apply(PoiModelL input) {
                MtPoiModel mtPoiModel = PoiModelHelper.convertPoiModel(input);
                ViewItemDo viewItemDo = new ViewItemDo();
                viewItemDo.setMtShop(mtPoiModel);
                return viewItemDo;
            }
        });
        return initViewListDo(mtCommonParam, allPoiModel, viewItemDos);
    }

    private ViewListDo initViewListDo(MtCommonParam mtCommonParam, List<PoiModelL> allPoiModel, List<ViewItemDo> viewItemDos) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.DealDetailBizStrategy.initViewListDo(com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam,java.util.List,java.util.List)");
        ViewListDo viewListDo = new ViewListDo();
        viewListDo.setStartIndex(mtCommonParam.getOffset());
        if (mtCommonParam.getOffset() + mtCommonParam.getLimit() < allPoiModel.size()) {
            viewListDo.setIsEnd(false);
        }
        viewListDo.setRecordCount(allPoiModel.size());
        viewListDo.setList(viewItemDos);
        return viewListDo;
    }
}
