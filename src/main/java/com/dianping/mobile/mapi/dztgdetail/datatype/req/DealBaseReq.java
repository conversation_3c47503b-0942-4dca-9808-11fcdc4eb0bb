package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.annotation.MobileRequest.Param;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.basic.SVIPMapperCacheProcessor;
import com.dianping.mobile.mapi.dztgdetail.util.LongUtils;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.DecryptedField;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Objects;

import static com.sankuai.sig.botdefender.core.enums.AssetIdType.SHOP_UUID;

@Data
@TypeDoc(description = "团单主接口请求参数")
@MobileRequest
public class DealBaseReq implements IMobileRequest, Serializable {

    @Deprecated
    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，int类型")
    @Param(name = "dealgroupid")
    private Integer dealgroupid;

    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，string类型")
    @Param(name = "stringdealgroupid")
    private String stringDealGroupId;

    @FieldDoc(description = "skuID", rule = "美团点评统一")
    @Param(name = "skuid")
    private String skuId;

    @FieldDoc(description = "商户ID", rule = "美团平台为美团商户ID，点评平台为点评商户ID")
    @Param(name = "poiid", shopuuid = "shopuuid")
    private Long poiid;
    @Param(name = "poiidEncrypt")
    @DecryptedField(targetFieldName = "poiid")
    private String poiidEncrypt;

    @FieldDoc(description = "商户ID", rule = "美团平台为美团商户ID，点评平台为点评商户ID")
    @Param(name = "poiidstr")
    private String poiidStr;
    @Param(name = "poiidStrEncrypt")
    @DecryptedField(targetFieldName = "poiidStr")
    private String poiidStrEncrypt;

    @FieldDoc(description = "商户ID", rule = "美团平台为美团商户UUID，点评平台为点评商户UUID")
    @Param(name = "shopuuid")
    private String shopUuid;
    @Param(name = "shopUuidEncrypt")
    @DecryptedField(targetFieldName = "shopUuid", assetIdType = SHOP_UUID)
    private String shopUuidEncrypt;

    @FieldDoc(description = "商户ID对应的加密字符串", rule = "商户ID对应的加密字符串，用于快手等场景")
    @Param(name = "encryptedshopstr")
    private String encryptedShopStr;

    @FieldDoc(description = "城市ID", rule = "美团平台为美团城市ID，点评平台为点评城市ID，优先给到首页城市ID（非用户地理位置城市）")
    @Param(name = "cityid")
    private Integer cityid;

    @FieldDoc(description = "用户定位城市ID", rule = "用户定位城市ID，点评传点评体系，美团传美团体系," +
            "点评app环境IOS端的用户定位城市传的是美团的，注意这个是大坑，具体参考文档https://km.sankuai.com/collabpage/**********")
    @Param(name = "gpscityid")
    /**
     * 点评app环境IOS端的用户定位城市传的是美团的，注意这个是大坑，具体参考文档https://km.sankuai.com/collabpage/**********
     * 通过SVIPMapperCacheProcessor转换成了正确的双平台定位城市id，勿从此处获取请使用DealCtx中的GpsCityId
     * @see SVIPMapperCacheProcessor
     */
    private Integer gpscityid;

    @FieldDoc(description = "站点：APP可以不传，其他站点必须传，值参见后端枚举", rule = "@see com.dianping.deal.common.enums.ClientTypeEnum")
    @Param(name = "clienttype")
    private Integer clienttype;

    @FieldDoc(description = "用户经度(只接受火星坐标系gcj02)")
    @Param(name = "userlng")
    private Double userlng;

    @FieldDoc(description = "用户纬度(只接受火星坐标系gcj02)")
    @Param(name = "userlat")
    private Double userlat;

    @FieldDoc(description = "首页城市经度")
    @Param(name = "cityLongitude")
    private Double cityLongitude;

    @FieldDoc(description = "首页城市纬度")
    @Param(name = "cityLatitude")
    private Double cityLatitude;

    @Deprecated
    @FieldDoc(description = "位信息（神会员券包查询使用）, 该字段无需传入，改由后端配置化下发")
    @Param(name = "position")
    private String position;

    @FieldDoc(description = "区域id，用于到家set化，前端获取透传")
    @Param(name = "wtt_region_id")
    private String regionid;

    @FieldDoc(description = "实验结果")
    @Param(name = "expresults")
    private String expResults;

    @FieldDoc(description = "乐摇摇userid")
    @Param(name = "lyyuserid")
    private String lyyuserid;

    @FieldDoc(description = "经纬度坐标类型, 上游传入透传给营销侧，值为对应枚举.name(), @see com.sankuai.nib.mkt.common.base.enums.RealGpsCoordTypeEnum")
    @Param(name = "gpscoordinatetype")
    private String gpsCoordinateType;

    @FieldDoc(description = "算价渠道 https://km.sankuai.com/collabpage/2716451355")
    @Param(name = "pagesource")
    private String pageSource;

    @FieldDoc(description = "流量渠道 https://km.sankuai.com/collabpage/2716510738")
    @Param(name = "fromPage")
    private String fromPage;

    @FieldDoc(description = "是否需求转换颜色")
    @Param(name = "convertcolor")
    private Boolean convertcolor;

    @FieldDoc(description = "手艺人分销标识")
    @Param(name ="eventpromochannel")
    private String eventpromochannel;

    @Param(name ="pass_param")
    private String pass_param;

    @FieldDoc(description = "团单频道（用于路由），迁移自mtdetailtemplategn.bin")
    @Param(name ="channel")
    private String channel;

    /**
     * 这个地方传递值是json字符串，否则会无法转换导致报错
     */
    @FieldDoc(description = "扩展信息，根据不同pageSource可能有不同扩展信息")
    @Param(name = "extparam")
    private String extParam;

    @FieldDoc(description = "诚信参数")
    @Param(name = "cx")
    private String cx;

    @FieldDoc(description = "前端MRN版本号")
    @Param(name = "mrnversion")
    private String mrnversion;

    @FieldDoc(description = "团单通用字段（JsonString，内容可扩展）")
    @Param(name = "dealparam")
    private String dealparam;

    @FieldDoc(description = "价惠密文")
    @Param(name = "pricecipher")
    private String pricecipher;

    @FieldDoc(description = "直播场景AB实验参数，由直播侧做，前端传入")
    @Param(name = "mliveAbTestArgs")
    private String mliveAbTestArgs;

    @FieldDoc(description = "私域直播直播间Id")
    @Param(name = "privateLiveId")
    private String privateLiveId;

    @FieldDoc(description = "拼团参数，场景id")
    @Param(name = "sceneType")
    private Integer sceneType;

    @FieldDoc(description = "拼团参数，拼团活动id")
    @Param(name = "pintuanActivityId")
    private String pintuanActivityId;

    @FieldDoc(description = "拼团参数，拼团id")
    @Param(name = "orderGroupId")
    private String orderGroupId;

    @FieldDoc(description = "款式ID")
    @Param(name = "infoContentId")
    private Long infoContentId;

    @FieldDoc(description = "神券膨胀标识： 1-可膨 2-不可膨")
    @Param(name = "mmcinflate")
    private Integer mmcinflate;

    @FieldDoc(description = "神券可用标识： 1-可用 2-不可用")
    @Param(name = "mmcuse")
    private Integer mmcuse;

    @FieldDoc(description = "券包可买标识： 1-可买 2-不可买")
    @Param(name = "mmcbuy")
    private Integer mmcbuy;

    @FieldDoc(description = "神券可领塞标识：1-可领塞 2-不可领塞")
    @Param(name = "mmcfree")
    private Integer mmcfree;

    @FieldDoc(description = "小程序版本")
    @Param(name = "csecversionname")
    private String wxVersion;

    @FieldDoc(description = "线下码ID")
    @Param(name = "offlinecode")
    private String offlineCode;

    @FieldDoc(description = "分销参数")
    @Param(name = "distributionParam")
    private String distributionParam;

    @FieldDoc(description = "个人分销参数")
    @Param(name = "userDistributionParam")
    private String userDistributionParam;

    @FieldDoc(description = "神券包组件版本")
    @Param(name = "mmcpkgversion")
    private String mmcPkgVersion;

    @FieldDoc(description = "新老团详DIFF关联唯一key")
    @Param(name = "diffUniqueId")
    private String diffUniqueId;

    @FieldDoc(description = "新老团详DIFF回放请求特殊标识 1-回放请求 0或空-非回放请求")
    @Param(name = "diffReplayFlag")
    private Integer diffReplayFlag;

    public int getIntPoiid() {
        return LongUtils.toInt(getPoiid());
    }

    public long getLongPoiid() {
        if(StringUtils.isNumeric(poiidStr)) {
            return Long.parseLong(poiidStr);
        } else if(poiid != null) {
            return poiid;
        }
        return 0L;
    }

    public Integer getCityid() {
        return cityid != null ? cityid : 0;
    }

    public Integer getClienttype() {
        return clienttype != null ? clienttype : 0;
    }

    public Double getUserlng() {
        return userlng != null ? userlng : 0;
    }

    public Double getUserlat() {
        return userlat != null ? userlat : 0;
    }

    public Integer getGpsCityId() {
        return gpscityid != null ? gpscityid : 0;
    }

    public String getChannel() {
        return channel == null ? "" : channel;
    }

    public String getPass_param() {
        if (Objects.equals(pass_param, "undefined")) {
            return StringUtils.EMPTY;
        }
        return pass_param;
    }

    public String getDealParam() {
        if (Objects.equals(dealparam, "undefined")) {
            return StringUtils.EMPTY;
        }
        return dealparam;
    }

    public String getExtParam() {
        if (Objects.equals(extParam, "undefined")) {
            return StringUtils.EMPTY;
        }
        return extParam;
    }
}
