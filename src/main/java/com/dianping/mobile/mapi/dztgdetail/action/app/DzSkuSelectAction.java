package com.dianping.mobile.mapi.dztgdetail.action.app;


import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.SkuSelectReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.Response;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.sku.SkuSelectorVO;
import com.dianping.mobile.mapi.dztgdetail.facade.SkuSelectorFacade;
import com.meituan.servicecatalog.api.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

@InterfaceDoc(displayName = "到综团单展示信息APP查询接口",
        type = "restful",
        description = "查询到综团单规格选择信息。",
        scenarios = "该接口适用于双平台APP站点的提单页规格选择模块",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "qian.wang.sh"
)
@Controller("general/platform/dztgdetail/dzskuselect.bin")
@Action(url = "dzskuselect.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class DzSkuSelectAction extends AbsAction<SkuSelectReq> {

    @Autowired
    private SkuSelectorFacade skuSelectorFacade;

    @Override
    protected IMobileResponse validate(SkuSelectReq request, IMobileContext iMobileContext) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.action.app.DzSkuSelectAction.validate(com.dianping.mobile.mapi.dztgdetail.datatype.req.SkuSelectReq,com.dianping.mobile.framework.datatypes.IMobileContext)");
        if (request == null || StringUtils.isEmpty(request.getDealgroupid())) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "dzdealbase.bin",
            displayName = "查询到综团单规格选择信息。",
            description = "查询到综团单规格选择信息。",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "dzskuselect.bin请求参数",
                            type = SkuSelectReq.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "result", description = "团购picasso数据", type = DealGroupPBO.class)},
            restExampleUrl = "http://mapi.51ping.com/general/platform/dztgdetail/dzskuselect.bin?" +
                    "cityid=1&convertcolor=true&dealgroupid=419965223&skuid=446887263",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    ),
            }
    )
    @Override
    protected IMobileResponse execute(SkuSelectReq request, IMobileContext iMobileContext) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.action.app.DzSkuSelectAction.execute(com.dianping.mobile.mapi.dztgdetail.datatype.req.SkuSelectReq,com.dianping.mobile.framework.datatypes.IMobileContext)");
        EnvCtx envCtx = initEnvCtx(iMobileContext);
        try {
            Response<SkuSelectorVO> response = skuSelectorFacade.querySkuSelector(request, envCtx);

            return new CommonMobileResponse(response.getResult());
        } catch (Exception e) {
            logger.error("dzskuselect.bin error", e);
        }
        return Resps.SYSTEM_ERROR;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.action.app.DzSkuSelectAction.getRule()");
        return null;
    }
}
