package com.dianping.mobile.mapi.dztgdetail.facade.shop;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.general.unified.search.api.common.dto.ClientEnv;
import com.dianping.general.unified.search.api.common.dto.ExtendSearchOption;
import com.dianping.general.unified.search.api.common.dto.SearchConditionUnit;
import com.dianping.general.unified.search.api.productshopsearch.GeneralProductShopSearchService;
import com.dianping.general.unified.search.api.productshopsearch.dto.LocationInfoDTO;
import com.dianping.general.unified.search.api.productshopsearch.dto.ProductShopSearchDTO;
import com.dianping.general.unified.search.api.productshopsearch.dto.PromotionDTO;
import com.dianping.general.unified.search.api.productshopsearch.dto.option.BaseSearchOption;
import com.dianping.general.unified.search.api.productshopsearch.dto.option.PageOption;
import com.dianping.general.unified.search.api.productshopsearch.dto.option.SortOption;
import com.dianping.general.unified.search.api.productshopsearch.dto.unit.SortUnit;
import com.dianping.general.unified.search.api.productshopsearch.enums.*;
import com.dianping.general.unified.search.api.productshopsearch.request.GeneralProductShopSearchRequest;
import com.dianping.general.unified.search.api.productshopsearch.response.GeneralProductShopSearchResponse;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ThreadPoolNameEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.shop.EntryPageEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.shop.PageConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.shop.ShopListContext;
import com.dianping.mobile.mapi.dztgdetail.util.JsonUtils;
import com.dianping.mobile.mapi.dztgdetail.util.ThreadPoolUtils;
import com.dianping.pay.order.common.enums.AmountType;
import com.dianping.pay.order.service.query.GetUnifiedOrderService;
import com.dianping.pay.order.service.query.dto.UnifiedOrderWithId;
import com.dianping.pay.unifiedorder.onlinequery.model.UnifiedOrderPaymentDetailDTO;
import com.dianping.zebra.util.StringUtils;
import com.google.common.collect.Lists;
import com.meituan.nibtp.trade.client.buy.enums.OrderExtraFieldEnum;
import com.meituan.nibtp.trade.client.buy.enums.OrderPaymentDetailExtraFieldEnum;
import com.meituan.nibtp.trade.client.buy.enums.OrderPaymentExtraFieldEnum;
import com.sankuai.web.dealadapter.dto.enums.BizLineEnum;
import com.sankuai.web.dealadapter.request.QueryOrderVerifySnapshotReq;
import com.sankuai.web.dealadapter.response.HasBlackPoiResp;
import com.sankuai.web.dealadapter.service.OrderVerifySnapshotService;
import com.sankuai.wpt.user.merge.query.thrift.message.FlattedBindRelation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @Author: zhangyuan103
 * @Date: 2025/6/3
 * @Description 到综适用门店的抽象类
 */
@Component
@Slf4j
public abstract class AbsDzDealShopsFacade<T, R> {
    @Resource
    protected GetUnifiedOrderService getUnifiedOrderService;
    @Resource(name = "generalProductShopSearchService")
    protected GeneralProductShopSearchService generalProductShopSearchService;
    @Resource
    protected OrderVerifySnapshotService orderVerifySnapshotService;
    @Resource
    protected MapperWrapper mapperWrapper;
    @Resource
    protected DealGroupWrapper dealGroupWrapper;

    protected static final String DEAL_SHOP_CONFIG_LION_KEY = "deal.shop.list.page.config";
    protected static final String LOG_ALL = "deal.shops.dealgroup.log.all";
    protected static final String LOG_WHITE_LIST = "deal.shops.dealgroup.whitelist";

    protected final ExecutorService EXECUTOR_TRACE_WRAPPER = ThreadPoolUtils
            .getCatExecutor(ThreadPoolNameEnum.DEAL_SHOP_EXECUTOR);

    public R execute(T t, EnvCtx envCtx) {
        try {
            // 初始化上下文
            ShopListContext shopListContext = initShopListContext(t, envCtx);

            // 前置处理逻辑
            preExecute(t, shopListContext);

            // 召回商户id
            CompletableFuture<Void> recallShopIdTasks = recallShopId(shopListContext);

            // 填充商户信息
            return fillShopInfo(shopListContext, recallShopIdTasks);
        } catch (Exception e) {
            log.error("{} error, req={}", this.getClass().getSimpleName(), JsonUtils.toJson(t), e);
        }
        return null;
    }

    public void preExecute(T t, ShopListContext shopListContext) {
        if (shopListContext.isNeedLog()) {
            log.info("{}.execute, req={}", this.getClass().getSimpleName(), JsonUtils.toJson(t));
        }
    }

    public abstract ShopListContext initShopListContext(T t, EnvCtx envCtx);

    public CompletableFuture<Void> recallShopId(ShopListContext shopListContext) {
        // 1 查询订单快照
        CompletableFuture<UnifiedOrderWithId> orderWithIdCF = CompletableFuture
                .supplyAsync(() -> getOrderInfo(shopListContext), EXECUTOR_TRACE_WRAPPER);
        // 2.1 召回适用门店
        CompletableFuture<GeneralProductShopSearchResponse> shopResponseCF = orderWithIdCF
                .thenCompose(orderWithId -> recallShops(orderWithId, shopListContext));
        // 2.2 根据快照id查询是否包含黑选单门店
        CompletableFuture<Boolean> hasBlackShopCF = orderWithIdCF.thenCompose(
                orderWithId -> CompletableFuture.supplyAsync(() -> hasBlackShop(orderWithId), EXECUTOR_TRACE_WRAPPER));
        // 2.3 点评映射美团dealGroupId
        CompletableFuture<Long> mtDealGroupIdCF = CompletableFuture.supplyAsync(() -> getMtDealGroupId(shopListContext),
                EXECUTOR_TRACE_WRAPPER);
        // 2.4 美团映射点评dealGroupId
        CompletableFuture<Long> dpDealGroupIdCF = CompletableFuture.supplyAsync(() -> getDpDealGroupId(shopListContext),
                EXECUTOR_TRACE_WRAPPER);
        // 2.5 查询页面配置
        CompletableFuture<PageConfig> pageConfigCF = CompletableFuture.supplyAsync(this::getPageConfig,
                EXECUTOR_TRACE_WRAPPER);
        // 3 context数据填充
        CompletableFuture<Void> allTasks = CompletableFuture.allOf(shopResponseCF, hasBlackShopCF,
                mtDealGroupIdCF, dpDealGroupIdCF, pageConfigCF);
        allTasks.thenRun(() -> {
            shopListContext.setShopIds(getShopIds(shopResponseCF.join(), shopListContext.isNeedLog()));
            shopListContext.setTotalCount(shopResponseCF.join().getTotalHits());
            shopListContext.setMtDealGroupId(mtDealGroupIdCF.join());
            shopListContext.setDpDealGroupId(dpDealGroupIdCF.join());
            shopListContext.setPageConfig(pageConfigCF.join());
            shopListContext.setHasBlackShop(hasBlackShopCF.join());
            shopListContext.setUnifiedOrderWithId(orderWithIdCF.join());
        }).join();
        return allTasks;
    }

    public abstract R fillShopInfo(ShopListContext shopListContext, CompletableFuture<Void> recallShopIdTasks);

    protected long getMtRealUserId(FlattedBindRelation userRelation) {
        return userRelation == null ? 0L : userRelation.getMtRealUserId();
    }

    protected List<Long> getShopIds(GeneralProductShopSearchResponse shopResponse, boolean needLog) {
        if (needLog) {
            log.info("DzDealShopsFacade.getShopIds, shopResponse={}", JsonUtils.toJson(shopResponse));
        }
        if (shopResponse == null || CollectionUtils.isEmpty(shopResponse.getResult())
                || shopResponse.getTotalHits() <= 0) {
            return Lists.newArrayList();
        }
        return shopResponse.getResult().stream().map(ProductShopSearchDTO::getShopId).distinct()
                .collect(Collectors.toList());
    }

    protected boolean hasBlackShop(UnifiedOrderWithId orderWithId) {
        String snapshotId = getSnapshotVersion(orderWithId);
        long versionId = NumberUtils.toLong(snapshotId);
        if (versionId <= 0L) {
            return false;
        }
        QueryOrderVerifySnapshotReq snapshotReq = new QueryOrderVerifySnapshotReq();
        snapshotReq.setSnapshotId(versionId);
        snapshotReq.setBizLine(BizLineEnum.GENERAL.getValue());
        HasBlackPoiResp hasBlackPoi = orderVerifySnapshotService.hasBlackPoi(snapshotReq);
        return hasBlackPoi != null && hasBlackPoi.isSuccess() && hasBlackPoi.getHasBlackPoi();
    }

    protected FlattedBindRelation getUserRelation(EnvCtx envCtx) {
        if (envCtx.isMt()) {
            FlattedBindRelation flattedBindRelation = new FlattedBindRelation();
            flattedBindRelation.setMtRealUserId(envCtx.getMtUserId());
            return flattedBindRelation;
        }
        Future future = mapperWrapper.preUserInfoByDpUserId(envCtx.getDpUserId());
        return mapperWrapper.getFlattedBindRelationByFuture(future);
    }

    protected Long getMtDealGroupId(ShopListContext shopListContext) {
        EnvCtx envCtx = shopListContext.getEnvCtx();
        if (envCtx.isMt()) {
            return shopListContext.getDealGroupId();
        }
        Future future = mapperWrapper.preDpIdMtIdMapper(shopListContext.getDealGroupId().intValue());
        return (long)mapperWrapper.getMtIdByDpIdMtIdMapper(future);
    }

    protected Long getDpDealGroupId(ShopListContext shopListContext) {
        EnvCtx envCtx = shopListContext.getEnvCtx();
        if (!envCtx.isMt()) {
            return shopListContext.getDealGroupId();
        }
        return (long) dealGroupWrapper.getDpDealGroupId(shopListContext.getDealGroupId().intValue());
    }

    // 团购id查适用门店列表(分页查询 + 距离排序)
    protected CompletableFuture<GeneralProductShopSearchResponse> recallShops(UnifiedOrderWithId orderWithId,
            ShopListContext shopListContext) {
        GeneralProductShopSearchRequest shopSearchRequest = buildGeneralProductShopSearchRequest(shopListContext,
                orderWithId);
        if (shopListContext.isNeedLog()) {
            log.info("DzDealShopsFacade.recallShops, shopSearchRequest={}", JsonUtils.toJson(shopSearchRequest));
        }
        return CompletableFuture.supplyAsync(
                () -> generalProductShopSearchService.searchProductShops(shopSearchRequest), EXECUTOR_TRACE_WRAPPER);
    }

    protected GeneralProductShopSearchRequest buildGeneralProductShopSearchRequest(ShopListContext shopListContext,
            UnifiedOrderWithId orderWithId) {
        GeneralProductShopSearchRequest request = new GeneralProductShopSearchRequest();
        request.setIdPlatform(
                shopListContext.isMt() ? ProductShopSearchIdTypeEnum.MT_PP : ProductShopSearchIdTypeEnum.DP_BP);

        BaseSearchOption baseSearchOption = new BaseSearchOption();
        baseSearchOption.setProductBizTypes(Collections.singletonList(ProductBizTypeEnum.DEALGROUP));
        baseSearchOption.setProductIds(Collections.singletonList(shopListContext.getDealGroupId()));

        // 用户选择城市做过滤
        if (shopListContext.getTargetCityId() != null && shopListContext.getTargetCityId() > 0) {
            baseSearchOption.setCityIds(Collections.singletonList(shopListContext.getTargetCityId().longValue()));
        }

        // 团购可被门店展示和核销
        baseSearchOption.setShopCanDisplay(true);
        baseSearchOption.setShopCanVerify(true);
        if (isBookingSource(shopListContext)) {
            baseSearchOption.setShopCanBook(true);
        }

        request.setBaseSearchOption(baseSearchOption);

        PageOption pageOption = new PageOption();
        pageOption.setPageNo(shopListContext.getPageNum());
        pageOption.setPageSize(shopListContext.getPageSize());
        request.setPageOption(pageOption);

        // 用户身份信息
        ClientEnv clientEnv = new ClientEnv();
        clientEnv.setMtRealUserId(getMtRealUserId(shopListContext.getUserRelation()));
        if (!shopListContext.isMt()) {
            clientEnv.setDpVirtualUserId(shopListContext.getUserRelation() == null ? 0L
                    : shopListContext.getUserRelation().getTargetVirtualUserId());
        }
        clientEnv.setClientType(getBpClientType(shopListContext.getEnvCtx()));
        clientEnv.setVersion(getVersion(shopListContext));
        clientEnv.setPageSource(StringUtils.isBlank(shopListContext.getOrderId())
                ? com.dianping.general.unified.search.api.common.enums.PageSourceEnum.PRODUCT_DETAIL.getCode()
                : com.dianping.general.unified.search.api.common.enums.PageSourceEnum.ORDER_DETAIL.getCode());
        request.setClientEnv(clientEnv);

        // 订单营销优惠信息
        List<PromotionDTO> promotionDTOList = buildOrderPromotion(orderWithId);
        if (CollectionUtils.isNotEmpty(promotionDTOList)) {
            request.setPromotionDTOs(promotionDTOList);
        }

        // 排序规则
        SortOption sortOption = new SortOption();
        SortUnit sortUnit = new SortUnit();
        sortUnit.setSortField(ProductShopSortFieldEnum.SHOP_COMPOSITE.getCode());
        sortUnit.setSortOrder(ProductShopSortOrderEnum.DESC);
        sortOption.setSortUnits(Lists.newArrayList(sortUnit));
        request.setSortOption(sortOption);

        LocationInfoDTO locationInfoDTO = new LocationInfoDTO();
        // 召回排序场景需要支持 「用户指定经纬度」替换 「实际用户经纬度」
        locationInfoDTO.setLat(getFinalGpsParam(shopListContext.getLat(), shopListContext.getTargetlat()));
        locationInfoDTO.setLng(getFinalGpsParam(shopListContext.getLng(), shopListContext.getTargetlng()));
        request.setLocationInfo(locationInfoDTO);

        ExtendSearchOption extendSearchOption = new ExtendSearchOption();
        List<SearchConditionUnit> andConditions = Lists.newArrayList();

        // 前置门店ID（前一个页面带进来的门店，置顶用）
        if (shopListContext.getShopId() != null && shopListContext.getShopId() > 0) {
            SearchConditionUnit preShopIdCondition = new SearchConditionUnit();
            preShopIdCondition.setConditionField(ProductShopSearchFieldEnum.PRE_SHOP_ID.getName());
            preShopIdCondition.setValues(Lists.newArrayList(String.valueOf(shopListContext.getShopId())));
            andConditions.add(preShopIdCondition);
        }

        // 定位城市ID
        if (shopListContext.getGpsCityId() != null && shopListContext.getGpsCityId() > 0) {
            SearchConditionUnit positionCityId = new SearchConditionUnit();
            positionCityId.setConditionField(ProductShopSearchFieldEnum.POSITION_CITY_ID.getName());
            positionCityId.setValues(Lists.newArrayList(
                    String.valueOf(getFinalCityId(shopListContext.getGpsCityId(), shopListContext.getTargetCityId()))));
            andConditions.add(positionCityId);
        }

        // 首页城市ID
        if (shopListContext.getCityId() != null && shopListContext.getCityId() > 0) {
            SearchConditionUnit homeCityId = new SearchConditionUnit();
            homeCityId.setConditionField(ProductShopSearchFieldEnum.HOME_PAGE_CITY_ID.getName());
            homeCityId.setValues(Lists.newArrayList(String.valueOf(shopListContext.getCityId())));
            andConditions.add(homeCityId);
        }

        // 传入快照ID和场景参数
        // 1）没有合法的快照ID表明是旧订单，不用请求快照系统；
        // 2) 快照ID为-1 表明新订单生成快照ID失败，走新订单黑选单兜底逻辑（实时黑选单）
        // 3) 快照ID 大于 0 查快照系统获取快照黑选单，调用快照系统查询失败，兜底走实时黑选单逻辑
        String versionId = getSnapshotVersion(orderWithId);
        if (StringUtils.isNotBlank(versionId)) {
            // 1.增加快照参数
            SearchConditionUnit productShopSearchConditionUnit = new SearchConditionUnit();
            productShopSearchConditionUnit
                    .setConditionField(ProductShopSearchFieldEnum.MAGICAL_MEMBER_SHOP_BLACKLIST_SNAPSHOT_ID.getName());
            productShopSearchConditionUnit.setValues(Lists.newArrayList(versionId));
            andConditions.add(productShopSearchConditionUnit);
        }

        // 需要过滤营销数据/神会员黑名单的场景
        if (StringUtils.isNotBlank(shopListContext.getOrderId())) {
            request.setSearchCustomizedScene(ProductShopSearchCustomizedSceneEnum.MAGICAL_MEMBER_SHOP_LIST);
        }

        extendSearchOption.setAndConditions(andConditions);
        request.setExtendSearchOption(extendSearchOption);

        return request;
    }

    protected boolean isBookingSource(ShopListContext shopListContext) {
        return shopListContext.getEntryPage() != null
                && shopListContext.getEntryPage().equals(EntryPageEnum.AFTER_ORDER_BOOKING.getValue());
    }

    protected List<PromotionDTO> buildOrderPromotion(UnifiedOrderWithId orderWithId) {
        if (orderWithId == null) {
            return null;
        }
        List<PromotionDTO> promotionDTOList = Lists.newArrayList();

        // OPT券
        List<PromotionDTO> optPromotions = getOptRegionCheckPromotions(orderWithId);
        if (CollectionUtils.isNotEmpty(optPromotions)) {
            promotionDTOList.addAll(optPromotions);
        }

        // 金融政府券
        if (usedGovernmentCoupon(orderWithId)) {
            String code = orderWithId.getOrderPayments().get(0).getExtraData()
                    .get(OrderPaymentExtraFieldEnum.CASHIER_USE_PROMO_ID.getKey());
            PromotionDTO promotionDTO = new PromotionDTO();
            promotionDTO.setPromotionType(PromotionTypeEnum.FINANCE_COUPON.getCode());
            promotionDTO.setPromotionId(NumberUtils.toLong(code));
            promotionDTOList.add(promotionDTO);
        }

        return promotionDTOList;
    }

    protected static boolean usedGovernmentCoupon(UnifiedOrderWithId unifiedOrderWithId) {
        if (unifiedOrderWithId == null || CollectionUtils.isEmpty(unifiedOrderWithId.getOrderPayments())) {
            return false;
        }
        Map<String, String> extraDataMap = unifiedOrderWithId.getOrderPayments().get(0).getExtraData();
        return org.apache.commons.collections4.MapUtils.isNotEmpty(extraDataMap)
                && "1".equals(extraDataMap.get(OrderPaymentExtraFieldEnum.CASHIER_USE_PROMO_RESTRICT_TYPE.getKey()));
    }

    protected List<PromotionDTO> getOptRegionCheckPromotions(UnifiedOrderWithId unifiedOrder) {
        List<PromotionDTO> promotionDTOList = Lists.newArrayList();
        if (Objects.isNull(unifiedOrder) || CollectionUtils.isEmpty(unifiedOrder.getPaymentDetails())) {
            return promotionDTOList;
        }
        List<UnifiedOrderPaymentDetailDTO> paymentDetails = unifiedOrder.getPaymentDetails();
        for (UnifiedOrderPaymentDetailDTO dto : paymentDetails) {
            if (dto == null || !getIfUseOptReceipt(dto)) {
                continue;
            }
            Map<String, String> checkPromotionExtraDataMap;
            try {
                String promotionCheckNibMktExtStr = Optional.of(dto).map(UnifiedOrderPaymentDetailDTO::getExtraData)
                        .map(ext -> ext.get(OrderPaymentDetailExtraFieldEnum.PROMOTION_CHECK_NIBMKT_EXT_DATA.getKey()))
                        .orElse("");
                if (StringUtils.isBlank(promotionCheckNibMktExtStr)) {
                    continue;
                }
                checkPromotionExtraDataMap = JSON.parseObject(promotionCheckNibMktExtStr,
                        new TypeReference<Map<String, String>>() {});
                // 有一个营销是需要校验的就返回true;营销侧定义 verifyType = 1为需要校验门店的场景
                if (Optional.ofNullable(checkPromotionExtraDataMap).map(map -> ("1".equals(map.get("verifyType"))))
                        .orElse(false)) {
                    PromotionDTO promotionDTO = new PromotionDTO();
                    promotionDTO.setPromotionType(PromotionTypeEnum.OPT_COUPON.getCode());
                    promotionDTO.setPromotionId(NumberUtils.toLong(dto.getAmountId()));
                    promotionDTOList.add(promotionDTO);
                }
            } catch (Exception e) {
                log.error("optReceipt check isNeedCheckOptRegion exception", e);
            }
        }
        return promotionDTOList;
    }

    protected boolean getIfUseOptReceipt(UnifiedOrderPaymentDetailDTO unifiedOrderPaymentDetailDTO) {
        try {
            return Optional.ofNullable(unifiedOrderPaymentDetailDTO).map(UnifiedOrderPaymentDetailDTO::getAmountType)
                    .map(amountType -> (AmountType.COUPON.value == amountType
                            || AmountType.SHOPCOUPON.value == amountType))
                    .orElse(false);
        } catch (Exception e) {
            log.error("check ifUseoptReceipt exception", e);
            return false;
        }
    }

    protected BigDecimal getFinalGpsParam(Double user, Double target) {
        if (target != null && target.compareTo(0.0) != 0) {
            return BigDecimal.valueOf(target);
        }
        return BigDecimal.valueOf(user);
    }

    protected Integer getFinalCityId(Integer user, Integer target) {
        if (target != null && target > 0) {
            return target;
        }
        return user;
    }

    protected int getBpClientType(EnvCtx envCtx) {
        if (envCtx.isMt()) {
            if (envCtx.isWxMini()) {
                return com.dianping.deal.common.enums.ClientTypeEnum.mt_weApp.getType();
            }
            return envCtx.isIos() ? com.dianping.deal.common.enums.ClientTypeEnum.mt_mainApp_ios.getType()
                    : com.dianping.deal.common.enums.ClientTypeEnum.mt_mainApp_android.getType();
        }
        if (envCtx.isWxMini()) {
            return com.dianping.deal.common.enums.ClientTypeEnum.dp_weApp.getType();
        }
        return envCtx.isIos() ? com.dianping.deal.common.enums.ClientTypeEnum.dp_mainApp_ios.getType()
                : com.dianping.deal.common.enums.ClientTypeEnum.dp_mainApp_android.getType();
    }

    // 订单id查订单信息
    protected UnifiedOrderWithId getOrderInfo(ShopListContext shopListContext) {
        if (StringUtils.isBlank(shopListContext.getOrderId())) {
            return null;
        }
        return getUnifiedOrderService.getByUnifiedOrderId(shopListContext.getOrderId());
    }

    protected String getSnapshotVersion(UnifiedOrderWithId unifiedOrderWithId) {
        if (unifiedOrderWithId == null || MapUtils.isEmpty(unifiedOrderWithId.getNibExtraFields())) {
            return "";
        }
        return unifiedOrderWithId.getNibExtraFields().get(OrderExtraFieldEnum.TP_PLAT_ORDER_VERIFY_SNAPSHOT.getKey());
    }

    protected String getVersion(ShopListContext shopListContext) {
        EnvCtx envCtx = shopListContext.getEnvCtx();
        if (envCtx.isWxMini()) {
            return shopListContext.getWxVersion();
        }
        return envCtx.getVersion();
    }

    protected PageConfig getPageConfig() {
        PageConfig pageConfig = Lion.getBean("com.sankuai.dzu.tpbase.dztgdetailweb", DEAL_SHOP_CONFIG_LION_KEY,
                PageConfig.class);
        if (pageConfig == null) {
            return new PageConfig();
        }
        return pageConfig;
    }

    public static boolean needLog(Long dealGroupId) {
        if (Environment.isTestEnv()) {
            return true;
        }
        if (Lion.getBoolean("com.sankuai.dzu.tpbase.dztgdetailweb", LOG_ALL, true)) {
            return true;
        }
        List<Long> whitelist = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb", LOG_WHITE_LIST, Long.class);
        return CollectionUtils.isNotEmpty(whitelist) && whitelist.contains(dealGroupId);
    }
}
