package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.cat.Cat;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.detail.dto.DealGroupDetailDTO;
import com.dianping.deal.detail.dto.DealGroupTemplateDetailDTO;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DpProductType;
import com.dianping.mobile.mapi.dztgdetail.common.enums.FreeDealEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.Pair;
import com.dianping.mobile.mapi.dztgdetail.util.FitnessCrossUtils;
import com.dianping.mobile.mapi.dztgdetail.util.TimesDealUtil;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 类型（团购详情，特别提示，其他）
 * DESCRIPTION(1,"团购详情"),
 * SPECIAL_REMINDER(2,"特别提示"),
 * USAGE_FLOW(3,"使用流程"),
 * RETURN_EXCHANGE_NOTICE(4,"退换货须知"),
 * PRODUCT_DESCRIPTION(5,"产品介绍"),
 * SHOP_DESCRIPTION(6,"商户介绍"),
 * PROCESS_TEAM(7,"制作团队"),
 * EDITOR_COMMENT(8,"小编说"),
 * CORPORATION_INFO(9,"公司信息"),
 * REFINED_DESCRIPTION(10,"无表格团购详情"),
 * SERVICE_FLOW(11,"服务流程"),
 * SHOP_PROVIDES(12,"商家服务"),
 * OTHERS(100,"其他");
 * >= 1000 自定义类型
 */
@Data
@Slf4j
public final class DpDealDetailBuilder2 {
	public static final int TYPE_NOTICE = -1;                      //通知
	public static final int TYPE_DESCRIPTION = 1;                 //团购详情
	public static final int TYPE_SPECIAL_REMINDER = 2;            //购买须知
	public static final int TYPE_USAGE_FLOW = 3;
	public static final int TYPE_PRODUCT_DESCRIPTION = 5;         //产品信息
	public static final int TYPE_SHOP_DESCRIPTION = 6;            //商户介绍
	public static final int TYPE_SERVICE_FLOW = 11;
	public static final int TYPE_SHOP_PROVIDES = 12;
	public static final int TYPE_RESERVATION = 40;
	public static final int TYPE_PREPAIDCARD_HELP = 50;
	public static final int TYPE_OTHERS = 100;
	public static final int TYPE_MOREDETAILS = 1000;
	public static final int TYPE_PROMOTION = 1001;
    public static final int TYPE_RECOMMEND_CUISINE = 1002;
	public static final Set<Integer> ALLOWED_TYPES = Sets.newHashSet(1, 2, 3, 5);

	private DealGroupDetailDTO dealGroupDetailDto;
	private DealGroupBaseDTO dealGroupBaseDto;
	private boolean enableStructuredDetails;
	/**
	 * 给新版本原始数据集(HTML)
	 */
	private Map<Integer, Pair> map;

	/**
	 * 附加信息
	 */
	private Map<Integer, Pair> additionalMap = Maps.newTreeMap();
    //新版更多团购详情
    private List<Pair> newMoreDetails = Lists.newArrayList();

    //代金券适用项目
    private String vouncherLimit;

	public DpDealDetailBuilder2() { }

	public static DpDealDetailBuilder2 build(DealGroupBaseDTO dealGroupBaseDto, DealGroupDetailDTO dealGroupDetailDto, boolean enableStructuredDetails) {
        if (dealGroupBaseDto == null || dealGroupDetailDto == null) {
            throw new IllegalArgumentException("dealGroupBaseDto or dealGroupDetailDto is null");
        }

//        DealGroupDetailTransferUtil.procDetail(dealGroupDto);
        DpDealDetailBuilder2 instance = new DpDealDetailBuilder2();
        instance.dealGroupBaseDto = dealGroupBaseDto;
        instance.dealGroupDetailDto = dealGroupDetailDto;
        instance.enableStructuredDetails = enableStructuredDetails;
        return instance;
	}

	public static DpDealDetailBuilder2 build_2(DealGroupBaseDTO dealGroupBaseDto, DealGroupDetailDTO dealGroupDetailDto, boolean enableStructuredDetails, String vouncherLimit) {
		DpDealDetailBuilder2 build = build(dealGroupBaseDto, dealGroupDetailDto, enableStructuredDetails);
		build.vouncherLimit = vouncherLimit;
		return build;
	}

	private void addToMap(int type, String title, String html) {
		if (StringUtils.isNotBlank(html)) {
			map.put(type, Pair.of(title, resizeImages(html), type));
		}
	}

	public void addToMapForTimesDeal(int type, String originalTitle, String newTitle, String html) {
		if (StringUtils.isNotBlank(html)) {
			Pair pair = new Pair(originalTitle, newTitle, html, type, newTitle);
			map.put(type, pair);
		}
	}

    private void addToNewMoreDetails(int type, String title, String html) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.helper.DpDealDetailBuilder2.addToNewMoreDetails(int,java.lang.String,java.lang.String)");
        if (StringUtils.isNotBlank(html)) {
            newMoreDetails.add(Pair.of(title, resizeImages(html), type));
        }
    }

	public void addAdditionalDetail(int type, String title, String html) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.helper.DpDealDetailBuilder2.addAdditionalDetail(int,java.lang.String,java.lang.String)");
        if (StringUtils.isNotBlank(html)) {
			additionalMap.put(type, Pair.of(title, html, type));
		}
	}

	public void initMap(DealCtx dealCtx) {
		if (map == null) {
			map = Maps.newTreeMap();
			if (enableStructuredDetails
					&& dealGroupDetailDto != null
					&& dealGroupDetailDto.getTemplateDetailDTOs() != null) {
				for (DealGroupTemplateDetailDTO dto : dealGroupDetailDto.getTemplateDetailDTOs()) {
//                    if (dto.getType() == TYPE_PRODUCT_DESCRIPTION) { // 为了不让type == 5的详情 覆盖 Deal`GroupDetailTransferUtil工具类中fillDetail方法填充的产品信息
//                        continue;
					if (dealGroupBaseDto.getDealGroupType() == DpProductType.PRODUCT_TYPE_TRIPTICKET && StringUtils.equals(dto.getTitle(), "在线预约流程")) {
						addToMap(TYPE_USAGE_FLOW, "使用流程", dto.getContent());
					} else if(StringUtils.equals(dto.getTitle(), "使用流程")){
                        addToMap(TYPE_USAGE_FLOW,"使用流程",dto.getContent());
                    } else if (TYPE_OTHERS == dto.getType()) {
                        addToNewMoreDetails(TYPE_OTHERS, dto.getTitle(), dto.getContent());
                    } else {
						if (StringUtils.equals(dto.getTitle(), "团购详情")) {
							if (TimesDealUtil.isMultiTimesCard(dealCtx) && !FitnessCrossUtils.isFitnessCrossDeal(dealCtx)) {
								// 多次卡
								try {
									String multiTimesCardTitle = TimesDealUtil.getMultiTimesCardTitle(dealCtx);
									dto.setContent(TimesDealUtil.getTimesTitleToHtmlIfNecessary(dto.getContent(), dealCtx));
									addToMapForTimesDeal(dto.getType(), dto.getTitle(), StringUtils.isEmpty(multiTimesCardTitle) ? dto.getTitle() : multiTimesCardTitle, dto.getContent());
								} catch (Exception e) {
									// 异常兜底，走非多次卡团详方法
									log.error("DpDealDetailBuilder2.initMap fail, e is ", e);
									addToMap(dto.getType(), dto.getTitle(), dto.getContent());
								}
							} else if (FitnessCrossUtils.isFitnessCrossDeal(dealCtx)) {
								// 健身通
								try{
									addToMap(dto.getType(), dto.getTitle(), FitnessCrossUtils.buildDealBaseContent(dto.getContent()));
								} catch (Exception e) {
									// 异常兜底
									log.error("DpDealDetailBuilder2.initMap fail, dpDealId is [{}], e is ", dealCtx.getDpDealId(), e);
									addToMap(dto.getType(), dto.getTitle(), dto.getContent());
								}
							} else {
								addToMap(dto.getType(), dto.getTitle(), dto.getContent());
							}
						} else {
							// 其他
							addToMap(dto.getType(), dto.getTitle(), dto.getContent());
						}
                    }
				}
			}
			// 由于老上单系统的单子仍然在线，所以当新数据（templateDetailDTO）不存在的时候，需要兼容旧数据
//			if (!map.containsKey(TYPE_DESCRIPTION)) {
//			    addToMap(TYPE_DESCRIPTION, "团购详情", dealGroupDetailDto.getInfo());
//			}
//			if (!map.containsKey(TYPE_SPECIAL_REMINDER)) {
//			    addToMap(TYPE_SPECIAL_REMINDER, "购买须知", dealGroupDetailDto.getSpecialPoint());
//			}
			addToMap(TYPE_NOTICE, "通知", dealGroupDetailDto.getImportantPoint());
//			if (!map.containsKey(TYPE_PRODUCT_DESCRIPTION)) {
//			    addToMap(TYPE_PRODUCT_DESCRIPTION, "产品信息", dealGroupDetailDto.getProductInfo());
//			}
//			if (!map.containsKey(TYPE_SHOP_DESCRIPTION)) {
//			    addToMap(TYPE_SHOP_DESCRIPTION, "商户介绍", dealGroupDetailDto.getShopInfo());
//			}

			// 如果使用结构化购买须知且RequestUnStructuredContent=false，则不返回非结构化购买须知信息
			removeToMap(dealCtx);
		}
	}

	public void removeToMap(DealCtx dealCtx) {
		if (Objects.nonNull(dealCtx.getPNSConfig()) && !dealCtx.getPNSConfig().isRequestUnStructuredContent() && map.containsKey(TYPE_SPECIAL_REMINDER)) {
			map.remove(TYPE_SPECIAL_REMINDER);
		}
	}

	private String getPrepaidCardHelpHtml() {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.helper.DpDealDetailBuilder2.getPrepaidCardHelpHtml()");
        return "- 打开大众点评客户端，找到\"我的\"-\"卡包\"<br />- 打开"
				+ dealGroupBaseDto.getDealGroupShortTitle()
				+ "电子储值卡<br />- 结账时向服务员出示此卡<br />- 服务员进行验证、扣款";
	}

	/**
	 * @return 结构化团购详情
	 */
	public List<Pair> toStructedDetails(boolean hasStructedDetail, DealCtx dealCtx) {
		initMap(dealCtx);

		List<Pair> list = Lists.newArrayList();
		List<String> moreDetails = Lists.newArrayList();
        //更多详情页面 产品介绍和商户介绍提前
        if (map.containsKey(TYPE_PRODUCT_DESCRIPTION)) {
            moreDetails.add(toEmbeddedDetail(map.get(TYPE_PRODUCT_DESCRIPTION)));
        }
        if(map.containsKey(TYPE_SHOP_DESCRIPTION)){
            moreDetails.add(toEmbeddedDetail(map.get(TYPE_SHOP_DESCRIPTION)));
        }

        // 由于newMoreDetails里面的内容同TYPE_PRODUCT_DESCRIPTION中的内容重复，注释此代码
        // 又：由于不再使用productInfo，故解除注释
        for (Pair pair : newMoreDetails) {
            moreDetails.add(toEmbeddedDetail(pair));
        }

        moreDetails.add(getSpecialReminderBarHeader());

        if (additionalMap.containsKey(TYPE_RESERVATION)) {
            moreDetails.add(additionalMap.get(TYPE_RESERVATION).getName());
            list.add(wrapDiv(removeHtmlImg(additionalMap.get(TYPE_RESERVATION))));
        }
		if (map.containsKey(TYPE_DESCRIPTION) && !hasStructedDetail && !(dealCtx.isFreeDeal() && dealCtx.getFreeDealType() != null)) {
            moreDetails.add(map.get(TYPE_DESCRIPTION).getName());
            if (additionalMap.containsKey(TYPE_RECOMMEND_CUISINE)) {
                Pair pair = removeHtmlImg(map.get(TYPE_DESCRIPTION));
                if(pair != null){
					pair.setName(pair.getName() + additionalMap.get(TYPE_RECOMMEND_CUISINE).getName());
					list.add(wrapDiv(pair));
				}
            } else {
				Pair p = map.get(TYPE_DESCRIPTION);
				Pair p1 = removeHtmlImg(p);
				Pair p2 = wrapDiv(p1);
				if (StringUtils.isNotBlank(vouncherLimit)) {
					p2.setName(vouncherLimit + p2.getName());
				}
				list.add(p2);
            }
        }
		if (map.containsKey(TYPE_SPECIAL_REMINDER)) {
			moreDetails.add(map.get(TYPE_SPECIAL_REMINDER).getName());

			Pair pair = wrapDiv(removeHtmlImg(map.get(TYPE_SPECIAL_REMINDER)));
			// 购买须知概要
			pair.setSubTitle(ReminderHelper.getReminderSummary(dealCtx));
			if (dealCtx.isFreeDeal() && dealCtx.getFreeDealType() != null) {
				if (dealCtx.getFreeDealType().equals(FreeDealEnum.HOME_DESIGN_BOOKING) || dealCtx.getFreeDealType().equals(FreeDealEnum.LIFE_HOUSEKEEPING_BOOKING)|| dealCtx.getFreeDealType().equals(FreeDealEnum.RECYCLE)) {
					pair.setID("领取须知");
					pair.setKey("领取须知");
					if (CollectionUtils.isNotEmpty(dealCtx.getDealGroupDTO().getAttrs())) {
						dealCtx.getDealGroupDTO().getAttrs().stream()
								.filter(a -> "free_product_notice".equals(a.getName())).findFirst()
								.ifPresent(attrDTO -> pair.setName(StringUtils.join(attrDTO.getValue(), "。")));
					}
					pair.setSubTitle("");
				} else if (dealCtx.getFreeDealType().equals(FreeDealEnum.EDU_TRIAL_BOOKING)) {
					pair.setID("报名须知");
					pair.setKey("报名须知");
				}
			}
			list.add(pair);
		}
		if (map.containsKey(TYPE_NOTICE)) {
			//2016-05-29 团详页面调整“通知”位置
			//2016-06-07 更对详情页面不放“通知”模块
			//moreDetails.add(map.get(TYPE_NOTICE).getName());
			list.add(wrapDiv(removeHtmlImg(map.get(TYPE_NOTICE))));
		}
		if (map.containsKey(TYPE_USAGE_FLOW)) {
			list.add(wrapDiv(removeHtmlImg(map.get(TYPE_USAGE_FLOW))));
		}


		if (dealGroupBaseDto.getDealGroupType() == DpProductType.PRODUCT_TYPE_PREPAIDCARD) {
			list.add(Pair.of("使用帮助", wrapDiv(getPrepaidCardHelpHtml()), TYPE_PREPAIDCARD_HELP));
		}
        moreDetails.add(getSpecialReminderBarTail());
		// moreDetail信息下线灰度策略和结构化购买须知灰度策略保持一致（已和前端确认，该字段废弃）
		if (moreDetails.size() > 0 && !dealCtx.isHitStructuredPurchaseNote()) {
			list.add(Pair.of("更多详情", wrapDiv(StringUtils.join(moreDetails, '\n')), TYPE_MOREDETAILS));
		}
		return list;
	}

	/**
	 * 新版本团购详情最外层需要一个DIV
	 */
	private static String wrapDiv(String html) {
        return "<div>\n" + html + "\n</div>";
	}

	/**
	 * 新版本团购详情最外层需要一个DIV
	 */
	private static Pair wrapDiv(Pair p) {
		return new Pair(p.getId(), p.getID(), wrapDiv(p.getName()), p.getType(), p.getKey());
	}

	private static Pair removeHtmlImg(Pair p) {
		if (p != null && p.getName() != null) {
			p.setName(DpDealDetailFormatter.removeHtmlImage(p.getName()));
		}
		return p;
	}

	private static String toEmbeddedDetail(Pair p) {
	    if (p != null) {
	        return "<div class=\"detail-box\"><h3 class=\"tit\">"
	                + StringUtils.defaultString(p.getID())
	                + "</h3>"
	                + StringUtils.defaultString(p.getName())
	                + "</div>";
	    }
	    return StringUtils.EMPTY;
	}

    //给下沉的详情加上“购买须知文案”
    private static String getSpecialReminderBarHeader(){
        return "<div class=\"detail-box\"><h3 class=\"tit\">购买须知</h3>";
    }

    private static String getSpecialReminderBarTail(){
        return "</div>";
    }

	private String resizeImages(String html) {
		// (640c400) -> (450x1024)
		// (640x1024) -> (450x1024)
		return StringUtils.replace(StringUtils.replace(html, "(640c400)/thumb.jpg", "(450x1024)/thumb.jpg"), "(640x1024)/thumb.jpg", "(450x1024)/thumb.jpg");
	}

}