package com.dianping.mobile.mapi.dztgdetail.datatype.resp.module;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

import java.io.Serializable;

@MobileDo(id = 0xf48e)
public class AbConfigBo implements Serializable {

    public AbConfigBo(String expId, String expResult, String expBiInfo) {
        this.expId = expId;
        this.expResult = expResult;
        this.expBiInfo = expBiInfo;
    }

    @FieldDoc(description = "实验ID")
    @MobileField(key = 0x85df)
    private String expId;

    @FieldDoc(description = "实验结果")
    @MobileField(key = 0x9263)
    private String expResult;

    @FieldDoc(description = "实验上报字段")
    @MobileField(key = 0xddc3)
    private String expBiInfo;

    @FieldDoc(description = "实验扩展字段")
    @MobileField(key = 0x97fe)
    private String expExtraInfo;

    public String getExpId() {
        return expId;
    }

    public void setExpId(String expId) {
        this.expId = expId;
    }

    public String getExpResult() {
        return expResult;
    }

    public void setExpResult(String expResult) {
        this.expResult = expResult;
    }

    public String getExpBiInfo() {
        return expBiInfo;
    }

    public void setExpBiInfo(String expBiInfo) {
        this.expBiInfo = expBiInfo;
    }

    public String getExpExtraInfo() {
        return expExtraInfo;
    }

    public void setExpExtraInfo(String expExtraInfo) {
        this.expExtraInfo = expExtraInfo;
    }
}