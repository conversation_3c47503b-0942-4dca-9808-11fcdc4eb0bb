package com.dianping.mobile.mapi.dztgdetail.datatype.resp.other;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

public class PriceExplainContentDTO implements Serializable {
    /**
     * 图片URL
     */
    @MobileDo.MobileField
    private String picurl;

    /**
     * 价格说明文案
     */
    @MobileDo.MobileField
    private String text;

    public String getPicurl() {
        return picurl;
    }

    public void setPicurl(String picurl) {
        this.picurl = picurl;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }
}
