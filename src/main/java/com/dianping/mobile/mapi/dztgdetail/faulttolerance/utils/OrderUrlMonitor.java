package com.dianping.mobile.mapi.dztgdetail.faulttolerance.utils;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.APP_KEY;

public class OrderUrlMonitor {

    private static final String ORDER_URL_MONITOR = "ORDER_URL_MONITOR";

    public static void monitor(DealGroupPBO result) {
        try {
            if (result == null
                    || result.getBuyBar() == null
                    || CollectionUtils.isEmpty(result.getBuyBar().getBuyBtns())) {
                return;
            }
            DealBuyBar buyBar = result.getBuyBar();
            for (DealBuyBtn buyBtn : buyBar.getBuyBtns()) {
                if (buyBtn != null) {
                    monitor(buyBtn.getRedirectUrl());
                }
            }
        } catch (Exception e) {
            Cat.logError(e);
        }
    }

    public static void monitor(String url) {
        try {
            if (StringUtils.isBlank(url)) {
                return;
            }
            if (!Lion.getBoolean(APP_KEY, "orderUrlMonitorSwitch", false)) {
                Cat.logEvent(ORDER_URL_MONITOR, "orderUrlMonitor-close");
                return;
            }
            Cat.logEvent(ORDER_URL_MONITOR, "orderUrlMonitor-open");
            List<String> schemaList = monitorOrderUrlSchema();
            for (String schema : schemaList) {
                if (url.startsWith(schema)) {
                    Cat.logEvent(ORDER_URL_MONITOR, "orderUrlMonitor-hit");
                    Cat.logEvent(ORDER_URL_MONITOR, schema);
                    return;
                }
            }
            Cat.logEvent(ORDER_URL_MONITOR, "orderUrlMonitor-miss");
        } catch (Exception e) {
            Cat.logError(e);
        }
    }

    public static List<String> monitorOrderUrlSchema() {
        try {
            List<String> prefix = Lion.getList(APP_KEY, "monitorOrderUrlSchema", String.class, Collections.emptyList());
            return prefix == null ? Collections.emptyList() : prefix;
        } catch (Exception e) {
            Cat.logError(e);
            return Collections.emptyList();
        }
    }

}
