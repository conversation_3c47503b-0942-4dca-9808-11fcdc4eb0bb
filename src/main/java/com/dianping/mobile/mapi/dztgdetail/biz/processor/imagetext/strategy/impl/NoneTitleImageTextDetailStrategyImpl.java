package com.dianping.mobile.mapi.dztgdetail.biz.processor.imagetext.strategy.impl;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.imagetext.strategy.AbstractImageTextDetailStrategy;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.imagetext.strategy.ImageTextDetailStrategy;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ImageTextStrategyEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ContentDetailPBO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-12-11
 * @desc 不展示图文详情处理策略
 */
@Component
public class NoneTitleImageTextDetailStrategyImpl extends AbstractImageTextDetailStrategy implements ImageTextDetailStrategy {
    @Override
    public ImageTextStrategyEnum getStrategyName() {
        return ImageTextStrategyEnum.NONE_TITLE;
    }

    @Override
    public ContentDetailPBO getContentDetail(DealGroupDTO dealGroupDTO, List<ContentPBO> contents, int foldThreshold) {
        ContentDetailPBO contentDetail = new ContentDetailPBO();
        // 图文详情标题
        contentDetail.setTitle("");
        // 图文详情展示内容
        contentDetail.setContents(contents);
        // 默认不折叠
        contentDetail.setFold(false);
        // 折叠阈值
        contentDetail.setFoldThreshold(contents.size() + 1);
        return contentDetail;
    }
}
