package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class CommonModuleUtil {

    /**
     * 查询通用模块需请求的moduleKey
     * @param dealGroupDTO
     * @param envCtx
     * @return
     */
    public static List<String> getModuleKeys(DealGroupDTO dealGroupDTO, EnvCtx envCtx) {
        if (dealGroupDTO == null || dealGroupDTO.getCategory() == null) {
            return Lists.newArrayList();
        }
        CommonConfig config = Lion.getBean(MdpContextUtils.getAppKey(), LionConstants.COMMON_MODULE_CALL_CONFIG, CommonConfig.class);
        if (config == null) {
            return Lists.newArrayList();
        }
        List<String> moduleList = getModuleByCategory(dealGroupDTO, config);
        if (MapUtils.isEmpty(config.getModuleClient())) {
            return moduleList;
        }
        return moduleList.stream().filter(m -> isTargetClient(envCtx.getDztgClientTypeEnum(), config.getModuleClient().get(m))).collect(Collectors.toList());
    }

    private static List<String> getModuleByCategory(DealGroupDTO dealGroupDTO, CommonConfig config) {
        if (dealGroupDTO.getCategory().getServiceTypeId() != null && dealGroupDTO.getCategory().getServiceTypeId() > 0 && config.getServiceTypeModule().containsKey(dealGroupDTO.getCategory().getServiceTypeId())) {
            return config.getServiceTypeModule().get(dealGroupDTO.getCategory().getServiceTypeId());
        }
        if (dealGroupDTO.getCategory().getCategoryId() != null && dealGroupDTO.getCategory().getCategoryId() > 0 && config.getCategoryModule().containsKey(dealGroupDTO.getCategory().getCategoryId())) {
            return config.getCategoryModule().get(dealGroupDTO.getCategory().getCategoryId());
        }
        return Lists.newArrayList();
    }


    private static boolean isTargetClient(DztgClientTypeEnum dztgClientTypeEnum, List<Integer> client) {
        if (CollectionUtils.isEmpty(client)) {
            return true;
        }
        return client.contains(dztgClientTypeEnum.getCode());
    }

    @Data
    public static class CommonConfig {
        Map<Long, List<String>> serviceTypeModule;
        Map<Long, List<String>> categoryModule;
        Map<String, List<Integer>> moduleClient;
    }
}
