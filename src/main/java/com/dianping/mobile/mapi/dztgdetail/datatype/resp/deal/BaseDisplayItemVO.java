package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@TypeDoc(description = "展示条目模型")
@MobileDo(id = 0xcc2d)
public class BaseDisplayItemVO implements Serializable {

    @FieldDoc(description = "名称")
    @MobileField(key = 0x7ab8)
    private String name;

    @FieldDoc(description = "值")
    @MobileField(key = 0x53c7)
    private List<String> values;

    @FieldDoc(description = "背景图url")
    @MobileField(key = 0x3364)
    private String bgPicUrl;

    @FieldDoc(description = "icon")
    @MobileField(key = 0x3c48)
    private String icon;

    @FieldDoc(description = "描述")
    @MobileField(key = 0xfebf)
    private String desc;

    @FieldDoc(description = "详情")
    @MobileField(key = 0xa83b)
    private String detail;

}
