package com.dianping.mobile.mapi.dztgdetail.common.enums;

/**
 * <AUTHOR>
 * @create 2025/7/2 14:22
 */
public enum CategoryToDtEnum {
    // 在这里定义枚举实例
    // 例如: FOOD(1, 10, "美食", "food"),
    // MOVIE(2, 20, "电影", "movie");
    BEAUTY_HAIR(5501, 501, "美发", "beauty"),
    BEAUTY_NAIL(5502, 502, "美甲/美睫", "beauty"),
    BEAUTY_BODY(5503, 503, "美容美体", "beauty"),
    BEAUTY_OTHER(5505, 505, "其他", "beauty"),
    BEAUTY_YOGA(5507, 507, "舞蹈/瑜伽", "beauty"),
    ;

    // 可以根据实际需求添加更多枚举实例

    private final int dt;
    private final int category;
    private final String title;
    private final String channel;

    CategoryToDtEnum(int dt, int category, String title, String channel) {
        this.dt = dt;
        this.category = category;
        this.title = title;
        this.channel = channel;
    }

    public int getDt() {
        return dt;
    }

    public int getCategory() {
        return category;
    }

    public String getTitle() {
        return title;
    }

    public String getChannel() {
        return channel;
    }

    // 根据category查找对应的枚举
    public static CategoryToDtEnum getByCategory(int category) {
        for (CategoryToDtEnum item : values()) {
            if (item.getCategory() == category) {
                return item;
            }
        }
        return null;
    }

    // 根据dt查找对应的枚举
    public static CategoryToDtEnum getByDt(int dt) {
        for (CategoryToDtEnum item : values()) {
            if (item.getDt() == dt) {
                return item;
            }
        }
        return null;
    }
}