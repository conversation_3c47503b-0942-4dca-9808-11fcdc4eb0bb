package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.DecryptedField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-10-09
 * @desc 获取团单简要信息的请求体
 */
@Data
@TypeDoc(description = "获取团单简要信息的请求体")
@MobileRequest
public class GetDealTinyInfoRequest implements IMobileRequest, Serializable {
    @FieldDoc(description = "团单Id", rule = "美团平台为美团团单Id，点评平台为点评团单Id")
    @MobileRequest.Param(name = "dealGroupId", required = true)
    private String dealGroupId;

    @FieldDoc(description = "门店Id", rule = "美团平台为美团门店Id，点评平台为点评门店Id")
    @MobileRequest.Param(name = "shopId", required = true)
    private String shopId;
    @MobileRequest.Param(name = "shopIdEncrypt")
    @DecryptedField(targetFieldName = "shopId")
    private String shopIdEncrypt;

    @FieldDoc(
            description = "页面来源",
            rule = "deal-团详场景；shopcarnoselect-购物车&当前商品未被选中的场景；shopcarselect-购物车&当前商品被选中的场景；shelf-货架场景；caixi-猜喜场景；beauty_buyinglist-丽人常买清单"
    )
    @MobileRequest.Param(name = "pageSource", required = true)
    private String pageSource;

    @FieldDoc(description = "用户定位城市Id")
    @MobileRequest.Param(name = "cityId")
    private Integer cityId;

    @FieldDoc(description = "用户定位纬度")
    @MobileRequest.Param(name = "lat")
    private Double lat;

    @FieldDoc(description = "用户定位经度")
    @MobileRequest.Param(name = "lng")
    private Double lng;

    @FieldDoc(description = "平台", rule = "1-点评；2-美团")
    private Integer platform;

    @FieldDoc(description = "业务类型")
    @MobileRequest.Param(name = "businessType")
    private String businessType;
}
