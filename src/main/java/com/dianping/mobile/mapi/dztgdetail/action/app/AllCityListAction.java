package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.action.DefaultAction;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.AllCityListRequest;
import com.dianping.mobile.mapi.dztgdetail.facade.AllCityListFacade;
import com.dianping.mobile.mapi.dztgdetail.helper.AppCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.util.AntiCrawlerUtils;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@InterfaceDoc(displayName = "全量城市列表",
        type = "restful",
        description = "全量城市列表，美团下发美团城市列表，点评下发点评城市列表",
        scenarios = "全量城市列表，美团下发美团城市列表，点评下发点评城市列表",
        host = "http://mapi.dianping.com/",
        authors = "litengfei04"
)
@Controller("general/platform/dztgdetail/allcitylist.bin")
@Action(url = "allcitylist.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class AllCityListAction extends DefaultAction<AllCityListRequest> {

    @Resource
    private AllCityListFacade allCityListFacade;

    @Override
    protected IMobileResponse validate(AllCityListRequest request, IMobileContext iMobileContext) {
        if (AntiCrawlerUtils.hide(iMobileContext)) {
            return new CommonMobileResponse(Resps.NoDataResp);
        }
        return null;
    }

    @Override
    protected IMobileResponse execute(AllCityListRequest request, IMobileContext context) {
        return new CommonMobileResponse(allCityListFacade.getAllCityList(AppCtxHelper.isMeituanClient(context)));
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return Collections.emptyList();
    }

}
