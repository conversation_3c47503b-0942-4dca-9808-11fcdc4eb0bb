package com.dianping.mobile.mapi.dztgdetail.button.joy;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.enums.FitnessCrossIdentityEnum;
import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.constants.FitnessCrossConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.LeadActionEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.*;
import com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;

@Slf4j
public class FitnessCrossButtonBuilder extends AbstractButtonBuilder {

    /**
     * 美团展示新图片的最低版本
     */
    private static final String MT_MIN_VERSION = "12.20.400";

    /**
     * 点评展示新图片的最低版本
     */
    private static final String DP_MIN_VERSION = "11.17.0";

    @Override
    protected void doBuild(DealCtx context, ButtonBuilderChain chain) {
        context.setBuyBar(buildBar(context));
        chain.build(context);
    }

    /**
     * 构建按钮模块
     */
    private DealBuyBar buildBar(DealCtx ctx) {
        // 设置按钮信息
        DealBuyBar buyBar = new DealBuyBar(DealBuyBar.BuyType.CROSS_CARD.type, Collections.singletonList(buildDealBuyBtn(ctx)));
        // 设置按钮横幅信息
        buyBar.setBuyBanner(buildBuyBanner(ctx));
        return buyBar;
    }

    /**
     * 构建按钮横幅信息
     */
    private DealBuyBanner buildBuyBanner(DealCtx ctx) {
        DealBuyBanner buyBanner = new DealBuyBanner();

        FitnessCrossBO.Config textConfig = (ctx == null || ctx.getFitnessCrossBO() == null) ? null : ctx.getFitnessCrossBO().getConfig();

        // 横幅左侧图标
        boolean isNewVersion = isNewVersion(ctx);
        buyBanner.setIconUrl(getBuyBannerIconUrl(isNewVersion, textConfig));
        buyBanner.setIconHeight((textConfig == null || !isNewVersion) ? 11 : textConfig.getBuyBannerIconHeight());
        buyBanner.setIconWidth((textConfig == null || !isNewVersion) ? 11 : textConfig.getBuyBannerIconWidth());

        // 横幅右侧图标
        buyBanner.setArrowIconUrl(textConfig == null ? null : textConfig.getBuyBannerArrowIconUrl());

        // 点击引导
        buyBanner.setLeadAction(LeadActionEnum.REDIRECT_URL.getCode());
        buyBanner.setLeadUrl(getRechargeFromDealBaseUrl(ctx));
        buyBanner.setLeadText(FitnessCrossConstants.BUY_BANNER_LEAD_TEXT);
        buyBanner.setLeadTextColor(FitnessCrossConstants.BUY_BANNER_LEAD_TEXT_COLOR);
        buyBanner.setBackGroundColor(FitnessCrossConstants.BUY_BANNER_BACKGROUND_COLORS);

        // 内容
        buyBanner.setContent(buildBannerContent(ctx));

        buyBanner.setShow(true);
        buyBanner.setBannerType(FitnessCrossConstants.BUY_BANNER_TYPE);

        return buyBanner;
    }

    /**
     * 是否是新版本，兜底新版本
     */
    private boolean isNewVersion(DealCtx ctx) {
        if (ctx == null || ctx.getEnvCtx() == null || StringUtils.isEmpty(ctx.getEnvCtx().getVersion())) {
            return true;
        }

        // 最低可用版本
        String minVersion = ctx.getEnvCtx().isMt() ? MT_MIN_VERSION : DP_MIN_VERSION;
        return minVersion.compareTo(ctx.getEnvCtx().getVersion()) <= 0;
    }

    /**
     * 按钮横幅icon url
     */
    private String getBuyBannerIconUrl(boolean isNewVersion, FitnessCrossBO.Config textConfig) {
        if (textConfig == null) {
            return null;
        }

        return isNewVersion ? textConfig.getNewVersionBuyBannerIconUrl() : textConfig.getOldVersionBuyBannerIconUrl();
    }

    /**
     * 构建横幅内容
     */
    private String buildBannerContent(DealCtx ctx) {
        if (ctx == null
                || ctx.getFitnessCrossBO() == null
                || ctx.getFitnessCrossBO().getConfig() == null
                || MapUtils.isEmpty(ctx.getFitnessCrossBO().getConfig().getBtnBarTextMap())) {
            return null;
        }
        FitnessCrossIdentityEnum identityEnum = ctx.getFitnessCrossBO().getIdentityEnum();
        String key = identityEnum == null ? FitnessCrossIdentityEnum.TOURIST.name() : identityEnum.name();
        return JsonLabelUtil.fitnessCrossBannerJson(ctx.getFitnessCrossBO().getConfig().getBtnBarTextMap().get(key));
    }

    /**
     * 构建按钮
     */
    private DealBuyBtn buildDealBuyBtn(DealCtx ctx) {
        if (ctx.getFitnessCrossBO() == null
                || ctx.getFitnessCrossBO().getConfig() == null
                || MapUtils.isEmpty(ctx.getFitnessCrossBO().getConfig().getBtnBarTextMap())) {
            // 参数错误，构建默认的按钮
            return getDefaultBtn(ctx);
        }
        DealBuyBtn buyBtn = new DealBuyBtn(true, FitnessCrossConstants.BUY_BUTTON_TITLE);
        buyBtn.setBtnTag(FitnessCrossConstants.BUY_BUTTON_TAG);
        buyBtn.setRedirectUrl(getRedirectUrl(ctx));
        buyBtn.setBtnIcons(buildDealBuyBtnIcons());
        buyBtn.setBtnSubTitle(FitnessCrossConstants.BUY_BUTTON_SUB_TITLE);
        return buyBtn;
    }

    /**
     * 获取按钮跳转地址
     */
    private String getRedirectUrl(DealCtx ctx) {
        // 兜底跳转到充值页面
        if (ctx.getFitnessCrossBO() == null) {
            return getRechargeFromDealBaseUrl(ctx);
        }

        // 有可用卡券跳到提单页
        if (ctx.getEnvCtx() != null && ctx.getEnvCtx().isLogin() && ctx.getFitnessCrossBO().isHasAvailableCoupon()) {
            return ctx.getFitnessCrossBO().getOrderUrl();
        }

        // 无可用卡券，跳转到充值页
        return getRechargeFromDealBaseUrl(ctx);
    }

    /**
     * 获取从团详跳转到充值页的url
     */
    private String getRechargeFromDealBaseUrl(DealCtx ctx) {
        if (ctx == null) {
            log.info("FitnessCrossButtonBuilder.getRechargeFromDealBaseUrl fail, ctx is null");
            return null;
        }
        String urlFormat = ctx.isMt() ? FitnessCrossConstants.MT_RECHARGE_BTN_JUMP_URL : FitnessCrossConstants.DP_RECHARGE_BTN_JUMP_URL;
        long shopId = ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId();
        long dealId = ctx.isMt() ? ctx.getMtId() : ctx.getDpId();
        return String.format(urlFormat, shopId, dealId);
    }

    /**
     * 构建默认按钮
     */
    private DealBuyBtn getDefaultBtn(DealCtx ctx) {
        DealBuyBtn buyBtn = new DealBuyBtn(true, FitnessCrossConstants.BUY_BUTTON_TITLE);
        buyBtn.setRedirectUrl(getRechargeFromDealBaseUrl(ctx));
        return buyBtn;
    }

    /**
     * 构建按钮描述
     */
    private List<DealBuyBtnIcon> buildDealBuyBtnIcons() {
        DealBuyBtnIcon btnIcon = new DealBuyBtnIcon();
        btnIcon.setTitle(FitnessCrossConstants.BUY_BUTTON_ICON_TITLE);
        btnIcon.setStyle(FitnessCrossConstants.BUY_BUTTON_ICON_STYLE);
        btnIcon.setType(FitnessCrossConstants.BUY_BUTTON_ICON_TYPE);
        btnIcon.setTitleColor(FitnessCrossConstants.BUY_BUTTON_ICON_TITLE_COLOR);
        btnIcon.setBgColor(FitnessCrossConstants.BUY_BUTTON_ICON_TITLE_BACKGROUND_COLOR);
        btnIcon.setBorderColor(FitnessCrossConstants.BUY_BUTTON_ICON_TITLE_BORDER_COLOR);
        return Collections.singletonList(btnIcon);
    }

}
