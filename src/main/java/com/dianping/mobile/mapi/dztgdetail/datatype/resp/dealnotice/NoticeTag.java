package com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealnotice;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2024/9/30 15:05
 * https://mobile.sankuai.com/studio/model/info/39394
 */
@Data
@MobileDo(id = 0xe89e4182)
public class NoticeTag implements Serializable {
    /**
     * 不同样式对应的值，比如高亮下发对应颜色值
     */
    @MobileDo.MobileField(key = 0x1b3a)
    private String style;

    /**
     * 展示类型，0-纯文本，1-高亮
     */
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;

    /**
     * icon信息
     */
    @MobileDo.MobileField(key = 0x3c48)
    String icon;

    /**
     * tag内容
     */
    @MobileDo.MobileField(key = 0x451b)
    private String text;
}
