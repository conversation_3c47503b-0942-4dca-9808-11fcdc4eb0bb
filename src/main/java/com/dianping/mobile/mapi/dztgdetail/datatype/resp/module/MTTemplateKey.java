package com.dianping.mobile.mapi.dztgdetail.datatype.resp.module;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/6/17.
 */
@TypeDoc(description = "页面模板数据模型")
@MobileDo(name = "TemplateKey")
@Data
public class MTTemplateKey {

    @FieldDoc(description = "模板key")
    @MobileField(key = 0x263e)
    private String key;

    @FieldDoc(description = "是否上海团单")
    @MobileField(key = 0xe754)
    private boolean isDp;

    @FieldDoc(description = "是否到综单中心")
    @MobileField(key = 0xcc2)
    private boolean isDzx;

    @FieldDoc(description = "是否走上海订单")
    @MobileField(key = 0x2b07)
    private boolean isDpOrder;

    @FieldDoc(description = "模板额外的key")
    @MobileField(key = 0x6cea)
    private String extraInfo;

    @FieldDoc(description = "模块配置，通常用于A/B-TEST")
    @MobileField(key = 0x33b0)
    private List<ModuleConfigDo> moduleConfigs;

    @FieldDoc(description = "模块的AB，目前用于实验平台斗斛系统")
    @MobileField(key = 0xbae3)
    private List<ModuleAbConfigDo> moduleAbConfigs;

    // 是否为降级配置
    private boolean isDegrade;
}