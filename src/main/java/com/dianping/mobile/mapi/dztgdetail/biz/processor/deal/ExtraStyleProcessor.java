package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.style.protocol.AbConfig;
import com.dianping.deal.style.protocol.ExtraStyleResponse;
import com.dianping.deal.style.protocol.ModuleAbConfig;
import com.dianping.deal.style.protocol.ModuleDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealStyleWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Cons;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BusinessStyleEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ModuleConfigsEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.style.BusinessStyle;
import com.dianping.mobile.mapi.dztgdetail.entity.ExpResultConfig;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import com.dianping.mobile.mapi.dztgdetail.util.DealVersionUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Future;

public class ExtraStyleProcessor extends AbsDealProcessor {
    private static final String ATTRIBUTE_TORT_VALUE = "1";
    private static final String TORT_TEXT = "当前团购已失效，请选择其他团购";
    private static final String TORT_TITLE = "当前团购已失效";
    private static final String TORT_DESC = "请选择其他团购";
    public static final List<String> FOLD_STYLE_EXP_RESULT_LIST = Lists.newArrayList("a", "b", "c");

    public static final String PIPE_WUYOUTONG = "pipe_wuyoutong";
    public static final String LIFE_FIX_TODOOR = "easylife_structure";

    public static final String STRUCT_DETAIL_KEY = "dealdetail_gc_packagedetail";


    @Autowired
    private DealStyleWrapper dealStyleWrapper;
    @Autowired
    private DouHuBiz douHuBiz;
    @Resource
    private DouHuService douHuService;

    @Override
    public boolean isEnable(DealCtx ctx) {
        return ctx.getEnvCtx() != null && !ctx.getEnvCtx().isFromH5();
    }

    @Override
    public void prepare(DealCtx ctx) {
        Future extraFuture = dealStyleWrapper.prepareExtraStyle(ctx);
        ctx.getFutureCtx().setExtraStyleFuture(extraFuture);
    }

    @Override
    public void process(DealCtx ctx) {
        ExtraStyleResponse extraStyleResp = dealStyleWrapper.getExtraStyleResponse(ctx.getFutureCtx().getExtraStyleFuture());
        if(ctx.isMt()) {
            MTTemplateKey mtTemplateKey = generateMtTemplateKey(ctx, extraStyleResp);
            if(mtTemplateKey == null) {
                return;
            }
            List<ModuleConfigDo> moduleConfigs = mtTemplateKey.getModuleConfigs();
            List<ModuleConfigDo> lyyModuleConfigs = getLyyModuleConfigs(ctx.getLyyuserid());

            if (CollectionUtils.isNotEmpty(moduleConfigs)) {
                moduleConfigs.addAll(lyyModuleConfigs);
            } else {
                if (CollectionUtils.isNotEmpty(lyyModuleConfigs)) {
                    mtTemplateKey.setModuleConfigs(lyyModuleConfigs);
                }
            }
            ctx.setMtTemplateKey(mtTemplateKey);
            catMtTemplateResult(mtTemplateKey);
        } else {
            DealStyle dealStyle = generateDpDealStyle(ctx, extraStyleResp);
            ctx.setDealStyle(dealStyle);
            catDpTemplateResult(dealStyle);
        }
        ModuleConfigsModule moduleConfigsModule = buildModuleConfigsModule(ctx);
        buildComparePriceTabStyleModuleConfig(ctx, moduleConfigsModule);
        ctx.setModuleConfigsModule(moduleConfigsModule);
        if (isJuShengQianBigPhoto(moduleConfigsModule)) {
            // 展示头图最近购买弹幕
            if(ctx.getDealExtraTypes() == null){
                ctx.setDealExtraTypes(Lists.newArrayList());
            }
            ctx.getDealExtraTypes().add("JUSHENGQIAN_DEAL");
        }
        // CPV改造团单类目如果涉及到页面布局调整，需要配置新的样式key
        boolean oldMetaVersion = DealVersionUtils.isOldMetaVersion(ctx.getDealGroupDTO(),
                LionConstants.COMMON_MODULE_CALL_VERSION_CONFIG);
        if (!oldMetaVersion && Objects.nonNull(ctx.getModuleConfigsModule())) {
            Map<String, String> dealStyleKeyForCpvMap = LionConfigUtils.getDealStyleKeyForCpv();
            String key = ctx.getModuleConfigsModule().getKey();
            String newKey = dealStyleKeyForCpvMap.getOrDefault(key, key);
            ctx.getModuleConfigsModule().setKey(newKey);
        }
    }

    private void catMtTemplateResult(MTTemplateKey result) {
        if(result == null) {
            return;
        }
        String key = StringUtils.isBlank(result.getKey()) ? "empty" : result.getKey();
        String extraInfo = StringUtils.isBlank(result.getExtraInfo()) ? "empty" : result.getExtraInfo();
        String template = key + "_" + extraInfo;

        Map<String, String> tags = new HashMap<>();
        tags.put("template", template);

        Cat.logMetricForCount("MtTemplate", tags);
    }

    private void catDpTemplateResult(DealStyle result) {
        if(result == null) {
            return;
        }
        String key = StringUtils.isBlank(result.getModuleKey()) ? "empty" : result.getModuleKey();
        String extraInfo = StringUtils.isBlank(result.getExtra()) ? "empty" : result.getExtra();
        String template = key + "_" + extraInfo;

        Map<String, String> tags = new HashMap<>();
        tags.put("template", template);

        Cat.logMetricForCount("DpTemplate", tags);
    }

    /**
     * 乐摇摇美团团详需隐藏更多团购及适用门店模块
     * @param lyyUserId
     */
    private List<ModuleConfigDo> getLyyModuleConfigs(String lyyUserId) {
        if (StringUtils.isBlank(lyyUserId)) {
            return Lists.newArrayList();
        }

        List<ModuleConfigDo> configs = new ArrayList<>();

        ModuleConfigDo moreDealsModule = new ModuleConfigDo();
        moreDealsModule.setKey("dealdetail_gc_moredeals");
        moreDealsModule.setValue("NA");

        configs.add(moreDealsModule);

        ModuleConfigDo shopModule = new ModuleConfigDo();
        shopModule.setKey("GCPlatformModules/picasso_deal_detail_shop_info_mt_module");
        shopModule.setValue("NA");

        configs.add(shopModule);

        return configs;
    }

    public MTTemplateKey generateMtTemplateKey(DealCtx dealCtx, ExtraStyleResponse extraStyleResp) {
        MTTemplateKey templateKeyDo = dealCtx.getMtTemplateKey();

        if(templateKeyDo.isDegrade()) {
            return templateKeyDo;
        }
        if (extraStyleResp != null && extraStyleResp.isSuccess()) {
            templateKeyDo.setExtraInfo(extraStyleResp.getExtraStyle());

            List<ModuleConfigDo> moduleConfigs = transferModule(extraStyleResp.getModules());
            templateKeyDo.setModuleConfigs(moduleConfigs);

            List<ModuleAbConfigDo> moduleAbConfigs = transferModuleAb(extraStyleResp.getModuleAbConfigs());

            if (CollectionUtils.isNotEmpty(moduleAbConfigs) && CollectionUtils.isNotEmpty(templateKeyDo.getModuleAbConfigs())) {
                templateKeyDo.getModuleAbConfigs().addAll(moduleAbConfigs);
            } else if (CollectionUtils.isNotEmpty(moduleAbConfigs)) {
                templateKeyDo.setModuleAbConfigs(moduleAbConfigs);
            }
        }

        return templateKeyDo;
    }

    private List<ModuleConfigDo> transferModule(List<ModuleDTO> moduleDTOS) {
        if (CollectionUtils.isEmpty(moduleDTOS)) {
            return null;
        }

        List<ModuleConfigDo> moduleConfigDos = Lists.newArrayListWithCapacity(moduleDTOS.size());

        for (ModuleDTO moduleDTO : moduleDTOS) {
            if (moduleDTO != null) {
                ModuleConfigDo moduleConfigDo = new ModuleConfigDo();
                moduleConfigDo.setKey(moduleDTO.getModuleKey());
                moduleConfigDo.setValue(moduleDTO.getModuleValue());
                moduleConfigDos.add(moduleConfigDo);
            }
        }

        return moduleConfigDos;
    }

    private List<ModuleAbConfigDo> transferModuleAb(List<ModuleAbConfig> moduleAbConfigs) {
        if (CollectionUtils.isEmpty(moduleAbConfigs)) {
            return null;
        }
        List<ModuleAbConfigDo> result = Lists.newArrayListWithCapacity(moduleAbConfigs.size());
        for (ModuleAbConfig input : moduleAbConfigs) {
            ModuleAbConfigDo configDo = new ModuleAbConfigDo();
            configDo.setKey(input.getKey());
            if (CollectionUtils.isNotEmpty(input.getAbConfigs())) {
                List<AbConfigDo> abConfigDoList = Lists.transform(input.getAbConfigs(), new Function<AbConfig, AbConfigDo>() {
                    @Override
                    public AbConfigDo apply(@Nullable AbConfig one) {
                        AbConfigDo out = new AbConfigDo(one.getExpId(), one.getExpResult(), one.getExpBiInfo());
                        return out;
                    }
                });
                configDo.setConfigs(abConfigDoList);
            }
            result.add(configDo);
        }
        return result;
    }

    public DealStyle generateDpDealStyle(DealCtx dealCtx, ExtraStyleResponse extraStyleResp) {
        DealStyle dealStyle = dealCtx.getDealStyle();
        if (dealStyle != null) {

            if (extraStyleResp != null && extraStyleResp.isSuccess()) {
                dealStyle.setExtra(extraStyleResp.getExtraStyle());
                dealStyle.setModuleConfigs(transferModuleForDp(extraStyleResp.getModules()));
                List<ModuleAbConfigBo> moduleAbConfigs = transferModuleAbForDp(extraStyleResp.getModuleAbConfigs());

                if (CollectionUtils.isNotEmpty(dealStyle.getModuleAbConfigs()) && CollectionUtils.isNotEmpty(moduleAbConfigs)) {
                    dealStyle.getModuleAbConfigs().addAll(moduleAbConfigs);
                } else if (CollectionUtils.isNotEmpty(moduleAbConfigs)) {
                    dealStyle.setModuleAbConfigs(moduleAbConfigs);
                }
            }

        }
        return dealStyle;
    }

    private List<ModuleConfigDo> transferModuleForDp(List<ModuleDTO> moduleDTOS) {
        if (CollectionUtils.isEmpty(moduleDTOS)) {
            return null;
        }
        List<ModuleConfigDo> moduleConfigDos = Lists.newArrayListWithCapacity(moduleDTOS.size());
        for (ModuleDTO moduleDTO : moduleDTOS) {
            if (moduleDTO != null) {
                ModuleConfigDo moduleConfigDo = new ModuleConfigDo();
                moduleConfigDo.setKey(moduleDTO.getModuleKey());
                moduleConfigDo.setValue(moduleDTO.getModuleValue());
                moduleConfigDos.add(moduleConfigDo);
            }
        }
        return moduleConfigDos;
    }

    private List<ModuleAbConfigBo> transferModuleAbForDp(List<ModuleAbConfig> moduleAbConfigs) {
        if (CollectionUtils.isEmpty(moduleAbConfigs)) {
            return null;
        }
        List<ModuleAbConfigBo> result = Lists.newArrayListWithCapacity(moduleAbConfigs.size());
        for (ModuleAbConfig input : moduleAbConfigs) {
            ModuleAbConfigBo configDo = new ModuleAbConfigBo();
            configDo.setKey(input.getKey());
            if (CollectionUtils.isNotEmpty(input.getAbConfigs())) {
                List<AbConfigBo> abConfigDoList = Lists.transform(input.getAbConfigs(), new Function<AbConfig, AbConfigBo>() {
                    @Override
                    public AbConfigBo apply(@Nullable AbConfig one) {
                        AbConfigBo out = new AbConfigBo(one.getExpId(), one.getExpResult(), one.getExpBiInfo());
                        return out;
                    }
                });
                configDo.setConfigs(abConfigDoList);
            }
            result.add(configDo);
        }
        return result;
    }

    private ModuleConfigsModule buildModuleConfigsModule(DealCtx dealCtx) {
        ModuleConfigsModule moduleConfigsModule = null;
        if(dealCtx.isMt()) {
            MTTemplateKey mtTemplateKey = dealCtx.getMtTemplateKey();
            moduleConfigsModule = buildModuleConfigsModule(mtTemplateKey, dealCtx.getAttrs(), dealCtx.getDealGroupDTO(), dealCtx.getMrnVersion());
        } else {
            DealStyle dealStyle = dealCtx.getDealStyle();
            moduleConfigsModule = buildModuleConfigsModule(dealStyle, dealCtx.getAttrs(), dealCtx.getDealGroupDTO(), dealCtx.getMrnVersion());
        }
        setStyleGeneralInfo(dealCtx, moduleConfigsModule);
        if(AttributeUtils.isTort(dealCtx.getAttrs())) {
            moduleConfigsModule.setTort(true);
            moduleConfigsModule.setTortText(TORT_TEXT);
            moduleConfigsModule.setTortTitle(TORT_TITLE);
            moduleConfigsModule.setTortDesc(TORT_DESC);
        }
        return moduleConfigsModule;
    }

    private void setStyleGeneralInfo(DealCtx ctx, ModuleConfigsModule moduleConfigsModule) {
        // 团详折叠样式
        Map<String, List> foldStyleSourceCategories = Lion.getMap(LionConstants.APP_KEY,
                LionConstants.FOLD_STYLE_PAGESOURCE_CATEGORIES, List.class, Collections.emptyMap());
        List<Integer> categories = foldStyleSourceCategories.get(ctx.getRequestSource());
        if (ctx.isMt() && CollectionUtils.isNotEmpty(categories) && categories.contains(ctx.getCategoryId())) {
            com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig moduleAbConfig =
                    douHuBiz.getAbExpResultV2(ctx, ctx.getCategoryId() + "_FoldStyle_V2");
            if (moduleAbConfig == null || moduleAbConfig.getConfigs() == null) {
                // 命中对照组或未命中实验时走卡片样式
                setDefaultStyle(ctx, moduleConfigsModule);
                return;
            }
            com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig abConfig = moduleAbConfig.getConfigs().get(0);
            if (abConfig == null) {
                // 命中对照组或未命中实验时走卡片样式
                setDefaultStyle(ctx, moduleConfigsModule);
                return;
            }
            ctx.getModuleAbConfigs().add(moduleAbConfig);
            if (FOLD_STYLE_EXP_RESULT_LIST.contains(abConfig.getExpResult())) {
                moduleConfigsModule.setGeneralInfo(Cons.FOLD_STYLE + "_" + abConfig.getExpResult());
            }else {
                // d组实验（对照组），走卡片样式
                setDefaultStyle(ctx, moduleConfigsModule);
            }
        } else {
            setDefaultStyle(ctx, moduleConfigsModule);
        }
    }

    /**
     * 设置业务场景下的样式
     * @param ctx 上下文对象
     */
    public void setBusinessStyle(DealCtx ctx) {
        String pageSource = ctx.getRequestSource();
        // 1. 直播场景
        if (RequestSourceEnum.fromLive(pageSource)) {
            DealBaseReq dealBaseReq = ctx.getDealBaseReq();
            String styleFromLive = getBusinessStyleFromLive(dealBaseReq.getMliveAbTestArgs());
            ctx.setBusinessStyle(new BusinessStyle(styleFromLive));
        } else if (RequestSourceEnum.fromMiniVision(pageSource)) {
            // 2. 小视界场景
            EnvCtx envCtx = ctx.getEnvCtx();
            String styleFromMiniVision = getBusinessStyleFromMiniVision(envCtx.getUnionId(), pageSource, envCtx.isMt());
            ctx.setBusinessStyle(new BusinessStyle(styleFromMiniVision));
        } else if(RequestSourceEnum.fromMVideo(pageSource)) {
            // 3.直播浮层场景
            ctx.setBusinessStyle(new BusinessStyle(BusinessStyleEnum.MVIDEO.getStyleName()));
        }
    }

    /**
     * 直播场景下团详页面展示样式
     * @param mliveAbTestArgs AB实验参数，由直播团队实现，通过跳链透传
     *                        对照组 duizhaozu     不生效
     *                        空白组 AA_duizhaozu  不生效
     *                        实验组 shiyanzu1     生效
     * @return 页面样式
     */
    private String getBusinessStyleFromLive(String mliveAbTestArgs) {
        return Objects.equals(mliveAbTestArgs, "shiyanzu1") ? "mlive" : StringUtils.EMPTY;
    }

    private String getBusinessStyleFromMiniVision(String unionId, String pageSource, boolean isMt) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.ExtraStyleProcessor.getBusinessStyleFromMiniVision(java.lang.String,java.lang.String,boolean)");
        boolean newStyle = douHuService.getMiniVisionStyleExpResult(unionId, pageSource, isMt);
        return newStyle ? "mini_vision" : StringUtils.EMPTY;
    }

    /**
     *  命中对照组或未命中实验时，走卡片样式
     * @param ctx
     * @param moduleConfigsModule
     */
    private void setDefaultStyle(DealCtx ctx, ModuleConfigsModule moduleConfigsModule) {
        if (ctx.isMtLiveMinApp()) {
            // 私域直播小程序默认是card_style_v1，无款式沉浸页
            moduleConfigsModule.setGeneralInfo(Cons.CARD_STYLE);
        }
        if (ctx.isEnableCardStyle()) {
            if (!ctx.isBeautyNailMultiStyle()) {
                moduleConfigsModule.setGeneralInfo(Cons.CARD_STYLE);
            }
        }
        if (ctx.isEnableCardStyleV2()) {
            moduleConfigsModule.setGeneralInfo(Cons.CARD_STYLE_V2);
            // 基于团详2.0版本，实现直播场景和小视界场景进入团详页面的适配
            setBusinessStyle(ctx);
        }
    }

    private ModuleConfigsModule buildModuleConfigsModule(MTTemplateKey mtTemplateKey, List<AttributeDTO> attrs, DealGroupDTO dealGroupDTO, String mrnVersion) {
        ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
        if(mtTemplateKey != null) {
            moduleConfigsModule.setDzx(mtTemplateKey.isDzx());
            moduleConfigsModule.setDpOrder(mtTemplateKey.isDpOrder());
            moduleConfigsModule.setKey(mtTemplateKey.getKey());
            moduleConfigsModule.setExtraInfo(mtTemplateKey.getExtraInfo());
            moduleConfigsModule.setModuleConfigs(mtTemplateKey.getModuleConfigs());
            moduleConfigsModule.setModuleAbConfigs(transfer2ModuleAbConfigListForMt(mtTemplateKey.getModuleAbConfigs()));
        }
        if (DealAttrHelper.isWuyoutong(attrs, mrnVersion)) {
            moduleConfigsModule.setKey(PIPE_WUYOUTONG);
        }
        if (DealAttrHelper.isToHomeFixDeal(dealGroupDTO)) {
            moduleConfigsModule.setKey(LIFE_FIX_TODOOR);
        }
        return moduleConfigsModule;
    }

    private ModuleConfigsModule buildModuleConfigsModule(DealStyle dealStyle, List<AttributeDTO> attrs, DealGroupDTO dealGroupDTO, String mrnVersion) {
        ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
        if(dealStyle != null) {
            moduleConfigsModule.setKey(dealStyle.getModuleKey());
            moduleConfigsModule.setExtraInfo(dealStyle.getExtra());
            moduleConfigsModule.setModuleConfigs(dealStyle.getModuleConfigs());
            moduleConfigsModule.setModuleAbConfigs(transfer2ModuleAbConfigListForDp(dealStyle.getModuleAbConfigs()));
        }
        if (DealAttrHelper.isWuyoutong(attrs, mrnVersion)) {
            moduleConfigsModule.setKey(PIPE_WUYOUTONG);
        }
        if (DealAttrHelper.isToHomeFixDeal(dealGroupDTO)) {
            moduleConfigsModule.setKey(LIFE_FIX_TODOOR);
        }
        return moduleConfigsModule;
    }

    private List<DztgModuleAbConfig> transfer2ModuleAbConfigListForMt(List<ModuleAbConfigDo> abConfigDoList) {
        if(abConfigDoList == null) {
            return null;
        }
        List<DztgModuleAbConfig> result = new ArrayList<>();
        for(ModuleAbConfigDo moduleAbConfigDo : abConfigDoList) {
            if(moduleAbConfigDo == null) {
                continue;
            }
            DztgModuleAbConfig config = transfer(moduleAbConfigDo);
            result.add(config);
        }
        return result;
    }

    private List<DztgModuleAbConfig> transfer2ModuleAbConfigListForDp(List<ModuleAbConfigBo> abConfigDoList) {
        if(abConfigDoList == null) {
            return null;
        }
        List<DztgModuleAbConfig> result = new ArrayList<>();
        for(ModuleAbConfigBo moduleAbConfigDo : abConfigDoList) {
            if(moduleAbConfigDo == null) {
                continue;
            }
            DztgModuleAbConfig config = transfer(moduleAbConfigDo);
            result.add(config);
        }
        return result;
    }

    private DztgModuleAbConfig transfer(ModuleAbConfigDo moduleAbConfigDo) {
        if(moduleAbConfigDo == null) {
            return null;
        }
        DztgModuleAbConfig result = new DztgModuleAbConfig();
        result.setKey(moduleAbConfigDo.getKey());
        result.setConfigs(transferAbConfigDo(moduleAbConfigDo.getConfigs()));

        return result;
    }

    private DztgModuleAbConfig transfer(ModuleAbConfigBo moduleAbConfigDo) {
        if(moduleAbConfigDo == null) {
            return null;
        }
        DztgModuleAbConfig result = new DztgModuleAbConfig();
        result.setKey(moduleAbConfigDo.getKey());
        result.setConfigs(transferAbConfigBo(moduleAbConfigDo.getConfigs()));

        return result;
    }

    private List<DztgAbConfig> transferAbConfigDo(List<AbConfigDo> abConfigDoList) {
        if(abConfigDoList == null) {
            return null;
        }
        List<DztgAbConfig> result = new ArrayList<>();
        for(AbConfigDo abConfigDo : abConfigDoList) {
            if(abConfigDo == null) {
                continue;
            }
            DztgAbConfig abConfig = new DztgAbConfig();
            abConfig.setExpId(abConfigDo.getExpId());
            abConfig.setExpBiInfo(abConfigDo.getExpBiInfo());
            abConfig.setExpResult(abConfigDo.getExpResult());
            result.add(abConfig);
        }
        return result;
    }

    private List<DztgAbConfig> transferAbConfigBo(List<AbConfigBo> abConfigDoList) {
        if(abConfigDoList == null) {
            return null;
        }
        List<DztgAbConfig> result = new ArrayList<>();
        for(AbConfigBo abConfigDo : abConfigDoList) {
            if(abConfigDo == null) {
                continue;
            }
            DztgAbConfig abConfig = new DztgAbConfig();
            abConfig.setExpId(abConfigDo.getExpId());
            abConfig.setExpBiInfo(abConfigDo.getExpBiInfo());
            abConfig.setExpResult(abConfigDo.getExpResult());
            result.add(abConfig);
        }
        return result;
    }

    /**
     * 是否有"巨省钱"头图样式
     * @param moduleConfigsModule
     * @return
     */
    private boolean isJuShengQianBigPhoto(ModuleConfigsModule moduleConfigsModule) {
        if (moduleConfigsModule == null || CollectionUtils.isEmpty(moduleConfigsModule.getModuleConfigs())) {
            return false;
        }
        return moduleConfigsModule.getModuleConfigs().stream()
                .anyMatch(moduleConfigDo ->
                        "GCPlatformModules/picasso_deal_detail_head_module".equals(moduleConfigDo.getKey())
                        && StringUtils.isNotEmpty(moduleConfigDo.getValue()) && moduleConfigDo.getValue().endsWith("jushengqian"));
    }

    private void buildComparePriceTabStyleModuleConfig(DealCtx ctx, ModuleConfigsModule moduleConfigsModule) {
        if (ctx.isEnableCardStyleV2()){
            ExpResultConfig expResultConfig = LionConfigUtils.getExpResultConfig();
            com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig moduleAbConfig = douHuService.getCompareSameShopPriceStyleAbConfigByDealCtx(ctx);
            String expResult = douHuService.getExpResult(moduleAbConfig);
            if ("c".equals(expResult) || "e".equals(expResult)){
                String tabStyle = douHuService.getTabStyle(expResultConfig, ctx, moduleAbConfig);
                buildModuleConfigDos(tabStyle, moduleConfigsModule);
            }
        }
    }
    public void buildModuleConfigDos(String tabStyle, ModuleConfigsModule moduleConfigsModule){
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.ExtraStyleProcessor.buildModuleConfigDos(java.lang.String,com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule)");
        if (StringUtils.isNotBlank(tabStyle)){
            List<ModuleConfigDo> moduleConfigDos = getModuleConfig(moduleConfigsModule);
            moduleConfigDos.add(buildModuleConfigDo(ModuleConfigsEnum.TAB_STYLE.getCode(), tabStyle));
        }
    }
    public List<ModuleConfigDo> getModuleConfig(ModuleConfigsModule moduleConfigsModule){
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.ExtraStyleProcessor.getModuleConfig(com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule)");
        List<ModuleConfigDo> moduleConfigs = moduleConfigsModule.getModuleConfigs();
        if (CollectionUtils.isEmpty(moduleConfigs)){
            moduleConfigs = new ArrayList<>();
            moduleConfigsModule.setModuleConfigs(moduleConfigs);
        }
        return moduleConfigs;
    }
    public ModuleConfigDo buildModuleConfigDo(String key, String value){
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.ExtraStyleProcessor.buildModuleConfigDo(java.lang.String,java.lang.String)");
        ModuleConfigDo moduleConfigDo = new ModuleConfigDo();
        moduleConfigDo.setKey(key);
        moduleConfigDo.setValue(value);
        return moduleConfigDo;
    }



}
