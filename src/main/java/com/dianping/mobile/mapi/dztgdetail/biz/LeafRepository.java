package com.dianping.mobile.mapi.dztgdetail.biz;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.inf.leaf.thrift.IDGen;
import com.sankuai.inf.leaf.thrift.Result;
import com.sankuai.inf.leaf.thrift.Status;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
@Slf4j
public class LeafRepository {

    @Autowired
    private IDGen.Iface idGen;

    private static final int INVOKE_RETRY_TIME = 3;

    private static final int MAX_LEAF_BATCH_NUM = 100;

    private static final String LEAF_EVENT = "leafEvent";

    /**
     * leafKey  stockId
     * 库存id
     */
    private static final String SERIANO_LEAF_KEY = "nibmkt.couponcomponent.financialconsumeserialno";

    public List<Long> batchGenFinancialConsumeSerialId(int batchNum) {
        if(batchNum > MAX_LEAF_BATCH_NUM) {
            Cat.logEvent(LEAF_EVENT, "reachMaxBatchSize100");
            return null;
        }
        int retryTime = 0;
        while (++retryTime <= INVOKE_RETRY_TIME) {
            List<Long> leafIdList = Lists.newArrayListWithExpectedSize(batchNum);
            try {
                List<Result> batchResult = idGen.getSnowFlakeBatch(SERIANO_LEAF_KEY, batchNum);
                if (CollectionUtils.isNotEmpty(batchResult)) {
                    for (Result result : batchResult) {
                        if (Status.SUCCESS.equals(result.getStatus())) {
                            leafIdList.add(result.getId());
                        } else {
                            Cat.logEvent(LEAF_EVENT, "batchGenLeafIdFail");
                            throw new RuntimeException("leaf id生成异常, 异常码:" + result.getStatus());
                        }
                    }
                    if (leafIdList.size() == batchNum) {
                        return leafIdList;
                    }
                }
            } catch (Exception e) {
                log.error("batchGenFinancialConsumeSerialId exception, num:{}", batchNum, e);
                if (e.getMessage() != null && e.getMessage().contains("timeout")) {
                    Cat.logEvent(LEAF_EVENT, "batchTimeoutException");
                    continue;
                }
                //非超时异常，或因超时重试达到最大重试次数
                if ((e.getMessage() != null && !e.getMessage().contains("timeout")) || retryTime >= INVOKE_RETRY_TIME) {
                    Cat.logEvent(LEAF_EVENT, "batchMeetException");
                    return null;
                }
            }
        }
        return null;
    }

    public Long nextFinancialConsumeSerialId(){
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.LeafRepository.nextFinancialConsumeSerialId()");
        int retryTime = 0;
        while (++retryTime <= INVOKE_RETRY_TIME) {
            try {
                Result result = idGen.getSnowFlake(SERIANO_LEAF_KEY);
                if (Status.SUCCESS.equals(result.getStatus())) {
                    return result.getId();
                } else {
                    Cat.logEvent(LEAF_EVENT, "genLeafIdFail");
                    throw new RuntimeException("leaf id生成异常, 异常码:" + result.getId());
                }
            } catch (Exception e) {
                log.error("nextFinancialConsumeSerialId exception", e);
                if (e.getMessage() != null && e.getMessage().contains("timeout")) {
                    Cat.logEvent(LEAF_EVENT, "timeoutException");
                    continue;
                }
                //非超时异常，或因超时重试达到最大重试次数
                if ((e.getMessage() != null && !e.getMessage().contains("timeout")) || retryTime >= INVOKE_RETRY_TIME) {
                    Cat.logEvent(LEAF_EVENT, "meetException");
                    return null;
                }
            }
        }
        return null;
    }
}