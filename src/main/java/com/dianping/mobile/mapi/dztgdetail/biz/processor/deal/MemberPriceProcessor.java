package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MemberPriceWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.google.common.collect.Sets;
import com.sankuai.mpmctmember.process.common.enums.MemberChargeTypeEnum;
import com.sankuai.mpmctmember.process.common.enums.MemberDiscountTypeEnum;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.MemberBaseInfoDTO;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.MemberDiscountInfoDTO;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.resp.GetGrouponMemberDiscountInfoRespDTO;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/12/13
 */
@Slf4j
public class MemberPriceProcessor extends AbsDealProcessor {
    @Resource
    private MemberPriceWrapper memberPriceWrapper;

    private static final Set<Integer> MEMBER_TYPE_SET = Sets.newHashSet(
            MemberDiscountTypeEnum.MEMBER_PROMO.getType(),
            MemberDiscountTypeEnum.NEW_MEMBER_PROMO.getType()
    );

    @Override
    public boolean isEnable(DealCtx ctx) {
        return memberPriceWrapper.isMemberPriceProcessorEnable(ctx);
    }

    @Override
    public void prepare(DealCtx ctx) {
        try {
            //因为后续流程需要用到会员信息，所以放在准备流程里面
            //2024.07.06 rcf优化，将会员信息查询改为异步查询,报价的prepare改为全量发起请求，在process中根据queryGrouponMemberDiscount的结果确认是否需要拿会员价的future结果
            Future<GetGrouponMemberDiscountInfoRespDTO> getGrouponMemberDiscountInfoRespDTOFuture = memberPriceWrapper.queryGrouponMemberDiscount(ctx);
            if (getGrouponMemberDiscountInfoRespDTOFuture == null) {
                return;
            }
            ctx.getFutureCtx().setMemberDiscountInfoRespDTOFuture(getGrouponMemberDiscountInfoRespDTOFuture);
        } catch (Exception e) {
            log.error("##MemberPriceProcessor getMemberDiscountInfoRespDTOFuturePrepare error", e);
        }
    }

    @Override
    public void process(DealCtx ctx) {
        try {
            GetGrouponMemberDiscountInfoRespDTO respDTO = ctx.getFutureCtx().getMemberDiscountInfoRespDTOFuture().get(1, TimeUnit.SECONDS);

            if (respDTO == null || respDTO.getMemberDiscountInfo() == null) {
                return;
            }

            MemberDiscountInfoDTO memberDiscountInfoDTO = respDTO.getMemberDiscountInfo();
            if (memberDiscountInfoDTO.getMemberBaseInfo() == null) {
                return;
            }

            //如果商品没有会员价/新会员价，直接返回
            if (!MEMBER_TYPE_SET.contains(memberDiscountInfoDTO.getMemberDiscountType())) {
                return;
            }
            // 用户未加入商家付费会员，直接返回
            if (notJoinMemberChargeCard(memberDiscountInfoDTO)) {
                return;
            }

            //如果是会员价商品，不需要判断用户身份。将优惠信息设置到上下文
            if (MemberDiscountTypeEnum.MEMBER_PROMO.getType().equals(memberDiscountInfoDTO.getMemberDiscountType())) {
                ctx.setShopMemberDiscountInfoDTO(memberDiscountInfoDTO);
                return;
            }

            //如果是新会员商品，需要判断用户身份，非会员和新会员可以领取。将优惠信息设置到上下文
            boolean isMember = Boolean.TRUE.equals(memberDiscountInfoDTO.getMemberBaseInfo().getMember());
            boolean isNewMember = Boolean.TRUE.equals(memberDiscountInfoDTO.getMemberBaseInfo().getNewMember());
            if(!isMember || isNewMember) {
                ctx.setShopMemberDiscountInfoDTO(memberDiscountInfoDTO);
            }
        } catch (Exception e) {
            Cat.logError(e);
            log.error("##MemberPriceProcessor getMemberDiscountInfoRespDTOFutureProcess error", e);
        }
    }

    public boolean notJoinMemberChargeCard(MemberDiscountInfoDTO memberDiscountInfoDTO) {
        if (Objects.isNull(memberDiscountInfoDTO)) {
            return true;
        }
        MemberBaseInfoDTO memberBaseInfo = memberDiscountInfoDTO.getMemberBaseInfo();
        if (Objects.isNull(memberBaseInfo)) {
            return true;
        }
        return Objects.equals(memberDiscountInfoDTO.getChargeType(), MemberChargeTypeEnum.CHARGE.getCode())
                && !memberBaseInfo.getMember();
    }

}
