package com.dianping.mobile.mapi.dztgdetail.datatype.resp.other;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Date: 2023/11/22
 */
@Data
public class DigestInfoDTO {
    /**
     * 视频宽
     */
    private int videoWidth;

    /**
     * 视频url
     */
    private String videoUrl;

    /**
     * 点评团单id
     */
    private int dpDealId;

    /**
     * 视频封面图
     */
    private String videoFrameUrl;

    /**
     * 视频大小
     */
    private int videoSize;

    /**
     * 视频高
     */
    private int videoHeight;

    /*
    * 材料脱落免费重补时长类型
    * */
    private Integer materialShedFreeRefillDurationType;

    /*
    * 材料脱落免费重补时长
    * */
    private Integer materialShedFreeRefillDuration;

    /*
    * 术后免费复查保障时长类型
    * */
    private Integer postoperativeFreeReviewServiceDurationType;

    /*
     * 术后免费复查保障时长
     * */
    private Integer postoperativeFreeReviewServiceDuration;

    /*
     * 术后免费复查保障次数类型
     * */
    private Integer postoperativeFreeReviewServiceCountType;

    /*
     * 术后免费复查保障次数
     * */
    private Integer postoperativeFreeReviewServiceCount;

    /*
     * 植体脱落免费复种保障方式类型
     * */
    private Integer shedFreeReplantGuaranteeType;

    /*
     * 植体脱落免费复种保障时长类型
     * */
    private Integer shedFreeReplantGuaranteeDurationType;

    /*
     * 植体脱落免费复种保障时长
     * */
    private Integer shedFreeReplantGuaranteeDuration;

    /*
     * 种植体质保保障时长类型
     * */
    private Integer implantQualityGuaranteeDurationType;

    /*
     * 种植体质保保障时长
     * */
    private Integer implantQualityGuaranteeDuration;

    public Map<String,Integer> toSafeDentureMap() {
        Map<String,Integer> result = new HashMap<>();
        result.put("materialShedFreeRefillDurationType", this.materialShedFreeRefillDurationType);
        result.put("materialShedFreeRefillDuration", this.materialShedFreeRefillDuration);
        result.put("postoperativeFreeReviewServiceDurationType", this.postoperativeFreeReviewServiceDurationType);
        result.put("postoperativeFreeReviewServiceDuration", this.postoperativeFreeReviewServiceDuration);
        result.put("postoperativeFreeReviewServiceCountType", this.postoperativeFreeReviewServiceCountType);
        result.put("postoperativeFreeReviewServiceCount", this.postoperativeFreeReviewServiceCount);
        result.put("shedFreeReplantGuaranteeDurationType", this.shedFreeReplantGuaranteeDurationType);
        result.put("shedFreeReplantGuaranteeDuration", this.shedFreeReplantGuaranteeDuration);
        result.put("implantQualityGuaranteeDurationType", this.implantQualityGuaranteeDurationType);
        result.put("implantQualityGuaranteeDuration", this.implantQualityGuaranteeDuration);
        return result;
    }


}
