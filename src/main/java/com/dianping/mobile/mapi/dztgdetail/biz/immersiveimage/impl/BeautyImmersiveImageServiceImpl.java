package com.dianping.mobile.mapi.dztgdetail.biz.immersiveimage.impl;

import com.dianping.cat.Cat;
import com.dianping.martgeneral.recommend.api.enums.PlatformEnum;
import com.dianping.mobile.mapi.dztgdetail.biz.immersiveimage.ImmersiveImageService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ImmersiveImageWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiClientWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.RecommendServiceWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ShopTagWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.CatEvents;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ExhibitImageSceneEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ShopCategoryEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageFilterRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImageTagVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageFilterVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageVO;
import com.dianping.mobile.mapi.dztgdetail.entity.QueryExhibitImageParam;
import com.dianping.mobile.mapi.dztgdetail.entity.QueryRecommendParam;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.general.product.query.center.client.builder.model.*;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import com.sankuai.general.product.query.center.client.dto.PriceDTO;
import com.sankuai.general.product.query.center.client.enums.AttrSubjectEnum;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.QueryCenterProcessor.getQueryCenterDealGroupAttrKey;

/**
 * <AUTHOR>
 * @date 2023-08-28
 * @desc 美甲/美睫行业沉浸页逻辑处理
 */
@Component
public class BeautyImmersiveImageServiceImpl implements ImmersiveImageService {

    @Resource
    private ImmersiveImageWrapper immersiveImageWrapper;

    @Resource
    private RecommendServiceWrapper recommendServiceWrapper;

    @Resource
    private DouHuBiz douHuBiz;

    @Resource
    private QueryCenterWrapper queryCenterWrapper;

    @Resource
    private ShopTagWrapper shopTagWrapper;
    
    @Resource
    private PoiClientWrapper poiClientWrapper;
    /**
     * 美甲/美睫团单二级类目ID
     */
    private static final List<Integer> BEAUTY_NAIL_CATEGORY_IDS = Lists.newArrayList(502);

    /**
     * 美甲团单标签
     * 纯色美甲标签：100075023L
     * 款式美甲标签：100075024L
     */
    private static final Set<Long> BEAUTY_NAIL_TAG_IDS = Sets.newHashSet(100075024L, 100075023L);

    @Override
    public List<Integer> getCategoryIds() {
        return BEAUTY_NAIL_CATEGORY_IDS;
    }

    @Override
    public ImmersiveImageVO getImmersiveImage(GetImmersiveImageRequest request, EnvCtx envCtx) {
        // 如果是对照组，则直接返回空数据
        ModuleAbConfig moduleAbConfig = null;
        if (Objects.equals(request.getSceneCode(), ExhibitImageSceneEnum.RECOMMEND.getSceneCode())) {
            moduleAbConfig = getAbTestResultForRecommend(envCtx);
        } else if (ExhibitImageSceneEnum.fromOrderDetailPage(request.getSceneCode())) {
            moduleAbConfig = getAbTestResultForOrder(envCtx);
        } else if (Objects.equals(request.getSceneCode(), ExhibitImageSceneEnum.SELF.getSceneCode())) {
            moduleAbConfig = getAbTestResultForSelf(envCtx);
        }
        if (Objects.isNull(moduleAbConfig)) {
            return null;
        }
        String expResult = "";
        if (CollectionUtils.isNotEmpty(moduleAbConfig.getConfigs())) {
            expResult = moduleAbConfig.getConfigs().get(0).getExpResult();
        }
        // b组为对照组，不展示款式数据
        if (!Objects.equals(request.getSceneCode(), ExhibitImageSceneEnum.SELF.getSceneCode())
                && Objects.equals(expResult, "b")) {
            ImmersiveImageVO immersiveImageVO = new ImmersiveImageVO();
            immersiveImageVO.setModuleAbConfigs(Collections.singletonList(moduleAbConfig));
            return immersiveImageVO;
        }

        QueryExhibitImageParam param = QueryExhibitImageParam.builder()
                .categoryId(request.getCategoryId())
                .dpDealGroupId(request.getDealGroupId())
                .start(request.getStart())
                .limit(request.getLimit())
                .shopId(request.getShopId())
                .clientType(envCtx.getClientType())
                .build();
        // 查询团单信息
        DealGroupDTO dealGroupDTO = getDealGroupDTO(param.getDpDealGroupId().longValue());
        if (ExhibitImageSceneEnum.fromRecommendScene(request.getSceneCode())) {
            QueryRecommendParam recommendParam = build(dealGroupDTO, param, envCtx);
            ImmersiveImageVO immersiveImageVO = recommendServiceWrapper.getRecommendStyleImage(recommendParam);
            if (Objects.nonNull(immersiveImageVO)) {
                immersiveImageVO.setModuleAbConfigs(Collections.singletonList(moduleAbConfig));
            }
            return immersiveImageVO;
        }
        // 获取团单三级类目
        String serviceType = "";
        Long serviceTypeId = 0L;
        if (Objects.nonNull(dealGroupDTO) && Objects.nonNull(dealGroupDTO.getCategory())) {
            serviceType = dealGroupDTO.getCategory().getServiceType();
            serviceTypeId = dealGroupDTO.getCategory().getServiceTypeId();
        }
        // 入参需要serviceType，构造serviceType
        param.setServiceType(serviceType);
        param.setServiceTypeId(serviceTypeId);
        ImmersiveImageVO immersiveImageVO = getImmersiveImageData(envCtx, request, param, dealGroupDTO);

        Map<String, String> metricTags = Maps.newHashMap();
        metricTags.put("categoryId", "502");
        metricTags.put("serviceType", serviceType);
        metricTags.put("scene", request.getSceneCode());
        metricTags.put("hasData", String.valueOf(Objects.nonNull(immersiveImageVO)));
        if (Objects.nonNull(immersiveImageVO)) {
            immersiveImageVO.setModuleAbConfigs(Collections.singletonList(moduleAbConfig));
            // 新穿戴甲不能到店佩戴，则去除常驻标签
            if (DealUtils.isNewWearableNailDeal(dealGroupDTO)) {
                String isFreeWearingAtStore = getFirstAttrValFromAttr(dealGroupDTO, "isFreeWearingAtStore");
                if (StringUtils.isNotBlank(isFreeWearingAtStore) && Objects.equals(isFreeWearingAtStore, "false")) {
                    immersiveImageVO.getItems().forEach(itemVO -> {
                        List<ImageTagVO> tags = itemVO.getTags().stream().filter(tag -> tag.getStyle() == 0)
                                .collect(Collectors.toList());
                        itemVO.setTags(tags);
                    });
                }
                // 穿戴甲设置门店类型，用于埋点
                Long dpShopId = request.getShopId();
                if (envCtx.isMt()) {
                    MtPoiDTO mtPoiDTO = poiClientWrapper.getMtPoiDTO(param.getShopId(),
                            Lists.newArrayList("dpPoiId", "mtPoiId"));
                    dpShopId = Objects.nonNull(mtPoiDTO) ? mtPoiDTO.getDpPoiId() : 0L;
                }
                ShopCategoryEnum shopCategoryEnum = shopTagWrapper.getWearableNailShopTag(dpShopId);
                immersiveImageVO.setShopCategoryId(shopCategoryEnum.getCode());
            }
            metricTags.put("shopCategoryId", String.valueOf(immersiveImageVO.getShopCategoryId()));
        }
        Cat.logMetricForCount(CatEvents.IMMERSIVE_IMAGE_COUNT, metricTags);
        return immersiveImageVO;
    }

    private ImmersiveImageVO getImmersiveImageData(EnvCtx envCtx, GetImmersiveImageRequest request, QueryExhibitImageParam param,
                                                   DealGroupDTO dealGroupDTO) {
        String exhibitItemId = GsonUtils.getParamFromMapJson(request.getExtParam(), "exhibitItemId", String.class);
        // 三级团购类目为美甲且入口为营销入口，则获取排序后款式结果
        if (isBeautyNailCubeExhibitEntry(request, dealGroupDTO, exhibitItemId)) {
            return immersiveImageWrapper.getImmersiveImageWithTopItemId(dealGroupDTO, exhibitItemId,
                    param.getCategoryId(), envCtx);
        }
        // 如果是新穿戴甲团单
        if (DealUtils.isNewWearableNailDeal(dealGroupDTO)) {
            Long dealGroupId = envCtx.isMt() ? request.getMtDealGroupId() : request.getDpDealGroupId();
            param.setDealGroupStatus(getDealGroupStatus(dealGroupDTO));
            return immersiveImageWrapper.getImmersiveImageWithDeals(dealGroupDTO.getDeals(),
                    param, envCtx, dealGroupId, request.getShopId());
        }
        return immersiveImageWrapper.getImmersiveImage(param, envCtx);
    }

    private boolean isBeautyNailCubeExhibitEntry(GetImmersiveImageRequest request, DealGroupDTO dealGroupDTO, String exhibitItemId) {
        // 设置开关，待下游接口功能调通后打开开关
        return DealUtils.isBeautyNailServiceTypeDeal(dealGroupDTO) && isCubeExhibitNail(request)
                && StringUtils.isNotBlank(exhibitItemId) && LionConfigUtils.enableExhibitSourceBeautyNailImmersiveImage();
    }

    private boolean isCubeExhibitNail(GetImmersiveImageRequest request) {
        return RequestSourceEnum.CUBE_EXHIBIT_NAIL.getSource().equals(request.getPageSource());
    }

    private Integer getDealGroupStatus(DealGroupDTO dealGroupDTO) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.immersiveimage.impl.BeautyImmersiveImageServiceImpl.getDealGroupStatus(com.sankuai.general.product.query.center.client.dto.DealGroupDTO)");
        return Optional.ofNullable(dealGroupDTO)
                .map(DealGroupDTO::getBasic)
                .map(DealGroupBasicDTO::getStatus)
                .orElse(-1);
    }


    @Override
    public ImmersiveImageFilterVO getImmersiveImageFilter(GetImmersiveImageFilterRequest request) {
        return null;
    }

    private QueryRecommendParam build(DealGroupDTO dealGroupDTO, QueryExhibitImageParam exhibitImageParam,
                                      EnvCtx envCtx) {
        QueryRecommendParam.QueryRecommendParamBuilder builder = QueryRecommendParam.builder();
        if (Objects.isNull(exhibitImageParam)) {
            return builder.build();
        }
        String finalPrice = "";
        String dealGroupTagIds = "";
        if (Objects.nonNull(dealGroupDTO)) {
            finalPrice = getDealGroupPriceForBeautyNail(dealGroupDTO.getPrice());
            dealGroupTagIds = getDealGroupTagIdsForBeautyNail(dealGroupDTO.getTags());
        }

        return builder.cityId(exhibitImageParam.getCityId())
                .dpId(envCtx.getDpId())
                .isMt(envCtx.isMt())
                .uuid(envCtx.getUuid())
                .platformEnum(envCtx.isMt() ? PlatformEnum.MT : PlatformEnum.DP)
                .originUserId(String.valueOf(envCtx.isMt() ? envCtx.getMtUserId() : envCtx.getDpUserId()))//已确认判断平台后再使用
                .start(exhibitImageParam.getStart())
                .limit(exhibitImageParam.getLimit())
                .sortType("AI")
                .shopRatingThreshold("0")
                .shopId(exhibitImageParam.getShopId())
                .clientType(envCtx.getClientType())
                .categoryId(exhibitImageParam.getCategoryId())
                .dealGroupPrice(finalPrice)
                .dealGroupTagIds(dealGroupTagIds)
                .flowFlag("002")
                .dpDealGroupId(exhibitImageParam.getDpDealGroupId().longValue())
                .build();
    }

    private DealGroupDTO getDealGroupDTO(Long dpDealGroupId) {
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet(dpDealGroupId), IdTypeEnum.DP)
                .dealGroupId(DealGroupIdBuilder.builder().mtDealGroupId())
                .basicInfo(DealGroupBasicInfoBuilder.builder().status())
                .dealGroupTag(DealGroupTagBuilder.builder().all())
                .dealGroupPrice(DealGroupPriceBuilder.builder().all())
                .category(DealGroupCategoryBuilder.builder().all())
                .attrsByKey(AttrSubjectEnum.DEAL_GROUP, getQueryCenterDealGroupAttrKey())
                .dealBasicInfo(DealBasicInfoBuilder.builder().all())
                .build();
        try {
            Future dealGroupFuture = queryCenterWrapper.preDealGroupDTO(queryByDealGroupIdRequest);
            DealGroupDTO dealGroupDTO = queryCenterWrapper.getDealGroupDTO(dealGroupFuture);
            if (Objects.isNull(dealGroupDTO)) {
                return null;
            }
            return dealGroupDTO;
        } catch(Exception e) {
            Cat.logError("[BeautyImmersiveImageServiceImpl] queryCenterWrapper.preDealGroupDTO err {}", e);
        }
        return null;
    }


    private String getDealGroupTagIdsForBeautyNail(List<DealGroupTagDTO> dealGroupTagDTOS) {
        if (CollectionUtils.isEmpty(dealGroupTagDTOS)) {
            return StringUtils.EMPTY;
        }
        Set<Long> beautyNailIds = dealGroupTagDTOS.stream()
                .filter(tag -> BEAUTY_NAIL_TAG_IDS.contains(tag.getId()))
                .map(DealGroupTagDTO::getId)
                .collect(Collectors.toSet());
        return CollectionUtils.isEmpty(beautyNailIds) ? StringUtils.EMPTY : Joiner.on(",").join(beautyNailIds);
    }

    private String getDealGroupPriceForBeautyNail(PriceDTO priceDTO) {
        if (Objects.isNull(priceDTO)) {
            return StringUtils.EMPTY;
        }
        return priceDTO.getSalePrice();
    }

    private ModuleAbConfig getAbTestResultForRecommend(EnvCtx ctx) {
        // 款式推荐接口AB模块
        String module = ctx.isMt() ? "MtNailStyleRecommend" : "DpNailStyleRecommend";
        return douHuBiz.getAbByUnionId(ctx.getUnionId(), module, ctx.isMt());
    }

    private ModuleAbConfig getAbTestResultForOrder(EnvCtx ctx) {
        // 订详页款式展示AB实验上报
        String module = ctx.isMt() ? "MtNailOrder" : "DpNailOrder";
        return douHuBiz.getAbByUnionId(ctx.getUnionId(), module, ctx.isMt());
    }

    private ModuleAbConfig getAbTestResultForSelf(EnvCtx ctx) {
        // 本团购款式AB模块
        return getAbTestResultForRecommend(ctx);
    }

    public String getFirstAttrValFromAttr(DealGroupDTO dealGroup, String attrName) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.immersiveimage.impl.BeautyImmersiveImageServiceImpl.getFirstAttrValFromAttr(com.sankuai.general.product.query.center.client.dto.DealGroupDTO,java.lang.String)");
        List<String> attrValFromAttr = getAttrValFromAttr(dealGroup, attrName);
        return CollectionUtils.isEmpty(attrValFromAttr) ? null : attrValFromAttr.get(0);
    }

    private List<String> getAttrValFromAttr(DealGroupDTO dealGroup, String attrName) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.immersiveimage.impl.BeautyImmersiveImageServiceImpl.getAttrValFromAttr(com.sankuai.general.product.query.center.client.dto.DealGroupDTO,java.lang.String)");
        if (Objects.isNull(dealGroup) || StringUtils.isBlank(attrName)) {
            return Lists.newArrayList();
        }
        List<AttrDTO> attrs = dealGroup.getAttrs();
        if (CollectionUtils.isEmpty(attrs)) {
            return Lists.newArrayList();
        }
        Optional<AttrDTO> first = attrs.stream().filter(k -> Objects.equals(k.getName(), attrName)).findFirst();
        if (first.isPresent()) {
            return first.get().getValue();
        }
        return Lists.newArrayList();
    }
}
