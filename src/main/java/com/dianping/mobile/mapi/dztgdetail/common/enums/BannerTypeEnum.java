package com.dianping.mobile.mapi.dztgdetail.common.enums;

import lombok.Getter;

@Getter
public enum BannerTypeEnum {
    Default(0, "默认"),
    BeautyCouponBag(1, "丽人券包"),
    WarmUp(2, "预热"),
    MemberExclusive(3, "会员专属"),
    COUPON_INFO(4, "券后信息透传"),
    BIANMEI(5,"变美神券"),
    MINI_PROGRAM_LIVE(6, "私域直播"),
    COMMON_TYPE(7, "通用类型"),
    ;

    private int type;
    private String name;

    BannerTypeEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }
}
