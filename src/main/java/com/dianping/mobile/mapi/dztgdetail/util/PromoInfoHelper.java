package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.cat.Cat;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.lion.common.util.JsonUtils;
import com.dianping.mobile.mapi.dztgdetail.common.constants.PlusIcons;
import com.dianping.mobile.mapi.dztgdetail.common.enums.LeadActionEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.PromoTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.PriceContext;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo.CouponDetailItem;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo.ExposurePromoInfo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo.PromoAggInfo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo.PromoInfoDescItem;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo.PromoInfoItem;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.PromoDetailModuleBuilderService;
import com.dianping.pay.promo.common.bean.PromoTime;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PricePowerTagDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PricePowerTagItem;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import com.sankuai.nibmktproxy.queryclient.proxy.CouponDTO;
import com.sankuai.nibmktproxy.queryclient.proxy.GetPromotionDTO;
import com.sankuai.nibmktproxy.queryclient.proxy.PromotionDTOResult;
import com.sankuai.nibmktproxy.queryclient.proxy.PromotionType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class PromoInfoHelper {

    /**
     * 政府消费券券包密钥
     */
    private static final String PACKAGE_SECRET_KEY = "packageSecretKey";

    /**
     * 对应竞争圈价格排名前20%的价格的key
     */
    private static final String TOP_TWENTY_PERCENT_PRICE = "topTwentyPercentPrice";
    /**
     * 对应竞争圈价格排名前50%的价格的key
     */
    private static final String TOP_FIFTY_PERCENT_PRICE = "topFiftyPercentPrice";


    public static ExposurePromoInfo buildExposurePromoInfo(DealCtx ctx) {
        DealGroupBaseDTO dealGroupBase = ctx.getDealGroupBase();
        List<PromoDisplayDTO> promoWithTimeList = ctx.getPromoWithTimeList();

        if (CollectionUtils.isEmpty(promoWithTimeList)) {
            return null;
        }

        PromoDisplayDTO dto = promoWithTimeList.get(0);
        if (!com.dianping.mobile.mapi.dztgdetail.helper.PromoHelper.isValid(dto)) {
            return null;
        }

        PromoTime canConsumeTime = dto.getCanConsumeTime();
        if (canConsumeTime == null || CollectionUtils.isEmpty(canConsumeTime.getTimes()) || !canConsumeTime.getTimes().contains("4")) {
            return null;
        }

        BigDecimal price = dealGroupBase.getDealGroupPrice().subtract(dto.getPromoAmount());
        if (price.compareTo(BigDecimal.ZERO) < 0) {
            return null;
        }
        BigDecimal discount = price.multiply(BigDecimal.TEN).divide(dealGroupBase.getDealGroupPrice(), 1, RoundingMode.HALF_UP);
        price = price.setScale(2, BigDecimal.ROUND_CEILING).stripTrailingZeros();

        ExposurePromoInfo info = new ExposurePromoInfo();
        info.setPromoDetail(getPromoDetail(price, discount));
        info.setLabelBgStartColor("#FF9459");
        info.setLabelBgEndColor("#FF5C38");

        String labelText = discount.compareTo(BigDecimal.valueOf(5)) == 0 ? "周四半价" : ("周四" + discount + "折");
        info.setLabelText(labelText);

        return info;
    }

    private static String getPromoDetail(BigDecimal price, BigDecimal discount) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.util.PromoInfoHelper.getPromoDetail(java.math.BigDecimal,java.math.BigDecimal)");
        LocalDate now = LocalDate.now();
        int dayOfWeek = now.getDayOfWeek().getValue();
        int interval = DayOfWeek.THURSDAY.getValue() - dayOfWeek;
        LocalDate nextThu = interval > 0 ? now.plusDays(interval) : now.plusDays(interval + 7);

        String discountDesc = discount.compareTo(BigDecimal.valueOf(5)) == 0 ? "半价" : (discount + "折");

        return nextThu.getMonthValue() + "月" + nextThu.getDayOfMonth() + "日" + discountDesc + "￥" + price.stripTrailingZeros().toPlainString();
    }

    private static PromotionDTOResult extractPromoResult(DealCtx ctx) {
        int dealGroupId = ctx.isMt() ? ctx.getMtId() : ctx.getDpId();

        if (MapUtils.isEmpty(ctx.getPromotionMap())) {
            return null;
        }

        PromotionDTOResult promoResult = ctx.getPromotionMap().get(String.valueOf(dealGroupId));
        if (promoResult == null || CollectionUtils.isEmpty(promoResult.getGetPromotionDTO())) {
            return null;
        }

        return ctx.getPromotionMap().get(String.valueOf(dealGroupId));
    }


    public static PromoAggInfo buildPromoAggInfo(DealCtx ctx) {
        PromotionDTOResult promoResult = extractPromoResult(ctx);
        if (promoResult == null) {
            return null;
        }

        List<CouponDetailItem> merchantCoupons = new ArrayList<>();
        List<CouponDetailItem> platformCoupons = new ArrayList<>();
        List<GetPromotionDTO> promos = promoResult.getGetPromotionDTO();

        for (GetPromotionDTO promo : promos) {
            if (!isCouponPromo(promo)) {
                continue;
            }

            CouponDTO couponDTO = promo.getPromotionDTO().getCouponDTO();
            CouponDetailItem item = genCouponDetailItem(couponDTO);

            if (couponDTO.isIsMerchantCoupon()) {
                merchantCoupons.add(item);
            } else {
                platformCoupons.add(item);
            }
        }

        if (CollectionUtils.isEmpty(merchantCoupons) && CollectionUtils.isEmpty(platformCoupons)) {
            return null;
        }

        PromoAggInfo info = new PromoAggInfo();
        info.setMerchantCoupons(merchantCoupons);
        info.setPlatformCoupons(platformCoupons);

        return info;
    }

    private static CouponDetailItem genCouponDetailItem(CouponDTO dto) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.util.PromoInfoHelper.genCouponDetailItem(com.sankuai.nibmktproxy.queryclient.proxy.CouponDTO)");
        CouponDetailItem item = new CouponDetailItem();
        item.setCouponGroupId(dto.getCouponGroupId());
        item.setAmount(toYuanStr(dto.getCouponValue()));
        item.setCanAssign(dto.isCanAssign());
        item.setTitle(buildCouponTitle(dto));

        String categoryLimitText = dto.getCategoryLimitText();
        item.setCouponCategory(StringUtils.isBlank(categoryLimitText) ? null : "查看适用品类");
        item.setCouponCategoryDesc(categoryLimitText);

        item.setCouponSuitDesc(buildCouponSuitDesc(dto));
        item.setCouponThresholdDesc(buildCouponThresholdDesc(dto));
        //item.setDisplayText("");
        //item.setCouponSrc(dto.isIsMerchantCoupon() ? 1 : 0);
        item.setFreshExclusive(dto.isNewUserUse());
        item.setOptimal(dto.isOptimal());

        return item;
    }

    private static BigDecimal toYuan(String couponValue) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.util.PromoInfoHelper.toYuan(java.lang.String)");
        return new BigDecimal(couponValue).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP);
    }

    private static String toYuanStr(String amount) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.util.PromoInfoHelper.toYuanStr(java.lang.String)");
        return toYuan(amount).stripTrailingZeros().toPlainString();
    }

    private static String buildCouponTitle(CouponDTO couponDTO) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.util.PromoInfoHelper.buildCouponTitle(com.sankuai.nibmktproxy.queryclient.proxy.CouponDTO)");
        if (!couponDTO.isIsMerchantCoupon()) {
            return couponDTO.getCouponTitle();
        }

        int categoryLimit = couponDTO.getCategoryLimit();
        String title;

        switch (categoryLimit) {
            case 0 :
                title = "商家通用券";
                break;
            case 1 :
                title = "商家品类券";
                break;
            case 2 :
                title = "商家商品券";
                break;
            default:
                title = couponDTO.getCouponTitle();

        }

        return title;
    }

    private static String buildCouponThresholdDesc(CouponDTO couponDTO) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.util.PromoInfoHelper.buildCouponThresholdDesc(com.sankuai.nibmktproxy.queryclient.proxy.CouponDTO)");
        String minConsumption = couponDTO.getMinConsumption();
        if (noThreshold(couponDTO)) {
            return "无门槛";
        }

        return "满" + toYuan(minConsumption).stripTrailingZeros().toPlainString() + "元可用";
    }

    private static boolean noThreshold(CouponDTO dto) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.util.PromoInfoHelper.noThreshold(com.sankuai.nibmktproxy.queryclient.proxy.CouponDTO)");
        return StringUtils.isBlank(dto.getMinConsumption()) || BigDecimal.ZERO.compareTo(new BigDecimal(dto.getMinConsumption())) == 0;
    }

    private static String buildCouponSuitDesc(CouponDTO couponDTO) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.util.PromoInfoHelper.buildCouponSuitDesc(com.sankuai.nibmktproxy.queryclient.proxy.CouponDTO)");
        String validDateText = couponDTO.getValidDateText();
        String compositionText = null;

        if (couponDTO.isIsMerchantCoupon()) {
            compositionText = couponDTO.getCompositionText();
        }

        String text = null;

        if (StringUtils.isNotBlank(validDateText)) {
            text = validDateText;
        }

        if (StringUtils.isNotBlank(compositionText)) {
            if (text != null) {
                text = validDateText + "，" + compositionText;
            } else {
                text = compositionText;
            }
        }

        return text;
    }

    public static PromoInfoItem genCouponInfoItem(DealCtx ctx) {
        PromotionDTOResult promotionDTOResult = extractPromoResult(ctx);
        if (promotionDTOResult == null) {
            return null;
        }

        List<GetPromotionDTO> promos = promotionDTOResult.getGetPromotionDTO();

        PromoInfoItem promoInfoItem = new PromoInfoItem();
        promoInfoItem.setType(PromoTypeEnum.COUPON.getCode());
        promoInfoItem.setPromoTitle("抵用券");
        promoInfoItem.setIconUrl(ctx.isMt() ? PlusIcons.MT_COUPON : PlusIcons.DP_COUPON);
        List<PromoInfoDescItem> concisePromoInfoDescItems = Lists.newArrayList();

        for (GetPromotionDTO promo : promos) {
            if (!isCouponPromo(promo)) {
                continue;
            }

            CouponDTO couponDTO = promo.getPromotionDTO().getCouponDTO();
            PromoInfoDescItem promoInfoDescItem = new PromoInfoDescItem();

            promoInfoDescItem.setStyle(1);
            promoInfoDescItem.setText(buildConciseCouponText(couponDTO));
            concisePromoInfoDescItems.add(promoInfoDescItem);
        }

        if (CollectionUtils.isEmpty(concisePromoInfoDescItems)) {
            return null;
        }
        if (concisePromoInfoDescItems.size() > 2) {
            promoInfoItem.setPromoInfoDescItems(concisePromoInfoDescItems.subList(0, 2));
        } else {
            promoInfoItem.setPromoInfoDescItems(concisePromoInfoDescItems);
        }

        promoInfoItem.setLeadAction(LeadActionEnum.TOAST_DEFAULT.getCode());
        promoInfoItem.setLeadText("去领券");
        return promoInfoItem;

    }

    private static String buildConciseCouponText(CouponDTO couponDTO) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.util.PromoInfoHelper.buildConciseCouponText(com.sankuai.nibmktproxy.queryclient.proxy.CouponDTO)");
        String minConsumption = couponDTO.getMinConsumption();
        String amt = toYuanStr(couponDTO.getCouponValue());

        if (noThreshold(couponDTO)) {
            return amt + "元无门槛券";
        }

        String thresholdAmt = toYuanStr(minConsumption);
        return "满" + thresholdAmt + "减" + amt + "券";
    }

    private static boolean isCouponPromo(GetPromotionDTO promo) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.util.PromoInfoHelper.isCouponPromo(com.sankuai.nibmktproxy.queryclient.proxy.GetPromotionDTO)");

        if (promo.getPromotionDTO() == null || promo.getPromotionDTO().getCouponDTO() == null) {
            return false;
        }

        return promo.getPromotionType() != null && promo.getPromotionType().getValue() == PromotionType.COUPON.getValue();
    }

    public static String getFinanceExtJson(PromoDTO coupon) {
        if (Objects.isNull(coupon)) {
            return org.apache.commons.lang.StringUtils.EMPTY;
        }
        Map<String, String> promotionOtherInfoMap = coupon.getPromotionOtherInfoMap();
        if (org.apache.commons.collections.MapUtils.isNotEmpty(promotionOtherInfoMap) && promotionOtherInfoMap.containsKey(PromotionPropertyEnum.FINANCE_EXT.getValue())) {
            return promotionOtherInfoMap.get(PromotionPropertyEnum.FINANCE_EXT.getValue());
        }
        return org.apache.commons.lang.StringUtils.EMPTY;
    }

    public static String getFinanceExtPackageSecretKey(PromoDTO coupon) {
        String financeExtJson = PromoInfoHelper.getFinanceExtJson(coupon);
        if (org.apache.commons.lang.StringUtils.isNotBlank(financeExtJson)) {
            Map<String, String> financeExtMap = JsonUtils.fromJson(financeExtJson, new TypeReference<Map<String, String>>() {});
            return financeExtMap.getOrDefault(PACKAGE_SECRET_KEY, org.apache.commons.lang.StringUtils.EMPTY);
        }
        return org.apache.commons.lang.StringUtils.EMPTY;
    }

    public static PricePowerTagItem getFirstPricePowerTag(DealCtx ctx) {
        PriceContext priceContext = ctx.getPriceContext();
        if (priceContext == null) {
            return null;
        }
        PriceDisplayDTO priceDisplayDTO = priceContext.getDealPromoPrice();
        if (Objects.isNull(priceDisplayDTO)) {
            return null;
        }
        PricePowerTagDisplayDTO pricePowerTagDisplayDTO = priceDisplayDTO.getPricePowerTagDisplayDTO();
        if (pricePowerTagDisplayDTO == null || CollectionUtils.isEmpty(pricePowerTagDisplayDTO.getAllTagList())) {
            return null;
        }
        List<PricePowerTagItem> allTagList = pricePowerTagDisplayDTO.getAllTagList();
        if (CollectionUtils.isEmpty(allTagList)) {
            return null;
        }
        allTagList.sort(Comparator.comparingInt(item -> PromoDetailModuleBuilderService.XIUYU_PRICE_POWER_TAG_SORT_ORDER.indexOf(item.getTagType())));
        PricePowerTagItem firstTag = allTagList.get(0);
        boolean showPricePowerTag = firstTag != null
                && PromoDetailModuleBuilderService.TIME_PRICE_POWER_TAG_VALUES.contains(firstTag.getTagType());
        return showPricePowerTag ? firstTag : null;
    }
}
