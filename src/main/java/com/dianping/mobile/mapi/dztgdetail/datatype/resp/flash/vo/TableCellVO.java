package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/1/28 3:54 下午
 */
@MobileDo(id = 0x5615)
public class TableCellVO implements Serializable {
    /**
     * 横跨几个单元格
     */
    @MobileDo.MobileField(key = 0xea38)
    private int cellColumnCount;

    /**
     * 竖跨几个单元格
     */
    @MobileDo.MobileField(key = 0x7266)
    private int cellRowCount;

    /**
     * 弹窗
     */
    @MobileDo.MobileField(key = 0x6a92)
    private PopUpWindowVO popUp;

    /**
     * 内容
     */
    @MobileDo.MobileField(key = 0xcce)
    private String content;

    public int getCellColumnCount() {
        return cellColumnCount;
    }

    public void setCellColumnCount(int cellColumnCount) {
        this.cellColumnCount = cellColumnCount;
    }

    public int getCellRowCount() {
        return cellRowCount;
    }

    public void setCellRowCount(int cellRowCount) {
        this.cellRowCount = cellRowCount;
    }

    public PopUpWindowVO getPopUp() {
        return popUp;
    }

    public void setPopUp(PopUpWindowVO popUp) {
        this.popUp = popUp;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
