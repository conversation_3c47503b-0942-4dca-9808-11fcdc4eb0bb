package com.dianping.mobile.mapi.dztgdetail.facade.rcf.grey;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Set;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2023/8/7
 * @since dzviewscene-productshelf-home
 */
@Getter
@Setter
@NoArgsConstructor
public class DefaultGroupGrayConfig<T extends AbstractGrayConfigContext> extends AbstractStaticsGrayConfig<T> {

    private volatile DefaultGroupGrayConfig staticsGrayConfig;

    private static Set<String> repeats = Sets.newHashSet();

    public static  DefaultGroupGrayConfig createGroupGrayConfig(String name) {
        return createGroupGrayConfig(name,DefaultGroupGrayConfig.class);
    }
    public static  DefaultGroupGrayConfig createGroupGrayConfigWithKey(String key) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.grey.DefaultGroupGrayConfig.createGroupGrayConfigWithKey(java.lang.String)");
        try {
            return JSON.parseObject(Lion.get(key, "{}"), DefaultGroupGrayConfig.class);
        } catch (Exception e) {
            Cat.logError(e);
        }
        return null;
    }

    public static  <U extends AbstractStaticsGrayConfig> U createGroupGrayConfig(String name,Class<U> clz) {
        try {
            return JSON.parseObject(Lion.get(getGrayKey(name), "{}"), clz);
        } catch (Exception e) {
            Cat.logError(e);
        }
        return null;
    }

    /***
     * com.sankuai.dzu.tpbase.dztgdetailweb.deal.config
     * @param name
     * @return
     */
    private static String getGrayKey(String name) {
        StringBuffer buffer = new StringBuffer();
        buffer.append("com.sankuai.dzu.tpbase.dztgdetailweb");
        buffer.append(".");
        buffer.append(name);
        buffer.append(".config");
        return buffer.toString();
    }

}