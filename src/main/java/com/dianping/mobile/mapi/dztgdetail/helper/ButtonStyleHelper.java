package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.BestPromoDetailsStyleInfo;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class ButtonStyleHelper {

    protected Logger logger = LoggerFactory.getLogger(ButtonStyleHelper.class);

    /**
     * 在单会员button的时候，修改优惠明细信息
     */
    public boolean singleMemberButton(DealCtx ctx) {
        if (ctx.getBuyBar() == null || ctx.getBuyBar().getBuyBtns() == null) {
            return false;
        }

        List<DealBuyBtn> buyBtns = ctx.getBuyBar().getBuyBtns();
        if (buyBtns.size() != 1) {
            return false;
        }

        return buyBtns.get(0).getDetailBuyType() == BuyBtnTypeEnum.MEMBER_CARD.getCode();
    }

    /**
     * 在双button的时候，且会员button在右边的时候，修改优惠明细信息
     */
    public boolean doubleButtonMemberRight(DealCtx ctx) {
        if (ctx.getBuyBar() == null || ctx.getBuyBar().getBuyBtns() == null) {
            return false;
        }

        List<DealBuyBtn> buyBtns = ctx.getBuyBar().getBuyBtns();
        if (buyBtns.size() != 2) {
            return false;
        }

        return buyBtns.get(1).getDetailBuyType() == BuyBtnTypeEnum.MEMBER_CARD.getCode();
    }

    public void entertainmentMemberPromoDetailComplete(DealCtx ctx, boolean isZuLiaoButtonNewStyle, DealGroupPBO result,
                                                       PromoDTO promoDTO, PromoDetailModule promoDetailModule, DealBestPromoDetail bestPromoDetail) {
        try {
            if (isZuLiaoButtonNewStyle) {
                if (isMemberOrDiscountPromoDto(promoDTO)) {
                    BigDecimal promoDiscount = promoDTO.getPromoDiscount();
                    BigDecimal discountRateWithinTen = promoDiscount.multiply(new BigDecimal(10)).stripTrailingZeros();

                    bestPromoDetail.setIconUrl("https://p1.meituan.net/travelcube/86bb081f1be80d382eedefc7ca9b08264615.png");
                    if (promoDTO.getIdentity().getPromoType() == PromoTypeEnum.DISCOUNT_CARD.getType()){
                        bestPromoDetail.setPromoName("会员享" + discountRateWithinTen + "折优惠");
                    } else if(promoDTO.getIdentity().getPromoType() == PromoTypeEnum.MEMBER_DAY.getType()) {
                        bestPromoDetail.setPromoName("会员日享" + discountRateWithinTen + "折优惠");
                    }

                    buildPromoDetailExtraInfo(bestPromoDetail, promoDiscount, discountRateWithinTen);

                    if (ctx.getBuyBar() != null && CollectionUtils.isNotEmpty(ctx.getBuyBar().getBuyBtns())
                            && ctx.getBuyBar().getBuyBtns().size() == 1) {

                        DealBuyBtn dealBuyBtn = ctx.getBuyBar().getBuyBtns().get(0);
                        if (dealBuyBtn.getDetailBuyType() == BuyBtnTypeEnum.MEMBER_CARD.getCode()) {
                            buildBestPromoDetailsStyleInfo(promoDetailModule);
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("zuLiaoPromoDetailComplete error!", e);
        }
    }

    private boolean isMemberOrDiscountPromoDto(PromoDTO promoDTO) {
        return (promoDTO.getIdentity().getPromoType() == PromoTypeEnum.DISCOUNT_CARD.getType()
                || (promoDTO.getIdentity().getPromoType() == PromoTypeEnum.MEMBER_DAY.getType()))
                && promoDTO.getAmount() != null && promoDTO.getPromoDiscount() != null;
    }

    private void buildPromoDetailExtraInfo(DealBestPromoDetail bestPromoDetail, BigDecimal promoDiscount,BigDecimal discountRateWithinTen) {
        PromoDetailExtraInfo promoDetailExtraInfo = new PromoDetailExtraInfo();
        promoDetailExtraInfo.setPromoDetailExtraInfoType(1);
        promoDetailExtraInfo.setDiscountRateWithinOne(PriceHelper.dropLastZero(promoDiscount));
        promoDetailExtraInfo.setDiscountRateWithinTen(PriceHelper.dropLastZero(discountRateWithinTen));
        bestPromoDetail.setPromoDetailExtraInfo(promoDetailExtraInfo);
    }

    private void buildBestPromoDetailsStyleInfo(PromoDetailModule promoDetailModule) {
        BestPromoDetailsStyleInfo bestPromoDetailsStyleInfo = new BestPromoDetailsStyleInfo();
        bestPromoDetailsStyleInfo.setIcon("https://p0.meituan.net/travelcube/5a8155ede19cb4b569fca5de022be3c42177.png");
        bestPromoDetailsStyleInfo.setDesc("会员共省");
        bestPromoDetailsStyleInfo.setMark("https://p1.meituan.net/travelcube/8fbfba021c31dd4aa4629941f558d3ba1339.png");
        bestPromoDetailsStyleInfo.setType(1);

        promoDetailModule.setBestPromoDetailsStyleInfo(bestPromoDetailsStyleInfo);
    }
}
