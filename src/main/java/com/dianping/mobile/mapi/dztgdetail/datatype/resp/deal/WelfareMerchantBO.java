package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: huqi
 * @Date: 2020/3/12 3:39 下午
 */
@Data
@TypeDoc(description = "公益商家模型")
public class WelfareMerchantBO implements Serializable {
    /**
     * 公益商家描述
     */
    @MobileDo.MobileField(key = 0xfebf)
    private String desc;

    /**
     * 公益商家标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * 公益商家icon
     */
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;


}
