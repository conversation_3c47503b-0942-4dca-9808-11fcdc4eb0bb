package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.alibaba.fastjson.JSON;
import com.dianping.account.dto.MeituanUserInfoDTO;
import com.dianping.account.dto.UserAccountDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.enums.FitnessCrossIdentityEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.FitnessCrossBO;
import com.dianping.pay.promo.common.enums.User;
import com.dianping.tuangu.dztg.usercenter.api.CreateOrderPageUrlService;
import com.dianping.tuangu.dztg.usercenter.api.dto.BatchGetCreateOrderPageUrlDto;
import com.dianping.tuangu.dztg.usercenter.api.dto.GetCreateOrderPageUrlEnvDto;
import com.dianping.tuangu.dztg.usercenter.api.dto.Response;
import com.dianping.tuangu.dztg.usercenter.api.enums.ChannelEnum;
import com.dianping.tuangu.dztg.usercenter.api.enums.CreateOrderPageSourceEnum;
import com.dianping.tuangu.dztg.usercenter.api.enums.Source;
import com.dianping.tuangu.dztg.usercenter.api.request.BatchGetCreateOrderPageUrlReq;
import com.dianping.unified.coupon.manage.api.UnifiedCouponInfoService;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.enums.UnifiedCouponManageResultCodeEnum;
import com.dianping.unified.coupon.manage.api.request.BatchLoadCouponRequest;
import com.dianping.unified.coupon.manage.api.response.UnifiedCouponManageResponse;
import com.dianping.vc.sdk.concurrent.threadpool.ExecutorServices;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.dzcard.fulfill.api.MemberCardQueryService;
import com.sankuai.dzcard.fulfill.api.dto.MemberCardDTO;
import com.sankuai.dzcard.fulfill.api.dto.MemberCardRightDTO;
import com.sankuai.dzcard.fulfill.api.enums.MemberCardResponseCode;
import com.sankuai.dzcard.fulfill.api.request.PageQueryMemberCardRequest;
import com.sankuai.dzcard.fulfill.api.request.QueryMemberCardRequest;
import com.sankuai.dzcard.fulfill.api.response.MemberCardResponse;
import com.sankuai.dzcard.fulfill.core.enums.MemberCardStatusEnum;
import com.sankuai.dzcard.fulfill.core.enums.MemberCardTypeEnum;
import com.sankuai.dzcard.supply.enums.RightPackageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
public class FitnessCrossDealProcessor extends AbsDealProcessor {

    /**
     * 卡券、交易
     */
    @Resource
    @Qualifier("memberCardQueryService")
    private MemberCardQueryService memberCardQueryService;

    /**
     * 营销
     */
    @Resource
    @Qualifier("unifiedCouponInfoService")
    private UnifiedCouponInfoService unifiedCouponInfoService;

    /**
     * 交易提单跳链查询
     */
    @Resource
    @Qualifier("createOrderPageUrlService")
    private CreateOrderPageUrlService createOrderPageUrlService;

    /**
     * 执行IO查询操作的线程池
     */
    public static final ExecutorService FITNESS_CROSS_EXECUTOR_SERVICE =
            ExecutorServices.forThreadPoolExecutor("FITNESS_CROSS_EXECUTOR_SERVICE", Runtime.getRuntime().availableProcessors(), 2 * Runtime.getRuntime().availableProcessors());

    /**
     * 健身通老客状态
     */
    public static final Set<Integer> FITNESS_CROSS_STATUS_SET = Sets.newHashSet(
            MemberCardStatusEnum.CREATED.getStatus(),
            MemberCardStatusEnum.UN_ACTIVE.getStatus(),
            MemberCardStatusEnum.NORMAL.getStatus());

    /**
     * 健身通卡类型
     */
    public static final int FITNESS_CROSS_RIGHT_PACKAGE_TYPE = RightPackageTypeEnum.FITNESS_COUPON_PACKAGE.getCode();

    /**
     * lion配置相关
     */
    public static final String LION_APP_KEY = "com.sankuai.dzu.tpbase.dztgdetailweb";
    public static final String LION_FITNESS_TEXT_CONFIG_KEY = "com.sankuai.dzu.tpbase.dztgdetailweb.fitnesscross.text.config";

    @Override
    public boolean isEnable(DealCtx ctx) {
        return ctx != null && ctx.getEnvCtx() != null && ctx.isFitnessCrossDeal();
    }

    @Override
    public void prepare(DealCtx ctx) {
        long userId = ctx.isMt() ? ctx.getEnvCtx().getMtUserId() : ctx.getEnvCtx().getDpUserId();//已确认判断平台后再使用
        FutureCtx futureCtx = ctx.getFutureCtx();

        // 用户信息
        Future<?> userAccountFuture = ctx.isMt() ? futureCtx.getMtUserAccountDTOFuture() : futureCtx.getDpUserAccountDTOFuture();

        // 查是否有可用券
        futureCtx.setFitnessCrossAvailableCouponFuture(CompletableFuture.supplyAsync(
                () -> hasAvailableCoupon(
                        userId,
                        userAccountFuture,
                        !ctx.isMt())
                , FITNESS_CROSS_EXECUTOR_SERVICE));

        // 查身份
        futureCtx.setFitnessCrossIdentityEnumFuture(CompletableFuture.supplyAsync(
                () -> getIdentityEnum(
                        ctx,
                        userAccountFuture)
                , FITNESS_CROSS_EXECUTOR_SERVICE));

        // 查提单页url
        futureCtx.setFitnessCrossOrderPageUrlFuture(CompletableFuture.supplyAsync(() -> getOrderPageUrl(ctx), FITNESS_CROSS_EXECUTOR_SERVICE));
    }

    @Override
    public void process(DealCtx ctx) {
        FutureCtx futureCtx = ctx.getFutureCtx();
        if (futureCtx == null) {
            log.info("FitnessCrossDealProcessor.process fail, futureCtx is null");
            return;
        }

        try {
            FitnessCrossBO fitnessCrossBO = FitnessCrossBO.builder()
                    .hasAvailableCoupon(futureCtx.getFitnessCrossAvailableCouponFuture().get()) // 是否有有效卡券
                    .identityEnum(futureCtx.getFitnessCrossIdentityEnumFuture().get()) // 用户身份
                    .config(getTextConfig()) // 文案配置
                    .orderUrl(futureCtx.getFitnessCrossOrderPageUrlFuture().get()) // 提单页url
                    .build();

            ctx.setFitnessCrossBO(fitnessCrossBO);
        } catch (Exception e) {
            log.error("FitnessCrossDealProcessor.process fail, e is ", e);
        }
    }

    /**
     * 获取并设置身份枚举
     */
    private FitnessCrossIdentityEnum getIdentityEnum(DealCtx ctx, Future<?> userAccountFuture) {
        if (ctx == null || ctx.getEnvCtx() == null) {
            return FitnessCrossIdentityEnum.TOURIST;
        }

        if (userAccountFuture == null) {
            return FitnessCrossIdentityEnum.TOURIST;
        }

        try {
            return getUserIdentity(getMobile(userAccountFuture), ctx.isMt() ? 2 : 1);
        } catch (Exception e) {
            log.error("FitnessCrossDealProcessor.getIdentityEnum fail, e is ", e);
            return FitnessCrossIdentityEnum.TOURIST;
        }
    }

    /**
     * 从future中获取手机号码
     */
    private String getMobile(Future<?> userAccountFuture) {
        if (userAccountFuture == null) {
            return null;
        }

        try {
            Object userAccountObj = userAccountFuture.get(500, TimeUnit.MILLISECONDS);

            if (userAccountObj == null) {
                return null;
            }

            if (userAccountObj instanceof UserAccountDTO) {
                return ((UserAccountDTO) userAccountObj).getMobile();
            } else if (userAccountObj instanceof MeituanUserInfoDTO) {
                return ((MeituanUserInfoDTO) userAccountObj).getMobileNo();
            } else {
                log.error("FitnessCrossDealProcessor.getMobile fail, userAccountObj type error, userAccountObj is [{}]", JSON.toJSONString(userAccountObj));
                return null;
            }
        } catch (Exception e) {
            log.error("FitnessCrossDealProcessor.getMobile fail, e is ", e);
            return null;
        }
    }

    /**
     * 获取并设置文案配置
     */
    private FitnessCrossBO.Config getTextConfig() {
        return Lion.getBean(LION_APP_KEY, LION_FITNESS_TEXT_CONFIG_KEY, FitnessCrossBO.Config.class);
    }

    /**
     * 查询用户所有开卡信息
     *
     * @param phoneNo  用户手机
     * @param platform 平台，点评=1，美团=2
     */
    private List<MemberCardDTO> getCardListByUserId(String phoneNo, int platform) {
        if (StringUtils.isEmpty(phoneNo)) {
            return Collections.emptyList();
        }
        QueryMemberCardRequest request = new QueryMemberCardRequest();
        request.setPhoneNo(phoneNo);
        request.setPlatform(platform);
        request.setStatus(Lists.newArrayList(FitnessCrossDealProcessor.FITNESS_CROSS_STATUS_SET));
        request.setCardTemplateTypeList(Lists.newArrayList(FitnessCrossDealProcessor.FITNESS_CROSS_RIGHT_PACKAGE_TYPE));
        MemberCardResponse<List<MemberCardDTO>> response;
        try {
            response = memberCardQueryService.queryMemberCard(request);
        } catch (Exception e) {
            log.error("查询是否开卡失败 mobile is [{}], e is ", phoneNo, e);
            return null;
        }
        if (!response.isSuccess()) {
            log.error("查询是否开卡失败 mobile is [{}], msg is [{}]", phoneNo, response.getResultMessage());
            return null;
        }
        if (CollectionUtils.isEmpty(response.getResult())) {
            return Collections.emptyList();
        }
        return Optional.ofNullable(response.getResult()).orElse(new ArrayList<>());
    }

    /**
     * 获取用户类型
     * NOT_PAY(1, "首充用户"),
     * TOURIST(2, "游客"),
     * HAS_PAY(3, "复充用户");
     *
     * @see FitnessCrossIdentityEnum
     */
    public FitnessCrossIdentityEnum getUserIdentity(String phoneNo, int platform) {
        if (StringUtils.isEmpty(phoneNo)) {
            // 参数错误：游客状态
            log.error("CardHandler.hadPayCard fail, phoneNo is [{}], platform is [{}]", phoneNo, platform);
            return FitnessCrossIdentityEnum.TOURIST;
        }

        // 健身通卡对象
        List<MemberCardDTO> cardDTOS = getCardListByUserId(phoneNo, platform);
        if (cardDTOS == null) {
            // 查询失败：游客状态
            return FitnessCrossIdentityEnum.TOURIST;
        }
        return CollectionUtils.isEmpty(cardDTOS) ? FitnessCrossIdentityEnum.NOT_PAY : FitnessCrossIdentityEnum.HAS_PAY;
    }

    /**
     * 是否有有效的卡券，状态有效并且时间没有过期
     */
    public boolean hasAvailableCoupon(long userId, Future<?> userAccountFuture, boolean isDp) {
        if (userId <= 0) {
            log.info("FitnessCrossDealProcessor.hasAvailableCoupon fail, userId is [{}]", userId);
            return false;
        }

        String mobile = getMobile(userAccountFuture);

        if (StringUtils.isEmpty(mobile)) {
            log.info("FitnessCrossDealProcessor.hasAvailableCoupon fail, mobile is empty");
            return false;
        }

        // 分页参数，页面大小由交易侧定
        int pageNo = 1;
        int pageSize = 20;
        boolean hasAvailableCoupon = false;
        while (!hasAvailableCoupon) {
            // 查交易侧的券
            List<MemberCardDTO> memberCards = getMemberCards(mobile, pageNo++, pageSize);
            if (CollectionUtils.isEmpty(memberCards)) {
                break;
            }

            // 查营销侧券信息
            List<String> couponIds = memberCards.stream()
                    .filter(memberCardDTO -> memberCardDTO != null && !CollectionUtils.isEmpty(memberCardDTO.getRights()))
                    .map(MemberCardDTO::getRights)
                    .flatMap(List::stream)
                    .filter(Objects::nonNull)
                    .map(MemberCardRightDTO::getReferUserRightId)
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(couponIds)) {
                break;
            }

            List<UnifiedCouponDTO> couponDTOS = batchLoadCoupon(userId, isDp, couponIds);
            if (CollectionUtils.isEmpty(couponDTOS)) {
                break;
            }

            // 判断是否有效
            hasAvailableCoupon = couponDTOS.stream().filter(Objects::nonNull).anyMatch(this::judgeCouponIsAvailable);
        }

        return hasAvailableCoupon;
    }

    /**
     * 查券
     */
    public List<MemberCardDTO> getMemberCards(String phoneNo, int pageNo, int pageSize) {
        Date now = new Date();
        PageQueryMemberCardRequest request = new PageQueryMemberCardRequest();
        request.setPhoneNo(phoneNo);
        request.setMemberCardType(MemberCardTypeEnum.FITNESS_PASS_COUPON_PACKAGE.getCode());
        request.setStartTime(getTwoYearsAgoTime(now));
        request.setEndTime(now);
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        request.setQueryMemberCardRightOption(true);
        request.setQueryMemberCardRightFlowOption(true);

        MemberCardResponse<List<MemberCardDTO>> response;
        try {
            response = memberCardQueryService.pageQueryMemberCard(request);
        } catch (Exception e) {
            log.error("FitnessCrossDealProcessor.getMemberCards fail, phoneNo is [{}], e is {}", phoneNo, e);
            return Collections.emptyList();
        }

        if (response == null || response.getResultCode() != MemberCardResponseCode.SUCCESS.getCode()) {
            log.error("FitnessCrossDealProcessor.getMemberCards fail, phoneNo is [{}], resultMsg is [{}]", phoneNo, response == null ? null : response.getResultMessage());
            return Collections.emptyList();
        }

        return response.getResult();
    }

    /**
     * 根据用户券ID查询券信息
     */
    public List<UnifiedCouponDTO> batchLoadCoupon(long userId, boolean isDp, List<String> couponIds) {
        if (userId <= 0 || CollectionUtils.isEmpty(couponIds)) {
            log.error("FitnessCrossDealProcessor.batchLoadCoupon fail, userId is [{}], couponIds is [{}]", userId, JSON.toJSONString(couponIds));
            return Collections.emptyList();
        }

        BatchLoadCouponRequest request = new BatchLoadCouponRequest();
        request.setUnifiedCouponIdList(couponIds);
        request.setUserType(isDp ? User.DP.getFlag() : User.MT.getFlag());
        request.setUserId(userId);

        UnifiedCouponManageResponse<List<UnifiedCouponDTO>> response;
        try {
            response = unifiedCouponInfoService.batchLoadCoupon(request);
        } catch (Exception e) {
            log.error("FitnessCrossDealProcessor.batchLoadCoupon fail, userId is [{}], couponIds is [{}], e is {}", userId, JSON.toJSONString(couponIds), e);
            return Collections.emptyList();
        }

        if (response == null || UnifiedCouponManageResultCodeEnum.Success.getCode() != response.getResultCode()) {
            log.error("FitnessCrossDealProcessor.batchLoadCoupon fail, userId is [{}], couponIds is [{}], resultMsg is [{}]", userId, JSON.toJSONString(couponIds), response == null ? null : response.getResultMsg());
            return Collections.emptyList();
        }

        return response.getResult();
    }

    /**
     * 获取提单页url
     */
    public String getOrderPageUrl(DealCtx ctx) {
        if (ctx == null) {
            return null;
        }

        int channel = ChannelEnum.APP.getType();
        if (ctx.getEnvCtx() != null
                && (ctx.getEnvCtx().getClientType() == DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP.getCode() || ctx.getEnvCtx().getClientType() == DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP.getCode())) {
            channel = ChannelEnum.WX_MINI_PROGRAM.getType();
        }
        if (ctx.getEnvCtx() != null && ctx.getEnvCtx().getClientType() == DztgClientTypeEnum.MEITUAN_KUAISHOU_MINIAPP.getCode()) {
            channel = ChannelEnum.KS_MINI_PROGRAM.getType();
        }

        GetCreateOrderPageUrlEnvDto envDto = new GetCreateOrderPageUrlEnvDto();
        envDto.setPlatform(ctx.isMt() ? Source.MT.value : Source.DP.value);
        envDto.setChannel(channel);
        envDto.setPageSource(CreateOrderPageSourceEnum.DEAL_GROUP_DETAIL.getType());
        envDto.setVersion(ctx.getEnvCtx().getVersion());

        BatchGetCreateOrderPageUrlDto pageUrlDto = new BatchGetCreateOrderPageUrlDto();
        pageUrlDto.setProductId(String.valueOf(ctx.isMt() ? ctx.getMtId() : ctx.getDpId()));
        pageUrlDto.setSkuId(ctx.getSkuId());
        pageUrlDto.setShopId(String.valueOf(ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId()));
        pageUrlDto.setShopUuid(ctx.isMt() ? null : ctx.getDpShopUuid()); // 点评传，美团不传
        Map<String, String> extUrlParam = Maps.newHashMap();
        extUrlParam.put("expid", "floatingLayer");
        pageUrlDto.setExtUrlParam(extUrlParam); // 指定返回提单页浮层

        BatchGetCreateOrderPageUrlReq req = new BatchGetCreateOrderPageUrlReq();
        req.setEnvDto(envDto);
        req.setBatchGetCreateOrderPageUrlDtoList(Collections.singletonList(pageUrlDto));

        Response<Map<String, String>> response;
        try {
            response = createOrderPageUrlService.batchGetCreateOrderPageUrl(req);
        } catch (Exception e) {
            log.error("FitnessCrossDealProcessor.getOrderPageUrl fail, e is ", e);
            return null;
        }

        if (response == null || !response.isSuccess() || response.getContent() == null) {
            log.info("FitnessCrossDealProcessor.getOrderPageUrl fail, errorMsg is [{}]", response == null ? null : response.getMsg());
            return null;
        }

        String url = response.getContent().get(String.valueOf(ctx.isMt() ? ctx.getMtId() : ctx.getDpId()));
        // 指定高度
        return StringUtils.isEmpty(url) ? null : url + "&popupscreenrate=0.6";
    }

    /**
     * 获取两年前的时间
     */
    private Date getTwoYearsAgoTime(Date date) {
        if (date == null) {
            date = new Date();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.YEAR, -2);
        return calendar.getTime();
    }

    /**
     * 判断卡券是否有效
     */
    private boolean judgeCouponIsAvailable(UnifiedCouponDTO couponDTO) {
        if (couponDTO == null) {
            return false;
        }
        return couponDTO.isAvailable()
                && couponDTO.getEndTime() != null
                && new Date().compareTo(couponDTO.getEndTime()) < 0
                && couponDTO.getStatus() == 1
                && !couponDTO.isUsed();
    }

}
