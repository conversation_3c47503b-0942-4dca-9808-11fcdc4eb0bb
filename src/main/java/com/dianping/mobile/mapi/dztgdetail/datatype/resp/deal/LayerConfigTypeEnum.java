package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

/**
 * <AUTHOR>
 * @create 2024/11/19 15:41
 */
public enum LayerConfigTypeEnum {
    TYPE_1("退改协议", 1),
    TYPE_2("价保", 2),
    TYPE_3("买贵必赔", 3),
    TYPE_4("游乐险", 4),
    TYPE_5("正规资质", 5),
    TYPE_6("正品可追溯", 6),
    TYPE_7("安心配镜", 7),
    TYPE_8("正品保障", 8),
    TYPE_9("预付金退款", 9),
    TYPE_10("延期赔付", 10),
    TYPE_11("保修保障", 11),
    TYPE_12("安心医·补牙", 12),
    TYPE_13("安心医·种植", 13),
    TYPE_14("安心学/安心练", 14),
    ;
    private int type;
    private String title;
    LayerConfigTypeEnum(String title, int type){
        this.type = type;
        this.title = title;
    }
    public int getType() {
        return this.type;
    }
}
