package com.dianping.mobile.mapi.dztgdetail.button.joy;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonStateEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;

public class NewCarIconAndTitleAdaptBuilder extends AbstractButtonBuilder {

    @Override
    public void build(DealCtx context, ButtonBuilderChain chain) {
        adaptIconTitle(context);
        chain.build(context);
    }

    private void adaptIconTitle(DealCtx context) {

        // 只有一个按钮， 不用处理
        if (context.getButtonCount() <= 1) {
            return;
        }

        // 双button按钮名称调整，移除图片的icon
        for (DealBuyBtn buyBtn : context.getBuyBar().getBuyBtns()) {
            if (buyBtn.getDetailBuyType() == BuyBtnTypeEnum.MEMBER_CARD.getCode()) {
                buyBtn.setBtnTitle(getPromoButtonTitle(context, "会员价"));
                if (buyBtn.getState() == ButtonStateEnum.HOLD) {
                    buyBtn.getBtnIcons().remove(0);
                }
            } else if (buyBtn.getDetailBuyType() == BuyBtnTypeEnum.JOY_CARD.getCode()) {
                buyBtn.setBtnTitle(getPromoButtonTitle(context, "玩乐卡价"));
                if (buyBtn.getState() == ButtonStateEnum.HOLD) {
                    buyBtn.getBtnIcons().remove(0);
                }
            }
        }

    }

    @Override
    protected void doBuild(DealCtx context, ButtonBuilderChain chain) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.button.joy.NewCarIconAndTitleAdaptBuilder.doBuild(DealCtx,ButtonBuilderChain)");

    }
}
