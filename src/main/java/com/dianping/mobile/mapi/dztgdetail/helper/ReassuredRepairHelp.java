package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public class ReassuredRepairHelp {


    public static List<String> getReassuredRepairReminders(DealCtx ctx,  List<String> reminders){
        Map<String, ServiceProjectAttrDTO> serviceProjectAttrDTOMap = getStringServiceProjectAttrDTOMap(ctx);
        if (serviceProjectAttrDTOMap.containsKey("qizhuangxianzhi")) {
            ServiceProjectAttrDTO qizhuangxianzhi = serviceProjectAttrDTOMap.get("qizhuangxianzhi");
            ServiceProjectAttrDTO qizhuangxianzhi2 = serviceProjectAttrDTOMap.get("qizhuangxianzhi2");
            if (qizhuangxianzhi.getAttrValue().equals("是") && qizhuangxianzhi2!=null) {
                ServiceProjectAttrDTO priceunit = serviceProjectAttrDTOMap.get("priceunit");
                if (priceunit != null) {
                    reminders.add(qizhuangxianzhi2.getAttrValue() + priceunit.getAttrValue() + "起装");
                }
            }
        }
        if (serviceProjectAttrDTOMap.containsKey("zhichiyufujinketui")) {
            ServiceProjectAttrDTO zhichiyufujinketui = serviceProjectAttrDTOMap.get("zhichiyufujinketui");
            if (zhichiyufujinketui.getAttrValue().equals("是")) {
                reminders.add("上门后不满意预付金可退");
            }
        }
        Optional.ofNullable(ctx.getDealGroupDTO())
                .map(DealGroupDTO::getRule)
                .map(DealGroupRuleDTO::getUseRule)
                .map(DealGroupUseRuleDTO::getReceiptEffectiveDate).ifPresent(receiptEffectiveDateDTO -> reminders.add(receiptEffectiveDateDTO.getShowText()));
        return reminders.stream().filter(reminder -> reminder != null && !reminder.isEmpty()).collect(Collectors.toList());
    }

    private static Map<String, ServiceProjectAttrDTO> getStringServiceProjectAttrDTOMap(DealCtx ctx) {
        return Optional.ofNullable(ctx.getDealGroupDTO().getServiceProject())
                .map(DealGroupServiceProjectDTO::getMustGroups)
                .filter(mustGroups -> !mustGroups.isEmpty())
                .map(mustGroups -> mustGroups.get(0))
                .map(MustServiceProjectGroupDTO::getGroups)
                .filter(groups -> !groups.isEmpty())
                .map(groups -> groups.get(0))
                .map(ServiceProjectDTO::getAttrs)
                .filter(attrs -> !attrs.isEmpty())
                .map(attrs -> attrs.stream().collect(Collectors.toMap(ServiceProjectAttrDTO::getAttrName, p -> p)))
                .orElse(Collections.emptyMap());
    }


}
