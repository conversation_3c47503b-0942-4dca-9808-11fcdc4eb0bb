package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ResultList;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2024/10/24
 */
@TypeDoc(description = "团详页关联推荐模块")
@Data
@MobileDo(id = 0xc775)
public class RelatedRecommendVO extends ResultList {
    @FieldDoc(description = "推荐模块信息")
    @MobileDo.MobileField(key = 0x56a6)
    private RelatedRecommendModule recommendModule;

    @FieldDoc(description = "关联项目列表")
    @MobileDo.MobileField(key = 0x8f00)
    private List<RecommendItemDTO> recommendItemList;

    @FieldDoc(description = "模块AB配置")
    @MobileDo.MobileField(key = 0xbae3)
    private List<ModuleAbConfig> moduleAbConfigs;
}
