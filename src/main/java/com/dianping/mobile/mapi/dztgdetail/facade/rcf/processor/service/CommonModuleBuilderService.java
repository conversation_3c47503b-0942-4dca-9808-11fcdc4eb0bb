package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.alibaba.fastjson.JSON;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.Pair;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.InventoryModuleVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.ProductDetailModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.StructDetailVO;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.google.common.collect.Lists;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericModuleResponse;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericProductDetailPageResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Deprecated
@Slf4j
@Component
public class CommonModuleBuilderService {

    public ProductDetailModule build(DealCtx ctx) {
        try {
            GenericProductDetailPageResponse response = ctx.getCommonModuleResponse();
            if (Objects.isNull(response) || MapUtils.isEmpty(response.getModuleResponse())) {
                return null;
            }
            ProductDetailModule productDetailModule = new ProductDetailModule();
            Map<String, GenericModuleResponse> moduleResponse = response.getModuleResponse();
            // 结构化详情
            GenericModuleResponse structuredDetailResponse = moduleResponse.get("module_detail_structured_detail");
            if (Objects.nonNull(structuredDetailResponse) && Objects.nonNull(structuredDetailResponse.getModuleVO())) {
                StructDetailVO structDetailVO = JSON.parseObject(JSON.toJSONString(structuredDetailResponse.getModuleVO()), StructDetailVO.class);
                productDetailModule.setStructDetail(Optional.ofNullable(structDetailVO).map(StructDetailVO::getDealDetails).orElse(Lists.newArrayList()));
            }
            // 商品分类和属性信息模型
            GenericModuleResponse inventoryResponse = moduleResponse.get("module_detail_inventory_module");
            if (Objects.nonNull(inventoryResponse) && Objects.nonNull(inventoryResponse.getModuleVO())) {
                InventoryModuleVO inventoryModuleVO = JSON.parseObject(JSON.toJSONString(inventoryResponse.getModuleVO()), InventoryModuleVO.class);
                productDetailModule.setDealInventory(Optional.ofNullable(inventoryModuleVO).map(InventoryModuleVO::getInventoryDetails).orElse(
                        Lists.newArrayList()));
            }
            return productDetailModule;
        } catch (Exception e) {
            log.error("build common module error, ctx:{}", ctx, e);
        }
        return null;
    }

    public List<Pair> buildRichText(DealCtx ctx) {
        boolean hitCpvCategoryId = LionConfigUtils.isHitCpvCategoryId(ctx.getCategoryId());
        List<Pair> structuredDetails = ctx.getStructedDetails();
        if (CollectionUtils.isEmpty(structuredDetails) || !hitCpvCategoryId) {
            return structuredDetails;
        }

        Pair targetPair = structuredDetails.stream().filter(Objects::nonNull)
                .filter(pair -> StringUtils.equals("套餐", pair.getId()) || StringUtils.equals("套餐", pair.getID()) || StringUtils.equals("套餐", pair.getKey()))
                .findFirst().orElse(null);

        String richText = Optional.ofNullable(buildInventoryModuleVO(ctx)).map(InventoryModuleVO::getRichText).orElse(null);
        if (StringUtils.isBlank(richText)) {
            return structuredDetails;
        }

        if (Objects.isNull(targetPair)) {
            targetPair = new Pair();
            targetPair.setName(richText);
            targetPair.setID("套餐");
            targetPair.setId("套餐");
            targetPair.setKey("套餐");
            targetPair.setType(1);
            structuredDetails.add(targetPair);
            return structuredDetails;
        }

        targetPair.setName(richText);

        return structuredDetails;
    }

    public InventoryModuleVO buildInventoryModuleVO(DealCtx ctx) {
        try {
            GenericProductDetailPageResponse response = ctx.getCommonModuleResponse();
            if (Objects.isNull(response) || MapUtils.isEmpty(response.getModuleResponse())) {
                return null;
            }
            Map<String, GenericModuleResponse> moduleResponse = response.getModuleResponse();
            GenericModuleResponse inventoryResponse = moduleResponse.get("module_detail_inventory_module");
            if (Objects.isNull(inventoryResponse) || Objects.isNull(inventoryResponse.getModuleVO())) {
                return null;
            }
            return JSON.parseObject(JSON.toJSONString(inventoryResponse.getModuleVO()), InventoryModuleVO.class);
        } catch (Exception e) {
            log.error("build common module error, ctx:{}", ctx, e);
        }
        return null;
    }
}