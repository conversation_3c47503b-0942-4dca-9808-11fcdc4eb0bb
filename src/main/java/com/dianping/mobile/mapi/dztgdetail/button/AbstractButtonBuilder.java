package com.dianping.mobile.mapi.dztgdetail.button;

import com.dianping.cat.Cat;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.SaleStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.SkuCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.PriceHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Map;

import static com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper.buildPromotionChannel;

@Slf4j
public abstract class AbstractButtonBuilder implements ButtonBuilder {

    protected BuilderConfig config;

    protected static final String DEFAULT_BUTTON_NAME = "立即抢购";
    public static final String YOU_HUI_MA_MINI_CHANNEL = "youhuimaMini";
    protected static final String DEFAULT_PRE_ORDER_BUTTON_NAME = "立即预订";
    protected static final String PROMO_PRE_ORDER_BUTTON_NAME = "优惠订";

    @Override
    public void init(BuilderConfig config) {
        this.config = config;
    }

    public void build(DealCtx context, ButtonBuilderChain chain) {

        // button数量已经达到上限，不构建
        if (context.getButtonCount() >= config.getMaxButtonSize()) {
            chain.build(context);
            return;
        }

        // 与所有类型互斥并且已经存在其他按钮，不构建
        if (config.isExclusiveAll() && context.getPreButton() != null) {
            chain.build(context);
            return;
        }

        // 与已存在按钮的类型互斥，不构建
        if (isExclusive(context)) {
            chain.build(context);
            return;
        }

        // 如果和前一个按钮可以共存，构建
        if (!config.isInclusiveAll() && !isInclusive(context)) {
            chain.build(context);
            return;
        }

        // 新老客状态不匹配
        if (config.getBuildOnNewUser() != null
                && config.getBuildOnNewUser() != context.getPriceContext().isNewUser()) {
            chain.build(context);
            return;
        }

        doBuild(context, chain);
    }

    /**
     * 和前一个按钮可以共存
     *
     * @param context context
     * @return boolean
     */
    private boolean isInclusive(DealCtx context) {
        DealBuyBtn preButton = context.getPreButton();
        if (preButton == null) {
            return true;
        }
        if (CollectionUtils.isEmpty(config.getInclusiveType())) {
            return true;
        }

        for (ButtonStateConfig statusConfig : config.getInclusiveType()) {
            if (buttonStatusMatch(preButton, statusConfig)) {
                return true;
            }
        }
        return false;
    }

    private boolean buttonStatusMatch(DealBuyBtn preButton, ButtonStateConfig statusConfig) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder.buttonStatusMatch(DealBuyBtn,ButtonStateConfig)");
        if (statusConfig.getButtonType().getCode() != preButton.getDetailBuyType()) {
            return false;
        }
        if (CollectionUtils.isEmpty(statusConfig.getButtonStates())) {
            return false;
        }
        return statusConfig.getButtonStates().contains(ButtonStateEnum.ANY) ||
                statusConfig.getButtonStates().contains(preButton.getState());
    }

    private boolean isExclusive(DealCtx context) {

        DealBuyBtn preButton = context.getPreButton();

        if (CollectionUtils.isEmpty(config.getExclusiveType())
                || preButton == null
                || preButton.getState() == null) {
            return false;
        }

        for (ButtonStateConfig statusConfig : config.getExclusiveType()) {
            if (buttonStatusMatch(preButton, statusConfig)) {
                return true;
            }
        }
        return false;
    }

    protected boolean stateMatch(DealCtx context) {
        if (CollectionUtils.isNotEmpty(config.getSkipOnStatues())
                && config.getSkipOnStatues().contains(ButtonStateEnum.HOLD)
                && isHoldState(context)) {
            return false;
        }

        if (CollectionUtils.isNotEmpty(config.getBuildOnStatues())
                && config.getBuildOnStatues().contains(ButtonStateEnum.HOLD)
                && !isHoldState(context)) {
            return false;
        }

        return true;
    }

    protected boolean isHoldState(DealCtx context) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder.isHoldState(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        return false;
    }

    protected DealBuyBtn buildOriginButton(DealCtx ctx, String buttonName) {
        DealGroupBaseDTO dealGroupBase = ctx.getDealGroupBase();
        boolean isApp = ctx.getEnvCtx().isMainApp();

        DealBuyBtn buyBtn = new DealBuyBtn(true, buttonName);
        buyBtn.setPriceStr(PriceHelper.dropLastZero(dealGroupBase.getDealGroupPrice()));
        if (ctx.isMarketPriceHided()) {
            buyBtn.setBtnDesc(StringUtils.EMPTY);
        } else {
            buyBtn.setBtnDesc(DealBuyHelper.getUnUsedPriceJlDesc("门市价 ￥",
                    dealGroupBase.getMarketPrice(), isApp, true));
        }
        buyBtn.setDetailBuyType(BuyBtnTypeEnum.NORMAL_DEAL.getCode());
        String buyUrl = UrlHelper.getCommonBuyUrl(ctx, ctx.getMtCityId(), buyBtn.getPriceStr());
        buyUrl = putPromotionChannelParamIfNeeded(ctx, buyUrl);
        buyUrl = addMultiSkuParams(ctx, buyUrl);
        buyUrl = UrlHelper.addPriceCipher(ctx,buyUrl);
        buyBtn.setRedirectUrl(buyUrl);
        return buyBtn;
    }

    private String addMultiSkuParams(DealCtx ctx, String buyUrl) {
        if (ctx.getSkuCtx() == null) {
            return buyUrl;
        }

        SkuCtx skuCtx = ctx.getSkuCtx();

        // 设置是否多sku参数
        buyUrl = String.format("%s&%s=%s", buyUrl, "is_sku", skuCtx.isMultiSku() ? "1" : "0");

        // 设置提单链接服务参数
        if (StringUtils.isNotBlank(skuCtx.getCreatOrderExpId())) {
            buyUrl = String.format("%s&%s=%s", buyUrl, "expid", skuCtx.getCreatOrderExpId());
        }

        // 设置团详实验参数
        if (skuCtx.getDealCreatOrderAbConfig() != null && CollectionUtils.isNotEmpty(skuCtx.getDealCreatOrderAbConfig().getConfigs())) {
            try {
                buyUrl = String.format("%s&%s=%s", buyUrl, "expBiInfo", URLEncoder.encode(skuCtx.getDealCreatOrderAbConfig().getConfigs().get(0).getExpBiInfo(), StandardCharsets.UTF_8.name()));
            } catch (Exception e) {
                log.warn("expBiInfo={} encode failed", skuCtx.getDealCreatOrderAbConfig().getConfigs().get(0).getExpBiInfo(), e);
            }
        }
        return buyUrl;
    }

    private String putPromotionChannelParamIfNeeded(DealCtx ctx, String buyUrl) {
        if (StringUtils.isEmpty(buyUrl)) {
            return buyUrl;
        }
        // 渠道专属立减 提单页链接增加参数
        Map<String, String> pageSource2OrderPromotionChannel = Lion.getMap(LionConstants.APP_KEY, LionConstants.PAGESOURCE_TO_ORDER_PROMOTIONChANNEL, String.class, Collections.emptyMap());
        String promotionChannel = buildPromotionChannel(ctx, pageSource2OrderPromotionChannel);
        if (ctx.getPriceContext().isHasExclusiveDeduction()) {
            // 默认是1
            if (StringUtils.isBlank(promotionChannel)) {
                promotionChannel = "1";
            }
            return buyUrl + "&promotionchannel=" + promotionChannel;
        } else if (StringUtils.isNotBlank(promotionChannel)) {
            return buyUrl + "&promotionchannel=" + promotionChannel;
        }
        return buyUrl;
    }

    /**
     * 有优惠的时候按钮描述为"团购价"
     *
     * @param context context
     * @return desc
     */
    protected String buildHadPromoDesc(DealCtx context) {
        return DealBuyHelper.getUnUsedPriceJlDesc(
                "团购价 ￥",
                context.getDealGroupBase().getDealGroupPrice(),
                context.getEnvCtx().isMainApp(),
                true);
    }

    /**
     * 按钮描述为"门市价"
     *
     * @param context context
     * @return desc
     */
    protected String buildMarketPriceDesc(DealCtx context) {
        return DealBuyHelper.getUnUsedPriceJlDescWithStrikethrough(
                "门市价 ￥",
                context.getDealGroupBase().getMarketPrice(),
                context.getEnvCtx().isMainApp(),
                true);
    }

    /**
     * 展示价格取一位小数，向上取整
     *
     * @param price 价格
     * @return 格式化的价格
     */
    protected String formatPrice(BigDecimal price) {
        return price.setScale(2, RoundingMode.CEILING)
                .stripTrailingZeros()
                .toPlainString();
    }

    /**
     * 展示折扣取一位小数，向上取整
     *
     * @param discount 折扣
     * @return 格式化的折扣
     */
    protected String formatDiscount(BigDecimal discount) {
        return discount.setScale(1, RoundingMode.CEILING)
                .stripTrailingZeros()
                .toPlainString();
    }

    protected abstract void doBuild(DealCtx context, ButtonBuilderChain chain);

    protected String getButtonTitle(DealCtx ctx, String title) {
        // 是否为预订单，普通团单直接返回标题
        boolean needPreOrder = DealCtxHelper.isPreOrderDeal(ctx);
        return needPreOrder ? DEFAULT_PRE_ORDER_BUTTON_NAME : title;
    }

    protected String getPromoButtonTitle(DealCtx ctx, String title) {
        // 是否为预订单，普通团单直接返回标题
        boolean needPreOrder = DealCtxHelper.isPreOrderDeal(ctx);
        return needPreOrder ? PROMO_PRE_ORDER_BUTTON_NAME : title;
    }

    protected String getSaleStatusButtonTitle(DealCtx ctx) {
        // 是否为预订单
        boolean needPreOrder = DealCtxHelper.isPreOrderDeal(ctx);
        return needPreOrder ? SaleStatusEnum.getOrderDesc(ctx.getSaleStatus()) : SaleStatusEnum.getDesc(ctx.getSaleStatus());
    }

}
