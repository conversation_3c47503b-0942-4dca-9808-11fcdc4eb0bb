package com.dianping.mobile.mapi.dztgdetail.entity;

import lombok.Data;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2024-09-03
 * @description: 留资型bar配置
 */
@Data
public class LeadsDealBarConfig {
    /**
     * 预约按钮标题
     */
    private String resvBtnTitle;

    /**
     * 超值特惠预约按钮标题
     */
    private String specialValueResvBtnTitle;

    /**
     * 电话预约按钮标题
     */
    private String phoneResvBtnTitle;

    /**
     * 不可预约按钮标题
     */
    private String emptyResvBtnTitle;

    /**
     * 购买按钮标题
     */
    private String buyBtnTitle;

    /**
     * 美团App跳转前缀
     */
    private String mtAppJumpPrefix;

    /**
     * 点评App跳链前缀
     */
    private String dpAppJumpPrefix;

    /**
     * 预约跳链前缀
     */
    private String resvJumpUrlPrefix;

    /**
     * 预约跳链后缀
     */
    private String resvJumpUrlSuffix;

    /**
     * 新版预约礼文案模板
     */
    private String newLeadsGiftsTemplate;

    /**
     * 旧版预约礼文案模板
     */
    private String oldLeadsGiftsTemplate;

    /**
     * 预约礼banner图标
     */
    private String bannerIcon;

    /**
     * 预约礼banner文案大小
     */
    private Integer bannerTextSize;

    /**
     * 预约礼banner文案颜色
     */
    private String bannerTextColor;

    /**
     * banner背景色
     */
    private String bannerBackGroundColor;

    /**
     * 超值特惠预约弹窗标题
     */
    private String specialValueResvPopTitle;
}
