package com.dianping.mobile.mapi.dztgdetail.datatype.context;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealNoticeLayerReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealnotice.DealNoticeLayerPBO;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2024/10/31 16:13
 */
@Data
public class DealNoticeLayerCtx {

    public DealNoticeLayerCtx(EnvCtx ctx) {
        if (ctx != null) {
            this.envCtx = ctx;
        }
    }

    private long poiid;
    private long dealGroupId;
    private Double userLng;
    private Double userLat;

    private EnvCtx envCtx = new EnvCtx();
    private DealNoticeLayerReq request ;
    private DealGroupDTO dealGroupDTO;


    private DealNoticeLayerPBO dealNoticeLayerPBO;
}
