package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.cat.Cat;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.detail.dto.DealGroupDetailDTO;
import com.dianping.deal.detail.dto.DealGroupTemplateDetailDTO;
import com.dianping.mobile.mapi.dztgdetail.common.enums.FreeDealEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MtDetailProductType;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.Pair;
import com.dianping.mobile.mapi.dztgdetail.util.FitnessCrossUtils;
import com.dianping.mobile.mapi.dztgdetail.util.ReassuredRepairUtil;
import com.dianping.mobile.mapi.dztgdetail.util.TimesDealUtil;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 类型（团购详情，特别提示，其他）
 * DESCRIPTION(1,"团购详情"),
 * SPECIAL_REMINDER(2,"特别提示"),
 * USAGE_FLOW(3,"使用流程"),
 * RETURN_EXCHANGE_NOTICE(4,"退换货须知"),
 * PRODUCT_DESCRIPTION(5,"产品介绍"),
 * SHOP_DESCRIPTION(6,"商户介绍"),
 * PROCESS_TEAM(7,"制作团队"),
 * EDITOR_COMMENT(8,"小编说"),
 * CORPORATION_INFO(9,"公司信息"),
 * REFINED_DESCRIPTION(10,"无表格团购详情"),
 * SERVICE_FLOW(11,"服务流程"),
 * SHOP_PROVIDES(12,"商家服务"),
 * OTHERS(100,"其他");
 * >= 1000 自定义类型
 */
@Slf4j
@Data
public final class MtDealDetailBuilder {
    public static final int TYPE_NOTICE = 0;                      //通知
    public static final int TYPE_DESCRIPTION = 1;                 //团购详情
    public static final int TYPE_SPECIAL_REMINDER = 2;            //购买须知
    public static final int TYPE_USAGE_FLOW = 3;
    public static final int TYPE_PRODUCT_DESCRIPTION = 5;         //产品信息
    public static final int TYPE_SHOP_DESCRIPTION = 6;            //商户介绍
    public static final int TYPE_SERVICE_FLOW = 11;
    public static final int TYPE_SHOP_PROVIDES = 12;
    public static final int TYPE_RESERVATION = 40;
    public static final int TYPE_PREPAIDCARD_HELP = 50;
    public static final int TYPE_OTHERS = 100;
    public static final int TYPE_MOREDETAILS = 1000;
    public static final int TYPE_PROMOTION = 1001;
    public static final int TYPE_RECOMMEND_CUISINE = 1002;
    //预约须知
    public static final int TYPE_RESV_NEED_KNOW = 1003;
    //疫苗详情
    public static final int TYPE_VACCINE_DETAIL = 1004;


    public static final String DETAIL_KEY = "套餐";

    private DealGroupDetailDTO dealGroupDetailDto;
    private DealGroupBaseDTO dealGroupBaseDto;
    private boolean enableStructuredDetails;
    /**
     * 给新版本原始数据集(HTML)
     */
    private Map<Integer, Pair> map;

    /**
     * 附加信息
     */
    private Map<Integer, Pair> additionalMap = Maps.newTreeMap();
    //新版更多团购详情
    private List<Pair> newMoreDetails = Lists.newArrayList();

    private String voucherLimit;

    public MtDealDetailBuilder() {
    }


    public static MtDealDetailBuilder build(DealGroupBaseDTO dealGroupBaseDto, DealGroupDetailDTO dealGroupDetailDto, boolean enableStructuredDetails) {
        Preconditions.checkNotNull(dealGroupBaseDto);
        Preconditions.checkNotNull(dealGroupDetailDto);

//        DealGroupDetailTransferUtil.procDetail(dealGroupDto);
        MtDealDetailBuilder instance = new MtDealDetailBuilder();
        instance.dealGroupBaseDto = dealGroupBaseDto;
        instance.dealGroupDetailDto = dealGroupDetailDto;
        instance.enableStructuredDetails = enableStructuredDetails;
        return instance;
    }

    public static MtDealDetailBuilder build_2(DealGroupBaseDTO dealGroupBaseDto, DealGroupDetailDTO dealGroupDetailDto, boolean enableStructuredDetails, String voucherLimit) {
        MtDealDetailBuilder build = build(dealGroupBaseDto, dealGroupDetailDto, enableStructuredDetails);
        build.voucherLimit = voucherLimit;
        return build;
    }

    private void addToMap(int type, String title, String html) {
        if (StringUtils.isNotBlank(html)) {
            map.put(type, Pair.of(title, resizeImages(html), type));
        }
    }

    public void addToMapForTimesDeal(int type, String originalTitle, String newTitle, String html) {
        if (StringUtils.isNotBlank(html)) {
            Pair pair = new Pair(originalTitle, newTitle, html, type, originalTitle);
            map.put(type, pair);
        }
    }

    private void addToNewMoreDetails(int type, String title, String html) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.helper.MtDealDetailBuilder.addToNewMoreDetails(int,java.lang.String,java.lang.String)");
        if (StringUtils.isNotBlank(html)) {
            newMoreDetails.add(Pair.of(title, resizeImages(html), type));
        }
    }

    public void initMap(DealCtx dealCtx) {
        if (map == null) {
            map = Maps.newTreeMap();
            if (hasText(dealGroupDetailDto.getImportantPoint())) {
                //addToMap(TYPE_NOTICE, "通知", dealGroupDetailDto.getImportantPoint());
            }
            //TODO remove debug info
            if (enableStructuredDetails
                    && dealGroupDetailDto != null
                    && dealGroupDetailDto.getTemplateDetailDTOs() != null) {
                for (DealGroupTemplateDetailDTO dto : dealGroupDetailDto.getTemplateDetailDTOs()) {
//                    if (dto.getType() == TYPE_PRODUCT_DESCRIPTION) { // 为了不让type == 5的详情 覆盖 Deal`GroupDetailTransferUtil工具类中fillDetail方法填充的产品信息
//                        continue;
                    if (dealGroupBaseDto.getDealGroupType() == MtDetailProductType.PRODUCT_TYPE_TRIPTICKET && StringUtils.equals(dto.getTitle(), "在线预约流程")) {
                        addToMap(TYPE_USAGE_FLOW, "使用流程", dto.getContent());
                    } else if (StringUtils.equals(dto.getTitle(), "使用流程")) {
                        addToMap(TYPE_USAGE_FLOW, "使用流程", dto.getContent());
                    } else if (TYPE_OTHERS == dto.getType()) {
                        addToNewMoreDetails(TYPE_OTHERS, dto.getTitle(), dto.getContent());
                    } else {
                        if (dto.getTitle().equals("团购详情")) {
                            // 多次卡
                            if (TimesDealUtil.isMultiTimesCard(dealCtx) && !FitnessCrossUtils.isFitnessCrossDeal(dealCtx)) {
                                try {
                                    String multiTimesCardTitle = TimesDealUtil.getMultiTimesCardTitle(dealCtx);
                                    dto.setContent(TimesDealUtil.getTimesTitleToHtmlIfNecessary(dto.getContent(), dealCtx));
                                    addToMapForTimesDeal(dto.getType(), DETAIL_KEY, StringUtils.isEmpty(multiTimesCardTitle) ? DETAIL_KEY : multiTimesCardTitle, dto.getContent());
                                } catch (Exception e) {
                                    // 异常兜底，走非多次卡团详方法
                                    log.error("MtDealDetailBuilder.initMap fail, e is ", e);
                                    addToMap(dto.getType(), DETAIL_KEY, dto.getContent());
                                }
                            } else if (FitnessCrossUtils.isFitnessCrossDeal(dealCtx)) {
                                // 健身通团详
                                try{
                                    addToMap(dto.getType(), DETAIL_KEY, FitnessCrossUtils.buildDealBaseContent(dto.getContent()));
                                } catch (Exception e) {
                                    // 异常兜底
                                    log.error("MtDealDetailBuilder.initMap fail, dpDealId is [{}], e is ", dealCtx.getDpDealId(), e);
                                    addToMap(dto.getType(), DETAIL_KEY, dto.getContent());
                                }
                            } else {
                                // 其他case
                                addToMap(dto.getType(), DETAIL_KEY, dto.getContent());
                            }
                        } else {
                            addToMap(dto.getType(), dto.getTitle(), dto.getContent());
                        }
                    }
                }
            }
            // 由于老上单系统的单子仍然在线，所以当新数据（templateDetailDTO）不存在的时候，需要兼容旧数据
            if (!map.containsKey(TYPE_DESCRIPTION)) {
                // 多次卡
                if (TimesDealUtil.isMultiTimesCard(dealCtx)) {
                    try {
                        String multiTimesCardTitle = TimesDealUtil.getMultiTimesCardTitle(dealCtx);
                        addToMapForTimesDeal(TYPE_DESCRIPTION, DETAIL_KEY, StringUtils.isEmpty(multiTimesCardTitle) ? DETAIL_KEY : multiTimesCardTitle, TimesDealUtil.getTimesTitleToHtmlIfNecessary(dealGroupDetailDto.getInfo(), dealCtx));
                    } catch (Exception e) {
                        // 异常兜底，走非多次卡团详方法
                        log.error("MtDealDetailBuilder.initMap fail, e is ", e);
                        addToMap(TYPE_DESCRIPTION, DETAIL_KEY, dealGroupDetailDto.getInfo());
                    }
                } else {
                    addToMap(TYPE_DESCRIPTION, DETAIL_KEY, dealGroupDetailDto.getInfo());
                }
            }
            if (!map.containsKey(TYPE_SPECIAL_REMINDER)) {
                addToMap(TYPE_SPECIAL_REMINDER, "购买须知", dealGroupDetailDto.getSpecialPoint());
            }
            if (!map.containsKey(TYPE_PRODUCT_DESCRIPTION)) {
                addToMap(TYPE_PRODUCT_DESCRIPTION, "产品信息", dealGroupDetailDto.getProductInfo());
            }
            if (!map.containsKey(TYPE_SHOP_DESCRIPTION)) {
                addToMap(TYPE_SHOP_DESCRIPTION, "商户介绍", dealGroupDetailDto.getShopInfo());
            }

            // 如果使用结构化购买须知且RequestUnStructuredContent=false，则不返回非结构化购买须知信息
            removeToMap(dealCtx);
        }
    }

    public void removeToMap(DealCtx dealCtx) {
        if (Objects.nonNull(dealCtx.getPNSConfig()) && !dealCtx.getPNSConfig().isRequestUnStructuredContent() && map.containsKey(TYPE_SPECIAL_REMINDER)) {
            map.remove(TYPE_SPECIAL_REMINDER);
        }
    }

    private String getPrepaidCardHelpHtml() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.helper.MtDealDetailBuilder.getPrepaidCardHelpHtml()");
        return "- 打开大众点评客户端，找到\"我的\"-\"卡包\"<br />- 打开"
                + dealGroupBaseDto.getDealGroupShortTitle()
                + "电子储值卡<br />- 结账时向服务员出示此卡<br />- 服务员进行验证、扣款";
    }

    /**
     * hasStructedDetail ==true的情况，不能添加“套餐”信息
     *
     * @return 结构化团购详情
     */
    public List<Pair> toStructedDetails(DealCtx dealCtx, boolean hasStructedDetail) {
        initMap(dealCtx);

        List<Pair> list = Lists.newArrayList();
        List<String> moreDetails = Lists.newArrayList();
        //更多详情页面 产品介绍和商户介绍提前
        if (map.containsKey(TYPE_PRODUCT_DESCRIPTION)) {
            moreDetails.add(toEmbeddedDetail(map.get(TYPE_PRODUCT_DESCRIPTION)));
        }
        if (map.containsKey(TYPE_SHOP_DESCRIPTION)) {
            moreDetails.add(toEmbeddedDetail(map.get(TYPE_SHOP_DESCRIPTION)));
        }

        // 由于newMoreDetails里面的内容同TYPE_PRODUCT_DESCRIPTION中的内容重复，注释此代码
        // 又：由于不再使用productInfo，故解除注释
        for (Pair pair : newMoreDetails) {
            moreDetails.add(toEmbeddedDetail(pair));
        }

        moreDetails.add(getSpecialReminderBarHeader());

        // TODO
        if (additionalMap.containsKey(TYPE_RESERVATION)) {
            moreDetails.add(additionalMap.get(TYPE_RESERVATION).getName());
            list.add(wrapDiv(removeHtmlImg(additionalMap.get(TYPE_RESERVATION))));
        }
        if (map.containsKey(TYPE_DESCRIPTION) && !hasStructedDetail && !(dealCtx.isFreeDeal() && dealCtx.getFreeDealType() != null)) {
            moreDetails.add(map.get(TYPE_DESCRIPTION).getName());
            if (additionalMap.containsKey(TYPE_RECOMMEND_CUISINE)) {
                Pair pair = removeHtmlImg(map.get(TYPE_DESCRIPTION));
                if (pair != null) {
                    pair.setName(pair.getName() + additionalMap.get(TYPE_RECOMMEND_CUISINE).getName());
                    list.add(wrapDiv(pair));
                }
            } else {
                Pair p = map.get(TYPE_DESCRIPTION);
                Pair p1 = removeHtmlImg(p);
                Pair p2 = wrapDiv(p1);
                if (StringUtils.isNotBlank(voucherLimit)) {
                    p2.setName(voucherLimit + p2.getName());
                }
                list.add(p2);
            }
        }
        if (map.containsKey(TYPE_SPECIAL_REMINDER)) {
            moreDetails.add(map.get(TYPE_SPECIAL_REMINDER).getName());
            Pair pair = wrapDiv(removeHtmlImg(map.get(TYPE_SPECIAL_REMINDER)));
            // 购买须知概要
            pair.setSubTitle(ReminderHelper.getReminderSummary(dealCtx));
            if (dealCtx.isFreeDeal() && dealCtx.getFreeDealType() != null) {
                if (dealCtx.getFreeDealType().equals(FreeDealEnum.HOME_DESIGN_BOOKING) || dealCtx.getFreeDealType().equals(FreeDealEnum.LIFE_HOUSEKEEPING_BOOKING)|| dealCtx.getFreeDealType().equals(FreeDealEnum.RECYCLE)) {
                    pair.setID("领取须知");
                    pair.setKey("领取须知");
                    if (CollectionUtils.isNotEmpty(dealCtx.getDealGroupDTO().getAttrs())) {
                        dealCtx.getDealGroupDTO().getAttrs().stream()
                                .filter(a -> "free_product_notice".equals(a.getName())).findFirst()
                                .ifPresent(attrDTO -> pair.setName(StringUtils.join(attrDTO.getValue(), "。")));
                    }
                    pair.setSubTitle("");
                } else if (dealCtx.getFreeDealType().equals(FreeDealEnum.EDU_TRIAL_BOOKING)) {
                    pair.setID("报名须知");
                    pair.setKey("报名须知");
                }
            }
            processDealGroupDetails(dealCtx,pair);
            list.add(pair);
        }
        // TODO
        if (map.containsKey(TYPE_NOTICE)) {
            //2016-05-29 团详页面调整“通知”位置
            //2016-06-07 更对详情页面不放“通知”模块
            //moreDetails.add(map.get(TYPE_NOTICE).getName());
            list.add(wrapDiv(removeHtmlImg(map.get(TYPE_NOTICE))));
        }
        if (map.containsKey(TYPE_USAGE_FLOW)) {
            list.add(wrapDiv(removeHtmlImg(map.get(TYPE_USAGE_FLOW))));
        }


        if (dealGroupBaseDto.getDealGroupType() == MtDetailProductType.PRODUCT_TYPE_PREPAIDCARD) {
            list.add(Pair.of("使用帮助", wrapDiv(getPrepaidCardHelpHtml()), TYPE_PREPAIDCARD_HELP));
        }
        moreDetails.add(getSpecialReminderBarTail());

        // moreDetail信息下线灰度策略和结构化购买须知灰度策略保持一致（已和前端确认，该字段废弃）
        if (moreDetails.size() > 0 && !dealCtx.isHitStructuredPurchaseNote()) {
            list.add(Pair.of("更多详情", wrapDiv(StringUtils.join(moreDetails, '\n')), TYPE_MOREDETAILS));
        }
        if(ReassuredRepairUtil.isTagPresent(dealCtx)){
            list = list.stream().filter(x -> !"套餐".equals(x.getId())).collect(Collectors.toList());
        }
        return list;
    }

    private void processDealGroupDetails(DealCtx ctx, Pair pair) {
        try {
            if (ReassuredRepairUtil.isTagPresent(ctx)) {
                String subTitle = pair.getName();
                String preDlContent = extractPreDlContent(subTitle);
                String postDlContent = extractPostDlContent(subTitle);

                String updatedContent = preDlContent + buildRestrictionsContent(ctx) + postDlContent;
                pair.setName(updatedContent);
            }
            if (DealAttrHelper.isWuyoutong(ctx)) {
                String subTitle = Optional.ofNullable(pair).map(Pair::getName).orElse("");
                int ruleReminderStart = subTitle.indexOf("<dt>规则提醒</dt>\n");
                if (ruleReminderStart == -1) {
                    // 规则提醒标签不存在, 直接返回
                    return;
                }
                int ruleReminderEnd = ruleReminderStart + "<dt>规则提醒</dt>\n".length();

                String newContent = "<dd>\n" +
                        "    <p class=\"listitem\">如涉及维修配件、复杂堵塞等情况会加收额外费用，收费标准参考团购详情</p>\n" +
                        "</dd>\n";

                String updatedContent = subTitle.substring(0, ruleReminderEnd) + newContent + subTitle.substring(ruleReminderEnd);
                pair.setName(updatedContent);
            }
        } catch (Exception e) {
            log.error("processDealGroupDetails error, ctx={}, pair={}", e, ctx, pair);
        }
    }

    private String buildRestrictionsContent(DealCtx ctx) {
        Map<String, ServiceProjectAttrDTO> attrMap = getStringServiceProjectAttrDTOMap(ctx);
        String content = "";
        if ("是".equals(attrMap.get("qizhuangxianzhi").getAttrValue())) {
            ServiceProjectAttrDTO priceUnitAttr = attrMap.get("priceunit");
            ServiceProjectAttrDTO qizhuangxianzhi2 = attrMap.get("qizhuangxianzhi2");
            if (priceUnitAttr != null && qizhuangxianzhi2 != null) {
                content = buildPriceUnitRestrictionContent(qizhuangxianzhi2.getAttrValue() + priceUnitAttr.getAttrValue());
            }
        }
        if (attrMap.containsKey("zhichiyufujinketui")) {
            ServiceProjectAttrDTO zhichiyufujinketui = attrMap.get("zhichiyufujinketui");
            if (zhichiyufujinketui.getAttrValue().equals("是")) {
                content  +=  "<dl> \n <dt>退款规则</dt> \n <dd>\n " +
                        " <p class=\"listitem\"> \n " + "上门前预付金随时可退，上门后不满意预付金可退\n" + "</p>\n" + "</dd>\n </dl>\n";
            }
        }
        return content;
    }

    private String buildPriceUnitRestrictionContent(String priceUnitValue) {
        return "<dl> \n <dt>适用面积</dt> \n <dd>\n " +
                " <p class=\"listitem\"> \n "+"起做面积" + priceUnitValue +"，若不足起做面积则按" + priceUnitValue + "面积价格计费\n" + "</p>\n" + "</dd>\n </dl>\n";
    }

    private String extractPreDlContent(String subTitle) {
        return subTitle.substring(0, subTitle.indexOf("<dl>"));
    }

    private String extractPostDlContent(String subTitle) {
        return subTitle.substring(subTitle.indexOf("<dl>"));
    }

    private Map<String, ServiceProjectAttrDTO> getStringServiceProjectAttrDTOMap(DealCtx ctx) {
        return Optional.ofNullable(ctx.getDealGroupDTO().getServiceProject())
                .map(DealGroupServiceProjectDTO::getMustGroups)
                .filter(mustGroups -> !mustGroups.isEmpty())
                .map(mustGroups -> mustGroups.get(0))
                .map(MustServiceProjectGroupDTO::getGroups)
                .filter(groups -> !groups.isEmpty())
                .map(groups -> groups.get(0))
                .map(ServiceProjectDTO::getAttrs)
                .filter(attrs -> !attrs.isEmpty())
                .map(attrs -> attrs.stream().collect(Collectors.toMap(ServiceProjectAttrDTO::getAttrName, p -> p)))
                .orElse(Collections.emptyMap());
    }

    /**
     * 新版本团购详情最外层需要一个DIV
     */
    private static String wrapDiv(String html) {
        return "<div>\n" + html + "\n</div>";
    }

    /**
     * 新版本团购详情最外层需要一个DIV
     */
    private static Pair wrapDiv(Pair p) {
        return Pair.of(p.getID(), wrapDiv(p.getName()), p.getType());
    }

    private static Pair removeHtmlImg(Pair p) {
        if (p != null && p.getName() != null) {
            p.setName(MtDealDetailFormatter.removeHtmlImage(p.getName()));
        }
        return p;
    }

    private static String toEmbeddedDetail(Pair p) {
        if (p != null) {
            return "<div class=\"detail-box\"><h3 class=\"tit\">"
                    + StringUtils.defaultString(p.getID())
                    + "</h3>"
                    + StringUtils.defaultString(p.getName())
                    + "</div>";
        }
        return StringUtils.EMPTY;
    }

    //给下沉的详情加上“购买须知文案”
    private static String getSpecialReminderBarHeader() {
        return "<div class=\"detail-box\"><h3 class=\"tit\">购买须知</h3>";
    }

    private static String getSpecialReminderBarTail() {
        return "</div>";
    }


    private static boolean hasText(String html) {
        return !isBlank(MtHtmlCleaner.clean(html));
    }

    private static boolean isBlank(String s) {
        for (int i = 0; i < s.length(); i++) {
            if (!Character.isWhitespace(s.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    private String resizeImages(String html) {
        // (640c400) -> (450x1024)
        // (640x1024) -> (450x1024)
        return StringUtils.replace(StringUtils.replace(html, "(640c400)/thumb.jpg", "(450x1024)/thumb.jpg"), "(640x1024)/thumb.jpg", "(450x1024)/thumb.jpg");
    }

}
