package com.dianping.mobile.mapi.dztgdetail.common.enums;

/**
 * @Author: z<PERSON><PERSON><PERSON><PERSON><PERSON>@meituan.com
 * @Date: 2024/11/13
 */
public enum ItemTextTypeEnum {
    TOP_PICKS(1, "人气精选"),
    AD(2, "广告"),
    SIMILAR_ITEM(3, "相似好价，比出来的好价 >"),
    ;

    private int value;

    private String name;

    ItemTextTypeEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static ItemTextTypeEnum valueOf(int value) {

        for (ItemTextTypeEnum itemTextTypeEnum : values()) {
            if (itemTextTypeEnum.value == value) {
                return itemTextTypeEnum;
            }
        }
        return null;
    }
}
