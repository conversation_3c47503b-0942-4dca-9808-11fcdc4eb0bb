package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.degrade.util.JsonCodec;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.DealDouHuUtil;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.QualityEducationUtil;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBestPromoDetail;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PriceDisplayModuleDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PromoDetailModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.DealGift;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.PromoActivityInfoVO;
import com.dianping.mobile.mapi.dztgdetail.entity.PrepayCategoryConfig;
import com.dianping.mobile.mapi.dztgdetail.helper.PriceHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.PriceProtectionHelper;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.TimesDealUtil;
import com.dianping.mobile.mapi.dztgdetail.util.VersionUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PricePowerTagDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PricePowerTagItem;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.dealuser.price.display.api.enums.PricePowerTagEnum.*;

/**
 * <AUTHOR>
 * @date 2024-07-03
 * @desc 优惠明&券模块
 */
@Slf4j
@Component
public class PromoDetailModuleBuilderService {
    @Resource
    private DouHuService douHuService;

    @Resource
    private DouHuBiz douHuBiz;

    /**
     * 时间价格力标签
     */
    public static final Set<Integer> TIME_PRICE_POWER_TAG_VALUES = Sets.newHashSet(
            LOWEST_PRICE_IN_RECENT_30_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_60_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_90_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_180_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_365_DAYS.getType()
    );

    /**
     * 丽人价格力标签排序
     */
    private static final List<Integer> BEAUTY_PRICE_POWER_TAG_SORT_ORDER = Lists.newArrayList(
            LOWEST_PRICE_IN_RECENT_365_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_180_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_90_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_60_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_30_DAYS.getType(),
            NETWORK_LOW_PRICE.getType()
    );

    /**
     * 足疗价格力排序标签
     */
    private static final List<Integer> FOOT_MASSAGE_PRICE_POWER_TAG_SORT_ORDER = Lists.newArrayList(
            NETWORK_LOW_PRICE.getType(),
            LOWEST_PRICE_IN_RADIUS_3KM_RANGE.getType(),
            LOWEST_PRICE_IN_CITY.getType(),
            LOWEST_PRICE_IN_DISTRICT.getType(),
            LOWEST_PRICE_IN_REGION.getType(),
            LOWEST_PRICE_IN_RECENT_365_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_180_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_90_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_60_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_30_DAYS.getType()
    );

    /**
     * 休娱价格力排序标签
     */
    public static final List<Integer> XIUYU_PRICE_POWER_TAG_SORT_ORDER = Lists.newArrayList(
            NETWORK_LOW_PRICE.getType(),
            LOWEST_PRICE_IN_RECENT_365_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_180_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_90_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_60_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_30_DAYS.getType()
    );

    /**
     * 展示的价格力标签列表
     */
    private static final Set<Integer> SHOW_PRICE_POWER_TAG_VALUES = Sets.newHashSet(
            LOWEST_PRICE_IN_RECENT_30_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_60_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_90_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_180_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_365_DAYS.getType(),
            LOWEST_PRICE_IN_RADIUS_3KM_RANGE.getType(),
            LOWEST_PRICE_IN_CITY.getType(),
            LOWEST_PRICE_IN_DISTRICT.getType(),
            LOWEST_PRICE_IN_REGION.getType(),
            NETWORK_LOW_PRICE.getType()
    );

    public void setPriceStrengthDesc(DealCtx ctx, PromoDetailModule promoDetailModule, PriceDisplayDTO priceDisplayDTO) {
        try {
            PricePowerTagDisplayDTO pricePowerTagDisplayDTO = Optional.ofNullable(priceDisplayDTO)
                    .map(PriceDisplayDTO::getPricePowerTagDisplayDTO).orElse(null);
            if (pricePowerTagDisplayDTO == null || CollectionUtils.isEmpty(pricePowerTagDisplayDTO.getAllTagList())) {
                return;
            }
            List<PricePowerTagItem> allTagList = pricePowerTagDisplayDTO.getAllTagList();
            List<Integer> sortOrder;

            if (isCategoryMatch(ctx.getCategoryId(), Arrays.asList(501, 502, 503, 509, 511, 514))) {
                sortOrder = BEAUTY_PRICE_POWER_TAG_SORT_ORDER;
            }
            // 足疗：增加空间比价标签
            else if (isCategoryMatch(ctx.getCategoryId(), Collections.singletonList(303))) {
                String sk = getSpacePriceTagSk(ctx);
                if (StringUtils.isNotBlank(sk) && sk.contains("e")) {
                    // e组不返回任何比价标签
                    return;
                }
                sortOrder = FOOT_MASSAGE_PRICE_POWER_TAG_SORT_ORDER;
                if (CollectionUtils.isNotEmpty(allTagList)) {
                    allTagList = allTagList.stream().filter(a -> sortOrder.contains(a.getTagType())).collect(Collectors.toList());
                }
            } else {
                sortOrder = XIUYU_PRICE_POWER_TAG_SORT_ORDER;
            }
            sortAndSetPriceStrengthDesc(promoDetailModule, allTagList, sortOrder, ctx);
        } catch (Exception e) {
            log.error("setPriceStrengthDesc error,", e);
        }
    }

    private void sortAndSetPriceStrengthDesc(PromoDetailModule promoDetailModule, List<PricePowerTagItem> allTagList,
                                             List<Integer> sortOrder, DealCtx ctx) {
        if (CollectionUtils.isEmpty(allTagList) || promoDetailModule == null || CollectionUtils.isEmpty(sortOrder)) {
            return;
        }
        allTagList.sort(Comparator.comparingInt(item -> sortOrder.indexOf(item.getTagType())));
        PricePowerTagItem firstTag = allTagList.get(0);

        boolean showPricePowerTag = firstTag != null && SHOW_PRICE_POWER_TAG_VALUES.contains(firstTag.getTagType());
        if (!showPricePowerTag) {
            return;
        }
        promoDetailModule.setPriceStrengthDesc(firstTag.getTagName());
        // 时间比价
        if (TIME_PRICE_POWER_TAG_VALUES.contains(firstTag.getTagType())) {
            boolean showPriceTrend = douHuService.showPriceTrend(ctx);
            promoDetailModule.setShowPriceCompareEntrance(showPriceTrend);
        }
    }

    private boolean isCategoryMatch(int publishCategory, List<Integer> categories) {
        return categories.contains(publishCategory);
    }

    private String getSpacePriceTagSk(DealCtx ctx) {
        String module = ctx.isMt() ? "MTMassageSpacePriceTag" : "DPMassageSpacePriceTag";
        ModuleAbConfig abConfig = douHuBiz.getAbExpResultByUserId(ctx, module);
        List<ModuleAbConfig> moduleAbConfigs = ctx.getModuleAbConfigs();
        if (CollectionUtils.isEmpty(moduleAbConfigs)) {
            moduleAbConfigs = new ArrayList<>();
        }
        moduleAbConfigs.add(abConfig);
        ctx.setModuleAbConfigs(moduleAbConfigs);
        if (Objects.isNull(abConfig) || CollectionUtils.isEmpty(abConfig.getConfigs())) {
            return null;
        }
        return abConfig.getConfigs().get(0).getExpResult();
    }

    public void hideLeadsDealPromoDetailInfo(PromoDetailModule promoDetailModule) {
        if (promoDetailModule == null) {
            return;
        }
        // 优惠价格特殊处理，对第二位进行隐藏
        Set<Integer> posSet = Sets.newHashSet(2);
        promoDetailModule.setDealGroupPrice(PriceHelper.hidePriceWithQuestionMark(promoDetailModule.getDealGroupPrice(), posSet));
        promoDetailModule.setPreSaleDealGroupPrice(PriceHelper.hidePriceWithQuestionMark(promoDetailModule.getPreSaleDealGroupPrice(), posSet));
        promoDetailModule.setPromoPrice(PriceHelper.hidePriceWithQuestionMark(promoDetailModule.getPromoPrice(), posSet));
        promoDetailModule.setNetworkLowestPrice(promoDetailModule.getPromoPrice());
        // 隐藏优惠
        promoDetailModule.setCouponPromo(null);
        promoDetailModule.setReductionPromo(null);
        promoDetailModule.setTotalPromo(null);
        promoDetailModule.setPresalePromo(null);
        promoDetailModule.setPricePostfix(null);
        promoDetailModule.setMarketPricePromo(null);
    }

    public void hideLeadsDealPriceInfo(PriceDisplayModuleDo priceDisplayModuleDo, PriceDisplayDTO normalPrice) {
        if (priceDisplayModuleDo == null) {
            return;
        }
        // 优惠价格特殊处理，对第二位进行隐藏
        Set<Integer> posSet = Sets.newHashSet(2);
        priceDisplayModuleDo.setDealGroupPrice(PriceHelper.hidePriceWithQuestionMark(priceDisplayModuleDo.getDealGroupPrice(), posSet));
        priceDisplayModuleDo.setPrice(PriceHelper.hidePriceWithQuestionMark(priceDisplayModuleDo.getPrice(), posSet));
        if (StringUtils.isNotBlank(priceDisplayModuleDo.getPromoPrice()) && normalPrice != null && normalPrice.getPrice() != null) {
            String promoPrice = PriceHelper.hidePriceWithQuestionMark(NumberFormat.getInstance().format(normalPrice.getPrice()), posSet);
            priceDisplayModuleDo.setPromoPrice("减后价 ￥" + promoPrice);
        }
        // 隐藏优惠
        priceDisplayModuleDo.setPromoTag(null);
    }

    public void hideLeadsDealDisplayPrice(DealGroupPBO result) {
        if (result == null) {
            return;
        }
        Set<Integer> posSet = Sets.newHashSet(2);
        result.setDisplayPrice(PriceHelper.hidePriceWithQuestionMark(result.getDisplayPrice(), posSet));
    }

    public void setBestPriceGuaranteeTag(PromoDetailModule promoDetailModule, DealCtx dealCtx) {
        // 设置买贵必赔
        if (DealDouHuUtil.getCouponAlleviate2ExpResult(dealCtx)
                && PriceProtectionHelper.checkBestPriceGuaranteeInfoValid(dealCtx.getBestPriceGuaranteeInfo())
                && LionConfigUtils.showBestPriceGuaranteeTag()) {
            promoDetailModule.setPriceStrengthDesc("买贵必赔");
        }
    }

    public List<String> buildPromoAbstractList(PromoDetailModule promoDetailModule, DealCtx dealCtx) {
        try {
            if (promoDetailModule == null) {
                return null;
            }
            List<String> promoAbstractListBest = new ArrayList<>();

            boolean couponAlleviate2ExpResult = DealDouHuUtil.getCouponAlleviate2ExpResult(dealCtx);

            // 最佳优惠
            if (CollectionUtils.isNotEmpty(promoDetailModule.getBestPromoDetails())) {
                for (DealBestPromoDetail bestPromoDetail : promoDetailModule.getBestPromoDetails()) {
                    if (bestPromoDetail == null || StringUtils.isBlank(bestPromoDetail.getPromoName())) {
                        continue;
                    }
                    // 优惠减负二期 强化会员渲染
                    if (bestPromoDetail.getPromoName().contains("会员") && couponAlleviate2ExpResult ) {
                        promoAbstractListBest.add(highlightMemberPromoName(bestPromoDetail.getPromoName()));
                    } else {
                        promoAbstractListBest.add(bestPromoDetail.getPromoName());
                    }
                }
            } else if (dealCtx != null && dealCtx.getPriceContext() != null && dealCtx.getPriceContext().getNormalPrice() != null) {
                // 兜底用 normalPrice
                List<PromoDTO> usedPromos = Optional.ofNullable(dealCtx.getPriceContext().getNormalPrice().getUsedPromos()).orElse(Collections.emptyList());
                for (PromoDTO promoDTO : usedPromos) {
                    if (promoDTO.getIdentity() == null || promoDTO.getAmount() == null || StringUtils.isEmpty(promoDTO.getIdentity().getPromoTypeDesc())) {
                        continue;
                    }
                    promoAbstractListBest.add(promoDTO.getIdentity().getPromoTypeDesc());
                }
            }

            final List<String> promoOrder = couponAlleviate2ExpResult
                    ? Arrays.asList("会员", "秒杀", "补贴", "新客", "特惠", "券", "减", "团购优惠", "工作日", "人团", "返")
                    : Arrays.asList("会员", "补贴", "新客", "秒杀", "特惠", "券", "减", "团购优惠", "工作日", "人团", "返");

            // 最佳优惠排序
            List<String> sortedPromoAbstractListBest = promoAbstractListBest.stream()
                    .filter(StringUtils::isNotBlank)
                    .sorted((promo1, promo2) -> {
                        int index1 = promoOrder.stream().filter(promo1::contains).findFirst().map(promoOrder::indexOf).orElse(Integer.MAX_VALUE);
                        int index2 = promoOrder.stream().filter(promo2::contains).findFirst().map(promoOrder::indexOf).orElse(Integer.MAX_VALUE);
                        return Integer.compare(index1, index2);
                    })
                    .collect(Collectors.toList());
            // 活动
            List<String> activities = new ArrayList<>();
            if (promoDetailModule.getPromoActivityList() != null) {
                for (PromoActivityInfoVO activityInfoVO : promoDetailModule.getPromoActivityList()) {
                    if (activityInfoVO == null || StringUtils.isBlank(activityInfoVO.getShortText())) {
                        continue;
                    }
                    activities.add(activityInfoVO.getShortText());
                }
            }

            // 活动排序
            List<String> sortedActivities = activities.stream()
                    .filter(StringUtils::isNotBlank)
                    .sorted((promo1, promo2) -> {
                        int index1 = promoOrder.stream().filter(promo1::contains).findFirst().map(promoOrder::indexOf).orElse(Integer.MAX_VALUE);
                        int index2 = promoOrder.stream().filter(promo2::contains).findFirst().map(promoOrder::indexOf).orElse(Integer.MAX_VALUE);
                        return Integer.compare(index1, index2);
                    })
                    .collect(Collectors.toList());

            // 合并sortedPromoAbstractListBest和sortedActivities
            List<String> result = new ArrayList<>();
            // 插入赠品
            if (couponAlleviate2ExpResult) {
                // 优惠减负,跨界营销惊喜买赠拍平展示
                insert2ResultIfHasGiftAndAlleviate(result, dealCtx);
            } else {
                insert2ResultIfHasGift(result, dealCtx);
            }
            result.addAll(sortedPromoAbstractListBest);
            result.addAll(sortedActivities);
            return result;
        } catch (Exception e) {
            log.error("buildPromoAbstractList error,", e);
        }
        return null;
    }

    private String highlightMemberPromoName(String promoName) {
        List<Map<String, Object>> encodeList = Lists.newArrayList();
        Map<String, Object> map = new HashMap<>();
        map.put("containercolor", "#FFEDDE");
        map.put("strikethrough", false);
        map.put("text", promoName);
        map.put("textcolor", "#8e3c13");
        map.put("textsize", 10);
        map.put("textstyle", "Bold");
        map.put("underline", false);
        map.put("customize", true);
        encodeList.add(map);
        return JsonCodec.encode(encodeList);
    }

    private void insert2ResultIfHasGiftAndAlleviate(List<String> result, DealCtx dealCtx) {
        if (dealCtx == null || CollectionUtils.isEmpty(dealCtx.getDealGifts())) {
            return;
        }
        for (DealGift dealGift : dealCtx.getDealGifts()) {
            if (StringUtils.isEmpty(dealGift.getTitle())) {
                continue;
            }
            if (StringUtils.isNotEmpty(dealGift.getCustomerActivityPrefix())) {
                result.add(dealGift.getCustomerActivityPrefix() + dealGift.getTitle());
            } else {
                result.add("赠" + dealGift.getTitle());
            }
        }
    }

    private void insert2ResultIfHasGift(List<String> result, DealCtx dealCtx) {
        if (dealCtx == null || CollectionUtils.isEmpty(dealCtx.getDealGifts())) {
            return;
        }
        List<Map<String, Object>> encodeList = Lists.newArrayList();
        for (DealGift dealGift : dealCtx.getDealGifts()) {
            if (StringUtils.isEmpty(dealGift.getTitle())) {
                continue;
            }
            Map<String, Object> map = new HashMap<>();
            map.put("containercolor", "#FF4B10CC");
            map.put("strikethrough", false);
            if (StringUtils.isNotEmpty(dealGift.getCustomerActivityPrefix())) {
                map.put("text", new StringBuilder(dealGift.getCustomerActivityPrefix()).append(dealGift.getTitle()).toString());
            } else {
                map.put("text", new StringBuilder("赠").append(dealGift.getTitle()).toString());
            }
            map.put("textcolor", "#FFFFFF");
            map.put("textsize", 10);
            map.put("textstyle", "Bold");
            map.put("underline", false);
            map.put("customize", true);
            encodeList.add(map);
        }
        if (CollectionUtils.isNotEmpty(encodeList)) {
            result.add(JsonCodec.encode(encodeList));
        }
    }

    /**
     * 维修预付团单优惠信息
     * @param ctx 上下文对象
     * @param promoDetailModule 优惠模块对象
     */
    public void buildRepairPrepayDealPromoDetailModule(DealCtx ctx, PromoDetailModule promoDetailModule) {
        // 维修预付团单优惠明细模块的优惠折扣为空
        promoDetailModule.setMarketPromoDiscount(StringUtils.EMPTY);
        // 价格说明文案
        promoDetailModule.setPriceDisplayText(getPriceDisplayText(ctx, promoDetailModule));
    }

    private String getPriceDisplayText(DealCtx ctx, PromoDetailModule promoDetailModule) {
        if (LionConfigUtils.enableShowRepairPriceDesc() && DouHuService.isHitRepairPayAbTest(ctx)) {
            return getPriceDescText(ctx);
        }
        // 如果没有打开开关，则使用promoDetailModule原有字段
        return Objects.isNull(promoDetailModule) ? StringUtils.EMPTY : promoDetailModule.getPriceDisplayText();
    }

    private String getPriceDescText(DealCtx dealCtx) {
        PrepayCategoryConfig config = LionConfigUtils.getPrepayCategoryConfig();
        if (Objects.isNull(config) || MapUtils.isEmpty(config.getCategory2TextMap())) {
            return null;
        }
        Map<String, String> category2TextMap = config.getCategory2TextMap();
        String categoryId = String.valueOf(dealCtx.getCategoryId());
        return category2TextMap.getOrDefault(categoryId, null);
    }

    /**
     * 团购次卡属性设置
     */
    public void buildTimesDealPromoInfo(DealCtx ctx, PromoDetailModule promoDetailModule) {

        if (promoDetailModule == null || (StringUtils.isBlank(promoDetailModule.getFinalPrice())
                && StringUtils.isBlank(promoDetailModule.getPromoPrice()))) {
            return;
        }
        if (!TimesDealUtil.isTimesDeal(ctx.getDealGroupDTO())) {
            return;
        }
        // 次数
        String times = TimesDealUtil.parseTimes(ctx.getDealGroupDTO());
        if (StringUtils.isBlank(times) || !NumberUtils.isDigits(times)) {
            return;
        }
        // 单份价格
        String singlePrice = new BigDecimal(getSalePrice(promoDetailModule))
                .divide(new BigDecimal(times), 2, RoundingMode.UP).stripTrailingZeros().toPlainString();

        // 团购次卡C端优化表达
        boolean expressOptimizeExpResult = DealDouHuUtil.getExpressOptimizeExpResult(ctx);
        boolean switch2newField = LionConfigUtils.timesCardSinglePriceSwitch();
        if (expressOptimizeExpResult && TimesDealUtil.isMultiTimesCard(ctx)
                && LionConfigUtils.timesDealExpressOptimizeSwitch()) {
            if (switch2newField) {
                promoDetailModule.setPricePerUnit(String.format("%s", singlePrice));
            } else {
                promoDetailModule.setSingleTimePrice(String.format("%s", singlePrice));
            }

            promoDetailModule.setTimesUnit(
                    QualityEducationUtil.isQualityEducationCategoryByCategoryId(ctx.getCategoryId()) ? "/节" : "/次");
            // 易生活-交易类行业接团购次卡 不适用新次卡样式
            if (!LionConfigUtils.notUseExpressOptimize(ctx, LionConfigUtils.getTimesCardStyleConfig())) {
                promoDetailModule.setExpressOptimize(true);
            }
        }

        String module = ctx.isMt() ? "MtTimesDealExp" : "DpTimesDealExp";
        // 足疗行业做实验，其他行业未做实验
        String abResult = ctx.getModuleAbConfigs().stream()
                .filter(moduleAbConfig -> module.equals(moduleAbConfig.getKey()))
                .filter(moduleAbConfig -> CollectionUtils.isNotEmpty(moduleAbConfig.getConfigs()))
                .map(moduleAbConfig -> moduleAbConfig.getConfigs().get(0).getExpResult()).findFirst().orElse(null);
        if (StringUtils.isBlank(abResult) || "c".equals(abResult)
                || !VersionUtils.isGreatEqualThan(ctx.getMrnVersion(), "0.5.8")) {
            // 根据类目判断将 次->节
            if (QualityEducationUtil.isQualityEducationCategory(ctx)) {
                promoDetailModule.setCopies(String.format("/%s节", times));
                promoDetailModule.setSinglePrice(String.format("单节￥%s", singlePrice));
                return;
            }
            promoDetailModule.setCopies(String.format("/%s次", times));
            promoDetailModule.setSinglePrice(String.format("单次￥%s", singlePrice));
        } else {
            promoDetailModule.setPricePrefix("单次");
            promoDetailModule.setSingleTimePrice(String.format("%s", singlePrice));
            promoDetailModule.setMultiPrice(String.format("%s次总价￥%s", times, getSalePrice(promoDetailModule)));
        }

    }

    private String getSalePrice(PromoDetailModule promoDetailModule) {
        return StringUtils.isNotBlank(promoDetailModule.getFinalPrice()) ? promoDetailModule.getFinalPrice()
                : promoDetailModule.getPromoPrice();
    }


    @Getter
    private enum ComparePriceIndustryEnum {
        DEFAULT(1),
        MEDICAL(2),
        ;
        final int code;

        ComparePriceIndustryEnum(int code) {
            this.code = code;
        }
    }
}
