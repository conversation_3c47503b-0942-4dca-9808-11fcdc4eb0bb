package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;

/**
 * <AUTHOR>
 * @date 2023/4/10
 */
public class RhinoLimitProcessor extends AbsDealProcessor {

    @Override
    public boolean isEnd(DealCtx ctx){
        if(ctx.getDealTechCtx() == null){
            return false;
        }

        return ctx.getDealTechCtx().isRhinoReject();
    }

    @Override
    public void prepare(DealCtx ctx) {

    }

    @Override
    public void process(DealCtx ctx) {

    }
}
