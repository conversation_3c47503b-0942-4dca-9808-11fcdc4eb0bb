package com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/12/13.
 */
@TypeDoc(description = "评论标签模型")
@MobileDo(id = 0x52d9)
@Data
public class ReviewTagDO implements Serializable {

    private int id;

    @FieldDoc(description = "对应的标签评论数")
    @MobileField(key = 0x54a8)
    private int count;

    @FieldDoc(description = "情感：喜欢：1，不喜欢：-1，中立：0")
    @MobileField(key = 0x3bb7)
    private int affection;

    @FieldDoc(description = "标签排序")
    @MobileField(key = 0x56c5)
    private int rankType;

    @FieldDoc(description = "评价标签名")
    @MobileField(key = 0x7ab8)
    private String name;

    @FieldDoc(description = "跳转URL")
    @MobileField(key = 0xc56e)
    private String url;
}
