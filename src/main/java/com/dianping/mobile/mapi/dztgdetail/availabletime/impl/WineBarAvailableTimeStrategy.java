package com.dianping.mobile.mapi.dztgdetail.availabletime.impl;

import com.dianping.mobile.mapi.dztgdetail.availabletime.AvailableTimeStrategy;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.common.enums.AvailableTimeStrategyEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import static com.dianping.mobile.mapi.dztgdetail.helper.AvailableTimeHelper.*;

@Service
public class WineBarAvailableTimeStrategy implements AvailableTimeStrategy {
    @Override
    public String getAvailableTime(DealCtx dealCtx) {
        List<String> wineBarAvailableTime = DealAttrHelper.getAttributeValues(dealCtx.getAttrs(),
                DealAttrKeys.WINE_BAR_AVAILABLE_TIME);
        return getTimeOfDayFormAvailableTime(wineBarAvailableTime);
    }

    private String getTimeOfDayFormAvailableTime(List<String> availableTimeOfDays) {
        if (CollectionUtils.isEmpty(availableTimeOfDays)) {
            return ALL_DAY;
        }
        List<String[]> startEndTimes = new ArrayList<>();
        for (String availableTimes : availableTimeOfDays) {
            String timeStr = getTimeOfDayFromStr(availableTimes);
            if (StringUtils.isBlank(timeStr)) {
                continue;
            }
            if (!checkTimeOfDayFormat(timeStr)) {
                return PARTIAL_TIME;
            }
            startEndTimes.add(timeStr.split("-"));
        }
        if (CollectionUtils.isEmpty(startEndTimes)) {
            return ALL_DAY;
        }
        return getTimeDoc(getFinalStartEndTime(startEndTimes));
    }

    @Override
    public AvailableTimeStrategyEnum getStrategyType() {
        return AvailableTimeStrategyEnum.WIN_BAR_STRATEGY;
    }
}