package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2024/3/20
 * @since mapi-dztgdetail-web
 */
public enum CouponTypeEnum {

    UN_KNOWN(0, "未识别"),
    PAY_COUPON(1, "付费神券"),
    FREE_COUPON(2, "免费神券");

    final private int code;
    final private String name;

    CouponTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

}
