package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintao<PERSON>i <p>
 * @version 2024/3/20
 * @since mapi-dztgdetail-web
 */
@MobileDo(id = 0xd62b)
public class CouponItemInfoDTO implements Serializable {

    /**
     * 有效时间戳
     */
    @MobileDo.MobileField(key = 0x453a)
    private String validTime;

    /**
     * 实膨参数透传
     */
    @MobileDo.MobileField(key = 0xcb02)
    private String bizToken;

    /**
     * 业务线
     */
    @MobileDo.MobileField(key = 0x258d)
    private String bizLine;

    /**
     * 资产类型 1：到家券类型（包括到家、家店通用券）、2到店券类型、3到家通用券
     */
    @MobileDo.MobileField(key = 0xb82f)
    private int assetType;

    /**
     * 券类型 1-付费神券 2-免费神券
     */
    @MobileDo.MobileField(key = 0x7cd6)
    private int couponType;

    /**
     * 是否已膨胀 1-已膨胀 2-未膨胀
     */
    @MobileDo.MobileField(key = 0x73d9)
    private int inflatedStatus;

    /**
     * 是否可膨胀 1-可膨胀 2-不可膨胀
     */
    @MobileDo.MobileField(key = 0x2055)
    private int canInflate;

    /**
     * 最大可膨金额（单位是元）
     */
    @MobileDo.MobileField(key = 0xe399)
    private String couponDesc;

    /**
     * 膨胀后金额（单位为元）
     */
    @MobileDo.MobileField(key = 0xea09)
    private long originalReduceAmount;

    /**
     * 膨胀前金额（单位为元）
     */
    @MobileDo.MobileField(key = 0xdbd2)
    private long originalRequiredAmount;

    /**
     * 券张数
     */
    @MobileDo.MobileField(key = 0xf240)
    private int couponNum;

    /**
     * 顶部icon链接
     */
    @MobileDo.MobileField(key = 0x8d6f)
    private String logoIconUrl;

    /**
     * 新顶部icon链接
     */
    @MobileDo.MobileField(key = 0x21d0)
    private String topLeftNewIconInfo;

    /**
     * 券金额
     */
    @MobileDo.MobileField(key = 0xa712)
    private String couponAmount;

    /**
     * 券名称
     */
    @MobileDo.MobileField(key = 0x6782)
    private String couponName;

    /**
     * 门槛值（满XX可用）
     */
    @MobileDo.MobileField(key = 0x77c8)
    private String thresholdDesc;

    /**
     * 券使用门槛，已膨胀券取minConsumptionAmount，可膨胀券取AFTER_INFLATE_REQUIRE_AMOUNT
     */
    private BigDecimal thresholdAmount;

    /**
     * 券按钮文案
     */
    @MobileDo.MobileField(key = 0x95fd)
    private String couponButtonText;

    /**
     * 到期时间文案
     */
    @MobileDo.MobileField(key = 0x8f8a)
    private String couponValidTimeText;

    /**
     * 券批次id
     */
    @MobileDo.MobileField(key = 0xb991)
    private String applyId;

    /**
     * 券码
     */
    @MobileDo.MobileField(key = 0x537d)
    private String couponCode;

    /**
     * 券是否失效 1:有效 2:失效
     */
    @MobileDo.MobileField(key = 0xc894)
    private int validStatus;

    /**
     * 是否锁券，1-锁券；0-未锁券，已锁券应该排在券包第一个
     */
    @MobileDo.MobileField(key = 0xee48)
    private int queryInflateFlag;

    /**
     * 券到期时间（仅用于排序）
     */
    private long endTime;


    private long couponId;

    private BigDecimal amount;

    public int getAssetType() {
        return assetType;
    }

    public void setAssetType(int assetType) {
        this.assetType = assetType;
    }

    public int getCouponType() {
        return couponType;
    }

    public void setCouponType(int couponType) {
        this.couponType = couponType;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public int getInflatedStatus() {
        return inflatedStatus;
    }

    public void setInflatedStatus(int inflatedStatus) {
        this.inflatedStatus = inflatedStatus;
    }

    public int getCanInflate() {
        return canInflate;
    }

    public void setCanInflate(int canInflate) {
        this.canInflate = canInflate;
    }

    public String getCouponDesc() {
        return couponDesc;
    }

    public void setCouponDesc(String couponDesc) {
        this.couponDesc = couponDesc;
    }


    public int getCouponNum() {
        return couponNum;
    }

    public void setCouponNum(int couponNum) {
        this.couponNum = couponNum;
    }

    public String getLogoIconUrl() {
        return logoIconUrl;
    }

    public void setLogoIconUrl(String logoIconUrl) {
        this.logoIconUrl = logoIconUrl;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }

    public String getCouponValidTimeText() {
        return couponValidTimeText;
    }

    public void setCouponValidTimeText(String couponValidTimeText) {
        this.couponValidTimeText = couponValidTimeText;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public int getValidStatus() {
        return validStatus;
    }

    public void setValidStatus(int validStatus) {
        this.validStatus = validStatus;
    }

    public String getCouponButtonText() {
        return couponButtonText;
    }

    public void setCouponButtonText(String couponButtonText) {
        this.couponButtonText = couponButtonText;
    }

    public String getBizLine() {
        return bizLine;
    }

    public void setBizLine(String bizLine) {
        this.bizLine = bizLine;
    }

    public String getBizToken() {
        return bizToken;
    }

    public void setBizToken(String bizToken) {
        this.bizToken = bizToken;
    }

    public long getCouponId() {
        return couponId;
    }

    public void setCouponId(long couponId) {
        this.couponId = couponId;
    }

    public String getValidTime() {
        return validTime;
    }

    public void setValidTime(String validTime) {
        this.validTime = validTime;
    }

    public long getOriginalReduceAmount() {
        return originalReduceAmount;
    }

    public void setOriginalReduceAmount(long originalReduceAmount) {
        this.originalReduceAmount = originalReduceAmount;
    }

    public long getOriginalRequiredAmount() {
        return originalRequiredAmount;
    }

    public void setOriginalRequiredAmount(long originalRequiredAmount) {
        this.originalRequiredAmount = originalRequiredAmount;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getCouponAmount() {
        return couponAmount;
    }

    public void setCouponAmount(String couponAmount) {
        this.couponAmount = couponAmount;
    }

    public String getThresholdDesc() {
        return thresholdDesc;
    }

    public void setThresholdDesc(String thresholdDesc) {
        this.thresholdDesc = thresholdDesc;
    }

    public int getQueryInflateFlag() {
        return queryInflateFlag;
    }

    public void setQueryInflateFlag(int queryInflateFlag) {
        this.queryInflateFlag = queryInflateFlag;
    }

    public BigDecimal getThresholdAmount() {
        return thresholdAmount;
    }

    public void setThresholdAmount(BigDecimal thresholdAmount) {
        this.thresholdAmount = thresholdAmount;
    }

    public String getTopLeftNewIconInfo() {
        return topLeftNewIconInfo;
    }

    public void setTopLeftNewIconInfo(String topLeftNewIconInfo) {
        this.topLeftNewIconInfo = topLeftNewIconInfo;
    }
}
