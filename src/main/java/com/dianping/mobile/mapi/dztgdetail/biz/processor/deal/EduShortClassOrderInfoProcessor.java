package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.util.EduDealUtils;
import com.dianping.vc.sdk.concurrent.threadpool.ExecutorServices;
import com.google.common.collect.Lists;
import com.sankuai.clr.content.process.common.enums.MerchantTypeEnum;
import com.sankuai.clr.content.process.common.leads.enums.LeadsEntityTypeEnum;
import com.sankuai.clr.content.process.thrift.api.LeadsQueryService;
import com.sankuai.clr.content.process.thrift.dto.leads.req.BatchQueryLeadsInfoReqDTO;
import com.sankuai.clr.content.process.thrift.dto.leads.req.QueryLeadsInfoReqDTO;
import com.sankuai.clr.content.process.thrift.dto.leads.resp.BatchQueryLeadsInfoRespDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.apache.thrift.TException;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;


public class EduShortClassOrderInfoProcessor extends AbsDealProcessor {
    @Resource
    private LeadsQueryService leadsQueryService;

    public static final ExecutorService executorService = ExecutorServices.forThreadPoolExecutor("eduShortClassOrderInfo", 10, 50);

    public static final int MT_CODE = 2;
    public static final int DP_CODE = 1;
    public static final int ENTRANCE_CODE = 12;

    @Override
    public void prepare(DealCtx ctx) {
        if (!EduDealUtils.preCheckShortClassHasButton(ctx)) {
            return;
        }
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        if (dealGroupDTO == null) {
            return;
        }

        Future<BatchQueryLeadsInfoRespDTO> eduShortClassInfoFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return leadsQueryService.batchQueryLeadsInfo(buildRequest(ctx));
            } catch (TException e) {
                logger.error("leadsQueryService.batchQueryLeadsInfo error", e);
            }
            return null;
        }, executorService);

        ctx.getFutureCtx().setQueryLeadsInfoRespDTOFuture(eduShortClassInfoFuture);
    }

    private BatchQueryLeadsInfoReqDTO buildRequest(DealCtx ctx) {
        BatchQueryLeadsInfoReqDTO reqDTO = new BatchQueryLeadsInfoReqDTO();

        reqDTO.setEntranceCode(12);
        QueryLeadsInfoReqDTO queryLeadsInfoReqDTO = new QueryLeadsInfoReqDTO();
        queryLeadsInfoReqDTO.setEntranceCode(ENTRANCE_CODE);
        queryLeadsInfoReqDTO.setMerchantType(MerchantTypeEnum.SHOP.getType());
        queryLeadsInfoReqDTO.setPlatform(ctx.isMt() ? MT_CODE : DP_CODE);
        queryLeadsInfoReqDTO.setEntityId((long) (ctx.isMt() ? ctx.getMtId() : ctx.getDpId()));
        queryLeadsInfoReqDTO.setEntityType(LeadsEntityTypeEnum.DEAL_GROUP_ORDER.getType());
        if (ctx.isMt()) {
            queryLeadsInfoReqDTO.setMtUserId(ctx.getEnvCtx().getMtUserId());//已确认判断平台后再使用
        } else {
            queryLeadsInfoReqDTO.setDpUserId(ctx.getEnvCtx().getDpUserId());
        }
        reqDTO.setQueryLeadsInfoReqDTOList(Lists.newArrayList(queryLeadsInfoReqDTO));
        return reqDTO;
    }

    @Override
    public void process(DealCtx ctx) {

    }
}
