package com.dianping.mobile.mapi.dztgdetail.facade.rcf.fetcher;

import com.dianping.deal.style.DealPageLayoutService;
import com.dianping.deal.style.dto.laout.DealPageLayoutBatchQueryRequest;
import com.dianping.deal.style.dto.laout.DealPageLayoutQueryRequest;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.AbsWrapper;
import com.dianping.mobile.mapi.dztgdetail.util.PigeonInvokeSettingUtils;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.meituan.inf.xmdlog.XMDLogFormat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.concurrent.Future;

/**
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2024/10/25 10:56
 */
@Slf4j
@Component
public class DealLayoutFetcher extends AbsWrapper {

    @Autowired
    @Qualifier("dealPageLayoutServiceFuture")
    private DealPageLayoutService dealPageLayoutService;
    public Future preQueryLayout(final DealPageLayoutQueryRequest request) {
        try {
            PigeonInvokeSettingUtils.setInvokeTimeout();
            dealPageLayoutService.query(request);
            return FutureFactory.getFuture();
        } catch (Exception e) {
            log.error("preBatchQueryLayout, error:{}", request, e);
            return null;
        }
    }

    public Future preBatchQueryLayout(final DealPageLayoutBatchQueryRequest request) {
        try {
            PigeonInvokeSettingUtils.setInvokeTimeout();
            dealPageLayoutService.batchQuery(request);
            return FutureFactory.getFuture();
        } catch (Exception e) {
            log.error("preBatchQueryLayout, error:{}", request, e);
            return null;
        }
    }

//    public CompletableFuture<DealPageLayoutDTO> preQueryLayout(final DealPageLayoutQueryRequest request) {
//        return AthenaInf.getRpcCompletableFuture(dealPageLayoutService.query(request))
//                .exceptionally(ex -> {
//                    commonExceptionHandler("preBatchQueryLayout", JSON.toJSONString(request), ex);
//                    return null;
//                }).thenApply(response -> {
//                    if (response == null) {
//                        commonExceptionHandler("preBatchQueryLayout-response", JSON.toJSONString(request), new RpcException("查询团详布局失败,response为null"));
//                        return null;
//                    }
//                    if (response.isSuccess()) {
//                        return response.getLayout();
//                    } else {
//                        commonExceptionHandler("preBatchQueryLayout-response", JSON.toJSONString(request), new RpcException("查询团详布局失败,msg:" + response.getMsg()));
//                        return null;
//                    }
//                });
//    }
//
//    public CompletableFuture<Map<Long, DealPageLayoutDTO>> preBatchQueryLayout(final DealPageLayoutBatchQueryRequest request) {
//        return AthenaInf.getRpcCompletableFuture(dealPageLayoutService.batchQuery(request))
//                .exceptionally(ex -> {
//                    commonExceptionHandler("preBatchQueryLayout", JSON.toJSONString(request), ex);
//                    return null;
//                }).thenApply(response -> {
//                    if (response == null) {
//                        commonExceptionHandler("preBatchQueryLayout-response", JSON.toJSONString(request), new RpcException("查询团详布局失败,response为null"));
//                        return null;
//                    }
//                    if (response.isSuccess()) {
//                        return response.getMtDealGroupIdLayoutMap();
//                    } else {
//                        commonExceptionHandler("preBatchQueryLayout-response", JSON.toJSONString(request), new RpcException("查询团详布局失败,msg:" + response.getMsg()));
//                        return null;
//                    }
//                });
//    }

    private void commonExceptionHandler(String methodName, String paramsStr, Throwable throwable) {
        log.error(XMDLogFormat.build()
                .putTag("scene", "FacadeService")
                .putTag("method", methodName)
                .message(String.format("%s error, params: %s", methodName, paramsStr)), throwable);
    }


}
