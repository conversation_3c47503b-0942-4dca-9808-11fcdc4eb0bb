package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.action.extend.buy;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.BottomBarActionDataVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.enums.BottomBarActionTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/06/12
 * @description 吸底去购买
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SuckBottomBuyActionVO extends BottomBarActionDataVO {

    /**
     * 动作类型
     */
    private final int actionType = BottomBarActionTypeEnum.SUCK_BOTTOM_BUY.getCode();

    /**
     * 打开类型
     * redirect=直接跳转
     * modal=打开浮层
     */
    private String openType;

    /**
     * 跳转链接
     */
    private String url;

    /**
     * 按钮文案
     */
    private String text;

    /**
     * 图标
     */
    private String icon;


}