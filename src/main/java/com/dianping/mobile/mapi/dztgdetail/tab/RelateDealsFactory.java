package com.dianping.mobile.mapi.dztgdetail.tab;

import com.dianping.mobile.mapi.dztgdetail.tab.postprocess.RelatePostProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class RelateDealsFactory implements ApplicationListener<ContextRefreshedEvent> {

    @Autowired
    ApplicationContext applicationContext;

    private Map<Integer, RelateDeals> publishCategory2RD = new HashMap<>();

    private List<RelatePostProcessor> relatePostProcessors = new ArrayList<>();

    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        Map<String, RelateDeals> relateDealsMap = applicationContext.getBeansOfType(RelateDeals.class);

        for (RelateDeals rd : relateDealsMap.values()) {
            for (int publishCategory : rd.identifyByPublishCategory()) {
                publishCategory2RD.put(publishCategory, rd);
            }
        }

        Map<String, RelatePostProcessor> relatePostProcessorMap = applicationContext.getBeansOfType(RelatePostProcessor.class);
        relatePostProcessors.addAll(relatePostProcessorMap.values());

    }

    public RelateDeals getRelateDeals(int publishCategory) {
        return publishCategory2RD.get(publishCategory);
    }

    public List<RelatePostProcessor> getRelatePostProcessors() {
        return relatePostProcessors;
    }
}
