package com.dianping.mobile.mapi.dztgdetail.biz.immersiveimage;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageFilterRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageFilterVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-08-28
 * @desc 沉浸页图片服务接口定义
 */
public interface ImmersiveImageService {
    /**
     * 获取二级团单行业ID
     *
     * @return 行业ID
     */
    List<Integer> getCategoryIds();

    /**
     * 获取沉浸页展示信息
     *
     * @param request 请求参数
     * @return 沉浸页展示信息，包含图集ID、图集名称、标签、图片资源链接等
     */
    ImmersiveImageVO getImmersiveImage(GetImmersiveImageRequest request, EnvCtx envCtx);

    ImmersiveImageFilterVO getImmersiveImageFilter(GetImmersiveImageFilterRequest request);
}
