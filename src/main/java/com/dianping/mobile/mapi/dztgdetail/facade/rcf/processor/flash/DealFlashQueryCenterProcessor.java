package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.flash;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsFlashDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.service.cellarservice.dealdetail.DealGroupDTOCellarService;
import com.dianping.mobile.mapi.dztgdetail.biz.service.cellarservice.dealdetail.req.DealGroupCacheRequest;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FlashDealCtx;
import com.google.common.collect.Sets;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupCategoryBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupIdBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.Future;

import static com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils.hitDealId2CategorySwitch;

/**
 * @Author: guangyujie
 * @Date: 2024/10/25 14:15
 */
@Slf4j
public class DealFlashQueryCenterProcessor extends AbsFlashDealProcessor {

    @Autowired
    private QueryCenterWrapper queryCenterWrapper;
    
    @Resource
    private DealGroupDTOCellarService dealGroupDTOCellarService; 

    @Override
    public void prepare(FlashDealCtx ctx) {
        // 如果从缓存拿到数据就不走 查询中心
        DealGroupCacheRequest request = new DealGroupCacheRequest();
        request.setDealGroupId(ctx.getDealGroupId());
        request.setMT(ctx.isMt());
        String cacheKey = dealGroupDTOCellarService.buildKey(request);
        DealGroupDTO dealGroupDTO = (DealGroupDTO) dealGroupDTOCellarService.getCacheValue(cacheKey);
        if(Objects.isNull(dealGroupDTO)) {
            doPrepare(ctx);
        }else {
            ctx.setDealGroupDTO(dealGroupDTO);
        }
    }

    @Override
    public void process(FlashDealCtx ctx) {
        // 如果已有数据 就不解析查询中心结果 
       if (Objects.isNull(ctx.getDealGroupDTO())) {
           doProcess(ctx);
       }
    }
    
    private void doPrepare(FlashDealCtx ctx){
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.builder().dealGroupIds(Sets.newHashSet((long) ctx.getDealGroupId()), ctx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP).dealGroupId(DealGroupIdBuilder.builder().all()).category(DealGroupCategoryBuilder.builder().all()).build();
        Future singleDealGroupDtoFuture = queryCenterWrapper.preDealGroupDTO(queryByDealGroupIdRequest);
        ctx.getFutureCtx().setQueryCenterFuture(singleDealGroupDtoFuture);
    }
    
    private void doProcess(FlashDealCtx ctx) {
        try {
            DealGroupDTO dealGroupDTO = queryCenterWrapper.getDealGroupDTO(ctx.getFutureCtx().getQueryCenterFuture());
            if (dealGroupDTO == null) {
                throw new IllegalArgumentException("查不到团单信息");
            }
            ctx.setDealGroupDTO(dealGroupDTO);
            // 将结果缓存到cellar，团单id为key
            if (hitDealId2CategorySwitch(ctx.getDealGroupId())){
                DealGroupCacheRequest request = new DealGroupCacheRequest();
                request.setDealGroupId(ctx.getDealGroupId());
                request.setMT(ctx.isMt());
                dealGroupDTOCellarService.saveOrUpdate(request, dealGroupDTO);
            }
        } catch (Exception e) {
            log.error("DealFlashQueryCenterProcessor,dealGroupId:{}", ctx.getDealGroupId(), e);
            ctx.setEnd(true);
        }
    }
}
