package com.dianping.mobile.mapi.dztgdetail.biz;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @date 2022/5/7
 */
@Slf4j
public class AbstractBiz {

    public <T> T getFutureResult(Future serviceFuture, Class<T> clz) {
        if (serviceFuture == null)
            return null;

        try {
            return (T) serviceFuture.get();
        } catch (Exception e) {
            log.error(String.format("getFutureResult has error:%s", clz == null ? "null" : clz.getCanonicalName()), e);
        }
        return null;
    }

    public <T> T getFutureResult(Future serviceFuture) {
        return getFutureResult(serviceFuture, null);
    }
}
