package com.dianping.mobile.mapi.dztgdetail.facade.rcf.facade;

import com.dianping.deal.style.dto.laout.DealPageLayoutConfigDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.ProcessHandler;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FlashDealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealFlashReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealStyleBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.shankai.DealDetailFlashPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.shankai.DealLayoutConfigVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.shankai.DealPageLayoutComponentVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.shankai.FlashDealDetailVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/10/17 17:27
 */
@Slf4j
@Component
public class DealDetailFlashFacade {

    public static final String CUSTOM_STRUCTURED_MODULE = "custom_structured_module";
    public static final String UNIFORM_STRUCTURE_TABLE = "uniform-structure-table";

    @Autowired
    @Qualifier("dealDetailFlashHandler")
    private ProcessHandler<FlashDealCtx> dealDetailFlashHandler;

    @Autowired
    @Qualifier("dealDetailFlashOtherHandler")
    private ProcessHandler<FlashDealCtx> dealDetailFlashOtherHandler;

    public DealDetailFlashPBO buildDealDetailFlashPBO(DealFlashReq req, EnvCtx envCtx){
        FlashDealCtx flashDealCtx = doDealDetailFlashCf(req, envCtx);
        return buildResult(flashDealCtx);
    }

    public FlashDealCtx doDealDetailFlashCf(DealFlashReq req, EnvCtx envCtx) {
        FlashDealCtx flashDealCtx = initDealsBaseData(req, envCtx);
        dealDetailFlashHandler.preThenProc(flashDealCtx);
        dealDetailFlashOtherHandler.preThenProc(flashDealCtx);
        return flashDealCtx;
    }

    public DealDetailFlashPBO buildResult(FlashDealCtx dealCtx) {
        try {
            if (dealCtx == null || dealCtx.getLayoutConfig() == null) {
                throw new IllegalArgumentException("FlashDealCtx异常");
            }
            DealStyleBO dealStyleBO = dealCtx.getDealStyleBO();
            DealDetailFlashPBO result = new DealDetailFlashPBO();
            result.setDealLayoutConfig(buildDealLayoutConfig(dealCtx.getLayoutConfig()));
            if (dealCtx.getLayoutConfig().isRender()) {
                result.setDealLayoutComponents(buildDealLayoutComponents(dealCtx));
                result.setDealStyle(dealStyleBO);
                result.setDealDetail(buildDealDetail(dealCtx, dealStyleBO));
            }
            return result;
        } catch (Exception e) {
            log.error("DealDetailFlashFacade.buildResult,dealGroupId:{},isMt:{}", dealCtx.getDealGroupId(), dealCtx.isMt(), e);
            DealDetailFlashPBO result = new DealDetailFlashPBO();
            result.setDealLayoutConfig(buildDealLayoutConfig(DealPageLayoutConfigDTO.getDefaultConfig()));
            return result;
        }
    }

    private FlashDealDetailVO buildDealDetail(FlashDealCtx dealCtx, DealStyleBO dealStyleBO){
        if (Objects.isNull(dealStyleBO) || Objects.isNull(dealStyleBO.getModuleConfigsModule()) || CollectionUtils.isEmpty(dealStyleBO.getModuleConfigsModule()
                .getModuleConfigs())) {
             return null;
        }
        List<String> dealStyleValues = dealStyleBO.getModuleConfigsModule().getModuleConfigs().stream()
                .filter(moduleConfigDo -> Objects.nonNull(moduleConfigDo)
                        && StringUtils.isNotBlank(moduleConfigDo.getKey())
                        && StringUtils.isNotBlank(moduleConfigDo.getValue()))
                .map(ModuleConfigDo::getValue)
                .collect(
                        Collectors
                                .toList());
        FlashDealDetailVO dealDetailVO = new FlashDealDetailVO();
        if (dealStyleValues.contains(CUSTOM_STRUCTURED_MODULE)) {
            dealDetailVO.setDealModuleDetail(dealCtx.getDealModuleDetail());
        }else if (dealStyleValues.contains(UNIFORM_STRUCTURE_TABLE)) {
            dealDetailVO.setDealDetailStruct(dealCtx.getDealDetailStruct());
        }
        return dealDetailVO;
    }

    public FlashDealCtx initDealsBaseData(DealFlashReq req, EnvCtx envCtx) {
        FlashDealCtx dealCtx = new FlashDealCtx(envCtx);
        if (req.getDeviceheight() != null) {
            dealCtx.setDeviceHeight((int) Math.round(req.getDeviceheight()));
        }
        if (envCtx.isMt()) {
            dealCtx.setMtId(req.getDealgroupid());
            dealCtx.setMtLongShopId(req.getPoiid());
            //打点判断入口处的shopid是否为空
        } else {
            long dpShopId = req.getPoiid();
            dealCtx.setDpId(req.getDealgroupid());
            dealCtx.setDpLongShopId(dpShopId);
        }
        dealCtx.setDealGroupId(req.getDealgroupid());
        dealCtx.setPoiid(req.getPoiid());
        if (req.getUserlat() != null && req.getUserlng() != null) {
            dealCtx.setUserlng(req.getUserlng());
            dealCtx.setUserlat(req.getUserlat());
        }
        dealCtx.setRequestSource(req.getPageSource());
        dealCtx.setMrnVersion(req.getMrnversion());
        dealCtx.setDealflashReq(req);
        return dealCtx;
    }

    private static @NotNull List<DealPageLayoutComponentVO> buildDealLayoutComponents(FlashDealCtx dealCtx) {
        if (CollectionUtils.isEmpty(dealCtx.getLayoutComponents())){
            return Lists.newArrayList();
        }
        return dealCtx.getLayoutComponents().stream().filter(dealPageLayoutComponentDTO -> Objects.nonNull(dealPageLayoutComponentDTO))
                .map(component -> {
                    DealPageLayoutComponentVO vo = new DealPageLayoutComponentVO();
                    vo.setKey(component.getKey());
                    vo.setHeight(component.getHeight());
                    return vo;
                }).collect(Collectors.toList());
    }

    private static @NotNull DealLayoutConfigVO buildDealLayoutConfig(DealPageLayoutConfigDTO layoutConfig) {
        DealLayoutConfigVO vo = new DealLayoutConfigVO();
        vo.setReport(layoutConfig.isReport());
        vo.setRender(layoutConfig.isRender());
        return vo;
    }

}
