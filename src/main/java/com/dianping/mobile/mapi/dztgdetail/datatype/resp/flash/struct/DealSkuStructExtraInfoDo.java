package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.struct;


import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

import java.util.List;

/**
 * Created by ji<PERSON><PERSON><PERSON> on 2020/3/16.
 */
@MobileDo(id = 0xf75b)
public class DealSkuStructExtraInfoDo {

    @FieldDoc(description = "标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "副标题")
    @MobileDo.MobileField(key = 0xd894)
    private String subTitle;

    @FieldDoc(description = "补充信息")
    @MobileDo.MobileField(key = 0x4974)
    private List<DealSkuStructInfoDo> dealStructInfos;

    @FieldDoc(description = "提示信息")
    @MobileDo.MobileField(key = 0x3dd0)
    private List<DealExtraNoticeDo> dealExtraNotices;


    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public List<DealSkuStructInfoDo> getDealStructInfos() {
        return dealStructInfos;
    }

    public void setDealStructInfos(List<DealSkuStructInfoDo> dealStructInfos) {
        this.dealStructInfos = dealStructInfos;
    }

    public List<DealExtraNoticeDo> getDealExtraNotices() {
        return dealExtraNotices;
    }

    public void setDealExtraNotices(List<DealExtraNoticeDo> dealExtraNotices) {
        this.dealExtraNotices = dealExtraNotices;
    }
}
