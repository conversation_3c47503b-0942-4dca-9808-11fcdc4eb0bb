package com.dianping.mobile.mapi.dztgdetail.datatype.resp.shop;

import lombok.Data;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;

import java.io.Serializable;

/**
 * 标签四个角圆角
 */
@Data
public class RoundCornerRadius implements Serializable {
    /**
     * 右下角圆角
     */
    @MobileField(key = 0xf0d8)
    private double borderBottomRightRadius;

    /**
     * 左下角圆角
     */
    @MobileField(key = 0x56f5)
    private double borderBottomLeftRadius;

    /**
     * 右上角圆角
     */
    @MobileField(key = 0xdb5a)
    private double borderTopRightRadius;

    /**
     * 左上角圆角
     */
    @MobileField(key = 0x872f)
    private double borderTopLeftRadius;
}
