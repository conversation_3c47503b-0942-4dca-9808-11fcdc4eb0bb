package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.AbsWrapper;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.sankuai.dztheme.spuproduct.SpuThemeQueryService;
import com.sankuai.dztheme.spuproduct.req.SpuRequest;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.Future;

@Component
public class DzGeneralProductWrapper extends AbsWrapper {

    @Resource
    @Qualifier("SpuThemeQueryServiceFuture")
    public SpuThemeQueryService spuThemeQueryService;


    /**
     * 预查询团标品详情
     *
     * @param request
     * @return
     */
    public Future preQuerySpuTheme(SpuRequest request) {
        if (request == null) {
            return null;
        }
        try {
            spuThemeQueryService.query(request);
            return FutureFactory.getFuture();
        } catch (Exception e) {
            logger.error("DzGeneralProductWrapper preQueryDealSubTitle has exception.", e);
            return null;
        }
    }
}
