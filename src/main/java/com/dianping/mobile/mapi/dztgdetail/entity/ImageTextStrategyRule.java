package com.dianping.mobile.mapi.dztgdetail.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-12-12
 * @desc 图文详情折叠策略规则
 */
@Data
public class ImageTextStrategyRule {
    private String pageSource;
    private List<Integer> categoryIds;
    private List<String> serviceTypes;
    private List<Long> dpDealGroupIds;
    /**
     * 从大到小排序
     */
    private int priority;
    private String strategyName;
    /**
     * 折叠阈值，图文详情图片数大于该值时折叠
     */
    private int threshold;
}
