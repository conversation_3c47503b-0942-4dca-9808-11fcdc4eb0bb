package com.dianping.mobile.mapi.dztgdetail.common.constants;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DealStyle;

public final class DealStyles {
    private DealStyles() {}

    public static final DealStyle FOOD = new DealStyle("food".intern());
    public static final DealStyle FUN = new DealStyle("fun".intern());
    public static final DealStyle FUN_MSG = new DealStyle("fun_massage".intern());
    public static final DealStyle SCENIC = new DealStyle("scenic".intern());
    public static final DealStyle KTV = new DealStyle("ktv".intern());
    public static final DealStyle HOTEL = new DealStyle("hotel".intern());
    public static final DealStyle HOTEL_SELECTED = new DealStyle("hotel_selected");
    public static final DealStyle TRAVEL = new DealStyle("travel".intern());
    public static final DealStyle TRAVEL_SELECTED = new DealStyle("travel_selected");
    public static final DealStyle BEAUTY = new DealStyle("beauty".intern());
    public static final DealStyle BEAUTY_NAIL = new DealStyle("beauty_nail");
    public static final DealStyle CLOTHES = new DealStyle("clothes".intern());
    public static final DealStyle EDUCATION = new DealStyle("education".intern());
    public static final DealStyle BABY_PHOTO = new DealStyle("baby_photoedutg".intern());
    public static final DealStyle DEFAULT_STYLE = new DealStyle("default".intern());
    public static final DealStyle DEFAULT_DEGRADE= new DealStyle("default_degrade");
    public static final DealStyle DEFAULT_DOWN = new DealStyle("default_bardown");
    public static final DealStyle DEFAULT_DOWN_DEGRADE = new DealStyle("default_bardown_degrade");
    public static final DealStyle GC_DEFAULT = new DealStyle("gcdefault");
    public static final DealStyle FLOWER = new DealStyle("flower_all");
    public static final DealStyle BACK_ROOM = new DealStyle("fun_backroom");
    public static final DealStyle WEDDING = new DealStyle("wedding");
}