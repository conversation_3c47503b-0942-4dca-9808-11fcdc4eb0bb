package com.dianping.mobile.mapi.dztgdetail.common.constants;

public class CatEvents {

    public static final String DEALBASE_SUC = "dealbaseSuc";
    public static final String DEALBASE_SUC_H5 = "dealbaseSuc_h5";
    public static final String DEALBASE_FAIL = "dealbaseFail";
    public static final String DEALBASE_FAIL_H5 = "dealbaseFail_h5";
    public static final String DEALBASE_PRODUCT_INTERCEPT = "dealbaseProductIntercept";
    public static final String DEALBASE_PRODUCT_INTERCEPT_H5 = "dealbaseProductIntercept_h5";
    public static final String PUBLISH_CATEGORY = "publishCategory";
    public static final String DEALBASE_SOURCE = "dealbaseSource";
    public static final String DEALBASE_DETAIL_UNLOGIN = "detailUnLogin";
    public static final String DEALBASE_DETAIL_APP_UNLOGIN = "detailAPPUnLogin";
    public static final String IMMERSIVE_IMAGE_FAIL = "immersiveImageFail";
    public static final String IMMERSIVE_IMAGE_NO_DATA = "immersiveImageNoData";
    public static final String IMMERSIVE_IMAGE_SUC = "immersiveImageSuc";
    public static final String IMMERSIVE_IMAGE_COUNT = "immersiveImageCount";
    /**
     * 查询团单简要信息接口-失败-打点
     */
    public static final String DEAL_TINY_INFO_FAIL = "dealTinyInfoFail";
    /**
     * 查询团单简要信息接口-无数据-打点
     */
    public static final String DEAL_TINY_INFO_NO_DATA = "dealTinyInfoNoData";
    /**
     * 查询团单简要信息接口-成功-打点
     */
    public static final String DEAL_TINY_INFO_SUC = "dealTinyInfoSuc";
    public static final String IS_SHOW_DEAL_LOW_PRICE_ENTRANCE_FAIL = "isShowDealLowPriceItemEntranceFail";
    public static final String IS_SHOW_DEAL_LOW_PRICE_ENTRANCE_NO_DATA = "isShowDealLowPriceItemEntranceNoData";
    public static final String IS_SHOW_DEAL_LOW_PRICE_ENTRANCE_SUC = "isShowDealLowPriceItemEntranceSuc";
    public static final String DEAL_PRICE_TREND_SUC = "dealPriceTrendSuc";
    public static final String DEAL_PRICE_TREND_FAIL = "dealPriceTrendFail";
    public static final String DEAL_PRICE_TREND_NO_DATA = "dealPriceTrendNoData";

    public static final String IMMERSIVE_IMAGE_FILTER_SUC = "immersiveImageFilterSuc";
    public static final String IMMERSIVE_IMAGE_FILTER_NO_DATA = "immersiveImageFilterNoData";
    public static final String IMMERSIVE_IMAGE_FILTER_FAIL = "immersiveImageFilterFail";
    /**
     * 进入降级链路
     */
    public static final String DZ_DEAL_INTO_DEGRADE = "dztgdetail.bin.into.degrade";
    /**
     * 导致发生接口降级的错误
     */
    public static final String CAUSE_DEGRADE_ERROR = "cause.degrade.error";

    /**
     * 渠道信息
     */
    public static final String PAGE_SOURCE_ID = "pagesourceId"; // 所有渠道id打点
    public static final String PLATFORM_PAGE_SOURCE_ID = "platformPagesourceId";    // 平台渠道id打点
    public static final String DZTG_PAGE_SOURCE_ID = "dztgPagesourceId";    // 团详侧渠道id打点
    public static final String INVALID_PAGE_SOURCE_ID =  "invalidPagesourceId"; // 非法渠道id打点（既不是平台渠道也不是团详侧渠道）
    public static final String PAGE_SOURCE_NAME = "pagesourceName"; // 所有渠道name打点
    public static final String PLATFORM_PAGE_SOURCE_NAME = "platformPagesourceName";    // 平台渠道name打点
    public static final String DZTG_PAGE_SOURCE_NAME = "dztgPagesourceName";    // 团详侧渠道name打点
    public static final String INVALID_PAGE_SOURCE_NAME =  "invalidPagesourceName"; // 非法渠道name打点（既不是平台渠道也不是团详侧渠道）
    public static final String HOT_NAIL_MODULE_NO_DARA = "hotNailModuleNoData";
    public static final String HOT_NAIL_MODULE_SUC = "hotNailModuleSuc";
    public static final String HOT_NAIL_MODULE_ERR = "hotNailModuleErr";
    public static final String ORDER_NAIL_STYLE_NO_DARA = "orderNailStyleNoData";
    public static final String ORDER_NAIL_STYLE_SUC = "orderNailStyleSuc";
    public static final String ORDER_NAIL_STYLE_ERR = "orderNailStyleErr";

    public static final String NOT_HIT_CARD_STYLE_V2 =  "notHitCardStyleV2"; // 未命中cardStyleV2样式实验
    public static final String NOT_MEET_CARD_STYLE_V2 =  "notMeetCardStyleV2"; // 不满足cardStyleV2样式实验条件

    // 底bar调用场景
    public static final String BUY_BAR_CALL_SCENE = "buyBarCallScene";
    /**
     * 查询适用门店id成功
     */
    public static final String DEAL_SHOP_ID_SUC = "dealShopIdSuc";
    /**
     * 查询适用门店id无数据
     */
    public static final String DEAL_SHOP_ID_NO_DATA = "dealShopIdNoData";
    /**
     * 查询适用门店id失败
     */
    public static final String DEAL_SHOP_ID_FAIL = "dealShopIdFail";

    /**
     * 跨店推荐广告监控-无广告请求次数-打点
     */
    public static final String CROSS_SHOP_RECOMMEND_AD_EMPTY_COUNT = "crossShopRecommendAdEmptyCount";

    /**
     * 跨店推荐广告监控-广告返回item数
     */
    public static final String CROSS_SHOP_RECOMMEND_AD = "crossShopRecommendAd";

}
