package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.DecryptedField;
import lombok.Data;

import java.io.Serializable;

import static com.sankuai.sig.botdefender.core.enums.AssetIdType.SHOP_UUID;

@TypeDoc(description = "练车门店入参")
@MobileRequest
@Data
public class DrivingPoiReq implements IMobileRequest, Serializable {

    /**
     * @Deprecated
     * 团单id，原int类型
     */
    @Deprecated
    @MobileRequest.Param(name = "dealGroupId")
    private Integer dealGroupId;

    /**
     * 团单id，string类型
     */
    @MobileRequest.Param(name = "stringDealGroupId")
    private String stringDealGroupId;

    /**
     * 门店id
     */
    @MobileRequest.Param(name = "shopIdStr")
    private String shopIdStr;
    @MobileRequest.Param(name = "shopIdStrEncrypt")
    @DecryptedField(targetFieldName = "shopIdStr")
    private String shopIdStrEncrypt;

    /**
     * shopUuid
     */
    @MobileRequest.Param(name = "shopUuid")
    private String shopUuid;
    @MobileRequest.Param(name = "shopUuidEncrypt")
    @DecryptedField(targetFieldName = "shopUuid", assetIdType = SHOP_UUID)
    private String shopUuidEncrypt;

    /**
     * 城市id
     */
    @MobileRequest.Param(name = "cityId")
    private Integer cityId;

    /**
     * 用户经度
     */
    @MobileRequest.Param(name = "userLng")
    private Double userLng;

    /**
     * 用户纬度
     */
    @MobileRequest.Param(name = "userLat")
    private Double userLat;

    /**
     * 站点：APP可以不传，其他站点必须传，值参见后端枚举"
     * @see com.dianping.deal.common.enums.ClientTypeEnum
     */
    @MobileRequest.Param(name = "clientType")
    private Integer clientType;

    public Integer getDealGroupId() {
        return dealGroupId;
    }

    public void setDealGroupId(Integer dealGroupId) {
        this.dealGroupId = dealGroupId;
    }

    public String getShopIdStr() {
        return shopIdStr;
    }

    public void setShopIdStr(String shopIdStr) {
        this.shopIdStr = shopIdStr;
    }

    public Integer getCityId() {
        return cityId == null ? 0 : cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Double getUserLng() {
        return userLng == null ? 0.0 : userLng;
    }

    public void setUserLng(Double userLng) {
        this.userLng = userLng;
    }

    public Double getUserLat() {
        return userLat == null ? 0.0 : userLat;
    }

    public void setUserLat(Double userLat) {
        this.userLat = userLat;
    }

    public Integer getClientType() {
        return clientType == null ? 0 : clientType;
    }

    public void setClientType(Integer clientType) {
        this.clientType = clientType;
    }

    public String getShopUuid() {
        return shopUuid;
    }

    public void setShopUuid(String shopUuid) {
        this.shopUuid = shopUuid;
    }

    public String getShopIdStrEncrypt() {
        return shopIdStrEncrypt;
    }

    public void setShopIdStrEncrypt(String shopIdStrEncrypt) {
        this.shopIdStrEncrypt = shopIdStrEncrypt;
    }
}