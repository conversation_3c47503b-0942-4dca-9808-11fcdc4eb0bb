package com.dianping.mobile.mapi.dztgdetail.datatype.resp.other;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

@Data
@TypeDoc(description = "解密数据模型")
@MobileDo(id = 0xc895)
public class DecryptVO implements Serializable {

    @FieldDoc(description = "decryptedStr")
    @MobileDo.MobileField(key = 0xdff2)
    private String decryptedStr;

}
