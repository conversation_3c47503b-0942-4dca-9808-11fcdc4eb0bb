package com.dianping.mobile.mapi.dztgdetail.common.enums;

/**
 * @Author: z<PERSON><PERSON><PERSON><PERSON><PERSON>@meituan.com
 * @Date: 2024/11/1
 */
public enum AppTypeEnum {
    DP(1,"点评"),
    MT(2,"美团");

    private int value;

    private String name;

    AppTypeEnum(int value, String name){
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static AppTypeEnum valueOf(int value) {

        for (AppTypeEnum appTypeEnum : values()) {
            if (appTypeEnum.value == value) {
                return appTypeEnum;
            }
        }
        return null;
    }
}
