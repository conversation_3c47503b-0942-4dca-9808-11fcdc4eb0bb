package com.dianping.mobile.mapi.dztgdetail.common.enums;

import lombok.Getter;

@Getter
public enum MpAppIdEnum {
    /**
     * 小程序的唯一标识
     */
    MT_KUAISHOU_MINIPROGRAM(1, "ks652177521456999275"),
    MT_WEIXIN_MINIPROGRAM(2, "wxde8ac0a21135c07d"),
    DP_WEIXIN_MINIPROGRAM(3, "wx734c1ad7b3562129"),
    XIUYU_WANWU_MINIPROGRAM(4, "wx46d3ba1216c12af4"),
    DP_BAIDUMAP_MINIPROGRAM(5, "11553825"),
    /**
     * 私域直播小程序
     */
    MT_LIVE_WEIXIN_MINIPROGRAM(6, "wxe955ef83bdcc9f82"),
    /**
     * 私域直播提单小程序
     */
    MT_LIVE_ORDER_WEIXIN_MINIPROGRAM(7, "wx33c85a6366be4fc8"),
    ;


    private int code;
    private final String mpAppId;

    MpAppIdEnum(int code, String mpAppId) {
        this.code = code;
        this.mpAppId = mpAppId;
    }

    public static MpAppIdEnum codeOf(int code) {
        for (MpAppIdEnum value : MpAppIdEnum.values()) {
            if (value.code == code) {
                return value;
            }
        }

        throw new UnsupportedOperationException("MpAppIdEnum has no code of " + code);
    }
}
