package com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

@MobileDo
@Data
public class ComButton implements Serializable {

    /**
     * 动作类型
     *
     * @see ActionEnum
     */
    @MobileDo.MobileField(key = 0x1ec2)
    private int action;

    /**
     * 展示信息
     */
    @MobileDo.MobileField(key = 0x36e9)
    private String title;

    /**
     * 跳转链接
     */
    @MobileDo.MobileField(key = 0xa706)
    private String clickUrl;

    /**
     * 0-只展示 1-点击跳转 2-点击分享
     */
    public enum ActionEnum {
        SHOW(0),
        REDIRECT(1),
        SHARE(3);

        public int type;

        ActionEnum(int type) {
            this.type = type;
        }
    }
}
