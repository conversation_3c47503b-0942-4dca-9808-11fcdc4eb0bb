package com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.RelatedModuleCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedDealModuleVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/4 21:09
 */
public interface RelatedDealIdProcessor {

    List<Long> getRelatedDealGroupIds(RelatedModuleCtx ctx);

    RelatedDealModuleVO assemble(RelatedModuleCtx ctx, List<Long> ids);
}
