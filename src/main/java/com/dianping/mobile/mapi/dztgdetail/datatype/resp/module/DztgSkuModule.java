package com.dianping.mobile.mapi.dztgdetail.datatype.resp.module;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.sankuai.sig.botdefender.adapter.annotation.EncryptedLinkField;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@MobileDo(id = 0x889b)
public class DztgSkuModule implements Serializable {
    @MobileField(key = 0x241d)
    private List<String> skuAttrCnNameList = new ArrayList<>();
    @MobileField(key = 0x57dd)
    private List<String> skuAttrValueList = new ArrayList<>();
    @MobileField(key = 0xc56e)
//    @EncryptedLinkField(queries = {"shopid"})
    private String url;

    public List<String> getSkuAttrCnNameList() {
        return skuAttrCnNameList;
    }

    public void setSkuAttrCnNameList(List<String> skuAttrCnNameList) {
        this.skuAttrCnNameList = skuAttrCnNameList;
    }

    public List<String> getSkuAttrValueList() {
        return skuAttrValueList;
    }

    public void setSkuAttrValueList(List<String> skuAttrValueList) {
        this.skuAttrValueList = skuAttrValueList;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
