package com.dianping.mobile.mapi.dztgdetail.tab;

import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.struct.query.api.entity.dto.DealDetailDto;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.*;
import com.dianping.mobile.mapi.dztgdetail.tab.match.DealTagsBasedMatcher;
import com.dianping.mobile.mapi.dztgdetail.tab.match.DealTagsMatcher;
import com.dianping.mobile.mapi.dztgdetail.util.ListUtils;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Service
public class DealTagsBasedRelateDeals extends AbstractRelateDeals implements DealTagsBasedMatcher {

    @Resource
    DealTagsMatcher dealTagsMatcher;

    @Override
    public List<Integer> identifyByPublishCategory() {
        return Lists.newArrayList();
    }

    @Override
    protected SourceDataHolder getSourceDataHolder() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.tab.DealTagsBasedRelateDeals.getSourceDataHolder()");
        return new DealTagSourceDataHolder();
    }

    @Override
    public void loadBeforeRelate(BaseLoadParam param, SourceDataHolder holder) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.tab.DealTagsBasedRelateDeals.loadBeforeRelate(com.dianping.mobile.mapi.dztgdetail.tab.bo.BaseLoadParam,com.dianping.mobile.mapi.dztgdetail.tab.bo.SourceDataHolder)");
        super.loadBeforeRelate(param, holder);

        DealTagSourceDataHolder dealTagSourceDataHolder = (DealTagSourceDataHolder) holder;
        List<Future> dealTagFutures = productTagWrapper.preQueryDealGroupTags(ListUtils.convertInteger2LongList(param.getBaseData().getDpDealGroupIds()));
        dealTagSourceDataHolder.setDealTagMap(productTagWrapper.queryDealGroupTagIds(dealTagFutures));
    }

    @Override
    protected List<Integer> getRelatedDpDealGroupIds(BaseData baseData, SourceDataHolder holder) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.tab.DealTagsBasedRelateDeals.getRelatedDpDealGroupIds(BaseData,SourceDataHolder)");
        DealTagSourceDataHolder dealTagSourceDataHolder = (DealTagSourceDataHolder) holder;
        Map<Integer, List<Long>> dealTagMap = dealTagSourceDataHolder.getDealTagMap();
        if (MapUtils.isEmpty(dealTagMap)) {
            return null;
        }

        List<Long> dealTagsMatched = dealTagsMatched();
        return dealTagMap.entrySet().stream()
                .filter(entry -> match(dealTagsMatched, entry.getValue()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    @Override
    public void loadAfterRelate(BaseLoadParam param, SourceDataHolder holder) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.tab.DealTagsBasedRelateDeals.loadAfterRelate(com.dianping.mobile.mapi.dztgdetail.tab.bo.BaseLoadParam,com.dianping.mobile.mapi.dztgdetail.tab.bo.SourceDataHolder)");
        DealTagSourceDataHolder tagHolder = (DealTagSourceDataHolder) holder;

        super.loadAfterRelate(param, holder);
        Future dealAttrFuture = dealGroupWrapper.preGetDealGroupAttrs(param.getBaseData().getRelatedDpDealGroupIds(), dealAttrsToLoadAfterRelate());
        List<Future> dealStructFuture = dealGroupWrapper.preBatchQueryDealDetailInfo(param.getBaseData().getRelatedDpDealGroupIds());

        tagHolder.setDealBaseMap(dealGroupWrapper.getBatchQueryDealGroupBaseResult(tagHolder.getDealBaseFutures()));
        tagHolder.setDealSaleMap(dealStockSaleWrapper.getShopSceneSalesDisplay(tagHolder.getDealSaleFutures()));
        tagHolder.setDealAttrMap(dealGroupWrapper.getDealGroupAttrs(dealAttrFuture));
        tagHolder.setDealStructMap(dealGroupWrapper.getBatchQueryDealDetailInfoResult(dealStructFuture));
    }

    @Override
    protected DealTabHolder doListRelatedDealTabs(BaseData baseData, SourceDataHolder holder) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.tab.DealTagsBasedRelateDeals.doListRelatedDealTabs(com.dianping.mobile.mapi.dztgdetail.tab.bo.BaseData,com.dianping.mobile.mapi.dztgdetail.tab.bo.SourceDataHolder)");
        DealTagSourceDataHolder attrHolder = (DealTagSourceDataHolder) holder;
        Map<Integer, DealGroupBaseDTO> dealBaseMap = attrHolder.getDealBaseMap();
        Map<Integer, List<AttributeDTO>> dealAttrMap = attrHolder.getDealAttrMap();
        Map<Integer, DealDetailDto> dealDetailMap = attrHolder.getDealStructMap();

        int currentDpDealGroupId = baseData.getCurrentDpGroupId();
        if (!dealBaseMap.containsKey(currentDpDealGroupId)
                || !dealAttrMap.containsKey(currentDpDealGroupId)
                || !dealDetailMap.containsKey(currentDpDealGroupId)) {
            return null;
        }

        List<DealTab> relatedDealTabs = new ArrayList<>();
        DealTab currentTab = null;
        for (Map.Entry<Integer, DealGroupBaseDTO> entry: dealBaseMap.entrySet()) {
            Integer dpDealGroupId = entry.getKey();
            DealTab dealTab = buildDealTab(dpDealGroupId, holder);
            if (dealTab == null) {
                continue;
            }

            if (dpDealGroupId == currentDpDealGroupId) {
                currentTab = dealTab;
            } else {
                relatedDealTabs.add(dealTab);
            }
        }

        if (currentTab == null) {
            return null;
        }
        DealTabHolder dealTabHolder = new DealTabHolder();
        dealTabHolder.setCurrentTab(currentTab);
        dealTabHolder.setRelatedTabs(relatedDealTabs);
        return dealTabHolder;
    }

    protected List<String> dealAttrsToLoadAfterRelate() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.tab.DealTagsBasedRelateDeals.dealAttrsToLoadAfterRelate()");
        return Lists.newArrayList();
    }

    protected DealTab buildDealTab(Integer dpDealGroupId, SourceDataHolder holder) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.tab.DealTagsBasedRelateDeals.buildDealTab(java.lang.Integer,com.dianping.mobile.mapi.dztgdetail.tab.bo.SourceDataHolder)");
        return null;
    }

    @Override
    public List<Long> dealTagsMatched() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.tab.DealTagsBasedRelateDeals.dealTagsMatched()");
        return Lists.newArrayList();
    }

    @Override
    public boolean match(List<Long> a, List<Long> b) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.tab.DealTagsBasedRelateDeals.match(java.util.List,java.util.List)");
        return dealTagsMatcher.match(a, b);
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    protected static class DealTagSourceDataHolder extends SourceDataHolder {
        private Map<Integer, List<Long>> dealTagMap;
        private Map<Integer, List<AttributeDTO>> dealAttrMap;
        private Map<Integer, DealDetailDto> dealStructMap;
    }

}
