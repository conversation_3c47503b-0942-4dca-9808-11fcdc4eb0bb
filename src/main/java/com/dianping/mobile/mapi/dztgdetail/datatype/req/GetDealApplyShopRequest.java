package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-08-10
 */
@Data
@TypeDoc(description = "请求适用门店id的请求体")
@MobileRequest
public class GetDealApplyShopRequest implements IMobileRequest, Serializable {
    @FieldDoc(description = "团单Id", rule = "美团平台为美团团单ID，点评平台为点评团单ID")
    @MobileRequest.Param(name = "dealGroupId", required = true)
    private Long dealGroupId;

    @FieldDoc(description = "首页城市Id", rule = "首页城市Id，区分平台")
    @MobileRequest.Param(name = "homeCityId", required = true)
    private Integer homeCityId;

    @FieldDoc(description = "用户定位城市Id", rule = "用户定位城市Id，区分平台")
    @MobileRequest.Param(name = "gpsCityId")
    private Integer gpsCityId;

    @FieldDoc(description = "经度")
    @MobileRequest.Param(name = "userLng")
    private Double userLng;

    @FieldDoc(description = "纬度")
    @MobileRequest.Param(name = "userLat")
    private Double userLat;

    @FieldDoc(description = "门店Id", rule = "门店Id，区分平台，有就传")
    @MobileRequest.Param(name = "shopId")
    private Long shopId;
}
