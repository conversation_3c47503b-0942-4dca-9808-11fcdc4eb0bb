package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.gmkt.activ.api.enums.ExposureActivityTypeEnum;
import com.dianping.gmkt.activity.api.dto.DealActivityDTO;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
public class ActivitiesHelper {

    public static boolean isJuShengQianDeal(List<DealActivityDTO> dealActivities) {
        return isContainsSpecActivity(dealActivities, Lists.newArrayList(ExposureActivityTypeEnum.JSQ_NORMAL.getType(),
                ExposureActivityTypeEnum.JSQ_ACTIVITY.getType(), ExposureActivityTypeEnum.COST_EFFECTIVE.getType()));
    }

    public static boolean isContainsSpecActivity(List<DealActivityDTO> dealActivities, List<Integer> activityTypes) {
        if (CollectionUtils.isEmpty(dealActivities)) {
            return false;
        }
        return dealActivities.stream()
                .anyMatch(dealActivity -> dealActivity != null && activityTypes.contains(dealActivity.getActivityType()));
    }
}
