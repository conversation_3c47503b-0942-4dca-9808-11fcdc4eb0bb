package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.stock.dto.ProductGroupStock;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.MemberExclusiveProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.constants.PlusIcons;
import com.dianping.mobile.mapi.dztgdetail.common.enums.AtmosphereSceneEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MtLiveSaleStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.SaleStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealAtmosphereBarModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import com.dianping.mobile.mapi.dztgdetail.util.CustomAtmosphereUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.scrum.util.DateUtils;
import com.google.common.collect.Lists;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.LiveRoomDetail;
import com.sankuai.general.product.query.center.client.dto.DealTimeStockDTO;
import com.sankuai.general.product.query.center.client.enums.TimeStockStatusEnum;
import com.sankuai.general.product.query.center.client.util.DealGroupUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.Cons.WARM_UP_DEAL;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys.USING_STOCK_PLAN;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys.WARM_UP_START_TIME;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.PlusIcons.SECOND_KILL_MEDIATE_PROMO;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.PlusIcons.SECOND_KILL_UP_PROMO;

/**
 * @Author: <EMAIL>
 * @Date: 2024/6/9
 */
@Component
@Slf4j
public class AtmospherBuilderService {
    @Resource
    private DouHuBiz douHuBiz;

    public void buildAtmosphereBar(DealCtx ctx, DealGroupPBO result) {
        // 除特团氛围外优先级最高 && 命中实验
        if (CustomAtmosphereUtils.distributeSuperDealAtmosphere(ctx) && hitDisplayAtmosphereExp(ctx)) {
            CustomAtmosphereUtils.buildSuperDealAtmosphereBar(result);
        }

        //填充氛围条信息，设置售卖状态，要先于底bar的构造
        if (CollectionUtils.isNotEmpty(ctx.getDealExtraTypes()) && ctx.getDealExtraTypes().contains(WARM_UP_DEAL)) {
            buildDealAtmosphereBarAndSetSaleStatus(ctx, result);
        }else if (CollectionUtils.isNotEmpty(ctx.getDealExtraTypes()) && ctx.getDealExtraTypes().contains(MemberExclusiveProcessor.MALL_MEMBER_DEAL)) {
            // 填充会员专属氛围条信息
            buildMemberExclusiveAtmosphereBar(result);
        }

        // 美团美播小程序端，展示售卖开始、结束时间
        if (ctx.isMtLiveMinApp()) {
            buildDealAtmosphereBarFromMtLiveMiniApp(ctx, result);
        }
    }

    public boolean hitDisplayAtmosphereExp(DealCtx ctx) {
        String module = ctx.isMt() ? "MtSuperDealAtmosphereExp" : "DpSuperDealAtmosphereExp";
        ModuleAbConfig moduleAbConfig = douHuBiz.getAbExpResultByUuidAndDpid(ctx, module);
        if (Objects.isNull(moduleAbConfig) || CollectionUtils.isEmpty(moduleAbConfig.getConfigs())) {
            return false;
        }

        ctx.getModuleAbConfigs().add(moduleAbConfig);
        String expResult = douHuBiz.getExpResult(moduleAbConfig);
        if (expResult.contains("b")) {
            return true;
        }
        return false;
    }

    /**
     * 填充（秒杀）氛围条信息
     *
     * @param ctx
     * @param result
     */
    public void buildDealAtmosphereBarAndSetSaleStatus(DealCtx ctx, DealGroupPBO result) {
        if (result.getDealAtmosphereBarModules() == null) {
            result.setDealAtmosphereBarModules(Lists.newArrayList());
        }

        List<AttributeDTO> attrs = ctx.getAttrs();
        String warmUpValue = AttributeUtils.getFirstValue(attrs, WARM_UP_START_TIME);
        String usingStockPlanValue = AttributeUtils.getFirstValue(attrs, USING_STOCK_PLAN);

        DealAtmosphereBarModule dealAtmosphereBarModule = new DealAtmosphereBarModule();
        if (StringUtils.isNotBlank(warmUpValue) && StringUtils.isBlank(usingStockPlanValue)) {
            buildDealAtmosphereBarWarmUpOnly(ctx, warmUpValue, dealAtmosphereBarModule);
        }

        if (StringUtils.isBlank(warmUpValue) && StringUtils.isNotBlank(usingStockPlanValue)) {
            buildDealAtmosphereBarUsingStockPlanOnly(ctx, dealAtmosphereBarModule);
        }

        if (StringUtils.isNotBlank(warmUpValue) && StringUtils.isNotBlank(usingStockPlanValue)) {
            buildDealAtmosphereBarBothOn(ctx, warmUpValue, dealAtmosphereBarModule);
        }

        result.getDealAtmosphereBarModules().add(dealAtmosphereBarModule);
    }

    public void buildMemberExclusiveAtmosphereBar(DealGroupPBO result){
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.buildMemberExclusiveAtmosphereBar(com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO)");
        if (result.getDealAtmosphereBarModules() == null) {
            result.setDealAtmosphereBarModules(Lists.newArrayList());
        }
        DealAtmosphereBarModule dealAtmosphereBarModule = new DealAtmosphereBarModule();
        dealAtmosphereBarModule.setScene(AtmosphereSceneEnum.MEMBER_EXCLUSIVE.sceneName);
        dealAtmosphereBarModule.setText("会员价");
        dealAtmosphereBarModule.setBaseMapUrl(PlusIcons.MEMBER_EXCLUSIVE);
        result.getDealAtmosphereBarModules().add(dealAtmosphereBarModule);
    }

    private void buildDealAtmosphereBarWarmUpOnly(DealCtx ctx, String warmUpValue, DealAtmosphereBarModule dealAtmosphereBarModule) {
        Date warmUpDate = DealGroupUtils.convertString2Date(warmUpValue);
        Date now = DateUtils.currentDate();
        Date beginDate = ctx.getDealGroupBase().getBeginDate();
        Date endDate = ctx.getDealGroupBase().getEndDate();
        if (beginDate.after(now) && !warmUpDate.after(now)) {
            dealAtmosphereBarModule.setScene(AtmosphereSceneEnum.SECOND_KILL.sceneName);
            dealAtmosphereBarModule.setCountDownTs(beginDate.getTime());
            dealAtmosphereBarModule.setTimeSubFix("后开抢");
            dealAtmosphereBarModule.setBaseMapUrl(SECOND_KILL_UP_PROMO);

            ctx.setSaleStatus(SaleStatusEnum.COMING_SOON.saleStatusName);
            return;
        }

        if (now.after(endDate)) {
            dealAtmosphereBarModule.setScene(AtmosphereSceneEnum.SECOND_KILL.sceneName);
            dealAtmosphereBarModule.setText("抢购结束");
            dealAtmosphereBarModule.setBaseMapUrl(SECOND_KILL_UP_PROMO);
            ctx.setSaleStatus(SaleStatusEnum.END_OF_SALE.saleStatusName);
            return;
        }

        if (!now.before(beginDate)) {
            dealAtmosphereBarModule.setScene(AtmosphereSceneEnum.SECOND_KILL.sceneName);
            dealAtmosphereBarModule.setTimePreFix("距结束");
            dealAtmosphereBarModule.setCountDownTs(endDate.getTime());
            dealAtmosphereBarModule.setBaseMapUrl(SECOND_KILL_UP_PROMO);

            ctx.setSaleStatus(SaleStatusEnum.SNAP_UP_NOW.saleStatusName);
        }
    }

    private void buildDealAtmosphereBarUsingStockPlanOnly(DealCtx ctx, DealAtmosphereBarModule dealAtmosphereBarModule) {
        Date beginDate = ctx.getDealGroupBase().getBeginDate();
        Date now = DateUtils.currentDate();
        DealTimeStockDTO dealTimeStockDTO = ctx.getDealTimeStockDTO();

        if (dealTimeStockDTO == null || TimeStockStatusEnum.NOT_DAILYTIMESTOCK_DEAL.getCode() == dealTimeStockDTO.getTimeStockStatus()) {
            return;
        }

        if (!beginDate.after(now) && TimeStockStatusEnum.NOT_STARTED_DAILYTIMESTOCK_DEAL.getCode() == dealTimeStockDTO.getTimeStockStatus()) {
            dealAtmosphereBarModule.setScene(AtmosphereSceneEnum.SECOND_KILL.sceneName);
            dealAtmosphereBarModule.setCountDownTs(dealTimeStockDTO.getNextStartTime());
            dealAtmosphereBarModule.setTimeSubFix("后开始");
            dealAtmosphereBarModule.setBaseMapUrl(SECOND_KILL_UP_PROMO);

            ctx.setSaleStatus(SaleStatusEnum.COMING_SOON.saleStatusName);
            return;
        }

        if (!beginDate.after(now) && TimeStockStatusEnum.IN_PROCESS_DAILYTIMESTOCK_DEAL.getCode() == dealTimeStockDTO.getTimeStockStatus()) {
            dealAtmosphereBarModule.setScene(AtmosphereSceneEnum.SECOND_KILL.sceneName);
            dealAtmosphereBarModule.setTimePreFix("距结束");
            dealAtmosphereBarModule.setCountDownTs(dealTimeStockDTO.getCurrentEndTime());
            dealAtmosphereBarModule.setBaseMapUrl(SECOND_KILL_UP_PROMO);

            ctx.setSaleStatus(SaleStatusEnum.SNAP_UP_NOW.saleStatusName);
            return;
        }

        if (!beginDate.after(now) && TimeStockStatusEnum.CURRENT_TIME_END_DAILYTIMESTOCK_DEAL.getCode() == dealTimeStockDTO.getTimeStockStatus()) {
            dealAtmosphereBarModule.setScene(AtmosphereSceneEnum.SECOND_KILL.sceneName);
            dealAtmosphereBarModule.setTimeSubFix("后下一场");
            dealAtmosphereBarModule.setCountDownTs(dealTimeStockDTO.getNextStartTime());
            dealAtmosphereBarModule.setBaseMapUrl(SECOND_KILL_UP_PROMO);

            ctx.setSaleStatus(SaleStatusEnum.SOLD_OUT.saleStatusName);
            return;
        }

        if (!beginDate.after(now) && TimeStockStatusEnum.CURRENT_STOCK_END_DAILYTIMESTOCK_DEAL.getCode() == dealTimeStockDTO.getTimeStockStatus()) {
            dealAtmosphereBarModule.setScene(AtmosphereSceneEnum.SECOND_KILL.sceneName);
            dealAtmosphereBarModule.setTimeSubFix("后下一场");
            dealAtmosphereBarModule.setCountDownTs(dealTimeStockDTO.getNextStartTime());
            dealAtmosphereBarModule.setBaseMapUrl(SECOND_KILL_UP_PROMO);

            ctx.setSaleStatus(SaleStatusEnum.SOLD_OUT.saleStatusName);
            return;
        }

        if (!beginDate.after(now) && TimeStockStatusEnum.NO_NEXT_DAILYTIMESTOCK_DEAL.getCode() == dealTimeStockDTO.getTimeStockStatus()) {
            dealAtmosphereBarModule.setScene(AtmosphereSceneEnum.SECOND_KILL.sceneName);
            dealAtmosphereBarModule.setBaseMapUrl(SECOND_KILL_MEDIATE_PROMO);

            ctx.setSaleStatus(SaleStatusEnum.END_OF_SALE.saleStatusName);
            return;
        }

        if (!beginDate.after(now) && TimeStockStatusEnum.NO_STOCK_DEAL.getCode() == dealTimeStockDTO.getTimeStockStatus()) {
            dealAtmosphereBarModule.setScene(AtmosphereSceneEnum.SECOND_KILL.sceneName);
            dealAtmosphereBarModule.setBaseMapUrl(SECOND_KILL_MEDIATE_PROMO);

            ctx.setSaleStatus(SaleStatusEnum.END_OF_SALE.saleStatusName);
        }

    }

    private void buildDealAtmosphereBarBothOn(DealCtx ctx, String warmUpValue, DealAtmosphereBarModule dealAtmosphereBarModule) {
        Date warmUpDate = DealGroupUtils.convertString2Date(warmUpValue);
        Date now = DateUtils.currentDate();
        Date beginDate = ctx.getDealGroupBase().getBeginDate();
        Long nextStartTime = ctx.getDealTimeStockDTO().getNextStartTime();
        if (beginDate.after(now) && !warmUpDate.after(now)) {
            dealAtmosphereBarModule.setScene(AtmosphereSceneEnum.SECOND_KILL.sceneName);
            dealAtmosphereBarModule.setTimeSubFix("后开抢");
            dealAtmosphereBarModule.setCountDownTs(nextStartTime);
            dealAtmosphereBarModule.setBaseMapUrl(SECOND_KILL_UP_PROMO);

            ctx.setSaleStatus(SaleStatusEnum.COMING_SOON.saleStatusName);
            return;
        }

        if (now.after(beginDate)) {
            buildDealAtmosphereBarUsingStockPlanOnly(ctx, dealAtmosphereBarModule);
        }
    }


    /**
     * 从小程序直播间场景进入团详页，构建氛围条
     * @param ctx 团详上下文
     * @param result 团详返回数据
     */
    private void buildDealAtmosphereBarFromMtLiveMiniApp(DealCtx ctx, DealGroupPBO result) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.buildDealAtmosphereBarFromMtLiveMiniApp(DealCtx,DealGroupPBO)");
        if (result.getDealAtmosphereBarModules() == null) {
            result.setDealAtmosphereBarModules(Lists.newArrayList());
        }

        DealAtmosphereBarModule dealAtmosphereBar = new DealAtmosphereBarModule();
        dealAtmosphereBar.setScene(AtmosphereSceneEnum.MEITUAN_MLIVE_MINIAPP.sceneName);
        dealAtmosphereBar.setPriceNameDesc("快闪价");
        // 氛围条底图
        dealAtmosphereBar.setBaseMapUrl(LionConfigUtils.getMtLiveMiniAppAtmosphereImg(ctx.getCategoryId()));
        Date beginSaleDate = null;
        Date endSaleDate = null;
        Date now = DateUtils.currentDate();
        if (Objects.nonNull(ctx.getDealGroupDTO()) && Objects.nonNull(ctx.getDealGroupDTO().getBasic())) {
            endSaleDate = DealGroupUtils.convertString2Date(ctx.getDealGroupDTO().getBasic().getEndSaleDate());
            beginSaleDate = DealGroupUtils.convertString2Date(ctx.getDealGroupDTO().getBasic().getBeginSaleDate());
        }
        // 未开售
        if (Objects.nonNull(beginSaleDate) && now.before(beginSaleDate)) {
            dealAtmosphereBar.setTimePreFix("开售时间");
            dealAtmosphereBar.setCountDownTs(beginSaleDate.getTime());
            dealAtmosphereBar.setStockText(getStockText(ctx));
            ctx.setMtLiveSaleStatusEnum(MtLiveSaleStatusEnum.COMING_SOON);
        } else if (Objects.nonNull(endSaleDate) && now.before(endSaleDate)) { // 已开售
            dealAtmosphereBar.setTimePreFix("距结束");
            dealAtmosphereBar.setCountDownTs(endSaleDate.getTime());
            dealAtmosphereBar.setStockText(getStockText(ctx));
            ctx.setMtLiveSaleStatusEnum(MtLiveSaleStatusEnum.SNAP_UP_NOW);
        } else {
            dealAtmosphereBar.setStockText(getStockText(ctx));
            dealAtmosphereBar.setCountDownTs(endSaleDate.getTime());
            ctx.setMtLiveSaleStatusEnum(MtLiveSaleStatusEnum.END_OF_SALE);
        }
        // 已抢光：立即抢购|售卖结束 + 库存为0
        if ((Objects.equals(ctx.getMtLiveSaleStatusEnum(), MtLiveSaleStatusEnum.SNAP_UP_NOW)
                || Objects.equals(ctx.getMtLiveSaleStatusEnum(), MtLiveSaleStatusEnum.END_OF_SALE)) && getStock(ctx) == 0) {
            ctx.setMtLiveSaleStatusEnum(MtLiveSaleStatusEnum.SOLD_OUT);
        }
        // 已下架
        if (Objects.nonNull(ctx.getDealGroupBase()) && (ctx.getDealGroupBase().getStatus() <= 0 || ctx.getDealGroupBase().getStatus() == 3)) {
            ctx.setMtLiveSaleStatusEnum(MtLiveSaleStatusEnum.OUT_OF_STOCK);
        }
        LiveRoomDetail liveRoomDetail = ctx.getPrivateLiveRoomInfo();
        if (Objects.nonNull(liveRoomDetail)) {
            dealAtmosphereBar.setText(liveRoomDetail.getAtmosphereText());
        }
        result.getDealAtmosphereBarModules().add(dealAtmosphereBar);
    }

    private String getStockText(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.getStockText(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        int stock = getStock(ctx);
        return stock > 0 && stock <= 10 ? String.format("仅剩%s件", stock) : StringUtils.EMPTY;
    }

    /**
     * 获取团单库存
     * @param ctx 团单上下文
     * @return 团单库存，-1标识库存无限
     */
    private int getStock(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.getStock(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        ProductGroupStock dealGroupStock = ctx.getDealGroupStock();
        if (ctx.isMt()) {
            return dealGroupStock.getMtTotal() != 0 ? dealGroupStock.getMtRemain() : -1;
        }
        return dealGroupStock.getDpTotal() != 0 ? dealGroupStock.getDpRemain() : -1;
    }

}
