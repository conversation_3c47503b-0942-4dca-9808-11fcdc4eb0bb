package com.dianping.mobile.mapi.dztgdetail.rcf.repository.bff.cache;

import com.dianping.deal.bff.cache.DealBffCacheQueryService;
import com.dianping.deal.bff.cache.dto.RcfDealBffCommonParamDTO;
import com.dianping.deal.bff.cache.response.DealBffCacheQueryResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * @Author: guangyujie
 * @Date: 2024/11/26 17:22
 */
@Component
public class DealBffCacheAclService {

    @Autowired
    @Qualifier("dealBffCacheQueryService")
    private DealBffCacheQueryService dealBffCacheQueryService;

    public DealBffCacheQueryResponse query(RcfDealBffCommonParamDTO param) {
        return dealBffCacheQueryService.query(param);
    }

}
