package com.dianping.mobile.mapi.dztgdetail.common.model;

import lombok.Data;

import java.util.concurrent.Future;

/**
 * @Author: <EMAIL>
 * @Date: 2024/10/28
 */
@Data
public class RecommendUserBaseInfoModel {
    /**
     * 用户经度
     */
    private Double userLng;

    /**
     * 用户纬度，火星坐标系GCJ02
     */
    private Double userLat;

    /**
     * 用户定位城市ID
     */
    private Integer locCityId;

    /**
     * 用户选择城市id
     */
    private Integer cityId;

    /**
     * 点评dpid
     */
    private String dpid;

    /**
     * 美团uuid
     */
    private String uuid;

    /**
     * 美团用户id
     */
    private Long mtUserId;

    /**
     * 点评用户id
     */
    private Long dpUserId;

    /**
     * 用户app版本信息
     */
    private String appVersion;

    /**
     * 用户网络信息
     */
    private String network;

    /**
     * 用户系统信息
     */
    private String os;

    /**
     * 用户ip信息
     */
    private String userIp;

    /**
     * app,"dp"
     */
    private String appType;

    /**
     * 用户经度
     */
    private String deviceModel;

    /**
     * 防作弊字段
     */
    private String mtsiScore;
    /**
     * 防作弊字段
     */
    private String mtsiFlag;

    /**
     * 用户选择城市id 点评需要映射成美团城市id，到店平台推荐需求
     */
    private Integer mtCityId;

    /**
     * 用户所在行政区
     */
    private Integer districtId;

    /**
     * 用户所在商圈
     */
    private Integer regionId;

    /**
     * 小程序使用
     */
    private String openId;

    /**
     * dpCityId
     */
    private Integer dpCityId;

    private Future mtLocationFuture;

    private Future regionAllDTOFuture;

    private Future dpDistrictFuture;
}
