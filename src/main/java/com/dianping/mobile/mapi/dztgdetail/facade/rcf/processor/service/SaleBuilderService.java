package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.deal.sales.common.datatype.SalesDisplayDTO;
import com.dianping.deal.sales.common.datatype.SalesDisplayInfoDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.EduTrailAuditionNumProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.entity.CareCenterHouseLeadsCountConfig;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.NumbersUtils;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.leads.count.thrift.api.NewLeadsCountService;
import com.sankuai.leads.count.thrift.dto.LeadsCountADTO;
import com.sankuai.leads.count.thrift.dto.LeadsCountRespDTO;
import com.sankuai.leads.count.thrift.dto.SalesDTO;
import com.sankuai.leads.count.thrift.dto.SalesSubjectDTO;
import com.sankuai.leads.count.thrift.dto.req.QueryLeadsSalesReqDTO;
import com.sankuai.leads.count.thrift.dto.resp.QueryLeadsSalesRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: <EMAIL>
 * @Date: 2024/6/9
 */
@Component
@Slf4j
public class SaleBuilderService {
    @Autowired
    private DouHuBiz douHuBiz;
    @Resource
    private NewLeadsCountService newLeadsCountService;
    @Resource
    private EduTrailAuditionNumProcessor eduTrailAuditionNumProcessor;

    private static final String SALE_ZERO = "已预约0";

    public void setSaleDescAndSaleDescStr(DealCtx ctx, DealGroupPBO result) {
        // 使用新销量结果数据替换老接口
        if (Objects.nonNull(ctx.getProductSaleDTO()) && Objects.nonNull(ctx.getSalesDisplayDTO())) {
            SalesDisplayDTO oldSaleDTO = ctx.getSalesDisplayDTO();
            SalesDisplayInfoDTO newSaleDTO = ctx.getProductSaleDTO();
            int newSaleNum = Optional.ofNullable(newSaleDTO.getSalesNum()).map(Number::intValue).orElse(0);
            if (newSaleNum > 0) {
                oldSaleDTO.setSales(newSaleNum);
                oldSaleDTO.setSalesTag(newSaleDTO.getSalesTag());
            }
        }
        if(ctx.isFreeDeal()){
            if (ctx.getCategoryId()==1611){
                result.setSaleDesc(buildResvCountTag(ctx));
                result.setSaleDescStr(buildResvCountTag(ctx));
                return;
            }
            buildFreeDealSaleDesc(ctx, result);
            return;
        }

        //月子中心新建房型团单三级类目126041
        CareCenterHouseLeadsCountConfig careCenterHouseLeadsCountConfig = isEnableCareCenterLeadsCount(ctx);
        if (careCenterHouseLeadsCountConfig != null || DealUtils.isWeddingSpecialWithPoiAndDealCategory(ctx)) {
            String saleName = buildSaleDesc(ctx);
            if (!SALE_ZERO.equals(saleName)) {
                result.setSaleDesc(saleName);
                result.setSaleDescStr(saleName);
            }
            return;
        }

        // 预订团单特殊处理
        if (DealCtxHelper.isPreOrderDeal(ctx)) {
            int preOrderSales = 0;
            // 展示团购销量
            if (LionConfigUtils.enablePreOrderDealGroupSale()) {
                if (ctx.getSalesDisplayDTO() == null || ctx.getSalesDisplayDTO().getSales() <= 0) {
                    return;
                }
                preOrderSales = ctx.getSalesDisplayDTO().getSales();
            } else {
                // 仅预订销量
                if (NumbersUtils.lessThanAndEqualZero(ctx.getPreOrderSales())) {
                    return;
                }
                preOrderSales = (int) ctx.getPreOrderSales();
            }

            buildSaleDescForPreOrderDeal(preOrderSales, result);
            // 添加销量颜色AB实验
            executeSalesColorAbExp(ctx, result);
            return;
        }

        if (ctx.getSalesDisplayDTO() != null && ctx.getSalesDisplayDTO().getSales() > 0) {
            result.setSaleDesc(ctx.getSalesDisplayDTO().getSalesTag());
            result.setSaleDescStr(ctx.getSalesDisplayDTO().getSalesTag());
            //添加销量颜色AB实验
            executeSalesColorAbExp(ctx, result);
//            // 超级团购销售文案拼接
//            if (CustomAtmosphereUtils.distributeSuperDealAtmosphere(ctx)) {
//                result.setSaleDescStr(CustomAtmosphereUtils.getSaleDescStr(ctx.getBaseSaleMap()));
//            }
        } else {
            result.setPriorTag("新品");
        }
    }

    private String buildResvCountTag(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.buildResvCountTag(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        LeadsCountADTO leadsCountADTO = buildLeadsCountADTO(ctx);
        LeadsCountRespDTO leadsCountRespDTO = newLeadsCountService.queryCountByCache(leadsCountADTO);
        int count = leadsCountRespDTO.getCount();
        if (count < 10) {
            return null;
        }
        return "已预约" + count;
    }

    /**
     * 调用新的留资接口查询预约数量
     * 参考文档
     * @param ctx
     * @return
     */
    private String buildSaleDesc(DealCtx ctx) {
        try {
            QueryLeadsSalesReqDTO param = eduTrailAuditionNumProcessor.buildRequest(ctx);
            QueryLeadsSalesRespDTO queryLeadsSalesRespDTO = newLeadsCountService.queryLeadsSales(param);

            if (queryLeadsSalesRespDTO == null) {
                return SALE_ZERO;
            }

            String referId = ctx.isMt() ? String.valueOf(ctx.getDealGroupDTO().getMtDealGroupId()) : String.valueOf(ctx.getDealGroupDTO().getDpDealGroupId());
            SalesDTO retrievedSalesDTO = getSalesDTOByReferId(queryLeadsSalesRespDTO, referId);

            if (retrievedSalesDTO != null && MapUtils.isNotEmpty(retrievedSalesDTO.getCycleSales())) {
                Integer cycleSales360 = retrievedSalesDTO.getCycleSales().get(360);
                if (cycleSales360 == null) {
                    return SALE_ZERO;
                }
                return "已预约" + cycleSales360;
            }
        } catch (Exception e) {
            log.error("buildSaleDesc error", e);
        }
        return SALE_ZERO;
    }

    private SalesDTO getSalesDTOByReferId(QueryLeadsSalesRespDTO queryLeadsSalesRespDTO, String referId) {
        try {
            if (queryLeadsSalesRespDTO == null || queryLeadsSalesRespDTO.getSalesMap() == null) {
                return null;
            }
            for (Map.Entry<SalesSubjectDTO, SalesDTO> entry : queryLeadsSalesRespDTO.getSalesMap().entrySet()) {
                SalesSubjectDTO salesSubjectDTO = entry.getKey();
                if (salesSubjectDTO != null && salesSubjectDTO.getSubjectMap() != null) {
                    String refereedValue = salesSubjectDTO.getSubjectMap().get("referid");
                    if (Objects.equals(referId, refereedValue)) {
                        return entry.getValue();
                    }
                }
            }
        }catch (Exception e) {
            log.error("getSalesDTOByReferId error", e);
        }
        return null;
    }

    private void buildFreeDealSaleDesc(DealCtx ctx, DealGroupPBO result) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.buildFreeDealSaleDesc(DealCtx,DealGroupPBO)");
        if (Objects.isNull(ctx.getFreeDealType()) || Objects.isNull(ctx.getFreeDealConfig())) {
            return;
        }
        if (ctx.getSalesDisplayDTO() == null) {
            return;
        }

        String prefixText = StringUtils.isBlank(ctx.getFreeDealConfig().getSaleDescPrefix()) ? "" : ctx.getFreeDealConfig().getSaleDescPrefix();
        result.setSaleDesc(prefixText + ctx.getSalesDisplayDTO().getSales());
        result.setSaleDescStr(prefixText + ctx.getSalesDisplayDTO().getSales());
    }

    private void executeSalesColorAbExp(DealCtx ctx, DealGroupPBO result) {
        if (ctx == null) {
            return;
        }
        List<Integer> categoryList = Lion.getList(LionConstants.AB_EXP_CATEGORY_IDS, Integer.class, Lists.newArrayList(303, 304));
        if (!categoryList.contains(ctx.getCategoryId())) {
            return;
        }
        //目前AB实验只做洗浴和足疗两个类目
        String module = ctx.isMt() ? "MTSalesColorExp" : "DPSalesColorExp";
        ModuleAbConfig moduleAbConfig = douHuBiz.getAbExpResult(ctx, module);
        String saleDesc = result.getSaleDesc();
        if (moduleAbConfig != null && moduleAbConfig.getConfigs().get(0).isUseNewStyle()) {
            result.setSaleDesc(JsonLabelUtil.salesColorJson(ctx.isMt(), saleDesc, saleDesc));
        }
        result.setAbConfigModel(moduleAbConfig);
    }

    @NotNull
    private LeadsCountADTO buildLeadsCountADTO(DealCtx ctx) {
        LeadsCountADTO leadsCountADTO = new LeadsCountADTO();
        leadsCountADTO.setSubjectId(String.valueOf(ctx.getDealId4P()));
        leadsCountADTO.setSubjectType(com.sankuai.leads.count.thrift.enums.SubjectTypeEnum.DEALID_TYPE.getCode());
        leadsCountADTO.setLogicExpressionId(35);
        leadsCountADTO.setBizId(1037);
        return leadsCountADTO;
    }

    /**
     * 根据三级团单与lion中配置的服务类型判断是否开开启
     * @param ctx
     * @return
     */
    public CareCenterHouseLeadsCountConfig isEnableCareCenterLeadsCount(DealCtx ctx) {
        if (ctx == null || ctx.getDealGroupDTO() == null || ctx.getDealGroupDTO().getCategory() == null) {
            return null;
        }
        CareCenterHouseLeadsCountConfig careCenterHouseLeadsCountConfig = null;
        try {
            String config = Lion.getString(LionConstants.APP_KEY, LionConstants.CARE_CENTER_HOUSE_LEADS_COUNT);
            careCenterHouseLeadsCountConfig = JSON.parseObject(config, CareCenterHouseLeadsCountConfig.class);
             //团购商品信息
            DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
            Long serviceTypeId = dealGroupDTO.getCategory().getServiceTypeId();
            if (careCenterHouseLeadsCountConfig == null || serviceTypeId == null || CollectionUtils.isEmpty(careCenterHouseLeadsCountConfig.getServiceTypeIds()) || !careCenterHouseLeadsCountConfig.getServiceTypeIds().contains(serviceTypeId.intValue())) {
                return null;
            }
        } catch (Exception e) {
            log.error("SaleBuilderService isEnableCareCenterLeadsCount error!", e);
        }
        return careCenterHouseLeadsCountConfig;
    }

    private void buildSaleDescForPreOrderDeal(int sales, DealGroupPBO result) {
        if (result == null || sales <= 0) {
            return;
        }
        String salePrefix = "已订";
        String saleSuffix = "0";
        if (sales > 0 && sales < 100) {
            saleSuffix = String.valueOf(sales);
        } else if (sales >= 100 && sales <= 1000) {
            saleSuffix = String.format("%d+", sales / 10 * 10);
        } else if (sales > 1000 && sales <= 10000) {
            saleSuffix = String.format("%d+", sales / 100 * 100);
        } else {
            saleSuffix = String.format("%.1f万+", 1.0 * sales / 10000);
        }
        StringBuilder res = new StringBuilder();
        res.append(salePrefix).append(saleSuffix);
        result.setSaleDesc(res.toString());
        result.setSaleDescStr(res.toString());
    }



}
