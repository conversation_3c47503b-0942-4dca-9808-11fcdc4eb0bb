package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ReserveProductWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.FreeDealEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.helper.*;
import com.dianping.mobile.mapi.dztgdetail.util.*;
import com.google.common.collect.Lists;
import com.sankuai.trade.general.reserve.response.ReserveResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper.isReservationEmpty;

/**
 * @author: wuwenqiang
 * @create: 2024-11-06
 * @description:
 */
@Service
@Slf4j
public class RemindBuilderService {

    @Autowired
    private ReserveProductWrapper reserveProductWrapper;

    public List<String> getReservationInfo(DealCtx ctx) {
        List<String> reservationInfo = new ArrayList<>();
        String reservation = null;
        //从上单信息（团单属性）中判断是否需预约、是否支持上门、是否支持到店
        boolean needReservation = DealAttrHelper.needReservation(ctx.getAttrs());
        boolean supportHome = DealAttrHelper.isSupportHomeService(ctx.getAttrs());
        boolean supportShop = DealAttrHelper.isSupportShopService(ctx.getAttrs());
        // 判断是否为预订单，仅支持可预约团单
        boolean needPreOrder = DealCtxHelper.isPreOrderDeal(ctx);
        if (ReserveProductWrapper.reserveAfterPurchase(ctx.getCategoryId())) {
            if (reserveOnline(ctx)) {
                reservation = needPreOrder ? "在线预订" : "在线预约";
            } else if (supportHome) {
                reservation = needPreOrder ? "预订上门" : "预约上门";
            } else if (supportShop && needReservation) {
                reservation = needPreOrder ? "预订到店" : "预约到店";
            } else if (needReservation) {
                reservation = needPreOrder ? "需预订" : "需预约";
            }
        } else if (needReservation) {
            reservation = needPreOrder ? "需预订" : "需预约";
        }
        if (reservation != null) {
            reservationInfo.add(reservation);
        }
        // 指定类目并且预约信息为不为"是"显示"免预约", 若指定类目预约信息为空时不显示"免预约"
        if ((!needReservation && !forceReserve(ctx.getCategoryId())) &&
                (!isReservationEmpty(ctx.getAttrs(), ctx.getDealGroupDTO()))) {
            reservationInfo.add("免预约");
        }
        return reservationInfo;
    }


    /**
     * 调用交易接口trade-general-reserve-api判断当前团单是否支持在线预约
     *
     * @param ctx
     * @return
     */
    private boolean reserveOnline(DealCtx ctx) {
        try {
            ReserveResponse<Boolean> reserveResponse = reserveProductWrapper.getFutureResult(ctx);
            if (reserveResponse != null && reserveResponse.isSuccess()) {
                return reserveResponse.getResult();
            }
        } catch (Exception e) {
            log.error("reserveOnlineFuture err, dpGroupId : {}", ctx != null ? ctx.getDpId() : 0, e);
            return false;
        }
        return false;
    }

    /**
     * 是否强制预约
     *
     * @param categoryId
     * @return
     */
    private boolean forceReserve(int categoryId) {
        List<Integer> config = Lion.getList(LionConstants.FORCE_BOOKING_PUBLISH_CATEGORY, Integer.class, Lists.newArrayList());
        return CollectionUtils.isNotEmpty(config) && config.contains(categoryId);
    }
}
