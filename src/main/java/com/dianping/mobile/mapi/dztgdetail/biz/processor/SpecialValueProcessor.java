package com.dianping.mobile.mapi.dztgdetail.biz.processor;


import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealActivityWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: zhangyuan103
 * @Date: 2025/7/11
 * @Description: 超值特惠团购
 */
public class SpecialValueProcessor extends AbsDealProcessor {

    @Resource
    private DealActivityWrapper dealActivityWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        return DealUtils.isWeddingLeadsDeal(ctx) || DealUtils.isLeadsDeal(ctx);
    }

    @Override
    public void prepare(DealCtx ctx) {
        ctx.getFutureCtx().setSpecialValueFuture(dealActivityWrapper.preQuerySpecialValueDeal(ctx));
    }

    @Override
    public void process(DealCtx ctx) {
        if (Objects.isNull(ctx.getFutureCtx()) || Objects.isNull(ctx.getFutureCtx().getSpecialValueFuture())) {
            return;
        }
        ctx.setHitSpecialValueDeal(dealActivityWrapper.hasSpecialValueDeal(ctx.getFutureCtx().getSpecialValueFuture()));
    }
}
