package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * 到综团购曝光券模型
 */
@Data
@MobileDo(id = 0x7ee7)
public class DztgPromoExposureInfoVO implements Serializable {

    /**
     * 	分流位id，投放券专属字段
     */
    @MobileDo.MobileField(key = 0xc2dd)
    private Long flowId;

    /**
     * 投放活动id，活动投放券专属
     */
    @MobileDo.MobileField(key = 0x1b0d)
    private Long resourceActivityId;

    /**
     * 活动id，投放券专属字段
     */
    @MobileDo.MobileField(key = 0xe91)
    private Long activityId;

    /**
     * 	物料id，投放券专属字段
     */
    @MobileDo.MobileField(key = 0x2a2c)
    private String materialId;

    /**
     * rowkey，投放券专属字段
     */
    @MobileDo.MobileField(key = 0x2efe)
    private String rowKey;

    /**
     * 传1
     */
    @MobileDo.MobileField(key = 0x7cd6)
    private Integer couponType;

    /**
     * 金额
     */
    @MobileDo.MobileField(key = 0xfbe2)
    private String amount;

    /**
     * 券标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * 券类型，0：普通券；6：膨胀券
     */
    @MobileDo.MobileField(key = 0x59e0)
    private Long couponValueType;

    /**
     * 副标题
     */
    @MobileDo.MobileField(key = 0xd894)
    private String subTitle;

    /**
     * 是否可领取 （如果可领取是，右侧展示点击领取按钮，如果不可领取，右侧展示倒计时时间）
     */
    @MobileDo.MobileField(key = 0x33f4)
    private Boolean canAssign;

    /**
     * 时间描述字段
     */
    @MobileDo.MobileField(key = 0x12d8)
    private String timeDesc;

    /**
     * 时间描述字段
     */
    @MobileDo.MobileField(key = 0xbfe8)
    private String timeSubDesc;

    /**
     * 券使用结束时间
     */
    @MobileDo.MobileField(key = 0x7950)
    private Long useEndTime;
}
