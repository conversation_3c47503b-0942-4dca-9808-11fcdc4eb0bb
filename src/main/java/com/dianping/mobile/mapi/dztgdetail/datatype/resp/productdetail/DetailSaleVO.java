package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-02-26
 * @desc 销量信息模型
 */
@Data
@MobileDo(id = 0xc556)
@TypeDoc(description = "销量信息模型")
public class DetailSaleVO implements Serializable {

    @FieldDoc(description = "销量标签")
    @MobileField(key = 0xa2c0)
    private String saleTag;

    @FieldDoc(description = "字体颜色")
    @MobileField(key = 0xeead)
    private String textColor;
}
