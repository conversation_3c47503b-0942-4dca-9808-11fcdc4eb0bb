package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2024/4/25
 */
@Data
@TypeDoc(description = "拼团成员信息")
@MobileDo(id = 0x4e73)
public class PinTuanMemberModule implements Serializable {
    @FieldDoc(description = "拼团概要信息")
    @MobileDo.MobileField(key = 0xb01f)
    private String generalInfo;

    @FieldDoc(description = "头像列表")
    @MobileDo.MobileField(key = 0x19eb)
    private List<String> avatars;
}
