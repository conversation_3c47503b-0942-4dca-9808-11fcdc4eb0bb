package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.healthExamination;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/10/18 11:03 上午
 */
@MobileDo(id = 0x2258)
public class HeaderItemVO implements Serializable {
    /**
     * 表头名
     */
    @MobileField(key = 0xef84)
    private String data;

    /**
     * 表头key
     */
    @MobileField(key = 0x9e5e)
    private String key;

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }
}