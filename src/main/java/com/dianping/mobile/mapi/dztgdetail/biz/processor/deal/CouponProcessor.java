package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.CommissionWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PromoWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.tgc.open.entity.*;
import com.sankuai.nib.mkt.common.base.enums.propertyValueType.ClientTypeEnum;

import javax.annotation.Resource;
import java.util.concurrent.Future;

public class CouponProcessor extends AbsDealProcessor {

    @Resource
    private PromoWrapper promoWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        return true;
    }

    @Override
    public void prepare(DealCtx ctx) {
        if(ctx.isEnableCardStyle() || ctx.isEnableCardStyleV2()) {
            BatchExProxyCouponRequest batchExProxyCouponRequest = buildBatchExProxyCouponRequest(ctx);
            Future excludeProxyCouponListFuture = promoWrapper.preQueryExcludeProxyCouponList(batchExProxyCouponRequest);
            ctx.getFutureCtx().setExcludeProxyCouponListFuture(excludeProxyCouponListFuture);
        }
    }

    @Override
    public void process(DealCtx ctx) {
        if(ctx.isEnableCardStyle() || ctx.isEnableCardStyleV2()) {
            Future excludeProxyCouponListFuture = ctx.getFutureCtx().getExcludeProxyCouponListFuture();
            BatchExProxyCouponResponseDTO batchExProxyCouponResponseDTO = promoWrapper.queryExcludeProxyCouponList(excludeProxyCouponListFuture);
            ctx.setBatchExProxyCouponResponseDTO(batchExProxyCouponResponseDTO);
        }
    }

    private BatchExProxyCouponRequest buildBatchExProxyCouponRequest(DealCtx dealCtx) {
        BatchExProxyCouponRequest request = new BatchExProxyCouponRequest();
        if(dealCtx.isMt()) {
            request.setPlatform(PlatformEnum.MT.getCode());
            request.setBizType(BizIdType.MT_DEAL_GROUP.getCode());
            request.setBizId((long) dealCtx.getMtId());
            request.setMtUserId(dealCtx.getEnvCtx().getMtUserId());//已确认判断平台后再使用
            request.setMtShopId(dealCtx.getMtLongShopId());
        } else {
            request.setPlatform(PlatformEnum.DP.getCode());
            request.setBizType(BizIdType.DEAL_GROUP_ID.getCode());
            request.setDpBizId((long) dealCtx.getDpId());
            request.setDpUserId(dealCtx.getEnvCtx().getDpUserId());
            request.setDpShopId(dealCtx.getDpLongShopId());
        }

        request.setClientType(getClientTypeEnum(dealCtx).getCode());
        request.setCityId(dealCtx.getCityId4P());
        request.setUserLatitude(dealCtx.getUserlat());
        request.setUserLongitude(dealCtx.getUserlng());
        request.setPageSource(dealCtx.getRequestSource());
        request.setNeedDiscountCoupon(true);
        request.setExProxyCouponContext(buildExProxyCouponContext(dealCtx));

        return request;
    }

    private ClientTypeEnum getClientTypeEnum(DealCtx dealCtx) {
        DztgClientTypeEnum dztgClientTypeEnum = dealCtx.getEnvCtx().getDztgClientTypeEnum();
        boolean isIos = dealCtx.getEnvCtx().isIos();

        switch (dztgClientTypeEnum) {
            case MEITUAN_APP:
                return isIos ? ClientTypeEnum.IPHONE : ClientTypeEnum.ANDROID;
            case DIANPING_APP:
                return isIos ? ClientTypeEnum.DP_IPHONE : ClientTypeEnum.DP_ANDROID;
            case MEITUAN_WEIXIN_MINIAPP:
                return ClientTypeEnum.WE_CHAT_APPLET;
            case DIANPING_WEIXIN_MINIAPP:
                return ClientTypeEnum.DP_WE_CHAT_APPLET;
            case MEITUAN_MAP_APP:
            case MEITUAN_FROM_H5:
            case DIANPING_FROM_H5:
            case MEITUAN_WANWU_MINIAPP:
            case MEITUAN_KUAISHOU_MINIAPP:
            case DIANPING_BAIDUMAP_MINIAPP:
            case UNKNOWN:
            default:
                return ClientTypeEnum.PC;
        }
    }

    private ExProxyCouponContext buildExProxyCouponContext(DealCtx dealCtx) {

        ExProxyCouponContext exProxyCouponContext = new ExProxyCouponContext();
        exProxyCouponContext.setDpId(dealCtx.getEnvCtx().getDpId());
        exProxyCouponContext.setVersion(dealCtx.getEnvCtx().getVersion());
        exProxyCouponContext.setRequestURI(dealCtx.getEnvCtx().getRequestURI());
        exProxyCouponContext.setUserAgent(dealCtx.getEnvCtx().getUserAgent());
        exProxyCouponContext.setUserIp(dealCtx.getEnvCtx().getUserIp());
        exProxyCouponContext.setCx(dealCtx.getCx());

        return exProxyCouponContext;
    }

}
