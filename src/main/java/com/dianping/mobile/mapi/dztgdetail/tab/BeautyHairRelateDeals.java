package com.dianping.mobile.mapi.dztgdetail.tab;

import com.dianping.cat.Cat;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.struct.query.api.entity.dto.*;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.BeautyTagWrapper;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

@Service
public class BeautyHairRelateDeals extends AbstractRelateDeals {

    private final static String TAG = "剪发";

    @Autowired
    BeautyTagWrapper beautyTagWrapper;

    @Override
    public List<Integer> identifyByPublishCategory() {
        return Collections.singletonList(501);
    }

    @Override
    protected SourceDataHolder getSourceDataHolder() {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.tab.BeautyHairRelateDeals.getSourceDataHolder()");
        return new BeautyHairSourceDataHolder();
    }

    @Override
    public void loadBeforeRelate(BaseLoadParam param, SourceDataHolder holder) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.tab.BeautyHairRelateDeals.loadBeforeRelate(com.dianping.mobile.mapi.dztgdetail.tab.bo.BaseLoadParam,com.dianping.mobile.mapi.dztgdetail.tab.bo.SourceDataHolder)");
        BeautyHairSourceDataHolder bhHolder = (BeautyHairSourceDataHolder) holder;

        super.loadBeforeRelate(param, bhHolder);

        List<Integer> dpDealGroupIds = param.getBaseData().getDpDealGroupIds();
        List<Future> dealMainTagFutures = beautyTagWrapper.preBeautyMainTag(dpDealGroupIds);
        //List<Future> detailFutures = dealGroupWrapper.preBatchQueryDealDetailInfo(dpDealGroupIds);

        //bhHolder.setDealBaseMap(dealGroupWrapper.getBatchQueryDealGroupBaseResult(bhHolder.getDealBaseFutures()));
        //bhHolder.setDealSaleMap(dealStockSaleWrapper.getShopSceneSalesDisplay(bhHolder.getDealSaleFutures()));
        bhHolder.setDealMainTagMap(beautyTagWrapper.getBeautyMainTag(dealMainTagFutures));
        //bhHolder.setDealDetailMap(dealGroupWrapper.getBatchQueryDealDetailInfoResult(detailFutures));
    }

    @Override
    public void loadAfterRelate(BaseLoadParam param, SourceDataHolder holder) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.tab.BeautyHairRelateDeals.loadAfterRelate(com.dianping.mobile.mapi.dztgdetail.tab.bo.BaseLoadParam,com.dianping.mobile.mapi.dztgdetail.tab.bo.SourceDataHolder)");
        BeautyHairSourceDataHolder bhHolder = (BeautyHairSourceDataHolder) holder;
        List<Integer> relatedDpDealGroupIds = param.getBaseData().getRelatedDpDealGroupIds();

        super.loadAfterRelate(param, bhHolder);
        List<Future> detailFutures = dealGroupWrapper.preBatchQueryDealDetailInfo(relatedDpDealGroupIds);

        bhHolder.setDealBaseMap(dealGroupWrapper.getBatchQueryDealGroupBaseResult(bhHolder.getDealBaseFutures()));
        bhHolder.setDealSaleMap(dealStockSaleWrapper.getShopSceneSalesDisplay(bhHolder.getDealSaleFutures()));
        bhHolder.setDealDetailMap(dealGroupWrapper.getBatchQueryDealDetailInfoResult(detailFutures));

    }

    @Override
    protected List<Integer> getRelatedDpDealGroupIds(BaseData baseData, SourceDataHolder sourceDataHolder) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.tab.BeautyHairRelateDeals.getRelatedDpDealGroupIds(com.dianping.mobile.mapi.dztgdetail.tab.bo.BaseData,com.dianping.mobile.mapi.dztgdetail.tab.bo.SourceDataHolder)");
        List<Integer> relatedDpDealGroups = new ArrayList<>();
        BeautyHairSourceDataHolder holder = (BeautyHairSourceDataHolder) sourceDataHolder;

        Map<Integer, String> dealMainTagMap = holder.getDealMainTagMap();
        for (Map.Entry<Integer, String> entry : dealMainTagMap.entrySet()) {
            Integer key = entry.getKey();
            String value = entry.getValue();

            if (isCutHair(value)) {
                relatedDpDealGroups.add(key);
            }
        }

        return relatedDpDealGroups;
    }

    @Override
    protected DealTabHolder doListRelatedDealTabs(BaseData baseData, SourceDataHolder sourceDataHolder) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.tab.BeautyHairRelateDeals.doListRelatedDealTabs(com.dianping.mobile.mapi.dztgdetail.tab.bo.BaseData,com.dianping.mobile.mapi.dztgdetail.tab.bo.SourceDataHolder)");

        BeautyHairSourceDataHolder holder = (BeautyHairSourceDataHolder) sourceDataHolder;

        if (!chkResp(baseData.getCurrentDpGroupId(), holder)) {
            return null;
        }

        Map<Integer, DealDetailDto> dealDetailMap = holder.getDealDetailMap();
        Map<Integer, DealGroupBaseDTO> dealGroupBaseMap = holder.getDealBaseMap();
        Map<Integer, String> tagMap = holder.getDealMainTagMap();

        List<DealTab> tabs = new ArrayList<>();
        DealTab currentTab = null;

        for (Map.Entry<Integer, String> entry : tagMap.entrySet()) {
            Integer dpDealGroupId = entry.getKey();

            DealTab tab = genDealTab(dpDealGroupId, dealDetailMap.get(dpDealGroupId), dealGroupBaseMap.get(dpDealGroupId));

            if (tab != null) {
                if (baseData.getCurrentDpGroupId() == dpDealGroupId) {
                    currentTab = tab;
                } else {
                    tabs.add(tab);
                }
            }

        }

        if (currentTab == null) {
            return null;
        }

        DealTabHolder dealTabHolder = new DealTabHolder();
        dealTabHolder.setCurrentTab(currentTab);
        dealTabHolder.setRelatedTabs(tabs);

        return dealTabHolder;
    }

    private boolean chkResp(int currentDpGroupId, BeautyHairSourceDataHolder holder) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.tab.BeautyHairRelateDeals.chkResp(int,com.dianping.mobile.mapi.dztgdetail.tab.BeautyHairRelateDeals$BeautyHairSourceDataHolder)");
        if (MapUtils.isEmpty(holder.getDealMainTagMap()) || MapUtils.isEmpty(holder.getDealDetailMap()) || MapUtils.isEmpty(holder.getDealBaseMap())) {
            return false;
        }

        //当前团单主标签不是剪发
        return isCutHair(holder.getDealMainTagMap().get(currentDpGroupId));
    }

    private DealTab genDealTab(int dpDealGroupId, DealDetailDto detail, DealGroupBaseDTO base) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.tab.BeautyHairRelateDeals.genDealTab(int,com.dianping.deal.struct.query.api.entity.dto.DealDetailDto,com.dianping.deal.base.dto.DealGroupBaseDTO)");

        if (base == null) {
            return null;
        }

        DealTab tab = new DealTab();
        tab.setDealGroupId(dpDealGroupId);
        tab.setSalePriceWithUnit(base.getDealGroupPrice());
        tab.setTag(TAG);

        if (detail == null || detail.getSkuUniStructuredDto() == null || CollectionUtils.isEmpty(detail.getSkuUniStructuredDto().getMustGroups())) {
            return tab;
        }

        DealDetailSkuUniStructuredDto sku = detail.getSkuUniStructuredDto();
        List<MustSkuItemsGroupDto> mustGroups = sku.getMustGroups();

        String lvl = "";
        for (MustSkuItemsGroupDto group : mustGroups) {
            List<SkuItemDto> items = group.getSkuItems();

            for (SkuItemDto item : items) {

                if ("洗剪吹".equals(item.getName())) {

                    for (SkuAttrItemDto attr : item.getAttrItems()) {

                        if ("productOwner".equals(attr.getAttrName())) {
                            lvl = attr.getAttrValue();
                        }

                    }

                }

            }
        }

        tab.setTag(lvl + TAG);
        return tab;

    }

    private boolean isCutHair(String mainTag) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.tab.BeautyHairRelateDeals.isCutHair(java.lang.String)");

        if (StringUtils.isBlank(mainTag)) {
            return false;
        }

        return TAG.equals(mainTag);
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    private static class BeautyHairSourceDataHolder extends SourceDataHolder {

        private Map<Integer, String> dealMainTagMap;

        private Map<Integer, DealDetailDto> dealDetailMap;

    }

}