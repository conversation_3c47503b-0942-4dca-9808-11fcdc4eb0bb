package com.dianping.mobile.mapi.dztgdetail.tab.bo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class DealTab {
    //团单id
    private int dealGroupId;

    //售价
    private String salePrice;

    //团单关键标签
    private String tag;

    //团单对应的moduleKey
    private String moduleKey;

    public void setSalePriceWithUnit(BigDecimal salePrice) {
        if (salePrice == null) {
            return;
        }

        this.salePrice = salePrice.stripTrailingZeros().toPlainString() + "元";
    }
}
