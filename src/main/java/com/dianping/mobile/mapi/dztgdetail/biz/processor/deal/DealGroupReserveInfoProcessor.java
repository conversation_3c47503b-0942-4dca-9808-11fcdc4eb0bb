package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.detail.enums.PlatformEnum;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.utils.ExaminerUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.StringFormatUtil;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.CategoryCombineTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.SelfOperatedCleanEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.AdditionalInfo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DztgShareModule;
import com.dianping.mobile.mapi.dztgdetail.entity.ShowReservationGrayConfig;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.handler.SelfOperatedCleaningHandler;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.ReserveMaintenanceService;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.utils.FaultToleranceUtils;
import com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.ReassuredRepairUtil;
import com.dianping.mobile.mapi.dztgdetail.util.VersionUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.SettableFuture;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import com.sankuai.clr.content.process.gateway.thrift.common.user.trade.ResvLeadsEntranceAfterTradeGatewayService;
import com.sankuai.clr.content.process.gateway.thrift.common.user.trade.request.*;
import com.sankuai.clr.content.process.gateway.thrift.common.user.trade.response.DealGroupBuyingResvEntranceRespDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.combine.CombineDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.CombineQueryScope;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.request.QueryScope;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Future;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.*;

@Slf4j
public class DealGroupReserveInfoProcessor extends AbsDealProcessor {
    private static final String POI_LEVEL2_CATEGORYIDS = "com.sankuai.dzu.tpbase.dztgdetailweb.wash.backlevel2CategoryId";

    private static final String MT_WASH_RESERVE_EXP = "MtWashReserveExp";

    private static final String DP_WASH_RESERVE_EXP = "DpWashReserveExp";

    @Autowired
    private ResvLeadsEntranceAfterTradeGatewayService resvLeadsEntranceAfterTradeGatewayServiceFuture;

    @Autowired
    private QueryCenterWrapper queryCenterWrapper;

    @Autowired
    private DouHuBiz douHuBiz;

    @Autowired
    private ReserveMaintenanceService reserveMaintenanceService;

    @Resource
    private SelfOperatedCleaningHandler selfOperatedCleaningHandler;

    @Override
    public boolean isEnable(DealCtx ctx) {
        if (RequestSourceEnum.fromTradeSnapshot(ctx.getRequestSource())) {
            return false;
        }

        boolean hasLogin = false;
        boolean isReserveEnableCategory = false;
        boolean isNewerAppVersion = false;
        if (ctx.isExternal()) {
            return false;
        }
        if (ctx.getEnvCtx().getMtUserId() != 0L || ctx.getEnvCtx().getDpUserId() != 0L) {//已确认判断平台后再使用
            hasLogin = true;
        }

        if (hasLogin && isForeSupportAdditional(ctx)) {
            // 控制版本
            if (isValidateAdditionalVersion(ctx)) {
                return true;
            }
        }

        reserveMaintenanceService.isEnableIntegratedReserved(ctx);

        List<Integer> reserveEnabledCategory = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.reserve.entrance.category", Integer.class, new ArrayList<>());
        if (reserveEnabledCategory.contains(ctx.getCategoryId())) {
            isReserveEnableCategory = true;
        }

        Map<String, String> platform2VersionMap = Lion.getMap("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.reserve.entrance.version", String.class, new HashMap<>());
        if (ctx.isMt()) {
            String enableVersion = platform2VersionMap.get("mt");
            isNewerAppVersion = VersionUtils.isGreatEqualThan(ctx.getEnvCtx().getVersion(), enableVersion);
        } else {
            String enableVersion = platform2VersionMap.get("dp");
            isNewerAppVersion = VersionUtils.isGreatEqualThan(ctx.getEnvCtx().getVersion(), enableVersion);
        }
        if (LionConfigUtils.isInReserveEntranceAbTestCategoryIds(ctx.getCategoryId())) {
            isReserveEnableCategory = enableReserveEntranceAbTest(ctx);
        }
        return hasLogin && isReserveEnableCategory && isNewerAppVersion;
    }

    public boolean enableReserveEntranceAbTest(DealCtx ctx) {
        String moduleName = ctx.isMt() ? "MtReserveEntrance" : "DpReserveEntrance";
        ModuleAbConfig moduleAbConfig = douHuBiz.getAbByUnionId(ctx.getEnvCtx().getUnionId(), moduleName, ctx.isMt());
        // 如果为空，则初始化moduleAbConfigs
        if (Objects.isNull(ctx.getModuleAbConfigs())) {
            ctx.setModuleAbConfigs(Lists.newArrayList());
        }
        if (Objects.isNull(moduleAbConfig) || CollectionUtils.isEmpty(moduleAbConfig.getConfigs())) {
            return false;
        }
        // 去除重复实验结果
        if (!isDuplicateKey(ctx.getModuleAbConfigs(), moduleName)) {
            // 添加moduleAbConfig到moduleAbConfigs中
            ctx.getModuleAbConfigs().add(moduleAbConfig);
        }
        String expResult = moduleAbConfig.getConfigs().get(0).getExpResult();
        // 实验结果a、b -> 不走买约一体
        // 实验结果c -> 走买约一体
        return Objects.equals(expResult, "c");
    }

    /**
     * 是否存在重复的key
     */
    private boolean isDuplicateKey(List<ModuleAbConfig> moduleAbConfigs, String moduleKey) {
        return moduleAbConfigs.stream()
                .anyMatch(config -> Objects.equals(config.getKey(), moduleKey));
    }

    private boolean isValidateAdditionalVersion(DealCtx ctx) {
        boolean isNewerAppVersion = false;
        Map<String, String> platform2VersionMap = Lion.getMap("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.additional.version", String.class, new HashMap<>());
        if (ctx.isMt()) {
            String enableVersion = platform2VersionMap.get("mt");
            isNewerAppVersion = VersionUtils.isGreatEqualThan(ctx.getEnvCtx().getVersion(), enableVersion);
        } else {
            String enableVersion = platform2VersionMap.get("dp");
            isNewerAppVersion = VersionUtils.isGreatEqualThan(ctx.getEnvCtx().getVersion(), enableVersion);
        }
        return isNewerAppVersion;
    }

    @Override
    public void prepare(DealCtx ctx) {
        // 保洁自营门店不走团详页的预约浮层
        if (Objects.equals(ctx.getCategoryId(), 409) && selfOperatedCleaningHandler.isSelfOperatedCleaningDeal(ctx)) {
            ctx.setCleanSelfOperationShop(true);
            return;
        }
        /***
         * 如果用加项逻辑，就不在立即购买展示预约弹框了
         */
        if (isForeSupportAdditional(ctx)) {
            int dealGroupId = ctx.isMt() ? ctx.getMtId() : ctx.getDpId();
            QueryByDealGroupIdRequest request = ExaminerUtils.getExaminerRequest(dealGroupId,
                    ctx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP);
            QueryScope queryScope = new QueryScope();
            CombineQueryScope combineQueryScope = new CombineQueryScope();
            combineQueryScope.setCombineTypeEnum(CategoryCombineTypeEnum.getEnumByCategoryId(ctx.getCategoryId()));
            combineQueryScope.setIdType(ctx.isMt() ? IdTypeEnum.MT.getCode() : IdTypeEnum.DP.getCode());
            combineQueryScope.setShopId(ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId());
            queryScope.setCombineQueryScope(combineQueryScope);
            request.setQueryScope(queryScope);
            Future singleDealGroupDtoFuture = queryCenterWrapper.preDealGroupDTO(request);
            ctx.getFutureCtx().setAdditionalDealGroupDtoFuture(singleDealGroupDtoFuture);
            return;
        }
        DealGroupBuyingResvEntranceReqDTO reqDTO = new DealGroupBuyingResvEntranceReqDTO();
        int platform = ctx.isMt() ? 2 : 1;
        reqDTO.setPlatform(platform);
        reqDTO.setDpShopId(ctx.getDpLongShopId());
        reqDTO.setMtShopId(ctx.getMtLongShopId());
        reqDTO.setUserDpCityId(ctx.getDpCityId());
        Long dpDealGroupId = (long)ctx.getDpId();
        reqDTO.setProductId(dpDealGroupId);
        reqDTO.setDealGroupInfoDTO(buildDealGroupInfoDTO(ctx));

        try {
            resvLeadsEntranceAfterTradeGatewayServiceFuture.queryDealGroupBuyingResvEntranceInfo(reqDTO);
            SettableFuture dealGroupBuyingResvEntranceFuture = ContextStore.getSettableFuture();
            ctx.getFutureCtx().setDealGroupBuyingResvEntranceFuture(dealGroupBuyingResvEntranceFuture);
        } catch (TException e) {
            logger.error("resvLeadsEntranceAfterTradeGatewayService.queryDealGroupReserveEntranceInfo error, ", e);
        }
    }

    @Override
    public void process(DealCtx ctx) {
        if (isForeSupportAdditional(ctx)) {
            if (ctx.getFutureCtx().getAdditionalDealGroupDtoFuture() != null) {
                DealGroupDTO dealGroupDTO = null;
                try {
                    dealGroupDTO = queryCenterWrapper
                            .getDealGroupDTO(ctx.getFutureCtx().getAdditionalDealGroupDtoFuture());
                    ctx.setAdditionalInfo(buildAdditional(ctx, dealGroupDTO));
                } catch (Exception e) {
                    logger.error("queryCenterWrapper.getDealGroupDTO error,", e);
                    ctx.setQueryCenterHasError(true);
                }
            }
            return;
        }
        DealGroupBuyingResvEntranceRespDTO respDTO = null;
        if (ctx.getFutureCtx().getDealGroupBuyingResvEntranceFuture() != null) {
            try {
                respDTO = (DealGroupBuyingResvEntranceRespDTO)ctx.getFutureCtx().getDealGroupBuyingResvEntranceFuture()
                        .get();
            } catch (Exception e) {
                logger.error("resvLeadsEntranceAfterTradeGatewayService.queryDealGroupReserveEntranceInfo error, ", e);
                FaultToleranceUtils.addException("queryDealGroupBuyingResvEntranceInfo", e);
            }
        }
        if (respDTO != null && respDTO.getHasReserveEntrance() != null) {
            Boolean shopSupportReserve = respDTO.getHasReserveEntrance();
            if (shopSupportReserve) {
                ModuleAbConfig moduleAbConfig = null;
                if (enableAbTest()) {
                    getDouHuModuleAbConfig(ctx,"MTPhysicalExerciseReserve","DPPhysicalExerciseReserve");
                }
            }
            boolean enableIntegratedReserved = reserveMaintenanceService.getIntegratedReservedResult(ctx);
            // 快照不需要走AB实验
            if (LionConfigUtils.isSnapShotPhoto(ctx.getCategoryId())) {
                ctx.setShowReserveEntrance(shopSupportReserve);
            } else {
                String physicalExerciseReserveExpResult = getExpResult(ctx,"MTPhysicalExerciseReserve","DPPhysicalExerciseReserve");
                boolean showReserveEntrance = isShowReserveEntrance(shopSupportReserve, physicalExerciseReserveExpResult);
                ctx.setShowReserveEntrance(selfOperatedCleanOrderReserve(ctx,showReserveEntrance && !enableIntegratedReserved));
            }

            // 直播路径不展示预约浮窗
            if (RequestSourceEnum.LIVE_STREAM.getSource().equals(ctx.getRequestSource())
                    && LionConfigUtils.hideReserveEntrance(ctx.getCategoryId())) {
                ctx.setShowReserveEntrance(false);
            }
        }
        // 设置新预约弹窗
        setNewReserveInfo(ctx, respDTO);
        if(ReassuredRepairUtil.isTagPresent(ctx)){
           ctx.setShowNewReserveEntrance(true);
           ctx.setReserveRedirectUrl(buildReserveRedirectUrl(ctx));
        }
    }

    private void getDouHuModuleAbConfig(DealCtx ctx, String mtKey, String dpKey) {
        ModuleAbConfig moduleAbConfig;
        moduleAbConfig = douHuBiz.getAbExpResult(ctx,
                ctx.isMt() ? mtKey : dpKey);
        if (moduleAbConfig != null) {
            if ( ctx.getModuleAbConfigs() != null) {
                ctx.getModuleAbConfigs().add(moduleAbConfig);
            } else {
                ctx.setModuleAbConfigs(new ArrayList<>());
                ctx.getModuleAbConfigs().add(moduleAbConfig);
            }
        }
    }

    public DealGroupInfoDTO buildDealGroupInfoDTO(DealCtx ctx) {
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        try {
            if (dealGroupDTO == null) {
                return null;
            }
            DealGroupInfoDTO dto = new DealGroupInfoDTO();
            dto.setDpDealGroupId(dealGroupDTO.getDpDealGroupId());
            dto.setMtDealGroupId(dealGroupDTO.getMtDealGroupId());
            if (dealGroupDTO.getCategory() != null) {
                DealGroupCategoryInfoDTO categoryInfoDTO = new DealGroupCategoryInfoDTO();
                categoryInfoDTO.setCategoryId(dealGroupDTO.getCategory().getCategoryId());
                categoryInfoDTO.setServiceType(dealGroupDTO.getCategory().getServiceType());
                categoryInfoDTO.setServiceTypeId(dealGroupDTO.getCategory().getServiceTypeId());
                dto.setDealGroupCategoryInfoDTO(categoryInfoDTO);
            }
            if (dealGroupDTO.getChannel() != null) {
                DealGroupChannelInfoDTO channelInfoDTO = new DealGroupChannelInfoDTO();
                channelInfoDTO.setChannelId(dealGroupDTO.getChannel().getChannelId());
                channelInfoDTO.setChannelEn(dealGroupDTO.getChannel().getChannelEn());
                channelInfoDTO.setChannelCn(dealGroupDTO.getChannel().getChannelCn());
                channelInfoDTO.setChannelGroupId(dealGroupDTO.getChannel().getChannelGroupId());
                channelInfoDTO.setChannelGroupEn(dealGroupDTO.getChannel().getChannelGroupEn());
                channelInfoDTO.setChannelGroupCn(dealGroupDTO.getChannel().getChannelGroupCn());
                dto.setDealGroupChannelInfoDTO(channelInfoDTO);
            }
            // todo xiangrui 当做入参了，是预约的类目，可以截断
            if (dealGroupDTO.getDisplayShopInfo() != null) {
                dto.setDpDisplayShopIds(dealGroupDTO.getDisplayShopInfo().getDpDisplayShopIds());
                dto.setMtDisplayShopIds(dealGroupDTO.getDisplayShopInfo().getMtDisplayShopIds());
            }
            if (CollectionUtils.isNotEmpty(dealGroupDTO.getRegions())) {
                List<DealGroupRegionInfoDTO> regionInfoDTOList = dealGroupDTO.getRegions().stream()
                        .map(dealGroupRegionDTO -> {
                            DealGroupRegionInfoDTO regionInfoDTO = new DealGroupRegionInfoDTO();
                            regionInfoDTO.setDpCityId(dealGroupRegionDTO.getDpCityId());
                            regionInfoDTO.setMtCityId(dealGroupRegionDTO.getMtCityId());
                            return regionInfoDTO;
                        }).collect(Collectors.toList());
                dto.setDealGroupRegionInfoDTOList(regionInfoDTOList);
            }
            if (dealGroupDTO.getCustomer() != null) {
                DealGroupCustomerInfoDTO customerInfoDTO = new DealGroupCustomerInfoDTO();
                customerInfoDTO.setOriginCustomerId(dealGroupDTO.getCustomer().getOriginCustomerId());
                customerInfoDTO.setPlatformCustomerId(dealGroupDTO.getCustomer().getPlatformCustomerId());
                dto.setDealGroupCustomerInfoDTO(customerInfoDTO);
            }
            if (CollectionUtils.isNotEmpty(dealGroupDTO.getAttrs())) {
                dto.setDealGroupAttrInfoDTOList(
                        dealGroupDTO.getAttrs().stream().map(attr -> {
                            DealGroupAttrInfoDTO attrInfoDTO = new DealGroupAttrInfoDTO();
                            attrInfoDTO.setName(attr.getName());
                            attrInfoDTO.setValue(attr.getValue());
                            attrInfoDTO.setSource(attr.getSource());
                            attrInfoDTO.setCnName(attr.getCnName());
                            attrInfoDTO.setType(attr.getType());
                            return attrInfoDTO;
                        }).collect(Collectors.toList())
                );
            }
            return dto;
        } catch (Exception e) {
            log.error("DealGroupReserveInfoProcessor.buildDealGroupInfoDTO,dealGroupDTO:{}", JSON.toJSONString(dealGroupDTO), e);
            return null;
        }
    }

    private String buildReserveRedirectUrl(DealCtx ctx) {
        DealGroupBaseDTO dealGroupBase = ctx.getDealGroupBase();
        if(dealGroupBase == null){
            Cat.logEvent("buildReserveRedirectUrl", "dealGroupBase is null");
        }
        DztgShareModule dztgShareModule = ctx.getDztgShareModule();
        try {
            if (ctx.isMt()) {
                return Lion.getString(APP_KEY, REASSURED_REPAIR_DEAL_RESERVE_REDIRECT_MTURL).replace("${dealGroupId}", String.valueOf(ctx.getMtId()))
                        .replace("${shopId}", (ctx.getMtLongShopId() == 0 ? StringUtils.EMPTY : String.valueOf(ctx.getMtLongShopId())))
                        .replace("${dealTitle}", dealGroupBase != null ? dealGroupBase.getProductTitle() : dztgShareModule.getMt().getBrandName());
            }
            return Lion.getString(APP_KEY,REASSURED_REPAIR_DEAL_RESERVE_REDIRECT_DPURL).replace("${dealGroupId}", String.valueOf(ctx.getDpId()))
                    .replace("${shopId}", (ctx.getDpLongShopId() == 0 ? StringUtils.EMPTY : String.valueOf(ctx.getDpLongShopId())))
                    .replace("${dealTitle}",dealGroupBase != null ? dealGroupBase.getProductTitle() : dztgShareModule.getDp().getProductTitle());
        } catch (Exception e) {
            logger.error("buildReserveRedirectUrl Exception", e);
        }
        return StringUtils.EMPTY;
    }

    private AdditionalInfo buildAdditional(DealCtx ctx, DealGroupDTO dealGroupDTO) {
        AdditionalInfo additionalInfo = new AdditionalInfo();
        List<CombineDTO> combineDTOS = Optional.ofNullable(dealGroupDTO.getCombines()).orElse(Lists.newArrayList());
        boolean additional = combineDTOS.stream().map(CombineDTO::getCombineItems).filter(Objects::nonNull).count() > 0;
        additionalInfo.setAdditional(additional);
        additionalInfo.setAdditionalJumpUrl(UrlHelper.addPriceCipher(ctx, additionalJumpUrl(ctx, dealGroupDTO)));
        return additionalInfo;
    }

    private String additionalJumpUrl(DealCtx ctx, DealGroupDTO dealGroupDTO) {
        List<AdditionalCategoryJumpUrl> jumpUrls = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb","com.sankuai.dzu.tpbase.dztgdetailweb.category.additionalJumpUrl", AdditionalCategoryJumpUrl.class, Lists.newArrayList());;
        int platform = ctx.isMt() ? PlatformEnum.MEI_TUAN.getType() : PlatformEnum.DIAN_PING.getType();
        AdditionalCategoryJumpUrl additionalCategoryJumpUrl = jumpUrls.stream().filter(t -> t.getCategoryId() == ctx.getCategoryId()).findFirst().orElse(null);
        if (additionalCategoryJumpUrl == null || MapUtils.isEmpty(additionalCategoryJumpUrl.getJumpUrlMap())){
            return "";
        }
        String jumpUrlTemplate = additionalCategoryJumpUrl.getJumpUrlMap().get(platform);
        Map map = Maps.newHashMap();
        map.put("platform", platform);
        map.put("shopId", ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId());
        map.put("productId", ctx.isMt() ? dealGroupDTO.getMtDealGroupId() : dealGroupDTO.getDpDealGroupId());
        map.put("cityId", ctx.isMt() ? ctx.getMtCityId() : ctx.getDpCityId());
        map.put("userLat", String.format("%.7f", ctx.getUserlat()));
        map.put("userLng", String.format("%.7f", ctx.getUserlng()));
        map.put("gpsCoordinateType", ctx.getGpsCoordinateType());
        map.put("shelfSceneCode", additionalCategoryJumpUrl.getShelfSceneCode());
        map.put("categoryId", ctx.getCategoryId());

        return StringFormatUtil.format(jumpUrlTemplate, map);
    }

    private String getJumpUrlTemplate(List<AdditionalJumpUrl> jumpUrls, Predicate<AdditionalJumpUrl> predicate) {
        return jumpUrls.stream().filter(t -> predicate.test(t)).findFirst().map(tt -> tt.getJumpUrl()).orElse("");
    }

    @Data
    public static class AdditionalJumpUrl {
        int platform;
        String jumpUrl;
    }

    @Data
    public static class AdditionalCategoryJumpUrl {
        int categoryId;

        String shelfSceneCode;
        /**
         * platform -> JumpUrl
         */
        Map<Integer, String> jumpUrlMap;
    }

    private boolean enableAbTest() {
        return Lion.getBoolean("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.physical.exercise.reserve.abtest.enable", false);
    }
    private String getExpResult(DealCtx ctx,String mtKey, String dpKey) {
        List<ModuleAbConfig> moduleAbConfigs = ctx.getModuleAbConfigs();
        if (moduleAbConfigs != null) {
            for (ModuleAbConfig abConfig : moduleAbConfigs) {
                if (abConfig == null) {
                    continue;
                }
                String key = ctx.isMt() ? mtKey : dpKey;
                if (key.equals(abConfig.getKey())) {
                    if (CollectionUtils.isNotEmpty(abConfig.getConfigs()) && abConfig.getConfigs().get(0) != null) {
                        return abConfig.getConfigs().get(0).getExpResult();
                    }
                }
            }
        }
        return null;
    }

    private boolean isShowReserveEntrance(Boolean shopSupportReserve, String expResult) {
        if (expResult != null && shopSupportReserve != null) {
            return shopSupportReserve && ("b".equals(expResult));
        } else if (shopSupportReserve != null) {
            return shopSupportReserve;
        } else {
            return false;
        }
    }

    private void setNewReserveInfo(DealCtx ctx, DealGroupBuyingResvEntranceRespDTO respDTO) {
        if (!enableNewReserve()) {
            // 校验新预约浮层开关是否开启
            return;
        }
        if (!newReserveMrnVersionCheck(ctx)) {
            // 校验mrn版本
            return;
        }
        if (ctx.isMultiSku()) {
            // 多sku：先进提单再出预约，需在团详将预约隐藏
            return;
        }
        if (isSnapshotExpA(ctx)) {
            // 快照团详实验a：不出预约
            return;
        }

        ShowReservationGrayConfig grayConfig = Lion.getBean(LionConstants.APP_KEY, LionConstants.SHOW_RESERVATION_GRAY_CONFIG, ShowReservationGrayConfig.class);
        int publishCategoryId = ctx.getCategoryId();
        // 只有当grayConfig配置中配置了对应categoryId且没有命中商户白名单和实验的才直接返回，否则（命中白名单或命中实验或其他类目）继续执行后续逻辑
        if (Objects.nonNull(grayConfig) && grayConfig.getCategoryIds().contains(publishCategoryId) && !hitShowReservation(grayConfig, ctx)) {
            return;
        }
        boolean showNewReserveEntrance = isShowNewReserveEntrance(respDTO);
        boolean enableIntegratedReserved = reserveMaintenanceService.getIntegratedReservedResult(ctx);
        ctx.setShowNewReserveEntrance(selfOperatedCleanOrderReserve(ctx,showNewReserveEntrance && !enableIntegratedReserved));
        if (showNewReserveEntrance) {
            ctx.setReserveRedirectUrl(respDTO.getBuyingEntranceDTO().getBuyingEntranceUrl());
        }
    }

    /**
     * 通过团详页的跳链中参数来直接控制提单按钮展示预约浮层还是强预订提单
     * @param ctx
     * @param originResult
     * @return
     */
    private boolean selfOperatedCleanOrderReserve(DealCtx ctx,boolean originResult) {
        SelfOperatedCleanEnum selfOperatedCleanEnum = SelfOperatedCleanEnum.parseDealParam(ctx.getDealParam());
        if (Objects.equals(SelfOperatedCleanEnum.COMPEL,selfOperatedCleanEnum) || Objects.equals(SelfOperatedCleanEnum.OPTION_ORDER,selfOperatedCleanEnum)) {
            return false;
        }

        if (Objects.equals(SelfOperatedCleanEnum.SEPARATE_ORDER,selfOperatedCleanEnum)) {
            return true;
        }

        return originResult;
    }

    public boolean hitShowReservation(ShowReservationGrayConfig grayConfig, DealCtx dealCtx) {
        int publishCategoryId = dealCtx.getCategoryId();
        boolean isMt = dealCtx.isMt();
        String key = isMt ? "mt" : "dp";
        key += publishCategoryId;

        // 商户白名单模式
        if (grayConfig.getWhiteListMode() == 0 && MapUtils.isNotEmpty(grayConfig.getCategory2ShopIds()) && grayConfig.getCategory2ShopIds().containsKey(String.valueOf(publishCategoryId))) {
            if (isMt && !grayConfig.getCategory2ShopIds().get(String.valueOf(publishCategoryId)).contains(dealCtx.getMtLongShopId())) {
                return false;
            }
            if (!isMt && !grayConfig.getCategory2ShopIds().get(String.valueOf(publishCategoryId)).contains(dealCtx.getDpLongShopId())) {
                return false;
            }
        }

        // 实验逻辑
        if (MapUtils.isNotEmpty(grayConfig.getCategory2ExpId()) && grayConfig.getCategory2ExpId().containsKey(key)) {
            String expId = grayConfig.getCategory2ExpId().get(key);
            String unionId = dealCtx.getEnvCtx().getUnionId();
            String module = isMt ? "MTShowReservationExpModule" : "DPShowReservationExpModule";
            ModuleAbConfig moduleAbConfig = douHuBiz.getAbByUnionIdAndExpId(unionId, expId, module, isMt);
            if(moduleAbConfig != null && CollectionUtils.isNotEmpty(moduleAbConfig.getConfigs())) {
                dealCtx.setShowReservationAbConfig(moduleAbConfig);
                if("b".equals(moduleAbConfig.getConfigs().get(0).getExpResult())) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean isSnapshotExpA(DealCtx ctx) {
        if (ctx.getSkuCtx() == null || ctx.getSkuCtx().getDealCreatOrderAbConfig() == null) {
            return false;
        }
        if (CollectionUtils.isEmpty(ctx.getSkuCtx().getDealCreatOrderAbConfig().getConfigs())) {
            return false;
        }
        return "a".equals(ctx.getSkuCtx().getDealCreatOrderAbConfig().getConfigs().get(0).getExpResult());
    }

    private boolean enableNewReserve() {
        return GreyUtils.isEnableNewReserve();
    }

    private boolean newReserveMrnVersionCheck(DealCtx ctx) {
        return VersionUtils.isGreatEqualThan(ctx.getMrnVersion(), GreyUtils.getNewReserveMrnVersion());
    }

    private boolean isShowNewReserveEntrance(DealGroupBuyingResvEntranceRespDTO respDTO) {
        return respDTO != null && respDTO.getBuyingEntranceDTO() != null
                && respDTO.getBuyingEntranceDTO().getHasBuyingEntrance() != null
                && respDTO.getBuyingEntranceDTO().getHasBuyingEntrance();
    }

    public static List<Integer> supportAdditionalCategory() {
        return Lion.getList(Environment.getAppName(), "com.sankuai.dzu.tpbase.dztgdetailweb.supportAdditionalCategory",
                Integer.class, Lists.newArrayList());
    }

    public boolean isForeSupportAdditional(DealCtx ctx) {
        return supportAdditionalCategory().contains(ctx.getCategoryId());
    }
}