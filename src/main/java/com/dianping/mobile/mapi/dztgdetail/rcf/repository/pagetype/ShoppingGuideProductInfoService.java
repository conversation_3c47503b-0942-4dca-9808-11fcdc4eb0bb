package com.dianping.mobile.mapi.dztgdetail.rcf.repository.pagetype;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.rcf.api.req.DealNativeSnapshotReq;
import com.sankuai.dz.product.detail.gateway.api.page.type.ProductDetailPageTypeService;
import com.sankuai.dz.product.detail.gateway.api.page.type.request.PageTypeRequest;
import com.sankuai.dz.product.detail.gateway.api.page.type.response.PageTypeResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2025/4/10 21:05
 */
@Slf4j
@Component
public class ShoppingGuideProductInfoService {

    @Autowired
    @Qualifier("productDetailPageTypeServiceSync")
    private ProductDetailPageTypeService productDetailPageTypeService;

    public PageTypeRequest buildRequest(DealNativeSnapshotReq request, EnvCtx envCtx){
        PageTypeRequest pageTypeRequest = new PageTypeRequest();
        pageTypeRequest.setProductId(request.getDealGroupId());
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("pragma-token",  envCtx.getPragmaToken());
        if (envCtx.isMt()){
            headerMap.put("pragma-uuid",  envCtx.getUuid());
        }else {
            headerMap.put("pragma-dpid",  envCtx.getDpId());
        }
        headerMap.put("pragma-unionid",  envCtx.getUnionId());
        headerMap.put("user-agent",  envCtx.getUserAgent());
        pageTypeRequest.setHeaderMap(headerMap);
        return pageTypeRequest;
    }

    public boolean isNewDeal(DealNativeSnapshotReq request, EnvCtx envCtx){
        try{
            if (StringUtils.isNotBlank(request.getPageType())){
                return "deal".equals(request.getPageType());
            }
            return  "deal".equals(getPageType(productDetailPageTypeService.queryPageType(buildRequest(request, envCtx))));
        }catch (Exception e){
            log.error("ShoppingGuideProductInfoService.queryPageTypeInfo error : ", e);
            return false;
        }
    }

    public String getPageType(PageTypeResponse response){
        if (Objects.isNull(response)){
            return null;
        }
        return response.getPageType();
    }
}
