package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.generic.entrance.poiphone.dto.PhoneDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiPhoneWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @description 详情页使用的电话，统一切换到查询平台提供的接口
 * @since 2020/10/9 下午5:43
 **/
public class DealShopPhoneProcessor extends AbsDealProcessor {

    @Resource
    private PoiPhoneWrapper poiPhoneWrapper;

    private static final Logger LOGGER = LoggerFactory.getLogger(DealShopPhoneProcessor.class);

    private static Gson gson = new Gson();

    private static final Pattern PATTERN_PHONE_NUMBER = Pattern.compile("\\d{2,}[-—]\\d{5,}|\\d{5,}");

    @Override
    public boolean isEnable(DealCtx ctx) {
        return true;
//        return ctx.getEnvCtx().isMainApp();
    }

    @Override
    public void prepare(DealCtx ctx) {
        Future poiPhoneFuture = poiPhoneWrapper.preparePoiPhone(ctx);
        ctx.getFutureCtx().setPoiPhoneFuture(poiPhoneFuture);
    }

    @Override
    public void process(DealCtx ctx) {

        List<String> phNos = getPhoneNoFor401(ctx);

        if (CollectionUtils.isNotEmpty(phNos)) {
            ctx.setPoiPhones(phNos);
            return;
        }

        List<PhoneDTO> phoneDTOS = poiPhoneWrapper.getFutureResult(ctx);
        String env = ctx.getEnvCtx().isMt() ? "MT" : "DP";

        if (phoneDTOS != null && !phoneDTOS.isEmpty()) {
            List<String> poiPhones = Lists.newArrayList();

            for (PhoneDTO phoneDTO : phoneDTOS) {
                poiPhones.add(phoneDTO.getDisplayPhone());
            }

            ctx.setPoiPhones(poiPhones);
        } else {
            LOGGER.error("DealShopPhoneProcessor.findAllPhoneDTO error dpshopid={} env={} phoneDtos={}", ctx.getDpLongShopId(), env, gson.toJson(phoneDTOS));
        }
    }

    /**
     * 体检类目（401）优先使用bil-tel属性对应的电话号码
     * @param ctx
     * @return
     */
    private List<String> getPhoneNoFor401(DealCtx ctx) {
        List<String> phNos = new ArrayList<>();

        if (ctx.getChannelDTO() == null || ctx.getChannelDTO().getCategoryId() != 401) {
            return phNos;
        }

        List<String> attrs = AttributeUtils.getAttributeValues(DealAttrKeys.RESERVATION_NUMBER, ctx.getAttrs());

        for (String attr : attrs) {
            String[] split = StringUtils.split(attr, ",");

            if (split == null) {
                continue;
            }

            for (String ph : split) {
                if (isValidPhNo(ph)) {
                    phNos.add(ph);
                }
            }

        }

        if (phNos.size() > 2) {
            return phNos.subList(0, 2);
        } else {
            return phNos;
        }
    }

    private boolean isValidPhNo(String ph) {
        if (StringUtils.isBlank(ph)) {
            return false;
        }

        Matcher m = PATTERN_PHONE_NUMBER.matcher(ph);
        return m.matches();
    }

    @Override
    public List<Integer> getConfigUrlDztgClient(DealCtx ctx) {
        return ctx.getEnvCtx().MAIN_APP_CLIENT_LIST;
    }

}
