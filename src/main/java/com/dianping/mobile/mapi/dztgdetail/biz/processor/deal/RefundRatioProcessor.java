package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.deal.detail.dto.DealGroupAntiFleeOrderDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.Future;

public class RefundRatioProcessor extends AbsDealProcessor {

    @Autowired
    private DealGroupWrapper dealGroupWrapper;

    @Override
    public void prepare(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.RefundRatioProcessor.prepare(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        Future antiFuture = dealGroupWrapper.preAntiFleeOrder(ctx.getDpId(),ctx.isMt());
        ctx.getFutureCtx().setAntiFleeOrderFuture(antiFuture);
    }

    @Override
    public void process(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.RefundRatioProcessor.process(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        DealGroupAntiFleeOrderDTO fleeOrderDTO = dealGroupWrapper.
                queryDealGroupAntiFleeOrderInfo(ctx.getFutureCtx().getAntiFleeOrderFuture());
        ctx.setFleeOrderDTO(fleeOrderDTO);
    }
}
