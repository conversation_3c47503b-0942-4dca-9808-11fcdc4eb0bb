package com.dianping.mobile.mapi.dztgdetail.faulttolerance.deal;

import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.deal.cleaner.DzDealBaseReportCleaner;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.deal.executor.DzDealBaseExecutor;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.req.DealBaseContextRequest;
import com.sankuai.athena.stability.faulttolerance.FaultToleranceConfiguration;
import com.sankuai.athena.stability.faulttolerance.core.Executor;
import com.sankuai.athena.stability.faulttolerance.mirror.MirrorConfiguration;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023-10-25
 * @desc 团详主接口接口容错降级配置类
 */
@Service
public class DzDealBaseFTConfiguration extends
        FaultToleranceConfiguration<DealBaseContextRequest, CommonMobileResponse> {
    @Resource
    private DzDealBaseExecutor dzDealBaseExecutor;

    @Resource
    private DzDealBaseReportCleaner dzDealBaseReportCleaner;

    @Override
    public String getFtName() {
        return "dzDealBase";
    }


    @Override
    public Executor<DealBaseContextRequest, CommonMobileResponse> getMainExecutor() {
        return dzDealBaseExecutor;
    }

    @Override
    public MirrorConfiguration getMirrorConfiguration() {
        return dzDealBaseReportCleaner;
    }
}
