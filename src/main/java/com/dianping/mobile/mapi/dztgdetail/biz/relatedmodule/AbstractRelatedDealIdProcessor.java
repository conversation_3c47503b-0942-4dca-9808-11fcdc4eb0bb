package com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule;

import com.dianping.cat.Cat;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.deal.idmapper.api.DealIdMapperService;
import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.dianping.deal.sales.common.datatype.ProductParam;
import com.dianping.deal.sales.common.datatype.SalesDisplayRequest;
import com.dianping.deal.sales.common.enums.SalesPlatform;
import com.dianping.deal.shop.constants.Platform;
import com.dianping.deal.shop.dto.DealGroupShop;
import com.dianping.deal.shop.dto.DealGroupShopSearchRequest;
import com.dianping.gmkt.activ.api.polymeric.request.MaterialClientType;
import com.dianping.gmkt.activity.api.enums.AppPlatform;
import com.dianping.gmkt.activity.api.enums.QuerySecKillSceneStrategyEnum;
import com.dianping.gmkt.activity.api.request.QuerySecKillSceneByMaterialRequest;
import com.dianping.gmkt.event.datapools.api.enums.seckill.SeckillActivityMaterialTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealActivityWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealStockSaleWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MoreDealsWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.SaleConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.RelatedModuleCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedDealModuleVO;
import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupBasicInfoBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupIdBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupImageBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2023/7/5
 */
@Component
@Slf4j
public abstract class AbstractRelatedDealIdProcessor implements RelatedDealIdProcessor {
    @Autowired
    private QueryCenterWrapper queryCenterWrapper;
    @Autowired
    private DealStockSaleWrapper dealStockSaleWrapper;
    @Autowired
    private DealIdMapperService dealIdMapperService;
    @Resource
    private DealActivityWrapper dealActivityWrapper;
    @Resource
    private MoreDealsWrapper moreDealsWrapper;

    public abstract List<Long> getRelatedDealGroupIds(RelatedModuleCtx ctx);

    public abstract RelatedDealModuleVO assemble(RelatedModuleCtx ctx, List<Long> ids);

    public Future getDealGroupFuture(EnvCtx envCtx, List<Integer> dealGroupIds) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule.AbstractRelatedDealIdProcessor.getDealGroupFuture(com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx,java.util.List)");
        Set<Long> dealGroupIdSet = dealGroupIds.stream().map(Integer::longValue).collect(Collectors.toSet());
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(dealGroupIdSet, envCtx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP)
                .basicInfo(DealGroupBasicInfoBuilder.builder().all())
                .image(DealGroupImageBuilder.builder().all())
                .dealGroupId(DealGroupIdBuilder.builder().dpDealGroupId())
                .build();
        return queryCenterWrapper.preDealGroupDTO(queryByDealGroupIdRequest);
    }

    public Future getStocksFuture(EnvCtx envCtx, RelatedModuleCtx ctx, List<Integer> dealGroupIds) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule.AbstractRelatedDealIdProcessor.getStocksFuture(EnvCtx,RelatedModuleCtx,List)");
        List<ProductParam> productParamList = dealGroupIds.stream()
                .map(dealGroupId -> ProductParam.productScene(dealGroupId, ctx.getReq().getCityId()))
                .collect(Collectors.toList());
        SalesDisplayRequest multiRequest = SalesDisplayRequest.multiQuery(productParamList);
        multiRequest.setPlatform(envCtx.isMt() ? SalesPlatform.MT.getValue() : SalesPlatform.DP.getValue());
        Map<String, String> extra = Maps.newHashMap();
        //透传销量区间差异化，反爬标识
        extra.put(SaleConstants.MTSI_FLAG, ctx.getEnvCtx().getMtsiFlag());
        extra.put(SaleConstants.CONFUSION_FLAG, SaleConstants.GENERAL_SECTION);
        multiRequest.setExtra(extra);
        return dealStockSaleWrapper.preUnifiedStocksFuture(multiRequest);
    }

    public Future getSecKillSceneFuture(EnvCtx envCtx, List<Integer> dealGroupIds) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule.AbstractRelatedDealIdProcessor.getSecKillSceneFuture(com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx,java.util.List)");
        QuerySecKillSceneByMaterialRequest secKillSceneByMaterialRequest = new QuerySecKillSceneByMaterialRequest();
        secKillSceneByMaterialRequest.setPlatform(envCtx.isMt() ? AppPlatform.MT.getCode() : AppPlatform.DP.getCode());
        secKillSceneByMaterialRequest.setMaterialType(SeckillActivityMaterialTypeEnum.DEAL.getCode());
        secKillSceneByMaterialRequest.setMaterialIds(dealGroupIds);
        secKillSceneByMaterialRequest.setQuerySceneStrategy(QuerySecKillSceneStrategyEnum.ONGOING_filterNoReduce.getCode());
        secKillSceneByMaterialRequest.setUserId(envCtx.isMt() ? envCtx.getMtUserId() : envCtx.getDpUserId());//已确认判断平台后再使用
        secKillSceneByMaterialRequest.setDeviceId(envCtx.isMt() ? envCtx.getUuid() : envCtx.getDpId());
        secKillSceneByMaterialRequest.setClientType(ClientTypeEnum.isIos(envCtx.getClientType()) ? MaterialClientType.IPHONE.getCode() : MaterialClientType.ANDROID.getCode());
        secKillSceneByMaterialRequest.setClientVersion(envCtx.getVersion());
        return dealActivityWrapper.preSecKillSceneByMaterial(secKillSceneByMaterialRequest);
    }

    public BiMap<Integer, Integer> getMtDpDIdBiMap(EnvCtx envCtx, List<Integer> dealGroupIds) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule.AbstractRelatedDealIdProcessor.getMtDpDIdBiMap(com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx,java.util.List)");
        BiMap<Integer, Integer> mtDpDIdBiMap = HashBiMap.create();
        if (envCtx.isMt()) {
            List<IdMapper> idMappers = dealIdMapperService.queryByMtDealGroupIds(dealGroupIds);
            idMappers.forEach(idMapper -> {
                mtDpDIdBiMap.put(idMapper.getMtDealGroupID(), idMapper.getDpDealGroupID());
            });
        }
        return mtDpDIdBiMap;
    }

    public Map<Integer, DealGroupShop> getDealGroupShopMap(RelatedModuleReq req, RelatedModuleCtx ctx, List<Integer> dealGroupIds, BiMap<Integer, Integer> mtDpDIdBiMap) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule.AbstractRelatedDealIdProcessor.getDealGroupShopMap(RelatedModuleReq,RelatedModuleCtx,List,BiMap)");
        DealGroupShopSearchRequest dealGroupShopRequest = new DealGroupShopSearchRequest();
        List<Integer> dpDealGroupIds = ctx.getEnvCtx().isMt() ? new ArrayList<>(mtDpDIdBiMap.values()) : dealGroupIds;
        dealGroupShopRequest.setDealGroupIds(dpDealGroupIds);
        if (req.getUserLat() != null && req.getUserLng() != null) {
            dealGroupShopRequest.setGoogleLat(req.getUserLat());
            dealGroupShopRequest.setGoogleLng(req.getUserLng());
        }
        dealGroupShopRequest.setCityIds(Lists.newArrayList(ctx.getDpPoiDTO().getCityId()));
        dealGroupShopRequest.setPageSize(50);
        dealGroupShopRequest.setPlatform(Platform.MAIN_APP.name());
        return moreDealsWrapper.getDealGroupShops(dealGroupShopRequest);
    }
}