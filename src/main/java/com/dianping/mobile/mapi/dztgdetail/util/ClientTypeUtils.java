package com.dianping.mobile.mapi.dztgdetail.util;

import com.google.common.collect.Maps;
import com.sankuai.nib.mkt.common.base.enums.propertyValueType.ClientTypeEnum;

import java.util.Map;

import static com.dianping.deal.common.enums.ClientTypeEnum.*;

/**
 * client type 转换工具，从团购体系转成营销体系
 */
public final class ClientTypeUtils {

    private ClientTypeUtils() {
    }

    private static final Map<Integer, ClientTypeEnum> CLIENT_TYPE_MAP = Maps.newHashMap();

    static {
        CLIENT_TYPE_MAP.put(mt_mainApp_ios.getType(), ClientTypeEnum.IPHONE);
        CLIENT_TYPE_MAP.put(mt_mainApp_android.getType(), ClientTypeEnum.ANDROID);
        CLIENT_TYPE_MAP.put(mt_web.getType(), ClientTypeEnum.PC);
        CLIENT_TYPE_MAP.put(mt_wap.getType(), ClientTypeEnum.I_VERSION);
        CLIENT_TYPE_MAP.put(mt_weApp.getType(), ClientTypeEnum.MT_WE_CHAT_APPLET);

        CLIENT_TYPE_MAP.put(dp_mainApp_ios.getType(), ClientTypeEnum.DP_IPHONE);
        CLIENT_TYPE_MAP.put(dp_mainApp_android.getType(), ClientTypeEnum.DP_ANDROID);
        CLIENT_TYPE_MAP.put(dp_app_iPadHd.getType(), ClientTypeEnum.IPAD);
        CLIENT_TYPE_MAP.put(dp_web.getType(), ClientTypeEnum.DP_PC);
        CLIENT_TYPE_MAP.put(dp_wap.getType(), ClientTypeEnum.M_STATION);
        CLIENT_TYPE_MAP.put(dp_weApp.getType(), ClientTypeEnum.DP_WE_CHAT_APPLET);
        CLIENT_TYPE_MAP.put(dp_wxWap.getType(), ClientTypeEnum.DP_I_VERSION);
    }

    public static ClientTypeEnum convert(int clientType) {

        ClientTypeEnum promoClientType = CLIENT_TYPE_MAP.get(clientType);

        if (promoClientType == null) {
            promoClientType = isMtPlatform(clientType) ? ClientTypeEnum.I_VERSION : ClientTypeEnum.M_STATION;
        }

        return promoClientType;
    }

}
