package com.dianping.mobile.mapi.dztgdetail.common.constants;

/**
 * Created by yangquan02 on 19/4/28.
 */
public class LionConstants {

    public static final String APP_KEY = "com.sankuai.dzu.tpbase.dztgdetailweb";

    public static final String COMMON_APP_KEY = "com.sankuai.dzshoppingguide.detail.commonmodule";

    public static final String QUERY_CENTER_DEALGROUP_ATTR_KEYS = "com.sankuai.dzu.tpbase.dztgdetailweb.query.center.dealgroup.attr.keys";

    public static final String QUERY_CENTER_DEAL_ATTR_KEYS = "com.sankuai.dzu.tpbase.dztgdetailweb.query.center.deal.attr.keys";

    public static final String Image_Text_Detail_AbTest_Whitelist = "com.sankuai.dzu.tpbase.dztgdetailweb.ImageText.fold.whitelist";
    public static final String IOS_CHANNEL_MODULE_CONFIGS = "com.sankuai.dzu.tpbase.dztgdetailweb.ios.channel.module.configs";
    public static final String ANDROID_CHANNEL_MODULE_CONFIGS = "com.sankuai.dzu.tpbase.dztgdetailweb.android.channel.module.configs";
    public static final String NEW_CHANNEL_MODULE_CONFIGS = "com.sankuai.dzu.tpbase.dztgdetailweb.new.channel.module.configs";
    public static final String SELECTED_DEALGROUPIDS = "com.sankuai.dzu.tpbase.dztgdetailweb.selected.deals";
    public static final String GRAY_ENABLE = "com.sankuai.dzu.tpbase.dztgdetailweb.gray.enable";
    public static final String GRAY_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.gray.config";
    public static final String CHOICEST_ICON_DISPLAY_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.choicest.icon.display.switch";
    public static final String BOOK_CATEGORYIDS = "com.sankuai.dzu.tpbase.dztgdetailweb.book.categoryids";
    public static final String DETAIL_HIDE_CATEGORYIDS = "com.sankuai.dzu.tpbase.dztgdetailweb.detail.hide.categoryids";
    public static final String PRODUCT_TITLE_ENABLE = "com.sankuai.dzu.tpbase.dztgdetailweb.detail.product.title.enable";
    public static final String IM_CHANNEL_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.im.channel.ids";
    public static final String MarketPrice_Show_Config = "com.sankuai.dzu.tpbase.dztgdetailweb.marketprice.show.config";
    public static final String IM_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.im.category.ids";
    public static final String PICASPECTRATIO_EXP = "com.sankuai.dzu.tpbase.dztgdetailweb.picaspectratio.exp.results";
    public static final String PICSIZE_EXP = "com.sankuai.dzu.tpbase.dztgdetailweb.picaspectratio.exp.picsize";
    public static final String PICSIZE_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.picaspectratio.picsize.config";

    public static final String VOUCHER_DEAL_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.voucher.deal.category.ids";

    public static final String VOUCHER_DEAL_CUSTOM_STYLE_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.voucher.deal.custom.style.category.ids";

    public static final String COUNDDOWN = "com.sankuai.dzu.tpbase.dztgdetailweb.countdonw";
    public static final String FORCE_BOOKING_PUBLISH_CATEGORY = "com.sankuai.dzu.tpbase.dztgdetailweb.force.booking.publish.category";

    public static final String UNIFIED_ACTIVITY_ENABLE = "com.sankuai.dzu.tpbase.dztgdetailweb.unified.activity.enable";

    public static final String DISABLE_BUY_AND_SHARE_ATTR = "com.sankuai.dzu.tpbase.dztgdetailweb.disable.buy.and.share.attr";

    public static final String DOUHU_EXP_ID = "com.sankuai.dzu.tpbase.dztgdetailweb.douhu.exp.ids";

    public static final String DOUHU_MODULE_EXP_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.douhu.module.exp.config";

    public static final String BEAUTY_COUPON_DP_ENABLE = "com.sankuai.dzu.tpbase.dztgdetailweb.beauty.coupon.dp.enable";

    public static final String AVAILABLE_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.available.category.ids";

    public static final String JOY_BUY_BAR_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.joy.buy.bar.poi.category.ids";

    public static final String SHOP_AT_TOP_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.shop.at.top.category.ids";

    public static final String COST_EFFECTIVE_SHOP_AT_TOP_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.cost.effective.shop.at.top.category.ids";

    public static final String XI_YU_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.xiyu.category.ids";

    public static final String QIN_ZI_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.qinzi.category.ids";

    public static final String ZU_LIAO_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.zuliao.category.ids";

    public static final String SHOPPING_CART_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.shopping.cart.category.ids";

    public static final String SHOPPING_CART_NEW_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.shopping.cart.new.category.ids";

    public static final String COUPON_BAG_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.couponTag.configs";

    public static final String MarketPrice_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.showMarketPrice.category.ids";

    public static final String COUPON_BAR_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.coupon.bar";

    public static final String BEAUTY_BIANMEI_COUPON_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.beautybianmeicoupon.category.ids";

    public static final String MarketPrice_POI_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.showMarketPrice.shop.category.ids";

    public static final String COUPON_FUSION_CHANNELS = "com.sankuai.dzu.tpbase.dztgdetailweb.detail.coupon.fusion.channels";

    public static final String COUPON_FUSION_CATEGORIES = "com.sankuai.dzu.tpbase.dztgdetailweb.detail.coupon.fusion.categories";

    public static final String AB_EXP_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.ab.exp.category.ids";

    public static final String MULTI_PKGS_DISCOUNT_CATEGORIES = "com.sankuai.dzu.tpbase.dztgdetailweb.multi.pkgs.discount.categories";

    public static final String PRICE_ZHILI_CATEGORIES = "com.sankuai.dzu.tpbase.dztgdetailweb.price.zhili.categories";

    public static final String HIDE_MARKETPRICE_CATEGORY_LIST = "com.sankuai.dzu.tpbase.dztgdetailweb.hide.marketprice.dealgroupcategories";

    public static final String NORMAL_HIDE_MARKETPRICE_CATEGORY_LIST = "com.sankuai.dzu.tpbase.dztgdetailweb.hide.marketprice.normal.dealcategories";

    public static final String DIRECT_HIDE_MARKETPRICE_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.direct.hide.marketprice.config";

    public static final String POI_DISTANCE_CATEGORY_BLACKLIST = "com.sankuai.dzu.tpbase.dztgdetailweb.poi.distance.category.blacklist";

    public static final String JOY_CARD_ABTEST_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.joy.card.abtest.poi.category.ids";

    public static final String XI_YU_SHOP_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.xiyu.shop.category.ids";

    public static final String QIN_ZI_SHOP_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.qinzi.shop.category.ids";

    public static final String ZU_LIAO_SHOP_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.zuliao.shop.category.ids";

    public static final String LYY_SHOP_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.lyy.shop.ids";

    public static final String POI_ADDRESS_CUSTOMIZED_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.poi.address.customized.config";

    public static final String HIDE_POI_ADDRESS_CFG = "com.sankuai.dzu.tpbase.dztgdetailweb.hide.poi.address.cfg";

    public static final String HEGUI_NOTICE_SHOPID_WHITELIST = "com.sankuai.dzu.tpbase.dztgdetailweb.hegui.notice.shopid.whitelist";

    public static final String HEGUI_NOTICE_CUSTOMER_NAME_MAP = "com.sankuai.dzu.tpbase.dztgdetailweb.hegui.notice.customer.name.map";

    public static final String DRIVING_SHOP_GRAY_CFG = "com.sankuai.dzu.tpbase.dztgdetailweb.driving.shop.gray.cfg";

    public static final String BOOK_SHOW_TYPES = "com.sankuai.dzu.tpbase.dztgdetailweb.book.showtypes";

    public static final String COLLABORATIVE_RECOMMEND_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.collaborative.recommend.swtich";

    public static final String TUAN_ACTIVITY_CONFIGS = "com.sankuai.dzu.tpbase.dztgdetailweb.tuan.activity.configs";

    public static final String SPECIAL_TAG_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.specialtagconfig";

    public static final String IMAGE_TEXT_DETAIL_FOLD_GRAY_CFG = "image.text.detail.fold.gray.cfg";

    public static final String FILTER_PHONE_NO_POI_CATEGORIES = "com.sankuai.dzu.tpbase.dztgdetailweb.filter.phone.no.poi.categories";

    public static final String DEAL_RELATED_BEHAVIOR_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.deal.related.behavior.config";

    public static final String LIREN_NATIONWIDE_SALES_GRAY_DP_POIS = "com.sankuai.dzu.tpbase.dztgdetailweb.liren.nationwide.sales.dppois";

    public static final String LIREN_NATIONWIDE_SALES_GRAY_MT_POIS = "com.sankuai.dzu.tpbase.dztgdetailweb.liren.nationwide.sales.mtpois";

    public static final String LIREN_NATIONWIDE_SALES_GRAY_DP_CITYS = "com.sankuai.dzu.tpbase.dztgdetailweb.liren.nationwide.sales.dpcitys";

    public static final String LIREN_NATIONWIDE_SALES_GRAY_MT_CITYS = "com.sankuai.dzu.tpbase.dztgdetailweb.liren.nationwide.sales.mtcitys";

    public static final String EDU_DEAL_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.edu.total.categoryid";

    public static final String EDU_ONLINE_DEAL_SERVICE_LEAF_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.edu.online.serviceleafids";


    public static final String CAN_DEGRADE_MT_STYLE_SERVICE_LEAF_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.candegradestyle.mt.serviceleafids";

    public static final String CAN_DEGRADE_MT_STYLE_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.candegradestyle.mt.categoryids";

    public static final String TIME_STOCK_PLAN_QUERY_CATEGORIES = "com.sankuai.dzu.tpbase.dztgdetailweb.timestockplan.categories";

    public static final String WARM_UP_BANNER_CATEGORIES = "com.sankuai.dzu.tpbase.dztgdetailweb.warmupbanner.categories";

    public static final String RHINO_LIMITED_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.rhinolimited.switch";
    public static final String PRICE_PROTECTION_SHOW_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.price.protection.switch";

    public static final String MALL_MEMBER_CATEGORIES = "com.sankuai.dzu.tpbase.dztgdetailweb.mall.member.categories";

    /**
     * 会员专属团单适用的分类
     */
    public static final String MEMBER_CATEGORIES = "com.sankuai.dzu.tpbase.dztgdetailweb.member.categories";
    public static final String MALL_MEMBER_WHITE_DEAL_GROUP_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.mall.member.dealgroupId.whitelist";
    public static final String MALL_MEMBER_MT_MINI_APP_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.mall.member.mt.mini.app.switch";
    public static final String MALL_MEMBER_DP_MINI_APP_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.mall.member.dp.mini.app.switch";

    /**
     * 丽人全国销量展示灰度门店配置，lion文件配置规则
     */
    public static final String LIREN_NATIONWIDE_SALES_LION_FILE_CONFIG_CLIENT = "beauty-deal-service";
    public static final String LIREN_NATIONWIDE_SALES_LION_FILE_CONFIG_MT_CONTENT = "beauty-national-sale-mtshop-whiteList";
    public static final String LIREN_NATIONWIDE_SALES_LION_FILE_CONFIG_DP_CONTENT = "beauty-national-sale-dpshop-whiteList";
    /**
     * 团详pageSource和报价source的映射
     */
    public static final String PAGESOURCE_TO_PRICE_SOURCE = "com.sankuai.dzu.tpbase.dztgdetailweb.pagesource.to.price.source";
    //沉浸式头图的pagesource
    public static final String IMMERSIVE_HEADER_IMAGE_SOURCE = "com.sankuai.dzu.tpbase.dztgdetailweb.imm.head.pic.pagesource";
    public static final String PAGESOURCE_TO_ORDER_PROMOTIONChANNEL = "com.sankuai.dzu.tpbase.dztgdetailweb.pagesource.to.order.promotionchannel";
    public static final String PAGESOURCE_TO_ORDER_TRAFFICFLAG = "com.sankuai.dzu.tpbase.dztgdetailweb.pagesource.to.order.trafficFlag";
    // 团详页登录校验开关
    public static final String DETAIL_LOGIN_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.detail.login.clienttype.switch";
    // 展示用户行为（包括最近购买）分类
    public static final String RELATED_BEHAVIOR_CATEGORIES = "com.sankuai.dzu.tpbase.dztgdetailweb.related.behavior.categories";
    // 不能购买预热底bar展示优惠后价格的分类
    public static final String COMING_BAR_PROMO_CATEGORIES = "com.sankuai.dzu.tpbase.dztgdetailweb.coming.bar.promo.categories";
    // 控制团详折叠样式的pagesource和团单分类
    public static final String FOLD_STYLE_PAGESOURCE_CATEGORIES = "com.sankuai.dzu.tpbase.dztgdetailweb.fold.style.pagesource.categories";
    // 附近优惠团购模块团单分类和推荐BizId的映射
    public static final String CATEGORY_2_RECOMMEND_BIZID = "com.sankuai.dzu.tpbase.dztgdetailweb.category.recommendBizId";
    // 附近优惠团购模块团单分类和推荐门店BizId的映射
    public static final String CATEGORY_2_RECOMMEND_SHOP_BIZID = "com.sankuai.dzu.tpbase.dztgdetailweb.category.recommendShopBizId";
    // generalinfo和scene的映射
    public static final String GENERAL_INFO_2_SCENE_CODE = "com.sankuai.dzu.tpbase.dztgdetailweb.generalinfo.sceneCode";

    // 团详关联团单模块scene和title的映射
    public static final String RETEADMODULE_SCENE_TITLE = "com.sankuai.dzu.tpbase.dztgdetailweb.reteadmodule.scene.title";

    //快照团单类目ID,控制浮层及展示标签的展示
    public static final String SNAPSHOTPHOTO_CATEGORYIDS = "com.sankuai.dzu.tpbase.dztgdetailweb.snapshotphoto.categoryid";
    // 关闭买约一体浮窗行业白名单
    public static final String HIDE_RESERVE_ENTRANCE = "com.sankuai.dzu.tpbase.dztgdetailweb.hide.reserve.entrance";
    // 团单分类和榜单scene映射关系
    public static final String RANK_SCENE_RELATION = "com.sankuai.dzu.tpbase.dztgdetailweb.category.rank.scene.relation";
    // 自定义退改条款类目白名单
    public static final String CUSTOM_REFUND_CATGORY_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.custom.refund.category.config";
    // 自定义头图配置
    public static final String CUSTOM_HEADER_PIC_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.custom.header.pic.config";
    // 匹配和头图策略关系配置
    public static final String CATEGORY_HEADER_PIC_PROCESSOR_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.category.header.pic.processor.config";
    // 丽人命中头图比例团单标签列表
    public static final String BEAUTY_HEADER_PIC_DEAL_TAG_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.beauty.header.pic.deal.tag.config";
    // 匹配和头图尺寸关系配置
    public static final String CATEGORY_HEADER_PIC_SCALE_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.category.header.pic.scale.config";
    // 头图&款式展示规则
    public static final String HEADER_PIC_EXHIBIT_SHOW_RULE_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.header.pic.exhibit.show.rule.config";
    // 行业容器比例团单白名单
    public static final String INDUSTRY_SCALE_DEAL_WHITELIST_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.industry.scale.deal.whitelist.config";
    // 款式标题配置信息
    public static final String CATEGORY_EXHIBIT_TITLE_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.category.exhibit.title.config";
    // 是否压缩视频封面
    public static final String COMPRESS_VIDEO_COVER_PIC = "com.sankuai.dzu.tpbase.dztgdetailweb.compress.video.cover.pic";
    // 请求款式信息行业类目白名单
    public static final String CALL_EXHIBIT_INFO_WHITELIST_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.call.exhibit.info.config";
    // 境外门店配置项
    public static final String CUSTOM_OVERSEAS_CATGORY_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.custom.overseas.category.config";

    public static final String NEW_RESERVE_MRN_VERSION = "com.sankuai.dzu.tpbase.dztgdetailweb.newreserve.mrnversion";
    // 无忧通商品属性value配置，默认key：
    public static final String WUYOUTONG_ATTR_VALUES = "com.sankuai.dzu.tpbase.dztgdetailweb.wuyoutong.attr.values";

    // 无忧通商品属性弹窗文案
    public static final String WUYOUTONG_POPUP_EXTS = "com.sankuai.dzu.tpbase.dztgdetailweb.wuyoutong.popup.exts";

    // 无忧通商品标题icon
    public static final String WUYOUTONG_TITLE_ICON = "com.sankuai.dzu.tpbase.dztgdetailweb.wuyoutong.title.icon";

    // 无忧通商品标题icon
    public static final String WUYOUTONG_MIN_MRNVERSION = "com.sankuai.dzu.tpbase.dztgdetailweb.wuyoutong.min.mrnversion";

    public static final String NEW_RESERVE_ENABLE = "com.sankuai.dzu.tpbase.dztgdetailweb.newreserve.enable";

    //销量区间化ab实验开关
    public static final String SALE_SECTION_ABEXP_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.saleSection.abexp.switch";

    //销量区间化规则
    public static final String SALE_GENERAL_SECTION_RULE_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.sales.general.section.rule.config";

    //亮点模块样式
    public static final String HIGHLIGHTS_STYLE_TYPE = "com.sankuai.dzu.tpbase.dztgdetailweb.highlights.style.type";

    //亮点模块浮层标识
    public static final String HIGHLIGHTS_IDENTIFY = "com.sankuai.dzu.tpbase.dztgdetailweb.highlights.identify";

    //多买多省搭售展示条目个数
    public static final String BUY_MORE_SAVE_MORE_SHOW_ITEM_SIZE = "com.sankuai.dzu.tpbase.dztgdetailweb.buy.more.save.more.show.item.size";

    // 多买多省文案map
    public static final String BUY_MORE_SAVE_MORE_TEXT_MAP = "com.sankuai.dzu.tpbase.dztgdetailweb.buy.more.save.more.text.map";

    //价保浮层
    public static final String LAYER_CONFIGS_DP = "com.sankuai.dzu.tpbase.dztgdetailweb.priceprotection.layerConfigs.dp";
    public static final String LAYER_CONFIGS_DP_UNRETURN = "com.sankuai.dzu.tpbase.dztgdetailweb.priceprotection.layerConfigs.dp.unreturn";

    //价保浮层
    public static final String LAYER_CONFIGS_MT = "com.sankuai.dzu.tpbase.dztgdetailweb.priceprotection.layerConfigs.mt";
    public static final String LAYER_CONFIGS_MT_UNRETURN = "com.sankuai.dzu.tpbase.dztgdetailweb.priceprotection.layerConfigs.mt.unreturn";

    // LE预付款团购
    public static final String TRADE_ASSURANCE_LAYER_CONFIGS = "com.sankuai.dzu.tpbase.dztgdetailweb.priceprotection.layerConfigs.trade.assurance";
    public static final String PREPAY_DP_MINI_APP_BUY_BAR_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.prepay.dp.mini.app.buybar.switch";

    //价保浮层categoryId
    public static final String PRICE_PROTECTION_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.priceprotection.categoryIds";

    //LE洗涤履约团单相关三级类目
    public static final String LE_INSURANCE_AGREEMENT_CATEGORY = "com.sankuai.dzu.tpbase.dztgdetailweb.le.insurance.agreement.category";
    /**
     * 款式图片配置，包含常驻标签、展示样式等
     */
    public static final String IMMERSIVE_EXHIBIT_IMAGE_CONFIG = "immersive.exhibit.image.config";

    /**
     * 款式筛选项配置，展示款式categoryId白名单
     */
    public static final String IMMERSIVE_EXHIBIT_IMAGE_SHOW_FILTER_CATEGORY_IDS_CONFIG = "immersive.exhibit.image.show.filter.categoryIds.config";

    /**
     * 团详框架改版
     */
    public static final String ANDROID_CHANNEL_MODULE_CONFIGS_V2 = "com.sankuai.dzu.tpbase.dztgdetailweb.android.channel.module.configs.v2";
    /**
     * 团详框架改版
     */
    public static final String IOS_CHANNEL_MODULE_CONFIGS_V2 = "com.sankuai.dzu.tpbase.dztgdetailweb.ios.channel.module.configs.v2";
    /**
     * 团详框架改版
     */
    public static final String NEW_CHANNEL_MODULE_CONFIGS_V2 = "com.sankuai.dzu.tpbase.dztgdetailweb.new.channel.module.configs.v2";

    // 是否下发3:4尺寸类目ID白名单
    public static final String DISTRIBUTE_IMAGE_SCALE_WHITELIST = "com.sankuai.dzu.tpbase.dztgdetailweb.distribute.image.scale.whitelist";
    // 展示达人视频类目
    public static final String SHOW_DAREN_VIDEO_CATEGORY = "com.sankuai.dzu.tpbase.dztgdetailweb.show.daren.video.category";
    // 不调用到餐接口品类白名单
    public static final String SKIP_CALL_PROMETHEUS_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.skip.call.prometheus.category.ids";
    /**
     * 允许展示综团比价浮层的团单类目
     */
    public static final String COMPARE_PRICE_CATEGORY_ALLOW = "compare.price.category.allow";

    /**
     * 综团比价AB实验开关
     */
    public static final String COMPARE_PRICE_AB_TEST_SWITCH = "compare.price.ab.test.switch";

    /**
     * 综团比价门店黑名单，此黑名单门店不展示查价比价入口
     */
    public static final String COMPARE_PRICE_SHOP_BLACK_LIST = "compare.price.shop.blacklist";

    /**
     * 保障游乐险配置信息
     */
    public static final String FEATURE_DETAIL_INFO = "com.sankuai.dzu.tpbase.dztgdetailweb.feature.detail.config";
    // 游乐险品类关系配置
    public static final String CATEGORY_INSURANCE_PROCESSOR_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.category.insurance.processor.config";

    /**
     * 在线教育团单的退款规则显示配置
     */
    public static final String ONLINE_EDU_DEAL_REFUND_RULE_ATTR_KEY = "com.sankuai.dzu.tpbase.dztgdetailweb.edu.online.refundrule.show";

    /**
     * 教育的在线类团购分类
     */
    public static final String EDU_ONLINE_DEAL_TYPE_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.edu.online.category2servicetypes";

    /**
     * 使用提单页浮层的分类
     */
    public static final String SHOW_CREATE_ORDER_LAYER_DEAL_CATEGORY_SERVICE_TYPE_WHITE  = "showcreateorderlayer.deal.category.servicetype.whitelist";

    /**
     * 猜喜侧的ModuleKey顺序配置
     */
    public static final String CAI_JI_MODULE_CONFIG_KEY_SORT  = "com.sankuai.dzu.tpbase.dztgdetailweb.caixi.module.sort";

    /**
     * 使用太极多Sku的分类
     */
    public static final String MULTI_SKU_DEAL_CATEGORY_SERVICE_TYPE_WHITE  = "multi.sku.deal.category.servicetype.whitelist";

    /**
     * 反爬接口隐藏关键信息的开关
     */
    public static final String ANTI_CRAWLER_HIDE_KEY_INFO_SWITCH = "anti.crawler.hide.key.info.switch";
    /*
    * getmtpoilist接口反爬接口隐藏关键信息的开关
    * */
    public static final String GETMTPOILIST_HIDE_KEY_INFO_SWITCH = "getmtpoilist.hide.key.info.switch";
    /**
     * 团详主接口降级开关表达式
     */
    public static final String DZ_DEAL_BASE_FAULT_TOLERANCE_EXPRESSION = "dzdealbase.fault.tolerance.expression";
    /**
     * 团详样式接口降级开关表达式
     */
    public static final String DZ_DEAL_STYLE_FAULT_TOLERANCE_EXPRESSION = "dzdealstyle.fault.tolerance.expression";
    /**
     * 发生异常时会导致降级的下游接口列表及异常类型配置
     */
    public static final String FAULT_TOLERANCE_INTERFACES_EXCEPTION =  "fault.tolerance.interfaces.exception";
    /**
     * 头图视频迭代开关
     */
    public static final String HEAD_VIDEO_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.headvideo.switch";
    /**
     * 小视界团详适配样式AB实验配置
     */
    public static final String MINI_VISION_STYLE_AB_TEST_EXP = "mini.vision.style.ab.test.exp";

    /**
     * 商家会员优惠，白名单类目
     */
    public static final String MEMBER_PRICE_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.memberprice.category.ids";

    public static final String DISTRIBUTE_HEALTH_CHECK_KUAI_SHOU_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.distribute.health.check.kuai.shou.config";

    /**
     * 多买多省商品状态配置
     */
    public static final String BUY_MORE_SAVE_MORE_DEAL_STATUS_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.bmsm.deal.status.config";
    /**
     * 渠道打点白名单
     */
    public static final String PAGE_SOURCE_METRIC_WHITE_LIST = "com.sankuai.dzu.tpbase.dztgdetailweb.page.source.metric.white.list";

    /**
     * 渠道信息配置
     */
    public static final String PAGE_SOURCE_INFO_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.page.source.info.config";
    /**
     * 款式列表分scene开关
     */
    public static final String IMMERSIVE_IMAGE_SCENE_SWITCH = "immersive.image.scene.switch";

    /**
     * 推荐款式配置
     */
    public static final String RECOMMEND_EXHIBIT_IMAGE_CONFIG = "recommend.exhibit.image.config";

    /**
     * 美甲热门款式模块开关
     */
    public static final String HOT_NAIL_STYLE_MODULE_SWITCH = "hot.nail.style.module.switch";
    /**
     * 美甲频道页运营位-热门款式
     */
    public static final String NAIL_FILTER_CONFIG = "nail.filter.config";
    /**
     * 热门款式模块配置项
     */
    public static final String HOT_NAIL_STYLE_CONFIG = "hot.nail.style.config";

    /**
     * 允许查询美甲热门款式的二级类目id
     */
    public static final String ALLOW_HOT_NAIL_STYLE_CATEGORY_IDS = "allow.query.hot.nail.style.categoryids";

    /**
     * 热门款式模块黑名单商户id，区分平台
     */
    public static final String HOT_NAIL_MODULE_BLACK_LIST_SHOP = "hot.nail.module.blacklist.shop";

    /**
     * 图文详情压缩灰度配置
     */
    public static final String IMAGE_TEXT_COMPRESS_GRAY_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.imageTextCompressGrayConfig";
    /**
     * 私域小程序团详氛围条底图
     */
    public static final String MINI_PROGRAM_LIVE_ATMOSPHERE_BASE_IMG = "mini.program.live.atmosphere.base.img";

    /**
     * 企业微信入口配置
     */
    /**
     * 直播间、非直播间渠道进入直播渠道品团详页buybar处理开关
     */
    public static final String MLIVE_BUY_BAR_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.mlive.buybarSwitch";

    public static final String FLOW_ENTRY_WX_MATERIAL_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.flowEntryWxMaterial.config";

    /**
     * 到手价=市场价隐藏市场价后台二级类目配置
     */
    public static final String HIDE_MARKETPRICE_CATEGORYID_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.hidemarketprice.categoryid.config";

    /**
     * 是否过滤多sku场景下的无效套餐
     */
    public static final String FILTER_STATUS_FOR_MULTI_SKU = "com.sankuai.dzu.tpbase.dztgdetailweb.filter.status.for.multi.sku";

    /**
     * apollo，反爬信息校验开关
     */
    public static final String APOLLO_AUTHENTIC_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.apollo.authentic.switch";


    /**
     * 直播渠道品 分享功能按钮开关
     */
    public static final String MLIVE_CHANNEL_SHAREABLE_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.mlive.shareable.switch";

    /**
     * 订单页款式展示开关
     */
    public static final String ORDER_NAIL_STYLE_IMAGE_SWITCH = "order.nail.style.image.switch";

    /**
     * 订单详情页款式展示数量配置
     */
    public static final String ORDER_NAIL_STYLE_NUM_CONFIG = "order.nail.style.num.config";

    /**
     * 订单详情页款式跳转款式列表页的跳链
     */
    public static final String ORDER_NAIL_STYLE_LIST_URL = "order.nail.style.list.url";

    /**
     * 购买须知结构化灰度配置
     */
    public static final String STRUCTURED_PURCHASE_NOTE_GRAY_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.structured.purchase.note.gray.config";

    /**
     * 展示新预约浮窗类目配置
     */
    public static final String SHOW_RESERVATION_GRAY_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.show.reservation.gray.config";

    /**
     * 春节不打烊配置
     */
    public static final String REMINDER_EXTEND_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.reminderinfo.extend.config";

    /**
     * 热门款式模块对照组实验上报开关
     */
    public static final String HOT_NAIL_MODULE_DOUHU_COMPARE_EXP_REPORT_SWITCH = "hot.nail.module.douhu.compare.exp.report.switch";
    /**
     * 须知条定制逻辑类目配置
     */
    public static final String CUSTOM_REMINDER_INFO_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.custom.reminder.info.config";

    /**
     * 宠物类目配置
     */
    public static final String PET_CATEGORY_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.pet.category.config";

    /**
     * 宠物代金券标题展示开关
     */
    public static final String PET_VOUCHER_TITLE_SHOW_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.pet.voucher.title.show.switch";
    /**
     * 通用浮层配置
     */
    public static final String GENERAL_LAYER_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.general.layer.config";

    /**
     *限回头客榜单及复购信息透传--限制条适用行业类目ID
     */
    public static final String LIMIT_INFO_SUIT_CATEGORY_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.limitinfo.category.config";
    /**
     * 适用门店位置配置
     */
    public static final String APPLY_SHOP_POSITION_CONFIG = "apply.shop.position.rule.config";
    /**
     * 实验结果映射关系
     */
    public static final String EXP_RESULT_2_RESULT = "com.sankuai.dzu.tpbase.dztgdetailweb.douhu.module.exp.result.map";

    /**
     * 请求来源与key value的映射
     */
    public static final String PAGE_SOURCE_TO_KEY_VALUE = "com.sankuai.dzu.tpbase.dztgdetailweb.pagesource.map.key.value";

    /**
     * 网友评价tab配置
     */
    public static final String FILTER_REVIEWER_TAB_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.filter.reviewer.tab.config";
    /**
     * 禁止分享客户端配置
     */
    public static final String FORBID_SHARE_CLINT_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.forbid.share.client.config";

    /**
     * 赠品类别配置——用于判断是否展示赠品
     */
    public static final String DEAL_GIFT_CATEGORY_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.deal.gift.category.config";
    /**
     * processor粒度接口和端配置映射关系
     */
    public static final String URL_PROCESSOR_DZTG_CLIENT_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.url.processor.dztg.client.config";
    /**
     * 接口粒度接口和端配置映射关系
     */
    public static final String URL_DZTG_CLIENT_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.url.dztg.client.config";

    // shoppingCard底bar构建size
    public static final String SHOPPING_CART_BUILDER_BUTTON_SIZE = "com.sankuai.dzu.tpbase.dztgdetailweb.shopping.card.button.size";

    /**
     * 购买按钮提示弹窗信息配置
     */
    public static final String PURCHASE_BTN_MESSAGE_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.purchase.btn.message.config";
    /**
     * 可复购货架配置
     */
    public static final String REPURCHASE_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.repurchase.config";


    /**
     * 穿戴甲款式查询条目配置
     */
    public static final String WEARABLE_NAIL_STYLE_LIMIT_NUM = "clothing.nail.style.limit.num";

    /**
     * 多款式个数判断条件配置
     */
    public static final String MULTI_STYLE_SIZE_CONFIG = "multi.style.size.config";

    /**
     * 启用穿戴甲门店processor的开关
     */
    public static final String WEARABLE_NAIL_SHOP_CONFIG = "wearable.nail.shop.processor.switch";
    /**
     * 穿戴甲门店标签配置
     */
    public static final String WEARABLE_NAIL_SHOP_TAG_ID_CONFIG = "wearable.nail.shop.tag.id.config";

    public static final String GLASS_GENUINE_GUARANTEE_CONFIG = "glass.genuine.guarantee.tag.config";

    public static final String EXTRA_MOUDLE_CONFIG ="com.sankuai.dzu.tpbase.dztgdetailweb.extradealdetailmodule.moduleconfig";
    /**
     * 购买按钮展示价格标签的团单二级类目配置
     */
    public static final String DEAL_BTN_PRICE_STRENGTH_TAG_CATEGORY_IDS = "deal.btn.price.strength.tag.category.ids";
    /**
     * 特团“拼团规则”配置，图标+文案
     */
    public static final String PIN_TUAN_RULE = "com.sankuai.dzu.tpbase.dztgdetailweb.pin.tuan.rule";
    /**
     * 拼团参数配置
     */
    public static final String COST_EFFECTIVE_PIN_TUAN_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.cost.effective.pin.tuan.config";
    /**
     * 是否开启特团拼团
     */
    public static final String COST_EFFECTIVE_PIN_TUAN_ENABLE = "com.sankuai.dzu.tpbase.dztgdetailweb.cost.effective.pin.tuan.enbale";

    /**
     * 是否开启ssr开关
     */
    public static final String SSR_EXP_ENABLED = "com.sankuai.dzu.tpbase.dztgdetailweb.ssr.exp.enabled";

    /**
     * 当适用门店为1时展示小门店的category
     */
    public static final String IS_SHOW_SHOP_CATEGORIES = "com.sankuai.dzu.tpbase.dztgdetailweb.isShowShopCategory";

    /**
     * 日志打印条件配置
     */
    public static final String LOG_PRINT_CONDITION_CONFIG = "log.print.condition.config";

    /**
     * 美团美播小程序是否开启internal
     */
    public static final String ENABLE_MT_LIVE_WEIXIN_MINIAPP_INTERNAL = "enable.meituan.live.weixin.miniapp.internal";

    /**
     * 提单页为浮层样式的团单类目列表
     */
    public static final String POP_OVERLAY_CATEGORIES = "com.sankuai.dzu.tpbase.dztgdetailweb.pop.overlay.category";

    /**
     * 基于PopOverlayButtonBuilder实现提单页浮层的灰度开关
     */
    public static final String POP_OVERLAY_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.pop.overlay.switch";
    /**
     * 团单价格趋势说说明
     */
    public static final String DEAL_PRICE_TREND_DESC = "deal.price.trend.desc";

    /**
     * 局改团购售价
     */
    public static final String BUREAU_CHANGES_GROUP_PURCHASE_PRICE = APP_KEY + ".bureau.changes.group.purchase.price";

    /**
     * 商家接口控制
     */
    public static final String MERCHANT_API_CONTROL = "com.sankuai.dzu.tpbase.dztgdetailweb.merchant.api.pass";
    /**
     * 下发分享图灰度开关
     */
    public static final String SCREEN_SHOT_SHARE_ENABLE = "com.sankuai.dzu.tpbase.dztgdetailweb.screen.shot.share.enable";

    /**
     * 洗涤履约保障相关浮层配置
     */
    public static final String LE_INSURANCE_AGREEMENT_LAYER_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.insurance.agreement.layerConfig";

    public static final String LE_INSURANCE_AGREEMENT_LAYER_CONFIG_URL = "com.sankuai.dzu.tpbase.dztgdetailweb.insurance.agreement.layerConfig.url";

    /**
     * 休娱特团渠道同店升单&跨店搭售
     * 搭售玩法id
     */
    public static final String PLAY_ID = "com.sankuai.dzu.tpbase.dztgdetailweb.playId";
    /**
     * 休娱特团渠道同店升单&跨店搭售
     * 搭售玩法开关
     */
    public static final String PLAY_CENTER_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.playcenter.service.switch";
    /**
     * 【使用场景】在指定的团单二级类目下，预约信息为空的团单，不显示预约信息
     * 【值描述】团单二级类目
     */
    public static final String RESERVATION_EMPTY_NOT_DISPLAY_TYPE = "reservation.empty.not.display.type";

    /**
     * 多买多省全量开关
     */
    public static final String PLAY_CENTER_ALL_PASS = "com.sankuai.dzu.tpbase.dztgdetailweb.playcenter.service.all.pass";
    /**
     * 多买多省白名单，unionid
     */
    public static final String PLAY_CENTER_WHITE_LIST = "com.sankuai.dzu.tpbase.dztgdetailweb.playcenter.service.white.list";
    /**
     * 家电维修448、居家维修445、数码影音维修451、开锁415、管道疏通414、电脑维修449、手机维修450。
     * 在这些类目上新增功能：针对"维修费以师傅上门报价为准"文案展示新增过滤逻辑(过滤条件为判断支持尾款 和 商品类型字段无值或有值但≠“一口价”)
     */
    public static final String LIFE_DEAL_GROUP_CATEGORY = "com.sankuai.dzu.tpbase.dztgdetailweb.life.dealGroupCategory";
    /**
     * 报价poi传参打点日志开关
     */
    public static final String PRICE_DISPLAY_LOG_ENABLED = "price.display.log.enabled";
    /**
     * 报价poi传参日志抽样配置
     */
    public static final String TEMPLATE_SCENE_LOG_SAMPLING_RATE = "template.scene.log.sampling.rate";

    /**
     * 团单详情页赠品展示playId
     */
    public static final String DEAL_GIFT_PLAY_ID_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.deal.gift.play.id.config";

    /**
     * 履约保障标签配置-保障标签和浮层
     */
    public static final String PERFORMANCE_GUARANTEE_FEATURE_DETAIL_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.performance.guarantee.feature.detail.config";

    /**
     * 放心改团单预约跳转链接(美团)
     */
    public static String REASSURED_REPAIR_DEAL_RESERVE_REDIRECT_MTURL = "com.sankuai.dzu.tpbase.dztgdetailweb.reassured.repair.deal.reserve.redirect.mturl";

    /**
     * 放心改团单预约跳转链接(点评)
     */
    public static String REASSURED_REPAIR_DEAL_RESERVE_REDIRECT_DPURL = "com.sankuai.dzu.tpbase.dztgdetailweb.reassured.repair.deal.reserve.redirect.dpurl";
    /**
     * N选1政府消费券MRN版本控制
     */
    public static final String N_SELECT_ONE_GOVERNMENT_COUPON_MRN_VERSION = "n.select.one.government.coupon.mrn.version";

    /**
     * 定制化可用时间适用行业类目ID
     * 根据属性key：times_available_all 定制化可用时间
     */
    public static final String CUSTOM_AVAILABLE_TIMES_OF_DAYS = "com.sankuai.dzu.tpbase.dztgdetailweb.custom.available.times.of.days";

    /**
     * 保洁商品信息
     */
    public static final String CLEANING_PRODUCT_INFORMATION_CONFIG = "cleaning.product.information.config";

    /**
     * 保洁自营保障相关浮层配置
     */
    public static final String LE_INSURANCE_GUARANTEE_LAYER_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.insurance.guarantee.layerConfig";

    /**
     * 过滤无效sku开关
     */
    public static final String DEALS_FILTER_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.deal.status.filter.switch";

    /**
     * 美甲沉浸页款式置顶功能开关
     */
    public static final String EXHIBIT_SOURCE_BEAUTY_NAIL_IMMERSIVE_IMAGE_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.exhibit.source.beauty.nail.immersive.image.switch";

    /**
     * 美甲款式置顶功能开关
     */
    public static final String EXHIBIT_SOURCE_BEAUTY_NAIL_TOP_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.exhibit.source.beauty.nail.top.switch";

    /*
    * 安心医zdc标签
    * */
    public static final String MEDICAL_SAFE_TREAT_ZDC_TAGID = "com.sankuai.dzu.tpbase.dztgdetailweb.medical.safe.treat.zdc.tagid";


    /**
     * 使用新款式接口类目列表
     */
    public static final String NEW_EXHIBIT_CAETGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.new.exhibit.category.ids";

    /**
     * 指定渠道分享配置
     */
    public static final String CUSTOM_PAGESOURCE_SHARE_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.custom.pagesource.share.config";

    /**
     * 预约入口AB实验类目
     */
    public static final String RESERVE_ENTRANCE_AB_TEST_CATE = "reserve.entrance.ab.test.category";

    /**
     * 优惠减负,新老样式切换开关
     */
    public static final String COUPON_ALLEVIATE_PROMO_STYLE_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.coupon.alleviate.style.config";

    /**
     * 小程序接入商家会员控制开关
     */
    public static final String MEMBER_CARD_MINIPROGRAM_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.membercard.miniprogram.switch";
    /**
     * 标准服务氛围配置
     */
    public static final String STANDARD_PRODUCT_ATMOSPHERE_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.distribute.standard.product.atmosphere";

    /**
     * 新客自动化测试开关
     */
    public static final String NEW_CUSTOMER_AUTOMATED_TESTING = "cleaning.product.information.new.customer.automated.testing";

    /**
     * 台球自助开台控制开关
     */
    public static final String SELFSERVICE_BILLIARDS_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.selfService.billiards.switch";

    /**
     * 摄影团详斗斛实验开关
     */
    public static final String PHOTO_NEW_DEAL_DETAIL_DOUHU_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.douhu.photo.deal.detail";

    /**
     * 摄影团详(新)类目控制
     */
    public static final String PHOTO_NEW_DEAL_DETAIL_CATEGORY = "com.sankuai.dzu.tpbase.dztgdetailweb.new.photo.deal.detail.category";
    /**
     * 摄影团详实验1类目控制
     */
    public static final String PHOTO_NEW_DEAL_DETAIL_ABTEST_CATEGORY = "com.sankuai.dzu.tpbase.dztgdetailweb.new.photo.deal.detail.abtest";

    /**
     * 摄影团详图片详情默认展开阈值
     */
    public static final String PHOTO_IMAGE_TEXT_FOLD_THRESHOLD = "com.sankuai.dzu.tpbase.dztgdetailweb.image.text.foldThreshold";

    /**
     * 自助开台类目白名单
     */
    public static final String SELF_AUTO_OPEN_TABLE_CATEGORY = "com.sankuai.dzu.tpbase.dztgdetailweb.auto.open.table.category";

    /*
    * 价格说明链接配置
    * */
    public static final String PRICE_EXPLAIN_LINK_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.price.explain.link.config";

    /**
     * 点评APP神会员生效最低mrn版本
     */
    public static final String MAGICAL_DP_MRN_MIN_VERSION = "dp.magical.member.valid.min.version";

    /*
     * 体检价格说明
     * */
    public static final String EXAM_PRICE_EXPLAIN_TEXT = "com.sankuai.dzu.tpbase.dztgdetailweb.exam.price.explain.text.config";
    /**
     * 留资型行业团单类目配置
     */
    public static final String LEADS_DEAL_CATE_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.leads.deal.cate.config";

    /**
     * 结婚行业留资型行业团单类目配置
     */
    public static final String WEDDING_LEADS_DEAL_CATE_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.wedding.leads.deal.cate.config";

    /**
     * poi后台类目配置白名单：结婚行业中超值特惠团单显示获取底价和咨询的按钮
     */
    public static final String WEDDING_SPECIAL_POI_BACK_CATE_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.wedding.special.poi.back.cate.config";

    /**
     * 留资型行业团单底bar配置
     */
    public static final String LEADS_DEAL_BOTTOM_BAR_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.leads.deal.bottom.bar.config";

    /**
     * 月子中心新房型
     */
    public static final String CARE_CENTER_HOUSE_STYLE = "com.sankuai.dzu.tpbase.dztgdetailweb.care.center.house.style";

    /**
     * 月子中心新房型留资数据查询匹配
     */
    public static final String CARE_CENTER_HOUSE_LEADS_COUNT = "com.sankuai.dzu.tpbase.dztgdetailweb.care.center.house.leads.count";

    /**
     * h5登录验证开关
     */
    public static final String H5_LOGIN_VERIFICATION = "com.sankuai.dzu.tpbase.dztgdetailweb.h5.login.verification";

    /**
     * 留资型行业团单最低MRN版本
     */
    public static final String LEADS_DEAL_MIN_MRN_VERSION = "com.sankuai.dzu.tpbase.dztgdetailweb.leads.deal.min.mrn.version";

    /**
     * 团详RCF缓存开关
     */
    public static final String DEAL_DETAIL_FLASH_CACHE_SWITCH = "deal.detail.flash.cache.switch";
    /**
     * 团详RCF快接口配置
     */
    public static final String DEAL_DETAIL_FLASH_CONFIG = "deal.detail.flash.gray.config";
    /**
     * 统一商品通用服务模块请求配置
     */
    public static final String COMMON_MODULE_CALL_CONFIG = "common.module.service.call.config";
    /**
     * 统一商品交易服务模块请求配置
     */
    public static final String TRADE_MODULE_CALL_CONFIG = "trade.module.service.call.config";
    /**
     * 统一商品通用服务模块请求版本控制
     */
    public static final String COMMON_MODULE_CALL_VERSION_CONFIG = "common.module.service.call.version.config";
    /**
     * 新版沉浸页面配置（跳转商家侧提供的页面）
     */
    public static final String IMMERSIVE_IMAGE_CONFIG = "deal.detail.immersive.image.config";
    /**
     * 新版沉浸页面配置（跳转商家侧提供的页面）
     */
    public static final String OLD_CASE_LIST_PAGE_CONFIG = "deal.detail.old.case.list.page.config";
    /**
     * 安心学适用类目
     */
    public static final String GUARANTEE_AN_XIN_XUE_CATEGORY = "com.sankuai.dzu.tpbase.dztgdetailweb.guarantee.an.xin.xue";
    /**
     * 安心学落地页
     */
    public static final String GUARANTEE_AN_XIN_XUE_DETAIL_MT = "com.sankuai.dzu.tpbase.dztgdetailweb.guarantee.an.xin.xue.detail.mt";
    public static final String GUARANTEE_AN_XIN_XUE_DETAIL_DP = "com.sankuai.dzu.tpbase.dztgdetailweb.guarantee.an.xin.xue.detail.dp";
    /**
     * 安心学落地页
     */
    public static final String GUARANTEE_AN_XIN_XUE_ICON = "com.sankuai.dzu.tpbase.dztgdetailweb.guarantee.an.xin.xue.icon";
    public static final String GUARANTEE_AN_XIN_EXERCISE_ICON = "com.sankuai.dzu.tpbase.dztgdetailweb.guarantee.an.xin.exercise.icon";
    public static final String GUARANTEE_AN_XIN_XUE_LAYER_TYPE = "com.sankuai.dzu.tpbase.dztgdetailweb.guarantee.an.xin.xue.layer.type";
    public static final String GUARANTEE_AN_XIN_XUE_DISPLAY_TEXT = "com.sankuai.dzu.tpbase.dztgdetailweb.guarantee.an.xin.xue.display.text";
    public static final String GUARANTEE_AN_XIN_EXERCISE_DISPLAY_TEXT = "com.sankuai.dzu.tpbase.dztgdetailweb.guarantee.an.xin.exercise.display.text";

    /**
     * 安心练落地页
     */
    public static final String GUARANTEE_AN_XIN_EXERCISE_DETAIL_MT = "com.sankuai.dzu.tpbase.dztgdetailweb.guarantee.an.xin.exercise.detail.mt";
    public static final String GUARANTEE_AN_XIN_EXERCISE_DETAIL_DP = "com.sankuai.dzu.tpbase.dztgdetailweb.guarantee.an.xin.exercise.detail.dp";

    /**
     * 团单id和团单分类信息 缓存的映射
     */
    public static final String DEAL_ID_TO_CATEGORY_CACHE_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.dealId2category.cache.switch";
    /**
     * 预订团单保障标签配置-保障标签和浮层
     */
    public static final String PREORDER_GUARANTEE_FEATURE_DETAIL_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.preorder.guarantee.feature.detail.config";

    /**
     * 猜喜渠道是否使用新渠道来源
     */
    public static final String USE_NEW_SOURCE_FOR_CAIXI_SWITCH = "use.new.source.for.caixi.switch";

    /**
     * 快照日志用户白名单
     */
    public static final String SNAPSHOT_LOG_USER_ID_WHITE_LIST = "snapshot.log.user.id.white.list";

    /**
     * 快照日志用户灰度配置
     */
    public static final String SNAPSHOT_LOG_USER_GRAY_CONFIG = "snapshot.log.user.gray.config";

    /**
     * 底部推荐参数配置
     */
    public static final String RELATED_RECOMMEND_CONFIG_MAP = "related.recommend.config.map";
    /**
     * 不展示同店推荐类目
     */
    public static final String HIDE_IN_SHOP_RECOMMEND_CATEGORY_IDS = "hide.in.shop.recommend.category.ids";

    /**
     * 是否使用底部推荐
     */
    public static final String USE_RECOMMEND_STRATEGY_SWITCH = "use.recommend.strategy.switch";
    /**
     * 查询新样式类目,通过attributeKey查询款式Id信息
     */
    public static final String NEW_STYLE_BY_SHOP_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.new.style.by.shop.category.ids";

    /**
     * 查询老案例类目,通过attributeKey-exhibits查询款式Id信息
     */
    public static final String OLD_CASE_BY_SHOP_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.old.case.by.shop.category.ids";

    /**
     * 比价助手分类目实验配置
     */
    public static final String COMPARE_PRICE_ASSISTANT_CATEGORY_AB_TEST_CONFIG = "compare.price.assistant.category.ab.test.config";

    public static final String SHOW_BEST_PRICE_GUARANTEE_TAG = "com.sankuai.dzu.tpbase.dztgdetailweb.show.best.price.guarantee.tag";
    /**
     * 是否打开rcf计算高度日志开关
     */
    public static final String RCF_CALCULATE_HEIGHT_LOG_SWITCH = "rcf.calculate.height.log.switch";


    /**
     * 素质教育类行业类目(2级类目)
     */
    public static final String QUALITY_EDU_CATION_BY_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.quality.edu.cation.by.category.ids";

    /**
     * 素质教育类行业类目(需要排除的额外4级类目)
     */
    public static final String QUALITY_EDU_CATION_BY_SERVICE_TYPE_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.quality.edu.cation.by.service.type.ids";

    /**
     * 款式场景code
     */
    public static final String EXHIBIT_DIGEST_SCENE_CODE = "exhibit.digest.scene.code";

    /**
     * 图片详情展示策略规则
     */
    public static final String IMAGE_TEXT_STRATEGY_RULE = "image.text.strategy.rule";

    public static final String DEAL_TIMES_CARD_REFUND_TABLE = "deal.times.card.refund.table.switch";

    /**
     * 预付金类目和文案配置
     */
    public static final String PREPAY_CATEGORY_TEXT_CONFIG = "prepay.category.text.config";

    /**
     * 是否展示维修二段支付团单的价格说明文案
     */
    public static final String ENABLE_SHOW_REPAIR_PRICE_DESC = "enable.show.repair.price.desc";

    /**
     * id映射缓存刷新时间配置
     */
    public static final String ID_MAPPER_CACHE_REFRESH_TIME_CONFIG = "id.mapper.cache.refresh.time.config";

    /**
     * id映射缓存过期时间配置
     */
    public static final String ID_MAPPER_CACHE_EXPIRE_TIME_CONFIG = "id.mapper.cache.expire.time.config";

    /**
     * 团购次卡c端表达优化控制开关
     */
    public static final String TIMES_DEAL_OPTIMIZE_SWITCH = "times_deal_optimize_switch";

    /**
     * 强预订团单类目
     */
    public static final String PRE_ORDER_CATEGORY_IDS = "pre.order.category.ids";

    /**
     * LE留资行业cross类目品推荐
     * prd：https://km.sankuai.com/collabpage/2494478870
     */
    public static final String LE_CROSS_RECOMMEND_DATA = "le.cross.recommend.data";

    /**
     * 券聚合开关
     */
    public static final String MAGIC_MEMBER_COUPON_AGGREGATE_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.magic.member.coupon.aggregate.switch";
    public static final String MAGIC_MEMBER_COUPON_AGGREGATE_USER_ID_RATIO = "com.sankuai.dzu.tpbase.dztgdetailweb.magic.member.coupon.aggregate.userid.ratio";
    public static final String MAGIC_MEMBER_COUPON_AGGREGATE_WHITE_LIST = "com.sankuai.dzu.tpbase.dztgdetailweb.magic.member.coupon.aggregate.white.list";

    /**
     * 展示安心植牙标签类目
     */
    public static final String SHOW_SAFE_IMPLANT_TAG_CATEGORY_IDS = "show.safe.implant.tag.category.ids";

    /**
     * 安心植牙团详页头图氛围配置
     */
    public static final String  SAFE_IMPLANT_HEAD_PIC_ATMOSPHERE_CONFIG = "safe.implant.head.pic.atmosphere";

    public static final String DEAL_TIMES_CARD_REFUND_CUSTOMER_WHITELIST = "deal.timescard.refund.info.customer.whiteList";

    /**
     * 商品结构元数据新版本下界配置
     */
    public static final String META_VERSION_BOUND_MAP = "com.sankuai.dzu.tpbase.dztgdetailweb.metaVersionBoundMap";
    /**
     * 自助保洁新预订开关降级开关
     */
    public static final String SELF_OPERATED_CLEANING_NEW_RESERVE_DEGRADE_SWITCH = "self.operated.cleaning.new.reserve.degrade.switch";

    /**
     * 预订团单销量取团购销量开关
     */
    public static final String PRE_ORDER_DEAL_SALE_DEAL_GROUP_SWITCH = "pre.order.deal.sale.deal.group.switch";

    /**
     * 团详页生活服务安心修模块分类目图片链接配置
     */
    public static final String LE_REPAIR_CARE_CATEGORY_PIC_MAP = "le.repair.care.category.pic.map";

    /**
     * 开启sku属性排序
     */
    public static final String SKU_ATTR_SORT_SWITCH = "sku.attr.sort.switch";

    /**
     * 走快照的实验分组
     */
    public static final String RCF_SNAPSHOT_EXPERIMENT_GROUP = "com.sankuai.dzu.tpbase.dztgdetailweb.snapshot.experiment.group";

    /**
     * 快照实验结果全走快照开关，防止斗斛实验平台异常情况
     */
    public static final String RCF_SNAPSHOT_EXPERIMENT_RESULT_ALL_PASS = "com.sankuai.dzu.tpbase.dztgdetailweb.rcf.snapshot.experiment.result.allpass";

    /**
     * 团详主接口RCF数据处理开关
     */
    public static final String RCF_SNAPSHOT_CACHE_RESULT_DEAL_BASE_PROCESS = "com.sankuai.dzu.tpbase.dztgdetailweb.rcf.snapshot.result.deal.base.process";

    /**
     * 团购次卡先用后付团单二级分类白名单
     */
    public static final String PRODUCT_CREDIT_PAY_CATEGORY = "com.sankuai.dzu.tpbase.dztgdetailweb.product.credit.pay.category";

    /**
     * 团购次卡先用后付 控制开关
     */
    public static final String PRODUCT_CREDIT_PAY_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.product.credit.pay.switch";

    /**
     * 私域直播分享获取微信名称开关
     */
    public static final String WX_NAME_QUERY_SWITCH = "wx.name.query.switch";

    /**
     * 新团详URL diff 忽略字段
     */
    public static final String DETAIL_DIFF_IGNORE_KEYS = "com.sankuai.dzu.tpbase.dztgdetailweb.url.diff.ignore.keys";

    /**
     * 保洁自链接参数控制提单和订详样式控制开关
     */
    public static final String POI_SELF_CLEAN_ORDER_TYPE_SWITCH = "poi.self.clean.order.type.switch";

    /**
     * 闪购进场需求涉及二级类目
     */
    public static final String CPV_CATEGORY_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.cpv.category.config";

    /**
     *
     */
    public static final String DEAL_SPECIAL_CATEGORY_HIT_CONFIG = "deal.special.category.cat.log.config";

    /**
     * 团详样式统计开关
     */
    public static final String DEAL_STYLE_STATISTIC_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.deal.style.statistic.switch";

    /**
     * 团详样式统计 日志开关
     */
    public static final String DEAL_STYLE_STATISTIC_LOG_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.deal.style.statistic.log.switch";


    /**
     * 营销场的渠道
     */
    public static final String PROMOTION_PAGE_SOURCE_LIST = "com.sankuai.dzu.tpbase.dztgdetailweb.promotion.page.source";

    /**
     * 安心补牙保障标签控制开关
     */
    public static final String TOOTH_FILL_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.tooth.fill.switch";

    /**
     * 神券感知强化黑名单城市
     */
    public static final String MAGIC_COUPON_ENHANCEMENT_CITY_BLACK_LIST = "magic.coupon.enhancement.city.black.list";

    /**
     * 留资类目
     */
    public static final String LEADS_DEAL_CATEGORY_IDS = "leads.deal.category.ids";

    /**
     * 国家补贴团单三级类目
     */
    public static final String COUNTRY_SUBSIDY_SERVICE_TYPE_IDS = "country.subsidy.service.type.ids";
    /**
     * 神券感知强化屏蔽的页面来源
     */
    public static final String MAGIC_COUPON_ENHANCEMENT_HIDE_PAGE_SOURCE = "magic.coupon.enhancement.hide.page.source";

    /**
     * 国家补贴生效的客户端类型
     */
    public static final String COUNTRY_SUBSIDY_CLIENT_TYPE = "country.subsidy.client.type";
    /**
     * 国家补贴降级开关
     */
    public static final String COUNTRY_SUBSIDY_DEGRADE_SWITCH = "country.subsidy.degrade.switch";
    /**
     * 中低线会员免单配置
     */
    public static final String MEMBER_FREE_CONFIG = "member.free.config";

    /**
     * nib类目
     */
    public static final String NIB_CATEGORY_IDS = "nib.category.ids";
    /**
     * 交易连续包月类目配置
     */
    public static final String MONTHLY_SUBSCRIPTION_CATEGORY_IDS = "monthly.subscription.category.ids";

    /**
     * 次卡优化表达切换开关
     */
    public static final String TIMES_CARD_SINGLE_PRICE = "com.sankuai.dzu.tpbase.dztgdetailweb.timescard.single.price.switch";
    /**
     * 自助保洁排除的页面来源
     */
    public static final String CLEANING_SELF_OPERATION_EXCLUDE_PAGE_SOURCE = "cleaning.self.operation.exclude.page.source";

    /**
     * 团详样式日志配置
     */
    public static final String LOG_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.deal.style.log.config";

    public static final String DINNER_DEAL_GRAY_CONFIG = "dinner.deal.gray.config";

    public static final String SEARCH_NAV_BAR_FIX_TEXT = "search.nav.bar.fix.text";

    /**
     * 支持微信openid登录态 开关
     */
    public static final String WX_OPENID_SWITCH = "wx.openid.switch";

    public static final String HIDE_STYLE_PICTURES_BAR_CATEGORY_IDS = "hide.style.pictures.bar.category.ids";

    /**
     * 跑路赔
     */
    public static final String COMPENSATION_FOR_RUNNING_AWAY_CONFIG = "com.sankuai.dzshoppingguide.detail.commonmodule.compensation.for.running.away.config";

    public static final String DEAL_DETAIL_MODULE_KEY_CONFIG = "deal.detail.module.key.for.cpv";

    public static final String SHOW_DISCLAIMER_CONFIG = "show.disclaimer.config";

    public static final String DEAL_STYLE_KEY_FOR_CPV = "deal.style.key.for.cpv";

    /**
     * categoryId 和 id的映射
     */
    public static final String CATEGORY_TO_DT = "category.to.dt";

    public static final String MIGRATE_CATEGORY = "page.migrate.categoryId";

    /**
     * 删除某些三级类目下的限制条展示
     */
    public static final String LIMIT_INFO_IGNORE_THIRD_CATEGORY_CONFIG = "limit.info.ignore.third.category.config";

    /**
     * 代金券抽样打点
     */
    public static final String VOUCHER_LOG_SAMPLE_RATE = "voucher.log.sample.rate";


    /**
     * 3c认证类目
     */
    public static final String SERVICE_TYPE_LIST_3C = "3c.servicetype.whitelist";

    /**
     * 3c认证图标
     */
    public static final String ICON_3C = "3c.icon";

    public static final String CLEANING_SELF_PRE_ORDER_SWITCH = "cleaning.self.pre.order.switch";

    public static final String CLEANING_SELF_PRE_ORDER_WHITE_LIST = "cleaning.self.pre.order.white.list";
    /**
     * 次卡样式配置
     */
    public static final String TIMES_CARD_PRICE_STYLE_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.times.card.price.style.config";
    /**
     * 多买多省白名单渠道
     */
    public static final String BUY_MORE_TRAFFIC_FLAG_WHITE_LIST = "buy.more.source.white.list";
    public static final String JUDGE_UNAVAILABLE_DATE_CATEGORY_IDS = "judge.unavailable.date.category.ids";
    public static final String INSURANCE_INFO_DETAIL_CONFIG = "insurance.info.detail.config";

    /**
     * 团详迁移打点配置
     */
    public static final String DETAIL_MIGRATION_BATCH_CATEGORY_LIST = "detail.migration.batch.category.list";

    public static final String NEW_DEAL_SALE_TAG_SWITCH = "new.deal.sale.tag.switch";
}
