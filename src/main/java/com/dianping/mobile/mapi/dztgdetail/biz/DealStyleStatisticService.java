package com.dianping.mobile.mapi.dztgdetail.biz;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule;
import com.dianping.mobile.mapi.dztgdetail.entity.DealStyleConfig;
import com.dianping.mobile.mapi.dztgdetail.util.DealVersionUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.VersionUtils;
import com.google.common.collect.Maps;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;


/**
 * <AUTHOR>
 * @create 2025/4/15 15:42
 */
@Component
@Slf4j
public class DealStyleStatisticService {

    private static String MT_DealDetail = "dealdetail_gc_packagedetail";
    private static String DP_DealDetail = "tuandeal_gc_packagedetail";

    // 代金券属性
    private static String SYS_DEAL_UNIVERSAL_TYPE = "sys_deal_universal_type";
    private static String SYS_DEAL_UNIVERSAL_TYPE_VALUE = "2";

    // 环境
    private static String RC_ENV = "gray-release-plat-rc";
    private static String ST_ENV = "gray-release-nib-st";

    // cpv改造后的样式
    private static String NEW_CPV_MODULE = "new_cpv_module";

    public void dealStyleStatistic(DealBaseReq request, EnvCtx envCtx, DealGroupPBO result, boolean withServiceType, boolean logSwitch){
        try{
            dealDetailStatistic(request, envCtx, result, withServiceType, logSwitch);
            ModuleConfigsModule moduleConfigsModule = result.getModuleConfigsModule();
            String dealStyle = Objects.nonNull(moduleConfigsModule) ? moduleConfigsModule.getGeneralInfo() : "";
            Cat.logEvent("DealStyleStatistic", dealStyle);
        }catch (Exception e){
            log.error("DealStyleStatisticService.dealStyleStatistic mtid:{} dpid:{} client:{} appversion:{} mrnversion:{} error:{}",
                    result.getMtId(), result.getDpId(),envCtx.getDztgClientTypeEnum().name(), envCtx.getVersion(), request.getMrnversion(), e);
        }
    }

    public void dealDetailStatistic(DealBaseReq request, EnvCtx envCtx, DealGroupPBO result, boolean withServiceType, boolean logSwitch){
        ModuleConfigsModule moduleConfigsModule = result.getModuleConfigsModule();
        if (Objects.isNull(moduleConfigsModule)) {
            return;
        }
        // 团详样式：不同行业不同样式 key
        String dealStyle = moduleConfigsModule.getKey();
        // 卡片样式
        String cardStyle =  moduleConfigsModule.getGeneralInfo();
        String extraInfo = moduleConfigsModule.getExtraInfo();

        String appVersion = envCtx.getVersion();
        String mrnVersion = request.getMrnversion();
        String client = envCtx.getDztgClientTypeEnum().name();

        int categoryId = result.getCategoryId();
        Long serviceTypeId = result.getServiceTypeId();

        DealStyleConfig config = LionConfigUtils.getDealStyleConfig();
        boolean hitEventLog = hitEventLog(envCtx.isMt(), client, appVersion, mrnVersion, dealStyle, cardStyle, extraInfo, config);

        if (StringUtils.isBlank(dealStyle) && logSwitch) {
            log.warn("dealStyle is empty mtid:{} dpid:{} client:{} appversion:{} mrnversion:{} dealstyle:{} ",
                    result.getMtId(), result.getDpId(), envCtx.getDztgClientTypeEnum().name(), envCtx.getVersion(), request.getMrnversion(), dealStyle);
        }

        if(hitEventLog) {
            String eventName = StringUtils.isNotBlank(config.getEventName()) ? config.getEventName() : "DealCardStyleStatistic";
            Cat.logEvent(eventName, String.format("%s.%s.%s.%s", client, dealStyle, cardStyle, extraInfo));
            log.warn("dealstyle hitEventLog mtid:{}, dpid:{}, categoryid:{}, serviceTypeid:{} client:{}  dealstyle:{}  cardstyle:{} extrainfo:{} appversion:{} mrnversion:{}",
                    result.getMtId(), result.getDpId(), result.getCategoryId(), result.getServiceTypeId(), client, dealStyle, cardStyle, extraInfo, envCtx.getVersion(), request.getMrnversion());

            if (withServiceType){
                Cat.logEvent("DealStyleCategory", String.format("%s.%s.%s", dealStyle, categoryId, serviceTypeId));
            }else {
                Cat.logEvent("DealStyleCategory", String.format("%s.%s", dealStyle, categoryId));
            }

        }
    }

    public boolean hitEventLog(boolean isMt, String client, String appVersion, String mrnVersion, String dealStyle, String cardStyle, String extraInfo, DealStyleConfig config) {

        if (Objects.isNull(config)) {
            return false;
        }

        if (config.isLogAll()){
            return true;
        }
        
        String appVersionConfig = isMt ? config.getMtAppVersion() : config.getDpAppVersion();
        String mrnVersionConfig = isMt ? config.getMtMrnVersion() : config.getDpMrnVersion();
        
        // 版本判断
        boolean appVersionValid = StringUtils.isBlank(appVersionConfig) || VersionUtils.isGreatEqualThan(appVersion, appVersionConfig);
        boolean mrnVersionValid = StringUtils.isBlank(mrnVersionConfig) || VersionUtils.isGreatEqualThan(mrnVersion, mrnVersionConfig);
        // 排除项判断
        boolean dealStyleValid = CollectionUtils.isEmpty(config.getDealStyleExclude()) || !config.getDealStyleExclude().contains(dealStyle);
        boolean cardStyleValid = CollectionUtils.isEmpty(config.getCardStyleExclude()) || !config.getCardStyleExclude().contains(cardStyle);
        boolean extraInfoValid = CollectionUtils.isEmpty(config.getExtraInfoExclude()) || !config.getExtraInfoExclude().contains(extraInfo);
        boolean clientValid = CollectionUtils.isEmpty(config.getClientExclude()) || !config.getClientExclude().contains(client);

        return appVersionValid && mrnVersionValid && dealStyleValid && cardStyleValid && extraInfoValid && clientValid;
    }

    // 迁移相关打点
    public void logMigration(DealCtx dealCtx, DealGroupPBO result) {
        try{
            // 判断是否为CPV改造后的样式
            boolean isNewCpv = !DealVersionUtils.isOldMetaVersionForLog(dealCtx.getDealGroupDTO(), LionConstants.COMMON_MODULE_CALL_VERSION_CONFIG);
            logStyleAndDetailConfig(dealCtx, result, isNewCpv);
            logVoucher(dealCtx, result, isNewCpv);
        } catch (Exception e) {
            log.error("DealStyleStatisticService.logMigration mtId={}", result.getMtId(), e);
        }
    }

    private void logStyleAndDetailConfig(DealCtx ctx, DealGroupPBO result, boolean isNewCpv) {
        Map<String, Set<Integer>> batchCategoryIdsMap = JSONObject.parseObject(LionConfigUtils.getMigrationConfig(), new TypeReference<Map<String, Set<Integer>>>() {});
        EnvCtx envCtx = ctx.getEnvCtx();
        DealBaseReq request = ctx.getDealBaseReq();
        // 迁移批次名称
        String batchName = getBatchNameByCategoryId(batchCategoryIdsMap, result.getCategoryId());
        if (!envCtx.judgeMainApp() || isNotDefaultEnv() || StringUtils.isBlank(batchName)) {
            return;
        }

        int categoryId = result.getCategoryId();
        long serviceTypeId = Optional.ofNullable(result.getServiceTypeId()).orElse(0L);
        int dealGroupId = request.getDealgroupid();
        String platform = envCtx.isDp() ? "dp" : "mt";

        // 上报页面Style配置
        ModuleConfigsModule moduleConfigsModule = result.getModuleConfigsModule();
        String dealStyle = Optional.ofNullable(moduleConfigsModule).map(ModuleConfigsModule::getKey).orElse("null");
        String extraInfo = Optional.ofNullable(moduleConfigsModule).map(ModuleConfigsModule::getExtraInfo).orElse("");
        String styleConfig = StringUtils.isBlank(extraInfo) ? dealStyle : String.format("%s_%s", dealStyle, extraInfo);

        Map<String, String> tags = Maps.newConcurrentMap();
        tags.put("platform", platform);
        tags.put("categoryId", String.valueOf(categoryId));
        tags.put("style", styleConfig);
        Cat.logMetricForCount("StyleConfig" + batchName, 1, tags);

        // 上报结构化详情配置
        String detailConfig = getModuleDetailConfig(envCtx, result, isNewCpv);
        Map<String, String> detailTags = Maps.newConcurrentMap();
        detailTags.put("categoryId", String.valueOf(categoryId));
        detailTags.put("detail", detailConfig);
        Cat.logMetricForCount("DetailConfig" + batchName, 1, detailTags);

        // 打日志
        if (needLog(categoryId) && !isNewCpv) {
            log.info("styleConfig {} log, categoryId={}, styleConfig={}, serviceTypeId={}, pageSource={}, platform={}, productId={}, " +
                            "clientType={}, appVersion={}", batchName, categoryId, styleConfig, serviceTypeId,
                    request.getPageSource(), platform, dealGroupId, envCtx.getDztgClientTypeEnum(), envCtx.getVersion());
            log.info("detailConfig {} log, categoryId={}, detailConfig={}, serviceTypeId={}, pageSource={}, platform={}, productId={}, " +
                            "clientType={}, appVersion={}", batchName, categoryId, detailConfig, serviceTypeId,
                    request.getPageSource(), platform, dealGroupId, envCtx.getDztgClientTypeEnum(), envCtx.getVersion());
        }
    }

    private String getBatchNameByCategoryId(Map<String, Set<Integer>> batchCategoryIdsMap, int categoryId) {
        if (MapUtils.isEmpty(batchCategoryIdsMap)) {
            return StringUtils.EMPTY;
        }
        return batchCategoryIdsMap.entrySet().stream()
                .filter(entry -> entry.getValue().contains(categoryId))
                .findFirst()
                .map(Map.Entry::getKey)
                .orElse(StringUtils.EMPTY);
    }

    // 查询团购详情是什么配置
    public static String getModuleDetailConfig(EnvCtx envCtx, DealGroupPBO result, boolean isNewCpv) {
        if (isNewCpv) {
            return NEW_CPV_MODULE;
        }
        String structKey = envCtx.isDp() ? DP_DealDetail : MT_DealDetail;
        String detailConfig = "unknown";
        if (result.getModuleConfigsModule() != null && CollectionUtils.isNotEmpty(result.getModuleConfigsModule().getModuleConfigs())) {
            for (ModuleConfigDo config : result.getModuleConfigsModule().getModuleConfigs()) {
                if (structKey.equals(config.getKey())) {
                    detailConfig = config.getValue();
                    break;
                }
            }
        }
        return detailConfig;
    }

    private static boolean needLog(int categoryId) {
        List<Integer> categoryIds = Lion.getList(MdpContextUtils.getAppKey(), "style.detail.log.category.ids", Integer.class);
        if (CollectionUtils.isEmpty(categoryIds) || !categoryIds.contains(categoryId)) {
            return false;
        }
        return needLogByLion("style.detail.log.ratio");
    }


    // 代金券打点
    private void logVoucher(DealCtx dealCtx, DealGroupPBO result, boolean isNewCpv) {
        try{
            if (!dealCtx.getEnvCtx().judgeMainApp()) {
                return;
            }
            int categoryId = Optional.ofNullable(dealCtx.getDealGroupDTO())
                    .map(DealGroupDTO::getCategory)
                    .map(DealGroupCategoryDTO::getCategoryId)
                    .map(Long::intValue)
                    .orElse(0);
            AttrDTO attrDTO = Optional.ofNullable(dealCtx.getDealGroupDTO())
                    .map(DealGroupDTO::getAttrs).orElse(Collections.emptyList())
                    .stream().filter(attr -> SYS_DEAL_UNIVERSAL_TYPE.equals(attr.getName()))
                    .findFirst().orElse(null);
            boolean isVoucher = Optional.ofNullable(attrDTO)
                    .map(AttrDTO::getValue)
                    .filter(CollectionUtils::isNotEmpty)
                    .map(value -> SYS_DEAL_UNIVERSAL_TYPE_VALUE.equals(value.get(0))).orElse(false);
            // 代金券属性
            if (!isVoucher) {
                return;
            }
            String moduleConfigType = getModuleDetailConfig(dealCtx.getEnvCtx(), result, isNewCpv);
            Map<String, String> tags = Maps.newHashMap();
            tags.put("categoryId", String.valueOf(categoryId));
            tags.put("detail", moduleConfigType);
            Cat.logMetricForCount("voucher", tags);

            // 抽样打日志
            if (needLogByLion(LionConstants.VOUCHER_LOG_SAMPLE_RATE)) {
                log.info("voucher log, mtDealGroupId={}, categoryId={}, detail={}",
                        dealCtx.getMtId(), categoryId, moduleConfigType);
            }
        } catch (Exception e) {
            log.error("logMetricOfVoucher error", e);
        }

    }

    // 判断是否为生产环境，若为rc和st环境，表示不是生产环境
    private static boolean isNotDefaultEnv() {
        String cell = Environment.getCell();
        return RC_ENV.equals(cell) || ST_ENV.equals(cell);
    }

    private static boolean needLogByLion(String lionKey) {
        Integer n = Lion.getInt(MdpContextUtils.getAppKey(), lionKey, 5);
        return Math.random() * 1000 < n;
    }
}
