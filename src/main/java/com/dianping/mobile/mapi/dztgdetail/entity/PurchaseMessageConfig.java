package com.dianping.mobile.mapi.dztgdetail.entity;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2024/4/18 11:05
 */
@Data
public class PurchaseMessageConfig {
    @MobileDo.MobileField(key = 0x8f0c)
    @FieldDoc(description = "弹窗类型")
    private String type;

    @MobileDo.MobileField(key = 0x24cc)
    @FieldDoc(description = "弹窗标题")
    private String title;

    @MobileDo.MobileField(key = 0xcce)
    @FieldDoc(description = "弹窗内容")
    private String content;

    @MobileDo.MobileField(key = 0x509a)
    @FieldDoc(description = "弹窗确认按钮文案")
    private String confirmBtnTxt;
}
