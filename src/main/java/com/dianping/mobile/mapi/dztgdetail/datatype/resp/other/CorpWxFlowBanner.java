package com.dianping.mobile.mapi.dztgdetail.datatype.resp.other;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

@MobileDo(id = 0x361a)
public class CorpWxFlowBanner implements Serializable {
    /**
     * 副标题文案
     */
    @MobileDo.MobileField(key = 0xa4d0)
    private String subTitleText;

    /**
     * 展示文案
     */
    @MobileDo.MobileField(key = 0x2607)
    private String showText;

    /**
     * 跳转链接
     */
    @MobileDo.MobileField(key = 0x774e)
    private String jumpUrl;

    /**
     * 按钮文案
     */
    @MobileDo.MobileField(key = 0xe221)
    private String buttonText;

    /**
     * 图片链接
     */
    @MobileDo.MobileField(key = 0x7291)
    private String picUrl;

    /**
     * 是否展示资源信息
     */
    @MobileDo.MobileField(key = 0x23f7)
    private boolean isShowResource;

    public String getSubTitleText() {
        return subTitleText;
    }

    public void setSubTitleText(String subTitleText) {
        this.subTitleText = subTitleText;
    }

    public String getShowText() {
        return showText;
    }

    public void setShowText(String showText) {
        this.showText = showText;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    public String getButtonText() {
        return buttonText;
    }

    public void setButtonText(String buttonText) {
        this.buttonText = buttonText;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public boolean getIsShowResource() {
        return isShowResource;
    }

    public void setIsShowResource(boolean isShowResource) {
        this.isShowResource = isShowResource;
    }
}