package com.dianping.mobile.mapi.dztgdetail.action.app;


import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.SpecificModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailSpecificModuleVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.facade.DealDetailFacade;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.meituan.servicecatalog.api.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

@InterfaceDoc(displayName = "团详特定模块APP查询接口",
        type = "restful",
        description = "查询团详特定展示模块",
        scenarios = "该接口仅适用于双平台APP站点的团购详情页",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "wangziyun04"
)
@Controller("general/platform/dztgdetail/dealdetailspecificmodule.bin")
@Action(url = "dealdetailspecificmodule.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class DealDetailSpecificModuleAction extends AbsAction<SpecificModuleReq> {

    @Autowired
    private DealDetailFacade dealDetailFacade;

    @Override
    protected IMobileResponse validate(SpecificModuleReq request, IMobileContext iMobileContext) {
        IdUpgradeUtils.processProductIdForSpecificModuleReq(request, "dealdetailspecificmodule.bin");
        if (request == null || request.getDealgroupid() == null || request.getDealgroupid() <= 0) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "dealdetailspecificmodule.bin",
            displayName = "查询团详特定展示模块接口",
            description = "查询团详特定展示模块",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "dealdetailspecificmodule.bin请求参数",
                            type = DealBaseReq.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "result", description = "团购picasso数据", type = DealGroupPBO.class)},
            restExampleUrl = "http://mapi.51ping.com/general/platform/dztgdetail/dealdetailspecificmodule.bin?" +
                    "dealgroupid=200139713",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    ),
            }
    )
    @Override
    protected IMobileResponse execute(SpecificModuleReq request, IMobileContext iMobileContext) {
        EnvCtx envCtx = initEnvCtx(iMobileContext);
        try {
            DealDetailSpecificModuleVO result = dealDetailFacade.getDealDetailSpecificModule(request, envCtx);
            return new CommonMobileResponse(result);
        } catch (Exception e) {
            logger.error("dealdetailspecificmodule.bin error", e);
        }

        return Resps.SYSTEM_ERROR;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
