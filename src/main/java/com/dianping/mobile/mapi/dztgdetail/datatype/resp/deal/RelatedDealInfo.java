package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.sankuai.sig.botdefender.adapter.annotation.EncryptedField;
import lombok.Data;

import java.io.Serializable;

import static com.sankuai.sig.botdefender.core.enums.AssetIdType.SHOP_UUID;

@MobileDo(id = 0x4f90)
@Data
public class RelatedDealInfo implements Serializable {
    /**
     * 团单id
     */
    @MobileDo.MobileField(key = 0x9a1c)
    private String dealGroupId;

    /**
     * 门店id
     */
    @MobileDo.MobileField(key = 0x349b)
    @EncryptedField(targetFieldName = "shopIdEncrypt")
    private String shopId;
    @MobileDo.MobileField(key = 0x8143)
    private String shopIdEncrypt;

    /**
     * 门店uuid
     */
    @MobileDo.MobileField(key = 0x3cba)
    @EncryptedField(targetFieldName = "shopUuidEncrypt", assetIdType = SHOP_UUID)
    private String shopUuid;
    @MobileDo.MobileField(key = 0xd071)
    private String shopUuidEncrypt;

    /**
     *
     */
    @MobileDo.MobileField(key = 0x7b2f)
    private String moduleKey;

    /**
     * tab名称
     */
    @MobileDo.MobileField(key = 0x78a2)
    private String tabName;

}
