package com.dianping.mobile.mapi.dztgdetail.entity;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DealStyleConfig {
        private boolean logAll;
        private String eventName;
        private Map<String, String> version;
        private String mtAppVersion;
        private String mtMrnVersion;
        private String dpAppVersion;
        private String dpMrnVersion;
        private List<String> dealStyleExclude;
        private List<String> cardStyleExclude;
        private List<String> extraInfoExclude;
        private List<String> clientExclude;
    }