package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.flash;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsFlashDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.service.cellarservice.DealModuleDetailCacheBizService;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FlashDealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.DealModuleDetailVO;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @create 2024/10/17 17:08
 */
public class DealModuleDetailFlashProcessor extends AbsFlashDealProcessor {
    @Resource
    DealModuleDetailCacheBizService dealModuleDetailCacheBizService;

    @Override
    public void prepare(FlashDealCtx ctx) {
        // 从缓存获取 定制样式团购详情
        String key = dealModuleDetailCacheBizService.buildKey(ctx.getDealflashReq());
        Future future = dealModuleDetailCacheBizService.getCacheValueFuture(key);
        ctx.getFutureCtx().setDealModuleDetailFuture(future);
    }

    @Override
    public void process(FlashDealCtx ctx) {
        try {
            Future future = ctx.getFutureCtx().getDealModuleDetailFuture();
            if ( Objects.nonNull(future)) {
                DealModuleDetailVO dealModuleDetailVO = (DealModuleDetailVO) dealModuleDetailCacheBizService.getCacheValueResult(future);
                ctx.setDealModuleDetail(dealModuleDetailVO);
            }
        } catch (Exception e) {
            logger.error("DealModuleDetailFlashProcessor.process error:", e);
        }
    }
}
