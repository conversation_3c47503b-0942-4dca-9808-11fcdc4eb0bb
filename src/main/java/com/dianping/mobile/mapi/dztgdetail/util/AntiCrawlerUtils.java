package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.lion.client.Lion;
import com.dianping.lion.common.util.JsonUtils;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ShopPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule;
import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.hotel.login.authenticate.api.model.HttpResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-11-21
 * @desc 反爬方法
 */
public class AntiCrawlerUtils {
    /**
     * 是否隐藏关键信息
     * @param ctx 上下文环境
     * @return true/false
     */
    public static boolean hide(IMobileContext ctx) {
        return Lion.getBoolean(LionConstants.APP_KEY, LionConstants.ANTI_CRAWLER_HIDE_KEY_INFO_SWITCH, false)
                && ctx.getUserId() <= 0;
    }

    /**
     * 是否隐藏关键信息，通过userId 和 微信小程序openId的登录态来判断
     * @param ctx 上下文环境
     * @return true/false
     */
    public static boolean hideWithOpenId(IMobileContext ctx) {
        boolean antiSwitch = Lion.getBoolean(LionConstants.APP_KEY, LionConstants.ANTI_CRAWLER_HIDE_KEY_INFO_SWITCH, false);
        if (!antiSwitch) {
            return false;
        }
        boolean notLogin = ctx.getUserId() <= 0;
        // 用户未登录时，微信小程序的openid有效也被认作为登录
        if (notLogin && KmsUtils.checkOpenid(ctx)) {
            notLogin = false;
        }
        return notLogin;
    }

    public static void hideKeyInfo(DealGroupPBO result, IMobileContext ctx) {
        if ( Objects.isNull(result)) {
            return;
        }
        if (!hideWithOpenId(ctx)) {
            return;
        }
        if (Objects.nonNull(result.getBuyBar()) && CollectionUtils.isNotEmpty(result.getBuyBar().getBuyBtns())) {
            result.getBuyBar().getBuyBtns().forEach(btn -> {
                // 隐藏到手价
                btn.setPriceStr(StringUtils.EMPTY);
                // 隐藏市场价
                btn.setMarketPrice(StringUtils.EMPTY);
                btn.setBtnText(StringUtils.EMPTY);
                btn.setBtnTag(StringUtils.EMPTY);
                // 隐藏底部优惠信息
                btn.setBtnIcons(Lists.newArrayList());
                // 隐藏底部团购价、门市价
                btn.setBtnDesc("");
            });
        }
        // 隐藏团购详情
        if (CollectionUtils.isNotEmpty(result.getStructedDetails())) {
            result.getStructedDetails().forEach(detail -> {
                if (detail.getType() == 1) {
                    detail.setName("<div></div>");
                }
            });
        }
        // 隐藏 originalPrice
        if (result.getShareModule() != null && Objects.nonNull(result.getShareModule().getDp())) {
            result.getShareModule().getDp().setOriginalPrice(0d);
            result.getShareModule().getDp().setPrice(0d);
        }
        if (result.getShareModule() != null && Objects.nonNull(result.getShareModule().getMt())) {
            result.getShareModule().getMt().setPrice(0d);
        }
        // 隐藏 displayPrice
        result.setDisplayPrice(StringUtils.EMPTY);
        result.setDisplayPriceDesc(StringUtils.EMPTY);
        //隐藏订单实付价格
        if(Objects.nonNull(result.getSpecialParamsForPay())) {
            result.getSpecialParamsForPay().setPayFeeCent(0);
        }
        // 隐藏 promoDetailModule
        if (Objects.nonNull(result.getPromoDetailModule())) {
            result.getPromoDetailModule().setDealGroupPrice(StringUtils.EMPTY);
            result.getPromoDetailModule().setPromoPrice(StringUtils.EMPTY);
            result.getPromoDetailModule().setBestPromoDetails(Lists.newArrayList());
            result.getPromoDetailModule().setMarketPricePromo(StringUtils.EMPTY);
            result.getPromoDetailModule().setMarketPrice(StringUtils.EMPTY);
            result.getPromoDetailModule().setFinalPrice(StringUtils.EMPTY);
            result.getPromoDetailModule().setMarketPromoDiscount(StringUtils.EMPTY);
            result.getPromoDetailModule().setPlainMarketPromoDiscount(StringUtils.EMPTY);
        }
        // 隐藏 priceDisplayModuleDo
        result.setPriceDisplayModuleDo(null);
        // 隐藏门店地址
        if (Objects.nonNull(result.getShop()) && StringUtils.isNotBlank(result.getShop().getAddress())) {
            result.getShop().setAddress("登录后查看具体地址");
            result.getShop().setPhoneNos(Lists.newArrayList());
        }
    }

    public static boolean onlyDisplayWhitelistFields() {
        return Lion.getBoolean(MdpContextUtils.getAppKey(), "only.display.whitelist.fields", true);
    }

    public static DealGroupPBO onlyShowSafeFields(DealGroupPBO originResult, IMobileContext ctx) {
        if (Objects.isNull(originResult)) {
            return null;
        }

        if (!hideWithOpenId(ctx)) {
            return originResult;
        }

        DealGroupPBO validResult = new DealGroupPBO();

        // ==================== 配置等必须的非展示信息 ====================
        // ID
        validResult.setDpId(originResult.getDpId());
        validResult.setMtId(originResult.getMtId());
        // 页面模块配置
        ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
        moduleConfigsModule.setKey("default");
        moduleConfigsModule.setGeneralInfo(ctx.isMeituanClient() ? "card_style_v2" : "");
        validResult.setModuleConfigsModule(moduleConfigsModule);

        // ==================== 展示信息 ====================
        // 商品头图
        if (CollectionUtils.isNotEmpty(originResult.getDealContents())) {
            validResult.setDealContents(originResult.getDealContents().stream().filter(Objects::nonNull).map(AntiCrawlerUtils::toContentPBO).collect(Collectors.toList()));
        }
        // 商品标题
        validResult.setTitle(originResult.getTitle());
        // 适用门店
        if (Objects.nonNull(originResult.getShop())) {
            ShopPBO shop = new ShopPBO();
            shop.setShopId(originResult.getShop().getShopId());
            shop.setShopName(originResult.getShop().getShopName());
            shop.setShopUrl(originResult.getShop().getShopUrl());
            validResult.setShop(shop);
        }
        // 底bar跳链
        if (Objects.nonNull(originResult.getBuyBar()) && CollectionUtils.isNotEmpty(originResult.getBuyBar().getBuyBtns())) {
            List<DealBuyBtn> buyBtnList = originResult.getBuyBar().getBuyBtns().stream().filter(Objects::nonNull).map(AntiCrawlerUtils::toDealBuyBtn).collect(Collectors.toList());
            validResult.setBuyBar(new DealBuyBar(originResult.getBuyBar().getBuyType(), buyBtnList));
        }

        return validResult;
    }

    public static ContentPBO toContentPBO(ContentPBO origin) {
        ContentPBO result = new ContentPBO(origin.getType(), origin.getContent());
        result.setScale(origin.getScale());
        return result;
    }

    private static DealBuyBtn toDealBuyBtn(DealBuyBtn origin) {
        DealBuyBtn result = new DealBuyBtn(origin.isBtnEnable(), origin.getBtnTitle());
        result.setRedirectUrl(origin.getRedirectUrl());
        result.setDetailBuyType(origin.getDetailBuyType());
        return result;
    }


    /**
     * 拦截没有授权的请求
     * @param ctx
     * @throws IOException
     */
    public static void antiUnauthenticLogin(IMobileContext ctx) throws IOException {
        if (validUnAuthorization(ctx)){
            HttpServletResponse response = ctx.getResponse();
            HttpResponse httpResponse = buildUnAuthorizedResponse();
            byte[] bytes = JsonUtils.toJson(httpResponse).getBytes();
            response.setStatus(httpResponse.getCode());
            response.setContentLength(bytes.length);
            response.getOutputStream().write(bytes);
            response.addHeader("M-LOGIN-FLAG", "1");
        }

    }

    /**
     * 构建未授权的https响应
     * @return
     */
    public static HttpResponse buildUnAuthorizedResponse() {
        HttpResponse httpResponse = new HttpResponse();
        httpResponse.setCode(HttpStatus.UNAUTHORIZED.value());
        httpResponse.setMessage(HttpStatus.UNAUTHORIZED.getReasonPhrase());
        return httpResponse;
    }


    /**
     * 检测是否为授权登录
     * 没有登陆授权返回 true
     * @param ctx
     * @return
     */
    public static boolean validUnAuthorization(IMobileContext ctx) {
        return Lion.getBoolean(LionConstants.APP_KEY, LionConstants.APOLLO_AUTHENTIC_SWITCH, false)
                && ctx.getUserStatus().getMtUserId() <= 0 && ctx.getUserStatus().getUserId() <= 0 && ctx.getUserId() <= 0;
    }


    /*
    * getmtpoilist.bin接口开关
    * */
    public static boolean getMtPoiListHide(IMobileContext ctx) {
        return Lion.getBoolean(LionConstants.APP_KEY, LionConstants.GETMTPOILIST_HIDE_KEY_INFO_SWITCH, false)
                && ctx.getUserStatus().getMtUserId() <= 0 && ctx.getUserStatus().getUserId() <= 0 && ctx.getUserId() <= 0;
    }

    /*
    * 电话信息脱敏
    * */
    public static String hidePhoneInfo(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.length() < 4) {
            return StringUtils.EMPTY;
        }
        int len = phoneNumber.length();
        int start = (len - 4) / 2;
        int end = start + 4;
        return phoneNumber.substring(0, start) + "****" + phoneNumber.substring(end);
    }



    /*
    *地址信息脱敏
    * */
    public static String hideAddressInfo(String addressName) {
        return StringUtils.isEmpty(addressName) ? StringUtils.EMPTY : addressName.charAt(0) + "******";
    }

    public static boolean isNotLogin(Map<String, Boolean> loginSwitch, EnvCtx envCtx) {
        boolean notLogin = Boolean.TRUE.equals(loginSwitch.get(envCtx.getDztgClientTypeEnum().name())) && !envCtx.isLogin();
        // 用户未登录时，微信小程序的openid有效也被认作为登录
        if (notLogin && KmsUtils.checkOpenid(envCtx)) {
            notLogin = false;
        }
        return notLogin;
    }

}
