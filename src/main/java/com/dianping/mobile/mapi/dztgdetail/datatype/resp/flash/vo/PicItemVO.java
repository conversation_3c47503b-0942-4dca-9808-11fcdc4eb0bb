package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/11/25 4:20 下午
 */
@MobileDo(id = 0x13fc)
public class PicItemVO implements Serializable {
    /**
     * 图片模块图片url
     */
    @MobileDo.MobileField(key = 0xc56e)
    private String url;

    /**
     * 图片模块标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
