package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedMoreDealsReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.more.UnifiedMoreList;
import com.dianping.mobile.mapi.dztgdetail.facade.UnifiedMoreDealsFacade;
import com.dianping.mobile.mapi.dztgdetail.util.AntiCrawlerUtils;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.HttpMethod;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;
import java.util.Objects;

import static com.dianping.mobile.mapi.dztgdetail.util.AntiCrawlerUtils.antiUnauthenticLogin;

/**
 * Created by zuomlin on 2018/12/13.
 */
@InterfaceDoc(displayName = "到综团单详情页统一更多团购模块接口",
        type = "restful",
        description = "到综团单详情页统一更多团购模块接口，包括本店团购以及其他分店模块，主要展示团单价格、优惠、标题、头图",
        scenarios = "到综团单详情页统一更多团购模块接口，包括本店团购以及其他分店模块，主要展示团单价格、优惠、标题、头图",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "yangquan02"
)
@Controller("general/platform/dztgdetail/unifiedmoredeals.bin")
@Action(url = "unifiedmoredeals.bin", httpType = "get")
public class UnifiedMoreDealsAction extends AbsAction<UnifiedMoreDealsReq> {

    @Autowired
    private UnifiedMoreDealsFacade unifiedMoreDealsFacade;

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "unifiedmoredeals.bin",
            displayName = "到综团单详情页统一更多团购模块接口",
            description = "到综团单详情页统一更多团购模块接口，包括本店团购以及其他分店模块，主要展示团单价格、优惠、标题、头图。",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "unifiedmoredeals.bin请求参数",
                            type = UnifiedMoreDealsReq.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "result", description = "用户评论", type = UnifiedMoreList.class)},
            restExampleUrl = "http://mapi.51ping.com/general/platform/dztgdetail/unifiedmoredeals.bin?",
            restExamplePostData = "{}",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    ),
            }
    )
    @Override
    protected IMobileResponse validate(UnifiedMoreDealsReq request, IMobileContext iMobileContext) {
        IdUpgradeUtils.processProductIdForUnifiedMoreDealsReq(request, "unifiedmoredeals.bin");
        if (request == null || request.getDealGroupId() <= 0) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @Override
    @CryptMethod
    protected IMobileResponse execute(UnifiedMoreDealsReq request, IMobileContext iMobileContext) {
        try {
            EnvCtx envCtx = initEnvCtx(iMobileContext);
            // 拦截没有授权的登录
            antiUnauthenticLogin(iMobileContext);
            UnifiedMoreList result = unifiedMoreDealsFacade.queryUnifiedMoreList(request, envCtx, iMobileContext);
            if (result == null || StringUtils.isEmpty(result.getTitle())) {
                return Resps.NoDataResp;
            }
            // 隐藏关键信息
            hideKeyInfo(result, iMobileContext);
            return new CommonMobileResponse(result);
        } catch (Exception e) {
            logger.error("unifiedshopreview.bin failed, params: request={}, context ={}", request, iMobileContext);
        }
        return Resps.SYSTEM_ERROR;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }

    private void hideKeyInfo(UnifiedMoreList result, IMobileContext ctx) {
        if ( Objects.isNull(result)) {
            return;
        }
        if (!AntiCrawlerUtils.hide(ctx)) {
            return;
        }
        if (Objects.nonNull(result.getSameMoreItemList())
                && CollectionUtils.isNotEmpty(result.getSameMoreItemList().getItemList())) {
            result.getSameMoreItemList().getItemList().forEach(item -> {
                item.setItemCampaignTag(StringUtils.EMPTY);
                item.setMarketPrice(0d);
                item.setCurrentPrice(0d);
            });
        }
        if (Objects.nonNull(result.getOtherMoreItemList())
                && CollectionUtils.isNotEmpty(result.getOtherMoreItemList().getItemList())) {
            result.getOtherMoreItemList().getItemList().forEach(item -> {
                item.setItemCampaignTag(StringUtils.EMPTY);
                item.setMarketPrice(0d);
                item.setCurrentPrice(0d);
            });
        }
     }
}
