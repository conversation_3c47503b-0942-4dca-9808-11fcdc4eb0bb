package com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by wa<PERSON><PERSON><PERSON> on 16/4/20.
 */
@MobileDo(id = 0xf4d2)
@Data
public class MtPromotionInfo implements Serializable {

    @MobileField(key = 0x93b)
    private int id;
    @MobileField(key = 0x3ca7)
    private String color;
    @MobileField(key = 0xeaee)
    private String shortTag;
    @MobileField(key = 0xca1)
    private String festival;
    @MobileField(key = 0x956a)
    private int buyStatus;
    @MobileField(key = 0xceec)
    private String infoUrl;
    @MobileField(key = 0x39ef)
    private String logo;
    @MobileField(key = 0x372)
    private int type;
    @MobileField(key = 0x477b)
    private String tag;
    @MobileField(key = 0x4769)
    private String longTitle;

}





