package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealConsumerWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.google.common.collect.Lists;
import com.sankuai.clr.content.core.common.enums.ContentBizTypeEnum;
import com.sankuai.clr.content.core.common.enums.ContentSubjectTypeEnum;
import com.sankuai.clr.content.core.thrift.dto.PlanDTO;
import com.sankuai.clr.content.process.thrift.dto.req.BatchQueryPlanListReqDTO;
import com.sankuai.clr.content.process.thrift.enums.ContentSubjectFieldAttributeEnum;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

public class DealConsumerProcessor extends AbsDealProcessor {

    private static final List<Integer> CATEGORY_IDS = Lists.newArrayList(506);

    private static final String PROJECT_TYPE = "1";

    @Resource
    private DealConsumerWrapper dealConsumerWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        int categoryId = ctx.getCategoryId();
        return CATEGORY_IDS.contains(categoryId);
    }

    @Override
    public void prepare(DealCtx ctx) {
        BatchQueryPlanListReqDTO req = buildReq(ctx);
        Future future = dealConsumerWrapper.queryDealConsumerInfo(req);
        ctx.getFutureCtx().setContentProcessFuture(future);
    }

    private BatchQueryPlanListReqDTO buildReq(DealCtx ctx) {
        List<Map<String, String>> mapList = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        map.put(ContentSubjectFieldAttributeEnum.MT_SHOP.getAttribute(), String.valueOf(ctx.getMtLongShopId()));
        map.put(ContentSubjectFieldAttributeEnum.PROJECT_TYPE.getAttribute(), PROJECT_TYPE);
        map.put(ContentSubjectFieldAttributeEnum.PROJECT_CODE.getAttribute(), String.valueOf(ctx.getDpId()));
        mapList.add(map);
        BatchQueryPlanListReqDTO reqDTO = new BatchQueryPlanListReqDTO();
        reqDTO.setBizType(ContentBizTypeEnum.RESERVATION.getCode());
        reqDTO.setPlanSubjectType(ContentSubjectTypeEnum.MT_SHOP_PROJECT.getCode());
        reqDTO.setPlanSubjectMapList(mapList);
        return reqDTO;
    }

    @Override
    public void process(DealCtx ctx) {
        PlanDTO planDTO = dealConsumerWrapper.getPlanDTO(ctx.getFutureCtx().getContentProcessFuture());
        ctx.setPlanDTO(planDTO);
    }
}
