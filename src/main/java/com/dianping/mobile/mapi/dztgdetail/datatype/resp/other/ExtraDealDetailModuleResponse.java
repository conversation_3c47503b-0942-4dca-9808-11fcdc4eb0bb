package com.dianping.mobile.mapi.dztgdetail.datatype.resp.other;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.sankuai.sig.botdefender.adapter.annotation.EncryptedLinkField;

import java.io.Serializable;
import java.util.List;

public class ExtraDealDetailModuleResponse implements Serializable {
    /**
     * 背景图片
     */
    @MobileDo.MobileField
    private String backgroundPic;
    /**
     * 主标题标签
     */
    @MobileDo.MobileField
    private String icon;
    /**
    *跳转链接
     */
    @MobileDo.MobileField
//    @EncryptedLinkField(queries = {"shopId"})
    private String jumpUrl;

    @MobileDo.MobileField
    private List<ExtraDealDetailModule> attrList;

    /**
    * 主标题
    */
    @MobileDo.MobileField
    private String mainTitle;

    /**
    * 标题信息
    */
    @MobileDo.MobileField
    private String titleInfo;

    public String getBackgroundPic() {
        return backgroundPic;
    }

    public void setBackgroundPic(String backgroundPic) {
        this.backgroundPic = backgroundPic;
    }


    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    public List<ExtraDealDetailModule> getAttrList() {
        return attrList;
    }

    public void setAttrList(List<ExtraDealDetailModule> attrList) {
        this.attrList = attrList;
    }

    public String getMainTitle() {
        return mainTitle;
    }

    public void setMainTitle(String mainTitle) {
        this.mainTitle = mainTitle;
    }

    public String getTitleInfo() {
        return titleInfo;
    }

    public void setTitleInfo(String titleInfo) {
        this.titleInfo = titleInfo;
    }
}