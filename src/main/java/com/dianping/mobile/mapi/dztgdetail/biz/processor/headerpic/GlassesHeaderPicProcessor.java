package com.dianping.mobile.mapi.dztgdetail.biz.processor.headerpic;

import com.dianping.mobile.mapi.dztgdetail.common.enums.ImageScaleEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.ExhibitContentDTO;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * @Author: <EMAIL>
 * @Date: 2024/8/12
 */
@Component("glassesHeaderPicProcessor")
@Slf4j
public class GlassesHeaderPicProcessor extends AbstractHeaderPicProcessor {
    @Override
    public void fillPicScale(DealCtx ctx, List<ContentPBO> result, DealGroupPBO dealGroupPBO) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        // 如果团单不是指定品类，则直接返回
        if (!LionConfigUtils.hasCustomHeaderPic("glasses", ctx.getCategoryId())) {
            return;
        }
        // 判定是否有款式
        boolean showExhibit = matchShowExhibit(ctx);

        // 不满足有款式条件时，下发团购头图信息即可
        if (!showExhibit) {
            result.forEach(contentPBO -> {
                contentPBO.setScale(ImageScaleEnum.SIXTEEN_TO_NINE.getScale());
            });
            dealGroupPBO.setExhibitContents(null);
            return;
        }
        // 有款式
        result.forEach(contentPBO -> {
            contentPBO.setScale(ImageScaleEnum.THREE_TO_FOUR.getScale());
        });
        // 获取头图&款式展示规则配置
        assembleHeaderPicAndExhibitInfo(ctx, dealGroupPBO);
        return;
    }

    @Override
    public boolean matchShowExhibit(DealCtx ctx) {
        boolean showExhibit = false;
        ExhibitContentDTO exhibitContentDTO = ctx.getExhibitContentDTO();
        int recordCount = Optional.ofNullable(exhibitContentDTO).map(ExhibitContentDTO::getRecordCount).orElse(0);
        if (recordCount > 0) {
            showExhibit = true;
        }
        return showExhibit;
    }

}