package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.detailSwitch.SwitchModel;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.car.CarDetailVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.healthExamination.HealthExaminationDealDetailVO;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/30 10:58 上午
 */
@MobileDo(id = 0xa3258902)
public class DealModuleDetailVO implements Serializable {
    /**
     * 爱车团详返回VO
     */
    @MobileDo.MobileField(key = 0x4e60)
    private CarDetailVO carDetailVO;

    /**
     * 体检团详返回VO
     */
    @MobileDo.MobileField(key = 0xb097)
    private HealthExaminationDealDetailVO healthExaminationDealDetailVO;

    /**
     * 弹窗展示样式组件列表
     */
    @MobileDo.MobileField(key = 0xeac)
    private List<PopupTypeVO> popupTypes;

    /**
     * 团购详情组件列表
     */
    @MobileDo.MobileField(key = 0x7934)
    private List<DealDetailModuleVO> moduleList;

    /**
     * 降级标识 true: 走降级
     */
    @MobileDo.MobileField(key = 0x498a)
    private boolean degradation;

    /**
     * 内部字段，兼容团详的开关PMF
     */
    private SwitchModel switchModel;

    /**
     * 场景标识
     */
    @MobileDo.MobileField(key = 0x7395)
    private String sceneCode;

    public CarDetailVO getCarDetailVO() {
        return carDetailVO;
    }

    public void setCarDetailVO(CarDetailVO carDetailVO) {
        this.carDetailVO = carDetailVO;
    }

    public HealthExaminationDealDetailVO getHealthExaminationDealDetailVO() {
        return healthExaminationDealDetailVO;
    }

    public void setHealthExaminationDealDetailVO(
            HealthExaminationDealDetailVO healthExaminationDealDetailVO) {
        this.healthExaminationDealDetailVO = healthExaminationDealDetailVO;
    }

    public List<PopupTypeVO> getPopupTypes() {
        return popupTypes;
    }

    public void setPopupTypes(List<PopupTypeVO> popupTypes) {
        this.popupTypes = popupTypes;
    }

    public List<DealDetailModuleVO> getModuleList() {
        return moduleList;
    }

    public void setModuleList(List<DealDetailModuleVO> moduleList) {
        this.moduleList = moduleList;
    }

    public boolean isDegradation() {
        return degradation;
    }

    public void setDegradation(boolean degradation) {
        this.degradation = degradation;
    }

    public String getSceneCode() {
        return sceneCode;
    }

    public void setSceneCode(String sceneCode) {
        this.sceneCode = sceneCode;
    }

    public SwitchModel getSwitchModel() {
        return switchModel;
    }

    public void setSwitchModel(SwitchModel switchModel) {
        this.switchModel = switchModel;
    }
}
