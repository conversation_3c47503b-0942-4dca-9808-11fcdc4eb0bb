package com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-09-04
 */
@Data
@TypeDoc(description = "款式列表页配置")
@MobileDo(id = 0xa818)
public class ExhibitListConfigVO {
    @FieldDoc(description = "固定宽高比", rule = "如果不为空（如1:1），则前端按网格样式展示款式列表页，否则按瀑布流样式展示")
    @MobileDo.MobileField(key = 0xc8d3)
    private String fixedAspectRatio;
}
