package com.dianping.mobile.mapi.dztgdetail.rcf.repository.pageconfig;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.deallayout.dto.ModuleItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2024/11/22 17:06
 */
@Slf4j
@Component
public class DealDetailPageConfigService {

    public Map<String , ModuleItem> processPageConfig(String configKey, JSONObject pageConfigJsonObject){
        Map<String, ModuleItem> moduleItemMap = new HashMap<>();
        try{
            if (Objects.isNull(pageConfigJsonObject)) {
                return moduleItemMap;
            }
            JSONArray array = (JSONArray) pageConfigJsonObject.get(configKey);
            if (Objects.isNull(array)) {
                return moduleItemMap;
            }
            array.forEach(e->{
                List<ModuleItem> moduleItems = JSON.parseArray(JSON.toJSONString(e), ModuleItem.class);
                flatArrays(moduleItemMap,moduleItems);
            });
        }catch (Exception e){
            log.error("DealDetailPageConfigService.processPageConfig error configKey:{} ", configKey, e);
        }

        return moduleItemMap;
    }

    public void flatArrays(Map<String, ModuleItem> moduleItemMap, List<ModuleItem> moduleItems) {
        if (moduleItemMap == null || moduleItems == null){
            return;
        }
        Map<String, ModuleItem> finalModuleItemMap = moduleItemMap;
        moduleItems.forEach(moduleItem->{
            if (Objects.nonNull(moduleItem)){
                String key = moduleItem.getKey();
                moduleItem.setKey(null);
                finalModuleItemMap.put(key, moduleItem);
            }
        });
    }}
