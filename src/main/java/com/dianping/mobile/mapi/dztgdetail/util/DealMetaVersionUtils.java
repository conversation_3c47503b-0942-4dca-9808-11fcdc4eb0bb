package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.dp.config.LionObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.sankuai.dztheme.deal.dto.DealProductAttrDTO;
import com.sankuai.dztheme.deal.dto.DealProductDTO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

public class DealMetaVersionUtils {

    private static final String META_VERSION = "metaVersion";
    public static final LionObject<Map<Long, List<DealVersionConfig>>> versionBoundMapObj = LionObject
            .create(LionConstants.META_VERSION_BOUND_MAP, new TypeReference<Map<Long, List<DealVersionConfig>>>() {});


    /**
     * 判断数据版本是否低于新版下界
     * 
     * @return true-老版（数据版本<配置版本下界） false-新版（数据版本>=配置版本下界）
     *         https://km.sankuai.com/collabpage/2666808593
     */
    public static boolean isOldMetaVersion(DealProductDTO dealProductDTO, Long categoryId) {
        DealGroupMetaModel dealGroupMetaModel = extractDealGroupMetaModel(dealProductDTO);
        if (dealGroupMetaModel == null || dealGroupMetaModel.getObjectVersion() == null || dealGroupMetaModel.getObjectVersion() <= 0) {
            return true;
        }
        // 查询新版的版本下界配置
        Map<Long, List<DealVersionConfig>> versionBoundMap = versionBoundMapObj != null
                ? versionBoundMapObj.getObject() : Maps.newHashMap();
        if (MapUtils.isEmpty(versionBoundMap) || !versionBoundMap.containsKey(categoryId)) {
            return false;
        }
        List<DealVersionConfig> versionConfigList = versionBoundMap.get(categoryId);
        if (CollectionUtils.isEmpty(versionConfigList)) {
            return false;
        }
        // 判断商品结构id
        if (dealGroupMetaModel.getObjectId() == null || dealGroupMetaModel.getObjectId() <= 0) {
            return true;
        }
        DealVersionConfig versionConfig = versionConfigList.stream()
                .filter(c -> dealGroupMetaModel.getObjectId().equals(c.getObjectId())).findFirst().orElse(null);
        // 如果无版本约束返回新版
        if (versionConfig == null || versionConfig.getObjectId() == null || versionConfig.getObjectId() <= 0) {
            return false;
        }
        // 判断商品版本，当数据版本<新版下界时返回老版(true)，否则返回新版(false)
        if (versionConfig.getMinVersion() == null || versionConfig.getMinVersion() <= 0) {
            return false;
        }
        return dealGroupMetaModel.getObjectVersion().compareTo(versionConfig.getMinVersion()) < 0;
    }

    private static DealGroupMetaModel extractDealGroupMetaModel(DealProductDTO dealProductDTO) {
        if (dealProductDTO == null || CollectionUtils.isEmpty(dealProductDTO.getAttrs())) {
            return null;
        }
        String metaVersionStr = dealProductDTO.getAttrs().stream()
                .filter(dealProductAttrDTO -> META_VERSION.equals(dealProductAttrDTO.getName()))
                .map(DealProductAttrDTO::getValue).findFirst().orElse(StringUtils.EMPTY);
        return JsonCodec.decode(metaVersionStr, DealGroupMetaModel.class);
    }

    @Data
    public static class DealGroupMetaModel {
        /**
         * 商品结构id
         */
        private Long objectId;
        /**
         * 商品结构版本
         */
        private Long objectVersion;
    }

    @Data
    public static class DealVersionConfig {
        /**
         * 商品结构id
         */
        private Long objectId;
        /**
         * 商品结构新版本下界
         */
        private Long minVersion;
    }
}
