package com.dianping.mobile.mapi.dztgdetail.util;

import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.SettableFuture;
import com.meituan.service.mobile.mtthrift.callback.OctoThriftCallback;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import org.jetbrains.annotations.NotNull;

import java.util.concurrent.CompletableFuture;

/**
 * @Author: guangyujie
 * @Date: 2025/2/11 02:00
 */
public class ThriftAsyncUtils {

    @SuppressWarnings("unchecked")
    public static <T> CompletableFuture<T> getThriftFuture() {
        SettableFuture<T> future = ContextStore.getSettableFuture();

        // 创建 CompletableFuture
        CompletableFuture<T> completableFuture = new CompletableFuture<>();

        // 添加回调到 ListenableFuture，beta方法，内部用future.addListener实现
        Futures.addCallback(future, new FutureCallback<T>() {
            @Override
            public void onSuccess(T result) {
                completableFuture.complete(result); // 成功时完成
            }

            @Override
            public void onFailure(@NotNull Throwable t) {
                completableFuture.completeExceptionally(t); // 失败时传递异常
            }
        },
                // 回调会在 SettableFuture 完成的线程中直接执行（通常是异步线程），无需额外线程池，避免资源浪费。
                MoreExecutors.directExecutor());

        return completableFuture;
    }

    public static <T> CompletableFuture<T> getThriftFuture(OctoThriftCallback callback) {
        SettableFuture<T> future = callback.getSettableFuture();

        // 创建 CompletableFuture
        CompletableFuture<T> completableFuture = new CompletableFuture<>();

        // 添加回调到 ListenableFuture，beta方法，内部用future.addListener实现
        Futures.addCallback(future, new FutureCallback<T>() {
            @Override
            public void onSuccess(T result) {
                completableFuture.complete(result); // 成功时完成
            }

            @Override
            public void onFailure(@NotNull Throwable t) {
                completableFuture.completeExceptionally(t); // 失败时传递异常
            }
        },
                // 回调会在 SettableFuture 完成的线程中直接执行（通常是异步线程），无需额外线程池，避免资源浪费。
                MoreExecutors.directExecutor());

        return completableFuture;
    }

}
