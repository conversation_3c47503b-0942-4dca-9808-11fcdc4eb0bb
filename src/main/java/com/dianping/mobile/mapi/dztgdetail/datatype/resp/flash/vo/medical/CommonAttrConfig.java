package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.medical;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.CommonAttrsVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CommonAttrConfig implements Serializable {
    private List<CommonAttrsVO> commonAttrs;
    private String value;
    private String name;
    private String pic;

    private Integer validateType;
    private String validateKey;
    private String validateValue;

    private Boolean addCommonAttrsToLast;
}
