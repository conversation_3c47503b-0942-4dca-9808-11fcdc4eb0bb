package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@MobileDo(id = 0x53d0)
@TypeDoc(description = "氛围信息模型")
public class AtmosphereInfoDetailVO implements Serializable {
    @FieldDoc(description = "氛围条右侧副标题颜色")
    @MobileField(key = 0x2a61)
    private String subTitleColor;

    @FieldDoc(description = "氛围条右侧副标题")
    @MobileField(key = 0xd894)
    private String subTitle;

    @FieldDoc(description = "活动曝光id")
    @MobileField(key = 0xe749)
    private String activityExposureId;

    @FieldDoc(description = "氛围条右侧主标题高度")
    @MobileField(key = 0xea00)
    private String mainTitleHeight;

    @FieldDoc(description = "氛围条右侧主标题宽度")
    @MobileField(key = 0x1939)
    private String mainTitleWidth;


    @FieldDoc(description = "展示新氛围条")
    @MobileField(key = 0xb13f)
    private boolean showNewAtmosphereBar;

    @FieldDoc(description = "倒计时时间戳，13位")
    @MobileField(key = 0xddb1)
    private Long countDownTs;

    @FieldDoc(description = "氛围条文案，如'仅剩'")
    @MobileField(key = 0x451b)
    private String countDownDesc;

    private String countDownColor;

    private String countDownBackgroundColor;

    @FieldDoc(description = "氛围背景图")
    @MobileField(key = 0x3dc6)
    private String backgroundImage;

    @FieldDoc(description = "点击跳转链接")
    @MobileField(key = 0xcc0)
    private String clickUrl;

    @FieldDoc(description = "氛围条类型")
    @MobileField(key = 0x8f0c)
    private int type;

    @FieldDoc(description = "活动id")
    @MobileField(key = 0xe91)
    private String activityId;
}