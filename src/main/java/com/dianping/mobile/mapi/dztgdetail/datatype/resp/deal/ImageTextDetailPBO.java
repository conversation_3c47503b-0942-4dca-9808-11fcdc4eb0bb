package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;
import java.util.List;

@TypeDoc(description = "图文详情数据类型")
@MobileDo(id = 0x36e4)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ImageTextDetailPBO implements Serializable {

    @FieldDoc(description = "内容数组")
    @MobileField(key = 0x8535)
    private List<ContentDetailPBO> contents;

    @FieldDoc(description = "ab实验结果集")
    @MobileField(key = 0xbae3)
    private List<ModuleAbConfig> moduleAbConfigs;

    /**
     * 图文详情使用卡片类型
     */
    @FieldDoc(description = "图文详情使用卡片类型")
    @MobileField(key = 0x8eae)
    private boolean useCardStyle;

    public List<ContentDetailPBO> getContents() {
        return contents;
    }

    public void setContents(List<ContentDetailPBO> contents) {
        this.contents = contents;
    }

    public List<ModuleAbConfig> getmoduleAbConfigs() {
        return moduleAbConfigs;
    }

    public void setmoduleAbConfigs(List<ModuleAbConfig> moduleAbConfigs) {
        this.moduleAbConfigs = moduleAbConfigs;
    }

    public boolean isUseCardStyle() {
        return useCardStyle;
    }

    public void setUseCardStyle(boolean useCardStyle) {
        this.useCardStyle = useCardStyle;
    }
}
