package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.json.facade.JsonFacade;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.DealCategoryFactory;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryParam;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.FreeDealEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleExtraDTO;
import com.dianping.mobile.mapi.dztgdetail.entity.MrnVersionConfig;
import com.dianping.mobile.mapi.dztgdetail.helper.AppCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.DealVersionUtils;
import com.dianping.mobile.mapi.dztgdetail.util.FreeDealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.ModuleUtils;
import com.dianping.mobile.mapi.dztgdetail.util.SwitchUtils;
import com.dianping.mobile.mapi.dztgdetail.util.VersionUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.nibscp.common.api.enums.TradeTypeEnum;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.PHOTO_NEW_DEAL_DETAIL_CATEGORY;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.PHOTO_NEW_DEAL_DETAIL_DOUHU_SWITCH;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.PhotoDealDetailConstants.DOUHU_EXPERIMENTAL_GROUP;

/**
 * 功能描述: 团详页Tab栏Processor
 *
 * <AUTHOR> lintaolei <p>
 * @version 2024/6/18
 * @since mapi-dztgdetail-web
 */
@Slf4j
public class UnifiedModuleProcessor extends AbsDealProcessor {

    private static final String REPORT_TYPE_KEY = "physical_examination_report_type";

    private static final String THIRD_PARTY_VERIFY_KEY = "product_third_party_verify";

    public static final String ATTR_SERVICE_TYPE_LEAF_ID = "service_type_leaf_id";


    @Autowired
    private DouHuBiz douHuBiz;

    @Resource
    private DouHuService douHuService;

    @Resource
    private DealCategoryFactory dealCategoryFactory;

    @Override
    public boolean isEnable(DealCtx ctx) {
        //灰度开关
        return SwitchUtils.isUnifiedModuleEnable();
    }

    @Override
    public void prepare(DealCtx ctx) {

    }

    @Override
    public void process(DealCtx ctx) {
        ModuleExtraDTO moduleExtraDTO = queryUnifiedModuleExtraDTO(ctx);
        ctx.setModuleExtraDTO(moduleExtraDTO);
    }

    public ModuleExtraDTO queryUnifiedModuleExtraDTO(DealCtx ctx) {
        long publishCategoryId = ctx.getCategoryId();
        String serviceType = Optional.ofNullable(ctx)
                .map(DealCtx::getDealGroupDTO).map(DealGroupDTO::getCategory).map(DealGroupCategoryDTO::getServiceType)
                .orElse("");
        boolean isNewPhysicalExerciseModule = false;
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        if (401L == publishCategoryId && dealGroupDTO != null) {
            String reportType = AttributeUtils.getFirstValueV2(dealGroupDTO.getAttrs(), REPORT_TYPE_KEY);
            boolean isThirdPartyVerify = parseBoolean(AttributeUtils.getFirstValueV2(dealGroupDTO.getAttrs(), THIRD_PARTY_VERIFY_KEY));
            int thirdPartyId = getThirdPartyId(dealGroupDTO);
            isNewPhysicalExerciseModule = isNewPhysicalExerciseModuleV2(ctx, ctx.getMrnVersion(),
                    (int) publishCategoryId, reportType, isThirdPartyVerify, thirdPartyId);
        }

        List<Long> eduCategoryIds = LionConfigUtils.getEduDealCategoryIds();
        // 管道疏通需要判断是否是无忧通团单，教育需要判断是不是在线教育
        if ((publishCategoryId == 414 || eduCategoryIds.contains(publishCategoryId)) && dealGroupDTO != null
                && CollectionUtils.isNotEmpty(dealGroupDTO.getAttrs())) {
            List<AttributeDTO> attrs = dealGroupDTO.getAttrs().stream().map(attrDTO -> {
                AttributeDTO attributeDTO = new AttributeDTO();
                attributeDTO.setName(attrDTO.getName());
                attributeDTO.setValue(attrDTO.getValue());
                attributeDTO.setSource(attrDTO.getSource());
                return attributeDTO;
            }).collect(Collectors.toList());
            ctx.setAttributes(attrs);
        }

        //判断是不是0元预约团单
        boolean isZeroResv = dealGroupDTO != null && dealGroupDTO.getBasic() != null
                && dealGroupDTO.getBasic().getTradeType() != null
                && dealGroupDTO.getBasic().getTradeType() == TradeTypeEnum.RESERVATION.getCode()
                && Lion.getBoolean("com.sankuai.dzu.tpbase.dztgdetailweb","com.sankuai.dzu.tpbase.dztgdetailweb.zeroVaccine.switch",false);

        // 判断是否是穿戴甲
        boolean isWearableNail = DealUtils.isNewWearableNailDeal(dealGroupDTO);
        ModuleExtraDTO moduleExtraDTO = buildModuleExtraDTO(ctx, (int) publishCategoryId,
                isNewPhysicalExerciseModule, isStandardEyeDealGroup(ctx, dealGroupDTO), isZeroResv,
                isWearableNail, serviceType);

        // 教育零元单
        if (moduleExtraDTO != null && isZeroResv && FreeDealEnum.EDU_TRIAL_BOOKING == FreeDealEnum.fromDealCategory(String.valueOf(publishCategoryId))) {
            return eduFreeDealModuleRename(moduleExtraDTO);
        }

        // 特定场景下修改"团购详情"的key
        dealCategoryFactory.resetDealDetailModuleConfig(DealCategoryParam.builder().envCtx(ctx.getEnvCtx()).dealCategoryId(publishCategoryId).moduleExtraDTO(moduleExtraDTO).dealGroupDTO(dealGroupDTO).build());
        return moduleExtraDTO;
    }




    public ModuleExtraDTO eduFreeDealModuleRename(ModuleExtraDTO moduleExtraDTO) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.UnifiedModuleExtraFacade.eduFreeDealModuleRename(com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleExtraDTO)");
        moduleExtraDTO.getModuleConfigDos().forEach(moduleConfigDO -> {
            if (moduleConfigDO.getKey().equals("团购详情")) {
                moduleConfigDO.setKey("课程详情");
            }
            if (moduleConfigDO.getKey().equals("购买须知")) {
                moduleConfigDO.setKey("报名须知");
            }
        });
        return moduleExtraDTO;
    }

    private static boolean isStandardEyeDealGroup(DealCtx ctx, DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO == null || dealGroupDTO.getCategory() == null
                || dealGroupDTO.getCategory().getCategoryId() == null
                || dealGroupDTO.getCategory().getCategoryId() != 1604) {
            return false;
        }
        Long serviceProjectCategoryId = Optional.ofNullable(dealGroupDTO.getServiceProject())
                .map(DealGroupServiceProjectDTO::getMustGroups)
                .flatMap(mustGroups -> mustGroups.stream()
                        .findFirst()
                        .map(MustServiceProjectGroupDTO::getGroups)
                        .flatMap(groups -> groups.stream().findFirst()))
                .map(ServiceProjectDTO::getCategoryId)
                .orElse(0L);
        List<Long> standardEyeServiceProjectCategoryIds = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb", "com.sankuai.dzu.tpbase.dztgdetailweb.eye.standard.sku.category.ids", Long.class, new ArrayList<>());
        return VersionUtils.isGreatEqualThan(ctx.getMrnVersion(), "0.4.0") && standardEyeServiceProjectCategoryIds.contains(serviceProjectCategoryId);
    }

    public ModuleExtraDTO  buildModuleExtraDTO(DealCtx ctx,
                                              int publishCategoryId, boolean isNewPhysicalExerciseModule,
                                              boolean isStandardEyeDealGroup, boolean isZeroResvDealGroup,
                                              boolean isWearableNail, String serviceType) {
        if (publishCategoryId <= 0) {
            return null;
        }
        List<ModuleConfigDo> moduleConfigDoList = getModuleConfigDoList(ctx, publishCategoryId,
                isNewPhysicalExerciseModule, isStandardEyeDealGroup,isZeroResvDealGroup, isWearableNail, serviceType);
        moduleConfigDoList = filterFromCreateOrderPreview(ctx, moduleConfigDoList);
        moduleConfigDoList = resetItemIndexForCaiXi(ctx, publishCategoryId, moduleConfigDoList);
        ModuleExtraDTO moduleExtraDTO = new ModuleExtraDTO();
        moduleExtraDTO.setSuccess(true);
        moduleExtraDTO.setModuleConfigDos(moduleConfigDoList);
        moduleExtraDTO.setExpResults(ctx.getModuleAbConfigs());
        moduleExtraDTO.setExtraInfo(buildExtraInfoBySource(ctx));
        return moduleExtraDTO;
    }

    public List<ModuleConfigDo> buildExtraInfoBySource(DealCtx ctx){
        if (isFromCreateOrderPreview(ctx)){
            Map<String, ModuleConfigDo> pagesourceMapKeyValueMap = LionConfigUtils.getPagesourceMapKeyValueMap();
            List<ModuleConfigDo> extraInfo = Lists.newArrayList();
            extraInfo.add(pagesourceMapKeyValueMap.get(ctx.getRequestSource()));
            return extraInfo;
        }
        return null;
    }

    public boolean isFromCreateOrderPreview(DealCtx ctx){
        if (Objects.nonNull(ctx) && RequestSourceEnum.CREATE_ORDER_PREVIEW.getSource().equals(ctx.getRequestSource())){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    List<ModuleConfigDo> resetItemIndexForCaiXi(DealCtx ctx, int publishCategoryId, List<ModuleConfigDo> result) {
        if (!isNeedResetItemIndex(ctx, result)) {
            return result;
        }
        List<String> sortList = LionConfigUtils.getModuleConfigKeySort(buildKey(ctx, publishCategoryId));
        if (CollectionUtils.isEmpty(sortList)) {
            return result;
        }
        return resetConfigItemSort(result, sortList);
    }

    private boolean isNeedResetItemIndex(DealCtx ctx, List<ModuleConfigDo> result) {
        return CollectionUtils.isNotEmpty(result) && fromCAIXI(ctx.getRequestSource());
    }

    private static Boolean fromCAIXI(String requestSource){
        if (RequestSourceEnum.CAI_XI.getSource().equals(requestSource) || RequestSourceEnum.HOME_PAGE.getSource().equals(requestSource)){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    public ModuleConfigDo findModuleConfig(List<ModuleConfigDo> configDoList, String key) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.UnifiedModuleExtraFacade.findModuleConfig(java.util.List,java.lang.String)");
        return configDoList.stream()
                .filter(configDo -> configDo != null && configDo.getKey().equals(key))
                .findFirst().orElse(null);
    }

    public List<ModuleConfigDo> resetConfigItemSort(List<ModuleConfigDo> result, List<String> sortList) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.UnifiedModuleExtraFacade.resetConfigItemSort(java.util.List,java.util.List)");
        List<ModuleConfigDo> sortedList = new ArrayList<>();
        for (String key : sortList) {
            ModuleConfigDo configDo = findModuleConfig(result, key);
            if (configDo != null) {
                sortedList.add(configDo);
            }
        }
        for (ModuleConfigDo configDo : result) {
            if (!sortedList.contains(configDo)) {
                sortedList.add(configDo);
            }
        }
        return sortedList;
    }

    public List<ModuleConfigDo> filterFromCreateOrderPreview(DealCtx ctx, List<ModuleConfigDo> moduleConfigDoList){
        if (isFromCreateOrderPreview(ctx) && CollectionUtils.isNotEmpty(moduleConfigDoList)){
            return moduleConfigDoList.stream().filter(e->!isDealReviews(e.getValue())).collect(Collectors.toList());
        }
        return moduleConfigDoList;
    }

    private boolean isDealReviews(String config){
        Set<String> reviewerTabConfig = LionConfigUtils.getFilterReviewerTabConfig();
        return reviewerTabConfig.contains(config);
    }

    private static String getTuanDetailV2Config(boolean defaultModule, boolean android){
        String lionConfig;
        if (!defaultModule) {
            lionConfig = Lion.getStringValue(android ? LionConstants.ANDROID_CHANNEL_MODULE_CONFIGS_V2 : LionConstants.IOS_CHANNEL_MODULE_CONFIGS_V2);
        } else {
            lionConfig = Lion.getStringValue(LionConstants.NEW_CHANNEL_MODULE_CONFIGS_V2);
        }
        return lionConfig;
    }

    private static String getTuanDetailV1Config(boolean defaultModule, boolean android){
        String lionConfig;
        if (!defaultModule) {
            lionConfig = Lion.getStringValue(android ? LionConstants.ANDROID_CHANNEL_MODULE_CONFIGS : LionConstants.IOS_CHANNEL_MODULE_CONFIGS);
        } else {
            lionConfig = Lion.getStringValue(LionConstants.NEW_CHANNEL_MODULE_CONFIGS);
        }
        return lionConfig;
    }

    private static Map<String, List<ModuleConfigDo>> parseModuleConfigs(boolean tuanDetailV2, boolean defaultModule, boolean android) {
        String lionConfig;
        if (tuanDetailV2){
            lionConfig = getTuanDetailV2Config(defaultModule, android);
        }else {
            lionConfig = getTuanDetailV1Config(defaultModule, android);
        }
        if (StringUtils.isBlank(lionConfig)) {
            return null;
        }
        List<Object> jsonArray = JsonFacade.deserializeList(lionConfig, Object.class);
        if (jsonArray == null || jsonArray.size() <= 0) {
            return null;
        }
        Map<String, List<ModuleConfigDo>> result = Maps.newHashMap();
        for (int i = 0; i < jsonArray.size(); i++) {
            Map<String, Object> obj = (Map<String, Object>) jsonArray.get(i);
            String publishCategories = String.valueOf(obj.get("categories"));
            if (StringUtils.isEmpty(publishCategories)) {
                continue;
            }
            List<Object> moduleJsonArray = (List<Object>) obj.get("configs");
            if (moduleJsonArray == null || moduleJsonArray.size() <= 0) {
                continue;
            }
            List<ModuleConfigDo> moduleConfigDos = Lists.newArrayList();
            for (int j = 0; j < moduleJsonArray.size(); j++) {
                ModuleConfigDo moduleConfigDo = new ModuleConfigDo();
                Map<String, Object> expObj = (Map<String, Object>) moduleJsonArray.get(j);
                String configKey = String.valueOf(expObj.get("configKey"));
                String configValue = String.valueOf(expObj.get("configValue"));
                if (StringUtils.isBlank(configKey) || StringUtils.isBlank(configValue)) {
                    continue;
                }
                moduleConfigDo.setKey(configKey);
                moduleConfigDo.setValue(configValue);
                moduleConfigDos.add(moduleConfigDo);
            }
            result.put(publishCategories, moduleConfigDos);
        }
        return result;
    }

    private static boolean newModule(DealCtx ctx) {
        if (StringUtils.isEmpty(ctx.getExpResults())) {
            return false;
        }
        List<String> results = JsonFacade.deserializeList(ctx.getExpResults().toLowerCase(), String.class);
        for (String exp : results) {
            if (Lion.getStringValue("com.sankuai.dzu.tpbase.dztgdetailweb.new.module.exp.results", "exp000002_a").contains(exp)) {
                return true;
            }
        }
        return false;
    }

    public List<ModuleConfigDo> getModuleConfigDoList(DealCtx ctx,
                                                      int publishCategoryId, boolean isNewPhysicalExerciseModule,
                                                      boolean isStandardEyeDealGroup, boolean isZeroResvDealGroup,
                                                      boolean isWearableNail, String serviceType) {
        // 团详框架改版
        ModuleAbConfig moduleAbConfig = douHuService.enableCardStyleV2(ctx.getEnvCtx(), publishCategoryId, ctx.getMrnVersion());
        // 判断是否命中 团详改版实验
        Boolean isTuanDetailV2 = douHuService.hitEnableCardStyleV2(moduleAbConfig);
        Map<String, List<ModuleConfigDo>> moduleConfigMaps = parseModuleConfigs(isTuanDetailV2, newModule(ctx), ctx.getEnvCtx().isAndroid());
        if (MapUtils.isEmpty(moduleConfigMaps)) {
            return Collections.emptyList();
        }
        // 如果是CPV改造后的团单
        String categoryAndServiceTypeKey = buildKeyWithCategoryAndServiceType(ctx, publishCategoryId, serviceType);
        List<ModuleConfigDo> dealDetailModuleKey4Cpv = getModuleConfigsByCpvKey(moduleConfigMaps,
                categoryAndServiceTypeKey + "_cpv");
        if (!DealVersionUtils.isOldMetaVersion(ctx.getDealGroupDTO(), LionConstants.COMMON_MODULE_CALL_VERSION_CONFIG)
                && CollectionUtils.isNotEmpty(dealDetailModuleKey4Cpv)) {
            return dealDetailModuleKey4Cpv;
        }
        //如果是0元预约：
        if (isZeroResvDealGroup){
            if (FreeDealUtils.useIndependentTab((long) publishCategoryId)) {
                return moduleConfigMaps.get((ctx.isMt() ? "mt" + publishCategoryId: "dp" + publishCategoryId) + "_resv");
            }
        }

        // 如果是预订团单
        if (DealCtxHelper.isPreOrderDeal(ctx)) {
            String moduleConfigKey = (ctx.isMt() ? "mt" + publishCategoryId : "dp" + publishCategoryId) + "_preorder";
            List<ModuleConfigDo> moduleConfigDos = ModuleUtils.getModuleConfigsSupportMultiCat(moduleConfigMaps, moduleConfigKey);
            if (CollectionUtils.isNotEmpty(moduleConfigDos)) {
                return moduleConfigDos;
            }
        }

        // 如果是穿戴甲
        if (isWearableNail) {
            String moduleConfigKey = (ctx.isMt() ? "mt" + publishCategoryId : "dp" + publishCategoryId) + "_wearableNail";
            return moduleConfigMaps.get(moduleConfigKey);
        }

        // 体检行业快手小程序特殊处理，临时方案
        if (Lion.getBoolean(LionConstants.APP_KEY, LionConstants.DISTRIBUTE_HEALTH_CHECK_KUAI_SHOU_CONFIG) && publishCategoryId == 401 && AppCtxHelper.isKuaiShouMiniProgram(ctx.getEnvCtx()) && ctx.isMt()) {
            return moduleConfigMaps.get("mt" + publishCategoryId + "_kuaishou");
        }

        // 如果为摄影新团详
        String catStr = publishCategoryId + ":" + serviceType;
        HashSet<String> photoDealDetailCats = new HashSet<>(Lion.getList(Environment.getAppName(), PHOTO_NEW_DEAL_DETAIL_CATEGORY, String.class));
        String expResult = douHuService.getPhotoAbResult(ctx);
        // 关闭斗斛开关则全部切换新团详
        Boolean douhuEnabled = Lion.getBoolean(Environment.getAppName(), PHOTO_NEW_DEAL_DETAIL_DOUHU_SWITCH, true);
        if ((CollectionUtils.isNotEmpty(photoDealDetailCats) && photoDealDetailCats.contains(catStr))
                && (!douhuEnabled || DOUHU_EXPERIMENTAL_GROUP.equals(expResult))) {
            String moduleConfigKey = buildKeyWithCategoryAndServiceType(ctx, publishCategoryId, serviceType);
            return getModuleConfigsFromCategoriesKey(ctx, moduleConfigMaps, moduleConfigKey);
//            return moduleConfigMaps.get((ctx.isMt() ? "mt" + publishCategoryId : "dp" + publishCategoryId) + "_" + serviceType);
        }

        if(isNewPhysicalExerciseModule) {
            if (useStandardPhysicalExamModule(ctx)) {
                return moduleConfigMaps.get((ctx.isMt() ? "mt" + publishCategoryId : "dp" + publishCategoryId) + "_standard");
            } else {
                return moduleConfigMaps.get((ctx.isMt() ? "mt" + publishCategoryId : "dp" + publishCategoryId) + "_new");
            }
        }
        if (isStandardEyeDealGroup) {
            return moduleConfigMaps.get((ctx.isMt() ? "mt" + publishCategoryId : "dp" + publishCategoryId) + "_standard");
        }

        List<ModuleConfigDo> moduleConfigDos = ModuleUtils.getModuleConfigsSupportMultiCat(moduleConfigMaps, buildKey(ctx, publishCategoryId));
        if (CollectionUtils.isNotEmpty(moduleConfigDos)) {
            return moduleConfigDos;
        }
        return moduleConfigMaps.get(ctx.isMt() ? "mt" : "dp");
    }

    private List<ModuleConfigDo> getModuleConfigsByCpvKey(Map<String, List<ModuleConfigDo>> moduleConfigMaps,
            String key) {
        if (MapUtils.isEmpty(moduleConfigMaps) || StringUtils.isBlank(key)) {
            return null;
        }
        for (Map.Entry<String, List<ModuleConfigDo>> entry : moduleConfigMaps.entrySet()) {
            if (StringUtils.isEmpty(entry.getKey())) {
                continue;
            }
            Set<String> categories = Sets.newHashSet(entry.getKey().split(","));
            if (categories.contains(key)) {
                return entry.getValue();
            }
        }
        return null;
    }

    private List<ModuleConfigDo> getModuleConfigsFromCategoriesKey(DealCtx ctx, Map<String, List<ModuleConfigDo>> moduleConfigMaps, String categoriesKey) {
        for (Map.Entry<String, List<ModuleConfigDo>> entry : moduleConfigMaps.entrySet()) {
            if (StringUtils.isEmpty(entry.getKey())) {
                continue;
            }
            Set<String> categories = Sets.newHashSet(entry.getKey().split(","));
            if (categories.contains(categoriesKey)) {
                return entry.getValue();
            }
        }
        return moduleConfigMaps.get(ctx.isMt() ? "mt" : "dp");
    }

    private String buildKeyWithCategoryAndServiceType(DealCtx ctx, int publishCategoryId, String serviceType) {
        String key = ctx.isMt() ? "mt" + publishCategoryId : "dp" + publishCategoryId;
        if (StringUtils.isNotBlank(serviceType)) {
            key = key + "_" + serviceType;
        }
        return key;
    }


    public static String buildKey(DealCtx ctx, int publishCategoryId) {
        String key = ctx.isMt() ? "mt" + publishCategoryId : "dp" + publishCategoryId;
        if (isBeautyOlderVersion(ctx, publishCategoryId)) {
            key = key + "_old";
        }
        if (publishCategoryId == 414 && DealAttrHelper.isWuyoutong(ctx.getAttributes(), ctx.getMrnVersion())) {
            key = key + "_wuyoutong";
        }
        if (LionConfigUtils.isEduDealCategoryId(publishCategoryId) && isEduOnlineDeal(ctx.getAttributes())) {
            key = key + "_online";
        }
        return key;
    }

    private static boolean isBeautyOlderVersion(DealCtx ctx, int publishCategoryId) {
        try {
            if(ctx.isExternal()) {
                return false;
            }
            if(publishCategoryId == 501) {
                if(ctx.isMt() && VersionUtils.isGreatEqualThan("11.18.400", ctx.getEnvCtx().getVersion())) {
                    return true;
                }
                if(!ctx.isMt() && VersionUtils.isGreatEqualThan("10.59.0", ctx.getEnvCtx().getVersion())) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("version compare error, version is " + ctx.getEnvCtx().getVersion(),e);
        }

        return false;
    }

    private static boolean isEduOnlineDeal(List<AttributeDTO> attributes) {
        String serviceLeafIdStr = DealAttrHelper.getFirstValue(attributes, ATTR_SERVICE_TYPE_LEAF_ID);
        if (StringUtils.isEmpty(serviceLeafIdStr)) {
            return false;
        }
        Long serviceLeafId = Long.parseLong(serviceLeafIdStr);
        return LionConfigUtils.getEduOnlineDealServiceLeafIds().contains(serviceLeafId);
    }

    private boolean useStandardPhysicalExamModule(DealCtx context) {
        if (!VersionUtils.isGreatEqualThan(context.getMrnVersion(), "0.3.0")) {
            return false;
        }
        ModuleAbConfig config = douHuBiz.getAbExpResult(context.getEnvCtx(), context.isMt() ? "MTStandardPhysicalExamModule" : "DPStandardPhysicalExamModule");
        if (config == null || CollectionUtils.isEmpty(config.getConfigs())) {
            return false;
        }
        context.getModuleAbConfigs().add(config);
        return Optional.ofNullable(config.getConfigs().get(0))
                .map(AbConfig::isUseNewStyle)
                .orElse(false);
    }

    private static boolean isNewPhysicalExerciseModuleV2(DealCtx ctx, String mrnVersion,
                                                         long publishCategoryId, String reportType,
                                                         boolean isThirdPartyVerify, int thirdPartyId) {
        if(ctx.isExternal() && !(DztgClientTypeEnum.MEITUAN_MAP_APP.equals(ctx.getEnvCtx().getDztgClientTypeEnum())) && !ctx.getEnvCtx().getWxMiniList().contains(ctx.getEnvCtx().getDztgClientTypeEnum())) {
            return false;
        }

        boolean isSwitchOn = Lion.getBoolean("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.physicalexam.newmodule.switch", false);
        if(!isSwitchOn) {
            return false;
        }

        Map<String, MrnVersionConfig> versionConfigMap = Lion.getMap("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.mrnversion.config.map", MrnVersionConfig.class, new HashMap<>());
        MrnVersionConfig versionConfig = versionConfigMap.get(String.valueOf(publishCategoryId));
        if(versionConfig == null) {
            return false;
        }

        // publishCategory == 401 && mrnVersion >= 0.2.0
        if(publishCategoryId == 401L && VersionUtils.isGreatEqualThan(mrnVersion, versionConfig.getLowestVersion())) {
            if(StringUtils.isNotBlank(reportType)) {
                return isThirdPartyConditionPass(isThirdPartyVerify, thirdPartyId);
            }
        }
        return false;
    }

    private static boolean isThirdPartyConditionPass(boolean isThirdPartVerify, int thirdPartyId) {
        if(!isThirdPartVerify) {
            // 不是三方上单，后面逻辑不用判断了
            return true;
        }
        List<Integer> thirdPartyIdWhiteList = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.physicalexam.newmodule.thirdparty.whitelist", Integer.class, new ArrayList<>());

        return thirdPartyIdWhiteList.contains(thirdPartyId);
    }

    private int getThirdPartyId(DealGroupDTO dealGroupDTO) {
        if(dealGroupDTO == null || CollectionUtils.isEmpty(dealGroupDTO.getDeals())) {
            return 0;
        }
        for(DealGroupDealDTO dealDTO : dealGroupDTO.getDeals()) {
            if(dealDTO == null || dealDTO.getBasic() == null || dealDTO.getBasic().getStatus() != 1) {
                continue;
            }
            return Math.toIntExact(dealDTO.getBasic().getThirdPartyId() == null ? 0 : dealDTO.getBasic().getThirdPartyId());
        }
        return 0;
    }

    private boolean parseBoolean(String string){
        if (org.apache.commons.lang3.StringUtils.isBlank(string)) {
            return false;
        } else {
            return Boolean.parseBoolean(string);
        }
    }
}
