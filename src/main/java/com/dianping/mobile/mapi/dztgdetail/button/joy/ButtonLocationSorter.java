package com.dianping.mobile.mapi.dztgdetail.button.joy;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.google.common.collect.Sets;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Set;

public class ButtonLocationSorter extends AbstractButtonBuilder {

    private static final Set<Integer> NEED_SORT_BTN_TYPES = Sets.newHashSet(
            BuyBtnTypeEnum.MEMBER_CARD.getCode(), BuyBtnTypeEnum.JOY_CARD.getCode());

    private static final Comparator<DealBuyBtn> BUY_BTN_COMPARATOR = new Comparator<DealBuyBtn>() {
        @Override
        public int compare(DealBuyBtn o1, DealBuyBtn o2) {
            BigDecimal p1 = new BigDecimal(o1.getPriceStr());
            BigDecimal p2 = new BigDecimal(o2.getPriceStr());
            // 价格倒排
            return p2.compareTo(p1);
        }
    };

    @Override
    public void build(DealCtx context, ButtonBuilderChain chain) {
        sort(context);
        chain.build(context);
    }

    private void sort(DealCtx context) {
        if (context.getButtonCount() <= 1) {
            return;
        }
        for (DealBuyBtn buyBtn : context.getBuyBar().getBuyBtns()) {
            if (!NEED_SORT_BTN_TYPES.contains(buyBtn.getDetailBuyType())) {
                return;
            }
        }
        context.getBuyBar().getBuyBtns().sort(BUY_BTN_COMPARATOR);

        if (context.getBuyBar().getBuyBtns().get(0).getDetailBuyType() == BuyBtnTypeEnum.JOY_CARD.getCode()) {
            context.getBuyBar().setBuyType(DealBuyBar.BuyType.JOY_CARD.type);
        } else {
            context.getBuyBar().setBuyType(DealBuyBar.BuyType.DISCOUNTCARD.type);
        }
    }

    @Override
    protected void doBuild(DealCtx context, ButtonBuilderChain chain) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.button.joy.ButtonLocationSorter.doBuild(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx,com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain)");

    }
}
