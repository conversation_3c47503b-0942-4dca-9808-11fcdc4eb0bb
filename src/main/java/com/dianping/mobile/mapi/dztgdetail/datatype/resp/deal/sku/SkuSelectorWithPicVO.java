package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.sku;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

@MobileDo(id = 0xb714)
public class SkuSelectorWithPicVO implements Serializable {
    /**
     * 团购名称
     */
    @MobileDo.MobileField(key = 0xb0e)
    private String productName;

    /**
     *
     */
    @MobileDo.MobileField(key = 0x8cdc)
    private List<SalesAttrToSkuBasicInfoDO> salesAttrToSkuBasicInfo;

    /**
     *
     */
    @MobileDo.MobileField(key = 0xd03f)
    private List<SkuSalesAttrWithPicDO> skuSalesAttrInfo;

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public List<SalesAttrToSkuBasicInfoDO> getSalesAttrToSkuBasicInfo() {
        return salesAttrToSkuBasicInfo;
    }

    public void setSalesAttrToSkuBasicInfo(
            List<SalesAttrToSkuBasicInfoDO> salesAttrToSkuBasicInfo) {
        this.salesAttrToSkuBasicInfo = salesAttrToSkuBasicInfo;
    }

    public List<SkuSalesAttrWithPicDO> getSkuSalesAttrInfo() {
        return skuSalesAttrInfo;
    }

    public void setSkuSalesAttrInfo(
            List<SkuSalesAttrWithPicDO> skuSalesAttrInfo) {
        this.skuSalesAttrInfo = skuSalesAttrInfo;
    }
}