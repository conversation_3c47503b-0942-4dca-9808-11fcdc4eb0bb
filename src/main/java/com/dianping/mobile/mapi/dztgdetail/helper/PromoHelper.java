package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Cons;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.DealBaseDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.MtDealModel;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.MtPromotionInfo;
import com.dianping.pay.promo.common.enums.promo.SpecialPromoType;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.PromoIdentityEnum;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.PromoUseTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.nib.mkt.common.base.enums.activity.SpecialPromoTypeEnum;
import com.sankuai.nibmktproxy.queryclient.proxy.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

public class PromoHelper {
    /**
     * 100
     */
    private static final BigDecimal HUNDRED = new BigDecimal(100);

    public static BigDecimal getPromoDisplayDTOAmount(PromoDisplayDTO promoDisplayDTO) {
        return promoDisplayDTO != null ? promoDisplayDTO.getPromoAmount() : BigDecimal.ZERO;
    }

    public static String getPromoDisplayDTODesc(PromoDisplayDTO promoDisplayDTO) {
        return promoDisplayDTO != null ? promoDisplayDTO.getTag() : null;
    }

    //选取优惠最大的普通优惠
    public static PromoDisplayDTO getValidPromo(List<PromoDisplayDTO> promoDisplayDTOs) {
        if (CollectionUtils.isEmpty(promoDisplayDTOs)) {
            return null;
        }
        PromoDisplayDTO result = null;
        BigDecimal maxCampaignPrice = BigDecimal.ZERO;
        for (PromoDisplayDTO dto : promoDisplayDTOs) {
            if (isValid(dto) && !isIdlePromo(dto)
                    && dto.getPromoAmount().compareTo(maxCampaignPrice) > 0) {
                result = dto;
                maxCampaignPrice = dto.getPromoAmount();
            }
        }
        return result;
    }

    public static List<PromoDisplayDTO> getNormalPromo(List<PromoDisplayDTO> promoDisplayList) {
        if (CollectionUtils.isEmpty(promoDisplayList)) {
            return Collections.emptyList();
        }

        List<PromoDisplayDTO> result = Lists.newArrayList();
        for (PromoDisplayDTO promo : promoDisplayList) {
            if (isValid(promo) && !isIdlePromo(promo)) {
                result.add(promo);
            }
        }

        return result;
    }

    public static List<PromoDisplayDTO> getIdlePromo(List<PromoDisplayDTO> promoDisplayList) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.helper.PromoHelper.getIdlePromo(java.util.List)");
        if (CollectionUtils.isEmpty(promoDisplayList)) {
            return Collections.emptyList();
        }

        PromoDisplayDTO maxNormalPromo = getValidPromo(promoDisplayList);

        List<PromoDisplayDTO> result = Lists.newArrayList();
        for (PromoDisplayDTO promo : promoDisplayList) {
            if (isValid(promo) && isIdlePromo(promo) && biggerThanMaxNormal(maxNormalPromo, promo)) {
                result.add(promo);
            }
        }

        return result;
    }

    public static boolean isValid(PromoDisplayDTO promo) {
        return promo != null && promo.isEnable() && StringUtils.isNotBlank(promo.getDescription())
                && promo.getPromoAmount() != null && promo.getPromoAmount().compareTo(BigDecimal.ZERO) > 0;
    }

    public static boolean isIdlePromo(PromoDisplayDTO promo) {
        return SpecialPromoType.IDLETIMES_PROMO.getCode() == promo.getSpecialPromoType();
    }

    private static boolean biggerThanMaxNormal(PromoDisplayDTO maxNormalPromo, PromoDisplayDTO idlePromo) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.helper.PromoHelper.biggerThanMaxNormal(com.dianping.pay.promo.display.api.dto.PromoDisplayDTO,com.dianping.pay.promo.display.api.dto.PromoDisplayDTO)");
        if (maxNormalPromo == null) {
            return true;
        }
        if (!maxNormalPromo.isEnable() || !maxNormalPromo.isPriceLineThrough()) {
            return true;
        }
        return idlePromo.getPromoAmount().compareTo(maxNormalPromo.getPromoAmount()) > 0;
    }

    public static GetPromotionDTO getValidNormalPromo(PromotionDTOResult promotion) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.helper.PromoHelper.getValidNormalPromo(com.sankuai.nibmktproxy.queryclient.proxy.PromotionDTOResult)");
        if (promotion == null || CollectionUtils.isEmpty(promotion.getGetPromotionDTO())) {
            return null;
        }

        for (GetPromotionDTO getPromotionDTO : promotion.getGetPromotionDTO()) {
            if (isValidDeductionPromo(getPromotionDTO) && isNormalPromo(getPromotionDTO)) {
                return getPromotionDTO;
            }
        }
        return null;
    }

    public static GetPromotionDTO getValidIdlePromo(PromotionDTOResult promotion) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.helper.PromoHelper.getValidIdlePromo(com.sankuai.nibmktproxy.queryclient.proxy.PromotionDTOResult)");
        if (promotion == null || CollectionUtils.isEmpty(promotion.getGetPromotionDTO())) {
            return null;
        }

        for (GetPromotionDTO getPromotionDTO : promotion.getGetPromotionDTO()) {
            if (isValidIdlePromo(getPromotionDTO)) {
                return getPromotionDTO;
            }
        }
        return null;
    }

    private static boolean isValidIdlePromo(GetPromotionDTO promotion) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.helper.PromoHelper.isValidIdlePromo(com.sankuai.nibmktproxy.queryclient.proxy.GetPromotionDTO)");
        return isValidDeductionPromo(promotion) && isIdlePromo(promotion);
    }

    private static boolean isIdlePromo(GetPromotionDTO promotion) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.helper.PromoHelper.isIdlePromo(com.sankuai.nibmktproxy.queryclient.proxy.GetPromotionDTO)");
        return promotion.getPromotionDTO().getDeductionDTO().getDzSpecialPromoType()
                == SpecialPromoTypeEnum.IDLETIMES_PROMO.getCode();
    }

    private static boolean isNormalPromo(GetPromotionDTO promotion) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.helper.PromoHelper.isNormalPromo(com.sankuai.nibmktproxy.queryclient.proxy.GetPromotionDTO)");
        return promotion.getPromotionDTO().getDeductionDTO().getDzSpecialPromoType()
                == SpecialPromoTypeEnum.NORMAL.getCode();
    }

    private static boolean isValidDeductionPromo(GetPromotionDTO promotion) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.helper.PromoHelper.isValidDeductionPromo(com.sankuai.nibmktproxy.queryclient.proxy.GetPromotionDTO)");
        if (promotion == null) {
            return false;
        }

        if (promotion.getPromotionType() != PromotionType.DEDUCTION) {
            return false;
        }

        if (promotion.getPromotionDTO() == null || promotion.getPromotionDTO().getDeductionDTO() == null) {
            return false;
        }

        DeductionDTO deductionDTO = promotion.getPromotionDTO().getDeductionDTO();

        if (new BigDecimal(deductionDTO.getReduce()).compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        return deductionDTO.isIsEnable();
    }

    private static boolean isValidCouponPromo(GetPromotionDTO promotion) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.helper.PromoHelper.isValidCouponPromo(com.sankuai.nibmktproxy.queryclient.proxy.GetPromotionDTO)");
        if (promotion == null) {
            return false;
        }

        if (promotion.getPromotionType() != PromotionType.COUPON) {
            return false;
        }

        if (promotion.getPromotionDTO() == null || promotion.getPromotionDTO().getCouponDTO() == null) {
            return false;
        }

        CouponDTO couponDTO = promotion.getPromotionDTO().getCouponDTO();

        if (new BigDecimal(couponDTO.getCouponValue()).compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        return true;
    }

    public static PromoDTO getCouponPromo(DealCtx context) {
        PriceDisplayDTO normalPrice = PriceHelper.getNormalPrice(context);
        if (CollectionUtils.isEmpty(normalPrice.getMorePromos())) {
            return null;
        }
        for (PromoDTO morePromo : normalPrice.getMorePromos()) {
            if (morePromo.getIdentity().getPromoType() == PromoTypeEnum.COUPON.getType()) {
                return morePromo;
            }
        }
        return null;
    }

    public static List<PromoDTO> getCouponPromos(DealCtx context) {
        PriceDisplayDTO normalPrice = PriceHelper.getNormalPrice(context);
        if (CollectionUtils.isEmpty(normalPrice.getUsedPromos())) {
            return null;
        }
        return normalPrice.getUsedPromos().stream()
                .filter(promoDTO -> promoDTO != null && promoDTO.getIdentity() != null && promoDTO.getIdentity().getPromoType() == PromoTypeEnum.COUPON.getType())
                .collect(Collectors.toList());
    }

    public static List<PromoDTO> getGovernmentConsumeCoupon(DealCtx context) {
        PriceDisplayDTO normalPrice = PriceHelper.getNormalPrice(context);
        if (CollectionUtils.isEmpty(normalPrice.getUsedPromos())) {
            return null;
        }
        return normalPrice.getUsedPromos().stream()
                .filter(promoDTO -> promoDTO != null && promoDTO.getIdentity() != null && promoDTO.getIdentity().getPromoType() == PromoTypeEnum.GOVERNMENT_CONSUME_COUPON.getType())
                .collect(Collectors.toList());
    }

    public static List<PromoDTO> getCouponPurchase(DealCtx context) {
        return Optional.of(PriceHelper.getNormalPrice(context))
                .filter(x -> CollectionUtils.isNotEmpty(x.getUsedPromos()))
                .map(PriceDisplayDTO::getUsedPromos)
                .orElse(new ArrayList<>())
                .stream()
                .filter(Objects::nonNull)
                .filter(PromoHelper::isCouponPurchase)
                .collect(Collectors.toList());
    }

    public static boolean isCouponPurchase(PromoDTO promoDTO) {
        return Optional.ofNullable(promoDTO)
                .filter(x -> x.getIdentity() != null && x.getIdentity().getPromoType() == PromoTypeEnum.COUPON.getType())
                .filter(x -> x.getUseType() != null && x.getUseType() == PromoUseTypeEnum.COUPON_PURCHASE.getType())
                .isPresent();
    }

    public static List<PromoDTO> getCouponUsePromo(DealCtx context) {
        PriceDisplayDTO normalPrice = PriceHelper.getNormalPrice(context);
        return normalPrice.getUsedPromos();
    }

    public static boolean canAssign(PromoDTO couponPromo) {
        return couponPromo != null && couponPromo.isCanAssign();
    }

    public static void assemblePromoInfo(DealBaseDo mtDealBase, List<PromoDisplayDTO> promoDisplayDTOs) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.helper.PromoHelper.assemblePromoInfo(com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.DealBaseDo,java.util.List)");
        if (org.apache.commons.collections.CollectionUtils.isEmpty(promoDisplayDTOs)) {
            return;
        }
        List<MtPromotionInfo> promotionInfos = Lists.newArrayList();
        BigDecimal maxCampaignPrice = BigDecimal.ZERO;
        for (PromoDisplayDTO dto : promoDisplayDTOs) {
            if (dto.isEnable()) {
                MtPromotionInfo info = new MtPromotionInfo();
                info.setId(dto.getPromoId());
                info.setTag(dto.getTag());
                info.setLongTitle(dto.getDescription());
                info.setLogo("减");
                info.setColor(Cons.PROMOTION_TAG_COLOR);
                promotionInfos.add(info);
                if (dto.getPromoAmount().compareTo(maxCampaignPrice) > 0 && dto.isSimpleReduce()) {
                    maxCampaignPrice = dto.getPromoAmount();
                }
            }
        }
        mtDealBase.setPromotionInfos(promotionInfos);
        if (maxCampaignPrice.compareTo(BigDecimal.ZERO) > 0) {
            mtDealBase.setCampaignPrice(maxCampaignPrice.doubleValue());
            mtDealBase.setCanBuyPrice(mtDealBase.getPrice() - mtDealBase.getCampaignPrice());
        }
    }


    public static void assemblePromoInfo(MtDealModel mtDealBase, List<PromoDisplayDTO> promoDisplayDTOs) {
        if (CollectionUtils.isEmpty(promoDisplayDTOs)) {
            return;
        }
        List<MtPromotionInfo> promotionInfos = Lists.newArrayList();
        BigDecimal maxCampaignPrice = BigDecimal.ZERO;
        for (PromoDisplayDTO dto : promoDisplayDTOs) {
            if (dto.isEnable()) {
                MtPromotionInfo info = new MtPromotionInfo();
                info.setId(dto.getPromoId());
                info.setTag(dto.getTag());
                info.setLongTitle(dto.getDescription());
                info.setLogo("减");
                info.setColor(Cons.PROMOTION_TAG_COLOR);
                promotionInfos.add(info);
                if (dto.getPromoAmount().compareTo(maxCampaignPrice) > 0 && dto.isSimpleReduce()) {
                    maxCampaignPrice = dto.getPromoAmount();
                }
            }
        }
        mtDealBase.setMtPromotionInfos(promotionInfos);
        if (maxCampaignPrice.compareTo(BigDecimal.ZERO) > 0) {
            mtDealBase.setCampaignPrice(maxCampaignPrice.doubleValue());
            mtDealBase.setCanbuyprice(mtDealBase.getPrice() - mtDealBase.getCampaignPrice());
        }
    }
}
