package com.dianping.mobile.mapi.dztgdetail.biz.processor.imagetext.strategy;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.DealCategoryFactory;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryParam;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ImageTextStrategyEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ContentDetailPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ImageTextDetailPBO;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024-12-12
 * @desc 图文详情处理策略抽象类
 */
public abstract class AbstractImageTextDetailStrategy implements ImageTextDetailStrategy  {
    @Resource
    private DealCategoryFactory dealCategoryFactory;

    @Override
    public ImageTextStrategyEnum getStrategyName() {
        return ImageTextStrategyEnum.DEFAULT;
    }

    @Override
    public ContentDetailPBO getContentDetail(DealGroupDTO dealGroupDTO, List<ContentPBO> contents, int threshold) {
        return getDefaultContentDetail(dealGroupDTO, contents);
    }

    @Override
    public ImageTextDetailPBO buildImageTextDetail(DealGroupDTO dealGroupDTO, List<ContentPBO> contents, int threshold, EnvCtx envCtx) {
        ImageTextDetailPBO imageTextDetail = new ImageTextDetailPBO();
        ContentDetailPBO content = getContentDetail(dealGroupDTO, contents, threshold);
        imageTextDetail.setContents(Lists.newArrayList(content));

        long categoryId = Optional.ofNullable(dealGroupDTO.getCategory())
                .map(DealGroupCategoryDTO::getCategoryId)
                .orElse(0L);

        boolean useNewDealStyle = dealCategoryFactory.newDealStyle(DealCategoryParam.builder()
                .dealCategoryId(categoryId).dealGroupDTO(dealGroupDTO).envCtx(envCtx).build());
        imageTextDetail.setUseCardStyle(useNewDealStyle);
        return imageTextDetail;
    }

    private ContentDetailPBO getDefaultContentDetail(DealGroupDTO dealGroupDTO, List<ContentPBO> contents) {
        ContentDetailPBO contentDetail = new ContentDetailPBO();
        // 图文详情标题
        contentDetail.setTitle("图文详情");
        // 图文详情展示内容
        contentDetail.setContents(contents);
        // 默认不折叠
        contentDetail.setFold(false);
        // 折叠阈值
        contentDetail.setFoldThreshold(contents.size() + 1);
        return contentDetail;
    }
}
