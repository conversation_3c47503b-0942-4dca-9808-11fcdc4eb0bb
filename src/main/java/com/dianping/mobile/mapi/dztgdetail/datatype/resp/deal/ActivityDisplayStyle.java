package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

@Data
@MobileDo(id = 0x480d)
public class ActivityDisplayStyle {

    /**
     * 倒计时文字背景色
     */
    @MobileDo.MobileField(key = 0x71db)
    private String countdownBgColor;

    /**
     * 倒计时文字颜色
     */
    @MobileDo.MobileField(key = 0xaea6)
    private String countdownTextColor;

    /**
     * 标签背景色
     */
    @MobileDo.MobileField(key = 0xe232)
    private String labelBgColor;

    /**
     * 标签文字颜色
     */
    @MobileDo.MobileField(key = 0xc1d9)
    private String labelTextColor;

    /**
     * 活动次标题
     */
    @MobileDo.MobileField(key = 0x1ba5)
    private String timeReplaceText;

    /**
     * 跳转链接
     */
    @MobileDo.MobileField(key = 0xa18c)
    private String jumpLink;

}