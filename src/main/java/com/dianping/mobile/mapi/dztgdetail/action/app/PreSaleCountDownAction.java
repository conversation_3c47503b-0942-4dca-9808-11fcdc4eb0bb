package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.PreSaleCountDownReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CountDownPBO;
import com.dianping.mobile.mapi.dztgdetail.facade.DealPreSaleQueryFacade;
import com.dianping.mobile.mapi.dztgdetail.helper.AppCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.util.Version;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

/**
 * @Author: huqi
 * @Date: 2020/3/12 3:26 下午
 */
@InterfaceDoc(displayName = "到综团祥页大促接口",
        type = "mapi",
        description = "到综团单展示倒计时，背景图，文字描述等",
        scenarios = "该接口仅适用于双平台APP站点的团购详情页，其他以到综团购为纬度的页面如需使用请咨询项目owner",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/presalecountdown.bin",
        authors = "huqi"
)
@Controller("general/platform/dztgdetail/presalecountdown.bin")
@Action(url = "presalecountdown.bin", httpType = "get")
public class PreSaleCountDownAction extends AbsAction<PreSaleCountDownReq> {

    Logger logger = LoggerFactory.getLogger(PreSaleCountDownAction.class);

    @Autowired
    private DealPreSaleQueryFacade dealPreSaleQueryFacade;

    @Override
    protected IMobileResponse validate(PreSaleCountDownReq request, IMobileContext iMobileContext) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.action.app.PreSaleCountDownAction.validate(PreSaleCountDownReq,IMobileContext)");
        if (request == null || request.getDealgroupid() <= 0) {
            return Resps.PARAM_ERROR;
        }
        if (versionLimited(iMobileContext)) {
            return Resps.NoDataResp;
        }
        return null;
    }

    private boolean versionLimited(IMobileContext iMobileContext) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.action.app.PreSaleCountDownAction.versionLimited(com.dianping.mobile.framework.datatypes.IMobileContext)");
        Version v = new Version(iMobileContext.getVersion());
        return (AppCtxHelper.isMeituanClient(iMobileContext) && v.less(Version.MT_V10_8_200))
                || (!AppCtxHelper.isMeituanClient(iMobileContext) && v.less(Version.DP_V10_26_0));
    }

    @Override
    protected IMobileResponse execute(PreSaleCountDownReq request, IMobileContext iMobileContext) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.action.app.PreSaleCountDownAction.execute(PreSaleCountDownReq,IMobileContext)");
        try {
            EnvCtx envCtx = initEnvCtx(iMobileContext);
            CountDownPBO result = dealPreSaleQueryFacade.queryPreSaleCountDown(request, envCtx);

            if (result != null) {
                return new CommonMobileResponse(result);
            }
        } catch (Exception e) {
            logger.error("queryPreSaleCountDown failed,request={},错误信息:", JSON.toJSONString(request), e);
        }
        return Resps.NoDataResp;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.action.app.PreSaleCountDownAction.getRule()");
        return null;
    }

}
