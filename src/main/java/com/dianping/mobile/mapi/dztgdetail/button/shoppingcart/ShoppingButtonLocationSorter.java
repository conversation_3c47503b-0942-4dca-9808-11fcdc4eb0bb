package com.dianping.mobile.mapi.dztgdetail.button.shoppingcart;

import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.util.CostEffectivePinTuanUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/16
 */
public class ShoppingButtonLocationSorter extends AbstractButtonBuilder {
    public static final Map<Integer, Integer> INDEX_MAP = new HashMap<Integer, Integer>(){
        {
            put(BuyBtnTypeEnum.COST_EFFECTIVE_PINTUAN.getCode(), 0);
            put(BuyBtnTypeEnum.NORMAL_DEAL.getCode(), 1);
            put(BuyBtnTypeEnum.MEMBER_CARD.getCode(), 2);
            put(BuyBtnTypeEnum.TIMES_CARD.getCode(), 3);
            put(BuyBtnTypeEnum.IDLE_DEAL.getCode(), 4);
            put(BuyBtnTypeEnum.PINTUAN.getCode(), 5);
        }
    };

    @Override
    public void doBuild(DealCtx context, ButtonBuilderChain chain) {
        sort(context);
        chain.build(context);
    }

    /**
     * 前端通过buyButtons的list顺序决定展示逻辑
     * @param context
     */
    public void sort(DealCtx context) {
        List<DealBuyBtn> buyButtons = context.getBuyBar().getBuyBtns();
        buyButtons = buyButtons.stream().filter(btn -> INDEX_MAP.keySet().contains(btn.getPriceRuleModule().getPriceRuleType())).collect(Collectors.toList());
        buyButtons.sort((btn1, btn2) -> {
                    int index1 = INDEX_MAP.getOrDefault(btn1.getPriceRuleModule().getPriceRuleType(), -1);
                    int index2 = INDEX_MAP.getOrDefault(btn2.getPriceRuleModule().getPriceRuleType(), -1);
                    return Integer.compare(index1, index2);
                });

        handleCostEffectivePinTuan(context, buyButtons);
        context.getBuyBar().setBuyBtns(buyButtons);
    }

    private void handleCostEffectivePinTuan(DealCtx context, List<DealBuyBtn> buyButtons) {
        if (CollectionUtils.isEmpty(buyButtons)) {
            return;
        }
        if (!context.getCostEffectivePinTuan().isCePinTuanScene()) {
            return;
        }

        // 将非拼团底bar的第一个底bar信息赋值给拼团场景下的直接购买按钮
        CostEffectivePinTuanUtils.updateDirectBuyBarForPinTuan(buyButtons);
    }
}
