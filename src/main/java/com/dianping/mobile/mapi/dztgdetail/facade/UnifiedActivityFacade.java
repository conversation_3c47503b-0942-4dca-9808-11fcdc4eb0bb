package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.gm.marketing.times.card.api.enums.PlatformEnum;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.*;
import com.dianping.mobile.mapi.dztgdetail.common.constants.PlusIcons;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.ActivityCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedActivityModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.ActivityModuleDTO;
import com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper;
import com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil;
import com.dianping.mobile.mapi.dztgdetail.util.PlusNumUtils;
import com.dianping.pigeon.remoting.invoker.config.annotation.Reference;
import com.dianping.tpfun.product.api.sku.pintuan.dto.BestPinTag;
import com.sankuai.dealuser.price.display.api.PriceDisplayService;
import com.sankuai.dealuser.price.display.api.enums.ProductTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.RequestSceneEnum;
import com.sankuai.dealuser.price.display.api.model.*;
import com.sankuai.dzcard.navigation.api.dto.CardQualifyEventIdDTO;
import com.sankuai.dzcard.navigation.api.enums.QualifyEventTypeEnum;
import com.sankuai.merchantcard.timescard.exposure.dto.CardSummaryBarDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.Future;

/**
 * Created by zuomlin on 2018/12/13.
 */
@Component
public class UnifiedActivityFacade {

    @Autowired
    private SkuWrapper skuWrapper;

    @Autowired
    private MapperWrapper mapperWrapper;

    @Autowired
    private TimesCardWrapper timesCardWrapper;

    @Autowired
    private DealGroupWrapper dealGroupWrapper;

    @Autowired
    private DzCardPromoWrapper cardWrapper;

    @Reference(timeout = 300)
    private PriceDisplayService priceDisplayService;

    public ActivityModuleDTO queryUnifiedActivityModule(UnifiedActivityModuleReq request, EnvCtx envCtx, IMobileContext iMobileContext) throws Exception {
        ActivityCtx activityCtx = initActivityCtx(request, envCtx);
        activityCtx.setContext(iMobileContext);

        Future pinPoolFuture = skuWrapper.prepareBestPin(activityCtx.getDpId(), activityCtx);
        Future timesCardFuture;
        Future<?> cardFuture;

        if (activityCtx.isMt()) {
            timesCardFuture = timesCardWrapper.preTimesCardsV2(activityCtx.getMtId(), activityCtx.getMtShopIdLong(), PlatformEnum.MT.getCode(), activityCtx.getEnvCtx().getMtUserId());//已确认判断平台后再使用
            cardFuture = cardWrapper.prepareV2(activityCtx.getMtShopIdLong(), activityCtx.getMtId(), iMobileContext.getUserId(), true, envCtx, request.getPageSource());
        } else {
            timesCardFuture = timesCardWrapper.preTimesCardsV2(activityCtx.getDpId(), activityCtx.getDpShopIdLong(), PlatformEnum.DP.getCode(), activityCtx.getEnvCtx().getDpUserId());
            cardFuture = cardWrapper.prepareV2(activityCtx.getDpShopIdLong(), activityCtx.getDpId(), iMobileContext.getUserId(), false, envCtx, request.getPageSource());
        }

        List<CardQualifyEventIdDTO> cards = cardWrapper.resolve(cardFuture);
        CardSummaryBarDTO timesCard = timesCardWrapper.queryTimesCard(timesCardFuture);
        BestPinTag bestPinTag = skuWrapper.getBestPin(pinPoolFuture);

        if (!isValidTimesCard(timesCard) && !isValidMemberCard(cards) && !isValidJoyCard(cards)) {//这个判断成立表示底bar已经有了拼团btn，所以不需要返回拼团活动
            return null;
        }

        return buildUnifiedActivityModule(envCtx, activityCtx, bestPinTag);
    }

    private ActivityModuleDTO buildUnifiedActivityModule(EnvCtx envCtx, ActivityCtx activityCtx, BestPinTag pinProductBrief) {
        if (!isValidAssembleDeal(pinProductBrief)) {
            return null;
        }

        if (activityCtx.getDpShopIdLong() > 0L || activityCtx.getMtShopIdLong() > 0L) {
            PriceResponse<PriceDisplayDTO> response = priceDisplayService.queryPrice(buildPriceRequest(activityCtx));

            if (isPinTuanDaoGua(response, pinProductBrief.getPrice())) {
                return null;
            }
        }

        ActivityModuleDTO activityModuleDTO = new ActivityModuleDTO();
        activityModuleDTO.setTitle("活动");

        String poolDesc = String.format("%s人拼团 ￥%s", pinProductBrief.getPinPersonNum(), PlusNumUtils.trimDecimal(pinProductBrief.getPrice().toString()));
        String desc = activityCtx.isMt() ?
                String.format("%s人拼团，每人%s", pinProductBrief.getPinPersonNum(), PlusNumUtils.trimDecimal(pinProductBrief.getPrice().toString())) :
                JsonLabelUtil.pinPoolDPJson(poolDesc, "￥" + PlusNumUtils.trimDecimal(pinProductBrief.getPrice().toString()));
        activityModuleDTO.setDesc(desc);
        activityModuleDTO.setIconUrl(activityCtx.isMt() ? PlusIcons.MT_POOL : PlusIcons.DP_POOL);
        activityModuleDTO.setLeadText("去拼团");
        activityModuleDTO.setLeadUrl(UrlHelper.getAppUrl(envCtx, pinProductBrief.getUrl(), activityCtx.isMt()));
        return activityModuleDTO;
    }

    private boolean isPinTuanDaoGua(PriceResponse<PriceDisplayDTO> response, BigDecimal pinTuanPrice) {
        if (response == null || !response.isSuccess() || response.getData() == null) {
            return false;
        }

        return pinTuanPrice.compareTo(response.getData().getPrice()) >= 0;
    }

    private PriceRequest buildPriceRequest(ActivityCtx ctx) {
        PriceRequest request = new PriceRequest();

        int dealGroupId = ctx.isMt() ? ctx.getMtId() : ctx.getDpId();
        ProductIdentity identity = new ProductIdentity(dealGroupId, ProductTypeEnum.DEAL.getType());

        ClientEnv clientEnv = new ClientEnv();
        request.setClientEnv(clientEnv);
        request.setIdentity(identity);
        request.setScene(RequestSceneEnum.PROMO_DETAIL_DESC.getScene());

        EnvCtx envCtx = ctx.getEnvCtx();
        clientEnv.setClientType(envCtx.getClientType());
        clientEnv.setUnionId(envCtx.getUnionId());
        clientEnv.setVersion(envCtx.getVersion());

        if (ctx.isMt()) {
            clientEnv.setCityId(ctx.getMtCityId());
            clientEnv.setUuid(envCtx.getUuid());
            request.setLongShopId(ctx.getMtShopIdLong());
            request.setUserId(envCtx.getMtUserId());//已确认判断平台后再使用
        } else {
            clientEnv.setCityId(ctx.getDpCityId());
            clientEnv.setUuid(envCtx.getDpId());
            request.setLongShopId(ctx.getDpShopIdLong());
            request.setUserId(envCtx.getDpUserId());
        }

        return request;
    }

    private ActivityCtx initActivityCtx(UnifiedActivityModuleReq request, EnvCtx envCtx) {
        ActivityCtx activityCtx = new ActivityCtx(envCtx);

        long longShopId = request.getShopIdLong();

        if (envCtx.isMt()) {
            activityCtx.setMtId(request.getDealGroupId());
            activityCtx.setMtCityId(request.getCityId());
            activityCtx.setMtShopIdLong(longShopId);

            Future dealIdMapperFuture = dealGroupWrapper.preDpDealGroupId(activityCtx.getMtId());
            Future cityIdMapperFuture = mapperWrapper.preDpCityByMtCity(activityCtx.getMtCityId());
            Future shopIdMapperFuture = mapperWrapper.preDpShopIdByMtShopId(activityCtx.getMtShopIdLong());
            activityCtx.setDpId(dealGroupWrapper.getDpDealGroupId(dealIdMapperFuture));
            activityCtx.setDpShopIdLong(mapperWrapper.getDpShopIdByMtShopIdLong(shopIdMapperFuture));
            activityCtx.setDpCityId(mapperWrapper.getDpCityByMtCity(cityIdMapperFuture));
        } else {
            activityCtx.setDpCityId(request.getCityId());
            activityCtx.setDpId(request.getDealGroupId());
            activityCtx.setDpShopIdLong(longShopId);
        }

        return activityCtx;
    }

    private static boolean isValidTimesCard(CardSummaryBarDTO timesCard) {
        return timesCard != null && timesCard.getPrice() != null && timesCard.getTimes() > 0;
    }

    private static boolean isValidMemberCard(List<CardQualifyEventIdDTO> cards) {
        return Optional.ofNullable(cards)
                .orElse(Collections.emptyList())
                .stream()
                .anyMatch(c -> c.getQualifyEventType() == QualifyEventTypeEnum.DISCOUNT_CARD.getCode()
                        || c.getQualifyEventType() == QualifyEventTypeEnum.MEMBER_DAY.getCode());
    }

    private static boolean isValidJoyCard(List<CardQualifyEventIdDTO> cards) {
        return Optional.ofNullable(cards)
                .orElse(Collections.emptyList())
                .stream()
                .anyMatch(c -> c.getQualifyEventType() == QualifyEventTypeEnum.JOY_CARD.getCode());
    }

    private boolean isValidAssembleDeal(BestPinTag bestPinTag) {
        return bestPinTag != null && bestPinTag.getId() != null && bestPinTag.getId() > 0;
    }
}
