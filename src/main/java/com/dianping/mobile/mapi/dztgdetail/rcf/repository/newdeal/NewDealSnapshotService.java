package com.dianping.mobile.mapi.dztgdetail.rcf.repository.newdeal;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.bff.cache.enums.RcfDealBffClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.rcf.api.req.DealNativeSnapshotReq;
import com.dianping.mobile.mapi.dztgdetail.rcf.api.vo.DealFlexBoxCfg;
import com.dianping.mobile.mapi.dztgdetail.rcf.api.vo.DealRcfNativeSnapshot;
import com.meituan.dorado.common.exception.RpcException;
import com.sankuai.dz.product.detail.gateway.api.bff.request.UnifiedPageRequest;
import com.sankuai.dz.product.detail.gateway.api.cache.ProductDetailRcfCacheService;
import com.sankuai.dz.product.detail.gateway.api.cache.request.PageRequestExtraParamDTO;
import com.sankuai.dz.product.detail.gateway.api.cache.request.RcfCacheRequest;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.GpsCoordinateTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.PageRegionEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2025/8/1 15:10
 */
@Slf4j
@Service
public class NewDealSnapshotService {

    @Autowired
    private ProductDetailRcfCacheService productDetailRcfCacheService;

    public DealRcfNativeSnapshot getSnapshot(final DealNativeSnapshotReq request,
                                             final EnvCtx envCtx,
                                             final RcfDealBffClientTypeEnum rcfClientType) {
        try {
            RcfCacheRequest rcfCacheRequest = new RcfCacheRequest();
            rcfCacheRequest.setRequest(buildRequest(request, envCtx, rcfClientType));
            rcfCacheRequest.setExtraParam(buildExtraParam(request));
            com.sankuai.dz.product.detail.gateway.api.cache.response.DealRcfNativeSnapshot newDealSnapshot =
                    productDetailRcfCacheService.query(rcfCacheRequest);
            if (newDealSnapshot == null || newDealSnapshot.getDealFlexBoxConfig() == null) {
                throw new RpcException("新团详RCF接口返回值非法!!!");
            }
            return mapToOldDealSnapshot(newDealSnapshot);
        } catch (Throwable throwable) {
            log.error("NewDealSnapshotService.getSnapshot,req:{}", JSON.toJSONString(request), throwable);
            return buildDefaultResult();
        }
    }

    @NotNull
    private static UnifiedPageRequest buildRequest(final DealNativeSnapshotReq request,
                                                   final EnvCtx envCtx,
                                                   final RcfDealBffClientTypeEnum rcfClientType) {
        UnifiedPageRequest unifiedPageRequest = new UnifiedPageRequest();
        unifiedPageRequest.setClientType(getClientTypeEnum(rcfClientType).getCode());
        unifiedPageRequest.setUserId(envCtx.getUserId());
        unifiedPageRequest.setPageRegion(PageRegionEnum.FIRST_SCREEN_CACHE.getCode());
        unifiedPageRequest.setProductId(request.getDealGroupId());
//        unifiedPageRequest.setSkuId();
        unifiedPageRequest.setProductType(ProductTypeEnum.DEAL.getCode());
        unifiedPageRequest.setPoiId(request.getPoiId());
        unifiedPageRequest.setPoiIdEncrypt(request.getPoiidEncrypt());
        unifiedPageRequest.setCityId(request.getCityId());
        unifiedPageRequest.setGpsCityId(request.getGpsCityId());
        unifiedPageRequest.setUserLng(request.getUserLng());
        unifiedPageRequest.setUserLat(request.getUserLat());
//        unifiedPageRequest.setCityLng();
//        unifiedPageRequest.setCityLat();
        unifiedPageRequest.setGpsCoordinateType(GpsCoordinateTypeEnum.GCJ02.getCode());
        unifiedPageRequest.setPageSource(request.getPageSource());
//        unifiedPageRequest.setFromPage();
//        unifiedPageRequest.setCustomParam();
        unifiedPageRequest.setHeaderMap(new HashMap<>());
        unifiedPageRequest.setCookieMap(new HashMap<>());
//        unifiedPageRequest.setToken();
//        unifiedPageRequest.setPricecipher();
//        unifiedPageRequest.setCx();
//        unifiedPageRequest.setCsecversionname();
        unifiedPageRequest.setMrnversion(request.getMrnVersion());
        return unifiedPageRequest;
    }

    private static PageRequestExtraParamDTO buildExtraParam(final DealNativeSnapshotReq request) {
        PageRequestExtraParamDTO extraParamDTO = new PageRequestExtraParamDTO();
        extraParamDTO.setDeviceHeight(Optional.ofNullable(request.getDeviceHeight()).orElse(0.0));
        extraParamDTO.setDeviceWidth(Optional.ofNullable(request.getDeviceWidth()).orElse(0.0));
        return extraParamDTO;
    }

    private static ClientTypeEnum getClientTypeEnum(final RcfDealBffClientTypeEnum rcfClientType) {
        switch (rcfClientType) {
            case MT_APP:
                return ClientTypeEnum.MT_APP;
            default:
                throw new IllegalArgumentException("unknown clientType:" + rcfClientType);
        }
    }

    @NotNull
    private static DealRcfNativeSnapshot mapToOldDealSnapshot(
            com.sankuai.dz.product.detail.gateway.api.cache.response.DealRcfNativeSnapshot newDealSnapshot
    ) {
        DealRcfNativeSnapshot oldDealSnapshot = new DealRcfNativeSnapshot();
        DealFlexBoxCfg dealFlexBoxCfg = new DealFlexBoxCfg();
        dealFlexBoxCfg.setRender(newDealSnapshot.getDealFlexBoxConfig().isRender());
        dealFlexBoxCfg.setMtFlexboxTemplateUrl(newDealSnapshot.getDealFlexBoxConfig().getMtFlexboxTemplateUrl());
        oldDealSnapshot.setDealFlexBoxConfig(dealFlexBoxCfg);
        oldDealSnapshot.setFirstScreenBffCache(newDealSnapshot.getFirstScreenBffCache());
        oldDealSnapshot.setDealLayoutComponents(newDealSnapshot.getDealLayoutComponents());
        return oldDealSnapshot;
    }

    private static DealRcfNativeSnapshot buildDefaultResult() {
        DealRcfNativeSnapshot snapshot = new DealRcfNativeSnapshot();
        snapshot.setDealFlexBoxConfig(new DealFlexBoxCfg());
        return snapshot;
    }

}
