package com.dianping.mobile.mapi.dztgdetail.common.enums;

/**
 * deal酒店图标的枚举
 * Author: oulong
 * Version: 1.0
 * Created: 7/13/13 - 12:07 上午
 */
public enum DealHotelAttrEnum {
    // 早餐
    breakfast_free(5, "免费早餐", 1, "F"),
    breakfast_charge(5, "收费早餐", 1, "P"),
    breakfast_no(5, "不含早餐", 0, "N"),
    // wifi
    wifi_free(3, "免费WiFi", 1, "F"),
    wifi_charge(3, "收费WiFi", 1, "P"),
    wifi_no(3, "无WiFi", 0, "N"),
    // 宽带
    wideband_free(4, "免费宽带", 1, "F"),
    wideband_charge(4, "收费宽带", 1, "P"),
    wideband_no(4, "无宽带", 0, "N"),
    // 洗浴
    bath_free(6, "免费洗浴", 1, "F"),
    bath_charge(6, "收费洗浴", 1, "P"),
    bath_no(6, "无热水洗浴", 0, "N"),
    // 美团在线预约
    bookonline_support(33, "支持在线预约", 1, "Y"),
    bookonline_no(33, "不支持在线预约", 0, "N");

    // 属性id
    private int attrId;
    // 酒店属性文案
    private String text;
    // 图标亮起状态
    private int status;
    // 属性编码
    private String code;

    private DealHotelAttrEnum(int attrId, String text, int status, String code) {
        this.attrId = attrId;
        this.text = text;
        this.status = status;
        this.code = code;
    }

    public int getAttrId() {
        return attrId;
    }

    public String getText() {
        return text;
    }

    public int getStatus() {
        return status;
    }

    public String getCode() {
        return code;
    }
}

