package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;

@TypeDoc(description = "到综团单购买按钮数据模型")
@MobileDo(id = 0x233e)
public class DealBuyPBO implements Serializable {

    @FieldDoc(description = "按钮是否可用")
    @MobileField(key = 0x51dd)
    private boolean btnEnable;

    @FieldDoc(description = "按钮文案")
    @MobileField(key = 0x6f4e)
    private String btnDesc;

    @FieldDoc(description = "true表示禁止用优惠台")
    @MobileField(key = 0x836f)
    private boolean disablePromo;

    public boolean getBtnEnable() {
        return btnEnable;
    }

    public void setBtnEnable(boolean btnEnable) {
        this.btnEnable = btnEnable;
    }

    public String getBtnDesc() {
        return btnDesc;
    }

    public void setBtnDesc(String btnDesc) {
        this.btnDesc = btnDesc;
    }

    public boolean getDisablePromo() {
        return disablePromo;
    }

    public void setDisablePromo(boolean disablePromo) {
        this.disablePromo = disablePromo;
    }

}
