package com.dianping.mobile.mapi.dztgdetail.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-06-06
 * @desc 日志打印条件配置
 */
@Data
public class LogPrintConditionConfig {
    /**
     * 环境，test、prod
     */
    private List<String> env;
    /**
     * cell
     */
    private List<String> cell;
    /**
     * 泳道
     */
    private List<String> swimlane;
    /**
     * 用户ID
     */
    private List<String> userIdStr;
    /**
     * 客户端名称
     */
    private List<String> dztgClientType;
    /**
     * 接口名
     */
    private List<String> apiName;
    /**
     * 统一设备id
     */
    private List<String> unionId;
    /**
     * 渠道来源
     */
    private List<String> pageSource;
    /**
     * 是否打印日志
     */
    private boolean printLog;
    /**
     * 日志打印策略
     */
    private LogPrintStrategy logPrintStrategy;
}
