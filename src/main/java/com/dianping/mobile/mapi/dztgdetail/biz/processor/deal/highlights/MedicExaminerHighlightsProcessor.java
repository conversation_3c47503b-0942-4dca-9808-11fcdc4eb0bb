package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.utils.ExaminerUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.sankuai.general.product.query.center.client.builder.model.*;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.AttrSubjectEnum;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import jodd.util.ArraysUtil;
import org.apache.curator.shaded.com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.Future;

@Component
public class MedicExaminerHighlightsProcessor extends AbsDealProcessor implements ApplicationListener {

    public static final Map<String, IExaminerAbstractHandler> EXAMINER_SERVER_TYPE_HANDLER_MAP = new HashMap<>();

    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    private QueryCenterWrapper queryCenterWrapper;

    @Override
    public void prepare(DealCtx ctx) {
        int dealGroupId = ctx.isMt() ? ctx.getMtId() : ctx.getDpId();
        QueryByDealGroupIdRequest request = ExaminerUtils.getExaminerRequest(dealGroupId, ctx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP);
        Future singleDealGroupDtoFuture = queryCenterWrapper.preDealGroupDTO(request);
        ctx.getFutureCtx().setSingleDealGroupDtoFuture(singleDealGroupDtoFuture);
    }

    @Override
    public void process(DealCtx ctx) {
        DealGroupDTO dealGroupDTO = null;
        try {
            dealGroupDTO = queryCenterWrapper.getDealGroupDTO(ctx.getFutureCtx().getSingleDealGroupDtoFuture());
        } catch (Exception e) {
            logger.error("queryCenterWrapper.getDealGroupDTO error,", e);
            ctx.setQueryCenterHasError(true);
        }
        ctx.setDealGroupDTO(dealGroupDTO);

        buildExaminerHighlights(ctx);
    }

    public void buildExaminerHighlights(DealCtx ctx) {
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        if (dealGroupDTO == null) {
            return;
        }
        String serviceType = Optional.ofNullable(dealGroupDTO.getCategory())
                .map(DealGroupCategoryDTO::getServiceType).orElse("默认");
        IExaminerAbstractHandler handler = EXAMINER_SERVER_TYPE_HANDLER_MAP.getOrDefault(serviceType, EXAMINER_SERVER_TYPE_HANDLER_MAP.get("默认"));
        if (handler == null) {
            return;
        }
        handler.execute(ctx);
    }


    @Override
    public void onApplicationEvent(ApplicationEvent applicationEvent) {
        EXAMINER_SERVER_TYPE_HANDLER_MAP.put("默认", applicationContext.getBean(DefaultExaminerHandler.class));
        EXAMINER_SERVER_TYPE_HANDLER_MAP.put("入职体检", applicationContext.getBean(EntryExaminerHandler.class));
        EXAMINER_SERVER_TYPE_HANDLER_MAP.put("健康证检查", applicationContext.getBean(HealthCertificateExaminerHandler.class));
    }
}
