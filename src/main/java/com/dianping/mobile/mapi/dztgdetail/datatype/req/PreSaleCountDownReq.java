package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * @Author: huqi
 * @Date: 2020/3/12 3:30 下午
 */
@TypeDoc(description = "倒计时参数")
@MobileRequest
@Data
public class PreSaleCountDownReq implements IMobileRequest {

    @MobileRequest.Param(name = "pagetype")
    private Integer pagetype;

    /**
     * 城市id
     */
    @MobileRequest.Param(name = "cityid")
    private Integer cityid;

    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID")
    @MobileRequest.Param(name = "dealgroupid")
    private Long dealgroupid;

    @FieldDoc(description = "是否需要转换颜色")
    @MobileRequest.Param(name = "convertcolor")
    private Boolean convertcolor;

}
