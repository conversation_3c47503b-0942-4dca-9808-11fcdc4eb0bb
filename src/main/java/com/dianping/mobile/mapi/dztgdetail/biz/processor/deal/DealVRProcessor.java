package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DigestQueryWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.InterestWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.DigestConfinementVrDTO;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.interest.core.thrift.remote.dto.InterestCalculateResponse;
import com.sankuai.sig.botdefender.core.crypt.utils.SigCryptUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2024-11-11-17:22
 */
public class DealVRProcessor extends AbsDealProcessor {

    @Resource
    private InterestWrapper interestWrapper;

    @Resource
    private DigestQueryWrapper digestQueryWrapper;

    private static final List<Integer> CATEGORY_IDS = Lists.newArrayList(1011);

    private static final long VR_INTEREST_CODE = 1000616L;

    @Override
    public boolean isEnable(DealCtx ctx) {
        // 只在APP
        if (ctx.getEnvCtx() == null || ctx.getEnvCtx().isFromH5()) {
            return false;
        }
        // 团单二级类目
        if (!CATEGORY_IDS.contains(ctx.getCategoryId())) {
            return false;
        }
        // 关键属性判断
        return hasKeyAttr(ctx.getDealGroupDTO());
    }

    @Override
    public void prepare(DealCtx ctx) {
        if (ctx.getFutureCtx() == null) {
            return;
        }
        ctx.getFutureCtx().setShopInterestFuture(interestWrapper.preInterestCalculateByShop(ctx, VR_INTEREST_CODE, 1));
        ctx.getFutureCtx().setVrInfoFuture(digestQueryWrapper.getRoomVRFuture(ctx));
    }

    @Override
    public void process(DealCtx ctx) {
        if (Objects.isNull(ctx.getFutureCtx())) {
            return;
        }
        InterestCalculateResponse interestResp = interestWrapper.getFutureResult(ctx.getFutureCtx().getShopInterestFuture());
        // 商户无VR权益,则不展示
        if (interestResp == null || !interestResp.getResult()) {
            return;
        }
        DigestConfinementVrDTO vrInfo = digestQueryWrapper.getVRInfo(ctx.getFutureCtx().getVrInfoFuture(), ctx);
        if (Objects.isNull(vrInfo)) {
            return;
        }
        ctx.setVrUrl(getSplicedVrJumpUrl(vrInfo.getVrUrl(), ctx.getLongPoiId4PFromReq(), ctx));
    }

    private boolean hasKeyAttr(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO == null) {
            return false;
        }
        String roomId = AttributeUtils.getFirstValueV2(dealGroupDTO.getAttrs(), DealAttrKeys.CONFINEMENT_ROOM_ID);
        if (StringUtils.isBlank(roomId)) {
            return false;
        }
        return true;
    }

    private String getSplicedVrJumpUrl(String vrJumpUrl, long poiId, DealCtx ctx) {
        if (ctx.isMt()) {
            return String.format("%s&mtShopId=%s&platform=mt&mtShopIdEncrypt=%s", vrJumpUrl, poiId, SigCryptUtils.encryptPoiId(poiId));
        }
        return String.format("%s&dpShopId=%s&platform=dp&dpShopIdEncrypt=%s", vrJumpUrl, poiId, SigCryptUtils.encryptPoiId(poiId));
    }

}
