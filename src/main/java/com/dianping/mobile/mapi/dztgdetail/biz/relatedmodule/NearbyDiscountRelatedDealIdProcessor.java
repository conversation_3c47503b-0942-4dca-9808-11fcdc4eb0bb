package com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule;

import com.dianping.cat.Cat;
import com.dianping.deal.sales.common.datatype.ProductParam;
import com.dianping.deal.sales.common.datatype.SalesDisplayDTO;
import com.dianping.deal.shop.dto.DealGroupShop;
import com.dianping.gmkt.event.datapools.api.model.seckill.SeckillSceneSimpleDTO;
import com.dianping.lion.client.Lion;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.entity.Response;
import com.dianping.martgeneral.recommend.api.enums.PlatformEnum;
import com.dianping.martgeneral.recommend.api.service.RecommendService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.*;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.RelatedModuleCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.*;
import com.dianping.mobile.mapi.dztgdetail.helper.PriceHelper;
import com.google.common.collect.*;
import com.sankuai.dealuser.price.display.api.enums.*;
import com.sankuai.dealuser.price.display.api.model.*;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 附近优惠团购模块
 * <AUTHOR>
 * @date 2023/5/4 21:21
 */
@Component("nearbyDiscountDeal")
@Slf4j
public class NearbyDiscountRelatedDealIdProcessor extends AbstractRelatedDealIdProcessor {
    private static final String MT_DETAIL_URL_TEMPLATE = "imeituan://www.meituan.com/gc/deal/detail?did=%d&poiid=%d";
    private static final String DP_DETAIL_URL_TEMPLATE = "dianping://tuandeal?shopid=%d&id=%d";
    private static final DecimalFormat df   =new DecimalFormat("0.0");

    @Resource(name = "generalRecommendService")
    private RecommendService recommendService;

    @Autowired
    private PoiShopCategoryWrapper poiShopCategoryWrapper;

    @Autowired
    private QueryCenterWrapper queryCenterWrapper;

    @Resource
    private DealActivityWrapper dealActivityWrapper;

    @Autowired
    private MapperWrapper mapperWrapper;

    @Autowired
    private DealStockSaleWrapper dealStockSaleWrapper;

    @Autowired
    private PriceDisplayWrapper priceDisplayWrapper;

    @Override
    public List<Long> getRelatedDealGroupIds(RelatedModuleCtx ctx) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule.NearbyDiscountRelatedDealIdProcessor.getRelatedDealGroupIds(com.dianping.mobile.mapi.dztgdetail.datatype.context.RelatedModuleCtx)");
        RelatedModuleReq req = ctx.getReq();
        EnvCtx envCtx = ctx.getEnvCtx();
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        DpPoiDTO dpPoiDTO = ctx.getDpPoiDTO();
        if (dealGroupDTO == null || dpPoiDTO == null) {
            return Collections.emptyList();
        }

        Long categoryId = dealGroupDTO.getCategory().getCategoryId();
        Map<String, Integer> categoryId2RecommendBizId = Lion.getMap(LionConstants.APP_KEY,
                LionConstants.CATEGORY_2_RECOMMEND_BIZID, Integer.class, Collections.emptyMap());
        Integer bizId = categoryId2RecommendBizId.get(String.valueOf(categoryId));
        if (bizId == null) {
            return Collections.emptyList();
        }
        Integer shopCategory = poiShopCategoryWrapper.getBackSecondMainCategory(dpPoiDTO);
        if (shopCategory == null) {
            return Collections.emptyList();
        }

        RecommendParameters recommendParameters = new RecommendParameters();
        recommendParameters.setBizId(bizId);
        recommendParameters.setCityId(req.getCityId());
        if (envCtx.isMt()) {
            recommendParameters.setUuid(envCtx.getUuid());
            recommendParameters.setPlatformEnum(PlatformEnum.MT);
            recommendParameters.setOriginUserId(String.valueOf(envCtx.getMtUserId()));//已确认判断平台后再使用
        } else {
            recommendParameters.setDpid(envCtx.getDpId());
            recommendParameters.setPlatformEnum(PlatformEnum.DP);
            recommendParameters.setOriginUserId(String.valueOf(envCtx.getDpUserId()));
        }
        recommendParameters.setLat(req.getUserLat());
        recommendParameters.setLng(req.getUserLng());
        recommendParameters.setPageNumber(req.getStart()/req.getLimit()+1);
        recommendParameters.setPageSize(req.getLimit());
        Map<String, Object> bizParams = new HashMap<>();

        bizParams.put("cat1Id", shopCategory.toString());
        bizParams.put("shopId", req.getShopIdStr());

        recommendParameters.setBizParams(bizParams);
        Response<RecommendResult<RecommendDTO>> resultResponse =  recommendService.recommend(recommendParameters, RecommendDTO.class);
        if (resultResponse != null && resultResponse.getResult() != null) {
            if (ctx.getResult() == null) {
                ctx.setResult(new RelatedDealModuleVO());
            }
            ctx.getResult().setRecordCount(resultResponse.getResult().getTotalSize());
            ctx.getResult().setNextStartIndex(req.getStart() + req.getLimit());
            if (CollectionUtils.isNotEmpty(resultResponse.getResult().getSortedResult())) {
                ctx.getResult().setIsEnd(resultResponse.getResult().getSortedResult().size() < req.getLimit());
                return resultResponse.getResult().getSortedResult().stream()
                        .map(recommendDTO -> Long.valueOf(recommendDTO.getItem()))
                        .collect(Collectors.toList());
            }
            ctx.getResult().setIsEnd(true);
        }

        return Collections.emptyList();
    }

    @Override
    public RelatedDealModuleVO assemble(RelatedModuleCtx ctx, List<Long> dealGroupIdLs) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule.NearbyDiscountRelatedDealIdProcessor.assemble(com.dianping.mobile.mapi.dztgdetail.datatype.context.RelatedModuleCtx,java.util.List)");
        List<Integer> dealGroupIds = CollectionUtils.emptyIfNull(dealGroupIdLs).stream().map(Long::intValue).collect(Collectors.toList());
        RelatedModuleReq req = ctx.getReq();
        EnvCtx envCtx = ctx.getEnvCtx();

        // 关联团单基础信息
        Future dealGroupFuture = getDealGroupFuture(envCtx, dealGroupIds);
        // 销量
        Future preStocksFuture = getStocksFuture(envCtx, ctx, dealGroupIds);
        // 秒杀信息
        Future secKillSceneByMaterialFuture = getSecKillSceneFuture(envCtx, dealGroupIds);
        // 团单ID映射
        BiMap<Integer, Integer> mtDpDIdBiMap = getMtDpDIdBiMap(envCtx, dealGroupIds);
        // 门店信息
        Map<Integer, DealGroupShop> dealGroupShopMap = getDealGroupShopMap(req, ctx, dealGroupIds, mtDpDIdBiMap);
        if (MapUtils.isEmpty(dealGroupShopMap)) {
            return null;
        }
        // 门店ID映射
        List<Long> dpShopIds = dealGroupShopMap.values().stream().map(DealGroupShop::getLongShopId).collect(Collectors.toList());
        Map<Long, List<Long>> mtByDpShopIds = mapperWrapper.getMtByDpShopIds(dpShopIds);
        if (MapUtils.isEmpty(mtByDpShopIds)) {
            return null;
        }
        // 价格优惠
        Future<PriceResponse<Map<Long, List<PriceDisplayDTO>>>> priceResponseFuture
                = getPriceFuture(req, envCtx, dealGroupIds, mtDpDIdBiMap, dealGroupShopMap, mtByDpShopIds);

        List<DealGroupDTO> dealGroupDTOs = queryCenterWrapper.getDealGroupDTOs(dealGroupFuture);
        if (CollectionUtils.isEmpty(dealGroupDTOs)) {
            return null;
        }
        Map<Integer, PriceDisplayDTO> priceDisplayMap = priceDisplayWrapper.getProductMap(priceResponseFuture);
        Map<ProductParam, SalesDisplayDTO> salesDisplayDTOMap = dealStockSaleWrapper.getFutureResult(preStocksFuture);
        Map<Integer, List<SeckillSceneSimpleDTO>> secKillSceneByMaterial = dealActivityWrapper.getSecKillSceneByMaterial(secKillSceneByMaterialFuture);
        Map<Integer, SalesDisplayDTO> productId2SaleMap = new HashMap<>();
        if (MapUtils.isNotEmpty(salesDisplayDTOMap)) {
            for (Map.Entry<ProductParam, SalesDisplayDTO> entry : salesDisplayDTOMap.entrySet()) {
                productId2SaleMap.put((int) entry.getKey().getProductGroupId(), entry.getValue());
            }
        }
        ctx.setDealGroupShopMap(dealGroupShopMap);
        ctx.setMtByDpShopIds(mtByDpShopIds);
        ctx.setPriceDisplayMap(priceDisplayMap);
        ctx.setProductId2SaleMap(productId2SaleMap);
        ctx.setSecKillSceneByMaterial(secKillSceneByMaterial);
        List<DealGroupPBO> deals = new ArrayList<>();
        dealGroupDTOs.forEach(dealGroupDTO -> {
            DealGroupPBO dealGroupPBO = buildDealGroupPBO(ctx, dealGroupDTO);
            if (dealGroupPBO == null) return;
            deals.add(dealGroupPBO);
        });

        ctx.getResult().setDeals(deals);
        Map<String, String> scene2TitleMap = Lion.getMap(LionConstants.APP_KEY,
                LionConstants.RETEADMODULE_SCENE_TITLE, String.class, Collections.emptyMap());
        ctx.getResult().setScene(req.getScene());
        ctx.getResult().setTitle(scene2TitleMap.get(req.getScene()));
        return ctx.getResult();
    }

    private DealGroupPBO buildDealGroupPBO(RelatedModuleCtx ctx, DealGroupDTO dealGroupDTO) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule.NearbyDiscountRelatedDealIdProcessor.buildDealGroupPBO(RelatedModuleCtx,DealGroupDTO)");
        Map<Integer, DealGroupShop> dealGroupShopMap = ctx.getDealGroupShopMap();
        Map<Long, List<Long>> mtByDpShopIds = ctx.getMtByDpShopIds();
        Map<Integer, PriceDisplayDTO> priceDisplayMap = ctx.getPriceDisplayMap();
        Map<Integer, SalesDisplayDTO> productId2SaleMap = ctx.getProductId2SaleMap();
        Map<Integer, List<SeckillSceneSimpleDTO>> secKillSceneByMaterial = ctx.getSecKillSceneByMaterial();
        DealGroupPBO dealGroupPBO = new DealGroupPBO();
        Integer curDealGroupId;
        if (ctx.getEnvCtx().isMt()) {
            dealGroupPBO.setMtId(dealGroupDTO.getMtDealGroupIdInt());
            curDealGroupId = dealGroupDTO.getMtDealGroupIdInt();
        }else {
            dealGroupPBO.setDpId(dealGroupDTO.getDpDealGroupIdInt());
            curDealGroupId = dealGroupDTO.getDpDealGroupIdInt();
        }
        // 头图
        if (dealGroupDTO.getImage() != null && StringUtils.isNotEmpty(dealGroupDTO.getImage().getDefaultPicPath())) {
            dealGroupPBO.setDealContents(Lists.newArrayList(new ContentPBO(1, dealGroupDTO.getImage().getDefaultPicPath())));
        }
        // 标题
        dealGroupPBO.setTitle(dealGroupDTO.getBasic().getTitle());
        // 门店
        DealGroupShop dealGroupShop = dealGroupShopMap.get(dealGroupDTO.getDpDealGroupIdInt());
        if (dealGroupShop == null) {
            return null;
        }
        Long curShopId;
        if (ctx.getEnvCtx().isMt()) {
            List<Long> mtShopIds = mtByDpShopIds.get(dealGroupShop.getLongShopId());
            if (CollectionUtils.isEmpty(mtShopIds)) {
                return null;
            }
            curShopId = mtShopIds.get(0);
        } else {
            curShopId = dealGroupShop.getLongShopId();
        }
        // 团详链接
        if (ctx.getEnvCtx().isMt()) {
            dealGroupPBO.setDetailUrl(String.format(MT_DETAIL_URL_TEMPLATE, curDealGroupId, curShopId));
        } else {
            dealGroupPBO.setDetailUrl(String.format(DP_DETAIL_URL_TEMPLATE, curShopId, curDealGroupId));
        }

        ShopPBO shop = new ShopPBO();
        shop.setShopId(curShopId);
        shop.setShopName(ctx.getReq().getShopIdStr().equals(String.valueOf(curShopId)) ? "同店推荐" : dealGroupShop.getShopName());
        shop.setDistanceDesc(getDistanceDesc(dealGroupShop.getDistance()));
        dealGroupPBO.setShop(shop);
        // 价格
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        PriceDisplayDTO priceDisplayDTO = priceDisplayMap.get(curDealGroupId);
        if (priceDisplayDTO != null) {
            promoDetailModule.setPromoPrice(PriceHelper.formatPrice(priceDisplayDTO.getPrice()));
            promoDetailModule.setMarketPrice(PriceHelper.formatPrice(priceDisplayDTO.getMarketPrice()));
            setMarketPromoDiscount(promoDetailModule, priceDisplayDTO);
            if (priceDisplayDTO.getActivityDTO() != null && PriceDiscountClassifyTypeEnum.COST_EFFECTIVE.getPriceCode()
                    .equals(priceDisplayDTO.getActivityDTO().getDiscountClassifyType())) {
                DealPromoTag dealPromoTag = new DealPromoTag();
                dealPromoTag.setTag("https://p1.meituan.net/travelcube/622269e06ed470d65b8c744fa7927db535073.png");
                dealPromoTag.setType(2);
                promoDetailModule.setPromoTags(Lists.newArrayList(dealPromoTag));
            }
            if (MapUtils.isNotEmpty(secKillSceneByMaterial) && CollectionUtils.isNotEmpty(secKillSceneByMaterial.get(curDealGroupId))) {
                if (StringUtils.isNotBlank(priceDisplayDTO.getPromoTag()) && priceDisplayDTO.getPromoTag().contains("新客")){
                    promoDetailModule.setPromoDesc("秒杀共省" + priceDisplayDTO.getPromoAmount());
                }else {
                    promoDetailModule.setPromoDesc("秒杀" + priceDisplayDTO.getShortPromoTag());
                }
            } else {
                if (StringUtils.isNotBlank(priceDisplayDTO.getPromoTag()) && priceDisplayDTO.getPromoTag().contains("新客")){
                    promoDetailModule.setPromoDesc("补贴共省" + priceDisplayDTO.getPromoAmount());
                }else {
                    promoDetailModule.setPromoDesc("补贴" + priceDisplayDTO.getShortPromoTag());
                }
            }
        } else {
            promoDetailModule.setPromoPrice(dealGroupDTO.getPrice().getSalePrice());
            promoDetailModule.setMarketPrice(dealGroupDTO.getPrice().getMarketPrice());
        }
        dealGroupPBO.setPromoDetailModule(promoDetailModule);
        // 销量
        SalesDisplayDTO salesDisplayDTO = productId2SaleMap.get(curDealGroupId);
        if (salesDisplayDTO != null) {
            dealGroupPBO.setSaleDesc(salesDisplayDTO.getSalesTag());
        }
        return dealGroupPBO;
    }

    private Future<PriceResponse<Map<Long, List<PriceDisplayDTO>>>> getPriceFuture(RelatedModuleReq req, EnvCtx envCtx, List<Integer> dealGroupIds,
                                                                                   BiMap<Integer, Integer> mtDpDIdBiMap, Map<Integer, DealGroupShop> dealGroupShopMap, Map<Long, List<Long>> mtByDpShopIds) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule.NearbyDiscountRelatedDealIdProcessor.getPriceFuture(RelatedModuleReq,EnvCtx,List,BiMap,Map,Map)");
        BatchPriceRequest priceRequest = new BatchPriceRequest();
        ClientEnv clientEnv = new ClientEnv();
        clientEnv.setClientType(envCtx.getClientType());
        clientEnv.setUnionId(envCtx.getUnionId());
        clientEnv.setVersion(envCtx.getVersion());
        priceRequest.setClientEnv(clientEnv);
        priceRequest.setScene(RequestSceneEnum.PROMO_DETAIL_DESC_WithDealPromo.getScene());
        Map<Long, List<ProductIdentity>> shopId2ProductIds = new HashMap<>();
        priceRequest.setLongShopId2ProductIds(shopId2ProductIds);
        if (envCtx.isMt()) {
            dealGroupIds.forEach(mtDealGroupId -> {
                Integer dpDealGroupId = mtDpDIdBiMap.get(mtDealGroupId);
                DealGroupShop dealGroupShop = dealGroupShopMap.get(dpDealGroupId);
                if (dealGroupShop == null) {
                    return;
                }
                ProductIdentity identity = new ProductIdentity(mtDealGroupId, ProductTypeEnum.DEAL.getType());
                List<Long> mtShopIds = mtByDpShopIds.get(dealGroupShop.getLongShopId());
                if (CollectionUtils.isEmpty(mtShopIds)) {
                    return;
                }
                if (shopId2ProductIds.containsKey(mtShopIds.get(0))) {
                    shopId2ProductIds.get(mtShopIds.get(0)).add(identity);
                } else {
                    shopId2ProductIds.put(mtShopIds.get(0), Lists.newArrayList(identity));
                }
            });
            clientEnv.setCityId(req.getCityId());
            clientEnv.setUuid(envCtx.getUuid());
            priceRequest.setUserId(envCtx.getMtUserId());//已确认判断平台后再使用
        } else {
            dealGroupIds.forEach(dealGroupId -> {
                DealGroupShop dealGroupShop = dealGroupShopMap.get(dealGroupId);
                if (dealGroupShop == null) {
                    return;
                }
                ProductIdentity identity = new ProductIdentity(dealGroupId, ProductTypeEnum.DEAL.getType());
                if (shopId2ProductIds.containsKey(dealGroupShop.getLongShopId())) {
                    shopId2ProductIds.get(dealGroupShop.getLongShopId()).add(identity);
                } else {
                    shopId2ProductIds.put(dealGroupShop.getLongShopId(), Lists.newArrayList(identity));
                }
            });
            clientEnv.setCityId(req.getCityId());
            clientEnv.setUuid(envCtx.getDpId());
            priceRequest.setUserId(envCtx.getDpUserId());
        }
        // 猜喜侧的推荐全都是双列货架
        Map<String, String> extension = Maps.newHashMap();
        extension.put(PriceDescEnum.DoubleShelf.getDesc(), String.valueOf(PriceDescEnum.DoubleShelf.getType()));
        // 设置双列货架入参
        priceRequest.setExtension(extension);
        Future<PriceResponse<Map<Long, List<PriceDisplayDTO>>>> priceResponseFuture = priceDisplayWrapper.prepareByRequest(priceRequest);
        return priceResponseFuture;
    }

    private void setMarketPromoDiscount(PromoDetailModule promoDetailModule, PriceDisplayDTO priceDisplayDTO) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule.NearbyDiscountRelatedDealIdProcessor.setMarketPromoDiscount(PromoDetailModule,PriceDisplayDTO)");
        BigDecimal marketPrice = priceDisplayDTO.getMarketPrice();
        BigDecimal finalPrice = priceDisplayDTO.getPrice();
        if (marketPrice == null || finalPrice == null){
            return;
        }
        BigDecimal discountRate = calcDiscountRate(marketPrice, finalPrice);

        String discountRateStr;
        if (discountRate.compareTo(new BigDecimal("9.9")) > 0) {
            discountRateStr = "";
        } else if (discountRate.compareTo(new BigDecimal("0.1")) <= 0) {
            discountRateStr = "0.1折";
        } else {
            discountRateStr = discountRate + "折";
        }
        promoDetailModule.setMarketPromoDiscount(discountRateStr);
        promoDetailModule.setPlainMarketPromoDiscount(discountRateStr);
    }
    /**
     * 计算折扣率（保留一位小数 + 向上取整）
     */
    private static BigDecimal calcDiscountRate(BigDecimal marketPrice, BigDecimal finalPrice) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule.NearbyDiscountRelatedDealIdProcessor.calcDiscountRate(java.math.BigDecimal,java.math.BigDecimal)");
        if (marketPrice == null || finalPrice == null || marketPrice.compareTo(BigDecimal.ZERO) == 0
                || finalPrice.compareTo(BigDecimal.ZERO) == 0 || finalPrice.compareTo(marketPrice) == 0
                || marketPrice.compareTo(finalPrice) < 0) {
            return null;
        }
        return finalPrice.divide(marketPrice, 2, RoundingMode.CEILING).multiply(new BigDecimal(10)).setScale(1, RoundingMode.CEILING);
    }

    private static String getDistanceDesc(String distance) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule.NearbyDiscountRelatedDealIdProcessor.getDistanceDesc(java.lang.String)");
        if (StringUtils.isEmpty(distance)) {
            return null;
        }
        int y = Integer.parseInt(distance);
        if(y <= 1000){
            return y + "m";
        }else if(y <= 100000){
            return df.format(y / 1000.0) + "km";
        }else{
            return ">100km";
        }
    }
}
