package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.annotation.MobileRequest.Param;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/12/13.
 */
@Data
@TypeDoc(description = "团单促销模块请求参数")
@MobileRequest
public class UnifiedGetCouponReq implements IMobileRequest {

    @Deprecated
    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，原int类型")
    @Param(name = "dealgroupid")
    private Integer dealGroupId;

    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，string类型")
    @Param(name = "stringdealgroupid")
    private String stringDealGroupId;

    @FieldDoc(description = "团单城市ID", rule = "美团平台为美团城市ID，点评平台为点评城市ID")
    @Param(name = "cityid")
    private Integer cityId;

    @FieldDoc(description = "商家券ID", rule = "商家券加密ID")
    @Param(name = "coupongroupid")
    private String couponGroupId;

    @Deprecated
    @FieldDoc(description = "商户ID", rule = "商户ID")
    @Param(name = "shopid", shopuuid = "shopuuid")
    private Long shopId;

    @FieldDoc(description = "商户UUID", rule = "商户UUID")
    @Param(name = "shopuuid")
    private String shopUuid;

    @FieldDoc(description = "商户ID", rule = "商户ID")
    @Param(name = "shopidstr")
    private String shopIdStr;

    @FieldDoc(description = "诚信字段", rule = "诚信字段")
    @Param(name = "cx")
    private String cx;

    @FieldDoc(description = "用户键鼠信息")
    @Param(name = "risktoken")
    private String riskToken;

    @FieldDoc(description = "客户端平台ID")
    @Param(name = "clienttype")
    private Integer clientType;

    public Integer getDealGroupId() {
        return dealGroupId != null ? dealGroupId : 0;
    }

    public Integer getCityId() {
        return cityId != null ? cityId : 0;
    }

    public Integer getShopId() {
        if(shopId == null) {
            return 0;
        }
        if(shopId > Integer.MAX_VALUE) {
            return 0;
        }
        return shopId.intValue();
    }

    public Integer getClientType() {
        return clientType != null ? clientType : 0;
    }

    public long getShopIdLong() {
        if(StringUtils.isNumeric(shopIdStr)) {
            return Long.parseLong(shopIdStr);
        } else if(shopId != null) {
            return shopId.longValue();
        }
        return 0L;
    }
}
