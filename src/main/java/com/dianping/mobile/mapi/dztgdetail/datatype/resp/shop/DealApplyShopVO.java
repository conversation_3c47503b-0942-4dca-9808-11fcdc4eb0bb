package com.dianping.mobile.mapi.dztgdetail.datatype.resp.shop;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-08-10
 * @desc 团单适用门店模型
 */
@Data
@MobileDo(id = 0x8ce44d11)
public class DealApplyShopVO implements Serializable {
    /**
     * 点评门店id
     */
    @MobileDo.MobileField(key = 0x1262)
    private long dpShopId;

    /**
     * 美团门店id
     */
    @MobileDo.MobileField(key = 0x19fa)
    private long mtShopId;

    public DealApplyShopVO() {
    }

    public DealApplyShopVO(long dpShopId, long mtShopId) {
        this.dpShopId = dpShopId;
        this.mtShopId = mtShopId;
    }

}
