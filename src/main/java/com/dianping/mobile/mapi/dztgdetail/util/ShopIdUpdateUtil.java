package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.cat.Cat;
import com.meituan.mtrace.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2021-09-07-3:53 下午
 */
@Slf4j
public class ShopIdUpdateUtil {

    public static Long transferStrToLong(Integer shopId, String shopIdStr, String methodName) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.util.ShopIdUpdateUtil.transferStrToLong(java.lang.Integer,java.lang.String,java.lang.String)");
        try {
            if (StringUtils.isNotBlank(shopIdStr)) {
                return Long.parseLong(shopIdStr);
            }
            Cat.logEvent("Req_poiIdInt2LongUndo", String.format("%s", methodName));
            return shopId.longValue();
        } catch (Exception e) {
            log.error("transferStrToInteger error, shopId: {}, shopIdStr: {}", shopId, shopIdStr, e);
            Cat.logEvent("Req_poiIdInt2LongUndoError", methodName);
            return shopId.longValue();
        }
    }

}
