package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
@Data
@MobileDo(id = 0xcc64)
public class DealRelatedBehaviorModule implements Serializable {

    /**
     * 关联用户行为列表
     */
    @MobileField(key = 0x441f)
    private List<DealRelatedBehaviorItem> relatedUserBehaviorItems;

}
