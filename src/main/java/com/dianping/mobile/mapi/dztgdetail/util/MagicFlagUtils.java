package com.dianping.mobile.mapi.dztgdetail.util;

/**
 * 神会员生效标识工具类
 * https://km.sankuai.com/collabpage/**********
 */
public class MagicFlagUtils {

    private static final int VACANCY = 0;
    private static final int EFFECTIVE = 1;
    private static final int INEFFECTIVE = 2;

    /**
     * 神券可用
     *
     * @return
     */
    public static boolean canUse(Integer magicFlag) {
        return magicFlag != null && magicFlag == EFFECTIVE;
    }

    /**
     * 神券可膨
     *
     * @return
     */
    public static boolean canInflate(Integer magicFlag) {
        return magicFlag != null && magicFlag == EFFECTIVE;
    }

    /**
     * 券包可买
     *
     * @return
     */
    public static boolean canBuy(Integer magicFlag) {
        return magicFlag != null && magicFlag == EFFECTIVE;
    }

    /**
     * 是否有效值
     *
     * @return
     */
    public static boolean isValid(Integer magicFlag) {
        return magicFlag != null && (magicFlag == EFFECTIVE || magicFlag == INEFFECTIVE);
    }

    public static String toString(Integer magicFlag) {
        if (magicFlag == null || magicFlag == 0) {
            return "";
        }
        return String.valueOf(magicFlag);
    }

}
