package com.dianping.mobile.mapi.dztgdetail.common.enums;

import com.sankuai.general.product.query.center.client.enums.CombineTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: created by hang.yu on 2024/6/11 21:04
 */
@Getter
@AllArgsConstructor
public enum CategoryCombineTypeEnum {

    BATH(304, CombineTypeEnum.ENTERTAINMENT_BATH_ADDING_ITEM),

    PHYSICAL(401, CombineTypeEnum.PHYSICAL_EXAMINATION_ADDING_ITEM);

    /**
     * 团单分类id
     */
    private final int categoryId;

    /**
     * 组合类型枚举
     */
    private final CombineTypeEnum combineTypeEnum;

    public static CombineTypeEnum getEnumByCategoryId(int categoryId) {
        for (CategoryCombineTypeEnum categoryCombineTypeEnum : CategoryCombineTypeEnum.values()) {
            if (categoryCombineTypeEnum.getCategoryId() == categoryId) {
                return categoryCombineTypeEnum.getCombineTypeEnum();
            }
        }
        // 默认返回体检
        return CombineTypeEnum.PHYSICAL_EXAMINATION_ADDING_ITEM;
    }


}
