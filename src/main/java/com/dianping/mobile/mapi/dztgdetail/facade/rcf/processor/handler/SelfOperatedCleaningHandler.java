package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.handler;

import com.dianping.mobile.mapi.dztgdetail.common.constants.Constants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.google.common.collect.Lists;
import com.sankuai.fbi.lifeevent.reserverpcapi.dto.NewReserveSubmissionPageWhiteShopCheckRespDTO;
import com.sankuai.fbi.lifeevent.reserverpcapi.request.NewReserveSubmissionPageWhiteShopCheckRequest;
import com.sankuai.fbi.lifeevent.reserverpcapi.service.ReserveConfigQueryService;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/11/11 17:32
 */
@Service
@Slf4j
public class SelfOperatedCleaningHandler implements BaseReserveMaintenanceHandler {

    @Resource(name = "reserveConfigQueryService")
    private ReserveConfigQueryService reserveConfigQueryService;

    private static final String MT_KEY = "MtSelfOperatedCleanReserveExp";
    private static final String DP_KEY = "DpSelfOperatedCleanReserveExp";
    private static final List<String> SELF_OPERATED_CLEAN_RESERVE_AB_KEYS = Lists.newArrayList(MT_KEY, DP_KEY);

    @Override
    public int getDealSecondCategory() {
        return 409;
    }

    @Override
    public String getExpName(boolean isMt) {
        return isMt ? MT_KEY : DP_KEY;
    }

    @Override
    public String getExpName(DealCtx ctx) {
        if (ctx == null) {
            return null;
        }
        if (DealCtxHelper.isPreOrderDeal(ctx)) {
            return ctx.isMt() ? Constants.MT_PRE_ORDER_EXP : Constants.DP_PRE_ORDER_EXP;
        }
        return getExpName(ctx.isMt());
    }

    @Override
    public List<String> getAbKeys() {
        if (LionConfigUtils.hitSelfOperatedCleaningNewReserveDegradeSwitch()) {
            return null;
        }
        return SELF_OPERATED_CLEAN_RESERVE_AB_KEYS;
    }

    @Override
    public List<String> getSpecialAbKeys() {
        return Lists.newArrayList(Constants.MT_PRE_ORDER_EXP, Constants.DP_PRE_ORDER_EXP);
    }

    @Override
    public boolean isEnable(DealCtx ctx) {
        return isSelfOperatedCleaningDeal(ctx) || DealCtxHelper.isPreOrderDeal(ctx);
    }

    public boolean isSelfOperatedCleaningDeal(DealCtx ctx) {
        if (ctx == null || ctx.getEnvCtx() == null) {
            return false;
        }

        long mtDealGroupId = Optional.of(ctx).map(DealCtx::getDealGroupDTO).map(DealGroupDTO::getMtDealGroupId)
                .orElse(0L);
        Long dpShopId = Optional.of(ctx).map(DealCtx::getDpLongShopId).orElse(0L);
        try {
            // 还有个命中指定门店的判断逻辑,stl提供接口根据结果返回对应的结果
            NewReserveSubmissionPageWhiteShopCheckRequest req = new NewReserveSubmissionPageWhiteShopCheckRequest();
            // 入参固定传点评shopId
            req.setDpShopId(dpShopId);
            NewReserveSubmissionPageWhiteShopCheckRespDTO respDTO = reserveConfigQueryService
                    .checkIsNewReserveSubmissionPageWhiteShop(req);
            if (respDTO == null || !respDTO.getCheckRes()) {
                return false;
            }

            // 双端APP  + 门店在白名单里面
            boolean mainApp = ctx.getEnvCtx().judgeMainApp();
            return mainApp && respDTO.getCheckRes();
        } catch (Exception e) {
            log.error("SelfOperatedCleaningHandler isEnable error,mtDealGroupId:{},dpShopId:{}", mtDealGroupId,
                    dpShopId, e);
        }
        return false;
    }

    @Override
    public boolean isHitAbTest(AbConfig abConfig) {
        if (abConfig == null) {
            return false;
        }
        return "c".equals(abConfig.getExpResult()) || "b".equals(abConfig.getExpResult());
    }
}
