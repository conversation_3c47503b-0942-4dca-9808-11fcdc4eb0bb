package com.dianping.mobile.mapi.dztgdetail.common;

import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.helper.AppCtxHelper;
import com.sankuai.pearl.framework.service.PearlShopService;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class PearlShopServiceImpl implements PearlShopService {
    /**
     * 按当前协议上线文判断是否进行 shop uuid 到 shop id 的转换。
     * 如果当前不是美团 App 则接入当前改造方案。
     *
     * @param context
     * @return
     */
    @Override
    public boolean isShopUuidToShopId(IMobileContext context) {
        return !AppCtxHelper.isMeituanClient(context);
    }
}
