package com.dianping.mobile.mapi.dztgdetail.biz.processor.mtlive;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PrivateLiveWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.LiveRoomDetail;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @date 2023-12-29
 * @desc 私域直播直播间信息查询
 */
public class PrivateLiveProcessor extends AbsDealProcessor {

    @Resource
    private PrivateLiveWrapper privateLiveWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        return ctx.isMtLiveMinApp();
    }

    @Override
    public void prepare(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.processor.mtlive.PrivateLiveProcessor.prepare(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        DealBaseReq req = ctx.getDealBaseReq();
        if (Objects.isNull(req)) {
            return;
        }
        Future future = privateLiveWrapper.preGetLiveRoomDetail(req.getPrivateLiveId());
        ctx.getFutureCtx().setPrivateLiveFuture(future);
    }

    @Override
    public void process(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.mtlive.PrivateLiveProcessor.process(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        LiveRoomDetail liveRoomDetail = privateLiveWrapper.getFutureResult(ctx.getFutureCtx().getPrivateLiveFuture());
        if (Objects.isNull(liveRoomDetail)) {
            return;
        }
        ctx.setPrivateLiveRoomInfo(liveRoomDetail);
    }
}
