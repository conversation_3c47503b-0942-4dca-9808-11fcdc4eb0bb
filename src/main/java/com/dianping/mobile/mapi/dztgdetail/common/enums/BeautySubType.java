package com.dianping.mobile.mapi.dztgdetail.common.enums;

import lombok.Getter;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/6/17.
 */

@Getter
public enum BeautySubType {
    //舞蹈/瑜伽(DP)
    DANCEORYOGA(5507, "beauty_danceoryoga"),
    //其它(DP)
    ORTHERS(5505, "beauty_others"),
    //美容美体SPA(DP)
    SPA(5503, "beauty_SPA"),
    //美甲/美睫(DP)
    BEAUTIFYNAILOREYELASH(5502, "beauty_nail"),
    //美发(DP)
    BEAUTIFYHAIRDP(5502, "beauty_hair"),
    BEAUTIFYHAIRDP1(5501, "beauty_hair"),
    //瑜伽/普拉提/健身操/形体
    YOGAORBODYCARE(81, "beauty_yogaorbodycare"),
    //其他美容美体
    OTHERBODYCARE(50, "beauty_otherbodycare"),
    //化妆
    MAKEUP(47, "beauty_makeup"),
    //脱毛/塑身/整容
    SHEDORCOSMETIC(44, "beauty_shedorcosmetic"),
    //SPA/美容/美体
    SPAORBODYCARE(42, "beauty_spaorbodycare"),
    //美甲/手护
    BEAUTIFYNAILORHANDS(39, "beauty_nail"),
    //美发
    BEAUTIFYHAIR(38, "beauty_hair"),
    BEAUTIDEAFAULT(0, "beauty_default");
    private int subTypeId;
    private String subTypeName;

    public static BeautySubType findSubTypeName(int subTypeId) {
        BeautySubType beautySubType = BeautySubType.BEAUTIDEAFAULT;
        for (BeautySubType subType : BeautySubType.values()) {
            if (subType.getSubTypeId() == subTypeId) {
                beautySubType = subType;
                break;
            }
        }
        return beautySubType;
    }

    BeautySubType(int subTypeId, String subTypeName) {
        this.subTypeId = subTypeId;
        this.subTypeName = subTypeName;
    }
}