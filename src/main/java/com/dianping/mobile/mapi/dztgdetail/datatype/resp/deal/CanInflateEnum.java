package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2024/3/20
 * @since mapi-dztgdetail-web
 */
public enum CanInflateEnum {

    CAN_NOT_INFLATE(0, "不可膨胀"),
    CAN_INFLATE(1, "可膨胀");


    final private int code;
    final private String name;

    CanInflateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

}
