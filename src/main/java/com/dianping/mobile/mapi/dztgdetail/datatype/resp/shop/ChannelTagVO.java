package com.dianping.mobile.mapi.dztgdetail.datatype.resp.shop;

import lombok.Data;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;

import java.io.Serializable;


/**
 * zdc标签模型-样式由后端控制
 */
@Data
public class ChannelTagVO implements Serializable {

    /**
     * 标签类型
     */
    @MobileField(key = 0xc658)
    private int tagType;

    /**
     * 标签高度
     */
    @MobileField(key = 0xf5f2)
    private double tagHeight;

    /**
     * 标签之间的边距
     */
    @MobileField(key = 0x6780)
    private double tagMargins;

    /**
     * 标签四个角圆角
     */
    @MobileField(key = 0x27ce)
    private RoundCornerRadius roundCornerRadius;

    /**
     * 边框宽度
     */
    @MobileField(key = 0xa8d6)
    private double borderWidth;

    /**
     * 边框颜色
     */
    @MobileField(key = 0xad82)
    private String borderColor;

    /**
     * 文本类型字段信息
     */
    @MobileField(key = 0xb84b)
    private TagTextVO tagText;

    /**
     * Icon类型字段信息，左边icon
     */
    @MobileField(key = 0x9347)
    private TagIconsVO leftIcon;

    /**
     * icon类型字段信息，右边icon
     */
    @MobileField(key = 0xa6c1)
    private TagIconsVO rightIcon;

    /**
     * 标签跳转链接
     */
    @MobileField(key = 0x774e)
    private String jumpUrl;

    /**
     * 标签背景颜色
     */
    @MobileField(key = 0x2e94)
    private String backGroundColor;
}
