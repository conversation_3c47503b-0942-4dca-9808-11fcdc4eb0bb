package com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/12/13.
 */
@Data
@TypeDoc(description = "优惠信息")
@MobileDo(id = 0xca69)
public class PromoInfoItem implements Serializable {

    @FieldDoc(description = "优惠标题，立减/抵用券/反礼")
    @MobileField(key = 0x83f6)
    private String promoTitle;

    @FieldDoc(description = "样式类型，0：文案型(取promoInfoDescItems) 1：抵用券TableView(取couponInfoDescItems)")
    @MobileField(key = 0x1b3a)
    private int style;

    @FieldDoc(description = "优惠信息描述项列表")
    @MobileField(key = 0x2b40)
    private List<PromoInfoDescItem> promoInfoDescItems;

    @FieldDoc(description = "抵用券描述项列表")
    @MobileField(key = 0x1ddb)
    private List<CouponDescItem> couponDescItems;

    @FieldDoc(description = "跳转链接")
    @MobileField(key = 0x8283)
    private String redirectUrl;

    @FieldDoc(description = "1是立减，2是拼团，3是抵用券，4是反礼，5是红包分享, 6是闲时优惠")
    @MobileField(key = 0x8f0c)
    private int type;

    @FieldDoc(description = "小图标URL")
    @MobileField(key = 0xf39b)
    private String iconUrl;

    @FieldDoc(description = "引导文案，e.g. 去领券/查看优惠/更多优惠")
    @MobileField(key = 0x17aa)
    private String leadText;

    @FieldDoc(description = "引导动作，0：无引导 1：弹出浮层 2：跳转leadUrl 3: 弹出闲时单浮层")
    @MobileField(key = 0x1c63)
    private int leadAction;

    @FieldDoc(description = "引导跳转链接")
    @MobileField(key = 0x2373)
    private String leadUrl;
}
