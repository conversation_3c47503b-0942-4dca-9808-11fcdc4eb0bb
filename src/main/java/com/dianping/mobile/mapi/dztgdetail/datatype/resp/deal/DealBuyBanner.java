package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 2020/3/17 11:28 上午
 */
@Data
@MobileDo
public class DealBuyBanner implements Serializable {

    /**
     * 横幅右侧箭头图片url
     */
    @MobileDo.MobileField(key = 0x90f4)
    private String arrowIconUrl;

    /**
     * icon url
     */
    @MobileDo.MobileField(key = 0xf39b)
    private String iconUrl;

    /**
     * 横幅图标高
     */
    @MobileDo.MobileField(key = 0x75c3)
    private int iconHeight;

    /**
     * 横幅图标宽
     */
    @MobileDo.MobileField(key = 0x4864)
    private int iconWidth;

    /**
     * 横幅内容，jsonlabel格式
     */
    @MobileDo.MobileField(key = 0xcce)
    private String content;
    /**
     * 引导类型
     *
     * @see com.dianping.mobile.mapi.dztgdetail.common.enums.LeadActionEnum
     */
    @MobileDo.MobileField(key = 0x1c63)
    private int leadAction;
    /**
     * 引导URL
     */
    @MobileDo.MobileField(key = 0x2373)
    private String leadUrl;
    /**
     * 引导文案
     */
    @MobileDo.MobileField(key = 0x17aa)
    private String leadText;
    /**
     * 是否展示
     */
    @MobileDo.MobileField(key = 0xdac8)
    private boolean show;
    /**
     * 浮层内容
     */
    @MobileDo.MobileField(key = 0x7f56)
    private DealBuyBannerDetail bannerDetail;

    /**
     * banner 类型   BannerTypeEnum
     */
    @MobileDo.MobileField(key = 0xce04)
    private int bannerType;

    /**
     * 打点信息
     */
    @MobileDo.MobileField(key = 0x93ac)
    private CardDotInfo cardDotInfo;

    @FieldDoc(description = "倒计时时间戳")
    @MobileDo.MobileField(key = 0xddb1)
    private Long countDownTs;

    @FieldDoc(description = "文案颜色")
    @MobileDo.MobileField(key = 0x2e94)
    private String leadTextColor;

    @FieldDoc(description = "背景颜色")
    @MobileDo.MobileField(key = 0x81f8)
    private List<String> backGroundColor;
}
