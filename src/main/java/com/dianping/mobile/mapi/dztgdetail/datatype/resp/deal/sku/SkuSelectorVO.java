package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.sku;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

@MobileDo(id = 0xe7de)
public class SkuSelectorVO implements Serializable {
    @MobileDo.MobileField(key = 0x8cdc)
    private List<SalesAttrToSkuBasicInfoDO> salesAttrToSkuBasicInfo;

    @MobileDo.MobileField(key = 0xd03f)
    private List<SkuSalesAttrDO> skuSalesAttrInfo;

    public List<SalesAttrToSkuBasicInfoDO> getSalesAttrToSkuBasicInfo() {
        return salesAttrToSkuBasicInfo;
    }

    public void setSalesAttrToSkuBasicInfo(
            List<SalesAttrToSkuBasicInfoDO> salesAttrToSkuBasicInfo) {
        this.salesAttrToSkuBasicInfo = salesAttrToSkuBasicInfo;
    }

    public List<SkuSalesAttrDO> getSkuSalesAttrInfo() {
        return skuSalesAttrInfo;
    }

    public void setSkuSalesAttrInfo(List<SkuSalesAttrDO> skuSalesAttrInfo) {
        this.skuSalesAttrInfo = skuSalesAttrInfo;
    }
}
