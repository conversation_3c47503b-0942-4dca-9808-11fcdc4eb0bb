package com.dianping.mobile.mapi.dztgdetail.facade.rcf.facade;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.lion.Environment;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.ProcessHandler;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ThreadPoolNameEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.Response;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealStyleBO;
import com.dianping.mobile.mapi.dztgdetail.facade.DealQueryFacade;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.exception.DealResponseBuilderException;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.deal.executor.ResultPostProcessHandler;
import com.dianping.mobile.mapi.dztgdetail.flowdye.FlowDyeExtImpl;
import com.dianping.mobile.mapi.dztgdetail.mq.TrafficDiffSamplingService;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.NumbersUtils;
import com.dianping.mobile.mapi.dztgdetail.util.ThreadPoolUtils;
import com.google.common.collect.Lists;
import com.sankuai.nibpt.transparentvalidator.domain.TransparentValidatorParam;
import com.sankuai.nibpt.transparentvalidator.enums.PlatformEnum;
import com.sankuai.nibpt.transparentvalidator.util.TransparentValidatorUtils;
import com.sankuai.nibscp.common.flow.identify.anno.FlowDyeAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2023/8/16
 * @since mapi-dztgdetail-web
 */
@Slf4j
@Component
public class DealQueryParallFacade {

    private static final Logger LOGGER = LoggerFactory.getLogger(DealQueryParallFacade.class);

    @Autowired
    private DealQueryFacade dealQueryFacade;


    @Autowired
    @Qualifier("dealStyleBaseQueryHandler")
    private ProcessHandler<DealCtx> dealStyleBaseQueryHandler;

    @Autowired
    @Qualifier("dealStyleOtherQueryHandler")
    private ProcessHandler<DealCtx> dealStyleOtherQueryHandler;

    @Autowired
    @Qualifier("dealPriceQueryHandler")
    private ProcessHandler<DealCtx> dealPriceQueryHandler;

    @Autowired
    @Qualifier("dealPriceOtherQueryHandler")
    private ProcessHandler<DealCtx> dealPriceOtherQueryHandler;

    @Autowired
    @Qualifier("dealPromoBaseQueryHandler")
    private ProcessHandler<DealCtx> dealPromoBaseQueryHandler;

    @Autowired
    @Qualifier("dealPromoOtherQueryHandler")
    private ProcessHandler<DealCtx> dealPromoOtherQueryHandler;

    @Autowired
    @Qualifier("parallDealBuilderHandler")
    private ProcessHandler<DealCtx> parallDealBuilderHandler;

    @Resource
    private TrafficDiffSamplingService samplingService;

    public static final ExecutorService STYLE_EXECUTOR_TRACE_WRAPPER = ThreadPoolUtils.getCatExecutor(ThreadPoolNameEnum.STYLE_EXECUTOR);
    public static final ExecutorService PRICE_EXECUTOR_TRACE_WRAPPER = ThreadPoolUtils.getCatExecutor(ThreadPoolNameEnum.PRICE_EXECUTOR);
    public static final ExecutorService PROMO_EXECUTOR_TRACE_WRAPPER = ThreadPoolUtils.getCatExecutor(ThreadPoolNameEnum.PROMO_EXECUTOR);

    public static final String DEAL_QUERY_RCF = "DealQueryRcf";

    private static final String DEAL_QUERY_RCF_PRICE = "DealQueryRcfPrice";
    private static final String DEAL_QUERY_RCF_STYLE = "DealQueryRcfStyle";
    private static final String DEAL_QUERY_RCF_PROMO = "DealQueryRcfPromo";

    @FlowDyeAnnotation(extClass = FlowDyeExtImpl.class) //特别注意：方法入参改动时（包括参数的类型、变量名、包含的字段名等）需要兼容FlowDyeExtImpl，FlowDyeExtImpl用反射做了一些逻辑！！！
    public Response<DealGroupPBO> queryDealGroup(DealBaseReq req, EnvCtx envCtx, Map<String, String> headerMap) {
        CompletableFuture<DealCtx> priceCf = CompletableFuture.supplyAsync(() -> doPriceCf(req, envCtx), PRICE_EXECUTOR_TRACE_WRAPPER);
        CompletableFuture<DealCtx> styleCf = CompletableFuture.supplyAsync(() -> doStyleCf(req, envCtx), STYLE_EXECUTOR_TRACE_WRAPPER);
        CompletableFuture<DealCtx> promoCf = CompletableFuture.supplyAsync(() -> doPromoCf(req, envCtx), PROMO_EXECUTOR_TRACE_WRAPPER);
        CompletableFuture.allOf(styleCf, priceCf, promoCf).join();
        return doBuildResponse(req, envCtx, priceCf, styleCf, promoCf, headerMap);
    }

    public @Nullable Response<DealGroupPBO> doBuildResponse(DealBaseReq req, EnvCtx envCtx,
                                                            CompletableFuture<DealCtx> priceCf,
                                                            CompletableFuture<DealCtx> styleCf,
                                                            CompletableFuture<DealCtx> promoCf,
                                                            Map<String, String> headerMap) {
        Transaction transaction = Cat.newTransaction(DEAL_QUERY_RCF, "builder");
        try {
            //dealCtx组装
            DealCtx dealCtx = buildDealCtx(priceCf.get(), styleCf.get(), promoCf.get());
            //团单结果组装
            parallDealBuilderHandler.preThenProc(dealCtx);

            //设置合规
            dealQueryFacade.setHeguiNotice(dealCtx);

            // 填充直播浮窗开关
            dealQueryFacade.fillBusinessFigure(dealCtx);

            // 处理标准团单
            dealQueryFacade.processStandardDealGroup(dealCtx);

            // 团单返回值后置处理
            postProcessResult(dealCtx);

            // 上报点位校验
            reportValidateParams(req, envCtx, dealCtx);

            // 流量DIFF采样
            samplingService.sendMessage(req, envCtx, dealCtx, headerMap);

            transaction.setStatus(Transaction.SUCCESS);
            return Response.createSuccessResponse(dealCtx.getResult());
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("DealQueryParallFacade.doBuildResponse,req:{}", JSON.toJSONString(req), e);
            Cat.logError(new DealResponseBuilderException(e));
            return null;
        } finally {
            transaction.complete();
        }
    }

    @FlowDyeAnnotation(extClass = FlowDyeExtImpl.class) //特别注意：方法入参改动时（包括参数的类型、变量名、包含的字段名等）需要兼容FlowDyeExtImpl，FlowDyeExtImpl用反射做了一些逻辑！！！
    public Response<DealStyleBO> queryDealGroupStyle(DealBaseReq req, EnvCtx envCtx) {
        DealStyleBO result = new DealStyleBO();
        try {
            DealCtx dealCtx = doStyleCf(req, envCtx);
            result.setModuleConfigsModule(dealCtx.getModuleConfigsModule());
            return Response.createSuccessResponse(result);
        } catch (Exception e) {
            Cat.logError(e);
        }
        return  null;
    }


    private DealCtx buildDealCtx(DealCtx priceCf, DealCtx styleCf, DealCtx promoCf) {
        priceCf.setUrlProcessorDztgClientList(LionConfigUtils.getUrlProcessorDztgClientList());
        priceCf.setMtTemplateKey(styleCf.getMtTemplateKey());
        priceCf.setDealStyle(styleCf.getDealStyle());
        priceCf.setModuleConfigsModule(styleCf.getModuleConfigsModule());
        priceCf.setBusinessStyle(styleCf.getBusinessStyle());
        priceCf.setBatchExProxyCouponResponseDTO(promoCf.getBatchExProxyCouponResponseDTO());
        if(CollectionUtils.isNotEmpty(priceCf.getDealExtraTypes())) {
            if(CollectionUtils.isNotEmpty(styleCf.getDealExtraTypes())) {
                priceCf.getDealExtraTypes().addAll(styleCf.getDealExtraTypes());
            }
            if(CollectionUtils.isNotEmpty(promoCf.getDealExtraTypes())) {
                priceCf.getDealExtraTypes().addAll(promoCf.getDealExtraTypes());
            }
        } else {
            List<String> list = Lists.newArrayList();
            if(CollectionUtils.isNotEmpty(styleCf.getDealExtraTypes())) {
                list.addAll(styleCf.getDealExtraTypes());
            }
            if(CollectionUtils.isNotEmpty(promoCf.getDealExtraTypes())) {
                list.addAll(promoCf.getDealExtraTypes());
            }
            priceCf.setDealExtraTypes(list);
        }
        // 私域直播间信息
        priceCf.setPrivateLiveRoomInfo(styleCf.getPrivateLiveRoomInfo());
        // 私域直播分销信息
        priceCf.setLiveRoomDistributionInfo(styleCf.getLiveRoomDistributionInfo());
        // 洗涤履约保障
        priceCf.setLEInsuranceAgreementEnum(styleCf.getLEInsuranceAgreementEnum());

        return priceCf;
    }

    public DealCtx doStyleCf(DealBaseReq req, EnvCtx envCtx) {
        Transaction trx = Cat.newTransaction(DEAL_QUERY_RCF, "doStyleCf");
        DealCtx dealCtx = dealQueryFacade.initDealsBaseData(req, envCtx);
        Transaction trx1 = Cat.newTransaction(DEAL_QUERY_RCF_STYLE, "dealStyleQueryHandler");
        dealStyleBaseQueryHandler.preThenProc(dealCtx);
        trx1.complete();
        Transaction trx2 = Cat.newTransaction(DEAL_QUERY_RCF_STYLE, "dealStyleOtherQueryHandler");
        dealStyleOtherQueryHandler.preThenProc(dealCtx);
        trx2.complete();
        trx.complete();
        return dealCtx;
    }

    public DealCtx doPriceCf(DealBaseReq req, EnvCtx envCtx) {
        Transaction trx = Cat.newTransaction(DEAL_QUERY_RCF, "doPriceCf");
        DealCtx dealCtx = dealQueryFacade.initDealsBaseData(req, envCtx);
        Transaction trx1 = Cat.newTransaction(DEAL_QUERY_RCF_PRICE, "dealPriceQueryHandler");
        dealPriceQueryHandler.preThenProc(dealCtx);
        trx1.complete();
        dealQueryFacade.processMarketPriceDisplayStatus(req, dealCtx);
        Transaction trx2 = Cat.newTransaction(DEAL_QUERY_RCF_PRICE, "dealPriceOtherQueryHandler");
        dealPriceOtherQueryHandler.preThenProc(dealCtx);
        trx2.complete();
        trx.complete();
        return dealCtx;
    }

    public DealCtx doPromoCf(DealBaseReq req, EnvCtx envCtx) {
        Transaction trx = Cat.newTransaction(DEAL_QUERY_RCF, "doPromoCf");
        DealCtx dealCtx = dealQueryFacade.initDealsBaseData(req, envCtx);
        Transaction trx1 = Cat.newTransaction(DEAL_QUERY_RCF_PROMO, "dealPromoQueryHandler");
        dealPromoBaseQueryHandler.preThenProc(dealCtx);
        trx1.complete();
        Transaction trx2 = Cat.newTransaction(DEAL_QUERY_RCF_PROMO, "dealPromoOtherQueryHandler");
        dealPromoOtherQueryHandler.preThenProc(dealCtx);
        trx2.complete();
        trx.complete();
        return dealCtx;
    }

    /**
     * 团单返回值后置处理
     */
    @SuppressWarnings("unchecked")
    public void postProcessResult(DealCtx dealCtx) {
        if(dealCtx == null || dealCtx.getResult() == null){
            return;
        }
        ResultPostProcessHandler.getInstance().execute(dealCtx.getResult(), dealCtx);
    }

    /**
     * 点位参数上报
     * @param request 主接口入参
     * @param envCtx 环境上下文
     */
    private void reportValidateParams(DealBaseReq request, EnvCtx envCtx, DealCtx dealCtx) {
        try {
            long userId = envCtx.isMt() ? envCtx.getMtUserId() : envCtx.getDpUserId();
            if (TransparentValidatorUtils.needReport(userId)) {
                TransparentValidatorParam.TransparentValidatorParamBuilder builder = TransparentValidatorParam.builder()
                        .methodName(envCtx.getRequestURI())
                        .cPosition(dealCtx.getPosition())
                        .bizLine(40)
                        .mtRealUserId(envCtx.getMtUserId())
                        .dpRealUserId(envCtx.getDpUserId())
                        .mtVirtualUserId(envCtx.getMtVirtualUserId())
                        .dpId(envCtx.isMt() ? "" : envCtx.getDpId())
                        .uuid(envCtx.isMt() ? envCtx.getUuid() : "")
                        .platform(envCtx.isMt() ? (envCtx.getDztgClientTypeEnum() == DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP ? PlatformEnum.MEITUAN_APPLET.getCode() : PlatformEnum.MEITUAN.getCode()) : PlatformEnum.DIANPING.getCode())
                        .appVersion(envCtx.isWxMini() ? request.getWxVersion() : envCtx.getVersion())
                        .ip(envCtx.getUserIp())
                        .actualLatitude((long) (NumbersUtils.toDouble(request.getUserlat()) * 1000000))
                        .actualLongitude((long) (NumbersUtils.toDouble(request.getUserlng()) * 1000000))
                        .homeLatitude((long) (NumbersUtils.toDouble(request.getCityLatitude()) * 1000000))
                        .homeLongitude((long) (NumbersUtils.toDouble(request.getCityLongitude()) * 1000000))
                        .mtHomeCityId(envCtx.isMt() ? request.getCityid() : 0)
                        .dpHomeCityId(envCtx.isMt() ? 0 : request.getCityid())
                        .userLocalCityId(dealCtx.getMtGpsCityId())
                        .dpUserLocalCityId(dealCtx.getDpGpsCityId())
                        .mtPoiId(dealCtx.getBestShopResp() != null ? dealCtx.getBestShopResp().getMtShopId() : 0)
                        .dpPoiId(dealCtx.getBestShopResp() != null ? dealCtx.getBestShopResp().getDpShopId() : 0)
                        .regionId(request.getRegionid())
                        .cType(getCType(envCtx))
                        .appId(envCtx.isWxMini() ? envCtx.getMpAppId() : "")
                        .wxOpenId(envCtx.isWxMini() ? envCtx.getOpenId() : "");
                if (Environment.isTestEnv()) {
                    builder.serviceName("mapi.51ping.com");
                } else {
                    builder.serviceName("mapi.dianping.com");
                }
                TransparentValidatorUtils.reportValidateParams(builder.build());
            }
        } catch (Exception e) {
            log.error("reportValidateParams error", e);
        }
    }

    // https://km.sankuai.com/page/**********
    private String getCType(EnvCtx envCtx) {
        if (envCtx.getDztgClientTypeEnum() == DztgClientTypeEnum.MEITUAN_APP) {
            return envCtx.isIos() ? "mtiphone" : "mtandroid";
        }
        if (envCtx.getDztgClientTypeEnum() == DztgClientTypeEnum.DIANPING_APP) {
            return envCtx.isIos() ? "dp_iphone" : "dp_android";
        }
        if (envCtx.getDztgClientTypeEnum() == DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP) {
            return "mt_weapp";
        }
        return "";
    }

}