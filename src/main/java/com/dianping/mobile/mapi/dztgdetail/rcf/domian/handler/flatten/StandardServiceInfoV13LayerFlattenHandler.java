package com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler.flatten;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.mobile.mapi.dztgdetail.rcf.enums.ModuleType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;


/**
 * <AUTHOR>
 * @create 2024/12/25 11:16
 */
@Slf4j
@Component("standard_service_info_v1_3layer")
public class StandardServiceInfoV13LayerFlattenHandler implements DealModuleFlattenProcessor{

    public static final String TYPE_2_1 = "type_2_1";
    public static final String TYPE_2_2 = "type_2_2";
    public static final String TYPE_2_3 = "type_2_3";

    @Override
    public ModuleType getType() {
        return ModuleType.standard_service_info_v1_3layer;
    }

    @Override
    public void flattenModule(JSONArray rcfSkuGroupsModule1Flatten, JSONObject module) {
        JSONArray skuGroupsModel1 = (JSONArray) module.get("skuGroupsModel1");
        if (Objects.isNull(skuGroupsModel1) || skuGroupsModel1.isEmpty()){
            return;
        }
        for (int i = 0; i < skuGroupsModel1.size(); i++) {
            JSONObject skuGroupsModel = (JSONObject) skuGroupsModel1.get(i);
            if (Objects.isNull(skuGroupsModel)){
                continue;
            }
            String title = (String) skuGroupsModel.get("title");
            if (StringUtils.isNotBlank(title)){
                JSONObject result = new JSONObject();
                result.put("key", TYPE_2_3);
                result.put("title", title);
                rcfSkuGroupsModule1Flatten.add(result);
            }
            JSONArray dealSkuList = (JSONArray) skuGroupsModel.get("dealSkuList");
            if (dealSkuList == null || dealSkuList.isEmpty()){
                continue;
            }
            for (int j = 0; j < dealSkuList.size(); j++) {
                JSONObject dealSku = (JSONObject) dealSkuList.get(j);
                // 处理 standard_service_info_v1_3layer_dealSkuTitle
                flattenDealSkuTitle(dealSku, rcfSkuGroupsModule1Flatten);
                JSONArray items = (JSONArray) dealSku.get("items");
                if (items != null){
                    // 处理 standard_service_info_v1_3layer_dealSkuItem
                    flattenDealSkuItem(items, rcfSkuGroupsModule1Flatten);
                }
            }
        }
    }

    private void flattenDealSkuTitle(JSONObject dealSku, JSONArray rcfSkuGroupsModule1Flatten){
        String title = (String) dealSku.get("title");
        String subTitle = (String) dealSku.get("subTitle");
        String rightText = (String) dealSku.get("rightText");
        if (StringUtils.isBlank(rightText)){
            rightText = (String) dealSku.get("price");
        }
        if (StringUtils.isNotBlank(title)){
            JSONObject result = new JSONObject();
            result.put("key", TYPE_2_1);
            result.put("title", title);
            result.put("subTitle", subTitle);
            result.put("rightText", rightText);
            rcfSkuGroupsModule1Flatten.add(result);
        }
    }
    private void flattenDealSkuItem(JSONArray items, JSONArray rcfSkuGroupsModule1Flatten){
        if (Objects.isNull(items)){
            return;
        }
        for (int i = 0; i < items.size(); i++) {
            JSONObject item = (JSONObject) items.get(i);
            flattenAttrValues(item, rcfSkuGroupsModule1Flatten);
        }
    }

    private void flattenAttrValues(JSONObject item, JSONArray rcfSkuGroupsModule1Flatten){
        JSONArray valueAttrs = (JSONArray) item.get("valueAttrs");
        String name = (String) item.get("name");
        String value = (String) item.get("value");
        if (StringUtils.isNotBlank(value) || StringUtils.isNotBlank(name)){
            JSONObject result = new JSONObject();
            result.put("key", TYPE_2_2);
            result.put("name", name);
            result.put("value", value);
            rcfSkuGroupsModule1Flatten.add(result);
        }
        if (valueAttrs == null || valueAttrs.isEmpty()){
            return;
        }
        for (int i = 0; i < valueAttrs.size(); i++) {
            JSONObject result = new JSONObject();
            result.put("key", TYPE_2_2);
            JSONObject valueAttr = (JSONObject) valueAttrs.get(i);
            JSONArray info = (JSONArray) valueAttr.get("info");
            if (i == 0 && StringUtils.isBlank(value)){
                result.put("name", name);
                result.put("attrIndex", i+1);
                result.put("attrName", getValueAttrsByIndex(valueAttrs, 0, "name"));
                result.put("attrRightText", joinValueAttrInfo(info));
                rcfSkuGroupsModule1Flatten.add(result);
                continue;
            }
            if (i > 0 && StringUtils.isBlank(value)){
                result.put("attrIndex", i+1);
                result.put("attrName", valueAttr.get("name"));
                result.put("attrRightText", joinValueAttrInfo(info));
                rcfSkuGroupsModule1Flatten.add(result);
                continue;
            }
            if (StringUtils.isNotBlank(value)){
                result.put("attrIndex", i+1);
                result.put("attrName", valueAttr.get("name"));
                result.put("attrRightText", joinValueAttrInfo(info));
                rcfSkuGroupsModule1Flatten.add(result);
            }
        }
    }

    private String joinValueAttrInfo(JSONArray info){
        if (Objects.isNull(info)){
            return null;
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < info.size(); i++) {
            String value = (String) info.get(i);
            if (StringUtils.isNotBlank(value)){
                stringBuilder.append(value);
                stringBuilder.append(",");
            }
        }
        return stringBuilder.length() > 0 ? stringBuilder.substring(0,stringBuilder.length()-2) : stringBuilder.toString();
    }

    private String getValueAttrsByIndex(JSONArray valueAttrs, int index, String key){
        if (Objects.isNull(valueAttrs)){
            return null;
        }
        JSONObject valueAttr = (JSONObject) valueAttrs.get(index);
        return (String) valueAttr.get(key);
    }
}
