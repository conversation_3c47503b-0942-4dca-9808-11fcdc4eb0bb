package com.dianping.mobile.mapi.dztgdetail.rcf.repository.switcher.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Optional;

/**
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2024/11/18 19:28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DealRcfSwitcherResult {

    private boolean render;

    private boolean report;

    private String reason;

    public static DealRcfSwitcherResult getDefaultConfig(String reason) {
        DealRcfSwitcherResult configDTO = new DealRcfSwitcherResult();
        configDTO.setRender(false);
        configDTO.setReport(false);
        configDTO.setReason(Optional.ofNullable(reason).orElse("UNKNOWN_REASON"));
        return configDTO;
    }

}
