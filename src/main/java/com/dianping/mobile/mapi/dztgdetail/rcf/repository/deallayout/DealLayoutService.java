package com.dianping.mobile.mapi.dztgdetail.rcf.repository.deallayout;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.deallayout.dto.ModuleItem;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.pageconfig.DealDetailPageConfigService;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.switcher.DealRcfSwitcherService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.dianping.mobile.mapi.dztgdetail.rcf.repository.constant.DealRcfNativeSnapshotConstant.*;

/**
 * <AUTHOR>
 * @create 2024/11/21 21:22
 */
@Slf4j
@Component
public class DealLayoutService {

    private final static String GC_DEAL_DETAIL_TRIPLE = "gcdealdetail_%s_%s_%s";
    private final static String GC_DEAL_DETAIL_DOUBLE = "gcdealdetail_%s_%s";
    private final static String GC_DEAL_DETAIL = "gcdealdetail_%s";

    private final static String CROSS_BUY = "cross_buy";
    private final static String FOLD_STYLE = "fold_style";
    private final static String CARD_STYLE = "card_style";
    private final static String CARD_STYLE_V2 = "card_style_v2";
    private final static String GCDealDetailModulesKeyDefault = "gcdealdetail_default_newtuandeal";
    private final static String GCDealDetailModulesKeyDefaultV2 = "gcdealdetail_default_newtuandeal_v2";

    private static JSONObject DEAL_DETAIL_PAGE_CONFIGS_VALUE =  JSON.parseObject(Lion.getString(LionConstants.APP_KEY, DEAL_DETAIL_PAGE_CONFIGS));
    private static JSONObject DEAL_DETAIL_PAGE_CONFIGS_V2_VALUE = JSON.parseObject(Lion.getString(LionConstants.APP_KEY, DEAL_DETAIL_PAGE_CONFIGS_V2));
    private static JSONObject DEAL_DETAIL_TAB_CONFIGS_VALUE = JSON.parseObject(Lion.getString(LionConstants.APP_KEY, DEAL_DETAIL_TAB_CONFIGS));


    static {
        Lion.addConfigListener(LionConstants.APP_KEY,"default", DEAL_DETAIL_PAGE_CONFIGS, configEvent -> parseDealDetailPageConfigs(configEvent.getValue()));
        Lion.addConfigListener(LionConstants.APP_KEY,"default", DEAL_DETAIL_PAGE_CONFIGS_V2, configEvent -> parseDealDetailPageConfigsV2(configEvent.getValue()));
        Lion.addConfigListener(LionConstants.APP_KEY,"default", DEAL_DETAIL_TAB_CONFIGS, configEvent -> parseDealDetailTabConfigs(configEvent.getValue()));
    }
    private static void parseDealDetailPageConfigs(String config) {
        DEAL_DETAIL_PAGE_CONFIGS_VALUE = JSON.parseObject(config);
    }
    private static void parseDealDetailPageConfigsV2(String config) {
        DEAL_DETAIL_PAGE_CONFIGS_V2_VALUE = JSON.parseObject(config);
    }
    private static void parseDealDetailTabConfigs(String config) {
        DEAL_DETAIL_TAB_CONFIGS_VALUE = JSON.parseObject(config);
    }


    @Resource
    private DealDetailPageConfigService dealDetailPageConfigService;

    public Map<String, ModuleItem> getDealLayout(String key, String extraInfo, String generalInfo, Map<String, String> params){
        Map<String, ModuleItem> config = new HashMap<String, ModuleItem>();
        if (StringUtils.isNotBlank(extraInfo) && StringUtils.isNotBlank(generalInfo)) {
            String configKey = String.format(GC_DEAL_DETAIL_TRIPLE, key, extraInfo, generalInfo);
            config = fetchLocalConfig(configKey, params, generalInfo);
        }
        // 后端会下发card_style_v2标识代表此时是团购详情页改版2.0样式
        if (MapUtils.isEmpty(config) && CARD_STYLE_V2.equals(generalInfo)) {
            config = fetchLocalConfig(
                    GCDealDetailModulesKeyDefaultV2,
                    params,
                    generalInfo
            );
        }
        if (MapUtils.isEmpty(config) && StringUtils.isNotBlank(extraInfo)) {
            String configKey = String.format(GC_DEAL_DETAIL_DOUBLE, key, extraInfo);
            config = fetchLocalConfig(configKey, params, generalInfo);
        }
        if (MapUtils.isEmpty(config) && StringUtils.isNotBlank(generalInfo)) {
            String configKey = String.format(GC_DEAL_DETAIL_DOUBLE, key, generalInfo);
            config = fetchLocalConfig(configKey, params, generalInfo);
        }
        if (MapUtils.isEmpty(config)) {
            String configKey = String.format(GC_DEAL_DETAIL, key);
            config = fetchLocalConfig(configKey, params, generalInfo);
        }
        if (MapUtils.isEmpty(config)) {
            config = fetchLocalConfig(GCDealDetailModulesKeyDefault, params, generalInfo);
        }

        return config;
    }

    public Map<String, ModuleItem> fetchLocalConfig(String configKey, Map<String, String> params, String generalInfo) {
        if (isCrossBuyStatus(params)) {
            // 到综通用全品类的特殊配置
            return dealDetailPageConfigService.processPageConfig("gcdealdetail_cross_buy_newtuandeal", DEAL_DETAIL_PAGE_CONFIGS_VALUE);
        }
        if (CARD_STYLE.equals(generalInfo) || StringUtils.isNotBlank(generalInfo)&&generalInfo.startsWith(FOLD_STYLE) || CARD_STYLE_V2.equals(generalInfo)) {
            // 卡片样式、折叠样式、团详2.0样式
            return dealDetailPageConfigService.processPageConfig(configKey, DEAL_DETAIL_PAGE_CONFIGS_V2_VALUE);
        }
        // 团详1.0样式
        return dealDetailPageConfigService.processPageConfig(configKey, DEAL_DETAIL_PAGE_CONFIGS_VALUE);
    }

    public boolean isCrossBuyStatus(Map<String, String> params) {
        return MapUtils.isNotEmpty(params) && CROSS_BUY.equals(params.get("dealConfig"));
    }

    public Map<String, ModuleItem> fetchDealDetailTabConfig(String configKey) {
        return dealDetailPageConfigService.processPageConfig(configKey, DEAL_DETAIL_TAB_CONFIGS_VALUE);
    }

    public ModuleItem getModuleItem(String key, Map<String, ModuleItem> moduleItems){
        return moduleItems.get(key);
    }
}
