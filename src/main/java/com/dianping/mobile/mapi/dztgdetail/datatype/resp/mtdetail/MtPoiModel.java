package com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.sankuai.sig.botdefender.adapter.annotation.EncryptedField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 团详页商户信息
 * Created by wa<PERSON><PERSON><PERSON> on 16/4/19.
 */
@MobileDo(id = 0x2b0e)
@Data
public class MtPoiModel implements Serializable {

    @MobileField(key = 0x91b)
    @Deprecated
    @EncryptedField(targetFieldName = "poiidEncrypt")
    private int poiid;
    @MobileField(key = 0xc446)
    private String poiidEncrypt;

    @MobileField(key = 0x835b)
    @EncryptedField(targetFieldName = "poiIdStrEncrypt")
    private String poiIdStr;
    @MobileField(key = 0x5bb1)
    private String poiIdStrEncrypt;

    @MobileField(key = 0xee8f)
    private String name;

    @MobileField(key = 0x49d6)
    private String phone;

    @MobileField(key = 0x100e)
    private String addr;

    @MobileField(key = 0x945f)
    private String showType;

    @MobileField(key = 0x297e)
    private double lat;

    @MobileField(key = 0x2b04)
    private double lng;

    @MobileField(key = 0x8950)
    private boolean isShowPhoneNo = true;

    @MobileField(key = 0xfdc)
    private List<ComButton> recallButtons;

}
