package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 简单文案展示，支持颜色
 */
@Data
@MobileDo(id = 0xbcb2)
@AllArgsConstructor
@NoArgsConstructor
public class SimpleContextVO implements Serializable {

    /**
     * 文案
     */
    @MobileDo.MobileField(key = 0x451b)
    private String text;

    /**
     * 字体颜色
     */
    @MobileDo.MobileField(key = 0xeead)
    private String textColor;

}
