package com.dianping.mobile.mapi.dztgdetail.button.joy;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtnIcon;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.PromoHelper;
import com.dianping.zebra.util.StringUtils;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

public abstract class AbstractPriceServiceButtonBuilder extends AbstractButtonBuilder {

    @Override
    protected void doBuild(DealCtx context, ButtonBuilderChain chain) {
        buildButton(context);
        chain.build(context);
    }

    private void buildButton(DealCtx context) {

        PriceDisplayDTO price = getPrice(context);
        if (price == null || !stateMatch(context)) {
            return;
        }
        DealBuyBtn button = buildOriginButton(context, getButtonTitle(context, DEFAULT_BUTTON_NAME));

        button.setPriceStr(formatPrice(price.getPrice()));
        if (DealBuyHelper.isCouponBar(context)) {
            buildPriceCouponInfo(context, button);
        }
        button.setBtnText("￥" + button.getPriceStr() + " 立即购买");
        button.setBtnIcons(Lists.newArrayList());
        if (price.getPromoAmount() != null && price.getPromoAmount().compareTo(BigDecimal.ZERO) > 0) {
            button.setBtnDesc(buildHadPromoDesc(context));
        }
        if (CollectionUtils.isNotEmpty(price.getUsedPromos())) {
            button.setBtnTag(StringUtils.isNotBlank(price.getPromoTag())? price.getPromoTag() : price.getUsedPromos().get(0).getTag());
            if (StringUtils.isNotBlank(price.getPromoTag())){
                button.getBtnIcons().add(DealBuyHelper.getPromoIcon(context, price.getPromoTag()));
            } else {
                List<DealBuyBtnIcon> icons = price.getUsedPromos()
                        .stream()
                        .map(p -> DealBuyHelper.getPromoIcon(context, p.getTag()))
                        .collect(Collectors.toList());
                button.getBtnIcons().addAll(icons);
            }
        }
        afterBuild(context, button);
        context.addButton(button);
    }

    private void buildPriceCouponInfo(DealCtx context, DealBuyBtn buyBtn) {
        // 构建价格前为“预付金”的文案，表示当前价格为预付金
        if (DealAttrHelper.isPrepayDeal(context.getDealGroupDTO()) && DouHuService.isHitRepairPayAbTest(context)) {
            buyBtn.setPricePrefix("预付金");
            return;
        }
        List<PromoDTO> couponPromos = PromoHelper.getCouponUsePromo(context);
        if (CollectionUtils.isEmpty(couponPromos)) {
            return;
        }
        for (PromoDTO couponPromo : couponPromos) {
            if (couponPromo.getIdentity().getPromoType() == PromoTypeEnum.COUPON.getType() || couponPromo.getIdentity().getPromoType() == PromoTypeEnum.GOVERNMENT_CONSUME_COUPON.getType()) {
                buyBtn.setPricePrefix("券后");
                return;
            }
        }
    }

    protected String getMemberCardTitle(DealCtx context) {
        if (context.getPriceContext().getDcCardMemberCard() == null) {
            return null;
        }
        String title = "会员价";
        if (context.getPriceContext().isDcCardMemberDay()) {
            BigDecimal discount = context.getPriceContext()
                    .getDcCardMemberCard()
                    .getDiscount()
                    .multiply(BigDecimal.TEN);
            title = "会员日" + formatDiscount(discount) + "折";
        }
        return title;
    }

    protected void afterBuild(DealCtx context, DealBuyBtn button) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.button.joy.AbstractPriceServiceButtonBuilder.afterBuild(DealCtx,DealBuyBtn)");
    }

    protected abstract PriceDisplayDTO getPrice(DealCtx context);
}
