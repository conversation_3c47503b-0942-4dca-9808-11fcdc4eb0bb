package com.dianping.mobile.mapi.dztgdetail.entity;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/10/24
 */
@Data
@Builder
public class QueryExhibitImageFilterParam {
    /**
     * 1-点评、2-美团
     */
    private Integer clientType;
    /**
     * 门店id，和client保持一致
     */
    private Long shopId;
    /**
     * 业务标识，1
     */
    private Integer bizType;
    /**
     * 子业务标识，21孕婴童摄影，25个性写真
     */
    private Integer subBizType;
    /**
     * 团单id
     */
    private Integer externalBizId;
    /**
     * 团单类型：1-点评、2-美团
     */
    private Integer externalBizIdType;
    /**
     * 团单二级类目ID
     */
    private Integer categoryId;
}

