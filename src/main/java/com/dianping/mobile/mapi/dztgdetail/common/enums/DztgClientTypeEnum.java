package com.dianping.mobile.mapi.dztgdetail.common.enums;

import lombok.Getter;

/**
 * 背景：2022年H2，到综团详页开始接入越来越多的客户端类型（之前只有美团和点评的native app）。
 * 此枚举仅用于本工程内判断客户端类型，不用于下游传参。
 */
@Getter
public enum DztgClientTypeEnum {
    /**
     * 到综团详页的客户端类型
     */
    MEITUAN_APP(1, "美团app", DztgPlatformEnum.MT),
    MEITUAN_WEIXIN_MINIAPP(2, "美团微信小程序", DztgPlatformEnum.MT),
    MEITUAN_KUAISHOU_MINIAPP(3, "美团快手小程序", DztgPlatformEnum.MT),
    DIANPING_APP(4, "点评app", DztgPlatformEnum.DP),
    DIANPING_WEIXIN_MINIAPP(5, "点评微信小程序", DztgPlatformEnum.DP),
    DIANPING_BAIDUMAP_MINIAPP(6, "点评百度地图小程序", DztgPlatformEnum.DP),
    MEITUAN_MAP_APP(7, "美团地图app", DztgPlatformEnum.MT),
    MEITUAN_FROM_H5(8, "美团.json接口", DztgPlatformEnum.MT),
    DIANPING_FROM_H5(9, "点评.json接口", DztgPlatformEnum.DP),
    MEITUAN_WANWU_MINIAPP(10, "美团万物小程序", DztgPlatformEnum.MT),
    THIRD_PLATFORM(11, "第三方平台", DztgPlatformEnum.OTHER),
    APOLLO(12, "阿波罗", DztgPlatformEnum.OTHER),
    DPMERCHANT(13, "开店宝", DztgPlatformEnum.OTHER),
    MEITUAN_LIVE_WEIXIN_MINIAPP(14, "美团美播微信小程序", DztgPlatformEnum.MT),
    MEITUAN_LIVE_ORDER_WEIXIN_MINIAPP(15, "美团美播提单小程序", DztgPlatformEnum.MT),
    BEAM_APP(16, "Beam App", DztgPlatformEnum.MT),
    UNKNOWN(99, "未知客户端", DztgPlatformEnum.OTHER);

    private int code;
    private final String desc;
    private final DztgPlatformEnum platform;

    DztgClientTypeEnum(int code, String desc, DztgPlatformEnum platform) {
        this.code = code;
        this.desc = desc;
        this.platform = platform;
    }

    public static DztgClientTypeEnum codeOf(int code) {
        for (DztgClientTypeEnum value : DztgClientTypeEnum.values()) {
            if (value.code == code) {
                return value;
            }
        }

        throw new UnsupportedOperationException("MiniProgramSceneEnum has no code of " + code);
    }

    public static DztgClientTypeEnum descOf(String desc) {
        for (DztgClientTypeEnum value : DztgClientTypeEnum.values()) {
            if (value.desc.equals(desc)) {
                return value;
            }
        }

        throw new UnsupportedOperationException("MiniProgramSceneEnum has no desc of " + desc);
    }
}
