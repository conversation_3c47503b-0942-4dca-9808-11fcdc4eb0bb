package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/29 8:38 下午
 */
@MobileDo(id = 0x4375)
public class DealDetailStructAttrModuleVO implements Serializable {
    /**
     * 属性值
     */
    @MobileDo.MobileField(key = 0x9ddc)
    private List<String> attrValues;

    /**
     * 属性图标url
     */
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;

    /**
     * 属性名
     */
    @MobileDo.MobileField(key = 0x491e)
    private String attrName;

    /**
     * 对主属性的补充说明
     */
    @MobileDo.MobileField(key = 0xfebf)
    private List<DealDetailAttrDescVO> desc;

    /**
     * 用于设置活动标签
     */
    @MobileDo.MobileField(key = 0xf112)
    private ActivityTag additionalTags;

    /**
     * 副标题
     */
    @MobileDo.MobileField(key = 0xd894)
    private String subTitle;

    public List<String> getAttrValues() {
        return attrValues;
    }

    public void setAttrValues(List<String> attrValues) {
        this.attrValues = attrValues;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getAttrName() {
        return attrName;
    }

    public void setAttrName(String attrName) {
        this.attrName = attrName;
    }

    public List<DealDetailAttrDescVO> getDesc() {
        return desc;
    }
    public void setDesc(List<DealDetailAttrDescVO> desc) {
        this.desc = desc;
    }

    public ActivityTag getAdditionalTags() {
        return additionalTags;
    }

    public void setAdditionalTags(ActivityTag additionalTags) {
        this.additionalTags = additionalTags;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }
}
