package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.deal.bff.cache.dto.RcfDealBffCommonParamDTO;
import com.dianping.deal.bff.cache.enums.RcfDealBffClientTypeEnum;
import com.dianping.deal.bff.cache.enums.RcfDealBffInterfaceEnum;
import com.dianping.deal.bff.cache.response.DealBffCacheQueryResponse;
import com.dianping.gmkt.scene.api.delivery.dto.res.DeliveryCommonResponse;
import com.dianping.gmkt.scene.api.delivery.dto.res.ResourceExposureResponseDTO;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.bff.cache.DealBffCacheAclService;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.TimesDealUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.dianping.deal.bff.cache.enums.RcfDealBffInterfaceEnum.queryexposureresources;

/**
 * <AUTHOR>
 */
public class RcfSnapshotCacheDataProcessor extends AbsDealProcessor {

    @Resource
    private DealBffCacheAclService dealBffCacheAclService;

    @Override
    public boolean isEnable(DealCtx ctx) {
        //连续包月团单不展示 相似团单缓存
        return LionConfigUtils.getDealBaseRcfSnapshotCacheProcessSwitch()
                && !RequestSourceEnum.fromTradeSnapshot(ctx.getRequestSource()) && !isMonthlySubscription(ctx);
    }

    @Override
    public void prepare(DealCtx ctx) {

    }

    @Override
    public void process(DealCtx ctx) {
         try{
             if (!validParam(ctx)){
                 return;
             }
             DealBffCacheQueryResponse cacheQueryResponse = dealBffCacheAclService.query(buildRequest(ctx));
             Map<String, String> cacheDataMap = cacheQueryResponse.getCacheDataMap();
             buildCacheData(ctx, cacheDataMap);
        }catch (Exception e){
            logger.error(" RcfSnapshotCacheDataProcessor process.error:{}", e);
        }
    }

    public boolean validParam(DealCtx ctx){
        if (ctx.isMt() && ctx.getDealId4P() > 0 && null != fromRequestClientType(ctx.getEnvCtx().getDztgClientTypeEnum())){
            return true;
        }
        return false;
    }

    public void buildCacheData(DealCtx ctx, Map<String, String> cacheDataMap) {
        if (MapUtils.isNotEmpty(cacheDataMap)) {
            // 氛围条缓存
            buildQueryExposureResources(ctx, cacheDataMap);

            // 相似团购推荐
            Object dealFilterList = cacheDataMap.get(RcfDealBffInterfaceEnum.dealfilterlist.name());
            ctx.setDealFilterListCache(dealFilterList != null ? String.valueOf(dealFilterList) : null);
        }
    }

    public void buildQueryExposureResources(DealCtx ctx, Map<String, String> cacheDataMap){
        // 氛围条缓存
        String queryExposureResourcesJson = cacheDataMap.get(queryexposureresources.name());
        if (StringUtils.isNotBlank(queryExposureResourcesJson)) {
            DeliveryCommonResponse queryExposureResources = new DeliveryCommonResponse<ResourceExposureResponseDTO>();
            JSONObject jsonObject = JSONObject.parseObject(queryExposureResourcesJson);
            if (jsonObject == null){
                return;
            }
            queryExposureResources.setCode(jsonObject.getString("code"));
            queryExposureResources.setDesc(jsonObject.getString("desc"));
            JSONArray dataJson = (JSONArray) jsonObject.get("data");
            if (dataJson != null) {
                List data = JSONObject.parseArray(JSONArray.toJSONString(dataJson), ResourceExposureResponseDTO.class);
                queryExposureResources.setData(data);
            }
            ctx.setQueryExposureResourcesCache(queryExposureResources);
        }
    }

    public RcfDealBffCommonParamDTO buildRequest(DealCtx ctx){
        RcfDealBffClientTypeEnum rcfClientType = fromRequestClientType(ctx.getEnvCtx().getDztgClientTypeEnum());
        RcfDealBffCommonParamDTO param = new RcfDealBffCommonParamDTO();
        param.setDealGroupId(ctx.getDealId4P());
        param.setShopId(ctx.getLongPoiId4PFromReq());
        param.setClientType(Objects.nonNull(rcfClientType) ? rcfClientType.getCode() : 0);
        param.setCityId(ctx.getCityId4P());
        param.setUserLng(Objects.nonNull(ctx.getDealBaseReq()) ? ctx.getDealBaseReq().getUserlng() : 0);
        param.setUserLat(Objects.nonNull(ctx.getDealBaseReq()) ? ctx.getDealBaseReq().getUserlat() : 0);
        param.setPageSource(ctx.getDealBaseReq().getPageSource());
        param.setAppVersion(ctx.getEnvCtx().getVersion());
        return param;
    }

    public RcfDealBffClientTypeEnum fromRequestClientType(DztgClientTypeEnum dztgClientType) {
        if (dztgClientType == DztgClientTypeEnum.MEITUAN_APP) {
            return RcfDealBffClientTypeEnum.MT_APP;
        }
        return null;
    }

    /**
     * 判断是否为连续包月团单
     * @param ctx
     * @return
     */
    public boolean isMonthlySubscription(DealCtx ctx) {
        if (Objects.isNull(ctx.getDealGroupDTO())) {
            return false;
        }
        List<String> serviceTypeIds = LionConfigUtils.getMonthlySubscriptionCategoryList(String.valueOf(ctx.getCategoryId()));
        if (CollectionUtils.isEmpty(serviceTypeIds)) {
            return false;
        }
        Long serviceTypeId = ctx.getDealGroupDTO().getCategory().getServiceTypeId();
        return (ctx.getEnvCtx().getDztgClientTypeEnum() == DztgClientTypeEnum.MEITUAN_APP ||
                ctx.getEnvCtx().getDztgClientTypeEnum() == DztgClientTypeEnum.DIANPING_APP ) &&
                serviceTypeIds.contains(String.valueOf(serviceTypeId)) &&
                TimesDealUtil.isMonthlySubscription(ctx);
    }
}
