package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.cat.Cat;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.publishcategory.dto.ChannelDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.json.facade.JsonFacade;
import com.dianping.lion.client.Lion;
import com.dianping.lion.facade.LionFacade;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtnIcon;
import com.dianping.mobile.mapi.dztgdetail.util.*;
import com.dianping.pay.promo.common.enums.promo.PromoTimeFormatType;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meituan.nibscp.common.api.enums.TradeTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.merchantcard.timescard.exposure.dto.CardSummaryBarDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

public class DealBuyHelper {

    private static final String MEMBER_PRICE_PIC = "https://p0.meituan.net/travelcube/9b2aea61e24cce89662c8ed02e0992de5046.png";
    private static final String MEMBER_DAY_PRICE_PIC = "https://p1.meituan.net/travelcube/871513572f7c0d85523006495fb479e74100.png";
    private static final String MEMBER_PRICE_COLOR = "#C7924A";
    private static final String MEMBER_DAY_PRICE_COLOR = "#FFFFFF";
    private static final String ENTERTAINMENT_MEMBER_DAY_PRICE_COLOR = "#572B00";
    private static final String REDUCE_PRICE_COLOR_MT = "#646464";
    private static final String REDUCE_PRICE_COLOR_DP = "#FF6633";
    private static final String DEFAULT_COLOR = "#000000";
    private static final String BG_CARD_COLOR_MT = "#4DFED1CE";
    private static final String BG_CARD_COLOR_MT_MRN = "#FED1CE4D";
    private static final String BG_CARD_COLOR_DP = "#1AFF6633";
    private static final String BG_CARD_COLOR_DP_MRN = "#FF66331A";
    private static final String DEFAULT_BG_CARD_COLOR_MT = "#FFFFFF";
    private static final String DEFAULT_BG_CARD_COLOR_DP = "#00FF6633";
    private static final String DEFAULT_BG_CARD_COLOR_DP_MRN = "#FF663300";
    private static final String BORDER_CARD_COLOR_MT = "#FED1CE";
    private static final String BORDER_CARD_COLOR_DP = "#00FFFFFF";
    private static final String BORDER_CARD_COLOR_DP_MRN = "#FFFFFF00";
    private static final String TITLE_CARD_COLOR_MT = "#FF4A4A";
    private static final String TITLE_CARD_COLOR_DP = "#FF6633";
    private static final String ENTERTAINMENT_NEW_TITLE_CARD_COLOR_MT = "#FF6200";
    private static final String ENTERTAINMENT_NEW_TITLE_CARD_COLOR_DP = "#FF6633";
    private static final String TRANSPARENT_COLOR = "transparent";

    public static final String SHOPPING_CART_MRN_LIMITED_VERSION = "0.5.1";
    public static final String SHOPPING_CART_APP_LIMITED_VERSION = "12.8.200";

    public static DealBuyBtn getCanNotBuyButton(DealCtx ctx) {
        DealGroupBaseDTO dealGroupBase = ctx.getDealGroupBase();

        boolean needPreOrder = DealCtxHelper.isPreOrderDeal(ctx);
        if (dealGroupBase.getBeginDate() != null && dealGroupBase.getBeginDate().compareTo(new Date()) > 0) {
            return getCommonSingleBuyBtn(false, needPreOrder ? "即将开订" : "即将开始", ctx);
        }

        if ((dealGroupBase.getEndDate() != null && dealGroupBase.getEndDate().compareTo(new Date()) < 0)
                || dealGroupBase.getStatus() == 0) {
            return getCommonSingleBuyBtn(false, "已结束", ctx);
        }

        if (dealGroupBase.getStatus() <= 0 || dealGroupBase.getStatus() == 3) {
            return getCommonSingleBuyBtn(false, "已结束", ctx);
        }

        if (DealHelper.isSoldOut(ctx.getEnvCtx().getClientType(), ctx.getDealGroupStock())) {
            return getCommonSingleBuyBtn(false, needPreOrder ? "已订满" : "已卖光", ctx);
        }

        DealBuyBtn customizedBtn = getCustomizedCanNotBuyButton(ctx);
        if (customizedBtn != null) {
            return customizedBtn;
        }

        return null;
    }

    public static DealBuyBtn getCanNotResvButton(DealCtx ctx) {
        if (ctx.getIsCanResv()){
            return null;
        }
        DealBuyBtn btn = new DealBuyBtn(false, "抱歉，近60天已约满");
        btn.setAddShoppingCartStatus(0);
        btn.setDetailBuyType(BuyBtnTypeEnum.RESV_DEAL.getCode());
        return btn;
    }
    public static DealBuyBtn getResvButton(DealCtx ctx) {
        if (!ctx.getIsCanResv()){
            return null;
        }
        DealBuyBtn btn = new DealBuyBtn(true, "预约接种");
        btn.setAddShoppingCartStatus(0);
        btn.setDetailBuyType(BuyBtnTypeEnum.RESV_DEAL.getCode());
        BigDecimal dealGroupPrice = ctx.getDealGroupBase().getDealGroupPrice();
        // key:"retailPriceStyle" value:"1"
        // * 1：正常展示 2：隐藏 3：起价
        DealGroupDealDTO dealGroupDealDTO = null;
        if (ctx.getDealGroupDTO()!=null && CollectionUtils.isNotEmpty(ctx.getDealGroupDTO().getDeals())){
            dealGroupDealDTO = ctx.getDealGroupDTO().getDeals().get(0);
        }
        if (dealGroupDealDTO == null){
            return null;
        }
        String retailPriceStyle = getAttr(dealGroupDealDTO, "retailPriceStyle");
        //retailPriceStyle为null正常展示
        if (StringUtils.isEmpty(retailPriceStyle) || ("1").equals(retailPriceStyle)) {
            btn.setBtnSubTitle("到门诊支付¥"+dealGroupPrice);
        }
        String resvFillingUrl = UrlHelper.getResvFillingUrl(ctx);
        btn.setRedirectUrl(resvFillingUrl);
        return btn;
    }
    public static String getAttr(DealGroupDealDTO dealDTO, String attrName) {
        if(dealDTO.getAttrs() == null) {
            return "";
        }
        return dealDTO.getAttrs()
                .stream()
                .filter(attrDTO -> attrDTO.getName().equals(attrName))
                .findAny()
                .map(AttrDTO::getValue)
                .flatMap(list -> list.stream().findFirst())
                .orElse("");
    }
    public static DealBuyBtn getCustomizedCanNotBuyButton(DealCtx ctx) {
        //分销项目要求分销场景提供统一降级能力
        if (DealCtxHelper.isOdpSource(ctx) && Lion.getBoolean("com.sankuai.dzu.tpbase.dztgdetailweb", "com.sankuai.dzu.tpbase.dztgdetailweb.odp.btn.degrade", false)) {
            return getCommonSingleBuyBtn(false, "已结束", ctx);
        }
        return null;
    }

    public static void convertToDoubleButtonStyle(DealCtx ctx, DealBuyBtn dealBuyBtn) {
        List<DealBuyBtnIcon> btnIcons = dealBuyBtn.getBtnIcons();
        if (CollectionUtils.isNotEmpty(btnIcons)) {
            List<DealBuyBtnIcon> newIcons = Lists.newArrayList();
            for (DealBuyBtnIcon btnIcon : btnIcons) {
                newIcons.add(getDoubleButtonRightIcon(ctx, btnIcon.getTitle()));
            }
            dealBuyBtn.setBtnIcons(newIcons);
        }
    }

    public static PromoDisplayDTO getValidIdlePromo(DealCtx ctx) {

        if (Version.idlePromoLimited(ctx)) {
            return null;
        }

        List<PromoDisplayDTO> idlePromos = PromoHelper.getIdlePromo(ctx.getPromoList());
        if (CollectionUtils.isEmpty(idlePromos)) {
            return null;
        }
        PromoDisplayDTO idlePromo = idlePromos.get(0);
        if (idlePromo.getCanConsumeTime().getType() != PromoTimeFormatType.DAY_OF_WEEK.getCode()) {
            return null;
        }
        if (CollectionUtils.isEmpty(idlePromo.getCanConsumeTime().getTimes())) {
            return null;
        }

        // 防止价格倒挂
        BigDecimal idlePrice = ctx.getDealGroupBase().getDealGroupPrice().subtract(idlePromo.getPromoAmount());
        BigDecimal originPrice = ctx.getDealGroupBase().getDealGroupPrice();
        PromoDisplayDTO normalPromo = PromoHelper.getValidPromo(ctx.getPromoList());
        if (normalPromo != null) {
            originPrice = originPrice.subtract(normalPromo.getPromoAmount());
        }
        if (idlePrice.compareTo(originPrice) >= 0) {
            return null;
        }

        if (idlePrice.compareTo(BigDecimal.ZERO) < 0) {
            return null;
        }

        return idlePromo;
    }

    private static DealBuyBtn getCommonSingleBuyBtn(boolean btnEnable, String title, DealCtx ctx) {
        DealGroupBaseDTO dealGroupBase = ctx.getDealGroupBase();
        DealBuyBtn buyBtn = new DealBuyBtn(btnEnable, title);
        PromoDisplayDTO promo = PromoHelper.getValidPromo(ctx.getPromoList());
        boolean isApp = ctx.getEnvCtx().isMainApp();
        if (promo != null && promo.isPriceLineThrough()) {
            BigDecimal canBuyPrice = promo.getPromoAmount().compareTo(dealGroupBase.getDealGroupPrice()) > 0 ? new BigDecimal("0") : dealGroupBase.getDealGroupPrice().subtract(promo.getPromoAmount());
            buyBtn.setPriceStr(PriceHelper.dropLastZero(canBuyPrice));
            buyBtn.setBtnDesc(getUnUsedPriceJlDesc("团购价 ￥", dealGroupBase.getDealGroupPrice(), isApp, true));
            buyBtn.setBtnTag(promo.getTag());
            if (StringUtils.isNotEmpty(promo.getTag())) {
                buyBtn.setBtnIcons(Collections.singletonList(getPromoIcon(ctx, promo.getTag())));
            }
            buyBtn.setDetailBuyType(1);
        } else {
            List<Integer> comingBarPromoCategories = Lion.getList(LionConstants.APP_KEY,
                    LionConstants.COMING_BAR_PROMO_CATEGORIES, Integer.class, Collections.emptyList());
            if (comingBarPromoCategories.contains(ctx.getCategoryId())) {
                // "即将开始"底bar展示优惠后价格
                PriceDisplayDTO promoPrice = PriceHelper.getMarketOrNormalPromoPrice(ctx);
                buyBtn.setPriceStr(PriceHelper.dropLastZero(promoPrice.getPrice()));
            } else {
                BigDecimal canBuyPrice = dealGroupBase.getDealGroupPrice();
                buyBtn.setPriceStr(PriceHelper.dropLastZero(canBuyPrice));
            }
            if (promo != null) {
                buyBtn.setBtnTag(promo.getTag());
                if (StringUtils.isNotEmpty(promo.getTag())) {
                    buyBtn.setBtnIcons(Collections.singletonList(getPromoIcon(ctx, promo.getTag())));
                }
            }
            if (ctx.isMarketPriceHided()) {
                buyBtn.setBtnDesc(org.apache.commons.lang.StringUtils.EMPTY);
            } else {
                buyBtn.setBtnDesc(getUnUsedPriceJlDesc("门市价 ￥", dealGroupBase.getMarketPrice(), isApp, true));
            }
            buyBtn.setDetailBuyType(1);
        }
        if (btnEnable) {
            String finalPrice = buyBtn.getPriceStr();
            String url = UrlHelper.getCommonBuyUrl(ctx, ctx.getMtCityId(), finalPrice);
            buyBtn.setRedirectUrl(url);
        }
        return buyBtn;
    }

    public static DealBuyBtn getAssembleDealButton(DealCtx ctx) {
        PinProductBrief pinProductBrief = ctx.getPinProductBrief();
        String btnTitle = String.format("%s人拼团", pinProductBrief.getPinPersonNum());
        if (ctx.getPriceContext().isZuLiaoButtonNewStyle()) {
            btnTitle = String.format("%s人团", pinProductBrief.getPinPersonNum());
        }
        String pinPoolUrl = UrlHelper.getAppUrl(ctx.getEnvCtx(), pinProductBrief.getUrl(), ctx.isMt());

        DealBuyBtn leftBtn = new DealBuyBtn(true, btnTitle);
        leftBtn.setPriceStr(PriceHelper.format(pinProductBrief.getPrice().setScale(2, RoundingMode.CEILING)));

        //拼团的button产品要求不展示优惠标签

//        String pinTag = getPinBtnTag(ctx);
//        if (StringUtils.isNotBlank(pinTag)) {
//            leftBtn.setBtnTag(pinTag);
//            leftBtn.setBtnIcons(Collections.singletonList(getDoubleButtonLeftIcon(ctx, pinTag)));
//        }

        if (GreyUtils.showZuLiaoPriceDeal(ctx) && GreyUtils.showZuLiaoMarketPricePoi(ctx)) {
            PriceDisplayDTO normalPrice = ctx.getPriceContext().getNormalPrice();
            String promoIconName;
            if (normalPrice != null && normalPrice.getMarketPrice() != null
                    && ctx.getPriceContext().isZuLiaoButtonNewStyle()) {
                promoIconName = "单人省￥" + normalPrice.getMarketPrice().subtract(new BigDecimal(leftBtn.getPriceStr()))
                        .setScale(1, RoundingMode.CEILING).stripTrailingZeros().toPlainString();
            } else {
                promoIconName = leftBtn.getBtnTag();
            }
            DealBuyBtnIcon buttonIcon = DealBuyHelper.getNewTimesCardIcon(ctx, promoIconName);
            leftBtn.setBtnIcons(Lists.newArrayList(buttonIcon));
        }

        leftBtn.setRedirectUrl(pinPoolUrl);
        leftBtn.setDetailBuyType(4);

        return leftBtn;
    }

    public static DealBuyBtn getJoyAssembleDealButton(DealCtx ctx) {
        PinProductBrief pinProductBrief = ctx.getPinProductBrief();
        String btnTitle = String.format("%s人团", pinProductBrief.getPinPersonNum());
        String pinPoolUrl = UrlHelper.getAppUrl(ctx.getEnvCtx(), pinProductBrief.getUrl(), ctx.isMt());

        DealBuyBtn leftBtn = new DealBuyBtn(true, btnTitle);
        leftBtn.setPriceStr(PriceHelper.dropLastZero(pinProductBrief.getPrice().setScale(2, RoundingMode.CEILING)));

        PriceDisplayDTO normalPrice = ctx.getPriceContext().getNormalPrice();
        String promoIconName;
        if (normalPrice != null && normalPrice.getMarketPrice() != null) {
            promoIconName = "每人省￥" + normalPrice.getMarketPrice().subtract(new BigDecimal(leftBtn.getPriceStr()))
                    .setScale(1, RoundingMode.CEILING).stripTrailingZeros().toPlainString();
        } else {
            promoIconName = leftBtn.getBtnTag();
        }
        DealBuyBtnIcon buttonIcon = DealBuyHelper.getNewTimesCardIcon(ctx, promoIconName);
        leftBtn.setBtnIcons(Lists.newArrayList(buttonIcon));

        leftBtn.setRedirectUrl(pinPoolUrl);
        leftBtn.setDetailBuyType(4);

        return leftBtn;
    }

    public static DealBuyBtn getTimesCardButton(DealCtx ctx) {
        CardSummaryBarDTO timesCard = ctx.getTimesCard();
        String btnTitle;
        if (ctx.isMultiCard()) {
            btnTitle = "购买多次";
        } else {
            btnTitle = String.format("购买%s次", timesCard.getTimes());
        }

        if (ctx.getPriceContext().isZuLiaoButtonNewStyle()) {
            btnTitle = String.format("%s次卡", timesCard.getTimes());
        }

        String timesCardUrl = UrlHelper.getTimesCardBuyUrl(ctx, timesCard.getProductId(), timesCard.getChannelSource());

        DealBuyBtn leftBtn = new DealBuyBtn(true, btnTitle);
        leftBtn.setPriceStr(PriceHelper.format(BuyButtonHelper.getTimesCardTruthPrice(ctx)));
        if (StringUtils.isNotEmpty(timesCard.getBtnTag())) {
            if (GreyUtils.showZuLiaoPriceDeal(ctx) && GreyUtils.showZuLiaoMarketPricePoi(ctx)) {
                PriceDisplayDTO normalPrice = ctx.getPriceContext().getNormalPrice();
                if (normalPrice != null && normalPrice.getMarketPrice() != null) {
                    String promoIconName;
                    if (ctx.getPriceContext().isZuLiaoButtonNewStyle()) {
                        promoIconName = "单次省￥"
                                + normalPrice.getMarketPrice().subtract(new BigDecimal(leftBtn.getPriceStr()))
                                .setScale(1, RoundingMode.CEILING).stripTrailingZeros().toPlainString();
                    } else {
                        promoIconName = timesCard.getBtnTag();
                    }
                    leftBtn.setBtnIcons(Collections.singletonList(getNewTimesCardIcon(ctx, promoIconName)));
                }
            } else {
                leftBtn.setBtnIcons(Collections.singletonList(getTimesCardIcon(ctx, timesCard.getBtnTag())));
            }

        }
        if (ctx.isMultiCard()) {
            leftBtn.setPricePostfix("/次起");
        } else {
            leftBtn.setPricePostfix("/次");
        }
        leftBtn.setRedirectUrl(timesCardUrl);
        leftBtn.setDetailBuyType(2);
        return leftBtn;
    }

    public static DealBuyBtn getJoyTimesCardButton(DealCtx ctx) {
        CardSummaryBarDTO timesCard = ctx.getTimesCard();
        String btnTitle = String.format("%s次卡", timesCard.getTimes());

        String timesCardUrl = UrlHelper.getTimesCardBuyUrl(ctx, timesCard.getProductId(), timesCard.getChannelSource());

        DealBuyBtn leftBtn = new DealBuyBtn(true, btnTitle);
        leftBtn.setPriceStr(PriceHelper.dropLastZero(BuyButtonHelper.getTimesCardTruthPrice(ctx)));
        if (StringUtils.isNotEmpty(timesCard.getBtnTag())) {
            PriceDisplayDTO normalPrice = ctx.getPriceContext().getNormalPrice();
            if (normalPrice != null && normalPrice.getMarketPrice() != null) {
                String promoIconName = "每次省￥"
                        + normalPrice.getMarketPrice().subtract(new BigDecimal(leftBtn.getPriceStr()))
                        .setScale(1, RoundingMode.CEILING).stripTrailingZeros().toPlainString();
                leftBtn.setBtnIcons(Collections.singletonList(getNewTimesCardIcon(ctx, promoIconName)));
            }
        }
        if (ctx.isMultiCard()) {
            leftBtn.setPricePostfix("/次起");
        } else {
            leftBtn.setPricePostfix("/次");
        }
        leftBtn.setRedirectUrl(timesCardUrl);
        leftBtn.setDetailBuyType(2);
        return leftBtn;
    }

    public static DealBuyBtnIcon getPromoIcon(DealCtx ctx, String title) {
        String color = ctx.isMt() ? REDUCE_PRICE_COLOR_MT : REDUCE_PRICE_COLOR_DP;
        String bgColor = ctx.isMt() ? DEFAULT_BG_CARD_COLOR_MT : (ctx.isConvertColor() ? DEFAULT_BG_CARD_COLOR_DP_MRN : DEFAULT_BG_CARD_COLOR_DP);
        return new DealBuyBtnIcon(title, color, bgColor, BuyBtnType.BORDER.getValue());
    }

    private static DealBuyBtnIcon getDoubleButtonRightIcon(DealCtx ctx, String title) {
        return new DealBuyBtnIcon(title, MEMBER_DAY_PRICE_COLOR, ctx.isConvertColor() ? BORDER_CARD_COLOR_DP_MRN : BORDER_CARD_COLOR_DP, MEMBER_DAY_PRICE_COLOR, BuyBtnType.BORDER.getValue());
    }

    private static DealBuyBtnIcon getTimesCardIcon(DealCtx ctx, String title) {
        String color = ctx.isMt() ? TITLE_CARD_COLOR_MT : TITLE_CARD_COLOR_DP;
        boolean convertColor = ctx.isConvertColor();
        String bgColor = ctx.isMt() ? (convertColor ? BG_CARD_COLOR_MT_MRN : BG_CARD_COLOR_MT) : (convertColor ? BG_CARD_COLOR_DP_MRN : BG_CARD_COLOR_DP);
        String borderColor = ctx.isMt() ? BORDER_CARD_COLOR_MT : (convertColor ? BORDER_CARD_COLOR_DP_MRN : BORDER_CARD_COLOR_DP);
        return new DealBuyBtnIcon(title, color, bgColor, borderColor, BuyBtnType.BORDER.getValue());
    }

    private static DealBuyBtnIcon getNewTimesCardIcon(DealCtx ctx, String title) {
        String color = ctx.isMt() ? ENTERTAINMENT_NEW_TITLE_CARD_COLOR_MT : ENTERTAINMENT_NEW_TITLE_CARD_COLOR_DP;
        String borderColor = ctx.isMt() ? ENTERTAINMENT_NEW_TITLE_CARD_COLOR_MT : ENTERTAINMENT_NEW_TITLE_CARD_COLOR_DP;
        String bgColor = TRANSPARENT_COLOR;
        return new DealBuyBtnIcon(title, color, bgColor, borderColor, BuyBtnType.BORDER.getValue());
    }

    public static DealBuyBtnIcon getPinTuanTipsIcon(String title, boolean isLeft) {
        DealBuyBtnIcon icon = new DealBuyBtnIcon();
        icon.setTitle(title);
        if (isLeft) {
            icon.setTitleColor("#FFFFFF");
            icon.setBgColor("#FF2727");
        } else {
            icon.setTitleColor("#FF2727");
            icon.setBgColor("#FFE9E3");
        }
        return icon;
    }

    public static List<DealBuyBtnIcon> getWeakActivePinTuanTipsIcon(DealCtx context) {
        List<DealBuyBtnIcon> result = new ArrayList<>();
        result.add(new DealBuyBtnIcon("还差" + CostEffectivePinTuanUtils.getNeedMemberCount(context) + "人成团", "#FFFFFF", "#FF2727", "", BuyBtnType.BORDER.getValue()));
        return result;
    }

    public static DealBuyBtnIcon getNewNormalCardIcon(DealCtx ctx, String title) {
        String color = MEMBER_DAY_PRICE_COLOR;
        String borderColor = MEMBER_DAY_PRICE_COLOR;
        String bgColor = TRANSPARENT_COLOR;
        return new DealBuyBtnIcon(title, color, bgColor, borderColor, BuyBtnType.BORDER.getValue());
    }


    //获取不用价格JsonLabel描述
    public static String getUnUsedPriceJlDesc(String desc, BigDecimal price, boolean isApp, boolean strikeThrough) {
        List<TextItem> itemList = Lists.newArrayList();
        TextItem item1 = new TextItem();
        item1.setText(desc);
        item1.setTextsize(12);
        item1.setTextcolor(isApp ? TextItem.GRAY : TextItem.GRAY_H5);
        item1.setBackgroundcolor(isApp ? TextItem.TRANSPARENT_WHITE : TextItem.TRANSPARENT_WHITE_H5);
        itemList.add(item1);

        if (price != null) {
            TextItem item2 = new TextItem();
            item2.setText(PriceHelper.format(price));
            item2.setTextsize(12);
            item2.setTextcolor(isApp ? TextItem.GRAY : TextItem.GRAY_H5);
            item2.setBackgroundcolor(isApp ? TextItem.TRANSPARENT_WHITE : TextItem.TRANSPARENT_WHITE_H5);
            /**
             * 产品提需求取消划线：参见https://ones.sankuai.com/ones/product/13548/workItem/requirement/detail/7509176
             */
            //item2.setStrikethrough(strikeThrough);
            itemList.add(item2);
        }
        return JsonFacade.serialize(itemList);
    }

    //获取不用价格JsonLabel描述
    public static String getUnUsedPriceJlDescWithStrikethrough(String desc, BigDecimal price, boolean isApp, boolean strikeThrough) {
        List<TextItem> itemList = Lists.newArrayList();
        TextItem item1 = new TextItem();
        item1.setText(desc);
        item1.setTextsize(12);
        item1.setTextcolor(isApp ? TextItem.GRAY : TextItem.GRAY_H5);
        item1.setBackgroundcolor(isApp ? TextItem.TRANSPARENT_WHITE : TextItem.TRANSPARENT_WHITE_H5);
        itemList.add(item1);

        if (price != null) {
            TextItem item2 = new TextItem();
            item2.setText(PriceHelper.format(price));
            item2.setTextsize(12);
            item2.setTextcolor(isApp ? TextItem.GRAY : TextItem.GRAY_H5);
            item2.setBackgroundcolor(isApp ? TextItem.TRANSPARENT_WHITE : TextItem.TRANSPARENT_WHITE_H5);
            /**
             * 洗浴产品提需求展示划线：
             */
            item2.setStrikethrough(strikeThrough);
            itemList.add(item2);
        }
        return JsonFacade.serialize(itemList);
    }

    /**
     * 是否是健身通商品
     */
    public static boolean isFitnessCrossDeal(DealCtx dealCtx) {
        return dealCtx != null && dealCtx.isFitnessCrossDeal();
    }

    /**
     * 券融合
     *
     * @param dealCtx
     * @return
     */
    public static boolean isDetailCouponFusion(DealCtx dealCtx) {
        if (dealCtx == null || dealCtx.getChannelDTO() == null) {
            return false;
        }

        DealGroupChannelDTO channel = dealCtx.getChannelDTO();
        int categoryId = channel.getCategoryId();
        List<Integer> cateConfig = Lion.getList(LionConstants.COUPON_FUSION_CATEGORIES, Integer.class, new ArrayList<>());

        if (CollectionUtils.isNotEmpty(cateConfig) && cateConfig.contains(categoryId)) {
            return true;
        }

        ChannelDTO channelDTO = channel.getChannelDTO();

        if (channelDTO == null) {
            return false;
        }

        List<Integer> channelConfig = Lion.getList(LionConstants.COUPON_FUSION_CHANNELS, Integer.class, new ArrayList<>());

        return CollectionUtils.isNotEmpty(channelConfig) && channelConfig.contains(channelDTO.getChannelId());
    }

    public static boolean isJoy(DealCtx dealCtx) {

        Set<Integer> joyBuyBarPoiCategories = LionFacade.getSet(LionConstants.JOY_BUY_BAR_CATEGORY_IDS,
                Integer.TYPE, Collections.emptySet());

        return CollectionUtils.isNotEmpty(dealCtx.getPoiBackCategoryIds())
                && !Sets.intersection(joyBuyBarPoiCategories, dealCtx.getPoiBackCategoryIds()).isEmpty();

    }

    static boolean isOdpRequestSource(DealCtx dealCtx) {
        return DealCtxHelper.isOdpSource(dealCtx);
    }

    public static boolean isMtLiveMiniApp(DealCtx ctx) {
        return ctx.isMtLiveMinApp();
    }

    public static boolean isShoppingCart(DealCtx dealCtx) {
        //到综购物车提单页留资能力新增加购品类，douhu灰度结束后可以去掉该if判断分支，合并到else中
        if (GreyUtils.isShoppingCartNewCategory(dealCtx)) {
            return isMTPlatform(dealCtx)
                    && isMainApp(dealCtx)
                    && versionCheck(dealCtx)
                    && GreyUtils.hitNewShoppingCartAB(null, "MTShoppingCartBuyBarNew", dealCtx)
                    && isNotLive(dealCtx);
        } else {
            return isMTPlatform(dealCtx)
                    && isMainApp(dealCtx)
                    && versionCheck(dealCtx)
                    && GreyUtils.isShoppingCartCategory(dealCtx)
                    && GreyUtils.hitShoppingCartAB(null, "MTShoppingCartBuyBar", dealCtx)
                    && isNotLive(dealCtx);
        }
    }

    private static boolean isNotLive(DealCtx dealCtx) {
        return !Objects.equals(dealCtx.getRequestSource(), "mlive");
    }

    private static boolean isMTPlatform(DealCtx dealCtx) {
        return dealCtx.isMt();
    }

    private static boolean isMainApp(DealCtx dealCtx) {
        return DztgClientTypeEnum.MEITUAN_APP.equals(dealCtx.getEnvCtx().getDztgClientTypeEnum())
                || DztgClientTypeEnum.DIANPING_APP.equals(dealCtx.getEnvCtx().getDztgClientTypeEnum());
    }

    private static boolean versionCheck(DealCtx dealCtx) {
        return mrnVersionCheck(dealCtx) && appVersionCheck(dealCtx);
    }

    private static boolean mrnVersionCheck(DealCtx dealCtx) {
        return VersionUtils.isGreatEqualThan(dealCtx.getMrnVersion(), SHOPPING_CART_MRN_LIMITED_VERSION);
    }

    private static boolean appVersionCheck(DealCtx dealCtx) {
        return VersionUtils.isGreatEqualThan(dealCtx.getEnvCtx().getVersion(), SHOPPING_CART_APP_LIMITED_VERSION);
    }

    public static boolean xiYuShowMarketPrice(DealCtx dealCtx) {
        return GreyUtils.isXiYuShowMarketPrice(dealCtx);
    }

    public static boolean joyShowMarketPrice(DealCtx dealCtx) {
        return GreyUtils.isJoyShowMarketPrice(dealCtx);
    }

    /**
     * 判断是否是券后信息透传样式的底bar，影响了1. 横幅展示券信息 2. 按钮根据券的领取状态修改文案 3. 价格区根据券的领取状态修改文案
     *
     * @param dealCtx
     * @return
     */
    public static boolean isCouponBar(DealCtx dealCtx) {
        return GreyUtils.isCouponBar(dealCtx) && GreyUtils.hitCouponBarABC("DPCouponBar", "MTCouponBar", dealCtx);
    }

    public static boolean isBeautyBianMeiCoupon(DealCtx dealCtx) {
        return GreyUtils.isBeautyBianMeiCoupon(dealCtx) ;
    }

    public static boolean isShowZuLiaoMarketPrice(DealCtx dealCtx) {
        return dealCtx.getPriceContext().isZuLiaoButtonNewStyle();
    }

    static boolean isMemberExclusive(DealCtx dealCtx) {
        return GreyUtils.isMemberExclusive(dealCtx);
    }

    public static DealBuyBtnIcon getCardIcon(String title, boolean isMemberDay) {
        String titleColor = isMemberDay ? MEMBER_DAY_PRICE_COLOR : MEMBER_PRICE_COLOR;
        String style = isMemberDay ? MEMBER_DAY_PRICE_PIC : MEMBER_PRICE_PIC;
        return new DealBuyBtnIcon(title, titleColor, style, BuyBtnType.PICTURE.getValue());
    }

    public static DealBuyBtnIcon getEntertainmentCardIcon(String title) {
        String titleColor = ENTERTAINMENT_MEMBER_DAY_PRICE_COLOR;
        String borderColor = ENTERTAINMENT_MEMBER_DAY_PRICE_COLOR;
        String bgColor = TRANSPARENT_COLOR;

        return new DealBuyBtnIcon(title, titleColor, titleColor, bgColor, borderColor, BuyBtnType.BORDER.getValue());
    }

    public static boolean isFreeDeal(DealCtx ctx) {
        return ctx.isFreeDeal();
    }

    public static boolean isZeroResv(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper.isZeroResv(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        boolean zeroVaccineSwitch = Lion.getBoolean("com.sankuai.dzu.tpbase.dztgdetailweb","com.sankuai.dzu.tpbase.dztgdetailweb.zeroVaccine.switch",false);
        return ctx.getDealGroupDTO() != null 
                && ctx.getDealGroupDTO().getBasic() != null
                && Objects.nonNull(ctx.getDealGroupDTO().getBasic().getTradeType())
                && ctx.getDealGroupDTO().getBasic().getTradeType() == TradeTypeEnum.RESERVATION.getCode()
                && zeroVaccineSwitch;
    }

    public static boolean isEduOnlineCourseDeal(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper.isEduOnlineCourseDeal(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        if (ctx == null) {
            return false;
        }
        return LionConfigUtils.isEduOnlineDeal(ctx.getCategoryId(), getServiceTypeId(ctx.getDealGroupDTO()));
    }

    public static boolean isPrePayDeal(DealCtx ctx) {
        return DealUtils.isTradeAssuranceDeal(ctx) && DealUtils.isPrePayDeal(ctx);
    }

    public static boolean isLeadsDeal(DealCtx ctx) {
        return DealUtils.isLeadsDeal(ctx);
    }

    public static boolean isWeddingLeadsDeal(DealCtx ctx) {
        return DealUtils.isWeddingLeadsDeal(ctx);
    }

    private static Long getServiceTypeId(DealGroupDTO dealGroupDTO) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper.getServiceTypeId(com.sankuai.general.product.query.center.client.dto.DealGroupDTO)");
        if (dealGroupDTO == null || dealGroupDTO.getCategory() == null) {
            return null;
        }
        return dealGroupDTO.getCategory().getServiceTypeId();
    }

    private enum BuyBtnType {
        BORDER(1),
        PICTURE(2);

        private int value;

        BuyBtnType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }
}
