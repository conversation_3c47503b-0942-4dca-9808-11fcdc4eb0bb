package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.DecryptBiz;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.DecryptVO;
import com.dianping.poi.open.common.Result;
import com.dianping.poi.open.constants.RequestIdType;
import com.dianping.poi.open.service.OpenDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;

@Service
@Slf4j
public class DecryptFacade {

    @Resource
    private DecryptBiz decryptBiz;

    public DecryptVO decrypt(String encryptedStr, boolean isMt) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.facade.DecryptFacade.decrypt(java.lang.String,boolean)");
        return decryptBiz.decrypt(encryptedStr, isMt);
    }

}
