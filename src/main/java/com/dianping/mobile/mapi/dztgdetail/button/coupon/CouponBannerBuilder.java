package com.dianping.mobile.mapi.dztgdetail.button.coupon;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.constants.PlusIcons;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BannerTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBanner;
import com.dianping.mobile.mapi.dztgdetail.entity.CouponBannerInfo;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.PriceHelper;
import com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil;
import com.sankuai.dealuser.price.display.api.enums.CouponAssignStatusEnum;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class CouponBannerBuilder extends AbstractButtonBuilder {

    private static final long MILLISECONDS_OF_DAY = 24 * 60 * 60 * 1000;

    @Override
    public void build(DealCtx context, ButtonBuilderChain chain) {
        if (DealBuyHelper.isCouponBar(context)) {
            buildBanner(context);
        }
        chain.build(context);
    }

    private void buildBanner(DealCtx context) {
        // 横条已经存在则不展示
        if (context.getBuyBar().getBuyBanner() != null) {
            return;
        }
        PriceDisplayDTO dealPromoPrice = PriceHelper.getDealPromoPrice(context);
        if (CollectionUtils.isEmpty(dealPromoPrice.getUsedPromos())) {
            return;
        }
        CouponBannerInfo couponBannerInfo = buildCouponInfoBannerInfo(dealPromoPrice);
        if (couponBannerInfo.getShow() && couponBannerInfo.getEndTime() != null) {
            //  判断是否匹配推广通QCPX优惠券
            if (matchQcpx(dealPromoPrice.getUsedPromos())) {
                context.getBuyBar().setBuyBanner(buildQcpxCouponBanner(couponBannerInfo));
                return;
            }
            context.getBuyBar().setBuyBanner(buildCouponBanner(couponBannerInfo));
        }
    }

    @Override
    public void doBuild(DealCtx context, ButtonBuilderChain chain) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.button.coupon.CouponBannerBuilder.doBuild(DealCtx,ButtonBuilderChain)");

    }

    /**
     * 构造券横幅信息，其中有已领取的券则used设为true，否则设为false
     *
     * @param priceDisplayDTO
     * @return
     */
    private CouponBannerInfo buildCouponInfoBannerInfo(PriceDisplayDTO priceDisplayDTO) {
        List<PromoDTO> couponPromos = priceDisplayDTO.getUsedPromos();
        CouponBannerInfo couponBannerInfo = new CouponBannerInfo();
        if (CollectionUtils.isEmpty(couponPromos)) {
            couponBannerInfo.setShow(false);
            return couponBannerInfo;
        }

        BigDecimal totalAmount = BigDecimal.ZERO;
        boolean show = false;
        boolean used = false;
        Date endTime = new Date(Long.MAX_VALUE);
        //普通消费券和政府消费券判断领取方式不一样，时间取结束时间最早的
        for (PromoDTO couponPromo : couponPromos) {
            if (couponPromo.getIdentity().getPromoType() == PromoTypeEnum.COUPON.getType()) {
                show = true;
                if (!couponPromo.isCanAssign()) {
                    used = true;
                }
                totalAmount = totalAmount.add(couponPromo.getAmount());
                if (couponPromo.getEndTime() != null && couponPromo.getEndTime().before(endTime)) {
                    endTime = couponPromo.getEndTime();
                }
            } else if (couponPromo.getIdentity().getPromoType() == PromoTypeEnum.GOVERNMENT_CONSUME_COUPON.getType()) {
                show = true;
                if (couponPromo.getCouponAssignStatus() == CouponAssignStatusEnum.ASSIGNED.getCode()) {
                    used = true;
                }
                totalAmount = totalAmount.add(couponPromo.getAmount());
                if (couponPromo.getEndTime() != null && couponPromo.getEndTime().before(endTime)) {
                    endTime = couponPromo.getEndTime();
                }
            }
        }

        couponBannerInfo.setShow(show);
        couponBannerInfo.setUsed(used);
        couponBannerInfo.setAmount(totalAmount);
        if (endTime.before(new Date(Long.MAX_VALUE))) {
            couponBannerInfo.setEndTime(endTime);
        }

        return couponBannerInfo;
    }

    private DealBuyBanner buildCouponBanner(CouponBannerInfo couponBannerInfo) {
        DealBuyBanner banner = new DealBuyBanner();
        banner.setShow(true);
        banner.setBannerType(BannerTypeEnum.COUPON_INFO.getType());

        String amountStr = String.valueOf(couponBannerInfo.getAmount());
        StringBuilder content = new StringBuilder("你有" + amountStr + "元专属优惠券");
        String timeDifferStr = "";
        StringBuilder highlightStr=new StringBuilder(amountStr);
        long now = System.currentTimeMillis();
        if (couponBannerInfo.getUsed() && couponBannerInfo.getEndTime().getTime() > now) {
            content.append("，下单立享");
            Date sevenDaysLater = new Date(now + 7L * MILLISECONDS_OF_DAY);
            Date oneDayLater = new Date(now + MILLISECONDS_OF_DAY);
            if (couponBannerInfo.getEndTime().before(oneDayLater)) {
                //只有24小时之内才设置倒计时时间戳
                banner.setCountDownTs(couponBannerInfo.getEndTime().getTime());
                content.append("，仅剩");
            } else if (couponBannerInfo.getEndTime().before(sevenDaysLater)) {
                int days = (int) ((couponBannerInfo.getEndTime().getTime() - now) / MILLISECONDS_OF_DAY);
                timeDifferStr = String.valueOf(days);
                content.append("，仅剩").append(timeDifferStr).append("天 ");
                highlightStr.append("|").append(timeDifferStr);
            }
        } else {
            content.append("，点击下单领取可立享优惠");
        }

        banner.setContent(JsonLabelUtil.CouponInfoBannerJson(content.toString(), highlightStr.toString()));
        banner.setIconUrl(PlusIcons.COUPON_INFO);
        return banner;
    }

    private DealBuyBanner buildQcpxCouponBanner(CouponBannerInfo couponBannerInfo) {
        DealBuyBanner banner = new DealBuyBanner();
        banner.setShow(true);
        banner.setBannerType(BannerTypeEnum.COUPON_INFO.getType());
        String amountStr = String.valueOf(couponBannerInfo.getAmount());
        StringBuilder content = new StringBuilder("你有" + amountStr + "元专属优惠券，仅在当前门店可用，下单立享");
        StringBuilder highlightStr=new StringBuilder(amountStr);
        banner.setContent(JsonLabelUtil.CouponInfoBannerJson(content.toString(), highlightStr.toString()));
        banner.setIconUrl(PlusIcons.COUPON_INFO);
        return banner;
    }

    /**
     * 判断是否有Qcpx优惠
     * @param promoDTOS
     * @return
     */
    private static boolean matchQcpx(List<PromoDTO> promoDTOS) {
        if (CollectionUtils.isEmpty(promoDTOS)) {
            return false;
        }
        String materialInfoStr = promoDTOS.stream().filter(pro -> MapUtils.isNotEmpty(pro.getPromotionOtherInfoMap()) && pro.getPromotionOtherInfoMap().containsKey(PromotionPropertyEnum.ISSUE_SOURCE_LIMIT.getValue()))
                .map(promo -> promo.getPromotionOtherInfoMap().get(PromotionPropertyEnum.ISSUE_SOURCE_LIMIT.getValue()))
                .findFirst().orElse(null);
        if (StringUtils.isBlank(materialInfoStr)) {
            return false;
        }
        return "DZ_QCPX_PLATFORM".equals(materialInfoStr);
    }
}
