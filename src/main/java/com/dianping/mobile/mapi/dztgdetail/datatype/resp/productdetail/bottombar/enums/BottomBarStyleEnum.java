package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.enums;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.ProductBottomBarVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.StandardTradeBottomBarVO;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/3/16 14:09
 */
@Getter
public enum BottomBarStyleEnum {

    STANDARD_TRADE_STYLE(1, "标准交易", StandardTradeBottomBarVO.class);

    private final int code;

    private final String desc;

    private final Class<? extends ProductBottomBarVO> bottomBarVOClass;

    BottomBarStyleEnum(int code, String desc,
                       Class<? extends ProductBottomBarVO> bottomBarVOClass) {
        this.code = code;
        this.desc = desc;
        this.bottomBarVOClass = bottomBarVOClass;
    }

    public static BottomBarStyleEnum fromCode(int code) {
        for (BottomBarStyleEnum value : BottomBarStyleEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效code:" + code);
    }

    public static boolean containsCode(int code) {
        for (BottomBarStyleEnum value : BottomBarStyleEnum.values()) {
            if (value.getCode() == code) {
                return true;
            }
        }
        return false;
    }

    public static Map<Integer, String> buildCodeDescMap() {
        return Arrays.stream(BottomBarStyleEnum.values()).collect(Collectors.toMap(
                BottomBarStyleEnum::getCode,
                BottomBarStyleEnum::getDesc
        ));
    }

    public static Map<String, String> buildNameDescMap() {
        return Arrays.stream(BottomBarStyleEnum.values()).collect(Collectors.toMap(
                BottomBarStyleEnum::name,
                BottomBarStyleEnum::getDesc
        ));
    }

}