package com.dianping.mobile.mapi.dztgdetail.entity;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ActionTextVO;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-08-30
 * @desc 团详款式信息配置
 */
@Data
public class ExhibitImageConfig {
    /**
     * 商家平台款式类型
     */
    private Integer multiStyleType;
    /**
     * 常驻标签
     */
    private List<String> permanentTag;
    /**
     * 款式展示，1=单图展示；2=多图展示
     */
    private Integer itemDisplayStyle;
    /**
     * 相册页标题
     */
    private String title;
    
    private String styleTitle;
    /**
     * 相册页标题下方图标
     */
    private String descIcon;
    /**
     * 相册页标题下方描述
     */
    private String descText;
    /**
     * 固定宽高比，如果设置（如1:1），前端按网格样式展示款式列表，如果不设置则按瀑布流样式展示款式列表
     */
    private String fixedAspectRatio;
    /**
     * 款式展示的标签fieldCode
     */
    private List<String> multiStyleTagKey;
    /**
     * 展示结果不显示此标签数据
     */
    private List<String> excludeStyleTagKey = Lists.newArrayList();
    /**
     * 商家平台moduleKey
     */
    private String styleModuleKey;
    /**
     * 默认的图片最佳宽高比，作为款式信息没有宽高数据时的兜底处理
     */
    private String defaultImageBestScale;

    /**
     * 查看更多本团购参考款式
     */
    private String moreStyleText;

    /**
     * 查看更多本团购参考款式icon
     */
    private String moreStyleIcon;

    /**
     * 美甲款式频道页引导
     */
    private NailStyleChannelGuide channelNailStyle;

    /**
     * 查看更多热门款式交互动作引导语
     */
    private ActionTextVO actionTextVO;

    private String plcMoreStyleText;
    private String plcMoreStyleIcon;
    private String dpPlcMoreStyleUrl;
    private String mtPlcMoreStyleUrl;
    /**
     * 美团提单页链接
     */
    private String mtSubmitOrderUrl;
    /**
     * 大众点评提单页链接
     */
    private String dpSubmitOrderUrl;

    /**
     * 提交订单按钮文案
     */
    private String submitOrderText;
    /**
     * 美团添加购物车链接
     */
    private String mtAddShopCartUrl;
    /**
     * 大众点评添加购物车链接
     */
    private String dpAddShopCartUrl;
    /**
     * 添加购物车icon
     */
    private String addShopCartIcon;
}
