package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 健身通工具类
 */
public class FitnessCrossUtils {

    /**
     * 健身通团购属性key
     */
    private static final String FITNESS_CROSS_KEY = "dealGroupFitnessPassConfig";

    /**
     * 健身通团购属性value
     */
    private static final String FITNESS_CROSS_VALUE = "fitnessPass";

    /**
     * 判断是否是健身通商品
     */
    public static boolean isFitnessCrossDeal(DealCtx ctx) {
        if (ctx == null || ctx.getAttrs() == null) {
            return false;
        }
        return ctx.getAttrs().stream()
                .anyMatch(attrDTO ->
                        FITNESS_CROSS_KEY.equals(attrDTO.getName())
                                && !CollectionUtils.isEmpty(attrDTO.getValue())
                                && FITNESS_CROSS_VALUE.equals(attrDTO.getValue().get(0)));
    }

    /**
     * 判断是否是健身通商品
     */
    public static boolean isFitnessCrossDeal(DealGroupPBO dealGroupPBO) {
        return dealGroupPBO != null
                && dealGroupPBO.getBuyBar() != null
                && !CollectionUtils.isEmpty(dealGroupPBO.getBuyBar().getBuyBtns())
                && dealGroupPBO.getBuyBar().getBuyBtns().get(0) != null
                && "使用1张健身通兑换券".equals(dealGroupPBO.getBuyBar().getBuyBtns().get(0).getBtnTag());
    }

    /**
     * 健身通团详html
     */
    public static String buildDealBaseContent(String oldHtml) {
        if (StringUtils.isEmpty(oldHtml)) {
            return null;
        }
        oldHtml = oldHtml.replaceAll("\\d+元", "");
        return oldHtml.replaceAll("[\\u4e00-\\u9fa5]*价", "");
    }

}
