package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import java.util.Collections;
import java.util.List;

public class Version implements Comparable {
    public static final Version V10_0_800 = new Version("10.0.800");
    public static final Version MT_V10_8_400 = new Version("10.0.800");
    public static final Version DP_V10_27_0 = new Version("10.27.0");

    public static final Version MT_V10_8_200 = new Version("10.8.200");
    public static final Version DP_V10_26_0 = new Version("10.26.0");

    private final int[] nums;

    public Version(String num) {
        List<Integer> list = parseToIntList(num, "\\.");
        nums = new int[list.size()];
        int index = -1;
        for (Integer i : list) {
            nums[++index] = i;
        }
    }

    public static List<Integer> parseToIntList(String str, String regex) {
        if (StringUtils.isBlank(str)) {
            return Collections.EMPTY_LIST;
        } else {
            str = str.trim().replace("\"", "");
            String[] strArr = str.split(regex);
            List<Integer> numList = Lists.newArrayListWithCapacity(strArr.length);

            for (int i = 0; i < strArr.length; ++i) {
                numList.add(NumberUtils.toInt(strArr[i]));
            }

            return numList;
        }
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || !(o instanceof Version)) {
            return false;
        }
        Version target = (Version) o;
        if (target.nums.length != this.nums.length) {
            return false;
        }
        for (int i = 0; i < this.nums.length; i++) {
            if (this.nums[i] != target.nums[i]) {
                return false;
            }
        }
        return true;
    }

    @Override
    public int compareTo(Object o) {
        if (o == null || !(o instanceof Version)) {
            return 1;
        }
        Version target = (Version) o;
        int index = -1;
        for (int i : this.nums) {
            if (target.nums.length <= ++index) {
                return 1;
            }
            int result = i - target.nums[index];
            if (result != 0) {
                return result;
            }
        }
        if (target.nums.length > ++index) {
            return -1;
        }
        return 0;
    }

    public boolean gatherEqual(Version v) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.util.Version.gatherEqual(com.dianping.mobile.mapi.dztgdetail.util.Version)");
        return compareTo(v) >= 0;
    }

    public boolean gather(Version v) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.util.Version.gather(com.dianping.mobile.mapi.dztgdetail.util.Version)");
        return compareTo(v) > 0;
    }

    public boolean lessEqual(Version v) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.util.Version.lessEqual(com.dianping.mobile.mapi.dztgdetail.util.Version)");
        return compareTo(v) <= 0;
    }

    public boolean less(Version v) {
        return compareTo(v) < 0;
    }

    public static boolean idlePromoLimited(DealCtx ctx) {
        return new Version(ctx.getEnvCtx().getVersion()).less(ctx.isMt() ? MT_V10_8_400 : DP_V10_27_0);
    }

    public int[] getVersionNum() {
        return nums;
    }
}
