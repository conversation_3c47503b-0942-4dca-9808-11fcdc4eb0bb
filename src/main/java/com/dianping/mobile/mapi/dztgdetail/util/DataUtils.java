package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.List;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2023-02-22-9:02 PM
 */
public class DataUtils {

    public static List<String> toList(String source, String split) {
        if (StringUtils.isEmpty(source)) {
            return Lists.newArrayList();
        }
        String[] splitArr = source.split(split);
        return toList(splitArr, true);
    }

    public static List<String> toList(String[] split) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.util.DataUtils.toList(java.lang.String[])");
        return toList(split, false);
    }

    public static List<String> toList(String[] split, boolean trim) {
        if (split == null || split.length == 0) {
            return Lists.newArrayList();
        }
        List<String> list = Lists.newArrayList();
        for (String s : split) {
            if (trim) {
                if (s == null) {
                    list.add(null);
                } else {
                    list.add(s.trim());
                }
            } else {
                list.add(s);
            }
        }
        return list;
    }

    public static <R, T> List<R> partitionInvoke(Collection<T> paramList, Function<List<T>, List<R>> func2, int pageSize) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.util.DataUtils.partitionInvoke(java.util.Collection,java.util.function.Function,int)");
        if (CollectionUtils.isEmpty(paramList)) {
            return Lists.newArrayList();
        }
        List<List<T>> queryList = Lists.partition(Lists.newArrayList(paramList), pageSize);
        List<R> resultList = Lists.newArrayList();
        for (List<T> query : queryList) {
            List<R> result = func2.apply(query);
            if (CollectionUtils.isNotEmpty(result)) {
                resultList.addAll(result);
            }
        }
        return resultList;
    }

}
