package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedRecommendStrategy;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2024/10/23
 */
@Service
public class AbBizBuilderService {
    @Resource
    private DouHuBiz douHuBiz;

    public RelatedRecommendStrategy buildRecommendStrategy(DealCtx ctx) {
        RelatedRecommendStrategy relatedRecommendStrategy = new RelatedRecommendStrategy();
        // 命中隐藏同店推荐类目，则不返回推荐数据
        boolean hitHideInShopRecommendCategoryIds = LionConfigUtils.hitHideInShopRecommendCategoryIds(ctx.getCategoryId());
        if (hitHideInShopRecommendCategoryIds) {
            return relatedRecommendStrategy;
        }
        if (ctx.getEnvCtx().judgeMainApp() || ctx.getEnvCtx().isMtWxMainMini()) {
            // 同店推荐实验
            RelatedRecommendStrategy inShopRelatedRecommendStrategy = handleInShopRecommendExp(ctx, relatedRecommendStrategy);
            // 跨店推荐实验
            RelatedRecommendStrategy crossShopRelatedRecommendStrategy = handleCrossShopRecommendExp(ctx, inShopRelatedRecommendStrategy);
            return crossShopRelatedRecommendStrategy;
        }
        return relatedRecommendStrategy;
    }

    private RelatedRecommendStrategy handleInShopRecommendExp(DealCtx ctx, RelatedRecommendStrategy relatedRecommendStrategy) {
        String moduleName = ctx.isMt() ? "MtInRecommendStrategy" : "DpInRecommendStrategy";
        if(ctx.getEnvCtx().isMtWxMainMini()){
            moduleName ="MtWxMiniInRecommendStrategy";
        }
        ModuleAbConfig moduleAbConfig = douHuBiz.getAbExpResultByUuidAndDpidV2(ctx, moduleName);
        List<ModuleAbConfig> moduleAbConfigs = ctx.getModuleAbConfigs();
        if (CollectionUtils.isEmpty(moduleAbConfigs)) {
            moduleAbConfigs = new ArrayList<>();
        }
        if (Objects.nonNull(moduleAbConfig)) {
            moduleAbConfigs.add(moduleAbConfig);
            ctx.setModuleAbConfigs(moduleAbConfigs);
        }
        if (Objects.isNull(moduleAbConfig) || CollectionUtils.isEmpty(moduleAbConfig.getConfigs())) {
            relatedRecommendStrategy.setHitInShopRec(false);
            return relatedRecommendStrategy;
        }

        String expResult = moduleAbConfig.getConfigs().get(0).getExpResult();
        if (expResult.contains("c")) {
            relatedRecommendStrategy.setHitInShopRec(true);
            return relatedRecommendStrategy;
        }
        return relatedRecommendStrategy;
    }

    private RelatedRecommendStrategy handleCrossShopRecommendExp(DealCtx ctx, RelatedRecommendStrategy relatedRecommendStrategy) {
        String moduleName = ctx.isMt() ? "MtRecommendStrategy" : "DpRecommendStrategy";
        if(ctx.getEnvCtx().isMtWxMainMini()){
            moduleName ="MtWxMiniRecommendStrategy";
        }
        ModuleAbConfig moduleAbConfig = douHuBiz.getAbExpResultByUuidAndDpidV2(ctx, moduleName);
        List<ModuleAbConfig> moduleAbConfigs = ctx.getModuleAbConfigs();
        if (CollectionUtils.isEmpty(moduleAbConfigs)) {
            moduleAbConfigs = new ArrayList<>();
        }
        if (Objects.nonNull(moduleAbConfig)) {
            moduleAbConfigs.add(moduleAbConfig);
            ctx.setModuleAbConfigs(moduleAbConfigs);
        }
        relatedRecommendStrategy.setAbConfigModel(moduleAbConfig);
        if (Objects.isNull(moduleAbConfig) || CollectionUtils.isEmpty(moduleAbConfig.getConfigs())) {
            relatedRecommendStrategy.setHitCrossShopRec(false);
            relatedRecommendStrategy.setHitNewRecommendStrategy(false);
            return relatedRecommendStrategy;
        }

        String expResult = moduleAbConfig.getConfigs().get(0).getExpResult();
        if (expResult.contains("c") || expResult.contains("d") || expResult.contains("e") || expResult.contains("f") || expResult.contains("g")) {
            relatedRecommendStrategy.setHitCrossShopRec(true);
            relatedRecommendStrategy.setHitNewRecommendStrategy(true);
            return relatedRecommendStrategy;
        }
        return relatedRecommendStrategy;
    }
}
