package com.dianping.mobile.mapi.dztgdetail.common.enums;

import lombok.Getter;

/**
 * 团详渠道枚举
 *
 * @Author: z<PERSON><PERSON><PERSON><PERSON><PERSON>@meituan.com
 * @Date: 2023/12/18
 */
@Getter
public enum PageSourceEnum {
    PS_UNKNOWN(-100, "unknown", ""),
    PS_DPLIVE_1050559(1000001,  "dplive_1050559", ""),
    PS_LEMARKETING(1000002, "lemarketing", ""),
    PS_EXCLUSIVEDEDUCTION(1000003, "exclusiveDeduction", ""),
    PS_OFFLINE_BABY_SCAN(1000004, "offline_baby_scan", ""),
    PS_DPLIVE_1079812(1000005, "dplive_1079812", ""),
    PS_COST_EFFECTIVE(1000006, "cost_effective", ""),
    PS_DPLIVE_1080372(1000007, "dplive_1080372", ""),
    PS_DPLIVE_1077277(1000008, "dplive_1077277", ""),
    PS_PRODUCTSHELF(1000009, "productshelf", ""),
    PS_WTHZ(1000010, "wthz", ""),
    PS_DP_XIANSHIMIAOSHA(1000011, "dp_xianshimiaosha", ""),
    PS_YOUHUIMAMINI(1000012, "youhuimaMini", ""),
    PS_IM(1000013, "IM", ""),
    PS_DPLIVE_1077468(1000014, "dplive_1077468", ""),
    PS_DPLIVE_1078315(1000015, "dplive_1078315", ""),
    PS_DPLIVE_1078834(1000016, "dplive_1078834", ""),
    PS_0(1000017, "0", ""),
    PS_DPLIVE_1078671(1000018, "dplive_1078671", ""),
    PS_1(1000019, "1", ""),
    PS_2(1000020, "2", ""),
    PS_200(1000021, "200", ""),
    PS_123(1000022, "123", ""),
    PS_3(1000023, "3", ""),
    PS_DPLIVE_1080225(1000024, "dplive_1080225", ""),
    PS_MALLFOODPOISHELF(1000025, "mallFoodPoiShelf", ""),
    PS_DPLIVE_1078795(1000026, "dplive_1078795", ""),
    PS_DZSECKILLLIST(1000027, "DZSecKillList", ""),
    PS_DPLIVE_1080147(1000028, "dplive_1080147", ""),
    PS_DPLIVE_1078830(1000029, "dplive_1078830", ""),
    PS_TRUE(1000030, "true", ""),
    PS_DPLIVE_1079081(1000031, "dplive_1079081", ""),
    PS_ODP(1000032, "Odp", ""),
    PS_ACTIVITY(1000033, "activity", ""),
    PS_COST_EFF(1000034, "cost_eff", ""),
    PS_DPLIVE_1078821(1000035, "dplive_1078821", ""),
    PS_DPLIVE_1080150(1000036, "dplive_1080150", ""),
    PS_DPLIVE_1078706(1000037, "dplive_1078706", ""),
    PS_MALLFOODPOISHELF1(1000038, "mallFoodPoiShelf1", ""),
    PS_DPLIVE_1077179(1000039, "dplive_1077179", ""),
    PS_DPLIVE_1078784(1000040, "dplive_1078784", ""),
    PS_MINI_VISION(1000041, "mini_vision", ""),
    PS_CAIXI(1000042, "caixi", ""),
    PS_ABC(1000043, "abc", ""),
    PS_DPLIVE_1077447(1000044, "dplive_1077447", ""),
    PS_MLIVE(1000045, "mlive", ""),
    PS_DPLIVE_1079266(1000046, "dplive_1079266", ""),
    PS_JHS(1000047, "jhs", ""),
    PS_DPLIVE_1028199(1000048, "dplive_1028199", ""),
    PS_DZ_COST_EFFECTIVE(1000049, "dz_cost_effective", ""),
    PS_ZHIYINGDAREN(1000050, "zhiyingdaren", ""),
    PS_HOMEPAGE(1000051, "homepage", ""),
    ;

    private int id;
    private String name;
    private String desc;

    PageSourceEnum(int id, String name, String desc) {
        this.id = id;
        this.name = name;
        this.desc = desc;
    }
    
    public PageSourceEnum getIdByName(String name) {
        for (PageSourceEnum pageSource : PageSourceEnum.values()) {
            if (pageSource.name.equals(name)) {
                return pageSource;
            }
        }
        return null;
    }

    public PageSourceEnum getNameById(int id) {
        for (PageSourceEnum pageSourceEnum : PageSourceEnum.values()) {
            if (pageSourceEnum.id == id) {
                return pageSourceEnum;
            }
        }
        return null;
    }
}
