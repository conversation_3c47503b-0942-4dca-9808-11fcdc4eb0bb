package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.cat.Cat;
import com.dianping.deal.shop.dto.DealGroupDTO;
import com.dianping.deal.shop.dto.ShopOnlineDealGroup;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;

public class ShopDealsHelper {

    private static final String DP_APP_DEAL_DETAIL_URL = "dianping://tuandeal?id=%s";

    public static void removeDuplicateDealGroupDTO(
            Map<Integer, DealGroupDTO> sameDealGroupDTOMap, Map<Integer, DealGroupDTO> otherDealGroupDTOMap) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.helper.ShopDealsHelper.removeDuplicateDealGroupDTO(java.util.Map,java.util.Map)");
        if (MapUtils.isEmpty(sameDealGroupDTOMap) || MapUtils.isEmpty(otherDealGroupDTOMap)) {
            return;
        }
        List<Integer> sameDealGroupIds = Lists.newArrayList(sameDealGroupDTOMap.keySet());
        for (int dealGroupId : sameDealGroupIds) {
            if (otherDealGroupDTOMap.containsKey(dealGroupId)) {
                otherDealGroupDTOMap.remove(dealGroupId);
            }
        }
    }

    /**
     * 去除其他门店中和本门店中相同的团单
     * @param sameDealGroupDTOMap 本门店的团单
     * @param otherDpShopId2DealGroupMap 其他门店的团单
     */
    public static void removeDuplicateDealGroupDTO0(
            Map<Integer, DealGroupDTO> sameDealGroupDTOMap, Map<Long, Map<Integer, DealGroupDTO>> otherDpShopId2DealGroupMap) {
        if (MapUtils.isEmpty(sameDealGroupDTOMap) || MapUtils.isEmpty(otherDpShopId2DealGroupMap)) {
            return;
        }
        List<Integer> sameDealGroupIds = Lists.newArrayList(sameDealGroupDTOMap.keySet());
        for (int dealGroupId : sameDealGroupIds) {
            for (Map<Integer, DealGroupDTO> otherDealGroupDTOMap : otherDpShopId2DealGroupMap.values()) {
                if (otherDealGroupDTOMap.containsKey(dealGroupId)) {
                    otherDealGroupDTOMap.remove(dealGroupId);
                }
            }
        }
    }

    public static Map<Integer, DealGroupDTO> getOtherShopOnlineDealGroup(Map<Long, ShopOnlineDealGroup> tempMap, long shopId) {
        Map<Integer, DealGroupDTO> result = Maps.newHashMap();
        if (MapUtils.isEmpty(tempMap)) {
            return result;
        }
        if (tempMap.containsKey(shopId)) {
            tempMap.remove(shopId);
        }
        for (ShopOnlineDealGroup shopOnlineDealGroup : tempMap.values()) {
            if (shopOnlineDealGroup == null || CollectionUtils.isEmpty(shopOnlineDealGroup.getDealGroups())) {
                continue;
            }
            for (DealGroupDTO dealGroupDTO : shopOnlineDealGroup.getDealGroups()) {
                if (dealGroupDTO != null) {
                    result.put(dealGroupDTO.getDealGroupId(), dealGroupDTO);
                }
            }
        }
        return result;
    }


    /**
     * 获取其他的（非本团购门店）门店团购信息
     * @param dpShopId2DealGroupMap 点评门店id和团购信息的映射
     * @param dpShopIdLong 本团购点评门店id
     * @return 其他的（非本团购门店）门店团购信息，key为门店id，value为团购信息的映射
     */
    public static Map<Long, Map<Integer, DealGroupDTO>> getOtherShopOnlineDealGroup0(
            Map<Long, ShopOnlineDealGroup> dpShopId2DealGroupMap, long dpShopIdLong) {
        Map<Long, Map<Integer, DealGroupDTO>> result = Maps.newHashMap();
        if (MapUtils.isEmpty(dpShopId2DealGroupMap)) {
            return result;
        }
        if (dpShopId2DealGroupMap.containsKey(dpShopIdLong)) {
            dpShopId2DealGroupMap.remove(dpShopIdLong);
        }
        for (Map.Entry<Long, ShopOnlineDealGroup> entry : dpShopId2DealGroupMap.entrySet()) {
            long shopId = entry.getKey();
            ShopOnlineDealGroup shopOnlineDealGroup = entry.getValue();
            if (shopOnlineDealGroup == null || CollectionUtils.isEmpty(shopOnlineDealGroup.getDealGroups())) {
                continue;
            }
            Map<Integer, DealGroupDTO> id2DealGroupMap = Maps.newHashMap();
            for (DealGroupDTO dealGroupDTO : shopOnlineDealGroup.getDealGroups()) {
                if (dealGroupDTO != null) {
                    id2DealGroupMap.put(dealGroupDTO.getDealGroupId(), dealGroupDTO);
                }
            }
            result.put(shopId, id2DealGroupMap);
        }
        return result;
    }

    public static Map<Integer, DealGroupDTO> getSameShopOnlineDealGroup(Map<Long, ShopOnlineDealGroup> tempMap, long shopId) {
        Map<Integer, DealGroupDTO> result = Maps.newHashMap();
        if (MapUtils.isEmpty(tempMap)) {
            return result;
        }
        ShopOnlineDealGroup shopOnlineDealGroup = tempMap.get(shopId);
        if (shopOnlineDealGroup == null || CollectionUtils.isEmpty(shopOnlineDealGroup.getDealGroups())) {
            return result;
        }
        for (DealGroupDTO dealGroupDTO : shopOnlineDealGroup.getDealGroups()) {
            if (dealGroupDTO != null) {
                result.put(dealGroupDTO.getDealGroupId(), dealGroupDTO);
            }
        }
        return result;
    }

    public static String getDpAppDealDetailUrl(int dealGroupId) {
        return String.format(DP_APP_DEAL_DETAIL_URL, dealGroupId);
    }

}
