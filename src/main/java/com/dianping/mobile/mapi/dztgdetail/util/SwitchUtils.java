package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.constants.MttgDetailLionKeys;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by wa<PERSON><PERSON><PERSON> on 16/4/25.
 */
public class SwitchUtils {

    public static boolean isInBook(String showType) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.util.SwitchUtils.isInBook(java.lang.String)");
        String categoryIds = Lion.get(LionConstants.BOOK_SHOW_TYPES);
        if (StringUtils.isBlank(categoryIds)) {
            return false;
        }
        return ArrayUtils.contains(categoryIds.split(","), showType);
    }

    public static boolean isAllNotDpOrder() {
        return Lion.getBooleanValue(MttgDetailLionKeys.ALL_NOT_DPORDER, Boolean.FALSE);
    }

    public static boolean isListDealByIdsDegrade() {
        return Lion.getBooleanValue(MttgDetailLionKeys.LIST_DEAL_BY_IDS_DEGRADE, Boolean.FALSE);
    }

    public static boolean isAllNotDzx() {
        return Lion.getBooleanValue(MttgDetailLionKeys.ALL_NOT_DZX, Boolean.FALSE);
    }

    public static boolean isStructedDetailEnable() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.util.SwitchUtils.isStructedDetailEnable()");
        return Lion.getBooleanValue(MttgDetailLionKeys.STRUCTED_DETAIL_ENABLE, Boolean.FALSE);
    }

    public static boolean isBeautyHairEnable() {
        return Lion.getBooleanValue(MttgDetailLionKeys.BEAUTY_HAIR_STRUCTURE_ENABLE, Boolean.FALSE);
    }

    public static boolean isBeautyNailEnable() {
        return Lion.getBooleanValue(MttgDetailLionKeys.BEAUTY_NAIL_STRUCTURE_ENABLE, Boolean.FALSE);
    }

    public static boolean isUnifiedModuleEnable() {
        return Lion.getBooleanValue(MttgDetailLionKeys.UNIFIED_MODULE_ENABLE, Boolean.FALSE);
    }

    public static boolean isBeautyBagEnable() {
        return Lion.getBooleanValue(MttgDetailLionKeys.BEAUTY_BAG_ENABLE, Boolean.FALSE);
    }

    public static boolean isMemberExclusiveEnable() {
        return Lion.getBooleanValue(MttgDetailLionKeys.MEMBER_EXCLUSIVE_ENABLE, Boolean.FALSE);
    }

    public static boolean isPoiShopCategoryCacheEnable() {
            return Lion.getBooleanValue(MttgDetailLionKeys.POI_SHOP_CATEGORY_CACHE_ENABLE, Boolean.FALSE);
     }

}
