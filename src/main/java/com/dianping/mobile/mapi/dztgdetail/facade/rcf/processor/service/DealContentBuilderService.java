package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.cat.Cat;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.haima.client.request.HaimaRequest;
import com.dianping.haima.client.response.HaimaResponse;
import com.dianping.haima.entity.haima.HaimaConfig;
import com.dianping.haima.entity.haima.HaimaContent;
import com.dianping.json.facade.JsonFacade;
import com.dianping.lion.client.Lion;
import com.dianping.lion.facade.LionFacade;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.headerpic.HeaderPicProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.HaimaWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ContentType;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ImageScaleEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImageSize;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImageTagVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.SpritePicVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.DigestInfoDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.ExhibitContentDTO;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.ImageHelper;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.video.DealGroupVideoDTO;
import com.sankuai.general.product.query.center.client.dto.video.ExtendVideoDTO;
import com.sankuai.general.product.query.center.client.dto.video.SpriteImageDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.meta.BatchQueryTagValueResponseDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.meta.TagValueResponseReader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2024/5/18
 */
@Component
@Slf4j
public class DealContentBuilderService {
    private static final String PIC_WIDTH = "width";
    private static final String PIC_HEIGHT = "height";
    // 雪碧图 行列数： 10
    private static final int TEN = 10;
    public static final String COLON_SIGN = ":";

    @Autowired
    private Map<String, HeaderPicProcessor> headerPicProcessorMap;
    @Autowired
    private DouHuBiz douHuBiz;
    @Resource
    private HaimaWrapper haimaWrapper;

    public List<ContentPBO> getContent(final DealGroupBaseDTO dealGroupBaseDTO, final boolean isMt, DealCtx ctx, DealGroupPBO dealGroupPBO) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.DealContentBuilderService.getContent(DealGroupBaseDTO,boolean,DealCtx,DealGroupPBO)");
        if (dealGroupBaseDTO == null) {
            return null;
        }
        List<ContentPBO> result = Lists.newArrayList();
        try {
            if (LionConfigUtils.getHeadVideoIterationSwitch()) {
                // 增加开关
                // 团购视频模块迭代
                assembleVideosIteration(result, ctx);
            } else {
                assembleVideo(dealGroupBaseDTO, isMt, ctx, result);
            }
            Integer width = getPicSize(ctx, PIC_WIDTH);
            Integer height = getPicSize(ctx, PIC_HEIGHT);
            if (width == null || height == null) {
                width = ImageHelper.getOriginalImageWidth();
                height = ImageHelper.getOriginalImageHeight();
            }
            // 添加放心种封面
            addAssurePlantCoverPic(ctx,width, height, result);
            if (StringUtils.isNotBlank(dealGroupBaseDTO.getDefaultPic())) {
                String picUrl = ImageHelper.format(dealGroupBaseDTO.getDefaultPic(), width, height, isMt);
                ContentPBO defPic = new ContentPBO(ContentType.PIC.getType(), picUrl);
                result.add(defPic);
            }
            if (StringUtils.isNotBlank(dealGroupBaseDTO.getDealGroupPics())) {
                final int finalWidth = width;
                final int finalHeight = height;
                List<ContentPBO> otherPic = Arrays.stream(dealGroupBaseDTO.getDealGroupPics().split("\\|"))
                        .filter(input -> input != null && !dealGroupBaseDTO.getDefaultPic().equals(input))
                        .map(input -> new ContentPBO(ContentType.PIC.getType(), ImageHelper.format(input, finalWidth, finalHeight, isMt)))
                        .collect(Collectors.toList());
                result.addAll(otherPic);
            }

            int categoryId = ctx.getCategoryId();
            String headerPicProcessorKey = LionConfigUtils.getHeaderPicProcessor(categoryId);
            HeaderPicProcessor headerPicProcessor = headerPicProcessorMap.get(headerPicProcessorKey);
            dealGroupPBO.setExhibitContents(getExhibitContent(ctx));
            headerPicProcessor.fillPicScale(ctx, result, dealGroupPBO);
            DealGroupCategoryDTO categoryDTO = ctx.getDealGroupDTO().getCategory();
            // 如果是新穿戴甲团单，则不展示头图
            if (DealUtils.isNewWearableNailDeal(ctx)) {
                return null;
            }
            // 有VR信息，头图二次处理
            if (ctx.getVrUrl() != null) {
                assembleVrInfo(result, ctx);
            }
        } catch (Exception e) {
            log.error("[DealContentBuilderService] getContent error", e);
        }
        return result;
    }

    // 放心种封面
    private void addAssurePlantCoverPic(DealCtx ctx,Integer width, Integer height, List<ContentPBO> result) {
        String imPlantHeadPic = getPlatFormHeadPic(ctx, LionConfigUtils.getImplantBrandHeadPic());
        if (StringUtils.isNotBlank(imPlantHeadPic)) {
            String picUrl = ImageHelper.format(imPlantHeadPic, width, height, ctx.isMt());
            ContentPBO defPic = new ContentPBO(ContentType.PIC.getType(), picUrl);
            result.add(defPic);
        }
    }
    /**
     * 获取放心种头图封面
     * @param ctx
     * @return
     */
    private String getPlatFormHeadPic(DealCtx ctx, Map<String, List> brandHeadPicConfigs) {
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        if (Objects.isNull(dealGroupDTO) || CollectionUtils.isEmpty(dealGroupDTO.getAttrs()) || MapUtils.isEmpty(brandHeadPicConfigs)) {
            return StringUtils.EMPTY;
        }
        AttrDTO implantBandAttr = dealGroupDTO.getAttrs().stream().filter(e->StringUtils.equals(e.getName(), "implant_brand")).findFirst().orElse(null);
        if (DealCtxHelper.isAssuredImplant(ctx, LionConfigUtils.getInsuranceInfoDetailConfig()) && Objects.nonNull(implantBandAttr)) {
            List<String> brands = implantBandAttr.getValue();
            Set<String> headPics= brandHeadPicConfigs.keySet();
            for (String headPic : headPics) {
                List<String> brandNames = brandHeadPicConfigs.get(headPic);
                if (CollectionUtils.containsAll(brandNames, brands)) {
                    return headPic;
                }
            }
        }
        return StringUtils.EMPTY;
    }
    private void assembleVrInfo(List<ContentPBO> result, DealCtx ctx) {
        if (StringUtils.isBlank(ctx.getVrUrl())) {
            return;
        }
        if (CollectionUtils.isEmpty(result) || result.get(0) == null) {
            return;
        }
        ContentPBO headPic = result.get(0);
        if (headPic.getType() == ContentType.PIC.getType()) {
            headPic.setType(ContentType.VR.getType());
            headPic.setVrUrl(ctx.getVrUrl());
            headPic.setVrIconUrl("https://img.meituan.net/dpmobile/4c7918c4a63cecccedf8d355d2e04bc251188.webp");
        }
    }

    private ExhibitContentDTO getExhibitContent(DealCtx ctx) {
        if (Objects.isNull(ctx.getExhibitContentDTO()) || CollectionUtils.isEmpty(ctx.getExhibitContentDTO().getItems())) {
            if (LionConfigUtils.isHideStylePicturesBar(ctx.getCategoryId())) {
                return null;
            }
            return ctx.getExhibitContentDTO();
        }
        // 非新款式接口类目，直接返回款式模型即可
        if (!LionConfigUtils.useNewExhibitCategoryIds(ctx.getCategoryId())) {
            return ctx.getExhibitContentDTO();  // 直接返回
        }
        // 眼镜款式 对照组不展示款式
        String module = ctx.isMt() ? "Mt" + ctx.getCategoryId() + "ExhibitExp" : "Dp" + ctx.getCategoryId() + "ExhibitExp";
        if (LionConfigUtils.useNewExhibitCategoryIds(ctx.getCategoryId())) {
            ModuleAbConfig moduleAbConfig = douHuBiz.getAbByUnionIdV2(ctx.getEnvCtx().getUnionId(), module, ctx.isMt());
            if(moduleAbConfig != null && CollectionUtils.isNotEmpty(moduleAbConfig.getConfigs())) {
                ctx.setGlassesExhibitAbConfig(moduleAbConfig);
                if(!"c".equals(moduleAbConfig.getConfigs().get(0).getExpResult())) {
                    return null;
                }
            }
        }

        // 拼装标签信息
        ExhibitContentDTO exhibitContentDTO = ctx.getExhibitContentDTO();
        //眼镜款式对于标签存在特殊逻辑处理
        if (ctx.getCategoryId() == 406) {
            BatchQueryTagValueResponseDTO tagValueResponseDTO = ctx.getTagValueResponseDTO();
            TagValueResponseReader reader = TagValueResponseReader.read(tagValueResponseDTO);
            Map<Long, String> tagMapping = Maps.newHashMap();
            Map<Long, String> themeMap = reader.getTagMapping(23, "material_glasses_frame_style", "styleTheme"); // tagId -> tag label
            if (MapUtils.isNotEmpty(themeMap)) {
                tagMapping.putAll(themeMap);
            }
            Map<Long, String> materialMap = reader.getTagMapping(23, "material_glasses_frame_style", "styleMaterial"); // tagId -> tag label
            if (MapUtils.isNotEmpty(materialMap)) {
                tagMapping.putAll(materialMap);
            }
            CollectionUtils.emptyIfNull(exhibitContentDTO.getItems()).forEach(item -> {
                List<ImageTagVO> tags = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(item.getStyleMaterialList())) {
                    ImageTagVO imageTagVO = new ImageTagVO();
                    String label = tagMapping.get(item.getStyleMaterialList().get(0));
                    if (StringUtils.isBlank(label)) return;
                    imageTagVO.setName(label);
                    imageTagVO.setStyle(0);
                    tags.add(imageTagVO);
                }

                if (CollectionUtils.isNotEmpty(item.getStyleThemeList())) {
                    ImageTagVO imageTagVO = new ImageTagVO();
                    String label = tagMapping.get(item.getStyleThemeList().get(0));
                    if (StringUtils.isBlank(label)) return;
                    imageTagVO.setName(label);
                    imageTagVO.setStyle(0);
                    tags.add(imageTagVO);
                }

                if (Objects.nonNull(item.getRecommended()) && item.getRecommended() > 0) {
                    ImageTagVO imageTagVO = new ImageTagVO();
                    imageTagVO.setName("商家推荐");
                    imageTagVO.setStyle(2);
                    tags.add(imageTagVO);
                }
                item.setTags(tags);
            });
        }
        return exhibitContentDTO;
    }

    public void setHeaderPicProcessorMap(Map<String, HeaderPicProcessor> headerPicProcessorMap) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.DealContentBuilderService.setHeaderPicProcessorMap(java.util.Map)");
        this.headerPicProcessorMap = headerPicProcessorMap;
    }

    /**
     * 团购视频模块迭代
     * @param result
     * @param ctx
     */
    private void assembleVideosIteration(List<ContentPBO> result, DealCtx ctx){
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.DealContentBuilderService.assembleVideosIteration(java.util.List,com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        if (LionConfigUtils.showDarenVideo(ctx.getCategoryId()) && Objects.nonNull(ctx.getDigestInfoDTO()) && StringUtils.isNotEmpty(ctx.getDigestInfoDTO().getVideoUrl())) {    // 指定类目且白名单内的团单才走实验逻辑
            // 命中实验 && 黑名单过滤
            buildShowDarenVideo(result, ctx);
        }
        if (Objects.isNull(dealGroupDTO)
                || Objects.isNull(dealGroupDTO.getImage())
                || CollectionUtils.isEmpty(dealGroupDTO.getImage().getAllVideos())){
            return;
        }
        List<DealGroupVideoDTO> videos = dealGroupDTO.getImage().getAllVideos();
        for (DealGroupVideoDTO videoDTO : videos) {
            String picUrl = getPicUlr(videoDTO.getVideoCoverPath(), ctx.isMt());
            String desc = String.format("当前Wi-Fi环境确定播放？预计花费流量%.2fM", videoDTO.getVideoSize() / 1024.0);
            ContentPBO video = new ContentPBO(ContentType.VIDEO.getType(), picUrl);
            video.setVideoUrl(videoDTO.getVideoPath());
            video.setDesc(desc);
            video.setVideoId(Objects.isNull(videoDTO.getVideoId()) ? StringUtils.EMPTY : String.valueOf(videoDTO.getVideoId()));
            // 默认，下发视频原始比例雪碧图
            assembleOriginVideoSpritePic(videoDTO, video);
            // 根据团详样式版本下发不同比例视频
            if (LionConfigUtils.distributeImageScaleByVersion(ctx.getCategoryId())) {
                distributeImageScale(ctx, video, videoDTO);
            }
            result.add(video);
        }
    }

    public void assembleVideo(DealGroupBaseDTO dealGroupBaseDTO, boolean isMt, DealCtx ctx, List<ContentPBO> result){
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.DealContentBuilderService.assembleVideo(DealGroupBaseDTO,boolean,DealCtx,List)");
        if (dealGroupBaseDTO.getHeadVideo() != null) {
            String picUrl = getPicUlr(dealGroupBaseDTO, isMt);
            String desc = String.format("当前Wi-Fi环境确定播放？预计花费流量%.2fM", dealGroupBaseDTO.getHeadVideo().getSize() / 1024.0);
            ContentPBO video = new ContentPBO(ContentType.VIDEO.getType(), picUrl);
            video.setVideoUrl(dealGroupBaseDTO.getHeadVideo().getVideoPath());
            video.setDesc(desc);
            video.setVideoId(Objects.isNull(video.getVideoId()) ? StringUtils.EMPTY : String.valueOf(video.getVideoId()));
            // 根据团详样式版本下发不同比例视频
            if (LionConfigUtils.distributeImageScaleByVersion(ctx.getCategoryId())) {
                distributeImageScale(ctx, video, null);
            }
            result.add(video);
        } else if (LionConfigUtils.showDarenVideo(ctx.getCategoryId()) && Objects.nonNull(ctx.getDigestInfoDTO()) && StringUtils.isNotEmpty(ctx.getDigestInfoDTO().getVideoUrl())) {    // 指定类目且白名单内的团单才走实验逻辑
            buildShowDarenVideo(result, ctx);
        }
    }

    private void buildShowDarenVideo(List<ContentPBO> result, DealCtx ctx){
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.DealContentBuilderService.buildShowDarenVideo(java.util.List,com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        // 命中实验 && 黑名单过滤
        if (showCustomVideo(ctx) && !hitBlacklist(ctx)) {
            DigestInfoDTO digestInfoDTO = ctx.getDigestInfoDTO();
            String desc = String.format("当前Wi-Fi环境确定播放？预计花费流量%.2fM", digestInfoDTO.getVideoSize() / 1024.0 /1024.0);
            ContentPBO video = new ContentPBO(ContentType.VIDEO.getType(), digestInfoDTO.getVideoFrameUrl());
            video.setVideoUrl(digestInfoDTO.getVideoUrl());
            video.setDesc(desc);

            Integer compareResult = BigDecimal.valueOf(digestInfoDTO.getVideoWidth()).divide(BigDecimal.valueOf(digestInfoDTO.getVideoHeight()), RoundingMode.CEILING).compareTo(BigDecimal.valueOf(1));
            // 如果尺寸比值大于0则置为16:9，否则为3:4
            if (compareResult > 0) {
                video.setScale(ImageScaleEnum.SIXTEEN_TO_NINE.getScale());
            } else {
                video.setScale(ImageScaleEnum.THREE_TO_FOUR.getScale());
            }
            result.add(video);
            return;
        }
    }

    /**
     * 下发 雪碧图
     * @param video
     * @param extendVideoDTO
     */
    public void buildSpritePicByExtendVideoDto(ContentPBO video, ExtendVideoDTO extendVideoDTO){
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.DealContentBuilderService.buildSpritePicByExtendVideoDto(ContentPBO,ExtendVideoDTO)");
        if (Objects.isNull(extendVideoDTO)
                || CollectionUtils.isEmpty(extendVideoDTO.getSpriteImageList())
                || Objects.isNull(extendVideoDTO.getSpriteImageList().get(0)) ){
            return;
        }
        SpriteImageDTO spriteImageDTO = extendVideoDTO.getSpriteImageList().get(0);
        SpritePicVO.SpritePicVOBuilder spritePicVOBuilder = SpritePicVO.builder()
                .spritePicUrl(spriteImageDTO.getPath())
                .totalCount(spriteImageDTO.getSubImageCount())
                .row(TEN)
                .column(TEN)
                .spriteCellSize(buildSpritePicCellSize(spriteImageDTO))
                .allSpriteImageSize(buildAllSpritePicSize(spriteImageDTO));
        video.setSpritePic(spritePicVOBuilder.build());
    }

    /**
     * 构造雪碧图--小图尺寸信息
     * @param spriteImageDTO
     * @return
     */
    private ImageSize buildSpritePicCellSize(SpriteImageDTO spriteImageDTO){
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.DealContentBuilderService.buildSpritePicCellSize(com.sankuai.general.product.query.center.client.dto.video.SpriteImageDTO)");
        if (Objects.isNull(spriteImageDTO)){
            return null;
        }
        ImageSize.ImageSizeBuilder imageSizeBuilder = ImageSize.builder();
        imageSizeBuilder.width(spriteImageDTO.getWidth())
                .height(spriteImageDTO.getHeight());
        return imageSizeBuilder.build();
    }

    /**
     * 构造雪碧图--小图尺寸信息
     * @param spriteImageDTO
     * @return
     */
    private ImageSize buildAllSpritePicSize(SpriteImageDTO spriteImageDTO){
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.DealContentBuilderService.buildAllSpritePicSize(com.sankuai.general.product.query.center.client.dto.video.SpriteImageDTO)");
        if (Objects.isNull(spriteImageDTO)
                || Objects.isNull(spriteImageDTO.getWidth())
                || Objects.isNull(spriteImageDTO.getHeight())){
            return null;
        }
        ImageSize.ImageSizeBuilder imageSizeBuilder = ImageSize.builder();
        imageSizeBuilder.width(spriteImageDTO.getWidth() * TEN)
                .height(spriteImageDTO.getHeight().intValue() * TEN);
        return imageSizeBuilder.build();
    }

    private void distributeImageScale(DealCtx ctx, ContentPBO video, DealGroupVideoDTO videoDTO) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.DealContentBuilderService.distributeImageScale(DealCtx,ContentPBO)");
        // 新团详及之前版本只能使用16:9
        if (ctx.isEnableCardStyleV2()) {
            assembleVideoContentPBO(ctx, video, videoDTO);
        } else {
            video.setScale(ImageScaleEnum.SIXTEEN_TO_NINE.getScale());
        }
    }

    public boolean showCustomVideo(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.DealContentBuilderService.showCustomVideo(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        String module = ctx.isMt() ? "MTDarenVideo" : "DpDarenVideo";
        ModuleAbConfig abConfig = douHuBiz.getAbExpResultByUserId(ctx, module);
        List<ModuleAbConfig> moduleAbConfigs = ctx.getModuleAbConfigs();
        if (CollectionUtils.isEmpty(moduleAbConfigs)) {
            moduleAbConfigs = new ArrayList<>();
        }
        moduleAbConfigs.add(abConfig);
        ctx.setModuleAbConfigs(moduleAbConfigs);
        if (Objects.isNull(abConfig) || CollectionUtils.isEmpty(abConfig.getConfigs()) || !abConfig.getConfigs().get(0).getExpResult().contains("b")) {
            return false;
        }

        return true;
    }

    /**
     * 下发视频 原始比例雪碧图
     * @param dealGroupVideoDTO
     * @param video
     */
    public void assembleOriginVideoSpritePic(DealGroupVideoDTO dealGroupVideoDTO, ContentPBO video) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.DealContentBuilderService.assembleOriginVideoSpritePic(DealGroupVideoDTO,ContentPBO)");
        if (Objects.isNull(dealGroupVideoDTO)
                || CollectionUtils.isEmpty(dealGroupVideoDTO.getExtendVideos())) {
            return;
        }
        List<ExtendVideoDTO> spriteExtendVideoDTOS = dealGroupVideoDTO.getExtendVideos().stream()
                .filter(e->CollectionUtils.isNotEmpty(e.getSpriteImageList()) && Objects.nonNull(e.getSpriteImageList().get(0)))
                .collect(Collectors.toList());
        // 找出原始比例的雪碧图
        if (CollectionUtils.isNotEmpty(spriteExtendVideoDTOS)
                && Objects.nonNull(spriteExtendVideoDTOS.get(0))) {
            // 组装雪碧图
            buildSpritePicByExtendVideoDto(video, spriteExtendVideoDTOS.get(0));
        }
    }

    private void assembleVideoContentPBO(DealCtx ctx, ContentPBO video, DealGroupVideoDTO videoDTO) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.DealContentBuilderService.assembleVideoContentPBO(DealCtx,ContentPBO)");
        video.setScale(ImageScaleEnum.SIXTEEN_TO_NINE.getScale());
        if (Objects.isNull(ctx.getDealGroupDTO()) || Objects.isNull(ctx.getDealGroupDTO().getImage()) || CollectionUtils.isEmpty(ctx.getDealGroupDTO().getImage().getExtendVideos())) {
            return;
        }

        List<ExtendVideoDTO> extendVideoDTOS = Objects.nonNull(videoDTO) ? videoDTO.getExtendVideos() : ctx.getDealGroupDTO().getImage().getExtendVideos();
        // 剔除第一个16:9，可能原尺寸也是16:9
        extendVideoDTOS.remove(extendVideoDTOS.stream().filter(e -> e.getRatio().equals(ImageScaleEnum.SIXTEEN_TO_NINE.getScale())).findFirst().orElse(null));
        ExtendVideoDTO extendVideoDTO = extendVideoDTOS.stream().findFirst().orElse(null);
        // 如果从ExtendVideos中没有取到原始尺寸，则使用16:9进行兜底返回
        if (Objects.isNull(extendVideoDTO) || !extendVideoDTO.getRatio().contains(COLON_SIGN) || extendVideoDTO.getRatio().split(COLON_SIGN).length != 2) {
            video.setScale(ImageScaleEnum.SIXTEEN_TO_NINE.getScale());
            return;
        }

        String[] ratioArray = extendVideoDTO.getRatio().split(COLON_SIGN);
        Integer compareResult = NumberUtils.toScaledBigDecimal(ratioArray[0]).divide(NumberUtils.toScaledBigDecimal(ratioArray[1]), RoundingMode.CEILING).compareTo(BigDecimal.valueOf(1));
        // 如果尺寸比值大于0则置为16:9，否则为3:4
        if (compareResult > 0) {
            video.setScale(ImageScaleEnum.SIXTEEN_TO_NINE.getScale());
        } else {
            video.setScale(ImageScaleEnum.THREE_TO_FOUR.getScale());
        }
        video.setVideoUrl(extendVideoDTO.getPath());
        video.setContent(extendVideoDTO.getCoverPath());
    }

    public boolean hitBlacklist(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.DealContentBuilderService.hitBlacklist(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        // 黑名单过滤
        HaimaRequest haimaRequest = new HaimaRequest();
        haimaRequest.setSceneKey("dz_deal_daren_video");
        HaimaResponse haimaResponse = haimaWrapper.queryHaimaConfig(haimaRequest);
        if (Objects.isNull(haimaResponse)) {
            return false;
        }

        List<HaimaConfig> haimaConfigs = haimaResponse.getData();
        if (CollectionUtils.isEmpty(haimaConfigs)) {
            return false;
        }

        HaimaConfig haimaConfig = haimaConfigs.get(0);
        List<HaimaContent> contents = haimaConfig.getContents();
        if (CollectionUtils.isEmpty(contents)) {
            return false;
        }
        for (HaimaContent content : contents) {
            List<String> shopIdstBlacklist = Splitter.on(",").splitToList(content.getContentString("shopids_blacklist"));
            List<String> dealIdstBlacklist =Splitter.on(",").splitToList(content.getContentString("dealids_blacklist"));
            if (dealIdstBlacklist.contains(String.valueOf(ctx.getDpId())) || shopIdstBlacklist.contains(String.valueOf(ctx.getDpLongShopId()))) {
                return true;
            }
        }

        return false;
    }

    private String getPicUlr(DealGroupBaseDTO dealGroupBaseDTO, boolean isMt) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.DealContentBuilderService.getPicUlr(com.dianping.deal.base.dto.DealGroupBaseDTO,boolean)");
        return Lion.getBoolean(LionConstants.APP_KEY, LionConstants.COMPRESS_VIDEO_COVER_PIC, true)
                ? ImageHelper.mediumSize(dealGroupBaseDTO.getHeadVideo().getVideoCoverPath(), isMt)
                : dealGroupBaseDTO.getHeadVideo().getVideoCoverPath();
    }
    private String getPicUlr(String videoCoverPath, boolean isMt) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.DealContentBuilderService.getPicUlr(java.lang.String,boolean)");
        return Lion.getBoolean(LionConstants.APP_KEY, LionConstants.COMPRESS_VIDEO_COVER_PIC, true)
                ? ImageHelper.mediumSize(videoCoverPath, isMt)
                : videoCoverPath;
    }

    private Integer getPicSize(DealCtx ctx, String heightWidth) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.DealContentBuilderService.getPicSize(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx,java.lang.String)");
        String expResults = StringUtils.isEmpty(ctx.getExpResults()) ? "[]" : ctx.getExpResults();
        try {
            List<String> results = JsonFacade.deserializeList(expResults.toLowerCase(), String.class);
            if (CollectionUtils.isNotEmpty(results)) {
                //支持体检做头图AB
                Map<Integer, Map<String, Map<String, Integer>>> picaspectratioExpMap = LionFacade.get(LionConstants.PICSIZE_EXP, new TypeReference<Map<Integer, Map<String, Map<String, Integer>>>>() {
                });
                if (MapUtils.isEmpty(picaspectratioExpMap)) {
                    return null;
                }
                for (Integer categoryId : picaspectratioExpMap.keySet()) {
                    if (categoryId == null) {
                        continue;
                    }
                    if (ctx.getCategoryId() == categoryId) {
                        Map<String, Map<String, Integer>> picaspectratioExp = picaspectratioExpMap.get(categoryId);
                        if (MapUtils.isEmpty(picaspectratioExp)) {
                            continue;
                        }
                        Map<String, Integer> picSize = null;
                        for (String exp : results) {
                            picSize = picaspectratioExp.get(exp);
                            if (picSize != null) {
                                return picSize.get(heightWidth);
                            }
                        }
                    }
                }
            }

            // 头图比例配置化
            Map<Integer, Map<String, Integer>> picAspectRatioMap = LionFacade.get(LionConstants.PICSIZE_CONFIG, new TypeReference<Map<Integer, Map<String, Integer>>>() {
            });
            if (MapUtils.isEmpty(picAspectRatioMap)) {
                return null;
            }
            for (Integer categoryId : picAspectRatioMap.keySet()) {
                if (categoryId == null) {
                    continue;
                }
                if (ctx.getCategoryId() == categoryId) {
                    Map<String, Integer> picsizeMap = picAspectRatioMap.get(categoryId);
                    if (MapUtils.isEmpty(picsizeMap)) {
                        continue;
                    }
                    return picsizeMap.get(heightWidth);
                }
            }

        } catch (Exception e) {
            log.error("putPicAspectRatio is error,dealGroupId is {}", ctx.getResult().getDpDealId(), e);
        }
        return null;
    }
}
