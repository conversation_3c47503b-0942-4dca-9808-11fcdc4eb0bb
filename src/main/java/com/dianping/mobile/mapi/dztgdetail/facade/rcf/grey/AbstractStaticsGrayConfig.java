package com.dianping.mobile.mapi.dztgdetail.facade.rcf.grey;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.grey.enums.GrayStrategyEnum;
import com.google.common.base.Predicate;
import com.google.common.base.Predicates;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Optional;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2023/8/7
 * @since dzviewscene-productshelf-home
 */
@Getter
@Setter
@NoArgsConstructor
public abstract class AbstractStaticsGrayConfig<T extends AbstractGrayConfigContext> implements StaticsGrayConfig<T> {

    /**
     * 是否需要处理
     */
    boolean processSwitch;
    /***
     * 是否全量
     */
    boolean showHand;

    /**
     * 灰度设备编号
     */
    List<String> grayUnionIds = Lists.newArrayList();

    int percentage ;

    String strategy;
    /**
     * 监控通知Mis
     */
    List<String> notifyMis = Lists.newArrayList();

    List<Predicate<? extends AbstractGrayConfigContext>> predicates = Lists.newArrayList();

    @Override
    public boolean isAllowAccess(AbstractGrayConfigContext context, String unionId) {
        if (showHand) {
            return true;
        }
        GrayStrategyEnum strategyEnum = Optional.ofNullable(strategy)
                .map(s-> GrayStrategyEnum.getFromType(strategy)).orElse(GrayStrategyEnum.WHITE_LIST);
        if( strategyEnum == null || strategyEnum == GrayStrategyEnum.WHITE_LIST) {
            addGrayUnionIds();
        } else {
            addPercent(unionId);
        }
        return Predicates.and(predicates.toArray(new Predicate[0])).apply(context);
    }

    private void addPercent(String unionId) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.grey.AbstractStaticsGrayConfig.addPercent(java.lang.String)");
        addPredicate(t-> {
            int percentValue = Math.min(percentage,100);
            if(ObjectUtils.isEmpty(unionId)) {
                return false;
            }
            if(Math.abs(unionId.hashCode()%100) <= percentValue) {
                return true;
            }
            return false;
        });
    }

    public void addGrayUnionIds() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.grey.AbstractStaticsGrayConfig.addGrayUnionIds()");
        addPredicate(t -> {
            return ObjectUtils.isEmpty(grayUnionIds) || grayUnionIds.containsAll(
                    Optional.ofNullable(t.getGrayUnionIds()).orElse(Lists.newArrayList()));
        });
    }

    public void addPredicate(Predicate<AbstractGrayConfigContext> predicate) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.grey.AbstractStaticsGrayConfig.addPredicate(com.google.common.base.Predicate)");
        predicates.add(predicate);
    }

}
