package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DzCardPromoWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MiniProgramSceneEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.basic.ParallBestShopProcessor;
import com.dianping.mobile.mapi.dztgdetail.helper.CardHelper;
import com.sankuai.dzcard.navigation.api.dto.CardQualifyEventIdDTO;
import com.sankuai.dzcard.navigation.api.enums.QualifyEventTypeEnum;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

public class JoyDiscountCardProcessor extends AbsDealProcessor {

    @Resource
    private DzCardPromoWrapper wrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        boolean isNotExternal = !ctx.isExternal();
        boolean isExternalAndEnabled = ctx.getEnvCtx().isExternalAndEnabled(MiniProgramSceneEnum.JOY_CARD.getPromoScene());
        return isNotExternal || isExternalAndEnabled;
    }

    @Override
    public void prepare(DealCtx ctx) {
        if(ctx.getDpLongShopId() > 0) {
            Cat.logEvent(ParallBestShopProcessor.BEST_SHOP_GREY, "prepareJoyCardInfo_JoyCard");
            ctx.getFutureCtx().setDzCardFuture(wrapper.prepare(ctx));
            ctx.getFutureCtx().setUserStateFuture(wrapper.prepareUserState(ctx));
        }
    }

    @Override
    public void process(DealCtx ctx) {
        ctx.getPriceContext().setNewUser(wrapper.resolveUserState(ctx.getFutureCtx().getUserStateFuture()));
        processDiscountCard(ctx);
    }

    private void processDiscountCard(DealCtx ctx) {
        List<CardQualifyEventIdDTO> cards = wrapper.resolve(ctx.getFutureCtx().getDzCardFuture());

        if (cards.isEmpty()) {
            return;
        }

        // 会员卡和会员日卡当成一种优惠处理， 都存在的时候使用会员日
        removeMemberCardIfExistedMemberDayCard(cards);

        // 1.优先展示持有的卡
        // 2.商户只有一张卡，默认展示
        // 3.商户有多张卡，用户都不持有，新客玩乐卡， 老客折扣卡
        // 在这里就指定卡是为了价格服务请求次数，减少下游流量放大倍数
        // 这个选择逻辑在ButtonBuilder里面可以通过配置实现，且已配置
        decisionUseCard(cards);

        identityCard(ctx, cards);
    }

    private void decisionUseCard(List<CardQualifyEventIdDTO> cards) {

        //商户只有一张卡，就展示这张卡
        if (cards.size() <= 1) {
            return;
        }

        int holdCount = 0;
        for (CardQualifyEventIdDTO card : cards) {
            if (CardHelper.holdCard(card)) {
                holdCount++;
            }
        }

        // 持有所有的卡，展示所有卡
        if (holdCount >= cards.size()) {
            return;
        }

        // 持有任意卡，但没有持有全部的卡，就只保留持有的卡
        if (holdCount > 0) {
            List<CardQualifyEventIdDTO> holdCards = cards.stream()
                    .filter(CardHelper::holdCard)
                    .collect(Collectors.toList());
            cards.clear();
            cards.addAll(holdCards);
        }
    }

    private void identityCard(DealCtx ctx, List<CardQualifyEventIdDTO> cards) {
        for (CardQualifyEventIdDTO card : cards) {
            if (card.getQualifyEventType() == QualifyEventTypeEnum.DISCOUNT_CARD.getCode()) {
                ctx.getPriceContext().setDcCardMemberCard(card);
            } else if (card.getQualifyEventType() == QualifyEventTypeEnum.MEMBER_DAY.getCode()) {
                ctx.getPriceContext().setDcCardMemberDay(true);
                ctx.getPriceContext().setDcCardMemberCard(card);
            } else if (card.getQualifyEventType() == QualifyEventTypeEnum.JOY_CARD.getCode()) {
                Cat.logError(new IllegalArgumentException("玩乐卡已经下线了，但是权益台返回了"));
//                ctx.getPriceContext().setJoyCard(card);
            }
        }
    }

    private void removeMemberCardIfExistedMemberDayCard(List<CardQualifyEventIdDTO> cards) {
        CardQualifyEventIdDTO memberCard = null;
        CardQualifyEventIdDTO memberDayCard = null;

        for (CardQualifyEventIdDTO card : cards) {
            if (card.getQualifyEventType() == QualifyEventTypeEnum.DISCOUNT_CARD.getCode()) {
                memberCard = card;
            } else if (card.getQualifyEventType() == QualifyEventTypeEnum.MEMBER_DAY.getCode()) {
                memberDayCard = card;
            }
        }

        if (memberDayCard != null && memberCard != null) {
            cards.remove(memberCard);
        }
    }

}
