package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CommonAttrVO;
import com.dianping.mobile.mapi.dztgdetail.entity.CareCenterHouseStyleConfig;
import com.dianping.pigeon.remoting.provider.config.annotation.Service;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class CareCenterHouseStyleHighlightsProcessor extends AbstractHighlightsProcessor {

    //高亮模块
    public CareCenterHouseStyleConfig careCenterHouseStyleConfig;

    //是否启用
    public boolean enable;

    //获取三级类目信息
    public Long serviceTypeId;

    //初始化Lion中数据
    public void initLion() {
        try {
            String config = Lion.getString(LionConstants.APP_KEY, LionConstants.CARE_CENTER_HOUSE_STYLE);
            careCenterHouseStyleConfig = JSON.parseObject(config, CareCenterHouseStyleConfig.class);
            if (careCenterHouseStyleConfig != null &&
                    careCenterHouseStyleConfig.getServiceTypeIds()
                            .contains(Optional.ofNullable(serviceTypeId).map(String::valueOf).orElse("0"))) {
                enable = true;
                return;
            }
            enable = false;
        } catch (Exception e) {
            log.error("CareCenterHouseStyleHighlightsProcessor CareCenterHouseStyleHighlightsProcessor error!", e);
        }
    }


    /**
     * 初始化数据
     *
     * @param ctx
     */
    @Override
    protected void beforeBuild(DealCtx ctx) {
        //初始化Lion中数据
        serviceTypeId = ctx.getDealGroupDTO().getCategory().getServiceTypeId();
        initLion();
    }

    /**
     * 拼装高亮模块中属性信息
     *
     * @param ctx
     * @return
     */
    @Override
    protected List<CommonAttrVO> getHighlightsAttrs(DealCtx ctx) {
        if (enable) {
            return careCenterHouseStyleConfig.getAttrs();
        }
        return null;
    }


    /**
     * 拼装高亮模块中分隔符信息
     *
     * @param ctx
     */
    @Override
    protected void afterBuild(DealCtx ctx) {
        if (enable) {
            ctx.getHighlightsModule().setDelimiter(careCenterHouseStyleConfig.getDelimiter());
        }
    }

    @Override
    protected String getHighlightsIdentify(DealCtx ctx) {
        return "";
    }

    @Override
    protected String getHighlightsStyle(DealCtx ctx) {
        if (enable) {
            return careCenterHouseStyleConfig.getStyle();
        }
        return "";
    }

    @Override
    protected String getHighlightsContent(DealCtx ctx) {
        return "";
    }


}
