package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import java.io.Serializable;

/**
 * 导航栏搜索模块数据对象
 */
public class NavBarSearchModuleVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 搜索词
     */
    private String text;

    /**
     * 搜索图标
     */
    private String icon;

    /**
     * 跳转链接
     */
    private String jumpUrl;

    /**
     * 默认构造函数
     */
    public NavBarSearchModuleVO() {
    }

    /**
     * 全参数构造函数
     */
    public NavBarSearchModuleVO(String text, String icon, String jumpUrl) {
        this.text = text;
        this.icon = icon;
        this.jumpUrl = jumpUrl;
    }

    /**
     * 获取搜索词
     */
    public String getText() {
        return text;
    }

    /**
     * 设置搜索词
     */
    public void setText(String text) {
        this.text = text;
    }

    /**
     * 获取搜索图标
     */
    public String getIcon() {
        return icon;
    }

    /**
     * 设置搜索图标
     */
    public void setIcon(String icon) {
        this.icon = icon;
    }

    /**
     * 获取跳转链接
     */
    public String getJumpUrl() {
        return jumpUrl;
    }

    /**
     * 设置跳转链接
     */
    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    @Override
    public String toString() {
        return "NavBarSearchModuleVO{" +
                "text='" + text + '\'' +
                ", icon='" + icon + '\'' +
                ", jumpUrl='" + jumpUrl + '\'' +
                '}';
    }
}