package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.req.DealBaseContextRequest;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.style.DzDealStyleFTConfiguration;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.style.executor.DzDealStyleExecutor;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.utils.FaultToleranceUtils;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.sankuai.athena.stability.faulttolerance.FaultToleranceEngine;
import com.sankuai.athena.stability.faulttolerance.FaultToleranceExecutionEngine;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2023/8/31
 * @since mapi-dztgdetail-web
 */
@Controller("general/platform/dztgdetail/dzdealstyle.bin")
@Action(url = "dzdealstyle.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class DzDealStyleAction extends AbsAction<DealBaseReq>  {

    @Resource
    private DzDealStyleFTConfiguration dzDealStyleFTConfiguration;

    @Resource
    private DzDealStyleExecutor dzDealStyleExecutor;

    private FaultToleranceEngine faultToleranceEngine = new FaultToleranceExecutionEngine();



    @Override
    protected IMobileResponse validate(DealBaseReq request, IMobileContext iMobileContext) {
        IdUpgradeUtils.processProductIdForDealBaseReq(request, "dzdealstyle.bin");
        if (request == null || request.getDealgroupid() == null || request.getDealgroupid() <= 0) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @Override
    @CryptMethod
    protected IMobileResponse execute(DealBaseReq request, IMobileContext iMobileContext) {
        EnvCtx envCtx = initEnvCtx(iMobileContext);
        DealBaseContextRequest contextRequest = DealBaseContextRequest.build(request, iMobileContext, envCtx);
        String ftExpression = Lion.getString(LionConstants.APP_KEY, LionConstants.DZ_DEAL_STYLE_FAULT_TOLERANCE_EXPRESSION, "");
        // 1. 满足表达式，则走降级链路
        if (FaultToleranceUtils.hit(request, ftExpression)) {
            // 接入容错兜底组件
            CommonMobileResponse response = faultToleranceEngine.execute(contextRequest, dzDealStyleFTConfiguration);
            if (Objects.isNull(response) || Objects.isNull(response.getStatusCode())) {
                return Resps.SYSTEM_ERROR;
            }
            // 处理状态码
            int code = response.getStatusCode().getCode();
            response.setStatusCode(FaultToleranceUtils.getStatusCode(code));
            return response;
        }
        return dzDealStyleExecutor.getExecuteResult(request, envCtx);
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
