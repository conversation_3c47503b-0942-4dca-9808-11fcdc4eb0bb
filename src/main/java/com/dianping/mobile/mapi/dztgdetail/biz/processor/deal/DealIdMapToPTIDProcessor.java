package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.IdMappingWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.sankuai.mpproduct.idservice.api.enums.BizProductIdType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.Map;
import java.util.concurrent.Future;

@Slf4j

public class DealIdMapToPTIDProcessor extends AbsDealProcessor {
    @Autowired
    IdMappingWrapper idMappingWrapper;

    @Override
    public void prepare(DealCtx ctx) {
        if (ctx.isMt()) {
            return;
        }
        Future bizProductIdToPtIdFuture = idMappingWrapper.preBizProductIdToPtId(BizProductIdType.DP_DEAL_GROUP_ID, Collections.singletonList((long) ctx.getDpId()));
        ctx.getFutureCtx().setBizProductIdToPtIdFuture(bizProductIdToPtIdFuture);
    }

    @Override
    public void process(DealCtx ctx) {
        if (ctx.isMt()) {
            ctx.setPtId((long) ctx.getMtId());
            return;
        }
        Map<Long, Long> productIdToPtIdMap = idMappingWrapper.bizProductIdToPtId(ctx.getFutureCtx().getBizProductIdToPtIdFuture());
        if (productIdToPtIdMap != null) {
            ctx.setPtId(productIdToPtIdMap.get((long) ctx.getDpId()));
        } else {
            log.info("idmapping fail, dpId:{}", ctx.getDpId());
        }
    }
}
