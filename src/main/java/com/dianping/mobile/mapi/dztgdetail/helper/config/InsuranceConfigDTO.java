package com.dianping.mobile.mapi.dztgdetail.helper.config;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class InsuranceConfigDTO implements Serializable {

    // 类目对应的保险配置
    private Map<String, InsuranceItemConfigDTO> insuranceItemConfigMap;

    // 类目对应的商品标签配置
    private Map<String, List<Long>> productTagConfigMap;

    // 类目对应的ZDC标签配置
    private Map<String, List<Long>> shopTagConfigMap;

    // 安心中模块的icon 和 落地页配置
    private Map<String, InsuranceItemConfigDTO> easePlantConfigMap;
}
