package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/12/13
 */
@TypeDoc(description = "会员价加购物车")
@MobileDo(id = 0x377a)
@Data
public class DealCartMemberPrice implements Serializable {
    @FieldDoc(description = "标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "文案")
    @MobileDo.MobileField(key = 0x451b)
    private String text;

    @FieldDoc(description = "副文案")
    @MobileDo.MobileField(key = 0xc72d)
    private String subText;

    @FieldDoc(description = "跳转链接")
    @MobileDo.MobileField(key = 0x8283)
    private String redirectUrl;

    @FieldDoc(description = "初始价格按钮标题")
    @MobileDo.MobileField(key = 0xe07f)
    private String originBntTitle;

    @FieldDoc(description = "会员价格按钮标题")
    @MobileDo.MobileField(key = 0xf2cb)
    private String memberBntTitle;

    @FieldDoc(description = "初始价格")
    @MobileDo.MobileField(key = 0x6bf0)
    private String originPriceStr;
}
