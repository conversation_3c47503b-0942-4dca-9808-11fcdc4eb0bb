package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.annotation.MobileRequest.Param;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.dianping.mobile.mapi.dztgdetail.common.constants.QueryParams;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.Setter;

@Data
@TypeDoc(description = "collaborative.bin接口请求参数")
@MobileRequest
public class CollaborativeRequest implements IMobileRequest {
    @Deprecated
    @Param(name = QueryParams.DID)
    @Setter
    Integer dealId;
    @Param(name = QueryParams.SDID)
    @Setter
    String stringDealId;
    @Param(name = QueryParams.SCENE)
    @Setter
    //1: 看了也看 2: 相关团购
    Integer scene;
    @Param(name = QueryParams.LAT)
    @Setter
    Double lat;
    @Param(name = QueryParams.LNG)
    @Setter
    Double lng;
    @Param(name = QueryParams.CITY_ID)
    @Setter
    Integer cityId;
    @Param(name = QueryParams.DISTANCE)
    @Setter
    Integer distance;

    public Integer getDistance() {
        return distance == null ? -1 : distance;
    }

    public Integer getScene() {
        return scene == null ? 1 : scene;
    }

    public Integer getDealId() {
        return dealId == null ? 0 : dealId;
    }

    public Double getLat() {
        return lat == null ? 0.0 : lat;
    }

    public Double getLng() {
        return lng == null ? 0.0 : lng;
    }

    public Integer getCityId() {
        return cityId == null ? 0 : cityId;
    }
}
