package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ContentTagWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.google.common.collect.Lists;
import com.sankuai.mpmctcontent.query.thrift.dto.meta.BatchQueryTagValueRequestDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.meta.MetaInfoEntityIdDTO;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2024/8/6
 */
public class ContentTagProcessor extends AbsDealProcessor {
    @Resource
    private ContentTagWrapper contentTagWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        return LionConfigUtils.useNewExhibitCategoryIds(ctx.getCategoryId());
    }

    @Override
    public void prepare(DealCtx ctx) {
        BatchQueryTagValueRequestDTO requestDTO = buildRequestDTO();
        ctx.getFutureCtx().setContentTagFuture(contentTagWrapper.getContentTagFuture(requestDTO));
    }

    private BatchQueryTagValueRequestDTO buildRequestDTO() {
        BatchQueryTagValueRequestDTO requestDTO = new BatchQueryTagValueRequestDTO();
        List<MetaInfoEntityIdDTO> entityIdList = Lists.newArrayList();
        MetaInfoEntityIdDTO entityIdDTO = new MetaInfoEntityIdDTO();
        entityIdDTO.setBizLine(23);
        entityIdDTO.setEntityName("material_glasses_frame_style");
        entityIdList.add(entityIdDTO);
        requestDTO.setEntityIdList(entityIdList);
        return requestDTO;
    }

    @Override
    public void process(DealCtx ctx) {
        ctx.setTagValueResponseDTO(contentTagWrapper.getFutureResult(ctx.getFutureCtx().getContentTagFuture()));
    }
}
