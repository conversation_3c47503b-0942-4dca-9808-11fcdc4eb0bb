package com.dianping.mobile.mapi.dztgdetail.datatype.resp.other;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;
import java.util.List;

@TypeDoc(description = "到综好评度数据模型")
@MobileDo(id = 0x3ce4)
public class GoodReviewPBO implements Serializable {

    @FieldDoc(description = "好评度")
    @MobileField(key = 0x48c9)
    private String goodReviewRatio;

    @FieldDoc(description = "总评价描述")
    @MobileField(key = 0xaeb)
    private String totalReviewDesc;

    @FieldDoc(description = "评价分数")
    @MobileField(key = 0x7e44)
    private double reviewScore;

    @FieldDoc(description = "用户头像")
    @MobileField(key = 0xcf0)
    private List<String> userIcons;

    @FieldDoc(description = "评价短语")
    @MobileField(key = 0x16a1)
    private String reviewPhrase;

    @FieldDoc(description = "跳转地址")
    @MobileField(key = 0x8283)
    private String redirectUrl;

    public String getGoodReviewRatio() {
        return goodReviewRatio;
    }

    public void setGoodReviewRatio(String goodReviewRatio) {
        this.goodReviewRatio = goodReviewRatio;
    }

    public String getTotalReviewDesc() {
        return totalReviewDesc;
    }

    public void setTotalReviewDesc(String totalReviewDesc) {
        this.totalReviewDesc = totalReviewDesc;
    }

    public double getReviewScore() {
        return reviewScore;
    }

    public void setReviewScore(double reviewScore) {
        this.reviewScore = reviewScore;
    }

    public String getRedirectUrl() {
        return redirectUrl;
    }

    public void setRedirectUrl(String redirectUrl) {
        this.redirectUrl = redirectUrl;
    }

    public String getReviewPhrase() {
        return reviewPhrase;
    }

    public void setReviewPhrase(String reviewPhrase) {
        this.reviewPhrase = reviewPhrase;
    }

    public List<String> getUserIcons() {
        return userIcons;
    }

    public void setUserIcons(List<String> userIcons) {
        this.userIcons = userIcons;
    }
}
