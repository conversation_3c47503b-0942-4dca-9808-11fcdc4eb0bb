package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.sku;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

import java.io.Serializable;
import java.util.List;

@MobileDo(id = 0xd836)
public class SkuSalesAttrWithPicDO implements Serializable {
    @FieldDoc(description = "属性类型 1-文本描述型 2-图片描述型 3-左图右字型")
    @MobileDo.MobileField(key = 0x5dce)
    private int attrType;

    @FieldDoc(description = "销售属性中文名")
    @MobileDo.MobileField(key = 0xf301)
    private String skuSalesAttrCnName;

    @FieldDoc(description = "销售属性英文名")
    @MobileDo.MobileField(key = 0xfe58)
    private String skuSalesAttrName;

    @FieldDoc(description = "销售属性可选值")
    @MobileDo.MobileField(key = 0xe138)
    private List<SkuSalesAttrValueDO> skuSalesAttrValues;

    @FieldDoc(description = "规格信息扩展字段")
    @MobileDo.MobileField(key = 0x9e06)
    private SkuSalesAttrExt skuSalesAttrExt;

    public String getSkuSalesAttrDesc() {
        return skuSalesAttrDesc;
    }

    public void setSkuSalesAttrDesc(String skuSalesAttrDesc) {
        this.skuSalesAttrDesc = skuSalesAttrDesc;
    }

    private String skuSalesAttrDesc;


    public List<SkuSalesAttrValueDO> getSkuSalesAttrValues() {
        return skuSalesAttrValues;
    }

    public void setSkuSalesAttrValues(
            List<SkuSalesAttrValueDO> skuSalesAttrValues) {
        this.skuSalesAttrValues = skuSalesAttrValues;
    }

    public String getSkuSalesAttrName() {
        return skuSalesAttrName;
    }

    public void setSkuSalesAttrName(String skuSalesAttrName) {
        this.skuSalesAttrName = skuSalesAttrName;
    }

    public int getAttrType() {
        return attrType;
    }

    public void setAttrType(int attrType) {
        this.attrType = attrType;
    }

    public String getSkuSalesAttrCnName() {
        return skuSalesAttrCnName;
    }

    public void setSkuSalesAttrCnName(String skuSalesAttrCnName) {
        this.skuSalesAttrCnName = skuSalesAttrCnName;
    }

    public SkuSalesAttrExt getSkuSalesAttrExt() {
        return skuSalesAttrExt;
    }

    public void setSkuSalesAttrExt(SkuSalesAttrExt skuSalesAttrExt) {
        this.skuSalesAttrExt = skuSalesAttrExt;
    }
}