package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ServiceGuarantee;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.util.List;
import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 16/11/2022
 * @time 11:43
 * 模型描述：服务保障内容详情数据
 * 移动之家链接: https://mobile.sankuai.com/studio/model/info/29815
 */
@MobileDo(id = 0x46dd)
@Data
public class ServiceGuaranteeContentDetailDTO implements Serializable {

    /**
     * 顶部标题
     */
    @MobileField(key = 0x3bc8)
    private String topTitle;

    /**
     * 顶部标题文本内容
     */
    @MobileField(key = 0x1e3e)
    private String topTitleText;

    /**
     * 服务保障内容详情模块列表
     */
    @MobileField(key = 0x10dd)
    private List<ServiceGuaranteeContentDetailModule> serviceGuaranteeContentDetailModules;
}
