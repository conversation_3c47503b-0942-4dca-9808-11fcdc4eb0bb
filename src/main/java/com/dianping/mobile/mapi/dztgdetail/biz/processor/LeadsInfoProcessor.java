package com.dianping.mobile.mapi.dztgdetail.biz.processor;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealBookWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.sankuai.clr.content.process.gateway.thrift.dto.leads.LoadLeadsInfoRespDTO;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @author: wuwenqiang
 * @create: 2024-09-03
 * @description: 留资信息Processor
 */

public class LeadsInfoProcessor extends AbsDealProcessor {

    @Resource
    private DealBookWrapper dealBookWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        return DealUtils.isLeadsDeal(ctx);
    }

    @Override
    public void prepare(DealCtx ctx) {
        // 如果后续需要扩展类目，务必区分留资弹窗信息和特惠团购的场景
        ctx.getFutureCtx().setLeadsInfoFuture(dealBookWrapper.prepareLoadLeadsInfo(ctx));
    }

    @Override
    public void process(DealCtx ctx) {
        if (Objects.isNull(ctx.getFutureCtx()) || Objects.isNull(ctx.getFutureCtx().getLeadsInfoFuture())) {
            return;
        }
        LoadLeadsInfoRespDTO loadLeadsInfoRespDTO = dealBookWrapper.queryLoadLeadsInfo(ctx.getFutureCtx().getLeadsInfoFuture());
        ctx.setLeadsInfo(loadLeadsInfoRespDTO);
    }
}
