package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.PriceExplainContentRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.PriceExplainContentDTO;
import com.google.common.collect.Sets;
import com.sankuai.beautycontent.dzhealth.medical.dealgroup.consumerecord.api.query.MedicalDealConsumeRecordQueryService;
import com.sankuai.beautycontent.dzhealth.medical.dealgroup.consumerecord.dto.QueryImageUrlDTO;
import com.sankuai.beautycontent.dzhealth.medical.dealgroup.consumerecord.dto.QueryImageUrlResponse;
import com.sankuai.beautycontent.dzhealth.medical.dealgroup.consumerecord.request.QueryImageUrlRequest;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupCategoryBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupIdBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.APP_KEY;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.EXAM_PRICE_EXPLAIN_TEXT;

@Component
@Slf4j
public class PriceExplainContentFacade {

    @Autowired
    private DealGroupWrapper dealGroupWrapper;
    @Resource
    @Qualifier("medicalDealConsumeRecordQueryService")
    private MedicalDealConsumeRecordQueryService medicalDealConsumeRecordQueryService;

    @Autowired
    private QueryCenterWrapper queryCenterWrapper;

    public PriceExplainContentDTO getPriceExplainContent(PriceExplainContentRequest req, EnvCtx ctx) {
        DealGroupDTO dealGroupDto = getDealGroupDto(req, ctx);
        if (dealGroupDto == null) {
            return null;
        }
        DealGroupCategoryDTO categoryDTO = dealGroupDto.getCategory();
        //根据团单分类ID做区分
        if (categoryDTO.getCategoryId() == 401) {
            return buildExamPriceExplain(req);
        }
        return null;
    }

    private PriceExplainContentDTO buildExamPriceExplain(PriceExplainContentRequest req) {

        PriceExplainContentDTO priceExplainContentDTO = new PriceExplainContentDTO();

        priceExplainContentDTO.setText(buildExamPriceExplainText());
        priceExplainContentDTO.setPicurl(getPicUrl(req));

        return priceExplainContentDTO;
    }

    private String buildExamPriceExplainText() {
        return Lion.getString(APP_KEY, EXAM_PRICE_EXPLAIN_TEXT, Strings.EMPTY);
    }

    private String getPicUrl(PriceExplainContentRequest req) {

        QueryImageUrlRequest request = new QueryImageUrlRequest();
        request.setDpDealGroupId(req.getDpDealGroupId());
        request.setDpShopId(req.getDpLongShopId());

        QueryImageUrlDTO queryImageUrlDTO = null;
        try {
            QueryImageUrlResponse urlResponse = medicalDealConsumeRecordQueryService.queryImageUrl(request);
            if (urlResponse == null || urlResponse.getCode() != 200) {
                return null;
            }
            queryImageUrlDTO = urlResponse.getData();
            if (queryImageUrlDTO == null) {
                return null;
            }
            return queryImageUrlDTO.getUrl();
        } catch (Exception e) {
            log.error("getPicUrl，dpDealGroupId={}", req.getDpDealGroupId(), e);
        }
        return null;
    }


    private DealGroupDTO getDealGroupDto(PriceExplainContentRequest req, EnvCtx ctx) {
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = buildQueryCenterRequest(req, ctx);
        try {
            DealGroupDTO dealGroupDTO = queryCenterWrapper.getDealGroupDTO(queryByDealGroupIdRequest);
            return dealGroupDTO;
        } catch (TException e) {
            log.error("QueryCenterWrapper.getDealGroupDTO error!", e);
        }
        return null;
    }

    private QueryByDealGroupIdRequest buildQueryCenterRequest(PriceExplainContentRequest req, EnvCtx ctx) {
        return QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet(req.getDpDealGroupId()), IdTypeEnum.DP)
                .dealGroupId(DealGroupIdBuilder.builder().all())
                .category(DealGroupCategoryBuilder.builder().all())
                .build();
    }
}
