package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.Pair;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public final class DpDealDetailFormatter {
	
    private DpDealDetailFormatter() {
    }

	public static List<Pair> toPairList(String s) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.helper.DpDealDetailFormatter.toPairList(java.lang.String)");
        List<Pair> list = new ArrayList<Pair>();
		if (s == null || s.isEmpty()) {
			return list;
		}
		
		// Pair.type: 1=TITLE, 2=TEXT, 3=IMG, 4=RED TEXT
		int p = 0;
		while (p < s.length()) {
			int imgStart = s.indexOf("<img>", p);
			if (imgStart == -1) {
				list.add(new Pair("", chomp(s.substring(p)), 2));
				p = s.length();
			} else {
				int imgEnd = s.indexOf("</img>", imgStart);
				if (imgEnd == -1) {
					list.add(new Pair("", chomp(s.substring(p)), 2));
					p = s.length();
				} else {
					if (imgStart-p > 0) {
						list.add(new Pair("", chomp(s.substring(p, imgStart)), 2));
					}
					list.add(new Pair("", s.substring(imgStart + 5, imgEnd), 3));
					p = imgEnd + 6;
					if (p < s.length() && s.charAt(p) == '\n') {
						p++;
					}
				}
			}
		}

		return list;
	}
	
	private static String chomp(String s) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.helper.DpDealDetailFormatter.chomp(java.lang.String)");
        return (s != null && s.endsWith("\n")) ? s.substring(0, s.length() - 1) : s;
	}
	
	public static String chopTitle(String text, String title) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.helper.DpDealDetailFormatter.chopTitle(java.lang.String,java.lang.String)");
        if (text == null) {
			return null;
		}
		text = StringUtils.strip(text);
		text = StringUtils.removeStart(text, title);
		text = StringUtils.removeStart(text, '[' + title + ']');
//		text = StringUtils.removeStart(text, '【' + title + '】');
		text = StringUtils.strip(text);
		return text;
	}
	
	private static final Pattern HTML_IMG = Pattern.compile("<\\s*img\\s+[^>]*>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);

	public static String removeHtmlImage(String html) {
		if (StringUtils.isNotEmpty(html)) {
			StringBuilder sb = new StringBuilder();
			Matcher m = HTML_IMG.matcher(html);
			int index = 0;
			while (m.find()) {
				if (index < m.start()) {
					for (int i = index; i < m.start(); i++) {
						sb.append(html.charAt(i));
					}
				}
				index = m.end();
			}
			if (index < html.length()) {
				for (int i = index; i < html.length(); i++) {
					sb.append(html.charAt(i));
				}
			}
			return sb.length() > 0 ? sb.toString() : html;
		}
		return html;
	}
}
