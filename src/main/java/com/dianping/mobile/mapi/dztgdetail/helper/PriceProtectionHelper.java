package com.dianping.mobile.mapi.dztgdetail.helper;

import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.BestPriceGuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.PriceProtectionTagDTO;

public class PriceProtectionHelper {
    public static boolean checkPriceProtectionValid(ObjectGuaranteeTagDTO objectGuaranteeTagDTO) {
        if (objectGuaranteeTagDTO == null) {
            return false;
        }
        PriceProtectionTagDTO priceProtectionTagDTO = objectGuaranteeTagDTO.getPriceProtectionTag();
        return priceProtectionTagDTO != null
                && priceProtectionTagDTO.getValid() != null
                && priceProtectionTagDTO.getValid()
                && priceProtectionTagDTO.getValidityDays() != null;
    }

    public static boolean checkBestPriceGuaranteeInfoValid(ObjectGuaranteeTagDTO objectGuaranteeTagDTO) {
        if (objectGuaranteeTagDTO == null) {
            return false;
        }
        BestPriceGuaranteeTagDTO bestPriceGuaranteeTagDTO = objectGuaranteeTagDTO.getBestPriceGuaranteeTagDTO();
        return bestPriceGuaranteeTagDTO != null
                && bestPriceGuaranteeTagDTO.getValid() != null
                && bestPriceGuaranteeTagDTO.getValid();
    }
}
