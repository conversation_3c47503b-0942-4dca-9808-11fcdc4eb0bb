package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.DecryptedField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/10/24
 */
@Data
@TypeDoc(description = "查看头图沉浸页筛选项入参")
@MobileRequest
public class GetImmersiveImageFilterRequest implements IMobileRequest, Serializable {
    @Deprecated
    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，原int类型")
    @MobileRequest.Param(name = "dealGroupId", required = true)
    private Integer dealGroupId;

    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，string类型")
    @MobileRequest.Param(name = "stringDealGroupId")
    private String stringDealGroupId;

    @FieldDoc(description = "商铺ID")
    @MobileRequest.Param(name = "shopId", required = true)
    private Long shopId;
    @MobileRequest.Param(name = "shopIdEncrypt")
    @DecryptedField(targetFieldName = "shopId")
    private String shopIdEncrypt;

    @FieldDoc(description = "团单二级类目ID")
    private Integer categoryId;

    @FieldDoc(description = "团单类型", rule = "点评团单:1，美团团单：2")
    private Integer dealGroupType;
}
