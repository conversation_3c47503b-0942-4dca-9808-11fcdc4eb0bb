package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.sankuai.nibscp.common.flow.identify.util.SptDyeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2024/6/25
 * @since mapi-dztgdetail-web
 */
@Slf4j
public class IndustryRcfTrackUtils {

    //分行业性能打点
    public static void industryRcfTrack(int categoryId) {
        try {
            //分行业性能打点
            List<SptDyeUtil.DyeTraceParam> traceParams = Lists.newArrayList();
            // step1 创建染色场景参数对象 (主场景、子场景)
            Map<String, String> industryMap = Lion.getMap("com.sankuai.dzu.tpbase.dztgdetailweb.industry.rcf.track");
            String subScene = industryMap.get(String.valueOf(categoryId));
            if(StringUtils.isBlank(subScene)) {
                subScene = "default";
            }
            SptDyeUtil.DyeTraceParam flowEntranceParam = SptDyeUtil.DyeTraceParam.ofMarketing("RCF_CATEGORY", subScene);
            traceParams.add(flowEntranceParam);
            SptDyeUtil.putDyeTraceParams(traceParams);
        } catch (Exception e) {
            log.error("industryRcfTrack error", e); }
    }
}
