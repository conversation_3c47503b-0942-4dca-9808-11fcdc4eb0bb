package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.handler;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/11/7 15:45
 */
@Service
@Slf4j
public class HomeApplianceRepairHandler implements BaseReserveMaintenanceHandler {
    private static final String MT_KEY = "MtApplianceReserveExp";
    private static final String DP_KEY = "DpApplianceReserveExp";
    private static final List<String> APPLIANCE_RESERVE_AB_KEYS = Lists.newArrayList(MT_KEY, DP_KEY);

    /**
     * 家电维修
     * 
     * @return
     */
    @Override
    public int getDealSecondCategory() {
        return 448;
    }

    @Override
    public String getExpName(boolean isMt) {
        return isMt ? MT_KEY : DP_KEY;
    }

    @Override
    public List<String> getAbKeys() {
        return APPLIANCE_RESERVE_AB_KEYS;
    }
}
