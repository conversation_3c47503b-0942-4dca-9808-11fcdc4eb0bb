package com.dianping.mobile.mapi.dztgdetail.biz;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.MtDealModel;
import com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam;
import com.dianping.mobile.mapi.dztgdetail.helper.PromoHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.UTMHelper;
import com.dianping.pay.common.enums.PayPlatform;
import com.dianping.pay.promo.common.enums.ProductType;
import com.dianping.pay.promo.display.api.dto.BatchQueryPromoDisplayRequest;
import com.dianping.pay.promo.display.api.dto.Product;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * Created by guozhengyao on 16/4/13.
 */
@Component
@Slf4j
public class DealCampaignBiz {

    private static final String CHANNEL_KEY = "channel";
    private static final String APP_KEY = "app";
    private static final String ANDROID = "android";
    private static final int MULTI_PROMO_TEMP_ID = 131;

    @Resource
    private PromoBiz promoBiz;

    public void bindingCampaign2DealsV2(List<MtDealModel> mtDealModels, MtCommonParam mtCommonParam) {
        if (CollectionUtils.isEmpty(mtDealModels)) {
            return;
        }

        BatchQueryPromoDisplayRequest request = new BatchQueryPromoDisplayRequest();
        List<Product> products = Lists.newArrayList();
        for (MtDealModel mtDealModel : mtDealModels) {
            Product product = new Product();
            product.setProductId(mtDealModel.getId());
            product.setPrice(BigDecimal.valueOf(mtDealModel.getPrice()));
            products.add(product);
        }

        request.setProductList(products);
        request.setUserId(mtCommonParam.getUserId());
        request.setCityId(mtCommonParam.getCityId());
        request.setTemplateID(MULTI_PROMO_TEMP_ID);
        request.setPlatform(PayPlatform.mt_iphone_native.getCode());
        String utmMedium = mtCommonParam.getUtmMedium();
        request.setClientVersion(mtCommonParam.getUtmMedium());
        Map<String, String> contextMap = Maps.newHashMap();
        contextMap.put(CHANNEL_KEY, mtCommonParam.getUtmSource());
        contextMap.put(APP_KEY, UTMHelper.convertUTMCampaign2App(mtCommonParam.getUtmCampaign()));
        request.setContextMap(contextMap);
        if (ANDROID.equals(utmMedium)) {
            request.setPlatform(PayPlatform.mt_android_native.getCode());
        }
        request.setProductType(ProductType.mt_tuangou.getValue());
        try {
            Map<Integer, List<PromoDisplayDTO>> promoMap = promoBiz.batchQueryPromoDisplayDTO(request);
            String version = mtCommonParam.getVersion();
            wrapDealsWithCampaigns(mtDealModels, promoMap, mtCommonParam.getUtmMedium(), version);
        } catch (Exception e) {
            log.error("fail to get deal's campaign" + e);
        }
    }

    private void wrapDealsWithCampaigns(List<MtDealModel> deals, Map<Integer, List<PromoDisplayDTO>> promoMap, String os, String osVersion) {
        if (deals == null || deals.size() == 0 || MapUtils.isEmpty(promoMap)) {
            return;
        }
        for (MtDealModel dealModel : deals) {
            Integer id = dealModel.getId();
            List<PromoDisplayDTO> promoDisplayDTOs = promoMap.get(id);
            PromoHelper.assemblePromoInfo(dealModel, promoDisplayDTOs);
        }
    }

}