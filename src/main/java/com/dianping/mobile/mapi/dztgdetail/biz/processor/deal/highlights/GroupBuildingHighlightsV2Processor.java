package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CommonAttrVO;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;


@Component
public class GroupBuildingHighlightsV2Processor extends AbstractHighlightsProcessor {

    @Override
    protected String getHighlightsIdentify(DealCtx ctx) {
        //和弹窗有关的，没有就不返
        return null;
    }

    @Override
    protected String getHighlightsStyle(DealCtx ctx) {
        return "struct";
    }

    @Override
    protected String getHighlightsContent(DealCtx ctx) {
        //content是在style为null的时候才展示的
        return null;
    }

    @Override
    protected List<CommonAttrVO> getHighlightsAttrs(DealCtx ctx) {
        if (CollectionUtils.isEmpty(ctx.getAttrs())) {
            return null;
        }
        List<CommonAttrVO> results = Lists.newArrayList();
        CommonAttrVO number = buildPeopleNumRange(ctx.getAttrs());
        if (Objects.nonNull(number)) {
            results.add(number);
        }
        CommonAttrVO price = buildPrice(ctx);
        if (Objects.nonNull(price)) {
            results.add(price);
        }
        for (StructEnum structEnum : StructEnum.values()) {
            String reallyShowValue = DealUtils.findFirstAttrValue(ctx.getAttrs(), structEnum.getKey());
            if (StringUtils.isEmpty(reallyShowValue)) {
                continue;
            }
            CommonAttrVO attrVO = new CommonAttrVO();
            attrVO.setName(structEnum.getName());
            attrVO.setValue(String.format(structEnum.getFormat(), reallyShowValue));
            results.add(attrVO);

        }
        return results;
    }

    private CommonAttrVO buildPrice(DealCtx ctx) {
        String number = DealUtils.findFirstAttrValue(ctx.getAttrs(), "limit_the_number_of_users");
        String reallyShowValue = DealUtils.findFirstAttrValue(ctx.getAttrs(), "averageAmusingPrice");
        if(StringUtils.isEmpty(reallyShowValue)){
            return null;
        }
        reallyShowValue = new BigDecimal(reallyShowValue).stripTrailingZeros().toPlainString();
        String format = "¥%s起";
        if ("1".equals(number)) {
            format= "¥%s";
        }
        CommonAttrVO commonAttrVO = new CommonAttrVO();
        commonAttrVO.setName("人均价格");
        commonAttrVO.setValue(String.format(format, reallyShowValue));
        return commonAttrVO;
    }

    private CommonAttrVO buildPeopleNumRange(List<AttributeDTO> attrs) {
        if (CollectionUtils.isEmpty(attrs)) {
            return null;
        }
        String minParticipantsCheck = DealUtils.findFirstAttrValue(attrs, "minParticipantsCheck");
        String maxParticipantsCheck = DealUtils.findFirstAttrValue(attrs, "maxParticipantsCheck");
        if (StringUtils.isEmpty(minParticipantsCheck) || StringUtils.isEmpty(maxParticipantsCheck)) {
            return null;
        }
        String peopleNumRange;
        if (minParticipantsCheck.equals(maxParticipantsCheck)) {
            peopleNumRange = minParticipantsCheck.concat("人");
        } else {
            peopleNumRange = String.format("%s-%s人", minParticipantsCheck, maxParticipantsCheck);
        }
        CommonAttrVO commonAttrVO = new CommonAttrVO();
        commonAttrVO.setName("人数范围");
        commonAttrVO.setValue(peopleNumRange);
        return commonAttrVO;
    }

    @Getter
    @AllArgsConstructor
    public enum StructEnum {
        DURATION("serviceDuration", "活动时长", "%s小时"),
        VENUE("venueTypeCheck", "场地", "%s"),
        INTENSITY("exerciseIntensityCheck", "运动强度", "%s");

        private final String key;
        private final String name;
        private final String format;
    }

    @Override
    protected void afterBuild(DealCtx ctx) {
    }

}
