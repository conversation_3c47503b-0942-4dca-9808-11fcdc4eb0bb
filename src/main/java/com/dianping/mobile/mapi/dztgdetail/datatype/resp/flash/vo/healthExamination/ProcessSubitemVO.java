package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.healthExamination;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/10/18 11:11 上午
 */
@MobileDo(id = 0xf651)
public class ProcessSubitemVO implements Serializable {
    /**
     * 模块值
     */
    @MobileDo.MobileField(key = 0x65c7)
    private String itemValue;

    /**
     * 模块名
     */
    @MobileDo.MobileField(key = 0xee12)
    private String itemName;

    public String getItemValue() {
        return itemValue;
    }

    public void setItemValue(String itemValue) {
        this.itemValue = itemValue;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }
}