package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.cat.Cat;
import com.dianping.deal.book.req.BookQueryRequest;
import com.dianping.deal.book.req.ShopBookDto;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.deal.common.enums.PageType;
import com.dianping.mobile.base.datatypes.enums.Platform;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.biz.DealDetailBizStrategy;
import com.dianping.mobile.mapi.dztgdetail.biz.PoiDetailBizStrategy;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealBookWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetMtPoiListRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.ComButton;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.MtPoiModel;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.ViewItemDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.ViewListDo;
import com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam;
import com.dianping.mobile.mapi.dztgdetail.util.ShopIdUpdateUtil;
import com.dianping.mobile.mapi.dztgdetail.util.SwitchUtils;
import com.google.common.collect.Lists;
import com.sankuai.mms.utils.VersionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * Description:
 * Author: lijie
 * Version: 0.0.1
 * Created: 16/5/10 上午10:29
 */
@Component
@Slf4j
public class GetMtPoiListFacade {
    @Resource
    private DealDetailBizStrategy dealDetailBizStrategy;
    @Resource
    private PoiDetailBizStrategy poiDetailBizStrategy;
    @Resource
    private DealBookWrapper dealBookWrapper;

    public ViewListDo getMtPoiList(GetMtPoiListRequest request, IMobileContext iMobileContext) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.GetMtPoiListFacade.getMtPoiList(com.dianping.mobile.mapi.dztgdetail.datatype.req.GetMtPoiListRequest,com.dianping.mobile.framework.datatypes.IMobileContext)");
        MtCommonParam mtCommonParam = new MtCommonParam(iMobileContext);
        mtCommonParam.setOffset(request.getStart());
        mtCommonParam.setLimit(request.getLimit());
        mtCommonParam.setLat(request.getLat());
        mtCommonParam.setLng(request.getLng());
        //按照距离排序
        mtCommonParam.setSortStr(request.getSort());
        mtCommonParam.setCityId(request.getCityId());
        Long poiId = request.getPoiIdLong();
        ViewListDo viewListDo;
        if (request.getDealId() != 0 && (poiId == null || poiId == 0)) {
            viewListDo = dealDetailBizStrategy.getMtPoiList(
                    request.getDealId(), request.getOnlyCurrentCityPois(), mtCommonParam);
        } else {
            //商户详情分店列表页
            viewListDo = poiDetailBizStrategy.getMtPoiList(poiId, request.getOnlyCurrentCityPois(), mtCommonParam);
        }
        fillRecallBtns(viewListDo, iMobileContext);
        return viewListDo;
    }

    private void fillRecallBtns(ViewListDo viewListDo, IMobileContext iMobileContext) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.GetMtPoiListFacade.fillRecallBtns(ViewListDo,IMobileContext)");
        if (viewListDo == null || CollectionUtils.isEmpty(viewListDo.getList()) || VersionUtil.compare(iMobileContext.getVersion(), "8.9") < 0) {
            return;
        }
        List<Long> mtShopIds = Lists.newArrayList();
        for (ViewItemDo viewItemDo : viewListDo.getList()) {
            MtPoiModel poiModel = viewItemDo.getMtShop();
            if (poiModel != null && SwitchUtils.isInBook(poiModel.getShowType())) {
                mtShopIds.add(Long.valueOf(viewItemDo.getMtShop().getPoiIdStr()));
            }
        }
        if (CollectionUtils.isEmpty(mtShopIds)) {
            return;
        }
        Map<Long, ShopBookDto> bookMap = dealBookWrapper.queryLongShopDealBooKInfo(buildBookQueryRequest(mtShopIds, iMobileContext));
        if (MapUtils.isNotEmpty(bookMap)) {
            for (ViewItemDo viewItemDo : viewListDo.getList()) {
                MtPoiModel poiModel = viewItemDo.getMtShop();
                if (poiModel == null || poiModel.getPoiIdStr() == null) {
                    continue;
                }
                ShopBookDto shopBookDto = bookMap.get(Long.valueOf(poiModel.getPoiIdStr()));
                ComButton comButton = getRecallBtns(shopBookDto);
                if (comButton != null) {
                    poiModel.setRecallButtons(Lists.newArrayList(comButton));
                }
            }
        }
    }

    private BookQueryRequest buildBookQueryRequest(List<Long> mtShopIds, IMobileContext context) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.GetMtPoiListFacade.buildBookQueryRequest(java.util.List,com.dianping.mobile.framework.datatypes.IMobileContext)");
        BookQueryRequest request = new BookQueryRequest();
        request.setShopIdsLong(mtShopIds);
        request.setIdIsMt(true);
        request.setPageType(PageType.BOOK_SHOP_LIST.getType());
        if (context.getClient().getPlatform() == Platform.iPhone) {
            request.setClientType(ClientTypeEnum.mt_mainApp_ios.getType());
        } else if (context.getClient().getPlatform() == Platform.Android) {
            request.setClientType(ClientTypeEnum.mt_mainApp_android.getType());
        } else {
            request.setClientType(ClientTypeEnum.mt_wap.getType());
        }
        return request;
    }

    private ComButton getRecallBtns(ShopBookDto shopBookDto) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.GetMtPoiListFacade.getRecallBtns(com.dianping.deal.book.req.ShopBookDto)");
        if (shopBookDto != null && shopBookDto.isHasBook()) {
            ComButton btn = new ComButton();
            btn.setTitle(shopBookDto.getBookActionName());
            btn.setClickUrl(shopBookDto.getBookUrl());
            btn.setAction(ComButton.ActionEnum.REDIRECT.type);
            return btn;
        }
        return null;
    }
}
