package com.dianping.mobile.mapi.dztgdetail.datatype.resp.other;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;

@TypeDoc(description = "到综团单折扣卡数据模型")
@MobileDo(id = 0x9fd9)
public class DiscountCardPBO implements Serializable {

    @FieldDoc(description = "标题")
    @MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "图标地址")
    @MobileField(key = 0xf39b)
    private String iconUrl;

    @FieldDoc(description = "用户会员卡状态：0未开；1已开")
    @MobileField(key = 0xe852)
    private int userMemberStatus;

    @FieldDoc(description = "折扣价（售卖价 * 折扣)")
    @MobileField(key = 0x9bc8)
    private double discountPrice;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public int getUserMemberStatus() {
        return userMemberStatus;
    }

    public void setUserMemberStatus(int userMemberStatus) {
        this.userMemberStatus = userMemberStatus;
    }

    public double getDiscountPrice() {
        return discountPrice;
    }

    public void setDiscountPrice(double discountPrice) {
        this.discountPrice = discountPrice;
    }
}
