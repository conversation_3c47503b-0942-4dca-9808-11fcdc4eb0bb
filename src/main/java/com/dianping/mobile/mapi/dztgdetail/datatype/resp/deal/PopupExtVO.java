package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;


@Data
@TypeDoc(description = "团单特征扩展")
@MobileDo(id = 0x37f976e)
public class PopupExtVO implements Serializable {

    @MobileDo.MobileField(key = 0xd536)
    @FieldDoc(description = "标题级别，1-主标题，2-一级标题")
    private Integer titleLevel;

    @MobileDo.MobileField(key = 0x24cc)
    @FieldDoc(description = "标题内容")
    private String title;

    @MobileDo.MobileField(key = 0xcce)
    @FieldDoc(description = "具体内容")
    private String content;

    @MobileDo.MobileField(key = 0x489b)
    @FieldDoc(description = "标题图片")
    private String titlePic;

}
