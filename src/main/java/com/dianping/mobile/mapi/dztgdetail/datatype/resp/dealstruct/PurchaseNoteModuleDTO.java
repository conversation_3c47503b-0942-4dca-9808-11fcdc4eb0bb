package com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealstruct;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2024/1/11
 */
@Data
@TypeDoc(description = "展示模块")
@MobileDo(id = 0x7382)
public class PurchaseNoteModuleDTO implements Serializable {
    @FieldDoc(description = "模块名")
    @MobileDo.MobileField(key = 0x195b)
    private String pnModuleName;

    @FieldDoc(description = "图标")
    @MobileDo.MobileField(key = 0x4ad8)
    private String pnIcon;

    @FieldDoc(description = "展示条目")
    @MobileDo.MobileField(key = 0x1ccb)
    private List<PnStandardDisplayItemDTO> pnItems;
}
