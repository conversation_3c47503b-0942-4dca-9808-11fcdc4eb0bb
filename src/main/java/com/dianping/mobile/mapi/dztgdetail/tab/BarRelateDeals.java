package com.dianping.mobile.mapi.dztgdetail.tab;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.struct.query.api.entity.dto.DealDetailDto;
import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.OptionalSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.CommissionWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.BaseLoadParam;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.DealTab;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.SourceDataHolder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;

/**
 * <AUTHOR>
 * @date 2023/1/19
 */
@Service
public class BarRelateDeals extends AllRelateDeals {

    @Autowired
    CommissionWrapper commissionWrapper;

    @Autowired
    private QueryCenterWrapper queryCenterWrapper;

    public static final Map<Long, String> DRINKS_CATEGORY_MAP = new HashMap<Long, String>() {
        {
            put(2104502L, "啤酒");
            put(2104525L, "鸡尾酒");
            put(2104526L, "洋酒");
            put(2104527L, "可乐桶");
            put(2104528L, "葡萄酒");
            put(2104529L, "特色酒");
            put(2104530L, "其他类型酒品");
            put(2104531L, "软饮");
        }
    };

    @Override
    public List<Integer> identifyByPublishCategory() {
        return Lists.newArrayList(312);
    }

    @Override
    public void loadAfterRelate(BaseLoadParam param, SourceDataHolder holder) {
        super.loadAfterRelate(param, holder);

        AllDealSourceDataHolder allHolder = (AllDealSourceDataHolder) holder;
        Future normalPriceFuture = priceDisplayWrapper.preBatchQueryNormalPrice(param);
        allHolder.setDealNormalPriceMap(priceDisplayWrapper.getBatchQueryNormalPrice(param, normalPriceFuture));
    }

    protected List<String> dealAttrsToLoadAfterRelate() {
        return Lists.newArrayList("service_type", "number_of_pkg");
    }

    @Override
    protected DealTab buildDealTab(Integer dpDealGroupId, SourceDataHolder holder) {
        AllDealSourceDataHolder tagSourceDataHolder = (AllDealSourceDataHolder) holder;
        DealGroupBaseDTO dealGroupBaseDTO = tagSourceDataHolder.getDealBaseMap().get(dpDealGroupId);
        List<AttributeDTO> attributeList = tagSourceDataHolder.getDealAttrMap().get(dpDealGroupId);
        DealDetailDto dealDetailDto = tagSourceDataHolder.getDealStructMap().get(dpDealGroupId);
        PriceDisplayDTO priceDisplayDTO = tagSourceDataHolder.getDealNormalPriceMap().get(dpDealGroupId);

        if (dealGroupBaseDTO == null || CollectionUtils.isEmpty(attributeList)) {
            return null;
        }
        String serviceType = AttributeUtils.getAttributeValue("service_type", attributeList);
        DealTab dealTab = null;
        switch (serviceType) {
            case "酒水":
                dealTab = buildDrinksDealTab(dealGroupBaseDTO, priceDisplayDTO, dealDetailDto);
                break;
            case "酒水小食套餐":
                dealTab = buildDrinksAndFoodsDealTab(dealGroupBaseDTO, priceDisplayDTO, attributeList);
                break;
            case "餐食":
                dealTab = buildFoodsDealTab(dealGroupBaseDTO, priceDisplayDTO, dealDetailDto);
                break;
            case "门票入场券":
            case "入场券":
                dealTab = buildDealTab(dealGroupBaseDTO, priceDisplayDTO, "入场券");
                break;
            default:
                return buildDealTab(dealGroupBaseDTO, priceDisplayDTO, serviceType);
        }
        if (dealTab == null) {
            dealTab = buildDealTab(dealGroupBaseDTO, priceDisplayDTO, serviceType);
        }
        return dealTab;
    }

    private DealTab buildDrinksDealTab(DealGroupBaseDTO dealGroupBaseDTO, PriceDisplayDTO priceDisplayDTO, DealDetailDto dealDetailDto) {
        if (dealDetailDto == null || dealDetailDto.getSkuUniStructuredDto() == null
                || CollectionUtils.isEmpty(dealDetailDto.getSkuUniStructuredDto().getMustGroups())) {
            return null;
        }
        List<SkuItemDto> skuItemList = Lists.newArrayList();
        skuItemList.addAll(dealDetailDto.getSkuUniStructuredDto().getMustGroups()
                .stream()
                .map(MustSkuItemsGroupDto::getSkuItems)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(dealDetailDto.getSkuUniStructuredDto().getOptionalGroups())) {
            skuItemList.addAll(dealDetailDto.getSkuUniStructuredDto().getOptionalGroups()
                    .stream()
                    .map(OptionalSkuItemsGroupDto::getSkuItems)
                    .filter(CollectionUtils::isNotEmpty)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList()));
        }

        Map<Long, Set<String>> drinksTypeCategoryMap = Maps.newHashMap();
        for (SkuItemDto skuItemDto : skuItemList) {
            if (skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems())) {
                continue;
            }
            for (SkuAttrItemDto skuAttrItemDto : skuItemDto.getAttrItems()) {
                if (skuAttrItemDto == null || !"homePkgCategory".equals(skuAttrItemDto.getAttrName())) {
                    continue;
                }
                if (!drinksTypeCategoryMap.containsKey(skuItemDto.getProductCategory())) {
                    drinksTypeCategoryMap.put(skuItemDto.getProductCategory(), Sets.newHashSet());
                }
                drinksTypeCategoryMap.get(skuItemDto.getProductCategory()).add(skuAttrItemDto.getAttrValue());
            }
        }
        String drinksTypeString = null;
        if (drinksTypeCategoryMap.keySet().size() == 1) {
            Set<String> homePkgCategorySet = drinksTypeCategoryMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
            if (homePkgCategorySet.size() == 1) {
                drinksTypeString = StringUtils.join(homePkgCategorySet, "+");
            }
        }
        if (StringUtils.isBlank(drinksTypeString)) {
            drinksTypeString = StringUtils.join(drinksTypeCategoryMap.keySet().stream().map(DRINKS_CATEGORY_MAP::get).filter(Objects::nonNull).collect(Collectors.toSet()), "+");
        }

        if (StringUtils.isBlank(drinksTypeString)) {
            return null;
        }
        return buildDealTab(dealGroupBaseDTO, priceDisplayDTO, drinksTypeString);
    }

    private DealTab buildDrinksAndFoodsDealTab(DealGroupBaseDTO dealGroupBaseDTO, PriceDisplayDTO priceDisplayDTO, List<AttributeDTO> attributeList) {
        if (CollectionUtils.isEmpty(attributeList)) {
            return null;
        }
        String suitPeopleAttrValue = AttributeUtils.getAttributeValue("number_of_pkg", attributeList);
        if (StringUtils.isBlank(suitPeopleAttrValue)) {
            return null;
        }
        return buildDealTab(dealGroupBaseDTO, priceDisplayDTO, suitPeopleAttrValue);
    }

    private DealTab buildFoodsDealTab(DealGroupBaseDTO dealGroupBaseDTO, PriceDisplayDTO priceDisplayDTO, DealDetailDto dealDetailDto) {
        if (dealDetailDto == null || dealDetailDto.getSkuUniStructuredDto() == null
                || CollectionUtils.isEmpty(dealDetailDto.getSkuUniStructuredDto().getMustGroups())) {
            return null;
        }
        List<SkuItemDto> skuItems = dealDetailDto.getSkuUniStructuredDto().getMustGroups().stream()
                .map(MustSkuItemsGroupDto::getSkuItems)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuItems)) {
            return null;
        }
        String foodsContentString = null;
        if (skuItems.size() > 1) {
            int foodsCount = skuItems.stream().map(skuItemDto -> {
                String unit = skuItemDto.getAttrItems().stream()
                        .filter(attrItem -> "unit".equals(attrItem.getAttrName()))
                        .map(SkuAttrItemDto::getAttrValue)
                        .findAny()
                        .orElse("0");
                return skuItemDto.getCopies() * Integer.parseInt(unit);
            }).mapToInt(Integer::intValue).sum();
            if (foodsCount > 0) {
                foodsContentString = String.format("小食%s份", foodsCount);
            }
        } else {
            SkuItemDto skuItemDto = skuItems.get(0);
            int foodsCount = Integer.parseInt(skuItemDto.getAttrItems().stream()
                    .filter(attrItem -> "unit".equals(attrItem.getAttrName()))
                    .map(SkuAttrItemDto::getAttrValue)
                    .findAny()
                    .orElse("0"));
            if (foodsCount > 0) {
                foodsContentString = String.format("%s%s份", skuItemDto.getName(), foodsCount);
            }
        }
        if (StringUtils.isBlank(foodsContentString)) {
            return null;
        }
        return buildDealTab(dealGroupBaseDTO, priceDisplayDTO, foodsContentString);
    }

    private DealTab buildDealTab(DealGroupBaseDTO dealGroupBaseDTO, PriceDisplayDTO priceDisplayDTO, String tag) {
        DealTab dealTab = new DealTab();
        dealTab.setDealGroupId(dealGroupBaseDTO.getDealGroupId());
        dealTab.setSalePriceWithUnit(priceDisplayDTO == null ? dealGroupBaseDTO.getDealGroupPrice() : priceDisplayDTO.getPrice());
        dealTab.setTag(tag);
        return dealTab;
    }

}
