package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.feature.model.FeatureDetailDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiClientWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ReserveProductWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.LEInsuranceAgreementEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.Guarantee;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.LayerConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.Icon;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.ProductDetailGuaranteeVO;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.PriceProtectionHelper;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.ReassuredRepairUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.clr.content.process.gateway.thrift.dto.book.BookStatusQueryGatewayReqDTO;
import com.sankuai.clr.content.process.gateway.thrift.dto.book.BookStatusQueryGatewayRespDTO;
import com.sankuai.clr.content.process.gateway.thrift.enums.BookStatusSceneEnum;
import com.sankuai.clr.content.process.gateway.thrift.enums.ContentGatewayCodeEnum;
import com.sankuai.clr.content.process.gateway.thrift.enums.SubjectTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericModuleResponse;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericProductDetailPageResponse;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTagNameEnum;
import com.sankuai.trade.general.reserve.response.ReserveResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.dianping.mobile.mapi.dztgdetail.util.GlassDealUtils.isGenuineGuarantee;
import static com.dianping.mobile.mapi.dztgdetail.util.GlassDealUtils.isSafePtometry;
import static com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils.getAnXinExerciseDisplayText;
import static com.dianping.mobile.mapi.dztgdetail.util.OralDealUtils.isSafeDenture;
import static com.dianping.mobile.mapi.dztgdetail.util.OralDealUtils.isSafeImplant;

/**
 * @Author: <EMAIL>
 * @Date: 2024/5/18
 */
@Component
@Slf4j
public class GuaranteeBuilderService {
    public static final Set<Integer> PARENT_CHILD_FUN = Sets.newHashSet(1002);

    @Autowired
    private DealGroupWrapper dealGroupWrapper;
    @Autowired
    PoiClientWrapper poiClientWrapper;
    @Autowired
    private ReserveProductWrapper reserveProductWrapper;
    @Autowired
    private DouHuService douHuService;

    private static final Set<Integer> ORAL_TEETH_CATEGORY = Sets.newHashSet(506);

    /**
     * 放心种植标签
     */
    private static final Set<Integer> SAFE_IMPLANT_TAGS = Sets.newHashSet(
            GuaranteeTagNameEnum.REASSURING_DENTAL_IMPLANT_3_YEARS_GUARANTEE.getCode(),
            GuaranteeTagNameEnum.REASSURING_DENTAL_IMPLANT_5_YEARS_GUARANTEE.getCode());

    /**
     * 3c认证标签
     */
    private static final Long tag3c = 100383225L;


    public List<Guarantee> getGuarantee(DealCtx ctx, boolean isFromBarManager) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.GuaranteeBuilderService.getGuarantee(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx,boolean)");
        ProductDetailGuaranteeVO productDetailGuaranteeVO = getCommonModuleGuarantee(ctx);
        if (ctx.isMtLiveMinApp()) {
            return Collections.emptyList();
        }
        List<Guarantee> result = Lists.newArrayList();
        if (DealAttrHelper.isWuyoutong(ctx)) {
            return Collections.emptyList();
        }
        // 3c认证标签
        if (isHit3c(ctx)) {
            result.add(Guarantee.builder()
                    .icon(LionConfigUtils.get3cIcon())
                    .iconHeight(16)
                    .iconWidth(58)
                    .type(1)
                    .style("#BB4D22")
                    .build());
        }
        // 安心学标签
        if (ctx.isAnXinXue()){
            result.add(Guarantee.builder()
                    .text(getAnXinXueDisplayText(productDetailGuaranteeVO))
                    .icon(getAnXinXueIcon(productDetailGuaranteeVO))
                    .iconHeight(12)
                    .iconWidth(48)
                    .type(1)
                    .style("#8E3C12")
                    .build());
            //
            return result;
        }

        // 安心练标签
        if (ctx.isAnXinExercise()){
            result.add(Guarantee.builder()
                    .text(getAnXinExerciseDisplayText())
                    .icon(LionConfigUtils.getAnXinExerciseIcon())
                    .iconHeight(12)
                    .iconWidth(36)
                    .type(1)
                    .style("#BB4D22")
                    .build());
            return result;
        }

        // 团购次卡 按次核销，剩余可退
        if(DealCtxHelper.hitCompensationForRunningAway(ctx)) {
            String icon = LionConfigUtils.getCompensationForRunningAwayIcon();
            return buildRunAwayCompensationGuarantees(result, icon);
        }

        //0元预约场景
        if (ctx.isFreeDeal()) {
            //疫苗
            if (ctx.getCategoryId() == 1611) {
                result.addAll(Lists.newArrayList(assembleGuarantee("正品可追溯"), assembleGuarantee("正规资质"), assembleGuarantee("支持改退")));
            }
            return result;
        }
        // 预订团单
        if (DealCtxHelper.isPreOrderDeal(ctx)) {
            if (CollectionUtils.isNotEmpty(ctx.getPreOrderFeatDetails())) {
                ctx.getPreOrderFeatDetails().forEach(feat -> {
                    if (Objects.nonNull(feat)) {
                        result.add(Guarantee.builder()
                                .text(feat.getText())
                                .style(feat.getStyle())
                                .type(feat.getType())
                                .icon(feat.getIcon())
                                .build());
                    }
                });
            }
            return result;
        }
        if (DealUtils.isTradeAssuranceDeal(ctx)) {
            Map<Long, LayerConfig> tagId2ConfigMap = LionConfigUtils.getTradeAssuranceLayerConfig(ctx.isMt());
            //从商品bp拿标签
            Map<Long, String> tagMap = Optional.ofNullable(ctx.getDealGroupDTO().getTags()).map(tags ->
                    tags.stream().collect(Collectors.toMap(DealGroupTagDTO::getId, DealGroupTagDTO::getTagName))).orElse(new HashMap<>());

            List<Guarantee> tradeAssuranceGuarantees = tagId2ConfigMap.keySet().stream().filter(tagMap::containsKey).
                    map(layerConfig -> assembleGuarantee(tagMap.get(layerConfig))).collect(Collectors.toList());
            result.addAll(tradeAssuranceGuarantees);
            return result;

        }
        //局改装修
        if (ReassuredRepairUtil.isTagPresent(ctx)) {
            addGuaranteesBasedOnServiceProjectAttributes(ctx, result);
            return result;
        }

        // 在线教育类
        if (LionConfigUtils.isEduOnlineDeal(ctx.getCategoryId(), getServiceTypeId(ctx.getDealGroupDTO()))) {
            List<String> attrValues = DealAttrHelper.getAttributeValues(ctx.getAttrs(), "refund_rule");
            if (CollectionUtils.isNotEmpty(attrValues)) {
                result.add(assembleGuarantee(attrValues.get(0)));
            }
            return result;
        }
        //眼科安心配镜和正品保障：type字段
        if (ctx.getCategoryId() == 406){
            //设置安心配镜
            if (isSafePtometry(ctx) && !isDefaultExpResult(ctx.getCityId4P(), ctx.getEnvCtx())) {
                result.add(Guarantee.builder()
                        .text("安心配镜")
                        .style("#003576")
                        .type(1)
                        .build());
            }
            if (isGenuineGuarantee(ctx) && !isDefaultExpResult(ctx.getCityId4P(), ctx.getEnvCtx())) {
                //设置正品保障
                result.add(Guarantee.builder()
                        .text("正品保障")
                        .style("#003576")
                        .type(1)
                        .build());
            }
        }
        //齿科
        if (ctx.getCategoryId() == 506){
            // 放心种植标签
            if (hasSafeImplantTag(ctx)) {
                result.add(Guarantee.builder()
                        .text("")
                        .icon("https://p0.meituan.net/ingee/769b243d7c0ea088c695470091fe967d2794.png")
                        .iconWidth(50)
                        .iconHeight(12)
                        .type(0)
                        .style("#038880")
                        .build());
            }
            //设置安心补牙
            if(isSafeDenture(ctx) && LionConfigUtils.dentalSwitch()){
                result.add(Guarantee.builder()
                        .text("免费重补・免费复查")
                        .icon("https://p1.meituan.net/travelcube/7214dc8ddc44579ba88758f78e6bb02e15880.png")
                        .type(2)
                        .style("#038880")
                        .build());
            }
            //设置安心种植牙
            if (isSafeImplant(ctx)){
                result.add(Guarantee.builder()
                        .text("免费复种・正品保障・种植体质保・免费复查")
                        .icon("https://p1.meituan.net/travelcube/1dfac41cc35d1f21fc8d6a8d1345308515690.png")
                        .type(2)
                        .style("#038880")
                        .build());
            }
        }
        // 设置买贵必赔
        if (PriceProtectionHelper.checkBestPriceGuaranteeInfoValid(ctx.getBestPriceGuaranteeInfo())) {
            result.add(Guarantee.builder()
                    .text("买贵必赔")
                    .style("#8E3C12")
                    .type(1)
                    .icon("https://p1.meituan.net/travelcube/409cdc9bf49ebac30967bf41d62665781754.png").build());
        }
        //设置价保
        if (LionConfigUtils.showPriceProtectionInfo(ctx.getCategoryId()) && PriceProtectionHelper.checkPriceProtectionValid(ctx.getPriceProtectionInfo())) {
            result.add(assembleGuarantee("价保" + ctx.getPriceProtectionInfo().getPriceProtectionTag().getValidityDays() + "天"));
        }

        if (Objects.nonNull(ctx.getFeatureDetailDTO()) && Objects.nonNull(ctx.getFeatureDetailDTO().getLayerConfig())) {
            result.add(Guarantee.builder()
                    .text(ctx.getFeatureDetailDTO().getText())
                    .style(ctx.getFeatureDetailDTO().getStyle())
                    .type(ctx.getFeatureDetailDTO().getType())
                    .build());
        }

        //设置洗涤履约保障
        //当条件为保洁自营时走保洁自营否则走原有逻辑
        if (ctx.getLEInsuranceAgreementEnum() != null) {
            if (ctx.getLEInsuranceAgreementEnum() == LEInsuranceAgreementEnum.CLEANING_SELF_OWN_PRODUCT){
                // 保洁自营
                result.add(assembleGuarantee("迟到爽约赔"));
                result.add(assembleGuarantee("财产损失赔"));
                result.add(assembleGuarantee("不满意重做"));
            } else {
                result.add(Guarantee.builder()
                        .text(ctx.getLEInsuranceAgreementEnum().getText())
                        .style("#744E2A")
                        .type(1)
                        .icon("https://p1.meituan.net/travelcube/9c4b563d359a7c5dd078efb00b57bb8b2155.png").build());
            }
        }

        // 设置履约保障标签
        if (CollectionUtils.isNotEmpty(ctx.getShopTagFeatures())) {
            ctx.getShopTagFeatures().forEach(tag -> {
                if (Objects.nonNull(tag)) {
                    result.add(Guarantee.builder()
                            .text(tag.getText())
                            .style(tag.getStyle())
                            .type(tag.getType())
                            .build());
                }
            });
        }
        //当酒吧经理时添加额外保洁自营判断条件
        //酒吧营销经理、保洁自营不需要
        if (!isFromBarManager && !(ctx.getLEInsuranceAgreementEnum() != null && ctx.getLEInsuranceAgreementEnum() == LEInsuranceAgreementEnum.CLEANING_SELF_OWN_PRODUCT)) {
            if (checkRefundByProduct(ctx)) {
                result.add(assembleGuarantee("未预约可退"));
            } else if (ctx.getDealGroupBase().getAutoRefundSwitch() > 0) {
                result.add(assembleGuarantee("随时退"));
            }
            if (ctx.getDealGroupBase().isOverdueAutoRefund()) {
                result.add(assembleGuarantee("过期退"));
            }
        }

        if (!ctx.isEnableCardStyleV2()) {
            result.addAll(getReservationInfoV2(ctx));
        }

        String applicableTime = getApplicableTimeDesc(ctx);

        if (applicableTime != null) {
            result.add(assembleGuarantee(applicableTime));
        }

        String applicablePeople = getApplicablePeopleDesc(ctx);

        if (applicablePeople != null) {
            result.add(assembleGuarantee(applicablePeople));
        }

        if (!isFromBarManager) {
            if (applicablePeople == null && applicableTime == null && ctx.isPurchaseCanBook()) {
                result.add(assembleGuarantee("购后可在线预约"));
            }

            if (physicalExamReserveOnline(ctx)) {
                result.add(assembleGuarantee("可在线预约"));
            }

            if (parentChildFunReserveOnline(ctx)) {
                result.add(assembleGuarantee("在线预约"));
            }
        }

        //如果快照类目设置了团购在线预约,展示在线预约
        if (photoReserveOnline(ctx)) {
            removeTagsV2(result);
            result.add(assembleGuarantee("在线预约"));
        }

        if(!ObjectUtils.isEmpty(ctx.getAdditionalInfo())&&
                ctx.getAdditionalInfo().isAdditional()) {
            result.add(assembleGuarantee("可加项"));
        }
        return result;
    }

    private ProductDetailGuaranteeVO getCommonModuleGuarantee(DealCtx ctx) {
        try {
            GenericProductDetailPageResponse response = ctx.getCommonModuleResponse();
            if (Objects.isNull(response) || org.apache.commons.collections4.MapUtils.isEmpty(response.getModuleResponse())) {
                return null;
            }
            Map<String, GenericModuleResponse> moduleResponse = response.getModuleResponse();
            // 保障条和保障浮层
            GenericModuleResponse guaranteeInfoResponse = moduleResponse.get("module_detail_guarantee_info_tag");
            if (Objects.nonNull(guaranteeInfoResponse) && Objects.nonNull(guaranteeInfoResponse.getModuleVO())) {
                return JSON.parseObject(JSON.toJSONString(guaranteeInfoResponse.getModuleVO()), ProductDetailGuaranteeVO.class);
            }
            return null;
        } catch (Exception e) {
            log.error("GuaranteeBuilderService.getCommonModuleGuarantee error, ctx:{}", ctx, e);
        }
        return null;
    }

    private String getAnXinXueDisplayText(ProductDetailGuaranteeVO guaranteeVO){
        if (Objects.nonNull(guaranteeVO) && Objects.nonNull(guaranteeVO.getContents())) {
            return guaranteeVO.getContents().get(0).getText();
        }
        return LionConfigUtils.getAnXinXueDisplayText();
    }

    private String getAnXinXueIcon(ProductDetailGuaranteeVO guaranteeVO){
        if (Objects.nonNull(guaranteeVO) && Objects.nonNull(guaranteeVO.getContents())) {
            Icon icon = guaranteeVO.getContents().get(0).getPrefixIcon();
            if (Objects.nonNull(icon)) {
                return icon.getIcon();
            }
        }
        return LionConfigUtils.getAnXinXueIcon();
    }

    /**
     * 构造跑路赔 保障条
     * @param guarantees
     * @param icon
     * @return
     */
    public List<Guarantee> buildRunAwayCompensationGuarantees(List<Guarantee> guarantees, String icon) {
        guarantees.add(Guarantee.builder()
                .text("按次核销")
                .icon(icon)
                .iconHeight(12)
                .iconWidth(12)
                .type(1)
                .style("#8E3C12")
                .build());
        guarantees.add(Guarantee.builder()
                .text("剩余可退")
                .type(1)
                .style("#8E3C12")
                .build());
        return guarantees;
    }

    public boolean hasSafeImplantTag(DealCtx ctx) {
        // 类目匹配
        if (!LionConfigUtils.showSafeImplantTag(ctx)) {
            return false;
        }

        // 标签匹配
        return Objects.nonNull(ctx.getSafeImplantTag()) && SAFE_IMPLANT_TAGS.contains(ctx.getSafeImplantTag().getCode());
    }

    private void addGuaranteesBasedOnServiceProjectAttributes(DealCtx ctx, List<Guarantee> result) {
        Map<String, ServiceProjectAttrDTO> serviceProjectAttrDTOMap = Optional.of(ctx.getDealGroupDTO())
                .map(DealGroupDTO::getServiceProject)
                .map(DealGroupServiceProjectDTO::getMustGroups)
                .filter(groups -> !groups.isEmpty())
                .map(groups -> groups.get(0))
                .map(MustServiceProjectGroupDTO::getGroups)
                .filter(groups -> !groups.isEmpty())
                .map(groups -> groups.get(0))
                .map(ServiceProjectDTO::getAttrs)
                .map(attrs -> attrs.stream().collect(Collectors.toMap(ServiceProjectAttrDTO::getAttrName, Function.identity())))
                .orElse(Collections.emptyMap());
        if (serviceProjectAttrDTOMap.containsKey("zhichiyufujinketui")) {
            ServiceProjectAttrDTO zhichiyufujinketui = serviceProjectAttrDTOMap.get("zhichiyufujinketui");
            if (zhichiyufujinketui.getAttrValue().equals("是")) {
                result.add(Guarantee.builder().text("上门后不满意预付金可退").build());
            }
        }
        if (serviceProjectAttrDTOMap.containsKey("spuCategory")) {
            result.add(Guarantee.builder().text("延期必赔").build());
        }
        if (serviceProjectAttrDTOMap.containsKey("is_special")) {
            ServiceProjectAttrDTO special = serviceProjectAttrDTOMap.get("is_special");
            if (!special.getAttrValue().equals("否")) {
                result.add(Guarantee.builder().text("保修" + special.getAttrValue()).build());
            }
        }
    }

    private boolean isDefaultExpResult(Integer cityId,EnvCtx envCtx){
        // 判断是否命中a组实验
        String expResult = douHuService.getGlassDealDetailExpResult(cityId,envCtx);
        return "a".equals(expResult);
    }

    public Guarantee assembleGuarantee(String text) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.GuaranteeBuilderService.assembleGuarantee(java.lang.String)");
        return Guarantee.builder().text(text).build();
    }

    public Long getServiceTypeId(DealGroupDTO dealGroupDTO) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.GuaranteeBuilderService.getServiceTypeId(com.sankuai.general.product.query.center.client.dto.DealGroupDTO)");
        if (dealGroupDTO == null || dealGroupDTO.getCategory() == null) {
            return null;
        }
        return dealGroupDTO.getCategory().getServiceTypeId();
    }

    public boolean checkRefundByProduct(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.GuaranteeBuilderService.checkRefundByProduct(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        if (!LionConfigUtils.hitCustomRefundCategoryConfig(ctx.getCategoryId())) {
            return false;
        }

        List<String> attrValueList = DealAttrHelper.getAttributeValues(ctx.getAttrs(), "reservation_policy");
        if (CollectionUtils.isEmpty(attrValueList)) {
            return false;
        }

        String refundDesc = attrValueList.iterator().next();
        return StringUtils.equals("预约成功后不可退改", refundDesc);
    }

    public List<Guarantee> getReservationInfoV2(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.GuaranteeBuilderService.getReservationInfoV2(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        List<Guarantee> reservationInfo = new ArrayList<>();
        Guarantee reservation = null;
        //从上单信息（团单属性）中判断是否需预约、是否支持上门、是否支持到店
        boolean needReservation = DealAttrHelper.needReservation(ctx.getAttrs());
        boolean supportHome = DealAttrHelper.isSupportHomeService(ctx.getAttrs());
        boolean supportShop = DealAttrHelper.isSupportShopService(ctx.getAttrs());
        if (reserveAfterPurchase(ctx.getCategoryId())) {
            if (reserveOnline(ctx)) {
                reservation = assembleGuarantee("在线预约");
            } else if (supportHome) {
                reservation = assembleGuarantee("预约上门");
            } else if (supportShop && needReservation) {
                reservation = assembleGuarantee("预约到店");
            } else if (needReservation) {
                reservation = assembleGuarantee("需预约");
            }
        } else if (needReservation) {
            reservation = assembleGuarantee("需预约");
        }
        if (reservation != null) {
            reservationInfo.add(reservation);
        }
        if (!DealAttrHelper.needReservation(ctx.getAttrs()) && !forceReserve(ctx.getCategoryId())) {
            reservationInfo.add(assembleGuarantee("免预约"));
        }
        return reservationInfo;
    }

    public String getApplicableTimeDesc(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.GuaranteeBuilderService.getApplicableTimeDesc(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        if (CollectionUtils.isEmpty(ctx.getAttrs()) || ORAL_TEETH_CATEGORY.contains(ctx.getCategoryId())) {
            return null;
        }
        //口腔齿科定制化适用时间展示tag
        if (DealAttrHelper.workDayAvailable(ctx.getAttrs())) {
            return "仅工作日可用";
        }
        return null;
    }

    public String getApplicablePeopleDesc(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.GuaranteeBuilderService.getApplicablePeopleDesc(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        if (CollectionUtils.isEmpty(ctx.getAttrs()) || ORAL_TEETH_CATEGORY.contains(ctx.getCategoryId())) {
            return null;
        }
        List<String> peopleApplicable = DealAttrHelper.getAttributeValues(ctx.getAttrs(), DealAttrKeys.TOOTH_SUIT_PEOPLE);

        if (CollectionUtils.isEmpty(peopleApplicable)) {
            return null;
        }
        if (peopleApplicable.contains("成人") && peopleApplicable.contains("儿童")) {
            return "成人/儿童通用";
        }
        if (peopleApplicable.contains("成人")) {
            return "限成人";
        }
        if (peopleApplicable.contains("儿童")) {
            return "限儿童";
        }
        return null;
    }

    /**
     * 体检中心预约
     *
     * @param ctx
     * @return
     */
    public boolean physicalExamReserveOnline(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.GuaranteeBuilderService.physicalExamReserveOnline(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        String config = ctx.isMt() ? "mt" + ctx.getMtLongShopId() : "dp" + ctx.getDpLongShopId();
        List<String> configs = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb.physical.exam.third.party.shops", String.class, new ArrayList<>());

        boolean isCfgShop = CollectionUtils.isNotEmpty(configs) && configs.contains(config);
        boolean isThirdPartyDealGroup = dealGroupWrapper.isThirdPartyDealGroup(ctx.getFutureCtx().getDealGroupThirdPartyFuture());

        return isThirdPartyDealGroup && isCfgShop;
    }

    /**
     * 亲子游乐在线预约
     *
     * @param ctx 团单信息的环境变量
     * @return 商家是否设置在线预约
     */
    public boolean parentChildFunReserveOnline(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.GuaranteeBuilderService.parentChildFunReserveOnline(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        if (!PARENT_CHILD_FUN.contains(ctx.getCategoryId())) {
            return false;
        }
        boolean bookable = false;
        BookStatusQueryGatewayReqDTO reqDTO = new BookStatusQueryGatewayReqDTO();
        reqDTO.setSubjectId((long) ctx.getDpId());
        reqDTO.setSubjectType(SubjectTypeEnum.DEAL_GROUP.getCode());
        reqDTO.setScene(BookStatusSceneEnum.TRADE.getCode());
        BookStatusQueryGatewayRespDTO respDTO;
        try {
            respDTO = poiClientWrapper.getFutureResult(ctx.getFutureCtx().getBookStatusFuture(), "", "query");
            if (respDTO == null || respDTO.getCommonResp() == null) {
                return false;
            }
        } catch (Exception e) {
            log.error("BookStatusGatewayService Query Error!", e);
            return false;
        }
        if (ContentGatewayCodeEnum.SUCESS.getValue() == respDTO.getCommonResp().getCode()) {
            bookable = respDTO.getBookable();
        }
        return bookable;
    }

    /**
     * 仅快照类目,团单需预约，且当前适用门店可进行在线预约
     *
     * @param ctx 团单信息的环境变量
     * @return 商家是否设置在线预约
     */
    public boolean photoReserveOnline(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.GuaranteeBuilderService.photoReserveOnline(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        if (!LionConfigUtils.isSnapShotPhoto(ctx.getCategoryId())) {
            return false;
        }
        return ctx.isShowReserveEntrance();
    }

    //针对快照删除所有可能冲突的预约文案,如购后可在线预约、需预约、免预约
    public void removeTagsV2(List<Guarantee> result) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.GuaranteeBuilderService.removeTagsV2(java.util.List)");
        if (null == result) {
            return;
        }
        result.remove(assembleGuarantee("需预约"));
        result.remove(assembleGuarantee("免预约"));
        result.remove(assembleGuarantee("可在线预约"));
        result.remove(assembleGuarantee("购后可在线预约"));
    }

    /**
     * 是否是团购后预约适用类目
     *
     * @param categoryId
     * @return
     */
    public boolean reserveAfterPurchase(int categoryId) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.GuaranteeBuilderService.reserveAfterPurchase(int)");
        List<Integer> categories = Lion.getList(LionConstants.AVAILABLE_CATEGORY_IDS, Integer.class, Lists.newArrayList());
        return CollectionUtils.isNotEmpty(categories) && categories.contains(categoryId);
    }

    /**
     * 调用交易接口trade-general-reserve-api判断当前团单是否支持在线预约
     *
     * @param ctx
     * @return
     */
    public boolean reserveOnline(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.GuaranteeBuilderService.reserveOnline(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        try {
            ReserveResponse<Boolean> reserveResponse = reserveProductWrapper.getFutureResult(ctx);
            if (reserveResponse != null && reserveResponse.isSuccess()) {
                return reserveResponse.getResult();
            }
        } catch (Exception e) {
            log.error("reserveOnlineFuture err, dpGroupId : {}", ctx != null ? ctx.getDpId() : 0, e);
            return false;
        }
        return false;
    }

    /**
     * 是否强制预约
     *
     * @param categoryId
     * @return
     */
    public boolean forceReserve(int categoryId) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.GuaranteeBuilderService.forceReserve(int)");
        List<Integer> config = Lion.getList(LionConstants.FORCE_BOOKING_PUBLISH_CATEGORY, Integer.class, Lists.newArrayList());
        return CollectionUtils.isNotEmpty(config) && config.contains(categoryId);
    }

    public List<String> getFeatures(DealCtx ctx, boolean isFromBarManager) {
        if (ctx.isMtLiveMinApp()) {
            return Collections.emptyList();
        }
        List<String> result = Lists.newArrayList();
        if (DealAttrHelper.isWuyoutong(ctx)) {
            return Collections.emptyList();
        }
        //0元预约场景
        if(ctx.isFreeDeal()){
            //疫苗---0元预约
            if (ctx.getCategoryId() == 1611){
                result.addAll(Lists.newArrayList("正品可追溯","正规资质","支持改退"));
            }
            return result ;
        }
        // 预订团单
        if (DealCtxHelper.isPreOrderDeal(ctx)) {
            if (CollectionUtils.isNotEmpty(ctx.getPreOrderFeatDetails())) {
                ctx.getPreOrderFeatDetails().forEach(feat -> {
                    if (Objects.nonNull(feat)) {
                        result.add(feat.getText());
                    }
                });
            }
            return result;
        }
        // 设置买贵必赔
        if (PriceProtectionHelper.checkBestPriceGuaranteeInfoValid(ctx.getBestPriceGuaranteeInfo())) {
            result.add("买贵必赔");
        }
        //设置价保
        if (LionConfigUtils.showPriceProtectionInfo(ctx.getCategoryId()) && PriceProtectionHelper.checkPriceProtectionValid(ctx.getPriceProtectionInfo())) {
            result.add("价保" + ctx.getPriceProtectionInfo().getPriceProtectionTag().getValidityDays() + "天");
        }

        // 设置履约保障标签
        if (CollectionUtils.isNotEmpty(ctx.getShopTagFeatures())) {
            ctx.getShopTagFeatures().forEach(tag -> {
                if (Objects.nonNull(tag)) {
                    result.add(tag.getText());
                }
            });
        }

        //酒吧营销经理不需要
        if (!isFromBarManager) {
            if (checkRefundByProduct(ctx)) {
                result.add("未预约可退");
            } else if (ctx.getDealGroupBase().getAutoRefundSwitch() > 0) {
                result.add("随时退");
            }
            if (ctx.getDealGroupBase().isOverdueAutoRefund()) {
                result.add("过期退");
            }
        }
        if (!ctx.isEnableCardStyleV2()) {
            result.addAll(getReservationInfo(ctx));
        }

        String applicableTime = getApplicableTimeDesc(ctx);

        if (applicableTime != null) {
            result.add(applicableTime);
        }

        String applicablePeople = getApplicablePeopleDesc(ctx);

        if (applicablePeople != null) {
            result.add(applicablePeople);
        }

        if (!isFromBarManager) {
            if (applicablePeople == null && applicableTime == null && ctx.isPurchaseCanBook()) {
                result.add("购后可在线预约");
            }
            if (physicalExamReserveOnline(ctx)) {
                result.add("可在线预约");
            }

            if (parentChildFunReserveOnline(ctx)) {
                result.add("在线预约");
            }

            //如果快照类目设置了团购在线预约,展示在线预约
            if (photoReserveOnline(ctx)) {
                removeTags(result);
                result.add("在线预约");
            }
        }
        if(!ObjectUtils.isEmpty(ctx.getAdditionalInfo())&&
                ctx.getAdditionalInfo().isAdditional()) {
            result.add("可加项");
        }
        return result;
    }

    public List<String> getReservationInfo(DealCtx ctx) {
        List<String> reservationInfo = new ArrayList<>();
        String reservation = null;
        //从上单信息（团单属性）中判断是否需预约、是否支持上门、是否支持到店
        boolean needReservation = DealAttrHelper.needReservation(ctx.getAttrs());
        boolean supportHome = DealAttrHelper.isSupportHomeService(ctx.getAttrs());
        boolean supportShop = DealAttrHelper.isSupportShopService(ctx.getAttrs());
        if (reserveAfterPurchase(ctx.getCategoryId())) {
            if (reserveOnline(ctx)) {
                reservation = "在线预约";
            } else if (supportHome) {
                reservation = "预约上门";
            } else if (supportShop && needReservation) {
                reservation = "预约到店";
            } else if (needReservation) {
                reservation = "需预约";
            }
        } else if (needReservation) {
            reservation = "需预约";
        }
        if (reservation != null) {
            reservationInfo.add(reservation);
        }
        if (!DealAttrHelper.needReservation(ctx.getAttrs()) && !forceReserve(ctx.getCategoryId())) {
            reservationInfo.add("免预约");
        }
        return reservationInfo;
    }

    //针对快照删除所有可能冲突的预约文案,如购后可在线预约、需预约、免预约
    public void removeTags(List<String> result) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.removeTags(java.util.List)");
        if (null == result) {
            return;
        }
        result.remove("需预约");
        result.remove("免预约");
        result.remove("可在线预约");
        result.remove("购后可在线预约");
    }

    public List<String> getSpecialFeatures(DealCtx ctx) {
        List<String> specialFeatures = Lists.newArrayList();
        if (ORAL_TEETH_CATEGORY.contains(ctx.getCategoryId())) {
            specialFeatures = getPhysicianOperationSpecialTagId(ctx);
        }
        return specialFeatures;
    }

    private List<String> getPhysicianOperationSpecialTagId(DealCtx ctx) {
        List<String> resultName = new ArrayList<>();
        if (CollectionUtils.isEmpty(ctx.getAttrs())) {
            return resultName;
        }
        List<String> resultTagIdList = DealAttrHelper.getAttributeValues(ctx.getAttrs(), DealAttrKeys.ORAL_DENTISTRY_RULE);
        if (CollectionUtils.isEmpty(resultTagIdList)) {
            return resultName;
        }
        Map<String, String> specialTagConfig = getSpecialTagConfig();
        if (MapUtils.isEmpty(specialTagConfig)) {
            return resultName;
        }
        for (String tagIdStr : resultTagIdList) {
            String tagName = specialTagConfig.get(tagIdStr);
            if (StringUtils.isNotBlank(tagName)) {
                resultName.add(tagName);
            }
        }
        return resultName;
    }

    private Map<String, String> getSpecialTagConfig() {
        Map<String, String> config = Lion.getMap(LionConstants.SPECIAL_TAG_CONFIG);
        return config;
    }

    public List<LayerConfig> convertFeatureLayerConfigs2LayerConfigs(List<FeatureDetailDTO> featureDetailDTOS) {
        if (CollectionUtils.isEmpty(featureDetailDTOS)) {
            return Lists.newArrayList();
        }
        return featureDetailDTOS.stream()
                .filter(feat -> Objects.nonNull(feat) && Objects.nonNull(feat.getLayerConfig()))
                .map(FeatureDetailDTO::getLayerConfig)
                .collect(Collectors.toList());
    }

    // 3c认证的类目 + 查询中心有3c标签
    private boolean isHit3c(DealCtx ctx) {
        if (!ctx.getEnvCtx().judgeMainApp()) {
            return false;
        }
        long serviceTypeId = Optional.ofNullable(ctx.getDealGroupDTO())
                .map(DealGroupDTO::getCategory)
                .map(DealGroupCategoryDTO::getServiceTypeId)
                .orElse(0L);
        List<DealGroupTagDTO> dealGroupTagDTOS = Optional.ofNullable(ctx.getDealGroupDTO())
                .map(DealGroupDTO::getTags)
                .orElse(Collections.emptyList());
        boolean hasTag3c = dealGroupTagDTOS.stream()
                .anyMatch(t -> tag3c.equals(t.getId()));

        return LionConfigUtils.get3cServiceTypeList().contains(serviceTypeId) && hasTag3c;
    }

}
