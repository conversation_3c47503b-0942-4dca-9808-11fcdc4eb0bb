package com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealstruct;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2024/1/11
 */
@Data
@TypeDoc(description = "展示条目")
@MobileDo(id = 0x7bbd)
public class PnStandardDisplayItemDTO implements Serializable {
    @FieldDoc(description = "展示条目名称")
    @MobileDo.MobileField(key = 0xe3cc)
    private String pnItemName;

    @FieldDoc(description = "展示条目值")
    @MobileDo.MobileField(key = 0x64aa)
    private List<PnStandardDisplayValueDTO> pnItemValues;
}
