package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2024/3/20
 * @since mapi-dztgdetail-web
 */
public enum CouponValidStatusEnum {

    UN_VALID(0, "无效"),
    VALID(1, "有效");


    final private int code;
    final private String name;

    CouponValidStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }


}
