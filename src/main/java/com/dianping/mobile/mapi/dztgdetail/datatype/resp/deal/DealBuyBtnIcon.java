package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@TypeDoc(description = "团单购买按钮图标")
@MobileDo(id = 0x9fb3)
@EqualsAndHashCode
@NoArgsConstructor
public class DealBuyBtnIcon implements Serializable {

    @FieldDoc(description = "图标文本")
    @MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "文本颜色")
    @MobileField(key = 0x4754)
    private String titleColor;

    @FieldDoc(description = "边框颜色")
    @MobileField(key = 0xad82)
    private String borderColor;

    @FieldDoc(description = "文本颜色")
    @MobileField(key = 0xe5d4)
    private String bgColor;

    @FieldDoc(description = "图标样式（底色 or 图片链接）")
    @MobileField(key = 0x1b3a)
    private String style;

    @FieldDoc(description = "图标类型：1.底色，2.图片")
    @MobileField(key = 0x8f0c)
    private Integer type;

    public DealBuyBtnIcon(String style, Integer type) {
        this.style = style;
        this.type = type;
    }

    public DealBuyBtnIcon(String title, String titleColor, String style, Integer type) {
        this.title = title;
        this.style = titleColor;
        this.borderColor = titleColor; //原来都是统一颜色
        this.titleColor = titleColor;
        this.type = type;
        if (type == 2) {
            this.style = style;
            this.bgColor = titleColor;
        } else {
            this.style = titleColor;
            this.bgColor = style;
        }
    }

    public DealBuyBtnIcon(String title, String titleColor, String bgColor, String borderColor, Integer type) {
        this.type = type;
        this.title = title;
        this.style = bgColor;
        this.bgColor = bgColor;
        this.titleColor = titleColor;
        this.borderColor = borderColor;
    }

    public DealBuyBtnIcon(String title, String style, String titleColor, String bgColor, String borderColor, Integer type) {
        this.type = type;
        this.title = title;
        this.style = style;
        this.bgColor = bgColor;
        this.titleColor = titleColor;
        this.borderColor = borderColor;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getStyle() {
        return style;
    }

    public void setStyle(String style) {
        this.style = style;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getTitleColor() {
        return titleColor;
    }

    public void setTitleColor(String titleColor) {
        this.titleColor = titleColor;
    }

    public String getBgColor() {
        return bgColor;
    }

    public void setBgColor(String bgColor) {
        this.bgColor = bgColor;
    }

    public String getBorderColor() {
        return borderColor;
    }

    public void setBorderColor(String borderColor) {
        this.borderColor = borderColor;
    }
}
