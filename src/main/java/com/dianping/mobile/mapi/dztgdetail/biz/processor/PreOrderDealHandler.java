package com.dianping.mobile.mapi.dztgdetail.biz.processor;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.HaimaWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PreOrderWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.sankuai.fbi.lifeevent.reserverpcapi.dto.ProductShopReserveSupportStatusDTO;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @author: zhangyuan103
 * @create: 2025-04-02
 * @description: [家政]强预订团单处理器。
 *               若命中 商店白名单 + 非营销场 + 团单类别 + 家政团购有预约属性，则代表是强预订的团单，跳转到融合提单页面
 */
public class PreOrderDealHandler extends AbsDealProcessor {
    @Resource
    HaimaWrapper haimaWrapper;

    @Resource
    PreOrderWrapper preOrderWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        // 若命中 团单类别 + 非营销场 + 商店白名单，则进行rpc调用判断家政团购是否有预约属性
        return DealUtils.isPreOrderDealForCateAndPromotion(ctx)
                && haimaWrapper.isHitPreOrderDpShopId(ctx.getDpLongShopId());
    }

    @Override
    public void prepare(DealCtx ctx) {
        ctx.getFutureCtx().setPreOrderDealRpcFuture(preOrderWrapper.preProductShopReserve(ctx));
    }

    @Override
    public void process(DealCtx ctx) {
        if (Objects.isNull(ctx.getFutureCtx()) || Objects.isNull(ctx.getFutureCtx().getPreOrderDealRpcFuture())) {
            return;
        }
        ProductShopReserveSupportStatusDTO productShopReserveSupportStatusDTO = preOrderWrapper
                .queryProductShopReserve(ctx.getFutureCtx().getPreOrderDealRpcFuture());
        if (Objects.isNull(productShopReserveSupportStatusDTO)) {
            return;
        }
        // 若命中 商店白名单 + 非营销场 + 团单类别 + 家政团购有预约属性，则代表是强预订的团单
        ctx.setPreOderDeal(productShopReserveSupportStatusDTO.getReserveEnable());
    }
}
