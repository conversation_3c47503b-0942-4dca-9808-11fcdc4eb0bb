package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.detail.calc.enums.MixedContentType;
import com.dianping.deal.detail.dto.DealGroupTemplateDetailDTO;
import com.dianping.deal.detail.dto.ImageContent;
import com.dianping.deal.detail.dto.MixedContent;
import com.dianping.deal.detail.enums.BlockTypeEnum;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.QueryCenterProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.DealCategoryFactory;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.impl.BathCategoryStrategyImpl;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule.DealDetailSpecificModuleHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule.DealDetailSpecificModuleHandlerContainer;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule.SpecificModuleCtx;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.imagetext.strategy.ImageTextDetailHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.HaimaWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ContentType;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealImageTextDetailReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.SpecificModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ContentDetailPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailSpecificModuleVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ImageTextDetailPBO;
import com.dianping.mobile.mapi.dztgdetail.entity.ActivityConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.CleaningProductLionConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.DisclaimerConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.ImageTextCompressGrayConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.LifeClearHaiMaConfig;
import com.dianping.mobile.mapi.dztgdetail.exception.QueryCenterResultException;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.SwitchHelper;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.dianping.mobile.mapi.dztgdetail.util.HtmlUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupBasicInfoBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupCategoryBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupChannelBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupDetailBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupIdBuilder;
import com.sankuai.general.product.query.center.client.builder.model.ServiceProjectBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.AttrSubjectEnum;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class DealDetailFacade {

    @Autowired
    private DealGroupWrapper dealGroupWrapper;

    @Autowired
    private DealGroupQueryService queryCenterDealGroupQueryService;

    @Autowired
    private DealDetailSpecificModuleHandlerContainer dealDetailSpecificModuleHandlerContainer;

    @Autowired
    private DealCategoryFactory dealCategoryFactory;

    @Resource
    private HaimaWrapper haimaWrapper;

    @Resource
    private ImageTextDetailHandler imageTextDetailHandler;

    public ImageTextDetailPBO queryImageTextFromQueryCenter(DealImageTextDetailReq req, EnvCtx envCtx) {
        ImageTextDetailPBO result = new ImageTextDetailPBO();

        DealGroupDTO dealGroupDTO = null;
        try {
            dealGroupDTO = getDealGroupDTO(req, envCtx);
        } catch (TException e) {
            throw new QueryCenterResultException("getDealGroupDTO error,", e);
        }
        DealGroupBasicDTO dealGroupBase = dealGroupDTO.getBasic();
        int dpId = dealGroupDTO.getDpDealGroupIdInt() == null ? 0 : dealGroupDTO.getDpDealGroupIdInt();

        if (dealGroupBase == null || dealGroupBase.getStatus() == null) {
            Cat.logEvent("NullPointer", "dealGroupBase or dealGroupBase.getStatus() is null");
            return result;
        }

        if (dealGroupBase.getStatus() < 1) {
            return result;
        }

        int publishCategoryId = Math.toIntExact(dealGroupDTO.getCategory() == null ? 0 : dealGroupDTO.getCategory().getCategoryId());
        Future detailFuture = dealGroupWrapper.preDealGroupObjectDetail(dpId, envCtx.isMt());

        List<AttrDTO> attributeDTOS = dealGroupDTO.getAttrs();
        List<DealGroupTemplateDetailDTO> templateDetailDTOs = dealGroupWrapper.getFutureResult(detailFuture);

        if (CollectionUtils.isEmpty(templateDetailDTOs) || hideDetailV2(attributeDTOS, publishCategoryId)) {
            return result;
        }
        //获取“产品介绍”或者“套餐详情介绍”任一，他们的type都等于5
        Optional<DealGroupTemplateDetailDTO> productOptional = templateDetailDTOs.stream()
                .filter(temp -> temp.getType() == BlockTypeEnum.PRODUCT_INFO.getBlockType())
                .findFirst();
        if (!productOptional.isPresent()) {
            return result;
        }

        List<ContentPBO> contents = Lists.newArrayList();
        contents.addAll(getActivityContents(dpId));
        contents.addAll(product2ContentPBOS(productOptional.get(), publishCategoryId));
        // 添加免责声明
        DisclaimerConfig showDisclaimersConfig = LionConfigUtils.getShowDisclaimersConfig();
        if (showDisclaimers(attributeDTOS, publishCategoryId, showDisclaimersConfig)) {
            contents.add(new ContentPBO(ContentType.TEXT.getType(), showDisclaimersConfig.getText()));
        }
        return imageTextDetailHandler.buildImageTextDetail(dealGroupDTO, contents, envCtx, req.getPageSource());
    }

    private boolean showDisclaimers(List<AttrDTO> attributeDTOS, int publishCategoryId,
            DisclaimerConfig showDisclaimersConfig) {
        if (Objects.isNull(showDisclaimersConfig) || CollectionUtils.isEmpty(showDisclaimersConfig.getCategoryIds())) {
            return false;
        }
        return showDisclaimersConfig.getCategoryIds().contains(publishCategoryId) && DealUtils.isHotDeal(attributeDTOS);
    }

    private DealGroupDTO getDealGroupDTO(DealImageTextDetailReq req, EnvCtx envCtx) throws TException {
        QueryDealGroupListResponse queryDealGroupListResponse = null;
        Set<Long> set = new HashSet<>();
        set.add(Long.valueOf(req.getDealgroupid()));
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(set, envCtx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP)
                .dealGroupId(DealGroupIdBuilder.builder().all())
                .basicInfo(DealGroupBasicInfoBuilder.builder().status())
                .category(DealGroupCategoryBuilder.builder().all())
                .detail(DealGroupDetailBuilder.builder().all())
                .serviceProject(ServiceProjectBuilder.builder().all())
                .attrsByKey(AttrSubjectEnum.DEAL_GROUP,
                        Stream.concat(Arrays.stream(BathCategoryStrategyImpl.ATTR_ARR),
                                Stream.of(DealAttrKeys.VOUCHER, DealAttrKeys.TOP_PERFORMING_PRODUCT))
                                .toArray(String[]::new))
                .channel(DealGroupChannelBuilder.builder().all())
                .build();
        queryDealGroupListResponse = queryCenterDealGroupQueryService.queryByDealGroupIds(queryByDealGroupIdRequest);

        if(queryDealGroupListResponse == null) {
            throw new QueryCenterResultException("queryCenter returns null, request : " + GsonUtils.toJsonString(queryByDealGroupIdRequest));
        }

        if(queryDealGroupListResponse.getCode() != 0) {
            throw new QueryCenterResultException("queryCenter not success, request : " + GsonUtils.toJsonString(queryByDealGroupIdRequest));
        }

        if(CollectionUtils.isEmpty(queryDealGroupListResponse.getData().getList())) {
            throw new QueryCenterResultException("queryDealGroupListResponse.getData().getList() is empty, request : " + GsonUtils.toJsonString(queryByDealGroupIdRequest));
        }

        if(queryDealGroupListResponse.getData().getList().get(0) == null) {
            throw new QueryCenterResultException("queryDealGroupListResponse.getData().getList().get(0) is null, request : " + GsonUtils.toJsonString(queryByDealGroupIdRequest));
        }

        return queryDealGroupListResponse.getData().getList().get(0);
    }

    public ImageTextDetailPBO queryImageText(DealImageTextDetailReq req, EnvCtx envCtx) {
        if(GreyUtils.isQueryCenterGreyBatch2(req.getDealgroupid())) {
            try {
                return queryImageTextFromQueryCenter(req, envCtx);
            } catch (Exception e) {
                log.error("queryImageTextFromQueryCenter error, ", e);
            }
        }
        ImageTextDetailPBO result = new ImageTextDetailPBO();

        int dpId = envCtx.isMt() ? dealGroupWrapper.getDpDealGroupId(req.getDealgroupid()) : req.getDealgroupid();

        Future groupBaseFuture = dealGroupWrapper.preDealGroupBase(dpId);
        DealGroupBaseDTO dealGroupBase = dealGroupWrapper.getFutureResult(groupBaseFuture);

        if (dealGroupBase == null || dealGroupBase.getStatus() < 1) {
            return result;
        }

        Future categoryFuture = dealGroupWrapper.preDealGroupChannelById(dpId);
        Future detailFuture = dealGroupWrapper.preDealGroupObjectDetail(dpId, envCtx.isMt());
        Future attrFuture = dealGroupWrapper.preAttrs(dpId, Lists.newArrayList(DealAttrKeys.VOUCHER));

        List<AttributeDTO> attributeDTOS = dealGroupWrapper.getFutureResult(attrFuture);
        List<DealGroupTemplateDetailDTO> templateDetailDTOs = dealGroupWrapper.getFutureResult(detailFuture);
        DealGroupChannelDTO dealGroupChannelDTO = dealGroupWrapper.getFutureResult(categoryFuture);

        int categoryId = getCategoryId(dealGroupChannelDTO);
        if (CollectionUtils.isEmpty(templateDetailDTOs) || hideDetail(attributeDTOS, categoryId)) {
            return result;
        }
        //获取“产品介绍”或者“套餐详情介绍”任一，他们的type都等于5
        Optional<DealGroupTemplateDetailDTO> productOptional = templateDetailDTOs.stream()
                .filter(temp -> temp.getType() == BlockTypeEnum.PRODUCT_INFO.getBlockType())
                .findFirst();
        if (!productOptional.isPresent()) {
            return result;
        }
        List<ContentPBO> contents = Lists.newArrayList();
        contents.addAll(getActivityContents(dpId));
        contents.addAll(product2ContentPBOS(productOptional.get(), categoryId));

        ContentDetailPBO contentDetail = new ContentDetailPBO();

        CleaningProductLionConfig config = Lion.getBean(LionConstants.APP_KEY, LionConstants.CLEANING_PRODUCT_INFORMATION_CONFIG, CleaningProductLionConfig.class);
        List<LifeClearHaiMaConfig> list = haimaWrapper.cleaningProductInformation(config);
        contentDetail.setTitle(getSelfTitle(list, dpId, config));

        contentDetail.setContents(contents);

        /*
          fold = false：图文详情展开
          fold = true：图文详情折叠
          foldThreshold：contents size < foldThreshold时会忽略fold的值直接全部展开且无"收起"按钮
         */
        long userId = envCtx.isMt() ? envCtx.getMtUserId() : envCtx.getDpUserId();//已确认判断平台后再使用
        boolean fold = SwitchHelper.foldDetailCategoryStruct(dealGroupChannelDTO, userId, dpId);
        contentDetail.setFold(fold);
        contentDetail.setFoldThreshold(fold ? 2 : contents.size() + 1);

        result.setContents(Lists.newArrayList(contentDetail));
        return result;
    }

    private List<ContentPBO> getActivityContents(int dpId) {
        List<ContentPBO> result = Lists.newArrayList();
        List<ActivityConfig> activityConfigs = LionConfigUtils.getImgTextActivityConfigs();
        if (CollectionUtils.isEmpty(activityConfigs)) {
            return result;
        }
        Optional<ActivityConfig> activityConfig = activityConfigs.stream()
                .filter(config -> config != null && config.getDealGroupIds() != null && config.getDealGroupIds().contains(dpId))
                .findFirst();
        if (!activityConfig.isPresent()) {
            return result;
        }
        result.add(new ContentPBO(ContentType.PIC.getType(), activityConfig.get().getActivityPics()));
        return result;
    }

    private List<ContentPBO> product2ContentPBOS(DealGroupTemplateDetailDTO product, int categoryId) {
        if (Objects.isNull(product)) {
            return Collections.emptyList();
        }
        List<ContentPBO> contents = buildMixedContent(product, categoryId);

        // 上线阶段命中缓存时没有混合详情使用旧的分离详情
        if (CollectionUtils.isEmpty(contents)) {
            contents = buildSplitContent(product, categoryId, product.getId());
        }

        return contents;
    }

    private List<ContentPBO> buildMixedContent(DealGroupTemplateDetailDTO product, int categoryId) {
        if (CollectionUtils.isEmpty(product.getMixedContents())) {
            return Collections.emptyList();
        }

        List<ContentPBO> contents = new ArrayList<>();
        for (MixedContent mixedContent : product.getMixedContents()) {

            if (MixedContentType.TEXT.name().equals(mixedContent.getType())) {
                contents.add(new ContentPBO(ContentType.TEXT.getType(), HtmlUtils.html2text(mixedContent.getContent())));
            }

            if (MixedContentType.IMAGE.name().equals(mixedContent.getType())) {
                buildTitle(contents, mixedContent.getTitle());
                buildDesc(contents, mixedContent.getDesc());
                buildPic(contents, mixedContent.getContent(), categoryId, product.getId());
            }
        }

        return contents;
    }

    private void buildTitle(List<ContentPBO> contents, String title) {
        if (StringUtils.isNotBlank(title) && Lion.getBooleanValue(LionConstants.PRODUCT_TITLE_ENABLE, false)) {
            contents.add(new ContentPBO(ContentType.TITLE.getType(), title));
        }
    }

    private void buildDesc(List<ContentPBO> contents, String desc) {
        if (StringUtils.isNotBlank(desc)) {
            contents.add(new ContentPBO(ContentType.TEXT.getType(), desc));
        }
    }

    private void buildPic(List<ContentPBO> contents, String url, int categoryId, int dpDealGroupId) {
        if (StringUtils.isNotBlank(url)) {
            String processedUrl = processPicUrl(url, categoryId, dpDealGroupId);
            contents.add(new ContentPBO(ContentType.PIC.getType(), processedUrl));
        }
    }

    private String processPicUrl(String url, int categoryId, int dpDealGroupId) {
        try {
            ImageTextCompressGrayConfig imageTextCompressGrayConfig =
                    Lion.getBean(LionConstants.APP_KEY, LionConstants.IMAGE_TEXT_COMPRESS_GRAY_CONFIG, ImageTextCompressGrayConfig.class, null);
            if (imageTextCompressGrayConfig == null) {
                return url;
            }
            // 特殊后缀拼接
            if (MapUtils.isNotEmpty(imageTextCompressGrayConfig.getDpDealGroupId2SpecificPostfix())) {
                String postfix =  imageTextCompressGrayConfig.getDpDealGroupId2SpecificPostfix().getOrDefault(dpDealGroupId, "");
                if(StringUtils.isNotBlank(postfix)) {
                    return url + postfix;
                }
            }

            // 如果没有统一拼接后缀，直接返回url
            if (StringUtils.isBlank(imageTextCompressGrayConfig.getCommonPostfix())) {
                return url;
            }

            // 全量开关打开，返回url+ 统一拼接后缀
            if (imageTextCompressGrayConfig.isAllCompress()) {
                return url + imageTextCompressGrayConfig.getCommonPostfix();
            }
            // 根据品类灰度
            if (MapUtils.isNotEmpty(imageTextCompressGrayConfig.getCategory2GrayRatio())) {
                int grayRatio = imageTextCompressGrayConfig.getCategory2GrayRatio().getOrDefault(categoryId, 0);
                if(isInGray(dpDealGroupId, grayRatio)) {
                    return url + imageTextCompressGrayConfig.getCommonPostfix();
                }
            }
        } catch (Exception e) {
            log.error("processPicUrl error, ", e);
        }

        return url;
    }

    private boolean isInGray(int dpDealGroupId, int grayRatio) {
        if (grayRatio < 0 || grayRatio > 100) {
            return false;
        }
        int tailNumber = dpDealGroupId % 100;
        return tailNumber < grayRatio;
    }


    private List<ContentPBO> buildSplitContent(DealGroupTemplateDetailDTO product, int categoryId, int dpDealGroupId) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.facade.DealDetailFacade.buildSplitContent(com.dianping.deal.detail.dto.DealGroupTemplateDetailDTO,int,int)");
        List<ContentPBO> contents = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(product.getImageContents())) {
            for (ImageContent imageContent : product.getImageContents()) {
                buildTitle(contents, imageContent.getTitle());
                buildDesc(contents, imageContent.getDesc());
                buildPic(contents, imageContent.getPath(), categoryId, dpDealGroupId);
            }
        }

        if (StringUtils.isNotBlank(product.getContent())) {
            //html转换成text
            contents.add(new ContentPBO(ContentType.TEXT.getType(), HtmlUtils.html2text(product.getContent())));
        }

        return contents;
    }

    private static int getCategoryId(DealGroupChannelDTO dealGroupChannelDTO) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.DealDetailFacade.getCategoryId(com.dianping.deal.publishcategory.dto.DealGroupChannelDTO)");
        if (dealGroupChannelDTO == null) {
            return 0;
        }
        return dealGroupChannelDTO.getCategoryId();
    }

    private static boolean hideDetail(List<AttributeDTO> attributeDTOS, int categoryId) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.DealDetailFacade.hideDetail(java.util.List,int)");
        return DealAttrHelper.isVoucher(attributeDTOS) && SwitchHelper.hideDetailCategory(categoryId);
    }

    private static boolean hideDetailV2(List<AttrDTO> attributeDTOS, int categoryId) {
        return DealAttrHelper.isVoucherV2(attributeDTOS) && SwitchHelper.hideDetailCategory(categoryId);
    }


    public DealDetailSpecificModuleVO getDealDetailSpecificModule(SpecificModuleReq request, EnvCtx envCtx) {
        Integer dpDealGroupId;

        if(GreyUtils.isQueryCenterGreyBatch1(request.getDealgroupid())) {
            try {
                return getDealDetailSpecificModuleFromQueryCenter(request, envCtx);
            } catch (Exception e) {
                log.error("getDealDetailSpecificModuleFromQueryCenter error, ", e);
            }
        }

        if (envCtx.isMt()) {
            dpDealGroupId = dealGroupWrapper.getDpDealGroupId(request.getDealgroupid());
        } else {
            dpDealGroupId = request.getDealgroupid();
        }

        int categoryId = dealGroupWrapper.getCategoryId(dpDealGroupId);
        DealDetailSpecificModuleHandler handler = dealDetailSpecificModuleHandlerContainer.getHandler(String.valueOf(categoryId));

        if (handler == null) {
            return null;
        }

        SpecificModuleCtx ctx = new SpecificModuleCtx();
        ctx.setExtraInfo(request.getExtrajson());
        ctx.setDpDealGroupId(dpDealGroupId);
        handler.handle(ctx);

        return ctx.getResult();
    }

    private DealDetailSpecificModuleVO getDealDetailSpecificModuleFromQueryCenter(SpecificModuleReq request, EnvCtx envCtx) {
        QueryDealGroupListResponse queryDealGroupListResponse = null;

        Set<Long> set = new HashSet<>();
        set.add(Long.valueOf(request.getDealgroupid()));
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(set, envCtx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP)
                .dealGroupId(DealGroupIdBuilder.builder().all())
                .category(DealGroupCategoryBuilder.builder().all())
                .attrsByKey(AttrSubjectEnum.DEAL_GROUP, QueryCenterProcessor.getQueryCenterDealGroupAttrKey())
                .serviceProject(ServiceProjectBuilder.builder().all())
                .build();

        try {
            queryDealGroupListResponse = queryCenterDealGroupQueryService.queryByDealGroupIds(queryByDealGroupIdRequest);
        } catch (Exception e) {
            throw new QueryCenterResultException("queryCenter throws exception", e);
        }

        if(queryDealGroupListResponse == null) {
            throw new QueryCenterResultException("queryCenter returns null, request : " + GsonUtils.toJsonString(queryByDealGroupIdRequest));
        }

        if(queryDealGroupListResponse.getCode() != 0) {
            throw new QueryCenterResultException("queryCenter not success, request : " + GsonUtils.toJsonString(queryByDealGroupIdRequest));
        }

        if(CollectionUtils.isEmpty(queryDealGroupListResponse.getData().getList())) {
            throw new QueryCenterResultException("queryDealGroupListResponse.getData().getList() is empty, request : " + GsonUtils.toJsonString(queryByDealGroupIdRequest));
        }

        if(queryDealGroupListResponse.getData().getList().get(0) == null) {
            throw new QueryCenterResultException("queryDealGroupListResponse.getData().getList().get(0) is null, request : " + GsonUtils.toJsonString(queryByDealGroupIdRequest));
        }

        DealGroupDTO dealGroupDTO = queryDealGroupListResponse.getData().getList().get(0);
        Long categoryId = dealGroupDTO.getCategory() == null ? 0L : dealGroupDTO.getCategory().getCategoryId();

        DealDetailSpecificModuleHandler handler = dealDetailSpecificModuleHandlerContainer.getHandler(String.valueOf(categoryId));

        if (handler == null) {
            return null;
        }

        SpecificModuleCtx ctx = new SpecificModuleCtx();
        ctx.setDpDealGroupId(dealGroupDTO.getDpDealGroupIdInt());
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.setUseQueryCenter(true);
        ctx.setExtraInfo(request.getExtrajson());
        handler.handle(ctx);

        return ctx.getResult();
    }


    /**
     * 保洁自营时不反回标题中内容
     * 故意设置为“”空用来反回空字符串展示结果
     * @param list
     * @param id
     * @param config
     * @return
     */
    public String getSelfTitle(List<LifeClearHaiMaConfig> list, int id, CleaningProductLionConfig config) {
        if (config != null && config.isEnable()) {
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(list)) {
                for (LifeClearHaiMaConfig item : list) {
                    String mtIdString = String.valueOf(id);
                    if (mtIdString.equals(item.getId())) {
                        return null;
                    }
                }
            }
        }
        return "图文详情";
    }
}
