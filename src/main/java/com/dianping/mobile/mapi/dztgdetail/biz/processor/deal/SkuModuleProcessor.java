package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.CreateOrderPageUrlBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.CleaningSelfOperationWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ImmersiveImageWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.SkuWrapper;
import com.dianping.mobile.mapi.dztgdetail.button.normal.OdpSourceButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.common.ShopUuidUtils;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.SkuCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DztgSkuModule;
import com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.RedirectUrls;
import com.dianping.tuangu.dztg.usercenter.api.dto.BatchGetCreateOrderPageUrlDto;
import com.dianping.tuangu.dztg.usercenter.api.dto.GetCreateOrderPageUrlEnvDto;
import com.dianping.tuangu.dztg.usercenter.api.dto.Response;
import com.dianping.tuangu.dztg.usercenter.api.enums.CreateOrderPageSourceEnum;
import com.dianping.tuangu.dztg.usercenter.api.request.BatchGetCreateOrderPageUrlReq;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.general.product.query.center.client.enums.AttrTypeEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Stream;

@Slf4j
@Component
public class SkuModuleProcessor extends AbsDealProcessor {

    @Resource
    private CreateOrderPageUrlBiz createOrderPageUrlBiz;

    @Resource
    private SkuWrapper skuWrapper;

    @Resource
    private ImmersiveImageWrapper immersiveImageWrapper;

    @Resource
    private CleaningSelfOperationWrapper cleaningSelfOperationWrapper;

    private static final Long POSTPARTUM_CARE_HOUSE_SERVICE_TYPE_ID = 126041L;

    /**
     * 次数
     */
    private static final String CLASS_COUNT = "ClassCount";


    @Override
    public boolean isEnable(DealCtx ctx) {
        return CreateOrderPageUrlBiz.useTradePageUrl() && CreateOrderPageUrlBiz.isMainApp(ctx.getEnvCtx())
                && doBusinessCheckProcess(ctx) && !RequestSourceEnum.fromTradeSnapshot(ctx.getRequestSource());
    }

    @Autowired
    DouHuBiz douHuBiz;

    @Override
    public void prepare(DealCtx ctx) {
        String skuId = ctx.getSkuId();
        EnvCtx envCtx = ctx.getEnvCtx();

        BatchGetCreateOrderPageUrlReq req = new BatchGetCreateOrderPageUrlReq();
        GetCreateOrderPageUrlEnvDto getCreateOrderPageUrlEnvDto = new GetCreateOrderPageUrlEnvDto();
        getCreateOrderPageUrlEnvDto.setPlatform(envCtx.isMt() ? 1 : 2);
        getCreateOrderPageUrlEnvDto.setChannel(CreateOrderPageUrlBiz.CLIENT_TYPE_MAP.get(envCtx.getDztgClientTypeEnum()));
        getCreateOrderPageUrlEnvDto.setPageSource(CreateOrderPageSourceEnum.DEAL_GROUP_DETAIL.type);
        getCreateOrderPageUrlEnvDto.setVersion(envCtx.getVersion());

        req.setEnvDto(getCreateOrderPageUrlEnvDto);

        BatchGetCreateOrderPageUrlDto batchGetCreateOrderPageUrlDto = new BatchGetCreateOrderPageUrlDto();
        batchGetCreateOrderPageUrlDto.setProductId(String.valueOf(envCtx.isMt() ? ctx.getMtId() : ctx.getDpId()));
        if (StringUtils.isNumeric(skuId) && Long.parseLong(skuId) > 0 && !DealUtils.isNewWearableNailDeal(ctx)) {
            //将请求中的skuId透传给提单页
            batchGetCreateOrderPageUrlDto.setSkuId(skuId);
        }
        long poiid4p = ctx.getLongPoiId4PFromResp();
        if (ctx.getEnvCtx().isMt()) {
            batchGetCreateOrderPageUrlDto.setShopId(String.valueOf(poiid4p));
        } else {
            batchGetCreateOrderPageUrlDto.setShopUuid(ctx.getShopUuidFromResp());
            if (ShopUuidUtils.retainShopId(poiid4p)) {
                batchGetCreateOrderPageUrlDto.setShopId(String.valueOf(poiid4p));
            }
        }
        fillSource(ctx, batchGetCreateOrderPageUrlDto);
        fillExtParam(ctx, batchGetCreateOrderPageUrlDto);
        req.setBatchGetCreateOrderPageUrlDtoList(Lists.newArrayList(batchGetCreateOrderPageUrlDto));
        fillOverNightAbResult(ctx);
        addMultiSkuParams(ctx, batchGetCreateOrderPageUrlDto);
        // 填充斗斛实验，以用于交易侧完成跳链拼接
        createOrderPageUrlBiz.fillOverNightDouHuSk(ctx, batchGetCreateOrderPageUrlDto);
        Future urlFuture = createOrderPageUrlBiz.createOrderPageUrlServiceFuture(req);
        ctx.getFutureCtx().setOrderUrlFuture(urlFuture);
    }

    /**
     * 填充扩展参数 dealextparam 目前只支持json转map
     * @param ctx
     * @param dto
     */
    public void fillExtParam(DealCtx ctx, BatchGetCreateOrderPageUrlDto dto) {
        try{
            String extParam = ctx.getRequestExtParam();
            Map<String, String> extParamMap = GsonUtils.fromJsonString(extParam, new TypeToken<Map<String, String>>() {}.getType());
            if (MapUtils.isEmpty(extParamMap)) {
                return;
            }
            String dealExtParam = UrlHelper.buildDealExtParam(extParamMap, OdpSourceButtonBuilder.REMAIN_EXT_PARAM_KEYS);
            if (StringUtils.isEmpty(dealExtParam)) {
                return;
            }
            createOrderPageUrlBiz.setExtUrlParam(dto, "dealextparam", dealExtParam);
        }catch (Exception e){
            log.error("SkuModuleProcessor fillExtParam error!, request = {}", JSON.toJSONString(dto), e);
        }
    }

    private void fillSource(DealCtx ctx, BatchGetCreateOrderPageUrlDto dto) {
        String requestSource = ctx.getRequestSource();
        if (StringUtils.isEmpty(requestSource)) {
            return;
        }
        createOrderPageUrlBiz.setExtUrlParam(dto, "source", ctx.getRequestSource());
    }

    @Override
    public void process(DealCtx ctx) {
        String skuId = ctx.getSkuId();
        long dealId = StringUtils.isNumeric(skuId) ? Long.parseLong(skuId) : 0;
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();

        if (dealGroupDTO == null || dealGroupDTO.getDeals() == null) {
            Cat.logEvent("NullPointer", "dealGroupDTO or dealGroupDTO.getDeals() is null");
            return;
        }
        //默认取第一个
        DealGroupDealDTO dealGroupDealDTO = dealGroupDTO.getDeals().stream()
                .filter(dealDTO -> dealDTO != null && dealDTO.getBasic() != null
                        && dealDTO.getBasic().getStatus() != null && dealDTO.getBasic().getStatus() == 1)
                .findFirst().orElse(null);
        if (dealId > 0) {//如果入参中指定了skuid，则取对应的
            dealGroupDealDTO = dealGroupDTO.getDeals().stream()
                    .filter(deal -> deal != null && deal.getBasic() != null
                            && deal.getBasic().getStatus() != null && deal.getBasic().getStatus() == 1
                            && deal.getDealId().equals(dealId))
                    .findFirst().orElse(null);
        }
        if (dealGroupDealDTO == null) {
            return;
        }

        if (CollectionUtils.isEmpty(dealGroupDealDTO.getAttrs())) {
            return;
        }

        // 对销售属性进行排序
        if (DealUtils.isNewWearableNailDeal(ctx) || DealUtils.isBabyCareHouseDeal(ctx) || LionConfigUtils.enableSkuAttrSort()) {
            DealGroupCategoryDTO categoryDTO = DealUtils.getDealGroupCategory(ctx);
            List<String> dealSaleAttrSort = getDealSaleAttrSort(categoryDTO.getServiceTypeId(), categoryDTO.getServiceType());
            dealGroupDealDTO.getAttrs().sort(Comparator.comparing(attr -> dealSaleAttrSort.indexOf(attr.getName())));
        }

        DztgSkuModule skuModule = new DztgSkuModule();

        dealGroupDealDTO.getAttrs().stream()
                .filter(attrDTO -> attrDTO.getType() != null && attrDTO.getType() == AttrTypeEnum.SALE_PROP.getCode())
                .forEach(attrDTO -> {
                    skuModule.getSkuAttrCnNameList().add(attrDTO.getCnName());
                    if (dealId > 0) {
                        if (Objects.equals(attrDTO.getName(), "styleId")) {
                            String styleIdStr = attrDTO.getValue().get(0);
                            String styleImageName = immersiveImageWrapper.getDealGroupStyleImageName(Long.parseLong(styleIdStr));
                            skuModule.getSkuAttrValueList().add(styleImageName);
                        } else {
                            skuModule.getSkuAttrValueList().add(getEduMusicAttr(attrDTO));
                        }
                    }
                });

        Response<Map<String, String>> mapResponse = skuWrapper.getFutureResult(ctx.getFutureCtx().getOrderUrlFuture());
        skuModule.setUrl(getOrderUrl(ctx, mapResponse));
        ctx.setSkuModule(skuModule);
    }

    private String getEduMusicAttr(AttrDTO attrDTO) {
        String name = Optional.ofNullable(attrDTO).map(AttrDTO::getName).orElse(StringUtils.EMPTY);
        String value = Optional.ofNullable(attrDTO).map(AttrDTO::getValue).orElse(Lists.newArrayList()).stream().filter(StringUtils::isNotBlank).findFirst().orElse(StringUtils.EMPTY);
        if (StringUtils.equals(CLASS_COUNT,name) && StringUtils.isNotBlank(value)) {
            int count = parseTimes(value);
            return count <= 0 ? value : String.format("%s节课",count);
        }
        return value;
    }

    private int parseTimes(String classCount) {
        if (StringUtils.isBlank(classCount)) {
            return 0;
        }
        String numStr = classCount.replaceAll("[^\\d]", "");
        return NumberUtils.toInt(numStr,0);
    }

    public String getOrderUrl(DealCtx ctx, Response<Map<String, String>> mapResponse) {
        String orderUrl = Optional.ofNullable(mapResponse)
                .map(Response::getContent)
                .map(Map::values)
                .map(Collection::stream)
                .flatMap(Stream::findAny)
                .orElse("");
        return RedirectUrls.filterOrderUrlSkuId(ctx, orderUrl);
    }


    /**
     * 填充足洗过夜的实验，会返回不同的跳链
     *
     * @param ctx
     */
    private void fillOverNightAbResult(DealCtx ctx) {
        // 只命中足疗、洗浴类目的团单
        if (ctx.getCategoryId() == 303 || ctx.getCategoryId() == 304) {
            ModuleAbConfig overNightAbConfig = getOverNightAbConfig(ctx);
            if (Objects.nonNull(overNightAbConfig)) {
                // 填充
                List<ModuleAbConfig> moduleAbConfigs = ctx.getModuleAbConfigs();
                if (moduleAbConfigs == null) {
                    moduleAbConfigs = Lists.newArrayList();
                }
                moduleAbConfigs.add(overNightAbConfig);
            }
        }
    }

    /**
     * 获取过夜的实验配置
     *
     * @param ctx
     * @return
     */
    private ModuleAbConfig getOverNightAbConfig(DealCtx ctx) {
        String module = null;
        if (ctx.getCategoryId() == 303) {
            module = ctx.isMt() ? "MTMassageOverNight" : "DPMassageOverNight";
        }
        if (ctx.getCategoryId() == 304) {
            module = ctx.isMt() ? "MTBathOverNight" : "DPBathOverNight";
        }
        if (StringUtils.isEmpty(module)) {
            return null;
        }
        return douHuBiz.getAbByUnionId(ctx.getEnvCtx().getUnionId(), module, ctx.isMt());
    }

    private void addMultiSkuParams(DealCtx ctx, BatchGetCreateOrderPageUrlDto pageUrlDto) {
        if (ctx.getSkuCtx() == null) {
            return;
        }

        Map<String, String> map = pageUrlDto.getExtUrlParam();
        if (MapUtils.isEmpty(map)) {
            map = new HashMap<>();
            pageUrlDto.setExtUrlParam(map);
        }

        SkuCtx skuCtx = ctx.getSkuCtx();

        // 设置是否多sku参数
        map.put("is_sku", skuCtx.isMultiSku() ? "1" : "0");

        // 设置提单链接服务参数
        if (StringUtils.isNotBlank(skuCtx.getCreatOrderExpId())) {
            map.put("expid", skuCtx.getCreatOrderExpId());
        }

        // 设置团详实验参数
        if (skuCtx.getDealCreatOrderAbConfig() != null && CollectionUtils.isNotEmpty(skuCtx.getDealCreatOrderAbConfig().getConfigs())) {
            try {
                map.put("expBiInfo", URLEncoder.encode(skuCtx.getDealCreatOrderAbConfig().getConfigs().get(0).getExpBiInfo(), StandardCharsets.UTF_8.name()));
            } catch (Exception e) {
                log.warn("expBiInfo={} encode failed", skuCtx.getDealCreatOrderAbConfig().getConfigs().get(0).getExpBiInfo(), e);
            }
        }
    }

    public List<String> getDealSaleAttrSort(Long serviceTypeId, String serviceType) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.SkuModuleProcessor.getDealSaleAttrSort(java.lang.Long,java.lang.String)");
        List<String> defaultSort;
        // 月子房型套餐团单
        if (Objects.equals(serviceTypeId, POSTPARTUM_CARE_HOUSE_SERVICE_TYPE_ID)) {
            defaultSort = Lists.newArrayList("stay_days", "care_mode", "door_to_door_serve_days");
        } else {
            defaultSort = Lists.newArrayList("styleId", "nailsType", "Jiapianchicun");
        }
        return getDealSaleAttrSort(serviceTypeId, serviceType, defaultSort);
    }

    private List<String> getDealSaleAttrSort(Long serviceTypeId, String serviceType, List<String> defaultSort) {
        String skuAttrConfigJson = Lion.getString("com.sankuai.dztheme.dealgroup", "sku.attr.config");
        try {
            List<SaleAttrSortConfig> saleAttrSortConfigs = JSON.parseArray(skuAttrConfigJson, SaleAttrSortConfig.class);
            if (CollectionUtils.isEmpty(saleAttrSortConfigs)) {
                return defaultSort;
            }
            return saleAttrSortConfigs.stream()
                    .filter(config -> Objects.equals(config.getServiceTypeId(), serviceTypeId)
                            && Objects.equals(config.getServiceType(), serviceType))
                    .map(SaleAttrSortConfig::getSaleAttrSort)
                    .findFirst().orElse(defaultSort);
        } catch (Exception e) {
            log.error("[SkuModuleProcessor] JSON.parseArray err", e);
            return defaultSort;
        }
    }

    /**
     * 业务校验不通过，不进行后续处理
     * @return true 业务校验通过，执行后续处理；false 不执行后续处理
     */
    private boolean doBusinessCheckProcess(DealCtx ctx) {
        // 留资型团单（目前仅月子中心房型）且为（超值特惠团单或小程序），则不展示多sku模块
        if (DealUtils.isLeadsDeal(ctx) && (ctx.isHitSpecialValueDeal() || !(ctx.getEnvCtx().judgeMainApp()))) {
            return false;
        }
        return true;
    }

    @Data
    public static class SaleAttrSortConfig implements Serializable {

        private Long serviceTypeId;
        private String serviceType;

        /**
         * 销售属性排序，如果为空，则默认按查询到的顺序排
         */
        private List<String> saleAttrSort;
    }
}