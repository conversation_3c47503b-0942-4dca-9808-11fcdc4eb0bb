package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.gm.marketing.member.card.api.dto.membercard.UserMemberCardInfoDTO;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Maps;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.PromoProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PromoMergeWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.pay.promo.reception.service.dto.DefaultSelectedPromos;
import com.dianping.pay.promo.reception.service.dto.MergePromoProductInfo;
import com.dianping.pay.promo.reception.service.dto.request.PromoMergeRequest;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

/**
 * Created by zhaopanwen on 2019/6/24.
 */
public class PromoMergeProcessor extends AbsDealProcessor {

    @Autowired
    private PromoMergeWrapper promoMergeWrapper;
    @Autowired
    private DiscountCardProcessor discountCardProcessor;
    @Autowired
    private PromoProcessor promoProcessor;

    @Override
    public boolean isEnable(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.PromoMergeProcessor.isEnable(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        return ctx.getEnvCtx().isMainApp();
    }

    @Override
    public void prepare(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.PromoMergeProcessor.prepare(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        //promoMergeWrapper里的输入参数需要依赖这2个的返回值
        discountCardProcessor.process(ctx);
        promoProcessor.process(ctx);

        PromoMergeRequest request = new PromoMergeRequest();
        Map<String, UserMemberCardInfoDTO> discountMap = Maps.newHashMap();
        Map<String, PromoDisplayDTO> reduceMap = Maps.newHashMap();
        MergePromoProductInfo mergePromoProductInfo = new MergePromoProductInfo();
        DefaultSelectedPromos defaultSelectedPromos = new DefaultSelectedPromos();
        if (ctx.getUserMemberCard() != null) {
            request.setDiscountCards(discountMap);
            request.setDefaultSelectedPromos(defaultSelectedPromos);
        }
        request.setReduces(reduceMap);
        request.setProductInfo(mergePromoProductInfo);
        //key和DefaultSelectedPromos一样,为当前日期+随机数 保证唯一
        discountMap.put(genTimeStamp(), ctx.getUserMemberCard());

        //需要list转换map
        transfer(reduceMap, ctx.getDiscountPromoList());
        buildProductInfo(mergePromoProductInfo, ctx);
        defaultSelectedPromos.setSelectedDiscountCard(discountMap);

        Future promoMergeFuture = promoMergeWrapper.prePayPromoMergeInfo(request);
        ctx.getFutureCtx().setPromoMergeFuture(promoMergeFuture);
    }

    @Override
    public void process(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.PromoMergeProcessor.process(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        ctx.setPromoMerge(promoMergeWrapper.loadPromoMergeInfo(ctx.getFutureCtx().getPromoMergeFuture()));
    }

    private void transfer(Map<String, PromoDisplayDTO> reduceMap, List<PromoDisplayDTO> promoList) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.PromoMergeProcessor.transfer(java.util.Map,java.util.List)");
        if (CollectionUtils.isNotEmpty(promoList)) {
            for (PromoDisplayDTO promo : promoList) {
                reduceMap.put(String.valueOf(promo.getRuleId()), promo);
            }
        }
    }

    private void buildProductInfo(MergePromoProductInfo product, DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.PromoMergeProcessor.buildProductInfo(MergePromoProductInfo,DealCtx)");
        product.setProductId(ctx.isMt() ? ctx.getMtId() : ctx.getDpId());
        product.setLongShopId(ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId());
        product.setPrice(ctx.getDealGroupBase().getDealGroupPrice());
        product.setQuantity(1);
    }

    private String genTimeStamp() {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.PromoMergeProcessor.genTimeStamp()");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmsSSSS");
        //时间+四位随机数[1000,10000)
        return sdf.format(new Date()) + (Math.random() * 9000 + 1000);
    }
}
