package com.dianping.mobile.mapi.dztgdetail.biz;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealClientWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.GroupDealWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiClientWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Cons;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealFields;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.MtDealModel;
import com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam;
import com.dianping.mobile.mapi.dztgdetail.util.ListUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mobile.sinai.base.common.PoiFields;
import com.meituan.service.mobile.message.group.deal.PoiidListRequestMsg;
import com.meituan.service.mobile.message.group.deal.PoiidListResponseMsg;
import com.meituan.service.mobile.prometheus.fields.DealModelFields;
import com.meituan.service.mobile.prometheus.model.DealModel;
import com.meituan.service.mobile.sinai.client.model.PoiModelL;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * Created by guozhengyao on 16/4/1.
 */
@Component
@Slf4j
public class DealBiz {
    @Resource
    private DealFieldTransferBiz dealFieldTransferBiz;
    @Resource
    private DealFieldChangerBiz dealFieldChangerBiz;
    @Resource
    private DealClientWrapper dealClientWrapper;
    @Resource
    private GroupDealWrapper groupDealWrapper;
    @Resource
    private PoiClientWrapper poiClientWrapper;

    private static final int THRESHOLD = 20;
    private static final Integer MAX_BATCH_SIZE = 100;
    private static final List<String> POIFIELDS4DIDS = Arrays.asList(PoiFields.poiid, PoiFields.lat, PoiFields.lng);

    public List<DealModel> getDealListByDids(List<Integer> dealIds, List<String> fields) {
        if (CollectionUtils.isEmpty(dealIds)) {
            return Collections.emptyList();
        }
        // 根据did获取deal详细信息
        List<String> dealFields = getDealFields(fields);
        List<DealModel> result = new ArrayList<>();
        // 每次最多取30个
        int didsCount = dealIds.size();
        int countPerRequest = 30;
        for (int start = 0; start < didsCount; start += countPerRequest) {
            int end = Math.min(start + countPerRequest, didsCount);
            List<Integer> subDids = dealIds.subList(start, end);
            List<DealModel> dealList = null;
            try {
                dealList = dealClientWrapper.listDealByIds(subDids, dealFields);
            } catch (Exception e) {
                log.error("dealThriftClient.listDealByIds error", e);
            }
            if (CollectionUtils.isNotEmpty(dealList)) {
                result.addAll(dealList);
            }
        }
        return result;
    }

    public List<MtDealModel> getDealJsonByModel(List<DealModel> dealList, List<String> fields, MtCommonParam mtCommonParam) throws Exception {
        int cityId = mtCommonParam.getCityId();
        // fields增多，兼容老版本app
        Set<String> allFields = new HashSet(getDealFields(fields));
        for (String f : fields) {
            allFields.add(f);
        }
        List<MtDealModel> mtDealModels = Lists.newArrayList();
        List<String> poiFields = getPoiFields(fields);
        if (CollectionUtils.isEmpty(poiFields)) {
            for (DealModel dealModel : dealList) {
                MtDealModel mtDealModel = dealFieldTransferBiz.transferMsg2Json(dealModel, allFields, mtCommonParam);
                mtDealModels.add(mtDealModel);
            }
        } else {
            List<Integer> dealIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(dealList)) {
                for (DealModel deal : dealList) {
                    dealIds.add(deal.getDid());
                }
            }
            if (CollectionUtils.isNotEmpty(dealIds)) {
                Map<Integer, MtDealModel> dealJsonMap = new HashMap<>();
                Map<Integer, List<PoiModelL>> poiModelMap = getPoiModelByDids(dealIds, cityId);
                for (DealModel dealModel : dealList) {
                    int did = dealModel.getDid();
                    List<PoiModelL> poiModelList = poiModelMap.get(did);
                    dealFieldChangerBiz.changeDeal(dealModel, cityId, fields, mtCommonParam);
                    MtDealModel mtDealModel = dealFieldTransferBiz.transferMsg2JsonObject(dealModel, allFields, poiModelList, mtCommonParam);
                    dealJsonMap.put(did, mtDealModel);
                }
                // 这里需要对deal列表做重新排序
                mtDealModels = sortByDidsOrder(dealJsonMap, dealIds);
            }
        }
        return mtDealModels;
    }

    /**
     * 返回did与相应poi信息列表的对应关系.
     */
    public Map<Integer, List<PoiModelL>> getPoiModelByDids(List<Integer> dealIds, int cityId) throws Exception {
        if (CollectionUtils.isEmpty(dealIds)) {
            return Maps.newHashMap();
        }
        Map<Integer, List<PoiModelL>> poiModelMap = Maps.newHashMap();
        PoiidListRequestMsg poiIdListReq = new PoiidListRequestMsg();
        poiIdListReq.setCityId(Math.max(cityId, 0));
        poiIdListReq.setDids(dealIds);
        poiIdListReq.setThreshold(THRESHOLD);
        Map<Integer, List<Long>> did2poiIdMap = groupDealWrapper.listPoiIdByDIds(poiIdListReq);
        if (MapUtils.isEmpty(did2poiIdMap)) {
            return Maps.newHashMap();
        }
        // 从poi-thrift获取poi信息
        List<Long> allPoiIds = new ArrayList<>();
        for (List<Long> lst : did2poiIdMap.values()) {
            allPoiIds.addAll(lst);
        }
        Map<Long, PoiModelL> allPoiModel = getPoiModelMap(allPoiIds);
        for (Integer did : did2poiIdMap.keySet()) {
            List<Long> poiIds = did2poiIdMap.get(did);
            List<PoiModelL> poiMsgList = poiModelMap.get(did);
            if (CollectionUtils.isEmpty(poiMsgList)) {
                poiMsgList = new ArrayList<>();
            }
            for (Long poiId : poiIds) {
                if (allPoiModel.get(poiId) != null) {
                    poiMsgList.add(allPoiModel.get(poiId));
                }
            }
            poiModelMap.put(did, poiMsgList);
        }
        return poiModelMap;
    }

    /**
     * 分段请求poi-thrift，防止其超时，同时减少访问次数.
     */
    private Map<Long, PoiModelL> getPoiModelMap(List<Long> poiIds) {
        Map<Long, PoiModelL> poiModelMap = new HashMap<>();
        if (CollectionUtils.isEmpty(poiIds)) {
            return poiModelMap;
        }
        List<PoiModelL> poiMsgList = null;
        try {
            if (poiIds.size() <= MAX_BATCH_SIZE) {
                poiMsgList = poiClientWrapper.listPoiL(poiIds, POIFIELDS4DIDS);
            } else {
                List<PoiModelL> poiModels = new LinkedList<>();
                int startPos = 0;
                while (startPos < poiIds.size()) {
                    int endPos = startPos + MAX_BATCH_SIZE;
                    if (endPos > poiIds.size()) {
                        endPos = poiIds.size();
                    }
                    List<Long> tmpPoiIds = poiIds.subList(startPos, endPos);
                    poiModels.addAll(poiClientWrapper.listPoiL(tmpPoiIds, POIFIELDS4DIDS));
                    startPos = endPos;
                }
                poiMsgList = poiModels;
            }
        } catch (Exception e) {
            log.error("getPoiModelMap error!", e);
        }
        if (CollectionUtils.isNotEmpty(poiMsgList)) {
            for (PoiModelL poiModel : poiMsgList) {
                poiModelMap.put(poiModel.getId(), poiModel);
            }
        }
        return poiModelMap;
    }

    /**
     * 返回属性信息中poi属性列表.
     */
    private List<String> getPoiFields(List<String> fields) {
        // 取交集
        return ListUtils.intersection(new HashSet<>(fields), DealFields.POI_FIELDS_SET);
    }

    private List<MtDealModel> sortByDidsOrder(Map<Integer, MtDealModel> dealJsonMap, List<Integer> dids) {
        if (CollectionUtils.isEmpty(dealJsonMap.keySet())
                || CollectionUtils.isEmpty(dids)) {
            return Lists.newArrayList();
        }
        List<MtDealModel> deals = Lists.newArrayList();
        for (int did : dids) {
            MtDealModel one = dealJsonMap.get(did);
            if (null != one) {
                deals.add(one);
            }
        }
        return deals;
    }

    /**
     * 返回去除poi相关属性信息的fields列表.
     */
    private List<String> getDealFields(List<String> rowFields) {
        List<String> dealFields = new ArrayList<>();
        dealFields.addAll(CollectionUtils.isEmpty(rowFields) ? DealFields.LIST_FIELDS : rowFields);
        // 去除poi相关fields
        Iterator<String> iterator = dealFields.iterator();
        while (iterator.hasNext()) {
            String field = iterator.next();
            for (String poiField : DealFields.POI_FIELDS) {
                if (field.equals(poiField)) {
                    iterator.remove();
                }
            }
        }
        //add chancel
        dealFields.add(DealFields.DEAL_FILED_CHANNEL);
        Set<String> fields = new HashSet<>(dealFields);
        //** 处理字段之间的依赖 */
        // 总是加上id
        fields.add(DealFields.DEAL_FIELD_ID);
        // 总是加上price（因为android取详情页不会传price字段）
        fields.add(DealFields.DEAL_FIELD_PRICE);
        fields.add(DealFields.DEAL_FIELD_PROPERTIES);
        fields.add(DealFields.DEAL_FIELD_CATE);
        fields.add(DealFields.DEAL_FIELD_CTYPE);
        fields.add(DealFields.DEAL_FIELD_DIGESTION);

        // 若有range，加上ctype和cityIds
        if (fields.contains(DealFields.DEAL_FIELD_RANGE)) {
            fields.add(DealFields.DEAL_FIELD_CTYPE);
            fields.add(DealFields.DEAL_FIELD_CITYIDS);
        }

        // 若有voice，加上ctype、status
        if (fields.contains(DealFields.DEAL_FIELD_VOICE)) {
            fields.add(DealFields.DEAL_FIELD_CTYPE);
            fields.add(DealFields.DEAL_FIELD_STATUS);
        }

        // 若有fakerefund，加上ctype和start
        if (fields.contains(DealFields.DEAL_FIELD_FAKEREFUND)) {
            fields.add(DealFields.DEAL_FIELD_CTYPE);
            fields.add(DealFields.DEAL_FIELD_START);
        }

        if (fields.contains(DealFields.DEAL_FIELD_OPTIONALATTRS)) {
            fields.add(DealFields.DEAL_FIELD_ATTRJSON);
        }

        if (fields.contains(DealFields.DEAL_FIELD_CATE)
                || fields.contains(DealFields.DEAL_FIELD_SUBCATE)) {
            fields.add(DealFields.DEAL_FIELD_DT);
            fields.add(DealFields.DEAL_FIELD_ATTRJSON);
            fields.add(DealFields.DEAL_FIELD_NOBOOKING);
            fields.add(DealFields.DEAL_FIELD_FESTCANUSE);
            fields.add(DealFields.DEAL_FIELD_MEALCOUNT);
        }
        if (fields.contains(DealFields.DEAL_FIELD_VOICE)) {
            fields.add(DealFields.DEAL_FIELD_PRICECALENDAR);
        }
        if (fields.contains(DealFields.DEAL_FIELD_CAMPAIGNS)) {
            fields.add(DealFields.DEAL_FIELD_CITYIDS);
        }
        //** 处理api字段与dealModel字段的转换 */
        if (fields.contains(DealFields.DEAL_FIELD_NEWRATING)) {
            fields.add(DealModelFields.RATINGMODEL);
        }
        if (fields.contains(DealFields.DEAL_FIELD_RATING)) {
            fields.add(DealModelFields.RATING);
        }
        if (fields.contains(DealFields.DEAL_FIELD_RATE_COUNT)) {
            fields.add(DealModelFields.RATECOUNT);
        }
        if (fields.contains(DealFields.DEAL_FIELD_SATISFACTION)) {
            fields.add(DealModelFields.SATISFACTION);
        }
        if (fields.contains(DealFields.DEAL_FIELD_ATTRJSON)) {
            fields.add(DealModelFields.ATTRS);
        }
        if (fields.contains(DealFields.DEAL_FIELD_KTV)) {
            fields.add(DealModelFields.ATTRS + Cons.STR_SEPARATOR_DOT + 678);
        }
        if (fields.contains(DealFields.DEAL_FIELD_BOOKINGPHONE)) {
            fields.add(DealModelFields.ATTRS + Cons.STR_SEPARATOR_DOT + 552);
        }
        if (fields.contains(DealFields.DEAL_FIELD_REFUND)) {
            fields.add(DealModelFields.REFUND);
        }
        if (fields.contains(DealFields.DEAL_FIELD_SEVENREFUND)) {
            fields.add(DealModelFields.SEVENREFUND);
        }
        if (fields.contains(DealFields.DEAL_FIELD_CATE)
                || fields.contains(DealFields.DEAL_FIELD_SUBCATE)) {
            fields.add(DealModelFields.CATES);
        }
        //the following two if are copied from groupapi
        if (fields.contains(DealFields.DEAL_FIELD_TERMS)) {
            fields.add(DealModelFields.ATTRS);
            fields.add(DealModelFields.MENU);
        }
        //酒店房型名是从属性里面取拼接获取的，必须依赖一些属性字段
        if (fields.contains(DealFields.DEAL_FIELD_HOTELROOMNAME)) {
            fields.add(DealModelFields.ATTRS + Cons.STR_SEPARATOR_DOT + 35);
            fields.add(DealModelFields.ATTRS + Cons.STR_SEPARATOR_DOT + 38);
            fields.add(DealModelFields.ATTRS + Cons.STR_SEPARATOR_DOT + 9994);
        }
        return new ArrayList<>(fields);
    }
}
