package com.dianping.mobile.mapi.dztgdetail.entity;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Author: zheng<PERSON><EMAIL>
 * @Date: 2024/1/26
 */
@Data
public class ShowReservationGrayConfig {
    /**
     * 走预约浮窗逻辑判断的类目ID列表
     */
    private List<Integer> categoryIds;

    /**
     * 白名单模式，0-在白名单内走实验，1-直接走实验
     */
    private int whiteListMode;

    /**
     * 商户白名单
     */
    private Map<String, List<Long>> category2ShopIds;

    /**
     * 品类实验
     */
    private Map<String, String> category2ExpId;
}
