package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * @author: zhangyuan103
 * @create: 2025-03-20
 * @description: [家政]强预订主路径交易流程定制化逻辑，
 * 若命中 商店白名单 + 非营销场 + 团单类别 + 团购有预约属性，则对下单按钮的跳链修改，跳转到融合提单页面
 */
@Slf4j
@Service
public class PreOrderParamBuilderService {

    private static final String ADD_PARAM = "&preordershowtype=%s&orderdetailtype=%s";
    // 1是强预订提单页
    private static final String PRE_ORDER_PAGE = "1";
    // 1-新版详情页，2-强预订新详情页
    private static final String PRE_ORDER_DETAIL_PAGE = "2";

    public void buildPreOrderParam(DealCtx ctx, DealGroupPBO result) {
        // [家政]强预订主路径交易流程定制化逻辑，如果符合条件，则对下单按钮的跳链修改，跳转到融合提单页面
        if (ctx.isPreOderDeal()) {
            String integratedParam = String.format(ADD_PARAM, PRE_ORDER_PAGE, PRE_ORDER_DETAIL_PAGE);
            DealUtils.assembleUrl(result, integratedParam);
            // 原逻辑是前端通过"hasReserveEntrance=true" 发起预约浮层，再跳转到提单页。
            // 融合订提单页不需要前端发起预约浮层，融合提单页可以控制显示预约浮层，所以这里置为false，标识不需要前端控制
            result.setHasReserveEntrance(false);
        }
    }
}
