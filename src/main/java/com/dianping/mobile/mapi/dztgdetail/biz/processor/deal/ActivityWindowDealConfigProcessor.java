package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.lion.client.util.CollectionUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealActivityWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.dianping.product.shelf.common.dto.Response;
import com.dianping.product.shelf.common.dto.activity.ActivityDetailDTO;
import com.dianping.product.shelf.common.dto.subjectConfig.SubjectConfigDTO;
import com.dianping.product.shelf.common.dto.subjectConfig.SubjectConfigDTOList;
import com.dianping.product.shelf.common.dto.subjectConfig.SubjectConfigQueryDTO;
import com.dianping.product.shelf.common.dto.subjectConfig.SubjectDTO;
import com.dianping.product.shelf.common.enums.ConfigSubjectTypeEnum;
import com.dianping.product.shelf.common.enums.SubjectUnitKeyEnum;
import com.dianping.product.shelf.common.request.subjectManage.SubjectConfigQueryRequest;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2024-11-11-11:01
 * * 活动橱窗团单属性处理
 */
public class ActivityWindowDealConfigProcessor extends AbsDealProcessor {
    @Resource
    private DealActivityWrapper dealActivityWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        // 目前同留资型团单限制，后续有扩类目需注意区分
        return DealUtils.isLeadsDeal(ctx) || DealUtils.isWeddingLeadsDeal(ctx);
    }

    @Override
    public void prepare(DealCtx ctx) {
        ActivityDetailDTO activityDetailDTO = dealActivityWrapper.getActivityDealDetail(ctx);
        if (activityDetailDTO == null) {
            return;
        }
        SubjectConfigQueryRequest request = new SubjectConfigQueryRequest();
        SubjectConfigQueryDTO queryDTO = new SubjectConfigQueryDTO();
        SubjectDTO subjectDTO = new SubjectDTO();

        subjectDTO.setSubjectType(ConfigSubjectTypeEnum.Shop_DealGroup_Activity.getSubjectType());
        Map<String, String> subjectUnitMap = Maps.newHashMap();
        subjectUnitMap.put(SubjectUnitKeyEnum.dpShopId.getSubjectUnitKey(), String.valueOf(ctx.getDpLongShopId()));
        subjectUnitMap.put(SubjectUnitKeyEnum.dpDealGroupId.getSubjectUnitKey(), String.valueOf(ctx.getDpId()));
        subjectUnitMap.put(SubjectUnitKeyEnum.activityId.getSubjectUnitKey(), String.valueOf(activityDetailDTO.getActivityId()));
        subjectDTO.setSubjectUnitMap(subjectUnitMap);
        queryDTO.setSubjectDTO(subjectDTO);

        queryDTO.setConfigNameList(Lists.newArrayList("activityProductSecretHandPrice", "activityProductStock"));

        request.setSubjectConfigQueryDTOList(Lists.newArrayList(queryDTO));
        ctx.getFutureCtx().setActivityDealConfigFuture(dealActivityWrapper.preQueryDealActivityConfig(request));
    }

    @Override
    public void process(DealCtx ctx) {
        if (Objects.isNull(ctx.getFutureCtx())) {
            return;
        }
        Response<SubjectConfigDTOList> response = dealActivityWrapper.getFutureResult(ctx.getFutureCtx().getActivityDealConfigFuture());
        if (response == null || !response.isSuccess() || response.getContent() == null) {
            return;
        }
        SubjectConfigDTOList subjectConfigDTOList = response.getContent();
        if (CollectionUtils.isEmpty(subjectConfigDTOList.getSubjectConfigDTOList())) {
            return;
        }
        SubjectConfigDTO configDTO = subjectConfigDTOList.getSubjectConfigDTOList().get(0);
        if (configDTO == null || CollectionUtils.isEmpty(configDTO.getConfigDTOList())) {
            return;
        }
        ctx.setActivityWindowDealConfigs(configDTO.getConfigDTOList());
    }

}
