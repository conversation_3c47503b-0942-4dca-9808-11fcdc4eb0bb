package com.dianping.mobile.mapi.dztgdetail.datatype.resp.shop;

import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.EncryptedField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: zhangyuan103
 * @Date: 2024/8/13
 */
@Data
@TypeDoc(description = "适用门店信息")
public class DealGroupShop implements Serializable {

    /**
     * 距离文案，距离描述。首页和商户不在同一个城市时，返回城市信息
     */
    @MobileField
    private String distanceDesc;

    /**
     * 地址和定位文案
     */
    @MobileField
    private String distanceAddress;

    /**
     * 标签列表
     * 已废弃，请使用zdcTags
     */
    @Deprecated
    @MobileField
    private List<ShopIcon> icons;

    /**
     * 商户详情页
     */
    @MobileField
    private String shopDetailUrl;

    /**
     * 距离
     */
    @MobileField
    private String distance;

    /**
     * 商户电话数组
     */
    @MobileField
    private List<String> phoneNos;

    /**
     * 门店地址
     */
    @MobileField
    private String address;

    /**
     * 门店名称
     */
    @MobileField
    private String shopName;

    /**
     * 门店id
     */
    @EncryptedField(targetFieldName = "shopIdEncrypt")
    @MobileField
    private long shopId;

    /**
     * shopid密文
     */
    @MobileField
    private String shopIdEncrypt;

    /**
     * 当前营业状态
     * @see BusinessStateEnum
     */
    @MobileField
    private int businessState;

    /**
     * 营业时间
     */
    @MobileField
    private String businessHour;

    /**
     * 门店星级，数值在[0,50]，如果传入-1代表无星级信息
     */
    @MobileField
    private int shopPower;

    /**
     * zdc商户标签
     */
    @MobileField
    private List<ChannelTagVO> zdcTags;

    /**
     * 是否下单门店
     */
    @MobileField
    private boolean isOrderShop;

    /**
     * 地图跳转链接
     */
    @MobileField
    private String mapUrl;
    /**
     * 预订相关图标
     */
    @MobileField
    private BookIcon bookIcon;
    /**
     * 是否为离用户最近的门店
     */
    @MobileField
    private boolean isNearestShop;
    /**
     * 最早可订的时间
     */
    @MobileField
    private String firstOrderTime;
    /**
     * 点评shopId
     */
    @MobileField
    private long dpShopId;
    /**
     * 美团shopId
     */
    @MobileField
    private long mtShopId;
}