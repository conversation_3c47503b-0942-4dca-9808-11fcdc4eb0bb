package com.dianping.mobile.mapi.dztgdetail.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.URLEncoder;

@Slf4j
public class UrlUtils {

    private static final String MT_APP_PREFIX = "imeituan://";

    private static final String DP_APP_PREFIX = "dianping://";

    /**
     * 将h5链接包装为点评webview url
     *
     * @param originUrl
     * @return
     */
    public static String encodeDpWebViewUrl(String originUrl) {
        if (StringUtils.isEmpty(originUrl)) {
            log.error("encodeDpWebViewUrl url empty, originUrl is {}", originUrl);
            return "";
        }
        String result = originUrl;
        if (originUrl.startsWith(DP_APP_PREFIX)) {
            return originUrl;
        }
        try {
            result = "dianping://web?url=" + URLEncoder.encode(originUrl, "utf-8");
        } catch (Exception e) {
            log.error("encodeDpWebViewUrl encode error, originUrl is {}", originUrl, e);
        }
        return result;
    }

    /**
     * 将h5链接包装为美团webview url
     *
     * @param originUrl
     * @return
     */
    public static String encodeMtWebViewUrl(String originUrl) {
        if (StringUtils.isEmpty(originUrl)) {
            log.error("encodeMtWebViewUrl url empty, originUrl is {}", originUrl);
            return "";
        }
        String result = originUrl;
        if (originUrl.startsWith(MT_APP_PREFIX)) {
            return originUrl;
        }
        try {
            result = "imeituan://www.meituan.com/web?url=" + URLEncoder.encode(originUrl, "utf-8");
        } catch (Exception e) {
            log.error("encodeMtWebViewUrl encode error, originUrl is {}", originUrl, e);
        }
        return result;
    }

}
