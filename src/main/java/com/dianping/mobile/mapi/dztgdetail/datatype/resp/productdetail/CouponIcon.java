package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * @desc 优惠券图标
 */
@TypeDoc(description = "优惠券图标")
@Data
@MobileDo(id = 0x7afd)
public class CouponIcon implements Serializable {
    @FieldDoc(description = "右下角边框圆角")
    @MobileField(key = 0xf0d8)
    private int borderBottomRightRadius;

    @FieldDoc(description = "左下角边框圆角")
    @MobileField(key = 0x56f5)
    private int borderBottomLeftRadius;

    @FieldDoc(description = "右上角边框圆角")
    @MobileField(key = 0xdb5a)
    private int borderTopRightRadius;

    @FieldDoc(description = "左上角边框圆角")
    @MobileField(key = 0x872f)
    private int borderTopLeftRadius;

    @FieldDoc(description = "背景渐变结束颜色")
    @MobileField(key = 0xec70)
    private String backgroundEndColor;

    @FieldDoc(description = "背景渐变开始颜色")
    @MobileField(key = 0x6d65)
    private String backgroundStartColor;

    @FieldDoc(description = "icon文本颜色")
    @MobileField(key = 0xeead)
    private String textColor;

    @FieldDoc(description = "icon文本")
    @MobileField(key = 0x451b)
    private String text;
}
