package com.dianping.mobile.mapi.dztgdetail.faulttolerance.deal.executor;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.deal.publishcategory.enums.ChannelGroupEnum;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.base.datatypes.StatusCode;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.biz.CreateOrderPageUrlBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.DealStyleStatisticService;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.LionUtils;
import com.dianping.mobile.mapi.dztgdetail.common.DpVersion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.CatEvents;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Cons;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.common.enums.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.Response;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ShopPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.PromoActivityInfoVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule;
import com.dianping.mobile.mapi.dztgdetail.entity.BrowserContent;
import com.dianping.mobile.mapi.dztgdetail.entity.BrowserEntity;
import com.dianping.mobile.mapi.dztgdetail.exception.RemoveAddShoppingCartStatusException;
import com.dianping.mobile.mapi.dztgdetail.facade.DealQueryFacade;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.facade.DealQueryParallFacade;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.grey.DefaultGrayConfigContext;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.grey.DefaultGroupGrayConfig;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.req.DealBaseContextRequest;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.utils.FaultToleranceUtils;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.utils.OrderUrlMonitor;
import com.dianping.mobile.mapi.dztgdetail.helper.SwitchHelper;
import com.dianping.mobile.mapi.dztgdetail.util.*;
import com.dianping.mobile.mapi.dztgdetail.util.dinner.DinnerDealUtils;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.onelimiter.LimitResult;
import com.dianping.rhino.onelimiter.OneLimiter;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mafka.client.bean.MafkaProducer;
import com.meituan.mafka.client.producer.AsyncProducerResult;
import com.meituan.mafka.client.producer.FutureCallback;
import com.meituan.mtrace.Tracer;
import com.sankuai.athena.stability.faulttolerance.core.Executor;
import com.sankuai.athena.stability.faulttolerance.tracer.ExceptionTracer;
import com.sankuai.meituan.waimai.thrift.tools.cache.RegionSetConfigCache;
import com.sankuai.meituan.waimai.thrift.tools.enums.SetProxyTypeEnum;
import com.sankuai.meituan.waimai.thrift.tools.utils.MTraceRouterInfoUtil;
import com.sankuai.nibpt.unionlogger.UnionLoggerContext;
import com.sankuai.nibscp.common.flow.identify.exception.FlowIdentifyControlException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023-11-01
 * @desc 团详主接口执行逻辑
 */
@Component
@Slf4j
public class DzDealBaseExecutor implements Executor<DealBaseContextRequest, CommonMobileResponse> {

    @Autowired
    private DealQueryFacade dealQueryFacade;

    @Autowired
    private DealQueryParallFacade dealQueryParallFacade;

    @Resource
    private CreateOrderPageUrlBiz createOrderPageUrlBiz;

    @Resource
    private DealStyleStatisticService dealStyleStatisticService;

    @Autowired
    @Qualifier("ItemBrowseProducer")
    private MafkaProducer itemBrowseProducer;

    private final static List<String> ACCESS_BENEFIT_LIST = Lists.newArrayList(
            "pass_param",
            "eventpromochannel",
            "lyyuserid",
            "promotionchannel"
    );

    @Override
    public CommonMobileResponse execute(DealBaseContextRequest context) {
        try {
            ExceptionTracer.start();
            CommonMobileResponse response = getExecuteResult(context.getRequest(), context.getIMobileContext(), context.getEnvCtx(), false);
            StatusCode statusCode = response.getStatusCode();
            // 根据返回的状态码和响应数据，触发容错处理
            FaultToleranceUtils.triggerFaultTolerantByUnExpectResult("dzDealBase", statusCode.getCode(), response.getData());
            return response;
        } finally {
            ExceptionTracer.end();
        }
    }


    private static final String RHINO_CAT_KEY = "DealDetailRhinoLimit";
    private static final OneLimiter ONE_LIMITER = Rhino.newOneLimiter();
    private static final String ENTRANCE = "dzdealbase.rhino";

    /**
     * 获取团购详情页大部分数据
     * @param request 请求对象
     * @param iMobileContext 移动端上下文对象
     * @param envCtx  环境上下文对象
     * @return  返回团购详情页大部分数据
     */
    public CommonMobileResponse getExecuteResult(DealBaseReq request, IMobileContext iMobileContext, EnvCtx envCtx, boolean localCall) {
        UnionLoggerContext.logUserId(envCtx.isMt() ? String.valueOf(envCtx.getMtUserId()) : String.valueOf(envCtx.getDpUserId()));//已确认判断平台后再使用
        try {
            putTraceContext(envCtx, request.getRegionid());
            if (!localCall){
                LogUtils.info("[DzDealBaseExecutor] getExecuteResult: request={}, iMobileContext={}, envCtx={}",
                        JsonCodec.encode(request), iMobileContext, envCtx);
            }
            // 登录校验
            Map<String, Boolean> loginSwitch = Lion.getMap(LionConstants.APP_KEY,
                    LionConstants.DETAIL_LOGIN_SWITCH, Boolean.class, Collections.emptyMap());
            boolean canNotAccess = enableLogin(envCtx, request);
            if(canNotAccess) {
                Cat.logMetricForCount(CatEvents.DEALBASE_DETAIL_APP_UNLOGIN);
                DealGroupPBO result = new DealGroupPBO();
                result.setNeedLogin(true);
                return new CommonMobileResponse(result);
            }
            // id，pageSource 限流能力
            if (LionUtils.rhinoLimitSwitch()) {
                Cat.logEvent(RHINO_CAT_KEY, ENTRANCE + "-normal");
                Map<String, String> params = Maps.newHashMap();
                params.put("dealGroupId", request.getStringDealGroupId());
                params.put("pageSource", request.getPageSource());
                LimitResult run = ONE_LIMITER.run(ENTRANCE, params);
                if (run.isReject()) {
                    Cat.logEvent(RHINO_CAT_KEY, ENTRANCE + "-reject");
                    return Resps.rhinoLimitError();
                }
            }
            Map<String, String> logTags = Maps.newHashMap();
            logTags.put("platform", envCtx.getDztgClientTypeEnum().getDesc());
            logTags.put("pageSource", request.getPageSource());
            boolean notLogin = AntiCrawlerUtils.isNotLogin(loginSwitch, envCtx) ;
            LogUtils.info(logTags,
                    "[DzDealBaseExecutor] getExecuteResult: isLogin={}, dealGroupId={}, userId={}, unionId={}",
                    !notLogin, request.getDealgroupid(),
                    envCtx.isMt() ? envCtx.getMtUserId() : envCtx.getDpUserId(), envCtx.getUnionId());//已确认判断平台后再使用
            if (notLogin) {
                Cat.logMetricForCount(CatEvents.DEALBASE_DETAIL_UNLOGIN);
                DealGroupPBO result = new DealGroupPBO();
                result.setNeedLogin(true);
                return new CommonMobileResponse(result);
            }

            boolean allowAccess = allowAccess(envCtx);
            Response<DealGroupPBO> response;
            if (allowAccess) {
                Cat.logMetricForCount("DealQueryRcf_parallQuery");
                Transaction trx = Cat.newTransaction("DealQueryRcf", "parallQuery");
                response = dealQueryParallFacade.queryDealGroup(request, envCtx, iMobileContext.getHeaders());
                trx.complete();
            } else {
                Cat.logMetricForCount("DealQueryRcf_singleQuery");
                Transaction trx = Cat.newTransaction("DealQueryRcf", "singleQuery");
                response = dealQueryFacade.queryDealGroup(request, envCtx);
                trx.complete();
            }
            if (response.getCode() == Response.RespCode.RHINO_REJECT.getVal()) {
                LogUtils.warn(logTags, "[DzDealBaseExecutor] getExecuteResult: userId={} rhino reject",
                        envCtx.isMt() ? envCtx.getMtUserId() : envCtx.getDpUserId());//已确认判断平台后再使用
                return Resps.SYSTEM_BUSY;
            }

            DealGroupPBO result = response.getResult();
            if (result != null) {
                if (result.getModuleConfigsModule() != null && result.getModuleConfigsModule().isTort()) {
                    Cat.logMetricForCount(CatEvents.DEALBASE_FAIL);
                    return new CommonMobileResponse(result);
                }
                processLyyUserId(request.getLyyuserid(), result);
                processEventPromoChannel(request.getEventpromochannel(), result);
                processPassParam(request.getPass_param(),result);
                processTrafficFlag(request, result);
                // 从前端入参往提单跳链透传参数
                transparentAppendParamFromReq(result, request);

                // 发送点评浏览记录消息（迁移自mapi-tgdetail-web）
                if (!localCall){
                    productBrowserMQ(request, result, iMobileContext, envCtx);
                }

                Map<String, String> tags = Maps.newHashMap();
                int categoryId = result.getCategoryId();
                tags.put("category", String.valueOf(categoryId));
                tags.put("clientType", String.valueOf(envCtx.getClientType()));
                buildTags(tags, result);
                if (envCtx.getMpAppId() != null) {
                    tags.put("mpAppId", envCtx.getMpAppId());
                }
                if (request.getPageSource() != null) {
                    tags.put("pageSource", request.getPageSource());
                }

                createOrderPageUrlBiz.replaceUrlByTradeUrlService(envCtx, result, request);

                // 加入购物车不支持的场景
                removeAddShoppingCartStatus(request, result);

                processMallDeal(request.getPageSource(), result);
                processPromoActivityUrl(result);

                Cat.logMetricForCount(CatEvents.DEALBASE_SUC, tags);
                logMetricOfSource(envCtx, request);

                if (Objects.nonNull(result) && Objects.nonNull(result.getModuleConfigsModule())) {
                    Cat.newCompletedTransactionWithDuration("DealBase.SceneCode", result.getModuleConfigsModule().getKey(), System.currentTimeMillis() - envCtx.getStartTime());
                }
                // 反爬信息处理
                if (!localCall){
                    if (AntiCrawlerUtils.onlyDisplayWhitelistFields()) {
                        result = AntiCrawlerUtils.onlyShowSafeFields(result, iMobileContext);
                    } else {
                        AntiCrawlerUtils.hideKeyInfo(result, iMobileContext);
                    }
                }
                LogUtils.info("[DzDealBaseExecutor] getExecuteResult: buyBar={}, ", JsonCodec.encodeWithUTF8(result.getBuyBar()));

                catLogSpecialDeal(categoryId);
                dealStyleStatisticService.dealStyleStatistic(request, envCtx, result, LionConfigUtils.getDealStyleStatisticSwitch(), LionConfigUtils.getDealStyleStatisticLogSwitch());
                CatUtils.reportDealClientType("dzdealbase.bin", envCtx, categoryId, result.getMtId(), request.getPageSource());
                // 拦截到餐商品，避免信息外漏
                if (DinnerDealUtils.isDinnerDeal(categoryId, envCtx)) {
                    Cat.logMetricForCount(CatEvents.DEALBASE_PRODUCT_INTERCEPT);
                    log.info("到餐团单ID={}, 类目ID={}", result.getMtId(), categoryId);
                    return Resps.PRODUCT_ERROR;
                }
                // 上报提单URL
                OrderUrlMonitor.monitor(result);
                return new CommonMobileResponse(result);
            }
        } catch (Exception e) {
            UnionLoggerContext.clear();
            log.error("dzdealbase.bin error", e);
            if (e instanceof FlowIdentifyControlException) {
                Cat.logMetricForCount(CatEvents.DEALBASE_FAIL);
                return Resps.rhinoLimitError();
            }
        }
        Cat.logMetricForCount(CatEvents.DEALBASE_FAIL);
        return Resps.SYSTEM_ERROR;
    }

    private void catLogSpecialDeal(int categoryId) {
        try {
            boolean hit = LionConfigUtils.hitSpecialCategory(categoryId);
            if (hit) {
                Cat.logEvent("DzDealBaseReportCategory", String.valueOf(categoryId));
            }
        } catch (Exception e) {
            log.error("catLogSpecialDeal error", e);
        }
    }

    private void processTrafficFlag(DealBaseReq request, DealGroupPBO result) {
        if (StringUtils.isBlank(request.getPageSource())) {
            return;
        }
        Map<String, String> pageSource2OrderTrafficFlag = Lion.getMap(LionConstants.APP_KEY, LionConstants.PAGESOURCE_TO_ORDER_TRAFFICFLAG, String.class, Collections.emptyMap());
        String trafficFlag = pageSource2OrderTrafficFlag.get(request.getPageSource());
        if (StringUtils.isBlank(trafficFlag)) {
            return;
        }
        assembleUrl(result, String.format("&trafficflag=%s", trafficFlag));
    }

    private void putTraceContext(EnvCtx envCtx, String regionId) {
        if (Objects.isNull(envCtx) || !envCtx.isMt()) {
            return;
        }
        try {
            long mtUserId = envCtx.getMtUserId();//已确认判断平台后再使用
            //客户端缓存中解析setName
            String setName = RegionSetConfigCache.getCellNameByRegionId(regionId);
            //调用公共方法，把路由信息放置到mtrace中
            MTraceRouterInfoUtil.setTraceRoutingInfo4Api(SetProxyTypeEnum.BY_USER_ID, mtUserId, regionId, setName);
        } catch (Exception e) {
            log.error("putTraceContext err", e);
        }
    }
    
    private void buildTags(Map<String, String> tags, DealGroupPBO result) {
        try {
            tags.put("category4type", result.getCategoryId() + " " + result.getServiceType());
            DealBuyBar dealBuyBar = result.getBuyBar();
            if(dealBuyBar != null) {
                tags.put("BuyBar", "展示");
                tags.put("BuyType", String.valueOf(dealBuyBar.getBuyType()));
                if(CollectionUtils.isNotEmpty(dealBuyBar.getBuyBtns())) {
                    tags.put("buyButton", "展示");
                    boolean btnEnable = dealBuyBar.getBuyBtns().get(0).isBtnEnable();
                    tags.put("btnEnable", String.valueOf(btnEnable));
                    tags.put("urlEmpty", String.valueOf(StringUtils.isBlank(dealBuyBar.getBuyBtns().get(0).getRedirectUrl())));
                } else {
                    tags.put("buyButton", "不展示");
                }
            } else {
                tags.put("BuyBar", "不展示");
            }
        } catch (Exception e) {
            Cat.logError(e);
        }
    }

    private DealGroupPBO getDifferDeal(DealBaseReq request, IMobileContext iMobileContext, EnvCtx envCtx) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.faulttolerance.deal.executor.DzDealBaseExecutor.getDifferDeal(DealBaseReq,IMobileContext,EnvCtx)");
        Response<DealGroupPBO> response = dealQueryFacade.queryDealGroup(request, envCtx);
        DealGroupPBO result = response.getResult();
        if (result != null) {
            processLyyUserId(request.getLyyuserid(), result);
            processEventPromoChannel(request.getEventpromochannel(), result);
            processPassParam(request.getPass_param(),result);

            // 发送点评浏览记录消息（迁移自mapi-tgdetail-web）
            productBrowserMQ(request, result, iMobileContext, envCtx);

            Map<String, String> tags = Maps.newHashMap();
            tags.put("category", String.valueOf(result.getCategoryId()));
            tags.put("clientType", String.valueOf(envCtx.getClientType()));
            if (envCtx.getMpAppId() != null) {
                tags.put("mpAppId", envCtx.getMpAppId());
            }
            if (request.getPageSource() != null) {
                tags.put("pageSource", request.getPageSource());
            }

            createOrderPageUrlBiz.replaceUrlByTradeUrlService(envCtx, result, request);

            // 加入购物车不支持的场景
            removeAddShoppingCartStatus(request, result);

            processMallDeal(request.getPageSource(), result);
            processPromoActivityUrl(result);

            logMetricOfSource(envCtx, request);

            if (Objects.nonNull(result) && Objects.nonNull(result.getModuleAbConfigs())) {
                Cat.newCompletedTransactionWithDuration("DealBase.SceneCode", result.getModuleConfigsModule().getKey(), System.currentTimeMillis() - envCtx.getStartTime());
            }
            return result;
        }
        return null;
    }

    private void processPromoActivityUrl(DealGroupPBO result) {
        try {
            if(result == null) {
                return;
            }
            if(result.getPromoDetailModule() == null || result.getPromoDetailModule().getPromoActivityList() == null) {
                return;
            }
            List<PromoActivityInfoVO> promoActivityInfoVOList = result.getPromoDetailModule().getPromoActivityList();
            for(PromoActivityInfoVO activityInfoVO : promoActivityInfoVOList) {
                if(activityInfoVO != null && "限时".equals(activityInfoVO.getBonusType())) {
                    DealBuyBtn idlePromoBtn = getIdlePromoBtn(result);
                    if(idlePromoBtn != null && idlePromoBtn.isBtnEnable()) {
                        activityInfoVO.setLeadUrl(idlePromoBtn.getRedirectUrl());
                    }
                } else if(activityInfoVO != null && "拼团".equals(activityInfoVO.getBonusType()) && StringUtils.isNotBlank(activityInfoVO.getLeadUrl())) {
                    DealBuyBtn pintuanBtn = getPintuanBtn(result);
                    if(pintuanBtn != null && pintuanBtn.isBtnEnable()) {
                        activityInfoVO.setLeadUrl(pintuanBtn.getRedirectUrl());
                    }
                }
            }
        } catch (Exception e) {
            log.error("processPromoActivityUrl error", e);
        }
    }

    private DealBuyBtn getPintuanBtn(DealGroupPBO result) {
        if(result == null || result.getBuyBar() == null || result.getBuyBar().getBuyBtns() == null) {
            return null;
        }
        List<DealBuyBtn> btns = result.getBuyBar().getBuyBtns();
        for(DealBuyBtn btn : btns) {
            if(btn != null && BuyBtnTypeEnum.PINTUAN.getCode() == btn.getDetailBuyType()) {
                return btn;
            }
        }
        return null;
    }

    private DealBuyBtn getIdlePromoBtn(DealGroupPBO result) {
        if(result == null || result.getBuyBar() == null || result.getBuyBar().getBuyBtns() == null) {
            return null;
        }
        List<DealBuyBtn> btns = result.getBuyBar().getBuyBtns();
        for(DealBuyBtn btn : btns) {
            if(btn != null && BuyBtnTypeEnum.IDLE_DEAL.getCode() == btn.getDetailBuyType()) {
                return btn;
            }
        }
        return null;
    }

    private void removeAddShoppingCartStatus(DealBaseReq request, DealGroupPBO result) {
        if(result.getBuyBar() == null || CollectionUtils.isEmpty(result.getBuyBar().getBuyBtns())) {
            return;
        }
        List<DealBuyBtn> buyButtons = result.getBuyBar().getBuyBtns();
        if(result.getBuyBar().getStyleType() != StyleTypeEnum.SHOPPING_CART.code){
            return;
        }

        int sizeBeforeSort = buyButtons.size();

        for (DealBuyBtn buyButton : buyButtons) {
            //URL中含渠道优惠，无加购按钮
            String redirectUrl = buyButton.getRedirectUrl();
            for (String accessBenefit : ACCESS_BENEFIT_LIST) {
                if(StringUtils.isNotBlank(redirectUrl) && redirectUrl.contains(accessBenefit)){
                    result.getBuyBar().setStyleType(StyleTypeEnum.NORMAL.code);
                    buyButton.setAddShoppingCartStatus(ShoppingCartStatusEnum.NONE.code);
                }
            }

            //预热单不是立即抢购时，无加购按钮
            if(StringUtils.isNotBlank(buyButton.getSaleStatus()) && !Objects.equals(buyButton.getSaleStatus(), SaleStatusEnum.SNAP_UP_NOW.saleStatusName)){
                result.getBuyBar().setStyleType(StyleTypeEnum.NORMAL.code);
                buyButton.setAddShoppingCartStatus(ShoppingCartStatusEnum.NONE.code);
            }
        }
        Cat.logEvent("removeAddShoppingCartStatus", "removeStyle");

        try {
            // 如果是从购物车样式又改回普通样式的，则把普通团购button置于第二位
            if(result.getBuyBar().getStyleType() == StyleTypeEnum.NORMAL.code) {
                DealBuyBtn normalDealBtn = removeNormalDealBuyBtn(buyButtons);
                Cat.logEvent("removeAddShoppingCartStatus", "removeNormalButton");
                if(normalDealBtn != null) {
                    if (buyButtons.isEmpty()) {
                        buyButtons.add(normalDealBtn);
                    } else if (buyButtons.size() > 1) {
                        buyButtons.add(1, normalDealBtn);
                    } else {
                        buyButtons.add(normalDealBtn);
                    }
                }
                Cat.logEvent("removeAddShoppingCartStatus", "sortButton");

                int sizeAfterSort = buyButtons.size();
                if(sizeBeforeSort != sizeAfterSort) {
                    throw new RemoveAddShoppingCartStatusException(
                            String.format("size is not equal, sizeBeforeSort:%s, sizeAfterSort:%s, dpId:%s, mtId:%s",
                                    sizeBeforeSort, sizeAfterSort, result.getDpId(), result.getMtId()));
                }
            }
        } catch (Exception e) {
            log.error("removeAddShoppingCartStatus error,", e);
        }

    }

    private DealBuyBtn removeNormalDealBuyBtn(List<DealBuyBtn> buyButtons) {
        if (buyButtons == null) {
            return null;
        }
        for (int i = 0; i < buyButtons.size(); i++) {
            DealBuyBtn btn = buyButtons.get(i);
            if (btn != null && btn.getDetailBuyType() == BuyBtnTypeEnum.NORMAL_DEAL.getCode()) {
                buyButtons.remove(i);
                return btn;
            }
        }
        return null;
    }

    private void logMetricOfSource(EnvCtx envCtx, DealBaseReq request) {
        try {
            Map<String, String> tags = Maps.newHashMap();
            tags.put("platform", String.valueOf(envCtx.getClientType()));
            if(envCtx.getDztgClientTypeEnum() != null) {
                tags.put("client", envCtx.getDztgClientTypeEnum().getDesc());
            }
            if (request.getPageSource() != null) {
                tags.put("pageSource", request.getPageSource());
            } else {
                tags.put("pageSource", "unknown");
            }
            Cat.logMetricForCount(CatEvents.DEALBASE_SOURCE, tags);
        } catch (Exception e) {
            log.error("logMetricOfSource error, ", e);
        }
    }

    private void processMallDeal(String pageSource, DealGroupPBO result) {
        if (Objects.isNull(result)) {
            return;
        }
        if (MallUtils.isFromMallFoodPoiVoucher(pageSource, result.getCategoryId())) {
            ModuleConfigsModule moduleConfigsModule = result.getModuleConfigsModule();
            if (Objects.nonNull(moduleConfigsModule)) {
                moduleConfigsModule.setKey(Cons.MALL_FOOD_POI_DEAL_DETAIL_KEY);
                moduleConfigsModule.setExtraInfo(Cons.MALL_FOOD_POI_DEAL_DETAIL_EXTRA_INFO);
            }
            if (StringUtils.isNotBlank(result.getTitle())) {
                result.setTitle(String.format("%s代金券", result.getTitle()));
            }
        } else if (MallUtils.isMallDealCategory(result.getCategoryId())) {
            result.setTitle(result.getDealName());
        }
    }

    private void processEventPromoChannel(String eventpromochannel, DealGroupPBO result) {
        if (StringUtils.isEmpty(eventpromochannel) || result == null) {
            return;
        }
        String urlStr = "&eventpromochannel=" + eventpromochannel;
        assembleUrl(result, urlStr);
    }

    private void processLyyUserId(String lyyuserid, DealGroupPBO result) {

        if (StringUtils.isEmpty(lyyuserid) || result == null) {
            return;
        }

        result.setShareAble(false);
        String str = null;

        try {
            str = "&lyyuserid=" + URLEncoder.encode(lyyuserid, StandardCharsets.UTF_8.name());
        } catch (UnsupportedEncodingException e) {
            log.error("lyyuserid处理异常", e);
        }

        if (str == null) {
            return;
        }

        assembleUrl(result, str);
        ShopPBO shop = result.getShop();

        if (shop != null && StringUtils.isNotEmpty(shop.getShopUrl())) {
            shop.setShopUrl(shop.getShopUrl() + str);
        }

    }

    private void processPassParam(String pass_param, DealGroupPBO result) {
        if (StringUtils.isEmpty(pass_param) || result == null) {
            return;
        }
        String urlStr = null;
        try {
            urlStr = "&pass_param=" + URLEncoder.encode(pass_param, StandardCharsets.UTF_8.name());
        } catch (UnsupportedEncodingException e) {
            log.error("pass_param encode error!", e);
        }
        if (StringUtils.isEmpty(urlStr)) {
            return;
        }
        assembleUrl(result, urlStr);
    }

    private void transparentAppendParamFromReq(DealGroupPBO result, DealBaseReq request) {
        StringBuilder sb = new StringBuilder();
        try {
            // 传递神会员生效标识参数
            sb.append(String.format("&%s=%s", "mmcinflate", URLEncoder.encode(MagicFlagUtils.toString(request.getMmcinflate()), StandardCharsets.UTF_8.name())));
            sb.append(String.format("&%s=%s", "mmcuse", URLEncoder.encode(MagicFlagUtils.toString(request.getMmcuse()), StandardCharsets.UTF_8.name())));
            sb.append(String.format("&%s=%s", "mmcbuy", URLEncoder.encode(MagicFlagUtils.toString(request.getMmcbuy()), StandardCharsets.UTF_8.name())));
            sb.append(String.format("&%s=%s", "mmcfree", URLEncoder.encode(MagicFlagUtils.toString(request.getMmcfree()), StandardCharsets.UTF_8.name())));
            // 传递优惠码
            sb.append(String.format("&%s=%s", "offlinecode", URLEncoder.encode(StringUtils.isBlank(request.getOfflineCode()) ? "" : request.getOfflineCode(), StandardCharsets.UTF_8.name())));
        } catch (UnsupportedEncodingException e) {
            log.error("transparentAppendParamFromReq encode error!", e);
        }
        if (StringUtils.isBlank(sb.toString())) {
            return;
        }
        assembleUrl(result, sb.toString());
    }

    private void assembleUrl(DealGroupPBO result, String str) {
        if (result.getBuyBar() != null && CollectionUtils.isNotEmpty(result.getBuyBar().getBuyBtns())) {
            List<DealBuyBtn> buyBtns = result.getBuyBar().getBuyBtns();

            for (DealBuyBtn btn : buyBtns) {
                if (StringUtils.isNotEmpty(btn.getRedirectUrl())) {
                    btn.setRedirectUrl(btn.getRedirectUrl() + str);
                }
            }
        }
    }

    //发送用户访问团详消息：添加至【最近浏览-团购浏览】(仅点评)
    private void productBrowserMQ(DealBaseReq request,
                                  DealGroupPBO dealGroup, IMobileContext iMobileContext, EnvCtx envCtx) {
        if(envCtx.isExternal() || !iMobileContext.isDianpingClient()) {
            return;
        }

        try {
            //测试流量 or 非到综 or 降级 or 版本小于10.3均不发
            if (!SwitchHelper.isTgBrowse()
                    || !isTargetChannelGroup(dealGroup.getBgName(), ChannelGroupEnum.GENERAL_TYPE)
                    || DpVersion.compareTo(new DpVersion(iMobileContext.getVersion()), DpVersion.V10_3_0) < 0
                    || Tracer.isTest()) {
                return;
            }

            BrowserEntity browserEntity = new BrowserEntity();
            browserEntity.setType(BrowserEntity.TG_TYPE);
            browserEntity.setCategoryid(dealGroup.getCategoryId());
            browserEntity.setCityid(request.getCityid());
            browserEntity.setDpid(iMobileContext.getHeader().getDpid());
            browserEntity.setUserid(String.valueOf(envCtx.getDpUserId()));
            browserEntity.setContent(BrowserContent.getTgBrowserContent(request.getDealgroupid()));
            browserEntity.setPlatform("app");
            final String msg = JSON.toJSONString(browserEntity);

            itemBrowseProducer.sendAsyncMessage(msg, new FutureCallback() {
                @Override
                public void onSuccess(AsyncProducerResult result) {
                    return;
                }

                @Override
                public void onFailure(Throwable t) {
                    log.error(String.format("itemBrowseProducer.sendMessage fail, %s", msg));
                }
            });
        } catch (Exception e) {
            log.error("productBrowserMQ sendMessage error", e);
        }
    }

    private boolean enableLogin(EnvCtx envCtx, DealBaseReq request) {
        if(Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.deal.detail.login",true)) {
            return false;
        }

        //点评和美团app，mrnversion>=0.5.6需要强制登陆
        return (envCtx.getDztgClientTypeEnum() == DztgClientTypeEnum.DIANPING_APP || envCtx.getDztgClientTypeEnum() == DztgClientTypeEnum.MEITUAN_APP)
                && !envCtx.isLogin() && VersionUtils.isGreatEqualThan(request.getMrnversion(), "0.5.6");
    }

    public static boolean isTargetChannelGroup(String channelGroupName, ChannelGroupEnum target){
        if(target == null || StringUtils.isBlank(channelGroupName)){
            return false;
        }
        return target.getEn().equals(channelGroupName);
    }


    private boolean allowAccess(EnvCtx envCtx) {
        try {
            DefaultGroupGrayConfig dealGroupGrayConfig = DefaultGroupGrayConfig.createGroupGrayConfig("deal");
            if(ObjectUtils.isEmpty(dealGroupGrayConfig)) {
                return false;
            }
            DefaultGrayConfigContext context = DefaultGrayConfigContext
                    .builder()
                    .grayUnionIds(Arrays.asList(envCtx.getUnionId()))
                    .build();

            return dealGroupGrayConfig.isAllowAccess(context, envCtx.getUnionId());
        } catch (Exception e) {
            Cat.logError(e);
            return false;
        }

    }

    private boolean isDiffer(String unionId) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.faulttolerance.deal.executor.DzDealBaseExecutor.isDiffer(java.lang.String)");
        int percentValue = Math.min(Lion.getIntValue("com.sankuai.dzu.tpbase.dztgdetailweb.deal.differ.switch"),100);
        if(ObjectUtils.isEmpty(unionId)) {
            return false;
        }
        return Math.abs(unionId.hashCode() % 100) <= percentValue;
    }
}
