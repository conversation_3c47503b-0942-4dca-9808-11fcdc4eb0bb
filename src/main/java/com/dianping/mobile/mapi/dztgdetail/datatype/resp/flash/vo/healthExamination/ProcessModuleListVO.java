package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.healthExamination;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/18 11:01 上午
 */
@MobileDo(id = 0x7237)
public class ProcessModuleListVO implements Serializable {
    /**
     * 步骤列表
     */
    @MobileDo.MobileField(key = 0x70a0)
    private List<ProcessModuleVO> processModuleList;

    /**
     * 标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    public List<ProcessModuleVO> getProcessModuleList() {
        return processModuleList;
    }

    public void setProcessModuleList(List<ProcessModuleVO> processModuleList) {
        this.processModuleList = processModuleList;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}