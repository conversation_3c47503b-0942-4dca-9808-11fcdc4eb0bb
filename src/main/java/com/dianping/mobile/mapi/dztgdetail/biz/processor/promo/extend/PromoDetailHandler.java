package com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.extend;

import com.dianping.mobile.mapi.dztgdetail.common.enums.PromoDetailEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBestPromoDetail;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ServiceGuarantee.DealBestPromoDetailDTO;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;

/**
 * <AUTHOR>
 */
public interface PromoDetailHandler {

    /**
     * 获取优惠类型枚举
     *
     * @return {@link PromoDetailEnum}
     */
    PromoDetailEnum getPromoDetailEnum();

    /**
     * 优惠计算
     *
     * @return {@link DealBestPromoDetail}
     */
    DealBestPromoDetailDTO getDealBestPromoDetail(PriceDisplayDTO priceDisplayDTO );
}
