package com.dianping.mobile.mapi.dztgdetail.datatype.resp.module;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by yangquan02 on 17/11/6.
 */
@TypeDoc(description = "模块配置（对应KV）")
@MobileDo (id = 0x96b)
@Data
public class ModuleConfigDo implements Serializable {

    @FieldDoc(description = "对应KV的key")
    @MobileDo.MobileField(key = 0x263e)
    private String key;

    @FieldDoc(description = "对应KV的value")
    @MobileDo.MobileField(key = 0xa5b8)
    private String value;

}
