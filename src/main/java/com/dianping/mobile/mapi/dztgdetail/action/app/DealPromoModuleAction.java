package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealPromoModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo.ConcisePromoInfo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo.DealPromoModule;
import com.dianping.mobile.mapi.dztgdetail.facade.DealPromoFacade;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.builder.ReflectionToStringBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

/**
 * Created by zuomlin on 2018/12/13.
 */
@InterfaceDoc(displayName = "到综团单促销模块接口",
        type = "restful",
        description = "查询到综团购模块促销信息接口：包括立减、红包分享、拼团、抵用券等等。",
        scenarios = "该接口仅适用于双平台APP站点的团购详情页的团单促销模块，其他以团购详情页维度请咨询项目owner",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "yangquan02"
)
@Controller("general/platform/dztgdetail/dealpromomodule.bin")
@Action(url = "dealpromomodule.bin", httpType = "get")
public class DealPromoModuleAction extends AbsAction<DealPromoModuleReq> {

    @Autowired
    private DealPromoFacade dealPromoFacade;

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "dealpromomodule.bin",
            displayName = "到综团单促销模块接口",
            description = "查询到综团购模块促销信息接口：包括立减、红包分享、拼团、抵用券等等。如果团单没有优惠，则后端不返回数据。",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "dealpromomodule.bin请求参数",
                            type = DealPromoModuleReq.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "result", description = "团购picasso返回数据", type = DealPromoModule.class)},
            restExampleUrl = "http://mapi.51ping.com/general/platform/dztgdetail/dealpromomodule.bin?" +
                    "dealgroupid=200139713&shopid=123&cityid=1",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    ),
            }
    )
    @Override
    protected IMobileResponse validate(DealPromoModuleReq request, IMobileContext iMobileContext) {
        IdUpgradeUtils.processProductIdForDealPromoModuleReq(request, "dealpromomodule.bin");
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.action.app.DealPromoModuleAction.validate(DealPromoModuleReq,IMobileContext)");
        if (request == null || request.getDealGroupId() <= 0) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @Override
    @CryptMethod
    protected IMobileResponse execute(DealPromoModuleReq request, IMobileContext iMobileContext) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.action.app.DealPromoModuleAction.execute(DealPromoModuleReq,IMobileContext)");
        try {
            EnvCtx envCtx = initEnvCtx(iMobileContext);
            DealPromoModule dealPromoModule = dealPromoFacade.queryDealPromoModule(request, envCtx);

            boolean noResult = noResult(dealPromoModule);
            if (noResult) {
                return Resps.SYSTEM_ERROR;
            }
            return new CommonMobileResponse(dealPromoModule);
        } catch (Exception e) {
            logger.error(String.format("dealpromomodule.bin failed, params: request=%s, context=%s",
                    ReflectionToStringBuilder.toString(request), ReflectionToStringBuilder.toString(iMobileContext)), e);
        }
        return Resps.SYSTEM_ERROR;
    }

    private boolean noResult(DealPromoModule dealPromoModule) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.action.app.DealPromoModuleAction.noResult(com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo.DealPromoModule)");
        if (dealPromoModule == null) {
            return true;
        }

        ConcisePromoInfo concisePromoInfo = dealPromoModule.getConcisePromoInfo();
        return (concisePromoInfo == null || CollectionUtils.isEmpty(concisePromoInfo.getPromoInfoItems())) && dealPromoModule.getExposurePromoInfo() == null;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.action.app.DealPromoModuleAction.getRule()");
        return null;
    }
}
