package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/11/25 11:46 上午
 */
@MobileDo(id = 0x844e)
public class DealDetailPriceModuleVO implements Serializable {
    /**
     * 售价价格
     */
    @MobileDo.MobileField(key = 0xec65)
    private String salePrice;

    /**
     * 售价标题
     */
    @MobileDo.MobileField(key = 0xe459)
    private String salePriceTitle;

    /**
     * 原价价格
     */
    @MobileDo.MobileField(key = 0xdfc4)
    private String originalPrice;

    /**
     * 原价标题
     */
    @MobileDo.MobileField(key = 0x364c)
    private String originalPriceTitle;

    public String getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(String salePrice) {
        this.salePrice = salePrice;
    }

    public String getSalePriceTitle() {
        return salePriceTitle;
    }

    public void setSalePriceTitle(String salePriceTitle) {
        this.salePriceTitle = salePriceTitle;
    }

    public String getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(String originalPrice) {
        this.originalPrice = originalPrice;
    }

    public String getOriginalPriceTitle() {
        return originalPriceTitle;
    }

    public void setOriginalPriceTitle(String originalPriceTitle) {
        this.originalPriceTitle = originalPriceTitle;
    }
}