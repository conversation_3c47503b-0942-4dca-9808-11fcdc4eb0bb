package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.sankuai.sig.botdefender.adapter.annotation.DecryptedField;
import lombok.Data;

import java.io.Serializable;

@MobileRequest
@Data
public class ExtraDealDetailModuleReq implements IMobileRequest, Serializable {
    @MobileRequest.Param(name = "cityid", required = true)
    private Integer cityId;
    /**
     * 商户id
     */
    @MobileRequest.Param(name = "shopid")
    private String shopId;
    @MobileRequest.Param(name = "shopIdEncrypt")
    @DecryptedField(targetFieldName = "shopId")
    private String shopIdEncrypt;
    /**
     * 团单id
     */
    @MobileRequest.Param(name = "dealgroupid", required = true)
    private String dealGroupId;
    /**
     * 用户纬度
     */
    @MobileRequest.Param(name = "userlat", required = true)
    private Double userLat;

    /**
     * 用户经度
     */
    @MobileRequest.Param(name = "userlng", required = true)
    private Double userLng;

    /**
     * 保留一个入参的shopId，这个ID 是区分美团点评的
     */
    private String requestShopId;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getShopId() {
        return shopId;
    }

    public void setShopId(String shopIdForLong) {
        this.shopId = shopIdForLong;
    }

    public String getDealGroupId() {
        return dealGroupId;
    }

    public void setDealGroupId(String dealGroupId) {
        this.dealGroupId = dealGroupId;
    }

    public Double getUserLat() {
        return userLat;
    }

    public void setUserLat(Double userLat) {
        this.userLat = userLat;
    }

    public Double getUserLng() {
        return userLng;
    }

    public void setUserLng(Double userLng) {
        this.userLng = userLng;
    }
}