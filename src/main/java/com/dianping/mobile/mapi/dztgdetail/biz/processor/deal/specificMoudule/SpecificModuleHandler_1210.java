package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.DealCategoryEnum;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.EducationDealAttrUtils;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.BaseDisplayItemVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailDisplayUnitVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailSpecificModuleVO;
import com.dianping.mobile.mapi.dztgdetail.util.EduDealUtils;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.dianping.technician.common.api.domain.TechnicianResp;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.technician.info.online.dto.OnlineTechWithAttrsDTO;
import com.sankuai.technician.info.online.service.OnlineTechQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SpecificModuleHandler_1210 implements DealDetailSpecificModuleHandler {

    public static final String JOIN_STR = "、";
    public static final String ATTR_NAME_CLASS_SAFEGUARD = "课程保障";
    public static final String ATTR_NAME_APPEND_SERVICE = "附加服务";
    public static final String ATTR_NAME_CLASS_TYPE = "班型";
    public static final String ATTR_NAME_SUITABLE_CLASEE = "适用阶段";
    public static final String ATTR_NAME_SUITABLE_PEOPLE = "适用人群";
    public static final String ATTR_NAME_CLASS_NUM = "课时数";
    public static final String ATTR_NAME_MAIN_TEACHER = "主讲老师";
    public static final String ATTR_EXAM_MATERIALS = "备考资料";
    public static final String ATTR_NAME_GIFT_PRODUCT = "实物赠品";
    public static final String ATTR_NAME_TRAIN_TIME = "集训时长";
    public static final String ATTR_NAME_CLASS_TIME = "课时";
    public static final String ATTR_NAME_DORM = "住宿";
    public static final String ATTR_NAME_STUDY_ROOM = "自习室";
    public static final String ATTR_NAME_SUPPORT_SERVICE = "配套服务";

    public static final String DISPLAY_TYPE_SERVICE = "service";
    public static final int TIMEOUT = 500;
    @Autowired
    @Qualifier("onlineTechQueryServiceFuture")
    private OnlineTechQueryService onlineTechQueryService;

    @Override
    public String identity() {
        return String.valueOf(DealCategoryEnum.EDU_1210.getDealCategoryId());
    }

    @Override
    public void handle(SpecificModuleCtx ctx) {
        ctx.setResult(buildResult(ctx));
    }


    private DealDetailSpecificModuleVO buildResult(SpecificModuleCtx context) {
        DealGroupDTO dealGroupDTO = context.getDealGroupDTO();
        if (dealGroupDTO == null) {
            return null;
        }
        List<DealDetailDisplayUnitVO> units = new ArrayList<>();
        int categoryId = DealCategoryEnum.EDU_1210.getDealCategoryId().intValue();
        if (EduDealUtils.isVocationalEduCourseOrCamp(dealGroupDTO) && StringUtils.isNotBlank(EducationDealAttrUtils.getSubject(dealGroupDTO))) {
            // 集训时长
            units.add(getTrainTime(context));
            // 主讲老师
            units.add(getTeacher(context));
            // 课时
            units.add(getClassTime(context));
            // 班型
            units.add(getClassType(context));
            // 住宿
            units.add(getDorm(context));
            // 自习室
            units.add(getStudyRoom(context));
            // 配套服务
            units.add(getSupportService(context));
            // 备考资料
            units.add(getExamMaterials(context));
        }

        if (EduDealUtils.isEduOnlineCourseDeal(categoryId, dealGroupDTO)){
            // 主讲老师
            units.add(getTeacher(context));
            // 课时数
            units.add(getClassNum(context));
            // 适用人群
            units.add(getSuitablePeople(context));
            // 适用阶段
            units.add(getSuitableClass(context));
            // 班型
            units.add(getClassType(context));
            // 附加服务
            units.add(getAppendService(context));
            // 课程保障
            units.add(getClassSafeguard(context));
            // 实物赠品
            units.add(getGiftProduct(context));
            // 备考资料
            units.add(getExamMaterials(context));
        }

        DealDetailSpecificModuleVO result = new DealDetailSpecificModuleVO();
        result.setUnits(units.stream().filter(Objects::nonNull).collect(Collectors.toList()));
        return result;
    }

    private DealDetailDisplayUnitVO getSupportService(SpecificModuleCtx context) {
        List<String> appendServices = EducationDealAttrUtils.getAppendServices(context.getDealGroupDTO());
        if (CollectionUtils.isEmpty(appendServices)) {
            return null;
        }
        return getDealDetailDisplayUnitVO(ATTR_NAME_SUPPORT_SERVICE, Lists.newArrayList(buildDisplayItem("",
                String.join(JOIN_STR, appendServices))));
    }

    private DealDetailDisplayUnitVO getExamMaterials(SpecificModuleCtx context) {
        int examMaterials = EducationDealAttrUtils.getExamMaterialNum(context.getDealGroupDTO());
        if (examMaterials == 0) {
            return null;
        }
        return getDealDetailDisplayUnitVO(ATTR_EXAM_MATERIALS, Lists.newArrayList(buildDisplayItem("", String.valueOf(examMaterials))));
    }

    private DealDetailDisplayUnitVO getGiftProduct(SpecificModuleCtx context) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule.SpecificModuleHandler_1210.getGiftProduct(SpecificModuleCtx)");
        List<String> giftProduct = EducationDealAttrUtils.getGiftProduct(context.getDealGroupDTO());
        if (CollectionUtils.isEmpty(giftProduct)) {
            return null;
        }
        return getDealDetailDisplayUnitVO(ATTR_NAME_GIFT_PRODUCT, Lists.newArrayList(buildDisplayItem("",
                String.join(JOIN_STR, giftProduct))));
    }

    private DealDetailDisplayUnitVO getClassSafeguard(SpecificModuleCtx context) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule.SpecificModuleHandler_1210.getClassSafeguard(SpecificModuleCtx)");
        List<String> classSafeguard = EducationDealAttrUtils.getClassSafeguard(context.getDealGroupDTO());
        if (CollectionUtils.isEmpty(classSafeguard)) {
            return null;
        }
        return getDealDetailDisplayUnitVO(ATTR_NAME_CLASS_SAFEGUARD, Lists.newArrayList(buildDisplayItem("",
                String.join(JOIN_STR, classSafeguard))));
    }

    private DealDetailDisplayUnitVO getAppendService(SpecificModuleCtx context) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule.SpecificModuleHandler_1210.getAppendService(SpecificModuleCtx)");
        List<String> appendServices = EducationDealAttrUtils.getAppendServices(context.getDealGroupDTO());
        if (CollectionUtils.isEmpty(appendServices)) {
            return null;
        }
        return getDealDetailDisplayUnitVO(ATTR_NAME_APPEND_SERVICE, Lists.newArrayList(buildDisplayItem("",
                String.join(JOIN_STR, appendServices))));
    }

    private DealDetailDisplayUnitVO getClassType(SpecificModuleCtx context) {
        String origClassType = EducationDealAttrUtils.getOrigClassType(context.getDealGroupDTO());
        if (StringUtils.isBlank(origClassType)) {
            return null;
        }
        return getDealDetailDisplayUnitVO(ATTR_NAME_CLASS_TYPE, Lists.newArrayList(buildDisplayItem("", origClassType)));
    }

    private DealDetailDisplayUnitVO getSuitableClass(SpecificModuleCtx context) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule.SpecificModuleHandler_1210.getSuitableClass(SpecificModuleCtx)");
        String suitableClass = EducationDealAttrUtils.getSuitableClass(context.getDealGroupDTO());
        if (StringUtils.isBlank(suitableClass)) {
            return null;
        }
        return getDealDetailDisplayUnitVO(ATTR_NAME_SUITABLE_CLASEE, Lists.newArrayList(buildDisplayItem("", suitableClass)));
    }

    private DealDetailDisplayUnitVO getSuitablePeople(SpecificModuleCtx context) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule.SpecificModuleHandler_1210.getSuitablePeople(SpecificModuleCtx)");
        String suitablePeople = EducationDealAttrUtils.getSuitablePeople(context.getDealGroupDTO());
        if (StringUtils.isBlank(suitablePeople)) {
            return null;
        }
        return getDealDetailDisplayUnitVO(ATTR_NAME_SUITABLE_PEOPLE, Lists.newArrayList(buildDisplayItem("", suitablePeople)));
    }

    private DealDetailDisplayUnitVO getClassNum(SpecificModuleCtx context) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule.SpecificModuleHandler_1210.getClassNum(com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule.SpecificModuleCtx)");
        String classNum = EducationDealAttrUtils.getClassNum(context.getDealGroupDTO());
        if (StringUtils.isBlank(classNum)) {
            return null;
        }
        return getDealDetailDisplayUnitVO(ATTR_NAME_CLASS_NUM, Lists.newArrayList(buildDisplayItem("", classNum)));
    }

    private DealDetailDisplayUnitVO getTeacher(SpecificModuleCtx context) {
        try {
            List<Integer> teacherIds = EducationDealAttrUtils.getTeacherIds(context.getDealGroupDTO());
            onlineTechQueryService.getTechByTechIds(teacherIds);
            Future<TechnicianResp<List<OnlineTechWithAttrsDTO>>> future = (Future<TechnicianResp<List<OnlineTechWithAttrsDTO>>>) FutureFactory.getFuture();
            TechnicianResp<List<OnlineTechWithAttrsDTO>> onlineTechResponse = future.get(TIMEOUT, TimeUnit.MILLISECONDS);
            if (onlineTechResponse == null || onlineTechResponse.respFail() || CollectionUtils.isEmpty(onlineTechResponse.getData())) {
                return null;
            }
            List<OnlineTechWithAttrsDTO> onlineTechList = onlineTechResponse.getData();
            String teacherStr = onlineTechList.stream()
                    .filter(dto -> dto != null && dto.getTechnician() != null)
                    .map(dto -> dto.getTechnician().getName())
                    .collect(Collectors.joining(JOIN_STR));
            return getDealDetailDisplayUnitVO(ATTR_NAME_MAIN_TEACHER, Lists.newArrayList(buildDisplayItem("", teacherStr)));
        } catch (Exception e) {
            log.error("构建老师失败", e);
        }
        return null;
    }

    private DealDetailDisplayUnitVO getClassTime(SpecificModuleCtx context) {
        String classTime = EducationDealAttrUtils.getClassNum(context.getDealGroupDTO());
        if (StringUtils.isBlank(classTime)) {
            return null;
        }
        return getDealDetailDisplayUnitVO(ATTR_NAME_CLASS_TIME, Lists.newArrayList(buildDisplayItem("", classTime)));
    }

    private DealDetailDisplayUnitVO getTrainTime(SpecificModuleCtx context) {
        String trainTime = EducationDealAttrUtils.getTrainTime(context.getDealGroupDTO());
        if (StringUtils.isBlank(trainTime)) {
            return null;
        }
        return getDealDetailDisplayUnitVO(ATTR_NAME_TRAIN_TIME, Lists.newArrayList(buildDisplayItem("", trainTime)));
    }

    private DealDetailDisplayUnitVO getStudyRoom(SpecificModuleCtx context) {
        String studyRoom = EducationDealAttrUtils.getStudyRoom(context.getDealGroupDTO());
        if (StringUtils.isBlank(studyRoom)) {
            return null;
        }
        return getDealDetailDisplayUnitVO(ATTR_NAME_STUDY_ROOM, Lists.newArrayList(buildDisplayItem("", studyRoom)));
    }

    private DealDetailDisplayUnitVO getDorm(SpecificModuleCtx context) {
        String dormSize = EducationDealAttrUtils.getDorm(context.getDealGroupDTO());
        if (StringUtils.isBlank(dormSize)) {
            return null;
        }
        return getDealDetailDisplayUnitVO(ATTR_NAME_DORM, Lists.newArrayList(buildDisplayItem("", dormSize)));
    }

    private static DealDetailDisplayUnitVO getDealDetailDisplayUnitVO(String attrName, List<BaseDisplayItemVO> displayItemList) {
        DealDetailDisplayUnitVO unit = new DealDetailDisplayUnitVO();
        unit.setTitle(attrName);
        unit.setDisplayItems(displayItemList);
        //这个是和前端约定的，用于区分浮层样式
        unit.setType(DISPLAY_TYPE_SERVICE);
        return unit;
    }

    private BaseDisplayItemVO buildDisplayItem(String name, String detail) {
        BaseDisplayItemVO itemVO = new BaseDisplayItemVO();
        itemVO.setName(name);
        itemVO.setDetail(detail);
        return itemVO;
    }
}