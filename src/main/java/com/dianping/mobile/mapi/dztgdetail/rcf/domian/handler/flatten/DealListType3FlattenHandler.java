package com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler.flatten;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.mobile.mapi.dztgdetail.rcf.enums.ModuleType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;


/**
 * <AUTHOR>
 * @create 2024/12/25 11:16
 */
@Slf4j
@Component("detailist_type3")
public class DealListType3FlattenHandler implements DealModuleFlattenProcessor{

    public static final String TYPE_4_1 = "type_4_1";
    public static final String TYPE_4_2 = "type_4_2";
    public static final String TYPE_4_3 = "type_4_3";

    @Override
    public ModuleType getType() {
        return ModuleType.detailist_type3;
    }

    @Override
    public void flattenModule(JSONArray rcfSkuGroupsModule1Flatten, JSONObject module) {
        JSONArray skuGroupsModel1 = (JSONArray) module.get("skuGroupsModel1");
        if (Objects.isNull(skuGroupsModel1) || skuGroupsModel1.isEmpty()){
            return;
        }
        for (int i = 0; i < skuGroupsModel1.size(); i++) {
            JSONObject skuGroupsModel = (JSONObject) skuGroupsModel1.get(i);
            if (Objects.isNull(skuGroupsModel)){
                continue;
            }
            String title = (String) skuGroupsModel.get("title");
            if (StringUtils.isNotBlank(title)){
                JSONObject result = new JSONObject();
                result.put("key", TYPE_4_1);
                result.put("title", title);
                rcfSkuGroupsModule1Flatten.add(result);
            }
            JSONArray dealSkuList = (JSONArray) skuGroupsModel.get("dealSkuList");
            if (dealSkuList == null || dealSkuList.isEmpty()){
                continue;
            }
            for (int j = 0; j < dealSkuList.size(); j++) {
                JSONObject dealSku = (JSONObject) dealSkuList.get(j);
                if (Objects.isNull(dealSku)){
                    continue;
                }
                // 处理 detailist_type1_dealSkuTitle
                flattenDealSkuTitle(dealSku, rcfSkuGroupsModule1Flatten);
                JSONArray items = (JSONArray) dealSku.get("items");
                if (items != null){
                    // 处理 standard_service_info_v1_3layer_dealSkuItem
                    flattenDealSkuItem(items, rcfSkuGroupsModule1Flatten);
                }
            }
        }
    }

    private String getRightText(JSONObject dealSku){
        String copies = (String) dealSku.get("copies");
        String price = (String) dealSku.get("price");
        if (StringUtils.isBlank(copies) && StringUtils.isBlank(price)){
            return null;
        }
        if (StringUtils.isNotBlank(copies) && StringUtils.isNotBlank(price)){
            return copies + " | " + price;
        }
        return StringUtils.isNotBlank(copies) ? copies : price;
    }

    private void flattenDealSkuTitle(JSONObject dealSku, JSONArray rcfSkuGroupsModule1Flatten){
        if (dealSku == null){
            return;
        }
        String icon = (String) dealSku.get("icon");
        String title = (String) dealSku.get("title");
        String copies = (String) dealSku.get("copies");
        String price = (String) dealSku.get("price");

        JSONObject result = new JSONObject();
        result.put("key", TYPE_4_2);
        result.put("icon", icon);
        result.put("title", title);
        result.put("copies", copies);
        result.put("price", price);
        rcfSkuGroupsModule1Flatten.add(result);
    }
    private void flattenDealSkuItem(JSONArray items, JSONArray rcfSkuGroupsModule1Flatten){
        if (Objects.isNull(items)){
            return;
        }
        for (int i = 0; i < items.size(); i++) {
            JSONObject item = (JSONObject) items.get(i);
            String name = (String) item.get("name");
            String value = (String) item.get("value");
            JSONObject result = new JSONObject();
            result.put("key", TYPE_4_3);
            result.put("content", String.format("%s: %s", name, value));
            rcfSkuGroupsModule1Flatten.add(result);
        }
    }
}
