package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.deal.style.DealPageLayoutService;
import com.dianping.deal.style.dto.laout.DealPageLayoutComponentDTO;
import com.dianping.deal.style.dto.laout.DealPageLayoutUpdateRequest;
import com.dianping.deal.style.dto.laout.DealPageLayoutUpdateResponse;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealLayoutReportReq;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.req.DealLayoutContext;
import com.dianping.pigeon.remoting.common.exception.RpcException;
import com.google.common.collect.Sets;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupCategoryBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupIdBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2024/10/14 17:50
 */
@Slf4j
@Controller("general/platform/dztgdetail/dzdeallayoutreport.bin")
@Action(url = "dzdeallayoutreport.bin", httpType = "post", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class DzDealLayoutReportAction extends AbsAction<DealLayoutReportReq> {

    @Autowired
    private QueryCenterWrapper queryCenterWrapper;

    @Autowired
    @Qualifier("dealPageLayoutService")
    private DealPageLayoutService dealPageLayoutService;

    @Override
    protected IMobileResponse validate(DealLayoutReportReq request, IMobileContext context) {
        if (request == null || request.getDealgroupid() == null || request.getDealgroupid() <= 0) {
            Cat.logEvent("NullPointer", "request.getDealgroupid() is null or <= 0");
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @Override
    protected IMobileResponse execute(DealLayoutReportReq request, IMobileContext iMobileContext) {
        try {
            if (iMobileContext.getUserId() <= 0) {
                return Resps.USER_ERROR;
            }
            DealLayoutContext context = buildContext(request, iMobileContext);
            if (context.isParamIllegal()) {
                return Resps.PARAM_ERROR;
            }
            if (context.getEnvCtx().getDztgClientTypeEnum() != DztgClientTypeEnum.MEITUAN_APP
                    && context.getEnvCtx().getDztgClientTypeEnum() != DztgClientTypeEnum.DIANPING_APP) {
                return new CommonMobileResponse(null);
            }
            DealGroupDTO dealGroupDTO = queryDealInfo(context);
            if (dealGroupDTO == null || dealGroupDTO.getCategory() == null) {
                return Resps.PARAM_ERROR;
            }
            Cat.logEvent("DzDealLayoutReportCategory", String.valueOf(dealGroupDTO.getCategory().getCategoryId()));
            updateCache(dealGroupDTO, context);
            return new CommonMobileResponse(null);
        } catch (Exception e) {
            log.error("DzDealLayoutReportAction,request:{}", JSON.toJSONString(request), e);
            return Resps.SYSTEM_ERROR;
        }
    }

    private void updateCache(DealGroupDTO dealGroupDTO, DealLayoutContext context) {
        DealPageLayoutUpdateRequest updateRequest = new DealPageLayoutUpdateRequest();
        updateRequest.setMtDealGroupId(dealGroupDTO.getMtDealGroupId());
        updateRequest.setUserId(context.getUserId());
        updateRequest.setDealCategoryId(
                Optional.ofNullable(dealGroupDTO.getCategory()).map(DealGroupCategoryDTO::getCategoryId).orElse(0L));
        updateRequest.setClientType(context.getEnvCtx().getDztgClientTypeEnum().getCode());
        updateRequest.setDeviceHeight(context.getDeviceHeight());
        updateRequest.setSource(context.getPageSource());
        updateRequest.setAppVersion(context.getEnvCtx().getVersion());
        updateRequest.setComponents(context.getComponents());
        DealPageLayoutUpdateResponse updateResponse = dealPageLayoutService.update(updateRequest);
        if (!updateResponse.isSuccess()) {
            throw new RpcException("更新布局信息缓存失败,msg:" + updateResponse.getMsg());
        }
    }

    private DealLayoutContext buildContext(DealLayoutReportReq request, IMobileContext iMobileContext) {
        DealLayoutContext context = new DealLayoutContext();
        context.setUserId(iMobileContext.getUserId());
        context.setEnvCtx(initEnvCtxV2(iMobileContext));
        context.setDealGroupId(request.getDealgroupid());
        context.setPageSource(request.getPagesource());
        if (request.getDeviceheight() != null) {
            context.setDeviceHeight((int) Math.round(request.getDeviceheight()));
        }
        context.setComponents(JSON.parseArray(request.getComponents(), DealPageLayoutComponentDTO.class));
        return context;
    }

    private DealGroupDTO queryDealInfo(DealLayoutContext contextRequest) throws TException {
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet(contextRequest.getDealGroupId()), contextRequest.getEnvCtx().isMt() ? IdTypeEnum.MT : IdTypeEnum.DP)
                .dealGroupId(DealGroupIdBuilder.builder().all())
                .category(DealGroupCategoryBuilder.builder().all())
                .build();
        return queryCenterWrapper.getDealGroupDTO(queryByDealGroupIdRequest);
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
