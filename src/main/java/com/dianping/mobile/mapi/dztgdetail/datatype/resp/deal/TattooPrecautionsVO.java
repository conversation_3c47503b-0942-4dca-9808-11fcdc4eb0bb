package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;


import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@TypeDoc(description = "纹绣注意事项返回视图")
@MobileDo(id = 0xa774)
public class TattooPrecautionsVO implements Serializable {

    @FieldDoc(description = "标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "注意事项列表")
    @MobileDo.MobileField(key = 0x347e)
    private List<TattooPrecautionsInfo> infos;

    @FieldDoc(description = "小贴士信息")
    @MobileDo.MobileField(key = 0xbf94)
    private String tab;
}
