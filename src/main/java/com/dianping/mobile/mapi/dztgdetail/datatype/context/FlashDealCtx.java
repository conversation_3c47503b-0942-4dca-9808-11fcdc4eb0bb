package com.dianping.mobile.mapi.dztgdetail.datatype.context;

import com.dianping.deal.style.dto.laout.DealPageLayoutComponentDTO;
import com.dianping.deal.style.dto.laout.DealPageLayoutConfigDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealFlashReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealStyleBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.struct.DealDetailStructModuleDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.DealModuleDetailVO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import lombok.Data;

import java.util.List;

/**
 * 查询团单信息的环境变量
 */
@Data
public class FlashDealCtx {

    public FlashDealCtx(EnvCtx ctx) {
        if (ctx != null) {
            this.envCtx = ctx;
        }
    }

    public boolean isMt() {
        return this.envCtx.isMt();
    }

    private String requestSource;

    private boolean isEnd;
    private EnvCtx envCtx = new EnvCtx();
    private FlashFutureCtx futureCtx = new FlashFutureCtx();
    // 团单id
    private int dealGroupId;
    private long poiid;

    //点评团单ID
    private int dpId;
    //美团团单ID
    private int mtId;
    //点评套餐ID
    private int dpDealId;
    private int dpCityId;
    private int mtCityId;

    private long mtLongShopId;
    private long dpLongShopId;
    private String dpShopUuid;

    private double userlng;
    private double userlat;

    private DealGroupPBO result;
    private DealGroupDTO dealGroupDTO;

    // mrn版本
    private String mrnVersion;

    /**
     * 设备高度
     */
    private int deviceHeight;

    /**
     * 入参
     */
    private DealFlashReq dealflashReq;

    /**
     * 团详样式信息
     */
    private DealStyleBO dealStyleBO;
    /**
     * 定制样式团购详情
     */
    private DealModuleDetailVO dealModuleDetail;
    /**
     * 通用样式团购详情
     */
    private DealDetailStructModuleDo dealDetailStruct;
    /**
     * 闪开控制变量
     */
    private DealPageLayoutConfigDTO layoutConfig;
    /**
     * 团详页布局信息
     */
    private List<DealPageLayoutComponentDTO> layoutComponents;

}