package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ResultList;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/30 14:30
 */
@MobileDo(id = 0x7e73)
@Data
public class RelatedDealModuleVO extends ResultList {

    @FieldDoc(description = "场景")
    @MobileDo.MobileField(key = 0x1e0f)
    private String scene;

    @FieldDoc(description = "标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "关联团单列表")
    @MobileDo.MobileField(key = 0x9437)
    private List<DealGroupPBO> deals;

    @FieldDoc(description = "关联门店列表")
    @MobileDo.MobileField(key = 0x82b8)
    private List<RelatedShopPBO> shops;
}
