package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.CatEvents;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetNailStyleImageRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.nailstyle.OrderNailStyleImageVO;
import com.dianping.mobile.mapi.dztgdetail.facade.NailStyleFacade;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-08-24
 */
@InterfaceDoc(
        displayName = "到综团单美甲类目热门款式查询接口",
        type = "restful",
        description = "到综团单美甲类目热门款式查询，展示款式图、款式列表页跳链等",
        scenarios = "该接口适用于双平台App站点的美甲类目订单详情页展示团单参考款式或推荐款式",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "liuwen17"
)
@Controller("general/platform/dztgdetail/getnailstyleimage.bin")
@Action(url = "getnailstyleimage.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class GetNailStyleImageAction extends AbsAction<GetNailStyleImageRequest> {

    @Resource
    private NailStyleFacade nailStyleFacade;

    @Override
    protected IMobileResponse validate(GetNailStyleImageRequest request, IMobileContext context) {
        if (Objects.isNull(request) || request.getDealGroupId() <= 0) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @Override
    @CryptMethod
    protected IMobileResponse execute(GetNailStyleImageRequest request, IMobileContext context) {
        try {
            EnvCtx envCtx = initEnvCtx(context);
            OrderNailStyleImageVO orderNailStyle = nailStyleFacade.getOrderNailStyle(request, envCtx);
            if (Objects.nonNull(orderNailStyle)) {
                Cat.logMetricForCount(CatEvents.ORDER_NAIL_STYLE_SUC);
                return new CommonMobileResponse(orderNailStyle);
            }
            Cat.logMetricForCount(CatEvents.ORDER_NAIL_STYLE_NO_DARA);
            return new CommonMobileResponse(Resps.NoDataResp);
        } catch (Exception e) {
            Cat.logMetricForCount(CatEvents.ORDER_NAIL_STYLE_ERR);
            Cat.logError("getnailstyleimage.bin err: {}", e);
        }
        return Resps.SYSTEM_ERROR;
        
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
