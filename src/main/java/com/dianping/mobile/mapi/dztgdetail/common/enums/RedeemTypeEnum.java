package com.dianping.mobile.mapi.dztgdetail.common.enums;

import org.apache.commons.lang.StringUtils;

/**
 * 兑换类型
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/4/21.
 */
public enum RedeemTypeEnum {

    DEFAULT(0), //不可能返回该字段，默认值
    DIRECT(1,"ZX"), // 直接消费
    THIRD_PARTY(2,"YD"), // 第三方兑换单
    OTHER(3,"QT");   //其他

    RedeemTypeEnum(int type) {
        this.type = type;
    }

    RedeemTypeEnum(int type, String content) {
        this.type = type;
        this.content = content;
    }

    public static RedeemTypeEnum getRedeemTypeEnum(String str){
        if(StringUtils.isBlank(str)){
            return DEFAULT;
        }
        for(RedeemTypeEnum redeemType : RedeemTypeEnum.values()){
            if(str.equals(redeemType.getContent())){
                return redeemType;
            }
        }
        return DEFAULT;
    }

    private int type;
    private String content;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
