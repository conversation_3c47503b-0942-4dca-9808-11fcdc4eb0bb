package com.dianping.mobile.mapi.dztgdetail.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * 2020/3/16 12:02 下午
 */
@Getter
public enum PromoTypeEnum {
    /**
     * 1是立减，2是拼团，3是抵用券，4是反礼，5是红包分享, 6是闲时优惠
     */
    REDUCTION(1, "立减"),
    PINTUAN(2, "拼团"),
    COUPON(3, "抵用券"),
    BONUS(4, "返礼"),
    RED_PACK(5, "红包分享"),
    IDLE_HOURS(6, "闲时优惠");

    private int code;
    private String desc;

    PromoTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
