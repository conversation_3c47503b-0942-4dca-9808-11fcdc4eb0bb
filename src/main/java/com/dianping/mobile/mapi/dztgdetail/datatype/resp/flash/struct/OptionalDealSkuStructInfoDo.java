package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.struct;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;
import java.util.List;

@TypeDoc(description = "结构化信息可选数据")
@MobileDo(id = 0xc533)
public class OptionalDealSkuStructInfoDo implements Serializable {

    @FieldDoc(description = "可选的项目信息")
    @MobileDo.MobileField(key = 0x234a)
    private List<DealSkuStructInfoDo> dealStructInfo;

    @FieldDoc(description = "描述")
    @MobileDo.MobileField(key = 0xfebf)
    private String desc;

    public List<DealSkuStructInfoDo> getDealStructInfo() {
        return dealStructInfo;
    }

    public void setDealStructInfo(List<DealSkuStructInfoDo> dealStructInfo) {
        this.dealStructInfo = dealStructInfo;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}