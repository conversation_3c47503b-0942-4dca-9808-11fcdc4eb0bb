package com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2023/8/1
 * @since mapi-dztgdetail-web
 */
@MobileDo(id = 0x683a)
public class DealBundBO implements Serializable {
    /**
     *
     */
    @MobileDo.MobileField(key = 0x7764)
    private List<CouponDescItem> coupon;

    public List<CouponDescItem> getCoupon() {
        return coupon;
    }

    public void setCoupon(List<CouponDescItem> coupon) {
        this.coupon = coupon;
    }
}
