package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.deal.sales.common.datatype.IResponse;
import com.dianping.deal.sales.common.datatype.SalesDisplayInfoDTO;
import com.dianping.deal.sales.common.datatype.SalesDisplayQueryRequest;
import com.dianping.deal.sales.common.datatype.SalesOption;
import com.dianping.deal.sales.common.datatype.SalesSubjectParam;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealStockSaleWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.entity.NewDealSaleTagSwitch;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @date 2025-08-01
 * @desc
 */
@Slf4j
public class ProductSaleProcessor extends AbsDealProcessor {

    @Autowired
    private DealStockSaleWrapper dealStockSaleWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        try {
            int thirdCategoryId = Math.toIntExact(DealUtils.getDealGroupServiceTypeId(ctx));
            int secondCategoryId = ctx.getCategoryId();
            NewDealSaleTagSwitch saleTagSwitch = LionConfigUtils.getNewDealSaleTagSwitch();
            return displayNewDealSaleTag(saleTagSwitch, secondCategoryId, thirdCategoryId);
        } catch (Exception e) {
            log.error("ProductSaleProcessor isEnable error", e);
            return false;
        }
    }

    @Override
    public void prepare(DealCtx ctx) {
        SalesDisplayQueryRequest request = buildSaleRequest(ctx);
        Future future = dealStockSaleWrapper.preQueryProductSaleDisplay(request);
        ctx.getFutureCtx().setProductSaleFuture(future);
    }

    @Override
    public void process(DealCtx ctx) {
        if (ctx.getFutureCtx() == null || ctx.getFutureCtx().getProductSaleFuture() == null) {
            return;
        }
        IResponse<Map<SalesSubjectParam, SalesDisplayInfoDTO>> result = dealStockSaleWrapper
                .getFutureResult(ctx.getFutureCtx().getProductSaleFuture());
        if (result == null || !result.isSuccess() || result.getData() == null) {
            return;
        }
        Long thirdCategoryId = DealUtils.getDealGroupServiceTypeId(ctx);
        SalesSubjectParam key = buildSaleSubjectParam(ctx);
        SalesDisplayInfoDTO saleDTO = handleSaleDTO(result.getData().get(key), thirdCategoryId);
        ctx.setProductSaleDTO(saleDTO);
    }

    private SalesDisplayQueryRequest buildSaleRequest(DealCtx ctx) {
        SalesDisplayQueryRequest saleRequest = new SalesDisplayQueryRequest();
        saleRequest.setSubjectParams(Collections.singletonList(buildSaleSubjectParam(ctx)));
        saleRequest.setOption(SalesOption.defaultOption());
        return saleRequest;
    }

    private SalesSubjectParam buildSaleSubjectParam(DealCtx ctx) {
        if (ctx.isMt()) {
            return SalesSubjectParam.ptDealGroup((long)ctx.getMtId());
        }
        return SalesSubjectParam.bizDealGroup((long)ctx.getDpId());
    }

    private SalesDisplayInfoDTO handleSaleDTO(SalesDisplayInfoDTO salesDisplayInfoDTO, long thirdCategoryId) {
        if (Objects.isNull(salesDisplayInfoDTO)) {
            return null;
        }
        // 如果是健康证，需要特殊处理销量文案，销量（0,100]：展示文案：全国热销中
        if (thirdCategoryId == 623L) {
            return handleHealthCertificateSaleDTO(salesDisplayInfoDTO);
        }
        return salesDisplayInfoDTO;
    }

    private boolean displayNewDealSaleTag(NewDealSaleTagSwitch saleTagSwitch, int secondCategoryId,
            int thirdCategoryId) {
        if (saleTagSwitch.isAllSwitch()) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(saleTagSwitch.getSecondCategoryIds())
                && saleTagSwitch.getSecondCategoryIds().contains(secondCategoryId)) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(saleTagSwitch.getThirdCategoryIds())
                && saleTagSwitch.getThirdCategoryIds().contains(thirdCategoryId)) {
            return true;
        }
        return false;
    }

    private SalesDisplayInfoDTO handleHealthCertificateSaleDTO(SalesDisplayInfoDTO salesDisplayInfoDTO) {
        long saleNum = Optional.ofNullable(salesDisplayInfoDTO).map(SalesDisplayInfoDTO::getSalesNum).orElse(0L);
        if (saleNum > 0L && saleNum <= 100L) {
            salesDisplayInfoDTO.setSalesTag("全国热销中");
        }
        return salesDisplayInfoDTO;
    }
}
