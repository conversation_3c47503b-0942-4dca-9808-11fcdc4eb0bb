package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.gm.marketing.times.card.api.enums.PlatformEnum;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.TimesCardWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MiniProgramSceneEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.sankuai.merchantcard.timescard.exposure.dto.CardSummaryBarDTO;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.concurrent.Future;

public class TimesCardProcessor extends AbsDealProcessor {

    @Autowired
    private TimesCardWrapper timesCardWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        boolean isMainApp = (ctx.getEnvCtx().isMainApp() || ctx.getEnvCtx().isMainWX()) && !ctx.isExternal();
        boolean isExternalAndEnabled = ctx.getEnvCtx().isExternalAndEnabled(MiniProgramSceneEnum.TIMES_CARD.getPromoScene());
        return isMainApp || isExternalAndEnabled;
    }

    @Override
    public void prepare(DealCtx ctx) {
        Future timesCardFuture;
        if (ctx.isMt()) {
            timesCardFuture = timesCardWrapper.preTimesCardsV2(ctx.getMtId(), ctx.getMtLongShopId(), PlatformEnum.MT.getCode(), ctx.getEnvCtx().getMtUserId());//已确认判断平台后再使用
        } else {
            timesCardFuture = timesCardWrapper.preTimesCardsV2(ctx.getDpId(), ctx.getDpLongShopId(), PlatformEnum.DP.getCode(), ctx.getEnvCtx().getDpUserId());
        }
        ctx.getFutureCtx().setTimesCardFuture(timesCardFuture);
    }

    @Override
    public void process(DealCtx ctx) {
        CardSummaryBarDTO cardSummaryBarDTO = timesCardWrapper.queryTimesCard(ctx.getFutureCtx().getTimesCardFuture());
        if (cardSummaryBarDTO == null) {
            return;
        }
        ctx.setMultiCard(cardSummaryBarDTO.isIfMultity());
        putTimesCardInfo(ctx, cardSummaryBarDTO);
    }

    private static void putTimesCardInfo(DealCtx ctx, CardSummaryBarDTO cardSummaryBarDTO) {
        BigDecimal timesCardTruthPrice = cardSummaryBarDTO.getPrice();
        if (timesCardTruthPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        ctx.setTimesCard(cardSummaryBarDTO);
    }
}
