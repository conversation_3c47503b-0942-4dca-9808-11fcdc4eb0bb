package com.dianping.mobile.mapi.dztgdetail.common.enums;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 优惠明细枚举
 */
public enum PromoDetailEnum {
    merchantSubsidies("merchantSubsidies","商家补贴","包括商家优惠券和商家立减"),
    merchantCoupons("merchantCoupons","商家优惠券","团单的所有商家优惠券金额"),
    merchantInstantDiscounts("merchantInstantDiscount","商家立减","团单的所有商家立减金额"),
    platformSubsidies("platformSubsidies","美团补贴","包括美团优惠券和美团立减"),
    platformCoupons("platformCoupons","美团优惠券","团单的所有平台优惠券金额"),
    platformInstantDiscounts("platformInstantDiscounts","美团立减","团单的所有平台立减金额"),
    ;

    private String type;
    private String name;
    private String desc;

    PromoDetailEnum(String type, String name, String desc) {
        this.type = type;
        this.name = name;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    private static Map<String, PromoDetailEnum> map = new HashMap<>(PromoDetailEnum.values().length);

    static {
        Arrays.stream(PromoDetailEnum.values())
                .forEach(promoDetailEnum -> map.put(promoDetailEnum.getType(), promoDetailEnum));
    }

    public static PromoDetailEnum getByType(String type) {
        return map.get(type);
    }
}
