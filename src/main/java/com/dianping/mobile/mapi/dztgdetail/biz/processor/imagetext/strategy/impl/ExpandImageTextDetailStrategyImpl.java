package com.dianping.mobile.mapi.dztgdetail.biz.processor.imagetext.strategy.impl;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.imagetext.strategy.AbstractImageTextDetailStrategy;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.imagetext.strategy.ImageTextDetailStrategy;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ImageTextStrategyEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ContentDetailPBO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-12-11
 * @desc 展开图片文字详情策略
 */
@Component
public class ExpandImageTextDetailStrategyImpl extends AbstractImageTextDetailStrategy implements ImageTextDetailStrategy {
    @Override
    public ImageTextStrategyEnum getStrategyName() {
        return ImageTextStrategyEnum.EXPAND;
    }

    @Override
    public ContentDetailPBO getContentDetail(DealGroupDTO dealGroupDTO, List<ContentPBO> contents, int threshold) {
        return super.getContentDetail(dealGroupDTO, contents, threshold);
    }
}
