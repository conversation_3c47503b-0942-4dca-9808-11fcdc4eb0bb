package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;



import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;

@MobileDo(id = 0x7e9b)
@TypeDoc(description = "团详页精选标签")
public class DealChoicestIcon implements Serializable {

    @FieldDoc(description = "icon链接")
    @MobileField(key = 0x3c48)
    private String icon;

    @FieldDoc(description = "icon宽度")
    @MobileField(key = 0x4864)
    private int iconWidth;

    @FieldDoc(description = "icon高度")
    @MobileField(key = 0x75c3)
    private int iconHeight;

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public int getIconWidth() {
        return iconWidth;
    }

    public void setIconWidth(int iconWidth) {
        this.iconWidth = iconWidth;
    }

    public int getIconHeight() {
        return iconHeight;
    }

    public void setIconHeight(int iconHeight) {
        this.iconHeight = iconHeight;
    }
}