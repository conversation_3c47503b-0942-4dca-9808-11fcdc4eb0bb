package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.joygeneral.api.thirdpart.SelfHelpBilliardService;
import com.dianping.joygeneral.api.thirdpart.dto.QueryAutoOpenTableReqDTO;
import com.dianping.joygeneral.api.thirdpart.dto.Response;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.utils.FaultToleranceUtils;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.SwitchUtils;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.dianping.tuangou.dztg.bjwrapper.api.dto.MtDealDto;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

public class SelfServiceBilliardsProcessor extends AbsDealProcessor{
    @Autowired
    private SelfHelpBilliardService selfHelpBilliardServiceFuture;

    @Resource
    private DealGroupWrapper dealGroupWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        return LionConfigUtils.isBilliardsCategoryIds(ctx.getCategoryId());
    }

    @Override
    public void prepare(DealCtx ctx) {
        try {
            QueryAutoOpenTableReqDTO req = new QueryAutoOpenTableReqDTO();
            req.setCustomerId(getCustomerId(ctx));
            req.setDpPoiId(ctx.getDpLongShopId());
            req.setMtPoiId(ctx.getMtLongShopId());
            selfHelpBilliardServiceFuture.queryAutoOpenTable(req);
            ctx.getFutureCtx().setAutoOpenTableFuture(FutureFactory.getFuture());
        } catch (Exception e) {
            logger.error("SelfServiceBilliardsProcessor.queryAutoOpenTable error, ", e);
        }

    }

    @Override
    public void process(DealCtx ctx) {
        if (Objects.isNull(ctx.getFutureCtx()) || Objects.isNull(ctx.getFutureCtx().getAutoOpenTableFuture())) {
            return;
        }
        Boolean autoOpenTable = false;
        try {
            Future<?> autoOpenTableFuture = ctx.getFutureCtx().getAutoOpenTableFuture();
            Response<Boolean> response = (Response<Boolean>) autoOpenTableFuture.get(1000, TimeUnit.MILLISECONDS);
            if (response != null && response.getData() != null) {
                autoOpenTable = response.getData();
            }
        } catch (Exception e) {
            logger.error("SelfHelpBilliardService.queryAutoOpenTable error, ", e);
            FaultToleranceUtils.addException("queryAutoOpenTable", e);
        }
        ctx.setAutoOpenTable(autoOpenTable);
    }

    private long getCustomerId(DealCtx ctx) {
        long customerId = ctx.getCustomerId();

        if(customerId > 0) {
            return customerId;
        }

        if(ctx.getDealGroupDTO() != null && ctx.getDealGroupDTO().getCustomer() != null) {
            customerId = ctx.getDealGroupDTO().getCustomer().getOriginCustomerId();
            ctx.setCustomerId(customerId);
        } else {
            customerId = dealGroupWrapper.getCustomerId(ctx.getDpId());
            ctx.setCustomerId(customerId);
        }

        return customerId;
    }
}
