package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.CatEvents;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetDealTinyInfoRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealPriceTrendVO;
import com.dianping.mobile.mapi.dztgdetail.facade.DealTinyInfoFacade;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-10-10
 * @desc 获取团单价格趋势
 */
@InterfaceDoc(
        displayName = "获取团单价格趋势", type = "restful", description = "获取团单价格趋势，用于查价浮层绘制价格趋势图",
        scenarios = "该接口适用于双平台App站点查价浮层中价格趋势图的绘制", host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "liuwen17"
)
@Controller("general/platform/dztgdetail/getdealgrouppricetrend.bin")
@Action(url = "getdealgrouppricetrend.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class GetDealPriceTrendAction extends AbsAction<GetDealTinyInfoRequest> {

    @Resource
    private DealTinyInfoFacade dealTinyInfoFacade;

    @Override
    protected IMobileResponse validate(GetDealTinyInfoRequest request, IMobileContext context) {
        if (Objects.isNull(request) || Integer.parseInt(request.getDealGroupId()) < 0) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @Override
    @CryptMethod
    protected IMobileResponse execute(GetDealTinyInfoRequest request, IMobileContext context) {
        try {
            EnvCtx envCtx = initEnvCtx(context);
            DealPriceTrendVO dealPriceTrendVO = dealTinyInfoFacade.getDealPriceTrend(request, envCtx);
            if (Objects.nonNull(dealPriceTrendVO)) {
                Cat.logMetricForCount(CatEvents.DEAL_PRICE_TREND_SUC);
                return new CommonMobileResponse(dealPriceTrendVO);
            }
            Cat.logMetricForCount(CatEvents.DEAL_PRICE_TREND_NO_DATA);
            return new CommonMobileResponse(Resps.NoDataResp);
        } catch (Exception e) {
            log.error("getdealgrouppricetrend.bin error: {}", e);
        }
        Cat.logMetricForCount(CatEvents.DEAL_PRICE_TREND_FAIL);
        return Resps.SYSTEM_ERROR;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
