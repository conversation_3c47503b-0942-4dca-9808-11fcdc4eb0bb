package com.dianping.mobile.mapi.dztgdetail.datatype.context;

import com.dianping.cat.Cat;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.pay.common.enums.PayPlatform;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 该类用于表述环境变量，通常包含站点、设备号等通用信息。
 */
@Data
public class EnvCtx {

    //mt侧需要注意，只有priceCf链路一定有
    private long dpUserId;

    //需要注意，只有priceCf链路有
    private long dpVirtualUserId;

    //点评侧需要注意，只有priceCf链路一定有
    private long mtUserId;

    //需要注意，只有priceCf链路有
    private long mtVirtualUserId;

    public long getUserId() {
        return isMt() ? mtUserId : dpUserId;
    }

    public long getVirtualUserId() {
        return isMt() ? mtVirtualUserId : dpVirtualUserId;
    }

    //统一设备号
    protected String unionId;

    //点评设备号
    protected String dpId;

    //美团设备号
    protected String uuid;

    //版本号
    protected String version;

    //是否纯血鸿蒙
    protected boolean isHarmony = false;

    //站点
    protected int clientType;

    // 标识请求来源
    private String mpAppId;

    private String mpSource;

    // 微信小程序用户openid-明文
    private String openId;

    // 微信小程序用户openid-密文
    private String openIdCipher;

    // 字段语义不明，不建议适用（美团侧是 union id、点评侧是 dp id）
    private String appDeviceId;

    private boolean isFromH5;

    private int appId;

    // 爬虫识别
    private String mtsiFlag;

    private String requestURI;

    private String userAgent;

    private String userIp;

    private DztgClientTypeEnum dztgClientTypeEnum;

    private String pragmaToken;

    // 第三方平台专用
    private DztgClientTypeEnum thirdDztgClientTypeEnum;
    /**
     * 开始时间
     */
    private long startTime = System.currentTimeMillis();

    private final List<DztgClientTypeEnum> meituanClientList = Lists.newArrayList(
            DztgClientTypeEnum.MEITUAN_APP,
            DztgClientTypeEnum.MEITUAN_FROM_H5,
            DztgClientTypeEnum.MEITUAN_MAP_APP,
            DztgClientTypeEnum.MEITUAN_WANWU_MINIAPP,
            DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP,
            DztgClientTypeEnum.MEITUAN_LIVE_WEIXIN_MINIAPP,
            DztgClientTypeEnum.BEAM_APP
    );

    private final List<DztgClientTypeEnum> dianpingClientList = Lists.newArrayList(
            DztgClientTypeEnum.DIANPING_APP,
            DztgClientTypeEnum.DIANPING_FROM_H5,
            DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP,
            DztgClientTypeEnum.DIANPING_BAIDUMAP_MINIAPP
    );

    private final List<DztgClientTypeEnum> merchantClientList = Lists.newArrayList(
            DztgClientTypeEnum.DPMERCHANT
    );

    private final List<DztgClientTypeEnum> mtMiniAppList = Lists.newArrayList(
            DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP,
            DztgClientTypeEnum.MEITUAN_KUAISHOU_MINIAPP,
            DztgClientTypeEnum.MEITUAN_WANWU_MINIAPP,
            DztgClientTypeEnum.MEITUAN_LIVE_WEIXIN_MINIAPP
    );

    private final List<DztgClientTypeEnum> dpMiniAppList = Lists.newArrayList(
            DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP,
            DztgClientTypeEnum.DIANPING_BAIDUMAP_MINIAPP
    );

    private final List<DztgClientTypeEnum> nativeAppList = Lists.newArrayList(
            DztgClientTypeEnum.MEITUAN_APP,
            DztgClientTypeEnum.MEITUAN_MAP_APP,
            DztgClientTypeEnum.DIANPING_APP,
            DztgClientTypeEnum.BEAM_APP
    );

    private final List<DztgClientTypeEnum> wxMiniList = Lists.newArrayList(
            DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP,
            DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP
    );

    private final List<DztgClientTypeEnum> memberAllowedClient = Lists.newArrayList(
            DztgClientTypeEnum.MEITUAN_APP,
            DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP,
            DztgClientTypeEnum.DIANPING_APP
    );

    public static final List<Integer> WEIXIN_MINI_PROGRAM_LIST = Lists.newArrayList(
            DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP.getCode());

    public static final List<Integer> MAIN_APP_CLIENT_LIST = Lists.newArrayList(DztgClientTypeEnum.MEITUAN_APP.getCode(),
            DztgClientTypeEnum.DIANPING_APP.getCode());

    public static final List<Integer> MT_WEIXIN_MAIN_MINI = Lists.newArrayList(
            DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP.getCode());

    public boolean isNative(){
        return nativeAppList.contains(dztgClientTypeEnum);
    }

    public boolean isMemberAllowedClient() {
        return memberAllowedClient.contains(dztgClientTypeEnum);
    }

    public boolean isMtMiniApp(){
        return mtMiniAppList.contains(dztgClientTypeEnum);
    }
    public boolean isDpMiniApp(){
        return dpMiniAppList.contains(dztgClientTypeEnum);
    }
    public boolean isMiniApp(){
        return isDpMiniApp() || isMtMiniApp();
    }

    public boolean isWxMini() {
        return wxMiniList.contains(dztgClientTypeEnum);
    }

    //是否为美团站点
    public boolean isMt() {
        if(meituanClientList.contains(dztgClientTypeEnum)) {
            return true;
        }
        return ClientTypeEnum.isMtPlatform(clientType);
    }

    //是否为点评
    public boolean isDp() {
        if(dianpingClientList.contains(dztgClientTypeEnum)) {
            return true;
        }
        return ClientTypeEnum.isDpMainApp(clientType);
    }

    //是否登录了
    public boolean isLogin() {
        return isMt() ? mtUserId > 0 : dpUserId > 0;//已确认判断平台后再使用
    }

    // 是否为开店宝侧
    public boolean isDpMerchant() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx.isDpMerchant()");
        if(DztgClientTypeEnum.DPMERCHANT == dztgClientTypeEnum) {
            return true;
        }
        return false;
    }

    // 是否为阿波罗侧
    public boolean isApollo() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx.isApollo()");
        if(DztgClientTypeEnum.APOLLO == dztgClientTypeEnum) {
            return true;
        }
        return false;
    }

    // 是否为第三方平台 开店宝侧、阿波罗侧
    public boolean isThirdPlatform() {
        if (dztgClientTypeEnum == DztgClientTypeEnum.BEAM_APP) {
            return true;
        }
        if(DztgClientTypeEnum.THIRD_PLATFORM == dztgClientTypeEnum) {
            return true;
        }
        return false;
    }

    /**
     * 是否是美团美播小程序/提单小程序
     * @return true/false
     */
    public boolean isMtLiveMinApp() {
        return Objects.equals(dztgClientTypeEnum, DztgClientTypeEnum.MEITUAN_LIVE_WEIXIN_MINIAPP)
                || Objects.equals(dztgClientTypeEnum, DztgClientTypeEnum.MEITUAN_LIVE_ORDER_WEIXIN_MINIAPP);
    }

    // 勿使用该方法，请使用judgeMainApp()
    @Deprecated
    public boolean isMainApp() {
        return ClientTypeEnum.isMainApp(clientType);
    }

    //美团、点评APP客户端
    public boolean judgeMainApp() {
        return DztgClientTypeEnum.MEITUAN_APP == dztgClientTypeEnum || DztgClientTypeEnum.DIANPING_APP == dztgClientTypeEnum;
    }

    // 勿使用该方法，请使用isWxMini()
    @Deprecated
    public boolean isMainWX() {
        return ClientTypeEnum.isMainWX(clientType);
    }

    public boolean isMainWeb() {
        return clientType == ClientTypeEnum.dp_wap.getType() || clientType == ClientTypeEnum.mt_wap.getType();
    }

    public boolean isIos() {
        return ClientTypeEnum.isIos(clientType);
    }

    public boolean isAndroid() {
        return ClientTypeEnum.isAndroid(clientType);
    }

    public boolean isFromH5() {
        return isFromH5;
    }

    public void setFromH5(boolean fromH5) {
        isFromH5 = fromH5;
    }

    //clientType转PayPlatform
    public int toPayPlatformCode() {
        if (clientType == ClientTypeEnum.mt_weApp.getType()) {
            return PayPlatform.mt_weixin_api.getCode();
        }
        if (clientType == ClientTypeEnum.mt_mainApp_ios.getType()) {
            return PayPlatform.mt_iphone_native.getCode();
        }
        if (clientType == ClientTypeEnum.mt_mainApp_android.getType()) {
            return PayPlatform.mt_android_native.getCode();
        }
        if (clientType == ClientTypeEnum.mt_wap.getType()) {
            return PayPlatform.mt_wap_m.getCode();
        }
        if (clientType == ClientTypeEnum.mt_web.getType()) {
            return PayPlatform.mt_pc.getCode();
        }
        /***下方为点评各个站点***/
        if (clientType == ClientTypeEnum.dp_weApp.getType()) {
            return PayPlatform.weixin_api.getCode();
        }
        if (clientType == ClientTypeEnum.dp_mainApp_android.getType()) {
            return PayPlatform.dp_android_native.getCode();
        }
        if (clientType == ClientTypeEnum.dp_mainApp_ios.getType()) {
            return PayPlatform.dp_iphone_native.getCode();
        }
        if (clientType == ClientTypeEnum.dp_wap.getType()) {
            return PayPlatform.tg_wap_m.getCode();
        }
        if (clientType == ClientTypeEnum.dp_web.getType()) {
            return PayPlatform.tg_pc.getCode();
        }
        return 0;
    }

    /**
     * 如果是美团app、点评app以外的请求，则为external
     */
    public boolean isExternal() {
        if(dztgClientTypeEnum == null) {
            return true;
        }
        if(DztgClientTypeEnum.MEITUAN_APP.getCode() == dztgClientTypeEnum.getCode()) {
            return false;
        }
        if(DztgClientTypeEnum.DIANPING_APP.getCode() == dztgClientTypeEnum.getCode()) {
            return false;
        }
        if(DztgClientTypeEnum.MEITUAN_FROM_H5.getCode() == dztgClientTypeEnum.getCode()) {
            return false;
        }
        if(DztgClientTypeEnum.DIANPING_FROM_H5.getCode() == dztgClientTypeEnum.getCode()) {
            return false;
        }
        if (LionConfigUtils.isMtLiveMiniAppInternal() && DztgClientTypeEnum.MEITUAN_LIVE_WEIXIN_MINIAPP.getCode() == dztgClientTypeEnum.getCode()) {
            return false;
        }
        return true;
    }

    public boolean isExternalAndEnabled(String scene) {
        if(isExternal()) {
            if(dztgClientTypeEnum == null || scene == null) {
                return false;
            }
            String clientId = Integer.toString(dztgClientTypeEnum.getCode());
            Map<String, List> dztgClientTypeEnumId2SceneMap = Lion.getMap("com.sankuai.dzu.tpbase.dztgdetailweb.external.request.scene",
                    List.class, new HashMap<>());
            return dztgClientTypeEnumId2SceneMap != null && dztgClientTypeEnumId2SceneMap.get(clientId) != null && dztgClientTypeEnumId2SceneMap.get(clientId).contains(scene);
        }
        return false;
    }

    // 是外部请求（小程序），并且没有优惠场景
    public boolean isExternalAndNoScene() {
        if(!isExternal()) {
            return false;
        }
        Map<String, List> dztgClientTypeEnumId2SceneMap = Lion.getMap("com.sankuai.dzu.tpbase.dztgdetailweb.external.request.scene",
                List.class, new HashMap<>());
        return dztgClientTypeEnum != null && CollectionUtils.isEmpty(dztgClientTypeEnumId2SceneMap.get(Integer.toString(dztgClientTypeEnum.getCode())));
    }

    public int getAppId() {
        return appId;
    }

    public void setAppId(int appId) {
        this.appId = appId;
    }

    public boolean isMtWxMainMini() {
        if (dztgClientTypeEnum == null) {
            return false;
        }
        return MT_WEIXIN_MAIN_MINI.contains(dztgClientTypeEnum.getCode());
    }
}
