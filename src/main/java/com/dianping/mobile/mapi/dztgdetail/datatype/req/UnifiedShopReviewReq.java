package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.annotation.MobileRequest.Param;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.DecryptedField;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

import static com.sankuai.sig.botdefender.core.enums.AssetIdType.SHOP_UUID;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/12/13.
 */
@Data
@TypeDoc(description = "团单评价模块请求参数")
@MobileRequest
public class UnifiedShopReviewReq implements IMobileRequest, Serializable {

    @Deprecated
    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，int类型")
    @Param(name = "dealgroupid")
    private Integer dealGroupId;

    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，string类型")
    @Param(name = "stringdealgroupid")
    private String stringDealGroupId;

    @Deprecated
    @FieldDoc(description = "商户ID", rule = "商户ID")
    @Param(name = "shopid", shopuuid = "shopuuid")
    private Long shopId;
    @Param(name = "shopIdEncrypt")
    @DecryptedField(targetFieldName = "shopId")
    private String shopIdEncrypt;

    @FieldDoc(description = "商户ID(Long)", rule = "商户ID(Long)")
    @Param(name = "shopidstr", shopuuid = "shopuuid")
    private String shopIdStr;
    @Param(name = "shopIdStrEncrypt")
    @DecryptedField(targetFieldName = "shopIdStr")
    private String shopIdStrEncrypt;

    @FieldDoc(description = "商户UUID", rule = "商户UUID")
    @Param(name = "shopuuid")
    private String shopUuid;
    @Param(name = "shopuuidEncrypt")
    @DecryptedField(targetFieldName = "shopUuid", assetIdType = SHOP_UUID)
    private String shopUuidEncrypt;

    @FieldDoc(description = "团单城市ID", rule = "美团平台为美团城市ID，点评平台为点评城市ID")
    @Param(name = "cityid")
    private Integer cityId;

    @FieldDoc(description = "评论展示数量",  rule = "评论展示数量")
    @Param(name = "displayamount")
    private Integer displayAmount;

    @FieldDoc(description = "前端mrn版本")
    @Param(name = "mrnversion")
    private String mrnVersion;

    @FieldDoc(description = "查看所有评论文案类型", rule = "0:查看全部精选评论；1:全部xx条评价")
    private Integer allCommentTextType;

    public Integer getDealGroupId() {
        return dealGroupId == null ? 0 : dealGroupId;
    }

    @Deprecated
    public Integer getShopId() {
        if(shopId == null) {
            return 0;
        }
        if(shopId > Integer.MAX_VALUE) {
            return 0;
        }
        return shopId.intValue();
    }

    @Deprecated
    public Integer getCityId() {
        return cityId == null ? 0 : cityId;
    }

    public Long getShopIdLong() {
        if(StringUtils.isNumeric(shopIdStr)) {
            return Long.parseLong(shopIdStr);
        } else if(shopId != null) {
            return shopId;
        } else {
            return 0L;
        }
    }
}
