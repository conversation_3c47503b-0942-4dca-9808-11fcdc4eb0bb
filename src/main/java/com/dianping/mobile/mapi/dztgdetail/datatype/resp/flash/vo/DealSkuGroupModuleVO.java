package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/25 4:23 下午
 */
@MobileDo(id = 0x2725)
public class DealSkuGroupModuleVO implements Serializable {
    /**
     * sku列表
     */
    @MobileDo.MobileField(key = 0x7a42)
    private List<DealSkuVO> dealSkuList;

    /**
     * 标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * 是否为m选n
     */
    @MobileDo.MobileField(key = 0x3503)
    private Integer titleStyle;

    public List<DealSkuVO> getDealSkuList() {
        return dealSkuList;
    }

    public void setDealSkuList(List<DealSkuVO> dealSkuList) {
        this.dealSkuList = dealSkuList;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getTitleStyle() {
        return titleStyle;
    }

    public void setTitleStyle(Integer titleStyle) {
        this.titleStyle = titleStyle;
    }
}