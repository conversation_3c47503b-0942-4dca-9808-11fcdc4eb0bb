package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DigestQueryWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.DigestInfoDTO;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;
import java.util.concurrent.Future;

/**
 * @Author: <EMAIL>
 * @Date: 2023/11/21
 */
public class DigestQueryProcessor extends AbsDealProcessor {
    @Autowired
    private DigestQueryWrapper digestQueryWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        return LionConfigUtils.showDarenVideo(ctx.getCategoryId());
    }

    @Override
    public void prepare(DealCtx ctx) {
        Future digestFuture = digestQueryWrapper.getDarenVideosFuture(ctx);
        ctx.getFutureCtx().setDigestFuture(digestFuture);
    }

    @Override
    public void process(DealCtx ctx) {
        if (Objects.isNull(ctx.getFutureCtx().getDigestFuture())) {
            return;
        }

        DigestInfoDTO digestInfoDTO = digestQueryWrapper.getDarenVideos(ctx.getFutureCtx().getDigestFuture(), ctx.getDpId());
        ctx.setDigestInfoDTO(digestInfoDTO);
    }
}
