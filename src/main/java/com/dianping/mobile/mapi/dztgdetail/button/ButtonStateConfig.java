package com.dianping.mobile.mapi.dztgdetail.button;

import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ButtonStateConfig implements Serializable {
    /**
     * 按钮类型
     */
    private BuyBtnTypeEnum buttonType;
    /**
     * 按钮状态
     */
    private List<ButtonStateEnum> buttonStates = Lists.newArrayList(ButtonStateEnum.ANY);
}
