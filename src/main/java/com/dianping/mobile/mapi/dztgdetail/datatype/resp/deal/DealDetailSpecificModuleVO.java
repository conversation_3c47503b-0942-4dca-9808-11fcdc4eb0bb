package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@TypeDoc(description = "团详展示单元")
@MobileDo(id = 0x1cf8)
public class DealDetailSpecificModuleVO implements Serializable {

    @FieldDoc(description = "展示单元列表")
    @MobileField(key = 0x51cb)
    private List<DealDetailDisplayUnitVO> units;

}
