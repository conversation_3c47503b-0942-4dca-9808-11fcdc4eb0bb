package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.pigeon.remoting.invoker.util.InvokerHelper;

public class PigeonInvokeSettingUtils {

    public static void setInvokeTimeout() {
        if (Environment.isTestEnv() || Environment.isQaEnv()) {
            Integer timeout = Lion.getInt(Environment.getAppName(), "pigeonCall.timeout", 2000);
            InvokerHelper.setTimeout(timeout);
        }
    }
}
