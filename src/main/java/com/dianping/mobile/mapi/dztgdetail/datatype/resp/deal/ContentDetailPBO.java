package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;
import java.util.List;

@TypeDoc(description = "详情内容类型")
@MobileDo(id = 0xc026)
public class ContentDetailPBO implements Serializable {

    @FieldDoc(description = "标题")
    @MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "内容组")
    @MobileField(key = 0x8535)
    private List<ContentPBO> contents;

    @FieldDoc(description = "折叠阈值")
    @MobileField(key = 0x5fd0)
    private int foldThreshold = 2;

    @FieldDoc(description = "是否折叠")
    @MobileField(key = 0xc31)
    private boolean fold = true;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<ContentPBO> getContents() {
        return contents;
    }

    public void setContents(List<ContentPBO> contents) {
        this.contents = contents;
    }

    public int getFoldThreshold() {
        return foldThreshold;
    }

    public void setFoldThreshold(int foldThreshold) {
        this.foldThreshold = foldThreshold;
    }

    public boolean isFold() {
        return fold;
    }

    public void setFold(boolean fold) {
        this.fold = fold;
    }
}
