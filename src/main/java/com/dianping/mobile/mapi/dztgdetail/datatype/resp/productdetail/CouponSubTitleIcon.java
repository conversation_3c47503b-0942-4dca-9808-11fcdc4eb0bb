package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-04-09
 * @desc
 */
@Data
@MobileDo(id = 0x69a6)
public class CouponSubTitleIcon implements Serializable {

    @FieldDoc(description = "弹窗信息")
    @MobileField(key = 0xb422)
    private PopUpContentModule popUpContent;

    @FieldDoc(description = "图标")
    @MobileField(key = 0x3c48)
    private Icon icon;
}
