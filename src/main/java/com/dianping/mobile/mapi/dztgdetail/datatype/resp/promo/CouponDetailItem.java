package com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

@TypeDoc(description = "券详情")
@MobileDo(id = 0xebb9)
@Data
public class CouponDetailItem {

    @FieldDoc(description = "优惠券id")
    @MobileDo.MobileField(key = 0x2488)
    public String couponGroupId;

    @FieldDoc(description = "优惠券金额")
    @MobileDo.MobileField(key = 0xfbe2)
    public String amount;

    @FieldDoc(description = "是否可领")
    @MobileDo.MobileField(key = 0x33f4)
    public boolean canAssign;

    @FieldDoc(description = "券名称")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "券适用品类描述")
    @MobileDo.MobileField(key = 0x57a8)
    private String couponCategoryDesc;

    @FieldDoc(description = "券适用品类")
    @MobileDo.MobileField(key = 0x60b4)
    private String couponCategory;

    @FieldDoc(description = "券适用日期及叠加情况描述")
    @MobileDo.MobileField(key = 0x4479)
    private String couponSuitDesc;

    @FieldDoc(description = "券门槛描述")
    @MobileDo.MobileField(key = 0x8ded)
    private String couponThresholdDesc;

    @FieldDoc(description = "最优券详情页外露信息")
    @MobileDo.MobileField(key = 0xc721)
    private String displayText;

    @FieldDoc(description = "优惠券来源 0 平台券 1 商家券")
    @MobileDo.MobileField(key = 0xe01e)
    private Integer couponSrc;

    @FieldDoc(description = "券是否新客专享")
    @MobileDo.MobileField(key = 0xa349)
    private boolean freshExclusive;

    @FieldDoc(description = "是否最优")
    @MobileDo.MobileField(key = 0xc804)
    private boolean optimal;

}
