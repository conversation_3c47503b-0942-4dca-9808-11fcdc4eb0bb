package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.enums;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.ProductBottomBarBlockVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.QuickEntranceBlockVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.StandardTradeBlockVO;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: guangyu<PERSON>e
 * @Date: 2025/3/16 14:30
 */
@Getter
public enum BottomBarBlockTypeEnum {

    STANDARD_TRADE_BUTTON_LIST(1, "购买按钮标准样式", StandardTradeBlockVO.class),
    QUICK_ENTRANCE_BUTTON_LIST(2, "快捷入口标准样式", QuickEntranceBlockVO.class);

    private final int code;

    private final String desc;

    private final Class<? extends ProductBottomBarBlockVO> blockVOClass;

    BottomBarBlockTypeEnum(int code, String desc, Class<? extends ProductBottomBarBlockVO> blockVOClass) {
        this.code = code;
        this.desc = desc;
        this.blockVOClass = blockVOClass;
    }

    public static BottomBarBlockTypeEnum fromCode(int code) {
        for (BottomBarBlockTypeEnum value : BottomBarBlockTypeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效code:" + code);
    }

    public static boolean containsCode(int code) {
        for (BottomBarBlockTypeEnum value : BottomBarBlockTypeEnum.values()) {
            if (value.getCode() == code) {
                return true;
            }
        }
        return false;
    }

    public static Map<Integer, String> buildCodeDescMap() {
        return Arrays.stream(BottomBarBlockTypeEnum.values()).collect(Collectors.toMap(
                BottomBarBlockTypeEnum::getCode,
                BottomBarBlockTypeEnum::getDesc
        ));
    }

    public static Map<String, String> buildNameDescMap() {
        return Arrays.stream(BottomBarBlockTypeEnum.values()).collect(Collectors.toMap(
                BottomBarBlockTypeEnum::name,
                BottomBarBlockTypeEnum::getDesc
        ));
    }

}