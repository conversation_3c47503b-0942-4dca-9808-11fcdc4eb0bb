package com.dianping.mobile.mapi.dztgdetail.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/10/22 14:29
 */
@Data
public class DealDetailCacheGrayConfig {
    /**
     * 缓存总开关
     */
    private boolean enable;

    /**
     * 是否全部走缓存
     */
    private boolean allowAll;

    /**
     * mrn版本 卡控
     */
    private String mrnVersion;

    /**
     * 不允许走缓存的二级类目id
     * 比 allowServiceKeys 优先级高
     */
    private List<Long> denyCategoryIds;

    /**
     * 不允许走缓存的三级类目
     */
    private List<String> denyServiceTypes;
    private List<Long> denyServiceTypeIds;

    /**
     * 加白 unionId,走缓存
     */
    private List<String> unionIds;
    /**
     * 灰度比例
     */
    private int grayRatio;
}
