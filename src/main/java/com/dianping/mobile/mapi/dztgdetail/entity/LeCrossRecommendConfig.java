package com.dianping.mobile.mapi.dztgdetail.entity;

import com.sankuai.dztheme.spuproduct.res.SpuIdDTO;
import lombok.Data;

import java.util.List;

@Data
public class LeCrossRecommendConfig {

    //是否启用Lion配置
    private boolean enable;
    //包含的二级类目
    private List<Integer> categoryIds;
    //标题前缀RelatedDealPBO
    private String prefix;
    //标题后缀
    private String suffix;
    // 实验方案Id
    private String planId;
    //SpuId列表
    private List<SpuIdDTO> spuIds;
    //是否启用斗斛
    private boolean enableDouHu;
    //美团douhuKey
    private String mtDouHuKey;
    //点评douhuKey
    private String dpDouHuKey;
    //查询标品来源
    private String pageSource;
    //标品透传参数
    private String passParam;
}
