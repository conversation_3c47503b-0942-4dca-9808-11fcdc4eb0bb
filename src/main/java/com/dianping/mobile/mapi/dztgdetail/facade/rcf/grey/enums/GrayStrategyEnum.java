package com.dianping.mobile.mapi.dztgdetail.facade.rcf.grey.enums;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2023/8/7
 * @since dzviewscene-productshelf-home
 */
public enum GrayStrategyEnum {


    /**
     * 白名单
     */
    WHITE_LIST("whiteList"),
    PERCENT("percent")
    ;

    GrayStrategyEnum(String type) {
        this.type = type;
    }

    private String type;

    public String getType() {
        return type;
    }

    public static GrayStrategyEnum getFromType(String type) {
        for (GrayStrategyEnum ruleType : values()) {
            if (ruleType.getType().equals(type)) {
                return ruleType;
            }
        }
        return null;
    }

}


