package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.sku;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-04-23
 * @desc 销售规格属性描述信息扩展字段
 */
@Data
@MobileDo(id = 0x9d7b)
public class SkuSalesAttrExt implements Serializable {
    @FieldDoc(description = "描述信息扩展字段的链接资源")
    @MobileDo.MobileField(key = 0xc56e)
    private String url;

    @FieldDoc(description = "销售属性描述信息扩展字段")
    @MobileDo.MobileField(key = 0xfebf)
    private String desc;
}