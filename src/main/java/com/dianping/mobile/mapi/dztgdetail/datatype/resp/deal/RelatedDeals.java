package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DztgModuleAbConfig;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@MobileDo(id = 0x2d47)
@Data
public class RelatedDeals implements Serializable {

    @MobileDo.MobileField(key = 0x862b)
    private List<RelatedDealInfo> dealInfos;

    /**
     * 全部浮层露出数量限制
     */
    @MobileDo.MobileField(key = 0x8c56)
    private Integer popoverDisplayThreshold;

    /**
     * 全部浮层展示标题
     */
    @MobileDo.MobileField(key = 0xac1a)
    private String popoverTitle;

    /**
     * 展示样式信息
     */
    @MobileDo.MobileField(key = 0x19eb)
    private String generalInfo;

    /**
     * ab打点信息
     */
    @MobileDo.MobileField(key = 0xbae3)
    private List<ModuleAbConfig> moduleAbConfigs;

    /**
     * 展示样式信息
     */
    @MobileDo.MobileField(key = 0x2a5b)
    private String tabStyle;
}
