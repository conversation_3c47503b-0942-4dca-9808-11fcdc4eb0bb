package com.dianping.mobile.mapi.dztgdetail.datatype.resp.shop;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.pay.order.service.query.dto.UnifiedOrderWithId;
import com.sankuai.spt.statequery.api.dto.BaseStateDTO;
import com.sankuai.spt.statequery.api.dto.ShopBookInfoDTO;
import com.sankuai.wpt.user.merge.query.thrift.message.FlattedBindRelation;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Data
public class ShopListContext {
    /**
     * 是否美团请求
     */
    private boolean isMt;
    /**
     * 统一订单id
     */
    private String orderId;
    /**
     * 是否需要打日志
     */
    private boolean needLog;
    /**
     * 召回门店
     */
    private List<Long> shopIds;
    /**
     * 适用门店总数
     */
    private long totalCount;
    /**
     * 页面配置项
     */
    private PageConfig pageConfig;
    /**
     * 是否存在黑门店
     */
    private Boolean hasBlackShop;
    /**
     * 用户id关系
     */
    private FlattedBindRelation userRelation;
    /**
     * 团购id,区分平台
     */
    private Long dealGroupId;
    /**
     * 美团团单id
     */
    private Long mtDealGroupId;
    /**
     * 点评团单id
     */
    private Long dpDealGroupId;
    /**
     * 团单二级类目
     */
    private Integer categoryId;
    /**
     * 首页城市id
     */
    private Integer cityId;
    /**
     * 用户定位城市
     */
    private Integer gpsCityId;
    /**
     * 用户选择城市
     */
    private Integer targetCityId;
    /**
     * 环境参数
     */
    private EnvCtx envCtx;
    /**
     * 页面展示类型
     * @see com.dianping.mobile.mapi.dztgdetail.datatype.resp.shop.EntryPageEnum
     */
    private Integer entryPage;
    /**
     * 渠道标识
     */
    private String source;
    /**
     * 点评id到美团id映射
     */
    private Map<Long, List<Long>> dp2MtShopMap;
    /**
     * 美团id到点评id映射
     */
    private Map<Long, List<Long>> mt2DpShopMap;
    /**
     * 一品多态(先囤后订)的订详页场景下，商户 7天内的可预订时间，List<BaseStateDTO>存储了一个商户7天内的可预订时间
     */
    private List<List<BaseStateDTO>> shopBaseStateDTOs;
    /**
     *
     * 一品多态(先囤后订)的订详页场景下，商户预订的状态
     * 美团团单id-商户预订状态
     */
    private Map<Long, ShopBookInfoDTO> shopBookMap;
    /**
     * 神券膨胀标识： 1-可膨 2-不可膨
     */
    private Integer mmcinflate;
    /**
     * 神券可用标识： 1-可用 2-不可用
     */
    private Integer mmcuse;
    /**
     * 券包可买标识： 1-可买 2-不可买
     */
    private Integer mmcbuy;
    /**
     * 神券可领塞标识：1-可领塞 2-不可领塞
     */
    private Integer mmcfree;
    /**
     * 来源页门店id（该门店会被置顶）
     */
    private Long shopId;
    /**
     * 页面号(从1开始)
     */
    private Integer pageNum;
    /**
     * 页面大小
     */
    private Integer pageSize;
    /**
     * 用户纬度
     */
    private Double lat;
    /**
     * 用户经度
     */
    private Double lng;
    /**
     * 用户选定纬度
     */
    private Double targetlat;
    /**
     * 用户选定经度
     */
    private Double targetlng;
    /**
     * 小程序版本
     */
    private String wxVersion;
    /**
     * 线下码ID
     */
    private String offlineCode;
    /**
     * 订单快照
     */
    private UnifiedOrderWithId unifiedOrderWithId;

    /**
     * dpShopId, 是否为保洁自营商店
     */
    private Map<Long, Boolean> dpShopId2IsSelfMap;

    public Long getMtShopId(Long shopId) {
        return isMt ? shopId : getShopIdByMap(shopId, dp2MtShopMap);
    }

    public Long getDpShopId(Long shopId) {
        return isMt ? getShopIdByMap(shopId, mt2DpShopMap) : shopId;
    }
    
    private long getShopIdByMap(Long shopId, Map<Long, List<Long>> shopMap) {
        return Optional.ofNullable(shopMap)
                .map(map -> map.get(shopId))
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.get(0))
                .orElse(0L);
    }
}
