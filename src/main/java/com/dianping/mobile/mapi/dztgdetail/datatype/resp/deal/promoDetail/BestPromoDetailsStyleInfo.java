package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@MobileDo(id = 0x2a2f)
@Data
public class BestPromoDetailsStyleInfo implements Serializable {
    /**
     * 优惠明细样式类型，0普通，1是会员底bar优惠明细样式
     */
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;

    /**
     * 样式描述信息
     */
    @MobileDo.MobileField(key = 0xfebf)
    private String desc;

    /**
     * 样式标注，可标注问号...
     */
    @MobileDo.MobileField(key = 0x6fe)
    private String mark;

    /**
     * 样式Icon
     */
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;
}
