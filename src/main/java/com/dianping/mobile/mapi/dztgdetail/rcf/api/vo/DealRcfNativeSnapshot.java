package com.dianping.mobile.mapi.dztgdetail.rcf.api.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2024/11/16 16:03
 */
@Data
@MobileDo(id = 0xa483)
public class DealRcfNativeSnapshot implements Serializable {
    /**
     * flexbox配置
     */
    @MobileDo.MobileField(key = 0xe537)
    private DealFlexBoxCfg dealFlexBoxConfig;

    /**
     * 首屏BFF缓存数据JSONString
     */
    @MobileDo.MobileField(key = 0x2b0c)
    private String firstScreenBffCache;

    /**
     * 团详模块布局JSONString
     */
    @MobileDo.MobileField(key = 0x7c01)
    private String dealLayoutComponents;

}