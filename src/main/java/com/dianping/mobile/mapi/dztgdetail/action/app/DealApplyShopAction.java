package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetDealApplyShopRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.shop.DealApplyShopVO;
import com.dianping.mobile.mapi.dztgdetail.facade.DealApplyShopFacade;
import com.dianping.mobile.mapi.dztgdetail.helper.AppCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.util.NumbersUtils;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-08-09
 * @desc 综团详适用门店接口
 */
@InterfaceDoc(
        displayName = "到综团详查询适用门店",
        type = "restful",
        description = "到综团详查询适用门店，前端调用BFF接口前没有门店id时调用，不用做团详适用门店模块信息的查询",
        scenarios = "前端调用BFF接口前没有门店id时调用，不用做团详适用门店模块信息的查询",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "liuwen17"
)
@Controller("general/platform/dztgdetail/getdealapplyshop.bin")
@Action(url = "getdealapplyshop.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
@Slf4j
public class DealApplyShopAction extends AbsAction<GetDealApplyShopRequest> {

    @Resource
    private DealApplyShopFacade dealApplyShopFacade;

    @Override
    protected IMobileResponse validate(GetDealApplyShopRequest request, IMobileContext context) {
         if (NumbersUtils.lessThanAndEqualZero(request.getDealGroupId()) 
                 || NumbersUtils.lessThanAndEqualZero(request.getHomeCityId())) {
             return Resps.PARAM_ERROR;
         }
         return null;
    }

    @Override
    protected IMobileResponse execute(GetDealApplyShopRequest request, IMobileContext context) {
        Transaction transaction = Cat.newTransaction("DealApplyShopAction", "execute");
        try {
            DealApplyShopVO dealApplyShopVO = dealApplyShopFacade.queryBestShop(
                    request, AppCtxHelper.isMeituanClient(context), AppCtxHelper.getAppClientType(context)
            );
            transaction.setStatus(Transaction.SUCCESS);
            return new CommonMobileResponse(dealApplyShopVO);
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("getdealapplyshop.bin error,request:{}", JSON.toJSONString(request), e);
            return Resps.SYSTEM_ERROR;
        } finally {
            transaction.complete();
        }
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
