package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealFlashReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.shankai.DealDetailFlashPBO;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.facade.DealDetailFlashFacade;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.List;

/**
 * 功能描述:  <p>
 */
@Controller("general/platform/dztgdetail/dealdetailflash.bin")
@Action(url = "dealdetailflash.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class DzDealDetailFlashAction extends AbsAction<DealFlashReq>  {

    @Resource
    DealDetailFlashFacade dealDetailFlashFacade;

    @Override
    protected IMobileResponse validate(DealFlashReq request, IMobileContext iMobileContext) {
        IdUpgradeUtils.processProductIdForDealFlashReq(request, "dealdetailflash.bin");
        if (request == null || request.getDealgroupid() <= 0) {
            return Resps.PARAM_ERROR;
        }
        if (request.getDeviceheight() == null || request.getDeviceheight() <= 0) {
            //必须要有设备高度，不然无法获取缓存信息
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @Override
    @CryptMethod
    protected IMobileResponse execute(DealFlashReq request, IMobileContext iMobileContext) {
        EnvCtx envCtx = initEnvCtxV2(iMobileContext);
        DealDetailFlashPBO result = dealDetailFlashFacade.buildDealDetailFlashPBO(request, envCtx);
        return new CommonMobileResponse(result);
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
