package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest.Param;
import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.DecryptedField;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: zhangyuan103
 * @Date: 2024/8/13
 */
@Data
@TypeDoc(description = "团购适用门店列表入参")
@MobileRequest
public class DzDealShopsRequest implements IMobileRequest, Serializable {

    /**
     * 神券膨胀标识： 1-可膨 2-不可膨
     */
    @Param(name = "mmcinflate")
    private Integer mmcinflate;

    /**
     * 神券可用标识： 1-可用 2-不可用
     */
    @Param(name = "mmcuse")
    private Integer mmcuse;

    /**
     * 券包可买标识： 1-可买 2-不可买
     */
    @Param(name = "mmcbuy")
    private Integer mmcbuy;

    /**
     * 神券可领塞标识：1-可领塞 2-不可领塞
     */
    @Param(name = "mmcfree")
    private Integer mmcfree;

    /**
     * 来源页门店id（该门店会被置顶）
     */
    @Param(name = "shopId")
    private Long shopId;

    /**
     * 门店Id密文
     */
    @Param(name = "shopIdEncrypt")
    @DecryptedField(targetFieldName = "shopId")
    private String shopIdEncrypt;

    /**
     * 定位城市id
     * 该接口对应平台传入的对应平台的cityId
     */
    @Param(name = "gpsCityId")
    private Integer gpsCityId;

    /**
     * 统一订单id
     */
    @Param(name = "orderId")
    private String orderId;

    /**
     * 渠道标识
     */
    @Param(name = "source")
    private String source;

    /**
     * 团购id
     */
    @Param(name = "dealGroupId", required = true)
    private Long dealGroupId;

    /**
     * 页面号(从1开始)
     */
    @Param(name = "pageNum")
    private Integer pageNum;

    /**
     * 页面大小
     */
    @Param(name = "pageSize")
    private Integer pageSize;

    /**
     * 首页城市id
     */
    @Param(name = "cityId")
    private Integer cityId;

    /**
     * 用户纬度
     */
    @Param(name = "lat")
    private Double lat;

    /**
     * 用户经度
     */
    @Param(name = "lng")
    private Double lng;

    /**
     * 小程序版本
     */
    @Param(name = "csecversionname")
    private String wxVersion;

    /**
     * 用户选定城市id，用于召回过滤
     */
    @Param(name = "targetcityid")
    private Integer targetcityid;

    /**
     * 用户选定纬度
     */
    @Param(name = "targetlat")
    private Double targetlat;

    /**
     * 用户选定经度
     */
    @Param(name = "targetlng")
    private Double targetlng;

    /**
     * 页面展示类型
     * @see com.dianping.mobile.mapi.dztgdetail.datatype.resp.shop.EntryPageEnum
     */
    @Param(name = "entrypage")
    private Integer entryPage;

    /**
     * 线下码ID
     */
    @Param(name = "offlinecode")
    private String offlineCode;
}
