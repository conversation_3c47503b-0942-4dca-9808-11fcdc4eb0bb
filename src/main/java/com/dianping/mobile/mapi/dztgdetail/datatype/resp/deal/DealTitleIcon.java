package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2023/11/8
 * @since mapi-dztgdetail-web
 */
@MobileDo(id = 0xa662)
public class DealTitleIcon implements Serializable {
    /**
     * 标签文案
     */
    @MobileDo.MobileField(key = 0x4587)
    private String labelText;

    /**
     * 标签背景色
     */
    @MobileDo.MobileField(key = 0x1ac7)
    private String labelBackgroundColor;

    public String getLabelText() {
        return labelText;
    }

    public void setLabelText(String labelText) {
        this.labelText = labelText;
    }

    public String getLabelBackgroundColor() {
        return labelBackgroundColor;
    }

    public void setLabelBackgroundColor(String labelBackgroundColor) {
        this.labelBackgroundColor = labelBackgroundColor;
    }
}
