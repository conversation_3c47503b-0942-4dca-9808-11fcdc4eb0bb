package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.struct;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

import java.util.List;

/**
 * Created by ji<PERSON><PERSON><PERSON> on 2020/3/16.
 */
@MobileDo(id = 0x37be)
public class DealExtraNoticeDo {

    @FieldDoc(description = "标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;


    @FieldDoc(description = "提示内容")
    @MobileDo.MobileField(key = 0x8535)
    private List<String> contents;


    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<String> getContents() {
        return contents;
    }

    public void setContents(List<String> contents) {
        this.contents = contents;
    }
}
