package com.dianping.mobile.mapi.dztgdetail.faulttolerance.req;

import com.dianping.deal.detail.enums.PlatformEnum;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.common.ShopUuidUtils;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-10-25
 * @desc 请求上线文
 */
@Data
public class DealBaseContextRequest {
    private Integer dealgroupid;
    private Long poiid;
    private Integer platform;
    private DealBaseReq request;
    private IMobileContext iMobileContext;
    private EnvCtx envCtx;

    public static DealBaseContextRequest build(DealBaseReq request, IMobileContext iMobileContext, EnvCtx envCtx) {
        DealBaseContextRequest contextRequest = new DealBaseContextRequest();
        contextRequest.setRequest(request);
        contextRequest.setIMobileContext(iMobileContext);
        contextRequest.setEnvCtx(envCtx);
        contextRequest.setDealgroupid(request.getDealgroupid());
        Long poiId = Objects.isNull(request.getPoiid()) ? ShopUuidUtils.getShopIdByUuid(request.getShopUuid()) : request.getPoiid();
        contextRequest.setPoiid(poiId);
        contextRequest.setPlatform(envCtx.isMt() ? PlatformEnum.MEI_TUAN.getType() : PlatformEnum.DIAN_PING.getType());
        return contextRequest;
    }
}
