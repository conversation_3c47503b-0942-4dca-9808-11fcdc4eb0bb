package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

@MobileDo(id = 0xc157)
public class ActivityTag implements Serializable {
    /**
     * 背景色
     */
    @MobileDo.MobileField(key = 0xba62)
    private String backgroundColor;

    /**
     * 文案颜色
     */
    @MobileDo.MobileField(key = 0xeead)
    private String textColor;

    /**
     * 文案
     */
    @MobileDo.MobileField(key = 0x451b)
    private String text;

    public String getBackgroundColor() {
        return backgroundColor;
    }

    public void setBackgroundColor(String backgroundColor) {
        this.backgroundColor = backgroundColor;
    }

    public String getTextColor() {
        return textColor;
    }

    public void setTextColor(String textColor) {
        this.textColor = textColor;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }
}
