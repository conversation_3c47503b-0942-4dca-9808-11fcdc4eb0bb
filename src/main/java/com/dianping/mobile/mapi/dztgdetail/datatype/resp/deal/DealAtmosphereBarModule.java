package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/4/4
 */
@Data
public class DealAtmosphereBarModule implements Serializable {

    @FieldDoc(description = "场景，例如秒杀")
    @MobileField(key = 0x1e0f)
    private String scene;

    @FieldDoc(description = "文案")
    @MobileField(key = 0x451b)
    private String text;

    @FieldDoc(description = "倒计时前缀，例如距开始、距结束")
    @MobileField(key = 0x4198)
    private String timePreFix;

    @FieldDoc(description = "倒计时后缀，例如后开抢、后下一场")
    @MobileField(key = 0xf1ac)
    private String timeSubFix;

    @FieldDoc(description = "倒计时时间戳")
    @MobileField(key = 0xddb1)
    private Long countDownTs;

    @FieldDoc(description = "底图")
    @MobileField(key = 0xef5c)
    private String baseMapUrl;

    @FieldDoc(description = "价格名称")
    @MobileField(key = 0x2d0f)
    private String priceNameDesc;

    /**
     * 库存文案
     */
    @FieldDoc(description = "库存文案")
    @MobileField(key = 0x7ef6)
    private String stockText;
}
