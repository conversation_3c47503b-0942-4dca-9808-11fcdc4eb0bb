package com.dianping.mobile.mapi.dztgdetail.datatype.resp.nailstyle;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.EncryptedLinkField;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-24
 * @desc 订单美甲款式返回体
 */
@Setter
@Getter
@TypeDoc(description = "订单美甲款式返回体")
@MobileDo(id = 0xadadc6e7)
public class OrderNailStyleImageVO implements Serializable {

    @FieldDoc(description = "AB实验")
    @MobileDo.MobileField(key = 0xbae3)
    private List<ModuleAbConfig> moduleAbConfigs;

    @FieldDoc(description = "款式列表跳链")
    @MobileDo.MobileField(key = 0x31f3)
//    @EncryptedLinkField(queries = {"shopId"})
    private String nailStyleListUrl;

    @FieldDoc(description = "美甲款式简要信息列表")
    @MobileDo.MobileField(key = 0xe23d)
    private List<NailStyleItemVO> items;
}
