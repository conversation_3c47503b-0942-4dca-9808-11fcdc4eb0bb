package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025-02-26
 * @desc
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "商品详情页氛围条")
@MobileDo(id = 0x63f6)
public class ProductAtmosphereModuleVO extends ProductPriceBarModuleVO {

    @FieldDoc(description = "氛围信息")
    @MobileField(key = 0xe900)
    private AtmosphereInfoDetailVO atmosphere;
}
