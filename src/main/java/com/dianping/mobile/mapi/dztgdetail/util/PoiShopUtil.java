package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.cat.Cat;

import java.text.DecimalFormat;

public class PoiShopUtil {
    // 地球平均半径,单位：m
    private static final double EARTH_RADIUS = 6371393;

    //计算公式采用“球面距离公式”：S=R·arccos[cosβ1cosβ2cos（α1-α2）+sinβ1sinβ2]
    public static double getDistance(double lng1, double lat1, double lng2, double lat2) {
        // 经纬度（角度）转弧度。弧度用作参数，以调用Math.cos和Math.sin
        double radiansAX = Math.toRadians(lng1); // A经弧度
        double radiansAY = Math.toRadians(lat1); // A纬弧度
        double radiansBX = Math.toRadians(lng2); // B经弧度
        double radiansBY = Math.toRadians(lat2); // B纬弧度

        // 公式中“cosβ1cosβ2cos（α1-α2）+sinβ1sinβ2”的部分，得到∠AOB的cos值
        double cos = Math.cos(radiansAY) * Math.cos(radiansBY) * Math.cos(radiansAX - radiansBX)
                + Math.sin(radiansAY) * Math.sin(radiansBY);
        double acos = Math.acos(cos); // 反余弦值
        return EARTH_RADIUS * acos; // 最终结果
    }

    //计算公式采用“球面距离公式”：S=R·arccos[cosβ1cosβ2cos（α1-α2）+sinβ1sinβ2]
    public static String getDistanceStr(double lng1, double lat1, double lng2, double lat2) {
        return buildDistance(getDistance(lng1, lat1, lng2, lat2));
    }

    public static String getDistanceStr2(double lng1, double lat1, double lng2, double lat2) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.util.PoiShopUtil.getDistanceStr2(double,double,double,double)");
        return distanceText(getDistance(lng1, lat1, lng2, lat2));
    }

    public static String buildDistance(double distance){

        double tmp = distance / 1000;
        if(tmp > 30){
            return ">30km";
        }else if (tmp >= 10 && tmp <= 30) {
            return Math.floor(tmp) + "km";
        } else if (tmp >= 1 && tmp < 10 ) {
            DecimalFormat df = new DecimalFormat("0.0");
            return df.format(tmp) + "km";
        } else if (tmp >= 0.1 && tmp < 1 ) {
            return (int)(tmp*1000) + "m";
        } else if (tmp < 0.1 ) {
            return "<100m";
        }

        return "";
    }

    public static String distanceText(double distance) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.util.PoiShopUtil.distanceText(double)");
        double tmp = distance / 1000;
        if (tmp >= 99) {
            return "≥99km";
        } else if (tmp >= 1 && tmp < 99) {
            return String.format("%.1f", tmp) + "km";
        }else if (tmp >= 0.1 && tmp < 1 ) {
            return (int)(tmp*1000) + "m";
        } else {
            return "<100m";
        }
    }

}
