package com.dianping.mobile.mapi.dztgdetail.biz.immersiveimage.impl;

import com.dianping.mobile.mapi.dztgdetail.biz.immersiveimage.ImmersiveImageService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ContentSearchWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageFilterRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageFilterVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageVO;
import com.dianping.mobile.mapi.dztgdetail.entity.QueryExhibitImageParam;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2024/8/8
 */
@Component
public class GlassesImmersiveImageServiceImpl implements ImmersiveImageService {
    @Resource
    private ContentSearchWrapper contentSearchWrapper;
    @Resource
    private MapperWrapper mapperWrapper;
    /**
     * 眼镜团单二级类目ID
     */
    private static final List<Integer> GLASSES_CATEGORY_IDS = Lists.newArrayList(406);

    @Override
    public List<Integer> getCategoryIds() {
        return GLASSES_CATEGORY_IDS;
    }

    @Override
    public ImmersiveImageVO getImmersiveImage(GetImmersiveImageRequest request, EnvCtx envCtx) {
        QueryExhibitImageParam param = QueryExhibitImageParam.builder()
                .categoryId(request.getCategoryId())
                .dpDealGroupId(request.getDealGroupId())
                .start(request.getStart())
                .limit(request.getLimit())
                .shopId(envCtx.isMt() ? request.getShopId() : mapperWrapper.getMtShopIdByDpShopIdLong(request.getShopId()))
                .clientType(envCtx.getClientType())
                .infoContentId(request.getInfoContentId())
                .mtDealGroupId(request.getMtDealGroupId().intValue())
                .build();
        // 查询团单信息
        ImmersiveImageVO immersiveImageVO = contentSearchWrapper.getImmersiveImage(param);
        return immersiveImageVO;
    }

    @Override
    public ImmersiveImageFilterVO getImmersiveImageFilter(GetImmersiveImageFilterRequest request) {
        return null;
    }
}
