package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.annotation.MobileRequest.Param;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

@Data
@TypeDoc(description = "解密接口请求参数")
@MobileRequest
public class DecryptReq implements IMobileRequest {

    @FieldDoc(description = "加密字符串", rule = "加密字符串")
    @Param(name = "encryptedStr")
    private String encryptedStr;

}