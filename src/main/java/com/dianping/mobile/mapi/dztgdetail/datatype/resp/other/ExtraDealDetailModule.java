package com.dianping.mobile.mapi.dztgdetail.datatype.resp.other;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

public class ExtraDealDetailModule implements Serializable {
    /**
     *属性值
     */
    @MobileDo.MobileField
    private String value;

    /**
    * 属性名
    */
    @MobileDo.MobileField
    private String name;

    /**
    * 图片
    */
    @MobileDo.MobileField
    private String pic;

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }
}