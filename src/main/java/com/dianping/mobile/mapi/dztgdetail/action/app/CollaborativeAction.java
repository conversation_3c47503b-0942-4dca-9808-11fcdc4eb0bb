package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.CollaborativeRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.MtCollaborativeResponse;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.MtDealModel;
import com.dianping.mobile.mapi.dztgdetail.facade.RecommendFacade;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.meituan.servicecatalog.api.annotations.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.List;


@InterfaceDoc(displayName = "美团到综APP团详页查询团购推荐/看了又看列表",
        type = "restful",
        description = "美团到综APP团详页访问或者非单中心团单访问获取团购推荐/看了又看列表(待废弃)",
        scenarios = "适用于美团APP历史版本团详页访问或者非单中心团单访问，处于长期下线中，请新版本或其他情况都不要使用，如必须调用请联系当前owner",
        host = "http://mapi.meituan.com/general/platform/mtdetail/",
        authors = "qian.wang.sh"
)
@Controller("collaborative.bin")
@Action(url = "general/platform/mtdetail/collaborative.bin", httpType = "get")
public class CollaborativeAction extends AbsAction<CollaborativeRequest> {

    @Resource
    private RecommendFacade recommendFacade;

    @Override
    protected IMobileResponse validate(CollaborativeRequest request, IMobileContext iMobileContext) {
        IdUpgradeUtils.processProductIdForCollaborativeRequest(request, "collaborative.bin");
        if (request.getDealId() <= 0) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "collaborative.bin",
            displayName = "美团到综APP团详页查询团购推荐/看了又看列表",
            description = "美团到综APP团详页访问或者非单中心团单访问获取团购推荐/看了又看列表(待废弃)",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "collaborative.bin请求参数",
                            type = CollaborativeRequest.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "iMobileResponse", description = "通用返回数据模型")},
            restExampleUrl = "http://mapi.meituan.com/general/platform/mtdetail/collaborative.bin?dealid=40700733",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    )
            }
    )
    @Override
    protected IMobileResponse execute(CollaborativeRequest request, IMobileContext iMobileContext) {
        MtCollaborativeResponse message;
        try {
            message = recommendFacade.getCollaborativeDealGroup(request, iMobileContext);
            // 判断用户是否登录，未登录则隐藏价格信息
            long userId = iMobileContext.getUserId();
            if (userId <= 0 && message != null && !CollectionUtils.isEmpty(message.getDeals())) {
                for (MtDealModel deal : message.getDeals()) {
                    if (deal != null) {
                        deal.setPrice(0);
                        deal.setOriginalPrice(0);
                        deal.setCampaignPrice(0);
                        deal.setCanbuyprice(0);
                        deal.setPriceCalendars(null);
                    }
                }
            }
        } catch (Exception e) {
            log.error("collaborative.bin error!", e);
            return Resps.SERVER_ERROR;
        }
        if (message == null) {
            return Resps.SERVER_ERROR;
        }
        return new CommonMobileResponse(message);
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
