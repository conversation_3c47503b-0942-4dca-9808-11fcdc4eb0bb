package com.dianping.mobile.mapi.dztgdetail.button.shoppingcart;

import java.math.RoundingMode;
import java.util.List;

import com.dianping.mobile.mapi.dztgdetail.button.joy.NewIdlePromoButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PriceRuleModule;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;

import static com.dianping.mobile.mapi.dztgdetail.common.enums.ShoppingCartStatusEnum.*;

/**
 * @<PERSON> she<PERSON>
 * @date 2023/5/15
 */
public class ShoppingCartIdlePromoButtonBuilder extends NewIdlePromoButtonBuilder {

    @Override
    protected void afterBuild(DealCtx context, DealBuyBtn button) {
        super.afterBuild(context, button);

        button.setBtnTitle(getButtonTitle(context, "立即抢购"));
        button.setAddShoppingCartStatus(ADD_SHOPPING_CART.code);
        button.setPriceRuleModule(buildPriceRuleModule(context, button));

    }

    private PriceRuleModule buildPriceRuleModule(DealCtx context, DealBuyBtn button) {
        PriceRuleModule priceRuleModule = new PriceRuleModule();
        priceRuleModule.setPriceRuleType(BuyBtnTypeEnum.IDLE_DEAL.getCode());
        priceRuleModule.setPriceRuleTags(buildPriceRuleTag(button));
        priceRuleModule.setPromoDesc(buildPromoDesc(context));
        return priceRuleModule;
    }

    private List<String> buildPriceRuleTag(DealBuyBtn button) {
        List<String> priceRuleTags = Lists.newArrayList();

        String priceRuleTitle= "限时特惠";
        String pricePerTime = "￥" + button.getPriceStr();

        priceRuleTags.add(priceRuleTitle);
        priceRuleTags.add(pricePerTime);

        return priceRuleTags;
    }

    private String buildPromoDesc(DealCtx context) {
        String useTime = getPrice(context).getUsedPromos().get(0).getConsumeTimeDesc();
        PriceDisplayDTO price = super.getPrice(context);
        String savedMoney = price.getMarketPrice().subtract(price.getPrice())
                .setScale(1, RoundingMode.CEILING).stripTrailingZeros().toPlainString();

        return useTime + "可用，本单省￥" + savedMoney;
    }
}
