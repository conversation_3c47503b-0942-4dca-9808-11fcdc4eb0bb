package com.dianping.mobile.mapi.dztgdetail.util;

import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/16
 */

public class DealSkuUtils {

    public static final String SELL_DIFFERENT_GRADES = "SellDifferentGrades";

    public static List<DealGroupDealDTO> getValidDealSkuList(List<DealGroupDealDTO> dealGroupDealDTOS) {
        return dealGroupDealDTOS.stream()
                // 过滤掉没有销售属性的skuId
                .filter(d -> CollectionUtils.isNotEmpty(d.getAttrs()))
                // 过滤掉没有标题的skuId
                .filter(d -> Objects.nonNull(d.getBasic()) && StringUtils.isNotBlank(d.getBasic().getTitle()))
                // 过滤掉无效的skuId
                .filter(d -> d.getBasic().getStatus() == 1)
                // 过滤非分时段定价skuId
                .filter(d -> hitMultiSku(d.getAttrs())).collect(Collectors.toList());
    }

    private static boolean hitMultiSku(List<AttrDTO> attrs) {
        AttrDTO attrDTO = attrs.stream().filter(a -> Objects.equals(a.getName(), SELL_DIFFERENT_GRADES))
//                && Objects.equals(a.getValue(), Constants.TIME_SEGMENT_PRICING)
//                .filter(a -> Objects.equals(a.getName(), Constants.SEGMENT_TYPE)
//                && (Objects.equals(a.getValue(), Constants.DAY_NIGHT_RANGE) || (Objects.equals(a.getValue(), Constants.WEEKDAY_WEEKEND_RANGE))))
                .findFirst().orElse(null);
        return Objects.nonNull(attrDTO);
    }

}