package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.annotation.MobileRequest.Param;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.DecryptedField;
import lombok.Data;

import java.io.Serializable;

@Data
@TypeDoc(description = "团单通知层接口请求参数")
@MobileRequest
public class DealNoticeLayerReq implements IMobileRequest, Serializable {

    @Deprecated
    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，原int类型")
    @Param(name = "dealgroupid")
    private Integer dealgroupid;

    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，string类型")
    @Param(name = "stringdealgroupid")
    private String stringDealGroupId;

    @FieldDoc(description = "商户ID", rule = "美团平台为美团商户ID，点评平台为点评商户ID")
    @Param(name = "poiid", shopuuid = "shopuuid")
    private Long poiid;
    @Param(name = "poiidEncrypt")
    @DecryptedField(targetFieldName = "poiid")
    private String poiidEncrypt;


    @FieldDoc(description = "城市ID", rule = "美团平台为美团城市ID，点评平台为点评城市ID，优先给到首页城市ID（非用户地理位置城市）")
    @Param(name = "cityid")
    private Integer cityid;


    @FieldDoc(description = "用户经度(只接受火星坐标系gcj02)")
    @Param(name = "userlng")
    private Double userlng;

    @FieldDoc(description = "用户纬度(只接受火星坐标系gcj02)")
    @Param(name = "userlat")
    private Double userlat;


    @FieldDoc(description = "请求来源")
    @Param(name = "pagesource")
    private String pageSource;


    @FieldDoc(description = "前端MRN版本号")
    @Param(name = "mrnversion")
    private String mrnversion;

    @FieldDoc(description = "团单通用字段（JsonString，内容可扩展）")
    @Param(name = "dealparam")
    private String dealparam;

    public Integer getCityid() {
        return cityid != null ? cityid : 0;
    }


    public Double getUserlng() {
        return userlng != null ? userlng : 0;
    }

    public Double getUserlat() {
        return userlat != null ? userlat : 0;
    }


}
