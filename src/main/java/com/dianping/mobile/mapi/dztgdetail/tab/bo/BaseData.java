package com.dianping.mobile.mapi.dztgdetail.tab.bo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Data;

/**
 * 团单相关信息
 */

@Data
public class BaseData {
    /**
     * 是否是点评平台
     */
    private boolean isDp;

    /**
     * 当前团单对应的点评团单id
     */
    private int currentDpGroupId;

    /**
     * 团单发布类目
     */
    private int publishCategoryId;

    /**
     * 城市id，与平台对应
     */
    private int cityId;

    /**
     * 门店id，与平台对应
     */
    @Deprecated
    private int shopId;

    /**
     * 门店id，与平台对应
     */
    private long shopIdLong;

    /**
     * 在线点评团单id
     */
    private List<Integer> dpDealGroupIds;

    /**
     * 关联点评团单（包含当前点评团单）
     */
    private List<Integer> relatedDpDealGroupIds;

    /**
     * 在线美团团单id
     */
    private List<Integer> mtDealGroupIds;

    /**
     * key:在线点评团单id
     * value:在线美团团单id
     */
    private Map<Integer, Integer> dp2mt;

    /**
     * key:在线美团团单id
     * value:在线点评团单id
     */
    private Map<Integer, Integer> mt2dp;

    private List<Integer> getPlatformRelatedDealGroupIds() {
        if (isDp) {
            return relatedDpDealGroupIds;
        } else {
            List<Integer> relatedMtDealGroupIds = new ArrayList<>();
            for (Integer relatedDpDealGroupId : relatedDpDealGroupIds) {
                Integer relatedMtDealGroupId = dp2mt.get(relatedDpDealGroupId);
                if (relatedMtDealGroupId != null) {
                    relatedMtDealGroupIds.add(relatedMtDealGroupId);
                }
            }
            return relatedMtDealGroupIds;
        }
    }

    public List<Long> getLDealGroupIds() {
        List<Integer> dealGroupIds = getPlatformRelatedDealGroupIds();
        return dealGroupIds.stream().mapToLong(Integer::longValue).boxed().collect(Collectors.toList());
    }
}