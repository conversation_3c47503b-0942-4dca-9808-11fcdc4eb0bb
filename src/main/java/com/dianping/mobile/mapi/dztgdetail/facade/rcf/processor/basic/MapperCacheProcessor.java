package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.basic;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperCacheWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.exception.CityIdMapperException;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.exception.DealIdMapperException;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.exception.ShopIdMapperException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class MapperCacheProcessor extends AbsDealProcessor {

    @Autowired
    private MapperCacheWrapper mapperCacheWrapper;

    private static final String Already_Not_Null_IN_DP = "AlreadyNotNullINDP";

    private static final String Error_Id_Cat_Name = "ErrorIdMapper";


    @Override
    public boolean isEnable(DealCtx ctx) {
        return true;
    }

    @Override
    public void prepare(DealCtx ctx) {
        if (ctx.getEnvCtx().isMt()) {
            ctx.setDpId(mapperCacheWrapper.fetchDpDealId(ctx.getMtId()));
            ctx.setDpLongShopId(mapperCacheWrapper.fetchDpShopId(ctx.getMtLongShopId()));
            ctx.setDpCityId(mapperCacheWrapper.fetchDpCityId(ctx.getMtCityId()));
        } else {
            if (ctx.getMtId() <= 0) {
                ctx.setMtId(mapperCacheWrapper.fetchMtDealId(ctx.getDpId()));
            } else {
                Cat.logEvent(Already_Not_Null_IN_DP, "mtDealId");
            }
            if (ctx.getMtLongShopId() <= 0) {
                ctx.setMtLongShopId(mapperCacheWrapper.fetchMtShopId(ctx.getDpLongShopId()));
            } else {
                Cat.logEvent(Already_Not_Null_IN_DP, "mtShopId");
            }
            if (ctx.getMtCityId() <= 0) {
                ctx.setMtCityId(mapperCacheWrapper.fetchMtCityId(ctx.getDpCityId()));
            } else {
                Cat.logEvent(Already_Not_Null_IN_DP, "mtCityId");
            }
        }
        //兼容老逻辑，这俩字段可以废弃了
        ctx.setRealMtCityId(ctx.getMtCityId());
        doEmptyCheck(ctx);
    }

    public static void doEmptyCheck(DealCtx ctx) {
        if (ctx.getDpId() <= 0 && ctx.getMtId() > 0) {
            Cat.logEvent(Error_Id_Cat_Name, String.format("%s-dpDealGroupId", ctx.getRequestSource()));
            log.error("dpDealGroupId is null,mtDealGroupId:{}", ctx.getMtId(), new DealIdMapperException("dpDealGroupId is null,mtDealGroupId:" + ctx.getMtId()));
        }
        if (ctx.getMtId() <= 0 && ctx.getDpId() > 0) {
            Cat.logEvent(Error_Id_Cat_Name, String.format("%s-mtDealGroupId", ctx.getRequestSource()));
            log.error("mtDealGroupId is null,dpDealGroupId:{}", ctx.getDpId(), new DealIdMapperException("mtDealGroupId is null,dpDealGroupId:" + ctx.getDpId()));
        }
        if (ctx.getDpLongShopId() <= 0 && ctx.getMtLongShopId() > 0) {
            Cat.logEvent(Error_Id_Cat_Name, String.format("%s-dpLongShopId", ctx.getRequestSource()));
            log.error("dpShopId is null,mtShopId:{}", ctx.getMtLongShopId(), new ShopIdMapperException("dpShopId is null,mtShopId:" + ctx.getMtLongShopId()));
        }
        if (ctx.getMtLongShopId() <= 0 && ctx.getDpLongShopId() > 0) {
            Cat.logEvent(Error_Id_Cat_Name, String.format("%s-mtLongShopId", ctx.getRequestSource()));
            log.error("mtShopId is null,dpShopId:{}", ctx.getDpLongShopId(), new ShopIdMapperException("mtShopId is null,dpShopId:" + ctx.getDpLongShopId()));
        }
        if (ctx.getDpCityId() <= 0 && ctx.getMtCityId() > 0) {
            Cat.logEvent(Error_Id_Cat_Name, String.format("%s-dpCityId", ctx.getRequestSource()));
            log.error("dpCityId is null,mtCityId:{}", ctx.getMtCityId(), new CityIdMapperException("dpCityId is null,mtCityId:" + ctx.getMtCityId()));
        }
        if (ctx.getMtCityId() <= 0 && ctx.getDpCityId() > 0) {
            Cat.logEvent(Error_Id_Cat_Name, String.format("%s-mtCityId", ctx.getRequestSource()));
            log.error("mtCityId is null,dpCityId:{}", ctx.getDpCityId(), new CityIdMapperException("mtCityId is null,dpCityId:" + ctx.getDpCityId()));
        }
    }

    @Override
    public void process(DealCtx ctx) {
    }

}
