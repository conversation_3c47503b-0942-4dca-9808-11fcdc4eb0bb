package com.dianping.mobile.mapi.dztgdetail.datatype.resp.other;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/9/10 11:14
 */
@Data
public class DigestMaterialInfoPhotoDTO {
    private List<CaseVideosDTO> caseVideos;
    private String photoSpot;
    private List<String> customerAuth;
    private DestinationDTO destination;
    private List<String> themeScene;
    private long editTime;
    private long sys_createTime;
    private long sampleType;
    private List<String> photographyStyle;
    private long oldContentId;
    private RelateInfoDTO relateInfo;
    private long top;
    private List<Long> sys_dz_mt_dealId;
    private CaseCoverDTO caseCover;
    private List<Long> caseStyle;
    private long rank;
    private String caseName;
    private List<CasePicsDTO> casePics;
    private long caseCategory;
    private String clothingType;
    private String makeupType;
    private long infoContentId;
    private List<Long> shootScene;
    private String caseHighlights;

}
