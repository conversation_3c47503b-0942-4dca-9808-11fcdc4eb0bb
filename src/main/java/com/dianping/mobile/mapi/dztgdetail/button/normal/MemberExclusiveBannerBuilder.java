package com.dianping.mobile.mapi.dztgdetail.button.normal;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MemberExclusiveWrapper;
import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BannerTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.LeadActionEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBanner;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * @Desc 会员专属底部bar构建
 * <AUTHOR>
 * @Date 2023/5/26 11:44
 */
@Slf4j
public class MemberExclusiveBannerBuilder extends AbstractButtonBuilder {

    private static final String VERSION = "0.5.0";

    @Override
    protected void doBuild(DealCtx context, ButtonBuilderChain chain) {
        if (!GreyUtils.isMemberExclusive(context)) {
            return;
        }
        buildBanner(context);
        chain.build(context);
    }


    private void buildBanner(DealCtx context) {
        boolean isMember = MemberExclusiveWrapper.isMember(context);
        if (isMember) {
            return;
        }
        // 非会员时展示
        DealBuyBanner banner = new DealBuyBanner();
        banner.setShow(true);
        banner.setBannerType(BannerTypeEnum.MemberExclusive.getType());
        banner.setLeadAction(LeadActionEnum.NOOP.getCode());
        banner.setLeadText("会员专属商品，仅限商家会员购买");
        banner.setLeadAction(LeadActionEnum.TOAST_IDLE_HOURS.getCode());
        context.getBuyBar().setBuyBanner(banner);
    }

}