package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;

/**
 * 判断团单是否为预览团单
 * <AUTHOR>
 */
public class PreviewDealGroupUtils {

    /**
     * 判断点评团单是否为预览单
     * 不使用美团ID，美团场景请勿使用
     * @param ctx DealCtx
     * @return boolean
     */
    public static boolean isPreviewDianpingDealGroup(DealCtx ctx) {
        return ctx.getDpId() >= 500000 && ctx.getDpId() <= 510000;
    }

}