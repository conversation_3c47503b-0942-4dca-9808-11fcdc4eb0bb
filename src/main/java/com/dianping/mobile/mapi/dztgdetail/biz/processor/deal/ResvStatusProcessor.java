package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.meituan.nibscp.common.api.enums.TradeTypeEnum;
import com.sankuai.clr.content.process.thrift.api.ResvQueryService;
import com.sankuai.clr.content.process.thrift.dto.common.ProjectDTO;
import com.sankuai.clr.content.process.thrift.dto.common.TimeDTO;
import com.sankuai.clr.content.process.thrift.dto.req.QueryStockReqDTO;
import com.sankuai.clr.content.process.thrift.dto.resp.QueryStockRespDTO;
import org.apache.thrift.TException;

import javax.annotation.Resource;
import java.util.Objects;

public class ResvStatusProcessor extends AbsDealProcessor{
    @Resource
    private ResvQueryService resvQueryService;

    @Override
    public boolean isEnable(DealCtx ctx) {
        boolean zeroVaccineSwitch = Lion.getBoolean("com.sankuai.dzu.tpbase.dztgdetailweb","com.sankuai.dzu.tpbase.dztgdetailweb.zeroVaccine.switch",false);
        return ctx.getDealGroupDTO() != null
                && ctx.getDealGroupDTO().getBasic() != null
                && Objects.nonNull(ctx.getDealGroupDTO().getBasic().getTradeType())
                && ctx.getDealGroupDTO().getBasic().getTradeType() == TradeTypeEnum.RESERVATION.getCode()
                && zeroVaccineSwitch;
    }
    @Override
    public void prepare(DealCtx ctx) {

    }

    @Override
    public void process(DealCtx ctx) {
        QueryStockReqDTO queryStockReqDTO = buildQueryStockReqDTO(ctx);
        QueryStockRespDTO respDTO = null;
        try {
            respDTO = resvQueryService.queryStock(queryStockReqDTO);
        } catch (TException e) {
            Cat.logError(e);
        }
        if (respDTO == null) {
            ctx.setIsCanResv(false);
            return ;
        }
        if (respDTO.getStockItemStatus() == null || respDTO.getStockItemStatus()==2 ){
            ctx.setIsCanResv(false);
            return;
        }
        if ( respDTO.getStockItemStatus() == 1){
            //可约
            ctx.setIsCanResv(true);
        }
    }

    private QueryStockReqDTO buildQueryStockReqDTO(DealCtx ctx) {
        QueryStockReqDTO queryStockReqDTO =new QueryStockReqDTO();
        if (ctx.isMt()){
            long mtLongShopId = ctx.getMtLongShopId();
            queryStockReqDTO.setMtShopId(String.valueOf(mtLongShopId));
            queryStockReqDTO.setPlatform(2);
        }else{
            long dpLongShopId = ctx.getDpLongShopId();
            queryStockReqDTO.setDpShopId(String.valueOf(dpLongShopId));
            queryStockReqDTO.setPlatform(1);
        }
        queryStockReqDTO.setEntranceCode(1);

        ProjectDTO projectDTO = new ProjectDTO();
        projectDTO.setProjectType(1);
        projectDTO.setProjectCode(Long.valueOf(ctx.getDpId()));
        queryStockReqDTO.setProject(projectDTO);
        TimeDTO timeDTO = new TimeDTO();
        timeDTO.setTimeType(1);
        timeDTO.setTimeUnit(1);
        timeDTO.setTimeValue(String.valueOf(60));
        queryStockReqDTO.setTime(timeDTO);
        queryStockReqDTO.setReservationScene(1);
        return queryStockReqDTO;
    }
}
