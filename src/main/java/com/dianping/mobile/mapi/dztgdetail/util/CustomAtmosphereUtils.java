package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.sales.common.datatype.KeyParam;
import com.dianping.deal.sales.common.datatype.SaleItem;
import com.dianping.deal.sales.common.datatype.SpuSale;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.AtmosphereSceneEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DealAttrCons;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealAtmosphereBarModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2024/9/10
 */
public class CustomAtmosphereUtils {
    public static final String SPU_ATMOSPHERE_ENABLE = "spu_atmosphere_enable";
    public static final String SPU_ATMOSPHERE_BGURL = "spu_atmosphere_bgurl";
    public static final String SALE_TEXT_PREFIX = "sale_text_prefix";
    public static final String SUPER_DEAL_SPU_TYPE = "17";

    public static boolean distributeSuperDealAtmosphere(DealCtx ctx) {
        Map<String, String> atmosphereConfig = Lion.getMap(LionConstants.APP_KEY, LionConstants.STANDARD_PRODUCT_ATMOSPHERE_CONFIG, String.class, Collections.emptyMap());
        if (MapUtils.isEmpty(atmosphereConfig)) {
            return false;
        }

        // 开关打开 && 非特团渠道 && 团单对应为超团商品
        return MapUtils.isNotEmpty(atmosphereConfig)
                && atmosphereConfig.containsKey(SPU_ATMOSPHERE_ENABLE)
                && "true".equals(atmosphereConfig.get(SPU_ATMOSPHERE_ENABLE))
                && !Objects.equals(ctx.getRequestSource(), RequestSourceEnum.COST_EFFECTIVE.getSource())
                && CollectionUtils.isNotEmpty(DealAttrHelper.getAttributeValues(ctx.getAttrs(), DealAttrCons.MTSS_REF_SPU_ID))
                && matchSuperDealType(ctx.getAttrs())
                && VersionUtils.isGreatEqualThan(ctx.getMrnVersion(), "0.5.12");
    }

    private static boolean matchSuperDealType(List<AttributeDTO> attrs) {
        List<String> spuSceneTypeList = DealAttrHelper.getAttributeValues(attrs, DealAttrCons.SPU_SCENE_TYPE);
        if (CollectionUtils.isEmpty(spuSceneTypeList)) {
            return false;
        }

        if (spuSceneTypeList.contains(SUPER_DEAL_SPU_TYPE)) {
            return true;
        }
        return false;
    }

    public static void buildSuperDealAtmosphereBar(DealGroupPBO result) {
        Map<String, String> atmosphereConfig = Lion.getMap(LionConstants.APP_KEY, LionConstants.STANDARD_PRODUCT_ATMOSPHERE_CONFIG, String.class, Collections.emptyMap());
        if (result.getDealAtmosphereBarModules() == null) {
            result.setDealAtmosphereBarModules(Lists.newArrayList());
        }

        if (!atmosphereConfig.containsKey(SPU_ATMOSPHERE_BGURL)) {
            return;
        }
        DealAtmosphereBarModule dealAtmosphereBarModule = new DealAtmosphereBarModule();
        dealAtmosphereBarModule.setScene(AtmosphereSceneEnum.STANDARD_SERVICE.sceneName);
        dealAtmosphereBarModule.setBaseMapUrl(atmosphereConfig.get(SPU_ATMOSPHERE_BGURL));
        result.getDealAtmosphereBarModules().add(dealAtmosphereBarModule);
    }

    public static String getSaleDescStr(Map<KeyParam, SpuSale> spuSaleMap) {
        Map<String, String> atmosphereConfig = Lion.getMap(LionConstants.APP_KEY, LionConstants.STANDARD_PRODUCT_ATMOSPHERE_CONFIG, String.class, Collections.emptyMap());
        if (MapUtils.isEmpty(spuSaleMap)) {
            return null;
        }

        SpuSale spuSale = spuSaleMap.entrySet().iterator().next().getValue();
        if (Objects.isNull(spuSale) || MapUtils.isEmpty(spuSale.getCycleSale()) || !spuSale.getCycleSale().containsKey(360)) {
            return null;
        }
        SaleItem yearSale = spuSale.getCycleSale().get(360);
        long totalSale = yearSale.getDpDisplay() + yearSale.getMtDisplay();
        String saleCountStr;
        if (totalSale < 50) {
            saleCountStr = String.valueOf(totalSale);
        } else if (totalSale < 100) {
            saleCountStr = (totalSale / 10 * 10) + "+";
        } else if (totalSale < 1000) {
            saleCountStr = (totalSale / 100 * 100) + "+";
        } else if (totalSale < 10000) {
            saleCountStr = (totalSale / 1000 * 1000) + "+";
        } else {
            saleCountStr = (totalSale / 10000) + "." + ((totalSale % 10000) / 1000) + "万+";
        }
        return atmosphereConfig.get(SALE_TEXT_PREFIX) + saleCountStr;
    }
}
