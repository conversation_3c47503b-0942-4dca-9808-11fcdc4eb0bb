package com.dianping.mobile.mapi.dztgdetail.button.shoppingcart;

import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.PriceContext;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealCartBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealCartMemberPrice;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.PriceHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.mpmctmember.process.common.enums.MemberChargeTypeEnum;
import com.sankuai.mpmctmember.process.common.enums.MemberDiscountTypeEnum;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.MemberBaseInfoDTO;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.MemberDiscountInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/12/13
 */
@Slf4j
public class ShoppingCartMemberPriceBuilder extends AbstractButtonBuilder {
    private static final String MEMBER_PRICE = "MEMBER_PRICE";

    private static final Map<String, String> BTN_TITLE_MAP = Maps.newHashMap();
    static {
        BTN_TITLE_MAP.put("NEW_MEMBER_BENEFITS", "新会员价");
        BTN_TITLE_MAP.put("MEMBER_BENEFITS", "会员价");
    }

    private static final String NEW_MEMBER_TEXT_FORMAT = "当前商品为%s新会员专享优惠，立即免费领取会员享会员优惠￥%s元";

    private static final String MEMBER_TEXT_FORMAT = "您还未开通%s，马上领取会员享受专属会员优惠¥%s元";

    @Override
    public void doBuild(DealCtx context, ButtonBuilderChain chain) {
        buildMemberPriceButton(context);
        //保证一定往下执行
        chain.build(context);
    }

    private void buildMemberPriceButton(DealCtx context) {
        //会员价不是最优组合，则返回
        if (!context.getPriceContext().isMemberPromoDealPrice()) {
            return;
        }

        //如果没有会员价活动
        PromoDTO promoDTO = getMemberPricePromoDTO(context);
        if (promoDTO == null) {
            log.error("failed to get member price, dealPromoPrice={}", context.getPriceContext().getDealPromoPrice());
            return;
        }

        MemberDiscountInfoDTO memberDiscountInfoDTO = context.getShopMemberDiscountInfoDTO();
        MemberBaseInfoDTO memberBaseInfoDTO = memberDiscountInfoDTO.getMemberBaseInfo();

        if(!context.getPriceContext().isDisplayInflateCoupon() && !context.getCostEffectivePinTuan().isCePinTuanScene()) {
            //购买按钮
            processBtn(context, promoDTO);
        }


        //如果有会员价活动，但是已经领卡了
        if (Boolean.TRUE.equals(memberBaseInfoDTO.getMember())) {
            return;
        }
        
        // 如果是付费会员卡
        if (Objects.equals(memberDiscountInfoDTO.getChargeType(), MemberChargeTypeEnum.CHARGE.getCode())) {
            return;
        }

        String promoAmount = PriceHelper.dropLastZero(promoDTO.getAmount());
        DealCartMemberPrice memberPriceInfo = new DealCartMemberPrice();
        memberPriceInfo.setTitle("领取品牌会员卡");
        if (MemberDiscountTypeEnum.NEW_MEMBER_PROMO.getType().equals(memberDiscountInfoDTO.getMemberDiscountType())) {
            memberPriceInfo.setSubText("若曾在其他渠道领取过此品牌会员则无法享受此优惠");
            memberPriceInfo.setText(String.format(NEW_MEMBER_TEXT_FORMAT, memberBaseInfoDTO.getMemberCardName(), promoAmount));
        } else {
            memberPriceInfo.setText(String.format(MEMBER_TEXT_FORMAT, memberBaseInfoDTO.getMemberCardName(), promoAmount));
        }
        memberPriceInfo.setRedirectUrl(memberDiscountInfoDTO.getMemberPageUrl());

        memberPriceInfo.setOriginBntTitle("美团价购买");
        memberPriceInfo.setMemberBntTitle("免费领卡享会员价");
        BigDecimal originPrice = context.getPriceContext().getOriginDealPromoPrice().getPrice();
        memberPriceInfo.setOriginPriceStr(PriceHelper.formatPrice(originPrice));

        DealCartBtn cartBtn = new DealCartBtn();
        cartBtn.setMemberPriceInfo(memberPriceInfo);
        DealBuyBar dealBuyBar = context.getBuyBar();
        dealBuyBar.setCartBtn(cartBtn);
    }


    private void processBtn(DealCtx context, PromoDTO promoDTO) {
        //购买按钮颜色加深
        if (context.getDealExtraTypes() == null) {
            context.setDealExtraTypes(Lists.newArrayList(MEMBER_PRICE));
        } else {
            context.getDealExtraTypes().add(MEMBER_PRICE);
        }

        //购买按钮文案
        if (context.getBuyBar() != null && CollectionUtils.isNotEmpty(context.getBuyBar().getBuyBtns())) {
            DealBuyBtn dealBuyBtn = context.getBuyBar().getBuyBtns().get(0);
            dealBuyBtn.setBtnTitle(getPromoButtonTitle(context, BTN_TITLE_MAP.get(promoDTO.getIdentity().getPromoShowType())));
        }
    }

    private PromoDTO getMemberPricePromoDTO(DealCtx context) {
        PriceContext priceContext = context.getPriceContext();
        PriceDisplayDTO dealPromoPrice = priceContext.getDealPromoPrice();
        List<PromoDTO> promoDTOList = dealPromoPrice.getUsedPromos();
        if (CollectionUtils.isEmpty(promoDTOList)) {
            return null;
        }

        for (PromoDTO promoDTO : promoDTOList) {
            if (promoDTO.getIdentity() != null && BTN_TITLE_MAP.containsKey(promoDTO.getIdentity().getPromoShowType())) {
                return promoDTO;
            }
        }
        return null;
    }
}
