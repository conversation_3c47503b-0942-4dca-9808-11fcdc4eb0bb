package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.handler;

import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.degrade.util.JsonCodec;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.DealDouHuUtil;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.EducationDealAttrUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.utils.FaultToleranceUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.ThriftAsyncUtils;
import com.dianping.mobile.mapi.dztgdetail.util.TimesDealUtil;
import com.dianping.vc.sdk.lang.NumberUtils;
import com.google.common.collect.Maps;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.IBNPLAccessThriftService;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.dto.BNPLExposureDTO;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.dto.DeviceInfoDTO;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.dto.ExposureScenarioDTO;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.dto.ProductSignDTO;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.enums.BizIdFieldMeaning;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.enums.ExposureEnum;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.request.BNPLExposureRequest;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.response.BNPLExposureResponse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/4 16:56
 */
@Slf4j
public class UserAndProductCreditPayProcessor extends AbsDealProcessor {

    private static final String USER_CREDIT_PAY_CONFIG = "deal.shelf.user.credit.pay.config";
    private static final String USE_CASE = "OPEN";
    private static final String BIZ_ID = "bizId";
    private static final String PLAN_ID = "planId";
    private static final String SIGN_IPH_PAY_MERCHANT_NO = "signIphPayMerchantNo";

    @Autowired
    private IBNPLAccessThriftService bnplAccessThriftService;

    @Autowired
    private DouHuBiz douHuBiz;

    @Override
    public boolean isEnable(DealCtx ctx) {
        List<Integer> productCreditPayCategories = LionConfigUtils.getProductCreditPayCategories();
        // 放量开关true; 团购次卡; 美团侧; 类目符合白名单: [303,501]
        return LionConfigUtils.getProductCreditPaySwitch() && TimesDealUtil.isTimesDeal(ctx.getDealGroupDTO())
                && ctx.getEnvCtx().isMt() && productCreditPayCategories.contains(ctx.getCategoryId()) &&
                Optional.of(ctx.getEnvCtx()).map(EnvCtx::getUserId).orElse(0L) > 0;
    }

    @Override
    public void prepare(DealCtx ctx) {
        try {
            bnplAccessThriftService.exposure(buildBNPLExposureRequest(ctx));
            ctx.getFutureCtx().setUserAndProductCreditPayFuture(ContextStore.getFuture());
        } catch (Exception e) {
            logger.error("UserAndProductCreditPayProcessor.exposure error, ", e);
        }
    }

    @Override
    public void process(DealCtx ctx) {
        int dpId = ctx.getDpId();
        try {
            // 团购次卡接入先用后付,美团侧调实验
            ModuleAbConfig creditPayAbConfig = douHuBiz.getAbExpResult(ctx, "MtCreditPayExp");

            List<ModuleAbConfig> moduleAbConfigs = ctx.getModuleAbConfigs();
            if (moduleAbConfigs == null) {
                moduleAbConfigs = new ArrayList<>();
            }

            if (Objects.nonNull(creditPayAbConfig)) {
                moduleAbConfigs.add(creditPayAbConfig);
            }

            // 获取商品是否可用属性
            String payMethodStr = EducationDealAttrUtils.getFormatAttrByName(ctx.getDealGroupDTO().getAttrs(),
                    "pay_method");
            int payMethod = NumberUtils.toInt(payMethodStr, 0);
            if (payMethod != 4) {
                return;
            }

            // 获取用户是否支持曝光信息
            Future<?> userAndProductCreditPayFuture = ctx.getFutureCtx().getUserAndProductCreditPayFuture();
            BNPLExposureResponse response = (BNPLExposureResponse)userAndProductCreditPayFuture.get(1000,
                    TimeUnit.MILLISECONDS);
            if (response == null || !response.isSuccess() || response.getData() == null) {
                return;
            }
            BNPLExposureDTO data = response.getData();
            String exposureResult = Optional.ofNullable(data).map(BNPLExposureDTO::getExposure)
                    .orElse(StringUtils.EMPTY);
            ctx.setCreditPay(StringUtils.equals(ExposureEnum.EXPOSED.getCode(), exposureResult)
                    && DealDouHuUtil.getCreditPayExpResult(ctx));
        } catch (Exception e) {
            logger.error("UserAndProductCreditPayProcessor.process error,dpDealGroupId:{} ", dpId, e);
            FaultToleranceUtils.addException("UserAndProductCreditPayProcessor", e);
        }
    }

    private BNPLExposureRequest buildBNPLExposureRequest(DealCtx ctx) {
        BNPLExposureRequest request = new BNPLExposureRequest();
        ExposureScenarioDTO exposureScenarioDTO = new ExposureScenarioDTO();
        // 业务ID字段含义
        exposureScenarioDTO.setBizIdFieldMeaning(BizIdFieldMeaning.PLAN_ID.getValue());

        // 用例 OPEN：预览准入； OPEN_ACCOUNT：开通准入； COMMIT_ORDER：提单页准入
        exposureScenarioDTO.setUseCase(USE_CASE);
        // 丽人次卡：BeauCard 教育次卡：EduCard 按摩足疗次卡:SpaCard
        exposureScenarioDTO.setSubBizScenario(getSubBizScenario(ctx));
        request.setExposureScenario(exposureScenarioDTO);

        EnvCtx envCtx = ctx.getEnvCtx();
        request.setUserId(envCtx.getUserId());
        ProductSignDTO productSignDTO = new ProductSignDTO();
        UserCreditPayConfig userCreditPayConfig = getUserCreditPayConfig();
        if (userCreditPayConfig != null) {
            // 业务ID值 金服提供
            exposureScenarioDTO.setBizId(userCreditPayConfig.getBizId());
            // 先用后付模版ID 金服提供
            productSignDTO.setPlanId(userCreditPayConfig.getPlanId());
            // 签约商户号 金服提供
            productSignDTO.setSignIphPayMerchantNo(userCreditPayConfig.getSignPayMerchantNo());
        }
        request.setProductSignInfo(productSignDTO);
        DeviceInfoDTO deviceInfoDTO = new DeviceInfoDTO();
        deviceInfoDTO.setUuid(envCtx.getUuid());
        // 平台标识
        deviceInfoDTO.setPlatform(envCtx.isIos() ? "iphone" : "android");
        // 客户端标识
        deviceInfoDTO.setApp(getApp(ctx));
        deviceInfoDTO.setLocation(getLocation(ctx));
        deviceInfoDTO.setCityId((long)ctx.getCityId4P());
        deviceInfoDTO.setAppVersion(ctx.getEnvCtx().getVersion());
        request.setDeviceInfo(deviceInfoDTO);
        return request;
    }

    private String getSubBizScenario(DealCtx ctx) {
        int shopType = Optional.ofNullable(ctx).map(DealCtx::getBestShopResp).map(BestShopDTO::getShopType).orElse(0);
        if (shopType == 50) {
            return "BeauCard";
        } else if (shopType == 30) {
            return "SpaCard";
        } else if (shopType == 75) {
            return "EduCard";
        }
        return null;
    }

    private String getLocation(DealCtx ctx) {
        double userLat = ctx.getUserlat();
        double userLng = ctx.getUserlng();
        return userLat + "_" + userLng;
    }

    private String getApp(DealCtx ctx) {
        EnvCtx envCtx = ctx.getEnvCtx();
        boolean mainApp = envCtx.isNative();
        boolean mt = envCtx.isMt();
        if (mt) {
            if (mainApp) {
                return "group";
            } else {
                return "weixin";
            }
        } else {
            if (mainApp) {
                return "dianping-nova";
            }
        }
        return null;
    }

    @Data
    static class UserCreditPayConfig {
        private String bizId;
        private Long planId;
        private Long signPayMerchantNo;
    }

    private UserCreditPayConfig getUserCreditPayConfig() {
        try {
            Map<String, String> configMap = Lion.getMap("com.sankuai.dzviewscene.dealshelf", USER_CREDIT_PAY_CONFIG,
                    String.class, Maps.newHashMap());
            String bizIdStr = configMap.getOrDefault(BIZ_ID, StringUtils.EMPTY);
            String planIdStr = configMap.getOrDefault(PLAN_ID, StringUtils.EMPTY);
            String signPayStr = configMap.getOrDefault(SIGN_IPH_PAY_MERCHANT_NO, StringUtils.EMPTY);
            UserCreditPayConfig config = new UserCreditPayConfig();
            config.setBizId(bizIdStr);
            config.setPlanId(NumberUtils.toLong(planIdStr, 0));
            config.setSignPayMerchantNo(NumberUtils.toLong(signPayStr, 0));
            return config;
        } catch (Exception e) {
            log.error("getUserCreditPayConfig error", e);
            return null;
        }
    }
}
