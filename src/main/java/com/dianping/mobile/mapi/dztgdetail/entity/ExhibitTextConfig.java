package com.dianping.mobile.mapi.dztgdetail.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: <EMAIL>
 * @Date: 2023/9/1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExhibitTextConfig {
    /**
     * 是否展示"查看全部"文案
     */
    private String showAllText;

    /**
     * 小标题
     */
    private String title;
    /**
     * 箭头图标
     */
    private String arrowIcon;
    /**
     * 美团跳转链接
     */
    private String mtJumpUrl;
    /**
     * 点评跳转链接
     */
    private String dpJumpUrl;
}
