package com.dianping.mobile.mapi.dztgdetail.button.joy;

import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.SaleStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtnIcon;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo.CouponDescItem;
import com.dianping.mobile.mapi.dztgdetail.helper.*;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.CouponAssignStatusEnum;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public class MarketPriceNormalButtonBuilder extends AbstractPriceServiceButtonBuilder {

    protected static final Map<Integer, String> BUTTON_NAME_MAP = ImmutableMap.of(
            BuyBtnTypeEnum.TIMES_CARD.getCode(), "立即抢购",
            BuyBtnTypeEnum.PINTUAN.getCode(), "立即抢购",
            BuyBtnTypeEnum.MEMBER_CARD.getCode(), "团购价",
            BuyBtnTypeEnum.JOY_CARD.getCode(), "团购价"
    );
    protected static final String HAD_COUPON_PREFIX = "用券";
    protected static final String NO_COUPON_PREFIX = "领券";
    protected static final String BUY = "抢购";

    @Override
    public PriceDisplayDTO getPrice(DealCtx context) {
        return PriceHelper.getDealPromoPrice(context);
    }

    @Override
    public void afterBuild(DealCtx context, DealBuyBtn button) {
        buildCouponAB(context, button);
        buildBtnTitle(context, button);
        buildBtnDesc(context, button);
        buildBtnTag(context, button);
        buildSaleStatus(context, button);
        buildBtnEnable(context, button);
        if (DealBuyHelper.isCouponBar(context)) {
            button.setBtnTitle(CouponBarHelper.getCouponBtnTitle(context));
        }
    }

    private void buildBtnEnable(DealCtx context, DealBuyBtn button) {
        if (StringUtils.isNotBlank(context.getSaleStatus())) {
            if (context.getSaleStatus().equals(SaleStatusEnum.SNAP_UP_NOW.saleStatusName)) {
                button.setBtnEnable(true);
            } else {
                button.setBtnEnable(false);
            }
        }
    }

    private void buildSaleStatus(DealCtx context, DealBuyBtn button) {
        //若是预热单，更新title&&设置按钮的售卖状态
        if (StringUtils.isNotBlank(context.getSaleStatus())) {
            button.setSaleStatus(context.getSaleStatus());
        }
    }

    private void buildBtnTag(DealCtx context, DealBuyBtn button) {
        PriceDisplayDTO price = getPrice(context);
        if (price == null || price.getPromoAmount() == null) {
            return;
        }
        if (GreyUtils.isShowMarketPrice(context)) {
            if (button.getDetailBuyType() == BuyBtnTypeEnum.NORMAL_DEAL.getCode()) {
                button.setBtnTag(StringUtils.isNotBlank(price.getShortPromoTag()) ? price.getShortPromoTag() : "共省" + price.getPromoAmount().toPlainString());

                String promoIconName = StringUtils.isNotBlank(price.getShortPromoTag()) ? price.getShortPromoTag() : "共省" + price.getPromoAmount().toPlainString();
                DealBuyBtnIcon newNormalIcon = DealBuyHelper.getPromoIcon(context, promoIconName);
                List<DealBuyBtnIcon> btnIcons = button.getBtnIcons();
                deleteNormalDealBtnIcon(btnIcons);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(btnIcons)) {
                    btnIcons.add(0, newNormalIcon);
                } else {
                    button.setBtnIcons(Lists.newArrayList(newNormalIcon));
                }
                if (DealAttrHelper.isWuyoutong(context) || context.isMarketPriceNormalButtonHide()) {
                    button.setBtnIcons(Lists.newArrayList());
                }
            }
        }
    }

    private void deleteNormalDealBtnIcon(List<DealBuyBtnIcon> btnIcons) {
        if (CollectionUtils.isEmpty(btnIcons)) {
            return;
        }
        for (DealBuyBtnIcon dealBuyBtnIcon : btnIcons) {
            if (dealBuyBtnIcon != null && dealBuyBtnIcon.getType() != null && dealBuyBtnIcon.getType() == BuyBtnTypeEnum.NORMAL_DEAL.getCode()) {
                btnIcons.remove(dealBuyBtnIcon);
                break;
            }
        }
    }

    private void buildBtnDesc(DealCtx context, DealBuyBtn button) {
        if (DealAttrHelper.isWuyoutong(context) || context.isMarketPriceNormalButtonHide()) {
            button.setBtnDesc(StringUtils.EMPTY);
            return;
        }
        button.setBtnDesc(buildMarketPriceDesc(context));
    }

    private void buildBtnTitle(DealCtx context, DealBuyBtn button) {
        //设置预热单button的title
        if (StringUtils.isNotBlank(context.getSaleStatus())) {
            button.setBtnTitle(getSaleStatusButtonTitle(context));
            return;
        }

        if (Objects.equals(context.getEnvCtx().getDztgClientTypeEnum(), DztgClientTypeEnum.DIANPING_BAIDUMAP_MINIAPP)) {
            List<PromoDTO> couponPromos = PromoHelper.getCouponPromos(context);
            if (CollectionUtils.isNotEmpty(couponPromos)) {
                //百度地图小程序抢购按钮逻辑固定为「XX抢购」，任何一张券可用就展示用券抢购
                button.setBtnTitle((couponPromos.stream().anyMatch(item -> !item.isCanAssign()) ? HAD_COUPON_PREFIX : NO_COUPON_PREFIX) + BUY);
                return;
            }
        }

        if (DealAttrHelper.isWuyoutong(context)) {
            button.setBtnTitle(getButtonTitle(context, "立即预约"));
        }

        if (context.getPreButton() == null) {
            return;
        }
        String title = BUTTON_NAME_MAP.get(context.getPreButton().getDetailBuyType());
        if (StringUtils.isNotBlank(title)) {
            button.setBtnTitle(getButtonTitle(context, title));
        }

    }

    protected void buildCouponAB(DealCtx context, DealBuyBtn buyBtn) {
        List<PromoDTO> couponPromos = PromoHelper.getCouponUsePromo(context);
        if (CollectionUtils.isEmpty(couponPromos)) {
            return;
        }
        for (PromoDTO couponPromo : couponPromos) {
            if (couponPromo.getIdentity().getPromoType() != PromoTypeEnum.COUPON.getType() || !PromoHelper.canAssign(couponPromo)) {
                continue;
            }
            if(PromoHelper.isCouponPurchase(couponPromo)){
                continue;
            }
            CouponDescItem coupon = new CouponDescItem();
            coupon.setCouponGroupId((int) couponPromo.getIdentity().getPromoId());
            coupon.setUnifiedcoupongroupids(String.valueOf(couponPromo.getIdentity().getPromoId()));
            buyBtn.setCoupon(coupon);
        }
    }
}
