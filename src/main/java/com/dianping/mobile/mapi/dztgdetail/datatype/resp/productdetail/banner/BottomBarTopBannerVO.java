package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.banner;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.action.extend.none.DoNothingActionVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.banner.enums.BottomBarBannerTypeEnums;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.BottomBarActionDataVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.backgroud.BottomBarBackgroundVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.rich.content.RichContentVO;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2025/3/5 18:28
 */
@Data
public class BottomBarTopBannerVO implements Serializable {

    /**
     * 1=通用样式（目前只有1，以后也可能只有1）
     *
     * @see BottomBarBannerTypeEnums
     */
    private int bannerType = BottomBarBannerTypeEnums.COMMON_TYPE.getCode();

    /**
     * bannerData会根据bannerType改编模型，参考https://km.sankuai.com/collabpage/**********
     */
    private List<RichContentVO> bannerData = new ArrayList<>();

    /**
     * 背景色
     */
    private BottomBarBackgroundVO background;

    /**
     * banner点击事件
     */
    private BottomBarActionDataVO actionData = new DoNothingActionVO();

    public BottomBarTopBannerVO add(RichContentVO richContentVO) {
        if (richContentVO != null) {
            this.bannerData.add(richContentVO);
        }
        return this;
    }

}
