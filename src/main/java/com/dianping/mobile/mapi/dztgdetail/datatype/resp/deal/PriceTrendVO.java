package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023-10-10
 * @desc 价格趋势
 */
@TypeDoc(description = "价格趋势，存储日期和对应价格")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PriceTrendVO {
    @FieldDoc(description = "日期")
    private String date;

    @FieldDoc(description = "价格")
    private BigDecimal price;
}
