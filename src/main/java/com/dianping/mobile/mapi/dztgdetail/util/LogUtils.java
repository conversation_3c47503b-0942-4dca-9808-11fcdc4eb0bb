package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.lion.common.util.JsonUtils;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.constants.TracerContextConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RuleStrategyEnum;
import com.dianping.mobile.mapi.dztgdetail.entity.LogPrintConditionConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.LogPrintStrategy;
import com.fasterxml.jackson.core.type.TypeReference;
import com.maoyan.mtrace.Tracer;
import com.meituan.inf.xmdlog.XMDLogFormat;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-02-27
 * @desc 日志打印工具类
 */
@Slf4j
public class LogUtils {

    private static List<LogPrintConditionConfig> logPrintConditionConfig = LionConfigUtils.getLogPrintConditionConfig();

    static {
        Lion.addConfigListener(LionConstants.APP_KEY, "default", LionConstants.LOG_PRINT_CONDITION_CONFIG, configEvent -> {
            try {
                logPrintConditionConfig = JsonUtils.fromJson(configEvent.getValue(),
                        new TypeReference<List<LogPrintConditionConfig>>() {});
                log.info("[LogUtils] configListener oldValue={}, newValue={}", configEvent.getOldValue(), configEvent.getValue());
            } catch (Exception e) {
                log.error("[LogUtils] JsonUtils.fromJson err={}, oldValue={}, newValue={}", e, configEvent.getOldValue(), configEvent.getValue());
            }
        });
    }

    public static boolean getLogPrintSwitch(List<LogPrintConditionConfig> logPrintConditionConfig) {
        if (CollectionUtils.isEmpty(logPrintConditionConfig)) {
            return false;
        }
        String envName = toString(Environment.getEnvironment());
        String cellName = toString(Environment.getCell());
        String swimlane = toString(Environment.getSwimlane());
        String userIdStr = toString(Tracer.getContext(TracerContextConstants.USER_ID_STR));
        String dztgClientType = toString(Tracer.getContext(TracerContextConstants.DZTG_CLIENT_TYPE));
        String apiName = toString(Tracer.getContext(TracerContextConstants.API_NAME));
        String unionId = toString(Tracer.getContext(TracerContextConstants.UNION_ID));
        String pageSource = toString(Tracer.getContext(TracerContextConstants.PAGE_SOURCE));
        try {
            for (LogPrintConditionConfig rule : logPrintConditionConfig) {
                LogPrintStrategy strategy = rule.getLogPrintStrategy();
                boolean factor1 = matchStrategy(rule.getEnv(), envName, strategy.getEnvStrategy());
                boolean factor2 = matchStrategy(rule.getCell(), cellName, strategy.getCellStrategy());
                boolean factor3 = matchStrategy(rule.getSwimlane(), swimlane, strategy.getSwimlaneStrategy());
                boolean factor4 = matchStrategy(rule.getApiName(), apiName, strategy.getApiNameStrategy());
                boolean factor5 = matchStrategy(rule.getUserIdStr(), userIdStr, strategy.getUserIdAndUnionIdStrategy());
                boolean factor6 = matchStrategy(rule.getUnionId(), unionId, strategy.getUserIdAndUnionIdStrategy());
                boolean factor7 = matchStrategy(rule.getDztgClientType(), dztgClientType, strategy.getDztgClientTypeStrategy());
                boolean factor8 = matchStrategy(rule.getPageSource(), pageSource, strategy.getPageSourceStrategy());
                if (factor1 && factor2 && factor3 && factor4 && factor5 && factor6 && factor7 && factor8) {
                    // 如果打印日志 & 设置了日志采样率 & 用户ID不为空
                    if (rule.isPrintLog() && StringUtils.isNotBlank(userIdStr) && NumbersUtils.greaterThanZero(strategy.getLogRate())) {
                        return Long.parseLong(userIdStr) % 100 < strategy.getLogRate();
                    }
                    return rule.isPrintLog();
                }
            }
        } catch (Exception e) {
            log.error("getLogPrintSwitch err", e);
        }
        return false;
    }

    private static String toString(String str) {
        if (StringUtils.isBlank(str)) {
            return StringUtils.EMPTY;
        }
        return str;
    }

    public static boolean matchStrategy(List<?> objs, Object obj, String strategyName) {
        if (obj == null || StringUtils.isBlank(strategyName)) {
            return false;
        }
        RuleStrategyEnum strategyEnum = RuleStrategyEnum.of(strategyName.toLowerCase());
        switch (strategyEnum) {
            case IGNORE:
                return true;
            case ALLOW:
                return objs.contains(obj);
            case DENY:
                return !objs.contains(obj);
            default:
                return false;
        }
    }

    /**
     * 打印 info 级别日志
     * @param tags 标签
     * @param message 日志内容
     * @param objects 参数
     */
    public static void info(Map<String, String> tags, String message, Object... objects) {
        if (!getLogPrintSwitch(logPrintConditionConfig)) {
            return;
        }
        XMDLogFormat logFormat = XMDLogFormat.build();
        if (MapUtils.isNotEmpty(tags)) {
            message = String.format("%s %s", logFormat.putTags(tags).toString(), message);
        }
        log.info(message, objects);
    }

    /**
     * 打印 info 级别日志
     * @param message 日志内容
     * @param objects 参数
     */
    public static void info(String message, Object... objects) {
        if (!getLogPrintSwitch(logPrintConditionConfig)) {
            return;
        }
        log.info(message, objects);
    }

    /**
     * 打印 error 级别的日志
     * @param ex 异常实例
     * @param tags 标签
     * @param message 日志内容
     * @param objects 参数
     */
    public static void error(Exception ex, Map<String, String> tags, String message, Object... objects) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.util.LogUtils.error(java.lang.Exception,java.util.Map,java.lang.String,java.lang.Object[])");
        if (!getLogPrintSwitch(logPrintConditionConfig)) {
            return;
        }
        XMDLogFormat logFormat = XMDLogFormat.build();
        if (MapUtils.isNotEmpty(tags)) {
            message = String.format("%s %s", logFormat.putTags(tags).toString(), message);
        }
        log.error(message, ex, objects);
    }

    /**
     * 打印 error 级别的日志
     * @param ex 异常实例
     * @param message 日志内容
     * @param objects 参数
     */
    public static void error(Exception ex, String message, Object... objects) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.util.LogUtils.error(java.lang.Exception,java.lang.String,java.lang.Object[])");
        if (!getLogPrintSwitch(logPrintConditionConfig)) {
            return;
        }
        log.error(message, objects, ex);
    }

    /**
     * 打印 error 级别的日志
     * @param message 日志内容
     * @param objects 参数
     */
    public static void error(String message, Object... objects) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.util.LogUtils.error(java.lang.String,java.lang.Object[])");
        if (!getLogPrintSwitch(logPrintConditionConfig)) {
            return;
        }
        log.error(message, objects);
    }

    /**
     * 打印 warn 级别的日志
     * @param tags 标签
     * @param message 日志内容
     * @param objects 参数
     */
    public static void warn(Map<String, String> tags, String message, Object... objects) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.util.LogUtils.warn(java.util.Map,java.lang.String,java.lang.Object[])");
        if (!getLogPrintSwitch(logPrintConditionConfig)) {
            return;
        }
        XMDLogFormat logFormat = XMDLogFormat.build();
        if (MapUtils.isNotEmpty(tags)) {
            message = String.format("%s %s", logFormat.putTags(tags).toString(), message);
        }
        log.warn(message, objects);
    }
}
