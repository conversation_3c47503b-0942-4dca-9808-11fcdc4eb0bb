package com.dianping.mobile.mapi.dztgdetail.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * 2020/3/18 11:13 上午
 */
@Getter
public enum BuyBtnTypeEnum {
    /**
     * 购买按钮类型
     */
    NORMAL_DEAL(1, "普通团单"),
    TIMES_CARD(2, "次卡"),
    MEMBER_CARD(3, "会员卡"),
    PINTUAN(4, "拼团"),
    IDLE_DEAL(5, "闲时单"),
    IDLE_BANNER(6, "闲时单banner"),
    JOY_CARD(7, "玩乐卡"),
    MEMBER(8, "会员专属团购领取会员按钮"),
    RESV_DEAL(9,"预约团单"),
    TRIAL_CLASS(10, "试听课"),
    COST_EFFECTIVE_PINTUAN(11, "特价团购拼团")
    ;

    private final int code;
    private final String desc;

    BuyBtnTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static BuyBtnTypeEnum codeOf(int code) {
        for (BuyBtnTypeEnum value : BuyBtnTypeEnum.values()) {
            if (value.code == code) {
                return value;
            }
        }

        throw new UnsupportedOperationException("BuyBtnTypeEnum has no code of " + code);
    }
}
