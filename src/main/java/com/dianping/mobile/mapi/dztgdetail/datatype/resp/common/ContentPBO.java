package com.dianping.mobile.mapi.dztgdetail.datatype.resp.common;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.SpritePicVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

@TypeDoc(description = "内容类型")
@MobileDo(id = 0xdb32)
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ContentPBO implements Serializable {


    public ContentPBO(int type, String content) {
        this.type = type;
        this.content = content;
    }

    @FieldDoc(description = "类型：0：文本；1：图片；2：视频")
    @MobileField(key = 0x8f0c)
    private int type;

    @FieldDoc(description = "内容：0是普通文本；1为图片地址；2为视频截图地址；")
    @MobileField(key = 0xcce)
    private String content;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @FieldDoc(description = "视频链接")
    @MobileField(key = 0xe654)
    private String videoUrl;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @FieldDoc(description = "描述：2是视频弹窗提醒描述")
    @MobileField(key = 0xfebf)
    private String desc;

    @FieldDoc(description = "容器尺寸")
    @MobileField(key = 0xfc9)
    private String scale;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @FieldDoc(description = "雪碧图")
    @MobileDo.MobileField(key = 0xd103)
    private SpritePicVO spritePic;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @FieldDoc(description = "视频ID")
    @MobileDo.MobileField(key = 0xf00a)
    private String videoId;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @FieldDoc(description = "VR链接")
    @MobileDo.MobileField(key = 0xf3a0)
    private String vrUrl;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @FieldDoc(description = "VR-icon链接")
    @MobileDo.MobileField(key = 0x116d)
    private String vrIconUrl;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getScale() {
        return scale;
    }

    public void setScale(String scale) {
        this.scale = scale;
    }

    public SpritePicVO getSpritePic() {
        return spritePic;
    }

    public void setSpritePic(SpritePicVO spritePic) {
        this.spritePic = spritePic;
    }
}
