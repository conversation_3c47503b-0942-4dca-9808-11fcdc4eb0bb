package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.struct;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;
import java.util.List;

@TypeDoc(description = "到综团单结构化商品条目")
@MobileDo(id = 0xc318)
public class DealStructItemDo implements Serializable {

    @FieldDoc(description = "条目值")
    @MobileDo.MobileField(key = 0x97dd)
    private String value;
    @FieldDoc(description = "条目值数组")
    @MobileDo.MobileField(key = 0xb0ac)
    private List<String> list;

    @FieldDoc(description = "条目名")
    @MobileDo.MobileField(key = 0x7ab8)
    private String name;

    //分组名
    private String groupName;

    @FieldDoc(description = "流程模块类型区分")
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;

    @FieldDoc(description = "流程模块")
    @MobileDo.MobileField(key = 0xf30b)
    private List<ProcessItemDo> processItemDos;

    @FieldDoc(description = "流程模块，该字段与processItemDos数据一样，仅为适配前端增加的字段")
    @MobileDo.MobileField(key = 0xf30b)
    private List<ProcessItemDo> processItems;

    @FieldDoc(description = "提示模块")
    @MobileDo.MobileField(key = 0x4a2f)
    private TipModule tipModule;

    @FieldDoc(description = "标签展示内容")
    @MobileDo.MobileField(key = 0xbf9b)
    private String tag;

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public List<ProcessItemDo> getProcessItemDos() {
        return processItemDos;
    }

    public void setProcessItemDos(List<ProcessItemDo> processItemDos) {
        this.processItemDos = processItemDos;
    }

    public List<ProcessItemDo> getProcessItems() {
        return processItems;
    }

    public void setProcessItems(List<ProcessItemDo> processItems) {
        this.processItems = processItems;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public TipModule getTipModule() {
        return tipModule;
    }

    public void setTipModule(TipModule tipModule) {
        this.tipModule = tipModule;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public List<String> getList() {
        return list;
    }

    public void setList(List<String> list) {
        this.list = list;
    }
}