package com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MtTerm;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@MobileDo(id = 0x7aec)
@Data
public class DealBaseDo implements Serializable {

    @MobileField(key = 0xd8d)
    private String effectSpan;

    @MobileField(key = 0x3a5e)
    private String costSpan;

    @MobileField(key = 0x58a0)
    private int curcityRdCount;

    @MobileField(key = 0x13e5)
    private List<Integer> frontPoiCates;

    @MobileField(key = 0x5a34)
    private int refund;

    @MobileField(key = 0x945f)
    private String showType;

    @MobileField(key = 0x92b9)
    private String bookingPhone;

    @MobileField(key = 0x994)
    private String notice;

    @MobileField(key = 0xb13a)
    private int solds;

    @MobileField(key = 0x22b2)
    private String soldStr;

    @MobileField(key = 0x93b)
    private int id;

    @MobileField(key = 0x640d)
    private String imgUrl;

    @MobileField(key = 0x79b7)
    private int noBooking;

    @MobileField(key = 0xef70)
    private String cate;

    @MobileField(key = 0x36e9)
    private String title;

    @MobileField(key = 0xa2b8)
    private List<MtMenu> menus;

    @MobileField(key = 0x88c4)
    private int redeemType;

    @MobileField(key = 0x207)
    private String howUse;

    @MobileField(key = 0x77a0)
    private double campaignPrice;

    @MobileField(key = 0xc6a4)
    private boolean todayAvailable;

    @MobileField(key = 0x24ae)
    private String squareImgUrl;


    @MobileField(key = 0xc5b5)
    private double price;

    @MobileField(key = 0x9ba7)
    private String channel;

    @MobileField(key = 0x5da8)
    private List<MtTerm> terms;

    @MobileField(key = 0x2820)
    private int status;

    @MobileField(key = 0x6fa3)
    private int dealType;

    @MobileField(key = 0xf41d)
    private int expireAutoRefund;

    @MobileField(key = 0xd65e)
    private String subCate;

    @MobileField(key = 0x144)
    private Date endTime;

    @MobileField(key = 0x5eec)
    private String slug;

    @MobileField(key = 0x1ffd)
    private List<MtPriceCalendar> priceCalendars;

    @MobileField(key = 0x1f8)
    private MtRatingModel ratingModel;

    @MobileField(key = 0x7c03)
    private double originalPrice;

    @MobileField(key = 0xe065)
    private String couponTitle;

    @MobileField(key = 0x4dac)
    private String range;

    @MobileField(key = 0x11a1)
    private double deposit;

    @MobileField(key = 0x52a8)
    private Date startTime;

    @MobileField(key = 0x7610)
    private String brandName;

    @MobileField(key = 0x4f31)
    private MtPoiModel shop;

    @MobileField(key = 0x1dc7)
    private String shopListUrl;

    @MobileField(key = 0x2747)
    private double canBuyPrice;

    @MobileField(key = 0xf4b7)
    private MtDealBuyConfig dealBuyConfig;

    @MobileField(key = 0x93c7)
    private List<MtPromotionInfo> promotionInfos;

    private int dt;
}
