package com.dianping.mobile.mapi.dztgdetail.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024-02-26
 * @desc 线程池名称枚举
 */
@Getter
public enum ThreadPoolNameEnum {
    STYLE_EXECUTOR("styleExecutor"),
    PRICE_EXECUTOR("priceExecutor"),
    PROMO_EXECUTOR("promotionExecutor"),
    DEAL_SHOP_EXECUTOR("dealShopExecutor"),
    DEFAULT_EXECUTOR("defaultExecutor"),
    ;

    final String threadPoolName;

    ThreadPoolNameEnum(String name) {
        this.threadPoolName = name;
    }
}
