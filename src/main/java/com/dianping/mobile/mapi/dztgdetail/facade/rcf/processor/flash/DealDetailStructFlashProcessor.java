package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.flash;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsFlashDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.service.cellarservice.DealDetailStructCacheBizService;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FlashDealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.struct.DealDetailStructModuleDo;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @create 2024/10/17 17:09
 */
public class DealDetailStructFlashProcessor extends AbsFlashDealProcessor {

    @Resource
    DealDetailStructCacheBizService dealDetailStructCacheBizService;

    @Override
    public void prepare(FlashDealCtx ctx) {
        // 从缓存获取 通用样式团购详情
        String key = dealDetailStructCacheBizService.buildKey(ctx.getDealflashReq());
        Future future = dealDetailStructCacheBizService.getCacheValueFuture(key);
        ctx.getFutureCtx().setDealDetailStructFuture(future);
    }

    @Override
    public void process(FlashDealCtx ctx) {
        try {
            Future future = ctx.getFutureCtx().getDealDetailStructFuture();
            if ( Objects.nonNull(future)) {
                DealDetailStructModuleDo result = (DealDetailStructModuleDo) dealDetailStructCacheBizService.getCacheValueResult(future);
                ctx.setDealDetailStruct(result);
            }
        } catch (Exception e) {
            logger.error("DealDetailStructFlashProcessor.process error", e);
        }
    }

}
