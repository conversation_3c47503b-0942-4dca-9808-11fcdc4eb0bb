package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <EMAIL>
 * @Date: 2024/10/28
 */
@Data
@TypeDoc(description = "推荐策略模型")
@MobileDo(id = 0x11d4)
public class RelatedRecommendStrategy implements Serializable {
    @FieldDoc(description = "是否走新推荐策略")
    @MobileDo.MobileField(key = 0x623e)
    private boolean hitNewRecommendStrategy;

    @FieldDoc(description = "是否走新同店推荐策略")
    @MobileDo.MobileField(key = 0xe970)
    private boolean hitInShopRec;

    @FieldDoc(description = "是否走新跨店推荐策略")
    @MobileDo.MobileField(key = 0xcc77)
    private boolean hitCrossShopRec;

    @FieldDoc(description = "ab实验结果")
    @MobileDo.MobileField(key = 0x1b3d)
    private ModuleAbConfig abConfigModel;
}
