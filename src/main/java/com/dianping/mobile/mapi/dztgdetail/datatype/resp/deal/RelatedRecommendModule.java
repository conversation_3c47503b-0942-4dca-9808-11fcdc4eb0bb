package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <EMAIL>
 * @Date: 2024/10/30
 */
@Data
@TypeDoc(description = "推荐团单模型")
@MobileDo(id = 0x5e23)
public class RelatedRecommendModule implements Serializable {
    @FieldDoc(description = "按钮文案")
    @MobileDo.MobileField(key = 0xa5e3)
    private String btnText;

    @FieldDoc(description = "门店跳转链接，需锚定货架")
    @MobileDo.MobileField(key = 0xaba1)
    private String shopJumpUrl;
}
