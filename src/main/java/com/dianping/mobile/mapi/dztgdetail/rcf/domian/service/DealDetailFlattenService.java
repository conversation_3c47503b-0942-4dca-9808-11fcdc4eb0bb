package com.dianping.mobile.mapi.dztgdetail.rcf.domian.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.deal.bff.cache.enums.RcfDealBffInterfaceEnum;
import com.dianping.mobile.mapi.dztgdetail.rcf.api.req.DealNativeSnapshotReq;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.dto.DealBffResponseDTO;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler.flatten.DealModuleFlattenProcessor;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler.flatten.FlattenProcessorFactory;
import com.dianping.mobile.mapi.dztgdetail.rcf.enums.ModuleType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2024/12/27 11:38
 */
@Component
public class DealDetailFlattenService {
    @Resource
    private FlattenProcessorFactory flattenProcessorFactory;

    public void flattenDetail(JSONObject dealModuleResult) {
        JSONArray moduleList = (JSONArray) dealModuleResult.get("moduleList");
        if (moduleList == null || moduleList.isEmpty()) {
            return;
        }
        for (int i = 0; i < moduleList.size(); i++) {
            JSONObject module = (JSONObject) moduleList.get(i);
            String type = (String) module.get("type");
            ModuleType moduleType =  ModuleType.fromType(type);
            rcfRenderSnapshot(dealModuleResult, moduleType);
            DealModuleFlattenProcessor flattenProcessor = flattenProcessorFactory.getFlattenHandler(moduleType);
            if (Objects.isNull(flattenProcessor)) {
                continue;
            }
            // 返回结果增加拍平数组
            JSONArray rcfSkuGroupsModule1Flatten = new JSONArray();
            module.put("rcfSkuGroupsModule1Flatten", rcfSkuGroupsModule1Flatten);
            flattenProcessor.flattenModule(rcfSkuGroupsModule1Flatten, module);
        }
    }

    public void rcfRenderSnapshot(JSONObject dealModuleResult, ModuleType moduleType){
        if (moduleType != null){
            dealModuleResult.put("rcfRenderSnapshot", true);
        }
    }
}
