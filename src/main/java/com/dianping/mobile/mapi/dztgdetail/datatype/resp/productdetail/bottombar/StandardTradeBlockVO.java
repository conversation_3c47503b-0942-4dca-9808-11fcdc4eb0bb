package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.enums.BottomBarBlockTypeEnum;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2025/3/16 14:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StandardTradeBlockVO extends ProductBottomBarBlockVO {

    /**
     * 交易按钮列表
     */
    private List<BaseTradeButtonVO> buttonList;

    @Override
    public int getBlockType() {
        return BottomBarBlockTypeEnum.STANDARD_TRADE_BUTTON_LIST.getCode();
    }

    public static StandardTradeBlockVO buildTwoButtonStyle(BaseTradeButtonVO leftButton, BaseTradeButtonVO rightButton) {
        StandardTradeBlockVO standardTradeBlockVO = new StandardTradeBlockVO();
        standardTradeBlockVO.setButtonList(
                Lists.newArrayList(leftButton, rightButton)
                        .stream()
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList())
        );
        return standardTradeBlockVO;
    }

    public static StandardTradeBlockVO buildSingleButtonStyle(BaseTradeButtonVO button) {
        StandardTradeBlockVO standardTradeBlockVO = new StandardTradeBlockVO();
        standardTradeBlockVO.setButtonList(Lists.newArrayList(button));
        return standardTradeBlockVO;
    }
}