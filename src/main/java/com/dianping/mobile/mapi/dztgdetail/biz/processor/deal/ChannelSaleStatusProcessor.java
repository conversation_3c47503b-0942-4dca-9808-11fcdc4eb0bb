package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.GoodsAllowSellingInfoWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.utils.FaultToleranceUtils;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.SaleChannelAggregationDTO;
import com.sankuai.general.product.query.center.client.dto.SaleChannelDTO;
import com.sankuai.mlive.goods.trade.api.model.GoodsSellingInfoDTO;
import com.sankuai.mlive.goods.trade.api.request.QueryGoodsAllowSellingInfoRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class ChannelSaleStatusProcessor extends AbsDealProcessor{

    @Autowired
    private GoodsAllowSellingInfoWrapper goodsAllowSellingInfoWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        return !RequestSourceEnum.fromTradeSnapshot(ctx.getRequestSource());
    }
    @Override
    public void prepare(DealCtx ctx) {}

    @Override
    public void process(DealCtx ctx) {
        try {
            // 查询是否是直播渠道品
            checkSaleChannel(ctx);
            // 填充goodsTypeId
            ctx.getMLiveInfoVo().setGoodsTypeId(String.valueOf(getGoodsType(ctx)));
            // 查询商品售卖状态
            if (isFromMLive(ctx) && validQueryParam(ctx)){
                // 直播渠道可用
                GoodsSellingInfoDTO goodsSellingInfoDTO = goodsAllowSellingInfoWrapper.getGoodsAllowSellingInfoResponse(buildQueryGoodsAllowSellingInfoRequest(ctx));
                ctx.setMliveSellingInfo(goodsSellingInfoDTO);
            }
        } catch (Exception e) {
            logger.error("queryCenterWrapper.getDealGroupDTO error, req:{}", JSON.toJSONString(ctx.getDealBaseReq()), e);
            ctx.setQueryCenterHasError(true);
            FaultToleranceUtils.addException("queryByDealGroupIds", e);
            Cat.logError(e);
        }
    }

    /**
     * 判断是否是直播间来源
     * @param ctx
     * @return
     */
    private boolean isFromMLive(DealCtx ctx){
        return RequestSourceEnum.LIVE_STREAM.getSource().equals(ctx.getRequestSource()) || ctx.getMLiveId() > 0;
    }

    /**
     * 校验可售状态查询参数
     * @param ctx
     * @return
     */
    private boolean validQueryParam(DealCtx ctx){
       return getGoodsType(ctx) > 0 && ctx.getMLiveId() > 0;
    }
    /**
     * 检查售卖渠道
     * @param ctx
     */
    private void checkSaleChannel(DealCtx ctx){
        DealGroupDTO dealGroupDTO  = ctx.getDealGroupDTO();
        // 如果没有获取到SaleChannelAggregation().getSupportChannels()，则代表支持全渠道
        if (Objects.nonNull(dealGroupDTO)
                && Objects.nonNull(dealGroupDTO.getSaleChannelAggregation())
                &&CollectionUtils.isNotEmpty(dealGroupDTO.getSaleChannelAggregation().getSupportChannels())){
            SaleChannelAggregationDTO saleChannelAggregationDTO = dealGroupDTO.getSaleChannelAggregation();
            List<SaleChannelDTO> supportChannels = saleChannelAggregationDTO.getSupportChannels();
            List<Long> channelNos = supportChannels.stream().map(e->e.getChannelNo()).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(channelNos)){
                // 直播渠道： 直播（非餐）、到综私域直播
                if (channelNos.size()== 1
                        && ( 10003L == channelNos.get(0) || 10017L == channelNos.get(0))){
                    ctx.setMLiveChannel(Boolean.TRUE);
                }
                if (channelNos.size() == 2
                        && channelNos.contains(10003L)
                        && channelNos.contains(10017L)){
                    ctx.setMLiveChannel(Boolean.TRUE);
                }
            }
        }
    }
    private QueryGoodsAllowSellingInfoRequest buildQueryGoodsAllowSellingInfoRequest(DealCtx ctx){
        // 直播场景只能用美团id
        int dealGroupId = ctx.getMtId();
        // 查询售卖状态 Code不为0是返回null
        QueryGoodsAllowSellingInfoRequest queryGoodsAllowSellingInfoRequest = new QueryGoodsAllowSellingInfoRequest();
        queryGoodsAllowSellingInfoRequest.setGoodsId(String.valueOf(dealGroupId));
        // 传入goodstype
        queryGoodsAllowSellingInfoRequest.setGoodsType(getGoodsType(ctx));
        // 传入直播id
        queryGoodsAllowSellingInfoRequest.setLiveId(ctx.getMLiveId());
        return queryGoodsAllowSellingInfoRequest;
    }

    private int getGoodsType(DealCtx ctx){
        Map<String, String> paraMap = getDealParamMap(ctx.getDealParam());
        if (MapUtils.isEmpty(paraMap)){
            return 0;
        }
        String goodsTypeId = paraMap.get("goodsTypeId");
        if (StringUtils.isBlank(goodsTypeId)){
            return 0;
        }
        try {
            return Integer.parseInt(goodsTypeId);
        } catch (Exception e) {
            logger.error("Failed to get goodstype : {}", e);
            return 0;
        }

    }
    private static Map<String, String> getDealParamMap(String dealParam) {
        return GsonUtils.fromJsonString(dealParam, new com.google.gson.reflect.TypeToken<Map<String, String>>() {}.getType());
    }
}
