package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.BestPromoDetailsStyleInfo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.DealGift;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.DztgCouponInfo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.DztgPromoExposureInfoVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.NationalSubsidyInfo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.NationalSubsidyPromotionBar;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.PromoActivityInfoVO;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * https://mobile.sankuai.com/studio/model/info/29693
 */
@Data
@MobileDo(id = 0xfb94)
public class PromoDetailModule implements Serializable {
    /**
     * 价格展示文案
     */
    @MobileField(key = 0x46de)
    private String priceDisplayText;

    /**
     * 0-有价格的场景 1-价格到店咨询的场景
     */
    @MobileField(key = 0xccb)
    private int priceDisplayType;
    /**
     * 描述信息
     */
    @MobileField(key = 0xf4f7)
    private List<DescriptionTag> descriptionTags;
    /**
     * 团购价
     */
    @MobileField(key = 0x9218)
    private String dealGroupPrice;

    /**
     * 预售团购价，团购属性维度的展示型信息
     */
    @MobileField(key = 0x380e)
    private String preSaleDealGroupPrice;

    /**
     * 减后价
     */
    @FieldDoc(name = "优惠模块的最终优惠减后价。", description = "如该字段的前端展示样式不同，可和前端约定展示样式类型。")
    @MobileField(key = 0x7031)
    private String promoPrice;

    /**
     * 价格描述
     */
    @FieldDoc(name = "价格描述", description = "如：健身通专享")
    @MobileField(key = 0xc87d)
    private SimpleContextVO promoPriceDesc;

    /**
     * 价格后缀，如：起
     */
    @FieldDoc(name = "价格后缀，如：起")
    @MobileField(key = 0x3a08)
    private String pricePostfix;

    /**
     * 折扣率
     */
    @MobileField(key = 0x8d01)
    private String discountRate;

    /**
     * 折扣率描述
     */
    @MobileField(key = 0xb6f5)
    private String discountRateDescription;

    /**
     * 立减优惠
     */
    @MobileField(key = 0x1915)
    private String reductionPromo;

    /**
     * 立减优惠详情，用于需要细分不同立减的场景，如商家神券、平台补贴券等。
     */
    @MobileField(key = 0x533d)
    private List<ReductionPromoDetail> reductionPromoDetails;

    /**
     * 优惠详细信息，最优组合明细。
     */
    @MobileField(key = 0x1245)
    private List<DealBestPromoDetail> bestPromoDetails;

    @FieldDoc(name = "氛围条的优惠信息列表", description = "用在团详的氛围条优惠信息，可按照不同行业来进行赋值计算逻辑。如该字段的前端展示样式不同，可和前端约定展示样式类型。")
    @MobileField(key = 0x2b77)
    private List<DealBestPromoDetail> atmosphereBarPromoList;

    @FieldDoc(name = "通用的优惠明细", description = "用在团详的优惠明细弹窗模块，可按照不同行业来进行赋值计算逻辑。如该字段的前端展示样式不同，可和前端约定展示样式类型。")
    @MobileField(key = 0x6534)
    private List<DealBestPromoDetail> generalPromoDetailList;

    /**
     * 券优惠
     */
    @MobileField(key = 0x4eb0)
    private String couponPromo;

    /**
     * 总优惠金额 = 团购价 - 到手价
     */
    @MobileField(key = 0xbd0b)
    private String totalPromo;

    /**
     * 预售优惠金额 = 预售团购价 - 到手价
     */
    @MobileField(key = 0xc475)
    private String presalePromo;

    /**
     * 总优惠金额 = 市场价 - 到手价
     */
    @MobileField(key = 0x1ee6)
    private String marketPricePromo;

    /**
     * 全网低价 = 团购价 - 商家立减 - 商家券
     */
    @MobileField(key = 0x3944)
    private String networkLowestPrice;

    /**
     * 门市价
     */
    @MobileField(key = 0x6242)
    private String marketPrice;

    /**
     * 是否展示门店价，默认不展示
     */
    @MobileField(key = 0xfe97)
    private boolean showMarketPrice;

    /**
     * 是否展示最优优惠组合明细，默认不展示
     */
    @MobileField(key = 0x7c0d)
    private boolean showBestPromoDetails;

    /**
     * 最终的实付价
     */
    @MobileField(key = 0xe949)
    private String finalPrice;

    /**
     * 神券模型
     */
    @MobileField(key = 0xd0a7)
    private InflateCounponDTO inflateCounpon;

    /**
     * 单价，比如100个游戏币50元，单价是0.5元
     */
    @MobileField(key = 0x2252)
    private String perPrice;
    /**
     * 最佳优惠明细组合样式信息
     */
    @MobileField(key = 0xd8)
    private BestPromoDetailsStyleInfo bestPromoDetailsStyleInfo;

    /**
     * 门市价折扣 = 到手价 / 门市价
     */
    @MobileField(key = 0x725e)
    private String marketPromoDiscount;

    /**
     * 门市价折扣 = 到手价 / 门市价 , 除了折扣外后面不会拼接其他内容
     */
    @MobileField(key = 0x8061)
    private String plainMarketPromoDiscount;

    /**
     * 优惠描述
     */
    @MobileField(key = 0xcc07)
    private String promoDesc;

    /**
     * 优惠标签
     */
    @MobileField(key = 0x8186)
    private List<DealPromoTag> promoTags;

    /**
     * 领券组件列表（非曝光券）
     */
    @MobileField(key = 0x5db6)
    private List<DztgCouponInfo> couponList;

    /**
     * 曝光券列表
     */
    @MobileField(key = 0x5bf6)
    private List<DztgPromoExposureInfoVO> exposureList;

    /**
     * 优惠活动列表
     */
    @MobileField(key = 0x4278)
    private List<PromoActivityInfoVO> promoActivityList;


    /**
     * 赠品列表
     */
    @MobileField(key = 0x1a04)
    private List<DealGift> dealGifts;

    /**
     * 国补优惠详细信息
     */
    @MobileDo.MobileField(key = 0x2fd8)
    private NationalSubsidyInfo nationalSubsidyInfo;

    /**
     * 国补领券栏信息
     */
    @MobileDo.MobileField(key = 0x401f)
    private NationalSubsidyPromotionBar nationalSubsidyPromotionBar;

    /**
     * 优惠摘要列表
     */
    @MobileField(key = 0x14c7)
    private List<String> promoAbstractList;

    /**
     * 价格力标签
     */
    @MobileField(key = 0xe583)
    private String priceStrengthDesc;

    @MobileField(key = 0xb820)
    private boolean showPriceCompareEntrance;

    /**
     * 单份价格
     */
    @MobileField(key = 0x3e12)
    private String singlePrice;

    /**
     * 份数
     */
    @MobileField(key = 0xd993)
    private String copies;

    /**
     * 价格前缀，返回示例："单次"
     */
    @MobileField(key = 0x238c)
    private String pricePrefix;

    /**
     * 单次卡价格，返回示例： "￥80"
     */
    @MobileField(key = 0x5385)
    private String singleTimePrice;

    /**
     * 团购次卡C端表达优化单次价格
     */
    @MobileField(key = 0xd00f)
    private String pricePerUnit;

    /**
     * 多次卡总价文案，返回示例： "3次总价￥240"
     */
    @MobileField(key = 0xe37)
    private String multiPrice;

    /**
     * 优惠减负,新老样式切换控制
     */
    @MobileField(key = 0xc1f3)
    private boolean promoNewStyle;

    /**
     * 打码标签价格，活动商品的非交易价格,返回示例 "1?300"
     */
    @MobileField(key = 0x1d48)
    private String maskedPromoPrice;

    /**
     * 活动商品库存
     */
    @MobileField(key = 0xb631)
    private String promoStock;

    /**
     * 团购次卡C端表达优化新老样式控制开关
     */
    @MobileField(key = 0xf962)
    private boolean expressOptimize;

    /**
     * 团购次卡次数单位 /次 或者 /节
     */
    @MobileField(key = 0x42d1)
    private String timesUnit;

}