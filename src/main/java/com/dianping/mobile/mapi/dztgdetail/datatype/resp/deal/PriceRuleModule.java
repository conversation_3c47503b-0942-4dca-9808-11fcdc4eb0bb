package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/11
 */
@Data
@TypeDoc(description = "购物车价格规则模块")
public class PriceRuleModule implements Serializable {

    /**
     * 价格规则模块类型
     * @see com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum
     */
    @MobileField(key = 0x7841)
    private int priceRuleType;

    /**
     * 价格规则信息
     */
    @MobileField(key = 0x1d60)
    private List<String> priceRuleTags;

    /**
     * 价格规则优惠信息
     */
    @MobileField(key = 0xcc07)
    private String promoDesc;

}
