package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailSpecificModuleVO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import lombok.Data;

import java.util.Map;

@Data
public class SpecificModuleCtx {

    private Integer dpDealGroupId;

    private Long dpDealGroupIdLong;

    private DealGroupDTO dealGroupDTO;

    private boolean useQueryCenter;

    private String extraInfo;

    private DealDetailSpecificModuleVO result;
}
