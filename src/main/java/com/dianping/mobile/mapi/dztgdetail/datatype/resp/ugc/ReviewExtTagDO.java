package com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc;


import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/15.
 */
@TypeDoc(description = "用户评价模块扩展类型标签模型")
@MobileDo(id = 0xb80d)
@Data
public class ReviewExtTagDO implements Serializable {

    @FieldDoc(description = "扩展标签类型")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "标签列表")
    @MobileDo.MobileField(key = 0x53c7)
    private List<String> values;
}
