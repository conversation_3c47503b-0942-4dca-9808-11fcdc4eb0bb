package com.dianping.mobile.mapi.dztgdetail.flowdye;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import com.sankuai.mpproduct.idservice.api.enums.BizProductIdType;
import com.sankuai.mpproduct.tagservice.effective.service.PpCostEffectiveTagService;
import com.sankuai.nibscp.common.flow.identify.pojo.FlowDyeSDKContext;
import com.sankuai.nibscp.common.flow.identify.spi.FlowDyeExt;
import com.sankuai.nibscp.common.flow.identify.util.SptDyeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class FlowDyeExtImpl implements FlowDyeExt {

    @Autowired
    private PpCostEffectiveTagService ppCostEffectiveTagService;

    @Override
    public List<SptDyeUtil.DyeTraceParam> parseFlowForDyeing(FlowDyeSDKContext flowDyeSDKContext) {
        if (!validate(flowDyeSDKContext)) {
            return null;
        }

        Map<String, Object> parameterMap = flowDyeSDKContext.getParameterMap();
        DealBaseReq req = (DealBaseReq) parameterMap.get("req");
        EnvCtx ctx = (EnvCtx) parameterMap.get("envCtx");
        SptDyeUtil.DyeTraceParam dyeTraceParamFromUrl;

        //优先走运营后台走配置解析到的
        List<SptDyeUtil.DyeTraceParam>  dyeTraceParamFromConfigs = SptDyeUtil.identify(flowDyeSDKContext.getKey(), flowDyeSDKContext.getIdentifyEnv());
        if(CollectionUtils.isNotEmpty(dyeTraceParamFromConfigs)){
            Cat.logEvent("FlowDye", "config");
            return dyeTraceParamFromConfigs;
        }
        
        dyeTraceParamFromUrl = fromUrlParams(req);

        if (dyeTraceParamFromUrl != null) {
            Cat.logEvent("FlowDye", "url");
            return Lists.newArrayList(dyeTraceParamFromUrl);
        }

        dyeTraceParamFromUrl = fromProductTag(req.getDealgroupid(), ctx.isMt());
        if (dyeTraceParamFromUrl != null) {
            RequestSourceEnum requestSourceEnum = RequestSourceEnum.fromDyeTraceScene(dyeTraceParamFromUrl.getScene());
            req.setPageSource(requestSourceEnum == null ? null : requestSourceEnum.getSource());
        }

        return Lists.newArrayList(dyeTraceParamFromUrl);
    }

    private SptDyeUtil.DyeTraceParam fromProductTag(Integer dealgroupid, boolean isMt) {
        if (!LionConfigUtils.requestSourceFromProductTagEnable()) {
            return null;
        }

        boolean isCostEffective = false;

        try {
            if (isMt) {
                PpCostEffectiveTagService.PlatformProductId platformProductId = PpCostEffectiveTagService.PlatformProductId.builder()
                        .id(Long.valueOf(dealgroupid))
                        .build();
                isCostEffective = ppCostEffectiveTagService.queryCostEffectiveTag(platformProductId);
            } else {
                PpCostEffectiveTagService.BizProductId bizProductId = PpCostEffectiveTagService.BizProductId.builder()
                        .id(Long.valueOf(dealgroupid))
                        .bizProductIdType(BizProductIdType.DP_DEAL_GROUP_ID)
                        .build();
                isCostEffective = ppCostEffectiveTagService.bizQueryCostEffectiveTag(bizProductId);
            }
        } catch (TException e) {
            log.error("ppCostEffectiveTagService err", e);
        }

        return isCostEffective ? SptDyeUtil.DyeTraceParam.ofFlowEntrance(RequestSourceEnum.COST_EFFECTIVE
                .getScene(), null) : null;
    }


    private SptDyeUtil.DyeTraceParam fromUrlParams(DealBaseReq req) {
        String pageSource = req.getPageSource();

        if (RequestSourceEnum.ODP.getSource().equals(pageSource)) {

            Map<String, String> requestExtParams = new HashMap<>();

            if (StringUtils.isNotBlank(req.getExtParam())) {
                requestExtParams = GsonUtils.fromJsonString(req.getExtParam(), new TypeToken<Map<String, String>>() {}.getType());
            }
            String subScene = MapUtils.isEmpty(requestExtParams) ? null : requestExtParams.get("odpChannelType");
            return SptDyeUtil.DyeTraceParam.ofFlowEntrance(RequestSourceEnum.ODP.getScene(),subScene);

        } else if (RequestSourceEnum.COST_EFFECTIVE.getSource().equals(pageSource)) {
            return SptDyeUtil.DyeTraceParam.ofFlowEntrance(RequestSourceEnum.COST_EFFECTIVE.getScene(),null);
        } else if(RequestSourceEnum.LIVE_STREAM.getSource().equals(pageSource)){
            return SptDyeUtil.DyeTraceParam.ofFlowEntrance(RequestSourceEnum.LIVE_STREAM.getScene(),null);
        }

        return null;
    }

    private boolean validate(FlowDyeSDKContext flowDyeSDKContext) {
        if (flowDyeSDKContext == null || MapUtils.isEmpty(flowDyeSDKContext.getParameterMap())) {
            return false;
        }

        Map<String, Object> parameterMap = flowDyeSDKContext.getParameterMap();

        if (parameterMap.get("req") == null || !(parameterMap.get("req") instanceof DealBaseReq)) {
            return false;
        }

        return parameterMap.get("envCtx") != null && parameterMap.get("envCtx") instanceof EnvCtx;
    }

}