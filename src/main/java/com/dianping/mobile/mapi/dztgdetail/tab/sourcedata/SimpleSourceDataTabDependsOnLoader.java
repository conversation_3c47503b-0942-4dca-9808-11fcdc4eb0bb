package com.dianping.mobile.mapi.dztgdetail.tab.sourcedata;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealStockSaleWrapper;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.BaseLoadParam;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.SourceDataHolder;

/**
 * 加载团单tab依赖的数据资源顶层类
 */

@Component
public class SimpleSourceDataTabDependsOnLoader implements Loader {

    @Autowired
    DealGroupWrapper dealGroupWrapper;

    @Autowired
    DealStockSaleWrapper dealStockSaleWrapper;

    @Override
    public void loadBeforeRelate(BaseLoadParam param, SourceDataHolder holder) {

    }

    @Override
    public void loadAfterRelate(BaseLoadParam param, SourceDataHolder holder) {
        holder.setDealBaseFutures(dealGroupWrapper.preBatchQueryDealGroupBase(param.getBaseData().getRelatedDpDealGroupIds()));
        holder.setDealSaleFutures(dealStockSaleWrapper.preShopSceneSalesDisplay(param.getBaseData()));
    }

}