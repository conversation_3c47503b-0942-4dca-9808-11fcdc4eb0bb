package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * 领券组件
 */
@Data
@MobileDo(id = 0x257d)
public class DztgCouponInfo implements Serializable {

    /***
     * "券批次id（发券时需指定券批次）"
     */
    @MobileDo.MobileField(key = 0x2488)
    private Long couponGroupId;

    private String couponId;

    /**
     * 名称，如限【团购】指定商品，JsonLabel格式
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * 抵用券金额，JsonLabel格式
     */
    @MobileDo.MobileField(key = 0xfbe2)
    private String amount;

    /**
     * 面额属性角标 eg: 元、折
     */
    @MobileDo.MobileField(key = 0x3aa)
    private String amountCornerMark;

    /**
     * 券使用时间文案，eg：领取后8天有效 （原逻辑使用的jsonlabel）
     */
    @MobileDo.MobileField(key = 0x12d8)
    private String timeDesc;

    /**
     * 抵用券金额描述，例如满***元 （jsonlabel）
     */
    @MobileDo.MobileField(key = 0x7c03)
    private String amountDesc;

    /**
     * 券类型，0是抵用券，1是投放活动券
     */
    @MobileDo.MobileField(key = 0x7cd6)
    private Integer couponType;

    /**
     * 券来源标签文案：商家券/平台券
     */
    @MobileDo.MobileField(key = 0xbfbb)
    private String sourceTag;

    /**
     * 特定标签文案：神券联盟等
     */
    @MobileDo.MobileField(key = 0xad6c)
    private String specificTag;

    /**
     * 标签url
     */
    @MobileDo.MobileField(key = 0xe40c)
    private String tagUrl;

    /**
     * 按钮
     */
    @MobileDo.MobileField(key = 0x2900)
    private DztgCouponButton promoCouponButton;

    /**
     * 领取状态( 0:可领取 1:已领取)
     */
    @MobileDo.MobileField(key = 0x53f)
    private Integer status;

    /**
     * 标签,已领取金融券使用
     */
    @MobileDo.MobileField(key = 0xc542)
    private String iconText;

    /**
     * 标签url,未领取金融券使用
     */
    @MobileDo.MobileField(key = 0xf39b)
    private String iconUrl;

    /**
     * 唯一键ID
     */
    @MobileDo.MobileField(key = 0xfcf)
    private String serialno;

    @FieldDoc(description = "政府金融券N选1，券包密钥")
    @MobileDo.MobileField(key = 0x37ce)
    private String packageSecretKey;
}

