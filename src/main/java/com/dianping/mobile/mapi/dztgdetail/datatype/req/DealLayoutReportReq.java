package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * @Author: guangyujie
 * @Date: 2024/10/14 18:15
 */
@Data
@TypeDoc(description = "团单主接口请求参数")
@MobileRequest
public class DealLayoutReportReq implements IMobileRequest {

    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID")
    @MobileRequest.Param(name = "dealgroupid")
    private Long dealgroupid;

    @FieldDoc(description = "请求来源")
    @MobileRequest.Param(name = "pagesource")
    private String pagesource;

    @FieldDoc(description = "设备屏幕高度")
    @MobileRequest.Param(name = "deviceheight")
    private Double deviceheight;

    @FieldDoc(description = "布局信息json")
    @MobileRequest.Param(name = "components")
    private String components;

}
