package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2024/3/19
 * @since mapi-dztgdetail-web
 */
@MobileDo(id = 0x7b3f)
public class InflateCounponDTO implements Serializable {

    /**
     * 业务线（区分休闲娱乐，丽人等）
     */
    @MobileDo.MobileField(key = 0x8826)
    private String nibBiz;

    /**
     * 标签类型主题
     */
    @MobileDo.MobileField(key = 0xa23b)
    private String labelTheme;

    /**
     * 展示标签类型 文档：https://km.sankuai.com/collabpage/**********
     */
    @MobileDo.MobileField(key = 0xfc19)
    private String showType;


    /**
     * 券包模块信息
     */
    @MobileDo.MobileField(key = 0xda64)
    private String couponWalletInfo;

    /**
     * 券包id列表
     */
    @MobileDo.MobileField(key = 0x55bf)
    private List<String> couponGroupIdList;

    /**
     * 已领神券列表
     */
    @MobileDo.MobileField(key = 0x22ed)
    private List<CouponItemInfoDTO> couponItems;

    /**
     * 券状态，1-可膨胀，2-已膨胀，3-未购买
     */
    @MobileDo.MobileField(key = 0x3998)
    private String couponStatus;

    /**
     * 最高可膨胀金
     */
    @MobileDo.MobileField(key = 0xb622)
    private String maxInflateMoney;

    /**
     * 券膨胀文案
     */
    @MobileDo.MobileField(key = 0xe399)
    private String couponDesc;

    /**
     * 券logo图片
     */
    @MobileDo.MobileField(key = 0xd010)
    private String logoIcon;

    private List<String> couponIds;


    public List<Integer> getMmcPkgResult() {
        return mmcPkgResult;
    }

    public void setMmcPkgResult(List<Integer> mmcPkgResult) {
        this.mmcPkgResult = mmcPkgResult;
    }

    /**
     * 券组件版本实验结果
     */
    @MobileDo.MobileField(key = 0xfeb9)
    private List<Integer> mmcPkgResult;

    public String getLabelTheme() {
        return labelTheme;
    }

    public void setLabelTheme(String labelTheme) {
        this.labelTheme = labelTheme;
    }

    public List<CouponItemInfoDTO> getCouponItems() {
        return couponItems;
    }

    public void setCouponItems(List<CouponItemInfoDTO> couponItems) {
        this.couponItems = couponItems;
    }

    public String getCouponWalletInfo() {
        return couponWalletInfo;
    }

    public void setCouponWalletInfo(String couponWalletInfo) {
        this.couponWalletInfo = couponWalletInfo;
    }

    public List<String> getCouponGroupIdList() {
        return couponGroupIdList;
    }

    public void setCouponGroupIdList(List<String> couponGroupIdList) {
        this.couponGroupIdList = couponGroupIdList;
    }

    public String getCouponStatus() {
        return couponStatus;
    }

    public void setCouponStatus(String couponStatus) {
        this.couponStatus = couponStatus;
    }

    public String getCouponDesc() {
        return couponDesc;
    }

    public void setCouponDesc(String couponDesc) {
        this.couponDesc = couponDesc;
    }

    public String getLogoIcon() {
        return logoIcon;
    }

    public void setLogoIcon(String logoIcon) {
        this.logoIcon = logoIcon;
    }

    public String getMaxInflateMoney() {
        return maxInflateMoney;
    }

    public void setMaxInflateMoney(String maxInflateMoney) {
        this.maxInflateMoney = maxInflateMoney;
    }

    public String getShowType() {
        return showType;
    }

    public void setShowType(String showType) {
        this.showType = showType;
    }

    public String getNibBiz() {
        return nibBiz;
    }

    public void setNibBiz(String nibBiz) {
        this.nibBiz = nibBiz;
    }

    public List<String> getCouponIds() {
        return couponIds;
    }

    public void setCouponIds(List<String> couponIds) {
        this.couponIds = couponIds;
    }


}
