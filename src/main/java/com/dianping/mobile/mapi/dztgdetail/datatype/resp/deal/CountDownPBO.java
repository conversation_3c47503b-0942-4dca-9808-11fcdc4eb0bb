package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: huqi
 * @Date: 2020/3/12 3:39 下午
 */
@MobileDo(id = 0x8036)
@Data
public class CountDownPBO implements Serializable {
    /**
     * 团单id
     */
    @MobileDo.MobileField(key = 0x9a1c)
    private long dealGroupId;

    /**
     * 标题、文案
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * 背景图
     */
    @MobileDo.MobileField(key = 0x7291)
    private String picUrl;

    /**
     * 预售倒计时描述、时间段
     */
    @MobileDo.MobileField(key = 0xb229)
    private String countDownDesc;

    /**
     * 倒计时
     */
    @MobileDo.MobileField(key = 0x9adb)
    private long countDown;

    /**
     * 类型：1 - 统一大促倒计时， 2 - 统一大促时间段， 3 - 预售倒计时，4 - 大促售价模式
     */
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;

    /**
     * 售价模式下样式（type = 4）
     */
    @MobileDo.MobileField(key = 0xa1b0)
    private ActivityDisplayStyle activityDisplayStyle;

    /**
     * 活动id
     */
    @MobileDo.MobileField(key = 0xe91)
    private String activityId;

}
