package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.base.dto.DealBaseDTO;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.base.dto.DealGroupVideoDTO;
import com.dianping.deal.publishcategory.dto.ChannelDTO;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.lion.facade.LionFacade;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MultiSkuExpBiz;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.entity.SkuSummary;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupChannelDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupImageDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.ReceiptEffectiveDateDTO;
import com.sankuai.general.product.query.center.client.util.DealGroupUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

public class DealGroupProcessor extends AbsDealProcessor {

    @Autowired
    private DealGroupWrapper dealGroupWrapper;

    @Autowired
    private DouHuBiz douHuBiz;

    @Autowired
    private MultiSkuExpBiz multiSkuExpBiz;

    // 替换为multi.sku.deal.category.servicetype.whitelist
//    private static final String DEAL_CATEGORY = "multi.sku.deal.category.whitelist";

    @Override
    public void prepare(DealCtx ctx) {
        if(GreyUtils.enableQueryCenterForMainApi(ctx)) {
            return;
        } else {
            Future channelFuture = dealGroupWrapper.preDealGroupChannelById(ctx.getDpId());
            Future dealGroupFuture = dealGroupWrapper.preDealGroupBase(ctx.getDpId());
            Future attrFuture = dealGroupWrapper.preAttrs(ctx.getDpId(), getAttrNames());

            ctx.getFutureCtx().setChannelFuture(channelFuture);
            ctx.getFutureCtx().setDealGroupFuture(dealGroupFuture);
            ctx.getFutureCtx().setAttrFuture(attrFuture);
        }
    }

    private List<String> getAttrNames() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealGroupProcessor.getAttrNames()");
        Map<String, String> configMap = LionFacade
                .getMap(LionConstants.DISABLE_BUY_AND_SHARE_ATTR, String.class, String.class, Collections.emptyMap());
        ArrayList<String> result = Lists.newArrayList(DealAttrHelper.REQUIRE_ATTRIBUTES);
        result.addAll(configMap.keySet());
        return result;
    }

    @Override
    public void process(DealCtx ctx) {
        if(GreyUtils.enableQueryCenterForMainApi(ctx)) {
            ctx.setDealGroupBase(trans2OldBaseDTO(ctx.getDealGroupDTO()));
            ctx.setChannelDTO(trans2OldChannelDTO(ctx.getDealGroupDTO()));
            ctx.setAttrs(trans2OldAttrDTO(ctx.getDealGroupDTO()));
        } else {
            ctx.setDealGroupBase(dealGroupWrapper.getFutureResult(ctx.getFutureCtx().getDealGroupFuture()));
            ctx.setChannelDTO(dealGroupWrapper.getFutureResult(ctx.getFutureCtx().getChannelFuture()));
            ctx.setAttrs(dealGroupWrapper.getFutureResult(ctx.getFutureCtx().getAttrFuture()));
        }

        // 因为依赖团购类目信息做过滤，为降低请求数量，将请求前置到category处
        String serviceType = getServiceType(ctx);
        if (!LionConfigUtils.isUseMultiSku(ctx.getCategoryId(), serviceType)) {
            return;
        }
        SkuSummary skuSummary = multiSkuExpBiz.getSkuSummaryFromCacheWithFailOver(ctx.getDpId());
        if (skuSummary != null) {
            ctx.setSkuSummary(skuSummary);
        }
    }

    private String getServiceType(DealCtx ctx) {
        return ctx.getDealGroupDTO() == null || ctx.getDealGroupDTO().getCategory() == null
                ? null : ctx.getDealGroupDTO().getCategory().getServiceType();
    }

    private DealGroupBaseDTO trans2OldBaseDTO(DealGroupDTO newDealGroup) {
        if(newDealGroup == null) {
            return null;
        }
        DealGroupBaseDTO oldDealGroup = new DealGroupBaseDTO();
        if (newDealGroup.getBasic() != null) {
            oldDealGroup.setProductTitle(newDealGroup.getBasic().getTitle());
            oldDealGroup.setDealGroupShortTitle(newDealGroup.getBasic().getBrandName());
            oldDealGroup.setDealGroupTitleDesc(newDealGroup.getBasic().getTitleDesc());
            oldDealGroup.setBeginDate(DealGroupUtils.convertString2Date(newDealGroup.getBasic().getBeginSaleDate()));
            oldDealGroup.setEndDate(DealGroupUtils.convertString2Date(newDealGroup.getBasic().getEndSaleDate()));
            if (null == newDealGroup.getBasic().getStatus()) {
                Cat.logEvent("NullPointer", "newDealGroup.getBasic().getStatus() is null");
            }
            oldDealGroup.setStatus(null == newDealGroup.getBasic().getStatus() ? 0 : newDealGroup.getBasic().getStatus());
            if (null == newDealGroup.getBasic().getSaleChannel()) {
                Cat.logEvent("NullPointer", "newDealGroup.getBasic().getSaleChannel() is null");
            }
            oldDealGroup.setSaleChannel(null == newDealGroup.getBasic().getSaleChannel() ? 0 : newDealGroup.getBasic().getSaleChannel());
            oldDealGroup.setSourceId(null == newDealGroup.getBasic().getSourceId() ? 0 : newDealGroup.getBasic().getSourceId());
        }

        if (newDealGroup.getImage() != null) {
            oldDealGroup.setDefaultPic(newDealGroup.getImage().getDefaultPicPath());
            oldDealGroup.setDealGroupPics(newDealGroup.getImage().getAllPicPaths());
            if (StringUtils.isNotBlank(newDealGroup.getImage().getVideoPath())) {
                DealGroupVideoDTO headVideo = new DealGroupVideoDTO();
                headVideo.setVideoPath(newDealGroup.getImage().getVideoPath());
                headVideo.setVideoCoverPath(newDealGroup.getImage().getVideoCoverPath());
                headVideo.setSize(Optional.of(newDealGroup.getImage()).map(DealGroupImageDTO::getVideoSize).map(Long::intValue).orElse(0));
                oldDealGroup.setHeadVideo(headVideo);
            }
        }
        if (newDealGroup.getPrice() != null) {
            oldDealGroup.setDealGroupPrice(DealGroupUtils.convertPrice(newDealGroup.getPrice().getSalePrice()));
            oldDealGroup.setMarketPrice(DealGroupUtils.convertPrice(newDealGroup.getPrice().getMarketPrice()));
        }

        oldDealGroup.setFinishDate(DealGroupUtils.convertString2Date(
                getAttr(newDealGroup,"product_finish_date")));

        if (newDealGroup.getRule() != null) {
            DealGroupRuleDTO rule = newDealGroup.getRule();
            if (rule.getBuyRule() != null) {
                oldDealGroup.setMaxPerUser(parseInt(newDealGroup.getRule().getBuyRule().getMaxPerUser()));
                oldDealGroup.setMinPerUser(parseInt(newDealGroup.getRule().getBuyRule().getMinPerUser()));
            }

            if (rule.getRefundRule() != null) {
                oldDealGroup.setAutoRefundSwitch(newDealGroup.getRule().getRefundRule().getSupportRefundType());
                oldDealGroup.setOverdueAutoRefund(newDealGroup.getRule().getRefundRule().isSupportOverdueAutoRefund());
            }
        }

        oldDealGroup.setDeals(null);
        oldDealGroup.setDealGroupId(Math.toIntExact(newDealGroup.getDpDealGroupId()));
        oldDealGroup.setPublishStatus(1);
        oldDealGroup.setDealGroupType(parseInt(getAttr(newDealGroup,"product_business_type")));
        oldDealGroup.setCanUseCoupon(parseBoolean(getAttr(newDealGroup,"product_can_use_coupon")));
        oldDealGroup.setThirdPartVerify(parseBoolean(getAttr(newDealGroup,"product_third_party_verify")));
        oldDealGroup.setPayChannelIDAllowed(parseInt(getAttr(newDealGroup,"product_channel_id_allowed")));
        oldDealGroup.setDiscountRuleID(parseInt(getAttr(newDealGroup,"product_discount_rule_id")));
        oldDealGroup.setBlockStock(parseBoolean(getAttr(newDealGroup,"product_block_stock")));
        oldDealGroup.setSalePlatform(null == newDealGroup.getBasic().getSalePlatform() ? 0 : newDealGroup.getBasic().getSalePlatform());

        if (CollectionUtils.isNotEmpty(newDealGroup.getDeals())) {
            List<DealBaseDTO> dealBaseDTOList = newDealGroup.getDeals().stream().map(deal -> {
                DealBaseDTO dealBaseDTO = new DealBaseDTO();
                dealBaseDTO.setDealId(deal.getDealIdInt());
                dealBaseDTO.setDealGroupId(newDealGroup.getDpDealGroupIdInt());
                if (deal.getBasic() != null) {
                    dealBaseDTO.setShortTitle(deal.getBasic().getTitle());
                    dealBaseDTO.setThirdPartyId(Math.toIntExact(deal.getBasic().getThirdPartyId() == null ? 0 : deal.getBasic().getThirdPartyId()));
                    dealBaseDTO.setDealStatus(deal.getBasic().getStatus() == null ? 0 : deal.getBasic().getStatus());
                }
                if (deal.getPrice() != null) {
                    dealBaseDTO.setPrice(DealGroupUtils.convertPrice(deal.getPrice().getSalePrice()));
                    dealBaseDTO.setMarketPrice(DealGroupUtils.convertPrice(deal.getPrice().getMarketPrice()));
                }

                if (Optional.of(newDealGroup).map(DealGroupDTO::getRule).map(DealGroupRuleDTO::getUseRule).isPresent()) {
                    ReceiptEffectiveDateDTO receiptEffectiveDate = newDealGroup.getRule().getUseRule().getReceiptEffectiveDate();
                    dealBaseDTO.setReceiptBeginDate(DealGroupUtils.convertString2Date(receiptEffectiveDate.getReceiptBeginDate()));
                    dealBaseDTO.setReceiptEndDate(DealGroupUtils.convertString2Date(receiptEffectiveDate.getReceiptEndDate()));
                    dealBaseDTO.setReceiptDateType(receiptEffectiveDate.getReceiptDateType());
                    dealBaseDTO.setReceiptValidDays(receiptEffectiveDate.getReceiptValidDays());
                }
                dealBaseDTO.setReceiptType(parseInt(getAttr(deal, "sku_receipt_type")));
                dealBaseDTO.setDeliverType(parseInt(getAttr(newDealGroup, "product_purchase_process_type")));

                return dealBaseDTO;
            }).collect(Collectors.toList());

            oldDealGroup.setDeals(dealBaseDTOList);
        }

        return oldDealGroup;

    }

    private List<AttributeDTO> trans2OldAttrDTO(DealGroupDTO dealGroupDTO) {
        if(dealGroupDTO == null || dealGroupDTO.getAttrs() == null) {
            return new ArrayList<>();
        }

        return dealGroupDTO.getAttrs().stream().map(attrDTO -> {
            AttributeDTO attributeDTO = new AttributeDTO();
            attributeDTO.setName(attrDTO.getName());
            attributeDTO.setValue(attrDTO.getValue());
            attributeDTO.setSource(attrDTO.getSource());

            return attributeDTO;
        }).collect(Collectors.toList());
    }

    public com.dianping.deal.publishcategory.dto.DealGroupChannelDTO trans2OldChannelDTO(DealGroupDTO dealGroupDTO) {
        if(dealGroupDTO == null || dealGroupDTO.getChannel() == null) {
            return null;
        }
        com.dianping.deal.publishcategory.dto.DealGroupChannelDTO oldDto = new com.dianping.deal.publishcategory.dto.DealGroupChannelDTO();
        DealGroupChannelDTO dealGroupChannelDTO = dealGroupDTO.getChannel();

        com.dianping.deal.publishcategory.dto.ChannelDTO channelDTO = new ChannelDTO();
        channelDTO.setChannelId(dealGroupChannelDTO.getChannelId());
        channelDTO.setChannelEn(dealGroupChannelDTO.getChannelEn());
        channelDTO.setChannelCn(dealGroupChannelDTO.getChannelCn());
        channelDTO.setChannelGroupId(dealGroupChannelDTO.getChannelGroupId());
        channelDTO.setChannelGroupEn(dealGroupChannelDTO.getChannelGroupEn());
        channelDTO.setChannelGroupCn(dealGroupChannelDTO.getChannelGroupCn());

        oldDto.setChannelDTO(channelDTO);
        oldDto.setDealGroupId(dealGroupDTO.getDpDealGroupIdInt());
        oldDto.setCategoryId(dealGroupDTO.getCategory() == null ? 0 : Math.toIntExact(dealGroupDTO.getCategory().getCategoryId()));

        return oldDto;
    }

    private String getAttr(DealGroupDTO dealGroupDTO, String attrName) {
        if(dealGroupDTO.getAttrs() == null) {
            return "";
        }
        return dealGroupDTO.getAttrs()
                .stream()
                .filter(Objects::nonNull)
                .filter(attrDTO -> Objects.equals(attrDTO.getName(), attrName))
                .findAny()
                .map(AttrDTO::getValue)
                .flatMap(list -> list.stream().findFirst())
                .orElse("");
    }

    private String getAttr(DealGroupDealDTO dealDTO, String attrName) {
        if(dealDTO.getAttrs() == null) {
            return "";
        }
        return dealDTO.getAttrs()
                .stream()
                .filter(attrDTO -> attrDTO.getName().equals(attrName))
                .findAny()
                .map(AttrDTO::getValue)
                .flatMap(list -> list.stream().findFirst())
                .orElse("");
    }

     private int parseInt(Integer integer){
        if (integer != null) {
            return integer;
        } else {
            return 0;
        }
    }

     private int parseInt(String string){
        if (StringUtils.isNumeric(string)) {
            return Integer.parseInt(string);
        } else {
            return 0;
        }
    }

    private boolean parseBoolean(String string){
        if (StringUtils.isBlank(string)) {
            return false;
        } else {
            return Boolean.parseBoolean(string);
        }
    }

}
