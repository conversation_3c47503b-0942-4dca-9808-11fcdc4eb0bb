package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.DecryptedField;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

import static com.sankuai.sig.botdefender.core.enums.AssetIdType.SHOP_UUID;

@Data
@TypeDoc(description = "关联团单入参")
@MobileRequest
public class RelatedDealsReq implements IMobileRequest, Serializable {
    /**
     * @Deprecated use stringDealGroupId instead
     * 团单id，原int类型
     */
    @Deprecated
    @MobileRequest.Param(name = "dealGroupId")
    private Integer dealGroupId;

    /**
     * 团单id，string类型
     */
    @MobileRequest.Param(name = "stringDealGroupId")
    private String stringDealGroupId;

    /**
     * 门店id
     */
    @Deprecated
    @MobileRequest.Param(name = "shopId")
    private Integer shopId;
    @MobileRequest.Param(name = "shopIdEncrypt")
    @DecryptedField(targetFieldName = "shopId")
    private String shopIdEncrypt;

    @MobileRequest.Param(name = "shopidstr")
    private String shopIdStr;
    @MobileRequest.Param(name = "shopIdStrEncrypt")
    @DecryptedField(targetFieldName = "shopIdStr")
    private String shopIdStrEncrypt;

    @MobileRequest.Param(name = "shopUuid")
    private String shopUuid;
    @MobileRequest.Param(name = "shopUuidEncrypt")
    @DecryptedField(targetFieldName = "shopUuid", assetIdType = SHOP_UUID)
    private String shopUuidEncrypt;

    /**
     * 城市id
     */
    @MobileRequest.Param(name = "cityId")
    private Integer cityId;

    /**
     * mrn版本
     */
    @MobileRequest.Param(name = "mrnversion")
    private String mrnversion;

    public long getShopIdLong() {
        if(StringUtils.isNumeric(shopIdStr)) {
            return Long.parseLong(shopIdStr);
        } else if(shopId != null) {
            return shopId.longValue();
        } else {
            return 0L;
        }
    }

}