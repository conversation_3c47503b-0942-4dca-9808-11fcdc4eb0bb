package com.dianping.mobile.mapi.dztgdetail.tab;

import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.*;
import com.dianping.mobile.mapi.dztgdetail.tab.match.DealAttrsBasedMatcher;
import com.dianping.mobile.mapi.dztgdetail.tab.match.DealAttrsMatcher;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.Future;

@Service
public class DealAttrsBasedRelateDeals extends AbstractRelateDeals implements DealAttrsBasedMatcher {

    @Autowired
    DealAttrsMatcher dealAttrsMatcher;

    @Override
    public List<Integer> identifyByPublishCategory() {
        return Arrays.asList(402, 1501, 1502, 1503, 1504, 1505, 1506, 1507, 1508, 1509, 1510, 1511, 1512, 1513,
                2001, 2002, 2003, 2004, 2005, 2006, 2007);
    }

    @Override
    protected SourceDataHolder getSourceDataHolder() {
        return new DealAttrsSourceDataHolder();
    }

    @Override
    public void loadBeforeRelate(BaseLoadParam param, SourceDataHolder holder) {
        super.loadBeforeRelate(param, holder);

        DealAttrsSourceDataHolder attrsSourceDataHolder = (DealAttrsSourceDataHolder) holder;

        Future dealAttrFuture = dealGroupWrapper.preGetDealGroupAttrs(param.getBaseData().getDpDealGroupIds(), dealAttrsMatched());
        attrsSourceDataHolder.setDealAttrMap(dealGroupWrapper.getDealGroupAttrs(dealAttrFuture));
    }

    @Override
    public void loadAfterRelate(BaseLoadParam param, SourceDataHolder holder) {

        DealAttrsSourceDataHolder attrHolder = (DealAttrsSourceDataHolder) holder;

        super.loadAfterRelate(param, holder);
        Future dealAttrFuture = dealGroupWrapper.preGetDealGroupAttrs(
                param.getBaseData().getRelatedDpDealGroupIds(),
                dealAttrsToLoadAfterRelate());

        attrHolder.setDealBaseMap(dealGroupWrapper.getBatchQueryDealGroupBaseResult(attrHolder.getDealBaseFutures()));
        attrHolder.setDealSaleMap(dealStockSaleWrapper.getShopSceneSalesDisplay(attrHolder.getDealSaleFutures()));
        attrHolder.setDealAttrMap(dealGroupWrapper.getDealGroupAttrs(dealAttrFuture));

    }

    protected List<String> dealAttrsToLoadAfterRelate() {
        return Collections.singletonList("service_type");
    }

    @Override
    protected List<Integer> getRelatedDpDealGroupIds(BaseData baseData, SourceDataHolder holder) {

        DealAttrsSourceDataHolder attrsSourceDataHolder = (DealAttrsSourceDataHolder) holder;
        int currentDpGroupId = baseData.getCurrentDpGroupId();
        Map<Integer, List<AttributeDTO>> dealAttrMap = attrsSourceDataHolder.getDealAttrMap();

        if (MapUtils.isEmpty(dealAttrMap) || CollectionUtils.isEmpty(dealAttrMap.get(currentDpGroupId))) {
            return null;
        }

        List<String> currentDealFeatures = getFeatureAttrs(dealAttrMap.get(currentDpGroupId), dealAttrsMatched());

        List<Integer> relatedDpDealGroupIds = new ArrayList<>();

        for (Map.Entry<Integer, List<AttributeDTO>> entry : dealAttrMap.entrySet()) {
            Integer dpDealGroupId = entry.getKey();
            List<AttributeDTO> attrs = entry.getValue();

            if (match(currentDealFeatures, getFeatureAttrs(attrs, dealAttrsMatched()))) {
                relatedDpDealGroupIds.add(dpDealGroupId);
            }
        }

        return relatedDpDealGroupIds;
    }

    @Override
    protected DealTabHolder doListRelatedDealTabs(BaseData baseData, SourceDataHolder holder) {

        DealAttrsSourceDataHolder attrHolder = (DealAttrsSourceDataHolder) holder;
        Map<Integer, DealGroupBaseDTO> dealBaseMap = attrHolder.getDealBaseMap();
        Map<Integer, List<AttributeDTO>> dealAttrMap = attrHolder.getDealAttrMap();
        int currentDpGroupId = baseData.getCurrentDpGroupId();

        if (MapUtils.isEmpty(dealAttrMap) || CollectionUtils.isEmpty(dealAttrMap.get(currentDpGroupId))) {
            return null;
        }

        List<DealTab> relatedDealTabs = new ArrayList<>();
        DealTab currentTab = null;

        for (Map.Entry<Integer, List<AttributeDTO>> entry : dealAttrMap.entrySet()) {
            Integer dpDealGroupId = entry.getKey();
            List<AttributeDTO> attrs = entry.getValue();

            DealTab tab = buildDealTab(dpDealGroupId, getFeatureAttrs(attrs, dealAttrsMatched()), dealBaseMap.get(dpDealGroupId), attrs);

            if (tab == null) {
                continue;
            }

            if (dpDealGroupId == currentDpGroupId) {
                currentTab = tab;
            } else {
                relatedDealTabs.add(tab);
            }
        }

        if (currentTab == null) {
            return null;
        }

        DealTabHolder dealTabHolder = new DealTabHolder();
        dealTabHolder.setCurrentTab(currentTab);
        dealTabHolder.setRelatedTabs(relatedDealTabs);

        return dealTabHolder;
    }

    private DealTab buildDealTab(Integer dpDealGroupId, List<String> featureAttrs, DealGroupBaseDTO base, List<AttributeDTO> attrs) {
        if (base == null) {
            return null;
        }

        DealTab tab = new DealTab();
        tab.setDealGroupId(dpDealGroupId);
        tab.setSalePriceWithUnit(base.getDealGroupPrice());
        String tag = genTag(featureAttrs, attrs);
        tab.setTag(tag);

        return tab;
    }

    protected String genTag(List<String> featureAttrs, List<AttributeDTO> attrs) {

        if (CollectionUtils.isEmpty(featureAttrs)) {
            return "";
        }

        return featureAttrs.get(0);
    }

    /**
     *
     * @param currentDealAttrs
     * @param dealAttrsMatched
     * @return
     */
    private List<String> getFeatureAttrs(List<AttributeDTO> currentDealAttrs, List<String> dealAttrsMatched) {
        List<String> features = new ArrayList<>();

        for (AttributeDTO attr : currentDealAttrs) {
            if (dealAttrsMatched.contains(attr.getName())) {
                features.addAll(attr.getValue());
            }
        }

        return features;
    }

    @Override
    public List<String> dealAttrsMatched() {
        return Collections.singletonList("service_type");
    }

    @Override
    public boolean match(List<String> a, List<String> b) {
        return dealAttrsMatcher.match(a, b);
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    private static class DealAttrsSourceDataHolder extends SourceDataHolder {
        private Map<Integer, List<AttributeDTO>> dealAttrMap;
    }

}
