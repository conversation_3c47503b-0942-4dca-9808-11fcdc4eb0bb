package com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ResultList;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-08-28
 */
@TypeDoc(description = "沉浸页图片展示层对象")
@MobileDo(id = 0x695)
@Setter
@Getter
public class ImmersiveImageVO extends ResultList {
    @FieldDoc(description = "款式列表页配置")
    @MobileDo.MobileField(key = 0x42da)
    private ExhibitListConfigVO exhibitListConfig;

    @FieldDoc(description = "款式资源属性，包含款式资源路径、款式名称、款式标签等")
    @MobileDo.MobileField(key = 0xe23d)
    private List<ExhibitImageItemVO> items;

    @FieldDoc(description = "款式列表页标题下方描述文本")
    @MobileDo.MobileField(key = 0x3bfd)
    private String descText;

    @FieldDoc(description = "款式列表页标题下方icon")
    @MobileDo.MobileField(key = 0xc237)
    private String descIcon;

    @FieldDoc(description = "款式列表页标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "推荐款式标题")
    @MobileDo.MobileField(key = 0xfc9e)
    private String styleTitle;

    @FieldDoc(description = "款式展示风格", rule = "1=单图展示；2=多图展示")
    @MobileDo.MobileField(key = 0x4736)
    private int itemDisplayStyle;

    @FieldDoc(description = "查看全部文案")
    @MobileDo.MobileField(key = 0xb075)
    private String showAllText;

    @FieldDoc(description = "查看更多本团购参考款式icon")
    @MobileDo.MobileField(key = 0x145c)
    private String moreStyleIcon;

    @FieldDoc(description = "查看更多本团购参考款式")
    @MobileDo.MobileField(key = 0x1f07)
    private String moreStyleText;

    @FieldDoc(description = "跳转频道页引导模块")
    @MobileDo.MobileField(key = 0xc563)
    private ChannelNailStyleVO channelNailStyle;

    @FieldDoc(description = "款式沉浸页滑动切换到下一模块时展示的文案和icon")
    @MobileDo.MobileField(key = 0x42e2)
    private ActionTextVO actionText;

    @FieldDoc(description = "ab实验")
    @MobileDo.MobileField(key = 0xbae3)
    private List<ModuleAbConfig> moduleAbConfigs;

    @FieldDoc(description = "添加购物车的链接")
    @MobileDo.MobileField(key = 0x622b)
    private String addShopCarUrl;

    @FieldDoc(description = "提单跳链")
    @MobileDo.MobileField(key = 0x990b)
    private String submitOrderUrl;

    @FieldDoc(description = "到手价")
    @MobileDo.MobileField(key = 0xe949)
    private Double finalPrice;

    @FieldDoc(description = "购买按钮的文案")
    @MobileDo.MobileField(key = 0x2b8a)
    private String submitOrderText;

    @FieldDoc(description = "添加购物车的icon")
    @MobileDo.MobileField(key = 0x76ce)
    private String addShopCarIcon;

    @FieldDoc(description = "服务类型id")
    @MobileDo.MobileField(key = 0x4fd9)
    private Long serviceTypeId;

    @FieldDoc(description = "门店类型id", rule = "1=穿戴甲专卖店、2=穿戴甲寄售店")
    @MobileDo.MobileField(key = 0xe045)
    private Integer shopCategoryId;
}
