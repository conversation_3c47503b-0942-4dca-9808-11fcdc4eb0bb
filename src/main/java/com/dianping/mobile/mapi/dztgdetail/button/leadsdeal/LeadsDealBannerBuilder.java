package com.dianping.mobile.mapi.dztgdetail.button.leadsdeal;

import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BannerTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBanner;
import com.dianping.mobile.mapi.dztgdetail.entity.LeadsDealBarConfig;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.richText.RichText;
import com.dianping.mobile.mapi.dztgdetail.util.richText.TextStyle;
import com.google.common.collect.Lists;
import com.sankuai.clr.content.process.gateway.thrift.dto.leads.LoadLeadsInfoRespDTO;
import com.sankuai.clr.content.process.thrift.dto.leads.dto.LeadsPromotionDTO;
import com.sankuai.clr.content.process.thrift.dto.leads.dto.LeadsSalesDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * @author: wuwenqiang
 * @create: 2024-08-29
 * @description: 留资型行业banner横幅
 */
public class LeadsDealBannerBuilder extends AbstractButtonBuilder {

    private static final String IN_STORE_GIFT = "到店礼";

    @Override
    protected void doBuild(DealCtx context, ButtonBuilderChain chain) {
        buildBanner(context);
        chain.build(context);
    }

    private void buildBanner(DealCtx context) {
        LeadsDealBarConfig buyBarConfig = LionConfigUtils.getLeadsDealBarConfig(context);
        if (Objects.isNull(buyBarConfig)) {
            return;
        }
        // 商家没有预约权益则直接返回
        if (!DealUtils.hasResvBenefits(context)) {
            return;
        }
        LoadLeadsInfoRespDTO leadsInfo = context.getLeadsInfo();
        String giftText = getLeadsGiftText(leadsInfo, buyBarConfig);
        if (StringUtils.isBlank(giftText)) {
            return;
        }
        // 构造banner
        DealBuyBanner dealBuyBanner = buildBanner(buyBarConfig, giftText);
        context.getBuyBar().setBuyBanner(dealBuyBanner);
    }

    private String getLeadsGiftText(LoadLeadsInfoRespDTO leadsInfo, LeadsDealBarConfig buyBarConfig) {
        if (Objects.isNull(leadsInfo) || CollectionUtils.isEmpty(leadsInfo.getPromotions())) {
            return StringUtils.EMPTY;
        }
        List<LeadsPromotionDTO> promotionDTOList = leadsInfo.getPromotions();
        for (LeadsPromotionDTO promotionDTO : promotionDTOList) {
            // 仅处理到店礼文案，且仅取第一个
            List<LeadsSalesDTO> leadsSalesDTOS = promotionDTO.getSales();
            LeadsSalesDTO bookGiftSalesDTO = leadsSalesDTOS.stream()
                    .filter(sale -> Objects.nonNull(sale) && Objects.equals(IN_STORE_GIFT, sale.getGiftKey()))
                    .findFirst().orElse(null);
            if (Objects.isNull(bookGiftSalesDTO)) {
                continue;
            }
            return extractGiftText(bookGiftSalesDTO, buyBarConfig);
        }
        return StringUtils.EMPTY;
    }

    private String extractGiftText(LeadsSalesDTO leadsSalesDTO, LeadsDealBarConfig buyBarConfig) {
        if (Objects.isNull(leadsSalesDTO)) {
            return StringUtils.EMPTY;
        }
        // 价格和名称同时存在
        if (StringUtils.isNotBlank(leadsSalesDTO.getGiftName())
                && StringUtils.isNotBlank(leadsSalesDTO.getGiftPrice())
                && StringUtils.isNotBlank(buyBarConfig.getNewLeadsGiftsTemplate())) {
            String price = formatPriceWithHalfUp(new BigDecimal(leadsSalesDTO.getGiftPrice()));
            return String.format(buyBarConfig.getNewLeadsGiftsTemplate(), price, leadsSalesDTO.getGiftName());
        }
        // 仅有名称存在
        else if (StringUtils.isNotBlank(leadsSalesDTO.getGiftName())
                && StringUtils.isNotBlank(buyBarConfig.getOldLeadsGiftsTemplate())) {
            return String.format(buyBarConfig.getOldLeadsGiftsTemplate(), leadsSalesDTO.getGiftName());
        }
        return StringUtils.EMPTY;
    }

    private String formatPriceWithHalfUp(BigDecimal price) {
        return price.setScale(2, RoundingMode.HALF_UP)
                .stripTrailingZeros()
                .toPlainString();
    }

    private DealBuyBanner buildBanner(LeadsDealBarConfig buyBarConfig, String giftText) {
        DealBuyBanner dealBuyBanner = new DealBuyBanner();
        dealBuyBanner.setBannerType(BannerTypeEnum.COMMON_TYPE.getType());
        dealBuyBanner.setIconUrl(buyBarConfig.getBannerIcon());
        dealBuyBanner.setBackGroundColor(Lists.newArrayList(buyBarConfig.getBannerBackGroundColor()));
        dealBuyBanner.setContent(buildBannerContextJson(buyBarConfig, giftText));
        dealBuyBanner.setShow(true);
        return dealBuyBanner;
    }

    private String buildBannerContextJson(LeadsDealBarConfig buyBarConfig, String giftText) {
        RichText.TextItem defaultTextItem = new RichText.TextItem();
        defaultTextItem.setText(giftText);
        defaultTextItem.setTextsize(buyBarConfig.getBannerTextSize());
        defaultTextItem.setTextcolor(buyBarConfig.getBannerTextColor());
        defaultTextItem.setTextstyle(TextStyle.DEFAULT.getStyle());

        RichText richText=new RichText();
        richText.getTextItemList().add(defaultTextItem);
        return richText.toString();
    }
}
