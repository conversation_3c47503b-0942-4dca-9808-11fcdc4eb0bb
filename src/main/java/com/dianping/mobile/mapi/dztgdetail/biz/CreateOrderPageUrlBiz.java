package com.dianping.mobile.mapi.dztgdetail.biz;

import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.DealDouHuUtil;
import com.dianping.mobile.mapi.dztgdetail.common.ShopUuidUtils;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BindingSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.StyleTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.BuyMoreSaveMoreCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.BuyMoreSaveMoreReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.*;
import com.dianping.mobile.mapi.dztgdetail.entity.CombinationDealInfo;
import com.dianping.mobile.mapi.dztgdetail.exception.QueryCreateOrderPageUrlException;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.facade.DealQueryParallFacade;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.ReserveMaintenanceService;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.utils.FaultToleranceUtils;
import com.dianping.mobile.mapi.dztgdetail.util.*;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.dianping.tuangu.dztg.usercenter.api.CreateOrderPageUrlService;
import com.dianping.tuangu.dztg.usercenter.api.dto.BatchGetCreateOrderPageUrlDto;
import com.dianping.tuangu.dztg.usercenter.api.dto.GetCreateOrderPageUrlEnvDto;
import com.dianping.tuangu.dztg.usercenter.api.dto.Response;
import com.dianping.tuangu.dztg.usercenter.api.enums.ChannelEnum;
import com.dianping.tuangu.dztg.usercenter.api.enums.CreateOrderPageSourceEnum;
import com.dianping.tuangu.dztg.usercenter.api.request.BatchGetCreateOrderPageUrlReq;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.Future;
import java.util.function.Consumer;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/3/9 20:41
 **/
@Component
@Slf4j
public class CreateOrderPageUrlBiz {

    private final static Set<Integer> NOT_SUPPORT_BTN_TYPE_ENUM_SET = new HashSet<>();

    public static final Map<DztgClientTypeEnum, Integer> CLIENT_TYPE_MAP = Maps.newHashMap();

    public static final String JUMP_URL_SUFFIX = "&combinationid=%s&combinationproductid=%s&combinationskuid=%s";

    private static final String FLOATING_LAYER = "floatingLayer";

    private static final int MT_LIVE_CHANNEL = 8;

    static {
        NOT_SUPPORT_BTN_TYPE_ENUM_SET.add(BuyBtnTypeEnum.TIMES_CARD.getCode());
        NOT_SUPPORT_BTN_TYPE_ENUM_SET.add(BuyBtnTypeEnum.PINTUAN.getCode());
        NOT_SUPPORT_BTN_TYPE_ENUM_SET.add(BuyBtnTypeEnum.MEMBER.getCode());
        NOT_SUPPORT_BTN_TYPE_ENUM_SET.add(BuyBtnTypeEnum.RESV_DEAL.getCode());
        NOT_SUPPORT_BTN_TYPE_ENUM_SET.add(BuyBtnTypeEnum.TRIAL_CLASS.getCode());
    }

    static {
        CLIENT_TYPE_MAP.put(DztgClientTypeEnum.MEITUAN_APP, ChannelEnum.APP.getType());
        CLIENT_TYPE_MAP.put(DztgClientTypeEnum.DIANPING_APP, ChannelEnum.APP.getType());
        CLIENT_TYPE_MAP.put(DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP, ChannelEnum.WX_MINI_PROGRAM.getType());
        CLIENT_TYPE_MAP.put(DztgClientTypeEnum.DIANPING_BAIDUMAP_MINIAPP, ChannelEnum.BAIDU_MAP_MINI_PROGRAM.getType());
        CLIENT_TYPE_MAP.put(DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP, ChannelEnum.WX_MINI_PROGRAM.getType());
        CLIENT_TYPE_MAP.put(DztgClientTypeEnum.MEITUAN_LIVE_WEIXIN_MINIAPP, MT_LIVE_CHANNEL);
    }

    @Resource
    private CreateOrderPageUrlService createOrderPageUrlService;

    @Resource(name = "createOrderPageUrlServiceFuture")
    private CreateOrderPageUrlService createOrderPageUrlServiceFuture;

    @Autowired
    private ReserveMaintenanceService reserveMaintenanceService;

    public static boolean isMainApp(EnvCtx envCtx) {
        return DztgClientTypeEnum.MEITUAN_APP.equals(envCtx.getDztgClientTypeEnum())
                || DztgClientTypeEnum.DIANPING_APP.equals(envCtx.getDztgClientTypeEnum())
                || DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP.equals(envCtx.getDztgClientTypeEnum())
                || DztgClientTypeEnum.DIANPING_BAIDUMAP_MINIAPP.equals(envCtx.getDztgClientTypeEnum())
                || DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP.equals(envCtx.getDztgClientTypeEnum());
    }

    public void replaceUrlByTradeUrlService(EnvCtx envCtx, DealGroupPBO result, DealBaseReq request) {
        long startTime = System.currentTimeMillis();
        if (!useTradePageUrl()) {
            return;
        }

        if (!isMainApp(envCtx) && !isMtLiveWxMiniApp(envCtx.getDztgClientTypeEnum())) {
            return;
        }

        // 特殊业务前置校验
        if (!doBusinessPreCheckForShouldExecute(result)) {
            return;
        }

        String skuId = request.getSkuId();

        BatchGetCreateOrderPageUrlReq req = new BatchGetCreateOrderPageUrlReq();
        GetCreateOrderPageUrlEnvDto getCreateOrderPageUrlEnvDto = new GetCreateOrderPageUrlEnvDto();
        getCreateOrderPageUrlEnvDto.setPlatform(envCtx.isMt() ? 1 : 2);
        getCreateOrderPageUrlEnvDto.setChannel(CLIENT_TYPE_MAP.get(envCtx.getDztgClientTypeEnum()));
        getCreateOrderPageUrlEnvDto.setPageSource(getPageSource(request.getPageSource(), result.getCategoryId(), envCtx.getDztgClientTypeEnum()));
        getCreateOrderPageUrlEnvDto.setVersion(envCtx.getVersion());

        req.setEnvDto(getCreateOrderPageUrlEnvDto);

        BatchGetCreateOrderPageUrlDto batchGetCreateOrderPageUrlDto = new BatchGetCreateOrderPageUrlDto();
        batchGetCreateOrderPageUrlDto.setProductId(String.valueOf(envCtx.isMt() ? result.getMtId() : result.getDpId()));
        if (StringUtils.isNumeric(skuId) && Long.parseLong(skuId) > 0 && !DealUtils.isNewWearableNailDeal(result)) {
            //将请求中的skuId透传给提单页
            batchGetCreateOrderPageUrlDto.setSkuId(skuId);
        }

        if (result.getShop() != null) {
            batchGetCreateOrderPageUrlDto.setShopId(String.valueOf(result.getShop().getShopId()));
            batchGetCreateOrderPageUrlDto.setShopUuid(result.getShop().getShopUuid());
        }

        if (result.getBuyBar() != null && result.getBuyBar().getBuyBtns() != null) {
            for (DealBuyBtn buyBtn : result.getBuyBar().getBuyBtns()) {
                if (isNotSupportBtnType(buyBtn) || isPinTuanProcessingRedirectUrl(result, buyBtn)) {
                    // 如果是小程序并且团单适用于专属会员,需要继续的往下执行innerReplace里面的代码,生成提单页的跳链
                    if (!(envCtx.isMiniApp() && buyBtn.getDetailBuyType() == BuyBtnTypeEnum.MEMBER.getCode() && LionConfigUtils.memberCardMiniProgramSwitch())) {
                        continue;
                    }
                }
                innerReplace(req, batchGetCreateOrderPageUrlDto, buyBtn.getRedirectUrl(), buyBtn::setRedirectUrl, result, request, envCtx);
            }
        } else {
            Cat.logEvent("NullPointer", "result.getBuyBar() or result.getBuyBar().getBuyBtns() is null");
        }

        Optional<DealBuyBannerDetail> optionalDealBuyBannerDetail = Optional.of(result)
                .map(DealGroupPBO::getBuyBar)
                .map(DealBuyBar::getBuyBanner)
                .map(DealBuyBanner::getBannerDetail);

        if (StringUtils.isNotBlank(optionalDealBuyBannerDetail.map(DealBuyBannerDetail::getRedirectUrl).orElse(null))) {
            DealBuyBannerDetail dealBuyBannerDetail = optionalDealBuyBannerDetail.get();
            innerReplace(
                    req,
                    batchGetCreateOrderPageUrlDto, dealBuyBannerDetail.getRedirectUrl(),
                    dealBuyBannerDetail::setRedirectUrl, result, request, envCtx);
        }

        if (Objects.nonNull(result.getBuyBar()) && CollectionUtils.isNotEmpty(result.getBuyBar().getBuyBtns())) {
            CostEffectivePinTuanUtils.updateDirectBuyBarForPinTuan(result.getBuyBar().getBuyBtns());
        }
        Cat.newCompletedTransactionWithDuration(DealQueryParallFacade.DEAL_QUERY_RCF, "replaceUrl", System.currentTimeMillis() - startTime);
    }

    // 拼团弱化样式主态底bar场景
    private boolean isPinTuanProcessingRedirectUrl(DealGroupPBO result, DealBuyBtn buyBtn) {
        return result.getBuyBar().getStyleType() == StyleTypeEnum.PINTUAN_WEAK_ACTIVE_TWO_BUTTONS.code
                && StringUtils.isBlank(buyBtn.getRedirectUrl());
    }

    private void innerReplace(BatchGetCreateOrderPageUrlReq req,
                              BatchGetCreateOrderPageUrlDto batchGetCreateOrderPageUrlDto,
                              String sourceUrl,
                              Consumer<String> urlConsumer, DealGroupPBO dealGroupPBO, DealBaseReq request, EnvCtx envCtx) {
        req.setBatchGetCreateOrderPageUrlDtoList(Lists.newArrayList(batchGetCreateOrderPageUrlDto));
        try {
            try {
                batchGetCreateOrderPageUrlDto.setExtUrlParam(getParamMapFromUrl(sourceUrl, request.getPageSource(), bestPromoWithMagical(dealGroupPBO.getPromoDetailModule())));
            } catch (Exception e) {
                log.error("getParamMapFromUrl error, url:{}", sourceUrl, e);
                return;
            }
            fillIntegratedReserveDouHuSk(dealGroupPBO, batchGetCreateOrderPageUrlDto, request, envCtx);

            Response<Map<String, String>> res = createOrderPageUrlService.batchGetCreateOrderPageUrl(req);

            String url = Optional.ofNullable(res)
                    .map(Response::getContent)
                    .map(Map::values)
                    .map(Collection::stream)
                    .flatMap(Stream::findAny)
                    .orElse(null);
            // 穿戴甲类目过滤掉skuid参数，由前端拼接
            url = RedirectUrls.filterOrderUrlSkuId(dealGroupPBO, url);
            if (StringUtils.isNotBlank(url) && res.isSuccess()) {
                urlConsumer.accept(url);
            } else {
                if (!allowDegradeToOldTradePageUrl()) {
                    throw new QueryCreateOrderPageUrlException("查询提单页接口结果异常，req:" + GsonUtils.toJsonString(req) + ", res:" + GsonUtils.toJsonString(res));
                }
            }
        } catch (Exception e) {
            FaultToleranceUtils.addException("batchGetCreateOrderPageUrl", e);
            if (allowDegradeToOldTradePageUrl()) {
                log.error("查询提单页接口异常，req:{}", req, e);
                return;
            }
            throw new QueryCreateOrderPageUrlException("查询提单页接口异常，req:" + GsonUtils.toJsonString(req), e);
        }
    }

    public Future createOrderPageUrlServiceFuture(BatchGetCreateOrderPageUrlReq req) {
            try {
                createOrderPageUrlServiceFuture.batchGetCreateOrderPageUrl(req);
                return FutureFactory.getFuture();
            } catch (Exception e) {
                log.error("createOrderPageUrlServiceFuture error", e);
            }
        return null;
    }

    public Map<Integer, String> buildBuyMoreSaveMoreUrl(BuyMoreSaveMoreCtx ctx, Long shopId, Map<Integer,
            CombinationDealInfo> bindingDealCombinationInfoMap, Integer mainDealId, boolean floatLayer, String priceSecretInfo) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.biz.CreateOrderPageUrlBiz.buildBuyMoreSaveMoreUrl(BuyMoreSaveMoreCtx,Long,Map,Integer)");
        Map<Integer, String> dealIdUrlMap = Maps.newHashMap();
        if (!useTradePageUrl()) {
            return dealIdUrlMap;
        }

        EnvCtx envCtx = ctx.getEnvCtx();
        if (!isMainApp(envCtx)) {
            return dealIdUrlMap;
        }
        BuyMoreSaveMoreReq buyMoreSaveMoreReq = ctx.getReq();

        BatchGetCreateOrderPageUrlReq req = new BatchGetCreateOrderPageUrlReq();
        GetCreateOrderPageUrlEnvDto getCreateOrderPageUrlEnvDto = new GetCreateOrderPageUrlEnvDto();
        getCreateOrderPageUrlEnvDto.setPlatform(envCtx.isMt() ? 1 : 2);
        getCreateOrderPageUrlEnvDto.setChannel(CLIENT_TYPE_MAP.get(envCtx.getDztgClientTypeEnum()));
        getCreateOrderPageUrlEnvDto.setPageSource(CreateOrderPageSourceEnum.DEAL_GROUP_DETAIL.type);
        getCreateOrderPageUrlEnvDto.setVersion(envCtx.getVersion());
        req.setEnvDto(getCreateOrderPageUrlEnvDto);

        List<BatchGetCreateOrderPageUrlDto> dtos = Lists.newArrayList();
        bindingDealCombinationInfoMap.forEach((bindingDealId, combinationDealInfo) -> {
            BatchGetCreateOrderPageUrlDto batchGetCreateOrderPageUrlDto = new BatchGetCreateOrderPageUrlDto();
            if (Objects.nonNull(combinationDealInfo)){
                batchGetCreateOrderPageUrlDto.setProductId(String.valueOf(combinationDealInfo.getMainDealId()));
            }
            long poiid4p = shopId;
            if (ctx.getEnvCtx().isMt()) {
                batchGetCreateOrderPageUrlDto.setShopId(String.valueOf(poiid4p));
            } else {
                batchGetCreateOrderPageUrlDto.setShopUuid(buyMoreSaveMoreReq.getShopUuid());
                if (ShopUuidUtils.retainShopId(poiid4p)) {
                    batchGetCreateOrderPageUrlDto.setShopId(String.valueOf(poiid4p));
                }
            }
            Map<String, String> extUrlParamMap = batchGetCreateOrderPageUrlDto.getExtUrlParam();
            if (MapUtils.isEmpty(extUrlParamMap)) {
                extUrlParamMap = Maps.newHashMap();
                batchGetCreateOrderPageUrlDto.setExtUrlParam(extUrlParamMap);
            }
            // 展示浮层的参数
            if (floatLayer) {
                extUrlParamMap.put("expid", FLOATING_LAYER);
            }
            if (StringUtils.isNotBlank(priceSecretInfo)) {
                extUrlParamMap.put("pricecipher", priceSecretInfo);
            }
            dtos.add(batchGetCreateOrderPageUrlDto);
        });
        req.setBatchGetCreateOrderPageUrlDtoList(dtos);
        try {
            Response<Map<String, String>> res = createOrderPageUrlService.batchGetCreateOrderPageUrl(req);
            if (Objects.isNull(res) || MapUtils.isEmpty(res.getContent())) {
                return Maps.newHashMap();
            }

            String jumpUrlPrefix = res.getContent().get(String.valueOf(mainDealId));
            bindingDealCombinationInfoMap.forEach((bindingDealId, combinationDealInfo) -> {
                dealIdUrlMap.put(bindingDealId, jumpUrlPrefix + String.format(JUMP_URL_SUFFIX, getCombinationId(combinationDealInfo), String.valueOf(combinationDealInfo.getBindingDealId()), String.valueOf(combinationDealInfo.getTyingSaleSkuId())));
            });
        } catch (Exception e) {
            FaultToleranceUtils.addException("batchGetCreateOrderPageUrl", e);
            log.error("CreateOrderPageUrlBiz batchGetCreateOrderPageUrl error", e);
        }

        return dealIdUrlMap;
    }

    // 算法组品屏蔽组合id
    private String getCombinationId(CombinationDealInfo combinationDealInfo) {
        if (combinationDealInfo.getBindingSource() == BindingSourceEnum.ALGORITHM.getValue()) {
            return "0";
        }
        return combinationDealInfo.getItemId();
    }

    /**
     * 填充过夜的实验号
     *
     * @param ctx
     * @param dto
     */
    public void fillOverNightDouHuSk(DealCtx ctx, BatchGetCreateOrderPageUrlDto dto) {
        ModuleAbConfig overNightAbConfig = DealDouHuUtil.getOverNightDouHuSk(ctx);
        if (Objects.isNull(overNightAbConfig) || CollectionUtils.isEmpty(overNightAbConfig.getConfigs()) || Objects.isNull(overNightAbConfig.getConfigs().get(0))) {
            return;
        }
        setExtUrlParam(dto, "expid", overNightAbConfig.getConfigs().get(0).getExpId() + "_" + overNightAbConfig.getConfigs().get(0).getExpResult());
    }

    /**
     * 给交易传参的具体的逻辑可以参考这个wiki: https://km.sankuai.com/collabpage/2705479460
     * @param result
     * @param dto
     * @param request
     */
    public void fillIntegratedReserveDouHuSk(DealGroupPBO result, BatchGetCreateOrderPageUrlDto dto, DealBaseReq request,
                                             EnvCtx envCtx) {
        if (result.isCleaningSelfOperationShop()) {
            setExtUrlParam(dto, "orderdetailtype", "2");
            String unionId = Optional.ofNullable(envCtx).map(EnvCtx::getUnionId).orElse(StringUtils.EMPTY);
            if (LionConfigUtils.isCleaningSelfPreOrderSwitch() ||
                    LionConfigUtils.getCleaningSelfPreOrderWhiteList().contains(unionId)) {
                setExtUrlParam(dto, "preordershowtype", "1");
                result.setHasReserveEntrance(false);
            }
        }
    }

    public void setExtUrlParam(BatchGetCreateOrderPageUrlDto dto, String key, String value) {
        if (StringUtils.isEmpty(key) || StringUtils.isEmpty(value)) {
            return;
        }
        Map<String, String> map = dto.getExtUrlParam();
        if (MapUtils.isEmpty(map)) {
            map = new HashMap<>();
            dto.setExtUrlParam(map);
        }
        map.put(key, value);
    }

    public static boolean isNotSupportBtnType(DealBuyBtn buyBtn) {
        return NOT_SUPPORT_BTN_TYPE_ENUM_SET.contains(buyBtn.getDetailBuyType());
    }

    private Map<String, String> getParamMapFromUrl(String redirectUrl, String trafficFlag, boolean isMagicalPrice) throws UnsupportedEncodingException {
        if (StringUtils.isBlank(redirectUrl)) {
            return null;
        }

        Map<String, String> result = new HashMap<>();
        result.put("origin_product_pre_order_url", redirectUrl);

        String[] split = redirectUrl.split("\\?");

        if (split.length < 2) {
            return result;
        } else {
            String[] argPairs = split[1].split("&");
            for (String argPair : argPairs) {
                String[] keyValue = argPair.split("=");
                if (keyValue.length < 2) {
                    continue;
                }
                result.put(keyValue[0], URLDecoder.decode(keyValue[1], StandardCharsets.UTF_8.name()));
            }
        }

        // 设置流量标识参数（该标识已废弃，仅用于LE预约场景，提单实际使用trafficflag作为流量标识传递）
        if (StringUtils.isNotBlank(trafficFlag)) {
            result.put("trafficFlag", trafficFlag);
        }

        // 最优优惠组合包含神券标识
        result.put("isMagicalPrice", String.valueOf(isMagicalPrice));

        return result;
    }

    private boolean bestPromoWithMagical(PromoDetailModule promoDetailModule) {
        if (promoDetailModule == null || CollectionUtils.isEmpty(promoDetailModule.getBestPromoDetails())) {
            return false;
        }
        DealBestPromoDetail dealBestPromoDetail = promoDetailModule.getBestPromoDetails().stream().filter(d -> ParallDealBuilderProcessor.DEFAULT_VIP_COUPON_DESC.equals(d.getPromoTag())).findFirst().orElse(null);
        return dealBestPromoDetail != null;
    }


    public static boolean useTradePageUrl() {
        return Lion.getBoolean(Environment.getAppName(), "com.sankuai.dzu.tpbase.dztgdetailweb.useTradePageUrl", false);
    }

    public static boolean allowDegradeToOldTradePageUrl() {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.biz.CreateOrderPageUrlBiz.allowDegradeToOldTradePageUrl()");
        return Lion.getBoolean(Environment.getAppName(), "com.sankuai.dzu.tpbase.dztgdetailweb.allowDegradeToOldTradePageUrl", true);
    }

    private static String getPageSource(String requestPageSource, Integer dealCategoryId, DztgClientTypeEnum clientTypeEnum) {
        if (isMtLiveWxMiniApp(clientTypeEnum)) {
            return CreateOrderPageSourceEnum.MEIBO.type;
        }
        if (MallUtils.isFromMallFoodPoiVoucher(requestPageSource, dealCategoryId)) {
            return CreateOrderPageSourceEnum.UNITY_DEAL_GROUP_DETAIL.type;
        }
        return CreateOrderPageSourceEnum.DEAL_GROUP_DETAIL.type;
    }

    /**
     * 业务前置校验，判断是否执行
     * @return  true需要执行；false不需要执行
     */
    private boolean doBusinessPreCheckForShouldExecute(DealGroupPBO result) {
        // 健身通按钮链接不后置处理
        if (FitnessCrossUtils.isFitnessCrossDeal(result)) {
            return false;
        }
        return true;
    }

    private static boolean isMtLiveWxMiniApp(DztgClientTypeEnum clientTypeEnum) {
        return DztgClientTypeEnum.MEITUAN_LIVE_WEIXIN_MINIAPP.equals(clientTypeEnum);
    }

}
