package com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by wa<PERSON><PERSON><PERSON> on 16/4/21.
 */
@MobileDo
@Data
public class MtMenu implements Serializable {

    @MobileField
    private String title;

    @MobileField
    private List<MtMenuDetail> menuDetails;

    @MobileField
    private List<String> tips;

    @MobileField(key =0x5285)
    private String hint;

}

