package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DzDealMerchantInfoRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.Response;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealMerchantInfoBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.WelfareMerchantBO;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Sets;
import com.sankuai.general.product.query.center.client.builder.model.*;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupCustomerDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.meituan.charity.merchant.main.sdk.WelfareDocFacade;
import com.sankuai.meituan.charity.merchant.main.sdk.dto.request.DzVpoiReq;
import com.sankuai.meituan.charity.merchant.main.sdk.dto.response.DzProductDocResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/7/16 5:47 PM
 */
@Component
@Slf4j
public class DealMerchantAdditionFacade {
    @Resource
    private WelfareDocFacade welfareDocFacade;

    @Resource
    private MapperWrapper mapperWrapper;

    @Resource
    private QueryCenterWrapper queryCenterWrapper;

    public Response<DealMerchantInfoBO> buildMerchantAddition(DzDealMerchantInfoRequest request, EnvCtx envCtx) {
        DealMerchantInfoBO dealMerchantInfoBO = new DealMerchantInfoBO();
        dealMerchantInfoBO.setWelfare(buildWelfare(request, envCtx));
        return Response.createSuccessResponse(dealMerchantInfoBO);
    }

    private WelfareMerchantBO buildWelfare(DzDealMerchantInfoRequest request, EnvCtx envCtx) {
        try {
            DzVpoiReq dzVpoiReq = new DzVpoiReq();
            dzVpoiReq.setPoiId(getMtShopId(request.getShopId(), envCtx.isMt()));
            dzVpoiReq.setPartnerId(buildPartnerId(request, envCtx));
            DzProductDocResp dzProductDocResp = welfareDocFacade.queryDzProductDoc(dzVpoiReq);
            Boolean signCharity = dzProductDocResp.getSignCharity();
            if (BooleanUtils.isNotTrue(signCharity)) {
                return null;
            }
            WelfareMerchantBO welfareMerchant = new WelfareMerchantBO();
            welfareMerchant.setDesc(dzProductDocResp.getText());
            welfareMerchant.setTitle("公益商家");
            welfareMerchant.setIcon("https://p0.meituan.net/travelcube/ab2944835b44a6388458340d007d97a61464.png");
            return welfareMerchant;
        } catch (Exception e) {
            log.error("buildWelfare error, request:{}", JsonCodec.encode(request), e);
            return null;
        }
    }

    private String buildPartnerId(DzDealMerchantInfoRequest request, EnvCtx envCtx) throws TException {
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet(NumberUtils.toLong(request.getDealId())), envCtx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP)
                .dealGroupId(DealGroupIdBuilder.builder().all())
                .channel(DealGroupChannelBuilder.builder().all())
                .customer(DealGroupCustomerBuilder.builder().originCustomerId())
                .build();
        DealGroupDTO dealGroupDTO = queryCenterWrapper.getDealGroupDTO(queryCenterWrapper.preDealGroupDTO(queryByDealGroupIdRequest));
        return Optional.ofNullable(dealGroupDTO)
                .map(DealGroupDTO::getCustomer)
                .filter(Objects::nonNull)
                .map(DealGroupCustomerDTO::getOriginCustomerId)
                .map(String::valueOf)
                .orElse(null);
    }

    private String getMtShopId(String shopId, boolean mt) {
        if (mt) {
            return shopId;
        }
        long mtShopIdByDpShopIdLong = mapperWrapper.getMtShopIdByDpShopIdLong(NumberUtils.toLong(shopId));
        return String.valueOf(mtShopIdByDpShopIdLong);
    }
}
