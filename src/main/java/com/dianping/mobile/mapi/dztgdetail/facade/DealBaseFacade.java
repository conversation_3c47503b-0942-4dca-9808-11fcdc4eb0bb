package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.beauty.deal.bean.nail.BeautyNailTitleBean;
import com.dianping.beauty.deal.service.BeautyNailService;
import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.base.datatypes.utils.VersionUtil;
import com.dianping.mobile.biz.base.service.log.APISysLog;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.biz.PromoBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.*;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Cons;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealFields;
import com.dianping.mobile.mapi.dztgdetail.common.constants.MttgDetailLionKeys;
import com.dianping.mobile.mapi.dztgdetail.common.constants.PoiConsts;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DealShowTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MtTerm;
import com.dianping.mobile.mapi.dztgdetail.common.enums.PromotionBuyStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.exception.MtDealUnavailableException;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.*;
import com.dianping.mobile.mapi.dztgdetail.entity.DealBranchesParam;
import com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam;
import com.dianping.mobile.mapi.dztgdetail.helper.*;
import com.dianping.mobile.mapi.dztgdetail.util.*;
import com.dianping.pay.common.enums.PayPlatform;
import com.dianping.pay.promo.common.enums.ProductType;
import com.dianping.pay.promo.display.api.dto.Product;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.pay.promo.display.api.dto.QueryPromoDisplayRequest;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.jmonitor.LOG;
import com.meituan.service.mobile.group.geo.bean.CityInfo;
import com.meituan.service.mobile.message.group.deal.PoiidListRequestMsg;
import com.meituan.service.mobile.prometheus.enums.DealTypeEnum;
import com.meituan.service.mobile.prometheus.model.DealModel;
import com.meituan.service.mobile.prometheus.utils.PropertyUtil;
import com.meituan.service.mobile.sinai.client.model.PoiModelL;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormatter;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Future;

/**
 * Created by liumingyong on 16/4/5.
 */
@Component
@Slf4j
public class DealBaseFacade {

    @Resource
    private DealClientWrapper dealClientServiceWrapper;
    @Resource
    private PromoBiz promoBiz;
    @Resource
    private GroupDealWrapper groupDealWrapper;
    @Resource
    private PoiClientWrapper poiClientWrapper;
    @Resource
    private CityServiceWrapper cityServiceWrapper;
    @Resource
    private DealGroupWrapper dealGroupWrapper;
    @Resource
    private BeautyNailService beautyNailService;

    private static final int THRESHOLD = 20;
    private static final String MEALITEM_TYPE_KEY = "type";
    private static final String MEALITEM_CONTENT_KEY = "content";
    private static final String ROWTYPE_TITLE = "0";
    private static final String ROWTYPE_ITEM = "128";
    private static final String MEALITEM_SUBTYPE_KEY = "subtype";
    private static final int SUBTYPE_MENU_TITLE = 0;
    private static final int SUBTYPE_MEAL_TITLE = 1;
    private static final int SUBTYPE_MEAL_ITEM = 2;
    private static final int SUBTYPE_MEAL_DESC = 4;
    private static final String CHANNEL_KEY = "channel";
    private static final String APP_KEY = "app";
    private static final String ANDROID = "android";
    private static final int PROMO_TEMP_ID = 131;

    public DealBaseDo getDealBase(DealBaseRequest request, IMobileContext context) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.getDealBase(com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseRequest,com.dianping.mobile.framework.datatypes.IMobileContext)");
        validate(request, context);
        List<DealModel> dealModelList = dealClientServiceWrapper.listDealByIds(Lists.newArrayList(request.getDealId()), DealFields.DETAIL_ALL_FIELDS);
        if (CollectionUtils.isEmpty(dealModelList)) {
            throw new MtDealUnavailableException();
        }
        MtCommonParam commonParam = new MtCommonParam(context);
        DealBaseDo dbd = getDealByModel(dealModelList, DealFields.DETAIL_ALL_FIELDS, request, commonParam);
        fillBeauty(dbd, request);
        //如果最后返回为null则抛错
        if (dbd == null) {
            throw new MtDealUnavailableException();
        }
        return dbd;
    }

    private void validate(DealBaseRequest request, IMobileContext context) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.validate(com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseRequest,com.dianping.mobile.framework.datatypes.IMobileContext)");
        if (request.getDealId() == null) {
            throw new MtDealUnavailableException("deal id is null");
        }
        if (context == null) {
            throw new MtDealUnavailableException("mobile context is null");
        }
        int dpDealGroupId = dealGroupWrapper.getDpDealGroupId(request.getDealId());
        DealGroupBaseDTO dealGroup = dealGroupWrapper.getFutureResult(dealGroupWrapper.preDealGroupBase(dpDealGroupId));

        if (dealGroup == null) {
            throw new NullPointerException("deal group base info not found " + request.getDealId());
        }

        if (dealGroup.getSalePlatform() == Cons.DP_ONLY) {
            String msg = String.format("deal [%s] is dianping sales only", request.getDealId());
            throw new UnsupportedOperationException(msg);
        }
    }

    private void fillBeauty(DealBaseDo dbd, DealBaseRequest request) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.fillBeauty(DealBaseDo,DealBaseRequest)");
        String temp = dbd.getCate() + "," + dbd.getSubCate();
        if (dbd.getFrontPoiCates() != null && dbd.getFrontPoiCates().size() > 0) {
            for (Integer cate : dbd.getFrontPoiCates()) {
                temp = temp + "," + cate;
            }
        }
        // 美甲美睫
        if (temp.contains("20413") || temp.contains("20414") || temp.contains("20415") || temp.contains("75")) {
            int dpDealGroupID = dealGroupWrapper.getDpDealGroupId(request.getDealId());
            if (dpDealGroupID <= 0) {
                log.error("dealRelationService.findByMTDealGroupID return null [" + request.getDealId() + "]");
                return;
            }
            Future future = dealGroupWrapper.preGetDealGroupAttrs(Lists.newArrayList(dpDealGroupID), Lists.newArrayList("dealdetail_beautytable_new", "take-time", "holdtime"));
            // 美甲美睫的title使用业务方定制提供的title
            replaceBeautyNailEyeTitle(dbd, dpDealGroupID);
            adaptBeautyTag(dbd, future, dpDealGroupID);
        }
    }

    private void replaceBeautyNailEyeTitle(DealBaseDo dbd, Integer dpDealID) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.replaceBeautyNailEyeTitle(com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.DealBaseDo,java.lang.Integer)");
        BeautyNailTitleBean beautyNailTitleBean = beautyNailService.loadNailTitle4WebView(dpDealID);
        if (beautyNailTitleBean.isShowable()) {
            String title = beautyNailTitleBean.getTitle();
            if (title != null && title.trim().length() > 0) {
                //主标题
                dbd.setBrandName(title);
            }
            String subTitle = beautyNailTitleBean.getSubTitle();
            if (subTitle != null && subTitle.trim().length() > 0) {
                // 副标题
                dbd.setTitle(subTitle);
            }
        }
    }

    /**
     * 美甲美睫 新单 增加 保持时长,服务耗时
     */
    private void adaptBeautyTag(DealBaseDo dbd, Future future, Integer dpDealGroupID) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.adaptBeautyTag(com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.DealBaseDo,java.util.concurrent.Future,java.lang.Integer)");
        List<AttributeDTO> attributeDtoList = getDealGroupAttribute(dpDealGroupID, future);
        // 美甲美睫 新单
        if (AttributeUtils.hasAttribute(attributeDtoList, "dealdetail_beautytable_new", "1")) {
            dbd.setCostSpan(AttributeUtils.getFirstValue(attributeDtoList, "take-time"));
            dbd.setEffectSpan(AttributeUtils.getFirstValue(attributeDtoList, "holdtime"));
        }
    }

    public List<AttributeDTO> getDealGroupAttribute(int dpDealGroupID, Future serviceFuture) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.getDealGroupAttribute(int,java.util.concurrent.Future)");
        if (serviceFuture == null) {
            return null;
        }

        try {
            Map<Integer, List<AttributeDTO>> result = dealGroupWrapper.getDealGroupAttrs(serviceFuture);
            return result == null ? null : result.get(dpDealGroupID);
        } catch (Exception e) {
            log.error("deal-attribute failed, dealGroupId: " + dpDealGroupID, e);
        }
        return null;
    }

    private DealBaseDo getDealByModel(List<DealModel> dealList, List<String> fields,
                                      DealBaseRequest request,
                                      MtCommonParam commonParam) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.getDealByModel(List,List,DealBaseRequest,MtCommonParam)");
        if (CollectionUtils.isEmpty(dealList)) {
            return null;
        }
        DealModel dealModel = dealList.get(0);

        QueryPromoDisplayRequest promoReq = getQueryPromoDisplayRequest(commonParam, dealModel);
        List<PromoDisplayDTO> promoDisplayDTOs = promoBiz.queryPromoDisplayDTO(promoReq);

        DealBaseDo dbd = new DealBaseDo();
        int did = dealModel.getDid();
        Map<Integer, List<PoiModelL>> poiModelMap = getPoiModelByDids(Lists.newArrayList(did), request.getCityId());
        List<PoiModelL> poiModelList = poiModelMap.get(did);
        changeDeal(dealModel, request.getCityId(), fields, commonParam);
        Long poiId = ShopIdUpdateUtil.transferStrToLong(request.getPoiId(), request.getPoiIdStr(), "dealbase.bin");
        buildDealBaseDoWithPoi(dealModel, poiModelList, promoDisplayDTOs, commonParam, dbd, poiId);
        return dbd;
    }

    private QueryPromoDisplayRequest getQueryPromoDisplayRequest(MtCommonParam commonParam, DealModel dealModel) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.getQueryPromoDisplayRequest(com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam,com.meituan.service.mobile.prometheus.model.DealModel)");
        QueryPromoDisplayRequest promoReq = new QueryPromoDisplayRequest();
        Product product = new Product();
        product.setProductId(dealModel.getDid());
        product.setPrice(BigDecimal.valueOf(dealModel.getPrice()));

        promoReq.setProduct(product);
        promoReq.setProductType(ProductType.mt_tuangou.getValue());

        promoReq.setUserId(commonParam.getUserId());
        promoReq.setCityId(commonParam.getCityId());
        promoReq.setTemplateID(PROMO_TEMP_ID);
        promoReq.setPlatform(PayPlatform.mt_iphone_native.getCode());
        String utmMedium = commonParam.getUtmMedium();
        promoReq.setClientVersion(commonParam.getUtmTerm());
        Map<String, String> contextMap = Maps.newHashMap();
        contextMap.put(CHANNEL_KEY, commonParam.getUtmSource());
        contextMap.put(APP_KEY, UTMHelper.convertUTMCampaign2App(commonParam.getUtmCampaign()));
        promoReq.setContextMap(contextMap);
        if (ANDROID.equals(utmMedium)) {
            promoReq.setPlatform(PayPlatform.mt_android_native.getCode());
        }
        return promoReq;
    }

    public DealBaseDo buildDealBaseDo(DealModel dealModel, List<PromoDisplayDTO> promoDisplayDTOs,
                                      MtCommonParam commonParam, DealBaseDo dbd) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.buildDealBaseDo(DealModel,List,MtCommonParam,DealBaseDo)");

        dbd.setId(dealModel.getDid());
        dbd.setShopListUrl(RedirectUrls.getMtAppShopList(dealModel.getDid()));
        dbd.setSlug(dealModel.getSlug());
        dbd.setFrontPoiCates(dealModel.getFrontPoiCates());
        dbd.setChannel(dealModel.getChannel());
        dbd.setDealType(dealModel.getCtype());
        Map<Integer, String> attrsMap = dealModel.getAttrs() == null ? new HashMap<>() : dealModel.getAttrs();

        int solds = genSoldQuantity(dealModel, attrsMap);
        if (getDisplaySalesFuzzy()) {
            dbd.setSolds(0);
            dbd.setSoldStr("已售" + formatSaleCount(solds));
        } else {
            dbd.setSolds(solds);
            dbd.setSoldStr("已售" + solds);
        }
        dbd.setStatus(dealModel.getStatus());
        dbd.setStartTime(new Date(dealModel.getStart() * 1000));
        dbd.setEndTime(new Date(dealModel.getEnd() * 1000));
        dbd.setPrice(dealModel.getPrice());
        dbd.setOriginalPrice(dealModel.getValue());
        dbd.setTitle(dealModel.getTitle());
        dbd.setCouponTitle(dealModel.getCoupontitle());
        dbd.setImgUrl(ImageUtils.getFullImageUrl(dealModel.getImageUrl()));
        dbd.setSquareImgUrl(ImageUtils.getFullImageUrl(StringUtils.isEmpty(dealModel.getSquareImageUrl())
                ? dealModel.getImageUrl() : dealModel.getSquareImageUrl()));
        dbd.setBrandName(dealModel.getBrandName());
        dbd.setRange(dealModel.getRange());
        dbd.setTerms(spliceTerms(dealModel));
        dbd.setHowUse(dealModel.getHowuse());
        int redeemType = DealFieldHelper.getRedeemTypeFromHowUse(dealModel.getHowuse());
        dbd.setRedeemType(redeemType);
        dbd.setMenus(DealFieldHelper.getMenusFromJson(dealModel.getMenu(), redeemType));
        dbd.setRefund(dealModel.getRefundModel().getRefund());
        dbd.setExpireAutoRefund(dealModel.getRefundModel().getExpireautorefund());
        dbd.setNotice(dealModel.getVoice());
        dbd.setRatingModel(genRating(dealModel));
        dbd.setDeposit(dealModel.getDeposit());
        dbd.setShowType(genShowType(dealModel));

        PromoHelper.assemblePromoInfo(dbd, promoDisplayDTOs);

        String bookingPhone = genBookingPhone(attrsMap.get(Cons.ATTRS_BOOKING_PHONE));
        dbd.setBookingPhone(bookingPhone);
        dbd.setNoBooking(dealModel.getNobooking());
        dbd.setTodayAvailable(todayAvailable(attrsMap.get(11)));
        buildBuyConfig(dealModel, dbd);
        return dbd;
    }

    private void buildBuyConfig(DealModel dealModel, DealBaseDo dealBaseDo) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.buildBuyConfig(com.meituan.service.mobile.prometheus.model.DealModel,com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.DealBaseDo)");

        Date startTime = new Date(dealModel.getStart() * 1000);
        Date endTime = new Date(dealModel.getEnd() * 1000);
        boolean dealAvailable = (dealModel.getStatus() == 0);

        MtDealBuyConfig buyConfig = new MtDealBuyConfig();
        if (startTime.compareTo(new Date()) > 0) {
            buyConfig.setButtonText("即将开始");
            buyConfig.setButtonEnable(false);
            buyConfig.setPriceStrikeThrough(false);
        } else if (endTime.compareTo(new Date()) < 0) {
            buyConfig.setButtonText("已结束");
            buyConfig.setButtonEnable(false);
            buyConfig.setPriceStrikeThrough(false);
        } else if (!dealAvailable) {
            buyConfig.setButtonText("已卖光");
            buyConfig.setButtonEnable(false);
            buyConfig.setPriceStrikeThrough(false);
        } else {
            boolean hasPromotion = isHasPromotion(dealBaseDo.getPromotionInfos());
            if (dealBaseDo.getCanBuyPrice() > 0 && dealBaseDo.getCampaignPrice() > 0 && hasPromotion) {
                String priceStr = PriceHelper.priceFormat(dealBaseDo.getCanBuyPrice());
                buyConfig.setButtonText(Cons.MONEY_SYMBOL + priceStr + (priceStr.length() > 5 ? "" : " 限时抢购"));
                buyConfig.setPriceStrikeThrough(true);
            } else {
                buyConfig.setButtonText("立即抢购");
                buyConfig.setPriceStrikeThrough(false);
            }
            buyConfig.setButtonEnable(true);
        }

        dealBaseDo.setDealBuyConfig(buyConfig);
    }

    private boolean isHasPromotion(List<MtPromotionInfo> promotionInfos) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.isHasPromotion(java.util.List)");
        if (CollectionUtils.isEmpty(promotionInfos)) {
            return false;
        }

        for (MtPromotionInfo info : promotionInfos) {
            if (info.getBuyStatus() == PromotionBuyStatusEnum.Underway.getValue()) {
                return true;
            }
        }
        return false;
    }

    private int genSoldQuantity(DealModel dealModel, Map<Integer, String> attrsMap) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.genSoldQuantity(com.meituan.service.mobile.prometheus.model.DealModel,java.util.Map)");
        int result = dealModel.getSoldQuantity();
        if (attrsMap.containsKey(Cons.ATTRS_B_CHEAT)) {
            if (null != attrsMap.get(Cons.ATTRS_B_CHEAT)) {
                // handle cheat operation
                String[] args = attrsMap.get(Cons.ATTRS_B_CHEAT).split(",");
                for (String arg : args) {
                    // 刷单
                    if (Cons.CHEAT_SCALPING.equals(arg)) {
                        result = 0;
                        break;
                    }
                }
            }
        }
        return result;
    }

    private List<String> getContentsWithString(String contents) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.getContentsWithString(java.lang.String)");
        if (StringUtils.isBlank(contents)) {
            return null;
        }
        List<String> list = Lists.newArrayList();
        try {
            JSONArray jsonContent = new JSONArray(contents);
            for (int j = 0; j < jsonContent.length(); j++) {
                list.add((String) jsonContent.get(j));
            }
            return list;
        } catch (Exception e) {
            log.error("getContentsWithString has failed:" + contents, e);
        }
        return null;
    }

    /**
     * 合并套餐模块中的适用范围到使用须知
     */
    private List<MtTerm> spliceTerms(DealModel dealModel) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.spliceTerms(com.meituan.service.mobile.prometheus.model.DealModel)");
        List<MtTerm> mtTerms = DealFieldHelper.genMtTermListFromTermStr(dealModel.getTerms());
        List<MtTerm> result = Lists.newArrayList();

        Map<Integer, String> attrs = dealModel.getAttrs();

        if (attrs != null && attrs.containsKey(Cons.ATTRS_IS_COUPON) && "1".equals(attrs.get(Cons.ATTRS_IS_COUPON))) {
            String menu = dealModel.getMenu();
            if (menu != null && !menu.isEmpty()) {
                try {
                    if (menu.startsWith("[[")) {
                        menu = menu.substring(1, menu.length() - 1);
                    }
                    JSONArray ja = new JSONArray(menu);
                    for (int i = 0; i < ja.length(); ++i) {
                        JSONObject contentJo = ja.getJSONObject(i);
                        String content = contentJo.has("content") ? contentJo.optString("content") : null;
                        if (content != null && content.contains("适用范围：")) {
                            MtTerm mtTerm = new MtTerm();
                            mtTerm.setTitle("适用范围");

                            // 有的数据  "适用范围：仅限菜品\r\n店内人均消费参考价格：40元"
                            String[] strings = content.split("\r|\n|\t");
                            for (String s : strings) {
                                if (!Strings.isNullOrEmpty(s) &&
                                        s.contains("适用范围：")) {
                                    content = s;
                                    break;
                                }
                            }
                            mtTerm.setContents(getContentsWithString("[\"" + content.replaceAll("适用范围：", "") + "\"]"));
                            result.add(mtTerm);
                            break;
                        }
                    }
                } catch (JSONException e) {
                    APISysLog.warn(e.getMessage() + " did=" + dealModel.getDid()
                            + " menu=" + menu, e);
                    return mtTerms;
                }
            }
        }

        result.addAll(mtTerms);
        return result;
    }

    private MtRatingModel genRating(DealModel dealModel) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.genRating(com.meituan.service.mobile.prometheus.model.DealModel)");
        MtRatingModel mtRatingModel = new MtRatingModel();
        if (null != dealModel.getRatingModel()) {
            int rateCount = dealModel.getRatingModel().getRateCount();
            // 没有评分数，newrating给空json
            if (rateCount > 0) {
                mtRatingModel.setRateCount(rateCount);
                mtRatingModel.setRating(dealModel.getRatingModel().getRating());
            }
        }
        return mtRatingModel;
    }

    private String genShowType(DealModel dealModel) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.genShowType(com.meituan.service.mobile.prometheus.model.DealModel)");
        long properties = dealModel.getProperties();
        if (PropertyUtil.isTrue(properties, PropertyUtil.BIT_ISNEWWEDINGDEAL)) {
            return DealShowTypeEnum.WEDDING.getName();
        }
        //如果是品类包含20，showtype为酒店
        if (null != dealModel.getCates() && dealModel.getCates().contains(20)) {
            return DealShowTypeEnum.HOTEL.getName();
        }
        return DealShowTypeEnum.NORMAL.getName();
    }


    private String genBookingPhone(String attrJson) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.genBookingPhone(java.lang.String)");
        if (StringUtils.isBlank(attrJson)) {
            return StringUtils.EMPTY;
        }
        String bookingPhone = StringUtils.EMPTY;
        try {
            JSONObject jo = new JSONObject(attrJson).getJSONObject("label");
            if (null != jo) {
                if (jo.has("hotelPhoneNum")) {
                    String value = jo.optString("hotelPhoneNum");
                    if (StringUtils.isNotBlank(value)) {
                        bookingPhone = value;
                    }
                }
            }
        } catch (JSONException e) {
            APISysLog.warn("genBookingPhone() parsing error. json=" + attrJson, e);
        }
        return bookingPhone;
    }

    // 目前只能判断deal 不可用, 只有false 是可用返回值
    // true 返回值请忽略
    public static boolean todayAvailable(String str) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.todayAvailable(java.lang.String)");
        try {
            if (StringUtils.isNotBlank(str)) {
                JSONObject notAvailableInfo = new JSONObject(str);
                if (notAvailableInfo.optInt("useDay") == 0) {
                    DateTime dateTime = DateTime.now();
                    DateTimeFormatter formatter = org.joda.time.format.DateTimeFormat.forPattern("yyyy-MM-dd");
                    String dayOfWeek = dateTime.dayOfWeek().getAsString();
                    if (notAvailableInfo.has("weekday")) {
                        String weekDateStr = notAvailableInfo.optString("weekday");
                        if (StringUtils.isNotBlank(weekDateStr)) {
                            String[] weekDates = weekDateStr.split(Cons.STR_SEPARATOR_COMMA);
                            for (String weekDate : weekDates) {
                                if (dayOfWeek.equals(weekDate)) {
                                    return false;
                                }
                            }
                        }
                    }
                    if (notAvailableInfo.has("startDate") && notAvailableInfo.has("endDate")) {
                        String startDateStr = notAvailableInfo.optString("startDate");
                        String endDateStr = notAvailableInfo.optString("endDate");
                        if (StringUtils.isNotBlank(startDateStr) && StringUtils.isNotBlank(endDateStr)) {
                            String[] startDates = startDateStr.split(Cons.STR_SEPARATOR_COMMA);
                            String[] endDates = endDateStr.split(Cons.STR_SEPARATOR_COMMA);
                            for (int i = 0; i < startDates.length; i++) {
                                String startDate = startDates[i];
                                String endDate = endDates[i];
                                if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
                                    continue;
                                }
                                long start = DateTime.parse(startDate, formatter).getMillis();
                                long end = DateTime.parse(endDate, formatter).getMillis();
                                end += millSecondsOfOneDay();
                                if (dateTime.getMillis() >= start && dateTime.getMillis() <= end) {
                                    return false;
                                }
                            }
                        }
                    }
                }
            }
            return true;
        } catch (Exception e) {
            LOG.error("todayAvailable error", e);
            return false;
        }
    }

    private static long millSecondsOfOneDay() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.millSecondsOfOneDay()");
        return 24 * 60 * 60 * 1000L;
    }

    /**
     * 返回did与相应poi信息列表的对应关系.
     */
    public Map<Integer, List<PoiModelL>> getPoiModelByDids(List<Integer> dids, int cityId) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.getPoiModelByDids(java.util.List,int)");
        if (CollectionUtils.isEmpty(dids)) {
            return null;
        }
        try {
            Map<Integer, List<PoiModelL>> poiModelMap = new HashMap<>();
            PoiidListRequestMsg poiIdListReq = new PoiidListRequestMsg();
            poiIdListReq.setCityId(Math.max(cityId, 0));
            poiIdListReq.setDids(dids);
            poiIdListReq.setThreshold(THRESHOLD);
            Map<Integer, List<Long>> did2PoiIdMap = listPoiIdByDIds(poiIdListReq);

            // 从poi-thrift获取poi信息
            List<Long> allPoiIds = new ArrayList<>();
            for (List<Long> lst : did2PoiIdMap.values()) {
                allPoiIds.addAll(lst);
            }

            Map<Long, PoiModelL> allPoiModel = getPoiModelMap(allPoiIds);

            for (Integer did : did2PoiIdMap.keySet()) {
                List<Long> poiIds = did2PoiIdMap.get(did);
                List<PoiModelL> poiMsgList = poiModelMap.get(did);
                if (CollectionUtils.isEmpty(poiMsgList)) {
                    poiMsgList = new ArrayList<>();
                }
                for (Long poiId : poiIds) {
                    if (allPoiModel.get(poiId) != null) {
                        poiMsgList.add(allPoiModel.get(poiId));
                    }
                }
                poiModelMap.put(did, poiMsgList);
            }
            return poiModelMap;
        } catch (Exception e) {
            log.error("getPoiModelByDids has error", e);
        }
        return null;
    }

    /**
     * 分段获取deal
     */
    public Map<Integer, List<Long>> listPoiIdByDIds(PoiidListRequestMsg req) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.listPoiIdByDIds(com.meituan.service.mobile.message.group.deal.PoiidListRequestMsg)");
        int reqCount = req.getDidsSize();
        int maxCount = 20;
        int offset = 0;
        List<Integer> didList = new ArrayList(req.getDids());
        Map<Integer, List<Long>> resultMap = new HashMap<>();

        /**
         * 分段请求数据
         */
        while (reqCount > maxCount) {
            List<Integer> tmpList = didList.subList(offset, offset + maxCount);
            req.setDids(tmpList);
            Map<Integer, List<Long>> tmpResult = groupDealWrapper.listPoiIdByDIds(req);
            if (tmpResult != null) {
                resultMap.putAll(tmpResult);
            }
            offset += maxCount;
            reqCount -= maxCount;
        }

        List<Integer> tmpList = didList.subList(offset, didList.size());
        if (CollectionUtils.isNotEmpty(tmpList)) {
            req.setDids(tmpList);
            Map<Integer, List<Long>> tmpResult = groupDealWrapper.listPoiIdByDIds(req);
            if (tmpResult != null) {
                resultMap.putAll(tmpResult);
            }
        }

        return resultMap;
    }

    /**
     * 分段请求poi-thrift，防止其超时，同时减少访问次数.
     */
    private Map<Long, PoiModelL> getPoiModelMap(List<Long> poiids) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.getPoiModelMap(java.util.List)");
        long start = System.currentTimeMillis();
        Map<Long, PoiModelL> poiModelMap = new HashMap<>();
        if (CollectionUtils.isEmpty(poiids)) {
            return poiModelMap;
        }

        List<PoiModelL> poiMsgList = poiClientWrapper.listShoppingMalls(poiids, PoiConsts.defaultPoiFields);
        if (CollectionUtils.isNotEmpty(poiMsgList)) {
            for (PoiModelL poiModel : poiMsgList) {
                poiModelMap.put(poiModel.getId(), poiModel);
            }
        }

        long cost = System.currentTimeMillis() - start;
        if (cost > 200) {
            APISysLog.warn("getPoiModelMap list poi from poi-thrift cost time: " + cost
                    + " poiid size: " + poiids.size(), null);
        }
        return poiModelMap;
    }

    /**
     * 变更deal的字段，需要持有一些依赖的bean才能修改的字段.
     *
     * @param rowFields app请求时给出的字段列表
     */
    public void changeDeal(DealModel deal, int currentCityId, List<String> rowFields, MtCommonParam commonParam) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.changeDeal(com.meituan.service.mobile.prometheus.model.DealModel,int,java.util.List,com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam)");
        if (null == deal || CollectionUtils.isEmpty(rowFields)) {
            return;
        }

        alterRange(deal, currentCityId);
        alterVoice(deal);
        alterMenu(deal);

        /**
         * 从5.1版本开始，免预约需要在后台拼装
         * http://wiki.sankuai.com/pages/viewpage.action?pageId=113022566
         */
        if (deal.getNobooking() == 1 && patchVersion4Title(commonParam)) {
            deal.setTitle(deal.getTitle() + "，免预约");
        }
    }

    /**
     * 修改range字段.
     */
    private void alterRange(DealModel deal, int currentCityId) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.alterRange(com.meituan.service.mobile.prometheus.model.DealModel,int)");
        // 生产新的range
        int ctype = deal.getCtype();
        // 1.若是物流单，range不做变更
        if (DealTypeEnum.WULIU.getCtype() == ctype) {
            return;
        }
        // 2.其他的，range做变更
        List<Integer> cityIds = deal.getCityIds();
        String oldRange = deal.getRange();
        CityInfo curentCity = cityServiceWrapper.getCityById(currentCityId);
        String newRange = produceRangeName(cityIds, curentCity, oldRange);
        deal.setRange(newRange);
    }

    /**
     * 生产新的rangeName.
     */
    private static String produceRangeName(List<Integer> cityIds, CityInfo currentCity, String basicRangeName) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.produceRangeName(java.util.List,com.meituan.service.mobile.group.geo.bean.CityInfo,java.lang.String)");
        List<Integer> cityIdsList = uniqCityIds(cityIds);
        // 如果为空，返回已有的rangeName
        if (CollectionUtils.isEmpty(cityIdsList)) {
            return basicRangeName;
        }
        // 没有当前城市且cityIds>1
        if (currentCity == null) {
            if (cityIdsList.size() > 1) {
                return "多城市";
            } else {
                return basicRangeName;
            }
        }
        // 全国单
        if (cityIdsList.contains(DealFields.GLOBAL_CITY_ID)) {
            return "全国";
        }
        // 北京等
        if (cityIdsList.size() > 1) {
            return currentCity.getName() + "等";
        }
        return basicRangeName;
    }

    private static List<Integer> uniqCityIds(List<Integer> cityIds) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.uniqCityIds(java.util.List)");
        if (CollectionUtils.isEmpty(cityIds)) {
            return Collections.emptyList();
        }
        Set<Integer> set = new HashSet<>();
        for (Integer each : cityIds) {
            if (each <= 0) {
                continue;
            }
            set.add(each);
        }
        List<Integer> list = new ArrayList<>();
        list.addAll(set);
        return list;
    }

    /**
     * 修改最新通知.
     */
    private void alterVoice(DealModel deal) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.alterVoice(com.meituan.service.mobile.prometheus.model.DealModel)");
        // 变更逻辑
        //临时加入逻辑，price<5元，不参加活动,**********=Wed Jan  1 00:15:43 CST 2014
        if (deal.getPrice() > 0 && deal.getPrice() < 5 && System.currentTimeMillis() < **********000l) {
            String newVoice = "本单是特惠单，暂不支持代金券且不参加任何促销活动";
            if (StringUtils.isBlank(deal.getVoice())) {
                newVoice = newVoice + "\n" + deal.getVoice();
            }
            deal.setVoice(newVoice);
        }
        if (StringUtils.isNotBlank(deal.getVoice())) {
            deal.setVoice(HtmlUtils.format(deal.getVoice()));
        }
    }

    private void alterMenu(DealModel deal) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.alterMenu(com.meituan.service.mobile.prometheus.model.DealModel)");
        String oldMenu = deal.getMenu();
        if (StringUtils.isBlank(oldMenu) || "[[]]".equals(oldMenu)) {
            return;
        }

        JSONArray ja4newMenu = new JSONArray();
        try {
            JSONArray ja4menu = new JSONArray(oldMenu);
            for (int i = 0; i < ja4menu.length(); i++) {
                JSONArray ja4meal = ja4menu.getJSONArray(i);
                addSubtype4MealItem(ja4meal);
                ja4newMenu.put(ja4meal);
            }
        } catch (Exception e) {
            APISysLog.warn("alterMenu() parse string to jsonarray error. menu=" + oldMenu, e);
        }
        deal.setMenu(ja4newMenu.toString());
    }

    /**
     * 为每款套餐中项增加subtype字段.
     */
    private void addSubtype4MealItem(JSONArray ja4meal) throws JSONException {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.addSubtype4MealItem(org.json.JSONArray)");
        int len = ja4meal.length();
        for (int i = 0; i < len; i++) {
            JSONObject jo4item = ja4meal.getJSONObject(i);
            String rowType = jo4item.optString(MEALITEM_TYPE_KEY);
            if (ROWTYPE_ITEM.equals(rowType)) {
                jo4item.put(MEALITEM_SUBTYPE_KEY, SUBTYPE_MEAL_ITEM);
            } else if (ROWTYPE_TITLE.equals(rowType)) {
                if (0 == i && len > 2
                        && ROWTYPE_TITLE.equals(ja4meal.getJSONObject(i + 1).optString(MEALITEM_TYPE_KEY))) {
                    jo4item.put(MEALITEM_SUBTYPE_KEY, SUBTYPE_MENU_TITLE);
                } else if (len - 1 == i) {
                    jo4item.put(MEALITEM_SUBTYPE_KEY, SUBTYPE_MEAL_DESC);
                } else {
                    jo4item.put(MEALITEM_SUBTYPE_KEY, SUBTYPE_MEAL_TITLE);
                }
            }
        }

        // 对最后一个且type=“0”的项做特殊处理
        JSONObject last = ja4meal.getJSONObject(len - 1);
        String rowType = last.optString(MEALITEM_TYPE_KEY);
        if (StringUtils.isNotBlank(rowType) && ROWTYPE_TITLE.equals(rowType)) {
            String content = last.optString(MEALITEM_CONTENT_KEY);
            if (StringUtils.isNotBlank(content)) {
                String[] subcontents = content.split("\n");
                for (String subcontent : subcontents) {
                    String s = HtmlUtils.format(subcontent);
                    if (StringUtils.isNotBlank(s)) {
                        JSONObject jo4sub = new JSONObject();
                        jo4sub.put(MEALITEM_CONTENT_KEY, HtmlUtils.format(subcontent));
                        jo4sub.put(MEALITEM_TYPE_KEY, ROWTYPE_TITLE);
                        jo4sub.put(MEALITEM_SUBTYPE_KEY, SUBTYPE_MEAL_DESC);
                        ja4meal.put(jo4sub);
                    }
                }
            }
        }
    }

    //true走新逻辑，false走老逻辑.
    private boolean patchVersion4Title(MtCommonParam commonParam) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.patchVersion4Title(com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam)");
        String utmMedium = commonParam.getUtmMedium();
        String utmTerm = commonParam.getUtmTerm();
        String versionName = commonParam.getVersionName();
        if (StringUtils.isBlank(utmMedium) || StringUtils.isBlank(utmTerm)) {
            return false;
        }

        if ("iphone".equals(utmMedium)) {
            return VersionUtil.compare(utmTerm, "5.1") >= 0;
        } else if ("android".equals(utmMedium) && StringUtils.isNotBlank(versionName)) {
            return VersionUtil.compare(versionName, "5.1") >= 0;
        }
        return false;
    }

    private List<MtPoiModel> produceRdplocList(List<PoiModelL> poiModelList, boolean show) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.produceRdplocList(java.util.List,boolean)");
        List<MtPoiModel> result = Lists.newArrayList();
        if (show && CollectionUtils.isNotEmpty(poiModelList)) {
            for (PoiModelL poi : poiModelList) {
                MtPoiModel mtRdploc = PoiModelHelper.buildRdploc(poi);
                if (null != mtRdploc) {
                    result.add(mtRdploc);
                }
            }
        }
        return result;
    }

    private MtPoiModel producePickedRdploc(List<PoiModelL> poiModelList,
                                           boolean show, long poiId,
                                           MtCommonParam commonParam) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.producePickedRdploc(java.util.List,boolean,long,com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam)");
        if (CollectionUtils.isEmpty(poiModelList)) {
            return null;
        }
        DealBranchesParam param = new DealBranchesParam().buildParam(commonParam, 1, 0, false, StringUtils.EMPTY);

        //如果APP传了poiId，则展示APP希望看到的poi
        if (poiId > 0) {
            for (PoiModelL poiModel : poiModelList) {
                if (poiId == poiModel.getId()) {
                    return PoiModelHelper.buildRdploc(poiModel);
                }
            }
        }

        List<PoiModelL> picked = PoisRankHelper.pick(poiModelList, show, param);
        List<MtPoiModel> mtPoiModels = produceRdplocList(picked, show);
        return CollectionUtils.isEmpty(mtPoiModels) ? null : mtPoiModels.get(0);
    }

    // 获取当前城市与dealId对应的poi数量
    private int produceCurrentCityRDCount(List<PoiModelL> poiModelList, MtCommonParam commonParam) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.produceCurrentCityRDCount(java.util.List,com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam)");
        int count = 0, cityId = 0;
        String ci = commonParam.getCityIdStr();

        if (StringUtils.isBlank(ci)) {
            return poiModelList != null ? poiModelList.size() : 0;
        }
        try {
            cityId = commonParam.getCityId();
        } catch (NumberFormatException e) {
            LOG.error("Parameter commonParam.ci Type Convert Error!", e);
        }
        if (CollectionUtils.isEmpty(poiModelList)) {
            return count;
        }
        for (PoiModelL poi : poiModelList) {
            if (null != poi.getCityIds()) {
                if (poi.getCityIds().contains(cityId)) {
                    count++;
                }
            }
        }
        return count;
    }

    private void buildDealBaseDoWithPoi(DealModel deal,
                                        List<PoiModelL> poiModels,
                                        List<PromoDisplayDTO> promoDisplayDTOs,
                                        MtCommonParam commonParam,
                                        DealBaseDo dbd, long poiId) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.buildDealBaseDoWithPoi(DealModel,List,List,MtCommonParam,DealBaseDo,long)");
        // --- deal相关属性 ---
        buildDealBaseDo(deal, promoDisplayDTOs, commonParam, dbd);
        // --- poi相关属性 ---
        boolean showRdploc = DealUtils.IsShowRdploc(deal);
        dbd.setShop(producePickedRdploc(poiModels, showRdploc, poiId, commonParam));
        // 第二个参数实现不好
        dbd.setCurcityRdCount(produceCurrentCityRDCount(poiModels, commonParam));
    }

    private static String formatSaleCount(int saleCount) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.formatSaleCount(int)");
        String saleCountStr;
        if (saleCount < 50) {
            saleCountStr = String.valueOf(saleCount);
        } else if (saleCount < 100) {
            saleCountStr = (saleCount / 10 * 10) + "+";
        } else if (saleCount < 1000) {
            saleCountStr = (saleCount / 100 * 100) + "+";
        } else if (saleCount < 10000) {
            saleCountStr = (saleCount / 1000 * 1000) + "+";
        } else {
            saleCountStr = (saleCount / 10000) + "." + ((saleCount % 10000) / 1000) + "万+";
        }
        return saleCountStr;
    }

    /**
     * 销量区间化开关，true 不返回真实销量，false 返回真实销量，默认值 false
     *
     * @return bool
     */
    private boolean getDisplaySalesFuzzy() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade.getDisplaySalesFuzzy()");
        return (Lion.getBoolean("com.sankuai.dzu.tpbase.dztgdetailweb", MttgDetailLionKeys.DISPLAY_SALES_FUZZY, false));
    }
}
