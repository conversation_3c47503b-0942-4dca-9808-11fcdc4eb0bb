package com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

@TypeDoc(description = "优惠提前曝光")
@MobileDo(id = 0x441d)
@Data
public class ExposurePromoInfo {

    @FieldDoc(description = "标签文字")
    @MobileDo.MobileField(key = 0x4587)
    private String labelText;

    @FieldDoc(description = "标签背景开始色")
    @MobileDo.MobileField(key = 0x9e5c)
    private String labelBgStartColor;

    @FieldDoc(description = "标签背景结束色")
    @MobileDo.MobileField(key = 0x2c32)
    private String labelBgEndColor;

    @FieldDoc(description = "优惠详情")
    @MobileDo.MobileField(key = 0x5cb1)
    private String promoDetail;

}