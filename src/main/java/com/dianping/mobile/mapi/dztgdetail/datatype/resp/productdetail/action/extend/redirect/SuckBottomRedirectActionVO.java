package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.action.extend.redirect;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.action.enums.OpenTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.BottomBarActionDataVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/04/11
 * 神券感知增强吸底条 去购买按钮
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SuckBottomRedirectActionVO extends BottomBarActionDataVO {

    /**
     * 动作类型
     */
    private  int actionType;

    /**
     * 打开类型
     * redirect=直接跳转
     * modal=打开浮层
     *
     * @see OpenTypeEnum
     */
    private String openType;

    /**
     * 跳转链接
     */
    private String url;

    /**
     * 按钮文案
     */
    private String text;

    /**
     * 图标
     */
    private String icon;

}