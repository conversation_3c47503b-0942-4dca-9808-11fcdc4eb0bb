package com.dianping.mobile.mapi.dztgdetail.sig;

import com.sankuai.sig.botdefender.adapter.annotation.configure.SigAopBeansConfigure;
import com.sankuai.sig.botdefender.adapter.configure.SigEnvironmentConfigure;
import com.sankuai.sig.botdefender.adapter.configure.SigPearlConfigure;
import com.sankuai.sig.botdefender.adapter.configure.SigPearlContextInitConfigure;
import com.sankuai.sig.botdefender.adapter.configure.SigSpringMvcConfigure;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import({SigEnvironmentConfigure.class,
        SigPearlConfigure.class,
        SigPearlContextInitConfigure.class,
        SigSpringMvcConfigure.class,
        SigAopBeansConfigure.class})
public class SigConfig {
}