package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.style;

import com.dianping.beauty.deal.bean.BeautyContentDetail;
import com.dianping.beauty.deal.service.BeautyStructureService;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.Pair;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.utils.FaultToleranceUtils;
import com.dianping.mobile.mapi.dztgdetail.helper.MtDealDetailBuilder;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.dianping.mobile.mapi.dztgdetail.util.SwitchUtils;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import com.sankuai.mpmctcontent.query.thrift.api.ContentFusion2CService;
import com.sankuai.mpmctcontent.query.thrift.dto.ItemFieldDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.aggre.SearchFusionInfoReqDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.aggre.SearchFusionInfoRespDTO;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2023/8/17
 * @since mapi-dztgdetail-web
 */
public class ParallBeautyAdaptor extends AbsDealProcessor {

    @Autowired
    @Qualifier("beautyStructureServiceFuture")
    private BeautyStructureService beautyStructureServiceFuture;

    @Autowired
    @Qualifier("contentFusion2CService")
    private ContentFusion2CService contentFusion2CServiceFuture;

    public static int BEAUTY_NAIL_CATEGORY = 502;

    @Override
    public boolean isEnable(DealCtx ctx) {
        if(ctx.getEnvCtx() != null && ctx.getEnvCtx().isFromH5()) {
            return false;
        }
        return SwitchUtils.isBeautyHairEnable();
    }

    @Override
    public void prepare(DealCtx ctx) {
        String category = Lion.getStringValue("mapi-mttgdetail-web.beauty.category");
        int publishCategoryId = ctx.getCategoryId();
        if (!isBeauty(category, publishCategoryId)) {
            return;
        }
        if(publishCategoryId == BEAUTY_NAIL_CATEGORY) {
            SearchFusionInfoReqDTO searchFusionInfoReqDTO = buildReq(ctx);
            Future beautyNailStyleFuture = null;
            try {
                contentFusion2CServiceFuture.searchFusionInfo(searchFusionInfoReqDTO);
                beautyNailStyleFuture = ContextStore.getFuture();
            } catch (Exception e) {
                logger.error("BeautyAdaptor contentFusion2CServiceFuture.searchFusionInfo error!, request = " + GsonUtils.toJsonString(searchFusionInfoReqDTO), e);
            }
            ctx.getFutureCtx().setBeautyNailStyleFuture(beautyNailStyleFuture);
        }
        
    }

    /**
     * 查询美甲款式个数
     * @param ctx
     * @return
     */
    private SearchFusionInfoReqDTO buildReq(DealCtx ctx) {
        SearchFusionInfoReqDTO searchFusionInfoReqDTO = new SearchFusionInfoReqDTO();
        searchFusionInfoReqDTO.setOwnerId(null);
        searchFusionInfoReqDTO.setOwnerType(null);
        searchFusionInfoReqDTO.setSubBizType(27);
        searchFusionInfoReqDTO.setStart(0);
        searchFusionInfoReqDTO.setLimit(50);

        List<ItemFieldDTO> queryList = new ArrayList<>();
        ItemFieldDTO query = new ItemFieldDTO();
        query.setFieldCode("relatedDpDealId");
        query.setFieldValue(String.valueOf(ctx.getDpId()));
        queryList.add(query);

        searchFusionInfoReqDTO.setQueryList(queryList);
        searchFusionInfoReqDTO.setNonQueryList(null);
        searchFusionInfoReqDTO.setModuleKey("manicure_deal_related_module");
        searchFusionInfoReqDTO.setBizType(2);
        searchFusionInfoReqDTO.setNeedTrans(null);
        searchFusionInfoReqDTO.setStatFieldList(null);
        searchFusionInfoReqDTO.setNeedValidateInterest(false);

        return searchFusionInfoReqDTO;
    }

    public static SearchFusionInfoReqDTO buildReq(int dpId) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.style.ParallBeautyAdaptor.buildReq(int)");
        SearchFusionInfoReqDTO searchFusionInfoReqDTO = new SearchFusionInfoReqDTO();
        searchFusionInfoReqDTO.setOwnerId(null);
        searchFusionInfoReqDTO.setOwnerType(null);
        searchFusionInfoReqDTO.setSubBizType(27);
        searchFusionInfoReqDTO.setStart(0);
        searchFusionInfoReqDTO.setLimit(50);

        List<ItemFieldDTO> queryList = new ArrayList<>();
        ItemFieldDTO query = new ItemFieldDTO();
        query.setFieldCode("relatedDpDealId");
        query.setFieldValue(String.valueOf(dpId));
        queryList.add(query);

        searchFusionInfoReqDTO.setQueryList(queryList);
        searchFusionInfoReqDTO.setNonQueryList(null);
        searchFusionInfoReqDTO.setModuleKey("manicure_deal_related_module");
        searchFusionInfoReqDTO.setBizType(2);
        searchFusionInfoReqDTO.setNeedTrans(null);
        searchFusionInfoReqDTO.setStatFieldList(null);
        searchFusionInfoReqDTO.setNeedValidateInterest(false);

        return searchFusionInfoReqDTO;
    }

    @Override
    public void process(DealCtx ctx) {
        if(ctx.getCategoryId() == BEAUTY_NAIL_CATEGORY) {
            Future beautyNailStyleFuture = ctx.getFutureCtx().getBeautyNailStyleFuture();
            try {
                SearchFusionInfoRespDTO searchFusionInfoRespDTO = (SearchFusionInfoRespDTO) beautyNailStyleFuture.get(500, TimeUnit.MILLISECONDS);
                if(searchFusionInfoRespDTO != null && searchFusionInfoRespDTO.isSuccess()
                        && searchFusionInfoRespDTO.getTotalCount() != null && searchFusionInfoRespDTO.getTotalCount() >= 10L) {
                    ctx.setBeautyNailMultiStyle(true);
                }
            } catch (Exception e) {
                logger.error("BeautyAdaptor contentFusion2CServiceFuture.searchFusionInfo error!", e);
                FaultToleranceUtils.addException("searchFusionInfo", e);
            }
        }
    }

    private boolean isBeauty(String category, final int publishCategoryId) {
        if (StringUtils.isEmpty(category)) {
            return false;
        }
        String[] categoryIds = category.split(",");
        for (String categoryID : categoryIds) {
            if (Integer.valueOf(categoryID) == publishCategoryId) {
                return true;
            }
        }
        return false;
    }

    private void replaceDealDetails(BeautyContentDetail beautyContentDetail, List<Pair> structedDetails, boolean isMt) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.style.ParallBeautyAdaptor.replaceDealDetails(com.dianping.beauty.deal.bean.BeautyContentDetail,java.util.List,boolean)");
        if (beautyContentDetail == null || StringUtils.isBlank(beautyContentDetail.getContent())) {
            return;
        }

        boolean hasDetail = false;
        for (Pair pair : structedDetails) {
            if (pair != null && StringUtils.equals(pair.getID(), getDetailKey(isMt))) {
                hasDetail = true;
                pair.setName(beautyContentDetail.getContent());
                break;
            }
        }

        if (!hasDetail) {
            structedDetails.add(0, new Pair(getDetailKey(isMt),
                    beautyContentDetail.getContent(), MtDealDetailBuilder.TYPE_DESCRIPTION));
        }
    }

    private void addServiceProcess(BeautyContentDetail beautyContentDetail, List<Pair> structedDetails, boolean isMt) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.style.ParallBeautyAdaptor.addServiceProcess(com.dianping.beauty.deal.bean.BeautyContentDetail,java.util.List,boolean)");
        if (beautyContentDetail == null || StringUtils.isBlank(beautyContentDetail.getContent())) {
            return;
        }

        for (int pos = 0; pos < structedDetails.size(); pos++) {
            Pair pair = structedDetails.get(pos);
            if (pair != null && StringUtils.equals(pair.getID(), getDetailKey(isMt))) {
                structedDetails.add(
                        pos + 1, new Pair("服务流程", beautyContentDetail.getContent(), MtDealDetailBuilder.TYPE_SERVICE_FLOW));
            }
        }
    }

    private void addShopProvides(BeautyContentDetail beautyContentDetail, List<Pair> structedDetails) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.style.ParallBeautyAdaptor.addShopProvides(com.dianping.beauty.deal.bean.BeautyContentDetail,java.util.List)");
        if (beautyContentDetail == null || StringUtils.isBlank(beautyContentDetail.getContent())) {
            return;
        }
        structedDetails.add(new Pair("商家服务", beautyContentDetail.getContent(), MtDealDetailBuilder.TYPE_SHOP_PROVIDES));

        for (Pair pair : structedDetails) {
            if (pair != null && StringUtils.equals(pair.getID(), "购买须知")) {
                pair.setName(removeOldShopProvides(pair.getName()));
            }
        }
    }

    /*
     * 去除购买须知中老的商家服务模块
     */
    private String removeOldShopProvides(String specialPoint) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.style.ParallBeautyAdaptor.removeOldShopProvides(java.lang.String)");
        Document document = Jsoup.parse(specialPoint);
        Iterator<Element> iterator = document.select("div .purchase-notes dl").iterator();
        for (; iterator.hasNext(); ) {
            Element element = iterator.next();
            Elements dts = element.select("dt");
            if (dts == null || dts.get(0) == null) {
                continue;
            }

            if (StringUtils.equals(dts.get(0).text(), "商家服务")) {
                element.remove();
            }
        }
        return document.toString();
    }

    private String getDetailKey(boolean isMt) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.style.ParallBeautyAdaptor.getDetailKey(boolean)");
        return isMt ? MtDealDetailBuilder.DETAIL_KEY : "团购详情";
    }



}
