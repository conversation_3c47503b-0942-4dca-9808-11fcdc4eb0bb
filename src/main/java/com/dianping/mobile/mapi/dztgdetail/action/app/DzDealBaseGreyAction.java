package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseGreyReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DzDealbaseGreyResult;
import com.dianping.mobile.mapi.dztgdetail.facade.DzDealBaseGreyFacade;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.HttpMethod;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

@InterfaceDoc(displayName = "到综团单展示信息APP查询接口",
        type = "restful",
        description = "查询到综团单展示信息：包括团单基础信息、购买栏、优惠、适用商户等等。",
        scenarios = "该接口仅适用于双平台APP站点的团购详情页，其他以到综团购为纬度的页面如需使用请咨询项目owner",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "qian.wang.sh"
)
@Controller("general/platform/dztgdetail/dzdealbasegrey.bin")
@Action(url = "dzdealbasegrey.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class DzDealBaseGreyAction extends AbsAction<DealBaseGreyReq> {

    @Autowired
    private DzDealBaseGreyFacade dzDealBaseGreyFacade;

    @Override
    protected IMobileResponse validate(DealBaseGreyReq request, IMobileContext context) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.action.app.DzDealBaseGreyAction.validate(com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseGreyReq,com.dianping.mobile.framework.datatypes.IMobileContext)");
        IdUpgradeUtils.processProductIdForDealBaseGreyReq(request, "dzdealbasegrey.bin");
        if (request == null || request.getDealgroupid() <= 0) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "dzdealbasegrey.bin",
            displayName = "到综团单展示信息查询接口",
            description = "查询到综团单展示信息：包括团单基础信息、购买栏、优惠、适用商户等等。\n如果团单是侵权团单，则后端不返回数据。",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "dzdealbase.bin请求参数",
                            type = DealBaseReq.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "result", description = "团购picasso数据", type = DealGroupPBO.class)},
            restExampleUrl = "http://mapi.51ping.com/general/platform/dztgdetail/dzdealbasegrey.bin?" +
                    "dealgroupid=200139713",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    ),
            }
    )
    @Override
    protected IMobileResponse execute(DealBaseGreyReq request, IMobileContext iMobileContext) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.action.app.DzDealBaseGreyAction.execute(com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseGreyReq,com.dianping.mobile.framework.datatypes.IMobileContext)");
        EnvCtx envCtx = initEnvCtx(iMobileContext);
        try {
            DzDealbaseGreyResult result = dzDealBaseGreyFacade.queryGreyResult(request, envCtx);
            return new CommonMobileResponse(result);
        } catch (Exception e) {
            logger.error("dzdealbasegrey.bin error", e);
        }
        return Resps.SYSTEM_ERROR;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.action.app.DzDealBaseGreyAction.getRule()");
        return null;
    }
}