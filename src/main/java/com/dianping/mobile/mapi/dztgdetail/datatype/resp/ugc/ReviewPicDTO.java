package com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/12/13.
 */
@TypeDoc(description = "评论图片")
@MobileDo(id = 0x9ef)
@Data
public class ReviewPicDTO implements Serializable {

    @FieldDoc(description = "小图")
    @MobileField(key = 0xd1e5)
    private String smallPicUrl;

    @FieldDoc(description = "大图")
    @MobileField(key = 0x540c)
    private String bigPicUrl;

    @FieldDoc(description = "图片类型")
    @MobileField(key = 0x8f0c)
    private int type;

    @FieldDoc(description = "标题")
    @MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "上传时间")
    @MobileField(key = 0x1dec)
    private String uploadTime;

}
