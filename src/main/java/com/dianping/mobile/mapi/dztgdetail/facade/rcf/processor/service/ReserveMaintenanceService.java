package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.cat.Cat;
import com.dianping.cat.util.StringUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.handler.BaseReserveMaintenanceHandler;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/11/7 11:07
 */
@Service
@Slf4j
public class ReserveMaintenanceService implements InitializingBean {
    @Autowired
    private List<BaseReserveMaintenanceHandler> reserveMaintenanceHandlerList;

    @Autowired
    private DouHuBiz douHuBiz;

    /**
     * true走新版买约一体服务,false位置线上现有逻辑
     *
     * @param ctx
     * @return
     */
    public boolean isEnableIntegratedReserved(DealCtx ctx) {
        try {
            for (BaseReserveMaintenanceHandler handler : reserveMaintenanceHandlerList) {
                if (acquireIntegratedReserveExp(ctx, handler)) {
                    Cat.logEvent("ReserveMaintenanceService", "categoryId-" + handler.getDealSecondCategory());
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("isEnableIntegratedReserved error", e);
        }
        return false;
    }

    public AbConfig getIntegratedReservedConfig(DealGroupPBO result, String pageSource) {
        try {
            for (BaseReserveMaintenanceHandler handler : reserveMaintenanceHandlerList) {
                AbConfig abConfig = getAbConfig(result, handler, pageSource);
                if (abConfig != null) {
                    Cat.logEvent("ReserveMaintenanceService", "expId-" + abConfig.getExpId());
                    return abConfig;
                }
            }
        } catch (Exception e) {
            log.error("getIntegratedReservedConfig error", e);
        }
        return null;
    }

    public boolean isHitAbTest(DealGroupPBO result, String pageSource, BaseReserveMaintenanceHandler handler) {
        AbConfig abConfig = getAbConfig(result, handler, pageSource);
        if (abConfig != null) {
            Cat.logEvent("ReserveMaintenanceService",
                    String.format("expId-%s,expResult-%s", abConfig.getExpId(), abConfig.getExpResult()));
            return "c".equals(abConfig.getExpResult());
        }
        return false;
    }

    public boolean getSelfOperatedCleaningReservedResult(DealGroupPBO result, String pageSource) {
        // 找到保洁自营类目的处理器
        Optional<BaseReserveMaintenanceHandler> handlerOpt = reserveMaintenanceHandlerList.stream()
                .filter(handler -> handler.getDealSecondCategory() == 409)
                .findFirst();
        BaseReserveMaintenanceHandler handler = handlerOpt.orElse(null);
        if (handler == null) {
            return false;
        }
        return isHitAbTest(result, pageSource, handler);
    }

    public boolean getIntegratedReservedResult(DealCtx ctx) {
        try {
            for (BaseReserveMaintenanceHandler handler : reserveMaintenanceHandlerList) {
                if (isHitAbTest(ctx, handler)) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("getIntegratedReservedResult error", e);
        }
        return false;
    }

    private boolean isHitAbTest(DealCtx ctx, BaseReserveMaintenanceHandler handler) {
        String pageSource = ctx == null ? "" : ctx.getRequestSource();
        AbConfig abConfig = getAbConfig(ctx, handler, pageSource);
        if (abConfig != null) {
            Cat.logEvent("ReserveMaintenanceService",
                    String.format("expId-%s,expResult-%s", abConfig.getExpId(), abConfig.getExpResult()));
            // 强预订团单特殊逻辑
            if (DealCtxHelper.isPreOrderDeal(ctx)) {
                return "c".equals(abConfig.getExpResult()) || "d".equals(abConfig.getExpResult());
            }
            return handler.isHitAbTest(abConfig);
        }
        return false;
    }

    private AbConfig getAbConfig(Object obj, BaseReserveMaintenanceHandler handler, String pageSource) {
        if (obj == null) {
            return null;
        }

        List<ModuleAbConfig> moduleAbConfigs;

        if (obj instanceof DealGroupPBO) {
            DealGroupPBO result = (DealGroupPBO)obj;
            moduleAbConfigs = result.getModuleAbConfigs();
        } else if (obj instanceof DealCtx) {
            DealCtx result = (DealCtx)obj;
            moduleAbConfigs = result.getModuleAbConfigs();
        } else {
            return null;
        }

        List<String> abKeys = getAbKeys(handler, pageSource);

        if (CollectionUtils.isEmpty(moduleAbConfigs) || CollectionUtils.isEmpty(abKeys)) {
            return null;
        }

        return moduleAbConfigs.stream()
                .filter(moduleAbConfig -> Objects.nonNull(moduleAbConfig) && abKeys.contains(moduleAbConfig.getKey()))
                .findFirst().map(ModuleAbConfig::getConfigs).filter(CollectionUtils::isNotEmpty)
                .map(list -> list.get(0)).orElse(null);
    }

    private List<String> getAbKeys(BaseReserveMaintenanceHandler handler, String pageSource) {
        // 强预订团单更换实验keys
        if (RequestSourceEnum.PRE_ORDER_DEAL.getSource().equals(pageSource)) {
            return handler.getSpecialAbKeys();
        }
        return handler.getAbKeys();
    }

    private boolean acquireIntegratedReserveExp(DealCtx ctx, BaseReserveMaintenanceHandler handler) {
        if (ctx == null) {
            return false;
        }

        int categoryId = ctx.getCategoryId();
        // 识别对应的行业
        if (categoryId != handler.getDealSecondCategory()) {
            return false;
        }

        // 保洁特殊逻辑
        if (!handler.isEnable(ctx)) {
            return false;
        }

        // 获取ab实验结果
        List<ModuleAbConfig> moduleAbConfigs = ctx.getModuleAbConfigs();
        ModuleAbConfig moduleAbConfig = douHuBiz.getAbExpResult(ctx, handler.getExpName(ctx));
        if (moduleAbConfig == null) {
            return false;
        }

        if (CollectionUtils.isEmpty(moduleAbConfigs)) {
            moduleAbConfigs = Lists.newArrayList();
        }
        boolean alreadyExist = moduleAbConfigs.stream()
                .anyMatch(config -> StringUtils.equals(config.getKey(), moduleAbConfig.getKey()));
        if (!alreadyExist) {
            moduleAbConfigs.add(moduleAbConfig);
        }

        List<AbConfig> configs = moduleAbConfig.getConfigs();

        String expResult = Optional.ofNullable(configs).filter(CollectionUtils::isNotEmpty).map(list -> list.get(0))
                .map(AbConfig::getExpResult).orElse("");

        if (DealCtxHelper.isPreOrderDeal(ctx)) {
            return "c".equals(expResult) || "d".equals(expResult);
        }
        return "c".equals(expResult);
    }

    @Override
    public void afterPropertiesSet() {
        reserveMaintenanceHandlerList.sort((o1, o2) -> o2.getDealSecondCategory() - o1.getDealSecondCategory());
    }
}
