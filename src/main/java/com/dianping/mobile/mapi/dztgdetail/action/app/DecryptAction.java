package com.dianping.mobile.mapi.dztgdetail.action.app;


import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DecryptReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.DecryptVO;
import com.dianping.mobile.mapi.dztgdetail.facade.DecryptFacade;
import com.dianping.mobile.mapi.dztgdetail.helper.AppCtxHelper;
import com.meituan.servicecatalog.api.annotations.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

@InterfaceDoc(displayName = "解密接口",
        type = "支持MAPI协议及REST协议",
        description = "解密",
        scenarios = "",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "qian.wang.sh"
)
@Controller("general/platform/dztgdetail/decrypt.bin")
@Action(url = "decrypt.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class DecryptAction extends AbsAction<DecryptReq> {

    @Autowired
    private DecryptFacade decryptFacade;

    @Override
    protected IMobileResponse validate(DecryptReq request, IMobileContext iMobileContext) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.action.app.DecryptAction.validate(com.dianping.mobile.mapi.dztgdetail.datatype.req.DecryptReq,com.dianping.mobile.framework.datatypes.IMobileContext)");
        if (request == null || StringUtils.isBlank(request.getEncryptedStr())) {
            return Resps.PARAM_ERROR;
        }

        return null;
    }

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "decrypt.bin",
            displayName = "解密接口",
            description = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "encrypt.bin请求参数",
                            type = DealBaseReq.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "iMobileContext",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "result", description = "团购picasso数据", type = DealGroupPBO.class)},
            restExampleUrl = "http://mapi.51ping.com/general/platform/dztgdetail/decrypt.bin",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    ),
            }
    )
    @Override
    protected IMobileResponse execute(DecryptReq request, IMobileContext iMobileContext) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.action.app.DecryptAction.execute(com.dianping.mobile.mapi.dztgdetail.datatype.req.DecryptReq,com.dianping.mobile.framework.datatypes.IMobileContext)");

        DecryptVO vo = decryptFacade.decrypt(request.getEncryptedStr(), AppCtxHelper.isMeituanClient(iMobileContext));

        if (vo != null) {
            return new CommonMobileResponse(vo);
        } else {
            return Resps.SYSTEM_ERROR;
        }

    }

    @Override
    protected List<ClientInfoRule> getRule() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.action.app.DecryptAction.getRule()");
        return null;
    }
}
