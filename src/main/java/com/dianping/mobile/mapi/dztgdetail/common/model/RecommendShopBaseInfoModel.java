package com.dianping.mobile.mapi.dztgdetail.common.model;

import com.google.common.util.concurrent.SettableFuture;
import com.sankuai.sinai.data.api.dto.NewPoiSensLevel;
import lombok.Data;

import java.util.List;
import java.util.concurrent.Future;

/**
 * @Author: <EMAIL>
 * @Date: 2024/10/25
 */
@Data
public class RecommendShopBaseInfoModel {
    /**
     * 平台
     */
    private Integer platform;

    /**
     * 点评门店id
     */
    private Long dpShopId;

    /**
     * 美团门店id
     */
    private Long mtShopId;

    /**
     * 前台类目树
     */
    private List<Integer> frontCategory;

    /**
     * 后台类目树
     */
    private List<Integer> backCategory;

    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * 经度
     */
    private Double lng;
    /**
     * 纬度
     */
    private Double lat;

    /**
     * 行政区ID
     */
    private Integer districtId;

    /**
     * 商圈ID
     */
    private Integer regionId;

    /**
     * 新敏感度
     */
    private NewPoiSensLevel newPoiSensLevel;

    private Future poiFuture;

    private SettableFuture poiDtoFuture;
}
