package com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-08-28
 */
@Data
@TypeDoc(description = "款式资源属性，包含款式资源路径、款式名称、款式标签等")
@MobileDo(id = 0xe4531d0e)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ExhibitImageItemVO implements Serializable {
    @FieldDoc(description = "款式标签列表")
    @MobileDo.MobileField(key = 0x342f)
    private List<ImageTagVO> tags;

    @FieldDoc(description = "款式资源列表")
    @MobileDo.MobileField(key = 0xe8d2)
    private List<ImageUrlVO> urls;

    @FieldDoc(description = "款式名称")
    @MobileDo.MobileField(key = 0x7ab8)
    private String name;

    @FieldDoc(description = "款式ID")
    @MobileDo.MobileField(key = 0xb231)
    private String itemId;

    @FieldDoc(description = "im咨询页跳链")
    @MobileDo.MobileField(key = 0x7851)
    private String imUrl;

    @FieldDoc(description = "更多推荐款式场景下对款式来源进行说明")
    @MobileDo.MobileField(key = 0x7db2)
    private String styleCopyright;

    @FieldDoc(description = "款式对应的某一个skuId")
    @MobileDo.MobileField(key = 0xf59e)
    private String skuId;

    @FieldDoc(description = "样式主题ID列表")
    @MobileDo.MobileField(key = 0xb1b2)
    private List<Long> styleThemeList;

    @FieldDoc(description = "样式材质ID列表")
    @MobileDo.MobileField(key = 0xd4bb)
    private List<Long> styleMaterialList;

    @FieldDoc(description = "商户推荐")
    @MobileDo.MobileField(key = 0x8bef)
    private Long recommended;

    @FieldDoc(description = "款式及关联图片总数")
    @MobileDo.MobileField(key = 0xc463)
    private int picCount;
}
