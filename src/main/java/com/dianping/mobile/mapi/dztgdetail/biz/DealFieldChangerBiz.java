package com.dianping.mobile.mapi.dztgdetail.biz;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.CityServiceWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealFields;
import com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam;
import com.dianping.mobile.mapi.dztgdetail.helper.DealFieldTransferHelper;
import com.dianping.mobile.mapi.dztgdetail.util.HtmlUtils;
import com.meituan.dataapp.poi.helper.VersionHelper;
import com.meituan.service.mobile.group.geo.bean.CityInfo;
import com.meituan.service.mobile.prometheus.enums.DealTypeEnum;
import com.meituan.service.mobile.prometheus.model.DealModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by guozhengyao on 16/4/1.
 */
@Component
@Slf4j
public class DealFieldChangerBiz {
    @Resource
    private CityServiceWrapper cityServiceWrapper;

    // ------------------------------- deal信息的变更 -------------------------------- /

    /**
     * 变更deal的字段，需要持有一些依赖的bean才能修改的字段.
     *
     * @param rowFields app请求时给出的字段列表
     */
    public void changeDeal(DealModel deal, int currentCityId, List<String> rowFields, MtCommonParam mtCommonParam) {
        if (null == deal || CollectionUtils.isEmpty(rowFields)) {
            return;
        }

        alterRange(deal, currentCityId);

        if (rowFields.contains(DealFields.DEAL_FIELD_VOICE)) {
            alterVoice(deal);
        }
        if (rowFields.contains(DealFields.DEAL_FIELD_MENU)) {
            alterMenu(deal);
        }

        /**
         * 从5.1版本开始，免预约需要在后台拼装
         * http://wiki.sankuai.com/pages/viewpage.action?pageId=113022566
         */
        if (deal.getNobooking() == 1 && patchVersion4Title(mtCommonParam)) {
            deal.setTitle(deal.getTitle() + "，免预约");
        }
    }

    private boolean patchVersion4Title(MtCommonParam mtCommonParam) {
        String version = VersionHelper.getVersion(mtCommonParam.getUtmMedium(), mtCommonParam.getVersion());
        if (version != null && version.compareTo("5.1") < 0) {
            return false;
        }
        return true;
    }

    /**
     * 修改range字段.
     */
    private void alterRange(DealModel deal, int currentCityId) {
        // 生产新的range
        int ctype = deal.getCtype();
        // 1.若是物流单，range不做变更
        if (DealTypeEnum.WULIU.getCtype() == ctype) {
            return;
        }
        // 2.其他的，range做变更
        List<Integer> cityIds = deal.getCityIds();
        String oldRange = deal.getRange();
        CityInfo currentCity = cityServiceWrapper.getCityById(currentCityId);
        String newRange = DealFieldTransferHelper.produceRangeName(cityIds, currentCity, oldRange);
        deal.setRange(newRange);
    }

    /**
     * 修改最新通知.
     */
    private void alterVoice(DealModel deal) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.DealFieldChangerBiz.alterVoice(com.meituan.service.mobile.prometheus.model.DealModel)");
        // 变更逻辑
        //临时加入逻辑，price<5元，不参加活动,**********=Wed Jan  1 00:15:43 CST 2014
        if (deal.getPrice() > 0 && deal.getPrice() < 5 && System.currentTimeMillis() < **********000l) {
            String newVoice = "本单是特惠单，暂不支持代金券且不参加任何促销活动";
            if (StringUtils.isBlank(deal.getVoice())) {
                newVoice = newVoice + "\n" + deal.getVoice();
            }
            deal.setVoice(newVoice);
        }
        if (StringUtils.isNotBlank(deal.getVoice())) {
            deal.setVoice(HtmlUtils.format(deal.getVoice()));
        }
    }

    private void alterMenu(DealModel deal) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.DealFieldChangerBiz.alterMenu(com.meituan.service.mobile.prometheus.model.DealModel)");
        String oldMenu = deal.getMenu();
        if (StringUtils.isBlank(oldMenu) || "[[]]".equals(oldMenu)) {
            return;
        }

        JSONArray ja4newMenu = new JSONArray();
        try {
            JSONArray ja4menu = new JSONArray(oldMenu);
            for (int i = 0; i < ja4menu.length(); i++) {
                JSONArray ja4meal = ja4menu.getJSONArray(i);
                addSubtype4MealItem(ja4meal);
                ja4newMenu.put(ja4meal);
            }
        } catch (Exception e) {
            log.error("alterMenu() parse string to jsonarray error. menu=" + oldMenu, e);
        }
        deal.setMenu(ja4newMenu.toString());
    }

    private static final String MEALITEM_SUBTYPE_KEY = "subtype";
    private static final int SUBTYPE_MENU_TITLE = 0;
    private static final int SUBTYPE_MEAL_TITLE = 1;
    private static final int SUBTYPE_MEAL_ITEM = 2;
    private static final int SUBTYPE_MEAL_DESC = 4;

    private static final String MEALITEM_TYPE_KEY = "type";
    private static final String MEALITEM_CONTENT_KEY = "content";
    private static final String ROWTYPE_TITLE = "0";
    private static final String ROWTYPE_ITEM = "128";

    /**
     * 为每款套餐中项增加subtype字段.
     */
    private void addSubtype4MealItem(JSONArray ja4meal) throws JSONException {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.DealFieldChangerBiz.addSubtype4MealItem(org.json.JSONArray)");
        int len = ja4meal.length();
        for (int i = 0; i < len; i++) {
            JSONObject jo4item = ja4meal.getJSONObject(i);
            String rowType = jo4item.getString(MEALITEM_TYPE_KEY);
            if (ROWTYPE_ITEM.equals(rowType)) {
                jo4item.put(MEALITEM_SUBTYPE_KEY, SUBTYPE_MEAL_ITEM);
            } else if (ROWTYPE_TITLE.equals(rowType)) {
                if (0 == i && len > 2
                        && ROWTYPE_TITLE.equals(ja4meal.getJSONObject(i + 1).getString(MEALITEM_TYPE_KEY))) {
                    jo4item.put(MEALITEM_SUBTYPE_KEY, SUBTYPE_MENU_TITLE);
                } else if (len - 1 == i) {
                    jo4item.put(MEALITEM_SUBTYPE_KEY, SUBTYPE_MEAL_DESC);
                } else {
                    jo4item.put(MEALITEM_SUBTYPE_KEY, SUBTYPE_MEAL_TITLE);
                }
            }
        }

        // 对最后一个且type=“0”的项做特殊处理
        JSONObject last = ja4meal.getJSONObject(len - 1);
        String rowType = last.getString(MEALITEM_TYPE_KEY);
        if (StringUtils.isNotBlank(rowType) && ROWTYPE_TITLE.equals(rowType)) {
            String content = last.getString(MEALITEM_CONTENT_KEY);
            if (StringUtils.isNotBlank(content)) {
                String[] subcontents = content.split("\n");
                for (String subcontent : subcontents) {
                    String s = HtmlUtils.format(subcontent);
                    if (StringUtils.isNotBlank(s)) {
                        JSONObject jo4sub = new JSONObject();
                        jo4sub.put(MEALITEM_CONTENT_KEY, HtmlUtils.format(subcontent));
                        jo4sub.put(MEALITEM_TYPE_KEY, ROWTYPE_TITLE);
                        jo4sub.put(MEALITEM_SUBTYPE_KEY, SUBTYPE_MEAL_DESC);
                        ja4meal.put(jo4sub);
                    }
                }
            }
        }
    }
}
