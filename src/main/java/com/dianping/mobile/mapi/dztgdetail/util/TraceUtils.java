package com.dianping.mobile.mapi.dztgdetail.util;

import com.meituan.mtrace.Tracer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024-11-12
 * @desc 链路追踪工具类
 */
@Slf4j
public class TraceUtils {
    public static String getTraceId() {
        try {
            return Tracer.id();
        } catch (Exception e) {
            log.error("getTraceId error", e);
            return StringUtils.EMPTY;
        }
    }
}
