package com.dianping.mobile.mapi.dztgdetail.datatype.context;

import com.dianping.deal.sales.common.datatype.SalesDisplayDTO;
import com.dianping.deal.shop.dto.DealGroupShop;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedRecommendReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedRecommendVO;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.dianping.mobile.mapi.dztgdetail.common.model.RecommendShopBaseInfoModel;
import com.dianping.mobile.mapi.dztgdetail.common.model.RecommendUserBaseInfoModel;
import java.util.concurrent.Future;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Date: 2024/10/24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RelatedRecommendCtx {
    /**
     * request
     */
    RelatedRecommendReq req;
    /**
     * 团单信息
     */
    DealGroupDTO dealGroupDTO;
    /**
     * 门店信息
     */
    DpPoiDTO dpPoiDTO;

    /**
     * 门店基本信息（点评侧）
     */
    Map<Long, DpPoiDTO> shopIdDpPoiDTOMap;

    EnvCtx envCtx;

    /**
     * 关联最近门店信息
     */
    Map<Integer, DealGroupShop> dealGroupShopMap;

    /**
     * 团单ID映射
     */
    Map<Long, List<Long>> dpMtShopIds;

    /**
     * 价格
     */
    Map<Integer, PriceDisplayDTO> priceDisplayMap;

    /**
     * 销量
     */
    Map<Integer, SalesDisplayDTO> productId2SaleMap;

    /**
     * 团单信息
     */
    Map<Integer, DealGroupDTO> dealGroupDTOMap;

    /**
     * 返回结果
     */
    RelatedRecommendVO result;

    Map<String, String> relatedRecommendConfig;

    /**
     * 货架商品ID列表
     */
    List<Long> dealShelfDealIds;

    DealCtx dealCtx;

    /**
     * 查询标品
     */
    private Future querySpuThemeFuture;

    /**
     * 查询LE交叉推荐标题信息
     */
    private Future querLeCrossRecommendTitltInfoFuture;

    /**
     * 推荐门店基础信息
     */
    RecommendShopBaseInfoModel recommendShopBaseInfoModel;

    /**
     * 推荐用户基础信息
     */
    RecommendUserBaseInfoModel recommendUserBaseInfoModel;

    /**
     * 模块AB配置
     */
    private List<ModuleAbConfig> moduleAbConfigs;

    /**
     * 查询主题
     */
    private DealProductResult dealProductResult;

    private boolean needLog;
}
