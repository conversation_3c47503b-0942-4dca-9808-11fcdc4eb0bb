package com.dianping.mobile.mapi.dztgdetail.button.joy;
import java.util.Date;

import com.dianping.cat.Cat;
import org.apache.commons.lang.StringUtils;
import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BannerTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.SaleStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBanner;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.scrum.util.DateUtils;
import com.sankuai.general.product.query.center.client.util.DealGroupUtils;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys.*;
/**
 * <AUTHOR>
 * @date 2023/5/5
 */
public class WarmUpBannerBuilder extends AbstractButtonBuilder {
    @Override
    protected void doBuild(DealCtx context, ButtonBuilderChain chain) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.button.joy.WarmUpBannerBuilder.doBuild(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx,com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain)");
    }
    @Override
    public void build(DealCtx context, ButtonBuilderChain chain) {
        buildBanner(context);
        chain.build(context);
    }
    private void buildBanner(DealCtx context) {
        if (!GreyUtils.showWarmUpBanner(context)){
            return;
        }

        DealBuyBanner dealBuyBanner = buildQinziWarmUpDealBuyBanner(context);

        if (dealBuyBanner != null){
            context.getBuyBar().setBuyBanner(dealBuyBanner);
        }
    }
    private DealBuyBanner buildQinziWarmUpDealBuyBanner(DealCtx context) {
        String warmUpValue = AttributeUtils.getFirstValue(context.getAttrs(), WARM_UP_START_TIME);
        if(StringUtils.isBlank(warmUpValue)){
            return null;
        }

        DealBuyBanner dealBuyBanner = new DealBuyBanner();
        Date warmUpTimePoint = DealGroupUtils.convertString2Date(warmUpValue);
        Date now = DateUtils.currentDate();
        Date beginDate = context.getDealGroupBase().getBeginDate();
        if(!warmUpTimePoint.after(now) && beginDate.after(now)){
            dealBuyBanner.setBannerType(BannerTypeEnum.WarmUp.getType());
            dealBuyBanner.setShow(true);
            dealBuyBanner.setCountDownTs(beginDate.getTime());
            context.setSaleStatus(SaleStatusEnum.COMING_SOON.saleStatusName);
        }

        return dealBuyBanner;
    }
}