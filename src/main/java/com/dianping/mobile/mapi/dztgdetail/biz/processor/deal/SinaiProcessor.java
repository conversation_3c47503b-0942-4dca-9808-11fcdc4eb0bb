package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.geo.map.entity.GeoPoint;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.LionUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.service.GeoBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperCacheWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiClientWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiShopCategoryWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.QueryParams;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.LocationHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.PhoneNumberHelper;
import com.dianping.mobile.mapi.dztgdetail.util.GEOUtils;
import com.dianping.tuangou.dztg.bjwrapper.api.dto.MtPoiDto;
import com.google.common.util.concurrent.SettableFuture;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDisplayShopDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Future;

@Slf4j
public class SinaiProcessor extends AbsDealProcessor {

    @Autowired
    private PoiShopCategoryWrapper poiShopCategoryWrapper;

    @Resource
    private PoiClientWrapper poiClientWrapper;

    @Autowired
    private GeoBiz geoBiz;

    @Autowired
    private MapperCacheWrapper mapperCacheWrapper;

    @Autowired
    private DealGroupWrapper dealGroupWrapper;

    public static final List<String> SINAI_DP_POI_FIELDS_BEST_SHOP = Lists.newArrayList("dpPoiId", "shopId",
            "hospitalInfo", "useType","shopName", "shopType", "branchName", "normPhones", "shopPower", "address",
            "crossRoad", "lat", "lng", "defaultPic","backMainCategoryPath", "cityId", "power", "businessHours",
            "avgPrice", "fiveScore", "mainRegionName", "appSides");

    @Override
    public void prepare(DealCtx ctx) {
        // 插入门店数量查询
        int dealGroupId = ctx.isMt() ? ctx.getMtId() : ctx.getDpId();
        Future<?> future = dealGroupWrapper.preDealShopQtyBySearchFuture(dealGroupId, ctx.isMt());
        ctx.getFutureCtx().setDealShopQtyBySearchFuture(future);

        //判断是否要填充bestShop的信息
        SettableFuture dpPoiDtoFuture;
        if(ctx.isNeedFillBestShop()) {
            dpPoiDtoFuture = poiShopCategoryWrapper.preDpPoiDto(ctx.getDpLongShopId(), SINAI_DP_POI_FIELDS_BEST_SHOP);
            if(ctx.getMtLongShopId() > 0) {
                Future mtPoiShowTypeDTOFuture = poiClientWrapper.preMtPoiWrappers(ctx.getMtLongShopId());
                ctx.getFutureCtx().setMtShowTypeDTOFuture(mtPoiShowTypeDTOFuture);
            }
        } else {
            //SINAI_DP_POI_FIELDS_BEST_SHOP完全包含SINAI_DP_POI_FIELDS，不知道这里为啥要做区分
            dpPoiDtoFuture = poiShopCategoryWrapper.preDpPoiDto(ctx.getDpLongShopId(), QueryParams.SINAI_DP_POI_FIELDS);
        }
        ctx.getFutureCtx().setDpPoiDtoFuture(dpPoiDtoFuture);
        if (ctx.isMt()) {
            Future mtPoiDTOFuture = poiClientWrapper.preMtPoiDTO(ctx.getMtLongShopId(), Lists.newArrayList("mtAvgScore","typeHierarchy", "frontCateHierarchy"));
            ctx.getFutureCtx().setMtPoiDTOFuture(mtPoiDTOFuture);
        }else{
            fulfillMtShopId(ctx);
        }
    }

    @Override
    public void process(DealCtx ctx) {
        DpPoiDTO dpPoiDTO = poiShopCategoryWrapper.queryDpPoiDto(ctx.getFutureCtx().getDpPoiDtoFuture());
        ctx.setDpPoiDTO(dpPoiDTO);
        if(ctx.isNeedFillBestShop() && !ctx.isPreviewDeal() && dpPoiDTO != null) {
            //填充BestShop数据
            fillBestShopInfo(dpPoiDTO, ctx);
        }
        Map<Long, MtPoiDTO> mtPoiDTOMap = poiClientWrapper.getMtPoiDTOByFuture(ctx.getFutureCtx().getMtPoiDTOFuture());
        if (MapUtils.isNotEmpty(mtPoiDTOMap)) {
            ctx.setMtPoiDTO(mtPoiDTOMap.get(ctx.getMtLongShopId()));
        }
    }

    private void fillBestShopInfo(DpPoiDTO dpPoiDTO, DealCtx ctx) {
        if ( Objects.isNull(dpPoiDTO) || Objects.isNull(ctx.getBestShopResp())) {
            return;
        }
        BestShopDTO bestShop = ctx.getBestShopResp();
        bestShop.setShopName(dpPoiDTO.getShopName());
        bestShop.setShopType(Optional.ofNullable(dpPoiDTO.getShopType()).orElse(0));
        bestShop.setBranchName(dpPoiDTO.getBranchName());
        bestShop.setPhoneNos(PhoneNumberHelper.format(dpPoiDTO.getNormPhones()));
        bestShop.setShopPower(Objects.nonNull(dpPoiDTO.getShopPower()) ? dpPoiDTO.getShopPower() : 0);
        bestShop.setAddress(generateAddr(dpPoiDTO.getAddress(), dpPoiDTO.getCrossRoad()));
        double shopGLat = dpPoiDTO.getLat() != null ? dpPoiDTO.getLat() : 0;
        double shopGLng = dpPoiDTO.getLng() != null ? dpPoiDTO.getLng() : 0;
        bestShop.setGlat(shopGLat);
        bestShop.setGlng(shopGLng);
        // todo xiangrui 查询门店数量
        DealGroupDisplayShopDTO dealGroupDisplayShops = Optional.ofNullable(ctx.getDealGroupDTO()).map(DealGroupDTO::getDisplayShopInfo).orElse(null);
        if(ctx.isMt()) {
            if(null != dealGroupDisplayShops && CollectionUtils.isNotEmpty(dealGroupDisplayShops.getMtDisplayShopIds())) {
                bestShop.setTotalShopsNum(dealGroupDisplayShops.getMtDisplayShopIds().size());
            }
            List<MtPoiDto> mtPoiDtos = poiClientWrapper.getFutureResult(ctx.getFutureCtx().getMtShowTypeDTOFuture());
            if (CollectionUtils.isNotEmpty(mtPoiDtos) && mtPoiDtos.get(0) != null) {
                bestShop.setShowType(mtPoiDtos.get(0).getShowType());
            }
        } else {
            if(null != dealGroupDisplayShops && CollectionUtils.isNotEmpty(dealGroupDisplayShops.getDpDisplayShopIds())) {
                bestShop.setTotalShopsNum(dealGroupDisplayShops.getDpDisplayShopIds().size());
            }
        }
        // 如果查询到了新的商户数据量，则用新的替代
        long dealShopQtyBySearch = dealGroupWrapper.getDealShopQtyBySearch(ctx.getFutureCtx().getDealShopQtyBySearchFuture(), ctx.isMt() ? ctx.getMtId() : ctx.getDpId());
        if (LionUtils.dealDisplayShopQtySwitch() && dealShopQtyBySearch > 0) {
            Cat.logEvent("SinaiProcessor", "dealDisplayShopQtySwitch");
            bestShop.setTotalShopsNum((int) dealShopQtyBySearch);
        }

        if(LocationHelper.isValidPoint(ctx.getUserlat(), ctx.getUserlng())) {
            bestShop.setDistance(GEOUtils.distanceStr(
                    bestShop.getGlat(),bestShop.getGlng(),ctx.getUserlat(),ctx.getUserlng()));
        }
        //这里有串行，但是TP99=6ms
        GeoPoint wgsPoint = geoBiz.googleToGps(new GeoPoint(bestShop.getGlat(),bestShop.getGlng()));
        if(wgsPoint != null){
            bestShop.setLat(wgsPoint.getLat());
            bestShop.setLng(wgsPoint.getLng());
        }

        bestShop.setShopPic(getOriginPic(dpPoiDTO.getDefaultPic()));
    }

    private String generateAddr(String addr, String crossRoad) {
        if (StringUtils.isNotEmpty(crossRoad)) {
            return addr + "(" + crossRoad + ")";
        }
        return addr;
    }

    public void fulfillMtShopId(DealCtx ctx) {
        if (ctx.getMtLongShopId() <= 0) {
            //团详2.0前置链路做了id转换，这里默认有，主要针对团详1.0的流量
            Cat.logEvent("SinaiProcessor", "empty_mtShopId");
            ctx.setMtLongShopId(mapperCacheWrapper.fetchMtShopId(ctx.getDpLongShopId()));
        }
    }

    private String getOriginPic(String origin) {
        if (StringUtils.isBlank(origin)) {
            return "";
        }
        if (!origin.contains("%40")) {
            return origin;
        }
        String[] list = origin.split("%40");
        return list.length < 2 ? origin : list[0] + "%40300w_0e_1l";
    }

}