package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.action.extend.none;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.BottomBarActionDataVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.enums.BottomBarActionTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: guang<PERSON><PERSON><PERSON>
 * @Date: 2025/3/16 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DoNothingActionVO extends BottomBarActionDataVO {

    private final int actionType = BottomBarActionTypeEnum.NOTHING.getCode();

}
