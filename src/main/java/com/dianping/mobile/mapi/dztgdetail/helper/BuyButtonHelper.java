package com.dianping.mobile.mapi.dztgdetail.helper;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.merchantcard.timescard.exposure.dto.CardSummaryBarDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class BuyButtonHelper {

    public static boolean isValidTimesCard(DealCtx ctx) {
        CardSummaryBarDTO timesCard = ctx.getTimesCard();
        // 预订场景不展示次卡
        if(DealCtxHelper.isPreOrderDeal(ctx)) {
            return false;
        }

        if (timesCard == null || timesCard.getPrice() == null || timesCard.getTimes() <= 0) {
            return false;
        }

        if (isDaoGua(timesCard.getPrice(), ctx)) {
            log.debug("团单id={},次卡倒挂,次卡={},常规价格={}", ctx.getDealId4P(), JSON.toJSONString(timesCard), JSON.toJSONString(PriceHelper.getNormalPrice(ctx)));
            return false;
        }

        return true;
    }

    public static BigDecimal getTimesCardTruthPrice(DealCtx ctx) {
        CardSummaryBarDTO timesCard = ctx.getTimesCard();
        if (timesCard == null || timesCard.getPrice() == null || timesCard.getTimes() <= 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal timesCardTruthPrice = timesCard.getPrice();
        return timesCardTruthPrice.setScale(2, RoundingMode.CEILING);
    }

    public static boolean isValidPinPool(DealCtx ctx) {
        PinProductBrief pinProductBrief = ctx.getPinProductBrief();

        if (pinProductBrief == null || pinProductBrief.getProductId() <= 0) {
            return false;
        }

        if (pinProductBrief.isSoldOut() || pinProductBrief.getPrice() == null || pinProductBrief.getPrice().doubleValue() <= 0) {
            return false;
        }

        if (StringUtils.isNotEmpty(pinProductBrief.getUrl())) {
            BigDecimal pinPoolPrice = pinProductBrief.getPrice();

            if (ctx.getPinPoolPromoAmount() != null && ctx.getPinPoolPromoAmount().compareTo(BigDecimal.ZERO) > 0) {
                pinPoolPrice = pinPoolPrice.subtract(ctx.getPinPoolPromoAmount()).setScale(1, RoundingMode.CEILING);
            }

            if (isDaoGua(pinPoolPrice, ctx)) {

                log.debug("团单id={},拼团倒挂,拼团={},拼团优惠={},常规价格={}",
                        ctx.getDealId4P(), JSON.toJSONString(pinProductBrief), ctx.getPinPoolPromoAmount(), JSON.toJSONString(PriceHelper.getNormalPrice(ctx)));
                return false;

            } else {
                return true;
            }
        }

        return false;
    }

    /**
     * 是否启用价格治理逻辑
     * @param ctx
     * @return
     */
    public static boolean enablePriceZhiLi(DealCtx ctx) {

        if (ctx == null || ctx.getChannelDTO() == null) {
            return false;
        }

        List<String> configs = Lion.getList(LionConstants.PRICE_ZHILI_CATEGORIES, String.class, new ArrayList<>());

        //配置为空表示全量启用价格治理
        if (CollectionUtils.isEmpty(configs)) {
            return true;
        }

        String category = String.valueOf(ctx.getChannelDTO().getCategoryId());

        if (ctx.isMt()) {
            category = "mt" + category;
        } else {
            category = "dp" + category;
        }

        return configs.contains(category);
    }


    /**
     * 是否倒挂
     * @param price
     * @param ctx
     * @return
     */
    public static boolean isDaoGua(BigDecimal price, DealCtx ctx) {

        /**
         * 足疗市场价button的PK先不考虑价格倒挂，在后续PK逻辑中处理
         */
        if (ctx.getPriceContext().isZuLiaoButtonNewStyle()) {
            return false;
        }

        if (!BuyButtonHelper.enablePriceZhiLi(ctx)) {
            return false;
        }

        PriceDisplayDTO normalPrice = PriceHelper.getNormalPrice(ctx);

        return price.compareTo(normalPrice.getPrice()) >= 0;
    }

}
