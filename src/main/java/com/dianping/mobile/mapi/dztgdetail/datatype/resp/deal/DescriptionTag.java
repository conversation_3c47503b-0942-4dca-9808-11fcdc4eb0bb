package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

@MobileDo(id = 0xe0ec)
public class DescriptionTag implements Serializable {
    /**
     * 描述信息类型
     */
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;
    /**
     * 描述信息浮层
     */
    @MobileDo.MobileField(key = 0xa83b)
    private DescriptionDetail detail;

    /**
     * 标签名
     */
    @MobileDo.MobileField(key = 0x7ab8)
    private String name;

    /**
     * 文字后图片（url）
     */
    @MobileDo.MobileField(key = 0xb464)
    private String postPic;

    /**
     * 文字前图片（url）
     */
    @MobileDo.MobileField(key = 0x9a73)
    private String prePic;

    /**
     * 背景颜色
     */
    @MobileDo.MobileField(key = 0xba62)
    private String backgroundColor;

    /**
     * 文案颜色
     */
    @MobileDo.MobileField(key = 0xeead)
    private String textColor;

    /**
     * 圆角
     */
    @MobileDo.MobileField(key = 0xabb4)
    private int borderRadius;

    /**
     * 有没有边框
     */
    @MobileDo.MobileField(key = 0xc51b)
    private boolean hasBorder;

    /**
     * 标签边框颜色
     */
    @MobileDo.MobileField(key = 0xad82)
    private String borderColor;

    /**
     * 图与文字之间间距
     */
    @MobileDo.MobileField(key = 0x2d9d)
    private boolean noGapBetweenPicText;

    @MobileDo.MobileField(key = 0x431)
    private String shapeType;
    public DescriptionDetail getDetail() {
        return detail;
    }

    public void setDetail(DescriptionDetail detail) {
        this.detail = detail;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPostPic() {
        return postPic;
    }

    public void setPostPic(String postPic) {
        this.postPic = postPic;
    }

    public String getPrePic() {
        return prePic;
    }

    public void setPrePic(String prePic) {
        this.prePic = prePic;
    }

    public String getBackgroundColor() {
        return backgroundColor;
    }

    public void setBackgroundColor(String backgroundColor) {
        this.backgroundColor = backgroundColor;
    }

    public String getTextColor() {
        return textColor;
    }

    public void setTextColor(String textColor) {
        this.textColor = textColor;
    }

    public int getBorderRadius() {
        return borderRadius;
    }

    public void setBorderRadius(int borderRadius) {
        this.borderRadius = borderRadius;
    }

    public boolean getHasBorder() {
        return hasBorder;
    }

    public void setHasBorder(boolean hasBorder) {
        this.hasBorder = hasBorder;
    }

    public String getBorderColor() {
        return borderColor;
    }

    public void setBorderColor(String borderColor) {
        this.borderColor = borderColor;
    }

    public boolean getNoGapBetweenPicText() {
        return noGapBetweenPicText;
    }

    public void setNoGapBetweenPicText(boolean noGapBetweenPicText) {
        this.noGapBetweenPicText = noGapBetweenPicText;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getShapeType() {
        return shapeType;
    }

    public void setShapeType(String shapeType) {
        this.shapeType = shapeType;
    }
}
