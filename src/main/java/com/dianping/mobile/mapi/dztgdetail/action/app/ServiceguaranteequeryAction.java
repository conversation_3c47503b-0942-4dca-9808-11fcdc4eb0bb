package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.CatEvents;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.ServiceguaranteequeryRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ServiceGuarantee.ServiceGuaranteeDTO;
import com.dianping.mobile.mapi.dztgdetail.facade.ServiceGuaranteeQueryFacade;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;


/**
 * <AUTHOR>
 * @date 16/11/2022
 * @time 11:43
 * 移动之家链接：https://mobile.sankuai.com/studio/api/19220/index
 */

@InterfaceDoc(
        displayName = "到综团单服务保障查询",
        type = "restful",
        description = "到综团单服务保障查询",
        scenarios = "到综团单服务保障查询",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/serviceguaranteequery.bin",
        authors = "zhouzhixiang"
)
@Controller(value = "serviceguaranteequery.bin")
@Action(url = "serviceguaranteequery.bin", httpType = "get", protocol = {ReqProtocol.MAPI})
public class ServiceguaranteequeryAction extends AbsAction<ServiceguaranteequeryRequest> {

    @Autowired
    private ServiceGuaranteeQueryFacade serviceGuaranteeQueryFacade;

    @Override
    protected IMobileResponse validate(ServiceguaranteequeryRequest request, IMobileContext context) {
        IdUpgradeUtils.processProductIdForServiceguaranteequeryRequest(request, "serviceguaranteequery.bin");
        if (request.getDealgroupId() == null
                || request.getDealgroupId() <= 0
                || request.getShopidstr() == null) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "serviceguaranteequery.bin",
            displayName = "到综团单服务保障查询",
            description = "到综团单服务保障查询",
            returnValueDescription = "服务保障数据类型",
            restExampleUrl = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "接口serviceguaranteequery.bin的请求参数",
                            type = ServiceguaranteequeryRequest.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "iMobileResponse", description = "通用返回数据模型")},
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "{鉴权逻辑，请自行填写} "
                    ),
            }
    )
    @Override
    @CryptMethod
    protected IMobileResponse execute(ServiceguaranteequeryRequest request, IMobileContext context) {
        EnvCtx envCtx = initEnvCtx(context);
        try {
            ServiceGuaranteeDTO serviceGuaranteeDTO = serviceGuaranteeQueryFacade.queryServiceGuaranteeDTO(request, envCtx);
            return new CommonMobileResponse(serviceGuaranteeDTO);
        } catch (Exception e) {
            logger.error("serviceguaranteequery.bin error", e);
        }
        Cat.logMetricForCount(CatEvents.DEALBASE_FAIL);
        return Resps.SYSTEM_ERROR;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
