package com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.extend;

import com.dianping.mobile.mapi.dztgdetail.common.enums.PromoDetailEnum;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class PromoDetailLocator implements ApplicationContextAware {
    private static final Map<PromoDetailEnum, PromoDetailHandler> PROMO_DETAIL_HANDLER_LOCATOR_MAP = new HashMap<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        applicationContext
                .getBeansOfType(PromoDetailHandler.class)
                .values()
                .forEach(promoDetailHandler -> PROMO_DETAIL_HANDLER_LOCATOR_MAP.put(promoDetailHandler.getPromoDetailEnum(), promoDetailHandler));
    }

    public static PromoDetailHandler getByEnum(PromoDetailEnum promoDetailEnum) {
        return PROMO_DETAIL_HANDLER_LOCATOR_MAP.get(promoDetailEnum);
    }
}
