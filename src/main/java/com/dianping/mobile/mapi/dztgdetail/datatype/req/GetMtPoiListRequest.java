package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.dianping.mobile.mapi.dztgdetail.common.enums.PoiSortEnum;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.DecryptedField;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@TypeDoc(description = "getmtpoilist.bin接口请求参数")
@MobileRequest
public class GetMtPoiListRequest implements IMobileRequest, Serializable {
    @FieldDoc(description = "经度")
    @MobileRequest.Param(name = "lng")
    private Double lng;
    @FieldDoc(description = "纬度")
    @MobileRequest.Param(name = "lat")
    private Double lat;
    @Deprecated
    @FieldDoc(description = "商户id")
    @MobileRequest.Param(name = "poiid")
    private Integer poiId;
    @MobileRequest.Param(name = "poiIdEncrypt")
    @DecryptedField(targetFieldName = "poiId")
    private String poiIdEncrypt;
    @FieldDoc(description = "商户id")
    @MobileRequest.Param(name = "poiIdStr")
    private String poiIdStr;
    @MobileRequest.Param(name = "poiIdStrEncrypt")
    @DecryptedField(targetFieldName = "poiIdStr")
    private String poiIdStrEncrypt;
    @FieldDoc(description = "是否只显示当前城市门店")
    @MobileRequest.Param(name = "onlycurrentcitypois")
    private Boolean onlyCurrentCityPois;
    @FieldDoc(description = "单页显示限制")
    @MobileRequest.Param(name = "limit")
    private Integer limit;
    @FieldDoc(description = "分页偏移量")
    @MobileRequest.Param(name = "start")
    private Integer start;
    @FieldDoc(description = "城市id")
    @MobileRequest.Param(name = "cityid")
    private Integer cityId;
    @FieldDoc(description = "团单id")
    @MobileRequest.Param(name = "dealid")
    private Integer dealId;
    @FieldDoc(description = "排序策略")
    @MobileRequest.Param(name = "sort")
    private String sort;

    public Double getLng() {
        return lng == null ? 0 : lng;
    }

    public void setLng(Double lng) {
        this.lng = lng;
    }

    public Double getLat() {
        return lat == null ? 0 : lat;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }

    public Integer getPoiId() {
        return poiId == null ? 0 : poiId;
    }

    public void setPoiId(Integer poiId) {
        this.poiId = poiId;
    }

    public String getPoiIdStr() {
        return poiIdStr;
    }

    public void setPoiIdStr(String poiIdStr) {
        this.poiIdStr = poiIdStr;
    }

    public Boolean getOnlyCurrentCityPois() {
        return onlyCurrentCityPois == null ? Boolean.TRUE : onlyCurrentCityPois;
    }

    public void setOnlyCurrentCityPois(Boolean onlyCurrentCityPois) {
        this.onlyCurrentCityPois = onlyCurrentCityPois;
    }

    public Integer getLimit() {
        return limit == null ? 0 : limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getStart() {
        return start == null ? 0 : start;
    }

    public void setStart(Integer start) {
        this.start = start;
    }

    public Integer getCityId() {
        return cityId == null ? 0 : cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getDealId() {
        return dealId == null ? 0 : dealId;
    }

    public void setDealId(Integer dealId) {
        this.dealId = dealId;
    }

    public String getSort() {
        return sort == null ? PoiSortEnum.DISTANCE.getValue() : sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public String getPoiIdEncrypt() {
        return poiIdEncrypt;
    }

    public void setPoiIdEncrypt(String poiIdEncrypt) {
        this.poiIdEncrypt = poiIdEncrypt;
    }

    public String getPoiIdStrEncrypt() {
        return poiIdStrEncrypt;
    }

    public void setPoiIdStrEncrypt(String poiIdStrEncrypt) {
        this.poiIdStrEncrypt = poiIdStrEncrypt;
    }

    public Long getPoiIdLong() {
        if (StringUtils.isNumeric(poiIdStr)) {
            return Long.parseLong(poiIdStr);
        } else if(poiId != null) {
            return poiId.longValue();
        } else {
            return 0L;
        }
    }
}
