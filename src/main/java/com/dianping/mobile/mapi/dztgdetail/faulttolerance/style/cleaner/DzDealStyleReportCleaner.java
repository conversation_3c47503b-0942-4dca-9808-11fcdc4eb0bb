package com.dianping.mobile.mapi.dztgdetail.faulttolerance.style.cleaner;

import com.dianping.cat.Cat;
import com.dianping.mobile.framework.base.datatypes.HttpCode;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealStyleBO;
import com.google.gson.Gson;
import com.sankuai.athena.stability.faulttolerance.execution.FaultToleranceExecutionCtx;
import com.sankuai.athena.stability.faulttolerance.mirror.MirrorConfiguration;
import com.sankuai.athena.stability.faulttolerance.mirror.fetch.MirrorFetchCleaner;
import com.sankuai.athena.stability.faulttolerance.mirror.report.MirrorReportCleaner;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-12-06
 * @desc 团详样式上报信息处理器
 */
@Component
public class DzDealStyleReportCleaner extends MirrorConfiguration implements MirrorReportCleaner, MirrorFetchCleaner {
    @Override
    public <R> MirrorReportCleaner<R> getCleaner() {
        return this;
    }

    @Override
    public <P, R> MirrorFetchCleaner<P, R> getFetchCleaner() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.faulttolerance.style.cleaner.DzDealStyleReportCleaner.getFetchCleaner()");
        return this;
    }

    @Override
    public Object clean(FaultToleranceExecutionCtx ctx, Object result) {
        if (result instanceof CommonMobileResponse) {
            CommonMobileResponse response = (CommonMobileResponse) result;
            if (!Objects.equals(response.getStatusCode().getCode(), HttpCode.HTTPOK.getCode())) {
                return null;
            }
            Gson gson = new Gson();
            DealStyleBO dealStyleBO = gson.fromJson(gson.toJson(response.getData()), DealStyleBO.class);
            return new CommonMobileResponse(dealStyleBO, HttpCode.HTTPOK);
        }
        return null;
    }

    @Override
    public Object clean(Object request, Object result) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.faulttolerance.style.cleaner.DzDealStyleReportCleaner.clean(java.lang.Object,java.lang.Object)");
        if (result instanceof CommonMobileResponse) {
            CommonMobileResponse response = (CommonMobileResponse) result;
            response.setStatusCode(HttpCode.HTTPOK);
            return response;
        }
        return result;
    }
}
