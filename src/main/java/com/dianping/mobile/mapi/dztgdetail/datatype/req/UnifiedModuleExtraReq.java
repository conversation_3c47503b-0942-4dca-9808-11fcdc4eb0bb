package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.annotation.MobileRequest.Param;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/12/13.
 */
@Data
@TypeDoc(description = "团购详情更多定制化模块请求")
@MobileRequest
public class UnifiedModuleExtraReq implements IMobileRequest {

    @Deprecated
    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，原int类型")
    @Param(name = "dealgroupid")
    private Integer dealGroupId;

    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，string类型")
    @Param(name = "stringdealgroupid")
    private String stringDealGroupId;

    @FieldDoc(description = "团单城市ID", rule = "美团平台为美团城市ID，点评平台为点评城市ID")
    @Param(name = "cityid")
    private Integer cityId;

    @FieldDoc(description = "实验结果")
    @Param(name = "expresults")
    private String expResults;

    @FieldDoc(description = "前端mrn版本")
    @Param(name = "mrnversion")
    private String mrnVersion;

    @FieldDoc(description = "请求来源")
    @Param(name = "pagesource")
    private String pageSource;


    public Integer getDealGroupId() {
        return dealGroupId == null ? 0 : dealGroupId;
    }

    public void setDealGroupId(Integer dealGroupId) {
        this.dealGroupId = dealGroupId;
    }

    public Integer getCityId() {
        return cityId == null ? 0 : cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getExpResults() {
        return expResults;
    }

    public void setExpResults(String expResults) {
        this.expResults = expResults;
    }

    public String getMrnVersion() {
        return mrnVersion;
    }

    public void setMrnVersion(String mrnVersion) {
        this.mrnVersion = mrnVersion;
    }

    public String getPageSource() {
        return pageSource;
    }

    public void setPageSource(String pageSource) {
        this.pageSource = pageSource;
    }
}
