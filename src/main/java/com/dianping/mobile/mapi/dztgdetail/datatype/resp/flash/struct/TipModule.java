package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.struct;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@MobileDo(id = 0xa2fd)
public class TipModule implements Serializable {

    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @MobileDo.MobileField(key = 0x8535)
    private List<String> contents;

    @MobileDo.MobileField(key = 0x8ad0)
    private String button;

}
