package com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/10/24
 */
@Data
@TypeDoc(description = "沉浸页筛选项展示层对象")
@MobileDo(id = 0xb6e2)
public class ImmersiveImageFilterVO {
    /**
     * 筛选项
     */
    @MobileDo.MobileField(key = 0xd75c)
    private List<ImmersiveSelectItemVO> filterList;
    /**
     * 标题，考虑到前端渲染次序不适合放在列表页接口
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;
}
