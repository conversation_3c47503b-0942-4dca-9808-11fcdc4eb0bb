package com.dianping.mobile.mapi.dztgdetail.biz.dealnotice;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiClientWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.constants.QueryParams;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealNoticeLayerCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealNoticeLayerReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.Guarantee;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealnotice.DealNoticeLayerPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealnotice.GeneralLayerConfig;
import com.dianping.mobile.mapi.dztgdetail.facade.MapWrapper;
import com.dianping.mobile.mapi.dztgdetail.util.PoiShopUtil;
import com.dianping.mobile.mapi.dztgdetail.util.UrlUtils;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2024/9/30 16:53
 */
@Component
@Slf4j
public class CommunityHealthToHomeNoticeProcessor extends AbstractDealNoticeProcessor{

    public static final String DP_ADDRESS_LINK = "https://m.dianping.com/life/node/html/address.html?from=easy_life_v2&currentAddressName=%s&source=undefined&version_name=undefined&notitlebar=1&latitude=0.0&longitude=0.0&cityid=%s&dpid=%s&token=";
    public static final String MT_ADDRESS_LINK = "http://i.meituan.com/life/node/html/address.html?from=easy_life_v2&currentAddressName=%s&source=undefined&version_name=undefined&notitlebar=1&&lat=%s&lng=%s&ci=%s&uuid=%s";

    private final String DP_ADDRESS_LINK_LION = "com.sankuai.dzu.tpbase.dztgdetailweb.deal.detail.choose.addresslink.dp";
    private final String MT_ADDRESS_LINK_LION = "com.sankuai.dzu.tpbase.dztgdetailweb.deal.detail.choose.addresslink.mt";
    private final String IN_SERVICE = "可上门";
    private final String EXCEED_SERVICE = "超出上门范围";
    private final int CATEGORY_ID = 1631;
    private final String FARTHEST_SERVICE_RANGE = "Farthest_service_range";

    /**
     * 距离单位km
     */
    private final int UNIT = 1000;



    @Resource
    private PoiClientWrapper poiClientWrapper;

    @Autowired
    private MapperWrapper mapperWrapper;
    
    @Resource
    private MapWrapper mapWrapper;


    @Override
    public boolean valid(DealNoticeLayerCtx ctx) {
        DealGroupDTO dealGroupDTO = getDealGroupDTO(ctx.getDealGroupId(), ctx);
        ctx.setDealGroupDTO(dealGroupDTO);
        // 判断逻辑：
        // 团单分类：二级分类=“上门医护”的团单
        // 团购字段：“是否支持上门服务”为“是”
        if (Objects.nonNull(dealGroupDTO) && Objects.nonNull(dealGroupDTO.getCategory()) && Objects.nonNull(dealGroupDTO.getCategory().getCategoryId())
                && dealGroupDTO.getCategory().getCategoryId() == CATEGORY_ID){
            return true;
        }
        return false;
    }

    @Override
    public DealNoticeLayerPBO getDealNoticeLayerPBO(DealNoticeLayerCtx ctx) {
        DpPoiDTO poiDTO = getShopInfo(ctx);
        if (Objects.isNull(poiDTO)){
            return null;
        }

        // 获取当前地址
        String position = mapWrapper.getUserAddress(!ctx.getEnvCtx().isMt(), ctx.getUserLng(), ctx.getUserLat());
        if (StringUtils.isBlank(position)){
            return null;
        }
        List<String> displayValues = Lists.newArrayList();
        displayValues.add(position);

        DealNoticeLayerPBO result = buildDealNoticeLayerPBO("上门",
                displayValues,
                buildDisplayTags(ctx.getRequest(),ctx.getDealGroupDTO(), poiDTO),
                buildLayer(ctx.getRequest(), ctx.getEnvCtx(), position));
        return result;
    }

    public DealNoticeLayerPBO buildDealNoticeLayerPBO(String moduleName, List<String> displayValues, List<Guarantee> tags, GeneralLayerConfig generalLayerConfig){
        DealNoticeLayerPBO result = new DealNoticeLayerPBO();
        // 模块名称 上门
        result.setModuleName(moduleName);
        // 地址：
        result.setDisplayValues(displayValues);
        // 可上门/超出上门范围
        result.setTags(tags);
        // 浮层，跳链
        result.setLayer(generalLayerConfig);
        return result;
    }


    public DpPoiDTO getShopInfo(DealNoticeLayerCtx ctx){
        Long shopId = ctx.getPoiid();
        if (ctx.getEnvCtx().isMt()) {
            // 如果是美团侧，转换为点评ID
            shopId = mapperWrapper.getDpByMtShopId(ctx.getPoiid());
        }
        // 获取门店基本信息 id是点评的id
        if (Objects.nonNull(shopId)) {
            return poiClientWrapper.getDpPoiDTO(shopId, QueryParams.SINAI_DP_POI_FIELDS);
        }
        return null;
    }

    /**
     * 构建地址选择跳链
     */
    public String buildAddressSelectLink(DealNoticeLayerReq request, EnvCtx envCtx, String position) {
        try {
            if (envCtx.isMt()) {
                String link = Lion.getString(LionConstants.APP_KEY, MT_ADDRESS_LINK_LION, MT_ADDRESS_LINK);
                return UrlUtils.encodeMtWebViewUrl(String.format(link, URLEncoder.encode(position, "UTF-8"),
                        request.getUserlat(), request.getUserlng(), request.getCityid(), envCtx.getAppDeviceId() == null ? "" : envCtx.getAppDeviceId()));
            } else {
                String link = Lion.getString(LionConstants.APP_KEY, DP_ADDRESS_LINK_LION, DP_ADDRESS_LINK);
                return UrlUtils.encodeDpWebViewUrl(String.format(link, URLEncoder.encode(position, "UTF-8"),
                        request.getCityid(), envCtx.getAppDeviceId() == null ? "" : envCtx.getAppDeviceId()));
            }
        } catch (Exception e) {
            log.error("buildAddressSelectLink exception：", e);
            return "";
        }
    }
    
    public List<Guarantee> buildDisplayTags(DealNoticeLayerReq request, DealGroupDTO dealGroupDTO, DpPoiDTO poiDTO){
        List<Guarantee> tags = Lists.newArrayList();
        String serviceTag = getServiceTag(request, dealGroupDTO, poiDTO);
        if (StringUtils.isBlank(serviceTag)){
            return null;
        }
        Guarantee tag = Guarantee.builder()
                .text(serviceTag)
                .build();
        tags.add(tag);
        return tags;
    }

    public String getServiceTag(DealNoticeLayerReq request, DealGroupDTO dealGroupDTO, DpPoiDTO dpPoiDTO){
        // 获取 用户和门店的距离
        double userDistance = PoiShopUtil.getDistance(request.getUserlng(), request.getUserlat(), dpPoiDTO.getLng(), dpPoiDTO.getLat());
        if (CollectionUtils.isEmpty(dealGroupDTO.getAttrs())){
            return StringUtils.EMPTY;
        }
        AttrDTO farthestServiceRangeAttr = dealGroupDTO.getAttrs().stream()
                .filter(attr -> attr.getName().equals(FARTHEST_SERVICE_RANGE))
                .findFirst().orElse(null);
        if (Objects.isNull(farthestServiceRangeAttr) || CollectionUtils.isEmpty(farthestServiceRangeAttr.getValue())){
            return StringUtils.EMPTY;
        }
        String farthestServiceRangeStr =  farthestServiceRangeAttr.getValue().stream().findFirst().get();
        double farthestServiceRange = Double.parseDouble(farthestServiceRangeStr);
        if (userDistance <= farthestServiceRange * UNIT ){
            return IN_SERVICE;
        }
        return EXCEED_SERVICE;
    }
    
    public GeneralLayerConfig buildLayer(DealNoticeLayerReq request, EnvCtx envCtx, String position){
        String jumpUrl = buildAddressSelectLink(request, envCtx, position);
        if (StringUtils.isBlank(jumpUrl)){
            return null;
        }
        GeneralLayerConfig layer = new GeneralLayerConfig();
        // 门店跳链
        layer.setJumpUrl(jumpUrl);
        return layer;
    }


}
