package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.cat.Cat;
import com.dianping.deal.base.dto.DealBaseDTO;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.deal.detail.enums.PlatformEnum;
import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.service.bo.DrivingPoi;
import com.dianping.mobile.mapi.dztgdetail.common.ShopUuidUtils;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Constants;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DealParamEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MpAppIdEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.PriceContext;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DzDealShopsRequest;
import com.dianping.mobile.mapi.dztgdetail.entity.FreeDealConfig;
import com.dianping.mobile.mapi.dztgdetail.util.*;
import com.google.common.reflect.TypeToken;
import lombok.extern.log4j.Log4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils.hitOverseasCategoryConfig;

@Log4j
public class UrlHelper {

    private static final String MT_TIMES_CARD_BUY_URL = "imeituan://www.meituan.com/picassomodules?" +
            "picassojs=BeautyPicassoModules/picasso_beauty_vipcard_vc&shieldtype=1&shopid=%s&dealid=%s&goodsid=%s&channelsource=%s";

    private static final String DP_TIMES_CARD_BUY_URL = "dianping://picassomodules?" +
            "picassojs=BeautyPicassoModules/picasso_beauty_vipcard_vc&shieldtype=1&dealid=%s&goodsid=%s&channelsource=%s";

    /**
     * 获取商户列表页地址
     *
     * @param ctx   点评传点评ID，美团传美团ID
     * @param poiId 仅点评需要
     * @return
     */
    public static String getShopListUrl(DealCtx ctx, long poiId) {
        if (ClientTypeEnum.isDpMainApp(ctx.getEnvCtx().getClientType())) {
            String url = String.format("dianping://getshopbranches?dealid=%s&istuan=1", ctx.getDealId4P());
            // 境外门店 不能传shopid和shopuuid
            if (hitOverseasCategoryConfig(ctx.getCategoryId())){
                return url;
            }
            String dpShopUuid = ShopUuidUtils.getUuidById(poiId);
            url += "&shopuuid=" + dpShopUuid;
            if (ShopUuidUtils.retainShopId(poiId)) {
                url += "&shopid=" + poiId;
            }
            return url;
        }
        if (ClientTypeEnum.isMtMainApp(ctx.getEnvCtx().getClientType())) {
            return String.format("imeituan://www.meituan.com/gc/branchlist?dealid=%s&frompage=1", ctx.getDealId4P());
        }
        return null;
    }

    //点评传点评ID，美团传美团ID
    public static String getGoodReviewUrl(int dealId, int clientType) {
        if (ClientTypeEnum.isDpMainApp(clientType)) {
            return String.format("dianping://review?referid=%s&refertype=1", dealId);
        }
        if (ClientTypeEnum.isMtMainApp(clientType)) {
            return String.format("imeituan://www.meituan.com/reviewlist?refertype=1&referid=%s", dealId);
        }
        return null;
    }

    //仅支持点评APP
    public static String getMapUrl(BestShopDTO bestShop, DealCtx ctx) {
        if (ClientTypeEnum.isDpMainApp(ctx.getEnvCtx().getClientType()) && bestShop.getDpShopId() > 0) {
            StringBuilder stringBuilder = new StringBuilder("dianping://picassobox?");
            stringBuilder
                    .append("picassoid=").append("picasso_shopmap/RouteVC-bundle.js").append("&notitlebar=").append(true)
                    .append("&disableslideback=").append(true).append("&destlat=").append(bestShop.getGlat())
                    .append("&destlng=").append(bestShop.getGlng()).append("&sourcelat=").append(ctx.getUserlat())
                    .append("&sourcelng=").append(ctx.getUserlng()).append("&cityid=").append(ctx.getDpCityId())
                    .append("&source=").append("gc")
                    .append("&shopaddress=").append(NetUtils.encode(bestShop.getAddress()));

            stringBuilder.append("&shopuuid=").append(ctx.getDpShopUuid());
            if (ShopUuidUtils.retainShopId(bestShop.getDpShopId())) {
                stringBuilder.append("&shopid=").append(bestShop.getDpShopId());
            }
            return stringBuilder.toString();
        }

        if (ClientTypeEnum.isMtMainApp(ctx.getEnvCtx().getClientType()) && bestShop.getMtShopId() > 0) {
            Version version = new Version(ctx.getEnvCtx().getVersion());
            if (Version.V10_0_800.compareTo(version) > 0) {
                return "imeituan://www.meituan.com/map/poi?id=" + bestShop.getMtShopId();
            } else {
                //coordtype 是坐标系类型，“GCJ02”：0，“WGS84”：1，业务范围均为大陆地区，请使用GCJ02 https://km.sankuai.com/page/155675043
                if (ClientTypeEnum.isMtMainApp(ctx.getEnvCtx().getClientType()) && bestShop.getMtShopId() > 0) {
                    return "imeituan://www.meituan.com/mapchannel/poi/detail?mapsource=gc&overseas=0&coordtype=0&poi_id=" + bestShop.getMtShopId() +
                            "&latitude=" +
                            bestShop.getLat() +
                            "&longitude=" +
                            bestShop.getLng();
                }
            }
        }

        return null;
    }

    public static String getDpMapUrl(DrivingPoi req, long dpShopId, String dpShopUUid, double lng, double lat, String address) {
        if (dpShopId <= 0) {
            return null;
        }

        StringBuilder stringBuilder = new StringBuilder("dianping://picassobox?");
        stringBuilder
                .append("picassoid=").append("picasso_shopmap/RouteVC-bundle.js").append("&notitlebar=").append(true)
                .append("&disableslideback=").append(true).append("&destlat=").append(lat)
                .append("&destlng=").append(lng).append("&sourcelat=").append(req.getUserLat())
                .append("&sourcelng=").append(req.getUserLng()).append("&cityid=").append(req.getCityId())
                .append("&source=").append("gc")
                .append("&shopaddress=").append(NetUtils.encode(address));

        stringBuilder.append("&shopuuid=").append(dpShopUUid);

        if (ShopUuidUtils.retainShopId(dpShopId)) {
            stringBuilder.append("&shopid=").append(dpShopId);
        }

        return stringBuilder.toString();
    }

    public static String getMtMapUrl(long mtShopId, double lng, double lat) {
        if (mtShopId <= 0) {
            return null;
        }

        //coordtype 是坐标系类型，“GCJ02”：0，“WGS84”：1，业务范围均为大陆地区，请使用GCJ02 https://km.sankuai.com/page/155675043
        return "imeituan://www.meituan.com/mapchannel/poi/detail?mapsource=gc&overseas=0&coordtype=0&poi_id=" + mtShopId
                + "&latitude=" + lat + "&longitude=" + lng;
    }

    public static String getNewCommonBuyUrlWithSourceExt(DealCtx ctx, int cityId, List<String> remainExtParam) {
        String normalBuyUrl = getNewCommonBuyUrl(ctx, cityId);
        if (normalBuyUrl == null) {
            return null;
        }
        return fillSourceAndExt(ctx, normalBuyUrl, remainExtParam);
    }

    /**
     * 获取各站点普通购买地址，传参需要对应各个平台
     * 与前端确认表示后续一码多端使用新的统一URL：https://km.sankuai.com/collabpage/1455333207
     */
    public static String getNewCommonBuyUrl(DealCtx ctx, int cityId) {
        int clientType = ctx.getEnvCtx().getClientType();
        int dealId4P = ctx.getDealId4P();
        long poiId4P = ctx.getLongPoiId4PFromReq();

        if (ctx.isExternal()) {
            if (Objects.equals(ctx.getEnvCtx().getMpAppId(), MpAppIdEnum.MT_WEIXIN_MINIPROGRAM.getMpAppId())) {
                return getNewWxCommonBuyUrl(dealId4P, cityId, ClientTypeEnum.isMtPlatform(clientType), poiId4P);
            }
            return getExternalCommonBuyUrl(dealId4P, poiId4P);
        }
        if (ClientTypeEnum.isMtMainApp(clientType)) {
            return getNewMtAppCommonBuyUrl(dealId4P, poiId4P);
        }
        if (ClientTypeEnum.isDpMainApp(clientType)) {
            return getNewDpAppCommonBuyUrl(dealId4P, poiId4P, ctx.getDpShopUuid());
        }
        if (ClientTypeEnum.isMainWX(clientType)) {
            return getNewWxCommonBuyUrl(dealId4P, cityId, ClientTypeEnum.isMtPlatform(clientType), poiId4P);
        }
        if (ClientTypeEnum.dp_wap.getType() == clientType || ClientTypeEnum.mt_wap.getType() == clientType) {
            return getMWebCommonBuyUrl(dealId4P, ctx.getDpDealId(), ClientTypeEnum.isMtPlatform(clientType));
        }

        return null;
    }

    /**
     * 获取各站点普通购买地址，传参需要对应各个平台
     */
    public static String getCommonBuyUrl(DealCtx ctx, int cityId, String finalPrice) {
        int clientType = ctx.getEnvCtx().getClientType();
        int dealId4P = ctx.getDealId4P();
        long poiId4P = ctx.getLongPoiId4PFromReq();

        if (ctx.isExternal()) {
            if (Objects.equals(ctx.getEnvCtx().getMpAppId(), MpAppIdEnum.MT_WEIXIN_MINIPROGRAM.getMpAppId())) {
                return getWxCommonBuyUrl(ctx, dealId4P, cityId, ClientTypeEnum.isMtPlatform(clientType), poiId4P);
            }
            if(Objects.equals(ctx.getEnvCtx().getDztgClientTypeEnum(), DztgClientTypeEnum.DIANPING_BAIDUMAP_MINIAPP)) {
                return getBaiduDianpingMiniAppBuyUrl(ctx, finalPrice);
            }
            if(Objects.equals(ctx.getEnvCtx().getDztgClientTypeEnum(), DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP)) {
                return getDianpingMiniAppBuyUrl(ctx, finalPrice);
            }
            if(Objects.equals(ctx.getEnvCtx().getDztgClientTypeEnum(), DztgClientTypeEnum.MEITUAN_MAP_APP)) {
                return getMtAppCommonBuyUrl(dealId4P, poiId4P);
            }
            return getExternalCommonBuyUrl(dealId4P, poiId4P);
        }

        if (ClientTypeEnum.isMtMainApp(clientType)) {
            String mtAppCommonBuyUrl = getMtAppCommonBuyUrl(dealId4P, poiId4P);
            return assembleSourceParam(ctx, mtAppCommonBuyUrl);
        }

        if (ClientTypeEnum.isDpMainApp(clientType)) {
            String dpAppCommonBuyUrl = getDpAppCommonBuyUrl(dealId4P, poiId4P, ctx.getDpShopUuid());
            return assembleSourceParam(ctx, dpAppCommonBuyUrl);
        }

        if (ClientTypeEnum.isMainWX(clientType)) {
            return getWxCommonBuyUrl(ctx, dealId4P, cityId, ClientTypeEnum.isMtPlatform(clientType), poiId4P);
        }

        if (ClientTypeEnum.dp_wap.getType() == clientType || ClientTypeEnum.mt_wap.getType() == clientType) {
            return getMWebCommonBuyUrl(dealId4P, ctx.getDpDealId(), ClientTypeEnum.isMtPlatform(clientType));
        }

        return null;
    }

    private static String assembleSourceParam(DealCtx ctx, String url) {
        if (StringUtils.isBlank(url)) {
            return url;
        }

        if(StringUtils.isBlank(ctx.getRequestSource()) && StringUtils.isBlank(ctx.getDealParam())) {
            return url;
        }

        if (RequestSourceEnum.needDyeAndReport(ctx.getRequestSource())) {
            return url + "&source=" + ctx.getRequestSource();
        }

        if(DealParamEnum.isMLiveSource(ctx.getDealParam())) {
            return url + "&source=mlive";
        }

        return url;
    }

    //获取小程序普通购买地址
    public static String getWxCommonBuyUrl(DealCtx ctx, int dealId, int cityId, boolean mt, long shopId) {
        if (dealId <= 0) {
            return null;
        }

         String url = StringUtils.EMPTY;
        if (mt) {
            url = String.format("/gnc/pages/ordering/index?id=%s&cityId=%s&shopId=%s", dealId, cityId, shopId);
        } else {
            url = "/packages/tuan/pages/ordersubmit/ordersubmit?type=1";
        }

        if (RequestSourceEnum.COST_EFFECTIVE.getSource().equals(ctx.getRequestSource())) {
            url += "&source=cost_effective";
        }
        return url;
    }

    private static String getNewWxCommonBuyUrl(int dealId, int cityId, boolean mt, long shopId) {
        if (dealId <= 0) {
            return null;
        }
        if (mt) {
            return String.format("/gc-business/pages/deal/submitorder/submitorder?dealid=%s&shopid=%s", dealId, shopId);
        } else {
            return "/packages/tuan/pages/ordersubmit/ordersubmit?type=1";
        }
    }

    private static String getBaiduDianpingMiniAppBuyUrl(final DealCtx ctx, final String finalPrice) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper.getBaiduDianpingMiniAppBuyUrl(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx,java.lang.String)");
        Map<String, Object> encodeURIComponent = new LinkedHashMap<>();
        if(ctx.getDealGroupBase() != null) {
            if(CollectionUtils.isNotEmpty(ctx.getDealGroupBase().getDeals())) {
                Optional<DealBaseDTO> dealBaseDTO = ctx.getDealGroupBase().getDeals().stream().filter(dealBase -> dealBase.getDealStatus() == 1).findFirst();
                if (dealBaseDTO.isPresent()) {
                    encodeURIComponent.put("dealId", dealBaseDTO.get().getDealId());
                    encodeURIComponent.put("title", dealBaseDTO.get().getShortTitle());
                }
            }
            encodeURIComponent.put("dealGroupId", ctx.getDealGroupBase().getDealGroupId());
            encodeURIComponent.put("maxPerUser", ctx.getDealGroupBase().getMaxPerUser());
        }
        encodeURIComponent.put("price", finalPrice);
        encodeURIComponent.put("shopUuid", ctx.getDpShopUuid());
        encodeURIComponent.put("shopId", ctx.getDpLongShopId());

        String encodedJson = "";
        try {
            String json = GsonUtils.toJsonString(encodeURIComponent);
            encodedJson = URLEncoder.encode(json, "UTF8");
        } catch (UnsupportedEncodingException e) {
            log.error("URLEncoder.encode error", e);
        }

        return "/packages/dpmapp-gc-deal/tuan/pages/ordersubmit/ordersubmit?type=1&orderInfo=" + encodedJson;
    }

    private static String getDianpingMiniAppBuyUrl(final DealCtx ctx, final String finalPrice) {
        Map<String, Object> encodeURIComponent = new LinkedHashMap<>();
        if(ctx.getDealGroupBase() != null) {
            if(CollectionUtils.isNotEmpty(ctx.getDealGroupBase().getDeals())) {
                Optional<DealBaseDTO> dealBaseDTO = ctx.getDealGroupBase().getDeals().stream().filter(dealBase -> dealBase.getDealStatus() == 1).findFirst();
                if (dealBaseDTO.isPresent()) {
                    encodeURIComponent.put("dealId", dealBaseDTO.get().getDealId());
                    encodeURIComponent.put("title", dealBaseDTO.get().getShortTitle());
                }
            }
            encodeURIComponent.put("dealGroupId", ctx.getDealGroupBase().getDealGroupId());
            encodeURIComponent.put("maxPerUser", ctx.getDealGroupBase().getMaxPerUser());
        }
        encodeURIComponent.put("price", finalPrice);
        encodeURIComponent.put("shopUuid", ctx.getDpShopUuid());
        encodeURIComponent.put("shopId", ctx.getDpLongShopId());

        String encodedJson = "";
        try {
            String json = GsonUtils.toJsonString(encodeURIComponent);
            encodedJson = URLEncoder.encode(json, "UTF8");
        } catch (UnsupportedEncodingException e) {
            log.error("URLEncoder.encode error", e);
        }

        String url = "/packages/tuan/pages/ordersubmit/ordersubmit?type=1&orderInfo=" + encodedJson;
        if (RequestSourceEnum.COST_EFFECTIVE.getSource().equals(ctx.getRequestSource())) {
            url += "&source=cost_effective";
        }

        return url;
    }

    //获取小程序次卡购买地址
    private static String getWxCardsCommonBuyUrl(long productId, long poiId, String dpShopUuid, boolean mt, int channelSource) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper.getWxCardsCommonBuyUrl(long,long,java.lang.String,boolean,int)");
        if (productId <= 0) {
            return null;
        }
        String url;
        String baseUrl;
        if (mt) {
            baseUrl = Lion.getStringValue("com.sankuai.dzu.tpbase.dztgdetailweb.mt.wx.cards.url",
                    "https://i.meituan.com/node/card/html/timecard/index.html?productId=%s&shopId=%s");
            url = String.format(baseUrl, productId, poiId);
        } else {
            baseUrl = Lion.getStringValue("com.sankuai.dzu.tpbase.dztgdetailweb.dp.wx.cards.url",
                    "https://m.dianping.com/node/card/html/timecard/index.html?productId=%s");
            url = String.format(baseUrl, productId);
            url += "&shopuuid=" + dpShopUuid;
            if (ShopUuidUtils.retainShopId(poiId)) {
                url += "&shopid=" + poiId;
            }
        }
        url += "&channelsource" + channelSource;
        return getWxH5Url(url, mt);
    }

    private static String getExternalCommonBuyUrl(int mtDealId, long mtPoiId) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper.getExternalCommonBuyUrl(int,long)");
        return String.format("/gc/pages/deal/createorder/createorder?dealid=%s&shopid=%s", mtDealId, mtPoiId);
    }

    //获取美团APP普通购买地址
    private static String getMtAppCommonBuyUrl(int mtDealId, long mtPoiId) {
        return String.format("imeituan://www.meituan.com/gc/createorder?dealid=%s&shopid=%s", mtDealId, mtPoiId);
    }

    private static String getNewMtAppCommonBuyUrl(int mtDealId, long mtPoiId) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper.getNewMtAppCommonBuyUrl(int,long)");
        return String.format("imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=gcsubmitordermrnmodules&mrn_component=dealsubmitorderpage&dealid=%s&shopid=%s", mtDealId, mtPoiId);
    }

    //获取点评APP普通购买地址
    private static String getDpAppCommonBuyUrl(int dpDealGroupId, long dpPoiId, String shopUuid) {
        String url = String.format("dianping://createorder?dealid=%s", dpDealGroupId);

        url += "&shopuuid=" + shopUuid;
        if (ShopUuidUtils.retainShopId(dpPoiId)) {
            url += "&shopid=" + dpPoiId;
        }
        return url;
    }

    private static String getNewDpAppCommonBuyUrl(int dpDealGroupId, long dpPoiId, String shopUuid) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper.getNewDpAppCommonBuyUrl(int,long,java.lang.String)");
        String url = String.format("dianping://mrn?mrn_biz=gc&mrn_entry=gcsubmitordermrnmodules&mrn_component=dealsubmitorderpage&dealid=%s", dpDealGroupId);

        url += "&shopuuid=" + shopUuid;
        if (ShopUuidUtils.retainShopId(dpPoiId)) {
            url += "&shopid=" + dpPoiId;
        }
        return url;
    }

    //获取M站订单跳转
    private static String getMWebCommonBuyUrl(int dpDealGroupId, int dpDealId, boolean mt) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper.getMWebCommonBuyUrl(int,int,boolean)");
        if (dpDealGroupId <= 0) {
            return null;
        }
        String url;
        String baseUrl;
        if (mt) {
            baseUrl = Lion.getStringValue("com.sankuai.dzu.tpbase.dztgdetailweb.mt.web.url",
                    "https://test-g.meituan.com/app/gfe-app-page-tuan/create-order.html?dealId=%s");
            url = String.format(baseUrl, dpDealGroupId);
        } else {
            baseUrl = Lion.getStringValue("com.sankuai.dzu.tpbase.dztgdetailweb.dp.web.url",
                    "https://m.51ping.com/tuan/buy/%s");
            url = String.format(baseUrl, dpDealId);
        }
        return url;
    }

    /**
     * 获取次卡购买地址
     */
    public static String getTimesCardBuyUrl(DealCtx ctx, long tcProductId, int channelSource) {
        int clientType = ctx.getEnvCtx().getClientType();
        long poiId4PFromResp = ctx.getLongPoiId4PFromResp();
        if (ClientTypeEnum.isMtMainApp(clientType)) {
            return String.format(MT_TIMES_CARD_BUY_URL, poiId4PFromResp, ctx.getDealId4P(), tcProductId, channelSource);
        }
        String shopUuidFromResp = ctx.getShopUuidFromResp();
        if (ClientTypeEnum.isDpMainApp(clientType)) {
            String url = String.format(DP_TIMES_CARD_BUY_URL, ctx.getDealId4P(), tcProductId, channelSource);

            url += "&shopuuid=" + shopUuidFromResp;
            if (ShopUuidUtils.retainShopId(poiId4PFromResp)) {
                url += "&shopid=" + poiId4PFromResp;
            }
            return url;
        }
        if (ClientTypeEnum.isMainWX(clientType) || Objects.equals(ctx.getEnvCtx().getMpAppId(), MpAppIdEnum.MT_WEIXIN_MINIPROGRAM)) {
            return getWxCardsCommonBuyUrl(tcProductId,
                    poiId4PFromResp,
                    shopUuidFromResp,
                    ClientTypeEnum.isMtPlatform(clientType),
                    channelSource);
        }
        return null;
    }

    public static String getDiscountCardBuyUrl(DealCtx ctx, boolean usediscountprice, boolean disablepromo) {
        String comBuyUrl = getCommonBuyUrl(ctx, 0, "");
        if (StringUtils.isNotBlank(comBuyUrl)) {
            String param = String.format("&usediscountprice=%s&disablepromodesk=%s", usediscountprice, disablepromo);
            return comBuyUrl + param;
        }
        return null;
    }

    //获取商户地址
    public static String getShopUrl(DealCtx ctx) {
        int clientType = ctx.getEnvCtx().getClientType();
        long poiId = ctx.getLongPoiId4PFromResp();
        String dpShopUuid = ctx.getShopUuidFromResp();
        if (ctx.isExternal()) {
            if (MpAppIdEnum.MT_KUAISHOU_MINIPROGRAM.getMpAppId().equals(ctx.getEnvCtx().getMpAppId())
                    || "kuaishou".equals(ctx.getEnvCtx().getMpSource())) {
                return String.format("/gcpoi/detail-page/detail?shopid=%s", poiId);
            }
        }
        if (DztgClientTypeEnum.MEITUAN_MAP_APP.equals(ctx.getEnvCtx().getDztgClientTypeEnum())) {
            return String.format("mtmap://www.meituan.com/mrn?mrn_biz=gcbu&mrn_entry=mrn-joy-poidetail&mrn_component=mappoidetail&id=%s&shopId=%s", poiId, poiId);
        }
        if (ClientTypeEnum.isMtMainApp(clientType)) {
            return String.format("imeituan://www.meituan.com/gc/poi/detail?id=%s&mmcinflate=%s&mmcuse=%s&mmcbuy=%s&mmcfree=%s&channelType=%s&offlinecode=%s", poiId, MagicFlagUtils.toString(ctx.getMmcInflate()), MagicFlagUtils.toString(ctx.getMmcUse()), MagicFlagUtils.toString(ctx.getMmcBuy()), MagicFlagUtils.toString(ctx.getMmcFree()), StringUtils.isNotBlank(ctx.getRequestSource()) ? ctx.getRequestSource() : "", StringUtils.isNotBlank(ctx.getOfflineCode()) ? ctx.getOfflineCode() : "");
        }
        if (ClientTypeEnum.isDpMainApp(clientType)) {
            StringBuilder url = new StringBuilder("dianping://shopinfo");
            url.append("?shopuuid=").append(dpShopUuid);
            if (ShopUuidUtils.retainShopId(poiId)) {
                url.append("&shopid=").append(poiId);
            }
            url.append("&mmcinflate=").append(MagicFlagUtils.toString(ctx.getMmcInflate()));
            url.append("&mmcuse=").append(MagicFlagUtils.toString(ctx.getMmcUse()));
            url.append("&mmcbuy=").append(MagicFlagUtils.toString(ctx.getMmcBuy()));
            url.append("&mmcfree=").append(MagicFlagUtils.toString(ctx.getMmcFree()));
            url.append("&channelType=").append(StringUtils.isNotBlank(ctx.getRequestSource()) ? ctx.getRequestSource() : "");
            url.append("&offlinecode=").append(StringUtils.isNotBlank(ctx.getOfflineCode()) ? ctx.getOfflineCode() : "");
            return url.toString();
        }
        if (ClientTypeEnum.dp_wap.getType() == clientType) {
            long shopId = ShopUuidUtils.retainShopId(poiId) ? poiId : 0;
            return String.format("https://m.dianping.com/shop/%s?shopuuid=%s", shopId, dpShopUuid);
        }
        if (ClientTypeEnum.mt_wap.getType() == clientType) {
            return String.format("https://i.meituan.com/poi/%s", poiId);
        }
        return null;
    }

    public static String getMtWxMainShopUrl(DealCtx ctx) {
        long poiId = ctx.getLongPoiId4PFromResp();
        return String.format("/gcpoi/pages/index?id=%s", poiId);
    }

    public static String getCouponDpUrl(String unifiedCouponId) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper.getCouponDpUrl(java.lang.String)");
        String baseUrl = Lion.getStringValue("com.sankuai.dzu.tpbase.dztgdetailweb.unified.coupon.dp.url",
                "dianping://web?url=https%3a%2f%2fg.dianping.com%2fav%2frainbow%2f1737308%2findex.html%3fcouponid%3d");
        return String.format("%s%s", baseUrl, unifiedCouponId);
    }

    public static String getCouponMtUrl(String unifiedCouponId) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper.getCouponMtUrl(java.lang.String)");
        String baseUrl = Lion.getStringValue("com.sankuai.dzu.tpbase.dztgdetailweb.unified.coupon.mt.url",
                "imeituan://www.meituan.com/web?url=https%3a%2f%2fg.meituan.com%2fav%2frainbow%2f1737308%2findex.html%3fcouponid%3d");
        return String.format("%s%s", baseUrl, unifiedCouponId);
    }
    public static String getCouponDpWebUrl(String unifiedCouponId) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper.getCouponDpWebUrl(java.lang.String)");
        String baseUrl = Lion.getStringValue("com.sankuai.dzu.tpbase.dztgdetailweb.unified.coupon.dp.web.url",
                "https://g.dianping.com/av/rainbow/1737308/index.html?couponid=");
        return baseUrl + unifiedCouponId;
    }

    public static String getCouponMtWebUrl(String unifiedCouponId) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper.getCouponMtWebUrl(java.lang.String)");
        String baseUrl = Lion.getStringValue("com.sankuai.dzu.tpbase.dztgdetailweb.unified.coupon.mt.web.url",
                "https://g.meituan.com/av/rainbow/1737308/index.html?couponid=");
        return baseUrl + unifiedCouponId;
    }

    public static String getBonusUrl(DealCtx dealCtx) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper.getBonusUrl(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        String baseUrl;
        StringBuilder stringBuilder = new StringBuilder();
        if (dealCtx.isMt()) {
            baseUrl = Lion.getStringValue("com.sankuai.dzu.tpbase.dztgdetailweb.bonus.mt.url",
                    "http://test-g.meituan.com/app/gfe-deal-return-present/index.html");
            stringBuilder.append(baseUrl).append("?shopid=")
                    .append(dealCtx.getMtLongShopId()).append("&dealid=")
                    .append(dealCtx.getMtId()).append("&type=").append(1);
        } else {
            baseUrl = Lion.getStringValue("com.sankuai.dzu.tpbase.dztgdetailweb.bonus.dp.url",
                    "http://g.51ping.com/app/gfe-deal-return-present/index.html");
            stringBuilder.append(baseUrl)
                    .append("?shopuuid=").append(dealCtx.getDpShopUuid())
                    .append("&dealid=").append(dealCtx.getDpId())
                    .append("&type=").append(1);
            if (ShopUuidUtils.retainShopId(dealCtx.getDpLongShopId())) {
                stringBuilder.append("&shopid=").append(dealCtx.getDpLongShopId());
            }
        }
        if (dealCtx.getEnvCtx().isMainWeb()) {
            return stringBuilder.toString();
        }
        if (dealCtx.getEnvCtx().isMainWX()) {
            return getWxBaseShareUrl(stringBuilder.toString(), dealCtx.isMt());
        }
        return getAppUrl(stringBuilder.toString(), dealCtx.isMt());
    }

    public static String getAppUrl(String shareUrl, boolean mt) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper.getAppUrl(java.lang.String,boolean)");
        if (StringUtils.isEmpty(shareUrl)) {
            return null;
        }
        if (mt) {
            return "imeituan://www.meituan.com/web?url=" + NetUtils.encode(shareUrl);
        } else {
            shareUrl = ShopUuidUtils.filterUrl(shareUrl);
            return "dianping://web?url=" + NetUtils.encode(shareUrl);
        }
    }

    public static String getAppUrl(EnvCtx ctx, String shareUrl, boolean mt) {
        if (StringUtils.isEmpty(shareUrl)) {
            return null;
        }
        if (mt) {
            if(ctx != null && Objects.equals(ctx.getMpAppId(), MpAppIdEnum.MT_WEIXIN_MINIPROGRAM.getMpAppId())) {
                String miniappUrl = shareUrl + "&product=mtwxapp";
                return "/index/pages/h5/h5?f_token=1&f_openId=1&weburl=" + NetUtils.encode(miniappUrl);
            }
            return "imeituan://www.meituan.com/web?url=" + NetUtils.encode(shareUrl);
        } else {
            shareUrl = ShopUuidUtils.filterUrl(shareUrl);
            if(ctx != null && Objects.equals(ctx.getDztgClientTypeEnum(), DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP)) {
                return "/pages/webview/webview?url=" + NetUtils.encode(shareUrl);
            }
            return "dianping://web?url=" + NetUtils.encode(shareUrl);
        }
    }

    private static String getWxH5Url(String h5Url, boolean mt) {
        if (StringUtils.isEmpty(h5Url)) {
            return null;
        }
        if (mt) {
            return "/index/pages/h5/h5?f_openId=1&f_token=1&weburl=" + NetUtils.encode(h5Url);
        } else {
            return "/pages/webview/webview?url=" + NetUtils.encode(h5Url);
        }
    }

    public static String getWxBaseShareUrl(String h5Url, boolean mt) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper.getWxBaseShareUrl(java.lang.String,boolean)");
        if (StringUtils.isEmpty(h5Url)) {
            return null;
        }
        String baseUrl;
        if (mt) {
            baseUrl = h5Url + "&product=mtwxapp";
            return "/index/pages/h5/h5?f_openId=1&f_token=1&weburl=" + NetUtils.encode(baseUrl);
        } else {
            baseUrl = h5Url + "&utm_source=dianping-wxapp&token=!&openId=!";
            return "/pages/webview/webview?url=" + NetUtils.encode(baseUrl);
        }
    }

    public static String getIdleHoursBuyUrl(DealCtx ctx, Integer cityId) {
        String url = getCommonBuyUrl(ctx, cityId, "");
        url += "&promosource=1";
        return url;
    }

    public static String getMemberCardBuyUrl(DealCtx ctx) {
        String url = getDiscountCardBuyUrl(ctx, true, false);
        url += "&promosource=2";
        url = putPromotionChannelParamIfNeeded(ctx, url);
        return url;
    }

    public static String getJoyCardBuyUrl(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper.getJoyCardBuyUrl(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        String url = getDiscountCardBuyUrl(ctx, true, false);
        url += "&promosource=3";
        return url;
    }

    public static String fillSourceAndExt(DealCtx context, String redirectUrl, List<String> remainExtParamKeys) {
        StringBuilder sb = new StringBuilder(redirectUrl);
        fillSourceParam(context, sb);
        fillExtParam(context, remainExtParamKeys, sb);
        return sb.toString();
    }

    public static void fillSourceParam(DealCtx context, StringBuilder sb) {
        String source = context.getRequestSource();
        if (StringUtils.isEmpty(source)) {
            return;
        }
        if (sb.toString().contains("?")) {
            sb.append("&source=").append(source);
        } else {
            sb.append("?source=").append(source);
        }
    }

    public static void fillExtParam(DealCtx context, List<String> remainExtParamKeys, StringBuilder sb) {
        String extParam = context.getRequestExtParam();
        if (StringUtils.isEmpty(extParam) || CollectionUtils.isEmpty(remainExtParamKeys)) {
            return;
        }
        Map<String, String> extParamMap = GsonUtils.fromJsonString(extParam, new TypeToken<Map<String, String>>() {}.getType());
        if (MapUtils.isEmpty(extParamMap)) {
            return;
        }
        String dealExtParam = buildDealExtParam(extParamMap, remainExtParamKeys);
        if (StringUtils.isEmpty(dealExtParam)) {
            return;
        }
        if (sb.toString().contains("?")) {
            sb.append("&dealextparam=").append(NetUtils.encode(dealExtParam));
        } else{
            sb.append("?dealextparam=").append(NetUtils.encode(dealExtParam));
        }
    }

    public static String buildDealExtParam(Map<String, String> extParamMap, List<String> remainExtParamKeys) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper.buildDealExtParam(java.util.Map,java.util.List)");
        Map<String, String> remainParamMap = extParamMap.entrySet().stream()
                .filter(entry -> remainExtParamKeys.contains(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        return GsonUtils.toJsonString(remainParamMap);
    }

    public static String addPriceCipher(DealCtx ctx, String buyUrl){
        try {
            if (ctx == null || StringUtils.isEmpty(buyUrl) || buyUrl.contains("pricecipher")){
                return buyUrl;
            }
            PriceContext priceContext = ctx.getPriceContext();
            String priceCipher = null;
            if (priceContext == null) {
                return buyUrl;
            }
            // 先出门市价的价惠密文，如果门市价的为空，初始化成普通的价格
            priceCipher = priceContext.getDealPromoPriceCipher();
            if (priceContext.getDealPromoPrice() == null) {
                priceCipher = priceContext.getNormalPriceCipher();
            }
            if (StringUtils.isNotEmpty(priceCipher)){
                return buyUrl + "&pricecipher="+priceCipher;
            }
        } catch (Exception e) {
            log.error("addPriceCipher error", e);
        }
        return buyUrl;
    }

    public static String getResvFillingUrl(DealCtx ctx) {
        long dpLongShopId = ctx.getDpLongShopId();
        long mtLongShopId = ctx.getMtLongShopId();
        DealGroupBaseDTO dealGroupBase = ctx.getDealGroupBase();
        if (dealGroupBase == null){
            return null;
        }
        int dealGroupId = dealGroupBase.getDealGroupId();
        if (ctx.isMt()){
            String mtUrl="imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=vg-mrn-reserve&mrn_component=reservesubmit";
            return mtUrl+"&bizCode=1037"
            +"&platform="+PlatformEnum.MEI_TUAN.getType()
            +"&mtShopId="+mtLongShopId
            +"&dpDealGroupId="+dealGroupId
            +"&mtDealGroupId="+dealGroupId
            +"&pageType=1"
            +"&entranceCode=3"
            +"&bizSourceId=51027";
        }else{
            String dpUrl = "dianping://mrn?mrn_biz=gc&mrn_entry=vg-mrn-reserve&mrn_component=reservesubmit";
            return dpUrl+"&bizCode=1037"
                    +"&platform="+PlatformEnum.DIAN_PING.getType()
                    +"&dpShopId="+dpLongShopId
                    +"&dpDealGroupId="+dealGroupId
                    +"&mtDealGroupId="+dealGroupId
                    +"&pageType=1"
                    +"&entranceCode=3"
                    +"&bizSourceId=51027";
        }
    }

    public static String putPromotionChannelParamIfNeeded(DealCtx ctx, String buyUrl) {
        if (StringUtils.isEmpty(buyUrl)) {
            return buyUrl;
        }
        // 渠道专属立减 提单页链接增加参数
        Map<String, String> pageSource2OrderPromotionChannel = Lion.getMap(LionConstants.APP_KEY, LionConstants.PAGESOURCE_TO_ORDER_PROMOTIONChANNEL, String.class, Collections.emptyMap());
        String promotionChannel = buildPromotionChannel(ctx, pageSource2OrderPromotionChannel);
        if (ctx.getPriceContext().isHasExclusiveDeduction()) {
            // 默认是1
            if (StringUtils.isBlank(promotionChannel)) {
                promotionChannel = "1";
            }
            return buyUrl + "&promotionchannel=" + promotionChannel;
        } else if (StringUtils.isNotBlank(promotionChannel)) {
            return buyUrl + "&promotionchannel=" + promotionChannel;
        }
        return buyUrl;
    }

    public static String buildPromotionChannel(DealCtx ctx, Map<String, String> pageSource2OrderPromotionChannel) {
        String promotionChannel = pageSource2OrderPromotionChannel.get(ctx.getRequestSource());
        // 猜喜渠道流量来源为source=caixi，但是猜喜侧只有hitGuessLikeSubsidy=true时调用报价才传source=caixi，为和猜喜保持一致，使用hitGuessLikeSubsidy字段判定
        if (!LionConfigUtils.useNewSourceForCaixi()) {
            return promotionChannel;
        }
        DealBaseReq req = ctx.getDealBaseReq();
        Map<String, String> requestExtParams = new HashMap<>();
        if (RequestSourceEnum.CAI_XI.getSource().equals(ctx.getRequestSource())) {
            if (StringUtils.isNotBlank(req.getExtParam())) {
                try {
                    requestExtParams = GsonUtils.fromJsonString(ctx.getDealBaseReq().getExtParam(), new com.google.gson.reflect.TypeToken<Map<String, String>>() {}.getType());
                } catch (Exception e) {
                    log.error("GsonUtils.fromJsonString failed", e);
                    return promotionChannel;
                }
            }
            return MapUtils.isNotEmpty(requestExtParams) && requestExtParams.containsKey(Constants.SUBSIDY_SCENE) && Constants.HIT_GUESS_LIKE_SUBSIDY.equals(requestExtParams.get(Constants.SUBSIDY_SCENE)) ? promotionChannel : null;
        }

        return promotionChannel;
    }

    public static String getFreeDealUrl(DealCtx ctx, FreeDealConfig freeDealConfig) {
        long shopId = ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId();
        long dealGroupId = ctx.isMt() ? ctx.getMtId() : ctx.getDpId();
        boolean isNative = ctx.getEnvCtx().isNative();
        String schema = ctx.isMt() ? (isNative ? freeDealConfig.getMtAppSchema() : freeDealConfig.getMtH5Schema()) : (isNative ? freeDealConfig.getDpAppSchema() : freeDealConfig.getDpH5Schema());
        String schemaAfter = schema.replace("{shopId}", String.valueOf(shopId));
        return schemaAfter.replace("{dealId}", String.valueOf(dealGroupId));
    }

    public static String getOrderUrl(DealCtx ctx) {
        DealGroupBaseDTO dealGroupBase = ctx.getDealGroupBase();
        if (dealGroupBase == null) {
            return null;
        }
        if (ctx.isMt()){
            String mtUrl = "imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=vg-mrn-reserve&mrn_component=customersubmit&entranceCode=11&mtShopId=%s&mtDealGroupId=%s";
            return String.format(mtUrl, ctx.getMtLongShopId(), ctx.getMtId());
        } else {
            String dpUrl = "dianping://mrn?mrn_biz=gc&mrn_entry=vg-mrn-reserve&mrn_component=customersubmit&entranceCode=11&dpShopId=%s&dpDealGroupId=%s";
            return String.format(dpUrl, ctx.getDpLongShopId(), ctx.getDpId());
        }
    }
}
