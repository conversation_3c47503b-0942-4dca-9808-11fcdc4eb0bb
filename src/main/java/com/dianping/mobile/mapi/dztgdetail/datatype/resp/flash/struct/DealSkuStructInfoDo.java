package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.struct;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;
import java.util.List;

@TypeDoc(description = "到综团单结构化商品数据")
@MobileDo(id = 0xd693)
public class DealSkuStructInfoDo implements Serializable {

    @FieldDoc(description = "包含条目")
    @MobileDo.MobileField(key = 0xe23d)
    private List<DealStructItemDo> items;

    @FieldDoc(description = "项目图标")
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;

    @FieldDoc(description = "项目名")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "价格")
    @MobileDo.MobileField(key = 0xb716)
    private String price;

    @FieldDoc(description = "份数")
    @MobileDo.MobileField(key = 0xd993)
    private String copies;

    @FieldDoc(description = "项目链接")
    @MobileDo.MobileField(key = 0xce7c)
    private String productUrl;

    @FieldDoc(description = "项目标签")
    @MobileDo.MobileField(key = 0xc857)
    private String productTag;

    public List<DealStructItemDo> getItems() {
        return items;
    }

    public void setItems(List<DealStructItemDo> items) {
        this.items = items;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getCopies() {
        return copies;
    }

    public void setCopies(String copies) {
        this.copies = copies;
    }

    public String getProductUrl() {
        return productUrl;
    }

    public void setProductUrl(String productUrl) {
        this.productUrl = productUrl;
    }

    public String getProductTag() {
        return productTag;
    }

    public void setProductTag(String productTag) {
        this.productTag = productTag;
    }
}