package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.enums.FitnessCrossIdentityEnum;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * 健身通数据BO
 */
@Data
@Builder
public class FitnessCrossBO {

    /**
     * 是否有有效卡券
     */
    private boolean hasAvailableCoupon;

    /**
     * 用户健身通身份
     */
    private FitnessCrossIdentityEnum identityEnum;

    /**
     * 提单页url
     */
    private String orderUrl;

    /**
     * 文案配置
     */
    private Config config;

    /**
     * 配置
     */
    @Data
    public static class Config {

        /**
         * 按钮横幅配置
         */
        private Map<String, String> btnBarTextMap;

        /**
         * 新app版本按钮横幅icon url
         */
        private String newVersionBuyBannerIconUrl;

        /**
         * 旧app版本按钮横幅icon url
         */
        private String oldVersionBuyBannerIconUrl;

        /**
         * 按钮横幅icon 宽
         */
        private int buyBannerIconWidth;

        /**
         * 按钮横幅icon 高
         */
        private int buyBannerIconHeight;

        /**
         * 按钮横幅右侧图标url
         */
        private String buyBannerArrowIconUrl;

        /**
         * 是否开启销量展示
         */
        private boolean enableSaleDesc;

    }

}
