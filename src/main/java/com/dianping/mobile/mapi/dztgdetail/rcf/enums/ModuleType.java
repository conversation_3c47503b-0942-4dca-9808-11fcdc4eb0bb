package com.dianping.mobile.mapi.dztgdetail.rcf.enums;

import com.dianping.deal.bff.cache.enums.RcfDealBffInterfaceEnum;

/**
 * <AUTHOR>
 * @create 2024/12/25 11:02
 */
public enum ModuleType {
    standard_service_info_v1_1layer("standard_service_info_v1_1layer"),
    standard_service_info_v1_3layer("standard_service_info_v1_3layer"),
    detailist_type1("detailist_type1"),
    detailist_type3("detailist_type3"),
    description_type1("description_type1"),
    standard_additional_v1_1layer("standard_additional_v1_1layer"),
    standard_additional_v1_2layer("standard_additional_v1_2layer"),
    standard_freebie_v1_3layer("standard_freebie_v1_3layer"),
    standard_facility_v1_2layer("standard_facility_v1_2layer"),
    attr_type2("attr_type2"),
    attr_type1("attr_type1"),
    attr_type6("attr_type6"),
    attr_type5("attr_type5"),
    extra_type1("extra_type1"),
    attr_type3("attr_type3"),
    ;
    private final String type;

    ModuleType(String type){
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public static ModuleType fromType(String type) {
        for (ModuleType value : ModuleType.values()) {
            if (value.name().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
