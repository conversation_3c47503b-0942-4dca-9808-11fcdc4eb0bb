package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GoodReviewReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.GoodReviewPBO;
import com.dianping.mobile.mapi.dztgdetail.facade.GoodReviewFacade;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

import static com.dianping.mobile.mapi.dztgdetail.util.AntiCrawlerUtils.antiUnauthenticLogin;

@InterfaceDoc(displayName = "到综团单好评度信息查询接口",
        type = "restful",
        description = "查询团单好评度信息：双平台来自不同的UGC服务，整个接口填充的数据也根据平台有所不同。",
        scenarios = "该接口仅适用于双平台APP站点的团购详情页及其他以到综团购为纬度的页面",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "qian.wang.sh"
)
@Controller("general/platform/dztgdetail/dzgoodreview.bin")
@Action(url = "dzgoodreview.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class DzGoodReviewAction extends AbsAction<GoodReviewReq> {

    @Autowired
    private GoodReviewFacade goodReviewFacade;

    @Override
    protected IMobileResponse validate(GoodReviewReq request, IMobileContext iMobileContext) {
        IdUpgradeUtils.processProductIdForGoodReviewReq(request, "dzgoodreview.bin");
        if(request.getDealgroupid() <= 0){
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "dzgoodreview.bin",
            displayName = "查询到综团单好评度信息",
            description = "查询到综团单好评度信息：双平台来自不同的UGC服务，整个接口填充的数据也根据平台有所不同",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "dzgoodreview.bin请求参数",
                            type = GoodReviewReq.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "result", description = "到综好评度picasso数据",type = GoodReviewPBO.class)},
            restExampleUrl = "https://mapi.51ping.com/general/platform/dztgdetail/dzgoodreview.bin?dealgroupid=200153707",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    ),
            }
    )
    @Override
    @CryptMethod
    protected IMobileResponse execute(GoodReviewReq request, IMobileContext iMobileContext) {
        EnvCtx envCtx = initEnvCtx(iMobileContext);
        GoodReviewPBO result = null;
        // 拦截没有授权的登录
        try {
            antiUnauthenticLogin(iMobileContext);
            result =  goodReviewFacade.queryGoodReview(request, envCtx, iMobileContext);
        } catch (Exception e) {
            logger.error("dzgoodreview.bin error:",e);
        }
        return result != null ? new CommonMobileResponse(result) : Resps.SYSTEM_ERROR;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
