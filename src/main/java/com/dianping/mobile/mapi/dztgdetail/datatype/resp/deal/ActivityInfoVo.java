package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

@TypeDoc(description = "活动详情相关数据")
@MobileDo(id = 0xc3cb)
@Data
public class ActivityInfoVo {

    @FieldDoc(description = "内容链接")
    @MobileField(key = 0x9cd9)
    private String contentLink;

    @FieldDoc(description = "展示标题")
    @MobileField(key = 0x20b6)
    private String showTitle;

    @FieldDoc(description = "是否展示")
    @MobileField(key = 0xdac8)
    private boolean show;
}
