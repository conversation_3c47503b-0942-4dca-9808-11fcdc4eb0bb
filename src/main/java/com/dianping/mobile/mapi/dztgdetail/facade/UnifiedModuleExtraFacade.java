package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.attribute.dto.DealGroupAttributeDTO;
import com.dianping.deal.attribute.dto.DealGroupAttributeResult;
import com.dianping.deal.base.dto.DealBaseDTO;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.json.facade.JsonFacade;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.DealCategoryFactory;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.impl.BathCategoryStrategyImpl;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryParam;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.FreeDealEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.UnifiedCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedModuleExtraReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleExtraDTO;
import com.dianping.mobile.mapi.dztgdetail.entity.MrnVersionConfig;
import com.dianping.mobile.mapi.dztgdetail.exception.QueryCenterResultException;
import com.dianping.mobile.mapi.dztgdetail.helper.AppCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.FreeDealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.VersionUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.nibscp.common.api.enums.TradeTypeEnum;
import com.sankuai.general.product.query.center.client.builder.model.DealBasicInfoBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupBasicInfoBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupCategoryBuilder;
import com.sankuai.general.product.query.center.client.builder.model.ServiceProjectBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import com.sankuai.general.product.query.center.client.enums.AttrSubjectEnum;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import lombok.extern.log4j.Log4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created by zuomlin on 2018/12/13.
 */
@Component
@Log4j
public class UnifiedModuleExtraFacade {

    @Resource
    private MapperWrapper mapperWrapper;

    @Resource
    private DealGroupWrapper dealGroupWrapper;

    @Autowired
    private DouHuBiz douHuBiz;

    @Autowired
    private QueryCenterWrapper queryCenterWrapper;

    @Resource
    private DouHuService douHuService;

    @Resource
    private DealCategoryFactory dealCategoryFactory;


    private static final String THIRD_PARTY_VERIFY_KEY = "product_third_party_verify";
    private static final String REPORT_TYPE_KEY = "physical_examination_report_type";
    private static final String REPORT_TYPE_VALUE = "physical_examination_report_type";
    private static final String STANDARD_DEAL_GROUP_KEY = "standardDealGroupKey";
    public static final String ATTR_SERVICE_TYPE_LEAF_ID = "service_type_leaf_id";

    public static final String SERVICE_TYPE = "service_type";
    protected static final String ABOVE_TUAN_DETAIL = "abovetuandetail";
    /**
     * 穿戴甲团单标识key
     */
    private static final String WEARABLE_NAIL_KEY = "tag_unifyProduct";

    public ModuleExtraDTO queryUnifiedModuleExtraDTO(UnifiedModuleExtraReq request, EnvCtx envCtx, IMobileContext iMobileContext) throws Exception {
        if(GreyUtils.isQueryCenterGreyBatch3(request.getDealGroupId())) {
            try {
                return queryUnifiedModuleExtraDTOByQueryCenter(request, envCtx, iMobileContext);
            } catch (Exception e) {
                log.error("queryUnifiedModuleExtraDTO error", e);
            }
        }
        UnifiedCtx unifiedCtx = initUnifiedCtx(request, envCtx, iMobileContext);
        Future channelFuture = dealGroupWrapper.preDealGroupChannelById(unifiedCtx.getDpId());
        DealGroupChannelDTO dealGroupChannelDTO = dealGroupWrapper.getFutureResult(channelFuture);

        DealGroupAttributeResult attributeResult = null;
        DealGroupBaseDTO dealGroupBaseDTO = null;
        boolean isNewPhysicalExerciseModule = false;
        if(dealGroupChannelDTO != null && dealGroupChannelDTO.getCategoryId() == 401) {
            List<Integer> dealGroupIds = Lists.newArrayList(unifiedCtx.getDpId());
            List<String> attrs = Lists.newArrayList(REPORT_TYPE_KEY, THIRD_PARTY_VERIFY_KEY);
            Future attrFuture = dealGroupWrapper.preGetDealGroupAttrs(dealGroupIds, attrs);
            Future dealGroupBaseFuture = dealGroupWrapper.preDealGroupBase(unifiedCtx.getDpId());

            attributeResult = dealGroupWrapper.getFutureResult(attrFuture);
            dealGroupBaseDTO = dealGroupWrapper.getFutureResult(dealGroupBaseFuture);

            isNewPhysicalExerciseModule = isNewPhysicalExerciseModule(unifiedCtx, request.getMrnVersion(),
                    dealGroupChannelDTO.getCategoryId(),attributeResult, dealGroupBaseDTO);
        }
        return buildModuleExtraDTO(request, unifiedCtx, dealGroupChannelDTO == null ? 0 :
                dealGroupChannelDTO.getCategoryId(), isNewPhysicalExerciseModule, false,false, false);
    }

    public ModuleExtraDTO buildModuleExtraDTO(UnifiedModuleExtraReq request, UnifiedCtx unifiedCtx,
                                              int publishCategoryId, boolean isNewPhysicalExerciseModule,
                                              boolean isStandardEyeDealGroup, boolean isZeroResvDealGroup,
                                              boolean isWearableNail) {
        if (publishCategoryId <= 0) {
            return null;
        }
        List<ModuleConfigDo> moduleConfigDoList = getModuleConfigDoList(request, unifiedCtx, publishCategoryId,
                isNewPhysicalExerciseModule, isStandardEyeDealGroup,isZeroResvDealGroup, isWearableNail);
        moduleConfigDoList = filterFromCreateOrderPreview(request, moduleConfigDoList);
        moduleConfigDoList = resetItemIndexForCaiXi(request, unifiedCtx, publishCategoryId, moduleConfigDoList);
        ModuleExtraDTO moduleExtraDTO = new ModuleExtraDTO();
        moduleExtraDTO.setSuccess(true);
        moduleExtraDTO.setModuleConfigDos(moduleConfigDoList);
        moduleExtraDTO.setExpResults(unifiedCtx.getModuleAbConfigs());
        moduleExtraDTO.setExtraInfo(buildExtraInfoBySource(request));
        return moduleExtraDTO;
    }

    public List<ModuleConfigDo> getModuleConfigDoList(UnifiedModuleExtraReq request, UnifiedCtx unifiedCtx,
                                                      int publishCategoryId, boolean isNewPhysicalExerciseModule,
                                                      boolean isStandardEyeDealGroup, boolean isZeroResvDealGroup,
                                                      boolean isWearableNail) {
        // 团详框架改版
        ModuleAbConfig moduleAbConfig = douHuService.enableCardStyleV2(unifiedCtx.getEnvCtx(), publishCategoryId, unifiedCtx.getMrnVersion());
        // 判断是否命中 团详改版实验
        Boolean isTuanDetailV2 = douHuService.hitEnableCardStyleV2(moduleAbConfig);
        Map<String, List<ModuleConfigDo>> moduleConfigMaps = parseModuleConfigs(isTuanDetailV2, newModule(unifiedCtx), unifiedCtx.getEnvCtx().isAndroid());
        if (MapUtils.isEmpty(moduleConfigMaps)) {
            return Collections.emptyList();
        }
        //如果是0元预约：
        if (isZeroResvDealGroup){
            if (FreeDealUtils.useIndependentTab((long) publishCategoryId)) {
                return moduleConfigMaps.get((unifiedCtx.isMt() ? "mt" + publishCategoryId: "dp" + publishCategoryId) + "_resv");
            }
        }
        // 如果是穿戴甲
         if (isWearableNail) {
             String moduleConfigKey = (unifiedCtx.isMt() ? "mt" + publishCategoryId : "dp" + publishCategoryId) + "_wearableNail";
             return moduleConfigMaps.get(moduleConfigKey);
         }

        // 体检行业快手小程序特殊处理，临时方案
        if (Lion.getBoolean(LionConstants.APP_KEY, LionConstants.DISTRIBUTE_HEALTH_CHECK_KUAI_SHOU_CONFIG) && publishCategoryId == 401 && unifiedCtx.isKuaiShouMiniProgram() && unifiedCtx.isMt()) {
            return moduleConfigMaps.get("mt" + publishCategoryId + "_kuaishou");
        }
        if(isNewPhysicalExerciseModule) {
            if (useStandardPhysicalExamModule(unifiedCtx)) {
                return moduleConfigMaps.get((unifiedCtx.isMt() ? "mt" + publishCategoryId : "dp" + publishCategoryId) + "_standard");
            } else {
                return moduleConfigMaps.get((unifiedCtx.isMt() ? "mt" + publishCategoryId : "dp" + publishCategoryId) + "_new");
            }
        }
        if (isStandardEyeDealGroup) {
            return moduleConfigMaps.get((unifiedCtx.isMt() ? "mt" + publishCategoryId : "dp" + publishCategoryId) + "_standard");
        }
        for (Map.Entry<String, List<ModuleConfigDo>> entry : moduleConfigMaps.entrySet()) {
            if (StringUtils.isEmpty(entry.getKey())) {
                continue;
            }
            Set<String> categories = Sets.newHashSet(entry.getKey().split(","));
            if (categories.contains(buildKey(unifiedCtx, publishCategoryId))) {
                return entry.getValue();
            }
        }
        return moduleConfigMaps.get(unifiedCtx.isMt() ? "mt" : "dp");
    }
    
    public List<ModuleConfigDo> buildExtraInfoBySource(UnifiedModuleExtraReq request){
        if (isFromCreateOrderPreview(request)){
            Map<String, ModuleConfigDo> pagesourceMapKeyValueMap = LionConfigUtils.getPagesourceMapKeyValueMap();
            List<ModuleConfigDo> extraInfo = Lists.newArrayList();
            extraInfo.add(pagesourceMapKeyValueMap.get(request.getPageSource()));
            return extraInfo;
        }
        return null;
    }

    public List<ModuleConfigDo> filterFromCreateOrderPreview(UnifiedModuleExtraReq request, List<ModuleConfigDo> moduleConfigDoList){
        if (isFromCreateOrderPreview(request) && CollectionUtils.isNotEmpty(moduleConfigDoList)){
            return moduleConfigDoList.stream().filter(e->!isDealReviews(e.getValue())).collect(Collectors.toList());
        }
        return moduleConfigDoList;
    }

    private boolean isDealReviews(String config){
        Set<String> reviewerTabConfig = LionConfigUtils.getFilterReviewerTabConfig();
        return reviewerTabConfig.contains(config);
    }
    /**
     * 判断是否时上单预览场景
     * @param request
     * @return
     */
    public boolean isFromCreateOrderPreview(UnifiedModuleExtraReq request){
        if (Objects.nonNull(request) &&
                (RequestSourceEnum.CREATE_ORDER_PREVIEW.getSource().equals(request.getPageSource())
                    || DealUtils.isPreviewDeal(request.getPageSource()))) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    List<ModuleConfigDo> resetItemIndexForCaiXi(UnifiedModuleExtraReq request, UnifiedCtx unifiedCtx, int publishCategoryId, List<ModuleConfigDo> result) {
        if (!isNeedResetItemIndex(request, result)) {
            return result;
        }
        List<String> sortList = LionConfigUtils.getModuleConfigKeySort(buildKey(unifiedCtx, publishCategoryId));
        if (CollectionUtils.isEmpty(sortList)) {
            return result;
        }
        return resetConfigItemSort(result, sortList);
    }

    private List<ModuleConfigDo> resetConfigItemSort(List<ModuleConfigDo> result, List<String> sortList) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.UnifiedModuleExtraFacade.resetConfigItemSort(java.util.List,java.util.List)");
        List<ModuleConfigDo> sortedList = new ArrayList<>();
        for (String key : sortList) {
            ModuleConfigDo configDo = findModuleConfig(result, key);
            if (configDo != null) {
                sortedList.add(configDo);
            }
        }
        for (ModuleConfigDo configDo : result) {
            if (!sortedList.contains(configDo)) {
                sortedList.add(configDo);
            }
        }
        return sortedList;
    }

    private boolean isNeedResetItemIndex(UnifiedModuleExtraReq request, List<ModuleConfigDo> result) {
        return CollectionUtils.isNotEmpty(result) && fromCAIXI(request.getPageSource());
    }

    private ModuleConfigDo findModuleConfig(List<ModuleConfigDo> configDoList, String key) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.UnifiedModuleExtraFacade.findModuleConfig(java.util.List,java.lang.String)");
        return configDoList.stream()
                .filter(configDo -> configDo != null && configDo.getKey().equals(key))
                .findFirst().orElse(null);
    }

    private static Boolean fromCAIXI(String requestSource){
        if (RequestSourceEnum.CAI_XI.getSource().equals(requestSource) || RequestSourceEnum.HOME_PAGE.getSource().equals(requestSource)){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 若mrnVersion>=0.3.0 && 斗槲命中实验组,使用苏盈盈版团详模版，否则使用李南南版
     */
    private boolean useStandardPhysicalExamModule(UnifiedCtx context) {
        if (!VersionUtils.isGreatEqualThan(context.getMrnVersion(), "0.3.0")) {
            return false;
        }
        ModuleAbConfig config = douHuBiz.getAbExpResult(context.getEnvCtx(), context.isMt() ? "MTStandardPhysicalExamModule" : "DPStandardPhysicalExamModule");
        if (config == null || CollectionUtils.isEmpty(config.getConfigs())) {
            return false;
        }
        context.getModuleAbConfigs().add(config);
        return Optional.ofNullable(config.getConfigs().get(0))
                .map(AbConfig::isUseNewStyle)
                .orElse(false);
    }

    private static String buildKey(UnifiedCtx unifiedCtx, int publishCategoryId) {
        String key = unifiedCtx.isMt() ? "mt" + publishCategoryId : "dp" + publishCategoryId;
        if (isBeautyOlderVersion(unifiedCtx, publishCategoryId)) {
            key = key + "_old";
        }
        if (publishCategoryId == 414 && DealAttrHelper.isWuyoutong(unifiedCtx.getAttributes(), unifiedCtx.getMrnVersion())) {
            key = key + "_wuyoutong";
        }
        if (LionConfigUtils.isEduDealCategoryId(publishCategoryId) && isEduOnlineDeal(unifiedCtx.getAttributes())) {
            key = key + "_online";
        }
        return key;
    }

    private static boolean isEduOnlineDeal(List<AttributeDTO> attributes) {
        String serviceLeafIdStr = DealAttrHelper.getFirstValue(attributes, ATTR_SERVICE_TYPE_LEAF_ID);
        if (StringUtils.isEmpty(serviceLeafIdStr)) {
            return false;
        }
        Long serviceLeafId = Long.parseLong(serviceLeafIdStr);
        return LionConfigUtils.getEduOnlineDealServiceLeafIds().contains(serviceLeafId);
    }

    private static boolean isBeautyOlderVersion(UnifiedCtx unifiedCtx, int publishCategoryId) {
        try {
            if(unifiedCtx.isExternal()) {
                return false;
            }
            if(publishCategoryId == 501) {
                if(unifiedCtx.isMt() && VersionUtils.isGreatEqualThan("11.18.400", unifiedCtx.getVersion())) {
                    return true;
                }
                if(!unifiedCtx.isMt() && VersionUtils.isGreatEqualThan("10.59.0", unifiedCtx.getVersion())) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("version compare error, version is " + unifiedCtx.getVersion(),e);
        }

        return false;
    }

    private static Map<String, List<ModuleConfigDo>> parseModuleConfigs(boolean tuanDetailV2, boolean defaultModule, boolean android) {
        String lionConfig;
        if (tuanDetailV2){
            lionConfig = getTuanDetailV2Config(defaultModule, android);
        }else {
           lionConfig = getTuanDetailV1Config(defaultModule, android);
        }
        if (StringUtils.isBlank(lionConfig)) {
            return null;
        }
        List<Object> jsonArray = JsonFacade.deserializeList(lionConfig, Object.class);
        if (jsonArray == null || jsonArray.size() <= 0) {
            return null;
        }
        Map<String, List<ModuleConfigDo>> result = Maps.newHashMap();
        for (int i = 0; i < jsonArray.size(); i++) {
            Map<String, Object> obj = (Map<String, Object>) jsonArray.get(i);
            String publishCategories = String.valueOf(obj.get("categories"));
            if (StringUtils.isEmpty(publishCategories)) {
                continue;
            }
            List<Object> moduleJsonArray = (List<Object>) obj.get("configs");
            if (moduleJsonArray == null || moduleJsonArray.size() <= 0) {
                continue;
            }
            List<ModuleConfigDo> moduleConfigDos = Lists.newArrayList();
            for (int j = 0; j < moduleJsonArray.size(); j++) {
                ModuleConfigDo moduleConfigDo = new ModuleConfigDo();
                Map<String, Object> expObj = (Map<String, Object>) moduleJsonArray.get(j);
                String configKey = String.valueOf(expObj.get("configKey"));
                String configValue = String.valueOf(expObj.get("configValue"));
                if (StringUtils.isBlank(configKey) || StringUtils.isBlank(configValue)) {
                    continue;
                }
                moduleConfigDo.setKey(configKey);
                moduleConfigDo.setValue(configValue);
                moduleConfigDos.add(moduleConfigDo);
            }
            result.put(publishCategories, moduleConfigDos);
        }
        return result;
    }

    /**
     * 团详框架改版v1
     * @param defaultModule
     * @param android
     * @return
     */
    private static String getTuanDetailV1Config(boolean defaultModule, boolean android){
        String lionConfig;
        if (!defaultModule) {
            lionConfig = Lion.getStringValue(android ? LionConstants.ANDROID_CHANNEL_MODULE_CONFIGS : LionConstants.IOS_CHANNEL_MODULE_CONFIGS);
        } else {
            lionConfig = Lion.getStringValue(LionConstants.NEW_CHANNEL_MODULE_CONFIGS);
        }
        return lionConfig;
    }
    /**
     * 团详框架改版，获取团购详情tab配置
     * @param defaultModule
     * @param android
     * @return
     */
    private static String getTuanDetailV2Config(boolean defaultModule, boolean android){
        String lionConfig;
        if (!defaultModule) {
            lionConfig = Lion.getStringValue(android ? LionConstants.ANDROID_CHANNEL_MODULE_CONFIGS_V2 : LionConstants.IOS_CHANNEL_MODULE_CONFIGS_V2);
        } else {
            lionConfig = Lion.getStringValue(LionConstants.NEW_CHANNEL_MODULE_CONFIGS_V2);
        }
        return lionConfig;
    }

    private UnifiedCtx initUnifiedCtx(UnifiedModuleExtraReq request, EnvCtx envCtx, IMobileContext iMobileContext) {
        UnifiedCtx unifiedCtx = new UnifiedCtx(envCtx);

        if (envCtx.isMt()) {
            unifiedCtx.setMtId(request.getDealGroupId());
            unifiedCtx.setMtCityId(request.getCityId());
            Future dealIdMapperFuture = dealGroupWrapper.preDpDealGroupId(unifiedCtx.getMtId());
            Future cityIdMapperFuture = mapperWrapper.preDpCityByMtCity(unifiedCtx.getMtCityId());
            unifiedCtx.setDpId(dealGroupWrapper.getDpDealGroupId(dealIdMapperFuture));
            unifiedCtx.setDpCityId(mapperWrapper.getDpCityByMtCity(cityIdMapperFuture));
        } else {
            unifiedCtx.setDpCityId(request.getCityId());
            unifiedCtx.setDpId(request.getDealGroupId());
        }

        unifiedCtx.setExpResults(request.getExpResults());
        unifiedCtx.setToken(iMobileContext.getHeader().getNewToken());
        unifiedCtx.setVersion(envCtx.getVersion());
        unifiedCtx.setMrnVersion(request.getMrnVersion());
        unifiedCtx.setKuaiShouMiniProgram(AppCtxHelper.isKuaiShouMiniProgram(envCtx));
        return unifiedCtx;
    }

    private static boolean newModule(UnifiedCtx unifiedCtx) {
        if (StringUtils.isEmpty(unifiedCtx.getExpResults())) {
            return false;
        }
        List<String> results = JsonFacade.deserializeList(unifiedCtx.getExpResults().toLowerCase(), String.class);
        for (String exp : results) {
            if (Lion.getStringValue("com.sankuai.dzu.tpbase.dztgdetailweb.new.module.exp.results", "exp000002_a").contains(exp)) {
                return true;
            }
        }
        return false;
    }

    private static boolean isNewPhysicalExerciseModule(UnifiedCtx ctx, String mrnVersion,
                                                       int publishCategoryId, DealGroupAttributeResult attributeResult,
                                                       DealGroupBaseDTO dealGroupBaseDTO) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.UnifiedModuleExtraFacade.isNewPhysicalExerciseModule(UnifiedCtx,String,int,DealGroupAttributeResult,DealGroupBaseDTO)");
        if(ctx.isExternal() && !(DztgClientTypeEnum.MEITUAN_MAP_APP.equals(ctx.getEnvCtx().getDztgClientTypeEnum()))) {
            return false;
        }

        boolean isSwitchOn = Lion.getBoolean("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.physicalexam.newmodule.switch", false);
        if(!isSwitchOn) {
            return false;
        }

        Map<String, MrnVersionConfig> versionConfigMap = Lion.getMap("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.mrnversion.config.map", MrnVersionConfig.class, new HashMap<>());
        MrnVersionConfig versionConfig = versionConfigMap.get(String.valueOf(publishCategoryId));
        if(versionConfig == null) {
            return false;
        }

        // publishCategory == 401 && mrnVersion >= 0.2.0
        if(publishCategoryId == 401 && VersionUtils.isGreatEqualThan(mrnVersion, versionConfig.getLowestVersion())) {
            if(isNewPhysicalExerciseDealGroup(attributeResult)) {
                return isThirdPartyConditionPass(dealGroupBaseDTO);
            }
        }
        return false;
    }

    private static boolean isNewPhysicalExerciseModuleV2(UnifiedCtx ctx, String mrnVersion,
                                                       long publishCategoryId, String reportType,
                                                       boolean isThirdPartyVerify, int thirdPartyId) {
        if(ctx.isExternal() && !(DztgClientTypeEnum.MEITUAN_MAP_APP.equals(ctx.getEnvCtx().getDztgClientTypeEnum())) && !ctx.getEnvCtx().getWxMiniList().contains(ctx.getEnvCtx().getDztgClientTypeEnum())) {
            return false;
        }

        boolean isSwitchOn = Lion.getBoolean("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.physicalexam.newmodule.switch", false);
        if(!isSwitchOn) {
            return false;
        }

        Map<String, MrnVersionConfig> versionConfigMap = Lion.getMap("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.mrnversion.config.map", MrnVersionConfig.class, new HashMap<>());
        MrnVersionConfig versionConfig = versionConfigMap.get(String.valueOf(publishCategoryId));
        if(versionConfig == null) {
            return false;
        }

        // publishCategory == 401 && mrnVersion >= 0.2.0
        if(publishCategoryId == 401L && VersionUtils.isGreatEqualThan(mrnVersion, versionConfig.getLowestVersion())) {
            if(StringUtils.isNotBlank(reportType)) {
                return isThirdPartyConditionPass(isThirdPartyVerify, thirdPartyId);
            }
        }
        return false;
    }

    private static boolean isThirdPartyConditionPass(DealGroupBaseDTO dealGroupBaseDTO) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.UnifiedModuleExtraFacade.isThirdPartyConditionPass(com.dianping.deal.base.dto.DealGroupBaseDTO)");
        if(dealGroupBaseDTO == null) {
            return false;
        }
        if(!dealGroupBaseDTO.isThirdPartVerify()) {
            // 不是三方上单，后面逻辑不用判断了
            return true;
        }
        List<Integer> thirdPartyIdWhiteList = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.physicalexam.newmodule.thirdparty.whitelist", Integer.class, new ArrayList<>());

        List<DealBaseDTO> dealBaseDTOS = dealGroupBaseDTO.getDeals();
        if(CollectionUtils.isEmpty(dealBaseDTOS)) {
            return false;
        }
        DealBaseDTO deal = dealBaseDTOS.get(0);
        return thirdPartyIdWhiteList.contains(deal.getThirdPartyId());
    }

    private static boolean isThirdPartyConditionPass(boolean isThirdPartVerify, int thirdPartyId) {
        if(!isThirdPartVerify) {
            // 不是三方上单，后面逻辑不用判断了
            return true;
        }
        List<Integer> thirdPartyIdWhiteList = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.physicalexam.newmodule.thirdparty.whitelist", Integer.class, new ArrayList<>());

        return thirdPartyIdWhiteList.contains(thirdPartyId);
    }

    /**
     * 判断是否存在属性 physical_examination_report_type
     * @param attributeResult
     * @return
     */
    private static boolean isNewPhysicalExerciseDealGroup(DealGroupAttributeResult attributeResult) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.UnifiedModuleExtraFacade.isNewPhysicalExerciseDealGroup(com.dianping.deal.attribute.dto.DealGroupAttributeResult)");
        if(attributeResult == null) {
            return false;
        }
        List<DealGroupAttributeDTO> dealGroupAttributeDTOS = attributeResult.getContent();
        if(CollectionUtils.isEmpty(dealGroupAttributeDTOS)) {
            return false;
        }
        List<AttributeDTO> attributeDTOS = dealGroupAttributeDTOS.get(0) == null ? null : dealGroupAttributeDTOS.get(0).getAttributes();
        if(CollectionUtils.isEmpty(attributeDTOS)) {
            return false;
        }
        for(AttributeDTO attributeDTO : attributeDTOS) {
            if(attributeDTO == null) {
                continue;
            }
            if(REPORT_TYPE_VALUE.equals(attributeDTO.getName())) {
                return true;
            }
        }
        return false;
    }

    private static boolean isNewPhysicalExaminationDealGroup(String reportType) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.UnifiedModuleExtraFacade.isNewPhysicalExaminationDealGroup(java.lang.String)");
        return REPORT_TYPE_VALUE.equals(reportType);
    }


    /*
    RCF优化：Tab栏接口要和主接口合并了，后续迭代去主接口
     */
    private ModuleExtraDTO queryUnifiedModuleExtraDTOByQueryCenter(UnifiedModuleExtraReq request, EnvCtx envCtx, IMobileContext iMobileContext) {
        UnifiedCtx unifiedCtx = initUnifiedCtx(request, envCtx, iMobileContext);

        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet(Long.valueOf(request.getDealGroupId())), envCtx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP)
                .category(DealGroupCategoryBuilder.builder().categoryId().serviceType().serviceTypeId())
                .dealBasicInfo(DealBasicInfoBuilder.builder().thirdPartyId().status())
                .basicInfo(DealGroupBasicInfoBuilder.builder().tradeType())
                .serviceProject(ServiceProjectBuilder.builder().all())
                .attrsByKey(AttrSubjectEnum.DEAL_GROUP, Stream.concat(Arrays.stream(BathCategoryStrategyImpl.ATTR_ARR),
                        Stream.of(REPORT_TYPE_KEY, THIRD_PARTY_VERIFY_KEY, STANDARD_DEAL_GROUP_KEY,
                                ATTR_SERVICE_TYPE_LEAF_ID, SERVICE_TYPE, WEARABLE_NAIL_KEY)).toArray(String[]::new))
                .build();

        DealGroupDTO dealGroupDTO = null;
        try {
            dealGroupDTO = queryCenterWrapper.getDealGroupDTO(queryByDealGroupIdRequest);
        } catch (TException e) {
            log.error("queryCenterWrapper.getDealGroupDTO error", e);
            throw new QueryCenterResultException(e);
        }

        long publishCategoryId = (dealGroupDTO.getCategory() == null || dealGroupDTO.getCategory().getCategoryId() == null) ?
                0L : dealGroupDTO.getCategory().getCategoryId();
        boolean isNewPhysicalExerciseModule = false;
        if(401L == publishCategoryId) {
            String reportType = AttributeUtils.getFirstValueV2(dealGroupDTO.getAttrs(), REPORT_TYPE_KEY);
            boolean isThirdPartyVerify = parseBoolean(AttributeUtils.getFirstValueV2(dealGroupDTO.getAttrs(), THIRD_PARTY_VERIFY_KEY));
            int thirdPartyId = getThirdPartyId(dealGroupDTO);
            isNewPhysicalExerciseModule = isNewPhysicalExerciseModuleV2(unifiedCtx, request.getMrnVersion(),
                    (int) publishCategoryId, reportType, isThirdPartyVerify, thirdPartyId);
        }
        List<Long> eduCategoryIds = LionConfigUtils.getEduDealCategoryIds();
        // 管道疏通需要判断是否是无忧通团单，教育需要判断是不是在线教育
        if ((publishCategoryId == 414 || eduCategoryIds.contains(publishCategoryId) )&& CollectionUtils.isNotEmpty(dealGroupDTO.getAttrs())) {
            List<AttributeDTO> attrs = dealGroupDTO.getAttrs().stream().map(attrDTO -> {
                AttributeDTO attributeDTO = new AttributeDTO();
                attributeDTO.setName(attrDTO.getName());
                attributeDTO.setValue(attrDTO.getValue());
                attributeDTO.setSource(attrDTO.getSource());
                return attributeDTO;
            }).collect(Collectors.toList());
            unifiedCtx.setAttributes(attrs);
        }
        //判断是不是0元预约团单
        boolean isZeroResv = dealGroupDTO.getBasic() != null
                && dealGroupDTO.getBasic().getTradeType() != null
                && dealGroupDTO.getBasic().getTradeType() == TradeTypeEnum.RESERVATION.getCode()
                && Lion.getBoolean("com.sankuai.dzu.tpbase.dztgdetailweb","com.sankuai.dzu.tpbase.dztgdetailweb.zeroVaccine.switch",false);
        // 判断是否是穿戴甲
        boolean isWearableNail = DealUtils.isNewWearableNailDeal(dealGroupDTO);
        ModuleExtraDTO moduleExtraDTO = buildModuleExtraDTO(request, unifiedCtx, (int) publishCategoryId,
                isNewPhysicalExerciseModule, isStandardEyeDealGroup(unifiedCtx, dealGroupDTO), isZeroResv,
                isWearableNail);
        // 教育零元单
        if (moduleExtraDTO != null && isZeroResv && FreeDealEnum.EDU_TRIAL_BOOKING == FreeDealEnum.fromDealCategory(String.valueOf(publishCategoryId))) {
            return eduFreeDealModuleRename(moduleExtraDTO);
        }

        // 特定场景下修改"团购详情"的key
        dealCategoryFactory.resetDealDetailModuleConfig(DealCategoryParam.builder().envCtx(envCtx).dealCategoryId(publishCategoryId).moduleExtraDTO(moduleExtraDTO).dealGroupDTO(dealGroupDTO).build());
        return moduleExtraDTO;
    }

    public ModuleExtraDTO eduFreeDealModuleRename(ModuleExtraDTO moduleExtraDTO) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.UnifiedModuleExtraFacade.eduFreeDealModuleRename(com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleExtraDTO)");
        moduleExtraDTO.getModuleConfigDos().forEach(moduleConfigDO -> {
                    if (moduleConfigDO.getKey().equals("团购详情")) {
                        moduleConfigDO.setKey("课程详情");
                    }
                    if (moduleConfigDO.getKey().equals("购买须知")) {
                        moduleConfigDO.setKey("报名须知");
                    }
                });
        return moduleExtraDTO;
    }

    private boolean parseBoolean(String string){
        if (org.apache.commons.lang3.StringUtils.isBlank(string)) {
            return false;
        } else {
            return Boolean.parseBoolean(string);
        }
    }

    private int getThirdPartyId(DealGroupDTO dealGroupDTO) {
        if(dealGroupDTO == null || CollectionUtils.isEmpty(dealGroupDTO.getDeals())) {
            return 0;
        }
        for(DealGroupDealDTO dealDTO : dealGroupDTO.getDeals()) {
            if(dealDTO == null || dealDTO.getBasic() == null || dealDTO.getBasic().getStatus() != 1) {
                continue;
            }
            return Math.toIntExact(dealDTO.getBasic().getThirdPartyId() == null ? 0 : dealDTO.getBasic().getThirdPartyId());
        }
        return 0;
    }

    /**
     * 请求标准化团单且前端包版本>=0.4.0，下发新团详模版
     */
    private static boolean isStandardEyeDealGroup(UnifiedCtx unifiedCtx, DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO.getCategory() == null || dealGroupDTO.getCategory().getCategoryId() == null || dealGroupDTO.getCategory().getCategoryId() != 1604) {
            return false;
        }
        Long serviceProjectCategoryId = Optional.ofNullable(dealGroupDTO.getServiceProject())
                .map(DealGroupServiceProjectDTO::getMustGroups)
                .flatMap(mustGroups -> mustGroups.stream()
                        .findFirst()
                        .map(MustServiceProjectGroupDTO::getGroups)
                        .flatMap(groups -> groups.stream().findFirst()))
                .map(ServiceProjectDTO::getCategoryId)
                .orElse(0L);
        List<Long> standardEyeServiceProjectCategoryIds = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb", "com.sankuai.dzu.tpbase.dztgdetailweb.eye.standard.sku.category.ids", Long.class, new ArrayList<>());
        return VersionUtils.isGreatEqualThan(unifiedCtx.getMrnVersion(), "0.4.0") && standardEyeServiceProjectCategoryIds.contains(serviceProjectCategoryId);
    }
}
