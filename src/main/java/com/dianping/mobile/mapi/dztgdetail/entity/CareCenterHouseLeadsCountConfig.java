package com.dianping.mobile.mapi.dztgdetail.entity;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CommonAttrVO;
import lombok.Data;

import java.util.List;

/**
 * 参考文档 https://km.sankuai.com/collabpage/**********
 */
@Data
public class CareCenterHouseLeadsCountConfig {
    //是否启用Lion配置
    private boolean enable;

    //根据留资提供Id
    private Integer logicExpressionId;

    //业务Id
    private Integer bizId;

    //服务类型,三级团单Ids
    private List<Integer> serviceTypeIds;


}
