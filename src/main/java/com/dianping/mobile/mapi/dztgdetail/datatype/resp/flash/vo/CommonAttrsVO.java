package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/15 3:44 下午
 */
@MobileDo(id = 0x2c33)
public class CommonAttrsVO implements Serializable {
    /**
     * 属性值
     */
    @MobileDo.MobileField(key = 0x53c7)
    private List<String> values;

    /**
     * 属性名
     */
    @MobileDo.MobileField(key = 0x7ab8)
    private String name;

    public List<String> getValues() {
        return values;
    }

    public void setValues(List<String> values) {
        this.values = values;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
