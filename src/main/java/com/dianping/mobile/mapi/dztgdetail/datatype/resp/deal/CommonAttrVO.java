package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/5/8 16:19
 */
@Data
@MobileDo(id = 0xc871)
public class CommonAttrVO implements Serializable {

    @FieldDoc(description = "属性名")
    @MobileDo.MobileField(key = 0x7ab8)
    private String name;

    @FieldDoc(description = "属性值")
    @MobileDo.MobileField(key = 0x97dd)
    private String value;

    @FieldDoc(description = "属性详情浮层标识")
    @MobileDo.MobileField(key = 0x98e4)
    private String identify;

    public CommonAttrVO() {
    }

    public CommonAttrVO(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public CommonAttrVO(String name, String value, String identify) {
        this.name = name;
        this.value = value;
        this.identify = identify;
    }
}
