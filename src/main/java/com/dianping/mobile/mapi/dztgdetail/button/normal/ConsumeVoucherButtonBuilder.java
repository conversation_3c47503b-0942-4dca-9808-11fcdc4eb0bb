package com.dianping.mobile.mapi.dztgdetail.button.normal;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.CreateOrderPageUrlBiz;
import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.PromoHelper;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.dianping.mobile.mapi.dztgdetail.util.PromoInfoHelper;
import com.sankuai.dealuser.price.display.api.enums.CouponAssignStatusEnum;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/11/22
 */
@Slf4j
public class ConsumeVoucherButtonBuilder extends AbstractButtonBuilder {

    @Override
    public void build(DealCtx context, ButtonBuilderChain chain) {
        adaptIconTitle(context);
        chain.build(context);
    }

    private void adaptIconTitle(DealCtx context) {

        List<Map<String, Object>> tradeNeedMapList = new ArrayList<>();
        handleGovernmentConsumeCouponPromo(context, tradeNeedMapList);
        handleCouponPurchaseCouponPromos(context, tradeNeedMapList);
        if (CollectionUtils.isEmpty(tradeNeedMapList)) {
            return;
        }
        String appendUrlArg = "&otherpromptinfo=" + GsonUtils.toJsonString(tradeNeedMapList);

        for (DealBuyBtn buyBtn : context.getBuyBar().getBuyBtns()) {
            if (CreateOrderPageUrlBiz.isNotSupportBtnType(buyBtn)) {
                continue;
            }

            if (StringUtils.isNotBlank(buyBtn.getRedirectUrl())) {
                buyBtn.setRedirectUrl(buyBtn.getRedirectUrl() + appendUrlArg);
            }
        }
    }

    private void handleCouponPurchaseCouponPromos(DealCtx context, List<Map<String, Object>> tradeNeedMapList) {
        // 到综消费券二期支持美团微信小程序
        if (isMainApp(context.getEnvCtx())
                || DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP.equals(context.getEnvCtx().getDztgClientTypeEnum())) {
            PromoDTO couponPurchasePromoDTO = Optional.ofNullable(PromoHelper.getCouponPurchase(context))
                    .filter(CollectionUtils::isNotEmpty).orElse(new ArrayList<>()).stream().findFirst().orElse(null);
            if (couponPurchasePromoDTO == null) {
                return;
            }

            // 大于1个按钮， 不用处理
            if (context.getButtonCount() == 1  && showBtnCouponPurchaseText(couponPurchasePromoDTO)) {
                DealBuyBtn preButton = context.getPreButton();
                if (preButton != null) {
                    preButton.setBtnTitle(getPromoButtonTitle(context, "领券购买"));
                }
            }
            // 领券购
            Map<String, Object> map = getTradeNeedMap(couponPurchasePromoDTO, 2);
            if (MapUtils.isNotEmpty(map)) {
                tradeNeedMapList.add(map);
            }
        }
    }

    private void handleGovernmentConsumeCouponPromo(DealCtx context, List<Map<String, Object>> tradeNeedMapList) {
        if (isMainApp(context.getEnvCtx())) {
            PromoDTO governmentConsumeCouponPromoDTO = Optional.ofNullable(PromoHelper.getGovernmentConsumeCoupon(context))
                    .filter(CollectionUtils::isNotEmpty).orElse(new ArrayList<>()).stream().findFirst().orElse(null);
            if (governmentConsumeCouponPromoDTO == null) {
                return;
            }

            // 大于1个按钮， 不用处理
            if (context.getButtonCount() == 1 && showBtnCouponPurchaseText(governmentConsumeCouponPromoDTO)) {
                DealBuyBtn preButton = context.getPreButton();
                if (preButton != null) {
                    preButton.setBtnTitle(getPromoButtonTitle(context, "领券购买"));
                }
            }
            // 政府消费券
            Map<String, Object> map = getTradeNeedMap(governmentConsumeCouponPromoDTO, 1);
            if (MapUtils.isNotEmpty(map)) {
                tradeNeedMapList.add(map);
            }
        }
    }


    private boolean showBtnCouponPurchaseText(PromoDTO promoDTO) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.button.normal.ConsumeVoucherButtonBuilder.showBtnCouponPurchaseText(com.sankuai.dealuser.price.display.api.model.PromoDTO)");
        return promoDTO != null && promoDTO.getCouponAssignStatus() != null
                && promoDTO.getCouponAssignStatus().equals(CouponAssignStatusEnum.UN_ASSIGNED.getCode());
    }

    private Map<String, Object> getTradeNeedMap(PromoDTO item, int sceneType) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.button.normal.ConsumeVoucherButtonBuilder.getTradeNeedMap(com.sankuai.dealuser.price.display.api.model.PromoDTO,int)");
        Map<String, Object> map = new HashMap<>();
        map.put("receiptBatchId", item.getCouponGroupId());
        if (item.getCouponAssignStatus().equals(CouponAssignStatusEnum.ASSIGNED.getCode())) {
            map.put("receiptCode", item.getIdentity().getPromoId());
        }
        String financeExtJson = PromoInfoHelper.getFinanceExtJson(item);
        if (StringUtils.isNotBlank(financeExtJson)) {
            // 政府消费券N选1传的券包密钥
            map.put("ext", financeExtJson);
        }
        map.put("sceneType", sceneType);
        return map;
    }


    private boolean isMainApp(EnvCtx envCtx) {
        return DztgClientTypeEnum.MEITUAN_APP.equals(envCtx.getDztgClientTypeEnum())
                || DztgClientTypeEnum.DIANPING_APP.equals(envCtx.getDztgClientTypeEnum());
    }

    @Override
    protected void doBuild(DealCtx context, ButtonBuilderChain chain) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.button.normal.ConsumeVoucherButtonBuilder.doBuild(DealCtx,ButtonBuilderChain)");

    }
}