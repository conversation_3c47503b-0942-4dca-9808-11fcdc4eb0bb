package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.LayerConfig;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Author: <EMAIL>
 * @Date: 2024/12/24
 */
@Service
public class FeatureLayerBuilderService {
    @Resource
    private GuaranteeBuilderService guaranteeBuilderService;

    public LayerConfig getSafeImplantLayer(DealCtx ctx, LayerConfig layerConfig) {
        if (guaranteeBuilderService.hasSafeImplantTag(ctx)) {
            return layerConfig;
        }
        return null;
    }
}
