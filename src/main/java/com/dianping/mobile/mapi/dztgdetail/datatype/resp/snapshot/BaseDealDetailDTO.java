package com.dianping.mobile.mapi.dztgdetail.datatype.resp.snapshot;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.FeaturesLayer;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.Guarantee;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.highlights.DztgHighlightsModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealstruct.PnPurchaseNoteDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.ExhibitContentDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.Pair;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-10-23
 * @desc 团详基础信息
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class BaseDealDetailDTO implements Serializable {
    /**
     * 非结构化团购详情、购买须知、更多详情等
     */
    private List<Pair> structedDetails;

    /**
     * 结构化购买须知
     */
    @MobileDo.MobileField(key = 0x7105)
    private PnPurchaseNoteDTO pnPurchaseNoteDTO;

    /**
     * 是否使用结构化购买须知
     */
    @MobileDo.MobileField(key = 0x9551)
    private boolean hitStructuredPurchaseNote;

    /**
     * 款式信息
     */
    @MobileDo.MobileField(key = 0x6685)
    private ExhibitContentDTO exhibitContents;

    /**
     * 导购模块
     */
    @MobileDo.MobileField(key = 0xc97e)
    private DztgHighlightsModule highlightsModule;

    /**
     * 团单展示资源数组（头图、视频等）
     */
    @MobileDo.MobileField(key = 0x31fd)
    private List<ContentPBO> dealContents;

    /**
     * 限购条扩展字段
     */
    @MobileDo.MobileField(key = 0x1348)
    private List<Guarantee> limitsExtend;

    /**
     * 限制条款
     */
    @MobileDo.MobileField(key = 0x925e)
    private List<String> limits;

    /**
     * 新的须知信息，团详改版之后使用这个字段展示须知信息
     */
    @MobileDo.MobileField(key = 0xee60)
    private List<String> reminderInfo;

    /**
     * 团单特征（免预约、随时退、过期自动退），支持复杂结构
     */
    @MobileDo.MobileField(key = 0x4a49)
    private List<Guarantee> guarantee;

    /**
     * 团单特征（免预约、随时退、过期自动退）
     */
    @MobileDo.MobileField(key = 0xa91)
    private List<String> features;

    /**
     * 团单标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * 须知扩展字段
     */
    @MobileDo.MobileField(key = 0x3e30)
    private List<Guarantee> reminderExtend;

    /**
     * 团详样式数据模型
     */
    @MobileDo.MobileField(key = 0x5eb5)
    private ModuleConfigsModule moduleConfigsModule;

    @FieldDoc(description = "通用浮层信息")
    @MobileDo.MobileField(key = 0x8d8b)
    private FeaturesLayer generalFeaturesLayer;

    @FieldDoc(description = "价保标签、浮层信息")
    @MobileDo.MobileField(key = 0xc522)
    private FeaturesLayer featuresLayer;

    @FieldDoc(description = "团单后台类目ID，用于打点")
    @MobileDo.MobileField(key = 0x33fe)
    private Integer categoryId;
}
