package com.dianping.mobile.mapi.dztgdetail.common;

import com.dianping.cat.Cat;
import org.apache.commons.lang3.StringUtils;

/**
 * 迁移自 mapi-tgdetail-web
 */
public class DpVersion {
    private String version;  //major version + "."+minor version +"." +build + ...
    private int[] versionNum;
    public static final DpVersion TG_2_2 = new DpVersion("2.2");

    public static final DpVersion V7_6 = new DpVersion("7.6");
    public static final DpVersion V7_9_4 = new DpVersion("7.9.4");
    public static final DpVersion V9_0 = new DpVersion("9.0");
    public static final DpVersion V9_0_8 = new DpVersion("9.0.8");
    public static final DpVersion V9_2_4 = new DpVersion("9.2.4");
    public static final DpVersion V9_2_6 = new DpVersion("9.2.6");
    public static final DpVersion V9_5_0 = new DpVersion("9.5.0");
    public static final DpVersion V9_6_0 = new DpVersion("9.6.0");
    public static final DpVersion V9_6_3 = new DpVersion("9.6.3");
    public static final DpVersion V9_8_0 = new DpVersion("9.8.0");
    public static final DpVersion V9_4_0 = new DpVersion("9.4.0");
    public static final DpVersion V10_3_0 = new DpVersion("10.3.0");
    public static final DpVersion V10_5_0 = new DpVersion("10.5.0");
    public static final DpVersion V10_10_0 = new DpVersion("10.10.0");
    public static final DpVersion V10_41_0 = new DpVersion("10.41.0");

    public static final DpVersion V_H5 = new DpVersion("999.99.99"); //表示H5总是用最新的

    public DpVersion(String version) {
        boolean isValid = true;

        if (StringUtils.isNotBlank(version)) {
            String[] s = version.split("\\.");
            int[] versions = new int[s.length];
            for (int i = 0; i < s.length; i++) {
                int num = Integer.parseInt(s[i]);
                if (num < 0) {
                    isValid = false;
                } else {
                    versions[i] = num;
                }
            }
            if (isValid) {
                this.version = version;
                this.versionNum = versions;
            } else {
                throw new IllegalArgumentException();
            }
        } else {
            throw new IllegalArgumentException();
        }

    }

    public String getVersion() {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.common.DpVersion.getVersion()");
        return this.version;
    }

    public String toString() {
        return this.version;
    }

    public int[] getVersionNum() {
        return versionNum;
    }

    public void setVersionNum(int[] versionNum) {
        this.versionNum = versionNum;
    }

    /**
     * return 1,versionA>versionB
     * return 0,versionA=versionB
     * return -1,versionA<versionB
     *
     * @param versionA
     * @param versionB
     * @return
     */
    public static int compareTo(String versionA, String versionB) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.common.DpVersion.compareTo(java.lang.String,java.lang.String)");
        if (StringUtils.isBlank(versionA)) {
            return -1;
        } else if (StringUtils.isBlank(versionB)) {
            return 1;
        }
        return compareTo(new DpVersion(versionA), new DpVersion(versionB));
    }

    /**
     * return 1,versionA>versionB
     * return 0,versionA=versionB
     * return -1,versionA<versionB
     *
     * @param versionA
     * @param versionB
     * @return
     */
    public static int compareTo(DpVersion versionA, DpVersion versionB) {
        if (versionA == null) {
            return -1;
        } else if (versionB == null) {
            return 1;
        }
        int[] versionASplit = versionA.getVersionNum();
        int[] versionBSplit = versionB.getVersionNum();

        if (versionASplit.length != versionBSplit.length) {
            if (versionASplit.length > versionBSplit.length) {
                for (int l = 0; l < versionBSplit.length; l++) {
                    if (versionASplit[l] > versionBSplit[l]) {
                        return 1;
                    } else if (versionASplit[l] < versionBSplit[l]) {
                        return -1;
                    } else {
                        continue;
                    }
                }
                for (int i = versionASplit.length - 1; i >= versionBSplit.length; i--) {
                    if (versionASplit[i] != 0) {
                        return 1;
                    }
                }
                return 0;
            } else {
                for (int h = 0; h < versionASplit.length; h++) {
                    if (versionASplit[h] > versionBSplit[h]) {
                        return 1;
                    } else if (versionASplit[h] < versionBSplit[h]) {
                        return -1;
                    } else {
                        continue;
                    }
                }
                for (int j = versionBSplit.length - 1; j >= versionASplit.length; j--) {
                    if (versionBSplit[j] != 0) {
                        return -1;
                    }
                }
                return 0;
            }
        } else {
            for (int k = 0; k < versionASplit.length; k++) {
                if (versionASplit[k] > versionBSplit[k]) {
                    return 1;
                } else if (versionASplit[k] < versionBSplit[k]) {
                    return -1;
                } else {
                    continue;
                }
            }
            return 0;
        }
    }
}