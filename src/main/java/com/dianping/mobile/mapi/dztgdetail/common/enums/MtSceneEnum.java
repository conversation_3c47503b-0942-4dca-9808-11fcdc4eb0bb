package com.dianping.mobile.mapi.dztgdetail.common.enums;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/4/21.
 */
public enum MtSceneEnum {
    VIEW_V4(1, "view-v4"),
    SAME_BRAND(2, "samebrand"),
    PAY(3, "pay");
    private int key;
    private String value;

    MtSceneEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public int getKey() {
        return key;
    }


    public String getValue() {
        return value;
    }

    public static MtSceneEnum getEnumByKey(int key) {
        for (MtSceneEnum scene : MtSceneEnum.values()) {
            if (scene.getKey() == key) {
                return scene;
            }
        }
        return VIEW_V4;
    }
}
