package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.basic;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiClientWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ReserveProductWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import com.google.common.collect.Sets;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Set;
import java.util.concurrent.Future;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2023/8/31
 * @since mapi-dztgdetail-web
 */
public class DealBuilderInfoProcessor extends AbsDealProcessor {

    private static final Set<Integer> PARENT_CHILD_FUN = Sets.newHashSet(1002);

    @Autowired
    private ReserveProductWrapper reserveProductWrapper;

    @Autowired
    private DealGroupWrapper dealGroupWrapper;

    @Autowired
    PoiClientWrapper poiClientWrapper;

    @Override
    public void prepare(DealCtx ctx) {
        if (ReserveProductWrapper.reserveAfterPurchase(ctx.getCategoryId())) {
            Future reserveProductFuture = reserveProductWrapper.prepareOnlineReserve(ctx);
            ctx.getFutureCtx().setJudgeDpIdReserveOnlineFuture(reserveProductFuture);
        }

        if (ctx.getCategoryId() == 401) {
            ctx.getFutureCtx().setDealGroupThirdPartyFuture(dealGroupWrapper.preDealGroupThirdParty(ctx.getDpId()));
        }

       if(ctx.getBestShopResp() != null && ctx.getChannelDTO() != null) {
           int categoryId = ctx.getChannelDTO().getCategoryId();
           List<Integer> categoryList = Lion.getList(LionConstants.POI_DISTANCE_CATEGORY_BLACKLIST, Integer.TYPE, Lists.newArrayList());
            if(categoryList.contains(categoryId)) {
                ctx.getFutureCtx().setPoiBizAttrFuture(poiClientWrapper.prePoiBizAttrFuture(ctx.getDpLongShopId()));
            }
       }

        if (!DealAttrHelper.isWuyoutong(ctx) && PARENT_CHILD_FUN.contains(ctx.getCategoryId())) {
            ctx.getFutureCtx().setBookStatusFuture(poiClientWrapper.preBookStatusFuture(ctx));
       }
    }

    @Override
    public void process(DealCtx ctx) {

    }
}
