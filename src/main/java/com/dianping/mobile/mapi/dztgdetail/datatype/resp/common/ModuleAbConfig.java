package com.dianping.mobile.mapi.dztgdetail.datatype.resp.common;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@TypeDoc(description = "打点信息模型")
@MobileDo(id = 0x3abc)
@Data
public final class ModuleAbConfig implements Serializable {

    @FieldDoc(description = "模块名字")
    @MobileField(key = 0x9e5e)
    private String key;

    @FieldDoc(description = "打点信息")
    @MobileField(key = 0xddc1)
    private List<AbConfig> configs;
}
