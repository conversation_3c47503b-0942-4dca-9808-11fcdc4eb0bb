package com.dianping.mobile.mapi.dztgdetail.facade.repaircare;


import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DzRepairCareModule;
import com.dianping.mobile.mapi.dztgdetail.entity.repaircare.CategoryPicConfig;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.google.common.collect.Sets;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.athena.biz.Response;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupBasicInfoBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupCategoryBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupDisplayShopBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.AttrSubjectEnum;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.enums.ResponseCodeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import com.sankuai.zdc.tag.apply.api.PoiTagDisplayRPCService;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import com.sankuai.zdc.tag.apply.dto.FindSceneDisplayTagRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DzRepairCareFacade {

    @Autowired
    @Qualifier("queryCenterDealGroupQueryService")
    private DealGroupQueryService queryCenterDealGroupQueryService;

    @Resource
    @Qualifier("poiTagDisplayRPCService")
    private PoiTagDisplayRPCService poiTagDisplayRPCService;

    private static final String PAY_METHOD_ATTR = "pay_method";

    private static final String SUPPORT_FINAL_PAYMENT = "2";

    private static final String REPAIR_CARE_TAG = "SHENGHUOFUWU_MERCHANT_DETAILS_GUARANTEE";

    public DzRepairCareModule getDzRepairCareModule(int dpDealGroupId) {
        DealGroupDTO dealGroupDTO = getDealGroupDTO(dpDealGroupId);
        if (Objects.isNull(dealGroupDTO) || !isSupportFinalPayment(dealGroupDTO)) {
            return null;
        }
        int categoryId = getDealSecondCategoryId(dealGroupDTO);
        Long serviceType = getServiceType(dealGroupDTO);
        Map<String, CategoryPicConfig> dzRepairCareCategoryPicMap = LionConfigUtils.getRepairCareCategoryPicConfig();
        if (!checkCategoryAndServiceType(categoryId, serviceType, dzRepairCareCategoryPicMap)) {
            return null;
        }
        // 判断门店标签为安心修
        if (!checkShopTag(dealGroupDTO)) {
            return null;
        }
        CategoryPicConfig categoryPicConfig = dzRepairCareCategoryPicMap.get(String.valueOf(categoryId));
        DzRepairCareModule module = new DzRepairCareModule();
        module.setJumpUrl(categoryPicConfig.getJumpUrl());
        module.setPicture(categoryPicConfig.getPic());
        return module;
    }

    private boolean checkShopTag(DealGroupDTO dealGroupDTO) {
        if (Objects.isNull(dealGroupDTO) || Objects.isNull(dealGroupDTO.getDisplayShopInfo())) {
            return false;
        }
        // todo xiangrui 可以接受截断
        List<Long> mtDisplayShopIds = dealGroupDTO.getDisplayShopInfo().getMtDisplayShopIds();
        if (CollectionUtils.isEmpty(mtDisplayShopIds)) {
            return false;
        }
        //截断20个门店作查询，任一门店接入安心修则展示
        mtDisplayShopIds = mtDisplayShopIds.stream().limit(20).collect(Collectors.toList());
        FindSceneDisplayTagRequest request = new FindSceneDisplayTagRequest();
        request.setBizCode(REPAIR_CARE_TAG);
        request.setShopIds(mtDisplayShopIds);
        Response<Map<Long, List<DisplayTagDto>>> response = null;
        try {
            response = poiTagDisplayRPCService.findSceneDisplayTagOfMt(request);
        } catch (Exception e) {
            log.error("查询安心修门店标签失败, mtShopIds : {}", mtDisplayShopIds, e);
        }
        if (response == null || !response.isSuccess() || MapUtils.isEmpty(response.getData())) {
            return false;
        }
        return response.getData().entrySet().stream().anyMatch(entry -> CollectionUtils.isNotEmpty(entry.getValue()));
    }


    private boolean checkCategoryAndServiceType(int categoryId, Long serviceType, Map<String, CategoryPicConfig> dzRepairCareCategoryPicMap) {
        if (categoryId == 0 || Objects.isNull(serviceType) || serviceType <= 0) {
            return false;
        }
        //居家维修中需要排除三级团单类目防水治漏
        if (categoryId == 445 && serviceType == 275L) {
            return false;
        }
        return dzRepairCareCategoryPicMap.containsKey(String.valueOf(categoryId));
    }

    private int getDealSecondCategoryId(DealGroupDTO dealGroupDTO) {
        if (Objects.isNull(dealGroupDTO) || Objects.isNull(dealGroupDTO.getBasic()) || Objects.isNull(dealGroupDTO.getBasic().getCategoryId())) {
            return 0;
        }
        return dealGroupDTO.getBasic().getCategoryId().intValue();
    }

    private Long getServiceType(DealGroupDTO dealGroupDTO) {
        return dealGroupDTO == null || dealGroupDTO.getCategory() == null
                ? null : dealGroupDTO.getCategory().getServiceTypeId();
    }

    private DealGroupDTO getDealGroupDTO(long dpDealGroupId) {
        QueryByDealGroupIdRequest request = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet(dpDealGroupId), IdTypeEnum.DP)
                .category(DealGroupCategoryBuilder.builder().all())
                .attrsByKey(AttrSubjectEnum.DEAL_GROUP, PAY_METHOD_ATTR)
                .displayShop(DealGroupDisplayShopBuilder.builder().all())
                .basicInfo(DealGroupBasicInfoBuilder.builder().all()).build();
        try {
            QueryDealGroupListResponse listResponse = queryCenterDealGroupQueryService.queryByDealGroupIds(request);
            if (Objects.isNull(listResponse) || listResponse.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                log.warn("DealHeadPictureFetch查询失败，listResponse={}", listResponse);
                return null;
            }
            if (Objects.isNull(listResponse.getData()) || CollectionUtils.isEmpty(listResponse.getData().getList())) {
                log.info("DealHeadPictureFetch查询数据为空，listResponse={}", listResponse);
                return null;
            }
            return listResponse.getData().getList().get(0);
        } catch (Exception e) {
            log.error("queryByDealGroupIds失败，dealGroupId={}", dpDealGroupId, e);
        }
        return null;
    }

    /**
     * 判断线上尾款商品团单
     * @param dealGroupDTO 团单数据传输对象
     * @return true：线上尾款商品团单，false：不是线上尾款商品团单
     */
    private boolean isSupportFinalPayment(DealGroupDTO dealGroupDTO) {
        List<AttrDTO> attrs = dealGroupDTO.getAttrs();
        if (CollectionUtils.isEmpty(attrs)) {
            return false;
        }
        for (AttrDTO attr : attrs) {
            if (attr == null || CollectionUtils.isEmpty(attr.getValue()) || StringUtils.isEmpty(attr.getName())) {
                continue;
            }
            if (PAY_METHOD_ATTR.equals(attr.getName()) && attr.getValue().contains(SUPPORT_FINAL_PAYMENT)) {
                return true;
            }
        }
        return false;
    }



}
