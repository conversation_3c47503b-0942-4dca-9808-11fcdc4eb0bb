package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MpAppIdEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;

public class AppCtxHelper {

    public static int getAppClientType(IMobileContext appCtx){
        int clientType = 0;

        if(appCtx == null){
            return clientType;
        }

        if(isMeituanClient(appCtx)){
            return appCtx.isIOS() ? ClientTypeEnum.mt_mainApp_ios.getType() : ClientTypeEnum.mt_mainApp_android.getType();
        }else{
            return appCtx.isIOS() ? ClientTypeEnum.dp_mainApp_ios.getType() : ClientTypeEnum.dp_mainApp_android.getType();
        }
    }

    public static boolean isKuaiShouMiniProgram(EnvCtx envCtx) {
        if (!envCtx.isExternal()) {
            return false;
        }
        return MpAppIdEnum.MT_KUAISHOU_MINIPROGRAM.getMpAppId().equals(envCtx.getMpAppId())
                || "kuaishou".equals(envCtx.getMpSource());
    }

    public static boolean isMeituanClient(IMobileContext appCtx) {
        if(appCtx == null) {
            return true;
        }
        if(appCtx.getAppId() == 396) {
            return true;
        }
        return appCtx.isMeituanClient();
    }

}
