package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.deal.common.enums.GpsType;
import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.deal.shop.dto.BestShopReq;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.LionUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.PreviewDealGroupUtils;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;


/**
 * 由于预览团单的门店信息变更频繁，且团单-门店关联关系的查询是通过搜索引擎，延迟较严重，导致预览团单的门店信息时常显示错误门店
 * 故增加此processor，在判断团单为预览单后，通过事先在本地查出的门店ID作为参数，查询门店详细信息
 * <AUTHOR>
 */
public class PreviewBestShopProcessor extends AbsDealProcessor {

    @Autowired
    private DealGroupWrapper dealGroupWrapper;

    /**
     * 若是预览单，则开启
     * @param ctx context
     * @return boolean
     */
    @Override
    public boolean isEnable(DealCtx ctx) {
        return PreviewDealGroupUtils.isPreviewDianpingDealGroup(ctx);
    }

    @Override
    public void prepare(DealCtx ctx) {
        if(GreyUtils.enableQueryCenterForMainApi(ctx)) {
            // 插入门店数量查询
            int dealGroupId = ctx.isMt() ? ctx.getMtId() : ctx.getDpId();
            Future<?> future = dealGroupWrapper.preDealShopQtyBySearchFuture(dealGroupId, ctx.isMt());
            ctx.getFutureCtx().setDealShopQtyBySearchFuture(future);

            return;
        } else {
            // todo xiangrui 已经被灰度了，可以移除了
            Future previewShopIdFuture = dealGroupWrapper.preDealGroupDisplayShopIds(Lists.newArrayList(ctx.getDpId()));
            ctx.getFutureCtx().setPreviewShopIdFuture(previewShopIdFuture);
        }
    }

    @Override
    public void process(DealCtx ctx) {
        if(GreyUtils.enableQueryCenterForMainApi(ctx)) {
            // todo xiangrui 查询门店数量，在查询limit 的值，取第一个，可以截断
            List<Long> displayShopIds = getDisplayShopIds(ctx.getDealGroupDTO());
            if(CollectionUtils.isNotEmpty(displayShopIds)) {
                ctx.setDpLongShopId(displayShopIds.get(0));
                ctx.setPreviewShopTotalNum(displayShopIds.size());
            }
            // 如果查询到了新的商户数据量，则用新的替代
            long dealShopQtyBySearch = dealGroupWrapper.getDealShopQtyBySearch(ctx.getFutureCtx().getDealShopQtyBySearchFuture(),
                    ctx.isMt() ? ctx.getMtId() : ctx.getDpId());
            if (LionUtils.dealDisplayShopQtySwitch() && dealShopQtyBySearch > 0) {
                ctx.setPreviewShopTotalNum((int) dealShopQtyBySearch);
            }
            setBestShop(ctx);
        } else {
            Map<Integer, List<Long>> dpDealgroupId2ShopIdListMap = dealGroupWrapper
                    .getFutureResult(ctx.getFutureCtx().getPreviewShopIdFuture());
            if(dpDealgroupId2ShopIdListMap != null) {
                List<Long> shopIdList = dpDealgroupId2ShopIdListMap.getOrDefault(ctx.getDpId(), Lists.newArrayList(0L));
                if(CollectionUtils.isNotEmpty(shopIdList)) {
                    ctx.setDpLongShopId(shopIdList.get(0));
                    ctx.setPreviewShopTotalNum(shopIdList.size());
                }
            }
            setBestShop(ctx);
        }
    }

    private List<Long> getDisplayShopIds(DealGroupDTO dealGroupDTO) {
        if(dealGroupDTO == null || dealGroupDTO.getDisplayShopInfo() == null || dealGroupDTO.getDisplayShopInfo().getDpDisplayShopIds() == null) {
            return Collections.emptyList();
        }
        return dealGroupDTO.getDisplayShopInfo().getDpDisplayShopIds();
    }

    private void setBestShop(DealCtx ctx) {
        BestShopReq shopReq = new BestShopReq();
        if (ctx.isMt()) {
            shopReq.setDealGroupId(ctx.getMtId());
            shopReq.setShopId(ctx.getMtLongShopId());
            shopReq.setCityId(ctx.getMtCityId());
        } else {
            shopReq.setDealGroupId(ctx.getDpId());
            shopReq.setShopId(ctx.getDpLongShopId());
            shopReq.setCityId(ctx.getDpCityId());
        }
        shopReq.setLat(ctx.getUserlat());
        shopReq.setLng(ctx.getUserlng());
        shopReq.setGpsType(GpsType.GCJ02.getType());
        shopReq.setClientType(ctx.getEnvCtx().getClientType());
        Future shopFuture = dealGroupWrapper.preDealGroupBestShop(shopReq);
        ctx.getFutureCtx().setBestShopFuture(shopFuture);

        BestShopDTO bestShop = dealGroupWrapper
                .getFutureResult(ctx.getFutureCtx().getBestShopFuture());
        if (bestShop == null) {
            return;
        }
        bestShop.setTotalShopsNum(ctx.getPreviewShopTotalNum());
        ctx.setPreviewDeal(true);
        ctx.setBestShopResp(bestShop);
        if (ctx.getLongPoiId4PFromReq() > 0) {
            return;
        }
        ctx.setDpLongShopId(bestShop.getDpShopId());
        ctx.setMtLongShopId(bestShop.getMtShopId());
    }
}
