package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.Guarantee;
import com.dianping.mobile.mapi.dztgdetail.entity.ReminderExtendConfig;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.unavailable.UnavailabelDate;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.unavailable.UnavailableDateConfigService;
import com.dianping.mobile.mapi.dztgdetail.helper.AvailableTimeHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.DateHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.AvailableDateDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.AvailableDurationDateDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.CycleAvailableDateDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DateRangeDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DisableDateDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/12/13 14:28
 */
@Slf4j
@Component
public class ReminderInfoBuilderService {

    @Autowired
    private UnavailableDateConfigService dateConfig;
    /**
     * 构造春节不打烊
     * @param result
     */
    public void buildSpringFestivalBanner(DealCtx ctx, DealGroupPBO result){
        List<Guarantee> reminderExtend = result.getReminderExtend();
        if (CollectionUtils.isEmpty(reminderExtend)){
            reminderExtend = Lists.newArrayList();
            result.setReminderExtend(reminderExtend);
        }
        ReminderExtendConfig config = LionConfigUtils.getReminderExtendInfo("springFestival");
        // 判断是否在可用时间内
        if (LionConfigUtils.validSpringFestivalConfig(config)) {
            // 判断是否命中 春节不可用标签
            boolean springFestivalDisabled = DealAttrHelper.judgeDisableUsable(ctx.getDealGroupDTO())
                                                || DealAttrHelper.hitDisableDateType(new DealGroupDTO(), 102);
            if (!springFestivalDisabled){
                // 命中：有春节活动标签 且 春节期间可用
                return;
            }
            Map<String, String> reminderInfoMap = config.getReminderInfo();
            // 2025.1.23春不新策略调整,走 春节不可用文案
            String text = getDisplayText(reminderInfoMap, true, springFestivalDisabled);
            reminderExtend.add(buildGuarantee(text, reminderInfoMap.get("icon"), getType(reminderInfoMap.get("type")), reminderInfoMap.get("style")));
        } else if (LionConfigUtils.judgeUnavailableDateConfig(ctx.getCategoryId())) {
            // 判断是否在不可用日期
            handleDisabledDate(reminderExtend, ctx);
        }
    }

    public void handleDisabledDate(List<Guarantee> reminderExtend, DealCtx ctx) {
        DisableDateDTO disableDateDTO = Optional.ofNullable(ctx.getDealGroupDTO()).map(DealGroupDTO::getRule)
                .map(DealGroupRuleDTO::getUseRule).map(DealGroupUseRuleDTO::getDisableDate).orElse(null);
        // 命中周几不可用
        List<String> weekDay = Optional.ofNullable(ctx.getDealGroupDTO()).map(DealGroupDTO::getAttrs)
                .orElse(Lists.newArrayList()).stream().filter(attr -> StringUtils.equals(attr.getName(), "TimeRange3"))
                .findFirst().map(AttrDTO::getValue).orElse(null);
        List<Integer> weekDayInt = AvailableTimeHelper.getWeekDayInt(weekDay);
        int todayWeekDay = AvailableTimeHelper.getTodayWeekDay();
        if (CollectionUtils.isNotEmpty(weekDayInt) && !weekDayInt.contains(todayWeekDay)) {
            Guarantee tips = processDisabledTips();
            reminderExtend.add(tips);
            return;
        }

        // 命中每周以及节假日不可用日期
        List<Integer> disableDays = Optional.ofNullable(disableDateDTO).map(DisableDateDTO::getDisableDays)
                .orElse(Lists.newArrayList());
        disableDays = handleSpecialDisableDays(ctx.getDealGroupDTO(), disableDays);
        if (CollectionUtils.isNotEmpty(disableDays)
                && hitWeekAndHolidayUnavailable(disableDays, dateConfig.getConfig())) {
            Guarantee tips = processDisabledTips();
            reminderExtend.add(tips);
            return;
        }

        // 命中自定义不可用日期
        List<DateRangeDTO> disableDateRangeDTOS = Optional.ofNullable(disableDateDTO)
                .map(DisableDateDTO::getDisableDateRangeDTOS).orElse(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(disableDateRangeDTOS) && hitCustomUnavailable(disableDateRangeDTOS)) {
            Guarantee tips = processDisabledTips();
            reminderExtend.add(tips);
            return;
        }

        Long skuId = StringUtils.isNotBlank(ctx.getSkuId()) && NumberUtils.isCreatable(ctx.getSkuId())
                ? Long.parseLong(ctx.getSkuId()) : 0L;
        // 命中工作日-周末档不可用日期
        String weekendTime = Optional.ofNullable(ctx.getDealGroupDTO()).map(DealGroupDTO::getDeals)
                .filter(CollectionUtils::isNotEmpty).orElse(Lists.newArrayList()).stream()
                .filter(deal -> Objects.equals(deal.getDealId(), skuId)).findFirst().map(DealGroupDealDTO::getAttrs)
                .orElse(Lists.newArrayList()).stream().filter(attr -> StringUtils.equals(attr.getName(), "weekendTime"))
                .findFirst().map(AttrDTO::getValue).orElse(Lists.newArrayList()).stream().findFirst()
                .orElse(StringUtils.EMPTY);
        if (StringUtils.isNotBlank(weekendTime) && hitWeekendUnavailable(weekendTime)) {
            Guarantee tips = processDisabledTips();
            reminderExtend.add(tips);
            return;
        }

        // 如果设置了可用时间需要进一步判断
        AvailableDateDTO availableDateDTO = Optional.ofNullable(ctx.getDealGroupDTO()).map(DealGroupDTO::getRule)
                .map(DealGroupRuleDTO::getUseRule).map(DealGroupUseRuleDTO::getAvailableDate).orElse(null);
        if (availableDateDTO != null && hitAvailableTime(availableDateDTO)) {
            Guarantee tips = processDisabledTips();
            reminderExtend.add(tips);
            return;
        }
    }

    /**
     * 判断是否命中可用时间,true 表示当前可用, false 表示当前不可用
     */
    public boolean hitAvailableTime(AvailableDateDTO availableDateDTO) {
        if (availableDateDTO == null || availableDateDTO.getAvailableType() == null) {
            return true;
        }

        Integer availableType = availableDateDTO.getAvailableType();
        // 判断可用时间
        // 可用日期类型：0：周期使用，1：指定日期适用
        if (availableType == 0) {
            // 周期时间可用 例如：周一 ~~ 周日 可用，判断字段 CycleAvailableDateList
            List<CycleAvailableDateDTO> cycleAvailableDateList = availableDateDTO.getCycleAvailableDateList();
            if (CollectionUtils.isNotEmpty(cycleAvailableDateList)) {
                Set<Integer> availableDaysSet = cycleAvailableDateList.stream()
                        .filter(e -> e.getAvailableDays() != null).flatMap(e -> e.getAvailableDays().stream())
                        .collect(Collectors.toSet());
                return availableDaysSet.contains(dayOfWeek());
            }
        } else if (availableType == 1) {
            // 指定日期可用 判断字段 specifiedDurationDateList
            List<AvailableDurationDateDTO> specifiedDurationDateList = availableDateDTO.getSpecifiedDurationDateList();
            if (CollectionUtils.isNotEmpty(specifiedDurationDateList)) {
                return validSpecifiedDurationDateList(availableDateDTO.getSpecifiedDurationDateList());
            }
        }
        return true;
    }

    public boolean validSpecifiedDurationDateList(List<AvailableDurationDateDTO> specifiedDurationDateList) {
        if (specifiedDurationDateList != null) {
            return specifiedDurationDateList.stream()
                    .filter(e -> Objects.nonNull(e)
                            && org.apache.commons.collections.CollectionUtils.isNotEmpty(e.getAvailableDateRangeDTOS()))
                    .anyMatch(e -> validAvailableDateRange(e.getAvailableDateRangeDTOS()));
        }
        return false;
    }

    public static boolean validAvailableDateRange(List<DateRangeDTO> availableDateRangeDTOS) {
        if (CollectionUtils.isEmpty(availableDateRangeDTOS)) {
            return false;
        }
        return availableDateRangeDTOS.stream().anyMatch(e -> DateHelper.isCurrentDateInRange(e.getFrom(), e.getTo()));
    }

    public boolean hitWeekendUnavailable(String weekendTime) {
        // [1,2,3,4] 这个是一周中可用的天数
        if (com.dianping.zebra.util.StringUtils.isBlank(weekendTime)) {
            return false;
        }
        return !weekendTime.contains(String.valueOf(dayOfWeek()));
    }

    public boolean hitCustomUnavailable(List<DateRangeDTO> disableDateRangeDTOS) {
        if (CollectionUtils.isEmpty(disableDateRangeDTOS)) {
            return false;
        }
        return disableDateRangeDTOS.stream().filter(e -> e != null && e.getFrom() != null && e.getTo() != null)
                .anyMatch(e -> DateHelper.isCurrentDateInRange(e.getFrom(), e.getTo()));
    }

    private List<Integer> handleSpecialDisableDays(DealGroupDTO dealGroupDTO, List<Integer> disableDays) {
        String availableTimePeriod3 = Optional.ofNullable(dealGroupDTO).map(DealGroupDTO::getAttrs)
                .orElse(Lists.newArrayList()).stream()
                .filter(attr -> StringUtils.equals(attr.getName(), "AvailableTimePeriod3")).findFirst()
                .map(AttrDTO::getValue).orElse(Lists.newArrayList()).stream().findFirst().orElse(null);
        if (StringUtils.isNotBlank(availableTimePeriod3)) {
            disableDays = disableDays.stream().filter(day -> day < 1 || day > 7).collect(Collectors.toList());
        }
        return disableDays;
    }

    public boolean hitWeekAndHolidayUnavailable(List<Integer> disableDays,
            Map<String, UnavailabelDate> unavailabelDateMap) {
        if (CollectionUtils.isEmpty(disableDays)) {
            return false;
        }

        // 先判断是否命中了一周不可用设置
        if (disableDays.contains(dayOfWeek())) {
            return true;
        }

        // 再判断是否命中了节假日不可用
        if (MapUtils.isEmpty(unavailabelDateMap)) {
            return false;
        }

        return disableDays.stream().filter(Objects::nonNull).map(item ->
        // 将设置的一周不可用时间过滤掉
        unavailabelDateMap.getOrDefault(String.valueOf(item), null))
                .filter(date -> date != null && StringUtils.isNotBlank(date.getFrom())
                        && StringUtils.isNotBlank(date.getTo()))
                .anyMatch(date -> DateHelper.isCurrentDateInRange(date.getFrom(), date.getTo()));
    }

    public int dayOfWeek() {
        LocalDate localDate = LocalDate.now();
        DayOfWeek dayOfWeek = localDate.getDayOfWeek();
        return dayOfWeek.getValue();
    }

    public Guarantee processDisabledTips() {
        return buildGuarantee("该团购今日无法在门店使用，具体情况请联系门店确认", "", 1, "#222222");
    }

    public Guarantee buildGuarantee(String text, String icon, int type, String style){
        Guarantee.GuaranteeBuilder builder = Guarantee.builder()
                .text(text)
                .icon(icon)
                .type(type)
                .style(style);
        return builder.build();
    }

    public int getType(String type){
        return isNumeric(type) ? Integer.parseInt(type) : -1;
    }

    public String getDisplayText(Map<String, String> reminderInfoMap, boolean isSpringFestivalTag, boolean springFestivalDisabled){
        if (isSpringFestivalTag && springFestivalDisabled){
            return reminderInfoMap.get("disableText");
        }
        return reminderInfoMap.get("text");
    }

    /**
     * 判断是否为数字类型
     * @param str
     * @return
     */
    public static boolean isNumeric(String str) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.isNumeric(java.lang.String)");
        return str != null && str.matches("-?\\d+(\\.\\d+)?");
    }
}
