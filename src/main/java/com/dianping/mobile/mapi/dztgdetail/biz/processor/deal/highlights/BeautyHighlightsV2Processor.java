package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.BeautyTechGroupService;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CommonAttrVO;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.JsonUtils;
import com.dianping.technician.dto.shopsearch.TechCard;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.OptionalServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class BeautyHighlightsV2Processor extends AbstractHighlightsProcessor {
    /**
     * 美发-烫染-导购属性
     */
    private static final List<String> HOT_DYEING_ATTR_ITEMS = Lists.newArrayList("药水品牌", "发型师", "包含服务");

    private static final String HAIRSTYLIST_LEVEL = "hairstylist_level";
    private static final String YEARS_OF_EMPLOYMENT_OF_MAKE_UP_ARTIST = "years_of_employment_of_makeup_artist";
    private static final String MAKE_UP_SERVICE_TYPE = "makeup_service_type";
    private static final String MAKE_UP_SERVICE_TIME = "makeup_service_time";
    private static final String HAIRSTYLE_SERVICE = "hairstyle_service";

    private static final String DOT = ".";

    /**
     * 纹秀相关导购属性
     */
    private static final String PROJECT_CLASSFICATION = "ProjectClassification";

    private static final String SUIT_CROWDS = "suitCrowds";

    private static final String RANLIAOCHANDI = "ranliaochandi";

    private static final String GRANT = "grant";

    private static final String BUSE_TIME = "buseshijian";

    private static final String BUSE_CISHU = "mianfeibuse";

    @Autowired
    private BeautyTechGroupService beautyTechGroupService;

    @Override
    protected String getHighlightsIdentify(DealCtx ctx) {
        Map<String, String> highlightsIdentify = GreyUtils.getHighlightsIdentify();
        return getHighlightsConfig(ctx, highlightsIdentify);
    }

    @Override
    protected String getHighlightsStyle(DealCtx ctx) {
        Map<String, String> highlightStyle = GreyUtils.getHighlightsStyleType();
        return getHighlightsConfig(ctx, highlightStyle);
    }

    private String getHighlightsConfig(DealCtx ctx, Map<String, String> config) {
        DealGroupCategoryDTO categoryDTO = ctx.getDealGroupDTO().getCategory();
        if (Objects.isNull(categoryDTO)) {
            return StringUtils.EMPTY;
        }
        String categoryIdKey = String.valueOf(categoryDTO.getCategoryId());
        // 拼接成如「501.美甲」字符串
        String serviceTypeKey = Joiner.on(DOT).join(categoryIdKey, categoryDTO.getServiceType());
        String value = config.get(serviceTypeKey);
        return StringUtils.isEmpty(value) ? config.get(categoryIdKey) : value;
    }

    @Override
    protected String getHighlightsContent(DealCtx ctx) {
        DealGroupDTO dealGroup = ctx.getDealGroupDTO();
        DealGroupCategoryDTO category = dealGroup.getCategory();
        if (category == null) {
            return null;
        }
        Long categoryId = category.getCategoryId();
        String serviceType = category.getServiceType();

        try {
            // 美发
            if (categoryId == 501) {
                if ("护理".equals(serviceType)) {
                    return getNursingContent(dealGroup);
                }
            }
            // 美甲美睫
            if (categoryId == 502) {
                if ("美睫".equals(serviceType)) {
                    return getEyelashesContent(dealGroup);
                }
            }
            // 美容美体SPA、痘肌护理、瘦身纤体/产后塑型
            if (categoryId == 503 || categoryId == 509 || categoryId == 511 || categoryId == 514) {
                return getOtherContent(dealGroup);
            }
        } catch (Exception e) {
            logger.error("getHighlightsContent-error,dpDealId:{},mtDealId:{}", dealGroup.getDpDealGroupId(), dealGroup.getMtDealGroupId(), e);
        }
        return null;
    }

    protected List<CommonAttrVO> getHighlightsAttrs(DealCtx ctx) {
        try {
            DealGroupCategoryDTO category = ctx.getDealGroupDTO().getCategory();
            if (category == null) {
                return null;
            }

            Long categoryId = category.getCategoryId();
            String serviceType = category.getServiceType();
            // 美发
            if (categoryId == 501) {
                if ("烫染".equals(serviceType)) {
                    return getHotDyeingAttrs(ctx);
                }
            }
            // 化妆
            if (categoryId == 502 && ("化妆".equals(serviceType))) {
                return getMakeupAttrs(ctx);
            }
            // 纹身
            if (categoryId == 512) {
                return getTattooShoppingGuideAttrs(ctx);
            }
            // 新穿戴甲则展示亮点模块
            if (DealUtils.isNewWearableNailDeal(ctx)) {
                return getWearableNailAttrs(ctx);
            }
        } catch (Exception e) {
            logger.error("getHighlightsAttrs-error,dpDealId:{},mtDealId:{}", ctx.getDpId(), ctx.getMtId(), e);
        }
        return null;
    }

    public List<CommonAttrVO> getWearableNailAttrs(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.BeautyHighlightsV2Processor.getWearableNailAttrs(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        List<CommonAttrVO> attrs = Lists.newArrayList();
        DealGroupDTO dealGroup = ctx.getDealGroupDTO();
        // 是否到店免费佩戴
        String isFreeWearingAtStore = getFirstAttrValFromAttr(dealGroup, "isFreeWearingAtStore");
        if (StringUtils.isNotBlank(isFreeWearingAtStore) && Objects.equals(isFreeWearingAtStore, "true")) {
            attrs.add(new CommonAttrVO("增值服务", "到店免费佩戴"));
        }
        // 穿戴甲类型
        String wearingNailsType = getFirstAttrValFromAttr(dealGroup, "wearingNailsType");
        if (StringUtils.isNotBlank(wearingNailsType)) {
            attrs.add(new CommonAttrVO("穿戴甲类型", wearingNailsType));
        }
        // 附赠项目
        String nailAdditionalItemJson = getFirstAttrValFromAttr(dealGroup, "nail_additional_item");
        if (StringUtils.isNotBlank(nailAdditionalItemJson)) {
            List<String> nailAdditionalItems;
            if (nailAdditionalItemJson.startsWith("[") && nailAdditionalItemJson.endsWith("]")) {
                nailAdditionalItems = JSON.parseArray(nailAdditionalItemJson, String.class);
            } else {
                nailAdditionalItems = Lists.newArrayList(nailAdditionalItemJson);
            }
            if (CollectionUtils.isNotEmpty(nailAdditionalItems)) {
                attrs.add(new CommonAttrVO("附赠项目", nailAdditionalItems.get(0)));
            }
        }
        // 有两个及以上亮点才显示
        return attrs.size() > 1 ? attrs : null;
    }

    private String getNailContent1(DealGroupDTO dealGroup) {
        String selectableRange = getAttrValFromServiceProject(dealGroup, 4043, "colorSelectableRange");
        String selectableRangeF = null;
        if (StringUtils.isNotEmpty(selectableRange)) {
            if ("全店颜色任选".equals(selectableRange)) {
                selectableRangeF = "全店颜色任选";
            }
            if ("至少150色可选".equals(selectableRange)) {
                selectableRangeF = "150+色可选";
            }
            if ("至少100色可选".equals(selectableRange)) {
                selectableRangeF = "100+色可选";
            }
            if ("至少50色可选".equals(selectableRange)) {
                selectableRangeF = "50+色可选";
            }
        }
        if (StringUtils.isEmpty(selectableRangeF)) {
            return null;
        }

        String content = getAttrValFromServiceProject(dealGroup, 4043, "content");
        String toningIndex = getAttrValFromServiceProject(dealGroup, 4043, "toningIndex");
        String toningIndexF = null;
        if ("含跳色".equals(content)) {
            toningIndexF = String.format("含%s指跳色", toningIndex);
        }
        String category2 = getAttrValFromServiceProject(dealGroup, 4039, "category2");
        String category2F = null;
        if ("本次到店卸甲/卸睫二选一".equals(category2) || "本次到店卸甲".equals(category2)) {
            category2F = "含卸甲";
        }
        return Lists.newArrayList(selectableRangeF, toningIndexF, category2F).stream()
                .filter(Objects::nonNull)
                .collect(Collectors.joining("·"));
    }

    private String getNursingContent(DealGroupDTO dealGroup) {
        List<String> productOwner = getAllAttrValFromServiceProject(dealGroup, 3114, "productOwner");
        List<String> calcProductOwner = calcProductOwner(productOwner);
        if (CollectionUtils.isEmpty(calcProductOwner)) {
            return null;
        }
        String value;
        if (calcProductOwner.size() == 1) {
            value = calcProductOwner.stream().findFirst().get();
        } else {
            String prefix = calcProductOwner.stream().limit(2).collect(Collectors.joining("、"));
            value = prefix + "等";
        }
        return String.format("%s品质药水护理", value);
    }

    private String getNailContent2(DealGroupDTO dealGroup) {
        String value1 = null;
        if (getDealTags(dealGroup).contains(100076019L)) {
            value1 = "简约款任选";
        }
        if (getDealTags(dealGroup).contains(100076020L)) {
            value1 = "轻奢款任选";
        }
        if (getDealTags(dealGroup).contains(100076021L)) {
            value1 = "复杂款任选";
        }
        if (getDealTags(dealGroup).contains(100076022L)) {
            value1 = "款式随便做，饰品随便贴";
        }
        if (StringUtils.isEmpty(value1)) {
            return null;
        }

        List<ServiceProjectDTO> serviceProjectSku1 = getServiceProjectSku(dealGroup, 4042);
        List<ServiceProjectDTO> serviceProjectSku2 = getServiceProjectSku(dealGroup, 4041);
        String value2 = null;
        String value3 = null;
        if (CollectionUtils.isNotEmpty(serviceProjectSku1) && CollectionUtils.isEmpty(serviceProjectSku2)) {
            value2 = "含延长";
        }
        if (CollectionUtils.isEmpty(serviceProjectSku1) && CollectionUtils.isNotEmpty(serviceProjectSku2)) {
            value3 = "含甲片";
        }
        String category2 = getAttrValFromServiceProject(dealGroup, 4039, "category2");
        String category2F = null;
        if ("本次到店卸甲/卸睫二选一".equals(category2) || "本次到店卸甲".equals(category2)) {
            category2F = "含卸甲";
        }

        return Lists.newArrayList(value1, value2, value3, category2F).stream()
                .filter(Objects::nonNull)
                .collect(Collectors.joining("·"));
    }

    private String getEyelashesContent(DealGroupDTO dealGroup) {
        if (!getDealTags(dealGroup).contains(100005310L)) {
            return null;
        }
        String value1 = getFirstAttrValFromAttr(dealGroup, "eyelash_suit_part");
        if (StringUtils.isEmpty(value1)) {
            return null;
        }
        String value2 = null;
        if (CollectionUtils.isNotEmpty(getServiceProjectSku(dealGroup, 2104149))) {
            value2 = "睫毛嫁接";
        } else if (CollectionUtils.isNotEmpty(getServiceProjectSku(dealGroup, 2104150))) {
            value2 = "烫睫毛";
        }
        String value3 = null;
        String eyelashAfterSale = getFirstAttrValFromAttr(dealGroup, "eyelash_after_sale");
        if (StringUtils.isNotEmpty(eyelashAfterSale) && !"不含修补".equals(eyelashAfterSale)) {
            value3 = eyelashAfterSale;
        }
        return Lists.newArrayList(value1, value2, value3).stream()
                .filter(Objects::nonNull)
                .collect(Collectors.joining("·"));
    }


    private String getOtherContent(DealGroupDTO dealGroup) {
        List<String> attrValFromAttr = getAttrValFromAttr(dealGroup, "selling_point");
        if (CollectionUtils.isEmpty(attrValFromAttr)) {
            return null;
        }
        return attrValFromAttr.get(0);
    }

    public List<CommonAttrVO> getMakeupAttrs(DealCtx ctx) {
        DealGroupDTO dealGroup = ctx.getDealGroupDTO();
        List<CommonAttrVO> attrs = new ArrayList<>();

        // 属性1 化妆师经验
        String yearsOfArtist = getFirstAttrValFromAttr(dealGroup, YEARS_OF_EMPLOYMENT_OF_MAKE_UP_ARTIST);
        if (StringUtils.isNotEmpty(yearsOfArtist)) {
            attrs.add(new CommonAttrVO("化妆师经验", yearsOfArtist));
        }
        // 属性2 服务方式
        String makeUpServiceType = getFirstAttrValFromAttr(dealGroup, MAKE_UP_SERVICE_TYPE);
        if (StringUtils.isNotEmpty(makeUpServiceType)) {
            attrs.add(new CommonAttrVO("服务方式", makeUpServiceType));
        }
        // 属性3 服务时长
        String makeUpServiceTime = getFirstAttrValFromAttr(dealGroup, MAKE_UP_SERVICE_TIME);
        if (StringUtils.isNotEmpty(makeUpServiceType)) {
            attrs.add(new CommonAttrVO("服务时长", makeUpServiceTime));
        }
        // 属性4发型/造型服务
        String hairStyleService = getFirstAttrValFromAttr(dealGroup, HAIRSTYLE_SERVICE);
        if (StringUtils.isNotEmpty(makeUpServiceType)) {
            attrs.add(new CommonAttrVO("发型/造型服务", hairStyleService));
        }
        // 产品需求：至少要3个导购属性才能展示，否则影响用户体验
        if (attrs.size() >= 3){
            return attrs;
        }
        return Lists.newArrayList();
    }

    private List<CommonAttrVO> getHotDyeingAttrs(DealCtx ctx) {
        DealGroupDTO dealGroup = ctx.getDealGroupDTO();
        List<CommonAttrVO> attrs = new ArrayList<>();

        // 属性1
        List<Integer> skuIds = Lists.newArrayList(3112, 3113, 3117, 2104572);
        List<String> productOwner = getAllAttrValFromServiceProject(dealGroup, skuIds, "productOwner");
        List<String> calcProductOwner = calcProductOwner(productOwner);
        if (CollectionUtils.isNotEmpty(calcProductOwner)) {
            String value;
            if (calcProductOwner.size() == 1) {
                value = calcProductOwner.stream().findFirst().get();
            } else {
                String prefix = calcProductOwner.stream().limit(2).collect(Collectors.joining("、"));
                value = prefix + "等";
            }
            attrs.add(new CommonAttrVO("药水品牌", value));
        }
        // 属性2 手艺人
        String hariLevel = getFirstAttrValFromAttr(dealGroup, HAIRSTYLIST_LEVEL);
        if (StringUtils.isNotEmpty(hariLevel)) {
            // 查询手艺人
            List<TechCard> techCards = beautyTechGroupService.getGroupTechs(ctx);
            int techQty = techCards.size();
            if (techQty > 0) {
                attrs.add(new CommonAttrVO("适用发型师", hariLevel, "BeautyTechnician"));
            }
        }
        // 属性3
        String projectName1 = getServiceProjectSku(dealGroup, 3111).stream().findFirst().map(ServiceProjectDTO::getName).orElse(null);
        String projectName2 = getServiceProjectSku(dealGroup, 3114).stream().findFirst().map(ServiceProjectDTO::getName).orElse(null);
        String nameFormat = null;
        if (StringUtils.isNotEmpty(projectName1) && StringUtils.isNotEmpty(projectName2)) {
            nameFormat = String.format("含%s、%s", projectName1, projectName2);
        } else if (StringUtils.isEmpty(projectName1) && StringUtils.isNotEmpty(projectName2)) {
            nameFormat = String.format("含%s", projectName2);
        } else if (StringUtils.isNotEmpty(projectName1) && StringUtils.isEmpty(projectName2)) {
            nameFormat = String.format("含%s", projectName1);
        }
        if (StringUtils.isNotEmpty(nameFormat)) {
            attrs.add(new CommonAttrVO("包含服务", nameFormat));
        }
        if (attrs.size() >= 2) {
            return attrs;
        }
        return new ArrayList<>();
    }

    private static List<String> calcProductOwner(List<String> productOwners) {
        if (CollectionUtils.isEmpty(productOwners)) {
            return Lists.newArrayList();
        }
        List<String> configProductOwners = getConfigProductOwner();
        return productOwners.stream()
                .filter(StringUtils::isNotEmpty)
                .map(k -> {
                    for (String configProductOwner : configProductOwners) {
                        if (k.contains(configProductOwner)) {
                            return configProductOwner;
                        }
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());
    }

    private static List<String> getConfigProductOwner() {
        String string = Lion.getString(Environment.getAppName(), "com.sankuai.dzu.tpbase.dztgdetailweb.highlight.beauty.product.owner");
        if (StringUtils.isEmpty(string)) {
            return Lists.newArrayList();
        }
        List<String> strings = JSON.parseArray(string, String.class);
        return strings == null ? Lists.newArrayList() : strings;
    }

    private List<Long> getDealTags(DealGroupDTO dealGroup) {
        if (dealGroup == null || CollectionUtils.isEmpty(dealGroup.getTags())) {
            return new ArrayList<>();
        }
        return dealGroup.getTags().stream().map(DealGroupTagDTO::getId).distinct().collect(Collectors.toList());
    }

    private String getFirstAttrValFromAttr(DealGroupDTO dealGroup, String attrName) {
        List<String> attrValFromAttr = getAttrValFromAttr(dealGroup, attrName);
        return CollectionUtils.isEmpty(attrValFromAttr) ? null : attrValFromAttr.get(0);
    }

    private List<String> getAttrValFromAttr(DealGroupDTO dealGroup, String attrName) {
        List<AttrDTO> attrs = dealGroup.getAttrs();
        if (CollectionUtils.isEmpty(attrs)) {
            return Lists.newArrayList();
        }
        Optional<AttrDTO> first = attrs.stream().filter(k -> Objects.equals(k.getName(), attrName)).findFirst();
        if (first.isPresent()) {
            return first.get().getValue();
        }
        return Lists.newArrayList();
    }

    private List<String> getAllAttrValFromServiceProject(DealGroupDTO dealGroupDTO, List<Integer> skuIds, String attrName) {
        return skuIds.stream()
                .flatMap(k -> getAllAttrValFromServiceProject(dealGroupDTO, k, attrName).stream())
                .filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());
    }

    private List<String> getAllAttrValFromServiceProject(DealGroupDTO dealGroupDTO, int skuId, String attrName) {
        List<ServiceProjectDTO> serviceProjectList = getServiceProject(dealGroupDTO);
        if (CollectionUtils.isEmpty(serviceProjectList)) {
            return Lists.newArrayList();
        }
        return serviceProjectList.stream().filter(k -> Objects.equals(k.getCategoryId(), (long) skuId))
                .map(dto -> {
                    List<ServiceProjectAttrDTO> list = ObjectUtils.defaultIfNull(dto.getAttrs(), new ArrayList<ServiceProjectAttrDTO>());
                    Optional<ServiceProjectAttrDTO> attrVal = list.stream()
                            .filter(k -> Objects.equals(k.getAttrName(), attrName)).findFirst();
                    return attrVal.map(ServiceProjectAttrDTO::getAttrValue).orElse(null);
                }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<ServiceProjectDTO> getServiceProjectSku(DealGroupDTO dealGroupDTO, int skuId) {
        List<ServiceProjectDTO> serviceProjectList = getServiceProject(dealGroupDTO);
        if (CollectionUtils.isEmpty(serviceProjectList)) {
            return Lists.newArrayList();
        }
        return serviceProjectList.stream()
                .filter(k -> Objects.equals(k.getCategoryId(), (long) skuId))
                .collect(Collectors.toList());
    }

    private String getAttrValFromServiceProject(DealGroupDTO dealGroupDTO, int skuId, String attrName) {
        List<ServiceProjectDTO> serviceProjectList = getServiceProject(dealGroupDTO);
        Optional<ServiceProjectDTO> first = serviceProjectList.stream()
                .filter(k -> Objects.equals(k.getCategoryId(), (long) skuId)).findFirst();
        if (first.isPresent()) {
            List<ServiceProjectAttrDTO> list = ObjectUtils.defaultIfNull(first.get().getAttrs(), new ArrayList<ServiceProjectAttrDTO>());
            Optional<ServiceProjectAttrDTO> attrVal = list.stream()
                    .filter(k -> Objects.equals(k.getAttrName(), attrName)).findFirst();
            if (attrVal.isPresent()) {
                return attrVal.get().getAttrValue();
            }
        }
        return null;
    }

    private List<ServiceProjectDTO> getServiceProject(DealGroupDTO groupDTO) {
        DealGroupServiceProjectDTO serviceProject = groupDTO.getServiceProject();
        if (serviceProject == null) {
            return Lists.newArrayList();
        }
        List<ServiceProjectDTO> groups = Lists.newArrayList();
        List<MustServiceProjectGroupDTO> mustGroups = serviceProject.getMustGroups();
        if (CollectionUtils.isNotEmpty(mustGroups)) {
            groups.addAll(mustGroups.stream()
                    .filter(Objects::nonNull)
                    .filter(k -> k.getGroups() != null)
                    .flatMap(k -> k.getGroups().stream()).collect(Collectors.toList()));
        }
        List<OptionalServiceProjectGroupDTO> optionGroups = serviceProject.getOptionGroups();
        if (CollectionUtils.isNotEmpty(optionGroups)) {
            groups.addAll(optionGroups.stream()
                    .filter(Objects::nonNull)
                    .filter(k -> k.getGroups() != null)
                    .flatMap(k -> k.getGroups().stream()).collect(Collectors.toList()));
        }
        return groups;
    }

    public List<CommonAttrVO> getTattooShoppingGuideAttrs(DealCtx ctx) {
        //1.初始化
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        List<CommonAttrVO> attrsRes = new ArrayList<>();

        //2.从attr中取出剩余字段
        Map<String, ServiceProjectAttrDTO> attrs = getAttrFromMustGroup(dealGroupDTO);

        if (Objects.isNull(dealGroupDTO.getCategory()) || Objects.isNull(dealGroupDTO.getCategory().getServiceTypeId())) {
            logger.warn("团单类目信息不完整, 团单Id={}, category={}", dealGroupDTO.getMtDealGroupId(),
                    JsonUtils.toJson(dealGroupDTO.getCategory()));
            return attrsRes;
        }
        //3.逻辑判断与组装
        // 团单分类
        //取ServiceTypeId判断团单分类
        if (dealGroupDTO.getCategory().getServiceTypeId() == 137013) {
            CommonAttrVO projectClassification = getAttrVOFromMustGroupAttr(attrs, PROJECT_CLASSFICATION);
            if (projectClassification != null) {
                projectClassification.setName("团单分类");
                attrsRes.add(projectClassification);
            }
        }else{
            attrsRes.add(new CommonAttrVO("团单分类",dealGroupDTO.getCategory().getServiceType()));
        }

        // 适合人群
        CommonAttrVO suitCrowds = getAttrVOFromMustGroupAttr(attrs, SUIT_CROWDS);
        if (suitCrowds != null && StringUtils.isNotBlank(suitCrowds.getValue())) {
            if(suitCrowds.getValue().contains("不挑人群")){
                suitCrowds.setValue("不挑人群");
                attrsRes.add(suitCrowds);
            }else{
                suitCrowds.setValue(getRandomElem(suitCrowds.getValue(), 2));
                attrsRes.add(suitCrowds);
            }
        }

        // 免费补色
        CommonAttrVO grant = getAttrVOFromMustGroupAttr(attrs, GRANT);
        if(grant!=null && grant.getValue().equals("赠送")){
            CommonAttrVO buseTime = getAttrVOFromMustGroupAttr(attrs, BUSE_TIME);
            CommonAttrVO buseCishu = getAttrVOFromMustGroupAttr(attrs, BUSE_CISHU);
            if(buseTime!=null && buseCishu!=null){
                attrsRes.add(new CommonAttrVO("免费补色",buseTime.getValue()+"个月内"+buseCishu.getValue()+"次"));
            }
        }

        // 染料产地
        CommonAttrVO ranliaoFrom = getAttrVOFromMustGroupAttr(attrs, RANLIAOCHANDI);
        if (ranliaoFrom != null) {
            attrsRes.add(ranliaoFrom);
        }

        return attrsRes;
    }

    private String getRandomElem(String values, int limit){
        return Arrays.stream(values.split("、")).limit(limit).collect(Collectors.joining("、"));
    }
    private CommonAttrVO getAttrVOFromMustGroupAttr(Map<String, ServiceProjectAttrDTO> attrs, String attrName) {
        if (attrs.containsKey(attrName)) {
            ServiceProjectAttrDTO projectClassification = attrs.get(attrName);
            return new CommonAttrVO(projectClassification.getChnName(), projectClassification.getAttrValue());
        }else{
            Cat.logEvent("BeautyHighlightsV2Processor", attrName+"not found");
            return null;
        }
    }

    private Map<String, ServiceProjectAttrDTO> getAttrFromMustGroup(DealGroupDTO dealGroupDTO) {
        DealGroupServiceProjectDTO serviceProject = dealGroupDTO.getServiceProject();
        if (serviceProject == null) {
            return new HashMap<>();
        }
        List<MustServiceProjectGroupDTO> mustGroups = serviceProject.getMustGroups();
        if (CollectionUtils.isEmpty(mustGroups)) {
            return new HashMap<>();
        }
        Map<String, ServiceProjectAttrDTO> res = new HashMap<>();
        mustGroups.stream()
                .map(MustServiceProjectGroupDTO::getGroups)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .forEach(group -> convertItems(group.getAttrs(), res));
        return res;
    }

    private void convertItems(List<ServiceProjectAttrDTO> attrs, Map<String, ServiceProjectAttrDTO> res) {
        if (CollectionUtils.isEmpty(attrs)) {
            return;
        }
        // 这里默认attrName不会重复（该业务就是如此）
        attrs.forEach(attr -> res.put(attr.getAttrName(), attr));
    }
}
