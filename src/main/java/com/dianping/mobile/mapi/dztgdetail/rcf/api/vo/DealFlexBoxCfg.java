package com.dianping.mobile.mapi.dztgdetail.rcf.api.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: guang<PERSON>jie
 * @Date: 2024/11/16 17:51
 */
@Data
@MobileDo(id = 0x668e)
public class DealFlexBoxCfg implements Serializable {

    /**
     * 是否展示快照页，灰度开关
     */
    @MobileDo.MobileField(key = 0x89dd)
    private boolean render;

    /**
     * Flexbox的url
     */
    @MobileDo.MobileField(key = 0xf5fb)
    private String mtFlexboxTemplateUrl;

}