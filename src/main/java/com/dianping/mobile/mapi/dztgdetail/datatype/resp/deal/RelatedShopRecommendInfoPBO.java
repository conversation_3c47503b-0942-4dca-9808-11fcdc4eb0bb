package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: <EMAIL>
 * @Date: 2023/7/14
 */
@Data
@TypeDoc(description = "到综团单推荐商户推荐理由模型")
@MobileDo(id = 0x8ac5)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RelatedShopRecommendInfoPBO implements Serializable {
    @FieldDoc(description = "推荐来源，1-ugc推荐语，2-榜单")
    @MobileDo.MobileField(key = 0x9cc5)
    private int recommendSource;

    @FieldDoc(description = "推荐语或榜单标题")
    @MobileDo.MobileField(key = 0xb7e6)
    private String recommendText;

    @FieldDoc(description = "榜单icon")
    @MobileDo.MobileField(key = 0xb8bf)
    private String recommendIcon;

    @FieldDoc(description = "榜单跳链")
    @MobileDo.MobileField(key = 0x774e)
    private String jumpUrl;
}
