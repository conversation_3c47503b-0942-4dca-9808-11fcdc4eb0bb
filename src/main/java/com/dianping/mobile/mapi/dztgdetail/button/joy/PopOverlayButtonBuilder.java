package com.dianping.mobile.mapi.dztgdetail.button.joy;

import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import org.apache.commons.lang.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;

import java.util.List;

import static com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum.NORMAL_DEAL;

/**
 * 提单浮层跳链构造
 *
 * <AUTHOR>
 * @since 2024/6/14 19:46
 */
public class PopOverlayButtonBuilder extends AbstractButtonBuilder {

    public static boolean getPopOverlaySwitch() {
        return Lion.getBoolean("com.sankuai.dzu.tpbase.dztgdetailweb", LionConstants.POP_OVERLAY_SWITCH, false);
    }

    /**
     * 获取提单页为浮层样式的团单类目ID列表
     *
     * @return
     */
    public static List<Integer> getCategoryIdList() {
        return Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb", LionConstants.POP_OVERLAY_CATEGORIES, Integer.class, Lists.newArrayList(303, 304));
    }

    @Override
    public void build(DealCtx ctx, ButtonBuilderChain chain) {
        // 校验
        int clientType = ctx.getEnvCtx().getClientType();
        boolean isApp = ClientTypeEnum.isMtMainApp(clientType) || ClientTypeEnum.isDpMainApp(clientType);

        if (getPopOverlaySwitch() && getCategoryIdList().contains(ctx.getCategoryId()) && isApp) {
            // 设置为弹窗模式
            appendUrl(ctx, "&expid=floatingLayer");
            // 补充source
            fillSource(ctx);
        }
        chain.build(ctx);
    }

    @Override
    protected void doBuild(DealCtx ctx, ButtonBuilderChain chain) {
    }

    private void fillSource(DealCtx ctx) {
        String requestSource = ctx.getRequestSource();
        if (StringUtils.isEmpty(requestSource)) {
            return;
        }
        appendUrl(ctx, "&source=" + requestSource);
    }

    /**
     * 填充参数(只填充最后一个团购类型的按钮)
     *
     * @param ctx
     * @param appendUrlStr
     */
    private void appendUrl(DealCtx ctx, String appendUrlStr) {
        if (StringUtils.isEmpty(appendUrlStr)) {
            return;
        }
        DealBuyBtn buyBtn = findLastNormalDealBtn(ctx);
        if (buyBtn != null && StringUtils.isNotBlank(buyBtn.getRedirectUrl()) && !buyBtn.getRedirectUrl().contains(appendUrlStr)) {
            buyBtn.setRedirectUrl(buyBtn.getRedirectUrl() + appendUrlStr);
        }
    }

    private DealBuyBtn findLastNormalDealBtn(DealCtx ctx) {
        return ctx.getBuyBar().getBuyBtns().stream().filter(dealBuyBtn -> dealBuyBtn.getDetailBuyType() == NORMAL_DEAL.getCode()).reduce((first, second) -> second) // 使用reduce获取最后一个满足条件的元素
                .orElse(null); // 如果没有找到，返回null
    }
}