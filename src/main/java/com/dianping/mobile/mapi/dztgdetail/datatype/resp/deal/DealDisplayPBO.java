package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;

@Deprecated
@TypeDoc(description = "到综团单展示内容数据模型")
@MobileDo(id = 0x49f2)
public class DealDisplayPBO implements Serializable {

    @FieldDoc(description = "资源地址：图片地址或者视频截图的地址")
    @MobileField(key = 0xc56e)
    private String url;

    @FieldDoc(description = "类型：0：文本；1：图片；2：视频")
    @MobileField(key = 0x8f0c)
    private int type;

    @FieldDoc(description = "视频链接")
    @MobileField(key = 0xe654)
    private String videoUrl;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }
}
