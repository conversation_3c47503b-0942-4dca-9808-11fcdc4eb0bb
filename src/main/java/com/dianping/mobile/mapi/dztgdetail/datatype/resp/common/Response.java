package com.dianping.mobile.mapi.dztgdetail.datatype.resp.common;

import java.io.Serializable;

import com.dianping.cat.Cat;
import lombok.Data;

@Data
public class Response<T> implements Serializable {

    private boolean success;

    private String msg;

    private T result;

    private int code;

    public void setErrResponse(String msg) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.Response.setErrResponse(java.lang.String)");
        this.success = false;
        this.msg = msg;
    }

    public void setSuccessResponse(String msg) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.Response.setSuccessResponse(java.lang.String)");
        this.success = true;
        this.msg = msg;
    }

    public static enum RespCode {
        SUCCESS(200), RHINO_REJECT(301), INVALID(400), ERROR(500), ERROR_TIP(505);
        private int val;

        RespCode(int val) {
            this.val = val;
        }

        public int getVal() {
            return this.val;
        }
    }

    public void fillParam(int code,boolean success,String msg,T result){
        this.code = code;
        this.success = success;
        this.result = result;
        this.msg = msg;
    }

    public static <T> Response<T> createSuccessResponse(T result){
        Response<T> res = new Response<T>();
        res.fillParam(RespCode.SUCCESS.getVal(),true,"success", result);
        return res;
    }

    public static <T> Response<T> createRhinoRejectResponse(String msg, T result){
        Response<T> res = new Response<T>();
        res.fillParam(RespCode.RHINO_REJECT.getVal(),false,"success", result);
        return res;
    }

    public static <T> Response<T> createFailedResponse(String msg, T result){
        Response<T> res = new Response<T>();
        res.fillParam(RespCode.ERROR.getVal(),false, msg, result);
        return res;
    }

    public static <T> Response<T> createInvalidResponse(String msg, T result){
        Response<T> res = new Response<T>();
        res.fillParam(RespCode.INVALID.getVal(),false, msg, result);
        return res;
    }

    public static <T> Response<T> createErrorTipResponse(String msg, T result){
        Response<T> res = new Response<T>();
        res.fillParam(RespCode.ERROR_TIP.getVal(),false, msg, result);
        return res;
    }

    public boolean valid(){
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.Response.valid()");
        return this.code == RespCode.SUCCESS.getVal() && this.success;
    }

}