package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealImageTextDetailReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ImageTextDetailPBO;
import com.dianping.mobile.mapi.dztgdetail.facade.DealDetailFacade;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.meituan.servicecatalog.api.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

@InterfaceDoc(displayName = "到综团单图文详情展示接口",
        type = "restful",
        description = "查询到综团单图文详情的展示信息：包括“产品介绍”、“套餐详情介绍”等等。",
        scenarios = "该接口仅适用于双平台APP站点的团购详情页，其他以到综团购为纬度的页面如需使用请咨询项目owner",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "qian.wang.sh"
)
@Controller("general/platform/dztgdetail/dealimagetextdetail.bin")
@Action(url = "dealimagetextdetail.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class DealImageTextDetailAction extends AbsAction<DealImageTextDetailReq> {

    @Autowired
    private DealDetailFacade dealDetailFacade;

    @Override
    protected IMobileResponse validate(DealImageTextDetailReq request, IMobileContext iMobileContext) {
        IdUpgradeUtils.processProductIdForDealImageTextDetailReq(request, "dealimagetextdetail.bin");
        if (request == null || request.getDealgroupid() <= 0) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "dealimagetextdetail.bin",
            displayName = "到综团单图文详情展示接口",
            description = "查询到综团单图文详情的展示信息：包括“产品介绍”、“套餐详情介绍”等等。",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "dealimagetextdetail.bin请求参数",
                            type = DealBaseReq.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "result", description = "团购图文详情picasso数据", type = ImageTextDetailPBO.class)},
            restExampleUrl = "https://mapi.51ping.com/general/platform/dztgdetail/dealimagetextdetail.bin?dealgroupid=200153707",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    ),
            }
    )
    @Override
    protected IMobileResponse execute(DealImageTextDetailReq request, IMobileContext iMobileContext) {
        EnvCtx envCtx = initEnvCtx(iMobileContext);
        ImageTextDetailPBO result = dealDetailFacade.queryImageText(request, envCtx);
        return result != null ? new CommonMobileResponse(result) : Resps.SYSTEM_ERROR;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
