package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2023/7/5
 */
@Data
@TypeDoc(description = "到综团单推荐商户数据模型")
@MobileDo(id = 0x1a3)
public class RelatedShopPBO implements Serializable {
    @FieldDoc(description = "商户ID")
    @MobileDo.MobileField(key = 0x349b)
    private long shopId;

    @FieldDoc(description = "商户名称")
    @MobileDo.MobileField(key = 0xb5c9)
    private String shopName;

    @FieldDoc(description = "商户评分")
    @MobileDo.MobileField(key = 0xa669)
    private Integer shopPower;

    @FieldDoc(description = "用户商户距离描述文案")
    @MobileDo.MobileField(key = 0x9bc7)
    private String regionDistanceDesc;

//    @FieldDoc(description = "用户商户距离")
//    @MobileDo.MobileField(key = 0x9ac4)
//    private String distance;

    @FieldDoc(description = "商户详情页地址")
    @MobileDo.MobileField(key = 0x7dac)
    private String shopUrl;

//    @FieldDoc(description = "商户纬度")
////    @MobileDo.MobileField(key = 0xa19e)
//    private double lat;
//
//    @FieldDoc(description = "商户经度")
////    @MobileDo.MobileField(key = 0xa324)
//    private double lng;

    @FieldDoc(description = "门店图片")
    @MobileDo.MobileField(key = 0x8980)
    private String shopPic;

    @FieldDoc(description = "人均消费")
    @MobileDo.MobileField(key = 0x4b46)
    private String avgPrice;

    @FieldDoc(description = "评分")
    @MobileDo.MobileField(key = 0x76d8)
    private String fiveScore;

    @FieldDoc(description = "商圈")
    @MobileDo.MobileField(key = 0xe758)
    private String mainRegionName;

    @FieldDoc(description = "推荐理由")
    @MobileDo.MobileField(key = 0x9060)
    private RelatedShopRecommendInfoPBO recommendInfo;

    @FieldDoc(description = "下挂商品名称")
    @MobileDo.MobileField(key = 0x408a)
    private String appendDealName;

    @FieldDoc(description = "下挂商品价格")
    @MobileDo.MobileField(key = 0x9af1)
    private String appendDealPrice;

    @FieldDoc(description = "文案模型")
    @MobileDo.MobileField(key = 0xb84d)
    private List<ItemTextInfo> itemTextInfos = new ArrayList<>();

    @FieldDoc(description = "距离描述")
    @MobileDo.MobileField(key = 0x3936)
    private String distanceDesc;

    @FieldDoc(description = "商圈描述")
    @MobileDo.MobileField(key = 0x76f6)
    private String regionDesc;
}
