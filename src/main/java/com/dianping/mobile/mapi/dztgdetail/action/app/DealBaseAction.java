package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.DealBaseDo;
import com.dianping.mobile.mapi.dztgdetail.facade.DealBaseFacade;
import com.dianping.mobile.mapi.dztgdetail.util.AntiCrawlerUtils;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.HttpMethod;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@InterfaceDoc(displayName = "美团到综团购详情APP查询接口",
        type = "restful",
        description = "美团到综APP团详页访问或者非单中心团单访问团购详情信息(待废弃)",
        scenarios = "适用于美团APP历史版本团详页访问或者非单中心团单访问，处于长期下线中，请新版本或其他情况都不要使用，如必须调用请联系当前owner。",
        host = "http://mapi.meituan.com/general/platform/mtdetail/",
        authors = "qian.wang.hs"
)
@Controller("dealbase.bin")
@Action(url = "general/platform/mtdetail/dealbase.bin", isCheckToken = false)
public class DealBaseAction extends AbsAction<DealBaseRequest> {

    private static final String METRIC_DEALBASE_RETURN_SUCCESS = "DealBaseReturnSuccess";

    @Resource
    private DealBaseFacade dealBaseFacade;

    @Override
    protected IMobileResponse validate(DealBaseRequest request, IMobileContext iMobileContext) {
        IdUpgradeUtils.processProductIdForDealBaseRequest(request, "dealbase.bin");
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.action.app.DealBaseAction.validate(com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseRequest,com.dianping.mobile.framework.datatypes.IMobileContext)");
        return (request.getDealId() <= 0 || request.getCityId() <= 0) ? Resps.PARAM_ERROR : null;
    }

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "dealbase.bin",
            displayName = "美团到综团购详情APP查询接口",
            description = "美团到综APP团详页访问或者非单中心团单访问团购详情信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "dealbase.bin请求参数",
                            type = DealBaseRequest.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "iMobileResponse", description = "通用返回数据模型")},
            restExampleUrl = "http://mapi.meituan.com/general/platform/mtdetail/dealbase.bin?dealid=40700733",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    )
            }
    )
    @Override
    @CryptMethod
    protected IMobileResponse execute(DealBaseRequest request, IMobileContext iMobileContext) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.action.app.DealBaseAction.execute(com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseRequest,com.dianping.mobile.framework.datatypes.IMobileContext)");
        DealBaseDo dealBaseDo = dealBaseFacade.getDealBase(request, iMobileContext);

        if (dealBaseDo != null) {
            Cat.logMetricForCount(METRIC_DEALBASE_RETURN_SUCCESS);
            // 反爬信息处理
            hideKeyInfo(dealBaseDo, iMobileContext);
            return new CommonMobileResponse(dealBaseDo);
        }
        return Resps.SERVER_ERROR;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.action.app.DealBaseAction.getRule()");
        return null;
    }

    private void hideKeyInfo(DealBaseDo result, IMobileContext ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.action.app.DealBaseAction.hideKeyInfo(com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.DealBaseDo,com.dianping.mobile.framework.datatypes.IMobileContext)");
        if ( Objects.isNull(result)) {
            return;
        }
        if (!AntiCrawlerUtils.hide(ctx)) {
            return;
        }
        // 隐藏 couponTitle
        result.setCouponTitle(StringUtils.EMPTY);
        // 隐藏 campaignPrice
        result.setCampaignPrice(0d);
        // 隐藏 originPrice
        result.setOriginalPrice(0d);
        // 隐藏 price
        result.setPrice(0d);
        // 隐藏 CanBuyPrice
        result.setCanBuyPrice(0d);
        // 隐藏
        if (CollectionUtils.isNotEmpty(result.getPromotionInfos())) {
            result.getPromotionInfos().forEach(promo -> {
                promo.setLongTitle(StringUtils.EMPTY);
                promo.setTag(StringUtils.EMPTY);
            });
        }
        if (CollectionUtils.isNotEmpty(result.getMenus())) {
            result.getMenus().forEach(menu -> {
                CollectionUtils.emptyIfNull(menu.getMenuDetails()).forEach(detail -> {
                    detail.setTotalPrice(StringUtils.EMPTY);
                });
            });
        }
        // 隐藏 address
        // 隐藏电话信息
        if (Objects.nonNull(result.getShop())) {
            result.getShop().setPhone(AntiCrawlerUtils.hidePhoneInfo(result.getShop().getPhone()));
            result.getShop().setAddr("登录后查看具体地址");
        }
    }
}
