package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.book.req.ShopBookDto;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.lion.client.Lion;
import com.dianping.lion.facade.LionFacade;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.LionUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiClientWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.ShopUuidUtils;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Cons;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Constants;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.constants.QueryParams;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RuleStrategyEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ComBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DisplayPositionEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ShopPBO;
import com.dianping.mobile.mapi.dztgdetail.entity.ApplyShopPositionConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.ApplyShopPositionStrategy;
import com.dianping.mobile.mapi.dztgdetail.entity.PoiInfoCustomizedConfig;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import com.dianping.mobile.mapi.dztgdetail.helper.BusinessHourParser;
import com.dianping.mobile.mapi.dztgdetail.helper.SwitchHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper;
import com.dianping.mobile.mapi.dztgdetail.util.DealShopUtils;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.ReassuredRepairUtil;
import com.dianping.poi.bizhour.BizHourForecastService;
import com.dianping.poi.bizhour.dto.BizForecastDTO;
import com.dianping.poi.bizhour.enums.SourceEnum;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meituan.mobile.sinai.base.common.PoiFields;
import com.meituan.service.mobile.sinai.client.model.PoiModelL;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.HospitalInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.IS_SHOW_SHOP_CATEGORIES;

/**
 * @Author: <EMAIL>
 * @Date: 2024/6/8
 */
@Component
@Slf4j
public class ShopBuilderService {
    @Resource
    private DouHuService douHuService;
    @Resource
    private BizHourForecastService bizHourForecastService;
    @Autowired
    PoiClientWrapper poiClientWrapper;
    @Autowired
    MapperWrapper mapperWrapper;

    public static final int BUY_BAR_ICON_TYPE_ONLINE = 1;

    public ShopPBO getShopPBO(DealCtx ctx) {
        if (ctx == null || ctx.getBestShopResp() == null) {
            return null;
        }
        BestShopDTO bestShop = ctx.getBestShopResp();
        long poiid4p = ctx.getLongPoiId4PFromResp();
        ShopPBO shop = new ShopPBO();

        if (ctx.getEnvCtx().isMt()) {
            shop.setShopId(poiid4p);
        } else {
            shop.setShopUuid(ctx.getShopUuidFromResp());
            if (ShopUuidUtils.retainShopId(poiid4p)) {
                shop.setShopId(poiid4p);
            }
        }

        ModuleAbConfig moduleAbConfig = douHuService.enableCardStyleV2(ctx.getEnvCtx(), ctx.getCategoryId(), ctx.getMrnVersion());
        // 命中实验时，构建 人均消费价格、营业状态、营业时间
        if (douHuService.hitEnableCardStyleV2(moduleAbConfig)){
            buildAvgPriceAndBusinessState(ctx, shop);
        }


        shop.setShopUrl(UrlHelper.getShopUrl(ctx));
        shop.setShopName(bestShop.getShopName());
        shop.setBranchName(bestShop.getBranchName());
        shop.setPhoneNos(getPhoneNos(bestShop, ctx));
        shop.setAddress(bestShop.getAddress());
        shop.setHideAddrEnable(hideAddrEnable(ctx));
        shop.setShopPower(getShopScore(ctx));
        shop.setImUrl(ctx.getImUrl());
        shop.setShowType(bestShop.getShowType());
        shop.setShopType(bestShop.getShopType());
        shop.setLyyShop(isLyyShop(ctx.getEnvCtx().isMt(), bestShop.getMtShopId()));
        shop.setRelatedShops(ctx.getRelatedShops());
        shop.setShopPic(bestShop.getShopPic());

        boolean gray = SwitchHelper.enableDrivingShop(ctx.isMt(), ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId(), ctx.getCityId4P());
        shop.setShopDesc(gray && isDrivingSchoolCar(ctx) ? "报名点" : null);

        if (ctx.isMt()) {
            if (DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP.equals(ctx.getEnvCtx().getDztgClientTypeEnum())) {
                shop.setLat(bestShop.getGlat());
                shop.setLng(bestShop.getGlng());
            } else {
                shop.setLat(bestShop.getLat());
                shop.setLng(bestShop.getLng());
            }
        } else {
            shop.setLat(bestShop.getGlat());
            shop.setLng(bestShop.getGlng());
        }

        shop.setShopNum(bestShop.getTotalShopsNum());
        int categoryId = 0;
        if(ctx.getChannelDTO()!= null){
            categoryId = ctx.getChannelDTO().getCategoryId();
        }
        List<String> isShowShopCategories = Lion.getList(IS_SHOW_SHOP_CATEGORIES, String.class, Lists.newArrayList());
        // 私域直播渠道 门店条展示样式：[门店名称]·[距离]
        if ((ctx.isMtLiveMinApp() || isShowShopCategories.contains(String.valueOf(categoryId))) && bestShop.getTotalShopsNum() == 1 ) {
            String shopName = bestShop.getShopName() != null ? bestShop.getShopName() : "";
            String distance = bestShop.getDistance() != null ? bestShop.getDistance() : "";
            String shopDesc = Joiner.on("·").join(shopName, distance);
            shop.setShopListDesc(shopDesc);
        }
        if (bestShop.getTotalShopsNum() > 1) {
            String shopListUrl = DealShopUtils.getShopListUrl(ctx, ctx.isMt() ? bestShop.getMtShopId() : bestShop.getDpShopId());
            shop.setShopListUrl(shopListUrl);
            shop.setShopListDesc(String.format("%s家门店适用", bestShop.getTotalShopsNum()));
        }
        //局改不展示门店列表
        if(ReassuredRepairUtil.isTagPresent(ctx) || ctx.getEnvCtx().getDztgClientTypeEnum() == DztgClientTypeEnum.BEAM_APP){
            shop.setShopListUrl(StringUtils.EMPTY);
            shop.setShopListDesc(StringUtils.EMPTY);
            shop.setLyyShop(Boolean.TRUE);
        }
        List<Integer> categoryList = Lion.getList(LionConstants.POI_DISTANCE_CATEGORY_BLACKLIST, Integer.TYPE, Lists.newArrayList());

        if (StringUtils.isNotBlank(bestShop.getDistance())) {
            if (categoryList.contains(categoryId) && poiClientWrapper.isDoor2DoorPoi(ctx)) {
                shop.setDistanceDesc("上门服务");
            } else {
                shop.setDistanceDesc(String.format("距您%s", bestShop.getDistance()));
                shop.setDistance(bestShop.getDistance());
            }
        }

        shop.setRecallBtns(buildRecallBtns(ctx));
        shop.setMapUrl(UrlHelper.getMapUrl(bestShop, ctx));

        setDisplayPosition(ctx, shop);
        shop.setHideStars(hideStarsEnable(ctx));
        // 穿戴甲门店类型
        if (Objects.nonNull(ctx.getShopCategoryEnum())) {
            shop.setShopCategoryId(ctx.getShopCategoryEnum().getCode());
        }
        // 如果是预览团单，IM和电话需要隐藏
        if (DealUtils.isPreviewDeal(ctx)) {
            shop.setImUrl(StringUtils.EMPTY);
            shop.setPhoneNos(Collections.emptyList());
        }
        postProcessShop(ctx, shop);
        return shop;
    }

    private Integer getShopScore(DealCtx ctx) {
        if (ctx.isMt() && Objects.nonNull(ctx.getMtPoiDTO())) {
            return ctx.getMtPoiDTO().getMtAvgScore();
        }
        if (Objects.nonNull(ctx.getDpPoiDTO()) && Objects.nonNull(ctx.getDpPoiDTO().getFiveScore())) {
            return (int) (ctx.getDpPoiDTO().getFiveScore() * 10);
        }
        return ctx.getBestShopResp().getShopPower();
    }

    /**
     * 团详框架改版，构建 人均消费价格、营业状态、营业时间
     * @param ctx
     * @param shop
     */
    private void buildAvgPriceAndBusinessState(DealCtx ctx, ShopPBO shop){
        // 获取门店基本信息 id是点评的id
        DpPoiDTO dpPoiDTO = ctx.getDpPoiDTO();
        //poiClientWrapper.getDpPoiDTO(ctx.getDpLongShopId(), com.google.common.collect.Lists.newArrayList("shopId", "backMainCategoryPath", "cityId", "power", "businessHours",
        //"avgPrice", "shopPower", "fiveScore", "mainRegionName", "lat", "lng", "shopName", "defaultPic"));
        // 获取门店基本信息 id是点评的id
        if (null != dpPoiDTO){
            Integer price = dpPoiDTO.getAvgPrice();
            // 设置人均消费价格 来自商详页
            shop.setAvgPrice(buildAvgPrice(price));
            String currentTime = LocalDateTime.now().format(Constants.POIEXT_DATETIME_FORMATTER);
            BizForecastDTO bizForecastDTO = bizHourForecastService.getBizForecast(ctx.getDpLongShopId(), SourceEnum.DIANPING, currentTime);
            BusinessHourParser.BusinessHour businessHour = BusinessHourParser.parseBusinessHour(dpPoiDTO.getPower(), dpPoiDTO.getBusinessHours(), bizForecastDTO == null ? null : bizForecastDTO.getToday());
            // 设置门店 营业状态 来自商详页
            shop.setBusinessState(businessHour.businessStatus);
            if ("营业中".equals(shop.getBusinessState()) && LionConfigUtils.isEduOnlineDeal(ctx.getChannelDTO().getCategoryId(), getServiceTypeId(ctx.getDealGroupDTO()))) {
                shop.setBusinessState("服务时间");
            }
            // 设置门店营业时间
            shop.setBusinessHour(businessHour.businessHour);
        }
    }


    /**
     * 构建平均价格
     * @param avgPrice
     * @return
     */
    private String buildAvgPrice(Integer avgPrice){
        String priceText = StringUtils.EMPTY;
        if (avgPrice != null && avgPrice > 0 ){
            priceText = "¥" + avgPrice + "/人";
        }
        return priceText;
    }

    private Long getServiceTypeId(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO == null || dealGroupDTO.getCategory() == null) {
            return null;
        }
        return dealGroupDTO.getCategory().getServiceTypeId();
    }

    private List<String> getPhoneNos(BestShopDTO bestShop, DealCtx ctx) {
        // LE保洁自营/无忧通 隐藏电话模块
        if ((LionUtils.needReplaceImToCsForCleaning() && ctx.isCleaningSelfOwnDeal())
                || (LionUtils.needReplaceImToCsForCarefree() && ctx.isCareFreeDeal())){
            return Lists.newArrayList();
        }
        List<String> phoneNos;
        if (ctx.getEnvCtx().isMainApp() && ctx.getPoiPhones() != null && !ctx.getPoiPhones().isEmpty()) {
            phoneNos = ctx.getPoiPhones();
        } else {
            phoneNos = bestShop.getPhoneNos();
        }
        return filterPhoneNos(ctx, phoneNos);
    }

    private List<String> filterPhoneNos(DealCtx ctx, List<String> phoneNos) {
        List<Integer> filterPhoneNoPoiCategories = Lion.getList(LionConstants.FILTER_PHONE_NO_POI_CATEGORIES, Integer.class, Collections.emptyList());

        if (CollectionUtils.isEmpty(phoneNos)
                || CollectionUtils.isEmpty(ctx.getPoiBackCategoryIds())
                || Sets.intersection(new HashSet<>(filterPhoneNoPoiCategories), ctx.getPoiBackCategoryIds()).isEmpty()) {
            return phoneNos;
        }
        return phoneNos.stream()
                .filter(number -> StringUtils.isNotBlank(number) && !number.startsWith("400") && !number.startsWith("800"))
                .collect(Collectors.toList());
    }

    private boolean hideAddrEnable(DealCtx ctx) {
        DealGroupChannelDTO channel = ctx.getChannelDTO();
        long poiId4P = ctx.getLongPoiId4PFromResp();

        if (channel == null || channel.getChannelDTO() == null || poiId4P <= 0) {
            return false;
        }

        String channelStr = "ch" + channel.getChannelDTO().getChannelId();
        String categoryStr = "ca" + channel.getCategoryId();

        List<String> configs = Lion.getList(LionConstants.HIDE_POI_ADDRESS_CFG, String.class, new ArrayList<>());

        if (CollectionUtils.isEmpty(configs) || (!configs.contains(channelStr) && !configs.contains(categoryStr))) {
            return false;
        }

        long mtShopId;

        if (ctx.isMt()) {
            mtShopId = poiId4P;
        } else {
            mtShopId = mapperWrapper.getMtShopIdByDpShopIdLong(poiId4P);
        }

        List<PoiModelL> poiModels = poiClientWrapper.batchPoiInfoL(Collections.singletonList(mtShopId), Collections.singletonList(PoiFields.POI_SENSITIVE_LEVEL));

        if (CollectionUtils.isEmpty(poiModels)) {
            return false;
        }

        PoiModelL poiModel = poiModels.get(0);
        Map<String, Long> poiSensitiveLevel = poiModel.getPoiSensitiveLevel();

        return MapUtils.isNotEmpty(poiSensitiveLevel) && poiSensitiveLevel.get("HIDE_ADDR") != null && poiSensitiveLevel.get("HIDE_ADDR") == 1;

    }

    private boolean isLyyShop(boolean isMt, long mtShopId) {
        if (!isMt) {
            return false;
        }

        List<Long> lyyShopIds = Lion.getList(LionConstants.LYY_SHOP_IDS, Long.class, new ArrayList<>());

        return CollectionUtils.isNotEmpty(lyyShopIds) && lyyShopIds.contains(mtShopId);
    }


    /**
     * 是否是驾校小车
     * @param ctx
     * @return
     */
    public boolean isDrivingSchoolCar(DealCtx ctx) {
        List<AttributeDTO> attrs = ctx.getAttrs();
        List<String> serviceTypeAttr = AttributeUtils.getAttributeValues(DealAttrKeys.SERVICE_TYPE, attrs);

        return ctx.getCategoryId() == 404 && org.apache.commons.collections.CollectionUtils.isNotEmpty(serviceTypeAttr) && serviceTypeAttr.contains("小车");
    }

    private List<ComBtn> buildRecallBtns(DealCtx ctx) {
        ShopBookDto shopBookDto = ctx.getShopBookDto();
        if (shopBookDto == null || !shopBookDto.isHasBook()) {
            return null;
        }
        ComBtn btn = new ComBtn();
        btn.setTitle(shopBookDto.getBookActionName());
        btn.setClickUrl(shopBookDto.getBookUrl());
        btn.setActionType(ComBtn.ActionEnum.REDIRECT.type);
        return Lists.newArrayList(btn);
    }

    private void setDisplayPosition(DealCtx ctx, ShopPBO shop) {
        // 特团接入拼团需求：所有特价团购来源都需要将适用门店置于导航栏和团购详情之间
        if (RequestSourceEnum.COST_EFFECTIVE.getSource().equals(ctx.getRequestSource())){
            shop.setDisplayPosition(DisplayPositionEnum.BEFORE_DETAIL.getCode());
            return;
        }
        if (ctx.getModuleConfigsModule() != null && Cons.FOLD_STYLE.equals(ctx.getModuleConfigsModule().getGeneralInfo())) {
            // 门店模块和折叠样式联动
            shop.setDisplayPosition(DisplayPositionEnum.BEFORE_DETAIL.getCode());
            return;
        }
        // 根据配置的策略进行适用门店位置展示
        List<ApplyShopPositionConfig> applyShopPositionRules = LionConfigUtils.getApplyShopPositionConfig();
        DztgClientTypeEnum dztgClientTypeEnum = ctx.getEnvCtx().getDztgClientTypeEnum();
        setApplyShopPosition(applyShopPositionRules, shop, ctx.getChannelDTO(),
                ctx.getRequestSource(), dztgClientTypeEnum.name());

        if (!RequestSourceEnum.HOME_PAGE.getSource().equals(ctx.getRequestSource())
                && !RequestSourceEnum.CAI_XI.getSource().equals(ctx.getRequestSource())
                && !LionConfigUtils.isCostEffectiveByWhiteCategory(ctx)) {
            return;
        }
        Set<Integer> shopAtTopConfig = LionFacade
                .getSet(LionConstants.SHOP_AT_TOP_CATEGORY_IDS, Integer.TYPE, Collections.emptySet());
        if (ctx.getChannelDTO() != null && shopAtTopConfig.contains(ctx.getChannelDTO().getCategoryId()) ) {
            shop.setDisplayPosition(DisplayPositionEnum.BEFORE_DETAIL.getCode());
        }
    }

    private boolean hideStarsEnable(DealCtx ctx) {
        DpPoiDTO dpPoiDTO = ctx.getDpPoiDTO();
        if (dpPoiDTO != null && dpPoiDTO.getHospitalInfo() != null) {
            HospitalInfo hospitalInfo = dpPoiDTO.getHospitalInfo();
            if (Objects.equals(hospitalInfo.getNature(), 0)) {
                return true;
            }
        }
        return false;
    }

    private void postProcessShop(DealCtx ctx, ShopPBO shop) {
        PoiInfoCustomizedConfig poiInfoCustomizedConfig = getHidePoiAddressConfig(ctx);
        if (poiInfoCustomizedConfig == null) {
            return;
        }
        if (poiInfoCustomizedConfig.isHidePoiAddress()) {
            shop.setHideAddrEnable(true);
        }
        if (poiInfoCustomizedConfig.getFixPoiAddress() != null) {
            shop.setAddress(poiInfoCustomizedConfig.getFixPoiAddress());
        }
        if (ctx.getModuleConfigsModule() != null && Cons.FOLD_STYLE.equals(ctx.getModuleConfigsModule().getGeneralInfo())) {
            // 门店模块和折叠样式联动
            shop.setShopListTitle("适用门店");
        } else if (poiInfoCustomizedConfig.getFixPoiTitle() != null) {
            shop.setShopListTitle(poiInfoCustomizedConfig.getFixPoiTitle());
        }
        if (poiInfoCustomizedConfig.isHidePoiDistance()) {
            shop.setDistance(null);
            shop.setDistanceDesc(null);
        }
        if (poiInfoCustomizedConfig.isHideMapUrl()) {
            shop.setMapUrl(null);
        }
        if (poiInfoCustomizedConfig.getPoiBizType() != null) {
            shop.setShopBizType(poiInfoCustomizedConfig.getPoiBizType());
        }
        if (isOnlineShop(ctx.getDpPoiDTO())) {
            postProcessOnlineShop(ctx, shop);
        }
    }

    public void setApplyShopPosition(List<ApplyShopPositionConfig> applyShopPositionRules, ShopPBO shop,
                                     DealGroupChannelDTO channel, String pageSource, String dztgClientType) {
        if (CollectionUtils.isEmpty(applyShopPositionRules) || Objects.isNull(shop)
                || Objects.isNull(channel) || Objects.isNull(channel.getChannelDTO())) {
            return;
        }
        for (ApplyShopPositionConfig rule : applyShopPositionRules) {
            ApplyShopPositionStrategy strategy = rule.getApplyShopPositionStrategy();
            int channelId = channel.getChannelDTO().getChannelId();
            int categoryId = channel.getCategoryId();
            boolean factor1 = matchStrategy(rule.getChannelIds(), channelId, strategy.getChannelIdStrategy());
            boolean factor2 = matchStrategy(rule.getCategoryIds(), categoryId, strategy.getCategoryIdStrategy());
            boolean factor3 = matchStrategy(rule.getPageSource(), pageSource, strategy.getPageSourceStrategy());
            boolean factor4 = matchStrategy(rule.getDztgClientType(), dztgClientType, strategy.getDztgClientTypeStrategy());
            if (factor1 && factor2 && factor3 && factor4) {
                shop.setDisplayPosition(rule.getDisplayPosition());
                break;
            }
        }
    }

    private PoiInfoCustomizedConfig getHidePoiAddressConfig(DealCtx ctx) {
        DealGroupChannelDTO channelDTO = ctx.getChannelDTO();
        DpPoiDTO dpPoiDTO = ctx.getDpPoiDTO();
        List<PoiInfoCustomizedConfig> hidePoiConfigs = Lion.getList(LionConstants.POI_ADDRESS_CUSTOMIZED_CONFIG, PoiInfoCustomizedConfig.class);
        if (CollectionUtils.isEmpty(hidePoiConfigs)) {
            return null;
        }
        for (PoiInfoCustomizedConfig hidePoiAddrConfig : hidePoiConfigs) {
            boolean isPlatformMatch = hidePoiAddrConfig.getPlatforms() == null || (hidePoiAddrConfig.getPlatforms().contains(ctx.isMt() ? "MT" : "DP"));
            boolean isClientTypeMatch = hidePoiAddrConfig.getClientTypes() == null || hidePoiAddrConfig.getClientTypes().contains(ctx.getEnvCtx().getClientType());
            boolean isDealCategoryMatch = hidePoiAddrConfig.getDealCategories() == null || (channelDTO != null && hidePoiAddrConfig.getDealCategories().contains(channelDTO.getCategoryId()));
            boolean isPoiCategoryMatch;
            if (hidePoiAddrConfig.getPoiUseTypes() != null) {
                dpPoiDTO = dpPoiDTO != null ? dpPoiDTO : poiClientWrapper.getDpPoiDTO(ctx.getDpLongShopId(), QueryParams.SINAI_DP_POI_FIELDS);
                isPoiCategoryMatch = dpPoiDTO != null && hidePoiAddrConfig.getPoiUseTypes().contains(dpPoiDTO.getUseType());
            } else {
                isPoiCategoryMatch = true;
            }

            if (isClientTypeMatch && isPlatformMatch && isDealCategoryMatch && isPoiCategoryMatch) {
                return hidePoiAddrConfig;
            }
        }
        return null;
    }

    private boolean isOnlineShop(DpPoiDTO shop) {
        return shop.getUseType() != null && shop.getUseType() == 38 && "1119".equals(shop.getAppSides());
    }

    private static void postProcessOnlineShop(DealCtx ctx, ShopPBO shop) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.postProcessOnlineShop(DealCtx,ShopPBO)");
        shop.setDistance(null);
        shop.setDistanceDesc("线上机构");
        shop.setBuyBarIconType(BUY_BAR_ICON_TYPE_ONLINE);
        // 非猜喜路径不显示商户模块
        if (fromCAIXI(ctx.getRequestSource())) {
            shop.setDisplayPosition(DisplayPositionEnum.BEFORE_DETAIL.getCode());
        } else {
            shop.setShopName("");
        }
    }

    private boolean matchStrategy(List<?> objs, Object obj, String strategyName) {
        if (objs == null) {
            return false;
        }
        RuleStrategyEnum strategyEnum = RuleStrategyEnum.of(strategyName);
        switch (strategyEnum) {
            case IGNORE:
                return true;
            case ALLOW:
                return objs.contains(obj);
            case DENY:
                return !objs.contains(obj);
            default:
                return false;
        }
    }

    /**
     * 判断是否来自猜喜
     * @param requestSource
     * @return
     */
    private static Boolean fromCAIXI(String requestSource){
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.fromCAIXI(java.lang.String)");
        if (RequestSourceEnum.CAI_XI.getSource().equals(requestSource) || RequestSourceEnum.HOME_PAGE.getSource().equals(requestSource)){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
