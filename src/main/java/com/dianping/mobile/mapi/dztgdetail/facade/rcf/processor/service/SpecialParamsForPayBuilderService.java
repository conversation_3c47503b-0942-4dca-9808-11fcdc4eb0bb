package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PromoDetailModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.SpecialParamsForPayVO;
import com.sankuai.general.product.query.center.client.dto.DealGroupChannelDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2024/12/4 15:18
 */
@Slf4j
@Component
public class SpecialParamsForPayBuilderService {

    public SpecialParamsForPayVO build(final DealCtx ctx, final DealGroupPBO result) {
        try {
            final SpecialParamsForPayVO vo = new SpecialParamsForPayVO();
            Optional.ofNullable(result.getPromoDetailModule())
                    .map(PromoDetailModule::getPromoPrice)
                    .filter(NumberUtils::isCreatable)
                    .map(BigDecimal::new)
                    .ifPresent(price -> vo.setPayFeeCent(price.multiply(BigDecimal.valueOf(100L)).intValue()));
            vo.setFirstCategoryId(String.valueOf(Optional.ofNullable(ctx.getDealGroupDTO()).map(DealGroupDTO::getChannel).map(DealGroupChannelDTO::getChannelId).orElse(0)));
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date beginSaleDate = simpleDateFormat.parse(ctx.getDealGroupDTO().getBasic().getBeginSaleDate());
            Date endSaleDate = simpleDateFormat.parse(ctx.getDealGroupDTO().getBasic().getEndSaleDate());
            vo.setEndTimestamp(String.valueOf(endSaleDate.getTime()));
            vo.setStartTimestamp(String.valueOf(beginSaleDate.getTime()));
            return vo;
        } catch (Throwable throwable) {
            log.error("SpecialParamsForPayBuilderService.build", throwable);
            return null;
        }
    }

}
