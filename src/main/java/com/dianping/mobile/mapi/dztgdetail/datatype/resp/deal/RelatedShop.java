package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.EncryptedField;
import com.sankuai.sig.botdefender.adapter.annotation.EncryptedLinkField;
import lombok.Data;

import java.io.Serializable;

@Data
@TypeDoc(description = "关联门店，驾校类目中为练车门店模块")
@MobileDo(id = 0x2061)
public class RelatedShop implements Serializable {

    @FieldDoc(description = "商户ID")
    @MobileField(key = 0x349b)
    @EncryptedField(targetFieldName = "shopIdEncrypt")
    private long shopId;
    @MobileField(key = 0x8143)
    private String shopIdEncrypt;

    @FieldDoc(description = "商户图片")
    @MobileField(key = 0xa3d9)
    private String shopImg;

    @FieldDoc(description = "商户名称")
    @MobileField(key = 0xb5c9)
    private String shopName;

    @FieldDoc(description = "商户地址")
    @MobileField(key = 0x2063)
    private String address;

    @FieldDoc(description = "地图地址")
    @MobileField(key = 0xe5f8)
//    @EncryptedLinkField(queries = {"poi_id"})
    private String mapUrl;

    @FieldDoc(description = "用户商户距离描述")
    @MobileField(key = 0x9ac4)
    private String distance;

    @FieldDoc(description = "门店与练车门店之间的距离")
    @MobileField(key = 0xa581)
    private double shopDistance;

    @FieldDoc(description = "商户纬度")
    @MobileField(key = 0xa19e)
    private double lat;

    @FieldDoc(description = "商户经度")
    @MobileField(key = 0xa324)
    private double lng;

    @FieldDoc(description = "门店不可用时的文案，该字段不为空表示门店为无效数据，前端不展示相关模块）")
    @MobileField(key = 0x5c22)
    private String shopInvalidText;

}