package com.dianping.mobile.mapi.dztgdetail.button.joy;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.helper.CardHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Set;

public class ButtonLocationPricePkSorter extends AbstractButtonBuilder {

    private static final Set<Integer> SORT_BY_PRICE_BTN_TYPES = Sets.newHashSet(BuyBtnTypeEnum.MEMBER_CARD.getCode(),
            BuyBtnTypeEnum.NORMAL_DEAL.getCode(), BuyBtnTypeEnum.TIMES_CARD.getCode(), BuyBtnTypeEnum.PINTUAN.getCode(),
            BuyBtnTypeEnum.IDLE_BANNER.getCode());

    //价格PK，比较两个button中价格较小
    private boolean comparePriceLow(DealBuyBtn o1, DealBuyBtn o2) {
        BigDecimal p1 = new BigDecimal(o1.getPriceStr());
        BigDecimal p2 = new BigDecimal(o2.getPriceStr());
        return p1.compareTo(p2) < 0;
    }

    //价格PK，比较两个button中价格较小，返回价格较小的button。价格一样，返回后者
    private DealBuyBtn getPriceLowButton(DealBuyBtn o1, DealBuyBtn o2) {
        BigDecimal p1 = new BigDecimal(o1.getPriceStr());
        BigDecimal p2 = new BigDecimal(o2.getPriceStr());
        return p1.compareTo(p2) < 0 ? o1 : o2;
    }

    //价格PK，比较两个button中价格相等的情况
    private boolean priceEqual(DealBuyBtn o1, DealBuyBtn o2) {
        BigDecimal p1 = new BigDecimal(o1.getPriceStr());
        BigDecimal p2 = new BigDecimal(o2.getPriceStr());
        return p1.compareTo(p2) == 0;
    }

    //价格PK，比较两个button中价格较高
    private boolean comparePriceHigh(DealBuyBtn o1, DealBuyBtn o2) {
        BigDecimal p1 = new BigDecimal(o1.getPriceStr());
        BigDecimal p2 = new BigDecimal(o2.getPriceStr());
        return p1.compareTo(p2) > 0;
    }

    @Override
    public void build(DealCtx context, ButtonBuilderChain chain) {
        if (context.getPriceContext().isZuLiaoButtonNewStyle()) {
            sortByPricePerfer(context);
        }
        chain.build(context);
    }

    private void sortByPricePerfer(DealCtx context) {
        int buttonCount = context.getButtonCount();
        if (buttonCount <= 1) {
            return;
        }

        List<DealBuyBtn> buyBtns = context.getBuyBar().getBuyBtns();
        for (DealBuyBtn buyBtn : buyBtns) {
            if (!SORT_BY_PRICE_BTN_TYPES.contains(buyBtn.getDetailBuyType())) {
                return;
            }
        }

        /**
         * 最后button数为2个时，此时可能的顺序为
         * 1、会员卡、团购。
         * 2、次卡、团购
         * 3、拼团、团购
         * 4、闲时优惠、团购
         * 此时右边统一为团购
         */
        if (buttonCount == 2) {
            DealBuyBtn dealBuyBtn = buyBtns.get(0);
            DealBuyBtn dealBuyBtn1 = buyBtns.get(1);

            //1、会员卡、团购。
            // 根据用户是否会员进行PK
            if ((dealBuyBtn.getDetailBuyType() == BuyBtnTypeEnum.MEMBER_CARD.getCode()
                    && dealBuyBtn1.getDetailBuyType() == BuyBtnTypeEnum.NORMAL_DEAL.getCode())) {
                if (isUserHoldMemberCard(context)) {
                    /**
                     * 当用户是会员身份，并且团购到手价>=会员到手价。则移除团购button。此时只有单会员button
                     * 否则，应该只有团购button
                     */
                    if (comparePriceLow(dealBuyBtn, dealBuyBtn1) || (priceEqual(dealBuyBtn, dealBuyBtn1))) {
                        buyBtns.remove(1);
                    } else {
                        buyBtns.remove(0);
                    }
                } else {
                    /**当用户是非会员身份
                     * 团购到手价<会员到手价
                     * 【单】团购到手价、共省、门市价
                     */
                    if (comparePriceHigh(dealBuyBtn, dealBuyBtn1)) {
                        buyBtns.remove(0);
                    }

                    /**
                     * 团购到手价>=会员到手价
                     * 【左】会员价/会员日价、共省、门市价
                     * 【右】团购价到手价、共省、门市价
                     * 此种情况无需处理
                     */
                }
            }
        }

        /**
         * 最后button数为3个时，此时可能的顺序为
         * 1、会员卡、次卡、团购
         * 此时需要PKButton
         */
        if (buttonCount == 3) {
            DealBuyBtn dealBuyBtn = buyBtns.get(0);
            DealBuyBtn dealBuyBtn1 = buyBtns.get(1);
            DealBuyBtn dealBuyBtn2 = buyBtns.get(2);
            if (memberAndTimesAndNormalButton(dealBuyBtn, dealBuyBtn1, dealBuyBtn2)) {

                //会员和次卡的最低价button
                DealBuyBtn priceLowButton = getPriceLowButton(dealBuyBtn, dealBuyBtn1);

                //次卡和团购的最低价button
                DealBuyBtn priceLowButton1 = getPriceLowButton(dealBuyBtn1, dealBuyBtn2);

                //会员和团购的最低价button
                DealBuyBtn priceLowButton2 = getPriceLowButton(dealBuyBtn, dealBuyBtn2);

                if (isUserHoldMemberCard(context)) {

                    if (comparePriceLow(dealBuyBtn2, priceLowButton)) {
                        /** 当用户是会员身份
                         *  团购到手价< 会员到手价&次卡单词价的最低价
                         * 【单】团购到手价、共省、门市价
                         */
                        buyBtns.retainAll(Lists.newArrayList(dealBuyBtn2));
                    } else if (comparePriceLow(dealBuyBtn, priceLowButton1)
                            || priceEqual(dealBuyBtn, priceLowButton1)) {
                        /** 当用户是会员身份
                         *  团购到手价&次卡单词价>= 会员到手价
                         * 【单】会员价/会员日价、共省、门市价
                         */
                        buyBtns.retainAll(Lists.newArrayList(dealBuyBtn));
                    } else if (comparePriceLow(dealBuyBtn1, dealBuyBtn)
                            && (comparePriceLow(dealBuyBtn, dealBuyBtn2) || priceEqual(dealBuyBtn, dealBuyBtn2))) {
                        /**
                         *  当用户是会员身份
                         * 团购到手价 >= 会员到手价 > 次卡单次价
                         * 【左】次卡单次价、单次省、门市价
                         * 【右】会员到手价、共省、门市价
                         */
                        buyBtns.remove(2);
                        Collections.reverse(buyBtns);
                    } else if ((comparePriceLow(dealBuyBtn1, dealBuyBtn2) || priceEqual(dealBuyBtn1, dealBuyBtn2))
                            && comparePriceLow(dealBuyBtn2, dealBuyBtn)) {
                        /**
                         * 当用户是会员身份
                         * 会员到手价>团购到手价>=次卡单词价
                         * 【左】次卡单次价、单次省、门市价
                         * 【右】团购到手价、共省、门市价
                         */
                        buyBtns.remove(0);
                    }
                } else {
                    if (comparePriceLow(dealBuyBtn2, priceLowButton)) {
                        /**
                         * 当用户不是会员身份
                         * 团购到手价 < 会员到手价 & 次卡单词价的最低价
                         * 【单】团购到手价、共省、门市价
                         */
                        buyBtns.retainAll(Lists.newArrayList(dealBuyBtn2));
                    } else if (!comparePriceLow(dealBuyBtn2, priceLowButton)) {
                        /**
                         * 当用户不是会员身份
                         * 团购到手价 >= 会员到手价&次卡单词价的最低价
                         *
                         * 【左】会员到手价&次卡单词价的最低价、门市价、相应省标签
                         * 【右】团购到手价、共省、门市价
                         */
                        if (comparePriceLow(dealBuyBtn, dealBuyBtn1) || priceEqual(dealBuyBtn, dealBuyBtn1)) {
                            buyBtns.remove(1);
                        } else {
                            buyBtns.remove(0);
                        }
                    }
                }
            }
        }

        if (doubleButtonHasMemberThenFixButtonTitle(buyBtns)) {
            for (DealBuyBtn dealBuyBtn : buyBtns) {
                if (dealBuyBtn.getDetailBuyType() == BuyBtnTypeEnum.MEMBER_CARD.getCode()) {
                    String title = getNewMemberCardTitle(context);
                    dealBuyBtn.setBtnTitle(getPromoButtonTitle(context, title));
                }
            }
        }

        doubleButtonHasNormalThenFixButtonTitle(buyBtns, context);

        if (buyBtns.size()==1){
            /**
             * 单底bar的时候同步btn名称和buyType
             */
            DealBuyBtn dealBuyBtn = buyBtns.get(0);
            int detailBuyType = dealBuyBtn.getDetailBuyType();
            BuyBtnTypeEnum buyBtnTypeEnum = BuyBtnTypeEnum.codeOf(detailBuyType);

            dealBuyBtn.setBtnTitle(getButtonTitle(context, DEFAULT_BUTTON_NAME));
            if (DealBuyHelper.isCouponBar(context)) {
                dealBuyBtn.setBtnTitle(CouponBarHelper.getCouponBtnTitle(context));
            }
            DealBuyBar buyBar = context.getBuyBar();
            switch (buyBtnTypeEnum){
                case NORMAL_DEAL:
                    buyBar.setBuyType(DealBuyBar.BuyType.COMMON.type);
                    break;
                case MEMBER_CARD:
                    buyBar.setBuyType(DealBuyBar.BuyType.DISCOUNTCARD.type);
                   break;
                case IDLE_BANNER:
                    buyBar.setBuyType(DealBuyBar.BuyType.IDLE_PROMO.type);
                    break;
                case TIMES_CARD:
                    buyBar.setBuyType(DealBuyBar.BuyType.TIMESCARD.type);
                    break;
                case PINTUAN:
                    buyBar.setBuyType(DealBuyBar.BuyType.PINPOOL.type);
                    break;
                case JOY_CARD:
                    buyBar.setBuyType(DealBuyBar.BuyType.JOY_CARD.type);
                    break;
                default:
            }
        }

        if (buyBtns.size()==2){
            /**
             * 双底bar的时候同步btn名称和buyType，以左边第一个底bar的类型为准
             */
            DealBuyBtn dealBuyBtn = buyBtns.get(0);
            int detailBuyType = dealBuyBtn.getDetailBuyType();
            BuyBtnTypeEnum buyBtnTypeEnum = BuyBtnTypeEnum.codeOf(detailBuyType);

            DealBuyBar buyBar = context.getBuyBar();
            switch (buyBtnTypeEnum){
                case NORMAL_DEAL:
                    buyBar.setBuyType(DealBuyBar.BuyType.COMMON.type);
                    break;
                case MEMBER_CARD:
                    buyBar.setBuyType(DealBuyBar.BuyType.DISCOUNTCARD.type);
                    break;
                case IDLE_BANNER:
                    buyBar.setBuyType(DealBuyBar.BuyType.IDLE_PROMO.type);
                    break;
                case TIMES_CARD:
                    buyBar.setBuyType(DealBuyBar.BuyType.TIMESCARD.type);
                    break;
                case PINTUAN:
                    buyBar.setBuyType(DealBuyBar.BuyType.PINPOOL.type);
                    break;
                case JOY_CARD:
                    buyBar.setBuyType(DealBuyBar.BuyType.JOY_CARD.type);
                    break;
                default:
            }
        }

        bottomLineProcessStrategy(buyBtns);
    }

    private boolean memberAndTimesAndNormalButton(DealBuyBtn dealBuyBtn, DealBuyBtn dealBuyBtn1,
            DealBuyBtn dealBuyBtn2) {
        return dealBuyBtn.getDetailBuyType() == BuyBtnTypeEnum.MEMBER_CARD.getCode()
                && dealBuyBtn1.getDetailBuyType() == BuyBtnTypeEnum.TIMES_CARD.getCode()
                && dealBuyBtn2.getDetailBuyType() == BuyBtnTypeEnum.NORMAL_DEAL.getCode();
    }

    private boolean doubleButtonHasMemberThenFixButtonTitle(List<DealBuyBtn> buyBtns) {
        if (buyBtns.size() != 2) {
            return false;
        }
        DealBuyBtn dealBuyBtn = buyBtns.get(0);
        DealBuyBtn dealBuyBtn1 = buyBtns.get(1);

        return (dealBuyBtn.getDetailBuyType() == BuyBtnTypeEnum.MEMBER_CARD.getCode()
                || dealBuyBtn1.getDetailBuyType() == BuyBtnTypeEnum.MEMBER_CARD.getCode());
    }

    private void doubleButtonHasNormalThenFixButtonTitle(List<DealBuyBtn> buyBtns,DealCtx context) {
        if (buyBtns.size() != 2) {
            return;
        }
        for (DealBuyBtn dealBuyBtn : buyBtns) {
            if (dealBuyBtn.getDetailBuyType() == BuyBtnTypeEnum.NORMAL_DEAL.getCode()) {
                dealBuyBtn.setBtnTitle(getButtonTitle(context, "立即抢购"));
                if (DealBuyHelper.isCouponBar(context)) {
                    dealBuyBtn.setBtnTitle(CouponBarHelper.getCouponBtnTitle(context));
                }
            }
        }
    }

    private String getNewMemberCardTitle(DealCtx context) {
        if (context.getPriceContext().getDcCardMemberCard() == null) {
            return null;
        }
        String title = "会员价";
        if (context.getPriceContext().isDcCardMemberDay()) {
            title = "会员日价";
        }
        return title;
    }

    private void bottomLineProcessStrategy(List<DealBuyBtn> buyBtns) {
        if (buyBtns.size() < 3) {
            return;
        }

        DealBuyBtn dealBuyBtn = getNormalBuyBtn(buyBtns);
        if (dealBuyBtn == null) {
            buyBtns.retainAll(Lists.newArrayList(buyBtns.get(0), buyBtns.get(1)));
            return;
        } else {
            buyBtns.remove(dealBuyBtn);
            buyBtns.retainAll(Lists.newArrayList(dealBuyBtn, buyBtns.get(0)));
        }
    }

    private DealBuyBtn getNormalBuyBtn(List<DealBuyBtn> buyBtns) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.button.joy.ButtonLocationPricePkSorter.getNormalBuyBtn(java.util.List)");
        for (DealBuyBtn dealBuyBtn : buyBtns) {
            if (dealBuyBtn.getDetailBuyType() == BuyBtnTypeEnum.NORMAL_DEAL.getCode()) {
                return dealBuyBtn;
            }
        }
        return null;
    }

    private boolean isUserHoldMemberCard(DealCtx context) {
        return CardHelper.holdCard(context.getPriceContext().getDcCardMemberCard());
    }

    @Override
    protected void doBuild(DealCtx context, ButtonBuilderChain chain) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.button.joy.ButtonLocationPricePkSorter.doBuild(DealCtx,ButtonBuilderChain)");

    }
}
