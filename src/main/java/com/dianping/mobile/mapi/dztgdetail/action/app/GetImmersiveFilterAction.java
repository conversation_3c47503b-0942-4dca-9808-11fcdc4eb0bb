package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.CatEvents;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageFilterRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageFilterVO;
import com.dianping.mobile.mapi.dztgdetail.facade.ImmersiveImageFilterFacade;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/10/24
 */
@InterfaceDoc(
        displayName = "到综团单沉浸页筛选项信息查询接口",
        type = "restful",
        description = "到综团单沉浸页筛选项信息",
        scenarios = "该接口适用于双平台App站点的团购详情页中有款式的头图进入沉浸页筛选项信息展示",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "zoujinmao"
)
@Controller("general/platform/dztgdetail/getimmersivefilter.bin")
@Action(url = "getimmersivefilter.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class GetImmersiveFilterAction extends AbsAction<GetImmersiveImageFilterRequest> {

    @Resource
    private ImmersiveImageFilterFacade immersiveImageFilterFacade;

    @Override
    protected IMobileResponse validate(GetImmersiveImageFilterRequest request, IMobileContext iMobileContext) {
        IdUpgradeUtils.processProductIdForGetImmersiveImageFilterRequest(request, "getimmersivefilter.bin");
        if (Objects.isNull(request) || request.getDealGroupId() <= 0) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @Override
    @CryptMethod
    protected IMobileResponse execute(GetImmersiveImageFilterRequest request, IMobileContext iMobileContext) {
        EnvCtx envCtx = initEnvCtx(iMobileContext);
        ImmersiveImageFilterVO immersiveImageFilterVO = immersiveImageFilterFacade.getImmersiveImageFilter(request, envCtx);
        try {
            if (Objects.nonNull(immersiveImageFilterVO)) {
                Cat.logMetricForCount(CatEvents.IMMERSIVE_IMAGE_FILTER_SUC);
                return new CommonMobileResponse(immersiveImageFilterVO);
            }
            Cat.logMetricForCount(CatEvents.IMMERSIVE_IMAGE_FILTER_NO_DATA);
            return new CommonMobileResponse(Resps.NoDataResp);
        } catch (Exception e) {
            log.error("getimmersiveimagefilter.bin error!", e);
        }
        Cat.logMetricForCount(CatEvents.IMMERSIVE_IMAGE_FILTER_FAIL);
        return Resps.SYSTEM_ERROR;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
