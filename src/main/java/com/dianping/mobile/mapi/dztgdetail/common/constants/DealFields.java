package com.dianping.mobile.mapi.dztgdetail.common.constants;

import com.meituan.service.mobile.prometheus.fields.DealModelFields;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/3/29.
 */
public class DealFields {

    public static final int GLOBAL_CITY_ID = 9999;

    //***************************** deal属性 **************************//
    public static final String DEAL_FIELD_ID = "id";
    public static final String DEAL_FIELD_SLUG = "slug";
    public static final String DEAL_FIELD_DT = "dt";
    public static final String DEAL_FIELD_CATE = "cate";
    public static final String DEAL_FIELD_SUBCATE = "subcate";
    public static final String DEAL_FIELD_DTYPE = "dtype";
    public static final String DEAL_FIELD_CTYPE = "ctype";
    public static final String DEAL_FIELD_BRANDNAME = "brandname";
    public static final String DEAL_FIELD_MLLS = "mlls";
    public static final String DEAL_FIELD_SOLDS = "solds";
    public static final String DEAL_FIELD_STATUS = "status";
    public static final String DEAL_FIELD_RANGE = "range";
    public static final String DEAL_FIELD_STARTTIME = "starttime";
    public static final String DEAL_FIELD_START = "start";
    public static final String DEAL_FIELD_ENDTIME = "endtime";
    public static final String DEAL_FIELD_END = "end";
    public static final String DEAL_FIELD_IMGURL = "imgurl";
    public static final String DEAL_FIELD_SQUAREIMGURL = "squareimgurl";
    public static final String DEAL_FIELD_TITLE = "title";
    public static final String DEAL_FIELD_MTITLE = "mtitle";
    public static final String DEAL_FIELD_PRICE = "price";
    public static final String DEAL_FIELD_VALUE = "value";
    public static final String DEAL_FIELD_PRICECALENDAR = "pricecalendar";
    public static final String DEAL_FIELD_RATING = "rating";
    public static final String DEAL_FIELD_RATE_COUNT = "rate-count";
    public static final String DEAL_FIELD_SATISFACTION = "satisfaction";
    public static final String DEAL_FIELD_SMSTITLE = "smstitle";
    public static final String DEAL_FIELD_POIIDS = "poiids";
    public static final String DEAL_FIELD_STATE = "state";
    public static final String DEAL_FIELD_APPLELOTTERY = "applelottery";
    public static final String DEAL_FIELD_CITYIDS = "cityIds";
    public static final String DEAL_FIELD_TAG = "tag";
    public static final String DEAL_FIELD_OPTIONALATTRS = "optionalattrs";
    public static final String DEAL_FIELD_CAMPAIGNS = "campaigns";
    public static final String DEAL_FIELD_BOOKINGINFO = "bookinginfo";
    public static final String DEAL_FIELD_DIGESTION = "menudigest";

    public static final String DEAL_FIELD_MURL = "murl";
    public static final String DEAL_FIELD_RDCOUNT = "rdcount";
    public static final String DEAL_FIELD_TIPS = "tips";
    public static final String DEAL_FIELD_TERMS = "terms";
    public static final String DEAL_FIELD_REFUND = "refund";
    public static final String DEAL_FIELD_FAKEREFUND = "fakerefund";
    public static final String DEAL_FIELD_SEVENREFUND = "sevenrefund";
    public static final String DEAL_FIELD_USAGE = "howuse";
    public static final String DEAL_FIELD_MENU = "menu";
    public static final String DEAL_FIELD_MEALCOUNT = "mealcount";
    public static final String DEAL_FIELD_NOBOOKING = "nobooking";
    public static final String DEAL_FIELD_FESTCANUSE = "festcanuse";
    public static final String DEAL_FIELD_VOICE = "voice";
    public static final String DEAL_FIELD_NEWRATING = "newrating";
    public static final String DEAL_FIELD_ATTRJSON = "attrJson";
    public static final String DEAL_FIELD_KTV = "ktvplan";
    public static final String DEAL_FIELD_BOOKINGPHONE = "bookingphone";
    public static final String DEAL_FIELD_PROPERTIES = "properties";

    public static final String DEAL_FIELD_ISAPPOINTONLINE = "isappointonline";
    public static final String DEAL_FIELD_COUPONBEGINTIME = "couponbegintime";
    public static final String DEAL_FIELD_COUPONENDTIME = "couponendtime";

    public static final String DEAL_FIELD_PITCHHTML = "pitchhtml";
    public static final String DEAL_FIELD_SECURITYINFO = "securityinfo";

    public static final String DEAL_FIELD_HOTELROOMNAME = "hotelroomname";
    //deal 新丛林品类
    public static final String DEAL_FIELD_FRONT_POI_CATES = "frontPoiCates";

    public static final String DEAL_FIELD_SECURITY_INFO = "securityinfo";

    public static final String DEAL_FIELD_SHOWTYPE = "showtype";
    public static final String DEAL_FIELD_DEPOSIT = "deposit";
    public static final String DEAL_FILED_CHANNEL = "channel";

    //***************************** deal展示的fields集合 **************************//
    /**
     * deal列表时需要展示fields
     */
    public static List<String> LIST_FIELDS = new ArrayList<String>() {
        private static final long serialVersionUID = 1L;

        {
            add("id");
            add("slug");
            add("cate");
            add("subcate");
            add("dtype");
            add("ctype");
            add("mname");
            add("brandname");
            add("mlls");
            add("solds");
            add("status");
            add("range");
            add("start");
            add("end");
            add("imgurl");
            add("squareimgurl");
            add("title");
            add(DEAL_FIELD_MTITLE);
            add("price");
            add("value");
            add("pricecalendar");
            add("mealcount");
            add("nobooking");
            add("rating");
            add("rate-count");
            add("satisfaction");
            add("smstitle");
            add("festcanuse");
            add("applelottery");
            add("tag");
            add("optionalattrs");
            add("campaigns");
            add(DEAL_FIELD_STATE);
            add(DEAL_FIELD_SHOWTYPE);
            add(DEAL_FIELD_DEPOSIT);
            add(DEAL_FIELD_COUPONBEGINTIME);
            add(DEAL_FIELD_COUPONENDTIME);
            add(DEAL_FIELD_BOOKINGINFO);
            add(DEAL_FIELD_FRONT_POI_CATES);
            add(DEAL_FILED_CHANNEL);
        }
    };

    /**
     * deal详情时需要展示的fields
     */
    public static List<String> DETAIL_FIELDS = new ArrayList<String>() {
        private static final long serialVersionUID = 1L;

        {
            addAll(LIST_FIELDS);

            add("murl");
            add("rdcount");
            add("rdplocs");
            add("tips");
            add("refund");
            add("fakerefund");
            add("sevenrefund");
            add("howuse");
            add("menu");
            add("announcementtitle");
            add("coupontitle");
            add(DEAL_FIELD_SECURITY_INFO);
            add("attrJson");
            add("voice");
            add("newrating");
            add("terms");
            add("ktvplan");
            add("bookingphone");
            add(DEAL_FILED_CHANNEL);
        }
    };

    /**
     * poi收藏的时候需要展示的deal信息.
     */
    public static List<String> COLLECTION_FIELDS = new ArrayList<String>() {
        private static final long serialVersionUID = 1L;

        {
            add(DealFields.DEAL_FIELD_CAMPAIGNS);
            add("salestag");
            add("price");
            add("title");
            add("optionalattrs");
        }
    };

    //详情页返回的字段列表类型二
    public static List<String> DETAIL_ALL_FIELDS = new ArrayList<String>() {
        private static final long serialVersionUID = 1L;

        {
            add("channel");
            add("ktvplan");
            add("mealcount");
            add("deposit");
            add("tag");
            add("hotelExt");
            add("solds");
            add("newrating");
            add("dtype");
            add("value");
            add("rate-count");
            add("imgurl");
            add("pricecalendar");
            add("optionalattrs");
            add("menu");
            add("bookinginfo");
            add("campaigns");
            add("announcementtitle");
            add("price");
            add("start");
            add("satisfaction");
            add("slug");
            add("recreason");
            add("securityinfo");
            add("voice");
            add("range");
            add("todayavaliable");
            add("squareimgurl");
            add("mlls");
            add("rdploc");
            add("terms");
            add("id");
            add("title");
            add("coupontitle");
            add("murl");
            add("end");
            add("endTime");
            add("campaignprice");
            add("mname");
            add("rdcount");
            add("brandname");
            add("ctype");
            add("showtype");
            add("attrJson");
            add("howuse");
            add("nobooking");
            add("isappointonline");
            add("canbuyprice");
            add("bookingphone");
            add("curcityrdcount");
            add("isvoucher");
            add(DealModelFields.DT);
            add(DealModelFields.CATES);
            add(DealModelFields.REFUND);
            add(DealModelFields.EXPIREAUTOREFUND);
            add(DealModelFields.RATINGMODEL);
            add(DealModelFields.RATING);
            add(DealModelFields.FRONTPOICATES);
        }
    };

    public static List<String> POI_FIELDS = new ArrayList<String>() {
        {
            add("rdcount");
            add("rdploc");
            add("rdplocs");
            add("mlls");
        }
    };

    // 相关团购
    public static List<String> SAME_BRAND = new ArrayList<String>() {
        private static final long serialVersionUID = 1L;

        {
            add("id");
            add("slug");
            add("imgurl");
            add("price");
            add("title");
            add("brandname");
            add("range");
            add("value");
            add("mlls");
        }
    };

    // 看了也看
    public static List<String> REREAD = new ArrayList<String>() {
        private static final long serialVersionUID = 1L;

        {
            add("id");
            add("slug");
            add("imgurl");
            add("price");
            add("title");
            add("brandname");
            add("range");
            add("value");
            add("mlls");
            add("solds");
        }
    };


    public static final Set<String> LIST_FIELDS_SET = new HashSet<String>();
    public static final Set<String> DETAIL_FIELDS_SET = new HashSet<String>();
    public static final Set<String> POI_FIELDS_SET = new HashSet<String>();
    public static final Set<String> COLLECTION_FIELDS_SET = new HashSet<String>();
    public static final Set<String> SAME_BRAND_SET = new HashSet<String>();
    public static final Set<String> REREAD_SET = new HashSet<String>();

    static {
        LIST_FIELDS_SET.addAll(LIST_FIELDS);
        DETAIL_FIELDS_SET.addAll(DETAIL_FIELDS);
        POI_FIELDS_SET.addAll(POI_FIELDS);
        COLLECTION_FIELDS_SET.addAll(COLLECTION_FIELDS);
        SAME_BRAND_SET.addAll(SAME_BRAND);
        REREAD_SET.addAll(REREAD);
    }


}

