package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.unavailable;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.exception.DealConfigException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/15 20:22
 */
@Service
public class UnavailableDateConfigService implements InitializingBean {

    private Map<String, Map<String, UnavailabelDate>> map = new ConcurrentHashMap<>();

    private void parse(String json) {
        Map<String, Map<String, UnavailabelDate>> map = JSONObject.parseObject(json,
                new TypeReference<Map<String, Map<String, UnavailabelDate>>>() {});
        if (map == null) {
            throw new DealConfigException("FATAL ERROR!!!查询中心不可用日期配置为空!!!");
        }
        this.map = map;
    }

    public Map<String, UnavailabelDate> getConfig() {
        LocalDate localDate = LocalDate.now();
        int year = localDate.getYear();
        return map.getOrDefault(String.valueOf(year), new HashMap<>());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        String DEAL_DISABLE_DAYS_CONFIG = "deal.disable.days.config";
        parse(Lion.getString("com.sankuai.dzshoppingguide.detail.commonmodule", DEAL_DISABLE_DAYS_CONFIG));
        Lion.addConfigListener(DEAL_DISABLE_DAYS_CONFIG, configEvent -> parse(configEvent.getValue()));
    }
}
