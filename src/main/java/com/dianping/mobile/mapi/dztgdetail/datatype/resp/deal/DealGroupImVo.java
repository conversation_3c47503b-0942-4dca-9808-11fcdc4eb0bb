package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import java.io.Serializable;

@Data
@TypeDoc(description = "团单IM数据模型")
@MobileDo(id = 0x27f)
public class DealGroupImVo implements Serializable {

    @FieldDoc(description = "imUrl")
    @MobileDo.MobileField(key = 0x7851)
    private String imUrl;

}
