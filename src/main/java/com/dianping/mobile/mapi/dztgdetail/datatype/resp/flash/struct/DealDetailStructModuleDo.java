package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.struct;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;
import java.util.List;

@TypeDoc(description = "到综页面卡数据")
@MobileDo(id = 0xbca3)
public class DealDetailStructModuleDo implements Serializable {

    @FieldDoc(description = "必选项目组")
    @MobileDo.MobileField(key = 0x4595)
    private List<MustDealSkuStructInfoDo> mustGroups;


    @FieldDoc(description = "可选项目组")
    @MobileDo.MobileField(key = 0xac4b)
    private List<OptionalDealSkuStructInfoDo> optionalGroups;

    @FieldDoc(description = "额外信息模块")
    @MobileDo.MobileField(key = 0x9c0f)
    private List<DealSkuStructExtraInfoDo> extraInfos;


    @FieldDoc(description = "现价")
    @MobileDo.MobileField(key = 0xb716)
    private String price;

    @FieldDoc(description = "市场价")
    @MobileDo.MobileField(key = 0x6242)
    private String marketPrice;


    @FieldDoc(description = "模块标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "多次卡模块标题")
    @MobileDo.MobileField(key = 0x14d0)
    private String timesDealTitle;

    @FieldDoc(description = "富文本")
    @MobileDo.MobileField(key = 0xfebf)
    private String desc;

    @FieldDoc(description = "服务流程类型")
    @MobileDo.MobileField(key = 0x5e5b)
    private int processType;

    @FieldDoc(description = "展示类型：0 文字，1 图标")
    @MobileDo.MobileField(key = 0x2550)
    private int showStyle;

    @FieldDoc(description = "是否隐藏市场价")
    @MobileDo.MobileField(key = 0x1f3a)
    private boolean marketPriceHided;

    @FieldDoc(description = "结构化属性")
    @MobileDo.MobileField(key = 0x56e1)
    private List<DealDetailStructAttrDo> structAttrs;

    @FieldDoc(description = "团单公共模块")
    @MobileDo.MobileField(key = 0x9cb2)
    private List<DealDetailStructAttrDo> commonModule;

    @FieldDoc(description = "团单公共模块标题")
    @MobileDo.MobileField(key = 0x2563)
    private String commonModuleName;

    @FieldDoc(description = "项目组标题")
    @MobileDo.MobileField(key = 0xf306)
    private String groupsName;

    @FieldDoc(description = "服务流程信息")
    @MobileDo.MobileField(key = 0xfd76)
    private InstructionStruct instruction;

    @FieldDoc(description = "驾校考试费和补考费，目前仅h5请求返回数据")
    @MobileDo.MobileField(key = 0xae46)
    private FeeDetailsVO feeDetailsVO;

    public List<MustDealSkuStructInfoDo> getMustGroups() {
        return mustGroups;
    }

    public void setMustGroups(List<MustDealSkuStructInfoDo> mustGroups) {
        this.mustGroups = mustGroups;
    }

    public List<OptionalDealSkuStructInfoDo> getOptionalGroups() {
        return optionalGroups;
    }

    public void setOptionalGroups(List<OptionalDealSkuStructInfoDo> optionalGroups) {
        this.optionalGroups = optionalGroups;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(String marketPrice) {
        this.marketPrice = marketPrice;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTimesDealTitle() {
        return timesDealTitle;
    }

    public void setTimesDealTitle(String timesDealTitle) {
        this.timesDealTitle = timesDealTitle;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public List<DealDetailStructAttrDo> getStructAttrs() {
        return structAttrs;
    }

    public void setStructAttrs(List<DealDetailStructAttrDo> structAttrs) {
        this.structAttrs = structAttrs;
    }

    public int getProcessType() {
        return processType;
    }

    public void setProcessType(int processType) {
        this.processType = processType;
    }

    public List<DealSkuStructExtraInfoDo> getExtraInfos() {
        return extraInfos;
    }

    public void setExtraInfos(List<DealSkuStructExtraInfoDo> extraInfos) {
        this.extraInfos = extraInfos;
    }

    public int getShowStyle() {
        return showStyle;
    }

    public void setShowStyle(int showStyle) {
        this.showStyle = showStyle;
    }

    public boolean isMarketPriceHided() {
        return marketPriceHided;
    }

    public void setMarketPriceHided(boolean marketPriceHided) {
        this.marketPriceHided = marketPriceHided;
    }

    public List<DealDetailStructAttrDo> getCommonModule() {
        return commonModule;
    }

    public void setCommonModule(List<DealDetailStructAttrDo> commonModule) {
        this.commonModule = commonModule;
    }

    public String getCommonModuleName() {
        return commonModuleName;
    }

    public void setCommonModuleName(String commonModuleName) {
        this.commonModuleName = commonModuleName;
    }

    public String getGroupsName() {
        return groupsName;
    }

    public void setGroupsName(String groupsName) {
        this.groupsName = groupsName;
    }

    public InstructionStruct getInstruction() {
        return instruction;
    }

    public void setInstruction(InstructionStruct instruction) {
        this.instruction = instruction;
    }

    public FeeDetailsVO getFeeDetailsVO() {
        return feeDetailsVO;
    }

    public void setFeeDetailsVO(FeeDetailsVO feeDetailsVO) {
        this.feeDetailsVO = feeDetailsVO;
    }
}