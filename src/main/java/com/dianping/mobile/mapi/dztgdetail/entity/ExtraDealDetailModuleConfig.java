package com.dianping.mobile.mapi.dztgdetail.entity;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.ExtraDealDetailModule;
import com.sankuai.sig.botdefender.adapter.annotation.EncryptedLinkField;
import lombok.Data;

import java.util.List;
@Data
public class ExtraDealDetailModuleConfig {
    /**
     * 主标题标签
     */
    private String icon;

    private List<AttrListConfig> attrList;

    /**
     * 主标题
     */
    private String mainTitle;

    /**
     * 标题信息
     */
    private String titleInfo;

    /*
    * 背景图
    * */
    private String backgroundPic;

    @Data
    public static class AttrListConfig {
        private String value;

        private String name;

        private String attrKey;

        private String secondAttrKey;

        private String pic;
    }
}
