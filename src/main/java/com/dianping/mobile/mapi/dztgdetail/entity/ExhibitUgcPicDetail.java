package com.dianping.mobile.mapi.dztgdetail.entity;

import lombok.Data;

import java.util.List;

@Data
public class ExhibitUgcPicDetail {
    /**
     * 图片url
     */
    private String picUrl;
    /**
     * 美观度得分
     */
    private Double aestheticScore;
    /**
     * 评价来源，1为点评，2为美团
     */
    private Integer reviewPlatform;
    /**
     * 评价ID
     */
    private Long reviewId;
    /**
     * 图片素材ID
     */
    private String contentId;

    /**
     * 标签ID列表
     */
    private List<Integer> tags;
//    /**
//     * 用户ID
//     */
//    private Long userId;
//    /**
//     * 用户名称
//     */
//    private String userName;
//    /**
//     * 坐标
//     * todo
//     */
//    private List<Double> coordinate;
}
