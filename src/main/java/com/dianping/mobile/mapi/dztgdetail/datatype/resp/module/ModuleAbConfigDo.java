package com.dianping.mobile.mapi.dztgdetail.datatype.resp.module;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@MobileDo(id = 0xc631)
@Data
public class ModuleAbConfigDo implements Serializable {

    @FieldDoc(description = "模块名称")
    @MobileField(key = 0x9e5e)
    private String key;

    @FieldDoc(description = "ab测试")
    @MobileField(key = 0xddc1)
    private List<AbConfigDo> configs;
}