package com.dianping.mobile.mapi.dztgdetail.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * deal搜索排序种类
 * <p/>
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/3/29.
 */
public enum DealSearchSortEnum {
    natural("natural", 0)// 不排序
    , defaults("defaults", 1)// 默认排序
    , distance("distance", 1)// 距离
    , price("price", 1)// 价格
    , priceDesc("price", -1)// 价格高到低
    , solds("solds", -1)// 销量
    , start("start", -1)// 开始时间
    , rating("rating", -1)//评分
    ,;

    public final int asc;// 为正则升序

    private String field;

    DealSearchSortEnum(String field, int asc) {
        this.field = field;
        this.asc = asc;
    }

    public String getField() {
        return field;
    }

    /**
     * 根据name查找enum
     */
    public static DealSearchSortEnum getEnumByName(String name) {
        for (DealSearchSortEnum sort : DealSearchSortEnum.values()) {
            if (StringUtils.equals(name, sort.name())) {
                return sort;
            }
        }
        return natural;
    }
}
