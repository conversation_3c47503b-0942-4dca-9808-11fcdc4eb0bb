package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-10-10
 * @desc 团单价格趋势信息
 */
@TypeDoc(description = "团单价格趋势展示层对象")
@Data
@MobileDo(id = 0xe07)
public class DealPriceTrendVO {
    @FieldDoc(description = "团单Id")
    @MobileDo.MobileField(key = 0x9a1c)
    private Integer dealGroupId;

    @FieldDoc(description = "团单标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "团单副标题")
    @MobileDo.MobileField(key = 0xd894)
    private String subTitle;

    @FieldDoc(description = "年销量，已售1万")
    @MobileDo.MobileField(key = 0xa2c0)
    private String saleTag;

    @FieldDoc(description = "1:1头图")
    @MobileDo.MobileField(key = 0x30ed)
    private String headPic;

    @FieldDoc(description = "价格力标签")
    @MobileDo.MobileField(key = 0x2617)
    private List<String> pricePowerTag;

    @FieldDoc(description = "折扣，如5.0折")
    @MobileDo.MobileField(key = 0x6509)
    private String discount;

    @FieldDoc(description = "到手价")
    @MobileDo.MobileField(key = 0xe949)
    private String finalPrice;

    @FieldDoc(description = "门市价（划线价）")
    @MobileDo.MobileField(key = 0x6242)
    private String marketPrice;

    @FieldDoc(description = "基准价，即XX天前价￥AA.BB")
    @MobileDo.MobileField(key = 0x19f9)
    private String benchMarkPrice;

    @FieldDoc(description = "基准价标签，如180天前价")
    @MobileDo.MobileField(key = 0x623d)
    private String benchMarkPriceTag;

    @FieldDoc(description = "更新时间", rule = "当天时间7:00")
    @MobileDo.MobileField(key = 0x9d6a)
    private String updateTime;

    @FieldDoc(description = "购物车提单页")
    @MobileDo.MobileField(key = 0x60b)
    private String directBuyJumpUrl;

    @FieldDoc(description = "按日期/价格给出价格波动数据")
    @MobileDo.MobileField(key = 0xfe1e)
    private List<PriceTrendVO> trends;

    @FieldDoc(description = "价格走势说明")
    @MobileDo.MobileField(key = 0x830a)
    private String priceTrendDesc;
}
