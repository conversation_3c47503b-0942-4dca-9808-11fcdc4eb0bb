package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.rich.content.enums;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.rich.content.PicRichContentVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.rich.content.RichContentVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.rich.content.TextRichContentVO;
import lombok.Getter;

/**
 * @Author: guangyujie
 * @Date: 2025/3/16 15:12
 */
@Getter
public enum RichContentTypeEnum {
    UNKNOWN(0, "未知", null),
    TEXT(1, "文字", TextRichContentVO.class),
    PIC(2, "图片", PicRichContentVO.class),
    ;

    private final int code;

    private final String desc;

    private final Class<? extends RichContentVO> richContentVOClass;

    public static RichContentTypeEnum fromCode(int code) {
        for (RichContentTypeEnum value : RichContentTypeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return UNKNOWN;
    }

    RichContentTypeEnum(int code, String desc, Class<? extends RichContentVO> richContentVOClass) {
        this.code = code;
        this.desc = desc;
        this.richContentVOClass = richContentVOClass;
    }
}