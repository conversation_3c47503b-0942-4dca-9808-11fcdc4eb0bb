package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.cat.Cat;
import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;

@MobileRequest
public class GetcorpwxentrymaterialRequest implements IMobileRequest {
    /**
     * 商户id
     */
    @MobileRequest.Param(name = "shopidforlong")
    private Long shopIdForLong;

    /**
     * 页面来源
     */
    @MobileRequest.Param(name = "pageresource")
    private String pageResource;

    /**
     * 团单id
     */
    @MobileRequest.Param(name = "dealgroupid", required = true)
    private String dealGroupId;

    /**
     * 用户纬度
     */
    @MobileRequest.Param(name = "userlat", required = true)
    private Double userLat;

    /**
     * 用户经度
     */
    @MobileRequest.Param(name = "userlng", required = true)
    private Double userLng;

    /**
     * cityId
     */
    @MobileRequest.Param(name = "cityid", required = true)
    private Integer cityId;

    public Long getShopIdForLong() {
        return shopIdForLong;
    }

    public void setShopIdForLong(Long shopIdForLong) {
        this.shopIdForLong = shopIdForLong;
    }

    public String getPageResource() {
        return pageResource;
    }

    public void setPageResource(String pageResource) {
        this.pageResource = pageResource;
    }

    public String getDealGroupId() {
        return dealGroupId;
    }

    public void setDealgroupid(String dealGroupId) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.datatype.req.GetcorpwxentrymaterialRequest.setDealgroupid(java.lang.String)");
        this.dealGroupId = dealGroupId;
    }

    public Double getUserLat() {
        return userLat;
    }

    public void setUserLat(Double userLat) {
        this.userLat = userLat;
    }

    public Double getUserLng() {
        return userLng;
    }

    public void setUserlng(Double userLng) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.datatype.req.GetcorpwxentrymaterialRequest.setUserlng(java.lang.Double)");
        this.userLng = userLng;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityid(Integer cityId) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.datatype.req.GetcorpwxentrymaterialRequest.setCityid(java.lang.Integer)");
        this.cityId = cityId;
    }
}
