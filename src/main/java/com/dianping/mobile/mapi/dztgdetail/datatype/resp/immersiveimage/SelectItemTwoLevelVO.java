package com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.sankuai.sig.botdefender.adapter.annotation.EncryptedLinkField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/10/26
 */
@Data
@MobileDo(id = 0x3aa2)
public class SelectItemTwoLevelVO {
    /**
     * 标签配置icon链接
     */
    @MobileDo.MobileField(key = 0xf39b)
    private String iconUrl;

    /**
     * 选项类型，0单选，1多选
     */
    @MobileDo.MobileField(key = 0xb790)
    private Integer choiceType;

    /**
     * 是否默认选中
     */
    @MobileDo.MobileField(key = 0xb59e)
    private Boolean selected;

    /**
     * 有的筛选项是点击后直接跳转列表页
     */
    @MobileDo.MobileField(key = 0x774e)
//    @EncryptedLinkField(queries = {"shopid","shopId","poiid","poiId"})
    private String jumpUrl;

    /**
     * 筛选项值
     */
    @MobileDo.MobileField(key = 0x2585)
    private String selectValue;

    /**
     * 筛选项名词
     */
    @MobileDo.MobileField(key = 0x7ab8)
    private String name;
}
