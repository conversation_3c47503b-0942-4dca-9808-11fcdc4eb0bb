package com.dianping.mobile.mapi.dztgdetail.action.app;


import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.deal.idmapper.api.DealIdMapperService;
import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DrivingPoiService;
import com.dianping.mobile.mapi.dztgdetail.biz.service.bo.DrivingPoi;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.ShopUuidUtils;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DrivingPoiReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedDeals;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedShop;
import com.dianping.mobile.mapi.dztgdetail.exception.QueryCenterResultException;
import com.dianping.mobile.mapi.dztgdetail.helper.AppCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import com.dianping.mobile.mapi.dztgdetail.helper.SwitchHelper;
import com.dianping.mobile.mapi.dztgdetail.util.AntiCrawlerUtils;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.HttpMethod;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupCategoryBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Future;

@InterfaceDoc(displayName = "练车poi查询接口",
        type = "restful,mapi",
        description = "查询练车poi信息",
        scenarios = "该接口仅适用于双平台APP站点及h5的团购详情页",
        host = "http://mapi.dianping.com/general/platform/getdrivingpoi/",
        authors = "wangziyun04"
)
@Controller("general/platform/dztgdetail/getdrivingpoi.bin")
@Action(url = "getdrivingpoi.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class DrivingPoiAction extends AbsAction<DrivingPoiReq> {

    @Autowired
    private DrivingPoiService drivingPoiService;

    @Autowired
    private DealIdMapperService dealIdMapperService;

    @Autowired
    private DealGroupWrapper dealGroupWrapper;

    @Autowired
    @Qualifier("queryCenterDealGroupQueryService")
    private DealGroupQueryService queryCenterDealGroupQueryService;

    @Override
    protected IMobileResponse validate(DrivingPoiReq request, IMobileContext iMobileContext) {
        IdUpgradeUtils.processProductIdForDrivingPoiReq(request, "getdrivingpoi.bin");
        if (request == null || request.getDealGroupId() <= 0) {
            return Resps.PARAM_ERROR;
        }

        if (StringUtils.isBlank(request.getShopUuid()) && StringUtils.isBlank(request.getShopIdStr())) {
            return Resps.PARAM_ERROR;
        }

        return null;
    }

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "getdrivingpoi.bin",
            displayName = "查询到综团单关联团单信息",
            description = "查询到综团单关联团单信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "getdrivingpoi.bin请求参数",
                            type = DrivingPoiReq.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "iMobileContext",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "result", description = "团购picasso数据",type = RelatedDeals.class)},
            //restExampleUrl = "",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    ),
            }
    )
    @Override
    @CryptMethod
    protected IMobileResponse execute(DrivingPoiReq request, IMobileContext iMobileContext) {
        if(GreyUtils.isQueryCenterGreyBatch1(request.getDealGroupId())) {
            try {
                return getResultByQueryCenter(request, iMobileContext);
            } catch (Exception e) {
                logger.error("getResultByQueryCenter error, ", e);
            }
        }
        try {

            int dpDealGroupId;
            String clientType = iMobileContext.getParameter("clientType");
            boolean isMtH5 = StringUtils.isNotBlank(clientType) && ClientTypeEnum.isMtPlatform(Integer.parseInt(clientType));
            boolean isMt = isMtH5 || AppCtxHelper.isMeituanClient(iMobileContext);

            Long shopId = getShopIdFromReq(isMt, request.getShopIdStr(), request.getShopUuid());

            if (shopId == null || shopId <= 0) {
                return Resps.SYSTEM_ERROR;
            }

            if (!SwitchHelper.enableDrivingShop(isMt, shopId, request.getCityId())) {
                List<RelatedShop> drivingShops = new ArrayList<>();
                return new CommonMobileResponse(drivingShops);
            }

            if (isMt) {
                IdMapper mapper = dealIdMapperService.queryByMtDealGroupId(request.getDealGroupId());
                if (mapper == null || mapper.getDpDealGroupID() <= 0) {
                    return Resps.SYSTEM_ERROR;
                }

                dpDealGroupId = mapper.getDpDealGroupID();

            } else {
                dpDealGroupId = request.getDealGroupId();
            }

            Future publishCategoryFuture = dealGroupWrapper.preDealGroupPublishCategoryById(dpDealGroupId);
            Future attrFuture = dealGroupWrapper.preAttrs(dpDealGroupId, Collections.singletonList(DealAttrKeys.SERVICE_TYPE));

            int publishCategory = dealGroupWrapper.getFutureResult(publishCategoryFuture);
            List<AttributeDTO> dealGroupAttrs = dealGroupWrapper.getFutureResult(attrFuture);
            List<String> serviceTypeAttr = AttributeUtils.getAttributeValues(DealAttrKeys.SERVICE_TYPE, dealGroupAttrs);

            DrivingPoi drivingPoi = DrivingPoi.builder()
                    .publishCategory(publishCategory)
                    .serviceTypeAttr(serviceTypeAttr)
                    .isMt(isMt)
                    .shopId(shopId)
                    .cityId(request.getCityId())
                    .userLng(request.getUserLng())
                    .userLat(request.getUserLat())
                    .enableMap(false)
                    .build();

            List<RelatedShop> drivingShops = drivingPoiService.getDrivingShops(drivingPoi);

            return new CommonMobileResponse(drivingShops);

        } catch (Exception e) {
            logger.error("getdrivingpoi.bin error",e);
            return Resps.SYSTEM_ERROR;
        }

    }

    private CommonMobileResponse getResultByQueryCenter(DrivingPoiReq request, IMobileContext iMobileContext) throws Exception {
        String clientType = iMobileContext.getParameter("clientType");
        boolean isMtH5 = StringUtils.isNotBlank(clientType) && ClientTypeEnum.isMtPlatform(Integer.parseInt(clientType));
        boolean isMt = isMtH5 || AppCtxHelper.isMeituanClient(iMobileContext);

        Long shopId = getShopIdFromReq(isMt, request.getShopIdStr(), request.getShopUuid());

        if (shopId == null || shopId <= 0) {
            return Resps.SYSTEM_ERROR;
        }

        if (!SwitchHelper.enableDrivingShop(isMt, shopId, request.getCityId())) {
            List<RelatedShop> drivingShops = new ArrayList<>();
            return new CommonMobileResponse(drivingShops);
        }

        QueryDealGroupListResponse queryDealGroupListResponse = null;
        Set<Long> set = new HashSet<>();
        set.add(Long.valueOf(request.getDealGroupId()));
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(set, isMt ? IdTypeEnum.MT : IdTypeEnum.DP)
                .category(DealGroupCategoryBuilder.builder().all())
                .build();

        queryDealGroupListResponse = queryCenterDealGroupQueryService.queryByDealGroupIds(queryByDealGroupIdRequest);

        if(queryDealGroupListResponse == null) {
            throw new QueryCenterResultException("queryCenter returns null, request : " + GsonUtils.toJsonString(queryByDealGroupIdRequest));
        }

        if(queryDealGroupListResponse.getCode() != 0) {
            throw new QueryCenterResultException("queryCenter not success, request : " + GsonUtils.toJsonString(queryByDealGroupIdRequest));
        }

        if(CollectionUtils.isEmpty(queryDealGroupListResponse.getData().getList())) {
            throw new QueryCenterResultException("queryDealGroupListResponse.getData().getList() is empty, request : " + GsonUtils.toJsonString(queryByDealGroupIdRequest));
        }

        if(queryDealGroupListResponse.getData().getList().get(0) == null) {
            throw new QueryCenterResultException("queryDealGroupListResponse.getData().getList().get(0) is null, request : " + GsonUtils.toJsonString(queryByDealGroupIdRequest));
        }

        DealGroupDTO dealGroupDTO = queryDealGroupListResponse.getData().getList().get(0);
        Long publishCategory = dealGroupDTO.getCategory() == null ? 0L : dealGroupDTO.getCategory().getCategoryId();
        String serviceType = dealGroupDTO.getCategory() == null ? "" : dealGroupDTO.getCategory().getServiceType();
        List<String> serviceTypeAttr = Lists.newArrayList(serviceType);

        DrivingPoi drivingPoi = DrivingPoi.builder()
                .publishCategory(Math.toIntExact(publishCategory))
                .serviceTypeAttr(serviceTypeAttr)
                .isMt(isMt)
                .shopId(shopId)
                .cityId(request.getCityId())
                .userLng(request.getUserLng())
                .userLat(request.getUserLat())
                .enableMap(false)
                .build();

        List<RelatedShop> drivingShops = drivingPoiService.getDrivingShops(drivingPoi);
        // 反爬信息处理
        hideKeyInfo(drivingShops, iMobileContext);
        return new CommonMobileResponse(drivingShops);
    }

    private Long getShopIdFromReq(boolean isMt, String shopIdStr, String shopUuid) {
        if (StringUtils.isNotBlank(shopIdStr) && StringUtils.isNumeric(shopIdStr)) {
            Long shopId = Long.valueOf(shopIdStr);

            if (shopId > 0) {
                return shopId;
            }
        }

        if (!isMt && StringUtils.isNotBlank(shopUuid)) {
            return ShopUuidUtils.getShopIdByUuid(shopUuid);
        }

        return null;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }


    private void hideKeyInfo(List<RelatedShop> drivingShops, IMobileContext iMobileContext) {
        if (CollectionUtils.isEmpty(drivingShops)) {
            return;
        }
        if (!AntiCrawlerUtils.hide(iMobileContext)) {
            return;
        }
        drivingShops.forEach(drivingShop -> {
            drivingShop.setAddress("登录后查看具体地址");
        });
    }
}
