package com.dianping.mobile.mapi.dztgdetail.common.enums;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;

import java.io.Serializable;
import java.util.List;

/**
 * Created by wa<PERSON><PERSON><PERSON> on 16/4/19.
 *  用于title,content的对
 **/
@MobileDo
public class MtTerm implements Serializable{

    @MobileField
    private String title;
    @MobileField
    private List<String> contents;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<String> getContents() {
        return contents;
    }

    public void setContents(List<String> contents) {
        this.contents = contents;
    }
}
