package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.annotation.MobileRequest.Param;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 2020/6/17 4:57 下午
 */
@Data
@MobileRequest
public class SaleRankingRequest implements IMobileRequest {
    /**
     * 门店uuid
     */
    @Param(name = "shopuuid")
    private String shopuuid;

    /**
     * 门店ID
     */
    @Param(name = "shopid")
    private Integer shopid;

    /**
     * 团单ID
     */
    @Deprecated
    @Param(name = "dealgroupid")
    private Integer dealgroupid;

    /**
     * 团单ID
     */
    @Param(name = "stringdealgroupid")
    private String stringDealGroupId;

    /**
     * 门店ID
     */
    @Param(name = "shopidstr")
    private String shopIdStr;

    public Long getShopIdLong() {
        if(StringUtils.isNumeric(shopIdStr)) {
            return Long.parseLong(shopIdStr);
        } else if(shopid != null) {
            return shopid.longValue();
        } else {
            return 0L;
        }
    }

}
