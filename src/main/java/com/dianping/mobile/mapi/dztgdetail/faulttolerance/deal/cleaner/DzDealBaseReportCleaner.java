package com.dianping.mobile.mapi.dztgdetail.faulttolerance.deal.cleaner;

import com.dianping.mobile.framework.base.datatypes.HttpCode;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.google.gson.Gson;
import com.sankuai.athena.stability.faulttolerance.execution.FaultToleranceExecutionCtx;
import com.sankuai.athena.stability.faulttolerance.mirror.MirrorConfiguration;
import com.sankuai.athena.stability.faulttolerance.mirror.fetch.MirrorFetchCleaner;
import com.sankuai.athena.stability.faulttolerance.mirror.report.MirrorReportCleaner;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-11-06
 * @desc 团详主接口上报数据清洗
 */
@Component
public class DzDealBaseReportCleaner extends MirrorConfiguration implements MirrorReportCleaner, MirrorFetchCleaner {
    @Override
    public <R> MirrorReportCleaner<R> getCleaner() {
        return this;
    }

    @Override
    public <P,R> MirrorFetchCleaner<P,R> getFetchCleaner() {
        return this;
    }

    /**
     * 上报数据清洗
     * @param ctx 清洗上下文
     * @param result 清洗结果
     * @return  清洗后的响应结果
     */
    @Override
    public Object clean(FaultToleranceExecutionCtx ctx, Object result) {
        if (result instanceof CommonMobileResponse) {
            CommonMobileResponse response = (CommonMobileResponse) result;
            if (!Objects.equals(response.getStatusCode().getCode(), HttpCode.HTTPOK.getCode())) {
                return null;
            }
            Gson gson = new Gson();
            DealGroupPBO dealGroup = gson.fromJson(gson.toJson(response.getData()), DealGroupPBO.class);
            return new CommonMobileResponse(dealGroup, HttpCode.HTTPOK);
        }
        return result;
    }

    @Override
    public Object clean(Object request, Object result) {
        if (result instanceof CommonMobileResponse) {
            CommonMobileResponse response = (CommonMobileResponse) result;
            response.setStatusCode(HttpCode.HTTPOK);
            return response;
        }
        return result;
    }
}
