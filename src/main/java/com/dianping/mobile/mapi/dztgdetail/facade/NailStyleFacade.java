package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.mobile.mapi.dztgdetail.biz.hotstyle.NailStyleService;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetHotNailStyleRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetNailStyleImageRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.nailstyle.HotNailStyleModuleVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.nailstyle.OrderNailStyleImageVO;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024-01-03
 * @desc 实现查询热门款式信息
 */
@Component
public class NailStyleFacade {

    @Resource
    private NailStyleService nailStyleService;

    /**
     * 获取美甲热门款式
     * @param request 团单id、门店id
     * @param envCtx 环境上下文
     * @return 美甲热门款式
     */
    public HotNailStyleModuleVO getHotNailStyle(GetHotNailStyleRequest request, EnvCtx envCtx) {
        checkRequest(request);
        // 如果美甲热门款式模块开关关闭，则不展示该模块
        if (!LionConfigUtils.getHotNailStyleModuleSwitch()) {
            return null;
        }
        return nailStyleService.queryHotNailStyle(request, envCtx);
    }

    /**
     * 获取订单页款式
     * @param request 团单id、门店id
     * @param envCtx 环境上下文
     * @return 美甲款式
     */
    public OrderNailStyleImageVO getOrderNailStyle(GetNailStyleImageRequest request, EnvCtx envCtx) {
        checkRequest(request);
        if (!LionConfigUtils.getOrderNailStyleImageSwitch()) {
            return null;
        }
        return nailStyleService.queryOrderNailStyle(request, envCtx);
    }

    private void checkRequest(GetHotNailStyleRequest request) {
        Validate.isTrue(Objects.nonNull(request.getDealGroupId()) && request.getDealGroupId() > 0);
        Validate.isTrue(Objects.nonNull(request.getShopId()) && request.getShopId() > 0);
    }

    private void checkRequest(GetNailStyleImageRequest request) {
        Validate.isTrue(Objects.nonNull(request.getDealGroupId()) && request.getDealGroupId() > 0);
        Validate.isTrue(Objects.nonNull(request.getShopId()) && request.getShopId() > 0);
    }
}
