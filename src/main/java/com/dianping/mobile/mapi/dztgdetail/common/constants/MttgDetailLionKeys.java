package com.dianping.mobile.mapi.dztgdetail.common.constants;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/6/17.
 */
public class MttgDetailLionKeys {
    public static final String APP_KEY = "mapi-mttgdetail-web";

    //降级配置
    public static final String LIST_DEAL_BY_IDS_DEGRADE = "mapi-mttgdetail-web.degrade.listDealByIds";
    public static final String LIST_POIID_BY_IDDS_DEGRADE = "mapi-mttgdetail-web.degrade.listPoiidByDids";
    public static final String LIST_SHOPPING_MALLS_DEGRADE = "mapi-mttgdetail-web.degrade.listShoppingMalls";
    public static final String LIST_PRIMARY_FRONT_CATE_IDS_DEGRADE = "mapi-mttgdetail-web.degrade.listPrimaryFrontCateIds";

    public static final String DEAL_SHOP_VERIFY_SWITCH = "mapi-mttgdetail-web.degrade.shopverify.switch";

    public static final String ALL_NOT_DZX = "mapi-mttgdetail-web.degrade.allNotDzx";
    public static final String ALL_NOT_DPORDER = "mapi-mttgdetail-web.degrade.allNotDpOrder";

    public static final String PROMOTION_INFOS_DEGRADE = "mapi-mttgdetail-web.degrade.switch.promoDegrade";
    public static final String PROMOTION_INFOS_DEGRADE_TO_MT = "mapi-mttgdetail-web.degrade.switch.promoDegradeToMT";

    public static final String PROMO_ACTIVITY_ENABLE = "mapi-mttgdetail-web.promoactivity.enable";
    public static final String PROMO_ACTIVITY_CATEGORY = "mapi-mttgdetail-web.promoactivity.category";

    public static final String BEAUTY_HAIR_STRUCTURE_ENABLE = "mapi-mttgdetail-web.beauty.hair.structured.enable";
    public static final String BEAUTY_NAIL_STRUCTURE_ENABLE = "mapi-mttgdetail-web.beauty.nail.structured.enable";
    public static final String UNIFIED_MODULE_ENABLE = "com.sankuai.dzu.tpbase.dztgdetailweb.unified.module.enable";
    public static final String BEAUTY_BAG_ENABLE = "com.sankuai.dzu.tpbase.dztgdetailweb.beauty.bag.enable";
    public static final String MEMBER_EXCLUSIVE_ENABLE = "com.sankuai.dzu.tpbase.dztgdetailweb.member.exclusive.enable";
    public static final String POI_SHOP_CATEGORY_CACHE_ENABLE = "com.sankuai.dzu.tpbase.dztgdetailweb.poi.ctegory.cache.enable";


    public static final String UGC_SHOP_FEED_SIZZE = "mapi-mttgdetail-web.ugc.shop.feed.size";

    public static final String UGC_DEAL_FEED_SIZZE = "mapi-mttgdetail-web.ugc.deal.feed.size";

    public static final String UGC_SHOP_FEEDTAG_SHOW = "mapi-mttgdetail-web.ugc.shop.feedtag.show";

    public static final String DETAIL_I_MT_URL = "mapi-mttgdetail-web.mtdetail.i.mtURL";
    public static final String DETAIL_I_DP_URL = "mapi-mttgdetail-web.mtdetail.i.dpURL";

    public static final String PC_DEAL_DETAIL_DAOCANG_URL = "mapi-mttgdetail-web.pc.deal.detail.daocang.url";

    public static final String ORDER_I_DZX_PERCENT = "mapi-mttgdetail-web.order.i.dzxPercent";
    public static final String ORDER_I_MT_URL = "mapi-mttgdetail-web.order.i.mtURL";
    public static final String ORDER_I_DP_URL = "mapi-mttgdetail-web.order.i.dpURL";

    public static final String INVOKE_STYLE_SERVICE_ENABLE = "mapi-mttgdetail-web.style.service.enable";

    public static final String ONLINE_CONSULT_CATEGORY = "mapi-mttgdetail-web.onlineConsult.dpcate";
    public static final String STRUCTED_DETAIL_ENABLE = "mapi-mttgdetail-web.structeddetail.enable";

    public static final String BOOK_CATEGORYIDS = "mapi-mttgdetail-web.book.categoryids";
    public static final String UNIFIED_STOCK_ENABLE = "mapi-mttgdetail-web.unified.stock.enable";

    public static final String SHOW_STOCK_CATES = "mapi-mttgdetail-web.showstock.cates";
    public static final String DISPOSE_DETAIL = "mapi-mttgdetail-web.dispose.detail.enable";

    public static final String DEAL_DETAIL_IMG_URL_ENCRYPT_ENABLE = "mapi-mttgdetail-web.dealDetailImgUrl.encrypt.enable";
    public static final String APP_DEAL_DETAIL_IMG_URL_ENCRYPT_ENABLE = "mapi-mttgdetail-web.appDealDetailImgUrl.encrypt.enable";

    public static final String PHONE_SWITCH="mapi-mttgdetail-web.phone.switch";

    /**
     * 隐藏市场价的城市与POI后台二级分类配置
     */
    public static final String CITY_POIBACKCATEGORIES_CONFIG = "mapi-mttgdetail-web.hide.marketprice.city2poibackcategories.config";

    /**
     * 隐藏市场价的门店黑名单
     */
    public static final String HIDE_MARKETPRICE_SHOPBLACKLIST = "mapi-mttgdetail-web.hide.marketprice.shop.blacklist";

    /**
     * 隐藏市场价的团单二级分类
     */
    public static final String HIDE_MARKETPRICE_CATEGORY_LIST = "mapi-mttgdetail-web.hide.marketprice.dealgroupcategories";

    public static final String DISPLAY_SALES_FUZZY = "display.sales.fuzzy";

}