package com.dianping.mobile.mapi.dztgdetail.common.constants;

import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;

import static java.time.format.DateTimeFormatter.ISO_LOCAL_DATE;
import static java.time.temporal.ChronoField.HOUR_OF_DAY;
import static java.time.temporal.ChronoField.MINUTE_OF_HOUR;
import static java.time.temporal.ChronoField.SECOND_OF_MINUTE;

public class Constants {
    public static DateTimeFormatter POIEXT_DATETIME_FORMATTER = new DateTimeFormatterBuilder()
            .parseCaseInsensitive()
            .append(ISO_LOCAL_DATE)
            .appendLiteral(" ")
            .appendValue(HOUR_OF_DAY, 2)
            .appendLiteral(':')
            .appendValue(MINUTE_OF_HOUR, 2)
            .appendLiteral(':')
            .appendValue(SECOND_OF_MINUTE, 2)
             .toFormatter();

    /**
     * 查价浮层页面
     */
    public static String PRICE_TREND_PAGE = "priceTrendPage";
    /**
     * 比价浮层页面
     */
    public static String PRICE_COMPARE_PAGE = "priceComparePage";

    /**
     * 猜喜渠道
     */
    public static final String SUBSIDY_SCENE = "subsidyScene";
    public static final String HIT_GUESS_LIKE_SUBSIDY = "hitGuessLikeSubsidy";

    // 推荐来源，1-ugc推荐语，2-榜单
    public static final int UGC_SOURCE = 1;
    public static final int RANK_LABEL_SOURCE = 2;

    /**
     * 猜喜渠道
     */
    public static final String AD_LX_KEY = "ad_lx";
    public static final String AD_FEEDBACK_KEY = "adFeedback";
    public static final String AD_REQUEST_ID_KEY = "ad_request_id";
    public static final String AD_ICON_KEY = "adicon";
    public static final String AD_TAG_KEY = "adtag";
    public static final String AD_ICON_LENGTH_KEY = "adiconLength";
    public static final String AD_ICON_WIDTH_KEY = "adiconWidth";

    /**
     * 强预订实验号
     */
    public static final String MT_PRE_ORDER_EXP = "MtPreOrderExp";
    public static final String DP_PRE_ORDER_EXP = "DpPreOrderExp";
}
