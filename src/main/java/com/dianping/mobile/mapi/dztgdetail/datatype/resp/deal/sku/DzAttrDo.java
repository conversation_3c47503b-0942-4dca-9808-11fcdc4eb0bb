package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.sku;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

import java.io.Serializable;

@MobileDo(id = 0x7dbe)
public class DzAttrDo implements Serializable {
    @MobileDo.MobileField(key = 0x7ab8)
    private String name;
    @MobileDo.MobileField(key = 0x62b4)
    private String cnName;
    @MobileDo.MobileField(key = 0x97dd)
    private String value;

    @FieldDoc(description = "属性的id值")
    @MobileDo.MobileField(key = 0x86f3)
    private String attrId;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCnName() {
        return cnName;
    }

    public void setCnName(String cnName) {
        this.cnName = cnName;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getAttrId() {
        return attrId;
    }

    public void setAttrId(String attrId) {
        this.attrId = attrId;
    }
}
