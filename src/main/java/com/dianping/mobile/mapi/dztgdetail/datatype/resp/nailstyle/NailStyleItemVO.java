package com.dianping.mobile.mapi.dztgdetail.datatype.resp.nailstyle;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-01-24
 * @desc 美甲款式简要信息
 */
@Setter
@Getter
@TypeDoc(description = "美甲款式简要信息")
@MobileDo(id = 0x6356)
public class NailStyleItemVO implements Serializable {
    @FieldDoc(description = "款式图")
    @MobileDo.MobileField(key = 0x7291)
    private String picUrl;

    @FieldDoc(description = "对应商家款式的contentId")
    @MobileDo.MobileField(key = 0xb231)
    private String itemId;
}