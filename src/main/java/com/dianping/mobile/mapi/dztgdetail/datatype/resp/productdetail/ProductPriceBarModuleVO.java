package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-02-26
 * @see <a href="https://mobile.sankuai.com/studio/model/info/41556">商品详情页价格条模型</a>
 */
@Data
@TypeDoc(description = "商品详情页价格条")
@MobileDo(id = 0x10d6)
public class ProductPriceBarModuleVO extends AbstractModuleVO {
    @Override
    public String getModuleKey() {
        return "";
    }

    @FieldDoc(description = "销售量信息")
    @MobileField(key = 0xc072)
    private DetailSaleVO sale;

    @FieldDoc(description = "价格信息")
    @MobileField(key = 0xb716)
    private DetailPriceVO price;
}