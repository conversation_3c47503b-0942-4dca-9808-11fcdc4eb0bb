package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.cat.Cat;
import com.dianping.piccentercloud.display.api.PictureUrlGenerator;
import com.dianping.piccentercloud.display.api.PictureVisitParams;
import com.dianping.piccentercloud.display.api.enums.PictureVisitPattern;
import com.dianping.piccentercloud.display.api.enums.WaterMark;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class ImgUrlEncryptHelper {

    public static List<String> encryptImgUrl(List<String> urls, int w, int h) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.helper.ImgUrlEncryptHelper.encryptImgUrl(java.util.List,int,int)");

        if (CollectionUtils.isEmpty(urls)) {
            return urls;
        }

        List<String> encrypted = Lists.newArrayList();
        for (String url : urls) {
            encrypted.add(getEncryptedUrl(url, w, h));
        }

        return encrypted;
    }

    public static String encryptImgUrl(String url, int w, int h) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.helper.ImgUrlEncryptHelper.encryptImgUrl(java.lang.String,int,int)");
        if (StringUtils.isEmpty(url)) {
            return url;
        }
        return encryptImgUrl(Collections.singletonList(url), w, h).get(0);
    }

    public static String encryptImgUrlInStr(String str) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.helper.ImgUrlEncryptHelper.encryptImgUrlInStr(java.lang.String)");
        if (StringUtils.isEmpty(str) || !str.contains("img")) {
            return str;
        }

        try {

            Pattern pattern = Pattern.compile("(https|http)://.*jpg[^\"]*", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
            Matcher matcher = pattern.matcher(str);
            StringBuffer sb = new StringBuffer();

            while (matcher.find()) {
                String group = matcher.group();
                String encryptedUrl = getEncryptedUrl(group, 640, 1024);
                matcher.appendReplacement(sb, encryptedUrl);

            }

            matcher.appendTail(sb);
            return sb.toString();

        } catch (Exception e) {
            log.error("encryptImgUrlInStr err,入参={}", str, e);
            return str;
        }
    }

    private static String getEncryptedUrl(String url, int w, int h) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.helper.ImgUrlEncryptHelper.getEncryptedUrl(java.lang.String,int,int)");

        if (StringUtils.isEmpty(url)) {
            return url;
        }

        try {
            String encryptedUrl;
            PictureVisitParams params = buildParam(url, w, h);
            PictureUrlGenerator pictureUrlGenerator = new PictureUrlGenerator(params, PictureVisitPattern.COMPATIBLE);

            if (url.contains("https")) {
                encryptedUrl = pictureUrlGenerator.getPictureURLWithHTTPSProtocol();
            } else if (url.contains("http")) {
                encryptedUrl = pictureUrlGenerator.getFullPictureURL();
            } else {
                encryptedUrl = pictureUrlGenerator.getPictureURLWithoutProtocol();
            }

            return encryptedUrl;

        } catch (Exception e) {
            log.error("img url encrypt err,url={}", url, e);
        }

        return url;

    }

    private static PictureVisitParams buildParam(String url, int w, int h) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.helper.ImgUrlEncryptHelper.buildParam(java.lang.String,int,int)");
        String biz = "pc";
        int scale = 1;
        int crop = 0;

        PictureVisitParams params = new PictureVisitParams(biz, url, scale, crop, w, h, WaterMark.MEITUAN);

        Map<String, String> optionalParams = new HashMap<>();
        optionalParams.put("encryptVenus","1");
        params.setOptionalParams(optionalParams);

        return params;
    }

}