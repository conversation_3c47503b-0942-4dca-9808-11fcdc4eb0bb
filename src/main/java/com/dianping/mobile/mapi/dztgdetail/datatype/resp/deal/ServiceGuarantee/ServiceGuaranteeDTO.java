package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ServiceGuarantee;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.util.List;
import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 16/11/2022
 * @time 11:43
 * 模型描述：服务保障数据类型
 * 移动之家链接: https://mobile.sankuai.com/studio/model/info/29816
 */
@MobileDo(id = 0xcf45)
@Data
public class ServiceGuaranteeDTO implements Serializable {

    /**
     * 服务保障横条内容列表
     */
    @MobileField(key = 0x91f1)
    private List<ServiceGuaranteeBanner> serviceGuaranteeBanners;

    /**
     * 服务保障内容详情列表
     */
    @MobileField(key = 0x67f2)
    private List<ServiceGuaranteeContentDetailDTO> serviceGuaranteeContentDetailDTOS;
}
