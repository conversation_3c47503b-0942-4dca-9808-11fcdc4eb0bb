package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule;

import com.dianping.cat.Cat;
import com.dianping.deal.struct.common.dto.Resp;
import com.dianping.deal.struct.query.api.entity.dto.DealDetailDto;
import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.OptionalSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.BaseDisplayItemVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailDisplayUnitVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailSpecificModuleVO;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class SpecificModuleHandler_1502 implements DealDetailSpecificModuleHandler {

    @Autowired
    private DealGroupWrapper dealGroupWrapper;

    private final long WINDOW_FILM_V2_PRODUCT_CATEGORY_ID = 5200L;

    @Override
    public String identity() {
        return "1502";
    }

    @Override
    public void handle(SpecificModuleCtx ctx) {
        ctx.setResult(buildResult(ctx.getDpDealGroupId()));
    }

    private DealDetailSpecificModuleVO buildResult(Integer dpDealGroupId) {

        List<BaseDisplayItemVO> displayItems = getDisplayItem(dpDealGroupId);

        if (CollectionUtils.isEmpty(displayItems)) {
            return null;
        }

        DealDetailDisplayUnitVO unit = new DealDetailDisplayUnitVO();
        unit.setDisplayItems(displayItems);

        List<DealDetailDisplayUnitVO> units = new ArrayList<>();
        units.add(unit);

        DealDetailSpecificModuleVO result = new DealDetailSpecificModuleVO();
        result.setUnits(units);

        return result;
    }

    private List<BaseDisplayItemVO> getDisplayItem(Integer dpDealGroupId) {
//        Future detailFuture = dealGroupWrapper.preDealDetailInfo(dpDealGroupId);
//        Resp<DealDetailDto> detailDtoResp = dealGroupWrapper.getFutureResult(detailFuture);
//        List<Long> productCategoryList = getProductCategoryList(detailDtoResp);

        List<Integer> dpDealGroupIdWhiteList = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb.window.film.whitelist", Integer.class, new ArrayList<>());

        if (!dpDealGroupIdWhiteList.contains(dpDealGroupId)) {
            return null;
        }

        return buildDisplayItems();
    }

    private List<Long> getProductCategoryList(Resp<DealDetailDto> resp) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule.SpecificModuleHandler_1502.getProductCategoryList(com.dianping.deal.struct.common.dto.Resp)");
        List<Long> productCategoryList = new ArrayList<>();

        if (resp == null || !resp.isSuccess() || resp.getContent() == null) {
            return productCategoryList;
        }

        DealDetailDto content = resp.getContent();

        if (content.getSkuUniStructuredDto() == null) {
            return productCategoryList;
        }

        if(CollectionUtils.isNotEmpty(content.getSkuUniStructuredDto().getMustGroups())) {
            for(MustSkuItemsGroupDto mustGroup : content.getSkuUniStructuredDto().getMustGroups()) {
                List<SkuItemDto> skuItemDtos = mustGroup.getSkuItems();
                if(CollectionUtils.isEmpty(skuItemDtos)) {
                    continue;
                }
                for(SkuItemDto skuItemDto : skuItemDtos) {
                    long productCategoryId = skuItemDto.getProductCategory();
                    productCategoryList.add(productCategoryId);
                }
            }
        }

        if(CollectionUtils.isNotEmpty(content.getSkuUniStructuredDto().getOptionalGroups())) {
            for(OptionalSkuItemsGroupDto optionalGroup : content.getSkuUniStructuredDto().getOptionalGroups()) {
                List<SkuItemDto> skuItemDtos = optionalGroup.getSkuItems();
                if(CollectionUtils.isEmpty(skuItemDtos)) {
                    continue;
                }
                for(SkuItemDto skuItemDto : skuItemDtos) {
                    long productCategoryId = skuItemDto.getProductCategory();
                    productCategoryList.add(productCategoryId);
                }
            }
        }

        return productCategoryList;
    }

    private List<BaseDisplayItemVO> buildDisplayItems() {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule.SpecificModuleHandler_1502.buildDisplayItems()");

        BaseDisplayItemVO item1 = new BaseDisplayItemVO();
        item1.setBgPicUrl("https://p0.meituan.net/travelcube/691b59013e8c16ab4724eae45a61c91679427.png");

        return Lists.newArrayList(item1);
    }
}