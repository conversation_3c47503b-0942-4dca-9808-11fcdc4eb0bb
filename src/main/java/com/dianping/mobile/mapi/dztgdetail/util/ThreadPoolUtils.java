package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.cat.thread.pool.CatExecutorServiceTraceWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ThreadPoolNameEnum;
import com.dianping.pigeon.threadpool.NamedThreadFactory;
import com.maoyan.mtrace.thread.pool.ExecutorServiceTraceWrapper;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024-02-26
 * @desc 线程池工具类
 */
public class ThreadPoolUtils {
    private static final ThreadPoolExecutor DEFAULT_EXECUTOR = new ThreadPoolExecutor(150, 200, 0L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(500), new NamedThreadFactory("defaultExecutor"));

    private static final ThreadPoolExecutor STYLE_EXECUTOR = new ThreadPoolExecutor(150, 200, 0L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(500), new NamedThreadFactory("styleExecutor"));

    private static final ThreadPoolExecutor PRICE_EXECUTOR = new ThreadPoolExecutor(150, 200, 0L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(500), new NamedThreadFactory("priceExecutor"));

    private static final ThreadPoolExecutor PROMOTION_EXECUTOR = new ThreadPoolExecutor(150, 200, 0L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(500), new NamedThreadFactory("promotionExecutor"));

    private static final ThreadPoolExecutor DEAL_SHOP_EXECUTOR = new ThreadPoolExecutor(50, 80, 0L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(500), new NamedThreadFactory("dealShopExecutor"));


    /**
     * 获取线程池对象，保证trace信息在多线程环境下正常传递
     * @param threadPoolNameEnum 线程池名称枚举
     * @return 线程池
     */
    public static CatExecutorServiceTraceWrapper getCatExecutor(ThreadPoolNameEnum threadPoolNameEnum) {
        switch (threadPoolNameEnum) {
            case PRICE_EXECUTOR:
                return new CatExecutorServiceTraceWrapper(new ExecutorServiceTraceWrapper(PRICE_EXECUTOR));
            case STYLE_EXECUTOR:
                return new CatExecutorServiceTraceWrapper(new ExecutorServiceTraceWrapper(STYLE_EXECUTOR));
            case PROMO_EXECUTOR:
                return new CatExecutorServiceTraceWrapper(new ExecutorServiceTraceWrapper(PROMOTION_EXECUTOR));
            case DEAL_SHOP_EXECUTOR:
                return new CatExecutorServiceTraceWrapper(new ExecutorServiceTraceWrapper(DEAL_SHOP_EXECUTOR));
            default:
                return new CatExecutorServiceTraceWrapper(new ExecutorServiceTraceWrapper(DEFAULT_EXECUTOR));
        }
    }
}
