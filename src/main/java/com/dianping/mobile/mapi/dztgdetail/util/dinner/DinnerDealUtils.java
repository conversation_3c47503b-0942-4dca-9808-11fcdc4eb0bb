package com.dianping.mobile.mapi.dztgdetail.util.dinner;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.entity.dinner.DinnerDealGrayConfig;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.NumbersUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-05-26
 * @desc
 */
public class DinnerDealUtils {

    public static boolean isDinnerDeal(Integer categoryId, EnvCtx envCtx) {
        if (NumbersUtils.lessThanAndEqualZero(categoryId)) {
            return false;
        }
        List<Integer> nibCategoryIds = LionConfigUtils.getNibCategoryIds();
        int dealCategoryId = Objects.isNull(categoryId) ? 0 : categoryId;
        if (nibCategoryIds.contains(dealCategoryId)) {
            return false;
        }
        // 进入这里的是到餐的类目团单
        DinnerDealGrayConfig dinnerDealGrayConfig = LionConfigUtils.getDinnerDealGrayConfig();
        if (!dinnerDealGrayConfig.isOpen()) {
            return false;
        }
        if (CollectionUtils.isNotEmpty(dinnerDealGrayConfig.getExcludeClientTypes())
                && dinnerDealGrayConfig.getExcludeClientTypes().contains(envCtx.getDztgClientTypeEnum())) {
            return false;
        }
        long userId = envCtx.getUserId();
        return userId % 100 < dinnerDealGrayConfig.getRatio();
    }
}
