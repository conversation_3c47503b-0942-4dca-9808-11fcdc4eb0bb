package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.cat.Cat;
import com.dianping.deal.common.enums.GpsType;
import com.dianping.deal.detail.enums.PlatformEnum;
import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.deal.shop.dto.BestShopReq;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.QueryCenterProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetcorpwxentrymaterialRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.CorpWxFlowBanner;
import com.dianping.mobile.mapi.dztgdetail.util.DealProductUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.flow.dto.CorpWxFlowMaterialDTO;
import com.sankuai.dz.srcm.flow.dto.FlowEntryWxMaterialRequest;
import com.sankuai.dz.srcm.flow.dto.GeoDTO;
import com.sankuai.dz.srcm.flow.enums.PageLocationType;
import com.sankuai.dz.srcm.flow.service.FlowEntryWxMaterialService;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupDisplayShopBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupShopRelationCheckBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.BaseRequestBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.Future;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.APP_KEY;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.FLOW_ENTRY_WX_MATERIAL_CONFIG;
import static com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum.COST_EFFECTIVE;

@Component
@Slf4j
public class CorpWxEntryMaterialFacade {
    @Autowired
    private FlowEntryWxMaterialService flowEntryWxMaterialService;

    @Autowired
    private PoiShopCategoryWrapper poiShopCategoryWrapper;

    @Autowired
    private MapperWrapper mapperWrapper;

    @Autowired
    private PoiClientWrapper poiClientWrapper;

    @Autowired
    private QueryCenterWrapper queryCenterWrapper;

    @Autowired
    private DealGroupWrapper dealGroupWrapper;

    public CorpWxFlowBanner getCorpWxFlowBanner(GetcorpwxentrymaterialRequest req, EnvCtx ctx) {
        Long bestDpshopId = findbestShopId(req, ctx);
        if (bestDpshopId == null || bestDpshopId <= 0L
                || !Objects.equals(req.getPageResource(), COST_EFFECTIVE.getSource())) {
            return null;
        }
        req.setShopIdForLong(bestDpshopId);
        FlowEntryWxMaterialRequest request = buildRequest(req, ctx);
        if (request == null) {
            return null;
        }
        return convertResponse(request);
    }

    private CorpWxFlowBanner convertResponse(FlowEntryWxMaterialRequest request) {
        RemoteResponse<CorpWxFlowMaterialDTO> entryWxMaterial = null;
        try {
            entryWxMaterial = flowEntryWxMaterialService.getEntryWxMaterial(request);
            CorpWxFlowMaterialDTO corpWxFlowMaterialDTO = entryWxMaterial.getData();
            return Optional.ofNullable(corpWxFlowMaterialDTO).map(this::convertToCorpWxFlowBanner).orElse(null);
        } catch (Exception e) {
            Cat.logError("flowEntryWxMaterialService.error", e);
        }
        return null;
    }

    private CorpWxFlowBanner convertToCorpWxFlowBanner(CorpWxFlowMaterialDTO dto) {
        CorpWxFlowBanner corpWxFlowBanner = new CorpWxFlowBanner();
        corpWxFlowBanner.setButtonText(dto.getButtonText());
        corpWxFlowBanner.setJumpUrl(dto.getJumpUrl());
        corpWxFlowBanner.setIsShowResource(dto.isShowResource());
        corpWxFlowBanner.setPicUrl(dto.getPicUrl());
        corpWxFlowBanner.setSubTitleText(dto.getSubTitleText());
        corpWxFlowBanner.setShowText(dto.getShowText());
        return corpWxFlowBanner;
    }

    public FlowEntryWxMaterialRequest buildRequest(GetcorpwxentrymaterialRequest req, EnvCtx ctx) {
        FlowEntryWxMaterialRequest request = new FlowEntryWxMaterialRequest();
        request.setShopId(getDpShopId(req, ctx));
        Integer backPoiSecondCateGoryId = getBackPoiSecondCateGoryId(req);
        List<Integer> list = Lion.getList(APP_KEY, FLOW_ENTRY_WX_MATERIAL_CONFIG, Integer.class, Lists.newArrayList());
        if (backPoiSecondCateGoryId == null || !list.contains(backPoiSecondCateGoryId)) {
            return null;
        }
        request.setCategoryId(Long.valueOf(backPoiSecondCateGoryId));
        setCityId4Platform(request, req, ctx);
        request.setUserId(ctx.getMtUserId());//mtUserId有问题，需要确认，但不涉及主接口
        request.setPlatform(ctx.isMt() ? PlatformEnum.MEI_TUAN.getType() : PlatformEnum.DIAN_PING.getType());
        request.setPageLocation(PageLocationType.SPECIAL_PRICE_DETAIL_PAGE.getCode());
        request.setOrderId(req.getDealGroupId());
        GeoDTO geoDTO = new GeoDTO();
        geoDTO.setLat(req.getUserLat());
        geoDTO.setLng(req.getUserLng());
        request.setGeoDTO(geoDTO);
        return request;
    }

    private void setCityId4Platform(FlowEntryWxMaterialRequest request, GetcorpwxentrymaterialRequest req, EnvCtx ctx) {
        int cityId = req.getCityId();
        request.setMtCityId(
                ctx.isMt() ? cityId : mapperWrapper.getMtCityByDpCity(mapperWrapper.preMtCityByDpCity(cityId)));
        request.setDpCityId(ctx.isMt() ? mapperWrapper.fetchDpCityByMtCity(cityId) : cityId);
    }

    private Long getDpShopId(GetcorpwxentrymaterialRequest req, EnvCtx ctx) {
        return Optional.ofNullable(req.getShopIdForLong())
                .map(shopIdForLong -> ctx.isMt()
                        ? mapperWrapper.getDpShopIdByMtShopIdLong(mapperWrapper.preDpShopIdByMtShopId(shopIdForLong))
                        : shopIdForLong)
                .orElse(null);
    }

    private Integer getBackPoiSecondCateGoryId(GetcorpwxentrymaterialRequest req) {
        Long shopId = req.getShopIdForLong();
        if (shopId != null && shopId > 0) {
            DpPoiDTO dpPoiDTO = poiClientWrapper.getDpPoiDTO(shopId,
                    Lists.newArrayList("shopId", "backMainCategoryPath", "cityId"));
            if (dpPoiDTO != null) {
                return poiShopCategoryWrapper.getBackSecondMainCategory(dpPoiDTO);
            }
        }
        return null;
    }

    public Long findbestShopId(GetcorpwxentrymaterialRequest req, EnvCtx ctx) {
        Long shopId = req.getShopIdForLong();
        // todo xiangrui 门店判断
        DealGroupDTO dealGroupDTO = getProductDTO(req, ctx);
        List<Long> DpRelatedShops = DealProductUtils.getDpDisplayShopIds(dealGroupDTO);
        Future shopFuture = dealGroupWrapper.preDealGroupBestShop(buildBestShopReqWithoutShopId(req, ctx));
        Long bestDpShopId = null;
        if (shopId != null) {
            if (DealProductUtils.checkShopNoExist(dealGroupDTO, shopId, ctx.isMt())
                    &&!DpRelatedShops.contains(shopId)) {
                // 有来源门店，但是来源门店和团单不绑定，则重新请求，入参不指定门店
                BestShopDTO bestShop = dealGroupWrapper.getFutureResult(shopFuture);
                if (bestShop == null) {
                    return null;
                }
                bestDpShopId = bestShop.getDpShopId();
            } else {
                // 有来源门店，且来源门店和团单绑定，直接使用来源门店id
                bestDpShopId = shopId;
            }
        } else {
            // 没有来源门店id,可以直接使用请求结果
            BestShopDTO bestShop = dealGroupWrapper.getFutureResult(shopFuture);
            if (bestShop == null) {
                return null;
            }
            bestDpShopId = bestShop.getDpShopId();
        }
        return bestDpShopId;
    }

    private BestShopReq buildBestShopReqWithoutShopId(GetcorpwxentrymaterialRequest req, EnvCtx ctx) {
        BestShopReq shopReq = new BestShopReq();
        shopReq.setDealGroupId(Long.valueOf(req.getDealGroupId()));
        shopReq.setCityId(req.getCityId());
        shopReq.setLat(req.getUserLat());
        shopReq.setLng(req.getUserLng());
        shopReq.setGpsType(GpsType.GCJ02.getType());
        shopReq.setClientType(ctx.getClientType());
        return shopReq;
    }

    private List<Long> getDpDisplayShopIds(GetcorpwxentrymaterialRequest req, EnvCtx ctx) {
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = buildQueryCenterRequest(req, ctx);
        try {
            DealGroupDTO dealGroupDTO = queryCenterWrapper.getDealGroupDTO(queryByDealGroupIdRequest);
            if (dealGroupDTO == null || dealGroupDTO.getDisplayShopInfo() == null
                    || dealGroupDTO.getDisplayShopInfo().getDpDisplayShopIds() == null) {
                return Collections.emptyList();
            }
            return dealGroupDTO.getDisplayShopInfo().getDpDisplayShopIds();
        } catch (TException e) {
            log.error("QueryCenterWrapper.getDealGroupDTO error!", e);
        }
        return Collections.emptyList();
    }

    private DealGroupDTO getProductDTO(GetcorpwxentrymaterialRequest req, EnvCtx ctx) {
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = buildQueryCenterRequest(req, ctx);
        try {
            return queryCenterWrapper.getDealGroupDTO(queryByDealGroupIdRequest);
        } catch (TException e) {
            log.error("QueryCenterWrapper.getDealGroupDTO error!", e);
        }
        return null;
    }

    private QueryByDealGroupIdRequest buildQueryCenterRequest(GetcorpwxentrymaterialRequest req, EnvCtx ctx) {
        IdTypeEnum idTypeEnum = ctx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP;
        Map<Long, Set<Long>> deal2shop = Maps.newHashMap();
        deal2shop.put(NumberUtils.toLong(req.getDealGroupId()), Sets.newHashSet(req.getShopIdForLong()));
        BaseRequestBuilder<QueryByDealGroupIdRequest> builder = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet(Long.valueOf(req.getDealGroupId())),
                        ctx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP)
                .displayShop(DealGroupDisplayShopBuilder.builder().dpDisplayShopIds());
        if (QueryCenterProcessor.openQueryCenterCheckDealGroupShopRelation()) {
            builder.checkDealGroupShopRelation(DealGroupShopRelationCheckBuilder.builder().shopIdType(idTypeEnum.getCode()).checkDisplayShop(deal2shop));
        }
        return builder.build();
    }

}
