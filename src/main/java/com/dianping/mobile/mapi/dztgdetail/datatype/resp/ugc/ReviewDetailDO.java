package com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/12/13.
 */
@TypeDoc(description = "评价详细信息")
@MobileDo(id = 0x3f60)
@Data
public class ReviewDetailDO implements Serializable {

    @FieldDoc(description = "评论ID")
    @MobileField(key = 0x7afd)
    private String reviewId;

    @FieldDoc(description = "评价得分")
    @MobileField(key = 0x203a)
    private String scoreText;

    @FieldDoc(description = "商户点评总数")
    @MobileField(key = 0x56c5)
    private int reviewCount;

    @FieldDoc(description = "浏览总数")
    @MobileField(key = 0xa5db)
    private int viewCount;

    @FieldDoc(description = "点赞总数")
    @MobileField(key = 0x230a)
    private int likeCount;

    @FieldDoc(description = "该条评论总数")
    @MobileField(key = 0xb82f)
    private int commentCount;

    @FieldDoc(description = "打分星级")
    @MobileField(key = 0x664)
    private int star;

    @FieldDoc(description = "评价类型标签 eg.消费后点评")
    @MobileField(key = 0xec40)
    private String reviewTag;

    @FieldDoc(description = "评价内容")
    @MobileField(key = 0xcce)
    private String content;

    @FieldDoc(description = "优质点评")
    @MobileField(key = 0xa987)
    private String honor;

    @FieldDoc(description = "评论时间")
    @MobileField(key = 0xdcbe)
    private String reviewTime;

    @FieldDoc(description = "评论打分文案 eg.打分")
    @MobileField(key = 0xf916)
    private String actionNote;

    @FieldDoc(description = "该条评论具体链接")
    @MobileField(key = 0x8d3b)
    private String detailUrl;

    @FieldDoc(description = "用户信息")
    @MobileField(key = 0xbe22)
    private ReviewUserModel reviewUserModel;

    @FieldDoc(description = "评论图片列表")
    @MobileField(key = 0x328b)
    private List<ReviewPicDTO> reviewPicList;

    @FieldDoc(description = "评价标签")
    @MobileField(key = 0x9660)
    private List<ReviewExtTagDO> reviewExtTags;

    @MobileDo.MobileField(key = 0x49)
    private int reviewType;

    @FieldDoc(description = "门店人均")
    @MobileField(key = 0xb716)
    private String price;

}
