package com.dianping.mobile.mapi.dztgdetail.datatype.resp.module;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;

@MobileDo(id = 0xf2c6)
@Data
public class BuyBarShareInfo implements Serializable {
    /**
     * 跳链url
     */
    @MobileField(key = 0x774e)
    private String jumpUrl;

    /**
     * 标题
     */
    @MobileField(key = 0x24cc)
    private String title;

    /**
     * 背景图片
     */
    @MobileField(key = 0xe5b6)
    private String background;

    /**
     * 商品图片
     */
    @MobileField(key = 0xe1e)
    private String dealImage;

    /**
     * 分享模板id
     */
    @MobileField(key = 0xd28c)
    private Integer templateId;
}