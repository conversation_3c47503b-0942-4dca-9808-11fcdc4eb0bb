package com.dianping.mobile.mapi.dztgdetail.button.beauty;

import com.dianping.mobile.mapi.dztgdetail.button.joy.CouponBarHelper;
import com.dianping.mobile.mapi.dztgdetail.button.joy.NewNormalButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.SaleStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtnIcon;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.PriceHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.PromoHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

public class BeautyNormalButtonBuilder extends NewNormalButtonBuilder {

    private static final Map<Integer, String> COUPON_BUTTON_SUFFIX_MAP = ImmutableMap.of(
            BuyBtnTypeEnum.TIMES_CARD.getCode(), "购买1次",
            BuyBtnTypeEnum.PINTUAN.getCode(), "单独购买",
            BuyBtnTypeEnum.MEMBER_CARD.getCode(), "抢购"
    );

    @Override
    public void afterBuild(DealCtx context, DealBuyBtn button) {
        buildTitle(context, button);
        buildSaleStatus(context, button);
        buildBtnEnable(context, button);
        buildPromoTag(context, button);
        buildCouponAB(context, button);

        if (context.getPreButton() != null) {
            DealBuyHelper.convertToDoubleButtonStyle(context, button);
        }

        if (DealBuyHelper.isCouponBar(context)) {
            button.setBtnTitle(CouponBarHelper.getCouponBtnTitle(context));
        }
    }

    private void buildBtnEnable(DealCtx context, DealBuyBtn button) {
        if(StringUtils.isNotBlank(context.getSaleStatus())){
            if(context.getSaleStatus().equals(SaleStatusEnum.SNAP_UP_NOW.saleStatusName)){
                button.setBtnEnable(true);
            }else{
                button.setBtnEnable(false);
            }
        }
    }

    private void buildSaleStatus(DealCtx context, DealBuyBtn button) {
        if(StringUtils.isNotBlank(context.getSaleStatus())){
            button.setSaleStatus(context.getSaleStatus());
        }
    }

    private void buildPromoTag(DealCtx context, DealBuyBtn button) {

        PromoDTO couponPromo = PromoHelper.getCouponPromo(context);
        PromoDTO normalPromo = getNormalPromo(context);
        PriceDisplayDTO normalPrice = PriceHelper.getNormalPrice(context);
        String couponTag = couponPromo == null ? null : couponPromo.getTag();
        String promoTag = normalPromo == null ? null : normalPromo.getTag();
        if (StringUtils.isNotBlank(Optional.ofNullable(normalPrice).map(PriceDisplayDTO::getPromoTag).orElse(""))) {
            return;
        }
        List<DealBuyBtnIcon> btnIcons = Lists.newArrayList();
        if (context.getPreButton() == null) {
            // 单button展示两个标签，立减优先
            if (StringUtils.isNotEmpty(promoTag)) {
                button.setBtnTag(promoTag);
                btnIcons.add(DealBuyHelper.getPromoIcon(context, promoTag));
            }
            if (StringUtils.isNotEmpty(couponTag)) {
                if (StringUtils.isBlank(button.getBtnTag())) {
                    button.setBtnTag(couponTag);
                }
                btnIcons.add(DealBuyHelper.getPromoIcon(context, couponTag));
            }
        } else {
            // 双button只展示一个标签
            String tag = StringUtils.isNotEmpty(couponTag) ? couponTag : promoTag;
            button.setBtnTag(tag);
            btnIcons.add(DealBuyHelper.getPromoIcon(context, tag));
        }
        button.setBtnIcons(btnIcons);
    }

    private void buildTitle(DealCtx context, DealBuyBtn button) {
        PromoDTO couponPromo = PromoHelper.getCouponPromo(context);
        DealBuyBtn preButton = context.getPreButton();
        boolean needPreOrder = DealCtxHelper.isPreOrderDeal(context);

        String buttonTitle = null;
        if (couponPromo == null) {
            if (preButton != null) {
                buttonTitle = BUTTON_NAME_MAP.get(preButton.getDetailBuyType());
            }
            if (StringUtils.isBlank(buttonTitle)) {
                buttonTitle = DEFAULT_BUTTON_NAME;
            }
            //百度地图小程序抢购按钮逻辑固定为「XX抢购」
            if (Objects.equals(context.getEnvCtx().getDztgClientTypeEnum(), DztgClientTypeEnum.DIANPING_BAIDUMAP_MINIAPP)) {
                List<PromoDTO> couponPromos = PromoHelper.getCouponPromos(context);
                if (CollectionUtils.isNotEmpty(couponPromos)) {
                    //任何一张券可用就展示用券抢购
                    buttonTitle = (couponPromos.stream().anyMatch(item -> !item.isCanAssign()) ?  HAD_COUPON_PREFIX : NO_COUPON_PREFIX) + BUY;
                }
            }
            // 无优惠预订团单按钮设置为「立即预订」
            if (needPreOrder) {
                buttonTitle = DEFAULT_PRE_ORDER_BUTTON_NAME;
            }
        } else {
            boolean canAssign = couponPromo.isCanAssign();
            String buttonPrefix = canAssign ? NO_COUPON_PREFIX : HAD_COUPON_PREFIX;
            String buttonSuffix = null;
            if (preButton != null) {
                buttonSuffix = COUPON_BUTTON_SUFFIX_MAP.get(preButton.getDetailBuyType());
            }
            //百度地图小程序抢购按钮逻辑固定为「XX抢购」
            if (StringUtils.isBlank(buttonSuffix) || Objects.equals(context.getEnvCtx().getDztgClientTypeEnum(), DztgClientTypeEnum.DIANPING_BAIDUMAP_MINIAPP)) {
                buttonSuffix = BUY;
            }

            buttonTitle = buttonPrefix + buttonSuffix;
            // 优惠预订团单按钮设置为「优惠订」
            if (needPreOrder) {
                buttonTitle = PROMO_PRE_ORDER_BUTTON_NAME;
            }
        }

        if(StringUtils.isNotBlank(context.getSaleStatus())){
            button.setBtnTitle(getSaleStatusButtonTitle(context));
        }else{
            button.setBtnTitle(buttonTitle);
        }
    }

    private PromoDTO getNormalPromo(DealCtx context) {
        PriceDisplayDTO normalPrice = PriceHelper.getNormalPrice(context);
        if (CollectionUtils
                .isEmpty(Optional.ofNullable(normalPrice).map(PriceDisplayDTO::getUsedPromos).orElse(null))) {
            return null;
        }
        for (PromoDTO morePromo : normalPrice.getUsedPromos()) {
            if (morePromo.getIdentity().getPromoType() == PromoTypeEnum.NORMAL_PROMO.getType()) {
                return morePromo;
            }
        }
        return null;
    }


}
