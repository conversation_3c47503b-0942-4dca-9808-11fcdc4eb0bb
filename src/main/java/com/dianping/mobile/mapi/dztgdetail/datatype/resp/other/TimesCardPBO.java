package com.dianping.mobile.mapi.dztgdetail.datatype.resp.other;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ComBtn;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;

@TypeDoc(description = "到综团单次卡数据模型")
@MobileDo(id = 0x2147)
public class TimesCardPBO implements Serializable {

    public TimesCardPBO(String productId) {
        this.productId = productId;
    }

    //    @FieldDoc(description = "次数描述")
//    @MobileField(key = 0xfc72)
//    private String timesDesc;
//
//    @FieldDoc(description = "单次价格")
//    @MobileField(key = 0x8214)
//    private double perPrice;
//
//    @FieldDoc(description = "次卡优惠信息")
//    @MobileField(key = 0x7b99)
//    private String promoDesc;
//
//    @FieldDoc(description = "按钮")
//    @MobileField(key = 0x63d)
//    private ComBtn btn;

    @FieldDoc(description = "商品ID：前端用于打点")
    @MobileField(key = 0x4e8a)
    private String productId;

//    @FieldDoc(description = "背景图片")
//    @MobileField(key = 0x4f81)
//    private String backgroundUrl;
//
//    @FieldDoc(description = "跳转地址")
//    @MobileField(key = 0x4709)
//    private String redirectUrl;
//
//    @FieldDoc(description = "次数")
//    @MobileField(key = 0x1d2)
//    private int times;
//
//    public String getTimesDesc() {
//        return timesDesc;
//    }
//
//    public void setTimesDesc(String timesDesc) {
//        this.timesDesc = timesDesc;
//    }
//
//    public double getPerPrice() {
//        return perPrice;
//    }
//
//    public void setPerPrice(double perPrice) {
//        this.perPrice = perPrice;
//    }
//
//    public String getPromoDesc() {
//        return promoDesc;
//    }
//
//    public void setPromoDesc(String promoDesc) {
//        this.promoDesc = promoDesc;
//    }
//
//    public ComBtn getBtn() {
//        return btn;
//    }
//
//    public void setBtn(ComBtn btn) {
//        this.btn = btn;
//    }
//
    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }
//
//    public String getBackgroundUrl() {
//        return backgroundUrl;
//    }
//
//    public void setBackgroundUrl(String backgroundUrl) {
//        this.backgroundUrl = backgroundUrl;
//    }
//
//    public String getRedirectUrl() {
//        return redirectUrl;
//    }
//
//    public void setRedirectUrl(String redirectUrl) {
//        this.redirectUrl = redirectUrl;
//    }
//
//    public int getTimes() {
//        return times;
//    }
//
//    public void setTimes(int times) {
//        this.times = times;
//    }
}
