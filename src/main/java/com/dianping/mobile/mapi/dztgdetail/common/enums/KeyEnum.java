package com.dianping.mobile.mapi.dztgdetail.common.enums;

import com.dianping.mobile.mapi.dztgdetail.common.MttgVersion;
import com.google.common.collect.Lists;

import java.util.ArrayList;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/8/17.
 */
public enum KeyEnum {
    BEAUTY("beauty"),
    BEAUTY_DEFAULT("beauty_default"),
    KTV("ktv"),
    DEAFAULT("default"),
    FLOWER("flower"),
    CHIL<PERSON>EN("children"),
    CHILDREN_PHOTOEDUTG("children_photoedutg"),
    MEDICINE("medicine"),
    ENTERTAINMENT("entertainment"),
    JOY_MASSAGE("joy_massage"),
    WEDDING("wedding");
    static ArrayList<KeyEnum> versionOld = Lists.newArrayList(BEAUTY, KTV, DEAFAULT);
    static ArrayList<KeyEnum> version7_2 = Lists.newArrayList(FLOWER, CHILDREN, CH<PERSON><PERSON>EN_PHOTOEDUTG);
    static ArrayList<KeyEnum> version7_3 = Lists.newArrayList(MEDICINE);
    static ArrayList<KeyEnum> version7_4 = Lists.newArrayList(WEDDING, ENTERTAINMENT);
    static ArrayList<KeyEnum> version7_5 = Lists.newArrayList(BEAUTY_DEFAULT);

    static {
        version7_2.addAll(versionOld);
        version7_3.addAll(version7_2);
        version7_4.addAll(version7_3);
        version7_5.addAll(version7_4);
    }

    private String channel;

    public static KeyEnum findBgEnum(String channel, MttgVersion version, boolean isAndroid) {
        KeyEnum keyEnum = KeyEnum.DEAFAULT;
        ArrayList<KeyEnum> enumList;

        if (version.compareTo(MttgVersion.V7_5) >= 0) {
            enumList = version7_5;
        } else if (version.compareTo(MttgVersion.V7_4) >= 0) {
            enumList = version7_4;
        } else if (version.compareTo(MttgVersion.V7_3) >= 0) {
            enumList = version7_3;
        } else if (version.compareTo(MttgVersion.V7_2) >= 0) {
            enumList = version7_2;
        } else {
            enumList = versionOld;
        }

        for (KeyEnum key : enumList) {
            if (key.getChannel().equals(channel)) {
                keyEnum = key;
                break;
            }
        }

        if (keyEnum.equals(BEAUTY) && version.compareTo(MttgVersion.V7_5) >= 0) {
            keyEnum = BEAUTY_DEFAULT;
            if (version.compareTo(MttgVersion.V7_5) >= 0 && version.compareTo(MttgVersion.V7_6) < 0 && isAndroid) {
                keyEnum = BEAUTY;
            }
        }

        return keyEnum;
    }

    KeyEnum(String channel) {
        this.channel = channel;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }
}