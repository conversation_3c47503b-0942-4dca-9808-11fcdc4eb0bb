package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.basic;

import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperCacheWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2024/3/25
 * @since mapi-dztgdetail-web
 */
public class SVIPMapperCacheProcessor extends AbsDealProcessor {

    @Autowired
    private MapperCacheWrapper mapperCacheWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        return true;
    }

    /**
     * （神会员需求）点评app环境IOS端的用户定位城市传的是美团的，注意这个是大坑，具体参考文档https://km.sankuai.com/collabpage/**********
     */
    public static boolean isSpecialGpsCityId(int clientType) {
        return ClientTypeEnum.isDpMainApp(clientType) && ClientTypeEnum.isIos(clientType);
    }

    @Override
    public void prepare(DealCtx ctx) {
        if (isSpecialGpsCityId(ctx.getEnvCtx().getClientType())) {
            int mtGpsCityId = ctx.getGpsCityId();
            int dpGpsCityId = mapperCacheWrapper.fetchDpCityId(mtGpsCityId);
            ctx.setGpsCityId(dpGpsCityId);
            ctx.setDpGpsCityId(dpGpsCityId);
            ctx.setMtGpsCityId(mtGpsCityId);
        } else {
            if (ctx.isMt()) {
                int mtGpsCityId = ctx.getGpsCityId();
                int dpGpsCityId = mapperCacheWrapper.fetchDpCityId(mtGpsCityId);
                ctx.setDpGpsCityId(dpGpsCityId);
                ctx.setMtGpsCityId(mtGpsCityId);
            } else {
                int dpGpsCityId = ctx.getGpsCityId();
                int mtGpsCityId = mapperCacheWrapper.fetchMtCityId(dpGpsCityId);
                ctx.setDpGpsCityId(dpGpsCityId);
                ctx.setMtGpsCityId(mtGpsCityId);
            }
        }
    }

    @Override
    public void process(DealCtx ctx) {

    }

}
