package com.dianping.mobile.mapi.dztgdetail.action.h5;

import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetcorpwxentrymaterialRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.PriceExplainContentRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ImageTextDetailPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedDeals;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.PriceExplainContentDTO;
import com.dianping.mobile.mapi.dztgdetail.facade.PriceExplainContentFacade;
import com.meituan.servicecatalog.api.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@InterfaceDoc(
        displayName = "自定义价格说明模块H5页面", type = "restful", description = "自定义价格说明模块",
        scenarios = "该接口仅适用于h5的团购详情页", host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "zhangyongming"
)
@Controller("general/platform/dztgdetail/getpriceexplaincontent.json")
@Action(url = "getpriceexplaincontent.json", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class PriceExplainContentH5Action extends AbsAction<PriceExplainContentRequest> {

    @Autowired
    private PriceExplainContentFacade priceExplainContentFacade;

    @Override
    protected IMobileResponse validate(PriceExplainContentRequest request, IMobileContext context) {
        if (!isRequestValid(request)) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    private boolean isRequestValid(PriceExplainContentRequest request) {
        return Objects.nonNull(request.getDpDealGroupId())||
                Objects.nonNull(request.getDpLongShopId());
    }

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "getpriceexplaincontent.json",
            displayName = "自定义价格说明H5页面",
            description = "自定义价格说明H5页面",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "getpriceexplaincontent.json",
                            type = PriceExplainContentRequest.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "result", description = "自定义价格说明H5页面", type = PriceExplainContentDTO.class)},
            restExampleUrl = "https://mapi.51ping.com/general/platform/dztgdetail/getpriceexplaincontent.json?dealgroupid=200153707",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    ),
            }
    )
    @Override
    protected IMobileResponse execute(PriceExplainContentRequest request, IMobileContext context) {
        EnvCtx envCtx = initEnvCtx(context);
        try {
            PriceExplainContentDTO result = priceExplainContentFacade.getPriceExplainContent(request, envCtx);
            return new CommonMobileResponse(result);
        } catch (Exception e) {
            logger.error("getpriceexplaincontent.json error", e);
            return Resps.SYSTEM_ERROR;
        }
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return Collections.emptyList();
    }
}
