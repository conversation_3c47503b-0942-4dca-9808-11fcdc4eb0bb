package com.dianping.mobile.mapi.dztgdetail.button;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;

public class ResvButtonBuilder extends AbstractButtonBuilder{
    @Override
    protected void doBuild(DealCtx context, ButtonBuilderChain chain) {
        if (context.getIsCanResv()){
            DealBuyBtn resvButton = DealBuyHelper.getResvButton(context);
            context.addButton(resvButton);
            chain.interrupt();
            return;
        }
        chain.build(context);
    }
}
