package com.dianping.mobile.mapi.dztgdetail.helper;

public class LocationHelper {

    private static double MIN_LATITUDE = Double.valueOf("-90.0000");
    private static double MAX_LATITUDE = Double.valueOf("90.0000");
    private static double MIN_LONGITUDE = Double.valueOf("-180.0000");
    private static double MAX_LONGITUDE = Double.valueOf("180.0000");
    private static final double ZERO = 0;

    public static boolean isValidPoint(double lat, double lng) {
        return (Double.compare(lat, ZERO) != 0 || Double.compare(lng, ZERO) != 0) &&
                isValidLatitude(lat) && isValidLongitude(lng);
    }

    public static boolean isValidLatitude(double latitude) {
        return Double.compare(latitude, MIN_LATITUDE) >= 0 && Double.compare(latitude, MAX_LATITUDE) <= 0;
    }

    public static boolean isValidLongitude(double longitude) {
        return Double.compare(longitude, MIN_LONGITUDE) >= 0 && Double.compare(longitude, MAX_LONGITUDE) <= 0;
    }
}
