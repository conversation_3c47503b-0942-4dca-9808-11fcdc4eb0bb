package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.BuyMoreSaveMoreReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.CollaborativeRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseGreyReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealFlashReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealIMReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealImageTextDetailReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealNoticeLayerReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealPromoModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DrivingPoiReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DzRepairCareModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DzdealbundinfoReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageFilterRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GoodReviewReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedDealsReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedRecommendReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.SaleRankingRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.ServiceguaranteequeryRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.SpecificModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedActivityModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedGetCouponReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedModuleExtraReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedMoreDealsReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedShopReviewReq;
import com.dianping.mobile.mapi.dztgdetail.rcf.api.req.DealNativeSnapshotReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * <AUTHOR> , 2025/5/7
 */
@Slf4j
public class IdUpgradeUtils {

    public static void processProductIdForUnifiedShopReviewReq(UnifiedShopReviewReq request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealGroupId, request::getDealGroupId, request::setStringDealGroupId, request::setDealGroupId, name);
    }

    public static void processProductIdForRelatedModuleReq(RelatedModuleReq request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealGroupId, request::getDealGroupId, request::setStringDealGroupId, request::setDealGroupId, name);
    }

    public static void processProductIdForDealBaseRequest(DealBaseRequest request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealId, request::getDealId, request::setStringDealId, request::setDealId, name);
    }

    public static void processProductIdForBuyMoreSaveMoreReq(BuyMoreSaveMoreReq request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealGroupId, request::getDealGroupId, request::setStringDealGroupId, request::setDealGroupId, name);
    }

    public static void processProductIdForUnifiedMoreDealsReq(UnifiedMoreDealsReq request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealGroupId, request::getDealGroupId, request::setStringDealGroupId, request::setDealGroupId, name);
    }

    public static void processProductIdForDealPromoModuleReq(DealPromoModuleReq request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealGroupId, request::getDealGroupId, request::setStringDealGroupId, request::setDealGroupId, name);
    }

    public static void processProductIdForDealBaseReq(DealBaseReq request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealGroupId, request::getDealgroupid, request::setStringDealGroupId, request::setDealgroupid, name);
    }

    public static void processProductIdForDealBaseGreyReq(DealBaseGreyReq request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealGroupId, request::getDealgroupid, request::setStringDealGroupId, request::setDealgroupid, name);
    }

    public static void processProductIdForUnifiedGetCouponReq(UnifiedGetCouponReq request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealGroupId, request::getDealGroupId, request::setStringDealGroupId, request::setDealGroupId, name);
    }

    public static void processProductIdForDzRepairCareModuleReq(DzRepairCareModuleReq request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealGroupId, request::getDealgroupid, request::setStringDealGroupId, request::setDealgroupid, name);
    }

    public static void processProductIdForSaleRankingRequest(SaleRankingRequest request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealGroupId, request::getDealgroupid, request::setStringDealGroupId, request::setDealgroupid, name);
    }

    public static void processProductIdForDealNativeSnapshotReq(DealNativeSnapshotReq request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealGroupId, request::getDealGroupId, request::setStringDealGroupId, request::setDealGroupId, name);
    }

    public static void processProductIdForDealIMReq(DealIMReq request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealGroupId, request::getDealgroupid, request::setStringDealGroupId, request::setDealgroupid, name);
    }

    public static void processProductIdForRelatedRecommendReq(RelatedRecommendReq request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealGroupId, request::getDealGroupId, request::setStringDealGroupId, request::setDealGroupId, name);
    }

    public static void processProductIdForRelatedDealsReq(RelatedDealsReq request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealGroupId, request::getDealGroupId, request::setStringDealGroupId, request::setDealGroupId, name);
    }

    public static void processProductIdForDealNoticeLayerReq(DealNoticeLayerReq request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealGroupId, request::getDealgroupid, request::setStringDealGroupId, request::setDealgroupid, name);
    }

    public static void processProductIdForUnifiedModuleExtraReq(UnifiedModuleExtraReq request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealGroupId, request::getDealGroupId, request::setStringDealGroupId, request::setDealGroupId, name);
    }

    public static void processProductIdForGetImmersiveImageFilterRequest(GetImmersiveImageFilterRequest request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealGroupId, request::getDealGroupId, request::setStringDealGroupId, request::setDealGroupId, name);
    }

    public static void processProductIdForCollaborativeRequest(CollaborativeRequest request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealId, request::getDealId, request::setStringDealId, request::setDealId, name);
    }

    public static void processProductIdForDzdealbundinfoReq(DzdealbundinfoReq request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealGroupId, request::getDealgroupid, request::setStringDealGroupId, request::setDealgroupid, name);
    }

    public static void processProductIdForDealFlashReq(DealFlashReq request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealGroupId, request::getDealgroupid, request::setStringDealGroupId, request::setDealgroupid, name);
    }

    public static void processProductIdForGetImmersiveImageRequest(GetImmersiveImageRequest request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealGroupId, request::getDealGroupId, request::setStringDealGroupId, request::setDealGroupId, name);
    }

    public static void processProductIdForDealImageTextDetailReq(DealImageTextDetailReq request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealGroupId, request::getDealgroupid, request::setStringDealGroupId, request::setDealgroupid, name);
    }

    public static void processProductIdForServiceguaranteequeryRequest(ServiceguaranteequeryRequest request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealgroupId, request::getDealgroupId, request::setStringDealgroupId, request::setDealgroupId, name);
    }

    public static void processProductIdForGoodReviewReq(GoodReviewReq request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealGroupId, request::getDealgroupid, request::setStringDealGroupId, request::setDealgroupid, name);
    }

    public static void processProductIdForUnifiedActivityModuleReq(UnifiedActivityModuleReq request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealGroupId, request::getDealGroupId, request::setStringDealGroupId, request::setDealGroupId, name);
    }

    public static void processProductIdForSpecificModuleReq(SpecificModuleReq request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealGroupId, request::getDealgroupid, request::setStringDealGroupId, request::setDealgroupid, name);
    }

    public static void processProductIdForDrivingPoiReq(DrivingPoiReq request, String name) {
        if (request == null) {
            return;
        }
        processProductId(request::getStringDealGroupId, request::getDealGroupId, request::setStringDealGroupId, request::setDealGroupId, name);
    }

    private static void processProductId(Supplier<String> strGetter, Supplier<Integer> intGetter,
                                         Consumer<String> strSetter, Consumer<Integer> intSetter,
                                         String name) {
        try {
            // 入口逻辑兼容
            if (intGetter.get() == null && StringUtils.isNumeric(strGetter.get())) {
                long productId = Long.parseLong(strGetter.get());
                if (productId <= Integer.MAX_VALUE) {
                    intSetter.accept((int) productId);
                }
            } else if (intGetter.get() != null && StringUtils.isBlank(strGetter.get())) {
                strSetter.accept(intGetter.get() + "");
                //对未完成改造的客户端做打点，便于跟进进度
                Cat.logEvent("ProductIdNotUpgrated", name);
            }
        } catch (Exception e) {
            log.error("processProductId error, name={}", name, e);
        }
    }
}
