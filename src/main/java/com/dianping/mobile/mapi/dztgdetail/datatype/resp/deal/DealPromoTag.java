package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/5/22 20:27
 */
@Data
@MobileDo(id = 0xae9f38b7)
public class DealPromoTag implements Serializable {

    @FieldDoc(description = "标签（名称 or 图片链接）")
    @MobileDo.MobileField(key = 0xbf9b)
    private String tag;

    @FieldDoc(description = "标签类型：1.文字，2.图片")
    @MobileDo.MobileField(key = 0x8f0c)
    private Integer type;
}
