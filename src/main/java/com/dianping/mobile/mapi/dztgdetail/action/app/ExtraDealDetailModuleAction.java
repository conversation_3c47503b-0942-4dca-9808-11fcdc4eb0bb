package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ShopTagWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.ExtraDealDetailModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedDeals;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.CorpWxFlowBanner;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.ExtraDealDetailModuleResponse;
import com.dianping.mobile.mapi.dztgdetail.facade.ExtraDealDetailModuleFacade;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;
import java.util.Objects;

@InterfaceDoc(displayName = "额外团详模块信息",
        type = "restful,mapi",
        description = "展示安心配镜模块key-value",
        scenarios = "该接口仅适用于双平台APP站点的团购详情页搭售模块",
        host = "http://mapi.dianping.com/general/platform/getextradealdetailmodule/",
        authors = "wb_zhangyongming02"
)
@Controller("getextradealdetailmodule.bin")
@Action(url = "general/platform/dztgdetail/getextradealdetailmodule.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class ExtraDealDetailModuleAction extends AbsAction<ExtraDealDetailModuleReq> {
    @Autowired
    ExtraDealDetailModuleFacade extraDealDetailModuleFacade;

    @Override
    protected IMobileResponse validate(ExtraDealDetailModuleReq request, IMobileContext context) {
        if (!isRequestValid(request)) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    private boolean isRequestValid(ExtraDealDetailModuleReq request) {
        return Objects.nonNull(request.getDealGroupId());
    }

    @MethodDoc(
            requestMethods = HttpMethod.GET, urls = "getextradealdetailmodule.bin", displayName = "查询额外团详模块信息",
            description = "查询额外团详模块信息",
            parameters = {
                    @ParamDoc(
                            name = "request", description = "getextradealdetailmodule.bin请求参数",
                            type = ExtraDealDetailModuleReq.class, paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "iMobileContext", description = "mapi-shell协议上下文", type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )},
            responseParams = {@ParamDoc(name = "result", description = "团购picasso数据", type = RelatedDeals.class)},
            // restExampleUrl = "",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {@ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "公开数据无须鉴权"),}
    )
    @Override
    @CryptMethod
    protected IMobileResponse execute(ExtraDealDetailModuleReq request, IMobileContext context) {
        EnvCtx envCtx = initEnvCtx(context);
        try {
            ExtraDealDetailModuleResponse result = extraDealDetailModuleFacade.getExtraDealDetailModuleResponse(request, envCtx);
            return new CommonMobileResponse(result);
        } catch (Exception e) {
            logger.error("getextradealdetailmodule.bin error", e);
            return Resps.SYSTEM_ERROR;
        }
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}