package com.dianping.mobile.mapi.dztgdetail.common.constants;

import java.util.Arrays;
import java.util.List;

public class FitnessCrossConstants {

    /**
     * 点评：默认按钮跳转链接，充值页
     */
    public static final String DP_RECHARGE_BTN_JUMP_URL = "dianping://mrn?mrn_biz=gcbu&mrn_entry=mrn-play-fitness-recharge&mrn_component=fitness-recharge&entry_source=2&shop_id=%s&deal_id=%s";

    /**
     * 美团：默认按钮跳转链接，充值页
     */
    public static final String MT_RECHARGE_BTN_JUMP_URL = "imeituan://www.meituan.com/mrn?mrn_biz=gcbu&mrn_entry=mrn-play-fitness-recharge&mrn_component=fitness-recharge&entry_source=2&shop_id=%s&deal_id=%s";

    /**
     * 按钮横幅引导文案
     */
    public static final String BUY_BANNER_LEAD_TEXT = "去了解";

    /**
     * 按钮横幅引导文案颜色
     */
    public static final String BUY_BANNER_LEAD_TEXT_COLOR = "#FF4B10";

    /**
     * 按钮横幅背景色
     */
    public static final List<String> BUY_BANNER_BACKGROUND_COLORS = Arrays.asList("#FFEFE5", "#FFEFE5");

    /**
     * 按钮横幅类型
     */
    public static final int BUY_BANNER_TYPE = 1;

    /**
     * 按钮文案
     */
    public static final String BUY_BUTTON_TITLE = "立即兑换";

    /**
     * 按钮tag
     */
    public static final String BUY_BUTTON_TAG = "使用1张健身通兑换券";

    /**
     * 按钮副标题
     */
    public static final String BUY_BUTTON_SUB_TITLE = "使用1张健身通兑换券";

    /**
     * 按钮tag标题
     */
    public static final String BUY_BUTTON_ICON_TITLE = "使用1张健身通兑换券";

    /**
     * 按钮tag风格
     */
    public static final String BUY_BUTTON_ICON_STYLE = "#FFFFFF00";

    /**
     * 按钮tag类型
     */
    public static final int BUY_BUTTON_ICON_TYPE = 1;

    /**
     * 按钮tag文案颜色
     */
    public static final String BUY_BUTTON_ICON_TITLE_COLOR = "#FFFFFF";

    /**
     * 按钮tag背景颜色
     */
    public static final String BUY_BUTTON_ICON_TITLE_BACKGROUND_COLOR = "#FFFFFF00";

    /**
     * 按钮tag边框颜色
     */
    public static final String BUY_BUTTON_ICON_TITLE_BORDER_COLOR = "#FFFFFF";

}
