package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ServiceGuarantee;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.util.List;
import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 16/11/2022
 * @time 11:43
 * 模型描述：服务保障横条信息
 * 移动之家链接: https://mobile.sankuai.com/studio/model/info/29811
 */
@MobileDo(id = 0xcd16)
@Data
public class ServiceGuaranteeBanner implements Serializable {

    /**
     * 横条内容
     */
    @MobileField(key = 0xa42c)
    private List<String> bannerContent;

    /**
     * 服务保障横条icon的url
     */
    @MobileField(key = 0xf39b)
    private String iconUrl;
}
