package com.dianping.mobile.mapi.dztgdetail.tab;

import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Service
public class PetRelateDeals extends DealAttrsBasedRelateDeals {

    /**
     宠物服务： 408
     宠物店：  1701
     宠物医院：1702
     其他：   1704
     */
    @Override
    public List<Integer> identifyByPublishCategory() {
        return Arrays.asList(1701, 1702, 1704, 408);
    }

    @Override
    public List<String> dealAttrsMatched() {
        return Collections.singletonList("service_type");
    }

    @Override
    protected List<String> dealAttrsToLoadAfterRelate() {
        return Arrays.asList("service_type", "apply_pets_in_wash_beauty", "apply_pets", "body_type_pet_dog", "hair_length_pet_cat", "available_count_pet");
    }

    @Override
    protected String genTag(List<String> featureAttrs, List<AttributeDTO> attrs) {

        if (CollectionUtils.isEmpty(featureAttrs)) {
            return "";
        }

        String serviceType = featureAttrs.get(0);
        String tabTag;

        if ("洗澡".equals(serviceType) || "美容".equals(serviceType)) {
            tabTag = getWashBeautyTabTag(serviceType, attrs);
        } else if ("萌宠互动".equals(serviceType)) {
            tabTag = getPlayWithPetTabTag(attrs);
        } else {
            tabTag = getOtherTabTag(serviceType, attrs);
        }

        return tabTag;
    }

    private String getPlayWithPetTabTag(List<AttributeDTO> attrs) {
        String cnt = AttributeUtils.getAttributeValue("available_count_pet", attrs);
        return cnt + "萌宠互动";
    }


    private String getWashBeautyTabTag(String serviceType, List<AttributeDTO> attrs) {
        List<String> applyPetsAttrs = AttributeUtils.getAttributeValues("apply_pets_in_wash_beauty", attrs);
        String petInfo;

        if (CollectionUtils.isEmpty(applyPetsAttrs)) {
            return serviceType;
        }

        if (applyPetsAttrs.size() == 1) {

            String pet = applyPetsAttrs.get(0);

            if ("狗狗".equals(pet)) {

                String dogBodyTypeAttr = AttributeUtils.getAttributeValue("body_type_pet_dog", attrs);

                if (StringUtils.isBlank(dogBodyTypeAttr) || "不限制狗狗体型".equals(dogBodyTypeAttr)) {
                    petInfo = pet;
                } else {
                    petInfo = dogBodyTypeAttr.substring(0, 3);
                }

            } else if ("猫咪".equals(pet)) {

                String catHairLenTypeAttr = AttributeUtils.getAttributeValue("hair_length_pet_cat", attrs);

                if ("无毛长限制".equals(catHairLenTypeAttr)) {
                    petInfo = pet;
                } else {
                    petInfo =  catHairLenTypeAttr + pet;
                }

            } else {
                petInfo = pet;
            }

        } else if (applyPetsAttrs.size() == 2) {

            if (applyPetsAttrs.contains("狗狗") && applyPetsAttrs.contains("猫咪")) {
                petInfo = "犬猫";
            } else {
                petInfo = "宠物";
            }

        } else {
            petInfo = "宠物";
        }

        return petInfo + serviceType;
    }

    private String getOtherTabTag(String serviceType, List<AttributeDTO> attrs) {
        List<String> applyPets = AttributeUtils.getAttributeValues("apply_pets", attrs);
        String petInfo;

        if (applyPets.size() == 1) {
            petInfo = applyPets.get(0);
        } else if (applyPets.size() == 2) {
            if (applyPets.contains("狗狗") && applyPets.contains("猫咪")) {
                petInfo = "犬猫";
            } else {
                petInfo = "宠物";
            }
        } else {
            petInfo = "宠物";
        }

        return petInfo + serviceType;
    }

}
