package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.sku;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

@MobileDo(id = 0xbe57)
public class SkuSalesAttrDO implements Serializable {
    /**
     * 销售属性可选值
     */
    @MobileDo.MobileField(key = 0xe138)
    private List<String> skuSalesAttrValues;

    /**
     * 销售属性中文名
     */
    @MobileDo.MobileField(key = 0xf301)
    private String skuSalesAttrCnName;

    public List<String> getSkuSalesAttrValues() {
        return skuSalesAttrValues;
    }

    public void setSkuSalesAttrValues(List<String> skuSalesAttrValues) {
        this.skuSalesAttrValues = skuSalesAttrValues;
    }

    public String getSkuSalesAttrCnName() {
        return skuSalesAttrCnName;
    }

    public void setSkuSalesAttrCnName(String skuSalesAttrCnName) {
        this.skuSalesAttrCnName = skuSalesAttrCnName;
    }
}
