package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.util.EduDealUtils;
import com.dianping.vc.sdk.concurrent.threadpool.ExecutorServices;
import com.google.common.collect.Lists;
import com.sankuai.leads.count.thrift.api.NewLeadsCountService;
import com.sankuai.leads.count.thrift.dto.SalesSubjectDTO;
import com.sankuai.leads.count.thrift.dto.req.QueryLeadsSalesReqDTO;
import com.sankuai.leads.count.thrift.enums.LeadsCountOutputEnum;
import com.sankuai.leads.count.thrift.enums.SubjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

/**
 * 职业教育：获取试听课预约人数
 */
@Slf4j
public class EduTrailAuditionNumProcessor extends AbsDealProcessor {
    @Resource
    private NewLeadsCountService newLeadsCountService;

    public static final ExecutorService executorService = ExecutorServices.forThreadPoolExecutor("eduTrialAuditionNum", 10, 50);

    @Override
    public void prepare(DealCtx ctx) {
        if (!EduDealUtils.isVocationalEduCourseOrCamp(ctx.getDealGroupDTO()) && !EduDealUtils.preCheckShortClassHasButton(ctx)) {
            return;
        }

        Future query = CompletableFuture.supplyAsync(() -> {
            try {
                return newLeadsCountService.queryLeadsSales(buildRequest(ctx));
            } catch (TException e) {
                logger.error("newLeadsCountService.queryLeadsSales error", e);
            }
            return null;
        }, executorService);

        ctx.getFutureCtx().setQueryLeadsSalesRespDTOFuture(query);

    }

    public QueryLeadsSalesReqDTO buildRequest(DealCtx ctx) {
        SalesSubjectDTO salesSubjectDTO = new SalesSubjectDTO();
        Map<String, String> subjectValueMap = new HashMap<>();

        String referId = ctx.isMt() ? String.valueOf(ctx.getDealGroupDTO().getMtDealGroupId()) : String.valueOf(ctx.getDealGroupDTO().getDpDealGroupId());
        subjectValueMap.put(LeadsCountOutputEnum.REFER_ID.getValue(), referId);
        salesSubjectDTO.setSubjectType(SubjectTypeEnum.DEALID_TYPE.code);
        salesSubjectDTO.setSubjectMap(subjectValueMap);

        QueryLeadsSalesReqDTO queryLeadsSalesReqDTO = new QueryLeadsSalesReqDTO();
        queryLeadsSalesReqDTO.setLogicExpressionId(37);
        queryLeadsSalesReqDTO.setSalesSubjects(Lists.newArrayList(salesSubjectDTO));
        return queryLeadsSalesReqDTO;
    }


    @Override
    public void process(DealCtx ctx) {

    }
}
