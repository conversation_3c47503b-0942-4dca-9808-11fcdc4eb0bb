package com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.entity.Response;
import com.dianping.martgeneral.recommend.api.enums.PlatformEnum;
import com.dianping.martgeneral.recommend.api.service.RecommendService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.*;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.RelatedModuleCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.*;
import com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper;
import com.dianping.mobile.mapi.dztgdetail.util.PoiShopUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2023/7/5
 */
@Component("nearbyDiscountShop")
public class NearbyDiscountRelatedShopIdProcessor extends AbstractRelatedDealIdProcessor {
    private static final DecimalFormat df = new DecimalFormat("0.0");
    private static final int BIZID = 493;
    // 推荐来源，1-ugc推荐语，2-榜单
    private static final int UGC_SOURCE = 1;
    private static final int RANK_LABLE_SOURCE = 2;

    @Resource(name = "generalRecommendService")
    private RecommendService recommendService;
    @Autowired
    private PoiShopCategoryWrapper poiShopCategoryWrapper;
    @Autowired
    private MapperWrapper mapperWrapper;
    @Resource
    private PoiClientWrapper poiClientWrapper;
    @Resource
    private RecommendServiceWrapper recommendServiceWrapper;
    @Resource
    private RankWrapper rankWrapper;

    @Override
    public List<Long> getRelatedDealGroupIds(RelatedModuleCtx ctx) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule.NearbyDiscountRelatedShopIdProcessor.getRelatedDealGroupIds(com.dianping.mobile.mapi.dztgdetail.datatype.context.RelatedModuleCtx)");
        RelatedModuleReq req = ctx.getReq();
        EnvCtx envCtx = ctx.getEnvCtx();
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        DpPoiDTO dpPoiDTO = ctx.getDpPoiDTO();
        if (dealGroupDTO == null || dpPoiDTO == null) {
            return Collections.emptyList();
        }

        Integer shopCategory = poiShopCategoryWrapper.getBackSecondMainCategory(dpPoiDTO);
        if (shopCategory == null) {
            return Collections.emptyList();
        }

        RecommendParameters recommendParameters = new RecommendParameters();
        recommendParameters.setBizId(BIZID); // 门店 bizid取值为493
        recommendParameters.setCityId(req.getCityId());
        if (envCtx.isMt()) {
            recommendParameters.setUuid(envCtx.getUuid());// 美团侧 需要uuid
            recommendParameters.setPlatformEnum(PlatformEnum.MT);
            recommendParameters.setOriginUserId(String.valueOf(envCtx.getMtUserId()));//已确认判断平台后再使用
        } else {
            recommendParameters.setDpid(envCtx.getDpId());// 点评侧 需要 dpid
            recommendParameters.setPlatformEnum(PlatformEnum.DP);
            recommendParameters.setOriginUserId(String.valueOf(envCtx.getDpUserId()));
        }
        recommendParameters.setLat(req.getUserLat());
        recommendParameters.setLng(req.getUserLng());
        recommendParameters.setPageNumber(1);
        recommendParameters.setPageSize(4);
        Map<String, Object> bizParams = new HashMap<>();

        bizParams.put("genericCatIds", shopCategory.toString());// 当前商户二级类目；必传
        bizParams.put("blackShopList", req.getShopIdStr());// 商户黑名单, 多个逗号隔开

        recommendParameters.setBizParams(bizParams);
        Response<RecommendResult<RecommendDTO>> resultResponse = recommendService.recommend(recommendParameters, RecommendDTO.class);
        if (resultResponse != null && resultResponse.getResult() != null) {
            if (ctx.getResult() == null) {
                ctx.setResult(new RelatedDealModuleVO());
            }
            ctx.getResult().setRecordCount(resultResponse.getResult().getTotalSize());
            ctx.getResult().setNextStartIndex(req.getStart() + req.getLimit());
            if (CollectionUtils.isNotEmpty(resultResponse.getResult().getSortedResult())) {
                ctx.getResult().setIsEnd(resultResponse.getResult().getSortedResult().size() < req.getLimit());
                return resultResponse.getResult().getSortedResult().stream()
                        .map(recommendDTO -> Long.valueOf(recommendDTO.getItem()))
                        .collect(Collectors.toList());
            }
            ctx.getResult().setIsEnd(true);
        }

        return Collections.emptyList();
    }

    @Override
    public RelatedDealModuleVO assemble(RelatedModuleCtx ctx, List<Long> poiIds) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule.NearbyDiscountRelatedShopIdProcessor.assemble(com.dianping.mobile.mapi.dztgdetail.datatype.context.RelatedModuleCtx,java.util.List)");
        /**
         * 商户信息
         *
         * dpPoiServiceFuture.findShopsByShopIds
         *
         * 人均消费 avgPrice、星级 shopPower、评分 fiveScore、商圈 mainRegionName private String originalGeo（美团侧）
         * 用户距商户的距离 根据lat lng计算、商户名 shopName、商户图片 defaultPic、推荐理由（UGC推荐语 + 榜单）、跳链
         */

        RelatedModuleReq req = ctx.getReq();
        EnvCtx envCtx = ctx.getEnvCtx();

        Map<Long, List<Long>> dpMtIdMap = Maps.newHashMap();
        Map<Long, Long> dpMtIdPairMap = Maps.newHashMap();
        List<Long> shopIds = Lists.newArrayList();
        if (ctx.getEnvCtx().isMt()) {
            dpMtIdMap = mapperWrapper.queryDpByMtIdsL(poiIds);
            for (Map.Entry<Long, List<Long>> entry : dpMtIdMap.entrySet()) {
                shopIds.add(entry.getValue().get(0));
                dpMtIdPairMap.put(entry.getValue().get(0), entry.getKey());

            }
        } else {
            shopIds = poiIds;
        }

        // 获取门店基本信息 id是点评的id
        if (CollectionUtils.isNotEmpty(shopIds)) {
            List<DpPoiDTO> dpPoiDTOS = poiClientWrapper.batchGetDpPoiDTO(shopIds, Lists.newArrayList("shopId", "avgPrice", "shopPower", "fiveScore", "mainRegionName", "lat", "lng", "shopName", "defaultPic"));
            Map<Long, DpPoiDTO> shopIdDpPoiDTOMap = dpPoiDTOS.stream().collect(Collectors.toMap(DpPoiDTO::getShopId, Function.identity(), (o1, o2) -> o1));
            ctx.setShopIdDpPoiDTOMap(shopIdDpPoiDTOMap);
        }

        // 异步调用ugc接口
        // https://km.sankuai.com/collabpage/1720801995
        Future recommendServiceFuture = recommendServiceWrapper.invokeRecommend(req.getCityId(), req.getUserLat(), req.getUserLng(), req.getStart(), req.getLimit(), envCtx, poiIds);
        Map<Long, String> recommendReasonMap = recommendServiceWrapper.getRecommendReason(recommendServiceFuture);

        // 异步调用榜单接口
        Future rankFuture = rankWrapper.invokeRank(ctx.getEnvCtx(), ctx.getDealGroupDTO(), poiIds);
        Map<Long, RankingLabel> rankingLabelMap = rankWrapper.getRank(rankFuture);

        // 获取距离信息  注意此处返回的是点评的门店id
        Map<Long, Double> shopIdDistanceMap = getDistance(ctx.getShopIdDpPoiDTOMap(), req);

        // 拼装信息
        List<RelatedShopPBO> shopPBOS = new ArrayList<>();
        shopIds.forEach(poiId -> {
            RelatedShopPBO relatedShopPBO = buildRelatedShopPBO(poiId, ctx, recommendReasonMap, rankingLabelMap, shopIdDistanceMap, dpMtIdPairMap);
            if (Objects.nonNull(relatedShopPBO)) {
                shopPBOS.add(relatedShopPBO);
            }
        });
        Map<String, String> scene2TitleMap = Lion.getMap(LionConstants.APP_KEY,
                LionConstants.RETEADMODULE_SCENE_TITLE, String.class, Collections.emptyMap());
        ctx.getResult().setScene(req.getScene());
        ctx.getResult().setTitle(scene2TitleMap.get(req.getScene()));
        ctx.getResult().setShops(shopPBOS);
        return ctx.getResult();
    }

    private RelatedShopPBO buildRelatedShopPBO(Long poiId, RelatedModuleCtx ctx, Map<Long, String> recommendReasonMap, Map<Long, RankingLabel> rankingLabelMap, Map<Long, Double> shopIdDistanceMap, Map<Long, Long> dpMtIdPairMap) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule.NearbyDiscountRelatedShopIdProcessor.buildRelatedShopPBO(Long,RelatedModuleCtx,Map,Map,Map,Map)");
        Map<Long, DpPoiDTO> shopIdDpPoiDTOMap = ctx.getShopIdDpPoiDTOMap();
        if (MapUtils.isEmpty(shopIdDpPoiDTOMap) || !shopIdDpPoiDTOMap.containsKey(poiId)) {
            return null;
        }
        // 通过 点评id才能获取到
        DpPoiDTO dpPoiDTO = shopIdDpPoiDTOMap.get(poiId);
        RelatedShopPBO shopPBO = new RelatedShopPBO();
        shopPBO.setRegionDistanceDesc(getRegionDistanceDesc(dpPoiDTO.getMainRegionName(), shopIdDistanceMap.get(poiId)));
        boolean isMt = ctx.getEnvCtx().isMt();
        if (isMt){
            poiId = dpMtIdPairMap.get(poiId); 
        }
        shopPBO.setShopId(poiId);
        shopPBO.setAvgPrice(buildAvgPrice(dpPoiDTO.getAvgPrice()));
        shopPBO.setShopPower(dpPoiDTO.getShopPower());
        shopPBO.setFiveScore(String.valueOf(dpPoiDTO.getFiveScore()));
//        shopPBO.setMainRegionName(dpPoiDTO.getMainRegionName());
        shopPBO.setShopName(dpPoiDTO.getShopName());
        shopPBO.setShopPic(dpPoiDTO.getDefaultPic());
        this.buildRecommendInfo(shopPBO, rankingLabelMap.get(poiId), recommendReasonMap.get(poiId)); // 构造recommendInfo
        // 跳链处理
        DealCtx dealCtx = new DealCtx(ctx.getEnvCtx());
        dealCtx.setMtLongShopId(poiId);
        shopPBO.setShopUrl(UrlHelper.getShopUrl(dealCtx));// 跳链处理

        return shopPBO;
    }

    /**
     * 构建平均价格
     * @param avgPrice
     * @return
     */
    private String buildAvgPrice(Integer avgPrice){
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule.NearbyDiscountRelatedShopIdProcessor.buildAvgPrice(java.lang.Integer)");
        String priceText = StringUtils.EMPTY;
        if (avgPrice != null && avgPrice > 0 ){
            priceText = "¥" + avgPrice + "/人";
        }
        return priceText;
    }

    /**
     * 榜单优先级 > ugc参数
     * @param shopPBO
     * @param rankingLabel 榜单参数
     * @param recommendReason ugc参数
     */
    private void buildRecommendInfo(RelatedShopPBO shopPBO, RankingLabel rankingLabel, String recommendReason){
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule.NearbyDiscountRelatedShopIdProcessor.buildRecommendInfo(RelatedShopPBO,RankingLabel,String)");
        RelatedShopRecommendInfoPBO recommendInfoPBO = new RelatedShopRecommendInfoPBO();
//        recommendInfoPBO.setRecommendSource();
        // 优先级：榜单 > UGC
        if (null != rankingLabel){
            // 榜单
            recommendInfoPBO.setRecommendSource(RANK_LABLE_SOURCE);
            recommendInfoPBO.setRecommendText(rankingLabel.getLabelName());
            recommendInfoPBO.setRecommendIcon(rankingLabel.getIcon());
            recommendInfoPBO.setJumpUrl(rankingLabel.getRankLink());
        } else if (StringUtils.isNotEmpty(recommendReason)){
            // ugc
            recommendInfoPBO.setRecommendSource(UGC_SOURCE);
            recommendInfoPBO.setRecommendText(recommendReason);
        }
        shopPBO.setRecommendInfo(recommendInfoPBO);
    }

    private Map<Long, Double> getDistance(Map<Long, DpPoiDTO> shopIdDpPoiDTOMap, RelatedModuleReq req) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule.NearbyDiscountRelatedShopIdProcessor.getDistance(java.util.Map,com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedModuleReq)");
        Map<Long, Double> dpShopIdDistanceMap = Maps.newHashMap();
        Double userLat = req.getUserLat();
        Double userLng = req.getUserLng();
        shopIdDpPoiDTOMap.forEach((shopId, dpPoiDTO) -> {
            Double shopLat = dpPoiDTO.getLat();
            Double shopLng = dpPoiDTO.getLng();
            dpShopIdDistanceMap.put(dpPoiDTO.getShopId(), PoiShopUtil.getDistance(userLng, userLat, shopLng, shopLat));
        });

        return dpShopIdDistanceMap;
    }

    private static String getRegionDistanceDesc(String mainRegionName, Double distance) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule.NearbyDiscountRelatedShopIdProcessor.getRegionDistanceDesc(java.lang.String,java.lang.Double)");
        String distanceStr = String.valueOf(distance);
        if (StringUtils.isEmpty(distanceStr) && "null".equals(distanceStr)) {
            return null;
        }
        int y = distance.intValue();
        String distanceText = StringUtils.EMPTY;
        if (y <= 500) {
            distanceText = "距您" + y + "m";
        } else if (y <= 100000) {
            distanceText = "距您" + df.format(y / 1000.0) + "km";
        } else {
            distanceText = "距您" + ">100km";
        }

        String distanceDesc = mainRegionName + "｜" + distanceText;
        if (distanceDesc.equals("|")) { // 商圈和距离信息都为空
            distanceDesc = StringUtils.EMPTY;
        } else if (distanceDesc.startsWith("|")) {  // 只有距离信息
            distanceDesc = distanceDesc.substring(1, distanceDesc.length() - 1);
        } else if (distanceDesc.endsWith("|")) {    // 只有商圈信息
            distanceDesc = distanceDesc.substring(0, distanceDesc.length() - 2);
        }
        return distanceDesc;
    }
}
