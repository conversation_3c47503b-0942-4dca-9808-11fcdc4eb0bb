package com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-08-28
 */
@Setter
@Getter
@TypeDoc(description = "美甲款式频道页引导模块")
@MobileDo(id = 0xc5e3)
public class ChannelNailStyleVO implements Serializable {
    @FieldDoc(description = "点击按钮跳转链接")
    @MobileDo.MobileField(key = 0x522c)
    private String btnJumpUrl;

    @FieldDoc(description = "按钮文案")
    @MobileDo.MobileField(key = 0xa5e3)
    private String btnText;

    @FieldDoc(description = "引导跳转美甲频道页")
    @MobileDo.MobileField(key = 0x451b)
    private String text;
}
