package com.dianping.mobile.mapi.dztgdetail.biz.processor.imagetext.strategy.impl;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.imagetext.strategy.AbstractImageTextDetailStrategy;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ImageTextStrategyEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ContentDetailPBO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-12-11
 * @desc 默认图文详情处理策略
 */
@Component
public class DefaultImageTextDetailStrategyImpl extends AbstractImageTextDetailStrategy {
    @Override
    public ImageTextStrategyEnum getStrategyName() {
        return super.getStrategyName();
    }

    @Override
    public ContentDetailPBO getContentDetail(DealGroupDTO dealGroupDTO, List<ContentPBO> contents, int threshold) {
        return super.getContentDetail(dealGroupDTO, contents, threshold);
    }
}
