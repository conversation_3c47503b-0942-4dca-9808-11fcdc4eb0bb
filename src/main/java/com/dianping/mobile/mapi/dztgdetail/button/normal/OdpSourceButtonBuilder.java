package com.dianping.mobile.mapi.dztgdetail.button.normal;

import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.helper.PriceHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/22
 */
@Slf4j
public class OdpSourceButtonBuilder extends AbstractButtonBuilder {

    public static final List<String> REMAIN_EXT_PARAM_KEYS = Lists.newArrayList("odpflowinfo", "odpLaunchId", "odpFloorId", "odpChannelType");

    @Override
    protected void doBuild(DealCtx context, ButtonBuilderChain chain) {
        buildButton(context);
        chain.build(context);
    }

    private void buildButton(DealCtx context) {
        PriceDisplayDTO odpPrice = PriceHelper.getNormalPrice(context);
        DealBuyBtn button = buildOriginButton(context, DEFAULT_BUTTON_NAME);

        button.setPriceStr(formatPrice(odpPrice.getPrice()));
        button.setBtnText("￥" + button.getPriceStr() + " 立即购买");
        button.setBtnIcons(Lists.newArrayList());
        button.setBtnDesc(StringUtils.EMPTY);
        button.setBtnTag(buildBtnTag(odpPrice));
        button.setRedirectUrl(UrlHelper.getNewCommonBuyUrlWithSourceExt(context, context.getMtCityId(), REMAIN_EXT_PARAM_KEYS));
        context.addButton(button);
    }

    private String buildBtnTag(PriceDisplayDTO odpPrice) {
        if (CollectionUtils.isEmpty(odpPrice.getUsedPromos())) {
            return null;
        }
        BigDecimal totalReducePromo = new BigDecimal(0);
        for (PromoDTO promoDTO : odpPrice.getUsedPromos()) {
            if (promoDTO == null || promoDTO.getAmount() == null) {
                continue;
            }
            if (promoDTO.getIdentity().getPromoType() == PromoTypeEnum.NORMAL_PROMO.getType()) {
                totalReducePromo = totalReducePromo.add(promoDTO.getAmount());
            }
        }
        return totalReducePromo.compareTo(BigDecimal.ZERO) > 0 ? String.format("限时省¥%s元", totalReducePromo.stripTrailingZeros().toPlainString()) : null;
    }
}