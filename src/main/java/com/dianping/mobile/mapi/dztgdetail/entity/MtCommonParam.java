package com.dianping.mobile.mapi.dztgdetail.entity;

import com.dianping.cat.Cat;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Cons;
import com.dianping.mobile.mapi.dztgdetail.common.constants.QueryParams;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DealSearchSortEnum;
import com.dianping.mobile.mapi.dztgdetail.util.DoubleUtils;
import com.dianping.mobile.mapi.dztgdetail.util.ListUtils;
import com.google.common.collect.Lists;
import com.meituan.dataapp.poi.helper.VersionHelper;
import com.meituan.service.enums.ClientTypeEnum;
import com.meituan.service.mobile.message.recommend.CommonParamsMessage;
import com.meituan.service.mobile.message.recommend.UtmParamsMessage;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

/**
 * Created by guozhengyao on 16/4/1.
 */
public class MtCommonParam {
    protected Logger logger = LoggerFactory.getLogger(MtCommonParam.class);

    @Setter
    private Integer cityId;
    @Setter
    private Integer offset;
    private Integer limit;
    private Double lat;
    private String latStr;
    private Double lng;
    private String lngStr;
    @Setter
    private String sortStr;
    private String cityIdStr;
    private String fields;
    private List<String> fieldsList;
    private Long userId;
    private String deviceToken;
    private String utmCampaign;
    private String utmContent;
    private String utmSource;
    private String utmMedium;
    private String utmTerm;
    private String ste;
    private IMobileContext context;

    public MtCommonParam(IMobileContext context) {
        this.context = context;
        cityId = getIntParamWithDefault(QueryParams.CITY_ID, 0);
        cityIdStr = context.getParameter(QueryParams.CITY_ID);
        offset = getIntParamWithDefault(QueryParams.PAGING_OFFSET, 0);
        limit = getIntParamWithDefault(QueryParams.PAGING_LIMIT, 0);
        latStr = context.getParameter(QueryParams.LAT);
        lat = getDoubleParamWithDefault(QueryParams.LAT, 0.0);
        lngStr = context.getParameter(QueryParams.LNG);
        lng = getDoubleParamWithDefault(QueryParams.LNG, 0.0);
        sortStr = context.getParameter(QueryParams.SORT);
        fields = context.getParameter(QueryParams.FIELD);
        userId = context.getUserStatus().getMtUserId();
        utmCampaign = context.getParameter(QueryParams.UTM_CAMPAIGN);
        utmContent = context.getParameter(QueryParams.UTM_CONTENT);
        utmSource = context.getParameter(QueryParams.UTM_SOURCE);
        utmMedium = context.getParameter(QueryParams.UTM_MEDIUM);
        utmTerm = context.getParameter(QueryParams.UTM_TERM);
        ste = context.getParameter(QueryParams.STID_PARAM_STE);
        if (StringUtils.isNotBlank(fields)) {
            fieldsList = Arrays.asList(fields.trim().split(","));
        } else {
            fieldsList = Lists.newArrayList();
        }
        deviceToken = context.getParameter(Cons.DEVICE_TOKEN);
    }

    public String getSte() {
        return ste;
    }

    public String getDeviceToken() {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam.getDeviceToken()");
        return deviceToken;
    }

    private int getIntParamWithDefault(String key, int value) {
        int val;
        try {
            val = Integer.parseInt(context.getParameter(key));
        } catch (Exception e) {
            logger.error("getIntParamWithDefault error. key: {}", key, e);
            val = value;
        }
        return val;
    }

    private double getDoubleParamWithDefault(String key, double value) {
        double val;
        try {
            val = Double.parseDouble(context.getParameter(key));
        } catch (Exception e) {
            logger.error("getDoubleParamWithDefault error. key: {}", key, e);
            val = value;
        }
        return val;
    }

    public List<String> getFieldsList() {
        return fieldsList;
    }

    public String getFields() {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam.getFields()");
        return fields;
    }

    public Integer getOffset() {
        return offset;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public Long getUserId() {
        return userId;
    }

    public String getCityIdStr() {
        return cityIdStr;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setSort(String sort) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam.setSort(java.lang.String)");

    }

    public String getSortStr() {
        return sortStr;
    }

    public DealSearchSortEnum getSortType() {
        DealSearchSortEnum sort;
        String sortStr = getSortStr();
        if (StringUtils.isBlank(sortStr)) {
            return DealSearchSortEnum.natural;
        }
        //poi电影那边传的是avgscore，而deal传的是rating，为了统一
        if (sortStr.equals("avgscore")) {
            sortStr = "rating";
        }
        sort = DealSearchSortEnum.getEnumByName(sortStr);
        return sort;
    }

    public String getAreaId() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam.getAreaId()");
        return context.getParameter(QueryParams.AREA_ID);
    }

    public Double getLat() {
        return lat;
    }

    public String getLatStr() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam.getLatStr()");
        return latStr;
    }

    public String getLngStr() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam.getLngStr()");
        return lngStr;
    }

    public Double getLng() {
        return lng;
    }

    public String getUtmCampaign() {
        return utmCampaign;
    }

    public String getUtmContent() {
        return utmContent;
    }

    public String getUtmSource() {
        return utmSource;
    }

    public String getUtmMedium() {
        return utmMedium;
    }

    public String getUtmTerm() {
        return utmTerm;
    }

    public String getVersionName() {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam.getVersionName()");
        return context.getParameter(QueryParams.CLIENT_VERSION);
    }

    public String getVersion() {
        String version = context.getParameter(QueryParams.UTM_TERM);
        if (ClientTypeEnum.isFromAndroid(getUtmMedium())) {
            //http://wiki.sankuai.com/pages/viewpage.action?pageId=45744356
            String versionName = context.getParameter(Cons.HTTPPARAM_VERSION_NAME);
            if (versionName != null && !versionName.isEmpty()) {
                version = versionName;
            } else {
                version = VersionHelper.convertAndroidVersion(version);
            }
        }
        version = StringUtils.isBlank(version) ? "1.0" : version;
        return version;
    }

    public String getUuid() {
        return context.getParameter(QueryParams.UUID);
    }

    public String getClient() {
        return context.getParameter(QueryParams.CLIENT);
    }

    public String getCategory() {
        return context.getParameter(QueryParams.CATE);
    }

    public void setLng(Double lng) {
        this.lng = lng;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }

    public UtmParamsMessage getUtmParamMessage() {
        UtmParamsMessage utm = new UtmParamsMessage();
        utm.setCi(getCityId());
        utm.setUtmSource(getUtmSource());
        utm.setUtmCampaign(getUtmCampaign());
        utm.setUtmMedium(getUtmMedium());
        utm.setUtmTerm(getUtmTerm());
        utm.setUtmContent(getUtmContent());
        utm.setUuid(getUuid());
        return utm;
    }

    public CommonParamsMessage getCommonParamsMessage() {
        CommonParamsMessage msg = new CommonParamsMessage();
        msg.setOffset(getOffset());
        msg.setLimit(getLimit());
        if (getLimit() == 0) {
            // mt原有逻辑, 如果未设置limit则设置为5
            msg.setLimit(5);
        }
        msg.setMyLat(getLat());
        msg.setMyLng(getLng());
        DealSearchSortEnum sort = getSortType();
        if (sort == DealSearchSortEnum.distance) {
            if (!isValidCoordinate(msg.getMyLat(), msg.getMyLng())) {
                logger.error("invalid coordinate lat=" + msg.getMyLat() + " lng=" + msg.getMyLng());
                throw new IllegalArgumentException("invalid coordinate");
            }
            if (DoubleUtils.isZero(msg.getMyLat()) && DoubleUtils.isZero(msg.getMyLng())) {
                // 参数不合法时默认使用不排序
                msg.setSort(DealSearchSortEnum.natural.name());
            }
        }
        if (StringUtils.isNotBlank(getClient())) {
            msg.setClient(getClient());
        }
        msg.setFields(getFieldsList());
        if (StringUtils.isNotBlank(getCategory())) {
            msg.setCates(ListUtils.convertString2IntList(getCategory(), ","));
        }
        return msg;
    }

    // 判定坐标是否合法，在(0,180)之间认为合法
    private boolean isValidCoordinate(double lat, double lng) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam.isValidCoordinate(double,double)");
        return lat < 180 && lat >= 0 && lng >= 0 && lng < 180;
    }

}
