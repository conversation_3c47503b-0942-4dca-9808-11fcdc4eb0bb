package com.dianping.mobile.mapi.dztgdetail.datatype.context;

import com.dianping.deal.sales.common.datatype.SalesDisplayDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.BuyMoreSaveMoreReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.BuyMoreSaveMoreVO;
import com.dianping.mobile.mapi.dztgdetail.entity.CombinationDealInfo;
import com.dianping.mobile.mapi.dztgdetail.entity.SkuSummary;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Author: <EMAIL>
 * @Date: 2023/7/25
 */
@Data
public class BuyMoreSaveMoreCtx {
    /**
     * request
     */
    BuyMoreSaveMoreReq req;

    EnvCtx envCtx;


    /**
     * 团单Id对应价格信息
     */
    Map<Integer, BigDecimal> dealIdPriceMap;

    /**
     * 搭售品和CombinationDealInfo键值对
     */
    Map<Integer, CombinationDealInfo> bindingDealCombinationInfoMap;

    /**
     * 团单详情map
     */
    Map<Integer, DealGroupDTO> dealGroupDTOMap;

    /**
     * 提单页map
     */
    Map<Integer, String> dealOrderPageUrlMap;

    /**
     * 销量
     */
    Map<Integer, SalesDisplayDTO> productId2SaleMap;

    /**
     * 返回结果
     */
    BuyMoreSaveMoreVO result;

    private Set<Integer> shopCategoryIds;

    private SkuSummary skuSummary;
}
