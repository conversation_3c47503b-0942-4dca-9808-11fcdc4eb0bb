package com.dianping.mobile.mapi.dztgdetail.rcf.repository.dealbaseinfo;

import com.dianping.mobile.mapi.dztgdetail.biz.service.cellarservice.dealdetail.DealGroupDTOCellarService;
import com.dianping.mobile.mapi.dztgdetail.biz.service.cellarservice.dealdetail.req.DealGroupCacheRequest;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.google.common.collect.Sets;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupCategoryBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

import static com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils.hitDealId2CategorySwitch;

/**
 * @Author: guangyujie
 * @Date: 2024/11/18 19:34
 */
@Component
public class DealCategoryCacheService {

    @Autowired
    private QueryCenterWrapper queryCenterWrapper;

    @Resource
    private DealGroupDTOCellarService dealGroupDTOCellarService;

    public DealGroupCategoryDTO get(long dealId, boolean isMT) throws TException {
        DealGroupCacheRequest request = new DealGroupCacheRequest();
        request.setDealGroupId(dealId);
        request.setMT(isMT);
        String cacheKey = dealGroupDTOCellarService.buildKey(request);
        DealGroupDTO dealGroupDTO = (DealGroupDTO) dealGroupDTOCellarService.getCacheValue(cacheKey);
        if (dealGroupDTO != null && dealGroupDTO.getCategory() != null) {
            return dealGroupDTO.getCategory();
        }
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet(dealId), isMT ? IdTypeEnum.MT : IdTypeEnum.DP)
                .category(DealGroupCategoryBuilder.builder().all())
                .build();
        dealGroupDTO = queryCenterWrapper.getDealGroupDTO(queryByDealGroupIdRequest);
        // 将结果缓存到cellar，团单id为key
        saveCache(dealId, isMT, dealGroupDTO);
        return Optional.ofNullable(dealGroupDTO).map(DealGroupDTO::getCategory).orElse(null);
    }

    public void saveCache(long dealId, boolean isMT, DealGroupDTO dealGroupDTO) {
        // 将结果缓存到cellar，团单id为key
        if (hitDealId2CategorySwitch(Math.toIntExact(dealId))){
            dealGroupDTOCellarService.saveOrUpdate(buildCacheRequest(dealId, isMT), dealGroupDTO);
        }
    }

    public DealGroupCacheRequest buildCacheRequest(long dealId, boolean isMT){
        DealGroupCacheRequest cacheRequest = new DealGroupCacheRequest();
        cacheRequest.setDealGroupId(dealId);
        cacheRequest.setMT(isMT);
        return cacheRequest;
    }

}
