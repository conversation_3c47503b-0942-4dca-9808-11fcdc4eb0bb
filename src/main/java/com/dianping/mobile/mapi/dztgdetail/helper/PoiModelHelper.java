package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.common.enums.PoiCateEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.MtPoiModel;
import com.meituan.service.mobile.sinai.client.model.PoiModelL;
import org.apache.commons.collections.CollectionUtils;

/**
 * PoiModel给出的数据需要做一些转换，才能用
 * <p/>
 * Created by pan on 14/11/26.
 */
public class PoiModelHelper {


    public static MtPoiModel convertPoiModel(PoiModelL poiModel) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.helper.PoiModelHelper.convertPoiModel(com.meituan.service.mobile.sinai.client.model.PoiModelL)");
        return doBuildMtPoiModel(poiModel, true);
    }

    public static MtPoiModel buildRdploc(PoiModelL poiModel) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.helper.PoiModelHelper.buildRdploc(com.meituan.service.mobile.sinai.client.model.PoiModelL)");
        return doBuildMtPoiModel(poiModel, false);
    }

    private static MtPoiModel doBuildMtPoiModel(PoiModelL poiModel, boolean showPhoneNo) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.helper.PoiModelHelper.doBuildMtPoiModel(com.meituan.service.mobile.sinai.client.model.PoiModelL,boolean)");
        if (poiModel == null) {
            return null;
        }
        MtPoiModel mtPoiModel = new MtPoiModel();
        if (showPhoneNo && CollectionUtils.isNotEmpty(poiModel.getCate())
                && poiModel.getCate().contains(PoiCateEnum.TRAINING.getValue())) {
            mtPoiModel.setShowPhoneNo(false);
        }
        mtPoiModel.setPoiid(poiModel.getId() > Integer.MAX_VALUE ? 0 : (int) poiModel.getId());
        mtPoiModel.setPoiIdStr(String.valueOf(poiModel.getId()));
        mtPoiModel.setPhone(poiModel.getPhone());
        mtPoiModel.setName(poiModel.getName());
        mtPoiModel.setAddr(poiModel.getAddress());
        mtPoiModel.setShowType(poiModel.getShowType());
        mtPoiModel.setLng(poiModel.getLongitude());
        mtPoiModel.setLat(poiModel.getLatitude());
        return mtPoiModel;
    }

}
