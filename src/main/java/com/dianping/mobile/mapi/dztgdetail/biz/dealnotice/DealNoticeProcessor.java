package com.dianping.mobile.mapi.dztgdetail.biz.dealnotice;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealNoticeLayerCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealnotice.DealNoticeLayerPBO;

/**
 * <AUTHOR>
 * @create 2024/9/30 16:36
 */
public interface DealNoticeProcessor {
    boolean valid(DealNoticeLayerCtx ctx);

    DealNoticeLayerPBO getDealNoticeLayerPBO(DealNoticeLayerCtx ctx);
}
