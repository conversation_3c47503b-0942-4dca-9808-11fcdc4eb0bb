package com.dianping.mobile.mapi.dztgdetail.entity;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.CePinTuanPassParamConfig;
import lombok.Data;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2024/4/23
 */
@Data
public class CostEffectivePinTuan {
    /**
     * 特团来源场景
     */
    private Integer sceneType;

    /**
     * 拼团ID
     */
    private String pinTuanActivityId;

    /**
     * 分享token， 同orderGroupId
     */
    private String shareToken;

    /**
     * 是否含有拼团优惠
     */
    private boolean cePinTuanScene;

    /**
     * 是否拼团中，true-已开团，false-未开团
     */
    private boolean pinTuanOpened;

    /**
     * 是否主态拼团，true-主态，false-客态
     */
    private boolean activePinTuan;

    /**
     * 已拼&待拼，true-已拼，false-待拼
     */
    private boolean inPinTuan;

    /**
     * 拼团总人数
     */
    private Integer groupSuccCountMin;

    /**
     * 拼团成功人数
     */
    private Integer hasHelpCount;

    /**
     * 参团用户头像
     */
    private List<String> avatars;

    /**
     * 拼团完成时间，unix时间戳
     */
    private Long expireTime;

    /**
     * 拼团规则浮层文案
     */
    private List<RuleInfoPO> ruleInfoPOS;

    /**
     * 是否限制新客
     */
    private boolean limitNewCustomJoin;

    /**
     * 促销ID
     */
    private long promotionId;

    /**
     * 拼团参数配置
     */
    private CePinTuanPassParamConfig pinTuanPassParamConfig;

    /**
     * 拼团人数（该变量+1则为总拼团人数）
     */
    private Integer helpSuccCountMin;
}
