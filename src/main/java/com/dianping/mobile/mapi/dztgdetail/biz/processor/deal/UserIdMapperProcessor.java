package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.exception.UserIdMapperProcessorException;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.pigeon.remoting.common.exception.RpcException;
import com.sankuai.wpt.user.merge.query.thrift.message.Error;
import com.sankuai.wpt.user.merge.query.thrift.message.FlattedBindRelation;
import com.sankuai.wpt.user.merge.query.thrift.message.FlattedBindRelationAggregateResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;
import java.util.concurrent.Future;

/**
 * @Author: <EMAIL>
 * @Date: 2023/11/30
 */
@Slf4j
public class UserIdMapperProcessor extends AbsDealProcessor {

    @Autowired
    private MapperWrapper mapperWrapper;

    private final static String CAT_NAME = "UserIdMapperProcessor";

    @Override
    public void prepare(DealCtx ctx) {
        if (ctx.isMt()) {
            if (ctx.getEnvCtx().getMtUserId() > 0) {
                Future UserIdMapperFuture = mapperWrapper.preUserInfoByMtUserId(ctx.getEnvCtx().getMtUserId());
                ctx.getFutureCtx().setUserIdMapperFuture(UserIdMapperFuture);
            } else {
                Cat.logEvent(CAT_NAME, "mt_user_not_login");
            }
        } else {
            if (ctx.getEnvCtx().getDpUserId() > 0) {
                Future UserIdMapperFuture = mapperWrapper.preUserInfoByDpUserId(ctx.getEnvCtx().getDpUserId());
                ctx.getFutureCtx().setUserIdMapperFuture(UserIdMapperFuture);
            } else {
                Cat.logEvent(CAT_NAME, "dp_user_not_login");
            }
        }
    }

    @Override
    public void process(DealCtx ctx) {
        if (ctx.getFutureCtx().getUserIdMapperFuture() == null) {
            return;
        }
        FlattedBindRelationAggregateResp userIdMapperResponse = mapperWrapper.getFutureResult(ctx.getFutureCtx().getUserIdMapperFuture());
        if (userIdMapperResponse == null) {
            Cat.logEvent(CAT_NAME, "response_is_null");
            log.error("UserIdMapperProcessor,userIdMapperResponse is null", new RpcException("userId转换response为null"));
            return;
        }
        if (!userIdMapperResponse.isSuccess()) {
            Cat.logEvent(CAT_NAME, "response_fail");
            String errorMsg = Optional.ofNullable(userIdMapperResponse.getError()).map(Error::toString).orElse("未知问题");
            log.error("UserIdMapperProcessor fail,errorMsg:{}", errorMsg, new RpcException("UserId转换失败,errorMsg:" + errorMsg));
            return;
        }
        FlattedBindRelation flattedAggregateData = userIdMapperResponse.getFlattedAggregateData();
        if (flattedAggregateData == null) {
            Cat.logEvent(CAT_NAME, "response_data_is_null");
            log.error("UserIdMapperProcessor fail,data is null", new RpcException("UserId转换失败,返回值为null"));
            return;
        }
        if (flattedAggregateData.getTargetUserType() != 1) {
            Cat.logError(new UserIdMapperProcessorException("返回值的targetUserType!=1可能会导致返回的结果不是点评user体系"));
        }
        if (ctx.isMt()) {
            if (ctx.getEnvCtx().getMtUserId() != flattedAggregateData.getMtRealUserId()) {
                Cat.logError(new UserIdMapperProcessorException("用mtUserId查询双平台id，返回值里mtRealUserId不等于入参的mtUserId"));
            }
            ctx.getEnvCtx().setMtVirtualUserId(flattedAggregateData.getMtVirtualUserId());
            ctx.getEnvCtx().setDpUserId(flattedAggregateData.getTargetRealUserId());
            ctx.getEnvCtx().setDpVirtualUserId(flattedAggregateData.getTargetVirtualUserId());
        } else {
            if (ctx.getEnvCtx().getDpUserId() != flattedAggregateData.getTargetRealUserId()) {
                Cat.logError(new UserIdMapperProcessorException("用dpUserId查询双平台id，返回值里targetRealUserId不等于入参的dpUserId"));
            }
            ctx.getEnvCtx().setMtUserId(flattedAggregateData.getMtRealUserId());
            ctx.getEnvCtx().setMtVirtualUserId(flattedAggregateData.getMtVirtualUserId());
            ctx.getEnvCtx().setDpVirtualUserId(flattedAggregateData.getTargetVirtualUserId());
        }
        checkUserId(ctx);
    }

    private void checkUserId(DealCtx ctx) {
        if (ctx.getEnvCtx().getDpUserId() <= 0) {
            Cat.logEvent(CAT_NAME, "dp_user_id_is_null");
        }
        if (ctx.getEnvCtx().getDpVirtualUserId() <= 0) {
            Cat.logEvent(CAT_NAME, "dp_virtual_user_id_is_null");
        }
        if (ctx.getEnvCtx().getMtUserId() <= 0) {
            Cat.logEvent(CAT_NAME, "mt_user_id_is_null");
        }
        if (ctx.getEnvCtx().getMtVirtualUserId() <= 0) {
            Cat.logEvent(CAT_NAME, "mt_virtual_user_id_is_null");
        }
    }

}
