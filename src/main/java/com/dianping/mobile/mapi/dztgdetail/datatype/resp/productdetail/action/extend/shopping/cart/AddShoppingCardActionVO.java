package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.action.extend.shopping.cart;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.BottomBarActionDataVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.enums.BottomBarActionTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Author: guangyu<PERSON>e
 * @Date: 2025/3/16 15:40
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AddShoppingCardActionVO extends BottomBarActionDataVO {

    private final int actionType = BottomBarActionTypeEnum.ADD_SHOPPING_CARD.getCode();

    private int productType;

    private long productId;

    private long skuId;

}
