package com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.deal.bff.cache.enums.RcfDealBffInterfaceEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ImageScaleEnum;
import com.dianping.mobile.mapi.dztgdetail.rcf.api.req.DealNativeSnapshotReq;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.DealRcfCustomerProcessor;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.dto.DealBffResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

import static com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils.hitRcfCalHeightLogSwitch;

/**
 * @Author: guangyujie
 * @Date: 2024/11/26 20:09
 */
@Slf4j
@Component
public class DealHeadPicHeightCalculateHandler implements DealRcfCustomerProcessor {

    @Override
    public void customerProcess(DealNativeSnapshotReq request, DealBffResponseDTO bffResponse) {
        try {
            JSONObject dealMainInterfaceData = bffResponse.getBffResponse(RcfDealBffInterfaceEnum.dzdealbase);
            JSONArray dealContents = (JSONArray)dealMainInterfaceData.get("dealContents");
            if (Objects.isNull(dealContents) && hitRcfCalHeightLogSwitch()) {
                log.info("DealHeadPicHeightCalculateHandler.customerProcess dealContents is null, request:{}, dealMainInterfaceData:{}", request, dealMainInterfaceData);
            }
            String scale = getScale(dealContents);
            double picHeight = calculateHeadPicHeight(scale, request.getDeviceWidth(), dealContents);
            setPicHeight(dealContents, picHeight);
        } catch (Exception e) {
            log.error("DealHeadPicHeightCalculateHandler.customerProcess error , request:{}", request, e);
        }
    }

    @Override
    public boolean canProcess(DealNativeSnapshotReq request, DealBffResponseDTO bffResponse) {
        return true;
    }

    private JSONArray setPicHeight(JSONArray dealContents, double picHeight) {
        if (Objects.nonNull(dealContents)) {
            for (int i = 0; i < dealContents.size(); i++) {
                JSONObject dealContent = (JSONObject)dealContents.get(i);
                dealContent.put("rcfPicHeight", picHeight);
            }
        }
        return dealContents;
    }

    private String getScale(JSONArray dealContents) {
        try {
            if (Objects.isNull(dealContents) || dealContents.isEmpty()) {
                return ImageScaleEnum.SIXTEEN_TO_NINE.getScale();
            }
            JSONObject dealContent = (JSONObject)dealContents.stream().findFirst().get();
            return (String)dealContent.get("scale");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ImageScaleEnum.SIXTEEN_TO_NINE.getScale();
        }
    }

    private double calculateHeadPicHeight(String scale, double screenWidth, JSONArray dealContents) {
        // 3:4
        if (ImageScaleEnum.THREE_TO_FOUR.getScale().equals(scale)) {
            return (screenWidth / 3) * 4;
        } else if (ImageScaleEnum.SIXTEEN_TO_NINE.getScale().equals(scale)) {
            // 16:9
            double sixteenNineRatePicHeight = calculateSixteenNineRatePicHeight(screenWidth);
            double hasTabCapsuleHeight = calculateTabCapsuleHeight(dealContents);
            double hasProgressHeight = calculateProgressHeight(dealContents);
            return sixteenNineRatePicHeight + hasTabCapsuleHeight + hasProgressHeight;
        }
        return 0;
    }

    private double calculateSixteenNineRatePicHeight(double screenWidth) {
        return (screenWidth / 16) * 9;
    }

    private double calculateTabCapsuleHeight(JSONArray dealContents) {
        if (Objects.isNull(dealContents)) {
            return 0;
        }
        Set<Integer> types = new HashSet<>();
        for (int i = 0; i < dealContents.size(); i++) {
            JSONObject dealContent = (JSONObject)dealContents.get(i);
            int type = (int)dealContent.get("type");
            types.add(type);
        }
        if ((types.contains(1) && types.contains(2))) {
            return 24;
        }
        return 0;
    }

    private double calculateProgressHeight(JSONArray dealContents) {
        return Objects.nonNull(dealContents) && dealContents.size() > 1 ? 14 : 0;
    }

}
