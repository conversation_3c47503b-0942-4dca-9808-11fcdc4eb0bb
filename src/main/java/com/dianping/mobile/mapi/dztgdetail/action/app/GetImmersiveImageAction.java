package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.CatEvents;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageVO;
import com.dianping.mobile.mapi.dztgdetail.facade.ImmersiveImageFacade;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-08-24
 * @desc 到综团详页-头图点击查看全部（即进入沉浸页）时调用此接口
 */
@InterfaceDoc(
        displayName = "到综团单沉浸页展示信息查询接口",
        type = "restful",
        description = "到综团单沉浸页展示信息：包括团购头图、视频、团单关联的款式名称、款式标签、款式图集等",
        scenarios = "该接口适用于双平台App站点的团购详情页中有款式的头图进入沉浸页展示",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "liuwen17"
)
@Controller("general/platform/dztgdetail/getimmersiveimage.bin")
@Action(url = "getimmersiveimage.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class GetImmersiveImageAction extends AbsAction<GetImmersiveImageRequest> {

    @Resource
    private ImmersiveImageFacade immersiveImageFacade;

    @Override
    protected IMobileResponse validate(GetImmersiveImageRequest request, IMobileContext context) {
        IdUpgradeUtils.processProductIdForGetImmersiveImageRequest(request, "getimmersiveimage.bin");
        if (Objects.isNull(request) || request.getDealGroupId() <= 0) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @Override
    @CryptMethod
    protected IMobileResponse execute(GetImmersiveImageRequest request, IMobileContext iMobileContext) {
        try {
            EnvCtx envCtx = initEnvCtx(iMobileContext);
            ImmersiveImageVO immersiveImageVO = immersiveImageFacade.getImmersiveImage(request, envCtx);
            if (Objects.nonNull(immersiveImageVO)) {
                Cat.logMetricForCount(CatEvents.IMMERSIVE_IMAGE_SUC);
                return new CommonMobileResponse(immersiveImageVO);
            }
            Cat.logMetricForCount(CatEvents.IMMERSIVE_IMAGE_NO_DATA);
            return new CommonMobileResponse(Resps.NoDataResp);
        } catch (Exception e) {
            log.error("getimmersiveimage.bin error!", e);
        }
        Cat.logMetricForCount(CatEvents.IMMERSIVE_IMAGE_FAIL);
        return Resps.SYSTEM_ERROR;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
