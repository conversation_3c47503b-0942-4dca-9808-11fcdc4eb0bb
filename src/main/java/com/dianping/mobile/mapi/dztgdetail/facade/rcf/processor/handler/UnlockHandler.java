package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.handler;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/11/7 10:48
 */
@Service
@Slf4j
public class UnlockHandler implements BaseReserveMaintenanceHandler {
    private static final String MT_KEY = "MtUnlockReserveExp";
    private static final String DP_KEY = "DpUnlockReserveExp";
    private static final List<String> UNLOCK_RESERVE_AB_KEYS = Lists.newArrayList(MT_KEY, DP_KEY);

    /**
     * 开锁
     * 
     * @return
     */
    @Override
    public int getDealSecondCategory() {
        return 415;
    }

    @Override
    public String getExpName(boolean isMt) {
        return isMt ? MT_KEY : DP_KEY;
    }

    @Override
    public List<String> getAbKeys() {
        return UNLOCK_RESERVE_AB_KEYS;
    }
}
