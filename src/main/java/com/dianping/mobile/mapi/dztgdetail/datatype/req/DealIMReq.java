package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.dianping.mobile.framework.annotation.MobileRequest.Param;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.DecryptedField;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
@TypeDoc(description = "IM请求参数")
@MobileRequest
public class DealIMReq implements IMobileRequest, Serializable {

    @Deprecated
    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，原int类型")
    @Param(name = "dealgroupid")
    private Integer dealgroupid;

    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，string类型")
    @Param(name = "stringdealgroupid")
    private String stringDealGroupId;

    @Deprecated
    @FieldDoc(description = "商户ID", rule = "美团平台为美团商户ID，点评平台为点评商户ID")
    @Param(name = "shopid")
    private Integer shopid;
    @Param(name = "shopidEncrypt")
    @DecryptedField(targetFieldName = "shopid")
    private String shopidEncrypt;

    @FieldDoc(description = "商户ID str", rule = "美团平台为美团商户ID，点评平台为点评商户ID str")
    @Param(name = "shopidstr")
    private String shopidstr;
    @Param(name = "shopidstrEncrypt")
    @DecryptedField(targetFieldName = "shopidstr")
    private String shopidstrEncrypt;

    @FieldDoc(description = "站点：APP可以不传，其他站点必须传，值参见后端枚举", rule = "@see com.dianping.deal.common.enums.ClientTypeEnum")
    @Param(name = "clienttype")
    private Integer clienttype;

    public long getShopIdLong() {
        if(StringUtils.isNumeric(shopidstr)) {
            return Long.parseLong(shopidstr);
        } else if(shopid != null) {
            return shopid.longValue();
        } else {
            return 0L;
        }
    }

}
