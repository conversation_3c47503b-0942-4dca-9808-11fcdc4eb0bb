package com.dianping.mobile.mapi.dztgdetail.common.enums;

import lombok.Getter;

/**
 * @Author: z<PERSON><PERSON><PERSON><PERSON><PERSON>@meituan.com
 * @Date: 2024/4/29
 */
@Getter
public enum BuyBarCallSceneEnum {
    MT_LIVE_MINIAPP(1, "mt_live_miniapp"),
    FREE_DEAL(2, "free_deal"),
    ODP(3, "odp"),
    MEMBER_EXCLUSIVE(4, "member_exclusive"),
    SHOPPING_CART(5, "shopping_cart"),
    EDU_SPECIAL(6, "edu_special"),
    XIYU_BUILDER(7, "xiyu"),
    JOY_MARKET(8, "joy_market"),
    ZU_LIAO(9, "zu_liao"),
    JO<PERSON>(10, "joy"),
    BEAUTY(11, "beauty"),
    DEFAULT(12, "default"),
    FITNESS_CROSS(13, "fitness_cross"),
    PREPAY_DEAL(14, "prepay_deal"),
    LEADS_DEAL(15, "leads_deal"),
    WEDDING_LEADS_DEAL(16, "wedding_leads_deal");

    private int id;
    private String name;

    BuyBarCallSceneEnum(int id, String name) {
        this.id = id;
        this.name = name;
    }
}
