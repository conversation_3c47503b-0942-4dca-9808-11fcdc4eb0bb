package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.lion.client.util.CollectionUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.AbsWrapper;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import com.sankuai.fbi.faas.wed.api.CrossCatRecommendService;
import org.springframework.stereotype.Component;

import java.util.concurrent.Future;
import javax.annotation.Resource;
import java.util.List;


@Component
public class CrossCatRecommendServiceWrapper extends AbsWrapper {

    @Resource(name = "CrossCatRecommendServiceFuture")
    public CrossCatRecommendService crossCatRecommendService;

    /**
     * 获取交叉推荐主题(根据后台类目ID列表)
     *
     * @param backCatIds
     * @return
     */
    public Future prepareLoadLeadsInfo(List<Integer> backCatIds) {
        try {
            if (CollectionUtils.isEmpty(backCatIds)) {
                return null;
            }
            crossCatRecommendService.getEduCrossRecSubjectsBybackCatIds(backCatIds);
            return ContextStore.getFuture();
        } catch (Exception e) {
            logger.error(" CrossCatRecommendServiceWrapper prepareLoadLeadsInfo has error", e);
        }
        return null;
    }

}

