package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils.dealsFilterSwitch;

/**
 * @author: created by hang.yu on 2024/1/11 15:02
 */
public class TimesDealUtil {

    private static final Integer TIMES_CARD = 19;
    private static final int VALID_DEAL_STATUS = 1;
    private static final String MONTHLY_SUBSCRIPTION = "是";


    /**
     * 判断是否为 团购次卡的团单
     */
    public static boolean isTimesDeal(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO == null || dealGroupDTO.getBasic() == null) {
            return false;
        }
        return Objects.equals(TIMES_CARD, dealGroupDTO.getBasic().getTradeType());
    }

    /**
     * 判断是否 单次到店仅可核销一次
     */
    public static boolean onlyVerificationOne(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO == null || CollectionUtils.isEmpty(dealGroupDTO.getAttrs())) {
            return false;
        }
        return DealAttrHelper.onlyVerificationOne(dealGroupDTO.getAttrs());
    }

    /**
     * 解析次数
     */
    public static String parseTimes(DealGroupDTO dealGroupDTO) {
        // 过滤无效团单
        filterInvalidDeals(dealGroupDTO);
        if (dealGroupDTO == null || CollectionUtils.isEmpty(dealGroupDTO.getDeals())) {
            return null;
        }
        DealGroupDealDTO dealGroupDealDTO = dealGroupDTO.getDeals().get(0);
        if (dealGroupDealDTO == null || CollectionUtils.isEmpty(dealGroupDealDTO.getAttrs())) {
            return null;
        }
        return DealAttrHelper.getTimes(dealGroupDealDTO.getAttrs());
    }

    /**
     * 过滤无效团单
     * @param dealGroupDTO
     */
    public static void filterInvalidDeals(DealGroupDTO dealGroupDTO){
        if (dealsFilterSwitch() && Objects.nonNull(dealGroupDTO) && CollectionUtils.isNotEmpty(dealGroupDTO.getDeals())){
            List<DealGroupDealDTO> deals = dealGroupDTO.getDeals();
            deals = deals.stream().filter(deal->Objects.nonNull(deal.getBasic()) && Objects.nonNull(deal.getBasic().getStatus()) && deal.getBasic().getStatus() == VALID_DEAL_STATUS).collect(Collectors.toList());
            dealGroupDTO.setDeals(deals);
        }
    }
    /**
     * 判断是否是多次卡
     */
    public static boolean isMultiTimesCard(DealCtx dealCtx) {
        return dealCtx != null
                && dealCtx.getDealGroupDTO() != null
                && dealCtx.getDealGroupDTO().getBasic() != null
                && dealCtx.getDealGroupDTO().getBasic().getTradeType() != null
                && dealCtx.getDealGroupDTO().getBasic().getTradeType().equals(19);
    }

    /**
     * 获取多次卡团详title文案
     */
    public static String getMultiTimesCardTitle(DealCtx dealCtx) {
        if (dealCtx == null || dealCtx.getDealGroupDTO() == null || CollectionUtils.isEmpty(dealCtx.getDealGroupDTO().getDeals())) {
            return null;
        }

        Integer times = getTimes(dealCtx);
        if (times == null || times <= 0) {
            return null;
        }

        return String.format("每次套餐详情（共%s次）", times);
    }

    /**
     * 获取多次卡次数
     */
    public static Integer getTimes(DealCtx dealCtx) {
        if (dealCtx == null || dealCtx.getDealGroupDTO() == null || CollectionUtils.isEmpty(dealCtx.getDealGroupDTO().getDeals())) {
            return null;
        }
        DealGroupDealDTO dealGroupDealDTO = dealCtx.getDealGroupDTO().getDeals().get(0);
        if (dealGroupDealDTO == null || CollectionUtils.isEmpty(dealGroupDealDTO.getAttrs())) {
            return null;
        }
        Optional<AttrDTO> attrDTOOptional = dealGroupDealDTO.getAttrs().stream().filter(
                        attributeDTO -> attributeDTO != null
                                && "sys_multi_sale_number".equals(attributeDTO.getName())
                                && !CollectionUtils.isEmpty(attributeDTO.getValue())
                                && !"0".equals(attributeDTO.getValue().get(0)))
                .findFirst();
        return attrDTOOptional.map(attrDTO -> Integer.valueOf(attrDTO.getValue().get(0))).orElse(null);
    }

    /**
     * 次卡->连续包月
     * @param dealCtx
     * @return
     */
    public static Boolean isMonthlySubscription(DealCtx dealCtx) {
        boolean isMonthlySubscription = false;
        if(isMultiTimesCard(dealCtx)){
            List<AttributeDTO> attrs = dealCtx.getAttrs();
            if(CollectionUtils.isEmpty(attrs)){
                return false;
            }
            List<String> sysMultiSaleTypeAttr = AttributeUtils.getAttributeValues(DealAttrKeys.CONTINUOUS_MONTHLY_SUBSCRIPTION, attrs);
            if(CollectionUtils.isNotEmpty(sysMultiSaleTypeAttr)
                    && MONTHLY_SUBSCRIPTION.equals(sysMultiSaleTypeAttr.get(0))){
                isMonthlySubscription = true;
            }
        }
        return isMonthlySubscription;
    }

    /**
     * 老团详加多次卡标题
     */
    public static String getTimesTitleToHtmlIfNecessary(String html, DealCtx dealCtx) {
        if (dealCtx == null || dealCtx.getEnvCtx() == null || StringUtils.isEmpty(dealCtx.getEnvCtx().getUserAgent()) || StringUtils.isEmpty(html)) {
            return html;
        }
        String userAgent = dealCtx.getEnvCtx().getUserAgent();
        if (!userAgent.contains("Android")) {
            return html;
        }
        // 美团不处理富文本
        DztgClientTypeEnum dztgClientTypeEnum = dealCtx.getEnvCtx().getDztgClientTypeEnum();
        if (dztgClientTypeEnum == null
                || DztgClientTypeEnum.MEITUAN_APP.equals(dztgClientTypeEnum)
                || DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP.equals(dztgClientTypeEnum)
                || DztgClientTypeEnum.MEITUAN_KUAISHOU_MINIAPP.equals(dztgClientTypeEnum)
                || DztgClientTypeEnum.MEITUAN_WANWU_MINIAPP.equals(dztgClientTypeEnum)) {
            return html;
        }
        String title = String.format("<h3>每次套餐详情（共%s次）</h3>\n", getTimes(dealCtx));
        return title + html;
    }

}
