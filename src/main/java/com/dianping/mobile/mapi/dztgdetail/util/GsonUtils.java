package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.cat.Cat;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Type;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/2/24.
 */
@Slf4j
public class GsonUtils {

    public static Gson GSON = new Gson();

    public static String toJsonString(Object object) {
        return GSON.toJson(object);
    }

    public static<T> T fromJsonString(String objectStr, Class<T> clazz) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.util.GsonUtils.fromJsonString(java.lang.String,java.lang.Class)");
        return StringUtils.isEmpty(objectStr) ? null : GSON.fromJson(objectStr, clazz);
    }

    public static<T> T fromJsonString(String objectStr, Type type) {
        return StringUtils.isEmpty(objectStr) ? null : GSON.fromJson(objectStr, type);
    }

    public static<T, E> T getParamFromMapJson(String mapJson, E key, Class<T> clazz) {
        try {
            if (StringUtils.isEmpty(mapJson) || key == null || JsonParser.parseString(mapJson) == null) {
                return null;
            }
            JsonObject jsonObject = JsonParser.parseString(mapJson).getAsJsonObject();
            JsonElement jsonElement = jsonObject.get(key.toString());
            if (jsonElement == null) {
                return null;
            }
            return GSON.fromJson(jsonElement, clazz);
        } catch (Exception e) {
            log.error("[GsonUtils] getParamFromMapJson error, mapJson={}", mapJson, e);
        }
        return null;
    }
}
