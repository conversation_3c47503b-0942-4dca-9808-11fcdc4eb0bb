package com.dianping.mobile.mapi.dztgdetail.rcf.domian;

import com.dianping.mobile.mapi.dztgdetail.rcf.api.req.DealNativeSnapshotReq;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.dto.DealBffResponseDTO;

/**
 * @Author: guangyujie
 * @Date: 2024/11/26 19:08
 */
public interface DealRcfCustomerProcessor {

    void customerProcess(final DealNativeSnapshotReq request,
                         final DealBffResponseDTO bffResponse);

    boolean canProcess(final DealNativeSnapshotReq request,
                       final DealBffResponseDTO bffResponse);

}
