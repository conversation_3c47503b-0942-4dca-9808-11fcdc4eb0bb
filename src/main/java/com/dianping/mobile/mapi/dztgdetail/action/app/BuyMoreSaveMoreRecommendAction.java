package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.BuyMoreSaveMoreReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.BuyMoreSaveMoreVO;
import com.dianping.mobile.mapi.dztgdetail.facade.BuyMoreSaveMoreFacade;
import com.dianping.mobile.mapi.dztgdetail.util.AntiCrawlerUtils;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.HttpMethod;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;
import java.util.Objects;

import static com.dianping.mobile.mapi.dztgdetail.util.AntiCrawlerUtils.antiUnauthenticLogin;

/**
 * @Author: <EMAIL>
 * @Date: 2023/7/25
 */
@InterfaceDoc(displayName = "查询多买多省搭售推荐信息",
        type = "restful",
        description = "查询多买多省搭售推荐信息",
        scenarios = "该接口仅适用于双平台APP站点的团购详情页搭售模块",
        host = "http://mapi.dianping.com/general/platform/getBuySaveMoreRecommendInfo/",
        authors = "zhengjie27"
)
@Controller("general/platform/dztgdetail/getbuysavemorerecommendinfo.bin")
@Action(url = "getbuysavemorerecommendinfo.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class BuyMoreSaveMoreRecommendAction extends AbsAction<BuyMoreSaveMoreReq> {

    @Autowired
    private BuyMoreSaveMoreFacade buyMoreSaveMoreFacade;

    @Override
    protected IMobileResponse validate(BuyMoreSaveMoreReq request, IMobileContext iMobileContext) {
        IdUpgradeUtils.processProductIdForBuyMoreSaveMoreReq(request, "getbuysavemorerecommendinfo.bin");
        if(request == null || request.getDealGroupId() <= 0){
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "getbuysavemorerecommendinfo.bin",
            displayName = "查询多买多省搭售推荐信息",
            description = "查询多买多省搭售推荐信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "getbuysavemorerecommendinfo.bin请求参数",
                            type = BuyMoreSaveMoreReq.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "result", description = "团购picasso数据",type = BuyMoreSaveMoreVO.class)},
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    ),
            }
    )
    @Override
    @CryptMethod
    protected IMobileResponse execute(BuyMoreSaveMoreReq request, IMobileContext iMobileContext) {
        try {
            EnvCtx envCtx = initEnvCtx(iMobileContext);
            // 拦截没有授权的登录
            antiUnauthenticLogin(iMobileContext);
            BuyMoreSaveMoreVO result = buyMoreSaveMoreFacade.getRecommendCombineDeal(request, envCtx);
            // 反爬信息处理
            hideKeyInfo(result, iMobileContext);
            if (result != null) {
                return new CommonMobileResponse(result);
            }
            return new CommonMobileResponse(Resps.NoDataResp);
        } catch (Exception e) {
            logger.error("getbuysavemorerecommendinfo.bin error",e);
        }
        return Resps.SYSTEM_ERROR;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
    
    private void hideKeyInfo(BuyMoreSaveMoreVO result, IMobileContext ctx) {
        if (Objects.isNull(result) || CollectionUtils.isEmpty(result.getCardList())) {
            return;
        }
        if (!AntiCrawlerUtils.hide(ctx)) {
            return;
        }
        result.getCardList().forEach(card -> {
            // 主品到手价
            card.setMainDealPrice(StringUtils.EMPTY);
            // 搭售品到手价
            card.setBindingDealPrice(StringUtils.EMPTY);
            // 组合价格
            card.setCardPrice(StringUtils.EMPTY);
            // 组合价格文案
            card.setCardPriceText(StringUtils.EMPTY);
        });
    }
}
