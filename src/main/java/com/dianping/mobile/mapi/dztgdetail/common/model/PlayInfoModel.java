package com.dianping.mobile.mapi.dztgdetail.common.model;

import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/4/17 15:37
 */
@Data
public class PlayInfoModel {

    private String title;

    private String subTitle;

    private Long activityId;

    private long startTime;

    private long endTime;

    private List<MaterialInfoModel> materialInfoList;

    private List<TaskInfoModel> taskInfoList;

    /**
     * 查找素材
     * @param attrName
     * @param defaultValue
     * @return
     */
    public String getAttr(String attrName, String defaultValue) {
        if (CollectionUtils.isEmpty(materialInfoList)) {
            return defaultValue;
        }
        return materialInfoList.stream().filter(material -> attrName.equals(material.getFieldKey()))
                .map(MaterialInfoModel::getFieldValue).findFirst().orElse(defaultValue);
    }
}
