package com.dianping.mobile.mapi.dztgdetail.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-04-07
 * @desc 适用门店展示策略
 */
@Data
public class ApplyShopPositionStrategy {
    /**
     * 渠道来源的策略，
     * ignore - 忽略此项
     * allow - 在此配置中生效
     * deny - 不在此配置中生效
     */
    private String pageSourceStrategy;
    /**
     * 团详clientType的策略，
     * ignore - 忽略此项
     * allow - 在此配置中生效
     * deny - 不在此配置中生效
     */
    private String dztgClientTypeStrategy;
    /**
     * 团单一级类目的策略，
     * ignore - 忽略此项
     * allow - 在此配置中生效
     * deny - 不在此配置中生效
     */
    private String channelIdStrategy;
    /**
     * 团单二级类目的策略，
     * ignore - 忽略此项
     * allow - 在此配置中生效
     * deny - 不在此配置中生效
     */
    private String categoryIdStrategy;

}
