package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.deal.book.req.BookQueryRequest;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.deal.common.enums.PageType;
import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.LionUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.CsCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealBookWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DzImWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MiniProgramSceneEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MpAppIdEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.AppCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.SwitchHelper;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

public class ShopBookProcessor extends AbsDealProcessor {

    @Autowired
    private DzImWrapper dzImWrapper;

    @Autowired
    private DealBookWrapper dealBookWrapper;

    @Autowired
    private CsCenterWrapper csCenterWrapper;

    @Override
    public void prepare(DealCtx ctx) {
        BestShopDTO bestShop = ctx.getBestShopResp();
        if (bestShop == null || bestShop.getDpShopId() <= 0) {
            return;
        }
        long dpPoiid = bestShop.getDpShopId();
//        ctx.getFutureCtx().setDealBookFuture(dealBookWrapper.prepareDealBooKInfo(ctx)); //原先作用，根据门店id以及团单id查询是否可预约
//        //后续如果业务不用可以删除
//        if (isNeedBook((ctx))) { //原先作用，批量查询商户团购预约信息
//            ctx.getFutureCtx().setBookFuture(dealBookWrapper.prepareShopDealBooKInfo(buildBookReq(ctx, bestShop)));
//        }

        boolean categoryImEnable = hasImFunction(ctx);
        boolean sceneEnable = !ctx.isExternal() || ctx.getEnvCtx().isExternalAndEnabled(MiniProgramSceneEnum.IM_URL.getPromoScene());

        if (categoryImEnable && sceneEnable) {
            int clientType = ctx.getEnvCtx().getClientType();

            if(ctx.isExternal() &&
                    (Objects.equals(ctx.getEnvCtx().getMpAppId(), MpAppIdEnum.MT_WEIXIN_MINIPROGRAM.getMpAppId())
                            || AppCtxHelper.isKuaiShouMiniProgram(ctx.getEnvCtx()))) {

                clientType = ClientTypeEnum.mt_weApp.getType();

            }

            // LE保洁自营/无忧通 使用太平洋自营智能门户
            if ((LionUtils.needReplaceImToCsForCleaning() && ctx.isCleaningSelfOwnDeal())
                    || (LionUtils.needReplaceImToCsForCarefree() && ctx.isCareFreeDeal())) {
                ctx.getFutureCtx().setCsFuture(csCenterWrapper.prepareAccessIn(ctx));
            } else {
                ctx.getFutureCtx().setImFuture(dzImWrapper.preOnlineConsultUrl(dpPoiid, ctx.getDpId(), clientType));
            }
        }
    }

    @Override
    public void process(DealCtx ctx) {
        BestShopDTO bestShop = ctx.getBestShopResp();
        if (bestShop == null || bestShop.getDpShopId() <= 0) {
            return;
        }
//        ctx.setPurchaseCanBook(dealBookWrapper.queryDealBooKInfo(ctx, ctx.getFutureCtx().getDealBookFuture()));
        // LE保洁自营/无忧通 使用太平洋自营智能门户
        if ((LionUtils.needReplaceImToCsForCleaning() && ctx.isCleaningSelfOwnDeal())
                || (LionUtils.needReplaceImToCsForCarefree() && ctx.isCareFreeDeal())){
            ctx.setImUrl(csCenterWrapper.getCsCenterUrl(ctx.getFutureCtx().getCsFuture()));
        } else {
            String onlineConsultUrl = dzImWrapper.getOnlineConsultUrl(ctx.getFutureCtx().getImFuture());
            ctx.setImUrl(getOnlineConsultUrl(ctx, onlineConsultUrl));
        }
        long poiId = ctx.isMt() ? bestShop.getMtShopId() : bestShop.getDpShopId();
        ctx.setShopBookDto(dealBookWrapper.queryShopDealBooKInfo(poiId, ctx.getFutureCtx().getBookFuture()));
    }

    private String getOnlineConsultUrl(DealCtx ctx, String originOnlineConsultUrl) {
        if (StringUtils.isBlank(originOnlineConsultUrl)) {
            return originOnlineConsultUrl;
        }

        if (AppCtxHelper.isKuaiShouMiniProgram(ctx.getEnvCtx())) {
            String encodeUrl;

            try {
                encodeUrl = URLEncoder.encode(originOnlineConsultUrl, StandardCharsets.UTF_8.name());
            } catch (UnsupportedEncodingException e) {
                logger.error("originOnlineConsultUrl={},encode报错", originOnlineConsultUrl, e);
                return null;
            }

            return "/index/pages/h5/h5?f_token=1&noshare=1&weburl=" + encodeUrl;
        }

        return originOnlineConsultUrl;
    }

    private boolean hasImFunction(DealCtx ctx) {
        if (ctx.getChannelDTO() == null) {
            return false;
        }
        return SwitchHelper.isIm(ctx.getChannelDTO().getChannelDTO().getChannelId())
                || SwitchHelper.isCategoryIm(ctx.getChannelDTO().getCategoryId());
    }

    private boolean isNeedBook(DealCtx ctx) {
        return DealAttrHelper.needReservation(ctx.getAttrs()) && SwitchHelper.isInBook(ctx.getCategoryId());
    }

    private BookQueryRequest buildBookReq(DealCtx ctx, BestShopDTO bestShop) {
        BookQueryRequest request = new BookQueryRequest();
        long poiId = ctx.isMt() ? bestShop.getMtShopId() : bestShop.getDpShopId();
        request.setShopIdsLong(Lists.newArrayList(poiId));
        request.setIdIsMt(ctx.isMt()); //ID系统全部使用点评的
        request.setPageType(PageType.DEAL_DETAIL.getType());
        request.setClientType(ctx.getEnvCtx().getClientType());
        return request;
    }

}
