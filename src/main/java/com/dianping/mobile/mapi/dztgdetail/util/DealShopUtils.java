package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.entity.ShopListConfig;
import com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

public class DealShopUtils {

    private static final String USING_NEW_PAGE_LION_KEY = "using.new.shop.list";
    private static final String DP_OLD_NATIVE = "using.dp.old.native";
    private static final String DP_NEW_MRN_MIN_VERSION = "dp.new.mrn.min.version";
    private static final String SHOP_LIST_URL_SCHEMA = "shop.list.url.schema";
    private static final String MT_MINI_PREFIX = "/index/pages/h5/h5?f_token=1&weburl=";
    private static final String DP_MINI_PREFIX = "/pages/webview/webview?url=";
    private static final String DP_MINI_OLD_NATIVE = "/packages/tuan/pages/tuanshoplist/tuanshoplist?dealGroupId=";

    public static String getShopListUrl(DealCtx ctx, Long shopId) {
        if (usingDpOldNative(ctx) || isOldDpMrnVersion(ctx)) {
            return getDpOldNativeUrl(ctx, shopId);
        }
        return String.format("%s%s", getPrefix(ctx), getUrl(ctx, shopId));
    }

    private static boolean usingDpOldNative(DealCtx ctx) {
        return !ctx.isMt() && !usingNewPage() && Lion.getBoolean("com.sankuai.dzu.tpbase.dztgdetailweb", DP_OLD_NATIVE, true);
    }

    // 点评APP兼容老bundle特殊逻辑
    private static boolean isOldDpMrnVersion(DealCtx ctx) {
        if (ctx.getEnvCtx().getDztgClientTypeEnum() != DztgClientTypeEnum.DIANPING_APP) {
            return false;
        }
        if (StringUtils.isBlank(ctx.getMrnVersion())) {
            return true;
        }
        return VersionUtils.isLessAndEqualThan(ctx.getMrnVersion(), Lion.getString("com.sankuai.dzu.tpbase.dztgdetailweb", DP_NEW_MRN_MIN_VERSION, "0.5.9"));
    }

    private static String getDpOldNativeUrl(DealCtx ctx, Long shopId) {
        if (ctx.getEnvCtx().getDztgClientTypeEnum() == DztgClientTypeEnum.DIANPING_APP) {
            return UrlHelper.getShopListUrl(ctx, shopId);
        }
        if (ctx.getEnvCtx().getDztgClientTypeEnum() == DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP) {
            return String.format("%s%d", DP_MINI_OLD_NATIVE, ctx.getDpId());
        }
        return "";
    }

    private static boolean usingNewPage() {
        return Lion.getBoolean("com.sankuai.dzu.tpbase.dztgdetailweb", USING_NEW_PAGE_LION_KEY, false);
    }

    private static boolean usingBackendUrl(DztgClientTypeEnum dztgClientTypeEnum) {
        return DztgClientTypeEnum.MEITUAN_APP.equals(dztgClientTypeEnum) ||
                DztgClientTypeEnum.DIANPING_APP.equals(dztgClientTypeEnum) ||
                DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP.equals(dztgClientTypeEnum) ||
                DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP.equals(dztgClientTypeEnum);
    }

    private static String getUrl(DealCtx ctx, Long shopId) {
        ShopListConfig config = Lion.getBean("com.sankuai.dzu.tpbase.dztgdetailweb", SHOP_LIST_URL_SCHEMA, ShopListConfig.class);
        if (config == null) {
            return "";
        }
        if (!usingBackendUrl(ctx.getEnvCtx().getDztgClientTypeEnum())) {
            return "";
        }
        String page = getPageUrl(ctx, config);
        String params = buildParams(ctx, shopId, config);
        String url = String.format("%s%s", page, params);
        return ctx.getEnvCtx().isWxMini() ? NetUtils.encode(url) : url;
    }


    private static String getPrefix(DealCtx ctx) {
        if (ctx.getEnvCtx().getDztgClientTypeEnum() == DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP) {
            return DP_MINI_PREFIX;
        }
        if (ctx.getEnvCtx().getDztgClientTypeEnum() == DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP) {
            return MT_MINI_PREFIX;
        }
        return "";
    }


    private static String buildParams(DealCtx ctx, Long shopId, ShopListConfig config) {
        String schema = config.getParamsSchema();
        if (StringUtils.isBlank(schema)) {
            return "";
        }
        boolean isMt = ctx.isMt();
        Map<String, String> paramsMap = Maps.newHashMap();
        paramsMap.put("dealid", String.valueOf(isMt ? ctx.getMtId() : ctx.getDpId()));
        paramsMap.put("poiid", String.valueOf(shopId));
        paramsMap.put("frompage", StringUtils.isNotBlank(ctx.getRequestSource()) ? ctx.getRequestSource() : "");
        paramsMap.put("cityId", String.valueOf(isMt ? ctx.getMtCityId() : ctx.getDpCityId()));
        paramsMap.put("locatedCityId", String.valueOf(ctx.getGpsCityId()));
        paramsMap.put("lat", String.valueOf(ctx.getUserlat()));
        paramsMap.put("lng", String.valueOf(ctx.getUserlng()));
        paramsMap.put("mmcinflate", MagicFlagUtils.toString(ctx.getMmcInflate()));
        paramsMap.put("mmcuse", MagicFlagUtils.toString(ctx.getMmcUse()));
        paramsMap.put("mmcbuy", MagicFlagUtils.toString(ctx.getMmcBuy()));
        paramsMap.put("mmcfree", MagicFlagUtils.toString(ctx.getMmcFree()));
        paramsMap.put("offlinecode", ctx.getOfflineCode());
        String url = "";
        for (Map.Entry<String, String> entry : paramsMap.entrySet()) {
            url = StringUtils.replace(schema, String.format("{%s}", entry.getKey()), StringUtils.isNotBlank(entry.getValue()) ? entry.getValue() : "");
            schema = url;
        }
        return url;
    }

    private static String getPageUrl(DealCtx ctx, ShopListConfig config) {
        boolean isMt = ctx.isMt();
        boolean isApp = ctx.getEnvCtx().isNative();
        boolean usingNew = usingNewPage();
        if (usingNew) {
            return isApp ? (isMt ? config.getMtAppUrl() : config.getDpAppUrl()) : (isMt ? config.getMtMiniApp() : config.getDpMiniApp());
        }
        return isApp ? (isMt ? config.getMtAppUrlOld() : config.getDpAppUrlOld()) : (isMt ? config.getMtMiniAppOld() : config.getDpMiniAppOld());
    }

}
