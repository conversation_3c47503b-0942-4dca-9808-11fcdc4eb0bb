package com.dianping.mobile.mapi.dztgdetail.button.joy;

import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.SaleStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtnIcon;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo.CouponDescItem;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.PriceHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.PromoHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.CouponAssignStatusEnum;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class NewNormalButtonBuilder extends AbstractPriceServiceButtonBuilder {

    protected static final Map<Integer, String> BUTTON_NAME_MAP = ImmutableMap.of(
            BuyBtnTypeEnum.TIMES_CARD.getCode(), "购买1次",
            BuyBtnTypeEnum.PINTUAN.getCode(), "单独购买",
            BuyBtnTypeEnum.MEMBER_CARD.getCode(), "团购价",
            BuyBtnTypeEnum.JOY_CARD.getCode(), "团购价"
    );
    protected static final String HAD_COUPON_PREFIX = "用券";
    protected static final String NO_COUPON_PREFIX = "领券";
    protected static final String BUY = "抢购";

    @Override
    public PriceDisplayDTO getPrice(DealCtx context) {
        return PriceHelper.getNormalPrice(context);
    }

    @Override
    public void afterBuild(DealCtx context, DealBuyBtn button) {
        buildCouponAB(context, button);
        buildBtnTitle(context, button);
        buildSaleStatus(context, button);
        buildBtnEnable(context, button);
        buildNewNormalBtnTagAndCardIcon(context, button);
        if (DealBuyHelper.isCouponBar(context)) {
            button.setBtnTitle(CouponBarHelper.getCouponBtnTitle(context));
        }
    }

    private void buildBtnEnable(DealCtx context, DealBuyBtn button) {
        if(StringUtils.isNotBlank(context.getSaleStatus())){
            if(context.getSaleStatus().equals(SaleStatusEnum.SNAP_UP_NOW.saleStatusName)){
                button.setBtnEnable(true);
            }else{
                button.setBtnEnable(false);
            }
        }
    }

    private void buildSaleStatus(DealCtx context, DealBuyBtn button) {
        //若是预热单，更新title&&设置按钮的售卖状态
        if(StringUtils.isNotBlank(context.getSaleStatus())){
            button.setSaleStatus(context.getSaleStatus());
            button.setBtnTitle(getSaleStatusButtonTitle(context));
        }
    }

    private void buildNewNormalBtnTagAndCardIcon(DealCtx context, DealBuyBtn button) {
        /**
         * 团购button底色、标签文案补充
         */
        if (context.getPriceContext().isZuLiaoButtonNewStyle()) {
            if (button.getDetailBuyType() == BuyBtnTypeEnum.NORMAL_DEAL.getCode()) {
                PriceDisplayDTO normalPrice = context.getPriceContext().getNormalPrice();
                boolean isNewUser = calNewUser(normalPrice);
                if (normalPrice != null && normalPrice.getPromoAmount()!=null) {
                    String btnTag;
                    if (isNewUser){
                        btnTag = "新客共省¥" + PriceHelper.dropLastZero(normalPrice.getPromoAmount());
                    }else {
                        btnTag = "共省¥" + PriceHelper.dropLastZero(normalPrice.getPromoAmount());
                    }
                    button.setBtnTag(btnTag);
                }

                List<DealBuyBtnIcon> btnIcons = button.getBtnIcons();
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(btnIcons)) {
                    if (normalPrice != null && normalPrice.getMarketPrice() != null) {
                        String promoIconName;
                        if (isNewUser){
                            promoIconName = "新客共省¥"
                                    + normalPrice.getMarketPrice().subtract(new BigDecimal(button.getPriceStr())).setScale(1, RoundingMode.CEILING)
                                    .stripTrailingZeros().toPlainString();
                        }else {
                            promoIconName = "共省¥"
                                    + normalPrice.getMarketPrice().subtract(new BigDecimal(button.getPriceStr())).setScale(1, RoundingMode.CEILING)
                                    .stripTrailingZeros().toPlainString();
                        }
                        DealBuyBtnIcon newNormalCardIcon = DealBuyHelper.getNewNormalCardIcon(context, promoIconName);
                        btnIcons.add(0, newNormalCardIcon);
                    }
                }
            }
        }
    }

    private boolean calNewUser(PriceDisplayDTO normalPrice) {
        if (normalPrice == null || CollectionUtils.isEmpty(normalPrice.getUsedPromos())){
            return false;
        }
        for (PromoDTO promoDTO : normalPrice.getUsedPromos()){
            if (promoDTO == null){
                continue;
            }
            if (promoDTO.isNewUser()){
                return true;
            }
        }
        return false;
    }

    private void buildBtnTitle(DealCtx context, DealBuyBtn button) {
        if (Objects.equals(context.getEnvCtx().getDztgClientTypeEnum(), DztgClientTypeEnum.DIANPING_BAIDUMAP_MINIAPP)) {
            List<PromoDTO> couponPromos = PromoHelper.getCouponPromos(context);
            if (CollectionUtils.isNotEmpty(couponPromos)) {
                //百度地图小程序抢购按钮逻辑固定为「XX抢购」，任何一张券可用就展示用券抢购
                button.setBtnTitle((couponPromos.stream().anyMatch(item -> !item.isCanAssign()) ?  HAD_COUPON_PREFIX : NO_COUPON_PREFIX) + BUY);
                return;
            }
        }

        if (context.getPreButton() == null) {
            return;
        }
        String title = BUTTON_NAME_MAP.get(context.getPreButton().getDetailBuyType());
        if (StringUtils.isNotBlank(title)) {
            button.setBtnTitle(getButtonTitle(context, title));
        }
    }

    protected void buildCouponAB(DealCtx context, DealBuyBtn buyBtn) {
        List<PromoDTO> couponPromos = PromoHelper.getCouponUsePromo(context);
        if (CollectionUtils.isEmpty(couponPromos)) {
            return;
        }
        for (PromoDTO couponPromo : couponPromos){
            if (couponPromo.getIdentity().getPromoType() != PromoTypeEnum.COUPON.getType() || !PromoHelper.canAssign(couponPromo)) {
                continue;
            }
            if(PromoHelper.isCouponPurchase(couponPromo)){
                continue;
            }
            CouponDescItem coupon = new CouponDescItem();
            coupon.setCouponGroupId((int) couponPromo.getIdentity().getPromoId());
            coupon.setUnifiedcoupongroupids(String.valueOf(couponPromo.getIdentity().getPromoId()));
            buyBtn.setCoupon(coupon);
        }
    }

}
