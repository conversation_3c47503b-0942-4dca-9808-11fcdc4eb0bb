package com.dianping.mobile.mapi.dztgdetail.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2023/8/21
 * @since mapi-dztgdetail-web
 */
public class ObjectCompareUtils {

    public static void compareNum(long i, long j) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.util.ObjectCompareUtils.compareNum(long,long)");
        if (i != j) {
            throw new RuntimeException("not equal");
        }
    }
    public static void compareString(String i, String j) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.util.ObjectCompareUtils.compareString(java.lang.String,java.lang.String)");
        if (!StringUtils.equals(i, j)) {
            throw new RuntimeException("not equal");
        }
    }
    public static void compareBigDecimal(BigDecimal i, BigDecimal j) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.util.ObjectCompareUtils.compareBigDecimal(java.math.BigDecimal,java.math.BigDecimal)");
        if (i == null && j == null) {
            return;
        }
        if (i == null || j == null || i.compareTo(j) != 0) {
            throw new RuntimeException("not equal");
        }
    }
    public static void compareListSimple(List<?> list1, List<?> list2) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.util.ObjectCompareUtils.compareListSimple(java.util.List,java.util.List)");
        if (list1 == null && list2 == null) {
            return;
        }
        if (list1 == null || list2 == null || list1.size() != list2.size()) {
            throw new RuntimeException("not equal");
        }
        if (!CollectionUtils.isEqualCollection(list1, list2)) {
            throw new RuntimeException("not equal");
        }
    }
    public static void compareList(List<?> list1, List<?> list2) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.util.ObjectCompareUtils.compareList(java.util.List,java.util.List)");
        if (list1 == null && list2 == null) {
            return;
        }
        if (list1 == null || list2 == null || list1.size() != list2.size()) {
            throw new RuntimeException("not equal");
        }
        JSONArray jsonArray1 = JSON.parseArray(JSON.toJSONString(list1));
        JSONArray jsonArray2 = JSON.parseArray(JSON.toJSONString(list1));
        compareJsonArray(jsonArray1, jsonArray2);
    }
    public static void compareObject(Object ob1, Object ob2) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.util.ObjectCompareUtils.compareObject(java.lang.Object,java.lang.Object)");
        if (ob1 == null && ob2 == null) {
            return;
        }
        if (ob1 == null || ob2 == null) {
            throw new RuntimeException("not equal");
        }
        JSONObject jsonObject1 = JSON.parseObject(JSON.toJSONString(ob1));
        JSONObject jsonObject2 = JSON.parseObject(JSON.toJSONString(ob2));
        compareJsonObject(jsonObject1, jsonObject2);
    }
    /**
     * 比较两个jsonObject对象是否相等
     *
     * @param ob1
     * @param ob2
     */
    private static void compareJsonObject(JSONObject ob1, JSONObject ob2) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.util.ObjectCompareUtils.compareJsonObject(com.alibaba.fastjson.JSONObject,com.alibaba.fastjson.JSONObject)");
        if (ob1 == null && ob2 == null) {
            return;
        }
        if (ob1 == null || ob2 == null) {
            throw new RuntimeException("not equal");
        }
        for (String key : ob1.keySet()) {
            Object value1 = ob1.get(key);
            Object value2 = ob2.get(key);
            if (value1 instanceof JSONObject) {
                compareJsonObject((JSONObject) value1, (JSONObject) value2);
            } else if (value1 instanceof JSONArray) {
                compareJsonArray((JSONArray) value1, (JSONArray) value2);
            } else {
                String str1 = String.valueOf(value1);
                String str2 = String.valueOf(value2);
                if (!StringUtils.equals(str1, str2)) {
                    throw new RuntimeException("not equal");
                }
            }
        }
    }
    /**
     * 比较两个jsonArray列表是否相等
     *
     * @param jsonArray1
     * @param jsonArray2
     */
    private static void compareJsonArray(JSONArray jsonArray1, JSONArray jsonArray2) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.util.ObjectCompareUtils.compareJsonArray(com.alibaba.fastjson.JSONArray,com.alibaba.fastjson.JSONArray)");
        if (jsonArray1 == null && jsonArray2 == null) {
            return;
        }
        if (jsonArray1 == null || jsonArray2 == null || jsonArray1.size() != jsonArray2.size()) {
            throw new RuntimeException("not equal");
        }
        for (int i = 0; i < jsonArray1.size(); i++) {
            Object value1 = jsonArray1.get(i);
            Object value2 = jsonArray2.get(i);
            if (value1 instanceof JSONObject) {
                compareJsonObject((JSONObject) value1, (JSONObject) value2);
            } else if (value1 instanceof JSONArray) {
                compareJsonArray((JSONArray) value1, (JSONArray) value2);
            } else {
                String str1 = String.valueOf(value1);
                String str2 = String.valueOf(value2);
                if (!StringUtils.equals(str1, str2)) {
                    throw new RuntimeException("not equal");
                }
            }
        }
    }

}
