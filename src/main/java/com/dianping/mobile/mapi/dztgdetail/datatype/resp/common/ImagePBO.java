package com.dianping.mobile.mapi.dztgdetail.datatype.resp.common;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;

@TypeDoc(description = "复杂图片类型")
@MobileDo(id = 0x7d1f)
public class ImagePBO implements Serializable {

    @FieldDoc(description = "图片地址")
    @MobileField(key = 0xaed7)
    private String imageUrl;

    @FieldDoc(description = "图片标题")
    @MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "图片描述")
    @MobileField(key = 0xfebf)
    private String desc;

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
