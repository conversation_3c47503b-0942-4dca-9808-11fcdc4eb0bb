package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DisplayControlWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.sankuai.beautycontent.security.displaycontrol.response.DisplayControlResponse;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;

@Deprecated
public class DisplayControlProcessor extends AbsDealProcessor {

    @Autowired
    private DisplayControlWrapper displayControlWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        boolean isEnableTitlePrefix = Lion.getBoolean("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.enable.edu.title.prefix", false);//配置是false

        List<Integer> eduAgeCategoryId = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.edu.age.category.list", Integer.class, new ArrayList<>());

        return isEnableTitlePrefix && eduAgeCategoryId.contains(ctx.getCategoryId());
    }

    @Override
    public void prepare(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DisplayControlProcessor.prepare(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        int dealGroupId4P = ctx.isMt() ? ctx.getMtId() : ctx.getDpId();
        long shopId4P = getShopId4P(ctx);
        Future displayControlFuture = displayControlWrapper.preDisplayCheck(dealGroupId4P, ctx.getCityId4P(), shopId4P, ctx.isMt());
        ctx.getFutureCtx().setDisplayControlFuture(displayControlFuture);
    }

    @Override
    public void process(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DisplayControlProcessor.process(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        DisplayControlResponse displayControlResponse = displayControlWrapper.getDisplayCheckResponse(ctx.getFutureCtx().getDisplayControlFuture());
        ctx.setDisplayControlResponse(displayControlResponse);
    }

    private long getShopId4P(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DisplayControlProcessor.getShopId4P(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        return ctx.getLongPoiId4PFromResp();
    }

}
