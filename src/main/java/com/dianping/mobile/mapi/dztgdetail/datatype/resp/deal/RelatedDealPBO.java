package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2024/10/30
 */
@Data
@TypeDoc(description = "推荐团单模型")
@MobileDo(id = 0x3dba)
public class RelatedDealPBO implements Serializable {
    @FieldDoc(description = "点评团单ID")
    @MobileDo.MobileField(key = 0x22e8)
    private int dpId;

    @FieldDoc(description = "美团团单ID")
    @MobileDo.MobileField(key = 0x4911)
    private int mtId;

    @FieldDoc(description = "适用商户")
    @MobileDo.MobileField(key = 0xdac3)
    private ShopPBO shop;

    @FieldDoc(description = "标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "销量描述")
    @MobileDo.MobileField(key = 0xc426)
    private String saleDesc;

    @FieldDoc(description = "团单展示资源数组")
    @MobileDo.MobileField(key = 0x31fd)
    private List<ContentPBO> dealContents;

    @FieldDoc(description = "优惠详情模块")
    @MobileDo.MobileField(key = 0xb8)
    private PromoDetailModule promoDetailModule;

    @FieldDoc(description = "团详链接")
    @MobileDo.MobileField(key = 0x8d3b)
    private String detailUrl;

    @FieldDoc(description = "召回来源，1-相似好价，2-广告-CPT，3-广告-CPV")
    @MobileDo.MobileField(key = 0x7481)
    private Integer recallType;

    @FieldDoc(description = "角标模型")
    @MobileDo.MobileField(key = 0xb84d)
    private List<ItemTextInfo> itemTextInfos = new ArrayList<>();

    @FieldDoc(description = "推荐理由")
    @MobileDo.MobileField(key = 0x1e9a)
    private RelatedShopRecommendInfoPBO recommendReasonInfo;

    @FieldDoc(description = "标签信息（底部红框）")
    @MobileDo.MobileField(key = 0x342f)
    private List<String> tags;
}
