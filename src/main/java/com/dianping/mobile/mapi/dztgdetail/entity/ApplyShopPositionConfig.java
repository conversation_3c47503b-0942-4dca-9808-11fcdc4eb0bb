package com.dianping.mobile.mapi.dztgdetail.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-07
 * @desc 适用门店位置配置
 */
@Data
public class ApplyShopPositionConfig {
    /**
     * 一级团单类目
     */
    private List<Integer> channelIds;
    /**
     * 二级团单类目
     */
    private List<Integer> categoryIds;
    /**
     * 团单渠道来源
     */
    private List<String> pageSource;
    /**
     * 团详定义的client
     */
    private List<String> dztgClientType;

    /**
     * 适用门店位置；1-团购详情模块下方；2-团购详情模块上方
     */
    private Integer displayPosition;

    /**
     * 适用门店位置展示策略
     */
    private ApplyShopPositionStrategy applyShopPositionStrategy;
}
