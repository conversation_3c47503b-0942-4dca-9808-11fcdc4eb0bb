package com.dianping.mobile.mapi.dztgdetail.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024-05-06
 * @desc 门店类型枚举
 */
@Getter
public enum ShopCategoryEnum {
    UNKNOWN(0, "未知"),
    /**
     * 穿戴甲专卖店
     */

    WEARABLE_NAIL_ONLY(1, "穿戴甲专卖店"),
    /**
     * 穿戴甲寄售店
     */

    WEARABLE_NAIL_RETAIL(2, "穿戴甲寄售店"),
    ;

    private final int code;
    private final String desc;

    ShopCategoryEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
