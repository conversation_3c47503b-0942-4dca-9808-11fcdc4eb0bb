package com.dianping.mobile.mapi.dztgdetail.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cip.growth.mana.api.dto.response.UserManaDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.enums.PicVideoStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.ShopReviewCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.ReviewDetailDO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.ReviewExtTagDO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.ReviewPicDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.ReviewUserModel;
import com.dianping.mobile.mapi.dztgdetail.util.ImageUtils;
import com.dianping.mobile.mapi.dztgdetail.util.TimeUtils;
import com.dianping.piccentercloud.display.api.enums.WaterMark;
import com.dianping.review.professional.*;
import com.dianping.reviewremote.remote.utils.ReviewUtils;
import com.dianping.tuangou.dztg.bjwrapper.api.dto.MtUserDto;
import com.dianping.ugc.pic.remote.dto.MtReviewPicInfo;
import com.dianping.ugc.pic.remote.dto.VideoData;
import com.dianping.ugc.proxyService.remote.enums.feed.ReviewTypeEnum;
import com.dianping.ugc.review.remote.dto.MTReviewData;
import com.dianping.ugc.review.remote.dto.Star;
import com.dianping.userremote.base.dto.UserDTO;
import com.dianping.vipremote.vo.UserInfoForAppVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class ShopReviewHelper {

    private static final String ANONYMOUS_NAME = "匿名用户";
    private static final String ANONYMOUS_PIC = "https://p0.meituan.net/travelcube/34c81dbcfc46b64d8148edfe8d7a8d5e7896.png";
    private static final String DP_USER_DETAIL_URL = "dianping://user?userid=%d";
    private static final String MT_USER_DETAIL_URL = "imeituan://www.meituan.com/userreview/?uid=%d";
    private static final String NORMAL_REVIEW_DETAIL_URL = "dianping://reviewdetail?id=%d&type=1";    //点评详情跳转链接
    private static final String MT_DETAIL_REVIEW_SCHEMA = "imeituan://www.meituan.com/feed/detail?id=%s&type=%d"; //美团详情跳转链接
    private static final String MT_GOOD_REVIEW_PIC = "https://www.dpfile.com/ugc/reviewhonour/highQualityReview.png";
    private static final String DP_SHOP_REVIEW_SUPPLY = "com.sankuai.dzu.tpbase.dztgdetailweb.dp.shop.review.supply.enable";
    private static final String MT_SHOP_REVIEW_SUPPLY = "com.sankuai.dzu.tpbase.dztgdetailweb.mt.shop.review.supply.enable";
    private static final String MT_SHOP_REVIEW_VIDEO = "com.sankuai.dzu.tpbase.dztgdetailweb.mt.shop.review.video.enable";
    private static final String DISPLAY_SHOP_REVIEW_TAG = "com.sankuai.dzu.tpbase.dztgdetailweb.display.shop.review.tag.enable";

    public static List<ReviewPicDTO> buildMtReviewPicList(List<VideoData> videoDataList, List<MtReviewPicInfo> mtReviewPicInfoList) {
        List<ReviewPicDTO> reviewPicDTOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(videoDataList)) {
            for (VideoData videoData : videoDataList) {
                if (Objects.isNull(videoData)){
                    continue;
                }
                ReviewPicDTO reviewPicDTO = new ReviewPicDTO();
                reviewPicDTO.setType(2);
                reviewPicDTO.setSmallPicUrl(videoData.getCover());
                reviewPicDTO.setBigPicUrl(videoData.getUrl());
                reviewPicDTO.setTitle(TimeUtils.buildVideoTime(videoData.getDuration()));
                reviewPicDTO.setUploadTime(TimeUtils.getDateString(videoData.getModTime()));
                reviewPicDTOList.add(reviewPicDTO);
            }
        }

        if (CollectionUtils.isNotEmpty(mtReviewPicInfoList)) {
            for (MtReviewPicInfo mtReviewPicInfo : mtReviewPicInfoList) {
                if (Objects.isNull(mtReviewPicInfo)){
                    continue;
                }
                ReviewPicDTO reviewPicDTO = new ReviewPicDTO();
                reviewPicDTO.setType(1);
                String picUrl = mtReviewPicInfo.getUrl();
                picUrl = picUrl.replace("/w.h", "");//美团服务吐出图片为/w.h/格式，需要去掉wh来获取原图url
                reviewPicDTO.setBigPicUrl(ImageUtils.getJpgUrl(picUrl, 700, 700, 0, WaterMark.EMPTY));
                reviewPicDTO.setSmallPicUrl(ImageUtils.getJpgUrl(picUrl, 249, 249, 0, WaterMark.EMPTY));
                reviewPicDTO.setTitle(mtReviewPicInfo.getTitle());
                reviewPicDTO.setUploadTime(TimeUtils.getDateString(mtReviewPicInfo.getAddTime()));
                reviewPicDTOList.add(reviewPicDTO);
            }
        }
        return reviewPicDTOList;
    }

    public static ReviewDetailDO mtReviewDataToReviewDetailDO(MTReviewData mtReviewData, MtUserDto userModel, List<ReviewPicDTO> defaultReviewList, ShopReviewCtx shopReviewCtx) {
        ReviewDetailDO reviewDetailDO = new ReviewDetailDO();
        if (mtReviewData == null) {
            return reviewDetailDO;
        }
        reviewDetailDO.setReviewUserModel(buildMtShopReviewUserModel(mtReviewData, userModel));

        reviewDetailDO.setReviewId(String.valueOf(mtReviewData.getFeedbackId()));
        reviewDetailDO.setContent(mtReviewData.getReviewBody());
        reviewDetailDO.setStar(getScore(mtReviewData));
        reviewDetailDO.setReviewTag("消费后点评");
        reviewDetailDO.setCommentCount(mtReviewData.getReplyCount());
        reviewDetailDO.setLikeCount(mtReviewData.getFlowerCount());
        reviewDetailDO.setReviewTime(TimeUtils.getDateString(mtReviewData.getAddTime()));

        reviewDetailDO.setReviewTime(TimeUtils.getDateString(mtReviewData.getAddTime()));
        reviewDetailDO.setActionNote("打分");
        if (CollectionUtils.isEmpty(defaultReviewList)) {
            List<com.dianping.ugc.review.remote.dto.ReviewPic> reviewPics = mtReviewData.getReviewPics();
            List<ReviewPicDTO> reviewPicDTOList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(reviewPics)) {
                for (com.dianping.ugc.review.remote.dto.ReviewPic pic : reviewPics) {
                    // 过滤掉被删除或屏蔽的照片
                    if (filterPicStatusAndOwner(shopReviewCtx, mtReviewData.getUserId(), pic.getStatus())){
                        continue;
                    }
                    String picUrl = pic.getUrl();
                    ReviewPicDTO reviewPicDTO = new ReviewPicDTO();
                    picUrl = picUrl.replace("/w.h", "");//美团服务吐出图片为/w.h/格式，需要去掉wh来获取原图url
                    reviewPicDTO.setBigPicUrl(ImageUtils.getImageUrl(picUrl, 700, 700, 0, WaterMark.EMPTY));
                    reviewPicDTO.setSmallPicUrl(ImageUtils.getImageUrl(picUrl, 249, 249, 0, WaterMark.EMPTY));
                    reviewPicDTOList.add(reviewPicDTO);
                }
            }
            reviewDetailDO.setReviewPicList(reviewPicDTOList);
        } else {
            reviewDetailDO.setReviewPicList(defaultReviewList);
        }
        reviewDetailDO.setReviewType(ReviewTypeEnum.MEITUAN_REVIEW.value);
        if (shopReviewCtx.isMt()) {
            reviewDetailDO.setDetailUrl(String.format(MT_DETAIL_REVIEW_SCHEMA, reviewDetailDO.getReviewId(), 1));
        }
        return reviewDetailDO;
    }
    /**
     * 检测图片、视频状态，过滤掉被屏蔽、删除的照片
     * 视频，图片状态集合，PicVideoStatusEnum
     * 返回值：
     *      true：需要过滤
     *      false：不用过滤
     * @param shopReviewCtx
     * @param userid
     * @param status
     * @return
     */
    public static Boolean filterPicStatusAndOwner(ShopReviewCtx shopReviewCtx, Long userid, Integer status){
        if (Objects.isNull(status)){
            return Boolean.TRUE;
        }
        // 可正常展示
        if (PicVideoStatusEnum.NORMAL.code == status.intValue()){
            return Boolean.FALSE;
        }
        // 主态展示，客态不展示
        if (PicVideoStatusEnum.AUDIT.code  == status.intValue() && (Objects.nonNull(userid) && userid.longValue() == shopReviewCtx.getEnvCtx().getMtUserId())){//mtUserId有问题，需要确认，但不涉及主接口
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }
    public static Map<Long, ReviewUserModel> buildReviewUserModelList(
            List<Long> userIds, Map<Long, UserDTO> userDTOMap, Map<Long, UserManaDTO> userGrowthDTOMap,
            Map<Long, UserInfoForAppVO> vipUserMap) {
        Map<Long, ReviewUserModel> reviewUserModelMap = Maps.newHashMap();
        for (Long userId : userIds) {
            if (MapUtils.isNotEmpty(userDTOMap) && !userDTOMap.containsKey(userId) && userId == -1) {
                ReviewUserModel reviewUserModel = new ReviewUserModel();
                reviewUserModelMap.put(userId, reviewUserModel);
                continue;
            }
            ReviewUserModel reviewUserModel = new ReviewUserModel();
            reviewUserModel.setUserIdL(userId);
            if (MapUtils.isNotEmpty(userDTOMap) && userDTOMap.containsKey(userId)) {
                reviewUserModel.setAvatar(userDTOMap.get(userId).getBigFace());
                reviewUserModel.setUserName(userDTOMap.get(userId).getUserNickName());
                reviewUserModel.setDetailUrl(String.format(DP_USER_DETAIL_URL, userId));
            }
            if (MapUtils.isNotEmpty(vipUserMap) && vipUserMap.containsKey(userId)) {
                reviewUserModel.setVipIcon(vipUserMap.get(userId) == null ? null : "https://img.meituan.net/gpa/chengsev42.png"); //通用图标 vipUserMap.get(userId).getPic()
            }
            if (MapUtils.isNotEmpty(userGrowthDTOMap) && userGrowthDTOMap.containsKey(userId)) {
                reviewUserModel.setUserLevel(userGrowthDTOMap.get(userId) == null ? null : userGrowthDTOMap.get(userId).getRoundurl());
            }
            reviewUserModelMap.put(userId, reviewUserModel);
        }
        return reviewUserModelMap;
    }

    private static ReviewUserModel buildMtShopReviewUserModel(MTReviewData mtReviewData, MtUserDto userModel) {
        ReviewUserModel reviewUserModel = new ReviewUserModel();
        if (mtReviewData.getAnonymous()) {//匿名用户单独处理
            reviewUserModel.setAvatar(ANONYMOUS_PIC);
            reviewUserModel.setUserName(ANONYMOUS_NAME);
        } else {
            reviewUserModel.setUserIdL(mtReviewData.getUserId());
            if (userModel != null) {
                reviewUserModel.setUserName(userModel.getUserName());
                if (StringUtils.isNotEmpty(userModel.getAvatarUrl())) {
                    reviewUserModel.setAvatar(userModel.getAvatarUrl().replace("/w.h", ""));
                } else {
                    reviewUserModel.setAvatar(ANONYMOUS_PIC);
                }
            }
            reviewUserModel.setDetailUrl(String.format(MT_USER_DETAIL_URL, mtReviewData.getUserId()));
        }
        return reviewUserModel;
    }

    /**
     * REVIEW:打分,BATCH_PIC:发布了照片,CHECKIN:签到成功,PRAISE_SHOP:赞了这家店,POST:发布了帖子,KING_SIGN:报名了霸王餐,KING_WIN:中了霸王餐,
     * COMBODISHS:搭配了一桌菜,NOTE:发布了内容,MY_LIST:创建了榜单,SHORT_VIDEO:发布了视频
     *
     * @param reviewData
     * @param reviewUserModelMap
     * @return
     */
    public static ReviewDetailDO reviewDataToReviewDetailDO(ReviewDataV2 reviewData, Map<Long, ReviewUserModel> reviewUserModelMap, boolean supply) {
        ReviewDetailDO reviewDetailDO = new ReviewDetailDO();
        reviewDetailDO.setActionNote("打分");
        reviewDetailDO.setReviewType(ReviewTypeEnum.SHOP_REVIEW.value);
        reviewDetailDO.setStar(reviewData.getStar().getAccurateValue());
        reviewDetailDO.setReviewTag(getReviewTag(reviewData.getReferType()));
        reviewDetailDO.setReviewTime(TimeUtils.getDateString(reviewData.getAddTime()));
        reviewDetailDO.setContent(reviewData.getReviewBody());
        reviewDetailDO.setReviewId(reviewData.getReviewIdLong().toString());
        reviewDetailDO.setLikeCount(reviewData.getFlowerTotal());
        reviewDetailDO.setCommentCount(reviewData.getFollowNoteNo());
        reviewDetailDO.setPrice(buildPrice(reviewData));
        reviewDetailDO.setScoreText(buildScoreText(reviewData.getScoreList()));//计算小分

        //详情页链接
        if (!supply) {
            reviewDetailDO.setDetailUrl(String.format(NORMAL_REVIEW_DETAIL_URL, reviewData.getReviewIdLong()));
        }

        if (MapUtils.isNotEmpty(reviewUserModelMap) && reviewUserModelMap.containsKey(reviewData.getUserId())) {
            reviewDetailDO.setReviewUserModel(reviewUserModelMap.get(reviewData.getUserId()));
        }

        //图片数据封装
        List<ReviewPicDTO> reviewPicDTOList = Lists.newArrayList();
        List<ReviewPic> reviewPicList = reviewData.getReviewPics();

        if (CollectionUtils.isNotEmpty(reviewPicList)) {
            if (reviewPicList.size() > 9) {
                reviewPicList = new ArrayList<>(reviewPicList.subList(0, 9)); //最多展示10张图
            }

            for (ReviewPic reviewPic : reviewPicList) {
                if (reviewPic == null) {
                    continue;
                }
                ReviewPicDTO reviewPicDTO = buildFeedPic(reviewPic.getUrl());
                reviewPicDTOList.add(reviewPicDTO);
            }
        }

        reviewDetailDO.setReviewPicList(reviewPicDTOList);
        reviewDetailDO.setReviewExtTags(buildReviewTags(reviewData.getExtInfoList()));

        return reviewDetailDO;
    }

    private static List<ReviewExtTagDO> buildReviewTags(List<ExtInfo> extInfos) {
        List<ReviewExtTagDO> tags = new ArrayList<>();

        if (CollectionUtils.isEmpty(extInfos)) {
            return tags;
        }

        for (ExtInfo extInfo : extInfos) {
            String title;
            List<String> values;

            if ("cosmetology_tag_module".equals(extInfo.getTitle())) {
                title = "项目";
                values = buildExtTagDTOValues(extInfo, "tagName");
            } else if ("ugc_cosmetology_impression".equals(extInfo.getTitle())) {
                title = "印象";
                values = buildExtTagDTOValues(extInfo, "name");
            } else {
                continue;
            }

            if (StringUtils.isNotBlank(title) && CollectionUtils.isNotEmpty(values)) {
                ReviewExtTagDO extTagDTO = new ReviewExtTagDO();
                extTagDTO.setTitle(title);
                extTagDTO.setValues(values);
                tags.add(extTagDTO);
            }
        }

        return tags;
    }

    private static List<String> buildExtTagDTOValues(ExtInfo extinfo, String key){
        List<String> values = new ArrayList<>();
        for (String tag: extinfo.getValues()){
            JSONObject jsonObject = (JSONObject) JSON.parse(tag);
            String value = (String) jsonObject.get(key);
            if (StringUtils.isNotBlank(value)) {
                values.add(value);
            }
        }
        return values;
    }

    private static String getReviewTag(int referType) {
        if (ReviewUtils.isConsumedReview(referType)) {
            return "消费后点评";
        } else if (referType == 1 || referType == 27) {
            return "免费体验后点评";
        } else if (referType == 71) {
            return "免费试吃后点评";
        }
        return null;
    }

    private static ReviewPicDTO buildFeedPic(String url) {
        ReviewPicDTO reviewPicDTO = new ReviewPicDTO();
        if (StringUtils.isEmpty(url)) {
            return reviewPicDTO;
        }
        String smallUrl = ImageUtils.getJpgUrl(url, 249, 249, 0, WaterMark.EMPTY);
        String bigUrl = getBigUrl(url, 700, 700, 0, WaterMark.DIANPING, 0, false);
        reviewPicDTO.setSmallPicUrl(smallUrl);
        reviewPicDTO.setBigPicUrl(bigUrl);
        return reviewPicDTO;
    }

    /**
     * 根据灰度标识获取大图url
     */
    private static String getBigUrl(String key, int width, int height, int mode, WaterMark waterMark, int picFormatType, boolean isGray) {
        String url;
        if (picFormatType == 1 && isGray) {
            url = ImageUtils.getGifUrl(key, 0, 0, mode, waterMark);
        } else {
            url = ImageUtils.getJpgUrl(key, width, height, mode, waterMark);
        }
        return url;
    }

    private static String buildPrice(ReviewDataV2 reviewData) {
        String price = "";
        List<Expense> expenseInfoList = reviewData.getExpenseInfoList();
        if (CollectionUtils.isEmpty(expenseInfoList)) {
            return price;
        }
        String title = expenseInfoList.get(0).getTitle();
        int avgPrice = expenseInfoList.get(0).getValue().intValue();
        if ("人均".equals(title) && avgPrice > 0) {
            price = "￥" + avgPrice + "/人";
        } else {
            price = (avgPrice > 0) ? "￥" + avgPrice : StringUtils.EMPTY;
        }
        return price;
    }

    private static String buildScoreText(List<Score> scoreList) {
        String scoreText = "";
        if (CollectionUtils.isEmpty(scoreList) || scoreList.size() != 3) {
            return scoreText;
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (Score score : scoreList) {
            String scoreValue = PriceHelper.format(new BigDecimal(score.getAccurateValue()).divide(new BigDecimal(10), 1, RoundingMode.CEILING));
            String singleScore = score.getTitle() + ":" + scoreValue;//分数是 0 ~ 4，实际分数取值范围(5,10,15,20,25,30,35,40,45,50)
            stringBuilder.append(singleScore).append("  ");
        }
        scoreText = stringBuilder.toString();
        return scoreText;
    }

    private static int getScore(MTReviewData mtReviewData) {
        Star star = mtReviewData.getStar();
        if (star != null)
            return star.getStar();
        return 0;
    }

    public static boolean disPlayShopReviewTag() {
        return Lion.getBooleanValue(DISPLAY_SHOP_REVIEW_TAG, false);
    }

    public static boolean dpShopReviewNeedSupply() {
        return Lion.getBooleanValue(DP_SHOP_REVIEW_SUPPLY, false);
    }

    public static boolean mtShopReviewNeedSupply() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.helper.ShopReviewHelper.mtShopReviewNeedSupply()");
        return Lion.getBooleanValue(MT_SHOP_REVIEW_SUPPLY, false);
    }

    public static boolean mtShopReviewNeedVideo() {
        return Lion.getBooleanValue(MT_SHOP_REVIEW_VIDEO, false);
    }
}
