package com.dianping.mobile.mapi.dztgdetail.util;

import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

public class MallUtils {

    private static final int MALL_DEAL_CATEGORY_ID = 712;//商场团单类目

    private static final String MALL_FOOD_POI_SHELF_PAGE_SOURCE = "mallFoodPoiShelf";//商场场内餐厅代金券

    /**
     * 判断是否来自商场场内餐厅代金券
     */
    public static boolean isFromMallFoodPoiVoucher(String requestPageSource, Integer dealCategoryId) {
        if (StringUtils.isEmpty(requestPageSource) || Objects.isNull(dealCategoryId)) {
            return false;
        }
        if (!StringUtils.equals(requestPageSource, MALL_FOOD_POI_SHELF_PAGE_SOURCE)) {
            return false;
        }
        return isMallDealCategory(dealCategoryId);
    }

    public static boolean isMallDealCategory(Integer dealCategoryId) {
        return Objects.nonNull(dealCategoryId) && MALL_DEAL_CATEGORY_ID == dealCategoryId.intValue();
    }
}
