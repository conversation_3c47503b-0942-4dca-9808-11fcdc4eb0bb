package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

@MobileDo(id = 0x9f47)
@Data
public class PriceDisplayModuleDo implements Serializable {

    /**
     * 团购价
     */
    @MobileDo.MobileField(key = 0x9218)
    private String dealGroupPrice;

    /**
     * 门市价
     */
    @MobileDo.MobileField(key = 0x6242)
    private String marketPrice;

    /**
     * 优惠后价格描述，eg："减后价 ￥5400"
     */
    @MobileDo.MobileField(key = 0x7031)
    private String promoPrice;

    /**
     * 价格描述
     */
    @MobileDo.MobileField(key = 0xc87d)
    private SimpleContextVO priceDesc;

    /**
     * 购买价格
     */
    @MobileDo.MobileField(key = 0xb716)
    private String price;

    /**
     * 优惠标签
     */
    @MobileDo.MobileField(key = 0x7031)
    private String promoTag;

    /**
     * 是否展示：该字段为true前端则展示PriceDisplayModuleDo对应的内容，但前端一些特殊场景下则不会考虑该字段的值，如美发的beauty_hair_price_newtuandeal样式
     */
    @MobileDo.MobileField(key = 0x47e1)
    private boolean enableDisplay;

}
