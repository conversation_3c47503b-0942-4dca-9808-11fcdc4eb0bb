package com.dianping.mobile.mapi.dztgdetail.entity;

import lombok.Data;

@Data
public class SortCxt {

    private Double lat;

    private Double lng;

    private int offset;

    private int limit;

    private int mtCityId;

    private String sortStrategy;

    public SortCxt(MtCommonParam param) {
        this.lat = param.getLat();
        this.lng = param.getLng();
        this.offset = param.getOffset();
        this.limit = param.getLimit();
        this.mtCityId = param.getCityId();
        this.sortStrategy = param.getSortStr();
    }


    public boolean hasLatLng() {
        if (lat == null || lng == null) {
            return false;
        }
        return lat > 0 && lng > 0;
    }

}
