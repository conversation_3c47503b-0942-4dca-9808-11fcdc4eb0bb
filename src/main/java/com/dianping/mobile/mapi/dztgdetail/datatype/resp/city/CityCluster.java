package com.dianping.mobile.mapi.dztgdetail.datatype.resp.city;

import lombok.Data;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;

import java.io.Serializable;
import java.util.List;

/**
 * 首次母读音相同的城市簇
 */
@Data
public class CityCluster implements Serializable {
    /**
     * 城市区县列表
     */
    @MobileField
    private List<CityDistrict> cityList;

    /**
     * 首字母
     */
    @MobileField
    private String firstLetter;
}
