package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedActivityModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.ActivityModuleDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo.DealPromoModule;
import com.dianping.mobile.mapi.dztgdetail.facade.UnifiedActivityFacade;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

import static com.dianping.mobile.mapi.dztgdetail.util.AntiCrawlerUtils.antiUnauthenticLogin;

/**
 * Created by zuomlin on 2018/12/13.
 */
@InterfaceDoc(displayName = "到综团单详情页统一活动接口",
        type = "restful",
        description = "团单详情页统一活动中心——用户在团单详情页展示活动详情的模块，主要包括拼团",
        scenarios = "团单详情页统一活动中心——用户在团单详情页展示活动详情的模块，主要包括拼团",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "yangquan02"
)
@Controller("general/platform/dztgdetail/unifiedactivitymodule.bin")
@Action(url = "unifiedactivitymodule.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class UnifiedActivityModule extends AbsAction<UnifiedActivityModuleReq> {

    @Autowired
    private UnifiedActivityFacade unifiedActivityFacade;

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "unifiedactivitymodule.bin",
            displayName = "到综团单详情页统一活动接口",
            description = "团单详情页统一活动中心——用户在团单详情页展示活动详情的模块，主要包括拼团。如果领券失败，则后端不返回数据。",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "unifiedactivitymodule.bin请求参数",
                            type = UnifiedActivityModuleReq.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "result", description = "领券返回数据信息", type = DealPromoModule.class)},
            restExampleUrl = "http://mapi.51ping.com/general/platform/dztgdetail/unifiedactivitymodule.bin?",
            restExamplePostData = "{}",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    ),
            }
    )
    @Override
    protected IMobileResponse validate(UnifiedActivityModuleReq request, IMobileContext iMobileContext) {
        IdUpgradeUtils.processProductIdForUnifiedActivityModuleReq(request, "unifiedactivitymodule.bin");
        if (request == null || request.getDealGroupId() <= 0) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @Override
    @CryptMethod
    protected IMobileResponse execute(UnifiedActivityModuleReq request, IMobileContext iMobileContext) {
        try {
            EnvCtx envCtx = initEnvCtx(iMobileContext);
            // 拦截没有授权的登录
            antiUnauthenticLogin(iMobileContext);
            if (request.getClientType() > 0) {
                envCtx.setClientType(request.getClientType());
            }
            ActivityModuleDTO result = unifiedActivityFacade.queryUnifiedActivityModule(request, envCtx, iMobileContext);
            if (result == null) {
                return Resps.NoDataResp;
            }
            return new CommonMobileResponse(result);
        } catch (Exception e) {
            logger.error("unifiedgetcoupon.bin failed, params: request={}, context ={}", request, iMobileContext);
        }
        return Resps.SYSTEM_ERROR;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
