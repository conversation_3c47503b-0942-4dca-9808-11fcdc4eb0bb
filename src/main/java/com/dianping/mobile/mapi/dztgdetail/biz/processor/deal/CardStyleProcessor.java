package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.constants.CatEvents;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.CardStyleConfig;
import com.dianping.mobile.mapi.dztgdetail.util.JsonUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LogUtils;
import com.dianping.mobile.mapi.dztgdetail.util.VersionUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class CardStyleProcessor extends AbsDealProcessor {

    @Autowired
    DouHuBiz douHuBiz;
    @Autowired
    DouHuService douHuService;

    @Override
    public boolean isEnable(DealCtx ctx) {
        return true;
    }

    @Override
    public void prepare(DealCtx ctx) {

    }

    @Override
    public void process(DealCtx ctx) {
        if(enableCardStyle(ctx)) {
            ctx.setEnableCardStyle(true);
        }

        ModuleAbConfig moduleAbConfig = douHuService.enableCardStyleV2(ctx.getEnvCtx(), ctx.getCategoryId(), ctx.getMrnVersion());
        if (Objects.nonNull(moduleAbConfig)) {
            ctx.setCardStyleAbV2Config(moduleAbConfig);
            if(douHuService.hitEnableCardStyleV2(moduleAbConfig, ctx.getRequestSource())) {
                ctx.setEnableCardStyleV2(true);
                Cat.logEvent("CardStyleV2", String.valueOf(ctx.getCategoryId()));
            } else {    // 未命中实验
                LogUtils.info("[CardStyleProcessor] not hit expId, moduleAbConfig={}", JsonUtils.toJson(moduleAbConfig));
                cardStyleV2Metric(ctx, CatEvents.NOT_HIT_CARD_STYLE_V2, "hit");
            }
        } else {
            cardStyleV2Metric(ctx, CatEvents.NOT_MEET_CARD_STYLE_V2, "meet");
        }
    }

    public void cardStyleV2Metric(DealCtx ctx, String metricKey, String logType) {
        Map<String, String> tags = Maps.newHashMap();
        tags.put("categoryId", String.valueOf(ctx.getCategoryId()));
        tags.put("dztgClientType", String.valueOf(ctx.getEnvCtx().getDztgClientTypeEnum().getCode()));
        tags.put("mrnversion", ctx.getMrnVersion());
        tags.put("version", ctx.getEnvCtx().getVersion());
        Cat.logMetricForCount(metricKey, tags);
        LogUtils.info("[CardStyleProcessor] not {} cardStyleV2, categoryId:{}, dealgroupId:{}, dztgClientType:{}, mrnversion:{}, version:{}", logType, ctx.getCategoryId(), ctx.getDealId4P(), ctx.getEnvCtx().getDztgClientTypeEnum().getCode(), ctx.getMrnVersion(), ctx.getEnvCtx().getVersion());
    }

    public boolean enableCardStyle(DealCtx dealCtx) {
        CardStyleConfig cardStyleConfig = Lion.getBean("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.card.style.config", CardStyleConfig.class);
        if(cardStyleConfig == null || !cardStyleConfig.isEnableCardStyle()) {
            return false;
        }
        if(cardStyleConfig.getEnableClientType() != null && !cardStyleConfig.getEnableClientType().contains(dealCtx.getEnvCtx().getDztgClientTypeEnum().getCode())) {
            return false;
        }
        if(!environmentPass(dealCtx)) {
            return false;
        }
        if(cardStyleConfig.isAllPass()) {
            return true;
        }
        int publishCategoryId = dealCtx.getCategoryId();
        boolean isMt = dealCtx.isMt();
        String key = isMt ? "mt" : "dp";
        key += publishCategoryId;
        String expId = cardStyleConfig.getCategory2ExpId().getOrDefault(key, null);

        String unionId = dealCtx.getEnvCtx().getUnionId();
        Map<String, String> expId2ModuleId = Lion.getMap("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.card.style.expId2ModuleId", String.class, new HashMap<>());
        String module = expId2ModuleId.getOrDefault(expId, "CardStyleAB");

        ModuleAbConfig moduleAbConfig = douHuBiz.getAbByUnionIdAndExpId(unionId, expId, module, isMt);
        if(moduleAbConfig != null && CollectionUtils.isNotEmpty(moduleAbConfig.getConfigs())) {
            dealCtx.setCardStyleAbConfig(moduleAbConfig);
            if("c".equals(moduleAbConfig.getConfigs().get(0).getExpResult())) {
                Cat.logEvent("CardStyle", String.valueOf(publishCategoryId));
                return true;
            }
        }
        return false;
    }

    private boolean environmentPass(DealCtx dealCtx) {
        if(dealCtx == null || dealCtx.getEnvCtx() == null) {
            return false;
        }
        if(DztgClientTypeEnum.MEITUAN_APP.equals(dealCtx.getEnvCtx().getDztgClientTypeEnum())
                && VersionUtils.isGreatEqualThan(dealCtx.getEnvCtx().getVersion(), "12.11.200")
                && VersionUtils.isGreatEqualThan(dealCtx.getMrnVersion(), "0.5.3")) {
            return true;
        }
        if(DztgClientTypeEnum.DIANPING_APP.equals(dealCtx.getEnvCtx().getDztgClientTypeEnum())
                && VersionUtils.isGreatEqualThan(dealCtx.getEnvCtx().getVersion(), "11.4.0")
                && VersionUtils.isGreatEqualThan(dealCtx.getMrnVersion(), "0.5.3")) {
            return true;
        }
        return false;
    }

}