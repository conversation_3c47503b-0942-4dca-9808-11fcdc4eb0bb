package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;


@MobileDo(id = 0xcf51)
public class StandardServiceVO implements Serializable {

    /**
     * 标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * 副标题
     */
    @MobileDo.MobileField(key = 0xd894)
    private String subTitle;

    /**
     * 标准服务内容详情
     */
    @MobileDo.MobileField(key = 0xee7)
    private StandardServiceDetailVO standardServiceDetail;

    /**
     * 服务内容列表
     */
    @MobileDo.MobileField(key = 0xf482)
    private List<StandardServiceContentVO> standardServiceContents;

    public StandardServiceDetailVO getStandardServiceDetail() {
        return standardServiceDetail;
    }

    public void setStandardServiceDetail(
            StandardServiceDetailVO standardServiceDetail) {
        this.standardServiceDetail = standardServiceDetail;
    }

    public List<StandardServiceContentVO> getStandardServiceContents() {
        return standardServiceContents;
    }

    public void setStandardServiceContents(
            List<StandardServiceContentVO> standardServiceContents) {
        this.standardServiceContents = standardServiceContents;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}