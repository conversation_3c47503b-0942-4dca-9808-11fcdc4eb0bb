package com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-08-28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TypeDoc(description = "款式图片标签")
@MobileDo(id = 0xf77)
public class ImageTagVO implements Serializable {
    @FieldDoc(description = "标签名")
    @MobileDo.MobileField(key = 0x7ab8)
    private String name;

    @FieldDoc(description = "标签风格", rule = "0=普通标签；1=常驻标签，2=商家推荐")
    @MobileDo.MobileField(key = 0x1b3a)
    private int style;
}
