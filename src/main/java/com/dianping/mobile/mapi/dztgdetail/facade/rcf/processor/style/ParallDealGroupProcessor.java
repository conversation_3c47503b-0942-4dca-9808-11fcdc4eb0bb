package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.style;

import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.base.dto.DealBaseDTO;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.base.dto.DealGroupVideoDTO;
import com.dianping.deal.publishcategory.dto.ChannelDTO;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.lion.facade.LionFacade;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MultiSkuExpBiz;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.entity.SkuSummary;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.sankuai.dztheme.deal.req.SkuOptionBatchRequest;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupChannelDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupImageDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.ReceiptEffectiveDateDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.util.DealGroupUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2023/8/16
 * @since mapi-dztgdetail-web
 */
public class ParallDealGroupProcessor extends AbsDealProcessor {

    @Autowired
    private DealGroupWrapper dealGroupWrapper;

    @Autowired
    private DouHuBiz douHuBiz;

    @Autowired
    private MultiSkuExpBiz multiSkuExpBiz;

    // 替换为multi.sku.deal.category.servicetype.whitelist
//    private static final String DEAL_CATEGORY = "multi.sku.deal.category.whitelist";

    @Override
    public void prepare(DealCtx ctx) {
        if(GreyUtils.enableQueryCenterForMainApi(ctx)) {
            return;
        } else {
            Future channelFuture = dealGroupWrapper.preDealGroupChannelById(ctx.getDpId());
            Future dealGroupFuture = dealGroupWrapper.preDealGroupBase(ctx.getDpId());
            Future attrFuture = dealGroupWrapper.preAttrs(ctx.getDpId(), getAttrNames());

            ctx.getFutureCtx().setChannelFuture(channelFuture);
            ctx.getFutureCtx().setDealGroupFuture(dealGroupFuture);
            ctx.getFutureCtx().setAttrFuture(attrFuture);
        }
    }

    private List<String> getAttrNames() {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.style.ParallDealGroupProcessor.getAttrNames()");
        Map<String, String> configMap = LionFacade
                .getMap(LionConstants.DISABLE_BUY_AND_SHARE_ATTR, String.class, String.class, Collections.emptyMap());
        ArrayList<String> result = Lists.newArrayList(DealAttrHelper.REQUIRE_ATTRIBUTES);
        result.addAll(configMap.keySet());
        return result;
    }

    @Override
    public void process(DealCtx ctx) {
        if(GreyUtils.enableQueryCenterForMainApi(ctx)) {
            ctx.setChannelDTO(trans2OldChannelDTO(ctx.getDealGroupDTO()));
            ctx.setAttrs(trans2OldAttrDTO(ctx.getDealGroupDTO()));
        } else {
            ctx.setDealGroupBase(dealGroupWrapper.getFutureResult(ctx.getFutureCtx().getDealGroupFuture()));
            ctx.setChannelDTO(dealGroupWrapper.getFutureResult(ctx.getFutureCtx().getChannelFuture()));
            ctx.setAttrs(dealGroupWrapper.getFutureResult(ctx.getFutureCtx().getAttrFuture()));
        }

        // 因为依赖团购类目信息做过滤，为降低请求数量，将请求前置到category处
        String serviceType = getServiceType(ctx);
        if (!LionConfigUtils.isUseMultiSku(ctx.getCategoryId(), serviceType)) {
            return;
        }
        SkuSummary skuSummary = multiSkuExpBiz.getSkuSummaryFromCacheWithFailOver(ctx.getDpId());
        if (skuSummary != null) {
            ctx.setSkuSummary(skuSummary);
        }
    }

    private String getServiceType(DealCtx ctx) {
        return ctx.getDealGroupDTO() == null || ctx.getDealGroupDTO().getCategory() == null
                ? null : ctx.getDealGroupDTO().getCategory().getServiceType();
    }

    private List<AttributeDTO> trans2OldAttrDTO(DealGroupDTO dealGroupDTO) {
        if(dealGroupDTO == null || dealGroupDTO.getAttrs() == null) {
            return new ArrayList<>();
        }

        return dealGroupDTO.getAttrs().stream().map(attrDTO -> {
            AttributeDTO attributeDTO = new AttributeDTO();
            attributeDTO.setName(attrDTO.getName());
            attributeDTO.setValue(attrDTO.getValue());
            attributeDTO.setSource(attrDTO.getSource());

            return attributeDTO;
        }).collect(Collectors.toList());
    }

    private com.dianping.deal.publishcategory.dto.DealGroupChannelDTO trans2OldChannelDTO(DealGroupDTO dealGroupDTO) {
        if(dealGroupDTO == null || dealGroupDTO.getChannel() == null) {
            return null;
        }
        com.dianping.deal.publishcategory.dto.DealGroupChannelDTO oldDto = new com.dianping.deal.publishcategory.dto.DealGroupChannelDTO();
        DealGroupChannelDTO dealGroupChannelDTO = dealGroupDTO.getChannel();

        com.dianping.deal.publishcategory.dto.ChannelDTO channelDTO = new ChannelDTO();
        channelDTO.setChannelId(dealGroupChannelDTO.getChannelId());
        channelDTO.setChannelEn(dealGroupChannelDTO.getChannelEn());
        channelDTO.setChannelCn(dealGroupChannelDTO.getChannelCn());
        channelDTO.setChannelGroupId(dealGroupChannelDTO.getChannelGroupId());
        channelDTO.setChannelGroupEn(dealGroupChannelDTO.getChannelGroupEn());
        channelDTO.setChannelGroupCn(dealGroupChannelDTO.getChannelGroupCn());

        oldDto.setChannelDTO(channelDTO);
        oldDto.setDealGroupId(dealGroupDTO.getDpDealGroupIdInt());
        oldDto.setCategoryId(dealGroupDTO.getCategory() == null ? 0 : Math.toIntExact(dealGroupDTO.getCategory().getCategoryId()));

        return oldDto;
    }

}
