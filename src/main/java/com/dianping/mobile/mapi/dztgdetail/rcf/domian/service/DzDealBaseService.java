package com.dianping.mobile.mapi.dztgdetail.rcf.domian.service;

import com.alibaba.fastjson.JSONObject;
import com.dianping.mobile.mapi.dztgdetail.util.TextSpaceFormatter;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2025/2/18 19:34
 */
@Component
public class DzDealBaseService {
    
    public void processDealTitle(JSONObject dealBase){
        if(dealBase != null && Objects.nonNull(dealBase.get("title"))) {
            String title = (String) dealBase.get("title");
            title = TextSpaceFormatter.formatTextSpace(title);
            dealBase.put("title", title);
        }
    }
}
