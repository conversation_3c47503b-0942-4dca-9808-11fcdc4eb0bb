package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.struct;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.util.List;

@Data
@MobileDo(id = 0xc82b)
public class FeeDetailsVO {

    @MobileDo.MobileField(key = 0xda7d)
    private List<String> feeTableHeaders;

    @MobileDo.MobileField(key = 0x1d9)
    private List<DrivingSchoolFee> drivingSchoolFees;

    @MobileDo.MobileField(key = 0xfebf)
    private String desc;

    /**
     * 后端业务逻辑字段，不对前端透出
     */
    private List<String> cities;

}
