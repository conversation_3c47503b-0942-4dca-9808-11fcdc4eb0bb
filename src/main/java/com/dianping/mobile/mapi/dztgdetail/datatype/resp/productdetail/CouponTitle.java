package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-04-02
 * @desc
 */
@TypeDoc(description = "优惠券标题")
@Data
@MobileDo(id = 0xc9d729b2)
public class CouponTitle implements Serializable {

    @FieldDoc(description = "字体颜色")
    @MobileField(key = 0x2ac4)
    private String color;

    @FieldDoc(description = "券名称")
    @MobileField(key = 0xcce)
    private String content;
}