package com.dianping.mobile.mapi.dztgdetail.button.joy;

import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
import com.dianping.mobile.mapi.dztgdetail.helper.BuyButtonHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;

public class JoyTimesCardButtonBuilder extends AbstractButtonBuilder {

    @Override
    public void doBuild(DealCtx context, ButtonBuilderChain chain) {
        if (BuyButtonHelper.isValidTimesCard(context)) {
            if (context.getPreButton() == null) {
                context.getBuyBar().setBuyType(DealBuyBar.BuyType.TIMESCARD.type);
            }
            context.addButton(DealBuyHelper.getJoyTimesCardButton(context));
        }
        chain.build(context);
    }
}
