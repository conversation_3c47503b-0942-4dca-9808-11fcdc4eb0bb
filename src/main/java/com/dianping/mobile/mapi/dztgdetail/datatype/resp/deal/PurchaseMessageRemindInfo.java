package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2024/4/16 17:34
 * https://mobile.sankuai.com/studio/model/info/36233
 */
@Data
@TypeDoc(description = "团单特征扩展")
@MobileDo(id = 0x931e)
public class PurchaseMessageRemindInfo implements Serializable {
    @MobileDo.MobileField(key = 0x8f0c)
    @FieldDoc(description = "弹窗类型")
    private String type;

    @MobileDo.MobileField(key = 0x24cc)
    @FieldDoc(description = "弹窗标题")
    private String title;

    @MobileDo.MobileField(key = 0xcce)
    @FieldDoc(description = "弹窗内容")
    private String content;

    @MobileDo.MobileField(key = 0x509a)
    @FieldDoc(description = "弹窗确认按钮文案")
    private String confirmBtnTxt;
}
