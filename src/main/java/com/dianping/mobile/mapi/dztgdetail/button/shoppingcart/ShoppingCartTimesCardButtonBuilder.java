package com.dianping.mobile.mapi.dztgdetail.button.shoppingcart;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PriceRuleModule;
import com.dianping.mobile.mapi.dztgdetail.helper.BuyButtonHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.PriceHelper;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.merchantcard.timescard.exposure.dto.CardSummaryBarDTO;
import org.apache.commons.lang3.StringUtils;

import static com.dianping.mobile.mapi.dztgdetail.common.enums.ShoppingCartStatusEnum.*;

/**
 * <AUTHOR>
 * @date 2023/5/15
 */
public class ShoppingCartTimesCardButtonBuilder extends AbstractButtonBuilder {

    @Override
    public void doBuild(DealCtx context, ButtonBuilderChain chain) {
        if (BuyButtonHelper.isValidTimesCard(context)) {
            if(DealBuyHelper.xiYuShowMarketPrice(context) || DealBuyHelper.joyShowMarketPrice(context)){
                DealBuyBtn joyTimesCardButton = DealBuyHelper.getJoyTimesCardButton(context);
                joyTimesCardButton.setAddShoppingCartStatus(NONE.code);
                joyTimesCardButton.setPriceRuleModule(buildPriceRuleModule(context));

                //购物车button文案
                joyTimesCardButton.setBtnTitle("立即开卡");
                setBtnTag(joyTimesCardButton, context);

                context.addButton(joyTimesCardButton);
            }else {
                DealBuyBtn timesCardButton = DealBuyHelper.getTimesCardButton(context);
                timesCardButton.setAddShoppingCartStatus(NONE.code);
                timesCardButton.setPriceRuleModule(buildPriceRuleModule(context));

                //购物车button文案
                timesCardButton.setBtnTitle("立即开卡");
                setBtnTag(timesCardButton, context);

                context.addButton(timesCardButton);
            }
        }

        chain.build(context);
    }

    private void setBtnTag(DealBuyBtn button, DealCtx context) {
        CardSummaryBarDTO timesCard = context.getTimesCard();
        if(timesCard == null){
            return;
        }

        int timesCardTimes = timesCard.getTimes();
        String priceStr = PriceHelper.dropLastZero(BuyButtonHelper.getTimesCardTruthPrice(context));
        String btnTagPreStr= String.format("%s次卡", timesCardTimes);

        if (StringUtils.isNotEmpty(timesCard.getBtnTag())) {
            PriceDisplayDTO normalPrice = context.getPriceContext().getNormalPrice();
            if (normalPrice != null && normalPrice.getMarketPrice() != null) {
                String btnTag = btnTagPreStr + "共省￥"
                        + normalPrice.getMarketPrice().subtract(new BigDecimal(priceStr)).multiply(new BigDecimal(timesCardTimes))
                        .setScale(1, RoundingMode.CEILING).stripTrailingZeros().toPlainString();
                button.setBtnTag(btnTag);
            }
        }
    }

    private PriceRuleModule buildPriceRuleModule(DealCtx context) {
        PriceRuleModule priceRuleModule = new PriceRuleModule();
        priceRuleModule.setPriceRuleType(BuyBtnTypeEnum.TIMES_CARD.getCode());
        priceRuleModule.setPriceRuleTags(buildPriceRuleTag(context));
        priceRuleModule.setPromoDesc(buildPromoDesc(context));
        return priceRuleModule;
    }

    private List<String> buildPriceRuleTag(DealCtx context) {
        List<String> priceRuleTags = Lists.newArrayList();

        String priceRuleTitle= String.format("%s次卡", context.getTimesCard().getTimes());
        String pricePerTime = "￥" + PriceHelper.dropLastZero(BuyButtonHelper.getTimesCardTruthPrice(context)) + "/次";

        priceRuleTags.add(priceRuleTitle);
        priceRuleTags.add(pricePerTime);

        return priceRuleTags;
    }

    private String buildPromoDesc(DealCtx context) {
        PriceDisplayDTO normalPrice = context.getPriceContext().getNormalPrice();
        String priceStr = PriceHelper.dropLastZero(BuyButtonHelper.getTimesCardTruthPrice(context));
        if (normalPrice != null && normalPrice.getMarketPrice() != null) {
            return "单次省￥"
                    + normalPrice.getMarketPrice().subtract(new BigDecimal(priceStr))
                    .setScale(1, RoundingMode.CEILING).stripTrailingZeros().toPlainString();
        }

        return context.getTimesCard().getBtnTag();
    }
}
