package com.dianping.mobile.mapi.dztgdetail.action.h5;

import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedGetCouponReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.coupon.UnifiedGetCouponModule;
import com.dianping.mobile.mapi.dztgdetail.facade.UnifiedCouponFacade;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.HttpMethod;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

/**
 * Created by zuomlin on 2018/12/13.
 */
@InterfaceDoc(displayName = "到综团单促销统一领券H5接口",
        type = "restful",
        description = "统一领券中心——用户领取商家券，用户在团详页等领券组件中点击领券时触发的领券行为",
        scenarios = "统一领券中心——用户领取商家券，用户在团详页等领券组件中点击领券时触发的领券行为",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "yangquan02"
)
@Controller("general/platform/dztgdetail/unifiedgetcoupon.json")
@Action(url = "unifiedgetcoupon.json", httpType = "post", protocol = ReqProtocol.REST)
public class UnifiedGetCouponH5Action extends AbsAction<UnifiedGetCouponReq> {

    @Autowired
    private UnifiedCouponFacade unifiedCouponFacade;

    @MethodDoc(requestMethods = HttpMethod.POST,
            urls = "unifiedgetcoupon.json",
            displayName = "到综团单促销统一领券H5接口",
            description = "统一领券中心——用户领取商家券，用户在团详页等领券组件中点击领券时触发的领券行为。如果领券失败，则后端不返回数据。",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "unifiedgetcoupon.json请求参数",
                            type = UnifiedGetCouponReq.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "result", description = "领券返回数据信息", type = UnifiedGetCouponModule.class)},
            restExampleUrl = "http://mapi.51ping.com/general/platform/dztgdetail/unifiedgetcoupon.json?",
            restExamplePostData = "{}",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    ),
            }
    )
    @Override
    protected IMobileResponse validate(UnifiedGetCouponReq request, IMobileContext iMobileContext) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.action.h5.UnifiedGetCouponH5Action.validate(UnifiedGetCouponReq,IMobileContext)");
        IdUpgradeUtils.processProductIdForUnifiedGetCouponReq(request, "unifiedgetcoupon.json");
        if (request == null || request.getDealGroupId() <= 0 || StringUtils.isEmpty(request.getCouponGroupId())) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @Override
    protected IMobileResponse execute(UnifiedGetCouponReq request, IMobileContext iMobileContext) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.action.h5.UnifiedGetCouponH5Action.execute(UnifiedGetCouponReq,IMobileContext)");
        try {
            EnvCtx envCtx = initEnvCtxFromH5(iMobileContext, true);
            envCtx.setClientType(request.getClientType());

            if (!envCtx.isLogin()) {
                return Resps.USER_ERROR;
            }
            UnifiedGetCouponModule result = unifiedCouponFacade.queryUnifiedGetCouponModule(request, envCtx, iMobileContext);
            if (result == null || !result.isSuccess()) {
                return Resps.COUPON_FAIL;
            }
            return new CommonMobileResponse(result);
        } catch (Exception e) {
            logger.error("unifiedgetcoupon.bin failed, params: request={}, context ={}", request, iMobileContext);
        }
        return Resps.SYSTEM_ERROR;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.action.h5.UnifiedGetCouponH5Action.getRule()");
        return null;
    }
}
