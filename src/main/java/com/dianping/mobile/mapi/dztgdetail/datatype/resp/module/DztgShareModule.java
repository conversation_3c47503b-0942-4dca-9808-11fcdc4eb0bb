package com.dianping.mobile.mapi.dztgdetail.datatype.resp.module;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.TransParamModuleDTO;
import lombok.Data;

import java.io.Serializable;

@MobileDo(id = 0xbb4a)
@Data
public class DztgShareModule implements Serializable {
    /**
    * 点评在用字段
    */
    @MobileField(key = 0xc8c)
    private DpDztgShareModule dp;

    /**
    * 美团在用字段
    */
    @MobileField(key = 0xda7)
    private MtDztgShareModule mt;

    /**
     * 分享透传参数
     */
    @MobileField(key = 0x917)
    private TransParamModuleDTO transParam;

    /**
     * shareId
     */
    @MobileField(key = 0xa72a)
    private String shareId;

    /**
     * screenShotShareEnable
     */
    @MobileField(key = 0x652b)
    private Boolean screenShotShareEnable;

    public DpDztgShareModule getDp() {
        return dp;
    }

    public void setDp(DpDztgShareModule dp) {
        this.dp = dp;
    }

    public MtDztgShareModule getMt() {
        return mt;
    }

    public void setMt(MtDztgShareModule mt) {
        this.mt = mt;
    }

    public TransParamModuleDTO getTransParam() {
        return transParam;
    }

    public void setTransParam(TransParamModuleDTO transParam) {
        this.transParam = transParam;
    }

    public String getShareId() {
        return shareId;
    }

    public void setShareId(String shareId) {
        this.shareId = shareId;
    }
}