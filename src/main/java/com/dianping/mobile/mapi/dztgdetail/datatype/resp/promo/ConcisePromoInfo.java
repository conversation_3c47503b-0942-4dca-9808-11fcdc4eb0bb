package com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/12/13.
 */
@TypeDoc(description = "简洁优惠信息")
@MobileDo(id = 0x5175)
public class ConcisePromoInfo implements Serializable {

    @FieldDoc(description = "名称，比如:优惠")
    @MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "一种优惠信息类型对应一个PromoInfoItem")
    @MobileField(key = 0x926)
    private List<PromoInfoItem> promoInfoItems;

    @FieldDoc(description = "引导文案，e.g. 去领券/查看优惠/更多优惠")
    @MobileField(key = 0x17aa)
    private String leadText;

    @FieldDoc(description = "引导动作，0：无引导 1：弹出浮层 2：跳转leadRedirectUrl")
    @MobileField(key = 0x1c63)
    private int leadAction;

    @FieldDoc(description = "引导跳转链接")
    @MobileField(key = 0x3001)
    private String leadRedirectUrl;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<PromoInfoItem> getPromoInfoItems() {
        return promoInfoItems;
    }

    public void setPromoInfoItems(List<PromoInfoItem> promoInfoItems) {
        this.promoInfoItems = promoInfoItems;
    }

    public String getLeadText() {
        return leadText;
    }

    public void setLeadText(String leadText) {
        this.leadText = leadText;
    }

    public int getLeadAction() {
        return leadAction;
    }

    public void setLeadAction(int leadAction) {
        this.leadAction = leadAction;
    }

    public String getLeadRedirectUrl() {
        return leadRedirectUrl;
    }

    public void setLeadRedirectUrl(String leadRedirectUrl) {
        this.leadRedirectUrl = leadRedirectUrl;
    }
}