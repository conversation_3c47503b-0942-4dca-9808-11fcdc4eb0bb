package com.dianping.mobile.mapi.dztgdetail.action.app;


import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedDealsReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedDeals;
import com.dianping.mobile.mapi.dztgdetail.facade.DealQueryFacade;
import com.dianping.mobile.mapi.dztgdetail.util.AntiCrawlerUtils;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.HttpMethod;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;
import java.util.Objects;

@InterfaceDoc(displayName = "到综团单关联团单信息查询接口",
        type = "restful",
        description = "查询到综团单关联团单信息",
        scenarios = "该接口仅适用于双平台APP站点的团购详情页",
        host = "http://mapi.dianping.com/general/platform/getrelateddeals/",
        authors = "wangziyun04"
)
@Controller("general/platform/dztgdetail/getrelateddeals.bin")
@Action(url = "getrelateddeals.bin", httpType = "get")
public class RelatedDealsAction extends AbsAction<RelatedDealsReq> {

    @Autowired
    private DealQueryFacade dealQueryFacade;

    @Override
    protected IMobileResponse validate(RelatedDealsReq request, IMobileContext iMobileContext) {
        IdUpgradeUtils.processProductIdForRelatedDealsReq(request, "getrelateddeals.bin");
        if(request == null || request.getDealGroupId() <= 0){
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "relateddeals.bin",
            displayName = "查询到综团单关联团单信息",
            description = "查询到综团单关联团单信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "getrelateddeals.bin请求参数",
                            type = DealBaseReq.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "result", description = "团购picasso数据",type = RelatedDeals.class)},
            //restExampleUrl = "",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    ),
            }
    )
    @Override
    @CryptMethod
    protected IMobileResponse execute(RelatedDealsReq request, IMobileContext iMobileContext) {

        try {
            EnvCtx envCtx = initEnvCtx(iMobileContext);
            RelatedDeals result = dealQueryFacade.queryRelatedDeals(request, envCtx);

            if (result != null) {
                // 反爬信息处理
                hideKeyInfo(result, iMobileContext);
                return new CommonMobileResponse(result);
            }
        } catch (Exception e) {
            logger.error("relateddeals.bin error",e);
        }

        return Resps.SYSTEM_ERROR;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }

    private void hideKeyInfo(RelatedDeals result, IMobileContext ctx) {
        if ( Objects.isNull(result)) {
            return;
        }
        if (!AntiCrawlerUtils.hide(ctx)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(result.getDealInfos())) {
            result.getDealInfos().forEach(deal -> {
                deal.setTabName(StringUtils.EMPTY);
            });
        }
    }
}
