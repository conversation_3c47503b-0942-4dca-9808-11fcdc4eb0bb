package com.dianping.mobile.mapi.dztgdetail.datatype.context;

import com.dianping.deal.style.dto.laout.DealPageLayoutDTO;
import lombok.Data;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

/**
 * @Author: g<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/10/25 13:47
 */
@Data
public class FlashFutureCtx {

    /**
     * 查询中心
     */
    private Future queryCenterFuture;
    /**
     * 团详样式信息Future
     */
    private Future dealStyleBPOFuture;
    /**
     * 定制样式团购详情Future
     */
    private Future dealModuleDetailFuture;
    /**
     * 通用样式团购详情Future
     */
    private Future dealDetailStructFuture;
    /**
     * 团详布局信息Future
     */
    private Future dealPageLayoutFuture;

}
