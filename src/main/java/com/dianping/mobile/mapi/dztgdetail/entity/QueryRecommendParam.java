package com.dianping.mobile.mapi.dztgdetail.entity;

import com.dianping.martgeneral.recommend.api.enums.PlatformEnum;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-04
 * @desc 查询推荐接口的请求参数
 */
@Data
@Builder
public class QueryRecommendParam {
    private boolean isMt;
    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * 点评侧需要
     */
    private String dpId;
    /**
     * 美团侧需要
     */
    private String uuid;
    /**
     * 用户id，缺失时传null
     */
    private String originUserId;
    /**
     * 平台
     * @see PlatformEnum
     */
    private PlatformEnum platformEnum;
    /**
     * 纬度
     */
    private Double latitude;
    /**
     * 经度
     */
    private Double longitude;
    /**
     * 款式id
     */
    private List<String> exhibitImgIds;
    /**
     * 快筛词id
     */
    private String filterIds;
    /**
     * sortType: 排序方式
     * 智能排序: AI
     * 离我最近: distance
     * 好评优先: comment
     * 新款优先: update
     * 价格最低: priceAsc
     * 不传时默认 智能排序
     */
    private String sortType;
    /**
     * 流量位标识；001标识商详页、002标识团详页
     */
    private String flowFlag;
    /**
     * 最小星级门槛，全部星级传0或不传即可,50分制
     */
    private String shopRatingThreshold;
    private Long shopId;
    private Integer start;
    private Integer limit;
    private Integer clientType;
    /**
     * 二级类目ID
     */
    private Integer categoryId;
    /**
     * 团单到手价
     */
    private String dealGroupPrice;
    /**
     * 当传”全部“时需要传isAll='1'，否则传其他值，全部时会屏蔽filterIds，即filterIds传任何值都会失效
     */
    private String isAll;
    /**
     * 当前团单标签id
     */
    private String dealGroupTagIds;

    /**
     * 点评团单id
     */
    private Long dpDealGroupId;
}
