package com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler.flatten;

import com.dianping.deal.bff.cache.enums.RcfDealBffCacheEnum;
import com.dianping.mobile.mapi.dztgdetail.rcf.enums.ModuleType;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2025/1/2 10:25
 */
@Component
public class FlattenProcessorFactory implements InitializingBean {

    @Resource
    private List<DealModuleFlattenProcessor> flattenProcessors;
    private final Map<ModuleType, DealModuleFlattenProcessor> flattenProcessorMap = new HashMap<>();

    public DealModuleFlattenProcessor getFlattenHandler(ModuleType type){
       return flattenProcessorMap.get(type);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        for(DealModuleFlattenProcessor processor : flattenProcessors){
            flattenProcessorMap.put(processor.getType(), processor);
        }
    }
}
