package com.dianping.mobile.mapi.dztgdetail.biz.processor.promo;

import com.dianping.gm.bonus.exposure.api.dto.CommonBonusExposureDTO;
import com.dianping.gm.bonus.exposure.api.enums.BonusTypeEnum;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.common.constants.PlusIcons;
import com.dianping.mobile.mapi.dztgdetail.common.enums.LeadActionEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.PromoTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ComBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo.*;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.PromoHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper;
import com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil;
import com.dianping.mobile.mapi.dztgdetail.util.PlusNumUtils;
import com.dianping.mobile.mapi.dztgdetail.util.PromoInfoHelper;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.tgc.open.entity.ActivityDTO;
import com.dianping.tgc.open.entity.QueryResponseDTO;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.util.List;

import static com.dianping.mobile.mapi.dztgdetail.util.PromoInfoHelper.buildExposurePromoInfo;

/**
 * Created by zuomlin on 2018/12/18.
 */
public class PromoPostProcessor extends AbsDealProcessor {

    @Override
    public void prepare(DealCtx ctx) {
    }

    @Override
    public void process(DealCtx ctx) {
        DealPromoModule result = new DealPromoModule();
        ConcisePromoInfo concisePromoInfo = buildConcisePromoModule(ctx);
        DetailPromoInfo detailPromoInfo = buildDetailPromoModule(ctx);

        ExposurePromoInfo exposurePromoInfo = buildExposurePromoInfo(ctx);
        result.setExposurePromoInfo(exposurePromoInfo);

        PromoAggInfo promoAggInfo = PromoInfoHelper.buildPromoAggInfo(ctx);
        result.setPromoAggInfo(promoAggInfo);

        if (concisePromoInfo != null && (containsIdlePromo(concisePromoInfo) || detailPromoInfo != null)) {
            result.setConcisePromoInfo(concisePromoInfo);
            result.setDetailPromoInfo(detailPromoInfo);
        }

        ctx.setDealPromoModuleResult(result);
    }

    private boolean containsIdlePromo(ConcisePromoInfo concisePromoInfo) {
        return concisePromoInfo.getPromoInfoItems().get(0).getType() == PromoTypeEnum.IDLE_HOURS.getCode();
    }

    private ConcisePromoInfo buildConcisePromoModule(DealCtx ctx) {
        ConcisePromoInfo result = new ConcisePromoInfo();
        result.setTitle("优惠");
        List<PromoInfoItem> concisePromoInfoItems = Lists.newArrayList();
        result.setPromoInfoItems(concisePromoInfoItems);

        PromoInfoItem idleHoursPromo = buildConciseIdleHoursPromo(ctx);
        PromoInfoItem promoInfoItem = buildConcisePromoInfo(ctx.getPromoList(), ctx.isMt());
        PromoInfoItem pinProductInfoItem = buildConcisePinProductInfo(ctx.getPinProductBrief(), ctx.isMt());
        PromoInfoItem couponInfoItem = buildConciseCouponInfo(ctx);
        PromoInfoItem bonusInfoItem = buildConciseBonusInfo(ctx, ctx.getBonusExposureDTOList(), ctx.isMt());

        addPromo(concisePromoInfoItems, pinProductInfoItem);
        addPromo(concisePromoInfoItems, promoInfoItem);
        addPromo(concisePromoInfoItems, couponInfoItem);
        addPromo(concisePromoInfoItems, bonusInfoItem);

        if (CollectionUtils.isEmpty(concisePromoInfoItems) && idleHoursPromo == null) {
            return null;
        }
        if (concisePromoInfoItems.size() > 3) {
            result.setPromoInfoItems(concisePromoInfoItems.subList(0, 3));
        }

        if (concisePromoInfoItems.size() == 1) {
            if (promoInfoItem != null) {
                result.setLeadAction(LeadActionEnum.NOOP.getCode());//只有立减 不显示引导文案, 不跳转
            } else if (couponInfoItem != null) {
                result.setLeadAction(LeadActionEnum.TOAST_DEFAULT.getCode());//只有抵用券 显示去领取, 弹出浮层
                result.setLeadText("去领券");
            } else if (bonusInfoItem != null) {
                result.setLeadAction(LeadActionEnum.REDIRECT_URL.getCode());//只有返礼, 直接跳转
                result.setLeadRedirectUrl(bonusInfoItem.getRedirectUrl());
                bonusInfoItem.setRedirectUrl(null);
            }
        } else if (concisePromoInfoItems.size() <= 3) {
            result.setLeadAction(LeadActionEnum.TOAST_DEFAULT.getCode());
            result.setLeadText("查看优惠");
        } else {
            result.setLeadAction(LeadActionEnum.TOAST_DEFAULT.getCode());
            result.setLeadText("更多优惠");
        }

        if (concisePromoInfoItems.size() > 1) {
            for (PromoInfoItem item : concisePromoInfoItems) {
                item.setLeadUrl("");
                item.setLeadText("");
                item.setLeadAction(LeadActionEnum.NOOP.getCode());
            }
            concisePromoInfoItems.get(0).setLeadText("查看优惠");
            concisePromoInfoItems.get(0).setLeadAction(LeadActionEnum.TOAST_DEFAULT.getCode());
        }

        if (idleHoursPromo != null) {
            concisePromoInfoItems.add(0, idleHoursPromo);
        }

        return result;
    }

    private DetailPromoInfo buildDetailPromoModule(DealCtx ctx) {
        DetailPromoInfo result = new DetailPromoInfo();
        result.setTitle("优惠");
        List<PromoInfoItem> detailPromoInfoItems = Lists.newArrayList();
        result.setPromoInfoItems(detailPromoInfoItems);

        PromoInfoItem pinProductInfoItem = buildDetailPinProductInfo(ctx.getPinProductBrief());
        PromoInfoItem promoInfoItem = buildDetailPromoInfo(ctx.getPromoList());
        PromoInfoItem couponInfoItem = buildDetailCouponInfo(ctx, ctx.getVoucherList());
        PromoInfoItem bonusInfoItem = buildDetailBonusInfo(ctx);

        addPromo(detailPromoInfoItems, pinProductInfoItem);
        addPromo(detailPromoInfoItems, promoInfoItem);
        addPromo(detailPromoInfoItems, bonusInfoItem);
        addPromo(detailPromoInfoItems, couponInfoItem);

        if (CollectionUtils.isEmpty(detailPromoInfoItems)) {
            return null;
        }
        return result;
    }

    private void addPromo(List<PromoInfoItem> promoInfoItems, PromoInfoItem promoInfoItem) {
        if (promoInfoItem != null) {
            promoInfoItems.add(promoInfoItem);
        }
    }

    private PromoInfoItem buildConcisePinProductInfo(PinProductBrief pinProductBrief, boolean mt) {
        if (pinProductBrief == null) {
            return null;
        }
        PromoInfoItem concisePromoInfoItem = new PromoInfoItem();
        concisePromoInfoItem.setType(PromoTypeEnum.PINTUAN.getCode());
        concisePromoInfoItem.setPromoTitle("拼团");
        concisePromoInfoItem.setIconUrl(mt ? PlusIcons.MT_POOL : PlusIcons.DP_POOL);
        List<PromoInfoDescItem> concisePromoInfoDescItems = Lists.newArrayList();
        concisePromoInfoItem.setPromoInfoDescItems(concisePromoInfoDescItems);
        PromoInfoDescItem concisePromoInfoDescItem = new PromoInfoDescItem();
        concisePromoInfoDescItems.add(concisePromoInfoDescItem);
        String concisePinTextFormat =
                String.format("%s人拼团 ￥%s", pinProductBrief.getPinPersonNum(), PlusNumUtils.trimDecimal(pinProductBrief.getPrice().toString()));
        String concisePinPoolText = mt ?
                JsonLabelUtil.pinPoolMTJson(concisePinTextFormat, "￥" + PlusNumUtils.trimDecimal(pinProductBrief.getPrice().toString())) :
                JsonLabelUtil.pinPoolDPJson(concisePinTextFormat, "￥" + PlusNumUtils.trimDecimal(pinProductBrief.getPrice().toString()));
        concisePromoInfoDescItem.setText(concisePinPoolText);
        return concisePromoInfoItem;
    }

    private PromoInfoItem buildDetailPinProductInfo(PinProductBrief pinProductBrief) {
        if (pinProductBrief == null) {
            return null;
        }
        PromoInfoItem detailPromoInfoItem = new PromoInfoItem();
        detailPromoInfoItem.setType(2);
        detailPromoInfoItem.setPromoTitle("拼团");
        List<PromoInfoDescItem> detailPromoInfoDescItems = Lists.newArrayList();
        detailPromoInfoItem.setPromoInfoDescItems(detailPromoInfoDescItems);
        detailPromoInfoDescItems.add(new PromoInfoDescItem().setText("1、先付款，再邀请好友一起购买"));
        detailPromoInfoDescItems.add(new PromoInfoDescItem().setText("2、拼团失败将自动退款"));
        detailPromoInfoDescItems.add(new PromoInfoDescItem().setText("3、不能随时退，过期未消费将自动退款"));
        return detailPromoInfoItem;
    }

    private PromoInfoItem buildConciseIdleHoursPromo(DealCtx ctx) {

        PromoDisplayDTO idlePromo = DealBuyHelper.getValidIdlePromo(ctx);
        if (idlePromo == null) {
            return null;
        }

        BigDecimal price = ctx.getDealGroupBase().getDealGroupPrice().subtract(idlePromo.getPromoAmount())
                .setScale(2, BigDecimal.ROUND_CEILING).stripTrailingZeros();
        if (price.compareTo(BigDecimal.ZERO) < 0) {
            return null;
        }

        String priceText = "￥" + price.toPlainString() + "/次";
        String promoText = idlePromo.getDescription() + "  " + priceText;
        String promoJsonText = JsonLabelUtil.idleHoursPromoJson(ctx.isMt(), promoText, priceText);

        PromoInfoItem result = new PromoInfoItem();
        result.setType(PromoTypeEnum.IDLE_HOURS.getCode());
        result.setPromoTitle("限时优惠");
        result.setIconUrl(PlusIcons.IDLE_HOURS_PROMO);
        result.setPromoInfoDescItems(Lists.newArrayList(new PromoInfoDescItem().setText(promoJsonText)));

        result.setLeadText("去购买");
        result.setLeadAction(LeadActionEnum.TOAST_IDLE_HOURS.getCode());

        return result;
    }

    private PromoInfoItem buildConcisePromoInfo(List<PromoDisplayDTO> promoDisplayList, boolean mt) {

        List<PromoDisplayDTO> normalPromos = PromoHelper.getNormalPromo(promoDisplayList);

        if (CollectionUtils.isEmpty(normalPromos)) {
            return null;
        }

        PromoInfoItem result = new PromoInfoItem();
        result.setType(PromoTypeEnum.REDUCTION.getCode());
        result.setPromoTitle("立减");
        result.setIconUrl(mt ? PlusIcons.MT_PROMO : PlusIcons.DP_PROMO);
        List<PromoInfoDescItem> concisePromoInfoDescItems = Lists.newArrayList();
        result.setPromoInfoDescItems(concisePromoInfoDescItems);
        String promoText = normalPromos.get(0).getDescription();
        String hiLight = "减" + PlusNumUtils.trimDecimal(normalPromos.get(0).getPromoAmount().toString()) + "元";
        String promoJsonText = mt ? JsonLabelUtil.promoMTJson(promoText, hiLight) : JsonLabelUtil.promoDPJson(promoText, hiLight);
        concisePromoInfoDescItems.add(new PromoInfoDescItem().setText(promoJsonText));
        return result;
    }

    private PromoInfoItem buildDetailPromoInfo(List<PromoDisplayDTO> promoDisplayList) {
        promoDisplayList = PromoHelper.getNormalPromo(promoDisplayList);
        if (CollectionUtils.isEmpty(promoDisplayList)) {
            return null;
        }
        PromoInfoItem result = new PromoInfoItem();
        result.setType(1);
        result.setPromoTitle("立减");
        List<PromoInfoDescItem> detailPromoInfoDescItems = Lists.newArrayList();
        result.setPromoInfoDescItems(detailPromoInfoDescItems);
        for (PromoDisplayDTO promoDisplayDTO : promoDisplayList) {
            if (promoDisplayDTO == null || StringUtils.isBlank(promoDisplayDTO.getDescription())) {
                continue;
            }
            detailPromoInfoDescItems.add(new PromoInfoDescItem().setText(promoDisplayDTO.getDescription()));
        }
        return result;
    }

    private PromoInfoItem buildConciseBonusInfo(DealCtx ctx, List<CommonBonusExposureDTO> bonusExposureDTOList, boolean mt) {
        if (CollectionUtils.isEmpty(bonusExposureDTOList) || bonusExposureDTOList.get(0) == null
                || StringUtils.isBlank(bonusExposureDTOList.get(0).getSummaryInfo())) {
            return null;
        }
        PromoInfoItem result = new PromoInfoItem();
        result.setType(PromoTypeEnum.BONUS.getCode());
        result.setPromoTitle("返礼");
        result.setIconUrl(mt ? PlusIcons.MT_BONUS : PlusIcons.DP_BONUS);
        List<PromoInfoDescItem> concisePromoInfoDescItems = Lists.newArrayList();
        result.setPromoInfoDescItems(concisePromoInfoDescItems);
        String bonusTitle = bonusExposureDTOList.get(0).getSummaryInfo();
        if (bonusTitle.lastIndexOf("返") > 0) {
            String highLightTitle = bonusTitle.substring(bonusTitle.lastIndexOf("返"), bonusTitle.length());
            String bonusJsonText = mt ?
                    JsonLabelUtil.bonusMTJson(bonusTitle, highLightTitle) :
                    JsonLabelUtil.bonusDPJson(bonusTitle, highLightTitle);
            concisePromoInfoDescItems.add(new PromoInfoDescItem().setText(bonusJsonText));
        } else {
            concisePromoInfoDescItems.add(new PromoInfoDescItem().setText(bonusTitle));
        }
        if (Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.bonus.redirect.enable", false)) {
            result.setRedirectUrl(UrlHelper.getBonusUrl(ctx));
        }

        result.setLeadAction(LeadActionEnum.REDIRECT_URL.getCode());
        result.setLeadUrl(result.getRedirectUrl());
        return result;
    }

    private PromoInfoItem buildDetailBonusInfo(DealCtx ctx) {
        if (ctx == null || CollectionUtils.isEmpty(ctx.getBonusExposureDTOList())) {
            return null;
        }
        PromoInfoItem result = new PromoInfoItem();
        result.setType(4);
        result.setPromoTitle("返礼");
        List<PromoInfoDescItem> detailPromoInfoDescItems = Lists.newArrayList();
        result.setPromoInfoDescItems(detailPromoInfoDescItems);
        for (CommonBonusExposureDTO bonusExposureDTO : ctx.getBonusExposureDTOList()) {
            if (bonusExposureDTO == null || StringUtils.isBlank(bonusExposureDTO.getSummaryInfo())) {
                continue;
            }
            String highLight = BonusTypeEnum.getBonusTypeEnumByCode(bonusExposureDTO.getBonusType()).getDesc();
            String bonusText = highLight + ":" + bonusExposureDTO.getConditionInfo() + "返" + bonusExposureDTO.getGiftInfo();
            String bonusDesc = ctx.isMt() ? JsonLabelUtil.bonusDescMTJson(bonusText, highLight) : JsonLabelUtil.bonusDescDPJson(bonusText, highLight);
            if (StringUtils.isEmpty(bonusDesc)) {
                continue;
            }
            detailPromoInfoDescItems.add(new PromoInfoDescItem().setText(bonusDesc));
        }
        if (Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.bonus.redirect.enable", false)) {
            result.setRedirectUrl(UrlHelper.getBonusUrl(ctx));
        }
        return result;
    }

    private PromoInfoItem buildConciseCouponInfo(DealCtx ctx) {

        if (isPromoProxyCategory(ctx)) {
            return PromoInfoHelper.genCouponInfoItem(ctx);
        }

        QueryResponseDTO voucherResult = ctx.getVoucherList();
        boolean mt = ctx.isMt();

        if (voucherResult == null || CollectionUtils.isEmpty(voucherResult.getActivityDTOs())) {
            return null;
        }
        PromoInfoItem promoInfoItem = new PromoInfoItem();
        promoInfoItem.setType(PromoTypeEnum.COUPON.getCode());
        promoInfoItem.setPromoTitle("抵用券");
        promoInfoItem.setIconUrl(mt ? PlusIcons.MT_COUPON : PlusIcons.DP_COUPON);
        List<ActivityDTO> activityDTOS = voucherResult.getActivityDTOs();
        List<PromoInfoDescItem> concisePromoInfoDescItems = Lists.newArrayList();
        for (ActivityDTO activityDTO : activityDTOS) {
            PromoInfoDescItem promoInfoDescItem = new PromoInfoDescItem();
            if (buildConciseCouponText(activityDTO) == null) {
                continue;
            }
            if (activityDTO.getCouponDTO().isUpDailyLimit()) {
                continue;
            }
            promoInfoDescItem.setStyle(1);
            promoInfoDescItem.setText(buildConciseCouponText(activityDTO));
            concisePromoInfoDescItems.add(promoInfoDescItem);
        }
        if (CollectionUtils.isEmpty(concisePromoInfoDescItems)) {
            return null;
        }
        if (concisePromoInfoDescItems.size() > 2) {
            promoInfoItem.setPromoInfoDescItems(concisePromoInfoDescItems.subList(0, 2));
        } else {
            promoInfoItem.setPromoInfoDescItems(concisePromoInfoDescItems);
        }

        promoInfoItem.setLeadText("去领券");
        promoInfoItem.setLeadAction(LeadActionEnum.TOAST_DEFAULT.getCode());
        return promoInfoItem;
    }

    private static String buildConciseCouponText(ActivityDTO activityDTO) {
        if (activityDTO == null || activityDTO.getCouponDTO() == null) {
            return null;
        }
        if (BigDecimal.ZERO.compareTo(activityDTO.getCouponDTO().getFullCutAmount()) < 0) {
            return "满" + PlusNumUtils.trimDecimal(activityDTO.getCouponDTO().getFullCutAmount().toString())
                    + "减" + PlusNumUtils.trimDecimal(activityDTO.getCouponDTO().getDiscountAmount().toString()) + "券";
        } else {
            if (activityDTO.getCouponDTO().getDiscountAmount().compareTo(BigDecimal.ZERO) > 0) {
                return PlusNumUtils.trimDecimal(activityDTO.getCouponDTO().getDiscountAmount().toString()) + "元无门槛券";
            }
        }
        return null;
    }

    private PromoInfoItem buildDetailCouponInfo(DealCtx ctx, QueryResponseDTO voucherResult) {
        if (voucherResult == null || CollectionUtils.isEmpty(voucherResult.getActivityDTOs())) {
            return null;
        }
        PromoInfoItem promoInfoItem = new PromoInfoItem();
        promoInfoItem.setType(3);
        promoInfoItem.setPromoTitle("抵用券");
        promoInfoItem.setStyle(1);
        List<ActivityDTO> activityDTOS = voucherResult.getActivityDTOs();
        List<CouponDescItem> couponDescItems = Lists.newArrayList();
        for (ActivityDTO activityDTO : activityDTOS) {
            if (activityDTO == null || activityDTO.getCouponDTO() == null || activityDTO.getCouponDTO().getDiscountAmount().compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            if (activityDTO.getCouponDTO().isUpDailyLimit()) {
                continue;
            }
            CouponDescItem couponDescItem = new CouponDescItem();
            couponDescItem.setCouponGroupId(activityDTO.getActivityID());
            couponDescItem.setStatus(activityDTO.isUserHasCoupon() ? 1 : 0);
            couponDescItem.setEncryptCouponGroupId(activityDTO.getCouponDTO().getNewCouponGroupId());

            ComBtn btn = new ComBtn();

            couponDescItem.setButton(btn);
            if (!activityDTO.isUserHasCoupon()) {
                btn.setTitle("点击领取");
                btn.setClickUrl("");
                btn.setActionType(ComBtn.ActionEnum.SHOW.type);
            } else {
                btn.setTitle("去使用");
                btn.setActionType(ComBtn.ActionEnum.REDIRECT.type);
                if (ctx.getEnvCtx().isMainWeb()) {
                    btn.setClickUrl(ctx.isMt() ?
                            UrlHelper.getCouponMtWebUrl(activityDTO.getUserCouponId()) :
                            UrlHelper.getCouponDpWebUrl(activityDTO.getUserCouponId()));
                } else if (ctx.getEnvCtx().isMainWX()) {
                    btn.setClickUrl(ctx.isMt() ?
                            UrlHelper.getWxBaseShareUrl(UrlHelper.getCouponMtWebUrl(activityDTO.getUserCouponId()), true) :
                            UrlHelper.getWxBaseShareUrl(UrlHelper.getCouponDpWebUrl(activityDTO.getUserCouponId()), false));
                } else {
                    btn.setClickUrl(ctx.isMt() ?
                            UrlHelper.getCouponMtUrl(activityDTO.getUserCouponId()) :
                            UrlHelper.getCouponDpUrl(activityDTO.getUserCouponId()));
                }
            }

            String couponDesc = null;
            if (activityDTO.getCouponDTO().getEffectiveType() == 10) {
                couponDesc = "领取后" + activityDTO.getCouponDTO().getEffectiveValue() + "天有效";
            }
            if (activityDTO.getCouponDTO().getEffectiveType() == 20) {
                DateFormat dateFormat = DateFormat.getDateInstance();
                couponDesc = dateFormat.format(activityDTO.getCouponDTO().getEffectiveEndDate()) + "结束";
            }
            String couponTileText = activityDTO.getCouponDesc();
            String couponRulesText = buildDetailCouponDescText(activityDTO);
            String amountText = PlusNumUtils.trimDecimal(activityDTO.getCouponDTO().getDiscountAmount().toString());
            String couponAmountText = "￥ " + amountText;

            if (ctx.isMt()) {
                couponDescItem.setTitle(JsonLabelUtil.couponTitleMTJson(couponTileText, couponTileText));
                couponDescItem.setAmount(JsonLabelUtil.couponAmountMTJson(couponAmountText, amountText));
                couponDescItem.setDesc(JsonLabelUtil.couponAmountDescMTJson(couponDesc, couponDesc));
                couponDescItem.setAmountDesc(JsonLabelUtil.couponAmountRulesMTJson(couponRulesText, couponRulesText));
            } else {
                couponDescItem.setAmount(amountText);
                couponDescItem.setTitle(JsonLabelUtil.couponTitleDPJson(couponTileText, couponAmountText));
                couponDescItem.setDesc(JsonLabelUtil.couponAmountDescDPJson(couponDesc, couponDesc));
                couponDescItem.setAmountDesc(JsonLabelUtil.couponAmountRulesDPJson(couponRulesText, couponRulesText));
            }
            couponDescItems.add(couponDescItem);
        }
        if (CollectionUtils.isEmpty(couponDescItems)) {
            return null;
        }
        promoInfoItem.setCouponDescItems(couponDescItems);
        return promoInfoItem;
    }

    private static String buildDetailCouponDescText(ActivityDTO activityDTO) {
        if (activityDTO == null || activityDTO.getCouponDTO() == null) {
            return null;
        }
        if (CollectionUtils.isEmpty(activityDTO.getCouponDTO().getUseRules())) {
            return null;
        }
        return activityDTO.getCouponDTO().getUseRules().get(0);
    }

}
