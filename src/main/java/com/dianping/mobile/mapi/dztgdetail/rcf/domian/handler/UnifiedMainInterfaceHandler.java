package com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.deal.bff.cache.enums.RcfDealBffInterfaceEnum;
import com.dianping.mobile.mapi.dztgdetail.rcf.api.req.DealNativeSnapshotReq;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.DealRcfCustomerProcessor;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.dto.DealBffResponseDTO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @date: 2025/07/30
 */
@Component
public class UnifiedMainInterfaceHandler implements DealRcfCustomerProcessor {
    @Override
    public void customerProcess(DealNativeSnapshotReq request, DealBffResponseDTO bffResponse) {
        if (bffResponse == null) {
            return;
        }
        JSONObject unifiedMainInterfaceInfo = bffResponse.getBffResponse(RcfDealBffInterfaceEnum.unifiedmaininterface);
        if (unifiedMainInterfaceInfo == null) {
            return;
        }
        JSONObject data = unifiedMainInterfaceInfo.getJSONObject("data");
        if (data == null) {
            return;
        }
        JSONObject response = data.getJSONObject("response");
        if (response == null) {
            return;
        }
        processStructDetail(response);
    }

    @Override
    public boolean canProcess(DealNativeSnapshotReq request, DealBffResponseDTO bffResponse) {
        return bffResponse != null &&
                Objects.nonNull(bffResponse.getBffResponse(RcfDealBffInterfaceEnum.unifiedmaininterface));
    }

    public void processStructDetail(JSONObject response) {
        if (response == null) {
            return;
        }

        // 获取 module_detail_structured_detail 模块
        JSONObject moduleDetailStructuredDetail = response.getJSONObject("module_detail_structured_detail");
        if (moduleDetailStructuredDetail == null) {
            return;
        }

        // 获取 moduleVO
        JSONObject moduleVO = moduleDetailStructuredDetail.getJSONObject("moduleVO");
        if (moduleVO == null) {
            return;
        }

        // 获取 dealDetails 数组
        JSONArray dealDetails = moduleVO.getJSONArray("dealDetails");
        if (dealDetails == null || dealDetails.isEmpty()) {
            return;
        }

        // 按照 type=2000 进行分组
        List<JSONObject> groupedDealDetails = groupDealDetailsByType2000(dealDetails);

        // 更新原数组
        moduleVO.put("dealDetails", groupedDealDetails);
    }

    /**
     * 按照 type=2000 对 dealDetails 进行分组
     *
     * @param dealDetails 原始 dealDetails 数组
     * @return 分组后的 dealDetails 数组，每个元素包含 groupDetail 属性
     */
    private List<JSONObject> groupDealDetailsByType2000(JSONArray dealDetails) {
        if (dealDetails == null) {
            return new ArrayList<>();
        }

        List<JSONObject> result = new ArrayList<>();
        List<JSONObject> currentGroup = new ArrayList<>();

        IntStream.range(0, dealDetails.size())
                .mapToObj(dealDetails::getJSONObject)
                .filter(Objects::nonNull)
                .forEach(item -> {
                    Integer type = getIntegerValue(item, "type");
                    if (type == null) {
                        return;
                    }

                    if (type == 2000) {
                        // 如果当前组不为空，先处理当前组
                        if (!currentGroup.isEmpty()) {
                            addGroupToResult(result, new ArrayList<>(currentGroup));
                            currentGroup.clear();
                        }
                        // type=2000 只作为分组标识符，不加入到新组中
                    } else {
                        // 非 type=2000 的元素加入当前组
                        currentGroup.add(item);
                    }
                });

        // 处理最后一组
        if (!currentGroup.isEmpty()) {
            addGroupToResult(result, currentGroup);
        }

        return result;
    }

    /**
     * 将分组添加到结果中，每组创建一个包含 groupDetail 属性的对象
     *
     * @param result 结果列表
     * @param group  当前分组
     */
    private void addGroupToResult(List<JSONObject> result, List<JSONObject> group) {
        if (group == null || group.isEmpty()) {
            return;
        }

        // 创建一个新的对象来表示这一组
        JSONObject groupObject = new JSONObject();

        // 创建当前组的深拷贝作为 groupDetail
        List<JSONObject> groupDetailList = group.stream()
                .filter(Objects::nonNull)
                .map(item -> {
                    JSONObject itemCopy = new JSONObject();
                    itemCopy.putAll(item);

                    // 处理 type=12 的 subcontent 字段
                    processSubContentForType12(itemCopy);

                    return itemCopy;
                })
                .collect(Collectors.toList());

        // 判断 isBall 属性
        boolean isBall = checkIsBall(group);

        // 判断 isRule 和 isPoint 属性（都是判断组内是否有type=11）
        boolean isRule = checkIsRule(group);
        boolean isPoint = isRule; // isPoint 和 isRule 逻辑相同

        // 计算 type15TitleLength 和 type2TitleLength
        int type15TitleLength = getMaxTitleLengthByType(group, 15);
        int type2TitleLength = getMaxTitleLengthByType(group, 2);

        // 设置属性
        groupObject.put("groupDetail", groupDetailList);
        groupObject.put("isBall", isBall);
        groupObject.put("isRule", isRule);
        groupObject.put("isPoint", isPoint);
        groupObject.put("type15TitleLength", type15TitleLength);
        groupObject.put("type2TitleLength", type2TitleLength);

        // 将这个组对象添加到结果中
        result.add(groupObject);
    }

    /**
     * 判断分组是否满足 isBall 条件
     *
     * @param group 当前分组
     * @return 如果分组中有type=21或者(type=15且order有值)则返回true，否则返回false
     */
    private boolean checkIsBall(List<JSONObject> group) {
        if (group == null || group.isEmpty()) {
            return false;
        }

        return group.stream()
                .filter(Objects::nonNull)
                .anyMatch(item -> {
                    Integer type = getIntegerValue(item, "type");
                    if (type == null) {
                        return false;
                    }

                    // 如果有 type=21，直接返回 true
                    if (type == 21) {
                        return true;
                    }

                    // 如果有 type=15 且 order 有值，返回 true
                    if (type == 15) {
                        Object order = item.get("order");
                        return order != null && !order.toString().trim().isEmpty();
                    }

                    return false;
                });
    }

    /**
     * 判断分组是否满足 isRule 条件
     *
     * @param group 当前分组
     * @return 如果分组中有type=11则返回true，否则返回false
     */
    private boolean checkIsRule(List<JSONObject> group) {
        if (group == null || group.isEmpty()) {
            return false;
        }

        return group.stream()
                .filter(Objects::nonNull)
                .map(item -> getIntegerValue(item, "type"))
                .filter(Objects::nonNull)
                .anyMatch(type -> type == 11);
    }

    /**
     * 获取指定类型对象的title长度的最大值
     *
     * @param group      当前分组
     * @param targetType 目标类型
     * @return 指定类型对象的title长度的最大值，如果没有找到则返回0
     */
    private int getMaxTitleLengthByType(List<JSONObject> group, int targetType) {
        if (group == null || group.isEmpty()) {
            return 0;
        }

        return group.stream()
                .filter(Objects::nonNull)
                .filter(item -> {
                    Integer type = getIntegerValue(item, "type");
                    return type != null && type == targetType;
                })
                .map(item -> item.getString("title"))
                .filter(Objects::nonNull)
                .mapToInt(String::length)
                .max()
                .orElse(0);
    }

    /**
     * 处理 type=12 的 subcontent 字段，将二维数组转换为字符串
     *
     * @param item JSON对象
     */
    private void processSubContentForType12(JSONObject item) {
        if (item == null) {
            return;
        }

        Integer type = getIntegerValue(item, "type");
        if (type == null || type != 12) {
            return;
        }

        JSONArray subContent = item.getJSONArray("subContent");
        if (subContent == null || subContent.isEmpty()) {
            return;
        }

        List<String> result = new ArrayList<>();

        // 遍历二维数组的每一行
        for (int i = 0; i < subContent.size(); i++) {
            JSONArray row = subContent.getJSONArray(i);
            if (row == null || row.isEmpty()) {
                continue;
            }

            // 将每行的元素用｜符号连接
            List<String> rowElements = new ArrayList<>();
            for (int j = 0; j < row.size(); j++) {
                String element = row.getString(j);
                if (element != null) {
                    rowElements.add(element);
                }
            }

            if (!rowElements.isEmpty()) {
                result.add(String.join("｜", rowElements));
            }
        }

        // 将处理后的字符串重新设置到 subContent 字段
        item.put("subContent", String.join("\n", result));
    }

    /**
     * 安全获取Integer值
     *
     * @param jsonObject JSON对象
     * @param key        键名
     * @return Integer值，如果不存在或转换失败返回null
     */
    private Integer getIntegerValue(JSONObject jsonObject, String key) {
        if (jsonObject == null || key == null) {
            return null;
        }


        Object value = jsonObject.get(key);
        if (value == null) {
            return null;
        }

        if (value instanceof Integer) {
            return (Integer) value;
        }

        if (value instanceof Number) {
            return ((Number) value).intValue();
        }

        if (value instanceof String) {
            return Integer.parseInt((String) value);
        }

        return null;

    }
}
