package com.dianping.mobile.mapi.dztgdetail.datatype.context;

import com.dianping.deal.sales.common.datatype.SalesDisplayDTO;
import com.dianping.deal.shop.dto.DealGroupShop;
import com.dianping.gmkt.event.datapools.api.model.seckill.SeckillSceneSimpleDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedDealModuleVO;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/5 13:56
 */
@Data
public class RelatedModuleCtx {
    /**
     * request
      */
    RelatedModuleReq req;
    /**
     * 团单信息
      */
    DealGroupDTO dealGroupDTO;
    /**
     * 门店信息
      */
    DpPoiDTO dpPoiDTO;

    /**
     * 门店基本信息（点评侧）
     */
    Map<Long, DpPoiDTO> shopIdDpPoiDTOMap;

    EnvCtx envCtx;

    /**
     * 关联最近门店信息
     */
    Map<Integer, DealGroupShop> dealGroupShopMap;

    /**
     * 团单ID映射
     */
    Map<Long, List<Long>> mtByDpShopIds;

    /**
     * 价格
     */
    Map<Integer, PriceDisplayDTO> priceDisplayMap;

    /**
     * 销量
     */
    Map<Integer, SalesDisplayDTO> productId2SaleMap;

    /**
     * 秒杀活动
     */
    Map<Integer, List<SeckillSceneSimpleDTO>> secKillSceneByMaterial;


    /**
     * 返回结果
     */
    RelatedDealModuleVO result;

}
