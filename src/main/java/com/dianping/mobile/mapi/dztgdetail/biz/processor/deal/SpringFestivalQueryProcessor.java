package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.SpringFestivalWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.entity.ReminderExtendConfig;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.poi.feature.api.dto.business.TagDto;
import com.dianping.poi.feature.api.enums.FestivalTagEnum;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 */
public class SpringFestivalQueryProcessor extends AbsDealProcessor {

    @Resource
    private SpringFestivalWrapper springFestivalWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        ReminderExtendConfig config = LionConfigUtils.getReminderExtendInfo("springFestival");
        return LionConfigUtils.validSpringFestivalConfig(config);
    }

    @Override
    public void prepare(DealCtx ctx) {
       Future springFestivalTagsFuture = springFestivalWrapper.preSpringFestivalTags(ctx.isMt(), ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId());
       ctx.getFutureCtx().setSpringFestivalTagsFuture(springFestivalTagsFuture);
    }

    @Override
    public void process(DealCtx ctx) {
        List<TagDto> springFestivalTags = springFestivalWrapper.getSpringFestivalTags(ctx.getFutureCtx().getSpringFestivalTagsFuture());
        // “春节活动”标签枚举为：FestivalTagEnum.SPRING_FESTIVAL_TAG(326565, "春节活动");
        try{
            if (CollectionUtils.isNotEmpty(springFestivalTags)){
                ctx.setSpringFestivalTag(springFestivalTags.stream().anyMatch(e -> FestivalTagEnum.SPRING_FESTIVAL_TAG.getValue() == e.getTagId()));
            }
        }catch (Exception e){
            logger.error(" SpringFestivalQueryProcessor process.error:{}", e);
        }
    }
}
