package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.highlights;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.enums.HighlightsStyleEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CommonAttrVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 亮点模块
 *
 * <AUTHOR>
 * @date 2023/5/8 16:11
 */
@Data
@MobileDo(id = 0x1208)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class DztgHighlightsModule implements Serializable {

    @FieldDoc(description = "样式，simple:样式一-简单样式, struct:样式二-结构化样式, both:样式三-均展示")
    @MobileDo.MobileField(key = 0x1b3a)
    /**
     * {@link HighlightsStyleEnum}
     */
    private String style;

    @FieldDoc(description = "亮点内容（样式一）")
    @MobileDo.MobileField(key = 0xcce)
    private String content;

    @FieldDoc(description = "亮点属性整体浮层标识")
    @MobileDo.MobileField(key = 0x98e4)
    private String identify;

    @FieldDoc(description = "亮点属性（样式二）")
    @MobileDo.MobileField(key = 0x612f)
    private List<CommonAttrVO> attrs;

    @FieldDoc(description = "分隔符")
    @MobileDo.MobileField(key = 0x9716)
    private String delimiter;

    public DztgHighlightsModule addAttr(CommonAttrVO attr) {
        if (attrs == null) {
            attrs = new ArrayList<>();
        }
        attrs.add(attr);
        return this;
    }

    public static DztgHighlightsModule newSimple() {
        DztgHighlightsModule highlightsModule = new DztgHighlightsModule();
        highlightsModule.setStyle("simple");
        return highlightsModule;
    }

    public static DztgHighlightsModule newStruct() {
        DztgHighlightsModule highlightsModule = new DztgHighlightsModule();
        highlightsModule.setStyle("struct");
        return highlightsModule;
    }

}
