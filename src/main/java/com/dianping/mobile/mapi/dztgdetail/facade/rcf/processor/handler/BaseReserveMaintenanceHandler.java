package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.handler;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/11/7 10:38
 */
public interface BaseReserveMaintenanceHandler {

    int getDealSecondCategory();

    String getExpName(boolean isMt);

    List<String> getAbKeys();

    default boolean isEnable(DealCtx ctx) {
        // 双端APP
        return ctx.getEnvCtx().judgeMainApp();
    }

    default List<String> getSpecialAbKeys() {
        return Lists.newArrayList();
    }

    default String getExpName(DealCtx ctx) {
        return ctx == null ? null : getExpName(ctx.isMt());
    }

    default boolean isHitAbTest(AbConfig abConfig) {
        if ( Objects.isNull(abConfig)) {
            return false;
        }
        return "c".equals(abConfig.getExpResult());
    }
}
