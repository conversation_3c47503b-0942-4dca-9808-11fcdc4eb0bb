package com.dianping.mobile.mapi.dztgdetail.action.app;


import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.deal.DzDealBaseFTConfiguration;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.deal.executor.DzDealBaseExecutor;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.req.DealBaseContextRequest;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.utils.FaultToleranceUtils;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.HttpMethod;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.sankuai.athena.stability.faulttolerance.FaultToleranceEngine;
import com.sankuai.athena.stability.faulttolerance.FaultToleranceExecutionEngine;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@InterfaceDoc(displayName = "到综团单展示信息APP查询接口",
        type = "restful",
        description = "查询到综团单展示信息：包括团单基础信息、购买栏、优惠、适用商户等等。",
        scenarios = "该接口仅适用于双平台APP站点的团购详情页，其他以到综团购为纬度的页面如需使用请咨询项目owner",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "qian.wang.sh"
)
@Controller("general/platform/dztgdetail/dzdealbase.bin")
@Action(url = "dzdealbase.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class DzDealBaseAction extends AbsAction<DealBaseReq> {

    @Resource
    private DzDealBaseFTConfiguration dzDealBaseFTConfiguration;

    @Resource
    private DzDealBaseExecutor dzDealBaseExecutor;

    private FaultToleranceEngine faultToleranceEngine = new FaultToleranceExecutionEngine();


    @Override
    protected IMobileResponse validate(DealBaseReq request, IMobileContext iMobileContext) {
        IdUpgradeUtils.processProductIdForDealBaseReq(request, "dzdealbase.bin");
        if (request == null || request.getDealgroupid() == null || request.getDealgroupid() <= 0) {
            Cat.logEvent("NullPointer", "request.getDealgroupid() is null or <= 0");
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "dzdealbase.bin",
            displayName = "到综团单展示信息查询接口",
            description = "查询到综团单展示信息：包括团单基础信息、购买栏、优惠、适用商户等等。\n如果团单是侵权团单，则后端不返回数据。",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "dzdealbase.bin请求参数",
                            type = DealBaseReq.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "result", description = "团购picasso数据", type = DealGroupPBO.class)},
            restExampleUrl = "http://mapi.51ping.com/general/platform/dztgdetail/dzdealbase.bin?" +
                    "dealgroupid=200139713&userlng=121.3756670738241&userlat=31.21784036756436&cityid=1",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    ),
            }
    )
    @Override
    @CryptMethod
    protected IMobileResponse execute(DealBaseReq request, IMobileContext iMobileContext) {
        EnvCtx envCtx = initEnvCtxV2(iMobileContext);
        DealBaseContextRequest contextRequest = DealBaseContextRequest.build(request, iMobileContext, envCtx);
        String faultToleranceExpression = Lion.getString(LionConstants.APP_KEY, LionConstants.DZ_DEAL_BASE_FAULT_TOLERANCE_EXPRESSION, "");
        // 1. 是否走容错数据兜底，避免影响线上场景，可以自由决策
        if (FaultToleranceUtils.hit(request, faultToleranceExpression)) {
            // 接入容错兜底组件
            CommonMobileResponse response = faultToleranceEngine.execute(contextRequest, dzDealBaseFTConfiguration);
            if (Objects.isNull(response) || Objects.isNull(response.getStatusCode())) {
                return Resps.SYSTEM_ERROR;
            }
            // 处理状态码
            int code = response.getStatusCode().getCode();
            response.setStatusCode(FaultToleranceUtils.getStatusCode(code));
            return response;
        }
        // 2. 否则走无降级的逻辑
        return dzDealBaseExecutor.getExecuteResult(request, iMobileContext, envCtx, false);
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
