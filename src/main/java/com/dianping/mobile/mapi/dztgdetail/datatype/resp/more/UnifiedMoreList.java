package com.dianping.mobile.mapi.dztgdetail.datatype.resp.more;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/12/13.
 */
@TypeDoc(description = "更多模块数据列表")
@MobileDo(id = 0xc2dd)
@Data
public class UnifiedMoreList implements Serializable {

    @FieldDoc(description = "模块标题")
    @MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "分店名")
    @MobileField(key = 0x1328)
    private String branchShopName;

    @FieldDoc(description = "本店更多")
    @MobileField(key = 0x1351)
    private UnifiedMoreItemList sameMoreItemList;

    @FieldDoc(description = "分店更多")
    @MobileField(key = 0x66f9)
    private UnifiedMoreItemList otherMoreItemList;
}
