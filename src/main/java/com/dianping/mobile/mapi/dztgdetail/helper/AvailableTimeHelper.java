package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.availabletime.AvailableTimeStrategy;
import com.dianping.mobile.mapi.dztgdetail.availabletime.AvailableTimeStrategyFactory;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.util.ApplicationContextGetBeanHelper;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.AvailableDateDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DisableDateDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 帮助构造可用时间
 * <p>
 * 洗浴这边不会出现每周可用和每周不可用同时存在的情况
 * 每周可用只会和节假日一起出现
 * 这时展示“部分时间”
 * 每周不可用和节假日同时出现或是可用时间不连续也展示“部分时间”
 * 只有单独出现连续的每周可用时间或连续的每周不可用时间才展示周几至周几或周几
 */
@Slf4j
public class AvailableTimeHelper {
    public static final String PARTIAL_DATE = "部分日期";
    public static final String ALL_DAY = "全天";
    public static final String PARTIAL_TIME = "部分时段";
    public static final Map<Integer, String> WEEKDAY_MAP = new HashMap<>();

    /**
     * 匹配字符串中的时间段
     * 示例：“周一至周四 19:00-21:30” 匹配 “19:00-21:30”
     */
    public static final Pattern TIME_OF_DAY_PATTERN = Pattern.compile("(\\d{2}:\\d{2}-\\d{2}:\\d{2})");

    static {
        WEEKDAY_MAP.put(1, "周一");
        WEEKDAY_MAP.put(2, "周二");
        WEEKDAY_MAP.put(3, "周三");
        WEEKDAY_MAP.put(4, "周四");
        WEEKDAY_MAP.put(5, "周五");
        WEEKDAY_MAP.put(6, "周六");
        WEEKDAY_MAP.put(7, "周日");
    }

    public static String getAvailableTime(DealCtx dealCtx) {
        try {
            String dateDoc = getDateDoc(dealCtx);
            String timeOfDayDoc = getTimeOfDayDoc(dealCtx);
            if (!StringUtils.isEmpty(dateDoc) || !StringUtils.isEmpty(timeOfDayDoc)) {
                return dateDoc + timeOfDayDoc + "可用";
            }
        } catch (Exception e) {
            log.error("getReminderSummary error, ", e);
        }
        return "";
    }

    /**
     * 获取使用日期文案
     * <p>
     * 规则：
     * 排除周X，若剩余可用日期一周内仅1天周Y，则展示“周Y”
     * 排除周X，若剩余可用日期一周内有多日并且可用日期从周Y1至周Y2全部连续，则展示“周Y1至周Y2”「可用日期全部连续表示在一周内正向全部连续，
     * 即{周二、周三、周四}属于全部连续，而{周一、周三、周四}或{周六、周日、周一}均不属于全部连续」
     * 若非上述情况则展示“部分时间”
     *
     * @param dealCtx
     */
    private static String getDateDoc(DealCtx dealCtx) {
        List<Integer> disableDays = getDisableDays(dealCtx);
        List<Integer> availableDays = getAvailableDays(dealCtx);
        //针对配置了可用时间的品类，使用可用时间来生成文案
        if (Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb", "com.sankuai.dzu.tpbase.dztgdetailweb.useAvailableDateCategoryList", Integer.class, new ArrayList<>())
                .contains(dealCtx.getCategoryId())) {
            if (checkAvailableDIY(dealCtx)) {
                return PARTIAL_DATE;
            }
            return getAvailableDateDoc(availableDays, disableDays);
        }
        //其他情况使用不可用时间来生成文案
        if (checkDisableDIY(dealCtx)) {
            return PARTIAL_DATE;
        }
        return getDisableDateDoc(disableDays);
    }

    /**
     * 判断是否有自定义的可用时间，有则返回部分时间
     */
    private static boolean checkAvailableDIY(DealCtx ctx) {
        return Optional.of(ctx)
                .map(DealCtx::getDealGroupDTO)
                .map(DealGroupDTO::getRule)
                .map(DealGroupRuleDTO::getUseRule)
                .map(DealGroupUseRuleDTO::getAvailableDate)
                .map(AvailableDateDTO::getSpecifiedDurationDateList)
                .isPresent();
    }


    /**
     * 判断是否有自定义的不可用时间，有则返回部分时间
     */
    private static boolean checkDisableDIY(DealCtx ctx) {
        return Optional.of(ctx)
                .map(DealCtx::getDealGroupDTO)
                .map(DealGroupDTO::getRule)
                .map(DealGroupRuleDTO::getUseRule)
                .map(DealGroupUseRuleDTO::getDisableDate)
                .map(DisableDateDTO::getDisableDateRangeDTOS)
                .isPresent();
    }


    private static List<Integer> getAvailableDays(DealCtx ctx) {
        return Optional.of(ctx)
                .map(DealCtx::getDealGroupDTO)
                .map(DealGroupDTO::getRule)
                .map(DealGroupRuleDTO::getUseRule)
                .map(DealGroupUseRuleDTO::getAvailableDate)
                .map(AvailableDateDTO::getCycleAvailableDateList)
                .orElse(new ArrayList<>())
                .stream()
                .flatMap(date -> (date.getAvailableDays() != null ? date.getAvailableDays().stream() : Stream.empty()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private static List<Integer> getDisableDays(DealCtx ctx) {
        return Optional.of(ctx)
                .map(DealCtx::getDealGroupDTO)
                .map(DealGroupDTO::getRule)
                .map(DealGroupRuleDTO::getUseRule)
                .map(DealGroupUseRuleDTO::getDisableDate)
                .map(DisableDateDTO::getDisableDays)
                .orElse(new ArrayList<>());
    }

    private static String getAvailableDateDoc(List<Integer> availableDays, List<Integer> disableDays) {
        if (CollectionUtils.isNotEmpty(disableDays)) {
            return PARTIAL_DATE;
        }
        if (isCycleDate(availableDays)) {
            return getCycleDateAvailableDoc(availableDays);
        }
        return PARTIAL_DATE;
    }

    private static String getDisableDateDoc(List<Integer> disableDays) {
        if (isCycleDate(disableDays)) {
            return getCycleDateAvailableDoc(getAvailableDaysByDisableDays(disableDays));
        }
        return PARTIAL_DATE;
    }


    private static boolean isCycleDate(List<Integer> list) {
        for (Integer i : list) {
            if (i > 7) {
                return false;
            }
        }
        return true;
    }

    private static String getCycleDateAvailableDoc(List<Integer> list) {
        //若可用时间是一天，展示周x
        if (list.size() == 1) {
            return WEEKDAY_MAP.get(list.get(0));
        }
        //若可用时间非全部正向连续，展示部分时间
        list.sort(Comparator.comparingInt(a -> a));
        for (int i = 1; i < list.size(); i++) {
            if (list.get(i) != list.get(i - 1) + 1) {
                return PARTIAL_DATE;
            }
        }
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        //若可用时间全部正向连续，展示周x至周y
        return WEEKDAY_MAP.get(list.get(0)) + "至" + WEEKDAY_MAP.get(list.get(list.size() - 1));
    }

    private static List<Integer> getAvailableDaysByDisableDays(List<Integer> disableDays) {
        List<Integer> available = new ArrayList<>();
        for (int i = 1; i <= 7; i++) {
            if (!disableDays.contains(i)) {
                available.add(i);
            }
        }
        return available;
    }


    /**
     * 获取一天中的使用时间文案
     * <p>
     * 规则：
     * 空："全天"
     * 特殊使用时段：“部分时段”
     * hh:mm - hh:mm为单段时间或多段时间但相加=00:00-23:59，展示“全天”
     * hh:mm - hh:mm为单段时间或多段连续时间XX:XX-XX:XX（不是00:00 - 23:59），展示“XX:XX-XX:XX”
     * hh:mm - hh:mm为单段时间多段时间且不连续，展示“部分时段”
     *
     * @param dealCtx
     */
    private static String getTimeOfDayDoc(DealCtx dealCtx) {
        List<String> availableTimeOfDays = DealAttrHelper.getAttributeValues(dealCtx.getAttrs(), DealAttrKeys.AVAILABLE_TIME);
        if (CollectionUtils.isEmpty(availableTimeOfDays)) {
            if (LionConfigUtils.hitReminderInfoConfig(dealCtx.getCategoryId())) {
                return getCustomAvailableTimeOfDays(dealCtx);
            }
            return ALL_DAY;
        }
        if (availableTimeOfDays.contains("全天可用")) {
            return ALL_DAY;
        }
        List<String[]> startEndTimes = new ArrayList<>();
        for (String timeStr : availableTimeOfDays) {
            if (!checkTimeOfDayFormat(timeStr)) {
                return PARTIAL_TIME;
            }
            startEndTimes.add(timeStr.split("-"));
        }

        return getTimeDoc(getFinalStartEndTime(startEndTimes));

    }

    /**
     * 查不到可用时间区间则返回空
     * from和to对应为"00:00:00"和"23:59:59"则返回全天
     * 否则返回部分时段
     * @param dealCtx
     * @return
     */
    public static String getCustomAvailableTimeOfDays(DealCtx dealCtx) {
        try {
            AvailableTimeStrategyFactory availableTimeStrategyFactory = ApplicationContextGetBeanHelper
                    .getBean(AvailableTimeStrategyFactory.class);
            AvailableTimeStrategy strategy = availableTimeStrategyFactory.getStrategy(dealCtx);
            if (strategy == null) {
                return StringUtils.EMPTY;
            }
            return strategy.getAvailableTime(dealCtx);
        } catch (Exception e) {
            log.error("getCustomAvailableTimeOfDays error", e);
        }
        return StringUtils.EMPTY;
    }

    public static String getTimeOfDayFromStr(String timeStr) {
        if (StringUtils.isBlank(timeStr)){
            return null;
        }
        Matcher m = TIME_OF_DAY_PATTERN.matcher(timeStr);
        if (m.find()){
            return m.group(1);
        }
        return null;
    }

    public static boolean checkTimeOfDayFormat(String timeStr) {
        String pattern = "^([0-1]?[0-9]|2[0-3]):([0-5][0-9])-([0-1]?[0-9]|2[0-3]):([0-5][0-9])$";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(timeStr);
        return m.matches();
    }


    public static String[] getFinalStartEndTime(List<String[]> startEndTimes) {
        startEndTimes.sort(Comparator.comparing(a -> a[0]));
        String[] finalStartEndTime = new String[2];
        finalStartEndTime[0] = startEndTimes.get(0)[0];
        finalStartEndTime[1] = startEndTimes.get(0)[1];
        for (int i = 1; i < startEndTimes.size(); i++) {
            if (finalStartEndTime[1].compareTo(startEndTimes.get(i)[0]) < 0) {
                return new String[]{};
            }
            if (finalStartEndTime[1].compareTo(startEndTimes.get(i)[1]) < 0) {
                finalStartEndTime[1] = startEndTimes.get(i)[1];
            }
        }
        return finalStartEndTime;
    }

    public static String getTimeDoc(String[] finalStartEndTime) {
        if (finalStartEndTime.length == 0) {
            return PARTIAL_TIME;
        }
        if (Objects.equals(finalStartEndTime[0], "00:00") && Objects.equals(finalStartEndTime[1], "23:59")) {
            return ALL_DAY;
        }
        return finalStartEndTime[0] + "-" + finalStartEndTime[1];
    }

    public static List<Integer> getWeekDayInt(List<String> weekDay) {
        if (CollectionUtils.isEmpty(weekDay)) {
            return Collections.emptyList();
        }
        return weekDay.stream().filter(StringUtils::isNotBlank).map(String::trim).filter(str -> str.matches("\\d+"))
                .map(NumberUtils::toInt).filter(day -> day >= 1 && day <= 7).collect(Collectors.toList());
    }

    /**
     * 获取今天是周几
     * 
     * @return 返回1-7的数字，1代表周一，7代表周日
     */
    public static int getTodayWeekDay() {
        Calendar calendar = Calendar.getInstance();
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        // Calendar中周日是1，周一是2，需要转换一下
        return dayOfWeek == 1 ? 7 : dayOfWeek - 1;
    }

}
