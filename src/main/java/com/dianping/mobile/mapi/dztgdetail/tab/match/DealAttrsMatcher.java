package com.dianping.mobile.mapi.dztgdetail.tab.match;

import com.dianping.cat.Cat;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DealAttrsMatcher extends AbstractMatcher<String> implements DealAttrsBasedMatcher {

    @Override
    public List<String> dealAttrsMatched() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.tab.match.DealAttrsMatcher.dealAttrsMatched()");
        throw new UnsupportedOperationException("DealAttrsMatcher中不支持dealAttrsMatched()");
    }

}