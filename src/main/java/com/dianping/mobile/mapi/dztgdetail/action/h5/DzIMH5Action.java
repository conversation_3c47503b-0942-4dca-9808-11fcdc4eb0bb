package com.dianping.mobile.mapi.dztgdetail.action.h5;

import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealIMReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupImVo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.facade.DealQueryFacade;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

@InterfaceDoc(displayName = "到综团单IM信息H5查询接口",
        type = "restful",
        description = "查询到综团单ImUrl",
        scenarios = "",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "wangziyun04"
)
@Controller("general/platform/dztgdetail/dealim.json")
@Action(url = "dealim.json", protocol = ReqProtocol.REST)
public class DzIMH5Action extends AbsAction<DealIMReq> {

    @Autowired
    private DealQueryFacade dealQueryFacade;

    @Override
    protected IMobileResponse validate(DealIMReq request, IMobileContext context) {
        IdUpgradeUtils.processProductIdForDealIMReq(request, "dealim.json");
        if (request == null) {
            return Resps.PARAM_ERROR;
        }

        if (request.getDealgroupid() == null || request.getDealgroupid() <= 0) {
            return Resps.PARAM_ERROR;
        }

        if (request.getShopIdLong() <= 0L && StringUtils.isEmpty(request.getShopidstrEncrypt())
                && StringUtils.isEmpty(request.getShopidEncrypt())) {
            return Resps.PARAM_ERROR;
        }

        if (request.getClienttype() == null || request.getClienttype() <= 0) {
            return Resps.PARAM_ERROR;
        }

        return null;
    }

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "dealim.json",
            displayName = "到综团单im接口",
            description = "查询到综团单im信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "dealim.json请求参数",
                            type = DealBaseReq.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "result", description = "团购picasso数据", type = DealGroupPBO.class)},
            restExampleUrl = "http://mapi.51ping.com/general/platform/dztgdetail/dealim.json?" +
                    "dealGroupId=200139713&shopId=26970499",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    ),
            }
    )
    @Override
    @CryptMethod
    protected IMobileResponse execute(DealIMReq request, IMobileContext context) {
        EnvCtx envCtx = initEnvCtxFromH5(context, false);
        try {
            DealGroupImVo result = dealQueryFacade.queryDealGroupIM(request, envCtx);
            if (result != null) {
                return new CommonMobileResponse(result);
            }
        } catch (Exception e) {
            logger.error("dealim.json error", e);
        }

        return Resps.SYSTEM_ERROR;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
