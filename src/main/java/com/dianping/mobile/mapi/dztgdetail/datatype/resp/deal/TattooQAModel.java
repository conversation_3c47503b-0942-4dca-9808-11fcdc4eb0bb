package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;


import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

@Data
@TypeDoc(description = "纹绣问答信息模块")
@MobileDo(id = 0xb755)
public class TattooQAModel implements Serializable {

    @FieldDoc(description = "提问")
    @MobileDo.MobileField(key = 0x8164)
    private String question;

    @FieldDoc(description = "回答")
    @MobileDo.MobileField(key = 0x9474)
    private String answer;

    @FieldDoc(description = "icon链接")
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;
}
