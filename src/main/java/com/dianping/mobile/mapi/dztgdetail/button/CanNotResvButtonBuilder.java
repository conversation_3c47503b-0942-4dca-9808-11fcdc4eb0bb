package com.dianping.mobile.mapi.dztgdetail.button;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;

public class CanNotResvButtonBuilder extends AbstractButtonBuilder{
    @Override
    protected void doBuild(DealCtx context, ButtonBuilderChain chain) {
        DealBuyBtn canNotResvButton = DealBuyHelper.getCanNotResvButton(context);
        if (canNotResvButton != null && !context.getIsCanResv()) {
            context.addButton(canNotResvButton);
            chain.interrupt();
            return;
        }
        chain.build(context);
    }
}
