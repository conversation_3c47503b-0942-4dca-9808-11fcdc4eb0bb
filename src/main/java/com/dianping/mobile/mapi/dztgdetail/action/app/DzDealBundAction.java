package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.cat.Cat;
import com.dianping.deal.common.enums.GpsType;
import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.deal.shop.dto.BestShopReq;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PriceDisplayWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.RecommendServiceWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.BuyMoreSaveMoreCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.BuyMoreSaveMoreReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DzdealbundinfoReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.BuyMoreSaveMoreVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo.CouponDescItem;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo.DealBundBO;
import com.dianping.mobile.mapi.dztgdetail.entity.CombinationDealInfo;
import com.dianping.mobile.mapi.dztgdetail.facade.BuyMoreSaveMoreFacade;
import com.dianping.mobile.mapi.dztgdetail.helper.PromoHelper;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.CouponAssignStatusEnum;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PriceResponse;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.dianping.mobile.mapi.dztgdetail.util.AntiCrawlerUtils.antiUnauthenticLogin;

/**
 * 功能描述: 团详搭售接口
 *
 * <AUTHOR> lintaolei <p>
 * @version 2023/7/26
 * @since mapi-dztgdetail-web
 */
@Controller("general/platform/dztgdetail/dzdealbundinfo.bin")
@Action(url = "dzdealbundinfo.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class DzDealBundAction extends AbsAction<DzdealbundinfoReq> {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private RecommendServiceWrapper recommendServiceWrapper;

    @Autowired
    private PriceDisplayWrapper priceDisplayWrapper;

    @Autowired
    private BuyMoreSaveMoreFacade buyMoreSaveMoreFacade;

    @Autowired
    private DealGroupWrapper dealGroupWrapper;

    @Override
    protected IMobileResponse validate(DzdealbundinfoReq request, IMobileContext iMobileContext) {
        IdUpgradeUtils.processProductIdForDzdealbundinfoReq(request, "dzdealbundinfo.bin");
        if(request == null || request.getDealgroupid() <= 0) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @Override
    @CryptMethod
    protected IMobileResponse execute(DzdealbundinfoReq request, IMobileContext iMobileContext) {
        EnvCtx envCtx = initEnvCtx(iMobileContext);
        try {
            // 拦截没有授权的登录
            antiUnauthenticLogin(iMobileContext);
            List<CouponDescItem> result = Lists.newArrayList();
            DealBundBO dealBundBO = new DealBundBO();

            if(request.getPoiid() == null || request.getPoiid() < 0) {
                BestShopDTO bestShopDTO = Optional
                        .ofNullable((BestShopDTO)dealGroupWrapper.getFutureResult(getBestShopFuture(request, envCtx)))
                        .orElse(new BestShopDTO());
                request.setPoiid(envCtx.isMt()?bestShopDTO.getMtShopId():bestShopDTO.getDpShopId());
            }
            BuyMoreSaveMoreCtx ctx = buildBuyMoreRecommendReq(envCtx, request);
            List<RecommendDTO> recommendDTOS = recommendServiceWrapper.getRecommendCombineDeal(ctx);
            if(CollectionUtils.isNotEmpty(recommendDTOS)) {
                recommendDTOS = recommendDTOS.subList(0,1);
            } else {
                return new CommonMobileResponse(Resps.NoDataResp);
            }
            List<CombinationDealInfo> combinationDealInfos = buyMoreSaveMoreFacade.convertToCombinationDealInfo(recommendDTOS);
            if(CollectionUtils.isEmpty(combinationDealInfos)) {
                return new CommonMobileResponse(Resps.NoDataResp);
            }
            Future<PriceResponse<Map<Long, List<PriceDisplayDTO>>>> priceResponseFuture = priceDisplayWrapper.getBuyMoreSaveMorePriceInfo(request.getCityid(), envCtx, request.getPoiid(), combinationDealInfos, ctx.getReq());
            Map<Integer, PriceDisplayDTO> dealIdPriceDisplayMap = priceDisplayWrapper.getProductMap(priceResponseFuture);
            //主品优惠信息
            PriceDisplayDTO mainPriceDisplayDTOS = dealIdPriceDisplayMap.get(combinationDealInfos.get(0).getMainDealId());
            //搭售品优惠信息
            PriceDisplayDTO bundPriceDisplayDTOS = dealIdPriceDisplayMap.get(combinationDealInfos.get(0).getBindingDealId());
            ArrayList<PriceDisplayDTO> priceDisplayDTOS = Lists.newArrayList(mainPriceDisplayDTOS,bundPriceDisplayDTOS);
            result = priceDisplayDTOS.stream().map(dto -> {
                List<PromoDTO> couponPromos = dto.getUsedPromos();
                if(CollectionUtils.isEmpty(couponPromos)) {
                    return null;
                }

                return buildCouponItem(couponPromos, dto.getIdentity().getProductId());
            }).filter(Objects::nonNull).flatMap(List::stream).collect(Collectors.toList());

            dealBundBO.setCoupon(result);
            return new CommonMobileResponse(dealBundBO);
        } catch (Exception e) {
            logger.error("dzdealbundinfo.bin error", e);
        }

        return Resps.SYSTEM_ERROR;
    }



    private List<CouponDescItem> buildCouponItem(List<PromoDTO> couponPromos, int productId) {
        List<CouponDescItem> coupon = Lists.newArrayList();
        for (PromoDTO couponPromo : couponPromos){
            if(couponCanAssign(couponPromo) || govermentConsumeCouponCanAssign(couponPromo)) {
                CouponDescItem item = new CouponDescItem();
                item.setCouponGroupId((int) couponPromo.getIdentity().getPromoId());
                item.setUnifiedcoupongroupids(String.valueOf(couponPromo.getIdentity().getPromoId()));
                item.setDealGroupId(String.valueOf(productId));
                coupon.add(item);
            }
        }
        return coupon;
    }

    private boolean couponCanAssign(PromoDTO couponPromo) {
        return couponPromo.getIdentity().getPromoType() == PromoTypeEnum.COUPON.getType() && PromoHelper.canAssign(couponPromo);
    }

    private boolean govermentConsumeCouponCanAssign(PromoDTO couponPromo) {
        return couponPromo.getIdentity().getPromoType() == PromoTypeEnum.GOVERNMENT_CONSUME_COUPON.getType() && couponPromo.getCouponAssignStatus() != CouponAssignStatusEnum.ASSIGNED.getCode();
    }


    private BuyMoreSaveMoreCtx buildBuyMoreRecommendReq(EnvCtx envCtx, DzdealbundinfoReq request) {
        BuyMoreSaveMoreCtx ctx = new BuyMoreSaveMoreCtx();
        BuyMoreSaveMoreVO result = new BuyMoreSaveMoreVO();
        ctx.setResult(result);

        BuyMoreSaveMoreReq req = new BuyMoreSaveMoreReq();
        req.setDealGroupId(request.getDealgroupid());
        req.setSourceType(1);
        req.setPoiidStr(request.getPoiid() != null ? request.getPoiid().toString() : null);
        req.setShopUuid(request.getShopuuid() != null ? request.getShopuuid() : null);
        req.setCityId(request.getCityid());
        req.setUserLng(request.getUserlng());
        req.setUserLat(request.getUserlat());
        req.setStart(0);
        req.setLimit(20);
        ctx.setEnvCtx(envCtx);
        ctx.setReq(req);

        return ctx;
    }



    private Future getBestShopFuture(DzdealbundinfoReq request, EnvCtx envCtx) {
        Future shopFuture = dealGroupWrapper.preDealGroupBestShop(buildBestShopReq(request, envCtx));
        return shopFuture;
    }


    private BestShopReq buildBestShopReq(DzdealbundinfoReq request, EnvCtx envCtx) {
        BestShopReq shopReq = buildBestShopReqWithoutShopId(request, envCtx);
        if(request.getPoiid() != null) {
            shopReq.setShopId(request.getPoiid());
        }
        return shopReq;
    }

    private BestShopReq buildBestShopReqWithoutShopId(DzdealbundinfoReq request, EnvCtx envCtx) {
        BestShopReq shopReq = new BestShopReq();

        shopReq.setDealGroupId(request.getDealgroupid());
        shopReq.setCityId(request.getCityid());
        shopReq.setLat(Optional.ofNullable(request.getUserlat()).orElse(0.0d));
        shopReq.setLng(Optional.ofNullable(request.getUserlng()).orElse(0.0d));
        shopReq.setGpsType(GpsType.GCJ02.getType());
        shopReq.setClientType(envCtx.getClientType());
        return shopReq;
    }


    private int getProductId(PriceDisplayDTO priceDisplayDTO) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.action.app.DzDealBundAction.getProductId(com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO)");
        return priceDisplayDTO.getIdentity().getProductId();
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }


}
