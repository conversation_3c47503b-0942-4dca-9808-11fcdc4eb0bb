package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.biz.DealBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.DealCampaignBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.RecommendWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Cons;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealFields;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.CollaborativeRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.MtCollaborativeResponse;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.MtDealId2Stid;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.MtDealModel;
import com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam;
import com.dianping.mobile.mapi.dztgdetail.util.StidUtils;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.meituan.service.mobile.message.recommend.CollaborativeResponseMessage;
import com.meituan.service.mobile.message.recommend.DealStid;
import com.meituan.service.mobile.message.recommend.RecommendDealMessage;
import com.meituan.service.mobile.prometheus.client.exception.CException;
import com.meituan.service.mobile.prometheus.model.DealModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 推荐相关业务
 * Created by guozhengyao on 16/3/29.
 */
@Component
@Slf4j
public class RecommendFacade {

    @Resource
    private DealBiz dealBiz;
    @Resource
    private DealCampaignBiz dealCampaignBiz2;
    @Resource
    private RecommendWrapper recommendWrapper;

    public MtCollaborativeResponse getCollaborativeDealGroup(CollaborativeRequest request, IMobileContext context) throws Exception {
        MtCollaborativeResponse response = new MtCollaborativeResponse();
        try {
            MtCommonParam mtCommonParam = new MtCommonParam(context);
            CollaborativeResponseMessage collaborativeResp = recommendWrapper.getCollaborativeRecommend(request, mtCommonParam);
            if (collaborativeResp == null) {
                return null;
            }
            List<Integer> dealIds = Lists.newArrayList();
            if (collaborativeResp.getDeals() != null && collaborativeResp.getDeals().size() != 0) {
                dealIds = Lists.transform(collaborativeResp.getDeals(), new RecommendDealMessageToDealIdTransformer());
            }
            List<String> fields = mtCommonParam.getFieldsList();
            if (CollectionUtils.isEmpty(fields)) {
                fields.addAll(DealFields.LIST_FIELDS_SET);
            }
            List<DealModel> deals = dealBiz.getDealListByDids(dealIds, fields);
            List<MtDealModel> mtDealModels = dealBiz.getDealJsonByModel(deals, fields, mtCommonParam);
            if (request.getScene() == 2) {
                addBrandName2Title(mtDealModels);
            }
            dealCampaignBiz2.bindingCampaign2DealsV2(mtDealModels, mtCommonParam);
            //添加stids
            String ste = mtCommonParam.getSte();
            List<MtDealId2Stid> mtDealId2Stids = convertDealStidList(collaborativeResp.getStids());
            StidUtils.addSuffix2DidStidList(mtDealId2Stids, "", ste);
            assembleDealWithStid(mtDealModels, mtDealId2Stids);
            response.setTitle(collaborativeResp.getTitle());
            if (mtDealModels != null && mtDealModels.size() >= 1) {
                response.setStrategy(collaborativeResp.getDeals().get(0).getStrategy());
            }
            response.setDeals(mtDealModels);
            response.setStid(StidUtils.addSuffix2Stid(collaborativeResp.getStid(), "", ste));
            //打日志，包括stid和stids
            doStidStidsLog(response.getStid(), mtDealId2Stids, dealIds, mtCommonParam);
            return response;
        } catch (CException e) {
            log.error("getCollaborativeDealGroup() dealBiz.getDealListByDids error", e);
            throw e;
        } catch (Exception e) {
            log.error("getCollaborativeDealGroup() dealBiz.getDealJsonByModel error", e);
            throw e;
        }
    }

    private void addBrandName2Title(List<MtDealModel> mtDealModels) {
        if (mtDealModels == null || mtDealModels.isEmpty()) {
            return;
        }
        for (MtDealModel dealModel : mtDealModels) {
            String brandName = dealModel.getBrandName();
            String title = dealModel.getTitle();
            if (StringUtils.isEmpty(title) || StringUtils.isEmpty(brandName)) {
                continue;
            }
            try {
                String brandNameTitle = generateBrandNameTitle(brandName, title);
                dealModel.setTitle(brandNameTitle);
            } catch (Exception t) {
                log.error("fail to cover brandName " + brandName + " title" + title, t);
            }
        }
    }

    private String generateBrandNameTitle(String brandName, String title) {
        try {
            String trimmedBrandName = brandName.trim();
            String trimmedTitle = title.trim();
            if (StringUtils.isEmpty(brandName)) {
                return trimmedTitle;
            }
            if (StringUtils.isEmpty(trimmedTitle)) {
                return trimmedBrandName + ":" + trimmedTitle;
            }
            String expression = "^" + trimmedBrandName + "\\s*[\uFF1A:]";
            String replacedTitle = trimmedTitle.replaceFirst(expression, "");
            return trimmedBrandName + ":" + replacedTitle;
        } catch (Exception e) {
            log.error("generateBrandNameTitle error. brandName: {}, title: {}", brandName, title, e);
            log.error(e.getMessage());
        }
        return Strings.EMPTY;
    }

    private List<MtDealId2Stid> convertDealStidList(List<DealStid> dealStidList) {
        if (CollectionUtils.isEmpty(dealStidList)) {
            return Collections.emptyList();
        }
        List<MtDealId2Stid> list = new ArrayList<>();
        for (DealStid each : dealStidList) {
            MtDealId2Stid didStid = new MtDealId2Stid();
            didStid.setDealid(each.getDealid());
            didStid.setStid(each.getStid());
            list.add(didStid);
        }
        return list;
    }

    private void assembleDealWithStid(List<MtDealModel> dealModels, List<MtDealId2Stid> stids) {
        for (MtDealModel dealModel : dealModels) {
            for (MtDealId2Stid stid : stids) {
                if (stid.getDealid() == dealModel.getId()) {
                    dealModel.setStid(stid.getStid());
                    break;
                }
            }
        }
    }

    private class RecommendDealMessageToDealIdTransformer implements Function<RecommendDealMessage, Integer> {
        @Override
        public Integer apply(RecommendDealMessage input) {
            return input.getDeal().getId();
        }
    }

    private void doStidStidsLog(String stid, List<MtDealId2Stid> stids, List<Integer> dealIds, MtCommonParam commonParam, String... args) {
        JSONArray jsonArray = getStidsAsJsonArray(stids);
        String stidStr = (jsonArray == null ? "" : jsonArray.toString());
        doStidStidsLog(stid, stidStr, "[]", dealIds, commonParam, args);
    }

    private void doStidStidsLog(String stid, String stids, String topicStids, List<Integer> dealIds, MtCommonParam commonParam, String... args) {
        StringBuilder sb = new StringBuilder();
        appendStidLog(sb, stid, dealIds);
        //增加stids
        sb.append(" ").append("stids=").append(stids);
        //add topic stids
        sb.append(" ").append("topicstids=").append(topicStids);
        appendUTMLog(sb, commonParam, args);
        log.info(sb.toString());
    }

    private void appendStidLog(StringBuilder sb, String stid, List<Integer> dealIds) {
        String seperator = " ";
        sb.append("stid=").append(stid);
        if (CollectionUtils.isNotEmpty(dealIds)) {
            sb.append(seperator).append("returncount=").append(dealIds.size());
            sb.append(seperator).append("returnlist=").append(dealIds);
        } else {
            sb.append(seperator).append("returncount=0");
            sb.append(seperator).append("returnlist=[]");
        }
    }

    private void appendUTMLog(StringBuilder sb, MtCommonParam commonParam, String... args) {
        String seperator = " ";
        for (int i = 0; i < args.length; i += 2) {
            sb.append(seperator).append(args[i]).append("=").append(args[i + 1]);
        }
        String value;
        value = commonParam.getCityIdStr();
        append(sb, Cons.HTTPPARAM_CI, value, seperator);
        value = commonParam.getUtmMedium();
        append(sb, Cons.HTTPPARAM_UTM_MEDIUM, value, seperator);
        value = commonParam.getUtmTerm();
        append(sb, Cons.HTTPPARAM_UTM_TERM, value, seperator);
        value = commonParam.getUtmContent();
        append(sb, Cons.HTTPPARAM_UTM_COTENT, value, seperator);
        value = commonParam.getUtmCampaign();
        append(sb, Cons.HTTPPARAM_UTM_CAMPAIGN, value, seperator);
        value = commonParam.getUuid();
        append(sb, Cons.HTTPPARAM_UUID, value, seperator);
        sb.append(seperator).append("tm=").append(System.currentTimeMillis() / 1000);
    }

    private void append(StringBuilder sb, String key, String value, String separator) {
        if (StringUtils.isNotBlank(value)) {
            sb.append(separator).append(key).append("=").append(value);
        }
    }

    /**
     * 按list的顺序插入
     */
    private JSONArray getStidsAsJsonArray(List<MtDealId2Stid> stids) {
        if (CollectionUtils.isEmpty(stids)) {
            return null;
        }
        JSONArray ja = new JSONArray();
        try {
            for (MtDealId2Stid didStid : stids) {
                JSONObject jos = new JSONObject();
                jos.put("dealid", didStid.getDealid());
                jos.put("stid", didStid.getStid());
                ja.put(jos);
            }
        } catch (Exception e) {
            log.error("getStidsAsJsonArray failed", e);
        }
        return ja;
    }
}
