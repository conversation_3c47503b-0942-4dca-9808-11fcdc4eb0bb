package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.basic;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PromoWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.SkuWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MiniProgramSceneEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.PromoHelper;
import com.dianping.mobile.mapi.dztgdetail.util.RedisClientUtils;
import com.dianping.pay.promo.common.enums.ProductType;
import com.dianping.pay.promo.display.api.dto.Product;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.pay.promo.display.api.dto.QueryPromoDisplayRequest;
import com.dianping.tpfun.product.api.sku.common.enums.FunChannel;
import com.dianping.tpfun.product.api.sku.common.enums.FunClientType;
import com.dianping.tpfun.product.api.sku.common.enums.FunPlatform;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.dianping.tpfun.product.api.sku.pintuan.request.GetPinProductBriefReq;
import com.dianping.vc.sdk.concurrent.threadpool.ExecutorServices;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.cache2.CacheClient;
import com.sankuai.athena.inf.cache2.CacheClientConfig;
import com.sankuai.athena.inf.cache2.CacheKey;
import com.sankuai.athena.inf.cache2.loader.DataLoader;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Iterator;
import java.util.Map;
import java.util.Optional;
import java.util.Random;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

/**
 * Created by zuomlin on 2018/12/17.
 */
public class PricePinPoolProcessor extends AbsDealProcessor {

    private static final int PIN_POOL_PROMO_TEMPLATE_ID = 226;

    @Autowired
    private SkuWrapper skuWrapper;

    @Autowired
    private PromoWrapper promoWrapper;

    private static final String REDIS_DEAL_PINPOOL_PRODUCT_ID = "pinpool_product_id";

    private static final int CACHE_EXPIRE_TIME = 1296000;

    private static final int CACHE_REFRESH_TIME = 0;

    private static TypeReference<Map<Integer, Integer>> pinPoolCacheTypeReference = new TypeReference<Map<Integer, Integer>>() {};

    private static CacheClient cacheClient = RedisClientUtils.getRedisCacheClient();

    private static final String MAPPER_CACHE_KEY = "mapperCacheError";

    @Override
    public boolean isEnable(DealCtx ctx) {
        boolean isMainApp = ctx.getEnvCtx().isMainApp() && !ctx.isExternal();
        boolean isExternalAndEnabled = ctx.getEnvCtx().isExternalAndEnabled(MiniProgramSceneEnum.PINTUAN.getPromoScene());
        return isMainApp || isExternalAndEnabled;
    }

    @Override
    public void prepare(DealCtx ctx) {
        Map<Integer, Integer> dealGroupId2PinProductIdMap;
            if(Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.pin.pool.switch",false)) {
                dealGroupId2PinProductIdMap = fetchPinProductId(ctx.getDpId());
            } else {
                dealGroupId2PinProductIdMap = skuWrapper.getPinProductIdByDealGroupIds(Lists.newArrayList(ctx.getDpId()));
            }

            if (MapUtils.isEmpty(dealGroupId2PinProductIdMap)) {
                return;
            }
            GetPinProductBriefReq request = new GetPinProductBriefReq();
            request.setPinProductIds(Lists.newArrayList(dealGroupId2PinProductIdMap.values()));
            if (ctx.isMt()) {
                request.setCityId(ctx.getMtCityId());
                request.setFunChannel(FunChannel.MT.code);
                request.setUserId(ctx.getEnvCtx().getMtUserId());//已确认判断平台后再使用
            } else {
                request.setFunChannel(FunChannel.DP.code);
                request.setCityId(ctx.getDpCityId());
                request.setUserId(ctx.getEnvCtx().getDpUserId());
            }
            if (ctx.getEnvCtx().isMainApp()) {
                request.setClientVersion(ctx.getEnvCtx().getVersion());
                request.setFunClientType(FunClientType.NATIVE.code);
            } else if (ctx.getEnvCtx().isMainWX()) {
                request.setFunClientType(FunClientType.MINIPROGRAM.code);
            } else {
                request.setFunClientType(FunClientType.M.code);
            }
            if (ctx.getEnvCtx().isAndroid()) {
                request.setFunPlatform(FunPlatform.ANDROID.code);
            } else {
                request.setFunPlatform(FunPlatform.IPHONE.code);
            }
            //分平台透传门店id, 获取拼团下单页url
            request.setLongShopId(ctx.getLongPoiId4PFromReq());
            ctx.getFutureCtx().setPinProductBriefFuture(skuWrapper.prepareBatchGetPinProductBrief(request));
    }

    private Map<Integer, Integer> fetchPinProductId(int dpId) {
        Map<Integer, Integer> pinProductMap;
        try {
            pinProductMap = Optional.ofNullable(fetchPinProductFromCache(dpId).get()).orElse(null);
        } catch (Exception e) {
            Cat.logError(e);
            Cat.logEvent(MAPPER_CACHE_KEY, "pinProductError");
            return skuWrapper.getPinProductIdByDealGroupIds(Lists.newArrayList(dpId));
        }
        return pinProductMap;
    }

    private CompletableFuture<Map<Integer, Integer>> fetchPinProductFromCache(int dpId) {
        CacheKey cacheKey = new CacheKey(REDIS_DEAL_PINPOOL_PRODUCT_ID, dpId);
        DataLoader<Map<Integer, Integer>> dataLoader = key -> {
            if(key == null) {
                return CompletableFuture.completedFuture(null);
            }
            return CompletableFuture.completedFuture(skuWrapper.getPinProductIdByDealGroupIds(Lists.newArrayList(dpId)));
        };
        return cacheClient.asyncGetReadThrough(cacheKey,pinPoolCacheTypeReference, dataLoader, CACHE_EXPIRE_TIME + new Random().nextInt(300) , CACHE_REFRESH_TIME);
    }

    @Override
    public void process(DealCtx ctx) {
        Map<Integer, PinProductBrief> pinProductId2BriefMap = skuWrapper.batchGetPinProductBrief(ctx.getFutureCtx().getPinProductBriefFuture());
        if (MapUtils.isEmpty(pinProductId2BriefMap) || CollectionUtils.isEmpty(pinProductId2BriefMap.values())) {
            return;
        }
        Iterator<PinProductBrief> iterator = pinProductId2BriefMap.values().iterator();
        if (iterator.hasNext()) {
            ctx.setPinProductBrief(iterator.next());
        }
        if (ctx.getPinProductBrief() != null && ctx.getPinProductBrief().getPrice() != null && ctx.getPinProductBrief().getPrice().doubleValue() > 0) {
            PromoDisplayDTO promoDisplayDTO = promoWrapper.getPromoDisplayDTO(buildTimesPromoReq(ctx.getPinProductBrief(), ctx));
            ctx.setPinPoolPromoDesc(PromoHelper.getPromoDisplayDTODesc(promoDisplayDTO));
            ctx.setPinPoolPromoAmount(PromoHelper.getPromoDisplayDTOAmount(promoDisplayDTO));
        }
    }

    private QueryPromoDisplayRequest buildTimesPromoReq(PinProductBrief pinProductBrief, DealCtx dealCtx) {
        Product product = new Product();
        product.setProductId(pinProductBrief.getItemId());
        product.setPrice(pinProductBrief.getPrice());

        QueryPromoDisplayRequest request = new QueryPromoDisplayRequest();
        request.setProduct(product);
        request.setClientVersion(dealCtx.getEnvCtx().getVersion());
        request.setTemplateID(PIN_POOL_PROMO_TEMPLATE_ID); //商户页模板
        request.setPlatform(dealCtx.getEnvCtx().toPayPlatformCode());
        if (dealCtx.isMt()) {
            request.setCityId(dealCtx.getMtCityId());
            request.setUserId(dealCtx.getEnvCtx().getMtUserId());//已确认判断平台后再使用
            request.setDpId(dealCtx.getEnvCtx().getUuid());
            request.setProductType(ProductType.mt_generalPinTuan.value);
        } else {
            request.setCityId(dealCtx.getDpCityId());
            request.setUserId(dealCtx.getEnvCtx().getDpUserId());
            request.setDpId(dealCtx.getEnvCtx().getDpId());
            request.setProductType(ProductType.generalPinTuan.value);
        }
        return request;
    }
}
