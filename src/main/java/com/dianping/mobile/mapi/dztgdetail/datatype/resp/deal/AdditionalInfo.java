package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: bianzhan
 * @CreateTime: 2023-09-22 15:50
 * @Description: 加项信息
 */
@TypeDoc(description = "加项信息")
@MobileDo(id = 0xbba1)
@Data
public class AdditionalInfo implements Serializable {
    @FieldDoc(description = "是否有加项")
    @MobileDo.MobileField(key = 0x74c7)
    private boolean additional;

    @FieldDoc(description = "加项跳转链接")
    @MobileDo.MobileField(key = 0x7e74)
    private String additionalJumpUrl;
}
