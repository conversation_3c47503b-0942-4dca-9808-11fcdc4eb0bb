package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ServiceGuarantee;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.util.List;
import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 16/11/2022
 * @time 11:43
 * 模型描述：服务保障内容详情模块的文本列表
 * 移动之家链接: https://mobile.sankuai.com/studio/model/info/29813
 */
@MobileDo(id = 0xfd11)
@Data
public class ContentText implements Serializable {

    /**
     * 服务保障内容详情模块的副文本
     */
    @MobileField(key = 0xbf8)
    private List<String> paraTexts;

    /**
     * 服务保障内容详情模块的文本内容
     */
    @MobileField(key = 0xcc2a)
    private String textContent;
}
