package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.healthExamination;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/9 9:14 下午
 */
@MobileDo(id = 0xeaf)
public class PrimaryExaminationItemVO implements Serializable {
    /**
     * 描述
     */
    @MobileDo.MobileField(key = 0xfebf)
    private String desc;

    /**
     * 一级检查项名称
     */
    @MobileDo.MobileField(key = 0x7ab8)
    private String name;

    /**
     * 二级检查项列表
     */
    @MobileDo.MobileField(key = 0xf834)
    private List<SecondaryExaminationItemVO> secondaryExaminationItemList;

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<SecondaryExaminationItemVO> getSecondaryExaminationItemList() {
        return secondaryExaminationItemList;
    }

    public void setSecondaryExaminationItemList(
            List<SecondaryExaminationItemVO> secondaryExaminationItemList) {
        this.secondaryExaminationItemList = secondaryExaminationItemList;
    }
}