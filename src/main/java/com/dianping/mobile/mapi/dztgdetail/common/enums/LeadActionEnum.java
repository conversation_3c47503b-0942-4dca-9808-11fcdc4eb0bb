package com.dianping.mobile.mapi.dztgdetail.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * 2020/3/16 2:51 下午
 */
@Getter
public enum LeadActionEnum {
    /**
     * 优惠引导类型
     */
    NOOP(0, "无文案，不跳转"),
    TOAST_DEFAULT(1, "弹出默认浮层"),
    REDIRECT_URL(2, "跳转URL"),
    TOAST_IDLE_HOURS(3, "弹出闲时单浮层");

    private int code;
    private String desc;

    LeadActionEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
