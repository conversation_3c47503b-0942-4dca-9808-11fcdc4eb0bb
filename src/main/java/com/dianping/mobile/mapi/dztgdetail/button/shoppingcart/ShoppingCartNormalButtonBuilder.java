package com.dianping.mobile.mapi.dztgdetail.button.shoppingcart;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.button.beauty.BeautyNormalButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.joy.AbstractPriceServiceButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.joy.CouponBarHelper;
import com.dianping.mobile.mapi.dztgdetail.button.joy.MarketPriceNormalButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.joy.NewNormalButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ShoppingCartStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PriceRuleModule;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import org.apache.commons.lang.StringUtils;

import java.math.RoundingMode;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/15
 */
public class ShoppingCartNormalButtonBuilder extends AbstractPriceServiceButtonBuilder {

    private MarketPriceNormalButtonBuilder marketPriceNormalButtonBuilder = new MarketPriceNormalButtonBuilder();
    private NewNormalButtonBuilder newNormalButtonBuilder = new NewNormalButtonBuilder();
    private BeautyNormalButtonBuilder beautyNormalButtonBuilder = new BeautyNormalButtonBuilder();

    @Override
    public PriceDisplayDTO getPrice(DealCtx context) {
        if (DealBuyHelper.xiYuShowMarketPrice(context) || DealBuyHelper.joyShowMarketPrice(context)) {
            return marketPriceNormalButtonBuilder.getPrice(context);
        } else if (DealBuyHelper.isShowZuLiaoMarketPrice(context) || DealBuyHelper.isJoy(context)) {
            //足疗看这里
            return newNormalButtonBuilder.getPrice(context);
        } else {
            return beautyNormalButtonBuilder.getPrice(context);
        }
    }

    @Override
    public void afterBuild(DealCtx context, DealBuyBtn button) {
        if (DealBuyHelper.xiYuShowMarketPrice(context) || DealBuyHelper.joyShowMarketPrice(context)) {
            marketPriceNormalButtonBuilder.afterBuild(context, button);
        } else if (DealBuyHelper.isShowZuLiaoMarketPrice(context) || DealBuyHelper.isJoy(context)) {
            newNormalButtonBuilder.afterBuild(context, button);
        } else {
            newNormalButtonBuilder.afterBuild(context, button);
        }

        //title其实在这里赋值，之前的都会被覆盖掉，重构的时候只保留这个
        button.setBtnTitle(getButtonTitle(context, "立即抢购"));
        if (DealBuyHelper.isCouponBar(context)) {
            button.setBtnTitle(CouponBarHelper.getCouponBtnTitle(context));
        }
        //购物车在这里赋值，之前的都会被覆盖掉，重构的时候只保留这个
        button.setAddShoppingCartStatus(ShoppingCartStatusEnum.ADD_SHOPPING_CART.code);
        if (context.isMultiSku()) {
            // 购物车浮层拼接逻辑 https://km.sankuai.com/collabpage/**********
            button.setShoppingCartRedirectUrl(generateCartFloatLayerUrl(context));
        }
        button.setPriceRuleModule(buildPriceRuleModule(context, button));

    }

    private String generateCartFloatLayerUrl(DealCtx context) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.button.shoppingcart.ShoppingCartNormalButtonBuilder.generateCartFloatLayerUrl(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        String schema = "%s?mrn_biz=gc&mrn_entry=gcsubmitordermrnmodules-unify&mrn_component=addtocartpage-popup&mrn_min_version=0.0.26&isTransparent=true&hideLoading=true&mrn_transparent=true&mrn_hideloading=true&mrn_hideNextNavBar=true&pagesource=dealGroupDetail&dealid=%s&shopid=%s";
        // 如果是穿戴甲，则不用拼上skuid
        if (DealUtils.isNewWearableNailDeal(context)) {
            return String.format(schema + "&need_exhibit_skuid=1", context.isMt() ? "imeituan://www.meituan.com/mrn" : "dianping://mrn", context.getDealId4P(), context.getLongPoiId4PFromReq());
        }
        // 优先使用前端传入的用户选择sku，兜底使用第一个sku
        String skuId = StringUtils.isNotBlank(context.getSkuId()) ? context.getSkuId() : context.getResult().getSkuId();
        return String.format(schema + "&skuid=%s", context.isMt() ? "imeituan://www.meituan.com/mrn" : "dianping://mrn", context.getDealId4P(), context.getLongPoiId4PFromReq(), skuId);
    }

    private PriceRuleModule buildPriceRuleModule(DealCtx context, DealBuyBtn button) {
        PriceRuleModule priceRuleModule = new PriceRuleModule();
        priceRuleModule.setPriceRuleType(BuyBtnTypeEnum.NORMAL_DEAL.getCode());
        priceRuleModule.setPriceRuleTags(buildPriceRuleTag(context, button));
        if (DealBuyHelper.isCouponBar(context)) {
            priceRuleModule.setPromoDesc(buildPromoDesc(context));
        }
        return priceRuleModule;
    }

    private String buildPromoDesc(DealCtx context) {
        PriceDisplayDTO price = getPrice(context);
        String savedMoney = price.getMarketPrice().subtract(price.getPrice())
                .setScale(1, RoundingMode.CEILING).stripTrailingZeros().toPlainString();

        return "共省￥" + savedMoney;
    }

    private List<String> buildPriceRuleTag(DealCtx context, DealBuyBtn button) {
        List<String> priceRuleTags = Lists.newArrayList();

        String priceRuleTitle = getTitle(context);
        String priceStr = button.getPriceStr();

        priceRuleTags.add(priceRuleTitle);
        priceRuleTags.add("￥" + priceStr);

        return priceRuleTags;
    }

    private String getTitle(DealCtx context) {
        List<DealBuyBtn> buyButtons = context.getBuyBar().getBuyBtns();

        boolean memberCardPresent = buyButtons.stream()
                .anyMatch(e -> e.getPriceRuleModule().getPriceRuleType() == BuyBtnTypeEnum.MEMBER_CARD.getCode());
        if (memberCardPresent) {
            return "团购价";
        }

        boolean timesCardPresent = buyButtons.stream()
                .anyMatch(e -> e.getPriceRuleModule().getPriceRuleType() == BuyBtnTypeEnum.TIMES_CARD.getCode());
        if (timesCardPresent) {
            return "单次";
        }

        boolean idlePresent = buyButtons.stream()
                .anyMatch(e -> e.getPriceRuleModule().getPriceRuleType() == BuyBtnTypeEnum.IDLE_DEAL.getCode());
        if (idlePresent) {
            return "团购价";
        }

        boolean pintuanPresent = buyButtons.stream()
                .anyMatch(e -> e.getPriceRuleModule().getPriceRuleType() == BuyBtnTypeEnum.PINTUAN.getCode());
        if (pintuanPresent) {
            return "单独购买";
        }

        return "团购价";
    }
}
