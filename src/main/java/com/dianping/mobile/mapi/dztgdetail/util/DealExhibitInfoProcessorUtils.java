package com.dianping.mobile.mapi.dztgdetail.util;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.lion.client.util.CollectionUtils;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

@Slf4j
public class DealExhibitInfoProcessorUtils {

    public static final String PHOTO_STYLE = "photo_style";


    /**
     * 获取图片风格
     *
     * @param attrs 属性列表
     * @return 图片风格列表
     */
    public static List<String> getPhotoStyle(List<AttributeDTO> attrs) {
        LinkedList<String> resultList = new LinkedList<>();
        if (attrs == null || attrs.isEmpty()) {
            return resultList;
        }
        try {
            List<String> serviceTypeAttr = AttributeUtils.getAttributeValues(PHOTO_STYLE, attrs);
            if (CollectionUtils.isEmpty(serviceTypeAttr)) {
                return resultList;
            }
            for (String item : serviceTypeAttr) {
                processItem(item, resultList);
            }
        } catch (Exception e) {

            log.error("DealExhibitInfoProcessorUtils.getPhotoStyle error: {} attrs: {}", e, JSON.toJSON(attrs));
        }
        return resultList;
    }

    /**
     * 处理单个属性项，将其添加到结果列表中
     *
     * @param item      待处理的属性项
     * @param resultList 结果列表
     */
    private static void processItem(String item, LinkedList<String> resultList) {
        // 检查 item 是否为 JSON 数组格式
        if (item.startsWith("[") && item.endsWith("]")) {
            item = item.substring(1, item.length() - 1);
            String[] values = splitValues(item);
            for (String value : values) {
                resultList.add(value.replace("\"", "").trim());
            }
        } else {
            resultList.add(item.trim());
        }
    }

    /**
     * 使用简单的字符串分割逻辑将长字符串拆分为数组
     *
     * @param item 待分割的字符串
     * @return 拆分后的字符串数组
     */
    private static String[] splitValues(String item) {
        List<String> values = new ArrayList<>();
        int start = 0;
        int end;

        while ((end = item.indexOf(",", start)) != -1) {
            values.add(item.substring(start, end).trim());
            start = end + 1;
        }
        // 添加最后一个元素
        if (start < item.length()) {
            values.add(item.substring(start).trim());
        }
        return values.toArray(new String[0]);
    }
}
