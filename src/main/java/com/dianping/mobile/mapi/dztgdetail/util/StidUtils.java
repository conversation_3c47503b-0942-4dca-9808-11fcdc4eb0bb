package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.MtDealId2Stid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/4/5.
 */
@Slf4j
public class StidUtils {

    /**
     * 给stid增加后缀
     */
    public static String addSuffix2Stid(String stid, String keyWord, String ste) {
        try {
            if (StringUtils.isEmpty(stid)) {
                return stid;
            }
            //如果使用搜索，则加上字段"_a(关键字)"
            if (StringUtils.isNotEmpty(keyWord)) {
                stid = new StringBuilder().append(stid).append("_a").append(keyWord).toString();
            }
            //如果客户端传来ste字段，将该字段放到最后
            if (StringUtils.isNotEmpty(ste)) {
                stid = new StringBuilder().append(stid).append(ste).toString();
            }
        } catch (Exception e) {
            log.error("StidUtils.addSuffix2Stid failed!", e);
        }
        return stid.toLowerCase();
    }

    public static void addSuffix2DidStidList(List<MtDealId2Stid> stids, String keyWord, String ste) { //推荐使用List<DidStid> stids
        try {
            //推荐传过来的stids已经携带字段_c，运营的没有携带_c
            if (CollectionUtils.isNotEmpty(stids)) {
                for (MtDealId2Stid didStid : stids) {
                    //如果使用搜索，则加上字段"_a(关键字)"
                    if (StringUtils.isNotEmpty(keyWord)) {
                        didStid.setStid(new StringBuilder().append(didStid.getStid()).append("_a").append(keyWord).toString().toLowerCase());
                    }
                    //如果客户端传来ste字段，将该字段放到最后
                    if (StringUtils.isNotEmpty(ste)) {
                        didStid.setStid(new StringBuilder().append(didStid.getStid()).append(ste).toString().toLowerCase());
                    }
                }
            }
        } catch (Exception e) {
            log.error("StidUtils.addSuffix2DidStidList failed!F", e);
        }
    }
}
