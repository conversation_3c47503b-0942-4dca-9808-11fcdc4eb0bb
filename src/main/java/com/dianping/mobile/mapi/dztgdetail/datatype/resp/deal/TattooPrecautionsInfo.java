package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@TypeDoc(description = "纹绣注意事项信息模块")
@MobileDo(id = 0xea2b)
public class TattooPrecautionsInfo implements Serializable {

    @FieldDoc(description = "天数")
    @MobileDo.MobileField(key = 0x839d)
    private String day;

    @FieldDoc(description = "天数下面对应的描述")
    @MobileDo.MobileField(key = 0x3721)
    private List<String> detailInfos;
}
