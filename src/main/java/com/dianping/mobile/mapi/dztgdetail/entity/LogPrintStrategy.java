package com.dianping.mobile.mapi.dztgdetail.entity;

import com.dianping.mobile.mapi.dztgdetail.common.enums.RuleStrategyEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-06-06
 * @desc 日志打印策略
 * {@link RuleStrategyEnum}
 */
@Data
public class LogPrintStrategy {
    /**
     * 环境的策略
     */
    private String envStrategy;
    /**
     * cell的策略
     */
    private String cellStrategy;
    /**
     * 泳道的策略
     */
    private String swimlaneStrategy;
    /**
     * userId和unionId的策略，
     */
    private String userIdAndUnionIdStrategy;
    /**
     * apiName的策略
     */
    private String apiNameStrategy;
    /**
     * ClientType的策略
     */
    private String dztgClientTypeStrategy;
    /**
     * 渠道来源的策略
     */
    private String pageSourceStrategy;
    /**
     * 采样率 0 - 100
     */
    private Integer logRate = 0;
}
