package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetMtPoiListRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.MtPoiModel;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.ViewItemDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.ViewListDo;
import com.dianping.mobile.mapi.dztgdetail.facade.GetMtPoiListFacade;
import com.dianping.mobile.mapi.dztgdetail.util.AntiCrawlerUtils;
import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.dianping.mobile.mapi.dztgdetail.util.AntiCrawlerUtils.getMtPoiListHide;

@InterfaceDoc(displayName = "商户列表页查询请求",
        type = "restful",
        description = "提供美团历史老版本查询商户同品牌的其他团单或者团购的适用商户（待废弃）",
        scenarios = "适用于美团APP历史版本访问，处于长期下线中，请新版本或其他情况都不要使用，如必须调用请联系当前owner。",
        host = "http://mapi.meituan.com/general/platform/mtdetail/",
        authors = "qian.wang.sh"
)
@Controller("getmtpoilist.bin")
@Action(url = "general/pla¬tform/mtdetail/getmtpoilist.bin", isCheckToken = false)
public class GetMtPoiListAction extends AbsAction<GetMtPoiListRequest> {

    @Resource
    private GetMtPoiListFacade getMtPoiListFacade;

    @Override
    protected IMobileResponse validate(GetMtPoiListRequest request, IMobileContext iMobileContext) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.action.app.GetMtPoiListAction.validate(com.dianping.mobile.mapi.dztgdetail.datatype.req.GetMtPoiListRequest,com.dianping.mobile.framework.datatypes.IMobileContext)");
        return null;
    }

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "getmtpoilist.bin",
            displayName = "提供美团历史老版本查询商户同品牌的其他团单或者团购的适用商户（待废弃）",
            description = "提供美团历史老版本查询商户同品牌的其他团单或者团购的适用商户（待废弃）",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "getmtpoilist.bin请求参数",
                            type = GetMtPoiListRequest.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "iMobileResponse", description = "通用返回数据模型")},
            restExampleUrl = "http://mapi.meituan.com/general/platform/mtdetail/getmtpoilist.bin?dealid=40700733",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    )
            }
    )
    @Override
    @CryptMethod
    protected IMobileResponse execute(GetMtPoiListRequest request, IMobileContext iMobileContext) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.action.app.GetMtPoiListAction.execute(com.dianping.mobile.mapi.dztgdetail.datatype.req.GetMtPoiListRequest,com.dianping.mobile.framework.datatypes.IMobileContext)");
        ViewListDo viewListDo = getMtPoiListFacade.getMtPoiList(request, iMobileContext);
        // poiid加密
        viewListDo.setList(toNormalList(viewListDo.getList()));
        // 工单加固
        hideSensitiveInfo(viewListDo , iMobileContext);
        return viewListDo != null ? new CommonMobileResponse(viewListDo) : Resps.SERVER_ERROR;
    }

    private void hideSensitiveInfo(ViewListDo viewListDo, IMobileContext iMobileContext) {
        if (!getMtPoiListHide(iMobileContext)) {
            return;
        }
        if(viewListDo == null){
            return;
        }
        if (CollectionUtils.isEmpty(viewListDo.getList())){
            return;
        }
        viewListDo.getList().forEach(item -> {
            MtPoiModel mtShop = item.getMtShop();
            // 隐藏地址和电话信息,加星处理
            mtShop.setAddr(AntiCrawlerUtils.hideAddressInfo(mtShop.getAddr()));
            mtShop.setPhone(AntiCrawlerUtils.hidePhoneInfo(mtShop.getPhone()));
        });
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.action.app.GetMtPoiListAction.getRule()");
        return null;
    }
    
    private List toNormalList(List<?> originList){
        if (CollectionUtils.isEmpty(originList)){
            return Lists.newArrayList();
        }
        List newList = Lists.newArrayList();
        originList.forEach(e->{
            newList.add(e);
        });
        return newList;
    }
}
