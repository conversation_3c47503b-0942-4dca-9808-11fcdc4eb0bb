package com.dianping.mobile.mapi.dztgdetail.datatype.resp.module;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;

import java.io.Serializable;

@MobileDo(id = 0x82a2)
public class MtDztgShareModule implements Serializable {
    /**
     * 个人分销参数
     */
    @MobileField(key = 0xed42)
    private String userDistributionParam;

    /**
     * 私域直播分享微信名称
     */
    @MobileField(key = 0x74dc)
    private String wxName;

    /**
     * 价格
     */
    @MobileField(key = 0xb716)
    private double price;

    /**
     * 图片链接
     */
    @MobileField(key = 0x43f0)
    private String imgUrl;

    /**
     * 标题
     */
    @MobileField(key = 0x24cc)
    private String title;

    /**
     * 品牌名称
     */
    @MobileField(key = 0xcfa2)
    private String brandName;

    /**
     * mtDealGroupId
     */
    @MobileField(key = 0xac63)
    private long mtDealGroupId;

    public String getUserDistributionParam() {
        return userDistributionParam;
    }

    public void setUserDistributionParam(String userDistributionParam) {
        this.userDistributionParam = userDistributionParam;
    }

    public String getWxName() {
        return wxName;
    }

    public void setWxName(String wxName) {
        this.wxName = wxName;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public long getMtDealGroupId() {
        return mtDealGroupId;
    }

    public void setMtDealGroupId(long mtDealGroupId) {
        this.mtDealGroupId = mtDealGroupId;
    }
}