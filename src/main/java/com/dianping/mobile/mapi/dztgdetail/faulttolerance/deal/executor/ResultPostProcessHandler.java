package com.dianping.mobile.mapi.dztgdetail.faulttolerance.deal.executor;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.*;
import com.dianping.mobile.mapi.dztgdetail.util.FitnessCrossUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 返回值后置处理对象
 */
@Slf4j
public class ResultPostProcessHandler<Result, Context> {

    private static ResultPostProcessHandler handler = new ResultPostProcessHandler<>();

    /**
     * 处理器列表map
     */
    private Map<String, List<ResultPostProcess<Result, Context>>> processMap = Maps.newHashMap();

    private ResultPostProcessHandler() {
    }

    /**
     * 单例
     */
    public static ResultPostProcessHandler getInstance() {
        return handler;
    }

    /**
     * 获取后置处理器列表，先从缓存里拿
     */
    @SuppressWarnings("unchecked")
    private List<ResultPostProcess<Result, Context>> getProcesses(Result r, Context c) {
        if (r == null) {
            return Collections.emptyList();
        }

        // 先尝试从缓存中获取
        String key = String.format("%s_%s", r.getClass().getSimpleName(), c == null ? "" : c.getClass().getSimpleName());
        List<ResultPostProcess<Result, Context>> processes = processMap.get(key);
        if (!CollectionUtils.isEmpty(processes)) {
            return processes;
        }

        // 创建新的并放到缓存
        processes = Lists.newArrayList();

        if (r instanceof DealGroupPBO && c instanceof DealCtx) {
            processes.add((ResultPostProcess<Result, Context>) new DeleteDiscountModulePostProcess());
        }
        processMap.put(key, processes);

        return processes;
    }


    /**
     * 执行后置处理
     */
    public void execute(Result r, Context c) {
        try {
            List<ResultPostProcess<Result, Context>> processes = getProcesses(r, c);
            if (CollectionUtils.isEmpty(processes)) {
                return;
            }
            // 迭代器
            processes.forEach(process -> process.execute(r, c));
        } catch (Exception e) {
            log.error("ResultPostProcessHandler.execute fail, e is ", e);
        }
    }

    /**
     * 返回值后置处理器
     */
    public static abstract class ResultPostProcess<Result, Context> {

        private ResultPostProcess() {
        }

        /**
         * 执行后置处理逻辑
         */
        protected abstract void doExecute(Result r, Context c);

        /**
         * 是否需要执行
         */
        protected abstract boolean shouldExecute(Result r, Context c);

        /**
         * 执行
         */
        public void execute(Result r, Context c) {
            try {
                if (!this.shouldExecute(r, c)) {
                    return;
                }
                doExecute(r, c);
            } catch (Exception e) {
                log.error("ResultPostProcessHandler execute fail, e is ", e);
            }
        }

    }

    /**
     * DealGroupPBO去掉优惠台
     */
    public static class DeleteDiscountModulePostProcess extends ResultPostProcess<DealGroupPBO, DealCtx> {

        /**
         * 价格
         */
        private static final String PRICE = "0";

        /**
         * 价格描述
         */
        private static final String PRICE_DESC = "健身通专享";

        /**
         * 价格描述颜色
         */
        private static final String PRICE_DESC_COLOR = "#FF4B10";

        @Override
        protected void doExecute(DealGroupPBO r, DealCtx c) {
            // 修改价格
            modifyPrice(r, c);
            // 修改优惠信息
            modifyDiscount(r, c);
        }

        @Override
        protected boolean shouldExecute(DealGroupPBO r, DealCtx c) {
            return FitnessCrossUtils.isFitnessCrossDeal(c);
        }

        /**
         * 修改价格
         */
        private void modifyPrice(DealGroupPBO r, DealCtx c) {
            FitnessCrossBO fitnessCrossBO = c.getFitnessCrossBO();
            if (fitnessCrossBO == null) {
                return;
            }

            // 新团详
            PromoDetailModule promoDetailModule = r.getPromoDetailModule();
            if (promoDetailModule == null) {
                return;
            }
            promoDetailModule.setPromoPrice(PRICE);
            promoDetailModule.setFinalPrice(PRICE);
            promoDetailModule.setPromoPriceDesc(new SimpleContextVO(PRICE_DESC, PRICE_DESC_COLOR));

            // 老团详
            PriceDisplayModuleDo priceDisplayModuleDo = new PriceDisplayModuleDo();
            priceDisplayModuleDo.setPrice(PRICE);
            priceDisplayModuleDo.setPriceDesc(new SimpleContextVO(PRICE_DESC, PRICE_DESC_COLOR));
            r.setPriceDisplayModuleDo(priceDisplayModuleDo);
        }

        /**
         * 去掉优惠台
         */
        private void modifyDiscount(DealGroupPBO r, DealCtx c) {
            if (r == null || r.getPromoDetailModule() == null) {
                return;
            }
            PromoDetailModule promoDetailModule = r.getPromoDetailModule();
            // 优惠信息
            promoDetailModule.setPromoAbstractList(null);
            // 全部优惠信息
            promoDetailModule.setCouponList(null);
            promoDetailModule.setPromoActivityList(null);
            promoDetailModule.setBestPromoDetails(null);
            // 折扣
            promoDetailModule.setMarketPromoDiscount(null);
            // 划线价格
            promoDetailModule.setMarketPrice(null);
            // 销量，根据配置决定是否需要展示
            if (c != null && c.getFitnessCrossBO() != null && c.getFitnessCrossBO().getConfig() != null && !c.getFitnessCrossBO().getConfig().isEnableSaleDesc()) {
                r.setSaleDesc(null);
                r.setSaleDescStr(null);
            }
        }

    }

}
