package com.dianping.mobile.mapi.dztgdetail.biz.processor.promo;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PromoWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.pay.promo.common.enums.ProductType;
import com.dianping.pay.promo.common.enums.promo.DisplayScene;
import com.dianping.pay.promo.common.enums.promo.SpecialPromoType;
import com.dianping.pay.promo.display.api.dto.Product;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.pay.promo.display.api.dto.request.BatchQueryPromoDisplayWithTimeRequest;
import com.dianping.pay.promo.display.api.dto.request.ReturnControl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.Future;

@Slf4j
public class PromoExposureProcessor extends AbsDealProcessor {

    private static final int PROMO_TEMP_ID = 239;

    @Autowired
    private PromoWrapper promoWrapper;

    @Override
    public void prepare(DealCtx ctx) {

        if (!Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.idle.promo.exposure.enable", false)) {
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        if (isThu(now)) {
            return;
        }

        BatchQueryPromoDisplayWithTimeRequest request = buildPromoWithTimeReq(ctx, getNearThu(now));
        Future future = promoWrapper.batchQueryPromoDisplayDTOWithTime(request);
        ctx.getFutureCtx().setPromoWithTimeFuture(future);
    }

    @Override
    public void process(DealCtx ctx) {
        if (isThu(LocalDateTime.now())) {
            return;
        }

        Map<Integer, List<PromoDisplayDTO>> map = promoWrapper.batchQueryPromoDisplayDTO(ctx.getFutureCtx().getPromoWithTimeFuture());
        ctx.setPromoWithTimeList(getPromoWithTimeList(map, getDealGroupId(ctx)));
    }

    private List<PromoDisplayDTO> getPromoWithTimeList(Map<Integer, List<PromoDisplayDTO>> map, int dealGroupId) {
        List<PromoDisplayDTO> list = MapUtils.isEmpty(map) ? null : map.get(dealGroupId);

        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        List<PromoDisplayDTO> idleTimePromos = new ArrayList<>();

        for (PromoDisplayDTO dto : list) {
            if (dto.getSpecialPromoType() == SpecialPromoType.IDLETIMES_PROMO.getCode()) {
                idleTimePromos.add(dto);
            }
        }

        return idleTimePromos;
    }

    private BatchQueryPromoDisplayWithTimeRequest buildPromoWithTimeReq(DealCtx ctx, Date date) {
        BatchQueryPromoDisplayWithTimeRequest request = new BatchQueryPromoDisplayWithTimeRequest();
        request.setClientVersion(ctx.getEnvCtx().getVersion());
        request.setPlatform(ctx.getEnvCtx().toPayPlatformCode());
        request.setDisplayScene(DisplayScene.ALL_PROMO.getCode());
        request.setMergeDisplayScenePromo(false);

        Product product = new Product();
        product.setProductId(getDealGroupId(ctx));

        if (ctx.getDealGroupBase() != null) {
            product.setPrice(ctx.getDealGroupBase().getDealGroupPrice());
        }

        request.setProductList(Collections.singletonList(product));

        request.setQueryTime(date);

        ReturnControl returnControl = new ReturnControl();
        returnControl.setReturnComposition(true);
        request.setReturnControl(returnControl);

        request.setTemplateID(PROMO_TEMP_ID);

        if (ctx.isMt()) {
            request.setProductType(ProductType.mt_tuangou.getValue());
            request.setDpId(ctx.getEnvCtx().getUuid());
            request.setUserId(ctx.getEnvCtx().getMtUserId());//已确认判断平台后再使用
            request.setCityId(ctx.getMtCityId());
        } else {
            request.setProductType(ProductType.tuangou.getValue());
            request.setUserId(ctx.getEnvCtx().getDpUserId());
            request.setDpId(ctx.getEnvCtx().getDpId());
            request.setCityId(ctx.getDpCityId());
        }

        return request;
    }

    private int getDealGroupId(DealCtx ctx) {
        return ctx.isMt() ? ctx.getMtId() : ctx.getDpId();
    }

    /**
     * 当前日期是否是周四
     * @return
     * @param now
     */
    private boolean isThu(LocalDateTime now) {
        return now.getDayOfWeek() == DayOfWeek.THURSDAY;
    }

    /**
     * 距离当前日期最近的一个周四
     * @param now
     * @return
     */
    private Date getNearThu(LocalDateTime now) {
        int dayOfWeek = now.getDayOfWeek().getValue();
        int interval = DayOfWeek.THURSDAY.getValue() - dayOfWeek;
        LocalDateTime nearThu = interval > 0 ? now.plusDays(interval) : now.plusDays(interval + 7);
        //营销时间参数需要+1s
        LocalDateTime thu = nearThu.toLocalDate().atStartOfDay().plusSeconds(1);

        return Date.from(thu.atZone(ZoneId.systemDefault()).toInstant());
    }

}