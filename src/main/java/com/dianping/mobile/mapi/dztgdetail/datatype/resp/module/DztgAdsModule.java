package com.dianping.mobile.mapi.dztgdetail.datatype.resp.module;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;

import java.io.Serializable;
import java.util.List;

@MobileDo(id = 0xbe85)
public class DztgAdsModule implements Serializable {
    /**
    * 团单前台类目，逐渐废弃中请勿使用（点评在用）
    */
    @MobileField(key = 0x372)
    private List<Integer> categoryIds;

    /**
    * 团单id（点评在用）
    */
    @MobileField(key = 0x2ae6)
    private long dealID;

    /**
    * 频道（前端在用）
    */
    @MobileField(key = 0x5108)
    private String channel;

    /**
    * 前台类目（美团在用）
    */
    @MobileField(key = 0x7c64)
    private List<Integer> frontPoiCates;

    /**
    * mtDealGroupId（美团在用）
    */
    @MobileField(key = 0xac63)
    private long mtDealGroupId;

    /**
    * shopId（美团在用）
    */
    @MobileField(key = 0x349b)
    private long shopId;

    public List<Integer> getCategoryIds() {
        return categoryIds;
    }

    public void setCategoryIds(List<Integer> categoryIds) {
        this.categoryIds = categoryIds;
    }

    public long getDealID() {
        return dealID;
    }

    public void setDealID(long dealID) {
        this.dealID = dealID;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public List<Integer> getFrontPoiCates() {
        return frontPoiCates;
    }

    public void setFrontPoiCates(List<Integer> frontPoiCates) {
        this.frontPoiCates = frontPoiCates;
    }

    public long getMtDealGroupId() {
        return mtDealGroupId;
    }

    public void setMtDealGroupId(long mtDealGroupId) {
        this.mtDealGroupId = mtDealGroupId;
    }

    public long getShopId() {
        return shopId;
    }

    public void setShopId(long shopId) {
        this.shopId = shopId;
    }
}