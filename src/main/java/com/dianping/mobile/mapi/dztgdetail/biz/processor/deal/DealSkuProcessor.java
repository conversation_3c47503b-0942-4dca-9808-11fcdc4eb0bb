package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MultiSkuExpBiz;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.SkuCtx;
import com.dianping.mobile.mapi.dztgdetail.entity.SkuSummary;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

public class DealSkuProcessor extends AbsDealProcessor {

    @Autowired
    private MultiSkuExpBiz multiSkuExpBiz;

    private static final String FLOATING_LAYER = "floatingLayer";

    @Override
    public void prepare(DealCtx ctx) {
        // 因为依赖团购类目信息做过滤，为降低请求数量，将请求前置到ParallDealGroupProcessor获取category处
    }

    @Override
    public void process(DealCtx ctx) {
        SkuCtx skuCtx = new SkuCtx();
        // 设置多sku参数
        SkuSummary skuSummary = ctx.getSkuSummary();
        if (skuSummary != null) {
            skuCtx.setMultiSku(skuSummary.getWithSaleAttr());
            skuCtx.setSkuId(skuSummary.getDefaultSkuId() == null || skuSummary.getDefaultSkuId() == 0L ? "" : String.valueOf(skuSummary.getDefaultSkuId()));
        }

        // 控制出提单浮层：商户后台类目白名单 + 多sku团单
        if (multiSkuExpBiz.showLayer(ctx.getPoiBackCategoryIds()) || (skuSummary != null && skuSummary.getWithSaleAttr())
                || showLayerAsCategoryAndServiceType(ctx.getDealGroupDTO())) {
            skuCtx.setCreatOrderExpId(FLOATING_LAYER);
        }
        ctx.setSkuCtx(skuCtx);
    }

    public boolean showLayerAsCategoryAndServiceType(DealGroupDTO dealGroupDTO) {
        int categoryId = getDealSecondCategoryId(dealGroupDTO);
        String serviceType = getServiceType(dealGroupDTO);
        return LionConfigUtils.isShowCreateOrderLayer(categoryId, serviceType);
    }

    private String getServiceType(DealGroupDTO dealGroupDTO) {
        return dealGroupDTO == null || dealGroupDTO.getCategory() == null
                ? null : dealGroupDTO.getCategory().getServiceType();
    }

    private int getDealSecondCategoryId(DealGroupDTO dealGroupDTO) {
        if (Objects.isNull(dealGroupDTO) || Objects.isNull(dealGroupDTO.getBasic()) || Objects.isNull(dealGroupDTO.getBasic().getCategoryId())) {
            return 0;
        }
        return dealGroupDTO.getBasic().getCategoryId().intValue();
    }
}
