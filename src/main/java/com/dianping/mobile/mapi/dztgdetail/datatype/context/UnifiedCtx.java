package com.dianping.mobile.mapi.dztgdetail.datatype.context;

import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 查询团单信息的环境变量
 */
@Data
public class UnifiedCtx {

    public UnifiedCtx(EnvCtx ctx) {
        if (ctx != null) {
            this.envCtx = ctx;
        }
    }

    private EnvCtx envCtx = new EnvCtx();

    private int dpId; //点评团单ID
    private int mtId; //美团团单ID

    private int dpCityId;
    private int mtCityId;

    @Deprecated
    private int mtShopId;
    @Deprecated
    private int dpShopId;
    private String token;
    private String expResults;

    private double lat;
    private double lng;

    private String version;

    private String mrnVersion;
    /**
     * 是否为快手渠道
     */
    private boolean kuaiShouMiniProgram;

    private List<ModuleAbConfig> moduleAbConfigs = new ArrayList<>();

    private List<AttributeDTO> attributes;

    public boolean isMt() {
        return this.envCtx.isMt();
    }

    public boolean isExternal() {
        return this.envCtx.isExternal();
    }

}
