package com.dianping.mobile.mapi.dztgdetail.biz.dealnotice;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealNoticeLayerCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealnotice.DealNoticeLayerPBO;
import com.google.common.collect.Sets;
import com.sankuai.general.product.query.center.client.builder.model.*;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.AttrSubjectEnum;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @create 2024/9/30 17:17
 */
@Component
@Slf4j
public abstract class AbstractDealNoticeProcessor implements DealNoticeProcessor{

    @Autowired
    public QueryCenterWrapper queryCenterWrapper;
    public abstract boolean valid(DealNoticeLayerCtx ctx);

    public abstract DealNoticeLayerPBO getDealNoticeLayerPBO(DealNoticeLayerCtx ctx);

    public DealGroupDTO getDealGroupDTO(Long dpDealGroupId, DealNoticeLayerCtx ctx) {
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet(dpDealGroupId), ctx.getEnvCtx().isMt() ? IdTypeEnum.MT : IdTypeEnum.DP)
                .dealGroupId(DealGroupIdBuilder.builder().all())
                .category(DealGroupCategoryBuilder.builder().all())
                .attrsByKey(AttrSubjectEnum.DEAL_GROUP, getQueryCenterDealGroupAttrKey())
                .build();
        try {
            Future dealGroupFuture = queryCenterWrapper.preDealGroupDTO(queryByDealGroupIdRequest);
            DealGroupDTO dealGroupDTO = queryCenterWrapper.getDealGroupDTO(dealGroupFuture);
            if (Objects.isNull(dealGroupDTO)) {
                return null;
            }
            return dealGroupDTO;
        } catch(Exception e) {
            log.error("[AbstractDealNoticeProcessor] queryCenterWrapper.preDealGroupDTO err {}", e);
        }
        return null;
    }

    public static Set<String> getQueryCenterDealGroupAttrKey() {
        String keyArr = "Farthest_service_range,category";
        Set<String> keys = Sets.newHashSet(Arrays.asList(keyArr.split(",")));
        return keys;
    }

}
