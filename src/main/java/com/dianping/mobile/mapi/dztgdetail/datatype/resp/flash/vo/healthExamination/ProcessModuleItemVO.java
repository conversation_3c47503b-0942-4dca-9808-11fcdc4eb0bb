package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.healthExamination;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/10/18 11:10 上午
 */
@MobileDo(id = 0x7c5c)
public class ProcessModuleItemVO implements Serializable {
    /**
     * 流程步骤电话号码模块
     */
    @MobileDo.MobileField(key = 0x2ff4)
    private NumProcessSubitemVO numProcessItem;

    /**
     * 流程步骤子模块（如：预约时间：提前3天预约）
     */
    @MobileDo.MobileField(key = 0x322f)
    private ProcessSubitemVO processItem;

    /**
     * 流程步骤内容（单行文本，如：请您根据个人情况购买合适的体检套餐）
     */
    @MobileDo.MobileField(key = 0x50ad)
    private String processContent;

    public NumProcessSubitemVO getNumProcessItem() {
        return numProcessItem;
    }

    public void setNumProcessItem(NumProcessSubitemVO numProcessItem) {
        this.numProcessItem = numProcessItem;
    }

    public ProcessSubitemVO getProcessItem() {
        return processItem;
    }

    public void setProcessItem(ProcessSubitemVO processItem) {
        this.processItem = processItem;
    }

    public String getProcessContent() {
        return processContent;
    }

    public void setProcessContent(String processContent) {
        this.processContent = processContent;
    }
}
