package com.dianping.mobile.mapi.dztgdetail.common.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-12-18
 * @desc 款式场景枚举
 */
@Getter
public enum ExhibitImageSceneEnum {

    SELF("self", "本团购参考款式"),
    RECOMMEND("recommend", "更多推荐款式"),
    ORDER("order", "订单详情页美甲款式类目调用"),
    ORDER_RECOMMEND("orderrecommend", "订单详情页美甲款式推荐"),
    ;

    final String sceneCode;
    final String desc;

    ExhibitImageSceneEnum(String sceneCode, String desc) {
        this.sceneCode = sceneCode;
        this.desc = desc;
    }

    /**
     * 是否来自订单详情页
     * @param sceneCode 场景值
     * @return true/false
     */
    public static boolean fromOrderDetailPage(String sceneCode) {
        if (StringUtils.isBlank(sceneCode)) {
            return false;
        }
        return Objects.equals(sceneCode, ORDER.getSceneCode())
                || Objects.equals(sceneCode, ORDER_RECOMMEND.getSceneCode());
    }

    /**
     * 是否是推荐场景
     * @param sceneCode 场景值
     * @return true/false
     */
    public static boolean fromRecommendScene(String sceneCode) {
        if (StringUtils.isBlank(sceneCode)) {
            return false;
        }
        return Objects.equals(sceneCode, RECOMMEND.getSceneCode())
                || Objects.equals(sceneCode, ORDER_RECOMMEND.getSceneCode());
    }
}
