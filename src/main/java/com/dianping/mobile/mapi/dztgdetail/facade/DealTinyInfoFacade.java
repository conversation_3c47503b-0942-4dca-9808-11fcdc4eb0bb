package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.cat.Cat;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.lion.common.util.JsonUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DzDealThemeWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.HaimaWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiClientWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PriceRangeQueryWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.SkuWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.ShopUuidUtils;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetDealTinyInfoRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealLowPriceItemEntranceVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealPriceTrendVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealTinyInfoVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PriceTrendVO;
import com.dianping.mobile.mapi.dztgdetail.entity.DealRepurchaseConfig;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.TimeUtils;
import com.dianping.scrum.util.DateUtils;
import com.dianping.tuangu.dztg.usercenter.api.enums.CreateOrderPageSourceEnum;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dealuser.price.display.api.enums.PricePowerTagEnum;
import com.sankuai.dealuser.price.display.api.enums.RequestSceneEnum;
import com.sankuai.dztheme.deal.dto.DealProductAttrDTO;
import com.sankuai.dztheme.deal.dto.DealProductDTO;
import com.sankuai.dztheme.deal.dto.DealProductSaleDTO;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.dztheme.deal.res.DealSkuSummaryDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.tpfun.skuoperationapi.price.dto.PriceRangeDO;
import com.sankuai.tpfun.skuoperationapi.price.dto.PriceRangeItemDTO;
import com.sankuai.tpfun.skuoperationapi.price.dto.SubjectDTO;
import com.sankuai.tpfun.skuoperationapi.price.dto.enums.PriceRangeItemTypeEnum;
import com.sankuai.tpfun.skuoperationapi.price.dto.response.BatchPriceRangeInfoResponse;
import com.sankuai.tpfun.skuoperationapi.price.dto.response.Response;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-10-09
 * @desc 实现查询团单简要信息
 */
@Component
@Slf4j
public class DealTinyInfoFacade {
    /**
     * 综团比价-购物车未选中&团详场景
     */
    private static final String DZ_TINY_INFO_DEAL_PLAN_ID = "10002451";

    /**
     * 综团比价-购物车选中场景
     */
    private static final String DZ_TINY_INFO_SHOP_CAR_SELECT_PLAN_ID = "10002465";

    /**
     * 综团比价-购物车未选中&团详场景
     */
    private static final String DZ_PRICE_TREND_DEAL_PLAN_ID = "10002454";

    /**
     * 综团比价-购物车选中场景
     */
    private static final String DZ_PRICE_TREND_SHOP_CAR_SELECT_PLAN_ID = "10002466";

    /**
     * 页面来源-团详
     */
    private static final String PAGE_SOURCE_DEAL = "deal";
    /**
     * 页面来源-购物车未选中
     */
    private static final String PAGE_SOURCE_SHOP_CAR_NO_SELECT = "shopcarnoselect";
    /**
     * 页面来源-购物车选中
     */
    private static final String PAGE_SOURCE_SHOP_CAR_SELECT = "shopcarselect";
    /**
     * 页面来源-其他
     */
    private static final String PAGE_SOURCE_OTHER = "other";
    /**
     * 页面来源-猜喜
     */
    private static final String PAGE_SOURCE_CAIXI = "caixi";

    /**
     * 对应竞争圈价格排名前20%的价格的key
     */
    private static final String TOP_TWENTY_PERCENT_PRICE = "topTwentyPercentPrice";
    /**
     * 对应竞争圈价格排名前50%的价格的key
     */
    private static final String TOP_FIFTY_PERCENT_PRICE = "topFiftyPercentPrice";


    @Resource
    private DzDealThemeWrapper dzDealThemeWrapper;

    @Resource
    private PriceRangeQueryWrapper priceRangeQueryWrapper;

    @Resource
    private PoiClientWrapper poiClientWrapper;
    @Resource
    private DealGroupWrapper dealGroupWrapper;
    @Resource
    private SkuWrapper skuWrapper;

    @Resource
    private HaimaWrapper haimaWrapper;

    @Resource
    private DouHuService douHuService;

    public DealTinyInfoVO getDealTinyInfo(GetDealTinyInfoRequest request, EnvCtx envCtx) {
        // 参数校验
        checkRequest(request);
        request.setPlatform(envCtx.isMt() ? VCPlatformEnum.DP.getType() : VCPlatformEnum.MT.getType());
        // 请求团单概要信息
        return queryDealTinyInfo(request, envCtx);
    }

    public DealPriceTrendVO getDealPriceTrend(GetDealTinyInfoRequest request, EnvCtx envCtx) {
        // 参数校验
        checkRequest(request);
        request.setPlatform(envCtx.isMt() ? VCPlatformEnum.DP.getType() : VCPlatformEnum.MT.getType());
        // 预请求团单信息和价格趋势信息
        int dealId = Integer.parseInt(request.getDealGroupId());
        long shopId  = Long.parseLong(request.getShopId());
        // sku信息
        DealSkuSummaryDTO skuSummary = skuWrapper.getSkuSummaryByDealId(dealId, envCtx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP);
        int skuId = Objects.nonNull(skuSummary) && skuSummary.getDefaultSkuId() > 0 ? Math.toIntExact(skuSummary.getDefaultSkuId()) : 0;
        DealProductRequest dealProductRequest = buildQueryDealAndPriceRequest(envCtx, dealId, shopId, request, skuId);
        Future dealProductFuture = dzDealThemeWrapper.preQueryDealProduct(dealProductRequest);
        DealProductResult dealProductResult = dzDealThemeWrapper.getFutureResult(dealProductFuture);
        return getDealPriceTrendResult(dealProductResult, skuId, request.getPageSource());
    }

    private DealProductRequest buildQueryDealAndPriceRequest(EnvCtx ctx, int dealId, long shopId, GetDealTinyInfoRequest request,
                                                             int skuId) {
        DealProductRequestParam params = DealProductRequestParam.builder()
                .planId(DZ_PRICE_TREND_DEAL_PLAN_ID)
                .ctx(ctx)
                .dealId(dealId)
                .pageSource(request.getPageSource())
                .shopId(shopId)
                .skuId(skuId)
                .needPriceTrend(true)
                .cityId(request.getCityId())
                .lat(request.getLat())
                .lng(request.getLng())
                .build();
        if (Objects.equals(request.getPageSource(), PAGE_SOURCE_SHOP_CAR_SELECT)) {
            params.setPlanId(DZ_PRICE_TREND_SHOP_CAR_SELECT_PLAN_ID);
        }
        return buildRequest(params);
    }

    /**
     * 查询团单简要信息，不包含副标题
     *
     * @param request 团单Id、页面来源、门店Id
     * @param envCtx 上下文环境
     * @return 团单信息
     */
    private DealTinyInfoVO queryDealTinyInfo(GetDealTinyInfoRequest request, EnvCtx envCtx) {
        // 预请求团单概要信息
        int dealId = Integer.parseInt(request.getDealGroupId());
        long shopId = Long.parseLong(request.getShopId());
        DealSkuSummaryDTO skuSummary = skuWrapper.getSkuSummaryByDealId(dealId, envCtx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP);
        int skuId = Objects.nonNull(skuSummary) && (skuSummary.getDefaultSkuId() > 0) ? Math.toIntExact(skuSummary.getDefaultSkuId()) : 0;
        DealProductRequest dealProductRequest = buildQueryDealProductRequest(envCtx, dealId, shopId, request, skuId);
        Future dealProductFuture = dzDealThemeWrapper.preQueryDealProduct(dealProductRequest);
        DealProductResult dealProductResult = dzDealThemeWrapper.getFutureResult(dealProductFuture);
        return getDealResult(dealProductResult, skuId, request.getPageSource());
    }

    /**
     * 判断是否展示同款低价入口
     * @param request 参数请求
     * @param envCtx 环境上下文
     * @return 是否展示同款低价入口
     */
    public DealLowPriceItemEntranceVO isShowLowPriceItemEntrance(GetDealTinyInfoRequest request, EnvCtx envCtx) {
        DealLowPriceItemEntranceVO priceItemEntranceVO = new DealLowPriceItemEntranceVO();
        // 判断门店是否在黑名单中
        if (isShopInBlackList(request.getShopId(), envCtx.isMt())) {
            priceItemEntranceVO.setShowLowPriceDealList(false);
            return priceItemEntranceVO;
        }
        int dealId = Integer.parseInt(request.getDealGroupId());
        // 转成点评团单ID
        int dpDealId = envCtx.isMt() ? dealGroupWrapper.getDpDealGroupId(dealId) : dealId;
        // 查询图单类目Id
        int categoryId = dealGroupWrapper.getCategoryId(dpDealId);
        // 复购货架-团单被限购才会请求
        if ("purchase_limit".equals(request.getBusinessType())){
            priceItemEntranceVO.setShowLowPriceDealList(false);
            if(enablePurchaseLimit() && LionConfigUtils.enableRepurchase(categoryId, envCtx.isMt(), request.getCityId())){
                // 命中实验结果c 可展示
                priceItemEntranceVO.setShowLowPriceDealList(isRightExpResult(envCtx));
            }
            return priceItemEntranceVO;
        }
        // 转成点评门店Id
        long shopId = Long.parseLong(request.getShopId());
        long dpShopId = shopId;
        if (envCtx.isMt()) {
            MtPoiDTO mtPoiDTO = poiClientWrapper.getMtPoiDTO(shopId, Lists.newArrayList("dpPoiId", "mtPoiId"));
            if (mtPoiDTO != null && mtPoiDTO.getDpPoiId() != null) {
                dpShopId = mtPoiDTO.getDpPoiId();
            } else {
                Cat.logEvent("NullPointer", "mtPoiDTO or mtPoiDTO.getDpPoiId() is null");
            }
        }
        // 查询团单价格
        DealTinyInfoVO dealTinyInfoVO = queryDealTinyInfo(request, envCtx);
        BigDecimal dealFinalPrice = new BigDecimal(
                Optional.ofNullable(dealTinyInfoVO).map(DealTinyInfoVO::getFinalPrice).orElse("0"));
        boolean isShow;
        String topPriceStr;
        boolean hasPricePowerTag = false;
        if (Objects.equals(request.getPageSource(), PAGE_SOURCE_CAIXI)) {
            // 竞争圈前20%低价
            topPriceStr = getTopTwentyPercentPrice(dpDealId, dpShopId);
        } else {
            // 竞争圈前50%价格带
            topPriceStr = getTopFiftyPercentPrice(dpDealId, dpShopId);
            hasPricePowerTag = CollectionUtils.isNotEmpty(Optional.ofNullable(dealTinyInfoVO)
                    .map(DealTinyInfoVO::getPricePowerTag).orElse(Lists.newArrayList()));
        }
        DealCtx ctx = buildDealCtx(envCtx, categoryId);
        if (StringUtils.isBlank(topPriceStr) || hasPricePowerTag) {
            priceItemEntranceVO.setShowLowPriceDealList(false);
            return priceItemEntranceVO;
        }
        BigDecimal topPrice = new BigDecimal(topPriceStr);
        // 猜喜路径：当前团单价格 > 竞争圈前20%价格，则展示
        // 其他路径：当前团单无价格力标签 & 价格 > 竞争圈前50%价格，则展示
        isShow = dealFinalPrice.compareTo(topPrice) > 0;
        // 查询允许展示比价浮层的类目列表
        boolean comparePriceAbTestSwitch = isShow && douHuService.showComparePrice(ctx);
        priceItemEntranceVO.setShowLowPriceDealList(comparePriceAbTestSwitch);
        return priceItemEntranceVO;
    }

    private DealCtx buildDealCtx(EnvCtx envCtx, int categoryId){
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(categoryId);
        ctx.setChannelDTO(channelDTO);

        return ctx;
    }

    private boolean isRightExpResult(EnvCtx envCtx){
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.facade.DealTinyInfoFacade.isRightExpResult(com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx)");
        // 判断是否命中c组实验
        String expResult = douHuService.getRepurchaseShelfExpResult(envCtx);
        return "c".equals(expResult);
    }
    
    private boolean isShopInBlackList(String shopId, boolean isMt) {
        return haimaWrapper.isBlackListShop(shopId, isMt)
                || LionConfigUtils.isComparePriceShopBlackList(isMt, shopId);
    }

    /**
     * 比价助手开关
     * @return
     */
    private boolean enablePurchaseLimit(){
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.DealTinyInfoFacade.enablePurchaseLimit()");
        DealRepurchaseConfig dealRepurchaseConfig = LionConfigUtils.getRepurchaseConfig();
        if (Objects.nonNull(dealRepurchaseConfig) && dealRepurchaseConfig.isEnableSwitch()){
            return dealRepurchaseConfig.isEnablePriceItemList();
        }
        return Boolean.FALSE;
    }


    private String getTopTwentyPercentPrice(int dpDealId, long dpShopId) {
        return getTopPrice(dpDealId, dpShopId, TOP_TWENTY_PERCENT_PRICE);
    }

    private String getTopFiftyPercentPrice(int dpDealId, long dpShopId) {
        return getTopPrice(dpDealId, dpShopId, TOP_FIFTY_PERCENT_PRICE);
    }

    private String getTopPrice(int dpDealId, long dpShopId, String topPriceKey) {
        Future priceRangeFuture = priceRangeQueryWrapper.preQueryPriceRange(dpDealId, dpShopId);
        Response<BatchPriceRangeInfoResponse> response = priceRangeQueryWrapper.getFutureResult(priceRangeFuture);
        if (!response.isSuccess() || Objects.isNull(response.getResult())
                || Objects.isNull(response.getResult().getSubjectDTO2PriceRangesMap())) {
            return StringUtils.EMPTY;
        }
        SubjectDTO subjectDTO = new SubjectDTO();
        subjectDTO.setDpShopId(dpShopId);
        subjectDTO.setProductId((long)dpDealId);
        List<PriceRangeItemDTO> priceRangeItems = response.getResult().getSubjectDTO2PriceRangesMap()
                .getOrDefault(subjectDTO, Collections.emptyList());
        if (CollectionUtils.isEmpty(priceRangeItems)) {
            return StringUtils.EMPTY;
        }
        List<PriceRangeItemDTO> priceRangeItemDTOList = priceRangeItems.stream()
                .filter(e -> Objects.equals(e.getPriceRangeItemType(),
                        PriceRangeItemTypeEnum.SHOPPING_CART_SPACE_PRICE_RANGE_ITEM.getCode()))
                .collect(Collectors.toList());
        PriceRangeDO priceRangeDO = priceRangeItemDTOList.get(0).getPriceRangeDO();
        if (Objects.isNull(priceRangeDO) || Objects.isNull(priceRangeDO.getExtra())) {
            return StringUtils.EMPTY;
        }
        return priceRangeDO.getExtra().getOrDefault(topPriceKey, StringUtils.EMPTY);
    }

    private DealPriceTrendVO getDealPriceTrendResult(DealProductResult dealProductResult, Integer skuId,
                                                     String pageSource) {
        DealTinyInfoVO tinyDeal = getDealResult(dealProductResult, skuId, pageSource);
        if (Objects.isNull(tinyDeal)) {
            return null;
        }
        DealPriceTrendVO dealPriceTrendVO = convertTinyDealToPriceTrend(tinyDeal);
        if (Objects.isNull(dealPriceTrendVO)) {
            return null;
        }
        if (Objects.isNull(dealProductResult) || CollectionUtils.isEmpty(dealProductResult.getDeals())) {
            return null;
        }
        List<DealProductAttrDTO> attrs = dealProductResult.getDeals().get(0).getAttrs();
        // 价格趋势更新时间
        dealPriceTrendVO.setUpdateTime(getPriceTrendUpdateTime(7));
        dealPriceTrendVO.setDirectBuyJumpUrl(tinyDeal.getDirectBuyJumpUrl());
        // 价格力标签
        String pricePowerTag = CollectionUtils.isEmpty(tinyDeal.getPricePowerTag()) ? StringUtils.EMPTY
                : tinyDeal.getPricePowerTag().get(0);
        // 价格趋势
        String dealPriceTrendJson;
        if (Objects.equals(pageSource, PAGE_SOURCE_SHOP_CAR_SELECT)) {
            dealPriceTrendJson = getDealProductAttrValue(attrs, "dealPriceTrendForTrade");
        } else {
            dealPriceTrendJson = getDealProductAttrValue(attrs, "dealPriceTrend");
        }
        List<PriceTrendVO> dealPriceTrend = JsonUtils.fromJson(dealPriceTrendJson, new TypeReference<List<PriceTrendVO>>() {});
        List<PriceTrendVO> dealPriceTrendFormat = CollectionUtils.emptyIfNull(dealPriceTrend).stream()
                .map(e -> new PriceTrendVO(e.getDate(), e.getPrice().stripTrailingZeros())).collect(Collectors.toList());
        List<PriceTrendVO> priceTrends = getPriceTrends(pricePowerTag, dealPriceTrendFormat, tinyDeal.getFinalPrice());
        dealPriceTrendVO.setTrends(priceTrends);
        // N天前价
        BigDecimal benchMarkPrice = CollectionUtils.isNotEmpty(priceTrends) ? priceTrends.get(0).getPrice() : null;
        String benchMarkPriceStr = Objects.isNull(benchMarkPrice) ? null : benchMarkPrice.stripTrailingZeros().toPlainString();
        dealPriceTrendVO.setBenchMarkPrice(benchMarkPriceStr);
        // N天前价标签
        int pricePowerTagDay = getPricePowerTagDay(pricePowerTag);
        dealPriceTrendVO.setBenchMarkPriceTag(pricePowerTagDay + "天前价");
        // 价格趋势说明
        String dealPriceTrendDesc = LionConfigUtils.getDealPriceTrendDesc(tinyDeal.getCategoryId());
        dealPriceTrendVO.setPriceTrendDesc(dealPriceTrendDesc);
        return dealPriceTrendVO;
    }

    private List<PriceTrendVO> getPriceTrends(String pricePowerTag, List<PriceTrendVO> dealPriceTrend, String todayFinalPrice) {
        if (StringUtils.isBlank(pricePowerTag) || CollectionUtils.isEmpty(dealPriceTrend)) {
            return null;
        }
        int pricePowerTagDay = getPricePowerTagDay(pricePowerTag);
        if (pricePowerTagDay == 0) {
            return null;
        }
        BigDecimal recentPrice = dealPriceTrend.get(dealPriceTrend.size() - 1).getPrice();
        Date fewDaysBeforeDate = TimeUtils.getFewDaysBeforeDate(DateUtils.currentDate(), pricePowerTagDay - 1);
        List<PriceTrendVO> priceTrends = getPriceTrends(dealPriceTrend, fewDaysBeforeDate);
        String dateTimePattern = "yyyy-MM-dd";
        SimpleDateFormat sdf = new SimpleDateFormat(dateTimePattern);
        if (CollectionUtils.isEmpty(priceTrends)) {
            PriceTrendVO fewDaysPrice = new PriceTrendVO();
            fewDaysPrice.setDate(sdf.format(fewDaysBeforeDate));
            fewDaysPrice.setPrice(recentPrice);
            priceTrends = Lists.newArrayList();
            priceTrends.add(fewDaysPrice);
        }
        PriceTrendVO todayPrice = new PriceTrendVO();
        todayPrice.setDate(sdf.format(DateUtils.currentDate()));
        todayPrice.setPrice(new BigDecimal(todayFinalPrice));
        priceTrends.add(todayPrice);
        return priceTrends;
    }

    /**
     * 获取价格趋势，在趋势数据中增加了N天前的价格
     *
     * @param dealPriceTrend 价格趋势数据
     * @param fewDaysBeforeDate N天前
     * @return N天前-现在的价格趋势
     */
    private List<PriceTrendVO> getPriceTrends(List<PriceTrendVO> dealPriceTrend, Date fewDaysBeforeDate) {
        if (CollectionUtils.isEmpty(dealPriceTrend)) {
            return null;
        }
        PriceTrendVO firstPriceTrendVO = dealPriceTrend.get(0);
        String dateTimePattern = "yyyy-MM-dd";
        SimpleDateFormat sdf = new SimpleDateFormat(dateTimePattern);
        try {
            Date firstPriceTrendDate = sdf.parse(firstPriceTrendVO.getDate());
            if (firstPriceTrendDate.after(fewDaysBeforeDate)) {
                // N天前的价格 = 价格趋势第一天价格
                PriceTrendVO beforeNDayPriceTrendVO = new PriceTrendVO();
                beforeNDayPriceTrendVO.setDate(sdf.format(fewDaysBeforeDate));
                beforeNDayPriceTrendVO.setPrice(firstPriceTrendVO.getPrice());
                dealPriceTrend.add(0, beforeNDayPriceTrendVO);
            } else if (firstPriceTrendDate.before(fewDaysBeforeDate)) {
                // 删除在N天前的价格
                ListIterator<PriceTrendVO> iterator = dealPriceTrend.listIterator();
                PriceTrendVO beforeNDayPriceTrendVO = null;
                while (iterator.hasNext()) {
                    PriceTrendVO comparePriceTrendVO = iterator.next();
                    Date comparePriceTrendDate = sdf.parse(comparePriceTrendVO.getDate());
                    if (comparePriceTrendDate.before(fewDaysBeforeDate)) {
                        iterator.remove();
                    } else {
                        if (CollectionUtils.isEmpty(dealPriceTrend)) {
                            break;
                        }
                        beforeNDayPriceTrendVO = new PriceTrendVO();
                        beforeNDayPriceTrendVO.setDate(sdf.format(fewDaysBeforeDate));
                        beforeNDayPriceTrendVO.setPrice(dealPriceTrend.get(0).getPrice());
                        break;
                    }
                }
                if (Objects.nonNull(beforeNDayPriceTrendVO)) {
                    dealPriceTrend.add(0, beforeNDayPriceTrendVO);
                }
            }
        } catch (ParseException e) {
            log.error("DealTinyInfoFacade getBenchMarkPrice parse price trend date error!", e);
            return null;
        }
        return dealPriceTrend;
    }

    private String getPriceTrendUpdateTime(int hour) {
        LocalTime currentTime = LocalTime.now();
        LocalTime targetTime = LocalTime.of(hour, 0);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd");
        String date = "";
        if (currentTime.isAfter(targetTime)) {
            date = LocalDate.now().format(formatter);
        } else {
            date = LocalDate.now().minusDays(1).format(formatter);
        }
        return date + " 7:00";
    }

    private int getPricePowerTagDay(String pricePowerTag) {
        if (Objects.equals(pricePowerTag, PricePowerTagEnum.LOWEST_PRICE_IN_RECENT_30_DAYS.getDesc())) {
            return 30;
        }
        if (Objects.equals(pricePowerTag, PricePowerTagEnum.LOWEST_PRICE_IN_RECENT_60_DAYS.getDesc())) {
            return 60;
        }
        if (Objects.equals(pricePowerTag, PricePowerTagEnum.LOWEST_PRICE_IN_RECENT_90_DAYS.getDesc())) {
            return 90;
        }
        if (Objects.equals(pricePowerTag, PricePowerTagEnum.LOWEST_PRICE_IN_RECENT_180_DAYS.getDesc())) {
            return 180;
        }
        if (Objects.equals(pricePowerTag, PricePowerTagEnum.LOWEST_PRICE_IN_RECENT_365_DAYS.getDesc())) {
            return 365;
        }
        return 0;
    }

    private DealPriceTrendVO convertTinyDealToPriceTrend(DealTinyInfoVO tinyDeal) {
        if (Objects.isNull(tinyDeal)) {
            return null;
        }
        DealPriceTrendVO dealPriceTrendVO = new DealPriceTrendVO();
        dealPriceTrendVO.setDealGroupId(tinyDeal.getDealGroupId());
        dealPriceTrendVO.setTitle(tinyDeal.getTitle());
        dealPriceTrendVO.setSubTitle(tinyDeal.getSubTitle());
        dealPriceTrendVO.setSaleTag(tinyDeal.getSaleTag());
        dealPriceTrendVO.setHeadPic(tinyDeal.getHeadPic());
        dealPriceTrendVO.setPricePowerTag(tinyDeal.getPricePowerTag());
        dealPriceTrendVO.setDiscount(tinyDeal.getDiscount());
        dealPriceTrendVO.setFinalPrice(tinyDeal.getFinalPrice());
        dealPriceTrendVO.setMarketPrice(tinyDeal.getMarketPrice());
        return dealPriceTrendVO;
    }

    private DealTinyInfoVO getDealResult(DealProductResult dealProductResult, Integer skuId, String pageSource) {
        if (Objects.isNull(dealProductResult) || CollectionUtils.isEmpty(dealProductResult.getDeals())) {
            return null;
        }
        DealProductDTO dealProductDTO = dealProductResult.getDeals().get(0);
        DealTinyInfoVO dealTinyInfoVO = new DealTinyInfoVO();
        dealTinyInfoVO.setDealGroupId(dealProductDTO.getProductId());
        dealTinyInfoVO.setTitle(dealProductDTO.getName());
        DealProductSaleDTO sale = dealProductDTO.getSale();
        dealTinyInfoVO.setSaleTag(Optional.ofNullable(sale).map(DealProductSaleDTO::getSaleTag).orElse(StringUtils.EMPTY));
        dealTinyInfoVO.setHeadPic(dealProductDTO.getHeadPic());
        dealTinyInfoVO.setBtnText("抢购");
        List<DealProductAttrDTO> attrs = dealProductDTO.getAttrs();
        // 副标题
        String dealSubTitleJson = getDealProductAttrValue(attrs, "dealSubTitle");
        if (StringUtils.isNotBlank(dealSubTitleJson)) {
            List<String> dealSubTitles = JsonUtils.fromJson(dealSubTitleJson, new TypeReference<List<String>>() {});
            if (dealSubTitles != null) {
                if (dealSubTitles.contains(null)) {
                    Cat.logEvent("NullPointer", "dealSubTitles contain null");
                }
                dealTinyInfoVO.setSubTitle(Joiner.on("·").join(dealSubTitles.stream().filter(Objects::nonNull).collect(Collectors.toList())));
            } else {
                Cat.logEvent("NullPointer", "dealSubTitles is null");
            }
        }
        dealTinyInfoVO.setSkuId(skuId);
        // 价格力标签
        String pricePowerTag = getDealProductAttrValue(attrs, "highestPriorityPricePowerTagAttr");
        dealTinyInfoVO.setPricePowerTag(StringUtils.isBlank(pricePowerTag) ? null : Collections.singletonList(pricePowerTag));
        // 提单页
        if (Objects.equals(pageSource, PAGE_SOURCE_SHOP_CAR_SELECT)
                || Objects.equals(pageSource, PAGE_SOURCE_SHOP_CAR_NO_SELECT)) {
            dealTinyInfoVO.setDirectBuyJumpUrl(getDealProductAttrValue(attrs, "dzShopCarOrderUrl"));
        } else {
            dealTinyInfoVO.setDirectBuyJumpUrl(dealProductDTO.getOrderUrl());
        }
        // 到手价
        if (Objects.equals(pageSource, PAGE_SOURCE_SHOP_CAR_SELECT)) {
            String finalPriceForTradeStr = getDealProductAttrValue(attrs, "finalPriceForTrade");
            String marketPriceForTradeStr = getDealProductAttrValue(attrs, "marketPriceForTrade");
            BigDecimal finalPriceForTrade = new BigDecimal(finalPriceForTradeStr);
            BigDecimal marketPriceForTrade = new BigDecimal(marketPriceForTradeStr);
            dealTinyInfoVO.setDiscount(getDealProductAttrValue(attrs, "discountForTrade"));
            dealTinyInfoVO.setFinalPrice(finalPriceForTrade.stripTrailingZeros().toPlainString());
            dealTinyInfoVO.setMarketPrice(marketPriceForTrade.stripTrailingZeros().toPlainString());
        } else {
            // 团详场景 or 购物车场景且未被选中
            BigDecimal finalPrice = Optional.ofNullable(dealProductDTO.getPromoPrice()).orElse(BigDecimal.ZERO);
            BigDecimal marketPrice = new BigDecimal(
                    Optional.ofNullable(dealProductDTO.getMarketPriceTag()).orElse("0"));
            String discount = StringUtils.EMPTY;
            if (finalPrice != null) {
                dealTinyInfoVO.setFinalPrice(finalPrice.stripTrailingZeros().toPlainString());
                BigDecimal discountDecimal = calcDiscount(finalPrice, marketPrice);
                if (Objects.nonNull(discountDecimal)) { discount = discountDecimal.toPlainString(); }
            }
            dealTinyInfoVO.setMarketPrice(marketPrice.stripTrailingZeros().toPlainString());
            dealTinyInfoVO.setDiscount(StringUtils.isBlank(discount) ? StringUtils.EMPTY : discount + "折");
        }
        if (RequestSourceEnum.fromBeautyBuyingList(pageSource)) {
            dealTinyInfoVO.setBtnText("再次购买");
        }
        // 团单二级类目
        dealTinyInfoVO.setCategoryId(dealProductDTO.getCategoryId());
        return dealTinyInfoVO;
    }
    /**
     * 计算折扣(保留1位)四舍五入，即 9.9 折
     * @param finalPrice 优惠后价
     * @param marketPrice  优惠前价
     * @return 折扣
     */
    private BigDecimal calcDiscount(BigDecimal finalPrice, BigDecimal marketPrice) {
        if (finalPrice == null || marketPrice == null || marketPrice.compareTo(BigDecimal.ZERO) == 0 || finalPrice.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        return finalPrice.divide(marketPrice, 3, RoundingMode.DOWN)
                .multiply(new BigDecimal(10)).setScale(1, RoundingMode.HALF_UP);
    }

    private String getDealProductAttrValue(List<DealProductAttrDTO> attrs, String name) {
        if (CollectionUtils.isEmpty(attrs)) {
            return StringUtils.EMPTY;
        }
        DealProductAttrDTO attrDTO = attrs.stream().filter(Objects::nonNull)
                .filter(attr -> Objects.equals(attr.getName(), name)).findFirst().orElse(null);
        return attrDTO == null ? StringUtils.EMPTY : attrDTO.getValue();
    }

    private void checkRequest(GetDealTinyInfoRequest req) {
        Validate.isTrue(StringUtils.isNotBlank(req.getDealGroupId()) && Integer.parseInt(req.getDealGroupId()) > 0);
        Validate.isTrue(StringUtils.isNotBlank(req.getPageSource()));
    }

    private DealProductRequest buildQueryDealProductRequest(EnvCtx ctx, int dealId, long shopId,
                                                            GetDealTinyInfoRequest request, int skuId) {
        DealProductRequestParam params = DealProductRequestParam.builder()
                .skuId(skuId)
                .shopId(shopId)
                .planId(DZ_TINY_INFO_DEAL_PLAN_ID)
                .dealId(dealId)
                .pageSource(request.getPageSource())
                .ctx(ctx)
                .needPriceTrend(false)
                .cityId(request.getCityId())
                .lat(request.getLat())
                .lng(request.getLng())
                .build();
        if (Objects.equals(request.getPageSource(), PAGE_SOURCE_SHOP_CAR_SELECT)) {
            params.setPlanId(DZ_TINY_INFO_SHOP_CAR_SELECT_PLAN_ID);
        }
        return buildRequest(params);
    }


    private DealProductRequest buildRequest(DealProductRequestParam params) {
        DealProductRequest request = new DealProductRequest();
        List<Integer> dealIds = Collections.singletonList(params.getDealId());
        request.setProductIds(dealIds);
        request.setPlanId(params.getPlanId());
        // 扩展参数
        EnvCtx ctx = params.getCtx();
        Map<String, Object> extParams = Maps.newHashMap();
        extParams.put("dealIds", dealIds);
        extParams.put("clientType", ctx.isMt() ? VCClientTypeEnum.MT_APP.getCode() : VCClientTypeEnum.DP_APP.getCode());
        extParams.put("platform", ctx.isMt() ? VCPlatformEnum.MT.getType() : VCPlatformEnum.DP.getType());
        extParams.put("deviceId", ctx.getAppDeviceId());
        extParams.put("unionId", ctx.getUnionId());
        String shopUuid = ctx.isMt() ? StringUtils.EMPTY : ShopUuidUtils.getUuidById(params.getShopId());
        extParams.put("shopUuid", shopUuid);
        extParams.put("userId", ctx.isMt() ? ctx.getMtUserId() : ctx.getDpUserId());//已确认判断平台后再使用
        extParams.put("shopId", (int)params.getShopId());
        extParams.put("shopIdForLong", params.getShopId());
        extParams.put("appVersion", ctx.getVersion());
        Map<Integer, Integer> dealId2ShopIdMap = Maps.newHashMap();
        Map<Integer, Long> dealId2ShopIdForLong = Maps.newHashMap();
        dealId2ShopIdMap.put(params.getDealId(), (int)params.getShopId());
        dealId2ShopIdForLong.put(params.getDealId(), params.getShopId());
        extParams.put("dealId2ShopId", dealId2ShopIdMap);
        extParams.put("dealId2ShopIdForLong",dealId2ShopIdForLong);
        Map<Integer, Integer> dealId2SkuIdMap = Maps.newHashMap();
        dealId2SkuIdMap.put(params.getDealId(), params.getSkuId());
        extParams.put("dealId2SkuId", dealId2SkuIdMap);
        extParams.put("scene", "comparePopup");
        extParams.put("needPriceTrend", params.isNeedPriceTrend());
        extParams.put("cityId", Objects.nonNull(params.getCityId()) ? params.getCityId() : 0);
        extParams.put("lat", Objects.nonNull(params.getLat()) ? params.getLat() : 0.0d);
        extParams.put("lng", Objects.nonNull(params.getLng()) ? params.getLng() : 0.0d);
        extParams.put("coordType", "GCJ02");
        // 查询团详提单页需要此参数，与方法入参pageSource无关
        extParams.put("pageSource", CreateOrderPageSourceEnum.DEAL_GROUP_DETAIL.getType());
        if (Objects.equals(params.getPageSource(), PAGE_SOURCE_DEAL)) {
            extParams.put("directPromoSceneCode", RequestSceneEnum.PROMO_DETAIL_DESC_WithDealPromo.getScene());
        } else if (Objects.equals(params.getPageSource(), PAGE_SOURCE_SHOP_CAR_NO_SELECT)) {
            extParams.put("directPromoSceneCode", RequestSceneEnum.BUY_CAR_SINGLE_PRODUCT.getScene());
        }
        request.setExtParams(extParams);
        return request;
    }

    @Data
    @Builder
    private static class DealProductRequestParam {
        private EnvCtx ctx;
        private int dealId;
        private long shopId;
        /**
         * 页面请求来源，团详/购物车（选中/未选中）
         */
        private String pageSource;
        private String planId;
        private int skuId;
        /**
         * 是否需要查询价格趋势
         */
        private boolean needPriceTrend;
        /**
         * 用户定位城市
         */
        private Integer cityId;
        /**
         * 用户定位纬度
         */
        private Double lat;
        /**
         * 用户定位经度
         */
        private Double lng;
    }
}
