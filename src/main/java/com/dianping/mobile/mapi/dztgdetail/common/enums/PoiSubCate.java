package com.dianping.mobile.mapi.dztgdetail.common.enums;

import lombok.Getter;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/6/17.
 */
@Getter
public enum PoiSubCate {
    BEAUTYNAIL(75,"beauty_nail"),
    BEAUTYHAIR(74,"beauty_hair");
    private int value;
    private String name;

    PoiSubCate(int value,String name) {
        this.value = value;
        this.name = name;
    }
}