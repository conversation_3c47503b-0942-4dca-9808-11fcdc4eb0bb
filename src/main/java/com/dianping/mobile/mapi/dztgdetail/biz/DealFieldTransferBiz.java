package com.dianping.mobile.mapi.dztgdetail.biz;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Cons;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealFields;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DealHotelAttrEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DealShowTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MtTerm;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.*;
import com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam;
import com.dianping.mobile.mapi.dztgdetail.helper.DealFieldHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.DealFieldTransferHelper;
import com.dianping.mobile.mapi.dztgdetail.util.ImageUtils;
import com.google.common.collect.Lists;
import com.meituan.dataapp.poi.helper.VersionHelper;
import com.meituan.service.mobile.prometheus.model.DealModel;
import com.meituan.service.mobile.prometheus.utils.PropertyUtil;
import com.meituan.service.mobile.sinai.client.model.PoiModelL;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * Created by guozhengyao on 16/4/1.
 */
@Component
@Slf4j
public class DealFieldTransferBiz {

    public MtDealModel transferMsg2Json(DealModel dealModel, Set<String> set, MtCommonParam mtCommonParam) {
        MtDealModel mtDealModel = new MtDealModel();
        if (set.contains(DealFields.DEAL_FIELD_ID)) {
            mtDealModel.setId(dealModel.getDid());
        }
        if (set.contains(DealFields.DEAL_FIELD_SLUG)) {
            mtDealModel.setSlug(dealModel.getSlug());
        }
        if (set.contains(DealFields.DEAL_FIELD_DT)) {
            mtDealModel.setDt(dealModel.getDt());
        }
        if (set.contains(DealFields.DEAL_FIELD_DTYPE)) {
            mtDealModel.setLightning(dealModel.getDtype() == 1);
        }
        if (set.contains(DealFields.DEAL_FIELD_CTYPE)) {
            mtDealModel.setDealType(dealModel.getCtype());
        }
        if (set.contains(DealFields.DEAL_FIELD_SOLDS)) {
            mtDealModel.setSolds(dealModel.getSoldQuantity());
            mtDealModel.setSoldStr("已售" + dealModel.getSoldQuantity());
        }
        if (set.contains(DealFields.DEAL_FIELD_STATUS)) {
            mtDealModel.setStatus(dealModel.getStatus());
        }
        // 注意starttime、endtime，最开始定义成start、end，但与thrift关键字冲突
        // 为兼容旧版，多个属性名标示一个意义
        if (set.contains(DealFields.DEAL_FIELD_START)) {
            mtDealModel.setStart(new Date(dealModel.getStart() * 1000));
        }
        if (set.contains(DealFields.DEAL_FIELD_STARTTIME)) {
            mtDealModel.setStart(new Date(dealModel.getStart() * 1000));
        }
        if (set.contains(DealFields.DEAL_FIELD_END)) {
            mtDealModel.setEnd(new Date(dealModel.getEnd() * 1000));
        }
        if (set.contains(DealFields.DEAL_FIELD_ENDTIME)) {
            mtDealModel.setEnd(new Date(dealModel.getEnd() * 1000));
        }
        if (set.contains(DealFields.DEAL_FIELD_PRICE)) {
            mtDealModel.setPrice(dealModel.getPrice());
        }
        if (set.contains(DealFields.DEAL_FIELD_VALUE)) {
            mtDealModel.setOriginalPrice(dealModel.getValue());
        }
        if (set.contains(DealFields.DEAL_FIELD_PRICECALENDAR)) {
            mtDealModel.setPriceCalendars(DealFieldHelper.getPriceCalendarFromJson(dealModel.getPricecalendar()));
        }
        //
        MtRatingModel ratingModel = new MtRatingModel();
        if (set.contains(DealFields.DEAL_FIELD_RATING)) {
            ratingModel.setRating(dealModel.getRatingModel().getRating());
        }
        if (set.contains(DealFields.DEAL_FIELD_RATE_COUNT)) {
            ratingModel.setRateCount(dealModel.getRatingModel().getRateCount());
        }
        if (set.contains(DealFields.DEAL_FIELD_SATISFACTION)) {
            ratingModel.setSatisfaction(dealModel.getRatingModel().getSatisfaction());
        }
        mtDealModel.setRatingModel(ratingModel);
        //
        if (set.contains(DealFields.DEAL_FIELD_TITLE)) {
            mtDealModel.setTitle(dealModel.getTitle());
        }
        if (set.contains(DealFields.DEAL_FIELD_MTITLE)) {
            mtDealModel.setMtitle(dealModel.getMtitle());
        }
        if (set.contains(DealFields.DEAL_FIELD_SMSTITLE)) {
            mtDealModel.setSmstitle(dealModel.getSmstitle());
        }
        if (set.contains(DealFields.DEAL_FIELD_IMGURL)) {
            mtDealModel.setImgurl(ImageUtils.getFullImageUrl(dealModel.getImageUrl()));
        }
        if (set.contains(DealFields.DEAL_FIELD_SQUAREIMGURL)) {
            mtDealModel.setSquareimgurl(ImageUtils.getFullImageUrl(
                    dealModel.getSquareImageUrl() == null ? dealModel.getImageUrl() : dealModel.getSquareImageUrl()));
        }
        if (set.contains(DealFields.DEAL_FIELD_BRANDNAME)) {
            mtDealModel.setBrandName(dealModel.getBrandName());
        }
        if (set.contains(DealFields.DEAL_FIELD_MURL)) {
            mtDealModel.setMurl(dealModel.getMerchantWebsiteUrl());
        }
        if (set.contains(DealFields.DEAL_FIELD_RANGE)) {
            mtDealModel.setRange(dealModel.getRange());
        }
        if (set.contains(DealFields.DEAL_FIELD_NOBOOKING)) {
            mtDealModel.setNobooking(dealModel.getNobooking());
        }
        if (set.contains(DealFields.DEAL_FIELD_MEALCOUNT)) {
            mtDealModel.setMealcount(dealModel.getMealcount());
        }
        if (set.contains(DealFields.DEAL_FIELD_TIPS)) {
            mtDealModel.setTips(dealModel.getTips());
        }
        if (set.contains(DealFields.DEAL_FIELD_TERMS)) {
            try {
                mtDealModel.setTerms(spliceTerms(dealModel).toString());
            } catch (JSONException e) {
                log.error("DealFieldTransferBiz.spliceTerms error", e);
            }
        }
        if (set.contains(DealFields.DEAL_FIELD_REFUND)) {
            mtDealModel.setRefund(dealModel.getRefundModel().getRefund());
        }
        if (set.contains(DealFields.DEAL_FIELD_USAGE)) {
            mtDealModel.setHowuse(dealModel.getHowuse());
        }
        if (set.contains(DealFields.DEAL_FIELD_VOICE)) {
            mtDealModel.setNotice(dealModel.getVoice());
        }
        if (set.contains(DealFields.DEAL_FIELD_PITCHHTML)) {
            mtDealModel.setPitchHtml(dealModel.getPitchHtml());
        }
        if (set.contains(DealFields.DEAL_FIELD_APPLELOTTERY)) {
            mtDealModel.setAppleLottery(dealModel.getAppleLottery());
        }
        if (set.contains(DealFields.DEAL_FIELD_STATE)) {
            mtDealModel.setState(dealModel.getState());
        }
        if (set.contains(DealFields.DEAL_FIELD_POIIDS)) {
            mtDealModel.setPoiids(Collections.emptyList());
            mtDealModel.setPoiIdsStr(Collections.emptyList());
        }
        if (set.contains(DealFields.DEAL_FIELD_TAG)) {
            mtDealModel.setTag(dealModel.getTag());
        }
        if (set.contains(DealFields.DEAL_FIELD_SHOWTYPE)) {
            mtDealModel.setShowType(genShowType(dealModel));
        }
        if (set.contains(DealFields.DEAL_FIELD_DEPOSIT)) {
            mtDealModel.setDeposit(dealModel.getDeposit());
        }
        if (set.contains(DealFields.DEAL_FIELD_SECURITYINFO)) {
            mtDealModel.setSecurityInfoItems(genSecurityInfo(dealModel));
        }
        if (set.contains(DealFields.DEAL_FIELD_COUPONBEGINTIME)) {
            mtDealModel.setCouponBeginTime(new Date(dealModel.getCouponBeginTime() * 1000));
        }
        if (set.contains(DealFields.DEAL_FIELD_COUPONENDTIME)) {
            mtDealModel.setCouponEndTime(new Date(dealModel.getCouponEndTime() * 1000));
        }
        // deal属性信息（attrs相关）
        Map<Integer, String> attrsMap = (dealModel.getAttrs() == null ? new HashMap<>() : dealModel.getAttrs());
        if (set.contains(DealFields.DEAL_FIELD_ATTRJSON)) {
            mtDealModel.setIcons(generateIconWall(attrsMap));
        }
        if (set.contains(DealFields.DEAL_FIELD_KTV)) {
            mtDealModel.setKtvPlan(attrsMap.get(678));
        }
        if (set.contains(DealFields.DEAL_FIELD_BOOKINGPHONE)) {
            mtDealModel.setBookingphone(genBookingPhone(attrsMap.get(552)));
        }
        if (set.contains(DealFields.DEAL_FIELD_BOOKINGINFO)) {
            mtDealModel.setBookinginfo(genBookingInfo(attrsMap.get(Cons.ATTRS_BOOKING_INFO)));
        }
        if (set.contains(DealFields.DEAL_FIELD_HOTELROOMNAME)) {
            mtDealModel.setHotelroomname(DealFieldTransferHelper.genHotelRoomName(dealModel));
        }
        if (isGTYDeal(attrsMap) && VersionHelper.compare(mtCommonParam.getVersion(), "6.2.1") >= 0) {
            mtDealModel.setIUrl("imeituan://www.meituan.com/travel/packagetour/deal?dealid=" + dealModel.getDid());
        } else if ("Y".equals(attrsMap.get(Cons.ATTRS_TRAVEL_JJ_INFO)) && VersionHelper.compare(mtCommonParam.getVersion(), "6.5") >= 0) {
            mtDealModel.setIUrl("imeituan://www.meituan.com/hotel/packagedeal?dealid=" + dealModel.getDid());
        }
        //购物 deal跳转 http://task.sankuai.com/browse/MBK-1770
        List<Integer> frontCateslist = dealModel.getFrontPoiCates();
        List<Integer> cateslist = dealModel.getCates();
        if ((cateslist != null && cateslist.contains(4)) || (frontCateslist != null && frontCateslist.contains(4))) {
            mtDealModel.setIUrl("imeituan://www.meituan.com/web?url=https://i.meituan.com/shopping/deal/" + dealModel.getDid() + ".html");
        }
        if (set.contains(DealFields.DEAL_FIELD_OPTIONALATTRS)) {
            //JSONObject object = new JSONObject();
            Map<Integer, String> map = new HashMap<Integer, String>();
            // 添加是否支持在线预约
            List<MtIcon> iconWall = generateIconWall(attrsMap);
            if (iconWall.size() > 0) {
                for (MtIcon each : iconWall) {
                    int key = each.getKey();
                    if (33 == key) {
                        map.put(key, String.valueOf(each.getStatus()));
                    }
                }
            }
            // 添加其他deal属性
            if (CollectionUtils.isNotEmpty(attrsMap.keySet())) {
                for (Integer attrId : attrsMap.keySet()) {
                    String val = attrsMap.get(attrId).trim();
                    // 2：住宿时间类型；13：是否代金券；11:deal不可用日期;999880 酒店大客标签 38:住宿类型;999890酒店团购直连信息
                    if (StringUtils.isNotBlank(val) && Cons.DEAL_OPTION_ATTRS.contains(attrId)) {
                        map.put(attrId, attrsMap.get(attrId));
                    }
                    if (StringUtils.isNotBlank(val) && attrId == Cons.ATTRS_DEAL_NOT_VALID) {
                        mtDealModel.setAvailableToday(getAvailability(val));
                    }
                }
            }
            mtDealModel.setOptionalAttrs(com.alibaba.fastjson.JSONObject.toJSONString(map));
            mtDealModel.setMtOptionalAttrs(genMtOptionalAttrs(map));
        }
        if (set.contains(DealFields.DEAL_FIELD_DIGESTION)) {
            mtDealModel.setMenuDigest(dealModel.getMenuDigest());
        }
        if (set.contains(DealFields.DEAL_FIELD_ISAPPOINTONLINE)) {
            mtDealModel.setIsappointonline(isAppointOnline(dealModel));
        }
        if (set.contains(DealFields.DEAL_FIELD_FRONT_POI_CATES)) {
            if (dealModel.getFrontPoiCates() != null) {
                mtDealModel.setFrontPoiCates(dealModel.getFrontPoiCates());
            }
        }
        if (set.contains(DealFields.DEAL_FILED_CHANNEL)) {
            mtDealModel.setChannel(dealModel.getChannel());
        }
        return mtDealModel;
    }

    public MtDealModel transferMsg2JsonObject(DealModel deal, Set<String> fset, List<PoiModelL> poiModels, MtCommonParam mtCommonParam) {
        // --- deal相关属性 ---
        MtDealModel mtDealModel = transferMsg2Json(deal, fset, mtCommonParam);

        // --- poi相关属性 ---
        if (fset.contains(DealFields.DEAL_FIELD_RDCOUNT)) {
            mtDealModel.setRdcount(poiModels.size());
        }
        if (fset.contains(DealFields.DEAL_FIELD_MLLS)) {
            mtDealModel.setBranchLocations(genBranchLocations(deal, poiModels, mtCommonParam.getCityId()));
        }
        return mtDealModel;
    }

    private static final String DEFAULT_MLLS = "0.0,0.0";

    private String genBranchLocations(DealModel dealModel, List<PoiModelL> poiModels, int cityId) {
        StringBuilder sb = new StringBuilder();
        if (CollectionUtils.isNotEmpty(poiModels)) {
            int len = poiModels.size();
            if (len >= 100) {
                len = 100;
            }
            for (int i = 0; i < poiModels.size(); i++) {
                PoiModelL one = poiModels.get(i);
                sb.append(one.getLatitude()).append(Cons.STR_SEPARATOR_COMMA);
                sb.append(one.getLongitude());
                if (i != len - 1) {
                    sb.append(Cons.STR_SEPARATOR_SEMICOLON);
                }
            }
        } else {
            sb.append(DEFAULT_MLLS);
        }
        return sb.toString();
    }

    private MtOptionalAttrs genMtOptionalAttrs(Map<Integer, String> map) {
        MtOptionalAttrs optionalAttrs = new MtOptionalAttrs();
        optionalAttrs.setKey575(map.get(575));
        optionalAttrs.setKey972(map.get(972));
        optionalAttrs.setKey999887(map.get(999887));
        optionalAttrs.setKey11070001(map.get(11070001));
        optionalAttrs.setKey11020003(map.get(11020003));
        optionalAttrs.setKey11020004(map.get(11020004));
        return optionalAttrs;
    }

    private boolean isAppointOnline(DealModel dealModel) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.DealFieldTransferBiz.isAppointOnline(com.meituan.service.mobile.prometheus.model.DealModel)");
        Map<Integer, String> attrs = dealModel.getAttrs();
        if (attrs == null) {
            return false;
        }
        String value = attrs.get(Cons.ATTRS_ONLINE_ORDER);
        return value != null && !"N".equals(value);
    }

    private static List<MtIcon> generateIconWall(Map<Integer, String> attrMap) {
        List<DealHotelAttrEnum> attrList = new ArrayList<>();
        for (DealHotelAttrEnum hotelAttr : DealHotelAttrEnum.values()) {
            Integer key = hotelAttr.getAttrId();
            if (attrMap.containsKey(key)) {
                String value = attrMap.get(key);
                if (hotelAttr.getCode().equals(value)) {
                    attrList.add(hotelAttr);
                }
            }
        }
        Collections.sort(attrList, (o1, o2) -> {
            if (o1.getStatus() == o2.getStatus()) {
                return o1.ordinal() - o2.ordinal();
            } else {
                return o2.getStatus() - o1.getStatus();
            }
        });
        List<MtIcon> icons = Lists.newArrayList();
        for (DealHotelAttrEnum attrEnum : attrList) {
            MtIcon icon = new MtIcon();
            icon.setIconName(attrEnum.getText());
            icon.setStatus(attrEnum.getStatus());
            icon.setKey(attrEnum.getAttrId());
            icons.add(icon);
        }
        return icons;
    }

    //copied from groupapi
    //合并套餐模块中的适用范围到使用须知
    private List<MtTerm> spliceTerms(DealModel dealModel) throws JSONException {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.DealFieldTransferBiz.spliceTerms(com.meituan.service.mobile.prometheus.model.DealModel)");
        List<MtTerm> mtTerms = DealFieldHelper.genMtTermListFromTermStr(dealModel.getTerms());
        List<MtTerm> result = Lists.newArrayList();
        Map<Integer, String> attrs = dealModel.getAttrs();

        if (attrs != null && attrs.containsKey(Cons.ATTRS_IS_COUPON) && "1".equals(attrs.get(Cons.ATTRS_IS_COUPON))) {
            String menu = dealModel.getMenu();
            if (menu != null && !menu.isEmpty()) {
                try {
                    if (menu.startsWith("[[")) {
                        menu = menu.substring(1, menu.length() - 1);
                    }
                    JSONArray ja = new JSONArray(menu);

                    for (int i = 0; i < ja.length(); ++i) {
                        JSONObject contentJo = ja.getJSONObject(i);
                        String content = contentJo.has("content") ? contentJo.getString("content") : null;
                        if (content != null && !content.isEmpty() && content.contains("适用范围：")) {
                            MtTerm mtTerm = new MtTerm();
                            mtTerm.setTitle("适用范围");

                            // 有的数据  "适用范围：仅限菜品\r\n店内人均消费参考价格：40元"
                            String[] strings = content.split("\r|\n|\t");
                            for (String s : strings) {
                                if (!com.google.common.base.Strings.isNullOrEmpty(s) &&
                                        s.contains("适用范围：")) {
                                    content = s;
                                    break;
                                }
                            }
                            mtTerm.setContents(getContentsWithString("[\"" + content.replaceAll("适用范围：", "") + "\"]"));
                            result.add(mtTerm);
                            break;
                        }
                    }
                } catch (JSONException e) {
                    log.error(e.getMessage() + " did=" + dealModel.getDid() + " menu=" + menu, e);
                    return mtTerms;
                }
            }
        }
        result.addAll(mtTerms);
        return result;
    }

    private List<String> getContentsWithString(String contents) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.DealFieldTransferBiz.getContentsWithString(java.lang.String)");
        if (StringUtils.isBlank(contents)) {
            return null;
        }
        List<String> list = Lists.newArrayList();
        try {
            JSONArray jsonContent = new JSONArray(contents);
            for (int j = 0; j < jsonContent.length(); j++) {
                list.add((String) jsonContent.get(j));
            }
            return list;
        } catch (Exception e) {
            log.error("getContentsWithString has failed:" + contents, e);
        }
        return null;
    }

    private String genShowType(DealModel dealModel) {
        long properties = dealModel.getProperties();
        if (PropertyUtil.isTrue(properties, PropertyUtil.BIT_ISNEWWEDINGDEAL)) {
            return DealShowTypeEnum.WEDDING.getName();
        }
        //如果是品类包含20，showtype为酒店
        if (dealModel.getCates() != null && dealModel.getCates().contains(20)) {
            return DealShowTypeEnum.HOTEL.getName();
        }
        return DealShowTypeEnum.NORMAL.getName();
    }

    private List<MtSecurityInfoItem> genSecurityInfo(DealModel dealModel) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.biz.DealFieldTransferBiz.genSecurityInfo(com.meituan.service.mobile.prometheus.model.DealModel)");
        long properties = dealModel.getProperties();
        List<MtSecurityInfoItem> infoItems = Lists.newArrayList();
        if (PropertyUtil.isTrue(properties, PropertyUtil.BIT_ISNEWWEDINGDEAL)) {
            infoItems.add(genSecurityInfoItem(1, "2w保证金",
                    "2w保证金",
                    "精选商家均缴纳20000元，不满意全额退款"));
            infoItems.add(genSecurityInfoItem(2, "独家优惠",
                    "独家优惠",
                    "美团帮你砍价，比店内优惠还优惠"));
            infoItems.add(genSecurityInfoItem(3, "全程保障",
                    "全程保障",
                    "从咨询到取片，美团客服全程跟踪质量"));
        }
        return infoItems;
    }

    private MtSecurityInfoItem genSecurityInfoItem(int id, String name, String alias, String desc) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.DealFieldTransferBiz.genSecurityInfoItem(int,java.lang.String,java.lang.String,java.lang.String)");
        MtSecurityInfoItem infoItem = new MtSecurityInfoItem();
        infoItem.setId(id);
        infoItem.setName(name);
        infoItem.setAlias(alias);
        infoItem.setDesc(desc);
        return infoItem;
    }

    private String genBookingPhone(String attrJson) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.DealFieldTransferBiz.genBookingPhone(java.lang.String)");
        if (StringUtils.isBlank(attrJson)) {
            return StringUtils.EMPTY;
        }
        String bookingPhone = StringUtils.EMPTY;
        try {
            JSONObject jo = new JSONObject(attrJson).getJSONObject("label");
            if (null != jo) {
                String value = jo.optString("hotelPhoneNum", "");
                if (StringUtils.isNotBlank(value)) {
                    bookingPhone = value;
                }
            }
        } catch (JSONException e) {
            log.error("genBookingPhone() parsing error. json=" + attrJson, e);
        }
        return bookingPhone;
    }

    // http://task.sankuai.com/browse/MB-15857
    private String genBookingInfo(String attrJson) {
        if (StringUtils.isBlank(attrJson)) {
            return StringUtils.EMPTY;
        }
        String bookingInfo = StringUtils.EMPTY;
        try {
            String need = new JSONObject(attrJson).optString("need");
            String appointHour = new JSONObject(attrJson).optString("appointHour");
            String other = new JSONObject(attrJson).optString("other");
            if (StringUtils.isNotBlank(need)) {
                if ("1".equals(need) && StringUtils.isNotBlank(appointHour)) {
                    bookingInfo = "请提前" + appointHour + "小时预约";
                } else if ("2".equals(need) && StringUtils.isNotBlank(other)) {
                    bookingInfo = other;
                }
            }
        } catch (JSONException e) {
            log.error("genBookingInfo() parsing error. json=" + attrJson, e);
        }
        return bookingInfo;
    }

    //判断是不是跟团游的单子
    private boolean isGTYDeal(Map<Integer, String> attr) {
        boolean isgty = false;
        try {
            String key = attr.get(Cons.ATTRS_TRAVEL_PRODUCT_TYPE);
            if (key != null && key.length() > 0) {
                JSONObject attrjo = new JSONObject(key);
                if ("GTY".equals(attrjo.optString("key")) && "Y".equals(attr.get(Cons.ATTRS_TRAVEL_GTY_INFO))) {
                    isgty = true;
                }
            }
        } catch (JSONException e) {
            log.error("isGTYDeal error!", e);
        }
        return isgty;
    }

    //http://wiki.sankuai.com/pages/viewpage.action?pageId=*********
    private boolean getAvailability(String attr) {
        return DealFieldTransferHelper.isAvailableToday(attr, new Date());
    }

}
