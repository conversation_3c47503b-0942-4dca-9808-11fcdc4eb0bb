package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.healthExamination;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/18 11:10 上午
 */
@MobileDo(id = 0x61f6)
public class NumProcessSubitemVO implements Serializable {
    /**
     * 电话号码列表
     */
    @MobileDo.MobileField(key = 0xc59e)
    private List<String> nums;

    /**
     * 模块名，如：预约电话
     */
    @MobileDo.MobileField(key = 0xee12)
    private String itemName;

    public List<String> getNums() {
        return nums;
    }

    public void setNums(List<String> nums) {
        this.nums = nums;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }
}
