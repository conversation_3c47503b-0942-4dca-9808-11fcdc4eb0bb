package com.dianping.mobile.mapi.dztgdetail.biz;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiClientWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.PoiConsts;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.MtPoiModel;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.ViewItemDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.ViewListDo;
import com.dianping.mobile.mapi.dztgdetail.entity.DealBranchesParam;
import com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam;
import com.dianping.mobile.mapi.dztgdetail.helper.PoiModelHelper;
import com.meituan.service.mobile.sinai.client.model.PoiModelL;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Description:
 * Author: lijie
 * Version: 0.0.1
 * Created: 16/5/18 下午7:36
 */
@Component
@Slf4j
public class PoiDetailBizStrategy {
    @Resource
    private PoiClientWrapper poiClientWrapper;
    @Resource
    private BrandInfoBiz brandInfoBiz;

    public ViewListDo getMtPoiList(long poiId, boolean isOnlyCurCityPois, MtCommonParam mtCommonParam) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.PoiDetailBizStrategy.getMtPoiList(long,boolean,com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam)");
        List<PoiModelL> poiModels = poiClientWrapper.listPoiL(Arrays.asList(poiId), PoiConsts.defaultPoiFields);
        ViewListDo viewListDo = new ViewListDo();
        try {
            if (CollectionUtils.isEmpty(poiModels)) {
                log.error("poiDetail getMtPoiList poiClient.listPois is empty");
                return viewListDo;
            }
            PoiModelL poiModel = poiModels.get(0);
            int brandId = poiModel.getPoiBrandId() == null ? 0 : poiModel.getPoiBrandId();
            if (brandId > 0 && mtCommonParam.getCityId() > 0) {
                DealBranchesParam dealBranchesParam = new DealBranchesParam()
                        .buildParam(mtCommonParam, mtCommonParam.getLimit(), mtCommonParam.getOffset(), isOnlyCurCityPois, StringUtils.EMPTY);
                List<ViewItemDo> viewItemDos = new ArrayList<>();
                List<PoiModelL> poiModelsByBrandId = brandInfoBiz.getPoiModelsByBrandId(mtCommonParam.getCityId(), brandId, dealBranchesParam, viewListDo);
                if (!poiModelsByBrandId.isEmpty()) {
                    for (PoiModelL model : poiModelsByBrandId) {
                        MtPoiModel mtPoiModel = PoiModelHelper.convertPoiModel(model);
                        ViewItemDo viewItemDo = new ViewItemDo();
                        viewItemDo.setMtShop(mtPoiModel);
                        viewItemDos.add(viewItemDo);
                    }
                    viewListDo.setStartIndex(mtCommonParam.getOffset());
                    if (mtCommonParam.getOffset() + mtCommonParam.getLimit() < viewListDo.getRecordCount()) {
                        viewListDo.setIsEnd(false);
                    }
                    viewListDo.setList(viewItemDos);
                } else {
                    log.info("poiModelsByBrandId is empty, brandId: {}", brandId);
                }
            } else {
                log.error("brandId illegal, brandId: {}", brandId);
            }
        } catch (Exception e) {
            Cat.getProducer().logError("poiClientWrapper.listPois has error", e);
            log.error("poiDetail getMtPoiList poiClient.listPois has error", e);
        }
        return viewListDo;
    }
}
