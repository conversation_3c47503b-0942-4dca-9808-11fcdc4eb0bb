package com.dianping.mobile.mapi.dztgdetail.biz.processor.imagetext.strategy;

import com.dianping.mobile.mapi.dztgdetail.common.enums.ImageTextStrategyEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ContentDetailPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ImageTextDetailPBO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-12-11
 * @desc 图文详情处理策略
 */
public interface ImageTextDetailStrategy {
    /**
     * 图文详情展示策略名称
     * @return {@link ImageTextStrategyEnum}
     */
    ImageTextStrategyEnum getStrategyName();

    /**
     * 获取图文详情内容
     * @param dealGroupDTO 团单信息
     * @param contents 团单内容
     * @return {@link ContentDetailPBO}
     */
    ContentDetailPBO getContentDetail(DealGroupDTO dealGroupDTO, List<ContentPBO> contents, int threshold);

    /**
     * 构建图文详情
     * @param dealGroupDTO 团单信息
     * @param contents 团单内容
     * @param envCtx 环境上下文
     * @return {@link ImageTextDetailPBO}
     */
    ImageTextDetailPBO buildImageTextDetail(DealGroupDTO dealGroupDTO, List<ContentPBO> contents, int threshold, EnvCtx envCtx);
}
