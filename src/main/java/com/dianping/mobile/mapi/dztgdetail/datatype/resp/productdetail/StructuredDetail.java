package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * 移动之家：https://mobile.sankuai.com/studio/model/info/41235
 */
@MobileDo(id = 0xcaae)
@Data
public class StructuredDetail implements Serializable {

    /**
     * 行数据标识
     */
    @MobileDo.MobileField(key = 0xb231)
    private String itemId;

    /**
     * content颜色
     */
    @MobileDo.MobileField(key = 0xde6d)
    private String contentColor;

    /**
     * 信息前缀 https://mobile.sankuai.com/studio/model/edit/41235
     */
    @MobileDo.MobileField(key = 0x7706)
    private Integer prefix;

    /**
     * 跳转链接
     */
    @MobileDo.MobileField(key = 0x774e)
    private String jumpUrl;

    /**
     * 弹窗浮层数据
     */
    @MobileDo.MobileField(key = 0x4548)
    private String popupData;

    /**
     * 渐变背景色-终色
     */
    @MobileField(key = 0x90aa)
    private String endBackgroundColor;

    /**
     * 序号
     */
    @MobileField(key = 0x811f)
    private int order;

    /**
     * 副内容
     */
    @MobileField(key = 0x667e)
    private String subContent;

    /**
     * 图标地址
     */
    @MobileField(key = 0x3c48)
    private String icon;

    /**
     * 背景颜色
     */
    @MobileField(key = 0xba62)
    private String backgroundColor;

    /**
     * 详情
     */
    @MobileField(key = 0xa83b)
    private String detail;

    /**
     * 内容
     */
    @MobileField(key = 0xcce)
    private String content;

    /**
     * 标题
     */
    @MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "标题字体加粗")
    @MobileDo.MobileField(key = 0xc2e5)
    private String titleFontWeight;

    /**
     * 对应前端展示类型，目前1-5
     */
    @MobileField(key = 0x8f0c)
    private int type;

}