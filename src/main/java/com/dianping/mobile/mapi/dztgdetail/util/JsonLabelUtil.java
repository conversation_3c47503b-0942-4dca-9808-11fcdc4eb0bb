package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.cat.Cat;
import com.dianping.deal.common.builder.RichTextBuilder;
import com.dianping.mobile.mapi.dztgdetail.util.richText.RichText;
import com.dianping.mobile.mapi.dztgdetail.util.richText.RichTextUtil;
import com.dianping.mobile.mapi.dztgdetail.util.richText.TextStyle;
import org.apache.commons.lang.StringUtils;

/**
 * Created by yangquan02 on 18/11/7.
 */
public class JsonLabelUtil {

    private static final String TRANSPARENT_COLOR = "#FFFFFF";
    private static final String TEXT_STYLE_DEFAULT = RichTextBuilder.TextStyle.DEFAULT.getStyle();
    private static final String TEXT_STYLE_BOLD = RichTextBuilder.TextStyle.BOLD.getStyle();

    public static String pinPoolDPJson(String title, String highTitle) {
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(13, "#111111", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(13, "#FF6633", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

    public static String pinPoolMTJson(String title, String highTitle) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil.pinPoolMTJson(java.lang.String,java.lang.String)");
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(11, "#333333", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(11, "#FF4A4A", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

    public static String promoDPJson(String title, String highTitle) {
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(13, "#111111", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(13, "#FF6633", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

    public static String promoMTJson(String title, String highTitle) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil.promoMTJson(java.lang.String,java.lang.String)");
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(11, "#333333", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(11, "#333333", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

    public static String bonusDPJson(String title, String highTitle) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil.bonusDPJson(java.lang.String,java.lang.String)");
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(13, "#111111", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(13, "#FF6633", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

    public static String bonusMTJson(String title, String highTitle) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil.bonusMTJson(java.lang.String,java.lang.String)");
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(11, "#333333", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(11, "#333333", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

    public static String couponAmountMTJson(String title, String highTitle) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil.couponAmountMTJson(java.lang.String,java.lang.String)");
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(14, "#FF6200", TRANSPARENT_COLOR, TEXT_STYLE_BOLD, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(28, "#FF6200", TRANSPARENT_COLOR, TEXT_STYLE_BOLD, false, false))
                .build().toString();
    }

    public static String couponAmountRulesDPJson(String title, String highTitle) {
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(12, "#777777", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(12, "#777777", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

    public static String couponAmountRulesMTJson(String title, String highTitle) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil.couponAmountRulesMTJson(java.lang.String,java.lang.String)");
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(12, "#FF6600", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(12, "#FF6600", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

    public static String couponTitleDPJson(String title, String highTitle) {
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(14, "#111111", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(14, "#111111", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

    public static String couponTitleMTJson(String title, String highTitle) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil.couponTitleMTJson(java.lang.String,java.lang.String)");
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(14, "#333333", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(14, "#333333", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

    public static String couponAmountDescDPJson(String title, String highTitle) {
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(11, "#FF6633", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(11, "#FF6633", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

    public static String couponAmountDescMTJson(String title, String highTitle) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil.couponAmountDescMTJson(java.lang.String,java.lang.String)");
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(12, "#FF6600", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(12, "#FF6600", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

    public static String salesColorJson(boolean isMT, String title, String highTitle) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil.salesColorJson(boolean,java.lang.String,java.lang.String)");
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        //销量展示点评字体大小为13，美团字体大小为12
        int textSize = isMT ? 12 : 13;
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(textSize, "#ff6633", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(textSize, "#ff6633", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

    public static String bonusDescDPJson(String title, String highTitle) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil.bonusDescDPJson(java.lang.String,java.lang.String)");
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(12, "#000000", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(12, "#111111", TRANSPARENT_COLOR, TEXT_STYLE_BOLD, false, false))
                .build().toString();
    }

    public static String bonusDescMTJson(String title, String highTitle) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil.bonusDescMTJson(java.lang.String,java.lang.String)");
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(highTitle)) {
            return null;
        }
        return new RichTextBuilder(title, highTitle)
                .setDefaultTextItem(new RichTextBuilder.TextItem(12, "#151515", TRANSPARENT_COLOR, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(12, "#333333", TRANSPARENT_COLOR, TEXT_STYLE_BOLD, false, false))
                .build().toString();
    }

    public static String idleHoursPromoJson(boolean isMT, String content, String highlight) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil.idleHoursPromoJson(boolean,java.lang.String,java.lang.String)");
        return idleHoursPromoJson(isMT, content, highlight,
                RichTextBuilder.ARGBColor.BLACK.getColor(), TEXT_STYLE_DEFAULT);
    }

    public static String idleHoursPromoBannerJson(boolean isMT, String content, String highlight) {
        return idleHoursPromoJson(isMT, content, highlight,
                RichTextBuilder.ARGBColor.BLACK.getColor(), TEXT_STYLE_BOLD);
    }

    public static String idleHoursPromoBannerDetailJson(boolean isMT, String content, String highlight) {
        return idleHoursPromoJson(isMT, content, highlight,
                RichTextBuilder.ARGBColor.GRAY.getColor(), TEXT_STYLE_DEFAULT);
    }

    private static String idleHoursPromoJson(boolean isMT, String content, String highlight,
                                             String defaultColor, String highlightStyle) {
        if (StringUtils.isEmpty(content) || StringUtils.isEmpty(highlight)) {
            return null;
        }
        int textSize = isMT ? 11 : 13;
        RichTextBuilder.TextItem textItem = new RichTextBuilder.TextItem();
        textItem.setTextsize(textSize);
        if (StringUtils.isNotBlank(defaultColor)) {
            textItem.setTextcolor(defaultColor);
        }

        RichTextBuilder.TextItem highlightItem = new RichTextBuilder.TextItem();
        highlightItem.setTextcolor("#FF6633");
        highlightItem.setTextsize(textSize);
        highlightItem.setTextstyle(highlightStyle);

        return new RichTextBuilder(content, highlight)
                .setDefaultTextItem(textItem)
                .setHltTextItem(highlightItem)
                .build().toString();
    }
    
    public static String getMtLiveMiniAppBuyBannerJson(String content) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil.getMtLiveMiniAppBuyBannerJson(java.lang.String)");
        if (StringUtils.isBlank(content)) {
            return null;
        }
        int textSize = 12;
        RichText.TextItem defaultTextItem = new RichText.TextItem();
        defaultTextItem.setTextsize(textSize);
        defaultTextItem.setTextstyle(TextStyle.DEFAULT.getStyle());
        defaultTextItem.setTextcolor("#222222");

        RichText richText = new RichText();
        richText.getTextItemList().add(RichTextUtil.buildTextItem(content, defaultTextItem));
        return richText.toString();
    }

    public static String beautyCouponBagPromoBannerJson(boolean isMT, String content, String highlight) {
        return beautyCouponBagPromoJson(isMT, content, highlight,
                "#4C2600", TEXT_STYLE_BOLD);
    }

    private static String beautyCouponBagPromoJson(boolean isMT, String content, String highlight,
                                                   String defaultColor, String highlightStyle) {
        if (StringUtils.isEmpty(content) || StringUtils.isEmpty(highlight)) {
            return null;
        }
        int textSize = 13;
        RichTextBuilder.TextItem textItem = new RichTextBuilder.TextItem();
        textItem.setTextsize(textSize);
        textItem.setTextstyle(RichTextBuilder.TextStyle.BOLD.getStyle());
        if (StringUtils.isNotBlank(defaultColor)) {
            textItem.setTextcolor(defaultColor);
        }

        RichTextBuilder.TextItem highlightItem = new RichTextBuilder.TextItem();
        highlightItem.setTextcolor("#FF6633");
        highlightItem.setTextsize(textSize);
        highlightItem.setTextstyle(highlightStyle);
        highlightItem.setTextstyle(RichTextBuilder.TextStyle.BOLD.getStyle());
        return new RichTextBuilder(content, highlight)
                .setDefaultTextItem(textItem)
                .setHltTextItem(highlightItem)
                .build().toString();
    }

    public static String CouponInfoBannerJson(String content, String highlight) {
        if (StringUtils.isEmpty(content) || StringUtils.isEmpty(highlight)) {
            return null;
        }
        int textSize = 12;
        RichTextBuilder.TextItem textItem = new RichTextBuilder.TextItem();
        textItem.setTextsize(textSize);
        textItem.setTextstyle(RichTextBuilder.TextStyle.DEFAULT.getStyle());
        textItem.setTextcolor("222222");

        RichTextBuilder.TextItem highlightItem = new RichTextBuilder.TextItem();
        highlightItem.setTextcolor("#FF4B10");
        highlightItem.setTextsize(textSize);
        highlightItem.setTextstyle(RichTextBuilder.TextStyle.BOLD.getStyle());

        return new RichTextBuilder(content, highlight)
                .setDefaultTextItem(textItem)
                .setHltTextItem(highlightItem)
                .build().toString();
    }

    public static String BeautyBianMeiCouponBannerJson(String amountStr) {
        if (StringUtils.isEmpty(amountStr)) {
            return null;
        }
        int textSize = 13;
        RichText.TextItem defaultTextItem = new RichText.TextItem();
        defaultTextItem.setTextsize(textSize);
        defaultTextItem.setTextstyle(TextStyle.DEFAULT.getStyle());
        defaultTextItem.setTextcolor("222222");

        RichText.TextItem highlightItem = new RichText.TextItem();
        highlightItem.setTextcolor("#FD375D");
        highlightItem.setTextsize(textSize);
        highlightItem.setTextstyle(RichTextBuilder.TextStyle.BOLD.getStyle());

        RichText richText=new RichText();
        richText.getTextItemList().add(RichTextUtil.buildTextItem("您有", defaultTextItem));
        richText.getTextItemList().add(RichTextUtil.buildTextItem(amountStr + "元变美神券", highlightItem));
        richText.getTextItemList().add(RichTextUtil.buildTextItem("，下单立享，仅剩", defaultTextItem));

        return richText.toString();


    }

    public static String buildCostEffectivePinTuanBannerJson(String bannerText) {
        int textSize = 12;
        RichText.TextItem defaultTextItem = new RichText.TextItem();
        defaultTextItem.setTextsize(textSize);
        defaultTextItem.setTextstyle(TextStyle.DEFAULT.getStyle());
        defaultTextItem.setTextcolor("#FF2727");
        defaultTextItem.setText(bannerText);

        RichText richText=new RichText();
        richText.getTextItemList().add(defaultTextItem);
        return richText.toString();
    }

    /**
     * 健身通横幅富文本
     */
    public static String fitnessCrossBannerJson(String text) {
        if (StringUtils.isEmpty(text)) {
            return null;
        }

        int textSize = 13;
        return new RichTextBuilder(text, text)
                .setDefaultTextItem(new RichTextBuilder.TextItem(textSize, "#FF4B10", null, TEXT_STYLE_DEFAULT, false, false))
                .setHltTextItem(new RichTextBuilder.TextItem(textSize, "#FF4B10", null, TEXT_STYLE_DEFAULT, false, false))
                .build().toString();
    }

}
