package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.entity.meta.DealVersionConfig;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.COMMON_APP_KEY;

public class DealVersionUtils {

    /**
     * 判断数据版本是否低于新版下界
     * @return true-老版（数据版本<配置版本下界） false-新版（数据版本>=配置版本下界）
     * https://km.sankuai.com/collabpage/**********
     */
    public static boolean isOldMetaVersion(DealGroupDTO dealGroupDTO, String lionKey) {
        ProductMetaInfo productCategory = toProductMetaInfo(dealGroupDTO);
        if (productCategory == null || productCategory.getObjectVersion() == null || productCategory.getObjectVersion() <= 0 || productCategory.getSecondId() == null) {
            return true;
        }
        int categoryId = productCategory.getSecondId();
        // 查询新版的版本下界配置
        Config config = Lion.getBean(LionConstants.APP_KEY, lionKey, Config.class);
        if (config == null) {
            return true;
        }
        Map<Integer, List<DealVersionConfig>> versionBoundMap = config.getVersionBoundMap();
        if (MapUtils.isEmpty(versionBoundMap) || !versionBoundMap.containsKey(categoryId)) {
            return true;
        }
        List<DealVersionConfig> versionConfigList = versionBoundMap.get(categoryId);
        if (CollectionUtils.isEmpty(versionConfigList)) {
            return false;
        }
        // 判断商品结构id
        if (productCategory.getObjectId() == null || productCategory.getObjectId() <= 0) {
            return true;
        }
        DealVersionConfig versionConfig = versionConfigList.stream().filter(c -> productCategory.getObjectId().equals(c.getObjectId())).findFirst().orElse(null);
        // 如果无版本约束返回新版
        if (versionConfig == null || versionConfig.getObjectId() == null || versionConfig.getObjectId() <= 0) {
            return true;
        }
        // 判断商品版本，当数据版本<新版下界时返回老版(true)，否则返回新版(false)
        if (versionConfig.getMinVersion() == null || versionConfig.getMinVersion() <= 0) {
            return true;
        }
        return productCategory.getObjectVersion().compareTo(versionConfig.getMinVersion()) < 0;
    }

    /**
     * 判断数据版本是否低于新版下界
     * 仅用于打点，其他场景勿用
     * @param dealGroupDTO
     * @param lionKey
     * @return
     */
    public static boolean isOldMetaVersionForLog(DealGroupDTO dealGroupDTO, String lionKey) {
        ProductMetaInfo productCategory = toProductMetaInfo(dealGroupDTO);
        if (productCategory == null || productCategory.getObjectVersion() == null || productCategory.getObjectVersion() <= 0 || productCategory.getSecondId() == null) {
            return true;
        }
        int categoryId = productCategory.getSecondId();
        // 查询新版的版本下界配置
        Config config = Lion.getBean(COMMON_APP_KEY, lionKey, Config.class);
        if (config == null) {
            return true;
        }
        Map<Integer, List<DealVersionConfig>> versionBoundMap = config.getVersionBoundMap();
        if (MapUtils.isEmpty(versionBoundMap) || !versionBoundMap.containsKey(categoryId)) {
            return true;
        }
        List<DealVersionConfig> versionConfigList = versionBoundMap.get(categoryId);
        if (CollectionUtils.isEmpty(versionConfigList)) {
            return true;
        }
        // 判断商品结构id
        if (productCategory.getObjectId() == null || productCategory.getObjectId() <= 0) {
            return true;
        }
        DealVersionConfig versionConfig = versionConfigList.stream().filter(c -> productCategory.getObjectId().equals(c.getObjectId())).findFirst().orElse(null);
        // 如果无版本约束返回新版
        if (versionConfig == null || versionConfig.getObjectId() == null || versionConfig.getObjectId() <= 0) {
            return true;
        }
        // 判断商品版本，当数据版本<新版下界时返回老版(true)，否则返回新版(false)
        if (versionConfig.getMinVersion() == null || versionConfig.getMinVersion() <= 0) {
            return true;
        }
        return productCategory.getObjectVersion().compareTo(versionConfig.getMinVersion()) < 0;
    }

    private static ProductMetaInfo toProductMetaInfo(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO == null) {
            return null;
        }
        ProductMetaInfo productMetaInfo = new ProductMetaInfo();
        productMetaInfo.setSecondId(Optional.ofNullable(dealGroupDTO.getCategory()).map(DealGroupCategoryDTO::getCategoryId).map(Long::intValue).orElse(null));
        if (dealGroupDTO.getMetaObjectInfo() != null) {
            productMetaInfo.setObjectId(dealGroupDTO.getMetaObjectInfo().getObjectId());
            productMetaInfo.setObjectVersion(dealGroupDTO.getMetaObjectInfo().getObjectVersion());
        }
        return productMetaInfo;
    }

    @Data
    private static class Config implements Serializable {
        private Map<Integer, List<DealVersionConfig>> versionBoundMap;
    }

    @Data
    private static class ProductMetaInfo implements Serializable {
        /**
         * 二级分类
         */
        private Integer secondId;
        /**
         * 商品结构id
         */
        private Long objectId;
        /**
         * 商品结构版本
         */
        private Long objectVersion;
    }
}
