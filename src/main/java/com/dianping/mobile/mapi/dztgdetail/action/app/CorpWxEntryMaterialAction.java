package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetcorpwxentrymaterialRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedDeals;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.CorpWxFlowBanner;
import com.dianping.mobile.mapi.dztgdetail.facade.CorpWxEntryMaterialFacade;
import com.meituan.servicecatalog.api.annotations.*;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;
import java.util.Objects;

import static com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum.COST_EFFECTIVE;

@InterfaceDoc(
        displayName = "企业微信入口物料查询接口", type = "restful,mapi", description = "查询企业微信入口物料信息",
        scenarios = "该接口仅适用于双平台APP站点及h5的团购详情页", host = "http://mapi.dianping.com/general/platform/getcorpwxentrymaterial/",
        authors = "zhangyongming"
)
@Controller("getcorpwxentrymaterial.bin")
@Action(url = "general/platform/dztgdetail/getcorpwxentrymaterial.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class CorpWxEntryMaterialAction extends AbsAction<GetcorpwxentrymaterialRequest> {
    @Autowired
    CorpWxEntryMaterialFacade corpWxEntryMaterialFacade;

    @Override
    protected IMobileResponse validate(GetcorpwxentrymaterialRequest request, IMobileContext context) {
        if (!isRequestValid(request)) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    private boolean isRequestValid(GetcorpwxentrymaterialRequest request) {
        return Objects.nonNull(request.getCityId())
                || Objects.nonNull(request.getDealGroupId());
    }

    @MethodDoc(
            requestMethods = HttpMethod.GET, urls = "getcorpwxentrymaterial.bin", displayName = "查询企业微信入口物料信息",
            description = "查询企业微信入口物料信息",
            parameters = {
                @ParamDoc(
                        name = "request", description = "getcorpwxentrymaterial.bin请求参数",
                        type = GetcorpwxentrymaterialRequest.class, paramType = ParamType.REQUEST_BODY
                ),
                @ParamDoc(
                        name = "iMobileContext", description = "mapi-shell协议上下文", type = IMobileContext.class,
                        paramType = ParamType.REQUEST_BODY
                )},
            responseParams = {@ParamDoc(name = "result", description = "团购picasso数据", type = RelatedDeals.class)},
            // restExampleUrl = "",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {@ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "公开数据无须鉴权"),}
    )
    @Override
    protected IMobileResponse execute(GetcorpwxentrymaterialRequest request, IMobileContext context) {
        EnvCtx envCtx = initEnvCtx(context);
        try {
            CorpWxFlowBanner result = corpWxEntryMaterialFacade.getCorpWxFlowBanner(request, envCtx);
            return new CommonMobileResponse(result);
        } catch (Exception e) {
            logger.error("getcorpwxentrymaterial.bin error", e);
            return Resps.SYSTEM_ERROR;
        }
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }

}
