package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.common.constants.EduConstant;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.util.EduDealUtils;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.beautycontent.creator.application.edu.technicianVideo.dto.EduTechnicianVideoListForCDTO;
import com.sankuai.beautycontent.creator.application.edu.technicianVideo.facade.EduTechnicianVideoQueryFacade;
import com.sankuai.beautycontent.creator.application.edu.technicianVideo.request.QueryEduTechnicianVideoForCRequest;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;

import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.io.Serializable;
import java.util.List;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 在线教育：获取老师和试听课
 */
public class EduTeacherAndVideoProcessor extends AbsDealProcessor {

    @Autowired
    @Qualifier("eduTechnicianVideoQueryFacade")
    private EduTechnicianVideoQueryFacade eduTechnicianVideoQueryFacade;


    @Override
    public void prepare(DealCtx ctx) {
        if (!EduDealUtils.isEduOnlineCourseDeal(ctx)) {
            return;
        }
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        if (dealGroupDTO == null) {
            return;
        }
        if (CollectionUtils.isEmpty(dealGroupDTO.getAttrs())) {
            return;
        }
        AttrDTO courseTrialAttr = dealGroupDTO.getAttrs().stream()
                .filter(attr -> attr.getName().equals(EduConstant.ATTR_KEY_COURSE_TRIAL))
                .findFirst().orElse(null);
        List<EduCourseTrial> teacherAndTrialClassList = readEduCourseTrialFromAttr(courseTrialAttr);
        if (CollectionUtils.isEmpty(teacherAndTrialClassList)) {
            return;
        }
        List<Long> courseVideoIds = teacherAndTrialClassList.stream()
                .map(EduCourseTrial::getCourseVideoId)
                .collect(Collectors.toList());
        QueryEduTechnicianVideoForCRequest request = new QueryEduTechnicianVideoForCRequest();
        request.setIds(courseVideoIds);
        try {
            eduTechnicianVideoQueryFacade.queryData(request);
        }
        catch (Exception e) {
            logger.error("eduTechnicianVideoQueryFacade.queryData error", e);
        }

        ctx.getFutureCtx().setEduTechnicianVideoListFuture((Future<RemoteResponse<EduTechnicianVideoListForCDTO>>) FutureFactory.getFuture());
    }

    @Override
    public void process(DealCtx ctx) {

    }

    private List<EduCourseTrial> readEduCourseTrialFromAttr(AttrDTO courseTrialAttr) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.EduTeacherAndVideoProcessor.readEduCourseTrialFromAttr(com.sankuai.general.product.query.center.client.dto.AttrDTO)");
        if (courseTrialAttr == null || CollectionUtils.isEmpty(courseTrialAttr.getValue()) || StringUtils.isBlank(courseTrialAttr.getValue().get(0))) {
            return null;
        }
        return JsonCodec.decode(courseTrialAttr.getValue().get(0), new TypeReference<List<EduCourseTrial>>() {});
    }

    @Data
    public static class EduCourseTrial implements Serializable {
        /**
         * 老师ID
         **/
        private Integer teacher;

        /**
         * 试听课Id
         **/
        @JsonProperty("course_video")
        private Long courseVideoId;
    }

}
