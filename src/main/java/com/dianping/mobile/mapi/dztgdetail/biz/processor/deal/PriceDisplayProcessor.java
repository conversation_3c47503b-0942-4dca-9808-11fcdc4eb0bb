package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PriceDisplayWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;

import javax.annotation.Resource;

public class PriceDisplayProcessor extends AbsDealProcessor {

    @Resource
    private PriceDisplayWrapper wrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        // 交易快照来源，不请求报价 || 预览团单不请求报价
        return !RequestSourceEnum.fromTradeSnapshot(ctx.getRequestSource()) || DealUtils.isPreviewDeal(ctx);
    }

    @Override
    public void prepare(DealCtx ctx) {
        wrapper.prepare(ctx);
    }

    @Override
    public void process(DealCtx ctx) {
        wrapper.process(ctx);
    }
}
