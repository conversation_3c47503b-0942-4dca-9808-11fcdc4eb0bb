package com.dianping.mobile.mapi.dztgdetail.datatype.resp.common;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

@TypeDoc(description = "打点信息模型")
@MobileDo(id = 0xf48e)
@Data
public final class AbConfig implements Serializable {

    @FieldDoc(description = "实验ID")
    @MobileField(key = 0x85df)
    private String expId;

    @FieldDoc(description = "实验结果")
    @MobileField(key = 0x9263)
    private String expResult;

    @FieldDoc(description = "实验上报信息")
    @MobileField(key = 0xddc3)
    private String expBiInfo;

    public boolean isUseNewStyle(){
        return "b".equals(expResult);
    }
}
