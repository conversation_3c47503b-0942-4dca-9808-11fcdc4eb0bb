package com.dianping.mobile.mapi.dztgdetail.datatype.resp.module;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;

import java.io.Serializable;

@MobileDo(id = 0x9cee)
public class DpDztgShareModule implements Serializable {
    /**
    *
    */
    @MobileField(key = 0x1d80)
    private long dpDealGroupId;

    /**
    *
    */
    @MobileField(key = 0xdbee)
    private String bigPhoto;

    /**
    *
    */
    @MobileField(key = 0x3869)
    private String photo;

    /**
    *
    */
    @MobileField(key = 0x1970)
    private String productTitle;

    /**
    *
    */
    @MobileField(key = 0x478b)
    private String shortTitle;

    /**
    * 原价
    */
    @MobileField(key = 0xdfc4)
    private double originalPrice;

    /**
    * 售卖价格
    */
    @MobileField(key = 0xb716)
    private double price;

    /**
     * 是否关注
     */
    @MobileField(key = 0x31fb)
    private boolean interested;

    public long getDpDealGroupId() {
        return dpDealGroupId;
    }

    public void setDpDealGroupId(long dpDealGroupId) {
        this.dpDealGroupId = dpDealGroupId;
    }

    public String getBigPhoto() {
        return bigPhoto;
    }

    public void setBigPhoto(String bigPhoto) {
        this.bigPhoto = bigPhoto;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public String getProductTitle() {
        return productTitle;
    }

    public void setProductTitle(String productTitle) {
        this.productTitle = productTitle;
    }

    public String getShortTitle() {
        return shortTitle;
    }

    public void setShortTitle(String shortTitle) {
        this.shortTitle = shortTitle;
    }

    public double getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(double originalPrice) {
        this.originalPrice = originalPrice;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public boolean isInterested() {
        return interested;
    }

    public void setInterested(boolean interested) {
        this.interested = interested;
    }
}