package com.dianping.mobile.mapi.dztgdetail.entity;

import java.io.Serializable;

/**
 * https://123.sankuai.com/km/page/57936041
 */
public class BrowserContent implements Serializable{

    private String id;

    private long tm; //时间戳

    private int stype = 0;

    public BrowserContent(String id, long tm, int stype) {
        this.id = id;
        this.tm = tm;
        this.stype = stype;
    }

    public static BrowserContent getTgBrowserContent(int dealGroupId){
        long curTimeSec= System.currentTimeMillis() / 1000;
        return new BrowserContent(String.valueOf(dealGroupId),curTimeSec,1);
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public long getTm() {
        return tm;
    }

    public void setTm(long tm) {
        this.tm = tm;
    }

    public int getStype() {
        return stype;
    }

    public void setStype(int stype) {
        this.stype = stype;
    }
}