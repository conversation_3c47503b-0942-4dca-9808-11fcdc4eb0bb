package com.dianping.mobile.mapi.dztgdetail.biz.processor.promo;

import com.dianping.gm.bonus.exposure.api.dto.BonusExposureQueryRequestDTO;
import com.dianping.gm.bonus.exposure.api.enums.PlatFormEnum;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.GeneralMarketingWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/12/18.
 */
public class BonusExposureProcessor extends AbsDealProcessor {

    @Autowired
    private GeneralMarketingWrapper generalMarketingWrapper;

    @Override
    public void prepare(DealCtx ctx) {
        BonusExposureQueryRequestDTO bonusExposureRequest = new BonusExposureQueryRequestDTO();
        bonusExposureRequest.setDealGroupId(ctx.getDpId());
        if (ctx.isMt()) {
            bonusExposureRequest.setPlatform(PlatFormEnum.MT.getCode());
        } else {
            bonusExposureRequest.setPlatform(PlatFormEnum.DP.getCode());
        }
        if (ctx.getEnvCtx().getDpUserId() > 0) {
            bonusExposureRequest.setUserIdStr(String.valueOf(ctx.getEnvCtx().getDpUserId()));
        }
        ctx.getFutureCtx().setBonusExposureFuture(generalMarketingWrapper.prepareQueryBonusExposure(bonusExposureRequest));
    }

    @Override
    public void process(DealCtx ctx) {
        ctx.setBonusExposureDTOList(generalMarketingWrapper.queryBonusExposure(ctx.getFutureCtx().getBonusExposureFuture()));
    }
}
