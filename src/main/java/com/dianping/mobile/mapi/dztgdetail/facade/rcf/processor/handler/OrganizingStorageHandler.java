package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.handler;

import com.dianping.mobile.mapi.dztgdetail.common.constants.Constants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: wuwen<PERSON><PERSON>
 * @create: 2024-12-17
 * @description:
 */
@Service
public class OrganizingStorageHandler implements BaseReserveMaintenanceHandler {
    @Override
    public int getDealSecondCategory() {
        return 459;
    }

    @Override
    public String getExpName(boolean isMt) {
        return null;
    }

    @Override
    public String getExpName(DealCtx ctx) {
        if (ctx == null) {
            return null;
        }
        if (DealCtxHelper.isPreOrderDeal(ctx)) {
            return ctx.isMt() ? Constants.MT_PRE_ORDER_EXP : Constants.DP_PRE_ORDER_EXP;
        }
        return getExpName(ctx.isMt());
    }

    @Override
    public List<String> getAbKeys() {
        return null;
    }

    @Override
    public boolean isEnable(DealCtx ctx) {
        // 强预订团单支持买约一体
        if (DealCtxHelper.isPreOrderDeal(ctx)) {
            return true;
        }
        return false;
    }

    @Override
    public List<String> getSpecialAbKeys() {
        return Lists.newArrayList(Constants.MT_PRE_ORDER_EXP, Constants.DP_PRE_ORDER_EXP);
    }
}
