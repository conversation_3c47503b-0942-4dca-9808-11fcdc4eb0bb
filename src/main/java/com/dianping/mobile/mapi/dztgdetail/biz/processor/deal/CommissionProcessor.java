package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.CommissionWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;

import javax.annotation.Resource;

public class CommissionProcessor extends AbsDealProcessor {

    @Resource
    private CommissionWrapper commissionWrapper;

    @Override
    public void prepare(DealCtx ctx) {
        commissionWrapper.prepare(ctx);
    }

    @Override
    public void process(DealCtx ctx) {
        commissionWrapper.resolve(ctx);
    }
}
