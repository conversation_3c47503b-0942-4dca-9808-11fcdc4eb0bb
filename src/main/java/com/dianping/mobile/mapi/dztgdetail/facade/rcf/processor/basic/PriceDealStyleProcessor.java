package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.basic;

import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.style.dto.DealGroupDzxInfo;
import com.dianping.deal.style.dto.StyleAbConfig;
import com.dianping.deal.style.dto.StyleExp;
import com.dianping.deal.style.dto.StyleResponse;
import com.dianping.deal.style.enums.DzxPlatform;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealStyleWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.MttgVersion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealStyles;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BeautySubType;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DealAttrCons;
import com.dianping.mobile.mapi.dztgdetail.common.enums.KeyEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.PoiSubCate;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.*;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import com.dianping.mobile.mapi.dztgdetail.util.DealStyleUtil;
import com.dianping.mobile.mapi.dztgdetail.util.SwitchUtils;
import com.dianping.tuangou.dztg.bjwrapper.api.dto.MtDealDto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2023/8/17
 * @since mapi-dztgdetail-web
 */
public class PriceDealStyleProcessor extends AbsDealProcessor {

    private static final List<String> GC_DEFAULT_STYLE_LIST = com.google.common.collect.Lists.newArrayList("scenic","travel","travel_selected","food","default");

    @Autowired
    private DealStyleWrapper dealStyleWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        return ctx.getEnvCtx() != null && !ctx.getEnvCtx().isFromH5();
    }

    @Override
    public void prepare(DealCtx ctx) {
        if(ctx.isMt()) {
            Future mtDealDtoListFuture = dealStyleWrapper.preMtDealDtoList(Lists.newArrayList(ctx.getMtId()));
            ctx.getFutureCtx().setMtDealDtoListFuture(mtDealDtoListFuture);
        }
    }

    @Override
    public void process(DealCtx ctx) {
        ctx.setMtDealDtoList(dealStyleWrapper.getFutureResult(ctx.getFutureCtx().getMtDealDtoListFuture()));
    }
    
}
