package com.dianping.mobile.mapi.dztgdetail.faulttolerance.req;

import com.dianping.deal.style.dto.laout.DealPageLayoutComponentDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2024/10/14 18:24
 */
@Data
public class DealLayoutContext {

    private EnvCtx envCtx;

    private Long userId;

    private Long dealGroupId;

    private String pageSource;

    private Integer deviceHeight;

    private List<DealPageLayoutComponentDTO> components;

    public boolean isParamIllegal() {
        if (userId == null || userId <= 0) {
            return true;
        }
        if (dealGroupId == null || dealGroupId <= 0) {
            return true;
        }
        if (deviceHeight == null || deviceHeight <= 0) {
            return true;
        }
        if (CollectionUtils.isEmpty(components)) {
            return true;
        }
        return false;
    }

}
