package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Sets;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiShopCategoryWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.util.SwitchUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.cache2.CacheClient;
import com.sankuai.athena.inf.cache2.CacheClientConfig;
import com.sankuai.athena.inf.cache2.CacheKey;
import com.sankuai.athena.inf.cache2.loader.DataLoader;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/6/28.
 */
public class PoiShopCategoryProcessor extends AbsDealProcessor {
    @Autowired
    PoiShopCategoryWrapper poiShopCategoryWrapper;

    private static CacheClientConfig cacheClientConfig = new CacheClientConfig("redis-dealgroup");

    private static CacheClient cacheClient = AthenaInf.getCacheClient(cacheClientConfig);

    private static final int CACHE_EXPIRE_TIME = 1296000;

    private static final int CACHE_REFRESH_TIME = 0;

    private static final String REDIS_POI_SHOP_CATEGORY_ID = "poi_shop_category_id";

    private static final String REDIS_CATEGORY = "512";

    private static TypeReference<Set<Integer>> poiShopCategoryCacheTypeReference = new TypeReference<Set<Integer>>() {};

    @Override
    public boolean isEnable(DealCtx ctx) {
        return true;
//        return ctx.getEnvCtx().isMainApp();
    }

    @Override
    public void prepare(DealCtx ctx) {
    }

    @Override
    public void process(DealCtx ctx) {
        long dpLongShopId = ctx.getDpLongShopId();
        if(SwitchUtils.isPoiShopCategoryCacheEnable()) {
            ctx.setPoiBackCategoryIds(getPoiBackCategoryIds(dpLongShopId));
        } else {
            ctx.setPoiBackCategoryIds(poiShopCategoryWrapper.queryDpShopCategoryIds(dpLongShopId));
        }
    }


    public Set<Integer> getPoiBackCategoryIds(long dpLongShopId) {
        Set<Integer> poiBackCategoryIds;
        try {
            poiBackCategoryIds = Optional.ofNullable(getPoiShopCategoryIdsFromCache(dpLongShopId).get()).orElse(Sets.newHashSet());
        } catch (Exception e) {
            Cat.logError(e);
            Cat.logMetricForCount("getPoiBackCategoryIdsError");
            logger.error("getPoiBackCategoryIdsError");
            Cat.logEvent(REDIS_POI_SHOP_CATEGORY_ID, "getPoiBackCategoryIdsError");
            return poiShopCategoryWrapper.queryDpShopCategoryIds(dpLongShopId);
        }
        return poiBackCategoryIds;
    }


    private CompletableFuture<Set<Integer>> getPoiShopCategoryIdsFromCache(long dpLongShopId) {
        CacheKey cacheKey = new CacheKey(REDIS_POI_SHOP_CATEGORY_ID, dpLongShopId);
        DataLoader<Set<Integer>> dataLoader = key -> {
            if(key == null) {
                return CompletableFuture.completedFuture(null);
            }
            return CompletableFuture.completedFuture(queryPoiBackCategoryIds(dpLongShopId));
        };
        return cacheClient.asyncGetReadThrough(cacheKey,poiShopCategoryCacheTypeReference, dataLoader, CACHE_EXPIRE_TIME + new Random().nextInt(300) , CACHE_REFRESH_TIME);

    }

    public Set<Integer> queryPoiBackCategoryIds(long dpShopId) {
        return poiShopCategoryWrapper.queryDpShopCategoryIds(dpShopId);
    }


}
