package com.dianping.mobile.mapi.dztgdetail.common.enums;

import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/4/7
 */
public enum SaleStatusEnum {

    COMING_SOON(1, "COMING_SOON", "即将开抢", "即将开订"),
    SNAP_UP_NOW(2, "SNAP_UP_NOW", "立即抢", "立即预订"),
    OUT_OF_STOCK(3, "OUT_OF_STOCK", "本场已抢光", "已订满"),
    SOLD_OUT(4, "SOLD_OUT", "已抢光", "已订满"),
    END_OF_SALE(5, "END_OF_SALE", "抢购结束", "已结束"),
    PURCHASE_LIMIT(6, "PURCHASE_LIMIT", "已达购买次数上限", "已订满"),
    ;

    public int code;
    public String saleStatusName;
    public String desc;
    public String orderDesc;

    private SaleStatusEnum(int code, String saleStatusName, String desc, String orderDesc) {
        this.code = code;
        this.saleStatusName = saleStatusName;
        this.desc = desc;
        this.orderDesc = orderDesc;
    }

    public static String getDesc(String saleStatusName){
        if(StringUtils.isBlank(saleStatusName)){
            return "";
        }

        for(SaleStatusEnum saleStatusEnum : SaleStatusEnum.values()){
            if(saleStatusEnum.saleStatusName.equals(saleStatusName)){
                return saleStatusEnum.desc;
            }
        }

        return "";
    }

    public static String getOrderDesc(String saleStatusName){
        if(StringUtils.isBlank(saleStatusName)){
            return "";
        }

        for(SaleStatusEnum saleStatusEnum : SaleStatusEnum.values()){
            if(saleStatusEnum.saleStatusName.equals(saleStatusName)){
                return saleStatusEnum.orderDesc;
            }
        }

        return "";
    }
}
