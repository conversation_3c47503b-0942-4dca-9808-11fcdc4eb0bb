package com.dianping.mobile.mapi.dztgdetail.util;

import java.text.DecimalFormat;

public final class GEOUtils {
    private GEOUtils() {}

//    private static final double PI = 3.14159265;
    private static final double R = 6.371229 * 1e6;

    private static final DecimalFormat df   =new DecimalFormat("0.00");

    public static double distance(double lat1, double lng1, double lat2, double lng2) {
        double x = (lng2 - lng1) * Math.PI * R * Math.cos(((lat1 + lat2) / 2) * Math.PI / 180) / 180;
        double y = (lat2 - lat1) * Math.PI * R / 180;
        return Math.hypot(x, y);
    }

    public static String distanceStr(double lat1, double lng1, double lat2, double lng2) {
        double x = distance(lat1,lng1,lat2,lng2);
        int y = Double.valueOf(x).intValue();
        if(y <= 1000){
            return y + "m";
        }else if(y <= 100000){
            return df.format(x / 1000) + "km";
        }else{
            return ">100km";
        }
    }
}
