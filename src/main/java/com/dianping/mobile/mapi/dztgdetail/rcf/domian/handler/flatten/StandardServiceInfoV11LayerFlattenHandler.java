package com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler.flatten;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.mobile.mapi.dztgdetail.rcf.enums.ModuleType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;


/**
 * <AUTHOR>
 * @create 2024/12/25 11:16
 */
@Slf4j
@Component("standard_service_info_v1_1layer")
public class StandardServiceInfoV11LayerFlattenHandler implements DealModuleFlattenProcessor{

    public static final String TYPE_1_1 = "type_1_1";
    public static final String TYPE_1_2 = "type_1_2";

    @Override
    public ModuleType getType() {
        return ModuleType.standard_service_info_v1_1layer;
    }

    @Override
    public void flattenModule(JSONArray rcfSkuGroupsModule1Flatten, JSONObject module) {
        JSONArray skuGroupsModel1 = (JSONArray) module.get("skuGroupsModel1");
        if (Objects.isNull(skuGroupsModel1) || skuGroupsModel1.isEmpty()){
            return;
        }
        for (int i = 0; i < skuGroupsModel1.size(); i++) {
            JSONObject skuGroupsModel = (JSONObject) skuGroupsModel1.get(i);
            if (Objects.isNull(skuGroupsModel)){
                continue;
            }
            // 处理 standard_service_info_v1_1layer_dealSkuTitle
            flattenDealSkuTitle(skuGroupsModel, rcfSkuGroupsModule1Flatten);
            JSONArray dealSkuList = (JSONArray) skuGroupsModel.get("dealSkuList");
            // 需要透传下游
            Integer titleStyle = (Integer) skuGroupsModel.get("titleStyle");
            if (dealSkuList != null){
                // 处理 standard_service_info_v1_1layer_dealSkuItem
                flattenDealSkuItem(dealSkuList, titleStyle, rcfSkuGroupsModule1Flatten);
            }
        }
    }

    private void flattenDealSkuTitle(JSONObject skuGroupsModel, JSONArray rcfSkuGroupsModule1Flatten){
        String title = (String) skuGroupsModel.get("title");
        Integer titleStyle = (Integer) skuGroupsModel.get("titleStyle");
        if (StringUtils.isNotBlank(title)){
            JSONObject result = new JSONObject();
            result.put("key", TYPE_1_1);
            result.put("title", title);
            result.put("titleStyle", titleStyle);
            rcfSkuGroupsModule1Flatten.add(result);
        }
    }
    private void flattenDealSkuItem(JSONArray dealSkuList, Integer titleStyle, JSONArray rcfSkuGroupsModule1Flatten){
        if (Objects.isNull(dealSkuList)){
            return;
        }
        for (int i = 0; i < dealSkuList.size(); i++) {
            JSONObject dealSku = (JSONObject) dealSkuList.get(i);
            String title = (String) dealSku.get("title");
            if (StringUtils.isBlank(title)){
                continue;
            }
            String rightDesc = (String) dealSku.get("copies");

            String rightMainText = (String) dealSku.get("rightText");
            rightMainText = StringUtils.isBlank(rightMainText) ? (String) dealSku.get("price"):null;

            JSONArray items = (JSONArray) dealSku.get("items");
            String descriptions = joinSkuItems(items);

            JSONObject result = new JSONObject();
            result.put("key", TYPE_1_2);
            result.put("title", title);
            result.put("titleStyle", titleStyle);
            result.put("rightDesc", rightDesc);
            result.put("rightMainText", rightMainText);
            result.put("descriptions", descriptions);
            rcfSkuGroupsModule1Flatten.add(result);
        }
    }

    private String joinSkuItems(JSONArray items){
        if (Objects.isNull(items) || items.isEmpty()){
            return null;
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < items.size(); i++) {
            JSONObject item = (JSONObject) items.get(i);
            String value = (String) item.get("value");
            if (StringUtils.isNotBlank(value)){
                stringBuilder.append(value);
                stringBuilder.append(" | ");
            }
        }
        return stringBuilder.length() > 0 ? stringBuilder.substring(0,stringBuilder.length()-2) : stringBuilder.toString();
    }

}
