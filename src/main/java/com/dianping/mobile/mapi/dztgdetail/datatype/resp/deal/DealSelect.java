package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;

import java.io.Serializable;
import java.math.BigDecimal;

@MobileDo(name = "DealSelect")
public class DealSelect implements Serializable {
	private static final long serialVersionUID = 0L;

	public static final int DEAL_STATUS_AVAILABLE = 1;
	public static final int DEAL_STATUS_SOLDOUT = 2;

	@MobileField(key = 0x91b)
	private int id;

	@MobileField(key = 0x36e9)
	private String title;

	@MobileField(key = 0xc5b5)
	private String priceStr;

	private BigDecimal price;

	@MobileField(key = 0x630b)
	private int count;

	// 1-可购买 2-已卖光
	@MobileField(key = 0x2820)
	private int status;

	// 1-团购券 2-实物 3-抽奖
	@MobileField(key = 0x6fa3)
	private int dealType;

	private boolean isDeliver;

	private int maxJoin;

	@MobileField(key = 0x8d9a)
	private String groupTitle;

	private int provideInvoice;

	private int thirdPartyId;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
		this.priceStr = price.toPlainString();
	}

	public String getPriceStr() {
		return priceStr;
	}

	public void setPriceStr(String priceStr) {
		this.priceStr = priceStr;
	}

	public int getCount() {
		return count;
	}

	public void setCount(int count) {
		this.count = count;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public int getDealType() {
		return dealType;
	}

	public void setDealType(int dealType) {
		this.dealType = dealType;
	}

	public boolean isDeliver() {
		return isDeliver;
	}

	public void setDeliver(boolean isDeliver) {
		this.isDeliver = isDeliver;
	}

	public int getMaxJoin() {
		return maxJoin;
	}

	public void setMaxJoin(int maxJoin) {
		this.maxJoin = maxJoin;
	}

	public String getGroupTitle() {
		return groupTitle;
	}

	public void setGroupTitle(String groupTitle) {
		this.groupTitle = groupTitle;
	}

	public int getProvideInvoice() {
		return provideInvoice;
	}

	public void setProvideInvoice(int provideInvoice) {
		this.provideInvoice = provideInvoice;
	}

	public int getThirdPartyId() {
		return thirdPartyId;
	}

	public void setThirdPartyId(int thirdPartyId) {
		this.thirdPartyId = thirdPartyId;
	}
}