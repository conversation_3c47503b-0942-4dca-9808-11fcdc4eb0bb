package com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealstruct;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: z<PERSON><PERSON><PERSON><EMAIL>
 * @Date: 2024/1/12
 */
@Data
@TypeDoc(description = "展示内容项")
@MobileDo(id = 0x2eb9)
public class PnStandardDisplayValueDTO implements Serializable {
    @FieldDoc(description = "值的类型：1普通文案 2富文本")
    @MobileDo.MobileField(key = 0x270c)
    private Integer pnType;

    @FieldDoc(description = "具体值")
    @MobileDo.MobileField(key = 0x474b)
    private String pnValue;
}
