package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ResultList;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2023/7/26
 */
@Data
@TypeDoc(description = "多买多省搭售卡片信息")
@MobileDo(id = 0x77fc)
public class BuyMoreSaveMoreVO extends ResultList {
    @FieldDoc(description = "团单搭售模块名")
    @MobileDo.MobileField(key = 0x6f1f)
    private String dealModuleTitle;

    @FieldDoc(description = "列表页入口文案")
    @MobileDo.MobileField(key = 0xb23a)
    private String floatLayerEntranceTitle;

    @FieldDoc(description = "浮层整体名称")
    @MobileDo.MobileField(key = 0x9415)
    private String floatLayerTitle;

    @FieldDoc(description = "实验数据")
    @MobileDo.MobileField(key = 0xddc3)
    private String expBiInfo;

    @FieldDoc(description = "搭售卡片列表信息")
    @MobileDo.MobileField(key = 0x68b0)
    private List<BuyMoreSaveMoreCardVO> cardList;
}
