package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.annotation.MobileRequest.Param;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.dianping.mobile.mapi.dztgdetail.util.LongUtils;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
@TypeDoc(description = "团单主接口请求参数")
@MobileRequest
public class SkuSelectReq implements IMobileRequest {

    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID")
    @Param(name = "dealgroupid")
    private String dealgroupid;

    @FieldDoc(description = "skuID", rule = "美团点评统一")
    @Param(name = "skuid")
    private String skuId;
}
