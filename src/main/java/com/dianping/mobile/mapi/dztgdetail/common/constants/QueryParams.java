package com.dianping.mobile.mapi.dztgdetail.common.constants;

import org.apache.curator.shaded.com.google.common.collect.Lists;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/3/29.
 */
public class QueryParams {
    public static final String AREA_ID = "areaid";
    public static final String CITY_ID = "cityid";
    public static final String UUID = "uuid";
    public static final String LAT = "lat";
    public static final String LNG = "lng";
    public static final String PAGING_LIMIT = "limit";
    public static final String PAGING_OFFSET = "offset";
    public static final String STID_PARAM_STE = "ste";
    public static final String SORT = "sort";
    public static final String FIELD = "fields";
    //推荐相关参数
    public static final String SCENE = "scene";
    //推荐里面的，应该是多个cate组成一个String
    public static final String CATE = "cate";
    @Deprecated
    public static final String DID = "dealid";
    public static final String SDID = "stringdealid";
    @Deprecated
    public static final String STORE_ID = "storeid";

    //UTM相关参数
    public static final String UTM_MEDIUM = "utm_medium";
    public static final String UTM_CONTENT = "utm_content";
    public static final String UTM_SOURCE = "utm_source";
    public static final String UTM_TERM = "utm_term";
    public static final String UTM_CAMPAIGN = "utm_campaign";


    //客户端相关
    public static final String CLIENT = "client";
    public static final String CLIENT_VERSION = "version_name";

    //筛选项相关
    public static final String DISTANCE = "distance";

    public static final List<String> SINAI_DP_POI_FIELDS = Lists.newArrayList("dpPoiId", "shopId", "hospitalInfo",
            "useType",  "backMainCategoryPath", "cityId", "power", "businessHours", "avgPrice", "shopPower", "fiveScore",
            "mainRegionName", "lat", "lng", "shopName", "defaultPic", "appSides");
}
