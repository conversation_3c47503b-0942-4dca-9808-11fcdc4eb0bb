package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-04-02
 */
@Data
@MobileDo(id = 0x66ff)
@TypeDoc(description = "神券提示条展示文案")
public class TextDisplayInfo implements Serializable {
    @FieldDoc(description = "文案类型")
    @MobileField(key = 0x8f0c)
    private String type;

    @FieldDoc(description = "展示文案高亮颜色")
    @MobileField(key = 0xeead)
    private String textColor;

    @FieldDoc(description = "展示文案")
    @MobileField(key = 0x451b)
    private String text;

}