package com.dianping.mobile.mapi.dztgdetail.datatype.resp.shop;

import lombok.Data;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;

import java.io.Serializable;

/**
 * 标签icon字段信息
 */
@Data
public class TagIconsVO implements Serializable {
    /**
     * icon高
     */
    @MobileField(key = 0xd2f8)
    private int hight;

    /**
     * icon宽
     */
    @MobileField(key = 0x2b78)
    private int width;

    /**
     * icon的url
     */
    @MobileField(key = 0xb094)
    private String tagIcon;
}
