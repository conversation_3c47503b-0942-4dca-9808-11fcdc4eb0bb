package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.biz.service.DrivingPoiService;
import com.dianping.mobile.mapi.dztgdetail.biz.service.bo.DrivingPoi;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedShop;
import com.dianping.mobile.mapi.dztgdetail.helper.SwitchHelper;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.sankuai.zdc.apply.api.DrivingPoiQueryService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

public class DrivingShopProcessor extends AbsDealProcessor {

    @Resource
    private DrivingPoiQueryService drivingPoiQueryServiceFuture;

    @Autowired
    DrivingPoiService drivingPoiService;

    @Override
    public boolean isEnable(DealCtx ctx) {
        boolean gray = SwitchHelper.enableDrivingShop(ctx.isMt(), ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId(), ctx.getCityId4P());
        return ctx.getDpLongShopId() > 0 && isDrivingSchoolCar(ctx) && gray;
    }

    @Override
    public void prepare(DealCtx ctx) {
        drivingPoiQueryServiceFuture.queryShop2DrivingFieldMap(Collections.singletonList(ctx.getDpLongShopId()));
        ctx.getFutureCtx().setDrivingShopFuture(FutureFactory.getFuture());
    }

    @Override
    public void process(DealCtx ctx) {
        try {
            ctx.setRelatedShops(getRelatedShops(ctx));
        } catch (Exception e) {
            logger.error("获取关联门店错误", e);
        }
    }

    private List<RelatedShop> getRelatedShops(DealCtx ctx) throws Exception {
        Future drivingShopFuture = ctx.getFutureCtx().getDrivingShopFuture();
        Map<Long, List<Long>> drivingDpShopMap;
        long dpLongShopId = ctx.getDpLongShopId();

        if (drivingShopFuture == null) {
            return new ArrayList<>();
        }

        List<RelatedShop> defaultRelatedShops = new ArrayList<>();
        RelatedShop defaultShop = new RelatedShop();
        defaultShop.setShopInvalidText("商家暂未提供练车场地信息，请与商家确认");
        defaultRelatedShops.add(defaultShop);

        drivingDpShopMap = (Map<Long, List<Long>>) drivingShopFuture.get();

        if (MapUtils.isEmpty(drivingDpShopMap) || CollectionUtils.isEmpty(drivingDpShopMap.get(dpLongShopId))) {
            return defaultRelatedShops;
        }

        List<Long> drivingDpShopIds = drivingDpShopMap.get(dpLongShopId);

        DrivingPoi req = DrivingPoi.builder()
                .isMt(ctx.isMt())
                .dpDrivingPoiIds(drivingDpShopIds)
                .shopId(ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId())
                .cityId(ctx.getCityId4P())
                .enableMap(true)
                .userLng(ctx.getUserlng())
                .userLat(ctx.getUserlat())
                .build();

        List<RelatedShop> related = drivingPoiService.getDrivingShopsWithDpDrivingPoiIds(req);

        if (CollectionUtils.isEmpty(related)) {
            return defaultRelatedShops;
        } else {
            return related;
        }

    }

}
