package com.dianping.mobile.mapi.dztgdetail.datatype.context;

import com.dianping.mobile.framework.datatypes.IMobileContext;
import lombok.Data;


/**
 * 查询团单信息的环境变量
 */
@Data
public class CouponCtx {

    public CouponCtx(EnvCtx ctx) {
        if (ctx != null) {
            this.envCtx = ctx;
        }
    }

    private EnvCtx envCtx = new EnvCtx();
    private IMobileContext context;
    private FutureCtx futureCtx = new FutureCtx();


    private int dpId; //点评团单ID
    private int mtId; //美团团单ID

    private int dpCityId;
    private int mtCityId;

    @Deprecated
    private int mtShopId;
    private long mtShopIdLong;
    @Deprecated
    private int dpShopId;
    private long dpShopIdLong;

    private String cx;
    private String riskToken;
    private String couponGroupId;

    public boolean isMt() {
        return this.envCtx.isMt();
    }

}
