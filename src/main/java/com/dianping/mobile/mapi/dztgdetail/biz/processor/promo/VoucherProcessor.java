package com.dianping.mobile.mapi.dztgdetail.biz.processor.promo;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PromoWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.tgc.open.entity.BizIdType;
import com.dianping.tgc.open.entity.CouponBizIdQueryRequest;
import org.springframework.beans.factory.annotation.Autowired;

public class VoucherProcessor extends AbsDealProcessor {

    @Autowired
    private PromoWrapper promoWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        return ctx.getDpId() > 0;
    }

    @Override
    public void prepare(DealCtx ctx) {
        ctx.getFutureCtx().setVoucherFuture(promoWrapper.preVoucherFuture(wrapCouponBizIdQueryRequest(ctx)));
    }

    @Override
    public void process(DealCtx ctx) {
        ctx.setVoucherList(promoWrapper.queryQueryResponseDTO(ctx.getFutureCtx().getVoucherFuture()));
    }


    private static CouponBizIdQueryRequest wrapCouponBizIdQueryRequest(DealCtx ctx) {

        CouponBizIdQueryRequest request = new CouponBizIdQueryRequest();
        request.setBizId(ctx.getDpId());
        request.setBizIdType(BizIdType.DEAL_GROUP_ID.getCode());
        request.setMt(ctx.isMt());
        request.setUserId(ctx.isMt() ? ctx.getEnvCtx().getMtUserId() : ctx.getEnvCtx().getDpUserId());

        return request;
    }
}
