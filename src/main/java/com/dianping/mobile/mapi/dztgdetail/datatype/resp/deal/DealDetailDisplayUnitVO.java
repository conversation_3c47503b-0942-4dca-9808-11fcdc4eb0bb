package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@TypeDoc(description = "团详展示单元")
@MobileDo(id = 0x903c)
public class DealDetailDisplayUnitVO implements Serializable {

    @FieldDoc(description = "展示样式")
    @MobileField(key = 0x8f0c)
    private String type;

    @FieldDoc(description = "组件名")
    @MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "展示条目列表")
    @MobileField(key = 0x570f)
    private List<BaseDisplayItemVO> displayItems;

}
