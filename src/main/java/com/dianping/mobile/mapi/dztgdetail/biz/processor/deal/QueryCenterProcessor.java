package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.FreeDealEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealTechCtx;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.utils.FaultToleranceUtils;
import com.dianping.mobile.mapi.dztgdetail.util.FreeDealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.onelimiter.LimitResult;
import com.dianping.rhino.onelimiter.OneLimiter;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.nibscp.common.api.enums.TradeTypeEnum;
import com.sankuai.general.product.query.center.client.builder.model.*;
import com.sankuai.general.product.query.center.client.builder.requset.BaseRequestBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.AttrSubjectEnum;
import com.sankuai.general.product.query.center.client.enums.AttrTypeEnum;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.enums.PurchaseNoteSceneEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.util.DealGroupUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Future;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.APP_KEY;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.RHINO_LIMITED_SWITCH;

public class QueryCenterProcessor extends AbsDealProcessor {

    private static final List<String> DEAL_GROUP_ATTR_KEYS = Lists.newArrayList(
            "times_available_all", "product_finish_date", "support_shop_service",
            "female_only", "oral_dentistry_tuanxiang_rule", "product_channel_id_allowed",
            "pet-extra", "eduSuitableAge", "tort", "product_can_use_coupon", "preSaleTag",
            "product_discount_rule_id", "sys_deal_universal_type", "reservation_number", "reservation_is_needed_or_not_2",
            "reservation_is_needed_or_not_3", "voucher_limit_of_using_2", "rawDealGroupPrice", "calc_holiday_available",
            "product_business_type", "voucher_limit_of_using_1", "product_third_party_verify", "tooth_suit_people",
            "standardDealGroup", "product_block_stock", "service_type", "support_home_service", "hide_type",
            "isselecteddeal", "reservation_is_needed_or_not", "category", "token_time_use_limit_1", "warmUpStartTime",
            "usingStockPlan", "limit_of_using_each_eye", "available_time",
            "hairstylist_level", "selling_point", "eyelash_suit_part", "eyelash_after_sale",
            "tag_unifyProduct","px_suitable_age", "suitable_crowd", "checkup_sex", "px_additional_service", "physical_examination_get_result_time",
            "include_physical_cards","include_a_training_certificate","physical_examination_get_result_time","physical_card_checkout_time",
            "electronic_certificate_inquiry_method","physical_card_collection_method","dealGroupFitnessPassConfig"
    );

    private static final List<String> DEAL_ATTR_KEYS = Lists.newArrayList("sku_receipt_type");

    private static OneLimiter oneLimiter = Rhino.newOneLimiter();
    private static String ENTRANCE = "/general/platform/dztgdetail/dzdealbase.bin";

    private static final String PIPING_SERVICE_DEAL_GROUP_ATTR_KEY = "standardDealGroupKey";

    private static final String CLEANING_SELF_OWN_DEAL_GROUP_ATTR_KEY = "self_own_product";

    /**
     * 管道疏通类型 - 服务商团单 - 属性value列表
     */
    public static final String PIPING_SERVICE_DEAL_GROUP_ATTR_VALUE = "dztrade-mapi-web.piping.service.deal.group.attr.value";
    public static final String DZ_TRADE_MAPI_WEB_APP_KEY = "dztrade-mapi-web";

    @Autowired
    private QueryCenterWrapper queryCenterWrapper;

    /**
     * @param ctx context
     * @return boolean
     */
    @Override
    public boolean isEnable(DealCtx ctx) {
        boolean useQueryCenter = GreyUtils.isQueryCenterGreyBatch3(ctx.isMt() ? ctx.getMtId() : ctx.getDpId());
        ctx.setUseQueryCenter(useQueryCenter);
        return useQueryCenter;
    }

    public static boolean openQueryCenterCheckDealGroupShopRelation() {
        return Lion.getBoolean(APP_KEY, "com.sankuai.dzu.tpbase.dztgdetailweb.openQueryCenterCheckDealGroupShopRelation", false);
    }

    @Override
    public void prepare(DealCtx ctx) {
        int dealGroupId = ctx.isMt() ? ctx.getMtId() : ctx.getDpId();
        long shopId = ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId();
        IdTypeEnum idTypeEnum = ctx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP;
        Map<Long, Set<Long>> deal2shop = Maps.newHashMap();
        deal2shop.put((long) dealGroupId, Sets.newHashSet(shopId));

        PurchaseNoteBuilder purchaseNoteBuilder = PurchaseNoteBuilder.builder().all();
        if (StringUtils.isNotBlank(ctx.getSkuId())) {
            purchaseNoteBuilder
                    .scenes(Lists.newArrayList(PurchaseNoteSceneEnum.PRODUCT_PURCHASE_NOTE, PurchaseNoteSceneEnum.ORDER_DETAIL_USE_INFORMATION))
                    .dpShopId(ctx.getDpLongShopId())
                    .mtShopId(ctx.getMtLongShopId())
                    .dealId(Long.valueOf(ctx.getSkuId()));
        }
        BaseRequestBuilder<QueryByDealGroupIdRequest> builder = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet((long) dealGroupId), ctx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP)
                .dealGroupId(DealGroupIdBuilder.builder().all())
                .channel(DealGroupChannelBuilder.builder().all())
                .basicInfo(DealGroupBasicInfoBuilder.builder().all())
                .attrsByKey(AttrSubjectEnum.DEAL_GROUP, getQueryCenterDealGroupAttrKey())
                .attrsByKey(AttrSubjectEnum.DEAL, getQueryCenterDealAttrKey())
                .attrsByType(AttrSubjectEnum.DEAL, AttrTypeEnum.SALE_PROP)
                .dealGroupStock(DealGroupStockBuilder.builder().all())
                .dealStock(DealStockBuilder.builder().all())
                .customer(DealGroupCustomerBuilder.builder().all())
                .region(DealGroupRegionBuilder.builder().all())
                .detail(DealGroupDetailBuilder.builder().all())
                .dealBasicInfo(DealBasicInfoBuilder.builder().all())
                .dealRule(DealRuleBuilder.builder().readjustPriceRule())
                .rule(DealGroupRuleBuilder
                        .builder()
                        .refundRule()
                        .buyRule()
                        .useRule(DealGroupUtils.convertDate2String(new Date())))
                .image(DealGroupImageBuilder.builder().all())
                .dealGroupPrice(DealGroupPriceBuilder.builder().all())
                .dealPrice(DealPriceBuilder.builder().all())
                .dealPrepayPrice(DealPrepayPriceBuilder.builder().prePayPrice().finalPayPrice())
                .category(DealGroupCategoryBuilder.builder().all())
                .displayShop(DealGroupDisplayShopBuilder.builder().all())
                .dealGroupTag(DealGroupTagBuilder.builder().all())
                .serviceProject(ServiceProjectBuilder.builder().all())
                .dealGroupSaleChannelAggregation(DealGroupSaleChannelAggregationBuilder.builder().all())
                .purchaseNote(purchaseNoteBuilder)
                .metaObjectInfo(DealGroupMetaObjectInfoBuilder.builder().all());
        if (openQueryCenterCheckDealGroupShopRelation()) {
            builder.checkDealGroupShopRelation(DealGroupShopRelationCheckBuilder.builder().shopIdType(idTypeEnum.getCode()).checkDisplayShop(deal2shop));
        }
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = builder.build();
        Future singleDealGroupDtoFuture = queryCenterWrapper.preDealGroupDTO(queryByDealGroupIdRequest);
        ctx.getFutureCtx().setSingleDealGroupDtoFuture(singleDealGroupDtoFuture);
    }

    @Override
    public void process(DealCtx ctx) {
        DealGroupDTO dealGroupDTO = null;
        try {
            dealGroupDTO = queryCenterWrapper.getDealGroupDTO(ctx.getFutureCtx().getSingleDealGroupDtoFuture());
        } catch (Exception e) {
            logger.error("queryCenterWrapper.getDealGroupDTO error, req:{}", JSON.toJSONString(ctx.getDealBaseReq()), e);
            ctx.setQueryCenterHasError(true);
            FaultToleranceUtils.addException("queryByDealGroupIds", e);
        }
        ctx.setDealGroupDTO(dealGroupDTO);
        //根据团单分类ID获取限流
        if (Lion.getBoolean(APP_KEY, RHINO_LIMITED_SWITCH, false)) {
            putRhinoReject(ctx);
        }

        // 初始化ctx免费团单信息
        initFreeDealCtx(ctx);

        // 初始化自营团单信息
        fillSelfDealInfo(ctx, dealGroupDTO);
    }

    private void fillSelfDealInfo(DealCtx ctx, DealGroupDTO dealGroupDTO) {
        if (Objects.isNull(dealGroupDTO)) {
            return;
        }
        List<AttrDTO> attrs = dealGroupDTO.getAttrs();
        if (CollectionUtils.isEmpty(attrs)) {
            return;
        }
        for (AttrDTO attr : attrs) {
            List<String> dealAttributeList = attr.getValue();
            if (CollectionUtils.isEmpty(dealAttributeList)){
                continue;
            }
            if (CLEANING_SELF_OWN_DEAL_GROUP_ATTR_KEY.equals(attr.getName()) && "是".equals(dealAttributeList.get(0))) {
                ctx.setCleaningSelfOwnDeal(true);
                return;
            }
            if (PIPING_SERVICE_DEAL_GROUP_ATTR_KEY.equals(attr.getName())) {
                List<String> serviceAttrValueList = Lion.getList(DZ_TRADE_MAPI_WEB_APP_KEY, PIPING_SERVICE_DEAL_GROUP_ATTR_VALUE, String.class, Lists.newArrayList());
                if (serviceAttrValueList.stream().anyMatch(dealAttributeList::contains)){
                    ctx.setCareFreeDeal(true);
                    return;
                }
            }
        }
    }

    private void initFreeDealCtx(DealCtx ctx) {
        if (Objects.isNull(ctx.getDealGroupDTO()) || Objects.isNull(ctx.getDealGroupDTO().getCategory())) {
            ctx.setFreeDeal(false);
            return;
        }
        if (!FreeDealUtils.inCategoryList(ctx.getDealGroupDTO().getCategory().getCategoryId())) {
            ctx.setFreeDeal(false);
            return;
        }
        if (Objects.isNull(ctx.getDealGroupDTO().getBasic())) {
            ctx.setFreeDeal(false);
            return;
        }
        if (Objects.isNull(ctx.getDealGroupDTO().getBasic().getTradeType()) || ctx.getDealGroupDTO().getBasic().getTradeType() != TradeTypeEnum.RESERVATION.getCode()) {
            ctx.setFreeDeal(false);
            return;
        }
        ctx.setFreeDeal(true);
        ctx.setFreeDealType(FreeDealEnum.fromDealCategory(String.valueOf(ctx.getDealGroupDTO().getBasic().getCategoryId())));
        ctx.setFreeDealConfig(FreeDealUtils.getFreeDealConfig(ctx.getFreeDealType()));
    }

    private void putRhinoReject(DealCtx ctx) {
        if (ctx.getDealGroupDTO() == null){
            return;
        }
        Map<String, String> params = Maps.newHashMap();
        params.put("categoryId", String.valueOf(ctx.getDealGroupDTO().getCategory().getCategoryId()));
        LimitResult run = oneLimiter.run(ENTRANCE, params);
        if (ctx.getDealTechCtx() == null) {
            ctx.setDealTechCtx(new DealTechCtx());
        }

        ctx.getDealTechCtx().setRhinoReject(run.isReject());
    }

    public static Set<String> getQueryCenterDealGroupAttrKey() {
        List<String> queryCenterDealGroupAttrKey = Lion.getList(LionConstants.APP_KEY,
                LionConstants.QUERY_CENTER_DEALGROUP_ATTR_KEYS, String.class, DEAL_GROUP_ATTR_KEYS);

        return new HashSet<>(queryCenterDealGroupAttrKey);
    }

    public static Set<String> getQueryCenterDealAttrKey() {
        List<String> queryCenterDealGroupAttrKey = Lion.getList(LionConstants.APP_KEY,
                LionConstants.QUERY_CENTER_DEAL_ATTR_KEYS, String.class, DEAL_ATTR_KEYS);

        return new HashSet<>(queryCenterDealGroupAttrKey);
    }
}
