package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.annotation.MobileRequest.Param;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.DecryptedField;

import java.io.Serializable;

@TypeDoc(description = "好评度情请求参数")
@MobileRequest
public class GoodReviewReq implements IMobileRequest, Serializable {

    @Deprecated
    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，原int类型")
    @Param(name = "dealgroupid")
    private Integer dealgroupid;

    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，string类型")
    @Param(name = "stringdealgroupid")
    private String stringDealGroupId;

    @FieldDoc(description = "站点：APP可以不传，值参见后端枚举",rule = "@see com.dianping.deal.common.enums.ClientTypeEnum")
    @Param(name = "clienttype")
    private Integer clienttype;

    @FieldDoc(description = "门店id，美团传美团门店id，点评传点评门店id", rule = "美团传美团门店id，点评传点评门店id")
    @Param(name = "poiid")
    private String poiid;
    @Param(name = "poiidEncrypt")
    @DecryptedField(targetFieldName = "poiid")
    private String poiidEncrypt;

    @FieldDoc(description = "前端mrn版本")
    @Param(name = "mrnversion")
    private String mrnVersion;


    public Integer getDealgroupid() {
        return dealgroupid != null ? dealgroupid : 0;
    }

    public void setDealgroupid(Integer dealgroupid) {
        this.dealgroupid = dealgroupid;
    }

    public Integer getClienttype() {
        return clienttype != null ? clienttype : 0;
    }

    public void setClienttype(Integer clienttype) {
        this.clienttype = clienttype;
    }

    public String getPoiid() {
        return poiid;
    }

    public void setPoiid(String poiid) {
        this.poiid = poiid;
    }

    public String getMrnVersion() {
        return mrnVersion;
    }

    public void setMrnVersion(String mrnVersion) {
        this.mrnVersion = mrnVersion;
    }

    public String getPoiidEncrypt() {
        return poiidEncrypt;
    }

    public void setPoiidEncrypt(String poiidEncrypt) {
        this.poiidEncrypt = poiidEncrypt;
    }

    public String getStringDealGroupId() {
        return stringDealGroupId;
    }

    public void setStringDealGroupId(String stringDealGroupId) {
        this.stringDealGroupId = stringDealGroupId;
    }
}
