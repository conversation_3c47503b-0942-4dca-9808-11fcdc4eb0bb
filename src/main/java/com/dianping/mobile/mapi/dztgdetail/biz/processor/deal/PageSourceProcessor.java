package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.CatEvents;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.PageSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.entity.PageSourceInfoConfig;
import com.dianping.mobile.mapi.dztgdetail.util.PageSourceGrayUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2023/12/8
 */
public class PageSourceProcessor extends AbsDealProcessor {
    public static final String INVALID_PAGE_SOURCE_ID_STR = "-1";

    @Override
    public boolean isEnable(DealCtx ctx) {
        return PageSourceGrayUtils.hit(ctx.isMt() ? ctx.getMtId() : ctx.getDpId(), ctx.getCategoryId());
    }

    @Override
    public void prepare(DealCtx ctx) {
        List<PageSourceInfoConfig> pageSourceInfoConfigs =  Lion.getList(LionConstants.PAGE_SOURCE_INFO_CONFIG, PageSourceInfoConfig.class, Lists.newArrayList());
        if (CollectionUtils.isEmpty(pageSourceInfoConfigs)) {
            return;
        }
        Map<String, Integer> nameIdMap = pageSourceInfoConfigs.stream().collect(Collectors.toMap(PageSourceInfoConfig::getName, PageSourceInfoConfig::getId, (o1, o2) -> o1));

        /**
         * 平台侧渠道匹配，待接入对应SDK
         * 如果命中平台侧渠道，打点platform_page_source
         * logMetricForPlatformPageSource(ctx, nameIdMap);
         */

        // 团详侧渠道校验
        PageSourceInfoConfig pageSourceInfoConfig = pageSourceInfoConfigs.stream().filter(config -> filterRequestSource(config, ctx)).findFirst().orElse(null);
        if (Objects.isNull(pageSourceInfoConfig)) { // 表示不在配置的渠道内
            // 非法渠道打点
            logMetricForInvalidPageSource(ctx, nameIdMap);
        } else {
            // 已配置渠道
            logMetricForDztgPageSource(ctx, nameIdMap);
        }
    }

    private boolean filterRequestSource(PageSourceInfoConfig config, DealCtx ctx) {
        if (Objects.isNull(ctx.getRequestSource()) && config.getName().equals(PageSourceEnum.PS_UNKNOWN.getName())) {
            return true;
        }
        return config.getName().equals(ctx.getRequestSource());
    }

    @Override
    public void process(DealCtx ctx) {

    }


//    /**
//     *  记录平台侧渠道来源的指标
//     *
//     * @param ctx
//     * @param nameIdMap
//     */
//    private void logMetricForPlatformPageSource(DealCtx ctx, Map<String, Integer> nameIdMap) {
//        Map<String, String> tags = Maps.newHashMap();
//        if (ctx.getRequestSource() != null && MapUtils.isNotEmpty(nameIdMap) && nameIdMap.containsKey(ctx.getRequestSource())) {
//            tags.put("pageSource", String.valueOf(nameIdMap.get(ctx.getRequestSource())));
//        }
//        Cat.logMetricForCount(CatEvents.PLATFORM_PAGE_SOURCE_ID, tags);
//        Cat.logMetricForCount(CatEvents.PAGE_SOURCE_ID, tags);
//
//        tags = Maps.newHashMap();
//        if (ctx.getRequestSource() != null) {
//            tags.put("pageSource", ctx.getRequestSource());
//        }   // 平台渠道理论上无unknown值
//        Cat.logMetricForCount(CatEvents.PLATFORM_PAGE_SOURCE_NAME, tags);
//        Cat.logMetricForCount(CatEvents.PAGE_SOURCE_NAME, tags);
//    }

    /**
     *  记录到综团详侧渠道来源的指标
     *
     * @param ctx
     * @param nameIdMap
     */
    private void logMetricForDztgPageSource(DealCtx ctx, Map<String, Integer> nameIdMap) {
        Map<String, String> tags = Maps.newHashMap();
        if (ctx.getRequestSource() != null && MapUtils.isNotEmpty(nameIdMap) && nameIdMap.containsKey(ctx.getRequestSource())) {
            tags.put("pageSource", String.valueOf(nameIdMap.get(ctx.getRequestSource())));
        } else if (ctx.getRequestSource() == null) {
            tags.put("pageSource", String.valueOf(PageSourceEnum.PS_UNKNOWN.getId()));
        } else {
            tags.put("pageSource", INVALID_PAGE_SOURCE_ID_STR);
        }
        Cat.logMetricForCount(CatEvents.DZTG_PAGE_SOURCE_ID, tags);
        Cat.logMetricForCount(CatEvents.PAGE_SOURCE_ID, tags);

        tags = Maps.newHashMap();
        if (ctx.getRequestSource() != null) {
            tags.put("pageSource", ctx.getRequestSource());
        } else {
            tags.put("pageSource", String.valueOf(PageSourceEnum.PS_UNKNOWN.getName()));
        }
        Cat.logMetricForCount(CatEvents.DZTG_PAGE_SOURCE_NAME, tags);
        Cat.logMetricForCount(CatEvents.PAGE_SOURCE_NAME, tags);
    }

    private void logMetricForInvalidPageSource(DealCtx ctx, Map<String, Integer> nameIdMap) {
        Map<String, String> tags = Maps.newHashMap();
        if (ctx.getRequestSource() != null) {   // 表示传了渠道值，但是不在配置渠道值中
            tags.put("pageSource", INVALID_PAGE_SOURCE_ID_STR);
        } else if (StringUtils.isBlank(ctx.getRequestSource())) {   // 表示未传渠道值，显然也不在配置渠道值中
            tags.put("pageSource", String.valueOf(nameIdMap.get(PageSourceEnum.PS_UNKNOWN.getName())));
        }
        Cat.logMetricForCount(CatEvents.INVALID_PAGE_SOURCE_ID, tags);

        tags = Maps.newHashMap();
        if (ctx.getRequestSource() != null) {
            tags.put("pageSource", ctx.getRequestSource());
        } else {
            tags.put("pageSource", "unknown");
        }
        Cat.logMetricForCount(CatEvents.INVALID_PAGE_SOURCE_NAME, tags);
    }
}
