package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.deal.detail.enums.PlatformEnum;
import com.dianping.mobile.mapi.dztgdetail.biz.immersiveimage.ImmersiveImageService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ExhibitImageSceneEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageVO;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-08-25
 * @desc 实现查询沉浸页款式信息
 */
@Component
public class ImmersiveImageFacade {
    @Autowired
    private List<ImmersiveImageService> immersiveImageServices;

    @Autowired
    private DealGroupWrapper dealGroupWrapper;
    @Autowired
    private MapperWrapper mapperWrapper;

    /**
     * 获取沉浸页款式信息
     *
     * @param request 请求体
     * @return 沉浸页款式信息
     */
    public ImmersiveImageVO getImmersiveImage(GetImmersiveImageRequest request, EnvCtx envCtx) {
        // 参数校验
        checkRequest(request);
        // 默认设置查询本团购参考款式
        if (StringUtils.isBlank(request.getSceneCode())) {
            request.setSceneCode(ExhibitImageSceneEnum.SELF.getSceneCode());
        }
        // 转成点评团单ID，提升商家平台查询效率
        int dpDealGroupId = envCtx.isMt() ? dealGroupWrapper.getDpDealGroupId(request.getDealGroupId()) : request.getDealGroupId();
        int mtDealGroupId = envCtx.isMt() ? request.getDealGroupId() : mapperWrapper.getMtIdByDpIdMtIdMapper(mapperWrapper.preDpIdMtIdMapper(request.getDealGroupId()));
        int categoryId = dealGroupWrapper.getCategoryId(dpDealGroupId);
        String sceneCodeUniqueKey = String.format("%s.%s", categoryId, request.getSceneCode());
        // 校验开关，如果场景开关关闭，则直接返回
        if (!LionConfigUtils.getImmersiveImageSceneSwitch(sceneCodeUniqueKey)) {
            return null;
        }

        request.setMtDealGroupId((long)mtDealGroupId);
        if (!envCtx.isMt()) {
            request.setDpDealGroupId(request.getDealGroupId().longValue());
        }
        request.setDealGroupType(envCtx.isMt() ? PlatformEnum.MEI_TUAN.getType() : PlatformEnum.DIAN_PING.getType());
        request.setDealGroupId(dpDealGroupId);
        request.setCategoryId(categoryId);
        for (ImmersiveImageService immersiveImageService : immersiveImageServices) {
            if (immersiveImageService.getCategoryIds().contains(categoryId)) {
                return immersiveImageService.getImmersiveImage(request, envCtx);
            }
        }
        return null;
    }

    private void checkRequest(GetImmersiveImageRequest req) {
        Validate.isTrue(Objects.nonNull(req.getDealGroupId()) && req.getDealGroupId() > 0);
        Validate.isTrue(Objects.nonNull(req.getLimit()) && req.getLimit() > 0);
        Validate.isTrue(Objects.nonNull(req.getStart()) && req.getStart() >= 0);
        Validate.isTrue(Objects.nonNull(req.getShopId()) && req.getShopId() > 0);
        if (req.getLimit() > 50) {
            req.setLimit(50);
        }
    }
}
