package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.mobile.mapi.dztgdetail.common.constants.SaleConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.exception.DealGroupResultException;
import com.dianping.mobile.mapi.dztgdetail.exception.DealMatchSalePlatformException;
import com.dianping.mobile.mapi.dztgdetail.exception.QueryCenterResultException;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;

import java.util.Objects;

public class DealSalesPlatformProcessor extends AbsDealProcessor {

    @Override
    public boolean isEnable(DealCtx ctx) {
        return !RequestSourceEnum.fromTradeSnapshot(ctx.getRequestSource());
    }

    @Override
    public void prepare(DealCtx ctx) {
    }

    @Override
    public void process(DealCtx ctx) {
        if(GreyUtils.enableQueryCenterForMainApi(ctx)) {
            processByQueryCenter(ctx);
        } else {
            DealGroupBaseDTO dealGroupBase = ctx.getDealGroupBase();
            if (dealGroupBase == null) {
                throw new DealGroupResultException("deal group base info not found");
            }
            if (dealGroupBase.getSalePlatform() == SaleConstants.DOUBLE_PLATFORM) {
                return;
            }
            boolean isMatch = ctx.isMt() ? dealGroupBase.getSalePlatform() == SaleConstants.MT_ONLY :
                    dealGroupBase.getSalePlatform() == SaleConstants.DP_ONLY;

            if (!isMatch) {
                ctx.setDealGroupBase(null);
                ctx.setEnd(true);
                String msg = String.format("request client [%s] not match deal sales platform [%s]",
                        ctx.getEnvCtx().getClientType(), dealGroupBase.getSalePlatform());
                throw new DealMatchSalePlatformException(msg);
            }
        }
    }

    private void processByQueryCenter(DealCtx ctx) {
        if (ctx == null || ctx.getDealGroupDTO() == null || ctx.getDealGroupDTO().getBasic() == null) {
            throw new QueryCenterResultException("queryCenter dealGroupDTO basic info not found");
        }
        DealGroupBasicDTO dealGroupBase = ctx.getDealGroupDTO().getBasic();

        if (Objects.equals(SaleConstants.DOUBLE_PLATFORM,dealGroupBase.getSalePlatform())) {
            return;
        }
        boolean isMatch = ctx.isMt() ? Objects.equals(SaleConstants.MT_ONLY,dealGroupBase.getSalePlatform()) :
                Objects.equals(SaleConstants.DP_ONLY,dealGroupBase.getSalePlatform());

        if (!isMatch) {
            ctx.setDealGroupBase(null);
            ctx.setEnd(true);
            String msg = String.format("request client [%s] not match deal sales platform [%s], dealGroupId=%s",
                    ctx.getEnvCtx().getClientType(), dealGroupBase.getSalePlatform(), ctx.getDealId4P());
            throw new DealMatchSalePlatformException(msg);
        }
    }
}
