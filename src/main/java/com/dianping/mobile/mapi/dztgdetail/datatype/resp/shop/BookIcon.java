package com.dianping.mobile.mapi.dztgdetail.datatype.resp.shop;


import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: zhangyuan103
 * @Date: 2025/5/13
 */
@Data
@NoArgsConstructor
public class BookIcon implements Serializable {
    /**
     * 展示的类型
     */
    @MobileField
    private int type;

    /**
     * 预订按钮的文案
     */
    @MobileField
    private String text;

    public BookIcon(int type) {
        this.type = type;
    }

    public BookIcon(int type, String text) {
        this.type = type;
        this.text = text;
    }
}
