package com.dianping.mobile.mapi.dztgdetail.faulttolerance.utils;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.base.datatypes.HttpCode;
import com.dianping.mobile.framework.base.datatypes.StatusCode;
import com.dianping.mobile.mapi.dztgdetail.common.constants.CatEvents;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.sankuai.athena.stability.faulttolerance.exception.FaultToleranceException;
import com.sankuai.athena.stability.faulttolerance.tracer.ExceptionTracer;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-10-25
 */
public class FaultToleranceUtils {
    /**
     * 是否命中容错降级表达式
     */
    public static boolean hit(DealBaseReq request, String expression) {
        if (StringUtils.isBlank(expression)) {
            return false;
        }
        try {
            Expression compiledExpression = AviatorEvaluator.getInstance().compile(expression, true);
            Object value = compiledExpression.execute(toMap(request));
            if (value instanceof Boolean) {
                return (Boolean) value;
            }
        } catch (Exception e) {
            Cat.logError(e);
        }
        return false;
    }
    
    private static Map<String, Object> toMap(DealBaseReq request) {
        if ( Objects.isNull(request)) {
            return Maps.newHashMap();
        }
        Map<String, Object> map = Maps.newHashMap();
        map.put("request", request);
        return map;
    }

    /**
     * 判断返回状态码和数据符不符合期望，触发降级异常
     * @param statusCode 返回值中的状态码
     * @param data 返回数据
     */
    public static void triggerFaultTolerantByUnExpectResult(String methodName, int statusCode, Object data) {
        Map<String, String> metricTags = Maps.newHashMap();
        metricTags.put("methodName", methodName);
        boolean noData = Objects.isNull(data);
        boolean hasException = ExceptionTracer.hasException();
        boolean statusCodeErr = statusCode != HttpCode.HTTP_STATUS_OK;
        boolean happenDegrade = noData || hasException || statusCodeErr;
        // 上报降级监控数据
        metricTags.put("noData", String.valueOf(noData));
        metricTags.put("hasException", String.valueOf(hasException));
        metricTags.put("statusCodeErr", String.valueOf(statusCodeErr));
        metricTags.put("happenDegrade", String.valueOf(happenDegrade));
        Cat.logMetricForCount(CatEvents.DZ_DEAL_INTO_DEGRADE, metricTags);
        if (happenDegrade) {
            throw new FaultToleranceException();
        }
    }


    /**
     * 添加异常，用于识别是否进行接口降级
     * @param methodName 下游方法名
     * @param throwable 下游方法调用异常
     */
    public static void addException(String methodName, Throwable throwable) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.faulttolerance.utils.FaultToleranceUtils.addException(java.lang.String,java.lang.Throwable)");
        if (StringUtils.isBlank(methodName) || Objects.isNull(throwable)) {
            return;
        }
        String interfacesConfig = Lion.getString(LionConstants.APP_KEY, LionConstants.FAULT_TOLERANCE_INTERFACES_EXCEPTION);
        Map<String, List<String>> interface2ExceptionMap = JsonCodec.decode(interfacesConfig, new TypeReference<Map<String, List<String>>>() {});
        if (interface2ExceptionMap.containsKey(methodName)) {
            List<String> exceptions = interface2ExceptionMap.get(methodName);
            if (exceptions.contains("Exception") || exceptions.contains(throwable.getClass().getName())) {
                ExceptionTracer.addException(methodName, throwable);
                Cat.logError(CatEvents.CAUSE_DEGRADE_ERROR, throwable);
            }
        }
    }

    /**
     * Pearl 框架会对返回状态码地址进行判断，所以降级后的状态码对象需要替换
     * @param code 返回状态码
     * @return statusCode 对象
     */
    public static StatusCode getStatusCode(int code) {
        switch (code) {
            case HttpCode.HTTP_ILLEGAL_NEWTOKEN_KEY:
                return HttpCode.NEWTOKEN;
            case HttpCode.HTTP_INVALID_ENCRYPT_KEY:
                return HttpCode.ENCRYPT;
            case HttpCode.HTTP_REDIRECT_LOGIN:
                return HttpCode.REDIRECT_LOGIN;
            case HttpCode.HTTP_STATUS_OK:
                return HttpCode.HTTPOK;
            default:
                return StatusCode.code(code);
        }
    }
}