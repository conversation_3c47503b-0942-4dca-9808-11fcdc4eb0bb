package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.google.common.collect.Sets;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupIdBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.Future;

//各类MTid映射到Dpid
public class MapperProcessor extends AbsDealProcessor {

    @Autowired
    private QueryCenterWrapper queryCenterWrapper;

    @Autowired
    private DealGroupWrapper dealGroupWrapper;

    @Autowired
    private MapperWrapper mapperWrapper;

    //目前只有美团的请求，才需要做双平台ID的映射
    @Override
    public boolean isEnable(DealCtx ctx) {
        return ctx.getEnvCtx().isMt() && ctx.getMtId() > 0;
    }

    @Override
    public void prepare(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.MapperProcessor.prepare(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        if(GreyUtils.enableQueryCenterForMainApi(ctx)) {
            prepareByQueryCenter(ctx);
        } else {
            Future dealIdMapperFuture =  dealGroupWrapper.preDpDealGroupId(ctx.getMtId());
            Future shopIdMapperFuture =  mapperWrapper.preDpShopIdByMtShopId(ctx.getMtLongShopId());
            Future cityIdMapperFuture =  mapperWrapper.preDpCityByMtCity(ctx.getMtCityId());

            int dpId = dealGroupWrapper.getDpDealGroupId(dealIdMapperFuture);
            long dpShopId = mapperWrapper.getDpShopIdByMtShopIdLong(shopIdMapperFuture);
            int dpCityId = mapperWrapper.getDpCityByMtCity(cityIdMapperFuture);

            ctx.setDpId(dpId);
            ctx.setDpShopId(dpShopId > Integer.MAX_VALUE ? 0 : (int) dpShopId);
            ctx.setDpLongShopId(dpShopId);
            ctx.setDpCityId(dpCityId);
        }
    }

    @Override
    public void process(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.MapperProcessor.process(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
    }

    private void prepareByQueryCenter(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.MapperProcessor.prepareByQueryCenter(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        Future shopIdMapperFuture =  mapperWrapper.preDpShopIdByMtShopId(ctx.getMtLongShopId());
        Future cityIdMapperFuture =  mapperWrapper.preDpCityByMtCity(ctx.getMtCityId());

        int dpId = getDpDealGroupId(ctx);
        long dpShopId = mapperWrapper.getDpShopIdByMtShopIdLong(shopIdMapperFuture);
        int dpCityId = mapperWrapper.getDpCityByMtCity(cityIdMapperFuture);

        ctx.setDpId(dpId);
        ctx.setDpShopId(dpShopId > Integer.MAX_VALUE ? 0 : (int) dpShopId);
        ctx.setDpLongShopId(dpShopId);
        ctx.setDpCityId(dpCityId);
    }

    private int getDpDealGroupId(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.MapperProcessor.getDpDealGroupId(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        int dealGroupId = ctx.isMt() ? ctx.getMtId() : ctx.getDpId();
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet((long)dealGroupId), ctx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP)
                .dealGroupId(DealGroupIdBuilder.builder().dpDealGroupId())
                .build();

        DealGroupDTO dealGroupDTO = null;
        try {
            dealGroupDTO = queryCenterWrapper.getDealGroupDTO(queryByDealGroupIdRequest);
        } catch (TException e) {
            logger.error("queryCenterWrapper.getDealGroupDTO error, request = " + GsonUtils.toJsonString(queryByDealGroupIdRequest), e);
        }
        if(dealGroupDTO == null || dealGroupDTO.getDpDealGroupIdInt() == null) {
            Future dealIdMapperFuture =  dealGroupWrapper.preDpDealGroupId(ctx.getMtId());
            return dealGroupWrapper.getDpDealGroupId(dealIdMapperFuture);
        }
        return dealGroupDTO.getDpDealGroupIdInt();
    }
}
