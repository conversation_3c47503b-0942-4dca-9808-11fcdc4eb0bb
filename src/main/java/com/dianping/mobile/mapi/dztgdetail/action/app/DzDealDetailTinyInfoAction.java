package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.CatEvents;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetDealTinyInfoRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealTinyInfoVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.DealBaseDo;
import com.dianping.mobile.mapi.dztgdetail.facade.DealTinyInfoFacade;
import com.dianping.mobile.mapi.dztgdetail.util.AntiCrawlerUtils;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-10-09
 * @desc 获取团详简要信息
 */
@InterfaceDoc(
        displayName = "获取团单简要信息接口", type = "restful", description = "获取团单简要信息，包含1:1头图、标题、副标题、到手价、门市价等",
        scenarios = "该接口适用于双平台App站点的比价浮层中当前团单的信息展示", host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "liuwen17"
)
@Controller("general/platform/dztgdetail/getdealgroupinfo.bin")
@Action(url = "getdealgroupinfo.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class DzDealDetailTinyInfoAction extends AbsAction<GetDealTinyInfoRequest> {
    @Resource
    private DealTinyInfoFacade dealTinyInfoFacade;

    @Override
    protected IMobileResponse validate(GetDealTinyInfoRequest request, IMobileContext context) {
        if (Objects.isNull(request) || Integer.parseInt(request.getDealGroupId()) < 0) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @Override
    @CryptMethod
    protected IMobileResponse execute(GetDealTinyInfoRequest request, IMobileContext context) {
        try {
            EnvCtx envCtx = initEnvCtx(context);
            DealTinyInfoVO dealTinyInfoVO = dealTinyInfoFacade.getDealTinyInfo(request, envCtx);
            if (Objects.nonNull(dealTinyInfoVO)) {
                Cat.logMetricForCount(CatEvents.DEAL_TINY_INFO_SUC);
                // 安全工单加固
                hideKeyInfo(dealTinyInfoVO, context);
                return new CommonMobileResponse(dealTinyInfoVO);
            }
            Cat.logMetricForCount(CatEvents.DEAL_TINY_INFO_NO_DATA);
            return new CommonMobileResponse(Resps.NoDataResp);
        } catch (Exception e) {
            log.error("getdealgroupinfo.bin error: {}", e);
        }
        Cat.logMetricForCount(CatEvents.DEAL_TINY_INFO_FAIL);
        return Resps.SYSTEM_ERROR;
    }

    private void hideKeyInfo(DealTinyInfoVO dealTinyInfoVO, IMobileContext ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.action.app.DzDealDetailTinyInfoAction.hideKeyInfo(com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealTinyInfoVO,com.dianping.mobile.framework.datatypes.IMobileContext)");
        if ( Objects.isNull(dealTinyInfoVO)) {
            return;
        }
        if (!AntiCrawlerUtils.hide(ctx)) {
            return;
        }
        dealTinyInfoVO.setFinalPrice("");
        dealTinyInfoVO.setMarketPrice("");
    }
    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
