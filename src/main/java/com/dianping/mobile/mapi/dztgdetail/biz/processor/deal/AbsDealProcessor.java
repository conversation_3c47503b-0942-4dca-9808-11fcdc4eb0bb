package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.Processor;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.entity.ProcessorConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.UrlProcessorDztgClient;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

public abstract class AbsDealProcessor implements Processor<DealCtx> {

    protected Logger logger = LoggerFactory.getLogger(AbsDealProcessor.class);

    @Override
    public boolean isEnable(DealCtx ctx) {
        return true;
    }

    @Override
    public boolean matchDztgClient(DealCtx ctx, Processor<DealCtx> processor) {
        List<Integer> clients = getConfigUrlDztgClient(ctx);
        if (CollectionUtils.isEmpty(clients)) { // 自定义端配置为空，校验lion端配置
            if (!matchLionConfigClient(ctx, processor)) {
                return false;
            }
        } else {    // 自定义端配置不为空
            if (!clients.contains(ctx.getEnvCtx().getDztgClientTypeEnum().getCode())) { // 当前端不在自定义端配置内
                return false;
            } else {    // 校验是否在lion端配置中
                if (!matchLionConfigClient(ctx, processor)) {
                    return false;
                }
            }
        }
        return true;
    }

    public boolean matchLionConfigClient(DealCtx ctx, Processor processor) {
        if (CollectionUtils.isEmpty(ctx.getUrlProcessorDztgClientList())) {
            return true;
        }

        UrlProcessorDztgClient urlProcessorDztgClient = ctx.getUrlProcessorDztgClientList().stream().filter(processorDztgClient -> ctx.getEnvCtx().getRequestURI().contains(processorDztgClient.getUrl())).findFirst().orElse(null);
        if (Objects.isNull(urlProcessorDztgClient) || urlProcessorDztgClient.getClientSwitch() == 0 || CollectionUtils.isEmpty(urlProcessorDztgClient.getProcessorConfigList())) {    // url未配置
            return true;
        }

        // 获取url对应的processor和端映射关系
        Map<String, List<Integer>> processorNameClientMap = urlProcessorDztgClient.getProcessorConfigList().stream().collect(Collectors.toMap(ProcessorConfig::getProcessorName, ProcessorConfig::getClientList));
        if (MapUtils.isEmpty(processorNameClientMap)) {
            return true;
        }
        List<Integer> clientList = processorNameClientMap.get(processor.getClass().getSimpleName());
        if (CollectionUtils.isNotEmpty(clientList) && !clientList.contains(ctx.getEnvCtx().getDztgClientTypeEnum().getCode())) {   // 对应processor已配置但是当前端不在配置端列表中
            return false;
        }
        return true;
    }


    @Override
    public List<Integer> getConfigUrlDztgClient(DealCtx ctx) {
        return Collections.emptyList();
    }

    @Override
    public boolean isEnd(DealCtx ctx) {
        return ctx.isEnd();
    }

    protected boolean isPromoProxyCategory(DealCtx ctx) {

        List<Integer> dealWhiteList = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb.promo.proxy.deal.white.list", Integer.class, new ArrayList<>());
        if (CollectionUtils.isNotEmpty(dealWhiteList)) {
            return dealWhiteList.contains(ctx.isMt() ? ctx.getMtId() : ctx.getDpId());
        }

        List<Integer> categories = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb.promo.proxy.category", Integer.class, new ArrayList<>());
        DealGroupChannelDTO channelDTO = ctx.getChannelDTO();
        return channelDTO != null && CollectionUtils.isNotEmpty(categories) && categories.contains(channelDTO.getCategoryId());
    }

    /**
     * 是否是驾校小车
     * @param ctx
     * @return
     */
    public boolean isDrivingSchoolCar(DealCtx ctx) {
        List<AttributeDTO> attrs = ctx.getAttrs();
        List<String> serviceTypeAttr = AttributeUtils.getAttributeValues(DealAttrKeys.SERVICE_TYPE, attrs);

        return ctx.getCategoryId() == 404 && CollectionUtils.isNotEmpty(serviceTypeAttr) && serviceTypeAttr.contains("小车");
    }
}
