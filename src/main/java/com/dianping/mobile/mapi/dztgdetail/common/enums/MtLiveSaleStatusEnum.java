package com.dianping.mobile.mapi.dztgdetail.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023-12-25
 * @desc 美团美播小程序上商品售卖状态
 */
@Getter
public enum MtLiveSaleStatusEnum {
    COMING_SOON(1, "COMING_SOON", "即将开售"),
    SNAP_UP_NOW(2, "SNAP_UP_NOW", "立即抢购"),
    OUT_OF_STOCK(3, "OUT_OF_STOCK", "已下架"),
    SOLD_OUT(4, "SOLD_OUT", "已抢光"),
    END_OF_SALE(5, "END_OF_SALE", "售卖结束"),
    ;

    final int code;
    final String statusName;
    final String statusDesc;

    MtLiveSaleStatusEnum(int code, String statusName, String statusDesc) {
        this.code = code;
        this.statusName = statusName;
        this.statusDesc = statusDesc;
    }


}
