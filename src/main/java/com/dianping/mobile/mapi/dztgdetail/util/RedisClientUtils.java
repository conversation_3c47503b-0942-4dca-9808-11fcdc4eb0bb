package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.cat.Cat;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.cache2.CacheClient;
import com.sankuai.athena.inf.cache2.CacheClientConfig;
import lombok.extern.slf4j.Slf4j;

import static java.util.Objects.hash;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/9/23 10:28
 */
@Slf4j
public class RedisClientUtils {
    // 初始化配置全局只能有一个,不然会出现同一个服务创建多个redis连接
    private static CacheClientConfig CACHE_CONFIG = new CacheClientConfig("redis-vc", "master-slave");

    private static CacheClientConfig cacheClientConfig = new CacheClientConfig("redis-dz-guide", 100);

    public static CacheClient getRedisCacheClient() {
        // AthenaInf.getCacheClient 内部实现本来就已经是单例的,所以此处不需要再实现单例
        return AthenaInf.getCacheClient(CACHE_CONFIG);
    }

    public static CacheClient getRedisCacheClientV2() {
        return AthenaInf.getCacheClient(cacheClientConfig);
    }
}
