package com.dianping.mobile.mapi.dztgdetail.button.coupon;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.constants.PlusIcons;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BannerTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBanner;
import com.dianping.mobile.mapi.dztgdetail.entity.CouponBannerInfo;
import com.dianping.mobile.mapi.dztgdetail.helper.BeautyBianMeiCouponHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.PriceHelper;
import com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class BeautyBianMeiCouponBannerBuilder extends AbstractButtonBuilder {

    private static final long MILLISECONDS_OF_DAY = 24 * 60 * 60 * 1000;

    @Override
    public void build(DealCtx context, ButtonBuilderChain chain) {
        if (DealBuyHelper.isBeautyBianMeiCoupon(context)) {
            buildBanner(context);
        }
        chain.build(context);
    }

    private void buildBanner(DealCtx context) {
        // 横条已经存在则不展示
        if (context.getBuyBar().getBuyBanner() != null) {
            return;
        }
        PriceDisplayDTO dealPromoPrice = PriceHelper.getDealPromoPrice(context);
        if (CollectionUtils.isEmpty(dealPromoPrice.getUsedPromos())) {
            return;
        }
        CouponBannerInfo couponBannerInfo = buildCouponBannerInfo(dealPromoPrice.getUsedPromos());
        if (couponBannerInfo.getShow() && couponBannerInfo.getEndTime() != null) {
            context.getBuyBar().setBuyBanner(buildCouponBanner(couponBannerInfo));
        }
    }

    @Override
    public void doBuild(DealCtx context, ButtonBuilderChain chain) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.button.coupon.BeautyBianMeiCouponBannerBuilder.doBuild(DealCtx,ButtonBuilderChain)");

    }

    /**
     * 构造横幅信息
     *
     * @param usedPromos
     * @return
     */
    private CouponBannerInfo buildCouponBannerInfo(List<PromoDTO> usedPromos) {
        CouponBannerInfo couponBannerInfo = new CouponBannerInfo();
        BigDecimal totalAmount = BigDecimal.ZERO;
        boolean show = false;
        Date endTime = new Date(Long.MAX_VALUE);
        for (PromoDTO usedPromo : usedPromos) {
            if (BeautyBianMeiCouponHelper.isBeautyBianMeiCouponPromo(usedPromo)) {
                show = true;
                totalAmount = totalAmount.add(usedPromo.getAmount());
                if (usedPromo.getEndTime() != null && usedPromo.getEndTime().before(endTime)) {
                    endTime = usedPromo.getEndTime();
                }
            }
        }

        couponBannerInfo.setEndTime(endTime);
        couponBannerInfo.setShow(show && endTime.before(new Date(System.currentTimeMillis() + 2L * MILLISECONDS_OF_DAY)));
        couponBannerInfo.setAmount(totalAmount);
        return couponBannerInfo;
    }

    private DealBuyBanner buildCouponBanner(CouponBannerInfo couponBannerInfo) {
        DealBuyBanner banner = new DealBuyBanner();
        banner.setShow(true);
        banner.setBannerType(BannerTypeEnum.BIANMEI.getType());
        banner.setContent(getBannerContent(couponBannerInfo));
        banner.setIconUrl(PlusIcons.BeautyBianMeiCoupon);
        banner.setCountDownTs(couponBannerInfo.getEndTime().getTime());
        return banner;
    }

    private String getBannerContent(CouponBannerInfo couponBannerInfo) {
        String amountStr = String.valueOf(couponBannerInfo.getAmount());
        return JsonLabelUtil.BeautyBianMeiCouponBannerJson(amountStr);
    }
}
