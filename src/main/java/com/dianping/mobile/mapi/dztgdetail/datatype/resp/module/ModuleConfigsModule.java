package com.dianping.mobile.mapi.dztgdetail.datatype.resp.module;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@MobileDo(id = 0xb129)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ModuleConfigsModule implements Serializable {
    /**
     * 侵权时的文案
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @MobileDo.MobileField(key = 0x1624)
    private String tortText;

    /**
     * 是否侵权
     */
    @MobileField(key = 0xffee)
    private boolean isTort;

    /**
     * 侵权404页面展示的小字
     */
    @MobileField(key = 0x5cc9)
    private String tortDesc;

    /**
     * 侵权404页面展示的title
     */
    @MobileField(key = 0xecb5)
    private String tortTitle;


    /**
     * 是否走点评提交订单流程
     */
    @MobileField(key = 0x8295)
    private boolean isDpOrder;

    /**
     * 是否请求点评单中心项目
     */
    @MobileField(key = 0xfce4)
    private boolean isDzx;

    /**
     * 模板key
     */
    @MobileField(key = 0x9e5e)
    private String key;

    /**
     * 模板额外的key
     */
    @MobileField(key = 0x143f)
    private String extraInfo;

    /**
     * 模板通用的key
     */
    @MobileField(key = 0x19eb)
    private String generalInfo;

    /**
     * 模块配置，通常用于A/B-TEST
     */
    @MobileField(key = 0xd233)
    private List<ModuleConfigDo> moduleConfigs;

    /**
     * AB测试，目前用于斗斛系统
     */
    @MobileField(key = 0xbae3)
    private List<DztgModuleAbConfig> moduleAbConfigs;

    /**
     * 预览模式字段，用于第三方预览模式的扩展
     */
    @MobileField(key = 0x2b92)
    private String previewInfo;



}