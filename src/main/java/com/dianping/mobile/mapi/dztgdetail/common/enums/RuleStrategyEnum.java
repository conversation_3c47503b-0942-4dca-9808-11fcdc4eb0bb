package com.dianping.mobile.mapi.dztgdetail.common.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024-04-07
 * @desc 策略的枚举
 */
@Getter
public enum RuleStrategyEnum {
    UNKNOWN("unknown", "未知规则"),
    IGNORE("ignore", "忽略此项配置"),
    ALLOW("allow", "在此配置中生效"),
    DENY("deny", "不在此配置中生效"),
    ;

    final String name;
    final String desc;

    RuleStrategyEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }
    
    public static RuleStrategyEnum of(String name) {
        for (RuleStrategyEnum strategy : values()) {
            if ( Objects.equals(strategy.getName(), name)) {
                return strategy;
            }
        }
        return UNKNOWN;
    }
}
