package com.dianping.mobile.mapi.dztgdetail.util;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
@Slf4j
public class UrlParser {

    public static Map<String, Object> parseUrl(String url) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 1. 解析协议
            String[] protocolParts = url.split("://", 2);
            String protocol = protocolParts[0];
            result.put("protocol", protocol);
            
            if (protocolParts.length == 1) {
                // 无协议部分（如相对路径）
                return result;
            }

            String remaining = protocolParts[1];
            
            // 2. 解析主机、路径和查询参数
            int hostEndIndex = remaining.indexOf('/');
            String host, pathQuery;
            
            if (hostEndIndex == -1) {
                // 无路径部分
                host = remaining.contains("?") ? "" : remaining;
                pathQuery = remaining.contains("?") ? remaining : "";
            } else {
                host = remaining.substring(0, hostEndIndex);
                pathQuery = remaining.substring(hostEndIndex);
            }
            result.put("host", host);

            // 3. 分离路径和查询参数
            String[] pathAndQuery = pathQuery.split("\\?", 2);
            String path = pathAndQuery[0];
            result.put("path", path.isEmpty() ? "/" : path);

            Map<String, String> queryParams = new HashMap<>();
            if (pathAndQuery.length > 1) {
                String query = pathAndQuery[1];
                parseQueryParams(query, queryParams);
            }
            result.put("queryParams", queryParams);

        } catch (Exception e) {
            log.error("URL 格式错误: " + url, e);
            return result;
        }
        
        return result;
    }

    private static void parseQueryParams(String query, Map<String, String> params) {
        String[] pairs = query.split("&");
        for (String pair : pairs) {
            String[] keyValue = pair.split("=", 2); // 处理含=的值
            String key = keyValue[0];
            String value = keyValue.length > 1 ? keyValue[1] : "";
            params.put(key, value);
        }
    }
}
