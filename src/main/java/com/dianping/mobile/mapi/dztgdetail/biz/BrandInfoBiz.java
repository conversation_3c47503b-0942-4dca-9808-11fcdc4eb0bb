package com.dianping.mobile.mapi.dztgdetail.biz;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiClientWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.PoiConsts;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.ViewListDo;
import com.dianping.mobile.mapi.dztgdetail.entity.DealBranchesParam;
import com.dianping.mobile.mapi.dztgdetail.helper.PoisRankHelper;
import com.google.common.collect.Lists;
import com.meituan.service.mobile.sinai.client.model.PoiModelL;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * Description:
 * Author: lijie
 * Version: 0.0.1
 * Created: 16/5/17 下午3:01
 */
@Component
public class BrandInfoBiz {

    @Resource
    private PoiClientWrapper poiClientWrapper;

    public List<PoiModelL> getPoiModelsByBrandId(int cityId, int brandId, DealBranchesParam dealBranchesParam, ViewListDo viewListDo) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.BrandInfoBiz.getPoiModelsByBrandId(int,int,DealBranchesParam,ViewListDo)");
        List<Long> poiIds = poiClientWrapper.getMtShopIdsByBrandIdAndMtCityId(brandId, cityId);
        if (CollectionUtils.isEmpty(poiIds)) {
            return Lists.newArrayList();
        }
        viewListDo.setRecordCount(poiIds.size());
        List<PoiModelL> poiModels = poiClientWrapper.listPoiL(poiIds, PoiConsts.defaultPoiFields);
        return PoisRankHelper.pick(poiModels, true, dealBranchesParam);
    }

}
