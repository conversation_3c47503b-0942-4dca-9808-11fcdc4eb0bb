package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.struct;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

@MobileDo(id = 0x4ca8)
public class InstructionStruct implements Serializable {
    /**
     * 说明的标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * 说明条目
     */
    @MobileDo.MobileField(key = 0xa497)
    private List<String> instructionItems;

    /**
     * 须知图片url
     */
    @MobileDo.MobileField(key = 0xb175)
    private String instructionPicUrl;

    /**
     * 说明的副标题
     */
    @MobileDo.MobileField(key = 0xd894)
    private String subTitle;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<String> getInstructionItems() {
        return instructionItems;
    }

    public void setInstructionItems(List<String> instructionItems) {
        this.instructionItems = instructionItems;
    }

    public String getInstructionPicUrl() {
        return instructionPicUrl;
    }

    public void setInstructionPicUrl(String instructionPicUrl) {
        this.instructionPicUrl = instructionPicUrl;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }
}