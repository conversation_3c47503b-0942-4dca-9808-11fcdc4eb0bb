package com.dianping.mobile.mapi.dztgdetail.button;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class ButtonBuilderFactory {

    private static final String DEFAULT_PACKAGE = "com.dianping.mobile.mapi.dztgdetail.button.";
    private static final Class<ButtonBuilder> BUILDER_INTERFACE = ButtonBuilder.class;
    private static final Map<String, Class<? extends ButtonBuilder>> BUILDER_CLAZZ_CACHE = Maps.newConcurrentMap();

    public static ButtonBuilder getBuilder(String builderName) {
        try {
            Class<?> builderClazz = BUILDER_CLAZZ_CACHE.get(builderName);
            if (builderClazz != null) {
                return (ButtonBuilder) builderClazz.newInstance();
            }
            String clazzName = builderName.contains(".") ? builderName : DEFAULT_PACKAGE + builderName;
            builderClazz = Class.forName(clazzName);
            if (BUILDER_INTERFACE.isAssignableFrom(builderClazz)) {
                BUILDER_CLAZZ_CACHE.put(builderName, (Class<? extends ButtonBuilder>) builderClazz);
                return (ButtonBuilder) builderClazz.newInstance();
            }
        } catch (Exception e) {
            log.warn("button builder not found [{}]", builderName, e);
        }
        return null;
    }

    public static ButtonBuilderChain newButtonBuilderChain(BuilderChainConfig chainConfig) {
        List<ButtonBuilder> builders = Lists.newArrayListWithExpectedSize(chainConfig.getBuilderConfigs().size());
        for (BuilderConfig builderConfig : chainConfig.getBuilderConfigs()) {
            builderConfig.setMaxButtonSize(chainConfig.getMaxButtonSize());
            ButtonBuilder builder = getBuilder(builderConfig.getBuilderName());
            if (builder != null) {
                builder.init(builderConfig);
                builders.add(builder);
            }
        }

        return new DefaultButtonBuilderChain(builders);
    }

    static class DefaultButtonBuilderChain implements ButtonBuilderChain {

        private final List<ButtonBuilder> builders;
        private final ThreadLocal<Integer> builderIndex = ThreadLocal.withInitial(() -> 0);

        DefaultButtonBuilderChain(List<ButtonBuilder> builders) {
            this.builders = builders;
        }

        @Override
        public void build(DealCtx context) {

            Integer index = builderIndex.get();
            if (index >= builders.size()) {
                builderIndex.remove();
                return;
            }

            ButtonBuilder builder = builders.get(index);
            builderIndex.set(index + 1);
            builder.build(context, this);
        }

        @Override
        public void interrupt() {
            builderIndex.remove();
        }

    }
}
