package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.detail.dto.DealGroupDetailDTO;
import com.dianping.deal.detail.dto.DealGroupTemplateDetailDTO;
import com.dianping.deal.detail.dto.ImageContent;
import com.dianping.deal.detail.dto.MixedContent;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealDetailWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.MttgVersion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.constants.MttgDetailLionKeys;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealstruct.PnPurchaseNoteDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealstruct.PnStandardDisplayItemDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealstruct.PnStandardDisplayValueDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealstruct.PurchaseNoteModuleDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.Pair;
import com.dianping.mobile.mapi.dztgdetail.entity.PurchaseNoteStructuredConfig;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.DpDealDetailBuilder2;
import com.dianping.mobile.mapi.dztgdetail.helper.ImgUrlEncryptHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.MtDealDetailBuilder;
import com.dianping.mobile.mapi.dztgdetail.helper.ReminderHelper;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.SwitchUtils;
import com.dianping.mobile.mapi.dztgdetail.util.TimesDealUtil;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.PurchaseNoteDTO;
import com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO;
import com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO;
import com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public class DealDetailProcessor extends AbsDealProcessor {

    @Autowired
    private DealDetailWrapper dealDetailWrapper;
    @Autowired
    DouHuBiz douHuBiz;

    @Resource
    private DealGroupWrapper dealGroupWrapper;

    private static final String OLD_REFUND_INFO = "有效期内可申请全额退款";
    private static final String NEW_REFUND_INFO = "若次数未使用，可随时退全部实付金额。若发生核销后再申请退款，剩余所有未核销次数将一起退款，本商品为阶梯定价品，退款金额=团购实付金额-已核销次数的单次价格之和。";
    private static final String NEW_REFUND_INFO_FOR_WHITE_LIST_CUSTOMERS = "购买后若全部未使用，在有效期内可申请全额退款；过期自动将未使用部分退款；若发生核销后再申请退款，剩余所有未核销次数将一起退款";

    @Override
    public boolean isEnable(DealCtx ctx) {
        return ctx.getEnvCtx() != null && !ctx.getEnvCtx().isFromH5();
    }

    @Override
    public void prepare(DealCtx ctx) {
        if(GreyUtils.enableQueryCenterForMainApi(ctx)) {
            ctx.getFutureCtx().setDealGroupStructedDetailFuture(dealDetailWrapper.preHasStructedDetail(ctx.getDpId()));
        } else {
            ctx.getFutureCtx().setDealGroupDetailFuture(dealDetailWrapper.preDealGroupDetail(ctx.getDpId()));
            ctx.getFutureCtx().setDealGroupStructedDetailFuture(dealDetailWrapper.preHasStructedDetail(ctx.getDpId()));
        }
    }

    @Override
    public void process(DealCtx ctx) {
        if(GreyUtils.enableQueryCenterForMainApi(ctx)) {
            boolean hitStructuredPurchaseNote = hitStructuredPurchaseNote(ctx);
            ctx.setHitStructuredPurchaseNote(hitStructuredPurchaseNote);
            if (hitStructuredPurchaseNote) {
                fillPurchaseNoteStructureInfo(ctx);
            }
            processByQueryCenter(ctx);
        } else {
            ctx.setDealGroupDetailDTO(dealDetailWrapper.getFutureResult(ctx.getFutureCtx().getDealGroupDetailFuture()));
            ctx.setHasStructedDetail(dealDetailWrapper.getHasStructedDetail(ctx.getFutureCtx().getDealGroupStructedDetailFuture()));

            if(ctx.isMt()) {
                List<Pair> mtStructedDetails = buildMtStructedDetail(ctx);
                boolean encryptImgUrlEnable = Lion.getBooleanValue(MttgDetailLionKeys.APP_DEAL_DETAIL_IMG_URL_ENCRYPT_ENABLE, false);
                encryptImgUrlInResult(mtStructedDetails, encryptImgUrlEnable);

                ctx.setStructedDetails(mtStructedDetails);
            } else {
                ctx.setStructedDetails(buildDpStructedDetail(ctx));
            }
        }
    }

    /**
     *  获取购买须知结构化信息并填充到ctx中
     * @param ctx
     */
    public void fillPurchaseNoteStructureInfo(DealCtx ctx) {
        if (Objects.isNull(ctx.getDealGroupDTO())) {
            return;
        }
        PurchaseNoteDTO sourcePurchaseNoteDTO = ctx.getDealGroupDTO().getPurchaseNote();
        if(Objects.nonNull(sourcePurchaseNoteDTO)) {
            PnPurchaseNoteDTO pnPurchaseNoteDTO = new PnPurchaseNoteDTO();
            // 新版退款表格控制开关
            setHasPurchaseNoteTable(sourcePurchaseNoteDTO, ctx);
            // 小程序无法展示新版本的次卡退款表格信息,只需要展示退款信息文案即可
            PnPurchaseNoteDTO purchaseNoteDTO = assembleStructurePurchaseNoteDTO(sourcePurchaseNoteDTO,
                    pnPurchaseNoteDTO, ctx);
            handleRefundDetail(purchaseNoteDTO, ctx);
            ctx.setPnPurchaseNoteDTO(purchaseNoteDTO);
        }
    }

    private void handleRefundDetail(PnPurchaseNoteDTO purchaseNoteDTO, DealCtx ctx) {

        List<Long> whiteList = LionConfigUtils.getCustomerWhiteList();
        long customerId = getCustomerId(ctx);
        if (CollectionUtils.isEmpty(whiteList) || !whiteList.contains(customerId)) {
            handleMiniRefundDetail(purchaseNoteDTO, ctx);
            return;
        }
        // 增加客户白名单的能力
        handleWhiteListCustomer(purchaseNoteDTO, ctx);
    }

    private void handleWhiteListCustomer(PnPurchaseNoteDTO purchaseNoteDTO, DealCtx ctx) {
        if (Objects.isNull(purchaseNoteDTO)) {
            return;
        }

        if (!ctx.isHasPurchaseNoteTable()) {
            return;
        }

        deleteNewRefundInfo(purchaseNoteDTO);
        replaceRefundInfo(purchaseNoteDTO, NEW_REFUND_INFO_FOR_WHITE_LIST_CUSTOMERS);
    }

    /**
     * 到综侧 customerId
     * 
     * @param ctx
     * @return
     */
    private long getCustomerId(DealCtx ctx) {
        long customerId = ctx.getCustomerId();

        if (customerId > 0) {
            return customerId;
        }

        if (ctx.getDealGroupDTO() != null && ctx.getDealGroupDTO().getCustomer() != null) {
            customerId = ctx.getDealGroupDTO().getCustomer().getOriginCustomerId();
            ctx.setCustomerId(customerId);
        } else {
            customerId = dealGroupWrapper.getCustomerId(ctx.getDpId());
            ctx.setCustomerId(customerId);
        }

        return customerId;
    }

    private void handleMiniRefundDetail(PnPurchaseNoteDTO purchaseNoteDTO, DealCtx ctx) {
        if (!ctx.getEnvCtx().isMiniApp()) {
            return;
        }
        if (Objects.isNull(purchaseNoteDTO)) {
            return;
        }

        if (!ctx.isHasPurchaseNoteTable()) {
            return;
        }
        // 删除新的退款规则
        deleteNewRefundInfo(purchaseNoteDTO);
        // 替换旧退款规则文字描述
        replaceRefundInfo(purchaseNoteDTO, NEW_REFUND_INFO);
    }

    private void deleteNewRefundInfo(PnPurchaseNoteDTO purchaseNoteDTO) {
        List<PurchaseNoteModuleDTO> beforePnModules = purchaseNoteDTO.getPnModules();

        if (CollectionUtils.isEmpty(beforePnModules)) {
            return;
        }

        List<PurchaseNoteModuleDTO> afterPnModules = beforePnModules.stream().filter(Objects::nonNull)
                .filter(module -> !Objects.equals("退款规则", module.getPnModuleName())).collect(Collectors.toList());

        purchaseNoteDTO.setPnModules(afterPnModules);
    }

    private void replaceRefundInfo(PnPurchaseNoteDTO purchaseNoteDTO, String newRefundInfo) {
        List<PurchaseNoteModuleDTO> pnModules = purchaseNoteDTO.getPnModules();
        if (CollectionUtils.isEmpty(pnModules)) {
            return;
        }
        Optional<PurchaseNoteModuleDTO> warmTipsOpt = pnModules.stream().filter(Objects::nonNull)
                .filter(module -> Objects.equals("温馨提示", module.getPnModuleName())).findFirst();
        if (!warmTipsOpt.isPresent()) {
            return;
        }
        PurchaseNoteModuleDTO warmTips = warmTipsOpt.get();
        List<PnStandardDisplayItemDTO> pnItems = warmTips.getPnItems();
        if (CollectionUtils.isEmpty(pnItems)) {
            return;
        }
        // 找出包含旧退款信息的模块
        Optional<PnStandardDisplayValueDTO> oldRefundInfoOpt = pnItems.stream().filter(Objects::nonNull)
                .map(PnStandardDisplayItemDTO::getPnItemValues).filter(Objects::nonNull).flatMap(List::stream)
                .filter(item -> Objects.nonNull(item) && StringUtils.isNotBlank(item.getPnValue())
                        && item.getPnValue().contains(OLD_REFUND_INFO))
                .findFirst();

        // 有则改之
        if (oldRefundInfoOpt.isPresent()) {
            PnStandardDisplayValueDTO pnStandardDisplayValueDTO = oldRefundInfoOpt.get();
            pnStandardDisplayValueDTO.setPnValue(newRefundInfo);
            return;
        }

        // 无则加勉
        Optional<PnStandardDisplayItemDTO> firstPnStandardDisplayItemDTOOpt = pnItems.stream().filter(Objects::nonNull)
                .findFirst();

        if (!firstPnStandardDisplayItemDTOOpt.isPresent()) {
            return;
        }
        PnStandardDisplayItemDTO pnStandardDisplayItemDTO = firstPnStandardDisplayItemDTOOpt.get();
        List<PnStandardDisplayValueDTO> pnItemValues = pnStandardDisplayItemDTO.getPnItemValues();
        if (CollectionUtils.isEmpty(pnItemValues)) {
            pnStandardDisplayItemDTO.setPnItemValues(Lists.newArrayList(buildPnStandardDisplayValueDTO(newRefundInfo)));
        } else {
            pnItemValues.add(buildPnStandardDisplayValueDTO(newRefundInfo));
        }
    }

    private PnStandardDisplayValueDTO buildPnStandardDisplayValueDTO(String newRefundInfo) {
        PnStandardDisplayValueDTO pnStandardDisplayValueDTO = new PnStandardDisplayValueDTO();
        pnStandardDisplayValueDTO.setPnType(1);
        pnStandardDisplayValueDTO.setPnValue(newRefundInfo);
        return pnStandardDisplayValueDTO;
    }

    private void setHasPurchaseNoteTable(PurchaseNoteDTO sourcePurchaseNoteDTO, DealCtx ctx) {
        // 非团购次卡
        if (!TimesDealUtil.isMultiTimesCard(ctx)) {
            return;
        }
        if (Objects.isNull(sourcePurchaseNoteDTO)) {
            return;
        }
        List<StandardDisplayModuleDTO> modules = sourcePurchaseNoteDTO.getModules();

        if (CollectionUtils.isEmpty(modules)) {
            return;
        }

        // 说否含有新增的退款规则模块
        boolean isContainRefundModule = modules.stream().filter(Objects::nonNull)
                .anyMatch(module -> Objects.equals("退款规则", module.getModuleName()));

        // 是否含有退款表格信息
        boolean isContainRefundTable = modules.stream().filter(Objects::nonNull).map(StandardDisplayModuleDTO::getItems)
                .filter(Objects::nonNull).flatMap(List::stream).filter(Objects::nonNull)
                .map(StandardDisplayItemDTO::getItemValues).filter(Objects::nonNull).flatMap(List::stream)
                .anyMatch(item -> item != null && item.getType() == 4);

        // type为4 或者 含有退款规则的module 则说明含有退款表格信息
        ctx.setHasPurchaseNoteTable(
                (isContainRefundModule || isContainRefundTable) && LionConfigUtils.hasPurchaseNoteTableSwitch());
    }

    /**
     * 拼装PnPurchaseNoteDTO
     * @param purchaseNoteDTO
     * @param pnPurchaseNoteDTO
     * @return
     */
    private PnPurchaseNoteDTO assembleStructurePurchaseNoteDTO(PurchaseNoteDTO purchaseNoteDTO, PnPurchaseNoteDTO pnPurchaseNoteDTO, DealCtx ctx) {
        pnPurchaseNoteDTO.setPnTitle(replaceAppointTitleToPreOrder(purchaseNoteDTO.getTitle(), ctx));
        pnPurchaseNoteDTO.setPnModules(assemblePNModuleList(purchaseNoteDTO.getModules(), ctx));
        pnPurchaseNoteDTO.setSubTitle(ReminderHelper.getReminderSummary(ctx));
        return pnPurchaseNoteDTO;
    }

    /**
     * 拼装List<PurchaseNoteModuleDTO>
     * @param modules
     * @return
     */
    private List<PurchaseNoteModuleDTO> assemblePNModuleList(List<StandardDisplayModuleDTO> modules, DealCtx ctx) {
        return ListUtils.emptyIfNull(modules).stream().map(moduleDTO -> assemblePNModule(moduleDTO, ctx)).collect(Collectors.toList());
    }

    /**
     * 拼装PnStandardDisplayModuleDTO
     * @param moduleDTO
     * @return
     */
    private PurchaseNoteModuleDTO assemblePNModule(StandardDisplayModuleDTO moduleDTO, DealCtx ctx) {
        PurchaseNoteModuleDTO purchaseNoteModuleDTO = new PurchaseNoteModuleDTO();
        purchaseNoteModuleDTO.setPnModuleName(replaceAppointToPreOrder(moduleDTO.getModuleName(), ctx));
        purchaseNoteModuleDTO.setPnIcon(moduleDTO.getIcon());
        purchaseNoteModuleDTO.setPnItems(assemblePNItemList(moduleDTO.getItems(), ctx));
        return purchaseNoteModuleDTO;
    }

    /**
     * 拼装List<PnStandardDisplayItemDTO>
     * @param items
     * @return
     */
    private List<PnStandardDisplayItemDTO> assemblePNItemList(List<StandardDisplayItemDTO> items, DealCtx ctx) {
        return ListUtils.emptyIfNull(items).stream().map(itemDTO -> assemblePNItem(itemDTO, ctx)).collect(Collectors.toList());
    }

    /**
     * 拼装PnStandardDisplayItemDTO
     * @param itemDTO
     * @return
     */
    private PnStandardDisplayItemDTO assemblePNItem(StandardDisplayItemDTO itemDTO, DealCtx ctx) {
        PnStandardDisplayItemDTO pnStandardDisplayItemDTO = new PnStandardDisplayItemDTO();
        pnStandardDisplayItemDTO.setPnItemName(replaceAppointToPreOrder(itemDTO.getItemName(), ctx));
        pnStandardDisplayItemDTO.setPnItemValues(assemblePNItemValueList(itemDTO.getItemValues(), ctx));
        return pnStandardDisplayItemDTO;
    }

    /**
     * 拼装List<PnStandardDisplayValueDTO>
     * @param itemValues
     * @return
     */
    private List<PnStandardDisplayValueDTO> assemblePNItemValueList(List<StandardDisplayValueDTO> itemValues, DealCtx ctx) {
        return ListUtils.emptyIfNull(itemValues).stream().map(itemValueDTO -> assemblePNItemValue(itemValueDTO, ctx)).collect(Collectors.toList());
    }

    /**
     * 拼装PnStandardDisplayValueDTO
     * @param itemValueDTO
     * @return
     */
    private PnStandardDisplayValueDTO assemblePNItemValue(StandardDisplayValueDTO itemValueDTO, DealCtx ctx) {
        PnStandardDisplayValueDTO pnStandardDisplayValueDTO = new PnStandardDisplayValueDTO();
        pnStandardDisplayValueDTO.setPnType(itemValueDTO.getType());
        pnStandardDisplayValueDTO.setPnValue(replaceAppointToPreOrder(itemValueDTO.getValue(), ctx));
        return pnStandardDisplayValueDTO;
    }

    private String replaceText(String text, DealCtx ctx, String target, String replacement) {
        if (StringUtils.isEmpty(text) || !DealCtxHelper.isPreOrderDeal(ctx)) {
            return text;
        }
        return text.replace(target, replacement);
    }

    private String replaceAppointToPreOrder(String text, DealCtx ctx) {
        return replaceText(text, ctx, "预约", "预订");
    }

    private String replaceAppointTitleToPreOrder(String text, DealCtx ctx) {
        return replaceText(text, ctx, "购买须知", "预订须知");
    }

    public boolean hitStructuredPurchaseNote(DealCtx dealCtx) {
        PurchaseNoteStructuredConfig config = Lion.getBean(LionConstants.APP_KEY,
                LionConstants.STRUCTURED_PURCHASE_NOTE_GRAY_CONFIG, PurchaseNoteStructuredConfig.class);
        if(config == null || config.getGrayLevel() == 0) {
            return false;
        }
        dealCtx.setPNSConfig(config);
        if(config.getEnableClientType() != null && !config.getEnableClientType().contains(dealCtx.getEnvCtx().getDztgClientTypeEnum().getCode())) {
            return false;
        }
        if (CollectionUtils.isNotEmpty(config.getDealGroupIds()) && config.getDealGroupIds().contains(dealCtx.isMt() ? dealCtx.getMtId() : dealCtx.getDpId())) {
            return true;
        }
        if (config.getGrayLevel() == 2) {
            return true;
        }

        // 私域直播小程序无unionId，加白直接返回
        if (dealCtx.isMtLiveMinApp()) {
            return true;
        }
        // 如果是非结构化购买须知的三级类目 && 非团购次卡，则展示非结构化购买须知数据
        if (isUnStructuredPurchaseNote(dealCtx, config.getUnstructuredServiceType())
                && !TimesDealUtil.isTimesDeal(dealCtx.getDealGroupDTO())) {
            return false;
        }

        int publishCategoryId = dealCtx.getCategoryId();
        boolean isMt = dealCtx.isMt();
        String key = isMt ? "mt" : "dp";
        key += publishCategoryId;
        String expId = config.getCategory2ExpId().getOrDefault(key, null);

        String unionId = dealCtx.getEnvCtx().getUnionId();
        String module = dealCtx.isMt() ? "MtPurchaseNoteStructure" : "DpPurchaseNoteStructure";

        ModuleAbConfig moduleAbConfig = douHuBiz.getAbByUnionIdAndExpId(unionId, expId, module, isMt);
        if(moduleAbConfig != null && CollectionUtils.isNotEmpty(moduleAbConfig.getConfigs())) {
            dealCtx.setCardStyleAbConfig(moduleAbConfig);
            if("b".equals(moduleAbConfig.getConfigs().get(0).getExpResult()) || RequestSourceEnum.fromTradeSnapshot(dealCtx.getRequestSource())) {
                Cat.logEvent("PurchaseNoteStructure", String.valueOf(publishCategoryId));
                return true;
            }
        }
        return false;
    }

    private boolean isUnStructuredPurchaseNote(DealCtx dealCtx, List<String> unstructuredServiceTypeIds) {
        if (CollectionUtils.isEmpty(unstructuredServiceTypeIds)) {
            return false;
        }
        Long serviceTypeId = DealUtils.getDealGroupServiceTypeId(dealCtx);
        int categoryId = dealCtx.getCategoryId();
        boolean isMt = dealCtx.isMt();
        String unstructuredServiceTypeId = isMt ? String.format("mt%s.%s", categoryId, serviceTypeId) : String.format("dp%s.%s", categoryId, serviceTypeId);
        return unstructuredServiceTypeIds.contains(unstructuredServiceTypeId);
    }

    private void processByQueryCenter(DealCtx ctx) {
        DealGroupDetailDTO dealGroupDetailDTO = trans2OldDTO(ctx.getDealGroupDTO());
        ctx.setDealGroupDetailDTO(dealGroupDetailDTO);
        ctx.setHasStructedDetail(dealDetailWrapper.getHasStructedDetail(ctx.getFutureCtx().getDealGroupStructedDetailFuture()));

        if(ctx.isMt()) {
            List<Pair> mtStructedDetails = buildMtStructedDetail(ctx);
            boolean encryptImgUrlEnable = Lion.getBooleanValue(MttgDetailLionKeys.APP_DEAL_DETAIL_IMG_URL_ENCRYPT_ENABLE, false);
            encryptImgUrlInResult(mtStructedDetails, encryptImgUrlEnable);
            ctx.setStructedDetails(mtStructedDetails);
        } else {
            ctx.setStructedDetails(buildDpStructedDetail(ctx));
        }
    }

    private DealGroupDetailDTO trans2OldDTO(DealGroupDTO dealGroupDTO) {
        if(dealGroupDTO == null || dealGroupDTO.getDetail() == null) {
            return null;
        }
        DealGroupDetailDTO oldDto = new DealGroupDetailDTO();
        com.sankuai.general.product.query.center.client.dto.detail.DealGroupDetailDTO newDto = dealGroupDTO.getDetail();

        oldDto.setDealGroupId(dealGroupDTO.getDpDealGroupIdInt());
        oldDto.setDealGroupPics(newDto.getDealGroupPics());
        oldDto.setImages(newDto.getImages());
        oldDto.setInfo(newDto.getInfo());
        oldDto.setImportantPoint(newDto.getImportantPoint());
        oldDto.setSpecialPoint(newDto.getSpecialPoint());
        oldDto.setProductInfo(newDto.getProductInfo());
        oldDto.setEditorInfo(newDto.getEditorInfo());
        oldDto.setMemberInfo(newDto.getMemberInfo());
        oldDto.setShopInfo(newDto.getShopInfo());
        oldDto.setEditorTeam(newDto.getEditorTeam());
        oldDto.setSummary(newDto.getSummary());
        if(newDto.getTemplateDetailDTOs() != null) {
            oldDto.setTemplateDetailDTOs(newDto.getTemplateDetailDTOs().stream().filter(Objects::nonNull).map(template -> toOldDto(template, dealGroupDTO.getDpDealGroupIdInt())).collect(Collectors.toList()));
        }

        return oldDto;
    }

    private com.dianping.deal.detail.dto.DealGroupTemplateDetailDTO toOldDto(com.sankuai.general.product.query.center.client.dto.detail.DealGroupTemplateDetailDTO newDto, int dpDealGroupId) {
        com.dianping.deal.detail.dto.DealGroupTemplateDetailDTO oldDto = new DealGroupTemplateDetailDTO();
        oldDto.setId(dpDealGroupId);
        oldDto.setTitle(newDto.getTitle());
        oldDto.setContent(newDto.getContent());
        oldDto.setType(newDto.getType());
        oldDto.setBlockId(newDto.getBlockId());
        if(newDto.getImageContents() != null) {
            oldDto.setImageContents(newDto.getImageContents().stream().filter(Objects::nonNull).map(image -> {
                com.dianping.deal.detail.dto.ImageContent oldImage = new ImageContent();
                oldImage.setTitle(image.getTitle());
                oldImage.setDesc(image.getDesc());
                oldImage.setPath(image.getPath());
                return oldImage;
            }).collect(Collectors.toList()));
        }
        if(newDto.getMixedContents() != null) {
            oldDto.setMixedContents(newDto.getMixedContents().stream().filter(Objects::nonNull).map(content -> {
                com.dianping.deal.detail.dto.MixedContent oldContent = new MixedContent();
                oldContent.setTitle(content.getTitle());
                oldContent.setDesc(content.getDesc());
                oldContent.setType(content.getType());
                oldContent.setContent(content.getContent());
                return oldContent;
            }).collect(Collectors.toList()));
        }

        return oldDto;
    }

    private List<Pair> buildMtStructedDetail(DealCtx dealCtx) {
        // 获取非结构化信息DealGroupDetailDTO.templateDetailDTOs，通过toStructedDetails方法转化为结构化信息
        DealGroupDetailDTO dealGroupDetailDTO = dealCtx.getDealGroupDetailDTO();
        boolean hasStructedDetail = false;

        MttgVersion version = new MttgVersion(dealCtx.getEnvCtx().getVersion());
        if(version.compareTo(MttgVersion.V8_1) >= 0 && version.compareTo(MttgVersion.V9_1) < 0
                && SwitchUtils.isStructedDetailEnable()) {
            hasStructedDetail = dealCtx.isHasStructedDetail();
        }

        if (dealGroupDetailDTO != null && dealCtx.getDealGroupBase() != null) {
            DealGroupChannelDTO dealGroupChannelDTO = dealCtx.getChannelDTO();
            String voucherLimit = dealGroupChannelDTO == null ? null : getMtVoucherLimit(dealCtx);
            MtDealDetailBuilder ddc = MtDealDetailBuilder.build_2(dealCtx.getDealGroupBase(), dealGroupDetailDTO, true, voucherLimit);

            return ddc.toStructedDetails(dealCtx, hasStructedDetail);
        }
        return Lists.newArrayList();
    }

    private List<Pair> buildDpStructedDetail(DealCtx dealCtx) {
        DealGroupDetailDTO dealGroupDetailDTO = dealCtx.getDealGroupDetailDTO();
        boolean hasStructedDetail = false;

        if (dealGroupDetailDTO != null) {
            DealGroupChannelDTO dealGroupChannelDTO = dealCtx.getChannelDTO();
            String voucherLimit = dealGroupChannelDTO == null ? null : getDpVoucherLimit(dealCtx);
            DpDealDetailBuilder2 ddc = DpDealDetailBuilder2.build_2(dealCtx.getDealGroupBase(), dealGroupDetailDTO, true, voucherLimit);

            return ddc.toStructedDetails(hasStructedDetail, dealCtx);
        }
        return Lists.newArrayList();
    }

    private String getMtVoucherLimit(DealCtx dealCtx) {
        List<Integer> categories = Lion.getList("mapi-mttgdetail-web.voucher.limit.categories", Integer.class, new ArrayList<Integer>());

        if (CollectionUtils.isEmpty(categories) || !categories.contains(dealCtx.getCategoryId())) {
            return null;
        }

        List<AttributeDTO> attrs = dealCtx.getAttrs();
        if (CollectionUtils.isEmpty(attrs)) {
            return null;
        }

        for (AttributeDTO attr : attrs) {
            String name = attr.getName();
            String value = attr.getValue().get(0);

            if ("sys_deal_universal_type".equals(name)) {
                if (!"2".equals(value)) {
                    return null;
                }
            } else if ("voucher_limit_of_using_2".equals(name)) {
                return value;
            }
        }

        return null;
    }

    private String getDpVoucherLimit(DealCtx dealCtx) {
        List<Integer> categories = Lion.getList("mapi-tgdetail-web.voucher.limit.categories", Integer.class, new ArrayList<Integer>());

        if (CollectionUtils.isEmpty(categories) || !categories.contains(dealCtx.getCategoryId())) {
            return null;
        }

        if (dealCtx.getCategoryId() == 506) {
            List<AttributeDTO> attrs = dealCtx.getAttrs();
            if (CollectionUtils.isEmpty(attrs)) {
                return null;
            }

            for (AttributeDTO attr : attrs) {
                String name = attr.getName();
                String value = attr.getValue().get(0);

                if ("sys_deal_universal_type".equals(name)) {
                    if (!"2".equals(value)) {
                        return null;
                    }
                } else if ("voucher_limit_of_using_2".equals(name)) {
                    return value;
                }
            }
        }

        return null;
    }

    private void encryptImgUrlInResult(List<Pair> mtStructedDetails, boolean encryptImgUrl) {
        if (CollectionUtils.isEmpty(mtStructedDetails) || !encryptImgUrl) {
            return;
        }
        for (Pair pair : mtStructedDetails) {
            pair.setName(ImgUrlEncryptHelper.encryptImgUrlInStr(pair.getName()));
        }
    }

}
