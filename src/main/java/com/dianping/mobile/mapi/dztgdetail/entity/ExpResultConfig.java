package com.dianping.mobile.mapi.dztgdetail.entity;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 同店比价样式的配置
 * 
 * <AUTHOR>
 * 
 *     同店比货模块样式优化实验配置：
 *     enableStyle：实验开关，优先级：最高；
 *     categoryId2Module：行业类目id与实验moduel的映射，（
 *     module关联Lion：com.sankuai.dzu.tpbase.dztgdetailweb.douhu.module.exp.config）；
 *     key2Style：实验结果和样式的映射;
 *     allPassStyle：全行业推广的样式，不为空时推广全行业，为空时跳过;
 *     字段优先级：enableStyle（开关）>allPassStyle（全行业推广，不走实验）>categoryId2Module&&key2Style（走实验推广样式）>key2Style（直接推广行业样式）
 */
@Data
public class ExpResultConfig {

    /**
     * 样式开启开关
     */
    private boolean enableStyle;

    /**
     * 行业类目id与实验moduel的映射，
     * module关联Lion：com.sankuai.dzu.tpbase.dztgdetailweb.douhu.module.exp.config
     */
    private Map<String, String> categoryId2Module;

    /**
     * 实验结果 与样式的映射
     */
    private Map<String, String> key2Style;

    /**
     * 同店比价 推全行业 的样式配置
     */
    private String allPassStyle;

}
