package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
@Data
@MobileDo(id = 0x7509)
public class DealRelatedBehaviorItem implements Serializable {

    /**
     * 用户昵称
     */
    @MobileField(key = 0xcec)
    private String userName;

    /**
     * 用户头像链接
     */
    @MobileField(key = 0xf248)
    private String userAvatarUrl;
    /**
     * 用户行为描述
     */
    @MobileField(key = 0xd687)
    private String userBehaviorDesc;

}
