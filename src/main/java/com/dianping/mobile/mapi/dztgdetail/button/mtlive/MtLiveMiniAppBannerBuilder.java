package com.dianping.mobile.mapi.dztgdetail.button.mtlive;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BannerTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MtLiveSaleStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBanner;
import com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil;
import com.dianping.scrum.util.DateUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.util.DealGroupUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-12-18
 * @desc 美团美播底部banner构建器
 */
public class MtLiveMiniAppBannerBuilder extends AbstractButtonBuilder {
    @Override
    public void build(DealCtx context, ButtonBuilderChain chain) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.button.mtlive.MtLiveMiniAppBannerBuilder.build(DealCtx,ButtonBuilderChain)");
        buildBanner(context);
        chain.build(context);
    }

    private void buildBanner(DealCtx context) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.button.mtlive.MtLiveMiniAppBannerBuilder.buildBanner(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        if (!context.isMtLiveMinApp()) {
            return;
        }
        if (Objects.isNull(context.getDealGroupDTO()) || Objects.isNull(context.getDealGroupDTO().getBasic())) {
            return;
        }
        DealGroupBasicDTO dealGroupBasicDTO = context.getDealGroupDTO().getBasic();
        Date beginSaleDate = DealGroupUtils.convertString2Date(dealGroupBasicDTO.getBeginSaleDate());
        Date now = DateUtils.currentDate();
        // 未开售
        if (Objects.nonNull(beginSaleDate) && now.before(beginSaleDate)) {
            DealBuyBanner banner = new DealBuyBanner();
            banner.setBannerType(BannerTypeEnum.MINI_PROGRAM_LIVE.getType());
            banner.setShow(true);
            SimpleDateFormat sdf = new SimpleDateFormat("MM.dd HH:mm");
            String content = String.format("即将开始 %s开售", sdf.format(beginSaleDate));
            String bannerContent = JsonLabelUtil.getMtLiveMiniAppBuyBannerJson(content);
            banner.setContent(bannerContent);
            context.getBuyBar().setBuyBanner(banner);
            // 即将开售
            context.setMtLiveSaleStatusEnum(MtLiveSaleStatusEnum.COMING_SOON);
        }
    }

    @Override
    protected void doBuild(DealCtx context, ButtonBuilderChain chain) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.button.mtlive.MtLiveMiniAppBannerBuilder.doBuild(DealCtx,ButtonBuilderChain)");

    }
}
