package com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/12/13.
 */
@TypeDoc(description = "团单优惠模块数据")
@MobileDo(id = 0x5bb3)
@Data
public class DealPromoModule implements Serializable {

    @FieldDoc(description = "简洁优惠信息")
    @MobileField(key = 0x4711)
    private ConcisePromoInfo concisePromoInfo;

    @FieldDoc(description = "详细优惠信息")
    @MobileField(key = 0xc965)
    private DetailPromoInfo detailPromoInfo;

    @FieldDoc(description = "优惠提前曝光")
    @MobileField(key = 0xb330)
    private ExposurePromoInfo exposurePromoInfo;

    @FieldDoc(description = "优惠聚合信息，数据来自营销代理接口")
    @MobileField(key = 0xde74)
    private PromoAggInfo promoAggInfo;
}
