package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.enums.FreeDealEnum;
import com.dianping.mobile.mapi.dztgdetail.entity.FreeDealConfig;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

public class FreeDealUtils {

    private static final String FREE_DEAL_INFO_CONFIG_LION_KEY = "free.deal.info.config";
    private static final String FREE_DEAL_INFO_CONFIG_LIST_LION_KEY = "free.deal.category.list";
    private static final String FREE_DEAL_INDEPENDENT_TAB_LIST_LION_KEY = "free.deal.independent.tab.category.list";

    public static FreeDealConfig getFreeDealConfig(FreeDealEnum freeDealType) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.util.FreeDealUtils.getFreeDealConfig(com.dianping.mobile.mapi.dztgdetail.common.enums.FreeDealEnum)");
        if (Objects.isNull(freeDealType)) {
            return null;
        }
        String config = Lion.getString("com.sankuai.dzu.tpbase.dztgdetailweb",  String.format("%s.%s", FREE_DEAL_INFO_CONFIG_LION_KEY, freeDealType.getType()));
        if (StringUtils.isBlank(config)) {
            return null;
        }
        return JsonUtils.fromJson(config, FreeDealConfig.class);
    }

    // 是否在免费团购白名单（使用二级团单分类）
    public static boolean inCategoryList(Long categoryId) {
        List<Long> whiteList = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb",  FREE_DEAL_INFO_CONFIG_LIST_LION_KEY, Long.class);
        return !CollectionUtils.isEmpty(whiteList) && whiteList.contains(categoryId);
    }

    public static boolean useIndependentTab(Long categoryId) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.util.FreeDealUtils.useIndependentTab(java.lang.Long)");
        List<Long> whiteList = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb",  FREE_DEAL_INDEPENDENT_TAB_LIST_LION_KEY, Long.class);
        return !CollectionUtils.isEmpty(whiteList) && whiteList.contains(categoryId);
    }

}
