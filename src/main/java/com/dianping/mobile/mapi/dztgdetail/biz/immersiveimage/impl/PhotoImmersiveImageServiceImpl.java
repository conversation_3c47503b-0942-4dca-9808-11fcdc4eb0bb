package com.dianping.mobile.mapi.dztgdetail.biz.immersiveimage.impl;

import com.dianping.mobile.mapi.dztgdetail.biz.immersiveimage.ImmersiveImageService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ImmersiveImageWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageFilterRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageFilterVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageVO;
import com.dianping.mobile.mapi.dztgdetail.entity.QueryExhibitImageFilterParam;
import com.dianping.mobile.mapi.dztgdetail.entity.QueryExhibitImageParam;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Component
public class PhotoImmersiveImageServiceImpl implements ImmersiveImageService {

    @Resource
    private ImmersiveImageWrapper immersiveImageWrapper;
    /**
     * 摄影团单二级类目ID
     */
    private static final List<Integer> PHOTO_CATEGORY_IDS = Lists.newArrayList(504, 1004, 910, 908, 924);

    // 放在lion配置里
    private static final String PERSON_PHOTO_MODULE_KEY = "wed_photocase_list_module";

    @Override
    public List<Integer> getCategoryIds() {
        return PHOTO_CATEGORY_IDS;
    }

    @Override
    public ImmersiveImageVO getImmersiveImage(GetImmersiveImageRequest request, EnvCtx envCtx) {
        // 一期和美甲共用一套逻辑，二期需要加上筛选项
        // 二期筛选项对应字段queryStr，筛选项信息,是List<ItemFieldDTO>的json格式,跳转链接透传字段
        QueryExhibitImageParam param = QueryExhibitImageParam.builder()
                .categoryId(request.getCategoryId())
                .dpDealGroupId(request.getDealGroupId())
                .start(request.getStart())
                .limit(request.getLimit())
                .shopId(request.getShopId())
                .selectValue(request.getSelectValue())
                .build();
        return immersiveImageWrapper.getImmersiveImage(param, envCtx);
    }

    @Override
    public ImmersiveImageFilterVO getImmersiveImageFilter(GetImmersiveImageFilterRequest request) {
        if (Objects.isNull(request.getCategoryId())) {
            return null;
        }
        QueryExhibitImageFilterParam param = QueryExhibitImageFilterParam.builder()
                .categoryId(request.getCategoryId())
                .clientType(request.getDealGroupType())
                .shopId(request.getShopId())
                .bizType(1)
                .subBizType(1004 == request.getCategoryId() ? 21 : 25)
                .externalBizId(request.getDealGroupId())
                .externalBizIdType(request.getDealGroupType())
                .build();
        return immersiveImageWrapper.getImmersiveImageFilter(param);
    }
}
