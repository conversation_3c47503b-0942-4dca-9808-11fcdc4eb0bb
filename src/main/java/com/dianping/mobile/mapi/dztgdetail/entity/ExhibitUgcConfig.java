package com.dianping.mobile.mapi.dztgdetail.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ExhibitUgcConfig implements Serializable {
    private boolean enable = true;
    private List<Integer> categoryIds;
    private int ugcPicLimit = 5;
    private boolean showUserName = false;
    private boolean enablePicSearch = true;
    private List<Long> dpShopIdBlackList;
    private List<Long> dpDealGroupIdBlackList;
    private double minRealityScore = 3.58;
    private String defaultName = "实拍来自匿名用户";
    private String mtSearchSchema = "imeituan://www.meituan.com/gc/mrn?mrn_biz=gcbu&mrn_entry=similar-nail-pop&mrn_component=similar-nail-pop&similarsourceid=%s&shopid=%s&similarsourcestyles=%s";
    private String dpSearchSchema = "dianping://mrn?mrn_biz=gc&mrn_entry=similar-nail-pop&mrn_component=similar-nail-pop&similarsourceid=%s&shopid=%s&similarsourcestyles=%s";
}
