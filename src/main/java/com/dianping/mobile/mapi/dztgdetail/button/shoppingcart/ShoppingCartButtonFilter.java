package com.dianping.mobile.mapi.dztgdetail.button.shoppingcart;

import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.helper.CardHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

public class ShoppingCartButtonFilter extends AbstractButtonBuilder {

    private static final Set<Integer> SORT_BY_PRICE_BTN_TYPES = Sets.newHashSet(BuyBtnTypeEnum.MEMBER_CARD.getCode(), BuyBtnTypeEnum.IDLE_DEAL.getCode());

    @Override
    public void doBuild(DealCtx context, ButtonBuilderChain chain) {
        filterMemberCard(context);
        filterByPrice(context);
        chain.build(context);
    }
    
    private void filterMemberCard(DealCtx context){
        List<DealBuyBtn> buyButtons = context.getBuyBar().getBuyBtns();

        if(!isUserHoldMemberCard(context)){
            return;
        }

        List<DealBuyBtn> filterButtons = Lists.newArrayList();
        DealBuyBtn normalDealButton = buyButtons.stream()
                .filter(e -> e.getPriceRuleModule().getPriceRuleType() == BuyBtnTypeEnum.NORMAL_DEAL.getCode())
                .findAny()
                .orElse(null);
        DealBuyBtn memberCardDealButton = buyButtons.stream()
                .filter(e -> e.getPriceRuleModule().getPriceRuleType() == BuyBtnTypeEnum.MEMBER_CARD.getCode())
                .findAny()
                .orElse(null);
        if(normalDealButton == null || memberCardDealButton == null){
            return;
        }

        if(new BigDecimal(normalDealButton.getPriceStr()).compareTo(new BigDecimal(memberCardDealButton.getPriceStr())) < 0){
            for (DealBuyBtn buyButton : buyButtons) {
                if(buyButton.getPriceRuleModule().getPriceRuleType() == BuyBtnTypeEnum.MEMBER_CARD.getCode()){
                    continue;
                }
                filterButtons.add(buyButton);
            }
        }else {
            for (DealBuyBtn buyButton : buyButtons) {
                if(buyButton.getPriceRuleModule().getPriceRuleType() == BuyBtnTypeEnum.NORMAL_DEAL.getCode()){
                    continue;
                }
                filterButtons.add(buyButton);
            }
        }

        context.getBuyBar().setBuyBtns(filterButtons);
    }

    private void filterByPrice(DealCtx context) {
        List<DealBuyBtn> buyButtons = context.getBuyBar().getBuyBtns();
        List<DealBuyBtn> filterButtons = Lists.newArrayList();

        DealBuyBtn normalDealButton = buyButtons.stream()
                .filter(e -> e.getPriceRuleModule().getPriceRuleType() == BuyBtnTypeEnum.NORMAL_DEAL.getCode())
                .findAny()
                .orElse(null);

        if(normalDealButton == null){
            return;
        }

        for (DealBuyBtn dealBuyBtn : buyButtons) {
            if (SORT_BY_PRICE_BTN_TYPES.contains(dealBuyBtn.getPriceRuleModule().getPriceRuleType())
                    && new BigDecimal(normalDealButton.getPriceStr()).compareTo(new BigDecimal(dealBuyBtn.getPriceStr())) < 0) {
                continue;
            }
            filterButtons.add(dealBuyBtn);
        }

        context.getBuyBar().setBuyBtns(filterButtons);
    }

    private boolean isUserHoldMemberCard(DealCtx context) {
        return CardHelper.holdCard(context.getPriceContext().getDcCardMemberCard());
    }
}
