package com.dianping.mobile.mapi.dztgdetail.action.app;


import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedDealModuleVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedDeals;
import com.dianping.mobile.mapi.dztgdetail.facade.RelatedModuleFacade;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

@InterfaceDoc(displayName = "团详团单关联模块查询接口",
        type = "restful",
        description = "查询到综团单推荐团单信息",
        scenarios = "该接口仅适用于双平台APP站点的团购详情页",
        host = "http://mapi.dianping.com/general/platform/getrelatedmoduledeals/",
        authors = "yuanzhiqiang02"
)
@Controller("general/platform/dztgdetail/getrelatedmoduledeals.bin")
@Action(url = "getrelatedmoduledeals.bin", httpType = "get")
public class RelatedModuleAction extends AbsAction<RelatedModuleReq> {

    @Autowired
    private RelatedModuleFacade relatedModuleFacade;

    @Override
    protected IMobileResponse validate(RelatedModuleReq request, IMobileContext iMobileContext) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.action.app.RelatedModuleAction.validate(com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedModuleReq,com.dianping.mobile.framework.datatypes.IMobileContext)");
        IdUpgradeUtils.processProductIdForRelatedModuleReq(request, "getrelatedmoduledeals.bin");
        if(request == null || request.getDealGroupId() <= 0){
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "getrelatedmoduledeals.bin",
            displayName = "查询到综团单推荐团单信息",
            description = "查询到综团单推荐团单信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "getrelatedmoduledeals.bin请求参数",
                            type = DealBaseReq.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "result", description = "团购picasso数据",type = RelatedDeals.class)},
            //restExampleUrl = "",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    ),
            }
    )
    @Override
    @CryptMethod
    protected IMobileResponse execute(RelatedModuleReq request, IMobileContext iMobileContext) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.action.app.RelatedModuleAction.execute(com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedModuleReq,com.dianping.mobile.framework.datatypes.IMobileContext)");
        try {
            EnvCtx envCtx = initEnvCtx(iMobileContext);
            RelatedDealModuleVO result = relatedModuleFacade.getRelatedModuleDeals(request, envCtx);
            if(result != null){
                return new CommonMobileResponse(result);
            }
        } catch (Exception e) {
            logger.error("getrelatedmoduledeals.bin error",e);
        }
        return Resps.SYSTEM_ERROR;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.action.app.RelatedModuleAction.getRule()");
        return null;
    }
}
