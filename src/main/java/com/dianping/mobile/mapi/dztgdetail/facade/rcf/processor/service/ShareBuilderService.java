package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.entity.CustomShareAbleConfig;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.APP_KEY;

/**
 * @Author: zheng<PERSON><EMAIL>
 * @Date: 2024/8/25
 */
@Component
@Slf4j
public class ShareBuilderService {
    public void putShareAble(DealCtx ctx, DealGroupPBO result) {
        // 渠道专享立减，禁用分享按钮
        if (!enbaleShareAbleByPageSource(ctx) && ctx.getPriceContext().isHasExclusiveDeduction()) {
            result.setShareAble(false);
        }
        // 直播渠道品专享 禁止分享
        if (ctx.getMLiveChannel() && !LionConfigUtils.getmliveShareableSwitch() && LionConfigUtils.getForbidShareClient(ctx)) {
            result.setShareAble(false);
        }
    }

    private boolean enbaleShareAbleByPageSource(DealCtx ctx) {
        CustomShareAbleConfig customConfig = Lion.getBean(APP_KEY, LionConstants.CUSTOM_PAGESOURCE_SHARE_CONFIG, CustomShareAbleConfig.class);
        // 无配置或者总开关关闭，则不能分享
        if (Objects.isNull(customConfig) || !customConfig.isEnable()) {
            return false;
        }

        // 总开关打开且无定制渠道配置，则默认所有渠道支持分享
        if (CollectionUtils.isEmpty(customConfig.getPageSourceList())) {
            return true;
        }

        if (customConfig.getPageSourceList().contains(ctx.getRequestSource())) {
            return true;
        }
        return false;
    }
}
