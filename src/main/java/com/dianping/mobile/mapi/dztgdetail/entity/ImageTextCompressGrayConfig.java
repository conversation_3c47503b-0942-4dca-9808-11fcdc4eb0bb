package com.dianping.mobile.mapi.dztgdetail.entity;

import lombok.Data;

import java.util.Map;

@Data
public class ImageTextCompressGrayConfig {

    /**
     * key：点评团单id，value：特殊的url后缀，优先级1
     */
    private Map<Integer, String> dpDealGroupId2SpecificPostfix;

    /**
     * true 则开启全量压缩, false 则全部关闭压缩。优先级2
     */
    private boolean allCompress;

    /**
     * 团单类目 -> 灰度比例(全量100）, 如 303 -> 80
     */
    private Map<Integer, Integer> category2GrayRatio;

    /**
     * 所有图片统一拼接的压缩后缀
     */
    private String commonPostfix;

}
