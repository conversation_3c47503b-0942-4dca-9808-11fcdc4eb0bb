package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.APP_KEY;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.PRICE_EXPLAIN_LINK_CONFIG;
@Component
@Slf4j
public class PriceExplainBuilderService {


    public String buildCustomPriceDescJumpUrl(DealCtx ctx) {
        EnvCtx envCtx = ctx.getEnvCtx();
        if (envCtx == null) {
            return null;
        }
        String platform = envCtx.isMt() ? "mt" : "dp";
        String key = platform + "_" + ctx.getCategoryId();
        Map<String, String> map = Lion.getMap(APP_KEY, PRICE_EXPLAIN_LINK_CONFIG, String.class, Maps.newHashMap());
        String jumpUrl = map.get(key);
        if (StringUtils.isBlank(jumpUrl)) {
            return null;
        }
        long dpDealGroupId = ctx.getDpId();
        return String.format("%s?dpLongShopId=%d&dpDealGroupId=%d", jumpUrl, ctx.getDpLongShopId(),dpDealGroupId);
    }
}
