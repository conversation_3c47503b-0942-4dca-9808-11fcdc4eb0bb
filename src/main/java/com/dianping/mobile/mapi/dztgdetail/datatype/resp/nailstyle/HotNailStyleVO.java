package com.dianping.mobile.mapi.dztgdetail.datatype.resp.nailstyle;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImageTagVO;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-03
 * @desc 热门款式模型-单个
 */
@Data
@MobileDo(id = 0xa772)
public class HotNailStyleVO implements Serializable {
    @FieldDoc(description = "款式标签")
    @MobileDo.MobileField(key = 0x342f)
    private List<ImageTagVO> tags;

    @FieldDoc(description = "款式热度")
    @MobileDo.MobileField(key = 0x8c57)
    private String popularity;

    @FieldDoc(description = "热门主题跳链")
    @MobileDo.MobileField(key = 0x9802)
    private String moreStyleUrl;

    @FieldDoc(description = "款式主题")
    @MobileDo.MobileField(key = 0x4921)
    private String styleTheme;

    @FieldDoc(description = "热门款式小图链接")
    @MobileDo.MobileField(key = 0x1cde)
    private List<String> nailStyleUrl;
}
