package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.deal.sales.common.datatype.*;
import com.dianping.deal.sales.common.enums.*;
import com.dianping.deal.stock.dto.ProductGroupStock;
import com.dianping.deal.stock.dto.ProductStock;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealStockSaleWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.constants.MttgDetailLionKeys;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.util.*;
import com.google.common.collect.Maps;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.StockDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.SaleConstants.*;

public class StockSalesProcessor extends AbsDealProcessor {

    Logger logger = LoggerFactory.getLogger(StockSalesProcessor.class);
    @Autowired
    private DealStockSaleWrapper dealStockSaleWrapper;
    @Autowired
    private DouHuBiz douHuBiz;

    public static final String SALEDESC = "已售";
    public static final String LEAVE = "仅剩";

    private String mtShopIdStr;
    private String dpShopIdStr;

    @Override
    public boolean isEnable(DealCtx ctx) {
        return !RequestSourceEnum.fromTradeSnapshot(ctx.getRequestSource());
    }

    @PostConstruct
    public void init() {
        try {
            mtShopIdStr = Lion.createFileConfigClient(LionConstants.LIREN_NATIONWIDE_SALES_LION_FILE_CONFIG_CLIENT)
                    .getFileContent(LionConstants.LIREN_NATIONWIDE_SALES_LION_FILE_CONFIG_MT_CONTENT);
            dpShopIdStr = Lion.createFileConfigClient(LionConstants.LIREN_NATIONWIDE_SALES_LION_FILE_CONFIG_CLIENT)
                    .getFileContent(LionConstants.LIREN_NATIONWIDE_SALES_LION_FILE_CONFIG_DP_CONTENT);
        } catch (Exception e) {
            Cat.logErrorWithCategory("ReadLionFileError", new Throwable());
            logger.error("getLionFile error", e);
        }
    }

    @Override
    public void prepare(DealCtx ctx) {
        if (GreyUtils.enableQueryCenterForMainApi(ctx)) {
            SalesDisplayRequest singleRequest = initSalesReq(ctx);
            Future saleFuture = dealStockSaleWrapper.preUnifiedStockFuture(singleRequest);
            ctx.getFutureCtx().setSaleFuture(saleFuture);
            Future preSaleFuture = dealStockSaleWrapper.getBySpuProduct(ctx.getDpId());
            ctx.getFutureCtx().setPreSaleFuture(preSaleFuture);
//            // 超团使用门店全年销量
//            if (CustomAtmosphereUtils.distributeSuperDealAtmosphere(ctx)) {
//                Future shopSaleFuture = dealStockSaleWrapper.multiGetBySpuProductFuture(ctx);
//                ctx.getFutureCtx().setShopSaleFuture(shopSaleFuture);
//            }
        } else {
            Future stockFuture = dealStockSaleWrapper.preProductGroupStock(ctx.getDpId());
            SalesDisplayRequest singleRequest = initSalesReq(ctx);
            Future saleFuture = dealStockSaleWrapper.preUnifiedStockFuture(singleRequest);
            ctx.getFutureCtx().setStockFuture(stockFuture);
            ctx.getFutureCtx().setSaleFuture(saleFuture);
            Future preSaleFuture = dealStockSaleWrapper.getBySpuProduct(ctx.getDpId());
            ctx.getFutureCtx().setPreSaleFuture(preSaleFuture);
        }
        // 获取预订单销量（预订单销量不为团购销量）
        if (DealCtxHelper.isPreOrderDeal(ctx) && !LionConfigUtils.enablePreOrderDealGroupSale()) {
            ctx.getFutureCtx().setPreOrderSaleFuture(dealStockSaleWrapper.preQueryOrderReserveCount(ctx));
        }
    }

    @Override
    public void process(DealCtx ctx) {
        if (GreyUtils.enableQueryCenterForMainApi(ctx)) {
            processByQueryCenter(ctx);
        } else {
            ProductGroupStock dealGroupStock = dealStockSaleWrapper.getFutureResult(ctx.getFutureCtx().getStockFuture());
            ctx.setDealGroupStock(dealGroupStock);
            if (!isPreSale(ctx, dealGroupStock)) {
                SalesDisplayDTO salesDisplayDTO = dealStockSaleWrapper.getFutureResult(ctx.getFutureCtx().getSaleFuture());
                ctx.setSalesDisplayDTO(salesDisplayDTO);
            }
        }
        // 处理预订单销量（预订单销量不为团购销量）
        if (DealCtxHelper.isPreOrderDeal(ctx) && !LionConfigUtils.enablePreOrderDealGroupSale()) {
            long preOrderSale = dealStockSaleWrapper.getOrderReserveCount(ctx.getFutureCtx().getPreOrderSaleFuture(), ctx);
            ctx.setPreOrderSales(preOrderSale);
        }
    }

    private void processByQueryCenter(DealCtx ctx) {
        ProductGroupStock dealGroupStock = trans2OldDTO(ctx.getDealGroupDTO());
        ctx.setDealGroupStock(dealGroupStock);
//        if (CustomAtmosphereUtils.distributeSuperDealAtmosphere(ctx)) {
//            ctx.setBaseSaleMap(dealStockSaleWrapper.getFutureResult(ctx.getFutureCtx().getShopSaleFuture()));
//        }
        if (!isPreSale(ctx, dealGroupStock)) {
            SalesDisplayDTO salesDisplayDTO = dealStockSaleWrapper.getFutureResult(ctx.getFutureCtx().getSaleFuture());
            ctx.setSalesDisplayDTO(salesDisplayDTO);
        }
    }

    private ProductGroupStock trans2OldDTO(DealGroupDTO newDealGroup) {
        if (newDealGroup == null) {
            return null;
        }
        StockDTO stock = newDealGroup.getStock();
        ProductGroupStock productGroupStock = new ProductGroupStock();
        if (stock == null) {
            return productGroupStock;
        }
        productGroupStock.setProductGroupId(newDealGroup.getDpDealGroupIdInt());
        productGroupStock.setDpSales(stock.getDpSales());
        productGroupStock.setDpTotal(stock.getDpTotal());
        productGroupStock.setDpRemain(stock.getDpRemain());
        productGroupStock.setMtSales(stock.getMtSales());
        productGroupStock.setMtTotal(stock.getMtTotal());
        productGroupStock.setMtRemain(stock.getMtRemain());
        productGroupStock.setStatus(stock.getStatus());
        productGroupStock.setDpSoldOut(stock.getIsDpSoldOut());
        productGroupStock.setMtSoldOut(stock.getIsMtSoldOut());
        productGroupStock.setProductStocks(tans2OldDTO(newDealGroup.getDpDealGroupIdInt(), newDealGroup.getDeals()));
        return productGroupStock;
    }

    private ProductStock convertFromDealGroupDealDTO(Integer dpDealGroupIdInt, Integer dealIdInt, StockDTO stock) {
        ProductStock productStock = new ProductStock();
        productStock.setProductId(dealIdInt);
        productStock.setProductGroupId(dpDealGroupIdInt);
        productStock.setDpSales(stock.getDpSales());
        productStock.setDpTotal(stock.getDpTotal());
        productStock.setDpRemain(stock.getDpRemain());
        productStock.setMtSales(stock.getMtSales());
        productStock.setMtTotal(stock.getMtTotal());
        productStock.setMtRemain(stock.getMtRemain());
        productStock.setStatus(stock.getStatus());
        productStock.setDpSoldOut(stock.getIsDpSoldOut());
        productStock.setMtSoldOut(stock.getIsMtSoldOut());
        return productStock;
    }

    public List<ProductStock> tans2OldDTO(Integer dpDealGroupIdInt, List<DealGroupDealDTO> deals) {
        return deals.stream()
                .filter(dealGroupDealDTO -> filterByStatus(dealGroupDealDTO))
                .map(dealGroupDealDTO -> convertFromDealGroupDealDTO(dpDealGroupIdInt, dealGroupDealDTO.getDealIdInt(), dealGroupDealDTO.getStock()))
                .collect(Collectors.toList());
    }

    public boolean filterByStatus(DealGroupDealDTO dealGroupDealDTO) {
        boolean filter = Lion.getBoolean(LionConstants.APP_KEY, LionConstants.FILTER_STATUS_FOR_MULTI_SKU, false);
        if (!filter) {
            return true;
        }
        // 如果需要校验套餐状态，则需要status=1
        return Objects.nonNull(dealGroupDealDTO.getBasic()) && Objects.nonNull(dealGroupDealDTO.getBasic().getStatus()) && dealGroupDealDTO.getBasic().getStatus().equals(1);
    }

    /**
     * 是否是预售
     *
     * @param ctx
     * @param dealGroupStock
     * @return
     */
    private boolean isPreSale(DealCtx ctx, ProductGroupStock dealGroupStock) {
        try {
            if (!DealAttrHelper.isPreSale(ctx.getAttrs())) {
                return false;
            }
            SpuSale groupSale = dealStockSaleWrapper.getFutureResult(ctx.getFutureCtx().getPreSaleFuture());
            ctx.setSalesDisplayDTO(transferSale(groupSale, dealGroupStock, ctx));
        } catch (Exception e) {
            logger.error("isPreSale error", e);
        }
        return true;
    }

    private SalesDisplayDTO transferSale(SpuSale groupSale, ProductGroupStock stock, DealCtx context) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.StockSalesProcessor.transferSale(SpuSale,ProductGroupStock,DealCtx)");
        SalesDisplayDTO salesDisplayDTO = new SalesDisplayDTO();
        try {
            Long sales = 0l;
            if (groupSale != null) {
                SaleItem saleItem = groupSale.getAccumSale();
                sales = saleItem.getMtSales() + saleItem.getDpSales();
            }
            salesDisplayDTO.setSales(sales.intValue());
            salesDisplayDTO.setCityId(context.isMt() ? context.getMtCityId() : context.getDpCityId());
            long shopid = context.isMt() ?
                    context.getMtLongShopId() : context.getDpLongShopId();
            salesDisplayDTO.setShopId(shopid);
            BigDecimal remainderStock = new BigDecimal(context.isMt() ? stock.getMtRemain() : stock.getDpRemain());
            BigDecimal totalStock = new BigDecimal(context.isMt() ? stock.getMtTotal() : stock.getDpTotal());
            //销量描述和库存挂钩
            if (remainderStock(remainderStock, totalStock)) {
                salesDisplayDTO.setSalesTag(LEAVE + remainderStock.toString());
            } else {
                if (getDisplaySalesFuzzy()) {
                    salesDisplayDTO.setSalesTag(SALEDESC + formatSaleCount(salesDisplayDTO.getSales()));
                } else {
                    salesDisplayDTO.setSalesTag(SALEDESC + salesDisplayDTO.getSales());
                }
            }
        } catch (Exception e) {
            logger.error("transferSale error", e);
        }
        return salesDisplayDTO;
    }

    /**
     * 如果库存小于1/10时，显示仅剩xxx
     *
     * @param remainderStock
     * @param totalStock
     * @return
     */
    private boolean remainderStock(BigDecimal remainderStock, BigDecimal totalStock) {
        return remainderStock.divide(totalStock, 1, BigDecimal.ROUND_HALF_UP)
                .compareTo(BigDecimal.valueOf(0.1)) < 0;

    }

    private SalesDisplayRequest initSalesReq(DealCtx ctx) {
        if (ctx.isMt()) {
            ProductParam productParam = ProductParam.productScene(ctx.getMtId(), ctx.getMtCityId());
            SalesDisplayRequest singleRequest = SalesDisplayRequest.singleQuery(productParam);
            singleRequest.setPlatform(SalesPlatform.MT.getValue());
            singleRequest.setScene(isLiRenNationWideScene(ctx) ? SceneEnum.LIREN_NATIONWIDE.getValue() : SceneEnum.DEFAULT.getValue());
            Map<String, String> extra = Maps.newHashMap();
            //透传销量区间差异化，反爬标识
            extra.put(MTSI_FLAG, ctx.getEnvCtx().getMtsiFlag());
            putSalesGeneralSection(ctx, extra, "MTSalesGeneralSection");
            //销量口径调整AB实验
            putSalesCaliberIsQueryNew(ctx, extra, "MTSalesCaliberIsQueryNew");
            singleRequest.setExtra(extra);
            CatUtils.metric4SalesConfusion(ctx.getEnvCtx(), ctx.isMt());
            return singleRequest;
        } else {
            ProductParam productParam = ProductParam.productScene(ctx.getDpId(), ctx.getDpCityId());
            SalesDisplayRequest singleRequest = SalesDisplayRequest.singleQuery(productParam);
            singleRequest.setPlatform(SalesPlatform.DP.getValue());
            singleRequest.setScene(isLiRenNationWideScene(ctx) ? SceneEnum.LIREN_NATIONWIDE.getValue() : SceneEnum.DEFAULT.getValue());
            Map<String, String> extra = Maps.newHashMap();
            //透传销量区间差异化，反爬标识
            extra.put(MTSI_FLAG, ctx.getEnvCtx().getMtsiFlag());
            putSalesGeneralSection(ctx, extra, "DPSalesGeneralSection");
            // 销量口径调整AB实验
            putSalesCaliberIsQueryNew(ctx, extra, "DPSalesCaliberIsQueryNew");
            singleRequest.setExtra(extra);
            CatUtils.metric4SalesConfusion(ctx.getEnvCtx(), ctx.isMt());
            return singleRequest;
        }
    }

    private void putSalesGeneralSection(DealCtx ctx, Map<String, String> extra, String module) {
        if(Lion.getBoolean(LionConstants.APP_KEY,LionConstants.SALE_SECTION_ABEXP_SWITCH, false)){
            // 常规销量区间化ab
            ModuleAbConfig moduleAbConfig = douHuBiz.getAbExpResult(ctx, module);
            if (moduleAbConfig == null || moduleAbConfig.getConfigs() == null) {
                return;
            }
            AbConfig abConfig = moduleAbConfig.getConfigs().get(0);
            if (abConfig == null) {
                return;
            }
            ctx.getModuleAbConfigs().add(moduleAbConfig);
            if (abConfig.isUseNewStyle()) {
                extra.put(CONFUSION_FLAG, GENERAL_SECTION);
            }
        }else{
            extra.put(CONFUSION_FLAG, GENERAL_SECTION);
        }
    }

    // 从Lion配置获取灰度城市列表，判断当前城市是否在列表中
    private boolean isGrayCity(DealCtx ctx) {
        if (ctx.isMt()) {
            List<Integer> grayCitys = LionConfigUtils.getMtGrayCitys();
            if (grayCitys.contains(-1) || grayCitys.contains(ctx.getMtCityId())) { //有-1则所有城市走AB
                return true;
            }
        } else {
            List<Integer> grayCitys = LionConfigUtils.getDpGrayCitys();
            if (grayCitys.contains(-1) || grayCitys.contains(ctx.getDpCityId())) { //有-1则所有城市走AB
                return true;
            }
        }
        return false;
    }

    // 销量口径调整 AB
    private void putSalesCaliberIsQueryNew(DealCtx ctx, Map<String, String> extra, String module) {
        if (!isGrayCity(ctx)) {
            extra.put(IS_QUERY_NEW, "false");
            return;
        }

        // 获取AB实验结果
        ModuleAbConfig moduleAbConfig = douHuBiz.getAbExpResultByUuidAndDpid(ctx, module);
        if (moduleAbConfig == null || moduleAbConfig.getConfigs() == null) {
            extra.put(IS_QUERY_NEW, "true");
            return;
        }
        AbConfig abConfig = moduleAbConfig.getConfigs().get(0);
        if (abConfig == null) {
            extra.put(IS_QUERY_NEW, "true");
            return;
        }
        ctx.getModuleAbConfigs().add(moduleAbConfig);
        if (abConfig.isUseNewStyle()) {
            extra.put(IS_QUERY_NEW, "true");
        } else {
            extra.put(IS_QUERY_NEW, "false");
        }
    }

    private boolean isGrayScene(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.StockSalesProcessor.isGrayScene(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        if (ctx.isMt()) {
            List<Long> mtGrayPoiList = Lion.getList(LionConstants.LIREN_NATIONWIDE_SALES_GRAY_MT_POIS, Long.class);
            List<Integer> mtGrayCityList = Lion.getList(LionConstants.LIREN_NATIONWIDE_SALES_GRAY_MT_CITYS, Integer.class);
            if (CollectionUtils.isNotEmpty(mtGrayPoiList) && mtGrayPoiList.contains(ctx.getMtLongShopId())
                    && CollectionUtils.isNotEmpty(mtGrayCityList) && mtGrayCityList.contains(ctx.getMtCityId())) {
                return true;
            }
        } else {
            List<Long> dpGrayPoiList = Lion.getList(LionConstants.LIREN_NATIONWIDE_SALES_GRAY_DP_POIS, Long.class);
            List<Integer> dpGrayCityList = Lion.getList(LionConstants.LIREN_NATIONWIDE_SALES_GRAY_DP_CITYS, Integer.class);
            if (CollectionUtils.isNotEmpty(dpGrayPoiList) && dpGrayPoiList.contains(ctx.getDpLongShopId())
                    && CollectionUtils.isNotEmpty(dpGrayCityList) && dpGrayCityList.contains(ctx.getDpCityId())) {
                return true;
            }
        }
        return false;
    }

    private boolean isLiRenNationWideScene(DealCtx ctx) {
        try {
            List<String> grayShopIds = ctx.isMt() ? DataUtils.toList(mtShopIdStr, ",") : DataUtils.toList(dpShopIdStr, ",");
            long shopId = ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId();
            if (CollectionUtils.isEmpty(grayShopIds)) {
                return false;
            }
            return grayShopIds.contains(String.valueOf(shopId));
        } catch (Exception e) {
            logger.error("isLiRenNationWideScene error", e);
            //lionFile读取失败，走老的兜底逻辑
            return isGrayScene(ctx);
        }
    }

    private static String formatSaleCount(int saleCount) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.StockSalesProcessor.formatSaleCount(int)");
        String saleCountStr;
        if (saleCount < 50) {
            saleCountStr = String.valueOf(saleCount);
        } else if (saleCount < 100) {
            saleCountStr = (saleCount / 10 * 10) + "+";
        } else if (saleCount < 1000) {
            saleCountStr = (saleCount / 100 * 100) + "+";
        } else if (saleCount < 10000) {
            saleCountStr = (saleCount / 1000 * 1000) + "+";
        } else {
            saleCountStr = (saleCount / 10000) + "." + ((saleCount % 10000) / 1000) + "万+";
        }
        return saleCountStr;
    }

    /**
     * 销量区间化开关，true 不返回真实销量，false 返回真实销量，默认值 false
     *
     * @return bool
     */
    private boolean getDisplaySalesFuzzy() {
        return (Lion.getBoolean("com.sankuai.dzu.tpbase.dztgdetailweb", MttgDetailLionKeys.DISPLAY_SALES_FUZZY, false));
    }

}