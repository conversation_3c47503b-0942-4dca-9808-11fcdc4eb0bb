package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.lion.facade.LionFacade;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.feature.model.FeatureDetailDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.feature.model.PreOrderFeatureConfigDTO;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ImageScaleEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FlashDealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.LayerConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.atmosphere.HeadPicAtmosphere;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.dianping.mobile.mapi.dztgdetail.entity.*;
import com.dianping.mobile.mapi.dztgdetail.entity.dinner.DinnerDealGrayConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.repaircare.CategoryPicConfig;
import com.dianping.mobile.mapi.dztgdetail.helper.config.InsuranceConfigDTO;
import com.dianping.mobile.mapi.dztgdetail.helper.config.InsuranceItemConfigDTO;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.reflect.TypeToken;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import javax.annotation.Nullable;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.*;

/**
 * <AUTHOR>
 * @date 2022/5/31
 */
@Slf4j
public class LionConfigUtils {
    private static final List<String> DEFAULT_REVIEWER_TAB_CONFIG = Lists.newArrayList(
            "tuandeal_newtuandealtab_reviews",
            "tuandeal_newtuandealtab_medicinehealthcheck_reviews",
            "gcdealdetail_newtuandealtab_reviews",
            "gcdealdetail_newtuandealtab_medicinehealthcheck_reviews");
    private static List<ActivityConfig> IMG_TEXT_ACTIVITY_CONFIGS = Lists.newArrayList();

    static {
        Lion.addConfigListener("com.sankuai.dzu.tpbase.dztgdetailweb", "default", LionConstants.TUAN_ACTIVITY_CONFIGS, configEvent -> {
            refreshActivityConfig(GsonUtils.fromJsonString(configEvent.getConfigValue().getValue(), new TypeToken<List<ActivityConfig>>(){}.getType()));
            log.info("[TUAN_ACTIVITY_CONFIGS] changed from {} to {}", configEvent.getOldValue(), configEvent.getValue());
        });

        refreshActivityConfig(Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb", LionConstants.TUAN_ACTIVITY_CONFIGS, ActivityConfig.class));
    }

    private static void refreshActivityConfig(List<ActivityConfig> activityConfigs) {
        IMG_TEXT_ACTIVITY_CONFIGS = activityConfigs == null ? Lists.newArrayList() : activityConfigs;
    }

    public static List<ActivityConfig> getImgTextActivityConfigs() {
        return IMG_TEXT_ACTIVITY_CONFIGS;
    }

    public static List<String> getMtsiFlagStatusWhiteList(){
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils.getMtsiFlagStatusWhiteList()");
        return Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb.mtsiFlagStatus.whiteList", String.class, Lists.newArrayList());
    }

    public static boolean requestSourceFromProductTagEnable() {
        return Lion.getBoolean("com.sankuai.dzu.tpbase.dztgdetailweb", "request.source.from.product.tag.enable", false);
    }

    public static List<Integer> getMtGrayCitys() {
        return Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.mtgraycitys", Integer.class, new ArrayList<>());
    }

    public static List<Integer> getDpGrayCitys() {
        return Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.dpgraycitys", Integer.class, new ArrayList<>());
    }
    public static boolean isSnapShotPhoto(int categoryId) {
        List<Integer> snapShotPhotoList = Lion.getList(LionConstants.APP_KEY,
                LionConstants.SNAPSHOTPHOTO_CATEGORYIDS, Integer.class, Collections.emptyList());
        return CollectionUtils.isNotEmpty(snapShotPhotoList) && snapShotPhotoList.contains(categoryId);
    }

    public static boolean hideReserveEntrance(int categoryId) {
        List<Integer> snapShotPhotoList = Lion.getList(LionConstants.APP_KEY,
                LionConstants.HIDE_RESERVE_ENTRANCE, Integer.class, Collections.emptyList());
        return CollectionUtils.isNotEmpty(snapShotPhotoList) && snapShotPhotoList.contains(categoryId);
    }

    public static String getRankSceneByCategory(Long categoryId) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils.getRankSceneByCategory(java.lang.Long)");
        Map<String, String> categorySceneMap = Lion.getMap(LionConstants.APP_KEY,
                LionConstants.RANK_SCENE_RELATION, String.class, Collections.emptyMap());
        if (MapUtils.isNotEmpty(categorySceneMap) && categorySceneMap.containsKey(String.valueOf(categoryId))) {
            return categorySceneMap.get(String.valueOf(categoryId));
        }
        return null;
    }

    public static boolean hitCustomRefundCategoryConfig(int categoryId) {
        List<Integer> customRefundCategoryConfig = Lion.getList(LionConstants.APP_KEY,
                LionConstants.CUSTOM_REFUND_CATGORY_CONFIG, Integer.class, Collections.emptyList());
        return CollectionUtils.isNotEmpty(customRefundCategoryConfig) && customRefundCategoryConfig.contains(categoryId);
    }

    /**
     * 判断是否是境外门店
     * @param categoryId
     * @return
     */
    public static boolean hitOverseasCategoryConfig(int categoryId) {
        List<Integer> customRefundCategoryConfig = Lion.getList(LionConstants.APP_KEY,
                LionConstants.CUSTOM_OVERSEAS_CATGORY_CONFIG, Integer.class, Collections.emptyList());
        return CollectionUtils.isNotEmpty(customRefundCategoryConfig) && customRefundCategoryConfig.contains(categoryId);
    }

    public static boolean showPriceProtectionInfo() {
        return Lion.getBoolean(APP_KEY, PRICE_PROTECTION_SHOW_SWITCH, true);
    }

    public static boolean showPriceProtectionInfo(int categoryId) {
        List<Integer> priceProtectionCategoryIds = Lion.getList(LionConstants.APP_KEY, LionConstants.PRICE_PROTECTION_CATEGORY_IDS, Integer.class, new ArrayList<>());
        return priceProtectionCategoryIds.contains(categoryId) && Lion.getBoolean(APP_KEY, PRICE_PROTECTION_SHOW_SWITCH, true);
    }

    public static boolean hasCustomHeaderPic(String industry, int categoryId) {
        Map<String, String> config = Lion.getMap(LionConstants.APP_KEY,
                LionConstants.CUSTOM_HEADER_PIC_CONFIG, String.class, Collections.emptyMap());
        if (MapUtils.isEmpty(config) || !config.containsKey(industry) || StringUtils.isEmpty(config.get(industry))) {
            return false;
        }

        List<Integer> categoryIds = Splitter.on(",").splitToList(config.get(industry)).stream().map(Integer::parseInt).collect(Collectors.toList());
        return categoryIds.contains(categoryId);
    }

    public static String getHeaderPicProcessor(int categoryId) {
        Map<String, String> config = Lion.getMap(LionConstants.APP_KEY,
                LionConstants.CATEGORY_HEADER_PIC_PROCESSOR_CONFIG, String.class, Collections.emptyMap());
        if (MapUtils.isEmpty(config) || !config.containsKey(String.valueOf(categoryId))) {
            return config.get("default");
        }

        return config.get(String.valueOf(categoryId));
    }

    public static boolean hitDealTag(List<Long> tagIds) {
        List<Long> config = Lion.getList(LionConstants.APP_KEY,
                LionConstants.BEAUTY_HEADER_PIC_DEAL_TAG_CONFIG, Long.class, Collections.emptyList());
        if (CollectionUtils.isEmpty(config)) {
            return false;
        }

        for (Long tagId : tagIds) {
            if (config.contains(tagId)) {
                return true;
            }
        }
        return false;
    }

    public static String getCategoryHeaderScale(int categoryId) {
        Map<String, String> config = Lion.getMap(LionConstants.APP_KEY,
                LionConstants.CATEGORY_HEADER_PIC_SCALE_CONFIG, String.class, Collections.emptyMap());
        if (MapUtils.isEmpty(config) || !config.containsKey(String.valueOf(categoryId))) {
            return ImageScaleEnum.SIXTEEN_TO_NINE.getScale();
        }

        return config.get(String.valueOf(categoryId));
    }

    @Nullable
    public static HeaderPicExhibitShowRuleConfig getHeaderPicShowRule(int categoryId) {
        HeaderPicExhibitShowRuleConfig defaultConfig = HeaderPicExhibitShowRuleConfig.builder().showHeaderPic(true).showExhibit(false).build();
        Map<String, HeaderPicExhibitShowRuleConfig> config = Lion.getMap(LionConstants.APP_KEY,
                LionConstants.HEADER_PIC_EXHIBIT_SHOW_RULE_CONFIG, HeaderPicExhibitShowRuleConfig.class, Collections.emptyMap());
        if (MapUtils.isEmpty(config) || !config.containsKey(String.valueOf(categoryId))) {
            return defaultConfig;
        }

        HeaderPicExhibitShowRuleConfig ruleConfig = config.get(String.valueOf(categoryId));
        return ruleConfig;
    }

    public static boolean hitExhibitDealIdsConfig(int categoryId, Long dealId) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils.hitExhibitDealIdsConfig(int,java.lang.Long)");
        Map<String, String> config = Lion.getMap(LionConstants.APP_KEY,
                LionConstants.INDUSTRY_SCALE_DEAL_WHITELIST_CONFIG, String.class, Collections.emptyMap());
        if (MapUtils.isEmpty(config) || !config.containsKey(String.valueOf(categoryId))) {
            return false;
        }

        String whitelistConfig = config.get(String.valueOf(categoryId));
        if (StringUtils.isBlank(whitelistConfig)) {
            return false;
        }

        List<String> whitelist = Splitter.on(",").splitToList(whitelistConfig);
        if (whitelist.contains(String.valueOf(dealId))) {
            return true;
        }
        return false;
    }

    public static boolean callExhibitInfo(Integer categoryId) {
        List<Integer> whitelist = Lion.getList(LionConstants.APP_KEY,
                LionConstants.CALL_EXHIBIT_INFO_WHITELIST_CONFIG, Integer.class, Collections.emptyList());
        if (CollectionUtils.isEmpty(whitelist)) {
            return false;
        }

        return whitelist.contains(categoryId);
    }

    public static boolean distributeImageScaleByVersion(int categoryId) {
        List<Integer> categoryIds = Lion.getList(LionConstants.APP_KEY,
                LionConstants.DISTRIBUTE_IMAGE_SCALE_WHITELIST, Integer.class, Collections.emptyList());
        if (CollectionUtils.isEmpty(categoryIds)) {
            return false;
        }

        return categoryIds.contains(categoryId);
    }

    public static boolean showDarenVideo(int categoryId) {
        List<Integer> categoryIds = Lion.getList(LionConstants.APP_KEY,
                LionConstants.SHOW_DAREN_VIDEO_CATEGORY, Integer.class, Collections.emptyList());
        if (CollectionUtils.isEmpty(categoryIds)) {
            return false;
        }

        return categoryIds.contains(categoryId);
    }

    public static boolean skipCallPrometheus(Integer categoryId) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils.skipCallPrometheus(java.lang.Integer)");
        List<Integer> config = Lion.getList(LionConstants.APP_KEY,
                LionConstants.SKIP_CALL_PROMETHEUS_CATEGORY_IDS, Integer.class, Collections.emptyList());
        if (CollectionUtils.isEmpty(config)) {
            return false;
        }

        if (config.contains(categoryId)) {
            return true;
        }
        return false;
    }

    public static String processInsuranceModule(int categoryId) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils.processInsuranceModule(int)");
        Map<String, String> config = Lion.getMap(LionConstants.APP_KEY,
                LionConstants.CATEGORY_INSURANCE_PROCESSOR_CONFIG, String.class, Collections.emptyMap());
        if (MapUtils.isEmpty(config) || !config.containsKey(String.valueOf(categoryId))) {
            return null;
        }

        return config.get(String.valueOf(categoryId));
    }

    /**
     * 头图视频迭代开关
     * @return
     */
    public static boolean getHeadVideoIterationSwitch() {
        boolean headVideoSwitch = Lion.getBoolean(LionConstants.APP_KEY, LionConstants.HEAD_VIDEO_SWITCH, false);
        return headVideoSwitch;
    }

    /**
     * 款式列表接口分scene开关
     * @param sceneCode 场景值
     * @return true/false
     */
    public static boolean getImmersiveImageSceneSwitch(String sceneCode) {
        Map<String, Boolean> sceneSwitch = Lion.getMap(APP_KEY, LionConstants.IMMERSIVE_IMAGE_SCENE_SWITCH, Boolean.class, Collections.emptyMap());
        return sceneSwitch.getOrDefault(sceneCode, false);
    }

    /**
     *  判断是否为教育在线类团购
     * @param categoryId
     * @param serviceTypeId
     * @return
     */
    public static boolean isEduOnlineDeal(int categoryId, Long serviceTypeId) {
        return serviceTypeId != null && getEduOnlineDealServiceLeafIds().contains(serviceTypeId);
    }

    /**
     * 是否可以降级美团侧的样式信息
     * 注意:本方法主要用于解决测试环境美团侧到餐信息获取不到的问题
     * 计算样式时必须调用到餐侧的信息(历史逻辑无法去掉),不降级的话,获取不到就会返回默认样式
     * 降级后正常判断是否走定制样式逻辑
     * @param serviceTypeId
     * @return
     */
    public static boolean isDealCanDegradeMtStyle(Long serviceTypeId) {
        if (serviceTypeId == null) {
            return false;
        }
        List<Long> serviceLeafIds = Lion.getList(LionConstants.APP_KEY,
                LionConstants.CAN_DEGRADE_MT_STYLE_SERVICE_LEAF_IDS, Long.class, Lists.newArrayList());
        if (CollectionUtils.isEmpty(serviceLeafIds)) {
            return false;
        }
        return serviceLeafIds.contains(serviceTypeId);
    }

    /**
     * 是否可以降级美团侧的样式信息
     * 注意:本方法主要用于解决测试环境美团侧到餐信息获取不到的问题
     * 计算样式时必须调用到餐侧的信息(历史逻辑无法去掉),不降级的话,获取不到就会返回默认样式
     * 降级后正常判断是否走定制样式逻辑
     * @param categoryId
     * @return
     */
    public static boolean isDealCanDegradeMtStyleByCategoryId(Long categoryId) {
        if (categoryId == null) {
            return false;
        }
        List<Long> categoryIds = Lion.getList(LionConstants.APP_KEY, LionConstants.CAN_DEGRADE_MT_STYLE_CATEGORY_IDS,
                Long.class, Lists.newArrayList());
        if (CollectionUtils.isEmpty(categoryIds)) {
            return false;
        }
        return categoryIds.contains(categoryId);
    }

    /**
     * 是否是多Sku分类
     * @param categoryId
     * @param serviceType
     * @return
     */
    public static boolean isUseMultiSku(int categoryId, String serviceType) {
        Map<String, MultiServiceTypeSwitchConfig> config = Lion.getMap(LionConstants.APP_KEY,
                LionConstants.MULTI_SKU_DEAL_CATEGORY_SERVICE_TYPE_WHITE, MultiServiceTypeSwitchConfig.class, Collections.emptyMap());
        MultiServiceTypeSwitchConfig currentConfig = config.get(String.valueOf(categoryId));
        if (currentConfig == null) {
            return false;
        }
        return currentConfig.isMultiSku(serviceType);
    }

    public static boolean isShowCreateOrderLayer(int categoryId, String serviceType) {
        Map<String, MultiServiceTypeSwitchConfig> config = Lion.getMap(LionConstants.APP_KEY,
                LionConstants.SHOW_CREATE_ORDER_LAYER_DEAL_CATEGORY_SERVICE_TYPE_WHITE, MultiServiceTypeSwitchConfig.class, Collections.emptyMap());
        MultiServiceTypeSwitchConfig currentConfig = config.get(String.valueOf(categoryId));
        if (currentConfig == null) {
            return false;
        }
        return currentConfig.isMultiSku(serviceType);
    }

    public static List<String> getModuleConfigKeySort(String key) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils.getModuleConfigKeySort(java.lang.String)");
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        String config = Lion.getString(LionConstants.APP_KEY, LionConstants.CAI_JI_MODULE_CONFIG_KEY_SORT, "");
        if (StringUtils.isEmpty(config)) {
            return null;
        }
        Map<String, List<String>> configMap = GsonUtils.fromJsonString(config, new TypeToken<Map<String, List<String>>>(){}.getType());
        if (MapUtils.isEmpty(configMap)) {
            return null;
        }
        return configMap.get(key);
    }

    public static List<Long> getEduDealCategoryIds() {
        return Lion.getList(LionConstants.APP_KEY,
                LionConstants.EDU_DEAL_CATEGORY_IDS, Long.class, Lists.newArrayList(1210L));
    }

    public static boolean isEduDealCategoryId(long dealCategoryId) {
        return getEduDealCategoryIds().contains(dealCategoryId);
    }

    public static List<Long> getEduOnlineDealServiceLeafIds() {
        return Lion.getList(LionConstants.APP_KEY,
                LionConstants.EDU_ONLINE_DEAL_SERVICE_LEAF_IDS, Long.class, Lists.newArrayList(134013L, 123020L));
    }

    /**
     * 获取推荐款式配置
     * @param categoryId 二级类目id
     * @return 款式配置
     */
    public static ExhibitImageConfig getRecommendExhibitImageConfig(Integer categoryId) {
        if (Objects.isNull(categoryId) || categoryId <= 0) {
            return new ExhibitImageConfig();
        }

        Map<String, ExhibitImageConfig> exhibitImageConfigMap = Lion.getMap(LionConstants.APP_KEY,
                LionConstants.RECOMMEND_EXHIBIT_IMAGE_CONFIG, ExhibitImageConfig.class, Collections.emptyMap());
        return exhibitImageConfigMap.getOrDefault(String.valueOf(categoryId), null);
    }

    /**
     * 获取私域直播小程序团详氛围条底图
     * @param categoryId 团单类目id
     * @return 底图链接
     */
    public static String getMtLiveMiniAppAtmosphereImg(Integer categoryId) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils.getMtLiveMiniAppAtmosphereImg(java.lang.Integer)");
        Map<String, String> categoryId2AtmosphereImgMap = Lion.getMap(APP_KEY, MINI_PROGRAM_LIVE_ATMOSPHERE_BASE_IMG, String.class, Collections.emptyMap());
        if (Objects.isNull(categoryId)) {
            return categoryId2AtmosphereImgMap.getOrDefault("default", StringUtils.EMPTY);
        }
        String atmosphereImg = categoryId2AtmosphereImgMap.getOrDefault(String.valueOf(categoryId), StringUtils.EMPTY);
        // 如果查不到底图，则使用 default 对应的底图兜底
        return StringUtils.isNotBlank(atmosphereImg) ? atmosphereImg : categoryId2AtmosphereImgMap.getOrDefault("default", StringUtils.EMPTY);
    }

    /**
     * 获取美甲热门款式模块开关
     * @return true/false
     */
    public static boolean getHotNailStyleModuleSwitch() {
        return Lion.getBoolean(APP_KEY, LionConstants.HOT_NAIL_STYLE_MODULE_SWITCH, false);
    }

    /**
     * 订单页款式展示开关
     * @return true/false
     */
    public static boolean getOrderNailStyleImageSwitch() {
        return Lion.getBoolean(APP_KEY, LionConstants.ORDER_NAIL_STYLE_IMAGE_SWITCH, false);
    }

    /**
     * 直播渠道品底开关
     * @return
     */
    public static boolean getmliveSwitch() {
        return Lion.getBoolean(LionConstants.APP_KEY, LionConstants.MLIVE_BUY_BAR_SWITCH, false);
    }

    /**
     * 判断商户是否在热门款式模块黑名单中
     * @param shopId 商户id
     * @param isMt 是否是美团平台
     * @return true/false
     */
    public static boolean isHotNailModuleBlackShop(Long shopId, boolean isMt) {
        if (Objects.isNull(shopId) || shopId <= 0L) {
            return false;
        }
        Map<String, List> blackList = Lion.getMap(LionConstants.APP_KEY, LionConstants.HOT_NAIL_MODULE_BLACK_LIST_SHOP, List.class, Collections.emptyMap());
        if (MapUtils.isEmpty(blackList)) {
            return false;
        }
        return blackList.containsKey(isMt ? "mt" : "dp") && blackList.get(isMt ? "mt" : "dp").contains(String.valueOf(shopId));
    }

    /**
     * 是否允许在团详页展示美甲热门款式模块
     * @param categoryId 二级类目id
     * @param serviceType 服务类型，如美甲、美睫
     * @return 是否展示
     */
    public static boolean allowDisplayHotNailModule(Integer categoryId, String serviceType) {
        if (Objects.isNull(categoryId) || categoryId <= 0 || StringUtils.isEmpty(serviceType)) {
            return false;
        }
        Map<String, List> categoryId2ServiceTypeMap = Lion.getMap(LionConstants.APP_KEY,
                LionConstants.ALLOW_HOT_NAIL_STYLE_CATEGORY_IDS, List.class, Collections.emptyMap());
        String categoryIdStr = String.valueOf(categoryId);
        return categoryId2ServiceTypeMap.containsKey(categoryIdStr)
                && categoryId2ServiceTypeMap.get(categoryIdStr).contains(serviceType);
    }
    /**
     * 直播渠道品分享功能开关
     * 默认可分享（true)
     * @return
     */
    public static boolean getmliveShareableSwitch() {
        return Lion.getBoolean(LionConstants.APP_KEY, LionConstants.MLIVE_CHANNEL_SHAREABLE_SWITCH, true);

    }

    /**
     * 订单详情页款式展示数量配置
     * @return start -> 0
     */
    public static Map<String, Integer> getOrderNailStyleNumConfig() {
        return Lion.getMap(APP_KEY, LionConstants.ORDER_NAIL_STYLE_NUM_CONFIG, Integer.class, Collections.emptyMap());
    }

    /**
     * 订单详情页款式跳转款式列表页的跳链
     * @return 形如 mt.502.order -> url
     */
    public static Map<String, String> getOrderNailStyleListUrl() {
        return Lion.getMap(APP_KEY, LionConstants.ORDER_NAIL_STYLE_LIST_URL, String.class, Collections.emptyMap());
    }

    /**
     *
     * @return
     */
    public static ReminderExtendConfig getReminderExtendInfo(String key) {
        Map<String, ReminderExtendConfig> config = Lion.getMap(LionConstants.APP_KEY,
                LionConstants.REMINDER_EXTEND_CONFIG, ReminderExtendConfig.class, Collections.emptyMap());

        return config.get(key);
    }
    /**
     * 检查春节不打烊属性
     * @param config
     * @return
     */
    public static Boolean validSpringFestivalConfig(ReminderExtendConfig config){
        if (Objects.nonNull(config) && org.apache.commons.lang.StringUtils.isNotBlank(config.getStartTime())
                && org.apache.commons.lang.StringUtils.isNotBlank(config.getEndTime())
                && isCurrentDateInRange(config.getStartTime(),config.getEndTime())
                && Objects.nonNull(config.getReminderInfo())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
    /**
     * 判断是否在时间范围内
     * @param startDateStr
     * @param endDateStr
     * @return
     */
    public static boolean isCurrentDateInRange(String startDateStr, String endDateStr) {
        // 使用ISO日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE;

        try {
            // 解析输入的日期字符串
            LocalDate startDate = LocalDate.parse(startDateStr, formatter);
            LocalDate endDate = LocalDate.parse(endDateStr, formatter);

            // 获取当前日期
            LocalDate currentDate = LocalDate.now();

            // 检查当前日期是否在输入的日期区间内
            return !currentDate.isBefore(startDate) && !currentDate.isAfter(endDate);

        } catch (Exception e) {
            // 如果输入的日期格式不正确，抛出异常或返回false
            log.error("Invalid date format. Please use ISO format (yyyy-MM-dd). error:", e);
            return false;
        }
    }
    /**
     * 获取热门款式模块对照组实验上报结果开关
     * @return true/false
     */
    public static boolean getHotNailCompareExpReportSwitch() {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils.getHotNailCompareExpReportSwitch()");
        return Lion.getBoolean(APP_KEY, LionConstants.HOT_NAIL_MODULE_DOUHU_COMPARE_EXP_REPORT_SWITCH, false);
    }

    public static boolean hitReminderInfoConfig(int categoryId) {
        List<Integer> customReminderInfoConfig = Lion.getList(LionConstants.APP_KEY,
                LionConstants.CUSTOM_REMINDER_INFO_CONFIG, Integer.class, Collections.emptyList());
        return CollectionUtils.isNotEmpty(customReminderInfoConfig) && customReminderInfoConfig.contains(categoryId);
    }

    /**
     * 判断是否用新限购条
     * @param categoryId
     * @return
     */
    public static boolean hitLimitInfoConfig(int categoryId) {
        List<Integer> customReminderInfoConfig = Lion.getList(LionConstants.APP_KEY,
                LionConstants.LIMIT_INFO_SUIT_CATEGORY_CONFIG, Integer.class, Collections.emptyList());
        return CollectionUtils.isNotEmpty(customReminderInfoConfig) && customReminderInfoConfig.contains(categoryId);
    }

    /**
     * 获取请求来源映射
     * @return true/false
     */
    public static Map<String, ModuleConfigDo> getPagesourceMapKeyValueMap() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils.getPagesourceMapKeyValueMap()");
        return Lion.getMap(LionConstants.APP_KEY,
                LionConstants.PAGE_SOURCE_TO_KEY_VALUE, ModuleConfigDo.class, Collections.emptyMap());
    }


    public static boolean isCostEffectiveByWhiteCategory(DealCtx ctx) {
        //获取特团的店铺在顶部配置中的类别ID集合
        Set<Integer> eduCostEffectiveShopAtTopConfig = LionFacade
                .getSet(LionConstants.COST_EFFECTIVE_SHOP_AT_TOP_CATEGORY_IDS , Integer.TYPE, Collections.emptySet());
        if (ctx == null || CollectionUtils.isEmpty(eduCostEffectiveShopAtTopConfig) || ctx.getChannelDTO() == null) {
            return false;
        }
        return RequestSourceEnum.COST_EFFECTIVE.getSource().equals(ctx.getRequestSource())
                && eduCostEffectiveShopAtTopConfig.contains(ctx.getChannelDTO().getCategoryId());
    }

    public static Set<String> getFilterReviewerTabConfig() {
        List<String> queryCenterDealGroupAttrKey = Lion.getList(LionConstants.APP_KEY,
                LionConstants.FILTER_REVIEWER_TAB_CONFIG, String.class, DEFAULT_REVIEWER_TAB_CONFIG);

        return new HashSet<>(queryCenterDealGroupAttrKey);
    }

    public static boolean getForbidShareClient(DealCtx ctx) {
        List<Integer> forbidShareClientList = Lion.getList(LionConstants.APP_KEY, LionConstants.FORBID_SHARE_CLINT_CONFIG, Integer.class);
        return forbidShareClientList.contains(ctx.getEnvCtx().getDztgClientTypeEnum().getCode());
    }

    /**
     * 获取适用门店位置配置
     * @return
     */
    public static List<ApplyShopPositionConfig> getApplyShopPositionConfig() {
        return Lion.getList(APP_KEY, LionConstants.APPLY_SHOP_POSITION_CONFIG, ApplyShopPositionConfig.class,
                Collections.emptyList());
    }

    /**
     * 获取processor粒度的接口端配置信息
     * @return
     */
    public static List<UrlProcessorDztgClient> getUrlProcessorDztgClientList() {
        return Lion.getList(APP_KEY, LionConstants.URL_PROCESSOR_DZTG_CLIENT_CONFIG, UrlProcessorDztgClient.class, Lists.newArrayList());
    }

    /**
     * 获取接口粒度的接口端配置信息
     * @return
     */
    public static List<UrlDztgClient> getUrlDztgClientList() {
        return Lion.getList(APP_KEY, LionConstants.URL_DZTG_CLIENT_CONFIG, UrlDztgClient.class, Lists.newArrayList());
    }

    /**
     * 获取购买按钮弹窗信息配置
     * @return true/false
     */
    public static Map<String, PurchaseMessageConfig> getPurchaseBtnMessageConfig() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils.getPurchaseBtnMessageConfig()");
        return Lion.getMap(LionConstants.APP_KEY,
                LionConstants.PURCHASE_BTN_MESSAGE_CONFIG, PurchaseMessageConfig.class, Collections.emptyMap());
    }

    /**
     * 获取同店比价样式的配置
     * @param
     * @return
     */
    public static DealRepurchaseConfig getRepurchaseConfig() {
        return Lion.getBean(APP_KEY,
                LionConstants.REPURCHASE_CONFIG, DealRepurchaseConfig.class);
    }

    /**
     * 复购货架 配置信息
     * enableSwitch：总开关
     * allPass：不区分行业和城市放量开关，默认false
     * categoryIds：适用行业的类目id, 如果为空则不区分行业
     * mtCityIds ：适用美团的城市id，如果为空则不区分城市
     * dpCityIds：适用的点评城市id，如果为空则不区分城市
     * 优先级：
     * enableSwitch > allPass > categoryIds &&（mtCityIds || dpCityIds）> （mtCityIds || dpCityIds）|| categoryIds
     *
     * @param isMt
     * @return
     */
    public static boolean enableRepurchase(int categoryId, boolean isMt, int cityId){
        DealRepurchaseConfig repurchaseConfig = LionConfigUtils.getRepurchaseConfig();
        if (Objects.nonNull(repurchaseConfig) && repurchaseConfig.isEnableSwitch()){
            if (repurchaseConfig.isAllPass()){
                // 不区分行业和城市推广
                return Boolean.TRUE;
            }
            List<Integer> categoryIds = repurchaseConfig.getCategoryIds();
            List<Integer> cityIds;

            if (isMt){
                cityIds = repurchaseConfig.getMtCityIds();
            }else {
                cityIds = repurchaseConfig.getDpCityIds();
            }
            // 在“指定行业”和“指定城市”下推广
            if (CollectionUtils.isNotEmpty(categoryIds) && CollectionUtils.isNotEmpty(cityIds)){
                return categoryIds.contains(categoryId) && cityIds.contains(cityId);
            }
            // 在“指定行业”下推广
            if (CollectionUtils.isNotEmpty(categoryIds) ){
                return categoryIds.contains(categoryId);
            }
            // 在“指定城市”下推广
            if ( CollectionUtils.isNotEmpty(cityIds)){
                return cityIds.contains(cityId);
            }
        }
        // 兜底逻辑
        return Boolean.FALSE;
    }

    /**
     * 获取穿戴甲款式查询条目配置
     */
    public static Map<String, String> getWearableNailStyleConfig() {
        return Lion.getMap(APP_KEY, LionConstants.WEARABLE_NAIL_STYLE_LIMIT_NUM, String.class, Collections.emptyMap());
    }

    /**
     * 多款式判断条目数配置
     * @param categoryId 二级类目id
     * @param serviceType 服务类型，如美甲、美睫
     * @return 条目数
     */
    public static Integer getMultiStyleSizeConfig(Long categoryId, String serviceType) {
        Map<String, Integer> map = Lion.getMap(APP_KEY, LionConstants.MULTI_STYLE_SIZE_CONFIG, Integer.class, Collections.emptyMap());
        if ((categoryId == null || categoryId <= 0L) || StringUtils.isBlank(serviceType)) {
            return map.getOrDefault("default", 10);
        }
        String key = String.valueOf(categoryId);
        if (StringUtils.isNotBlank(serviceType)) {
            key = String.format("%s.%s", categoryId, serviceType);
        }
        return map.getOrDefault(key, 10);
    }

    /**
     * 获取穿戴甲门店processor的启用开关
     * @return true/false
     */
    public static boolean getShopIDToTagIDConfig(DealGroupCategoryDTO categoryDTO) {
        if (Objects.isNull(categoryDTO) || categoryDTO.getCategoryId() == null) {
            return false;
        }
        String categoryId = categoryDTO.getCategoryId().toString();
        String key = String.format("%s.%s", categoryDTO.getCategoryId(), categoryDTO.getServiceType());
        List<String> wearableNailKeys = Lion.getList(APP_KEY, LionConstants.WEARABLE_NAIL_SHOP_CONFIG, String.class, Collections.emptyList());
        return wearableNailKeys.contains(key) || wearableNailKeys.contains(categoryId);
    }

    /**
     * 获取穿戴甲门店标签配置
     * @return 标签id
     */
    public static Map<String, Long> getWearableNailShopTagIdConfig() {
        return Lion.getMap(APP_KEY, LionConstants.WEARABLE_NAIL_SHOP_TAG_ID_CONFIG,
                Long.class, Collections.emptyMap());
    }

    /**
     * 获取同店比价样式的配置
     * @param
     * @return
     */
    public static ExpResultConfig getExpResultConfig() {
        return Lion.getBean("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.module.exp.result.config", ExpResultConfig.class);
    }

    /**
     * 获取团购按钮价格力标签类目id
     * @return
     */
    public static List<Integer> getDealBtnPriceStrengthTagCategoryIds() {
        return Lion.getList(APP_KEY, LionConstants.DEAL_BTN_PRICE_STRENGTH_TAG_CATEGORY_IDS,
                Integer.class, Lists.newArrayList(401, 506));
    }

    public static Map<Long, LayerConfig> getTradeAssuranceLayerConfig(boolean isMt) {
        Map<String, LayerConfig> map = Lion.getMap(APP_KEY, LionConstants.TRADE_ASSURANCE_LAYER_CONFIGS, LayerConfig.class, Collections.emptyMap());
        String validPrefix = isMt ? "mt" : "dp";
        return map.entrySet().stream()
                .filter(entry -> StringUtils.startsWith(entry.getKey(), validPrefix))
                .collect(Collectors.toMap(
                        entry -> NumberUtils.toLong(StringUtils.removeStart(entry.getKey(), validPrefix)),
                        Map.Entry::getValue,
                        (oldValue, newValue) -> newValue,
                        LinkedHashMap::new
                ));

    }



    /**
     * 获取日志打印条件配置
     */
    public static List<LogPrintConditionConfig> getLogPrintConditionConfig() {
        return Lion.getList(APP_KEY, LionConstants.LOG_PRINT_CONDITION_CONFIG, LogPrintConditionConfig.class,
                Collections.emptyList());
    }

    public static boolean isMtLiveMiniAppInternal() {
        return Lion.getBoolean(APP_KEY, LionConstants.ENABLE_MT_LIVE_WEIXIN_MINIAPP_INTERNAL, true);
    }

    /**
     * 获取团单价格趋势说明文案
     * @param categoryId 团单二级类目
     * @return 价格趋势说明文案
     */
    public static String getDealPriceTrendDesc(Integer categoryId) {
        if (!NumbersUtils.greaterThanZero(categoryId)) {
            return StringUtils.EMPTY;
        }
        DealPriceTrendDescConfig priceTrendDescConfig = Lion.getBean(APP_KEY, LionConstants.DEAL_PRICE_TREND_DESC,
                DealPriceTrendDescConfig.class, new DealPriceTrendDescConfig());
        if (CollectionUtils.isEmpty(priceTrendDescConfig.getCategoryIds2Desc())) {
            return priceTrendDescConfig.getDefaultDesc();
        }
        Optional<String> priceTrendDescOpt = priceTrendDescConfig.getCategoryIds2Desc().stream()
                .filter(categoryIds2Desc -> CollectionUtils.isNotEmpty(categoryIds2Desc.getCategoryIds())
                        && categoryIds2Desc.getCategoryIds().contains(categoryId))
                .map(DealPriceTrendCategoryConfig::getDesc)
                .findFirst();
        return priceTrendDescOpt.orElseGet(priceTrendDescConfig::getDefaultDesc);
    }

    /**
     * 获取分享图下发开关
     * @return
     */
    public static Boolean getScreenShotShareSwitch(){
        return Lion.getBoolean(APP_KEY, LionConstants.SCREEN_SHOT_SHARE_ENABLE, false);
    }

    /**
     * 获取玩法id
     * @return
     */
    public static Long getPlayId(){
        return Lion.getLong(APP_KEY, PLAY_ID, 1000000000438002L);
    }

    /**
     * 获取玩法中心开关
     * @return
     */
    public static Boolean hitPlayCenterServiceSwitch(EnvCtx envCtx){
        Boolean serviceSwitch = Lion.getBoolean(APP_KEY, PLAY_CENTER_SWITCH, false);
        if (!serviceSwitch){
            // false：走原逻辑
            return false;
        }

        Boolean allPass =  Lion.getBoolean(APP_KEY, PLAY_CENTER_ALL_PASS, false);
        if (allPass){
            // 走玩法 全量开关
            return true;
        }
        List<String> whiteList = Lion.getList(APP_KEY, PLAY_CENTER_WHITE_LIST, String.class, Collections.emptyList());
        // 走白名单配置
        return whiteList.contains(envCtx.getUnionId());
    }


    /**
     * 获取履约保障标签是否参与计算的开关
     * @param categoryDTO
     * @return
     */
    public static boolean getPerformanceGuaranteeSwitch(DealGroupCategoryDTO categoryDTO) {
        if (Objects.isNull(categoryDTO)) {
            return false;
        }
        try {
            String categoryId = Objects.nonNull(categoryDTO.getCategoryId()) ? categoryDTO.getCategoryId().toString() : StringUtils.EMPTY;
            String key = String.format("%s.%s", categoryDTO.getCategoryId(), categoryDTO.getServiceTypeId());
            String featureDetailMapStr = Lion.getString(APP_KEY, LionConstants.PERFORMANCE_GUARANTEE_FEATURE_DETAIL_CONFIG,  "");
            Map<String, List<FeatureDetailDTO>> featureDetailMap = GsonUtils.fromJsonString(featureDetailMapStr, new TypeToken<Map<String, List>>() {}.getType());
            if (MapUtils.isEmpty(featureDetailMap)) {
                return false;
            }
            return featureDetailMap.containsKey(key) || featureDetailMap.containsKey(categoryId);
        } catch (Exception e) {
            log.error("[LionConfigUtils] getPerformanceGuaranteeSwitch error", e);
        }
        return false;
    }

    /**
     * 获取履约保障信息列表
     * @param categoryDTO
     * @return
     */
    public static List<FeatureDetailDTO> getPerformanceGuaranteeFeatureList(DealGroupCategoryDTO categoryDTO) {
        if (Objects.isNull(categoryDTO)) {
            return Collections.emptyList();
        }
        try {
            String categoryId = Objects.nonNull(categoryDTO.getCategoryId()) ? categoryDTO.getCategoryId().toString() : StringUtils.EMPTY;
            String key = String.format("%s.%s", categoryDTO.getCategoryId(), categoryDTO.getServiceTypeId());
            String featureDetailMapStr = Lion.getString(APP_KEY, LionConstants.PERFORMANCE_GUARANTEE_FEATURE_DETAIL_CONFIG,  "");
            Map<String, List<FeatureDetailDTO>> featureDetailMap = GsonUtils.fromJsonString(featureDetailMapStr, new TypeToken<Map<String, List<FeatureDetailDTO>>>() {}.getType());
            if (MapUtils.isEmpty(featureDetailMap)) {
                return Collections.emptyList();
            }
            if (featureDetailMap.containsKey(key)) {
                return featureDetailMap.get(key);

            } else if (featureDetailMap.containsKey(categoryId)) {
                return featureDetailMap.get(categoryId);
            }
        } catch (Exception e) {
            log.error("[LionConfigUtils] getPerformanceGuaranteeFeatureList error", e);
        }
        return Collections.emptyList();
    }

    /**
     * 获取预订团单保障信息
     * @return
     */
    public static PreOrderFeatureConfigDTO getPreOrderGuaranteeFeatConfig() {
        try {
            String featureDetailStr = Lion.getString(APP_KEY, PREORDER_GUARANTEE_FEATURE_DETAIL_CONFIG, "");
            PreOrderFeatureConfigDTO preOrderFeatureConfigDTO = GsonUtils.fromJsonString(featureDetailStr, PreOrderFeatureConfigDTO.class);
            return preOrderFeatureConfigDTO;
        } catch (Exception e) {
            log.error("[LionConfigUtils] getPreOrderGuaranteeFeatureDetailMap error", e);
        }
        return null;
    }

    /**
     * 获取N选1政府消费券MRN版控版本
     * @return MRN版本号
     */
    public static String getNSelectOneGovernmentCouponMrnVersion() {
        return Lion.getString(APP_KEY, LionConstants.N_SELECT_ONE_GOVERNMENT_COUPON_MRN_VERSION, "0.5.8");
    }
    /**
     * 判断是否用定制化使用时间
     * @param categoryId
     * @return
     */
    public static boolean hitCustomAvailableTimeOfDays(int categoryId) {
        List<Integer> customReminderInfoConfig = Lion.getList(LionConstants.APP_KEY,
                CUSTOM_AVAILABLE_TIMES_OF_DAYS, Integer.class, Collections.emptyList());
        return CollectionUtils.isNotEmpty(customReminderInfoConfig) && customReminderInfoConfig.contains(categoryId);
    }

    /**
     * 获取多sku过滤开关
     * @return
     */
    public static boolean dealsFilterSwitch() {
        return Lion.getBoolean(LionConstants.APP_KEY, LionConstants.DEALS_FILTER_SWITCH, false);
    }
    /**
     * 美甲沉浸页款式置顶功能开关
     * @return
     */
    public static boolean enableExhibitSourceBeautyNailImmersiveImage() {
        return Lion.getBoolean(APP_KEY, EXHIBIT_SOURCE_BEAUTY_NAIL_IMMERSIVE_IMAGE_SWITCH, false);
    }

    /**
     * 美甲款式置顶功能开关
     * @return
     */
    public static boolean enableExhibitSourceBeautyNailTop() {
        return Lion.getBoolean(APP_KEY, EXHIBIT_SOURCE_BEAUTY_NAIL_TOP_SWITCH, false);
    }


    public static boolean isComparePriceShopBlackList(boolean isMt, String shopIdStr) {
        Map<String, List> blackList = Lion.getMap(LionConstants.APP_KEY, LionConstants.COMPARE_PRICE_SHOP_BLACK_LIST, List.class, Collections.emptyMap());
        if (MapUtils.isEmpty(blackList)) {
            return false;
        }
        return blackList.containsKey(isMt ? "mt" : "dp") && blackList.get(isMt ? "mt" : "dp").contains(shopIdStr);
    }
    /**
     * 使用新款式接口的类目
     * @param categoryId
     * @return
     */
    public static boolean useNewExhibitCategoryIds(int categoryId) {
        List<Integer> newExhibitCategoryIds = Lion.getList(LionConstants.APP_KEY,
                NEW_EXHIBIT_CAETGORY_IDS, Integer.class, Collections.emptyList());
        return CollectionUtils.isNotEmpty(newExhibitCategoryIds) && newExhibitCategoryIds.contains(categoryId);
    }

    /**
     * 判断是否命中预约入口ab实验类目
     * @param categoryId 类目id
     * @return true/false
     */
    public static boolean isInReserveEntranceAbTestCategoryIds(int categoryId) {
        List<Integer> categoryIds = Lion.getList(APP_KEY, RESERVE_ENTRANCE_AB_TEST_CATE, Integer.class, Collections.emptyList());
        return categoryIds.contains(categoryId);
    }

    /**
     * 小程序接入商家会员控制开关 true允许小程序接入商家会员,反之不允许
     * @return
     */
    public static boolean memberCardMiniProgramSwitch() {
        return Lion.getBoolean(APP_KEY, MEMBER_CARD_MINIPROGRAM_SWITCH, false);
    }

    public static boolean isBilliardsCategoryIds(int categoryId) {
        List<Integer> categoryIds = Lion.getList(APP_KEY, SELF_AUTO_OPEN_TABLE_CATEGORY, Integer.class, Collections.emptyList());
        return categoryIds.contains(categoryId);
    }

    public static boolean hitLeadsDeal(DealGroupCategoryDTO categoryDTO) {
        return hitDealCategoryByLion(categoryDTO, LEADS_DEAL_CATE_CONFIG);
    }

    public static boolean hitWeddingLeadsDeal(DealGroupCategoryDTO categoryDTO) {
        return hitDealCategoryByLion(categoryDTO, WEDDING_LEADS_DEAL_CATE_CONFIG);
    }

    public static boolean hitDealCategoryByLion(DealGroupCategoryDTO categoryDTO, String lionConfigKey) {
        if (Objects.isNull(categoryDTO)) {
            return false;
        }
        String categoryId = Objects.nonNull(categoryDTO.getCategoryId()) ? categoryDTO.getCategoryId().toString() : StringUtils.EMPTY;
        String key = String.format("%s.%s", categoryDTO.getCategoryId(), categoryDTO.getServiceTypeId());
        List<String> leadsDealCats = Lion.getList(APP_KEY, lionConfigKey, String.class, Collections.emptyList());
        if (CollectionUtils.isEmpty(leadsDealCats)) {
            return false;
        }
        return leadsDealCats.contains(key) || leadsDealCats.contains(categoryId);
    }

    /**
     * 判断是否为非营销场团单(相应的渠道)
     */
    public static boolean isNotPromotionDeal(DealCtx ctx) {
        List<String> promotionPageSourceList = Lion.getList(APP_KEY, PROMOTION_PAGE_SOURCE_LIST, String.class, Collections.emptyList());
        return !promotionPageSourceList.contains(ctx.getRequestSource());
    }

    /**
     * 获取留资型团单按钮配置
     * @param ctx
     * @return
     */
    public static LeadsDealBarConfig getLeadsDealBarConfig(DealCtx ctx) {
        if (Objects.isNull(ctx.getDealGroupDTO()) || Objects.isNull(ctx.getDealGroupDTO().getCategory())) {
            return null;
        }
        return getLeadsDealBarConfig(ctx.getDealGroupDTO().getCategory());
    }

    public static LeadsDealBarConfig getLeadsDealBarConfig(DealGroupCategoryDTO categoryDTO) {
        if (Objects.isNull(categoryDTO)) {
            return null;
        }
        String categoryId = Objects.nonNull(categoryDTO.getCategoryId()) ? categoryDTO.getCategoryId().toString() : StringUtils.EMPTY;
        String key = String.format("%s.%s", categoryDTO.getCategoryId(), categoryDTO.getServiceTypeId());
        try {
            String configStr = Lion.getString(APP_KEY, LionConstants.LEADS_DEAL_BOTTOM_BAR_CONFIG, "");
            if (StringUtils.isBlank(configStr)) {
                return null;
            }
            Map<String, LeadsDealBarConfig> configMap = JsonUtils.fromJson(configStr, new TypeToken<Map<String, LeadsDealBarConfig>>() {}.getType());
            if (MapUtils.isEmpty(configMap)) {
                return null;
            }
            if (configMap.containsKey(categoryId)) {
                return configMap.get(categoryId);
            } else if (configMap.containsKey(key)) {
                return configMap.get(key);
            }
        } catch (Exception e) {
            log.error("[LionConfigUtils] getResvAndBuyBarConfig error", e);
        }
        return null;
    }

    /**
     * 获取留资型团单最低MRN版本
     * @return MRN版本号
     */
    public static String getLeadsDealMinMrnVersion() {
        return Lion.getString(APP_KEY, LionConstants.LEADS_DEAL_MIN_MRN_VERSION, "0.5.13");
    }

    /**
     * 团详RCF快接口缓存开关
     * @return
     */
    public static boolean hitDealDetailFlashCache(int dealId) {
        int ratio = Lion.getInt(APP_KEY, DEAL_DETAIL_FLASH_CACHE_SWITCH,0);
        return dealId % 100 < ratio;
    }

    /**
     * 获取cellar缓存配置
     * @return
     */
    public static DealDetailCacheGrayConfig getDealDetailCacheGrayConfig() {
        return Lion.getBean(APP_KEY,DEAL_DETAIL_FLASH_CONFIG, DealDetailCacheGrayConfig.class);
    }

    /**
     * 根据配置判断是否走快接口
     * @param ctx
     * @param cacheConfig
     * @return
     */
    public static Boolean allowFlashCache(FlashDealCtx ctx, DealDetailCacheGrayConfig cacheConfig){
        if (Objects.isNull(cacheConfig)){
            return Boolean.FALSE;
        }
        // 总开关
        if (!cacheConfig.isEnable()){
            return Boolean.FALSE;
        }
        // 所有场景开放缓存
        if (cacheConfig.isAllowAll()){
            return Boolean.TRUE;
        }
        // 通过mrn 版本管控
        String mrn = ctx.getMrnVersion();
        if (StringUtils.isNotBlank(mrn) && VersionUtils.isLessAndEqualThan(mrn, cacheConfig.getMrnVersion())){
            return Boolean.FALSE;
        }
        // 获取serviceTypeId
        DealGroupCategoryDTO categoryDTO = Objects.nonNull(ctx.getDealGroupDTO()) ? ctx.getDealGroupDTO().getCategory() : null;
        long serviceTypeId = Objects.nonNull(categoryDTO) ? categoryDTO.getServiceTypeId() : 0L;
        List<Long> denyServiceTypeIds = cacheConfig.getDenyServiceTypeIds();
        if(CollectionUtils.isNotEmpty(denyServiceTypeIds) && denyServiceTypeIds.contains(serviceTypeId)){
            return Boolean.FALSE;
        }
        Long categoryId = Objects.nonNull(categoryDTO) ? categoryDTO.getCategoryId() : 0L;
        List<Long> denyCategoryIds = cacheConfig.getDenyCategoryIds();
        // 通过二级类目判断是否使用 缓存
        if (CollectionUtils.isNotEmpty(denyCategoryIds) && denyCategoryIds.contains(categoryId)){
            return Boolean.FALSE;
        }
        Integer dealId =  ctx.getDealflashReq().getDealgroupid();
        String unionId = ctx.getEnvCtx().getUnionId();
        int grayRatio = cacheConfig.getGrayRatio();
        List<String> unionIds = cacheConfig.getUnionIds();
        return CollectionUtils.isNotEmpty(unionIds) && (unionIds.contains(unionId))
                || (dealId % 100 < grayRatio);
    }

    /**
     * 是否是安心学的团购类目
     * @param categoryId
     * @return
     */
    public static boolean isAnXinXueCategoryIds(int categoryId) {
        List<Integer> categoryIds = Lion.getList(APP_KEY, GUARANTEE_AN_XIN_XUE_CATEGORY, Integer.class, Collections.emptyList());
        return categoryIds.contains(categoryId);
    }

    /**
     * 安心学落地页
     * @param isMt
     * @return
     */
    public static String getAnXinXueDetailPage(Boolean isMt) {
        if (isMt) {
            return Lion.getString(APP_KEY, LionConstants.GUARANTEE_AN_XIN_XUE_DETAIL_MT, "https://cube.meituan.com/cube/block/f42805397d77/317412/index.html");
        }
        return Lion.getString(APP_KEY, LionConstants.GUARANTEE_AN_XIN_XUE_DETAIL_DP, "https://cube.dianping.com/cube/block/4e4ebff170e1/317745/index.html");
    }

    /**
     * 安心练落地页
     * @param isMt
     * @return
     */
    public static String getAnXinExerciseDetailPage(Boolean isMt, Boolean isMonthly) {
        // 连续包月
        if (isMonthly){
            Map<String, String> config = getCompensationForRunningAwayConfig();
            if (isMt){
                return MapUtils.getString(config, "mtMonthJumpUrl", "https://cube.meituan.com/cube/block/fc46bf64cbba/342847/index.html");
            }
            return MapUtils.getString(config, "dpMonthJumpUrl", "https://cube.dianping.com/cube/block/fc46bf64cbba/342847/index.html");
        }

        if (isMt) {
            return Lion.getString(APP_KEY, LionConstants.GUARANTEE_AN_XIN_EXERCISE_DETAIL_MT, "https://cube.meituan.com/cube/block/728e318931ce/336447/index.html");
        }
        return Lion.getString(APP_KEY, LionConstants.GUARANTEE_AN_XIN_EXERCISE_DETAIL_DP, "https://cube.dianping.com/cube/block/728e318931ce/336447/index.html");
    }

    /**
     * 安心学icon
     * @return
     */
    public static String getAnXinXueIcon() {
        return Lion.getString(APP_KEY, LionConstants.GUARANTEE_AN_XIN_XUE_ICON, "https://p0.meituan.net/ingee/f21b4b5a0ce92992a8911a4bfbfa1e6f2519.png");
    }

    // https://p0.meituan.net/ingee/b8967d6948116a467550b870451be4f93390.png
    /**
     * 安心练icon
     * @return
     */
    public static String getAnXinExerciseIcon() {
        return Lion.getString(APP_KEY, LionConstants.GUARANTEE_AN_XIN_EXERCISE_ICON, "https://p0.meituan.net/ingee/b8967d6948116a467550b870451be4f93390.png");
    }

    /**
     * 安心学浮层type
     * @return
     */
    public static int getAnXinXueLayerType(){
        return Lion.getInt(APP_KEY, GUARANTEE_AN_XIN_XUE_LAYER_TYPE, 14);
    }

    /**
     * 安心学标签展示文案
     * @return
     */
    public static String getAnXinXueDisplayText() {
        return Lion.getString(APP_KEY, LionConstants.GUARANTEE_AN_XIN_XUE_DISPLAY_TEXT, "线上购课 · 按次扣费 · 安心退款");
    }

    /**
     * 安心练标签展示文案
     * @return
     */
    public static String getAnXinExerciseDisplayText() {
        return Lion.getString(APP_KEY, LionConstants.GUARANTEE_AN_XIN_EXERCISE_DISPLAY_TEXT, "放心付 · 安心退 · 跑路赔");
    }

    /** 团单ID和团单分类信息缓存开关
     * @return
     */
    public static boolean hitDealId2CategorySwitch(int dealId) {
        int ratio = Lion.getInt(APP_KEY, DEAL_ID_TO_CATEGORY_CACHE_SWITCH,0);
        return dealId % 100 < ratio;
    }

    public static boolean useNewSourceForCaixi() {
        return Lion.getBoolean(APP_KEY, USE_NEW_SOURCE_FOR_CAIXI_SWITCH, false);
    }

    /**
     * 判断用户是否在白名单中
     * @param userId 用户ID
     * @return true/false
     */
    public static boolean inSnapshotLogWhiteList(Long userId) {
        List<Long> userIdWhiteList = Lion.getList(APP_KEY, SNAPSHOT_LOG_USER_ID_WHITE_LIST, Long.class, Collections.emptyList());
        return userIdWhiteList.contains(userId);
    }

    public static boolean inSnapshotUserIdGray(Long userId) {
        LogUserGrayConfig grayConfig = Lion.getBean(APP_KEY, SNAPSHOT_LOG_USER_GRAY_CONFIG, LogUserGrayConfig.class, new LogUserGrayConfig(0, 100, Collections.emptyList()));
        if (NumbersUtils.lessThanAndEqualZero(userId)) {
            return false;
        }
        return (CollectionUtils.isNotEmpty(grayConfig.getGrayWhiteList()) && grayConfig.getGrayWhiteList().contains(userId) )
                || userId % grayConfig.getGrayGroupRange() < grayConfig.getGrayRange();
    }

    /**
     * 获取底部推荐相关配置参数
     */
    public static Map<String, String> getRelatedRecommendConfigMap() {
        return Lion.getMap(APP_KEY, LionConstants.RELATED_RECOMMEND_CONFIG_MAP, String.class, Collections.emptyMap());
    }

    /**
     * 是否返回同店推荐数据
     * @param categoryId
     * @return
     */
    public static boolean hitHideInShopRecommendCategoryIds(int categoryId) {
        List<Integer> hideInshopRecommendCategoryIds = Lion.getList(LionConstants.APP_KEY,
                HIDE_IN_SHOP_RECOMMEND_CATEGORY_IDS, Integer.class, Collections.emptyList());
        return CollectionUtils.isNotEmpty(hideInshopRecommendCategoryIds) && hideInshopRecommendCategoryIds.contains(categoryId);
    }

    /**
     * 是否使用底部推荐
     * @return
     */
    public static boolean useRecommendStrategy() {
        return Lion.getBoolean(APP_KEY, USE_RECOMMEND_STRATEGY_SWITCH, false);
    }


    /**
     * 使用AttributeKey中的数据查询款式Id类目信息
     * @param categoryId
     * @return
     */
    public static boolean useAttributeKeyStyleCategoryIds(int categoryId) {
        List<Integer> newExhibitCategoryIds = Lion.getList(LionConstants.APP_KEY,
                NEW_STYLE_BY_SHOP_CATEGORY_IDS, Integer.class, Collections.emptyList());
        return CollectionUtils.isNotEmpty(newExhibitCategoryIds) && newExhibitCategoryIds.contains(categoryId);
    }

    /**
     * 使用AttributeKey中的exhibits数据查询老案例Id类目信息
     * @param categoryId
     * @return
     */
    public static boolean useAttributeKeyOldCaseCategoryIds(int categoryId) {
        List<Integer> oldExhibitCategoryIds = Lion.getList(LionConstants.APP_KEY,
                OLD_CASE_BY_SHOP_CATEGORY_IDS, Integer.class, Collections.emptyList());
        return CollectionUtils.isNotEmpty(oldExhibitCategoryIds) && oldExhibitCategoryIds.contains(categoryId);
    }

    /**
     * 获取比价助手实验配置
     * @return 实验配置信息
     */
    public static Map<String, String> getComparePriceAssistantExpConfig() {
        return Lion.getMap(APP_KEY, COMPARE_PRICE_ASSISTANT_CATEGORY_AB_TEST_CONFIG, String.class, Collections.emptyMap());
    }

    /**
     * 优惠减负二期,买贵必赔价格里标签展示控制开关
     *
     * @return
     */
    public static boolean showBestPriceGuaranteeTag() {
        return Lion.getBoolean(LionConstants.APP_KEY, SHOW_BEST_PRICE_GUARANTEE_TAG, false);
    }

    /**
     * rcf头图高度计算异常日志开关
     *
     * @return
     */
    public static boolean hitRcfCalHeightLogSwitch() {
        return Lion.getBoolean(APP_KEY, RCF_CALCULATE_HEIGHT_LOG_SWITCH, false);
    }

    /**
     * 获取款式场景
     * @return
     */
    public static String getDigestScene() {
        return Lion.getString(APP_KEY, EXHIBIT_DIGEST_SCENE_CODE, "67_1");
    }

    public static List<ImageTextStrategyRule> getImageTextStrategyRules() {
        return Lion.getList(LionConstants.APP_KEY, LionConstants.IMAGE_TEXT_STRATEGY_RULE, ImageTextStrategyRule.class,
                Collections.emptyList());
    }

    /**
     * 控制团购次卡退款规则新旧版本
     */
    public static boolean hasPurchaseNoteTableSwitch() {
        return Lion.getBoolean(APP_KEY, DEAL_TIMES_CARD_REFUND_TABLE, false);
    }

    /**
     * 获取预付类目文案配置
     * @return PrepayCategoryConfig 对象
     */
    public static PrepayCategoryConfig getPrepayCategoryConfig() {
        PrepayCategoryConfig defaultPrepayCategoryConfig = new PrepayCategoryConfig();
        defaultPrepayCategoryConfig.setCategory2TextMap(Collections.emptyMap());
        return Lion.getBean(APP_KEY, PREPAY_CATEGORY_TEXT_CONFIG, PrepayCategoryConfig.class, defaultPrepayCategoryConfig);
    }

    /**
     * 开启维修二段支付团单价格说明文案
     * @return true/false
     */
    public static boolean enableShowRepairPriceDesc() {
        return Lion.getBoolean(APP_KEY, ENABLE_SHOW_REPAIR_PRICE_DESC, false);
    }

    /**
     * 获取id映射缓存刷新时间
     *
     * @return 刷新时间（秒）
     */
    public static int getIdMapperCacheRefreshTime() {
        return Lion.getInt(APP_KEY, ID_MAPPER_CACHE_REFRESH_TIME_CONFIG, 0);
    }

    /**
     * 获取id映射缓存过期时间
     *
     * @return 过期时间（秒）
     */
    public static int getIdMapperCacheExpiryTime() {
        return Lion.getInt(APP_KEY, ID_MAPPER_CACHE_EXPIRE_TIME_CONFIG, 1296000);
    }

    public static boolean timesDealExpressOptimizeSwitch() {
        return Lion.getBoolean(APP_KEY, TIMES_DEAL_OPTIMIZE_SWITCH, false);
    }

    /**
     * 是否为强预订团单
     * @param ctx
     * @return
     */
    public static boolean hitPreOrderDeal(DealCtx ctx) {
        if (Objects.isNull(ctx) || Objects.isNull(ctx.getDealGroupDTO())) {
            return false;
        }
        DealGroupCategoryDTO categoryDTO = ctx.getDealGroupDTO().getCategory();
        return hitPreOrderDeal(categoryDTO);
    }

    /**
     * 是否为强预订团单
     * @param categoryDTO
     * @return
     */
    public static boolean hitPreOrderDeal(DealGroupCategoryDTO categoryDTO) {
        if (Objects.isNull(categoryDTO)) {
            return false;
        }
        String categoryId = Objects.nonNull(categoryDTO.getCategoryId()) ? categoryDTO.getCategoryId().toString() : StringUtils.EMPTY;
        List<String> preOrderCategoryIds = Lion.getList(APP_KEY, PRE_ORDER_CATEGORY_IDS, String.class, Collections.emptyList());
        if (CollectionUtils.isEmpty(preOrderCategoryIds)) {
            return false;
        }
        return preOrderCategoryIds.contains(categoryId);
    }

    /**
     * 是否命中 神会员全聚合逻辑
     * @return
     */
    public static boolean hitCouponAggregateSwitch(long userId) {
        List<Long> whiteList = Lion.getList(APP_KEY, MAGIC_MEMBER_COUPON_AGGREGATE_WHITE_LIST, Long.class, Collections.emptyList());
        if(CollectionUtils.isNotEmpty(whiteList) && whiteList.contains(userId)) {
            // 白名单
            return true;
        }

        // userid 比例放量
        int ratio = Lion.getInt(APP_KEY, MAGIC_MEMBER_COUPON_AGGREGATE_USER_ID_RATIO, 0);
        return ratio > userId%100 ;
    }


    public static boolean showSafeImplantTag(DealCtx ctx) {
        List<String> showSafeImplantTagList = Lion.getList(LionConstants.APP_KEY,
                LionConstants.SHOW_SAFE_IMPLANT_TAG_CATEGORY_IDS, String.class, Collections.emptyList());
        // 未配置类目则不用过滤
        if (CollectionUtils.isEmpty(showSafeImplantTagList)) {
            return true;
        }

        String cateKey = CategoryServiceTypeUtils.getCateKey(ctx, "_");
        return CollectionUtils.isNotEmpty(showSafeImplantTagList) && showSafeImplantTagList.contains(cateKey);
    }

    public static HeadPicAtmosphere getSafeImplantHeadPicAtmosphere() {
        return Lion.getBean(APP_KEY, SAFE_IMPLANT_HEAD_PIC_ATMOSPHERE_CONFIG, HeadPicAtmosphere.class, new HeadPicAtmosphere());
    }

    public static List<Long> getCustomerWhiteList() {
        return Lion.getList(APP_KEY, DEAL_TIMES_CARD_REFUND_CUSTOMER_WHITELIST, Long.class, Lists.newArrayList());
    }

    public static boolean hitSelfOperatedCleaningNewReserveDegradeSwitch() {
        return Lion.getBoolean(APP_KEY, SELF_OPERATED_CLEANING_NEW_RESERVE_DEGRADE_SWITCH, false);
    }

    public static boolean enablePreOrderDealGroupSale() {
        return Lion.getBoolean(APP_KEY, PRE_ORDER_DEAL_SALE_DEAL_GROUP_SWITCH, false);
    }

    public static Map<String, CategoryPicConfig> getRepairCareCategoryPicConfig() {
        return Lion.getMap(APP_KEY, LE_REPAIR_CARE_CATEGORY_PIC_MAP, CategoryPicConfig.class);
    }

    public static boolean enableSkuAttrSort() {
        return Lion.getBoolean(APP_KEY, SKU_ATTR_SORT_SWITCH, false);
    }

    public static boolean hitSnapshotGroup(String expResult){
        List<String> snapshotExperimentGroup = Lion.getList(LionConstants.APP_KEY,
                RCF_SNAPSHOT_EXPERIMENT_GROUP, String.class, Collections.emptyList());
        return snapshotExperimentGroup.contains(expResult);
    }

    public static boolean rcfSnapshotExpResultAllPass() {
        return Lion.getBoolean(APP_KEY, RCF_SNAPSHOT_EXPERIMENT_RESULT_ALL_PASS, true);
    }

    /**
     * 团详主接口RCF数据处理开关
     * @return
     */
    public static boolean getDealBaseRcfSnapshotCacheProcessSwitch() {
        return Lion.getBoolean(LionConstants.APP_KEY, LionConstants.RCF_SNAPSHOT_CACHE_RESULT_DEAL_BASE_PROCESS, false);
    }

    /**
     * 团购次卡接入先用后付团单二级类目白名单
     *
     * @return
     */
    public static List<Integer> getProductCreditPayCategories() {
        return Lion.getList(APP_KEY, PRODUCT_CREDIT_PAY_CATEGORY, Integer.class, Collections.emptyList());
    }

    /**
     * 团购次卡接入先用后付 控制开关
     */
    public static boolean getProductCreditPaySwitch() {
        return Lion.getBoolean(APP_KEY, PRODUCT_CREDIT_PAY_SWITCH, false);
    }


    /**
     * 根据poi类目判断团单是否展示获取底价和咨询的按钮
     */
    public static boolean isHitWeddingSpecialPoiCategory(DealCtx ctx) {
        return  isHitPoiCategory(ctx, WEDDING_SPECIAL_POI_BACK_CATE_CONFIG);
    }

    /**
     * 判断是否满足 poi后台类目需求
     */
    public static boolean isHitPoiCategory(DealCtx ctx, String lionKey) {
        if(CollectionUtils.isEmpty(ctx.getPoiBackCategoryIds())){
            return false;
        }
        Set<Integer> poiCategories = LionFacade.getSet(lionKey, Integer.TYPE, Collections.emptySet());
        return !Sets.intersection(poiCategories, ctx.getPoiBackCategoryIds()).isEmpty();
    }

    /**
     * 私域直播分享获取微信名称开关
     * @return
     */
    public static boolean getWxNameQuerySwitch() {
        return Lion.getBoolean(LionConstants.APP_KEY, LionConstants.WX_NAME_QUERY_SWITCH, false);
    }

    public static Set<String> getUrlIgnoreKeys(){
        List<String> ignoreList = Lion.getList(APP_KEY, DETAIL_DIFF_IGNORE_KEYS, String.class, Collections.emptyList());
        return new HashSet<>(ignoreList);
    }

    public static boolean isHitNewPoiStyle() {
        return Lion.getBoolean(LionConstants.APP_KEY, LionConstants.POI_SELF_CLEAN_ORDER_TYPE_SWITCH, false);
    }

    /**
     * 闪购进场涉及的二级类目
     * @param categoryId
     * @return
     */
    public static boolean isHitCpvCategoryId(int categoryId) {
        List<Integer> cpvCategories = Lion.getList(LionConstants.APP_KEY, CPV_CATEGORY_CONFIG, Integer.class, Collections.emptyList());
        return CollectionUtils.isNotEmpty(cpvCategories) && cpvCategories.contains(categoryId);
    }

    public static boolean hitSpecialCategory(int categoryId) {
        if (categoryId <= 0) {
            return false;
        }
        List<Integer> categories = Lion.getList(APP_KEY, DEAL_SPECIAL_CATEGORY_HIT_CONFIG, Integer.class, Collections.emptyList());
        return categories.contains(categoryId);
    }
    public static boolean getDealStyleStatisticSwitch() {
        return Lion.getBoolean(LionConstants.APP_KEY, LionConstants.DEAL_STYLE_STATISTIC_SWITCH,  false);
    }
    public static boolean getDealStyleStatisticLogSwitch() {
        return Lion.getBoolean(LionConstants.APP_KEY, LionConstants.DEAL_STYLE_STATISTIC_LOG_SWITCH,  false);
    }

    /**
     * 安心补牙控制开关
     * @return
     */
    public static boolean dentalSwitch() {
        return Lion.getBoolean(APP_KEY,TOOTH_FILL_SWITCH,true);
    }

    public static List<Integer> getMagicCouponEnhancementCityBlackList(boolean isMt) {
        Map<String, List> cityBlackListMap = Lion.getMap(APP_KEY, MAGIC_COUPON_ENHANCEMENT_CITY_BLACK_LIST, List.class, Collections.emptyMap());
        return (List<Integer>)cityBlackListMap.getOrDefault(isMt ? "mt" : "dp", Collections.emptyList());
    }

    public static boolean isLeadsDealCateGoryId(int categoryId) {
        List<Integer> leadDealCategoryIds = Lion.getList(APP_KEY, LEADS_DEAL_CATEGORY_IDS, Integer.class, Collections.emptyList());
        return leadDealCategoryIds.contains(categoryId);
    }

    public static boolean isCountrySubsidyDealServiceTypeId(DealGroupDTO dealGroupDTO) {
        if (Objects.isNull(dealGroupDTO) || Objects.isNull(dealGroupDTO.getCategory())) {
            return false;
        }
        Long serviceTypeId = dealGroupDTO.getCategory().getServiceTypeId();
        List<Long> serviceTypeIds = Lion.getList(APP_KEY, COUNTRY_SUBSIDY_SERVICE_TYPE_IDS, Long.class, Collections.emptyList());
        return serviceTypeIds.contains(serviceTypeId);
    }

    public static boolean isCountrySubsidyClientType(String clientTypeName) {
        List<String> clientTypes = Lion.getList(APP_KEY, COUNTRY_SUBSIDY_CLIENT_TYPE, String.class, Collections.emptyList());
        return clientTypes.contains(clientTypeName);
    }

    public static boolean isEnableCountrySubsidyDegradeSwitch() {
        return Lion.getBoolean(APP_KEY, COUNTRY_SUBSIDY_DEGRADE_SWITCH, false);
    }

    public static boolean isHidePageSource(String requestSource) {
        List<String> hidePageSources = Lion.getList(APP_KEY, MAGIC_COUPON_ENHANCEMENT_HIDE_PAGE_SOURCE, String.class, Collections.emptyList());
        return hidePageSources.contains(requestSource);
    }

    public static MemberFreeConfig getMemberFreeConfig() {
        return Lion.getBean(APP_KEY, MEMBER_FREE_CONFIG, MemberFreeConfig.class);
    }

    public static List<Integer> getNibCategoryIds() {
        return Lion.getList(APP_KEY, NIB_CATEGORY_IDS, Integer.class, Collections.emptyList());
    }

    public static List<String> getMonthlySubscriptionCategoryList(String categoryId) {
        Map<String, List> cityBlackListMap = Lion.getMap(APP_KEY, MONTHLY_SUBSCRIPTION_CATEGORY_IDS, List.class, Collections.emptyMap());
        if (MapUtils.isEmpty(cityBlackListMap)) {
            return Collections.emptyList();
        }
        return (List<String>)cityBlackListMap.getOrDefault(categoryId, Collections.emptyList());
    }

    public static boolean timesCardSinglePriceSwitch() {
        return Lion.getBoolean(APP_KEY, TIMES_CARD_SINGLE_PRICE, false);
    }

    public static DinnerDealGrayConfig getDinnerDealGrayConfig() {
        return Lion.getBean(APP_KEY, DINNER_DEAL_GRAY_CONFIG, DinnerDealGrayConfig.class, new DinnerDealGrayConfig());
    }

    public static DealStyleConfig getDealStyleConfig() {
        return Lion.getBean(APP_KEY,
                LionConstants.LOG_CONFIG, DealStyleConfig.class);
    }

    public static String getSearchNavBarFixText() {
        return Lion.getString(APP_KEY, SEARCH_NAV_BAR_FIX_TEXT, "搜索商家、品类或商圈");
    }


    /**
     * 微信小程序openid登录态 开关
     * @return true/false
     */
    public static boolean getWxOpenidSwitch() {
        return Lion.getBoolean(APP_KEY, LionConstants.WX_OPENID_SWITCH, false);
    }

    public static boolean isHideStylePicturesBar(int categoryId) {
        List<Integer> categoryIds = Lion.getList(APP_KEY, HIDE_STYLE_PICTURES_BAR_CATEGORY_IDS, Integer.class, Collections.emptyList());
        return categoryIds.contains(categoryId);
    }

    public static Map<String, String> getDealDetailModuleKeyMapConfig() {
        return Lion.getMap(APP_KEY, DEAL_DETAIL_MODULE_KEY_CONFIG, String.class, Collections.emptyMap());
    }

    /**
     * 跑路赔icon
     * @return
     */
    public static Map<String,String> getCompensationForRunningAwayConfig() {
        return Lion.getMap("com.sankuai.dzshoppingguide.detail.commonmodule", LionConstants.COMPENSATION_FOR_RUNNING_AWAY_CONFIG, String.class);
    }
    public static String getCompensationForRunningAwayIcon() {
        Map<String, String> config = getCompensationForRunningAwayConfig();
        return MapUtils.getString(config, "icon", "");
    }

    /**
     * 跑路赔展示文案
     * @return
     */
    public static String getCompensationForRunningAwayText() {
        Map<String, String> config = getCompensationForRunningAwayConfig();
        return MapUtils.getString(config, "text", "按次核销，剩余可退");
    }

    /**
     * 获取跑路赔落地页
     * @param
     * @return
     */
    public static String getCompensationForRunningAwayLayerTitle() {
        Map<String, String> config = getCompensationForRunningAwayConfig();
        return MapUtils.getString(config, "layerTitle", "按次核销，剩余可退");
    }
    public static String getCompensationForRunningAwayLayerDesc() {
        Map<String, String> config = getCompensationForRunningAwayConfig();
        return MapUtils.getString(config, "layerDesc", "");
    }

    /**
     * 判断是否命中 商户后台二级类目
     * @param categoryId
     * @return
     */
    public static boolean hitCompensationForRunningAwayCategory(int categoryId) {
        Map<String, String> config = getCompensationForRunningAwayConfig();
        String categoryIds = MapUtils.getString(config, "allowCategoryIds", "");
        List<String> allowCategoryIds = Lists.newArrayList(categoryIds.split(","));
        return allowCategoryIds.contains(String.valueOf(categoryId))
                || allowCategoryIds.contains("allowAll");
    }

    public static DisclaimerConfig getShowDisclaimersConfig() {
        return Lion.getBean(APP_KEY, SHOW_DISCLAIMER_CONFIG, DisclaimerConfig.class, new DisclaimerConfig());
    }

    public static Map<String, String> getDealStyleKeyForCpv() {
        return Lion.getMap(APP_KEY, DEAL_STYLE_KEY_FOR_CPV, String.class, Collections.emptyMap());
    }

    public static boolean hitLimitNoteIgnoreThirdCategory(Long categoryId) {
        List<Long> categoryIds = Lion.getList(APP_KEY, LIMIT_INFO_IGNORE_THIRD_CATEGORY_CONFIG, Long.class, Collections.emptyList());
        return categoryIds.contains(categoryId);
    }

    // 查看3C认证的配置信息
    public static List<Long> get3cServiceTypeList(){
        return Lion.getList(APP_KEY, SERVICE_TYPE_LIST_3C, Long.class, Collections.emptyList());
    }

    // 查看3C认证的图标
    public static String get3cIcon(){
        return Lion.getString(APP_KEY, ICON_3C, "https://p0.meituan.net/dealproduct/c12a8dcbd4e7a5415c7f2e88651665122037.png");
    }

    public static boolean isCleaningSelfPreOrderSwitch() {
        return Lion.getBoolean(APP_KEY, CLEANING_SELF_PRE_ORDER_SWITCH, false);
    }

    public static List<String> getCleaningSelfPreOrderWhiteList() {
        return Lion.getList(APP_KEY, CLEANING_SELF_PRE_ORDER_WHITE_LIST, String.class, new ArrayList<>());
    }

    public static Map<String, Map> getTimesCardStyleConfig() {
        return Lion.getMap(APP_KEY, TIMES_CARD_PRICE_STYLE_CONFIG, Map.class, Maps.newHashMap());
    }

    public static boolean notUseExpressOptimize(DealCtx ctx, Map<String, Map> categoryIdConfigs){
        int categoryId = ctx.getCategoryId();
        String serviceType = "";
        Long serviceTypeId = 0L;
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        if (Objects.nonNull(dealGroupDTO) && Objects.nonNull(dealGroupDTO.getCategory())) {
            serviceType = dealGroupDTO.getCategory().getServiceType();
            serviceTypeId = dealGroupDTO.getCategory().getServiceTypeId();
        }
        Map<String, List<String>> configs = categoryIdConfigs.get(String.valueOf(categoryId));
        List<String> serviceTypes = MapUtils.getObject(configs, "serviceTypes");
        List<String> serviceTypeIds = MapUtils.getObject(configs, "serviceTypeIds");
        // 针对二级类目生效
        boolean effectSecCategory = MapUtils.getBoolean(configs, "effectSecCategory", false);

        return (CollectionUtils.isNotEmpty(serviceTypeIds) && (serviceTypeIds.contains(String.valueOf(serviceTypeId)))
                || (CollectionUtils.isNotEmpty(serviceTypes) && serviceTypes.contains(serviceType)))
                || effectSecCategory;
    }

    /**
     * 多买多省trafficFlag白名单
     * @return
     */
    public static List<String> getBuyMoreTrafficFlagWhiteList() {
        return Lion.getList(APP_KEY, BUY_MORE_TRAFFIC_FLAG_WHITE_LIST, String.class, new ArrayList<>());
    }

    /**
     * 团详迁移打点配置
     */
    public static String getMigrationConfig() {
        return Lion.getString(APP_KEY, DETAIL_MIGRATION_BATCH_CATEGORY_LIST, StringUtils.EMPTY);
    }

    public static boolean judgeUnavailableDateConfig(int categoryId) {
        List<Integer> categoryIds = Lion.getList(APP_KEY, JUDGE_UNAVAILABLE_DATE_CATEGORY_IDS, Integer.class,
                Collections.emptyList());
        return categoryIds.contains(categoryId);
    }

    /**
     * 获取放心种头图配置
     * @return
     */
    public static Map<String, List> getImplantBrandHeadPic() {
       return Lion.getMap("com.sankuai.dzshoppingguide.detail.commonmodule", "implant.brand.head.pic.config", List.class, Collections.emptyMap());
    }

    public static InsuranceConfigDTO getInsuranceInfoDetailConfig() {
        try {

            String json = Lion.getString("com.sankuai.dzshoppingguide.detail.commonmodule", LionConstants.INSURANCE_INFO_DETAIL_CONFIG, "");
            if (StringUtils.isEmpty(json)) {
                return null;
            }
            return com.dianping.lion.common.util.JsonUtils.fromJson(json, InsuranceConfigDTO.class);
        } catch (Exception e) {
            log.error("[LionConfigUtils] getPerformanceGuaranteeSwitch error", e);
        }
        return null;
    }

    public static NewDealSaleTagSwitch getNewDealSaleTagSwitch() {
        return Lion.getBean(APP_KEY, NEW_DEAL_SALE_TAG_SWITCH, NewDealSaleTagSwitch.class, new NewDealSaleTagSwitch());
    }
}
