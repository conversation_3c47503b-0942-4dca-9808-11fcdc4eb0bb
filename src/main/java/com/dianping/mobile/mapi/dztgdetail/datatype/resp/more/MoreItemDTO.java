package com.dianping.mobile.mapi.dztgdetail.datatype.resp.more;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/12/13.
 */
@TypeDoc(description = "更多模块基础数据")
@MobileDo(id = 0x44db)
@Data
public class MoreItemDTO implements Serializable {

    @FieldDoc(description = "数据ID")
    @MobileField(key = 0xb231)
    private int itemId;

    @FieldDoc(description = "标题")
    @MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "基础数据短标题")
    @MobileField(key = 0x478b)
    private String shortTitle;

    @FieldDoc(description = "正则标题")
    @MobileField(key = 0x1970)
    private String productTitle;

    @FieldDoc(description = "现价")
    @MobileField(key = 0xd1e9)
    private double currentPrice;

    @FieldDoc(description = "原价")
    @MobileField(key = 0x6242)
    private double marketPrice;

    @FieldDoc(description = "销量展示文案")
    @MobileField(key = 0xaf42)
    private String salesDesc;

    @FieldDoc(description = "团单标题前缀")
    @MobileField(key = 0x4e7c)
    private String itemPrefix;

    @FieldDoc(description = "默认图片")
    @MobileField(key = 0x7fa)
    private String defaultPic;

    @FieldDoc(description = "大图")
    @MobileField(key = 0x3158)
    private String bigPic;

    @FieldDoc(description = "跳转链接")
    @MobileField(key = 0x8d3b)
    private String detailUrl;

    @FieldDoc(description = "优惠文案标签")
    @MobileField(key = 0xc27)
    private String itemCampaignTag;
}
