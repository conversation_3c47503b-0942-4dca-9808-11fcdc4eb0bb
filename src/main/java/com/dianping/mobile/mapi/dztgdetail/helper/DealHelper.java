package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.deal.stock.dto.ProductGroupStock;
import com.dianping.json.facade.JsonFacade;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import org.apache.commons.lang.StringUtils;

public class DealHelper {

    public static boolean isSoldOut(int clientType, ProductGroupStock dealGroupStock){
        if(dealGroupStock == null){
            return false; //查询失败的时候，认为没有卖完，避免影响正常单子线上售卖
        }
        return ClientTypeEnum.isMtPlatform(clientType) ? dealGroupStock.isMtSoldOut() : dealGroupStock.isDpSoldOut();
    }

    public static SelectDealConfig getSelectDealConfig () {
        String configStr = Lion.get(LionConstants.SELECTED_DEALGROUPIDS);
        if (StringUtils.isBlank(configStr)) {
            return null;
        }
        SelectDealConfig config = JsonFacade.deserialize(configStr, SelectDealConfig.class);

        if (config != null) {
            if (config.getDpDealGroupIds() != null || config.getMtDealGroupIds() != null) {
                return config;
            }
        }
        return null;

    }

    public static boolean getChoicestIconSwitch() {
        return Lion.getBooleanValue(LionConstants.CHOICEST_ICON_DISPLAY_SWITCH, false);
    }
}
