package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.CatEvents;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetDealTinyInfoRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealLowPriceItemEntranceVO;
import com.dianping.mobile.mapi.dztgdetail.facade.DealTinyInfoFacade;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-10-10
 * @desc 是否展示同款低价入口
 */
@InterfaceDoc(
        displayName = "是否展示同款低价入口", type = "restful", description = "团详页是否展示同款低价商品入口",
        scenarios = "该接口适用于双平台App站点中团详页中是否展示同款低价入口", host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "liuwen17"
)
@Controller("general/platform/dztgdetail/isshowdealgrouplowpriceitemlist.bin")
@Action(url = "isshowdealgrouplowpriceitemlist.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class DealLowPriceItemEntranceAction extends AbsAction<GetDealTinyInfoRequest> {
    @Resource
    private DealTinyInfoFacade dealTinyInfoFacade;

    @Override
    protected IMobileResponse validate(GetDealTinyInfoRequest request, IMobileContext context) {
        if (Objects.isNull(request) || Integer.parseInt(request.getDealGroupId()) < 0) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @Override
    @CryptMethod
    protected IMobileResponse execute(GetDealTinyInfoRequest request, IMobileContext context) {
        try {
            EnvCtx envCtx = initEnvCtxV2(context);
            DealLowPriceItemEntranceVO result = dealTinyInfoFacade.isShowLowPriceItemEntrance(request, envCtx);
            if (Objects.nonNull(result)) {
                Cat.logMetricForCount(CatEvents.IS_SHOW_DEAL_LOW_PRICE_ENTRANCE_SUC);
                return new CommonMobileResponse(result);
            }
            Cat.logMetricForCount(CatEvents.IS_SHOW_DEAL_LOW_PRICE_ENTRANCE_NO_DATA);
            return new CommonMobileResponse(Resps.NoDataResp);
        } catch (Exception e) {
            log.error("isshowdealgrouplowpriceitemlist.bin error!", e);
        }
        Cat.logMetricForCount(CatEvents.IS_SHOW_DEAL_LOW_PRICE_ENTRANCE_FAIL);
        return Resps.SYSTEM_ERROR;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
