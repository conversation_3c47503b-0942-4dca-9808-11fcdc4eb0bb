package com.dianping.mobile.mapi.dztgdetail.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class JsonCompareUtils {
        /**
         * 将Map类型转换为JSONObject
         */
        public static Object convertToJsonObject(Object obj) {
            if (obj instanceof Map && !(obj instanceof JSONObject)) {
                return new JSONObject((Map) obj);
            }
            return obj;
        }

        /**
         * 比较两个值是否相等
         */
        public static boolean compareValues(Object value1, Object value2) {
            // 处理 null 值
            if (value1 == null && value2 == null) {
                return true;
            }
            if (value1 == null || value2 == null) {
                return false;
            }

            // 转换类型
            value1 = convertToJsonObject(value1);
            value2 = convertToJsonObject(value2);

            // 比较逻辑
            if (value1 instanceof JSONObject && value2 instanceof JSONObject) {
                return compareJsonObject((JSONObject) value1, (JSONObject) value2);
            }
            if (value1 instanceof JSONArray && value2 instanceof JSONArray) {
                return compareJsonArray((JSONArray) value1, (JSONArray) value2);
            }
            if (value1 instanceof Number && value2 instanceof Number) {
                return ((Number) value1).intValue() == ((Number) value2).intValue();
            }
            if (value1 instanceof BigDecimal && value2 instanceof BigDecimal) {
                return ((BigDecimal) value1).compareTo((BigDecimal) value2) == 0;
            }
            return Objects.equals(value1, value2);
        }

        /**
         * 比较两个JSON数组是否相等
         */
        public static boolean compareJsonArray(JSONArray array1, JSONArray array2) {
            if (array1 == null && array2 == null) {
                return true;
            }
            if (array1 == null || array2 == null || array1.size() != array2.size()) {
                return false;
            }

            for (int i = 0; i < array1.size(); i++) {
                if (!compareValues(array1.get(i), array2.get(i))) {
                    return false;
                }
            }
            return true;
        }

        /**
         * 比较两个JSON对象是否相等
         */
        public static boolean compareJsonObject(JSONObject obj1, JSONObject obj2) {
            List<String> keys1 = new ArrayList<>(obj1.keySet());
            List<String> keys2 = new ArrayList<>(obj2.keySet());

            if (keys1.size() != keys2.size()) {
                return false;
            }

            for (int i = 0; i < keys1.size(); i++) {
                String key1 = keys1.get(i);
                String key2 = keys2.get(i);
                if (!compareValues(obj1.get(key1), obj2.get(key2))) {
                    return false;
                }
            }
            return true;
        }
    }