package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.annotation.MobileRequest.Param;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.DecryptedField;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/12/13.
 */
@Data
@TypeDoc(description = "团单促销模块请求参数")
@MobileRequest
public class UnifiedActivityModuleReq implements IMobileRequest, Serializable {

    @Deprecated
    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，原int类型")
    @Param(name = "dealgroupid")
    private Integer dealGroupId;

    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，string类型")
    @Param(name = "stringdealGroupId")
    private String stringDealGroupId;

    @FieldDoc(description = "团单城市ID", rule = "美团平台为美团城市ID，点评平台为点评城市ID")
    @Param(name = "cityid")
    private Integer cityId;

    @Deprecated
    @FieldDoc(description = "商户ID", rule = "商户ID")
    @Param(name = "shopid")
    private Integer shopId;
    @Param(name = "shopIdEncrypt")
    @DecryptedField(targetFieldName = "shopId")
    private String shopIdEncrypt;

    @FieldDoc(description = "客户端平台ID")
    @Param(name = "clienttype")
    private Integer clientType;

    @FieldDoc(description = "商户ID", rule = "商户ID")
    @Param(name = "shopidstr")
    private String shopidstr;
    @Param(name = "shopidstrEncrypt")
    @DecryptedField(targetFieldName = "shopidstr")
    private String shopidstrEncrypt;

    @FieldDoc(description = "请求来源")
    @Param(name = "pagesource")
    private String pageSource;

    public Integer getDealGroupId() {
        return dealGroupId != null ? dealGroupId : 0;
    }

    public void setDealGroupId(Integer dealGroupId) {
        this.dealGroupId = dealGroupId;
    }

    public Integer getCityId() {
        return cityId != null ? cityId : 0;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getClientType() {
        return clientType != null ? clientType : 0;
    }

    public void setClientType(Integer clientType) {
        this.clientType = clientType;
    }

    @Deprecated
    public Integer getShopId() {
        return shopId != null ? shopId : 0;
    }

    @Deprecated
    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public String getShopidstr() {
        return shopidstr;
    }

    public void setShopidstr(String shopidstr) {
        this.shopidstr = shopidstr;
    }

    public String getPageSource() {
        return pageSource;
    }

    public void setPageSource(String pageSource) {
        this.pageSource = pageSource;
    }

    public String getShopIdEncrypt() {
        return shopIdEncrypt;
    }

    public void setShopIdEncrypt(String shopIdEncrypt) {
        this.shopIdEncrypt = shopIdEncrypt;
    }

    public String getShopidstrEncrypt() {
        return shopidstrEncrypt;
    }

    public void setShopidstrEncrypt(String shopidstrEncrypt) {
        this.shopidstrEncrypt = shopidstrEncrypt;
    }

    public long getShopIdLong() {
        if(StringUtils.isNumeric(shopidstr)) {
            return Long.parseLong(shopidstr);
        } else if(shopId != null) {
            return shopId.longValue();
        } else {
            return 0L;
        }
    }
}
