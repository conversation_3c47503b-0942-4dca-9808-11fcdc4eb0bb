package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/7 10:44
 */
@Data
@TypeDoc(description = "到综团单适用商户数据模型")
@MobileDo(id = 0xf446)
public class SubTitleVO implements Serializable {
    @FieldDoc(description = "模块包含的组件")
    @MobileDo.MobileField(key = 0xae00)
    private List<TitleItemVO> itemDTOs;

    @FieldDoc(description = "模块标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "跳转链接")
    @MobileDo.MobileField(key = 0x774e)
    private String jumpUrl;
}
