package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.entity.PageSourceGrayConfig;
import org.apache.commons.collections4.CollectionUtils;

/**
 * @Author: <EMAIL>
 * @Date: 2023/12/11
 */
public class PageSourceGrayUtils {
    public static boolean hit(int dealId, int categoryId) {
        PageSourceGrayConfig grayConfig = Lion.getBean(LionConstants.APP_KEY, LionConstants.PAGE_SOURCE_METRIC_WHITE_LIST, PageSourceGrayConfig.class);
        if (grayConfig == null || grayConfig.getGrayLevel() == 0) {
            return false;
        }
        if (grayConfig.getGrayLevel() == 2) {
            return true;
        }
        if (CollectionUtils.isEmpty(grayConfig.getWhiteDealIds()) && CollectionUtils.isEmpty(grayConfig.getWhiteCategoryIds())) {
            return false;
        }
        return grayConfig.getWhiteDealIds().contains(dealId) || grayConfig.getWhiteCategoryIds().contains(categoryId);
    }
}
