package com.dianping.mobile.mapi.dztgdetail.datatype.resp.shop;

import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: zhangyuan103
 * @Date: 2024/8/13
 */
@Data
@TypeDoc(description = "适用门店")
public class DealGroupShopVO implements Serializable {
    /**
     * 页面提示文案
     */
    @MobileField
    private String tips;

    /**
     * 页面名称
     */
    @MobileField
    private String title;

    /**
     * 门店总数
     */
    @MobileField
    private int totalCount;

    /**
     * 门店列表信息
     */
    @MobileField
    private List<DealGroupShop> list;

    /**
     * 版本，用于后端下发页面版本控制信息
     */
    @MobileField
    private Integer version;

    /**
     * 团购二级类目
     */
    @MobileField
    private int dealCategoryId;
}