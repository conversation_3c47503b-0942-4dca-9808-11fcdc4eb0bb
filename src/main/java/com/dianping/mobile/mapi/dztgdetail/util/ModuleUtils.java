package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: wuwenqiang
 * @create: 2024-11-07
 * @description: 模块工具类
 */
public class ModuleUtils {

    /**
     * 根据指定模块key，获取模块配置
     * 支持moduleConfigMaps的key为多个类目
     * @param moduleConfigMaps 模块配置map
     * @param moduleConfigKey 模块key
     * @return
     */
    public static List<ModuleConfigDo> getModuleConfigsSupportMultiCat(Map<String, List<ModuleConfigDo>> moduleConfigMaps, String moduleConfigKey) {
        if (MapUtils.isEmpty(moduleConfigMaps)) {
            return Lists.newArrayList();
        }
        for (Map.Entry<String, List<ModuleConfigDo>> entry : moduleConfigMaps.entrySet()) {
            if (StringUtils.isEmpty(entry.getKey())) {
                continue;
            }
            Set<String> categories = Sets.newHashSet(entry.getKey().split(","));
            if (categories.contains(moduleConfigKey)) {
                return entry.getValue();
            }
        }
        return Lists.newArrayList();
    }

    public static String getDetailStyle(ModuleConfigsModule moduleConfigsModule, String key) {
        if (moduleConfigsModule == null || CollectionUtils.isEmpty(moduleConfigsModule.getModuleConfigs())) {
            return null;
        }
        List<ModuleConfigDo> moduleConfigs = moduleConfigsModule.getModuleConfigs();
        Map<String, String> map = moduleConfigs.stream().collect(Collectors.toMap(e->e.getKey(), e->e.getValue()));
        return map.get(key);
    }
}
