package com.dianping.mobile.mapi.dztgdetail.entity.shop;

import lombok.Data;

/**
 * 商品搜索传参-订单优惠模型
 */
@Data
public class CommonPromotion {
    private String promotionId;
    private String promotionGroupId;
    //优惠类型名称：参考com.dianping.general.unified.search.api.productshopsearch.enums.PromotionTpEnum,传code
    private Integer promotionType;
    //opt券的优惠信息传这个字段：verifyType为1时才会去查opt券
    private Integer verifyType;
    //金融券的优惠信息传这个字段：金融券是否属于政府券
    private Boolean checkGovernmentVoucher;
}
