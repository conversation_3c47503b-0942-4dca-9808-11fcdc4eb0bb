package com.dianping.mobile.mapi.dztgdetail.availabletime.impl;

import com.dianping.mobile.mapi.dztgdetail.availabletime.AvailableTimeStrategy;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.common.enums.AvailableTimeStrategyEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.sankuai.general.product.query.center.client.dto.rule.use.CycleAvailableDateDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DateRangeDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

import static com.dianping.mobile.mapi.dztgdetail.helper.AvailableTimeHelper.ALL_DAY;
import static com.dianping.mobile.mapi.dztgdetail.helper.AvailableTimeHelper.PARTIAL_TIME;

@Service
public class DefaultAvailableTimeStrategy implements AvailableTimeStrategy {
    @Override
    public String getAvailableTime(DealCtx dealCtx) {
        if (Objects.isNull(dealCtx.getDealGroupDTO()) || Objects.isNull(dealCtx.getDealGroupDTO().getRule())
                || Objects.isNull(dealCtx.getDealGroupDTO().getRule().getUseRule())) {
            return StringUtils.EMPTY;
        }
        List<String> timesAvailableAll = DealAttrHelper.getAttributeValues(dealCtx.getAttrs(),
                DealAttrKeys.TIMES_AVAILABLE_ALL);
        if (LionConfigUtils.hitCustomAvailableTimeOfDays(dealCtx.getCategoryId())
                && CollectionUtils.isNotEmpty(timesAvailableAll)) {
            // 针对茶馆行业
            if (timesAvailableAll.contains("否")) {
                return PARTIAL_TIME;
            }
        }
        DealGroupUseRuleDTO useRuleDTO = dealCtx.getDealGroupDTO().getRule().getUseRule();
        if (Objects.isNull(useRuleDTO.getAvailableDate())
                || CollectionUtils.isEmpty(useRuleDTO.getAvailableDate().getCycleAvailableDateList())) {
            return StringUtils.EMPTY;
        }

        CycleAvailableDateDTO cycleAvailableDateDTO = useRuleDTO.getAvailableDate().getCycleAvailableDateList().get(0);
        if (CollectionUtils.isEmpty(cycleAvailableDateDTO.getAvailableTimeRangePerDay())) {
            return StringUtils.EMPTY;
        }

        DateRangeDTO dateRangeDTO = cycleAvailableDateDTO.getAvailableTimeRangePerDay().get(0);
        if (Objects.isNull(dateRangeDTO) || StringUtils.isBlank(dateRangeDTO.getFrom())
                || StringUtils.isBlank(dateRangeDTO.getTo())) {
            return StringUtils.EMPTY;
        }

        if (dateRangeDTO.getFrom().length() > 11 && dateRangeDTO.getTo().length() > 11) {
            if ("00:00:00".equals(dateRangeDTO.getFrom().substring(11))
                    && "23:59:00".equals(dateRangeDTO.getTo().substring(11))) {
                return ALL_DAY;
            } else {
                return PARTIAL_TIME;
            }
        }
        return StringUtils.EMPTY;
    }

    @Override
    public AvailableTimeStrategyEnum getStrategyType() {
        return AvailableTimeStrategyEnum.DEFAULT_STRATEGY;
    }
}