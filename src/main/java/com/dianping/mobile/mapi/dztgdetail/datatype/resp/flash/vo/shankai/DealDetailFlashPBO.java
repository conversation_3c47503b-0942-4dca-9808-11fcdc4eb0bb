package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.shankai;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealStyleBO;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/10/17 17:47
 */
@Data
@MobileDo(id = 0x7fd2)
public class DealDetailFlashPBO implements Serializable {

    @FieldDoc(description = "闪开方案控制开关")
    @MobileDo.MobileField(key = 0xd21b)
    private DealLayoutConfigVO dealLayoutConfig;

    @FieldDoc(description = "骨架屏布局信息")
    @MobileDo.MobileField(key = 0x7c01)
    private List<DealPageLayoutComponentVO> dealLayoutComponents;

    @FieldDoc(description = "团购详情样式信息")
    @MobileDo.MobileField(key = 0xba47)
    private DealStyleBO dealStyle;

    @FieldDoc(description = "团购详情数据信息")
    @MobileDo.MobileField(key = 0xed2c)
    private FlashDealDetailVO dealDetail;

}
