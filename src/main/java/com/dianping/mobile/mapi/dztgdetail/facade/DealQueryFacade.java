package com.dianping.mobile.mapi.dztgdetail.facade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.dianping.deal.publishcategory.DealGroupPublishCategoryQueryService;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.deal.shop.DealShopQueryService;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.DecryptBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.ProcessHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DzImWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ProductTagWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.SkuWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.ShopUuidUtils;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Cons;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealIMReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedDealsReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.Response;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupImVo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.MLiveInfoVo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedDealInfo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedDeals;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.CePinTuanPassParamConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.DecryptVO;
import com.dianping.mobile.mapi.dztgdetail.entity.CardStyleConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.CostEffectivePinTuan;
import com.dianping.mobile.mapi.dztgdetail.entity.ExpResultConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.HideMarketPriceConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.LiveFloatingWindowConfig;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.deal.executor.ResultPostProcessHandler;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.utils.FaultToleranceUtils;
import com.dianping.mobile.mapi.dztgdetail.flowdye.FlowDyeExtImpl;
import com.dianping.mobile.mapi.dztgdetail.helper.AppCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import com.dianping.mobile.mapi.dztgdetail.helper.SwitchHelper;
import com.dianping.mobile.mapi.dztgdetail.tab.RelateDeals;
import com.dianping.mobile.mapi.dztgdetail.tab.RelateDealsFactory;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.BaseData;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.DealTab;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.DealTabHolder;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.VersionUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import com.sankuai.nibscp.common.flow.identify.anno.FlowDyeAnnotation;
import jodd.util.URLDecoder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Component
public class  DealQueryFacade {

    @Autowired
    @Qualifier("dealBaseQueryHandler")
    private ProcessHandler<com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx> dealBaseQueryHandler;

    @Autowired
    @Qualifier("dealOtherQueryHandler")
    private ProcessHandler<com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx> dealOtherQueryHandler;

    @Autowired
    @Qualifier("dealBuilderHandler")
    private ProcessHandler<com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx> dealBuilderHandler;

    @Autowired
    private DealShopQueryService dealShopQueryService;

    @Autowired
    private DealGroupWrapper dealGroupWrapper;

    @Autowired
    MapperWrapper mapperWrapper;

    @Resource
    DealGroupPublishCategoryQueryService dealGroupPublishCategoryQueryService;

    @Autowired
    RelateDealsFactory relateDealsFactory;

    @Autowired
    private DouHuBiz douHuBiz;
    @Resource
    private DouHuService douHuService;

    @Autowired
    private DzImWrapper dzImWrapper;
    @Resource
    private ProductTagWrapper productTagWrapper;
    @Resource
    private DecryptBiz decryptBiz;

    @Resource
    private SkuWrapper skuWrapper;

    private static final Logger LOGGER = LoggerFactory.getLogger(DealQueryFacade.class);

    private static final String POI_LOG_TYPE = "DealPoiLog";

    private static final Map<DztgClientTypeEnum, Boolean> MEITUAN_CLIENT_LIST = new HashMap<DztgClientTypeEnum, Boolean>() {
        {
            put(DztgClientTypeEnum.MEITUAN_APP, true);
            put(DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP, true);
        }
    };

    @FlowDyeAnnotation(extClass = FlowDyeExtImpl.class) //特别注意：方法入参改动时（包括参数的类型、变量名、包含的字段名等）需要兼容FlowDyeExtImpl，FlowDyeExtImpl用反射做了一些逻辑！！！
    public Response<DealGroupPBO> queryDealGroup(DealBaseReq req, EnvCtx envCtx) {
        DealCtx dealCtx = initDealsBaseData(req, envCtx);

        //负责团单基础信息或者一些后续接口依赖的前置数据聚合
        dealBaseQueryHandler.preThenProc(dealCtx);

        //触发Rhino限流
        if(dealCtx.getDealTechCtx() != null && dealCtx.getDealTechCtx().isRhinoReject()){
            return Response.createRhinoRejectResponse("rhino限流", dealCtx.getResult());
        }

        processMarketPriceDisplayStatus(req, dealCtx);

        //非前置依赖的数据聚合
        dealOtherQueryHandler.preThenProc(dealCtx);

        //团单结果组装
        dealBuilderHandler.preThenProc(dealCtx);

        //设置合规
        setHeguiNotice(dealCtx);

        // 填充直播浮窗开关
        fillBusinessFigure(dealCtx);

        // 处理标准团单
        processStandardDealGroup(dealCtx);

        // 团单返回值后置处理
        postProcessResult(dealCtx);

        return Response.createSuccessResponse(dealCtx.getResult());
    }

    public void fillBusinessFigure(DealCtx dealCtx) {
        if (Objects.isNull(dealCtx) || Objects.isNull(dealCtx.getChannelDTO()) || Objects.isNull(dealCtx.getResult())) {
            return;
        }
        DealGroupPBO result = dealCtx.getResult();
        result.setBusinessFigure("");

        DealGroupChannelDTO channelDTO = dealCtx.getChannelDTO();
        // 二级分类
        int categoryId = channelDTO.getCategoryId();
        // 一级分类
        int channelId = Objects.isNull(channelDTO.getChannelDTO()) ? 0 : channelDTO.getChannelDTO().getChannelId();

        LiveFloatingWindowConfig liveFloatingWindowConfig = Lion.getBean("com.sankuai.dzu.tpbase.dztgdetailweb.livefloatingwindow.config", LiveFloatingWindowConfig.class);
        if (liveFloatingWindowConfig == null) {
            return;
        }
        // 先看有没有配二级分类
        if (liveFloatingWindowConfig.getCategoryIdConfigMap() != null
                && liveFloatingWindowConfig.getCategoryIdConfigMap().get(String.valueOf(categoryId)) != null) {
            result.setBusinessFigure(liveFloatingWindowConfig.getCategoryIdConfigMap().get(String.valueOf(categoryId)));
            return;
        }
        // 再看有没有配一级分类
        if (liveFloatingWindowConfig.getChannelIdConfigMap() != null
                && liveFloatingWindowConfig.getChannelIdConfigMap().get(String.valueOf(channelId)) != null) {
            result.setBusinessFigure(liveFloatingWindowConfig.getChannelIdConfigMap().get(String.valueOf(channelId)));
        }
    }

    public void setHeguiNotice(DealCtx dealCtx) {
        try{
            long dpShopId = dealCtx.getDpLongShopId();
            List<Long> grayShopids = Lion.getList(LionConstants.HEGUI_NOTICE_SHOPID_WHITELIST, Long.class, new ArrayList<>());

            if (!grayShopids.isEmpty() && grayShopids.contains(dpShopId)) {
                String heguiNotice = buildHeguiNotice(dealCtx);
                dealCtx.getResult().setHeguiNotice(heguiNotice);
            }
        } catch(Exception e) {
            LOGGER.error("setHeguiNotice error!",e);
        }
    }

    private String buildHeguiNotice(DealCtx dealCtx) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.facade.DealQueryFacade.buildHeguiNotice(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        String heguiNotice = "本服务套餐由第三方代理公司提供";
        try {
            String customerName = getCustomerName(dealCtx.getCustomerId());
            if(StringUtils.isNotBlank(customerName)) {
                heguiNotice = "本服务套餐由" + customerName + "（第三方代理公司）提供";
            }
        } catch (Exception e) {
            LOGGER.error("buildHeguiNotice error!",e);
        }
        return heguiNotice;
    }

    private String getCustomerName(long customerId) {
        Map<String, String> customerIdToNameMap = Lion.getMap(LionConstants.HEGUI_NOTICE_CUSTOMER_NAME_MAP, String.class, new HashMap<>());
        return customerIdToNameMap.getOrDefault(Long.toString(customerId), "");
    }

    public void processMarketPriceDisplayStatus(DealBaseReq req, DealCtx dealCtx) {
        if (req == null || dealCtx == null) {
            return;
        }
        if(GreyUtils.enableQueryCenterForMainApi(dealCtx)) {
            int categoryId = getPublishCategoryId(dealCtx.getDealGroupDTO());
            List<Long> tagIds = getDealGroupTagIds(dealCtx.getDealGroupDTO());
            DealGroupBaseDTO dealGroupBase = dealCtx.getDealGroupBase();
            boolean hideMarketPrice = isEnableHideMarketPrice(categoryId, dealGroupBase, dealCtx, tagIds);
            dealCtx.setMarketPriceHided(hideMarketPrice);
            dealCtx.setMarketPriceNormalButtonHide(isHideMarketPrice(categoryId, dealGroupBase));
        } else {
            int dpId = dealCtx.isMt() ? dealGroupWrapper.getDpDealGroupId(req.getDealgroupid()) : req.getDealgroupid();
            Future future = productTagWrapper.preQueryDealGroupTag((long) dpId);
            //团单后台分类
            int categoryId = dealGroupWrapper.getCategoryId(dpId);
            List<Long> tagIds = productTagWrapper.queryDealGroupTag((long) dpId, future);
            DealGroupBaseDTO dealGroupBase = dealCtx.getDealGroupBase();
            boolean hideMarketPrice = isEnableHideMarketPrice(categoryId, dealGroupBase, dealCtx, tagIds);
            dealCtx.setMarketPriceHided(hideMarketPrice);
            dealCtx.setMarketPriceNormalButtonHide(isHideMarketPrice(categoryId, dealGroupBase));
        }
    }

    private List<Long> getDealGroupTagIds(DealGroupDTO dealGroupDTO) {
        List<Long> tagIds = new ArrayList<>();
        if(dealGroupDTO == null || CollectionUtils.isEmpty(dealGroupDTO.getTags())) {
            return tagIds;
        }
        for(DealGroupTagDTO tagDTO : dealGroupDTO.getTags()) {
            tagIds.add(tagDTO.getId());
        }
        return tagIds;
    }

    private int getPublishCategoryId(DealGroupDTO dealGroupDTO) {
        if(dealGroupDTO == null || dealGroupDTO.getCategory() == null || dealGroupDTO.getCategory().getCategoryId() == null) {
            return 0;
        }
        return Math.toIntExact(dealGroupDTO.getCategory().getCategoryId());
    }

    /**
     * 隐藏市场价的逻辑判断
     * @Param dpCityId 点评城市Id
     * @Param dpShopId 点评门店Id
     * @Param poiSecondBackCategory POI后台二级分类
     * @Param serviceType 团单服务类型
     * @return 是否隐藏
     * */
    private boolean isEnableHideMarketPrice(int categoryId, DealGroupBaseDTO dealGroupBase, DealCtx dealCtx, List<Long> tagIds) {
        List<Integer> dealGroupCategoryList = Lion.getList(LionConstants.HIDE_MARKETPRICE_CATEGORY_LIST, Integer.TYPE, Collections.<Integer>emptyList());
        if (CollectionUtils.isNotEmpty(dealGroupCategoryList) && dealGroupCategoryList.contains(categoryId)) {
            if (dealGroupBase != null && dealGroupBase.getMarketPrice() != null && dealGroupBase.getDealGroupPrice() != null
                    && dealGroupBase.getMarketPrice().compareTo(dealGroupBase.getDealGroupPrice()) == 0) {
                return true;
            }
        }
        List<HideMarketPriceConfig> directHideConfigList = Lion.getList(LionConstants.DIRECT_HIDE_MARKETPRICE_CONFIG, HideMarketPriceConfig.class, Collections.emptyList());
        if (CollectionUtils.isNotEmpty(directHideConfigList)) {
            boolean isCategoryMatch;
            boolean isTagMatch;
            for (HideMarketPriceConfig hideMarketPriceConfig : directHideConfigList) {
                isCategoryMatch = hideMarketPriceConfig.getCategoryId() == null || hideMarketPriceConfig.getCategoryId() == categoryId;
                isTagMatch = hideMarketPriceConfig.getTagId() == null || (CollectionUtils.isNotEmpty(tagIds) && tagIds.contains(hideMarketPriceConfig.getTagId()));
                if (isCategoryMatch && isTagMatch) {
                    return true;
                }
            }
        }
        List<String> attributeValues = AttributeUtils.getAttributeValues("service_type", dealCtx.getAttrs());
        if(categoryId == 401 && CollectionUtils.isNotEmpty(attributeValues) && attributeValues.contains("核酸检测")) {
            return true;
        }
        return false;
    }

    /**
     * 团购价=市场价，MarketPriceNormalButtonBuilder重置底bar时，隐藏所有优惠信息的逻辑判断；
     * */
    private boolean isHideMarketPrice(int categoryId, DealGroupBaseDTO dealGroupBase) {
        List<Integer> dealGroupCategoryList = Lion.getList(LionConstants.NORMAL_HIDE_MARKETPRICE_CATEGORY_LIST, Integer.TYPE, Collections.<Integer>emptyList());
        if (CollectionUtils.isNotEmpty(dealGroupCategoryList) && dealGroupCategoryList.contains(categoryId)) {
            if (dealGroupBase != null && dealGroupBase.getMarketPrice() != null && dealGroupBase.getDealGroupPrice() != null
                    && dealGroupBase.getMarketPrice().compareTo(dealGroupBase.getDealGroupPrice()) == 0) {
                return true;
            }
        }
        return false;
    }

    public DealCtx initDealsBaseData(DealBaseReq req, EnvCtx envCtx) {
        DealCtx dealCtx = new DealCtx(envCtx);
        dealCtx.setResult(new DealGroupPBO());

        if (req.getEncryptedShopStr() != null && AppCtxHelper.isKuaiShouMiniProgram(envCtx)) {
            DecryptVO decrypt = decryptBiz.decrypt(req.getEncryptedShopStr(), envCtx.isMt());
            dealCtx.setMtId(req.getDealgroupid());
            dealCtx.setMtCityId(req.getCityid());
            if (decrypt.getDecryptedStr() != null && NumberUtils.isDigits(decrypt.getDecryptedStr())) {
                dealCtx.setMtLongShopId(Long.parseLong(decrypt.getDecryptedStr()));
            }
        } else if (envCtx.isMt()) {
            dealCtx.setMtId(req.getDealgroupid());
            dealCtx.setMtCityId(req.getCityid());
            dealCtx.setMtLongShopId(req.getLongPoiid());
            //打点判断入口处的shopid是否为空
            logPoiIdNull(envCtx.isMt(), dealCtx.getMtLongShopId());
        } else {
            long dpShopId = req.getLongPoiid();
            if(dpShopId == 0L) {
                long longDpShopId = ShopUuidUtils.getShopIdByUuid(req.getShopUuid());
                dpShopId = longDpShopId;
            }
            dealCtx.setDpId(req.getDealgroupid());
            dealCtx.setDpCityId(req.getCityid());
            dealCtx.setDpLongShopId(dpShopId);
            //打点判断入口处的shopid是否为空
            logPoiIdNull(envCtx.isMt(), dealCtx.getDpLongShopId());
        }
        dealCtx.setSkuId(req.getSkuId());
        dealCtx.setUserlng(req.getUserlng());
        dealCtx.setPricecipher(req.getPricecipher());
        dealCtx.setUserlat(req.getUserlat());
        dealCtx.setGpsCityId(req.getGpsCityId());
        dealCtx.setPosition(getPosition(envCtx, req));
        dealCtx.setGpsCoordinateType(req.getGpsCoordinateType());
        dealCtx.setExpResults(req.getExpResults());
        dealCtx.setRequestSource(req.getPageSource());
        dealCtx.setRequestExtParam(req.getExtParam());
        dealCtx.setConvertColor(Boolean.TRUE.equals(req.getConvertcolor()));
        dealCtx.setExternal(envCtx.isExternal());
        dealCtx.setReqChannel(req.getChannel());
        dealCtx.setLyyuserid(req.getLyyuserid());
        dealCtx.setMrnVersion(req.getMrnversion());
        dealCtx.setDealParam(req.getDealParam());
        dealCtx.setPassParam(req.getPass_param());
        dealCtx.setCx(req.getCx());
        dealCtx.setDealBaseReq(req);
        dealCtx.setMLiveId(getMliveId(req.getPass_param()));
        dealCtx.setMLiveInfoVo(new MLiveInfoVo());
        dealCtx.getMLiveInfoVo().setMLiveId(dealCtx.getMLiveId());
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        CePinTuanPassParamConfig pinTuanPassParamConfig = Lion.getBean(LionConstants.APP_KEY, LionConstants.COST_EFFECTIVE_PIN_TUAN_CONFIG, CePinTuanPassParamConfig.class);
        costEffectivePinTuan.setSceneType(req.getSceneType());
        costEffectivePinTuan.setPinTuanActivityId(req.getPintuanActivityId());
        costEffectivePinTuan.setShareToken(req.getOrderGroupId());
        costEffectivePinTuan.setPinTuanPassParamConfig(pinTuanPassParamConfig);
        dealCtx.setCostEffectivePinTuan(costEffectivePinTuan);
        dealCtx.setHitCostEffectivePinTuan(hitCostEffectivePinTuan(dealCtx));
        dealCtx.setInfoContentId(req.getInfoContentId());
        dealCtx.setMmcInflate(Objects.isNull(req.getMmcinflate()) ? 0 : req.getMmcinflate());
        dealCtx.setMmcBuy(Objects.isNull(req.getMmcbuy()) ? 0 : req.getMmcbuy());
        dealCtx.setMmcUse(Objects.isNull(req.getMmcuse()) ? 0 : req.getMmcuse());
        dealCtx.setMmcFree(Objects.isNull(req.getMmcfree()) ? 0 : req.getMmcfree());
        dealCtx.setMmcPkgVersion(req.getMmcPkgVersion());
        dealCtx.setWxVersion(StringUtils.isBlank(req.getWxVersion()) ? "" : req.getWxVersion());
        dealCtx.setOfflineCode(req.getOfflineCode());
        dealCtx.setSkuId(skuWrapper.getDefaultSkuId(req, envCtx));
        return dealCtx;
    }

    private boolean hitCostEffectivePinTuan(DealCtx dealCtx) {
        return Lion.getBoolean(LionConstants.APP_KEY, LionConstants.COST_EFFECTIVE_PIN_TUAN_ENABLE, false)
                && MEITUAN_CLIENT_LIST.containsKey(dealCtx.getEnvCtx().getDztgClientTypeEnum())
                && RequestSourceEnum.COST_EFFECTIVE.getSource().equals(dealCtx.getRequestSource());
    }

    public long getMliveId(String passParam){
        if (StringUtils.isBlank(passParam)){
            return 0L;
        }
        String decodedPassParam = URLDecoder.decode(passParam);
        String promoteChannelInfo = getStringFromJson(decodedPassParam, "PROMOTE_CHANNEL_INFO");
        if (StringUtils.isBlank(promoteChannelInfo)) {
            return 0L;
        }
        String promoteExtend = getStringFromJson(promoteChannelInfo, "promoteExtend");
        if (StringUtils.isBlank(promoteExtend)) {
            return 0L;
        }
        String mLiveId = getStringFromJson(promoteExtend, "mLiveId");
        try {
            return Long.valueOf(mLiveId);
        } catch (NumberFormatException e) {
            LOGGER.error("Failed to convert mLiveId to Long: {}", mLiveId, e);
            return 0L;
        }
    }

    private String getStringFromJson(String json, String key) {
        if (StringUtils.isBlank(json) || Objects.equals(json, "undefined")) {
            return StringUtils.EMPTY;
        }
        JSONObject jsonObject = JSON.parseObject(json);
        String value = jsonObject.getString(key);
        return StringUtils.isBlank(value) ? StringUtils.EMPTY : value;
    }

    private static void logPoiIdNull(boolean isMt, long shopId) {
        try {
            if(isMt) {
                Cat.logEvent(POI_LOG_TYPE,"mtAllPoiCounts");
                if(shopId <= 0) {
                    Cat.logEvent(POI_LOG_TYPE,"mtNoPoiCounts");
                }
            } else {
                Cat.logEvent(POI_LOG_TYPE,"dpAllPoiCounts");
                if(shopId <= 0) {
                    Cat.logEvent(POI_LOG_TYPE, "dpNoPoiCounts");
                }
            }
        } catch (Exception e) {
            Cat.logError(e);
        }
    }

    public RelatedDeals queryRelatedDeals(RelatedDealsReq request, EnvCtx envCtx) {

        if (!versionChk(envCtx)) {
            return null;
        }

        BaseData baseData = initRelatedDealsBaseData(request, !envCtx.isMt());

        RelateDeals relateDeals = relateDealsFactory.getRelateDeals(baseData.getPublishCategoryId());

        if (relateDeals == null) {
            return null;
        }

        DealTabHolder dealTabHolder = relateDeals.listRelatedDealTabs(baseData, envCtx);

        return buildRelatedDeals(dealTabHolder, baseData, request.getShopIdLong(), envCtx, request.getMrnversion());
    }

    private boolean versionChk(EnvCtx envCtx) {
        if (envCtx == null || StringUtils.isBlank(envCtx.getVersion())) {
            return false;
        }

        Map<String, String> configMap = Lion.getMap("com.sankuai.dzu.tpbase.dztgdetailweb.tab.version", String.class, new HashMap<>());

        if (MapUtils.isEmpty(configMap)) {
            return false;
        }

        String configKey = envCtx.isMt() ? "mt" : "dp";

        return VersionUtils.isGreatEqualThan(envCtx.getVersion(), configMap.get(configKey));
    }

    private List<Long> getOnlineDealGroupIds(long dpShopId, boolean isDp) {
        int limit = 50;
        int start = 0;
        List<Long> onlineDealGroupIds = new ArrayList<>();

        while (true) {
            List<Long> part = dealShopQueryService.querySaleDealGroupId(dpShopId, isDp ? 100 : 200, start, limit);

            if (CollectionUtils.isEmpty(part)) {
                break;
            }

            onlineDealGroupIds.addAll(part);

            if (part.size() < limit) {
                break;
            } else {
                start += 50;
            }
        }

        return onlineDealGroupIds;
    }

    private BaseData initRelatedDealsBaseData(RelatedDealsReq request, boolean isDp) {

        Integer currentDealGroupId = request.getDealGroupId();
        long dpShopId;

        if (isDp) {
            dpShopId = request.getShopIdLong();
        } else {
            Future future = mapperWrapper.preDpShopIdByMtShopId(request.getShopIdLong());
            dpShopId = mapperWrapper.getDpShopIdByMtShopIdLong(future);
        }

        List<Long> onlineDealGroupIds = getOnlineDealGroupIds(dpShopId, isDp);

        if (CollectionUtils.isEmpty(onlineDealGroupIds)) {
            throw new RuntimeException("dpShopId=" + dpShopId + " no online deal found");
        }

        List<Integer> olDpDealGroupIds = onlineDealGroupIds.stream().mapToInt(Long::intValue).boxed().collect(Collectors.toList());

        BaseData data = new BaseData();
        data.setDp(isDp);
        data.setShopId(Optional.ofNullable(request.getShopId()).orElse(0));
        data.setShopIdLong(request.getShopIdLong());
        data.setCityId(Optional.ofNullable(request.getCityId()).orElse(0));
        data.setDpDealGroupIds(olDpDealGroupIds);

        if (isDp) {
            data.setCurrentDpGroupId(currentDealGroupId);
            data.setPublishCategoryId(dealGroupPublishCategoryQueryService.getPublishCategory(currentDealGroupId));
            return data;
        }

        List<IdMapper> idMappers = dealGroupWrapper.batchMtDealGroupIdByDp(olDpDealGroupIds);
        List<Integer> mtDealGroupIds = new ArrayList<>();
        Map<Integer, Integer> dp2mt = new HashMap<>();
        Map<Integer, Integer> mt2dp = new HashMap<>();

        for (IdMapper mapper : idMappers) {
            mtDealGroupIds.add(mapper.getMtDealGroupID());
            dp2mt.put(mapper.getDpDealGroupID(), mapper.getMtDealGroupID());
            mt2dp.put(mapper.getMtDealGroupID(), mapper.getDpDealGroupID());
        }

        data.setMtDealGroupIds(mtDealGroupIds);
        data.setDp2mt(dp2mt);
        data.setMt2dp(mt2dp);

        int currentDpGroupId = Optional.ofNullable(mt2dp.get(currentDealGroupId)).orElse(0);
        data.setCurrentDpGroupId(currentDpGroupId);
        data.setPublishCategoryId(dealGroupPublishCategoryQueryService.getPublishCategory(currentDpGroupId));

        return data;
    }

    private RelatedDeals buildRelatedDeals(DealTabHolder dealTabHolder, BaseData data, Long shopId, EnvCtx envCtx, String mrnVersion) {
        if (dealTabHolder == null || CollectionUtils.isEmpty(dealTabHolder.getRelatedTabs())) {
            return null;
        }

        boolean isDp = data.isDp();
        Map<Integer, Integer> dp2mt = data.getDp2mt();

        List<RelatedDealInfo> infos = new ArrayList<>();
        for (DealTab tab : dealTabHolder.getRelatedTabs()) {
            RelatedDealInfo info = new RelatedDealInfo();
            int dealGroupId = isDp ? tab.getDealGroupId() : dp2mt.get(tab.getDealGroupId());
            info.setDealGroupId(String.valueOf(dealGroupId));
            info.setShopId(String.valueOf(shopId));
            info.setShopUuid(isDp ? ShopUuidUtils.getUuidByIdLong(shopId) : null);
            info.setModuleKey(tab.getModuleKey());
            info.setTabName(genTabName(tab));

            infos.add(info);
        }

        RelatedDeals deals = new RelatedDeals();
        deals.setDealInfos(infos);
        deals.setPopoverDisplayThreshold(dealTabHolder.getPopoverDisplayThreshold());
        deals.setPopoverTitle(dealTabHolder.getPopoverTitle());

        // 设置展示样式
        if(isAllEnableCardStyle(envCtx, mrnVersion)) {
            deals.setGeneralInfo(Cons.CARD_STYLE);
        } else {
            ModuleAbConfig cardStyleAbConfig = getCardStyleAbConfig(data.getPublishCategoryId(), envCtx, mrnVersion) ;
            if(cardStyleAbConfig != null && CollectionUtils.isNotEmpty(cardStyleAbConfig.getConfigs())) {
                if("c".equals(cardStyleAbConfig.getConfigs().get(0).getExpResult())) {
                    deals.setGeneralInfo(Cons.CARD_STYLE);
                }
                List<ModuleAbConfig> moduleAbConfigs = new ArrayList<>();
                moduleAbConfigs.add(cardStyleAbConfig);
                deals.setModuleAbConfigs(moduleAbConfigs);
            }
        }

        // 同店比货模块样式优化实验
        buildCompareSameShopPriceStyleAb(envCtx, deals, data.getPublishCategoryId());
        return deals;
    }

    /**
     * 同店比货模块样式优化实验
     * @param envCtx
     * @param deals
     * @param categoryId
     */
    public void buildCompareSameShopPriceStyleAb(EnvCtx envCtx, RelatedDeals deals, Integer categoryId){
        // 在团详1.0的样式下
        if (Cons.CARD_STYLE.equals(deals.getGeneralInfo())){
            ModuleAbConfig moduleAbConfig = douHuService.getCompareSameShopPriceStyleAbConfigByEnvCtx(categoryId, envCtx);
            buildCompareSameShopPriceStyleAb(moduleAbConfig, deals, categoryId);
        }

    }

    public void buildCompareSameShopPriceStyleAb(ModuleAbConfig moduleAbConfig, RelatedDeals deals, Integer categoryId){
        String expResult = douHuService.getExpResult(moduleAbConfig);
        if ("a".equals(expResult) || "b".equals(expResult) || "d".equals(expResult)){
            ExpResultConfig expResultConfig = LionConfigUtils.getExpResultConfig();
            String tabStyle = douHuService.getTabStyle(expResultConfig, categoryId, moduleAbConfig);
            deals.setTabStyle(tabStyle);
            // 增加打点信息
            if (Objects.nonNull(moduleAbConfig)){
                List<ModuleAbConfig> moduleAbConfigList = getModuleAbConfigs(deals);
                moduleAbConfigList.add(moduleAbConfig);
            }
        }
    }

    private List<ModuleAbConfig> getModuleAbConfigs(RelatedDeals deals){
        List<ModuleAbConfig> moduleAbConfigs = deals.getModuleAbConfigs();
        if (CollectionUtils.isEmpty(moduleAbConfigs)){
            moduleAbConfigs = new ArrayList<>();
            deals.setModuleAbConfigs(moduleAbConfigs);
        }
        return moduleAbConfigs;
    }

    private String genTabName(DealTab tab) {
        return tab.getSalePrice() + " " + tab.getTag();
    }

    public DealGroupImVo queryDealGroupIM(DealIMReq request, EnvCtx envCtx) {

        DealGroupImVo vo = new DealGroupImVo();
        int dpDealGroupId;
        long dpShopId;

        if (envCtx.isMt()) {
            Future future = mapperWrapper.preDpShopIdByMtShopId(request.getShopIdLong());
            dpShopId = mapperWrapper.getDpShopIdByMtShopIdLong(future);
            dpDealGroupId = dealGroupWrapper.getDpDealGroupId(request.getDealgroupid());
        } else {
            dpShopId = request.getShopIdLong();
            dpDealGroupId = request.getDealgroupid();

        }

        if (dpDealGroupId <= 0 || dpShopId <= 0) {
            return vo;
        }
        try {
            DealGroupChannelDTO channel = dealGroupPublishCategoryQueryService.getDealGroupChannelById(dpDealGroupId);
            if (!hasImFunction(channel)) {
                return vo;
            }
        } catch (Exception e) {
            FaultToleranceUtils.addException("getDealGroupChannelById", e);
        }
        Future future = dzImWrapper.preOnlineConsultUrl(dpShopId, dpDealGroupId, envCtx.getClientType());
        String onlineConsultUrl = dzImWrapper.getOnlineConsultUrl(future);
        vo.setImUrl(onlineConsultUrl);

        return vo;

    }

    private boolean hasImFunction(DealGroupChannelDTO channel) {
        if (channel == null) {
            return false;
        }

        return SwitchHelper.isIm(channel.getChannelDTO().getChannelId())
                || SwitchHelper.isCategoryIm(channel.getCategoryId());
    }

    public void processStandardDealGroup(DealCtx ctx) {
        List<AttributeDTO> attrs = ctx.getAttrs();
        if(isStandardDealGroup(attrs)) {

            DealGroupPBO result = ctx.getResult();
            result.setStandardDealGroup(true);

            String serviceType = getServiceType(attrs);
            if (ctx.getCategoryId() == 502 && "美甲".equals(serviceType)) {
                String saleDesc = result.getSaleDesc();
                if (StringUtils.isNotBlank(saleDesc) && !StringUtils.startsWith(saleDesc, "本地")) {
                    saleDesc = "本地" + saleDesc;
                    result.setSaleDesc(saleDesc);
                }
            } else if (ctx.getCategoryId() == 503 && "SPA按摩".equals(serviceType)) {
                String title = result.getTitle();
                if (StringUtils.isNotBlank(title) && !StringUtils.startsWith(title, "【美团标准服务】")) {
                    title = "【美团标准服务】" + title;
                    result.setTitle(title);
                }
            }
        }

    }

    private boolean isStandardDealGroup(List<AttributeDTO> attributeDTOS) {
        if(CollectionUtils.isEmpty(attributeDTOS)) {
            return false;
        }
        for(AttributeDTO attributeDTO : attributeDTOS) {
            if(attributeDTO == null) {
                continue;
            }
            if("standardDealGroup".equals(attributeDTO.getName()) && attributeDTO.getValue() != null && attributeDTO.getValue().contains("1")) {
                return true;
            }
        }
        return false;
    }

    private String getServiceType(List<AttributeDTO> attributeDTOS) {
        if(CollectionUtils.isEmpty(attributeDTOS)) {
            return "";
        }
        for(AttributeDTO attributeDTO : attributeDTOS) {
            if(attributeDTO == null) {
                continue;
            }
            if("service_type".equals(attributeDTO.getName()) && CollectionUtils.isNotEmpty(attributeDTO.getValue())) {
                return attributeDTO.getValue().get(0);
            }
        }
        return "";
    }

    private ModuleAbConfig getCardStyleAbConfig(int publishCategoryId, EnvCtx envCtx, String mrnVersion) {
        CardStyleConfig cardStyleConfig = Lion.getBean("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.card.style.config", CardStyleConfig.class);
        if(envCtx == null || cardStyleConfig == null || !cardStyleConfig.isEnableCardStyle()) {
            return null;
        }
        if(cardStyleConfig.getEnableClientType() != null && !cardStyleConfig.getEnableClientType().contains(envCtx.getDztgClientTypeEnum().getCode())) {
            return null;
        }
        if(!cardStyleEnvironmentPass(mrnVersion, envCtx)) {
            return null;
        }
        if(cardStyleConfig.isAllPass()) {
            return null;
        }
        boolean isMt = envCtx.isMt();
        String key = isMt ? "mt" : "dp";
        key += publishCategoryId;
        String expId = cardStyleConfig.getCategory2ExpId().getOrDefault(key, null);

        String unionId = envCtx.getUnionId();
        Map<String, String> expId2ModuleId = Lion.getMap("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.card.style.expId2ModuleId", String.class, new HashMap<>());
        String module = expId2ModuleId.getOrDefault(expId, "CardStyleAB");

        return douHuBiz.getAbByUnionIdAndExpId(unionId, expId, module, isMt);
    }

    private boolean isAllEnableCardStyle(EnvCtx envCtx, String mrnVersion) {
        CardStyleConfig cardStyleConfig = Lion.getBean("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.card.style.config", CardStyleConfig.class);
        if(envCtx == null || cardStyleConfig == null || !cardStyleConfig.isEnableCardStyle()) {
            return false;
        }
        if(cardStyleConfig.getEnableClientType() != null && !cardStyleConfig.getEnableClientType().contains(envCtx.getDztgClientTypeEnum().getCode())) {
            return false;
        }
        if(!cardStyleEnvironmentPass(mrnVersion, envCtx)) {
            return false;
        }
        return cardStyleConfig.isAllPass();
    }

    private boolean cardStyleEnvironmentPass(String mrnVersion, EnvCtx envCtx) {
        if(envCtx == null) {
            return false;
        }
        if(DztgClientTypeEnum.MEITUAN_APP.equals(envCtx.getDztgClientTypeEnum())
                && VersionUtils.isGreatEqualThan(envCtx.getVersion(), "12.11.200")
                && VersionUtils.isGreatEqualThan(mrnVersion, "0.5.3")) {
            return true;
        }
        if(DztgClientTypeEnum.DIANPING_APP.equals(envCtx.getDztgClientTypeEnum())
                && VersionUtils.isGreatEqualThan(envCtx.getVersion(), "11.4.0")
                && VersionUtils.isGreatEqualThan(mrnVersion, "0.5.3")) {
            return true;
        }
        return false;
    }

    /**
     * 团单返回值后置处理
     */
    @SuppressWarnings("unchecked")
    public void postProcessResult(DealCtx dealCtx) {
        if(dealCtx == null || dealCtx.getResult() == null){
            return;
        }
        ResultPostProcessHandler.getInstance().execute(dealCtx.getResult(), dealCtx);
    }

    private String getPosition(EnvCtx envCtx, DealBaseReq req) {
        if (envCtx.isHarmony()) {
            return "";
        }
        if (envCtx.getDztgClientTypeEnum() == DztgClientTypeEnum.MEITUAN_APP) {
            return "2104";
        }
        if (envCtx.getDztgClientTypeEnum() == DztgClientTypeEnum.DIANPING_APP
                && isMagicMemberValid(req.getMrnversion())) {
            return "3104";
        }
        if (envCtx.getDztgClientTypeEnum() == DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP) {
            return "1104";
        }
        return "";
    }

    // 神会员优惠生效开关
    private static boolean isMagicMemberValid(String mrnVersion) {
        if (StringUtils.isBlank(mrnVersion)) {
            return false;
        }
        return VersionUtils.isGreatEqualThan(mrnVersion, Lion.getString("com.sankuai.dzu.tpbase.dztgdetailweb",
                LionConstants.MAGICAL_DP_MRN_MIN_VERSION, "0.5.11"));
    }

}