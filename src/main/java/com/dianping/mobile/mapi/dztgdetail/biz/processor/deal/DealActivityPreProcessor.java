package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.gmkt.activity.api.dto.BatchQueryDealActivityRequest;
import com.dianping.gmkt.activity.api.dto.DealActivityDTO;
import com.dianping.gmkt.activity.api.dto.Version;
import com.dianping.gmkt.activity.api.enums.ExposeChannel;
import com.dianping.gmkt.activity.api.enums.RequestSource;
import com.dianping.gmkt.activity.enums.AppPlatform;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealActivityWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.google.common.collect.Lists;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
public class DealActivityPreProcessor extends AbsDealProcessor {

    @Resource
    private DealActivityWrapper dealActivityWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        return true;
//        return ctx.getEnvCtx().isMainApp();
    }

    @Override
    public void prepare(DealCtx ctx) {
        Future activityFuture = dealActivityWrapper.prepareDealActivity(buildBatchQueryDealActivityReq(ctx));
        ctx.getFutureCtx().setDealPreActivitiesFuture(activityFuture);
    }

    @Override
    public void process(DealCtx ctx) {
        List<DealActivityDTO> dealActivities = dealActivityWrapper.queryDealActivity(ctx.getFutureCtx().getDealPreActivitiesFuture(), ctx);
        ctx.setDealActivities(dealActivities);
    }

    private BatchQueryDealActivityRequest buildBatchQueryDealActivityReq(DealCtx ctx) {
        BatchQueryDealActivityRequest request = new BatchQueryDealActivityRequest();
        if (ctx.isMt()) {
            request.setMtDealIds(Lists.newArrayList(ctx.getMtId()));
            request.setMtCity(ctx.getMtCityId());
            request.setUserIdL(ctx.getEnvCtx().getMtUserId());//已确认判断平台后再使用
            request.setAppPlatform(AppPlatform.MT);
        } else {
            request.setDpDealIds(Lists.newArrayList(ctx.getDpId()));
            request.setDpCity(ctx.getDpCityId());
            request.setUserIdL(ctx.getEnvCtx().getDpUserId());
            request.setAppPlatform(AppPlatform.DP);
        }
        request.setSource(RequestSource.TuanDetail);
        request.setVersion(new Version(ctx.getEnvCtx().getVersion()));
        request.setChannel(ExposeChannel.App.code);
        return request;
    }

    @Override
    public List<Integer> getConfigUrlDztgClient(DealCtx ctx) {
        return ctx.getEnvCtx().MAIN_APP_CLIENT_LIST;
    }
}
