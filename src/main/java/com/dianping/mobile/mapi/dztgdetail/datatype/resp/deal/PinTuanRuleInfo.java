package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/4/23 16:04
 */
@Data
@TypeDoc(description = "拼团规则信息")
@MobileDo(id = 0x1255)
public class PinTuanRuleInfo implements Serializable {

    @FieldDoc(description = "拼团规则名称")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "拼团规则弹窗")
    @MobileDo.MobileField(key = 0xc522)
    private FeaturesLayer featuresLayer;

    @FieldDoc(description = "拼团规则icon和文案")
    @MobileDo.MobileField(key = 0xe23d)
    private List<Guarantee> items;
}
