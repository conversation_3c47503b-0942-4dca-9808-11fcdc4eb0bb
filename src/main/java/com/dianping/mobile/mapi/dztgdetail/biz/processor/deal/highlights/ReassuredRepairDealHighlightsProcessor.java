package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CommonAttrVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.highlights.DztgHighlightsModule;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import com.dianping.mobile.mapi.dztgdetail.util.ReassuredRepairUtil;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


@Service
public class ReassuredRepairDealHighlightsProcessor extends AbsDealProcessor {

    @Override
    public boolean isEnable(DealCtx ctx) {
        return ReassuredRepairUtil.isTagPresent(ctx);
    }

    @Override
    public void prepare(DealCtx ctx) {

    }

    @Override
    public void process(DealCtx ctx) {
        if (ReassuredRepairUtil.isTagPresent(ctx)) {
            DztgHighlightsModule dztgHighlightsModule = new DztgHighlightsModule();
            List<CommonAttrVO> list = new ArrayList<>();
            if (DealAttrHelper.needReservation(ctx.getAttrs())) {
                //result.setServiceProcessModule(Arrays.asList("第1步:预约下单","第2步:待上门报价","第3步:线上付款","第4步:验收放款"));
                list = Arrays.asList(new CommonAttrVO("第1步", "预约下单"), new CommonAttrVO("第2步", "待上门报价"),
                        new CommonAttrVO("第3步", "线上付款"), new CommonAttrVO("第4步", "验收放款"));

            } else {
                //第1步:购买下单","第2步:待上门报价","第3步:线下付款
                list = Arrays.asList(new CommonAttrVO("第1步", "购买下单"), new CommonAttrVO("第2步", "待上门报价"),
                        new CommonAttrVO("第3步", "线下付款"));
            }
            dztgHighlightsModule.setAttrs(list);
            dztgHighlightsModule.setDelimiter("arrow");
            dztgHighlightsModule.setStyle("struct");
            ctx.setHighlightsModule(dztgHighlightsModule);
        }
    }
}
