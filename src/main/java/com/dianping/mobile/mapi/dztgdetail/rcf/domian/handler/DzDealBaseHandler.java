package com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.deal.bff.cache.enums.RcfDealBffInterfaceEnum;
import com.dianping.mobile.mapi.dztgdetail.rcf.api.req.DealNativeSnapshotReq;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.DealRcfCustomerProcessor;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.dto.DealBffResponseDTO;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.service.DzDealBaseService;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.deallayout.DealLayoutService;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.deallayout.dto.ModuleItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2024/12/25 10:39
 */
@Slf4j
@Component
public class DzDealBaseHandler implements DealRcfCustomerProcessor {

    public  static  final String  dealdetail_gc_packagedetail = "dealdetail_gc_packagedetail";
    public  static  final String  dealdetail = "团购详情";

    @Resource
    private DealLayoutService dealLayoutService;

    @Resource
    private DzDealBaseService dzDealBaseService;

    @Override
    public void customerProcess(DealNativeSnapshotReq request, DealBffResponseDTO bffResponse) {
        try{
            JSONObject dealBase = bffResponse.getBffResponse(RcfDealBffInterfaceEnum.dzdealbase);
            // 拍平moduleConfigsModule
            moduleConfigsModuleFlatten(dealBase);
            // 拍平moduleExtra
            moduleExtraFlatten(dealBase);
            // 团单名称格式化
            dzDealBaseService.processDealTitle(dealBase);
        } catch (Exception e){
            log.error("DzDealBaseFlattenHandler process error", e);
        }
    }

    @Override
    public boolean canProcess(DealNativeSnapshotReq request, DealBffResponseDTO bffResponse) {
        // dzdealbase接口返回有数据就处理
        return Objects.nonNull(bffResponse.getBffResponse(RcfDealBffInterfaceEnum.dzdealbase));
    }

    /**
     * moduleConfigsModule拍平
     * @param dealBase
     */
    public void moduleConfigsModuleFlatten(JSONObject dealBase){
        JSONObject moduleConfigsModule = (JSONObject) dealBase.get("moduleConfigsModule");
        if (Objects.isNull(moduleConfigsModule)){
            return;
        }
        JSONArray moduleConfigs = (JSONArray) moduleConfigsModule.get("moduleConfigs");
        if (Objects.isNull(moduleConfigs) || moduleConfigs.isEmpty()){
            return;
        }
        JSONObject rcfModuleConfigs = new JSONObject();
        dealBase.put("rcfModuleConfigs", rcfModuleConfigs);
        for (int i = 0; i < moduleConfigs.size(); i++) {
            JSONObject moduleConfig = (JSONObject) moduleConfigs.get(i);
            String key = (String) moduleConfig.get("key");
            if (dealdetail_gc_packagedetail.equals(key)){
                String value = (String) moduleConfig.get("value");
                rcfModuleConfigs.put(key, value);
                return;
            }
        }
    }

    /**
     * moduleExtra拍平
     */
    public void moduleExtraFlatten(JSONObject dealBase){
        JSONObject moduleExtra = (JSONObject) dealBase.get("moduleExtra");
        if (Objects.isNull(moduleExtra)){
            return;
        }
        JSONArray moduleConfigDos = (JSONArray) moduleExtra.get("moduleConfigDos");
        if (moduleConfigDos == null || moduleConfigDos.isEmpty()){
            return;
        }
        for (int i = 0; i <moduleConfigDos.size(); i++) {
            JSONObject moduleConfig = (JSONObject) moduleConfigDos.get(i);
            String key = (String) moduleConfig.get("key");
            String value = (String) moduleConfig.get("value");
            if (dealdetail.equals(key)){
                JSONObject rcfModuleExtra = new JSONObject();
                dealBase.put("rcfModuleExtra", rcfModuleExtra);
                JSONObject dealDetail = new JSONObject();
                dealDetail.put("type", value);
                rcfModuleExtra.put("dealDetail", dealDetail);
                buildMultiCard(value, dealDetail);
            }
        }
    }

    public void buildMultiCard(String key, JSONObject dealDetail){
        Map<String, ModuleItem> moduleItemMap = dealLayoutService.fetchDealDetailTabConfig(key);
        if (MapUtils.isEmpty(moduleItemMap)){
            return;
        }
        ModuleItem tabConfig = dealLayoutService.getModuleItem("local_dealdetail_struct_module", moduleItemMap);
        if (null == tabConfig || Objects.isNull(tabConfig.getExtraInfo())){
            return;
        }
        JSONObject extraInfo = tabConfig.getExtraInfo();
        Object multiCard = extraInfo.get("multiCard");
        dealDetail.put("multiCard", multiCard);
    }
}
