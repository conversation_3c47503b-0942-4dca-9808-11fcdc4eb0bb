package com.dianping.mobile.mapi.dztgdetail.entity;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * https://123.sankuai.com/km/page/57936041
 */
public class BrowserEntity implements Serializable {

    public static final int TG_TYPE = 2;

    private int type;

    private int cityid;

    private int categoryid;

    private String dpid;

    private String userid;

    private BrowserContent content;

    private String platform;

    @JSONField(name="_id")
    private String _id = "";

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getCityid() {
        return cityid;
    }

    public void setCityid(int cityid) {
        this.cityid = cityid;
    }

    public int getCategoryid() {
        return categoryid;
    }

    public void setCategoryid(int categoryid) {
        this.categoryid = categoryid;
    }

    public String getDpid() {
        return dpid;
    }

    public void setDpid(String dpid) {
        this.dpid = dpid;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public BrowserContent getContent() {
        return content;
    }

    public void setContent(BrowserContent content) {
        this.content = content;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    @JSONField(name="_id")
    public String get_id() {
        return _id;
    }

    @JSONField(name="_id")
    public void set_id(String _id) {
        this._id = _id;
    }
}