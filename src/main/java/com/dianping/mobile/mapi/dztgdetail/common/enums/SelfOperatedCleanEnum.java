package com.dianping.mobile.mapi.dztgdetail.common.enums;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Getter
@Slf4j
public enum SelfOperatedCleanEnum {
    NONE(0, "none", "无"),
    COMPEL(1, "compelOrder","强预订"),
    SEPARATE_ORDER(2,"separateOrder", "保持预约浮层"),
    OPTION_ORDER(3,"optionOrder", "支持暂不预约的融合提单浮层");

    private final int code;
    private final String value;
    private final String desc;

    SelfOperatedCleanEnum(int code, String value, String desc) {
        this.code = code;
        this.value = value;
        this.desc = desc;
    }

    public static String getResultFromDealParam(String dealParam,String key) {
        try {
            if (StringUtils.isBlank(dealParam)) {
                return "";
            }

            Map<String, String> paraMap = getDealParamMap(dealParam);
            if(MapUtils.isEmpty(paraMap)) {
                return "";
            }

            String expResult = paraMap.getOrDefault(key, "");

            if (StringUtils.isBlank(expResult)) {
                return "";
            }
            return expResult;
        } catch (Exception e) {
            log.error("getResultFromDealParam error,dealParam:{}",dealParam,e);
        }
        return "";
    }
    
    public static SelfOperatedCleanEnum parseDealParam(String dealParam) {
        boolean hitNewPoiStyle = LionConfigUtils.isHitNewPoiStyle();
        if (!hitNewPoiStyle) {
            return NONE;
        }
        if(StringUtils.isBlank(dealParam)) {
            return NONE;
        }
        try {
            String orderType = getResultFromDealParam(dealParam,"selfOperatedCleanOrderType");
            if(StringUtils.isBlank(orderType)) {
                return NONE;
            }

            if (StringUtils.equals("compelOrder",orderType)) {
                return COMPEL;
            }
            
            if (StringUtils.equals("separateOrder",orderType)) {
                return SEPARATE_ORDER;
            }

            if (StringUtils.equals("optionOrder",orderType)) {
                return OPTION_ORDER;
            }
            return NONE;

        } catch (Exception e) {
            log.error("parseDealParam error,dealParam:{}",dealParam,e);
        }
        return NONE;
    }

    public static String getExpResultFromDealParam(String dealParam) {
        return getResultFromDealParam(dealParam, "expResult");
    }

    private static Map<String, String> getDealParamMap(String dealParam) {
        return GsonUtils.fromJsonString(dealParam, new com.google.gson.reflect.TypeToken<Map<String, String>>() {}.getType());
    }

}
