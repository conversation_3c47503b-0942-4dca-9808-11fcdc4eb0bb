package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/3/11.
 */
@Slf4j
public class ListUtils {

    /**
     * 将“1,2,3"这样的字符串转化为对应的int列表<br>
     * 参数为空串时返回{@link Collections#emptyList()}，不为空时返回ArrayList
     */
    public static List<Integer> convertString2IntList(String str, String separator) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.util.ListUtils.convertString2IntList(java.lang.String,java.lang.String)");
        if (org.apache.commons.lang.StringUtils.isBlank(str)) {
            return Collections.emptyList();
        }
        String[] array = str.split(separator);
        ArrayList<Integer> list = new ArrayList<>();
        try {
            for (String s : array) {
                list.add(Integer.parseInt(s));
            }
        } catch (Exception e) {
            log.error("convertString2IntList error, str: {}, sep: {}", str, separator, e);
            return Collections.emptyList();
        }
        return list;
    }

    public static List<Long> convertInteger2LongList(List<Integer> list) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.util.ListUtils.convertInteger2LongList(java.util.List)");
        if (list == null) {
            return null;
        }
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().map(Integer::longValue).collect(Collectors.toList());
    }


    /**
     * 取两个集合的交集
     */
    public static <T> List<T> intersection(Set<T> set1, Set<T> set2) {
        List<T> list = new LinkedList<>();
        for (T each : set1) {
            if (set2.contains(each)) {
                list.add(each);
            }
        }
        return list;
    }

    public static <T> List<T> subList(List<T> list, int offset, int limit) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.util.ListUtils.subList(java.util.List,int,int)");
        if (list != null && !list.isEmpty()) {
            if (offset >= list.size()) {
                return Collections.emptyList();
            } else {
                if (offset < 0) {
                    offset = 0;
                }

                if (limit <= 0) {
                    limit = list.size();
                }

                int toIndex = offset + limit;
                if (toIndex > list.size()) {
                    toIndex = list.size();
                }

                return list.subList(offset, toIndex);
            }
        } else {
            return Collections.emptyList();
        }
    }

}
