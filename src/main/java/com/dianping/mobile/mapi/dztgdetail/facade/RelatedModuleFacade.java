package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.cat.Cat;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.deal.idmapper.api.DealIdMapperService;
import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.dianping.deal.sales.common.datatype.ProductParam;
import com.dianping.deal.sales.common.datatype.SalesDisplayDTO;
import com.dianping.deal.sales.common.datatype.SalesDisplayRequest;
import com.dianping.deal.sales.common.enums.SalesPlatform;
import com.dianping.deal.shop.constants.Platform;
import com.dianping.deal.shop.dto.DealGroupShop;
import com.dianping.deal.shop.dto.DealGroupShopSearchRequest;
import com.dianping.gmkt.activ.api.polymeric.request.MaterialClientType;
import com.dianping.gmkt.activity.api.enums.AppPlatform;
import com.dianping.gmkt.activity.api.enums.QuerySecKillSceneStrategyEnum;
import com.dianping.gmkt.activity.api.request.QuerySecKillSceneByMaterialRequest;
import com.dianping.gmkt.event.datapools.api.enums.seckill.SeckillActivityMaterialTypeEnum;
import com.dianping.gmkt.event.datapools.api.model.seckill.SeckillSceneSimpleDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.ProcessHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule.RelatedDealIdProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.*;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.constants.SaleConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.RelatedModuleCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.*;
import com.dianping.mobile.mapi.dztgdetail.helper.PriceHelper;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.dianping.poi.relation.service.api.PoiRelationService;
import com.google.common.collect.*;
import com.sankuai.dealuser.price.display.api.enums.PriceDiscountClassifyTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.ProductTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.RequestSceneEnum;
import com.sankuai.dealuser.price.display.api.model.*;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupBasicInfoBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupCategoryBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupIdBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupImageBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.Validate;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 团详相关模块（如：附近优惠团购模块）
 * <AUTHOR>
 * @date 2023/5/4 21:02
 */
@Component
@Slf4j
public class RelatedModuleFacade {
    @Autowired
    private Map<String, RelatedDealIdProcessor> relatedDealIdStrategyMap;

    @Autowired
    private QueryCenterWrapper queryCenterWrapper;

    @Resource
    private PoiClientWrapper poiClientWrapper;

    @Autowired
    private MapperWrapper mapperWrapper;

    public RelatedDealModuleVO getRelatedModuleDeals(RelatedModuleReq req, EnvCtx envCtx) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.RelatedModuleFacade.getRelatedModuleDeals(RelatedModuleReq,EnvCtx)");
        checkRequest(req);

        RelatedModuleCtx ctx = new RelatedModuleCtx();
        initContext(req, ctx, envCtx);

        RelatedDealIdProcessor relatedDealIdProcessorStrategy = relatedDealIdStrategyMap.get(req.getScene());
        if (relatedDealIdProcessorStrategy == null && StringUtils.isNotBlank(req.getScene())) {
            // 从Lion中获取
            Map<String, String> sceneCodeMap = Lion.getMap(LionConstants.APP_KEY,
                    LionConstants.GENERAL_INFO_2_SCENE_CODE, String.class, Collections.emptyMap());
            String sceneCode = sceneCodeMap.get(req.getScene());
            req.setScene(sceneCode);
            relatedDealIdProcessorStrategy = relatedDealIdStrategyMap.get(sceneCode);
        }
        if(relatedDealIdProcessorStrategy == null){
            log.error(String.format("RelatedModuleFacade scene:%s strategy not found", req.getScene()));
            return null;
        }
        List<Long> dealGroupIds = relatedDealIdProcessorStrategy.getRelatedDealGroupIds(ctx);
        if (CollectionUtils.isEmpty(dealGroupIds)) {
            log.error("RelatedModuleFacade dealGroupIds is empty, request:" + GsonUtils.toJsonString(req));
            return null;
        }

        return relatedDealIdProcessorStrategy.assemble(ctx, dealGroupIds);
    }

    private void checkRequest(RelatedModuleReq req) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.RelatedModuleFacade.checkRequest(com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedModuleReq)");
        Validate.notEmpty(req.getScene());
        Validate.notEmpty(req.getShopIdStr());
        Validate.notNull(req.getLimit());
        Validate.notNull(req.getStart());
    }

    private void initContext(RelatedModuleReq req, RelatedModuleCtx ctx, EnvCtx envCtx) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.RelatedModuleFacade.initContext(RelatedModuleReq,RelatedModuleCtx,EnvCtx)");
        ctx.setReq(req);
        ctx.setEnvCtx(envCtx);
        ctx.setResult(new RelatedDealModuleVO());
        QueryByDealGroupIdRequest dealGroupRequest;
        if (envCtx.isMt()) {
            dealGroupRequest = QueryByDealGroupIdRequestBuilder.builder()
                    .dealGroupIds(Sets.newHashSet(req.getDealGroupId().longValue()), IdTypeEnum.MT)
                    .category(DealGroupCategoryBuilder.builder().categoryId())
                    .dealGroupId(DealGroupIdBuilder.builder().dpDealGroupId())
                    .build();
        } else {
            dealGroupRequest = QueryByDealGroupIdRequestBuilder.builder()
                    .dealGroupIds(Sets.newHashSet(req.getDealGroupId().longValue()), IdTypeEnum.DP)
                    .category(DealGroupCategoryBuilder.builder().categoryId())
                    .build();
        }
        Future dealGroupDTOFuture = queryCenterWrapper.preDealGroupDTO(dealGroupRequest);

        Long dpShopId;
        if (envCtx.isMt()) {
            dpShopId = mapperWrapper.getDpByMtShopId(Long.valueOf(req.getShopIdStr()));
        } else {
            dpShopId = Long.valueOf(req.getShopIdStr());
        }
        if (dpShopId != null && dpShopId > 0) {
            DpPoiDTO dpPoiDTO = poiClientWrapper.getDpPoiDTO(dpShopId, Lists.newArrayList("shopId", "backMainCategoryPath", "cityId"));
            ctx.setDpPoiDTO(dpPoiDTO);
        }
        if (ctx.getDpPoiDTO() == null) {
            throw new RuntimeException("dpPoiDTO is null, shopId:" + req.getShopIdStr());
        }

        DealGroupDTO dealGroupDTO;
        try {
            dealGroupDTO = queryCenterWrapper.getDealGroupDTO(dealGroupDTOFuture);
            ctx.setDealGroupDTO(dealGroupDTO);
        } catch (TException e) {
            log.error("RelatedModuleFacade getDealGroupDTO error, dealGroupId:" + req.getDealGroupId(), e);
        }
    }
}
