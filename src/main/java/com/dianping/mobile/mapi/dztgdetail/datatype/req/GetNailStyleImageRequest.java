package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.DecryptedField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-12-27
 */
@Data
@TypeDoc(description = "美甲款式查询参数")
@MobileRequest
public class GetNailStyleImageRequest implements IMobileRequest, Serializable {
    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID")
    @MobileRequest.Param(name = "dealGroupId", required = true)
    private Long dealGroupId;

    @FieldDoc(description = "门店ID")
    @MobileRequest.Param(name = "shopId")
    private Long shopId;
    @MobileRequest.Param(name = "shopIdEncrypt")
    @DecryptedField(targetFieldName = "shopId")
    private String shopIdEncrypt;

    @FieldDoc(description = "首页城市id")
    @MobileRequest.Param(name = "cityId")
    private Integer cityId;

    @FieldDoc(description = "经度")
    @MobileRequest.Param(name = "userLng")
    private Double userLng;

    @FieldDoc(description = "纬度")
    @MobileRequest.Param(name = "userLat")
    private Double userLat;
}
