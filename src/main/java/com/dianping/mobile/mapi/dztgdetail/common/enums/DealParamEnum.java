package com.dianping.mobile.mapi.dztgdetail.common.enums;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.dianping.swallow.common.internal.util.MapUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2023/7/12
 * @since mapi-dztgdetail-web
 */
public enum DealParamEnum {

    MLIVE_INFO(1, "MLIVE_INFO","美团直播信息");


    private final int code;
    private final String key;
    private final String desc;


    DealParamEnum(int code, String key, String desc) {
        this.code = code;
        this.key = key;
        this.desc = desc;
    }

    public static DealParamEnum codeOf(int code) {
        for (DealParamEnum value : DealParamEnum.values()) {
            if (value.code == code) {
                return value;
            }
        }

        throw new UnsupportedOperationException("DealParamEnum has no code of " + code);
    }

    public static boolean isMLiveSource(String dealParam) {
        if(StringUtils.isBlank(dealParam)) {
            return false;
        }
        try {
            Map<String, String> paraMap = getDealParamMap(dealParam);
            if(MapUtils.isEmpty(paraMap)) {
                return false;
            }
            Map<String, String> liveMap = GsonUtils.fromJsonString(paraMap.getOrDefault(MLIVE_INFO.key, null), new com.google.gson.reflect.TypeToken<Map<String, String>>() {}.getType());
            if(MapUtils.isNotEmpty(liveMap)) {
                return liveMap.get("mlivesource").equals("mlive");
            }
        } catch (Exception e) {
            Cat.logError(e);
        }
        return false;
    }

    private static Map<String, String> getDealParamMap(String dealParam) {
        return GsonUtils.fromJsonString(dealParam, new com.google.gson.reflect.TypeToken<Map<String, String>>() {}.getType());
    }

}
