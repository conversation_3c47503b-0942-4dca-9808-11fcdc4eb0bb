package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.cat.Cat;
import com.dianping.deal.publishcategory.dto.ChannelDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Cons;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.constants.TgDetailLionKeys;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public class SwitchHelper {

    private static final List<Integer> STRUCT_CATEGORYS = Lists.newArrayList(401);

    public static boolean isIm(int channelId) {
        List<Integer> imChannelId = Lion.getList(LionConstants.IM_CHANNEL_IDS, Integer.class);
        return CollectionUtils.isNotEmpty(imChannelId) && imChannelId.contains(channelId);
    }

    public static boolean isCategoryIm(int channelId) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.helper.SwitchHelper.isCategoryIm(int)");
        List<Integer> imChannelId = Lion.getList(LionConstants.IM_CATEGORY_IDS, Integer.class);
        return CollectionUtils.isNotEmpty(imChannelId) && imChannelId.contains(channelId);
    }

    public static boolean isInBook(int publishCategoryId) {
        return useStrJudge(LionConstants.BOOK_CATEGORYIDS, String.valueOf(publishCategoryId), Cons.SEPARATOR);
    }

    private static boolean useStrJudge(String lionKey, String value, String separator) {
        if (StringUtils.isBlank(lionKey) || StringUtils.isBlank(value) || StringUtils.isBlank(separator)) {
            return false;
        }
        String lionValue = Lion.getStringValue(lionKey, StringUtils.EMPTY);
        return ArrayUtils.contains(lionValue.split(separator), value);
    }

    public static boolean isTgBrowse(){
        return Lion.getBooleanValue(TgDetailLionKeys.TG_BROWSE_SWITCH,false);
    }

    public static boolean foldDetailCategoryStruct(DealGroupChannelDTO channelDTO, long userId, int dpDealGroupId) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.helper.SwitchHelper.foldDetailCategoryStruct(com.dianping.deal.publishcategory.dto.DealGroupChannelDTO,long,int)");
        if (channelDTO == null) {
            return false;
        }

        int publishCategoryId = channelDTO.getCategoryId();
        ChannelDTO channel = channelDTO.getChannelDTO();
        int channelId = channel != null ? channel.getChannelId() : 0;

        ImageTextGrayCfg cfg = Lion.getBean(
                "com.sankuai.dzu.tpbase.dztgdetailweb", LionConstants.IMAGE_TEXT_DETAIL_FOLD_GRAY_CFG, ImageTextGrayCfg.class, null);

        if (cfg == null) {
            return true;
        }

        if (cfg.isAllPass()) {
            return false;
        }

        if (CollectionUtils.isNotEmpty(cfg.getDpDealGroupIds()) && cfg.getDpDealGroupIds().contains(dpDealGroupId)) {
            return false;
        }

        if (userId != 0 && MapUtils.isNotEmpty(cfg.getGrayRatioPercent())) {
            Map<Integer, Integer> grayRatioPercentMap = cfg.getGrayRatioPercent();
            Integer ratio = Optional.ofNullable(grayRatioPercentMap.get(publishCategoryId)).orElse(grayRatioPercentMap.get(channelId));
            ratio = Optional.ofNullable(ratio).orElse(grayRatioPercentMap.get(0));

            if (ratio != null) {
                return userId % 100 >= ratio;
            }
        }

        return true;
    }

    public static boolean foldDetailCategoryStructV2(com.sankuai.general.product.query.center.client.dto.DealGroupChannelDTO channel, long userId, int dpDealGroupId, int publishCategoryId) {
        if (channel == null) {
            return false;
        }

        int channelId = channel.getChannelId();

        ImageTextGrayCfg cfg = Lion.getBean(
                "com.sankuai.dzu.tpbase.dztgdetailweb", LionConstants.IMAGE_TEXT_DETAIL_FOLD_GRAY_CFG, ImageTextGrayCfg.class, null);

        if (cfg == null) {
            return true;
        }

        if (cfg.isAllPass()) {
            return false;
        }

        if (CollectionUtils.isNotEmpty(cfg.getDpDealGroupIds()) && cfg.getDpDealGroupIds().contains(dpDealGroupId)) {
            return false;
        }

        if (userId != 0 && MapUtils.isNotEmpty(cfg.getGrayRatioPercent())) {
            Map<Integer, Integer> grayRatioPercentMap = cfg.getGrayRatioPercent();
            Integer ratio = Optional.ofNullable(grayRatioPercentMap.get(publishCategoryId)).orElse(grayRatioPercentMap.get(channelId));
            ratio = Optional.ofNullable(ratio).orElse(grayRatioPercentMap.get(0));

            if (ratio != null) {
                return userId % 100 >= ratio;
            }
        }

        return true;
    }

    public static boolean hideDetailCategory(int publishCategoryId) {
        List<Integer> hideCategoryIds = Lion.getList(LionConstants.DETAIL_HIDE_CATEGORYIDS, Integer.class, Lists.newArrayList(Cons.JOY_BAR));
        return CollectionUtils.isNotEmpty(hideCategoryIds) && hideCategoryIds.contains(publishCategoryId);
    }

    public static boolean enableDrivingShop(boolean isMt, long shopId, int cityId) {

        Map<String, List> cfg = Lion.getMap(LionConstants.DRIVING_SHOP_GRAY_CFG, List.class, new HashMap<>());

        //配置为空表示全量
        if (MapUtils.isEmpty(cfg)) {
            return true;
        }

        String shopKey, cityKey;

        if (isMt) {
           shopKey = "mt" + shopId;
           cityKey = "mt" + cityId;
        } else {
           shopKey = "dp" + shopId;
           cityKey = "dp" + cityId;
        }

        List<String> shops = cfg.get("shops");

        if (CollectionUtils.isNotEmpty(shops) && shops.contains(shopKey)) {
            return true;
        }

        List<String> cities = cfg.get("cities");

        return CollectionUtils.isNotEmpty(cities) && cities.contains(cityKey);
    }

    @Data
    private static class ImageTextGrayCfg {
        private boolean allPass;

        private List<Integer> dpDealGroupIds;

        /**
         * key：类目id，包括一级类目和二级类目
         * value：灰度开放比例，100为全量
         */
        private Map<Integer, Integer> grayRatioPercent;
    }
}
