package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.DealCategoryEnum;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.EducationDealAttrUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.BaseDisplayItemVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailDisplayUnitVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailSpecificModuleVO;
import com.dianping.mobile.mapi.dztgdetail.util.EduDealUtils;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.dianping.technician.common.api.domain.TechnicianResp;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.technician.info.online.dto.OnlineTechWithAttrsDTO;
import com.sankuai.technician.info.online.service.OnlineTechQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SpecificModuleHandler_1205 implements DealDetailSpecificModuleHandler {

    public static final String ATTR_NAME_GIFT_PRODUCT = "实物赠品";
    public static final String JOIN_STR = "、";
    public static final String ATTR_NAME_CLASS_SAFEGUARD = "课程保障";
    public static final String ATTR_NAME_APPEND_SERVICE = "附加服务";
    public static final String ATTR_NAME_CLASS_TYPE = "班型";
    public static final String ATTR_NAME_SUITABLE_CLASEE = "适用阶段";
    public static final String ATTR_NAME_SUITABLE_PEOPLE = "适用人群";
    public static final String ATTR_NAME_CLASS_NUM = "课时数";
    public static final String ATTR_NAME_TRAIN_TIME = "集训时长";
    public static final String ATTR_NAME_MAIN_TEACHER = "主讲老师";
    public static final String DISPLAY_TYPE_SERVICE = "service";
    public static final int TIMEOUT = 500;
    public static final String ATTR_NAME_CLASS_STAGE = "课程阶段";
    public static final String ATTR_NAME_SUITABLE_PROVINCE = "适用省份";

    @Autowired
    private QueryCenterWrapper queryCenterWrapper;

    @Autowired
    @Qualifier("onlineTechQueryServiceFuture")
    private OnlineTechQueryService onlineTechQueryService;

    @Override
    public String identity() {
        return String.valueOf(DealCategoryEnum.EDU_1205.getDealCategoryId());
    }

    @Override
    public void handle(SpecificModuleCtx ctx) {
        ctx.setResult(buildResult(ctx));
    }


    private DealDetailSpecificModuleVO buildResult(SpecificModuleCtx context) {
        DealGroupDTO dealGroupDTO = context.getDealGroupDTO();
        if (dealGroupDTO == null) {
            return null;
        }
        List<DealDetailDisplayUnitVO> units = EduDealUtils.isCivilExam(dealGroupDTO)
                ? buildUnitsForCivilExam(context)
                : buildUnitsForNotCivilExam(context);

        DealDetailSpecificModuleVO result = new DealDetailSpecificModuleVO();
        result.setUnits(units);
        return result;

    }

    private List<DealDetailDisplayUnitVO> buildUnitsForCivilExam(SpecificModuleCtx context) {
        List<DealDetailDisplayUnitVO> units = Lists.newArrayList();
        // 主讲老师
        units.add(getTeacher(context));
        // 课时数
        units.add(getClassNum(context));
        // 适用人群
        units.add(getSuitablePeople(context));
        // 适用阶段
        units.add(getSuitableClass(context));
        // 班型
        units.add(getClassType(context));
        // 适用省份
        units.add(getSuitableProvince(context));
        // 课程阶段
        units.add(getClassStage(context));
        // 附加服务
        units.add(getAppendService(context));
        // 课程保障
        units.add(getClassSafeguard(context));
        // 实物赠品(附加商品)
        units.add(getGiftProduct(context));
        return units.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<DealDetailDisplayUnitVO> buildUnitsForNotCivilExam(SpecificModuleCtx context) {
        List<DealDetailDisplayUnitVO> units = Lists.newArrayList();
        // 集训时长
        units.add(getTrainTime(context));
        // 课时数
        units.add(getClassNum(context));
        // 班型
        units.add(getClassType(context));
        // 附加服务
        units.add(getAppendService(context));
        // 课程保障
        units.add(getClassSafeguard(context));
        // 实物赠品
        units.add(getGiftProduct(context));
        // 适用阶段
        units.add(getSuitableClass(context));
        // 适用人群
        units.add(getSuitablePeople(context));
        return units.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private DealDetailDisplayUnitVO getClassStage(SpecificModuleCtx context) {
        String suitableBase = EducationDealAttrUtils.getClassStage(context.getDealGroupDTO());
        if (StringUtils.isBlank(suitableBase)) {
            return null;
        }
        return getDealDetailDisplayUnitVO(ATTR_NAME_CLASS_STAGE, Lists.newArrayList(buildDisplayItem("", suitableBase)));
    }

    private DealDetailDisplayUnitVO getSuitableProvince(SpecificModuleCtx context) {
        List<String> suitableProvince = EducationDealAttrUtils.getSuitableProvince(context.getDealGroupDTO());
        if (CollectionUtils.isEmpty(suitableProvince)) {
            return null;
        }
        return getDealDetailDisplayUnitVO(ATTR_NAME_SUITABLE_PROVINCE,
                Lists.newArrayList(buildDisplayItem("", String.join(JOIN_STR, suitableProvince))));
    }

    private DealDetailDisplayUnitVO getGiftProduct(SpecificModuleCtx context) {
        List<String> giftProduct = EducationDealAttrUtils.getGiftProduct(context.getDealGroupDTO());
        if (CollectionUtils.isEmpty(giftProduct)) {
            return null;
        }
        return getDealDetailDisplayUnitVO(ATTR_NAME_GIFT_PRODUCT, Lists.newArrayList(buildDisplayItem("",
                String.join(JOIN_STR, giftProduct))));
    }

    private DealDetailDisplayUnitVO getClassSafeguard(SpecificModuleCtx context) {
        List<String> classSafeguard = EducationDealAttrUtils.getClassSafeguard(context.getDealGroupDTO());
        if (CollectionUtils.isEmpty(classSafeguard)) {
            return null;
        }
        return getDealDetailDisplayUnitVO(ATTR_NAME_CLASS_SAFEGUARD, Lists.newArrayList(buildDisplayItem("",
                String.join(JOIN_STR, classSafeguard))));
    }

    private DealDetailDisplayUnitVO getAppendService(SpecificModuleCtx context) {
        List<String> appendServices = EducationDealAttrUtils.getAppendServices(context.getDealGroupDTO());
        if (CollectionUtils.isEmpty(appendServices)) {
            return null;
        }
        return getDealDetailDisplayUnitVO(ATTR_NAME_APPEND_SERVICE, Lists.newArrayList(buildDisplayItem("",
                String.join(JOIN_STR, appendServices))));
    }

    private DealDetailDisplayUnitVO getClassType(SpecificModuleCtx context) {
        String origClassType = EducationDealAttrUtils.getOrigClassType(context.getDealGroupDTO());
        if (StringUtils.isBlank(origClassType)) {
            return null;
        }
        return getDealDetailDisplayUnitVO(ATTR_NAME_CLASS_TYPE, Lists.newArrayList(buildDisplayItem("", origClassType)));
    }

    private DealDetailDisplayUnitVO getSuitableClass(SpecificModuleCtx context) {
        String suitableClass = EducationDealAttrUtils.getSuitableClass(context.getDealGroupDTO());
        if (StringUtils.isBlank(suitableClass)) {
            return null;
        }
        return getDealDetailDisplayUnitVO(ATTR_NAME_SUITABLE_CLASEE, Lists.newArrayList(buildDisplayItem("", suitableClass)));
    }

    private DealDetailDisplayUnitVO getSuitablePeople(SpecificModuleCtx context) {
        String suitablePeople = EducationDealAttrUtils.getSuitablePeople(context.getDealGroupDTO());
        if (StringUtils.isBlank(suitablePeople)) {
            return null;
        }
        return getDealDetailDisplayUnitVO(ATTR_NAME_SUITABLE_PEOPLE, Lists.newArrayList(buildDisplayItem("", suitablePeople)));
    }

    private DealDetailDisplayUnitVO getTrainTime(SpecificModuleCtx context) {
        String trainTime = EducationDealAttrUtils.getTrainTime(context.getDealGroupDTO());
        if (StringUtils.isBlank(trainTime)) {
            return null;
        }
        return getDealDetailDisplayUnitVO(ATTR_NAME_TRAIN_TIME, Lists.newArrayList(buildDisplayItem("", trainTime)));
    }

    private DealDetailDisplayUnitVO getClassNum(SpecificModuleCtx context) {
        String classNum = EducationDealAttrUtils.getClassNum(context.getDealGroupDTO());
        if (StringUtils.isBlank(classNum)) {
            return null;
        }
        return getDealDetailDisplayUnitVO(ATTR_NAME_CLASS_NUM, Lists.newArrayList(buildDisplayItem("", classNum)));
    }

    private DealDetailDisplayUnitVO getTeacher(SpecificModuleCtx context) {
        try {
            List<Integer> teacherIds = EducationDealAttrUtils.getTeacherIds(context.getDealGroupDTO());
            onlineTechQueryService.getTechByTechIds(teacherIds);
            Future<TechnicianResp<List<OnlineTechWithAttrsDTO>>> future = (Future<TechnicianResp<List<OnlineTechWithAttrsDTO>>>) FutureFactory.getFuture();
            TechnicianResp<List<OnlineTechWithAttrsDTO>> onlineTechResponse = future.get(TIMEOUT, TimeUnit.MILLISECONDS);

            if (onlineTechResponse == null || onlineTechResponse.respFail() || CollectionUtils.isEmpty(onlineTechResponse.getData())) {
                return null;
            }
            List<OnlineTechWithAttrsDTO> onlineTechList = onlineTechResponse.getData();
            String teacherStr = onlineTechList.stream()
                    .filter(dto -> dto != null && dto.getTechnician() != null)
                    .map(dto -> dto.getTechnician().getName())
                    .collect(Collectors.joining(JOIN_STR));
            return getDealDetailDisplayUnitVO(ATTR_NAME_MAIN_TEACHER, Lists.newArrayList(buildDisplayItem("", teacherStr)));
        } catch (Exception e) {
            log.error("构建老师失败", e);
        }
        return null;
    }

    private static DealDetailDisplayUnitVO getDealDetailDisplayUnitVO(String attrName, List<BaseDisplayItemVO> displayItemList) {
        DealDetailDisplayUnitVO unit = new DealDetailDisplayUnitVO();
        unit.setTitle(attrName);
        unit.setDisplayItems(displayItemList);
        //这个是和前端约定的，用于区分浮层样式
        unit.setType(DISPLAY_TYPE_SERVICE);
        return unit;
    }

    private BaseDisplayItemVO buildDisplayItem(String name, String detail) {
        BaseDisplayItemVO itemVO = new BaseDisplayItemVO();
        itemVO.setName(name);
        itemVO.setDetail(detail);
        return itemVO;
    }
}