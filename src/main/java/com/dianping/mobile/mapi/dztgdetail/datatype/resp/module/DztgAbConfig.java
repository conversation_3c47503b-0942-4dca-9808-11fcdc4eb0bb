package com.dianping.mobile.mapi.dztgdetail.datatype.resp.module;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;

import java.io.Serializable;

@MobileDo(id = 0xfa7c)
public class DztgAbConfig implements Serializable {
    /**
    * 实验ID
    */
    @MobileField(key = 0x85df)
    private String expId;

    /**
    * 实验结果
    */
    @MobileField(key = 0x9263)
    private String expResult;

    /**
    * 实验上报信息
    */
    @MobileField(key = 0xddc3)
    private String expBiInfo;

    public String getExpId() {
        return expId;
    }

    public void setExpId(String expId) {
        this.expId = expId;
    }

    public String getExpResult() {
        return expResult;
    }

    public void setExpResult(String expResult) {
        this.expResult = expResult;
    }

    public String getExpBiInfo() {
        return expBiInfo;
    }

    public void setExpBiInfo(String expBiInfo) {
        this.expBiInfo = expBiInfo;
    }
}