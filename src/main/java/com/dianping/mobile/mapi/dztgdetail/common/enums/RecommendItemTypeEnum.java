package com.dianping.mobile.mapi.dztgdetail.common.enums;

/**
 * @Author: z<PERSON><PERSON><PERSON><PERSON><PERSON>@meituan.com
 * @Date: 2024/11/1
 */
public enum RecommendItemTypeEnum {
    DEAL(1, "deal"),
    POI(2, "poi"),
    PRODUCT(3, "product"),
    UNKNOWN(-999, "unknown");

    private int value;

    private String name;

    RecommendItemTypeEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static RecommendItemTypeEnum valueOf(int value) {

        for (RecommendItemTypeEnum recommendItemType : values()) {
            if (recommendItemType.value == value) {
                return recommendItemType;
            }
        }
        return null;
    }
}
