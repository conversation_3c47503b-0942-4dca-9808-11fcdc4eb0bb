package com.dianping.mobile.mapi.dztgdetail.biz.processor.headerpic;

import com.dianping.mobile.mapi.dztgdetail.common.enums.ImageScaleEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: zheng<PERSON><EMAIL>
 * @Date: 2023/8/20
 */
@Component("beautyHeaderPic")
@Slf4j
public class BeautyHeaderPicProcessor extends AbstractHeaderPicProcessor {

    @Override
    public void fillPicScale(DealCtx ctx, List<ContentPBO> result, DealGroupPBO dealGroupPBO) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }

        // 如果团单不是指定品类，则直接返回
        if (!LionConfigUtils.hasCustomHeaderPic("beauty", ctx.getCategoryId())) {
            return;
        }

        // 判定是否有款式
        boolean showExhibit = matchShowExhibit(ctx);

        // 不满足有款式条件时，下发团购头图信息即可
        if (!showExhibit) {
            result.forEach(contentPBO -> {
                contentPBO.setScale(ImageScaleEnum.SIXTEEN_TO_NINE.getScale());
            });
            dealGroupPBO.setExhibitContents(null);
            return;
        }

        // 有款式条件 则showExhibit=true，即展示3:4容器
        for (ContentPBO contentPBO : result) {
            contentPBO.setScale(ImageScaleEnum.THREE_TO_FOUR.getScale());
        }

        if (Objects.nonNull(ctx.getExhibitContentDTO()) && CollectionUtils.isNotEmpty(ctx.getExhibitContentDTO().getItems())) {
            ctx.getExhibitContentDTO().getItems().stream().flatMap(item -> item.getUrls().stream()).forEach(urlVO -> {
                urlVO.setScale(ImageScaleEnum.THREE_TO_FOUR.getScale());
            });
        }
        // 获取头图&款式展示规则配置
        assembleHeaderPicAndExhibitInfo(ctx, dealGroupPBO);
        return;
    }

    @Override
    public boolean matchShowExhibit(DealCtx ctx) {
        List<Long> tagIds = ListUtils.emptyIfNull(ctx.getDealGroupDTO().getTags()).stream().map(DealGroupTagDTO::getId).collect(Collectors.toList());
        boolean hitDealTag = LionConfigUtils.hitDealTag(tagIds);
        boolean hasExhibit = ctx.isBeautyNailMultiStyle();
        return hitDealTag && hasExhibit;
    }
}
