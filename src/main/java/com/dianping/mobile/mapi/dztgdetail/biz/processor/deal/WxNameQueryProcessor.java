package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.WxNameQueryWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.AppTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.common.ResponseDTO;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomtoconsumer.GetUserCodeRequest;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomtoconsumer.UserCodeAndNickNameDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.Optional;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @date 2025/03/07
 */
@Slf4j
public class WxNameQueryProcessor extends AbsDealProcessor {

    @Autowired
    private WxNameQueryWrapper wxNameQueryWrapper;


    @Override
    public boolean isEnable(DealCtx ctx) {
        return ctx.isMtLiveMinApp() && LionConfigUtils.getWxNameQuerySwitch();
    }


    @Override
    public void prepare(DealCtx ctx) {
        GetUserCodeRequest request = buildGetUserCodeRequest(ctx);
        Future future = wxNameQueryWrapper.getUserCodeAndNickName(request);
        Optional.ofNullable(future).ifPresent(f -> ctx.getFutureCtx().setDistributionRpcFuture(f));
    }

    @Override
    public void process(DealCtx ctx) {
        Future future = ctx.getFutureCtx().getDistributionRpcFuture();
        if (future == null) {
            log.warn("Distribution RPC future is null for ctx: {}", ctx);
            return;
        }
        ResponseDTO<UserCodeAndNickNameDTO> response = wxNameQueryWrapper.getFutureResult(future);
        Optional.ofNullable(response)
                .filter(ResponseDTO::isSuccess)
                .map(ResponseDTO::getData)
                .ifPresent(dto -> {
                    ctx.setWxName(dto.getNickName());
                    ctx.setUserDistributionParam(dto.getUserCode());
                });
    }


    private GetUserCodeRequest buildGetUserCodeRequest(DealCtx ctx) {
        GetUserCodeRequest req = new GetUserCodeRequest();
        req.setMtUserId(ctx.getEnvCtx().getMtUserId());
        req.setMiniProgramType(AppTypeEnum.MT.getValue());
        req.setUnionId(ctx.getEnvCtx().getUnionId());
        req.setTime(new Date().getTime());
        DealBaseReq dealBaseReq = ctx.getDealBaseReq();
        req.setLiveId(dealBaseReq.getPrivateLiveId());
        req.setUserCode(dealBaseReq.getUserDistributionParam());
        req.setDistributionParam(dealBaseReq.getDistributionParam());
        return req;
    }
}
