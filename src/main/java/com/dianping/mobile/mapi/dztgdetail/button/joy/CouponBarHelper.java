package com.dianping.mobile.mapi.dztgdetail.button.joy;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.PromoHelper;
import com.sankuai.dealuser.price.display.api.enums.CouponAssignStatusEnum;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

public class CouponBarHelper {
    public static final String DEFAULT_PRE_ORDER_BUTTON_NAME = "立即预订";
    public static final String PROMO_PRE_ORDER_BUTTON_NAME = "优惠订";
    public static String getCouponBtnTitle(DealCtx context) {
        List<PromoDTO> couponPromos = PromoHelper.getCouponUsePromo(context);
        // 是否是预订单
        boolean needPreOrder = DealCtxHelper.isPreOrderDeal(context);
        if (CollectionUtils.isEmpty(couponPromos)) {
            return replaceNormalTitle( context,"立即抢购");
        }
        boolean hasCoupon = false;
        for (PromoDTO couponPromo : couponPromos) {
            if (couponPromo.getIdentity().getPromoType() == PromoTypeEnum.COUPON.getType()) {
                hasCoupon = true;
                if (couponPromo.isCanAssign()) {
                    return replacePromoTitle(context, "领券抢购");
                }
            }
            if (couponPromo.getIdentity().getPromoType() == PromoTypeEnum.GOVERNMENT_CONSUME_COUPON.getType()) {
                hasCoupon = true;
                if (couponPromo.getCouponAssignStatus() == CouponAssignStatusEnum.UN_ASSIGNED.getCode()) {
                    return replacePromoTitle(context, "领券抢购");
                }
            }
        }
        if (hasCoupon) {
            return replacePromoTitle(context, "用券抢购");
        }
        return replaceNormalTitle(context, "立即抢购");
    }

    private static String replaceNormalTitle(DealCtx context, String title) {
        // 是否是预订单
        boolean needPreOrder = DealCtxHelper.isPreOrderDeal(context);
        return needPreOrder ? DEFAULT_PRE_ORDER_BUTTON_NAME : title;
    }

    private static String replacePromoTitle(DealCtx context, String title) {
        // 是否是预订单
        boolean needPreOrder = DealCtxHelper.isPreOrderDeal(context);
        return needPreOrder ? PROMO_PRE_ORDER_BUTTON_NAME : title;
    }
}
