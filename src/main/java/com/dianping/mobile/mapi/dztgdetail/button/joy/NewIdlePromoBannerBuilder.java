package com.dianping.mobile.mapi.dztgdetail.button.joy;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.constants.PlusIcons;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.LeadActionEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBanner;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBannerDetail;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.helper.PriceHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper;
import com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;

public class NewIdlePromoBannerBuilder extends AbstractButtonBuilder {

    @Override
    public void build(DealCtx context, ButtonBuilderChain chain) {
        buildBanner(context);
        chain.build(context);
    }

    private void buildBanner(DealCtx context) {
        PriceDisplayDTO promoPrice = context.getPriceContext().getIdlePromoPrice();
        if (promoPrice == null || CollectionUtils.isEmpty(promoPrice.getUsedPromos())) {
            return;
        }
        PriceDisplayDTO normalPrice = PriceHelper.getNormalPrice(context);
        if (promoPrice.getPrice().compareTo(
                Optional.ofNullable(normalPrice).map(PriceDisplayDTO::getPrice).orElse(BigDecimal.ZERO)) >= 0) {
            return;
        }
        context.getBuyBar().setBuyBanner(buildIdlePromoBanner(context, promoPrice));
    }

    @Override
    public void doBuild(DealCtx context, ButtonBuilderChain chain) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.button.joy.NewIdlePromoBannerBuilder.doBuild(DealCtx,ButtonBuilderChain)");

    }

    private DealBuyBanner buildIdlePromoBanner(DealCtx ctx, PriceDisplayDTO idlePromo) {
        BigDecimal price = ctx.getDealGroupBase().getDealGroupPrice()
                .subtract(idlePromo.getPromoAmount());

        price = price.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : price;

        PromoDTO promoDTO = idlePromo.getUsedPromos().get(0);

        String reductionStr = "￥" + idlePromo.getPromoAmount().setScale(2, RoundingMode.CEILING)
                .stripTrailingZeros().toPlainString();
        String bannerContent = promoDTO.getDescription() + " 再省  " + reductionStr;
        bannerContent = JsonLabelUtil.idleHoursPromoBannerJson(ctx.isMt(), bannerContent, reductionStr);

        String timeLimit = promoDTO.getConsumeTimeDesc();
        String lineOne = "为了保障您的权益，请在" + timeLimit + "到店消费，感谢您的理解和支持。";
        lineOne = JsonLabelUtil.idleHoursPromoBannerDetailJson(ctx.isMt(), lineOne, timeLimit);
        DealBuyBannerDetail detail = new DealBuyBannerDetail();
        detail.setContent(Lists.newArrayList(lineOne));
        detail.setMarketPrice(ctx.getDealGroupBase().getMarketPrice().stripTrailingZeros().toPlainString());
        detail.setPrice(price.toPlainString());
        detail.setRedirectUrl(UrlHelper.getIdleHoursBuyUrl(ctx, ctx.getMtCityId()));

        DealBuyBanner banner = new DealBuyBanner();
        banner.setBannerDetail(detail);
        banner.setLeadText("去购买");
        banner.setLeadAction(LeadActionEnum.TOAST_IDLE_HOURS.getCode());
        banner.setContent(bannerContent);
        banner.setIconUrl(PlusIcons.IDLE_HOURS_PROMO);
        banner.setShow(isShow(ctx));
        return banner;
    }

    private boolean isShow(DealCtx context) {
        boolean show = true;
        for (DealBuyBtn buyBtn : context.getBuyBar().getBuyBtns()) {
            if (buyBtn.getDetailBuyType() == BuyBtnTypeEnum.IDLE_DEAL.getCode()) {
                show = false;
                break;
            }
        }
        return show;
    }

}
