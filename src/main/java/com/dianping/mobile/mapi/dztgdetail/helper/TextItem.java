package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.json.facade.JsonFacade;

import java.util.Collections;

public class TextItem {

    public static final String GRAY = "#FF999999";
    public static final String GRAY_H5 = "#999999";
    public static final String TRANSPARENT_WHITE = "#00FFFFFF";
    public static final String TRANSPARENT_WHITE_H5 = "#FFFFFF";

    public static final String DEFAULT_TEXT_STYLE = "Default";

    private String text;
    private int textsize = 14;
    private String textcolor = GRAY;
    private String backgroundcolor = TRANSPARENT_WHITE;
    private String textstyle = DEFAULT_TEXT_STYLE;
    private boolean strikethrough = false;
    private boolean underline = false;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public int getTextsize() {
        return textsize;
    }

    public void setTextsize(int textsize) {
        this.textsize = textsize;
    }

    public String getTextcolor() {
        return textcolor;
    }

    public void setTextcolor(String textcolor) {
        this.textcolor = textcolor;
    }

    public String getBackgroundcolor() {
        return backgroundcolor;
    }

    public void setBackgroundcolor(String backgroundcolor) {
        this.backgroundcolor = backgroundcolor;
    }

    public String getTextstyle() {
        return textstyle;
    }

    public void setTextstyle(String textstyle) {
        this.textstyle = textstyle;
    }

    public boolean isStrikethrough() {
        return strikethrough;
    }

    public void setStrikethrough(boolean strikethrough) {
        this.strikethrough = strikethrough;
    }

    public boolean isUnderline() {
        return underline;
    }

    public void setUnderline(boolean underline) {
        this.underline = underline;
    }

    public String toJL() {
        return JsonFacade.serialize(Collections.singletonList(this));
    }
}
