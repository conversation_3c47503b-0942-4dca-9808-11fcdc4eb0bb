package com.dianping.mobile.mapi.dztgdetail.button;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.entity.FreeDealConfig;
import com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Objects;

/**
 * * 预付款团购按钮
 */
public class PrePayDealButtonBuilder extends AbstractButtonBuilder {

    @Override
    protected void doBuild(DealCtx context, ButtonBuilderChain chain) {
        if (context.getPriceContext() == null) {
            return;
        }
        // 新版团详用dealPromoPrice,旧版团详价格在normalPrice，对于点评小程序需要做旧版兼容
        PriceDisplayDTO priceDisplayDTO = context.isEnableCardStyleV2() ? context.getPriceContext().getDealPromoPrice() :
                context.getPriceContext().getNormalPrice();
        if (priceDisplayDTO == null) {
            return;
        }
        DealBuyBtn btn = buildOriginButton(context, DEFAULT_BUTTON_NAME);
        if (priceDisplayDTO.getPrePayPriceDetail() != null && priceDisplayDTO.getPrePayPriceDetail().getPrePayActualPrice() != null) {
            String prePayPrice = formatPrice(priceDisplayDTO.getPrePayPriceDetail().getPrePayActualPrice());
            btn.setBtnTitle("立即支付");
            btn.setAddShoppingCartStatus(0);
            btn.setDetailBuyType(BuyBtnTypeEnum.NORMAL_DEAL.getCode());
            btn.setBtnSubTitle("预付款¥" + prePayPrice + "元");
            // 新版样式上需要置空，否则会展示错乱
            if (context.isEnableCardStyleV2()) {
                btn.setPriceStr(StringUtils.EMPTY);
                btn.setBtnDesc(StringUtils.EMPTY);
            }
        }
        // 点评小程序预付款商品的新版提单页有问题，需要先拦截下单
        if (context.getEnvCtx().isDpMiniApp() && !Lion.getBoolean(LionConstants.APP_KEY, LionConstants.PREPAY_DP_MINI_APP_BUY_BAR_SWITCH, true)) {
            btn.setBlockMsg("为保证您的用户体验，请至“美团/点评”APP购买");
        }

        context.addButton(btn);
        chain.build(context);

    }
}
