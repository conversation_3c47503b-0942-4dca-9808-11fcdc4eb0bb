package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.feitianplus.data.onedata.api.thrift.domain.KeyQueryParamSync;
import com.sankuai.feitianplus.data.onedata.api.thrift.domain.QueryDataTitle;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @author: wuwen<PERSON><PERSON>
 * @create: 2024-11-27
 * @description: 飞天数据接口工具类
 */
public class QueryDataHelper {

    public static String QUERY_DATA_PARAM_KEY = "params";

    public static KeyQueryParamSync buildQueryParam(int bizTypeId, String queryKey, String cellName, Map<String, String> paramMap) {
        KeyQueryParamSync param = new KeyQueryParamSync();
        param.setBizTypeId(bizTypeId);
        param.setQueryKey(queryKey);
        param.setCellName(cellName);
        Map<String, String> keyParamMap = Maps.newHashMap();
        keyParamMap.put(QUERY_DATA_PARAM_KEY, GsonUtils.toJsonString(paramMap));
        param.setParams(keyParamMap);
        return param;
    }

    public static List<Map<String, String>> getQueryDataMapList(List<QueryDataTitle> queryDataTitles, List<List<String>> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }
        List<Map<String, String>> result = Lists.newArrayList();
        for (List<String> data : dataList) {
            Map<String, String> dataMap = getQueryDataMap(data, queryDataTitles);
            if (MapUtils.isNotEmpty(dataMap)) {
                result.add(dataMap);
            }
        }
        return result;
    }

    private static Map<String, String> getQueryDataMap(List<String> data, List<QueryDataTitle> queryDataTitles) {
        if (CollectionUtils.isEmpty(data) || CollectionUtils.isEmpty(queryDataTitles)
                || data.size() != queryDataTitles.size()) {
            return Maps.newHashMap();
        }
        Map<String, String> result = Maps.newHashMap();
        for (int i = 0; i < queryDataTitles.size(); i++) {
            QueryDataTitle queryDataTitle = queryDataTitles.get(i);
            if (Objects.isNull(queryDataTitle) || StringUtils.isEmpty(queryDataTitle.getColumnName())
                    || StringUtils.isEmpty(data.get(i))) {
                continue;
            }
            result.put(queryDataTitle.getColumnName(), data.get(i));
        }
        return result;
    }
}
