package com.dianping.mobile.mapi.dztgdetail.tab.postprocess;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.DealTab;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.DealTabHolder;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class CfgBasedRelatePostProcessor implements RelatePostProcessor {

    private final static int DEFAULT_TAB_SIZE = 5;

    @Override
    public void postProcessAfterRelate(int publishCategory, DealTabHolder dealTabHolder) {
        TabCfg config = matchCfg(publishCategory);
        if (config == null) {
            return;
        }

        List<DealTab> resultTabs = subTabs(dealTabHolder.getRelatedTabs(), config.getTabSize());

        for (DealTab tab : resultTabs) {
            tagMapping(tab, config.getTagMapping());
            setModuleKey(tab, config.getModuleKey());
        }

        if (StringUtils.isNotBlank(config.getPopoverTitle())) {
            dealTabHolder.setPopoverTitle(config.getPopoverTitle());
        }
        if (config.getPopoverDisplayThreshold() != null) {
            dealTabHolder.setPopoverDisplayThreshold(config.getPopoverDisplayThreshold());
        }
        dealTabHolder.setRelatedTabs(resultTabs);
    }

    /**
     * 截取tabSize个数的tab
     * @param tabs
     * @param tabSize
     * @return
     */
    private List<DealTab> subTabs(List<DealTab> tabs, int tabSize) {

        if (tabSize <= 0) {
            //默认展示5个tab
            tabSize = DEFAULT_TAB_SIZE;
        }

        if (tabs.size() > tabSize) {
            return tabs.subList(0, tabSize);
        } else {
            return tabs;
        }

    }

    /**
     *
     * @param tab
     * @param moduleKey
     */
    private void setModuleKey(DealTab tab, String moduleKey) {
        if (StringUtils.isNotBlank(moduleKey)) {
            tab.setModuleKey(moduleKey);
        }
    }

    /**
     *
     * @param tab
     * @param tagMapping
     */
    private void tagMapping(DealTab tab, Map<String, String> tagMapping) {
        String tag = tab.getTag();

        if (MapUtils.isNotEmpty(tagMapping) && StringUtils.isNotBlank(tagMapping.get(tag))) {
            tab.setTag(tagMapping.get(tag));
        }
    }

    /**
     * 根据类目匹配配置
     * @param publishCategory
     * @return
     */
    private TabCfg matchCfg(int publishCategory) {
        Gson gson = new Gson();
        /**
         * 控制猜喜入口：com.sankuai.dzu.tpbase.dztgdetailweb.tab.cfg -> 后续有变更可和猜喜确认是否已移除
         * 控制团详本身：com.sankuai.dzu.tpbase.dztgdetailweb.tab.cfg.new
         */
        List<TabCfg> configs = gson.fromJson(Lion.getStringValue("com.sankuai.dzu.tpbase.dztgdetailweb.tab.cfg.new"), new TypeToken<List<TabCfg>>() {
        }.getType());

        if (CollectionUtils.isEmpty(configs)) {
            return null;
        }

        for (TabCfg cfg : configs) {
            if (CollectionUtils.isNotEmpty(cfg.getPublishCategories()) && cfg.getPublishCategories().contains(publishCategory)) {
                return cfg;
            }
        }

        return null;
    }

    @Data
    private class TabCfg {
        private List<Integer> publishCategories;
        private Integer popoverDisplayThreshold;
        private String popoverTitle;
        private String moduleKey;
        private int tabSize = 5;
        private Map<String, String> tagMapping;
    }
}
