package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.base.dto.DealBaseDTO;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.book.req.ShopBookDto;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.gmkt.activity.api.dto.BatchQueryDealActivityRequest;
import com.dianping.gmkt.activity.api.dto.DealActivityDTO;
import com.dianping.gmkt.activity.api.dto.Version;
import com.dianping.gmkt.activity.api.enums.ExposeChannel;
import com.dianping.gmkt.activity.api.enums.ExposureDateKeyEnum;
import com.dianping.gmkt.activity.api.enums.PriceStrengthTimeEnum;
import com.dianping.gmkt.activity.api.enums.RequestSource;
import com.dianping.gmkt.activity.enums.AppPlatform;
import com.dianping.json.facade.JsonFacade;
import com.dianping.lion.client.Lion;
import com.dianping.lion.facade.LionFacade;
import com.dianping.mobile.mapi.dztgdetail.biz.LeafRepository;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.DealCategoryFactory;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.DealDouHuUtil;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.headerpic.HeaderPicProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.extend.PromoDetailHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.extend.PromoDetailLocator;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.extend.config.PromoDetailConfig;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.extend.config.PromoDetailConfigDto;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealActivityWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiClientWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiShopCategoryWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ReserveProductWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.ShopUuidUtils;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Cons;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Constants;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.constants.PlusIcons;
import com.dianping.mobile.mapi.dztgdetail.common.constants.QueryParams;
import com.dianping.mobile.mapi.dztgdetail.common.enums.AtmosphereSceneEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ContentType;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.PromoDetailEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.SaleStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.PriceContext;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ComBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ServiceGuarantee.DealBestPromoDetailDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.DztgCouponButton;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.DztgCouponInfo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.DztgPromoExposureInfoVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.PromoActivityInfoVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.TimesCardPBO;
import com.dianping.mobile.mapi.dztgdetail.entity.PoiInfoCustomizedConfig;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.utils.FaultToleranceUtils;
import com.dianping.mobile.mapi.dztgdetail.helper.*;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.poi.bizhour.BizHourForecastService;
import com.dianping.poi.bizhour.dto.BizForecastDTO;
import com.dianping.poi.bizhour.enums.SourceEnum;
import com.dianping.scrum.util.DateUtils;
import com.dianping.tgc.open.entity.BatchExProxyCouponResponseDTO;
import com.dianping.tgc.open.entity.PromoCouponButton;
import com.dianping.tgc.open.entity.PromoCouponInfo;
import com.dianping.tgc.open.entity.PromoExposureInfo;
import com.dianping.tgc.open.entity.PromoReturnInfo;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.meituan.mobile.sinai.base.common.PoiFields;
import com.meituan.service.mobile.sinai.client.model.PoiModelL;
import com.sankuai.beautycontent.security.displaycontrol.response.DisplayControlResponse;
import com.sankuai.clr.content.process.gateway.thrift.api.BookStatusGatewayService;
import com.sankuai.clr.content.process.gateway.thrift.dto.book.BookStatusQueryGatewayReqDTO;
import com.sankuai.clr.content.process.gateway.thrift.dto.book.BookStatusQueryGatewayRespDTO;
import com.sankuai.clr.content.process.gateway.thrift.enums.BookStatusSceneEnum;
import com.sankuai.clr.content.process.gateway.thrift.enums.ContentGatewayCodeEnum;
import com.sankuai.clr.content.process.gateway.thrift.enums.SubjectTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.CouponAssignStatusEnum;
import com.sankuai.dealuser.price.display.api.enums.ExtPriceTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.ExtPriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PricePowerTagDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PricePowerTagItem;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealTimeStockDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.general.product.query.center.client.dto.deal.rule.readjustPriceRule.ReadjustPriceRuleDTO;
import com.sankuai.general.product.query.center.client.dto.deal.rule.readjustPriceRule.standardPriceRule.StandardPriceRuleItemDTO;
import com.sankuai.general.product.query.center.client.enums.TimeStockStatusEnum;
import com.sankuai.general.product.query.center.client.util.DealGroupUtils;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.PriceProtectionTagDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.HospitalInfo;
import com.sankuai.swan.udqs.api.QueryData;
import com.sankuai.swan.udqs.api.Result;
import com.sankuai.swan.udqs.api.SwanParam;
import com.sankuai.swan.udqs.api.SwanQueryService;
import com.sankuai.trade.general.reserve.response.ReserveResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLDecoder;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Future;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.Cons.WARM_UP_DEAL;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys.USING_STOCK_PLAN;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys.WARM_UP_START_TIME;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.PlusIcons.SECOND_KILL_MEDIATE_PROMO;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.PlusIcons.SECOND_KILL_UP_PROMO;
import static com.dianping.mobile.mapi.dztgdetail.common.enums.StyleTypeEnum.SHOPPING_CART;
import static com.sankuai.dealuser.price.display.api.enums.PricePowerTagEnum.*;

@Slf4j
@Deprecated
public class DealBuilderProcessor extends AbsDealProcessor {

    private static final String DEFAULTPICASPECTRATIO = "default";
    private static final String PIC_WIDTH = "width";
    private static final String PIC_HEIGHT = "height";
    private static final Integer SWAN_QUERY_BIZ_TYPE_ID = 1011;
    private static final String SWAN_QUERY_BIZ_KEY_BY_DATE = "batch_query_for_productprice_tjck_by_date";
    private static final String SWAN_QUERY_BIZ_KEY = "batch_query_for_productprice_tjck";
    private static final String RESULT_MAP_KEY_PRICE_30D = "minSalesPrice30d";
    private static final String RESULT_MAP_KEY_PRICE_60D = "minSalesPrice60d";
    private static final String RESULT_MAP_KEY_PRICE_90D = "minSalesPrice90d";
    private static final Set<Integer> ORAL_TEETH_CATEGORY = Sets.newHashSet(506);
    private static final Set<Integer> PARENT_CHILD_FUN = Sets.newHashSet(1002);

    @Resource
    private SwanQueryService swanQueryService;

    @Autowired
    private DealActivityWrapper dealActivityWrapper;

    @Autowired
    private ReserveProductWrapper reserveProductWrapper;

    @Autowired
    private DouHuBiz douHuBiz;

    @Autowired
    private DealGroupWrapper dealGroupWrapper;

    @Autowired
    PoiClientWrapper poiClientWrapper;

    @Autowired
    MapperWrapper mapperWrapper;

    @Autowired
    private BookStatusGatewayService bookStatusGatewayService;

    @Autowired
    private PoiShopCategoryWrapper poiShopCategoryWrapper;

    @Resource
    private BizHourForecastService bizHourForecastService;

    @Resource
    private ButtonStyleHelper buttonStyleHelper;

    @Autowired
    private LeafRepository leafRepository;
    @Autowired
    private Map<String, HeaderPicProcessor> headerPicProcessorMap;
    @Resource
    private DouHuService douHuService;

    @Resource
    private DealCategoryFactory dealCategoryFactory;

    private Gson gson = new Gson();

    @Override
    public boolean isEnable(DealCtx ctx) {
        return ctx.getDealGroupBase() != null;
    }

    @Override
    public void prepare(DealCtx ctx) {
        if (ReserveProductWrapper.reserveAfterPurchase(ctx.getCategoryId())) {
            Future reserveProductFuture = reserveProductWrapper.prepareOnlineReserve(ctx);
            ctx.getFutureCtx().setJudgeDpIdReserveOnlineFuture(reserveProductFuture);
        }

        if (ctx.getCategoryId() == 401) {
            ctx.getFutureCtx().setDealGroupThirdPartyFuture(dealGroupWrapper.preDealGroupThirdParty(ctx.getDpId()));
        }

        if (ctx.getPoiBackCategoryIds() != null && ctx.getDpPoiDTO() != null) {
            if (ctx.getPoiBackCategoryIds().contains(450) || ctx.getPoiBackCategoryIds().contains(2580)) {
                ctx.setDpPoiDTO(poiClientWrapper.getDpPoiDTO(ctx.getDpLongShopId(), QueryParams.SINAI_DP_POI_FIELDS));
            }
        }
    }

    @Override
    public void process(DealCtx ctx) {
        DealGroupPBO result = ctx.getResult();

        // 处理侵权屏蔽商品
        result.setModuleConfigsModule(ctx.getModuleConfigsModule());
        if (AttributeUtils.isTort(ctx.getAttrs())) {
            AttributeUtils.logTortType(ctx.getAttrs());
            if (result.getModuleConfigsModule() == null || !result.getModuleConfigsModule().isTort()) {
                ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
                moduleConfigsModule.setTort(true);
                moduleConfigsModule.setTortText("当前团购已失效，请选择其他团购");
                moduleConfigsModule.setTortTitle("当前团购已失效");
                moduleConfigsModule.setTortDesc("请选择其他团购");

                result.setModuleConfigsModule(moduleConfigsModule);
            }
            return;
        }

        result.setDpId(ctx.getDpId());
        result.setMtId(ctx.getMtId());
        result.setDpDealId(NumberUtils.toInt(getDefaultSkuId(ctx)));
        result.setSkuId(getDefaultSkuId(ctx));
        ctx.setDpDealId(result.getDpDealId());
        result.setTitle(getTitle(ctx));
        result.setCategoryId(ctx.getCategoryId());
        result.setBgName(ctx.getChannelGroupName());
        result.setDealContents(getContent(ctx.getDealGroupBase(), ctx.isMt(), ctx, result));
        result.setShop(getShopPBO(ctx));
        result.setFeatures(getFeatures(ctx));
        result.setSpecialFeatures(getSpecialFeatures(ctx));
        result.setAbConfigModel(ctx.getModuleAbConfig());
        result.setSkuModule(ctx.getSkuModule());

        result.setModuleAbConfigs(getModuleAbConfigs(ctx));
        if (ctx.getSalesDisplayDTO() != null && ctx.getSalesDisplayDTO().getSales() > 0) {
            result.setSaleDesc(ctx.getSalesDisplayDTO().getSalesTag());
            result.setSaleDescStr(ctx.getSalesDisplayDTO().getSalesTag());
            //添加销量颜色AB实验
            executeSalesColorAbExp(ctx, result);
        } else {
            result.setPriorTag("新品");
        }

        if (ctx.getTimesCard() != null) {
            String timesCardId = String.valueOf(ctx.getTimesCard().getProductId());
            result.setTimesCard(new TimesCardPBO(timesCardId));
        }

        result.setMaxPerUser(ctx.getDealGroupBase().getMaxPerUser());

        //填充氛围条信息，设置售卖状态，要先于底bar的构造
        if (CollectionUtils.isNotEmpty(ctx.getDealExtraTypes()) && ctx.getDealExtraTypes().contains(WARM_UP_DEAL)) {
            buildDealAtmosphereBarAndSetSaleStatus(ctx, result);
        }

        buildBuyBar(ctx, result);
        buildBuyBarPricePostFix(ctx, result);
        buildPriceDisplayInfo(ctx, result);
        buildPromoDetailInfo(ctx, result);
        buildDealPromoDetailInfo(ctx, result);
        buildCardStylePromoDetailInfo(ctx, result);
        // 如果走到了团详改版的AB实验
        if (ctx.isEnableCardStyleV2()) {
            //构造团详改版须知信息
            buildReminder(ctx, result);
        }
        //精选标签
        result.setTitleTagIcon(buildSelectTag(ctx.getDpId(), ctx.isMt()));
        result.setChoicestIcon(buildChoicestIcon(ctx));
        putSmallHead(ctx);
        putExtraStyle(ctx);
        putPicAspectRatio(ctx, result);
        putOnlineConsult(result); //底部bar展示IM
        putOriginalOnlineConsult(result);
        putVoucherInfo(ctx, result); //酒吧茶馆代金券信息
        putDealName(ctx, result);
        filterBuyButtonEnable(ctx);
        putShareAble(ctx, result); //是否能分享

        // 是否展示预约浮层
        result.setHasReserveEntrance(ctx.isShowReserveEntrance());
        result.setShowNewReserveEntrance(ctx.isShowNewReserveEntrance());
        if (ctx.isShowNewReserveEntrance()) {
            result.setReserveRedirectUrl(ctx.getReserveRedirectUrl());
        }


        // 填充商户开卡状态，用户持卡状态
        buildCardState(ctx, result);

        result.setShareModule(ctx.getDztgShareModule());
        result.setMoreDealsModule(ctx.getDztgMoreDealModule());
        result.setAdModule(ctx.getDztgAdsModule());
        result.setStructedDetails(ctx.getStructedDetails());
        result.setHighlightsModule(ctx.getHighlightsModule());
        result.setSubTitleList(buildSubTitleList(ctx));

        //团详微信小程序未登录隐藏信息处理
        hideMtMiniAppInfo(ctx);

        // 无忧通不显示标签
        if (DealAttrHelper.isWuyoutong(ctx)) {
            result.setPriorTag(null);
        }

        //设置价保标签浮层
        result.setFeaturesLayer(getFeaturesLayer(ctx));
        //设置加项
        result.setAdditionalInfo(ctx.getAdditionalInfo());
        // 业务场景样式设置
        result.setBusinessStyle(ctx.getBusinessStyle());
    }

    /**
     * 构建副标题区
     *
     * @param ctx
     * @return
     */
    private List<SubTitleVO> buildSubTitleList(DealCtx ctx) {
        List<SubTitleVO> subTitleVOList = new ArrayList<>();
        // 过夜模块
        SubTitleVO overNightSubTitleVO = buildOverNightSubTitle(ctx);
        if (Objects.nonNull(overNightSubTitleVO)) {
            subTitleVOList.add(overNightSubTitleVO);
        }

        // 台球自助开台复用该字段
        if ( ctx.getAutoOpenTable() ) {
            subTitleVOList.add(buildBilliardsSubTitle(ctx));
        }

        return subTitleVOList;
    }

    private SubTitleVO buildBilliardsSubTitle(DealCtx ctx) {
        SubTitleVO subTitleVO = new SubTitleVO();
        subTitleVO.setTitle("服务");
        TitleItemVO titleItemVO = new TitleItemVO();
        titleItemVO.setText("支持购后一键开台");
        subTitleVO.setItemDTOs(com.google.common.collect.Lists.newArrayList(titleItemVO));
        return subTitleVO;
    }

    /**
     * 构建过夜副标题模块
     *
     * @param ctx
     * @return
     */
    private SubTitleVO buildOverNightSubTitle(DealCtx ctx) {
        // 限定足疗洗浴
        if (ctx.getCategoryId() != 303 && ctx.getCategoryId() != 304) {
            return null;
        }
        // 限定实验a组
        ModuleAbConfig overNightAbConfig = DealDouHuUtil.getOverNightDouHuSk(ctx);
        if (Objects.isNull(overNightAbConfig) || CollectionUtils.isEmpty(overNightAbConfig.getConfigs()) || Objects.isNull(overNightAbConfig.getConfigs().get(0))
                || !overNightAbConfig.getConfigs().get(0).getExpResult().contains("a")) {
            return null;
        }
        // 只有当 含有不免费的过夜服务时，才展示这个模块
        if (!hasNotFreeOverNightService(ctx)) {
            return null;
        }
        // 构建过夜服务模块
        SubTitleVO overNightSubTitleVO = new SubTitleVO();
        overNightSubTitleVO.setTitle("服务");
        overNightSubTitleVO.setJumpUrl(Objects.nonNull(ctx.getSkuModule()) ? ctx.getSkuModule().getUrl() : null);
        TitleItemVO titleItemVO = new TitleItemVO();
        titleItemVO.setText("过夜服务");
        titleItemVO.setPreIcon("https://p1.meituan.net/travelcube/140ba08104b3203b49b85edd4aa318612066.png");
        overNightSubTitleVO.setItemDTOs(Lists.newArrayList(titleItemVO));
        return overNightSubTitleVO;
    }

    private void buildReminder(DealCtx ctx, DealGroupPBO result) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.buildReminder(DealCtx,DealGroupPBO)");
        if (!ctx.isEnableCardStyleV2()) {
            return;
        }
        List<String> reminders = new ArrayList<>();
        // 预约信息
        reminders.addAll(getReservationInfo(ctx));
        // 使用时间限制，以天或者小时维度取值
        reminders.add(AvailableTimeHelper.getAvailableTime(ctx));
        // 有效期 X天内有效
        reminders.add(EffectiveDateHelper.getEffectiveDate(ctx));
        //过滤无效字符
        result.setReminderInfo(reminders.stream().filter(reminder -> reminder != null && !reminder.equals("")).collect(Collectors.toList()));
    }

    /**
     * 填充（秒杀）氛围条信息
     *
     * @param ctx
     * @param result
     */
    private void buildDealAtmosphereBarAndSetSaleStatus(DealCtx ctx, DealGroupPBO result) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.buildDealAtmosphereBarAndSetSaleStatus(DealCtx,DealGroupPBO)");
        if (result.getDealAtmosphereBarModules() == null) {
            result.setDealAtmosphereBarModules(Lists.newArrayList());
        }

        List<AttributeDTO> attrs = ctx.getAttrs();
        String warmUpValue = AttributeUtils.getFirstValue(attrs, WARM_UP_START_TIME);
        String usingStockPlanValue = AttributeUtils.getFirstValue(attrs, USING_STOCK_PLAN);

        DealAtmosphereBarModule dealAtmosphereBarModule = new DealAtmosphereBarModule();
        if (StringUtils.isNotBlank(warmUpValue) && StringUtils.isBlank(usingStockPlanValue)) {
            buildDealAtmosphereBarWarmUpOnly(ctx, warmUpValue, dealAtmosphereBarModule);
        }

        if (StringUtils.isBlank(warmUpValue) && StringUtils.isNotBlank(usingStockPlanValue)) {
            buildDealAtmosphereBarUsingStockPlanOnly(ctx, dealAtmosphereBarModule);
        }

        if (StringUtils.isNotBlank(warmUpValue) && StringUtils.isNotBlank(usingStockPlanValue)) {
            buildDealAtmosphereBarBothOn(ctx, warmUpValue, dealAtmosphereBarModule);
        }

        result.getDealAtmosphereBarModules().add(dealAtmosphereBarModule);
    }

    private void buildDealAtmosphereBarBothOn(DealCtx ctx, String warmUpValue, DealAtmosphereBarModule dealAtmosphereBarModule) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.buildDealAtmosphereBarBothOn(DealCtx,String,DealAtmosphereBarModule)");
        Date warmUpDate = DealGroupUtils.convertString2Date(warmUpValue);
        Date now = DateUtils.currentDate();
        Date beginDate = ctx.getDealGroupBase().getBeginDate();
        Long nextStartTime = ctx.getDealTimeStockDTO().getNextStartTime();
        if (beginDate.after(now) && !warmUpDate.after(now)) {
            dealAtmosphereBarModule.setScene(AtmosphereSceneEnum.SECOND_KILL.sceneName);
            dealAtmosphereBarModule.setTimeSubFix("后开抢");
            dealAtmosphereBarModule.setCountDownTs(nextStartTime);
            dealAtmosphereBarModule.setBaseMapUrl(SECOND_KILL_UP_PROMO);

            ctx.setSaleStatus(SaleStatusEnum.COMING_SOON.saleStatusName);
            return;
        }

        if (now.after(beginDate)) {
            buildDealAtmosphereBarUsingStockPlanOnly(ctx, dealAtmosphereBarModule);
        }
    }

    private void buildDealAtmosphereBarUsingStockPlanOnly(DealCtx ctx, DealAtmosphereBarModule dealAtmosphereBarModule) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.buildDealAtmosphereBarUsingStockPlanOnly(DealCtx,DealAtmosphereBarModule)");
        Date beginDate = ctx.getDealGroupBase().getBeginDate();
        Date now = DateUtils.currentDate();
        DealTimeStockDTO dealTimeStockDTO = ctx.getDealTimeStockDTO();

        if (TimeStockStatusEnum.NOT_DAILYTIMESTOCK_DEAL.getCode() == dealTimeStockDTO.getTimeStockStatus()) {
            return;
        }

        if (!beginDate.after(now) && TimeStockStatusEnum.NOT_STARTED_DAILYTIMESTOCK_DEAL.getCode() == dealTimeStockDTO.getTimeStockStatus()) {
            dealAtmosphereBarModule.setScene(AtmosphereSceneEnum.SECOND_KILL.sceneName);
            dealAtmosphereBarModule.setCountDownTs(dealTimeStockDTO.getNextStartTime());
            dealAtmosphereBarModule.setTimeSubFix("后开始");
            dealAtmosphereBarModule.setBaseMapUrl(SECOND_KILL_UP_PROMO);

            ctx.setSaleStatus(SaleStatusEnum.COMING_SOON.saleStatusName);
            return;
        }

        if (!beginDate.after(now) && TimeStockStatusEnum.IN_PROCESS_DAILYTIMESTOCK_DEAL.getCode() == dealTimeStockDTO.getTimeStockStatus()) {
            dealAtmosphereBarModule.setScene(AtmosphereSceneEnum.SECOND_KILL.sceneName);
            dealAtmosphereBarModule.setTimePreFix("距结束");
            dealAtmosphereBarModule.setCountDownTs(dealTimeStockDTO.getCurrentEndTime());
            dealAtmosphereBarModule.setBaseMapUrl(SECOND_KILL_UP_PROMO);

            ctx.setSaleStatus(SaleStatusEnum.SNAP_UP_NOW.saleStatusName);
            return;
        }

        if (!beginDate.after(now) && TimeStockStatusEnum.CURRENT_TIME_END_DAILYTIMESTOCK_DEAL.getCode() == dealTimeStockDTO.getTimeStockStatus()) {
            dealAtmosphereBarModule.setScene(AtmosphereSceneEnum.SECOND_KILL.sceneName);
            dealAtmosphereBarModule.setTimeSubFix("后下一场");
            dealAtmosphereBarModule.setCountDownTs(dealTimeStockDTO.getNextStartTime());
            dealAtmosphereBarModule.setBaseMapUrl(SECOND_KILL_UP_PROMO);

            ctx.setSaleStatus(SaleStatusEnum.SOLD_OUT.saleStatusName);
            return;
        }

        if (!beginDate.after(now) && TimeStockStatusEnum.CURRENT_STOCK_END_DAILYTIMESTOCK_DEAL.getCode() == dealTimeStockDTO.getTimeStockStatus()) {
            dealAtmosphereBarModule.setScene(AtmosphereSceneEnum.SECOND_KILL.sceneName);
            dealAtmosphereBarModule.setTimeSubFix("后下一场");
            dealAtmosphereBarModule.setCountDownTs(dealTimeStockDTO.getNextStartTime());
            dealAtmosphereBarModule.setBaseMapUrl(SECOND_KILL_UP_PROMO);

            ctx.setSaleStatus(SaleStatusEnum.SOLD_OUT.saleStatusName);
            return;
        }

        if (!beginDate.after(now) && TimeStockStatusEnum.NO_NEXT_DAILYTIMESTOCK_DEAL.getCode() == dealTimeStockDTO.getTimeStockStatus()) {
            dealAtmosphereBarModule.setScene(AtmosphereSceneEnum.SECOND_KILL.sceneName);
            dealAtmosphereBarModule.setBaseMapUrl(SECOND_KILL_MEDIATE_PROMO);

            ctx.setSaleStatus(SaleStatusEnum.END_OF_SALE.saleStatusName);
            return;
        }

        if (!beginDate.after(now) && TimeStockStatusEnum.NO_STOCK_DEAL.getCode() == dealTimeStockDTO.getTimeStockStatus()) {
            dealAtmosphereBarModule.setScene(AtmosphereSceneEnum.SECOND_KILL.sceneName);
            dealAtmosphereBarModule.setBaseMapUrl(SECOND_KILL_MEDIATE_PROMO);

            ctx.setSaleStatus(SaleStatusEnum.END_OF_SALE.saleStatusName);
        }

    }

    /**
     * 判断商品是否为过夜商品，true 是过夜商品
     *
     * @param ctx
     * @return
     */
    private boolean hasNotFreeOverNightService(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.hasNotFreeOverNightService(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        if (isInvalidContext(ctx)) {
            return false;
        }

        for (DealGroupDealDTO dealDTO : ctx.getDealGroupDTO().getDeals()) {
            if (hasNotFreeOverNightServiceInDeal(dealDTO)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 连续判断过夜商品
     *
     * @param ctx
     * @return
     */
    private boolean isInvalidContext(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.isInvalidContext(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        return Objects.isNull(ctx) || Objects.isNull(ctx.getDealGroupDTO()) || (ctx.getCategoryId() != 303 && ctx.getCategoryId() != 304);
    }

    /**
     * 连续判断过夜商品
     *
     * @param dealDTO
     * @return
     */
    private boolean hasNotFreeOverNightServiceInDeal(DealGroupDealDTO dealDTO) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.hasNotFreeOverNightServiceInDeal(com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO)");
        if (Objects.isNull(dealDTO) || Objects.isNull(dealDTO.getRule())) {
            return false;
        }

        for (ReadjustPriceRuleDTO readjustPriceRuleDTO : dealDTO.getRule().getReadjustPriceRules()) {
            if (hasNotFreeOverNightServiceInRule(readjustPriceRuleDTO)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 连续判断过夜商品
     *
     * @param readjustPriceRuleDTO
     * @return
     */
    private boolean hasNotFreeOverNightServiceInRule(ReadjustPriceRuleDTO readjustPriceRuleDTO) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.hasNotFreeOverNightServiceInRule(ReadjustPriceRuleDTO)");
        if (Objects.isNull(readjustPriceRuleDTO) || Objects.isNull(readjustPriceRuleDTO.getStandardPriceRule()) || CollectionUtils.isEmpty(readjustPriceRuleDTO.getStandardPriceRule().getStandardPriceRuleItems())) {
            return false;
        }

        for (StandardPriceRuleItemDTO itemDTO : readjustPriceRuleDTO.getStandardPriceRule().getStandardPriceRuleItems()) {
            if (isNotFreeOverNightServiceItem(itemDTO)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 连续判断过夜商品
     *
     * @param itemDTO
     * @return
     */
    private boolean isNotFreeOverNightServiceItem(StandardPriceRuleItemDTO itemDTO) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.isNotFreeOverNightServiceItem(StandardPriceRuleItemDTO)");
        return Objects.nonNull(itemDTO) && Objects.nonNull(itemDTO.getIdentityKey()) && itemDTO.getIdentityKey().equals("overNightService")
                && MapUtils.isNotEmpty(itemDTO.getAttributeValue()) && !Boolean.parseBoolean(itemDTO.getAttributeValue().get("free"));
    }

    private void buildDealAtmosphereBarWarmUpOnly(DealCtx ctx, String warmUpValue, DealAtmosphereBarModule dealAtmosphereBarModule) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.buildDealAtmosphereBarWarmUpOnly(DealCtx,String,DealAtmosphereBarModule)");
        Date warmUpDate = DealGroupUtils.convertString2Date(warmUpValue);
        Date now = DateUtils.currentDate();
        Date beginDate = ctx.getDealGroupBase().getBeginDate();
        Date endDate = ctx.getDealGroupBase().getEndDate();
        if (beginDate.after(now) && !warmUpDate.after(now)) {
            dealAtmosphereBarModule.setScene(AtmosphereSceneEnum.SECOND_KILL.sceneName);
            dealAtmosphereBarModule.setCountDownTs(beginDate.getTime());
            dealAtmosphereBarModule.setTimeSubFix("后开抢");
            dealAtmosphereBarModule.setBaseMapUrl(SECOND_KILL_UP_PROMO);

            ctx.setSaleStatus(SaleStatusEnum.COMING_SOON.saleStatusName);
            return;
        }

        if (!now.before(beginDate)) {
            dealAtmosphereBarModule.setScene(AtmosphereSceneEnum.SECOND_KILL.sceneName);
            dealAtmosphereBarModule.setTimePreFix("距结束");
            dealAtmosphereBarModule.setCountDownTs(endDate.getTime());
            dealAtmosphereBarModule.setBaseMapUrl(SECOND_KILL_UP_PROMO);

            ctx.setSaleStatus(SaleStatusEnum.SNAP_UP_NOW.saleStatusName);
        }
    }

    private List<ModuleAbConfig> getModuleAbConfigs(DealCtx ctx) {
        List<ModuleAbConfig> moduleAbConfigs = ctx.getModuleAbConfigs();
        if (moduleAbConfigs == null) {
            moduleAbConfigs = new ArrayList<>();
        }
        if (ctx.getCategoryId() == 303) {
            ModuleAbConfig abConfig = getZuliaoAbConfig(ctx);
            if (abConfig != null) {
                moduleAbConfigs.add(abConfig);
            }
        }

        ModuleAbConfig shoppingCartAbConfig = getShoppingCartAbConfig(ctx);
        if (shoppingCartAbConfig != null) {
            moduleAbConfigs.add(shoppingCartAbConfig);
        }

        ModuleAbConfig shoppingCartNewAbConfig = getShoppingCartNewAbConfig(ctx);
        if (shoppingCartNewAbConfig != null) {
            moduleAbConfigs.add(shoppingCartNewAbConfig);
        }

        ModuleAbConfig couponBarConfig = getCouponBarAbcConfig(ctx);
        if (couponBarConfig != null) {
            moduleAbConfigs.add(couponBarConfig);
        }

        ModuleAbConfig petStyleConfig = getPetNewStyleAbConfig(ctx);
        if (petStyleConfig != null) {
            moduleAbConfigs.add(petStyleConfig);
        }

        ModuleAbConfig cardStyleAbConfig = ctx.getCardStyleAbConfig();
        if (cardStyleAbConfig != null) {
            moduleAbConfigs.add(cardStyleAbConfig);
        }

        ModuleAbConfig cardStyleAbV2Config = ctx.getCardStyleAbV2Config();
        if (Objects.nonNull(cardStyleAbV2Config)) {
            moduleAbConfigs.add(cardStyleAbV2Config);
        }

        ModuleAbConfig overNightAbConfig = DealDouHuUtil.getOverNightDouHuSk(ctx);
        if (Objects.nonNull(overNightAbConfig)) {
            moduleAbConfigs.add(overNightAbConfig);
        }

        if (ctx.getSkuCtx() != null && ctx.getSkuCtx().getDealCreatOrderAbConfig() != null) {
            moduleAbConfigs.add(ctx.getSkuCtx().getDealCreatOrderAbConfig());
        }

        ModuleAbConfig moduleAbConfig = dealCategoryFactory.getModuleAbConfig(ctx.getEnvCtx(), (long) ctx.getCategoryId());
        if (moduleAbConfig != null){
            moduleAbConfigs.add(moduleAbConfig);
        }

        return moduleAbConfigs;
    }

    private ModuleAbConfig getShoppingCartAbConfig(DealCtx ctx) {
        return douHuBiz.getAbByUnionId(ctx.getEnvCtx().getUnionId(), "MTShoppingCartBuyBar", ctx.isMt());
    }

    private ModuleAbConfig getShoppingCartNewAbConfig(DealCtx ctx) {
        return douHuBiz.getAbByUnionId(ctx.getEnvCtx().getUnionId(), "MTShoppingCartBuyBarNew", ctx.isMt());
    }

    private ModuleAbConfig getCouponBarAbcConfig(DealCtx ctx) {
        String module = ctx.isMt() ? "MTCouponBar" : "DPCouponBar";
        return douHuBiz.getAbcByUnionId(ctx.getEnvCtx().getUnionId(), module, ctx.isMt());
    }

    private ModuleAbConfig getPetNewStyleAbConfig(DealCtx ctx) {
        if (ctx.getCategoryId() == 1701 || ctx.getCategoryId() == 1702){
            String module = ctx.isMt() ? "MTPetNewStyle" : "DPPetNewStyle";
            return douHuBiz.getAbcByUnionId(ctx.getEnvCtx().getUnionId(), module, ctx.isMt());
        }
        return null;
    }

    // 足疗货架团购按钮直跳下单页ab测试
    private ModuleAbConfig getZuliaoAbConfig(DealCtx ctx) {
        String module = ctx.isMt() ? "MTZuLiaoHuoJiaDirectPurchase" : "DPZuLiaoHuoJiaDirectPurchase";
        return douHuBiz.getAbByUnionId(ctx.getEnvCtx().getUnionId(), module, ctx.isMt());
    }

    private void putExtraStyle(DealCtx ctx) {
        if (CollectionUtils.isNotEmpty(ctx.getDealExtraTypes())) {
            if (ctx.getResult().getExtraStyles() == null) {
                ctx.getResult().setExtraStyles(Lists.newArrayList());
            }
            ctx.getResult().getExtraStyles().addAll(ctx.getDealExtraTypes());
        }
    }

    private void executeSalesColorAbExp(DealCtx ctx, DealGroupPBO result) {
        if (ctx == null) {
            return;
        }
        List<Integer> categoryList = Lion.getList(LionConstants.AB_EXP_CATEGORY_IDS, Integer.class, Lists.newArrayList(303, 304));
        if (!categoryList.contains(ctx.getCategoryId())) {
            return;
        }
        //目前AB实验只做洗浴和足疗两个类目
        String module = ctx.isMt() ? "MTSalesColorExp" : "DPSalesColorExp";
        ModuleAbConfig moduleAbConfig = douHuBiz.getAbExpResult(ctx, module);
        String saleDesc = result.getSaleDesc();
        if (moduleAbConfig != null && moduleAbConfig.getConfigs().get(0).isUseNewStyle()) {
            result.setSaleDesc(JsonLabelUtil.salesColorJson(ctx.isMt(), saleDesc, saleDesc));
        }
        result.setAbConfigModel(moduleAbConfig);
    }


    /**
     * 构造价格展示相关数据
     *
     * @param ctx
     * @param result
     */
    private void buildPriceDisplayInfo(DealCtx ctx, DealGroupPBO result) {
        PriceContext priceContext = ctx.getPriceContext();
        PriceDisplayDTO normalPrice = priceContext.getNormalPrice();
        PriceDisplayModuleDo priceDisplayModuleDo = new PriceDisplayModuleDo();

        BigDecimal dealGroupPrice = ctx.getDealGroupBase().getDealGroupPrice();
        BigDecimal marketPrice = ctx.getDealGroupBase().getMarketPrice();
        String dealGroupPriceStr = NumberFormat.getInstance().format(dealGroupPrice);
        String marketPriceStr = NumberFormat.getInstance().format(marketPrice);

        priceDisplayModuleDo.setDealGroupPrice(dealGroupPriceStr);
        priceDisplayModuleDo.setMarketPrice(marketPriceStr);
        priceDisplayModuleDo.setEnableDisplay(isDrivingSchoolCar(ctx));

        if (normalPrice != null) {
            priceDisplayModuleDo.setPrice(normalPrice.getPrice().stripTrailingZeros().toPlainString());
        } else {
            priceDisplayModuleDo.setPrice(dealGroupPriceStr);
        }

        if (normalPrice != null && CollectionUtils.isNotEmpty(normalPrice.getUsedPromos())) {
            result.setDisplayPriceDesc("团购价");
            result.setDisplayPrice(dealGroupPriceStr);
            BigDecimal promoPrice = normalPrice.getPrice();
            priceDisplayModuleDo.setPromoPrice("减后价 ￥" + NumberFormat.getInstance().format(promoPrice));
            priceDisplayModuleDo.setPromoTag(StringUtils.isNotBlank(normalPrice.getPromoTag()) ? normalPrice.getPromoTag() : normalPrice.getUsedPromos().get(0).getTag());
        } else {
            result.setDisplayPriceDesc("门市价");
            result.setDisplayPrice(NumberFormat.getInstance().format(marketPrice));
        }

        result.setPriceDisplayModuleDo(priceDisplayModuleDo);
    }

    private void buildPromoDetailInfo(DealCtx ctx, DealGroupPBO result) {
        try {
            PriceContext priceContext = ctx.getPriceContext();
            if (priceContext == null) {
                return;
            }
            if (DealCtxHelper.isOdpSource(ctx)) {
                buildOdpPromoDetailInfo(ctx, priceContext.getNormalPrice(), result);
            } else {
                buildNormalPromoDetailInfo(ctx, priceContext.getNormalPrice(), result);
                if (priceContext.getAtmosphereBarAndGeneralPromoDetailPrice() != null) {
                    buildAtmosphereBarAndGeneralPromoDetailInfo(ctx, priceContext.getAtmosphereBarAndGeneralPromoDetailPrice(), result);
                }
            }
        } catch (Exception e) {
            logger.error("buildPromoDetailInfo error", e);
        }
    }

    private void buildDealPromoDetailInfo(DealCtx ctx, DealGroupPBO result) {
        try {
            PriceContext priceContext = ctx.getPriceContext();
            if (priceContext == null) {
                return;
            }
            buildDealPromoDetailInfo(ctx, priceContext.getDealPromoPrice(), result);
        } catch (Exception e) {
            logger.error("buildPromoDetailInfo error", e);
        }
    }

    private void buildNormalPromoDetailInfo(DealCtx ctx, PriceDisplayDTO normalPrice, DealGroupPBO result) {
        if (normalPrice == null) {
            return;
        }
        if (ctx.isExternal() && ctx.getCategoryId() != 712) {
            return;
        }

        PromoDetailModule promoDetailModule = new PromoDetailModule();
        BigDecimal dealGroupPrice = ctx.getDealGroupBase().getDealGroupPrice();
        BigDecimal marketPrice = ctx.getDealGroupBase().getMarketPrice();
        //没有商家优惠时全网低价就是团单价
        BigDecimal networkLowestPrice = dealGroupPrice;
        String dealGroupPriceStr = PriceHelper.formatPrice(dealGroupPrice);
        String marketPriceStr = PriceHelper.formatPrice(marketPrice);

        promoDetailModule.setDealGroupPrice(dealGroupPriceStr);
        promoDetailModule.setMarketPrice(marketPriceStr);
        promoDetailModule.setPreSaleDealGroupPrice(getPreSaleDealGroupPrice(ctx));

        BigDecimal promoPrice = normalPrice.getPrice();
        promoDetailModule.setPromoPrice(PriceHelper.formatPrice(promoPrice));


        if (CollectionUtils.isNotEmpty(normalPrice.getUsedPromos())) {
            BigDecimal couponPromo = new BigDecimal(0);
            BigDecimal reductionPromo = new BigDecimal(0);
            if (normalPrice.getUsedPromos() != null) {
                List<PromoDTO> promoDTOList = normalPrice.getUsedPromos();
                for (PromoDTO promoDTO : promoDTOList) {
                    if (promoDTO == null || promoDTO.getAmount() == null) {
                        continue;
                    }
                    if (promoDTO.getIdentity().getPromoType() == PromoTypeEnum.NORMAL_PROMO.getType()) {
                        reductionPromo = reductionPromo.add(promoDTO.getAmount());
                    } else if (promoDTO.getIdentity().getPromoType() == PromoTypeEnum.IDLE_PROMO.getType()) {
                        reductionPromo = reductionPromo.add(promoDTO.getAmount());
                    } else if (promoDTO.getIdentity().getPromoType() == PromoTypeEnum.THRESHOLD.getType()) {
                        reductionPromo = reductionPromo.add(promoDTO.getAmount());
                    } else {
                        couponPromo = couponPromo.add(promoDTO.getAmount());
                    }
                }
            }
            promoDetailModule.setCouponPromo(PriceHelper.formatPrice(couponPromo));
            promoDetailModule.setReductionPromo(PriceHelper.formatPrice(reductionPromo));
        }

        if (CollectionUtils.isNotEmpty(normalPrice.getExtPrices())) {
            for (ExtPriceDisplayDTO extPriceDisplay : normalPrice.getExtPrices()) {
                if (extPriceDisplay != null && extPriceDisplay.getExtPriceType() == ExtPriceTypeEnum.LowestPrice_In_NetWork.getType()) {
                    networkLowestPrice = extPriceDisplay.getExtPrice();
                }
            }
        }

        //逻辑详见：https://km.sankuai.com/collabpage/1491774972
        if (promoPrice != null && networkLowestPrice != null && networkLowestPrice.compareTo(promoPrice) > 0) {
            promoDetailModule.setNetworkLowestPrice(PriceHelper.formatPrice(networkLowestPrice));
        }

        if (dealGroupPrice != null && promoPrice != null && dealGroupPrice.compareTo(promoPrice) > 0) {
            promoDetailModule.setTotalPromo(PriceHelper.formatPrice(dealGroupPrice.subtract(promoPrice)));
        }

        if (StringUtils.isNotEmpty(promoDetailModule.getPreSaleDealGroupPrice()) && promoPrice != null) {
            BigDecimal preSaleDealGroupPrice = new BigDecimal(promoDetailModule.getPreSaleDealGroupPrice());
            if (preSaleDealGroupPrice != null && preSaleDealGroupPrice.compareTo(promoPrice) > 0) {
                promoDetailModule.setPresalePromo(PriceHelper.formatPrice(preSaleDealGroupPrice.subtract(promoPrice)));
            }
        }

        if (marketPrice != null && promoPrice != null && marketPrice.compareTo(promoPrice) > 0) {
            promoDetailModule.setMarketPricePromo(PriceHelper.formatPrice(marketPrice.subtract(promoPrice)));
        }

        result.setPromoDetailModule(promoDetailModule);
    }

    private List<String> buildPromoAbstractList(PromoDetailModule promoDetailModule, DealCtx dealCtx) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.buildPromoAbstractList(PromoDetailModule,DealCtx)");
        try {
            if (promoDetailModule == null) {
                return null;
            }
            List<String> promoAbstractListBest = new ArrayList<>();
            // 最佳优惠
            if (CollectionUtils.isNotEmpty(promoDetailModule.getBestPromoDetails())) {
                for (DealBestPromoDetail bestPromoDetail : promoDetailModule.getBestPromoDetails()) {
                    if (bestPromoDetail == null || StringUtils.isBlank(bestPromoDetail.getPromoName())) {
                        continue;
                    }
                    promoAbstractListBest.add(bestPromoDetail.getPromoName());
                }
            } else if (dealCtx != null && dealCtx.getPriceContext() != null && dealCtx.getPriceContext().getNormalPrice() != null) {
                // 兜底用 normalPrice
                List<PromoDTO> usedPromos = dealCtx.getPriceContext().getNormalPrice().getUsedPromos();
                for (PromoDTO promoDTO : usedPromos) {
                    if (promoDTO.getIdentity() == null || promoDTO.getAmount() == null || StringUtils.isEmpty(promoDTO.getIdentity().getPromoTypeDesc())) {
                        continue;
                    }
                    promoAbstractListBest.add(promoDTO.getIdentity().getPromoTypeDesc());
                }
            }
            // 最佳优惠排序
            List<String> sortedPromoAbstractListBest = promoAbstractListBest.stream()
                    .filter(StringUtils::isNotBlank)
                    .sorted((promo1, promo2) -> {
                        List<String> promoOrder = Arrays.asList("会员", "补贴", "新客", "特惠", "券", "减", "团购优惠", "工作日", "人团", "返");
                        int index1 = promoOrder.stream().filter(promo1::contains).findFirst().map(promoOrder::indexOf).orElse(Integer.MAX_VALUE);
                        int index2 = promoOrder.stream().filter(promo2::contains).findFirst().map(promoOrder::indexOf).orElse(Integer.MAX_VALUE);
                        return Integer.compare(index1, index2);
                    })
                    .collect(Collectors.toList());
            // 活动
            List<String> activities = new ArrayList<>();
            if (promoDetailModule.getPromoActivityList() != null) {
                for (PromoActivityInfoVO activityInfoVO : promoDetailModule.getPromoActivityList()) {
                    if (activityInfoVO == null || StringUtils.isBlank(activityInfoVO.getShortText())) {
                        continue;
                    }
                    activities.add(activityInfoVO.getShortText());
                }
            }

            // 活动排序
            List<String> sortedActivities = activities.stream()
                    .filter(StringUtils::isNotBlank)
                    .sorted((promo1, promo2) -> {
                        List<String> promoOrder = Arrays.asList("会员", "补贴", "新客", "特惠", "券", "减", "团购优惠", "工作日", "人团", "返");
                        int index1 = promoOrder.stream().filter(promo1::contains).findFirst().map(promoOrder::indexOf).orElse(Integer.MAX_VALUE);
                        int index2 = promoOrder.stream().filter(promo2::contains).findFirst().map(promoOrder::indexOf).orElse(Integer.MAX_VALUE);
                        return Integer.compare(index1, index2);
                    })
                    .collect(Collectors.toList());

            // 合并sortedPromoAbstractListBest和sortedActivities
            List<String> result = new ArrayList<>();
            result.addAll(sortedPromoAbstractListBest);
            result.addAll(sortedActivities);

            return result;
        } catch (Exception e) {
            log.error("buildPromoAbstractList error,", e);
        }
        return null;
    }

    private List<DztgPromoExposureInfoVO> buildPromoExposureInfo(DealCtx dealCtx) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.buildPromoExposureInfo(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        try {
            BatchExProxyCouponResponseDTO batchExProxyCouponResponseDTO = dealCtx.getBatchExProxyCouponResponseDTO();
            if (batchExProxyCouponResponseDTO == null || batchExProxyCouponResponseDTO.getPromoExposureInfoList() == null) {
                return new ArrayList<>();
            }
            List<PromoExposureInfo> promoExposureInfoList = batchExProxyCouponResponseDTO.getPromoExposureInfoList();

            return promoExposureInfoList.stream()
                    .filter(Objects::nonNull)
                    .map(exposure -> {
                        DztgPromoExposureInfoVO dztgPromoExposureInfoVO = new DztgPromoExposureInfoVO();
                        dztgPromoExposureInfoVO.setFlowId(exposure.getFlowId());
                        dztgPromoExposureInfoVO.setResourceActivityId(exposure.getResourceActivityId());
                        dztgPromoExposureInfoVO.setActivityId(exposure.getActivityId());
                        dztgPromoExposureInfoVO.setMaterialId(exposure.getMaterialId());
                        dztgPromoExposureInfoVO.setRowKey(exposure.getRowKey());
                        dztgPromoExposureInfoVO.setCouponType(exposure.getCouponType());
                        dztgPromoExposureInfoVO.setAmount(exposure.getAmount());
                        dztgPromoExposureInfoVO.setTitle(exposure.getTitle());
                        dztgPromoExposureInfoVO.setCouponValueType(exposure.getCouponValueType());
                        dztgPromoExposureInfoVO.setSubTitle(exposure.getSubTitle());
                        dztgPromoExposureInfoVO.setCanAssign(exposure.getCanAssign());
                        dztgPromoExposureInfoVO.setTimeDesc(exposure.getTimeDesc());
                        dztgPromoExposureInfoVO.setTimeSubDesc(exposure.getTimeSubDesc());
                        dztgPromoExposureInfoVO.setUseEndTime(exposure.getUseEndTime());

                        return dztgPromoExposureInfoVO;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("buildPromoExposureInfo error,", e);
        }
        return null;
    }

    private List<DztgCouponInfo> buildCouponList(DealCtx dealCtx) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.buildCouponList(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        try {
            BatchExProxyCouponResponseDTO batchExProxyCouponResponseDTO = dealCtx.getBatchExProxyCouponResponseDTO();
            // 从优惠券团队拿到的券信息
            List<DztgCouponInfo> couponList = new ArrayList<>();
            if (batchExProxyCouponResponseDTO != null) {
                List<PromoCouponInfo> promoCouponInfoList = batchExProxyCouponResponseDTO.getPromoCouponInfoList();
                List<PromoCouponInfo> financialCouponInfoList = batchExProxyCouponResponseDTO.getFinancialCouponInfoList();

                // 从优惠券团队拿到的券信息
                couponList = Stream.of(promoCouponInfoList, financialCouponInfoList)
                        .filter(Objects::nonNull)
                        .flatMap(List::stream)
                        .map(this::convertPromoCouponInfo2DztgCouponInfo)
                        .collect(Collectors.toList());
            }
            List<Long> couponGroupIdList = couponList.stream().map(c -> c.getCouponGroupId()).collect(Collectors.toList());

            // 从优惠代理服务拿到的券信息，两者取并集，如果重复，以优惠券团队为准
            List<DztgCouponInfo> extraCoupon = new ArrayList<>();
            if (dealCtx.getPriceContext() != null) {
                PriceDisplayDTO normalPrice = dealCtx.getPriceContext().getNormalPrice();
                if (normalPrice != null) {
                    List<PromoDTO> usedPromos = normalPrice.getUsedPromos();
                    if (usedPromos != null) {
                        extraCoupon = usedPromos.stream()
                                .filter(this::isCoupon)
                                .map(this::convertPromoDTO2DztgCouponInfo)
                                .filter(Objects::nonNull)
                                .filter(dztgCouponInfo -> !couponGroupIdList.contains(dztgCouponInfo.getCouponGroupId()))
                                .collect(Collectors.toList());
                    }
                }
            }

            // 合并couponList和extraCoupon，如果couponGroupId重复，只保留couponList中的
            List<DztgCouponInfo> result = Stream.concat(couponList.stream(), extraCoupon.stream())
                    .collect(Collectors.toList());
            result.sort(Comparator.comparing(DztgCouponInfo::getStatus)
                    .thenComparing((a, b) -> {
                        String aAmountCornerMark = a.getAmountCornerMark();
                        String bAmountCornerMark = b.getAmountCornerMark();
                        if ("元".equals(aAmountCornerMark) && "折".equals(bAmountCornerMark)) {
                            return -1;
                        } else if ("折".equals(aAmountCornerMark) && "元".equals(bAmountCornerMark)) {
                            return 1;
                        } else {
                            return 0;
                        }
                    })
                    .thenComparing(DztgCouponInfo::getSourceTag)
                    .thenComparing((a, b) -> {
                        double aAmount = Double.parseDouble(a.getAmount());
                        double bAmount = Double.parseDouble(b.getAmount());
                        return Double.compare(bAmount, aAmount);
                    }));

            return result;
        } catch (Exception e) {
            log.error("buildCouponList error, ", e);
        }
        return null;
    }

    private boolean isCoupon(PromoDTO promoDTO) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.isCoupon(com.sankuai.dealuser.price.display.api.model.PromoDTO)");
        if (promoDTO == null) {
            return false;
        }
        if (promoDTO.getIdentity() == null) {
            return false;
        }
        if (Objects.equals(promoDTO.getIdentity().getPromoType(), PromoTypeEnum.COUPON.getType()) && promoDTO.getIdentity().getPromoId() != 0) {
            return true;
        }
        if (Objects.equals(promoDTO.getIdentity().getPromoType(), PromoTypeEnum.GOVERNMENT_CONSUME_COUPON.getType())
                && org.apache.commons.lang3.StringUtils.isNumeric(promoDTO.getCouponGroupId())) {
            return true;
        }
        return false;
    }

    private DztgCouponInfo convertPromoCouponInfo2DztgCouponInfo(PromoCouponInfo coupon) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.convertPromoCouponInfo2DztgCouponInfo(com.dianping.tgc.open.entity.PromoCouponInfo)");
        if (coupon == null) {
            return null;
        }
        DztgCouponInfo dztgCouponInfo = new DztgCouponInfo();
        dztgCouponInfo.setCouponGroupId(coupon.getCouponGroupId());
        dztgCouponInfo.setTitle(coupon.getTitle());
        dztgCouponInfo.setAmount(coupon.getAmount());
        dztgCouponInfo.setAmountCornerMark(coupon.getAmountCornerMark());
        dztgCouponInfo.setTimeDesc(coupon.getTimeDesc());
        dztgCouponInfo.setAmountDesc(coupon.getAmountDesc());
        dztgCouponInfo.setCouponType(coupon.getCouponType());
        dztgCouponInfo.setSourceTag(coupon.getSourceTag());
        dztgCouponInfo.setSpecificTag(coupon.getSpecificTag());
        dztgCouponInfo.setTagUrl(coupon.getTagUrl());
        dztgCouponInfo.setStatus(coupon.getStatus());
        dztgCouponInfo.setIconText(coupon.getIconText());
        dztgCouponInfo.setIconUrl(coupon.getIconUrl());
        dztgCouponInfo.setPromoCouponButton(buildDztgCouponButton(coupon.getPromoCouponButton()));

        return dztgCouponInfo;
    }

    private DztgCouponInfo convertPromoDTO2DztgCouponInfo(PromoDTO coupon) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.convertPromoDTO2DztgCouponInfo(com.sankuai.dealuser.price.display.api.model.PromoDTO)");
        if (coupon == null) {
            return null;
        }
        DztgCouponInfo dztgCouponInfo = new DztgCouponInfo();

        if (coupon.getIdentity() != null && Objects.equals(coupon.getIdentity().getPromoType(), PromoTypeEnum.COUPON.getType())) {
            dztgCouponInfo.setCouponGroupId(coupon.getIdentity().getPromoId());
            if ("MERCHANT_COUPON".equals(coupon.getIdentity().getPromoShowType())) {
                dztgCouponInfo.setCouponType(0);
            }
            if (coupon.isCanAssign()) {
                dztgCouponInfo.setStatus(0);
            } else {
                dztgCouponInfo.setStatus(1);
            }
        } else if (coupon.getIdentity() != null && Objects.equals(coupon.getIdentity().getPromoType(), PromoTypeEnum.GOVERNMENT_CONSUME_COUPON.getType())) {
            if (org.apache.commons.lang3.StringUtils.isNumeric(coupon.getCouponGroupId())) {
                dztgCouponInfo.setCouponGroupId(Long.valueOf(coupon.getCouponGroupId()));
            }
            dztgCouponInfo.setCouponType(2);
            if (Objects.equals(CouponAssignStatusEnum.UN_ASSIGNED.getCode(), coupon.getCouponAssignStatus())) {
                dztgCouponInfo.setStatus(0);
                List<Long> leafIds = leafRepository.batchGenFinancialConsumeSerialId(1);
                if (CollectionUtils.isNotEmpty(leafIds)) {
                    dztgCouponInfo.setSerialno(String.valueOf(leafIds.get(0)));
                }
            } else {
                dztgCouponInfo.setStatus(1);
            }
        }

        dztgCouponInfo.setTitle(coupon.getExtendDesc());

        if (coupon.getAmount() != null) {
            dztgCouponInfo.setAmount(coupon.getCouponValueText());
        }
        dztgCouponInfo.setAmountCornerMark(coupon.getCouponValueType() == 4 ? "折" : "元");
        dztgCouponInfo.setTimeDesc(coupon.getUseTimeDesc());
        dztgCouponInfo.setAmountDesc(coupon.getPriceLimitDesc());

        dztgCouponInfo.setSourceTag(coupon.getIdentity() == null ? "优惠券" : convertSourceTag(coupon));

        dztgCouponInfo.setSpecificTag(null);
        //dztgCouponInfo.setTagUrl(coupon.getIcon());


//        dztgCouponInfo.setIconText(coupon.getIconText());
//        dztgCouponInfo.setIconUrl(coupon.getIconUrl());
//        dztgCouponInfo.setPromoCouponButton(buildDztgCouponButton(coupon.getPromoCouponButton()));
        if (dztgCouponInfo.getAmount() == null || dztgCouponInfo.getTitle() == null) {
            // 如果最终生成的coupon有问题，过滤掉，不展示
            return null;
        }

        return dztgCouponInfo;
    }

    private DztgCouponButton buildDztgCouponButton(PromoCouponButton promoCouponButton) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.buildDztgCouponButton(com.dianping.tgc.open.entity.PromoCouponButton)");
        if (promoCouponButton == null) {
            return null;
        }
        DztgCouponButton dztgCouponButton = new DztgCouponButton();
        dztgCouponButton.setTitle(promoCouponButton.getTitle());
        dztgCouponButton.setClickUrl(promoCouponButton.getClickUrl());
        dztgCouponButton.setActionType(promoCouponButton.getActionType());

        return dztgCouponButton;
    }

    private List<PromoActivityInfoVO> buildPromoActivityList(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.buildPromoActivityList(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        try {
            BatchExProxyCouponResponseDTO batchExProxyCouponResponseDTO = ctx.getBatchExProxyCouponResponseDTO();
            if (batchExProxyCouponResponseDTO == null || batchExProxyCouponResponseDTO.getPromoReturnInfoList() == null) {
                return new ArrayList<>();
            }
            List<PromoReturnInfo> returnInfoList = batchExProxyCouponResponseDTO.getPromoReturnInfoList();

            List<PromoActivityInfoVO> result = new ArrayList<>();

            // 闲时特惠
            PromoActivityInfoVO idlePromo = buildIdlePromo(ctx);
            if (idlePromo != null) {
                result.add(idlePromo);
            }

            // 商户多份立减
            PromoActivityInfoVO buyMoreReduction = buildBuyMoreReduction(ctx);
            if (buyMoreReduction != null) {
                result.add(buyMoreReduction);
            }

            // 平台多买多折
            PromoActivityInfoVO buyMoreDiscount = buildBuyMoreDiscount(ctx);
            if (buyMoreDiscount != null) {
                result.add(buyMoreDiscount);
            }

            // 拼团
            PromoActivityInfoVO pintuan = buildPintuanActivity(ctx);
            if (pintuan != null) {
                result.add(pintuan);
            }

            // 返券、返礼、金融券活动
            List<PromoActivityInfoVO> couponActivityList = returnInfoList.stream()
                    .filter(Objects::nonNull)
                    .map(returnInfo -> {
                        PromoActivityInfoVO promoActivityInfoVO = new PromoActivityInfoVO();
                        promoActivityInfoVO.setBonusType(returnInfo.getBonusType());
                        promoActivityInfoVO.setText(returnInfo.getText());
                        promoActivityInfoVO.setStyle(returnInfo.getStyle());
                        promoActivityInfoVO.setLeadUrl(returnInfo.getLeadUrl());
                        promoActivityInfoVO.setShortText(returnInfo.getText());

                        return promoActivityInfoVO;
                    })
                    .collect(Collectors.toList());

            result.addAll(couponActivityList);
            return result;
        } catch (Exception e) {
            log.error("buildPromoActivityList error,", e);
        }

        return null;
    }

    private PromoActivityInfoVO buildIdlePromo(DealCtx context) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.buildIdlePromo(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        try {
            PriceDisplayDTO promoPrice = context.getPriceContext().getIdlePromoPrice();
            if (promoPrice == null || CollectionUtils.isEmpty(promoPrice.getUsedPromos())) {
                return null;
            }

            PromoActivityInfoVO promoActivityInfoVO = new PromoActivityInfoVO();
            promoActivityInfoVO.setBonusType("限时");
            String text = promoPrice.getUsedPromos().get(0).getExtendDesc();
            if (StringUtils.isBlank(text)) {
                String consumeTimeDesc = promoPrice.getUsedPromos().get(0).getConsumeTimeDesc() == null ? "" : promoPrice.getUsedPromos().get(0).getConsumeTimeDesc();
                String price = PriceHelper.dropLastZero(promoPrice.getPrice());
                text = String.format("限时特惠，%s ¥%s/次", consumeTimeDesc, price);
            }
            promoActivityInfoVO.setText(text);

            promoActivityInfoVO.setStyle(0);
            promoActivityInfoVO.setLeadUrl(UrlHelper.getIdleHoursBuyUrl(context, context.getMtCityId()));
            String shortText = promoPrice.getUsedPromos().get(0).getIdentity() == null ? null : promoPrice.getUsedPromos().get(0).getIdentity().getPromoTypeDesc();
            if (StringUtils.isBlank(shortText)) {
                String promoAmount = PriceHelper.dropLastZero(promoPrice.getPromoAmount());
                shortText = String.format("工作日立减%s元", promoAmount);
            }
            promoActivityInfoVO.setShortText(shortText);

            return promoActivityInfoVO;
        } catch (Exception e) {
            log.error("buildIdlePromo error, ", e);
        }
        return null;
    }

    private PromoActivityInfoVO buildBuyMoreReduction(DealCtx context) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.buildBuyMoreReduction(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        try {
            PriceDisplayDTO price = context.getPriceContext().getNormalPrice();
            if (price == null || CollectionUtils.isEmpty(price.getMorePromos())) {
                return null;
            }

            PromoDTO buyMoreReduction = null;
            for (PromoDTO promoDTO : price.getMorePromos()) {
                if (promoDTO.getIdentity() != null && "BUY_MORE_REDUCTION".equals(promoDTO.getIdentity().getPromoShowType())) {
                    buyMoreReduction = promoDTO;
                    break;
                }
            }
            if (buyMoreReduction == null) {
                return null;
            }

            PromoActivityInfoVO promoActivityInfoVO = new PromoActivityInfoVO();
            promoActivityInfoVO.setBonusType("促销");
            promoActivityInfoVO.setText(buyMoreReduction.getExtendDesc());
            promoActivityInfoVO.setStyle(0);
            promoActivityInfoVO.setShortText(buyMoreReduction.getTag());

            return promoActivityInfoVO;
        } catch (Exception e) {
            log.error("buildBuyMoreReduction error, ", e);
        }
        return null;
    }

    private PromoActivityInfoVO buildBuyMoreDiscount(DealCtx context) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.buildBuyMoreDiscount(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        try {
            PriceDisplayDTO price = context.getPriceContext().getNormalPrice();
            if (price == null || CollectionUtils.isEmpty(price.getMorePromos())) {
                return null;
            }

            PromoDTO buyMoreDiscount = null;
            for (PromoDTO promoDTO : price.getMorePromos()) {
                if (promoDTO.getIdentity() != null && "BUY_MORE_DISCOUNT".equals(promoDTO.getIdentity().getPromoShowType())) {
                    buyMoreDiscount = promoDTO;
                    break;
                }
            }
            if (buyMoreDiscount == null) {
                return null;
            }

            PromoActivityInfoVO promoActivityInfoVO = new PromoActivityInfoVO();
            promoActivityInfoVO.setBonusType("促销");
            promoActivityInfoVO.setText(buyMoreDiscount.getExtendDesc());
            promoActivityInfoVO.setStyle(0);
            promoActivityInfoVO.setShortText(buyMoreDiscount.getTag());

            return promoActivityInfoVO;
        } catch (Exception e) {
            log.error("buildBuyMoreDiscount error, ", e);
        }
        return null;
    }

    private PromoActivityInfoVO buildPintuanActivity(DealCtx context) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.buildPintuanActivity(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        try {
            PinProductBrief pinProductBrief = context.getPinProductBrief();
            if (!BuyButtonHelper.isValidPinPool(context)) {
                return null;
            }

            Integer pinPersonNum = pinProductBrief.getPinPersonNum();
            String pinPriceStr = PriceHelper.dropLastZero(pinProductBrief.getPrice().setScale(2, RoundingMode.CEILING));
            String preStr = String.format("%s人团 ¥%s元/人，", pinPersonNum, pinPriceStr);

            String shortText = String.format("特惠%s人团", pinPersonNum);

            PriceDisplayDTO normalPrice = context.getPriceContext().getNormalPrice();
            if (normalPrice != null && normalPrice.getPrice() != null) {
                String minusPrice = normalPrice.getPrice().subtract(new BigDecimal(pinPriceStr))
                        .setScale(2, RoundingMode.CEILING).stripTrailingZeros().toPlainString();
                BigDecimal minusPriceDecimal = new BigDecimal(minusPrice);
                if (minusPriceDecimal.compareTo(BigDecimal.ZERO) < 0) {
                    // minusPrice是负数, 说明拼团价格比正常价格还高
                    return null;
                }
                String fullStr = preStr + "每人多省" + minusPrice + "元";

                PromoActivityInfoVO promoActivityInfoVO = new PromoActivityInfoVO();
                promoActivityInfoVO.setBonusType("拼团");
                promoActivityInfoVO.setText(fullStr);
                promoActivityInfoVO.setStyle(0);
                promoActivityInfoVO.setLeadUrl(UrlHelper.getAppUrl(context.getEnvCtx(), pinProductBrief.getUrl(), context.isMt()));
                promoActivityInfoVO.setShortText(shortText);

                return promoActivityInfoVO;
            }
            return null;
        } catch (Exception e) {
            log.error("buildPintuanActivity error, ", e);
        }
        return null;
    }

    /**
     * 广平分销场景优惠展示逻辑
     */
    private void buildOdpPromoDetailInfo(DealCtx ctx, PriceDisplayDTO odpPrice, DealGroupPBO result) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.buildOdpPromoDetailInfo(DealCtx,PriceDisplayDTO,DealGroupPBO)");
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        BigDecimal dealGroupPrice = ctx.getDealGroupBase().getDealGroupPrice();
        BigDecimal marketPrice = ctx.getDealGroupBase().getMarketPrice();
        BigDecimal promoPrice = odpPrice != null ? odpPrice.getPrice() : dealGroupPrice;

        String dealGroupPriceStr = PriceHelper.format(dealGroupPrice);
        String marketPriceStr = PriceHelper.format(ctx.getDealGroupBase().getMarketPrice());
        String promoPriceStr = PriceHelper.format(promoPrice);

        promoDetailModule.setDealGroupPrice(dealGroupPriceStr);
        promoDetailModule.setMarketPrice(marketPriceStr);
        promoDetailModule.setPromoPrice(promoPriceStr);
        promoDetailModule.setPreSaleDealGroupPrice(getPreSaleDealGroupPrice(ctx));

        boolean existReductionPromo = false;
        if (odpPrice != null && CollectionUtils.isNotEmpty(odpPrice.getUsedPromos())) {
            List<ReductionPromoDetail> reductionPromoDetails = Lists.newArrayList();
            BigDecimal totalReductionPromo = new BigDecimal(0);
            for (PromoDTO promoDTO : odpPrice.getUsedPromos()) {
                if (promoDTO == null || promoDTO.getIdentity() == null || promoDTO.getAmount() == null) {
                    continue;
                }
                if (promoDTO.getIdentity().getPromoType() == PromoTypeEnum.NORMAL_PROMO.getType()) {
                    int sourceType = promoDTO.getIdentity().getSourceType();
                    String reduceName = sourceType == 1 ? "平台补贴" : (sourceType == 2 ? "商家神券" : null);

                    ReductionPromoDetail couponPromoDetail = new ReductionPromoDetail();
                    couponPromoDetail.setReduceName(reduceName);
                    couponPromoDetail.setReducePromo(PriceHelper.format(promoDTO.getAmount()));

                    existReductionPromo = true;
                    reductionPromoDetails.add(couponPromoDetail);
                    totalReductionPromo = totalReductionPromo.add(promoDTO.getAmount());
                }
            }
            promoDetailModule.setReductionPromo(PriceHelper.format(totalReductionPromo));
            promoDetailModule.setReductionPromoDetails(reductionPromoDetails);
        }

        String discountRate = PriceHelper.calcDiscountRate(marketPrice, promoPrice);
        if (discountRate != null) {
            String suffix = ctx.isCanNotBuy() || !existReductionPromo ? "折" : "折惊爆价";
            promoDetailModule.setDiscountRate(discountRate);
            promoDetailModule.setDiscountRateDescription(discountRate + suffix);
        }
        result.setPromoDetailModule(promoDetailModule);
    }

    private String getPreSaleDealGroupPrice(DealCtx dealCtx) {
        if (!DealAttrHelper.isPreSale(dealCtx.getAttrs())) {
            return null;
        }
        AttributeDTO dealGroupPriceAttribute = dealCtx.getAttrs().stream()
                .filter(Objects::nonNull)
                .filter(attributeDTO -> DealAttrKeys.PRE_SALE_DEAL_GROUP_PRICE.equals(attributeDTO.getName()))
                .findFirst()
                .orElse(null);
        if (dealGroupPriceAttribute == null || CollectionUtils.isEmpty(dealGroupPriceAttribute.getValue())) {
            return null;
        }
        return new BigDecimal(dealGroupPriceAttribute.getValue().get(0)).stripTrailingZeros().toPlainString();
    }

    private void buildAtmosphereBarAndGeneralPromoDetailInfo(DealCtx ctx, PriceDisplayDTO priceDisplayDTO, DealGroupPBO result) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.buildAtmosphereBarAndGeneralPromoDetailInfo(DealCtx,PriceDisplayDTO,DealGroupPBO)");
        try {
            PriceContext priceContext = ctx.getPriceContext();
            if (priceContext == null || priceDisplayDTO == null) {
                return;
            }
            int categoryId = ctx.getCategoryId();

            PromoDetailConfig promoDetailConfig = gson.fromJson(Lion.get("com.sankuai.dzu.tpbase.dztgdetailweb.promoDetailConfig"), new com.google.common.reflect.TypeToken<PromoDetailConfig>() {
            }.getType());
            if (promoDetailConfig == null) {
                return;
            }

            Map<Integer, PromoDetailConfigDto> dealPublishCategoryId2PromoDetailConfigDtoMap = promoDetailConfig.getDealPublishCategoryId2PromoDetailConfigDtoMap();
            if (MapUtils.isEmpty(dealPublishCategoryId2PromoDetailConfigDtoMap) || !dealPublishCategoryId2PromoDetailConfigDtoMap.containsKey(categoryId)) {
                return;
            }
            PromoDetailConfigDto promoDetailConfigDto = dealPublishCategoryId2PromoDetailConfigDtoMap.get(categoryId);

            String serviceType = DealUtils.getServiceType(ctx);
            if (CollectionUtils.isNotEmpty(promoDetailConfigDto.getServiceTypeList()) && !promoDetailConfigDto.getServiceTypeList().contains(serviceType)) {
                return;
            }

            PromoDetailModule promoDetailModule = result.getPromoDetailModule();
            if (promoDetailModule == null) {
                promoDetailModule = new PromoDetailModule();
            }

            if (CollectionUtils.isNotEmpty(promoDetailConfigDto.getAtmosphereBarPromoTypeList())) {
                List<String> atmosphereBarPromoTypeList = promoDetailConfigDto.getAtmosphereBarPromoTypeList();
                List<DealBestPromoDetailDTO> promoDetailList = getPromoDetailList(priceDisplayDTO, atmosphereBarPromoTypeList);
                if (CollectionUtils.isNotEmpty(promoDetailList)) {
                    List<DealBestPromoDetail> newPromoDetailList = promoDetailList.stream().filter(Objects::nonNull).map(DealBestPromoDetailDTO::getDealBestPromoDetail).collect(Collectors.toList());
                    promoDetailModule.setAtmosphereBarPromoList(newPromoDetailList);
                }
            }

            if (CollectionUtils.isNotEmpty(promoDetailConfigDto.getGeneralPromoDetailTypeList())) {
                List<String> generalPromoDetailTypeList = promoDetailConfigDto.getGeneralPromoDetailTypeList();
                List<DealBestPromoDetailDTO> promoDetailList = getPromoDetailList(priceDisplayDTO, generalPromoDetailTypeList);

                List<DealBestPromoDetail> newPromoDetailList = promoDetailList.stream().filter(Objects::nonNull)
                        .sorted(Comparator.comparing(DealBestPromoDetailDTO::getPromoAmount).reversed())
                        .collect(Collectors.toList())
                        .stream().map(DealBestPromoDetailDTO::getDealBestPromoDetail)
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(newPromoDetailList)) {
                    promoDetailModule.setGeneralPromoDetailList(newPromoDetailList);
                }
            }

            BigDecimal dealGroupPrice = ctx.getDealGroupBase().getDealGroupPrice();
            BigDecimal promoPrice = priceDisplayDTO.getPrice();
            if (promoPrice != null) {
                promoDetailModule.setPromoPrice(PriceHelper.formatPrice(promoPrice));
            }
            if (dealGroupPrice != null && promoPrice != null && dealGroupPrice.compareTo(promoPrice) > 0) {
                promoDetailModule.setTotalPromo(PriceHelper.formatPrice(dealGroupPrice.subtract(promoPrice)));
            }

        } catch (Exception e) {
            logger.error("buildAtmosphereBarAndGeneralPromoDetailInfo error", e);
        }
    }

    private List<DealBestPromoDetailDTO> getPromoDetailList(PriceDisplayDTO priceDisplayDTO, List<String> promoTypeList) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.getPromoDetailList(com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO,java.util.List)");
        List<DealBestPromoDetailDTO> promoDetailList = new ArrayList<>();

        promoTypeList.stream().filter(Objects::nonNull).forEach(
                promoDetailType -> {
                    PromoDetailHandler promoDetailHandler = PromoDetailLocator.getByEnum(PromoDetailEnum.getByType(promoDetailType));
                    if (promoDetailHandler == null) {
                        return;
                    }
                    DealBestPromoDetailDTO promoDetail = promoDetailHandler.getDealBestPromoDetail(priceDisplayDTO);
                    if (promoDetail != null) {
                        promoDetailList.add(promoDetail);
                    }
                }
        );

        return promoDetailList;
    }

    /**
     * 包含团购优惠（市场价-售卖价）的优惠明细
     */
    private void buildDealPromoDetailInfo(DealCtx ctx, PriceDisplayDTO priceDisplayDTO, DealGroupPBO result) {
        if (priceDisplayDTO == null || CollectionUtils.isEmpty(priceDisplayDTO.getUsedPromos())) {
            return;
        }
        PromoDetailModule promoDetailModule = result.getPromoDetailModule();
        if (promoDetailModule == null) {
            promoDetailModule = new PromoDetailModule();
        }
        promoDetailModule.setFinalPrice(priceDisplayDTO.getPrice() == null ? "0.01" : PriceHelper.dropLastZero(priceDisplayDTO.getPrice()));
        promoDetailModule.setMarketPricePromo(priceDisplayDTO.getPromoAmount() == null ? "0" : PriceHelper.dropLastZero(priceDisplayDTO.getPromoAmount()));
        List<DealBestPromoDetail> bestPromoDetails = Lists.newArrayList();
        promoDetailModule.setBestPromoDetails(bestPromoDetails);
        promoDetailModule.setPerPrice(getPerPrice(priceDisplayDTO.getExtPrices()));

        boolean isZuLiaoButtonNewStyle = ctx.getPriceContext().isZuLiaoButtonNewStyle();

        List<PromoDTO> usedPromos = priceDisplayDTO.getUsedPromos();

        PriceDisplayDTO memberCardPrice = ctx.getPriceContext().getDcCardMemberPrice();
        if (isShoppingCartStyle(ctx)) {
            if (ctx.getBuyBar() != null && CollectionUtils.isNotEmpty(ctx.getBuyBar().getBuyBtns())) {
                DealBuyBtn dealBuyBtn = ctx.getBuyBar().getBuyBtns().get(0);
                if (dealBuyBtn.getDetailBuyType() == BuyBtnTypeEnum.MEMBER_CARD.getCode()) {
                    usedPromos = memberCardPrice.getUsedPromos();
                    promoDetailModule.setMarketPricePromo(memberCardPrice.getPromoAmount() == null ? "0" : PriceHelper.dropLastZero(memberCardPrice.getPromoAmount()));
                    promoDetailModule.setFinalPrice(memberCardPrice.getPrice() == null ? "0.01" : PriceHelper.dropLastZero(memberCardPrice.getPrice()));
                }
            }

        } else if (memberCardPrice != null && CollectionUtils.isNotEmpty(memberCardPrice.getUsedPromos()) && isZuLiaoButtonNewStyle
                && (buttonStyleHelper.singleMemberButton(ctx) || buttonStyleHelper.doubleButtonMemberRight(ctx))) {
            usedPromos = memberCardPrice.getUsedPromos();
            promoDetailModule.setMarketPricePromo(memberCardPrice.getPromoAmount() == null ? "0" : PriceHelper.dropLastZero(memberCardPrice.getPromoAmount()));
            promoDetailModule.setFinalPrice(memberCardPrice.getPrice() == null ? "0.01" : PriceHelper.dropLastZero(memberCardPrice.getPrice()));
        }
        for (PromoDTO promoDTO : usedPromos) {
            if (promoDTO.getIdentity() == null || promoDTO.getAmount() == null || StringUtils.isEmpty(promoDTO.getIdentity().getPromoTypeDesc())) {
                continue;
            }
            DealBestPromoDetail bestPromoDetail = new DealBestPromoDetail();
            bestPromoDetail.setIconUrl(promoDTO.getIcon());
            bestPromoDetail.setPromoName(promoDTO.getIdentity().getPromoTypeDesc());
            if (BeautyBianMeiCouponHelper.isBeautyBianMeiCouponPromo(promoDTO)) {
                bestPromoDetail.setPromoName(BeautyBianMeiCouponHelper.getBeautyBianMeiCouponPromoName(promoDTO));
            }
            bestPromoDetail.setPromoAmount(PriceHelper.dropLastZero(promoDTO.getAmount()));
            bestPromoDetail.setPromoDesc(promoDTO.getExtendDesc() == null ? "团购优惠" : promoDTO.getExtendDesc());
            bestPromoDetail.setPromoTag(convertPromoTag(promoDTO));

            buttonStyleHelper.entertainmentMemberPromoDetailComplete(ctx, isZuLiaoButtonNewStyle, result, promoDTO, promoDetailModule, bestPromoDetail);

            bestPromoDetails.add(bestPromoDetail);
        }

        if (GreyUtils.isShowMarketPrice(ctx) || isZuLiaoButtonNewStyle) {
            promoDetailModule.setShowMarketPrice(true);
            setMarketPromoDiscount(promoDetailModule, priceDisplayDTO);
            if (CollectionUtils.isNotEmpty(bestPromoDetails)) {
                promoDetailModule.setShowBestPromoDetails(true);
            }
        }
        if (ctx.isEnableCardStyle() || ctx.isEnableCardStyleV2()) {
            setMarketPromoDiscount(promoDetailModule, priceDisplayDTO);
//            setPriceStrengthDesc(ctx, promoDetailModule, priceDisplayDTO);
        }
        // 无忧通团单不展示优惠标签以及门市价
        if (DealAttrHelper.isWuyoutong(ctx)) {
            setWuyoutongPromo(promoDetailModule);
        }
    }

    private void buildCardStylePromoDetailInfo(DealCtx ctx, DealGroupPBO result) {
        PromoDetailModule promoDetailModule = result.getPromoDetailModule();
        if (ctx.isEnableCardStyle() || ctx.isEnableCardStyleV2()) {
            if (promoDetailModule == null) {
                return;
            }
            promoDetailModule.setExposureList(buildPromoExposureInfo(ctx));
            promoDetailModule.setCouponList(buildCouponList(ctx));
            promoDetailModule.setPromoActivityList(buildPromoActivityList(ctx));

            List<String> promoAbstractList = buildPromoAbstractList(promoDetailModule, ctx);
            promoDetailModule.setPromoAbstractList(promoAbstractList);
        }
    }

    private String convertPromoTag(PromoDTO promoDTO) {
        if (promoDTO == null) {
            return null;
        }
        if (promoDTO.getIdentity() == null || promoDTO.getIdentity().getPromoShowType() == null) {
            return "团购优惠";
        }
        switch (promoDTO.getIdentity().getPromoShowType()) {
            case "DISCOUNT_SELL":
                return "特惠促销";
            case "MT_SUBSIDY":
                return "美团补贴";
            case "NEW_CUSTOMER_DISCOUNT":
                return "新客特惠";
            case "MEMBER_BENEFITS":
                return "会员优惠";
            case "PLATFORM_COUPON":
                return "美团券";
            case "MERCHANT_COUPON":
                return "商家券";
            case "GOVERNMENT_CONSUME_COUPON":
                return "政府消费券";
            case "DEAL_PROMO":
                return "团购优惠";
            case "PRESALE_PROMO":
                return "预售优惠";
            default:
                return "团购优惠";
        }
    }

    private String convertSourceTag(PromoDTO promoDTO) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.convertSourceTag(com.sankuai.dealuser.price.display.api.model.PromoDTO)");
        if (promoDTO == null) {
            return null;
        }
        if (promoDTO.getIdentity() == null || promoDTO.getIdentity().getPromoShowType() == null) {
            return "团购优惠";
        }
        if (promoDTO.getPromoIdentity() != null) {
            String promoIdentity = promoDTO.getPromoIdentity();
            if ("godCouponAlliance".equals(promoIdentity)) {
                return "神券联盟";
            } else if ("specialDiscountCode".equals(promoIdentity)) {
                return "特惠码专享";
            } else if ("beautyMember".equals(promoIdentity)) {
                return "丽人会员";
            }
        }
        switch (promoDTO.getIdentity().getPromoShowType()) {
            case "DISCOUNT_SELL":
                return "特惠促销";
            case "MT_SUBSIDY":
                return "美团补贴";
            case "NEW_CUSTOMER_DISCOUNT":
                return "新客特惠";
            case "MEMBER_BENEFITS":
                return "会员优惠";
            case "PLATFORM_COUPON":
                return "美团券";
            case "MERCHANT_COUPON":
                return "商家券";
            case "GOVERNMENT_CONSUME_COUPON":
                return "政府消费券";
            case "DEAL_PROMO":
                return "团购优惠";
            case "PRESALE_PROMO":
                return "预售优惠";
            default:
                return "优惠";
        }
    }

    private boolean isShoppingCartStyle(DealCtx ctx) {
        return ctx.getBuyBar().getStyleType() == SHOPPING_CART.code;
    }

    public void setWuyoutongPromo(PromoDetailModule promoDetailModule) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.setWuyoutongPromo(com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PromoDetailModule)");
        promoDetailModule.setShowMarketPrice(false);
        promoDetailModule.setMarketPricePromo(StringUtils.EMPTY);
        promoDetailModule.setShowBestPromoDetails(false);
        promoDetailModule.setBestPromoDetails(null);
        promoDetailModule.setMarketPrice(StringUtils.EMPTY);
        promoDetailModule.setMarketPromoDiscount(StringUtils.EMPTY);
    }

    public void setMarketPromoDiscount(PromoDetailModule promoDetailModule, PriceDisplayDTO priceDisplayDTO) {
        BigDecimal marketPrice = priceDisplayDTO.getMarketPrice();
        BigDecimal finalPrice = priceDisplayDTO.getPrice();
        if (marketPrice == null || finalPrice == null) {
            return;
        }
        BigDecimal discountRate = calcDiscountRate(marketPrice, finalPrice);

        String discountRateStr;
        if (discountRate.compareTo(new BigDecimal("9.9")) > 0) {
            discountRateStr = "";
        } else if (discountRate.compareTo(new BigDecimal("0.1")) <= 0) {
            discountRateStr = "0.1折";
        } else {
            discountRateStr = discountRate + "折";
        }
        promoDetailModule.setMarketPromoDiscount(discountRateStr);
        promoDetailModule.setPlainMarketPromoDiscount(discountRateStr);
    }

    public void setPriceStrengthDesc(DealCtx ctx, PromoDetailModule promoDetailModule, PriceDisplayDTO priceDisplayDTO) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.setPriceStrengthDesc(DealCtx,PromoDetailModule,PriceDisplayDTO,int)");
        try {
            PricePowerTagDisplayDTO pricePowerTagDisplayDTO = priceDisplayDTO.getPricePowerTagDisplayDTO();
            if (pricePowerTagDisplayDTO == null || CollectionUtils.isEmpty(pricePowerTagDisplayDTO.getAllTagList())) {
                return;
            }
            List<PricePowerTagItem> allTagList = pricePowerTagDisplayDTO.getAllTagList();
            List<Integer> sortOrder;
            int publishCategory = ctx.getCategoryId();
            if (isCategoryMatch(publishCategory, Arrays.asList(501, 502, 503, 509, 511, 514))) {
                sortOrder = Arrays.asList(
                        LOWEST_PRICE_IN_RECENT_365_DAYS.getType(),
                        LOWEST_PRICE_IN_RECENT_180_DAYS.getType(),
                        LOWEST_PRICE_IN_RECENT_90_DAYS.getType(),
                        LOWEST_PRICE_IN_RECENT_60_DAYS.getType(),
                        LOWEST_PRICE_IN_RECENT_30_DAYS.getType(),
                        NETWORK_LOW_PRICE.getType());
            }
            // 足疗：增加空间比价标签
            else if (isCategoryMatch(publishCategory, Collections.singletonList(303))) {
                String sk = getSpacePriceTagSk(ctx);
                if (StringUtils.isNotBlank(sk) && sk.contains("e")) {
                    // e组不返回任何比价标签
                    return;
                }
                sortOrder = Arrays.asList(
                        NETWORK_LOW_PRICE.getType(),
                        LOWEST_PRICE_IN_RADIUS_3KM_RANGE.getType(),
                        LOWEST_PRICE_IN_CITY.getType(),
                        LOWEST_PRICE_IN_DISTRICT.getType(),
                        LOWEST_PRICE_IN_REGION.getType(),
                        LOWEST_PRICE_IN_RECENT_365_DAYS.getType(),
                        LOWEST_PRICE_IN_RECENT_180_DAYS.getType(),
                        LOWEST_PRICE_IN_RECENT_90_DAYS.getType(),
                        LOWEST_PRICE_IN_RECENT_60_DAYS.getType(),
                        LOWEST_PRICE_IN_RECENT_30_DAYS.getType());
                if (CollectionUtils.isNotEmpty(allTagList)) {
                    allTagList = allTagList.stream().filter(a -> sortOrder.contains(a.getTagType())).collect(Collectors.toList());
                }
            } else {
                sortOrder = Arrays.asList(
                        NETWORK_LOW_PRICE.getType(),
                        LOWEST_PRICE_IN_RECENT_365_DAYS.getType(),
                        LOWEST_PRICE_IN_RECENT_180_DAYS.getType(),
                        LOWEST_PRICE_IN_RECENT_90_DAYS.getType(),
                        LOWEST_PRICE_IN_RECENT_60_DAYS.getType(),
                        LOWEST_PRICE_IN_RECENT_30_DAYS.getType());
            }
//            sortAndSetPriceStrengthDesc(promoDetailModule, allTagList, sortOrder, ctx);
        } catch (Exception e) {
            log.error("setPriceStrengthDesc error,", e);
        }
    }

    private String getSpacePriceTagSk(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.getSpacePriceTagSk(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        String module = ctx.isMt() ? "MTMassageSpacePriceTag" : "DPMassageSpacePriceTag";
        ModuleAbConfig abConfig = douHuBiz.getAbExpResultByUserId(ctx, module);
        List<ModuleAbConfig> moduleAbConfigs = ctx.getModuleAbConfigs();
        if (CollectionUtils.isEmpty(moduleAbConfigs)) {
            moduleAbConfigs = new ArrayList<>();
        }
        moduleAbConfigs.add(abConfig);
        ctx.setModuleAbConfigs(moduleAbConfigs);
        if (Objects.isNull(abConfig) || CollectionUtils.isEmpty(abConfig.getConfigs())) {
            return null;
        }
        return abConfig.getConfigs().get(0).getExpResult();
    }

    private boolean isCategoryMatch(int publishCategory, List<Integer> categories) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.isCategoryMatch(int,java.util.List)");
        return categories.contains(publishCategory);
    }

    private void sortAndSetPriceStrengthDesc(PromoDetailModule promoDetailModule, List<PricePowerTagItem> allTagList, List<Integer> sortOrder, DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.sortAndSetPriceStrengthDesc(PromoDetailModule,List,List,int,EnvCtx)");
        if (CollectionUtils.isEmpty(allTagList) || promoDetailModule == null || CollectionUtils.isEmpty(sortOrder)) {
            return;
        }
        allTagList.sort(Comparator.comparingInt(item -> sortOrder.indexOf(item.getTagType())));
        PricePowerTagItem firstTag = allTagList.get(0);
        List<Integer> showType = Arrays.asList(
                LOWEST_PRICE_IN_RECENT_30_DAYS.getType(),
                LOWEST_PRICE_IN_RECENT_60_DAYS.getType(),
                LOWEST_PRICE_IN_RECENT_90_DAYS.getType(),
                LOWEST_PRICE_IN_RECENT_180_DAYS.getType(),
                LOWEST_PRICE_IN_RECENT_365_DAYS.getType(),
                LOWEST_PRICE_IN_RADIUS_3KM_RANGE.getType(),
                LOWEST_PRICE_IN_CITY.getType(),
                LOWEST_PRICE_IN_DISTRICT.getType(),
                LOWEST_PRICE_IN_REGION.getType(),
                NETWORK_LOW_PRICE.getType());
        if (firstTag != null && showType.contains(firstTag.getTagType())) {
            promoDetailModule.setPriceStrengthDesc(allTagList.get(0).getTagName());
//            promoDetailModule.setShowPriceCompareEntrance(showPriceCompareEntrance(firstTag, ctx));
        }
    }

    /**
     * 计算折扣率（保留一位小数 + 向上取整）
     */
    private static BigDecimal calcDiscountRate(BigDecimal marketPrice, BigDecimal finalPrice) {
        if (marketPrice == null || finalPrice == null || marketPrice.compareTo(BigDecimal.ZERO) == 0
                || finalPrice.compareTo(BigDecimal.ZERO) == 0 || finalPrice.compareTo(marketPrice) == 0
                || marketPrice.compareTo(finalPrice) < 0) {
            return null;
        }
        return finalPrice.divide(marketPrice, 2, RoundingMode.CEILING).multiply(new BigDecimal(10)).setScale(1, RoundingMode.CEILING);
    }

    private String getPerPrice(List<ExtPriceDisplayDTO> extPrices) {
        if (CollectionUtils.isEmpty(extPrices)) {
            return null;
        }
        for (ExtPriceDisplayDTO extPriceDisplayDTO : extPrices) {
            if (ExtPriceTypeEnum.Per_price.getType() == extPriceDisplayDTO.getExtPriceType() && extPriceDisplayDTO.getExtPrice() != null) {
                return PriceHelper.dropLastZero(extPriceDisplayDTO.getExtPrice());
            }
        }
        return null;
    }


    private void putPicAspectRatio(DealCtx ctx, DealGroupPBO result) {

        String expResults = StringUtils.isEmpty(ctx.getExpResults()) ? "[]" : ctx.getExpResults();
        try {
            List<String> results = JsonFacade.deserializeList(expResults.toLowerCase(), String.class);
            if (CollectionUtils.isEmpty(results)) {
                return;
            }
            Map<Integer, Map<String, Double>> picaspectratioExpMap = LionFacade.get(LionConstants.PICASPECTRATIO_EXP, new TypeReference<Map<Integer, Map<String, Double>>>() {
            });
            if (MapUtils.isEmpty(picaspectratioExpMap)) {
                return;
            }
            for (Integer categoryId : picaspectratioExpMap.keySet()) {
                if (categoryId == null) {
                    continue;
                }
                if (ctx.getCategoryId() == categoryId) {
                    Map<String, Double> picaspectratioExp = picaspectratioExpMap.get(categoryId);
                    if (MapUtils.isEmpty(picaspectratioExp)) {
                        continue;
                    }
                    Double picaspectratio = null;
                    for (String exp : results) {
                        picaspectratio = picaspectratioExp.get(exp);
                        if (picaspectratio != null) {
                            result.setPicAspectRatio(picaspectratio);
                            break;
                        }
                    }
                    if (picaspectratio == null) {
                        picaspectratio = picaspectratioExp.get(DEFAULTPICASPECTRATIO);
                        if (picaspectratio != null) {
                            result.setPicAspectRatio(picaspectratio);
                        }
                    }
                    break;
                }
            }
        } catch (Exception e) {
            logger.error("putPicAspectRatio is error,dealGroupId is {}", ctx.getResult().getDpDealId());
        }
    }

    private void buildBuyBarPricePostFix(DealCtx ctx, DealGroupPBO result) {
        if (ctx.getDealGroupDTO().getDeals().stream()
                .filter(dealGroupDealDTO -> dealGroupDealDTO.getBasic().getStatus() == 1)
                .count() > 1) {//多sku的价格后面加"起"字
            DealBuyBar buildBuyBar = result.getBuyBar();
            buildBuyBar.getBuyBtns().stream()
                    .filter(buyBtn -> StringUtils.isEmpty(buyBtn.getPricePostfix()))//如果已经设置过PricePostfix就不处理了
                    .forEach(buyBtn -> buyBtn.setPricePostfix("起"));
        }
    }

    private void buildBuyBar(DealCtx ctx, DealGroupPBO result) {
        DealBuyBar buildBuyBar = NewBuyBarHelper.build(ctx);

        if (buildBuyBar == null || ctx == null
                || !isInWhite(ctx.getCategoryId())
                || CollectionUtils.isEmpty(buildBuyBar.getBuyBtns())
                || buildBuyBar.getStyleType() == SHOPPING_CART.code) {
            result.setBuyBar(buildBuyBar);
            return;
        }

        try {
            DealBuyBtn dealBuyBtn = buildBuyBar.getBuyBtns().get(buildBuyBar.getBuyBtns().size() - 1);
            Future activityFuture = dealActivityWrapper.prepareDealActivity(buildBatchQueryDealActivityReq(ctx));
            List<DealActivityDTO> dealActivityDTOS = dealActivityWrapper.queryDealActivity(activityFuture, ctx);
            if (CollectionUtils.isEmpty(dealActivityDTOS)) {
                result.setBuyBar(buildBuyBar);
                return;
            }
            DealActivityDTO selectedDealActivity = dealActivityDTOS.get(0);
            if (selectedDealActivity == null || selectedDealActivity.getPriceStrengthTime() == 0) {
                result.setBuyBar(buildBuyBar);
                return;
            }
            BigDecimal markPrice = ctx.getDealGroupBase().getMarketPrice();
            dealBuyBtn.setMarketPrice(NumberFormat.getInstance().format(markPrice));
            PriceStrengthTimeEnum priceDay = PriceStrengthTimeEnum.findByCode(selectedDealActivity.getPriceStrengthTime());
            Date priceAbilityDate = selectedDealActivity.getDateMap() == null ? null : selectedDealActivity.getDateMap().get(ExposureDateKeyEnum.PRICE_COMPARE_DATE.getKey());
            BigDecimal minPrice = queryPriceAbility(ctx.getDpId(), priceDay, priceAbilityDate);
            if (minPrice == null) {
                result.setBuyBar(buildBuyBar);
                return;
            }
            dealBuyBtn.setMinPrice(NumberFormat.getInstance().format(minPrice));
            if (minPrice.compareTo(new BigDecimal(dealBuyBtn.getPriceStr())) > 0) {
                result.setMinPriceDesc(String.format("近%s低价", priceDay.getDesc()));
                dealBuyBtn.setMinPriceDesc(String.format("近%s低价", priceDay.getDesc()));
            }
        } catch (Exception e) {
            logger.error("buildMinPrice", e);
        }

        result.setBuyBar(buildBuyBar);
    }

    public boolean isInWhite(int category) {
        return com.google.common.collect.Lists.newArrayList(401, 506).contains(category);
    }

    public BigDecimal queryPriceAbility(int dealGroupId, PriceStrengthTimeEnum priceStrengthTime, Date priceAbilityDate) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.queryPriceAbility(int,com.dianping.gmkt.activity.api.enums.PriceStrengthTimeEnum,java.util.Date)");
        try {
            SwanParam swanParam = new SwanParam();
            Map<String, Object> map = new HashMap<>();
            map.put("productId", dealGroupId);
            map.put("productType", "团购");
            String bizKey = SWAN_QUERY_BIZ_KEY;
            if (priceAbilityDate != null) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String priceAbilityDateStr = dateFormat.format(priceAbilityDate);
                map.put("queryDate", priceAbilityDateStr);
                bizKey = SWAN_QUERY_BIZ_KEY_BY_DATE;
            }
            swanParam.setRequestParams(com.google.common.collect.Lists.newArrayList(map));
            Result<QueryData> result = swanQueryService.queryByKey(SWAN_QUERY_BIZ_TYPE_ID, bizKey, swanParam);
            if (result == null || !result.isIfSuccess() || result.getData() == null
                    || CollectionUtils.isEmpty(result.getData().getResultSet())) {
                return null;
            }
            Double minPrice = null;
            for (Map<String, Object> objectMap : result.getData().getResultSet()) {
                if (priceStrengthTime == PriceStrengthTimeEnum.THIRTY_DAY) {
                    minPrice = objectMap.get(RESULT_MAP_KEY_PRICE_30D) == null ? null : (Double) objectMap.get(RESULT_MAP_KEY_PRICE_30D);
                } else if (priceStrengthTime == PriceStrengthTimeEnum.SIXTY_DAY) {
                    minPrice = objectMap.get(RESULT_MAP_KEY_PRICE_60D) == null ? null : (Double) objectMap.get(RESULT_MAP_KEY_PRICE_60D);
                } else if (priceStrengthTime == PriceStrengthTimeEnum.NINETY_DAY) {
                    minPrice = objectMap.get(RESULT_MAP_KEY_PRICE_90D) == null ? null : (Double) objectMap.get(RESULT_MAP_KEY_PRICE_90D);
                }
            }
            return BigDecimal.valueOf(minPrice).setScale(2, BigDecimal.ROUND_HALF_UP);
        } catch (Exception e) {
            logger.error("queryPriceAbility", e);
        }
        return null;
    }

    private String buildSelectTag(int dpId, boolean mt) {
        SelectDealConfig selectDealConfig = DealHelper.getSelectDealConfig();
        if (selectDealConfig == null) {
            return StringUtils.EMPTY;
        }
        if (mt && selectDealConfig.getMtDealGroupIds().contains(dpId)) {
            return PlusIcons.MT_SELECT_DEAL_ICON;
        }
        if (!mt && selectDealConfig.getDpDealGroupIds().contains(dpId)) {
            return PlusIcons.DP_SELECT_DEAL_ICON;
        }
        return StringUtils.EMPTY;
    }

    private DealChoicestIcon buildChoicestIcon(DealCtx ctx) {
        if (!DealHelper.getChoicestIconSwitch()) {
            return null;
        }
        try {
            DealChoicestIcon activityChoicestIcon = buildActivityChoicestIcon(ctx);
            if (activityChoicestIcon != null) {
                return activityChoicestIcon;
            }
            String titleTagIcon = buildSelectTag(ctx.getDpId(), ctx.isMt());
            if (StringUtils.isNotBlank(titleTagIcon)) {
                DealChoicestIcon dealChoicestIcon = new DealChoicestIcon();
                dealChoicestIcon.setIcon(titleTagIcon);
                dealChoicestIcon.setIconWidth(30);
                dealChoicestIcon.setIconHeight(18);
                return dealChoicestIcon;
            }
        } catch (Exception e) {
            logger.error("buildChoicestIcon error. dealGroupId: {}", ctx.getDpId(), e);
        }
        return null;
    }

    private DealChoicestIcon buildActivityChoicestIcon(DealCtx ctx) {
        DealActivityDTO activityDTO = ctx.getChoicestDealActivityDTO();
        if (activityDTO != null && CollectionUtils.isNotEmpty(activityDTO.getUrls())) {
            DealChoicestIcon dealChoicestIcon = new DealChoicestIcon();
            dealChoicestIcon.setIcon(activityDTO.getUrls().get(0));
            int width = 100;
            int height = 25;
            //营销返回了图片但是没有返回长宽信息则设置未默认信息
            if (activityDTO.getIconHeight() == null || activityDTO.getIconHeight() == 0
                    || activityDTO.getIconWidth() == null) {
                dealChoicestIcon.setIconWidth(width);
                dealChoicestIcon.setIconHeight(height);
                return dealChoicestIcon;
            }
            //图片本身的大小符合限制大小则直接赋值
            if (activityDTO.getIconHeight() <= 100 && activityDTO.getIconWidth() <= 25) {
                width = activityDTO.getIconWidth();
                height = activityDTO.getIconHeight();
            } else {
                //宽高比超过4按照宽度比例缩一下，否则按照高度比例缩一下
                double widthTimes = (double) activityDTO.getIconWidth() / 100;
                double heightTimes = (double) activityDTO.getIconHeight() / 25;
                double aspectRatio = (double) activityDTO.getIconWidth() / (double) activityDTO.getIconHeight();
                if (aspectRatio > 4) {
                    width = (int) (activityDTO.getIconWidth() / widthTimes);
                    height = (int) (activityDTO.getIconHeight() / widthTimes);
                } else {
                    width = (int) (activityDTO.getIconWidth() / heightTimes);
                    height = (int) (activityDTO.getIconHeight() / heightTimes);
                }
            }
            dealChoicestIcon.setIconWidth(width);
            dealChoicestIcon.setIconHeight(height);
            return dealChoicestIcon;
        }
        return null;
    }

    private String getDefaultSkuId(DealCtx dealCtx) {
        if (dealCtx.getSkuCtx() != null && StringUtils.isNotBlank(dealCtx.getSkuCtx().getSkuId())) {
            return dealCtx.getSkuCtx().getSkuId();
        }
        return String.valueOf(getDealId(dealCtx.getDealGroupBase()));
    }

    private Integer getDealId(DealGroupBaseDTO dealGroupBaseDTO) {
        List<DealBaseDTO> deals = dealGroupBaseDTO.getDeals();
        if (CollectionUtils.isNotEmpty(deals)) {
            for (DealBaseDTO deal : deals) {
                if (deal.getDealStatus() == 1) {
                    return deal.getDealId();
                }
            }
        }
        return 0;
    }

    private String getTitle(final DealCtx dealCtx) {
        if (GreyUtils.enableQueryCenterForMainApi(dealCtx) && dealCtx.getDealGroupDTO() != null) {
            DealGroupDTO dealGroupDTO = dealCtx.getDealGroupDTO();
            String title = dealGroupDTO.getBasic().getTitle();
            String eduPrefix = getEduPrefix(dealCtx);
            if (eduPrefix != null) {
                return eduPrefix + title;
            }
            return title;
        } else {
            DealGroupBaseDTO dealGroupBaseDTO = dealCtx.getDealGroupBase();
            String title = dealGroupBaseDTO.getProductTitle();
            String eduPrefix = getEduPrefix(dealCtx);
            if (eduPrefix != null) {
                return eduPrefix + title;
            }
            return title;
        }
    }

    private String getEduPrefix(final DealCtx dealCtx) {
        DisplayControlResponse displayControlResponse = dealCtx.getDisplayControlResponse();
        if (displayControlResponse == null || !displayControlResponse.canShow()) {
            return null;
        }
        List<AttributeDTO> attributeDTOS = dealCtx.getAttrs();
        String suitableAge = AttributeUtils.getAttributeValue(DealAttrKeys.EDU_SUITABLE_AGE, attributeDTOS);
        String femaleOnly = AttributeUtils.getAttributeValue(DealAttrKeys.EDU_FEMALE_ONLY, attributeDTOS);
        return matchEduPrefix(suitableAge, femaleOnly);
    }

    private String matchEduPrefix(String suitableAge, String femaleOnly) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.matchEduPrefix(java.lang.String,java.lang.String)");
        try {
            if (StringUtils.isBlank(suitableAge)) {
                return null;
            }
            Matcher betweenAgeMatcher = Pattern.compile("^(.*)岁-(.*)岁$").matcher(suitableAge);
            if (betweenAgeMatcher.matches()) {
                int maxAge = Integer.parseInt(betweenAgeMatcher.group(2));
                if (maxAge <= 7) {
                    return "【幼儿】";
                }
                if (maxAge <= 17) {
                    return "【青少】";
                }
            }

            Matcher beyondAgeMatcher = Pattern.compile("^(.*)岁及以上$").matcher(suitableAge);
            if (beyondAgeMatcher.matches()) {
                if ("是".equals(femaleOnly)) {
                    return "【女性班】";
                } else {
                    return "【成人】";
                }
            }
            return null;
        } catch (Exception e) {
            log.error("Failed to resolve attributes of edu deals, suitableAge = {}, femaleOnly = {}", suitableAge, femaleOnly, e);
            return null;
        }
    }

    private List<ContentPBO> getContent(final DealGroupBaseDTO dealGroupBaseDTO, final boolean isMt, DealCtx ctx, DealGroupPBO dealGroupPBO) {
        if (dealGroupBaseDTO == null) {
            return null;
        }
        List<ContentPBO> result = Lists.newArrayList();
        if (dealGroupBaseDTO.getHeadVideo() != null) {
            String picUrl = ImageHelper.legacy320Size(dealGroupBaseDTO.getHeadVideo().getVideoCoverPath(), isMt);
            String desc = String.format("当前Wi-Fi环境确定播放？预计花费流量%.2fM", dealGroupBaseDTO.getHeadVideo().getSize() / 1024.0);
            ContentPBO video = new ContentPBO(ContentType.VIDEO.getType(), picUrl);
            video.setVideoUrl(dealGroupBaseDTO.getHeadVideo().getVideoPath());
            video.setDesc(desc);
            result.add(video);
        }
        Integer width = getPicSize(ctx, PIC_WIDTH);
        Integer height = getPicSize(ctx, PIC_HEIGHT);
        if (width != null && height != null) {
            if (StringUtils.isNotBlank(dealGroupBaseDTO.getDefaultPic())) {
                String picUrl = ImageHelper.format(dealGroupBaseDTO.getDefaultPic(), width, height, isMt);
                ContentPBO defPic = new ContentPBO(ContentType.PIC.getType(), picUrl);
                result.add(defPic);
            }
            if (StringUtils.isNotBlank(dealGroupBaseDTO.getDealGroupPics())) {
                List<ContentPBO> otherPic =
                        Arrays.stream(dealGroupBaseDTO.getDealGroupPics().split("\\|"))
                                .filter(input -> input != null && !dealGroupBaseDTO.getDefaultPic().equals(input))
                                .map(input -> new ContentPBO(ContentType.PIC.getType(), ImageHelper.format(input, width, height, isMt)))
                                .collect(Collectors.toList());
                result.addAll(otherPic);
            }
        } else {
            if (StringUtils.isNotBlank(dealGroupBaseDTO.getDefaultPic())) {
                String picUrl = ImageHelper.originalSize(dealGroupBaseDTO.getDefaultPic(), isMt);
                ContentPBO defPic = new ContentPBO(ContentType.PIC.getType(), picUrl);
                result.add(defPic);
            }
            if (StringUtils.isNotBlank(dealGroupBaseDTO.getDealGroupPics())) {
                List<ContentPBO> otherPic =
                        Arrays.stream(dealGroupBaseDTO.getDealGroupPics().split("\\|"))
                                .filter(input -> input != null && !dealGroupBaseDTO.getDefaultPic().equals(input))
                                .map(input -> new ContentPBO(ContentType.PIC.getType(), ImageHelper.originalSize(input, isMt)))
                                .collect(Collectors.toList());
                result.addAll(otherPic);
            }
        }

        int categoryId = ctx.getCategoryId();
        String headerPicProcessorKey = LionConfigUtils.getHeaderPicProcessor(categoryId);
        HeaderPicProcessor headerPicProcessor = headerPicProcessorMap.get(headerPicProcessorKey);
        dealGroupPBO.setExhibitContents(ctx.getExhibitContentDTO());
        headerPicProcessor.fillPicScale(ctx, result, dealGroupPBO);
        return result;
    }

//    private List<ContentPBO> getContent(final DealGroupBaseDTO dealGroupBaseDTO, final boolean isMt, DealCtx ctx) {
//        if (dealGroupBaseDTO == null) {
//            return null;
//        }
//        List<ContentPBO> result = Lists.newArrayList();
//        if (dealGroupBaseDTO.getHeadVideo() != null) {
//            String picUrl = ImageHelper.mediumSize(dealGroupBaseDTO.getHeadVideo().getVideoCoverPath(), isMt);
//            String desc = String.format("当前Wi-Fi环境确定播放？预计花费流量%.2fM", dealGroupBaseDTO.getHeadVideo().getSize() / 1024.0);
//            ContentPBO video = new ContentPBO(ContentType.VIDEO.getType(), picUrl);
//            video.setVideoUrl(dealGroupBaseDTO.getHeadVideo().getVideoPath());
//            video.setDesc(desc);
//            result.add(video);
//        }
//        Integer width = getPicSize(ctx, PIC_WIDTH);
//        Integer height = getPicSize(ctx, PIC_HEIGHT);
//        if (width != null && height != null) {
//            if (StringUtils.isNotBlank(dealGroupBaseDTO.getDefaultPic())) {
//                String picUrl = ImageHelper.format(dealGroupBaseDTO.getDefaultPic(), width, height, isMt);
//                ContentPBO defPic = new ContentPBO(ContentType.PIC.getType(), picUrl);
//                result.add(defPic);
//            }
//            if (StringUtils.isNotBlank(dealGroupBaseDTO.getDealGroupPics())) {
//                List<ContentPBO> otherPic =
//                        Arrays.stream(dealGroupBaseDTO.getDealGroupPics().split("\\|"))
//                                .filter(input -> input != null && !dealGroupBaseDTO.getDefaultPic().equals(input))
//                                .map(input -> new ContentPBO(ContentType.PIC.getType(), ImageHelper.format(input, width, height, isMt)))
//                                .collect(Collectors.toList());
//                result.addAll(otherPic);
//            }
//        } else {
//            if (StringUtils.isNotBlank(dealGroupBaseDTO.getDefaultPic())) {
//                String picUrl = ImageHelper.originalSize(dealGroupBaseDTO.getDefaultPic(), isMt);
//                ContentPBO defPic = new ContentPBO(ContentType.PIC.getType(), picUrl);
//                result.add(defPic);
//            }
//            if (StringUtils.isNotBlank(dealGroupBaseDTO.getDealGroupPics())) {
//                List<ContentPBO> otherPic =
//                        Arrays.stream(dealGroupBaseDTO.getDealGroupPics().split("\\|"))
//                                .filter(input -> input != null && !dealGroupBaseDTO.getDefaultPic().equals(input))
//                                .map(input -> new ContentPBO(ContentType.PIC.getType(), ImageHelper.originalSize(input, isMt)))
//                                .collect(Collectors.toList());
//                result.addAll(otherPic);
//            }
//        }
//        return result;
//    }

    private Integer getPicSize(DealCtx ctx, String heightWidth) {
        String expResults = StringUtils.isEmpty(ctx.getExpResults()) ? "[]" : ctx.getExpResults();
        try {
            List<String> results = JsonFacade.deserializeList(expResults.toLowerCase(), String.class);
            if (CollectionUtils.isNotEmpty(results)) {
                //支持体检做头图AB
                Map<Integer, Map<String, Map<String, Integer>>> picaspectratioExpMap = LionFacade.get(LionConstants.PICSIZE_EXP, new TypeReference<Map<Integer, Map<String, Map<String, Integer>>>>() {
                });
                if (MapUtils.isEmpty(picaspectratioExpMap)) {
                    return null;
                }
                for (Integer categoryId : picaspectratioExpMap.keySet()) {
                    if (categoryId == null) {
                        continue;
                    }
                    if (ctx.getCategoryId() == categoryId) {
                        Map<String, Map<String, Integer>> picaspectratioExp = picaspectratioExpMap.get(categoryId);
                        if (MapUtils.isEmpty(picaspectratioExp)) {
                            continue;
                        }
                        Map<String, Integer> picSize = null;
                        for (String exp : results) {
                            picSize = picaspectratioExp.get(exp);
                            if (picSize != null) {
                                return picSize.get(heightWidth);
                            }
                        }
                    }
                }
            }

            // 头图比例配置化
            Map<Integer, Map<String, Integer>> picAspectRatioMap = LionFacade.get(LionConstants.PICSIZE_CONFIG, new TypeReference<Map<Integer, Map<String, Integer>>>() {
            });
            if (MapUtils.isEmpty(picAspectRatioMap)) {
                return null;
            }
            for (Integer categoryId : picAspectRatioMap.keySet()) {
                if (categoryId == null) {
                    continue;
                }
                if (ctx.getCategoryId() == categoryId) {
                    Map<String, Integer> picsizeMap = picAspectRatioMap.get(categoryId);
                    if (MapUtils.isEmpty(picsizeMap)) {
                        continue;
                    }
                    return picsizeMap.get(heightWidth);
                }
            }

        } catch (Exception e) {
            logger.error("putPicAspectRatio is error,dealGroupId is {}", ctx.getResult().getDpDealId());
        }
        return null;
    }

    private ShopPBO getShopPBO(DealCtx ctx) {
        if (ctx == null || ctx.getBestShopResp() == null) {
            return null;
        }

        BestShopDTO bestShop = ctx.getBestShopResp();
        long poiid4p = ctx.getLongPoiId4PFromResp();
        ShopPBO shop = new ShopPBO();

        if (ctx.getEnvCtx().isMt()) {
            shop.setShopId(poiid4p);
        } else {
            shop.setShopUuid(ctx.getShopUuidFromResp());
            if (ShopUuidUtils.retainShopId(poiid4p)) {
                shop.setShopId(poiid4p);
            }
        }

        // 团详框架改版，猜喜路径
        ModuleAbConfig moduleAbConfig = douHuService.enableCardStyleV2(ctx.getEnvCtx(), ctx.getCategoryId(), ctx.getMrnVersion());
        // 命中实验时，构建 人均消费价格、营业状态、营业时间
        if (douHuService.hitEnableCardStyleV2(moduleAbConfig)) {
            buildAvgPriceAndBusinessState(ctx, shop);
        }



        shop.setShopUrl(UrlHelper.getShopUrl(ctx));
        shop.setShopName(bestShop.getShopName());
        shop.setBranchName(bestShop.getBranchName());
        shop.setPhoneNos(getPhoneNos(bestShop, ctx));
        shop.setAddress(bestShop.getAddress());
        shop.setHideAddrEnable(hideAddrEnable(ctx));
        shop.setShopPower(ctx.isMt() && ctx.getMtPoiDTO() != null ? ctx.getMtPoiDTO().getMtAvgScore() : bestShop.getShopPower());
        shop.setImUrl(ctx.getImUrl());
        shop.setShowType(bestShop.getShowType());
        shop.setShopType(bestShop.getShopType());
        shop.setLyyShop(isLyyShop(ctx.getEnvCtx().isMt(), bestShop.getMtShopId()));
        shop.setRelatedShops(ctx.getRelatedShops());
        shop.setShopPic(bestShop.getShopPic());

        boolean gray = SwitchHelper.enableDrivingShop(ctx.isMt(), ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId(), ctx.getCityId4P());
        shop.setShopDesc(gray && isDrivingSchoolCar(ctx) ? "报名点" : null);

        if (ctx.isMt()) {
            if (DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP.equals(ctx.getEnvCtx().getDztgClientTypeEnum())) {
                shop.setLat(bestShop.getGlat());
                shop.setLng(bestShop.getGlng());
            } else {
                shop.setLat(bestShop.getLat());
                shop.setLng(bestShop.getLng());
            }
        } else {
            shop.setLat(bestShop.getGlat());
            shop.setLng(bestShop.getGlng());
        }

        shop.setShopNum(bestShop.getTotalShopsNum());

        if (bestShop.getTotalShopsNum() > 1) {
            String shopListUrl = UrlHelper.getShopListUrl(ctx, bestShop.getDpShopId());
            shop.setShopListUrl(shopListUrl);
            shop.setShopListDesc(String.format("%s家门店适用", bestShop.getTotalShopsNum()));
        }

        int categoryId = ctx.getChannelDTO().getCategoryId();
        List<Integer> categoryList = Lion.getList(LionConstants.POI_DISTANCE_CATEGORY_BLACKLIST, Integer.TYPE, Lists.newArrayList());

        if (StringUtils.isNotBlank(bestShop.getDistance())) {
            if (poiClientWrapper.isDoor2DoorPoi(ctx.getDpLongShopId()) && categoryList.contains(categoryId)) {
                shop.setDistanceDesc("上门服务");
            } else {
                shop.setDistanceDesc(String.format("距您%s", bestShop.getDistance()));
                shop.setDistance(bestShop.getDistance());
            }
        }

        shop.setRecallBtns(buildRecallBtns(ctx));
        shop.setMapUrl(UrlHelper.getMapUrl(bestShop, ctx));

        setDisplayPosition(ctx, shop);
        shop.setHideStars(hideStarsEnable(ctx));

        postProcessShop(ctx, shop);
        return shop;
    }

    /**
     * 团详框架改版，构建 人均消费价格、营业状态、营业时间
     *
     * @param ctx
     * @param shop
     */
    private void buildAvgPriceAndBusinessState(DealCtx ctx, ShopPBO shop) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.buildAvgPriceAndBusinessState(DealCtx,ShopPBO)");
        // 获取门店基本信息 id是点评的id
        DpPoiDTO dpPoiDTO = ctx.getDpPoiDTO();
        //poiClientWrapper.getDpPoiDTO(ctx.getDpLongShopId(), com.google.common.collect.Lists.newArrayList("shopId", "backMainCategoryPath", "cityId", "power", "businessHours",
        //"avgPrice", "shopPower", "fiveScore", "mainRegionName", "lat", "lng", "shopName", "defaultPic"));
        // 获取门店基本信息 id是点评的id
        if (null != dpPoiDTO) {
            Integer price = dpPoiDTO.getAvgPrice();
            // 设置人均消费价格 来自商详页
            shop.setAvgPrice(buildAvgPrice(price));
            String currentTime = LocalDateTime.now().format(Constants.POIEXT_DATETIME_FORMATTER);
            BizForecastDTO bizForecastDTO = bizHourForecastService.getBizForecast(ctx.getDpLongShopId(), SourceEnum.DIANPING, currentTime);
            BusinessHourParser.BusinessHour businessHour = BusinessHourParser.parseBusinessHour(dpPoiDTO.getPower(), dpPoiDTO.getBusinessHours(), bizForecastDTO == null ? null : bizForecastDTO.getToday());
            // 设置门店 营业状态 来自商详页
            shop.setBusinessState(businessHour.businessStatus);
            // 设置门店营业时间
            shop.setBusinessHour(businessHour.businessHour);
        }
    }

    /**
     * 判断是否来自猜喜
     *
     * @param requestSource
     * @return
     */
    private Boolean fromCAIXI(String requestSource) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.fromCAIXI(java.lang.String)");
        if (RequestSourceEnum.CAI_XI.getSource().equals(requestSource) || RequestSourceEnum.HOME_PAGE.getSource().equals(requestSource)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 构建平均价格
     *
     * @param avgPrice
     * @return
     */
    private String buildAvgPrice(Integer avgPrice) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.buildAvgPrice(java.lang.Integer)");
        String priceText = StringUtils.EMPTY;
        if (avgPrice != null && avgPrice > 0) {
            priceText = "¥" + avgPrice + "/人";
        }
        return priceText;
    }

    private void postProcessShop(DealCtx ctx, ShopPBO shop) {
        PoiInfoCustomizedConfig poiInfoCustomizedConfig = getHidePoiAddressConfig(ctx);
        if (poiInfoCustomizedConfig == null) {
            return;
        }
        if (poiInfoCustomizedConfig.isHidePoiAddress()) {
            shop.setHideAddrEnable(true);
        }
        if (poiInfoCustomizedConfig.getFixPoiAddress() != null) {
            shop.setAddress(poiInfoCustomizedConfig.getFixPoiAddress());
        }
        if (ctx.getModuleConfigsModule() != null && Cons.FOLD_STYLE.equals(ctx.getModuleConfigsModule().getGeneralInfo())) {
            // 门店模块和折叠样式联动
            shop.setShopListTitle("适用门店");
        } else if (poiInfoCustomizedConfig.getFixPoiTitle() != null) {
            shop.setShopListTitle(poiInfoCustomizedConfig.getFixPoiTitle());
        }
        if (poiInfoCustomizedConfig.isHidePoiDistance()) {
            shop.setDistance(null);
            shop.setDistanceDesc(null);
        }
        if (poiInfoCustomizedConfig.isHideMapUrl()) {
            shop.setMapUrl(null);
        }
        if (poiInfoCustomizedConfig.getPoiBizType() != null) {
            shop.setShopBizType(poiInfoCustomizedConfig.getPoiBizType());
        }
    }

    private boolean isLyyShop(boolean isMt, long mtShopId) {
        if (!isMt) {
            return false;
        }

        List<Long> lyyShopIds = Lion.getList(LionConstants.LYY_SHOP_IDS, Long.class, new ArrayList<>());

        return CollectionUtils.isNotEmpty(lyyShopIds) && lyyShopIds.contains(mtShopId);
    }

    private PoiInfoCustomizedConfig getHidePoiAddressConfig(DealCtx ctx) {
        DealGroupChannelDTO channelDTO = ctx.getChannelDTO();
        DpPoiDTO dpPoiDTO = ctx.getDpPoiDTO();
        List<PoiInfoCustomizedConfig> hidePoiConfigs = Lion.getList(LionConstants.POI_ADDRESS_CUSTOMIZED_CONFIG, PoiInfoCustomizedConfig.class);
        if (CollectionUtils.isEmpty(hidePoiConfigs)) {
            return null;
        }
        for (PoiInfoCustomizedConfig hidePoiAddrConfig : hidePoiConfigs) {
            boolean isPlatformMatch = hidePoiAddrConfig.getPlatforms() == null || (hidePoiAddrConfig.getPlatforms().contains(ctx.isMt() ? "MT" : "DP"));
            boolean isClientTypeMatch = hidePoiAddrConfig.getClientTypes() == null || hidePoiAddrConfig.getClientTypes().contains(ctx.getEnvCtx().getClientType());
            boolean isDealCategoryMatch = hidePoiAddrConfig.getDealCategories() == null || (channelDTO != null && hidePoiAddrConfig.getDealCategories().contains(channelDTO.getCategoryId()));
            boolean isPoiCategoryMatch;
            if (hidePoiAddrConfig.getPoiUseTypes() != null) {
                dpPoiDTO = dpPoiDTO != null ? dpPoiDTO : poiClientWrapper.getDpPoiDTO(ctx.getDpLongShopId(), QueryParams.SINAI_DP_POI_FIELDS);
                isPoiCategoryMatch = dpPoiDTO != null && hidePoiAddrConfig.getPoiUseTypes().contains(dpPoiDTO.getUseType());
            } else {
                isPoiCategoryMatch = true;
            }

            if (isClientTypeMatch && isPlatformMatch && isDealCategoryMatch && isPoiCategoryMatch) {
                return hidePoiAddrConfig;
            }
        }
        return null;
    }

    private boolean hideAddrEnable(DealCtx ctx) {
        DealGroupChannelDTO channel = ctx.getChannelDTO();
        long poiId4P = ctx.getLongPoiId4PFromResp();

        if (channel == null || channel.getChannelDTO() == null || poiId4P <= 0) {
            return false;
        }

        String channelStr = "ch" + channel.getChannelDTO().getChannelId();
        String categoryStr = "ca" + channel.getCategoryId();

        List<String> configs = Lion.getList(LionConstants.HIDE_POI_ADDRESS_CFG, String.class, new ArrayList<>());

        if (CollectionUtils.isEmpty(configs) || (!configs.contains(channelStr) && !configs.contains(categoryStr))) {
            return false;
        }

        long mtShopId;

        if (ctx.isMt()) {
            mtShopId = poiId4P;
        } else {
            mtShopId = mapperWrapper.getMtShopIdByDpShopIdLong(poiId4P);
        }

        List<PoiModelL> poiModels = poiClientWrapper.batchPoiInfoL(Collections.singletonList(mtShopId), Collections.singletonList(PoiFields.POI_SENSITIVE_LEVEL));

        if (CollectionUtils.isEmpty(poiModels)) {
            return false;
        }

        PoiModelL poiModel = poiModels.get(0);
        Map<String, Long> poiSensitiveLevel = poiModel.getPoiSensitiveLevel();

        return MapUtils.isNotEmpty(poiSensitiveLevel) && poiSensitiveLevel.get("HIDE_ADDR") != null && poiSensitiveLevel.get("HIDE_ADDR") == 1;

    }

    private List<String> getPhoneNos(BestShopDTO bestShop, DealCtx ctx) {
        List<String> phoneNos;
        if (ctx.getEnvCtx().isMainApp() && ctx.getPoiPhones() != null && !ctx.getPoiPhones().isEmpty()) {
            phoneNos = ctx.getPoiPhones();
        } else {
            phoneNos = bestShop.getPhoneNos();
        }
        return filterPhoneNos(ctx, phoneNos);
    }

    private List<String> filterPhoneNos(DealCtx ctx, List<String> phoneNos) {
        List<Integer> filterPhoneNoPoiCategories = Lion.getList(LionConstants.FILTER_PHONE_NO_POI_CATEGORIES, Integer.class, Collections.emptyList());

        if (CollectionUtils.isEmpty(phoneNos)
                || CollectionUtils.isEmpty(ctx.getPoiBackCategoryIds())
                || Sets.intersection(new HashSet<>(filterPhoneNoPoiCategories), ctx.getPoiBackCategoryIds()).isEmpty()) {
            return phoneNos;
        }
        return phoneNos.stream()
                .filter(number -> StringUtils.isNotBlank(number) && !number.startsWith("400") && !number.startsWith("800"))
                .collect(Collectors.toList());
    }

    private void setDisplayPosition(DealCtx ctx, ShopPBO shop) {
        if (ctx.getModuleConfigsModule() != null && Cons.FOLD_STYLE.equals(ctx.getModuleConfigsModule().getGeneralInfo())) {
            // 门店模块和折叠样式联动
            shop.setDisplayPosition(DisplayPositionEnum.BEFORE_DETAIL.getCode());
            return;
        }
        if (!RequestSourceEnum.HOME_PAGE.getSource().equals(ctx.getRequestSource())
                && !RequestSourceEnum.CAI_XI.getSource().equals(ctx.getRequestSource())
                && !LionConfigUtils.isCostEffectiveByWhiteCategory(ctx)) {
            return;
        }

        Set<Integer> shopAtTopConfig = LionFacade
                .getSet(LionConstants.SHOP_AT_TOP_CATEGORY_IDS, Integer.TYPE, Collections.emptySet());
        if (ctx.getChannelDTO() != null && shopAtTopConfig.contains(ctx.getChannelDTO().getCategoryId()) ) {
            shop.setDisplayPosition(DisplayPositionEnum.BEFORE_DETAIL.getCode());
        }
    }

    private List<ComBtn> buildRecallBtns(DealCtx ctx) {
        ShopBookDto shopBookDto = ctx.getShopBookDto();
        if (shopBookDto == null || !shopBookDto.isHasBook()) {
            return null;
        }
        ComBtn btn = new ComBtn();
        btn.setTitle(shopBookDto.getBookActionName());
        btn.setClickUrl(shopBookDto.getBookUrl());
        btn.setActionType(ComBtn.ActionEnum.REDIRECT.type);
        return Lists.newArrayList(btn);
    }

    private List<String> getSpecialFeatures(DealCtx ctx) {
        List<String> specialFeatures = Lists.newArrayList();
        if (ORAL_TEETH_CATEGORY.contains(ctx.getCategoryId())) {
            specialFeatures = getPhysicianOperationSpecialTagId(ctx);
        }
        return specialFeatures;
    }


    private List<String> getFeatures(DealCtx ctx) {
        List<String> result = Lists.newArrayList();
        if (DealAttrHelper.isWuyoutong(ctx)) {
            return Collections.emptyList();
        }

        if (checkRefundByProduct(ctx)) {
            result.add("未预约可退");
        } else if (ctx.getDealGroupBase().getAutoRefundSwitch() > 0) {
            result.add("随时退");
        }
        if (ctx.getDealGroupBase().isOverdueAutoRefund()) {
            result.add("过期退");
        }

        result.addAll(getReservationInfo(ctx));

        String applicableTime = getApplicableTimeDesc(ctx);

        if (applicableTime != null) {
            result.add(applicableTime);
        }

        String applicablePeople = getApplicablePeopleDesc(ctx);

        if (applicablePeople != null) {
            result.add(applicablePeople);
        }

        if (applicablePeople == null && applicableTime == null && ctx.isPurchaseCanBook()) {
            result.add("购后可在线预约");
        }

        if (physicalExamReserveOnline(ctx)) {
            result.add("可在线预约");
        }

        if (parentChildFunReserveOnline(ctx)) {
            result.add("在线预约");
        }

        //如果快照类目设置了团购在线预约,展示在线预约
        if (photoReserveOnline(ctx)) {
            removeTags(result);
            result.add("在线预约");
        }
        return result;
    }

    private List<String> getReservationInfo(DealCtx ctx) {
        List<String> reservationInfo = new ArrayList<>();

        String reservation = null;
        //从上单信息（团单属性）中判断是否需预约、是否支持上门、是否支持到店
        boolean needReservation = DealAttrHelper.needReservation(ctx.getAttrs());
        boolean supportHome = DealAttrHelper.isSupportHomeService(ctx.getAttrs());
        boolean supportShop = DealAttrHelper.isSupportShopService(ctx.getAttrs());

        if (ReserveProductWrapper.reserveAfterPurchase(ctx.getCategoryId())) {
            if (reserveOnline(ctx)) {
                reservation = "在线预约";
            } else if (supportHome) {
                reservation = "预约上门";
            } else if (supportShop && needReservation) {
                reservation = "预约到店";
            } else if (needReservation) {
                reservation = "需预约";
            }
        } else if (needReservation) {
            reservation = "需预约";
        }
        if (reservation != null) {
            reservationInfo.add(reservation);
        }

        if (!DealAttrHelper.needReservation(ctx.getAttrs()) && !forceReserve(ctx.getCategoryId())) {
            reservationInfo.add("免预约");
        }
        return reservationInfo;
    }


    private boolean checkRefundByProduct(DealCtx ctx) {
        if (!LionConfigUtils.hitCustomRefundCategoryConfig(ctx.getCategoryId())) {
            return false;
        }

        List<String> attrValueList = DealAttrHelper.getAttributeValues(ctx.getAttrs(), "reservation_policy");
        if (CollectionUtils.isEmpty(attrValueList)) {
            return false;
        }

        String refundDesc = attrValueList.iterator().next();
        return StringUtils.equals("预约成功后不可退改", refundDesc);
    }

    //针对快照删除所有可能冲突的预约文案,如购后可在线预约、需预约、免预约
    private void removeTags(List<String> result) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.removeTags(java.util.List)");
        if (null == result) {
            return;
        }
        result.remove("需预约");
        result.remove("免预约");
        result.remove("可在线预约");
        result.remove("购后可在线预约");
    }

    /**
     * 亲子游乐在线预约
     *
     * @param ctx 团单信息的环境变量
     * @return 商家是否设置在线预约
     */
    public boolean parentChildFunReserveOnline(DealCtx ctx) {
        if (!PARENT_CHILD_FUN.contains(ctx.getCategoryId())) {
            return false;
        }
        boolean bookable = false;
        BookStatusQueryGatewayReqDTO reqDTO = new BookStatusQueryGatewayReqDTO();
        reqDTO.setSubjectId((long) ctx.getDpId());
        reqDTO.setSubjectType(SubjectTypeEnum.DEAL_GROUP.getCode());
        reqDTO.setScene(BookStatusSceneEnum.TRADE.getCode());
        BookStatusQueryGatewayRespDTO respDTO;
        try {
            respDTO = bookStatusGatewayService.query(reqDTO);
        } catch (TException e) {
            logger.error("BookStatusGatewayService Query Error!", e);
            FaultToleranceUtils.addException("query", e);
            return false;
        }
        if (ContentGatewayCodeEnum.SUCESS.getValue() == respDTO.getCommonResp().getCode()) {
            bookable = respDTO.getBookable();
        }
        return bookable;
    }

    private String getApplicableTimeDesc(DealCtx ctx) {
        if (CollectionUtils.isEmpty(ctx.getAttrs())) {
            return null;
        }
        //口腔齿科定制化适用时间展示tag
        if (DealAttrHelper.workDayAvailable(ctx.getAttrs())) {
            return ORAL_TEETH_CATEGORY.contains(ctx.getCategoryId()) ? "限工作日" : "仅工作日可用";
        }
        if (DealAttrHelper.allDayAvailable(ctx.getAttrs()) && ORAL_TEETH_CATEGORY.contains(ctx.getCategoryId())) {
            return "周末节假日通用";
        }
        return null;
    }

    private List<String> getPhysicianOperationSpecialTagId(DealCtx ctx) {
        List<String> resultName = new ArrayList<>();
        if (CollectionUtils.isEmpty(ctx.getAttrs())) {
            return resultName;
        }
        List<String> resultTagIdList = DealAttrHelper.getAttributeValues(ctx.getAttrs(), DealAttrKeys.ORAL_DENTISTRY_RULE);
        if (CollectionUtils.isEmpty(resultTagIdList)) {
            return resultName;
        }
        Map<String, String> specialTagConfig = getSpecialTagConfig();
        if (MapUtils.isEmpty(specialTagConfig)) {
            return resultName;
        }
        for (String tagIdStr : resultTagIdList) {
            String tagName = specialTagConfig.get(tagIdStr);
            if (StringUtils.isNotBlank(tagName)) {
                resultName.add(tagName);
            }
        }
        return resultName;
    }

    private Map<String, String> getSpecialTagConfig() {
        Map<String, String> config = Lion.getMap(LionConstants.SPECIAL_TAG_CONFIG);
        return config;
    }

    private String getApplicablePeopleDesc(DealCtx ctx) {
        if (CollectionUtils.isEmpty(ctx.getAttrs())) {
            return null;
        }
        List<String> peopleApplicable = DealAttrHelper.getAttributeValues(ctx.getAttrs(), DealAttrKeys.TOOTH_SUIT_PEOPLE);

        if (CollectionUtils.isEmpty(peopleApplicable)) {
            return null;
        }
        if (peopleApplicable.contains("成人") && peopleApplicable.contains("儿童")) {
            return "成人/儿童通用";
        }
        if (peopleApplicable.contains("成人")) {
            return "限成人";
        }
        if (peopleApplicable.contains("儿童")) {
            return "限儿童";
        }
        return null;
    }

    /**
     * 体检中心预约
     *
     * @param ctx
     * @return
     */
    private boolean physicalExamReserveOnline(DealCtx ctx) {
        String config = ctx.isMt() ? "mt" + ctx.getMtLongShopId() : "dp" + ctx.getDpLongShopId();
        List<String> configs = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb.physical.exam.third.party.shops", String.class, new ArrayList<>());

        boolean isCfgShop = CollectionUtils.isNotEmpty(configs) && configs.contains(config);
        boolean isThirdPartyDealGroup = dealGroupWrapper.isThirdPartyDealGroup(ctx.getFutureCtx().getDealGroupThirdPartyFuture());

        return isThirdPartyDealGroup && isCfgShop;
    }

    /**
     * 是否强制预约
     *
     * @param categoryId
     * @return
     */
    private boolean forceReserve(int categoryId) {
        List<Integer> config = Lion.getList(LionConstants.FORCE_BOOKING_PUBLISH_CATEGORY, Integer.class, Lists.newArrayList());
        return CollectionUtils.isNotEmpty(config) && config.contains(categoryId);
    }

    private void putSmallHead(DealCtx ctx) {
        if (StringUtils.isEmpty(ctx.getExpResults())) {
            return;
        }
        List<String> results = JsonFacade.deserializeList(ctx.getExpResults().toLowerCase(), String.class);
        for (String exp : results) {
            if (Lion.getStringValue("com.sankuai.dzu.tpbase.dztgdetailweb.small.head.exp.results", "exp000002_c").contains(exp)) {
                ctx.getResult().setExtraStyles(Lists.newArrayList("SMALL_HEAD"));
                return;
            }
        }
    }

    /**
     * 调用交易接口trade-general-reserve-api判断当前团单是否支持在线预约
     *
     * @param ctx
     * @return
     */
    private boolean reserveOnline(DealCtx ctx) {
        try {
            ReserveResponse<Boolean> reserveResponse = reserveProductWrapper.getFutureResult(ctx);
            if (reserveResponse != null && reserveResponse.isSuccess()) {
                return reserveResponse.getResult();
            }
        } catch (Exception e) {
            logger.error("reserveOnlineFuture err, dpGroupId : {}", ctx != null ? ctx.getDpId() : 0, e);
            return false;
        }
        return false;
    }

    private static void putDealName(DealCtx ctx, DealGroupPBO result) {
        if (Objects.isNull(ctx.getDealGroupDTO()) || Objects.isNull(ctx.getDealGroupDTO().getBasic())) {
            return;
        }
        result.setDealName(ctx.getDealGroupDTO().getBasic().getTitle());
    }

    private static void putVoucherInfo(DealCtx ctx, DealGroupPBO result) {
        if (!Lion.getList(LionConstants.VOUCHER_DEAL_CATEGORY_IDS, Integer.TYPE, Collections.emptyList())
                .contains(result.getCategoryId())) {
            return;
        }
        if (!DealAttrHelper.isVoucher(ctx.getAttrs())) {
            return;
        }
        if (!isGray(ctx)) {
            return;
        }
        String promoTag = getDiscountTitle(ctx);
        if (StringUtils.isNotEmpty(promoTag)) {
            result.setPromoTags(Lists.newArrayList(new DealBuyBtnIcon(promoTag, VoucherHelper.PROMO_COLOR, VoucherHelper.PROMO_COLOR, 1)));
        }
        result.setType(1);
        resetVoucherTitle(ctx, result);
        result.setFeatureTag(buildFeatureTag(ctx, result));
        result.setModuleBackgroundColor(ctx.isMt() ? VoucherHelper.MT_MODULE_BACKGROUND : VoucherHelper.DP_MODULE_BACKGROUND);//背景图
    }

    private static String buildFeatureTag(DealCtx ctx, DealGroupPBO dealGroupPBO) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.buildFeatureTag(DealCtx,DealGroupPBO)");
        String featureTag = "";
        if (Lion.getList(LionConstants.VOUCHER_DEAL_CUSTOM_STYLE_CATEGORY_IDS, Integer.TYPE, Collections.emptyList())
                .contains(dealGroupPBO.getCategoryId())) {
            //使用时间限制
            if (DealAttrHelper.allDayAvailable(ctx.getAttrs())) {
                featureTag = appendFeatureTag(featureTag, "周末节假日通用");
            } else if (DealAttrHelper.workDayAvailable(ctx.getAttrs())) {
                featureTag = appendFeatureTag(featureTag, "周末不可用");
            } else if (DealAttrHelper.partHolidayAvailable(ctx.getAttrs())) {
                featureTag = appendFeatureTag(featureTag, "部分节假日不可用");
            } else if (DealAttrHelper.allHolidayDisable(ctx.getAttrs())) {
                featureTag = appendFeatureTag(featureTag, "节假日均不可用");
            }
            //代金券是否全场通用
            if (DealAttrHelper.allCanUse(ctx.getAttrs())) {
                featureTag = appendFeatureTag(featureTag, "全场通用");
            }
            //单次限用数量
            if (CollectionUtils.isNotEmpty(DealAttrHelper.getAttributeValues(ctx.getAttrs(), DealAttrKeys.LIMIT_OF_USING_EACH_EYE))) {
                featureTag = appendFeatureTag(featureTag, "单次可用" + DealAttrHelper.getAttributeValues(ctx.getAttrs(), DealAttrKeys.LIMIT_OF_USING_EACH_EYE).get(0) + "张");
            }

            //是否需要预约
            if (DealAttrHelper.needReservation(ctx.getAttrs())) {
                featureTag = appendFeatureTag(featureTag, "需要预约");
            } else {
                featureTag = appendFeatureTag(featureTag, "免预约");
            }
            return featureTag;
        }

        if (DealAttrHelper.holidayAvailable(ctx.getAttrs())) {
            featureTag += "节假日通用";
        }
        if (DealAttrHelper.availableAll(ctx.getAttrs())) {
            if (StringUtils.isNotEmpty(featureTag)) {
                featureTag += " | ";
            }
            featureTag += "不限时段";
        }
        if (DealAttrHelper.noTimesLimit(ctx.getAttrs())) {
            if (StringUtils.isNotEmpty(featureTag)) {
                featureTag += " | ";
            }
            featureTag += "可叠加使用";
        }
        if (DealAttrHelper.allCanUse(ctx.getAttrs())) {
            if (StringUtils.isNotEmpty(featureTag)) {
                featureTag += " | ";
            }
            featureTag += "全场通用";
        }
        return featureTag;
    }

    private static String appendFeatureTag(String featureTag, String value) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.appendFeatureTag(java.lang.String,java.lang.String)");
        if (StringUtils.isNotEmpty(featureTag)) {
            featureTag += " | ";
        }
        featureTag += value;
        return featureTag;
    }

    private static String getDiscountTitle(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.getDiscountTitle(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        if (ctx.getDealGroupBase() == null || ctx.getDealGroupBase().getDealGroupPrice() == null || BigDecimal.ZERO.compareTo(ctx.getDealGroupBase().getDealGroupPrice()) >= 0) {
            return null;
        }
        BigDecimal salePrice = ctx.getDealGroupBase().getDealGroupPrice() == null ? BigDecimal.ZERO : ctx.getDealGroupBase().getDealGroupPrice();
        BigDecimal marketPrice = ctx.getDealGroupBase().getMarketPrice() == null ? BigDecimal.ZERO : ctx.getDealGroupBase().getMarketPrice();
        BigDecimal discount = salePrice.divide(marketPrice, 2, RoundingMode.HALF_UP).multiply(new BigDecimal(10));
        if (BigDecimal.ZERO.compareTo(discount) >= 0 || discount.compareTo(new BigDecimal(9.9)) >= 0) {
            return null;
        }
        return PriceHelper.dropLastZero(discount) + "折";
    }

    private static void resetVoucherTitle(DealCtx ctx, DealGroupPBO result) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.resetVoucherTitle(DealCtx,DealGroupPBO)");
        if (ctx.getDealGroupBase() == null || ctx.getDealGroupBase().getDealGroupPrice() == null || BigDecimal.ZERO.compareTo(ctx.getDealGroupBase().getDealGroupPrice()) >= 0) {
            return;
        }
        BigDecimal salePrice = ctx.getDealGroupBase().getDealGroupPrice() == null ? BigDecimal.ZERO : ctx.getDealGroupBase().getDealGroupPrice();
        BigDecimal marketPrice = ctx.getDealGroupBase().getMarketPrice() == null ? BigDecimal.ZERO : ctx.getDealGroupBase().getMarketPrice();
        result.setTitle(PriceHelper.dropLastZero(salePrice) + "元代" + PriceHelper.dropLastZero(marketPrice) + "元");
    }

    private static boolean isGray(DealCtx ctx) {
        String grayId = ctx.isMt() ? ctx.getEnvCtx().getUuid() : ctx.getEnvCtx().getDpId();
        if (StringUtils.isEmpty(grayId) || grayId.length() <= 0) {
            return false;
        }
        try {
            if (Lion.getBooleanValue(LionConstants.GRAY_ENABLE, false)) {
                Map<String, String> grayConfig = Lion.getMap(LionConstants.GRAY_CONFIG);
                if (ctx.isMt()) {
                    if (grayConfig.containsKey("mt") && StringUtils.isNotEmpty(grayConfig.get("mt"))) {
                        return grayConfig.get("mt").contains(grayId.substring(grayId.length() - 1).toLowerCase());
                    }
                } else {
                    if (grayConfig.containsKey("dp") && StringUtils.isNotEmpty(grayConfig.get("dp"))) {
                        return grayConfig.get("dp").contains(grayId.substring(grayId.length() - 1).toLowerCase());
                    }
                }
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }

    private static void putOnlineConsult(DealGroupPBO result) {
        if (result == null || result.getShop() == null) {
            return;
        }
        if (displayOnlineConsult(result.getBuyBar())) {
            result.setOnlineConsultUrl(result.getShop().getImUrl());
        }
    }

    private static void putOriginalOnlineConsult(DealGroupPBO result) {
        if (result == null || result.getShop() == null) {
            return;
        }
        try {
            if (displayOnlineConsult(result.getBuyBar())) {
                String fullURL = result.getShop().getImUrl();
                if (fullURL == null) {
                    return;
                }
                int start = fullURL.indexOf("?url=");
                if (start == -1) {
                    result.setOriginalOnlineConsultUrl(URLDecoder.decode(fullURL, "UTF-8"));
                } else {
                    String urlWithoutPrefix = fullURL.substring(start + "?url=".length());
                    result.setOriginalOnlineConsultUrl(URLDecoder.decode(urlWithoutPrefix, "UTF-8"));
                }
            }
        } catch (Exception e) {
            log.error("putOriginalOnlineConsult error, e = ", e);
        }
    }

    private static boolean displayOnlineConsult(DealBuyBar dealBuyBar) {
        if (dealBuyBar.getStyleType() == SHOPPING_CART.code) {
            Set<Integer> priceRuleTypes = Sets.newHashSet();
            for (DealBuyBtn buyBtn : dealBuyBar.getBuyBtns()) {
                if (buyBtn != null && buyBtn.getPriceRuleModule() != null) {
                    priceRuleTypes.add(buyBtn.getPriceRuleModule().getPriceRuleType());
                }
            }
            return dealBuyBar.getBuyBtns().size() == 1 || priceRuleTypes.contains(BuyBtnTypeEnum.PINTUAN.getCode()) || priceRuleTypes.contains(BuyBtnTypeEnum.TIMES_CARD.getCode());
        } else {
            return dealBuyBar != null && (dealBuyBar.getBuyBtns().size() == 1 || dealBuyBar.getBuyType() == DealBuyBar.BuyType.PINPOOL.type || dealBuyBar.getBuyType() == DealBuyBar.BuyType.TIMESCARD.type);
        }
    }

    private BatchQueryDealActivityRequest buildBatchQueryDealActivityReq(DealCtx ctx) {
        BatchQueryDealActivityRequest request = new BatchQueryDealActivityRequest();
        if (ctx.isMt()) {
            request.setMtDealIds(com.google.common.collect.Lists.newArrayList(ctx.getMtId()));
            request.setMtCity(ctx.getMtCityId());
            request.setUserIdL(ctx.getEnvCtx().getMtUserId());//已确认判断平台后再使用
            request.setAppPlatform(AppPlatform.MT);
        } else {
            request.setDpDealIds(com.google.common.collect.Lists.newArrayList(ctx.getDpId()));
            request.setDpCity(ctx.getDpCityId());
            request.setUserIdL(ctx.getEnvCtx().getDpUserId());
            request.setAppPlatform(AppPlatform.DP);
        }
        request.setSource(RequestSource.TuanDetail);
        request.setVersion(new Version(ctx.getEnvCtx().getVersion()));
        request.setChannel(ExposeChannel.App.code);
        return request;
    }

    private void filterBuyButtonEnable(DealCtx ctx) {
        if (CollectionUtils.isEmpty(ctx.getAttrs())) {
            return;
        }

        for (AttributeDTO attr : ctx.getAttrs()) {
            // 如果是宠物加购代金券，团祥不能购买、不可分享，只能在下单页选择加购
            if (isBuyAndShareDisabled(attr)) {
                ctx.getResult().setShareAble(false);
                for (DealBuyBtn buyBtn : ctx.getResult().getBuyBar().getBuyBtns()) {
                    buyBtn.setBtnEnable(false);
                }
                break;
            }
        }
    }

    private void putShareAble(DealCtx ctx, DealGroupPBO result) {
        // 渠道专享立减，禁用分享按钮
        if (ctx.getPriceContext().isHasExclusiveDeduction()) {
            result.setShareAble(false);
        }
    }

    private boolean isBuyAndShareDisabled(AttributeDTO attr) {

        if (CollectionUtils.isEmpty(attr.getValue())) {
            return false;
        }

        Map<String, String> configMap = LionFacade.getMap(LionConstants.DISABLE_BUY_AND_SHARE_ATTR,
                String.class, String.class, Collections.emptyMap());

        if (MapUtils.isEmpty(configMap)) {
            return false;
        }

        String configValue = configMap.get(attr.getName());

        return Objects.equals(attr.getValue().get(0), configValue);
    }

    private void buildCardState(DealCtx ctx, DealGroupPBO result) {
        int shopState = 0;
        int userState = 0;

        if (ctx.getPriceContext().getDcCardMemberCard() != null) {
            shopState += 1;
            if (CardHelper.holdCard(ctx.getPriceContext().getDcCardMemberCard())) {
                userState += 1;
            }
        }

//        if (ctx.getPriceContext().getJoyCard() != null) {
//            shopState += 2;
//            if (CardHelper.holdCard(ctx.getPriceContext().getJoyCard())) {
//                userState += 2;
//            }
//        }

        result.setShopCardState(shopState == 0 ? 4 : shopState);
        result.setUserCardState(userState == 0 ? 4 : userState);
    }

    private boolean hideStarsEnable(DealCtx ctx) {
        DpPoiDTO dpPoiDTO = ctx.getDpPoiDTO();
        if (dpPoiDTO != null && dpPoiDTO.getHospitalInfo() != null) {
            HospitalInfo hospitalInfo = dpPoiDTO.getHospitalInfo();
            if (Objects.equals(hospitalInfo.getNature(), 0)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 仅快照类目,团单需预约，且当前适用门店可进行在线预约
     *
     * @param ctx 团单信息的环境变量
     * @return 商家是否设置在线预约
     */
    public boolean photoReserveOnline(DealCtx ctx) {
        if (!LionConfigUtils.isSnapShotPhoto(ctx.getCategoryId())) {
            return false;
        }
        return ctx.isShowReserveEntrance();
    }

    private void hideMtMiniAppInfo(DealCtx ctx) {
        if (MTMiniAppHideInformationHelper.needHideInformation(ctx.getEnvCtx())) {
            hideSaleInfo(ctx);
            hidePriceInfo(ctx);
        }
    }

    private void hideSaleInfo(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.hideSaleInfo(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        ctx.getResult().setSaleDesc(null);
        ctx.getResult().setSaleDescStr(null);
    }

    private void hidePriceInfo(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor.hidePriceInfo(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        for (DealBuyBtn btn : ctx.getResult().getBuyBar().getBuyBtns()) {
            btn.setPriceStr(null);
            btn.setBtnDesc(null);
            btn.setBtnIcons(null);
        }
    }

    private FeaturesLayer getFeaturesLayer(DealCtx ctx) {
        FeaturesLayer featuresLayer = new FeaturesLayer();

        ObjectGuaranteeTagDTO priceProtectionInfo = ctx.getPriceProtectionInfo();

        if (priceProtectionInfo != null) {
            PriceProtectionTagDTO priceProtectionTagDTO = priceProtectionInfo.getPriceProtectionTag();
            if (priceProtectionTagDTO != null
                    && priceProtectionTagDTO.getValid() != null
                    && priceProtectionTagDTO.getValid()
                    && priceProtectionTagDTO.getValidityDays() != null
            ) {
                featuresLayer.setPriceProtectionTag(priceProtectionTagDTO.getValidityDays() + "天");
            }
        }

        List<LayerConfig> layerConfigs;
        String lionKey = ctx.isMt() ?
                (checkRefundByProduct(ctx) ? LionConstants.LAYER_CONFIGS_MT_UNRETURN : LionConstants.LAYER_CONFIGS_MT) :
                (checkRefundByProduct(ctx) ? LionConstants.LAYER_CONFIGS_DP_UNRETURN : LionConstants.LAYER_CONFIGS_DP);

        layerConfigs = Lion.getList(LionConstants.APP_KEY, lionKey, LayerConfig.class);

        if (CollectionUtils.isNotEmpty(layerConfigs)) {
            for (LayerConfig layerConfig : layerConfigs) {
                if (layerConfig.getType() == 2) {
                    layerConfig.setTitle(layerConfig.getTitle() + featuresLayer.getPriceProtectionTag());
                }
            }
        }
        featuresLayer.setLayerConfigs(layerConfigs);

        return featuresLayer;
    }

}
