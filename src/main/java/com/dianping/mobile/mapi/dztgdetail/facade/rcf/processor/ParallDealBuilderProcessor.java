package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.gmkt.activity.api.dto.DealActivityDTO;
import com.dianping.json.facade.JsonFacade;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.lion.facade.LionFacade;
import com.dianping.mobile.mapi.dztgdetail.biz.DealStyleStatisticService;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.DealDouHuUtil;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.LifeClearUtil;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.ParallDealBuilderProcessorUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.extend.PromoDetailHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.extend.PromoDetailLocator;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.extend.config.PromoDetailConfig;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.extend.config.PromoDetailConfigDto;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.HaimaWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.PriceContext;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ServiceGuarantee.DealBestPromoDetailDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealstruct.PnPurchaseNoteDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealstruct.PnStandardDisplayItemDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealstruct.PurchaseNoteModuleDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.TimesCardPBO;
import com.dianping.mobile.mapi.dztgdetail.entity.*;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.*;
import com.dianping.mobile.mapi.dztgdetail.helper.*;
import com.dianping.mobile.mapi.dztgdetail.util.*;
import com.dianping.mobile.mapi.dztgdetail.util.richText.RichText;
import com.dianping.mobile.mapi.dztgdetail.util.richText.RichTextUtil;
import com.dianping.mobile.mapi.dztgdetail.util.richText.TextStyle;
import com.dianping.product.shelf.common.dto.subjectConfig.ConfigDTO;
import com.dianping.tgc.open.entity.*;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.sankuai.dealuser.price.display.api.enums.*;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.ExtPriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.PriceDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.general.product.query.center.client.dto.deal.rule.readjustPriceRule.ReadjustPriceRuleDTO;
import com.sankuai.general.product.query.center.client.dto.deal.rule.readjustPriceRule.standardPriceRule.StandardPriceRuleItemDTO;
import com.sankuai.general.product.query.center.client.dto.rule.buy.DealGroupBuyRuleDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import com.sankuai.mlive.goods.trade.api.model.GoodsSellingInfoDTO;
import com.sankuai.nibmkt.promotion.api.common.enums.MagicalMemberTagShowTypeEnum;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionTextEnum;
import com.sankuai.nibmkt.promotion.api.common.utils.MagicalMemberTagTextUtils;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MagicalMemberTagTextDTO;
import com.sankuai.nibscp.common.flow.identify.util.SptDyeUtil;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.swan.udqs.api.QueryData;
import com.sankuai.swan.udqs.api.Result;
import com.sankuai.swan.udqs.api.SwanParam;
import com.sankuai.swan.udqs.api.SwanQueryService;
import com.sankuai.technician.category.enums.TechCategoryEnum;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLDecoder;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.*;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.MedicalConstant.GUARANTEEIP_DENTURE;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.MedicalConstant.GUARANTEEIP_IMPANT;
import static com.dianping.mobile.mapi.dztgdetail.common.enums.StyleTypeEnum.SHOPPING_CART;
import static com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper.isPayAndNotOnePrice;
import static com.dianping.mobile.mapi.dztgdetail.util.GlassDealUtils.isGenuineGuarantee;
import static com.dianping.mobile.mapi.dztgdetail.util.OralDealUtils.isSafeDenture;
import static com.dianping.mobile.mapi.dztgdetail.util.OralDealUtils.isSafeImplant;

@Slf4j
public class ParallDealBuilderProcessor extends AbsDealProcessor {

    public static final int NO_LIMIT = 0;
    public static final int LIMIT_ONE = 1;
    public static final int LIMIT_MULTI = 2;
    public static final String REFUND_RULE_7_DAY_LESS = "7天内未学满可退";
    public static final String REFUND_RULE_7_DAY_START = "7天内未学满";
    public static final String DEFAULT_VIP_COUPON_DESC = "神券";
    public static final int MAX_CHARS_PER = 5;
    private static final String DEFAULTPICASPECTRATIO = "default";
    private static final Integer SWAN_QUERY_BIZ_TYPE_ID_1096 = 1096;
    private static final String SWAN_QUERY_BIZ_KEY_USER_PURCHASE_INFO = "dpapp_trade_user_purchase_product_info_d";
    private static final String SUIT_PERSON = "suitable_person_first_not_member";
    private static final String LIMIT_ONE_PERSON = "limit_one";
    private static final String LIMIT_ONE_PERSON_SUIT_PERSON_FIRST_NOT_MEMBER = "limit_one_suit_person_first_not_member";
    private static final String LIMIT_MULTI_PERSON = "limit_multi";
    private static final String LIMIT_MULTI_PERSON_SUIT_PERSON_FIRST_NOT_MEMBER = "limit_multi_suit_person_first_not_member";
    private static final String NO_LIMIT_PERSON = "no_limit";
    private static final String NO_LIMIT_PERSON_SUIT_PERSON_FIRST_NOT_MEMBER = "no_limit_suit_person_first_not_member";
    private static final List<Integer> themeLists = Lists.newArrayList(0, 3, 5, 8);
    private static final String MAGIC_NORMAL_TAG_THEME = "MagicNormalTagTheme";
    private static final String MAGIC_VIP_TAG_THEME = "MagicVipTagTheme";
    private static final String PIN_TUAN_RULE_PAY = "pinTuanRule_pay";
    private static final String PIN_TUAN_RULE_INVITE = "pinTuanRule_invite";
    private static final String PIN_TUAN_RULE_INVITE_NEW = "pinTuanRule_invite_new";
    private static final String PIN_TUAN_RULE_COMPLETE = "pinTuanRule_complete";
    private static final String TEXT = "text";
    private static final String ICON = "icon";
    private static final String DETAILED_TIPS = "实际方案与报价以商家上门测量为准";
    //优惠详情组装信息（最长不超过10行，每行字数<=5）
    private static final int MAX_CHAR = 10;
    @Resource
    private SwanQueryService swanQueryService;
    @Resource
    private ButtonStyleHelper buttonStyleHelper;
    @Resource
    private DouHuService douHuService;
    @Resource
    private BasicInfoBuilderService basicInfoBuilderService;
    @Resource
    private DealContentBuilderService dealContentBuilderService;
    @Resource
    private GuaranteeBuilderService guaranteeBuilderService;
    @Resource
    private ShopBuilderService shopBuilderService;
    @Resource
    private ModuleAbBuilderService moduleAbBuilderService;
    @Resource
    private SaleBuilderService saleBuilderService;
    @Resource
    private AtmospherBuilderService atmospherBuilderService;
    @Resource
    private BuyBarBuilderService buyBarBuilderService;
    @Resource
    private ShareBuilderService shareBuilderService;
    @Resource
    private AbBizBuilderService abBizBuilderService;
    @Resource
    private PromotionDetailBuilderService promotionDetailBuilderService;

    @Resource
    private PromoDetailModuleBuilderService promoDetailModuleBuilderService;

    @Resource
    private HaimaWrapper haimaWrapper;

    @Resource
    private PriceExplainBuilderService priceExplainBuilderService;

    @Resource
    private SpecialParamsForPayBuilderService specialParamsForPayBuilderService;

    @Resource
    private ReminderInfoBuilderService reminderInfoBuilderService;

    @Resource
    private RemindBuilderService remindBuilderService;

    @Resource
    private PassParamBuilderService passParamBuilderService;

    @Resource
    private FeatureLayerBuilderService featureLayerBuilderService;

    @Resource
    private HeadPicAtmosphereBuilderService headPicAtmosphereBuilderService;

    @Resource
    private CommonModuleBuilderService commonModuleBuilderService;

    @Resource
    private PreOrderParamBuilderService preOrderParamBuilderService;

    @Resource
    private ProductDetailBuilderService productDetailBuilderService;

    @Resource
    private DealStyleStatisticService dealStyleStatisticService;

    private Gson gson = new Gson();

    private static String getNibBiz(int categoryId) {
        Map<String, String> categoryIdMap = Lion.getMap("com.sankuai.dzu.tpbase.dztgdetailweb.svip.categoryid", String.class);
        return categoryIdMap.getOrDefault(String.valueOf(categoryId), null);
    }

    private static Pair<String, String> getValidTimeText(Date startTime, Date endTime) {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
            String startTimeStr = dateFormat.format(startTime);
            String endTimeStr = dateFormat.format(endTime);
            long currentTime = System.currentTimeMillis();
            if (currentTime > endTime.getTime()) {
                //已过期
                return new Pair<>(startTimeStr + " 至 " + endTimeStr, null);
            } else {
                if (endTime.getTime() - currentTime > 24 * 3600 * 1000) {
                    return new Pair<>(endTimeStr + " 到期", null);
                } else {
                    return new Pair<>("今日到期，仅剩", String.valueOf(endTime.getTime()));
                }

            }
        } catch (Exception e) {
            Cat.logError(e);
            log.error("getValidTimeText error", e);
        }

        return null;
    }

    public static int getCouponType(List<Integer> promotionExplanatoryTags) {
        if (promotionExplanatoryTags.contains(PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON.getCode())) {
            return CouponTypeEnum.PAY_COUPON.getCode();
        } else if (promotionExplanatoryTags.contains(PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON_FREE.getCode())) {
            return CouponTypeEnum.FREE_COUPON.getCode();
        } else {
            return CouponTypeEnum.UN_KNOWN.getCode();
        }

    }

    /**
     * 判断券是否在可用时间内
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static int getCouponValidStatus(Date startTime, Date endTime) {
        Date nowDate = new Date();
        if (nowDate.after(startTime) && nowDate.before(endTime)) {
            return CouponValidStatusEnum.VALID.getCode(); //有效
        }
        return CouponValidStatusEnum.UN_VALID.getCode(); //无效
    }

    /**
     * 计算折扣率（保留一位小数 + 向上取整）
     */
    private static BigDecimal calcDiscountRate(BigDecimal marketPrice, BigDecimal finalPrice) {
        if (marketPrice == null || finalPrice == null || marketPrice.compareTo(BigDecimal.ZERO) == 0
                || finalPrice.compareTo(BigDecimal.ZERO) == 0 || finalPrice.compareTo(marketPrice) == 0
                || marketPrice.compareTo(finalPrice) < 0) {
            return null;
        }
        return finalPrice.divide(marketPrice, 2, RoundingMode.CEILING).multiply(new BigDecimal(10)).setScale(1, RoundingMode.CEILING);
    }

    private static void putDealName(DealCtx ctx, DealGroupPBO result) {
        if (Objects.isNull(ctx.getDealGroupDTO()) || Objects.isNull(ctx.getDealGroupDTO().getBasic())) {
            return;
        }
        result.setDealName(ctx.getDealGroupDTO().getBasic().getTitle());
    }

    private static void putVoucherInfo(DealCtx ctx, DealGroupPBO result) {
        if (!Lion.getList(LionConstants.VOUCHER_DEAL_CATEGORY_IDS, Integer.TYPE, Collections.emptyList())
                .contains(result.getCategoryId())) {
            return;
        }
        if (!DealAttrHelper.isVoucher(ctx.getAttrs())) {
            return;
        }
        if (!isGray(ctx)) {
            return;
        }
        String promoTag = getDiscountTitle(ctx);
        if (StringUtils.isNotEmpty(promoTag)) {
            result.setPromoTags(Lists.newArrayList(new DealBuyBtnIcon(promoTag, VoucherHelper.PROMO_COLOR, VoucherHelper.PROMO_COLOR, 1)));
        }
        result.setType(1);
        resetVoucherTitle(ctx, result);
        result.setFeatureTag(buildFeatureTag(ctx, result));
        result.setModuleBackgroundColor(ctx.isMt() ? VoucherHelper.MT_MODULE_BACKGROUND : VoucherHelper.DP_MODULE_BACKGROUND);//背景图
    }

    private static String buildFeatureTag(DealCtx ctx, DealGroupPBO dealGroupPBO) {
        String featureTag = "";
        if (Lion.getList(LionConstants.VOUCHER_DEAL_CUSTOM_STYLE_CATEGORY_IDS, Integer.TYPE, Collections.emptyList())
                .contains(dealGroupPBO.getCategoryId())) {
            //使用时间限制
            if (DealAttrHelper.allDayAvailable(ctx.getAttrs())) {
                featureTag = appendFeatureTag(featureTag, "周末节假日通用");
            } else if (DealAttrHelper.workDayAvailable(ctx.getAttrs())) {
                featureTag = appendFeatureTag(featureTag, "周末不可用");
            } else if (DealAttrHelper.partHolidayAvailable(ctx.getAttrs())) {
                featureTag = appendFeatureTag(featureTag, "部分节假日不可用");
            } else if (DealAttrHelper.allHolidayDisable(ctx.getAttrs())) {
                featureTag = appendFeatureTag(featureTag, "节假日均不可用");
            }
            //代金券是否全场通用
            if (DealAttrHelper.allCanUse(ctx.getAttrs())) {
                featureTag = appendFeatureTag(featureTag, "全场通用");
            }
            //单次限用数量
            if (CollectionUtils.isNotEmpty(DealAttrHelper.getAttributeValues(ctx.getAttrs(), DealAttrKeys.LIMIT_OF_USING_EACH_EYE))) {
                featureTag = appendFeatureTag(featureTag, "单次可用" + DealAttrHelper.getAttributeValues(ctx.getAttrs(), DealAttrKeys.LIMIT_OF_USING_EACH_EYE).get(0) + "张");
            }

            //是否需要预约
            if (DealAttrHelper.needReservation(ctx.getAttrs())) {
                featureTag = appendFeatureTag(featureTag, "需要预约");
            } else {
                featureTag = appendFeatureTag(featureTag, "免预约");
            }
            return featureTag;
        }

        if (DealAttrHelper.holidayAvailable(ctx.getAttrs())) {
            featureTag += "节假日通用";
        }
        if (DealAttrHelper.availableAll(ctx.getAttrs())) {
            if (StringUtils.isNotEmpty(featureTag)) {
                featureTag += " | ";
            }
            featureTag += "不限时段";
        }
        if (DealAttrHelper.noTimesLimit(ctx.getAttrs())) {
            if (StringUtils.isNotEmpty(featureTag)) {
                featureTag += " | ";
            }
            featureTag += "可叠加使用";
        }
        if (DealAttrHelper.allCanUse(ctx.getAttrs())) {
            if (StringUtils.isNotEmpty(featureTag)) {
                featureTag += " | ";
            }
            featureTag += "全场通用";
        }
        return featureTag;
    }

    private static String appendFeatureTag(String featureTag, String value) {
        if (StringUtils.isNotEmpty(featureTag)) {
            featureTag += " | ";
        }
        featureTag += value;
        return featureTag;
    }

    private static String getDiscountTitle(DealCtx ctx) {
        if (ctx.getDealGroupBase() == null || ctx.getDealGroupBase().getDealGroupPrice() == null || BigDecimal.ZERO.compareTo(ctx.getDealGroupBase().getDealGroupPrice()) >= 0) {
            return null;
        }
        BigDecimal salePrice = ctx.getDealGroupBase().getDealGroupPrice() == null ? BigDecimal.ZERO : ctx.getDealGroupBase().getDealGroupPrice();
        BigDecimal marketPrice = ctx.getDealGroupBase().getMarketPrice() == null ? BigDecimal.ZERO : ctx.getDealGroupBase().getMarketPrice();
        BigDecimal discount = salePrice.divide(marketPrice, 2, RoundingMode.HALF_UP).multiply(new BigDecimal(10));
        if (BigDecimal.ZERO.compareTo(discount) >= 0 || discount.compareTo(BigDecimal.valueOf(9.9)) >= 0) {
            return null;
        }
        return PriceHelper.dropLastZero(discount) + "折";
    }

    private static void resetVoucherTitle(DealCtx ctx, DealGroupPBO result) {
        if (ctx.getDealGroupBase() == null || ctx.getDealGroupBase().getDealGroupPrice() == null || BigDecimal.ZERO.compareTo(ctx.getDealGroupBase().getDealGroupPrice()) >= 0) {
            return;
        }
        BigDecimal salePrice = ctx.getDealGroupBase().getDealGroupPrice() == null ? BigDecimal.ZERO : ctx.getDealGroupBase().getDealGroupPrice();
        BigDecimal marketPrice = ctx.getDealGroupBase().getMarketPrice() == null ? BigDecimal.ZERO : ctx.getDealGroupBase().getMarketPrice();
        result.setTitle(PriceHelper.dropLastZero(salePrice) + "元代" + PriceHelper.dropLastZero(marketPrice) + "元");
    }

    private static boolean isGray(DealCtx ctx) {
        String grayId = ctx.isMt() ? ctx.getEnvCtx().getUuid() : ctx.getEnvCtx().getDpId();
        if (StringUtils.isEmpty(grayId) || grayId.length() <= 0) {
            return false;
        }
        try {
            if (Lion.getBooleanValue(LionConstants.GRAY_ENABLE, false)) {
                Map<String, String> grayConfig = Lion.getMap(LionConstants.GRAY_CONFIG);
                if (ctx.isMt()) {
                    if (grayConfig.containsKey("mt") && StringUtils.isNotEmpty(grayConfig.get("mt"))) {
                        return grayConfig.get("mt").contains(grayId.substring(grayId.length() - 1).toLowerCase());
                    }
                } else {
                    if (grayConfig.containsKey("dp") && StringUtils.isNotEmpty(grayConfig.get("dp"))) {
                        return grayConfig.get("dp").contains(grayId.substring(grayId.length() - 1).toLowerCase());
                    }
                }
            }
        } catch (Exception e) {
            log.error("isGray error, e = ", e);
            return false;
        }
        return false;
    }

    private static void putOnlineConsult(DealCtx ctx, DealGroupPBO result) {
        if (result == null || result.getShop() == null) {
            return;
        }
        if (displayOnlineConsult(ctx, result.getBuyBar())) {
            result.setOnlineConsultUrl(result.getShop().getImUrl());
        }
    }

    private static void putOriginalOnlineConsult(DealCtx ctx, DealGroupPBO result) {
        if (result == null || result.getShop() == null) {
            return;
        }
        try {
            if (displayOnlineConsult(ctx, result.getBuyBar())) {
                String fullURL = result.getShop().getImUrl();
                if (fullURL == null) {
                    return;
                }
                int start = fullURL.indexOf("?url=");
                if (start == -1) {
                    result.setOriginalOnlineConsultUrl(URLDecoder.decode(fullURL, "UTF-8"));
                } else {
                    String urlWithoutPrefix = fullURL.substring(start + "?url=".length());
                    result.setOriginalOnlineConsultUrl(URLDecoder.decode(urlWithoutPrefix, "UTF-8"));
                }
            }
        } catch (Exception e) {
            log.error("putOriginalOnlineConsult error, e = ", e);
        }
    }

    static boolean displayOnlineConsult(DealCtx ctx, DealBuyBar dealBuyBar) {
        if (dealBuyBar == null) {
            return false;
        }
        if (dealBuyBar.getStyleType() == SHOPPING_CART.code) {
            Set<Integer> priceRuleTypes = Sets.newHashSet();
            for (DealBuyBtn buyBtn : dealBuyBar.getBuyBtns()) {
                if (buyBtn != null && buyBtn.getPriceRuleModule() != null) {
                    priceRuleTypes.add(buyBtn.getPriceRuleModule().getPriceRuleType());
                }
            }
            return dealBuyBar.getBuyBtns().size() == 1 || priceRuleTypes.contains(BuyBtnTypeEnum.PINTUAN.getCode()) || priceRuleTypes.contains(BuyBtnTypeEnum.TIMES_CARD.getCode());
        }
        if (EduDealUtils.isEduOnlineCourseDeal(ctx)) {
            DztgClientTypeEnum clientType = ctx.getEnvCtx().getDztgClientTypeEnum();
            // 在线类团购固定展示咨询按钮
            return dealBuyBar != null;
        }
        // 留资型团单固定展示咨询按钮
        if (DealBuyHelper.isLeadsDeal(ctx)) {
            return dealBuyBar != null;
        }
        return dealBuyBar != null && (dealBuyBar.getBuyBtns().size() == 1 || dealBuyBar.getBuyType() == DealBuyBar.BuyType.PINPOOL.type || dealBuyBar.getBuyType() == DealBuyBar.BuyType.TIMESCARD.type);
    }

    @Override
    public boolean isEnable(DealCtx ctx) {
        return ctx.getDealGroupBase() != null;
    }

    @Override
    public void prepare(DealCtx ctx) {

    }

    @Override
    public void process(DealCtx ctx) {
        DealGroupPBO result = ctx.getResult();

        // 处理侵权屏蔽商品
        result.setModuleConfigsModule(ctx.getModuleConfigsModule());

        if (!ctx.isMtLiveMinApp() && AttributeUtils.isTort(ctx.getAttrs())) {
            AttributeUtils.logTortType(ctx.getAttrs());
            if (result.getModuleConfigsModule() == null || !result.getModuleConfigsModule().isTort()) {
                ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
                moduleConfigsModule.setTort(true);
                moduleConfigsModule.setTortText("当前团购已失效，请选择其他团购");
                moduleConfigsModule.setTortTitle("当前团购已失效");
                moduleConfigsModule.setTortDesc("请选择其他团购");

                result.setModuleConfigsModule(moduleConfigsModule);
            }
            return;
        }

        // 通过渠道来源 下发预览模式key
        buildModuleConfigByRequestSource(ctx, result);

        result.setDpId(ctx.getDpId());
        result.setMtId(ctx.getMtId());
        result.setDpDealId(NumberUtils.toInt(basicInfoBuilderService.getDefaultSkuId(ctx)));
        result.setSkuId(basicInfoBuilderService.getDefaultSkuId(ctx));
        ctx.setDpDealId(result.getDpDealId());
        result.setTitle(basicInfoBuilderService.getTitle(ctx));
        result.setCategoryId(ctx.getCategoryId());
        if (ctx.getDealGroupDTO() != null && ctx.getDealGroupDTO().getCategory() != null) {
            DealGroupCategoryDTO category = ctx.getDealGroupDTO().getCategory();
            result.setServiceType(category.getServiceType());
            result.setServiceTypeId(category.getServiceTypeId());
        }
        result.setBgName(ctx.getChannelGroupName());
        result.setDealContents(dealContentBuilderService.getContent(ctx.getDealGroupBase(), ctx.isMt(), ctx, result));
        result.setShop(shopBuilderService.getShopPBO(ctx));
        CleaningProductLionConfig config = Lion.getBean(LionConstants.APP_KEY, LionConstants.CLEANING_PRODUCT_INFORMATION_CONFIG, CleaningProductLionConfig.class);
        List<LifeClearHaiMaConfig> list = haimaWrapper.cleaningProductInformation(config);
        result.setSelfScene(LifeClearUtil.getSelfScene(list, ctx.getMtId(), config));
        boolean isFromBarManager = this.isFromBarManager(ctx);
        result.setFeatures(guaranteeBuilderService.getFeatures(ctx, isFromBarManager));

        //是否来自酒吧营销经理
        if (isFromBarManager) {
            result.setLimits(Lists.newArrayList("需要商家审核退款"));
            List<Guarantee> limitsExtends = Lists.newArrayList();
            limitsExtends.add(Guarantee.builder().text("需要商家审核退款").build());
            result.setLimitsExtend(limitsExtends);
        }
        result.setGuarantee(guaranteeBuilderService.getGuarantee(ctx, isFromBarManager));
        result.setSpecialFeatures(guaranteeBuilderService.getSpecialFeatures(ctx));
        result.setAbConfigModel(ctx.getModuleAbConfig());
        result.setSkuModule(ctx.getSkuModule());
        result.setModuleAbConfigs(moduleAbBuilderService.getModuleAbConfigs(ctx));
        saleBuilderService.setSaleDescAndSaleDescStr(ctx, result);
        if (ctx.getTimesCard() != null) {
            String timesCardId = String.valueOf(ctx.getTimesCard().getProductId());
            result.setTimesCard(new TimesCardPBO(timesCardId));
        }

        result.setMaxPerUser(ctx.getDealGroupBase().getMaxPerUser());
        atmospherBuilderService.buildAtmosphereBar(ctx, result);
        buyBarBuilderService.buildBuyBar(ctx, result);
        buildBuyBarPricePostFix(ctx, result);
        buildPriceDisplayInfo(ctx, result);
        buildPromoDetailInfo(ctx, result);
        buildDealPromoDetailInfo(ctx, result);
        buildCardStylePromoDetailInfo(ctx, result);
        // 构造活动橱窗团购属性
        buildActivityWindowDealConfig(ctx, result);
        //构造团详改版须知信息
        buildReminder(ctx, result);
        //精选标签
        result.setTitleTagIcon(buildSelectTag(ctx.getDpId(), ctx.isMt()));
        result.setChoicestIcon(buildChoicestIcon(ctx));
        result.setDealTitleIcon(buildDealTitleIcon(ctx));
        putSmallHead(ctx);
        putExtraStyle(ctx);
        putPicAspectRatio(ctx, result);
        putOnlineConsult(ctx, result); //底部bar展示IM
        putOriginalOnlineConsult(ctx, result);
        putVoucherInfo(ctx, result); //酒吧茶馆代金券信息
        putDealName(ctx, result);
        filterBuyButtonEnable(ctx);
        shareBuilderService.putShareAble(ctx, result); //是否能分享
        //324团建类目 单位设置
        setCopiesForGroupBuild(ctx, result);

        // 是否展示预约浮层
        result.setHasReserveEntrance(ctx.isShowReserveEntrance());
        result.setShowNewReserveEntrance(ctx.isShowNewReserveEntrance());
        if (ctx.isShowNewReserveEntrance()) {
            result.setReserveRedirectUrl(ctx.getReserveRedirectUrl());
        }
        // 填充商户开卡状态，用户持卡状态
        buildCardState(ctx, result);

        result.setShareModule(ctx.getDztgShareModule());
        result.setMoreDealsModule(ctx.getDztgMoreDealModule());
        result.setAdModule(ctx.getDztgAdsModule());
        result.setStructedDetails(commonModuleBuilderService.buildRichText(ctx));
        result.setHighlightsModule(ctx.getHighlightsModule());
        result.setSubTitleList(buildSubTitleList(ctx));

        // 构造限购条
        buildPurchaseLimitInfo(ctx, result);
        //团详微信小程序未登录隐藏信息处理
        hideMtMiniAppInfo(ctx);
        // 下发团单是否是限制购买次数的团单
        result.setPurchaseLimitDeal(isPurchaseLimitDeal(ctx));
        // 用户达到限制的购买次数
        result.setMeetPurchaseLimit(userOrderCountOverPurchaseLimit(ctx));

        // 无忧通不显示标签
        if (DealAttrHelper.isWuyoutong(ctx)) {
            result.setPriorTag(null);
        }

        //设置价保标签浮层
        if (LionConfigUtils.showPriceProtectionInfo()) {
            result.setFeaturesLayer(getFeaturesLayer(ctx, isFromBarManager));
        }
        result.setAdditionalInfo(ctx.getAdditionalInfo());
        // 业务场景样式设置
        result.setBusinessStyle(ctx.getBusinessStyle());

        // 团购次卡属性设置
        promoDetailModuleBuilderService.buildTimesDealPromoInfo(ctx, result.getPromoDetailModule());
        buildStructuredPurchaseNote(ctx, result);
        // 构造春节不打烊
        reminderInfoBuilderService.buildSpringFestivalBanner(ctx, result);
        // 宠物代金券名称
        putPetVoucherTitle(ctx, result);
        // 设置交易类型
        putTradeType(ctx, result);
        buildMLiveInfoVo(ctx, result);
        // 构建拼团规则
        buildPinTuanRuleInfoByCtx(ctx, result);
        // 构建拼团透传参数
        buildPinTuanDTO(ctx, result);

        // 纹绣业务特有，常见问题+纹后须知字段
        result.setTattooPrecautionsVO(ctx.getTattooPrecautionsVO());
        result.setTattooQAsVO(ctx.getTattooQAsVO());
        result.setModuleExtra(ctx.getModuleExtraDTO());
        buildSsrExpEnabled(ctx, result);
        // 设置接口返回的时间戳，用于前端刷新组件
        result.setResponseTimestamp(System.currentTimeMillis());
        // 设置神会员点位
        result.setPosition(ctx.getPosition());
        // 台球自助开台
        //个性化价格说明，返回链接
        result.setCustomPriceDescJumpUrl(priceExplainBuilderService.buildCustomPriceDescJumpUrl(ctx));
        result.setRecommendStrategy(abBizBuilderService.buildRecommendStrategy(ctx));
        rcfLog(ctx.getCategoryId());
        //给交易用的特殊参数，团详页不要使用
        result.setSpecialParamsForPay(specialParamsForPayBuilderService.build(ctx, result));
        passParamBuilderService.buildPassParam(ctx, result);
        preOrderParamBuilderService.buildPreOrderParam(ctx, result);
        // 预订团单标识
        result.setPreOrderDeal(DealCtxHelper.isPreOrderDeal(ctx));
        // 构建团详页头图氛围
        result.setHeadPicAtmosphere(headPicAtmosphereBuilderService.build(ctx));

        //返回快照数据
        result.setQueryExposureResourcesCache(ctx.getQueryExposureResourcesCache());
        result.setDealFilterListCache(ctx.getDealFilterListCache());
        // 是否支持先用后付
        result.setCreditPay(ctx.isCreditPay());
        // 新团详模块
        result.setProductDetailModule(productDetailBuilderService.build(ctx));
        result.setHideMemberCardGuide(ctx.isHideMemberCardGuide());
        // 是否是保洁自营品牌们带你
        result.setCleaningSelfOperationShop(ctx.isCleanSelfOperationShop());
        // 导航栏搜索
        result.setNavBarSearchModuleVO(ctx.getNavBarSearchModuleVO());
        // 打点
        dealStyleStatisticService.logMigration(ctx, result);
    }

    public void rcfLog(Integer categoryId) {
        //rcf分行业性能打点
        if (categoryId != null) {
            IndustryRcfTrackUtils.industryRcfTrack(categoryId);
        }
    }

    public void buildSsrExpEnabled(DealCtx ctx, DealGroupPBO result) {
        if (ctx.getEnvCtx().getDztgClientTypeEnum().equals(DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP)) {
            result.setSsrExperimentEnabled(Lion.getBoolean(APP_KEY, LionConstants.SSR_EXP_ENABLED, false));
        }
    }

    public void buildPinTuanDTO(DealCtx ctx, DealGroupPBO result) {
        if (!CostEffectivePinTuanUtils.isCePinTuaScene(ctx)) {
            return;
        }
        TransParamModuleDTO dto = new TransParamModuleDTO();
        dto.setOrderGroupId(ctx.getCostEffectivePinTuan().getShareToken());
        dto.setSceneType(Objects.nonNull(ctx.getCostEffectivePinTuan().getSceneType()) ? ctx.getCostEffectivePinTuan().getSceneType() : null);
        dto.setPintuanActivityId(ctx.getCostEffectivePinTuan().getPinTuanActivityId());
        result.getShareModule().setTransParam(dto);
    }

    // 构建拼团规则
    public void buildPinTuanRuleInfoByCtx(DealCtx ctx, DealGroupPBO result) {
        CostEffectivePinTuan costEffectivePinTuan = ctx.getCostEffectivePinTuan();
        if (CostEffectivePinTuanUtils.isCePinTuaScene(ctx) && Objects.nonNull(costEffectivePinTuan) && CollectionUtils.isNotEmpty(costEffectivePinTuan.getRuleInfoPOS())) {
            result.setPinTuanRuleInfo(buildPinTuanRuleInfo(ctx, costEffectivePinTuan.getRuleInfoPOS()));
        }
    }

    public PinTuanRuleInfo buildPinTuanRuleInfo(DealCtx ctx, List<RuleInfoPO> ruleInfoPOS) {
        PinTuanRuleInfo pinTuanRuleInfo = new PinTuanRuleInfo();
        pinTuanRuleInfo.setTitle("拼团规则");
        pinTuanRuleInfo.setItems(buildPinTuanRuleItems(ctx));
        pinTuanRuleInfo.setFeaturesLayer(buildPinTuanRuleFeaturesLayer(ruleInfoPOS));
        return pinTuanRuleInfo;
    }

    public FeaturesLayer buildPinTuanRuleFeaturesLayer(List<RuleInfoPO> ruleInfoPOS) {
        FeaturesLayer featuresLayer = new FeaturesLayer();
        featuresLayer.setTitle("查看拼团规则");
        featuresLayer.setLayerConfigs(buildPinTuanLayerConfigs(ruleInfoPOS));
        return featuresLayer;
    }

    public List<LayerConfig> buildPinTuanLayerConfigs(List<RuleInfoPO> ruleInfoPOS) {
        List<LayerConfig> layerConfigs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ruleInfoPOS)) {
            ruleInfoPOS.forEach(e -> {
                LayerConfig layerConfig = new LayerConfig();
                layerConfig.setTitle(e.getTitle());
                layerConfig.setDesc(e.getContent());
                layerConfigs.add(layerConfig);
            });
        }
        return layerConfigs;
    }

    public List<Guarantee> buildPinTuanRuleItems(DealCtx ctx) {
        Map<String, Map> pinTuanRuleConfig = Lion.getMap(LionConstants.APP_KEY,
                LionConstants.PIN_TUAN_RULE, Map.class, Collections.emptyMap());
        List<Guarantee> items = new ArrayList<>();
        Map<String, String> payConfig = pinTuanRuleConfig.getOrDefault(PIN_TUAN_RULE_PAY, Maps.newHashMap());
        Map<String, String> inviteConfig = pinTuanRuleConfig.getOrDefault(PIN_TUAN_RULE_INVITE, Maps.newHashMap());
        if (ctx.getCostEffectivePinTuan().isLimitNewCustomJoin()) {
            inviteConfig = pinTuanRuleConfig.getOrDefault(PIN_TUAN_RULE_INVITE_NEW, Maps.newHashMap());
        }
        Map<String, String> completeConfig = pinTuanRuleConfig.getOrDefault(PIN_TUAN_RULE_COMPLETE, Maps.newHashMap());
        items.add(buildPinTuanIconText(payConfig.get(TEXT), payConfig.get(ICON), null, null));
        items.add(buildPinTuanIconText(inviteConfig.get(TEXT), inviteConfig.get(ICON), null, null));
        items.add(buildPinTuanIconText(completeConfig.get(TEXT), completeConfig.get(ICON), null, null));
        return items;
    }

    public Guarantee buildPinTuanIconText(String text, String icon, Integer type, String style) {
        Guarantee.GuaranteeBuilder builder = Guarantee.builder()
                .text(text)
                .icon(icon)
                .style(style);
        if (Objects.nonNull(type)) {
            builder.type(type);
        }
        return builder.build();
    }

    public void setCopiesForGroupBuild(DealCtx ctx, DealGroupPBO result) {
        if (ctx.getCategoryId() != 324 || result.getPromoDetailModule() == null) {
            return;
        }
        //若商家设置团单“使用人数”为1（limit_the_number_of_users=1），即按人收费，则展示为“XX元/人”；
        //商家设置团单“使用人数”不等于1（limit_the_number_of_users≠1），即团单一口价，则展示为“XX元/团”（与商品卡的展示逻辑保持一致）
        String number = DealUtils.findFirstAttrValue(ctx.getAttrs(), "limit_the_number_of_users");
        if (StringUtils.isEmpty(number) || !("1".equals(number))) {
            result.getPromoDetailModule().setCopies("/团");
        } else {
            result.getPromoDetailModule().setCopies("/人");
        }
    }

    public void buildMLiveInfoVo(DealCtx ctx, DealGroupPBO result) {
        ctx.getMLiveInfoVo().setChannelIdentity(ctx.getMLiveChannel());
        result.setMLiveInfoVo(ctx.getMLiveInfoVo());
    }

    public void buildStructuredPurchaseNote(DealCtx ctx, DealGroupPBO result) {
        result.setHitStructuredPurchaseNote(getHitStructuredPurchaseNote(ctx));
        if (result.isHitStructuredPurchaseNote()) {
            result.setPnPurchaseNoteDTO(assemblePnPurchaseNoteDTO(ctx));
        }
    }

    public PnPurchaseNoteDTO assemblePnPurchaseNoteDTO(DealCtx ctx) {
        if (ctx.isMtLiveMinApp()) {
            Iterator<PurchaseNoteModuleDTO> moduleDTOIterator = ctx.getPnPurchaseNoteDTO().getPnModules().iterator();
            while (moduleDTOIterator.hasNext()) {
                PurchaseNoteModuleDTO moduleDTO = moduleDTOIterator.next();
                if ("温馨提示".equals(moduleDTO.getPnModuleName())) {
                    PnStandardDisplayItemDTO itemDTO = moduleDTO.getPnItems().stream().filter(item -> "商家服务".equals(item.getPnItemName())).findFirst().orElse(null);
                    if (Objects.isNull(itemDTO)) {  // 无商家服务，删除温馨提示模块
                        moduleDTOIterator.remove();
                    } else {
                        Iterator<PnStandardDisplayItemDTO> itemDTOIterator = moduleDTO.getPnItems().iterator();
                        while (itemDTOIterator.hasNext()) { // 有商家服务，删除商家服务外的其他条目
                            PnStandardDisplayItemDTO itemDTOEle = itemDTOIterator.next();
                            if (!"商家服务".equals(itemDTOEle.getPnItemName())) {
                                itemDTOIterator.remove();
                            }
                        }
                    }
                }
                if ("其他规则".equals(moduleDTO.getPnModuleName())) {
                    moduleDTOIterator.remove();
                }
            }
        } else {
            return ctx.getPnPurchaseNoteDTO();
        }
        return ctx.getPnPurchaseNoteDTO();
    }

    /**
     * 通过渠道来源下发，预览key
     *
     * @param ctx
     * @param result
     */
    public void buildModuleConfigByRequestSource(DealCtx ctx, DealGroupPBO result){
        if (DealUtils.isPreviewDeal(ctx)){
            ModuleConfigsModule moduleConfigsModule = result.getModuleConfigsModule();
            if (Objects.isNull(moduleConfigsModule)) {
                moduleConfigsModule = new ModuleConfigsModule();
            }
            moduleConfigsModule.setPreviewInfo(getPreviewInfoByRequestSource(ctx.getRequestSource()));
        }
    }

    public String getPreviewInfoByRequestSource(String source) {
        Map<String, ModuleConfigDo> pagesourceMapKeyValueMap = LionConfigUtils.getPagesourceMapKeyValueMap();
        ModuleConfigDo moduleConfigDo = pagesourceMapKeyValueMap.get(source);
        return moduleConfigDo != null ? moduleConfigDo.getValue() : "create_order_preview";
    }

    /**
     * 只有hitStructuredPurchaseNote=true且新购买须知不为空时才设置ctx中hitStructuredPurchaseNote为true
     *
     * @param ctx
     * @return
     */
    public boolean getHitStructuredPurchaseNote(DealCtx ctx) {
        return ctx.isHitStructuredPurchaseNote() && Objects.nonNull(ctx.getPnPurchaseNoteDTO()) && CollectionUtils.isNotEmpty(ctx.getPnPurchaseNoteDTO().getPnModules());
    }

    /**
     * 构造限购条
     *
     * @param ctx
     * @param result
     */
    public void buildPurchaseLimitInfo(DealCtx ctx, DealGroupPBO result) {
        try {
            DealGroupDTO dealGroup = ctx.getDealGroupDTO();
            if (ctx.isFreeDeal() && ctx.getFreeDealConfig() != null && !CollectionUtils.isEmpty(ctx.getFreeDealConfig().getLimit())) {
                result.setLimits(ctx.getFreeDealConfig().getLimit());
                return;
            }
            // 读配置, 按三级类目区分, 删除限制条
            // 获取团单三级类目
            Long thirdCategoryId = Optional.ofNullable(dealGroup).map(DealGroupDTO::getCategory).map(DealGroupCategoryDTO::getServiceTypeId).orElse(0L);
            // 增加团单版控, 只有新团单不展示限制条
            if (LionConfigUtils.hitLimitNoteIgnoreThirdCategory(thirdCategoryId) && !DealVersionUtils.isOldMetaVersion(dealGroup, LionConstants.COMMON_MODULE_CALL_VERSION_CONFIG)) {
                return;
            }
            if (Objects.nonNull(dealGroup) && Objects.nonNull(dealGroup.getRule()) && Objects.nonNull(dealGroup.getRule().getBuyRule())) {
                DealGroupBuyRuleDTO buyRuleDTO = dealGroup.getRule().getBuyRule();
                // 构建限购条
                buildLimitInfo(dealGroup, ctx, result, buyRuleDTO);
            }
        } catch (Throwable t) {
            logger.error("buildPurchaseLimitInfo error", t);
        }
    }

    /**
     * 构造限购条
     *
     * @param dealGroup
     * @param ctx
     * @param result
     * @param buyRuleDTO
     */
    public void buildLimitInfo(DealGroupDTO dealGroup, DealCtx ctx, DealGroupPBO result, DealGroupBuyRuleDTO buyRuleDTO) {
        // 丽人限购条，有浮层效果，可按配置行业下发
        if (LionConfigUtils.hitLimitInfoConfig(result.getCategoryId())) {
            // 根据行业构建限购条
            buildLimitInfoByCategory(dealGroup, ctx, result, buyRuleDTO);
        } else {
            // 原来限制条
            buildLimitInfoByRules(result, buyRuleDTO);
        }
    }

    public void buildLimitInfoByRules(DealGroupPBO result, DealGroupBuyRuleDTO buyRuleDTO) {
        if (Objects.nonNull(buyRuleDTO.getMaxPerUser()) && buyRuleDTO.getMaxPerUser() > 0) {
            String limitInfo = String.format("每人限购%s张", buyRuleDTO.getMaxPerUser());
            List<String> limits = getLimitsFromDealGroupPBO(result);
            limits.add(limitInfo);
        }
    }

    public void buildLimitInfoByCategory(DealGroupDTO dealGroup, DealCtx ctx, DealGroupPBO result, DealGroupBuyRuleDTO buyRuleDTO) {
        if (Objects.nonNull(buyRuleDTO.getMaxPerUser())) {
            List<Guarantee> limitsExtends = getLimitsExtend(result);
            // 获取限购条信息、限购条浮层
            LayerConfig limitInfoConfig = getLimitInfo(buyRuleDTO, dealGroup);
            if (Objects.isNull(limitInfoConfig)) {
                return;
            }
            buildLayerConfig(limitInfoConfig, buyRuleDTO);
            // 限购条信息
            String limitTitle = limitInfoConfig.getTitle();
            // 构造限购条浮层
            buildLimitLayer(ctx, result, limitInfoConfig);
            // 限购条新字段，可支持高亮
            buildLimitExtendByMap(limitsExtends, limitInfoConfig);
            List<String> limits = getLimitsFromDealGroupPBO(result);
            limits.add(limitTitle);
        }
    }

    private List<String> getLimitsFromDealGroupPBO(DealGroupPBO result) {
        List<String> limits = result.getLimits();
        if (CollectionUtils.isEmpty(limits)) {
            limits = Lists.newArrayList();
            result.setLimits(limits);
        }
        return limits;
    }

    public List<Guarantee> getLimitsExtend(DealGroupPBO result) {
        List<Guarantee> limitsExtend = result.getLimitsExtend();
        if (CollectionUtils.isEmpty(limitsExtend)) {
            limitsExtend = Lists.newArrayList();
            result.setLimitsExtend(limitsExtend);
        }
        return limitsExtend;
    }

    public void buildLimitExtendByMap(List<Guarantee> limitsExtends, LayerConfig limitInfoConfig) {
        if (StringUtils.isNotBlank(limitInfoConfig.getTitle())) {
            Guarantee limitExtend = Guarantee.builder()
                    .text(limitInfoConfig.getTitle())
                    .type(limitInfoConfig.getTextType())
                    .style(limitInfoConfig.getTextStyle())
                    .build();
            limitsExtends.add(limitExtend);
        }

    }

    /**
     * 构造限制条浮层
     *
     * @param ctx
     * @param result
     */
    public void buildLimitLayer(DealCtx ctx, DealGroupPBO result, LayerConfig limitInfoConfig) {
        if (ctx.isMtLiveMinApp()) {
            return;
        }
        if (!isValidLimitInfoConfig(limitInfoConfig)) {
            return;
        }
        FeaturesLayer featuresLayer = result.getGeneralFeaturesLayer();
        if (Objects.isNull(featuresLayer)) {
            featuresLayer = new FeaturesLayer();
            featuresLayer.setLayerConfigs(new ArrayList<>());
            result.setGeneralFeaturesLayer(featuresLayer);
        }
        featuresLayer.getLayerConfigs().add(limitInfoConfig);
    }

    private Boolean isValidLimitInfoConfig(LayerConfig limitInfoConfig) {
        if (Objects.isNull(limitInfoConfig)
                || StringUtils.isBlank(limitInfoConfig.getDesc())) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 获取限购条及限购条浮层信息
     *
     * @return
     */
    public LayerConfig getLimitInfo(DealGroupBuyRuleDTO buyRuleDTO, DealGroupDTO dealGroup) {
        // 获取适用人群
        List<String> suitPerson = getAttrValFromAttr(dealGroup, SUIT_PERSON);
        Map<String, LayerConfig> generalLayerConfigMap = Lion.getMap(LionConstants.APP_KEY,
                LionConstants.GENERAL_LAYER_CONFIG, LayerConfig.class, Collections.emptyMap());

        // 不限购买次数
        if (buyRuleDTO.getMaxPerUser() == NO_LIMIT) {
            return getNoLimitInfo(suitPerson, generalLayerConfigMap);
        }
        // 限制购买次数1次
        if (buyRuleDTO.getMaxPerUser() == LIMIT_ONE) {
            return getOneLimitInfo(suitPerson, generalLayerConfigMap);
        }
        //限制购买次数>=2次
        if (buyRuleDTO.getMaxPerUser() >= LIMIT_MULTI) {
            return getMultiLimitInfo(suitPerson, generalLayerConfigMap);
        }
        return null;
    }

    /**
     * 判断是否限制购买次数
     *
     * @param buyRuleDTO
     * @return
     */
    public boolean isPurchaseLimit(DealGroupBuyRuleDTO buyRuleDTO) {
        if (buyRuleDTO.getMaxPerUser() >= LIMIT_ONE) {
            // 限制购买次数
            return Boolean.TRUE;
        }
        // 不限制次数
        return Boolean.FALSE;
    }

    public boolean isPurchaseLimitDeal(DealCtx ctx) {
        if (!meetAppVersion(ctx)) {
            // 没有达到app版本号（美团>12.20.400，点评>11.17.0）
            return Boolean.FALSE;
        }
        DealGroupDTO dealGroup = ctx.getDealGroupDTO();
        if (Objects.nonNull(dealGroup)
                && Objects.nonNull(dealGroup.getRule())
                && Objects.nonNull(dealGroup.getRule().getBuyRule())) {
            DealGroupBuyRuleDTO dealGroupBuyRuleDTO = dealGroup.getRule().getBuyRule();
            return isPurchaseLimit(dealGroupBuyRuleDTO);
        }
        return Boolean.FALSE;
    }

    /**
     * 判断是否达到 对应的app版本号
     *
     * @param ctx
     * @return
     */
    public boolean meetAppVersion(DealCtx ctx) {
        if (ctx.isMt()) {
            // 美团侧
            if (VersionUtils.isGreatEqualThan(ctx.getEnvCtx().getVersion(), "12.20.400")) {
                return Boolean.TRUE;
            }
        } else {
            // 点评侧
            if (VersionUtils.isGreatEqualThan(ctx.getEnvCtx().getVersion(), "11.17.0")) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    private LayerConfig getNoLimitInfo(List<String> suitPerson, Map<String, LayerConfig> generalLayerConfigMap) {
        // 勾选只适用于初次到店非会员
        if (suitPerson.contains("只适用于初次到店非商户会员使用")) {
            return generalLayerConfigMap.get(NO_LIMIT_PERSON_SUIT_PERSON_FIRST_NOT_MEMBER);
        } else { // 未勾选只适用于初次到店非会员
            return generalLayerConfigMap.get(NO_LIMIT_PERSON);
        }
    }

    private LayerConfig getOneLimitInfo(List<String> suitPerson, Map<String, LayerConfig> generalLayerConfigMap) {
        // 勾选只适用于初次到店非会员
        if (suitPerson.contains("只适用于初次到店非商户会员使用")) {
            return generalLayerConfigMap.get(LIMIT_ONE_PERSON_SUIT_PERSON_FIRST_NOT_MEMBER);
        } else { // 未勾选只适用于初次到店非会员
            return generalLayerConfigMap.get(LIMIT_ONE_PERSON);
        }
    }

    private LayerConfig getMultiLimitInfo(List<String> suitPerson, Map<String, LayerConfig> generalLayerConfigMap) {
        // 勾选只适用于初次到店非会员
        if (suitPerson.contains("只适用于初次到店非商户会员使用")) {
            return generalLayerConfigMap.get(LIMIT_MULTI_PERSON_SUIT_PERSON_FIRST_NOT_MEMBER);
        } else { // 未勾选只适用于初次到店非会员
            return generalLayerConfigMap.get(LIMIT_MULTI_PERSON);
        }
    }

    public LayerConfig buildLayerConfig(LayerConfig layerConfig, DealGroupBuyRuleDTO buyRuleDTO) {
        if (Objects.nonNull(layerConfig) && Objects.nonNull(buyRuleDTO)) {
            String title = layerConfig.getTitle();
            String desc = layerConfig.getDesc();
            layerConfig.setTitle(title != null ? String.format(title, buyRuleDTO.getMaxPerUser()) : "");
            layerConfig.setDesc(desc != null ? String.format(desc, buyRuleDTO.getMaxPerUser()) : "");
        }
        return layerConfig;
    }

    /**
     * 获取团单属性
     *
     * @param dealGroup
     * @param attrName
     * @return
     */
    private List<String> getAttrValFromAttr(DealGroupDTO dealGroup, String attrName) {
        Optional<AttrDTO> first = getFirst(dealGroup, attrName);
        if (Objects.nonNull(first) && first.isPresent()) {
            return first.get().getValue();
        }
        return com.google.common.collect.Lists.newArrayList();
    }

    private Optional<AttrDTO> getFirst(DealGroupDTO dealGroup, String attrName) {
        List<AttrDTO> attrs = dealGroup.getAttrs();
        if (CollectionUtils.isNotEmpty(attrs)) {
            return attrs.stream().filter(k -> Objects.equals(k.getName(), attrName)).findFirst();
        }
        return null;
    }

    /**
     * 构建副标题区
     *
     * @param ctx
     * @return
     */
    private List<SubTitleVO> buildSubTitleList(DealCtx ctx) {
        List<SubTitleVO> subTitleVOList = new ArrayList<>();
        // 过夜模块
        SubTitleVO overNightSubTitleVO = buildOverNightSubTitle(ctx);

        if (Objects.nonNull(overNightSubTitleVO) && DealVersionUtils.isOldMetaVersion(ctx.getDealGroupDTO(), LionConstants.COMMON_MODULE_CALL_VERSION_CONFIG)) {
            subTitleVOList.add(overNightSubTitleVO);
        }

        // 台球自助开台复用该字段
        if (ctx.getAutoOpenTable()) {
            subTitleVOList.add(buildBilliardsSubTitle(ctx));
        }

        return subTitleVOList;
    }

    private SubTitleVO buildBilliardsSubTitle(DealCtx ctx) {
        SubTitleVO subTitleVO = new SubTitleVO();
        subTitleVO.setTitle("服务");
        TitleItemVO titleItemVO = new TitleItemVO();
        titleItemVO.setText("支持购后一键开台");
        subTitleVO.setItemDTOs(Lists.newArrayList(titleItemVO));
        return subTitleVO;
    }

    /**
     * 构建过夜副标题模块
     *
     * @param ctx
     * @return
     */
    private SubTitleVO buildOverNightSubTitle(DealCtx ctx) {
        // 限定足疗洗浴
        if (ctx.getCategoryId() != 303 && ctx.getCategoryId() != 304) {
            return null;
        }
        // 限定实验a组
        ModuleAbConfig overNightAbConfig = DealDouHuUtil.getOverNightDouHuSk(ctx);
        if (Objects.isNull(overNightAbConfig) || CollectionUtils.isEmpty(overNightAbConfig.getConfigs()) || Objects.isNull(overNightAbConfig.getConfigs().get(0))
                || !overNightAbConfig.getConfigs().get(0).getExpResult().contains("a")) {
            return null;
        }
        // 只有当 含有不免费的过夜服务时，才展示这个模块
        if (!hasNotFreeOverNightService(ctx)) {
            return null;
        }
        // 构建过夜服务模块
        SubTitleVO overNightSubTitleVO = new SubTitleVO();
        overNightSubTitleVO.setTitle("服务");
        overNightSubTitleVO.setJumpUrl(Objects.nonNull(ctx.getSkuModule()) ? ctx.getSkuModule().getUrl() : null);
        TitleItemVO titleItemVO = new TitleItemVO();
        titleItemVO.setText("过夜服务");
        titleItemVO.setPreIcon("https://p1.meituan.net/travelcube/140ba08104b3203b49b85edd4aa318612066.png");
        overNightSubTitleVO.setItemDTOs(Lists.newArrayList(titleItemVO));
        return overNightSubTitleVO;
    }

    public void buildReminder(DealCtx ctx, DealGroupPBO result) {
        List<String> reminders = Lists.newArrayList();
        if (ctx.isMtLiveMinApp()) {
            reminders.add("7天无理由退款");
            reminders.add("过期自动退");
            List<String> reservationInfo = remindBuilderService.getReservationInfo(ctx);
            reminders.addAll(CollectionUtils.isNotEmpty(reservationInfo) ? reservationInfo.subList(0, 1) : reservationInfo);
            //过滤无效字符
            result.setReminderInfo(reminders.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList()));
            return;
        }
        if (!ctx.isEnableCardStyleV2()) {
            return;
        }
        if (ReassuredRepairUtil.isTagPresent(ctx)) {
            result.setReminderInfo(ReassuredRepairHelp.getReassuredRepairReminders(ctx, reminders));
            return;
        }
        //疫苗0元预约场景
        if (ctx.isFreeDeal() && ctx.getCategoryId() == 1611) {
            result.setReminderInfo(Lists.newArrayList("线上预约无需支付费用"));
            return;
        }
        if (ctx.isFreeDeal() && (FreeDealEnum.HOME_DESIGN_BOOKING == ctx.getFreeDealType() || FreeDealEnum.LIFE_HOUSEKEEPING_BOOKING == ctx.getFreeDealType() || FreeDealEnum.RECYCLE == ctx.getFreeDealType())) {
            if (CollectionUtils.isEmpty(ctx.getDealGroupDTO().getAttrs())) {
                return;
            }
            ctx.getDealGroupDTO().getAttrs().stream()
                    .filter(a -> "free_product_notice".equals(a.getName())).findFirst()
                    .ifPresent(attrDTO -> result.setReminderInfo(attrDTO.getValue()));
            return;
        }
        if (LionConfigUtils.isEduOnlineDeal(ctx.getCategoryId(), getServiceTypeId(ctx.getDealGroupDTO()))) {
            // 在线教育类团购
            buildEduOnlineCourseReminderInfo(ctx, result);
            return;
        }
        if (EduDealUtils.isVocationalEduPlan(ctx.getDealGroupDTO())) {
            buildEduVocaEduOfflineZeroReminderInfo(ctx, result);
            return;
        }

        reminders.addAll(remindBuilderService.getReservationInfo(ctx));
        // 团购次卡须知
        List<String> timesDealReminds = buildTimesDealRemind(ctx);
        if (CollectionUtils.isNotEmpty(timesDealReminds)) {
            reminders.addAll(timesDealReminds);
        } else {
            reminders.add(AvailableTimeHelper.getAvailableTime(ctx));
            reminders.add(EffectiveDateHelper.getEffectiveDate(ctx));
        }
        //过滤无效字符
        result.setReminderInfo(reminders.stream().filter(reminder -> reminder != null && !reminder.equals("")).collect(Collectors.toList()));
    }

    private Long getServiceTypeId(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO == null || dealGroupDTO.getCategory() == null) {
            return null;
        }
        return dealGroupDTO.getCategory().getServiceTypeId();
    }

    /**
     * 职业教育0元单
     *
     * @param ctx
     * @param result
     */
    private void buildEduVocaEduOfflineZeroReminderInfo(DealCtx ctx, DealGroupPBO result) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.buildEduVocaEduOfflineZeroReminderInfo(DealCtx,DealGroupPBO)");
        List<String> reminders = new ArrayList<>();
        reminders.add(EffectiveDateHelper.getEffectiveDateForVoCaEduZero(ctx));
        //过滤无效字符
        result.setReminderInfo(reminders.stream().filter(reminder -> reminder != null && !reminder.equals("")).collect(Collectors.toList()));
    }

    private void buildEduOnlineCourseReminderInfo(DealCtx ctx, DealGroupPBO result) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.buildEduOnlineCourseReminderInfo(DealCtx,DealGroupPBO)");
        List<String> reminders = new ArrayList<>();
        reminders.add(EffectiveDateHelper.getEffectiveDateForOnlineCourse(ctx));
        //过滤无效字符
        result.setReminderInfo(reminders.stream().filter(reminder -> reminder != null && !reminder.equals("")).collect(Collectors.toList()));
    }

    /**
     * 判断商品是否为过夜商品，true 是过夜商品
     *
     * @param ctx
     * @return
     */
    private boolean hasNotFreeOverNightService(DealCtx ctx) {
        if (isInvalidContext(ctx)) {
            return false;
        }

        for (DealGroupDealDTO dealDTO : ctx.getDealGroupDTO().getDeals()) {
            if (hasNotFreeOverNightServiceInDeal(dealDTO)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 连续判断过夜商品
     *
     * @param ctx
     * @return
     */
    private boolean isInvalidContext(DealCtx ctx) {
        return Objects.isNull(ctx) || Objects.isNull(ctx.getDealGroupDTO()) || (ctx.getCategoryId() != 303 && ctx.getCategoryId() != 304);
    }

    /**
     * 连续判断过夜商品
     *
     * @param dealDTO
     * @return
     */
    private boolean hasNotFreeOverNightServiceInDeal(DealGroupDealDTO dealDTO) {
        if (Objects.isNull(dealDTO) || Objects.isNull(dealDTO.getRule())) {
            return false;
        }

        for (ReadjustPriceRuleDTO readjustPriceRuleDTO : dealDTO.getRule().getReadjustPriceRules()) {
            if (hasNotFreeOverNightServiceInRule(readjustPriceRuleDTO)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 连续判断过夜商品
     *
     * @param readjustPriceRuleDTO
     * @return
     */
    private boolean hasNotFreeOverNightServiceInRule(ReadjustPriceRuleDTO readjustPriceRuleDTO) {
        if (Objects.isNull(readjustPriceRuleDTO) || Objects.isNull(readjustPriceRuleDTO.getStandardPriceRule()) || CollectionUtils.isEmpty(readjustPriceRuleDTO.getStandardPriceRule().getStandardPriceRuleItems())) {
            return false;
        }

        for (StandardPriceRuleItemDTO itemDTO : readjustPriceRuleDTO.getStandardPriceRule().getStandardPriceRuleItems()) {
            if (isNotFreeOverNightServiceItem(itemDTO)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 连续判断过夜商品
     *
     * @param itemDTO
     * @return
     */
    private boolean isNotFreeOverNightServiceItem(StandardPriceRuleItemDTO itemDTO) {
        return Objects.nonNull(itemDTO) && Objects.nonNull(itemDTO.getIdentityKey()) && itemDTO.getIdentityKey().equals("overNightService")
                && MapUtils.isNotEmpty(itemDTO.getAttributeValue()) && !Boolean.parseBoolean(itemDTO.getAttributeValue().get("free"));
    }

    private void putExtraStyle(DealCtx ctx) {
        if (ctx.getResult().getExtraStyles() == null) {
            ctx.getResult().setExtraStyles(Lists.newArrayList());
        }
        if (CollectionUtils.isNotEmpty(ctx.getDealExtraTypes())) {
            ctx.getResult().getExtraStyles().addAll(ctx.getDealExtraTypes());
        }
        if (CostEffectivePinTuanUtils.isCostEffectivePinTuanButtonStyle(ctx)) {
            ctx.getResult().getExtraStyles().add("TETUAN_PINTUAN_DEAL");
        }
    }

    /**
     * 构造价格展示相关数据
     *
     * @param ctx
     * @param result
     */
    private void buildPriceDisplayInfo(DealCtx ctx, DealGroupPBO result) {
        PriceContext priceContext = ctx.getPriceContext();
        PriceDisplayDTO normalPrice = priceContext.getNormalPrice();
        PriceDisplayModuleDo priceDisplayModuleDo = new PriceDisplayModuleDo();

        BigDecimal dealGroupPrice = ctx.getDealGroupBase().getDealGroupPrice();
        BigDecimal marketPrice = ctx.getDealGroupBase().getMarketPrice();
        String dealGroupPriceStr = NumberFormat.getInstance().format(dealGroupPrice);
        String marketPriceStr = NumberFormat.getInstance().format(marketPrice);

        priceDisplayModuleDo.setDealGroupPrice(dealGroupPriceStr);
        priceDisplayModuleDo.setMarketPrice(marketPriceStr);
        priceDisplayModuleDo.setEnableDisplay(isDrivingSchoolCar(ctx));

        if (normalPrice != null) {
            priceDisplayModuleDo.setPrice(normalPrice.getPrice().stripTrailingZeros().toPlainString());
        } else {
            priceDisplayModuleDo.setPrice(dealGroupPriceStr);
        }

        if (normalPrice != null && CollectionUtils.isNotEmpty(normalPrice.getUsedPromos())) {
            result.setDisplayPriceDesc("团购价");
            result.setDisplayPrice(dealGroupPriceStr);
            BigDecimal promoPrice = normalPrice.getPrice();
            priceDisplayModuleDo.setPromoPrice("减后价 ￥" + NumberFormat.getInstance().format(promoPrice));
            priceDisplayModuleDo.setPromoTag(StringUtils.isNotBlank(normalPrice.getPromoTag()) ? normalPrice.getPromoTag() : normalPrice.getUsedPromos().get(0).getTag());
        } else {
            result.setDisplayPriceDesc("门市价");
            result.setDisplayPrice(NumberFormat.getInstance().format(marketPrice));
        }
        // 留资型团单且(超值特惠套餐或小程序)特殊处理
        if (DealUtils.isLeadsDeal(ctx) && (ctx.isHitSpecialValueDeal() || !(ctx.getEnvCtx().judgeMainApp()))) {
            promoDetailModuleBuilderService.hideLeadsDealPriceInfo(priceDisplayModuleDo, normalPrice);
            promoDetailModuleBuilderService.hideLeadsDealDisplayPrice(result);
        }

        result.setPriceDisplayModuleDo(priceDisplayModuleDo);
    }

    private void buildPromoDetailInfo(DealCtx ctx, DealGroupPBO result) {
        try {
            PriceContext priceContext = ctx.getPriceContext();
            if (priceContext == null) {
                return;
            }
            //0元预约场景专用
            if (ctx.isFreeDeal()) {
                //疫苗--0元预约
                if (ctx.getCategoryId() == 1611) {
                    buildResvPromoDetailInfo(ctx, priceContext.getNormalPrice(), result);
                } else {
                    buildFreeDealPromoDetailInfo(ctx, result);
                }
            } else if (DealCtxHelper.isOdpSource(ctx)) {
                buildOdpPromoDetailInfo(ctx, priceContext.getNormalPrice(), result);
            } else {
                buildNormalPromoDetailInfo(ctx, priceContext.getNormalPrice(), result);
                if (priceContext.getAtmosphereBarAndGeneralPromoDetailPrice() != null) {
                    buildAtmosphereBarAndGeneralPromoDetailInfo(ctx, priceContext.getAtmosphereBarAndGeneralPromoDetailPrice(), result);
                }
            }
        } catch (Exception e) {
            logger.error("buildPromoDetailInfo error", e);
        }
    }

    /*
     * 0元预约场景使用
     * */
    private void buildResvPromoDetailInfo(DealCtx ctx, PriceDisplayDTO resvPrice, DealGroupPBO result) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.buildResvPromoDetailInfo(DealCtx,PriceDisplayDTO,DealGroupPBO)");
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        BigDecimal dealGroupPrice = ctx.getDealGroupBase().getDealGroupPrice();

        String dealGroupPriceStr = PriceHelper.format(dealGroupPrice);
        promoDetailModule.setDealGroupPrice(dealGroupPriceStr);

        // key:"retailPriceStyle" value:"1"
        // * 1：正常展示 2：隐藏 3：起价
        DealGroupDealDTO dealGroupDealDTO = null;
        if (ctx.getDealGroupDTO() != null && CollectionUtils.isNotEmpty(ctx.getDealGroupDTO().getDeals())) {
            dealGroupDealDTO = ctx.getDealGroupDTO().getDeals().get(0);
        }
        if (dealGroupDealDTO == null) {
            return;
        }
        String retailPriceStyle = getAttr(dealGroupDealDTO, "retailPriceStyle");
        //retailPriceStyle为null正常展示
        if (StringUtils.isEmpty(retailPriceStyle) || ("1").equals(retailPriceStyle)) {
            promoDetailModule.setPriceDisplayType(0);
            promoDetailModule.setFinalPrice(dealGroupPriceStr);
            //只有正常展示时，问号浮层信息才展示
            List<DescriptionTag> descriptionTags = buildDescriptionTags();
            promoDetailModule.setDescriptionTags(descriptionTags);
        }
        if (("2").equals(retailPriceStyle)) {
            promoDetailModule.setPriceDisplayType(1);
            promoDetailModule.setPriceDisplayText("价格详询机构");
        }
        if (("3").equals(retailPriceStyle)) {
            promoDetailModule.setPriceDisplayType(0);
            promoDetailModule.setFinalPrice(dealGroupPriceStr);
            promoDetailModule.setPricePostfix("起 具体详询机构");
        }

        result.setPromoDetailModule(promoDetailModule);
    }

    public void buildFreeDealPromoDetailInfo(DealCtx ctx, DealGroupPBO result) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.buildFreeDealPromoDetailInfo(DealCtx,DealGroupPBO)");
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        // 价格信息
        BigDecimal dealGroupPrice = ctx.getDealGroupBase().getDealGroupPrice();
        String dealGroupPriceStr = PriceHelper.format(dealGroupPrice);
        promoDetailModule.setDealGroupPrice(dealGroupPriceStr);
        promoDetailModule.setFinalPrice(dealGroupPriceStr);
        if (ctx.getFreeDealConfig() != null && ctx.getFreeDealConfig().isShowMarketPrice()) {
            BigDecimal marketPrice = ctx.getDealGroupBase().getMarketPrice();
            promoDetailModule.setMarketPrice(PriceHelper.formatPrice(marketPrice));
        }

        // 标签提示信息
        if (Objects.nonNull(ctx.getFreeDealConfig()) && CollectionUtils.isNotEmpty(ctx.getFreeDealConfig().getPriceTags())) {
            promoDetailModule.setDescriptionTags(ctx.getFreeDealConfig().getPriceTags());
        }

        result.setPromoDetailModule(promoDetailModule);
    }

    private List<DescriptionTag> buildDescriptionTags() {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.buildDescriptionTags()");
        return Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb", "com.sankuai.dzu.tpbase.dztgdetailweb.zeroVaccineResv.descriptionTags", DescriptionTag.class, Lists.newArrayList());
    }

    private String getAttr(DealGroupDealDTO dealDTO, String attrName) {
        if (dealDTO.getAttrs() == null) {
            return "";
        }
        return dealDTO.getAttrs()
                .stream()
                .filter(attrDTO -> attrDTO.getName().equals(attrName))
                .findAny()
                .map(AttrDTO::getValue)
                .flatMap(list -> list.stream().findFirst())
                .orElse("");
    }

    private void buildDealPromoDetailInfo(DealCtx ctx, DealGroupPBO result) {
        try {
            if (ctx.isMtLiveMinApp()) {
                // 私域直播价格无优惠
                return;
            }
            PriceContext priceContext = ctx.getPriceContext();
            if (priceContext == null) {
                return;
            }
            buildDealPromoDetailInfo(ctx, priceContext.getDealPromoPrice(), result);
        } catch (Exception e) {
            logger.error("buildPromoDetailInfo error", e);
        }
    }

    public void buildNormalPromoDetailInfo(DealCtx ctx, PriceDisplayDTO normalPrice, DealGroupPBO result) {
        if (normalPrice == null) {
            return;
        }
        // 适配第三方平台 开店宝侧、阿波罗侧 + 特团覆盖小程序 + 小程序升级新团详
        if (ctx.isExternal() && ctx.getCategoryId() != 712) {
            if (!ctx.isThirdPArt() && !ctx.isEnableCardStyleV2() && notMiniProgramCostEffective(ctx)
                    && !DealUtils.isOldDealDetailStyle(ctx)) {
                return;
            }
        }

        PromoDetailModule promoDetailModule = new PromoDetailModule();
        BigDecimal dealGroupPrice = ctx.getDealGroupBase().getDealGroupPrice();
        BigDecimal marketPrice = ctx.getDealGroupBase().getMarketPrice();
        //没有商家优惠时全网低价就是团单价
        BigDecimal networkLowestPrice = dealGroupPrice;
        String dealGroupPriceStr = PriceHelper.formatPrice(dealGroupPrice);
        String marketPriceStr = PriceHelper.formatPrice(marketPrice);

        promoDetailModule.setDealGroupPrice(dealGroupPriceStr);
        promoDetailModule.setMarketPrice(marketPriceStr);
        promoDetailModule.setPreSaleDealGroupPrice(getPreSaleDealGroupPrice(ctx));
        promoDetailModule.setPriceDisplayText(getPriceDisplayText(ctx));

        BigDecimal promoPrice = normalPrice.getPrice();
        promoDetailModule.setPromoPrice(PriceHelper.formatPrice(promoPrice));


        if (CollectionUtils.isNotEmpty(normalPrice.getUsedPromos())) {
            BigDecimal couponPromo = new BigDecimal(0);
            BigDecimal reductionPromo = new BigDecimal(0);
            if (normalPrice.getUsedPromos() != null) {
                List<PromoDTO> promoDTOList = normalPrice.getUsedPromos();
                for (PromoDTO promoDTO : promoDTOList) {
                    if (promoDTO == null || promoDTO.getAmount() == null) {
                        continue;
                    }
                    if (promoDTO.getIdentity().getPromoType() == PromoTypeEnum.NORMAL_PROMO.getType()) {
                        reductionPromo = reductionPromo.add(promoDTO.getAmount());
                    } else if (promoDTO.getIdentity().getPromoType() == PromoTypeEnum.IDLE_PROMO.getType()) {
                        reductionPromo = reductionPromo.add(promoDTO.getAmount());
                    } else if (promoDTO.getIdentity().getPromoType() == PromoTypeEnum.THRESHOLD.getType()) {
                        reductionPromo = reductionPromo.add(promoDTO.getAmount());
                    } else {
                        couponPromo = couponPromo.add(promoDTO.getAmount());
                    }
                }
            }
            promoDetailModule.setCouponPromo(PriceHelper.formatPrice(couponPromo));
            promoDetailModule.setReductionPromo(PriceHelper.formatPrice(reductionPromo));
        }

        if (CollectionUtils.isNotEmpty(normalPrice.getExtPrices())) {
            for (ExtPriceDisplayDTO extPriceDisplay : normalPrice.getExtPrices()) {
                if (extPriceDisplay != null && extPriceDisplay.getExtPriceType() == ExtPriceTypeEnum.LowestPrice_In_NetWork.getType()) {
                    networkLowestPrice = extPriceDisplay.getExtPrice();
                }
            }
        }

        //逻辑详见：https://km.sankuai.com/collabpage/1491774972
        if (promoPrice != null && networkLowestPrice != null && networkLowestPrice.compareTo(promoPrice) > 0) {
            promoDetailModule.setNetworkLowestPrice(PriceHelper.formatPrice(networkLowestPrice));
        }

        if (dealGroupPrice != null && promoPrice != null && dealGroupPrice.compareTo(promoPrice) > 0) {
            promoDetailModule.setTotalPromo(PriceHelper.formatPrice(dealGroupPrice.subtract(promoPrice)));
        }

        if (StringUtils.isNotEmpty(promoDetailModule.getPreSaleDealGroupPrice()) && promoPrice != null) {
            BigDecimal preSaleDealGroupPrice = new BigDecimal(promoDetailModule.getPreSaleDealGroupPrice());
            if (preSaleDealGroupPrice != null && preSaleDealGroupPrice.compareTo(promoPrice) > 0) {
                promoDetailModule.setPresalePromo(PriceHelper.formatPrice(preSaleDealGroupPrice.subtract(promoPrice)));
            }
        }
        //如果是放心改标签，则不返回该字段   ReassuredRepairUtil.isTagPresent(ctx)
        if (marketPrice != null && promoPrice != null && marketPrice.compareTo(promoPrice) > 0 && !(ReassuredRepairUtil.isTagPresent(ctx))) {
            promoDetailModule.setMarketPricePromo(PriceHelper.formatPrice(marketPrice.subtract(promoPrice)));
        }

        if (isSkuMultiPrice(ctx)) {
            promoDetailModule.setPricePostfix("起");
        }

        // 留资型团单且(超值特惠套餐或小程序)特殊处理
        if (DealUtils.isLeadsDeal(ctx) && (ctx.isHitSpecialValueDeal() || !(ctx.getEnvCtx().judgeMainApp()))) {
            promoDetailModuleBuilderService.hideLeadsDealPromoDetailInfo(promoDetailModule);
        }
        result.setPromoDetailModule(promoDetailModule);
    }

    public boolean notMiniProgramCostEffective(DealCtx ctx) {
        return ctx.getEnvCtx().WEIXIN_MINI_PROGRAM_LIST.contains(ctx.getEnvCtx().getDztgClientTypeEnum().getCode())
                && !(RequestSourceEnum.COST_EFFECTIVE.getSource().equals(ctx.getRequestSource()));
    }

    private boolean isSkuMultiPrice(DealCtx ctx) {
        // 新穿戴甲团单，虽然是多sku，但是统一价，不展示“起”
        if (DealUtils.isNewWearableNailDeal(ctx)) {
            return false;
        }
        return ctx.getDealGroupDTO() != null && CollectionUtils.isNotEmpty(ctx.getDealGroupDTO().getDeals()) && ctx.getDealGroupDTO().getDeals().stream()
                .filter(dealGroupDealDTO -> dealGroupDealDTO.getBasic().getStatus() == 1)
                .count() > 1;
    }

    private boolean needHideMarketPrice(DealCtx ctx) {
        List<Integer> list = Lion.getList(APP_KEY, HIDE_MARKETPRICE_CATEGORYID_CONFIG, Integer.class, Lists.newArrayList());
        return list.contains(ctx.getCategoryId());
    }

    private List<DztgPromoExposureInfoVO> buildPromoExposureInfo(DealCtx dealCtx) {
        try {
            BatchExProxyCouponResponseDTO batchExProxyCouponResponseDTO = dealCtx.getBatchExProxyCouponResponseDTO();
            if (batchExProxyCouponResponseDTO == null || batchExProxyCouponResponseDTO.getPromoExposureInfoList() == null) {
                return new ArrayList<>();
            }
            List<PromoExposureInfo> promoExposureInfoList = batchExProxyCouponResponseDTO.getPromoExposureInfoList();

            return promoExposureInfoList.stream()
                    .filter(Objects::nonNull)
                    .map(exposure -> {
                        DztgPromoExposureInfoVO dztgPromoExposureInfoVO = new DztgPromoExposureInfoVO();
                        dztgPromoExposureInfoVO.setFlowId(exposure.getFlowId());
                        dztgPromoExposureInfoVO.setResourceActivityId(exposure.getResourceActivityId());
                        dztgPromoExposureInfoVO.setActivityId(exposure.getActivityId());
                        dztgPromoExposureInfoVO.setMaterialId(exposure.getMaterialId());
                        dztgPromoExposureInfoVO.setRowKey(exposure.getRowKey());
                        dztgPromoExposureInfoVO.setCouponType(exposure.getCouponType());
                        dztgPromoExposureInfoVO.setAmount(exposure.getAmount());
                        dztgPromoExposureInfoVO.setTitle(exposure.getTitle());
                        dztgPromoExposureInfoVO.setCouponValueType(exposure.getCouponValueType());
                        dztgPromoExposureInfoVO.setSubTitle(exposure.getSubTitle());
                        dztgPromoExposureInfoVO.setCanAssign(exposure.getCanAssign());
                        dztgPromoExposureInfoVO.setTimeDesc(exposure.getTimeDesc());
                        dztgPromoExposureInfoVO.setTimeSubDesc(exposure.getTimeSubDesc());
                        dztgPromoExposureInfoVO.setUseEndTime(exposure.getUseEndTime());

                        return dztgPromoExposureInfoVO;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("buildPromoExposureInfo error,", e);
        }
        return null;
    }

    private List<DztgCouponInfo> buildCouponList(DealCtx dealCtx) {
        try {
            BatchExProxyCouponResponseDTO batchExProxyCouponResponseDTO = dealCtx.getBatchExProxyCouponResponseDTO();
            // 从优惠券团队拿到的券信息
            List<DztgCouponInfo> couponList = new ArrayList<>();
            if (batchExProxyCouponResponseDTO != null) {
                List<PromoCouponInfo> promoCouponInfoList = batchExProxyCouponResponseDTO.getPromoCouponInfoList();
                List<PromoCouponInfo> financialCouponInfoList = batchExProxyCouponResponseDTO.getFinancialCouponInfoList();

                // 从优惠券团队拿到的券信息
                couponList = Stream.of(promoCouponInfoList, financialCouponInfoList)
                        .filter(Objects::nonNull)
                        .flatMap(List::stream)
                        .map(this::convertPromoCouponInfo2DztgCouponInfo)
                        .collect(Collectors.toList());
            }
            List<Long> couponGroupIdList = couponList.stream().map(c -> c.getCouponGroupId()).collect(Collectors.toList());

            // 从优惠代理服务拿到的券信息，两者取并集，如果重复，以优惠券团队为准
            List<DztgCouponInfo> extraCoupon = new ArrayList<>();
            if (dealCtx.getPriceContext() != null) {
                PriceDisplayDTO normalPrice = dealCtx.getPriceContext().getNormalPrice();
                if (normalPrice != null) {
                    List<PromoDTO> usedPromos = normalPrice.getUsedPromos();
                    if (usedPromos != null) {
                        extraCoupon = usedPromos.stream()
                                .filter(this::isCoupon)
                                .map(usedPromo -> promotionDetailBuilderService.convertPromoDTO2DztgCouponInfo(usedPromo, dealCtx.getMrnVersion()))
                                .filter(Objects::nonNull)
                                .filter(dztgCouponInfo -> !couponGroupIdList.contains(dztgCouponInfo.getCouponGroupId()))
                                .collect(Collectors.toList());
                    }
                }
            }

            // 合并couponList和extraCoupon，如果couponGroupId重复，只保留couponList中的
            List<DztgCouponInfo> result = Stream.concat(couponList.stream(), extraCoupon.stream())
                    .collect(Collectors.toList());
            result.sort(Comparator.comparing(DztgCouponInfo::getStatus)
                    .thenComparing((a, b) -> {
                        String aAmountCornerMark = a.getAmountCornerMark();
                        String bAmountCornerMark = b.getAmountCornerMark();
                        if ("元".equals(aAmountCornerMark) && "折".equals(bAmountCornerMark)) {
                            return -1;
                        } else if ("折".equals(aAmountCornerMark) && "元".equals(bAmountCornerMark)) {
                            return 1;
                        } else {
                            return 0;
                        }
                    })
                    .thenComparing(DztgCouponInfo::getSourceTag)
                    .thenComparing((a, b) -> {
                        double aAmount = Double.parseDouble(a.getAmount());
                        double bAmount = Double.parseDouble(b.getAmount());
                        return Double.compare(bAmount, aAmount);
                    }));

            return result;
        } catch (Exception e) {
            log.error("buildCouponList error, ", e);
        }
        return null;
    }

    private boolean isCoupon(PromoDTO promoDTO) {
        if (promoDTO == null) {
            return false;
        }
        if (promoDTO.getIdentity() == null) {
            return false;
        }
        if (Objects.equals(promoDTO.getIdentity().getPromoType(), PromoTypeEnum.COUPON.getType()) && promoDTO.getIdentity().getPromoId() != 0) {
            return true;
        }
        if (Objects.equals(promoDTO.getIdentity().getPromoType(), PromoTypeEnum.GOVERNMENT_CONSUME_COUPON.getType())
                && org.apache.commons.lang3.StringUtils.isNumeric(promoDTO.getCouponGroupId())) {
            return true;
        }
        return false;
    }

    private DztgCouponInfo convertPromoCouponInfo2DztgCouponInfo(PromoCouponInfo coupon) {
        if (coupon == null) {
            return null;
        }
        DztgCouponInfo dztgCouponInfo = new DztgCouponInfo();
        dztgCouponInfo.setCouponGroupId(coupon.getCouponGroupId());
        dztgCouponInfo.setTitle(coupon.getTitle());
        dztgCouponInfo.setAmount(coupon.getAmount());
        dztgCouponInfo.setAmountCornerMark(coupon.getAmountCornerMark());
        dztgCouponInfo.setTimeDesc(coupon.getTimeDesc());
        dztgCouponInfo.setAmountDesc(coupon.getAmountDesc());
        dztgCouponInfo.setCouponType(coupon.getCouponType());
        dztgCouponInfo.setSourceTag(coupon.getSourceTag());
        dztgCouponInfo.setSpecificTag(coupon.getSpecificTag());
        dztgCouponInfo.setTagUrl(coupon.getTagUrl());
        dztgCouponInfo.setStatus(coupon.getStatus());
        dztgCouponInfo.setIconText(coupon.getIconText());
        dztgCouponInfo.setIconUrl(coupon.getIconUrl());
        dztgCouponInfo.setPromoCouponButton(buildDztgCouponButton(coupon.getPromoCouponButton()));

        return dztgCouponInfo;
    }

    private DztgCouponButton buildDztgCouponButton(PromoCouponButton promoCouponButton) {
        if (promoCouponButton == null) {
            return null;
        }
        DztgCouponButton dztgCouponButton = new DztgCouponButton();
        dztgCouponButton.setTitle(promoCouponButton.getTitle());
        dztgCouponButton.setClickUrl(promoCouponButton.getClickUrl());
        dztgCouponButton.setActionType(promoCouponButton.getActionType());

        return dztgCouponButton;
    }

    private int getCatBusinessClientType(DealCtx ctx) {
        if (ctx.getEnvCtx().getDztgClientTypeEnum() == null) {
            return DztgClientTypeEnum.UNKNOWN.getCode();
        }
        if (ctx.getEnvCtx().getDztgClientTypeEnum() == DztgClientTypeEnum.DIANPING_APP
                || ctx.getEnvCtx().getDztgClientTypeEnum() == DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP
                || ctx.getEnvCtx().getDztgClientTypeEnum() == DztgClientTypeEnum.MEITUAN_APP
                || ctx.getEnvCtx().getDztgClientTypeEnum() == DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP) {
            return ctx.getEnvCtx().getDztgClientTypeEnum().getCode();
        }
        return DztgClientTypeEnum.UNKNOWN.getCode();
    }

    public InflateCounponDTO buildInflateCoupon(DealCtx ctx) {
        PriceContext priceContext = ctx.getPriceContext();
        InflateCounponDTO inflateCounponDTO = new InflateCounponDTO();
        PriceDisplayDTO dealPrice = priceContext.getDealPromoPrice();
        Map<String, String> metricTags = Maps.newHashMap();
        metricTags.put("pageSource", ctx.getRequestSource());
        metricTags.put("categoryId", String.valueOf(ctx.getCategoryId()));
        metricTags.put("cityId", String.valueOf(ctx.getGpsCityId()));
        metricTags.put("isMt", String.valueOf(ctx.isMt()));
        metricTags.put("clientType", String.valueOf(getCatBusinessClientType(ctx)));
        Cat.logMetricForCount("magicalCoupon_all", metricTags);
        if (dealPrice == null) {
            return null;
        }
        Map<String, String> extendDisplayInfoMap = dealPrice.getExtendDisplayInfo();
        if (MapUtils.isEmpty(extendDisplayInfoMap)) {
            return null;
        }

        //神会员标签文案
        String magicalMemberCouponLabel = extendDisplayInfoMap.get(ExtendDisplayInfoKeyEnum.MAGICAL_MEMBER_COUPON_LABEL.getKey());

        MagicalMemberTagTextDTO memberTagText = MagicalMemberTagTextUtils.bestMagicalMemberTagTextDTO(Lists.newArrayList(magicalMemberCouponLabel));
        if (memberTagText == null) {
            return null;
        }

        Cat.logMetricForCount("magicalCoupon_hasData", metricTags);
        inflateCounponDTO.setLogoIcon(memberTagText.getMagicalMemberCouponTag()); //神会员标签
        List<SptDyeUtil.DyeTraceParam> traceParams = Lists.newArrayList();
        // step1 创建染色场景参数对象 (主场景、子场景)
        SptDyeUtil.DyeTraceParam flowEntranceParam = SptDyeUtil.DyeTraceParam.ofFlowEntrance("MAGIC_MEMBER", "default");
        traceParams.add(flowEntranceParam);
        // step1.5 根据定位城市id创建染色对象
        if (ctx.isMt()) {
            SptDyeUtil.DyeTraceParam magicMemberMtCityParam = SptDyeUtil.DyeTraceParam.ofMagicMemberMtCity(
                    String.valueOf(ctx.getGpsCityId()));
            traceParams.add(magicMemberMtCityParam);
        } else {
            SptDyeUtil.DyeTraceParam magicMemberDpCityParam = SptDyeUtil.DyeTraceParam.ofMagicMemberDpCity(
                    String.valueOf(ctx.getGpsCityId()));
            traceParams.add(magicMemberDpCityParam);
        }
        // step2 调用工具类方法染色
        SptDyeUtil.putDyeTraceParams(traceParams);

        inflateCounponDTO.setCouponDesc(memberTagText.getInflateShowText()); //膨胀券文案
        inflateCounponDTO.setMaxInflateMoney(memberTagText.getReduceMoney()); //膨胀金额文案
        inflateCounponDTO.setCouponStatus(memberTagText.getStatus()); //膨胀状态
        MagicalMemberTagShowTypeEnum magicMemberNumEnum = MagicalMemberTagShowTypeEnum.findByValue(memberTagText.getShowType());
        if (magicMemberNumEnum != null) {
            if (null != MagicalMemberTagShowTypeEnum.findByValue(memberTagText.getShowType())) {
                int code = Objects.requireNonNull(MagicalMemberTagShowTypeEnum.findByValue(memberTagText.getShowType())).getCode();
                inflateCounponDTO.setShowType(String.valueOf(code)); //展示标签类型
                if (themeLists.contains(code)) {
                    inflateCounponDTO.setLabelTheme(MAGIC_NORMAL_TAG_THEME);
                    if (StringUtils.isNotBlank(memberTagText.getInflateShowText())) {
                        inflateCounponDTO.setLogoIcon("https://p0.meituan.net/ingee/88acab73b52c30f7c987d11b11bc13813516.png");

                    }
                } else {
                    inflateCounponDTO.setLabelTheme(MAGIC_VIP_TAG_THEME);
                    if (StringUtils.isNotBlank(memberTagText.getInflateShowText())) {
                        inflateCounponDTO.setLogoIcon("https://p0.meituan.net/ingee/ccf230b75315f1f3bec4eeef9cd988de2508.png");
                    }
                }
            }
        }
        inflateCounponDTO.setCouponWalletInfo(extendDisplayInfoMap.get(ExtendDisplayInfoKeyEnum.COUPON_WALLET_INFO.getKey())); //券包模块信息
        inflateCounponDTO.setMmcPkgResult(toMmcPkgResult(extendDisplayInfoMap.get(ExtendDisplayInfoKeyEnum.COMMON_EXPERIMENT_ID_SET.getKey()))); // 神券组件实验结果
        inflateCounponDTO.setCouponGroupIdList(buildCouponGroupIdList(inflateCounponDTO.getCouponWalletInfo()));
        //组装已领券列表
        CouponItemPackageDTO couponItemPackage = buildCouponItems(dealPrice, ctx);
        inflateCounponDTO.setCouponItems(couponItemPackage.getCouponItems());
        inflateCounponDTO.setCouponIds(couponItemPackage.getCouponIds());
        inflateCounponDTO.setNibBiz(getNibBiz(ctx.getCategoryId()));

        return inflateCounponDTO;
    }

    private List<Integer> toMmcPkgResult(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        try {
            return JSONObject.parseArray(value, Integer.class);
        } catch (Exception e) {
            log.error("toMmcPkgResult, jSON parseArray failed, value={}", value, e);
        }
        return null;
    }

    public List<String> buildCouponGroupIdList(String couponWalletInfoStr) {
        List<String> result = Lists.newArrayList();
        try {
            if (StringUtils.isNotBlank(couponWalletInfoStr)) {
                JSONObject jsonObject = JSONObject.parseObject(couponWalletInfoStr);
                if (jsonObject.get("couponPackageList") != null) {
                    JSONArray jsonArray = (JSONArray) jsonObject.get("couponPackageList");
                    for (Object element : jsonArray) {
                        // 处理每个元素
                        JSONObject elementJson = (JSONObject) element;
                        String couponPackageId = (String) elementJson.get("couponPackageId");
                        if (StringUtils.isNotBlank(couponPackageId)) {
                            result.add(couponPackageId);
                        }
                    }
                }
            }
        } catch (Exception e) {
            Cat.logError(e);
        }
        return result;
    }

    public CouponItemPackageDTO buildCouponItems(PriceDisplayDTO dealPrice, DealCtx ctx) {
        CouponItemPackageDTO couponItemPackage = new CouponItemPackageDTO();
        List<CouponItemInfoDTO> couponItems = Lists.newArrayList();
        List<String> couponIds = Lists.newArrayList();

        Map<Integer, List<PromoDTO>> pricePromoInfoMap = dealPrice.getPricePromoInfoMap();
        if (MapUtils.isEmpty(pricePromoInfoMap) || CollectionUtils.isEmpty(pricePromoInfoMap.get(PricePromoInfoTypeEnum.MAGICAL_MEMBER.getType()))) {
            return couponItemPackage;
        }

        List<PromoDTO> promoDTOs = pricePromoInfoMap.get(PricePromoInfoTypeEnum.MAGICAL_MEMBER.getType());

        for (PromoDTO promoDTO : promoDTOs) {
            CouponItemInfoDTO couponItem = new CouponItemInfoDTO();
            couponItem.setEndTime(promoDTO.getEndTime().getTime());
            if (StringUtils.isNotBlank(promoDTO.getCouponId())) {
                couponIds.add(promoDTO.getCouponId());
            }
            //券码
            couponItem.setCouponCode(promoDTO.getCouponId());
            //券批次id
            couponItem.setApplyId(promoDTO.getCouponGroupId());
            Pair<String, String> pair = getValidTimeText(promoDTO.getStartTime(), promoDTO.getEndTime());
            if (pair != null) {
                //券到期时间文案
                couponItem.setCouponValidTimeText(pair.getKey());
                //到期有效时间
                couponItem.setValidTime(pair.getValue());
            }

            //券金额
            couponItem.setCouponAmount(promoDTO.getAmount().toPlainString());
            couponItem.setAmount(promoDTO.getAmount());
            //券名称
            couponItem.setCouponName(promoDTO.getCouponTitle());
            //券状态 1-有效 2-无效 （根据开始和结束时间判断）
            couponItem.setValidStatus(getCouponValidStatus(promoDTO.getStartTime(), promoDTO.getEndTime()));
            //付费神券/免费神券
            couponItem.setCouponType(getCouponType(promoDTO.getPromotionExplanatoryTags()));
            //营销扩展信息
            Map<String, String> promotionOtherInfoMap = promoDTO.getPromotionOtherInfoMap();
            //膨胀前金额
            if (promoDTO.getMinConsumptionAmount() != null) {
                couponItem.setOriginalRequiredAmount(promoDTO.getMinConsumptionAmount().multiply(new BigDecimal("100")).longValue());
            }

            Map<String, String> promotionDisplayTextMap = promoDTO.getPromotionDisplayTextMap();


            if (MapUtils.isNotEmpty(promotionDisplayTextMap)) {
                //顶部icon链接
                couponItem.setLogoIconUrl(promotionDisplayTextMap.get(PromotionTextEnum.topLeftIcon.getValue()));
                // 新顶部icon链接
                couponItem.setTopLeftNewIconInfo(promotionDisplayTextMap.get(PromotionTextEnum.topLeftNewIconInfo.getValue()));

                //膨胀按钮文案
                couponItem.setCouponButtonText(promotionDisplayTextMap.get(PromotionTextEnum.promotionButtonText.getValue()));
            }

            //膨胀后金额
            couponItem.setOriginalReduceAmount(promoDTO.getAmount().multiply(BigDecimal.valueOf(100)).longValue());

            //资产类型
            couponItem.setAssetType(Integer.parseInt(promotionOtherInfoMap.get(PromotionPropertyEnum.ASSET_TYPE.getValue())));

            if (couponItem.getAssetType() == 1) {
                //到家券用这个券id
                couponItem.setCouponCode(promotionOtherInfoMap.get(PromotionPropertyEnum.TSP_COUPON_ID.getValue()));
                couponItem.setApplyId(promotionOtherInfoMap.get(PromotionPropertyEnum.TSP_COUPON_GROUP_ID.getValue()));
            }

            //业务线
            couponItem.setBizLine(promotionOtherInfoMap.get(PromotionPropertyEnum.NIB_BIZ_LINE.getValue()));

            //实膨参数透传
            couponItem.setBizToken(promotionOtherInfoMap.get(PromotionPropertyEnum.BIZ_TOKEN.getValue()));

            //券是否可膨胀
            boolean canInflate = Boolean.parseBoolean(promotionOtherInfoMap.get(PromotionPropertyEnum.CAN_INFLATE.getValue()));
            couponItem.setCanInflate(canInflate ? CanInflateEnum.CAN_INFLATE.getCode() : CanInflateEnum.CAN_NOT_INFLATE.getCode());

            //券是否已膨胀
            boolean afterInflate = Boolean.parseBoolean(promotionOtherInfoMap.get(PromotionPropertyEnum.AFTER_INFLATE.getValue()));
            couponItem.setInflatedStatus(afterInflate ? InflateStatusEnum.AFTER_INFLATE.getCode() : InflateStatusEnum.NO_INFLATE.getCode());


            if (afterInflate) {
                //已膨胀后的神券就是普通券，门槛取基础信息字段
                couponItem.setThresholdDesc(buildCouponConditionText(promoDTO.getMinConsumptionAmount()));
                couponItem.setThresholdAmount(promoDTO.getMinConsumptionAmount());
            } else {
                if (canInflate) {
                    //未膨胀券的神券，
                    if (null != promotionOtherInfoMap.get(PromotionPropertyEnum.AFTER_INFLATE_REQUIRE_AMOUNT.getValue())) {
                        couponItem.setThresholdDesc(promoDTO.getPriceLimitDesc());
                        couponItem.setThresholdAmount(new BigDecimal(promotionOtherInfoMap.get(PromotionPropertyEnum.AFTER_INFLATE_REQUIRE_AMOUNT.getValue())));
                    }
                    if (null != promotionOtherInfoMap.get(PromotionPropertyEnum.MAX_INFLATE_MONEY.getValue())) {
                        //最高膨胀至XX
                        couponItem.setCouponDesc(buildCouponDesc(BigDecimal.valueOf(Double.parseDouble(promotionOtherInfoMap.get(PromotionPropertyEnum.MAX_INFLATE_MONEY.getValue())) / 100.0).stripTrailingZeros()));
                    }
                } else {
                    couponItem.setThresholdDesc(buildCouponConditionText(promoDTO.getMinConsumptionAmount()));
                    couponItem.setThresholdAmount(promoDTO.getMinConsumptionAmount());
                }
            }
            if (promotionOtherInfoMap.get(PromotionPropertyEnum.QUERY_INFLATE_FLAG.getValue()) != null
                    && "true".equals(promotionOtherInfoMap.get(PromotionPropertyEnum.QUERY_INFLATE_FLAG.getValue()))) {
                // 1标识券已锁价
                couponItem.setQueryInflateFlag(1);
            }
            // 券聚合数量
            if (LionConfigUtils.hitCouponAggregateSwitch(ctx.getUserId4P())) {
                couponItem.setCouponNum(getCouponNum(promotionOtherInfoMap));
            }
            couponItems.add(couponItem);
        }


        //券聚合（不聚合领塞、膨后的券）
        List<CouponItemInfoDTO> result = aggregateCouponProcess(couponItems, ctx);
        //券的排序
        couponItems = result.stream()
                .sorted(Comparator.comparing(CouponItemInfoDTO::getCanInflate)
                        .reversed()
                        .thenComparing(CouponItemInfoDTO::getQueryInflateFlag, Comparator.comparingInt(c -> c))
                        .thenComparing(CouponItemInfoDTO::getAmount, (price1, price2) -> -price1.compareTo(price2))
                        .thenComparing(CouponItemInfoDTO::getThresholdAmount)
                        .thenComparing(CouponItemInfoDTO::getCouponType)
                        .thenComparing(CouponItemInfoDTO::getEndTime))
                .collect(Collectors.toList());
        couponItemPackage.setCouponItems(couponItems);
        couponItemPackage.setCouponIds(couponIds);

        return couponItemPackage;
    }

    /**
     * 券聚合逻辑
     *
     * @param couponItems
     * @return
     */
    private List<CouponItemInfoDTO> aggregateCouponProcess(List<CouponItemInfoDTO> couponItems, DealCtx ctx) {
        // 新逻辑不用聚合
        if (LionConfigUtils.hitCouponAggregateSwitch(ctx.getUserId4P())) {
            return couponItems;
        }
        // 走老逻辑
        return buildCouponItemInfos(couponItems);
    }

    /**
     * 获取券数量
     *
     * @param promotionOtherInfoMap
     * @return
     */
    public int getCouponNum(Map<String, String> promotionOtherInfoMap) {
        try {
            String couponNum = promotionOtherInfoMap.get(PromotionPropertyEnum.COUPON_AGGREGATE_NUM.getValue());
            if (couponNum == null) {
                return 1;
            }
            return Integer.parseInt(couponNum);
        } catch (Exception e) {
            logger.error("getCouponNum error, e{}", e);
            return 0;
        }
    }

    private List<CouponItemInfoDTO> buildCouponItemInfos(List<CouponItemInfoDTO> couponItems) {
        if (CollectionUtils.isEmpty(couponItems)) {
            return couponItems;
        }

        Map<String, List<CouponItemInfoDTO>> couponListMap = Maps.newHashMap();
        for (CouponItemInfoDTO couponItem : couponItems) {
            if (couponItem.getInflatedStatus() == InflateStatusEnum.AFTER_INFLATE.getCode()) {
                //膨胀后券
                couponListMap.put(String.valueOf(couponItem.getCouponCode()), Lists.newArrayList(couponItem));
            } else if (couponItem.getCouponType() == CouponTypeEnum.FREE_COUPON.getCode()) {
                //免费券
                couponListMap.put(String.valueOf(couponItem.getCouponCode()), Lists.newArrayList(couponItem));
            } else {
                String key = couponItem.getApplyId() + couponItem.getCouponType() + couponItem.getCouponName() + couponItem.getCouponAmount() + couponItem.getThresholdDesc() + couponItem.getCouponValidTimeText();
                if (couponListMap.get(key) == null) {
                    couponListMap.put(key, Lists.newArrayList(couponItem));
                } else {
                    List<CouponItemInfoDTO> couponItemInfos = couponListMap.get(key);
                    couponItemInfos.add(couponItem);
                }
            }
        }

        List<CouponItemInfoDTO> result = Lists.newArrayList();

        for (Map.Entry<String, List<CouponItemInfoDTO>> entry : couponListMap.entrySet()) {
            List<CouponItemInfoDTO> list = entry.getValue();
            CouponItemInfoDTO couponItem = list.get(0);
            couponItem.setCouponNum(list.size());
            result.add(couponItem);
        }

        return result;
    }

    private String buildCouponDesc(BigDecimal price) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.buildCouponDesc(java.math.BigDecimal)");
        return "最高膨胀至" + price.toPlainString();

    }

    private String buildCouponConditionText(BigDecimal price) {
        if (price.compareTo(BigDecimal.valueOf(0)) == 0) {
            return "无门槛";
        }

        return "满" + price.toPlainString() + "可用";
    }

    private List<PromoActivityInfoVO> buildPromoActivityList(DealCtx ctx) {
        try {
            List<PromoActivityInfoVO> result = new ArrayList<>();

            // 平台拼团
            PromoActivityInfoVO costEffectivePinTuan = buildCostEffectivePintuan(ctx);
            if (costEffectivePinTuan != null) {
                result.add(costEffectivePinTuan);
            }

            BatchExProxyCouponResponseDTO batchExProxyCouponResponseDTO = ctx.getBatchExProxyCouponResponseDTO();
            if (batchExProxyCouponResponseDTO == null || batchExProxyCouponResponseDTO.getPromoReturnInfoList() == null) {
                return result;
            }
            List<PromoReturnInfo> returnInfoList = batchExProxyCouponResponseDTO.getPromoReturnInfoList();

            // 闲时特惠
            PromoActivityInfoVO idlePromo = buildIdlePromo(ctx);
            if (idlePromo != null) {
                result.add(idlePromo);
            }

            // 商户多份立减
            PromoActivityInfoVO buyMoreReduction = buildBuyMoreReduction(ctx);
            if (buyMoreReduction != null) {
                result.add(buyMoreReduction);
            }

            // 平台多买多折
            PromoActivityInfoVO buyMoreDiscount = buildBuyMoreDiscount(ctx);
            if (buyMoreDiscount != null) {
                result.add(buyMoreDiscount);
            }

            // 商家拼团
            PromoActivityInfoVO pintuan = buildPintuanActivity(ctx);
            if (pintuan != null && costEffectivePinTuan == null) {
                result.add(pintuan);
            }

            // 返券、返礼、金融券活动
            List<PromoActivityInfoVO> couponActivityList = returnInfoList.stream()
                    .filter(Objects::nonNull)
                    .map(returnInfo -> {
                        PromoActivityInfoVO promoActivityInfoVO = new PromoActivityInfoVO();
                        promoActivityInfoVO.setBonusType(returnInfo.getBonusType());
                        promoActivityInfoVO.setText(returnInfo.getText());
                        promoActivityInfoVO.setStyle(returnInfo.getStyle());
                        promoActivityInfoVO.setLeadUrl(returnInfo.getLeadUrl());
                        promoActivityInfoVO.setShortText(returnInfo.getText());

                        return promoActivityInfoVO;
                    })
                    .collect(Collectors.toList());

            result.addAll(couponActivityList);
            return result;
        } catch (Exception e) {
            log.error("buildPromoActivityList error,", e);
        }

        return null;
    }

    public PromoActivityInfoVO buildCostEffectivePintuan(DealCtx context) {
        if (!CostEffectivePinTuanUtils.enhancedStyle(context) || Objects.isNull(context.getCostEffectivePinTuan().getGroupSuccCountMin())) {
            return null;
        }

        try {
            PriceDisplayDTO price = context.getPriceContext().getCostEffectivePrice();
            if (price == null) {
                return null;
            }

            PromoActivityInfoVO promoActivityInfoVO = new PromoActivityInfoVO();
            promoActivityInfoVO.setBonusType("拼团");
            promoActivityInfoVO.setText(getText(context, price));
            promoActivityInfoVO.setStyle(0);
            promoActivityInfoVO.setShortText(getText(context, price));
            return promoActivityInfoVO;
        } catch (Exception e) {
            log.error("buildBuyMoreReduction error, ", e);
        }
        return null;
    }

    private String getText(DealCtx context, PriceDisplayDTO price) {
        int textSize = 11;
        RichText.TextItem defaultTextItem = new RichText.TextItem();
        defaultTextItem.setStrikethrough(false);
        defaultTextItem.setTextcolor("#222222");
        defaultTextItem.setTextsize(textSize);
        defaultTextItem.setTextstyle(TextStyle.DEFAULT.getStyle());
        defaultTextItem.setUnderline(false);

        RichText.TextItem highlightTextItem = new RichText.TextItem();
        highlightTextItem.setStrikethrough(false);
        highlightTextItem.setTextcolor("#222222");
        highlightTextItem.setTextsize(textSize);
        highlightTextItem.setTextstyle(TextStyle.DEFAULT.getStyle());
        highlightTextItem.setUnderline(false);

        RichText richText = new RichText();
        richText.getTextItemList().add(RichTextUtil.buildTextItem(context.getCostEffectivePinTuan().getGroupSuccCountMin() + "人团，每人到手价", highlightTextItem));
        richText.getTextItemList().add(RichTextUtil.buildTextItem("￥" + price.getPrice(), highlightTextItem));
        return richText.toString();
    }

    private PromoActivityInfoVO buildIdlePromo(DealCtx context) {
        try {
            PriceDisplayDTO promoPrice = context.getPriceContext().getIdlePromoPrice();
            if (promoPrice == null || CollectionUtils.isEmpty(promoPrice.getUsedPromos())) {
                return null;
            }

            PromoActivityInfoVO promoActivityInfoVO = new PromoActivityInfoVO();
            promoActivityInfoVO.setBonusType("限时");
            String consumeTimeDesc = promoPrice.getUsedPromos().get(0).getConsumeTimeDesc() == null ? "" : promoPrice.getUsedPromos().get(0).getConsumeTimeDesc();
            String price = PriceHelper.dropLastZero(promoPrice.getPrice());
            String text = String.format("限时特惠，%s ¥%s/次", consumeTimeDesc, price);
            promoActivityInfoVO.setText(text);

            promoActivityInfoVO.setStyle(0);
            promoActivityInfoVO.setLeadUrl(UrlHelper.getIdleHoursBuyUrl(context, context.getMtCityId()));
            String shortText = promoPrice.getUsedPromos().get(0).getIdentity() == null ? null : promoPrice.getUsedPromos().get(0).getIdentity().getPromoTypeDesc();
            if (StringUtils.isBlank(shortText)) {
                String promoAmount = PriceHelper.dropLastZero(promoPrice.getPromoAmount());
                shortText = String.format("工作日立减%s元", promoAmount);
            }
            promoActivityInfoVO.setShortText(shortText);

            return promoActivityInfoVO;
        } catch (Exception e) {
            log.error("buildIdlePromo error, ", e);
        }
        return null;
    }

    private PromoActivityInfoVO buildBuyMoreReduction(DealCtx context) {
        try {
            PriceDisplayDTO price = context.getPriceContext().getNormalPrice();
            if (price == null || CollectionUtils.isEmpty(price.getMorePromos())) {
                return null;
            }

            PromoDTO buyMoreReduction = null;
            for (PromoDTO promoDTO : price.getMorePromos()) {
                if (promoDTO.getIdentity() != null && "BUY_MORE_REDUCTION".equals(promoDTO.getIdentity().getPromoShowType())) {
                    buyMoreReduction = promoDTO;
                    break;
                }
            }
            if (buyMoreReduction == null) {
                return null;
            }

            PromoActivityInfoVO promoActivityInfoVO = new PromoActivityInfoVO();
            promoActivityInfoVO.setBonusType("促销");
            promoActivityInfoVO.setText(buyMoreReduction.getExtendDesc());
            promoActivityInfoVO.setStyle(0);
            promoActivityInfoVO.setShortText(buyMoreReduction.getTag());

            return promoActivityInfoVO;
        } catch (Exception e) {
            log.error("buildBuyMoreReduction error, ", e);
        }
        return null;
    }

    private PromoActivityInfoVO buildBuyMoreDiscount(DealCtx context) {
        try {
            PriceDisplayDTO price = context.getPriceContext().getNormalPrice();
            if (price == null || CollectionUtils.isEmpty(price.getMorePromos())) {
                return null;
            }

            PromoDTO buyMoreDiscount = null;
            for (PromoDTO promoDTO : price.getMorePromos()) {
                if (promoDTO.getIdentity() != null && "BUY_MORE_DISCOUNT".equals(promoDTO.getIdentity().getPromoShowType())) {
                    buyMoreDiscount = promoDTO;
                    break;
                }
            }
            if (buyMoreDiscount == null) {
                return null;
            }

            PromoActivityInfoVO promoActivityInfoVO = new PromoActivityInfoVO();
            promoActivityInfoVO.setBonusType("促销");
            promoActivityInfoVO.setText(buyMoreDiscount.getExtendDesc());
            promoActivityInfoVO.setStyle(0);
            promoActivityInfoVO.setShortText(buyMoreDiscount.getTag());

            return promoActivityInfoVO;
        } catch (Exception e) {
            log.error("buildBuyMoreDiscount error, ", e);
        }
        return null;
    }

    private PromoActivityInfoVO buildPintuanActivity(DealCtx context) {
        try {
            PinProductBrief pinProductBrief = context.getPinProductBrief();
            if (!BuyButtonHelper.isValidPinPool(context)) {
                return null;
            }

            Integer pinPersonNum = pinProductBrief.getPinPersonNum();
            String pinPriceStr = PriceHelper.dropLastZero(pinProductBrief.getPrice().setScale(2, RoundingMode.CEILING));
            String preStr = String.format("%s人团 ¥%s元/人，", pinPersonNum, pinPriceStr);

            String shortText = String.format("特惠%s人团", pinPersonNum);

            PriceDisplayDTO normalPrice = context.getPriceContext().getNormalPrice();
            if (normalPrice != null && normalPrice.getPrice() != null) {
                String minusPrice = normalPrice.getPrice().subtract(new BigDecimal(pinPriceStr))
                        .setScale(2, RoundingMode.CEILING).stripTrailingZeros().toPlainString();
                BigDecimal minusPriceDecimal = new BigDecimal(minusPrice);
                if (minusPriceDecimal.compareTo(BigDecimal.ZERO) < 0) {
                    // minusPrice是负数, 说明拼团价格比正常价格还高
                    return null;
                }
                String fullStr = preStr + "每人多省" + minusPrice + "元";

                PromoActivityInfoVO promoActivityInfoVO = new PromoActivityInfoVO();
                promoActivityInfoVO.setBonusType("拼团");
                promoActivityInfoVO.setText(fullStr);
                promoActivityInfoVO.setStyle(0);
                promoActivityInfoVO.setLeadUrl(UrlHelper.getAppUrl(context.getEnvCtx(), pinProductBrief.getUrl(), context.isMt()));
                promoActivityInfoVO.setShortText(shortText);

                return promoActivityInfoVO;
            }
            return null;
        } catch (Exception e) {
            log.error("buildPintuanActivity error, ", e);
        }
        return null;
    }

    /**
     * 广平分销场景优惠展示逻辑
     */
    private void buildOdpPromoDetailInfo(DealCtx ctx, PriceDisplayDTO odpPrice, DealGroupPBO result) {
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        BigDecimal dealGroupPrice = ctx.getDealGroupBase().getDealGroupPrice();
        BigDecimal marketPrice = ctx.getDealGroupBase().getMarketPrice();
        BigDecimal promoPrice = odpPrice != null ? odpPrice.getPrice() : dealGroupPrice;

        String dealGroupPriceStr = PriceHelper.format(dealGroupPrice);
        String marketPriceStr = PriceHelper.format(ctx.getDealGroupBase().getMarketPrice());
        String promoPriceStr = PriceHelper.format(promoPrice);

        promoDetailModule.setDealGroupPrice(dealGroupPriceStr);
        promoDetailModule.setMarketPrice(marketPriceStr);
        promoDetailModule.setPromoPrice(promoPriceStr);
        promoDetailModule.setPreSaleDealGroupPrice(getPreSaleDealGroupPrice(ctx));

        boolean existReductionPromo = false;
        if (odpPrice != null && CollectionUtils.isNotEmpty(odpPrice.getUsedPromos())) {
            List<ReductionPromoDetail> reductionPromoDetails = Lists.newArrayList();
            BigDecimal totalReductionPromo = new BigDecimal(0);
            for (PromoDTO promoDTO : odpPrice.getUsedPromos()) {
                if (promoDTO == null || promoDTO.getIdentity() == null || promoDTO.getAmount() == null) {
                    continue;
                }
                if (promoDTO.getIdentity().getPromoType() == PromoTypeEnum.NORMAL_PROMO.getType()) {
                    int sourceType = promoDTO.getIdentity().getSourceType();
                    String reduceName = sourceType == 1 ? "平台补贴" : (sourceType == 2 ? "商家神券" : null);

                    ReductionPromoDetail couponPromoDetail = new ReductionPromoDetail();
                    couponPromoDetail.setReduceName(reduceName);
                    couponPromoDetail.setReducePromo(PriceHelper.format(promoDTO.getAmount()));

                    existReductionPromo = true;
                    reductionPromoDetails.add(couponPromoDetail);
                    totalReductionPromo = totalReductionPromo.add(promoDTO.getAmount());
                }
            }
            promoDetailModule.setReductionPromo(PriceHelper.format(totalReductionPromo));
            promoDetailModule.setReductionPromoDetails(reductionPromoDetails);
        }

        String discountRate = PriceHelper.calcDiscountRate(marketPrice, promoPrice);
        if (discountRate != null) {
            String suffix = ctx.isCanNotBuy() || !existReductionPromo ? "折" : "折惊爆价";
            promoDetailModule.setDiscountRate(discountRate);
            promoDetailModule.setDiscountRateDescription(discountRate + suffix);
        }
        result.setPromoDetailModule(promoDetailModule);
    }

    private String getPreSaleDealGroupPrice(DealCtx dealCtx) {
        if (!DealAttrHelper.isPreSale(dealCtx.getAttrs())) {
            return null;
        }
        AttributeDTO dealGroupPriceAttribute = dealCtx.getAttrs().stream()
                .filter(Objects::nonNull)
                .filter(attributeDTO -> DealAttrKeys.PRE_SALE_DEAL_GROUP_PRICE.equals(attributeDTO.getName()))
                .findFirst()
                .orElse(null);
        if (dealGroupPriceAttribute == null || CollectionUtils.isEmpty(dealGroupPriceAttribute.getValue())) {
            return null;
        }
        return new BigDecimal(dealGroupPriceAttribute.getValue().get(0)).stripTrailingZeros().toPlainString();
    }

    private String getPriceDisplayText(DealCtx dealCtx) {
        if (!isPayAndNotOnePrice(dealCtx.getDealGroupDTO())) {
            return null;
        }
        return Lion.getString(Environment.getAppName(), "com.sankuai.dzu.tpbase.dztgdetailweb.tohome.fix.display.text", "维修费以师傅上门报价为准");
    }

    private void buildAtmosphereBarAndGeneralPromoDetailInfo(DealCtx ctx, PriceDisplayDTO priceDisplayDTO, DealGroupPBO result) {
        try {
            PriceContext priceContext = ctx.getPriceContext();
            if (priceContext == null || priceDisplayDTO == null) {
                return;
            }
            int categoryId = ctx.getCategoryId();

            PromoDetailConfig promoDetailConfig = gson.fromJson(Lion.get("com.sankuai.dzu.tpbase.dztgdetailweb.promoDetailConfig"), new com.google.common.reflect.TypeToken<PromoDetailConfig>() {
            }.getType());
            if (promoDetailConfig == null) {
                return;
            }

            Map<Integer, PromoDetailConfigDto> dealPublishCategoryId2PromoDetailConfigDtoMap = promoDetailConfig.getDealPublishCategoryId2PromoDetailConfigDtoMap();
            if (MapUtils.isEmpty(dealPublishCategoryId2PromoDetailConfigDtoMap) || !dealPublishCategoryId2PromoDetailConfigDtoMap.containsKey(categoryId)) {
                return;
            }
            PromoDetailConfigDto promoDetailConfigDto = dealPublishCategoryId2PromoDetailConfigDtoMap.get(categoryId);

            String serviceType = DealUtils.getServiceType(ctx);
            if (CollectionUtils.isNotEmpty(promoDetailConfigDto.getServiceTypeList()) && !promoDetailConfigDto.getServiceTypeList().contains(serviceType)) {
                return;
            }

            PromoDetailModule promoDetailModule = result.getPromoDetailModule();
            if (promoDetailModule == null) {
                promoDetailModule = new PromoDetailModule();
            }

            if (CollectionUtils.isNotEmpty(promoDetailConfigDto.getAtmosphereBarPromoTypeList())) {
                List<String> atmosphereBarPromoTypeList = promoDetailConfigDto.getAtmosphereBarPromoTypeList();
                List<DealBestPromoDetailDTO> promoDetailList = getPromoDetailList(priceDisplayDTO, atmosphereBarPromoTypeList);
                if (CollectionUtils.isNotEmpty(promoDetailList)) {
                    List<DealBestPromoDetail> newPromoDetailList = promoDetailList.stream().filter(Objects::nonNull).map(DealBestPromoDetailDTO::getDealBestPromoDetail).collect(Collectors.toList());
                    promoDetailModule.setAtmosphereBarPromoList(newPromoDetailList);
                }
            }

            if (CollectionUtils.isNotEmpty(promoDetailConfigDto.getGeneralPromoDetailTypeList())) {
                List<String> generalPromoDetailTypeList = promoDetailConfigDto.getGeneralPromoDetailTypeList();
                List<DealBestPromoDetailDTO> promoDetailList = getPromoDetailList(priceDisplayDTO, generalPromoDetailTypeList);

                List<DealBestPromoDetail> newPromoDetailList = promoDetailList.stream().filter(Objects::nonNull)
                        .sorted(Comparator.comparing(DealBestPromoDetailDTO::getPromoAmount).reversed())
                        .collect(Collectors.toList())
                        .stream().map(DealBestPromoDetailDTO::getDealBestPromoDetail)
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(newPromoDetailList)) {
                    promoDetailModule.setGeneralPromoDetailList(newPromoDetailList);
                }
            }

            BigDecimal dealGroupPrice = ctx.getDealGroupBase().getDealGroupPrice();
            BigDecimal promoPrice = priceDisplayDTO.getPrice();
            if (isPinTuanStyle(ctx)) {
                promoPrice = ctx.getPriceContext().getCostEffectivePrice().getPrice();
            }

            if (promoPrice != null) {
                promoDetailModule.setPromoPrice(PriceHelper.formatPrice(promoPrice));
            }
            if (dealGroupPrice != null && promoPrice != null && dealGroupPrice.compareTo(promoPrice) > 0) {
                promoDetailModule.setTotalPromo(PriceHelper.formatPrice(dealGroupPrice.subtract(promoPrice)));
            }

        } catch (Exception e) {
            logger.error("buildAtmosphereBarAndGeneralPromoDetailInfo error", e);
        }
    }

    private List<DealBestPromoDetailDTO> getPromoDetailList(PriceDisplayDTO priceDisplayDTO, List<String> promoTypeList) {
        List<DealBestPromoDetailDTO> promoDetailList = new ArrayList<>();

        promoTypeList.stream().filter(Objects::nonNull).forEach(
                promoDetailType -> {
                    PromoDetailHandler promoDetailHandler = PromoDetailLocator.getByEnum(PromoDetailEnum.getByType(promoDetailType));
                    if (promoDetailHandler == null) {
                        return;
                    }
                    DealBestPromoDetailDTO promoDetail = promoDetailHandler.getDealBestPromoDetail(priceDisplayDTO);
                    if (promoDetail != null) {
                        promoDetailList.add(promoDetail);
                    }
                }
        );

        return promoDetailList;
    }

    /**
     * 包含团购优惠（市场价-售卖价）的优惠明细
     */
    private void buildDealPromoDetailInfo(DealCtx ctx, PriceDisplayDTO priceDisplayDTO, DealGroupPBO result) {
        if ((priceDisplayDTO == null || CollectionUtils.isEmpty(priceDisplayDTO.getUsedPromos()))
                && !EduDealUtils.isShortClass(ctx) && !EduDealUtils.isNightSchoolDeal(ctx)) {
            if (needHideMarketPrice(ctx) && result.getPromoDetailModule() != null) {
                result.getPromoDetailModule().setMarketPrice(null);
            }
            return;
        }
        //0元预约-不展示优惠信息
        if (ctx.isFreeDeal()) {
            return;
        }
        // 留资型团单页面且（超值特惠套餐或小程序）不展示优惠信息
        if (DealUtils.isLeadsDeal(ctx) && (ctx.isHitSpecialValueDeal() || !(ctx.getEnvCtx().judgeMainApp()))) {
            return;
        }

        PromoDetailModule promoDetailModule = result.getPromoDetailModule();
        if (promoDetailModule == null) {
            promoDetailModule = new PromoDetailModule();
            result.setPromoDetailModule(promoDetailModule);
        }

        Boolean newStyleSwitch = Lion.getBoolean(APP_KEY, COUPON_ALLEVIATE_PROMO_STYLE_CONFIG);
        promoDetailModule.setPromoNewStyle(newStyleSwitch && getCouponAlleviate1ExpResult(ctx));

        promoDetailModule.setFinalPrice(priceDisplayDTO == null || priceDisplayDTO.getPrice() == null ? "0.01" : PriceHelper.dropLastZero(priceDisplayDTO.getPrice()));
        //开店宝/阿波罗App/PC
        buildSalePriceBySource(ctx, promoDetailModule);
        List<DealBestPromoDetail> bestPromoDetails = Lists.newArrayList();
        promoDetailModule.setBestPromoDetails(bestPromoDetails);
        if (!ReassuredRepairUtil.isTagPresent(ctx)) {
            promoDetailModule.setMarketPricePromo(priceDisplayDTO == null || priceDisplayDTO.getPromoAmount() == null ? "0" : PriceHelper.dropLastZero(priceDisplayDTO.getPromoAmount()));
        }
        promoDetailModule.setPerPrice(getPerPrice(priceDisplayDTO));
        promoDetailModule.setDealGifts(buildDealGifts(ctx));

        boolean isZuLiaoButtonNewStyle = ctx.getPriceContext().isZuLiaoButtonNewStyle();

        List<PromoDTO> usedPromos = Optional.ofNullable(priceDisplayDTO).map(PriceDisplayDTO::getUsedPromos)
                .orElse(Lists.newArrayList());

        PriceDisplayDTO memberCardPrice = ctx.getPriceContext().getDcCardMemberPrice();
        if (isShoppingCartStyle(ctx)) {
            if (ctx.getBuyBar() != null && CollectionUtils.isNotEmpty(ctx.getBuyBar().getBuyBtns())) {
                DealBuyBtn dealBuyBtn = ctx.getBuyBar().getBuyBtns().get(0);
                if (dealBuyBtn.getDetailBuyType() == BuyBtnTypeEnum.MEMBER_CARD.getCode()) {
                    usedPromos = memberCardPrice.getUsedPromos();
                    promoDetailModule.setMarketPricePromo(memberCardPrice.getPromoAmount() == null ? "0" : PriceHelper.dropLastZero(memberCardPrice.getPromoAmount()));
                    promoDetailModule.setFinalPrice(memberCardPrice.getPrice() == null ? "0.01" : PriceHelper.dropLastZero(memberCardPrice.getPrice()));
                }
            }

        } else if (memberCardPrice != null && CollectionUtils.isNotEmpty(memberCardPrice.getUsedPromos()) && isZuLiaoButtonNewStyle
                && (buttonStyleHelper.singleMemberButton(ctx) || buttonStyleHelper.doubleButtonMemberRight(ctx))) {
            usedPromos = memberCardPrice.getUsedPromos();
            promoDetailModule.setMarketPricePromo(memberCardPrice.getPromoAmount() == null ? "0" : PriceHelper.dropLastZero(memberCardPrice.getPromoAmount()));
            promoDetailModule.setFinalPrice(memberCardPrice.getPrice() == null ? "0.01" : PriceHelper.dropLastZero(memberCardPrice.getPrice()));
        } else if (isPinTuanStyle(ctx)) {
            promoDetailModule.setFinalPrice(Objects.isNull(ctx.getPriceContext().getCostEffectivePrice().getPrice()) ? "0.01" : PriceHelper.dropLastZero(ctx.getPriceContext().getCostEffectivePrice().getPrice()));
        }
        // 强化样式下需要展示拼团优惠
        if (CostEffectivePinTuanUtils.enhancedStyle(ctx) && Objects.nonNull(ctx.getPriceContext().getCostEffectivePrice()) && CollectionUtils.isNotEmpty(ctx.getPriceContext().getCostEffectivePrice().getUsedPromos())) {
            usedPromos = ctx.getPriceContext().getCostEffectivePrice().getUsedPromos();
        }
        for (PromoDTO promoDTO : usedPromos) {
            if (promoDTO.getIdentity() == null || promoDTO.getAmount() == null || StringUtils.isEmpty(promoDTO.getIdentity().getPromoTypeDesc())) {
                continue;
            }
            DealBestPromoDetail bestPromoDetail = new DealBestPromoDetail();
            bestPromoDetail.setIconUrl(promoDTO.getIcon());
            bestPromoDetail.setPromoName(promoDTO.getIdentity().getPromoTypeDesc());
            if (BeautyBianMeiCouponHelper.isBeautyBianMeiCouponPromo(promoDTO)) {
                bestPromoDetail.setPromoName(BeautyBianMeiCouponHelper.getBeautyBianMeiCouponPromoName(promoDTO));
            }
            bestPromoDetail.setPromoAmount(PriceHelper.dropLastZero(promoDTO.getAmount()));
            bestPromoDetail.setPromoTag(convertPromoTag(promoDTO, ctx.getCategoryId()));
            String promoDesc = convertPromoDesc(promoDTO, promoDetailModule, bestPromoDetail.getPromoTag());
            List<String> formatted = ParallDealBuilderProcessorUtils.formatToRichText(promoDesc, MAX_CHAR, MAX_CHARS_PER);
            bestPromoDetail.setPromoDesc(promoDesc);
            bestPromoDetail.setPromoDescList(formatted);
            buttonStyleHelper.entertainmentMemberPromoDetailComplete(ctx, isZuLiaoButtonNewStyle, result, promoDTO, promoDetailModule, bestPromoDetail);
            bestPromoDetails.add(bestPromoDetail);
        }

        if (GreyUtils.isShowMarketPrice(ctx) || isZuLiaoButtonNewStyle) {
            promoDetailModule.setShowMarketPrice(true);
            setMarketPromoDiscount(promoDetailModule, priceDisplayDTO);
            if (CollectionUtils.isNotEmpty(bestPromoDetails)) {
                promoDetailModule.setShowBestPromoDetails(true);
            }
        }
        if (ctx.isEnableCardStyle() || ctx.isEnableCardStyleV2()) {
            setMarketPromoDiscount(promoDetailModule, priceDisplayDTO);
            promoDetailModuleBuilderService.setPriceStrengthDesc(ctx, promoDetailModule, priceDisplayDTO);
            // 设置买贵必赔标签
            promoDetailModuleBuilderService.setBestPriceGuaranteeTag(promoDetailModule, ctx);
        }
        if (isPinTuanStyle(ctx) && Objects.nonNull(ctx.getPriceContext()) && Objects.nonNull(ctx.getPriceContext().getCostEffectivePrice())) {
            setMarketPromoDiscount(promoDetailModule, ctx.getPriceContext().getCostEffectivePrice());
        }
        // 设置预览团单折扣
        if (DealUtils.isPreviewDeal(ctx)){
            setPreviewMarketPromoDiscount(promoDetailModule, ctx.getDealGroupDTO());
        }
        // 无忧通团单不展示优惠标签以及门市价
        if (DealAttrHelper.isWuyoutong(ctx)) {
            setWuyoutongPromo(promoDetailModule);
        }
        //局改团详不展示优惠标签
        if (ReassuredRepairUtil.isTagPresent(ctx)) {
            //局改 团详页显示的是 门店售价
            buildReassuredRepairPromoDetailModule(ctx, priceDisplayDTO, promoDetailModule);
        }

        if (DealAttrHelper.isRepairPrepayDeal(ctx.getDealGroupDTO())) {
            promoDetailModuleBuilderService.buildRepairPrepayDealPromoDetailModule(ctx, promoDetailModule);
        }
        // 短期课添加课时单价
        EduDealUtils.modifyDiscountRateStr(ctx, promoDetailModule,
                Optional.ofNullable(priceDisplayDTO).map(PriceDisplayDTO::getMarketPrice).orElse(BigDecimal.ZERO),
                Optional.ofNullable(priceDisplayDTO).map(PriceDisplayDTO::getPrice).orElse(BigDecimal.ZERO));
    }

    private void buildSalePriceBySource(DealCtx ctx, PromoDetailModule promoDetailModule){
        // 如果是来自 预览端
        if (RequestSourceEnum.fromPreview(ctx.getRequestSource())
                || ctx.getEnvCtx().isApollo()
                || ctx.getEnvCtx().isDpMerchant()){
            DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
            if (dealGroupDTO != null && dealGroupDTO.getPrice() != null) {
                PriceDTO price = dealGroupDTO.getPrice();
                promoDetailModule.setFinalPrice(price.getSalePrice());
            }
        }
    }

    private boolean getCouponAlleviate1ExpResult(DealCtx ctx) {
        try {
            AbConfig couponAlleviate1Config = DealDouHuUtil.getCouponAlleviateConfig(ctx, null, Cons.COUPON_ALLEVIATE_1_ABKEYS);
            if (couponAlleviate1Config == null || StringUtils.isEmpty(couponAlleviate1Config.getExpResult())) {
                // 默认兜底就是false,不走新的展示样式
                return false;
            }
            // c是实验组
            return "c".equals(couponAlleviate1Config.getExpResult());
        } catch (Exception e) {
            log.error("getCouponAlleviate1ExpResult error", e);
        }
        return false;
    }

    private void buildReassuredRepairPromoDetailModule(DealCtx ctx, PriceDisplayDTO priceDisplayDTO, PromoDetailModule promoDetailModule) {
        promoDetailModule.setFinalPrice(priceDisplayDTO.getMarketPrice() == null ? "0.01" : PriceHelper.dropLastZero(priceDisplayDTO.getMarketPrice()));
        promoDetailModule.setPricePostfix(getPriceUnit(ctx));
        promoDetailModule.setDescriptionTags(getDescriptionTags(ctx));
        promoDetailModule.setPriceDisplayText(DETAILED_TIPS);
        promoDetailModule.setMarketPrice(StringUtils.EMPTY);
        promoDetailModule.setMarketPromoDiscount(StringUtils.EMPTY);
        promoDetailModule.setBestPromoDetails(null);
    }

    private List<DescriptionTag> getDescriptionTags(DealCtx ctx) {
        List<DescriptionTag> list = Lion.getList(APP_KEY, BUREAU_CHANGES_GROUP_PURCHASE_PRICE, DescriptionTag.class);
        if (CollectionUtils.isNotEmpty(list)) {
            DescriptionTag descriptionTag = list.get(0);
            descriptionTag.setName(descriptionTag.getName().replace("${str}", ctx.getDealGroupBase().getDealGroupPrice().toString()));
            return list;
        }
        return Collections.emptyList();
    }

    private String getPriceUnit(DealCtx ctx) {
        Map<String, ServiceProjectAttrDTO> serviceProjectAttrDTOMap = getStringServiceProjectAttrDTOMap(ctx);
        if (serviceProjectAttrDTOMap.containsKey("priceunit")) {
            ServiceProjectAttrDTO priceunit = serviceProjectAttrDTOMap.get("priceunit");
            return "/" + priceunit.getAttrValue();
        }
        return null;
    }

    private Map<String, ServiceProjectAttrDTO> getStringServiceProjectAttrDTOMap(DealCtx ctx) {
        return Optional.ofNullable(ctx.getDealGroupDTO().getServiceProject())
                .map(DealGroupServiceProjectDTO::getMustGroups)
                .filter(mustGroups -> !mustGroups.isEmpty())
                .map(mustGroups -> mustGroups.get(0))
                .map(MustServiceProjectGroupDTO::getGroups)
                .filter(groups -> !groups.isEmpty())
                .map(groups -> groups.get(0))
                .map(ServiceProjectDTO::getAttrs)
                .filter(attrs -> !attrs.isEmpty())
                .map(attrs -> attrs.stream().collect(Collectors.toMap(ServiceProjectAttrDTO::getAttrName, p -> p)))
                .orElse(Collections.emptyMap());
    }

    // 需要展示拼团价的场景
    private boolean isPinTuanStyle(DealCtx ctx) {
        return (ctx.getBuyBar().getStyleType() == StyleTypeEnum.PINTUAN_WAITING_TWO_BUTTONS.code && ctx.getBuyBar().getBuyBtns().get(0).getBtnTitle().contains(PinTuanConstants.PINTUAN_TITLE_SUFFIX))
                || ctx.getBuyBar().getStyleType() == StyleTypeEnum.PINTUAN_ENHANCE_SINGLE_BUTTON.code
                || ctx.getBuyBar().getStyleType() == StyleTypeEnum.PINTUAN_ENHANCE_PASSIVE_JOIN_SINGLE_BUTTON.code;
    }

    private List<DealGift> buildDealGifts(DealCtx ctx) {
        if (CollectionUtils.isEmpty(ctx.getDealGifts())) {
            return null;
        }
        return ctx.getDealGifts();
    }

    public void buildActivityWindowDealConfig(DealCtx ctx, DealGroupPBO result) {
        // 非活动橱窗团购无需构造
        if (!ctx.isHitSpecialValueDeal()) {
            return;
        }
        PromoDetailModule promoDetailModule = result.getPromoDetailModule();
        if (promoDetailModule == null) {
            return;
        }
        // 先设置默认值
        promoDetailModule.setPromoStock("99");
        promoDetailModule.setMaskedPromoPrice(result.getPromoDetailModule().getPromoPrice());

        List<ConfigDTO> dealConfigs = (DealUtils.isLeadsDeal(ctx) || DealUtils.isWeddingSpecialWithPoiAndDealCategory(ctx)) ?
                ctx.getActivityWindowDealConfigs() : null;
        if (CollectionUtils.isEmpty(dealConfigs)) {
            return;
        }
        dealConfigs.forEach(config -> {
            if (Objects.equals(config.getConfigName(), "activityProductStock")) {
                promoDetailModule.setPromoStock(config.getConfigValue());
            }
            if (Objects.equals(config.getConfigName(), "activityProductSecretHandPrice")) {
                promoDetailModule.setMaskedPromoPrice(config.getConfigValue());
            }
        });
    }

    public void buildCardStylePromoDetailInfo(DealCtx ctx, DealGroupPBO result) {
        //美团放心改不需要这个信息
        if (ReassuredRepairUtil.isTagPresent(ctx)) {
            return;
        }
        PromoDetailModule promoDetailModule = result.getPromoDetailModule();
        if (ctx.isEnableCardStyle() || ctx.isEnableCardStyleV2()) {
            if (promoDetailModule == null) {
                return;
            }
            // 0元预约单不展示任何优惠信息
            if (ctx.isFreeDeal() && ctx.getFreeDealType() != null) {
                return;
            }
            // 留资型团单且（超值特惠套餐或小程序）不展示任何优惠活动
            if (DealUtils.isLeadsDeal(ctx) && (ctx.isHitSpecialValueDeal() || !(ctx.getEnvCtx().judgeMainApp()))) {
                return;
            }
            promoDetailModule.setExposureList(buildPromoExposureInfo(ctx));
            promoDetailModule.setCouponList(buildCouponList(ctx));
            promoDetailModule.setPromoActivityList(buildPromoActivityList(ctx));
            promoDetailModule.setInflateCounpon(buildInflateCoupon(ctx));

            couponFilter(promoDetailModule);

            List<String> promoAbstractList = promoDetailModuleBuilderService.buildPromoAbstractList(promoDetailModule, ctx);
            promoDetailModule.setPromoAbstractList(promoAbstractList);
        }
    }

    private void couponFilter(PromoDetailModule promoDetailModule) {
        try {
            if (CollectionUtils.isNotEmpty(promoDetailModule.getCouponList()) && null != promoDetailModule.getInflateCounpon() && CollectionUtils.isNotEmpty(promoDetailModule.getInflateCounpon().getCouponIds())) {
                List<DztgCouponInfo> couponList = promoDetailModule.getCouponList();
                List<String> couponIds = promoDetailModule.getInflateCounpon().getCouponIds();
                couponList.removeIf(dztgCouponInfo -> dztgCouponInfo.getCouponId() != null && couponIds.contains(dztgCouponInfo.getCouponId().toString()));

                promoDetailModule.setCouponList(couponList);
            }
        } catch (Exception e) {
            Cat.logError(e);
        }

    }

    public String convertPromoDesc(PromoDTO promoDTO, PromoDetailModule promoDetailModule, String promoTag) {
        try {
            if (promoDTO == null || StringUtils.isBlank(promoDTO.getExtendDesc()) || promoDTO.getIdentity() == null || StringUtils.isBlank(promoDTO.getIdentity().getPromoShowType()) || promoDetailModule == null) {
                return "团购优惠";
            }
            if (!promoDetailModule.isPromoNewStyle()) {
                // 保持旧的样式
                return promoDTO.getExtendDesc();
            }
            if (!StringUtils.equals(promoDTO.getIdentity().getPromoShowType(), "MAGICAL_MEMBER_PLATFORM_COUPON")) {
                // 非神券,走去除开头的逻辑
                if (promoDTO.getPromoTextDTO() != null) {
                    List<String> couponList = Arrays.asList("GOVERNMENT_CONSUME_COUPON", "MERCHANT_COUPON", "PLATFORM_COUPON");
                    if (couponList.contains(promoDTO.getPromoTextDTO().getPromoDivideType())) {
                        return subtractPromoDesc(promoDTO.getPromoTextDTO().getTitle(), promoTag);
                    }
                }
                return subtractPromoDesc(promoDTO.getExtendDesc(), promoTag);
            }

            BigDecimal minConsumptionAmount = promoDTO.getMinConsumptionAmount();
            BigDecimal amount = promoDTO.getAmount();
            if (minConsumptionAmount == null || amount == null) {
                return promoDTO.getExtendDesc();
            }

            if (minConsumptionAmount.compareTo(BigDecimal.ZERO) == 0) {
                // 无门槛的神券
                return "无门槛";
            }

            // 到这个地方就是有门槛的了
            return String.format("满%s元减%s元", PriceHelper.dropLastZero(minConsumptionAmount), PriceHelper.dropLastZero(amount));
        } catch (Exception e) {
            log.error("convertPromoDesc error", e);
            return promoDTO.getExtendDesc() == null ? "团购优惠" : promoDTO.getExtendDesc();
        }
    }

    public String subtractPromoDesc(String promoDesc, String promoTag) {
        if (StringUtils.isBlank(promoTag) || StringUtils.isBlank(promoDesc)) {
            return promoDesc;
        }
        if (promoDesc.startsWith(promoTag + "，")) {
            return promoDesc.replace(promoTag + "，", "");
        }
        return promoDesc;
    }

    public String convertPromoTag(PromoDTO promoDTO, int categoryId) {
        if (promoDTO == null) {
            return null;
        }
        if (promoDTO.getIdentity() == null || promoDTO.getIdentity().getPromoShowType() == null) {
            return "团购优惠";
        }
        switch (promoDTO.getIdentity().getPromoShowType()) {
            case "DISCOUNT_SELL": {
                Boolean control = Lion.getBoolean(Environment.getAppName(), "com.sankuai.dzu.tpbase.dztgdetailweb.medical.promo.tag.control", false);
                //医疗特殊逻辑，特惠促销展示为商家立减
                if (control && !ObjectUtils.isEmpty(promoDTO.getIdentity().getPromoTypeDesc())
                        && promoDTO.getIdentity().getPromoTypeDesc().contains("商家立减")) {
                    return "商家立减";
                }
                return "特惠促销";
            }
            case "MT_SUBSIDY":
                return "美团补贴";
            case "NEW_CUSTOMER_DISCOUNT":
                return "新客特惠";
            case "MEMBER_BENEFITS":
                return "会员优惠";
            case "NEW_MEMBER_BENEFITS":
                return "新会员优惠";
            case "PLATFORM_COUPON":
                return "美团券";
            case "MERCHANT_COUPON":
                return "商家券";
            case "GOVERNMENT_CONSUME_COUPON":
                return "政府消费券";
            case "DEAL_PROMO":
                return "团购优惠";
            case "PRESALE_PROMO":
                return "预售优惠";
            case "MAGICAL_MEMBER_PLATFORM_COUPON":
                return getInflicateCouponPromoTag(categoryId, promoDTO);
            case "SECOND_KILL":
                return "限时秒杀";
            default:
                return "团购优惠";
        }
    }

    /**
     * 获取promoTag的膨胀券文案
     */
    private String getInflicateCouponPromoTag(int categoryId, PromoDTO promoDTO) {
        return DEFAULT_VIP_COUPON_DESC;
        // return MAGICAL_MEMBER_PLATFORM_COUPON_ALLEVIATE;
    }

    private boolean isShoppingCartStyle(DealCtx ctx) {
        return ctx.getBuyBar().getStyleType() == SHOPPING_CART.code;
    }

    public void setWuyoutongPromo(PromoDetailModule promoDetailModule) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.setWuyoutongPromo(com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PromoDetailModule)");
        promoDetailModule.setShowMarketPrice(false);
        promoDetailModule.setMarketPricePromo(StringUtils.EMPTY);
        promoDetailModule.setShowBestPromoDetails(false);
        promoDetailModule.setBestPromoDetails(null);
        promoDetailModule.setMarketPrice(StringUtils.EMPTY);
        promoDetailModule.setMarketPromoDiscount(StringUtils.EMPTY);
    }

    public void setMarketPromoDiscount(PromoDetailModule promoDetailModule, PriceDisplayDTO priceDisplayDTO) {
        if (priceDisplayDTO == null)
            return;
        BigDecimal marketPrice = priceDisplayDTO.getMarketPrice();
        BigDecimal finalPrice = priceDisplayDTO.getPrice();
        if (marketPrice == null || finalPrice == null) {
            return;
        }
        BigDecimal discountRate = calcDiscountRate(marketPrice, finalPrice);

        String discountRateStr;
        if (discountRate == null || discountRate.compareTo(new BigDecimal("9.9")) > 0) {
            discountRateStr = "";
        } else if (discountRate.compareTo(new BigDecimal("0.1")) <= 0) {
            discountRateStr = "0.1折";
        } else {
            discountRateStr = discountRate + "折";
        }
        // marketPromoDiscount该字段在特殊行业(例如安心学),会在折扣后面追加特殊的内容,例如: 7.2折｜131.1/节
        promoDetailModule.setMarketPromoDiscount(discountRateStr);
        // 新增纯折扣字段,只用来展示折扣
        promoDetailModule.setPlainMarketPromoDiscount(discountRateStr);
    }

    /**
     * 设置预览团单的价格
     * @param promoDetailModule
     * @param dealGroupDTO
     */
    public void setPreviewMarketPromoDiscount(PromoDetailModule promoDetailModule, DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO == null || dealGroupDTO.getPrice() == null) {
            return;
        }
        PriceDTO price = dealGroupDTO.getPrice();
        String marketPriceStr = price.getMarketPrice();
        String salePriceStr = price.getSalePrice();

        if (StringUtils.isBlank(salePriceStr) || StringUtils.isBlank(marketPriceStr)) {
            return;
        }
        BigDecimal marketPrice = new BigDecimal(marketPriceStr);
        BigDecimal finalPrice = new BigDecimal(salePriceStr);
        BigDecimal discountRate = calcDiscountRate(marketPrice, finalPrice);

        String discountRateStr;
        if (discountRate == null || discountRate.compareTo(new BigDecimal("9.9")) > 0) {
            discountRateStr = "";
        } else if (discountRate.compareTo(new BigDecimal("0.1")) <= 0) {
            discountRateStr = "0.1折";
        } else {
            discountRateStr = discountRate + "折";
        }
        // marketPromoDiscount该字段在特殊行业(例如安心学),会在折扣后面追加特殊的内容,例如: 7.2折｜131.1/节
        promoDetailModule.setMarketPromoDiscount(discountRateStr);
        // 新增纯折扣字段,只用来展示折扣
        promoDetailModule.setPlainMarketPromoDiscount(discountRateStr);
    }

    private String getPerPrice(PriceDisplayDTO priceDisplayDTO) {
        if (priceDisplayDTO == null || CollectionUtils.isEmpty(priceDisplayDTO.getExtPrices())) {
            return null;
        }
        for (ExtPriceDisplayDTO extPriceDisplayDTO : priceDisplayDTO.getExtPrices()) {
            if (ExtPriceTypeEnum.Per_price.getType() == extPriceDisplayDTO.getExtPriceType() && extPriceDisplayDTO.getExtPrice() != null) {
                return PriceHelper.dropLastZero(extPriceDisplayDTO.getExtPrice());
            }
        }
        return null;
    }

    private void putPicAspectRatio(DealCtx ctx, DealGroupPBO result) {

        String expResults = StringUtils.isEmpty(ctx.getExpResults()) ? "[]" : ctx.getExpResults();
        try {
            List<String> results = JsonFacade.deserializeList(expResults.toLowerCase(), String.class);
            if (CollectionUtils.isEmpty(results)) {
                return;
            }
            Map<Integer, Map<String, Double>> picaspectratioExpMap = LionFacade.get(LionConstants.PICASPECTRATIO_EXP, new TypeReference<Map<Integer, Map<String, Double>>>() {
            });
            if (MapUtils.isEmpty(picaspectratioExpMap)) {
                return;
            }
            for (Integer categoryId : picaspectratioExpMap.keySet()) {
                if (categoryId == null) {
                    continue;
                }
                if (ctx.getCategoryId() == categoryId) {
                    Map<String, Double> picaspectratioExp = picaspectratioExpMap.get(categoryId);
                    if (MapUtils.isEmpty(picaspectratioExp)) {
                        continue;
                    }
                    Double picaspectratio = null;
                    for (String exp : results) {
                        picaspectratio = picaspectratioExp.get(exp);
                        if (picaspectratio != null) {
                            result.setPicAspectRatio(picaspectratio);
                            break;
                        }
                    }
                    if (picaspectratio == null) {
                        picaspectratio = picaspectratioExp.get(DEFAULTPICASPECTRATIO);
                        if (picaspectratio != null) {
                            result.setPicAspectRatio(picaspectratio);
                        }
                    }
                    break;
                }
            }
        } catch (Exception e) {
            logger.error("putPicAspectRatio is error,dealGroupId is {}", ctx.getResult().getDpDealId());
        }
    }

    private void buildBuyBarPricePostFix(DealCtx ctx, DealGroupPBO result) {
        // 如果是新穿戴甲或留资型团单，则不需要展示“起”后缀
        if (DealUtils.isNewWearableNailDeal(ctx) || DealUtils.isLeadsDeal(ctx)) {
            return;
        }
        if (ctx.getDealGroupDTO().getDeals().stream()
                .filter(dealGroupDealDTO -> dealGroupDealDTO.getBasic().getStatus() == 1)
                .count() > 1) {//多sku的价格后面加"起"字
            DealBuyBar buildBuyBar = result.getBuyBar();
            buildBuyBar.getBuyBtns().stream()
                    .filter(buyBtn -> StringUtils.isEmpty(buyBtn.getPricePostfix()))//如果已经设置过PricePostfix就不处理了
                    .filter(buyBtn -> !isOnlineShop(ctx.getDpPoiDTO()))// 在线类商户不需要起字
                    .filter(buyBtn -> buyBtn.getDetailBuyType() != BuyBtnTypeEnum.TRIAL_CLASS.getCode()) // 预约试听按钮不需要起字
                    .forEach(buyBtn -> buyBtn.setPricePostfix("起"));
        }
    }

    /**
     * 判断是否达到售卖时间
     *
     * @param dealGroupBase
     * @return
     */
    public Boolean notMeetSellingTime(DealGroupBaseDTO dealGroupBase) {
        return Objects.nonNull(dealGroupBase)
                && dealGroupBase.getBeginDate() != null
                && dealGroupBase.getBeginDate().compareTo(new Date()) > 0;
    }

    /**
     * 判断是否可售
     *
     * @param sellingInfo
     * @return
     */
    public Boolean allowSelling(GoodsSellingInfoDTO sellingInfo) {
        if (Objects.isNull(sellingInfo)) {
            return Boolean.TRUE;
        } else {
            return sellingInfo.isAllowSelling();
        }

    }

    /**
     * 判断是否超出购买限制
     *
     * @param ctx
     * @return
     */
    public boolean userOrderCountOverPurchaseLimit(DealCtx ctx) {
        if (!meetAppVersion(ctx)) {
            // 没有达到app版本号（美团>12.20.400，点评>11.17.0）
            return Boolean.FALSE;
        }
        DealGroupDTO dealGroup = ctx.getDealGroupDTO();
        if (Objects.nonNull(dealGroup) && Objects.nonNull(dealGroup.getRule()) && Objects.nonNull(dealGroup.getRule().getBuyRule())) {
            DealGroupBuyRuleDTO buyRuleDTO = dealGroup.getRule().getBuyRule();
            // swan的userordercount = maxPerUser
            return isPurchaseLimit(buyRuleDTO) && (getUserOrderCount(ctx) >= buyRuleDTO.getMaxPerUser());
        }
        return Boolean.FALSE;
    }

    public int getUserOrderCount(DealCtx ctx) {
        try {
            SwanParam swanParam = new SwanParam();
            Map<String, Object> map = new HashMap<>();
            map.put("platform", ctx.isMt() ? 2 : 1);
            map.put("dealgroupid", ctx.isMt() ? ctx.getMtId() : ctx.getDpId());
            map.put("userid", ctx.isMt() ? ctx.getEnvCtx().getMtUserId() : ctx.getEnvCtx().getDpUserId());//已确认判断平台后再使用
            String bizKey = SWAN_QUERY_BIZ_KEY_USER_PURCHASE_INFO;
            swanParam.setRequestParams(Lists.newArrayList(map));
            Result<QueryData> result = swanQueryService.queryByKey(SWAN_QUERY_BIZ_TYPE_ID_1096, bizKey, swanParam);
            return getUserOrderCountFromResult(result);
        } catch (Exception e) {
            logger.error("ParallDealBuilderProcessor.getUserOrderCount error", e);
            return 0;
        }
    }

    public int getUserOrderCountFromResult(Result<QueryData> result) {
        if (result == null || !result.isIfSuccess() || result.getData() == null
                || CollectionUtils.isEmpty(result.getData().getResultSet())) {
            return 0;
        }
        Map<String, Object> resultMap = result.getData().getResultSet().get(0);
        String valueStr = (String) resultMap.get("value");
        if (StringUtils.isBlank(valueStr) || Objects.isNull(JSONObject.parseObject(valueStr).get("userordercount"))) {
            return 0;
        }
        String userOrderCountStr = (String) JSONObject.parseObject(valueStr).get("userordercount");
        if (!StringUtils.isNumeric(userOrderCountStr)) {
            return 0;
        }
        int userOrderCount = Integer.parseInt(userOrderCountStr);
        return userOrderCount;
    }

    private String buildSelectTag(int dpId, boolean mt) {
        SelectDealConfig selectDealConfig = DealHelper.getSelectDealConfig();
        if (selectDealConfig == null) {
            return StringUtils.EMPTY;
        }
        if (mt && selectDealConfig.getMtDealGroupIds().contains(dpId)) {
            return PlusIcons.MT_SELECT_DEAL_ICON;
        }
        if (!mt && selectDealConfig.getDpDealGroupIds().contains(dpId)) {
            return PlusIcons.DP_SELECT_DEAL_ICON;
        }
        return StringUtils.EMPTY;
    }

    private DealTitleIcon buildDealTitleIcon(DealCtx ctx) {
        DealTitleIcon dealTitleIcon = new DealTitleIcon();
        try {
            DealActivityDTO dealActivityDTO = ctx.getDealTitleActivityDTO();
            if (dealActivityDTO != null && MapUtils.isNotEmpty(dealActivityDTO.getColorMap()) && MapUtils.isNotEmpty(dealActivityDTO.getTextMap())) {
                String color = dealActivityDTO.getColorMap().getOrDefault("labelBackgroundColor", null);
                String labelText = dealActivityDTO.getTextMap().getOrDefault("labelText", null);
                dealTitleIcon.setLabelBackgroundColor(color);
                dealTitleIcon.setLabelText(labelText);

                return dealTitleIcon;
            }
        } catch (Exception e) {
            Cat.logError(e);
        }
        return null;
    }

    private DealChoicestIcon buildChoicestIcon(DealCtx ctx) {
        if (!DealHelper.getChoicestIconSwitch()) {
            return null;
        }
        try {
            DealChoicestIcon activityChoicestIcon = buildActivityChoicestIcon(ctx);
            if (activityChoicestIcon != null) {
                return activityChoicestIcon;
            }
            String titleTagIcon = buildSelectTag(ctx.getDpId(), ctx.isMt());
            if (StringUtils.isNotBlank(titleTagIcon)) {
                DealChoicestIcon dealChoicestIcon = new DealChoicestIcon();
                dealChoicestIcon.setIcon(titleTagIcon);
                dealChoicestIcon.setIconWidth(30);
                dealChoicestIcon.setIconHeight(18);
                return dealChoicestIcon;
            }
        } catch (Exception e) {
            logger.error("buildChoicestIcon error. dealGroupId: {}", ctx.getDpId(), e);
        }
        return null;
    }

    private DealChoicestIcon buildActivityChoicestIcon(DealCtx ctx) {
        DealActivityDTO activityDTO = ctx.getChoicestDealActivityDTO();
        if (activityDTO != null && CollectionUtils.isNotEmpty(activityDTO.getUrls())) {
            DealChoicestIcon dealChoicestIcon = new DealChoicestIcon();
            dealChoicestIcon.setIcon(activityDTO.getUrls().get(0));
            int width = 100;
            int height = 25;
            //营销返回了图片但是没有返回长宽信息则设置未默认信息
            if (activityDTO.getIconHeight() == null || activityDTO.getIconHeight() == 0
                    || activityDTO.getIconWidth() == null) {
                dealChoicestIcon.setIconWidth(width);
                dealChoicestIcon.setIconHeight(height);
                return dealChoicestIcon;
            }
            //图片本身的大小符合限制大小则直接赋值
            if (activityDTO.getIconHeight() <= 100 && activityDTO.getIconWidth() <= 25) {
                width = activityDTO.getIconWidth();
                height = activityDTO.getIconHeight();
            } else {
                //宽高比超过4按照宽度比例缩一下，否则按照高度比例缩一下
                double widthTimes = (double) activityDTO.getIconWidth() / 100;
                double heightTimes = (double) activityDTO.getIconHeight() / 25;
                double aspectRatio = (double) activityDTO.getIconWidth() / (double) activityDTO.getIconHeight();
                if (aspectRatio > 4) {
                    width = (int) (activityDTO.getIconWidth() / widthTimes);
                    height = (int) (activityDTO.getIconHeight() / widthTimes);
                } else {
                    width = (int) (activityDTO.getIconWidth() / heightTimes);
                    height = (int) (activityDTO.getIconHeight() / heightTimes);
                }
            }
            dealChoicestIcon.setIconWidth(width);
            dealChoicestIcon.setIconHeight(height);
            return dealChoicestIcon;
        }
        return null;
    }

    private boolean isOnlineShop(DpPoiDTO shop) {
        return shop != null && shop.getUseType() != null && shop.getUseType() == 38
                && "1119".equals(shop.getAppSides());
    }

    private boolean checkRefundByProduct(DealCtx ctx) {
        if (!LionConfigUtils.hitCustomRefundCategoryConfig(ctx.getCategoryId())) {
            return false;
        }

        List<String> attrValueList = DealAttrHelper.getAttributeValues(ctx.getAttrs(), "reservation_policy");
        if (CollectionUtils.isEmpty(attrValueList)) {
            return false;
        }

        String refundDesc = attrValueList.iterator().next();
        return StringUtils.equals("预约成功后不可退改", refundDesc);
    }

    //针对快照删除所有可能冲突的预约文案,如购后可在线预约、需预约、免预约
    private void removeTags(List<String> result) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.removeTags(java.util.List)");
        if (null == result) {
            return;
        }
        result.remove("需预约");
        result.remove("免预约");
        result.remove("可在线预约");
        result.remove("购后可在线预约");
    }

    private void putSmallHead(DealCtx ctx) {
        if (StringUtils.isEmpty(ctx.getExpResults())) {
            return;
        }
        List<String> results = JsonFacade.deserializeList(ctx.getExpResults().toLowerCase(), String.class);
        for (String exp : results) {
            if (Lion.getStringValue("com.sankuai.dzu.tpbase.dztgdetailweb.small.head.exp.results", "exp000002_c").contains(exp)) {
                ctx.getResult().setExtraStyles(Lists.newArrayList("SMALL_HEAD"));
                return;
            }
        }
    }

    private void filterBuyButtonEnable(DealCtx ctx) {
        if (CollectionUtils.isEmpty(ctx.getAttrs())) {
            return;
        }

        for (AttributeDTO attr : ctx.getAttrs()) {
            // 如果是宠物加购代金券，团祥不能购买、不可分享，只能在下单页选择加购
            if (isBuyAndShareDisabled(attr)) {
                ctx.getResult().setShareAble(false);
                for (DealBuyBtn buyBtn : ctx.getResult().getBuyBar().getBuyBtns()) {
                    buyBtn.setBtnEnable(false);
                }
                break;
            }
        }
    }

    private boolean isBuyAndShareDisabled(AttributeDTO attr) {

        if (CollectionUtils.isEmpty(attr.getValue())) {
            return false;
        }

        Map<String, String> configMap = LionFacade.getMap(LionConstants.DISABLE_BUY_AND_SHARE_ATTR,
                String.class, String.class, Collections.emptyMap());

        if (MapUtils.isEmpty(configMap)) {
            return false;
        }

        String configValue = configMap.get(attr.getName());

        return Objects.equals(attr.getValue().get(0), configValue);
    }

    private void buildCardState(DealCtx ctx, DealGroupPBO result) {
        int shopState = 0;
        int userState = 0;

        if (ctx.getPriceContext().getDcCardMemberCard() != null) {
            shopState += 1;
            if (CardHelper.holdCard(ctx.getPriceContext().getDcCardMemberCard())) {
                userState += 1;
            }
        }

//        if (ctx.getPriceContext().getJoyCard() != null) {
//            shopState += 2;
//            if (CardHelper.holdCard(ctx.getPriceContext().getJoyCard())) {
//                userState += 2;
//            }
//        }

        result.setShopCardState(shopState == 0 ? 4 : shopState);
        result.setUserCardState(userState == 0 ? 4 : userState);
    }

    /**
     * 仅快照类目,团单需预约，且当前适用门店可进行在线预约
     *
     * @param ctx 团单信息的环境变量
     * @return 商家是否设置在线预约
     */
    public boolean photoReserveOnline(DealCtx ctx) {
        if (!LionConfigUtils.isSnapShotPhoto(ctx.getCategoryId())) {
            return false;
        }
        return ctx.isShowReserveEntrance();
    }

    private void hideMtMiniAppInfo(DealCtx ctx) {
        if (MTMiniAppHideInformationHelper.needHideInformation(ctx.getEnvCtx())) {
            hideSaleInfo(ctx);
            hidePriceInfo(ctx);
        }
    }

    private void hideSaleInfo(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.hideSaleInfo(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        ctx.getResult().setSaleDesc(null);
        ctx.getResult().setSaleDescStr(null);
    }

    private void hidePriceInfo(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.hidePriceInfo(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        for (DealBuyBtn btn : ctx.getResult().getBuyBar().getBuyBtns()) {
            btn.setPriceStr(null);
            btn.setBtnDesc(null);
            btn.setBtnIcons(null);
        }
    }

    public FeaturesLayer getFeaturesLayer(DealCtx ctx, boolean isFromBarManager) {
        if (ctx.isMtLiveMinApp() || ctx.isBeamApp()) {
            return new FeaturesLayer();
        }
        if (ctx.isFreeDeal() && ctx.getCategoryId() != 1611) {
            return new FeaturesLayer();
        }
        boolean priceProtectionValid = PriceProtectionHelper.checkPriceProtectionValid(ctx.getPriceProtectionInfo());
        boolean bestPriceGuaranteeInfoValid = PriceProtectionHelper.checkBestPriceGuaranteeInfoValid(ctx.getBestPriceGuaranteeInfo());

        FeaturesLayer featuresLayer = new FeaturesLayer();
        featuresLayer.setLayerConfigs(new ArrayList<>());
        // 安心学 保障详情落地页下发
        if (ctx.isAnXinXue()) {
            LayerConfig layerConfig = new LayerConfig();
            layerConfig.setJumpUrl(LionConfigUtils.getAnXinXueDetailPage(ctx.isMt()));
            // 安心学 type=14
            layerConfig.setType(14);
            featuresLayer.getLayerConfigs().add(layerConfig);
            return featuresLayer;
        }

        // 安心练 保障详情落地页下发
        if (ctx.isAnXinExercise()) {
            LayerConfig layerConfig = new LayerConfig();
            layerConfig.setJumpUrl(LionConfigUtils.getAnXinExerciseDetailPage(ctx.isMt(), TimesDealUtil.isMonthlySubscription(ctx)));
            // 复用安心学的type,前端硬编码 type == 14 的时候是直接跳转到落地页,否则会出现一个浮层
            layerConfig.setType(LayerConfigTypeEnum.TYPE_14.getType());
            featuresLayer.getLayerConfigs().add(layerConfig);
            return featuresLayer;
        }

        // 跑路赔 prd：团购次卡、且非连续包月
        if(DealCtxHelper.hitCompensationForRunningAway(ctx)){
            Map<String,String> config = LionConfigUtils.getCompensationForRunningAwayConfig();
            return buildRunAwayCompensation(featuresLayer, config);
        }

        if (priceProtectionValid) {
            featuresLayer.setPriceProtectionTag(ctx.getPriceProtectionInfo().getPriceProtectionTag().getValidityDays() + "天");
        }
        // 预订团单
        if (DealCtxHelper.isPreOrderDeal(ctx)) {
            if (CollectionUtils.isNotEmpty(ctx.getPreOrderFeatDetails())) {
                featuresLayer.getLayerConfigs().addAll(guaranteeBuilderService.convertFeatureLayerConfigs2LayerConfigs(ctx.getPreOrderFeatDetails()));
            }
            return featuresLayer;
        }
        // 交易保障团单，标签从商品标签获取, 并根据Lion配置填充详情，定义type为9
        if (DealUtils.isTradeAssuranceDeal(ctx)) {
            Map<Long, LayerConfig> tagId2ConfigMap = LionConfigUtils.getTradeAssuranceLayerConfig(ctx.isMt());
            List<LayerConfig> tradeAssuranceLayerConfigs = DealLayerUtils.getTradeAssuranceDealLayers(ctx, tagId2ConfigMap);
            if (CollectionUtils.isNotEmpty(tradeAssuranceLayerConfigs)) {
                featuresLayer.getLayerConfigs().addAll(tradeAssuranceLayerConfigs);
            }
            return featuresLayer;
        }
        if (LionConfigUtils.isEduOnlineDeal(ctx.getCategoryId(), getServiceTypeId(ctx.getDealGroupDTO()))) {
            // 在线教育的团购，保障需要按照团购属性的值来映射
            LayerConfig layerConfig = getOnlineEduDealFeature(ctx);
            if (layerConfig != null) {
                featuresLayer.getLayerConfigs().add(layerConfig);
            }
            return featuresLayer;
        }

        List<LayerConfig> layerConfigs;
        String lionKey = ctx.isMt() ?
                (checkRefundByProduct(ctx) ? LionConstants.LAYER_CONFIGS_MT_UNRETURN : LionConstants.LAYER_CONFIGS_MT) :
                (checkRefundByProduct(ctx) ? LionConstants.LAYER_CONFIGS_DP_UNRETURN : LionConstants.LAYER_CONFIGS_DP);

        layerConfigs = Lion.getList(LionConstants.APP_KEY, lionKey, LayerConfig.class);

        if (CollectionUtils.isNotEmpty(layerConfigs)) {
            for (LayerConfig layerConfig : layerConfigs) {
                if (layerConfig.getType() == 2) {
                    if (priceProtectionValid) {
                        layerConfig.setTitle(layerConfig.getTitle() + featuresLayer.getPriceProtectionTag());
                        featuresLayer.getLayerConfigs().add(layerConfig);
                    }
                } else if (layerConfig.getType() == 3) {
                    if (bestPriceGuaranteeInfoValid) {
                        featuresLayer.getLayerConfigs().add(layerConfig);
                    }
                } else if (layerConfig.getType() == 4 || layerConfig.getType() == 5 || layerConfig.getType() == 6) {
                    //0元预约场景
                    if (ctx.isFreeDeal()) {
                        //0元预约--疫苗
                        if (ctx.getCategoryId() == 1611) {
                            featuresLayer.getLayerConfigs().add(layerConfig);
                        }
                    }
                } else if (layerConfig.getType() == 7 || layerConfig.getType() == 8) {
                    //配镜行业增加安心配镜和正品保障
                    if (ctx.getCategoryId() == 406) {
                        if (layerConfig.getType() == 8 && isGenuineGuarantee(ctx) && !isDefaultExpResult(ctx.getCityId4P(), ctx.getEnvCtx())) {
                            String jumpUrl = layerConfig.getJumpUrl();
                            String bizParam = GlassDealUtils.getBizParam(ctx);
                            long shopId = ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId();
                            jumpUrl = jumpUrl + "&bizParam=" + bizParam + "&shopId=" + shopId;
                            layerConfig.setJumpUrl(jumpUrl);
                            featuresLayer.getLayerConfigs().add(0, layerConfig);
                        }
                        if (layerConfig.getType() == 7 && GlassDealUtils.isSafePtometry(ctx) && !isDefaultExpResult(ctx.getCityId4P(), ctx.getEnvCtx())) {
                            String jumpUrl = layerConfig.getJumpUrl();
                            long shopId = ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId();
                            jumpUrl = jumpUrl + "&shopId=" + shopId;
                            layerConfig.setJumpUrl(jumpUrl);
                            featuresLayer.getLayerConfigs().add(0, layerConfig);
                        }
                    }
                } else if (layerConfig.getType() == 12 || layerConfig.getType() == 13) {
                    long shopId4Platform = ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId();
                    int platform = ctx.isMt() ? PlatformEnum.MT.getCode() : PlatformEnum.DP.getCode();
                    //齿科行业增加安心补牙和安心种植
                    if (layerConfig.getType() == 12 && isSafeDenture(ctx) && LionConfigUtils.dentalSwitch()) {
                        String jumpUrl = layerConfig.getJumpUrl();
                        jumpUrl = OralDealUtils.buildSafeTreatOralJumpUrl(shopId4Platform, platform, GUARANTEEIP_DENTURE, jumpUrl);
                        layerConfig.setJumpUrl(jumpUrl);
                        featuresLayer.getLayerConfigs().add(0, layerConfig);
                    }
                    if (layerConfig.getType() == 13 && isSafeImplant(ctx)) {
                        String jumpUrl = layerConfig.getJumpUrl();
                        jumpUrl = OralDealUtils.buildSafeTreatOralJumpUrl(shopId4Platform, platform, GUARANTEEIP_IMPANT, jumpUrl);
                        layerConfig.setJumpUrl(jumpUrl);
                        featuresLayer.getLayerConfigs().add(0, layerConfig);
                    }
                } else if (layerConfig.getType() == GuaranteeLayerTypeEnum.SAFE_IMPLANT.getType()) {
                    LayerConfig safeImplantLayer = featureLayerBuilderService.getSafeImplantLayer(ctx, layerConfig);
                    if (Objects.nonNull(safeImplantLayer)) {
                        featuresLayer.getLayerConfigs().add(0, safeImplantLayer);
                    }
                } else if (layerConfig.getType() == 9 || layerConfig.getType() == 10 || layerConfig.getType() == 11) {
                    ReassuredRepairUtil.buildReassuredRepairLayerConfig(ctx, layerConfig, featuresLayer);
                } else {
                    //酒吧营销经理的随时退去掉
                    //0元预约的随时退去掉
                    if (layerConfig.getType() == 1 && (isFromBarManager || ctx.isFreeDeal())) {
                        continue;
                    }
                    featuresLayer.getLayerConfigs().add(layerConfig);
                }
            }

            // 在退改协议前添加游乐险
            if (Objects.nonNull(ctx.getFeatureDetailDTO()) && Objects.nonNull(ctx.getFeatureDetailDTO().getLayerConfig())) {
                if (featuresLayer.getLayerConfigs().size() == 0) {
                    featuresLayer.getLayerConfigs().add(ctx.getFeatureDetailDTO().getLayerConfig());
                } else {
                    LayerConfig layerConfig = featuresLayer.getLayerConfigs().stream().filter(l -> l.getType() == 1).findFirst().orElse(null);
                    if (Objects.isNull(layerConfig)) {
                        featuresLayer.getLayerConfigs().add(ctx.getFeatureDetailDTO().getLayerConfig());
                    } else {
                        featuresLayer.getLayerConfigs().add(featuresLayer.getLayerConfigs().indexOf(layerConfig), ctx.getFeatureDetailDTO().getLayerConfig());
                    }
                }
            }

            // 在随时退和过期退之前添加履约保障标签
            if (CollectionUtils.isNotEmpty(ctx.getShopTagFeatures())) {
                addPerformanceGuaranteeLayerConfig(featuresLayer, ctx);
            }
        }

        // 洗涤履约保障
        if (ctx.getLEInsuranceAgreementEnum() != null) {
            addLEInsuranceAgreementLayerConfig(featuresLayer, ctx);
        }

        handleJumpUrlForMiniProgram(featuresLayer, ctx);
        return featuresLayer;
    }

    /**
     * 构造跑路赔 浮层
     * @param featuresLayer
     * @param config
     * @return
     */
    public FeaturesLayer buildRunAwayCompensation(FeaturesLayer featuresLayer, Map<String,String> config){
        String layerIcon = MapUtils.getString(config, "layerIcon", "https://p0.meituan.net/ingee/162cf92b448ca3a3c311f17883c4cee81777.png");

        int type = LayerConfigTypeEnum.TYPE_1.getType();
        String title = MapUtils.getString(config, "layerTitle1", "按次核销");
        String desc = MapUtils.getString(config, "layerDesc1", "购买次卡后，资金将存放于监管账户中，每次到店核销后平台才会与商户结算对应次数的费用。");
        featuresLayer.getLayerConfigs().add(buildLayerConfig(type, layerIcon, title, desc));

        title = MapUtils.getString(config, "layerTitle2", "剩余可退");
        desc = MapUtils.getString(config, "layerDesc2", "购买次卡后，若次数部分使用，剩余所有未核销的次数可随时申请退款。");
        featuresLayer.getLayerConfigs().add(buildLayerConfig(type, layerIcon, title, desc));

        title = MapUtils.getString(config, "layerTitle3", "随时退 过期退");
        desc = MapUtils.getString(config, "layerDesc3", "未消费随时退款，过期未消费自动退款。");
        featuresLayer.getLayerConfigs().add(buildLayerConfig(type, layerIcon, title, desc));

        return featuresLayer;
    }

    private LayerConfig buildLayerConfig(int type, String icon, String title, String desc){
        LayerConfig layerConfig = new LayerConfig();
        layerConfig.setIcon(icon);
        layerConfig.setType(type);
        layerConfig.setTitle(title);
        layerConfig.setDesc(desc);
        return layerConfig;
    }

    /**
     * 添加履约保障标签
     * prd:https://km.sankuai.com/collabpage/2368966174
     * 代码开发者wb_wangchangsheng
     *
     * @param featuresLayer
     * @param ctx
     */
    private void addPerformanceGuaranteeLayerConfig(FeaturesLayer featuresLayer, DealCtx ctx) {
        List<LayerConfig> guaranteeLayerConfigs = ctx.getShopTagFeatures().stream()
                .filter(Objects::nonNull)
                .map(feature -> feature.getLayerConfig())
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        // 设置跳链
        RedirectUrls.setPerformanceGuaranteeUrls(ctx, ctx.getEnvCtx(), guaranteeLayerConfigs);
        if (featuresLayer.getLayerConfigs().size() == 0) {
            featuresLayer.getLayerConfigs().addAll(guaranteeLayerConfigs);
            return;
        }
        // 找到随时退、过期退的标签，将当前标签置于其前方
        LayerConfig layerConfig = featuresLayer.getLayerConfigs().stream().filter(l -> l.getType() == 1).findFirst().orElse(null);
        if (Objects.nonNull(layerConfig)) {
            featuresLayer.getLayerConfigs().addAll(featuresLayer.getLayerConfigs().indexOf(layerConfig), guaranteeLayerConfigs);
            return;
        }
        featuresLayer.getLayerConfigs().addAll(guaranteeLayerConfigs);
    }

    //private时无法完成单元测试
    public void addLEInsuranceAgreementLayerConfig(FeaturesLayer featuresLayer, DealCtx ctx) {
        if (ctx.getLEInsuranceAgreementEnum() == LEInsuranceAgreementEnum.CLEANING_SELF_OWN_PRODUCT &&
                ctx.getDealGroupDTO() != null &&
                ctx.getDealGroupDTO().getCategory() != null &&
                ctx.getDealGroupDTO().getCategory().getServiceTypeId() != null) {
            // 保洁自营
            String lionKey = LionConstants.LE_INSURANCE_GUARANTEE_LAYER_CONFIG;
            Map<String, GuaranteeConfig> serviceType2layerConfigDesc = Lion.getMap(LionConstants.APP_KEY, lionKey, GuaranteeConfig.class, Collections.emptyMap());
            if (serviceType2layerConfigDesc.containsKey(String.valueOf(ctx.getDealGroupDTO().getCategory().getServiceTypeId()))) {
                List<GuaranteeConfigItem> guaranteeConfigItemList = serviceType2layerConfigDesc.get(String.valueOf(ctx.getDealGroupDTO().getCategory().getServiceTypeId())).getItemList();
                if (CollectionUtils.isNotEmpty(guaranteeConfigItemList)) {
                    List<LayerConfig> layerConfigList = new ArrayList<>();
                    for (GuaranteeConfigItem item : guaranteeConfigItemList) {
                        LayerConfig layerConfig = new LayerConfig();
                        layerConfig.setTitle(item.getTitle());
                        layerConfig.setDesc(item.getDesc());
                        layerConfig.setIcon(item.getIcon());
                        layerConfig.setJumpUrl(item.getJumpUrl());
                        layerConfigList.add(layerConfig);
                    }
                    featuresLayer.getLayerConfigs().addAll(0, layerConfigList);
                }
            } else {
                featuresLayer.getLayerConfigs().addAll(0, buildSelfOwnBranchLayoutConfig());
            }
        } else {
            LayerConfig layerConfig = new LayerConfig();
            layerConfig.setIcon("https://p0.meituan.net/ingee/2860c04f209c5ebe48ad7e05a726de711937.png");
            // 常规标签具体内容
            layerConfig.setTitle(ctx.getLEInsuranceAgreementEnum().getText());
            layerConfig.setDesc(ctx.getLEInsuranceAgreementEnum().getDesc());
            layerConfig.setJumpUrl(ctx.getLEInsuranceAgreementEnum().getUrl());

            if (ctx.getDealGroupDTO() == null || ctx.getDealGroupDTO().getCategory() == null || ctx.getDealGroupDTO().getCategory().getServiceTypeId() == null) {
                featuresLayer.getLayerConfigs().add(0, layerConfig);
                return;
            }
            // 例外标签具体内容
            String lionKey = LionConstants.LE_INSURANCE_AGREEMENT_LAYER_CONFIG;
            Map<String, String> serviceType2layerConfigDesc = Lion.getMap(LionConstants.APP_KEY, lionKey, String.class, Collections.emptyMap());
            if (serviceType2layerConfigDesc.containsKey(String.valueOf(ctx.getDealGroupDTO().getCategory().getServiceTypeId()))) {
                layerConfig.setDesc(serviceType2layerConfigDesc.get(String.valueOf(ctx.getDealGroupDTO().getCategory().getServiceTypeId())));
            }
            Map<String, String> serviceType2layerConfigUrl = Lion.getMap(LionConstants.APP_KEY, LionConstants.LE_INSURANCE_AGREEMENT_LAYER_CONFIG_URL, String.class, Collections.emptyMap());
            if (serviceType2layerConfigUrl.containsKey(String.valueOf(ctx.getDealGroupDTO().getCategory().getServiceTypeId()))) {
                layerConfig.setJumpUrl(serviceType2layerConfigUrl.get(String.valueOf(ctx.getDealGroupDTO().getCategory().getServiceTypeId())));
            }
            featuresLayer.getLayerConfigs().add(0, layerConfig);
        }
    }

    //private 时无法进行单元测试
    public List<LayerConfig> buildSelfOwnBranchLayoutConfig() {
        List<LayerConfig> layerConfigList = new ArrayList<>();
        LayerConfig layerConfig = new LayerConfig();
        layerConfig.setIcon("https://p0.meituan.net/ingee/2860c04f209c5ebe48ad7e05a726de711937.png");
        layerConfig.setTitle("不满意重做");
        layerConfig.setDesc("如遇打扫不干净、服务不满意，用户可要求重做，直到满意为止。");
        layerConfigList.add(layerConfig);
        LayerConfig layerConfig2 = new LayerConfig();
        layerConfig2.setIcon("https://p0.meituan.net/ingee/2860c04f209c5ebe48ad7e05a726de711937.png");
        layerConfig2.setTitle("财产损失赔");
        layerConfig2.setDesc("平台提供家政险，保障用户资产安全，如遇物品损坏可申报理赔。");
        layerConfigList.add(layerConfig2);
        LayerConfig layerConfig3 = new LayerConfig();
        layerConfig3.setIcon("https://p0.meituan.net/ingee/2860c04f209c5ebe48ad7e05a726de711937.png");
        layerConfig3.setTitle("迟到爽约赔");
        layerConfig3.setDesc("保障用户按时享受优质服务，服务人员迟到30分钟以上可申请赔付。");
        layerConfigList.add(layerConfig3);
        return layerConfigList;
    }

    private boolean isDefaultExpResult(Integer cityId, EnvCtx envCtx) {
        // 判断是否命中a组实验
        String expResult = douHuService.getGlassDealDetailExpResult(cityId, envCtx);
        return "a".equals(expResult);
    }

    private void handleJumpUrlForMiniProgram(FeaturesLayer featuresLayer, DealCtx ctx) {
        if (featuresLayer == null || CollectionUtils.isEmpty(featuresLayer.getLayerConfigs())) {
            return;
        }

        featuresLayer.getLayerConfigs().stream().filter(l -> ctx.getEnvCtx().isWxMini() && StringUtils.isNotEmpty(l.getMiniJumpUrl())).forEach(layer -> layer.setJumpUrl(layer.getMiniJumpUrl()));
    }

    private LayerConfig getOnlineEduDealFeature(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.getOnlineEduDealFeature(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        String attrValue = getFirstAttrValue(ctx, "refund_rule");
        if (StringUtils.isBlank(attrValue)) {
            return null;
        }
        if (attrValue.startsWith(REFUND_RULE_7_DAY_START)) {
            attrValue = REFUND_RULE_7_DAY_LESS;
        }
        Map<String, LayerConfig> config = Lion.getMap(LionConstants.APP_KEY,
                LionConstants.ONLINE_EDU_DEAL_REFUND_RULE_ATTR_KEY, LayerConfig.class, Collections.emptyMap());
        if (MapUtils.isEmpty(config) || !config.containsKey(attrValue)) {
            return null;
        }
        LayerConfig layerConfig = config.get(attrValue);
        if (!REFUND_RULE_7_DAY_LESS.equals(attrValue)) {
            return layerConfig;
        }
        // 7天未学满，需要填充文案
        String refundLearnTime = getFirstAttrValue(ctx, "refund_learn_time");
        if (StringUtils.isBlank(refundLearnTime)) {
            return null;
        }
        layerConfig.setTitle(String.format(layerConfig.getTitle(), refundLearnTime));
        layerConfig.setDesc(String.format(layerConfig.getDesc(), refundLearnTime));
        return layerConfig;
    }

    private String getFirstAttrValue(DealCtx ctx, String attrName) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.getFirstAttrValue(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx,java.lang.String)");
        List<String> dealAttrValues = DealAttrHelper.getAttributeValues(ctx.getAttrs(), attrName);
        if (CollectionUtils.isEmpty(dealAttrValues)) {
            return null;
        }
        return dealAttrValues.get(0);
    }

    /**
     * 来源是否是酒吧营销经理
     *
     * @param ctx
     * @return
     */
    private boolean isFromBarManager(DealCtx ctx) {
        DealBaseReq dealBaseReq = ctx.getDealBaseReq();
        String pass_paramStr = dealBaseReq.getPass_param();
        if (StringUtils.isEmpty(pass_paramStr)) {
            return false;
        }
        try {
            JSONObject pass_paramJsonObject = JSON.parseObject(pass_paramStr);
            String craftsmanExtParamStr = pass_paramJsonObject.getString("craftsmanExtParam");
            if (StringUtils.isEmpty(craftsmanExtParamStr)) {
                return false;
            }
            JSONObject craftsmanExtParamJsonObject = JSON.parseObject(craftsmanExtParamStr);
            Integer techCategoryId = craftsmanExtParamJsonObject.getInteger("techCategoryId");
            if (techCategoryId == null) {
                return false;
            }
            return techCategoryId == TechCategoryEnum.JOY_BAR_MANAGER.getCategoryId();
        } catch (Exception e) {
            log.error("解析来源字段异常", e);
        }
        return false;
    }

    public List<String> buildTimesDealRemind(DealCtx ctx) {
        if (!TimesDealUtil.isTimesDeal(ctx.getDealGroupDTO())) {
            return null;
        }
        List<String> result = Lists.newArrayList();
        result.add(TimesDealUtil.onlyVerificationOne(ctx.getDealGroupDTO()) ? "单次仅可核销一份" : "可单次核销多份");
        // 教育次卡，命中安心学标签，不展示“仅支持整单退”
        if (ctx.isAnXinXue()) {
            return result;
        }
        if (!ctx.isHasPurchaseNoteTable()) {
            result.add("仅支持整单退");
        }
        return result;
    }

    public void putTradeType(DealCtx ctx, DealGroupPBO result) {
        result.setTradeType(ctx.isFreeDeal() ? 0 : 1);
    }

    public void putPetVoucherTitle(DealCtx ctx, DealGroupPBO result) {
        // 是否开启了展示开关
        Boolean petVoucherTitleSwitch = Lion.getBoolean(LionConstants.APP_KEY, LionConstants.PET_VOUCHER_TITLE_SHOW_SWITCH, false);
        if (!petVoucherTitleSwitch) {
            return;
        }
        List<Integer> petCategory = Lion.getList(LionConstants.APP_KEY, LionConstants.PET_CATEGORY_CONFIG, Integer.class, Arrays.asList(1701, 1702, 1704, 1705));
        if (!petCategory.contains(result.getCategoryId())) {
            return;
        }
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        if (Objects.isNull(dealGroupDTO) || Objects.isNull(dealGroupDTO.getBasic()) || StringUtils.isBlank(dealGroupDTO.getBasic().getTitle())) {
            return;
        }
        result.setTitle(dealGroupDTO.getBasic().getTitle());
    }
}
