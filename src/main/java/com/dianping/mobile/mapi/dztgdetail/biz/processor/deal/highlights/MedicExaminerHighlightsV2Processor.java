package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.DefaultExaminerHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.EntryExaminerHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.HealthCertificateExaminerHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.IExaminerAbstractHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.utils.ExaminerUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CommonAttrVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.highlights.DztgHighlightsModule;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.Future;
@Component
public class MedicExaminerHighlightsV2Processor extends AbstractHighlightsProcessor implements ApplicationListener {
    public static final Map<String, IExaminerAbstractHandler> EXAMINER_SERVER_TYPE_V2_HANDLER_MAP = new HashMap<>();

    @Autowired
    ApplicationContext applicationContext;

    @Override
    public void beforeBuild(DealCtx ctx) {
        buildExaminerHighlights(ctx);
    }

    @Override
    protected String getHighlightsIdentify(DealCtx ctx) {
        DztgHighlightsModule highlightsModule = ctx.getHighlightsModule();
        if (highlightsModule != null){
            return highlightsModule.getIdentify();
        }
        return null;
    }

    @Override
    protected String getHighlightsStyle(DealCtx ctx) {
        DztgHighlightsModule highlightsModule = ctx.getHighlightsModule();
        if (highlightsModule != null){
            return highlightsModule.getStyle();
        }
        return null;
    }

    @Override
    protected String getHighlightsContent(DealCtx ctx) {
        DztgHighlightsModule highlightsModule = ctx.getHighlightsModule();
        if (highlightsModule != null){
            return highlightsModule.getContent();
        }
        return null;
    }

    @Override
    protected List<CommonAttrVO> getHighlightsAttrs(DealCtx ctx) {
        DztgHighlightsModule highlightsModule = ctx.getHighlightsModule();
        if (highlightsModule != null){
            return highlightsModule.getAttrs();
        }
        return null;
    }

    public void buildExaminerHighlights(DealCtx ctx) {
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        if (dealGroupDTO == null) {
            return;
        }
        String serviceType = Optional.ofNullable(dealGroupDTO.getCategory())
                .map(DealGroupCategoryDTO::getServiceType).orElse("默认");
        IExaminerAbstractHandler handler = EXAMINER_SERVER_TYPE_V2_HANDLER_MAP.getOrDefault(serviceType, EXAMINER_SERVER_TYPE_V2_HANDLER_MAP.get("默认"));
        if (handler == null) {
            return;
        }
        handler.execute(ctx);
    }


    @Override
    public void onApplicationEvent(ApplicationEvent applicationEvent) {
        EXAMINER_SERVER_TYPE_V2_HANDLER_MAP.put("默认", applicationContext.getBean(DefaultExaminerHandler.class));
        EXAMINER_SERVER_TYPE_V2_HANDLER_MAP.put("入职体检", applicationContext.getBean(EntryExaminerHandler.class));
        EXAMINER_SERVER_TYPE_V2_HANDLER_MAP.put("健康证检查", applicationContext.getBean(HealthCertificateExaminerHandler.class));
    }
}
