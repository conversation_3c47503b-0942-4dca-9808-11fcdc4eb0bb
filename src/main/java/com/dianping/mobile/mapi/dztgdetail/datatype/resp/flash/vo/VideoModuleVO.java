package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/5/4 8:59 下午
 */
@MobileDo(id = 0x3812)
public class VideoModuleVO implements Serializable {
    /**
     * 视频预览图链接
     */
    @MobileDo.MobileField(key = 0xaa5f)
    private String thumbnailURL;

    /**
     * 视频描述文案
     */
    @MobileDo.MobileField(key = 0xfebf)
    private String desc;

    /**
     * 视频链接
     */
    @MobileDo.MobileField(key = 0xc56e)
    private String url;

    /**
     * 视频文字内容
     */
    @MobileDo.MobileField(key = 0xcce)
    private String content;

    /**
     * 标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    public String getThumbnailURL() {
        return thumbnailURL;
    }

    public void setThumbnailURL(String thumbnailURL) {
        this.thumbnailURL = thumbnailURL;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}