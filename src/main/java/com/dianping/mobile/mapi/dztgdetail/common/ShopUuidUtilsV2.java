package com.dianping.mobile.mapi.dztgdetail.common;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ShopIdUuidDTO;
import com.dianping.mobile.mapi.dztgdetail.util.RedisClientUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.cache2.CacheClient;
import com.sankuai.athena.inf.cache2.CacheClientConfig;
import com.sankuai.athena.inf.cache2.CacheKey;
import com.sankuai.athena.inf.cache2.loader.DataLoader;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.dto.DpPoiUuidRequest;
import com.sankuai.sinai.data.api.service.DpPoiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;
import java.util.Random;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public final class ShopUuidUtilsV2 {

    private ShopUuidUtilsV2() {
    }

    private static final String CAT_EVENT_TYPE = "ShopIdToUuid";

    private static final String REDIS_DEAL_MAPPER_SHOP_UUID_NEW = "mapper_shop_uuid_new";

    private static CacheClient cacheClient = RedisClientUtils.getRedisCacheClient();

    private static TypeReference<ShopIdUuidDTO> shopUuidMapperCacheTypeReferenceNew = new TypeReference<ShopIdUuidDTO>() {};

    private static final int CACHE_EXPIRE_TIME = 1296000;

    private static final int CACHE_REFRESH_TIME = 0;

    private static final String MAPPER_CACHE_KEY = "mapperCacheError";

    private static DpPoiService dpPoiService;

    @Autowired
    @Qualifier("sinaiDpPoiService")
    private DpPoiService sinaiDpPoiService;
    @PostConstruct
    public void init(){
        dpPoiService = sinaiDpPoiService;
    }
    public static DpPoiService getDpPoiService(){
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.common.ShopUuidUtilsV2.getDpPoiService()");
        return dpPoiService;
    }

    public static ShopIdUuidDTO getUuidDtoByIdForLong(Long shopId) {
        if (shopId == null || shopId <= 0) {
            return null;
        }
        try {
            DpPoiRequest dpPoiRequest = new DpPoiRequest();
            dpPoiRequest.setShopIds(Lists.newArrayList(shopId));
            dpPoiRequest.setFields(Lists.newArrayList(new String[]{"shopId", "uuid"}));
            List<DpPoiDTO> dpPoiDTOList = dpPoiService.findShopsByShopIds(dpPoiRequest);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(dpPoiDTOList) && Objects.nonNull(dpPoiDTOList.get(0))) {
                ShopIdUuidDTO shopUuidDTO = new ShopIdUuidDTO();
                DpPoiDTO dpPoiDTO = dpPoiDTOList.get(0);
                shopUuidDTO.setShopId(dpPoiDTO.getShopId());
                shopUuidDTO.setShopUuid(dpPoiDTO.getUuid());
                return shopUuidDTO;
            }
        } catch (Exception e) {
            Cat.logEvent(CAT_EVENT_TYPE, "RemoteException", "1", e.getMessage());
            log.error("get shop uuid failed for [{}]", shopId, e);
        }
        return null;
    }

    public static ShopIdUuidDTO getUuidDtoById(Long shopId) {
        if (shopId == null || shopId <= 0L) {
            return null;
        }
        try {
            //新逻辑
            ShopIdUuidDTO shopUuidDTO = fetchUuidDtoById(shopId);
            return shopUuidDTO;
        } catch (Exception e) {
            Cat.logEvent(CAT_EVENT_TYPE, "RemoteException", "1", e.getMessage());
            log.error("get shop uuid failed for [{}]", shopId, e);
            return null;
        }
    }

    public static Long getShopIdByUuid(String uuid) {
        if(uuid == null) {
            return 0L;
        }
        try {
            DpPoiUuidRequest dpPoiUuidRequest = new DpPoiUuidRequest();
            dpPoiUuidRequest.setUuids(Lists.newArrayList(uuid));
            dpPoiUuidRequest.setFields(Lists.newArrayList(new String[]{"shopId", "uuid"}));
            List<DpPoiDTO> shopsByUuids = dpPoiService.findShopsByUuids(dpPoiUuidRequest);
            if(CollectionUtils.isEmpty(shopsByUuids)){
                return 0L;
            }
            DpPoiDTO shopUuidDTO = shopsByUuids.get(0);
            return shopUuidDTO == null ? 0L : shopUuidDTO.getShopId();
        } catch (Exception e) {
            Cat.logEvent(CAT_EVENT_TYPE, "RemoteException", "1", e.getMessage());
            log.error("get shopid failed for [{}]", uuid, e);
            return 0L;
        }
    }


    public static String getUuidById(Long shopId) {
        //压测过程中发现问题，先做一个走实时的降级开关，复压的时候再看下
        if (Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.ShopUuidUtilsV2.real.enable", false)) {
            Cat.logMetricForCount("ShopUuidUtilsV2.real.enable");
            ShopIdUuidDTO shopIdUuidDTO =  getUuidDtoByIdForLong(shopId);
            if (shopIdUuidDTO != null) {
                String uuid = shopIdUuidDTO.getShopUuid();
                if (StringUtils.isBlank(uuid)) {
                    Cat.logEvent(CAT_EVENT_TYPE, "RemoteReturnBlank", "1", "");
                }
                return uuid == null ? "" : uuid;
            }
            return "";
        }

        ShopIdUuidDTO uuidDto = getUuidDtoById(shopId);
        if (uuidDto != null) {
            String uuid = uuidDto.getShopUuid();
            if (StringUtils.isBlank(uuid)) {
                Cat.logEvent(CAT_EVENT_TYPE, "RemoteReturnBlank", "1", "");
            }
            return uuid == null ? "" : uuid;
        }
        return "";
    }

    private static ShopIdUuidDTO fetchUuidDtoById(long dpShopId) {
        ShopIdUuidDTO shopUuidDTO;
        try {
            shopUuidDTO = getUuidFromCache(dpShopId).get(1000, TimeUnit.MILLISECONDS);
            if(Objects.isNull(shopUuidDTO) || Objects.isNull(shopUuidDTO.getShopId())){
                //异常空数据清除
                Cat.logEvent("fetchUuidDtoById.getUuidFromCache","NPE:"+dpShopId);
                CacheKey cacheKey = new CacheKey(REDIS_DEAL_MAPPER_SHOP_UUID_NEW, dpShopId);
                cacheClient.asyncDelete(cacheKey);
            }
        } catch (Exception e) {
            Cat.logError(e);
            Cat.logEvent(MAPPER_CACHE_KEY, "shopUuidError");
            return getUuidDtoByIdForLong(dpShopId);
        }
        return shopUuidDTO;
    }

    private static CompletableFuture<ShopIdUuidDTO> getUuidFromCache(long dpShopId) {
        CacheKey cacheKey = new CacheKey(REDIS_DEAL_MAPPER_SHOP_UUID_NEW, dpShopId);
        DataLoader<ShopIdUuidDTO> dataLoader = key -> {
            if(key == null) {
                return CompletableFuture.completedFuture(null);
            }
            return CompletableFuture.completedFuture(getUuidDtoByIdForLong(dpShopId));
        };
        return cacheClient.asyncGetReadThrough(cacheKey,shopUuidMapperCacheTypeReferenceNew, dataLoader, CACHE_EXPIRE_TIME + new Random().nextInt(300) , CACHE_REFRESH_TIME);
    }

}
