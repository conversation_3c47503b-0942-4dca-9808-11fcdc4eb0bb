package com.dianping.mobile.mapi.dztgdetail.tab;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.dianping.cat.Cat;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.dianping.deal.sales.common.datatype.ProductParam;
import com.dianping.deal.sales.common.datatype.SalesDisplayDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealStockSaleWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PriceDisplayWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ProductTagWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.BaseData;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.BaseLoadParam;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.DealTab;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.DealTabHolder;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.SourceDataHolder;
import com.dianping.mobile.mapi.dztgdetail.tab.postprocess.RelatePostProcessor;
import com.dianping.mobile.mapi.dztgdetail.tab.sourcedata.Loader;
import com.google.common.collect.Lists;

public class AbstractRelateDeals implements RelateDeals, Loader {

    @Autowired
    DealGroupWrapper dealGroupWrapper;

    @Autowired
    DealStockSaleWrapper dealStockSaleWrapper;

    @Resource
    Loader simpleSourceDataTabDependsOnLoader;

    @Autowired
    RelateDealsFactory relateDealsFactory;

    @Resource
    ProductTagWrapper productTagWrapper;

    @Resource
    PriceDisplayWrapper priceDisplayWrapper;

    @Override
    public DealTabHolder listRelatedDealTabs(BaseData baseData, EnvCtx envCtx) {
        SourceDataHolder sourceDataHolder = getSourceDataHolder();

        BaseLoadParam param = new BaseLoadParam();
        param.setBaseData(baseData);
        param.setEnvCtx(envCtx);

        //1.关联前加载数据资源
        loadBeforeRelate(param, sourceDataHolder);

        //2.获取当前团单tab及所关联的其它团单tab
        List<Integer> relatedDpDealGroupIds = getRelatedDpDealGroupIds(baseData, sourceDataHolder);
        baseData.setRelatedDpDealGroupIds(relatedDpDealGroupIds);

        if (CollectionUtils.isEmpty(relatedDpDealGroupIds)) {
            return null;
        }

        //3.关联后加载数据资源
        loadAfterRelate(param, sourceDataHolder);

        DealTabHolder dealTabHolder = doListRelatedDealTabs(baseData, sourceDataHolder);

        if (dealTabHolder == null) {
            return null;
        }

        //4.当前团单关联团单（不包含当前团单）排序
        List<DealTab> relatedTabs = dealTabHolder.getRelatedTabs();
        sortTabs(relatedTabs, sourceDataHolder);

        //5.当前团单排第一个
        relatedTabs.add(0, dealTabHolder.getCurrentTab());

        //6.后置处理
        postProcess(param.getBaseData().getPublishCategoryId(), dealTabHolder);

        return dealTabHolder;
    }

    private DealTabHolder postProcess(int publishCategoryId, DealTabHolder tabHolder) {
        if (tabHolder == null || CollectionUtils.isEmpty(tabHolder.getRelatedTabs())) {
            return tabHolder;
        }

        List<RelatePostProcessor> postProcessors = relateDealsFactory.getRelatePostProcessors();

        for (RelatePostProcessor postProcessor : postProcessors) {
            postProcessor.postProcessAfterRelate(publishCategoryId, tabHolder);
            if (CollectionUtils.isEmpty(tabHolder.getRelatedTabs())) {
                break;
            }
        }

        return tabHolder;
    }

    protected List<Integer> getRelatedDpDealGroupIds(BaseData baseData, SourceDataHolder sourceDataHolder) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.tab.AbstractRelateDeals.getRelatedDpDealGroupIds(com.dianping.mobile.mapi.dztgdetail.tab.bo.BaseData,com.dianping.mobile.mapi.dztgdetail.tab.bo.SourceDataHolder)");
        return Lists.newArrayList();
    }

    protected DealTabHolder doListRelatedDealTabs(BaseData baseData, SourceDataHolder holder) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.tab.AbstractRelateDeals.doListRelatedDealTabs(com.dianping.mobile.mapi.dztgdetail.tab.bo.BaseData,com.dianping.mobile.mapi.dztgdetail.tab.bo.SourceDataHolder)");
        return null;
    }

    protected SourceDataHolder getSourceDataHolder() {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.tab.AbstractRelateDeals.getSourceDataHolder()");
        return new SourceDataHolder();
    }

    /**
     * 默认基于销量排序
     * @param tabs
     * @param holder
     */
    private void sortTabs(List<DealTab> tabs, SourceDataHolder holder) {

        if (CollectionUtils.isEmpty(tabs) || MapUtils.isEmpty(holder.getDealSaleMap())) {
            return;
        }

        Map<ProductParam, SalesDisplayDTO> dealSaleMap = holder.getDealSaleMap();
        Map<Integer, SalesDisplayDTO> map = new HashMap<>();

        for (Map.Entry<ProductParam, SalesDisplayDTO> entry : dealSaleMap.entrySet()) {
            map.put((int) entry.getKey().getProductGroupId(), entry.getValue());
        }

        tabs.sort(Comparator.comparingInt(a -> -(map.get(a.getDealGroupId()) == null ? 0 : map.get(a.getDealGroupId()).getSales())));
    }

    @Override
    public void loadBeforeRelate(BaseLoadParam param, SourceDataHolder holder) {
        simpleSourceDataTabDependsOnLoader.loadBeforeRelate(param, holder);
    }

    @Override
    public void loadAfterRelate(BaseLoadParam param, SourceDataHolder holder) {
        simpleSourceDataTabDependsOnLoader.loadAfterRelate(param, holder);
    }
}
