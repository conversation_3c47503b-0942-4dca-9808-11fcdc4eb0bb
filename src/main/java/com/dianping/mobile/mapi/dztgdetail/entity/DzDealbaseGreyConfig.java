package com.dianping.mobile.mapi.dztgdetail.entity;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/31
 */
@Data
public class DzDealbaseGreyConfig {
    /**
     * true 则开启全量
     */
    private boolean allPass;

    /**
     * 团单id白名单，名单内的直接通过灰度,格式 ： ["mt123456","dp567890"]
     */
    private List<String> dealGroupIdWhiteList;

    /**
     * key：团单后台类目id，value：灰度比例，满分100
     */
    private Map<Integer, Integer> categoryId2GreyRatioMap;
}