package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@TypeDoc(description = "大促活动展示信息模型")
@MobileDo(name = "ActivityDisplayVo")
@Data
public class ActivityDisplayVo implements Serializable {

    @FieldDoc(description = "是否显示")
    @MobileField(key = 0xdac8)
    private boolean show;

    @FieldDoc(description = "左边切图")
    @MobileField(key = 0x6054)
    private String imgLeft;

    @FieldDoc(description = "右边切图")
    @MobileField(key = 0x49c3)
    private String imgRight;

    @FieldDoc(description = "活动截止时间")
    @MobileField(key = 0xca7b)
    private Date endTime;

    @FieldDoc(description = "背景色")
    @MobileField(key = 0xf3b3)
    private String bgColor;

    @FieldDoc(description = "大促模块宽高比")
    @MobileField(key = 0xa518)
    private double viewRatio = 7.5;

    @FieldDoc(description = "是否展示倒计时, true:有倒(点开查看详情)")
    @MobileField(key = 0x3185)
    private boolean showCountDown = true;

//    @FieldDoc(description = "活动宣传 json label格式")
//    @MobileField
//    private String activityReport;

    @FieldDoc(description = "活动描述")
    @MobileField(key = 0x274e)
    private String activityDesc;

    @FieldDoc(description = "文案颜色")
    @MobileField(key = 0x9196)
    private String msgColor;

    @FieldDoc(description = "倒计时数字颜色")
    @MobileField(key = 0xa434)
    private String timeColor;

    @FieldDoc(description = "活动详情")
    @MobileField(key = 0x4c61)
    private ActivityInfoVo activityInfoVo;

    @FieldDoc(description = "大促跳转链接")
    @MobileField(key = 0x57f8)
    private String jumperLink;

}
