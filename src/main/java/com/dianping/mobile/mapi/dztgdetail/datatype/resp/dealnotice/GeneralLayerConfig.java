package com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealnotice;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
*<AUTHOR>
*@create 2024/9/30 15:04
 * https://mobile.sankuai.com/studio/model/info/39392
*/
@Data
@MobileDo(id = 0xc6267e0b)
public class GeneralLayerConfig implements Serializable {
    /**
     * 类型
     */
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;

    /**
     * 描述
     */
    @MobileDo.MobileField(key = 0xfebf)
    private String desc;

    /**
     * 跳链
     */
    @MobileDo.MobileField(key = 0x774e)
    private String jumpUrl;

    /**
     * 标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * icon
     */
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;
}
