package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.healthExamination;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/18 11:00 上午
 */
@MobileDo(id = 0x90bc)
public class FilterItemListVO implements Serializable {

    /**
     * 筛选下挂项目组列表
     */
    @MobileDo.MobileField(key = 0x1d2c)
    private List<HealthExaminationItemGroupModelVO> itemGroupList;

    /**
     * 是否默认选中
     */
    @MobileDo.MobileField(key = 0x8e58)
    private boolean isSelected;

    /**
     * 子筛选列表
     */
    @MobileDo.MobileField(key = 0x284b)
    private List<FilterItemListVO> childrenFilters;

    /**
     * 查看更多
     */
    @MobileDo.MobileField(key = 0x3b66)
    private ReadMoreVO more;

    /**
     * 筛选名
     */
    @MobileDo.MobileField(key = 0x78a2)
    private String tabName;

    /**
     * 筛选id
     */
    @MobileDo.MobileField(key = 0x3100)
    private int tabId;

    public List<HealthExaminationItemGroupModelVO> getItemGroupList() {
        return itemGroupList;
    }

    public void setItemGroupList(
            List<HealthExaminationItemGroupModelVO> itemGroupList) {
        this.itemGroupList = itemGroupList;
    }

    public boolean getIsSelected() {
        return isSelected;
    }

    public void setIsSelected(boolean isSelected) {
        this.isSelected = isSelected;
    }

    public List<FilterItemListVO> getChildrenFilters() {
        return childrenFilters;
    }

    public void setChildrenFilters(List<FilterItemListVO> childrenFilters) {
        this.childrenFilters = childrenFilters;
    }

    public ReadMoreVO getMore() {
        return more;
    }

    public void setMore(ReadMoreVO more) {
        this.more = more;
    }

    public String getTabName() {
        return tabName;
    }

    public void setTabName(String tabName) {
        this.tabName = tabName;
    }

    public int getTabId() {
        return tabId;
    }

    public void setTabId(int tabId) {
        this.tabId = tabId;
    }
}