package com.dianping.mobile.mapi.dztgdetail.action.h5;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.CatEvents;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.Response;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.Response.RespCode;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.entity.H5LoginVerificationConfig;
import com.dianping.mobile.mapi.dztgdetail.facade.DealQueryFacade;
import com.dianping.mobile.mapi.dztgdetail.util.AntiCrawlerUtils;
import com.dianping.mobile.mapi.dztgdetail.util.CatUtils;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.dianping.mobile.mapi.dztgdetail.util.dinner.DinnerDealUtils;
import com.google.common.collect.Maps;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.*;

@InterfaceDoc(displayName = "到综团单展示信息H5查询接口",
        type = "restful",
        description = "查询到综团单展示信息：包括团单基础信息、购买栏、优惠、适用商户等等。",
        scenarios = "该接口仅适用于双平台APP站点的团购详情页，其他以到综团购为纬度的页面如需使用请咨询项目owner",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "qian.wang.sh"
)
@Controller("general/platform/dztgdetail/dzdealbase.json")
@Action(url = "dzdealbase.json", protocol = ReqProtocol.REST)
public class DzDealBaseH5Action extends AbsAction<DealBaseReq> {

    @Autowired
    private DealQueryFacade dealQueryFacade;

    @Override
    protected IMobileResponse validate(DealBaseReq request, IMobileContext context) {
        IdUpgradeUtils.processProductIdForDealBaseReq(request, "dzdealbase.json");
        if (request == null || Objects.isNull(request.getDealgroupid()) || request.getDealgroupid() <= 0
                || request.getClienttype() <= 0) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "dzdealbase.json",
            displayName = "到综团单展示信息查询接口",
            description = "查询到综团单展示信息：包括团单基础信息、购买栏、优惠、适用商户等等。\n如果团单是侵权团单，则后端不返回数据。",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "dzdealbase.json请求参数",
                            type = DealBaseReq.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "result", description = "团购picasso数据", type = DealGroupPBO.class)},
            restExampleUrl = "http://mapi.51ping.com/general/platform/dztgdetail/dzdealbase.json?" +
                    "dealgroupid=200139713&userlng=121.3756670738241&userlat=31.21784036756436&cityid=1",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    ),
            }
    )
    @Override
    @CryptMethod
    protected IMobileResponse execute(DealBaseReq request, IMobileContext context) {
        EnvCtx envCtx = initEnvCtxFromH5(context, true);
        try {
            Response<DealGroupPBO> response = dealQueryFacade.queryDealGroup(request, envCtx);
            if (response.getCode() == RespCode.RHINO_REJECT.getVal()) {
                return Resps.SYSTEM_BUSY;
            }
            DealGroupPBO result = response.getResult();
            //添加H5反爬登录的动态开关
            CommonMobileResponse h5Verification = getH5LoginVerificationConfig(envCtx, request);
            if (result != null) {
                // 判断是否需要登录校验非空代表被登录拦截
                if (h5Verification != null) {
                    return h5Verification;
                }
                if (result.getModuleConfigsModule() != null && result.getModuleConfigsModule().isTort()) {
                    Cat.logMetricForCount(CatEvents.DEALBASE_FAIL_H5);
                    return new CommonMobileResponse(result);
                }
                Cat.logMetricForCount(CatEvents.DEALBASE_SUC_H5);
                // 隐藏关键信息 防止反爬
                if (AntiCrawlerUtils.onlyDisplayWhitelistFields()) {
                    result = AntiCrawlerUtils.onlyShowSafeFields(result, context);
                } else {
                    AntiCrawlerUtils.hideKeyInfo(result, context);
                }
                Integer categoryId = Optional.ofNullable(result).map(DealGroupPBO::getCategoryId).orElse(0);
                Integer mtDealGroupId = Optional.ofNullable(result).map(DealGroupPBO::getMtId).orElse(0);
                CatUtils.reportDealClientType("dzdealbase.json", envCtx, categoryId, mtDealGroupId, request.getPageSource());
                // 拦截到餐商品，避免信息外漏
                if (DinnerDealUtils.isDinnerDeal(categoryId, envCtx)) {
                    Cat.logMetricForCount(CatEvents.DEALBASE_PRODUCT_INTERCEPT_H5);
                    return Resps.PRODUCT_ERROR;
                }
                return new CommonMobileResponse(result);
            }
        } catch (Exception e) {
            logger.error("dzdealbase.json error", e);
        }
        Cat.logMetricForCount(CatEvents.DEALBASE_FAIL_H5);
        return Resps.SYSTEM_ERROR;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }


    /**
     * 读取Lion中的配置文件判断是否需要登录，当需要登录时返回登录校验结果
     * 1.loin返回信息为false时代表不需要登录校验
     * 2.lion返回信息为true时代表需要登录校验，返回null代表登录校验成功无异常走原先逻辑，非null代表未登录将needLogin置为true返回给前端
     *
     * @return
     */
    public CommonMobileResponse getH5LoginVerificationConfig(EnvCtx envCtx, DealBaseReq request) {
        H5LoginVerificationConfig config = null;
        try {
            config = Lion.getBean(LionConstants.APP_KEY, LionConstants.H5_LOGIN_VERIFICATION, H5LoginVerificationConfig.class);
            if (config == null || !config.isEnable()) {
                return null;
            } else {
                CommonMobileResponse commonMobileResponse = getLoginVerificationResponse(envCtx, request);
                if (commonMobileResponse != null) {
                    return commonMobileResponse;
                }
            }
        } catch (Exception e) {
            logger.error("getH5LoginVerificationConfig  error!", e);
        }
        return null;
    }

    /**
     * 该内容参考了DzDealBaseAction->DzDealBaseExecutor中的内容
     *
     * @param envCtx
     * @param request
     * @return
     */
    public CommonMobileResponse getLoginVerificationResponse(EnvCtx envCtx, DealBaseReq request) {
        // 登录校验获取需要判断的来源
        Map<String, Boolean> loginSwitch = Lion.getMap(LionConstants.APP_KEY, LionConstants.DETAIL_LOGIN_SWITCH, Boolean.class, Collections.emptyMap());
        Map<String, String> logTags = Maps.newHashMap();
        logTags.put("platform", envCtx.getDztgClientTypeEnum().getDesc());
        logTags.put("pageSource", request.getPageSource());
        //判断该来源是否配置了需要登录
        boolean notLogin = AntiCrawlerUtils.isNotLogin(loginSwitch, envCtx);
        if (notLogin) {
            Cat.logMetricForCount(CatEvents.DEALBASE_DETAIL_UNLOGIN);
            DealGroupPBO result = new DealGroupPBO();
            result.setNeedLogin(true);
            return new CommonMobileResponse(result);
        }
        return null;
    }
}
