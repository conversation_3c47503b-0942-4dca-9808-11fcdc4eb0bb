package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.BeautyTattoConfigWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.TattooPrecautionsInfo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.TattooPrecautionsVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.TattooQAModel;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.TattooQAsVO;
import com.google.common.collect.Lists;
import com.sankuai.beautycontent.function.tattoo.dto.AftercareFAQsDTO;
import com.sankuai.beautycontent.function.tattoo.dto.AftercareFAQsResponse;
import com.sankuai.beautycontent.function.tattoo.dto.FaqDTO;
import com.sankuai.beautycontent.function.tattoo.dto.StepDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Future;

/**
 * 丽人纹绣配置数据获取 & 纹后注意事项、常见问题的构建
 */
public class BeautyTattoConfigDataProcessor extends AbsDealProcessor {

    private static final Long VALID_DEAL_TAG_ID = 100211620L;
    @Resource
    private DouHuBiz douHuBiz;
    @Resource
    private BeautyTattoConfigWrapper beautyTattoConfigWrapper;

    Set<Long> validDealGroupIds = new HashSet<>(Arrays.asList(VALID_DEAL_TAG_ID, 100206606L, 100235387L, 100205607L));

    @Override
    public void prepare(DealCtx ctx) {
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        if (Objects.isNull(dealGroupDTO) || Objects.isNull(dealGroupDTO.getCategory())) {
            return;
        }
        Long categoryId = dealGroupDTO.getCategory().getCategoryId();
        if (Objects.nonNull(categoryId)
                && categoryId == 512) {
            List<DealGroupTagDTO> tagDTOs = CollectionUtils.isEmpty(dealGroupDTO.getTags()) ? Lists.newArrayList()
                    : dealGroupDTO.getTags();
            for (DealGroupTagDTO tagDTO : tagDTOs) {
                Long id = tagDTO.getId();
                if (validDealGroupIds.contains(id)) {
                    Future future = beautyTattoConfigWrapper.getTattooConfig(VALID_DEAL_TAG_ID);
                    ctx.getFutureCtx().setBeautyTattooFuture(future);
                    return;
                }
            }
            if (Objects.nonNull(dealGroupDTO.getCategory().getServiceTypeId())
                    && dealGroupDTO.getCategory().getServiceTypeId() == 137013) {
                Future future = beautyTattoConfigWrapper.getTattooConfig(VALID_DEAL_TAG_ID);
                ctx.getFutureCtx().setBeautyTattooFuture(future);
            }
        }
    }

    @Override
    public void process(DealCtx ctx) {
        // 补充douhu实验
        boolean mt = ctx.getEnvCtx().isMt();
        String unionId = ctx.getEnvCtx().getUnionId();
        ModuleAbConfig tattooGuideAbTestSwitch = getTattooGuideAbTestSwitch(unionId, mt);
        AftercareFAQsResponse futureResult = beautyTattoConfigWrapper.getFutureResult(ctx.getFutureCtx().getBeautyTattooFuture());
        AftercareFAQsDTO data = Objects.isNull(futureResult)? null : futureResult.getData();
        if ( !meetAbTest(tattooGuideAbTestSwitch)
                ||Objects.isNull(futureResult)
                || !futureResult.getSuccess()
                || Objects.isNull(data)) {
            return;
        }
        ctx.setTattooPrecautionsVO(buildTattooPrecautionsVO(data));
        ctx.setTattooQAsVO( buildTattooQAsVO(data));
    }

    // 返回为true，就是对照组，需要展示；返回为false，为实验组，不进行展示
    private Boolean meetAbTest(ModuleAbConfig tattooGuideAbTestSwitch){
        if(tattooGuideAbTestSwitch == null
                || CollectionUtils.isEmpty(tattooGuideAbTestSwitch.getConfigs())
                || tattooGuideAbTestSwitch.getConfigs().get(0) == null){
            // 如果产品没有配置ab实验，这里逻辑选择不展示
            return false;
        }
        String expResult = tattooGuideAbTestSwitch.getConfigs().get(0).getExpResult();
        // A组是对照，B组是实验；对照组不展示指南模块，实验组展示指南模块
        return "b".equals(expResult);
    }

    private ModuleAbConfig getTattooGuideAbTestSwitch(String unionId, boolean isMt){
        Map<String, String> lionMap = Lion.getMap(LionConstants.APP_KEY,
                LionConstants.DOUHU_MODULE_EXP_CONFIG, String.class, Collections.emptyMap());
        String moduleName = isMt ? "MtBeautyTattooGuide" : "DpBeautyTattooGuide";
        String expId = lionMap.get(moduleName);
        if (StringUtils.isBlank(expId)) {
            return null;
        }
        return douHuBiz.getAbByUnionIdAndExpId(unionId, expId, moduleName, isMt);
    }

    public TattooPrecautionsVO buildTattooPrecautionsVO(AftercareFAQsDTO data) {
        if (Objects.isNull(data) || CollectionUtils.isEmpty(data.getStepsDTO())){
            return null;
        }
        List<StepDTO> steps = data.getStepsDTO();
        List<TattooPrecautionsInfo> tattooPrecautionsInfos = new ArrayList<>();
        for (StepDTO entry : steps) {
            TattooPrecautionsInfo tattooPrecautionsInfo = new TattooPrecautionsInfo();
            tattooPrecautionsInfo.setDay(entry.getTitle());
            tattooPrecautionsInfo.setDetailInfos(entry.getContent());
            tattooPrecautionsInfos.add(tattooPrecautionsInfo);
        }
        TattooPrecautionsVO tattooPrecautionsVO = new TattooPrecautionsVO();
        tattooPrecautionsVO.setTitle("纹后注意事项");
        tattooPrecautionsVO.setInfos(tattooPrecautionsInfos);
        tattooPrecautionsVO.setTab("美团纹绣小贴士");
        return tattooPrecautionsVO;
    }

    public TattooQAsVO buildTattooQAsVO(AftercareFAQsDTO data) {
        if (Objects.isNull(data) || CollectionUtils.isEmpty(data.getFaqs())){
            return null;
        }
        List<FaqDTO> faqs = data.getFaqs();
        TattooQAsVO tattooQAsVO = new TattooQAsVO();
        tattooQAsVO.setTitle("常见问题");
        if (faqs.size() > 3) {
            tattooQAsVO.setExtraInfo(String.format("全部 %d 条回答", faqs.size()));
            tattooQAsVO.setExtraInfoIcon("https://p0.meituan.net/ingee/d2d4cba6b3f63c3f846aed711f39b43c379.png");
        }
        List<TattooQAModel> tattooQAsInfos = new ArrayList<>();
        for (FaqDTO faq : faqs) {
            TattooQAModel tattooQAsInfo = new TattooQAModel();
            tattooQAsInfo.setQuestion(faq.getQuestion());
            tattooQAsInfo.setAnswer(faq.getAnswer());
            tattooQAsInfo.setIcon("https://p0.meituan.net/ingee/3ad727ebf9075e028de7a298bb2e648e992.png");
            tattooQAsInfos.add(tattooQAsInfo);
        }
        tattooQAsVO.setQas(tattooQAsInfos);
        return tattooQAsVO;
    }

}
