package com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/12/13.
 */
@TypeDoc(description = "详细优惠信息")
@MobileDo(id = 0x6c10)
public class DetailPromoInfo implements Serializable {

    @FieldDoc(description = "名称，例如:优惠")
    @MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "一种优惠信息类型对应一个PromoInfoItem")
    @MobileField(key = 0x926)
    private List<PromoInfoItem> promoInfoItems;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<PromoInfoItem> getPromoInfoItems() {
        return promoInfoItems;
    }

    public void setPromoInfoItems(List<PromoInfoItem> promoInfoItems) {
        this.promoInfoItems = promoInfoItems;
    }
}
