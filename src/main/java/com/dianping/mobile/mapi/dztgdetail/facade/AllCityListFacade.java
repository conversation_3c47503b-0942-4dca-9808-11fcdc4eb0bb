package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.gis.remote.dto.CityInfoDTO;
import com.dianping.gis.remote.service.CityInfoService;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.city.AllCityVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.city.CityCluster;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.city.CityDistrict;
import com.google.common.collect.Lists;
import com.meituan.service.mobile.group.geo.bean.CityInfo;
import com.meituan.service.mobile.group.geo.service.CityService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AllCityListFacade {

    @Resource
    private CityInfoService dpCityInfoService;
    @Resource
    private CityService mtCityService;

    private static final int BATCH_SIZE = 1000;

    public AllCityVO getAllCityList(boolean isMt) {
        return isMt ? getMtAllCityVO() : getDpAllCityVO();
    }

    // 美团侧sdk有本地缓存，可一次性获取全量前台开站城市
    private AllCityVO getMtAllCityVO() {
        List<CityInfo> cityInfoList = mtCityService.listOpenCities();
        if (CollectionUtils.isEmpty(cityInfoList)) {
            return new AllCityVO();
        }
        List<CityDistrict> cityDistrictList = cityInfoList.stream().map(this::mtCityToCityDistrict).collect(Collectors.toList());
        return cityDistrictListToAllCityVO(cityDistrictList);
    }

    private AllCityVO getDpAllCityVO() {
        List<CityInfoDTO> cityInfoDTOList = Lists.newArrayList();
        int start = 0;
        do {
            List<CityInfoDTO> cityInfoList = getDpCityList(start);
            if (CollectionUtils.isEmpty(cityInfoList)) {
                break;
            }
            cityInfoDTOList.addAll(cityInfoList);
            start = start + BATCH_SIZE;
        } while (true);
        if (CollectionUtils.isEmpty(cityInfoDTOList)) {
            return new AllCityVO();
        }
        List<CityDistrict> cityDistrictList = cityInfoDTOList.stream().map(this::dpCityToCityDistrict).collect(Collectors.toList());
        return cityDistrictListToAllCityVO(cityDistrictList);
    }

    private CityDistrict dpCityToCityDistrict(CityInfoDTO cityInfoDTO) {
        CityDistrict cityDistrict = new CityDistrict();
        cityDistrict.setCityId(cityInfoDTO.getCityId());
        cityDistrict.setCityName(cityInfoDTO.getCityName());
        cityDistrict.setFirstLetter(getFirstLetter(cityInfoDTO.getCityPyName()));
        return cityDistrict;
    }

    private static String getFirstLetter(String str) {
        if (str == null || str.isEmpty()) {
            return "";
        }
        return String.valueOf(str.charAt(0));
    }

    private List<CityInfoDTO> getDpCityList(int skip) {
        return dpCityInfoService.getInlandCityList(skip, BATCH_SIZE);
    }

    private CityDistrict mtCityToCityDistrict(CityInfo cityInfo) {
        CityDistrict cityDistrict = new CityDistrict();
        cityDistrict.setCityId(cityInfo.getId());
        cityDistrict.setCityName(cityInfo.getName());
        cityDistrict.setFirstLetter(cityInfo.getFirstChar());
        return cityDistrict;
    }

    private AllCityVO cityDistrictListToAllCityVO(List<CityDistrict> cityDistrictList) {
        if (CollectionUtils.isEmpty(cityDistrictList)) {
            return new AllCityVO();
        }
        AllCityVO allCityVO = new AllCityVO();
        List<CityCluster> cityClusters = cityDistrictList.stream().collect(Collectors.groupingBy(CityDistrict::getFirstLetter)).entrySet().stream().map(entry -> {
            CityCluster cluster = new CityCluster();
            cluster.setFirstLetter(entry.getKey());
            cluster.setCityList(entry.getValue());
            return cluster;
        }).sorted(Comparator.comparing(CityCluster::getFirstLetter)).collect(Collectors.toList());
        allCityVO.setCityClusters(cityClusters);
        return allCityVO;
    }
}
