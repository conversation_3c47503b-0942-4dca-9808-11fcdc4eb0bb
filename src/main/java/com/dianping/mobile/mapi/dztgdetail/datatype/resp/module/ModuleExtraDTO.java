package com.dianping.mobile.mapi.dztgdetail.datatype.resp.module;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON>omlin on 2018/12/13.
 */
@TypeDoc(description = "更多模块数据列表")
@MobileDo(id = 0x6836)
@Data
public class ModuleExtraDTO implements Serializable {

    @FieldDoc(description = "模块标题")
    @MobileField(key = 0xd0b6)
    private boolean success;

    @FieldDoc(description = "模块标题")
    @MobileField(key = 0xc473)
    private List<ModuleConfigDo> moduleConfigDos;

    @FieldDoc(description = "实验结果集")
    @MobileField(key = 0xe72c)
    private List<ModuleAbConfig> expResults;

    @FieldDoc(description = "更多模块数据扩展字段")
    @MobileField(key = 0x143f)
    private List<ModuleConfigDo> extraInfo;
}
