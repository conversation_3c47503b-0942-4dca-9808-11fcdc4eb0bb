package com.dianping.mobile.mapi.dztgdetail.common.enums;

import lombok.Getter;

@Getter
public enum MiniProgramSceneEnum {
    /**
     * 小程序的使用场景
     */
    NORMAL_PROMO(1, "normalPromo"),
    TIMES_CARD(2, "timesCard"),
    MEMBER_CARD(3, "memberCard"),//折扣卡
    PINTUAN(4, "pintuan"),
    IDLE_PROMO(5, "idlePromo"),
    JOY_CARD(6, "joyCard"),//玩乐卡，废弃了
    IM_URL(7, "imUrl");

    private int code;
    private final String promoScene;

    MiniProgramSceneEnum(int code, String promoScene) {
        this.code = code;
        this.promoScene = promoScene;
    }

    public static MiniProgramSceneEnum codeOf(int code) {
        for (MiniProgramSceneEnum value : MiniProgramSceneEnum.values()) {
            if (value.code == code) {
                return value;
            }
        }

        throw new UnsupportedOperationException("MiniProgramSceneEnum has no code of " + code);
    }
}