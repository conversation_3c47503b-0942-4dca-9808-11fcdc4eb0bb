package com.dianping.mobile.mapi.dztgdetail.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-01-05
 * @desc 海马平台热门美甲款式配置
 */

@Data
@NoArgsConstructor
public class HaiMaNailFilterConfig {
    /**
     * 热门款式主题名称
     */
    private String name;
    private String id;
    /**
     * 快筛词id
     */
    private String filterIds;
    /**
     * 频道页二级id，用于锚点定位
     */
    private String secondTabId;
    /**
     * 主题引导icon图
     */
    private String icon;
}
