package com.dianping.mobile.mapi.dztgdetail.util;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @date 2021-05-20-5:47 下午
 */
public class JsonUtils {

    private static final Gson gson = new GsonBuilder().create();

    public static String toJson(Object o) {
        return gson.toJson(o);
    }

    public static<T> T fromJson(String json, Type type) {
        return gson.fromJson(json, type);
    }

}
