package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.UserWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.AppImageSize;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DpDztgShareModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DztgShareModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.MtDztgShareModule;
import com.dianping.mobile.mapi.dztgdetail.helper.ImageHelper;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.NumbersUtils;
import com.dianping.mobile.mapi.dztgdetail.util.ShareIdUtils;
import com.dianping.userremote.dto.collection.FavorDTO;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.Future;

public class ShareModuleProcessor extends AbsDealProcessor{
    @Autowired
    private UserWrapper userWrapper;

    @Override
    public void prepare(DealCtx ctx) {
        Future favorFuture = userWrapper.preLoadFavor(ctx.getEnvCtx().getDpUserId(), ctx.getDpId());
        ctx.getFutureCtx().setFavorFuture(favorFuture);
    }

    @Override
    public void process(DealCtx ctx) {
        FavorDTO favorDTO = userWrapper.getFutureResult(ctx.getFutureCtx().getFavorFuture());
        boolean favResult = (favorDTO != null);
        ctx.setFavResult(favResult);

        DztgShareModule shareModule = new DztgShareModule();
        if(ctx.isMt()) {
            MtDztgShareModule mtDztgShareModule = buildMtDztgShareModule(ctx);
            shareModule.setMt(mtDztgShareModule);
        } else {
            DpDztgShareModule dpDztgShareModule = buildDpDztgShareModule(ctx);
            shareModule.setDp(dpDztgShareModule);
        }
        shareModule.setShareId(ShareIdUtils.getShareId());
        // 分享图下发开关
        shareModule.setScreenShotShareEnable(LionConfigUtils.getScreenShotShareSwitch());
        ctx.setDztgShareModule(shareModule);
    }

    private MtDztgShareModule buildMtDztgShareModule(DealCtx ctx) {
        MtDztgShareModule mtDztgShareModule = new MtDztgShareModule();
        mtDztgShareModule.setMtDealGroupId(ctx.getMtId());
        mtDztgShareModule.setBrandName(ctx.getDealGroupBase() != null ? ctx.getDealGroupBase().getProductTitle() : null);
        mtDztgShareModule.setTitle(null);
        //  美团私域直播 分享模设置微信名称
        mtDztgShareModule.setWxName(ctx.getWxName());
        //  美团私域直播 分享模设置个人分销参数
        mtDztgShareModule.setUserDistributionParam(ctx.getUserDistributionParam());

        DealGroupBaseDTO dealGroupBaseDTO = ctx.getDealGroupBase();
        if(dealGroupBaseDTO != null) {
            double price = dealGroupBaseDTO.getDealGroupPrice() == null ? 0 : dealGroupBaseDTO.getDealGroupPrice().doubleValue();
            String defaultPic = dealGroupBaseDTO.getDefaultPic();
            int[] imageSize  = convertWidthHeight(ctx, AppImageSize.MINI_PROGRAM_SHARE.width, AppImageSize.MINI_PROGRAM_SHARE.height);
            if (imageSize != null) {
                mtDztgShareModule.setImgUrl(ImageHelper.formatWithoutWatermark(defaultPic, imageSize[0], imageSize[1], true));
            } else {
                mtDztgShareModule.setImgUrl(ImageHelper.formatWithoutWatermark(defaultPic, AppImageSize.MEDIUM.width, AppImageSize.MEDIUM.height, true));
            }
            mtDztgShareModule.setPrice(price);
        }

        return mtDztgShareModule;
    }

    public int[] convertWidthHeight(DealCtx ctx, int width, int height) {
        if (!ctx.isMtLiveMinApp()) {
            return null;
        }
        return getNewWidthHeight(width, height);
    }

    /**
     * 输入图片宽高，高不变，宽高比5:4，输出新的宽高
     * @param width 图宽
     * @param height 图高
     * @return 新宽高
     */
    private int[] getNewWidthHeight(int width, int height) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.ShareModuleProcessor.getNewWidthHeight(int,int)");
        if (!NumbersUtils.greaterThanZero(width) || !NumbersUtils.greaterThanZero(height)) {
            return null;
        }
        int newWidth = (int) (height * 5.0 / 4.0);
        int newHeight = height;
        if(newWidth > width) {
            newWidth = width;
            newHeight = (int) (width * 4.0 / 5.0);
        }
        return new int[]{newWidth, newHeight};
    }
    
    public DpDztgShareModule buildDpDztgShareModule(DealCtx ctx) {
        DpDztgShareModule dpDztgShareModule = new DpDztgShareModule();

        DealGroupBaseDTO dealGroupBaseDTO = ctx.getDealGroupBase();
        if(dealGroupBaseDTO != null) {
            dpDztgShareModule.setShortTitle(dealGroupBaseDTO.getProductTitle());
            dpDztgShareModule.setProductTitle(dealGroupBaseDTO.getProductTitle());
            dpDztgShareModule.setDpDealGroupId(dealGroupBaseDTO.getDealGroupId());
            dpDztgShareModule.setInterested(ctx.isFavResult());

            String defaultPic = dealGroupBaseDTO.getDefaultPic();
            String photo, bigPhoto;
            int[] imageSize  = convertWidthHeight(ctx, AppImageSize.MINI_PROGRAM_SHARE.width, AppImageSize.MINI_PROGRAM_SHARE.height);
            if (imageSize != null) {
               photo = ImageHelper.format(defaultPic, imageSize[0], imageSize[1]);
            } else {
               photo = ImageHelper.format(defaultPic, AppImageSize.MEDIUM.width, AppImageSize.MEDIUM.height);
            }

            int[] bigImageSize = convertWidthHeight(ctx, AppImageSize.ORIGINAL.width, AppImageSize.ORIGINAL.height);
            if (bigImageSize != null) {
               bigPhoto = ImageHelper.format(defaultPic, bigImageSize[0], bigImageSize[1]);
            } else {
               bigPhoto = ImageHelper.format(defaultPic, AppImageSize.ORIGINAL.width, AppImageSize.ORIGINAL.height);
            }
            dpDztgShareModule.setPhoto(photo);
            dpDztgShareModule.setBigPhoto(bigPhoto);

            double price = dealGroupBaseDTO.getDealGroupPrice() == null ? 0.0 : dealGroupBaseDTO.getDealGroupPrice().doubleValue();
            double marketPrice = dealGroupBaseDTO.getMarketPrice() == null ? 0.0 : dealGroupBaseDTO.getMarketPrice().doubleValue();

            dpDztgShareModule.setPrice(price);
            dpDztgShareModule.setOriginalPrice(marketPrice);
        }
        return dpDztgShareModule;
    }
}
