package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealNoticeLayerReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealnotice.DealNoticeLayerPBO;
import com.dianping.mobile.mapi.dztgdetail.facade.DzDealNoticeLayerFacade;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.List;

/**
 * 功能描述:  <p>
 * <AUTHOR>
 * @since mapi-dztgdetail-web
 */
@Controller("general/platform/dztgdetail/dzdealnoticelayer.bin")
@Action(url = "dzdealnoticelayer.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class DzDealNoticeLayerAction extends AbsAction<DealNoticeLayerReq>  {

    @Resource
    private DzDealNoticeLayerFacade dzDealNoticeLayerFacade;

    @Override
    protected IMobileResponse validate(DealNoticeLayerReq request, IMobileContext iMobileContext) {
        IdUpgradeUtils.processProductIdForDealNoticeLayerReq(request, "dzdealnoticelayer.bin");
        if (request == null || request.getDealgroupid() <= 0) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @Override
    @CryptMethod
    protected IMobileResponse execute(DealNoticeLayerReq request, IMobileContext iMobileContext) {
        EnvCtx envCtx = initEnvCtxV2(iMobileContext);
        DealNoticeLayerPBO response = dzDealNoticeLayerFacade.getDealGroupNoticeLayer(request, envCtx);
        return new CommonMobileResponse(response);
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
