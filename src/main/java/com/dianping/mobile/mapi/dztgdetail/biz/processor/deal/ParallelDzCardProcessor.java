package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DzCardPromoWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MiniProgramSceneEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.sankuai.dzcard.navigation.api.dto.CardQualifyEventIdDTO;
import com.sankuai.dzcard.navigation.api.enums.QualifyEventTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2024/8/27 15:51
 */
@Slf4j
public class ParallelDzCardProcessor extends AbsDealProcessor {

    @Resource
    private DzCardPromoWrapper wrapper;

    private final static String Cat_Name = "CardQualifyEventType";

    @Override
    public void prepare(DealCtx ctx) {
        ctx.getFutureCtx().setDzCardFuture(wrapper.prepare(ctx));
    }

    @Override
    public void process(DealCtx ctx) {
        List<CardQualifyEventIdDTO> qualifyEventIdDTOList = wrapper.resolve(ctx.getFutureCtx().getDzCardFuture());
        if (CollectionUtils.isEmpty(qualifyEventIdDTOList)) {
            return;
        }
        //目前通过第一个权益来判断用户权益优先级，在权益台会卡控只返回一种权益，但是权益项可能有多个，需要看具体业务场景
        CardQualifyEventIdDTO cardQualifyEventIdDTO = qualifyEventIdDTOList.get(0);
        if (cardQualifyEventIdDTO.getQualifyEventType() == QualifyEventTypeEnum.DISCOUNT_CARD.getCode()
                && isJoyDcCardValid(ctx)) {
            //折扣卡
            Cat.logEvent(Cat_Name, "DISCOUNT_CARD");
            processDiscountCard(ctx, qualifyEventIdDTOList);
        } else if (cardQualifyEventIdDTO.getQualifyEventType() == QualifyEventTypeEnum.DAO_ZONG_CARD.getCode()
                && isMemberPriceProcessorEnable(ctx)) {
            //商家会员
            Cat.logEvent(Cat_Name, "SHOP_MEMBER_CARD");
            //暂时用老的接口，后续迁到新接口
            //processShopMemberCard(ctx, qualifyEventIdDTOList);
        } else {
            Cat.logEvent(Cat_Name, String.valueOf(cardQualifyEventIdDTO.getQualifyEventType()));
        }
    }

    private void processDiscountCard(DealCtx ctx, List<CardQualifyEventIdDTO> qualifyEventIdDTOList) {
        CardQualifyEventIdDTO memberCard = null;
        CardQualifyEventIdDTO memberDayCard = null;

        for (CardQualifyEventIdDTO card : qualifyEventIdDTOList) {
            if (card.getQualifyEventType() == QualifyEventTypeEnum.DISCOUNT_CARD.getCode()) {
                memberCard = card;
            } else if (card.getQualifyEventType() == QualifyEventTypeEnum.MEMBER_DAY.getCode()) {
                memberDayCard = card;
            }
        }
        if (memberDayCard != null) {
            //会员日优先级更高
            ctx.getPriceContext().setDcCardMemberDay(true);
            ctx.getPriceContext().setDcCardMemberCard(memberDayCard);
        } else if (memberCard != null) {
            ctx.getPriceContext().setDcCardMemberCard(memberCard);
        }
    }

//    private void processShopMemberCard(DealCtx ctx, List<CardQualifyEventIdDTO> qualifyEventIdDTOList) {
//        CardQualifyEventIdDTO cardQualifyEventIdDTO = qualifyEventIdDTOList.get(0);
//        Map<String, String> extInfoMap = cardQualifyEventIdDTO.getExtInfoMap();
//        if (MapUtils.isEmpty(extInfoMap)) {
//            return;
//        }
//        try {
//            MemberInterestDetailDTO memberInterestDetailDTO = MemberInterestInPriceDisplayUtil.deserializeMemberInterest(
//                    extInfoMap.get(QualifyExtInfoKeyEnum.SHOP_MEMBER_ORIGIN_RESULT.name())
//            );
//            if (memberInterestDetailDTO == null) {
//                throw new IllegalArgumentException("商家会员原始返回值为空");
//            }
//            boolean isShopMember = Boolean.parseBoolean(extInfoMap.get(QualifyExtInfoKeyEnum.IS_SHOP_MEMBER.name()));
//            boolean isNewShopMember = Boolean.parseBoolean(extInfoMap.get(QualifyExtInfoKeyEnum.IS_NEW_SHOP_MEMBER.name()));
//            boolean isDealOnlyForNewMember = Boolean.parseBoolean(extInfoMap.get(QualifyExtInfoKeyEnum.IS_DEAL_ONLY_FOR_NEW_MEMBER.name()));
//            ShopMemberCardCtx shopMemberCardCtx = new ShopMemberCardCtx();
//            shopMemberCardCtx.setMemberCardId(cardQualifyEventIdDTO.getMemberCardId());
//            shopMemberCardCtx.setMemberUserRule(cardQualifyEventIdDTO.getMemberUserRule());
//            shopMemberCardCtx.setShopMember(isShopMember);
//            shopMemberCardCtx.setNewShopMember(isNewShopMember);
//            shopMemberCardCtx.setDealOnlyForNewMember(isDealOnlyForNewMember);
//            shopMemberCardCtx.setOriginalResult(memberInterestDetailDTO);
//            ctx.getPriceContext().setShopMemberCardCtx(shopMemberCardCtx);
//        } catch (Exception e) {
//            Cat.logEvent(Cat_Name, "SHOP_MEMBER_CARD_ERROR");
//            log.error("ParallelMemberCardProcessor.processShopMemberCard,qualifyEventIdDTOList:{}", JSON.toJSONString(qualifyEventIdDTOList), e);
//        }
//    }

    private boolean isJoyDcCardValid(DealCtx ctx) {
        boolean isNotExternal = !ctx.isExternal();
        boolean isExternalAndEnabled = ctx.getEnvCtx().isExternalAndEnabled(MiniProgramSceneEnum.JOY_CARD.getPromoScene());
        return isNotExternal || isExternalAndEnabled;
    }

    private boolean isMemberPriceProcessorEnable(DealCtx ctx) {
        boolean isMainApp = ctx.getEnvCtx().judgeMainApp();
        //APP请求且满足类目条件，类目现在由权益台来控制
        //return isMainApp && GreyUtils.judgeMemberPriceCategory(ctx);
        return isMainApp;
    }

}
