package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2020/6/17 5:03 下午
 */
@Data
@MobileDo(id = 0xd742)
public class SalesRankingVO implements Serializable {
    /**
     * 榜单落地页链接
     */
    @MobileField(key = 0xe250)
    private String rankingUrl;

    /**
     * 榜单文案
     */
    @MobileField(key = 0x8a4)
    private String rankingText;
}