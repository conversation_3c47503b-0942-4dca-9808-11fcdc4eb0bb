package com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.healthExamination;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/10/18 11:02 上午
 */
@MobileDo(id = 0x9059)
public class ReadMoreVO implements Serializable {
    /**
     * 查看更多跳转链接
     */
    @MobileDo.MobileField(key = 0xcebc)
    private String moreJumpUrl;

    /**
     * 查看更多文案
     */
    @MobileDo.MobileField(key = 0xb0d1)
    private String moreText;

    public String getMoreJumpUrl() {
        return moreJumpUrl;
    }

    public void setMoreJumpUrl(String moreJumpUrl) {
        this.moreJumpUrl = moreJumpUrl;
    }

    public String getMoreText() {
        return moreText;
    }

    public void setMoreText(String moreText) {
        this.moreText = moreText;
    }
}