package com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.EncryptedLinkField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/10/26
 */
@Data
@TypeDoc(description = "到综团单沉浸页筛选项详情")
@MobileDo(id = 0x9573)
public class ImmersiveSelectItemVO {
    /**
     * 子选项
     */
    @MobileDo.MobileField(key = 0xa05)
    private List<SelectItemTwoLevelVO> subs;

    /**
     * 名称
     */
    @MobileDo.MobileField(key = 0x7ab8)
    private String name;

    /**
     * 是否选中
     */
    @MobileDo.MobileField(key = 0xb59e)
    private Boolean selected;

    /**
     * 选中值
     */
    @MobileDo.MobileField(key = 0x2585)
    private String selectValue;

    /**
     * 筛选项的跳转链接
     */
    @MobileDo.MobileField(key = 0x774e)
//    @EncryptedLinkField(queries = {"shopid","shopId","poiid","poiId","poi_id"})
    private String jumpUrl;

    /**
     * 筛选项可选择的类型，0表示单选，1表示多选
     */
    @MobileDo.MobileField(key = 0xb790)
    private Integer choiceType;

    /**
     * 筛选项iconUrl
     */
    @MobileDo.MobileField(key = 0xf39b)
    private String iconUrl;
}