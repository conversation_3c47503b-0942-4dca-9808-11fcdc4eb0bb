package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.mobile.mapi.dztgdetail.biz.dealnotice.DealNoticeProcessor;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealNoticeLayerCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealNoticeLayerReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealnotice.DealNoticeLayerPBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class DzDealNoticeLayerFacade {
    @Resource
    private List<DealNoticeProcessor> dealNoticeProcessor;
    
    public DealNoticeLayerPBO getDealGroupNoticeLayer(DealNoticeLayerReq request, EnvCtx envCtx) {
        DealNoticeLayerCtx ctx = initCtx(request, envCtx);
        DealNoticeLayerPBO result = new DealNoticeLayerPBO();
        for (int i = 0; i < dealNoticeProcessor.size(); i++) {
            DealNoticeProcessor processor = dealNoticeProcessor.get(i);
            if (processor.valid(ctx)){
                return  processor.getDealNoticeLayerPBO(ctx);
            }
        }
        return result;
    }
    
    public DealNoticeLayerCtx initCtx(DealNoticeLayerReq request, EnvCtx envCtx){
        DealNoticeLayerCtx ctx = new DealNoticeLayerCtx(envCtx);
        ctx.setRequest(request);
        ctx.setDealGroupId(request.getDealgroupid());
        ctx.setPoiid(request.getPoiid());
        if (request.getUserlng() != null && request.getUserlat() != null) {
            ctx.setUserLat(request.getUserlat());
            ctx.setUserLng(request.getUserlng());
        }
        return ctx;
    }

}
