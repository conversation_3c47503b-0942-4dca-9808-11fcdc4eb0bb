package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DzCardPromoWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * @Author: guangyujie
 * @Date: 2024/8/29 14:23
 */
@Slf4j
public class ParallelNewUserProcessor extends AbsDealProcessor {

    @Resource
    private DzCardPromoWrapper wrapper;

    @Override
    public void prepare(DealCtx ctx) {
        ctx.getFutureCtx().setUserStateFuture(wrapper.prepareUserState(ctx));
    }

    @Override
    public void process(DealCtx ctx) {
        ctx.getPriceContext().setNewUser(wrapper.resolveUserState(ctx.getFutureCtx().getUserStateFuture()));
    }
}
