package com.dianping.mobile.mapi.dztgdetail.biz;

import com.dianping.cat.Cat;
import com.dianping.pay.promo.display.api.PromoDisplayService;
import com.dianping.pay.promo.display.api.dto.BatchQueryPromoDisplayRequest;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.pay.promo.display.api.dto.QueryPromoDisplayRequest;
import com.dianping.pay.promo.rule.api.dto.Response;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

/**
 * Created by wang<PERSON><PERSON> on 17/6/20.
 */
@Component
@Slf4j
public class PromoBiz extends AbstractBiz {

    @Resource(name = "promoDisplayServiceFuture")
    private PromoDisplayService promoDisplayServiceFuture;

    public Future preQueryPromoDisplayDTO(QueryPromoDisplayRequest request) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.PromoBiz.preQueryPromoDisplayDTO(com.dianping.pay.promo.display.api.dto.QueryPromoDisplayRequest)");
        try {
            promoDisplayServiceFuture.queryPromoDisplayDTO(request);
            return FutureFactory.getFuture();
        } catch (Exception e) {
            log.error("queryPromoDisplayDTO has error", e);
        }
        return null;
    }

    public Future preBatchQueryPromoDisplayDTO(BatchQueryPromoDisplayRequest request) {
        try {
            promoDisplayServiceFuture.batchQueryPromoDisplayDTO(request);
            return FutureFactory.getFuture();
        } catch (Exception e) {
            log.error("preBatchQueryPromoDisplayDTO has error", e);
        }
        return null;
    }

    public List<PromoDisplayDTO> queryPromoDisplayDTO(QueryPromoDisplayRequest request) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.biz.PromoBiz.queryPromoDisplayDTO(com.dianping.pay.promo.display.api.dto.QueryPromoDisplayRequest)");
        Response<List<PromoDisplayDTO>> resp = getFutureResult(preQueryPromoDisplayDTO(request));
        return resp != null && resp.isSuccess() ? resp.getResult() : null;
    }

    public Map<Integer, List<PromoDisplayDTO>> batchQueryPromoDisplayDTO(BatchQueryPromoDisplayRequest request) {
        Response<Map<Integer, List<PromoDisplayDTO>>> resp = getFutureResult(preBatchQueryPromoDisplayDTO(request));
        return resp != null && resp.isSuccess() ? resp.getResult() : null;
    }

}
