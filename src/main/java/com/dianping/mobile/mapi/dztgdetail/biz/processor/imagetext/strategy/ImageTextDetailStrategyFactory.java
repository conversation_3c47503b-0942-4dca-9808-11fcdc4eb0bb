package com.dianping.mobile.mapi.dztgdetail.biz.processor.imagetext.strategy;

import com.dianping.mobile.mapi.dztgdetail.common.enums.ImageTextStrategyEnum;
import com.google.common.collect.Maps;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-12-11
 * @desc 图文详情处理策略工厂类
 */
@Component
public class ImageTextDetailStrategyFactory implements InitializingBean, ApplicationContextAware {
    private static final Map<ImageTextStrategyEnum, ImageTextDetailStrategy> STRATEGY_MAP = Maps.newEnumMap(ImageTextStrategyEnum.class);

    private ApplicationContext applicationContext;

    public ImageTextDetailStrategy getImageTextDetailStrategy(String strategyName) {
        ImageTextStrategyEnum imageTextStrategyEnum = ImageTextStrategyEnum.of(strategyName);
        return STRATEGY_MAP.get(imageTextStrategyEnum);
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        applicationContext.getBeansOfType(ImageTextDetailStrategy.class)
                .values()
                .forEach(strategy -> STRATEGY_MAP.put(strategy.getStrategyName(), strategy));
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
