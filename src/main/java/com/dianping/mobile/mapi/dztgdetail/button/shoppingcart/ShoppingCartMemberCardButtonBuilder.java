package com.dianping.mobile.mapi.dztgdetail.button.shoppingcart;

import java.math.RoundingMode;
import java.util.List;

import com.dianping.mobile.mapi.dztgdetail.button.joy.JoyDiscountCardButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ShoppingCartStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PriceRuleModule;
import com.dianping.mobile.mapi.dztgdetail.helper.CardHelper;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;

/**
 * <AUTHOR>
 * @date 2023/5/15
 */
public class ShoppingCartMemberCardButtonBuilder extends JoyDiscountCardButtonBuilder {

    @Override
    protected void afterBuild(DealCtx context, DealBuyBtn button) {
        super.afterBuild(context, button);

        if(isUserHoldMemberCard(context)){
            button.setAddShoppingCartStatus(ShoppingCartStatusEnum.ADD_SHOPPING_CART.code);
        }else{
            button.setBtnTitle("开通会员购买");
            button.setAddShoppingCartStatus(ShoppingCartStatusEnum.NONE.code);
        }
        button.setPriceRuleModule(buildPriceRuleModule(context, button));
    }

    private PriceRuleModule buildPriceRuleModule(DealCtx context, DealBuyBtn button) {
        PriceRuleModule priceRuleModule = new PriceRuleModule();
        priceRuleModule.setPriceRuleType(BuyBtnTypeEnum.MEMBER_CARD.getCode());
        priceRuleModule.setPriceRuleTags(buildPriceRuleTag(button));
        priceRuleModule.setPromoDesc(buildPromoDesc(context));
        return priceRuleModule;
    }


    private List<String> buildPriceRuleTag(DealBuyBtn button) {
        List<String> priceRuleTags = Lists.newArrayList();

        String priceRuleTitle= "会员价";
        String pricePerTime = "￥" + button.getPriceStr();

        priceRuleTags.add(priceRuleTitle);
        priceRuleTags.add(pricePerTime);

        return priceRuleTags;
    }

    private String buildPromoDesc(DealCtx context) {
        PriceDisplayDTO price = super.getPrice(context);
        String savedMoney = price.getMarketPrice().subtract(price.getPrice())
                .setScale(1, RoundingMode.CEILING).stripTrailingZeros().toPlainString();

        return "共省￥" + savedMoney;
    }

    private boolean isUserHoldMemberCard(DealCtx context) {
        return CardHelper.holdCard(context.getPriceContext().getDcCardMemberCard());
    }

}
