package com.dianping.mobile.mapi.dztgdetail.common;

import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import java.util.Collections;
import java.util.List;

public class MttgVersion implements Comparable {
    public static final MttgVersion V7_1 = new MttgVersion("7.1");
    public static final MttgVersion V7_2 = new MttgVersion("7.2");
    public static final MttgVersion V7_3 = new MttgVersion("7.3");
    public static final MttgVersion V7_4 = new MttgVersion("7.4");
    public static final MttgVersion V7_5 = new MttgVersion("7.5");
    public static final MttgVersion V7_6 = new MttgVersion("7.6");
    public static final MttgVersion V7_9 = new MttgVersion("7.9");
    public static final MttgVersion V8_1 = new MttgVersion("8.1");
    public static final MttgVersion V8_6 = new MttgVersion("8.6");
    public static final MttgVersion V8_9 = new MttgVersion("8.9");
    public static final MttgVersion V9_0 = new MttgVersion("9.0");
    public static final MttgVersion V9_1 = new MttgVersion("9.1");
    public static final MttgVersion V9_4 = new MttgVersion("9.4");
    public static final MttgVersion V9_8_6 = new MttgVersion("9.8.6");
    public static final MttgVersion V9_13_400 = new MttgVersion("9.13.400");

    private final int[] nums;

    public MttgVersion(String num) {
        List<Integer> list = parseToIntList(num, "\\.");
        nums = new int[list.size()];
        int index = -1;
        for (Integer i : list) {
            nums[++index] = i;
        }
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || !(o instanceof MttgVersion)) {
            return false;
        }
        MttgVersion target = (MttgVersion) o;
        if (target.nums.length != this.nums.length) {
            return false;
        }
        for (int i = 0; i < this.nums.length; i++) {
            if (this.nums[i] != target.nums[i]) {
                return false;
            }
        }
        return true;
    }

    @Override
    public int compareTo(Object o) {
        if (o == null || !(o instanceof MttgVersion)) {
            return 1;
        }
        MttgVersion target = (MttgVersion) o;
        int index = -1;
        for (int i : this.nums) {
            if (target.nums.length <= ++index) {
                return 1;
            }
            int result = i - target.nums[index];
            if (result != 0) {
                return result;
            }
        }
        if (target.nums.length > ++index) {
            return -1;
        }
        return 0;
    }

    public List<Integer> parseToIntList(String str, String regex) {
        if (StringUtils.isBlank(str)) {
            return Lists.newArrayList();
        } else {
            str = str.trim().replace("\"", "");
            String[] strArr = str.split(regex);
            List<Integer> numList = Lists.newArrayListWithCapacity(strArr.length);

            for(int i = 0; i < strArr.length; ++i) {
                numList.add(NumberUtils.toInt(strArr[i]));
            }

            return numList;
        }
    }
}