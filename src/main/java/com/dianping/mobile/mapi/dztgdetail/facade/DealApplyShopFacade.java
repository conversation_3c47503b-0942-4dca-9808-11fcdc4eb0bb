package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.deal.shop.dto.BestShopFastReq;
import com.dianping.deal.shop.dto.BestShopSimpleDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperCacheWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetDealApplyShopRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.shop.DealApplyShopVO;
import com.dianping.mobile.mapi.dztgdetail.exception.DealApplyShopException;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.basic.SVIPMapperCacheProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024-08-10
 * @desc 实现没有门店id查询团单适用门店
 */
@Component
public class DealApplyShopFacade {

    @Autowired
    private DealGroupWrapper dealGroupWrapper;

    @Autowired
    private MapperCacheWrapper mapperCacheWrapper;

    public DealApplyShopVO queryBestShop(GetDealApplyShopRequest request, boolean isMT, int clientType) {
        if (Objects.isNull(request)) {
            return null;
        }
        BestShopFastReq bestShopFastReq = buildBestShopReq(request, isMT, clientType);
        BestShopSimpleDTO bestShopSimpleDTO = dealGroupWrapper.getFutureResult(dealGroupWrapper.preDealGroupBestShopFastly(bestShopFastReq));
        if (bestShopSimpleDTO == null || bestShopSimpleDTO.getDpShopId() <= 0) {
            throw new DealApplyShopException("查不到最佳门店!!!");
        }
        long mtShopId = mapperCacheWrapper.fetchMtShopId(bestShopSimpleDTO.getDpShopId());
        if (mtShopId <= 0) {
            throw new DealApplyShopException("根据点评门店查不到美团门店!!!");
        }
        return new DealApplyShopVO(bestShopSimpleDTO.getDpShopId(), mtShopId);
    }

    public BestShopFastReq buildBestShopReq(GetDealApplyShopRequest request, boolean isMT, int clientType) {
        if (Objects.isNull(request)) {
            return null;
        }
        BestShopFastReq bestShopReq = new BestShopFastReq();
        bestShopReq.setUserGcjLat(Objects.nonNull(request.getUserLat()) ? request.getUserLat() : 0L);
        bestShopReq.setUserGcjLng(Objects.nonNull(request.getUserLng()) ? request.getUserLng() : 0L);
        bestShopReq.setClientType(clientType);
        if (isMT) {
            bestShopReq.setGeoDpCityId(mapperCacheWrapper.fetchDpCityId(
                    Optional.ofNullable(request.getGpsCityId()).orElse(0)
            ));
            bestShopReq.setDpDealGroupId(mapperCacheWrapper.fetchDpDealId(
                    Optional.ofNullable(request.getDealGroupId()).map(Long::intValue).orElse(0)
            ));
            bestShopReq.setDpCityId(mapperCacheWrapper.fetchDpCityId(
                    Optional.ofNullable(request.getHomeCityId()).orElse(0)
            ));
        } else {
            /**
             * （神会员需求）点评app环境IOS端的用户定位城市传的是美团的，注意这个是大坑，具体参考文档https://km.sankuai.com/collabpage/2289046202
             */
            if (SVIPMapperCacheProcessor.isSpecialGpsCityId(clientType)) {
                bestShopReq.setGeoDpCityId(mapperCacheWrapper.fetchDpCityId(
                        Optional.ofNullable(request.getGpsCityId()).orElse(0)
                ));
            } else {
                bestShopReq.setGeoDpCityId(
                        Optional.ofNullable(request.getGpsCityId()).orElse(0)
                );
            }
            bestShopReq.setDpDealGroupId(
                    Optional.ofNullable(request.getDealGroupId()).orElse(0L)
            );
            bestShopReq.setDpCityId(
                    Optional.ofNullable(request.getHomeCityId()).orElse(0)
            );
        }
        return bestShopReq;
    }

}
