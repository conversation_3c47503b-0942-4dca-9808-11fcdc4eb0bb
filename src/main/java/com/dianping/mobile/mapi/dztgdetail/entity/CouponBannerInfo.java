package com.dianping.mobile.mapi.dztgdetail.entity;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class CouponBannerInfo {
    @FieldDoc(description = "适用券金额总和")
    private BigDecimal amount;
    @FieldDoc(description = "优惠券到期时间，取到期时间最早的")
    private Date endTime;
    @FieldDoc(description = "券是否已领取")
    private Boolean used;
    @FieldDoc(description = "是否展示横幅")
    private Boolean show;
}
