package com.dianping.mobile.mapi.dztgdetail.common.enums;

import lombok.Getter;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024-12-11
 * @desc 图片文字策略枚举
 */
@Getter
public enum ImageTextStrategyEnum {

    DEFAULT("default", "默认策略"),
    NONE_TITLE("none", "不展示图文详情标题策略"),
    COLLAPSE("collapse", "图文详情折叠策略"),
    EXPAND("expand", "图文详情展开策略")
    ;


    final String strategyName;
    final String strategyDesc;

    ImageTextStrategyEnum(String strategyName, String strategyDesc) {
        this.strategyName = strategyName;
        this.strategyDesc = strategyDesc;
    }

    public static ImageTextStrategyEnum of(String strategyName) {
        return Stream.of(ImageTextStrategyEnum.values())
                .filter(strategy -> strategy.getStrategyName().equals(strategyName))
                .findFirst()
                .orElse(DEFAULT);
    }
}
