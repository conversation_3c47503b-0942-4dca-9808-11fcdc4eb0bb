package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.annotation.MobileRequest.Param;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

@TypeDoc(description = "团单图文详情请求参数")
@MobileRequest
@Data
public class DealImageTextDetailReq implements IMobileRequest {

    @Deprecated
    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，原int类型")
    @Param(name = "dealgroupid")
    private Integer dealgroupid;

    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，string类型")
    @Param(name = "stringdealgroupid")
    private String stringDealGroupId;

    @FieldDoc(description = "站点：APP可以不传，值参见后端枚举",rule = "@see com.dianping.deal.common.enums.ClientTypeEnum")
    @Param(name = "clienttype")
    private Integer clienttype;

    @FieldDoc(description = "页面来源",rule = "和团详主接口dzdealbase.bin保持一致")
    @Param(name = "pagesource")
    private String pageSource;
}
