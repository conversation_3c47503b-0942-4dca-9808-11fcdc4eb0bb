package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.cat.Cat;
import com.dianping.midas.baymax.ad.count.thirdparty.codec.binary.Base64;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.sankuai.inf.kms.pangolin.api.model.EncryptionRequest;
import com.sankuai.inf.kms.pangolin.api.service.EncryptServiceFactory;
import com.sankuai.inf.kms.pangolin.api.service.IEncryptService;
import com.sankuai.pearl.framework.enums.MiniProgramType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;

/**
 * @Author: zhangyuan103
 * @Date: 2025/6/4
 * @Description: 微信小程序登录态兼容 openid -- kms加解密工具类
 */
@Slf4j
public class KmsUtils {
    // 使用volatile确保多线程可见性
    private static volatile IEncryptService encryptService;

    private static final String NAMESPACE = "com.sankuai.wpt.user.thirdlogin";

    private static final String M_KEY_MIDDLE = "weixinOpenid";

    private static final String secret = "86c07c1424a90402";

    private static final String iv = "7b5e30b5d03f1561";

    // 打点数据
    // 打点key
    private static final String OPENID = "openid";
    // 端侧，区分美团和点评
    private static final String CLIENT_TYPE = "clientType";
    // 结果，0是解析后的openid和原openid相等，1是解析后的openid和原openid不相等，2是解析openid异常或者解析后的openid为空
    private static final String RES = "res";
    private static final String EQUALS = "0";
    private static final String NOT_EQUALS = "1";
    private static final String EXCEPTION = "2";

    // 私有构造函数防止实例化
    private KmsUtils() {}

    // 初始化 IEncryptService 的方法
    private static IEncryptService getEncryptService() {
        if (encryptService == null) {
            synchronized (KmsUtils.class) {
                if (encryptService == null) {
                    EncryptionRequest request = EncryptionRequest.Builder.anEncryptionRequest().withNamespace(NAMESPACE)
                            .withKeyName(M_KEY_MIDDLE).build();
                    encryptService = EncryptServiceFactory.create(request);
                }
            }
        }
        return encryptService;
    }

    /**
     * 小程序openid检验
     *
     * @param envCtx 环境上下文
     * @return openId是否有效, 返回true代表是有效的openId
     */
    public static boolean checkOpenid(EnvCtx envCtx) {
        if (envCtx == null) {
            return false;
        }
        return checkOpenid(envCtx.getOpenId(), envCtx.getOpenIdCipher(), envCtx.isWxMini() || envCtx.isMainWX(), envCtx.isMt(), envCtx.isHarmony());
    }

    /**
     * 小程序openid检验
     *
     * @param iMobileContext 环境上下文
     * @return openId是否有效, 返回true代表是有效的openId
     */
    public static boolean checkOpenid(IMobileContext iMobileContext) {
        if (iMobileContext == null) {
            return false;
        }
        return checkOpenid(AbsAction.getOpenId(iMobileContext),
                AbsAction.getOpenIdCipher(iMobileContext),
                iMobileContext.getMiniProgramType().equals(MiniProgramType.WeChat), iMobileContext.isMeituanClient(),
                AbsAction.isPureHarmony(iMobileContext));
    }

    /**
     * 小程序openid检验
     *
     * @param plainText openId
     * @param cipherText openId密文
     * @param isWxMini 是否微信小程序
     * @param isMt 是否为美团端
     * @param isHarmony 是否为美团端
     * @return openId是否有效, 返回true代表是有效的openId
     */
    public static boolean checkOpenid(String plainText, String cipherText, boolean isWxMini, boolean isMt, boolean isHarmony) {
        if (!LionConfigUtils.getWxOpenidSwitch()) {
            return false;
        }
        // 排除鸿蒙端 和 非微信小程序端的
        if (isHarmony || !isWxMini) {
            return false;
        }
        if (StringUtils.isBlank(plainText) || StringUtils.isBlank(cipherText)) {
            return false;
        }
        String decryptText = "";
        if (isMt) {
            decryptText = mxDecryPt(plainText, cipherText);
        } else {
            decryptText = dxDecryptEod(plainText, cipherText);
        }
        catLog(plainText, decryptText, isMt);
        return plainText.equals(decryptText);
    }

    private static void catLog(String plainText, String decryptText, boolean isMt) {
        HashMap<String, String> tags = new HashMap<>();
        tags.put(CLIENT_TYPE, String.valueOf(isMt? DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP : DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP));
        if (decryptText == null) {
            tags.put(RES, EXCEPTION);
        } else if (!plainText.equals(decryptText)) {
            tags.put(RES, NOT_EQUALS);
        } else {
            tags.put(RES, EQUALS);
        }
        Cat.logMetricForCount(OPENID, tags);
    }

    /**
     * 适用于美小openid密文解密
     *
     * @param cipherText
     * @return
     */
    public static String mxDecryPt(String plainText, String cipherText) {
        // 使用已初始化的 encryptService
        try {
            String decryptedOpenid = getEncryptService().decryptUTF8String(cipherText);
            if (StringUtils.isBlank(decryptedOpenid)) {
                throw new IllegalArgumentException("decryptedOpenid is null");
            }
            return decryptedOpenid;
        } catch (Exception e) {
            log.warn("Failed to decrypt mt openid, openid = {}, openIdCipher = {}", plainText, cipherText, e);
            return null;
        }
    }

    /**
     * 适用于点小评openid密文解密
     *
     * @param encryptedData
     * @return
     */
    public static String dxDecryptEod(String plainText, String encryptedData) {
        try {
            byte[] cipherData = Base64.decodeBase64(encryptedData);
            SecretKeySpec secretKeySpec = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(2, secretKeySpec, new IvParameterSpec(iv.getBytes(StandardCharsets.UTF_8)));
            byte[] plainTextBytes = cipher.doFinal(cipherData);
            if (plainTextBytes == null || plainTextBytes.length == 0) {
                throw new IllegalArgumentException("plainTextBytes is null");
            }
            return new String(plainTextBytes);
        } catch (Exception e) {
            log.warn("Failed to decrypt dp openid, openid={}, openIdCipher={}", plainText, encryptedData, e);
            return null;
        }
    }
}