package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule;

import java.io.Serializable;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2023/8/31
 * @since mapi-dztgdetail-web
 */
@MobileDo(id = 0xcd8b)
public class DealStyleBO implements Serializable {
    /**
     *
     */
    @MobileDo.MobileField(key = 0x5eb5)
    private ModuleConfigsModule moduleConfigsModule;

    public ModuleConfigsModule getModuleConfigsModule() {
        return moduleConfigsModule;
    }

    public void setModuleConfigsModule(ModuleConfigsModule moduleConfigsModule) {
        this.moduleConfigsModule = moduleConfigsModule;
    }
}
