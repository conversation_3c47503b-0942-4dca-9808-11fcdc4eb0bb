package com.dianping.mobile.mapi.dztgdetail.util;
import java.util.regex.*;

public class TextSpaceFormatter {
    private static final String insertedLetter = "\u2006"; // 六分之一空格
    private static final String spaceLetters = " \u3000"; // 半角和全角空格

    // 汉字正则表达式（使用Unicode脚本）
    private static final String regHz = "\\p{sc=Han}";

    // 西文字符各部分
    private static final String regWestCommon = "0-9a-zA-Z";

    // 希腊字母范围
    private static final String regGreek = "\\u0370-\\u03FF\\u1F00-\\u1FFF";

    // 韩文字符范围
    private static final String regKorean = "\\uAC00-\\uD7A3\\u3130-\\u318F\\u1100-\\u11FF\\uA960-\\uA97F\\uD7B0-\\uD7FF";

    // 日文字符范围（包含代理对）
    private static final String regJapan =
            "\\u3040-\\u309F\\u30A0-\\u30FF\\u31F0-\\u31FF" +
                    "\\uD82C\\uDD00-\\uD82C\\uDD2F\\uD82B\\uDFF0-\\uD82B\\uDFFF" +
                    "\\uD82C\\uDC00-\\uD82C\\uDCFF\\uD82C\\uDD30-\\uD82C\\uDD6F";

    // 组合西文字符正则
    private static final String regWest =
            "[" + regWestCommon + regGreek + regKorean + regJapan + "]";

    // 预编译正则表达式
    private static final Pattern CHAR_TO_HZ_PATTERN = Pattern.compile(
            regWest + "[" + spaceLetters + "]*+(?=" + regHz + ")",
            Pattern.UNICODE_CHARACTER_CLASS
    );

    private static final Pattern HZ_TO_CHAR_PATTERN = Pattern.compile(
            regHz + "[" + spaceLetters + "]*+(?=" + regWest + ")",
            Pattern.UNICODE_CHARACTER_CLASS
    );

    private static String addSpaceBetweenCharAndHZ(String str) {
        String step1 = replaceMatches(CHAR_TO_HZ_PATTERN, str);
        return replaceMatches(HZ_TO_CHAR_PATTERN, step1);
    }

    private static String replaceMatches(Pattern pattern, String input) {
        Matcher matcher = pattern.matcher(input);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String matched = matcher.group();
            String replacement = matched.trim() + insertedLetter;
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    public static String formatTextSpace(String txt) {
        if (txt == null) {
            throw new IllegalArgumentException("Input must be a string");
        }
        return addSpaceBetweenCharAndHZ(txt);
    }
    
}
