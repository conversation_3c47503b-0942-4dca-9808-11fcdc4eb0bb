package com.dianping.mobile.mapi.dztgdetail.helper;

import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

@Service
public class BusinessHourParser {

    /**
     * 解析营业时间
     *
     * @param power                shopDTO的营业状态power
     * @param originalBusinessHour shopDTO的营业时间
     * @param today                bizForecastDTO的营业时间分布,48bit的string
     * @return
     *
     */
    public static BusinessHour parseBusinessHour(Integer power, String originalBusinessHour, String today) {
        BusinessHour businessHourTuple = new BusinessHour("", "", new JSONObject());
        if (power != null) {
            if (power == 1) {
                businessHourTuple.businessStatus = "已关门";
                businessHourTuple.businessHourRichText.put("text", businessHourTuple.businessStatus);
                businessHourTuple.businessHourRichText.put("textsize", 13);
                businessHourTuple.businessHourRichText.put("textcolor", "#111111");
            } else if (power == 4) {
                businessHourTuple.businessStatus = "门店尚未营业";
                businessHourTuple.businessHourRichText.put("text", businessHourTuple.businessStatus);
                businessHourTuple.businessHourRichText.put("textsize", 13);
                businessHourTuple.businessHourRichText.put("textcolor", "#FF463A");
            } else if (power == 2) {
                businessHourTuple.businessStatus = "门店暂停营业";
                businessHourTuple.businessHourRichText.put("text", businessHourTuple.businessStatus);
                businessHourTuple.businessHourRichText.put("textsize", 13);
                businessHourTuple.businessHourRichText.put("textcolor", "#FF463A");
            } else if (power == 5) {
                handleBusinessHourWhenInBusiness(businessHourTuple, originalBusinessHour, today);
            } else {
                // 其他状态不作展示
            }
        }
        return businessHourTuple;
    }

    private static void handleBusinessHourWhenInBusiness(BusinessHour businessHourTuple, String businessHour, String today) {
        if (StringUtils.isEmpty(businessHour)) {
            businessHourTuple.businessStatus = "";
            businessHourTuple.businessHour = "";
        } else {
            // 需要根据营业时间判断营业中或者休息中
            businessHourTuple.businessHour = businessHour.replace("\n", " ");
            if (today != null) {
                businessHourTuple.businessStatus = calculateBusinessStatusByBusinessHour(today);
                businessHourTuple.businessHourRichText.put("text", businessHourTuple.businessStatus);
                businessHourTuple.businessHourRichText.put("textsize", 13);
                if (businessHourTuple.businessStatus.equals("营业中")) {
                    businessHourTuple.businessHourRichText.put("textcolor", "#111111");
                } else if (businessHourTuple.businessStatus.equals("休息中")) {
                    businessHourTuple.businessHourRichText.put("textcolor", "#FF463A");
                }
            } else {
                // 没有预测营业时间字段, 默认不展示
                businessHourTuple.businessStatus = "";
                businessHourTuple.businessHour = "";
            }
        }
    }

    /**
     * 根据营业时间计算门店营业状态 营业中 or 休息中
     */
    private static String calculateBusinessStatusByBusinessHour(String today) {
        LocalDateTime now = LocalDateTime.now();
        int hour = now.getHour();
        int minute = now.getMinute();
        int index;
        if (minute < 30) {
            index = hour * 2;
        } else {
            index = hour * 2 + 1;
        }
        char bit = today.charAt(index);
        if (bit == '0') {
            return "休息中";
        } else {
            return "营业中";
        }
    }

    public static class BusinessHour {
        public String businessStatus; //营业状态: 已关闭(power=1),门店尚未营业(power=4),门店暂停营业(power=2),营业中(power=5),休息中(power=5)
        public String businessHour; // 营业时间
        public JSONObject businessHourRichText;

        public BusinessHour(String businessStatus, String businessHour, JSONObject businessHourRichText) {
            this.businessStatus = businessStatus;
            this.businessHour = businessHour;
            this.businessHourRichText = businessHourRichText;
        }
    }
}
