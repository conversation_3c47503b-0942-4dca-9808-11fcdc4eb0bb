package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.CatEvents;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DealAttrCons;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class AttributeUtils {

    public static boolean hasAttribute(List<AttributeDTO> attributeDtoList, String key, String value) {
        if (attributeDtoList != null) {
            for (AttributeDTO attr : attributeDtoList) {
                if (StringUtils.equals(attr.getName(), key) && attr.getValue().contains(value)) {
                    return true;
                }
            }
        }
        return false;
    }

    public static String getFirstValue(List<AttributeDTO> attributeDtoList, String key) {
        List<String> values = getAttributeValues(key, attributeDtoList);
        return CollectionUtils.isEmpty(values) ? StringUtils.EMPTY : values.get(0);
    }

    public static String getFirstValueV2(List<AttrDTO> attributeDtoList, String key) {
        List<String> values = getAttributeValuesV2(key, attributeDtoList);
        return CollectionUtils.isEmpty(values) ? StringUtils.EMPTY : values.get(0);
    }

    public static List<String> getAttributeValuesV2(String key, List<AttrDTO> attributeDtoList) {
        if (attributeDtoList != null) {
            for (AttrDTO attr : attributeDtoList) {
                if (StringUtils.equals(attr.getName(), key)) {
                    return attr.getValue();
                }
            }
        }
        return Lists.newArrayList();
    }

    public static List<String> getAttributeValues(String key, List<AttributeDTO> attributeDtoList) {
        if (attributeDtoList != null) {
            for (AttributeDTO attr : attributeDtoList) {
                if (StringUtils.equals(attr.getName(), key)) {
                    return attr.getValue();
                }
            }
        }
        return Lists.newArrayList();
    }

    public static String getAttributeValue(String key, List<AttributeDTO> attributeDtoList) {
        if (attributeDtoList != null) {
            for (AttributeDTO attr : attributeDtoList) {
                if (StringUtils.equals(attr.getName(), key)) {
                    String value = attr.getValue().get(0);
                    return StringUtils.isBlank(value) ? "" : value.trim();
                }
            }
        }

        return "";
    }

    public static List<Integer> getCategoryIds(List<AttributeDTO> attributes) {
        if (CollectionUtils.isNotEmpty(attributes)) {
            for (AttributeDTO attribute : attributes) {
                if (StringUtils.equals(attribute.getName(), DealAttrKeys.KEY_CATEGORY)) {
                    List<Integer> categoryIds = Lists.newArrayList();
                    for (String s : attribute.getValue()) {
                        int categoryId = NumberUtils.toInt(s, 0);
                        if (categoryId > 0) {
                            categoryIds.add(categoryId);
                        }
                    }
                    return categoryIds;
                }
            }
        }
        return Lists.newArrayList();
    }

    public static boolean isTort(List<AttributeDTO> attributeDtoList) {
        boolean isTort = hasAttribute(attributeDtoList, DealAttrKeys.TORT, DealAttrKeys.TORT_VALUE);
        String hideType = getFirstValue(attributeDtoList, DealAttrKeys.HIDE_TYPE);

        if(isTort) {
            boolean blockSecondaryTreatment = Lion.getBoolean("com.sankuai.dzu.tpbase.dztgdetailweb","com.sankuai.dzu.tpbase.dztgdetailweb.secondary.treatment.block.switch", false);
            if(DealAttrKeys.HIDE_TYPE_VALUE_SECONDARY_TREATMENT.equals(hideType) && !blockSecondaryTreatment) {
                return false;
            }
            return true;
        }
        return false;
    }

    public static void logTortType(List<AttributeDTO> attributeDtoList) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils.logTortType(java.util.List)");
        boolean isTort = hasAttribute(attributeDtoList, DealAttrKeys.TORT, DealAttrKeys.TORT_VALUE);
        String hideType = getFirstValue(attributeDtoList, DealAttrKeys.HIDE_TYPE);

        if(!isTort) {
            return;
        }
        if(StringUtils.isBlank(hideType) || DealAttrKeys.HIDE_TYPE_VALUE_COMMON.equals(hideType)) {
            Cat.logEvent(CatEvents.DEALBASE_FAIL, "NormalTortDeal");
            return;
        }
        if(DealAttrKeys.HIDE_TYPE_VALUE_EMERGENT_BLOCK.equals(hideType)) {
            Cat.logEvent(CatEvents.DEALBASE_FAIL, "EMERGENT_BLOCK");
            return;
        }
        boolean blockSecondaryTreatment = Lion.getBoolean("com.sankuai.dzu.tpbase.dztgdetailweb","com.sankuai.dzu.tpbase.dztgdetailweb.secondary.treatment.block.switch", false);
        if(DealAttrKeys.HIDE_TYPE_VALUE_SECONDARY_TREATMENT.equals(hideType) && blockSecondaryTreatment) {
            if(blockSecondaryTreatment) {
                Cat.logEvent(CatEvents.DEALBASE_FAIL, "SECONDARY_TREATMENT_BLOCK");
            } else {
                Cat.logEvent(CatEvents.DEALBASE_FAIL, "SECONDARY_TREATMENT_UNBLOCK");
            }
        }
    }



}