package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.CatEvents;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetHotNailStyleRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.nailstyle.HotNailStyleModuleVO;
import com.dianping.mobile.mapi.dztgdetail.facade.NailStyleFacade;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-08-24
 * @desc 查看热门款式
 */
@InterfaceDoc(
        displayName = "到综团单美甲类目热门款式查询接口",
        type = "restful",
        description = "到综团单美甲类目热门款式查询，展示款式图、引导语、美甲频道页跳转链接等",
        scenarios = "该接口适用于双平台App站点的团购详情页中美甲类目展示热门款式",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "liuwen17"
)
@Controller("general/platform/dztgdetail/gethotnailstyle.bin")
@Action(url = "gethotnailstyle.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class GetHotNailStyleAction extends AbsAction<GetHotNailStyleRequest> {

    @Resource
    private NailStyleFacade nailStyleFacade;

    @Override
    protected IMobileResponse validate(GetHotNailStyleRequest request, IMobileContext context) {
        if (Objects.isNull(request) || request.getDealGroupId() <= 0) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @Override
    @CryptMethod
    protected IMobileResponse execute(GetHotNailStyleRequest request, IMobileContext context) {
        try {
            EnvCtx envCtx = initEnvCtx(context);
            HotNailStyleModuleVO hotNailStyleModuleVO = nailStyleFacade.getHotNailStyle(request, envCtx);
            if (Objects.nonNull(hotNailStyleModuleVO)) {
                Cat.logMetricForCount(CatEvents.HOT_NAIL_MODULE_SUC);
                return new CommonMobileResponse(hotNailStyleModuleVO);
            }
            Cat.logMetricForCount(CatEvents.HOT_NAIL_MODULE_NO_DARA);
            return new CommonMobileResponse(Resps.NoDataResp);
        } catch (Exception e) {
            Cat.logMetricForCount(CatEvents.HOT_NAIL_MODULE_ERR);
            Cat.logError("gethotnailstyle.bin err: {}", e);
        }
        return Resps.SYSTEM_ERROR;
        
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }
}
