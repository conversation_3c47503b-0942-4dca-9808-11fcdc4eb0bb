package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/29
 */
@MobileDo(id = 0x3b4b)
@Data
public class DealFeature implements Serializable {

    /**
     * 团单特征类型
     */
    @MobileDo.MobileField(key = 0x8f0c)
    private String type;

    /**
     * 团单特征值
     */
    @MobileDo.MobileField(key = 0xa91)
    private List<String> features;

    /**
     * 团单特征值弹窗扩展
     */
    @MobileDo.MobileField(key = 0xb201)
    private List<PopupExtVO> popupExts;

}
