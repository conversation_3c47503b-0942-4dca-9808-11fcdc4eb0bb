package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.DecryptedField;
import lombok.Data;

import java.io.Serializable;

@Data
@TypeDoc(description = "团详关联模块入参")
@MobileRequest
public class RelatedModuleReq implements IMobileRequest, Serializable {

    @Deprecated
    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，int类型")
    @MobileRequest.Param(name = "dealgroupid")
    private Integer dealGroupId;

    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，string类型")
    @MobileRequest.Param(name = "stringdealgroupid")
    private String stringDealGroupId;

    /**
     * @link com.dianping.mobile.mapi.dztgdetail.common.enums.RelatedModuleSceneEnum
     */
    @FieldDoc(description = "模块场景")
    @MobileRequest.Param(name = "scene")
    private String scene;

    @FieldDoc(description = "门店id", rule = "美团平台为美团商户ID，点评平台为点评商户ID")
    @MobileRequest.Param(name = "shopidstr")
    private String shopIdStr;
    @MobileRequest.Param(name = "shopIdStrEncrypt")
    @DecryptedField(targetFieldName = "shopIdStr")
    private String shopIdStrEncrypt;

    @FieldDoc(description = "城市ID", rule = "美团平台为美团城市ID，点评平台为点评城市ID，优先给到首页城市ID（非用户地理位置城市）")
    @MobileRequest.Param(name = "cityid")
    private Integer cityId;

    @FieldDoc(description = "用户经度(注意app和小程序经纬度类型不同)")
    @MobileRequest.Param(name = "userlng")
    private Double userLng;

    @FieldDoc(description = "用户纬度(注意app和小程序经纬度类型不同)")
    @MobileRequest.Param(name = "userlat")
    private Double userLat;

    @FieldDoc(description = "开始位置")
    @MobileRequest.Param(name = "start")
    private Integer start;

    @FieldDoc(description = "数量")
    @MobileRequest.Param(name = "limit")
    private Integer limit;

}