package com.dianping.mobile.mapi.dztgdetail.entity;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-08-30
 * @desc 查询款式图片的参数
 */
@Data
@Builder
public class QueryExhibitImageParam {
    /**
     * 点评团单ID
     */
    private Integer dpDealGroupId;
    /**
     * 二级团单类目ID
     */
    private Integer categoryId;
    /**
     * 分页起始，从0开始
     */
    private Integer start;
    /**
     * 分页大小
     */
    private Integer limit;
    /**
     * 商铺ID
     */
    private Long shopId;
    /**
     * 筛选项信息
     */
    private String selectValue;
    private Integer cityId;
    private Double userLng;
    private Double userLat;
    private Integer clientType;
    /**
     * 服务类型
     */
    private String serviceType;
    /**
     * 服务类型ID
     */
    private Long serviceTypeId;
    /**
     * 团单状态
     */
    private Integer dealGroupStatus;

    /**
     * 款式ID
     */
    private Long infoContentId;

    /**
     * 美团团单ID
     */
    private Integer mtDealGroupId;

}
