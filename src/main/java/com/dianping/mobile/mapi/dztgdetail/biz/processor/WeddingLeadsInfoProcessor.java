package com.dianping.mobile.mapi.dztgdetail.biz.processor;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealBookWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.sankuai.clr.content.process.thrift.dto.ShopBookInfoProcessDTO;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @author: zhangyuan103
 * @create: 2025-03-12
 * @description:
 */
public class WeddingLeadsInfoProcessor extends AbsDealProcessor {
    @Resource
    private DealBookWrapper dealBookWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        return DealUtils.isWeddingLeadsDeal(ctx);
    }

    @Override
    public void prepare(DealCtx ctx) {
        // 根据shopId判断团购是否有商家预约权益
        ctx.getFutureCtx().setShopBookInfoFuture(dealBookWrapper.preQueryShopBookInfo(ctx));
    }

    @Override
    public void process(DealCtx ctx) {
        if (Objects.isNull(ctx.getFutureCtx()) || Objects.isNull(ctx.getFutureCtx().getShopBookInfoFuture())) {
            return;
        }
        ShopBookInfoProcessDTO shopBookInfoProcessDTO = dealBookWrapper.queryShopBookInfo(ctx.getFutureCtx().getShopBookInfoFuture());
        if (shopBookInfoProcessDTO == null) {
            ctx.setHasBookBenefit(false);
            return;
        }
        Integer status = shopBookInfoProcessDTO.getStatus();
        ctx.setHasBookBenefit(Objects.equals(status, 1));
    }
}
