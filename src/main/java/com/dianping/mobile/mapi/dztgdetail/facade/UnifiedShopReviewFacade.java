package com.dianping.mobile.mapi.dztgdetail.facade;

import com.alibaba.fastjson.JSON;
import com.dianping.cip.growth.mana.api.dto.response.UserManaDTO;
import com.dianping.core.type.PageModel;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.biz.aspect.UrlDztgClientCheckAnnotation;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DetailTagService;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.AbsWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ReviewWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.UserWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.ShopUuidUtils;
import com.dianping.mobile.mapi.dztgdetail.common.constants.ReviewFilterType;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.PicVideoStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.ShopReviewCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedModuleExtraReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedShopReviewReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.ReviewDetailDO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.ReviewPicDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.ReviewTagDO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.ReviewUserModel;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.UnifiedShopReviewList;
import com.dianping.mobile.mapi.dztgdetail.helper.ShopReviewHelper;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.NetUtils;
import com.dianping.review.professional.ReviewDataV2;
import com.dianping.tuangou.dztg.bjwrapper.api.dto.MtUserDto;
import com.dianping.ugc.pic.remote.dto.MtReviewPicInfo;
import com.dianping.ugc.pic.remote.dto.VideoData;
import com.dianping.ugc.proxyService.remote.dto.AnonymousUserInfo;
import com.dianping.ugc.proxyService.remote.dto.feed.Pendant;
import com.dianping.ugc.proxyService.remote.enums.feed.PendantTypeEnum;
import com.dianping.ugc.review.remote.dto.Expense;
import com.dianping.ugc.review.remote.dto.MTQueryResult;
import com.dianping.ugc.review.remote.dto.MTReviewData;
import com.dianping.ugc.review.remote.dto.ReviewCount;
import com.dianping.ugc.review.remote.dto.ReviewPic;
import com.dianping.ugc.review.remote.dto.ReviewVideo;
import com.dianping.userremote.base.dto.UserDTO;
import com.dianping.vipremote.vo.UserInfoForAppVO;
import com.dp.arts.client.response.Record;
import com.dp.arts.client.response.Response;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.general.product.query.center.client.builder.model.DealBasicInfoBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupCategoryBuilder;
import com.sankuai.general.product.query.center.client.builder.model.ServiceProjectBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.AttrSubjectEnum;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Future;

import static com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.QueryCenterProcessor.getQueryCenterDealGroupAttrKey;

/**
 * Created by zuomlin on 2018/12/13.
 */
@Component
public class UnifiedShopReviewFacade extends AbsWrapper {

    private static final int SHOP_PAGE_REVIEW_LENGTH = 150;
    /**
     * 优质点评标志
     */
    private static final String DP_REVIEW_LIST_URL = "dianping://review?referid=%s&refertype=0";
    private static final String MT_REVIEW_LIST_URL = "imeituan://www.meituan.com/reviewlist?refertype=1&referid=%s&filterid=%d";
    private static final String BAIDU_REVIEW_LIST_URL = "/packages/ugc/pages/reviewlist/reviewlist?shopUuid=%s&msource=wxappmain";
    private static final String DP_REVIEW_TAG_URL = "dianping://review?referid=%s&refertype=0&tagtype=%d&selecttagname=%s_%d";
    private static final String MT_REVIEW_TAG_URL = "imeituan://www.meituan.com/reviewlist?refertype=1&referid=%s&selecttagname=%s&tagtype=%d";
    /**
     * 优质点评标志
     */
    private static final String GOOD_REVIEW_PIC = "https://www.dpfile.com/ugc/reviewhonour/goodReview.png";
    private static final int ONE_SHOP_REVIEW = 1;
    private static final int TWO_SHOP_REVIEW = 2;
    @Resource
    private UserWrapper userWrapper;

    @Resource
    private ReviewWrapper reviewWrapper;
    @Autowired
    private QueryCenterWrapper queryCenterWrapper;
    @Resource
    private DouHuService douHuService;
    @Resource
    private DetailTagService detailTagService;

    @UrlDztgClientCheckAnnotation
    public UnifiedShopReviewList queryUnifiedShopReviewList(UnifiedShopReviewReq request, EnvCtx envCtx, IMobileContext iMobileContext) throws Exception {
        ShopReviewCtx shopReviewCtx = initShopReviewCtx(request, envCtx, iMobileContext);
        DealGroupDTO dealGroupDTO = getDealGroupDTO(request, envCtx);
        int displayReviewCount = getDisplayCount(request, envCtx, iMobileContext, dealGroupDTO);
        UnifiedShopReviewList unifiedShopReviewList = new UnifiedShopReviewList();

        try {
            if (envCtx.isMt()) {
                getMtReviewDetailList(shopReviewCtx, unifiedShopReviewList, displayReviewCount);
                unifiedShopReviewList.setRedirectUrl(String.format(MT_REVIEW_LIST_URL, request.getDealGroupId(), 800));//todo查看全部点评
                if (unifiedShopReviewList.getRecordCount() > 0) {
                    unifiedShopReviewList.setTopTitle("网友评价");
                    unifiedShopReviewList.setAppendTitle(unifiedShopReviewList.getRecordCount() + " 条评论");
                    if (unifiedShopReviewList.getRecordCount() > displayReviewCount) {
                        unifiedShopReviewList.setBottomTitle("查看" + unifiedShopReviewList.getRecordCount() + "条评论");
                        unifiedShopReviewList.setDisplayBottom(true);
                    }
                }
            } else {
                getDpReviewDetailList(shopReviewCtx, unifiedShopReviewList, displayReviewCount);
                String formatListUrl = DztgClientTypeEnum.DIANPING_BAIDUMAP_MINIAPP.equals(envCtx.getDztgClientTypeEnum()) ?
                        BAIDU_REVIEW_LIST_URL : DP_REVIEW_LIST_URL;
                unifiedShopReviewList.setRedirectUrl(String.format(formatListUrl, getShopUuid(shopReviewCtx)));
                if (unifiedShopReviewList.getRecordCount() > 0) {
                    unifiedShopReviewList.setTopTitle("精选评价");
                    if (unifiedShopReviewList.getRecordCount() > displayReviewCount) {
                        unifiedShopReviewList.setBottomTitle("查看全部精选评价");
                        unifiedShopReviewList.setDisplayBottom(true);
                    }
                }
            }
            // 如果是新穿戴甲团单
            if (DealUtils.isNewWearableNailDeal(dealGroupDTO)) {
                if (unifiedShopReviewList.getRecordCount() > displayReviewCount) {
                    unifiedShopReviewList.setBottomTitle("全部" + unifiedShopReviewList.getRecordCount() + "条评价");
                    unifiedShopReviewList.setDisplayBottom(true);
                }
            }
        } catch (Exception e) {
            logger.error("queryUnifiedShopReviewList has exception.", e);
        }

        return unifiedShopReviewList;
    }

    /**
     * 团详框架改版，获取页面展示数量
     * @param request
     * @param envCtx
     * @return
     */
    public int getDisplayCount(UnifiedShopReviewReq request, EnvCtx envCtx, IMobileContext iMobileContext,
                                DealGroupDTO dealGroupDTO){
        int displayCount = TWO_SHOP_REVIEW;
        long publishCategoryId = getCategoryId(dealGroupDTO);
        // 团详框架改版
        ModuleAbConfig moduleAbConfig = douHuService.enableCardStyleV2(envCtx, (int)publishCategoryId, request.getMrnVersion());
        if (douHuService.hitEnableCardStyleV2(moduleAbConfig)){
            // 如果"网友评价"tab 处于Tab的顶部
            if (detailTagService.reviewIsTopTab(convertToUnifiedModuleExtraReq(request), envCtx, iMobileContext)){
                displayCount = ONE_SHOP_REVIEW;
            }
        }
        return displayCount;
    }
    private UnifiedModuleExtraReq convertToUnifiedModuleExtraReq(UnifiedShopReviewReq req){
        UnifiedModuleExtraReq unifiedModuleExtraReq = new UnifiedModuleExtraReq();
        unifiedModuleExtraReq.setDealGroupId(req.getDealGroupId());
        unifiedModuleExtraReq.setExpResults("");
        unifiedModuleExtraReq.setCityId(-1);
        unifiedModuleExtraReq.setMrnVersion(req.getMrnVersion());
        return unifiedModuleExtraReq;
    }
    private long getCategoryId(DealGroupDTO dealGroupDTO){

        long publishCategoryId = (dealGroupDTO == null || dealGroupDTO.getCategory() == null || dealGroupDTO.getCategory().getCategoryId() == null)
                ? 0L : dealGroupDTO.getCategory().getCategoryId();
        return publishCategoryId;
    }

    public DealGroupDTO getDealGroupDTO(UnifiedShopReviewReq request, EnvCtx envCtx){
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet(Long.valueOf(request.getDealGroupId())), envCtx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP)
                .category(DealGroupCategoryBuilder.builder().categoryId().serviceType().serviceTypeId())
                .dealBasicInfo(DealBasicInfoBuilder.builder().thirdPartyId().status())
                .serviceProject(ServiceProjectBuilder.builder().all())
                .attrsByKey(AttrSubjectEnum.DEAL_GROUP, getQueryCenterDealGroupAttrKey())
                .build();
        try {
            return queryCenterWrapper.getDealGroupDTO(queryByDealGroupIdRequest);
        } catch (TException e) {
            logger.error("QueryCenterWrapper.getDealGroupDTO error!", e);
            return null;
        }
    }

    private String getShopUuid(ShopReviewCtx shopReviewCtx) {
        return StringUtils.isNotBlank(shopReviewCtx.getDpShopUuid()) ?
                shopReviewCtx.getDpShopUuid() :
                String.valueOf(shopReviewCtx.getDpLongShopId());
    }

    private void getMtReviewDetailList(ShopReviewCtx shopReviewCtx, UnifiedShopReviewList unifiedShopReviewList, int displayReviewCount) {
        Future mtShopReviewTagFuture = reviewWrapper.getMTAllShopReviewTagFuture(shopReviewCtx);
        Future mtReviewCountFuture = reviewWrapper.preReviewCountByDealIds(shopReviewCtx.getMtId());
        Future mtQueryResultFuture = reviewWrapper.getMtReviewByDeal(shopReviewCtx, displayReviewCount);

        MTQueryResult mtQueryResult = reviewWrapper.getFutureResult(mtQueryResultFuture);
        ReviewCount mtReviewCount = reviewWrapper.getReviewCount(shopReviewCtx.getMtId(), mtReviewCountFuture);

//        Future shopIdMapperFuture =  mapperWrapper.preDpShopIdByMtShopId(shopReviewCtx.getMtLongShopId());
//        long dpShopId = mapperWrapper.getDpShopIdByMtShopIdLong(shopIdMapperFuture);
//        shopReviewCtx.setDpLongShopId(dpShopId);
        //Future shopReviewFuture = reviewWrapper.getShopReviewFuture(shopReviewCtx, displayReviewCount);
//        PageModel pageModel = reviewWrapper.getFutureResult(shopReviewFuture);

        List<ReviewDetailDO> reviewDetailDOList = buildMtReviewDOList(shopReviewCtx, mtQueryResult); //美团数据
        unifiedShopReviewList.setRecordCount(mtReviewCount == null ? 0 : mtReviewCount.getAll());
        if (ShopReviewHelper.disPlayShopReviewTag()) {
            List<ReviewTagDO> reviewTagDOList = createReviewTag(mtShopReviewTagFuture, shopReviewCtx);
            unifiedShopReviewList.setReviewTagList(reviewTagDOList);
        }
        int dpReviewCount = 0;
        int mtReviewCountAll = unifiedShopReviewList.getRecordCount();
        //补充点评UGC数据
        /*if (mtReviewCountAll < displayReviewCount && ShopReviewHelper.mtShopReviewNeedSupply()) {
            Future shopIdMapperFuture = mapperWrapper.preDpShopIdbyMtShopId(shopReviewCtx.getMtShopId());
            shopReviewCtx.setDpShopId(mapperWrapper.getDpShopIdbyMtShopId(shopIdMapperFuture));
            Future shopReviewFuture = reviewWrapper.getShopReviewFuture(shopReviewCtx, displayReviewCount);
            PageModel pageModel = reviewWrapper.getFutureResult(shopReviewFuture);
            dpReviewCount = pageModel == null ? 0 : pageModel.getRecordCount();
            List<ReviewDetailDO> tempReviewDetailList = buildDpReviewDOList(shopReviewCtx, pageModel, true);
            if (CollectionUtils.isNotEmpty(tempReviewDetailList)) {
                if (CollectionUtils.isEmpty(reviewDetailDOList)) {
                    reviewDetailDOList = Lists.newArrayList();
                }
                reviewDetailDOList.addAll(tempReviewDetailList);
            }
        }*/
        unifiedShopReviewList.setRecordCount(dpReviewCount + mtReviewCountAll);
        unifiedShopReviewList.setReviewDetailList(reviewDetailDOList);
    }

    private void getDpReviewDetailList(ShopReviewCtx shopReviewCtx, UnifiedShopReviewList unifiedShopReviewList, int displayReviewCount) {
        Future shopReviewFuture = reviewWrapper.getShopReviewFuture(shopReviewCtx, displayReviewCount);
        Future allShopReviewTagFuture = reviewWrapper.getDPAllShopReviewTagFuture(shopReviewCtx);

        PageModel pageModel = reviewWrapper.getFutureResult(shopReviewFuture);
        unifiedShopReviewList.setRecordCount(pageModel == null ? 0 : pageModel.getRecordCount());

        List<ReviewDetailDO> reviewDetailDOList = buildDpReviewDOList(shopReviewCtx, pageModel, false);
        if (ShopReviewHelper.disPlayShopReviewTag()) {
            List<ReviewTagDO> reviewTagDOList = createReviewTag(allShopReviewTagFuture, shopReviewCtx);
            unifiedShopReviewList.setReviewTagList(reviewTagDOList);
        }
        int mtReviewCount = 0;
        int dpReviewCount = unifiedShopReviewList.getRecordCount();
        if (dpReviewCount < displayReviewCount && ShopReviewHelper.dpShopReviewNeedSupply()) {
            Future mtShopReviewFuture = reviewWrapper.getMtShopReviewFutureV2(shopReviewCtx.getDpLongShopId(), 0, displayReviewCount - dpReviewCount, ReviewFilterType.RANK_ALGO);
            MTQueryResult mtQueryResult = reviewWrapper.getFutureResult(mtShopReviewFuture);
            if (mtQueryResult != null && CollectionUtils.isNotEmpty(mtQueryResult.getMtReviewDataList())) {
                if (CollectionUtils.isEmpty(reviewDetailDOList)) {
                    reviewDetailDOList = Lists.newArrayList();
                }
                List<MTReviewData> mtReviewDataList = mtQueryResult.getMtReviewDataList();
                for (MTReviewData mtReviewData : mtReviewDataList) {
                    MtUserDto userModel = userWrapper.getUserModel(mtReviewData.getUserId());
                    ReviewDetailDO reviewDetailDO = ShopReviewHelper.mtReviewDataToReviewDetailDO(mtReviewData, userModel, null,  shopReviewCtx);
                    reviewDetailDOList.add(reviewDetailDO);
                }
                mtReviewCount = mtQueryResult.getQueryResultCount();
            }
        }
        unifiedShopReviewList.setRecordCount(dpReviewCount + mtReviewCount);
        unifiedShopReviewList.setReviewDetailList(reviewDetailDOList);
    }

    /**
     * 从评论内容中获取动态标签
     * 包括非正面评价标签
     */
    private List<ReviewTagDO> createReviewTag(Future allShopReviewTagFuture, ShopReviewCtx shopReviewCtx) {
        List<ReviewTagDO> reviewTagDOList = Lists.newArrayList();
        createBaseReviewTag(allShopReviewTagFuture, reviewTagDOList, shopReviewCtx);
        return reviewTagDOList;
    }

    /**
     * 从评论内容中获取动态标签
     */
    private void createBaseReviewTag(Future mtShopReviewTagFuture, List<ReviewTagDO> reviewTagDOList, ShopReviewCtx shopReviewCtx) {
        Response response = reviewWrapper.getFutureResult(mtShopReviewTagFuture);
        if (response == null) {
            return;
        }
        createDynamicAbstract(response, reviewTagDOList, shopReviewCtx);
    }

    private void createDynamicAbstract(Response response, List<ReviewTagDO> reviewAbstracts, ShopReviewCtx shopReviewCtx) {

        if (!response.getStatus().equals(Response.OK) || CollectionUtils.isEmpty(response.getRecordList())) {
            return;
        }

        List<Record> recordList = response.getRecordList();
        List<ReviewTagDO> reviewAbstractList = Lists.newArrayList();
        int count = 1;
        for (Record record : recordList) {
            Integer reviewCount = Integer.parseInt(record.get("hit"));
            String key = record.get("tag");
            String[] abstractWithAffective = key.split("_");
            if (abstractWithAffective.length < 2) {
                continue;
            }
            ReviewTagDO reviewAbstract = new ReviewTagDO();
            reviewAbstract.setId(count);
            reviewAbstract.setName(abstractWithAffective[0].trim());
            reviewAbstract.setAffection(Integer.parseInt(abstractWithAffective[1])); // 不做值的校验
            reviewAbstract.setCount(reviewCount);
            reviewAbstract.setRankType(ReviewFilterType.RANK_ABSTRACT * 100);
            reviewAbstract.setUrl(buildReviewTagUrl(shopReviewCtx, reviewAbstract));
            reviewAbstractList.add(reviewAbstract);
            count++;
        }
        if (CollectionUtils.isNotEmpty(reviewAbstractList)) {
            // 按评价标签的正向含义到负向含义排序 情感：喜欢：1，不喜欢：-1，中立：0
            reviewAbstractList.sort(Comparator.comparing(ReviewTagDO::getAffection).reversed());
            reviewAbstracts.addAll(reviewAbstractList);
        }
    }

    private String buildReviewTagUrl(ShopReviewCtx shopReviewCtx, ReviewTagDO reviewAbstract) {
        if (shopReviewCtx.isMt()) {
            return String.format(MT_REVIEW_TAG_URL, shopReviewCtx.getMtId(), NetUtils.encode(reviewAbstract.getName()), reviewAbstract.getRankType());
        } else if (DztgClientTypeEnum.DIANPING_BAIDUMAP_MINIAPP.equals(shopReviewCtx.getEnvCtx().getDztgClientTypeEnum())) {
            return StringUtils.EMPTY;
        } else {
            return String.format(DP_REVIEW_TAG_URL, getShopUuid(shopReviewCtx), reviewAbstract.getRankType(), NetUtils.encode(reviewAbstract.getName()), reviewAbstract.getAffection());
        }
    }

    private List<ReviewDetailDO> buildMtReviewDOList(ShopReviewCtx shopReviewCtx, MTQueryResult mtQueryResult) {
        if (mtQueryResult == null || CollectionUtils.isEmpty(mtQueryResult.getMtReviewDataList())) {
            return Collections.emptyList();
        }
        Set<Long> userIds = Sets.newHashSet();
        Set<Long> picIds = Sets.newHashSet();
        Set<Long> videoIds = Sets.newHashSet();
        for (MTReviewData mtReviewData : mtQueryResult.getMtReviewDataList()) {
            if (mtReviewData.getUserId() > 0) {
                userIds.add(mtReviewData.getUserId());
            }
            if (CollectionUtils.isNotEmpty(mtReviewData.getReviewPics())) {
                for (ReviewPic reviewPic : mtReviewData.getReviewPics()) {
                    // 过滤掉被删除或屏蔽的照片
                    if (filterPicStatusAndOwner(shopReviewCtx, mtReviewData.getUserId(), reviewPic.getStatus())){
                        continue;
                    }
                    picIds.add(reviewPic.getPicId());
                }
            }
            if (ShopReviewHelper.mtShopReviewNeedVideo()) {
                if (CollectionUtils.isNotEmpty(mtReviewData.getReviewVideoList())) {
                    for (ReviewVideo reviewVideo : mtReviewData.getReviewVideoList()) {
                        // 过滤掉被屏蔽或删除的 视频
                        if (filterPicStatusAndOwner(shopReviewCtx, mtReviewData.getUserId(), reviewVideo.getStatus())){
                            continue;
                        }
                        videoIds.add(reviewVideo.getVideoId());
                    }
                }
            }
        }
        List<ReviewDetailDO> reviewDetailDOList = Lists.newArrayList();
        Future mtReviewPicFuture = reviewWrapper.getMtReviewPicFuture(Lists.newArrayList(picIds));
        Future mtReviewVideoFuture = reviewWrapper.getMtReviewVideoFuture(Lists.newArrayList(videoIds));

        Map<Long, MtUserDto> userModelMap = userWrapper.getUserModelMap(userIds);
        Map<Long, VideoData> mtReviewVideoInfoMap = reviewWrapper.getMtReviewVideoInfoMap(mtReviewVideoFuture);
        Map<Long, MtReviewPicInfo> mtReviewPicInfoMap = reviewWrapper.getMtReviewPicInfoMap(mtReviewPicFuture);

        for (MTReviewData mtReviewData : mtQueryResult.getMtReviewDataList()) {
            List<VideoData> videoDataList = Lists.newArrayList();
            if (MapUtils.isNotEmpty(mtReviewVideoInfoMap)) {
                if (CollectionUtils.isNotEmpty(mtReviewData.getReviewVideoList())) {
                    for (ReviewVideo reviewVideo : mtReviewData.getReviewVideoList()) {
                        if (Objects.nonNull(mtReviewVideoInfoMap.get(reviewVideo.getVideoId()))){
                            videoDataList.add(mtReviewVideoInfoMap.get(reviewVideo.getVideoId()));
                        }
                    }
                }
            }
            List<MtReviewPicInfo> mtReviewPicInfoList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(mtReviewData.getReviewPics())) {
                for (ReviewPic reviewPic : mtReviewData.getReviewPics()) {
                    MtReviewPicInfo mtReviewPicInfo = mtReviewPicInfoMap.get(reviewPic.getPicId());
                    if (mtReviewPicInfo != null) {
                        mtReviewPicInfoList.add(mtReviewPicInfo);
                    }
                }
            }
            MtUserDto userModel = MapUtils.isEmpty(userModelMap) ? null : userModelMap.get(mtReviewData.getUserId());
            List<ReviewPicDTO> defaultReviewPicList = ShopReviewHelper.buildMtReviewPicList(videoDataList, mtReviewPicInfoList);
            ReviewDetailDO reviewDetailDO = ShopReviewHelper.mtReviewDataToReviewDetailDO(mtReviewData, userModel, defaultReviewPicList, shopReviewCtx);
            if (reviewDetailDO != null) {
                // 构造-客单价
                reviewDetailDO.setPrice(buildPrice(mtReviewData.getExpenseList()));
                reviewDetailDOList.add(reviewDetailDO);
            }
        }
        return reviewDetailDOList;
    }

    /**
     * 检测图片、视频状态，过滤掉被屏蔽、删除的照片
     * 视频，图片状态集合，PicVideoStatusEnum
     * 返回值：
     *      true：需要过滤
     *      false：不用过滤
     * @param shopReviewCtx
     * @param userid
     * @param status
     * @return
     */
    private Boolean filterPicStatusAndOwner(ShopReviewCtx shopReviewCtx, Long userid, Integer status){
        if (Objects.isNull(status)){
            return Boolean.TRUE;
        }
        // 可正常展示
        if (PicVideoStatusEnum.NORMAL.code == status.intValue()){
            return Boolean.FALSE;
        }
        // 主态展示，客态不展示
        if (PicVideoStatusEnum.AUDIT.code  == status.intValue() && (Objects.nonNull(userid) && userid.longValue() == shopReviewCtx.getEnvCtx().getMtUserId())){//已确认判断平台后再使用
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }
    private static String buildPrice(List<Expense> expenseInfoList) {
        String price = "";
        if (CollectionUtils.isEmpty(expenseInfoList)) {
            return  price;
        }
        String title = expenseInfoList.get(0).getTitle();
        int avgPrice = expenseInfoList.get(0).getExpense().intValue();
        if ("人均".equals(title) && avgPrice > 0) {
            price = "￥" + avgPrice + "/人";
        } else {
            price = (avgPrice > 0) ? "￥" + avgPrice : StringUtils.EMPTY;
        }
        return price;
    }
    public List<ReviewDetailDO> buildDpReviewDOList(ShopReviewCtx shopReviewCtx, PageModel pageModel, boolean supply) {
        if (pageModel == null || CollectionUtils.isEmpty(pageModel.getRecords())) {
            return Collections.emptyList();
        }
        Set<Long> userIdLSet = Sets.newHashSet();
        List<Long> reviewIdList = Lists.newArrayList();
        List<String> reviewIdStrList = Lists.newArrayList();
        List<Long> anonymousReviewIds = Lists.newArrayList();
        Map<Long, Long> userIdToReviewId = Maps.newHashMap();
        List<ReviewDataV2> reviewDataV2List = (List<ReviewDataV2>) pageModel.getRecords();
        for (ReviewDataV2 reviewData : reviewDataV2List) {
            if (reviewData == null) {
                continue;
            }
            if (reviewData.getReviewBody() != null && reviewData.getReviewBody().length() > SHOP_PAGE_REVIEW_LENGTH) {
                reviewData.setReviewBody(reviewData.getReviewBody().substring(0, SHOP_PAGE_REVIEW_LENGTH - 1));
            }
            if (reviewData.getAnonymous()) {
                anonymousReviewIds.add(reviewData.getReviewIdLong());
                userIdToReviewId.put(reviewData.getUserId(), reviewData.getReviewIdLong());
            }
            userIdLSet.add(reviewData.getUserId());
            reviewIdList.add(reviewData.getReviewIdLong());
            reviewIdStrList.add(String.valueOf(reviewData.getReviewIdLong()));
        }
        if (CollectionUtils.isEmpty(userIdLSet)) {
            return Collections.emptyList();
        }
        List<ReviewDetailDO> reviewDetailDOList = Lists.newArrayList();
        Future anonymousUserInfoFuture = reviewWrapper.getAnonymousUserInfoFuture(anonymousReviewIds);
        Future reviewBrowsCountsFuture = reviewWrapper.getReviewsBrowseCountFutureV2(reviewIdList);
        Future vipFuture = reviewWrapper.getUserVipInfoFuture(userIdLSet);
        Future userFuture = userWrapper.getUserInfos(Lists.newArrayList(userIdLSet));
        Future userGrowthFuture = userWrapper.batchQueryUserGrowth(Lists.newArrayList(userIdLSet));
        //Future goodReviewListFuture = reviewWrapper.findGoodReviewFuture(reviewIdStrList, shopReviewCtx.getDpShopId());//优质点评调搜索

        Map<Long, UserDTO> userDTOMap = userWrapper.getFutureResult(userFuture);
        Map<Long, UserInfoForAppVO> vipUserMap = reviewWrapper.getFutureResult(vipFuture);
        Map<Long, UserManaDTO> userGrowthDTOMap = userWrapper.getFutureResult(userGrowthFuture);
        Map<Long, Integer> reviewViewCounts = reviewWrapper.getFutureResult(reviewBrowsCountsFuture);
        Map<Long, AnonymousUserInfo> anonymousUserMap = reviewWrapper.getFutureResult(anonymousUserInfoFuture);
        //List<Integer> goodReviewList = reviewWrapper.getGoodReview(goodReviewListFuture);

        if (Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.log.enable", false)) {
            int dealGroupId = shopReviewCtx.isMt() ? shopReviewCtx.getMtId() : shopReviewCtx.getDpId();
            logger.info("团单id={},token={};pageModel={};AnonymousUserInfo请求参数={},返回={};UserVipInfo请求参数={},返回={};UserInfos请求参数={},返回={};UserGrowth请求参数={},返回={}",
                    dealGroupId,
                    shopReviewCtx.getToken(),
                    JSON.toJSONString(pageModel), JSON.toJSONString(anonymousReviewIds),JSON.toJSONString(anonymousUserMap),
                    JSON.toJSONString(userIdLSet), JSON.toJSONString(vipUserMap),
                    JSON.toJSONString(userIdLSet), JSON.toJSONString(userDTOMap),
                    JSON.toJSONString(userIdLSet), JSON.toJSONString(userGrowthDTOMap));
        }

        Map<Long, ReviewUserModel> reviewUserModelMap = ShopReviewHelper.buildReviewUserModelList(Lists.newArrayList(userIdLSet), userDTOMap, userGrowthDTOMap, vipUserMap);
        transferAnonymousUserInfo(reviewUserModelMap, userIdToReviewId, anonymousUserMap);

        for (ReviewDataV2 reviewData : reviewDataV2List) {
            ReviewDetailDO reviewDetailDO = ShopReviewHelper.reviewDataToReviewDetailDO(reviewData, reviewUserModelMap, supply);
            if (MapUtils.isNotEmpty(reviewViewCounts) && reviewViewCounts.containsKey(reviewData.getReviewIdLong())) {
                reviewDetailDO.setReviewCount(reviewViewCounts.get(reviewData.getReviewIdLong()));
            }

            reviewDetailDOList.add(reviewDetailDO);
        }
        return reviewDetailDOList;
    }

    private void transferAnonymousUserInfo(Map<Long, ReviewUserModel> reviewUserModelMap, Map<Long, Long> userIdToReviewId, Map<Long, AnonymousUserInfo> anonymousUserMap) {
        if (MapUtils.isEmpty(reviewUserModelMap) || MapUtils.isEmpty(userIdToReviewId)
                || MapUtils.isEmpty(anonymousUserMap)) {
            return;
        }
        for (Map.Entry<Long, ReviewUserModel> reviewUserModelEntry : reviewUserModelMap.entrySet()) {
            if (userIdToReviewId.containsKey(reviewUserModelEntry.getKey())) {
                long reviewId = userIdToReviewId.get(reviewUserModelEntry.getKey());
                AnonymousUserInfo anonymousUserInfo = anonymousUserMap.get(reviewId);
                if (anonymousUserInfo != null) {
                    reviewUserModelEntry.getValue().setDetailUrl(null);
                    reviewUserModelEntry.getValue().setAvatar(anonymousUserInfo.getAvatar());
                    reviewUserModelEntry.getValue().setUserName(anonymousUserInfo.getNickName());
                    if (CollectionUtils.isNotEmpty(anonymousUserInfo.getPendantList())) {
                        for (Pendant pendant : anonymousUserInfo.getPendantList()) {
                            if (pendant.getType() == PendantTypeEnum.LEVEL.getPendantType()) {
                                reviewUserModelEntry.getValue().setUserLevel(pendant.getImgUrl());
                            }
                        }
                    }
                }
            }
        }
    }

    private ShopReviewCtx initShopReviewCtx(UnifiedShopReviewReq request, EnvCtx envCtx, IMobileContext iMobileContext) {
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx(envCtx);

        if (envCtx.isMt()) {
            shopReviewCtx.setMtId(request.getDealGroupId());
            shopReviewCtx.setMtShopId(request.getShopId());
            shopReviewCtx.setMtLongShopId(request.getShopIdLong());
            shopReviewCtx.setMtCityId(request.getCityId());
        } else {
            shopReviewCtx.setDpCityId(request.getCityId());
            shopReviewCtx.setDpId(request.getDealGroupId());
            shopReviewCtx.setDpShopId(request.getShopId());
            shopReviewCtx.setDpLongShopId(request.getShopIdLong());
            String uuid = request.getShopUuid();
            if (StringUtils.isBlank(uuid)) {
                uuid = ShopUuidUtils.getUuidById(request.getShopIdLong());
            }
            shopReviewCtx.setDpShopUuid(uuid);
        }

        shopReviewCtx.setToken(iMobileContext.getHeader().getNewToken());
        return shopReviewCtx;
    }
}
