package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedShopReviewReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.UnifiedShopReviewList;
import com.dianping.mobile.mapi.dztgdetail.facade.UnifiedShopReviewFacade;
import com.dianping.mobile.mapi.dztgdetail.util.AntiCrawlerUtils;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.HttpMethod;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;
import java.util.Objects;

import static com.dianping.mobile.mapi.dztgdetail.util.AntiCrawlerUtils.antiUnauthenticLogin;

/**
 * Created by zuomlin on 2018/12/13.
 */
@InterfaceDoc(displayName = "到综团单详情页统一用户评价接口",
        type = "restful",
        description = "到综团单详情页统一用户评价接口，用户对商户的评价包括消费后、免费试吃、免费吃等点评数据",
        scenarios = "到综团单详情页统一用户评价接口，用户对商户的评价包括消费后、免费试吃、免费吃等点评数据",
        host = "http://mapi.dianping.com/general/platform/dztgdetail/",
        authors = "yangquan02"
)
@Controller("general/platform/dztgdetail/unifiedshopreview.bin")
@Action(url = "unifiedshopreview.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class UnifiedShopReviewAction extends AbsAction<UnifiedShopReviewReq> {

    @Autowired
    private UnifiedShopReviewFacade unifiedShopReviewFacade;

    @MethodDoc(requestMethods = HttpMethod.GET,
            urls = "unifiedshopreview.bin",
            displayName = "到综团单详情页统一评价接口",
            description = "到综团单详情页统一用户评价接口，用户对商户的评价包括消费后、免费试吃、免费吃等点评数据，如果没有点评数据，则后端不返回数据。",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "unifiedshopreview.bin请求参数",
                            type = UnifiedShopReviewReq.class,
                            paramType = ParamType.REQUEST_BODY
                    ),
                    @ParamDoc(
                            name = "context",
                            description = "mapi-shell协议上下文",
                            type = IMobileContext.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            responseParams = {@ParamDoc(name = "result", description = "用户评论", type = UnifiedShopReviewList.class)},
            restExampleUrl = "http://mapi.51ping.com/general/platform/dztgdetail/unifiedshopreview.bin?",
            restExamplePostData = "{}",
            restExampleResponseData = "参考上述restExampleUrl返回json内容",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "公开数据无须鉴权"
                    ),
            }
    )
    @Override
    protected IMobileResponse validate(UnifiedShopReviewReq request, IMobileContext iMobileContext) {
        IdUpgradeUtils.processProductIdForUnifiedShopReviewReq(request, "unifiedshopreview.bin");
        if (request == null || request.getDealGroupId() <= 0) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @Override
    @CryptMethod
    protected IMobileResponse execute(UnifiedShopReviewReq request, IMobileContext iMobileContext) {
        try {
            EnvCtx envCtx = initEnvCtx(iMobileContext);
            // 拦截没有授权的登录
            antiUnauthenticLogin(iMobileContext);
            UnifiedShopReviewList result = unifiedShopReviewFacade.queryUnifiedShopReviewList(request, envCtx, iMobileContext);
            if (result == null || StringUtils.isEmpty(result.getTopTitle())) {
                return Resps.NoDataResp;
            }
            // 反爬信息处理
            hideKeyInfo(result, iMobileContext);
            return new CommonMobileResponse(result);
        } catch (Exception e) {
            logger.error("unifiedshopreview.bin failed, params: request={}, context ={}", request, iMobileContext);
        }
        return Resps.SYSTEM_ERROR;
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return null;
    }

    private void hideKeyInfo(UnifiedShopReviewList result, IMobileContext ctx) {
        if ( Objects.isNull(result)) {
            return;
        }
        if (!AntiCrawlerUtils.hide(ctx)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(result.getReviewDetailList())) {
            result.getReviewDetailList().forEach(review -> {
                review.setPrice(StringUtils.EMPTY);
            });
        }
    }
}
