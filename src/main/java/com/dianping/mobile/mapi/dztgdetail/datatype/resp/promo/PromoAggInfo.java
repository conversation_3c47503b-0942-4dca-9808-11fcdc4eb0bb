package com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

@TypeDoc(description = "详细优惠信息")
@MobileDo(id = 0x7d9f)
@Data
public class PromoAggInfo {

//    @FieldDoc(description = "立减优惠")
//    @MobileDo.MobileField(key = 0x24cc)
    private DeductionPromo deductionPromo;

    @FieldDoc(description = "商家券")
    @MobileDo.MobileField(key = 0x1550)
    private List<CouponDetailItem> merchantCoupons;

    private String platformCouponInstruction;

    @FieldDoc(description = "平台券")
    @MobileDo.MobileField(key = 0x174f)
    private List<CouponDetailItem> platformCoupons;

}