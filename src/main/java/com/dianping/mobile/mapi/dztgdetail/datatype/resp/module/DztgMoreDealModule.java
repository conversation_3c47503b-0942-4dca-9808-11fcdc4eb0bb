package com.dianping.mobile.mapi.dztgdetail.datatype.resp.module;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;

import java.io.Serializable;

@MobileDo(id = 0x15a3)
public class DztgMoreDealModule implements Serializable {
    /**
    *
    */
    @MobileField(key = 0xaa30)
    private int publishCategoryId;

    /**
    *
    */
    @MobileField(key = 0x612b)
    private int buCode;

    public int getPublishCategoryId() {
        return publishCategoryId;
    }

    public void setPublishCategoryId(int publishCategoryId) {
        this.publishCategoryId = publishCategoryId;
    }

    public int getBuCode() {
        return buCode;
    }

    public void setBuCode(int buCode) {
        this.buCode = buCode;
    }
}