package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.enums.BottomBarComponentTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2025/3/16 14:46
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QuickEntranceButtonVO extends ProductBottomBarComponentVO {

    /**
     * 文字
     */
    private String buttonText;

    /**
     * 图片
     */
    private String buttonPic;

    /**
     * 快捷按钮标签
     */
    private TopRightSubscriptVO topRightSubscript;

    /**
     * 点击动作
     */
    private BottomBarActionDataVO actionData;

    @Override
    public int getComponentType() {
        return BottomBarComponentTypeEnum.QUICK_ENTRANCE_BUTTON.getCode();
    }

    @Data
    public static class TopRightSubscriptVO implements Serializable {

        /**
         * 圆圈内部文字
         */
        private String text;

        /**
         * 圆圈图片
         */
        private String backgroundPic;

        /**
         * 角标类型，1=圆圈+数字
         */
        private int type;

    }

}