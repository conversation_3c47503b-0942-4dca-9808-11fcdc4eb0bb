package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.backgroud;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: g<PERSON><PERSON><PERSON>e
 * @Date: 2025/3/16 15:06
 */
@Data
@NoArgsConstructor
public class BottomBarBackgroundVO implements Serializable {

    private int type;

    /**
     * 纯色or渐变色开始色，type=1or3有效
     */
    private List<String> colors;

    /**
     * 边框颜色
     */
    private String borderColor;

    /**
     * 边框宽度
     */
    private int borderWidth;

    /**
     * 图片url，type=2有效
     */
    private String image;
}
