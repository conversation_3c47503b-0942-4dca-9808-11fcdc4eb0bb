package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.deal.sale.api.dto.ProductSaleDto;
import com.dianping.deal.sale.api.enums.SourceEnum;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealStyleWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.UserWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealRelatedBehaviorItem;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealRelatedBehaviorModule;
import com.dianping.mobile.mapi.dztgdetail.entity.DealRelatedBehaviorConfig;
import com.dianping.mobile.mapi.dztgdetail.helper.ActivitiesHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.UserInfoHelper;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.TimeUtils;
import com.dianping.userremote.base.dto.UserDTO;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
public class DealRelatedBehaviorProcessor extends AbsDealProcessor {

    @Resource
    private DealGroupWrapper dealGroupWrapper;
    @Resource
    private UserWrapper userWrapper;
    @Resource
    private DouHuBiz douHuBiz;
    @Resource
    private DealStyleWrapper dealStyleWrapper;

    @Override
    public void prepare(DealCtx ctx) {
        if (userBuyBehaviorSwitch(ctx)) {
            Future relatedBehaviorFuture = dealGroupWrapper.preQueryCompleteProductSale(ctx.getDpId());
            ctx.getFutureCtx().setRelatedBehaviorFuture(relatedBehaviorFuture);
        }
    }

    @Override
    public void process(DealCtx ctx) {
        Future relatedBehaviorFuture = ctx.getFutureCtx().getRelatedBehaviorFuture();
        List<ProductSaleDto> productSales = dealGroupWrapper.queryCompleteProductSale(relatedBehaviorFuture);
        if (CollectionUtils.isEmpty(productSales)) {
            return;
        }

        List<Long> mtUserIds = Lists.newArrayList();
        List<Long> dpUserIds = Lists.newArrayList();
        for (ProductSaleDto productSaleDto : productSales) {
            if (SourceEnum.MtPayOrder.getSource() == productSaleDto.getSource()
                    && productSaleDto.getMtUserId() != 0) {
                mtUserIds.add(productSaleDto.getMtUserId());
            } else if (SourceEnum.DpPayOrder.getSource() == productSaleDto.getSource()
                    && productSaleDto.getDpUserId() != 0) {
                dpUserIds.add(productSaleDto.getDpUserId());
            }
        }
        if (CollectionUtils.isEmpty(dpUserIds) && CollectionUtils.isEmpty(mtUserIds)) {
            return;
        }

        Future dpUserFuture = userWrapper.getUserInfos(dpUserIds);

        Map<Long, UserModel> mtUserModelMap = userWrapper.getMtUserModelByMtIds(mtUserIds);
        Map<Long, UserDTO> dpUserModelMap = userWrapper.getFutureResult(dpUserFuture);
        if (MapUtils.isEmpty(mtUserModelMap) && MapUtils.isEmpty(dpUserModelMap)) {
            return;
        }

        List<DealRelatedBehaviorItem> relatedBehaviorItems = convertProductSale2RelatedBehavior(ctx.isMt(), productSales, mtUserModelMap, dpUserModelMap);

        DealRelatedBehaviorModule relatedBehaviorModule = new DealRelatedBehaviorModule();
        relatedBehaviorModule.setRelatedUserBehaviorItems(relatedBehaviorItems);
        ctx.getResult().setRelatedBehaviorModule(relatedBehaviorModule);
    }

    private List<DealRelatedBehaviorItem> convertProductSale2RelatedBehavior(boolean isMt, List<ProductSaleDto> productSales, Map<Long, UserModel> mtUserModelMap, Map<Long, UserDTO> dpUserModelMap) {
        return productSales.stream().filter(Objects::nonNull)
                .sorted((o1, o2) -> {
                    if (o1.getSuccessTime() == null) {
                        return 1;
                    }
                    if (o2.getSuccessTime() == null) {
                        return -1;
                    }
                    long o1Time = o1.getSuccessTime().getTime();
                    long o2Time = o2.getSuccessTime().getTime();
                    return Long.compare(o2Time, o1Time);
                })
                .map(productSaleDto -> {
                    String userName = null;
                    String userAvatarUrl = null;
                    String userBehaviorDesc = null;
                    if (SourceEnum.MtPayOrder.getSource() == productSaleDto.getSource()) {
                        UserModel mtUserModel = Optional.ofNullable(mtUserModelMap)
                                .map(item -> item.getOrDefault(productSaleDto.getMtUserId(), null)).orElse(null);
                        if (mtUserModel == null) {
                            return null;
                        }
                        userName = mtUserModel.getUsername();
                        userAvatarUrl = mtUserModel.getAvatarUrl();
                    } else if (SourceEnum.DpPayOrder.getSource() == productSaleDto.getSource()
                            && productSaleDto.getDpUserId() != 0) {
                        UserDTO dpUserModel = Optional.ofNullable(dpUserModelMap)
                                .map(item -> item.getOrDefault(productSaleDto.getDpUserId(), null)).orElse(null);
                        if (dpUserModel == null) {
                            return null;
                        }
                        userName = dpUserModel.getUserNickName();
                        userAvatarUrl = dpUserModel.getUserFace();
                    }
                    if (productSaleDto.getSuccessTime() != null) {
                        String hourDesc = TimeUtils.getHourDateString(productSaleDto.getSuccessTime());
                        if (hourDesc != null) {
                            userBehaviorDesc = hourDesc + "下单了";
                        }
                    }
                    if (StringUtils.isBlank(userAvatarUrl)) {
                        userAvatarUrl = UserInfoHelper.getDefaultAvatarUrl(isMt, userName);
                    }
                    DealRelatedBehaviorItem relatedBehaviorItem = new DealRelatedBehaviorItem();
                    relatedBehaviorItem.setUserName(userNameDesensitize(userName));
                    relatedBehaviorItem.setUserAvatarUrl(userAvatarUrl);
                    relatedBehaviorItem.setUserBehaviorDesc(userBehaviorDesc);
                    return relatedBehaviorItem;
                })
                .filter(relatedBehaviorItem -> relatedBehaviorItem != null && relatedBehaviorItem.getUserName() != null && relatedBehaviorItem.getUserBehaviorDesc() != null)
                .collect(Collectors.toList());
    }

    private String userNameDesensitize(String userName) {
        if (StringUtils.isBlank(userName)) {
            return null;
        }
        if (userName.length() <= 2) {
            return userName;
        }
        try {
            return userName.replaceAll("(?<=.{1}).*(?=.{1})", "*");
        } catch (Exception e) {
            logger.error("DealRelatedBehaviorProcessor.userNameDesensitize, userName: {}", userName, e);
        }
        return null;
    }

    private boolean userBuyBehaviorSwitch(DealCtx ctx) {
        // 用户享受渠道专供的情况下，或参加巨省钱活动团单
        if (dealStyleWrapper.isImmersiveHeaderImage(ctx.getRequestSource()) || ActivitiesHelper.isJuShengQianDeal(ctx.getDealActivities())) {
            return true;
        }
        // 在线教育按照四级分类做开关
        if (LionConfigUtils.isEduOnlineDeal(ctx.getCategoryId(), getServiceTypeId(ctx.getDealGroupDTO()))) {
            return true;
        }
        List<Integer> dealCategories = Lion.getList(LionConstants.APP_KEY,
                LionConstants.RELATED_BEHAVIOR_CATEGORIES, Integer.class, Collections.emptyList());
        return dealCategories.contains(ctx.getCategoryId());
    }

    private Long getServiceTypeId(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO == null || dealGroupDTO.getCategory() == null) {
            return null;
        }
        return dealGroupDTO.getCategory().getServiceTypeId();
    }

}
