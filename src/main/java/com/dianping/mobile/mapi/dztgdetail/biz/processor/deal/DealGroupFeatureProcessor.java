package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.base.dto.DealReceiptDTO;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealFeature;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PopupExtVO;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.TimeUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.ReceiptEffectiveDateDTO;
import com.sankuai.general.product.query.center.client.util.DealGroupUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @date 2022/12/29
 */
public class DealGroupFeatureProcessor extends AbsDealProcessor {

    @Resource
    private DealGroupWrapper dealGroupWrapper;

    @Override
    public void prepare(DealCtx ctx) {
        if (!DealAttrHelper.isPreSale(ctx.getAttrs()) && !DealAttrHelper.isWarmUpDeal(ctx.getAttrs())) {
            return;
        }
        if(GreyUtils.enableQueryCenterForMainApi(ctx)) {
            return;
        }
        Future dealReceiptFuture = dealGroupWrapper.preQueryDealGroupReceipt(ctx.getDpId(), ctx.getDealGroupBase().getEndDate());
        ctx.getFutureCtx().setDealReceiptFuture(dealReceiptFuture);
    }

    @Override
    public void process(DealCtx ctx) {
        if(GreyUtils.enableQueryCenterForMainApi(ctx)) {
            processByQueryCenter(ctx);
        } else {
            if (!DealAttrHelper.isPreSale(ctx.getAttrs()) || ctx.getFutureCtx().getDealReceiptFuture() == null) {
                return;
            }
            DealReceiptDTO dealReceiptDTO = dealGroupWrapper.queryDealGroupReceipt(ctx.getFutureCtx().getDealReceiptFuture());
            if (DealAttrHelper.isPreSale(ctx.getAttrs()) && dealReceiptDTO != null && dealReceiptDTO.getShowText() != null) {
//                String showText = getPreSaleReceiptShowTest(dealReceiptDTO, ctx.getDealGroupBase());
                String saleText = getPreSaleReceiptSaleTest(dealReceiptDTO, ctx.getDealGroupBase());
                String useText = getPreSaleReceiptUseTest(dealReceiptDTO, ctx.getDealGroupBase());
                if (saleText == null || useText == null) {
                    return;
                }
                DealFeature dealFeature = new DealFeature();
                dealFeature.setType("PreSaleAvailableDate");
                dealFeature.setFeatures(Lists.newArrayList(saleText,useText));
                if (ctx.getResult().getDealFeatures() == null) {
                    ctx.getResult().setDealFeatures(Lists.newArrayList());
                }
                ctx.getResult().getDealFeatures().add(dealFeature);
            }
        }
    }

    private String getPreSaleReceiptShowTest(DealReceiptDTO dealReceiptDTO, DealGroupBaseDTO dealGroupBaseDTO) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealGroupFeatureProcessor.getPreSaleReceiptShowTest(com.dianping.deal.base.dto.DealReceiptDTO,com.dianping.deal.base.dto.DealGroupBaseDTO)");
        String beginDate;
        String endDate;
        if (dealReceiptDTO.getReceiptDateType() == 1) {
            beginDate = TimeUtils.convertDate2DayDotString(dealGroupBaseDTO.getEndDate());
            endDate = TimeUtils.convertDate2DayDotString(TimeUtils.getFewDaysLaterBeginTime(dealGroupBaseDTO.getEndDate(), dealReceiptDTO.getReceiptValidDays()));
        } else {
            beginDate = TimeUtils.convertDate2DayDotString(dealReceiptDTO.getReceiptBeginDate());
            endDate = TimeUtils.convertDate2DayDotString(dealReceiptDTO.getReceiptEndDate());
        }
        return beginDate == null || endDate == null ? null : String.format("超值预售 %s-%s可用", beginDate, endDate);
    }

    private String getPreSaleReceiptUseTest(DealReceiptDTO dealReceiptDTO, DealGroupBaseDTO dealGroupBaseDTO) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealGroupFeatureProcessor.getPreSaleReceiptUseTest(com.dianping.deal.base.dto.DealReceiptDTO,com.dianping.deal.base.dto.DealGroupBaseDTO)");
        String beginDate;
        if (dealReceiptDTO.getReceiptDateType() == 1) {
            beginDate = TimeUtils.convertDate2DayDotString(dealGroupBaseDTO.getEndDate());
        } else {
            beginDate = TimeUtils.convertDate2DayDotString(dealReceiptDTO.getReceiptBeginDate());
        }
        return beginDate == null  ? null : String.format("可用时段：%s后可用", beginDate);
    }

    private String getPreSaleReceiptSaleTest(DealReceiptDTO dealReceiptDTO, DealGroupBaseDTO dealGroupBaseDTO) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealGroupFeatureProcessor.getPreSaleReceiptSaleTest(com.dianping.deal.base.dto.DealReceiptDTO,com.dianping.deal.base.dto.DealGroupBaseDTO)");
        String beginDate = TimeUtils.convertDate2DayDotString(dealGroupBaseDTO.getBeginDate());
        String endDate = TimeUtils.convertDate2DayDotString(dealGroupBaseDTO.getEndDate());
        return beginDate == null || endDate == null ? null : String.format("预售时段：%s-%s", beginDate, endDate);
    }

    private void processByQueryCenter(DealCtx ctx) {
        if (!DealAttrHelper.isPreSale(ctx.getAttrs())
                && !DealAttrHelper.isWarmUpDeal(ctx.getAttrs())
                && !DealAttrHelper.isWuyoutong(ctx)) {
            return;
        }
        ReceiptEffectiveDateDTO dealReceiptDTO = getReceipt(ctx.getDealGroupDTO());
        if (DealAttrHelper.isPreSale(ctx.getAttrs()) && dealReceiptDTO != null && dealReceiptDTO.getShowText() != null) {
//            String showText = getPreSaleReceiptShowTest(dealReceiptDTO, ctx.getDealGroupBase());
            String saleText = getPreSaleReceiptSaleText(dealReceiptDTO, ctx.getDealGroupBase());
            String useText = getPreSaleReceiptUseTest(dealReceiptDTO, ctx.getDealGroupBase());
            if (saleText == null || useText == null) {
                return;
            }
            DealFeature dealFeature = new DealFeature();
            dealFeature.setType("PreSaleAvailableDate");
            dealFeature.setFeatures(Lists.newArrayList(saleText,useText));
            if (ctx.getResult().getDealFeatures() == null) {
                ctx.getResult().setDealFeatures(Lists.newArrayList());
            }
            ctx.getResult().getDealFeatures().add(dealFeature);
        }

        if(DealAttrHelper.isWarmUpDeal(ctx.getAttrs()) && dealReceiptDTO != null && dealReceiptDTO.getShowText() != null){
            String useText = getWarmUpReceiptUseText(dealReceiptDTO);
            if (useText == null) {
                return;
            }
            DealFeature dealFeature = new DealFeature();
            dealFeature.setType("WarmUpAvailableDate");
            dealFeature.setFeatures(Lists.newArrayList(useText));
            if (ctx.getResult().getDealFeatures() == null) {
                ctx.getResult().setDealFeatures(Lists.newArrayList());
            }
            ctx.getResult().getDealFeatures().add(dealFeature);
        }
    }

    private String getWarmUpReceiptUseText(ReceiptEffectiveDateDTO dealReceiptDTO) {
        String beginDate = TimeUtils.convertDate2DayDotString(DealGroupUtils.convertString2Date(dealReceiptDTO.getReceiptBeginDate()));
        String endDate = TimeUtils.convertDate2DayDotString(DealGroupUtils.convertString2Date(dealReceiptDTO.getReceiptEndDate()));

        return (beginDate == null && endDate == null) ? null : String.format("%s至%s可用", beginDate, endDate);
    }

    private ReceiptEffectiveDateDTO getReceipt(DealGroupDTO dealGroupDTO) {
        if(dealGroupDTO == null || dealGroupDTO.getRule() == null || dealGroupDTO.getRule().getUseRule() == null) {
            return null;
        }
        DealGroupUseRuleDTO useRuleDTO = dealGroupDTO.getRule().getUseRule();
        return useRuleDTO.getReceiptEffectiveDate();
    }

    private String getPreSaleReceiptShowTest(ReceiptEffectiveDateDTO dealReceiptDTO, DealGroupBaseDTO dealGroupBaseDTO) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealGroupFeatureProcessor.getPreSaleReceiptShowTest(ReceiptEffectiveDateDTO,DealGroupBaseDTO)");
        String beginDate;
        String endDate;
        if (dealReceiptDTO.getReceiptDateType() == 1) {
            beginDate = TimeUtils.convertDate2DayDotString(dealGroupBaseDTO.getEndDate());
            endDate = TimeUtils.convertDate2DayDotString(TimeUtils.getFewDaysLaterBeginTime(dealGroupBaseDTO.getEndDate(), dealReceiptDTO.getReceiptValidDays()));
        } else {
            beginDate = TimeUtils.convertDate2DayDotString(DealGroupUtils.convertString2Date(dealReceiptDTO.getReceiptBeginDate()));
            endDate = TimeUtils.convertDate2DayDotString(DealGroupUtils.convertString2Date(dealReceiptDTO.getReceiptEndDate()));
        }
        return beginDate == null || endDate == null ? null : String.format("超值预售 %s-%s可用", beginDate, endDate);
    }

    private String getPreSaleReceiptUseTest(ReceiptEffectiveDateDTO dealReceiptDTO, DealGroupBaseDTO dealGroupBaseDTO) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealGroupFeatureProcessor.getPreSaleReceiptUseTest(ReceiptEffectiveDateDTO,DealGroupBaseDTO)");
        String beginDate;
        if (dealReceiptDTO.getReceiptDateType() == 1) {
            beginDate = TimeUtils.convertDate2MinuteDotString(dealGroupBaseDTO.getEndDate());
        } else {
            beginDate = TimeUtils.convertDate2MinuteDotString(DealGroupUtils.convertString2Date(dealReceiptDTO.getReceiptBeginDate()));
        }
        return beginDate == null  ? null : String.format("可用时段：%s后可用", beginDate);
    }

    private String getPreSaleReceiptSaleText(ReceiptEffectiveDateDTO dealReceiptDTO, DealGroupBaseDTO dealGroupBaseDTO) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealGroupFeatureProcessor.getPreSaleReceiptSaleText(ReceiptEffectiveDateDTO,DealGroupBaseDTO)");
        String beginDate = TimeUtils.convertDate2DayDotString(dealGroupBaseDTO.getBeginDate());
        String endDate = TimeUtils.convertDate2DayDotString(dealGroupBaseDTO.getEndDate());
        return beginDate == null || endDate == null ? null : String.format("预售时段：%s-%s", beginDate, endDate);
    }

}
