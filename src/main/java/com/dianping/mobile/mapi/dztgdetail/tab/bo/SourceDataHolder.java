package com.dianping.mobile.mapi.dztgdetail.tab.bo;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.sales.common.datatype.ProductParam;
import com.dianping.deal.sales.common.datatype.SalesDisplayDTO;

import lombok.Data;

@Data
public class SourceDataHolder {

    private List<Future> dealBaseFutures;

    private Map<Integer, DealGroupBaseDTO> dealBaseMap;

    private List<Future> dealSaleFutures;

    private Map<ProductParam, SalesDisplayDTO> dealSaleMap;

}
