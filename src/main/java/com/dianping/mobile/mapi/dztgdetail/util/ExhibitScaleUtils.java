package com.dianping.mobile.mapi.dztgdetail.util;

/**
 * @Author: z<PERSON><PERSON><EMAIL>
 * @Date: 2024/8/13
 */
public class ExhibitScaleUtils {
    /**
     * 根据图片宽高，计算出最适合该图片展示的尺寸
     */
    public static String calculateAspectRatio1(int width, int height) {
        // 常见的宽高比
        double[] commonAspectRatios = {1.0, 4.0 / 3, 3.0 / 4, 16.0 / 9, 9.0 / 16};
        String[] commonAspectRatioNames = {"1:1", "4:3", "3:4", "16:9", "9:16"};
        return calculateAspectRatio(width, height, commonAspectRatios, commonAspectRatioNames);
    }

    public static String calculateAspectRatio2(int width, int height) {
        // 常见的宽高比
        double[] commonAspectRatios = {1.0, 4.0 / 3, 3.0 / 4};
        String[] commonAspectRatioNames = {"1:1", "4:3", "3:4"};
        return calculateAspectRatio(width, height, commonAspectRatios, commonAspectRatioNames);
    }

    /**
     * 根据图片宽高，计算出最适合该图片展示的尺寸
     */
    private static String calculateAspectRatio(int width, int height, double[] commonAspectRatios,
                                        String[] commonAspectRatioNames) {
        if (height == 0) {
            return "";
        }
        double ratio = (double) width / height;
        double minDifference = Double.MAX_VALUE;
        int bestAspectRatioIndex = -1;

        for (int i = 0; i < commonAspectRatios.length; i++) {
            double difference = Math.abs(ratio - commonAspectRatios[i]);
            if (difference < minDifference) {
                minDifference = difference;
                bestAspectRatioIndex = i;
            }
        }
        // 返回最合适展示的宽高比名称
        return commonAspectRatioNames[bestAspectRatioIndex];
    }
}
