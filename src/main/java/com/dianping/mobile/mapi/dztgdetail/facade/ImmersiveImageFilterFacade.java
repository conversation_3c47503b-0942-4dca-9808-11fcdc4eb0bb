package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.deal.detail.enums.PlatformEnum;
import com.dianping.mobile.mapi.dztgdetail.biz.immersiveimage.ImmersiveImageService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageFilterRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageFilterVO;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/10/24
 */
@Component
public class ImmersiveImageFilterFacade {

    @Autowired
    private List<ImmersiveImageService> immersiveImageServices;

    @Autowired
    private DealGroupWrapper dealGroupWrapper;

    /**
     * 获取沉浸页款式筛选项信息
     *
     * @param request 请求体
     * @return 沉浸页款式筛选项信息
     */
    public ImmersiveImageFilterVO getImmersiveImageFilter(GetImmersiveImageFilterRequest request, EnvCtx envCtx) {
        //参数校验
        checkRequest(request);
        request.setDealGroupType(envCtx.isMt() ? PlatformEnum.MEI_TUAN.getType() : PlatformEnum.DIAN_PING.getType());
        int categoryId = dealGroupWrapper.getCategoryId(request.getDealGroupId());
        request.setCategoryId(categoryId);
        for (ImmersiveImageService immersiveImageService : immersiveImageServices) {
            if (immersiveImageService.getCategoryIds().contains(categoryId)) {
                return immersiveImageService.getImmersiveImageFilter(request);
            }
        }
        return null;
    }

    private void checkRequest(GetImmersiveImageFilterRequest request) {
        Validate.isTrue(Objects.nonNull(request.getDealGroupId()) && request.getDealGroupId() > 0);
        Validate.isTrue(Objects.nonNull(request.getShopId()) && request.getShopId() > 0);
    }
}
