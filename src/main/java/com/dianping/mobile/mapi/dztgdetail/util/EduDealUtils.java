package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.DealCategoryEnum;
import com.dianping.mobile.mapi.dztgdetail.common.constants.EduConstant;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PromoDetailModule;
import com.google.common.collect.Sets;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.beautycontent.creator.application.edu.technicianVideo.dto.EduTechnicianVideoListForCDTO;
import com.sankuai.clr.content.process.thrift.dto.leads.resp.BatchQueryLeadsInfoRespDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import com.sankuai.leads.count.thrift.dto.resp.QueryLeadsSalesRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class EduDealUtils {

    public static final int SALE_COUNT_TYPE_360 = 360;

    public static final String ATTR_QUANTITY = "quantity";
    public static final String ATTR_COUNT = "count";
    public static final String ATTR_EDU_COURSE_TYPE = "eduCourseType";
    public static final String ATTR_CLASS_DURATION = "classDuration";
    public static final String ATTR_BOOKING_INFO = "bookingInfo";
    public static final String SHORT_CLASS = "短期课";
    public static final String YES = "是";

    public static final Pattern EXTRACT_NUMBER_PATTERN = Pattern.compile("(\\d+)");
    public static final HashSet<String> NOT_SHOW_SHORT_CLASS_SERVICE_TYPE = Sets.newHashSet("绘画培训", "游泳培训", "滑雪");

    public static Long getServiceTypeId(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO == null || dealGroupDTO.getCategory() == null) {
            return null;
        }
        return dealGroupDTO.getCategory().getServiceTypeId();
    }

    public static int getLegalVideoNum(DealCtx context) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.util.EduDealUtils.getLegalVideoNum(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        if (context == null || context.getFutureCtx().getEduTechnicianVideoListFuture() == null) {
            return 0;
        }
        RemoteResponse<EduTechnicianVideoListForCDTO> videoList;
        try {
            videoList = context.getFutureCtx().getEduTechnicianVideoListFuture().get(100, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("EduDealUtils.hasLegalVideo error!", e);
            return 0;
        }
        if (videoList == null || videoList.getData() == null || CollectionUtils.isEmpty(videoList.getData().getVideos())) {
            return 0;
        }
        return (int) videoList.getData().getVideos().stream().filter(video -> video.getStatus() == 0).count();
    }

    // 职业教育:0元规划
    public static boolean isVocationalEduPlan(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO == null) {
            return false;
        }
        return EduConstant.SERVICE_ZERO_PLAN_TYPE_ID_LIST.stream().anyMatch(num -> num.equals(getServiceTypeId(dealGroupDTO)));
    }

    // 职业教育:寄宿
    public static boolean isVocationalEduDorm(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO == null) {
            return false;
        }
        return EduConstant.SERVICE_DORM_TYPE_ID_LIST.stream().anyMatch(num -> num.equals(getServiceTypeId(dealGroupDTO)));
    }

    // 职业教育：正价课 or 集训营
    public static boolean isVocationalEduCourseOrCamp(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO == null) {
            return false;
        }
        return EduConstant.SERVICE_COURSE_OR_CAMP_TYPE_ID_LIST.stream().anyMatch(num -> num.equals(getServiceTypeId(dealGroupDTO)));
    }

    public static boolean isCivilExam(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO == null) {
            return false;
        }
        return EduConstant.EXAM_CIVIL_SERVICE_TYPE_ID_LIST.stream().anyMatch(num -> num.equals(getServiceTypeId(dealGroupDTO)));
    }

    public static boolean isVocationTraining(DealCtx context) {
        return DealCategoryEnum.EDU_1205.getDealCategoryId().equals(context.getDealGroupDTO().getCategory().getCategoryId());
    }

    public static boolean isEduOnlineCourseDeal(DealCtx ctx) {
        if (ctx == null) {
            return false;
        }
        return LionConfigUtils.isEduOnlineDeal(ctx.getCategoryId(), getServiceTypeId(ctx.getDealGroupDTO()));
    }

    public static boolean isEduOnlineCourseDeal(int categoryId, DealGroupDTO dealGroupDTO) {
        return LionConfigUtils.isEduOnlineDeal(categoryId, getServiceTypeId(dealGroupDTO));
    }

    public static boolean isVocationalEduCourse(DealCtx ctx) {
        if (ctx == null) {
            return false;
        }
        return isVocationalEduCourseOrCamp(ctx.getDealGroupDTO()) || isVocationalEduDorm(ctx.getDealGroupDTO()) || isVocationalEduPlan(ctx.getDealGroupDTO());
    }

    public static int getTrialPeopleNum(DealCtx context) {
        if (context == null || context.getFutureCtx().getQueryLeadsSalesRespDTOFuture() == null) {
            return 0;
        }
        QueryLeadsSalesRespDTO queryData;
        try {
            queryData = context.getFutureCtx().getQueryLeadsSalesRespDTOFuture().get(500, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("EduDealUtils.getTrialPeopleNum error!", e);
            return 0;
        }
        if (queryData == null || queryData.getSalesMap() == null || queryData.getSalesMap().isEmpty()) {
            return 0;
        }

        Map<Integer, Integer> cycleSales = queryData.getSalesMap().values().stream().filter(Objects::nonNull).findFirst().get().getCycleSales();
        if (cycleSales == null || !cycleSales.containsKey(SALE_COUNT_TYPE_360)) {
            return 0;
        }
        return cycleSales.get(SALE_COUNT_TYPE_360);
    }

    /**
     * 课时数
     */
    public static int getShortClassFreeTrailNum(DealCtx ctx) {
        return getClassNumFromSkus(ctx, true);
    }

    /**
     * 预约试听课时数
     */
    public static int getClassNumFromSkus(DealCtx ctx, boolean justShortClass) {
        if (ctx == null || ctx.getDealGroupDTO() == null || ctx.getDealGroupDTO().getServiceProject() == null
                || CollectionUtils.isEmpty(ctx.getDealGroupDTO().getServiceProject().getMustGroups())) {
            return 0;
        }
        int num = 0; // 课时数
        boolean isShortClass = false;
        for (MustServiceProjectGroupDTO mustGroup : ctx.getDealGroupDTO().getServiceProject().getMustGroups()) {
            if (mustGroup == null || CollectionUtils.isEmpty(mustGroup.getGroups())) {
                continue;
            }
            for (ServiceProjectDTO group : mustGroup.getGroups()) {
                if (group == null || CollectionUtils.isEmpty(group.getAttrs())) {
                    continue;
                }
                for (ServiceProjectAttrDTO attr : group.getAttrs()) {
                    if (attr != null && ATTR_QUANTITY.equals(attr.getAttrName()) && StringUtils.isNotEmpty(attr.getAttrValue())) {
                        num += extractNumber(attr.getAttrValue());
                    }
                    if (attr != null && ATTR_COUNT.equals(attr.getAttrName()) && StringUtils.isNotEmpty(attr.getAttrValue())) {
                        num += extractNumber(attr.getAttrValue());
                    }
                    if (attr != null && ATTR_EDU_COURSE_TYPE.equals(attr.getAttrName()) && SHORT_CLASS.equals(attr.getAttrValue())) {
                        isShortClass = true;
                    }
                }
            }
        }
        if (justShortClass) {
            return isShortClass ? num : 0;
        }
        return num;
    }


    /**
     * 是否为短期课
     */
    public static boolean isShortClass(DealCtx ctx) {
        if (ctx == null || ctx.getDealGroupDTO() == null || ctx.getDealGroupDTO().getServiceProject() == null
                || CollectionUtils.isEmpty(ctx.getDealGroupDTO().getServiceProject().getMustGroups())) {
            return false;
        }
        // 排除不需要的serviceType类型团单
        if (notNeedShortClassAsServiceType(ctx)) {
            return false;
        }
        for (MustServiceProjectGroupDTO mustGroup : ctx.getDealGroupDTO().getServiceProject().getMustGroups()) {
            if (mustGroup == null || CollectionUtils.isEmpty(mustGroup.getGroups())) {
                return false;
            }
            for (ServiceProjectDTO group : mustGroup.getGroups()) {
                if (group == null || CollectionUtils.isEmpty(group.getAttrs())) {
                    return false;
                }
                for (ServiceProjectAttrDTO attr : group.getAttrs()) {
                    if (attr != null && ATTR_EDU_COURSE_TYPE.equals(attr.getAttrName()) && SHORT_CLASS.equals(attr.getAttrValue())) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private static boolean notNeedShortClassAsServiceType(DealCtx ctx) {
        String serviceType = DealUtils.getServiceType(ctx);
        if (StringUtils.isBlank(serviceType)) {
            return false;
        }
        if (serviceType.indexOf("-") > 0) {
            serviceType = serviceType.substring(0, serviceType.indexOf('-'));
        }
        return NOT_SHOW_SHORT_CLASS_SERVICE_TYPE.contains(serviceType);
    }

    public static String getClassDuration(DealCtx ctx) {
        if (ctx == null || ctx.getDealGroupDTO() == null || ctx.getDealGroupDTO().getServiceProject() == null
                || CollectionUtils.isEmpty(ctx.getDealGroupDTO().getServiceProject().getMustGroups())) {
            return null;
        }
        for (MustServiceProjectGroupDTO mustGroup : ctx.getDealGroupDTO().getServiceProject().getMustGroups()) {
            if (mustGroup == null || CollectionUtils.isEmpty(mustGroup.getGroups())) {
                return null;
            }
            for (ServiceProjectDTO group : mustGroup.getGroups()) {
                if (group == null || CollectionUtils.isEmpty(group.getAttrs())) {
                    return null;
                }
                for (ServiceProjectAttrDTO attr : group.getAttrs()) {
                    if (attr != null && ATTR_CLASS_DURATION.equals(attr.getAttrName())) {
                        return attr.getAttrValue();
                    }
                }
            }
        }
        return null;
    }

    /**
     * 短期课：预判断是否有预约试听按钮
     */
    public static boolean preCheckShortClassHasButton(DealCtx ctx) {
        if (ctx == null || ctx.getDealGroupDTO() == null || ctx.getDealGroupDTO().getServiceProject() == null) {
            return false;
        }
        List<MustServiceProjectGroupDTO> mustGroups = ctx.getDealGroupDTO().getServiceProject().getMustGroups();
        if (CollectionUtils.isEmpty(mustGroups) || mustGroups.size() != 1) {
            return false;
        }

        MustServiceProjectGroupDTO mustGroup = mustGroups.get(0);
        if (mustGroup == null || CollectionUtils.isEmpty(mustGroup.getGroups()) || mustGroup.getGroups().size() != 1) {
            return false;
        }
        ServiceProjectDTO group = mustGroup.getGroups().get(0);
        if (group == null || CollectionUtils.isEmpty(group.getAttrs())) {
            return false;
        }
        boolean hasBook = false;
        boolean isShortClass = false;
        for (ServiceProjectAttrDTO attr : group.getAttrs()) {
            if (attr != null && ATTR_EDU_COURSE_TYPE.equals(attr.getAttrName()) && SHORT_CLASS.equals(attr.getAttrValue())) {
                isShortClass = true;
            }
            if (attr != null && ATTR_BOOKING_INFO.equals(attr.getAttrName()) && YES.equals(attr.getAttrValue())) {
                hasBook = true;
            }
        }
        return hasBook && isShortClass;
    }

    public static boolean hasFreeTrialButton(DealCtx dealCtx) {
        return isEduOnlineCourseDeal(dealCtx) || isVocationalEduCourse(dealCtx) || isShortClass(dealCtx);
    }

    /**
     * 判断是否有预约试听按钮（需要满足 1.能预约试听，2.一个月内未预约过）
     *
     * @param dealCtx
     * @return
     */
    public static boolean checkShortClassHasButton(DealCtx dealCtx) {
        BatchQueryLeadsInfoRespDTO batchQueryLeadsInfoRespDTO;
        if (dealCtx == null || dealCtx.getFutureCtx().getQueryLeadsInfoRespDTOFuture() == null) {
            return false;
        }
        try {
            batchQueryLeadsInfoRespDTO = dealCtx.getFutureCtx().getQueryLeadsInfoRespDTOFuture().get(200L, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("EduDealUtils.qualifyShortClassFreeTrail error!", e);
            return false;
        }
        if (batchQueryLeadsInfoRespDTO == null || CollectionUtils.isEmpty(batchQueryLeadsInfoRespDTO.getLeadsInfoDTOs()) || batchQueryLeadsInfoRespDTO.getLeadsInfoDTOs().get(0) == null) {
            return false;
        }
        return batchQueryLeadsInfoRespDTO.getLeadsInfoDTOs().get(0).getLeadsAllowable();
    }

    public static void modifyDiscountRateStr(DealCtx dealctx, PromoDetailModule promoDetailModule, BigDecimal marketPrice, BigDecimal finalPrice) {
        if (marketPrice == null || finalPrice == null) {
            return;
        }
        if (marketPrice.compareTo(finalPrice) == 0 && isShortClass(dealctx)) {
            promoDetailModule.setMarketPrice(null);
            promoDetailModule.setMarketPricePromo(null);
        }
        if (!isShowUnitPrice(dealctx)) {
            return;
        }
        int times = getClassNumFromSkus(dealctx, false);
        if (times == 0) {
            return;
        }
        StringBuilder discountRateStr = new StringBuilder(promoDetailModule.getMarketPromoDiscount() == null ? "" : promoDetailModule.getMarketPromoDiscount());
        if (StringUtils.isNotEmpty(promoDetailModule.getMarketPromoDiscount())) {
            discountRateStr.append("｜");
        }
        discountRateStr.append(finalPrice.divide(new BigDecimal(times), 1, RoundingMode.FLOOR) + "/" + "节");
        promoDetailModule.setMarketPromoDiscount(discountRateStr.toString());
    }

    private static boolean isShowUnitPrice(DealCtx dealctx) {
        return isShortClass(dealctx) || isNightSchoolDeal(dealctx);
    }

    public static boolean isNightSchoolDeal(DealCtx dealctx) {
        return DealCategoryEnum.EDU_1226.getDealCategoryId().equals(Long.valueOf(dealctx.getCategoryId()));
    }

    public static int extractNumber(String input) {
        if (StringUtils.isEmpty(input)) {
            return 0;
        }
        Matcher matcher = EXTRACT_NUMBER_PATTERN.matcher(input);
        if (matcher.find()) {
            return Integer.parseInt(matcher.group(1));
        }
        return 0;
    }
}
