package com.dianping.mobile.mapi.dztgdetail.biz.processor.imagetext.strategy;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.HaimaWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ImageTextStrategyEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ImageTextDetailPBO;
import com.dianping.mobile.mapi.dztgdetail.entity.CleaningProductLionConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.ImageTextStrategyRule;
import com.dianping.mobile.mapi.dztgdetail.entity.LifeClearHaiMaConfig;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-12-11
 * @desc 图文详情处理器
 */
@Service
public class ImageTextDetailHandler {

    @Resource(name = "imageTextDetailStrategyFactory")
    private ImageTextDetailStrategyFactory factory;

    @Resource
    private HaimaWrapper haimaWrapper;

    public ImageTextDetailPBO buildImageTextDetail(DealGroupDTO dealGroupDTO, List<ContentPBO> contents, EnvCtx envCtx, String pageSource) {
        if (Objects.isNull(dealGroupDTO) || CollectionUtils.isEmpty(contents)) {
            return new ImageTextDetailPBO();
        }
        List<ImageTextStrategyRule> rules = getSortStrategyRules();
        // 团单二级类目
        int categoryId = Optional.ofNullable(dealGroupDTO.getCategory())
                .map(DealGroupCategoryDTO::getCategoryId)
                .map(Long::intValue)
                .orElse(0);
        String serviceType = Optional.ofNullable(dealGroupDTO.getCategory())
                .map(cate -> String.format("%s_%s", cate.getServiceTypeId(), cate.getServiceType()))
                .orElse("");
        long dpDealGroupId = Optional.ofNullable(dealGroupDTO.getDpDealGroupId()).orElse(0L);
        ImageTextDetailStrategy defaultStrategy = factory.getImageTextDetailStrategy(ImageTextStrategyEnum.DEFAULT.getStrategyName());
        ImageTextStrategyRule matchedRule = findMatchingRule(rules, pageSource, categoryId, serviceType, dpDealGroupId);
        ImageTextDetailStrategy matchedStrategy = findMatchingStrategy(matchedRule);

        int threshold = Optional.ofNullable(matchedRule)
                .map(ImageTextStrategyRule::getThreshold)
                .orElse(2);
        return Optional.ofNullable(matchedStrategy)
                .orElse(defaultStrategy)
                .buildImageTextDetail(dealGroupDTO, contents, threshold, envCtx);
    }

    private List<ImageTextStrategyRule> getSortStrategyRules() {
        List<ImageTextStrategyRule> rules = LionConfigUtils.getImageTextStrategyRules();
        // 兼容生活服务类目，从海马读出不展示图文详情的点评团单ID列表
        ImageTextStrategyRule lifeCleaningStrategyRule = buildLifeCleaningStrategyRule();
        if (Objects.nonNull(lifeCleaningStrategyRule)) {
            rules.add(lifeCleaningStrategyRule);
        }
        // 先按优先级排序
        rules.sort(Comparator.comparingInt(ImageTextStrategyRule::getPriority).reversed());
        return rules;
    }

    private ImageTextStrategyRule findMatchingRule(List<ImageTextStrategyRule> rules, String pageSource, int categoryId, String serviceType, long dpDealGroupId) {
        return rules.stream()
                .filter(rule -> matchesRule(rule, pageSource, categoryId, serviceType, dpDealGroupId))
                .findFirst()
                .orElse(null);
    }

    private ImageTextDetailStrategy findMatchingStrategy(ImageTextStrategyRule rule) {
        if (Objects.isNull(rule)) {
            return null;
        }
        return factory.getImageTextDetailStrategy(rule.getStrategyName());
    }

    private boolean matchesRule(ImageTextStrategyRule rule, String pageSource, int categoryId, String serviceType, long dpDealGroupId) {
        return (StringUtils.isBlank(rule.getPageSource()) || rule.getPageSource().equals(pageSource))
                && (CollectionUtils.isEmpty(rule.getCategoryIds()) || rule.getCategoryIds().contains(categoryId))
                && (CollectionUtils.isEmpty(rule.getServiceTypes()) || rule.getServiceTypes().contains(serviceType))
                && (CollectionUtils.isEmpty(rule.getDpDealGroupIds()) || rule.getDpDealGroupIds().contains(dpDealGroupId));
    }

    private ImageTextStrategyRule buildLifeCleaningStrategyRule() {
        CleaningProductLionConfig config = Lion.getBean(LionConstants.APP_KEY, LionConstants.CLEANING_PRODUCT_INFORMATION_CONFIG, CleaningProductLionConfig.class);
        if (!config.isEnable()) {
            return null;
        }
        List<LifeClearHaiMaConfig> haiMaConfigs = haimaWrapper.cleaningProductInformation(config);
        List<Long> dpDealGroupIds = haiMaConfigs
                .stream()
                .map(LifeClearHaiMaConfig::getId)
                .map(Long::valueOf)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dpDealGroupIds)) {
            return null;
        }
        ImageTextStrategyRule rule = new ImageTextStrategyRule();
        rule.setDpDealGroupIds(dpDealGroupIds);
        rule.setPriority(2);
        rule.setStrategyName(ImageTextStrategyEnum.NONE_TITLE.getStrategyName());
        return rule;
    }
}
