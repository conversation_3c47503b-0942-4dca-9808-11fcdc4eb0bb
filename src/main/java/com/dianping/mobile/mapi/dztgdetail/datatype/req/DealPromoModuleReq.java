package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.annotation.MobileRequest.Param;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.DecryptedField;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

import static com.sankuai.sig.botdefender.core.enums.AssetIdType.SHOP_UUID;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/12/13.
 */
@Data
@TypeDoc(description = "团单促销模块请求参数")
@MobileRequest
public class DealPromoModuleReq implements IMobileRequest, Serializable {

    @Deprecated
    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，int类型")
    @Param(name = "dealgroupid")
    private Integer dealGroupId;

    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，string类型")
    @Param(name = "stringdealgroupid")
    private String stringDealGroupId;

    @FieldDoc(description = "团单城市ID", rule = "美团平台为美团城市ID，点评平台为点评城市ID")
    @Param(name = "cityid")
    private Integer cityId;

    @Deprecated
    @FieldDoc(description = "商户ID", rule = "美团平台为美团商户ID，点评平台为点评商户ID")
    @Param(name = "shopid", shopuuid = "shopuuid")
    private Long shopId;
    @Param(name = "shopIdEncrypt")
    @DecryptedField(targetFieldName = "shopId")
    private String shopIdEncrypt;

    @FieldDoc(description = "商户ID str", rule = "美团平台为美团商户ID，点评平台为点评商户ID")
    @Param(name = "shopidstr")
    private String shopIdStr;
    @Param(name = "shopIdStrEncrypt")
    @DecryptedField(targetFieldName = "shopIdStr")
    private String shopIdStrEncrypt;

    @FieldDoc(description = "商户UUID", rule = "美团平台为美团商户UUID，点评平台为点评商户UUID")
    @Param(name = "shopuuid")
    private String shopUuid;
    @Param(name = "shopUuidEncrypt")
    @DecryptedField(targetFieldName = "shopUuid", assetIdType = SHOP_UUID)
    private String shopUuidEncrypt;

    @FieldDoc(description = "客户端平台ID")
    @Param(name = "clienttype")
    private Integer clientType;

    public Integer getDealGroupId() {
        return dealGroupId != null ? dealGroupId : 0;
    }

    public Integer getCityId() {
        return cityId != null ? cityId : 0;
    }

    public Integer getShopId() {
        if(shopId == null) {
            return 0;
        }
        if(shopId > Integer.MAX_VALUE) {
            return 0;
        }
        return shopId.intValue();
    }

    public Integer getClientType() {
        return clientType != null ? clientType : 0;
    }

    public long getShopIdLong() {
        if (StringUtils.isNumeric(shopIdStr)) {
            return Long.parseLong(shopIdStr);
        } else if(shopId != null) {
            return shopId;
        }
        return 0L;
    }
}
