package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import com.google.common.collect.Lists;
import com.meituan.service.mobile.prometheus.enums.DealTypeEnum;
import com.meituan.service.mobile.prometheus.model.DealModel;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by wangqian on 16/4/19.
 */
public class DealUtils {

    /**
     * 是否展示POI详情
     * 1.物流单不展示使用了ctype为1判断
     * 2.抽奖单：霸王餐展示，其他不展示
     * 3.其他情况展示
     */
    public static boolean IsShowRdploc(DealModel dealModel) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.util.DealUtils.IsShowRdploc(com.meituan.service.mobile.prometheus.model.DealModel)");
        if (dealModel == null) {
            return true;
        }
        int ctype = dealModel.getCtype();
        if (ctype == DealTypeEnum.WULIU.getCtype()) {
            return false;
        }
        if (ctype == DealTypeEnum.LOTTERY.getCtype()) {
            if (null == dealModel.getFrontPoiCates()) {
                return false;
            }
            //霸王餐显示商家
            return dealModel.getFrontPoiCates().contains(204);
        }
        return true;
    }

    public static void assembleUrl(DealGroupPBO result, String str) {
        if (result.getBuyBar() != null && CollectionUtils.isNotEmpty(result.getBuyBar().getBuyBtns())) {
            List<DealBuyBtn> buyBtns = result.getBuyBar().getBuyBtns();

            for (DealBuyBtn btn : buyBtns) {
                if (StringUtils.isNotEmpty(btn.getRedirectUrl())) {
                    btn.setRedirectUrl(btn.getRedirectUrl() + str);
                }
            }
        }
    }

    public static String getServiceType(DealCtx ctx) {
        List<AttributeDTO> attributeDTOS = ctx.getAttrs();

        if(CollectionUtils.isEmpty(attributeDTOS)) {
            return "";
        }
        for(AttributeDTO attributeDTO : attributeDTOS) {
            if(attributeDTO == null) {
                continue;
            }
            if("service_type".equals(attributeDTO.getName()) && CollectionUtils.isNotEmpty(attributeDTO.getValue())) {
                return attributeDTO.getValue().get(0);
            }
        }
        return "";
    }

    /**
     * 从DealGroupDTO结构体中获取团单的三级类目
     * @param ctx 团单上下文对象
     * @return 团单三级类目。
     */
    public static String getDealGroupServiceType(DealCtx ctx) {
        DealGroupDTO dealGroup = ctx.getDealGroupDTO();
        if (Objects.isNull(dealGroup)) {
            return StringUtils.EMPTY;
        }
        return Optional.ofNullable(dealGroup.getCategory())
                .map(DealGroupCategoryDTO::getServiceType)
                .orElse(StringUtils.EMPTY);
    }

    /**
     * 从DealGroupDTO结构体中获取团单的三级类目id
     * @param ctx 团单上下文对象
     * @return 团单三级类目id。
     */
    public static Long getDealGroupServiceTypeId(DealCtx ctx) {
        DealGroupDTO dealGroup = ctx.getDealGroupDTO();
        if (Objects.isNull(dealGroup)) {
            return 0L;
        }
        return Optional.ofNullable(dealGroup.getCategory())
               .map(DealGroupCategoryDTO::getServiceTypeId)
               .orElse(0L);
    }

    /**
     * 判断团单是否是新款穿戴甲团单
     * @param ctx 团单上下文对象
     * @return true：是新款穿戴甲团单，false：不是新款穿戴甲团单
     */
    public static boolean isNewWearableNailDeal(DealCtx ctx) {
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        return isNewWearableNailDeal(dealGroupDTO);
    }

    public static boolean isNewWearableNailDeal(DealGroupPBO dealGroupPBO) {
        if (Objects.isNull(dealGroupPBO)) {
            return false;
        }
        return Objects.equals(dealGroupPBO.getCategoryId(), 502)
                && Objects.equals(dealGroupPBO.getServiceType(), "穿戴甲");
    }

    public static boolean isNewWearableNailDeal(DealGroupDTO dealGroupDTO) {
        if (Objects.isNull(dealGroupDTO)) {
            return false;
        }
        DealGroupCategoryDTO categoryDTO = dealGroupDTO.getCategory();
        if (Objects.isNull(categoryDTO)) {
            return false;
        }
        return Objects.equals(categoryDTO.getCategoryId(), 502L)
                && Objects.equals(categoryDTO.getServiceType(), "穿戴甲")
                && containAttrWithName(dealGroupDTO, "tag_unifyProduct");
    }

    /**
     * 判断团单是否是美甲三级类目团单
     * @param ctx 团单上下文对象
     * @return true: 是美甲三级类目团单，false：不是美甲三级类目团单
     */
    public static boolean isBeautyNailServiceTypeDeal(DealCtx ctx) {
        if (Objects.isNull(ctx)) {
            return false;
        }
        return isBeautyNailServiceTypeDeal(ctx.getDealGroupDTO());
    }

    public static boolean isBeautyNailServiceTypeDeal(DealGroupDTO dealGroupDTO) {
        if (Objects.isNull(dealGroupDTO) || Objects.isNull(dealGroupDTO.getCategory())) {
            return false;
        }
        return Objects.equals(dealGroupDTO.getCategory().getCategoryId(), 502L)
                && Objects.equals(dealGroupDTO.getCategory().getServiceType(), "美甲");
    }

    public static DealGroupCategoryDTO getDealGroupCategory(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.util.DealUtils.getDealGroupCategory(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        return Optional.ofNullable(ctx.getDealGroupDTO())
                .map(DealGroupDTO::getCategory)
                .orElse(new DealGroupCategoryDTO());
    }

    /**
     * 使用Java Stream判断DealGroupDTO的Attrs里是否包含name为key的元素
     *
     * @param dealGroupDTO DealGroupDTO对象
     * @param attrKey      要检查的属性名
     * @return 如果存在返回true，否则返回false
     */
    public static boolean containAttrWithName(DealGroupDTO dealGroupDTO, String attrKey) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.util.DealUtils.containAttrWithName(com.sankuai.general.product.query.center.client.dto.DealGroupDTO,java.lang.String)");
        if (dealGroupDTO == null || dealGroupDTO.getAttrs() == null || attrKey == null) {
            return false;
        }
        List<AttrDTO> attrs = dealGroupDTO.getAttrs();
        return attrs.stream().anyMatch(attr -> attrKey.equals(attr.getName()));
    }

    /**
     * 过滤出本团单关联的款式skuId
     * @param ctx 团单上下文对象
     * @param styleRelatedSkuIds 款式关联的skuId列表（可能包含其他团单的关联skuId）
     * @return 本团单关联的款式skuId列表
     */
    public static List<String> filterDealGroupSkuIds(DealCtx ctx, List<String> styleRelatedSkuIds) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.util.DealUtils.filterDealGroupSkuIds(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx,java.util.List)");
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        if (Objects.isNull(dealGroupDTO) || CollectionUtils.isEmpty(styleRelatedSkuIds)) {
            return Lists.newArrayList();
        }
        return filterDealGroupSkuIds(dealGroupDTO.getDeals(), styleRelatedSkuIds);
    }

    /**
     * 过滤出本团单关联的款式skuId
     * @param dealGroupDealDTOS 团单套餐信息
     * @param styleRelatedSkuIds 款式关联的skuId列表（可能包含其他团单的关联skuId）
     * @return 本团单关联的款式skuId列表
     */
    public static List<String> filterDealGroupSkuIds(List<DealGroupDealDTO> dealGroupDealDTOS,
                                                     List<String> styleRelatedSkuIds) {
        if (CollectionUtils.isEmpty(dealGroupDealDTOS) || CollectionUtils.isEmpty(styleRelatedSkuIds)) {
            return Lists.newArrayList();
        }
        List<String> dealGroupSkuIds = dealGroupDealDTOS.stream()
                .filter(DealUtils::filterOfflineSku)
               .map(DealGroupDealDTO::getDealId)
               .map(String::valueOf)
               .collect(Collectors.toList());
        // 取dealGroupSkuIds和styleRelatedSkuIds的交集
        return dealGroupSkuIds.stream()
               .filter(styleRelatedSkuIds::contains)
               .collect(Collectors.toList());
    }

    public static boolean filterOfflineSku(DealGroupDealDTO deal) {
        if (Objects.isNull(deal) || Objects.isNull(deal.getBasic())) {
            return false;
        }
        return Objects.equals(deal.getBasic().getStatus(), 1);
    }


    public static String getServiceTypeByDealAttrs(List<AttrDTO> attrDTOS) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.util.DealUtils.getServiceTypeByDealAttrs(java.util.List)");
        if(CollectionUtils.isEmpty(attrDTOS)) {
            return "";
        }
        for(AttrDTO attrDTO : attrDTOS) {
            if(attrDTO == null) {
                continue;
            }
            if("service_type".equals(attrDTO.getName()) && CollectionUtils.isNotEmpty(attrDTO.getValue())) {
                return attrDTO.getValue().get(0);
            }
        }
        return "";
    }

    public static String findFirstAttrValue(List<AttributeDTO> attrs, String key) {
        if (CollectionUtils.isEmpty(attrs)) {
            return null;
        }
        return attrs.stream()
                .filter(attr -> Objects.equals(attr.getName(), key))
                .findFirst()
                .map(AttributeDTO::getValue)
                .filter(org.apache.commons.collections4.CollectionUtils::isNotEmpty)
                .flatMap(values -> values.stream().findFirst())
                .orElse(null);

    }

    /**
     * 是否是老团详样式
     *
     * @param ctx 上下文环境
     * @return 是否是老团详样式
     */
    public static boolean isOldDealDetailStyle(DealCtx ctx) {
        EnvCtx envCtx = ctx.getEnvCtx();
        return envCtx.isWxMini() && !ctx.isEnableCardStyle() && !ctx.isEnableCardStyleV2();
    }

    /**
     * 是否是交易保障团单
     *
     * @return
     */
    public static boolean isTradeAssuranceDeal(DealCtx ctx) {
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        return isTradeAssuranceDeal(dealGroupDTO);
    }

    public static boolean isTradeAssuranceDeal(DealGroupDTO dealGroupDTO) {
        if (Objects.isNull(dealGroupDTO)) {
            return false;
        }
        String isTradeAssurance = AttributeUtils.getFirstValueV2(dealGroupDTO.getAttrs(), "is_trade_assurance");
        if (StringUtils.isNotEmpty(isTradeAssurance) && isTradeAssurance.equals("1")) {
            return true;
        }
        return false;
    }

    /**
     * * 是否是预付款团单，1-预付款商品，0-全款商品
     *
     * @param ctx
     * @return
     */
    public static boolean isPrePayDeal(DealCtx ctx) {
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        return isPrePayDeal(dealGroupDTO);
    }

    public static boolean isPrePayDeal(DealGroupDTO dealGroupDTO) {
        if (Objects.isNull(dealGroupDTO)) {
            return false;
        }
        String payMethod = AttributeUtils.getFirstValueV2(dealGroupDTO.getAttrs(), "pay_method");
        if (StringUtils.isNotEmpty(payMethod) && payMethod.equals("1")) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否为留资型团单
     */
    public static boolean isLeadsDeal(DealCtx ctx) {
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        return isLeadsDeal(dealGroupDTO);
    }

    /**
     * 判断团单是否展示预约和购买按钮
     */
    public static boolean isLeadsDeal(DealGroupDTO dealGroupDTO) {
        if (Objects.isNull(dealGroupDTO) || Objects.isNull(dealGroupDTO.getCategory())) {
            return false;
        }
        DealGroupCategoryDTO categoryDTO = dealGroupDTO.getCategory();
        return LionConfigUtils.hitLeadsDeal(categoryDTO);
    }

    /**
     * 判断是否为结婚指定类目下的特惠团单 + 指定团购类目，若是则显示打码的价格、已预约人数和新型底bar[在线咨询、获取低价]
     */
    public static boolean isWeddingSpecialWithPoiAndDealCategory(DealCtx context) {
        return isWeddingSpecialWithPoiCategory(context) && isWeddingLeadsDeal(context);
    }

    /**
     * 判断是否为结婚指定类目下的特惠团单，若是则显示打码的价格、已预约人数和新型底bar[在线咨询、获取低价]
     */
    public static boolean isWeddingSpecialWithPoiCategory(DealCtx context) {
        return context.isHitSpecialValueDeal() && LionConfigUtils.isHitWeddingSpecialPoiCategory(context);
    }

    /**
     * 判断是否为预约和留资型团单
     */
    public static boolean isWeddingLeadsDeal(DealCtx ctx) {
        // 对非APP侧进行过滤
        if(!ctx.getEnvCtx().judgeMainApp()){
            return false;
        }
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        return isWeddingLeadsDeal(dealGroupDTO);
    }

    /**
     * 判断团单是否展示预约、留资、购买按钮
     */
    public static boolean isWeddingLeadsDeal(DealGroupDTO dealGroupDTO) {
        if (Objects.isNull(dealGroupDTO) || Objects.isNull(dealGroupDTO.getCategory())) {
            return false;
        }
        DealGroupCategoryDTO categoryDTO = dealGroupDTO.getCategory();
        return LionConfigUtils.hitWeddingLeadsDeal(categoryDTO);
    }

    /**
     * 根据团单类目、营销场 判断是否满足[家政]强预订的条件
     */
    public static boolean isPreOrderDealForCateAndPromotion(DealCtx ctx) {
        // 对非APP侧进行过滤
        if(!ctx.getEnvCtx().judgeMainApp()){
            return false;
        }
        return LionConfigUtils.hitPreOrderDeal(ctx) && LionConfigUtils.isNotPromotionDeal(ctx);
    }

    /**
     * 判断团单是否具备预约权益
     */
    public static boolean hasResvBenefits(DealCtx context) {
        if (Objects.isNull(context.getLeadsInfo()) || NumbersUtils.lessThanAndEqualZero(context.getLeadsInfo().getBizSourceId())) {
            return false;
        }
        return true;
    }

    /**
     * 判断是否为月子房型套餐团单
     * @param ctx
     * @return
     */
    public static boolean isBabyCareHouseDeal(DealCtx ctx) {
        if (Objects.isNull(ctx.getDealGroupDTO())) {
            return false;
        }
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        DealGroupCategoryDTO categoryDTO = dealGroupDTO.getCategory();
        if (Objects.isNull(categoryDTO)) {
            return false;
        }
        return Objects.equals(categoryDTO.getCategoryId(), 1011L)
                && Objects.equals(categoryDTO.getServiceTypeId(), 126041L)
                && containAttrWithName(dealGroupDTO, "tag_unifyProduct");
    }

    /**
     * 判断是否为预览团单
     * @param ctx 团单上下文对象
     * @return 是否为预览团单
     */
    public static boolean isPreviewDeal(DealCtx ctx) {
        return isPreviewDeal(ctx.getRequestSource())
                ||isPreviewClient(ctx);
    }

    public static boolean isPreviewDeal(String pageSource) {
        return RequestSourceEnum.fromPreview(pageSource);
    }

    /**
     * 预览端
     * @param ctx
     * @return
     */
    public static boolean isPreviewClient(DealCtx ctx) {
        if (Objects.isNull(ctx.getEnvCtx())) {
            return false;
        }
        return ctx.getEnvCtx().isApollo()
                || ctx.getEnvCtx().isDpMerchant();
    }

    public static boolean isHotDeal(List<AttrDTO> attrDTOS) {
        return Optional.ofNullable(attrDTOS).orElse(Lists.newArrayList()).stream()
                .anyMatch(attrDTO -> Objects.equals(attrDTO.getName(), "topPerformingProduct"));

    }
}
