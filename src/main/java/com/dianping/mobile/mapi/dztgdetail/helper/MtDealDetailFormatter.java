package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.Pair;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public final class MtDealDetailFormatter {
	
    private MtDealDetailFormatter() {
    }
	
	public static String toPlainText(String s) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.helper.MtDealDetailFormatter.toPlainText(java.lang.String)");
        StringBuilder sb = new StringBuilder();
		
		int p = 0;
		while (StringUtils.isNotEmpty(s) && p < s.length()) {
			int imgStart = s.indexOf("<img>", p);
			if (imgStart == -1) {
				sb.append(s.substring(p));
				p = s.length();
			} else {
				int imgEnd = s.indexOf("</img>", imgStart);
				if (imgEnd == -1) {
					sb.append(s.substring(p));
					p = s.length();
				} else {
					if (imgStart-p > 0) {
						sb.append(s.substring(p, imgStart));
					}
					p = imgEnd + 6;
					if (p < s.length() && s.charAt(p) == '\n') {
						p++;
					}
				}
			}
		}
		
		return sb.toString();
	}
	
	public static List<Pair> toPairList(String s) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.helper.MtDealDetailFormatter.toPairList(java.lang.String)");
        List<Pair> list = new ArrayList<Pair>();
		if (s == null || s.isEmpty()) {
			return list;
		}
		
		// Pair.type: 1=TITLE, 2=TEXT, 3=IMG, 4=RED TEXT
		int p = 0;
		while (p < s.length()) {
			int imgStart = s.indexOf("<img>", p);
			if (imgStart == -1) {
				list.add(new Pair("", chomp(s.substring(p)), 2));
				p = s.length();
			} else {
				int imgEnd = s.indexOf("</img>", imgStart);
				if (imgEnd == -1) {
					list.add(new Pair("", chomp(s.substring(p)), 2));
					p = s.length();
				} else {
					if (imgStart-p > 0) {
						list.add(new Pair("", chomp(s.substring(p, imgStart)), 2));
					}
					list.add(new Pair("", s.substring(imgStart + 5, imgEnd), 3));
					p = imgEnd + 6;
					if (p < s.length() && s.charAt(p) == '\n') {
						p++;
					}
				}
			}
		}

		return list;
	}
	
	private static String chomp(String s) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.helper.MtDealDetailFormatter.chomp(java.lang.String)");
        return (s != null && s.endsWith("\n")) ? s.substring(0, s.length() - 1) : s;
	}
	
	public static String chopTitle(String text, String title) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.helper.MtDealDetailFormatter.chopTitle(java.lang.String,java.lang.String)");
        if (text == null) {
			return null;
		}
		text = StringUtils.strip(text);
		text = StringUtils.removeStart(text, title);
		text = StringUtils.removeStart(text, '[' + title + ']');
//		text = StringUtils.removeStart(text, '【' + title + '】');
		text = StringUtils.strip(text);
		return text;
	}
	
	public static List<Pair> toPairListOld(String s) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.helper.MtDealDetailFormatter.toPairListOld(java.lang.String)");
        List<Pair> list = new ArrayList<Pair>();
		if (s == null || s.isEmpty()) {
			return list;
		}

		while (s.indexOf("</img>") > 0) {
			if (s.indexOf("<img>") > 0) {
				list.add(new Pair("", s.substring(0, s.indexOf("<img>")), 2));
				list.add(new Pair("", s.substring(s.indexOf("<img>") + 5, s.indexOf("</img>")), 3));
				s = s.substring(s.indexOf("</img>") + 6, s.length());
			} else if (s.indexOf("<img>") == 0) {
				list.add(new Pair("", s.substring(s.indexOf("<img>") + 5, s.indexOf("</img>")), 3));
				s = s.substring(s.indexOf("</img>") + 6, s.length());
			}
		}
		if (s.length() > 0) {
			list.add(new Pair("", s, 2));
		}
		return list;
	}
	
	private static final Pattern HTML_IMG = Pattern.compile("<\\s*img\\s+[^>]*>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);

	public static String removeHtmlImage(String html) {
		if (StringUtils.isNotEmpty(html)) {
			StringBuilder sb = new StringBuilder();
			Matcher m = HTML_IMG.matcher(html);
			int index = 0;
			while (m.find()) {
				if (index < m.start()) {
					for (int i = index; i < m.start(); i++) {
						sb.append(html.charAt(i));
					}
				}
				index = m.end();
			}
			if (index < html.length()) {
				for (int i = index; i < html.length(); i++) {
					sb.append(html.charAt(i));
				}
			}
			return sb.length() > 0 ? sb.toString() : html;
		}
		return html;
	}
}
