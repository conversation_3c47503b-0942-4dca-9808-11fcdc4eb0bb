package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.deal.common.enums.GpsType;
import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.deal.shop.dto.BestShopReq;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DzCardPromoWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MiniProgramSceneEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.util.DealProductUtils;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.PreviewDealGroupUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;

public class BestShopProcessor extends AbsDealProcessor {

    @Autowired
    private DealGroupWrapper dealGroupWrapper;
    @Resource
    private DzCardPromoWrapper wrapper;

    /**
     * 若是预览单，则关闭
     * @param ctx context
     * @return boolean
     */
    @Override
    public boolean isEnable(DealCtx ctx) {
        return !PreviewDealGroupUtils.isPreviewDianpingDealGroup(ctx);
    }

    @Override
    public void prepare(DealCtx ctx) {
        if(GreyUtils.enableQueryCenterForMainApi(ctx)) {
            Future shopFuture = dealGroupWrapper.preDealGroupBestShop(buildBestShopReq(ctx));
            ctx.getFutureCtx().setBestShopFuture(shopFuture);
        } else {
            Future shopFuture = dealGroupWrapper.preDealGroupBestShop(buildBestShopReq(ctx));
            ctx.getFutureCtx().setBestShopFuture(shopFuture);

            // todo xiangrui 已经被灰度了，可以移除了，只查询未使用
            Future relatedShopsFuture = dealGroupWrapper.queryDisplayLongShopIdByDealGroupFuture(ctx.getDpId());
            ctx.getFutureCtx().setRelatedShopsFuture(relatedShopsFuture);
        }

    }

    @Override
    public void process(DealCtx ctx) {
        processByQueryCenter(ctx);
    }

    private BestShopReq buildBestShopReq(DealCtx ctx) {
        BestShopReq shopReq = buildBestShopReqWithoutShopId(ctx);
        if (ctx.isMt()) {
            shopReq.setShopId(ctx.getMtLongShopId());
        } else {
            shopReq.setShopId(ctx.getDpLongShopId());
        }
        return shopReq;
    }

    private BestShopReq buildBestShopReqWithoutShopId(DealCtx ctx) {
        BestShopReq shopReq = new BestShopReq();
        if (ctx.isMt()) {
            shopReq.setDealGroupId(ctx.getMtId());
            shopReq.setCityId(ctx.getMtCityId());
        } else {
            shopReq.setDealGroupId(ctx.getDpId());
            shopReq.setCityId(ctx.getDpCityId());
        }
        shopReq.setLat(ctx.getUserlat());
        shopReq.setLng(ctx.getUserlng());
        shopReq.setGpsType(GpsType.GCJ02.getType());
        shopReq.setClientType(ctx.getEnvCtx().getClientType());
        return shopReq;
    }

    private void processByQueryCenter(DealCtx ctx) {
        // todo xiangrui 入口判断
        List<Long> relatedShops = getDisplayShopIds(ctx.getDealGroupDTO());

        BestShopDTO bestShop;
        if (ctx.getDpLongShopId() > 0
                // 多加一个判断
                && DealProductUtils.checkShopNoExist(ctx.getDealGroupDTO(), ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId(), ctx.isMt())
                && !relatedShops.contains(ctx.getDpLongShopId())) {
            // 有入口门店，且入口门店没有绑定该团购，则重新请求，入参不指定门店
            Future shopFuture = dealGroupWrapper.preDealGroupBestShop(buildBestShopReqWithoutShopId(ctx));
            bestShop = dealGroupWrapper.getFutureResult(shopFuture);
        } else {
            // 没有来源门店id或者来源门店id与团购绑定，可以直接使用请求结果
            bestShop = dealGroupWrapper.getFutureResult(ctx.getFutureCtx().getBestShopFuture());
        }

        ctx.setBestShopResp(bestShop);

        if (ctx.getLongPoiId4PFromReq() > 0) {
            return;
        }
        if (bestShop == null) {
            return;
        }
        ctx.setDpLongShopId(Integer.parseInt(String.valueOf(bestShop.getDpShopId())));
        ctx.setMtLongShopId(Integer.parseInt(String.valueOf(bestShop.getMtShopId())));

        if(Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.joy.card.switch", false)) {
            prepareJoyCardInfo(ctx);
        }
    }

    private void prepareJoyCardInfo(DealCtx ctx) {
        boolean isNotExternal = !ctx.isExternal();
        boolean isExternalAndEnabled = ctx.getEnvCtx().isExternalAndEnabled(MiniProgramSceneEnum.JOY_CARD.getPromoScene());
        if (isNotExternal || isExternalAndEnabled) {
            ctx.getFutureCtx().setDzCardFuture(wrapper.prepare(ctx));
            ctx.getFutureCtx().setUserStateFuture(wrapper.prepareUserState(ctx));
        }
    }


    private List<Long> getDisplayShopIds(DealGroupDTO dealGroupDTO) {
        if(dealGroupDTO == null || dealGroupDTO.getDisplayShopInfo() == null || dealGroupDTO.getDisplayShopInfo().getDpDisplayShopIds() == null) {
            return Collections.emptyList();
        }
        return dealGroupDTO.getDisplayShopInfo().getDpDisplayShopIds();
    }
}
