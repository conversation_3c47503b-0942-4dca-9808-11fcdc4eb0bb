package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@TypeDoc(description = "团单购买栏")
@MobileDo(id = 0xf464)
public class DealBuyBar implements Serializable {

    public DealBuyBar(int buyType, List<DealBuyBtn> buyBtns) {
        this.buyType = buyType;
        this.buyBtns = buyBtns;
    }

    /**
     * 0:原样式; 1:加入购物车样式
     * @see com.dianping.mobile.mapi.dztgdetail.common.enums.StyleTypeEnum
     */
    @FieldDoc(description = "底Bar展示样式")
    @MobileField(key = 0x8b11)
    private int styleType = 0;

    @FieldDoc(description = " 0：普通团单；1：次卡；2：折扣卡",
            rule = "@see com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar.BuyType")
    @MobileField(key = 0xc07a)
    private int buyType;

    /**
     * 购买按钮数组
     */
    @FieldDoc(description = "商品ID：前端用于打点")
    @MobileField(key = 0x83af)
    private List<DealBuyBtn> buyBtns;

    @FieldDoc(description = "加购物车按钮")
    @MobileField(key = 0x1617)
    private DealCartBtn cartBtn;

    /**
     * 横幅
     */
    @MobileField(key = 0xde4a)
    private DealBuyBanner buyBanner;

    @FieldDoc(description = "拼团成员信息")
    @MobileField(key = 0x58ee)
    private PinTuanMemberModule pinTuanMemberModule;

    public enum BuyType {

        COMMON(0), //普通团单
        TIMESCARD(1), //次卡
        DISCOUNTCARD(2), //折扣卡
        PINPOOL(3), //拼团
        IDLE_PROMO(4), //闲时优惠
        JOY_CARD(5), //玩乐卡
        CROSS_CARD(6); // 通用兑换卡

        public int type;

        BuyType(int type) {
            this.type = type;
        }
    }
}
