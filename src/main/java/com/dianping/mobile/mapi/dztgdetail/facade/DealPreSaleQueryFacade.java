package com.dianping.mobile.mapi.dztgdetail.facade;

import com.alibaba.fastjson.JSON;
import com.dianping.account.utils.util.LionConfigUtils;
import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.gmkt.activity.api.dto.BatchQueryDealActivityRequest;
import com.dianping.gmkt.activity.api.dto.BatchQueryDealActivityResponse;
import com.dianping.gmkt.activity.api.dto.DealActivityDTO;
import com.dianping.gmkt.activity.api.dto.Version;
import com.dianping.gmkt.activity.api.enums.ExposeChannel;
import com.dianping.gmkt.activity.api.enums.ExposureColorKeyEnum;
import com.dianping.gmkt.activity.api.enums.ExposurePicUrlKeyEnum;
import com.dianping.gmkt.activity.api.enums.ExposureTextKeyEnum;
import com.dianping.gmkt.activity.api.enums.RequestSource;
import com.dianping.gmkt.activity.enums.AppPlatform;
import com.dianping.json.facade.JsonFacade;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealActivityWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.PreSaleCountDownReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ActivityDisplayStyle;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CountDownPBO;
import com.dianping.mobile.mapi.dztgdetail.exception.QueryCenterResultException;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupBasicInfoBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupCategoryBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupIdBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.AttrSubjectEnum;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import com.sankuai.general.product.query.center.client.util.DealGroupUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Future;

/**
 * 团祥页统一大促模块、预售团单倒计时门面
 */
@Component
@Slf4j
public class DealPreSaleQueryFacade {

    private static final ArrayList<String> ATTRS = Lists.newArrayList(DealAttrKeys.PRE_SALE_TAG);

    @Autowired
    private DealGroupWrapper dealGroupWrapper;

    @Autowired
    private DealActivityWrapper dealActivityWrapper;

    @Autowired
    @Qualifier("queryCenterDealGroupQueryService")
    private DealGroupQueryService queryCenterDealGroupQueryService;

    public CountDownPBO queryPreSaleCountDown(PreSaleCountDownReq request, EnvCtx ctx) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.DealPreSaleQueryFacade.queryPreSaleCountDown(PreSaleCountDownReq,EnvCtx)");
        if(GreyUtils.isQueryCenterGreyBatch2(request.getDealgroupid().intValue())) {
            try {
                return queryPreSaleCountDownFromQueryCenter(request, ctx);
            } catch (TException e) {
                log.error("queryPreSaleCountDownFromQueryCenter," , e);
            }
        }
        int dealGroupId = request.getDealgroupid().intValue();

        if (ctx.isMt()) {
            dealGroupId = dealGroupWrapper.getDpDealGroupId(request.getDealgroupid().intValue());
        }

        BatchQueryDealActivityRequest activityReq = buildBatchQueryDealActivityReq(request, ctx);
        Future activityFuture = dealActivityWrapper.prepareDealActivity(activityReq);
        Future attrFuture = dealGroupWrapper.preAttrs(dealGroupId, ATTRS);
        Future dealGroupFuture = dealGroupWrapper.preDealGroupBase(dealGroupId);

        DealActivityDTO dealActivity = getDealActivity(activityReq, activityFuture);

        // 新大促
        CountDownPBO unifiedActivity = buildUnifiedActivity(dealActivity, Boolean.TRUE.equals(request.getConvertcolor()));
        if (unifiedActivity != null) {
            return unifiedActivity;
        }

        // 是预售单的时候展示预售倒计时
        return buildDealGroupPreSale(request, attrFuture, dealGroupFuture);
    }

    public CountDownPBO queryPreSaleCountDownFromQueryCenter(PreSaleCountDownReq request, EnvCtx ctx) throws TException {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.DealPreSaleQueryFacade.queryPreSaleCountDownFromQueryCenter(PreSaleCountDownReq,EnvCtx)");

        QueryDealGroupListResponse queryDealGroupListResponse = null;
        Set<Long> set = new HashSet<>();
        set.add(request.getDealgroupid());
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(set, ctx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP)
                .dealGroupId(DealGroupIdBuilder.builder().all())
                .basicInfo(DealGroupBasicInfoBuilder.builder().endSaleDate())
                .category(DealGroupCategoryBuilder.builder().all())
                .attrsByKey(AttrSubjectEnum.DEAL_GROUP, DealAttrKeys.PRE_SALE_TAG)
                .build();

        queryDealGroupListResponse = queryCenterDealGroupQueryService.queryByDealGroupIds(queryByDealGroupIdRequest);

        if(queryDealGroupListResponse == null) {
            throw new QueryCenterResultException("queryCenter returns null, request : " + GsonUtils.toJsonString(queryByDealGroupIdRequest));
        }

        if(queryDealGroupListResponse.getCode() != 0) {
            throw new QueryCenterResultException("queryCenter not success, request : " + GsonUtils.toJsonString(queryByDealGroupIdRequest));
        }

        if(CollectionUtils.isEmpty(queryDealGroupListResponse.getData().getList())) {
            throw new QueryCenterResultException("queryDealGroupListResponse.getData().getList() is empty, request : " + GsonUtils.toJsonString(queryByDealGroupIdRequest));
        }

        if(queryDealGroupListResponse.getData().getList().get(0) == null) {
            throw new QueryCenterResultException("queryDealGroupListResponse.getData().getList().get(0) is null, request : " + GsonUtils.toJsonString(queryByDealGroupIdRequest));
        }

        DealGroupDTO dealGroupDTO = queryDealGroupListResponse.getData().getList().get(0);

        BatchQueryDealActivityRequest activityReq = buildBatchQueryDealActivityReq(request, ctx);
        Future activityFuture = dealActivityWrapper.prepareDealActivity(activityReq);
        List<AttrDTO> attrDTOS = dealGroupDTO.getAttrs();
        DealGroupBasicDTO dealGroupBasicDTO = dealGroupDTO.getBasic();

        DealActivityDTO dealActivity = getDealActivity(activityReq, activityFuture);

        // 新大促
        CountDownPBO unifiedActivity = buildUnifiedActivity(dealActivity, Boolean.TRUE.equals(request.getConvertcolor()));
        if (unifiedActivity != null) {
            return unifiedActivity;
        }

        // 是预售单的时候展示预售倒计时
        return buildDealGroupPreSaleV2(request, attrDTOS, dealGroupBasicDTO);
    }

    private CountDownPBO buildDealGroupPreSaleV2(PreSaleCountDownReq request, List<AttrDTO> attrs, DealGroupBasicDTO dealGroupBaseDTO) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.DealPreSaleQueryFacade.buildDealGroupPreSaleV2(PreSaleCountDownReq,List,DealGroupBasicDTO)");

        if (!DealAttrHelper.isPreSaleV2(attrs)) {
            return null;
        }

        String value = LionConfigUtils.getProperty(LionConstants.COUNDDOWN, "");

        if (StringUtils.isEmpty(value)) {
            return null;
        }

        //如果加48小时还没结束时间，不用展示
        if(dealGroupBaseDTO == null) {
            return null;
        }
        Date endDate = DealGroupUtils.convertString2Date(dealGroupBaseDTO.getEndSaleDate());
        if (endDate == null
                || DateUtils.addHours(new Date(), 48).before(endDate)
                || new Date().after(endDate)) {
            return null;
        }

        CountDownPBO result = JsonFacade.deserialize(value, CountDownPBO.class);
        result.setType(3);
        result.setDealGroupId(request.getDealgroupid());
        result.setCountDown(endDate.getTime());

        return result;
    }

    private CountDownPBO buildDealGroupPreSale(PreSaleCountDownReq request, Future attrFuture, Future dealGroupFuture) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.DealPreSaleQueryFacade.buildDealGroupPreSale(PreSaleCountDownReq,Future,Future)");

        if (!isPreSale(attrFuture)) {
            return null;
        }

        String value = LionConfigUtils.getProperty(LionConstants.COUNDDOWN, "");

        if (StringUtils.isEmpty(value)) {
            return null;
        }

        DealGroupBaseDTO dealGroupBaseDTO = dealGroupWrapper.getFutureResult(dealGroupFuture);
        //如果加48小时还没结束时间，不用展示
        if (dealGroupBaseDTO == null || dealGroupBaseDTO.getEndDate() == null
                || DateUtils.addHours(new Date(), 48).before(dealGroupBaseDTO.getEndDate())
                || new Date().after(dealGroupBaseDTO.getEndDate())) {
            return null;
        }

        CountDownPBO result = JsonFacade.deserialize(value, CountDownPBO.class);
        result.setType(3);
        result.setDealGroupId(request.getDealgroupid());
        result.setCountDown(dealGroupBaseDTO.getEndDate().getTime());

        return result;
    }

    private BatchQueryDealActivityRequest buildBatchQueryDealActivityReq(PreSaleCountDownReq req, EnvCtx ctx) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.DealPreSaleQueryFacade.buildBatchQueryDealActivityReq(PreSaleCountDownReq,EnvCtx)");
        BatchQueryDealActivityRequest request = new BatchQueryDealActivityRequest();

        if (ctx.isMt()) {
            request.setMtDealIds(Lists.newArrayList(req.getDealgroupid().intValue()));
            request.setMtCity(req.getCityid());
            request.setUserIdL(ctx.getMtUserId());//已确认判断平台后再使用
            request.setAppPlatform(AppPlatform.MT);
        } else {
            request.setDpDealIds(Lists.newArrayList(req.getDealgroupid().intValue()));
            request.setDpCity(req.getCityid());
            request.setUserIdL(ctx.getDpUserId());
            request.setAppPlatform(AppPlatform.DP);
        }
        request.setSource(RequestSource.TuanDetail);
        request.setVersion(new Version(ctx.getVersion()));
        request.setChannel(ExposeChannel.App.code);
        return request;
    }

    private DealActivityDTO getDealActivity(BatchQueryDealActivityRequest activityReq, Future activityFuture) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.DealPreSaleQueryFacade.getDealActivity(com.dianping.gmkt.activity.api.dto.BatchQueryDealActivityRequest,java.util.concurrent.Future)");

        BatchQueryDealActivityResponse resp = dealGroupWrapper.getFutureResult(activityFuture, "", "batchQueryDealActivity");

        if (Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.log.enable", false)) {
            log.info("请求营销接口DealActivityQueryService.batchQueryDealActivity返回={},入参={}",
                    JSON.toJSONString(resp), JSON.toJSONString(activityReq));
        }

        if (resp == null
                || resp.getDealActivityDTO() == null
                || !resp.getDealActivityDTO().isEnable()) {
            return null;
        }

        return resp.getDealActivityDTO();
    }

    private boolean isPreSale(Future attrFuture) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.DealPreSaleQueryFacade.isPreSale(java.util.concurrent.Future)");
        List<AttributeDTO> list = dealGroupWrapper.getFutureResult(attrFuture);
        return DealAttrHelper.isPreSale(list);
    }

    private CountDownPBO buildUnifiedActivity(DealActivityDTO dto, boolean convertColor) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.DealPreSaleQueryFacade.buildUnifiedActivity(com.dianping.gmkt.activity.api.dto.DealActivityDTO,boolean)");
        boolean isValid = checkActivity(dto);
        boolean enable = activityEnable();

        if (!isValid || !enable) {
            return null;
        }

        CountDownPBO result = new CountDownPBO();
        String picUrl;
        ActivityDisplayStyle style = null;

        if (dto.getDisplayType() == 2) {
            picUrl = dto.getPicUrlMap().get(ExposurePicUrlKeyEnum.DEAL_PRICE_THEME_URL.getKey());
            style = buildActivityDisplayStyle(dto, convertColor);
        } else {
            picUrl = dto.getUrls().get(0);
        }

        result.setActivityId(dto.getPageId() == null ? null : String.valueOf(dto.getPageId()));
        result.setPicUrl(picUrl);
        result.setActivityDisplayStyle(style);

        boolean displayCountDown = Boolean.TRUE.equals(dto.getActivityCDDisplayFlag());

        if (dto.getDisplayType() == 2) {
            result.setType(4);
            Date now = new Date();
            boolean isActivityValid = now.after(dto.getActivityBeginDate()) && now.before(dto.getActivityEndDate());

            if (isActivityValid && displayCountDown) {
                result.setCountDown(dto.getActivityEndDate().getTime());
            }

        } else if (displayCountDown) {
            result.setCountDown(dto.getActivityEndDate().getTime());
            result.setTitle("还剩");
            result.setType(1);
        } else {
            result.setTitle("活动时间");
            SimpleDateFormat dateFormat = new SimpleDateFormat("MM.dd");
            String content = dateFormat.format(dto.getActivityBeginDate()) + "-" + dateFormat.format(dto.getActivityEndDate());
            result.setCountDownDesc(content);
            result.setType(2);
        }

        return result;
    }

    private boolean checkActivity(DealActivityDTO dto) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.DealPreSaleQueryFacade.checkActivity(com.dianping.gmkt.activity.api.dto.DealActivityDTO)");
        if (dto == null) {
            return false;
        }

        if (dto.getDisplayType() == 2) {
            Map<String, String> picUrlMap = dto.getPicUrlMap();
            return !MapUtils.isEmpty(picUrlMap) && picUrlMap.get(ExposurePicUrlKeyEnum.DEAL_PRICE_THEME_URL.getKey()) != null;
        } else {
            return !CollectionUtils.isEmpty(dto.getUrls());
        }
    }

    private ActivityDisplayStyle buildActivityDisplayStyle(DealActivityDTO dto, boolean convertColor) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.DealPreSaleQueryFacade.buildActivityDisplayStyle(com.dianping.gmkt.activity.api.dto.DealActivityDTO,boolean)");
        ActivityDisplayStyle style = new ActivityDisplayStyle();
        Map<String, String> colorMap = dto.getColorMap();

        if (MapUtils.isEmpty(colorMap)) {
            return null;
        }

        String countdownBgColor = colorMap.get(ExposureColorKeyEnum.TIME_BACKGROUND_COLOR.getKey());
        style.setCountdownBgColor(convertColorForMRN(countdownBgColor, convertColor));

        String countdownTextColor = colorMap.get(ExposureColorKeyEnum.TIME_TEXT_COLOR.getKey());
        style.setCountdownTextColor(convertColorForMRN(countdownTextColor, convertColor));

        String labelBgColor = colorMap.get(ExposureColorKeyEnum.LABEL_BACKGROUND_COLOR.getKey());
        style.setLabelBgColor(convertColorForMRN(labelBgColor, convertColor));

        String labelTextColor = colorMap.get(ExposureColorKeyEnum.LABEL_TEXT_COLOR.getKey());
        style.setLabelTextColor(convertColorForMRN(labelTextColor, convertColor));

        String value = getValue(dto.getTextMap(), ExposureTextKeyEnum.TIME_REPLACE_TEXT.getKey());
        style.setTimeReplaceText(convertColorForMRN(value, convertColor));

        style.setJumpLink(dto.getJumpLink());

        return style;
    }

    private String convertColorForMRN(String originColor, boolean convertColor) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.DealPreSaleQueryFacade.convertColorForMRN(java.lang.String,boolean)");
        if (!convertColor || StringUtils.isBlank(originColor)) {
            return originColor;
        }

        if (originColor.startsWith("#") && originColor.length() == 9) {
            return "#" + originColor.substring(3) + originColor.substring(1, 3);
        }

        return originColor;
    }

    private String getValue(Map<String, String> map, String key) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.DealPreSaleQueryFacade.getValue(java.util.Map,java.lang.String)");
        return MapUtils.isEmpty(map) ? null : map.get(key);
    }

    private boolean activityEnable() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.DealPreSaleQueryFacade.activityEnable()");
        return Lion.getBooleanValue(LionConstants.UNIFIED_ACTIVITY_ENABLE, true);
    }
}
