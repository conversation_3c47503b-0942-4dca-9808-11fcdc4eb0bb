package com.dianping.mobile.mapi.dztgdetail.rcf.domian;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import com.dianping.cat.Cat;
import com.dianping.deal.bff.cache.dto.RcfDealBffCommonParamDTO;
import com.dianping.deal.bff.cache.enums.RcfDealBffClientTypeEnum;
import com.dianping.deal.bff.cache.enums.RcfDealBffInterfaceEnum;
import com.dianping.deal.bff.cache.enums.RcfDealInterfaceReturnTypeEnum;
import com.dianping.deal.bff.cache.response.DealBffCacheQueryResponse;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.rcf.api.req.DealNativeSnapshotReq;
import com.dianping.mobile.mapi.dztgdetail.rcf.api.vo.DealFlexBoxCfg;
import com.dianping.mobile.mapi.dztgdetail.rcf.api.vo.DealRcfNativeSnapshot;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.dto.DealBffResponseDTO;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.bff.cache.DealBffCacheAclService;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.constant.DealRcfNativeSnapshotConstant;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.dealbaseinfo.DealCategoryCacheService;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.deallayout.DealLayoutService;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.deallayout.dto.ModuleItem;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.newdeal.NewDealSnapshotService;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.pagetype.ShoppingGuideProductInfoService;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.switcher.DealRcfSwitcherService;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.switcher.dto.DealRcfSwitcherResult;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.pigeon.remoting.common.exception.RpcException;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2024/11/18 19:01
 */
@Slf4j
@Component
public class DealNativeSnapshotMainProcessor {

    private static final String NAME = "DealNativeSnapshotMainProcessor";

    private static final Map<String, RcfDealBffInterfaceEnum> NAME_TO_ENUM = Arrays.stream(RcfDealBffInterfaceEnum.values())
            .collect(Collectors.toMap(
                    RcfDealBffInterfaceEnum::name,
                    i -> i,
                    (v1, v2) -> v1
            ));

    @Resource
    private DealRcfSwitcherService dealRcfSwitcher;
    @Resource
    private DealCategoryCacheService dealCategoryCacheService;
    @Resource
    private DealLayoutService dealLayoutService;
    @Resource
    private DealBffCacheAclService dealBffCacheAclService;
    @Resource
    private List<DealRcfCustomerProcessor> dealRcfCustomerProcessorList;
    @Resource
    private ShoppingGuideProductInfoService productInfoService;
    @Resource
    private NewDealSnapshotService newDealSnapshotService;
    @Resource
    private DouHuService douHuService;
    @Resource
    private DouHuBiz douHuBiz;

    public Response process(DealNativeSnapshotReq request, EnvCtx envCtx) {
        try {
            if (request.getDealGroupId() == null || request.getDealGroupId() <= 0) {
                return Response.fail("INVALID_PARAM");
            }
            RcfDealBffClientTypeEnum rcfClientType = fromRequestClientType(envCtx.getDztgClientTypeEnum());
            if (rcfClientType == null) {
                return Response.fail("INVALID_CLIENT_TYPE");
            }
            // 判断是否是新团详的团单
            if (productInfoService.isNewDeal(request, envCtx)) {
                DealRcfNativeSnapshot snapshot = newDealSnapshotService.getSnapshot(request, envCtx, rcfClientType);
                return Response.succeed(snapshot);
            }
            //缓存中获取团购分类
            DealGroupCategoryDTO dealGroupCategoryDTO = dealCategoryCacheService.get(request.getDealGroupId(), envCtx.isMt());
            if (dealGroupCategoryDTO == null || dealGroupCategoryDTO.getCategoryId() == null) {
                return Response.fail("NO_CATEGORY");
            }
            //灰度开关控制
            DealRcfSwitcherResult dealRcfSwitcherResult = dealRcfSwitcher.get(null == dealGroupCategoryDTO.getCategoryId() ? 0 : dealGroupCategoryDTO.getCategoryId(), null == dealGroupCategoryDTO.getServiceTypeId() ? 0 : dealGroupCategoryDTO.getServiceTypeId(), envCtx.getDztgClientTypeEnum(),
                    envCtx.getUserId(), envCtx.getVersion(), request.getMrnVersion()
            );
            if (!dealRcfSwitcherResult.isRender()) {
                return Response.fail(dealRcfSwitcherResult.getReason());
            }
            DealRcfNativeSnapshot snapshot = new DealRcfNativeSnapshot();
            snapshot.setDealFlexBoxConfig(buildDealFlexBoxCfg());
            //读取接口缓存
            DealBffCacheQueryResponse cacheQueryResponse = queryCacheData(request, rcfClientType);
            final DealBffResponseDTO bffResponse = parseBffData(cacheQueryResponse);
            JSONObject dealMainInterfaceJson = bffResponse.getBffResponse(RcfDealBffInterfaceEnum.dzdealbase);
            JSONObject dealStyleInterfaceJson = bffResponse.getBffResponse(RcfDealBffInterfaceEnum.dzdealstyle);
            if (dealMainInterfaceJson == null) {
                return Response.fail("NO_MAIN_INTERFACE_DATA");
            }
            JSONObject moduleConfigsModule = dealMainInterfaceJson.getJSONObject("moduleConfigsModule") != null ? dealMainInterfaceJson.getJSONObject("moduleConfigsModule") : dealStyleInterfaceJson.getJSONObject("moduleConfigsModule");
            //计算首屏模块
            snapshot.setDealLayoutComponents(buildDealLayoutComponents(moduleConfigsModule));
            //接口定制化改造
            for (DealRcfCustomerProcessor dealRcfCustomerProcessor : dealRcfCustomerProcessorList) {
                if (!dealRcfCustomerProcessor.canProcess(request, bffResponse)) {
                    continue;
                }
                dealRcfCustomerProcessor.customerProcess(request, bffResponse);
            }
            //数据合并
            snapshot.setFirstScreenBffCache(mergeFirstScreenBffCache(bffResponse));
            // AB实验
            ModuleAbConfig abTestResult = douHuService.getNativeDealDetailAbTestResult(envCtx);
            String expResult = douHuBiz.getExpResult(abTestResult);
            if (!LionConfigUtils.rcfSnapshotExpResultAllPass() && !LionConfigUtils.hitSnapshotGroup(expResult)){
                return Response.fail("UN_HIT_EXPERIMENT");
            }
            return Response.succeed(snapshot);
        } catch (Exception e) {
            log.error("DealNativeSnapshotFacade.process request:{},envCtx:{}", JSON.toJSONString(request), JSON.toJSONString(envCtx), e);
            return Response.fail("ERROR");
        }
    }

    private @NotNull DealBffCacheQueryResponse queryCacheData(DealNativeSnapshotReq request, RcfDealBffClientTypeEnum rcfClientType) {
        RcfDealBffCommonParamDTO param = new RcfDealBffCommonParamDTO();
        param.setDealGroupId(Objects.nonNull(request.getDealGroupId()) ? request.getDealGroupId() : 0);
        param.setShopId(Objects.nonNull(request.getPoiId()) ? request.getPoiId() : 0);
        param.setClientType(Objects.nonNull(rcfClientType.getCode()) ? rcfClientType.getCode() : 0);
        param.setCityId(Objects.nonNull(request.getCityId()) ? request.getCityId() : 0);
        param.setUserLng(Objects.nonNull(request.getUserLng()) ? request.getUserLng() : 0);
        param.setUserLat(Objects.nonNull(request.getUserLat()) ? request.getUserLat() : 0);
        param.setPageSource(request.getPageSource());
        param.setAppVersion(request.getAppVersion());
        DealBffCacheQueryResponse cacheQueryResponse = dealBffCacheAclService.query(param);
        if (cacheQueryResponse == null || cacheQueryResponse.getCacheDataMap() == null) {
            throw new RpcException("查询缓存失败，结果为空");
        }
        return cacheQueryResponse;
    }

    private RcfDealBffClientTypeEnum fromRequestClientType(DztgClientTypeEnum dztgClientType) {
        if (dztgClientType == DztgClientTypeEnum.MEITUAN_APP) {
            return RcfDealBffClientTypeEnum.MT_APP;
        }
        return null;
    }

    public DealBffResponseDTO parseBffData(DealBffCacheQueryResponse cacheQueryResponse) {
        Map<RcfDealBffInterfaceEnum, Object> result = new HashMap<>();
        for (Map.Entry<String, String> entry : cacheQueryResponse.getCacheDataMap().entrySet()) {
            RcfDealBffInterfaceEnum rcfDealBffInterfaceEnum = NAME_TO_ENUM.get(entry.getKey());
            if (rcfDealBffInterfaceEnum == null) {
                continue;
            }
            if (rcfDealBffInterfaceEnum.getReturnType() == RcfDealInterfaceReturnTypeEnum.String) {
                result.put(rcfDealBffInterfaceEnum, entry.getValue());
            } else if (rcfDealBffInterfaceEnum.getReturnType() == RcfDealInterfaceReturnTypeEnum.Collection) {
                result.put(rcfDealBffInterfaceEnum, JSON.parseArray(entry.getValue()));
            } else if (JSONValidator.from(entry.getValue()).validate()) {
                result.put(rcfDealBffInterfaceEnum, JSON.parseObject(entry.getValue()));
            } else {
                Cat.logError(new JSONException(String.format("%s接口返回值类型不是字符串，但是不能JSON反序列化", rcfDealBffInterfaceEnum)));
            }
        }
        return new DealBffResponseDTO(result);
    }

    public String buildDealLayoutComponents(JSONObject moduleConfigsModule) {
        if (moduleConfigsModule == null) {
            throw new IllegalArgumentException("FATAL ERROR!!!团详主接口中moduleConfigsModule字段为null，无法计算团详模块");
        }
        String key = moduleConfigsModule.getString("key");
        String extraInfo = moduleConfigsModule.getString("extraInfo");
        String generalInfo = moduleConfigsModule.getString("generalInfo");
        Map<String, ModuleItem> result = dealLayoutService.getDealLayout(key, extraInfo, generalInfo, new HashMap<>());
        if (MapUtils.isEmpty(result)) {
            throw new IllegalArgumentException("FATAL ERROR!!!团详布局计算失败，布局数据为空");
        }
        return JSON.toJSONString(result);
    }

    private String mergeFirstScreenBffCache(final DealBffResponseDTO bffResponse) {
        Map<String, Object> result = bffResponse.getBffResponse().entrySet().stream()
                .collect(Collectors.toMap(
                        entry -> entry.getKey().name(),
                        Map.Entry::getValue
                ));
        return JSON.toJSONString(result);
    }

    private DealFlexBoxCfg buildDealFlexBoxCfg() {
        DealFlexBoxCfg cfg = new DealFlexBoxCfg();
        cfg.setRender(true);
        cfg.setMtFlexboxTemplateUrl(Lion.getString(Environment.getAppName(), DealRcfNativeSnapshotConstant.FLEX_BOX_TEMPLATE_URL_LION_KEY));
        return cfg;
    }

    @MobileDo(id = 0xce69)
    @Data
    public static class Response {

        @MobileDo.MobileField(key = 0x8182)
        private String result;

        @MobileDo.MobileField(key = 0x50)
        private DealRcfNativeSnapshot dealRcfNativeSnapshot;

        public static Response succeed(DealRcfNativeSnapshot dealRcfNativeSnapshot) {
            Response response = new Response();
            response.setResult("SUCCESS");
            response.setDealRcfNativeSnapshot(dealRcfNativeSnapshot);
            return response;
        }

        public static Response fail(String result) {
            Response response = new Response();
            response.setResult(result);
            response.setDealRcfNativeSnapshot(getDefaultSnapshot());
            return response;
        }

        private static DealRcfNativeSnapshot getDefaultSnapshot() {
            DealRcfNativeSnapshot snapshot = new DealRcfNativeSnapshot();
            DealFlexBoxCfg cfg = new DealFlexBoxCfg();
            cfg.setRender(false);
            snapshot.setDealFlexBoxConfig(cfg);
            return snapshot;
        }

    }
}
