package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.style;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.publishcategory.dto.ChannelDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.deal.style.dto.DealGroupDzxInfo;
import com.dianping.deal.style.dto.StyleAbConfig;
import com.dianping.deal.style.dto.StyleExp;
import com.dianping.deal.style.dto.StyleResponse;
import com.dianping.deal.style.enums.DzxPlatform;
import com.dianping.lion.Environment;
import com.dianping.lion.client.ConfigRepository;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealStyleWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ParallDealStyleWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.MttgVersion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealStyles;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.*;
import com.dianping.mobile.mapi.dztgdetail.common.model.CategoryToDtChannelDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.AbConfigBo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.AbConfigDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DealStyle;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DealStyleRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.MTTemplateKey;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleAbConfigBo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleAbConfigDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.DealBaseDo;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import com.dianping.mobile.mapi.dztgdetail.util.DealStyleUtil;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.SwitchUtils;
import com.dianping.tuangou.dztg.bjwrapper.api.dto.MtDealDto;
import com.google.common.collect.Maps;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Future;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.APP_KEY;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.CATEGORY_TO_DT;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2023/8/16
 * @since mapi-dztgdetail-web
 */
public class ParallDealStyleProcessor extends AbsDealProcessor {

    private static final List<String> GC_DEFAULT_STYLE_LIST = com.google.common.collect.Lists.newArrayList("scenic","travel","travel_selected","food","default");

    private static List<CategoryToDtChannelDTO> CATEGORY_TO_DT_CHANNEL_LIST = Lion.getList(APP_KEY, CATEGORY_TO_DT, CategoryToDtChannelDTO.class, com.google.common.collect.Lists.newArrayList());
    static {
        ConfigRepository configRepository = Lion.getConfigRepository(LionConstants.APP_KEY);
        configRepository.addConfigListener(LionConstants.CATEGORY_TO_DT, configEvent -> {
            CATEGORY_TO_DT_CHANNEL_LIST = JSON.parseArray(configEvent.getValue(), CategoryToDtChannelDTO.class);
        });
    }

    @Autowired
    private DealStyleWrapper dealStyleWrapper;

    @Autowired
    private ParallDealStyleWrapper parallDealStyleWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        return ctx.getEnvCtx() != null && !ctx.getEnvCtx().isFromH5();
    }

    @Override
    public void prepare(DealCtx ctx) {
        if(ctx.isMt()) {
            Future styleFuture = dealStyleWrapper.getStyleFutureForMt(ctx);
            Future dzxFuture = dealStyleWrapper.prepareGetDealGroupDzxInfo(ctx.getMtId(), ctx.getEnvCtx().isAndroid() ? DzxPlatform.MT_APP_ANDROID.type : DzxPlatform.MT_APP_IOS.type);
            Future mtDealDtoListFutureParall = parallDealStyleWrapper.preMtDealDtoList(Lists.newArrayList(ctx.getMtId()));

            ctx.getFutureCtx().setStyleFuture(styleFuture);
            ctx.getFutureCtx().setDzxFuture(dzxFuture);
            ctx.getFutureCtx().setMtDealDtoListFutureParall(mtDealDtoListFutureParall);
        } else {
            Future dpStyleFuture = dealStyleWrapper.getStyleFutureForDp(ctx);
            ctx.getFutureCtx().setStyleFuture(dpStyleFuture);
        }
    }

    @Override
    public void process(DealCtx ctx) {
        ctx.setStyleResponse(dealStyleWrapper.getFutureResult(ctx.getFutureCtx().getStyleFuture()));
        ctx.setDealGroupDzxInfo(dealStyleWrapper.getFutureResult(ctx.getFutureCtx().getDzxFuture()));
        ctx.setMtDealDtoListParall(dealStyleWrapper.getFutureResult(ctx.getFutureCtx().getMtDealDtoListFutureParall()));
        if(ctx.isMt()) {
            if(DealUtils.isPreviewDeal(ctx)) {
                MTTemplateKey mtTemplateKey = buildPreviewTemplateKey(ctx);
                ctx.setMtTemplateKey(mtTemplateKey);
            }else {
                MTTemplateKey mtTemplateKey = buildTemplateKey(ctx);
                ctx.setMtTemplateKey(mtTemplateKey);
            }
        } else {
            DealStyle dealStyle = getDealStyle(ctx);
            ctx.setDealStyle(dealStyle);
        }
    }

    public MTTemplateKey buildTemplateKey(DealCtx dealCtx) {
        if (SwitchUtils.isAllNotDzx()) {
            return getDegradeTemplateKey();
        }

        boolean isAndroid = dealCtx.getEnvCtx().isAndroid();
        DealGroupDzxInfo dzxInfo = dealCtx.getDealGroupDzxInfo();

        if (dzxInfo == null || !dzxInfo.isDzx()) {
            return getDegradeTemplateKey();
        }

        List<MtDealDto> mtDealDtoList = dealCtx.getMtDealDtoListParall();
        MtDealDto dealModel = CollectionUtils.isEmpty(mtDealDtoList) ? null : mtDealDtoList.get(0);
        boolean canDegrade = canDegradeMtDealModel(dealCtx, isAndroid, dealCtx.getEnvCtx().getVersion());
        if (dealModel == null && !canDegrade) {
            return getDefaultTemplateKey(dzxInfo.getDpDealGroupId(), dzxInfo.isDzx());
        } else if (dealModel == null && canDegrade) {
            dealModel = new MtDealDto();
            // 可降级的已经判断过肯定不为空
            dealModel.setChannel(dealCtx.getChannelDTO().getChannelDTO().getChannelEn());
        }

        if (StringUtils.isEmpty(dealCtx.getReqChannel())) {
            dealCtx.setReqChannel(dealModel.getChannel());
        }

        MTTemplateKey templateKeyDo = new MTTemplateKey();
        templateKeyDo.setDzx(dzxInfo.isDzx());
        templateKeyDo.setDpOrder(isDpOrder(dzxInfo.getDpDealGroupId(), templateKeyDo.isDzx()));

        String key = generateKey(
                dealCtx,
                dealModel.getDt(),
                dealModel.getFrontPoiCates(), //丽人的subId=0时，需要根据poiCateId去区分
                dealCtx.getEnvCtx().getVersion(),
                dealCtx.getReqChannel(),
                isAndroid,
                dealCtx.isExternal()
        );

        StyleResponse styleResponse = dealCtx.getStyleResponse();
        if (isStyleDegrade(dealCtx, styleResponse)) {
            styleResponse = new StyleResponse();
            styleResponse.setStyle(dealModel.getChannel());
        }
        templateKeyDo.setKey(styleResponse != null && StringUtils.isNotBlank(styleResponse.getStyle()) ? styleResponse.getStyle() : key);
        if (styleResponse != null && CollectionUtils.isNotEmpty(styleResponse.getStyleAbConfigs())) {
            List<ModuleAbConfigDo> moduleAbConfigDos = new ArrayList<>();

            for (StyleAbConfig config : styleResponse.getStyleAbConfigs()) {

                ModuleAbConfigDo moduleAbConfigDo = new ModuleAbConfigDo();
                moduleAbConfigDo.setKey(config.getKey());
                List<AbConfigDo> abConfigDos  = new ArrayList<>();

                if (StringUtils.isNotBlank(config.getKey()) && CollectionUtils.isNotEmpty(config.getStyleExps())) {
                    for (StyleExp exp : config.getStyleExps()) {
                        AbConfigDo abConfigBo = new AbConfigDo(exp.getExpId(), exp.getExpResult(), exp.getExpBiInfo());
                        abConfigDos.add(abConfigBo);
                    }
                }

                moduleAbConfigDo.setConfigs(abConfigDos);
                moduleAbConfigDos.add(moduleAbConfigDo);

            }

            templateKeyDo.setModuleAbConfigs(moduleAbConfigDos);
        }

        return templateKeyDo;
    }

    public MTTemplateKey buildPreviewTemplateKey(DealCtx dealCtx) {
        boolean isAndroid = dealCtx.getEnvCtx().isAndroid();
        DealGroupDzxInfo dzxInfo = dealCtx.getDealGroupDzxInfo();
        MTTemplateKey templateKeyDo = new MTTemplateKey();
        templateKeyDo.setDzx(dzxInfo.isDzx());
        templateKeyDo.setDpOrder(isDpOrder(dzxInfo.getDpDealGroupId(), templateKeyDo.isDzx()));

        CategoryToDtChannelDTO dtChannelInfo = getDtChannelByCategory(dealCtx.getCategoryId());
        int dt = Objects.nonNull(dtChannelInfo) ? dtChannelInfo.getDt() : getDtByCategoryId(dealCtx.getCategoryId());
        String channel = Objects.nonNull(dtChannelInfo) ? dtChannelInfo.getChannel() : "";
        List<Integer> frontPoiCates = Objects.nonNull(dtChannelInfo) ? dtChannelInfo.getFrontPoiCate() : Lists.newArrayList();

        String key = generateKey(
                dealCtx,
                dt,
                frontPoiCates, //丽人的subId=0时，需要根据poiCateId去区分
                dealCtx.getEnvCtx().getVersion(),
                channel,
                isAndroid,
                dealCtx.isExternal()
        );

        StyleResponse styleResponse = dealCtx.getStyleResponse();
        if (isStyleDegrade(dealCtx, styleResponse)) {
            styleResponse = new StyleResponse();
            styleResponse.setStyle(dealCtx.getChannelDTO().getChannelDTO().getChannelEn());
        }
        templateKeyDo.setKey(styleResponse != null && StringUtils.isNotBlank(styleResponse.getStyle()) ? styleResponse.getStyle() : key);

        if (styleResponse != null && CollectionUtils.isNotEmpty(styleResponse.getStyleAbConfigs())) {
            List<ModuleAbConfigDo> moduleAbConfigDos = new ArrayList<>();

            for (StyleAbConfig config : styleResponse.getStyleAbConfigs()) {

                ModuleAbConfigDo moduleAbConfigDo = new ModuleAbConfigDo();
                moduleAbConfigDo.setKey(config.getKey());
                List<AbConfigDo> abConfigDos  = new ArrayList<>();

                if (StringUtils.isNotBlank(config.getKey()) && CollectionUtils.isNotEmpty(config.getStyleExps())) {
                    for (StyleExp exp : config.getStyleExps()) {
                        AbConfigDo abConfigBo = new AbConfigDo(exp.getExpId(), exp.getExpResult(), exp.getExpBiInfo());
                        abConfigDos.add(abConfigBo);
                    }
                }

                moduleAbConfigDo.setConfigs(abConfigDos);
                moduleAbConfigDos.add(moduleAbConfigDo);

            }

            templateKeyDo.setModuleAbConfigs(moduleAbConfigDos);
        }

        return templateKeyDo;
    }

    private boolean isStyleDegrade(DealCtx dealCtx, StyleResponse styleResponse) {
        return (styleResponse == null || StringUtils.isBlank(styleResponse.getStyle())) && Environment.isTestEnv()
                && (LionConfigUtils.isDealCanDegradeMtStyle(getServiceTypeId(dealCtx.getDealGroupDTO()))
                    || LionConfigUtils.isDealCanDegradeMtStyleByCategoryId(getCategoryId(dealCtx.getDealGroupDTO())));
    }

    private Long getServiceTypeId(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO == null || dealGroupDTO.getCategory() == null) {
            return null;
        }
        return dealGroupDTO.getCategory().getServiceTypeId();
    }

    private Long getCategoryId(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO == null || dealGroupDTO.getCategory() == null) {
            return null;
        }
        return dealGroupDTO.getCategory().getCategoryId();
    }

    private boolean canDegradeMtDealModel(DealCtx dealCtx, boolean isAndroid, String ver) {
        if (dealCtx.getChannelDTO() == null || dealCtx.getChannelDTO().getChannelDTO() == null) {
            return false;
        }
        if (!LionConfigUtils.isDealCanDegradeMtStyle(getServiceTypeId(dealCtx.getDealGroupDTO()))
                && !LionConfigUtils.isDealCanDegradeMtStyleByCategoryId(getCategoryId(dealCtx.getDealGroupDTO()))) {
            return false;
        }
        
        String channel = dealCtx.getChannelDTO().getChannelDTO().getChannelEn();
        if (StringUtils.isEmpty(channel)) {
            return false;
        }
        MttgVersion version = new MttgVersion(ver);
        KeyEnum keyEnum = KeyEnum.findBgEnum(channel, version, isAndroid);
        return !keyEnum.equals(KeyEnum.BEAUTY) && !keyEnum.equals(KeyEnum.BEAUTY_DEFAULT) && !keyEnum.equals(KeyEnum.ENTERTAINMENT);
    }

    private String generateKey(
            DealCtx dealCtx, int dt, List cateIds, String ver, String channel, boolean isAndroid, boolean isExternal) {

        String key = KeyEnum.DEAFAULT.getChannel();

        try {

            MttgVersion version = new MttgVersion(ver);
            KeyEnum keyEnum = KeyEnum.findBgEnum(channel, version, isAndroid);

            if (keyEnum.equals(KeyEnum.BEAUTY) || keyEnum.equals(KeyEnum.BEAUTY_DEFAULT)) {
                key = generateBeautyKey(dt, cateIds, keyEnum);
            } else if (keyEnum.equals(KeyEnum.CHILDREN)) {
                key = generateChildrenKey(dealCtx);
            } else if (keyEnum.equals(KeyEnum.ENTERTAINMENT)) {
                key = generateEntertainmentKey(dt);
            } else if (keyEnum.equals(KeyEnum.MEDICINE)) {
                key = KeyEnum.DEAFAULT.getChannel();
            } else {
                key = keyEnum.getChannel();
            }

        } catch (Exception e) {
            logger.error("MTDetailTemplateFacadeImpl.generateKey error", e);
            return key;
        }


        if (isExternal) {
            //外部请求（如快手小程序）支持的样式
            List<String> configs = Lion.getList("mapi-mttgdetail-web.external.support.styles", String.class, new ArrayList<String>());
            if (CollectionUtils.isNotEmpty(configs) && !configs.contains(key)) {
                key =  KeyEnum.DEAFAULT.getChannel();
            }
        }

        return key;
    }

    private String generateBeautyKey(int subId, List cateIds, KeyEnum keyEnum) {
        if (keyEnum.equals(KeyEnum.BEAUTY)) {
            keyEnum = KeyEnum.DEAFAULT;
        }

        String key = keyEnum.getChannel();

        if (CollectionUtils.isNotEmpty(cateIds)) {
            if (!cateIds.contains(PoiSubCate.BEAUTYHAIR.getValue()) && !cateIds.contains(PoiSubCate.BEAUTYNAIL.getValue())) {
                key = keyEnum.getChannel();
            } else if (cateIds.contains(PoiSubCate.BEAUTYHAIR.getValue())) {
                key = PoiSubCate.BEAUTYHAIR.getName();
            } else if (cateIds.contains(PoiSubCate.BEAUTYNAIL.getValue())) {
                key = PoiSubCate.BEAUTYNAIL.getName();
            }
        } else {
            BeautySubType beautySubType = BeautySubType.findSubTypeName(subId);

            if (beautySubType == BeautySubType.BEAUTIFYHAIR || beautySubType == BeautySubType.BEAUTIFYHAIRDP || beautySubType == BeautySubType.BEAUTIFYHAIRDP1) {
                key = BeautySubType.BEAUTIFYHAIR.getSubTypeName();
            } else if (beautySubType == BeautySubType.BEAUTIFYNAILORHANDS || beautySubType == BeautySubType.BEAUTIFYNAILOREYELASH) {
                key = BeautySubType.BEAUTIFYNAILOREYELASH.getSubTypeName();
            } else {
                key = keyEnum.getChannel();
            }
        }

        return key;
    }

    private String generateEntertainmentKey(int dt) {
        if (dt == 48 || dt == 49 || dt == 5303) {
            return KeyEnum.JOY_MASSAGE.getChannel();
        }
        return KeyEnum.DEAFAULT.getChannel();
    }

    private String generateChildrenKey(DealCtx dealCtx) {
        try {
            List<AttributeDTO> attrs = dealCtx.getAttrs();
            if(attrs == null) {
                return KeyEnum.DEAFAULT.getChannel();
            }
            for (AttributeDTO attributeDTO : attrs) {
                if (attributeDTO.getName().equals(DealAttrCons.CATEGORY)) {
                    if (attributeDTO.getValue().contains(DealAttrCons.CHILDREN_EDU)
                            || attributeDTO.getValue().contains(DealAttrCons.CHILDREN_PHOTO)
                            || attributeDTO.getValue().contains(DealAttrCons.CHILDREN_PREGNANT_PHOTO)) {
                        return KeyEnum.CHILDREN_PHOTOEDUTG.getChannel();
                    }
                }
            }
        } catch (Exception e) {
            logger.error("MTDetailTemplateFacadeImpl.generateChildrenKey error", e);
            return KeyEnum.DEAFAULT.getChannel();
        }
        return KeyEnum.DEAFAULT.getChannel();
    }

    private MTTemplateKey getDegradeTemplateKey() {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.style.ParallDealStyleProcessor.getDegradeTemplateKey()");
        MTTemplateKey templateKeyDo = new MTTemplateKey();
        templateKeyDo.setKey(KeyEnum.DEAFAULT.getChannel());
        templateKeyDo.setDzx(false);
        templateKeyDo.setDpOrder(false);
        templateKeyDo.setExtraInfo("newtuandeal");
        templateKeyDo.setDegrade(true);
        return templateKeyDo;
    }

    private MTTemplateKey getDefaultTemplateKey(int dpDealGroupId, boolean isDzx) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.style.ParallDealStyleProcessor.getDefaultTemplateKey(int,boolean)");
        MTTemplateKey templateKeyDo = new MTTemplateKey();
        templateKeyDo.setKey(KeyEnum.DEAFAULT.getChannel());
        templateKeyDo.setDzx(isDzx);
        templateKeyDo.setExtraInfo("newtuandeal");
        templateKeyDo.setDpOrder(isDpOrder(dpDealGroupId, isDzx));
        templateKeyDo.setDegrade(true);
        return templateKeyDo;
    }

    private boolean isDpOrder(int dpDealGroupId, boolean isDzx) {
        //应对极端服务挂掉情况，全切回美团交易流程
        return (isDzx) && !SwitchUtils.isAllNotDpOrder() && dpDealGroupId > 0;
    }

    public DealStyle getDefaultDealStyle(DealCtx dealCtx) {
        List<AttributeDTO> attributes = dealCtx.getAttrs();

        DealStyleRequest request = new DealStyleRequest();
        request.setCategoryValues(AttributeUtils.getAttributeValues("category", attributes));
        request.setSelectValues(AttributeUtils.getAttributeValues("isselecteddeal", attributes));
        request.setVersion(dealCtx.getEnvCtx().getVersion());
        DealStyle result = transfer(DealStyleUtil.getDealStyle(request));

        // 如果是以下的DealStyle，走gcdefault
        List<String> gcdefaultDealStyles = Lion.getList("mapi-tgdetail-web.unsupported.deal.config", String.class,
                GC_DEFAULT_STYLE_LIST);
        if(result != null && gcdefaultDealStyles.contains(result.getModuleKey())) {
            result = new DealStyle("gcdefault");
        }

        return result;
    }

    private DealStyle transfer(DealStyle dealStyle) {
        if (dealStyle == null) {
            return null;
        }
        return new DealStyle(dealStyle.getModuleKey());
    }

    public DealStyle getDealStyle(DealCtx dealCtx) {
        DealStyle defaultValue = getDefaultDealStyle(dealCtx);

        String style = null;
        StyleResponse styleResponse = dealCtx.getStyleResponse();

        if (styleResponse == null || org.apache.commons.lang3.StringUtils.isBlank(styleResponse.getStyle())) {
            return defaultValue;
        } else {
            style = styleResponse.getStyle();

            if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(style, DealStyles.TRAVEL.getModuleKey()) &&
                    org.apache.commons.lang3.StringUtils.equalsIgnoreCase(defaultValue.getModuleKey(), DealStyles.TRAVEL_SELECTED.getModuleKey())) {

                return defaultValue;
            }

            if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(style, DealStyles.HOTEL.getModuleKey()) &&
                    org.apache.commons.lang3.StringUtils.equalsIgnoreCase(defaultValue.getModuleKey(), DealStyles.HOTEL_SELECTED.getModuleKey())) {

                return defaultValue;
            }

            // 如果是以下的DealStyle，走gcdefault
            List<String> gcdefaultDealStyles = Lion.getList("mapi-tgdetail-web.unsupported.deal.config", String.class,
                    GC_DEFAULT_STYLE_LIST);
            if(gcdefaultDealStyles.contains(style)) {
                style = new DealStyle("gcdefault").getModuleKey();
            }

            DealStyle dealStyle = new DealStyle(style);

            if (CollectionUtils.isNotEmpty(styleResponse.getStyleAbConfigs())) {
                List<ModuleAbConfigBo> moduleAbConfigBos = new ArrayList<>();

                for (StyleAbConfig config : styleResponse.getStyleAbConfigs()) {

                    ModuleAbConfigBo moduleAbConfigBo = new ModuleAbConfigBo();
                    moduleAbConfigBo.setKey(config.getKey());
                    List<AbConfigBo> abConfigBos  = new ArrayList<>();

                    if (org.apache.commons.lang3.StringUtils.isNotBlank(config.getKey()) && CollectionUtils.isNotEmpty(config.getStyleExps())) {
                        for (StyleExp exp : config.getStyleExps()) {
                            AbConfigBo abConfigBo = new AbConfigBo(exp.getExpId(), exp.getExpResult(), exp.getExpBiInfo());
                            abConfigBos.add(abConfigBo);
                        }
                    }

                    moduleAbConfigBo.setConfigs(abConfigBos);
                    moduleAbConfigBos.add(moduleAbConfigBo);

                }

                dealStyle.setModuleAbConfigs(moduleAbConfigBos);
            }

            return dealStyle;
        }
    }

    // 根据category查找对应的枚举
    public static CategoryToDtChannelDTO getDtChannelByCategory(int category) {
        for (CategoryToDtChannelDTO item : CATEGORY_TO_DT_CHANNEL_LIST) {
            if (item.getCategory() == category) {
                return item;
            }
        }
        return null;
    }

    public static Integer getDtByCategoryId(int categoryId){
        CategoryToDtEnum categoryToDtEnum = CategoryToDtEnum.getByCategory(categoryId);
        return Objects.nonNull(categoryToDtEnum) ? categoryToDtEnum.getDt() : 0;
    }
}
