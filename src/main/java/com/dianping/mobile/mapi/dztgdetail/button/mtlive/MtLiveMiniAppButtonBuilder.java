package com.dianping.mobile.mapi.dztgdetail.button.mtlive;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MtLiveSaleStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.helper.PriceHelper;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.general.product.query.center.client.dto.PriceDTO;
import com.sankuai.general.product.query.center.client.util.DealGroupUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-12-25
 * @desc 美团美播小程序购买按钮构造器
 */
public class MtLiveMiniAppButtonBuilder extends AbstractButtonBuilder {

    private static final String PREPAY_TEXT_TEMPLATE = "立即支付预付款";
    private static final String FINAL_PAY_TEXT_TEMPLATE = "剩余尾款￥%s";

    @Override
    protected void doBuild(DealCtx context, ButtonBuilderChain chain) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.button.mtlive.MtLiveMiniAppButtonBuilder.doBuild(DealCtx,ButtonBuilderChain)");
        buildButton(context);
        chain.build(context);
    }

    private void buildButton(DealCtx context) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.button.mtlive.MtLiveMiniAppButtonBuilder.buildButton(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        DealBuyBtn button = buildOriginButton(context, MtLiveSaleStatusEnum.SNAP_UP_NOW.getStatusDesc());
        PriceDTO priceDTO = getPrice(context);
        // 预付商品且预付价格不为空时，构造预付按钮
        if (enableBuildPrepayBtn(context, priceDTO)) {
            buildPrepayButton(context, button, priceDTO);
        } else {
            buildNormalButton(context, button);
        }
        context.addButton(button);
    }

    private String getBuyUrl(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.button.mtlive.MtLiveMiniAppButtonBuilder.getBuyUrl(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        int dealId4P = ctx.getDealId4P();
        long poiId4P = ctx.getLongPoiId4PFromReq();
        int cityId4P = ctx.getCityId4P();
        return String.format("/gnc/pages/deal/submitorder/submitorder?dealid=%s&shopid=%s&cityid=%s",
                dealId4P, poiId4P, cityId4P);
    }
    
    private String buildBtnTag(PriceDisplayDTO normalPrice) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.button.mtlive.MtLiveMiniAppButtonBuilder.buildBtnTag(com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO)");
        if (CollectionUtils.isEmpty(normalPrice.getUsedPromos())) {
            return StringUtils.EMPTY;
        }
        BigDecimal totalReducePromo = new BigDecimal(0);
        for (PromoDTO promoDTO : normalPrice.getUsedPromos()) {
            if (Objects.isNull(promoDTO) || Objects.isNull(promoDTO.getAmount())) {
                continue;
            }
            if (promoDTO.getIdentity().getPromoType() == PromoTypeEnum.NORMAL_PROMO.getType()) {
                totalReducePromo = totalReducePromo.add(promoDTO.getAmount());
            }
        }
        return totalReducePromo.compareTo(BigDecimal.ZERO) > 0 ? String.format("省¥%s",
                totalReducePromo.stripTrailingZeros().toPlainString()) : null;
    }

    private void buildNormalButton(DealCtx context, DealBuyBtn button) {
        PriceDisplayDTO mtLivePrice = PriceHelper.getNormalPrice(context);
        button.setPriceStr(formatPrice(mtLivePrice.getPrice()));
        button.setBtnText(String.format("￥%s %s", mtLivePrice.getPrice(), context.getMtLiveSaleStatusEnum().getStatusDesc()));
        button.setBtnIcons(Lists.newArrayList());
        button.setBtnDesc(StringUtils.EMPTY);
        button.setBtnTag(buildBtnTag(mtLivePrice));
        button.setRedirectUrl(getBuyUrl(context));
        button.setSaleStatus(context.getMtLiveSaleStatusEnum().getStatusName());
    }

    private void buildPrepayButton(DealCtx context, DealBuyBtn button, PriceDTO priceDTO) {
        String prePayPriceStr = formatPrice(DealGroupUtils.convertPrice(priceDTO.getPrePayPrice()));
        button.setBtnTitle(PREPAY_TEXT_TEMPLATE);
        button.setPriceStr(prePayPriceStr);
        button.setBtnText(String.format("￥%s %s", prePayPriceStr, PREPAY_TEXT_TEMPLATE));
        button.setBtnIcons(Lists.newArrayList());
        button.setBtnDesc(StringUtils.EMPTY);
        button.setBtnTag(buildPrepayBtnTag(priceDTO));
        // 跳转链接会在主流程中被替换
        button.setRedirectUrl(getBuyUrl(context));
        button.setSaleStatus(context.getMtLiveSaleStatusEnum().getStatusName());
    }

    private String buildPrepayBtnTag(PriceDTO priceDTO) {
        String finalPayPriceStr = (Objects.isNull(priceDTO) || Objects.isNull(priceDTO.getFinalPayPrice())) ? StringUtils.EMPTY : formatPrice(DealGroupUtils.convertPrice(priceDTO.getFinalPayPrice()));
        return StringUtils.isEmpty(finalPayPriceStr) ? StringUtils.EMPTY : String.format(FINAL_PAY_TEXT_TEMPLATE, finalPayPriceStr);
    }

    private PriceDTO getPrice(DealCtx context) {
        if (Objects.isNull(context.getDealGroupDTO())
                || CollectionUtils.isEmpty(context.getDealGroupDTO().getDeals())
                || Objects.isNull(context.getDealGroupDTO().getDeals().get(0))) {
            return null;
        }
        return context.getDealGroupDTO().getDeals().get(0).getPrice();
    }

    private boolean enableBuildPrepayBtn(DealCtx context, PriceDTO priceDTO) {
        boolean isPrepay = DealUtils.isPrePayDeal(context);
        EnvCtx envCtx = context.getEnvCtx();
        if (Objects.isNull(envCtx)) {
            return false;
        }
        return isPrepay && Objects.nonNull(priceDTO)
                && Objects.nonNull(priceDTO.getPrePayPrice())
                && DztgClientTypeEnum.MEITUAN_LIVE_WEIXIN_MINIAPP.equals(envCtx.getDztgClientTypeEnum());
    }
}
