package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MTMiniAppHideInformationHelper {
    public static boolean needHideInformation(EnvCtx envCtx) {
        return isMTMiniApp(envCtx) && isUnLogin(envCtx);
    }

    private static boolean isMTMiniApp(EnvCtx envCtx) {
        return envCtx.isMtMiniApp();
    }

    private static boolean isUnLogin(EnvCtx envCtx) {
        return !envCtx.isLogin();
    }
}
