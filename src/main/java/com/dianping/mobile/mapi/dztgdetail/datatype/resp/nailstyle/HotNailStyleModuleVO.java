package com.dianping.mobile.mapi.dztgdetail.datatype.resp.nailstyle;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ActionTextVO;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-03
 * @desc 热门美甲款式模型
 */
@Data
@MobileDo(id = 0x802a)
public class HotNailStyleModuleVO implements Serializable {
    @FieldDoc(description = "背景图")
    @MobileDo.MobileField(key = 0x64c7)
    private String bgImgUrl;

    @FieldDoc(description = "theme:表示展示主题、热度的样式；tag:表示展示款式标签的样式")
    @MobileDo.MobileField(key = 0x6191)
    private String displayStyle;

    @FieldDoc(description = "切到更多款式推荐时的切换文案")
    @MobileDo.MobileField(key = 0x42e2)
    private ActionTextVO actionText;

    @FieldDoc(description = "热门美甲款式列表")
    @MobileDo.MobileField(key = 0x52f)
    private List<HotNailStyleVO> hotNailStyles;

    @FieldDoc(description = "查看更多热门款式icon")
    @MobileDo.MobileField(key = 0x145c)
    private String moreStyleIcon;

    @FieldDoc(description = "查看更多热门款式跳链")
    @MobileDo.MobileField(key = 0x9802)
    private String moreStyleUrl;

    @FieldDoc(description = "查看更多热门款式引导语")
    @MobileDo.MobileField(key = 0x1f07)
    private String moreStyleText;

    @FieldDoc(description = "热门美甲款式副标题")
    @MobileDo.MobileField(key = 0xd894)
    private String subTitle;

    @FieldDoc(description = "热门美甲款式模块标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "ab实验")
    @MobileDo.MobileField(key = 0xbae3)
    private List<ModuleAbConfig> moduleAbConfigs;

}
