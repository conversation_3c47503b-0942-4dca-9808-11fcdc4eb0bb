package com.dianping.mobile.mapi.dztgdetail.common.enums;

/**
 * @Author: zheng<PERSON><PERSON><EMAIL>
 * @Date: 2024/11/1
 */
public enum EntityTypeEnum {
    POI(1,"poi"),
    DEAL(5,"deal");

    private int value;

    private String name;

    EntityTypeEnum(int value, String name){
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static EntityTypeEnum valueOf(int value) {

        for (EntityTypeEnum entityTypeEnum : values()) {
            if (entityTypeEnum.value == value) {
                return entityTypeEnum;
            }
        }
        return null;
    }
}
