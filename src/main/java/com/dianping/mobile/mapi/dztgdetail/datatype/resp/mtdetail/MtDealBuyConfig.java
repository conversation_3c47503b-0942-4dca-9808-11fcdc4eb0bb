package com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by wa<PERSON><PERSON><PERSON> on 16/4/20.
 */
@MobileDo(id = 0x25df)
@Data
public class MtDealBuyConfig implements Serializable {

    @MobileField(key = 0x6340)
    private boolean priceStrikeThrough;

    @MobileField(key = 0xf550)
    private boolean buttonEnable;

    @MobileField(key = 0x5faf)
    private String buttonText;

}
