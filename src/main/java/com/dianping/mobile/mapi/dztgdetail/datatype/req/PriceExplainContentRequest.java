package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
@MobileRequest
public class PriceExplainContentRequest implements IMobileRequest {

    @MobileRequest.Param(name = "dpDealGroupId")
    private Long dpDealGroupId;

    @MobileRequest.Param(name = "dpLongShopId")
    private Long dpLongShopId;

    public Long getDpDealGroupId() {
        return dpDealGroupId;
    }

    public void setDpDealGroupId(Long dealGroupId) {
        this.dpDealGroupId = dealGroupId;
    }

    public Long getDpLongShopId() {
        return dpLongShopId;
    }

    public void setDpLongShopId(Long dpLongShopId) {
        this.dpLongShopId = dpLongShopId;
    }

}
