package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.poi.distance.dto.CoordType;
import com.dianping.poi.distance.dto.GeoPoint;
import com.dianping.poi.util.CoordTransfer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

@Slf4j
public class GeoUtil {

    private static final double EARTH_RADIUS = 6378137;

    public static GeoPoint transformGeo84To02(double lng, double lat) {
        try {
            GeoPoint gPoint = new GeoPoint(lat, lng);
            return CoordTransfer.coordTranfer(gPoint, CoordType.GPS, CoordType.GCJ02);
        } catch (Exception e) {
            log.error("transform geo from 84 to 02 failed!lng is {},lat is {}", lng, lat, e);
        }
        return null;
    }

    /**
     * 转化为弧度(rad)
     */
    private static double rad(double d) {
        return d * Math.PI / 180.0;
    }

    /**
     * 基于googleMap中的算法得到两经纬度之间的距离,计算精度与谷歌地图的距离精度差不多，相差范围在0.2米以下
     *
     * @param lon1 第一点的精度
     * @param lat1 第一点的纬度
     * @param lon2 第二点的精度
     * @param lat2 第二点的纬度
     * @return 返回的距离，单位km
     */
    public static String getDistance(double lon1, double lat1, double lon2, double lat2) {
        String distanceText;
        double radLat1 = rad(lat1);
        double radLat2 = rad(lat2);
        double a = radLat1 - radLat2;
        double b = rad(lon1) - rad(lon2);
        double distance = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2)
                + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
        distance = distance * EARTH_RADIUS;
        if (Double.isNaN(distance) || Double.isInfinite(distance)) {
            distanceText = StringUtils.EMPTY;
        } else if (distance >= 1000) {
            if (distance > 99000) {
                distanceText = ">99km";
            } else {
                distance /= 1000;
                BigDecimal bigDecimal = BigDecimal.valueOf(distance);
                distanceText = bigDecimal.setScale(1, BigDecimal.ROUND_HALF_UP) + "km";
            }
        } else {
            BigDecimal bigDecimal = BigDecimal.valueOf(distance);
            distanceText = bigDecimal.setScale(0, BigDecimal.ROUND_HALF_UP) + "m";
        }
        return distanceText;
    }

    /**
     * 基于googleMap中的算法得到两经纬度之间的距离,计算精度与谷歌地图的距离精度差不多，相差范围在0.2米以下
     *
     * @param lon1 第一点的精度
     * @param lat1 第一点的纬度
     * @param lon2 第二点的精度
     * @param lat2 第二点的纬度
     * @return 返回的距离
     */
    public static BigDecimal getDistanceNum(double lon1, double lat1, double lon2, double lat2) {
        double radLat1 = rad(lat1);
        double radLat2 = rad(lat2);
        double a = radLat1 - radLat2;
        double b = rad(lon1) - rad(lon2);
        double distance = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2)
                + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
        distance = distance * EARTH_RADIUS;
        if (Double.isNaN(distance) || Double.isInfinite(distance)) {
            return null;
        }
        try {
            return BigDecimal.valueOf(distance);
        } catch (Exception e) {
            log.error("distanceNum转BigDecimal失败", e);
            return null;
        }
    }
}