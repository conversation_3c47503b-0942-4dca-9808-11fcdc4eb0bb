package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.gis.remote.dto.CityInfoDTO;
import com.dianping.gis.remote.service.CityInfoService;
import com.google.common.collect.Lists;
import com.sankuai.sinai.data.api.dto.NormPhone;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class PhoneNumberHelper {

    private static final Logger log = LoggerFactory.getLogger(PhoneNumberHelper.class);

    public static List<String> format(List<NormPhone> normPhoneList) {
        List<String> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(normPhoneList)) {
            return result;
        }
        for (NormPhone normPhone : normPhoneList) {
            StringBuilder stringBuilder = new StringBuilder();
            if (StringUtils.isNotBlank(normPhone.getAreaCode())) {
                stringBuilder.append(normPhone.getAreaCode()).append("-");
            }
            stringBuilder.append(normPhone.getEntity());
            if (StringUtils.isNotBlank(normPhone.getBranch())) {
                stringBuilder.append("-").append(normPhone.getBranch());
            }
            result.add(stringBuilder.toString());
        }
        return result;
    }

}
