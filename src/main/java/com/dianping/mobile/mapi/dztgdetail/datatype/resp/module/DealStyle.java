package com.dianping.mobile.mapi.dztgdetail.datatype.resp.module;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17/9/22.
 */
@TypeDoc(description = "团单样式数据模型")
@MobileDo(name = "DealStyle")
public class DealStyle {

    @FieldDoc(description = "key字段")
    @MobileDo.MobileField(key = 0xe2dd)
    private String moduleKey;

    @FieldDoc(description = "extra字段")
    @MobileDo.MobileField(key = 0xa7f4)
    private String extra;

    @FieldDoc(description = "模块配置")
    @MobileDo.MobileField(key = 0x33b0)
    private List<ModuleConfigDo> moduleConfigs;

    @FieldDoc(description = "AB测试，目前用于斗斛系统")
    @MobileDo.MobileField(key = 0xbae3)
    private List<ModuleAbConfigBo> moduleAbConfigs;

    public DealStyle(String moduleKey) {
        this.moduleKey = moduleKey;
    }

    public String getModuleKey() {
        return moduleKey;
    }

    public void setModuleKey(String moduleKey) {
        this.moduleKey = moduleKey;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    public List<ModuleConfigDo> getModuleConfigs() {
        return moduleConfigs;
    }

    public void setModuleConfigs(List<ModuleConfigDo> moduleConfigs) {
        this.moduleConfigs = moduleConfigs;
    }

    public List<ModuleAbConfigBo> getModuleAbConfigs() {
        return moduleAbConfigs;
    }

    public void setModuleAbConfigs(List<ModuleAbConfigBo> moduleAbConfigs) {
        this.moduleAbConfigs = moduleAbConfigs;
    }

}
