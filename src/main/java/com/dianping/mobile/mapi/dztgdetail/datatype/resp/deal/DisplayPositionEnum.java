package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum DisplayPositionEnum {

    /**
     * 门店模块后置到团购详情模块下方
     */
    AFTER_DETAIL(1, "团购详情下方"),

    /**
     * 门店模块前置到团购详情模块上方
     */
    BEFORE_DETAIL(2, "团购详情上方");

    private final int code;
    private final String desc;

    DisplayPositionEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
