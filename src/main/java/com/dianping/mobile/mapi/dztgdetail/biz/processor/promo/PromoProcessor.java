package com.dianping.mobile.mapi.dztgdetail.biz.processor.promo;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.CommissionWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PromoWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.util.ClientTypeUtils;
import com.dianping.pay.promo.common.enums.ProductType;
import com.dianping.pay.promo.common.enums.promo.DisplayScene;
import com.dianping.pay.promo.display.api.dto.Product;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.pay.promo.display.api.dto.QueryPromoDisplayRequest;
import com.dianping.pay.promo.display.api.dto.request.ReturnControl;
import com.dianping.tgc.open.entity.*;
import com.google.common.collect.Lists;
import com.meituan.service.mobile.mtthrift.callback.OctoThriftCallback;
import com.sankuai.nib.mkt.common.base.enums.RulePropertyTypeEnum;
import com.sankuai.nib.mkt.common.base.enums.propertyValueType.ClientTypeEnum;
import com.sankuai.nib.mkt.common.base.enums.propertyValueType.MeiDianSourceEnum;
import com.sankuai.nibmktproxy.queryclient.proxy.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

public class PromoProcessor extends AbsDealProcessor {

    private static final int PROMO_TEMP_ID = 239;
    private static final int DISCOUNT_PROMO_TEMP_ID = 18;
    private static final int SCENE_ID = 3017;
    private static final String BIZ_CODE = "nib.general.groupbuy";

    @Autowired
    private PromoWrapper promoWrapper;

    @Autowired
    private PromotionProxyService.AsyncIface promotionProxyServiceFuture;

    @Autowired
    CommissionWrapper commissionWrapper;

    @Override
    public void prepare(DealCtx ctx) {
        QueryPromoDisplayRequest request = buildPromoReq(ctx, PROMO_TEMP_ID, DisplayScene.ALL_PROMO, false);
        Future promoFuture = promoWrapper.prePromoDisplayDTO(request);
        ctx.getFutureCtx().setPromoFuture(promoFuture);

        QueryPromoDisplayRequest request1 = buildPromoReq(ctx, DISCOUNT_PROMO_TEMP_ID,
                DisplayScene.NO_IDLETIMES_PROMO, null);
        Future discountPromoFuture = promoWrapper.prePromoDisplayDTO(request1);
        ctx.getFutureCtx().setDiscountPromoFuture(discountPromoFuture);

        if (isPromoProxyCategory(ctx)) {
            commissionWrapper.prepare(ctx);

            PromotionRequest promotionRequest = buildPromoProxyRequest(ctx);
            OctoThriftCallback<PromotionProxyService.AsyncClient.getPromotions_call, PromotionResponse> callback = new OctoThriftCallback<>();

            try {
                if (Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.log.enable", false)) {
                    logger.info("dealId={}请求营销代理接口入参={}", ctx.getDealId4P(), JSON.toJSONString(promotionRequest));
                }

                promotionProxyServiceFuture.getPromotions(promotionRequest, callback);
            } catch (Exception e) {
                logger.error("团单id={}请求营销代理接口失败,错误信息:", ctx.getDealId4P(), e);
            }

            ctx.getFutureCtx().setPromoProxyFuture(callback.getFuture());
        }
    }

    @Override
    public void process(DealCtx ctx) {
        List<PromoDisplayDTO> promoDisplayDTOS = promoWrapper.queryPromoDisplayDTO(ctx.getFutureCtx().getPromoFuture());
        ctx.setPromoList(promoDisplayDTOS);

        List<PromoDisplayDTO> discountPromoDisplayDTOS = promoWrapper.queryPromoDisplayDTO(ctx.getFutureCtx().getDiscountPromoFuture());
        ctx.setDiscountPromoList(discountPromoDisplayDTOS);

        Future<PromotionResponse> promoProxyFuture = ctx.getFutureCtx().getPromoProxyFuture();
        if (promoProxyFuture != null) {
            try {
                PromotionResponse response = promoProxyFuture.get();
                if (Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.log.enable", false)) {
                    logger.info("dealId={}请求营销代理接口返回结果={}", ctx.getDealId4P(), JSON.toJSONString(response));
                }
                if (response == null || response.getStatus() != 0) {
                    logger.error("dealGroupId={}请求优惠代理接口失败,response={}", ctx.getDealId4P(), JSON.toJSONString(response));
                    return;
                }

                Map<String, PromotionDTOResult> promotionMap = response.getPromotionMap();
                ctx.setPromotionMap(promotionMap);
            } catch (Exception e) {
                logger.error("dealGroupId={}请求优惠代理接口失败,错误信息:", ctx.getDealId4P(), e);
            }
        }
    }

    private QueryPromoDisplayRequest buildPromoReq(DealCtx ctx, int templateId,
                                                   DisplayScene displayScene, Boolean merge) {
        QueryPromoDisplayRequest request = new QueryPromoDisplayRequest();
        request.setClientVersion(ctx.getEnvCtx().getVersion());
        request.setPlatform(ctx.getEnvCtx().toPayPlatformCode());
        request.setDisplayScene(displayScene.getCode());
        if (merge != null) {
            request.setMergeDisplayScenePromo(merge);
        }

        Product product = new Product();
        product.setProductId(ctx.getDealId4P());
        if (ctx.getDealGroupBase() != null) {
            product.setPrice(ctx.getDealGroupBase().getDealGroupPrice());
        }
        request.setProduct(product);

        ReturnControl returnControl = new ReturnControl();
        returnControl.setReturnComposition(true);
        request.setReturnControl(returnControl);
        request.setTemplateID(templateId);

        if (ctx.isMt()) {
            request.setProductType(ProductType.mt_tuangou.getValue());
            request.setUserId(ctx.getEnvCtx().getMtUserId());//已确认判断平台后再使用
            request.setDpId(ctx.getEnvCtx().getUuid());
            request.setCityId(ctx.getMtCityId());
        } else {
            request.setProductType(ProductType.tuangou.getValue());
            request.setUserId(ctx.getEnvCtx().getDpUserId());
            request.setDpId(ctx.getEnvCtx().getDpId());
            request.setCityId(ctx.getDpCityId());
        }
        return request;
    }

    private PromotionRequest buildPromoProxyRequest(DealCtx ctx) {
        PromotionRequest promotionRequest = new PromotionRequest();
        promotionRequest.setSceneId(SCENE_ID);
        promotionRequest.setBizCode(BIZ_CODE);
        promotionRequest.setRequestStrategy(RequestStrategy.NORMAL);

        List<RequestItem> requestItemList = Lists.newArrayList();

        List<Property> properties = Lists.newArrayListWithExpectedSize(3);
        RequestItem requestItem = new RequestItem();

        requestItem.setItemId(String.valueOf(ctx.getDealId4P()));

        Property priceProperty = new Property();
        priceProperty.setCode(RulePropertyTypeEnum.price.getCode());
        priceProperty.setValue(ctx.getDealGroupBase().getDealGroupPrice().multiply(new BigDecimal(100)).toString());
        properties.add(priceProperty);

        Property productIdProperty = new Property();
        productIdProperty.setCode(RulePropertyTypeEnum.productId.getCode());
        productIdProperty.setValue(String.valueOf(ctx.getDealId4P()));
        properties.add(productIdProperty);

        Property poiProperty = new Property();
        poiProperty.setCode(RulePropertyTypeEnum.poiId.getCode());
        poiProperty.setValue(String.valueOf(ctx.getDealId4P()));
        properties.add(poiProperty);

        commissionWrapper.resolve(ctx);
        if (ctx.getCommissionRate() != null) {
            Property profitProperty = new Property();
            profitProperty.setCode(RulePropertyTypeEnum.commissionRate.getCode());
            BigDecimal commissionRate = ctx.getCommissionRate().multiply(BigDecimal.valueOf(100));
            profitProperty.setValue(commissionRate.toString());
            properties.add(profitProperty);
        }

        requestItem.setProperties(properties);
        requestItemList.add(requestItem);

        promotionRequest.setRequestItemList(requestItemList);


        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(ctx.getUserId4P());
        if (StringUtils.isNotBlank(ctx.getEnvCtx().getUnionId())) {
            userInfo.setDeviceId(ctx.getEnvCtx().getUnionId());
        }
        promotionRequest.setUserInfo(userInfo);

        List<Property> commonProperties = buildCommonInfo(ctx, promotionRequest);
        //券查询策略
        Property retAllProperty = new Property();
        retAllProperty.setCode(RulePropertyTypeEnum.retAll.getCode());
        retAllProperty.setValue("true");
        commonProperties.add(retAllProperty);
        promotionRequest.setCommonProperties(commonProperties);

        return promotionRequest;
    }

    private List<Property> buildCommonInfo(DealCtx ctx, PromotionRequest promotionRequest) {
        List<Property> commonProperties = Lists.newArrayList();

        Property templateIdProperty = new Property();
        templateIdProperty.setCode(RulePropertyTypeEnum.templateId.getCode());
        templateIdProperty.setValue(String.valueOf(PROMO_TEMP_ID));
        commonProperties.add(templateIdProperty);

        //城市id
        Property cityIdProperty = new Property();
        cityIdProperty.setCode(RulePropertyTypeEnum.browseMtCityId.getCode());
        cityIdProperty.setValue(String.valueOf(ctx.getCityId4P()));
        commonProperties.add(cityIdProperty);

        Property clientTpProperty = new Property();
        clientTpProperty.setCode(RulePropertyTypeEnum.clientTp.getCode());
        clientTpProperty.setValue(ClientTypeUtils.convert(ctx.getEnvCtx().getClientType()).getValue());
        commonProperties.add(clientTpProperty);

        //点评or美团
        Property meiDianSource = new Property();
        meiDianSource.setCode(RulePropertyTypeEnum.MeiDianSource.getCode());
        if (ctx.isMt()) {
            meiDianSource.setValue(MeiDianSourceEnum.MT.getValue());
        } else {
            meiDianSource.setValue(MeiDianSourceEnum.DP.getValue());
        }
        commonProperties.add(meiDianSource);
        //ReturnControl
        Property returnControlProperty = new Property();
        returnControlProperty.setCode(RulePropertyTypeEnum.returnComposition.getCode());
        returnControlProperty.setValue("true");
        commonProperties.add(returnControlProperty);
        return commonProperties;
    }
}
