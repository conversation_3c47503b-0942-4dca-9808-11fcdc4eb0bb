package com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.nailstyle.NailStylePlcVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-08-28
 */
@Data
@Builder
@AllArgsConstructor
@TypeDoc(description = "沉浸页图片资源属性")
@MobileDo(id = 0x33a8)
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ImageUrlVO implements Serializable {
    @FieldDoc(description = "视频/图片高度")
    @MobileDo.MobileField(key = 0x261f)
    private int height;

    @FieldDoc(description = "视频/图片宽度")
    @MobileDo.MobileField(key = 0x2b78)
    private int width;

    @FieldDoc(description = "0=图片；1=视频")
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;

    @FieldDoc(description = "视频/图片资源路径")
    @MobileDo.MobileField(key = 0xc56e)
    private String url;

    @FieldDoc(description = "缩略图资源路径")
    @MobileDo.MobileField(key = 0x72b1)
    private String thumbPic;

    @FieldDoc(description = "容器尺寸")
    @MobileDo.MobileField(key = 0xfc9)
    private String scale;

    @FieldDoc(description = "图片更合适展示的尺寸")
    @MobileDo.MobileField(key = 0xf603)
    private String imageBestScale;

    @FieldDoc(description = "沉浸页图片左下角标签")
    @MobileDo.MobileField(key = 0xfa27)
    private NailStylePlcVO hotNailStyle;

    @FieldDoc(description = "雪碧图")
    @MobileDo.MobileField(key = 0xd103)
    private SpritePicVO spritePic;

    @FieldDoc(description = "UGC用户昵称")
    @MobileDo.MobileField(key = 0xcec)
    private String userName;

    @FieldDoc(description = "图片找相似跳链")
    @MobileDo.MobileField(key = 0x6a92)
    private String similarSearchUrl;

    @FieldDoc(description = "图片来源 1-PGC图 2-UGC图")
    @MobileDo.MobileField(key = 0xb40f)
    private int picSource;

}
