package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.LiveRoomDistributionInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;


/**
 * <AUTHOR>
 * @create 2024/12/18 11:41
 */
@Slf4j
@Service
public class PassParamBuilderService {
    public void buildPassParam(DealCtx ctx, DealGroupPBO result){
        buildMtLiveMinPassParam(ctx, result);
    }

    public void buildMtLiveMinPassParam(DealCtx ctx, DealGroupPBO result){
        // 判断是否是美播小程序
        if (!ctx.isMtLiveMinApp()){
            return;
        }
        LiveRoomDistributionInfo liveRoomDistributionInfo = ctx.getLiveRoomDistributionInfo();
        if (!validLiveRoomDistributionInfo(liveRoomDistributionInfo)){
            return;
        }
        String urlStr = null;
        try {
            urlStr = "&pass_param=" + liveRoomDistributionInfo.getEncodedPassParam();
        } catch (Exception e) {
            log.error("pass_param encode error!", e);
        }
        if (StringUtils.isEmpty(urlStr)) {
            return;
        }
        DealUtils.assembleUrl(result, urlStr);
    }
    private boolean validLiveRoomDistributionInfo(LiveRoomDistributionInfo liveRoomDistributionInfo){
        if (Objects.isNull(liveRoomDistributionInfo)){
            return false;
        }
        if (StringUtils.isBlank(liveRoomDistributionInfo.getEncodedPassParam())){
            return false;
        }
        return true;
    }
}
