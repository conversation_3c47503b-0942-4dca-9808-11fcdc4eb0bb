package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.atmosphere.HeadPicAtmosphere;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.highlights.DztgHighlightsModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealstruct.PnPurchaseNoteDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DztgAdsModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DztgMoreDealModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DztgShareModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DztgSkuModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleExtraDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.ExhibitContentDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.Pair;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.TimesCardPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.ProductDetailModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.style.BusinessStyle;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * https://mobile.sankuai.com/studio/model/info/14453
 */
@Data
@TypeDoc(description = "到综团单数据模型")
@MobileDo(id = 0xca4f)
public class DealGroupPBO implements Serializable {

    /**
     * 新商品详情页模块
     */
    @MobileField(key = 0xaaf7)
    private ProductDetailModule productDetailModule;

    @FieldDoc(description = "神会员点位id")
    @MobileField(key = 0xb5bb)
    private String position;

    @MobileField(key = 0x9658)
    private ModuleExtraDTO moduleExtra;

    @FieldDoc(description = "点评团单ID")
    @MobileField(key = 0x22e8)
    private int dpId;

    @FieldDoc(description = "美团团单ID")
    @MobileField(key = 0x4911)
    private int mtId;

    @FieldDoc(description = "团单套餐ID：不再兼容多套餐的情况，就默认使用第一个")
    @MobileField(key = 0xc826)
    private int dpDealId;

    @FieldDoc(description = "适用商户")
    @MobileField(key = 0xdac3)
    private ShopPBO shop;

    @FieldDoc(description = "标题")
    @MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "销量描述")
    @MobileField(key = 0xc426)
    private String saleDesc;

    @FieldDoc(description = "团单特征（免预约、随时退、过期自动退）")
    @MobileField(key = 0xa91)
    private List<String> features;

    @FieldDoc(description = "限制条")
    @MobileField(key = 0x925e)
    private List<String> limits;

    @FieldDoc(description = "限制条")
    @MobileField(key = 0x1348)
    private List<Guarantee> limitsExtend;

    @FieldDoc(description = "团单特征（免预约、随时退、过期自动退）")
    @MobileField(key = 0x4a49)
    private List<Guarantee> guarantee;

    @FieldDoc(description = "行业定制化团单特征（如口腔的执业医师标签）")
    @MobileField(key = 0xa312)
    private List<String> specialFeatures;

    @FieldDoc(description = "带类型区分的团单特征（如预售可用时间）")
    @MobileField(key = 0xd610)
    private List<DealFeature> dealFeatures;

    @FieldDoc(description = "退款率描述")
    @MobileField(key = 0x732d)
    private String refundRatioDesc;

    @FieldDoc(description = "团单展示资源数组")
    @MobileField(key = 0x31fd)
    private List<ContentPBO> dealContents;

    @FieldDoc(description = "款式信息")
    @MobileField(key = 0x6685)
    private ExhibitContentDTO exhibitContents;

    @FieldDoc(description = "团单购买蓝")
    @MobileField(key = 0x24e)
    private DealBuyBar buyBar;

    @FieldDoc(description = "次卡信息")
    @MobileField(key = 0xe044)
    private TimesCardPBO timesCard;

    @FieldDoc(description = "优先的Tag：该属性有数据时SaleDesc不展示")
    @MobileField(key = 0x92f8)
    private String priorTag;

    @FieldDoc(description = "扩展样式字段，含有BIG_HEAD表示大图")
    @MobileField(key = 0x9c7b)
    private List<String> extraStyles;

    @FieldDoc(description = "平台优选icon")
    @MobileField(key = 0x7a93)
    private String titleTagIcon;

    @FieldDoc(description = "精选icon")
    @MobileField(key = 0x8f6a)
    private DealChoicestIcon choicestIcon;

    @FieldDoc(description = "标签icon")
    @MobileField(key = 0xe5ed)
    private DealTitleIcon dealTitleIcon;

    @FieldDoc(description = "单用户最大购买量，0为不限量")
    @MobileField(key = 0x9ff)
    private int maxPerUser;

    @FieldDoc(description = "团单后台类目ID，用于打点")
    @MobileField(key = 0x33fe)
    private int categoryId;

    @FieldDoc(description = "0表示有头图样式，1表示无头图代金券样式")
    @MobileField(key = 0x8f0c)
    private int type;

    @FieldDoc(description = "特色标签")
    @MobileField(key = 0x97c0)
    private String featureTag;

    @FieldDoc(description = "促销标签")
    @MobileField(key = 0x8186)
    private List<DealBuyBtnIcon> promoTags;

    @FieldDoc(description = "模块背景颜色")
    @MobileField(key = 0xf3ce)
    private String moduleBackgroundColor;

    @FieldDoc(description = "在线咨询按钮")
    @MobileField(key = 0xc1d0)
    private String onlineConsultUrl;

    @FieldDoc(description = "在线咨询url，去除协议头")
    @MobileField(key = 0xf33e)
    private String originalOnlineConsultUrl;

    @FieldDoc(description = "图片宽高比")
    @MobileField(key = 0xfc52)
    private double picAspectRatio;

    @MobileField(key = 0x85fd)
    private String minPriceDesc;

    @FieldDoc(description = "团单是否可分享")
    @MobileField(key = 0xf072)
    private Boolean shareAble = Boolean.TRUE;

    @FieldDoc(description = "ab实验，之前ab实验返回结果")
    @MobileField(key = 0x1b3d)
    private ModuleAbConfig abConfigModel;

    @FieldDoc(description = "ab实验结果集，后续ab实验返回结果统一使用该List")
    @MobileField(key = 0xbae3)
    private List<ModuleAbConfig> moduleAbConfigs;

    @FieldDoc(description = "0=都不持有，1=持有会员卡，2=持有玩乐卡，3=持有会员卡+玩乐卡")
    @MobileField(key = 0xe174)
    private int shopCardState;

    @FieldDoc(description = "0=都不持有，1=持有会员卡，2=持有玩乐卡，3=持有会员卡+玩乐卡")
    @MobileField(key = 0x6e1a)
    private int userCardState;

    @FieldDoc(description = "大促氛围图上展示的划线价描述")
    @MobileField(key = 0xf82c)
    private String displayPriceDesc;

    @FieldDoc(description = "大促氛围图上展示的划线价")
    @MobileField(key = 0x8ed5)
    private String displayPrice;

    @FieldDoc(description = "价格展示模块")
    @MobileField(key = 0x9c62)
    private PriceDisplayModuleDo priceDisplayModuleDo;

    @FieldDoc(description = "优惠详情模块")
    @MobileField(key = 0xb8)
    private PromoDetailModule promoDetailModule;

    @FieldDoc(description = "合规说明")
    @MobileField(key = 0x6b9)
    private String heguiNotice;

    @FieldDoc(description = "团详页直播浮窗展示配置")
    @MobileField(key = 0x635)
    private String businessFigure;

    @FieldDoc(description = "是否为标准团单")
    @MobileField(key = 0xb9a)
    private boolean isStandardDealGroup;

    @FieldDoc(description = "是否展示预约浮层")
    @MobileField(key = 0x1d00)
    private boolean hasReserveEntrance;

    @FieldDoc(description = "是否展示新预约浮层")
    @MobileField(key = 0xe3e3)
    private boolean showNewReserveEntrance;

    @FieldDoc(description = "新弹窗跳转链接")
    @MobileField(key = 0xebb)
    private String reserveRedirectUrl;

    @FieldDoc(description = "规格模块")
    @MobileField(key = 0xf92c)
    private DztgSkuModule skuModule;

    @FieldDoc(description = "moduleConfig相关信息")
    @MobileField(key = 0x5eb5)
    private ModuleConfigsModule moduleConfigsModule;

    @FieldDoc(description = "分享模块")
    @MobileField(key = 0xc667)
    private DztgShareModule shareModule;

    @FieldDoc(description = "更多团购模块")
    @MobileField(key = 0xb4f7)
    private DztgMoreDealModule moreDealsModule;

    @FieldDoc(description = "广告模块")
    @MobileField(key = 0x379e)
    private DztgAdsModule adModule;

    @FieldDoc(description = "非结构化团购详情、购买须知、更多详情等")
    @MobileField(key = 0x5936)
    private List<Pair> structedDetails;

    @FieldDoc(description = "须知信息")
    @MobileField(key = 0xee60)
    private List<String> reminderInfo;

    @FieldDoc(description = "团详页浮层")
    @MobileField(key = 0xad0f)
    @Deprecated
    // 字段已不再使用
    private List<DealPopover> dealPopovers;

    @FieldDoc(description = "团单关联行为模块，如用户下单")
    @MobileField(key = 0x4fb8)
    private DealRelatedBehaviorModule relatedBehaviorModule;

    @FieldDoc(description = "是否需要登录")
    @MobileField(key = 0x5b68)
    private boolean needLogin;

    @MobileField(key = 0xfd7b)
    private String bgName;

    @MobileField(key = 0xea80)
    private String serviceType;

    @FieldDoc(description = "团单三级属性的Id")
    @MobileField(key = 0x4fd9)
    private Long serviceTypeId;

    @FieldDoc(description = "团单氛围条信息，秒杀等场景")
    @MobileField(key = 0xfede)
    private List<DealAtmosphereBarModule> dealAtmosphereBarModules;

    @FieldDoc(description = "skuId")
    @MobileField(key = 0xf59e)
    private String skuId;

    @FieldDoc(description = "团详链接")
    @MobileField(key = 0x8d3b)
//    @EncryptedLinkField(queries = {"poiid"})
    private String detailUrl;

    @FieldDoc(description = "亮点模块")
    @MobileField(key = 0xc97e)
    private DztgHighlightsModule highlightsModule;


    @FieldDoc(description = "团单名称")
    @MobileField(key = 0xcb70)
    private String dealName;

    @FieldDoc(description = "销量描述（纯文本，非富文本）")
    @MobileField(key = 0xd0)
    private String saleDescStr;

    @FieldDoc(description = "副标题组件")
    @MobileField(key = 0x3927)
    private List<SubTitleVO> subTitleList;

    @FieldDoc(description = "价保标签、浮层信息")
    @MobileField(key = 0xc522)
    private FeaturesLayer featuresLayer;

    @FieldDoc(description = "限购条")
    @MobileField(key = 0x4a13)
    private List<String> purchaseLimitInfo;

    @FieldDoc(description = "加项信息")
    @MobileField(key = 0xa098)
    private AdditionalInfo additionalInfo;

    @FieldDoc(description = "业务场景样式，标识如直播、小视界场景下团详不同的页面样式")
    @MobileField(key = 0x6134)
    private BusinessStyle businessStyle;

    @FieldDoc(description = "是否使用结构化购买须知")
    @MobileField(key = 0x9551)
    private boolean hitStructuredPurchaseNote;

    @FieldDoc(description = "结构化购买须知")
    @MobileField(key = 0x7105)
    private PnPurchaseNoteDTO pnPurchaseNoteDTO;

    @FieldDoc(description = "须知信息扩展属性（春节不打烊小黄条）")
    @MobileField(key = 0x3e30)
    private List<Guarantee> reminderExtend;

    @FieldDoc(description = "通用浮层信息")
    @MobileField(key = 0x8d8b)
    private FeaturesLayer generalFeaturesLayer;

    @FieldDoc(description = "交易类型：0-免费团单（走预约） 1-常规团单（走交易）")
    @MobileField(key = 0x8e5e)
    private int tradeType;

    @FieldDoc(description = "直播信息")
    @MobileField(key = 0xcecf)
    private MLiveInfoVo mLiveInfoVo;

    @FieldDoc(description = "纹绣注意事项返回视图")
    @MobileField(key = 0x6198)
    private TattooPrecautionsVO tattooPrecautionsVO;

    @FieldDoc(description = "纹绣问答返回视图模块")
    @MobileField(key = 0x287e)
    private TattooQAsVO tattooQAsVO;

    @FieldDoc(description = "团单是否限制购买次数，true:限制购买次数的团单")
    @MobileField(key = 0x23df)
    private boolean purchaseLimitDeal;

    @FieldDoc(description = "用户是否达到限制购买次数，true:达到限制购买次数")
    @MobileField(key = 0xd4a5)
    private boolean meetPurchaseLimit;

    @FieldDoc(description = "拼团规则信息")
    @MobileField(key = 0xd28)
    private PinTuanRuleInfo pinTuanRuleInfo;

    @FieldDoc(description = "ssr开关是否开启")
    @MobileField(key = 0xb8b1)
    private boolean ssrExperimentEnabled;

    @FieldDoc(description = "接口返回的时间戳，用于前端组件（神会员相关组件）的刷新")
    @MobileField(key = 0xfdb5)
    private long responseTimestamp;

    @FieldDoc(description = "自营场景值")
    @MobileField(key = 0xc426)
    private String selfScene;

    @FieldDoc(description = "价格说明跳转链接")
    @MobileField(key = 0x810b)
    private String customPriceDescJumpUrl;

    @FieldDoc(description = "相关推荐策略")
    @MobileField(key = 0xe0de)
    private RelatedRecommendStrategy recommendStrategy;

    @FieldDoc(description = "给交易透传的参数，团详页不要使用")
    @MobileField(key = 0x3e69)
    private SpecialParamsForPayVO specialParamsForPay;


    @FieldDoc(description = "预订团单标识")
    @MobileField(key = 0x78fd)
    private boolean isPreOrderDeal;

    @FieldDoc(description = "头图氛围图")
    @MobileField(key = 0x8ec4)
    private HeadPicAtmosphere headPicAtmosphere;

    @FieldDoc(description = "快照-氛围条缓存数据")
    @MobileField(key = 0x4b00)
    private Object queryExposureResourcesCache;

    @FieldDoc(description = "快照-相似团购推荐缓存数据")
    @MobileField(key = 0xf177)
    private String dealFilterListCache;

    @FieldDoc(description = "是否支持先用后付")
    @MobileField(key = 0x56db)
    private boolean creditPay;

    @FieldDoc(description = "是否隐藏会员卡引导条")
    @MobileField(key = 0xb503)
    private boolean hideMemberCardGuide;

    @FieldDoc(description = "导航栏搜索词")
    @MobileField(key = 0x774)
    private NavBarSearchModuleVO navBarSearchModuleVO;

    private boolean cleaningSelfOperationShop;
}