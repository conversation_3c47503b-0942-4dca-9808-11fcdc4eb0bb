package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.enums.BottomBarComponentTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.backgroud.BottomBarBackgroundVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.rich.content.RichContentVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2025/3/16 14:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class StandardTradeButtonVO extends BaseTradeButtonVO {

    /**
     * 主标题
     */
    private List<RichContentVO> mainTitle;

    /**
     * 副标题
     */
    private List<RichContentVO> subTitle;

    /**
     * 背景色
     */
    private BottomBarBackgroundVO background;

    /**
     * 标签
     */
    private ButtonTopTagVO buttonTag;

    @Override
    public int getComponentType() {
        return BottomBarComponentTypeEnum.TRADE_BUTTON.getCode();
    }

}