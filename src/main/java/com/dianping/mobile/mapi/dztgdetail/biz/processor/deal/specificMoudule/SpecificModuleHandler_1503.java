package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule;

import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.struct.common.dto.Resp;
import com.dianping.deal.struct.query.api.entity.dto.DealDetailDto;
import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.BaseDisplayItemVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailDisplayUnitVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailSpecificModuleVO;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.detail.DealGroupDetailDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Service
public class SpecificModuleHandler_1503 implements DealDetailSpecificModuleHandler {

    @Autowired
    private DealGroupWrapper dealGroupWrapper;

    private final static List<String> ATTR_KEYS = Lists.newArrayList(DealAttrKeys.SERVICE_TYPE, DealAttrKeys.DEAL_GROUP_TEMPLATE_KEY);
    private final static String INTERIOR_TEMPLATE = "acarwash_delicacy_interior_service";

    private final static String EXTERIOR_TEMPLATE = "carwash_delicacy_exterior_service";

    private final static List<String> DEAL_GROUP_STANDARD_TEMPLATE = Lists.newArrayList(INTERIOR_TEMPLATE, EXTERIOR_TEMPLATE);

    private final static String INTERIOR_DISPLAY_TEXT_TEMPLATE = "内饰%s大部位，%s";

    private final static String EXTERIOR_DISPLAY_TEXT_TEMPLATE = "外观%s大部位，%s";

    @Override
    public String identity() {
        return "1503";
    }

    @Override
    public void handle(SpecificModuleCtx ctx) {
        if(ctx.isUseQueryCenter()) {
            ctx.setResult(buildResultFromQueryCenter(ctx));
        } else {
            ctx.setResult(buildResult(ctx.getDpDealGroupId()));
        }
    }

    private DealDetailSpecificModuleVO buildResultFromQueryCenter(SpecificModuleCtx ctx) {

        BaseDisplayItemVO displayItem = getDisplayItemFromQueryCenter(ctx.getDealGroupDTO());

        if (displayItem == null) {
            return null;
        }

        List<BaseDisplayItemVO> items = new ArrayList<>();
        items.add(displayItem);

        DealDetailDisplayUnitVO unit = new DealDetailDisplayUnitVO();
        unit.setTitle("服务特色");
        unit.setDisplayItems(items);

        List<DealDetailDisplayUnitVO> units = new ArrayList<>();
        units.add(unit);

        DealDetailSpecificModuleVO result = new DealDetailSpecificModuleVO();
        result.setUnits(units);

        return result;
    }

    private BaseDisplayItemVO getDisplayItemFromQueryCenter(DealGroupDTO dealGroupDTO) {
        if(dealGroupDTO == null) {
            return null;
        }
        List<AttrDTO> dealGroupAttrs = dealGroupDTO.getAttrs();
        DealGroupServiceProjectDTO dealGroupServiceProjectDTO = dealGroupDTO.getServiceProject();

        String serviceType = dealGroupDTO.getCategory() == null ? "" : dealGroupDTO.getCategory().getServiceType();
        String dealGroupStandardTemplate = AttributeUtils.getFirstValueV2(dealGroupAttrs, DealAttrKeys.DEAL_GROUP_TEMPLATE_KEY);

        if (!"精洗".equals(serviceType) || !DEAL_GROUP_STANDARD_TEMPLATE.contains(dealGroupStandardTemplate)) {
            return null;
        }

        return buildDisplayItem(dealGroupStandardTemplate, dealGroupServiceProjectDTO);
    }

    private BaseDisplayItemVO buildDisplayItem(String dealGroupStandardTemplate, DealGroupServiceProjectDTO dealGroupServiceProjectDTO) {
        BaseDisplayItemVO item = new BaseDisplayItemVO();
        Map<String, String> skuAttrMap = getSkuAttrMapFromDealDetailDto(dealGroupServiceProjectDTO);

        if (MapUtils.isEmpty(skuAttrMap)) {
            return null;
        }

        if (dealGroupStandardTemplate.equals(INTERIOR_TEMPLATE)) {
            item.setBgPicUrl("https://p0.meituan.net/travelcube/b10ec31abaca290d0e889c2e5abd8da032927.png");
        } else if (dealGroupStandardTemplate.equals(EXTERIOR_TEMPLATE)) {
            item.setBgPicUrl("https://p1.meituan.net/travelcube/70e7d68f8bf23bf2560e7e4a1da8fef142670.png");
        } else {
            return null;
        }

        return item;
    }

    private DealDetailSpecificModuleVO buildResult(Integer dpDealGroupId) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule.SpecificModuleHandler_1503.buildResult(java.lang.Integer)");

        BaseDisplayItemVO displayItem = getDisplayItem(dpDealGroupId);

        if (displayItem == null) {
            return null;
        }

        List<BaseDisplayItemVO> items = new ArrayList<>();
        items.add(displayItem);

        DealDetailDisplayUnitVO unit = new DealDetailDisplayUnitVO();
        unit.setTitle("服务特色");
        unit.setDisplayItems(items);

        List<DealDetailDisplayUnitVO> units = new ArrayList<>();
        units.add(unit);

        DealDetailSpecificModuleVO result = new DealDetailSpecificModuleVO();
        result.setUnits(units);

        return result;
    }

    private BaseDisplayItemVO getDisplayItem(Integer dpDealGroupId) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule.SpecificModuleHandler_1503.getDisplayItem(java.lang.Integer)");
        Future attrFuture = dealGroupWrapper.preAttrs(dpDealGroupId, ATTR_KEYS);
        Future detailFuture = dealGroupWrapper.preDealDetailInfo(dpDealGroupId);
        List<AttributeDTO> dealGroupAttrs = dealGroupWrapper.getFutureResult(attrFuture);
        Resp<DealDetailDto> detailDtoResp = dealGroupWrapper.getFutureResult(detailFuture);

        String serviceType = AttributeUtils.getFirstValue(dealGroupAttrs, DealAttrKeys.SERVICE_TYPE);
        String dealGroupStandardTemplate = AttributeUtils.getFirstValue(dealGroupAttrs, DealAttrKeys.DEAL_GROUP_TEMPLATE_KEY);

        if (!"精洗".equals(serviceType) || !DEAL_GROUP_STANDARD_TEMPLATE.contains(dealGroupStandardTemplate)) {
            return null;
        }

        return buildDisplayItem(dealGroupStandardTemplate, detailDtoResp);
    }

    private BaseDisplayItemVO buildDisplayItem(String dealGroupStandardTemplate, Resp<DealDetailDto> detailDtoResp) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule.SpecificModuleHandler_1503.buildDisplayItem(java.lang.String,com.dianping.deal.struct.common.dto.Resp)");
        BaseDisplayItemVO item = new BaseDisplayItemVO();
        Map<String, String> skuAttrMap = getSkuAttrMapFromDealDetailDto(detailDtoResp);

        if (MapUtils.isEmpty(skuAttrMap)) {
            return null;
        }

        if (dealGroupStandardTemplate.equals(INTERIOR_TEMPLATE)) {
            item.setBgPicUrl("https://p0.meituan.net/travelcube/b10ec31abaca290d0e889c2e5abd8da032927.png");
        } else if (dealGroupStandardTemplate.equals(EXTERIOR_TEMPLATE)) {
            item.setBgPicUrl("https://p1.meituan.net/travelcube/70e7d68f8bf23bf2560e7e4a1da8fef142670.png");
        } else {
            return null;
        }

        return item;
    }

    private Map<String, String> getSkuAttrMapFromDealDetailDto(Resp<DealDetailDto> resp) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule.SpecificModuleHandler_1503.getSkuAttrMapFromDealDetailDto(com.dianping.deal.struct.common.dto.Resp)");
        Map<String, String> res = new HashMap<>();

        if (resp == null || !resp.isSuccess() || resp.getContent() == null) {
            return res;
        }

        DealDetailDto content = resp.getContent();

        if (content.getSkuUniStructuredDto() == null
                || CollectionUtils.isEmpty(content.getSkuUniStructuredDto().getMustGroups())) {
            return res;
        }

        MustSkuItemsGroupDto mustGroup = content.getSkuUniStructuredDto().getMustGroups().get(0);

        if (CollectionUtils.isEmpty(mustGroup.getSkuItems())) {
            return res;
        }

        return mustGroup
                .getSkuItems()
                .get(0)
                .getAttrItems()
                .stream()
                .collect(Collectors.toMap(SkuAttrItemDto::getAttrName, SkuAttrItemDto::getAttrValue));
    }

    private Map<String, String> getSkuAttrMapFromDealDetailDto(DealGroupServiceProjectDTO serviceProjectDTO) {
        Map<String, String> res = new HashMap<>();

        if (serviceProjectDTO == null || CollectionUtils.isEmpty(serviceProjectDTO.getMustGroups())) {
            return res;
        }

        MustServiceProjectGroupDTO mustGroup = serviceProjectDTO.getMustGroups().get(0);

        if (CollectionUtils.isEmpty(mustGroup.getGroups())) {
            return res;
        }

        return mustGroup
                .getGroups()
                .get(0)
                .getAttrs()
                .stream()
                .collect(Collectors.toMap(ServiceProjectAttrDTO::getAttrName, ServiceProjectAttrDTO::getAttrValue));
    }
}
