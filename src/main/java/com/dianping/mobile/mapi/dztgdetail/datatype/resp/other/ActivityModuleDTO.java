package com.dianping.mobile.mapi.dztgdetail.datatype.resp.other;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

@TypeDoc(description = "团单详情页活动模块数据")
@MobileDo(id = 0x1a12)
@Data
public class ActivityModuleDTO implements Serializable {

    @FieldDoc(description = "标题")
    @MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "活动小图标")
    @MobileField(key = 0xf39b)
    private String iconUrl;

    @FieldDoc(description = "活动描述")
    @MobileField(key = 0xfebf)
    private String desc;

    @FieldDoc(description = "引导文案")
    @MobileField(key = 0x17aa)
    private String leadText;

    @FieldDoc(description = "标题")
    @MobileField(key = 0xc9b5)
    private int leadType;

    @FieldDoc(description = "标题")
    @MobileField(key = 0x2373)
    private String leadUrl;
}
