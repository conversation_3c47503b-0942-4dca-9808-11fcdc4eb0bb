package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.deal.common.enums.GpsType;
import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.deal.shop.dto.BestShopReq;
import com.dianping.lion.client.Lion;
import com.dianping.lion.client.util.StringUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.QueryCenterProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.ExtraDealDetailModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.DigestInfoDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.ExtraDealDetailModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.ExtraDealDetailModuleResponse;
import com.dianping.mobile.mapi.dztgdetail.entity.ExtraDealDetailModuleConfig;
import com.dianping.mobile.mapi.dztgdetail.util.DealProductUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.tgc.open.entity.PlatformEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.mdp.boot.starter.util.MdpEnvUtils;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupDisplayShopBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupIdBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupShopRelationCheckBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupTagBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.BaseRequestBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.AttrSubjectEnum;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.ChannelDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.GuaranteeObjectQueryDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.GuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.UserInfoDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.QueryTagOptionDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.request.BatchQueryGuaranteeTagRequest;
import com.sankuai.nib.price.operation.common.guarantee.enums.ChannelNoEnum;
import com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTagNameEnum;
import com.sankuai.nib.price.operation.common.guarantee.enums.QueryExtKeyEnum;
import com.sankuai.nib.price.operation.common.guarantee.enums.TerminalTypeEnum;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.QueryCenterProcessor.getQueryCenterDealGroupAttrKey;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.APP_KEY;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.EXTRA_MOUDLE_CONFIG;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.MedicalConstant.*;
import static com.dianping.mobile.mapi.dztgdetail.util.OralDealUtils.buildSafeTreatOralJumpUrl;
import static com.dianping.mobile.mapi.dztgdetail.util.OralDealUtils.getSafeTreatZdcId;
import static com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTypeEnum.ANXIN_MEDICAL_GUARANTEE;
import static com.sankuai.nib.price.operation.common.guarantee.enums.ObjectTypeEnum.PRODUCT;
import static com.sankuai.nib.price.operation.common.guarantee.enums.ReturnModeEnum.PRIORITY_SORT_ONE_GROUP_BY_GUARANTEE_TYPE;

@Component
@Slf4j
public class ExtraDealDetailModuleFacade {
    @Autowired
    private PoiShopCategoryWrapper poiShopCategoryWrapper;

    @Autowired
    private MapperWrapper mapperWrapper;

    @Autowired
    private PoiClientWrapper poiClientWrapper;

    @Autowired
    private QueryCenterWrapper queryCenterWrapper;

    @Autowired
    private DealGroupWrapper dealGroupWrapper;

    @Autowired
    private ShopTagWrapper shopTagWrapper;

    @Autowired
    private DigestQueryWrapper digestQueryWrapper;

    @Autowired
    private DouHuService douHuService;

    @Autowired
    private GuaranteeQueryWrapper guaranteeQueryWrapper;

    public ExtraDealDetailModuleResponse getExtraDealDetailModuleResponse(ExtraDealDetailModuleReq req, EnvCtx envCtx) {
        Map<String, ExtraDealDetailModuleConfig> configMap = Lion.getMap(APP_KEY, EXTRA_MOUDLE_CONFIG, ExtraDealDetailModuleConfig.class);

        req.setRequestShopId(req.getShopId());// 保留入参ShopID，区分美团点评
        Long dpShopId = envCtx.isMt() ? mapperWrapper.getDpByMtShopId(Long.valueOf(req.getShopId())) : Long.valueOf(req.getShopId());
        req.setShopId(Optional.ofNullable(dpShopId).map(String::valueOf).orElse(null));

        DealGroupDTO dealGroupDto = getDealGroupDto(req, envCtx);
        if (dealGroupDto == null) return null;

        Long bestDpshopId = findbestShopId(dealGroupDto, req, envCtx);
        if (bestDpshopId == null || bestDpshopId <= 0L) return null;

        req.setShopId(String.valueOf(bestDpshopId));
        Integer backPoiSecondCateGoryId = getBackPoiSecondCateGoryId(req);
        if (backPoiSecondCateGoryId == null) return null;


        //眼镜行业
        ExtraDealDetailModuleResponse extraDealDetailModuleResponse = getSafeGlassExtraDealDetailModuleResponse(req, envCtx, bestDpshopId, backPoiSecondCateGoryId, configMap);
        if (extraDealDetailModuleResponse != null) return extraDealDetailModuleResponse;

        //齿科
        ExtraDealDetailModuleResponse dentalExtraDealDetailModuleResponse = getDentalExtraDealDetailModuleResponse(dealGroupDto, bestDpshopId, backPoiSecondCateGoryId, configMap, envCtx);
        if (dentalExtraDealDetailModuleResponse != null) return dentalExtraDealDetailModuleResponse;

        return null;
    }

    private ExtraDealDetailModuleResponse getDentalExtraDealDetailModuleResponse(DealGroupDTO dealGroupDto, Long bestDpshopId, Integer backPoiSecondCateGoryId, Map<String, ExtraDealDetailModuleConfig> configMap, EnvCtx envCtx) {
        if (backPoiSecondCateGoryId != 46) return null;

        Long mtShopId = getMtShopId(bestDpshopId);
        Long shopIdForPlatform = envCtx.isMt() ? mtShopId : bestDpshopId;
        int platform = envCtx.isMt() ? PlatformEnum.MT.getCode() : PlatformEnum.DP.getCode();
        Long safeDentureTag = getSafeTreatZdcId(SAFE_DENTURE_ZDC_TAG);
        Long safeImplantTag = getSafeTreatZdcId(SAFE_IMPLANT_ZDC_TAG);
        if (safeImplantTag == null && safeDentureTag == null) {
            return null;
        }
        String originalJumpUrl = platform == PlatformEnum.DP.getCode() ? TEETH_GUARANTEE_DP_JUMPURL : TEETH_GUARANTEEP_MT_JUMPURL;

        List<Integer> anXinMedicalGuaranteeInfo = getGuaranteeInfoTag(dealGroupDto, envCtx, mtShopId);

        //安心拔牙
        if (hasDisplayPOITag(bestDpshopId, safeDentureTag) && hasGuaranteeTagCode(anXinMedicalGuaranteeInfo, GuaranteeTagNameEnum.ANXIN_MEDICAL_FILL_GUARANTEE.getCode()) && LionConfigUtils.dentalSwitch()) {
            Future safeDentureFuture = digestQueryWrapper.getSafeDentureFuture(mtShopId);
            DigestInfoDTO safeTreatDigest = digestQueryWrapper.getSafeDentureTags(safeDentureFuture, mtShopId);
            ExtraDealDetailModuleConfig extraDealDetailModuleConfig = configMap.get(backPoiSecondCateGoryId + "-补牙");
            String jumpUrl = buildSafeTreatOralJumpUrl(shopIdForPlatform, platform, GUARANTEEIP_DENTURE, originalJumpUrl);
            return buildExtraDealDetailModule(extraDealDetailModuleConfig, jumpUrl, buildOralSafeTreatAttrList(safeTreatDigest, extraDealDetailModuleConfig));
        }

        //安心种植牙
        if (hasDisplayPOITag(bestDpshopId, safeImplantTag) && hasGuaranteeTagCode(anXinMedicalGuaranteeInfo, GuaranteeTagNameEnum.ANXIN_MEDICAL_IMPLANT_GUARANTEE.getCode())) {
            Future safeDentureFuture = digestQueryWrapper.getSafeImplantFuture(mtShopId);
            DigestInfoDTO safeTreatDigest = digestQueryWrapper.getSafeImplantTags(safeDentureFuture, mtShopId);
            ExtraDealDetailModuleConfig extraDealDetailModuleConfig = configMap.get(backPoiSecondCateGoryId + "-种植牙");
            String jumpUrl = buildSafeTreatOralJumpUrl(shopIdForPlatform, platform, GUARANTEEIP_IMPANT, originalJumpUrl);
            return buildExtraDealDetailModule(extraDealDetailModuleConfig, jumpUrl, buildOralSafeTreatAttrList(safeTreatDigest, extraDealDetailModuleConfig));
        }

        return null;
    }


    private List<Integer> getGuaranteeInfoTag(DealGroupDTO dealGroupDto, EnvCtx envCtx, Long mtShopId) {
        Future future = guaranteeQueryWrapper.preGetGuaranteeTagDTOs(buildRequest(envCtx, mtShopId, dealGroupDto));
        List<ObjectGuaranteeTagDTO> guaranteeTagDTOs = guaranteeQueryWrapper.getGuaranteeTagDTOs(future);
        List<Integer> anXinMedicalGuaranteeInfo = guaranteeTagDTOs.stream()
                .filter(Objects::nonNull)
                .filter(o -> ANXIN_MEDICAL_GUARANTEE.getCode() == o.getGuaranteeType())
                .findFirst()
                .map(ObjectGuaranteeTagDTO::getGuaranteeTag)
                .map(GuaranteeTagDTO::getGuaranteeTagNames)
                .orElse(Collections.emptyList());
        return anXinMedicalGuaranteeInfo;
    }

    private BatchQueryGuaranteeTagRequest buildRequest(EnvCtx envCtx, Long mtShopId, DealGroupDTO dealGroupDto) {
        BatchQueryGuaranteeTagRequest request = new BatchQueryGuaranteeTagRequest();
        request.setObjects(getObjects(envCtx, mtShopId, dealGroupDto));
        request.setGuaranteeTypes(Sets.newHashSet(ANXIN_MEDICAL_GUARANTEE.getCode()));
        request.setUserInfo(getUserInfoDTO(envCtx));
        request.setQueryTagOption(getQueryTagOption());
        return request;
    }

    private Set<GuaranteeObjectQueryDTO> getObjects(EnvCtx envCtx, Long mtShopId, DealGroupDTO dealGroupDto) {
        if (dealGroupDto.getMtDealGroupId() == null || dealGroupDto.getDpDealGroupId() == null) {
            return new HashSet<>();
        }
        Set<GuaranteeObjectQueryDTO> objects = new HashSet<>();
        GuaranteeObjectQueryDTO object = new GuaranteeObjectQueryDTO();

        Long ptId = envCtx.isMt() ? dealGroupDto.getMtDealGroupId() : dealGroupDto.getDpDealGroupId();
        object.setObjectId(String.valueOf(ptId));
        object.setObjectType(PRODUCT.getCode());
        objects.add(object);
        object.setExt(getExt(mtShopId));
        return objects;
    }

    private Map<String, String> getExt(Long mtShopId) {
        if (mtShopId == null) {
            return new HashMap<>();
        }
        //齿科
        Map<String, String> ext = new HashMap<>();
        ext.put(QueryExtKeyEnum.POI_ID.getCode(), String.valueOf(mtShopId));
        return ext;
    }

    private UserInfoDTO getUserInfoDTO(EnvCtx envCtx) {
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        ChannelDTO channelDTO = new ChannelDTO();

        com.sankuai.nib.price.operation.common.guarantee.enums.PlatformEnum platformEnum;
        if (envCtx.isMt()) {
            platformEnum = com.sankuai.nib.price.operation.common.guarantee.enums.PlatformEnum.MT_PLATFORM;
        } else {
            platformEnum = com.sankuai.nib.price.operation.common.guarantee.enums.PlatformEnum.DP_PLATFORM;
        }
        channelDTO.setPlatform(platformEnum.getCode());

        channelDTO.setTerminalType(TerminalTypeEnum.APP.getCode());
        channelDTO.setChannelNo(ChannelNoEnum.UNKNOWN.getCode());

        userInfoDTO.setChannel(channelDTO);
        return userInfoDTO;
    }


    private QueryTagOptionDTO getQueryTagOption() {
        QueryTagOptionDTO queryTagOptionDTO = new QueryTagOptionDTO();
        queryTagOptionDTO.setReturnMode(PRIORITY_SORT_ONE_GROUP_BY_GUARANTEE_TYPE.getCode());
        return queryTagOptionDTO;
    }

    private List<ExtraDealDetailModule> buildOralSafeTreatAttrList(DigestInfoDTO safeTreatDigest, ExtraDealDetailModuleConfig config) {
        if (safeTreatDigest == null || config == null) return Collections.emptyList();

        Map<String, Integer> safeDentureMap = safeTreatDigest.toSafeDentureMap();
        if (MapUtils.isEmpty(safeDentureMap)) return Collections.emptyList();

        return config.getAttrList().stream().map(attrConfig -> {
            Integer timeType = safeDentureMap.get(attrConfig.getAttrKey());
            if (timeType != null) {
                String key = SAFE_DENTURE_RULE_CONFIG.get(attrConfig.getAttrKey());
                if (timeType == 1) {
                    Integer value = safeDentureMap.get(key);
                    String format = String.format(attrConfig.getValue(), value, "%s");
                    attrConfig.setValue(format);
                } else if (timeType == 2) {
                    String replace = attrConfig.getValue().replace("保障%s年", "保障术后终身");
                    attrConfig.setValue(replace);
                }
            }
            Integer countType = safeDentureMap.get(attrConfig.getSecondAttrKey());
            if (countType != null) {
                String secondKey = SAFE_DENTURE_RULE_CONFIG.get(attrConfig.getSecondAttrKey());
                if (countType == 1) {
                    Integer value = safeDentureMap.get(secondKey);
                    String format = String.format(attrConfig.getValue(), value, "%s");
                    attrConfig.setValue(format);
                }
                if (countType == 2) {
                    String replace = attrConfig.getValue().replace("%s次", "");
                    attrConfig.setValue(replace);
                }
            }
            ExtraDealDetailModule module = new ExtraDealDetailModule();
            module.setName(attrConfig.getName());
            module.setPic(attrConfig.getPic());
            module.setValue(attrConfig.getValue());
            return module;
        }).collect(Collectors.toList());
    }

    private Long getMtShopId(Long bestDpshopId) {
        return Optional.ofNullable(bestDpshopId)
                .map(shopId-> mapperWrapper.getMtShopIdByDpShopIdLong(shopId))
                .orElse(null);
    }

    private ExtraDealDetailModuleResponse getSafeGlassExtraDealDetailModuleResponse(ExtraDealDetailModuleReq req, EnvCtx ctx, Long bestDpshopId, Integer backPoiSecondCateGoryId, Map<String, ExtraDealDetailModuleConfig> configMap) {
        //配镜使用
        Long safePtometryTagForEnv = MdpEnvUtils.isTestEnv() ? SAFE_PTOMETRY_TAGID_TEST : SAFE_PTOMETRY_TAGID;
        if (backPoiSecondCateGoryId == 155 && hasDisplayPOITag(bestDpshopId, safePtometryTagForEnv) && !isDefaultExpResult(req.getCityId(), ctx)) {
            String jumpUrl = ctx.isMt() ? GLASS_ASSURED_MT_JUMPURL : GLASS_ASSURED_DP_JUMPURL;
            String shopId = ctx.isMt() ? String.valueOf(mapperWrapper.getMtShopIdByDpShopIdLong(bestDpshopId)) : bestDpshopId.toString();
            jumpUrl = jumpUrl + "&shopId=" + shopId;
            ExtraDealDetailModuleConfig extraDealDetailModuleConfig = configMap.get(String.valueOf(backPoiSecondCateGoryId));
            List<ExtraDealDetailModuleConfig.AttrListConfig> attrConfigList = extraDealDetailModuleConfig.getAttrList();
            List<ExtraDealDetailModule> attrList;
            attrList = attrConfigList.stream().map(attr -> {
                ExtraDealDetailModule module = new ExtraDealDetailModule();
                module.setPic(attr.getPic());
                module.setName(attr.getName());
                module.setValue(attr.getValue());
                return module;
            }).collect(Collectors.toList());
            return buildExtraDealDetailModule(extraDealDetailModuleConfig, jumpUrl, attrList);
        }
        return null;
    }

    private boolean hasGuaranteeTagCode(List<Integer> anXinMedicalGuaranteeInfo, Integer GuaranteeTagCode) {
        if (CollectionUtils.isEmpty(anXinMedicalGuaranteeInfo)) {
            return false;
        }
        return anXinMedicalGuaranteeInfo.contains(GuaranteeTagCode);
    }

    private ExtraDealDetailModuleResponse buildExtraDealDetailModule(ExtraDealDetailModuleConfig extraDealDetailModuleConfig, String jumpUrl, List<ExtraDealDetailModule> attrList) {
        if (extraDealDetailModuleConfig == null) return null;
        return buildextraDealDetailModuleResponse(extraDealDetailModuleConfig, jumpUrl, attrList);
    }

    private ExtraDealDetailModuleResponse buildextraDealDetailModuleResponse(ExtraDealDetailModuleConfig extraDealDetailModuleConfig, String jumpUrl, List<ExtraDealDetailModule> attrList) {
        ExtraDealDetailModuleResponse extraDealDetailModuleResponse = new ExtraDealDetailModuleResponse();
        extraDealDetailModuleResponse.setMainTitle(extraDealDetailModuleConfig.getMainTitle());
        extraDealDetailModuleResponse.setTitleInfo(extraDealDetailModuleConfig.getTitleInfo());
        extraDealDetailModuleResponse.setIcon(extraDealDetailModuleConfig.getIcon());
        extraDealDetailModuleResponse.setBackgroundPic(extraDealDetailModuleConfig.getBackgroundPic());
        extraDealDetailModuleResponse.setJumpUrl(jumpUrl);
        extraDealDetailModuleResponse.setAttrList(attrList);
        return extraDealDetailModuleResponse;
    }

    public boolean hasDisplayPOITag(Long bestDpshopId, Long tagId) {
        Future future = shopTagWrapper.preGetDpShopTags(bestDpshopId);
        Map<Long, List<DisplayTagDto>> dpShopId2TagsMap = shopTagWrapper.getShopId2TagsMap(future);
        if (MapUtils.isEmpty(dpShopId2TagsMap)) {
            return false;
        }
        List<DisplayTagDto> displayTagDtos = dpShopId2TagsMap.get(bestDpshopId);
        if (CollectionUtils.isEmpty(displayTagDtos)) {
            return false;
        }
        return displayTagDtos.stream().anyMatch(tag -> tagId.equals(tag.getTagId()));
    }

    private boolean isDefaultExpResult(Integer cityId, EnvCtx envCtx) {
        // 判断是否命中a组实验
        String expResult = douHuService.getGlassDealDetailExpResult(cityId, envCtx);
        return "a".equals(expResult);
    }

    private Integer getBackPoiSecondCateGoryId(ExtraDealDetailModuleReq req) {
        if (StringUtils.isNotEmpty(req.getShopId())) {
            Long shopId = Long.valueOf(req.getShopId());
            DpPoiDTO dpPoiDTO = poiClientWrapper.getDpPoiDTO(shopId,
                    Lists.newArrayList("shopId", "backMainCategoryPath", "cityId"));
            if (dpPoiDTO != null) {
                return poiShopCategoryWrapper.getBackSecondMainCategory(dpPoiDTO);
            }
        }
        return null;
    }

    public Long findbestShopId(DealGroupDTO dealGroupDTO, ExtraDealDetailModuleReq req, EnvCtx ctx) {
        // todo xiangrui 门店判断
        List<Long> DpRelatedShops = getDpDisplayShopIds(dealGroupDTO);
        Future shopFuture = dealGroupWrapper.preDealGroupBestShop(buildBestShopReqWithoutShopId(req, ctx));
        Long bestDpShopId = null;
        if (StringUtils.isNotEmpty(req.getShopId())) {
            Long shopId = Long.valueOf(req.getShopId());
            if (DealProductUtils.checkShopNoExist(dealGroupDTO, NumberUtils.toLong(req.getRequestShopId()), ctx.isMt())
                    && !DpRelatedShops.contains(shopId)) {
                // 有来源门店，但是来源门店和团单不绑定，则重新请求，入参不指定门店
                BestShopDTO bestShop = dealGroupWrapper.getFutureResult(shopFuture);
                if (bestShop == null) {
                    return null;
                }
                bestDpShopId = bestShop.getDpShopId();
            } else {
                // 有来源门店，且来源门店和团单绑定，直接使用来源门店id
                bestDpShopId = shopId;
            }
        } else {
            // 没有来源门店id,可以直接使用请求结果
            BestShopDTO bestShop = dealGroupWrapper.getFutureResult(shopFuture);
            if (bestShop == null) {
                return null;
            }
            bestDpShopId = bestShop.getDpShopId();
        }
        return bestDpShopId;
    }

    private BestShopReq buildBestShopReqWithoutShopId(ExtraDealDetailModuleReq req, EnvCtx ctx) {
        BestShopReq shopReq = new BestShopReq();
        shopReq.setDealGroupId(Long.parseLong(req.getDealGroupId()));
        shopReq.setCityId(req.getCityId());
        shopReq.setLat(req.getUserLat());
        shopReq.setLng(req.getUserLng());
        shopReq.setGpsType(GpsType.GCJ02.getType());
        shopReq.setClientType(ctx.getClientType());
        return shopReq;
    }

    private List<Long> getDpDisplayShopIds(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO == null || dealGroupDTO.getDisplayShopInfo() == null
                || dealGroupDTO.getDisplayShopInfo().getDpDisplayShopIds() == null) {
            return Collections.emptyList();
        }
        return dealGroupDTO.getDisplayShopInfo().getDpDisplayShopIds();
    }

    private DealGroupDTO getDealGroupDto(ExtraDealDetailModuleReq req, EnvCtx ctx) {
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = buildQueryCenterRequest(req, ctx);
        try {
            DealGroupDTO dealGroupDTO = queryCenterWrapper.getDealGroupDTO(queryByDealGroupIdRequest);
            return dealGroupDTO;
        } catch (TException e) {
            log.error("QueryCenterWrapper.getDealGroupDTO error!", e);
        }
        return null;
    }

    private QueryByDealGroupIdRequest buildQueryCenterRequest(ExtraDealDetailModuleReq req, EnvCtx ctx) {
        IdTypeEnum idTypeEnum = ctx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP;
        Map<Long, Set<Long>> deal2shop = Maps.newHashMap();
        deal2shop.put(NumberUtils.toLong(req.getDealGroupId()), Sets.newHashSet(NumberUtils.toLong(req.getRequestShopId())));

        BaseRequestBuilder<QueryByDealGroupIdRequest> builder = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet(Long.valueOf(req.getDealGroupId())),
                        ctx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP)
                .displayShop(DealGroupDisplayShopBuilder.builder().dpDisplayShopIds())
                .attrsByKey(AttrSubjectEnum.DEAL_GROUP, getQueryCenterDealGroupAttrKey())
                .dealGroupId(DealGroupIdBuilder.builder().all())
                .dealGroupTag(DealGroupTagBuilder.builder().all());
        if (QueryCenterProcessor.openQueryCenterCheckDealGroupShopRelation()) {
            builder.checkDealGroupShopRelation(DealGroupShopRelationCheckBuilder.builder().shopIdType(idTypeEnum.getCode()).checkDisplayShop(deal2shop));
        }
        return builder.build();
    }
}