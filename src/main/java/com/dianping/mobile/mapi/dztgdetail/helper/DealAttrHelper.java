package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.CatEvents;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ToHomeFeeDisplayJudgeType;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.entity.PrepayCategoryConfig;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.VersionUtils;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.AvailableDurationDateDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DateRangeDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
@Slf4j
public class DealAttrHelper {

    public static final List<String> REQUIRE_ATTRIBUTES = Arrays.asList(
            DealAttrKeys.RESERVATION, DealAttrKeys.RESERVATION_2, DealAttrKeys.RESERVATION_3, DealAttrKeys.TORT, DealAttrKeys.HIDE_TYPE, DealAttrKeys.VOUCHER,
            DealAttrKeys.TIMES_AVAILABLE_ALL, DealAttrKeys.HOLIDAY_AVAILABLE, DealAttrKeys.NO_TIMES_LIMIT, DealAttrKeys.ALL_CAN_USE,
            DealAttrKeys.PRE_SALE_TAG, DealAttrKeys.SUPPORT_SHOP_SERVICE, DealAttrKeys.SUPPORT_HOME_SERVICE, DealAttrKeys.TOOTH_SUIT_PEOPLE,
            DealAttrKeys.RESERVATION_NUMBER, DealAttrKeys.SERVICE_TYPE, DealAttrKeys.STANDARD_DEALGROUP, DealAttrKeys.EDU_FEMALE_ONLY,
            DealAttrKeys.EDU_SUITABLE_AGE,
            DealAttrKeys.RESERVATION_NUMBER, DealAttrKeys.SERVICE_TYPE, DealAttrKeys.STANDARD_DEALGROUP, DealAttrKeys.KEY_CATEGORY,
            DealAttrKeys.IS_SELECTED_DEAL, DealAttrKeys.VOUCHER_LIMIT_OF_USING_2,
            DealAttrKeys.ORAL_DENTISTRY_RULE, DealAttrKeys.PRE_SALE_DEAL_GROUP_PRICE, DealAttrKeys.LIMIT_OF_USING_EACH_EYE
    );

    private static final List<String> WORKDAYS = Lists.newArrayList("1001", "1002", "1003", "1004", "1005");
    public static final String YES = "true";
    public static final String ON = "1";
    // 支持尾款
    public static final String supportPayMethod = "2";
    public static final String onePrice = "一口价";

    /**
     * 维修类预付类团单属性值
     */
    private final static List<String> REPAIR_PREPAY_ATTR_VALUES = Lists.newArrayList("上门费", "优惠券", "检测费", "预付金尾款团购");

    // 指定类目的预约信息没有值, 则返回true
    public static boolean isReservationEmpty(List<AttributeDTO> attrs, DealGroupDTO dealGroupDTO) {
        // 指定的二级类目
        List<Long> filterCategory = Lion.getList(Environment.getAppName(), LionConstants.RESERVATION_EMPTY_NOT_DISPLAY_TYPE, Long.class, new ArrayList<>());
        // 团单二级类目 不是 指定Lion配置的类目则返回false
        if (dealGroupDTO == null || dealGroupDTO.getCategory() == null || !filterCategory.contains(dealGroupDTO.getCategory().getCategoryId())) {
            return false;
        }
        // 预约信息为空
        return CollectionUtils.isEmpty(getAttributeValues(attrs, DealAttrKeys.RESERVATION)) &&
                CollectionUtils.isEmpty(getAttributeValues(attrs, DealAttrKeys.RESERVATION_2)) &&
                CollectionUtils.isEmpty(getAttributeValues(attrs, DealAttrKeys.RESERVATION_3));
    }

    public static boolean needReservation(List<AttributeDTO> attrs) {
        return CollectionUtils.isNotEmpty(attrs) && (hasAttribute(attrs, DealAttrKeys.RESERVATION, DealAttrKeys.RESERVATION_VALUE_YES)
                || hasAttribute(attrs, DealAttrKeys.RESERVATION_2, DealAttrKeys.RESERVATION_VALUE_YES)
                || hasAttribute(attrs, DealAttrKeys.RESERVATION_3, DealAttrKeys.RESERVATION_VALUE_YES));
    }

    public static boolean isVoucher(List<AttributeDTO> attrs) {
        return hasAttribute(attrs, DealAttrKeys.VOUCHER, DealAttrKeys.VOUCHER_VALUE);
    }

    public static boolean isVoucherV2(List<AttrDTO> attrs) {
        return hasAttributeV2(attrs, DealAttrKeys.VOUCHER, DealAttrKeys.VOUCHER_VALUE);
    }

    @Deprecated
    public static boolean isNotTort(List<AttributeDTO> attrs) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper.isNotTort(java.util.List)");
        boolean isTort = hasAttribute(attrs, DealAttrKeys.TORT, DealAttrKeys.TORT_VALUE);
        if (isTort) {
            Cat.logEvent(CatEvents.DEALBASE_FAIL, "TortDeal");
        }
        return !isTort;
    }

    private static boolean hasAttribute(List<AttributeDTO> attrs, String key, String value) {
        List<String> attrValues = getAttributeValues(attrs, key);
        return CollectionUtils.isNotEmpty(attrValues) && attrValues.contains(value);
    }

    private static boolean hasAttributeV2(List<AttrDTO> attrs, String key, String value) {
        List<String> attrValues = getAttributeValuesV2(attrs, key);
        return CollectionUtils.isNotEmpty(attrValues) && attrValues.contains(value);
    }

    public static String getFirstValue(List<AttributeDTO> attributeDtoList, String key) {
        List<String> values = getAttributeValues(attributeDtoList, key);
        return CollectionUtils.isEmpty(values) ? StringUtils.EMPTY : values.get(0);
    }

    private static String getFirstValueV2(List<AttrDTO> attributeDtoList, String key) {
        List<String> values = getAttributeValuesV2(attributeDtoList, key);
        return CollectionUtils.isEmpty(values) ? StringUtils.EMPTY : values.get(0);
    }

    public static List<String> getAttributeValues(List<AttributeDTO> attributeDtoList, String key) {
        if (attributeDtoList != null) {
            for (AttributeDTO attr : attributeDtoList) {
                if (StringUtils.equals(attr.getName(), key)) {
                    return attr.getValue();
                }
            }
        }
        return Collections.emptyList();
    }

    public static List<String> getAttributeValuesV2(List<AttrDTO> attributeDtoList, String key) {
        if (attributeDtoList != null) {
            for (AttrDTO attr : attributeDtoList) {
                if (StringUtils.equals(attr.getName(), key)) {
                    return attr.getValue();
                }
            }
        }
        return Collections.emptyList();
    }

    public static boolean availableAll(List<AttributeDTO> attrs) {
        if (CollectionUtils.isEmpty(attrs)) {
            return false;
        }
        String available = getFirstValue(attrs, DealAttrKeys.TIMES_AVAILABLE_ALL);
        return StringUtils.isNotEmpty(available) && available.equals("是");
    }

    public static boolean workDayAvailable(List<AttributeDTO> attrs) {
        if (CollectionUtils.isEmpty(attrs)) {
            return false;
        }
        List<String> attrHolidays = getAttributeValues(attrs, DealAttrKeys.HOLIDAY_AVAILABLE);
        if (CollectionUtils.isEmpty(attrHolidays) || CollectionUtils.isEmpty(WORKDAYS)) {
            return false;
        }
        boolean flag = false;
        if (attrHolidays.contains("1006") && attrHolidays.contains("1007")) {
            flag = true;
        }
        for (String workday : WORKDAYS) {
            if (attrHolidays.contains(workday)) {
                return false;
            }
        }
        return flag;
    }

    public static boolean holidayAvailable(List<AttributeDTO> attrs) {
        if (CollectionUtils.isEmpty(attrs)) {
            return false;
        }
        String available = getFirstValue(attrs, DealAttrKeys.HOLIDAY_AVAILABLE);
        return StringUtils.isNotEmpty(available) && available.equals("1");
    }

    public static boolean allDayAvailable(List<AttributeDTO> attrs) {
        if (CollectionUtils.isEmpty(attrs)) {
            return false;
        }
        List<String> attrDays = getAttributeValues(attrs, DealAttrKeys.HOLIDAY_AVAILABLE);
        if (CollectionUtils.isEmpty(attrDays)) {
            return false;
        }
        return attrDays.size() == 1 && attrDays.get(0).equals("1");
    }

    public static boolean allCanUse(List<AttributeDTO> attrs) {
        if (CollectionUtils.isEmpty(attrs)) {
            return false;
        }
        String available = getFirstValue(attrs, DealAttrKeys.ALL_CAN_USE);
        return StringUtils.isNotEmpty(available) && available.equals("是");
    }

    public static boolean noTimesLimit(List<AttributeDTO> attrs) {
        if (CollectionUtils.isEmpty(attrs)) {
            return false;
        }
        String available = getFirstValue(attrs, DealAttrKeys.NO_TIMES_LIMIT);
        return StringUtils.isNotEmpty(available) && available.equals("可");
    }

    public static boolean isPreSale(List<AttributeDTO> attrs) {
        if (CollectionUtils.isEmpty(attrs)) {
            return false;
        }
        String available = getFirstValue(attrs, DealAttrKeys.PRE_SALE_TAG);
        return StringUtils.isNotEmpty(available) && available.equals(YES);
    }

    public static boolean isWarmUpDeal(List<AttributeDTO> attrs) {
        if (CollectionUtils.isEmpty(attrs)) {
            return false;
        }

        String warmUpStartTimeValue = getFirstValue(attrs, DealAttrKeys.WARM_UP_START_TIME);
        String usingStockPlanValue = getFirstValue(attrs, DealAttrKeys.USING_STOCK_PLAN);

        return StringUtils.isNotBlank(warmUpStartTimeValue) || (StringUtils.isNotBlank(usingStockPlanValue) && usingStockPlanValue.equals(ON));
    }

    // 支持尾款 和 商品类型”字段有值但≠“一口价”
    public static boolean isPayAndNotOnePrice(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO == null || dealGroupDTO.getCategory() == null || dealGroupDTO.getCategory().getCategoryId() == null) {
            return false;
        }
        List<Long> filterTypes = Lion.getList(Environment.getAppName(), LionConstants.LIFE_DEAL_GROUP_CATEGORY, Long.class, new ArrayList<>());
        // 不是目标类目则返回
        if(CollectionUtils.isEmpty(filterTypes) || !filterTypes.contains(dealGroupDTO.getCategory().getCategoryId())) {
            return false;
        }
        // 电脑维修商品类型
        String computerProjectType = getFirstValueV2(dealGroupDTO.getAttrs(), "computer_project_type");
        // 其他分商品类型
        String repairProjectType = getFirstValueV2(dealGroupDTO.getAttrs(), "repair_project_type");
        // 支持尾款(其中2代表尾款) 和 电脑维修、其他分类商品类型为 无值 或 有值且不为「一口价, 则返回true
        return supportPayMethod.equals(getFirstValueV2(dealGroupDTO.getAttrs(), "pay_method")) &&
                (!(onePrice.equals(computerProjectType) || onePrice.equals(repairProjectType)));
    }

    /**
     * 是否为维修预付团单
     * @param dealGroupDTO 团单信息
     * @return true 是，false 不是
     */
    public static boolean isRepairPrepayDeal(DealGroupDTO dealGroupDTO) {
        return Optional.ofNullable(dealGroupDTO)
                .map(DealGroupDTO::getAttrs)
                .map(attrs -> attrs.stream()
                        .filter(Objects::nonNull)
                        .anyMatch(DealAttrHelper::isRepairPrepayDeal))
                .orElse(false);
    }

    private static boolean isRepairPrepayDeal(AttrDTO attrDTO) {
        if (Objects.isNull(attrDTO)) {
            return false;
        }
        return isRepairPrepayDeal(attrDTO, "computer_project_type")
                || isRepairPrepayDeal(attrDTO, "repair_project_type");
    }

    private static boolean isRepairPrepayDeal(AttrDTO attrDTO, String prepayAttrName) {
        if (Objects.isNull(attrDTO) || StringUtils.isBlank(attrDTO.getName()) || CollectionUtils.isEmpty(attrDTO.getValue())) {
            return false;
        }
        return attrDTO.getName().equals(prepayAttrName) && REPAIR_PREPAY_ATTR_VALUES.contains(attrDTO.getValue().get(0));
    }

    public static boolean isPrepayDeal(DealGroupDTO dealGroupDTO) {
        PrepayCategoryConfig config = LionConfigUtils.getPrepayCategoryConfig();
        if (invalidParams(dealGroupDTO, config)) {
            return false;
        }
        String categoryId = String.valueOf(dealGroupDTO.getCategory().getCategoryId());
        return isRepairPrepayDeal(dealGroupDTO)
                && config.getCategory2TextMap().containsKey(categoryId);
    }

    private static boolean invalidParams(DealGroupDTO dealGroupDTO, PrepayCategoryConfig config) {
        return Objects.isNull(config) || MapUtils.isEmpty(config.getCategory2TextMap())
                || Objects.isNull(dealGroupDTO) || Objects.isNull(dealGroupDTO.getCategory())
                || Objects.isNull(dealGroupDTO.getCategory().getCategoryId());
    }

    public static boolean isToHomeFixDeal(DealCtx ctx) {
        return isToHomeFixDeal(ctx.getDealGroupDTO());
    }

    public static boolean isToHomeFixDeal(DealGroupDTO dealGroupDTO) {
        List<MustServiceProjectGroupDTO> serviceProjectMustGroups = Optional.ofNullable(dealGroupDTO)
                .map(DealGroupDTO::getServiceProject)
                .map(DealGroupServiceProjectDTO::getMustGroups).orElse(new ArrayList<>());
        if (CollectionUtils.isEmpty(serviceProjectMustGroups) || serviceProjectMustGroups.get(0) == null) {
            return false;
        }
        List<ServiceProjectDTO> groups = serviceProjectMustGroups.get(0).getGroups();
        if (CollectionUtils.isEmpty(groups) || groups.get(0) == null) {
            return false;
        }
        List<ServiceProjectAttrDTO> attrs = groups.get(0).getAttrs();
        if (CollectionUtils.isEmpty(attrs)) {
            //没有任何属性，直接不返回，边界场景
            return false;
        }
        ToHomeFeeDisplayJudgeType judgeType = isLifeToDoorCategory(dealGroupDTO);
        if (judgeType == null) {
            return false;
        }
        List<String> toHomeFeeAttrs = Lion.getList(Environment.getAppName(), "com.sankuai.dzu.tpbase.dztgdetailweb.life.fix.tohome.service.project.key", String.class, new ArrayList<>());
        List<ServiceProjectAttrDTO> filterAttrs = attrs.stream().filter(e -> e.getAttrName() != null && toHomeFeeAttrs.contains(e.getAttrName())).collect(Collectors.toList());
        String notEqualAttrValue = Lion.getString(Environment.getAppName(), "com.sankuai.dzu.tpbase.dztgdetailweb.life.fix.tohome.deal.notEqual.attr.value", "一口价");
        if (judgeType == ToHomeFeeDisplayJudgeType.HasNoValueOrNotFixedPrice) {
            //商品类型字段无值、有值但非「一口价」
            return CollectionUtils.isEmpty(filterAttrs) || filterAttrs.stream().allMatch(e -> e.getAttrValue() != null && !e.getAttrValue().equals(notEqualAttrValue));
        } else if (judgeType == ToHomeFeeDisplayJudgeType.HasValueAndEqualToHomeFee) {
            return CollectionUtils.isNotEmpty(filterAttrs) && filterAttrs.stream().anyMatch(e -> e.getAttrValue() != null && e.getAttrValue().equals("上门费"));
        }
        return false;
    }

    /**
     * 尾款功能默认支持的三级团单类目
     */
    private static ToHomeFeeDisplayJudgeType isLifeToDoorCategory(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO == null || dealGroupDTO.getCategory() == null) {
            return null;
        }
        Long serviceTypeId = dealGroupDTO.getCategory().getServiceTypeId();
        List<Long> toHomeFixServiceTypes = Lion.getList(Environment.getAppName(), "com.sankuai.dzu.tpbase.dztgdetailweb.life.fix.tohome.category", Long.class, new ArrayList<>());
        if (CollectionUtils.isNotEmpty(toHomeFixServiceTypes) && serviceTypeId != null && toHomeFixServiceTypes.contains(serviceTypeId)) {
            return ToHomeFeeDisplayJudgeType.HasValueAndEqualToHomeFee;
        }
        List<Long> noValueServiceTypeConfig = Lion.getList(Environment.getAppName(), "com.sankuai.dzu.tpbase.dztgdetailweb.life.tohome.hasnovalue.deal.category", Long.class, new ArrayList<>());
        if (CollectionUtils.isNotEmpty(noValueServiceTypeConfig) && serviceTypeId != null && noValueServiceTypeConfig.contains(serviceTypeId)) {
            return ToHomeFeeDisplayJudgeType.HasNoValueOrNotFixedPrice;
        }
        return null;
    }

    public static boolean isWuyoutong(DealCtx ctx) {
        return isWuyoutong(ctx.getAttrs(), ctx.getMrnVersion());
    }

    public static boolean isWuyoutong(List<AttributeDTO> attrs, String mrnVersion) {
        if(StringUtils.isEmpty(mrnVersion) || CollectionUtils.isEmpty(attrs)) {
            return false;
        }
        String configMrnVersion = Lion.getString(Environment.getAppName(), LionConstants.WUYOUTONG_MIN_MRNVERSION);
        if(StringUtils.isEmpty(configMrnVersion)) {
            return false;
        }

        return VersionUtils.isGreatEqualThan(mrnVersion, configMrnVersion)
                && attrs != null
                && attrs.stream().anyMatch(DealAttrHelper::isWuyoutongAttribute);
    }

    private static boolean isWuyoutongAttribute(AttributeDTO attributeDTO) {
        if (attributeDTO == null || org.apache.commons.lang.StringUtils.isEmpty(attributeDTO.getName()) || org.apache.commons.collections4.CollectionUtils.isEmpty(attributeDTO.getValue())) {
            return false;
        }
        if (!"standardDealGroupKey".equals(attributeDTO.getName())) {
            return false;
        }

        String value = attributeDTO.getValue().get(0);
        List<String> wuyoutongValues = Lion.getList(LionConstants.WUYOUTONG_ATTR_VALUES, String.class, Lists.newArrayList());
        Set<String> wuyoutongValuesSet = new HashSet<>(wuyoutongValues);
        return StringUtils.isNotEmpty(value) && wuyoutongValuesSet.contains(value);
    }

    public static boolean isPreSaleV2(List<AttrDTO> attrs) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper.isPreSaleV2(java.util.List)");
        if (CollectionUtils.isEmpty(attrs)) {
            return false;
        }
        String available = getFirstValueV2(attrs, DealAttrKeys.PRE_SALE_TAG);
        return StringUtils.isNotEmpty(available) && available.equals(YES);
    }

    public static boolean isSupportShopService(List<AttributeDTO> attrs) {
        if (CollectionUtils.isEmpty(attrs)) {
            return false;
        }
        String available = getFirstValue(attrs, DealAttrKeys.SUPPORT_SHOP_SERVICE);
        return StringUtils.isNotEmpty(available) && available.equals("是");
    }

    public static boolean isSupportHomeService(List<AttributeDTO> attrs) {
        if (CollectionUtils.isEmpty(attrs)) {
            return false;
        }
        String available = getFirstValue(attrs, DealAttrKeys.SUPPORT_HOME_SERVICE);
        return StringUtils.isNotEmpty(available) && available.equals("是");
    }

    public static boolean partHolidayAvailable(List<AttributeDTO> attrs) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper.partHolidayAvailable(java.util.List)");
        if (CollectionUtils.isEmpty(attrs)) {
            return false;
        }
        List<String> attrDisableDays = getAttributeValues(attrs, DealAttrKeys.HOLIDAY_AVAILABLE);
        if (CollectionUtils.isNotEmpty(attrDisableDays) && attrDisableDays.size() == 3 && attrDisableDays.get(0) == "0"
                && attrDisableDays.contains("101") && attrDisableDays.contains("102")) {
            return true;
        }
        return false;
    }

    public static boolean allHolidayDisable(List<AttributeDTO> attrs) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper.allHolidayDisable(java.util.List)");
        if (CollectionUtils.isEmpty(attrs)) {
            return false;
        }
        List<String> attrDisableDays = getAttributeValues(attrs, DealAttrKeys.HOLIDAY_AVAILABLE);
        if (CollectionUtils.isNotEmpty(attrDisableDays) && attrDisableDays.size() == 10 && attrDisableDays.get(0) == "0") {
            return true;
        }
        return false;
    }

    public static boolean onlyVerificationOne(List<AttrDTO> attrs){
        if (CollectionUtils.isEmpty(attrs)) {
            return false;
        }
        String firstValue = getFirstValueV2(attrs, DealAttrKeys.SINGLE_VERIFICATION_QUANTITY_DESC);
        if (StringUtils.isBlank(firstValue)){
            return false;
        }
        return "单次到店仅可核销一次，仅能一人使用".equals(firstValue);
    }

    public static String getTimes(List<AttrDTO> attrs){
        if (CollectionUtils.isEmpty(attrs)) {
            return null;
        }
        return getFirstValueV2(attrs, DealAttrKeys.SYS_MULTI_SALE_NUMBER);
    }

    public static String getServiceType(List<AttrDTO> attrs){
        if (CollectionUtils.isEmpty(attrs)) {
            return null;
        }
        return getFirstValueV2(attrs, DealAttrKeys.SERVICE_TYPE);
    }

    /**
     * 判断节日类型是否可以用
     * @param dealGroupDTO
     * @param disableDayType
     * @return
     */
    public static boolean hitDisableDateType(DealGroupDTO dealGroupDTO, int disableDayType){
        if (Objects.isNull(dealGroupDTO)
                || Objects.isNull(dealGroupDTO.getRule())
                || Objects.isNull(dealGroupDTO.getRule().getUseRule())
                || Objects.isNull(dealGroupDTO.getRule().getUseRule().getDisableDate())
                || CollectionUtils.isEmpty(dealGroupDTO.getRule().getUseRule().getDisableDate().getDisableDays())
        ) {
            return false;
        }
        List<Integer> disableDays = dealGroupDTO.getRule().getUseRule().getDisableDate().getDisableDays();
        return disableDays.contains(disableDayType);
    }


    public static boolean judgeDisableUsable(DealGroupDTO dealGroupDTO) {
        return !judgeUsable(dealGroupDTO);
    }

    public static boolean judgeUsable(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO == null ||dealGroupDTO.getRule() == null || dealGroupDTO.getRule().getUseRule() == null) {
            return true;
        }
        LocalDate localDate = LocalDate.now();
        DayOfWeek dayOfWeek = localDate.getDayOfWeek();
        int dayOfWeekValue = dayOfWeek.getValue();

        DealGroupUseRuleDTO useRuleDTO = dealGroupDTO.getRule().getUseRule();
        // 判断不可用时间
        if (useRuleDTO.getDisableDate() != null){
            if (CollectionUtils.isNotEmpty(useRuleDTO.getDisableDate().getDisableDateRangeDTOS())) {
                boolean hasNotUsable = useRuleDTO.getDisableDate().getDisableDateRangeDTOS().stream().filter(e -> e.getFrom() != null && e.getTo() != null)
                        .anyMatch(e ->DateHelper.isCurrentDateInRange(e.getFrom(), e.getTo()));
                if (hasNotUsable) {
                    return false;
                }
            }
            if (CollectionUtils.isNotEmpty(useRuleDTO.getDisableDate().getDisableDays())) {
                //如果还有周几(1~7)不可用，则返回不可用
                if (useRuleDTO.getDisableDate().getDisableDays().contains(dayOfWeekValue)) {
                    return false;
                }

            }
        }

        //判断可用时间
        // 可用日期类型：0：周期使用，1：指定日期适用
        if (useRuleDTO.getAvailableDate() != null
                && useRuleDTO.getAvailableDate().getAvailableType() != null
                && useRuleDTO.getAvailableDate().getAvailableType() == 0) {
            // 周期时间可用 例如：周一 ~~ 周日 可用，判断字段 CycleAvailableDateList
            if (useRuleDTO.getAvailableDate().getCycleAvailableDateList() != null) {
                Set<Integer> availableDaysSet = useRuleDTO.getAvailableDate().getCycleAvailableDateList().stream()
                        .filter(e -> e.getAvailableDays() != null)
                        .flatMap(e -> e.getAvailableDays().stream())
                        .collect(Collectors.toSet());
                if (!availableDaysSet.contains(dayOfWeekValue)) {
                    return false;
                }
            }
        }else if (useRuleDTO.getAvailableDate() != null
                && useRuleDTO.getAvailableDate().getAvailableType() != null
                && useRuleDTO.getAvailableDate().getAvailableType() == 1){
            // 指定日期可用 判断字段 specifiedDurationDateList
            if (useRuleDTO.getAvailableDate().getSpecifiedDurationDateList() != null) {
                boolean hasAvailableDateRange = validSpecifiedDurationDateList(useRuleDTO.getAvailableDate().getSpecifiedDurationDateList());
                if (hasAvailableDateRange){
                    return true;
                }else {
                    return false;
                }
            }
        }
        return true;
    }

    public static boolean validSpecifiedDurationDateList(List<AvailableDurationDateDTO> specifiedDurationDateList){
        if (specifiedDurationDateList != null) {
            return specifiedDurationDateList.stream()
                    .filter(e->Objects.nonNull(e) && CollectionUtils.isNotEmpty(e.getAvailableDateRangeDTOS()))
                    .anyMatch(e -> validAvailableDateRange(e.getAvailableDateRangeDTOS()));
        }
        return false;
    }

    public static boolean validAvailableDateRange(List<DateRangeDTO> availableDateRangeDTOS){
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(availableDateRangeDTOS)){
            return false;
        }
        return availableDateRangeDTOS.stream().anyMatch(e-> DateHelper.isCurrentDateInRange(e.getFrom(), e.getTo()));
    }

}
