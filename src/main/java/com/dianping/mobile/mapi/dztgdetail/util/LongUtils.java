package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class LongUtils {

    private static final Logger logger = LoggerFactory.getLogger(LongUtils.class);

    public static List<Integer> toInt(List<Long> values){
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.util.LongUtils.toInt(java.util.List)");
        List<Integer> result = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(values)){
            for(Long temp : values){
                result.add(toInt(temp));
            }
        }
        return result;
    }

    public static int toInt(Long value){
        if(value == null)
            return 0;
        long temp = value;
        try{
            return (int) temp;
        }catch (Exception e){
            logger.error("toInt error",e);
            return 0;
        }
    }
}
