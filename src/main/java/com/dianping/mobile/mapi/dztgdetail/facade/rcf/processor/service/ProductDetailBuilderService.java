package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.deal.common.builder.RichTextBuilder;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BannerTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBanner;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PromoDetailModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.Pair;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.DetailPriceVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.DetailSaleVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.InventoryModuleVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.ModulePriceDiscountDetail;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.ProductAtmosphereModuleVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.ProductDetailModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.ProductPriceModuleVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.StructDetailVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.action.extend.buy.SuckBottomBuyActionVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.action.extend.none.SuckBottomDoNothingActionVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.action.extend.redirect.SuckBottomRedirectActionVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.banner.BottomBarTopBannerVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.enums.BottomBarActionTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.backgroud.BottomBarBackgroundVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.rich.content.PicRichContentVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.rich.content.RichContentVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.rich.content.TextRichContentVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.rich.content.enums.RichContentTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.multisku.MultiSkuSelectItemVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.multisku.MultiSkuSelectVO;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.TimesDealUtil;
import com.google.common.collect.Lists;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericModuleResponse;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericProductDetailPageResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-04-07
 * @desc
 */
@Slf4j
@Component
public class ProductDetailBuilderService {

    public ProductDetailModule build(DealCtx ctx) {
        ProductDetailModule commonModule = buildCommonModule(ctx);
        ProductDetailModule tradeModule = buildTradeModule(ctx);
        ProductDetailModule result = new ProductDetailModule();
        if (Objects.nonNull(commonModule)) {
            result.setStructDetail(commonModule.getStructDetail());
            result.setDealInventory(commonModule.getDealInventory());
        }
        if (Objects.nonNull(tradeModule)) {
            result.setProductPrice(tradeModule.getProductPrice());
            result.setProductAtmosphere(tradeModule.getProductAtmosphere());
            result.setPromoDetail(tradeModule.getPromoDetail());
            result.setMultiSkuSelectVO(tradeModule.getMultiSkuSelectVO());
            // 处理底部跳链，当有多sku模块时，跳链中skuid要与选中的sku保持一致
            if (Objects.nonNull(tradeModule.getMultiSkuSelectVO())) {
                processButtonBuyUrl(tradeModule.getMultiSkuSelectVO().getSkuSelectItems(), ctx.getResult(), ctx.getDealBaseReq());
            }
        }
        return result;
    }

    private void processButtonBuyUrl(List<MultiSkuSelectItemVO> skuSelectItems, DealGroupPBO result, DealBaseReq dealBaseReq) {
        if (StringUtils.isNotBlank(dealBaseReq.getSkuId())) {
            return;
        }
        if (CollectionUtils.isEmpty(skuSelectItems)) {
            return;
        }
        MultiSkuSelectItemVO skuItem = skuSelectItems.get(0);
        if (Objects.isNull(skuItem) || skuItem.getSkuId() <= 0) {
            return;
        }
        result.setSkuId(String.valueOf(skuItem.getSkuId()));
        dealBaseReq.setSkuId(String.valueOf(skuItem.getSkuId()));
    }

    public ProductDetailModule buildTradeModule(DealCtx ctx) {
        try {
            GenericProductDetailPageResponse response = ctx.getProductDetailTradeModuleResponse();
            if (Objects.isNull(response) || MapUtils.isEmpty(response.getModuleResponse())) {
                return null;
            }
            ProductDetailModule productDetailModule = new ProductDetailModule();
            Map<String, GenericModuleResponse> moduleResponse = response.getModuleResponse();
            // 氛围条
            GenericModuleResponse atmospherePriceBar = moduleResponse.get("module_detail_deal_atmosphere_price_sale_bar");
            ctx.setHideMemberCardGuide(false);
            if (Objects.nonNull(atmospherePriceBar) && Objects.nonNull(atmospherePriceBar.getModuleVO())) {
                ProductAtmosphereModuleVO atmosphereModuleVO = JSON.parseObject(JSON.toJSONString(atmospherePriceBar.getModuleVO()), ProductAtmosphereModuleVO.class);
                if (Objects.nonNull(atmosphereModuleVO) && Objects.nonNull(atmosphereModuleVO.getAtmosphere())
                        && atmosphereModuleVO.getAtmosphere().isShowNewAtmosphereBar()) {
                    ctx.setHideMemberCardGuide(true);
                }
                productDetailModule.setProductAtmosphere(atmosphereModuleVO);
            }
            // 价格条
            GenericModuleResponse priceSaleBar = moduleResponse.get("module_detail_deal_price_sale_bar");
            if ((Objects.isNull(atmospherePriceBar) || Objects.isNull(atmospherePriceBar.getModuleVO()))
                    && Objects.nonNull(priceSaleBar) && Objects.nonNull(priceSaleBar.getModuleVO())) {
                ProductPriceModuleVO productPriceModuleVO = JSON.parseObject(JSON.toJSONString(priceSaleBar.getModuleVO()), ProductPriceModuleVO.class);
                productDetailModule.setProductPrice(productPriceModuleVO);
                // 次卡->连续包月
                if (TimesDealUtil.isMonthlySubscription(ctx)) {
                    //价格处理逻辑
                    DetailPriceVO detailPriceVO = productPriceModuleVO.getPrice();
                    PromoDetailModule promoDetail = ctx.getResult().getPromoDetailModule();
                    if(Objects.nonNull(promoDetail) && Objects.nonNull(detailPriceVO)){
                        // 优惠标签
                        String discountTag = detailPriceVO.getPriceSymbol() + detailPriceVO.getDiscountTag();
                        promoDetail.setMarketPromoDiscount(discountTag);
                        promoDetail.setPlainMarketPromoDiscount(discountTag);
                        if(StringUtils.isNotBlank(promoDetail.getSinglePrice())) {
                            promoDetail.setSinglePrice("");
                        }
                        if(StringUtils.isNotBlank(promoDetail.getCopies())) {
                            promoDetail.setCopies("");
                        }
                        if(StringUtils.isNotBlank(promoDetail.getPricePerUnit())) {
                            promoDetail.setPricePerUnit("");
                        }
                        if(StringUtils.isNotBlank(promoDetail.getTimesUnit())) {
                            promoDetail.setTimesUnit("");
                        }
                        // 价格
                        promoDetail.setPromoPrice(detailPriceVO.getFinalPrice());
                        promoDetail.setFinalPrice(detailPriceVO.getFinalPrice());
                        ctx.getResult().setPromoDetailModule(promoDetail);
                    }
                    // 销量
                    DetailSaleVO detailSaleVO = productPriceModuleVO.getSale();
                    ctx.getResult().setSaleDescStr(detailSaleVO.getSaleTag());

                }
            }
            // 多SKU选择
            GenericModuleResponse multiSkuSelect = moduleResponse.get("module_detail_deal_multi_sku_select");
            if (Objects.nonNull(multiSkuSelect) && Objects.nonNull(multiSkuSelect.getModuleVO())) {
                MultiSkuSelectVO multiSkuSelectVO = JSON.parseObject(JSON.toJSONString(multiSkuSelect.getModuleVO()), MultiSkuSelectVO.class);
                productDetailModule.setMultiSkuSelectVO(multiSkuSelectVO);
            }
            // 优惠浮层
            GenericModuleResponse promotionPop = moduleResponse.get("module_price_discount_detail");
            if (Objects.nonNull(promotionPop) && Objects.nonNull(promotionPop.getModuleVO())) {
                ModulePriceDiscountDetail promoDetailModule = JSON.parseObject(JSON.toJSONString(promotionPop.getModuleVO()), ModulePriceDiscountDetail.class);
                productDetailModule.setPromoDetail(promoDetailModule);
                // 如果是国补商品，则使用新团详的优惠浮层模型
                if (ctx.isCountrySubsidiesProduct() && Objects.nonNull(ctx.getResult()) && Objects.nonNull(promoDetailModule)) {
                    ctx.getResult().setPromoDetailModule(promoDetailModule.getPromoDetails());
                }
            }
            // 底部banner
            GenericModuleResponse bottomBar = moduleResponse.get("module_detail_deal_bottom_bar");
            if (Objects.nonNull(bottomBar) && Objects.nonNull(bottomBar.getModuleVO())) {
                // 次卡->连续包月
                if (TimesDealUtil.isMonthlySubscription(ctx)) {
                    List<DealBuyBtn> dealBuyBtns = processMonthlySubscriptionBottomBarModule(bottomBar);
                    if (CollectionUtils.isNotEmpty(dealBuyBtns)) {
                        ctx.getResult().getBuyBar().setBuyBtns(dealBuyBtns);
                    }
                    return productDetailModule;
                }

                // 会员卡的优先级最高 > 神券吸底条 > 其他
                if (Objects.nonNull(ctx.getResult()) && Objects.nonNull(ctx.getResult().getBuyBar())
                        && Objects.nonNull(ctx.getResult().getBuyBar().getBuyBanner())
                        && Objects.equals(ctx.getResult().getBuyBar().getBuyBanner().getBannerType(), BannerTypeEnum.MemberExclusive.getType())) {
                    return productDetailModule;
                }
                BottomBarTopBannerVO bannerVO = processBottomBarModule(bottomBar);
                processBottomBanner(bannerVO, ctx.getResult());
            }
            return productDetailModule;
        } catch (Exception e) {
            log.error("build product detail module error, ctx:{}", ctx, e);
        }
        return null;
    }

    private void processBottomBanner(BottomBarTopBannerVO bannerVO, DealGroupPBO result) {
        if (Objects.isNull(bannerVO) || Objects.isNull(result) || Objects.isNull(result.getBuyBar())) {
            return;
        }
        if (Objects.equals(bannerVO.getBannerType(), 8)) {
            processMagicCouponBottomBanner(bannerVO, result);
            return;
        }
        if (Objects.equals(bannerVO.getBannerType(), 1)) {
            processCountrySubsidiesBottomBanner(bannerVO, result);
        }
    }

    public List<DealBuyBtn> processMonthlySubscriptionBottomBarModule(GenericModuleResponse bottomBar) {
        try {
            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(bottomBar.getModuleVO()));
            List<DealBuyBtn> buyBtns = new ArrayList<>();

            // 使用自定义反序列化器处理 StandardTradeBottomBarVO
            StandardTradeBottomBarVO standardTradeBottomBarVO = toStandardTradeBottomBarVO(jsonObject.getJSONObject("bottomBar"));

            if (Objects.isNull(standardTradeBottomBarVO)) {
                return null;
            }

            DealBuyBtn dealBuyBtn = new DealBuyBtn(true);
            StandardTradeBlockVO rightBottomBar = standardTradeBottomBarVO.getRightBottomBar();
            if (Objects.isNull(rightBottomBar) || CollectionUtils.isEmpty(rightBottomBar.getButtonList())) {
                return null;
            }

            List<BaseTradeButtonVO> baseTradeButtonVOS = rightBottomBar.getButtonList();
            if (CollectionUtils.isEmpty(baseTradeButtonVOS)) {
                return null;
            }

            // 获取第一个按钮并安全地转换为 StandardTradeButtonVO
            BaseTradeButtonVO buttonVO = baseTradeButtonVOS.get(0);
            if (Objects.isNull(buttonVO) || !(buttonVO instanceof StandardTradeButtonVO)) {
                return null;
            }

            StandardTradeButtonVO standardTradeButtonVO = (StandardTradeButtonVO) buttonVO;
            if (CollectionUtils.isEmpty(standardTradeButtonVO.getMainTitle())) {
                return null;
            }

            // 设置按钮标题
            TextRichContentVO mainTitle = (TextRichContentVO) standardTradeButtonVO.getMainTitle().get(0);
            dealBuyBtn.setBtnTitle(mainTitle.getText());

            // 设置按钮副标题
            if (!CollectionUtils.isEmpty(standardTradeButtonVO.getSubTitle())) {
                TextRichContentVO subTitle = (TextRichContentVO) standardTradeButtonVO.getSubTitle().get(0);
                dealBuyBtn.setBtnSubTitle(subTitle.getText());
            }

            // 设置跳转链接
            BottomBarActionDataVO actionDataVO = standardTradeButtonVO.getActionData();
            if (actionDataVO instanceof BuyActionVO) {
                BuyActionVO actionData = (BuyActionVO) actionDataVO;
                dealBuyBtn.setRedirectUrl(actionData.getUrl());
            }
            buyBtns.add(dealBuyBtn);
            return buyBtns;
        } catch (Exception e) {
            log.error("处理数据异常, monthlySubscriptionBottomBar:{}", bottomBar, e);
            return null;
        }
    }

    private  StandardTradeBottomBarVO toStandardTradeBottomBarVO(JSONObject jsonObject) {
        if (jsonObject == null) {
            return null;
        }

        StandardTradeBottomBarVO bottomBarVO = new StandardTradeBottomBarVO();

        // 解析 rightBottomBar
        JSONObject rightBottomBarJson = jsonObject.getJSONObject("rightBottomBar");
        if (rightBottomBarJson != null) {
            StandardTradeBlockVO rightBottomBar = new StandardTradeBlockVO();

            // 解析 buttonList
            List<BaseTradeButtonVO> buttonList = new ArrayList<>();
            if (rightBottomBarJson.containsKey("buttonList")) {
                List<JSONObject> buttonJsonList = rightBottomBarJson.getJSONArray("buttonList").toJavaList(JSONObject.class);
                for (JSONObject buttonJson : buttonJsonList) {
                    // 创建 StandardTradeButtonVO 对象并手动设置属性
                    StandardTradeButtonVO buttonVO = new StandardTradeButtonVO();

                    // 处理 mainTitle
                    if (buttonJson.containsKey("mainTitle")) {
                        List<RichContentVO> mainTitleList = new ArrayList<>();
                        JSONArray mainTitleArray = buttonJson.getJSONArray("mainTitle");
                        if (mainTitleArray != null) {
                            for (int i = 0; i < mainTitleArray.size(); i++) {
                                JSONObject titleJson = mainTitleArray.getJSONObject(i);
                                TextRichContentVO textRichContentVO = new TextRichContentVO();
                                textRichContentVO.setText(titleJson.getString("text"));
                                textRichContentVO.setTextStyle(titleJson.getString("textStyle"));
                                mainTitleList.add(textRichContentVO);
                            }
                        }
                        buttonVO.setMainTitle(mainTitleList);
                    }

                    // 处理 subTitle
                    if (buttonJson.containsKey("subTitle")) {
                        List<RichContentVO> subTitleList = new ArrayList<>();
                        JSONArray subTitleArray = buttonJson.getJSONArray("subTitle");
                        if (subTitleArray != null) {
                            for (int i = 0; i < subTitleArray.size(); i++) {
                                JSONObject titleJson = subTitleArray.getJSONObject(i);
                                TextRichContentVO textRichContentVO = new TextRichContentVO();
                                textRichContentVO.setText(titleJson.getString("text"));
                                textRichContentVO.setTextStyle(titleJson.getString("textStyle"));
                                subTitleList.add(textRichContentVO);
                            }
                        }
                        buttonVO.setSubTitle(subTitleList);
                    }

                    // 处理 actionData
                    if (buttonJson.containsKey("actionData")) {
                        JSONObject actionDataJson = buttonJson.getJSONObject("actionData");
                        if (actionDataJson != null) {
                            String actionType = actionDataJson.getString("actionType");
                            if ("Buy".equals(actionType)) {
                                BuyActionVO buyActionVO = new BuyActionVO();
                                buyActionVO.setUrl(actionDataJson.getString("url"));
                                buyActionVO.setOpenType(actionDataJson.getString("openType"));
                                buttonVO.setActionData(buyActionVO);
                            }
                        }
                    }
                    buttonList.add(buttonVO);
                }
            }

            rightBottomBar.setButtonList(buttonList);
            bottomBarVO.setRightBottomBar(rightBottomBar);
        }

        return bottomBarVO;
    }

    private BottomBarTopBannerVO processBottomBarModule(GenericModuleResponse bottomBar) {
        if (Objects.isNull(bottomBar) || Objects.isNull(bottomBar.getModuleVO())) {
            return null;
        }
        BottomBarTopBannerVO banner = new BottomBarTopBannerVO();
        try {
            JSONArray topBannerList = bottomBar.getModuleVO().getJSONArray("topBannerList");
            if (CollectionUtils.isEmpty(topBannerList)) {
                return null;
            }
            JSONObject topBanner = topBannerList.getJSONObject(0);
            // 背景
            JSONObject backgroundJson = topBanner.getJSONObject("background");
            if (Objects.nonNull(backgroundJson)) {
                BottomBarBackgroundVO background = backgroundJson.toJavaObject(BottomBarBackgroundVO.class);
                banner.setBackground(background);
            }
            // banner类型
            banner.setBannerType(topBanner.getInteger("bannerType"));
            // banner展示数据
            List<RichContentVO> bannerDataList = new ArrayList<>();
            JSONArray bannerData = topBanner.getJSONArray("bannerData");
            if (CollectionUtils.isEmpty(bannerData)) {
                return null;
            }
            boolean isUnknown = false;
            for (int i = 0; i < bannerData.size(); i++) {
                JSONObject bannerDataJson = bannerData.getJSONObject(i);
                int type = bannerDataJson.getIntValue("type");
                RichContentTypeEnum richContentTypeEnum = RichContentTypeEnum.fromCode(type);
                if (richContentTypeEnum == RichContentTypeEnum.UNKNOWN) {
                    isUnknown = true;
                    break;
                }
                RichContentVO richContentVO = bannerDataJson.toJavaObject(richContentTypeEnum.getRichContentVOClass());
                bannerDataList.add(richContentVO);
            }
            if (isUnknown) {
                return null;
            }
            banner.setBannerData(bannerDataList);
            // actionData
            JSONObject actionDataJson = topBanner.getJSONObject("actionData");
            if (Objects.nonNull(actionDataJson)) {
                int actionType = actionDataJson.getIntValue("actionType");
                BottomBarActionTypeEnum actionTypeEnum = BottomBarActionTypeEnum.fromCode(actionType);
                BottomBarActionDataVO actionDataVO = actionDataJson.toJavaObject(actionTypeEnum.getActionVOClass());
                banner.setActionData(actionDataVO);
            }
        } catch (Exception e) {
            log.error("processBottomBarModule error, bottomBar:{}", bottomBar, e);
        }
        return banner;
    }

    public void processCountrySubsidiesBottomBanner(BottomBarTopBannerVO barTopBannerVO, DealGroupPBO result) {
        if (Objects.isNull(barTopBannerVO) || Objects.isNull(result) || Objects.isNull(result.getBuyBar())) {
            return;
        }
        DealBuyBanner dealBuyBanner = new DealBuyBanner();
        dealBuyBanner.setShow(true);
        int bannerType = barTopBannerVO.getBannerType();
        if (Objects.equals(barTopBannerVO.getBannerType(), 1)) {
            bannerType = 1;
        }
        // banner类型
        dealBuyBanner.setBannerType(bannerType);
        // banner背景色
        BottomBarBackgroundVO background = barTopBannerVO.getBackground();
        if (Objects.nonNull(background)) {
            dealBuyBanner.setBackGroundColor(background.getColors());
        }
        // banner内容
        dealBuyBanner.setContent(buildCountrySubsidiesBannerContent(barTopBannerVO.getBannerData()));
        RichContentVO richContentVO = CollectionUtils.isNotEmpty(barTopBannerVO.getBannerData()) ? barTopBannerVO.getBannerData().get(0) : null;
        if (Objects.nonNull(richContentVO) && richContentVO.getType() == RichContentTypeEnum.PIC.getCode()) {
            PicRichContentVO picRichContentVO = (PicRichContentVO) richContentVO;
            dealBuyBanner.setIconUrl(picRichContentVO.getIconUrl());
            dealBuyBanner.setIconWidth(picRichContentVO.getIconWidth());
            dealBuyBanner.setIconHeight(picRichContentVO.getIconHeight());
        }
        result.getBuyBar().setBuyBanner(dealBuyBanner);
    }

    public String buildCountrySubsidiesBannerContent(List<RichContentVO> bannerData) {
        if (CollectionUtils.isEmpty(bannerData)) {
            return null;
        }
        int textSize = 12;
        RichTextBuilder.TextItem textItem = null;
        textItem = new RichTextBuilder.TextItem();
        textItem.setTextsize(textSize);
        textItem.setTextstyle(RichTextBuilder.TextStyle.DEFAULT.getStyle());
        textItem.setTextcolor("#222222");
        textItem.setBackgroundcolor(StringUtils.EMPTY);
        StringBuilder sourceText = new StringBuilder();
        for (RichContentVO richContentVO : bannerData) {
            RichContentTypeEnum richContentTypeEnum = RichContentTypeEnum.fromCode(richContentVO.getType());
            if (richContentTypeEnum == RichContentTypeEnum.PIC) {
                continue;
            }
            TextRichContentVO textRichContentVO = (TextRichContentVO) richContentVO;
            sourceText.append(textRichContentVO.getText());

        }

        String regex = "(当前定位)|(国补城市)(?=,)";
        RichTextBuilder.TextItem highlightItem = null;
        highlightItem = new RichTextBuilder.TextItem();
        highlightItem.setTextcolor("#00A72D");
        highlightItem.setTextsize(textSize);
        highlightItem.setTextstyle(RichTextBuilder.TextStyle.BOLD.getStyle());

        return new RichTextBuilder(sourceText.toString(), regex)
                .setDefaultTextItem(textItem)
                .setHltTextItem(highlightItem)
                .build().toString();
    }

    private void processMagicCouponBottomBanner(BottomBarTopBannerVO barTopBannerVO, DealGroupPBO result) {
        if (Objects.isNull(result) || Objects.isNull(result.getBuyBar()) || Objects.isNull(barTopBannerVO)) {
            return;
        }
        DealBuyBanner dealBuyBanner = new DealBuyBanner();
        BottomBarBackgroundVO background = barTopBannerVO.getBackground();
        dealBuyBanner.setBackGroundColor(background.getColors());
        dealBuyBanner.setContent(buildBannerContent(barTopBannerVO.getBannerData()));
        dealBuyBanner.setBannerType(barTopBannerVO.getBannerType());
        dealBuyBanner.setShow(true);
        int actionType = barTopBannerVO.getActionData().getActionType();
        if (actionType == BottomBarActionTypeEnum.FLOATING_LAYER.getCode()) {
            SuckBottomRedirectActionVO actionData = (SuckBottomRedirectActionVO) barTopBannerVO.getActionData();
            if (Objects.nonNull(actionData)) {
                dealBuyBanner.setLeadAction(actionData.getActionType());
                dealBuyBanner.setLeadUrl(actionData.getUrl());
                dealBuyBanner.setLeadText(actionData.getText());
                dealBuyBanner.setLeadTextColor("#555555");
                dealBuyBanner.setIconUrl(actionData.getIcon());
                dealBuyBanner.setArrowIconUrl("https://p0.meituan.net/dztgdetailimages/e5e1506b89c4ad71660d9f8d9e52d299309.png");
                result.getBuyBar().setBuyBanner(dealBuyBanner);
            }
        }
        // 吸底条-无动作
        if (actionType == BottomBarActionTypeEnum.SUCK_BOTTOM_NOTHING.getCode()) {
            SuckBottomDoNothingActionVO actionData = (SuckBottomDoNothingActionVO) barTopBannerVO.getActionData();
            if (Objects.nonNull(actionData)) {
                dealBuyBanner.setLeadAction(BottomBarActionTypeEnum.NOTHING.getCode());
                dealBuyBanner.setLeadTextColor("#555555");
                dealBuyBanner.setIconUrl(actionData.getIcon());
                dealBuyBanner.setArrowIconUrl("https://p0.meituan.net/dztgdetailimages/e5e1506b89c4ad71660d9f8d9e52d299309.png");
                result.getBuyBar().setBuyBanner(dealBuyBanner);
            }
        }
        // 吸底条-跳提单页
        if (actionType == BottomBarActionTypeEnum.SUCK_BOTTOM_BUY.getCode()) {
            SuckBottomBuyActionVO actionData = (SuckBottomBuyActionVO) barTopBannerVO.getActionData();
            if (Objects.nonNull(actionData)) {
                dealBuyBanner.setLeadAction(BottomBarActionTypeEnum.BUY.getCode());
                dealBuyBanner.setLeadUrl(actionData.getUrl());
                dealBuyBanner.setLeadText(actionData.getText());
                dealBuyBanner.setLeadTextColor("#555555");
                dealBuyBanner.setIconUrl(actionData.getIcon());
                dealBuyBanner.setArrowIconUrl("https://p0.meituan.net/dztgdetailimages/e5e1506b89c4ad71660d9f8d9e52d299309.png");
                result.getBuyBar().setBuyBanner(dealBuyBanner);
            }
        }
    }

    private String buildBannerContent(List<RichContentVO> bannerData) {
        if (CollectionUtils.isEmpty(bannerData)) {
            return null;
        }
        int textSize = 12;
        RichTextBuilder.TextItem textItem = null;
        RichTextBuilder.TextItem highlightItem = null;
        StringBuilder sourceText = new StringBuilder();
        //StringBuilder regex = new StringBuilder();
        for (RichContentVO richContentVO : bannerData) {
            TextRichContentVO textRichContentVO = (TextRichContentVO) richContentVO;
            sourceText.append(textRichContentVO.getText());
            if (Objects.equals(textRichContentVO.getTextStyle(), "Default")) {
                textItem = new RichTextBuilder.TextItem();
                textItem.setTextsize(textSize);
                textItem.setTextstyle(RichTextBuilder.TextStyle.DEFAULT.getStyle());
                textItem.setTextcolor("#222222");
            } else if (Objects.equals(textRichContentVO.getTextStyle(), "Bold")) {
                //appendSafe(regex,textRichContentVO.getText());
                highlightItem = new RichTextBuilder.TextItem();
                highlightItem.setTextcolor("#FF4B10");
                highlightItem.setTextsize(textSize);
                highlightItem.setTextstyle(RichTextBuilder.TextStyle.BOLD.getStyle());
            }

        }

        String regex = "(\\d+|一单回本)";
        if ( highlightItem == null){
            highlightItem = new RichTextBuilder.TextItem();
            highlightItem.setTextcolor("#FF4B10");
            highlightItem.setTextsize(textSize);
            highlightItem.setTextstyle(RichTextBuilder.TextStyle.BOLD.getStyle());
        }
        return new RichTextBuilder(String.valueOf(sourceText), regex)
                .setDefaultTextItem(textItem)
                .setHltTextItem(highlightItem)
                .build().toString();
    }

    private ProductDetailModule buildCommonModule(DealCtx ctx) {
        try {
            GenericProductDetailPageResponse response = ctx.getCommonModuleResponse();
            if (Objects.isNull(response) || MapUtils.isEmpty(response.getModuleResponse())) {
                return null;
            }
            ProductDetailModule productDetailModule = new ProductDetailModule();
            Map<String, GenericModuleResponse> moduleResponse = response.getModuleResponse();
            // 结构化详情
            GenericModuleResponse structuredDetailResponse = moduleResponse.get("module_detail_structured_detail");
            if (Objects.nonNull(structuredDetailResponse) && Objects.nonNull(structuredDetailResponse.getModuleVO())) {
                StructDetailVO structDetailVO = JSON.parseObject(JSON.toJSONString(structuredDetailResponse.getModuleVO()), StructDetailVO.class);
                productDetailModule.setStructDetail(Optional.ofNullable(structDetailVO).map(StructDetailVO::getDealDetails).orElse(Lists.newArrayList()));
            }
            // 商品分类和属性信息模型
            GenericModuleResponse inventoryResponse = moduleResponse.get("module_detail_inventory_module");
            if (Objects.nonNull(inventoryResponse) && Objects.nonNull(inventoryResponse.getModuleVO())) {
                InventoryModuleVO inventoryModuleVO = JSON.parseObject(JSON.toJSONString(inventoryResponse.getModuleVO()), InventoryModuleVO.class);
                productDetailModule.setDealInventory(Optional.ofNullable(inventoryModuleVO).map(InventoryModuleVO::getInventoryDetails).orElse(
                        Lists.newArrayList()));
            }
            return productDetailModule;
        } catch (Exception e) {
            log.error("build common module error, ctx:{}", ctx, e);
        }
        return null;
    }



    public List<Pair> buildRichText(DealCtx ctx) {
        boolean hitCpvCategoryId = LionConfigUtils.isHitCpvCategoryId(ctx.getCategoryId());
        List<Pair> structuredDetails = ctx.getStructedDetails();
        if (CollectionUtils.isEmpty(structuredDetails) || !hitCpvCategoryId) {
            return structuredDetails;
        }

        Pair targetPair = structuredDetails.stream().filter(Objects::nonNull)
                .filter(pair -> StringUtils.equals("套餐", pair.getId()) || StringUtils.equals("套餐", pair.getID()) || StringUtils.equals("套餐", pair.getKey()))
                .findFirst().orElse(null);

        String richText = Optional.ofNullable(buildInventoryModuleVO(ctx)).map(InventoryModuleVO::getRichText).orElse(null);
        if (StringUtils.isBlank(richText)) {
            return structuredDetails;
        }

        if (Objects.isNull(targetPair)) {
            targetPair = new Pair();
            targetPair.setName(richText);
            targetPair.setID("套餐");
            targetPair.setId("套餐");
            targetPair.setKey("套餐");
            targetPair.setType(1);
            structuredDetails.add(targetPair);
            return structuredDetails;
        }

        targetPair.setName(richText);

        return structuredDetails;
    }

    public InventoryModuleVO buildInventoryModuleVO(DealCtx ctx) {
        try {
            GenericProductDetailPageResponse response = ctx.getCommonModuleResponse();
            if (Objects.isNull(response) || MapUtils.isEmpty(response.getModuleResponse())) {
                return null;
            }
            Map<String, GenericModuleResponse> moduleResponse = response.getModuleResponse();
            GenericModuleResponse inventoryResponse = moduleResponse.get("module_detail_inventory_module");
            if (Objects.isNull(inventoryResponse) || Objects.isNull(inventoryResponse.getModuleVO())) {
                return null;
            }
            return JSON.parseObject(JSON.toJSONString(inventoryResponse.getModuleVO()), InventoryModuleVO.class);
        } catch (Exception e) {
            log.error("build common module error, ctx:{}", ctx, e);
        }
        return null;
    }

}
