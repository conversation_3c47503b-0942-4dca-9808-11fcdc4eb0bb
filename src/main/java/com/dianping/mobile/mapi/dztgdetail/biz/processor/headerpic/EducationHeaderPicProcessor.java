package com.dianping.mobile.mapi.dztgdetail.biz.processor.headerpic;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ContentType;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ImageScaleEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.video.ExtendVideoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2023/9/13
 */
@Component("educationHeaderPicProcessor")
@Slf4j
public class EducationHeaderPicProcessor extends AbstractHeaderPicProcessor {

    public static final String COLON_SIGN = ":";

    @Override
    public void fillPicScale(DealCtx ctx, List<ContentPBO> result, DealGroupPBO dealGroupPBO) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        //图片展示默认比例为16:9
        String imageScale = ImageScaleEnum.SIXTEEN_TO_NINE.getScale();
        //根据视频原始尺寸，设置展示尺寸，并且图片展示尺寸和视频尺寸保持一致
        for (ContentPBO contentPBO : result) {
            if (contentPBO.getType() == ContentType.VIDEO.getType()) {
                assembleVideoScale(ctx.getDealGroupDTO(), contentPBO);
                imageScale = contentPBO.getScale();
            }
        }
        // 设置图片尺寸
        for (ContentPBO contentPBO : result) {
            if (contentPBO.getType() != ContentType.VIDEO.getType()) {
                contentPBO.setScale(imageScale);
            }
        }
    }

    @Override
    public boolean matchShowExhibit(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.processor.headerpic.EducationHeaderPicProcessor.matchShowExhibit(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        return false;
    }

    public void assembleVideoScale(DealGroupDTO dealGroupDTO, ContentPBO video) {
        if (Objects.isNull(dealGroupDTO)
                || Objects.isNull(dealGroupDTO.getImage())
                || CollectionUtils.isEmpty(dealGroupDTO.getImage().getExtendVideos())) {
            video.setScale(ImageScaleEnum.SIXTEEN_TO_NINE.getScale());
            return;
        }
        //目前只会有一个视频
        List<ExtendVideoDTO> videos = dealGroupDTO.getImage().getExtendVideos();
        //过滤掉一个默认生成的16:9的视频，取原视频（所有尺寸视频都生成一个16:9尺寸的视频）
        ExtendVideoDTO extendVideoDTO = videos.stream()
                .filter(e -> !e.getRatio().equals(ImageScaleEnum.SIXTEEN_TO_NINE.getScale()))
                .findFirst().orElse(null);

        if (Objects.isNull(extendVideoDTO) || !extendVideoDTO.getRatio().contains(COLON_SIGN)
                || extendVideoDTO.getRatio().split(COLON_SIGN).length != 2) {
            video.setScale(ImageScaleEnum.SIXTEEN_TO_NINE.getScale());
            return;
        }
        // 如果尺寸比值大于0则置为16:9，否则为3:4
        String[] ratioArray = extendVideoDTO.getRatio().split(COLON_SIGN);
        Integer compareResult = NumberUtils.toScaledBigDecimal(ratioArray[0]).divide(NumberUtils.toScaledBigDecimal(ratioArray[1]), RoundingMode.CEILING).compareTo(BigDecimal.valueOf(1));
        video.setScale(compareResult > 0 ? ImageScaleEnum.SIXTEEN_TO_NINE.getScale() : ImageScaleEnum.THREE_TO_FOUR.getScale());
        video.setVideoUrl(extendVideoDTO.getPath());
        video.setContent(extendVideoDTO.getCoverPath());
    }

}
