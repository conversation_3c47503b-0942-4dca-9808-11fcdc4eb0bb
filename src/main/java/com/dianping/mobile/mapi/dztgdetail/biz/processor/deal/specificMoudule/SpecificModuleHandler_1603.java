package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.BaseDisplayItemVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailDisplayUnitVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailSpecificModuleVO;
import com.dianping.mobile.mapi.dztgdetail.entity.EyeServiceProcessNode;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import joptsimple.internal.Strings;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
public class SpecificModuleHandler_1603 implements DealDetailSpecificModuleHandler {
    @Override
    public String identity() {
        return "1603";
    }

    @Override
    public void handle(SpecificModuleCtx ctx) {
        ctx.setResult(buildResult(ctx));
    }

    public DealDetailSpecificModuleVO buildResult(SpecificModuleCtx ctx) {
        List<ServiceProjectAttrDTO> serviceProjectAttributes = Optional.ofNullable(ctx.getDealGroupDTO())
                .map(DealGroupDTO::getServiceProject)
                .map(DealGroupServiceProjectDTO::getMustGroups)
                .flatMap(mustGroups -> mustGroups.stream()
                        .findFirst()
                        .map(MustServiceProjectGroupDTO::getGroups)
                        .flatMap(groups -> groups.stream().findFirst()))
                .map(ServiceProjectDTO::getAttrs)
                .orElse(Collections.emptyList());

        DealDetailSpecificModuleVO result = new DealDetailSpecificModuleVO();
        List<DealDetailDisplayUnitVO> units = new ArrayList<>();
        Optional.ofNullable(buildServiceProcess(serviceProjectAttributes)).map(units::add);
        result.setUnits(units);
        return result;
    }

    private DealDetailDisplayUnitVO buildServiceProcess(List<ServiceProjectAttrDTO> serviceProjectAttributes) {
        String serviceProcessStr = getServiceProjectAttrValue(serviceProjectAttributes, "standardServiceProcess");
        List<EyeServiceProcessNode> serviceProcessNodes = new Gson().fromJson(serviceProcessStr, new TypeToken<List<EyeServiceProcessNode>>(){}.getType());
        List<BaseDisplayItemVO> items = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(serviceProcessNodes)) {
            for (EyeServiceProcessNode node : serviceProcessNodes) {
                if (node == null) {
                    continue;
                }
                BaseDisplayItemVO item = new BaseDisplayItemVO();
                item.setName(node.getProcessName());
                item.setDesc(node.getProcessDescription());
                items.add(item);
            }
        }
        if (CollectionUtils.isEmpty(items)) {
            return null;
        }
        DealDetailDisplayUnitVO serviceProcess = new DealDetailDisplayUnitVO();
        serviceProcess.setType("process");
        serviceProcess.setTitle("服务流程");
        serviceProcess.setDisplayItems(items);
        return serviceProcess;
    }

    private String getServiceProjectAttrValue(List<ServiceProjectAttrDTO> serviceProjectAttributes, String attrName) {
        return serviceProjectAttributes.stream()
                .filter(attr -> attrName.equals(attr.getAttrName()) && StringUtils.isNotBlank(attr.getAttrValue()))
                .findFirst()
                .map(ServiceProjectAttrDTO::getAttrValue)
                .orElse(Strings.EMPTY);
    }
}
