package com.dianping.mobile.mapi.dztgdetail.button.joy;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo.CouponDescItem;
import com.dianping.mobile.mapi.dztgdetail.helper.PriceHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.PromoHelper;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.google.common.collect.ImmutableMap;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public class XiYuMarketPriceButtonBuilder extends AbstractPriceServiceButtonBuilder{
    protected static final Map<Integer, String> BUTTON_NAME_MAP = ImmutableMap.of(
            BuyBtnTypeEnum.TIMES_CARD.getCode(), "购买1次",
            BuyBtnTypeEnum.PINTUAN.getCode(), "单独购买",
            BuyBtnTypeEnum.MEMBER_CARD.getCode(), "团购价",
            BuyBtnTypeEnum.JOY_CARD.getCode(), "团购价"
    );
    protected static final String HAD_COUPON_PREFIX = "用券";
    protected static final String NO_COUPON_PREFIX = "领券";
    protected static final String BUY = "抢购";

    @Override
    protected PriceDisplayDTO getPrice(DealCtx context) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.button.joy.XiYuMarketPriceButtonBuilder.getPrice(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        return PriceHelper.getDealPromoPrice(context);
    }

    @Override
    protected void afterBuild(DealCtx context, DealBuyBtn button) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.button.joy.XiYuMarketPriceButtonBuilder.afterBuild(DealCtx,DealBuyBtn)");
        buildCouponAB(context, button);
        buildBtnTitle(context, button);
        buildBtnDesc(context, button);
        buildBtnTag(context, button);
    }

    private void buildBtnTag(DealCtx context, DealBuyBtn button) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.button.joy.XiYuMarketPriceButtonBuilder.buildBtnTag(DealCtx,DealBuyBtn)");
        PriceDisplayDTO price = getPrice(context);
        if (price == null || price.getPromoAmount() == null) {
            return;
        }
        if (GreyUtils.isShowMarketPrice(context)){
            button.setBtnTag(StringUtils.isNotBlank(price.getShortPromoTag())? price.getShortPromoTag() : "共省"+price.getPromoAmount().toPlainString());
        }
    }

    private void buildBtnDesc(DealCtx context, DealBuyBtn button) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.button.joy.XiYuMarketPriceButtonBuilder.buildBtnDesc(DealCtx,DealBuyBtn)");
        button.setBtnDesc(buildMarketPriceDesc(context));
    }

    private void buildBtnTitle(DealCtx context, DealBuyBtn button) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.button.joy.XiYuMarketPriceButtonBuilder.buildBtnTitle(DealCtx,DealBuyBtn)");
        if (Objects.equals(context.getEnvCtx().getDztgClientTypeEnum(), DztgClientTypeEnum.DIANPING_BAIDUMAP_MINIAPP)) {
            List<PromoDTO> couponPromos = PromoHelper.getCouponPromos(context);
            if (CollectionUtils.isNotEmpty(couponPromos)) {
                //百度地图小程序抢购按钮逻辑固定为「XX抢购」，任何一张券可用就展示用券抢购
                button.setBtnTitle((couponPromos.stream().anyMatch(item -> !item.isCanAssign()) ?  HAD_COUPON_PREFIX : NO_COUPON_PREFIX) + BUY);
                return;
            }
        }

        if (context.getPreButton() == null) {
            return;
        }
        String title = BUTTON_NAME_MAP.get(context.getPreButton().getDetailBuyType());
        if (StringUtils.isNotBlank(title)) {
            button.setBtnTitle(title);
        }
    }

    protected void buildCouponAB(DealCtx context, DealBuyBtn buyBtn) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.button.joy.XiYuMarketPriceButtonBuilder.buildCouponAB(DealCtx,DealBuyBtn)");
        List<PromoDTO> couponPromos = PromoHelper.getCouponUsePromo(context);
        if (CollectionUtils.isEmpty(couponPromos)) {
            return;
        }
        for (PromoDTO couponPromo : couponPromos){
            if (couponPromo.getIdentity().getPromoType() != PromoTypeEnum.COUPON.getType() || !PromoHelper.canAssign(couponPromo)) {
                continue;
            }
            if(PromoHelper.isCouponPurchase(couponPromo)){
                continue;
            }
            CouponDescItem coupon = new CouponDescItem();
            coupon.setCouponGroupId((int) couponPromo.getIdentity().getPromoId());
            coupon.setUnifiedcoupongroupids(String.valueOf(couponPromo.getIdentity().getPromoId()));
            buyBtn.setCoupon(coupon);
        }
    }
}
