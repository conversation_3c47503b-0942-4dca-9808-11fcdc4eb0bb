package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.sku;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

import java.io.Serializable;

@MobileDo(id = 0x5003)
public class SkuSalesAttrValueDO implements Serializable {
    /**
     * 属性值补充字段，1-desc与code描述一致 2-desc为code对应的url
     */
    @MobileDo.MobileField(key = 0xfebf)
    private String desc;

    /**
     * 属性值标识
     */
    @MobileDo.MobileField(key = 0xadc3)
    private String code;

    @FieldDoc(description = "属性的id值")
    @MobileDo.MobileField(key = 0x86f3)
    private String attrId;

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getAttrId() {
        return attrId;
    }

    public void setAttrId(String attrId) {
        this.attrId = attrId;
    }
}
