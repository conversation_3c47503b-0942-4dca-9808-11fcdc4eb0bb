package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.sku;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

@MobileDo(id = 0x22d2)
public class SkuBasicInfoDO implements Serializable {
    /**
     * sku头图
     */
    @MobileDo.MobileField(key = 0xad8c)
    private String skuHeadPic;

    /**
     * 到综商品/sku属性
     */
    @MobileDo.MobileField(key = 0xbf2e)
    private List<DzAttrDo> attrList;

    /**
     * sku库存
     */
    @MobileDo.MobileField(key = 0xf19c)
    private int stock;

    /**
     *
     */
    @MobileDo.MobileField(key = 0xf59e)
    private String skuId;

    public String getSkuHeadPic() {
        return skuHeadPic;
    }

    public void setSkuHeadPic(String skuHeadPic) {
        this.skuHeadPic = skuHeadPic;
    }

    public List<DzAttrDo> getAttrList() {
        return attrList;
    }

    public void setAttrList(List<DzAttrDo> attrList) {
        this.attrList = attrList;
    }

    public int getStock() {
        return stock;
    }

    public void setStock(int stock) {
        this.stock = stock;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }
}
