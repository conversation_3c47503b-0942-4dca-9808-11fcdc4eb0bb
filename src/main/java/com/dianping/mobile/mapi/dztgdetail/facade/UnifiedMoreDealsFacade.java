package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.cat.Cat;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.deal.idmapper.api.DealIdMapperService;
import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.dianping.deal.publishcategory.DealGroupPublishCategoryQueryService;
import com.dianping.deal.publishcategory.dto.ChannelDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.deal.publishcategory.enums.ChannelGroupEnum;
import com.dianping.deal.shop.dto.DealGroupDTO;
import com.dianping.deal.shop.dto.ShopOnlineDealGroup;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MoreDealsWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PriceDisplayWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PromoWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.AppImageSize;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.MoreDealsCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedMoreDealsReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.more.MoreItemDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.more.UnifiedMoreItemList;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.more.UnifiedMoreList;
import com.dianping.mobile.mapi.dztgdetail.entity.DealRecModuleOfflineList;
import com.dianping.mobile.mapi.dztgdetail.helper.ShopDealsHelper;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.ImageUtils;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.dealuser.price.display.api.model.BatchPriceRequest;
import com.sankuai.dealuser.price.display.api.model.ClientEnv;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PriceResponse;
import com.sankuai.dealuser.price.display.api.model.ProductIdentity;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupCategoryBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupChannelBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.dianping.mobile.mapi.dztgdetail.util.SalesConfusionUtil.getSectionSales;

/**
 * Created by zuomlin on 2018/12/13.
 */
@Component
@Slf4j
public class UnifiedMoreDealsFacade {

    @Resource
    private MoreDealsWrapper moreDealsWrapper;

    @Resource
    private PromoWrapper promoWrapper;

    @Autowired
    DealGroupWrapper dealGroupWrapper;

    @Autowired
    QueryCenterWrapper queryCenterWrapper;

    @Resource
    DealGroupPublishCategoryQueryService dealGroupPublishCategoryQueryService;

    @Resource
    private DealIdMapperService dealIdMapperService;
    @Resource
    private PriceDisplayWrapper priceDisplayWrapper;

    public UnifiedMoreList queryUnifiedMoreList(UnifiedMoreDealsReq request, EnvCtx envCtx, IMobileContext iMobileContext) throws Exception {
        // 部分行业下线该模块,返回空数据
        if (dealRecModuleOffline(request, envCtx)) {
            return null;
        }
        MoreDealsCtx moreDealsCtx = initMoreDealsCtx(request, envCtx, iMobileContext);
        UnifiedMoreList unifiedMoreList = new UnifiedMoreList();
        List<Long> shopIds = moreDealsWrapper.queryDealShopIds(moreDealsCtx);//获取适用商户；
        if (CollectionUtils.isEmpty(shopIds)) {
            return unifiedMoreList;
        }
        if (request.getShopIdLong() > 0L && !shopIds.contains(request.getShopIdLong())) {
            shopIds.add(0, request.getShopIdLong());
        } else if (request.getShopIdLong() <= 0L) {
            request.setShopIdStr(String.valueOf(shopIds.get(0)));
        }
        Future shopOnlineDealGroupFuture = moreDealsWrapper.preShopOnlineDealGroupsV2(shopIds, moreDealsCtx.getDpCityId());
        Map<Long, ShopOnlineDealGroup> shopOnlineDealGroupMap = moreDealsWrapper.getFutureResult(shopOnlineDealGroupFuture);
        if (MapUtils.isEmpty(shopOnlineDealGroupMap)) {
            return unifiedMoreList;
        }
        Map<Integer, DealGroupDTO> allShopOnlineDealGroup = ShopDealsHelper.getOtherShopOnlineDealGroup(shopOnlineDealGroupMap, 0);
        if (MapUtils.isEmpty(allShopOnlineDealGroup)) {
            return unifiedMoreList;
        }
        List<Integer> allDealGroupIds = Lists.newArrayList(allShopOnlineDealGroup.keySet());
        Future promoFuture = promoWrapper.batchPromoDisplayDTOFuture(moreDealsCtx, allShopOnlineDealGroup, allDealGroupIds);
        Future<PriceResponse<Map<Long, List<PriceDisplayDTO>>>> priceFuture
                = priceDisplayWrapper.prepareByRequest(buildRequest(moreDealsCtx,shopOnlineDealGroupMap));
        Map<Integer, PromoDisplayDTO> promoMap = promoWrapper.getPromoDisplayDTOMap(promoFuture);
        // 解析报价结果
        Map<Long, Map<Integer, PriceDisplayDTO>> dpShopId2PriceDisplayMap = priceDisplayWrapper.getProductMap0(priceFuture);

        Map<Integer, DealGroupDTO> sameShopOnlineDealGroup = ShopDealsHelper.getSameShopOnlineDealGroup(shopOnlineDealGroupMap, moreDealsCtx.getDpShopIdLong());
        // 其他门店团单信息
        Map<Long, Map<Integer, DealGroupDTO>> otherDpShopId2DealGroupMap = ShopDealsHelper.getOtherShopOnlineDealGroup0(
                shopOnlineDealGroupMap, moreDealsCtx.getDpShopIdLong());
        ShopDealsHelper.removeDuplicateDealGroupDTO0(sameShopOnlineDealGroup, otherDpShopId2DealGroupMap);
        // 本门店下的团单信息
        Map<Integer, PriceDisplayDTO> dealGroupId2PriceDisplayMap = dpShopId2PriceDisplayMap.get(moreDealsCtx.getDpShopIdLong());
        List<MoreItemDTO> sameMoreItemList = buildDpMoreItemDTO(sameShopOnlineDealGroup, promoMap,
                moreDealsCtx, dealGroupId2PriceDisplayMap);
        // 其他门店下的团单信息
        List<MoreItemDTO> otherMoreItemList = buildDpMoreOtherItemDTO(otherDpShopId2DealGroupMap, promoMap,
                moreDealsCtx, dpShopId2PriceDisplayMap);

        if (CollectionUtils.isNotEmpty(sameMoreItemList)) {
            UnifiedMoreItemList unifiedMoreItemList = new UnifiedMoreItemList();
            unifiedMoreItemList.setTitle("本店可用团购");
            unifiedMoreItemList.setSubTitle(String.valueOf(sameMoreItemList.size()));
            unifiedMoreItemList.setItemList(sameMoreItemList);
            unifiedMoreList.setSameMoreItemList(unifiedMoreItemList);
        }
        if (CollectionUtils.isNotEmpty(otherMoreItemList)) {
            UnifiedMoreItemList unifiedMoreItemList = new UnifiedMoreItemList();
            unifiedMoreItemList.setTitle("其他相关团购");
            unifiedMoreItemList.setSubTitle(String.valueOf(otherMoreItemList.size()));
            unifiedMoreItemList.setItemList(otherMoreItemList);
            unifiedMoreList.setOtherMoreItemList(unifiedMoreItemList);
        }
        if (CollectionUtils.isNotEmpty(sameMoreItemList) || CollectionUtils.isNotEmpty(otherMoreItemList)) {
            unifiedMoreList.setTitle("其他推荐团购");
            unifiedMoreList.setBranchShopName("");
        }

        return unifiedMoreList;
    }

    private BatchPriceRequest buildRequest(MoreDealsCtx moreDealsCtx,  Map<Long, ShopOnlineDealGroup> products) {
        BatchPriceRequest request  = new BatchPriceRequest();
        ClientEnv env = new ClientEnv();
        env.setCityId(/*moreDealsCtx.isMt() ? moreDealsCtx.getMtCityId() :*/ moreDealsCtx.getDpCityId());
        env.setClientType(/*moreDealsCtx.isMt() ? ClientTypeEnum.mt_mainApp_ios.getType() : */ClientTypeEnum.dp_mainApp_ios.getType());
        request.setClientEnv(env);
        request.setUserId(/*moreDealsCtx.isMt() ? moreDealsCtx.getEnvCtx().getMtUserId() : */moreDealsCtx.getEnvCtx().getDpUserId());
        request.setScene(400000);
        Map<Long,List<ProductIdentity>> longShopId2ProductIds = new HashMap<>();
        products.forEach((k,v) -> longShopId2ProductIds.put(k ,transform(v)));
        request.setLongShopId2ProductIds(longShopId2ProductIds);
        return request;
    }

    private List<ProductIdentity> transform(ShopOnlineDealGroup buildProducts) {
        return buildProducts.getDealGroups().stream().map(product -> new ProductIdentity(product.getDealGroupId(),1)
        ).limit(20).collect(Collectors.toList());
    }

    private List<MoreItemDTO> buildDpMoreItemDTO(Map<Integer, DealGroupDTO> shopOnlineDealGroup, Map<Integer, PromoDisplayDTO> promoMap, MoreDealsCtx ctx, Map<Integer, PriceDisplayDTO> priceDisplayMap) {

        int dealGroupId = ctx.getDpId();
        boolean isDz = isDaoDianZongHe(ctx.getChannel());

        if (MapUtils.isEmpty(shopOnlineDealGroup)) {
            return Collections.emptyList();
        }

        List<MoreItemDTO> moreItemDTOList = Lists.newArrayList();

        for (Map.Entry<Integer, DealGroupDTO> entry : shopOnlineDealGroup.entrySet()) {
            Integer key = entry.getKey();

            if (key != dealGroupId && entry.getValue() != null) {
                DealGroupDTO dealGroupDTO = entry.getValue();
                MoreItemDTO moreItemDTO = new MoreItemDTO();
                moreItemDTO.setItemId(key);
                moreItemDTO.setShortTitle(dealGroupDTO.getTitle());
                moreItemDTO.setTitle(dealGroupDTO.getProductTitle());
                moreItemDTO.setProductTitle(dealGroupDTO.getProductTitle());
                moreItemDTO.setCurrentPrice(dealGroupDTO.getPrice().doubleValue());
                moreItemDTO.setMarketPrice(dealGroupDTO.getMarketPrice().doubleValue());
                moreItemDTO.setBigPic(ImageUtils.format(dealGroupDTO.getImageUrl(), AppImageSize.ORIGINAL.width, AppImageSize.ORIGINAL.height));
                moreItemDTO.setDefaultPic(ImageUtils.format(dealGroupDTO.getImageUrl(), AppImageSize.MEDIUM.width, AppImageSize.MEDIUM.height));

                if(dealGroupDTO.getDealGroupSalesDTO() != null){
                    String salesTag = dealGroupDTO.getDealGroupSalesDTO().getSalesTag();
                    if(salesTag != null && !salesTag.contains("+")){
                        int sales = dealGroupDTO.getDealGroupSalesDTO().getSales();
                        String sectionSales = getSectionSales(sales);
                        if (StringUtils.isEmpty(sectionSales)) {
                            moreItemDTO.setSalesDesc(null);
                        } else {
                            moreItemDTO.setSalesDesc(salesTag.replace(String.valueOf(sales), sectionSales));
                        }
                    }else{
                        moreItemDTO.setSalesDesc(salesTag);
                    }

                }else{
                    moreItemDTO.setSalesDesc("已售0");
                }

                if(MapUtils.isNotEmpty(priceDisplayMap) && priceDisplayMap.containsKey(key)){
                    PriceDisplayDTO priceDisplayDTO = priceDisplayMap.get(key);
                    moreItemDTO.setItemCampaignTag(priceDisplayDTO.getPromoTag());
                    moreItemDTO.setCurrentPrice(priceDisplayDTO.getPrice().doubleValue());
                } else if (MapUtils.isNotEmpty(promoMap) && promoMap.get(key) != null) {
                    PromoDisplayDTO dto = promoMap.get(key);
                    moreItemDTO.setItemCampaignTag(dto.getTag());

                    BigDecimal amt = dto.getPromoAmount();

                    if (amt != null && amt.compareTo(BigDecimal.ZERO) > 0 && dto.isPriceLineThrough() && isDz) {
                        double currentPrice = moreItemDTO.getCurrentPrice();
                        moreItemDTO.setCurrentPrice(currentPrice > amt.doubleValue() ? subtract(currentPrice, amt.doubleValue()) : currentPrice);
                    }

                }

                moreItemDTO.setDetailUrl(ShopDealsHelper.getDpAppDealDetailUrl(key));
                moreItemDTOList.add(moreItemDTO);
            }
        }

        return moreItemDTOList;
    }

    private List<MoreItemDTO> buildDpMoreOtherItemDTO(Map<Long, Map<Integer, DealGroupDTO>> otherDpShopId2DealGroupMap,
                                                      Map<Integer, PromoDisplayDTO> promoMap, MoreDealsCtx ctx,
                                                      Map<Long, Map<Integer, PriceDisplayDTO>> dpShopId2PriceDisplayMap) {
        if (MapUtils.isEmpty(otherDpShopId2DealGroupMap) || MapUtils.isEmpty(dpShopId2PriceDisplayMap)) {
            return Collections.emptyList();
        }
        List<MoreItemDTO> moreItemDTOList = Lists.newArrayList();
        for (Map.Entry<Long, Map<Integer, DealGroupDTO>> entry : otherDpShopId2DealGroupMap.entrySet()) {
            Long dpShopId = entry.getKey();
            Map<Integer, PriceDisplayDTO> dealGroupId2PriceDisplayMap = dpShopId2PriceDisplayMap.get(dpShopId);
            List<MoreItemDTO> moreItemDTOS = buildDpMoreItemDTO(entry.getValue(), promoMap, ctx, dealGroupId2PriceDisplayMap);
            moreItemDTOList.addAll(moreItemDTOS);
        }
        return moreItemDTOList;
    }

    private double subtract(double a, double b) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.facade.UnifiedMoreDealsFacade.subtract(double,double)");
        return BigDecimal.valueOf(a).subtract(BigDecimal.valueOf(b)).doubleValue();
    }

    /**
     * 是否到综团单
     * @param channel
     * @return
     */
    private boolean isDaoDianZongHe(DealGroupChannelDTO channel) {
        if (channel == null || channel.getChannelDTO() == null) {
            return false;
        }

        ChannelDTO dto = channel.getChannelDTO();

        return ChannelGroupEnum.GENERAL_TYPE.getChannelGroupId() == dto.getChannelGroupId();

    }

    private MoreDealsCtx initMoreDealsCtx(UnifiedMoreDealsReq request, EnvCtx envCtx, IMobileContext iMobileContext) {
        if(GreyUtils.isQueryCenterGreyBatch3(request.getDealGroupId())) {
            try {
                return initMoreDealsCtxByQueryCenter(request, envCtx, iMobileContext);
            } catch (Exception e) {
                log.error("initMoreDealsCtxByQueryCenter error,", e);
            }
        }
        MoreDealsCtx moreDealsCtx = new MoreDealsCtx(envCtx);
        int dpDealGroupID;

        if (envCtx.isMt()) {
            moreDealsCtx.setMtId(request.getDealGroupId());
            moreDealsCtx.setMtShopIdLong(request.getShopIdLong());
            moreDealsCtx.setMtCityId(request.getCityId());
            IdMapper mapper = dealIdMapperService.queryByMtDealGroupId(request.getDealGroupId());
            dpDealGroupID = mapper.getDpDealGroupID();
        } else {
            moreDealsCtx.setDpCityId(request.getCityId());
            moreDealsCtx.setDpId(request.getDealGroupId());
            moreDealsCtx.setDpShopIdLong(request.getShopIdLong());
            dpDealGroupID = request.getDealGroupId();
        }

        DealGroupChannelDTO channel = dealGroupPublishCategoryQueryService.getDealGroupChannelById(dpDealGroupID);

        moreDealsCtx.setLat(request.getLat());
        moreDealsCtx.setLng(request.getLng());
        moreDealsCtx.setContext(iMobileContext);
        moreDealsCtx.setToken(iMobileContext.getHeader().getNewToken());
        moreDealsCtx.setChannel(channel);

        return moreDealsCtx;
    }

    private MoreDealsCtx initMoreDealsCtxByQueryCenter(UnifiedMoreDealsReq request, EnvCtx envCtx, IMobileContext iMobileContext) throws Exception {
        MoreDealsCtx moreDealsCtx = new MoreDealsCtx(envCtx);
        int dealGroupID = request.getDealGroupId() == null ? 0 : request.getDealGroupId();

        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet((long) dealGroupID), envCtx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP)
                .channel(DealGroupChannelBuilder.builder().all())
                .category(DealGroupCategoryBuilder.builder().categoryId())
                .build();
        com.sankuai.general.product.query.center.client.dto.DealGroupDTO dealGroupDTO = queryCenterWrapper.getDealGroupDTO(queryByDealGroupIdRequest);

        if (envCtx.isMt()) {
            moreDealsCtx.setMtId(Optional.ofNullable(request.getDealGroupId()).orElse(0));
            moreDealsCtx.setMtShopIdLong(request.getShopIdLong());
            moreDealsCtx.setMtCityId(Optional.ofNullable(request.getCityId()).orElse(0));
        } else {
            moreDealsCtx.setDpCityId(Optional.ofNullable(request.getCityId()).orElse(0));
            moreDealsCtx.setDpId(Optional.ofNullable(request.getDealGroupId()).orElse(0));
            moreDealsCtx.setDpShopIdLong(request.getShopIdLong());
        }

        DealGroupChannelDTO channel = getChannelDtoFromQueryCenter(dealGroupDTO);

        moreDealsCtx.setLat(request.getLat());
        moreDealsCtx.setLng(request.getLng());
        moreDealsCtx.setContext(iMobileContext);
        moreDealsCtx.setToken(iMobileContext.getHeader().getNewToken());
        moreDealsCtx.setChannel(channel);

        return moreDealsCtx;
    }

    private DealGroupChannelDTO getChannelDtoFromQueryCenter(com.sankuai.general.product.query.center.client.dto.DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO == null || dealGroupDTO.getChannel() == null) {
            return null;
        }
        com.sankuai.general.product.query.center.client.dto.DealGroupChannelDTO dealGroupChannelDTO = dealGroupDTO.getChannel();

        DealGroupChannelDTO oldDto = new DealGroupChannelDTO();
        ChannelDTO channelDTO = new ChannelDTO();
        channelDTO.setChannelId(dealGroupChannelDTO.getChannelId());
        channelDTO.setChannelEn(dealGroupChannelDTO.getChannelEn());
        channelDTO.setChannelCn(dealGroupChannelDTO.getChannelCn());
        channelDTO.setChannelGroupId(dealGroupChannelDTO.getChannelGroupId());
        channelDTO.setChannelGroupEn(dealGroupChannelDTO.getChannelGroupEn());
        channelDTO.setChannelGroupCn(dealGroupChannelDTO.getChannelGroupCn());

        oldDto.setChannelDTO(channelDTO);
        if(dealGroupDTO.getCategory() != null && dealGroupDTO.getCategory().getCategoryId() != null) {
            oldDto.setCategoryId(Math.toIntExact(dealGroupDTO.getCategory().getCategoryId()));
        }
        oldDto.setDealGroupId(dealGroupDTO.getDpDealGroupIdInt());

        return oldDto;
    }

    private boolean dealRecModuleOffline(UnifiedMoreDealsReq request, EnvCtx envCtx) throws Exception{
        if (request == null || request.getDealGroupId() == null || request.getDealGroupId() <= 0) {
            return false;
        }
        if (Lion.getBoolean(Environment.getAppName(), "com.sankuai.dzu.tpbase.dztgdetailweb.dealRecModuleSwitch", false)) {
            QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.builder()
                    .dealGroupIds(Sets.newHashSet((long) request.getDealGroupId()), envCtx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP)
                    .category(DealGroupCategoryBuilder.builder().categoryId())
                    .build();
            com.sankuai.general.product.query.center.client.dto.DealGroupDTO dealGroupDTO = queryCenterWrapper.getDealGroupDTO(queryByDealGroupIdRequest);
            if (dealGroupDTO == null || dealGroupDTO.getCategory() == null || dealGroupDTO.getCategory().getCategoryId() == null) {
                return false;
            }
            Long categoryId = dealGroupDTO.getCategory().getCategoryId();
            DealRecModuleOfflineList offlineList = Lion.getBean(Environment.getAppName(), "com.sankuai.dzu.tpbase.dztgdetailweb.deal.recModule.offline.category.list", DealRecModuleOfflineList.class, null);
            return offlineList != null && CollectionUtils.isNotEmpty(offlineList.getDealCategoryList()) && offlineList.getDealCategoryList().contains(categoryId);
        }
        return false;
    }
}
