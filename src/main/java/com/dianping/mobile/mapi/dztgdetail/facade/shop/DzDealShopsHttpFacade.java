package com.dianping.mobile.mapi.dztgdetail.facade.shop;


import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ShopTagTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DzDealShopsRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.shop.*;
import com.dianping.mobile.mapi.dztgdetail.util.JsonUtils;
import com.dianping.mobile.mapi.dztgdetail.util.MagicFlagUtils;
import com.dianping.pay.order.service.query.dto.UnifiedOrderWithId;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.zebra.util.StringUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dztheme.shop.enums.ClientOS;
import com.sankuai.dztheme.shop.enums.ClientType;
import com.sankuai.dztheme.shop.enums.ThemeCoordType;
import com.sankuai.dztheme.shop.service.DzThemeShopService;
import com.sankuai.dztheme.shop.vo.*;
import com.sankuai.dztheme.shop.vo.v1.ShopThemeResponse;
import com.sankuai.fbi.lifeevent.reserverpcapi.dto.CheckIsSelfBrandShopRespDTO;
import com.sankuai.fbi.lifeevent.reserverpcapi.request.CheckIsSelfBrandShopRequest;
import com.sankuai.fbi.lifeevent.reserverpcapi.service.ReserveConfigQueryService;
import com.sankuai.nib.mkt.common.base.enums.DepartmentTypeEnum;
import com.sankuai.nib.mkt.common.base.enums.PageSourceEnum;
import com.sankuai.nib.mkt.common.base.enums.RealGpsCoordTypeEnum;
import com.sankuai.nib.mkt.common.base.enums.RulePropertyTypeEnum;
import com.sankuai.nib.mkt.common.base.enums.daozong.QueryMMCTypeEnum;
import com.sankuai.nib.mkt.common.base.enums.propertyValueType.ClientTypeEnum;
import com.sankuai.nib.mkt.common.base.enums.propertyValueType.MeiDianSourceEnum;
import com.sankuai.nib.mkt.common.base.model.Property;
import com.sankuai.nib.mkt.promotion.tag.server.api.model.enums.ApplyResultEnum;
import com.sankuai.nib.mkt.promotion.tag.server.api.model.enums.PromotionTagQueryStrategyEnum;
import com.sankuai.nib.mkt.promotion.tag.server.api.model.request.PromotionTagApplyInfoRequest;
import com.sankuai.nib.mkt.promotion.tag.server.api.model.request.PromotionTagRequestUnit;
import com.sankuai.nib.mkt.promotion.tag.server.api.model.response.PromotionTagApplyInfoResponse;
import com.sankuai.nib.mkt.promotion.tag.server.api.model.response.PromotionTagResponseUnit;
import com.sankuai.nib.mkt.promotion.tag.server.api.service.IMagicalMemberTagService;
import com.sankuai.nibmkt.promotion.api.query.model.UserInfoDTO;
import com.sankuai.spt.statequery.api.dto.BaseStateDTO;
import com.sankuai.spt.statequery.api.dto.BaseStateQueryItemDTO;
import com.sankuai.spt.statequery.api.dto.ShopBookInfoDTO;
import com.sankuai.spt.statequery.api.dto.TimeSliceDTO;
import com.sankuai.spt.statequery.api.enums.IdTypeEnum;
import com.sankuai.spt.statequery.api.enums.SubjectTypeEnum;
import com.sankuai.spt.statequery.api.request.QueryBaseStateRequest;
import com.sankuai.spt.statequery.api.request.QueryShopBookInfoRequest;
import com.sankuai.spt.statequery.api.response.QueryBaseStateResponse;
import com.sankuai.spt.statequery.api.response.QueryShopBookInfoResponse;
import com.sankuai.spt.statequery.api.service.BaseStateQueryService;
import com.sankuai.spt.statequery.api.service.ShopBookInfoQueryService;
import com.sankuai.zdc.tag.apply.enums.SourceEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * @Author: zhangyuan103
 * @Date: 2025/6/3
 */
@Slf4j
@Component
public class DzDealShopsHttpFacade extends AbsDzDealShopsFacade<DzDealShopsRequest, DealGroupShopVO>{
    @Resource
    private DzThemeShopService dzThemeShopService;
    @Resource
    private IMagicalMemberTagService iMagicalPromotionMemberTagService;
    @Resource
    private ShopBookInfoQueryService shopBookInfoQueryService;
    @Resource
    private BaseStateQueryService baseStateQueryService;
    @Resource(name = "reserveConfigQueryService")
    private ReserveConfigQueryService reserveConfigQueryService;

    private static final String SOURCE_MAPPING_LION_KEY = "deal.source.2.mkt.source.mapping";
    private static final long ROW_ID = 1L;
    private static final int NOT_APPLY = 0;
    private static final long MAGICAL_TAG_ID = 20527;
    private static final long YOU_LE_XIAN_TAG_ID = 21437;
    private static final int TAG_MARGIN = 5;
    private static final int ROUND_CONNER_RADIUS_SIZE = 4;
    private static final String MAGICAL_ATTR_KEY = "shopMagicalInfoDTO";
    private static final String BUSINESS_HOUR_ATTR_KEY = "shopBusinessInfoDTO";
    private static final Integer CLEAN_SELF_OPERATION_CATEGORY_ID = 409;

    private static final String OFFLINE_CHANNEL = "newyouhuima";

    private static final String EARLIEST_BOOK_DESC_PREFIX = "最早可订";
    private static final String SEPARATOR = " ";
    private static final String TODAY = "";
    private static final String TOMORROW = "明天";

    @Override
    public ShopListContext initShopListContext(DzDealShopsRequest req, EnvCtx envCtx) {
        ShopListContext shopListContext = new ShopListContext();
        shopListContext.setOrderId(req.getOrderId());
        shopListContext.setMt(envCtx.isMt());
        shopListContext.setNeedLog(needLog(req.getDealGroupId()));
        shopListContext.setGpsCityId(req.getGpsCityId());
        shopListContext.setTargetCityId(req.getTargetcityid());
        shopListContext.setEnvCtx(envCtx);
        shopListContext.setEntryPage(req.getEntryPage());
        shopListContext.setUserRelation(getUserRelation(envCtx));
        shopListContext.setSource(req.getSource());
        shopListContext.setDealGroupId(req.getDealGroupId());
        shopListContext.setCityId(req.getCityId());
        shopListContext.setMmcinflate(req.getMmcinflate());
        shopListContext.setMmcuse(req.getMmcuse());
        shopListContext.setMmcbuy(req.getMmcbuy());
        shopListContext.setMmcfree(req.getMmcfree());
        shopListContext.setShopId(req.getShopId());
        shopListContext.setPageNum(req.getPageNum());
        shopListContext.setPageSize(req.getPageSize());
        shopListContext.setLat(req.getLat());
        shopListContext.setLng(req.getLng());
        shopListContext.setTargetlat(req.getTargetlat());
        shopListContext.setTargetlng(req.getTargetlng());
        shopListContext.setWxVersion(req.getWxVersion());
        shopListContext.setOfflineCode(req.getOfflineCode());
        return shopListContext;
    }

    @Override
    public DealGroupShopVO fillShopInfo(ShopListContext shopListContext, CompletableFuture<Void> recallShopIdTasks) {
        // 4.1 查询商户主题
        CompletableFuture<ShopThemeResponse> themeResponseCF = recallShopIdTasks.thenCompose(v -> CompletableFuture.supplyAsync(() -> queryShopTheme(shopListContext), EXECUTOR_TRACE_WRAPPER));
        // 4.2 查询团购是否神会员（包含黑渠道）
        CompletableFuture<Boolean> magicalDealCF = CompletableFuture.supplyAsync(() -> isMagicalDeal(shopListContext), EXECUTOR_TRACE_WRAPPER);
        // 4.3 点评映射美团shopId
        CompletableFuture<Map<Long, List<Long>>> dp2MtShopCF = CompletableFuture.supplyAsync(() -> getDp2MtShopMapping(shopListContext), EXECUTOR_TRACE_WRAPPER);
        CompletableFuture<Map<Long, List<Long>>> mt2DpShopCF = CompletableFuture.supplyAsync(() -> getMt2DpShopMapping(shopListContext), EXECUTOR_TRACE_WRAPPER);
        // 4.4 查询团购的类目
        CompletableFuture<Integer> dealCategoryIdCF = CompletableFuture.supplyAsync(
                () -> dealGroupWrapper.getCategoryId(shopListContext.getDpDealGroupId().intValue()),
                EXECUTOR_TRACE_WRAPPER);
        CompletableFuture<Void> allShopTasks = CompletableFuture.allOf(themeResponseCF, magicalDealCF, dp2MtShopCF, mt2DpShopCF, dealCategoryIdCF);
        allShopTasks.thenRun(() -> {
            shopListContext.setDp2MtShopMap(dp2MtShopCF.join());
            shopListContext.setMt2DpShopMap(mt2DpShopCF.join());
            shopListContext.setCategoryId(dealCategoryIdCF.join());
        }).join();
        // 5 一品多态(先囤后订) 订详情场景下：查询商户是否支持预订、最早可订时间
        // 5.1 查询商户预订的状态(是否支持预订)
        CompletableFuture<Map<Long, ShopBookInfoDTO>> shopBookCF = allShopTasks
                .thenCompose(
                        v -> CompletableFuture.supplyAsync(() -> getShopBook(shopListContext), EXECUTOR_TRACE_WRAPPER))
                .exceptionally(e -> {
                    log.error("getShopBook error", e);
                    return Maps.newHashMap();
                });
        // 5.2 查询商户 7天内的可预订时间
        List<CompletableFuture<List<BaseStateDTO>>> baseStateFutures = shopListContext.getShopIds().stream()
                .filter(Objects::nonNull)
                .map(shopId -> CompletableFuture
                        .supplyAsync(() -> getShopBaseState(shopListContext, shopListContext.getMtShopId(shopId)),
                                EXECUTOR_TRACE_WRAPPER)
                        .exceptionally(e -> {
                            log.error("getShopBaseState error", e);
                            return null;
                        }))
                .collect(Collectors.toList());
        // 合并所有异步任务
        CompletableFuture<Void> allBookTasks = CompletableFuture.allOf(
                Stream.concat(Stream.of(shopBookCF), baseStateFutures.stream()).toArray(CompletableFuture[]::new));
        // 等待完成并设置结果
        allBookTasks.thenRun(() -> {
            shopListContext.setShopBookMap(shopBookCF.join());
            shopListContext.setShopBaseStateDTOs(
                    baseStateFutures.stream().map(CompletableFuture::join).filter(Objects::nonNull).collect(Collectors.toList()));
        }).join();
        // 6 查询门店是否为保洁自营门店
        CompletableFuture<Map<Long, Boolean>> dpShopId2IsSelfCF = allBookTasks
                .thenCompose(v -> CompletableFuture.supplyAsync(() -> getDpShopId2IsSelfMap(shopListContext), EXECUTOR_TRACE_WRAPPER))
                .exceptionally(e -> {
                    log.error("checkIsSelfBrandShop error, shopIds={}",
                            shopListContext.getShopIds(), e);
                    return new HashMap<>();
                });
        shopListContext.setDpShopId2IsSelfMap(dpShopId2IsSelfCF.join());

        // 7 封装结果
        return buildDealGroupShopVO(themeResponseCF.join(), magicalDealCF.join(), shopListContext);
    }

    private Map<Long, Boolean> getDpShopId2IsSelfMap(ShopListContext shopListContext) {
        if (!Objects.equals(shopListContext.getCategoryId(), CLEAN_SELF_OPERATION_CATEGORY_ID) ||
                CollectionUtils.isEmpty(shopListContext.getShopIds())) {
            return new HashMap<>();
        }
        CheckIsSelfBrandShopRequest checkIsSelfBrandShopRequest = new CheckIsSelfBrandShopRequest();
        List<Long> dpShopIds = shopListContext.getShopIds().stream().map(shopListContext::getDpShopId).collect(Collectors.toList());
        checkIsSelfBrandShopRequest.setDpShopIds(dpShopIds);
        CheckIsSelfBrandShopRespDTO res = reserveConfigQueryService.checkIsSelfBrandShop(checkIsSelfBrandShopRequest);
        if (res == null || MapUtils.isEmpty(res.getShopId2IsSelfBrandMap())) {
            return new HashMap<>();
        }
        return res.getShopId2IsSelfBrandMap();
    }


    private List<Long> getMtShopIds(ShopListContext shopListContext) {
        return shopListContext.isMt() ? shopListContext.getShopIds()
                : shopListContext.getShopIds().stream().map(shopListContext::getMtShopId).collect(Collectors.toList());
    }

    private Map<Long, ShopBookInfoDTO> getShopBook(ShopListContext shopListContext) {
        // 非一品多态(先囤后订)的订单详情页
        if (!RequestSourceEnum.fromPreOrderDetailPage(shopListContext.getSource())
                || CollectionUtils.isEmpty(shopListContext.getShopIds())) {
            return Maps.newHashMap();
        }
        QueryShopBookInfoResponse shopSupportBookInfoResponse = shopBookInfoQueryService
                .queryShopBookInfo(buildQueryShopBookInfoRequest(shopListContext));
        if (shopSupportBookInfoResponse == null || !shopSupportBookInfoResponse.isSuccess()
                || shopSupportBookInfoResponse.getData() == null
                || CollectionUtils.isEmpty(shopSupportBookInfoResponse.getData().getShopBookInfos())) {
            return Maps.newHashMap();
        }
        List<ShopBookInfoDTO> shopBookInfos = shopSupportBookInfoResponse.getData().getShopBookInfos();
        return shopBookInfos.stream().collect(Collectors.toMap(
                ShopBookInfoDTO::getMtShopId, Function.identity(), (k1, k2) -> k1));
    }

    private QueryShopBookInfoRequest buildQueryShopBookInfoRequest(ShopListContext shopListContext) {
        QueryShopBookInfoRequest request = new QueryShopBookInfoRequest();
        request.setMtShopIds(getMtShopIds(shopListContext));
        return request;
    }

    // 构造查询 一个商家7天内可订时间 的参数
    private QueryBaseStateRequest buildQueryBaseStateRequest(ShopListContext shopListContext, Long mtShopId) {
        QueryBaseStateRequest request = new QueryBaseStateRequest();
        request.setQuerySubjectType(SubjectTypeEnum.PRODUCT_SHOP);
        List<BaseStateQueryItemDTO> queryItems = IntStream.range(0, 7).mapToObj(plusDay -> {
            BaseStateQueryItemDTO baseStateQueryItemDTO = new BaseStateQueryItemDTO();
            baseStateQueryItemDTO.setSubjectId(new HashMap<IdTypeEnum, Long>() {
                {
                    put(IdTypeEnum.SHOP_ID, mtShopId);
                    put(IdTypeEnum.PRODUCT_ID, shopListContext.getMtDealGroupId());
                }
            });
            String day = LocalDate.now().plusDays(plusDay).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            baseStateQueryItemDTO.setDay(day);
            return baseStateQueryItemDTO;
        }).collect(Collectors.toList());
        request.setQueryItems(queryItems);
        return request;
    }

    // 获取商户营业状态，包含可预订的时间
    private List<BaseStateDTO> getShopBaseState(ShopListContext shopListContext, Long mtShopId) {
        if (!RequestSourceEnum.fromPreOrderDetailPage(shopListContext.getSource())) {
            // 非一品多态(先囤后订)的订单详情页
            return new ArrayList<>();
        }
        QueryBaseStateResponse queryBaseStateResponse = baseStateQueryService
                .queryBaseState(buildQueryBaseStateRequest(shopListContext, mtShopId));
        if (queryBaseStateResponse == null || !queryBaseStateResponse.isSuccess()
                || queryBaseStateResponse.getData() == null
                || CollectionUtils.isEmpty(queryBaseStateResponse.getData().getBaseStates())) {
            return new ArrayList<>();
        }
        return queryBaseStateResponse.getData().getBaseStates();
    }

    private ShopThemeResponse queryShopTheme(ShopListContext shopListContext) {
        if (CollectionUtils.isEmpty(shopListContext.getShopIds())) {
            return null;
        }
        ShopThemePlanRequest request = buildReq(shopListContext, getMtRealUserId(shopListContext.getUserRelation()));
        ShopThemeResponse response = dzThemeShopService.queryShopTheme(request);
        if (shopListContext.isNeedLog()) {
            log.info("DzDealShopsHttpFacade.queryShopTheme, request={}, response={}", JsonUtils.toJson(request), JsonUtils.toJson(response));
        }
        return response;
    }

    // 注意UnifiedOrderWithId，若用户点评下单有美团门店和点评门店，美团下单只有美团门店
    private boolean isOrderShop(UnifiedOrderWithId unifiedOrderWithId, Long currentShopId, Map<Long, List<Long>> dp2MtShopMap, ShopListContext shopListContext) {
        if (currentShopId == null || unifiedOrderWithId == null) {
            return false;
        }
        if (shopListContext.isMt()) {
            return currentShopId.equals(unifiedOrderWithId.getLongMtShopId());
        }
        if (currentShopId.equals(unifiedOrderWithId.getLongShopId())) {
            return true;
        }
        if (unifiedOrderWithId.getLongMtShopId() == null) {
            return false;
        }
        List<Long> currentMtShopIds = Optional.ofNullable(dp2MtShopMap).map(m -> m.get(currentShopId)).orElse(null);
        return CollectionUtils.isNotEmpty(currentMtShopIds) && unifiedOrderWithId.getLongMtShopId().equals(currentMtShopIds.get(0));
    }

    private Map<Long, List<Long>> getDp2MtShopMapping(ShopListContext shopListContext) {
        if (shopListContext.isMt()) {
            return Maps.newHashMap();
        }
        return mapperWrapper.getMtByDpShopIds(shopListContext.getShopIds());
    }

    private Map<Long, List<Long>> getMt2DpShopMapping(ShopListContext shopListContext) {
        if (!shopListContext.isMt()) {
            return Maps.newHashMap();
        }
        return mapperWrapper.queryDpByMtIdsL(shopListContext.getShopIds());
    }

    // 查询团购是否 支持神会员 & 不属于黑渠道
    private boolean isMagicalDeal(ShopListContext shopListContext) {
        // 订详不需要展示神会员标签
        if (StringUtils.isNotBlank(shopListContext.getOrderId())) {
            return false;
        }
        PromotionTagApplyInfoRequest infoRequest = buildApplyInfoRequest(shopListContext, getMtRealUserId(shopListContext.getUserRelation()), shopListContext.getMtDealGroupId());
        PromotionTagApplyInfoResponse promotionTagApplyInfoResponse = iMagicalPromotionMemberTagService.queryMagicalMemberTagApplyInfo(infoRequest);
        if (promotionTagApplyInfoResponse == null || MapUtils.isEmpty(promotionTagApplyInfoResponse.getUnitResultMap())) {
            return false;
        }
        PromotionTagResponseUnit responseUnit = promotionTagApplyInfoResponse.getUnitResultMap().get(ROW_ID);
        return responseUnit != null && responseUnit.getApplyResult() != ApplyResultEnum.notApply.getCode() && !promotionTagApplyInfoResponse.isBlackTrafficFlag() && promotionTagApplyInfoResponse.isPassMatrix();
    }

    private PromotionTagApplyInfoRequest buildApplyInfoRequest(ShopListContext shopListContext, Long mtUserId, Long mtDealGroupId) {
        EnvCtx envCtx = shopListContext.getEnvCtx();
        PromotionTagApplyInfoRequest request = new PromotionTagApplyInfoRequest();
        // 请求维度
        request.setQueryStrategy(PromotionTagQueryStrategyEnum.magicalMemberTagByProductId.getCode());
        // 业务线：到综
        request.setDepartmentType(DepartmentTypeEnum.DAOZONG.getCode());
        // 构建用户信息
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        // 美团实id
        userInfoDTO.setMtRealUserId(mtUserId);
        // 设备号，非必填
        userInfoDTO.setDeviceId(envCtx.getAppDeviceId());
        request.setUserInfoDTO(userInfoDTO);
        // 页面
        request.setPageSource(PageSourceEnum.dealDetailPage.getCode());
        Map<Long, PromotionTagRequestUnit> units = Maps.newHashMap();
        PromotionTagRequestUnit unit = new PromotionTagRequestUnit();
        // 请求项唯一id
        unit.setRowId(ROW_ID);
        unit.setProductId(mtDealGroupId);
        units.put(unit.getRowId(), unit);
        request.setUnits(units);
        Map<Integer, Property> propertyMap = Maps.newHashMap();
        // 版本号
        putProperty(propertyMap, RulePropertyTypeEnum.version, getVersion(shopListContext));
        // 美团来源
        putProperty(propertyMap, RulePropertyTypeEnum.MeiDianSource, envCtx.isMt() ? MeiDianSourceEnum.MT : MeiDianSourceEnum.DP);
        // 火星坐标系
        putProperty(propertyMap, RulePropertyTypeEnum.realGpsCoordType, RealGpsCoordTypeEnum.GCJ02.name());
        if (envCtx.isMt()) {
            putProperty(propertyMap, RulePropertyTypeEnum.gpsMtCityId, shopListContext.getGpsCityId());
        } else {
            putProperty(propertyMap, RulePropertyTypeEnum.gpsDpCityId, shopListContext.getGpsCityId());
        }
        // 客户端类型，按用户客户端类型传
        putProperty(propertyMap, RulePropertyTypeEnum.clientTp, envCtx.isMt() ? (envCtx.isAndroid() ? ClientTypeEnum.ANDROID.getValue() : ClientTypeEnum.IPHONE.getValue()) : (envCtx.isAndroid() ? ClientTypeEnum.DP_ANDROID.getValue() : ClientTypeEnum.DP_IPHONE.getValue()));
        // 请求实验
        putProperty(propertyMap, RulePropertyTypeEnum.requestMatrix, "true");
        // MKT页面来源
        String mktSource = getMktSource(shopListContext.getSource());
        if (StringUtils.isNotBlank(mktSource)) {
            putProperty(propertyMap, RulePropertyTypeEnum.trafficFlag, mktSource);
        }
        // 经度
        putProperty(propertyMap, RulePropertyTypeEnum.longitude, shopListContext.getLng());
        // 纬度
        putProperty(propertyMap, RulePropertyTypeEnum.latitude, shopListContext.getLat());
        request.setCommonProperties(propertyMap);
        // 神券可用标识传递
        if (MagicFlagUtils.isValid(shopListContext.getMmcuse())) {
            putProperty(propertyMap, RulePropertyTypeEnum.queryMMCType, MagicFlagUtils.canUse(shopListContext.getMmcuse()) ? QueryMMCTypeEnum.QUERY_AVAILABLE_MMC.getValue() : QueryMMCTypeEnum.NOT_QUERY_MMC.getValue());
        }
        return request;
    }

    private static <V> void putProperty(Map<Integer, Property> commonProperties, RulePropertyTypeEnum rulePropertyTypeEnum, V value) {
        if (value == null) {
            return;
        }
        int key = rulePropertyTypeEnum.getCode();
        String valueStr = String.valueOf(value);
        commonProperties.put(key, new Property(key, valueStr));
    }

    // 调用主题服务，主题服务并发查询商户基础信息和是否支持神会员
    private ShopThemePlanRequest buildReq(ShopListContext shopListContext, Long mtUserId) {
        List<Long> shopIds = shopListContext.getShopIds();
        EnvCtx envCtx = shopListContext.getEnvCtx();
        PageConfig pageConfig = shopListContext.getPageConfig();
        if (CollectionUtils.isEmpty(shopIds)) {
            return null;
        }
        boolean isMt = envCtx.isMt();
        String planId = isMt ? pageConfig.getMtPlanId() : pageConfig.getDpPlanId();
        ShopThemePlanRequest shopThemePlanRequest = new ShopThemePlanRequest();
        shopThemePlanRequest.setLongShopIds(shopIds);
        shopThemePlanRequest.setClientType(toClientType(envCtx));
        shopThemePlanRequest.setMiniProgram(envCtx.isMiniApp());
        shopThemePlanRequest.setNativeClient(envCtx.isNative());
        shopThemePlanRequest.setCityId(getFinalCityId(shopListContext.getCityId(), shopListContext.getTargetCityId()));
        shopThemePlanRequest.setUserId(isMt ? envCtx.getMtUserId() : envCtx.getDpUserId());
        shopThemePlanRequest.setDeviceId(envCtx.getAppDeviceId());
        shopThemePlanRequest.setUnionId(envCtx.getUnionId());
        shopThemePlanRequest.setPlanId(planId);
        shopThemePlanRequest.setClientVersion(getVersion(shopListContext));
        shopThemePlanRequest.setPlatform(isMt ? VCPlatformEnum.MT.getType() : VCPlatformEnum.DP.getType());
        shopThemePlanRequest.setClientOS(envCtx.isIos() ? ClientOS.IOS.getType() : ClientOS.ANDROID.getType());
        shopThemePlanRequest.setCoordType(ThemeCoordType.GCJ02.getType());
        shopThemePlanRequest.setUserLat(getFinalGpsParam(shopListContext.getLat(), shopListContext.getTargetlat()).doubleValue());
        shopThemePlanRequest.setUserLng(getFinalGpsParam(shopListContext.getLng(), shopListContext.getTargetlng()).doubleValue());

        // 业务参数
        Map<String, Object> extParams = Maps.newHashMap();
        extParams.put("localCityId", String.valueOf(getFinalCityId(shopListContext.getCityId(), shopListContext.getTargetCityId())));
        extParams.put("mtUserId", mtUserId);
        extParams.put("mktPageSource", PageSourceEnum.dealDetailPage.getCode());
        extParams.put("appVersion", getVersion(shopListContext));
        extParams.put("dealId", shopListContext.getDealGroupId());
        extParams.put("trafficFlag", getMktSource(shopListContext.getSource()));
        extParams.put("source", SourceEnum.LISTING.getCode());
        if (envCtx.isWxMini()) {
            extParams.put("openId", envCtx.getOpenId());
            extParams.put("wxAppId", envCtx.getMpAppId());
        }
        extParams.put("isHarmonyOS", envCtx.isHarmony());
        shopThemePlanRequest.setExtParams(extParams);

        return shopThemePlanRequest;
    }

    private int toClientType(EnvCtx envCtx) {
        if (envCtx.isNative()) {
            return ClientType.APP.getType();
        }
        if (envCtx.isWxMini()) {
            return ClientType.MINI_PROGRAM.getType();
        }
        if (envCtx.isFromH5()) {
            return ClientType.M_SITE.getType();
        }
        return ClientType.APP.getType();
    }

    // 获取离用户最近的商户
    private Long getNearestShopId(Map<Long, ShopCardDTO> shopCardDTOMap) {
        if (MapUtils.isEmpty(shopCardDTOMap)) {
            return null;
        }
        return shopCardDTOMap.entrySet().stream()
                .filter(entry -> entry.getValue() != null && entry.getValue().getDistanceValue() != null)
                .min(Comparator.comparing(entry -> entry.getValue().getDistanceValue())).map(Map.Entry::getKey)
                .orElse(null);
    }

    private void setBookButton(ShopListContext shopListContext, DealGroupShop dealGroupShop) {
        Map<Long, ShopBookInfoDTO> shopBookMap = shopListContext.getShopBookMap();
        if (MapUtils.isEmpty(shopBookMap)) {
            return;
        }
        ShopBookInfoDTO shopBookInfoDTO = shopBookMap
                .get(shopListContext.getMtShopId(dealGroupShop.getShopId()));
        PageConfig pageConfig = shopListContext.getPageConfig();
        if (shopBookInfoDTO != null && shopBookInfoDTO.getDealgroupSupportBook() && StringUtils.isNotBlank(dealGroupShop.getFirstOrderTime())) {
            dealGroupShop.setBookIcon(new BookIcon(BookIconEnum.PRE_ORDER_ONLINE.getValue(), pageConfig.getOnlineText()));
        } else {
            dealGroupShop.setBookIcon(new BookIcon(BookIconEnum.PRE_ORDER_PHONE.getValue(), pageConfig.getPhoneText()));
        }
    }

    private String getDayDescription(String day) {
        LocalDate date = LocalDate.parse(day);
        LocalDate today = LocalDate.now();
        return date.isEqual(today) ? TODAY : date.isEqual(today.plusDays(1)) ? TOMORROW : day.substring(5);
    }

    private String getEarliestBookDesc(List<BaseStateDTO> baseStateDTOs) {
        for (BaseStateDTO baseStateDTO : baseStateDTOs) {
            if (baseStateDTO == null || CollectionUtils.isEmpty(baseStateDTO.getTimeSlices())) {
                continue;
            }
            String earliestBookTime = baseStateDTO.getTimeSlices().stream().filter(TimeSliceDTO::isAvailable)
                    .map(TimeSliceDTO::getStartTime).findFirst().orElse(null);
            if (StringUtils.isNotBlank(earliestBookTime)) {
                return EARLIEST_BOOK_DESC_PREFIX + SEPARATOR + getDayDescription(baseStateDTO.getDay()) + SEPARATOR
                        + earliestBookTime;
            }
        }
        return "";
    }

    private void setFirstOrderTime(ShopListContext shopListContext, DealGroupShop dealGroupShop) {
        List<List<BaseStateDTO>> shopBaseStateDTOs = shopListContext.getShopBaseStateDTOs();
        if (CollectionUtils.isEmpty(shopBaseStateDTOs)) {
            return;
        }
        Long mtShopId = shopListContext.getMtShopId(dealGroupShop.getShopId());
        // 获取商户7天内的可预订时间
        List<BaseStateDTO> baseStateDTOs = shopBaseStateDTOs.stream()
                .filter(baseStateDTOList -> CollectionUtils.isNotEmpty(baseStateDTOList)
                        && baseStateDTOList.get(0) != null && baseStateDTOList.get(0).getSubject() != null
                        && MapUtils.isNotEmpty(baseStateDTOList.get(0).getSubject().getSubjectId())
                        && mtShopId.equals(baseStateDTOList.get(0).getSubject().getSubjectId().get(IdTypeEnum.SHOP_ID)))
                .findFirst().orElse(null);
        if (CollectionUtils.isEmpty(baseStateDTOs)) {
            return;
        }
        String earliestBookDesc = getEarliestBookDesc(baseStateDTOs);
        if (StringUtils.isBlank(earliestBookDesc)) {
            return;
        }
        dealGroupShop.setFirstOrderTime(earliestBookDesc);
    }

    // 如果是一品多态(先囤后订)商品团购订单详情页进入，则设置对应的预订信息。即设置 最早可订时间、预订button，离用户最近的标签
    private void setPreOrderInfo(DealGroupShop dealGroupShop, ShopListContext shopListContext, Long nearestShopId) {
        // 设置离用户最近
        if (nearestShopId != null && nearestShopId.equals(dealGroupShop.getShopId())) {
            dealGroupShop.setNearestShop(true);
        }
        // 设置最早可订时间
        setFirstOrderTime(shopListContext, dealGroupShop);
        // 设置预订的button
        setBookButton(shopListContext, dealGroupShop);
    }

    private DealGroupShopVO buildDealGroupShopVO(ShopThemeResponse shopThemeResponse, boolean isMagicalDeal, ShopListContext shopListContext) {
        if (CollectionUtils.isEmpty(shopListContext.getShopIds()) || shopThemeResponse == null || !shopThemeResponse.isSuccess() || MapUtils.isEmpty(shopThemeResponse.getShopCardDTOMap())) {
            return null;
        }
        UnifiedOrderWithId unifiedOrderWithId = shopListContext.getUnifiedOrderWithId();
        String versionId = getSnapshotVersion(unifiedOrderWithId);
        boolean showSimpleCard = showSimpleCard(shopListContext.getEntryPage());
        Map<Long, ShopCardDTO> shopCardDTOMap = shopThemeResponse.getShopCardDTOMap();

        // 离用户最近的门店id，并在该门店展示 离用户最近 的标签。目前仅一品多态(先囤后订)的订详页需要此能力
        Long nearestShopId = RequestSourceEnum.fromPreOrderDetailPage(shopListContext.getSource()) ?
                getNearestShopId(shopCardDTOMap) : null;

        List<DealGroupShop> DealGroupShops = shopListContext.getShopIds().stream().map(shopId -> {
            DealGroupShop dealGroupShop = new DealGroupShop();
            ShopCardDTO shopCardDTO = shopCardDTOMap.get(shopId);
            if (shopCardDTO == null) {
                return null;
            }
            ShopBusinessInfo shopBusinessInfo = getObjectFromAttr(shopCardDTO, BUSINESS_HOUR_ATTR_KEY, ShopBusinessInfo.class);

            dealGroupShop.setShopId(shopId);
            dealGroupShop.setMtShopId(shopListContext.getMtShopId(shopId));
            dealGroupShop.setDpShopId(shopListContext.getDpShopId(shopId));
            dealGroupShop.setShopName(shopCardDTO.getShopName());
            dealGroupShop.setAddress(shopCardDTO.getAddress());
            setPhoneNos(dealGroupShop, shopCardDTO.getContact(), shopListContext.getDpShopId2IsSelfMap(), shopListContext.getDpShopId(shopId));
            dealGroupShop.setDistance(shopCardDTO.getDistanceStr());

            if (!showSimpleCard) {
                dealGroupShop.setShopDetailUrl(getPoiUrl(shopCardDTO.getShopUrl(), shopListContext, shopId));
                dealGroupShop.setIcons(buildShopIcons(showMagicalTag(shopCardDTO, isMagicalDeal), shopListContext.getPageConfig()));
                dealGroupShop.getIcons().addAll(buildShopIcons(shopCardDTO));
                dealGroupShop.setShopPower(formatShopPower(shopCardDTO));
                dealGroupShop.setZdcTags(toZdcTags(shopCardDTO, isMagicalDeal));
            }

            // 2024.10月底新增参数
            dealGroupShop.setBusinessHour(shopBusinessInfo == null ? "" : shopBusinessInfo.getBusinessHour());
            dealGroupShop.setBusinessState(formatCurrentState(shopBusinessInfo));
            dealGroupShop.setMapUrl(getMapUrl(shopCardDTO, shopListContext));
            dealGroupShop.setOrderShop(
                    isOrderShop(unifiedOrderWithId, shopId, shopListContext.getDp2MtShopMap(), shopListContext));
            dealGroupShop.setDistanceAddress(formatShopAddress(shopCardDTO.getAddress(), shopCardDTO.getDistanceStr(), getFinalCityId(shopListContext.getGpsCityId(), shopListContext.getTargetCityId()), shopCardDTO.getCity()));
            dealGroupShop.setDistanceDesc(buildDistanceDesc(shopCardDTO.getDistanceStr(),
                    getFinalCityId(shopListContext.getGpsCityId(), shopListContext.getTargetCityId()),
                    shopCardDTO.getCity()));

            // 如果是从一品多态(先囤后订)的订详页进入的
            if (RequestSourceEnum.fromPreOrderDetailPage(shopListContext.getSource())) {
                setPreOrderInfo(dealGroupShop, shopListContext, nearestShopId);
            } else {
                dealGroupShop.setBookIcon(new BookIcon(BookIconEnum.PHONE.getValue()));
            }

            return dealGroupShop;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        DealGroupShopVO dealGroupShopVO = new DealGroupShopVO();
        dealGroupShopVO.setTotalCount((int) shopListContext.getTotalCount());
        dealGroupShopVO.setTitle(getTitle(shopListContext));
        dealGroupShopVO.setTips(getTips(shopListContext, versionId, unifiedOrderWithId));
        dealGroupShopVO.setList(DealGroupShops);
        dealGroupShopVO.setVersion(shopListContext.getPageConfig().getVersion());

        if (shopListContext.isNeedLog()) {
            log.info("DzDealShopsHttpFacade.buildDealGroupShopVO, shopIds={}, isMagicalDeal={}, unifiedOrderWith={}, dealGroupShopVO={}", JsonUtils.toJson(shopListContext.getShopIds()), isMagicalDeal, JsonUtils.toJson(unifiedOrderWithId), JsonUtils.toJson(dealGroupShopVO));
        }
        doCatLog(shopListContext, dealGroupShopVO);
        dealGroupShopVO.setDealCategoryId(shopListContext.getCategoryId());
        return dealGroupShopVO;
    }

    private void setPhoneNos(DealGroupShop dealGroupShop, AggregationContact contact,
                             Map<Long, Boolean> dpShopId2IsSelfMap, long dpShopId) {
        // 保洁自营商店不需要展示电话号码
        if (MapUtils.isNotEmpty(dpShopId2IsSelfMap) && dpShopId2IsSelfMap.getOrDefault(dpShopId, false)) {
            return;
        }
        dealGroupShop.setPhoneNos(contact != null ? contact.getPhoneList() : null);
    }

    private void doCatLog(ShopListContext shopListContext, DealGroupShopVO dealGroupShopVO) {
        Map<String, String> tagMaps = Maps.newHashMap();
        tagMaps.put("EntryPage", Optional.ofNullable(shopListContext.getEntryPage()).map(String::valueOf).orElse("1"));
        tagMaps.put("Client", Optional.ofNullable(shopListContext.getEnvCtx().getDztgClientTypeEnum()).map(DztgClientTypeEnum::getDesc).orElse("unknown"));
        int recallNum = Optional.ofNullable(dealGroupShopVO).map(DealGroupShopVO::getList).map(List::size).orElse(0);
        Cat.logMetricForCount("DealShopRecall", recallNum, tagMaps);
    }

    private String getPoiUrl(String url, ShopListContext shopListContext, long shopId) {
        return String.format("%s&mmcinflate=%s&mmcuse=%s&mmcbuy=%s&mmcfree=%s&channelType=%s&offlinecode=%s", url,
                MagicFlagUtils.toString(shopListContext.getMmcinflate()), MagicFlagUtils.toString(shopListContext.getMmcuse()),
                MagicFlagUtils.toString(shopListContext.getMmcbuy()), MagicFlagUtils.toString(shopListContext.getMmcfree()),
                getChannelType(shopListContext, shopId), getOfflineCode(shopListContext, shopId));
    }


    private String getChannelType(ShopListContext shopListContext, long shopId) {
        // 优惠码场景只对第一个POI携带渠道
        if (OFFLINE_CHANNEL.equals(shopListContext.getSource())) {
            return (shopListContext.getShopId() != null && shopListContext.getShopId() == shopId) ? shopListContext.getSource() : "";
        }
        return StringUtils.isNotBlank(shopListContext.getSource()) ? shopListContext.getSource() : "";
    }
    private String getOfflineCode(ShopListContext shopListContext, long shopId) {
        // 仅对首个POI返回优惠码
        return (shopListContext.getShopId() != null && shopListContext.getShopId() == shopId) ? shopListContext.getOfflineCode() : "";
    }

    private String getTips(ShopListContext shopListContext, String versionId, UnifiedOrderWithId unifiedOrderWithId) {
        if (shopListContext.getEntryPage() == null) {
            return "";
        }
        // 订后场景：有快照 & 团单快照绑了黑门店 & 订单已支付
        if (shopListContext.getEntryPage() == EntryPageEnum.AFTER_ORDER.getValue()) {
            return StringUtils.isNotBlank(versionId) && unifiedOrderWithId != null && unifiedOrderWithId.isHasPayment() && shopListContext.getHasBlackShop() ? shopListContext.getPageConfig().getTips() : "";
        }
        // 订后预约场景
        if (shopListContext.getEntryPage() == EntryPageEnum.AFTER_ORDER_BOOKING.getValue()) {
            return shopListContext.getPageConfig().getAfterOrderBookingTips();
        }
        // 订中场景
        if (shopListContext.getEntryPage() == EntryPageEnum.IN_ORDER.getValue()) {
            return shopListContext.getPageConfig().getInOrderTips();
        }
        return "";
    }

    private String getTitle(ShopListContext shopListContext) {
        if (shopListContext.getEntryPage() == null) {
            return shopListContext.getPageConfig().getDealPageName();
        }
        // 订后场景
        if (shopListContext.getEntryPage() == EntryPageEnum.AFTER_ORDER.getValue()) {
            return shopListContext.getPageConfig().getOrderPageName();
        }
        // 订后预约场景
        if (shopListContext.getEntryPage() == EntryPageEnum.AFTER_ORDER_BOOKING.getValue()) {
            return shopListContext.getPageConfig().getBookingPageName();
        }
        // 订中场景
        if (shopListContext.getEntryPage() == EntryPageEnum.IN_ORDER.getValue()) {
            return shopListContext.getPageConfig().getInOrderPageName();
        }
        return shopListContext.getPageConfig().getDealPageName();
    }

    private List<ChannelTagVO> toZdcTags(ShopCardDTO shopCardDTO, boolean isMagicalDeal) {
        if (shopCardDTO == null || CollectionUtils.isEmpty(shopCardDTO.getLabels())) {
            return Lists.newArrayList();
        }
        return shopCardDTO.getLabels().stream().filter(l -> isMagicalDeal || l.getTagId() != MAGICAL_TAG_ID).map(this::toChannelTagVO).collect(Collectors.toList());
    }

    private ChannelTagVO toChannelTagVO(Label label) {
        ChannelTagVO channelTagVO = new ChannelTagVO();
        channelTagVO.setTagType(label.getType());
        channelTagVO.setTagHeight(Optional.ofNullable(label.getHeight()).orElse(0));
        channelTagVO.setTagMargins(TAG_MARGIN);
        channelTagVO.setRoundCornerRadius(buildRoundCornerRadius(label.getRoundCornerRadius()));
        channelTagVO.setBorderWidth(Optional.ofNullable(label.getBorderWidth()).orElse(0.0));
        channelTagVO.setBorderColor(label.getBorderColor());
        channelTagVO.setTagText(buildTagText(label));
        channelTagVO.setLeftIcon(buildIcon(label.getUrl(), label.getWidth(), label.getHeight()));
        channelTagVO.setRightIcon(buildIcon(label.getUrl2(), label.getUrl2Width(), label.getUrl2Height()));
        channelTagVO.setJumpUrl(label.getJumpUrl());
        channelTagVO.setBackGroundColor(label.getBgColor());
        return channelTagVO;
    }

    /**
     * 是否展示简版卡片样式
     */
    private boolean showSimpleCard(Integer entryPage) {
        return entryPage != null && entryPage == EntryPageEnum.IN_ORDER.getValue();
    }

    private TagTextVO buildTagText(Label tag) {
        TagTextVO tagTextVO = new TagTextVO();
        if (StringUtils.isEmpty(tag.getTitle())) {
            return null;
        }
        tagTextVO.setTagText(tag.getTitle());
        tagTextVO.setTextSize(tag.getTextSize());
        tagTextVO.setTextColor(tag.getTextColor());
        tagTextVO.setPaddingLeft(getPaddingLeft(tag));
        tagTextVO.setPaddingRight(getPaddingRight(tag));
        return tagTextVO;
    }

    private RoundCornerRadius buildRoundCornerRadius(List<Double> roundCornerRadiusList) {
        RoundCornerRadius roundCornerRadius = new RoundCornerRadius();
        if (CollectionUtils.isEmpty(roundCornerRadiusList) || CollectionUtils.size(roundCornerRadiusList) != ROUND_CONNER_RADIUS_SIZE) {
            return null;
        }
        roundCornerRadius.setBorderTopLeftRadius(Optional.ofNullable(roundCornerRadiusList.get(0)).orElse(0.0));
        roundCornerRadius.setBorderTopRightRadius(Optional.ofNullable(roundCornerRadiusList.get(1)).orElse(0.0));
        roundCornerRadius.setBorderBottomRightRadius(Optional.ofNullable(roundCornerRadiusList.get(2)).orElse(0.0));
        roundCornerRadius.setBorderBottomLeftRadius(Optional.ofNullable(roundCornerRadiusList.get(3)).orElse(0.0));
        return roundCornerRadius;
    }

    private double getPaddingLeft(Label tag) {
        if (tag.getTextPaddingLeft() > 0) {
            return tag.getTextPaddingLeft();
        }
        return tag.getPaddingHorizontal();
    }

    private double getPaddingRight(Label tag) {
        if (tag.getTextPaddingRight() > 0) {
            return tag.getTextPaddingRight();
        }
        return tag.getPaddingHorizontal();
    }

    protected TagIconsVO buildIcon(String url, Integer width, Integer height) {
        TagIconsVO tagIconsVO = new TagIconsVO();
        if (StringUtils.isEmpty(url)) {
            return null;
        }
        tagIconsVO.setTagIcon(url);
        tagIconsVO.setWidth(width != null ? width : 0);
        tagIconsVO.setHight(height != null ? height : 0);
        return tagIconsVO;
    }

    private String getMapUrl(ShopCardDTO shopCardDTO, ShopListContext shopListContext) {
        // 主站APP
        if (shopListContext.getEnvCtx().judgeMainApp()) {
            return Optional.ofNullable(shopCardDTO).map(ShopCardDTO::getMapUrl).orElse("");
        }
        if (Double.compare(shopCardDTO.getLng(), 0.0) == 0 || Double.compare(shopCardDTO.getLat(), 0.0) == 0) {
            return "";
        }
        // H5场景
        String mtH5MapSchema = "https://m-map.meituan.com/navi/?key=m84068fb336d4f07b059e25ee07d509j&dest=%s&destName=%s&start=%s";
        if (!shopListContext.getEnvCtx().isWxMini()) {
            return String.format(mtH5MapSchema, getDestGps(shopCardDTO), shopCardDTO.getShopName(), getCurrentGps(shopListContext));
        }

        // 微信小程序场景
        String preSchema = shopListContext.getEnvCtx().getDztgClientTypeEnum() == DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP ? "/pages/webview/webview?url=" : "/index/pages/h5/h5?weburl=";
        try {
            return preSchema + URLEncoder.encode(String.format(mtH5MapSchema, getDestGps(shopCardDTO), shopCardDTO.getShopName(), getCurrentGps(shopListContext)), StandardCharsets.UTF_8.name());
        } catch (UnsupportedEncodingException e) {
            log.error("DzDealShopsHttpFacade.getMapUrl failed, shopCardDTO={}", JsonUtils.toJson(shopCardDTO), e);
        }
        return "";
    }

    private String getCurrentGps(ShopListContext shopListContext) {
        if (Double.compare(shopListContext.getLng(), 0.0) == 0 || Double.compare(shopListContext.getLat(), 0.0) == 0) {
            return "";
        }
        return String.format("%.6f,%.6f", shopListContext.getLng(), shopListContext.getLat());
    }


    private String getDestGps(ShopCardDTO shopCardDTO) {
        return String.format("%.6f,%.6f", shopCardDTO.getLng(), shopCardDTO.getLat());
    }

    private int formatShopPower(ShopCardDTO shopCardDTO) {
        if (shopCardDTO == null) {
            return -1;
        }
        return (int) (NumberUtils.toDouble(shopCardDTO.getStarStr(), -0.1) * 10);
    }

    private String formatShopAddress(String address, String distanceStr, Integer userGpsCityId, City poiCity) {
        if (poiCity == null || StringUtils.isBlank(poiCity.getName()) || StringUtils.isBlank(distanceStr)) {
            return address;
        }
        // 城市id完全一致
        if (userGpsCityId != null && userGpsCityId == poiCity.getId()) {
            return String.format("距您 %s·%s", distanceStr, address);
        }
        return String.format("%s·%s", poiCity.getName(), address);
    }

    private String buildDistanceDesc(String distanceStr, Integer userGpsCityId, City poiCity) {
        if (userGpsCityId != null && poiCity != null && userGpsCityId != poiCity.getId()) {
            return poiCity.getName();
        }
        return String.format("距您 %s", distanceStr);
    }

    private boolean showMagicalTag(ShopCardDTO shopCardDTO, boolean isMagicalDeal) {
        if (!isMagicalDeal) {
            return false;
        }
        if (CollectionUtils.isEmpty(shopCardDTO.getAttrs())) {
            return false;
        }
        AttrItemDTO attrItemDTO = shopCardDTO.getAttrs().stream().filter(s -> MAGICAL_ATTR_KEY.equals(s.getAttrName())).findFirst().orElse(null);
        if (attrItemDTO == null || StringUtils.isBlank(attrItemDTO.getAttrValue())) {
            return false;
        }
        ShopMagicalInfo shopMagicalInfo = JsonUtils.fromJson(attrItemDTO.getAttrValue(), ShopMagicalInfo.class);
        return shopMagicalInfo != null && shopMagicalInfo.getApplyResult() != NOT_APPLY;
    }

    private <T> T getObjectFromAttr(ShopCardDTO shopCardDTO, String attrKey, Class<T> clazz) {
        if (CollectionUtils.isEmpty(shopCardDTO.getAttrs())) {
            return null;
        }
        AttrItemDTO attrItemDTO = shopCardDTO.getAttrs().stream().filter(s -> attrKey.equals(s.getAttrName())).findFirst().orElse(null);
        if (attrItemDTO == null || StringUtils.isBlank(attrItemDTO.getAttrValue())) {
            return null;
        }
        return JsonUtils.fromJson(attrItemDTO.getAttrValue(), clazz);
    }

    private List<ShopIcon> buildShopIcons(boolean isMagicalShop, PageConfig pageConfig) {
        if (!isMagicalShop) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(new ShopIcon(ShopTagTypeEnum.PIC.getValue(), pageConfig.getMagicalTagUrl()));
    }

    private List<ShopIcon> buildShopIcons(ShopCardDTO shopCardDTO) {
        if (CollectionUtils.isEmpty(shopCardDTO.getLabels())) {
            return Lists.newArrayList();
        }
        return shopCardDTO.getLabels().stream().filter(l -> l.getTagId() == YOU_LE_XIAN_TAG_ID).map(item -> new ShopIcon(1, item.getTitle())).collect(Collectors.toList());
    }

    private String getMktSource(String dealSource) {
        if (StringUtils.isBlank(dealSource)) {
            return "";
        }
        Map<String, String> mapping = Lion.getMap("com.sankuai.dzu.tpbase.dztgdetailweb", SOURCE_MAPPING_LION_KEY, String.class);
        return MapUtils.isEmpty(mapping) ? "" : mapping.get(dealSource);
    }

    @Data
    private static class ShopMagicalInfo {
        private Integer applyResult;
    }

    @Data
    public static class ShopBusinessInfo {
        /**
         * https://km.sankuai.com/page/140458470
         * 当日营业时间文案
         */
        private String businessHour;
        /**
         * 今日营业标识
         */
        private String todayFlag;
        /**
         * 明日营业标识
         */
        private String tomorrowFlag;
    }

    private int formatCurrentState(ShopBusinessInfo shopBusinessInfo) {
        if (shopBusinessInfo == null || StringUtils.isBlank(shopBusinessInfo.getTodayFlag())) {
            return 0;
        }
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY); // 获取当前小时
        int minute = calendar.get(Calendar.MINUTE); // 获取当前分钟
        int index = 2 * hour + (minute >= 30 ? 1 : 0);
        char flag = shopBusinessInfo.getTodayFlag().charAt(index);
        return flag == '0' ? BusinessStateEnum.REST.getValue() : BusinessStateEnum.OPENING.getValue();
    }

}
