package com.dianping.mobile.mapi.dztgdetail.datatype.context;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import lombok.Data;

@Data
public class SkuCtx {
    // 是否多sku商品：太极团单 & 存在规格
    private boolean isMultiSku = false;
    // 默认skuId（取有效sku中salePrice最低一个）
    private String skuId;
    // 提单链接服务实验参数
    private String creatOrderExpId;
    // 团详提单实验结果
    private ModuleAbConfig dealCreatOrderAbConfig;
}
