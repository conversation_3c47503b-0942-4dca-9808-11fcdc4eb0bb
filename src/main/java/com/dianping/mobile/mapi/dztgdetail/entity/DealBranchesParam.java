package com.dianping.mobile.mapi.dztgdetail.entity;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
 * Created by linfeng on 15/9/7.
 */
@Data
public class DealBranchesParam {

    private Log log = LogFactory.getLog(getClass());
    /**
     * optional
     */
    private String sort;
    /**
     * optional
     */
    private int areaId;
    /**
     * optional
     */
    private int offset;
    /**
     * optional
     */
    private int limit;
    /**
     * optional
     */
    private int cityId;
    /**
     * optional
     */
    private boolean onlyCurCityPOIs;
    /**
     * optional
     */
    private String filter;
    private double lat;
    private double lng;

    public DealBranchesParam buildParam(MtCommonParam param, int limit, int offset,
                                        boolean onlyCurCityPOIs, String filter) {
        this.sort = param.getSortStr();
        this.limit = limit;
        this.offset = offset;
        this.onlyCurCityPOIs = onlyCurCityPOIs;
        this.filter = filter;
        try {
            this.areaId = !StringUtils.isBlank(param.getAreaId()) ? Integer.parseInt(param.getAreaId()) : 0;
            this.cityId = param.getCityId();
            this.lat = !StringUtils.isBlank(param.getLatStr()) ? Double.parseDouble(param.getLatStr()) : 0;
            this.lng = !StringUtils.isBlank(param.getLngStr()) ? Double.parseDouble(param.getLngStr()) : 0;
        } catch (NumberFormatException e) {
            log.error("DealBranchesParam.buildParam Number Format Exception: areaId|cityId|latitude|longitude convert error", e);
        }
        return this;
    }

}
