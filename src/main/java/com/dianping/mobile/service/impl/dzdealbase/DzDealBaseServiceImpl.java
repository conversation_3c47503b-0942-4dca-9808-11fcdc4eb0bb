package com.dianping.mobile.service.impl.dzdealbase;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.deal.dzdealbase.DzDealBaseService;
import com.dianping.deal.dzdealbase.response.DzDealBaseResponse;
import com.dianping.lion.common.util.JsonUtils;
import com.dianping.mobile.framework.base.datatypes.HttpCode;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.biz.CreateOrderPageUrlBiz;
import com.dianping.mobile.mapi.dztgdetail.common.constants.CatEvents;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.Response;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ShopPBO;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.facade.DealQueryParallFacade;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.deal.executor.DzDealBaseExecutor;
import com.dianping.mobile.mapi.dztgdetail.util.TraceUtils;
import com.dianping.pigeon.remoting.provider.config.annotation.Service;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/26
 */
@org.springframework.stereotype.Service("rpcDzDealBaseService")
@Slf4j
@Service(url = "com.sankuai.dzu.tpbase.dztgdetailweb.service.DzDealBaseService")
public class DzDealBaseServiceImpl implements DzDealBaseService {
    @Resource
    private DzDealBaseExecutor dzDealBaseExecutor;

    @Override
    public DzDealBaseResponse<String> execute(String dealBaseReqStr, String envCtxStr) {
        log.info("DzDealBaseServiceImpl execute, dealBaseReq:{}, envCtx:{}", dealBaseReqStr, envCtxStr);
        if (StringUtils.isBlank(dealBaseReqStr) || StringUtils.isBlank(envCtxStr)) {
            return  DzDealBaseResponse.fail(null);
        }

        DealBaseReq dealBaseReq = JSON.parseObject(dealBaseReqStr, DealBaseReq.class);
        if (Objects.isNull(dealBaseReq) || Objects.isNull(dealBaseReq.getDealgroupid())) return DzDealBaseResponse.fail(null);

        EnvCtx envCtx = JSON.parseObject(envCtxStr, EnvCtx.class);
        if (Objects.isNull(envCtx)) return DzDealBaseResponse.fail(null);

        try {
            String traceId = TraceUtils.getTraceId();
            CommonMobileResponse response = dzDealBaseExecutor.getExecuteResult(dealBaseReq, null, envCtx, true);
            if (!HttpCode.HTTPOK.equals(response.getStatusCode()) || Objects.isNull(response.getData())) {
                return DzDealBaseResponse.fail(String.format("查询失败，团单信息为空，traceId:%s", traceId));
            }
            DealGroupPBO dealGroupPBO = (DealGroupPBO) response.getData();
            if (Objects.isNull(dealGroupPBO)) {
                return DzDealBaseResponse.fail(String.format("查询失败，团单信息为空，traceId:%s", traceId));
            }
            return DzDealBaseResponse.success(JsonUtils.toJson(response.getData()));
        } catch (Exception e) {
            log.error("DzDealBaseServiceImpl.execute error, dealBaseReqStr:{}, envCtxStr:{}", dealBaseReqStr, envCtxStr, e);
            return DzDealBaseResponse.fail(e.getMessage());
        }
    }

}
