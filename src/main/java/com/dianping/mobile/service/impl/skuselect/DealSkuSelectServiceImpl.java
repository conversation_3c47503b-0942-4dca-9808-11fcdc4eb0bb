package com.dianping.mobile.service.impl.skuselect;

import com.dianping.deal.style.DealSkuSelectService;
import com.dianping.deal.style.dto.skuselect.SkuSalesAttrExt;
import com.dianping.deal.style.dto.skuselect.*;
import com.sankuai.dztheme.deal.DealSkuService;
import com.sankuai.dztheme.deal.req.SkuOptionRequest;
import com.sankuai.dztheme.deal.res.*;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2024/6/25
 */
@Service
public class DealSkuSelectServiceImpl implements DealSkuSelectService {
    @Autowired
    @Qualifier("dealSkuService")
    private DealSkuService dealSkuService;

    @Override
    public DealSkuSelctInfoResponse getDealSkuSelectInfo(DealSkuSelectInfoRequest request) {
        DealSkuSelctInfoResponse response = new DealSkuSelctInfoResponse();
        if (Objects.isNull(request) || (request.getPlatform() != IdTypeEnum.DP.getCode() && request.getPlatform() != IdTypeEnum.MT.getCode())) {
            response.setSuccess(false);
            response.setErrorMsg("参数异常");
        }
        SkuOptionRequest skuOptionRequest = new SkuOptionRequest();
        skuOptionRequest.setDealGroupId(Long.parseLong(request.getDealgroupid()));
        skuOptionRequest.setDealGroupIdType(request.getPlatform() == IdTypeEnum.MT.getCode() ? IdTypeEnum.MT.getCode() : IdTypeEnum.DP.getCode());
        skuOptionRequest.setShopId(NumberUtils.toLong(request.getShopid()));
        // 调pigeon接口取数
        DealSkuOptionDTO dealSkuOptionDTO = dealSkuService.querySkuOptions(skuOptionRequest);
        if (dealSkuOptionDTO == null || CollectionUtils.isEmpty(dealSkuOptionDTO.getSkuItemList()) || CollectionUtils.isEmpty(dealSkuOptionDTO.getSkuAttrMetaList())) {
            return new DealSkuSelctInfoResponse();
        }

        // 数据映射 DealSkuOptionDTO -> SkuSelectorWithPicVO
        // sku列表映射 SkuItemDTO -> SalesAttrToSkuBasicInfoDO
        List<SkuItemDTO> skuItemList = dealSkuOptionDTO.getSkuItemList();
        List<SalesAttrToSkuBasicInfoDO> salesAttrToSkuBasicInfoDOList = new ArrayList<>();
        for (SkuItemDTO skuItemDTO : skuItemList) {
            SalesAttrToSkuBasicInfoDO salesAttrToSkuBasicInfoDO = buildSalesAttrToSkuBasicInfo(skuItemDTO, request.getPlatform() == IdTypeEnum.MT.getCode());
            if (salesAttrToSkuBasicInfoDO == null) {
                response.setSuccess(false);
                response.setErrorMsg("数据格式错误");
                return response;
            }
            salesAttrToSkuBasicInfoDOList.add(salesAttrToSkuBasicInfoDO);
        }
        // spu元信息映射 SkuAttrMetaDTO -> SkuSalesAttrWithPicDO
        List<SkuAttrMetaDTO> skuAttrMetaDTOList = dealSkuOptionDTO.getSkuAttrMetaList();
        List<SkuSalesAttrWithPicDO> skuSalesAttrWithPicDOlist = new ArrayList<>();
        for (SkuAttrMetaDTO skuAttrMetaDTO : skuAttrMetaDTOList) {
            SkuSalesAttrWithPicDO skuSalesAttrWithPicDO = buildSkuSalesAttrWithPic(skuAttrMetaDTO);
            if (skuSalesAttrWithPicDO == null) {
                response.setSuccess(false);
                response.setErrorMsg("数据格式错误");
                return response;
            }
            skuSalesAttrWithPicDOlist.add(skuSalesAttrWithPicDO);
        }
        DealSkuSelctInfoResponse skuSelectorWithPicVO = new DealSkuSelctInfoResponse();
        skuSelectorWithPicVO.setSalesAttrToSkuBasicInfo(salesAttrToSkuBasicInfoDOList);
        skuSelectorWithPicVO.setSkuSalesAttrInfo(skuSalesAttrWithPicDOlist);
        skuSelectorWithPicVO.setSuccess(true);
        return skuSelectorWithPicVO;
    }

    public SalesAttrToSkuBasicInfoDO buildSalesAttrToSkuBasicInfo(SkuItemDTO skuItemDTO, boolean isMt) {
        List<DzAttrDo> dzAttrDos = new ArrayList<>();
        if (CollectionUtils.isEmpty(skuItemDTO.getSkuAttrList())) {
            return null;
        }
        for (SkuAttrDTO skuAttrDTO : skuItemDTO.getSkuAttrList()) {
            DzAttrDo dzAttrDo = new DzAttrDo();
            dzAttrDo.setCnName(skuAttrDTO.getAttrCnName());
            dzAttrDo.setName(skuAttrDTO.getAttrName());
            dzAttrDo.setValue(skuAttrDTO.getAttrValueDTO().getCode());
            // 属性id值
            dzAttrDo.setAttrId(skuAttrDTO.getAttrValueDTO().getId());
            dzAttrDos.add(dzAttrDo);
        }

        SkuBasicInfoDO skuBasicInfoDO = new SkuBasicInfoDO();
        skuBasicInfoDO.setSkuId(skuItemDTO.getSkuId());
        skuBasicInfoDO.setStock(isMt ? skuItemDTO.getMtStock() : skuItemDTO.getDpStock());
        skuBasicInfoDO.setSkuHeadPic(skuItemDTO.getSkuHeadPic());
        skuBasicInfoDO.setAttrList(dzAttrDos);

        SalesAttrToSkuBasicInfoDO salesAttrToSkuBasicInfoDO = new SalesAttrToSkuBasicInfoDO();
        salesAttrToSkuBasicInfoDO.setSkuBasicInfo(skuBasicInfoDO);
        salesAttrToSkuBasicInfoDO.setSalesAttrInfo(skuItemDTO.getSkuCode());
        salesAttrToSkuBasicInfoDO.setSalesAttrInfoKey(skuItemDTO.getSalesAttrInfoKey());

        return salesAttrToSkuBasicInfoDO;
    }

    public SkuSalesAttrWithPicDO buildSkuSalesAttrWithPic(SkuAttrMetaDTO skuAttrMetaDTO) {
        List<SkuSalesAttrValueDO> skuSalesAttrValueDOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(skuAttrMetaDTO.getOptionValueList())) {
            return null;
        }
        for (AttrValueDTO attrValueDTO : skuAttrMetaDTO.getOptionValueList()) {
            SkuSalesAttrValueDO skuSalesAttrValueDO = new SkuSalesAttrValueDO();
            skuSalesAttrValueDO.setCode(attrValueDTO.getCode());
            skuSalesAttrValueDO.setDesc(attrValueDTO.getDesc());
            // 属性id值
            skuSalesAttrValueDO.setAttrId(attrValueDTO.getId());
            skuSalesAttrValueDOS.add(skuSalesAttrValueDO);
        }


        SkuSalesAttrWithPicDO skuSalesAttrWithPicDO = new SkuSalesAttrWithPicDO();
        skuSalesAttrWithPicDO.setSkuSalesAttrName(skuAttrMetaDTO.getAttrName());
        skuSalesAttrWithPicDO.setSkuSalesAttrCnName(skuAttrMetaDTO.getAttrCnName());
        skuSalesAttrWithPicDO.setSkuSalesAttrDesc(skuAttrMetaDTO.getAttrDesc());
        skuSalesAttrWithPicDO.setAttrType(skuAttrMetaDTO.getAttrType());
        skuSalesAttrWithPicDO.setSkuSalesAttrValues(skuSalesAttrValueDOS);
        skuSalesAttrWithPicDO.setSkuSalesAttrExt(buildSkuSalesAttrExt(skuAttrMetaDTO.getSkuSalesAttrExt()));
        return skuSalesAttrWithPicDO;
    }

    public SkuSalesAttrExt buildSkuSalesAttrExt(com.sankuai.dztheme.deal.res.SkuSalesAttrExt skuSalesAttrExt) {
        if ( Objects.isNull(skuSalesAttrExt)) {
            return null;
        }
        SkuSalesAttrExt skuSalesAttrExtDO = new SkuSalesAttrExt();
        skuSalesAttrExtDO.setUrl(skuSalesAttrExt.getUrl());
        skuSalesAttrExtDO.setDesc(skuSalesAttrExt.getDesc());
        return skuSalesAttrExtDO;
    }
}
