package com.dianping.mobile.service.impl.url;


import com.dianping.cat.Cat;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.CardStyleProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealGroupProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.QueryCenterProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.style.ParallBeautyAdaptor;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.dztheme.deal.DealUrlReplaceService;
import com.sankuai.dztheme.deal.dto.IResponse;
import com.sankuai.dztheme.deal.dto.UrlReplaceDto;
import com.sankuai.dztheme.deal.req.UrlReplaceBatchRequest;
import com.sankuai.dztheme.deal.req.UrlReplaceRequest;
import com.sankuai.general.product.query.center.client.builder.model.*;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.AttrSubjectEnum;
import com.sankuai.general.product.query.center.client.enums.AttrTypeEnum;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.util.DealGroupUtils;
import com.sankuai.mpmctcontent.query.thrift.api.ContentFusion2CService;
import com.sankuai.mpmctcontent.query.thrift.dto.ItemFieldDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.aggre.SearchFusionInfoReqDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.aggre.SearchFusionInfoRespDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.aggre.StatResultDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2023/9/12
 * @since mapi-dztgdetail-web
 */
@Service
public class DealUrlReplaceServiceImpl implements DealUrlReplaceService {

    @Autowired
    private QueryCenterWrapper queryCenterWrapper;

    @Autowired
    private DealGroupProcessor dealGroupProcessor;

    @Autowired
    private CardStyleProcessor cardStyleProcessor;

    @Autowired
    DouHuService douHuService;

    @Autowired
    @Qualifier("contentFusion2C4UrlService")
    private ContentFusion2CService contentFusion2CService;

    private final static String DP_CARD_STYLE_V1_URL = "dpCardStyleV1Url";

    private final static String MT_CARD_STYLE_V1_URL = "mtCardStyleV1Url";

    private final static String DP_CARD_STYLE_V2_URL = "dpCardStyleV2Url";

    private final static String MT_CARD_STYLE_V2_URL = "mtCardStyleV2Url";

    private final static String CARD_STYLE_URL_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.card.style.url";

    @Override
    public IResponse<UrlReplaceDto> queryReplaceUrl(UrlReplaceRequest request) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.service.impl.url.DealUrlReplaceServiceImpl.queryReplaceUrl(com.sankuai.dztheme.deal.req.UrlReplaceRequest)");
        IResponse<UrlReplaceDto> response = null;
        try {
            //全量开关，关闭则不替换链接
            if(!Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.url.replace.switch", false)) {
                UrlReplaceDto dto = new UrlReplaceDto();
                dto.setNeedReplace(false);
                return IResponse.success(dto);
            }

            UrlReplaceDto urlReplaceDto = new UrlReplaceDto();
            //参数检测
            IResponse<UrlReplaceDto> check = checkBadParam(request);
            if(check.isFail()) {
                return check;
            }

            //先查询商品的categoryId
            DealGroupDTO dealGroupDTO = queryCenterWrapper.getDealGroupDTO(buildDealGroupRequest(Sets.newHashSet(request.getDealGroupId()), request.isMt()));
            DealGroupChannelDTO channelDTO = dealGroupProcessor.trans2OldChannelDTO(dealGroupDTO);

            //组装DealCtx对象
            DealCtx ctx = buildDealCtx(request.getUnionId(), request.getDztgClientType(), channelDTO, request.getAppVersion());

            matchCardStyle(ctx);

            /**
             * enableCardStyle 命中团详1.0
             * enableCardStyleV2 命中团详2.0
             * 优先判断是否命中1.0样式
             */
            if(ctx.isEnableCardStyleV2()) {
                urlReplaceDto.setNeedReplace(true);
                Map<String, String> urlConfigMap = Lion.getMap(CARD_STYLE_URL_CONFIG, String.class, new HashMap<>());
                urlReplaceDto.setUrlHead(urlConfigMap.get(request.isMt()? MT_CARD_STYLE_V2_URL:DP_CARD_STYLE_V2_URL));
            } else if(ctx.isEnableCardStyle()) {
                urlReplaceDto.setNeedReplace(true);
                Map<String, String> urlConfigMap = Lion.getMap(CARD_STYLE_URL_CONFIG, String.class, new HashMap<>());
                urlReplaceDto.setUrlHead(urlConfigMap.get(request.isMt()? MT_CARD_STYLE_V1_URL:DP_CARD_STYLE_V1_URL));
            } else {
                urlReplaceDto.setNeedReplace(false);
            }

            //美甲比较特殊，还要看款式
            if(channelDTO.getCategoryId() == ParallBeautyAdaptor.BEAUTY_NAIL_CATEGORY) {
                //查询款式信息
                int dpId = dealGroupDTO.getDpDealGroupIdInt();
                SearchFusionInfoReqDTO searchFusionInfoReqDTO = ParallBeautyAdaptor.buildReq(dpId);
                SearchFusionInfoRespDTO searchFusionInfoRespDTO = contentFusion2CService.searchFusionInfo(searchFusionInfoReqDTO);
                if(searchFusionInfoRespDTO != null && searchFusionInfoRespDTO.isSuccess()
                        && searchFusionInfoRespDTO.getTotalCount() != null && searchFusionInfoRespDTO.getTotalCount() >= 10L) {
                    ctx.setBeautyNailMultiStyle(true);
                }

                if(ctx.isEnableCardStyleV2()) {
                    urlReplaceDto.setNeedReplace(true);
                    Map<String, String> urlConfigMap = Lion.getMap(CARD_STYLE_URL_CONFIG, String.class, new HashMap<>());
                    urlReplaceDto.setUrlHead(urlConfigMap.get(request.isMt()? MT_CARD_STYLE_V2_URL:DP_CARD_STYLE_V2_URL));
                } else if(ctx.isEnableCardStyle() && !ctx.isBeautyNailMultiStyle()) {
                    //满足1.0 且多款式false,走1.0逻辑
                    urlReplaceDto.setNeedReplace(true);
                    Map<String, String> urlConfigMap = Lion.getMap(CARD_STYLE_URL_CONFIG, String.class, new HashMap<>());
                    urlReplaceDto.setUrlHead(urlConfigMap.get(request.isMt()? MT_CARD_STYLE_V1_URL:DP_CARD_STYLE_V1_URL));
                } else {
                    urlReplaceDto.setNeedReplace(false);
                    urlReplaceDto.setUrlHead(null);
                }
            }

            return IResponse.success(urlReplaceDto);
        } catch (Exception e) {
            Cat.logError(e);
        }
        return response;
    }

    @Override
    public IResponse<Map<Long, UrlReplaceDto>> batchQueryReplaceUrl(UrlReplaceBatchRequest request) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.service.impl.url.DealUrlReplaceServiceImpl.batchQueryReplaceUrl(com.sankuai.dztheme.deal.req.UrlReplaceBatchRequest)");
        IResponse<Map<Long, UrlReplaceDto>> response = null;
        try {
            //全量开关，关闭则不替换链接
            if(!Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.url.replace.switch", false)) {
                Map<Long, UrlReplaceDto> map = Maps.newHashMap();
                if(request != null && CollectionUtils.isNotEmpty(request.getDealGroupIds())) {
                    for(Long dealGroupId : request.getDealGroupIds()) {
                        UrlReplaceDto dto = new UrlReplaceDto();
                        dto.setNeedReplace(false);
                        map.put(dealGroupId, dto);
                    }
                    return IResponse.success(map);
                } else {
                    return IResponse.fail("参数错误");
                }
            }

            Map<Long, UrlReplaceDto> map = Maps.newHashMap();
            //参数检测
            IResponse<Map<Long, UrlReplaceDto>> check = checkBadParam(request);
            if(check.isFail()) {
                return check;
            }

            //先查询商品的categoryId
            List<DealGroupDTO> dealGroupDTOs = queryCenterWrapper.getDealGroupDTOs(buildDealGroupRequest(Sets.newHashSet(request.getDealGroupIds()), request.isMt()));


            //特殊处理下如果有美甲的团单
            Map<Integer, Integer> nailMap = Maps.newHashMap();
            List<Integer> nailDealIds = dealGroupDTOs.stream().filter(dealGroupDTO -> {
                        int category = dealGroupDTO.getCategory() == null ? 0 : Math.toIntExact(dealGroupDTO.getCategory().getCategoryId()==null?0:dealGroupDTO.getCategory().getCategoryId());
                        return category == ParallBeautyAdaptor.BEAUTY_NAIL_CATEGORY;
                    }).map(DealGroupDTO::getDpDealGroupIdInt).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(nailDealIds)) {
                SearchFusionInfoReqDTO searchFusionInfoReq = buildReq(nailDealIds);
                SearchFusionInfoRespDTO searchFusionInfoResp = contentFusion2CService.searchFusionInfo(searchFusionInfoReq);
                if(searchFusionInfoResp.isSuccess() && CollectionUtils.isNotEmpty(searchFusionInfoResp.getStatResultList())) {
                    for(StatResultDTO statResult : searchFusionInfoResp.getStatResultList()) {
                        if(MapUtils.isNotEmpty(statResult.getAttribute()) &&  statResult.getAttribute().containsKey("relatedDpDealId")) {
                            int dealId = Integer.parseInt(statResult.getAttribute().get("relatedDpDealId"));
                            if(nailDealIds.contains(dealId)) {
                                nailMap.put(dealId, statResult.getCount());
                            }
                        }
                    }
                }
            }

            for(DealGroupDTO dealGroupDTO : dealGroupDTOs) {
                DealGroupChannelDTO channelDTO = dealGroupProcessor.trans2OldChannelDTO(dealGroupDTO);

                //组装DealCtx对象
                DealCtx ctx = buildDealCtx(request.getUnionId(), request.getDztgClientType(), channelDTO, request.getAppVersion());

                matchCardStyle(ctx);

                /**
                 * enableCardStyle 命中团详1.0
                 * enableCardStyleV2 命中团详2.0
                 * 优先判断是否命中1.0样式
                 */
                UrlReplaceDto urlReplaceDto = new UrlReplaceDto();
                if(ctx.isEnableCardStyleV2()) {
                    urlReplaceDto.setNeedReplace(true);
                    Map<String, String> urlConfigMap = Lion.getMap(CARD_STYLE_URL_CONFIG, String.class, new HashMap<>());
                    urlReplaceDto.setUrlHead(urlConfigMap.get(request.isMt()? MT_CARD_STYLE_V2_URL:DP_CARD_STYLE_V2_URL));
                } else if(ctx.isEnableCardStyle()) {
                    urlReplaceDto.setNeedReplace(true);
                    Map<String, String> urlConfigMap = Lion.getMap(CARD_STYLE_URL_CONFIG, String.class, new HashMap<>());
                    urlReplaceDto.setUrlHead(urlConfigMap.get(request.isMt()? MT_CARD_STYLE_V1_URL:DP_CARD_STYLE_V1_URL));
                } else {
                    urlReplaceDto.setNeedReplace(false);
                }

                //美甲比较特殊，还要看款式
                if(channelDTO.getCategoryId() == ParallBeautyAdaptor.BEAUTY_NAIL_CATEGORY) {
                    //查询款式信息
                    int dpId = dealGroupDTO.getDpDealGroupIdInt();
                    if(nailMap.containsKey(dpId)) {
                        int count = nailMap.get(dpId);
                        if(count >= 10) {
                            ctx.setBeautyNailMultiStyle(true);
                        }
                    }

                    if(ctx.isEnableCardStyleV2()) {
                        urlReplaceDto.setNeedReplace(true);
                        Map<String, String> urlConfigMap = Lion.getMap(CARD_STYLE_URL_CONFIG, String.class, new HashMap<>());
                        urlReplaceDto.setUrlHead(urlConfigMap.get(request.isMt()? MT_CARD_STYLE_V2_URL:DP_CARD_STYLE_V2_URL));
                    } else if(ctx.isEnableCardStyle() && !ctx.isBeautyNailMultiStyle()) {
                        //满足1.0 且多款式false,走1.0逻辑
                        urlReplaceDto.setNeedReplace(true);
                        Map<String, String> urlConfigMap = Lion.getMap(CARD_STYLE_URL_CONFIG, String.class, new HashMap<>());
                        urlReplaceDto.setUrlHead(urlConfigMap.get(request.isMt()? MT_CARD_STYLE_V1_URL:DP_CARD_STYLE_V1_URL));
                    } else {
                        urlReplaceDto.setNeedReplace(false);
                        urlReplaceDto.setUrlHead(null);
                    }
                }
                map.put(request.isMt()?dealGroupDTO.getMtDealGroupId():dealGroupDTO.getDpDealGroupId(), urlReplaceDto);
            }

            return IResponse.success(map);
        } catch (Exception e) {
            Cat.logError(e);
        }

        return response;
    }

    private void matchCardStyle(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.service.impl.url.DealUrlReplaceServiceImpl.matchCardStyle(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        ctx.setEnableCardStyle(cardStyleProcessor.enableCardStyle(ctx));

        ModuleAbConfig moduleAbConfig = douHuService.enableCardStyleV2(ctx.getEnvCtx(), ctx.getCategoryId(), ctx.getMrnVersion());
        if (Objects.nonNull(moduleAbConfig)) {
            if(douHuService.hitEnableCardStyleV2(moduleAbConfig)) {
                ctx.setEnableCardStyleV2(true);
                Cat.logEvent("CardStyleV2", String.valueOf(ctx.getCategoryId()));
            }
        }
    }

    private DealCtx buildDealCtx(String unionId, int dztgClientType, DealGroupChannelDTO channelDTO, String appVersion) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.service.impl.url.DealUrlReplaceServiceImpl.buildDealCtx(java.lang.String,int,com.dianping.deal.publishcategory.dto.DealGroupChannelDTO,java.lang.String)");
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.codeOf(dztgClientType));
        envCtx.setUnionId(unionId);
        envCtx.setVersion(appVersion);

        DealCtx dealCtx = new DealCtx(envCtx);
        dealCtx.setChannelDTO(channelDTO);
        dealCtx.setMrnVersion("0.5.4");

        return dealCtx;
    }

    private IResponse<Map<Long, UrlReplaceDto>> checkBadParam(UrlReplaceBatchRequest request) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.service.impl.url.DealUrlReplaceServiceImpl.checkBadParam(com.sankuai.dztheme.deal.req.UrlReplaceBatchRequest)");
        if(request == null) {
            return IResponse.fail("参数不能为空");
        }

        if(CollectionUtils.isEmpty(request.getDealGroupIds())) {
            return IResponse.fail("团单id为空");
        }

        if(request.getDealGroupIds().size() > 20) {
            return IResponse.fail("批量查询数量应小于等于20");
        }

        if(StringUtils.isBlank(request.getUnionId())) {
            return IResponse.fail("unionId为空");
        }

        if(StringUtils.isBlank(request.getAppVersion())) {
            return IResponse.fail("appVersion为空");
        }

        DztgClientTypeEnum type = DztgClientTypeEnum.codeOf(request.getDztgClientType());
        if(type != DztgClientTypeEnum.MEITUAN_APP && type != DztgClientTypeEnum.DIANPING_APP) {
            return IResponse.fail("客户端类型不适配");
        }

        return IResponse.success(null);
    }


    private IResponse<UrlReplaceDto> checkBadParam(UrlReplaceRequest request) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.service.impl.url.DealUrlReplaceServiceImpl.checkBadParam(com.sankuai.dztheme.deal.req.UrlReplaceRequest)");
        if(request == null) {
            return IResponse.fail("参数不能为空");
        }

        if(request.getDealGroupId() <= 0) {
            return IResponse.fail("团单id错误");
        }

        if(StringUtils.isBlank(request.getUnionId())) {
            return IResponse.fail("unionId为空");
        }

        if(StringUtils.isBlank(request.getAppVersion())) {
            return IResponse.fail("appVersion为空");
        }

        DztgClientTypeEnum type = DztgClientTypeEnum.codeOf(request.getDztgClientType());
        if(type != DztgClientTypeEnum.MEITUAN_APP && type != DztgClientTypeEnum.DIANPING_APP) {
            return IResponse.fail("客户端类型不适配");
        }

        return IResponse.success(null);
    }

    private QueryByDealGroupIdRequest buildDealGroupRequest(Set<Long> dpDealGroupIdSet, boolean isMt) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.service.impl.url.DealUrlReplaceServiceImpl.buildDealGroupRequest(java.util.Set,boolean)");
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(dpDealGroupIdSet, isMt ? IdTypeEnum.MT : IdTypeEnum.DP)
                .dealGroupId(DealGroupIdBuilder.builder().all())
                .channel(DealGroupChannelBuilder.builder().all())
                .basicInfo(DealGroupBasicInfoBuilder.builder().all())
                .attrsByKey(AttrSubjectEnum.DEAL_GROUP, QueryCenterProcessor.getQueryCenterDealGroupAttrKey())
                .attrsByKey(AttrSubjectEnum.DEAL, QueryCenterProcessor.getQueryCenterDealAttrKey())
                .customer(DealGroupCustomerBuilder.builder().originCustomerId())
                .detail(DealGroupDetailBuilder.builder().all())
                .dealBasicInfo(DealBasicInfoBuilder.builder().all())
                .image(DealGroupImageBuilder.builder().all())
                .category(DealGroupCategoryBuilder.builder().all())
                .displayShop(DealGroupDisplayShopBuilder.builder().dpDisplayShopIds())
                .dealGroupTag(DealGroupTagBuilder.builder().all())
                .serviceProject(ServiceProjectBuilder.builder().all())
                .build();

        return queryByDealGroupIdRequest;
    }

    public static SearchFusionInfoReqDTO buildReq(List<Integer> dpIds) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.service.impl.url.DealUrlReplaceServiceImpl.buildReq(java.util.List)");
        SearchFusionInfoReqDTO searchFusionInfoReqDTO = new SearchFusionInfoReqDTO();
        searchFusionInfoReqDTO.setOwnerId(null);
        searchFusionInfoReqDTO.setOwnerType(null);
        searchFusionInfoReqDTO.setSubBizType(27);
        searchFusionInfoReqDTO.setStart(0);
        searchFusionInfoReqDTO.setLimit(1);

        List<ItemFieldDTO> queryList = new ArrayList<>();
        ItemFieldDTO query = new ItemFieldDTO();
        query.setFieldCode("relatedDpDealId");
        query.setFieldValue(String.valueOf(dpIds));
        queryList.add(query);

        searchFusionInfoReqDTO.setQueryList(queryList);
        searchFusionInfoReqDTO.setNonQueryList(null);
        searchFusionInfoReqDTO.setModuleKey("manicure_deal_related_module_batch");
        searchFusionInfoReqDTO.setBizType(2);
        searchFusionInfoReqDTO.setNeedTrans(null);
        searchFusionInfoReqDTO.setStatFieldList(null);
        searchFusionInfoReqDTO.setNeedValidateInterest(false);
        searchFusionInfoReqDTO.setStatFieldList(Lists.newArrayList("relatedDpDealId"));

        return searchFusionInfoReqDTO;
    }


}
