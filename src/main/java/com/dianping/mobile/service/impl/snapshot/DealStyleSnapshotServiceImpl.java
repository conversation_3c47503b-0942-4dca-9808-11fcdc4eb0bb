package com.dianping.mobile.service.impl.snapshot;

import com.alibaba.fastjson.JSONValidator;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.deal.style.DealStyleSnapshotService;
import com.dianping.deal.style.dto.dealstyle.DealStyleDTO;
import com.dianping.deal.style.dto.dealstyle.DealStyleRequest;
import com.dianping.deal.style.dto.dealstyle.DealStyleResponse;
import com.dianping.deal.style.dto.snapshot.DealStyleSnapshotRequest;
import com.dianping.deal.style.dto.snapshot.DealStyleSnapshotResponse;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.lion.common.util.JsonUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.Response;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealStyleBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.facade.DealQueryParallFacade;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.NumbersUtils;
import com.dianping.mobile.mapi.dztgdetail.util.VersionUtils;
import com.dianping.pigeon.remoting.provider.config.annotation.Service;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.GetDealGroupSnapshotRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupSnapshotResponse;
import com.sankuai.general.product.query.center.client.service.DealGroupSnapshotQueryService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024-10-21
 * @desc
 */
@Slf4j
@Service(url = "com.sankuai.dzu.tpbase.dztgdetailweb.service.DealStyleSnapshotService")
public class DealStyleSnapshotServiceImpl implements DealStyleSnapshotService {
    private static final String MT_DEAL_STRUCT_STYLE_KEY = "dealdetail_gc_packagedetail";

    private static final String DP_DEAL_STRUCT_STYLE_KEY = "tuandeal_gc_packagedetail";

    /**
     * 美团默认版本号
     */
    private static final String MT_DEFAULT_APP_VERSION = "12.20.400";

    /**
     * 点评默认版本号
     */
    private static final String DP_DEFAULT_APP_VERSION = "11.17.0";

    private static final String DEFAULT_MRN_VERSION = "0.5.7";

    @Resource
    private DealQueryParallFacade dealQueryParallFacade;

    @Resource
    private MapperWrapper mapperWrapper;

    @Resource
    private DealGroupSnapshotQueryService dealGroupSnapshotQueryService;


    @Override
    public DealStyleSnapshotResponse<String> queryDealStyleSnapshot(DealStyleSnapshotRequest dealStyleSnapshotRequest) {
        if (LionConfigUtils.inSnapshotUserIdGray(dealStyleSnapshotRequest.getUserId())) {
            log.info("queryDealStyleSnapshot request:{}", JsonUtils.toJson(dealStyleSnapshotRequest));
        }
        try {
            DealStyleBO dealStyleBO = getDealStyle4Write(dealStyleSnapshotRequest);
            if (Objects.isNull(dealStyleBO)) {
                return DealStyleSnapshotResponse.fail("查询失败，团单样式为空");
            }
            // 精简团单样式返回
            simplifyDealStyle(dealStyleBO);
            return DealStyleSnapshotResponse.success(JsonUtils.toJson(dealStyleBO));
        } catch (Exception e) {
            log.error("queryDealStyleSnapshot error, request:{}", dealStyleSnapshotRequest, e);
            return DealStyleSnapshotResponse.fail("系统异常");
        }
    }

    @Override
    public DealStyleResponse<DealStyleDTO> getDealStyle(DealStyleRequest dealStyleRequest) {
        if (LionConfigUtils.inSnapshotUserIdGray(dealStyleRequest.getUserId())) {
            log.info("getDealStyle request:{}", JsonUtils.toJson(dealStyleRequest));
        }
        DealStyleModuleConfig dealStyleModuleConfig = getDealStyleModuleConfig();
        // 如果传了订单ID，则单独处理
        if (StringUtils.isNotBlank(dealStyleRequest.getOrderId())) {
            DealGroupDTO dealGroupDTO = querySnapshot(dealStyleRequest.getOrderId(), dealStyleRequest.getDealGroupId());
            if (Objects.isNull(dealGroupDTO)) {
                return DealStyleResponse.fail("订单信息为空");
            }
            DealStyleBO dealStyleBO = getDealStyleFromSnapshot(dealGroupDTO);
            DealStyleDTO dealStyleDTO = getDealStyleDTO(dealStyleBO, dealStyleModuleConfig);
            return DealStyleResponse.success("查询成功", dealStyleDTO);
        }
        DealStyleSnapshotRequest dealStyleSnapshotRequest = buildDealStyleSnapshotRequest(dealStyleRequest);
        try {
            // 版控
            if (!checkAppVersion(dealStyleSnapshotRequest.getClientTypeEnum(), dealStyleSnapshotRequest.getAppVersion(), dealStyleModuleConfig)) {
                return DealStyleResponse.success("版本号校验未通过", getDefaultDealStyleDTO());
            }
            // 灰度开关已打开 and 命中灰度范围
            if (hitGrayRange(dealStyleSnapshotRequest, dealStyleModuleConfig)) {
                DealStyleBO dealStyleBO = getDealStyle4Read(dealStyleSnapshotRequest);
                DealStyleDTO dealStyleDTO = getDealStyleDTO(dealStyleBO, dealStyleModuleConfig);
                return DealStyleResponse.success("查询成功", dealStyleDTO);
            }
            return DealStyleResponse.success("未命中灰度场景或未打开交易快照开关", getDefaultDealStyleDTO());
        } catch (Exception e) {
            log.error("getDealStyle error, dealStyleRequest:{}, dealStyleSnapshotRequest: {}", dealStyleRequest, dealStyleSnapshotRequest, e);
            return DealStyleResponse.fail("系统异常");
        }
    }

    private DealStyleBO getDealStyleFromSnapshot(DealGroupDTO dealGroupDTO) {
        if (Objects.isNull(dealGroupDTO) || CollectionUtils.isEmpty(dealGroupDTO.getAttrs())) {
            return null;
        }
        String dealStyleStr = getAttrValue(dealGroupDTO.getAttrs(), "productStyle");
        try (JSONValidator validator = JSONValidator.from(dealStyleStr)) {
            if (validator.validate()) {
                return JsonUtils.fromJson(dealStyleStr, DealStyleBO.class);
            }
        } catch(Exception e) {
            log.error("getDealStyle err, dealStyleStr={}", dealStyleStr, e);
        }
        return null;
    }

    /**
     * 订单id查订单信息
     **/
    private DealGroupDTO querySnapshot(String orderId, Long dpDealGroupId) {
        GetDealGroupSnapshotRequest request = new GetDealGroupSnapshotRequest();
        request.setOrderId(orderId);
        request.setDealGroupId(dpDealGroupId);
        request.setIdType(IdTypeEnum.DP.getCode());
        try {
            QueryDealGroupSnapshotResponse response = dealGroupSnapshotQueryService.getProductSnapshot(request);
            if (Objects.isNull(response) || Objects.isNull(response.getData()) || Objects.isNull(response.getData().getDealGroupDTO())) {
                return null;
            }
            return response.getData().getDealGroupDTO();
        } catch (Exception e) {
            log.error("querySnapshot error, orderId:{}, dpDealGroupId:{}", orderId, dpDealGroupId, e);
            return null;
        }
    }

    private boolean hitGrayRange(DealStyleSnapshotRequest dealStyleSnapshotRequest, DealStyleModuleConfig dealStyleModuleConfig) {
        // 三个条件满足其一即可展示新的交易快照
        // 1. 开启了全量开关
        // 2. 用户ID在灰度范围内
        // 3. 用户ID是配置的白名单
        return NumbersUtils.greaterThanZero(dealStyleSnapshotRequest.getUserId())
                && (dealStyleModuleConfig.isAllPass()
                    || (dealStyleModuleConfig.isEnable()
                        && (dealStyleSnapshotRequest.getUserId() % dealStyleModuleConfig.getGrayGroupRange() < dealStyleModuleConfig.getGrayRange()
                            || (CollectionUtils.isNotEmpty(dealStyleModuleConfig.getUserIds())
                                && dealStyleModuleConfig.getUserIds().contains(dealStyleSnapshotRequest.getUserId())))));
    }

    private DealStyleDTO getDealStyleDTO(DealStyleBO dealStyleBO, DealStyleModuleConfig dealStyleModuleConfig) {
        if (Objects.isNull(dealStyleBO)) {
            return getDefaultDealStyleDTO();
        }
        String dealStructStyleKey = getDealStructStyleKey(dealStyleBO);
        DealStyleDTO dealStyleDTO = new DealStyleDTO();
        boolean newDealStructStyle = isNewDealStructStyle(dealStyleModuleConfig, dealStructStyleKey);
        dealStyleDTO.setNewDealStructStyle(newDealStructStyle);
        dealStyleDTO.setDealStructModuleKey(dealStructStyleKey);
        return dealStyleDTO;
    }

    private boolean isNewDealStructStyle(DealStyleModuleConfig dealStyleModuleConfig, String dealStructStyleKey) {
        if (StringUtils.isBlank(dealStructStyleKey)) {
            return false;
        }
        return !dealStyleModuleConfig.getOldDealStyleKeys().contains(dealStructStyleKey);
    }

    private DealStyleModuleConfig getDealStyleModuleConfig() {
        return Lion.getBean(Environment.getAppName(), "old.deal.style.module.key",
                DealStyleModuleConfig.class, getDefaultDealStyleModuleConfig());
    }

    private String getDealStructStyleKey(DealStyleBO dealStyleBO) {
        if (Objects.isNull(dealStyleBO) || Objects.isNull(dealStyleBO.getModuleConfigsModule())
                || CollectionUtils.isEmpty(dealStyleBO.getModuleConfigsModule().getModuleConfigs())) {
            return null;
        }
        return dealStyleBO.getModuleConfigsModule().getModuleConfigs().stream()
                .filter(module -> Objects.equals(module.getKey(), MT_DEAL_STRUCT_STYLE_KEY)
                            || Objects.equals(module.getKey(), DP_DEAL_STRUCT_STYLE_KEY))
                .findFirst()
                .orElse(new ModuleConfigDo())
                .getValue();
    }

    private DealStyleBO getDealStyle4Write(DealStyleSnapshotRequest dealStyleSnapshotRequest) {
        try {
            DealBaseReq request = buildDealBaseReq4Write(dealStyleSnapshotRequest);
            EnvCtx envCtx = buildEnvCtx4Write(dealStyleSnapshotRequest);
            Response<DealStyleBO> response = dealQueryParallFacade.queryDealGroupStyle(request, envCtx);
            if (!response.isSuccess() || Objects.isNull(response.getResult())) {
                return null;
            }
            return response.getResult();
        } catch (Exception e) {
            log.error("getDealStyle4Write error, request:{}", dealStyleSnapshotRequest, e);
            return null;
        }
    }

    private DealStyleBO getDealStyle4Read(DealStyleSnapshotRequest dealStyleSnapshotRequest) {
        try {
            DealBaseReq request = buildDealBaseReq4Read(dealStyleSnapshotRequest);
            EnvCtx envCtx = buildEnvCtx4Read(dealStyleSnapshotRequest);
            Response<DealStyleBO> response = dealQueryParallFacade.queryDealGroupStyle(request, envCtx);
            if (!response.isSuccess() || Objects.isNull(response.getResult())) {
                return null;
            }
            return response.getResult();
        } catch (Exception e) {
            log.error("getDealStyle4Read error, request:{}", dealStyleSnapshotRequest, e);
            return null;
        }
    }

    private void simplifyDealStyle(DealStyleBO dealStyleBO) {
        if (Objects.isNull(dealStyleBO) || Objects.isNull(dealStyleBO.getModuleConfigsModule())) {
            return;
        }
        ModuleConfigsModule moduleConfigsModule = dealStyleBO.getModuleConfigsModule();
        moduleConfigsModule.setModuleAbConfigs(null);
        dealStyleBO.setModuleConfigsModule(moduleConfigsModule);
    }

    private DealStyleSnapshotRequest buildDealStyleSnapshotRequest(DealStyleRequest dealStyleRequest) {
        if (Objects.isNull(dealStyleRequest)) {
            return new DealStyleSnapshotRequest();
        }
        DealStyleSnapshotRequest request = new DealStyleSnapshotRequest();
        request.setDealGroupId(dealStyleRequest.getDealGroupId());
        request.setShopId(dealStyleRequest.getShopId());
        request.setCityId(dealStyleRequest.getCityId());
        request.setClientTypeEnum(dealStyleRequest.getClientTypeEnum());
        // 此用户ID是交易后端侧传入的，没有虚ID的问题，不需要处理
        request.setUserId(dealStyleRequest.getUserId());
        request.setUnionId(dealStyleRequest.getUnionId());
        request.setRequestSource(dealStyleRequest.getRequestSource());
        request.setAppVersion(dealStyleRequest.getAppVersion());
        // 如果是开店宝dp_other渠道，则默认设置一个美团的版本号
        if (StringUtils.isBlank(request.getAppVersion()) && Objects.equals(request.getClientTypeEnum(), ClientTypeEnum.dp_other)) {
            request.setAppVersion(DP_DEFAULT_APP_VERSION);
        }
        return request;
    }

    private EnvCtx buildEnvCtx4Write(DealStyleSnapshotRequest snapshotRequest) {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        envCtx.setClientType(ClientTypeEnum.mt_mainApp_ios.getType());
        long mtRealUserId = getMtRealUserId(snapshotRequest);
        envCtx.setMtUserId(mtRealUserId);
        envCtx.setVersion(snapshotRequest.getAppVersion());
        envCtx.setUnionId(snapshotRequest.getUnionId());
        // 点评平台的版本号需要改成美团平台的
        if (!snapshotRequest.getClientTypeEnum().isMtPlatform() || StringUtils.isBlank(snapshotRequest.getAppVersion())) {
            envCtx.setVersion(MT_DEFAULT_APP_VERSION);
        }
        return envCtx;
    }

    private EnvCtx buildEnvCtx4Read(DealStyleSnapshotRequest snapshotRequest) {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(convert2DztgClientTypeEnum(snapshotRequest.getClientTypeEnum()));
        envCtx.setClientType(snapshotRequest.getClientTypeEnum().getType());
        if (envCtx.isMt()) {
            envCtx.setMtUserId(snapshotRequest.getUserId());
        } else {
            envCtx.setDpUserId(snapshotRequest.getUserId());
        }
        envCtx.setVersion(snapshotRequest.getAppVersion());
        envCtx.setUnionId(snapshotRequest.getUnionId());
        return envCtx;
    }

    private DztgClientTypeEnum convert2DztgClientTypeEnum(ClientTypeEnum clientTypeEnum) {
        switch (clientTypeEnum) {
            case dp_mainApp_ios:
            case dp_mainApp_android:
                return DztgClientTypeEnum.DIANPING_APP;
            case dp_other:
                return DztgClientTypeEnum.DPMERCHANT;
            default:
                return DztgClientTypeEnum.MEITUAN_APP;
        }
    }

    private DealBaseReq buildDealBaseReq4Write(DealStyleSnapshotRequest snapshotRequest) {
        DealBaseReq request = new DealBaseReq();
        request.setDealgroupid(snapshotRequest.getDealGroupId().intValue());
        request.setClienttype(ClientTypeEnum.mt_mainApp_ios.getType());
        request.setPoiid(snapshotRequest.getShopId());
        request.setCityid(snapshotRequest.getCityId());
        request.setPageSource(snapshotRequest.getRequestSource());
        request.setMrnversion(DEFAULT_MRN_VERSION);
        return request;
    }

    private DealBaseReq buildDealBaseReq4Read(DealStyleSnapshotRequest snapshotRequest) {
        DealBaseReq request = new DealBaseReq();
        request.setDealgroupid(snapshotRequest.getDealGroupId().intValue());
        request.setClienttype(snapshotRequest.getClientTypeEnum().getType());
        request.setPoiid(snapshotRequest.getShopId());
        request.setCityid(snapshotRequest.getCityId());
        request.setPageSource(snapshotRequest.getRequestSource());
        request.setMrnversion(DEFAULT_MRN_VERSION);
        return request;
    }

    private DealStyleDTO getDefaultDealStyleDTO() {
        DealStyleDTO dealStyleDTO = new DealStyleDTO();
        dealStyleDTO.setNewDealStructStyle(false);
        return dealStyleDTO;
    }

    private DealStyleDTO getNewStyleDealStyleDTO() {
        DealStyleDTO dealStyleDTO = new DealStyleDTO();
        dealStyleDTO.setNewDealStructStyle(true);
        return dealStyleDTO;
    }

    private DealStyleModuleConfig getDefaultDealStyleModuleConfig() {
        DealStyleModuleConfig defaultConfig = new DealStyleModuleConfig();
        defaultConfig.setEnable(true);
        List<String> oldDealStyleKeys = Lists.newArrayList();
        oldDealStyleKeys.add("structured_beauty_uniform");
        oldDealStyleKeys.add("structured_beautyhaircut");
        oldDealStyleKeys.add("structured_beauty_yoga");
        oldDealStyleKeys.add("structured_education");
        oldDealStyleKeys.add("structured_pet_new");
        oldDealStyleKeys.add("health_detail_structure");
        oldDealStyleKeys.add("structured_common_a");
        oldDealStyleKeys.add("uniform-structure-table-d");
        defaultConfig.setOldDealStyleKeys(oldDealStyleKeys);
        defaultConfig.setAllPass(false);
        defaultConfig.setMtAppVersion(MT_DEFAULT_APP_VERSION);
        defaultConfig.setDpAppVersion(DP_DEFAULT_APP_VERSION);
        return defaultConfig;
    }

    private boolean checkAppVersion(ClientTypeEnum clientTypeEnum, String appVersion, DealStyleModuleConfig dealStyleModuleConfig) {
        if (Objects.isNull(clientTypeEnum) || StringUtils.isBlank(appVersion)) {
            return false;
        }
        if (clientTypeEnum.isMtPlatform()) {
            return VersionUtils.isGreatEqualThan(appVersion, dealStyleModuleConfig.getMtAppVersion());
        }
        return VersionUtils.isGreatEqualThan(appVersion, dealStyleModuleConfig.getDpAppVersion());
    }
    
    private long getMtRealUserId(DealStyleSnapshotRequest request) {
        Long userId = null;
        if (request.getClientTypeEnum().isMtPlatform()) {
            userId = request.getUserId();
        } else {
            // 点评端传入的是美团虚ID，需要转换成美团实ID
            userId = mapperWrapper.getMtUserIdByMtVirtualUserId(request.getUserId());
        }
        return NumbersUtils.greaterThanZero(userId) ? userId : 0L;
    }

    private String getAttrValue(List<AttrDTO> attrs, String attrName) {
        Optional<AttrDTO> matchedAttr = Optional.ofNullable(attrs)
                .orElse(Collections.emptyList())
                .stream()
                .filter(attr -> attrName.equals(attr.getName()))
                .findFirst();
        return matchedAttr.isPresent() ? matchedAttr.get().getValue().get(0) : StringUtils.EMPTY;
    }

    @Data
    public static class DealStyleModuleConfig {
        /**
         * 是否开启新的交易快照页，但会按用户ID灰度
         */
        private boolean enable;

        private List<Long> userIds;

        /**
         * 灰度范围
         */
        private int grayRange;
        /**
         * 灰度分组范围
         */
        private int grayGroupRange;

        /**
         * 老的交易快照页团单样式key
         */
        private List<String> oldDealStyleKeys;
        /**
         * 是否全量
         */
        private boolean allPass;
        /**
         * 美团app版本，大于等于此版本号才走新的交易快照页
         */
        private String mtAppVersion;
        /**
         * 点评app版本，大于等于此版本号才走新的交易快照页
         */
        private String dpAppVersion;
    }
}
