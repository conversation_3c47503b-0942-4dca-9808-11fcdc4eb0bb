package com.dianping.mobile.service.impl.snapshot;

import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.lion.common.util.JsonUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealImageTextDetailReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.Response;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ImageTextDetailPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.ExhibitContentDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.Pair;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.snapshot.BaseDealDetailDTO;
import com.dianping.mobile.mapi.dztgdetail.facade.DealDetailFacade;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.facade.DealQueryParallFacade;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.NumbersUtils;
import com.dianping.mobile.mapi.dztgdetail.util.TraceUtils;
import com.dianping.pigeon.remoting.provider.config.annotation.Service;
import com.google.common.collect.Maps;
import com.sankuai.dztheme.deal.DealDetailSnapshotService;
import com.sankuai.dztheme.deal.dto.IResponse;
import com.sankuai.dztheme.deal.req.DealDetailSnapshotRequest;
import com.sankuai.dztheme.deal.req.DealImageTextDetailSnapshotRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-10-21
 * @desc 团购基础信息交易快照数据查询服务
 */
@Slf4j
@Service(url = "com.sankuai.dzu.tpbase.dztgdetailweb.service.DealDetailSnapshotService")
public class DealDetailSnapshotServiceImpl implements DealDetailSnapshotService {

    @Resource
    private DealQueryParallFacade dealQueryParallFacade;

    @Resource
    private DealDetailFacade dealDetailFacade;

    @Resource
    private MapperWrapper mapperWrapper;

    private static final String MT_DEFAULT_APP_VERSION = "12.11.400";

    /**
     * 团购详情类型
     */
    private static final int TYPE_DESCRIPTION = 1;
    /**
     * 购买须知类型
     */
    private static final int TYPE_REMINDERS = 2;

    @Override
    public IResponse<String> queryDealDetailSnapshot(DealDetailSnapshotRequest request) {
        if (LionConfigUtils.inSnapshotUserIdGray(request.getUserId())) {
            log.info("queryDealDetailSnapshot request:{}", JsonUtils.toJson(request));
        }
        DealBaseReq dealBaseReq = buildDealDetailRequest(request);
        EnvCtx envCtx = buildEnvCtx(request);
        Response<DealGroupPBO> response;
        try {
            String traceId = TraceUtils.getTraceId();
            response = dealQueryParallFacade.queryDealGroup(dealBaseReq, envCtx, Maps.newHashMap());
            if (!response.isSuccess() || Objects.isNull(response.getResult())) {
                return IResponse.fail(String.format("查询失败，团单信息为空，traceId:%s", traceId));
            }
            BaseDealDetailDTO baseDealDetail = simplifyDealDetail(response.getResult());
            if (Objects.isNull(baseDealDetail)) {
                return IResponse.fail(String.format("查询失败，团单信息为空，traceId:%s", traceId));
            }
            return IResponse.success(JsonUtils.toJson(baseDealDetail), String.format("traceId:%s", traceId));
        } catch (Exception e) {
            log.error("queryDealDetailSnapshot error, request:{}", request, e);
            return IResponse.fail(e.getMessage());
        }
    }

    @Override
    public IResponse<String> queryDealImageTextDetailSnapshot(DealImageTextDetailSnapshotRequest request) {
        DealImageTextDetailReq imageTextDetailReq = buildDealImageTextDetailRequest(request);
        EnvCtx envCtx = buildEnvCtx();
        ImageTextDetailPBO imageTextDetail;
        try {
            imageTextDetail = dealDetailFacade.queryImageText(imageTextDetailReq, envCtx);
            if (Objects.isNull(imageTextDetail)) {
                return IResponse.success("", "图文详情为空");
            }
            // 精简图文详情结构
            simplifyImageTextDetail(imageTextDetail);
            return IResponse.success(JsonUtils.toJson(imageTextDetail));
        } catch (Exception e) {
            log.error("queryDealImageTextDetailSnapshot error, request:{}", request, e);
            return IResponse.fail(e.getMessage());
        }
    }

    private EnvCtx buildEnvCtx(DealDetailSnapshotRequest snapshotRequest) {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        envCtx.setClientType(ClientTypeEnum.mt_mainApp_ios.getType());
        envCtx.setRequestURI("dzdealbase.bin");
        long mtRealUserId = getMtRealUserId(snapshotRequest);
        envCtx.setMtUserId(mtRealUserId);
        envCtx.setUnionId(snapshotRequest.getUnionId());
        envCtx.setVersion(snapshotRequest.getAppVersion());
        // 点评平台的版本号需要改成美团平台的
        if (!snapshotRequest.getClientTypeEnum().isMtPlatform() || StringUtils.isBlank(envCtx.getVersion())) {
            envCtx.setVersion(MT_DEFAULT_APP_VERSION);
        }
        return envCtx;
    }

    private DealBaseReq buildDealDetailRequest(DealDetailSnapshotRequest snapshotRequest) {
        DealBaseReq request = new DealBaseReq();
        request.setDealgroupid(snapshotRequest.getDealGroupId().intValue());
        // 默认传美团客户端
        request.setClienttype(ClientTypeEnum.mt_mainApp_ios.getType());
        request.setPoiid(snapshotRequest.getShopId());
        request.setCityid(snapshotRequest.getCityId());
        request.setPageSource(snapshotRequest.getRequestSource());
        request.setMrnversion("0.5.7");
        return request;
    }

    private void simplifyImageTextDetail(ImageTextDetailPBO imageTextDetail) {
        if (Objects.isNull(imageTextDetail)) {
            return;
        }
        imageTextDetail.setmoduleAbConfigs(null);
    }

    private DealImageTextDetailReq buildDealImageTextDetailRequest(DealImageTextDetailSnapshotRequest snapshotRequest) {
        DealImageTextDetailReq request = new DealImageTextDetailReq();
        request.setDealgroupid(snapshotRequest.getDealGroupId().intValue());
        request.setClienttype(ClientTypeEnum.mt_mainApp_ios.getType());
        return request;
    }

    private EnvCtx buildEnvCtx() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(ClientTypeEnum.mt_mainApp_ios.getType());
        return envCtx;
    }

    private BaseDealDetailDTO simplifyDealDetail(DealGroupPBO dealGroupPBO) {
        if (Objects.isNull(dealGroupPBO)) {
            return null;
        }
        BaseDealDetailDTO baseDealDetail = new BaseDealDetailDTO();
        // 非结构化购买须知
        if (CollectionUtils.isNotEmpty(dealGroupPBO.getStructedDetails())) {
            // 过滤掉非结构化详情 & 如果使用了结构化购买须知，则过滤掉非结构化购买须知
            List<Pair> details = dealGroupPBO.getStructedDetails().stream()
                    .filter(Objects::nonNull)
                    .filter(detail -> detail.getType() != TYPE_DESCRIPTION)
                    .filter(detail -> !dealGroupPBO.isHitStructuredPurchaseNote() || detail.getType() != TYPE_REMINDERS)
                    .collect(Collectors.toList());
            baseDealDetail.setStructedDetails(details);
        }
        // 结构化购买须知
        if (Objects.nonNull(dealGroupPBO.getPnPurchaseNoteDTO())) {
            baseDealDetail.setHitStructuredPurchaseNote(dealGroupPBO.isHitStructuredPurchaseNote());
            baseDealDetail.setPnPurchaseNoteDTO(dealGroupPBO.getPnPurchaseNoteDTO());
        }
        // 款式
        if (Objects.nonNull(dealGroupPBO.getExhibitContents())) {
            ExhibitContentDTO exhibitContentDTO = simplifyDealStyleImage(dealGroupPBO.getExhibitContents());
            baseDealDetail.setExhibitContents(exhibitContentDTO);
        }
        // 导购模块
        if (Objects.nonNull(dealGroupPBO.getHighlightsModule())) {
            baseDealDetail.setHighlightsModule(dealGroupPBO.getHighlightsModule());
        }
        // 头图、视频
        if (CollectionUtils.isNotEmpty(dealGroupPBO.getDealContents())) {
            baseDealDetail.setDealContents(dealGroupPBO.getDealContents());
        }
        // 限购条
        if (CollectionUtils.isNotEmpty(dealGroupPBO.getLimitsExtend())) {
            baseDealDetail.setLimitsExtend(dealGroupPBO.getLimitsExtend());
        }
        if (CollectionUtils.isNotEmpty(dealGroupPBO.getLimits())) {
            baseDealDetail.setLimits(dealGroupPBO.getLimits());
        }
        // 须知
        if (CollectionUtils.isNotEmpty(dealGroupPBO.getReminderInfo())) {
            baseDealDetail.setReminderInfo(dealGroupPBO.getReminderInfo());
        }
        if (CollectionUtils.isNotEmpty(dealGroupPBO.getReminderExtend())) {
            baseDealDetail.setReminderExtend(dealGroupPBO.getReminderExtend());
        }
        // 保障
        if (CollectionUtils.isNotEmpty(dealGroupPBO.getGuarantee())) {
            baseDealDetail.setGuarantee(dealGroupPBO.getGuarantee());
        }
        if (CollectionUtils.isNotEmpty(dealGroupPBO.getFeatures())) {
            baseDealDetail.setFeatures(dealGroupPBO.getFeatures());
        }
        // 团单标题
        if (StringUtils.isNotBlank(dealGroupPBO.getTitle())) {
            baseDealDetail.setTitle(dealGroupPBO.getTitle());
        }
        // 团单样式
        if (Objects.nonNull(dealGroupPBO.getModuleConfigsModule())) {
            baseDealDetail.setModuleConfigsModule(dealGroupPBO.getModuleConfigsModule());
        }
        // 通用浮层
        if (Objects.nonNull(dealGroupPBO.getGeneralFeaturesLayer())) {
            baseDealDetail.setGeneralFeaturesLayer(dealGroupPBO.getGeneralFeaturesLayer());
        }
        // 须知保障浮层
        if (Objects.nonNull(dealGroupPBO.getFeaturesLayer())) {
            baseDealDetail.setFeaturesLayer(dealGroupPBO.getFeaturesLayer());
        }
        // 类目ID
        baseDealDetail.setCategoryId(dealGroupPBO.getCategoryId());
        return baseDealDetail;
    }

    private ExhibitContentDTO simplifyDealStyleImage(ExhibitContentDTO exhibitContents) {
        if (Objects.isNull(exhibitContents)) {
            return null;
        }
        exhibitContents.setJumpUrl(StringUtils.EMPTY);
        exhibitContents.setArrowIcon(StringUtils.EMPTY);
        return exhibitContents;
    }


    private long getMtRealUserId(DealDetailSnapshotRequest request) {
        Long userId = null;
        if (request.getClientTypeEnum().isMtPlatform()) {
            userId = request.getUserId();
        } else {
            // 点评端传入的是美团虚ID，需要转换成美团实ID
            userId = mapperWrapper.getMtUserIdByMtVirtualUserId(request.getUserId());
        }
        return NumbersUtils.greaterThanZero(userId) ? userId : 0L;
    }
}
