package com.dianping.mobile.service.impl.im;

import com.dianping.cat.Cat;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ImmersiveImageWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImageTagVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageVO;
import com.dianping.mobile.mapi.dztgdetail.util.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzim.message.common.enums.MessageTypeEnum;
import com.sankuai.dzim.message.spi.MessageUnitSpiService;
import com.sankuai.dzim.message.spi.dto.BatchMessageUnitQueryRequest;
import com.sankuai.dzim.message.spi.dto.BatchMessageUnitResponse;
import com.sankuai.dzim.message.spi.dto.BatchMessageUnitUrlQueryRequest;
import com.sankuai.dzim.message.spi.dto.BatchMessageUnitUrlResponse;
import com.sankuai.dzim.message.spi.dto.PaginateMessageUnitQueryRequest;
import com.sankuai.dzim.message.spi.dto.PaginateMessageUnitResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-01-02
 * @desc IM消息回调接口
 */
@Service
public class DealMessageUnitServiceImpl implements MessageUnitSpiService {

    @Resource
    private ImmersiveImageWrapper immersiveImageWrapper;

    @Resource
    private MapperWrapper mapperWrapper;

    @Override
    public BatchMessageUnitResponse batchGetMessageUnit(BatchMessageUnitQueryRequest request) throws TException {
        List<String> exhibitImgIds = request.getBizIds();
        BatchMessageUnitResponse response = new BatchMessageUnitResponse();
        if (CollectionUtils.isEmpty(exhibitImgIds)) {
            response.setSuccess(false);
            response.setMsg("款式Id不能为空");
            return response;
        }
        Map<String, Map<String, String>> styleCardMap = null;
        try {
            long dpShopId = 0L;
            boolean isMt = ClientTypeEnum.isMtPlatform(request.getClientType());
            if (StringUtils.isNotBlank(request.getMerchantId()) && isMt) {
                Future shopIdMapperFuture =  mapperWrapper.preDpShopIdByMtShopId(Long.parseLong(request.getMerchantId()));
                dpShopId = mapperWrapper.getDpShopIdByMtShopIdLong(shopIdMapperFuture);
            }
            ImmersiveImageVO recommendImageVO = immersiveImageWrapper.batchGetStyleImage(Lists.newArrayList(exhibitImgIds),
                    "contentId", 502, isMt, dpShopId, 0L, request.getClientType());
            styleCardMap = getStyleCardMap(recommendImageVO, request.getMerchantId());
        } catch (Exception e) {
            Cat.logError("[DealMessageUnitServiceImpl] batchGetMessageUnit err {}", e);
        }
        if (MapUtils.isEmpty(styleCardMap)) {
            response.setSuccess(false);
            response.setMsg("未查询到款式信息");
            return response;
        }
        response.setSuccess(true);
        response.setBizIdAndUnitContentMap(styleCardMap);
        return response;
    }

    private Map<String, Map<String, String>> getStyleCardMap(ImmersiveImageVO recommendImageVO, String dpShopId) {
        if (Objects.isNull(recommendImageVO) || CollectionUtils.isEmpty(recommendImageVO.getItems())) {
            return null;
        }
        Map<String, Map<String,String>> result = Maps.newHashMap();
        recommendImageVO.getItems().forEach(item -> {
            Map<String, String> styleCard = Maps.newHashMap();
            List<String> tagNames = item.getTags().stream()
                    .filter(Objects::nonNull).map(ImageTagVO::getName)
                    .limit(3)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(tagNames)) {
                tagNames = Lists.newArrayList();
            }
            styleCard.put("unitName", "款式卡片");
            styleCard.put("labels", JsonUtils.toJson(tagNames));
            styleCard.put("imageUrl", CollectionUtils.isNotEmpty(item.getUrls()) ? item.getUrls().get(0).getUrl() : "");
            styleCard.put("bizId", item.getItemId());
            styleCard.put("buttonName", "咨询这款");
            styleCard.put("shopId", dpShopId);
            styleCard.put("title", item.getName());
            styleCard.put("imSendUnitType", String.valueOf(MessageTypeEnum.STYLE_CARD.getType()));
            result.put(item.getItemId(), styleCard);
        });
        return result;
    }
    
    @Override
    public PaginateMessageUnitResponse paginateMessageUnit(PaginateMessageUnitQueryRequest request) throws TException {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.service.impl.im.DealMessageUnitServiceImpl.paginateMessageUnit(com.sankuai.dzim.message.spi.dto.PaginateMessageUnitQueryRequest)");
        return null;
    }

    @Override
    public BatchMessageUnitUrlResponse batchGetMessageUnitUrl(BatchMessageUnitUrlQueryRequest request) throws TException {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.service.impl.im.DealMessageUnitServiceImpl.batchGetMessageUnitUrl(com.sankuai.dzim.message.spi.dto.BatchMessageUnitUrlQueryRequest)");
        return null;
    }
}
