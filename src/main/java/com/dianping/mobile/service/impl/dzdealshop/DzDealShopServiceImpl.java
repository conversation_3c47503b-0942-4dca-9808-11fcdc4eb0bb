package com.dianping.mobile.service.impl.dzdealshop;

import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.facade.shop.DzDealShopsRpcFacade;
import com.dianping.mobile.mapi.dztgdetail.util.JsonUtils;
import com.dianping.pigeon.remoting.provider.config.annotation.Service;
import com.dianping.zebra.util.StringUtils;
import com.sankuai.dz.api.request.DealShopsRequest;
import com.sankuai.dz.api.response.DealShopIdResponse;
import com.sankuai.dz.api.service.dzdealshop.DealShopService;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.MobileOSTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.PageResponseCodeEnum;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: zhangyuan103
 * @Date: 2025/6/3
 */
@Slf4j
@Service(url = "com.sankuai.dz.api.service.dzdealshop.DealShopService")
public class DzDealShopServiceImpl implements DealShopService {
    // 参数传递错误
    private static final String REQUEST_ERROR_MSG = "没传REQUIRED参数";

    @Resource
    private DzDealShopsRpcFacade dzDealShopsRpcFacade;

    // gateway中ClientTypeEnum.code和DztgClientTypeEnum之间的映射
    private static final Map<Integer, DztgClientTypeEnum> ClientTypeEnumMap = new HashMap<Integer, DztgClientTypeEnum>(){{
        put(ClientTypeEnum.MT_APP.getCode(), DztgClientTypeEnum.MEITUAN_APP);
        put(ClientTypeEnum.MT_XCX.getCode(), DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP);
        put(ClientTypeEnum.DP_APP.getCode(), DztgClientTypeEnum.DIANPING_APP);
        put(ClientTypeEnum.DP_XCX.getCode(), DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP);
    }};

    @Override
    public DealShopIdResponse queryShopIdByDealId(DealShopsRequest request) {
        DealShopIdResponse dealShopIdResponse = new DealShopIdResponse();
        try {
            if (validate(request)) {
                log.error("DzDealShopServiceImpl.queryShopIdByDealId request error, request={}", JsonUtils.toJson(request));
                dealShopIdResponse.setCode(PageResponseCodeEnum.FAILURE.getCode());
                dealShopIdResponse.setMsg(REQUEST_ERROR_MSG);
                return dealShopIdResponse;
            }
            return dzDealShopsRpcFacade.execute(request, buildEnvCtx4DealShop(request));
        } catch (Exception e) {
            log.error("DzDealShopServiceImpl.queryShopIdByDealId error, request={}", JsonUtils.toJson(request), e);
            dealShopIdResponse.setCode(PageResponseCodeEnum.PARTLY_FAIL.getCode());
            dealShopIdResponse.setMsg(e.getMessage());
        }
        return dealShopIdResponse;
    }

    private boolean validate(DealShopsRequest request) {
        if (request == null || request.getDealGroupId() <= 0 || StringUtils.isBlank(request.getVersion())
                || request.getUserId() <= 0 || request.getUserLat() <= 0 || request.getUserLng() <= 0
                || request.getPageNum() < 1 || request.getPageSize() < 1 || request.getCityId() <= 0) {
            return true;
        }
        // 判断客户端是否在美团、点评的app和小程序中
        if (!ClientTypeEnumMap.containsKey(request.getClientType())) {
            return true;
        }
        // 判断客户端是否在android和ios中
        return StringUtils.isBlank(request.getMobileOSType()) || !MobileOSTypeEnum.containsCode(request.getMobileOSType());
    }

    private EnvCtx buildEnvCtx4DealShop(DealShopsRequest request) {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(ClientTypeEnumMap.get(request.getClientType()));
        if (envCtx.isMt()) {
            envCtx.setMtUserId(request.getUserId());
        } else {
            envCtx.setDpUserId(request.getUserId());
        }
        envCtx.setClientType(getClientType(envCtx, request.getMobileOSType()));
        envCtx.setVersion(request.getVersion());
        return envCtx;
    }

    private int getClientType(EnvCtx envCtx, String mobileOSType) {
        if (envCtx.isMt()) {
            if(MobileOSTypeEnum.ANDROID.getCode().equals(mobileOSType)){
                return com.dianping.deal.common.enums.ClientTypeEnum .mt_mainApp_android.getType();
            } else {
                return com.dianping.deal.common.enums.ClientTypeEnum.mt_mainApp_ios.getType();
            }
        } else {
            if (MobileOSTypeEnum.ANDROID.getCode().equals(mobileOSType)) {
                return com.dianping.deal.common.enums.ClientTypeEnum.dp_mainApp_android.getType();
            } else {
                return com.dianping.deal.common.enums.ClientTypeEnum.dp_mainApp_ios.getType();
            }
        }
    }
}
