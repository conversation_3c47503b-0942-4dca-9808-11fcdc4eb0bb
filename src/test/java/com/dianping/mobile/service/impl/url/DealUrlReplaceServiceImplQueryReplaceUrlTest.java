package com.dianping.mobile.service.impl.url;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;
import com.dianping.deal.publishcategory.dto.ChannelDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.CardStyleProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealGroupProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.sankuai.dztheme.deal.dto.IResponse;
import com.sankuai.dztheme.deal.dto.UrlReplaceDto;
import com.sankuai.dztheme.deal.req.UrlReplaceRequest;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.mpmctcontent.query.thrift.api.ContentFusion2CService;
import com.sankuai.mpmctcontent.query.thrift.dto.aggre.SearchFusionInfoRespDTO;
import com.sankuai.mpmctmvacommon.resource.response.CommonRespDTO;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealUrlReplaceServiceImplQueryReplaceUrlTest {

    @InjectMocks
    private DealUrlReplaceServiceImpl dealUrlReplaceService;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Mock
    private DealGroupProcessor dealGroupProcessor;

    @Mock
    private CardStyleProcessor cardStyleProcessor;

    @Mock
    private DouHuService douHuService;

    @Mock
    private ContentFusion2CService contentFusion2CService;

    @Mock
    private Lion lion;

    private UrlReplaceRequest validRequest;

    private DealGroupDTO dealGroupDTO;

    private DealGroupChannelDTO channelDTO;

    @Before
    public void setUp() {
        validRequest = createValidRequest();
        dealGroupDTO = new DealGroupDTO();
        channelDTO = createChannelDTO(100);
    }

    private void setupBasicMocks() throws Exception {
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(dealGroupDTO);
    }

    private UrlReplaceRequest createValidRequest() {
        UrlReplaceRequest request = new UrlReplaceRequest();
        request.setDealGroupId(123L);
        request.setUnionId("test_union_id");
        request.setAppVersion("1.0.0");
        request.setDztgClientType(DztgClientTypeEnum.MEITUAN_APP.getCode());
        request.setMt(true);
        return request;
    }

    private DealGroupChannelDTO createChannelDTO(int categoryId) {
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(categoryId);
        channelDTO.setDealGroupId(123);
        ChannelDTO channel = new ChannelDTO();
        channel.setChannelId(1);
        channelDTO.setChannelDTO(channel);
        return channelDTO;
    }

    /**
     * Test case for null request parameter
     */
    @Test
    public void testQueryReplaceUrl_WhenRequestIsNull() throws Throwable {
        // act
        IResponse<UrlReplaceDto> response = dealUrlReplaceService.queryReplaceUrl(null);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("参数不能为空", response.getMsg());
    }

    /**
     * Test case for invalid deal group ID
     */
    @Test
    public void testQueryReplaceUrl_WhenDealGroupIdInvalid() throws Throwable {
        // arrange
        validRequest.setDealGroupId(0);
        // act
        IResponse<UrlReplaceDto> response = dealUrlReplaceService.queryReplaceUrl(validRequest);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("团单id错误", response.getMsg());
    }

    /**
     * Test case for empty unionId
     */
    @Test
    public void testQueryReplaceUrl_WhenUnionIdEmpty() throws Throwable {
        // arrange
        validRequest.setUnionId("");
        // act
        IResponse<UrlReplaceDto> response = dealUrlReplaceService.queryReplaceUrl(validRequest);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("unionId为空", response.getMsg());
    }

    /**
     * Test case for empty appVersion
     */
    @Test
    public void testQueryReplaceUrl_WhenAppVersionEmpty() throws Throwable {
        // arrange
        validRequest.setAppVersion("");
        // act
        IResponse<UrlReplaceDto> response = dealUrlReplaceService.queryReplaceUrl(validRequest);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("appVersion为空", response.getMsg());
    }

    /**
     * Test case for invalid client type
     */
    @Test
    public void testQueryReplaceUrl_WhenInvalidClientType() throws Throwable {
        // arrange
        validRequest.setDztgClientType(99);
        // act
        IResponse<UrlReplaceDto> response = dealUrlReplaceService.queryReplaceUrl(validRequest);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("客户端类型不适配", response.getMsg());
    }

    /**
     * Test case for card style v2 enabled
     */
    @Test
    public void testQueryReplaceUrl_WhenCardStyleV2Enabled() throws Throwable {
        // arrange
        setupBasicMocks();
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        when(douHuService.enableCardStyleV2(any(), anyInt(), any())).thenReturn(moduleAbConfig);
        when(douHuService.hitEnableCardStyleV2(moduleAbConfig)).thenReturn(true);
        Map<String, String> urlConfigMap = new HashMap<>();
        urlConfigMap.put("mtCardStyleV2Url", "mt_v2_url");
        when(dealGroupProcessor.trans2OldChannelDTO(any())).thenReturn(channelDTO);
        // act
        IResponse<UrlReplaceDto> response = dealUrlReplaceService.queryReplaceUrl(validRequest);
        // assert
        assertTrue(response.isSuccess());
        assertTrue(response.getData().isNeedReplace());
    }

    /**
     * Test case for card style v1 enabled
     */
    @Test
    public void testQueryReplaceUrl_WhenCardStyleV1Enabled() throws Throwable {
        // arrange
        setupBasicMocks();
        when(douHuService.enableCardStyleV2(any(), anyInt(), any())).thenReturn(null);
        when(cardStyleProcessor.enableCardStyle(any())).thenReturn(true);
        when(dealGroupProcessor.trans2OldChannelDTO(any())).thenReturn(channelDTO);
        // act
        IResponse<UrlReplaceDto> response = dealUrlReplaceService.queryReplaceUrl(validRequest);
        // assert
        assertTrue(response.isSuccess());
        assertTrue(response.getData().isNeedReplace());
    }

    /**
     * Test case for exception handling
     */
    @Test
    public void testQueryReplaceUrl_WhenExceptionOccurs() throws Throwable {
        // arrange
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenThrow(new RuntimeException("Test exception"));
        // act
        IResponse<UrlReplaceDto> response = dealUrlReplaceService.queryReplaceUrl(validRequest);
        // assert
        assertNull(response);
    }
}
