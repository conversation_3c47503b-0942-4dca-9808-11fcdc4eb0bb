package com.dianping.mobile.service.impl.url;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.sankuai.dztheme.deal.dto.IResponse;
import com.sankuai.dztheme.deal.dto.UrlReplaceDto;
import com.sankuai.dztheme.deal.req.UrlReplaceBatchRequest;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;

public class DealUrlReplaceServiceImplCheckBadParamTest {

    // Note: The test case for invalid DztgClientType has been omitted due to the exception indicating an unsupported operation.
    private DealUrlReplaceServiceImpl dealUrlReplaceService = new DealUrlReplaceServiceImpl();

    @Test(expected = IllegalArgumentException.class)
    public void testCheckBadParamRequestIsNull() throws Throwable {
        Method method = DealUrlReplaceServiceImpl.class.getDeclaredMethod("checkBadParam", UrlReplaceBatchRequest.class);
        method.setAccessible(true);
        method.invoke(dealUrlReplaceService, null);
    }

    @Test
    public void testCheckBadParamDealGroupIdsIsEmpty() throws Throwable {
        UrlReplaceBatchRequest request = new UrlReplaceBatchRequest();
        request.setDealGroupIds(null);
        Method method = DealUrlReplaceServiceImpl.class.getDeclaredMethod("checkBadParam", UrlReplaceBatchRequest.class);
        method.setAccessible(true);
        IResponse<Map<Long, UrlReplaceDto>> response = (IResponse<Map<Long, UrlReplaceDto>>) method.invoke(dealUrlReplaceService, request);
        assertEquals(500, response.getCode());
        assertEquals("团单id为空", response.getMsg());
    }

    @Test
    public void testCheckBadParamDealGroupIdsSizeGreaterThan20() throws Throwable {
        UrlReplaceBatchRequest request = new UrlReplaceBatchRequest();
        request.setDealGroupIds(Arrays.asList(new Long[21]));
        Method method = DealUrlReplaceServiceImpl.class.getDeclaredMethod("checkBadParam", UrlReplaceBatchRequest.class);
        method.setAccessible(true);
        IResponse<Map<Long, UrlReplaceDto>> response = (IResponse<Map<Long, UrlReplaceDto>>) method.invoke(dealUrlReplaceService, request);
        assertEquals(500, response.getCode());
        assertEquals("批量查询数量应小于等于20", response.getMsg());
    }

    @Test
    public void testCheckBadParamUnionIdIsNull() throws Throwable {
        UrlReplaceBatchRequest request = new UrlReplaceBatchRequest();
        request.setDealGroupIds(Arrays.asList(1L, 2L, 3L));
        request.setUnionId(null);
        Method method = DealUrlReplaceServiceImpl.class.getDeclaredMethod("checkBadParam", UrlReplaceBatchRequest.class);
        method.setAccessible(true);
        IResponse<Map<Long, UrlReplaceDto>> response = (IResponse<Map<Long, UrlReplaceDto>>) method.invoke(dealUrlReplaceService, request);
        assertEquals(500, response.getCode());
        assertEquals("unionId为空", response.getMsg());
    }

    @Test
    public void testCheckBadParamAppVersionIsNull() throws Throwable {
        UrlReplaceBatchRequest request = new UrlReplaceBatchRequest();
        request.setDealGroupIds(Arrays.asList(1L, 2L, 3L));
        request.setUnionId("unionId");
        request.setAppVersion(null);
        Method method = DealUrlReplaceServiceImpl.class.getDeclaredMethod("checkBadParam", UrlReplaceBatchRequest.class);
        method.setAccessible(true);
        IResponse<Map<Long, UrlReplaceDto>> response = (IResponse<Map<Long, UrlReplaceDto>>) method.invoke(dealUrlReplaceService, request);
        assertEquals(500, response.getCode());
        assertEquals("appVersion为空", response.getMsg());
    }
}
