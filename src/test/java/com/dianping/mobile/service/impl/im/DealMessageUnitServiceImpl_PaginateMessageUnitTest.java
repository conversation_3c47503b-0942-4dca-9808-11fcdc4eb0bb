package com.dianping.mobile.service.impl.im;

import static org.junit.Assert.assertNull;
import com.sankuai.dzim.message.spi.dto.PaginateMessageUnitQueryRequest;
import com.sankuai.dzim.message.spi.dto.PaginateMessageUnitResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealMessageUnitServiceImpl_PaginateMessageUnitTest {

    private DealMessageUnitServiceImpl dealMessageUnitService = new DealMessageUnitServiceImpl();

    /**
     * 测试 paginateMessageUnit 方法是否总是返回 null
     */
    @Test
    public void testPaginateMessageUnitReturnNull() throws Throwable {
        // arrange
        PaginateMessageUnitQueryRequest request = new PaginateMessageUnitQueryRequest();
        // act
        PaginateMessageUnitResponse response = dealMessageUnitService.paginateMessageUnit(request);
        // assert
        assertNull(response);
    }
}
