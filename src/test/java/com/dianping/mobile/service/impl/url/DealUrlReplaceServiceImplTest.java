package com.dianping.mobile.service.impl.url;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.publishcategory.dto.ChannelDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.sankuai.dztheme.deal.dto.IResponse;
import com.sankuai.dztheme.deal.dto.UrlReplaceDto;
import com.sankuai.dztheme.deal.req.UrlReplaceRequest;
import com.sankuai.mpmctcontent.query.thrift.dto.ItemFieldDTO;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.CardStyleProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.mpmctcontent.query.thrift.dto.ItemFieldDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.aggre.SearchFusionInfoReqDTO;
import java.lang.reflect.Method;
import java.util.*;

import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealUrlReplaceServiceImplTest {

    @InjectMocks
    private DealUrlReplaceServiceImpl dealUrlReplaceService;

    @Mock
    private CardStyleProcessor cardStyleProcessor;

    @Mock
    private DouHuService douHuService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private void invokeMatchCardStyle(DealCtx ctx) throws Exception {
        Method method = DealUrlReplaceServiceImpl.class.getDeclaredMethod("matchCardStyle", DealCtx.class);
        method.setAccessible(true);
        method.invoke(dealUrlReplaceService, ctx);
    }


    private IResponse<UrlReplaceDto> invokePrivateMethod(String methodName, Object... args) throws Exception {
        Method method = DealUrlReplaceServiceImpl.class.getDeclaredMethod(methodName, UrlReplaceRequest.class);
        method.setAccessible(true);
        return (IResponse<UrlReplaceDto>) method.invoke(dealUrlReplaceService, args);
    }

    /**
     * Tests the buildReq method with a non-empty list of dpIds.
     */
    @Test
    public void testBuildReqWithNonEmptyDpIds() throws Throwable {
        List<Integer> dpIds = Arrays.asList(1, 2, 3);
        SearchFusionInfoReqDTO result = DealUrlReplaceServiceImpl.buildReq(dpIds);
        assertNotNull("Result should not be null", result);
        assertFalse("Query list should not be empty", result.getQueryList().isEmpty());
        // The assertion below is incorrect because it assumes the fieldValue will be a list represented as a string.
        // However, the fieldValue is set as String.valueOf(dpIds), which would not produce "[1, 2, 3]" directly.
        // We need to adjust the assertion to correctly check the expected output.
        // Assuming the fieldValue is expected to be a string representation of the list, we need to correct the assertion.
        assertEquals("Field value does not match expected", String.valueOf(dpIds), result.getQueryList().get(0).getFieldValue());
    }

    /**
     * Tests the buildReq method with null dpIds.
     */
    @Test
    public void testBuildReqWithNullDpIds() throws Throwable {
        List<Integer> dpIds = null;
        SearchFusionInfoReqDTO result = DealUrlReplaceServiceImpl.buildReq(dpIds);
        assertNotNull("Result should not be null when dpIds is null", result);
        // The behavior for null dpIds is not specified, so we ensure the result is not null.
        // Additional assertions would depend on the expected behavior for null input, which is not provided.
    }

    /**
     * 测试buildDealGroupRequest方法，当dpDealGroupIdSet为空，isMt为true时
     */
    @Test
    public void testBuildDealGroupRequestWhenDpDealGroupIdSetIsEmptyAndIsMtIsTrue() throws Throwable {
        // arrange
        Set<Long> dpDealGroupIdSet = new HashSet<>();
        boolean isMt = true;
        // act
        Method method = DealUrlReplaceServiceImpl.class.getDeclaredMethod("buildDealGroupRequest", Set.class, boolean.class);
        method.setAccessible(true);
        QueryByDealGroupIdRequest result = (QueryByDealGroupIdRequest) method.invoke(dealUrlReplaceService, dpDealGroupIdSet, isMt);
        // assert
        assertNotNull(result);
        assertEquals(IdTypeEnum.MT.getCode(), result.getIdType().intValue());
    }

    /**
     * 测试buildDealGroupRequest方法，当dpDealGroupIdSet为空，isMt为false时
     */
    @Test
    public void testBuildDealGroupRequestWhenDpDealGroupIdSetIsEmptyAndIsMtIsFalse() throws Throwable {
        // arrange
        Set<Long> dpDealGroupIdSet = new HashSet<>();
        boolean isMt = false;
        // act
        Method method = DealUrlReplaceServiceImpl.class.getDeclaredMethod("buildDealGroupRequest", Set.class, boolean.class);
        method.setAccessible(true);
        QueryByDealGroupIdRequest result = (QueryByDealGroupIdRequest) method.invoke(dealUrlReplaceService, dpDealGroupIdSet, isMt);
        // assert
        assertNotNull(result);
        assertEquals(dpDealGroupIdSet, result.getDealGroupIds());
        assertEquals(IdTypeEnum.DP.getCode(), result.getIdType().intValue());
    }

    /**
     * 测试buildDealGroupRequest方法，当dpDealGroupIdSet不为空，isMt为true时
     */
    @Test
    public void testBuildDealGroupRequestWhenDpDealGroupIdSetIsNotEmptyAndIsMtIsTrue() throws Throwable {
        // arrange
        Set<Long> dpDealGroupIdSet = new HashSet<>();
        dpDealGroupIdSet.add(1L);
        boolean isMt = true;
        // act
        Method method = DealUrlReplaceServiceImpl.class.getDeclaredMethod("buildDealGroupRequest", Set.class, boolean.class);
        method.setAccessible(true);
        QueryByDealGroupIdRequest result = (QueryByDealGroupIdRequest) method.invoke(dealUrlReplaceService, dpDealGroupIdSet, isMt);
        // assert
        assertNotNull(result);
        assertEquals(dpDealGroupIdSet, result.getDealGroupIds());
        assertEquals(IdTypeEnum.MT.getCode(), result.getIdType().intValue());
    }

    /**
     * 测试buildDealGroupRequest方法，当dpDealGroupIdSet不为空，isMt为false时
     */
    @Test
    public void testBuildDealGroupRequestWhenDpDealGroupIdSetIsNotEmptyAndIsMtIsFalse() throws Throwable {
        // arrange
        Set<Long> dpDealGroupIdSet = new HashSet<>();
        dpDealGroupIdSet.add(1L);
        boolean isMt = false;
        // act
        Method method = DealUrlReplaceServiceImpl.class.getDeclaredMethod("buildDealGroupRequest", Set.class, boolean.class);
        method.setAccessible(true);
        QueryByDealGroupIdRequest result = (QueryByDealGroupIdRequest) method.invoke(dealUrlReplaceService, dpDealGroupIdSet, isMt);
        // assert
        assertNotNull(result);
        assertEquals(dpDealGroupIdSet, result.getDealGroupIds());
        assertEquals(IdTypeEnum.DP.getCode(), result.getIdType().intValue());
    }

    @Test
    public void testMatchCardStyle_CardStyleNotEnabled_ModuleAbConfigIsNull() throws Throwable {
        DealCtx ctx = mock(DealCtx.class);
        when(cardStyleProcessor.enableCardStyle(ctx)).thenReturn(false);
        invokeMatchCardStyle(ctx);
        verify(cardStyleProcessor, times(1)).enableCardStyle(ctx);
        verify(douHuService, never()).enableCardStyleV2(any(), anyInt(), anyString());
    }

    @Test
    public void testMatchCardStyle_CardStyleNotEnabled_ModuleAbConfigIsNotNull_HitEnableCardStyleV2ReturnFalse() throws Throwable {
        DealCtx ctx = mock(DealCtx.class);
        when(cardStyleProcessor.enableCardStyle(ctx)).thenReturn(false);
        when(ctx.getMrnVersion()).thenReturn("someVersion");
        invokeMatchCardStyle(ctx);
        verify(cardStyleProcessor, times(1)).enableCardStyle(ctx);
        verify(douHuService, times(1)).enableCardStyleV2(eq(null), eq(0), eq("someVersion"));
    }

    @Test
    public void testMatchCardStyle_CardStyleEnabled_ModuleAbConfigIsNull() throws Throwable {
        DealCtx ctx = mock(DealCtx.class);
        when(cardStyleProcessor.enableCardStyle(ctx)).thenReturn(true);
        invokeMatchCardStyle(ctx);
        verify(cardStyleProcessor, times(1)).enableCardStyle(ctx);
        verify(douHuService, never()).enableCardStyleV2(any(), anyInt(), anyString());
    }

    @Test
    public void testMatchCardStyle_CardStyleEnabled_ModuleAbConfigIsNotNull_HitEnableCardStyleV2ReturnFalse() throws Throwable {
        DealCtx ctx = mock(DealCtx.class);
        when(cardStyleProcessor.enableCardStyle(ctx)).thenReturn(true);
        when(ctx.getMrnVersion()).thenReturn("someVersion");
        invokeMatchCardStyle(ctx);
        verify(cardStyleProcessor, times(1)).enableCardStyle(ctx);
        verify(douHuService, times(1)).enableCardStyleV2(eq(null), eq(0), eq("someVersion"));
    }

    @Test
    public void testMatchCardStyle_CardStyleEnabled_ModuleAbConfigIsNotNull_HitEnableCardStyleV2ReturnTrue() throws Throwable {
        DealCtx ctx = mock(DealCtx.class);
        when(cardStyleProcessor.enableCardStyle(ctx)).thenReturn(true);
        when(ctx.getMrnVersion()).thenReturn("someVersion");
        invokeMatchCardStyle(ctx);
        verify(cardStyleProcessor, times(1)).enableCardStyle(ctx);
        verify(douHuService, times(1)).enableCardStyleV2(eq(null), eq(0), eq("someVersion"));
    }

    /**
     * Tests the buildDealCtx method under normal conditions.
     */
    @Test
    public void testBuildDealCtxNormal() throws Throwable {
        // Arrange
        String unionId = "testUnionId";
        int dztgClientType = DztgClientTypeEnum.MEITUAN_APP.getCode();
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO(1, 1, new ChannelDTO(1, "test", "test", 1, "test", "test"));
        String appVersion = "1.0.0";
        // Act
        Method buildDealCtxMethod = DealUrlReplaceServiceImpl.class.getDeclaredMethod("buildDealCtx", String.class, int.class, DealGroupChannelDTO.class, String.class);
        buildDealCtxMethod.setAccessible(true);
        DealCtx result = (DealCtx) buildDealCtxMethod.invoke(dealUrlReplaceService, unionId, dztgClientType, channelDTO, appVersion);
        // Assert
        assertNotNull(result);
        assertEquals(unionId, result.getEnvCtx().getUnionId());
        assertEquals(DztgClientTypeEnum.MEITUAN_APP, result.getEnvCtx().getDztgClientTypeEnum());
        assertEquals(channelDTO, result.getChannelDTO());
        assertEquals("0.5.4", result.getMrnVersion());
    }

    /**
     * Tests the buildDealCtx method under exceptional conditions, with null parameters.
     */
    @Test
    public void testBuildDealCtxException() throws Throwable {
        // Arrange
        String unionId = null;
        int dztgClientType = 0;
        DealGroupChannelDTO channelDTO = null;
        String appVersion = null;
        // Act
        Method buildDealCtxMethod = DealUrlReplaceServiceImpl.class.getDeclaredMethod("buildDealCtx", String.class, int.class, DealGroupChannelDTO.class, String.class);
        buildDealCtxMethod.setAccessible(true);
        try {
            buildDealCtxMethod.invoke(dealUrlReplaceService, unionId, dztgClientType, channelDTO, appVersion);
            fail("Expected UnsupportedOperationException to be thrown");
        } catch (Exception e) {
            // Assert
            assertTrue(e.getCause() instanceof UnsupportedOperationException);
            assertEquals("MiniProgramSceneEnum has no code of 0", e.getCause().getMessage());
        }
    }

    @Test
    public void testCheckBadParamDealGroupIdIsZero() throws Throwable {
        UrlReplaceRequest request = new UrlReplaceRequest();
        request.setDealGroupId(0);
        IResponse<UrlReplaceDto> response = invokePrivateMethod("checkBadParam", request);
        assertEquals(500, response.getCode());
        assertEquals("团单id错误", response.getMsg());
    }

    @Test
    public void testCheckBadParamUnionIdIsEmpty() throws Throwable {
        UrlReplaceRequest request = new UrlReplaceRequest();
        request.setDealGroupId(1);
        request.setUnionId("");
        IResponse<UrlReplaceDto> response = invokePrivateMethod("checkBadParam", request);
        assertEquals(500, response.getCode());
        assertEquals("unionId为空", response.getMsg());
    }

    @Test
    public void testCheckBadParamAppVersionIsEmpty() throws Throwable {
        UrlReplaceRequest request = new UrlReplaceRequest();
        request.setDealGroupId(1);
        request.setUnionId("unionId");
        request.setAppVersion("");
        IResponse<UrlReplaceDto> response = invokePrivateMethod("checkBadParam", request);
        assertEquals(500, response.getCode());
        assertEquals("appVersion为空", response.getMsg());
    }

    @Test
    public void testCheckBadParamDztgClientTypeIsInvalid() throws Throwable {
        UrlReplaceRequest request = new UrlReplaceRequest();
        request.setDealGroupId(1);
        request.setUnionId("unionId");
        request.setAppVersion("1.0.0");
        request.setDztgClientType(DztgClientTypeEnum.UNKNOWN.getCode());
        IResponse<UrlReplaceDto> response = invokePrivateMethod("checkBadParam", request);
        assertEquals(500, response.getCode());
        assertEquals("客户端类型不适配", response.getMsg());
    }

    @Test
    public void testCheckBadParamAllParamsAreValid() throws Throwable {
        UrlReplaceRequest request = new UrlReplaceRequest();
        request.setDealGroupId(1);
        request.setUnionId("unionId");
        request.setAppVersion("1.0.0");
        request.setDztgClientType(DztgClientTypeEnum.MEITUAN_APP.getCode());
        IResponse<UrlReplaceDto> response = invokePrivateMethod("checkBadParam", request);
        assertEquals(0, response.getCode());
        assertEquals("success", response.getMsg());
    }
}
