package com.dianping.mobile.service.impl.url;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.deal.publishcategory.dto.ChannelDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.CardStyleProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealGroupProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.style.ParallBeautyAdaptor;
import com.sankuai.dztheme.deal.dto.IResponse;
import com.sankuai.dztheme.deal.dto.UrlReplaceDto;
import com.sankuai.dztheme.deal.req.UrlReplaceBatchRequest;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.mpmctcontent.query.thrift.api.ContentFusion2CService;
import com.sankuai.mpmctcontent.query.thrift.dto.aggre.SearchFusionInfoRespDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.aggre.StatResultDTO;
import java.lang.reflect.Method;
import java.util.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealUrlReplaceServiceImplBuildDealGroupRequestTest {

    @InjectMocks
    private DealUrlReplaceServiceImpl dealUrlReplaceService;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Mock
    private DealGroupProcessor dealGroupProcessor;

    @Mock
    private CardStyleProcessor cardStyleProcessor;

    @Mock
    private DouHuService douHuService;

    @Mock
    private ContentFusion2CService contentFusion2CService;

    private UrlReplaceBatchRequest request;

    @Before
    public void setUp() {
        request = new UrlReplaceBatchRequest();
        request.setDealGroupIds(Arrays.asList(1L));
        request.setUnionId("test_union_id");
        request.setAppVersion("1.0.0");
        request.setDztgClientType(DztgClientTypeEnum.DIANPING_APP.getCode());
    }

    /**
     * 测试buildDealGroupRequest方法，当dpDealGroupIdSet为空，isMt为true时
     */
    @Test
    public void testBuildDealGroupRequestWhenDpDealGroupIdSetIsEmptyAndIsMtIsTrue() throws Throwable {
        // arrange
        Set<Long> dpDealGroupIdSet = new HashSet<>();
        boolean isMt = true;
        // act
        Method method = DealUrlReplaceServiceImpl.class.getDeclaredMethod("buildDealGroupRequest", Set.class, boolean.class);
        method.setAccessible(true);
        QueryByDealGroupIdRequest result = (QueryByDealGroupIdRequest) method.invoke(dealUrlReplaceService, dpDealGroupIdSet, isMt);
        // assert
        assertNotNull(result);
        assertEquals(dpDealGroupIdSet, result.getDealGroupIds());
        assertEquals(IdTypeEnum.MT.getCode(), result.getIdType().intValue());
    }

    /**
     * 测试buildDealGroupRequest方法，当dpDealGroupIdSet为空，isMt为false时
     */
    @Test
    public void testBuildDealGroupRequestWhenDpDealGroupIdSetIsEmptyAndIsMtIsFalse() throws Throwable {
        // arrange
        Set<Long> dpDealGroupIdSet = new HashSet<>();
        boolean isMt = false;
        // act
        Method method = DealUrlReplaceServiceImpl.class.getDeclaredMethod("buildDealGroupRequest", Set.class, boolean.class);
        method.setAccessible(true);
        QueryByDealGroupIdRequest result = (QueryByDealGroupIdRequest) method.invoke(dealUrlReplaceService, dpDealGroupIdSet, isMt);
        // assert
        assertNotNull(result);
        assertEquals(dpDealGroupIdSet, result.getDealGroupIds());
        assertEquals(IdTypeEnum.DP.getCode(), result.getIdType().intValue());
    }

    /**
     * 测试buildDealGroupRequest方法，当dpDealGroupIdSet不为空，isMt为true时
     */
    @Test
    public void testBuildDealGroupRequestWhenDpDealGroupIdSetIsNotEmptyAndIsMtIsTrue() throws Throwable {
        // arrange
        Set<Long> dpDealGroupIdSet = new HashSet<>();
        dpDealGroupIdSet.add(1L);
        boolean isMt = true;
        // act
        Method method = DealUrlReplaceServiceImpl.class.getDeclaredMethod("buildDealGroupRequest", Set.class, boolean.class);
        method.setAccessible(true);
        QueryByDealGroupIdRequest result = (QueryByDealGroupIdRequest) method.invoke(dealUrlReplaceService, dpDealGroupIdSet, isMt);
        // assert
        assertNotNull(result);
        assertEquals(dpDealGroupIdSet, result.getDealGroupIds());
        assertEquals(IdTypeEnum.MT.getCode(), result.getIdType().intValue());
    }

    /**
     * 测试buildDealGroupRequest方法，当dpDealGroupIdSet不为空，isMt为false时
     */
    @Test
    public void testBuildDealGroupRequestWhenDpDealGroupIdSetIsNotEmptyAndIsMtIsFalse() throws Throwable {
        // arrange
        Set<Long> dpDealGroupIdSet = new HashSet<>();
        dpDealGroupIdSet.add(1L);
        boolean isMt = false;
        // act
        Method method = DealUrlReplaceServiceImpl.class.getDeclaredMethod("buildDealGroupRequest", Set.class, boolean.class);
        method.setAccessible(true);
        QueryByDealGroupIdRequest result = (QueryByDealGroupIdRequest) method.invoke(dealUrlReplaceService, dpDealGroupIdSet, isMt);
        // assert
        assertNotNull(result);
        assertEquals(dpDealGroupIdSet, result.getDealGroupIds());
        assertEquals(IdTypeEnum.DP.getCode(), result.getIdType().intValue());
    }

    /**
     * Test case for null request
     */
    @Test
    public void testBatchQueryReplaceUrl_NullRequest() throws Throwable {
        // act
        IResponse<Map<Long, UrlReplaceDto>> response = dealUrlReplaceService.batchQueryReplaceUrl(null);
        // assert
        assertNotNull(response);
        assertTrue(response.isFail());
        assertEquals("参数不能为空", response.getMsg());
    }

    /**
     * Test case for empty dealGroupIds
     */
    @Test
    public void testBatchQueryReplaceUrl_EmptyDealGroupIds() throws Throwable {
        // arrange
        request.setDealGroupIds(new ArrayList<>());
        // act
        IResponse<Map<Long, UrlReplaceDto>> response = dealUrlReplaceService.batchQueryReplaceUrl(request);
        // assert
        assertNotNull(response);
        assertTrue(response.isFail());
        assertEquals("团单id为空", response.getMsg());
    }

    /**
     * Test case for normal deal with card style v2
     */
    @Test
    public void testBatchQueryReplaceUrl_CardStyleV2() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDpDealGroupId(1L);
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(100L);
        dealGroupDTO.setCategory(categoryDTO);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(100);
        channelDTO.setChannelDTO(new ChannelDTO());
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        when(queryCenterWrapper.getDealGroupDTOs(any(QueryByDealGroupIdRequest.class))).thenReturn(Collections.singletonList(dealGroupDTO));
        when(dealGroupProcessor.trans2OldChannelDTO(any())).thenReturn(channelDTO);
        when(cardStyleProcessor.enableCardStyle(any())).thenReturn(false);
        when(douHuService.enableCardStyleV2(any(EnvCtx.class), anyInt(), anyString())).thenReturn(moduleAbConfig);
        when(douHuService.hitEnableCardStyleV2(any(ModuleAbConfig.class))).thenReturn(true);
        // act
        IResponse<Map<Long, UrlReplaceDto>> response = dealUrlReplaceService.batchQueryReplaceUrl(request);
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertTrue(response.getData().get(1L).isNeedReplace());
    }

    /**
     * Test case for nail beauty deal with multiple styles
     */
    @Test
    public void testBatchQueryReplaceUrl_NailBeautyMultiStyle() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDpDealGroupId(1L);
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId((long) ParallBeautyAdaptor.BEAUTY_NAIL_CATEGORY);
        dealGroupDTO.setCategory(categoryDTO);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(ParallBeautyAdaptor.BEAUTY_NAIL_CATEGORY);
        channelDTO.setChannelDTO(new ChannelDTO());
        SearchFusionInfoRespDTO searchFusionInfoRespDTO = new SearchFusionInfoRespDTO();
        List<StatResultDTO> statResultList = new ArrayList<>();
        StatResultDTO statResultDTO = new StatResultDTO();
        Map<String, String> attribute = new HashMap<>();
        attribute.put("relatedDpDealId", "1");
        statResultDTO.setAttribute(attribute);
        // Ensure count is >= 10 to set isBeautyNailMultiStyle to true
        statResultDTO.setCount(10);
        statResultList.add(statResultDTO);
        searchFusionInfoRespDTO.setStatResultList(statResultList);
        when(queryCenterWrapper.getDealGroupDTOs(any(QueryByDealGroupIdRequest.class))).thenReturn(Collections.singletonList(dealGroupDTO));
        when(dealGroupProcessor.trans2OldChannelDTO(any())).thenReturn(channelDTO);
        when(contentFusion2CService.searchFusionInfo(any())).thenReturn(searchFusionInfoRespDTO);
        // Ensure enableCardStyle is false
        when(cardStyleProcessor.enableCardStyle(any())).thenReturn(false);
        // Ensure enableCardStyleV2 is false
        when(douHuService.enableCardStyleV2(any(EnvCtx.class), anyInt(), anyString())).thenReturn(null);
        // act
        IResponse<Map<Long, UrlReplaceDto>> response = dealUrlReplaceService.batchQueryReplaceUrl(request);
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertFalse(response.getData().get(1L).isNeedReplace());
    }
}
