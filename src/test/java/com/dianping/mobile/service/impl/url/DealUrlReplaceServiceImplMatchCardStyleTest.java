package com.dianping.mobile.service.impl.url;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.CardStyleProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import java.lang.reflect.Method;
import java.util.Collections;
import org.junit.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class DealUrlReplaceServiceImplMatchCardStyleTest {

    @InjectMocks
    private DealUrlReplaceServiceImpl dealUrlReplaceService;

    @Mock
    private CardStyleProcessor cardStyleProcessor;

    @Mock
    private DouHuService douHuService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private void invokeMatchCardStyle(DealCtx ctx) throws Exception {
        Method method = DealUrlReplaceServiceImpl.class.getDeclaredMethod("matchCardStyle", DealCtx.class);
        method.setAccessible(true);
        method.invoke(dealUrlReplaceService, ctx);
    }

    @Test
    public void testMatchCardStyle_CardStyleProcessorReturnFalse() throws Throwable {
        DealCtx ctx = mock(DealCtx.class);
        EnvCtx envCtx = mock(EnvCtx.class);
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(cardStyleProcessor.enableCardStyle(ctx)).thenReturn(false);
        invokeMatchCardStyle(ctx);
        verify(cardStyleProcessor, times(1)).enableCardStyle(ctx);
        verify(douHuService, never()).enableCardStyleV2(any(), anyInt(), anyString());
    }

    @Test
    public void testMatchCardStyle_CardStyleProcessorReturnTrue_EnableCardStyleV2ReturnNull() throws Throwable {
        DealCtx ctx = mock(DealCtx.class);
        EnvCtx envCtx = mock(EnvCtx.class);
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(ctx.getCategoryId()).thenReturn(0);
        when(ctx.getMrnVersion()).thenReturn(null);
        when(cardStyleProcessor.enableCardStyle(ctx)).thenReturn(true);
        when(douHuService.enableCardStyleV2(any(EnvCtx.class), eq(0), isNull())).thenReturn(null);
        invokeMatchCardStyle(ctx);
        verify(cardStyleProcessor, times(1)).enableCardStyle(ctx);
        verify(douHuService, times(1)).enableCardStyleV2(any(EnvCtx.class), eq(0), isNull());
    }

    @Test
    public void testMatchCardStyle_CardStyleProcessorReturnTrue_EnableCardStyleV2ReturnModuleAbConfig_HitEnableCardStyleV2ReturnFalse() throws Throwable {
        DealCtx ctx = mock(DealCtx.class);
        EnvCtx envCtx = mock(EnvCtx.class);
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(ctx.getCategoryId()).thenReturn(0);
        when(ctx.getMrnVersion()).thenReturn(null);
        when(cardStyleProcessor.enableCardStyle(ctx)).thenReturn(true);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        when(douHuService.enableCardStyleV2(any(EnvCtx.class), eq(0), isNull())).thenReturn(moduleAbConfig);
        when(douHuService.hitEnableCardStyleV2(moduleAbConfig)).thenReturn(false);
        invokeMatchCardStyle(ctx);
        verify(cardStyleProcessor, times(1)).enableCardStyle(ctx);
        verify(douHuService, times(1)).enableCardStyleV2(any(EnvCtx.class), eq(0), isNull());
        verify(douHuService, times(1)).hitEnableCardStyleV2(moduleAbConfig);
    }

    @Test
    public void testMatchCardStyle_CardStyleProcessorReturnTrue_EnableCardStyleV2ReturnModuleAbConfig_HitEnableCardStyleV2ReturnTrue() throws Throwable {
        DealCtx ctx = mock(DealCtx.class);
        EnvCtx envCtx = mock(EnvCtx.class);
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(ctx.getCategoryId()).thenReturn(0);
        when(ctx.getMrnVersion()).thenReturn(null);
        when(cardStyleProcessor.enableCardStyle(ctx)).thenReturn(true);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        when(douHuService.enableCardStyleV2(any(EnvCtx.class), eq(0), isNull())).thenReturn(moduleAbConfig);
        when(douHuService.hitEnableCardStyleV2(moduleAbConfig)).thenReturn(true);
        invokeMatchCardStyle(ctx);
        verify(cardStyleProcessor, times(1)).enableCardStyle(ctx);
        verify(douHuService, times(1)).enableCardStyleV2(any(EnvCtx.class), eq(0), isNull());
        verify(douHuService, times(1)).hitEnableCardStyleV2(moduleAbConfig);
    }
}
