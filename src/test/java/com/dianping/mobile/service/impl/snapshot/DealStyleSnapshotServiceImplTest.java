package com.dianping.mobile.service.impl.snapshot;

import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.deal.style.dto.dealstyle.DealStyleDTO;
import com.dianping.deal.style.dto.dealstyle.DealStyleRequest;
import com.dianping.deal.style.dto.dealstyle.DealStyleResponse;
import com.dianping.deal.style.dto.snapshot.DealStyleSnapshotRequest;
import com.dianping.deal.style.dto.snapshot.DealStyleSnapshotResponse;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.Response;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealStyleBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.facade.DealQueryParallFacade;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupSnapshotDTO;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupSnapshotResponse;
import com.sankuai.general.product.query.center.client.service.DealGroupSnapshotQueryService;
import org.apache.thrift.TException;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DealStyleSnapshotServiceImplTest {

    @InjectMocks
    private DealStyleSnapshotServiceImpl service;

    @Mock
    private DealQueryParallFacade dealQueryParallFacade;

    private MockedStatic<LionConfigUtils> lionConfigUtilsMockedStatic;

    private MockedStatic<Lion> lionMockedStatic;

    @Mock
    private DealGroupSnapshotQueryService dealGroupSnapshotQueryService;

    @Before
    public void setUp() {
        lionConfigUtilsMockedStatic = mockStatic(LionConfigUtils.class);
        lionMockedStatic = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        lionConfigUtilsMockedStatic.close();
        lionMockedStatic.close();
    }

    /**
     * 测试 queryDealStyleSnapshot 方法，当 dealQueryParallFacade.queryDealGroupStyle 返回成功且结果非空时
     */
    @Test
    public void testQueryDealStyleSnapshot_Success() throws Exception {
        // arrange
        DealStyleSnapshotRequest request = getDefaultRequest();
        DealStyleBO dealStyleBO = new DealStyleBO();
        dealStyleBO.setModuleConfigsModule(new ModuleConfigsModule());
        Response<DealStyleBO> response = new Response<>();
        response.setSuccess(true);
        response.setResult(dealStyleBO);

        when(dealQueryParallFacade.queryDealGroupStyle(any(DealBaseReq.class), any(EnvCtx.class))).thenReturn(response);

        // act
        DealStyleSnapshotResponse<String> result = service.queryDealStyleSnapshot(request);

        // assert
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
    }

    /**
     * 测试 getDealStyle 方法，当版本号校验未通过时
     */
    @Test
    public void testGetDealStyleWhenVersionCheckFails() {
        DealStyleRequest request = new DealStyleRequest();
        request.setUserId(1L);
        request.setClientTypeEnum(ClientTypeEnum.dp_other);

        DealStyleSnapshotServiceImpl.DealStyleModuleConfig config = new DealStyleSnapshotServiceImpl.DealStyleModuleConfig();
        config.setEnable(true);
        config.setDpAppVersion("12.0.0");

        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.inSnapshotLogWhiteList(request.getUserId()))
                .thenReturn(true);
        lionMockedStatic.when(() -> Lion.getBean(anyString(), anyString(), any(), any()))
                .thenReturn(config);

        DealStyleResponse<DealStyleDTO> response = service.getDealStyle(request);
        assertTrue(response.isSuccess());
        assertEquals("版本号校验未通过", response.getMsg());
        assertNotNull(response.getData());
        assertFalse(response.getData().isNewDealStructStyle());
    }

    /**
     * 测试 queryDealStyleSnapshot 方法，当 dealQueryParallFacade.queryDealGroupStyle 返回失败时
     */
    @Test
    public void testQueryDealStyleSnapshot_Fail() throws Exception {
        // arrange
        DealStyleSnapshotRequest request = new DealStyleSnapshotRequest();
        Response<DealStyleBO> response = new Response<>();
        response.setSuccess(false);

        // act
        DealStyleSnapshotResponse<String> result = service.queryDealStyleSnapshot(request);

        // assert
        assertFalse(result.isSuccess());
        assertEquals("查询失败，团单样式为空", result.getMsg());
    }

    @Test
    public void testGetDealStyleWhenAllPassAndEnableAreFalse() {
        DealStyleRequest request = new DealStyleRequest();
        request.setUserId(1L);
        request.setClientTypeEnum(ClientTypeEnum.mt_mainApp_android);
        request.setAppVersion("12.11.0");

        DealStyleSnapshotServiceImpl.DealStyleModuleConfig config = new DealStyleSnapshotServiceImpl.DealStyleModuleConfig();
        config.setAllPass(false);
        config.setEnable(false);
        config.setMtAppVersion("12.0.0");

        lionMockedStatic.when(() -> Lion.getBean(anyString(), anyString(), any(), any()))
                .thenReturn(config);

        DealStyleResponse<DealStyleDTO> response = service.getDealStyle(request);
        assertTrue(response.isSuccess());
        assertEquals("未命中灰度场景或未打开交易快照开关", response.getMsg());
        assertNotNull(response.getData());
        assertFalse(response.getData().isNewDealStructStyle());
    }

    @Test
    public void testGetDealStyleWhenMissGrayRange() {
        DealStyleRequest request = new DealStyleRequest();
        request.setUserId(111L);
        request.setClientTypeEnum(ClientTypeEnum.mt_mainApp_android);
        request.setAppVersion("12.11.0");

        DealStyleSnapshotServiceImpl.DealStyleModuleConfig config = new DealStyleSnapshotServiceImpl.DealStyleModuleConfig();
        config.setAllPass(false);
        config.setEnable(true);
        config.setGrayRange(10);
        config.setGrayGroupRange(100);
        config.setMtAppVersion("12.0.0");

        lionMockedStatic.when(() -> Lion.getBean(anyString(), anyString(), any(), any()))
                .thenReturn(config);

        DealStyleResponse<DealStyleDTO> response = service.getDealStyle(request);
        assertTrue(response.isSuccess());
        assertEquals("未命中灰度场景或未打开交易快照开关", response.getMsg());
        assertNotNull(response.getData());
        assertFalse(response.getData().isNewDealStructStyle());
    }

    /**
     * 测试订单ID为空，返回默认DealStyleDTO
     */
    @Test
    public void testGetDealStyle_OrderIdIsNull_ReturnDefaultDealStyleDTO() {
        // arrange
        DealStyleRequest request = new DealStyleRequest();
        request.setOrderId(null);

        // act
        DealStyleResponse<DealStyleDTO> response = service.getDealStyle(request);

        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertFalse(response.getData().isNewDealStructStyle());
    }


    /**
     * 测试订单ID不为空，但查询结果为空，返回失败响应
     */
    @Test
    public void testGetDealStyle_OrderIdIsNotNull_QueryResultIsNull_ReturnFailResponse() throws TException {
        // arrange
        DealStyleRequest request = new DealStyleRequest();
        request.setOrderId("123");
        request.setDealGroupId(1L);

        when(dealGroupSnapshotQueryService.getProductSnapshot(any())).thenReturn(null);

        // act
        DealStyleResponse<DealStyleDTO> response = service.getDealStyle(request);

        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("订单信息为空", response.getMsg());
    }

    /**
     * 测试订单ID不为空，查询结果不为空，返回成功响应
     */
    @Test
    public void testGetDealStyle_OrderIdIsNotNull_QueryResultIsNotNull_ReturnSuccessResponse() throws TException {
        // arrange
        DealStyleRequest request = new DealStyleRequest();
        request.setOrderId("123");
        request.setDealGroupId(1L);

        QueryDealGroupSnapshotResponse snapshotResponse = new QueryDealGroupSnapshotResponse();
        DealGroupSnapshotDTO dealGroupSnapshotDTO = new DealGroupSnapshotDTO();
        dealGroupSnapshotDTO.setDealGroupDTO(new DealGroupDTO());
        snapshotResponse.setData(dealGroupSnapshotDTO);
        when(dealGroupSnapshotQueryService.getProductSnapshot(any())).thenReturn(snapshotResponse);

        // act
        DealStyleResponse<DealStyleDTO> response = service.getDealStyle(request);

        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
    }

    @Test
    public void testGetDealStyleWhenInGrayRange() {
        DealStyleRequest request = new DealStyleRequest();
        request.setUserId(8L);
        request.setClientTypeEnum(ClientTypeEnum.mt_mainApp_android);
        request.setAppVersion("12.11.0");
        request.setDealGroupId(123456L);
        request.setShopId(456L);
        request.setCityId(10);

        DealStyleSnapshotServiceImpl.DealStyleModuleConfig config = new DealStyleSnapshotServiceImpl.DealStyleModuleConfig();
        config.setAllPass(false);
        config.setEnable(true);
        config.setGrayRange(10);
        config.setGrayGroupRange(100);
        config.setMtAppVersion("12.0.0");

        // arrange
        DealStyleBO dealStyleBO = new DealStyleBO();
        dealStyleBO.setModuleConfigsModule(new ModuleConfigsModule());
        Response<DealStyleBO> expectedResp = new Response<>();
        expectedResp.setSuccess(true);
        expectedResp.setResult(dealStyleBO);

        lionMockedStatic.when(() -> Lion.getBean(anyString(), anyString(), any(), any()))
                .thenReturn(config);
        when(dealQueryParallFacade.queryDealGroupStyle(any(DealBaseReq.class), any(EnvCtx.class))).thenReturn(expectedResp);


        DealStyleResponse<DealStyleDTO> response = service.getDealStyle(request);
        assertTrue(response.isSuccess());
        assertEquals("查询成功", response.getMsg());
        assertNotNull(response.getData());
        assertFalse(response.getData().isNewDealStructStyle());
    }

    private DealStyleSnapshotRequest getDefaultRequest() {
        DealStyleSnapshotRequest request = new DealStyleSnapshotRequest();
        request.setDealGroupId(1020903169L);
        request.setShopId(1588581L);
        request.setCityId(10);
        request.setClientTypeEnum(ClientTypeEnum.mt_mainApp_ios);
        request.setRequestSource("trade_snapshot");
        request.setUserId(5094296096L);
        request.setUnionId("f0e31c3c6fb94fa69ff138d4bb3f8302a169827617007144504");
        request.setAppVersion("12.11.400");
        return request;
    }


}
