package com.dianping.mobile.service.impl.snapshot;

import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.lion.common.util.JsonUtils;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealImageTextDetailReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.Response;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.Guarantee;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ImageTextDetailPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.highlights.DztgHighlightsModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealstruct.PnPurchaseNoteDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.ExhibitContentDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.Pair;
import com.dianping.mobile.mapi.dztgdetail.facade.DealDetailFacade;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.facade.DealQueryParallFacade;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.sankuai.dztheme.deal.dto.IResponse;
import com.sankuai.dztheme.deal.req.DealDetailSnapshotRequest;
import com.sankuai.dztheme.deal.req.DealImageTextDetailSnapshotRequest;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * 测试DealDetailSnapshotServiceImpl的queryDealDetailSnapshot方法
 */
@RunWith(MockitoJUnitRunner.class)
public class DealDetailSnapshotServiceImplTest {

    @InjectMocks
    private DealDetailSnapshotServiceImpl service;

    @Mock
    private DealQueryParallFacade dealQueryParallFacade;

    @Mock
    private DealDetailFacade dealDetailFacade;

    private MockedStatic<LionConfigUtils> lionConfigUtilsMockedStatic;

    @Before
    public void setUp() {
        lionConfigUtilsMockedStatic = mockStatic(LionConfigUtils.class);
    }

    @After
    public void tearDown() {
        lionConfigUtilsMockedStatic.close();
    }

    /**
     * 测试查询成功且返回结果非空的场景
     */
    @Test
    public void testQueryDealDetailSnapshot_Success() throws Exception {
        // arrange
        DealDetailSnapshotRequest request = getDefaultRequest();
        Response<DealGroupPBO> response = mock(Response.class);
        DealGroupPBO dealGroupPBO = getDefaultDealGroupPBO();

        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.inSnapshotLogWhiteList(request.getUserId())).thenReturn(true);
        when(dealQueryParallFacade.queryDealGroup(any(), any(EnvCtx.class), any())).thenReturn(response);
        when(response.isSuccess()).thenReturn(true);
        when(response.getResult()).thenReturn(dealGroupPBO);
        // act
        IResponse<String> result = service.queryDealDetailSnapshot(request);

        // assert
        verify(dealQueryParallFacade, times(1)).queryDealGroup(any(), any(EnvCtx.class), any());
        assertTrue("查询应该成功", result.isSuccess());
        assertNotNull("返回结果不应为空", result.getData());
    }

    /**
     * 测试查询失败的场景
     */
    @Test
    public void testQueryDealDetailSnapshot_Fail() throws Exception {
        // arrange
        DealDetailSnapshotRequest request = getDefaultRequest();
        Response<DealGroupPBO> response = mock(Response.class);

        when(dealQueryParallFacade.queryDealGroup(any(), any(EnvCtx.class), any())).thenReturn(response);
        when(response.isSuccess()).thenReturn(false);

        // act
        IResponse<String> result = service.queryDealDetailSnapshot(request);

        // assert
        verify(dealQueryParallFacade, times(1)).queryDealGroup(any(), any(EnvCtx.class), any());
        assertFalse("查询应该失败", result.isSuccess());
    }

    /**
     * 测试查询成功但返回结果为空的场景
     */
    @Test
    public void testQueryDealDetailSnapshot_SuccessButResultNull() throws Exception {
        // arrange
        DealDetailSnapshotRequest request = getDefaultRequest();
        Response<DealGroupPBO> response = mock(Response.class);

        when(dealQueryParallFacade.queryDealGroup(any(), any(EnvCtx.class), any())).thenReturn(response);
        when(response.isSuccess()).thenReturn(true);
        when(response.getResult()).thenReturn(null);

        // act
        IResponse<String> result = service.queryDealDetailSnapshot(request);

        // assert
        verify(dealQueryParallFacade, times(1)).queryDealGroup(any(), any(EnvCtx.class), any());
        assertFalse("查询应该失败", result.isSuccess());
        assertNull("返回结果应为空", result.getData());
    }

    /**
     * 测试查询过程中发生异常的场景
     */
    @Test
    public void testQueryDealDetailSnapshot_Exception() throws Exception {
        // arrange
        DealDetailSnapshotRequest request = getDefaultRequest();

        when(dealQueryParallFacade.queryDealGroup(any(), any(EnvCtx.class), any())).thenThrow(new RuntimeException("模拟异常"));

        // act
        IResponse<String> result = service.queryDealDetailSnapshot(request);

        // assert
        verify(dealQueryParallFacade, times(1)).queryDealGroup(any(), any(EnvCtx.class), any());
        assertFalse("查询应该失败", result.isSuccess());
    }

    /**
     * 测试图文详情为空的情况
     */
    @Test
    public void testQueryDealImageTextDetailSnapshot_ImageTextDetailIsNull() throws Exception {
        DealImageTextDetailSnapshotRequest request = getDefaultDealImageTextDetailSnapshotRequest();
        when(dealDetailFacade.queryImageText(any(DealImageTextDetailReq.class), any(EnvCtx.class))).thenReturn(null);

        IResponse<String> result = service.queryDealImageTextDetailSnapshot(request);

        verify(dealDetailFacade, times(1)).queryImageText(any(DealImageTextDetailReq.class), any(EnvCtx.class));
        assertEquals("图文详情为空时，返回数据应为空字符串", "", result.getData());
        assertEquals("图文详情为空时，返回消息应为'图文详情为空'","图文详情为空", result.getMsg());
    }

    /**
     * 测试查询图文详情成功的情况
     */
    @Test
    public void testQueryDealImageTextDetailSnapshot_Success() throws Exception {
        DealImageTextDetailSnapshotRequest request = getDefaultDealImageTextDetailSnapshotRequest();
        ImageTextDetailPBO imageTextDetailPBO = new ImageTextDetailPBO();
        when(dealDetailFacade.queryImageText(any(DealImageTextDetailReq.class), any(EnvCtx.class))).thenReturn(imageTextDetailPBO);

        IResponse<String> result = service.queryDealImageTextDetailSnapshot(request);

        verify(dealDetailFacade, times(1)).queryImageText(any(DealImageTextDetailReq.class), any(EnvCtx.class));
        assertEquals("成功查询图文详情时，返回数据应为图文详情的JSON字符串", result.getData(), JsonUtils.toJson(imageTextDetailPBO));
    }

    private DealImageTextDetailSnapshotRequest getDefaultDealImageTextDetailSnapshotRequest() {
        DealImageTextDetailSnapshotRequest request = new DealImageTextDetailSnapshotRequest();
        request.setDealGroupId(1020903169L);
        request.setClientTypeEnum(ClientTypeEnum.mt_mainApp_ios);
        request.setRequestSource("trade_snapshot");
        return request;
    }

    private DealDetailSnapshotRequest getDefaultRequest() {
        DealDetailSnapshotRequest request = new DealDetailSnapshotRequest();
        request.setDealGroupId(1020903169L);
        request.setShopId(1588581L);
        request.setCityId(10);
        request.setClientTypeEnum(ClientTypeEnum.mt_mainApp_ios);
        request.setRequestSource("trade_snapshot");
        request.setUserId(5094296096L);
        request.setUnionId("f0e31c3c6fb94fa69ff138d4bb3f8302a169827617007144504");
        request.setAppVersion("12.11.400");
        return request;
    }

    private DealGroupPBO getDefaultDealGroupPBO() {
        DealGroupPBO dealGroupPBO = new DealGroupPBO();
        // 非结构化团购详情
        List<Pair> structedDetails = new ArrayList<>();
        structedDetails.add(new Pair("1", "测试", 2));
        dealGroupPBO.setStructedDetails(structedDetails);
        // 结构化购买须知
        PnPurchaseNoteDTO pnPurchaseNoteDTO = new PnPurchaseNoteDTO();
        pnPurchaseNoteDTO.setPnTitle("购买须知");
        dealGroupPBO.setHitStructuredPurchaseNote(true);
        dealGroupPBO.setPnPurchaseNoteDTO(pnPurchaseNoteDTO);
        // 款式
        ExhibitContentDTO exhibitContentDTO = new ExhibitContentDTO();
        exhibitContentDTO.setTitle("款式");
        dealGroupPBO.setExhibitContents(exhibitContentDTO);
        // 导购模块
        DztgHighlightsModule dztgHighlightsModule = new DztgHighlightsModule();
        dztgHighlightsModule.setStyle("simple");
        dealGroupPBO.setHighlightsModule(dztgHighlightsModule);
        // 头图、视频
        ContentPBO contentPBO = new ContentPBO(0, "文本");
        List<ContentPBO> dealContents = Collections.singletonList(contentPBO);
        dealGroupPBO.setDealContents(dealContents);
        // 限购条
        Guarantee limit = new Guarantee();
        limit.setText("限购");
        List<Guarantee> limitExtend = Collections.singletonList(limit);
        dealGroupPBO.setLimitsExtend(limitExtend);
        dealGroupPBO.setLimits(Collections.singletonList("限购"));
        // 须知
        dealGroupPBO.setReminderInfo(Collections.singletonList("须知"));
        Guarantee reminder = new Guarantee();
        reminder.setText("须知");
        List<Guarantee> reminderExtend = Collections.singletonList(reminder);
        dealGroupPBO.setReminderExtend(reminderExtend);
        // 保障
        Guarantee guarantee = new Guarantee();
        guarantee.setText("保障");
        List<Guarantee> guarantees = Collections.singletonList(guarantee);
        dealGroupPBO.setGuarantee(guarantees);
        dealGroupPBO.setFeatures(Collections.singletonList("保障"));
        // 标题
        dealGroupPBO.setTitle("标题");
        // 团单样式
        ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
        moduleConfigsModule.setKey("dealDetail");
        dealGroupPBO.setModuleConfigsModule(moduleConfigsModule);
        return dealGroupPBO;
    }
}
