package com.dianping.mobile.service.impl.url;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.publishcategory.dto.ChannelDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.sankuai.dztheme.deal.dto.IResponse;
import com.sankuai.dztheme.deal.dto.UrlReplaceDto;
import com.sankuai.dztheme.deal.req.UrlReplaceRequest;
import java.lang.reflect.Method;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealUrlReplaceServiceImplBuildDealCtxTest {

    private DealUrlReplaceServiceImpl dealUrlReplaceService = new DealUrlReplaceServiceImpl();

    private IResponse<UrlReplaceDto> invokePrivateMethod(String methodName, Object... args) throws Exception {
        Method method = DealUrlReplaceServiceImpl.class.getDeclaredMethod(methodName, UrlReplaceRequest.class);
        method.setAccessible(true);
        return (IResponse<UrlReplaceDto>) method.invoke(dealUrlReplaceService, args);
    }

    /**
     * Tests the buildDealCtx method under normal conditions.
     */
    @Test
    public void testBuildDealCtxNormal() throws Throwable {
        // Arrange
        String unionId = "testUnionId";
        int dztgClientType = DztgClientTypeEnum.MEITUAN_APP.getCode();
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO(1, 1, new ChannelDTO(1, "test", "test", 1, "test", "test"));
        String appVersion = "1.0.0";
        // Act
        Method buildDealCtxMethod = DealUrlReplaceServiceImpl.class.getDeclaredMethod("buildDealCtx", String.class, int.class, DealGroupChannelDTO.class, String.class);
        buildDealCtxMethod.setAccessible(true);
        DealCtx result = (DealCtx) buildDealCtxMethod.invoke(dealUrlReplaceService, unionId, dztgClientType, channelDTO, appVersion);
        // Assert
        assertNotNull(result);
        assertEquals(unionId, result.getEnvCtx().getUnionId());
        assertEquals(DztgClientTypeEnum.MEITUAN_APP, result.getEnvCtx().getDztgClientTypeEnum());
        assertEquals(channelDTO, result.getChannelDTO());
        assertEquals("0.5.4", result.getMrnVersion());
    }

    /**
     * Tests the buildDealCtx method under exceptional conditions, with null parameters.
     */
    @Test
    public void testBuildDealCtxException() throws Throwable {
        // Arrange
        String unionId = null;
        int dztgClientType = 0;
        DealGroupChannelDTO channelDTO = null;
        String appVersion = null;
        // Act
        Method buildDealCtxMethod = DealUrlReplaceServiceImpl.class.getDeclaredMethod("buildDealCtx", String.class, int.class, DealGroupChannelDTO.class, String.class);
        buildDealCtxMethod.setAccessible(true);
        try {
            buildDealCtxMethod.invoke(dealUrlReplaceService, unionId, dztgClientType, channelDTO, appVersion);
            fail("Expected UnsupportedOperationException to be thrown");
        } catch (Exception e) {
            // Assert
            assertTrue(e.getCause() instanceof UnsupportedOperationException);
            assertEquals("MiniProgramSceneEnum has no code of 0", e.getCause().getMessage());
        }
    }

    @Test
    public void testCheckBadParamDealGroupIdIsZero() throws Throwable {
        UrlReplaceRequest request = new UrlReplaceRequest();
        request.setDealGroupId(0);
        IResponse<UrlReplaceDto> response = invokePrivateMethod("checkBadParam", request);
        assertEquals(500, response.getCode());
        assertEquals("团单id错误", response.getMsg());
    }

    @Test
    public void testCheckBadParamUnionIdIsEmpty() throws Throwable {
        UrlReplaceRequest request = new UrlReplaceRequest();
        request.setDealGroupId(1);
        request.setUnionId("");
        IResponse<UrlReplaceDto> response = invokePrivateMethod("checkBadParam", request);
        assertEquals(500, response.getCode());
        assertEquals("unionId为空", response.getMsg());
    }

    @Test
    public void testCheckBadParamAppVersionIsEmpty() throws Throwable {
        UrlReplaceRequest request = new UrlReplaceRequest();
        request.setDealGroupId(1);
        request.setUnionId("unionId");
        request.setAppVersion("");
        IResponse<UrlReplaceDto> response = invokePrivateMethod("checkBadParam", request);
        assertEquals(500, response.getCode());
        assertEquals("appVersion为空", response.getMsg());
    }

    @Test
    public void testCheckBadParamDztgClientTypeIsInvalid() throws Throwable {
        UrlReplaceRequest request = new UrlReplaceRequest();
        request.setDealGroupId(1);
        request.setUnionId("unionId");
        request.setAppVersion("1.0.0");
        request.setDztgClientType(DztgClientTypeEnum.UNKNOWN.getCode());
        IResponse<UrlReplaceDto> response = invokePrivateMethod("checkBadParam", request);
        assertEquals(500, response.getCode());
        assertEquals("客户端类型不适配", response.getMsg());
    }

    @Test
    public void testCheckBadParamAllParamsAreValid() throws Throwable {
        UrlReplaceRequest request = new UrlReplaceRequest();
        request.setDealGroupId(1);
        request.setUnionId("unionId");
        request.setAppVersion("1.0.0");
        request.setDztgClientType(DztgClientTypeEnum.MEITUAN_APP.getCode());
        IResponse<UrlReplaceDto> response = invokePrivateMethod("checkBadParam", request);
        assertEquals(0, response.getCode());
        assertEquals("success", response.getMsg());
    }
}
