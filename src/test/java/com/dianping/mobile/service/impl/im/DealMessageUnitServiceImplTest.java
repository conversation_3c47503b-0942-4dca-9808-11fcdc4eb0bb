package com.dianping.mobile.service.impl.im;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ImmersiveImageWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ExhibitImageItemVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImageTagVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImageUrlVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageVO;
import com.sankuai.dzim.message.spi.dto.BatchMessageUnitQueryRequest;
import com.sankuai.dzim.message.spi.dto.BatchMessageUnitResponse;
import com.sankuai.dzim.message.spi.dto.BatchMessageUnitUrlQueryRequest;
import com.sankuai.dzim.message.spi.dto.BatchMessageUnitUrlResponse;
import java.util.Arrays;
import java.util.Collections;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealMessageUnitServiceImplTest {

    @InjectMocks
    private DealMessageUnitServiceImpl dealMessageUnitService;

    @Mock
    private ImmersiveImageWrapper immersiveImageWrapper;

    @Mock
    private MapperWrapper mapperWrapper;

    @Mock
    private Cat // Although we're not verifying logs, the mock is kept in case it's used for other purposes in the test context.
    cat;

    /**
     * 测试展示图片ID列表为空的情况
     */
    @Test
    public void testBatchGetMessageUnitExhibitImgIdsEmpty() throws Throwable {
        // arrange
        BatchMessageUnitQueryRequest request = new BatchMessageUnitQueryRequest();
        // act
        BatchMessageUnitResponse response = dealMessageUnitService.batchGetMessageUnit(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("款式Id不能为空", response.getMsg());
    }

    /**
     * 测试展示图片ID列表不为空，但获取店铺ID失败的情况
     */
    @Test
    public void testBatchGetMessageUnitGetShopIdFailed() throws Throwable {
        // arrange
        BatchMessageUnitQueryRequest request = new BatchMessageUnitQueryRequest();
        request.setBizIds(Arrays.asList("1", "2", "3"));
        // act
        BatchMessageUnitResponse response = dealMessageUnitService.batchGetMessageUnit(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("未查询到款式信息", response.getMsg());
    }

    /**
     * 测试展示图片ID列表不为空，获取店铺ID成功，但获取推荐图片信息失败的情况
     */
    @Test
    public void testBatchGetMessageUnitGetRecommendImageFailed() throws Throwable {
        // arrange
        BatchMessageUnitQueryRequest request = new BatchMessageUnitQueryRequest();
        request.setBizIds(Arrays.asList("1", "2", "3"));
        when(immersiveImageWrapper.batchGetStyleImage(any(), any(), anyInt(), anyBoolean(), anyLong(), anyLong(), anyInt())).thenThrow(new RuntimeException());
        // act
        BatchMessageUnitResponse response = dealMessageUnitService.batchGetMessageUnit(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("未查询到款式信息", response.getMsg());
    }

    /**
     * 测试展示图片ID列表不为空，获取店铺ID和推荐图片信息都成功，但处理推荐图片信息生成款式卡片映射失败的情况
     */
    @Test
    public void testBatchGetMessageUnitGetStyleCardMapFailed() throws Throwable {
        // arrange
        BatchMessageUnitQueryRequest request = new BatchMessageUnitQueryRequest();
        request.setBizIds(Arrays.asList("1", "2", "3"));
        ImmersiveImageVO recommendImageVO = new ImmersiveImageVO();
        recommendImageVO.setItems(Collections.emptyList());
        when(immersiveImageWrapper.batchGetStyleImage(any(), any(), anyInt(), anyBoolean(), anyLong(), anyLong(), anyInt())).thenReturn(recommendImageVO);
        // act
        BatchMessageUnitResponse response = dealMessageUnitService.batchGetMessageUnit(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("未查询到款式信息", response.getMsg());
    }

    /**
     * 测试展示图片ID列表不为空，获取店铺ID和推荐图片信息都成功，处理推荐图片信息生成款式卡片映射也成功的情况
     */
    @Test
    public void testBatchGetMessageUnitSuccess() throws Throwable {
        // arrange
        BatchMessageUnitQueryRequest request = new BatchMessageUnitQueryRequest();
        request.setBizIds(Arrays.asList("1", "2", "3"));
        ImmersiveImageVO recommendImageVO = new ImmersiveImageVO();
        ExhibitImageItemVO item = new ExhibitImageItemVO();
        item.setItemId("1");
        item.setTags(Collections.singletonList(new ImageTagVO("纯白", 0)));
        ImageUrlVO imageUrlVO = ImageUrlVO.builder().height(100).width(100).url("https://testimage.jpg").thumbPic("https://testimage.jpg").scale("3:4").imageBestScale("3:4").hotNailStyle(null).build();
        item.setUrls(Collections.singletonList(imageUrlVO));
        item.setName("name");
        recommendImageVO.setItems(Collections.singletonList(item));
        when(immersiveImageWrapper.batchGetStyleImage(any(), any(), anyInt(), anyBoolean(), anyLong(), anyLong(), anyInt())).thenReturn(recommendImageVO);
        // act
        BatchMessageUnitResponse response = dealMessageUnitService.batchGetMessageUnit(request);
        // assert
        assertTrue(response.isSuccess());
        assertNotNull(response.getBizIdAndUnitContentMap());
        assertEquals(1, response.getBizIdAndUnitContentMap().size());
    }

    /**
     * Tests whether the batchGetMessageUnitUrl method correctly returns null.
     */
    @Test
    public void testBatchGetMessageUnitUrl() throws Throwable {
        // Arrange
        BatchMessageUnitUrlQueryRequest request = new BatchMessageUnitUrlQueryRequest();
        // Act
        BatchMessageUnitUrlResponse response = dealMessageUnitService.batchGetMessageUnitUrl(request);
        // Assert
        assertNull(response);
        // Removed the verify(cat, times(1)).logEvent(anyString(), anyString()); as per requirement 2.
    }
}
