package com.dianping.mobile.mapi.dztgdetail.facade.rcf.flash;

import com.dianping.deal.style.DealDetailStyleFlashService;
import com.dianping.deal.style.dto.flash.DealDetailStyleQueryRequest;
import com.dianping.mobile.mapi.dztgdetail.biz.service.cellarservice.DealDetailStructCacheBizService;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FlashDealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FlashFutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealFlashReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.struct.DealDetailStructModuleDo;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.flash.DealDetailStructFlashProcessor;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @create 2024/10/17 17:09
 */
@RunWith(MockitoJUnitRunner.class)
public class DealDetailStructFlashProcessorTest{

    @InjectMocks
    DealDetailStructFlashProcessor dealDetailStructFlashProcessor;
    @Mock
    DealDetailStructCacheBizService dealDetailStructCacheBizService;
    @Mock
    DealDetailStyleFlashService dealDetailStyleFlashService;
    FlashDealCtx ctx = new FlashDealCtx(new EnvCtx());

    @Before
    public void setUp() throws Exception {
        ctx.setFutureCtx(new FlashFutureCtx());
    }
    @Test
    public void prepareTest() {
        // 从缓存获取 通用样式团购详情
        DealFlashReq req = new DealFlashReq();
        Future future = Mockito.mock(Future.class);
//        Mockito.when(dealDetailStructCacheBizService.buildKey(req)).thenReturn("key");
//        Mockito.when(dealDetailStructCacheBizService.getCacheValueFuture("key")).thenReturn(future);
        DealDetailStyleQueryRequest dealDetailStyleQueryRequest = new DealDetailStyleQueryRequest();
//        Mockito.when(dealDetailStyleFlashService.query(dealDetailStyleQueryRequest)).thenReturn(null);

        dealDetailStructFlashProcessor.prepare(ctx);
        Assert.assertNotNull(future);
    }

    @Test
    public void processTest() {
        DealDetailStructModuleDo result = new DealDetailStructModuleDo();
        Future future = Mockito.mock(Future.class);
//        Mockito.when(dealDetailStructCacheBizService.getCacheValueResult(future)).thenReturn(result) ;
        DealDetailStyleQueryRequest request = new DealDetailStyleQueryRequest();
//        Mockito.when(dealDetailStyleFlashService.query(request)).thenReturn(null);
        dealDetailStructFlashProcessor.process(ctx);
        Assert.assertNotNull(future);
    }

}
