package com.dianping.mobile.mapi.dztgdetail.helper;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class UrlHelper_GetAppUrlTest {

    /**
     * Tests the getAppUrl method when shareUrl is null.
     */
    @Test
    public void testGetAppUrlShareUrlIsNull() throws Throwable {
        // arrange
        String shareUrl = null;
        boolean mt = true;
        // act
        String result = UrlHelper.getAppUrl(shareUrl, mt);
        // assert
        assertNull(result);
    }

    /**
     * Tests the getAppUrl method when shareUrl is not null and mt is true.
     */
    @Test
    public void testGetAppUrlMtIsTrue() throws Throwable {
        // arrange
        String shareUrl = "http://www.meituan.com";
        boolean mt = true;
        // act
        String result = UrlHelper.getAppUrl(shareUrl, mt);
        // assert
        assertEquals("imeituan://www.meituan.com/web?url=http%3A%2F%2Fwww.meituan.com", result);
    }

    /**
     * Tests the getAppUrl method when shareUrl is not null and mt is false.
     */
    @Test
    public void testGetAppUrlMtIsFalse() throws Throwable {
        // arrange
        String shareUrl = "http://www.meituan.com";
        boolean mt = false;
        // act
        String result = UrlHelper.getAppUrl(shareUrl, mt);
        // assert
        assertEquals("dianping://web?url=http%3A%2F%2Fwww.meituan.com", result);
    }
}
