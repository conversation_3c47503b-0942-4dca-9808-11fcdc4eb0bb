package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import com.dianping.deal.shop.dto.DealGroupDTO;
import com.dianping.deal.shop.dto.ShopOnlineDealGroup;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.elasticsearch.common.collect.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ShopDealsHelperTest {

    /**
     * 测试removeDuplicateDealGroupDTO0方法，当两个输入的Map都为空时
     */
    @Test
    public void testRemoveDuplicateDealGroupDTO0BothMapsAreNull() {
        // arrange
        Map<Integer, DealGroupDTO> sameDealGroupDTOMap = null;
        Map<Long, Map<Integer, DealGroupDTO>> otherDpShopId2DealGroupMap = null;
        // act
        ShopDealsHelper.removeDuplicateDealGroupDTO0(sameDealGroupDTOMap, otherDpShopId2DealGroupMap);
        // assert
        assertNull(sameDealGroupDTOMap);
        assertNull(otherDpShopId2DealGroupMap);
    }

    /**
     * 测试removeDuplicateDealGroupDTO0方法，当sameDealGroupDTOMap为空，otherDpShopId2DealGroupMap不为空时
     */
    @Test
    public void testRemoveDuplicateDealGroupDTO0SameDealGroupDTOMapIsNull() {
        // arrange
        Map<Integer, DealGroupDTO> sameDealGroupDTOMap = null;
        Map<Long, Map<Integer, DealGroupDTO>> otherDpShopId2DealGroupMap = new HashMap<>();
        otherDpShopId2DealGroupMap.put(1L, new HashMap<>());
        // act
        ShopDealsHelper.removeDuplicateDealGroupDTO0(sameDealGroupDTOMap, otherDpShopId2DealGroupMap);
        // assert
        assertNull(sameDealGroupDTOMap);
        assertNotNull(otherDpShopId2DealGroupMap);
        assertFalse(otherDpShopId2DealGroupMap.isEmpty());
    }

    /**
     * 测试removeDuplicateDealGroupDTO0方法，当sameDealGroupDTOMap不为空，otherDpShopId2DealGroupMap为空时
     */
    @Test
    public void testRemoveDuplicateDealGroupDTO0OtherDpShopId2DealGroupMapIsNull() {
        // arrange
        Map<Integer, DealGroupDTO> sameDealGroupDTOMap = new HashMap<>();
        sameDealGroupDTOMap.put(1, new DealGroupDTO());
        Map<Long, Map<Integer, DealGroupDTO>> otherDpShopId2DealGroupMap = null;
        // act
        ShopDealsHelper.removeDuplicateDealGroupDTO0(sameDealGroupDTOMap, otherDpShopId2DealGroupMap);
        // assert
        assertNotNull(sameDealGroupDTOMap);
        assertNull(otherDpShopId2DealGroupMap);
    }

    /**
     * 测试removeDuplicateDealGroupDTO0方法，当sameDealGroupDTOMap和otherDpShopId2DealGroupMap都不为空，但是sameDealGroupDTOMap中的key在otherDpShopId2DealGroupMap的所有子Map中都不存在时
     */
    @Test
    public void testRemoveDuplicateDealGroupDTO0KeyNotExistsInSubMaps() {
        // arrange
        Map<Integer, DealGroupDTO> sameDealGroupDTOMap = new HashMap<>();
        sameDealGroupDTOMap.put(1, new DealGroupDTO());
        Map<Long, Map<Integer, DealGroupDTO>> otherDpShopId2DealGroupMap = new HashMap<>();
        otherDpShopId2DealGroupMap.put(1L, new HashMap<>());
        // act
        ShopDealsHelper.removeDuplicateDealGroupDTO0(sameDealGroupDTOMap, otherDpShopId2DealGroupMap);
        // assert
        assertNotNull(sameDealGroupDTOMap);
        assertNotNull(otherDpShopId2DealGroupMap);
        assertFalse(otherDpShopId2DealGroupMap.get(1L).containsKey(1));
    }

    /**
     * 测试removeDuplicateDealGroupDTO0方法，当sameDealGroupDTOMap和otherDpShopId2DealGroupMap都不为空，sameDealGroupDTOMap中的某个key在otherDpShopId2DealGroupMap的某个子Map中存在时
     */
    @Test
    public void testRemoveDuplicateDealGroupDTO0KeyExistsInSubMaps() {
        // arrange
        Map<Integer, DealGroupDTO> sameDealGroupDTOMap = new HashMap<>();
        sameDealGroupDTOMap.put(1, new DealGroupDTO());
        Map<Long, Map<Integer, DealGroupDTO>> otherDpShopId2DealGroupMap = new HashMap<>();
        Map<Integer, DealGroupDTO> subMap = new HashMap<>();
        subMap.put(1, new DealGroupDTO());
        otherDpShopId2DealGroupMap.put(1L, subMap);
        // act
        ShopDealsHelper.removeDuplicateDealGroupDTO0(sameDealGroupDTOMap, otherDpShopId2DealGroupMap);
        // assert
        assertNotNull(sameDealGroupDTOMap);
        assertNotNull(otherDpShopId2DealGroupMap);
        assertFalse(otherDpShopId2DealGroupMap.get(1L).containsKey(1));
    }

    /**
     * 测试dpShopId2DealGroupMap为空的情况
     */
    @Test
    public void testGetOtherShopOnlineDealGroup0_MapIsNull() throws Throwable {
        // arrange
        Map<Long, ShopOnlineDealGroup> dpShopId2DealGroupMap = null;
        long dpShopIdLong = 1L;
        // act
        Map<Long, Map<Integer, DealGroupDTO>> result = ShopDealsHelper.getOtherShopOnlineDealGroup0(dpShopId2DealGroupMap, dpShopIdLong);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试dpShopId2DealGroupMap包含dpShopIdLong的情况
     */
    @Test
    public void testGetOtherShopOnlineDealGroup0_MapContainsDpShopIdLong() throws Throwable {
        // arrange
        Map<Long, ShopOnlineDealGroup> dpShopId2DealGroupMap = new HashMap<>();
        long dpShopIdLong = 1L;
        dpShopId2DealGroupMap.put(dpShopIdLong, new ShopOnlineDealGroup());
        // act
        Map<Long, Map<Integer, DealGroupDTO>> result = ShopDealsHelper.getOtherShopOnlineDealGroup0(dpShopId2DealGroupMap, dpShopIdLong);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试dpShopId2DealGroupMap不包含dpShopIdLong，且所有的ShopOnlineDealGroup都为空或其dealGroups为空的情况
     */
    @Test
    public void testGetOtherShopOnlineDealGroup0_AllShopOnlineDealGroupIsNull() throws Throwable {
        // arrange
        Map<Long, ShopOnlineDealGroup> dpShopId2DealGroupMap = new HashMap<>();
        long dpShopIdLong = 1L;
        dpShopId2DealGroupMap.put(2L, null);
        dpShopId2DealGroupMap.put(3L, new ShopOnlineDealGroup());
        // act
        Map<Long, Map<Integer, DealGroupDTO>> result = ShopDealsHelper.getOtherShopOnlineDealGroup0(dpShopId2DealGroupMap, dpShopIdLong);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试dpShopId2DealGroupMap不包含dpShopIdLong，且存在不为空的ShopOnlineDealGroup和dealGroups的情况
     */
    @Test
    public void testGetOtherShopOnlineDealGroup0_NotNullShopOnlineDealGroupAndDealGroups() throws Throwable {
        // arrange
        Map<Long, ShopOnlineDealGroup> dpShopId2DealGroupMap = new HashMap<>();
        long dpShopIdLong = 1L;
        ShopOnlineDealGroup shopOnlineDealGroup = new ShopOnlineDealGroup();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDealGroupId(1);
        shopOnlineDealGroup.setDealGroups(List.of(dealGroupDTO));
        dpShopId2DealGroupMap.put(2L, shopOnlineDealGroup);
        // act
        Map<Long, Map<Integer, DealGroupDTO>> result = ShopDealsHelper.getOtherShopOnlineDealGroup0(dpShopId2DealGroupMap, dpShopIdLong);
        // assert
        assertEquals(1, result.size());
        assertTrue(result.containsKey(2L));
        assertEquals(1, result.get(2L).size());
        assertTrue(result.get(2L).containsKey(1));
    }

    /**
     * 测试tempMap为空的情况
     */
    @Test
    public void testGetSameShopOnlineDealGroupTempMapIsNull() {
        Map<Integer, DealGroupDTO> result = ShopDealsHelper.getSameShopOnlineDealGroup(null, 1L);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试tempMap不为空，但shopId对应的ShopOnlineDealGroup对象为null的情况
     */
    @Test
    public void testGetSameShopOnlineDealGroupShopOnlineDealGroupIsNull() {
        Map<Long, ShopOnlineDealGroup> tempMap = new HashMap<>();
        tempMap.put(1L, null);
        Map<Integer, DealGroupDTO> result = ShopDealsHelper.getSameShopOnlineDealGroup(tempMap, 1L);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试tempMap不为空，shopId对应的ShopOnlineDealGroup对象不为null，但其dealGroups属性为空的情况
     */
    @Test
    public void testGetSameShopOnlineDealGroupDealGroupsIsNull() {
        ShopOnlineDealGroup shopOnlineDealGroup = new ShopOnlineDealGroup();
        shopOnlineDealGroup.setDealGroups(null);
        Map<Long, ShopOnlineDealGroup> tempMap = new HashMap<>();
        tempMap.put(1L, shopOnlineDealGroup);
        Map<Integer, DealGroupDTO> result = ShopDealsHelper.getSameShopOnlineDealGroup(tempMap, 1L);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试tempMap不为空，shopId对应的ShopOnlineDealGroup对象不为null，其dealGroups属性不为空，但dealGroups中的DealGroupDTO对象为null的情况
     */
    @Test
    public void testGetSameShopOnlineDealGroupDealGroupDTOIsNull() {
        ShopOnlineDealGroup shopOnlineDealGroup = new ShopOnlineDealGroup();
        shopOnlineDealGroup.setDealGroups(Collections.singletonList(null));
        Map<Long, ShopOnlineDealGroup> tempMap = new HashMap<>();
        tempMap.put(1L, shopOnlineDealGroup);
        Map<Integer, DealGroupDTO> result = ShopDealsHelper.getSameShopOnlineDealGroup(tempMap, 1L);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试tempMap不为空，shopId对应的ShopOnlineDealGroup对象不为null，其dealGroups属性不为空，dealGroups中的DealGroupDTO对象也不为null的情况
     */
    @Test
    public void testGetSameShopOnlineDealGroupDealGroupDTOIsNotNull() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDealGroupId(1);
        ShopOnlineDealGroup shopOnlineDealGroup = new ShopOnlineDealGroup();
        shopOnlineDealGroup.setDealGroups(Collections.singletonList(dealGroupDTO));
        Map<Long, ShopOnlineDealGroup> tempMap = new HashMap<>();
        tempMap.put(1L, shopOnlineDealGroup);
        Map<Integer, DealGroupDTO> result = ShopDealsHelper.getSameShopOnlineDealGroup(tempMap, 1L);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(1));
        assertEquals(dealGroupDTO, result.get(1));
    }

    /**
     * 测试removeDuplicateDealGroupDTO方法，当sameDealGroupDTOMap和otherDealGroupDTOMap都为空时
     */
    @Test
    public void testRemoveDuplicateDealGroupDTO_BothMapsAreNull() throws Throwable {
        // arrange
        Map<Integer, DealGroupDTO> sameDealGroupDTOMap = null;
        Map<Integer, DealGroupDTO> otherDealGroupDTOMap = null;
        // act
        ShopDealsHelper.removeDuplicateDealGroupDTO(sameDealGroupDTOMap, otherDealGroupDTOMap);
        // assert
        assertEquals(null, sameDealGroupDTOMap);
        assertEquals(null, otherDealGroupDTOMap);
    }

    /**
     * 测试removeDuplicateDealGroupDTO方法，当sameDealGroupDTOMap为空，otherDealGroupDTOMap不为空时
     */
    @Test
    public void testRemoveDuplicateDealGroupDTO_SameDealGroupDTOMapIsNull() throws Throwable {
        // arrange
        Map<Integer, DealGroupDTO> sameDealGroupDTOMap = null;
        Map<Integer, DealGroupDTO> otherDealGroupDTOMap = new HashMap<>();
        otherDealGroupDTOMap.put(1, new DealGroupDTO());
        // act
        ShopDealsHelper.removeDuplicateDealGroupDTO(sameDealGroupDTOMap, otherDealGroupDTOMap);
        // assert
        assertEquals(null, sameDealGroupDTOMap);
        assertEquals(1, otherDealGroupDTOMap.size());
    }

    /**
     * 测试removeDuplicateDealGroupDTO方法，当sameDealGroupDTOMap不为空，otherDealGroupDTOMap为空时
     */
    @Test
    public void testRemoveDuplicateDealGroupDTO_OtherDealGroupDTOMapIsNull() throws Throwable {
        // arrange
        Map<Integer, DealGroupDTO> sameDealGroupDTOMap = new HashMap<>();
        sameDealGroupDTOMap.put(1, new DealGroupDTO());
        Map<Integer, DealGroupDTO> otherDealGroupDTOMap = null;
        // act
        ShopDealsHelper.removeDuplicateDealGroupDTO(sameDealGroupDTOMap, otherDealGroupDTOMap);
        // assert
        assertEquals(1, sameDealGroupDTOMap.size());
        assertEquals(null, otherDealGroupDTOMap);
    }

    /**
     * 测试removeDuplicateDealGroupDTO方法，当sameDealGroupDTOMap和otherDealGroupDTOMap都不为空，且otherDealGroupDTOMap中有sameDealGroupDTOMap中相同的键时
     */
    @Test
    public void testRemoveDuplicateDealGroupDTO_BothMapsAreNotNullAndHaveSameKey() throws Throwable {
        // arrange
        Map<Integer, DealGroupDTO> sameDealGroupDTOMap = new HashMap<>();
        sameDealGroupDTOMap.put(1, new DealGroupDTO());
        Map<Integer, DealGroupDTO> otherDealGroupDTOMap = new HashMap<>();
        otherDealGroupDTOMap.put(1, new DealGroupDTO());
        // act
        ShopDealsHelper.removeDuplicateDealGroupDTO(sameDealGroupDTOMap, otherDealGroupDTOMap);
        // assert
        assertEquals(1, sameDealGroupDTOMap.size());
        assertEquals(0, otherDealGroupDTOMap.size());
    }

    /**
     * 测试removeDuplicateDealGroupDTO方法，当sameDealGroupDTOMap和otherDealGroupDTOMap都不为空，且otherDealGroupDTOMap中没有sameDealGroupDTOMap中相同的键时
     */
    @Test
    public void testRemoveDuplicateDealGroupDTO_BothMapsAreNotNullAndHaveDifferentKey() throws Throwable {
        // arrange
        Map<Integer, DealGroupDTO> sameDealGroupDTOMap = new HashMap<>();
        sameDealGroupDTOMap.put(1, new DealGroupDTO());
        Map<Integer, DealGroupDTO> otherDealGroupDTOMap = new HashMap<>();
        otherDealGroupDTOMap.put(2, new DealGroupDTO());
        // act
        ShopDealsHelper.removeDuplicateDealGroupDTO(sameDealGroupDTOMap, otherDealGroupDTOMap);
        // assert
        assertEquals(1, sameDealGroupDTOMap.size());
        assertEquals(1, otherDealGroupDTOMap.size());
    }
}
