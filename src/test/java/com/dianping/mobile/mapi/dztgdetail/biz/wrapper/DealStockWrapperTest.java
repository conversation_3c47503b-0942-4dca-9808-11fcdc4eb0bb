package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.stock.DealStockQueryService;
import com.dianping.deal.stock.dto.ProductStock;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealStockWrapperTest {

    @InjectMocks
    private DealStockWrapper dealStockWrapper;

    @Mock
    private DealStockQueryService dealStockQueryServiceFuture;

    @Mock
    private Future<Map<Integer, ProductStock>> future;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testMGetProductStocksWhenDpDealIdsIsNull() throws Throwable {
        // No need to arrange mocks for null input, just act and assert
        Map<Integer, ProductStock> result = dealStockWrapper.mGetProductStocks(null);
        assertNull(result);
    }

    @Test
    public void testMGetProductStocksWhenPreDealStocksThrowsException() throws Throwable {
        // arrange
        when(dealStockQueryServiceFuture.mGetProductStocks(Arrays.asList(1, 2, 3))).thenThrow(new RuntimeException());
        // act
        Map<Integer, ProductStock> result = dealStockWrapper.mGetProductStocks(Arrays.asList(1, 2, 3));
        // assert
        assertNull(result);
    }

    /**
     * 测试 dpDealIds 为空的情况
     */
    @Test
    public void testPreDealStocksWithEmptyDpDealIds() throws Throwable {
        // arrange
        List<Integer> dpDealIds = null;
        // act
        Future result = dealStockWrapper.preDealStocks(dpDealIds);
        // assert
        assertNull(result);
    }

    /**
     * 测试 dpDealIds 不为空，且 mGetProductStocks 方法正常执行的情况
     */
    @Test
    @Ignore
    public void testPreDealStocksWithNonEmptyDpDealIdsAndNormalMGetProductStocks() throws Throwable {
        // arrange
        List<Integer> dpDealIds = Arrays.asList(1, 2, 3);
        // act
        Future result = dealStockWrapper.preDealStocks(dpDealIds);
        // assert
        assertTrue(result == FutureFactory.getFuture());
        verify(dealStockQueryServiceFuture, times(1)).mGetProductStocks(dpDealIds);
    }

    /**
     * 测试 dpDealIds 不为空，且 mGetProductStocks 方法抛出异常的情况
     */
    @Test
    public void testPreDealStocksWithNonEmptyDpDealIdsAndExceptionInMGetProductStocks() throws Throwable {
        // arrange
        List<Integer> dpDealIds = Arrays.asList(1, 2, 3);
        doThrow(new RuntimeException()).when(dealStockQueryServiceFuture).mGetProductStocks(dpDealIds);
        // act
        Future result = dealStockWrapper.preDealStocks(dpDealIds);
        // assert
        assertNull(result);
        verify(dealStockQueryServiceFuture, times(1)).mGetProductStocks(dpDealIds);
    }
}
