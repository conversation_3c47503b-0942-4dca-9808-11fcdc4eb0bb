package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DzDealThemeWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.HaimaWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PriceRangeQueryWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.SkuWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetDealTinyInfoRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealLowPriceItemEntranceVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealPriceTrendVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealTinyInfoVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PriceTrendVO;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.dianping.vc.enums.VCPlatformEnum;
import com.sankuai.dztheme.deal.dto.DealProductAttrDTO;
import com.sankuai.dztheme.deal.dto.DealProductDTO;
import com.sankuai.dztheme.deal.dto.DealProductSaleDTO;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.dztheme.deal.res.DealSkuSummaryDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.tpfun.skuoperationapi.price.dto.PriceRangeDO;
import com.sankuai.tpfun.skuoperationapi.price.dto.PriceRangeItemDTO;
import com.sankuai.tpfun.skuoperationapi.price.dto.SubjectDTO;
import com.sankuai.tpfun.skuoperationapi.price.dto.enums.PriceRangeItemTypeEnum;
import com.sankuai.tpfun.skuoperationapi.price.dto.response.BatchPriceRangeInfoResponse;
import com.sankuai.tpfun.skuoperationapi.price.dto.response.Response;
import com.sankuai.tpfun.skuoperationapi.price.service.PriceRangeQueryService;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealTinyInfoFacade_GetDealPriceTrendTest {

    @InjectMocks
    private DealTinyInfoFacade dealTinyInfoFacade;

    @Mock
    private DzDealThemeWrapper dzDealThemeWrapper;

    @Mock
    private SkuWrapper skuWrapper;

    @Mock
    private HaimaWrapper haimaWrapper;

    private static Method isShopInBlackListMethod;

    @Mock
    private PriceRangeQueryWrapper priceRangeQueryWrapper;

    @Mock
    private PriceRangeQueryService priceRangeQueryService;

    @Mock
    private DouHuService douHuService;

    @Mock
    private PriceTrendVO priceTrendVO;

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    @Before
    public void setUp() throws Exception {
        isShopInBlackListMethod = DealTinyInfoFacade.class.getDeclaredMethod("isShopInBlackList", String.class, boolean.class);
        isShopInBlackListMethod.setAccessible(true);
        // Assuming the setup for LionConfigUtils.isComparePriceShopBlackList to return true
        // is achieved through configuration or environment settings, which is outside the scope of this solution.
    }

    private boolean invokeIsShopInBlackList(String shopId, boolean isMt) throws Exception {
        return (boolean) isShopInBlackListMethod.invoke(dealTinyInfoFacade, shopId, isMt);
    }

    private DealTinyInfoVO invokePrivateGetDealResult(DealProductResult dealProductResult, Integer skuId, String pageSource) throws Exception {
        Method method = DealTinyInfoFacade.class.getDeclaredMethod("getDealResult", DealProductResult.class, Integer.class, String.class);
        method.setAccessible(true);
        return (DealTinyInfoVO) method.invoke(dealTinyInfoFacade, dealProductResult, skuId, pageSource);
    }

    private List<PriceTrendVO> createPriceTrendVOList(int size, Date date) {
        List<PriceTrendVO> list = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            PriceTrendVO vo = new PriceTrendVO();
            vo.setDate(sdf.format(date));
            vo.setPrice(new BigDecimal("100"));
            list.add(vo);
        }
        return list;
    }

    private List<PriceTrendVO> invokeGetPriceTrends(List<PriceTrendVO> dealPriceTrend, Date fewDaysBeforeDate) throws Exception {
        Method method = DealTinyInfoFacade.class.getDeclaredMethod("getPriceTrends", List.class, Date.class);
        method.setAccessible(true);
        return (List<PriceTrendVO>) method.invoke(dealTinyInfoFacade, dealPriceTrend, fewDaysBeforeDate);
    }

    private DealCtx invokePrivateMethod(DealTinyInfoFacade facade, String methodName, EnvCtx envCtx, int categoryId) throws Throwable {
        try {
            Method method = DealTinyInfoFacade.class.getDeclaredMethod(methodName, EnvCtx.class, int.class);
            method.setAccessible(true);
            return (DealCtx) method.invoke(facade, envCtx, categoryId);
        } catch (Exception e) {
            throw e.getCause();
        }
    }

    /**
     * Tests the scenario when the request is invalid.
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGetDealPriceTrendInvalidRequest() throws Throwable {
        // Arrange
        GetDealTinyInfoRequest request = new GetDealTinyInfoRequest();
        request.setDealGroupId("");
        request.setShopId("123");
        EnvCtx envCtx = new EnvCtx();
        // Act
        dealTinyInfoFacade.getDealPriceTrend(request, envCtx);
    }

    /**
     * Tests the scenario when pre-querying deal product fails.
     */
    @Test(expected = RuntimeException.class)
    public void testGetDealPriceTrendPreQueryFailed() throws Throwable {
        // Arrange
        GetDealTinyInfoRequest request = new GetDealTinyInfoRequest();
        request.setDealGroupId("1");
        request.setShopId("123");
        request.setPageSource("deal");
        EnvCtx envCtx = new EnvCtx();
        when(dzDealThemeWrapper.preQueryDealProduct(any())).thenThrow(new RuntimeException());
        // Act
        dealTinyInfoFacade.getDealPriceTrend(request, envCtx);
    }

    /**
     * Tests the scenario when getting deal product result fails.
     */
    @Test(expected = RuntimeException.class)
    public void testGetDealPriceTrendGetResultFailed() throws Throwable {
        // Arrange
        GetDealTinyInfoRequest request = new GetDealTinyInfoRequest();
        request.setDealGroupId("1");
        request.setShopId("123");
        request.setPageSource("deal");
        EnvCtx envCtx = new EnvCtx();
        when(dzDealThemeWrapper.preQueryDealProduct(any())).thenReturn(null);
        when(dzDealThemeWrapper.getFutureResult(Mockito.<Future>any())).thenThrow(new RuntimeException());
        // Act
        dealTinyInfoFacade.getDealPriceTrend(request, envCtx);
    }

    @Test
    public void testIsShopInBlackList_HaimaReturnsTrue_LionReturnsTrue() throws Throwable {
        when(haimaWrapper.isBlackListShop(anyString(), anyBoolean())).thenReturn(true);
        assertTrue(invokeIsShopInBlackList("123", true));
    }

    @Test
    public void testIsShopInBlackList_HaimaReturnsTrue_LionReturnsFalse() throws Throwable {
        when(haimaWrapper.isBlackListShop(anyString(), anyBoolean())).thenReturn(true);
        assertTrue(invokeIsShopInBlackList("123", true));
    }

    @Test
    public void testIsShopInBlackList_HaimaReturnsFalse_LionReturnsFalse() throws Throwable {
        assertFalse(invokeIsShopInBlackList("123", true));
    }

    @Test
    public void testGetTopTwentyPercentPriceNormal() throws Throwable {
        // arrange
        int dpDealId = 1;
        long dpShopId = 1L;
        PriceRangeDO priceRangeDO = new PriceRangeDO();
        Map<String, String> extra = new HashMap<>();
        extra.put("topTwentyPercentPrice", "100");
        priceRangeDO.setExtra(extra);
        PriceRangeItemDTO priceRangeItemDTO = new PriceRangeItemDTO();
        priceRangeItemDTO.setPriceRangeItemType(PriceRangeItemTypeEnum.SHOPPING_CART_SPACE_PRICE_RANGE_ITEM.getCode());
        priceRangeItemDTO.setPriceRangeDO(priceRangeDO);
        SubjectDTO subjectDTO = new SubjectDTO();
        subjectDTO.setDpShopId(dpShopId);
        subjectDTO.setProductId((long) dpDealId);
        Response<BatchPriceRangeInfoResponse> response = new Response<>();
        response.setSuccess(true);
        BatchPriceRangeInfoResponse batchPriceRangeInfoResponse = new BatchPriceRangeInfoResponse();
        Map<SubjectDTO, List<PriceRangeItemDTO>> subjectDTO2PriceRangesMap = new HashMap<>();
        subjectDTO2PriceRangesMap.put(subjectDTO, Collections.singletonList(priceRangeItemDTO));
        batchPriceRangeInfoResponse.setSubjectDTO2PriceRangesMap(subjectDTO2PriceRangesMap);
        response.setResult(batchPriceRangeInfoResponse);
        when(priceRangeQueryWrapper.preQueryPriceRange(dpDealId, dpShopId)).thenReturn(mock(Future.class));
        when(priceRangeQueryWrapper.getFutureResult(any(Future.class))).thenReturn(response);
        // act
        Method method = DealTinyInfoFacade.class.getDeclaredMethod("getTopTwentyPercentPrice", int.class, long.class);
        method.setAccessible(true);
        String result = (String) method.invoke(dealTinyInfoFacade, dpDealId, dpShopId);
        // assert
        assertEquals("100", result);
    }

    @Test
    public void testGetTopTwentyPercentPriceException() throws Throwable {
        // arrange
        int dpDealId = 1;
        long dpShopId = 1L;
        Response<BatchPriceRangeInfoResponse> response = new Response<>();
        response.setSuccess(false);
        when(priceRangeQueryWrapper.preQueryPriceRange(dpDealId, dpShopId)).thenReturn(mock(Future.class));
        when(priceRangeQueryWrapper.getFutureResult(any(Future.class))).thenReturn(response);
        // act
        Method method = DealTinyInfoFacade.class.getDeclaredMethod("getTopTwentyPercentPrice", int.class, long.class);
        method.setAccessible(true);
        String result = (String) method.invoke(dealTinyInfoFacade, dpDealId, dpShopId);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试 dealGroupId 为空的情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGetDealTinyInfoDealGroupIdIsEmpty() throws Throwable {
        // arrange
        GetDealTinyInfoRequest request = new GetDealTinyInfoRequest();
        request.setDealGroupId("");
        request.setPageSource("deal");
        request.setShopId("12345");
        EnvCtx envCtx = new EnvCtx();
        // act
        dealTinyInfoFacade.getDealTinyInfo(request, envCtx);
        // assert
        // 预期抛出 IllegalArgumentException
    }

    /**
     * 测试 dealGroupId 小于等于 0 的情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGetDealTinyInfoDealGroupIdIsZero() throws Throwable {
        // arrange
        GetDealTinyInfoRequest request = new GetDealTinyInfoRequest();
        request.setDealGroupId("0");
        request.setPageSource("deal");
        request.setShopId("12345");
        EnvCtx envCtx = new EnvCtx();
        // act
        dealTinyInfoFacade.getDealTinyInfo(request, envCtx);
        // assert
        // 预期抛出 IllegalArgumentException
    }

    /**
     * 测试 pageSource 为空的情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGetDealTinyInfoPageSourceIsEmpty() throws Throwable {
        // arrange
        GetDealTinyInfoRequest request = new GetDealTinyInfoRequest();
        request.setDealGroupId("123");
        request.setPageSource("");
        request.setShopId("12345");
        EnvCtx envCtx = new EnvCtx();
        // act
        dealTinyInfoFacade.getDealTinyInfo(request, envCtx);
        // assert
        // 预期抛出 IllegalArgumentException
    }

    /**
     * 测试 queryDealTinyInfo 返回 null 的情况
     */
    @Test
    public void testGetDealTinyInfoQueryDealTinyInfoReturnsNull() throws Throwable {
        // arrange
        GetDealTinyInfoRequest request = new GetDealTinyInfoRequest();
        request.setDealGroupId("123");
        request.setPageSource("deal");
        request.setShopId("12345");
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(VCPlatformEnum.MT.getType());
        DealSkuSummaryDTO skuSummary = new DealSkuSummaryDTO();
        skuSummary.setDefaultSkuId(1L);
        when(dzDealThemeWrapper.getFutureResult(FutureFactory.getFuture())).thenReturn(null);
        when(skuWrapper.getSkuSummaryByDealId(anyLong(), any())).thenReturn(skuSummary);
        // act
        DealTinyInfoVO result = dealTinyInfoFacade.getDealTinyInfo(request, envCtx);
        // assert
        assertNull(result);
    }

    /**
     * 测试 isRightExpResult 方法，当 douHuService.getRepurchaseShelfExpResult 返回 "c" 时，应返回 true
     */
    @Test
    public void testIsRightExpResultReturnTrue() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        when(douHuService.getRepurchaseShelfExpResult(envCtx)).thenReturn("c");
        // act
        Method method = DealTinyInfoFacade.class.getDeclaredMethod("isRightExpResult", EnvCtx.class);
        method.setAccessible(true);
        boolean result = (boolean) method.invoke(dealTinyInfoFacade, envCtx);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 isRightExpResult 方法，当 douHuService.getRepurchaseShelfExpResult 返回非 "c" 的其他值时，应返回 false
     */
    @Test
    public void testIsRightExpResultReturnFalse() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        when(douHuService.getRepurchaseShelfExpResult(envCtx)).thenReturn("not c");
        // act
        Method method = DealTinyInfoFacade.class.getDeclaredMethod("isRightExpResult", EnvCtx.class);
        method.setAccessible(true);
        boolean result = (boolean) method.invoke(dealTinyInfoFacade, envCtx);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 dealProductResult 为空的情况
     */
    @Test
    public void testGetDealResult_DealProductResultIsNull() throws Throwable {
        // arrange
        DealProductResult dealProductResult = null;
        Integer skuId = 123;
        String pageSource = "deal";
        // act
        DealTinyInfoVO result = invokePrivateGetDealResult(dealProductResult, skuId, pageSource);
        // assert
        assertNull(result);
    }

    /**
     * 测试 dealProductResult.getDeals() 为空的情况
     */
    @Test
    public void testGetDealResult_DealsIsEmpty() throws Throwable {
        // arrange
        DealProductResult dealProductResult = new DealProductResult();
        dealProductResult.setDeals(Collections.emptyList());
        Integer skuId = 123;
        String pageSource = "deal";
        // act
        DealTinyInfoVO result = invokePrivateGetDealResult(dealProductResult, skuId, pageSource);
        // assert
        assertNull(result);
    }

    /**
     * 测试正常情况，所有字段都正确设置
     */
    @Test
    public void testGetDealResult_NormalCase() throws Throwable {
        // arrange
        DealProductResult dealProductResult = new DealProductResult();
        DealProductDTO dealProductDTO = new DealProductDTO();
        dealProductDTO.setProductId(456);
        dealProductDTO.setName("Test Deal");
        dealProductDTO.setHeadPic("http://example.com/image.jpg");
        dealProductDTO.setOrderUrl("http://example.com/order");
        dealProductDTO.setCategoryId(789);
        DealProductSaleDTO sale = new DealProductSaleDTO();
        sale.setSaleTag("Sale Tag");
        dealProductDTO.setSale(sale);
        DealProductAttrDTO dealSubTitleAttr = new DealProductAttrDTO();
        dealSubTitleAttr.setName("dealSubTitle");
        dealSubTitleAttr.setValue("[\"SubTitle1\",\"SubTitle2\"]");
        DealProductAttrDTO pricePowerTagAttr = new DealProductAttrDTO();
        pricePowerTagAttr.setName("highestPriorityPricePowerTagAttr");
        pricePowerTagAttr.setValue("Price Power Tag");
        DealProductAttrDTO finalPriceAttr = new DealProductAttrDTO();
        finalPriceAttr.setName("finalPriceForTrade");
        finalPriceAttr.setValue("100.00");
        DealProductAttrDTO marketPriceAttr = new DealProductAttrDTO();
        marketPriceAttr.setName("marketPriceForTrade");
        marketPriceAttr.setValue("200.00");
        DealProductAttrDTO discountAttr = new DealProductAttrDTO();
        discountAttr.setName("discountForTrade");
        discountAttr.setValue("50.00");
        List<DealProductAttrDTO> attrs = Arrays.asList(dealSubTitleAttr, pricePowerTagAttr, finalPriceAttr, marketPriceAttr, discountAttr);
        dealProductDTO.setAttrs(attrs);
        dealProductResult.setDeals(Collections.singletonList(dealProductDTO));
        Integer skuId = 123;
        String pageSource = "shopcarselect";
        // act
        DealTinyInfoVO result = invokePrivateGetDealResult(dealProductResult, skuId, pageSource);
        // assert
        assertNotNull(result);
        assertEquals(456, (int) result.getDealGroupId());
        assertEquals("Test Deal", result.getTitle());
        assertEquals("SubTitle1·SubTitle2", result.getSubTitle());
        assertEquals("Sale Tag", result.getSaleTag());
        assertEquals("http://example.com/image.jpg", result.getHeadPic());
        assertEquals("抢购", result.getBtnText());
        assertEquals(123, (int) result.getSkuId());
        assertEquals(Collections.singletonList("Price Power Tag"), result.getPricePowerTag());
        assertEquals("100", result.getFinalPrice());
        assertEquals("200", result.getMarketPrice());
        assertEquals("50.00", result.getDiscount());
        assertEquals(789, (int) result.getCategoryId());
    }

    /**
     * 测试价格力标签为空的情况
     */
    @Test
    public void testGetDealResult_PricePowerTagIsEmpty() throws Throwable {
        // arrange
        DealProductResult dealProductResult = new DealProductResult();
        DealProductDTO dealProductDTO = new DealProductDTO();
        dealProductDTO.setProductId(456);
        dealProductDTO.setName("Test Deal");
        dealProductDTO.setHeadPic("http://example.com/image.jpg");
        dealProductDTO.setOrderUrl("http://example.com/order");
        dealProductDTO.setCategoryId(789);
        DealProductSaleDTO sale = new DealProductSaleDTO();
        sale.setSaleTag("Sale Tag");
        dealProductDTO.setSale(sale);
        DealProductAttrDTO dealSubTitleAttr = new DealProductAttrDTO();
        dealSubTitleAttr.setName("dealSubTitle");
        dealSubTitleAttr.setValue("[\"SubTitle1\",\"SubTitle2\"]");
        DealProductAttrDTO finalPriceAttr = new DealProductAttrDTO();
        finalPriceAttr.setName("finalPriceForTrade");
        finalPriceAttr.setValue("100.00");
        DealProductAttrDTO marketPriceAttr = new DealProductAttrDTO();
        marketPriceAttr.setName("marketPriceForTrade");
        marketPriceAttr.setValue("200.00");
        DealProductAttrDTO discountAttr = new DealProductAttrDTO();
        discountAttr.setName("discountForTrade");
        discountAttr.setValue("50.00");
        List<DealProductAttrDTO> attrs = Arrays.asList(dealSubTitleAttr, finalPriceAttr, marketPriceAttr, discountAttr);
        dealProductDTO.setAttrs(attrs);
        dealProductResult.setDeals(Collections.singletonList(dealProductDTO));
        Integer skuId = 123;
        String pageSource = "shopcarselect";
        // act
        DealTinyInfoVO result = invokePrivateGetDealResult(dealProductResult, skuId, pageSource);
        // assert
        assertNotNull(result);
        assertNull(result.getPricePowerTag());
    }

    @Test
    public void testGetPriceTrendsPricePowerTagOrDealPriceTrendIsNull() throws Throwable {
        List<PriceTrendVO> dealPriceTrend = new ArrayList<>();
        Date fewDaysBeforeDate = new Date();
        List<PriceTrendVO> result = invokeGetPriceTrends(dealPriceTrend, fewDaysBeforeDate);
        assertNull(result);
    }

    @Test
    public void testGetPriceTrendsDealPriceTrendIsEmpty() throws Throwable {
        List<PriceTrendVO> dealPriceTrend = new ArrayList<>();
        Date fewDaysBeforeDate = new Date();
        List<PriceTrendVO> result = invokeGetPriceTrends(dealPriceTrend, fewDaysBeforeDate);
        assertNull(result);
    }

    /**
     * Test the normal scenario where the method is called with valid EnvCtx and categoryId.
     */
    @Test
    public void testBuildDealCtxNormalScenario() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        int categoryId = 123;
        DealTinyInfoFacade facade = new DealTinyInfoFacade();
        // act
        DealCtx result = invokePrivateMethod(facade, "buildDealCtx", envCtx, categoryId);
        // assert
        assertNotNull(result);
        assertEquals(envCtx, result.getEnvCtx());
        assertNotNull(result.getChannelDTO());
        assertEquals(categoryId, result.getChannelDTO().getCategoryId());
    }

    /**
     * Test the scenario where the method is called with a null EnvCtx object.
     */
    @Test
    public void testBuildDealCtxNullEnvCtx() throws Throwable {
        // arrange
        EnvCtx envCtx = null;
        int categoryId = 123;
        DealTinyInfoFacade facade = new DealTinyInfoFacade();
        // act
        DealCtx result = invokePrivateMethod(facade, "buildDealCtx", envCtx, categoryId);
        // assert
        assertNotNull(result);
        // Check that envCtx is not null
        assertNotNull(result.getEnvCtx());
        // Check that envCtx is the default EnvCtx object
        EnvCtx expectedEnvCtx = new EnvCtx();
        // Set the startTime to match the actual value
        expectedEnvCtx.setStartTime(result.getEnvCtx().getStartTime());
        assertEquals(expectedEnvCtx, result.getEnvCtx());
        assertNotNull(result.getChannelDTO());
        assertEquals(categoryId, result.getChannelDTO().getCategoryId());
    }

    /**
     * Test the scenario where the method is called with a negative categoryId.
     */
    @Test
    public void testBuildDealCtxNegativeCategoryId() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        int categoryId = -1;
        DealTinyInfoFacade facade = new DealTinyInfoFacade();
        // act
        DealCtx result = invokePrivateMethod(facade, "buildDealCtx", envCtx, categoryId);
        // assert
        assertNotNull(result);
        assertEquals(envCtx, result.getEnvCtx());
        assertNotNull(result.getChannelDTO());
        assertEquals(categoryId, result.getChannelDTO().getCategoryId());
    }

    /**
     * Test the scenario where the method is called with a categoryId of zero.
     */
    @Test
    public void testBuildDealCtxZeroCategoryId() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        int categoryId = 0;
        DealTinyInfoFacade facade = new DealTinyInfoFacade();
        // act
        DealCtx result = invokePrivateMethod(facade, "buildDealCtx", envCtx, categoryId);
        // assert
        assertNotNull(result);
        assertEquals(envCtx, result.getEnvCtx());
        assertNotNull(result.getChannelDTO());
        assertEquals(categoryId, result.getChannelDTO().getCategoryId());
    }
}
