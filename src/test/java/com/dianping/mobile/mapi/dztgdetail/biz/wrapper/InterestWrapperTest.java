package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.sankuai.interest.core.thrift.remote.api.InterestCalculateService;
import com.sankuai.interest.core.thrift.remote.dto.InterestCalculateRequest;
import com.sankuai.interest.core.thrift.remote.dto.InterestCalculateResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.Future;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @create 2024-11-28-15:31
 */

@RunWith(MockitoJUnitRunner.class)
public class InterestWrapperTest {

    @Mock
    private InterestCalculateService interestCalculateServiceFuture;

    @InjectMocks
    private InterestWrapper interestWrapper;

    /**
     * 测试正常场景
     */
    @Test
    public void testPreInterestCalculateByShopNormal() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setMtLongShopId(12345L);
        Long interestCode = 1L;
        Integer interestSceneId = 1;

        when(interestCalculateServiceFuture.calculate(any())).thenReturn(new InterestCalculateResponse());
        // act
        Future result = interestWrapper.preInterestCalculateByShop(ctx, interestCode, interestSceneId);
        // assert

        verify(interestCalculateServiceFuture, times(1)).calculate(any(InterestCalculateRequest.class));
    }
}
