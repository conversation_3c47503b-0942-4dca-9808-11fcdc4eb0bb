package com.dianping.mobile.mapi.dztgdetail.biz.service.recommend;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.entity.Response;
import com.dianping.martgeneral.recommend.api.enums.RecommendType;
import com.dianping.mobile.mapi.dztgdetail.biz.service.RelatedRecommendService;
import com.dianping.mobile.mapi.dztgdetail.common.enums.EntityTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.RelatedRecommendCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedRecommendReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedRecommendVO;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class CrossShopRecommendHandlerFillTest {

    @Mock
    private LeCrossRecommendHandler leCrossRecommendHandler;

    @Mock
    private RelatedRecommendService relatedRecommendService;

    @InjectMocks
    private CrossShopRecommendHandler crossShopRecommendHandler;

    private EnvCtx createEnvCtx(boolean isMt) {
        EnvCtx envCtx = new EnvCtx();
        if (isMt) {
            // MT client type
            envCtx.setClientType(1);
        } else {
            // DP client type
            envCtx.setClientType(4);
        }
        return envCtx;
    }

    private RelatedRecommendReq createReq() {
        RelatedRecommendReq req = new RelatedRecommendReq();
        req.setDealGroupId(123);
        req.setCityId(1);
        req.setLimit(10);
        req.setStart(0);
        return req;
    }

    private RelatedRecommendCtx createBaseContext(boolean isMt) {
        RelatedRecommendCtx ctx = new RelatedRecommendCtx();
        ctx.setEnvCtx(createEnvCtx(isMt));
        ctx.setReq(createReq());
        RelatedRecommendVO resultVO = new RelatedRecommendVO();
        resultVO.setRecommendItemList(new ArrayList<>());
        ctx.setResult(resultVO);
        return ctx;
    }

    /**
     * Test Product type recommendation case
     */
    @Test
    public void testFill_ProductType() throws Throwable {
        // arrange
        RelatedRecommendCtx ctx = createBaseContext(true);
        Response<RecommendResult<RecommendDTO>> response = new Response<>();
        RecommendResult<RecommendDTO> result = new RecommendResult<>();
        List<RecommendDTO> sortedResult = new ArrayList<>();
        RecommendDTO dto = new RecommendDTO();
        dto.setType(RecommendType.Product);
        dto.setItem("123");
        sortedResult.add(dto);
        result.setSortedResult(sortedResult);
        response.setResult(result);
        when(leCrossRecommendHandler.buildLeCrossRecommendFuture(any())).thenReturn(false);
        when(relatedRecommendService.fillDealGroupInfo(any(), anyList(), anyBoolean())).thenReturn(ctx);
        // act
        RelatedRecommendVO vo = crossShopRecommendHandler.fill(ctx, response);
        // assert
        assertNotNull(vo);
        verify(leCrossRecommendHandler).buildLeCrossRecommendFuture(ctx);
        verify(relatedRecommendService).fillDealGroupInfo(any(), anyList(), anyBoolean());
    }

    /**
     * Test Ad type recommendation with deal entity type
     */
    @Test
    public void testFill_AdTypeWithDealEntity() throws Throwable {
        // arrange
        RelatedRecommendCtx ctx = createBaseContext(false);
        Response<RecommendResult<RecommendDTO>> response = new Response<>();
        RecommendResult<RecommendDTO> result = new RecommendResult<>();
        List<RecommendDTO> sortedResult = new ArrayList<>();
        RecommendDTO dto = new RecommendDTO();
        dto.setType(RecommendType.Ad);
        dto.setItem("123");
        Map<String, Object> bizData = new HashMap<>();
        Map<String, String> adCreative = new HashMap<>();
        adCreative.put("entitytype", String.valueOf(EntityTypeEnum.DEAL.getValue()));
        adCreative.put("entityid", "456");
        bizData.put("adCreative", adCreative);
        dto.setBizData(bizData);
        sortedResult.add(dto);
        result.setSortedResult(sortedResult);
        response.setResult(result);
        when(leCrossRecommendHandler.buildLeCrossRecommendFuture(any())).thenReturn(false);
        when(relatedRecommendService.fillDealGroupInfo(any(), anyList(), anyBoolean())).thenReturn(ctx);
        // act
        RelatedRecommendVO vo = crossShopRecommendHandler.fill(ctx, response);
        // assert
        assertNotNull(vo);
        verify(leCrossRecommendHandler).buildLeCrossRecommendFuture(ctx);
        verify(relatedRecommendService).fillDealGroupInfo(any(), anyList(), anyBoolean());
    }

    /**
     * Test invalid item type case
     */
    @Test
    public void testFill_InvalidItemType() throws Throwable {
        // arrange
        RelatedRecommendCtx ctx = createBaseContext(true);
        Response<RecommendResult<RecommendDTO>> response = new Response<>();
        RecommendResult<RecommendDTO> result = new RecommendResult<>();
        List<RecommendDTO> sortedResult = new ArrayList<>();
        RecommendDTO dto = new RecommendDTO();
        dto.setType(RecommendType.Product);
        dto.setItem("123");
        sortedResult.add(dto);
        result.setSortedResult(sortedResult);
        response.setResult(result);
        when(leCrossRecommendHandler.buildLeCrossRecommendFuture(any())).thenReturn(false);
        when(relatedRecommendService.fillDealGroupInfo(any(), anyList(), anyBoolean())).thenReturn(ctx);
        // act
        RelatedRecommendVO vo = crossShopRecommendHandler.fill(ctx, response);
        // assert
        assertNotNull(vo);
        assertTrue(vo.getRecommendItemList().isEmpty());
    }

    /**
     * Test module config case
     */
    @Test
    public void testFill_WithModuleConfig() throws Throwable {
        // arrange
        RelatedRecommendCtx ctx = createBaseContext(true);
        List<ModuleAbConfig> configs = new ArrayList<>();
        ModuleAbConfig config = new ModuleAbConfig();
        configs.add(config);
        ctx.setModuleAbConfigs(configs);
        ctx.getResult().setModuleAbConfigs(configs);
        Response<RecommendResult<RecommendDTO>> response = new Response<>();
        RecommendResult<RecommendDTO> result = new RecommendResult<>();
        List<RecommendDTO> sortedResult = new ArrayList<>();
        result.setSortedResult(sortedResult);
        response.setResult(result);
        when(leCrossRecommendHandler.buildLeCrossRecommendFuture(any())).thenReturn(false);
        // act
        RelatedRecommendVO vo = crossShopRecommendHandler.fill(ctx, response);
        // assert
        assertNotNull(vo);
        assertNotNull(vo.getModuleAbConfigs());
        assertEquals(1, vo.getModuleAbConfigs().size());
    }
}
