package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.sankuai.douhu.absdk.bean.DouHuRequest;
import com.sankuai.douhu.absdk.bean.DouHuResponse;
import com.sankuai.douhu.absdk.client.DouHuClient;
import com.sankuai.douhu.absdk.enums.ErrorCode;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DouHuBizGetAbExpResultV2Test {

    @Mock
    private DouHuClient douHuClient;

    @Mock
    private DealCtx context;

    @Mock
    private EnvCtx envCtx;

    @InjectMocks
    private DouHuBiz douHuBiz;

    private static final String TEST_MODULE = "test-module";

    private static final String TEST_EXP_ID = "test-exp-id";

    private static final String TEST_UNION_ID = "test-union-id";

    @Before
    public void setUp() {
        when(context.getEnvCtx()).thenReturn(envCtx);
        when(context.isMt()).thenReturn(false);
        when(envCtx.getUnionId()).thenReturn(TEST_UNION_ID);
        // Mock module to expId mapping
        Map<String, String> moduleExpMap = new HashMap<>();
        moduleExpMap.put(TEST_MODULE, TEST_EXP_ID);
        String moduleExpConfig = "{\"" + TEST_MODULE + "\":\"" + TEST_EXP_ID + "\"}";
        System.setProperty(LionConstants.DOUHU_MODULE_EXP_CONFIG, moduleExpConfig);
    }

    /**
     * Test when module is null should return null
     */
    @Test
    public void testGetAbExpResultV2ModuleNull() throws Throwable {
        // arrange
        String module = null;
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResultV2(context, module);
        // assert
        assertNull(result);
    }

    /**
     * Test when module has no expId mapping should return null
     */
    @Test
    public void testGetAbExpResultV2NoExpIdMapping() throws Throwable {
        // arrange
        String module = "non-existent-module";
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResultV2(context, module);
        // assert
        assertNull(result);
    }

    /**
     * Test response with blank sk should return null
     */
    @Test
    public void testGetAbExpResultV2BlankSk() throws Throwable {
        // arrange
        DouHuResponse response = new DouHuResponse();
        response.setCode(ErrorCode.SUCCESS.getCode());
        response.setSk("");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResultV2(context, TEST_MODULE);
        // assert
        assertNull(result);
    }

    /**
     * Test response with non-success code should return null
     */
    @Test
    public void testGetAbExpResultV2NonSuccessCode() throws Throwable {
        // arrange
        DouHuResponse response = new DouHuResponse();
        response.setCode(ErrorCode.FAIL.getCode());
        response.setSk("test-sk");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResultV2(context, TEST_MODULE);
        // assert
        assertNull(result);
    }

    /**
     * Test exception case should return null
     */
    @Test
    public void testGetAbExpResultV2Exception() throws Throwable {
        // arrange
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenThrow(new RuntimeException("test exception"));
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResultV2(context, TEST_MODULE);
        // assert
        assertNull(result);
    }

    /**
     * Test null response should return null
     */
    @Test
    public void testGetAbExpResultV2NullResponse() throws Throwable {
        // arrange
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(null);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResultV2(context, TEST_MODULE);
        // assert
        assertNull(result);
    }
}
