package com.dianping.mobile.mapi.dztgdetail.util;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertTrue;

/**
 * <AUTHOR>
 * @create 2025/2/18 19:35
 @RunWith(MockitoJUnitRunner.class)
 */
@RunWith(MockitoJUnitRunner.class)
public class TextSpaceFormatterTest {
    @Test
    public void testFormat() {
        String text = "这是一个测试text with spaces中文和English混合.";
        System.out.println(TextSpaceFormatter.formatTextSpace(text));
        text = TextSpaceFormatter.formatTextSpace(text);
        testFormatTextSpace();
        assertTrue( text.contains("\u2006"));
    }


    public  void testFormatTextSpace() {
        String[][] testCases = {
                // 加空格的测试
                {"Hello世界Bonjour안녕하세요", "Hello\u2006世界\u2006Bonjour안녕하세요"},
                {"你好WorldΓειάσου", "你好\u2006WorldΓειάσου"},
                {"Python编程Bonjour", "Python\u2006编程\u2006Bonjour"},
                {"JavaScript语言こんにちは", "JavaScript\u2006语言\u2006こんにちは"},
                {"Bonjour世界سلام", "Bonjour\u2006世界\u2006سلام"},
                {"안녕하세요Worldこんにちは", "안녕하세요Worldこんにちは"},
                {"こんにちは世界Hello", "こんにちは\u2006世界\u2006Hello"},
                {"ΓειάσουWorld안녕하세요", "ΓειάσουWorld안녕하세요"},
                {"سلام世界Bonjour", "سلام\u2006世界\u2006Bonjour"},
                {"123你好World", "123\u2006你好\u2006World"},
                {"你好123Γειάσ - **Shape**: 🔺06123Γειάσου",""},
                {"中文拼音ABC안녕하세요", "中文拼音\u2006ABC안녕하세요"},
                {"中文拼音abcこんにちは", "中文拼音\u2006abcこんにちは"},
                {"Hello中文拼音Bonjour", "Hello\u2006中文拼音\u2006Bonjour"},
                {"世界HelloΓειάσου", "世界\u2006HelloΓειάσου"},
                {"Test测试Bonjour", "Test\u2006测试\u2006Bonjour"},
                {"测试Testこんにちは", "测试\u2006Testこんにちは"},
                {"标点符号!测试Bonjour", "标点符号!测试\u2006Bonjour"},
                {"测试!标点符号Γειάσου", "测试!标点符号\u2006Γειάσου"},
                // 删除空格的测试
                {"！     你　好           。   是       Wor　ld      Γ   ε  ι  ά   σο  υ", "！     你　好           。   是\u2006Wor　ld      Γ   ε  ι  ά   σο  υ"},
                {"Hel　lo     世　界。   Bon　jour      안녕   하세요", "Hel　lo\u2006世　界。   Bon　jour      안녕   하세요"},
                {"Py　thon       编   程。   Bon　jour      こんにちは", "Py　thon\u2006编   程。   Bon　jour      こんにちは"},
                {"Java　Script语言。   こん　にちは      سلام", "Java　Script\u2006语言。   こん　にちは      سلام"},
                {"世　界Hel　lo。   Γειάσου", "世　界\u2006Hel　lo。   Γειάσου"},
                {"Test测　试。   こん   にちは", "Test\u2006测　试。   こん   にちは"},
                {"标点符号!测　试。   Bon　jour", "标点符号!测　试。   Bon　jour"},
                {"【女神专享】安神助眠spa｜60分钟全身精油SPA", ""},
        };

        for (String[] testCase : testCases) {
            String input = testCase[0];
            String expected = testCase[1];
            String actual = TextSpaceFormatter.formatTextSpace(input);
            System.out.println("expected: "+expected +" actual:"+  actual + " Input: " + input);
        }
    }
}
