package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.cat.Cat;
import com.sankuai.athena.inf.cache2.CacheClient;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.Silent.class)
public class MapperCacheWrapperGetMtDealIdFromCacheTest {

    @InjectMocks
    private MapperCacheWrapper mapperCacheWrapper;

    @Mock
    private CacheClient cacheClient;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private MapperWrapper mapperWrapper;

    @Mock
    private Cat cat;

    private static final int TEST_PLATFORM = 1;

    private static final String TEST_DEAL_GROUP_ID = "123";

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试当dpDealGroupId小于等于0时的情况
     */
    @Test
    public void testGetMtDealIdFromCache_WhenDpDealGroupIdLessThanOrEqualToZero() throws Throwable {
        // arrange
        int dpDealGroupId = 0;
        // act
        CompletableFuture<Integer> resultFuture = mapperCacheWrapper.getMtDealIdFromCache(dpDealGroupId);
        // assert
        assertEquals(Integer.valueOf(0), resultFuture.get());
    }
}
