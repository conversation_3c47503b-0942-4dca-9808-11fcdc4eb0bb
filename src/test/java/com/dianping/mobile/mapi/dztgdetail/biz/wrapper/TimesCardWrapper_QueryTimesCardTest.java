package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.gm.marketing.times.card.api.dto.CardResponse;
import com.sankuai.merchantcard.timescard.exposure.dto.CardSummaryBarDTO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.concurrent.Future;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class TimesCardWrapper_QueryTimesCardTest {

    @Mock
    private Future future;

    @Mock
    private CardResponse<CardSummaryBarDTO> cardResponse;

    private TimesCardWrapper timesCardWrapper = new TimesCardWrapper();

    /**
     * 测试 queryTimesCard 方法，当 Future 的结果为 null 时，应返回 null
     */
    @Test
    public void testQueryTimesCardWhenFutureResultIsNull() throws Throwable {
        // arrange
        when(future.get()).thenReturn(null);
        // act
        CardSummaryBarDTO result = timesCardWrapper.queryTimesCard(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 queryTimesCard 方法，当 Future 的结果不为 null，但 CardResponse 的 isSuccess 方法返回 false 时，应返回 null
     */
    @Test
    public void testQueryTimesCardWhenCardResponseIsNotSuccess() throws Throwable {
        // arrange
        when(future.get()).thenReturn(cardResponse);
        when(cardResponse.isSuccess()).thenReturn(false);
        // act
        CardSummaryBarDTO result = timesCardWrapper.queryTimesCard(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 queryTimesCard 方法，当 Future 的结果不为 null，CardResponse 的 isSuccess 方法返回 true 时，应返回 CardResponse 的 getData 方法的结果
     */
    @Test
    public void testQueryTimesCardWhenCardResponseIsSuccess() throws Throwable {
        // arrange
        CardSummaryBarDTO cardSummaryBarDTO = new CardSummaryBarDTO();
        when(future.get()).thenReturn(cardResponse);
        when(cardResponse.isSuccess()).thenReturn(true);
        when(cardResponse.getData()).thenReturn(cardSummaryBarDTO);
        // act
        CardSummaryBarDTO result = timesCardWrapper.queryTimesCard(future);
        // assert
        assertEquals(cardSummaryBarDTO, result);
    }
}
