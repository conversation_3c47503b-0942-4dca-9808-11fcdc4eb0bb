package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.biz.CreateOrderPageUrlBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealGroupProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.SkuModuleProcessor;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;

import static org.junit.Assert.*;

public class DzDealBaseActionTest {

    @InjectMocks
    private DzDealBaseAction dzDealBaseAction;

    @Mock
    private IMobileContext iMobileContext;

    @Mock
    private DealBaseReq dealBaseReq;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void test1() {
        assertEquals(dzDealBaseAction.validate(null, iMobileContext), Resps.PARAM_ERROR);
        assertEquals(dzDealBaseAction.validate(dealBaseReq, iMobileContext), Resps.PARAM_ERROR);
    }

    @Test
    public void test2() {
        DealGroupProcessor dealGroupProcessor = new DealGroupProcessor();
        EnvCtx envCtx = new EnvCtx();
        DealCtx dealCtx = new DealCtx(envCtx);
        dealCtx.setUseQueryCenter(true);
        dealCtx.setQueryCenterHasError(false);

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupBasicDTO basic = new DealGroupBasicDTO();
//        basic.setSaleChannel(1);
//        basic.setSalePlatform(1);
        dealGroupDTO.setBasic(basic);
        dealGroupDTO.setDpDealGroupId(1L);
        dealCtx.setDealGroupDTO(dealGroupDTO);
        dealGroupProcessor.process(dealCtx);
        assertNull(dealCtx.getSkuSummary());
    }

    @Test
    public void test3() {
        SkuModuleProcessor skuModuleProcessor = new SkuModuleProcessor();
        EnvCtx envCtx = new EnvCtx();
//        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx dealCtx = new DealCtx(envCtx);
        dealCtx.setUseQueryCenter(true);
        dealCtx.setQueryCenterHasError(false);

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupBasicDTO basic = new DealGroupBasicDTO();
        basic.setSaleChannel(1);
        dealGroupDTO.setBasic(basic);
        dealCtx.setDealGroupDTO(dealGroupDTO);
        skuModuleProcessor.process(dealCtx);

        dealGroupDTO.setDeals(new ArrayList<>());
        skuModuleProcessor.process(dealCtx);

        dealCtx.setSkuId("1");
        skuModuleProcessor.process(dealCtx);
        assertNull(dealCtx.getSkuModule());
    }

    @Test
    public void test4() {
        CreateOrderPageUrlBiz createOrderPageUrlBiz = new CreateOrderPageUrlBiz();
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);

        DealGroupPBO dealGroupPBO = new DealGroupPBO();
        dealGroupPBO.setCategoryId(1);
        createOrderPageUrlBiz.replaceUrlByTradeUrlService(envCtx, dealGroupPBO, dealBaseReq);

        DealBuyBar buyBar = new DealBuyBar(1, new ArrayList<>());
        dealGroupPBO.setBuyBar(buyBar);
        createOrderPageUrlBiz.replaceUrlByTradeUrlService(envCtx, dealGroupPBO, dealBaseReq);
        assertNotNull(envCtx);
    }

}
