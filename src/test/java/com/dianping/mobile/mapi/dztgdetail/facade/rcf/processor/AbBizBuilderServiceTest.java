package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedRecommendStrategy;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.AbBizBuilderService;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * @Author: <EMAIL>
 * @Date: 2024/12/21
 */
@RunWith(MockitoJUnitRunner.class)
public class AbBizBuilderServiceTest {
    @InjectMocks
    private AbBizBuilderService abBizBuilderService;
    @Mock
    private DouHuBiz douHuBiz;
    private MockedStatic<LionConfigUtils> lionConfigUtilsMockedStatic;

    @Before
    public void setup() {
        lionConfigUtilsMockedStatic = Mockito.mockStatic(LionConfigUtils.class);
    }

    @After
    public void tearDown() {
        lionConfigUtilsMockedStatic.close();
    }

    @Test
    public void test() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx ctx = new DealCtx(envCtx);
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.hitHideInShopRecommendCategoryIds(Mockito.anyInt())).thenReturn(false);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        Mockito.when(douHuBiz.getAbExpResultByUuidAndDpidV2(Mockito.any(), Mockito.any())).thenReturn(moduleAbConfig);
        RelatedRecommendStrategy result = abBizBuilderService.buildRecommendStrategy(ctx);
        Assert.assertNotNull(result);
    }
}
