package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.zdc.tag.apply.api.PoiTagDisplayRPCService;
import com.sankuai.zdc.tag.apply.dto.FindSceneDisplayTagRequest;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.concurrent.Future;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.any;
import org.junit.runner.RunWith;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import org.mockito.MockedStatic;

@RunWith(MockitoJUnitRunner.class)
public class ShopTagWrapper_PreGetDpShopTagsTest {

    @InjectMocks
    private ShopTagWrapper shopTagWrapper;

    @Mock
    private PoiTagDisplayRPCService poiTagDisplayRPCServiceFuture;

    @Mock
    private Future mockFuture;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testPreGetDpShopTagsWithNullDpShopId() throws Throwable {
        Long dpShopId = null;
        Future result = shopTagWrapper.preGetDpShopTags(dpShopId);
        assertNull(result);
    }

    @Test
    public void testPreGetDpShopTagsWithValidDpShopId() throws Throwable {
        Long dpShopId = 1L;
        when(poiTagDisplayRPCServiceFuture.findSceneDisplayTagOfDp(any(FindSceneDisplayTagRequest.class))).thenReturn(null);
        try (MockedStatic<FutureFactory> mockedStatic = mockStatic(FutureFactory.class)) {
            mockedStatic.when(FutureFactory::getFuture).thenReturn(mockFuture);
            Future result = shopTagWrapper.preGetDpShopTags(dpShopId);
            assertNotNull(result);
            verify(poiTagDisplayRPCServiceFuture, times(1)).findSceneDisplayTagOfDp(any(FindSceneDisplayTagRequest.class));
        }
    }

    @Test
    public void testPreGetDpShopTagsWithException() throws Throwable {
        Long dpShopId = 1L;
        doThrow(new RuntimeException()).when(poiTagDisplayRPCServiceFuture).findSceneDisplayTagOfDp(any(FindSceneDisplayTagRequest.class));
        Future result = shopTagWrapper.preGetDpShopTags(dpShopId);
        assertNull(result);
        verify(poiTagDisplayRPCServiceFuture, times(1)).findSceneDisplayTagOfDp(any(FindSceneDisplayTagRequest.class));
    }
}
