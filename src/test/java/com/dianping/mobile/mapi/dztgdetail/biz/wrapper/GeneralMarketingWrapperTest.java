package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.gm.bonus.exposure.api.dto.BonusExposureQueryRequestDTO;
import com.dianping.gm.bonus.exposure.api.service.BonusExposureQueryService;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import java.util.concurrent.Future;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

public class GeneralMarketingWrapperTest {

    @InjectMocks
    private GeneralMarketingWrapper generalMarketingWrapper;

    @Mock
    private BonusExposureQueryService bonusExposureQueryServiceFuture;

    public GeneralMarketingWrapperTest() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testPrepareQueryBonusExposureRequestIsNull() throws Throwable {
        BonusExposureQueryRequestDTO request = null;
        Future result = generalMarketingWrapper.prepareQueryBonusExposure(request);
        assertNull(result);
    }

    @Test
    public void testPrepareQueryBonusExposureRequestIsNotNullAndQuerySuccess() throws Throwable {
        BonusExposureQueryRequestDTO request = new BonusExposureQueryRequestDTO();
        Future future = mock(Future.class);
        try (MockedStatic<FutureFactory> mockedStatic = Mockito.mockStatic(FutureFactory.class)) {
            mockedStatic.when(FutureFactory::getFuture).thenReturn(future);
            Future result = generalMarketingWrapper.prepareQueryBonusExposure(request);
            assertTrue(result == future);
        }
    }

    @Test
    public void testPrepareQueryBonusExposureRequestIsNotNullAndQueryThrowException() throws Throwable {
        BonusExposureQueryRequestDTO request = new BonusExposureQueryRequestDTO();
        doThrow(new RuntimeException()).when(bonusExposureQueryServiceFuture).queryBonusExposure(request);
        Future result = generalMarketingWrapper.prepareQueryBonusExposure(request);
        assertNull(result);
    }
}
