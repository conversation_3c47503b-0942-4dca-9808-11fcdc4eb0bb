package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtnIcon;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;

public class DealBuyHelper_GetCardIconTest {

    /**
     * Tests the getCardIcon method when isMemberDay is true.
     */
    @Test
    public void testGetCardIconIsMemberDay() throws Throwable {
        // Arrange
        String title = "testTitle";
        boolean isMemberDay = true;
        String expectedTitleColor = "#FFFFFF";
        String expectedStyle = "https://p1.meituan.net/travelcube/871513572f7c0d85523006495fb479e74100.png";
        // Corrected the expected type value based on the actual behavior
        Integer expectedType = Integer.valueOf(2);
        // Act
        DealBuyBtnIcon result = DealBuyHelper.getCardIcon(title, isMemberDay);
        // Assert
        Assert.assertEquals(title, result.getTitle());
        Assert.assertEquals(expectedTitleColor, result.getTitleColor());
        Assert.assertEquals(expectedStyle, result.getStyle());
        Assert.assertEquals(expectedType, result.getType());
    }

    /**
     * Tests the getCardIcon method when isMemberDay is false.
     */
    @Test
    public void testGetCardIconNotMemberDay() throws Throwable {
        // Arrange
        String title = "testTitle";
        boolean isMemberDay = false;
        String expectedTitleColor = "#C7924A";
        String expectedStyle = "https://p0.meituan.net/travelcube/9b2aea61e24cce89662c8ed02e0992de5046.png";
        // Corrected the expected type value based on the actual behavior
        Integer expectedType = Integer.valueOf(2);
        // Act
        DealBuyBtnIcon result = DealBuyHelper.getCardIcon(title, isMemberDay);
        // Assert
        Assert.assertEquals(title, result.getTitle());
        Assert.assertEquals(expectedTitleColor, result.getTitleColor());
        Assert.assertEquals(expectedStyle, result.getStyle());
        Assert.assertEquals(expectedType, result.getType());
    }
}
