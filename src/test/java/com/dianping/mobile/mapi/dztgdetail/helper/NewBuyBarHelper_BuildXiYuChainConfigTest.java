package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.mobile.mapi.dztgdetail.button.BuilderChainConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

@RunWith(MockitoJUnitRunner.class)
public class NewBuyBarHelper_BuildXiYuChainConfigTest {

    /**
     * Tests the buildXiYuChainConfig method under normal conditions.
     * This method is expected to always return a non-null BuilderChainConfig object.
     */
    @Test
    public void testBuildXiYuChainConfigNormal() throws Throwable {
        // Act
        BuilderChainConfig result = NewBuyBarHelper.buildXiYuChainConfig();
        // Assert
        assertNotNull("The result should not be null.", result);
    }
}
