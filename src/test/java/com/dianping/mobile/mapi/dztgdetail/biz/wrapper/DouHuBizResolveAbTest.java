package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.sankuai.douhu.absdk.bean.DouHuRequest;
import com.sankuai.douhu.absdk.bean.DouHuResponse;
import com.sankuai.douhu.absdk.client.DouHuClient;
import com.sankuai.douhu.absdk.enums.ErrorCode;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DouHuBizResolveAbTest {

    @Mock
    private DouHuClient douHuClient;

    @InjectMocks
    private DouHuBiz douHuBiz;

    private DealCtx dealCtx;

    private EnvCtx envCtx;

    @Before
    public void setUp() {
        envCtx = new EnvCtx();
        envCtx.setMtUserId(123L);
        envCtx.setUuid("test-uuid");
        envCtx.setUnionId("test-union");
        envCtx.setVersion("1.0");
        dealCtx = new DealCtx(envCtx);
        dealCtx.setMtCityId(10);
    }

    /**
     * Test when module is null, should return null
     */
    @Test
    public void testResolveAbModuleNull() throws Throwable {
        // arrange
        String module = null;
        // act
        ModuleAbConfig result = douHuBiz.resolveAb(dealCtx, module);
        // assert
        assertNull(result);
    }

    /**
     * Test when module has no expId mapping, should use module name as expId
     */
    @Test
    public void testResolveAbNoExpIdMapping() throws Throwable {
        // arrange
        String module = "test_module";
        DouHuResponse mockResponse = new DouHuResponse();
        mockResponse.setCode(ErrorCode.SUCCESS.getCode());
        mockResponse.setSk("test_sk");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(mockResponse);
        // act
        ModuleAbConfig result = douHuBiz.resolveAb(dealCtx, module);
        // assert
        assertNotNull(result);
        assertEquals(module, result.getKey());
        assertEquals(1, result.getConfigs().size());
        assertEquals(module, result.getConfigs().get(0).getExpId());
    }

    /**
     * Test when DouHuClient returns null response, should return null
     */
    @Test
    public void testResolveAbNullResponse() throws Throwable {
        // arrange
        String module = "test_module";
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(null);
        // act
        ModuleAbConfig result = douHuBiz.resolveAb(dealCtx, module);
        // assert
        assertNull(result);
    }

    /**
     * Test when DouHuClient returns error response, should return null
     */
    @Test
    public void testResolveAbErrorResponse() throws Throwable {
        // arrange
        String module = "test_module";
        DouHuResponse mockResponse = new DouHuResponse();
        mockResponse.setCode(ErrorCode.FAIL.getCode());
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(mockResponse);
        // act
        ModuleAbConfig result = douHuBiz.resolveAb(dealCtx, module);
        // assert
        assertNull(result);
    }

    /**
     * Test when DouHuClient returns success response, should return ModuleAbConfig
     */
    @Test
    public void testResolveAbSuccessResponse() throws Throwable {
        // arrange
        String module = "test_module";
        String sk = "test_sk";
        DouHuResponse mockResponse = new DouHuResponse();
        mockResponse.setCode(ErrorCode.SUCCESS.getCode());
        mockResponse.setSk(sk);
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(mockResponse);
        // act
        ModuleAbConfig result = douHuBiz.resolveAb(dealCtx, module);
        // assert
        assertNotNull(result);
        assertEquals(module, result.getKey());
        assertEquals(1, result.getConfigs().size());
        AbConfig abConfig = result.getConfigs().get(0);
        // The expId should be the module name
        assertEquals(module, abConfig.getExpId());
        assertEquals(sk, abConfig.getExpResult());
        assertNotNull(abConfig.getExpBiInfo());
    }

    /**
     * Test when exception occurs, should return null and log error
     */
    @Test
    public void testResolveAbExceptionCase() throws Throwable {
        // arrange
        String module = "test_module";
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenThrow(new RuntimeException("test exception"));
        // act
        ModuleAbConfig result = douHuBiz.resolveAb(dealCtx, module);
        // assert
        assertNull(result);
    }

    /**
     * Test request creation with different client types
     */
    @Test
    public void testResolveAbRequestPlatform() throws Throwable {
        // arrange
        String module = "test_module";
        DouHuResponse mockResponse = new DouHuResponse();
        mockResponse.setCode(ErrorCode.SUCCESS.getCode());
        mockResponse.setSk("test_sk");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(mockResponse);
        // act
        douHuBiz.resolveAb(dealCtx, module);
        // assert
        verify(douHuClient).tryAb(argThat(request -> dealCtx.isMt() ? "mt".equals(request.getPlatform()) : "dp".equals(request.getPlatform())));
    }
}
