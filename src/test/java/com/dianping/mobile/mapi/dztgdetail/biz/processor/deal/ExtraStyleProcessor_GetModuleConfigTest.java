package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.List;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class ExtraStyleProcessor_GetModuleConfigTest {

    private ExtraStyleProcessor extraStyleProcessor;

    @Before
    public void setUp() {
        extraStyleProcessor = new ExtraStyleProcessor();
    }

    /**
     * 测试 getModuleConfig 方法，当 moduleConfigs 为空时
     */
    @Test
    public void testGetModuleConfigWhenModuleConfigsIsNull() {
        // arrange
        ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
        moduleConfigsModule.setModuleConfigs(null);
        // act
        List<ModuleConfigDo> result = extraStyleProcessor.getModuleConfig(moduleConfigsModule);
        // assert
        assertTrue(result.isEmpty());
        assertEquals(result, moduleConfigsModule.getModuleConfigs());
    }

    /**
     * 测试 getModuleConfig 方法，当 moduleConfigs 不为空时
     */
    @Test
    public void testGetModuleConfigWhenModuleConfigsIsNotNull() {
        // arrange
        ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
        List<ModuleConfigDo> moduleConfigs = new ArrayList<>();
        moduleConfigs.add(new ModuleConfigDo());
        moduleConfigsModule.setModuleConfigs(moduleConfigs);
        // act
        List<ModuleConfigDo> result = extraStyleProcessor.getModuleConfig(moduleConfigsModule);
        // assert
        assertEquals(moduleConfigs, result);
        assertEquals(result, moduleConfigsModule.getModuleConfigs());
    }
}
