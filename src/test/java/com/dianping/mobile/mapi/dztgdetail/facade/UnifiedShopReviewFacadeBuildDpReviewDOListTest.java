package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.cip.growth.mana.api.dto.response.UserManaDTO;
import com.dianping.core.type.PageModel;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ReviewWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.UserWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.ReviewFilterType;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.ShopReviewCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.ReviewDetailDO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.ReviewPicDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.ReviewTagDO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.ReviewUserModel;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.UnifiedShopReviewList;
import com.dianping.mobile.mapi.dztgdetail.helper.ShopReviewHelper;
import com.dianping.review.professional.ReviewDataV2;
import com.dianping.review.professional.Star;
import com.dianping.tuangou.dztg.bjwrapper.api.dto.MtUserDto;
import com.dianping.ugc.pic.remote.dto.MtReviewPicInfo;
import com.dianping.ugc.pic.remote.dto.VideoData;
import com.dianping.ugc.proxyService.remote.dto.AnonymousUserInfo;
import com.dianping.ugc.review.remote.dto.MTQueryResult;
import com.dianping.ugc.review.remote.dto.MTReviewData;
import com.dianping.ugc.review.remote.dto.ReviewPic;
import com.dianping.ugc.review.remote.dto.ReviewVideo;
import com.dianping.userremote.base.dto.UserDTO;
import com.dianping.vipremote.vo.UserInfoForAppVO;
import com.dp.arts.client.response.Response;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

public class UnifiedShopReviewFacadeBuildDpReviewDOListTest {

    @InjectMocks
    private UnifiedShopReviewFacade unifiedShopReviewFacade;

    @Mock
    private ReviewWrapper reviewWrapper;

    @Mock
    private UserWrapper userWrapper;

    @Mock
    private ShopReviewHelper shopReviewHelper;

    @Mock
    private Future<Response> allShopReviewTagFuture;

    @Mock
    private ShopReviewCtx shopReviewCtx;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private List<ReviewDetailDO> invokePrivateBuildMtReviewDOList(ShopReviewCtx shopReviewCtx, MTQueryResult mtQueryResult) throws Exception {
        Method method = UnifiedShopReviewFacade.class.getDeclaredMethod("buildMtReviewDOList", ShopReviewCtx.class, MTQueryResult.class);
        method.setAccessible(true);
        return (List<ReviewDetailDO>) method.invoke(unifiedShopReviewFacade, shopReviewCtx, mtQueryResult);
    }

    /**
     * 测试当 pageModel 为空时，返回空列表
     */
    @Test
    public void testBuildDpReviewDOListWithNullPageModel() throws Throwable {
        // arrange
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx(new EnvCtx());
        PageModel pageModel = null;
        boolean supply = false;
        // act
        List<ReviewDetailDO> result = unifiedShopReviewFacade.buildDpReviewDOList(shopReviewCtx, pageModel, supply);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试当 pageModel.getRecords() 为空时，返回空列表
     */
    @Test
    public void testBuildDpReviewDOListWithEmptyRecords() throws Throwable {
        // arrange
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx(new EnvCtx());
        PageModel pageModel = new PageModel();
        pageModel.setRecords(new ArrayList<>());
        boolean supply = false;
        // act
        List<ReviewDetailDO> result = unifiedShopReviewFacade.buildDpReviewDOList(shopReviewCtx, pageModel, supply);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试正常场景，所有异步调用成功返回数据
     */
    @Test
    public void testBuildDpReviewDOListWithAllData() throws Throwable {
        // arrange
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx(new EnvCtx());
        PageModel pageModel = new PageModel();
        List<ReviewDataV2> reviewDataV2List = new ArrayList<>();
        ReviewDataV2 reviewDataV2 = new ReviewDataV2();
        reviewDataV2.setReviewIdLong(1L);
        reviewDataV2.setShopId(1L);
        reviewDataV2.setUserId(1L);
        reviewDataV2.setAnonymous(true);
        reviewDataV2.setStar(new Star());
        reviewDataV2.setFlowerTotal(1);
        reviewDataV2.setFollowNoteNo(1);
        reviewDataV2List.add(reviewDataV2);
        pageModel.setRecords(reviewDataV2List);
        boolean supply = false;
        // Mock 数据准备
        Future anonymousUserInfoFuture = mock(Future.class);
        when(reviewWrapper.getAnonymousUserInfoFuture(any())).thenReturn(anonymousUserInfoFuture);
        when(reviewWrapper.getFutureResult(anonymousUserInfoFuture)).thenReturn(Collections.singletonMap(1L, new AnonymousUserInfo()));
        Future reviewBrowsCountsFuture = mock(Future.class);
        when(reviewWrapper.getReviewsBrowseCountFutureV2(any())).thenReturn(reviewBrowsCountsFuture);
        when(reviewWrapper.getFutureResult(reviewBrowsCountsFuture)).thenReturn(Collections.singletonMap(1L, 1));
        Future userFuture = mock(Future.class);
        when(userWrapper.getUserInfos(any())).thenReturn(userFuture);
        when(userWrapper.getFutureResult(userFuture)).thenReturn(Collections.singletonMap(1L, new UserDTO()));
        Future vipFuture = mock(Future.class);
        when(reviewWrapper.getUserVipInfoFuture(any())).thenReturn(vipFuture);
        when(reviewWrapper.getFutureResult(vipFuture)).thenReturn(Collections.singletonMap(1L, new UserInfoForAppVO()));
        Future userGrowthFuture = mock(Future.class);
        when(userWrapper.batchQueryUserGrowth(any())).thenReturn(userGrowthFuture);
        when(userWrapper.getFutureResult(userGrowthFuture)).thenReturn(Collections.singletonMap(1L, new UserManaDTO()));
        // act
        List<ReviewDetailDO> result = unifiedShopReviewFacade.buildDpReviewDOList(shopReviewCtx, pageModel, supply);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        // 更多的断言可以根据实际情况添加，这里只是示例
    }

    /**
     * 测试 PageModel 为空的情况
     */
    @Test
    public void testGetDpReviewDetailListPageModelIsNull() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx(envCtx);
        UnifiedShopReviewList unifiedShopReviewList = new UnifiedShopReviewList();
        int displayReviewCount = 10;
        Future<PageModel> shopReviewFuture = mock(Future.class);
        when(reviewWrapper.getShopReviewFuture(shopReviewCtx, displayReviewCount)).thenReturn(shopReviewFuture);
        when(reviewWrapper.getFutureResult(shopReviewFuture)).thenReturn(null);
        // act
        Method getDpReviewDetailListMethod = UnifiedShopReviewFacade.class.getDeclaredMethod("getDpReviewDetailList", ShopReviewCtx.class, UnifiedShopReviewList.class, int.class);
        getDpReviewDetailListMethod.setAccessible(true);
        getDpReviewDetailListMethod.invoke(unifiedShopReviewFacade, shopReviewCtx, unifiedShopReviewList, displayReviewCount);
        // assert
        assertEquals(0, unifiedShopReviewList.getRecordCount());
        assertTrue(unifiedShopReviewList.getReviewDetailList().isEmpty());
    }

    /**
     * 测试获取点评评论数据时抛出异常的情况
     */
    @Test(expected = Exception.class)
    public void testGetDpReviewDetailListGetShopReviewFutureThrowsException() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx(envCtx);
        UnifiedShopReviewList unifiedShopReviewList = new UnifiedShopReviewList();
        int displayReviewCount = 10;
        when(reviewWrapper.getShopReviewFuture(shopReviewCtx, displayReviewCount)).thenThrow(new Exception("Failed to get shop review"));
        // act
        Method getDpReviewDetailListMethod = UnifiedShopReviewFacade.class.getDeclaredMethod("getDpReviewDetailList", ShopReviewCtx.class, UnifiedShopReviewList.class, int.class);
        getDpReviewDetailListMethod.setAccessible(true);
        getDpReviewDetailListMethod.invoke(unifiedShopReviewFacade, shopReviewCtx, unifiedShopReviewList, displayReviewCount);
        // assert
        // Expecting exception
    }

    /**
     * 测试获取美团评论数据时抛出异常的情况
     */
    @Test(expected = Exception.class)
    public void testGetDpReviewDetailListGetMtShopReviewFutureThrowsException() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx(envCtx);
        UnifiedShopReviewList unifiedShopReviewList = new UnifiedShopReviewList();
        int displayReviewCount = 10;
        PageModel pageModel = new PageModel();
        pageModel.setRecordCount(5);
        pageModel.setRecords(Collections.emptyList());
        Future<PageModel> shopReviewFuture = mock(Future.class);
        when(reviewWrapper.getShopReviewFuture(shopReviewCtx, displayReviewCount)).thenReturn(shopReviewFuture);
        when(reviewWrapper.getFutureResult(shopReviewFuture)).thenReturn(pageModel);
        List<ReviewDetailDO> reviewDetailDOList = Collections.singletonList(new ReviewDetailDO());
        when(unifiedShopReviewFacade.buildDpReviewDOList(shopReviewCtx, pageModel, false)).thenReturn(reviewDetailDOList);
        when(shopReviewHelper.disPlayShopReviewTag()).thenReturn(true);
        List<ReviewTagDO> reviewTagDOList = Collections.singletonList(new ReviewTagDO());
        Future<?> allShopReviewTagFuture = mock(Future.class);
        when(reviewWrapper.getDPAllShopReviewTagFuture(shopReviewCtx)).thenReturn(allShopReviewTagFuture);
        Method createReviewTagMethod = UnifiedShopReviewFacade.class.getDeclaredMethod("createReviewTag", Future.class, ShopReviewCtx.class);
        createReviewTagMethod.setAccessible(true);
        when(createReviewTagMethod.invoke(unifiedShopReviewFacade, allShopReviewTagFuture, shopReviewCtx)).thenReturn(reviewTagDOList);
        when(shopReviewHelper.dpShopReviewNeedSupply()).thenReturn(true);
        when(reviewWrapper.getMtShopReviewFutureV2(anyLong(), eq(0), eq(5), eq(ReviewFilterType.RANK_ALGO))).thenThrow(new Exception("Failed to get mt shop review"));
        // act
        Method getDpReviewDetailListMethod = UnifiedShopReviewFacade.class.getDeclaredMethod("getDpReviewDetailList", ShopReviewCtx.class, UnifiedShopReviewList.class, int.class);
        getDpReviewDetailListMethod.setAccessible(true);
        getDpReviewDetailListMethod.invoke(unifiedShopReviewFacade, shopReviewCtx, unifiedShopReviewList, displayReviewCount);
        // assert
        // Expecting exception
    }

    /**
     * 测试 createReviewTag 方法在 Response 不为 null 时正常执行
     */
    @Test
    public void testCreateReviewTagResponseNotNull() throws Throwable {
        // arrange
        Response response = new Response();
        when(reviewWrapper.getFutureResult(allShopReviewTagFuture)).thenReturn(response);
        // act
        Method method = UnifiedShopReviewFacade.class.getDeclaredMethod("createReviewTag", Future.class, ShopReviewCtx.class);
        method.setAccessible(true);
        List<ReviewTagDO> result = (List<ReviewTagDO>) method.invoke(unifiedShopReviewFacade, allShopReviewTagFuture, shopReviewCtx);
        // assert
        assertNotNull(result);
        verify(reviewWrapper, times(1)).getFutureResult(allShopReviewTagFuture);
    }

    /**
     * 测试 createReviewTag 方法在 Response 为 null 时返回空列表
     */
    @Test
    public void testCreateReviewTagResponseNull() throws Throwable {
        // arrange
        when(reviewWrapper.getFutureResult(allShopReviewTagFuture)).thenReturn(null);
        // act
        Method method = UnifiedShopReviewFacade.class.getDeclaredMethod("createReviewTag", Future.class, ShopReviewCtx.class);
        method.setAccessible(true);
        List<ReviewTagDO> result = (List<ReviewTagDO>) method.invoke(unifiedShopReviewFacade, allShopReviewTagFuture, shopReviewCtx);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(reviewWrapper, times(1)).getFutureResult(allShopReviewTagFuture);
    }

    /**
     * 测试当 mtQueryResult 为空时，返回空列表
     */
    @Test
    public void testBuildMtReviewDOListWhenMtQueryResultIsNull() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx(envCtx);
        MTQueryResult mtQueryResult = null;
        // act
        List<ReviewDetailDO> result = invokePrivateBuildMtReviewDOList(shopReviewCtx, mtQueryResult);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试当 mtQueryResult.getMtReviewDataList() 为空时，返回空列表
     */
    @Test
    public void testBuildMtReviewDOListWhenMtReviewDataListIsEmpty() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx(envCtx);
        MTQueryResult mtQueryResult = new MTQueryResult();
        mtQueryResult.setMtReviewDataList(Collections.emptyList());
        // act
        List<ReviewDetailDO> result = invokePrivateBuildMtReviewDOList(shopReviewCtx, mtQueryResult);
        // assert
        assertTrue(result.isEmpty());
    }
}
