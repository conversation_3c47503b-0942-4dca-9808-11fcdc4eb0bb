package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.glasses.AdultGlassesHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.glasses.ChildGlassesHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.glasses.OnlyOptometryHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.glasses.factory.GlassesFactory;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.glasses.factory.GlassesStrategy;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.glasses.factory.impl.*;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CommonAttrVO;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.highlights.DztgHighlightsModule;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEvent;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.GlassesHighlightV2Processor.GLASSES_SERVER_TYPE_HANDLER_MAP;
import static groovy.util.GroovyTestCase.assertEquals;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class GlassesHighlightV2ProcessorTest {

    @InjectMocks
    private GlassesHighlightV2Processor glassesHighlightV2Processor;

    @Mock
    ApplicationContext appCtx;
    @Mock
    GlassesFactory glassesFactory;

    @Before
    public void setUp() throws Exception {
        reset(appCtx);
        GLASSES_SERVER_TYPE_HANDLER_MAP.put("成人配镜",new AdultGlassesHandler());
        GLASSES_SERVER_TYPE_HANDLER_MAP.put("儿童配镜", new AdultGlassesHandler());
        GLASSES_SERVER_TYPE_HANDLER_MAP.put("仅验光", new AdultGlassesHandler());
        glassesFactory.init();
    }

    /**
     * 测试beforeBuild方法，当dealGroupDTO为null时
     */
    @Test
    public void testBeforeBuildWhenDealGroupDTOIsNull() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());

        // act
        glassesHighlightV2Processor.beforeBuild(ctx);

        // assert
        assertNull(ctx.getHighlightsModule());
    }

    /**
     * 测试beforeBuild方法，当处理器为null时
     */
    @Test
    public void testBeforeBuildWhenHandlerIsNull() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        ctx.setDealGroupDTO(dealGroupDTO);

        // act
        glassesHighlightV2Processor.beforeBuild(ctx);

        // assert
        assertNull(ctx.getHighlightsModule());
    }

    /**
     * 测试afterBuild方法，当attrs的数量小于2时
     */
    @Test
    public void testAfterBuildWhenAttrsSizeLessThan2() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        DztgHighlightsModule highlightsModule = new DztgHighlightsModule();
        highlightsModule.setAttrs(new ArrayList<>());
        ctx.setHighlightsModule(highlightsModule);

        // act
        glassesHighlightV2Processor.afterBuild(ctx);

        // assert
        assertNull(ctx.getHighlightsModule());
    }

    /**
     * 测试getHighlightsIdentify方法，当DztgHighlightsModule为null时
     */
    @Test
    public void testGetHighlightsIdentify_ModuleIsNull() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.getHighlightsModule()).thenReturn(null);

        // act
        String result = glassesHighlightV2Processor.getHighlightsIdentify(ctx);

        // assert
        assertNull(result);
    }

    /**
     * 测试getHighlightsIdentify方法，当DztgHighlightsModule不为null时
     */
    @Test
    public void testGetHighlightsIdentify_ModuleIsNotNull() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        DztgHighlightsModule module = mock(DztgHighlightsModule.class);
        when(ctx.getHighlightsModule()).thenReturn(module);
        when(module.getIdentify()).thenReturn("test_identify");

        // act
        String result = glassesHighlightV2Processor.getHighlightsIdentify(ctx);

        // assert
        assertEquals("test_identify", result);
    }

    /**
     * 测试getHighlightsStyle方法，当DztgHighlightsModule为null时
     */
    @Test
    public void testGetHighlightsStyle_ModuleIsNull() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.getHighlightsModule()).thenReturn(null);

        // act
        String result = glassesHighlightV2Processor.getHighlightsStyle(ctx);

        // assert
        assertNull(result);
    }

    /**
     * 测试getHighlightsStyle方法，当DztgHighlightsModule不为null时
     */
    @Test
    public void testGetHighlightsStyle_ModuleIsNotNull() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        DztgHighlightsModule module = mock(DztgHighlightsModule.class);
        when(ctx.getHighlightsModule()).thenReturn(module);
        when(module.getStyle()).thenReturn("test_style");

        // act
        String result = glassesHighlightV2Processor.getHighlightsStyle(ctx);

        // assert
        assertEquals("test_style", result);
    }

    /**
     * 测试getHighlightsContent方法，当DztgHighlightsModule为null时
     */
    @Test
    public void testGetHighlightsContent_ModuleIsNull() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.getHighlightsModule()).thenReturn(null);

        // act
        String result = glassesHighlightV2Processor.getHighlightsContent(ctx);

        // assert
        assertNull(result);
    }

    /**
     * 测试getHighlightsContent方法，当DztgHighlightsModule不为null时
     */
    @Test
    public void testGetHighlightsContent_ModuleIsNotNull() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        DztgHighlightsModule module = mock(DztgHighlightsModule.class);
        when(ctx.getHighlightsModule()).thenReturn(module);
        when(module.getContent()).thenReturn("test_content");

        // act
        String result = glassesHighlightV2Processor.getHighlightsContent(ctx);

        // assert
        assertEquals("test_content", result);
    }

    /**
     * 测试getHighlightsAttrs方法，当DztgHighlightsModule为null时
     */
    @Test
    public void testGetHighlightsAttrs_ModuleIsNull() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.getHighlightsModule()).thenReturn(null);

        // act
        List<CommonAttrVO> result = glassesHighlightV2Processor.getHighlightsAttrs(ctx);

        // assert
        assertNull(result);
    }

    /**
     * 测试getHighlightsAttrs方法，当DztgHighlightsModule不为null时
     */
    @Test
    public void testGetHighlightsAttrs_ModuleIsNotNull() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        DztgHighlightsModule module = mock(DztgHighlightsModule.class);
        List<CommonAttrVO> attrs = Arrays.asList(new CommonAttrVO(), new CommonAttrVO());
        when(ctx.getHighlightsModule()).thenReturn(module);
        when(module.getAttrs()).thenReturn(attrs);

        // act
        List<CommonAttrVO> result = glassesHighlightV2Processor.getHighlightsAttrs(ctx);

        // assert
        assertEquals(attrs, result);
    }

    /**
     * 测试 newDztgHighlightsModel 方法
     */
    @Test
    public void testNewDztgHighlightsModel() throws Throwable {

        // act
        DztgHighlightsModule result = glassesHighlightV2Processor.newDztgHighlightsModel();

        // assert
        assertNotNull(result);
        assertEquals("struct", result.getStyle());
        assertNotNull(result.getAttrs());
        assertTrue(result.getAttrs().isEmpty());
    }
}
