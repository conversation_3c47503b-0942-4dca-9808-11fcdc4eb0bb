package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.gm.marketing.times.card.api.dto.CardResponse;
import com.sankuai.merchantcard.timescard.exposure.TimesCardNavigationService;
import com.sankuai.merchantcard.timescard.exposure.res.QueryDealGroupCardBarSummaryRequest;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.concurrent.Future;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;
import org.junit.Before;

public class TimesCardWrapper_PreTimesCardsTest {

    @InjectMocks
    private TimesCardWrapper timesCardWrapper = new TimesCardWrapper();

    @Mock
    private TimesCardNavigationService timesCardNavigationService;

    public TimesCardWrapper_PreTimesCardsTest() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testPreTimesCardsIdLessThanOrEqualToZero() throws Throwable {
        Future future = timesCardWrapper.preTimesCards(0, 1, 1, 1L);
        assertNull(future);
    }

    @Test
    public void testPreTimesCardsIdGreaterThanZeroAndLoadDealGroupCardBarSummaryThrowsException() throws Throwable {
        doThrow(new RuntimeException()).when(timesCardNavigationService).loadDealGroupCardBarSummary(any(QueryDealGroupCardBarSummaryRequest.class));
        Future future = timesCardWrapper.preTimesCards(1, 1, 1, 1L);
        assertNull(future);
        verify(timesCardNavigationService, times(1)).loadDealGroupCardBarSummary(any(QueryDealGroupCardBarSummaryRequest.class));
    }
}
