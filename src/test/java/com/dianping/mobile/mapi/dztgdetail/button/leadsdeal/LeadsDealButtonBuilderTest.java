package com.dianping.mobile.mapi.dztgdetail.button.leadsdeal;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DztgSkuModule;
import com.dianping.mobile.mapi.dztgdetail.entity.LeadsDealBarConfig;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.RedirectUrls;
import com.dianping.mobile.mapi.dztgdetail.util.VersionUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

/**
 * @author: wuwenqiang
 * @create: 2024-09-23
 * @description:
 */
@RunWith(MockitoJUnitRunner.class)
public class LeadsDealButtonBuilderTest {
    @Mock
    private ButtonBuilderChain chain;

    @InjectMocks
    private LeadsDealButtonBuilder leadsDealButtonBuilder;

    private MockedStatic<LionConfigUtils> mockedLionConfigUtils;
    private MockedStatic<DealUtils> mockedDealUtils;
    private MockedStatic<RedirectUrls> mockedRedirectUrls;
    private MockedStatic<DealBuyHelper> mockedDealBuyHelper;

    @Before
    public void setUp() {
        mockedLionConfigUtils = Mockito.mockStatic(LionConfigUtils.class);
        mockedDealUtils = Mockito.mockStatic(DealUtils.class);
        mockedRedirectUrls = Mockito.mockStatic(RedirectUrls.class);
        mockedDealBuyHelper = Mockito.mockStatic(DealBuyHelper.class);
    }

    @After
    public void after() {
        mockedLionConfigUtils.close();
        mockedDealUtils.close();
        mockedRedirectUrls.close();
        mockedDealBuyHelper.close();
    }

    /**
     * 测试底bar配置为空
     */
    @Test
    public void testBuild_ConfigIsNull() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx dealCtx = new DealCtx(envCtx);
        mockedLionConfigUtils.when(() -> LionConfigUtils.getLeadsDealBarConfig(dealCtx)).thenReturn(null);

        // act
        leadsDealButtonBuilder.doBuild(dealCtx, chain);

        // assert
        assertTrue(CollectionUtils.isEmpty(dealCtx.getBuyBar().getBuyBtns()));
    }

    /**
     * 测试美团App且有商家预约权益
     */
    @Test
    public void testBuild_MtAppWithResv() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx dealCtx = new DealCtx(envCtx);
        DztgSkuModule dztgSkuModule = new DztgSkuModule();
        dztgSkuModule.setUrl("buyUrl");
        dealCtx.setSkuModule(dztgSkuModule);
        dealCtx.setMrnVersion("0.5.14");
        String configStr = "{\"bannerBackGroundColor\":\"#FFF1EC\",\"bannerIcon\":\"https://p0.meituan.net/dztgdetailimages/427d9c96dd426b065da5b2e28db51ddd620.png\",\"bannerTextColor\":\"#FF4B10\",\"bannerTextSize\":11,\"buyBtnTitle\":\"购买\",\"dpAppJumpPrefix\":\"dianping://mrn\",\"mtAppJumpPrefix\":\"imeituan://www.meituan.com/mrn\",\"newLeadsGiftsTemplate\":\"预约到店赠送价值%s元%s\",\"oldLeadsGiftsTemplate\":\"预约到店赠送%s\",\"phoneResvBtnTitle\":\"致电商家预约看房型\",\"resvBtnTitle\":\"预约看房型\",\"resvJumpUrlPrefix\":\"mrn_biz=gc&mrn_entry=vg-mrn-reserve&mrn_component=customerpopup\",\"resvJumpUrlSuffix\":\"isTransparent=true&hideLoading=true&mrn_transparent=true&mrn_hideloading=true&mrn_hideNextNavBar=true\"}";
        LeadsDealBarConfig leadsDealBarConfig = JSON.parseObject(configStr, LeadsDealBarConfig.class);
        mockedLionConfigUtils.when(() -> LionConfigUtils.getLeadsDealMinMrnVersion()).thenReturn("0.5.13");
        mockedLionConfigUtils.when(() -> LionConfigUtils.getLeadsDealBarConfig(dealCtx)).thenReturn(leadsDealBarConfig);
        mockedDealUtils.when(() -> DealUtils.hasResvBenefits(dealCtx)).thenReturn(true);
        mockedRedirectUrls.when(() -> RedirectUrls.buildResvPopBtnUrl(dealCtx, leadsDealBarConfig)).thenReturn("resvUrl");
        mockedDealBuyHelper.when(() -> DealBuyHelper.getCanNotBuyButton(dealCtx)).thenReturn(null);
        // act
        leadsDealButtonBuilder.doBuild(dealCtx, chain);

        // assert
        assertEquals(BuyBtnTypeEnum.RESV_DEAL.getCode(), dealCtx.getBuyBar().getBuyBtns().get(0).getDetailBuyType());
        assertEquals("resvUrl", dealCtx.getBuyBar().getBuyBtns().get(0).getRedirectUrl());
        assertEquals("预约看房型", dealCtx.getBuyBar().getBuyBtns().get(0).getBtnTitle());
        assertEquals(BuyBtnTypeEnum.NORMAL_DEAL.getCode(), dealCtx.getBuyBar().getBuyBtns().get(1).getDetailBuyType());
        assertEquals("buyUrl", dealCtx.getBuyBar().getBuyBtns().get(1).getRedirectUrl());
        assertEquals("购买", dealCtx.getBuyBar().getBuyBtns().get(1).getBtnTitle());
    }

    /**
     * 测试美团App无商家预约权益
     */
    @Test
    public void testBuild_MtAppWithoutResv() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx dealCtx = new DealCtx(envCtx);
        DztgSkuModule dztgSkuModule = new DztgSkuModule();
        dztgSkuModule.setUrl("buyUrl");
        dealCtx.setSkuModule(dztgSkuModule);
        BestShopDTO bestShopDTO = new BestShopDTO();
        bestShopDTO.setPhoneNos(Lists.newArrayList("123"));
        dealCtx.setBestShopResp(bestShopDTO);
        dealCtx.setMrnVersion("0.5.14");
        String configStr = "{\"bannerBackGroundColor\":\"#FFF1EC\",\"bannerIcon\":\"https://p0.meituan.net/dztgdetailimages/427d9c96dd426b065da5b2e28db51ddd620.png\",\"bannerTextColor\":\"#FF4B10\",\"bannerTextSize\":11,\"buyBtnTitle\":\"购买\",\"dpAppJumpPrefix\":\"dianping://mrn\",\"mtAppJumpPrefix\":\"imeituan://www.meituan.com/mrn\",\"newLeadsGiftsTemplate\":\"预约到店赠送价值%s元%s\",\"oldLeadsGiftsTemplate\":\"预约到店赠送%s\",\"phoneResvBtnTitle\":\"致电商家预约看房型\",\"resvBtnTitle\":\"预约看房型\",\"resvJumpUrlPrefix\":\"mrn_biz=gc&mrn_entry=vg-mrn-reserve&mrn_component=customerpopup\",\"resvJumpUrlSuffix\":\"isTransparent=true&hideLoading=true&mrn_transparent=true&mrn_hideloading=true&mrn_hideNextNavBar=true\"}";
        LeadsDealBarConfig leadsDealBarConfig = JSON.parseObject(configStr, LeadsDealBarConfig.class);
        mockedLionConfigUtils.when(() -> LionConfigUtils.getLeadsDealMinMrnVersion()).thenReturn("0.5.13");
        mockedLionConfigUtils.when(() -> LionConfigUtils.getLeadsDealBarConfig(dealCtx)).thenReturn(leadsDealBarConfig);
        mockedDealUtils.when(() -> DealUtils.hasResvBenefits(dealCtx)).thenReturn(false);
        mockedRedirectUrls.when(() -> RedirectUrls.buildResvPopBtnUrl(dealCtx, leadsDealBarConfig)).thenReturn("resvUrl");
        mockedDealBuyHelper.when(() -> DealBuyHelper.getCanNotBuyButton(dealCtx)).thenReturn(null);

        // act
        leadsDealButtonBuilder.doBuild(dealCtx, chain);

        // assert
        assertEquals(BuyBtnTypeEnum.RESV_DEAL.getCode(), dealCtx.getBuyBar().getBuyBtns().get(0).getDetailBuyType());
        assertTrue(dealCtx.getBuyBar().getBuyBtns().get(0).isUsePhone());
        assertEquals("123", dealCtx.getBuyBar().getBuyBtns().get(0).getPhoneNos().get(0));
        assertEquals("致电商家预约看房型", dealCtx.getBuyBar().getBuyBtns().get(0).getBtnTitle());
        assertEquals(BuyBtnTypeEnum.NORMAL_DEAL.getCode(), dealCtx.getBuyBar().getBuyBtns().get(1).getDetailBuyType());
        assertEquals("buyUrl", dealCtx.getBuyBar().getBuyBtns().get(1).getRedirectUrl());
        assertEquals("购买", dealCtx.getBuyBar().getBuyBtns().get(1).getBtnTitle());
    }

    /**
     * 测试美团App无商家预约权益，且不含电话
     */
    @Test
    public void testBuild_MtAppWithoutResvWithoutPhone() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx dealCtx = new DealCtx(envCtx);
        BestShopDTO bestShopDTO = new BestShopDTO();
        bestShopDTO.setPhoneNos(Lists.newArrayList());
        dealCtx.setBestShopResp(bestShopDTO);
        dealCtx.setMrnVersion("0.5.14");
        String configStr = "{\"bannerBackGroundColor\":\"#FFF1EC\",\"bannerIcon\":\"https://p0.meituan.net/dztgdetailimages/427d9c96dd426b065da5b2e28db51ddd620.png\",\"bannerTextColor\":\"#FF4B10\",\"bannerTextSize\":11,\"buyBtnTitle\":\"购买\",\"dpAppJumpPrefix\":\"dianping://mrn\",\"mtAppJumpPrefix\":\"imeituan://www.meituan.com/mrn\",\"newLeadsGiftsTemplate\":\"预约到店赠送价值%s元%s\",\"oldLeadsGiftsTemplate\":\"预约到店赠送%s\",\"phoneResvBtnTitle\":\"致电商家预约看房型\",\"resvBtnTitle\":\"预约看房型\", \"emptyResvBtnTitle\":\"不可预约\",\"resvJumpUrlPrefix\":\"mrn_biz=gc&mrn_entry=vg-mrn-reserve&mrn_component=customerpopup\",\"resvJumpUrlSuffix\":\"isTransparent=true&hideLoading=true&mrn_transparent=true&mrn_hideloading=true&mrn_hideNextNavBar=true\"}";
        LeadsDealBarConfig leadsDealBarConfig = JSON.parseObject(configStr, LeadsDealBarConfig.class);
        mockedLionConfigUtils.when(() -> LionConfigUtils.getLeadsDealMinMrnVersion()).thenReturn("0.5.13");
        mockedLionConfigUtils.when(() -> LionConfigUtils.getLeadsDealBarConfig(dealCtx)).thenReturn(leadsDealBarConfig);
        mockedDealUtils.when(() -> DealUtils.hasResvBenefits(dealCtx)).thenReturn(false);
        mockedDealBuyHelper.when(() -> DealBuyHelper.getCanNotBuyButton(dealCtx)).thenReturn(null);

        // act
        leadsDealButtonBuilder.doBuild(dealCtx, chain);

        // assert
        assertEquals(BuyBtnTypeEnum.RESV_DEAL.getCode(), dealCtx.getBuyBar().getBuyBtns().get(0).getDetailBuyType());
        assertEquals("不可预约", dealCtx.getBuyBar().getBuyBtns().get(0).getBtnTitle());
    }

    @Test
    public void testBuild_MTAppSpecialDealAndWithResv() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx dealCtx = new DealCtx(envCtx);
        DztgSkuModule dztgSkuModule = new DztgSkuModule();
        dztgSkuModule.setUrl("buyUrl");
        dealCtx.setSkuModule(dztgSkuModule);
        dealCtx.setHitSpecialValueDeal(true);
        dealCtx.setMrnVersion("0.5.14");
        String configStr = "{\"bannerBackGroundColor\":\"#FFF1EC\",\"bannerIcon\":\"https://p0.meituan.net/dztgdetailimages/427d9c96dd426b065da5b2e28db51ddd620.png\",\"bannerTextColor\":\"#FF4B10\",\"bannerTextSize\":11,\"buyBtnTitle\":\"购买\",\"dpAppJumpPrefix\":\"dianping://mrn\",\"mtAppJumpPrefix\":\"imeituan://www.meituan.com/mrn\",\"newLeadsGiftsTemplate\":\"预约到店赠送价值%s元%s\",\"oldLeadsGiftsTemplate\":\"预约到店赠送%s\",\"phoneResvBtnTitle\":\"致电商家预约看房型\",\"resvBtnTitle\":\"预约看房型\",\"resvJumpUrlPrefix\":\"mrn_biz=gc&mrn_entry=vg-mrn-reserve&mrn_component=customerpopup\",\"resvJumpUrlSuffix\":\"isTransparent=true&hideLoading=true&mrn_transparent=true&mrn_hideloading=true&mrn_hideNextNavBar=true\"}";
        LeadsDealBarConfig leadsDealBarConfig = JSON.parseObject(configStr, LeadsDealBarConfig.class);
        mockedLionConfigUtils.when(() -> LionConfigUtils.getLeadsDealMinMrnVersion()).thenReturn("0.5.13");
        mockedLionConfigUtils.when(() -> LionConfigUtils.getLeadsDealBarConfig(dealCtx)).thenReturn(leadsDealBarConfig);
        mockedDealUtils.when(() -> DealUtils.hasResvBenefits(dealCtx)).thenReturn(true);
        mockedRedirectUrls.when(() -> RedirectUrls.buildResvPopBtnUrl(dealCtx, leadsDealBarConfig)).thenReturn("resvUrl");
        mockedDealBuyHelper.when(() -> DealBuyHelper.getCanNotBuyButton(dealCtx)).thenReturn(null);
        // act
        leadsDealButtonBuilder.doBuild(dealCtx, chain);

        // assert
        assertEquals(1, dealCtx.getBuyBar().getBuyBtns().size());
        assertEquals(BuyBtnTypeEnum.RESV_DEAL.getCode(), dealCtx.getBuyBar().getBuyBtns().get(0).getDetailBuyType());
        assertEquals("resvUrl", dealCtx.getBuyBar().getBuyBtns().get(0).getRedirectUrl());
        assertEquals("预约看房型", dealCtx.getBuyBar().getBuyBtns().get(0).getBtnTitle());
    }

    /**
     * 测试美团App且有商家预约权益，但是mrn版本小于最低要求mrn版本
     */
    @Test
    public void testBuild_MtAppWithResv_LessThanMinMrnVersion() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx dealCtx = new DealCtx(envCtx);
        BestShopDTO bestShopDTO = new BestShopDTO();
        bestShopDTO.setPhoneNos(Lists.newArrayList());
        dealCtx.setBestShopResp(bestShopDTO);
        dealCtx.setMrnVersion("0.5.12");
        String configStr = "{\"bannerBackGroundColor\":\"#FFF1EC\",\"bannerIcon\":\"https://p0.meituan.net/dztgdetailimages/427d9c96dd426b065da5b2e28db51ddd620.png\",\"bannerTextColor\":\"#FF4B10\",\"bannerTextSize\":11,\"buyBtnTitle\":\"购买\",\"dpAppJumpPrefix\":\"dianping://mrn\",\"mtAppJumpPrefix\":\"imeituan://www.meituan.com/mrn\",\"newLeadsGiftsTemplate\":\"预约到店赠送价值%s元%s\",\"oldLeadsGiftsTemplate\":\"预约到店赠送%s\",\"phoneResvBtnTitle\":\"致电商家预约看房型\",\"resvBtnTitle\":\"预约看房型\", \"emptyResvBtnTitle\":\"不可预约\",\"resvJumpUrlPrefix\":\"mrn_biz=gc&mrn_entry=vg-mrn-reserve&mrn_component=customerpopup\",\"resvJumpUrlSuffix\":\"isTransparent=true&hideLoading=true&mrn_transparent=true&mrn_hideloading=true&mrn_hideNextNavBar=true\"}";
        LeadsDealBarConfig leadsDealBarConfig = JSON.parseObject(configStr, LeadsDealBarConfig.class);
        mockedLionConfigUtils.when(() -> LionConfigUtils.getLeadsDealMinMrnVersion()).thenReturn("0.5.13");
        mockedLionConfigUtils.when(() -> LionConfigUtils.getLeadsDealBarConfig(dealCtx)).thenReturn(leadsDealBarConfig);
        mockedDealUtils.when(() -> DealUtils.hasResvBenefits(dealCtx)).thenReturn(false);
        mockedDealBuyHelper.when(() -> DealBuyHelper.getCanNotBuyButton(dealCtx)).thenReturn(null);

        // act
        leadsDealButtonBuilder.doBuild(dealCtx, chain);

        // assert
        assertEquals(BuyBtnTypeEnum.RESV_DEAL.getCode(), dealCtx.getBuyBar().getBuyBtns().get(0).getDetailBuyType());
        assertEquals("不可预约", dealCtx.getBuyBar().getBuyBtns().get(0).getBtnTitle());
    }

}
