package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.PoiCateEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.MtPoiModel;
import com.meituan.service.mobile.sinai.client.model.PoiModelL;
import java.util.Arrays;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PoiModelHelperTest {

    /**
     * 测试 convertPoiModel 方法，当 poiModel 为 null 时
     */
    @Test
    public void testConvertPoiModelWhenPoiModelIsNull() throws Throwable {
        // arrange
        PoiModelL poiModel = null;
        // act
        MtPoiModel result = PoiModelHelper.convertPoiModel(poiModel);
        // assert
        assertNull(result);
    }

    /**
     * 测试 convertPoiModel 方法，当 poiModel 不为 null，但其 cate 属性为空或者不包含 PoiCateEnum.TRAINING.getValue() 时
     */
    @Test
    public void testConvertPoiModelWhenPoiModelIsNotNullButCateIsEmpty() throws Throwable {
        // arrange
        PoiModelL poiModel = new PoiModelL();
        poiModel.setCate(null);
        // act
        MtPoiModel result = PoiModelHelper.convertPoiModel(poiModel);
        // assert
        assertNotNull(result);
        assertTrue("Expected isShowPhoneNo to be true when cate is empty", result.isShowPhoneNo());
    }

    /**
     * 测试 convertPoiModel 方法，当 poiModel 不为 null，其 cate 属性包含 PoiCateEnum.TRAINING.getValue() 时
     */
    @Test
    public void testConvertPoiModelWhenPoiModelIsNotNullAndCateContainsTraining() throws Throwable {
        // arrange
        PoiModelL poiModel = new PoiModelL();
        poiModel.setCate(Arrays.asList(PoiCateEnum.TRAINING.getValue()));
        // act
        MtPoiModel result = PoiModelHelper.convertPoiModel(poiModel);
        // assert
        assertNotNull(result);
        assertFalse("Expected isShowPhoneNo to be false when cate contains TRAINING", result.isShowPhoneNo());
    }

    /**
     * 测试 buildRdploc 方法，当 poiModel 为 null 时
     */
    @Test
    public void testBuildRdplocWhenPoiModelIsNull() throws Throwable {
        // arrange
        PoiModelL poiModel = null;
        // act
        MtPoiModel result = PoiModelHelper.buildRdploc(poiModel);
        // assert
        assertNull(result);
    }

    /**
     * 测试 buildRdploc 方法，当 poiModel 不为 null 时
     */
    @Test
    public void testBuildRdplocWhenPoiModelIsNotNull() throws Throwable {
        // arrange
        PoiModelL poiModel = new PoiModelL();
        poiModel.setId(123L);
        poiModel.setPhone("123456789");
        poiModel.setName("test");
        poiModel.setAddress("test address");
        poiModel.setShowType("test showType");
        poiModel.setLongitude(123.456);
        poiModel.setLatitude(78.90);
        // act
        MtPoiModel result = PoiModelHelper.buildRdploc(poiModel);
        // assert
        assertNotNull(result);
        assertEquals(123, result.getPoiid());
        assertEquals("123456789", result.getPhone());
        assertEquals("test", result.getName());
        assertEquals("test address", result.getAddr());
        assertEquals("test showType", result.getShowType());
        assertEquals(123.456, result.getLng(), 0.001);
        assertEquals(78.90, result.getLat(), 0.001);
    }
}
