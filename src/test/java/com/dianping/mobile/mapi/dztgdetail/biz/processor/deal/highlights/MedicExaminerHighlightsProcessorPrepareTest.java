package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.IExaminerAbstractHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class MedicExaminerHighlightsProcessorPrepareTest {

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private FutureCtx futureCtx;

    @Mock
    private Future mockFuture;

    @InjectMocks
    private MedicExaminerHighlightsProcessor medicExaminerHighlightsProcessor;

    @Mock
    private IExaminerAbstractHandler defaultHandler;

    @Mock
    private IExaminerAbstractHandler specialHandler;

    @Before
    public void setUp() {
        when(dealCtx.getFutureCtx()).thenReturn(futureCtx);
    }

    /**
     * Test prepare method when QueryCenterWrapper returns null future
     */
    @Test
    public void testPrepare_WhenQueryCenterWrapperReturnsNull_ShouldHandleGracefully() throws Throwable {
        // arrange
        when(dealCtx.isMt()).thenReturn(true);
        when(dealCtx.getMtId()).thenReturn(123);
        when(queryCenterWrapper.preDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(null);
        // act
        medicExaminerHighlightsProcessor.prepare(dealCtx);
        // assert
        verify(futureCtx).setSingleDealGroupDtoFuture(null);
    }

    /**
     * Test prepare method with zero dealGroupId
     */
    @Test
    public void testPrepare_WhenDealGroupIdIsZero_ShouldStillProcess() throws Throwable {
        // arrange
        when(dealCtx.isMt()).thenReturn(true);
        when(dealCtx.getMtId()).thenReturn(0);
        when(queryCenterWrapper.preDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(mockFuture);
        // act
        medicExaminerHighlightsProcessor.prepare(dealCtx);
        // assert
        verify(queryCenterWrapper).preDealGroupDTO(argThat(request -> request.getDealGroupIds().contains(0L)));
        verify(futureCtx).setSingleDealGroupDtoFuture(mockFuture);
    }

    /**
     * Test prepare method with negative dealGroupId
     */
    @Test
    public void testPrepare_WhenDealGroupIdIsNegative_ShouldStillProcess() throws Throwable {
        // arrange
        when(dealCtx.isMt()).thenReturn(false);
        when(dealCtx.getDpId()).thenReturn(-1);
        when(queryCenterWrapper.preDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(mockFuture);
        // act
        medicExaminerHighlightsProcessor.prepare(dealCtx);
        // assert
        verify(queryCenterWrapper).preDealGroupDTO(argThat(request -> request.getDealGroupIds().contains(-1L)));
        verify(futureCtx).setSingleDealGroupDtoFuture(mockFuture);
    }

    /**
     * Test successful processing with default handler
     */
    @Test
    public void testProcess_SuccessWithDefaultHandler() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        FutureCtx futureCtx = new FutureCtx();
        ctx.setFutureCtx(futureCtx);
        Future future = mock(Future.class);
        futureCtx.setSingleDealGroupDtoFuture(future);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setServiceType("默认");
        dealGroupDTO.setCategory(categoryDTO);
        when(queryCenterWrapper.getDealGroupDTO(any(Future.class))).thenReturn(dealGroupDTO);
        MedicExaminerHighlightsProcessor.EXAMINER_SERVER_TYPE_HANDLER_MAP.put("默认", defaultHandler);
        // act
        medicExaminerHighlightsProcessor.process(ctx);
        // assert
        verify(queryCenterWrapper).getDealGroupDTO(future);
        verify(defaultHandler).execute(ctx);
        assertFalse(ctx.isQueryCenterHasError());
    }

    /**
     * Test when QueryCenterWrapper throws exception
     */
    @Test
    public void testProcess_QueryCenterWrapperException() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        FutureCtx futureCtx = new FutureCtx();
        ctx.setFutureCtx(futureCtx);
        Future future = mock(Future.class);
        futureCtx.setSingleDealGroupDtoFuture(future);
        when(queryCenterWrapper.getDealGroupDTO(any(Future.class))).thenThrow(new RuntimeException("Query center error"));
        // act
        medicExaminerHighlightsProcessor.process(ctx);
        // assert
        verify(queryCenterWrapper).getDealGroupDTO(future);
        assertTrue(ctx.isQueryCenterHasError());
        assertNull(ctx.getDealGroupDTO());
    }

    /**
     * Test when DealGroupDTO is null
     */
    @Test
    public void testProcess_NullDealGroupDTO() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        FutureCtx futureCtx = new FutureCtx();
        ctx.setFutureCtx(futureCtx);
        Future future = mock(Future.class);
        futureCtx.setSingleDealGroupDtoFuture(future);
        when(queryCenterWrapper.getDealGroupDTO(any(Future.class))).thenReturn(null);
        // act
        medicExaminerHighlightsProcessor.process(ctx);
        // assert
        verify(queryCenterWrapper).getDealGroupDTO(future);
        assertFalse(ctx.isQueryCenterHasError());
        verifyNoInteractions(defaultHandler);
        verifyNoInteractions(specialHandler);
    }

    /**
     * Test with special service type handler
     */
    @Test
    public void testProcess_SpecialServiceTypeHandler() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        FutureCtx futureCtx = new FutureCtx();
        ctx.setFutureCtx(futureCtx);
        Future future = mock(Future.class);
        futureCtx.setSingleDealGroupDtoFuture(future);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setServiceType("special");
        dealGroupDTO.setCategory(categoryDTO);
        when(queryCenterWrapper.getDealGroupDTO(any(Future.class))).thenReturn(dealGroupDTO);
        MedicExaminerHighlightsProcessor.EXAMINER_SERVER_TYPE_HANDLER_MAP.put("special", specialHandler);
        // act
        medicExaminerHighlightsProcessor.process(ctx);
        // assert
        verify(queryCenterWrapper).getDealGroupDTO(future);
        verify(specialHandler).execute(ctx);
        assertFalse(ctx.isQueryCenterHasError());
    }

    /**
     * Test when no handler found for service type
     */
    @Test
    public void testProcess_NoHandlerFound() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        FutureCtx futureCtx = new FutureCtx();
        ctx.setFutureCtx(futureCtx);
        Future future = mock(Future.class);
        futureCtx.setSingleDealGroupDtoFuture(future);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setServiceType("unknown");
        dealGroupDTO.setCategory(categoryDTO);
        when(queryCenterWrapper.getDealGroupDTO(any(Future.class))).thenReturn(dealGroupDTO);
        MedicExaminerHighlightsProcessor.EXAMINER_SERVER_TYPE_HANDLER_MAP.clear();
        // act
        medicExaminerHighlightsProcessor.process(ctx);
        // assert
        verify(queryCenterWrapper).getDealGroupDTO(future);
        verifyNoInteractions(defaultHandler);
        verifyNoInteractions(specialHandler);
    }
}
