package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PlayCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.model.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.DealGift;
import com.google.common.collect.Lists;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteResponse;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.apache.commons.lang.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealGiftAdaptor_BuildGiftsTest {

    @Mock
    private PlayCenterWrapper playCenterWrapper;

    @Mock
    private DealCtx ctx;

    @InjectMocks
    private DealGiftAdaptor dealGiftAdaptor;

    private FilterPlayActivityModel filterPlayActivityModel;

    private TaskInfoModel taskInfoModel;

    private PrizeInfoModel prizeInfoModel;

    @Before
    public void setUp() {
        filterPlayActivityModel = new FilterPlayActivityModel();
        taskInfoModel = new TaskInfoModel();
        prizeInfoModel = new PrizeInfoModel();
    }

    private String invokeGetResult(PlayExecuteResponse response) throws Exception {
        Method method = DealGiftAdaptor.class.getDeclaredMethod("getResult", PlayExecuteResponse.class);
        method.setAccessible(true);
        return (String) method.invoke(dealGiftAdaptor, response);
    }

    private String getResult(PlayExecuteResponse response) {
        if (response == null || StringUtils.isEmpty(response.getResult())) {
            return null;
        }
        return response.getResult();
    }

    /**
     * Helper method to invoke the private buildGiftsForSummerPlay method using reflection.
     */
    private List<DealGift> invokePrivateBuildGiftsForSummerPlay(DealCtx ctx, FilterPlayActivityModel filterPlayActivityModel) throws Exception {
        Method method = DealGiftAdaptor.class.getDeclaredMethod("buildGiftsForSummerPlay", DealCtx.class, FilterPlayActivityModel.class);
        method.setAccessible(true);
        return (List<DealGift>) method.invoke(dealGiftAdaptor, ctx, filterPlayActivityModel);
    }

    @Test
    public void testBuildGiftsWhenPlayActivityModelsIsNull() throws Throwable {
        List<DealGift> result = dealGiftAdaptor.buildGifts(null);
        assertNull(result);
    }

    @Test
    public void testBuildGiftsWhenPlayInfoIsNull() throws Throwable {
        List<PlayActivityModel> playActivityModels = new ArrayList<>();
        List<DealGift> result = dealGiftAdaptor.buildGifts(playActivityModels);
        assertNull(result);
    }

    @Test
    public void testBuildGiftsWhenTaskInfoListIsNull() throws Throwable {
        List<PlayActivityModel> playActivityModels = new ArrayList<>();
        PlayActivityModel playActivityModel = new PlayActivityModel();
        playActivityModel.setPlayInfo(new PlayInfoModel());
        playActivityModels.add(playActivityModel);
        List<DealGift> result = dealGiftAdaptor.buildGifts(playActivityModels);
        assertNull(result);
    }

    @Test
    public void testBuildGiftsWhenPrizeInfoListIsNull() throws Throwable {
        List<PlayActivityModel> playActivityModels = new ArrayList<>();
        PlayActivityModel playActivityModel = new PlayActivityModel();
        PlayInfoModel playInfoModel = new PlayInfoModel();
        TaskInfoModel taskInfoModel = new TaskInfoModel();
        playInfoModel.setTaskInfoList(new ArrayList<>());
        playInfoModel.getTaskInfoList().add(taskInfoModel);
        playActivityModel.setPlayInfo(playInfoModel);
        playActivityModels.add(playActivityModel);
        List<DealGift> result = dealGiftAdaptor.buildGifts(playActivityModels);
        assertNull(result);
    }

    @Test
    public void testBuildGiftsWhenPrizeCountIsOne() throws Throwable {
        List<PlayActivityModel> playActivityModels = new ArrayList<>();
        PlayActivityModel playActivityModel = new PlayActivityModel();
        PlayInfoModel playInfoModel = new PlayInfoModel();
        // Set a non-null activityId
        playInfoModel.setActivityId(123L);
        TaskInfoModel taskInfoModel = new TaskInfoModel();
        PrizeInfoModel prizeInfoModel = new PrizeInfoModel();
        prizeInfoModel.setPrizeName("test");
        prizeInfoModel.setPrizeImage("test");
        List<PrizeInfoModel> prizeInfoList = new ArrayList<>();
        prizeInfoList.add(prizeInfoModel);
        taskInfoModel.setPrizeInfoList(prizeInfoList);
        taskInfoModel.setPrizeCount(1);
        List<TaskInfoModel> taskInfoList = new ArrayList<>();
        taskInfoList.add(taskInfoModel);
        playInfoModel.setTaskInfoList(taskInfoList);
        playActivityModel.setPlayInfo(playInfoModel);
        playActivityModels.add(playActivityModel);
        List<DealGift> result = dealGiftAdaptor.buildGifts(playActivityModels);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("赠品", result.get(0).getSpecificTag());
    }

    @Test
    public void testBuildGiftsWhenPrizeCountIsNotOne() throws Throwable {
        List<PlayActivityModel> playActivityModels = new ArrayList<>();
        PlayActivityModel playActivityModel = new PlayActivityModel();
        PlayInfoModel playInfoModel = new PlayInfoModel();
        // Set a non-null activityId
        playInfoModel.setActivityId(456L);
        TaskInfoModel taskInfoModel = new TaskInfoModel();
        PrizeInfoModel prizeInfoModel = new PrizeInfoModel();
        prizeInfoModel.setPrizeName("test");
        prizeInfoModel.setPrizeImage("test");
        List<PrizeInfoModel> prizeInfoList = new ArrayList<>();
        prizeInfoList.add(prizeInfoModel);
        taskInfoModel.setPrizeInfoList(prizeInfoList);
        taskInfoModel.setPrizeCount(2);
        List<TaskInfoModel> taskInfoList = new ArrayList<>();
        taskInfoList.add(taskInfoModel);
        playInfoModel.setTaskInfoList(taskInfoList);
        playActivityModel.setPlayInfo(playInfoModel);
        playActivityModels.add(playActivityModel);
        List<DealGift> result = dealGiftAdaptor.buildGifts(playActivityModels);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("赠品x2", result.get(0).getSpecificTag());
    }

    @Test
    public void testBuildGiftsWhenOnlyPrizeInfoModelIsNull() throws Throwable {
        List<PlayActivityModel> playActivityModels = new ArrayList<>();
        PlayActivityModel playActivityModel = new PlayActivityModel();
        PlayInfoModel playInfoModel = new PlayInfoModel();
        TaskInfoModel taskInfoModel = new TaskInfoModel();
        taskInfoModel.setPrizeInfoList(new ArrayList<>());
        playInfoModel.setTaskInfoList(new ArrayList<>());
        playActivityModel.setPlayInfo(playInfoModel);
        playActivityModels.add(playActivityModel);
        List<DealGift> result = dealGiftAdaptor.buildGifts(playActivityModels);
        assertNull(result);
    }

    /**
     * 测试暑促活动不存在且惊喜买赠活动不存在
     */
    @Test
    public void testBuildGiftsWhenNoActivityExists() throws Throwable {
        // arrange
        when(playCenterWrapper.validSummerPlayExist(ctx, null)).thenReturn(false);
        // act
        List<DealGift> result = dealGiftAdaptor.buildGifts(ctx, null, null, null);
        // assert
        assertNull(result);
    }

    /**
     * 测试暑促活动不存在但惊喜买赠活动存在
     */
    @Test
    public void testBuildGiftsWhenSummerActivityNotExists() throws Throwable {
        // arrange
        List<PlayActivityModel> playActivityModels = new ArrayList<>();
        PlayActivityModel playActivityModel = new PlayActivityModel();
        PlayInfoModel playInfoModel = new PlayInfoModel();
        // Set a non-null activityId
        playInfoModel.setActivityId(456L);
        TaskInfoModel taskInfoModel = new TaskInfoModel();
        PrizeInfoModel prizeInfoModel = new PrizeInfoModel();
        prizeInfoModel.setPrizeName("test");
        prizeInfoModel.setPrizeImage("test");
        List<PrizeInfoModel> prizeInfoList = new ArrayList<>();
        prizeInfoList.add(prizeInfoModel);
        taskInfoModel.setPrizeInfoList(prizeInfoList);
        taskInfoModel.setPrizeCount(2);
        List<TaskInfoModel> taskInfoList = new ArrayList<>();
        taskInfoList.add(taskInfoModel);
        playInfoModel.setTaskInfoList(taskInfoList);
        playActivityModel.setPlayInfo(playInfoModel);
        playActivityModels.add(playActivityModel);
        when(playCenterWrapper.validSummerPlayExist(ctx, null)).thenReturn(false);
        // act
        List<DealGift> result = dealGiftAdaptor.buildGifts(ctx, null, playActivityModels, null);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("赠品x2", result.get(0).getSpecificTag());
    }

    /**
     * 测试暑促活动存在
     */
    @Test
    public void testBuildGiftsWhenSummerActivitiesExist() throws Throwable {
        // arrange
        FilterPlayActivityModel filterPlayActivityModel = new FilterPlayActivityModel();
        filterPlayActivityModel.setActivityId("123");
        TaskInfoModel taskInfoModel = new TaskInfoModel();
        PrizeInfoModel prizeInfoModel = new PrizeInfoModel();
        List<PrizeInfoModel> prizeInfoList = Lists.newArrayList(prizeInfoModel);
        taskInfoModel.setPrizeCount(1);
        taskInfoModel.setTaskEndTime(1723105714L);
        taskInfoModel.setPrizeInfoList(prizeInfoList);
        prizeInfoModel.setPrizeImage("image");
        prizeInfoModel.setPrizeName("name");
        filterPlayActivityModel.setTaskInfo(taskInfoModel);
        when(playCenterWrapper.validSummerPlayExist(ctx, filterPlayActivityModel)).thenReturn(true);
        // act
        List<DealGift> result = dealGiftAdaptor.buildGifts(ctx, filterPlayActivityModel, null, null);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("image", result.get(0).getThumbnail());
    }

    /**
     * Test when response is null
     */
    @Test
    public void testGetResult_WhenResponseIsNull() throws Throwable {
        // arrange
        PlayExecuteResponse response = null;
        // act
        String result = invokeGetResult(response);
        // assert
        assertNull(result);
    }

    /**
     * Test when response.getResult() returns null
     */
    @Test
    public void testGetResult_WhenResponseResultIsNull() throws Throwable {
        // arrange
        PlayExecuteResponse response = mock(PlayExecuteResponse.class);
        when(response.getResult()).thenReturn(null);
        // act
        String result = invokeGetResult(response);
        // assert
        assertNull(result);
    }

    /**
     * Test when response.getResult() returns empty string
     */
    @Test
    public void testGetResult_WhenResponseResultIsEmpty() throws Throwable {
        // arrange
        PlayExecuteResponse response = mock(PlayExecuteResponse.class);
        when(response.getResult()).thenReturn("");
        // act
        String result = invokeGetResult(response);
        // assert
        assertNull(result);
    }

    /**
     * Test when response.getResult() returns valid string
     */
    @Test
    public void testGetResult_WhenResponseResultIsValid() throws Throwable {
        // arrange
        String expectedResult = "valid result";
        PlayExecuteResponse response = mock(PlayExecuteResponse.class);
        when(response.getResult()).thenReturn(expectedResult);
        // act
        String result = invokeGetResult(response);
        // assert
        assertEquals(expectedResult, result);
    }

    /**
     * Test case: filterPlayActivityModel is null
     * Expected: Should return null
     */
    @Test
    public void testBuildGiftsForSummerPlay_WhenFilterPlayActivityModelIsNull() throws Throwable {
        // arrange
        FilterPlayActivityModel nullModel = null;
        // act
        List<DealGift> result = invokePrivateBuildGiftsForSummerPlay(ctx, nullModel);
        // assert
        assertNull(result);
    }

    /**
     * Test case: taskInfo is null
     * Expected: Should return null
     */
    @Test
    public void testBuildGiftsForSummerPlay_WhenTaskInfoIsNull() throws Throwable {
        // arrange
        filterPlayActivityModel.setTaskInfo(null);
        when(playCenterWrapper.validSummerPlayExist(ctx, filterPlayActivityModel)).thenReturn(true);
        // act
        List<DealGift> result = invokePrivateBuildGiftsForSummerPlay(ctx, filterPlayActivityModel);
        // assert
        assertNull(result);
    }

    /**
     * Test case: prizeInfoList is empty
     * Expected: Should return null
     */
    @Test
    public void testBuildGiftsForSummerPlay_WhenPrizeInfoListIsEmpty() throws Throwable {
        // arrange
        taskInfoModel.setPrizeInfoList(new ArrayList<>());
        filterPlayActivityModel.setTaskInfo(taskInfoModel);
        when(playCenterWrapper.validSummerPlayExist(ctx, filterPlayActivityModel)).thenReturn(true);
        // act
        List<DealGift> result = invokePrivateBuildGiftsForSummerPlay(ctx, filterPlayActivityModel);
        // assert
        assertNull(result);
    }

    /**
     * Test case: validSummerPlayExist returns false
     * Expected: Should return null
     */
    @Test
    public void testBuildGiftsForSummerPlay_WhenValidSummerPlayExistReturnsFalse() throws Throwable {
        // arrange
        List<PrizeInfoModel> prizeInfoList = new ArrayList<>();
        prizeInfoList.add(prizeInfoModel);
        taskInfoModel.setPrizeInfoList(prizeInfoList);
        filterPlayActivityModel.setTaskInfo(taskInfoModel);
        when(playCenterWrapper.validSummerPlayExist(ctx, filterPlayActivityModel)).thenReturn(false);
        // act
        List<DealGift> result = invokePrivateBuildGiftsForSummerPlay(ctx, filterPlayActivityModel);
        // assert
        assertNull(result);
    }

    /**
     * Test case: Successful path with valid data
     * Expected: Should return populated DealGift list
     */
    @Test
    public void testBuildGiftsForSummerPlay_Success() throws Throwable {
        // arrange
        String prizeImage = "test_image.jpg";
        String prizeName = "Test Prize";
        long taskEndTime = System.currentTimeMillis();
        int prizeCount = 5;
        String activityId = "12345";
        prizeInfoModel.setPrizeImage(prizeImage);
        prizeInfoModel.setPrizeName(prizeName);
        List<PrizeInfoModel> prizeInfoList = new ArrayList<>();
        prizeInfoList.add(prizeInfoModel);
        taskInfoModel.setPrizeInfoList(prizeInfoList);
        taskInfoModel.setTaskEndTime(taskEndTime);
        taskInfoModel.setPrizeCount(prizeCount);
        filterPlayActivityModel.setTaskInfo(taskInfoModel);
        filterPlayActivityModel.setActivityId(activityId);
        when(playCenterWrapper.validSummerPlayExist(ctx, filterPlayActivityModel)).thenReturn(true);
        // act
        List<DealGift> result = invokePrivateBuildGiftsForSummerPlay(ctx, filterPlayActivityModel);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealGift dealGift = result.get(0);
        assertEquals(prizeImage, dealGift.getThumbnail());
        assertEquals(prizeName, dealGift.getTitle());
        assertEquals("核销后发放", dealGift.getProductTag());
        assertEquals("暑促活动专享", dealGift.getSpecificTag());
        assertEquals(prizeCount, dealGift.getCouponNum());
        assertEquals(Long.parseLong(activityId), dealGift.getActivityId());
        assertNotNull(dealGift.getTimeDesc());
    }

    /**
     * Test buildGifts when onlyPrizeInfoModel is null
     *
     * Case: playActivityModels has valid data but prizeInfoList's first element is null
     */
    @Test
    public void testBuildGifts_WhenOnlyPrizeInfoModelIsNull() throws Throwable {
        // arrange
        List<PlayActivityModel> playActivityModels = new ArrayList<>();
        PlayActivityModel playActivityModel = new PlayActivityModel();
        PlayInfoModel playInfo = new PlayInfoModel();
        TaskInfoModel taskInfoModel = new TaskInfoModel();
        // Set up taskInfoModel with null prize info
        taskInfoModel.setPrizeInfoList(Collections.singletonList(null));
        // Set up the object chain
        playInfo.setTaskInfoList(Collections.singletonList(taskInfoModel));
        playActivityModel.setPlayInfo(playInfo);
        playActivityModels.add(playActivityModel);
        // act
        List<DealGift> result = dealGiftAdaptor.buildGifts(playActivityModels);
        // assert
        assertNull("Result should be null when onlyPrizeInfoModel is null", result);
    }

    /**
     * Test buildGifts when input playActivityModels is null
     */
    @Test
    public void testBuildGifts_WhenPlayActivityModelsIsNull() throws Throwable {
        // arrange
        List<PlayActivityModel> playActivityModels = null;
        // act
        List<DealGift> result = dealGiftAdaptor.buildGifts(playActivityModels);
        // assert
        assertNull("Result should be null when playActivityModels is null", result);
    }

    /**
     * Test buildGifts when playActivityModels is empty
     */
    @Test
    public void testBuildGifts_WhenPlayActivityModelsIsEmpty() throws Throwable {
        // arrange
        List<PlayActivityModel> playActivityModels = new ArrayList<>();
        // act
        List<DealGift> result = dealGiftAdaptor.buildGifts(playActivityModels);
        // assert
        assertNull("Result should be null when playActivityModels is empty", result);
    }

    /**
     * Test buildGifts when playInfo is null
     */
    @Test
    public void testBuildGifts_WhenPlayInfoIsNull() throws Throwable {
        // arrange
        List<PlayActivityModel> playActivityModels = new ArrayList<>();
        PlayActivityModel playActivityModel = new PlayActivityModel();
        playActivityModel.setPlayInfo(null);
        playActivityModels.add(playActivityModel);
        // act
        List<DealGift> result = dealGiftAdaptor.buildGifts(playActivityModels);
        // assert
        assertNull("Result should be null when playInfo is null", result);
    }

    /**
     * Test buildGifts when taskInfoList is empty
     */
    @Test
    public void testBuildGifts_WhenTaskInfoListIsEmpty() throws Throwable {
        // arrange
        List<PlayActivityModel> playActivityModels = new ArrayList<>();
        PlayActivityModel playActivityModel = new PlayActivityModel();
        PlayInfoModel playInfo = new PlayInfoModel();
        playInfo.setTaskInfoList(new ArrayList<>());
        playActivityModel.setPlayInfo(playInfo);
        playActivityModels.add(playActivityModel);
        // act
        List<DealGift> result = dealGiftAdaptor.buildGifts(playActivityModels);
        // assert
        assertNull("Result should be null when taskInfoList is empty", result);
    }

    /**
     * Test buildGifts when taskInfoList's first element is null
     */
    @Test
    public void testBuildGifts_WhenFirstTaskInfoIsNull() throws Throwable {
        // arrange
        List<PlayActivityModel> playActivityModels = new ArrayList<>();
        PlayActivityModel playActivityModel = new PlayActivityModel();
        PlayInfoModel playInfo = new PlayInfoModel();
        playInfo.setTaskInfoList(Collections.singletonList(null));
        playActivityModel.setPlayInfo(playInfo);
        playActivityModels.add(playActivityModel);
        // act
        List<DealGift> result = dealGiftAdaptor.buildGifts(playActivityModels);
        // assert
        assertNull("Result should be null when first taskInfo is null", result);
    }

    /**
     * Test buildGifts with normal valid input but onlyPrizeInfoModel is null
     */
    @Test
    public void testBuildGifts_WithValidInput_ButNullPrizeInfo() throws Throwable {
        // arrange
        List<PlayActivityModel> playActivityModels = new ArrayList<>();
        PlayActivityModel playActivityModel = new PlayActivityModel();
        PlayInfoModel playInfo = new PlayInfoModel();
        playInfo.setEndTime(1000L);
        TaskInfoModel taskInfoModel = new TaskInfoModel();
        taskInfoModel.setPrizeCount(2);
        taskInfoModel.setPrizeInfoList(Collections.singletonList(null));
        playInfo.setTaskInfoList(Collections.singletonList(taskInfoModel));
        playActivityModel.setPlayInfo(playInfo);
        playActivityModels.add(playActivityModel);
        // act
        List<DealGift> result = dealGiftAdaptor.buildGifts(playActivityModels);
        // assert
        assertNull("Result should be null when prizeInfo is null even with other valid data", result);
    }
}
