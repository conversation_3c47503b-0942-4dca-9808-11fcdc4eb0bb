package com.dianping.mobile.mapi.dztgdetail.helper;

import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.List;
import com.dianping.mobile.mapi.dztgdetail.util.ObjectCompareUtils;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealCompareHelper_DifferFeaturesTest {

    /**
     * 测试differFeatures方法，当新Features列表和旧Features列表都为null时，应返回true
     */
    @Test
    public void testDifferFeaturesBothNull() throws Throwable {
        // arrange
        List<String> newFeatures = null;
        List<String> oldFeatures = null;
        int dealId = 1;
        // act
        boolean result = DealCompareHelper.differFeatures(newFeatures, oldFeatures, dealId);
        // assert
        assertTrue(result);
    }

    /**
     * 测试differFeatures方法，当新Features列表为null，旧Features列表不为null时，应返回false
     */
    @Test
    public void testDifferFeaturesNewNull() throws Throwable {
        // arrange
        List<String> newFeatures = null;
        List<String> oldFeatures = Arrays.asList("feature1", "feature2");
        int dealId = 1;
        // act
        boolean result = DealCompareHelper.differFeatures(newFeatures, oldFeatures, dealId);
        // assert
        assertFalse(result);
    }

    /**
     * 测试differFeatures方法，当新Features列表不为null，旧Features列表为null时，应返回false
     */
    @Test
    public void testDifferFeaturesOldNull() throws Throwable {
        // arrange
        List<String> newFeatures = Arrays.asList("feature1", "feature2");
        List<String> oldFeatures = null;
        int dealId = 1;
        // act
        boolean result = DealCompareHelper.differFeatures(newFeatures, oldFeatures, dealId);
        // assert
        assertFalse(result);
    }

    /**
     * 测试differFeatures方法，当新Features列表和旧Features列表大小不同时，应返回false
     */
    @Test
    public void testDifferFeaturesSizeNotEqual() throws Throwable {
        // arrange
        List<String> newFeatures = Arrays.asList("feature1", "feature2");
        List<String> oldFeatures = Arrays.asList("feature1");
        int dealId = 1;
        // act
        boolean result = DealCompareHelper.differFeatures(newFeatures, oldFeatures, dealId);
        // assert
        assertFalse(result);
    }

    /**
     * 测试differFeatures方法，当新Features列表和旧Features列表大小相同，但元素不同时，应返回true
     */
    @Test
    public void testDifferFeaturesElementsNotEqual() throws Throwable {
        // arrange
        List<String> newFeatures = Arrays.asList("feature1", "feature2");
        List<String> oldFeatures = Arrays.asList("feature1", "feature3");
        int dealId = 1;
        // act
        boolean result = DealCompareHelper.differFeatures(newFeatures, oldFeatures, dealId);
        // assert
        // Corrected expectation based on the method's behavior
        assertTrue(result);
    }

    /**
     * 测试differFeatures方法，当新Features列表和旧Features列表完全相同时，应返回true
     */
    @Test
    public void testDifferFeaturesEqual() throws Throwable {
        // arrange
        List<String> newFeatures = Arrays.asList("feature1", "feature2");
        List<String> oldFeatures = Arrays.asList("feature1", "feature2");
        int dealId = 1;
        // act
        boolean result = DealCompareHelper.differFeatures(newFeatures, oldFeatures, dealId);
        // assert
        assertTrue(result);
    }
}
