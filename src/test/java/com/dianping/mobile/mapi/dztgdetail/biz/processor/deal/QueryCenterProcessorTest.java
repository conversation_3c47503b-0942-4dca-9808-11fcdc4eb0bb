package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.biz.service.cellarservice.dealdetail.DealGroupDTOCellarService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.request.BaseRequest;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.Future;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
@RunWith(MockitoJUnitRunner.class)
public class QueryCenterProcessorTest {

    @InjectMocks
    private QueryCenterProcessor queryCenterProcessor;

    @Mock
    private DealGroupDTOCellarService dealGroupDTOCellarService;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试prepare方法正常情况
     */
    @Test
    public void testPrepareNormalCase() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setDpId(1);
        ctx.setMtId(2);
        ctx.setFutureCtx(new FutureCtx());
        Future future = mock(Future.class);
        when(queryCenterWrapper.preDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(future);
        // act
        queryCenterProcessor.prepare(ctx);
        // assert
        verify(queryCenterWrapper, times(1)).preDealGroupDTO(any(QueryByDealGroupIdRequest.class));
        assertSame(future, ctx.getFutureCtx().getSingleDealGroupDtoFuture());
    }

    /**
     * 测试prepare方法异常情况
     */
    @Test(expected = RuntimeException.class)
    public void testPrepareExceptionCase() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setDpId(1);
        ctx.setMtId(2);
        ctx.setFutureCtx(new FutureCtx());
        when(queryCenterWrapper.preDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenThrow(RuntimeException.class);
        // act
        queryCenterProcessor.prepare(ctx);
        // assert
        verify(queryCenterWrapper, times(1)).preDealGroupDTO(any(QueryByDealGroupIdRequest.class));
    }

    /**
     * 测试 process 方法正常情况
     */
    @Test
    @Ignore
    public void testProcessNormal() throws TException {
        // arrange
        DealCtx dealCtxMock = mock(DealCtx.class);
        DealBaseReq baseRequest = new DealBaseReq();
        baseRequest.setDealgroupid(123);
        dealCtxMock.setDealBaseReq(baseRequest);
        FutureCtx futureCtx = new FutureCtx();
        futureCtx.setSingleDealGroupDtoFuture(null);
        when(dealCtxMock.getFutureCtx()).thenReturn(futureCtx);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        // act
        queryCenterProcessor.process(dealCtxMock);

        // assert
        assertNotNull(dealGroupDTO);
    }
}
