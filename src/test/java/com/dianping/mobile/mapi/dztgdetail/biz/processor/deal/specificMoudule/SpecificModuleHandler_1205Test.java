package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.DealCategoryEnum;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.EducationDealAttrUtils;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.BaseDisplayItemVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailDisplayUnitVO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import org.apache.commons.collections.CollectionUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class SpecificModuleHandler_1205Test {

    private SpecificModuleHandler_1205 specificModuleHandler_1205 = new SpecificModuleHandler_1205();

    @InjectMocks
    private SpecificModuleHandler_1205 specificModuleHandler;

    @Spy
    private EducationDealAttrUtils educationDealAttrUtils;

    /**
     * 使用反射调用私有方法 getGiftProduct
     */
    private DealDetailDisplayUnitVO invokePrivateGetGiftProduct(SpecificModuleCtx context) throws Throwable {
        try {
            Method method = SpecificModuleHandler_1205.class.getDeclaredMethod("getGiftProduct", SpecificModuleCtx.class);
            method.setAccessible(true);
            return (DealDetailDisplayUnitVO) method.invoke(specificModuleHandler, context);
        } catch (Exception e) {
            throw e.getCause();
        }
    }

    /**
     * 测试 identity 方法是否能正确返回 DealCategoryEnum.EDU_1205.getDealCategoryId() 的结果
     */
    @Test
    public void testIdentity() {
        // arrange
        SpecificModuleHandler_1205 handler = new SpecificModuleHandler_1205();
        Long expected = DealCategoryEnum.EDU_1205.getDealCategoryId();
        // act
        String result = handler.identity();
        // assert
        Assert.assertEquals(expected.toString(), result);
    }

    /**
     * Tests the buildDisplayItem method to ensure it returns the correct BaseDisplayItemVO object
     * when provided with specific name and detail inputs.
     */
    @Test
    public void testBuildDisplayItem() throws Throwable {
        // arrange
        String name = "testName";
        String detail = "testDetail";
        // Use reflection to invoke the private method
        Method method = SpecificModuleHandler_1205.class.getDeclaredMethod("buildDisplayItem", String.class, String.class);
        method.setAccessible(true);
        // act
        BaseDisplayItemVO result = (BaseDisplayItemVO) method.invoke(specificModuleHandler_1205, name, detail);
        // assert
        assertEquals(name, result.getName());
        assertEquals(detail, result.getDetail());
    }

    /**
     * 测试边界场景：DealGroupDTO 为空
     */
    @Test
    public void testGetGiftProduct_DealGroupDTONull() throws Throwable {
        // arrange
        SpecificModuleCtx context = new SpecificModuleCtx();
        context.setDealGroupDTO(null);
        // act
        DealDetailDisplayUnitVO result = invokePrivateGetGiftProduct(context);
        // assert
        assertNull(result);
    }
}
