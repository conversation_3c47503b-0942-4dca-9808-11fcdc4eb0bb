package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.UnifiedCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedModuleExtraReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedModuleExtraFacadeTest {

    @InjectMocks
    private UnifiedModuleExtraFacade unifiedModuleExtraFacade;

    private UnifiedModuleExtraReq request;

    private List<ModuleConfigDo> moduleConfigDoList;

    @Mock
    private LionConfigUtils lionConfigUtils;

    @Before
    public void setUp() {
        request = new UnifiedModuleExtraReq();
        ModuleConfigDo moduleConfigDo = new ModuleConfigDo();
        moduleConfigDo.setValue("tuandeal_newtuandealtab_reviews");
        moduleConfigDoList = Arrays.asList(moduleConfigDo);
    }

    private UnifiedModuleExtraReq createRequest() {
        UnifiedModuleExtraReq request = new UnifiedModuleExtraReq();
        // Additional setup can be added here if necessary
        return request;
    }

    private UnifiedCtx createUnifiedCtx() {
        EnvCtx envCtx = new EnvCtx();
        return new UnifiedCtx(envCtx);
    }

    private List<ModuleConfigDo> createResultWithOneItem() {
        List<ModuleConfigDo> result = new ArrayList<>();
        ModuleConfigDo moduleConfigDo = new ModuleConfigDo();
        moduleConfigDo.setKey("key");
        moduleConfigDo.setValue("value");
        result.add(moduleConfigDo);
        return result;
    }

    @Test
    public void testFilterFromCreateOrderPreviewRequestIsNull() throws Throwable {
        List<ModuleConfigDo> result = unifiedModuleExtraFacade.filterFromCreateOrderPreview(null, moduleConfigDoList);
        assertEquals(moduleConfigDoList, result);
    }

    @Test
    public void testFilterFromCreateOrderPreviewNotFromCreateOrderPreview() throws Throwable {
        request.setPageSource("not_create_order_preview");
        List<ModuleConfigDo> result = unifiedModuleExtraFacade.filterFromCreateOrderPreview(request, moduleConfigDoList);
        assertEquals(moduleConfigDoList, result);
    }

    @Test
    public void testFilterFromCreateOrderPreviewFromCreateOrderPreviewButListIsEmpty() throws Throwable {
        request.setPageSource(RequestSourceEnum.CREATE_ORDER_PREVIEW.getSource());
        List<ModuleConfigDo> result = unifiedModuleExtraFacade.filterFromCreateOrderPreview(request, null);
        assertEquals(null, result);
    }

    @Test
    public void testFilterFromCreateOrderPreviewFromCreateOrderPreviewAndListIsNotEmptyButNotInReviewerTabConfig() throws Throwable {
        request.setPageSource(RequestSourceEnum.CREATE_ORDER_PREVIEW.getSource());
        try (MockedStatic<LionConfigUtils> mockedStatic = Mockito.mockStatic(LionConfigUtils.class)) {
            mockedStatic.when(LionConfigUtils::getFilterReviewerTabConfig).thenReturn(new HashSet<>());
            List<ModuleConfigDo> result = unifiedModuleExtraFacade.filterFromCreateOrderPreview(request, moduleConfigDoList);
            assertEquals(moduleConfigDoList, result);
        }
    }

    @Test
    public void testFilterFromCreateOrderPreviewFromCreateOrderPreviewAndListIsNotEmptyAndInReviewerTabConfig() throws Throwable {
        request.setPageSource(RequestSourceEnum.CREATE_ORDER_PREVIEW.getSource());
        try (MockedStatic<LionConfigUtils> mockedStatic = Mockito.mockStatic(LionConfigUtils.class)) {
            mockedStatic.when(LionConfigUtils::getFilterReviewerTabConfig).thenReturn(new HashSet<>(Arrays.asList("tuandeal_newtuandealtab_reviews")));
            List<ModuleConfigDo> result = unifiedModuleExtraFacade.filterFromCreateOrderPreview(request, moduleConfigDoList);
            assertEquals(0, result.size());
        }
    }

    @Test
    public void testResetItemIndexForCaiXi_ResultEmpty() throws Throwable {
        UnifiedModuleExtraReq request = createRequest();
        UnifiedCtx unifiedCtx = createUnifiedCtx();
        int publishCategoryId = 1;
        List<ModuleConfigDo> result = new ArrayList<>();
        List<ModuleConfigDo> actual = unifiedModuleExtraFacade.resetItemIndexForCaiXi(request, unifiedCtx, publishCategoryId, result);
        assertEquals(0, actual.size());
    }

    @Test
    public void testResetItemIndexForCaiXi_NotCaXi() throws Throwable {
        UnifiedModuleExtraReq request = createRequest();
        request.setPageSource("notCaXi");
        UnifiedCtx unifiedCtx = createUnifiedCtx();
        int publishCategoryId = 1;
        List<ModuleConfigDo> result = createResultWithOneItem();
        List<ModuleConfigDo> actual = unifiedModuleExtraFacade.resetItemIndexForCaiXi(request, unifiedCtx, publishCategoryId, result);
        assertEquals(1, actual.size());
        assertEquals("key", actual.get(0).getKey());
        assertEquals("value", actual.get(0).getValue());
    }
}
