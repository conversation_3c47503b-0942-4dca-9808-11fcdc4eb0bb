package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ReserveProductWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.google.common.collect.Lists;
import com.sankuai.trade.general.reserve.response.ReserveResponse;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

/**
 * @author: wuwenqiang
 * @create: 2024-12-17
 * @description:
 */
@RunWith(MockitoJUnitRunner.class)
public class RemindBuilderServiceTest {

    @Mock
    private ReserveProductWrapper reserveProductWrapper;
    @Mock
    private DealCtx ctx;

    @InjectMocks
    private RemindBuilderService remindBuilderService;

    private MockedStatic<DealAttrHelper> dealAttrHelperMockedStatic;
    private MockedStatic<DealCtxHelper> dealCtxHelperMockedStatic;
    private MockedStatic<Lion> lionMockedStatic;
    private MockedStatic<ReserveProductWrapper> reserveProductWrapperMockedStatic;

    @Before
    public void setUp() {
        dealAttrHelperMockedStatic = Mockito.mockStatic(DealAttrHelper.class);
        dealCtxHelperMockedStatic = Mockito.mockStatic(DealCtxHelper.class);
        lionMockedStatic = Mockito.mockStatic(Lion.class);
        reserveProductWrapperMockedStatic = Mockito.mockStatic(ReserveProductWrapper.class);
    }

    @After
    public void after() {
        dealAttrHelperMockedStatic.close();
        dealCtxHelperMockedStatic.close();
        lionMockedStatic.close();
        reserveProductWrapperMockedStatic.close();
    }

    @Test
    public void testGetReservationInfo_NeedReservation_Online() {
        when(ctx.getAttrs()).thenReturn(Lists.newArrayList(new AttributeDTO()));
        when(ctx.getCategoryId()).thenReturn(1);
        dealAttrHelperMockedStatic.when(() -> DealAttrHelper.needReservation(ctx.getAttrs())).thenReturn(true);
        dealAttrHelperMockedStatic.when(() -> DealAttrHelper.isSupportHomeService(ctx.getAttrs())).thenReturn(false);
        dealAttrHelperMockedStatic.when(() -> DealAttrHelper.isSupportShopService(ctx.getAttrs())).thenReturn(false);
        dealCtxHelperMockedStatic.when(() -> DealCtxHelper.isPreOrderDeal(ctx)).thenReturn(false);
        reserveProductWrapperMockedStatic.when(() -> ReserveProductWrapper.reserveAfterPurchase(ctx.getCategoryId())).thenReturn(true);
        when(reserveProductWrapper.getFutureResult(ctx)).thenReturn(ReserveResponse.createSuccessResponse(true));

        List<String> result = remindBuilderService.getReservationInfo(ctx);

        assertEquals(1, result.size());
        assertEquals("在线预约", result.get(0));
    }

    @Test
    public void testGetReservationInfo_NeedReservation_Online_Preorder() {
        when(ctx.getAttrs()).thenReturn(Lists.newArrayList(new AttributeDTO()));
        when(ctx.getCategoryId()).thenReturn(1);
        dealAttrHelperMockedStatic.when(() -> DealAttrHelper.needReservation(any())).thenReturn(true);
        dealAttrHelperMockedStatic.when(() -> DealAttrHelper.isSupportHomeService(any())).thenReturn(false);
        dealAttrHelperMockedStatic.when(() -> DealAttrHelper.isSupportShopService(any())).thenReturn(false);
        dealCtxHelperMockedStatic.when(() -> DealCtxHelper.isPreOrderDeal(ctx)).thenReturn(true);
        reserveProductWrapperMockedStatic.when(() -> ReserveProductWrapper.reserveAfterPurchase(anyInt())).thenReturn(true);
        when(reserveProductWrapper.getFutureResult(ctx)).thenReturn(ReserveResponse.createSuccessResponse(true));

        List<String> result = remindBuilderService.getReservationInfo(ctx);

        assertEquals(1, result.size());
        assertEquals("在线预订", result.get(0));
    }

    @Test
    public void testGetReservationInfo_NeedReservation() {
        when(ctx.getAttrs()).thenReturn(Lists.newArrayList(new AttributeDTO()));
        when(ctx.getCategoryId()).thenReturn(1);
        dealAttrHelperMockedStatic.when(() -> DealAttrHelper.needReservation(any())).thenReturn(true);
        dealAttrHelperMockedStatic.when(() -> DealAttrHelper.isSupportHomeService(any())).thenReturn(false);
        dealAttrHelperMockedStatic.when(() -> DealAttrHelper.isSupportShopService(any())).thenReturn(false);
        dealCtxHelperMockedStatic.when(() -> DealCtxHelper.isPreOrderDeal(ctx)).thenReturn(false);
        reserveProductWrapperMockedStatic.when(() -> ReserveProductWrapper.reserveAfterPurchase(anyInt())).thenReturn(false);

        List<String> result = remindBuilderService.getReservationInfo(ctx);

        assertEquals(1, result.size());
        assertEquals("需预约", result.get(0));
    }

    @Test
    public void testGetReservationInfo_NeedReservation_Preorder() {
        when(ctx.getAttrs()).thenReturn(Lists.newArrayList(new AttributeDTO()));
        when(ctx.getCategoryId()).thenReturn(1);
        dealAttrHelperMockedStatic.when(() -> DealAttrHelper.needReservation(any())).thenReturn(true);
        dealAttrHelperMockedStatic.when(() -> DealAttrHelper.isSupportHomeService(any())).thenReturn(false);
        dealAttrHelperMockedStatic.when(() -> DealAttrHelper.isSupportShopService(any())).thenReturn(false);
        dealCtxHelperMockedStatic.when(() -> DealCtxHelper.isPreOrderDeal(ctx)).thenReturn(true);
        reserveProductWrapperMockedStatic.when(() -> ReserveProductWrapper.reserveAfterPurchase(anyInt())).thenReturn(false);

        List<String> result = remindBuilderService.getReservationInfo(ctx);

        assertEquals(1, result.size());
        assertEquals("需预订", result.get(0));
    }

}
