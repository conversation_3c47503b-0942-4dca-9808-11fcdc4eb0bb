package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BannerTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBanner;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PromoDetailModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.ProductDetailModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.action.extend.redirect.SuckBottomRedirectActionVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.banner.BottomBarTopBannerVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.backgroud.BottomBarBackgroundVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.rich.content.PicRichContentVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.rich.content.RichContentVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.rich.content.TextRichContentVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.rich.content.enums.RichContentTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.util.TimesDealUtil;
import com.google.common.collect.Lists;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericModuleResponse;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericProductDetailPageResponse;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ProductDetailBuilderServiceTest {

    @InjectMocks
    private ProductDetailBuilderService productDetailBuilderService;
    private GenericModuleResponse bottomBar;
    private JSONObject moduleVO;
    private JSONArray topBannerList;
    private JSONObject bannerJson;
    private JSONArray bannerDataArray;
    private JSONObject bannerDataJson1;
    private JSONObject bannerDataJson2;
    private TextRichContentVO textRichContentVO1;
    private TextRichContentVO textRichContentVO2;
    private BottomBarBackgroundVO background;
    private SuckBottomRedirectActionVO actionData;

    @Before
    public void setUp() {
        // 初始化测试数据
        bottomBar = new GenericModuleResponse();
        moduleVO = new JSONObject();
        topBannerList = new JSONArray();
        bannerJson = new JSONObject();
        bannerDataArray = new JSONArray();

        // 创建第一个文本内容
        bannerDataJson1 = new JSONObject();
        textRichContentVO1 = new TextRichContentVO();
        textRichContentVO1.setText("会员专享价");
        textRichContentVO1.setTextStyle("Default");
        bannerDataJson1.putAll(JSON.parseObject(JSON.toJSONString(textRichContentVO1)));

        // 创建第二个文本内容（加粗样式）
        bannerDataJson2 = new JSONObject();
        textRichContentVO2 = new TextRichContentVO();
        textRichContentVO2.setText("一单回本");
        textRichContentVO2.setTextStyle("Bold");
        bannerDataJson2.putAll(JSON.parseObject(JSON.toJSONString(textRichContentVO2)));

        // 添加文本内容到bannerData数组
        bannerDataArray.add(bannerDataJson1);
        bannerDataArray.add(bannerDataJson2);

        // 创建背景对象
        background = new BottomBarBackgroundVO();
        background.setColors(Lists.newArrayList("#FFF8E8"));

        // 创建点击动作对象
        actionData = new SuckBottomRedirectActionVO();
        actionData.setActionType(1);
        actionData.setUrl("https://example.com/redirect");
        actionData.setText("查看详情");
        actionData.setIcon("https://example.com/icon.png");

        // 组装banner对象
        bannerJson.put("bannerData", bannerDataArray);
        bannerJson.put("background", background);
        bannerJson.put("actionData", actionData);
        bannerJson.put("bannerType", 1);

        // 添加banner到topBannerList
        topBannerList.add(bannerJson);

        // 设置moduleVO
        moduleVO.put("topBannerList", topBannerList);

        // 设置bottomBar的moduleVO
        bottomBar.setModuleVO(moduleVO);
    }

    /**
     * Test successful build with atmosphere price bar
     */
    @Test
    public void testBuildWithAtmospherePriceBar() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        GenericProductDetailPageResponse response = mock(GenericProductDetailPageResponse.class);
        Map<String, GenericModuleResponse> moduleResponse = new HashMap<>();
        // Setup atmosphere price bar module
        GenericModuleResponse atmosphereModule = mock(GenericModuleResponse.class);
        JSONObject atmosphereVO = new JSONObject();
        atmosphereVO.put("atmosphere", new JSONObject());
        when(atmosphereModule.getModuleVO()).thenReturn(atmosphereVO);
        moduleResponse.put("module_detail_deal_atmosphere_price_sale_bar", atmosphereModule);
        when(ctx.getProductDetailTradeModuleResponse()).thenReturn(response);
        when(response.getModuleResponse()).thenReturn(moduleResponse);
        // act
        ProductDetailModule result = productDetailBuilderService.build(ctx);
        // assert
        assertNotNull(result);
        assertNotNull(result.getProductAtmosphere());
    }

    /**
     * Test build with price sale bar when atmosphere is null
     */
    @Test
    public void testBuildWithPriceSaleBarWhenAtmosphereIsNull() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        GenericProductDetailPageResponse response = mock(GenericProductDetailPageResponse.class);
        Map<String, GenericModuleResponse> moduleResponse = new HashMap<>();
        // Setup price sale bar module
        GenericModuleResponse priceModule = mock(GenericModuleResponse.class);
        JSONObject priceVO = new JSONObject();
        when(priceModule.getModuleVO()).thenReturn(priceVO);
        moduleResponse.put("module_detail_deal_price_sale_bar", priceModule);
        when(ctx.getProductDetailTradeModuleResponse()).thenReturn(response);
        when(response.getModuleResponse()).thenReturn(moduleResponse);
        // act
        ProductDetailModule result = productDetailBuilderService.build(ctx);
        // assert
        assertNotNull(result);
        assertNotNull(result.getProductPrice());
    }

    /**
     * Test build with promotion popup
     */
    @Test
    public void testBuildWithPromotionPopup() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        GenericProductDetailPageResponse response = mock(GenericProductDetailPageResponse.class);
        Map<String, GenericModuleResponse> moduleResponse = new HashMap<>();
        // Setup promotion popup module
        GenericModuleResponse promoModule = mock(GenericModuleResponse.class);
        JSONObject promoVO = new JSONObject();
        when(promoModule.getModuleVO()).thenReturn(promoVO);
        moduleResponse.put("module_price_discount_detail", promoModule);
        when(ctx.getProductDetailTradeModuleResponse()).thenReturn(response);
        when(response.getModuleResponse()).thenReturn(moduleResponse);
        // act
        ProductDetailModule result = productDetailBuilderService.build(ctx);
        // assert
        assertNotNull(result);
        assertNotNull(result.getPromoDetail());
    }

    /**
     * Test build with member exclusive banner
     */
    @Test
    public void testBuildWithMemberExclusiveBanner() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        GenericProductDetailPageResponse response = mock(GenericProductDetailPageResponse.class);
        Map<String, GenericModuleResponse> moduleResponse = new HashMap<>();
        // Setup bottom bar module
        GenericModuleResponse bottomBarModule = mock(GenericModuleResponse.class);
        JSONObject bottomBarVO = new JSONObject();
        when(bottomBarModule.getModuleVO()).thenReturn(bottomBarVO);
        moduleResponse.put("module_detail_deal_bottom_bar", bottomBarModule);
        when(ctx.getProductDetailTradeModuleResponse()).thenReturn(response);
        when(response.getModuleResponse()).thenReturn(moduleResponse);
        // Setup member exclusive banner
        DealGroupPBO resultPBO = mock(DealGroupPBO.class);
        DealBuyBar buyBar = mock(DealBuyBar.class);
        DealBuyBanner buyBanner = new DealBuyBanner();
        buyBanner.setBannerType(BannerTypeEnum.MemberExclusive.getType());
        when(ctx.getResult()).thenReturn(resultPBO);
        when(resultPBO.getBuyBar()).thenReturn(buyBar);
        when(buyBar.getBuyBanner()).thenReturn(buyBanner);
        // act
        ProductDetailModule result = productDetailBuilderService.build(ctx);
        // assert
        assertNotNull(result);
    }

    /**
     * Test build with null moduleVO
     */
    @Test
    public void testBuildWithNullModuleVO() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        GenericProductDetailPageResponse response = mock(GenericProductDetailPageResponse.class);
        Map<String, GenericModuleResponse> moduleResponse = new HashMap<>();
        // Setup module with null moduleVO
        GenericModuleResponse nullModule = mock(GenericModuleResponse.class);
        when(nullModule.getModuleVO()).thenReturn(null);
        moduleResponse.put("module_detail_deal_atmosphere_price_sale_bar", nullModule);
        when(ctx.getProductDetailTradeModuleResponse()).thenReturn(response);
        when(response.getModuleResponse()).thenReturn(moduleResponse);
        // act
        ProductDetailModule result = productDetailBuilderService.build(ctx);
        // assert
        assertNotNull(result);
        assertNull(result.getProductAtmosphere());
    }

    @Test
    public void testProcessBottomBarModule_Success() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        // 设置bottomBar的moduleVO
        bottomBar.setModuleVO(moduleVO);
        Map<String, GenericModuleResponse> moduleResponse = new HashMap<>();
        moduleResponse.put("module_detail_deal_bottom_bar", bottomBar);
        GenericProductDetailPageResponse response = new GenericProductDetailPageResponse();
        response.setModuleResponse(moduleResponse);
        ctx.setProductDetailTradeModuleResponse(response);
        // 调用测试方法
        ProductDetailModule result = productDetailBuilderService.buildTradeModule(ctx);
        assertNotNull(result);
    }


    /**
     * 正常场景：返回有效的 DealBuyBtn 列表
     */
    @Test
    public void testProcessMonthlySubscriptionBottomBarModule_Normal() throws Throwable {
        // arrange
        String jsonStr = "{\"code\":200,\"moduleKey\":\"module_detail_deal_bottom_bar\",\"moduleVO\":{\"bottomBar\":{\"bottomBarStyle\":1,\"rightBottomBar\":{\"blockType\":1,\"buttonList\":[{\"componentType\":1,\"subTitle\":[{\"strikeThrough\":false,\"textSize\":12,\"underline\":false,\"text\":\"在线付首月 次月自动扣款\",\"textStyle\":\"Default\",\"type\":1,\"textColor\":\"#FFFFFF\"}],\"disable\":false,\"mainTitle\":[{\"strikeThrough\":false,\"textSize\":15,\"underline\":false,\"text\":\"立即抢购\",\"textStyle\":\"Bold\",\"type\":1,\"textColor\":\"#FFFFFF\"}],\"background\":{\"borderWidth\":0,\"type\":3,\"colors\":[\"#FF8225\",\"#FF4B10\"]},\"actionData\":{\"actionType\":2,\"url\":\"dianping://mrn?mrn_biz=gc&mrn_entry=group-order-submit&mrn_component=GroupOrderSubmit&dealid=**********&shopid=**********&skuid=464784185&isTransparent=true&mrn_transparent=ture&hideLoading=true&mrn_hideloading=true&mrn_hideNextNavBar=true&shopidEncrypt=h1yj4b77462b78705a1d3e83223ab858cb02fd88a81cee8819eb4e2c11de96dd92c8f6473219d02310d14d0768469e49fbh7fh&pagesource=dealGroupDetail&mmcinflate=0&mmcfree=0&isMagicalPrice=false&mmcuse=0&mmcbuy=0&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoWjd012eO6M7VPzoXLAX43sjAbH1hZ0Tof1py-NYu7h02LNlJ47m8l_eZUOj5qhU_y1ggIWw3DCdqBjv_mkv2nZGBK5kRcd76WOdj2ft2EhEuHa7tptSLebjbqaEoBCADh-Z8RQMFSEt19XO0QQZjtT4dpwuyEQdktoXQiY9UOOT2QgLBKH8fpuY8MXEBX6wURAuy5GzBlSclwfeXJoS-DnjQ8Mx0zRgid9oAZ5V_HQQCihvszqUCUljVSHRRY6n35ijyj_c6axcwNyrqceyW9utW4AgyXjerrpqjAeVStyCJXheDbzvwNr18mPBT5nSR2YPNOM2KvnXHZPX8TKeMHL\",\"openType\":\"redirect\"}}]}},\"moduleKey\":\"module_detail_deal_bottom_bar\"}}";
        GenericModuleResponse mockResponse = JSONObject.parseObject(jsonStr, GenericModuleResponse.class);
        // act
        List<DealBuyBtn> result = productDetailBuilderService.processMonthlySubscriptionBottomBarModule(mockResponse);

        // assert
        assertNotNull(result);
    }

    @Test
    public void testMonthlySubscriptionPromoDetailModule() {
        // 创建环境上下文
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);

        // 创建结果对象和优惠详情模块
        DealGroupPBO resultPBO = new DealGroupPBO();
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        // 设置初始值，以便测试清空操作
        promoDetailModule.setSinglePrice("99.17");
        promoDetailModule.setCopies("¥ 99.17");
        promoDetailModule.setPricePerUnit("99.17");
        promoDetailModule.setTimesUnit("¥");
        resultPBO.setPromoDetailModule(promoDetailModule);
        ctx.setResult(resultPBO);

        // 创建模拟响应对象
        GenericProductDetailPageResponse response = mock(GenericProductDetailPageResponse.class);
        Map<String, GenericModuleResponse> moduleResponse = new HashMap<>();

        // 创建价格模块
        GenericModuleResponse priceModule = mock(GenericModuleResponse.class);
        JSONObject priceVO = new JSONObject();
        JSONObject price = new JSONObject();
        price.put("finalPrice", "1190");
        price.put("priceSymbol", "¥");
        price.put("discountTag", "99.17/月");
        priceVO.put("price", price);
        JSONObject sale = new JSONObject();
        sale.put("saleTag", "年售3");
        priceVO.put("sale", sale);
        when(priceModule.getModuleVO()).thenReturn(priceVO);
        moduleResponse.put("module_detail_deal_price_sale_bar", priceModule);

        // 设置响应
        when(response.getModuleResponse()).thenReturn(moduleResponse);
        ctx.setProductDetailTradeModuleResponse(response);

        // 模拟连续包月场景
        try (MockedStatic<TimesDealUtil> mockedTimesDealUtil = mockStatic(TimesDealUtil.class)) {
            mockedTimesDealUtil.when(() -> TimesDealUtil.isMonthlySubscription(any(DealCtx.class))).thenReturn(true);

            // 执行测试方法
            ProductDetailModule result = productDetailBuilderService.buildTradeModule(ctx);

            // 验证结果
            assertNotNull(result);
        }
    }

    /**
     * 异常场景：JSON 解析异常
     */
    @Test
    public void testProcessMonthlySubscriptionBottomBarModule_JSONParseException() throws Throwable {
        // arrange
        GenericModuleResponse mockResponse = mock(GenericModuleResponse.class);
        when(mockResponse.getModuleVO()).thenThrow(new RuntimeException("JSON parse error"));

        // act
        List<DealBuyBtn> result = productDetailBuilderService.processMonthlySubscriptionBottomBarModule(mockResponse);

        // assert
        assertNull(result);
    }

    /**
     * 测试 barTopBannerVO 为 null 的场景
     */
    @Test
    public void testProcessCountrySubsidiesBottomBanner_BarTopBannerVOIsNull() {
        // arrange
        DealGroupPBO result = mock(DealGroupPBO.class);

        // act
        productDetailBuilderService.processCountrySubsidiesBottomBanner(null, result);

        // assert
        verify(result, never()).getBuyBar();
    }

    /**
     * 测试 result 为 null 的场景
     */
    @Test
    public void testProcessCountrySubsidiesBottomBanner_ResultIsNull() {
        // arrange
        BottomBarTopBannerVO barTopBannerVO = mock(BottomBarTopBannerVO.class);

        // act
        productDetailBuilderService.processCountrySubsidiesBottomBanner(barTopBannerVO, null);

        // assert
        // 无需断言，方法直接返回
    }

    /**
     * 测试正常场景，bannerType 为 1
     */
    @Test
    public void testProcessCountrySubsidiesBottomBanner_BannerTypeIsOne() {
        // arrange
        BottomBarTopBannerVO barTopBannerVO = mock(BottomBarTopBannerVO.class);
        when(barTopBannerVO.getBannerType()).thenReturn(1);

        BottomBarBackgroundVO background = mock(BottomBarBackgroundVO.class);
        when(barTopBannerVO.getBackground()).thenReturn(background);
        when(background.getColors()).thenReturn(Lists.newArrayList("#FFFFFF"));

        DealGroupPBO result = mock(DealGroupPBO.class);
        DealBuyBar buyBar = mock(DealBuyBar.class);
        when(result.getBuyBar()).thenReturn(buyBar);

        // act
        productDetailBuilderService.processCountrySubsidiesBottomBanner(barTopBannerVO, result);

        // assert
        verify(buyBar).setBuyBanner(any(DealBuyBanner.class));
    }

    /**
     * 测试正常场景，bannerType 不为 1
     */
    @Test
    public void testProcessCountrySubsidiesBottomBanner_BannerTypeIsNotOne() {
        // arrange
        BottomBarTopBannerVO barTopBannerVO = mock(BottomBarTopBannerVO.class);
        when(barTopBannerVO.getBannerType()).thenReturn(2);

        BottomBarBackgroundVO background = mock(BottomBarBackgroundVO.class);
        when(barTopBannerVO.getBackground()).thenReturn(background);
        when(background.getColors()).thenReturn(Lists.newArrayList("#FFFFFF"));

        DealGroupPBO result = mock(DealGroupPBO.class);
        DealBuyBar buyBar = mock(DealBuyBar.class);
        when(result.getBuyBar()).thenReturn(buyBar);

        // act
        productDetailBuilderService.processCountrySubsidiesBottomBanner(barTopBannerVO, result);

        // assert
        verify(buyBar).setBuyBanner(any(DealBuyBanner.class));
    }

    /**
     * 测试 bannerData 为 null 的场景
     */
    @Test
    public void testProcessCountrySubsidiesBottomBanner_BannerDataIsNull() {
        // arrange
        BottomBarTopBannerVO barTopBannerVO = mock(BottomBarTopBannerVO.class);
        when(barTopBannerVO.getBannerData()).thenReturn(null);

        DealGroupPBO result = mock(DealGroupPBO.class);
        DealBuyBar buyBar = mock(DealBuyBar.class);
        when(result.getBuyBar()).thenReturn(buyBar);

        // act
        productDetailBuilderService.processCountrySubsidiesBottomBanner(barTopBannerVO, result);

        // assert
        verify(buyBar).setBuyBanner(any(DealBuyBanner.class));
    }

    /**
     * 测试 bannerData 包含图片类型的场景
     */
    @Test
    public void testProcessCountrySubsidiesBottomBanner_BannerDataContainsPic() {
        // arrange
        BottomBarTopBannerVO barTopBannerVO = mock(BottomBarTopBannerVO.class);
        List<RichContentVO> bannerData = new ArrayList<>();
        PicRichContentVO picRichContentVO = mock(PicRichContentVO.class);
        when(picRichContentVO.getType()).thenReturn(RichContentTypeEnum.PIC.getCode());
        when(picRichContentVO.getIconUrl()).thenReturn("http://example.com/icon.png");
        bannerData.add(picRichContentVO);
        when(barTopBannerVO.getBannerData()).thenReturn(bannerData);


        DealGroupPBO result = mock(DealGroupPBO.class);
        DealBuyBar buyBar = mock(DealBuyBar.class);
        when(result.getBuyBar()).thenReturn(buyBar);

        // act
        productDetailBuilderService.processCountrySubsidiesBottomBanner(barTopBannerVO, result);

        // assert
        verify(buyBar).setBuyBanner(any(DealBuyBanner.class));
    }

}
