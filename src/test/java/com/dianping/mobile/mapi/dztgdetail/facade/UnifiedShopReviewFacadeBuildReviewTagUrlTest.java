package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.ShopReviewCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.ReviewTagDO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.ReviewUserModel;
import com.dianping.mobile.mapi.dztgdetail.util.NetUtils;
import com.dianping.ugc.proxyService.remote.dto.AnonymousUserInfo;
import com.dianping.ugc.proxyService.remote.dto.UserManaInfo;
import com.dianping.ugc.proxyService.remote.dto.UserVipIconInfo;
import com.dianping.ugc.proxyService.remote.dto.UserVipInfoForApp;
import com.dianping.ugc.proxyService.remote.dto.feed.Pendant;
import com.dianping.ugc.proxyService.remote.enums.feed.PendantTypeEnum;
import com.dianping.ugc.review.remote.dto.Expense;
import com.dp.arts.client.response.Record;
import com.dp.arts.client.response.Response;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedShopReviewFacadeBuildReviewTagUrlTest {

    @Mock
    private ShopReviewCtx shopReviewCtx;

    @Mock
    private EnvCtx envCtx;

    @Mock
    private ReviewTagDO reviewTagDO;

    private UnifiedShopReviewFacade facade = new UnifiedShopReviewFacade();

    private UnifiedShopReviewFacade unifiedShopReviewFacade = new UnifiedShopReviewFacade();

    private String invokePrivateBuildReviewTagUrl(ShopReviewCtx shopReviewCtx, ReviewTagDO reviewTagDO) throws Throwable {
        try {
            Method method = UnifiedShopReviewFacade.class.getDeclaredMethod("buildReviewTagUrl", ShopReviewCtx.class, ReviewTagDO.class);
            method.setAccessible(true);
            return (String) method.invoke(facade, shopReviewCtx, reviewTagDO);
        } catch (Exception e) {
            throw e.getCause();
        }
    }

    private void invokeTransferAnonymousUserInfo(Map<Long, ReviewUserModel> reviewUserModelMap, Map<Long, Long> userIdToReviewId, Map<Long, AnonymousUserInfo> anonymousUserMap) throws Exception {
        Method method = UnifiedShopReviewFacade.class.getDeclaredMethod("transferAnonymousUserInfo", Map.class, Map.class, Map.class);
        method.setAccessible(true);
        method.invoke(unifiedShopReviewFacade, reviewUserModelMap, userIdToReviewId, anonymousUserMap);
    }

    private String invokeBuildPrice(UnifiedShopReviewFacade facade, Object... args) throws Exception {
        Method method = UnifiedShopReviewFacade.class.getDeclaredMethod("buildPrice", java.util.List.class);
        method.setAccessible(true);
        return (String) method.invoke(facade, args);
    }

    private void invokePrivateMethod(UnifiedShopReviewFacade facade, String methodName, Object... args) throws Exception {
        Method method = UnifiedShopReviewFacade.class.getDeclaredMethod(methodName, Response.class, List.class, ShopReviewCtx.class);
        method.setAccessible(true);
        method.invoke(facade, args);
    }

    /**
     * Helper method to invoke the private getShopUuid method using reflection.
     *
     * @param shopReviewCtx the ShopReviewCtx object to pass to the method
     * @return the result of the method invocation
     * @throws Exception if an error occurs during reflection
     */
    private String invokePrivateGetShopUuidMethod(ShopReviewCtx shopReviewCtx) throws Exception {
        Method method = UnifiedShopReviewFacade.class.getDeclaredMethod("getShopUuid", ShopReviewCtx.class);
        method.setAccessible(true);
        try {
            return (String) method.invoke(unifiedShopReviewFacade, shopReviewCtx);
        } catch (InvocationTargetException e) {
            Throwable cause = e.getCause();
            if (cause instanceof NullPointerException) {
                throw (NullPointerException) cause;
            }
            throw e;
        }
    }

    /**
     * 测试场景：shopReviewCtx.isMt() 返回 true，使用 MT_REVIEW_TAG_URL 模板生成 URL。
     */
    @Test
    public void testBuildReviewTagUrl_WhenIsMt_ReturnsMtReviewTagUrl() throws Throwable {
        // arrange
        when(shopReviewCtx.isMt()).thenReturn(true);
        // Changed from 123L to 123
        when(shopReviewCtx.getMtId()).thenReturn(123);
        when(reviewTagDO.getName()).thenReturn("testTag");
        when(reviewTagDO.getRankType()).thenReturn(1);
        // act
        String result = invokePrivateBuildReviewTagUrl(shopReviewCtx, reviewTagDO);
        // assert
        assertEquals("imeituan://www.meituan.com/reviewlist?refertype=1&referid=123&selecttagname=testTag&tagtype=1", result);
    }

    /**
     * 测试场景：shopReviewCtx.isMt() 返回 false，且 DztgClientTypeEnum 是 DIANPING_BAIDUMAP_MINIAPP，返回空字符串。
     */
    @Test
    public void testBuildReviewTagUrl_WhenIsDianpingBaiduMapMiniApp_ReturnsEmptyString() throws Throwable {
        // arrange
        when(shopReviewCtx.isMt()).thenReturn(false);
        when(shopReviewCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.DIANPING_BAIDUMAP_MINIAPP);
        // act
        String result = invokePrivateBuildReviewTagUrl(shopReviewCtx, reviewTagDO);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试场景：shopReviewCtx.isMt() 返回 false，且 DztgClientTypeEnum 不是 DIANPING_BAIDUMAP_MINIAPP，使用 DP_REVIEW_TAG_URL 模板生成 URL。
     */
    @Test
    public void testBuildReviewTagUrl_WhenIsNotMtAndNotDianpingBaiduMapMiniApp_ReturnsDpReviewTagUrl() throws Throwable {
        // arrange
        when(shopReviewCtx.isMt()).thenReturn(false);
        when(shopReviewCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.DIANPING_APP);
        when(shopReviewCtx.getDpShopUuid()).thenReturn("shop123");
        when(reviewTagDO.getRankType()).thenReturn(2);
        when(reviewTagDO.getName()).thenReturn("testTag");
        when(reviewTagDO.getAffection()).thenReturn(3);
        // act
        String result = invokePrivateBuildReviewTagUrl(shopReviewCtx, reviewTagDO);
        // assert
        assertEquals("dianping://review?referid=shop123&refertype=0&tagtype=2&selecttagname=testTag_3", result);
    }

    @Test
    public void testTransferAnonymousUserInfoAllEmpty() throws Throwable {
        Map<Long, ReviewUserModel> reviewUserModelMap = new HashMap<>();
        Map<Long, Long> userIdToReviewId = new HashMap<>();
        Map<Long, AnonymousUserInfo> anonymousUserMap = new HashMap<>();
        invokeTransferAnonymousUserInfo(reviewUserModelMap, userIdToReviewId, anonymousUserMap);
        assertTrue(reviewUserModelMap.isEmpty());
    }

    @Test
    public void testTransferAnonymousUserInfoReviewUserModelMapEmpty() throws Throwable {
        Map<Long, ReviewUserModel> reviewUserModelMap = new HashMap<>();
        Map<Long, Long> userIdToReviewId = new HashMap<>();
        Map<Long, AnonymousUserInfo> anonymousUserMap = new HashMap<>();
        userIdToReviewId.put(1L, 1L);
        anonymousUserMap.put(1L, new AnonymousUserInfo());
        invokeTransferAnonymousUserInfo(reviewUserModelMap, userIdToReviewId, anonymousUserMap);
        // Corrected assertion to reflect the expected behavior
        assertTrue(reviewUserModelMap.isEmpty());
    }

    @Test
    public void testTransferAnonymousUserInfoUserIdToReviewIdEmpty() throws Throwable {
        Map<Long, ReviewUserModel> reviewUserModelMap = new HashMap<>();
        Map<Long, Long> userIdToReviewId = new HashMap<>();
        Map<Long, AnonymousUserInfo> anonymousUserMap = new HashMap<>();
        reviewUserModelMap.put(1L, new ReviewUserModel());
        anonymousUserMap.put(1L, new AnonymousUserInfo());
        invokeTransferAnonymousUserInfo(reviewUserModelMap, userIdToReviewId, anonymousUserMap);
        assertFalse(reviewUserModelMap.isEmpty());
    }

    @Test
    public void testTransferAnonymousUserInfoAnonymousUserMapEmpty() throws Throwable {
        Map<Long, ReviewUserModel> reviewUserModelMap = new HashMap<>();
        Map<Long, Long> userIdToReviewId = new HashMap<>();
        Map<Long, AnonymousUserInfo> anonymousUserMap = new HashMap<>();
        reviewUserModelMap.put(1L, new ReviewUserModel());
        userIdToReviewId.put(1L, 1L);
        invokeTransferAnonymousUserInfo(reviewUserModelMap, userIdToReviewId, anonymousUserMap);
        assertFalse(reviewUserModelMap.isEmpty());
    }

    @Test
    public void testTransferAnonymousUserInfoNotContainsKey() throws Throwable {
        Map<Long, ReviewUserModel> reviewUserModelMap = new HashMap<>();
        Map<Long, Long> userIdToReviewId = new HashMap<>();
        Map<Long, AnonymousUserInfo> anonymousUserMap = new HashMap<>();
        reviewUserModelMap.put(1L, new ReviewUserModel());
        userIdToReviewId.put(2L, 2L);
        anonymousUserMap.put(2L, new AnonymousUserInfo());
        invokeTransferAnonymousUserInfo(reviewUserModelMap, userIdToReviewId, anonymousUserMap);
        assertFalse(reviewUserModelMap.isEmpty());
    }

    @Test
    public void testTransferAnonymousUserInfoNotContainsReviewId() throws Throwable {
        Map<Long, ReviewUserModel> reviewUserModelMap = new HashMap<>();
        Map<Long, Long> userIdToReviewId = new HashMap<>();
        Map<Long, AnonymousUserInfo> anonymousUserMap = new HashMap<>();
        reviewUserModelMap.put(1L, new ReviewUserModel());
        userIdToReviewId.put(1L, 2L);
        anonymousUserMap.put(2L, new AnonymousUserInfo());
        invokeTransferAnonymousUserInfo(reviewUserModelMap, userIdToReviewId, anonymousUserMap);
        assertFalse(reviewUserModelMap.isEmpty());
    }

    @Test
    public void testTransferAnonymousUserInfoPendantListEmpty() throws Throwable {
        Map<Long, ReviewUserModel> reviewUserModelMap = new HashMap<>();
        Map<Long, Long> userIdToReviewId = new HashMap<>();
        Map<Long, AnonymousUserInfo> anonymousUserMap = new HashMap<>();
        ReviewUserModel reviewUserModel = new ReviewUserModel();
        reviewUserModelMap.put(1L, reviewUserModel);
        userIdToReviewId.put(1L, 1L);
        AnonymousUserInfo anonymousUserInfo = new AnonymousUserInfo();
        anonymousUserMap.put(1L, anonymousUserInfo);
        invokeTransferAnonymousUserInfo(reviewUserModelMap, userIdToReviewId, anonymousUserMap);
        assertNull(reviewUserModel.getDetailUrl());
    }

    @Test
    public void testTransferAnonymousUserInfoPendantTypeNotLevel() throws Throwable {
        Map<Long, ReviewUserModel> reviewUserModelMap = new HashMap<>();
        Map<Long, Long> userIdToReviewId = new HashMap<>();
        Map<Long, AnonymousUserInfo> anonymousUserMap = new HashMap<>();
        ReviewUserModel reviewUserModel = new ReviewUserModel();
        reviewUserModelMap.put(1L, reviewUserModel);
        userIdToReviewId.put(1L, 1L);
        AnonymousUserInfo anonymousUserInfo = new AnonymousUserInfo();
        anonymousUserMap.put(1L, anonymousUserInfo);
        Pendant pendant = new Pendant();
        pendant.setType(PendantTypeEnum.VIP.getPendantType());
        anonymousUserInfo.setPendantList(Collections.singletonList(pendant));
        invokeTransferAnonymousUserInfo(reviewUserModelMap, userIdToReviewId, anonymousUserMap);
        assertNull(reviewUserModel.getUserLevel());
    }

    @Test
    public void testTransferAnonymousUserInfoPendantTypeLevel() throws Throwable {
        Map<Long, ReviewUserModel> reviewUserModelMap = new HashMap<>();
        Map<Long, Long> userIdToReviewId = new HashMap<>();
        Map<Long, AnonymousUserInfo> anonymousUserMap = new HashMap<>();
        ReviewUserModel reviewUserModel = new ReviewUserModel();
        reviewUserModelMap.put(1L, reviewUserModel);
        userIdToReviewId.put(1L, 1L);
        AnonymousUserInfo anonymousUserInfo = new AnonymousUserInfo();
        anonymousUserMap.put(1L, anonymousUserInfo);
        Pendant pendant = new Pendant();
        pendant.setType(PendantTypeEnum.LEVEL.getPendantType());
        pendant.setImgUrl("level");
        anonymousUserInfo.setPendantList(Collections.singletonList(pendant));
        invokeTransferAnonymousUserInfo(reviewUserModelMap, userIdToReviewId, anonymousUserMap);
        assertEquals("level", reviewUserModel.getUserLevel());
    }

    @Test
    public void testBuildPriceWhenExpenseInfoListIsEmpty() throws Throwable {
        UnifiedShopReviewFacade facade = new UnifiedShopReviewFacade();
        String result = invokeBuildPrice(facade, Collections.emptyList());
        assertEquals("", result);
    }

    @Test
    public void testBuildPriceWhenTitleIsNotPerPerson() throws Throwable {
        UnifiedShopReviewFacade facade = new UnifiedShopReviewFacade();
        Expense expense = new Expense();
        expense.setTitle("价格");
        expense.setExpense(100);
        String result = invokeBuildPrice(facade, Arrays.asList(expense));
        assertEquals("￥100", result);
    }

    @Test
    public void testBuildPriceWhenExpenseIsZero() throws Throwable {
        UnifiedShopReviewFacade facade = new UnifiedShopReviewFacade();
        Expense expense = new Expense();
        expense.setTitle("人均");
        expense.setExpense(0);
        String result = invokeBuildPrice(facade, Arrays.asList(expense));
        assertEquals("", result);
    }

    @Test
    public void testBuildPriceWhenExpenseIsGreaterThanZero() throws Throwable {
        UnifiedShopReviewFacade facade = new UnifiedShopReviewFacade();
        Expense expense = new Expense();
        expense.setTitle("人均");
        expense.setExpense(100);
        String result = invokeBuildPrice(facade, Arrays.asList(expense));
        assertEquals("￥100/人", result);
    }

    /**
     * Test case for when the response status is not "OK".
     */
    @Test
    public void testCreateDynamicAbstractResponseStatusNotOK() throws Throwable {
        // arrange
        Response response = mock(Response.class);
        when(response.getStatus()).thenReturn("ERROR");
        List<ReviewTagDO> reviewAbstracts = new ArrayList<>();
        EnvCtx envCtx = new EnvCtx();
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx(envCtx);
        UnifiedShopReviewFacade facade = new UnifiedShopReviewFacade();
        // act
        invokePrivateMethod(facade, "createDynamicAbstract", response, reviewAbstracts, shopReviewCtx);
        // assert
        assertTrue(reviewAbstracts.isEmpty());
    }

    /**
     * Test case for when the response record list is empty.
     */
    @Test
    public void testCreateDynamicAbstractRecordListEmpty() throws Throwable {
        // arrange
        Response response = mock(Response.class);
        when(response.getStatus()).thenReturn("OK");
        when(response.getRecordList()).thenReturn(new ArrayList<>());
        List<ReviewTagDO> reviewAbstracts = new ArrayList<>();
        EnvCtx envCtx = new EnvCtx();
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx(envCtx);
        UnifiedShopReviewFacade facade = new UnifiedShopReviewFacade();
        // act
        invokePrivateMethod(facade, "createDynamicAbstract", response, reviewAbstracts, shopReviewCtx);
        // assert
        assertTrue(reviewAbstracts.isEmpty());
    }

    /**
     * Test case for when a record tag cannot be split into two parts.
     */
    @Test
    public void testCreateDynamicAbstractTagCannotBeSplit() throws Throwable {
        // arrange
        Response response = mock(Response.class);
        when(response.getStatus()).thenReturn("OK");
        Record record = mock(Record.class);
        when(record.get("hit")).thenReturn("10");
        when(record.get("tag")).thenReturn("invalidTag");
        List<Record> recordList = new ArrayList<>();
        recordList.add(record);
        when(response.getRecordList()).thenReturn(recordList);
        List<ReviewTagDO> reviewAbstracts = new ArrayList<>();
        EnvCtx envCtx = new EnvCtx();
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx(envCtx);
        UnifiedShopReviewFacade facade = new UnifiedShopReviewFacade();
        // act
        invokePrivateMethod(facade, "createDynamicAbstract", response, reviewAbstracts, shopReviewCtx);
        // assert
        assertTrue(reviewAbstracts.isEmpty());
    }

    /**
     * Test case for when a record tag can be split into two parts.
     */
    @Test
    public void testCreateDynamicAbstractTagCanBeSplit() throws Throwable {
        // arrange
        Response response = mock(Response.class);
        when(response.getStatus()).thenReturn("OK");
        Record record = mock(Record.class);
        when(record.get("hit")).thenReturn("10");
        when(record.get("tag")).thenReturn("tagName_1");
        List<Record> recordList = new ArrayList<>();
        recordList.add(record);
        when(response.getRecordList()).thenReturn(recordList);
        List<ReviewTagDO> reviewAbstracts = new ArrayList<>();
        EnvCtx envCtx = new EnvCtx();
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx(envCtx);
        // Initialize DztgClientTypeEnum
        shopReviewCtx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        UnifiedShopReviewFacade facade = new UnifiedShopReviewFacade();
        // act
        invokePrivateMethod(facade, "createDynamicAbstract", response, reviewAbstracts, shopReviewCtx);
        // assert
        assertEquals(1, reviewAbstracts.size());
        ReviewTagDO reviewTagDO = reviewAbstracts.get(0);
        assertEquals("tagName", reviewTagDO.getName());
        assertEquals(1, reviewTagDO.getAffection());
        assertEquals(10, reviewTagDO.getCount());
    }

    /**
     * Test case for when multiple records with valid tags are processed.
     */
    @Test
    public void testCreateDynamicAbstractMultipleRecords() throws Throwable {
        // arrange
        Response response = mock(Response.class);
        when(response.getStatus()).thenReturn("OK");
        Record record1 = mock(Record.class);
        when(record1.get("hit")).thenReturn("10");
        when(record1.get("tag")).thenReturn("tagName_1");
        Record record2 = mock(Record.class);
        when(record2.get("hit")).thenReturn("20");
        when(record2.get("tag")).thenReturn("tagName_-1");
        List<Record> recordList = new ArrayList<>();
        recordList.add(record1);
        recordList.add(record2);
        when(response.getRecordList()).thenReturn(recordList);
        List<ReviewTagDO> reviewAbstracts = new ArrayList<>();
        EnvCtx envCtx = new EnvCtx();
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx(envCtx);
        // Initialize DztgClientTypeEnum
        shopReviewCtx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        UnifiedShopReviewFacade facade = new UnifiedShopReviewFacade();
        // act
        invokePrivateMethod(facade, "createDynamicAbstract", response, reviewAbstracts, shopReviewCtx);
        // assert
        assertEquals(2, reviewAbstracts.size());
        ReviewTagDO reviewTagDO1 = reviewAbstracts.get(0);
        assertEquals("tagName", reviewTagDO1.getName());
        assertEquals(1, reviewTagDO1.getAffection());
        assertEquals(10, reviewTagDO1.getCount());
        ReviewTagDO reviewTagDO2 = reviewAbstracts.get(1);
        assertEquals("tagName", reviewTagDO2.getName());
        assertEquals(-1, reviewTagDO2.getAffection());
        assertEquals(20, reviewTagDO2.getCount());
    }

    /**
     * Test getShopUuid method when shopReviewCtx is null, expecting a NullPointerException.
     */
    @Test(expected = NullPointerException.class)
    public void testGetShopUuidWhenShopReviewCtxIsNull() throws Throwable {
        // arrange
        ShopReviewCtx shopReviewCtx = null;
        // act
        invokePrivateGetShopUuidMethod(shopReviewCtx);
        // assert is handled by the expected exception
    }

    /**
     * Test getShopUuid method when dpShopUuid is null, expecting the dpLongShopId string form.
     */
    @Test
    public void testGetShopUuidWhenDpShopUuidIsNull() throws Throwable {
        // arrange
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx(new EnvCtx());
        shopReviewCtx.setDpLongShopId(123456L);
        // act
        String result = invokePrivateGetShopUuidMethod(shopReviewCtx);
        // assert
        assertEquals("123456", result);
    }

    /**
     * Test getShopUuid method when dpShopUuid is not null, expecting the dpShopUuid.
     */
    @Test
    public void testGetShopUuidWhenDpShopUuidIsNotNull() throws Throwable {
        // arrange
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx(new EnvCtx());
        shopReviewCtx.setDpShopUuid("testUuid");
        // act
        String result = invokePrivateGetShopUuidMethod(shopReviewCtx);
        // assert
        assertEquals("testUuid", result);
    }
}
