package com.dianping.mobile.mapi.dztgdetail.immersiveimage;

import com.dianping.mobile.mapi.dztgdetail.biz.immersiveimage.ImmersiveImageService;
import com.dianping.mobile.mapi.dztgdetail.biz.immersiveimage.impl.PhotoImmersiveImageServiceImpl;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ImmersiveImageWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ContentType;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImageSize;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImageUrlVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.SpritePicVO;
import com.dianping.mobile.mapi.dztgdetail.facade.ImmersiveImageFacade;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.google.common.collect.Lists;
import com.sankuai.mpmctcontent.application.thrift.dto.content.DisplayItem;
import com.sankuai.mpmctcontent.application.thrift.dto.content.RatioInfoDTO;
import com.sankuai.mpmctcontent.application.thrift.dto.content.VideoSpriteDTO;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2023-09-18
 * @desc ImmersiveImageFacade单元测试
 */
@RunWith(MockitoJUnitRunner.class)
public class ImmersiveImageFacadeTest {
    @InjectMocks
    private ImmersiveImageFacade immersiveImageFacade;

    @InjectMocks
    private ImmersiveImageWrapper immersiveImageWrapper;

    @Spy
    private List<ImmersiveImageService> immersiveImageServices = new ArrayList<>();

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    private MockedStatic<LionConfigUtils> lionConfigUtilsMocked;

    @Mock
    private PhotoImmersiveImageServiceImpl photoImmersiveImageService;

    private GetImmersiveImageRequest request;
    private EnvCtx envCtx;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        request = new GetImmersiveImageRequest();
        request.setDealGroupId(1);
        request.setLimit(10);
        request.setStart(0);
        request.setShopId(1L);

        envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);

        immersiveImageServices.add(photoImmersiveImageService);
        lionConfigUtilsMocked = mockStatic(LionConfigUtils.class);
    }

    @After
    public void teardown() {
        lionConfigUtilsMocked.close();
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testGetImmersiveImageNormal() {
        ImmersiveImageVO expected = new ImmersiveImageVO();
        expected.setRecordCount(20);
        lionConfigUtilsMocked.when(() -> LionConfigUtils.getImmersiveImageSceneSwitch(any())).thenReturn(true);
        when(dealGroupWrapper.getDpDealGroupId(request.getDealGroupId())).thenReturn(1);
        when(dealGroupWrapper.getCategoryId(request.getDealGroupId())).thenReturn(504);
        when(photoImmersiveImageService.getCategoryIds()).thenReturn(Lists.newArrayList(504));
        when(photoImmersiveImageService.getImmersiveImage(request, envCtx)).thenReturn(expected);

        ImmersiveImageVO result = immersiveImageFacade.getImmersiveImage(request, envCtx);
        assertEquals(expected, result);
    }

    /**
     * 测试场景开关关闭的情况
     */
    @Test
    public void testGetImmersiveImageSceneSwitchOff() {
        lionConfigUtilsMocked.when(() -> LionConfigUtils.getImmersiveImageSceneSwitch(any())).thenReturn(false);

        ImmersiveImageVO result = immersiveImageFacade.getImmersiveImage(request, envCtx);
        assertNull(result);
    }

    @Test
    public void testGetImmersiveImage() {
        SpritePicVO.SpritePicVOBuilder spritePicVOBuilder= SpritePicVO.builder().spritePicUrl("");
        ImageUrlVO.ImageUrlVOBuilder builder = ImageUrlVO.builder()
                .type(ContentType.PIC.getType())
                .url("fasdf")
                .spritePic(spritePicVOBuilder.build())
                .thumbPic("contentFusion");
        ImageUrlVO imageUrlVO =  builder.build();
        imageUrlVO.getSpritePic();


        ContentPBO contentPBO = new ContentPBO(1,"");
        contentPBO.setSpritePic(spritePicVOBuilder.build());
        contentPBO.getSpritePic();
        assertNotNull(contentPBO);

    }

    /**
     * 测试未找到支持的服务的情况
     */
    @Test
    public void testGetImmersiveImageNoSupportedService() {
       lionConfigUtilsMocked. when(() -> LionConfigUtils.getImmersiveImageSceneSwitch(any())).thenReturn(true);
        when(dealGroupWrapper.getDpDealGroupId(request.getDealGroupId())).thenReturn(1);
        when(dealGroupWrapper.getCategoryId(request.getDealGroupId())).thenReturn(504);
        when(photoImmersiveImageService.getCategoryIds()).thenReturn(Collections.singletonList(502));

        ImmersiveImageVO result = immersiveImageFacade.getImmersiveImage(request, envCtx);
        assertNull(result);
    }


    /**
     * 测试沉浸页雪碧图
     */
    @Test
    public void testImmersiveSpriteImage(){
        DisplayItem item = new DisplayItem();
        item.setType(1);
        item.setPicUrl("url");
        item.setBigPicUrl("bigurl");
        item.setVideoUrl("videourl");
        item.setVideoDuration("100");
        RatioInfoDTO ratioInfo = new RatioInfoDTO();
        ratioInfo.setHeight(10);
        ratioInfo.setWidth(10);
        item.setRatioInfo(ratioInfo);
        item.setSubPicUrl("suburl");
        VideoSpriteDTO videoSprite = new VideoSpriteDTO();
        videoSprite.setRatioInfo(ratioInfo);
        videoSprite.setVideoSpriteUrl("videourl");
        item.setVideoSprite(videoSprite);
        SpritePicVO result = immersiveImageWrapper.buildSpritePic(item);
        ImageSize imageSize = ImageSize.builder().height(10).width(10).build();
        imageSize.getHeight();
        imageSize.getWidth();
        imageSize.setHeight(5);
        imageSize.setWidth(5);
        SpritePicVO spritePicVO = SpritePicVO.builder().spriteCellSize(imageSize).allSpriteImageSize(imageSize).build();
        spritePicVO.getAllSpriteImageSize();
        spritePicVO.setAllSpriteImageSize(imageSize);
        spritePicVO.getSpriteCellSize();
        spritePicVO.setSpriteCellSize(imageSize);
        assertNotNull(item);
    }
}
