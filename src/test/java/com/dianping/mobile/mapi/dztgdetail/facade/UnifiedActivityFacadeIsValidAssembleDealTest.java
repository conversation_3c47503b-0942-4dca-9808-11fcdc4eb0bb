package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.tpfun.product.api.sku.pintuan.dto.BestPinTag;
import java.lang.reflect.Method;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

public class UnifiedActivityFacadeIsValidAssembleDealTest {

    private UnifiedActivityFacade unifiedActivityFacade = new UnifiedActivityFacade();

    private boolean invokeIsValidAssembleDeal(BestPinTag bestPinTag) throws Exception {
        Method method = UnifiedActivityFacade.class.getDeclaredMethod("isValidAssembleDeal", BestPinTag.class);
        method.setAccessible(true);
        return (boolean) method.invoke(unifiedActivityFacade, bestPinTag);
    }

    /**
     * 测试 bestPinTag 为 null 的情况
     */
    @Test
    public void testIsValidAssembleDealNullBestPinTag() throws Throwable {
        // arrange
        BestPinTag bestPinTag = null;
        // act
        boolean result = invokeIsValidAssembleDeal(bestPinTag);
        // assert
        Assert.assertFalse(result);
    }

    /**
     * 测试 bestPinTag 不为 null，但 id 为 null 的情况
     */
    @Test
    public void testIsValidAssembleDealNullId() throws Throwable {
        // arrange
        BestPinTag bestPinTag = new BestPinTag();
        bestPinTag.setId(null);
        // act
        boolean result = invokeIsValidAssembleDeal(bestPinTag);
        // assert
        Assert.assertFalse(result);
    }

    /**
     * 测试 bestPinTag 不为 null，id 不为 null，但 id 小于等于 0 的情况
     */
    @Test
    public void testIsValidAssembleDealNegativeId() throws Throwable {
        // arrange
        BestPinTag bestPinTag = new BestPinTag();
        bestPinTag.setId(0);
        // act
        boolean result = invokeIsValidAssembleDeal(bestPinTag);
        // assert
        Assert.assertFalse(result);
    }

    /**
     * 测试 bestPinTag 不为 null，id 不为 null，id 大于 0 的情况
     */
    @Test
    public void testIsValidAssembleDealPositiveId() throws Throwable {
        // arrange
        BestPinTag bestPinTag = new BestPinTag();
        bestPinTag.setId(1);
        // act
        boolean result = invokeIsValidAssembleDeal(bestPinTag);
        // assert
        Assert.assertTrue(result);
    }
}
