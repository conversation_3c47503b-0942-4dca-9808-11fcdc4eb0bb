package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.impl;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * @author: created by hang.yu on 2024/4/29 17:20
 */
@RunWith(MockitoJUnitRunner.class)
public class KtvCategoryStrategyImplTest {

    @InjectMocks
    private KtvCategoryStrategyImpl ktvCategoryStrategy;

    @Mock
    private DouHuBiz douHuBiz;


    @Test
    public void newDealStyle() {
        boolean result = ktvCategoryStrategy.newDealStyle(null);
        Assert.assertFalse(result);
    }

    @Test
    public void getModuleAbConfig() {
        ModuleAbConfig result = ktvCategoryStrategy.getModuleAbConfig(null);
        Assert.assertNull(result);
    }

    @Test
    public void getSimilarDealModuleAbConfig() {
        when(douHuBiz.getAbExpResult(any(EnvCtx.class), anyString()))
                .thenReturn(new ModuleAbConfig());
        ModuleAbConfig result = ktvCategoryStrategy.getSimilarDealModuleAbConfig(new EnvCtx());
        Assert.assertNotNull(result);
    }

}