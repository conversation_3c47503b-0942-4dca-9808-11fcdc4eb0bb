package com.dianping.mobile.mapi.dztgdetail;

import com.alibaba.fastjson.JSON;
import com.dianping.mobile.mapi.dztgdetail.button.*;
import com.dianping.mobile.mapi.dztgdetail.button.shoppingcart.ShoppingCartMemberPriceBuilder;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.entity.CostEffectivePinTuan;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dealuser.price.display.api.model.PromoIdentity;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.MemberDiscountInfoDTO;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/16
 */
public class ShoppingCartMemberPriceBuilderTest  {
    @Test
    public void test() {
        DealBuyBtn dealBuyBtn = new DealBuyBtn(true, "立即抢购");

        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(5);
        identity.setSourceType(2);
        identity.setPromoShowType("NEW_MEMBER_BENEFITS");
        promoDTO.setIdentity(identity);
        promoDTO.setAmount(new BigDecimal("5.00"));

        PriceDisplayDTO dealPromoPrice = new PriceDisplayDTO();
        dealPromoPrice.setPrice(new BigDecimal("15.00"));
        dealPromoPrice.setUsedPromos(Lists.newArrayList(promoDTO));

        PriceDisplayDTO originDealPromoPrice = new PriceDisplayDTO();
        originDealPromoPrice.setPrice(new BigDecimal("20.00"));

        EnvCtx ctx = new EnvCtx();
        DealCtx context = new DealCtx(ctx);
        context.setCostEffectivePinTuan(new CostEffectivePinTuan());
        context.getPriceContext().setMemberPromoDealPrice(true);
        context.getPriceContext().setDealPromoPrice(dealPromoPrice);
        context.getPriceContext().setOriginDealPromoPrice(originDealPromoPrice);
        context.getBuyBar().setBuyBtns(Lists.newArrayList(dealBuyBtn));
        String txt = "{\"bizCode\":10,\"memberBaseInfo\":{\"member\":false,\"memberCardName\":\"游戏厅第一张卡\",\"newMember\":false},\"memberDiscountType\":3,\"memberPageUrl\":\"imeituan://www.meituan.com/web?url=https%3A%2F%2Ftest-g.meituan.com%2Farche%2Fdzplay%2Fbiz-common-mall%2Floyalty-card.html%3Ftenantid%3D1474581%26bizcode%3D10%26shopid%3D157754883\",\"tenantId\":1474581}";
        context.setShopMemberDiscountInfoDTO(JSON.parseObject(txt, MemberDiscountInfoDTO.class));

        ButtonBuilderChain chain = ButtonBuilderFactory.newButtonBuilderChain(buildChainConfig());

        ShoppingCartMemberPriceBuilder builder = new ShoppingCartMemberPriceBuilder();
        builder.doBuild(context, chain);

        assert context.getBuyBar() != null && context.getBuyBar().getCartBtn() != null;

    }

    private static BuilderConfig buildButtonConfig(String builderName, List<BuyBtnTypeEnum> inclusiveTypes) {
        BuilderConfig config = new BuilderConfig();
        config.setBuilderName(builderName);
        if (inclusiveTypes == null) {
            config.setExclusiveAll(true);
        } else if (inclusiveTypes.isEmpty()) {
            config.setInclusiveAll(true);
        } else {
            List<ButtonStateConfig> stateConfigs = Lists.newArrayList();
            for (BuyBtnTypeEnum inclusiveType : inclusiveTypes) {
                ButtonStateConfig buttonStateConfig = new ButtonStateConfig();
                buttonStateConfig.setButtonType(inclusiveType);
                stateConfigs.add(buttonStateConfig);
            }
            config.setInclusiveType(stateConfigs);
        }

        return config;
    }

    private static BuilderChainConfig buildChainConfig() {
        BuilderChainConfig config = new BuilderChainConfig();
        config.setMaxButtonSize(5);
        config.setBuilderConfigs(Lists.newArrayList());
        addConfig2Chain(ShoppingCartMemberPriceBuilder.class, config);
        return config;
    }

    private static void addConfig2Chain(Class<? extends ButtonBuilder> clazz, BuilderChainConfig config) {
        BuilderConfig h = buildButtonConfig(clazz.getName(), Lists.newArrayList());
        config.getBuilderConfigs().add(h);
    }
}
