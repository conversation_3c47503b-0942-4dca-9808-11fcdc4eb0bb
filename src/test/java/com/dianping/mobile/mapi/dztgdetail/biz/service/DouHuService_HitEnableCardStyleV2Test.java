package com.dianping.mobile.mapi.dztgdetail.biz.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.CardStyleConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.ExpResultConfig;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DouHuService_HitEnableCardStyleV2Test {

    @InjectMocks
    private DouHuService douHuService;

    @Mock
    private LionWrapper // Mockable wrapper for Lion's static methods.
    lionWrapper;

    @Mock
    private DouHuBiz douHuBiz;

    private static ExpResultConfig expResultConfig;

    private CardStyleConfig cardStyleV2Config;

    private CardStyleConfig cardStyleNonAppConfig;

    private EnvCtx envCtx;

    private ModuleAbConfig moduleAbConfig;

    public DouHuService_HitEnableCardStyleV2Test() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Test hitEnableCardStyleV2 method when moduleAbConfig is null.
     */
    @Test
    public void testHitEnableCardStyleV2WhenModuleAbConfigIsNull() throws Throwable {
        assertFalse(douHuService.hitEnableCardStyleV2(null));
    }

    // Mockable wrapper interface for Lion's static methods
    interface LionWrapper {
    }

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // Create a custom mock implementation for LionConfigUtils
        LionConfigUtilsMock.setExpResultConfig(null);
    }

    private void setUpCommon() {
        cardStyleV2Config = new CardStyleConfig();
        cardStyleV2Config.setEnableCardStyle(true);
        cardStyleV2Config.setEnableClientType(Arrays.asList(1, 4, 11));
        Map<String, String> category2ExpId = new HashMap<>();
        category2ExpId.put("mt502", "exp002675");
        category2ExpId.put("dp502", "exp003168");
        cardStyleV2Config.setCategory2ExpId(category2ExpId);
        cardStyleV2Config.setAllPass(false);
        cardStyleNonAppConfig = new CardStyleConfig();
        cardStyleNonAppConfig.setEnableCardStyle(true);
        cardStyleNonAppConfig.setEnableClientType(Arrays.asList(2, 5, 11));
        Map<String, String> category2ExpIdNonApp = new HashMap<>();
        category2ExpIdNonApp.put("mt502", "exp002675");
        category2ExpIdNonApp.put("dp502", "exp003168");
        cardStyleNonAppConfig.setCategory2ExpId(category2ExpIdNonApp);
        cardStyleNonAppConfig.setAllPass(false);
        envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum.MEITUAN_APP);
        envCtx.setClientType(200502);
        envCtx.setVersion("12.11.400");
        moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpId("exp002675");
        abConfig.setExpResult("b");
        moduleAbConfig.setConfigs(Arrays.asList(abConfig));
    }

    /**
     * Test getRepurchaseShelfExpResult for MT platform with valid result
     */
    @Test
    public void testGetRepurchaseShelfExpResult_MtPlatform_ValidResult() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        String uuid = "test-uuid";
        envCtx.setUuid(uuid);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        String expectedResult = "test_result";
        when(douHuBiz.getAbByUuId(eq(uuid), eq("MtRepurchaseShelfExp"), eq(true))).thenReturn(moduleAbConfig);
        when(douHuBiz.getExpResult(moduleAbConfig)).thenReturn(expectedResult);
        // act
        String result = douHuService.getRepurchaseShelfExpResult(envCtx);
        // assert
        assertEquals(expectedResult, result);
        verify(douHuBiz).getAbByUuId(uuid, "MtRepurchaseShelfExp", true);
        verify(douHuBiz).getExpResult(moduleAbConfig);
    }

    /**
     * Test getRepurchaseShelfExpResult for MT platform when AbByUuId returns null
     */
    @Test
    public void testGetRepurchaseShelfExpResult_MtPlatform_NullAbResult() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        String uuid = "test-uuid";
        envCtx.setUuid(uuid);
        when(douHuBiz.getAbByUuId(anyString(), anyString(), anyBoolean())).thenReturn(null);
        // act
        String result = douHuService.getRepurchaseShelfExpResult(envCtx);
        // assert
        assertNull(result);
        verify(douHuBiz).getAbByUuId(uuid, "MtRepurchaseShelfExp", true);
    }

    /**
     * Test getRepurchaseShelfExpResult for DP platform with valid result
     */
    @Test
    public void testGetRepurchaseShelfExpResult_DpPlatform_ValidResult() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        String uuid = "test-uuid";
        envCtx.setUuid(uuid);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        String expectedResult = "test_result";
        when(douHuBiz.getAbExpResultByUuidAndDpid(any(DealCtx.class), eq("DpRepurchaseShelfExp"))).thenReturn(moduleAbConfig);
        when(douHuBiz.getExpResult(moduleAbConfig)).thenReturn(expectedResult);
        // act
        String result = douHuService.getRepurchaseShelfExpResult(envCtx);
        // assert
        assertEquals(expectedResult, result);
        verify(douHuBiz).getAbExpResultByUuidAndDpid(any(DealCtx.class), eq("DpRepurchaseShelfExp"));
        verify(douHuBiz).getExpResult(moduleAbConfig);
    }

    /**
     * Test getRepurchaseShelfExpResult for DP platform when AbExpResult returns null
     */
    @Test
    public void testGetRepurchaseShelfExpResult_DpPlatform_NullAbResult() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        String uuid = "test-uuid";
        envCtx.setUuid(uuid);
        when(douHuBiz.getAbExpResultByUuidAndDpid(any(DealCtx.class), anyString())).thenReturn(null);
        // act
        String result = douHuService.getRepurchaseShelfExpResult(envCtx);
        // assert
        assertNull(result);
        verify(douHuBiz).getAbExpResultByUuidAndDpid(any(DealCtx.class), eq("DpRepurchaseShelfExp"));
    }

    /**
     * Test getRepurchaseShelfExpResult when getExpResult returns null
     */
    @Test
    public void testGetRepurchaseShelfExpResult_NullExpResult() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        String uuid = "test-uuid";
        envCtx.setUuid(uuid);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        when(douHuBiz.getAbByUuId(anyString(), anyString(), anyBoolean())).thenReturn(moduleAbConfig);
        when(douHuBiz.getExpResult(any(ModuleAbConfig.class))).thenReturn(null);
        // act
        String result = douHuService.getRepurchaseShelfExpResult(envCtx);
        // assert
        assertNull(result);
        verify(douHuBiz).getAbByUuId(uuid, "MtRepurchaseShelfExp", true);
        verify(douHuBiz).getExpResult(moduleAbConfig);
    }

    /**
     * Test getRepurchaseShelfExpResult with null EnvCtx
     */
    @Test(expected = NullPointerException.class)
    public void testGetRepurchaseShelfExpResult_NullEnvCtx() throws Throwable {
        // arrange
        EnvCtx envCtx = null;
        // act
        douHuService.getRepurchaseShelfExpResult(envCtx);
        // assert
        // Expecting NullPointerException
    }

    /**
     * Test getRepurchaseShelfExpResult for MT platform
     */
    @Test
    public void testGetRepurchaseShelfExpResultForMt() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        String uuid = "test-uuid";
        envCtx.setUuid(uuid);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        String expectedResult = "test_result";
        when(douHuBiz.getAbByUuId(eq(uuid), eq("MtRepurchaseShelfExp"), eq(true))).thenReturn(moduleAbConfig);
        when(douHuBiz.getExpResult(moduleAbConfig)).thenReturn(expectedResult);
        // act
        String result = douHuService.getRepurchaseShelfExpResult(envCtx);
        // assert
        assertEquals(expectedResult, result);
        verify(douHuBiz).getAbByUuId(uuid, "MtRepurchaseShelfExp", true);
        verify(douHuBiz).getExpResult(moduleAbConfig);
    }

    /**
     * Test getRepurchaseShelfExpResult for DP platform
     */
    @Test
    public void testGetRepurchaseShelfExpResultForDp() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        String uuid = "test-uuid";
        envCtx.setUuid(uuid);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        String expectedResult = "test_result";
        when(douHuBiz.getAbExpResultByUuidAndDpid(any(DealCtx.class), eq("DpRepurchaseShelfExp"))).thenReturn(moduleAbConfig);
        when(douHuBiz.getExpResult(moduleAbConfig)).thenReturn(expectedResult);
        // act
        String result = douHuService.getRepurchaseShelfExpResult(envCtx);
        // assert
        assertEquals(expectedResult, result);
        verify(douHuBiz).getAbExpResultByUuidAndDpid(any(DealCtx.class), eq("DpRepurchaseShelfExp"));
        verify(douHuBiz).getExpResult(moduleAbConfig);
    }

    // Custom mock implementation for LionConfigUtils
    public static class LionConfigUtilsMock {

        private static ExpResultConfig expResultConfig;

        private static boolean throwException;

        public static ExpResultConfig getExpResultConfig() {
            if (throwException) {
                throw new RuntimeException("Test Exception");
            }
            return expResultConfig;
        }

        public static void setExpResultConfig(ExpResultConfig config) {
            expResultConfig = config;
        }

        public static void setThrowException(boolean throwExceptionFlag) {
            throwException = throwExceptionFlag;
        }
    }

    /**
     * Test the boundary scenario where ExpResultConfig is null.
     */
    @Test
    public void testGetCompareSameShopPriceStyleAbConfigByDealCtx_ExpResultConfigIsNull() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setUnionId("testUnionId");
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setDpId(123);
        ctx.setMtId(1);
        LionConfigUtilsMock.setExpResultConfig(null);
        // act
        ModuleAbConfig result = douHuService.getCompareSameShopPriceStyleAbConfigByDealCtx(ctx);
        // assert
        assertNull(result);
    }

    /**
     * Test the boundary scenario where categoryId2Module map is empty.
     */
    @Test
    public void testGetCompareSameShopPriceStyleAbConfigByDealCtx_CategoryId2ModuleIsEmpty() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setUnionId("testUnionId");
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setDpId(123);
        ctx.setMtId(1);
        expResultConfig = new ExpResultConfig();
        expResultConfig.setCategoryId2Module(new HashMap<>());
        LionConfigUtilsMock.setExpResultConfig(expResultConfig);
        // act
        ModuleAbConfig result = douHuService.getCompareSameShopPriceStyleAbConfigByDealCtx(ctx);
        // assert
        assertNull(result);
    }

    /**
     * Test the boundary scenario where moduleKey does not exist in categoryId2Module map.
     */
    @Test
    public void testGetCompareSameShopPriceStyleAbConfigByDealCtx_ModuleKeyNotFound() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setUnionId("testUnionId");
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setDpId(123);
        ctx.setMtId(1);
        expResultConfig = new ExpResultConfig();
        Map<String, String> categoryId2Module = new HashMap<>();
        categoryId2Module.put("456mt", "testModule");
        expResultConfig.setCategoryId2Module(categoryId2Module);
        LionConfigUtilsMock.setExpResultConfig(expResultConfig);
        // act
        ModuleAbConfig result = douHuService.getCompareSameShopPriceStyleAbConfigByDealCtx(ctx);
        // assert
        assertNull(result);
    }

    @Test(expected = NullPointerException.class)
    public void testGetNativeDealDetailAbTestResultWhenCtxIsNull() throws Throwable {
        // Expecting NullPointerException due to the null context
        douHuService.getNativeDealDetailAbTestResult(null);
    }

    @Test
    public void testGetNativeDealDetailAbTestResultWhenUnionIdIsNull() throws Throwable {
        EnvCtx ctx = new EnvCtx();
        ModuleAbConfig result = douHuService.getNativeDealDetailAbTestResult(ctx);
        assertNull(result);
    }

    @Test
    public void testGetNativeDealDetailAbTestResultWhenIsMtIsTrue() throws Throwable {
        EnvCtx ctx = new EnvCtx();
        ctx.setUnionId("unionId");
        ctx.setDztgClientTypeEnum(com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum.MEITUAN_APP);
        when(douHuBiz.getAbByUnionId(anyString(), anyString(), anyBoolean())).thenReturn(null);
        ModuleAbConfig result = douHuService.getNativeDealDetailAbTestResult(ctx);
        assertNull(result);
    }

    @Test
    public void testGetNativeDealDetailAbTestResultWhenIsMtIsFalse() throws Throwable {
        EnvCtx ctx = new EnvCtx();
        ctx.setUnionId("unionId");
        ctx.setDztgClientTypeEnum(com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum.DIANPING_APP);
        when(douHuBiz.getAbByUnionId(anyString(), anyString(), anyBoolean())).thenReturn(null);
        ModuleAbConfig result = douHuService.getNativeDealDetailAbTestResult(ctx);
        assertNull(result);
    }

    @Test
    public void testGetNativeDealDetailAbTestResultWhenGetAbByUnionIdReturnsNull() throws Throwable {
        EnvCtx ctx = new EnvCtx();
        ctx.setUnionId("unionId");
        ctx.setDztgClientTypeEnum(com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum.MEITUAN_APP);
        when(douHuBiz.getAbByUnionId(anyString(), anyString(), anyBoolean())).thenReturn(null);
        ModuleAbConfig result = douHuService.getNativeDealDetailAbTestResult(ctx);
        assertNull(result);
    }

    @Test
    public void testGetNativeDealDetailAbTestResultWhenGetAbByUnionIdReturnsNonNull() throws Throwable {
        EnvCtx ctx = new EnvCtx();
        ctx.setUnionId("unionId");
        ctx.setDztgClientTypeEnum(com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum.MEITUAN_APP);
        ModuleAbConfig expected = new ModuleAbConfig();
        when(douHuBiz.getAbByUnionId(anyString(), anyString(), anyBoolean())).thenReturn(expected);
        ModuleAbConfig result = douHuService.getNativeDealDetailAbTestResult(ctx);
        assertEquals(expected, result);
    }

    /**
     * Test when ctx is null should return false
     */
    @Test
    public void testIsHitRepairPayAbTestWhenCtxIsNull() throws Throwable {
        // arrange
        DealCtx ctx = null;
        // act & assert
        try {
            boolean result = DouHuService.isHitRepairPayAbTest(ctx);
            fail("Should throw NPE");
        } catch (NullPointerException e) {
            // Expected NPE
            assertTrue(true);
        }
    }

    /**
     * Test when moduleAbConfigs is null should return false
     */
    @Test
    public void testIsHitRepairPayAbTestWhenModuleAbConfigsIsNull() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isMt()).thenReturn(false);
        when(ctx.getModuleAbConfigs()).thenReturn(null);
        // act & assert
        try {
            boolean result = DouHuService.isHitRepairPayAbTest(ctx);
            fail("Should throw NPE");
        } catch (NullPointerException e) {
            // Expected NPE
            assertTrue(true);
        }
    }

    /**
     * Test when moduleAbConfigs is empty should return false
     */
    @Test
    public void testIsHitRepairPayAbTestWhenModuleAbConfigsIsEmpty() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isMt()).thenReturn(false);
        when(ctx.getModuleAbConfigs()).thenReturn(Collections.emptyList());
        // act
        boolean result = DouHuService.isHitRepairPayAbTest(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * Test when no matching ModuleAbConfig found should return false
     */
    @Test
    public void testIsHitRepairPayAbTestWhenNoMatchingModuleAbConfig() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isMt()).thenReturn(false);
        ModuleAbConfig nonMatchingConfig = new ModuleAbConfig();
        nonMatchingConfig.setKey("OtherModule");
        nonMatchingConfig.setConfigs(new ArrayList<>());
        when(ctx.getModuleAbConfigs()).thenReturn(Arrays.asList(nonMatchingConfig));
        // act
        boolean result = DouHuService.isHitRepairPayAbTest(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * Test when matching ModuleAbConfig found but configs is null should return false
     */
    @Test
    public void testIsHitRepairPayAbTestWhenMatchingModuleAbConfigButConfigsNull() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isMt()).thenReturn(true);
        ModuleAbConfig matchingConfig = new ModuleAbConfig();
        matchingConfig.setKey("MtRepairPayModule");
        // Initialize with empty list instead of null
        matchingConfig.setConfigs(new ArrayList<>());
        when(ctx.getModuleAbConfigs()).thenReturn(Arrays.asList(matchingConfig));
        // act
        boolean result = DouHuService.isHitRepairPayAbTest(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * Test when matching ModuleAbConfig found but configs is empty should return false
     */
    @Test
    public void testIsHitRepairPayAbTestWhenMatchingModuleAbConfigButConfigsEmpty() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isMt()).thenReturn(true);
        ModuleAbConfig matchingConfig = new ModuleAbConfig();
        matchingConfig.setKey("MtRepairPayModule");
        matchingConfig.setConfigs(Collections.emptyList());
        when(ctx.getModuleAbConfigs()).thenReturn(Arrays.asList(matchingConfig));
        // act
        boolean result = DouHuService.isHitRepairPayAbTest(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * Test when matching ModuleAbConfig found with expResult "b" should return true
     */
    @Test
    public void testIsHitRepairPayAbTestWhenMatchingModuleAbConfigWithExpResultB() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isMt()).thenReturn(false);
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("b");
        ModuleAbConfig matchingConfig = new ModuleAbConfig();
        matchingConfig.setKey("DpRepairPayModule");
        matchingConfig.setConfigs(Arrays.asList(abConfig));
        when(ctx.getModuleAbConfigs()).thenReturn(Arrays.asList(matchingConfig));
        // act
        boolean result = DouHuService.isHitRepairPayAbTest(ctx);
        // assert
        assertTrue(result);
    }

    /**
     * Test when matching ModuleAbConfig found with expResult not "b" should return false
     */
    @Test
    public void testIsHitRepairPayAbTestWhenMatchingModuleAbConfigWithExpResultNotB() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isMt()).thenReturn(false);
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("a");
        ModuleAbConfig matchingConfig = new ModuleAbConfig();
        matchingConfig.setKey("DpRepairPayModule");
        matchingConfig.setConfigs(Arrays.asList(abConfig));
        when(ctx.getModuleAbConfigs()).thenReturn(Arrays.asList(matchingConfig));
        // act
        boolean result = DouHuService.isHitRepairPayAbTest(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * Test when multiple ModuleAbConfigs exist but only one matches should return correct result
     */
    @Test
    public void testIsHitRepairPayAbTestWhenMultipleModuleAbConfigs() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isMt()).thenReturn(true);
        ModuleAbConfig nonMatchingConfig = new ModuleAbConfig();
        nonMatchingConfig.setKey("OtherModule");
        nonMatchingConfig.setConfigs(new ArrayList<>());
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("b");
        ModuleAbConfig matchingConfig = new ModuleAbConfig();
        matchingConfig.setKey("MtRepairPayModule");
        matchingConfig.setConfigs(Arrays.asList(abConfig));
        when(ctx.getModuleAbConfigs()).thenReturn(Arrays.asList(nonMatchingConfig, matchingConfig));
        // act
        boolean result = DouHuService.isHitRepairPayAbTest(ctx);
        // assert
        assertTrue(result);
    }

    /**
     * Test when multiple AbConfigs exist but first is null should skip it
     */
    @Test
    public void testIsHitRepairPayAbTestWhenFirstAbConfigIsNull() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isMt()).thenReturn(false);
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("b");
        List<AbConfig> configs = new ArrayList<>();
        configs.add(null);
        configs.add(abConfig);
        ModuleAbConfig matchingConfig = new ModuleAbConfig();
        matchingConfig.setKey("DpRepairPayModule");
        matchingConfig.setConfigs(configs);
        when(ctx.getModuleAbConfigs()).thenReturn(Arrays.asList(matchingConfig));
        // act
        boolean result = DouHuService.isHitRepairPayAbTest(ctx);
        // assert
        assertTrue(result);
    }

    /**
     * Test case: when unionId is null
     */
    @Test
    public void testGetCompareSameShopPriceStyleAbConfigByEnvCtxWhenUnionIdIsNull() throws Throwable {
        // arrange
        Integer categoryId = 1;
        EnvCtx ctx = new EnvCtx();
        ctx.setUnionId(null);
        // act
        ModuleAbConfig result = douHuService.getCompareSameShopPriceStyleAbConfigByEnvCtx(categoryId, ctx);
        // assert
        assertNull(result);
        verifyNoInteractions(douHuBiz);
    }

    /**
     * Test case for null ModuleAbConfig input
     */
    @Test
    public void testGetExpABId_NullInput() throws Throwable {
        // arrange
        ModuleAbConfig moduleAbConfig = null;
        // act
        String result = douHuService.getExpABId(moduleAbConfig);
        // assert
        assertNull("Result should be null for null input", result);
    }

    /**
     * Test case for ModuleAbConfig with null configs list
     */
    @Test
    public void testGetExpABId_NullConfigs() throws Throwable {
        // arrange
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setConfigs(null);
        // act
        String result = douHuService.getExpABId(moduleAbConfig);
        // assert
        assertNull("Result should be null when configs is null", result);
    }

    /**
     * Test case for ModuleAbConfig with empty configs list
     */
    @Test
    public void testGetExpABId_EmptyConfigs() throws Throwable {
        // arrange
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setConfigs(new ArrayList<>());
        // act
        String result = douHuService.getExpABId(moduleAbConfig);
        // assert
        assertNull("Result should be null when configs is empty", result);
    }

    /**
     * Test case for ModuleAbConfig with null first config
     */
    @Test
    public void testGetExpABId_NullFirstConfig() throws Throwable {
        // arrange
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        List<AbConfig> configs = new ArrayList<>();
        configs.add(null);
        moduleAbConfig.setConfigs(configs);
        // act
        String result = douHuService.getExpABId(moduleAbConfig);
        // assert
        assertNull("Result should be null when first config is null", result);
    }

    /**
     * Test case for valid ModuleAbConfig with single config
     */
    @Test
    public void testGetExpABId_ValidSingleConfig() throws Throwable {
        // arrange
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpId("testExpId");
        abConfig.setExpResult("testResult");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        // act
        String result = douHuService.getExpABId(moduleAbConfig);
        // assert
        assertEquals("Result should be expId_expResult", "testExpId_testResult", result);
    }

    /**
     * Test case for ModuleAbConfig with multiple configs
     */
    @Test
    public void testGetExpABId_MultipleConfigs() throws Throwable {
        // arrange
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig firstConfig = new AbConfig();
        firstConfig.setExpId("firstExpId");
        firstConfig.setExpResult("firstResult");
        AbConfig secondConfig = new AbConfig();
        secondConfig.setExpId("secondExpId");
        secondConfig.setExpResult("secondResult");
        moduleAbConfig.setConfigs(Arrays.asList(firstConfig, secondConfig));
        // act
        String result = douHuService.getExpABId(moduleAbConfig);
        // assert
        assertEquals("Should only use first config for result", "firstExpId_firstResult", result);
    }

    /**
     * Test case for ModuleAbConfig with null expId in first config
     */
    @Test
    public void testGetExpABId_NullExpId() throws Throwable {
        // arrange
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpId(null);
        abConfig.setExpResult("testResult");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        // act
        String result = douHuService.getExpABId(moduleAbConfig);
        // assert
        assertEquals("Should handle null expId", "null_testResult", result);
    }

    /**
     * Test case for ModuleAbConfig with null expResult in first config
     */
    @Test
    public void testGetExpABId_NullExpResult() throws Throwable {
        // arrange
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpId("testExpId");
        abConfig.setExpResult(null);
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        // act
        String result = douHuService.getExpABId(moduleAbConfig);
        // assert
        assertEquals("Should handle null expResult", "testExpId_null", result);
    }

    /**
     * Test getExpABId when moduleAbConfig is null
     */
    @Test
    public void testGetExpABId_WhenModuleAbConfigIsNull() throws Throwable {
        // arrange
        ModuleAbConfig moduleAbConfig = null;
        // act
        String result = douHuService.getExpABId(moduleAbConfig);
        // assert
        assertNull("Should return null when moduleAbConfig is null", result);
    }

    /**
     * Test getExpABId when configs list is null
     */
    @Test
    public void testGetExpABId_WhenConfigsIsNull() throws Throwable {
        // arrange
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setConfigs(null);
        // act
        String result = douHuService.getExpABId(moduleAbConfig);
        // assert
        assertNull("Should return null when configs is null", result);
    }

    /**
     * Test getExpABId when configs list is empty
     */
    @Test
    public void testGetExpABId_WhenConfigsIsEmpty() throws Throwable {
        // arrange
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setConfigs(Collections.emptyList());
        // act
        String result = douHuService.getExpABId(moduleAbConfig);
        // assert
        assertNull("Should return null when configs is empty", result);
    }

    /**
     * Test getExpABId when first config in the list is null
     */
    @Test
    public void testGetExpABId_WhenFirstConfigIsNull() throws Throwable {
        // arrange
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setConfigs(Arrays.asList(null, new AbConfig()));
        // act
        String result = douHuService.getExpABId(moduleAbConfig);
        // assert
        assertNull("Should return null when first config is null", result);
    }

    /**
     * Test getExpABId with valid input data
     */
    @Test
    public void testGetExpABId_WithValidInput() throws Throwable {
        // arrange
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpId("test_exp_id");
        abConfig.setExpResult("test_result");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        // act
        String result = douHuService.getExpABId(moduleAbConfig);
        // assert
        assertEquals("Should return concatenated expId and expResult", "test_exp_id_test_result", result);
    }

    /**
     * Test getExpABId with multiple configs in the list
     */
    @Test
    public void testGetExpABId_WithMultipleConfigs() throws Throwable {
        // arrange
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig firstConfig = new AbConfig();
        firstConfig.setExpId("first_exp_id");
        firstConfig.setExpResult("first_result");
        AbConfig secondConfig = new AbConfig();
        secondConfig.setExpId("second_exp_id");
        secondConfig.setExpResult("second_result");
        moduleAbConfig.setConfigs(Arrays.asList(firstConfig, secondConfig));
        // act
        String result = douHuService.getExpABId(moduleAbConfig);
        // assert
        assertEquals("Should return concatenated expId and expResult of first config only", "first_exp_id_first_result", result);
    }

    /**
     * 测试 getExpABId 方法，当 ModuleAbConfig 对象为 null 时，应返回 null
     */
    @Test
    public void testGetExpABIdNullModuleAbConfig() throws Throwable {
        assertNull(douHuService.getExpABId(null));
    }

    @Test
    public void testEnableCardStyleV2_CardStyleConfigsNotEnableCardStyle() throws Throwable {
        setUpCommon();
        cardStyleV2Config.setEnableCardStyle(false);
        // Corrected the method call to include a boolean argument
        cardStyleNonAppConfig.setEnableCardStyle(false);
        when(douHuBiz.getAbByUnionIdAndExpId(any(), any(), any(), anyBoolean())).thenReturn(moduleAbConfig);
        ModuleAbConfig result = douHuService.enableCardStyleV2(envCtx, 50200, "0.5.7");
        assertNotNull(result);
    }

    /**
     * Test MT context with successful AB test result
     */
    @Test
    public void testGetRepairPayAbTestResult_MtContext_Success() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        envCtx.setUnionId("testUnionId");
        ModuleAbConfig expectedConfig = new ModuleAbConfig();
        when(douHuBiz.getAbByUnionId("testUnionId", "MtRepairPayModule", true)).thenReturn(expectedConfig);
        // act
        ModuleAbConfig result = douHuService.getRepairPayAbTestResult(envCtx);
        // assert
        assertNotNull("Result should not be null", result);
        assertSame("Should return the expected config", expectedConfig, result);
        verify(douHuBiz).getAbByUnionId("testUnionId", "MtRepairPayModule", true);
    }

    /**
     * Test DP context with successful AB test result
     */
    @Test
    public void testGetRepairPayAbTestResult_DpContext_Success() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        envCtx.setUnionId("testUnionId");
        ModuleAbConfig expectedConfig = new ModuleAbConfig();
        when(douHuBiz.getAbByUnionId("testUnionId", "DpRepairPayModule", false)).thenReturn(expectedConfig);
        // act
        ModuleAbConfig result = douHuService.getRepairPayAbTestResult(envCtx);
        // assert
        assertNotNull("Result should not be null", result);
        assertSame("Should return the expected config", expectedConfig, result);
        verify(douHuBiz).getAbByUnionId("testUnionId", "DpRepairPayModule", false);
    }

    /**
     * Test MT context with null AB test result
     */
    @Test
    public void testGetRepairPayAbTestResult_MtContext_NullResult() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        envCtx.setUnionId("testUnionId");
        when(douHuBiz.getAbByUnionId("testUnionId", "MtRepairPayModule", true)).thenReturn(null);
        // act
        ModuleAbConfig result = douHuService.getRepairPayAbTestResult(envCtx);
        // assert
        assertNull("Result should be null", result);
        verify(douHuBiz).getAbByUnionId("testUnionId", "MtRepairPayModule", true);
    }

    /**
     * Test DP context with null AB test result
     */
    @Test
    public void testGetRepairPayAbTestResult_DpContext_NullResult() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        envCtx.setUnionId("testUnionId");
        when(douHuBiz.getAbByUnionId("testUnionId", "DpRepairPayModule", false)).thenReturn(null);
        // act
        ModuleAbConfig result = douHuService.getRepairPayAbTestResult(envCtx);
        // assert
        assertNull("Result should be null", result);
        verify(douHuBiz).getAbByUnionId("testUnionId", "DpRepairPayModule", false);
    }

    /**
     * Test exception case when calling DouHuBiz
     * The method should handle exceptions internally and return null
     */
    @Test
    public void testGetRepairPayAbTestResult_ExceptionCase() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        envCtx.setUnionId("testUnionId");
        // DouHuBiz handles exceptions internally and returns null
        // DouHuBiz handles exceptions internally and returns null
        when(douHuBiz.getAbByUnionId(anyString(), anyString(), anyBoolean())).thenReturn(null);
        // act
        ModuleAbConfig result = douHuService.getRepairPayAbTestResult(envCtx);
        // assert
        assertNull("Result should be null when exception occurs", result);
        verify(douHuBiz).getAbByUnionId("testUnionId", "MtRepairPayModule", true);
    }

    /**
     * Test with null unionId in EnvCtx
     */
    @Test
    public void testGetRepairPayAbTestResult_NullUnionId() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        // unionId is null by default
        when(douHuBiz.getAbByUnionId(null, "MtRepairPayModule", true)).thenReturn(null);
        // act
        ModuleAbConfig result = douHuService.getRepairPayAbTestResult(envCtx);
        // assert
        assertNull("Result should be null for null unionId", result);
        verify(douHuBiz).getAbByUnionId(null, "MtRepairPayModule", true);
    }

    /**
     * Test with empty unionId in EnvCtx
     */
    @Test
    public void testGetRepairPayAbTestResult_EmptyUnionId() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        envCtx.setUnionId("");
        when(douHuBiz.getAbByUnionId("", "MtRepairPayModule", true)).thenReturn(null);
        // act
        ModuleAbConfig result = douHuService.getRepairPayAbTestResult(envCtx);
        // assert
        assertNull("Result should be null for empty unionId", result);
        verify(douHuBiz).getAbByUnionId("", "MtRepairPayModule", true);
    }

    /**
     * Test when DealCtx is null, should throw NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testBuildCatStr_NullDealCtx() throws Throwable {
        // arrange
        DouHuService service = new DouHuService();
        // act
        service.buildCatStr(null);
        // assert - expect NPE
    }

    /**
     * Test when DealGroupDTO is null, should return empty string
     */
    @Test
    public void testBuildCatStr_NullDealGroupDTO() throws Throwable {
        // arrange
        DouHuService service = new DouHuService();
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.getDealGroupDTO()).thenReturn(null);
        // act
        String result = service.buildCatStr(ctx);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when Category is null, should return empty string
     */
    @Test
    public void testBuildCatStr_NullCategory() throws Throwable {
        // arrange
        DouHuService service = new DouHuService();
        DealCtx ctx = mock(DealCtx.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(null);
        // act
        String result = service.buildCatStr(ctx);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when both DealGroupDTO and Category exist, should return formatted string
     */
    @Test
    public void testBuildCatStr_ValidCategory() throws Throwable {
        // arrange
        DouHuService service = new DouHuService();
        DealCtx ctx = mock(DealCtx.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(category.getCategoryId()).thenReturn(12345L);
        when(category.getServiceType()).thenReturn("massage");
        // act
        String result = service.buildCatStr(ctx);
        // assert
        assertEquals("12345:massage", result);
    }

    /**
     * Test when serviceType is empty, should still return formatted string
     */
    @Test
    public void testBuildCatStr_EmptyServiceType() throws Throwable {
        // arrange
        DouHuService service = new DouHuService();
        DealCtx ctx = mock(DealCtx.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(category.getCategoryId()).thenReturn(67890L);
        when(category.getServiceType()).thenReturn("");
        // act
        String result = service.buildCatStr(ctx);
        // assert
        assertEquals("67890:", result);
    }
}
