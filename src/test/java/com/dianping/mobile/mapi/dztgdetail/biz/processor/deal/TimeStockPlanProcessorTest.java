package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealTimeStockDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class TimeStockPlanProcessorTest {

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @InjectMocks
    private TimeStockPlanProcessor processor;

    /**
     * Test successful case where DealGroupDTO has deals with timeStock info
     */
    @Test
    public void testProcess_WithValidDealsAndTimeStock() throws Exception {
        // arrange
        DealCtx ctx = new DealCtx(null);
        FutureCtx futureCtx = new FutureCtx();
        Future future = mock(Future.class);
        futureCtx.setTimeStockFuture(future);
        ctx.setFutureCtx(futureCtx);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<DealGroupDealDTO> deals = new ArrayList<>();
        DealGroupDealDTO deal = new DealGroupDealDTO();
        DealTimeStockDTO timeStockDTO = new DealTimeStockDTO();
        deal.setDealTimeStockDTO(timeStockDTO);
        deals.add(deal);
        dealGroupDTO.setDeals(deals);
        when(queryCenterWrapper.getDealGroupDTO(any(Future.class))).thenReturn(dealGroupDTO);
        // act
        processor.process(ctx);
        // assert
        assertFalse(ctx.isQueryCenterHasError());
        assertEquals(timeStockDTO, ctx.getDealTimeStockDTO());
        verify(queryCenterWrapper).getDealGroupDTO(future);
    }

    /**
     * Test case where DealGroupDTO has empty deals list
     */
    @Test
    public void testProcess_WithEmptyDeals() throws Exception {
        // arrange
        DealCtx ctx = new DealCtx(null);
        FutureCtx futureCtx = new FutureCtx();
        Future future = mock(Future.class);
        futureCtx.setTimeStockFuture(future);
        ctx.setFutureCtx(futureCtx);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDeals(Collections.emptyList());
        when(queryCenterWrapper.getDealGroupDTO(any(Future.class))).thenReturn(dealGroupDTO);
        // act
        processor.process(ctx);
        // assert
        assertFalse(ctx.isQueryCenterHasError());
        assertNull(ctx.getDealTimeStockDTO());
        verify(queryCenterWrapper).getDealGroupDTO(future);
    }

    /**
     * Test case where DealGroupDTO is null
     */
    @Test
    public void testProcess_WithNullDealGroupDTO() throws Exception {
        // arrange
        DealCtx ctx = new DealCtx(null);
        FutureCtx futureCtx = new FutureCtx();
        Future future = mock(Future.class);
        futureCtx.setTimeStockFuture(future);
        ctx.setFutureCtx(futureCtx);
        when(queryCenterWrapper.getDealGroupDTO(any(Future.class))).thenReturn(null);
        // act
        processor.process(ctx);
        // assert
        assertFalse(ctx.isQueryCenterHasError());
        assertNull(ctx.getDealTimeStockDTO());
        verify(queryCenterWrapper).getDealGroupDTO(future);
    }

    /**
     * Test case where getDealGroupDTO throws exception
     */
    @Test
    public void testProcess_WhenExceptionOccurs() throws Exception {
        // arrange
        DealCtx ctx = new DealCtx(null);
        FutureCtx futureCtx = new FutureCtx();
        Future future = mock(Future.class);
        futureCtx.setTimeStockFuture(future);
        ctx.setFutureCtx(futureCtx);
        when(queryCenterWrapper.getDealGroupDTO(any(Future.class))).thenThrow(new RuntimeException("Test exception"));
        // act
        processor.process(ctx);
        // assert
        assertTrue(ctx.isQueryCenterHasError());
        assertNull(ctx.getDealTimeStockDTO());
        verify(queryCenterWrapper).getDealGroupDTO(future);
    }
}
