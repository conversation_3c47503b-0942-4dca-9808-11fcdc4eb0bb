package com.dianping.mobile.mapi.dztgdetail.biz.processor;

import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.CardStyleProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.constants.CatEvents;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.entity.CardStyleConfig;
import com.dianping.mobile.mapi.dztgdetail.util.JsonUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.mockStatic;

/**
 * @Author: <EMAIL>
 * @Date: 2024/4/9
 */
@RunWith(MockitoJUnitRunner.class)
public class CardStyleProcessorTest {
    @InjectMocks
    private CardStyleProcessor cardStyleProcessor;
    private MockedStatic<Lion> lionMockedStatic;
    @Mock
    private DouHuBiz douHuBiz;

    @Before
    public void setUp() {
        lionMockedStatic = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        lionMockedStatic.close();
    }

    @Test
    public void testCardStyleV2Metric() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        envCtx.setVersion("12.14.10");
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(502);
        ctx.setChannelDTO(channelDTO);
        ctx.setMrnVersion("10.3.1");
        cardStyleProcessor.cardStyleV2Metric(ctx, CatEvents.NOT_HIT_CARD_STYLE_V2, "hit");
        Assert.assertTrue(ctx.getCategoryId() == 502);
    }

    @Test
    public void testEnableCardStyle() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        envCtx.setVersion("12.11.200");
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(502);
        ctx.setChannelDTO(channelDTO);
        ctx.setMrnVersion("0.5.3");
        CardStyleConfig cardStyleConfig = JsonUtils.fromJson("{\"enableCardStyle\":true,\"enableClientType\":[1,4,11],\"category2ExpId\":{\"mt502\":\"exp002675\",\"mt2001\":\"exp002908\",\"dp502\":\"exp002676\",\"dp2001\":\"exp002909\"},\"allPass\":false}", CardStyleConfig.class);
        lionMockedStatic.when(() -> Lion.getBean("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.card.style.config", CardStyleConfig.class)).thenReturn(cardStyleConfig);
        boolean result = cardStyleProcessor.enableCardStyle(ctx);
        Assert.assertTrue(!result);
    }
}
