package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.style;

import static org.junit.Assert.*;
import com.sankuai.mpmctcontent.query.thrift.dto.ItemFieldDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.aggre.SearchFusionInfoReqDTO;
import org.junit.Test;

public class ParallBeautyAdaptorTest {

    /**
     * 测试 buildReq 方法是否能正确创建 SearchFusionInfoReqDTO 对象并设置其属性
     */
    @Test
    public void testBuildReq() throws Throwable {
        // arrange
        int dpId = 123;
        // act
        SearchFusionInfoReqDTO result = ParallBeautyAdaptor.buildReq(dpId);
        // assert
        assertNotNull(result);
        assertNull(result.getOwnerId());
        assertNull(result.getOwnerType());
        assertEquals(27, result.getSubBizType().intValue());
        assertEquals(0, result.getStart().intValue());
        assertEquals(50, result.getLimit().intValue());
        assertEquals("manicure_deal_related_module", result.getModuleKey());
        assertEquals(2, result.getBizType().intValue());
        assertFalse(result.getNeedValidateInterest());
        assertEquals(1, result.getQueryList().size());
        ItemFieldDTO query = result.getQueryList().get(0);
        assertEquals("relatedDpDealId", query.getFieldCode());
        assertEquals(String.valueOf(dpId), query.getFieldValue());
    }
}
