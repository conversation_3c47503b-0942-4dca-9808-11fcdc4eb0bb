package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealAttrHelper_AllDayAvailableTest {

    private static final String HOLIDAY_AVAILABLE = "HOLIDAY_AVAILABLE";

    private static Method isRepairPrepayDealMethod;

    @BeforeClass
    public static void setUpClass() throws Exception {
        // Use reflection to access the private method
        isRepairPrepayDealMethod = DealAttrHelper.class.getDeclaredMethod("isRepairPrepayDeal", AttrDTO.class);
        isRepairPrepayDealMethod.setAccessible(true);
    }

    @Test
    public void testAllDayAvailableWhenAttrsIsNull() throws Throwable {
        boolean result = DealAttrHelper.allDayAvailable(null);
        assertFalse("Expected false when attrs is null", result);
    }

    @Test
    public void testAllDayAvailableWhenNoHolidayAvailable() throws Throwable {
        AttributeDTO attr = new AttributeDTO();
        attr.setName("OTHER");
        attr.setValue(Collections.singletonList("1"));
        boolean result = DealAttrHelper.allDayAvailable(Collections.singletonList(attr));
        assertFalse("Expected false when no HOLIDAY_AVAILABLE attribute is present", result);
    }

    @Test
    public void testAllDayAvailableWhenHolidayAvailableValueIsNull() throws Throwable {
        AttributeDTO attr = new AttributeDTO();
        attr.setName(HOLIDAY_AVAILABLE);
        attr.setValue(null);
        boolean result = DealAttrHelper.allDayAvailable(Collections.singletonList(attr));
        assertFalse("Expected false when HOLIDAY_AVAILABLE attribute's value is null", result);
    }

    @Test
    public void testAllDayAvailableWhenHolidayAvailableValueIsNotOne() throws Throwable {
        AttributeDTO attr = new AttributeDTO();
        attr.setName(HOLIDAY_AVAILABLE);
        // Adjusted to clearly not "1"
        attr.setValue(Collections.singletonList("0"));
        boolean result = DealAttrHelper.allDayAvailable(Collections.singletonList(attr));
        assertFalse("Expected false when HOLIDAY_AVAILABLE attribute's value is not '1'", result);
    }

    @Test
    public void testAllDayAvailableWhenHolidayAvailableValueIsNotOne2() throws Throwable {
        AttributeDTO attr = new AttributeDTO();
        attr.setName(HOLIDAY_AVAILABLE);
        attr.setValue(Collections.singletonList("2"));
        boolean result = DealAttrHelper.allDayAvailable(Collections.singletonList(attr));
        assertFalse("Expected false when HOLIDAY_AVAILABLE attribute's value is '2'", result);
    }

    @Test
    public void testIsRepairPrepayDealNullAttrDTO() throws Throwable {
        AttrDTO attrDTO = null;
        boolean result = (boolean) isRepairPrepayDealMethod.invoke(null, attrDTO);
        assertFalse(result);
    }

    @Test
    public void testIsRepairPrepayDealEmptyNameOrValue() throws Throwable {
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("");
        attrDTO.setValue(Arrays.asList(""));
        boolean result = (boolean) isRepairPrepayDealMethod.invoke(null, attrDTO);
        assertFalse(result);
    }

    @Test
    public void testIsRepairPrepayDealNotMatchPrepayAttrName() throws Throwable {
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("other_project_type");
        attrDTO.setValue(Arrays.asList("上门费"));
        boolean result = (boolean) isRepairPrepayDealMethod.invoke(null, attrDTO);
        assertFalse(result);
    }

    @Test
    public void testIsRepairPrepayDealNotMatchPrepayAttrValue() throws Throwable {
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("computer_project_type");
        attrDTO.setValue(Arrays.asList("other_value"));
        boolean result = (boolean) isRepairPrepayDealMethod.invoke(null, attrDTO);
        assertFalse(result);
    }

    @Test
    public void testIsRepairPrepayDealMatchPrepayAttrValue() throws Throwable {
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("computer_project_type");
        attrDTO.setValue(Arrays.asList("上门费"));
        boolean result = (boolean) isRepairPrepayDealMethod.invoke(null, attrDTO);
        assertTrue(result);
    }
}
