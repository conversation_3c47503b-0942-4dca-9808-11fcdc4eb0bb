package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.biz.DealBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.DealCampaignBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.RecommendWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.CollaborativeRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.MtCollaborativeResponse;
import com.meituan.service.mobile.message.recommend.CollaborativeResponseMessage;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import static org.mockito.Mockito.*;
import com.meituan.service.mobile.message.recommend.DealStid;
import com.meituan.service.mobile.message.recommend.RecommendDealMessage;
import com.meituan.service.mobile.prometheus.client.exception.CException;
import java.util.List;
import static org.junit.Assert.*;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class RecommendFacadeTest {

    @InjectMocks
    private RecommendFacade recommendFacade;

    @Mock
    private DealBiz dealBiz;

    @Mock
    private DealCampaignBiz dealCampaignBiz2;

    @Mock
    private RecommendWrapper recommendWrapper;

    @Mock
    private IMobileContext context;

    @Mock
    private CollaborativeRequest request;

    @Mock
    private CollaborativeResponseMessage collaborativeResp;

    private void commonSetup() {
        when(collaborativeResp.getDeals()).thenReturn(new ArrayList<>());
        when(collaborativeResp.getStids()).thenReturn(new ArrayList<>());
        // Mocking context.getUserStatus().getMtUserId() to return a non-null value
        com.dianping.mobile.framework.datatypes.token.UserStatusResult userStatus = mock(com.dianping.mobile.framework.datatypes.token.UserStatusResult.class);
        // Assuming 12345 is a valid userId
        when(userStatus.getMtUserId()).thenReturn(12345L);
        when(context.getUserStatus()).thenReturn(userStatus);
    }

    @Test
    public void testGetCollaborativeDealGroup_CollaborativeRespIsNull() throws Throwable {
        commonSetup();
        when(recommendWrapper.getCollaborativeRecommend(eq(request), any())).thenReturn(null);
        MtCollaborativeResponse response = recommendFacade.getCollaborativeDealGroup(request, context);
        assertNull(response);
    }

    @Test
    public void testGetCollaborativeDealGroup_CollaborativeRespDealsIsEmpty() throws Throwable {
        commonSetup();
        when(recommendWrapper.getCollaborativeRecommend(eq(request), any())).thenReturn(collaborativeResp);
        MtCollaborativeResponse response = recommendFacade.getCollaborativeDealGroup(request, context);
        assertNotNull(response);
    }

    @Test
    public void testGetCollaborativeDealGroup_FieldsIsEmpty() throws Throwable {
        commonSetup();
        when(recommendWrapper.getCollaborativeRecommend(eq(request), any())).thenReturn(collaborativeResp);
        MtCollaborativeResponse response = recommendFacade.getCollaborativeDealGroup(request, context);
        assertNotNull(response);
    }

    @Test
    public void testGetCollaborativeDealGroup_SceneIs2() throws Throwable {
        commonSetup();
        when(request.getScene()).thenReturn(2);
        when(recommendWrapper.getCollaborativeRecommend(eq(request), any())).thenReturn(collaborativeResp);
        MtCollaborativeResponse response = recommendFacade.getCollaborativeDealGroup(request, context);
        assertNotNull(response);
    }

    @Test(expected = Exception.class)
    public void testGetCollaborativeDealGroup_DealBizGetDealListByDidsThrowsException() throws Throwable {
        commonSetup();
        when(dealBiz.getDealListByDids(any(), any())).thenThrow(new Exception());
        when(recommendWrapper.getCollaborativeRecommend(eq(request), any())).thenReturn(collaborativeResp);
        recommendFacade.getCollaborativeDealGroup(request, context);
    }

    @Test(expected = Exception.class)
    public void testGetCollaborativeDealGroup_DealBizGetDealJsonByModelThrowsException() throws Throwable {
        commonSetup();
        when(dealBiz.getDealJsonByModel(any(), any(), any())).thenThrow(new Exception());
        when(recommendWrapper.getCollaborativeRecommend(eq(request), any())).thenReturn(collaborativeResp);
        recommendFacade.getCollaborativeDealGroup(request, context);
    }
}
