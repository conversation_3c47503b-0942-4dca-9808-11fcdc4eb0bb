package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.beauty.deal.bean.nail.BeautyNailBean;
import com.dianping.beauty.deal.bean.nail.BeautyNailTitleBean;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DztgShareModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.MtDztgShareModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.Pair;
import com.dianping.mobile.mapi.dztgdetail.helper.MtDealDetailBuilder;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class BeautyNailAdaptorTest {

    @InjectMocks
    private BeautyNailAdaptor beautyNailAdaptor;

    @Mock
    private DealCtx ctx;

    @Mock
    private FutureCtx futureCtx;

    @Mock
    private DztgShareModule shareModule;

    @Mock
    private MtDztgShareModule mtDztgShareModule;

    @Mock
    private Future<BeautyNailTitleBean> beautyNailFuture4Title;

    @Mock
    private Future<BeautyNailBean> beautyNailFuture4Detail;

    /**
     * Test case: Normal flow with all valid data
     */
    @Test
    public void testProcess_WithValidData() throws Exception {
        // arrange
        BeautyNailTitleBean titleBean = new BeautyNailTitleBean();
        titleBean.setShowable(true);
        titleBean.setTitle("Test Title");
        titleBean.setSubTitle("Test SubTitle");
        BeautyNailBean nailBean = new BeautyNailBean();
        nailBean.setShowable(true);
        nailBean.setDealDetail("Test Detail");
        List<Pair> structedDetails = new ArrayList<>();
        when(ctx.getDztgShareModule()).thenReturn(shareModule);
        when(shareModule.getMt()).thenReturn(mtDztgShareModule);
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
        when(futureCtx.getBeautyNailFuture4Title()).thenReturn(beautyNailFuture4Title);
        when(beautyNailFuture4Title.get()).thenReturn(titleBean);
        when(futureCtx.getBeautyNailFuture4Detail()).thenReturn(beautyNailFuture4Detail);
        when(beautyNailFuture4Detail.get()).thenReturn(nailBean);
        when(ctx.getStructedDetails()).thenReturn(structedDetails);
        when(ctx.isMt()).thenReturn(true);
        // act
        beautyNailAdaptor.process(ctx);
        // assert
        verify(mtDztgShareModule).setBrandName("Test Title");
        assertEquals(1, structedDetails.size());
        assertEquals("Test Detail", structedDetails.get(0).getName());
    }

    /**
     * Test case: Share modules are null
     */
    @Test
    public void testProcess_WithNullShareModules() throws Exception {
        // arrange
        when(ctx.getDztgShareModule()).thenReturn(null);
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
        // act
        beautyNailAdaptor.process(ctx);
        // assert
        verify(ctx, never()).getStructedDetails();
    }

    /**
     * Test case: Future call throws exception
     */
    @Test
    public void testProcess_WhenFutureThrowsException() throws Exception {
        // arrange
        when(ctx.getDztgShareModule()).thenReturn(shareModule);
        when(shareModule.getMt()).thenReturn(mtDztgShareModule);
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
        when(futureCtx.getBeautyNailFuture4Title()).thenReturn(beautyNailFuture4Title);
        when(beautyNailFuture4Title.get()).thenThrow(new RuntimeException("Test exception"));
        // act
        beautyNailAdaptor.process(ctx);
        // assert
        verify(mtDztgShareModule, never()).setBrandName(anyString());
    }

    /**
     * Test case: Beauty beans are not showable
     */
    @Test
    public void testProcess_WithNonShowableBeautyBeans() throws Exception {
        // arrange
        BeautyNailTitleBean titleBean = new BeautyNailTitleBean();
        titleBean.setShowable(false);
        titleBean.setTitle("Test Title");
        BeautyNailBean nailBean = new BeautyNailBean();
        nailBean.setShowable(false);
        nailBean.setDealDetail("Test Detail");
        when(ctx.getDztgShareModule()).thenReturn(shareModule);
        when(shareModule.getMt()).thenReturn(mtDztgShareModule);
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
        when(futureCtx.getBeautyNailFuture4Title()).thenReturn(beautyNailFuture4Title);
        when(beautyNailFuture4Title.get()).thenReturn(titleBean);
        when(futureCtx.getBeautyNailFuture4Detail()).thenReturn(beautyNailFuture4Detail);
        when(beautyNailFuture4Detail.get()).thenReturn(nailBean);
        // act
        beautyNailAdaptor.process(ctx);
        // assert
        verify(mtDztgShareModule, never()).setBrandName(anyString());
        verify(ctx, never()).getStructedDetails();
    }

    /**
     * Test case: Beauty beans with empty content
     */
    @Test
    public void testProcess_WithEmptyContent() throws Exception {
        // arrange
        BeautyNailTitleBean titleBean = new BeautyNailTitleBean();
        titleBean.setShowable(true);
        titleBean.setTitle("");
        BeautyNailBean nailBean = new BeautyNailBean();
        nailBean.setShowable(true);
        nailBean.setDealDetail("");
        List<Pair> structedDetails = new ArrayList<>();
        when(ctx.getDztgShareModule()).thenReturn(shareModule);
        when(shareModule.getMt()).thenReturn(mtDztgShareModule);
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
        when(futureCtx.getBeautyNailFuture4Title()).thenReturn(beautyNailFuture4Title);
        when(beautyNailFuture4Title.get()).thenReturn(titleBean);
        when(futureCtx.getBeautyNailFuture4Detail()).thenReturn(beautyNailFuture4Detail);
        when(beautyNailFuture4Detail.get()).thenReturn(nailBean);
        when(ctx.getStructedDetails()).thenReturn(structedDetails);
        // act
        beautyNailAdaptor.process(ctx);
        // assert
        verify(mtDztgShareModule, never()).setBrandName(anyString());
        assertEquals(0, structedDetails.size());
    }

    /**
     * 测试场景：当 isMt 为 true 时，返回 MtDealDetailBuilder.DETAIL_KEY
     */
    @Test
    public void testGetDetailKeyWhenIsMtTrue() throws Throwable {
        // arrange
        boolean isMt = true;
        // act
        Method method = BeautyNailAdaptor.class.getDeclaredMethod("getDetailKey", boolean.class);
        method.setAccessible(true);
        String result = (String) method.invoke(beautyNailAdaptor, isMt);
        // assert
        assertEquals(MtDealDetailBuilder.DETAIL_KEY, result);
    }

    /**
     * 测试场景：当 isMt 为 false 时，返回 "团购详情"
     */
    @Test
    public void testGetDetailKeyWhenIsMtFalse() throws Throwable {
        // arrange
        boolean isMt = false;
        // act
        Method method = BeautyNailAdaptor.class.getDeclaredMethod("getDetailKey", boolean.class);
        method.setAccessible(true);
        String result = (String) method.invoke(beautyNailAdaptor, isMt);
        // assert
        assertEquals("团购详情", result);
    }
}
