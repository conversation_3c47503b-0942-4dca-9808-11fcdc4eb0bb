package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;


import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.util.EduDealUtils;
import com.sankuai.clr.content.process.thrift.api.LeadsQueryService;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class EduShortClassOrderInfoProcessorTest {

    @InjectMocks
    private EduShortClassOrderInfoProcessor eduShortClassOrderInfoProcessor;

    @Mock
    private LeadsQueryService leadsQueryService;

    private MockedStatic<EduDealUtils> eduDealUtils;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private EnvCtx envCtx;

    @Mock
    private DealGroupDTO dealGroupDTO;


    @Before
    public void setUp() {
        eduDealUtils = Mockito.mockStatic(EduDealUtils.class);
    }


    @After
    public void tearDown() {
        eduDealUtils.close();
    }

    /**
     * 测试prepare方法，当preCheckShortClassHasButton返回false，则直接返回
     */
    @Test
    public void testPrepareWhenDealGroupDTOIsNull() throws Exception {
        eduDealUtils.when(() ->EduDealUtils.preCheckShortClassHasButton(dealCtx)).thenReturn(false);

        eduShortClassOrderInfoProcessor.prepare(dealCtx);

        verify(leadsQueryService, never()).batchQueryLeadsInfo(any());
    }

    /**
     * 测试prepare方法，当dealGroupDTO为null 方法直接返回
     */
    @Test
    public void testPrepareWhenAttrsIsEmpty() throws Exception {
        eduDealUtils.when(() ->EduDealUtils.preCheckShortClassHasButton(dealCtx)).thenReturn(true);
        when(dealCtx.getDealGroupDTO()).thenReturn(null);

        eduShortClassOrderInfoProcessor.prepare(dealCtx);

        verify(leadsQueryService, never()).batchQueryLeadsInfo(any());
    }


    /**
     * 测试prepare方法，当所有条件满足时，leadsQueryService.batchQueryLeadsInfo()调用一次
     */
    @Test
    public void testPrepareWhenAllConditionsAreMet() throws Exception {
        FutureCtx futureCtx = new FutureCtx();

        eduDealUtils.when(() ->EduDealUtils.preCheckShortClassHasButton(dealCtx)).thenReturn(true);
        when(dealCtx.getFutureCtx()).thenReturn(futureCtx);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        lenient().when(dealCtx.isMt()).thenReturn(true);
        lenient().when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        lenient().when(envCtx.getMtUserId()).thenReturn(123L);

        eduShortClassOrderInfoProcessor.prepare(dealCtx);

        assertNotNull(futureCtx.getQueryLeadsInfoRespDTOFuture());
    }
}
