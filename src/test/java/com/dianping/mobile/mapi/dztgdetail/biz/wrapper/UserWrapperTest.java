package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.tuangou.dztg.bjwrapper.api.UserWrapperService;
import com.dianping.tuangou.dztg.bjwrapper.api.dto.MtUserDto;
import com.dianping.tuangou.dztg.bjwrapper.api.enums.UserFieldsTypeEnum;
import com.dianping.userremote.base.dto.UserDTO;
import com.dianping.userremote.base.service.UserService;
import com.sankuai.wpt.user.retrieve.thrift.message.RpcUserBatchRetrieveService;
import com.sankuai.wpt.user.retrieve.thrift.message.UserFields;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class UserWrapperTest {

    @InjectMocks
    private UserWrapper userWrapper;

    @Mock
    private UserWrapperService userWrapperService;

    @Mock
    private UserService userBaseFuture;

    @Mock
    private RpcUserBatchRetrieveService.Iface rpcUserBatchRetrieveService;

    @Mock
    private Future future;

    // Constructor for initializing mocks without using @Before annotation
    public UserWrapperTest() {
        MockitoAnnotations.initMocks(this);
    }

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 userId 小于等于0 的情况
     */
    @Test
    public void testGetUserModelUserIdLessThanOrEqualToZero() {
        long userId = 0;
        MtUserDto result = userWrapper.getUserModel(userId);
        assertNull(result);
    }

    /**
     * 测试 userId 大于0，且 userWrapperService.getUserModel 方法正常返回的情况
     */
    @Test
    public void testGetUserModelUserIdGreaterThanZeroAndGetUserModelReturnNormally() throws Exception {
        long userId = 1;
        MtUserDto expected = new MtUserDto();
        when(userWrapperService.getUserModel(userId, UserFieldsTypeEnum.SAMPLE_INFO.getUserFieldsType())).thenReturn(expected);
        MtUserDto result = userWrapper.getUserModel(userId);
        assertSame(expected, result);
    }

    /**
     * 测试 userId 大于0，且 userWrapperService.getUserModel 方法抛出异常的情况
     */
    @Test
    public void testGetUserModelUserIdGreaterThanZeroAndGetUserModelThrowsException() throws Exception {
        long userId = 1;
        when(userWrapperService.getUserModel(userId, UserFieldsTypeEnum.SAMPLE_INFO.getUserFieldsType())).thenThrow(new RuntimeException());
        MtUserDto result = userWrapper.getUserModel(userId);
        assertNull(result);
    }

    /**
     * Tests getUserInfos method, when userBaseFuture.getUserMap method is called successfully, it should return a Future object.
     */
    @Test
    public void testGetUserInfosSuccess() throws Throwable {
        // arrange
        List<Long> userIdList = Arrays.asList(1L, 2L, 3L);
        // Mocking userBaseFuture.getUserMap to return a non-null value to simulate successful call
        when(userBaseFuture.getUserMap(userIdList)).thenReturn(mock(Map.class));
        // act
        Future result = userWrapper.getUserInfos(userIdList);
        // assert
        // Since we cannot mock FutureFactory.getFuture() directly, we'll assert the behavior indirectly.
        // For example, we can check if userBaseFuture.getUserMap was called, indicating the method proceeded to the point of calling getFuture().
        verify(userBaseFuture).getUserMap(userIdList);
        // Note: We cannot assert on the result being not null directly since the actual Future object creation is not mocked.
    }

    /**
     * Tests getUserInfos method, when userBaseFuture.getUserMap method call throws an exception, it should return null.
     */
    @Test
    public void testGetUserInfosException() throws Throwable {
        // arrange
        List<Long> userIdList = Arrays.asList(1L, 2L, 3L);
        when(userBaseFuture.getUserMap(userIdList)).thenThrow(new RuntimeException());
        // act
        Future result = userWrapper.getUserInfos(userIdList);
        // assert
        assertNull(result);
    }

    /**
     * 测试 mtUserIds 为空的情况
     */
    @Test
    public void testGetMtUserModelByMtIdsEmpty() throws Throwable {
        // arrange
        List<Long> mtUserIds = Arrays.asList();
        // act
        Map<Long, UserModel> result = userWrapper.getMtUserModelByMtIds(mtUserIds);
        // assert
        assertEquals(new HashMap<>(), result);
    }

    /**
     * 测试 mtUserIds 不为空，且 batchGetUserByIdsWithMap 方法正常返回的情况
     */
    @Test
    public void testGetMtUserModelByMtIdsNormal() throws Throwable {
        // arrange
        List<Long> mtUserIds = Arrays.asList(1L, 2L, 3L);
        Map<Long, UserModel> expected = new HashMap<>();
        expected.put(1L, new UserModel());
        expected.put(2L, new UserModel());
        expected.put(3L, new UserModel());
        when(rpcUserBatchRetrieveService.batchGetUserByIdsWithMap(eq(mtUserIds), any(UserFields.class))).thenReturn(expected);
        // act
        Map<Long, UserModel> result = userWrapper.getMtUserModelByMtIds(mtUserIds);
        // assert
        assertEquals(expected, result);
    }

    @Test
    public void testGetMtUserModelMapFutureIsNull() throws Throwable {
        // arrange
        Future future = null;
        // act
        Map<Long, MtUserDto> result = userWrapper.getMtUserModelMap(future);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetMtUserModelMapFutureIsNotNullAndGetFutureResultReturnsNormally() throws Throwable {
        // arrange
        Map<Long, MtUserDto> expected = new HashMap<>();
        when(future.get()).thenReturn(expected);
        // act
        Map<Long, MtUserDto> result = userWrapper.getMtUserModelMap(future);
        // assert
        assertEquals(expected, result);
    }

    @Test
    public void testGetMtUserModelMapFutureIsNotNullAndGetFutureResultThrowsException() throws Throwable {
        // arrange
        when(future.get()).thenThrow(new InterruptedException());
        // act
        Map<Long, MtUserDto> result = userWrapper.getMtUserModelMap(future);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetDpUserModelMapFutureIsNull() throws Throwable {
        // arrange
        Future future = null;
        // act
        Map<Long, UserDTO> result = userWrapper.getDpUserModelMap(future);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetDpUserModelMapFutureIsNotNullAndGetFutureResultReturnsNormally() throws Throwable {
        // arrange
        Map<Long, UserDTO> expected = new HashMap<>();
        when(future.get()).thenReturn(expected);
        // act
        Map<Long, UserDTO> result = userWrapper.getDpUserModelMap(future);
        // assert
        assertEquals(expected, result);
    }

    @Test
    public void testGetDpUserModelMapFutureIsNotNullAndGetFutureResultThrowsException() throws Throwable {
        // arrange
        when(future.get()).thenThrow(new InterruptedException());
        // act
        Map<Long, UserDTO> result = userWrapper.getDpUserModelMap(future);
        // assert
        assertNull(result);
    }
}
