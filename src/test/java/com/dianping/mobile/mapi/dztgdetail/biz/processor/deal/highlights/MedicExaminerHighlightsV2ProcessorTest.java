package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.DefaultExaminerHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.EntryExaminerHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.HealthCertificateExaminerHandler;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CommonAttrVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.highlights.DztgHighlightsModule;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.util.ArrayList;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEvent;

@RunWith(MockitoJUnitRunner.class)
public class MedicExaminerHighlightsV2ProcessorTest {

    @InjectMocks
    private MedicExaminerHighlightsV2Processor medicExaminerHighlightsV2Processor;

    @Mock
    private DealCtx ctx;

    @Mock
    private DealGroupDTO dealGroupDTO;

    @Mock
    private DealGroupCategoryDTO dealGroupCategoryDTO;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private DefaultExaminerHandler defaultExaminerHandler;

    @Mock
    private EntryExaminerHandler entryExaminerHandler;

    @Mock
    private HealthCertificateExaminerHandler healthCertificateExaminerHandler;

    @Mock
    private DztgHighlightsModule dztgHighlightsModule;

    /**
     * 测试buildExaminerHighlights方法，当ctx中包含DealGroupDTO对象，且DealGroupDTO对象中的服务类型在EXAMINER_SERVER_TYPE_V2_HANDLER_MAP中有对应的处理器时
     */
    @Test
    public void testBuildExaminerHighlights_Normal() throws Throwable {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(dealGroupCategoryDTO);
        when(dealGroupCategoryDTO.getServiceType()).thenReturn("入职体检");
        // act
        medicExaminerHighlightsV2Processor.buildExaminerHighlights(ctx);
        // assert
        assertNotNull(ctx.getDealGroupDTO());
        assertNotNull(dealGroupDTO.getCategory());
        assertEquals("入职体检", dealGroupCategoryDTO.getServiceType());
    }

    /**
     * 测试buildExaminerHighlights方法，当dealgroup为null时
     */
    @Test
    public void testBuildExaminerHighlights_NullCtx() throws Throwable {
        // arrange
        DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
        dealGroupCategoryDTO.setCategoryId(1603L);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setCategory(dealGroupCategoryDTO);
        ctx.setDealGroupDTO(dealGroupDTO);
        // act
        medicExaminerHighlightsV2Processor.buildExaminerHighlights(ctx);
        // assert
        assertNull(ctx.getHighlightsModule());
    }

    /**
     * 测试buildExaminerHighlights方法，当ctx中不包含DealGroupDTO对象时
     */
    @Test
    public void testBuildExaminerHighlights_NoDealGroupDTO() throws Throwable {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(null);
        // act
        medicExaminerHighlightsV2Processor.buildExaminerHighlights(ctx);
        // assert
        assertNull(ctx.getDealGroupDTO());
    }

    /**
     * 测试buildExaminerHighlights方法，当DealGroupDTO对象中的服务类型在EXAMINER_SERVER_TYPE_V2_HANDLER_MAP中没有对应的处理器时
     */
    @Test
    public void testBuildExaminerHighlights_NoHandler() throws Throwable {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(dealGroupCategoryDTO);
        when(dealGroupCategoryDTO.getServiceType()).thenReturn("不存在的服务类型");
        // act
        medicExaminerHighlightsV2Processor.buildExaminerHighlights(ctx);
        // assert
        assertNotNull(ctx.getDealGroupDTO());
        assertNotNull(dealGroupDTO.getCategory());
        assertEquals("不存在的服务类型", dealGroupCategoryDTO.getServiceType());
    }

    /**
     * 测试buildExaminerHighlights方法，当DealGroupDTO对象中的服务类型为null或者空字符串时
     */
    @Test
    public void testBuildExaminerHighlights_NullOrEmptyServiceType() throws Throwable {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(dealGroupCategoryDTO);
        when(dealGroupCategoryDTO.getServiceType()).thenReturn(null);
        // act
        medicExaminerHighlightsV2Processor.buildExaminerHighlights(ctx);
        // assert
        assertNotNull(ctx.getDealGroupDTO());
        assertNotNull(dealGroupDTO.getCategory());
        assertNull(dealGroupCategoryDTO.getServiceType());
    }

    @Test
    public void testOnApplicationEvent() {
        // arrange
        ApplicationEvent applicationEvent = mock(ApplicationEvent.class);
        when(applicationContext.getBean(DefaultExaminerHandler.class)).thenReturn(defaultExaminerHandler);
        when(applicationContext.getBean(EntryExaminerHandler.class)).thenReturn(entryExaminerHandler);
        when(applicationContext.getBean(HealthCertificateExaminerHandler.class)).thenReturn(healthCertificateExaminerHandler);
        // act
        medicExaminerHighlightsV2Processor.onApplicationEvent(applicationEvent);
        // assert
        assertEquals(defaultExaminerHandler, MedicExaminerHighlightsV2Processor.EXAMINER_SERVER_TYPE_V2_HANDLER_MAP.get("默认"));
        assertEquals(entryExaminerHandler, MedicExaminerHighlightsV2Processor.EXAMINER_SERVER_TYPE_V2_HANDLER_MAP.get("入职体检"));
        assertEquals(healthCertificateExaminerHandler, MedicExaminerHighlightsV2Processor.EXAMINER_SERVER_TYPE_V2_HANDLER_MAP.get("健康证检查"));
    }

    @Test
    public void testGetHighlightsContent_HighlightsModuleIsNull() {
        // arrange
        when(ctx.getHighlightsModule()).thenReturn(null);
        // act
        String result = medicExaminerHighlightsV2Processor.getHighlightsContent(ctx);
        // assert
        assertEquals(null, result);
    }

    /**
     * 测试DealCtx对象中的DztgHighlightsModule对象不为null，且其style属性值不为空的情况
     */
    @Test
    public void testGetHighlightsStyle_NotNullHighlightsModule() {
        // arrange
        DztgHighlightsModule highlightsModule = new DztgHighlightsModule();
        highlightsModule.setStyle("custom_style");
        when(ctx.getHighlightsModule()).thenReturn(highlightsModule);
        // act
        String result = medicExaminerHighlightsV2Processor.getHighlightsStyle(ctx);
        // assert
        assertEquals("custom_style", result);
    }

    @Test
    public void testGetHighlightsIdentify_BoundaryCase_EmptyIdentify() {
        // arrange
        when(ctx.getHighlightsModule()).thenReturn(dztgHighlightsModule);
        when(dztgHighlightsModule.getIdentify()).thenReturn("");
        // act
        String result = medicExaminerHighlightsV2Processor.getHighlightsIdentify(ctx);
        // assert
        assertEquals("", result);
    }

    /**
     * Test getHighlightsStyle when highlightsModule is null
     * Should return null
     */
    @Test
    public void testGetHighlightsStyle_WhenHighlightsModuleIsNull() {
        // arrange
        when(ctx.getHighlightsModule()).thenReturn(null);
        // act
        String result = medicExaminerHighlightsV2Processor.getHighlightsStyle(ctx);
        // assert
        assertNull(result);
    }

    /**
     * Test getHighlightsStyle when highlightsModule is not null
     * Should return style from highlightsModule
     */
    @Test
    public void testGetHighlightsStyle_WhenHighlightsModuleNotNull() {
        // arrange
        DztgHighlightsModule highlightsModule = new DztgHighlightsModule();
        String expectedStyle = "test_style";
        highlightsModule.setStyle(expectedStyle);
        when(ctx.getHighlightsModule()).thenReturn(highlightsModule);
        // act
        String result = medicExaminerHighlightsV2Processor.getHighlightsStyle(ctx);
        // assert
        assertEquals(expectedStyle, result);
    }

    /**
     * Test getHighlightsStyle when highlightsModule exists but style is null
     * Should return null
     */
    @Test
    public void testGetHighlightsStyle_WhenHighlightsModuleExistsButStyleIsNull() {
        // arrange
        DztgHighlightsModule highlightsModule = new DztgHighlightsModule();
        highlightsModule.setStyle(null);
        when(ctx.getHighlightsModule()).thenReturn(highlightsModule);
        // act
        String result = medicExaminerHighlightsV2Processor.getHighlightsStyle(ctx);
        // assert
        assertNull(result);
    }

    /**
     * Test getHighlightsStyle when highlightsModule exists with empty style
     * Should return empty string
     */
    @Test
    public void testGetHighlightsStyle_WhenHighlightsModuleExistsWithEmptyStyle() {
        // arrange
        DztgHighlightsModule highlightsModule = new DztgHighlightsModule();
        highlightsModule.setStyle("");
        when(ctx.getHighlightsModule()).thenReturn(highlightsModule);
        // act
        String result = medicExaminerHighlightsV2Processor.getHighlightsStyle(ctx);
        // assert
        assertEquals("", result);
    }

    /**
     * Test getHighlightsIdentify when highlightsModule is null
     */
    @Test
    public void testGetHighlightsIdentifyWhenHighlightsModuleIsNull() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        // act
        String result = medicExaminerHighlightsV2Processor.getHighlightsIdentify(ctx);
        // assert
        assertNull(result);
    }

    /**
     * Test when highlightsModule is null
     */
    @Test
    public void testGetHighlightsAttrs_WhenHighlightsModuleIsNull() throws Throwable {
        // arrange
        when(ctx.getHighlightsModule()).thenReturn(null);
        // act
        List<CommonAttrVO> result = medicExaminerHighlightsV2Processor.getHighlightsAttrs(ctx);
        // assert
        assertNull(result);
    }

    /**
     * Test when highlightsModule exists but attrs is null
     */
    @Test
    public void testGetHighlightsAttrs_WhenHighlightsModuleExistsButAttrsIsNull() throws Throwable {
        // arrange
        when(ctx.getHighlightsModule()).thenReturn(dztgHighlightsModule);
        when(dztgHighlightsModule.getAttrs()).thenReturn(null);
        // act
        List<CommonAttrVO> result = medicExaminerHighlightsV2Processor.getHighlightsAttrs(ctx);
        // assert
        assertNull(result);
    }

    /**
     * Test when highlightsModule exists and attrs contains values
     */
    @Test
    public void testGetHighlightsAttrs_WhenHighlightsModuleAndAttrsExist() throws Throwable {
        // arrange
        List<CommonAttrVO> expectedAttrs = new ArrayList<>();
        expectedAttrs.add(new CommonAttrVO("test", "value"));
        when(ctx.getHighlightsModule()).thenReturn(dztgHighlightsModule);
        when(dztgHighlightsModule.getAttrs()).thenReturn(expectedAttrs);
        // act
        List<CommonAttrVO> result = medicExaminerHighlightsV2Processor.getHighlightsAttrs(ctx);
        // assert
        assertEquals(expectedAttrs, result);
    }

    /**
     * Test getHighlightsContent when highlightsModule has content
     */
    @Test
    public void testGetHighlightsContent_WhenHighlightsModuleHasContent() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        DztgHighlightsModule highlightsModule = new DztgHighlightsModule();
        String expectedContent = "test content";
        highlightsModule.setContent(expectedContent);
        ctx.setHighlightsModule(highlightsModule);
        // act
        String result = medicExaminerHighlightsV2Processor.getHighlightsContent(ctx);
        // assert
        assertEquals(expectedContent, result);
    }

    /**
     * Test getHighlightsContent when highlightsModule exists but content is null
     */
    @Test
    public void testGetHighlightsContent_WhenHighlightsModuleContentIsNull() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        DztgHighlightsModule highlightsModule = new DztgHighlightsModule();
        highlightsModule.setContent(null);
        ctx.setHighlightsModule(highlightsModule);
        // act
        String result = medicExaminerHighlightsV2Processor.getHighlightsContent(ctx);
        // assert
        assertNull(result);
    }

    /**
     * Test getHighlightsContent when highlightsModule exists but content is empty
     */
    @Test
    public void testGetHighlightsContent_WhenHighlightsModuleContentIsEmpty() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        DztgHighlightsModule highlightsModule = new DztgHighlightsModule();
        highlightsModule.setContent("");
        ctx.setHighlightsModule(highlightsModule);
        // act
        String result = medicExaminerHighlightsV2Processor.getHighlightsContent(ctx);
        // assert
        assertEquals("", result);
    }

    /**
     * Test getHighlightsContent when highlightsModule content contains special characters
     */
    @Test
    public void testGetHighlightsContent_WhenContentHasSpecialCharacters() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        DztgHighlightsModule highlightsModule = new DztgHighlightsModule();
        String expectedContent = "!@#$%^&*()_+{}|:\"<>?~`-=[]\\;',./";
        highlightsModule.setContent(expectedContent);
        ctx.setHighlightsModule(highlightsModule);
        // act
        String result = medicExaminerHighlightsV2Processor.getHighlightsContent(ctx);
        // assert
        assertEquals(expectedContent, result);
    }

    /**
     * Test getHighlightsContent when highlightsModule content contains Unicode characters
     */
    @Test
    public void testGetHighlightsContent_WhenContentHasUnicodeCharacters() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        DztgHighlightsModule highlightsModule = new DztgHighlightsModule();
        String expectedContent = "测试内容 - テスト - 테스트 - 🌟";
        highlightsModule.setContent(expectedContent);
        ctx.setHighlightsModule(highlightsModule);
        // act
        String result = medicExaminerHighlightsV2Processor.getHighlightsContent(ctx);
        // assert
        assertEquals(expectedContent, result);
    }
}
