package com.dianping.mobile.mapi.dztgdetail.biz.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.service.bo.DrivingPoi;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedShop;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.utils.FaultToleranceUtils;
import com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper;
import com.dianping.mobile.mapi.dztgdetail.util.PoiShopUtil;
import com.dianping.poi.relation.service.api.PoiRelationService;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.service.DpPoiService;
import com.sankuai.sinai.data.api.service.MtPoiService;
import com.sankuai.zdc.apply.api.DrivingPoiQueryService;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DrivingPoiServiceTest {

    @InjectMocks
    private DrivingPoiService drivingPoiService;

    @Mock
    private PoiRelationService poiRelationService;

    @Mock
    private DrivingPoiQueryService drivingPoiQueryService;

    private DrivingPoi req;

    @Mock
    private MtPoiService sinaiMtPoiService;

    private Map<Long, List<Long>> dp2MtShopMap;

    private List<Long> mtShopIds;

    private Map<Long, MtPoiDTO> poiDTOMap;

    @Mock
    private DpPoiService sinaiDpPoiService;

    @Before
    public void setUp() throws Exception {
        req = DrivingPoi.builder().shopId(1L).dpDrivingPoiIds(Arrays.asList(1L, 2L)).userLng(120.0).userLat(30.0).build();
        dp2MtShopMap = new HashMap<>();
        dp2MtShopMap.put(1L, Arrays.asList(1L, 2L));
        dp2MtShopMap.put(2L, Arrays.asList(2L, 3L));
        mtShopIds = Arrays.asList(1L, 2L, 3L);
        poiDTOMap = new HashMap<>();
        MtPoiDTO mtPoiDTO = new MtPoiDTO();
        mtPoiDTO.setMtPoiId(1L);
        mtPoiDTO.setLongitude(120.0);
        mtPoiDTO.setLatitude(30.0);
        poiDTOMap.put(1L, mtPoiDTO);
        MtPoiDTO mtPoiDTO2 = new MtPoiDTO();
        mtPoiDTO2.setMtPoiId(2L);
        mtPoiDTO2.setLongitude(121.0);
        mtPoiDTO2.setLatitude(31.0);
        poiDTOMap.put(2L, mtPoiDTO2);
        MtPoiDTO mtPoiDTO3 = new MtPoiDTO();
        mtPoiDTO3.setMtPoiId(3L);
        mtPoiDTO3.setLongitude(122.0);
        mtPoiDTO3.setLatitude(32.0);
        poiDTOMap.put(3L, mtPoiDTO3);
    }

    private List<String> getMT_POI_FIELDS() throws Exception {
        Field field = DrivingPoiService.class.getDeclaredField("MT_POI_FIELDS");
        field.setAccessible(true);
        return (List<String>) field.get(null);
    }

    private void invokePrivateValidateMethod(DrivingPoi req) throws Exception {
        Method validateMethod = DrivingPoiService.class.getDeclaredMethod("validate", DrivingPoi.class);
        validateMethod.setAccessible(true);
        validateMethod.invoke(drivingPoiService, req);
    }

    // Hypothetical method to create DrivingPoi instance for testing
    private DrivingPoi createDrivingPoi() {
        // Assuming a builder pattern is available for DrivingPoi
        return DrivingPoi.builder().shopId(1L).dpDrivingPoiIds(new ArrayList<>()).build();
    }

    private DpPoiDTO createDpPoiDTO(Long shopId) {
        DpPoiDTO dto = new DpPoiDTO();
        dto.setShopId(shopId);
        dto.setShopName("Test Shop");
        dto.setAddress("Test Address");
        dto.setLng(116.397128);
        dto.setLat(39.916527);
        dto.setUuid("test-uuid");
        dto.setDefaultPic("https://test.com/image.jpg");
        return dto;
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetDrivingShopsReqIsNull() throws Throwable {
        drivingPoiService.getDrivingShops(null);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetDrivingShopsShopIdIsZero() throws Throwable {
        req = DrivingPoi.builder().shopId(0).build();
        drivingPoiService.getDrivingShops(req);
    }

    @Test
    public void testGetDrivingShopsNotEnableDrivingShop() throws Throwable {
        req = DrivingPoi.builder().shopId(1L).publishCategory(403).build();
        List<RelatedShop> result = drivingPoiService.getDrivingShops(req);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetDrivingShopsQueryDpByMtIdLThrowException() throws Throwable {
        req = DrivingPoi.builder().shopId(1L).publishCategory(404).serviceTypeAttr(new ArrayList<>()).build();
        req.getServiceTypeAttr().add("小车");
        List<RelatedShop> result = drivingPoiService.getDrivingShops(req);
        assertEquals(1, result.size());
    }

    @Test
    public void testGetDrivingShopsDpPoiIdIsZero() throws Throwable {
        req = DrivingPoi.builder().shopId(1L).publishCategory(404).serviceTypeAttr(new ArrayList<>()).build();
        req.getServiceTypeAttr().add("小车");
        List<RelatedShop> result = drivingPoiService.getDrivingShops(req);
        assertEquals(1, result.size());
    }

    @Test
    public void testGetDrivingShopsNormal() throws Throwable {
        req = DrivingPoi.builder().shopId(1L).publishCategory(404).serviceTypeAttr(new ArrayList<>()).build();
        req.getServiceTypeAttr().add("小车");
        Map<Long, List<Long>> dpPoiId2DpDrivingPoiIds = new HashMap<>();
        dpPoiId2DpDrivingPoiIds.put(1L, new ArrayList<>());
        when(drivingPoiQueryService.queryShop2DrivingFieldMap(anyList())).thenReturn(dpPoiId2DpDrivingPoiIds);
        List<RelatedShop> result = drivingPoiService.getDrivingShops(req);
        assertEquals(1, result.size());
    }

    /**
     * Tests the normal scenario where the method under test is expected to return a list of RelatedShops.
     */
    @Test
    public void testGetMtRelatedShopsNormal() throws Throwable {
        when(poiRelationService.queryMtByDpIdsL(req.getDpDrivingPoiIds())).thenReturn(dp2MtShopMap);
        when(sinaiMtPoiService.findPoisById(anyList(), eq(getMT_POI_FIELDS()))).thenReturn(poiDTOMap);
        Method method = DrivingPoiService.class.getDeclaredMethod("getMtRelatedShops", DrivingPoi.class);
        method.setAccessible(true);
        List<RelatedShop> relatedShops = (List<RelatedShop>) method.invoke(drivingPoiService, req);
        assertEquals(2, relatedShops.size());
        verify(poiRelationService, times(1)).queryMtByDpIdsL(req.getDpDrivingPoiIds());
        verify(sinaiMtPoiService, times(1)).findPoisById(anyList(), eq(getMT_POI_FIELDS()));
    }

    /**
     * Tests the scenario where an exception is expected to be thrown by the method under test.
     */
    @Test
    public void testGetMtRelatedShopsException() throws Throwable {
        when(poiRelationService.queryMtByDpIdsL(req.getDpDrivingPoiIds())).thenThrow(new Exception());
        Method method = DrivingPoiService.class.getDeclaredMethod("getMtRelatedShops", DrivingPoi.class);
        method.setAccessible(true);
        List<RelatedShop> relatedShops = (List<RelatedShop>) method.invoke(drivingPoiService, req);
        assertTrue(relatedShops.isEmpty());
        verify(poiRelationService, times(1)).queryMtByDpIdsL(req.getDpDrivingPoiIds());
    }

    /**
     * Tests the boundary scenario where the method under test is expected to return an empty list of RelatedShops.
     */
    @Test
    public void testGetMtRelatedShopsBoundary() throws Throwable {
        // Ensure dp2MtShopMap is not empty to proceed to call sinaiMtPoiService.findPoisById
        when(poiRelationService.queryMtByDpIdsL(req.getDpDrivingPoiIds())).thenReturn(dp2MtShopMap);
        when(sinaiMtPoiService.findPoisById(anyList(), eq(getMT_POI_FIELDS()))).thenReturn(new HashMap<>());
        Method method = DrivingPoiService.class.getDeclaredMethod("getMtRelatedShops", DrivingPoi.class);
        method.setAccessible(true);
        List<RelatedShop> relatedShops = (List<RelatedShop>) method.invoke(drivingPoiService, req);
        assertEquals(0, relatedShops.size());
        verify(poiRelationService, times(1)).queryMtByDpIdsL(req.getDpDrivingPoiIds());
        verify(sinaiMtPoiService, times(1)).findPoisById(anyList(), eq(getMT_POI_FIELDS()));
    }

    /**
     * 测试 validate 方法在 req 不为 null 且 req.getShopId() > 0 时正常执行
     */
    @Test
    public void testValidateReqIsValid() throws Throwable {
        // arrange
        DrivingPoi req = DrivingPoi.builder().shopId(1).build();
        // act
        invokePrivateValidateMethod(req);
        // assert
        assertEquals(1, req.getShopId());
    }

    /**
     * 测试 validate 方法在 req 为 null 时抛出 IllegalArgumentException
     */
    @Test
    public void testValidateReqIsNull() throws Throwable {
        // arrange
        DrivingPoi req = null;
        try {
            // act
            invokePrivateValidateMethod(req);
            fail("Expected an IllegalArgumentException to be thrown");
        } catch (InvocationTargetException e) {
            // assert
            assertTrue(e.getCause() instanceof IllegalArgumentException);
            assertEquals("入参为空", e.getCause().getMessage());
        }
    }

    /**
     * 测试 validate 方法在 req.getShopId() <= 0 时抛出 IllegalArgumentException
     */
    @Test
    public void testValidateShopIdLessThanOrEqualToZero() throws Throwable {
        // arrange
        DrivingPoi req = DrivingPoi.builder().shopId(0).build();
        try {
            // act
            invokePrivateValidateMethod(req);
            fail("Expected an IllegalArgumentException to be thrown");
        } catch (InvocationTargetException e) {
            // assert
            assertTrue(e.getCause() instanceof IllegalArgumentException);
            assertEquals("门店id <= 0", e.getCause().getMessage());
        }
    }

    /**
     * 测试 dpDrivingPoiIds 为空的情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGetDrivingShopsWithDpDrivingPoiIdsEmpty() throws Throwable {
        DrivingPoi req = DrivingPoi.builder().dpDrivingPoiIds(Collections.emptyList()).build();
        drivingPoiService.getDrivingShopsWithDpDrivingPoiIds(req);
    }

    /**
     * 测试 req 为 mt 类型的情况
     */
    @Test
    public void testGetDrivingShopsWithDpDrivingPoiIdsMt() throws Throwable {
        DrivingPoi req = DrivingPoi.builder().dpDrivingPoiIds(Collections.singletonList(1L)).isMt(true).build();
        List<RelatedShop> result = new ArrayList<>();
        drivingPoiService.getDrivingShopsWithDpDrivingPoiIds(req);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 req 不为 mt 类型的情况
     */
    @Test
    public void testGetDrivingShopsWithDpDrivingPoiIdsNotMt() throws Throwable {
        // Corrected line
        DrivingPoi req = DrivingPoi.builder().dpDrivingPoiIds(new ArrayList<>(Collections.singletonList(1L))).isMt(false).build();
        List<RelatedShop> result = new ArrayList<>();
        drivingPoiService.getDrivingShopsWithDpDrivingPoiIds(req);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetDpRelatedShopsWhenDrivingDpShopInfosIsEmpty() throws Throwable {
        DrivingPoi req = createDrivingPoi();
        req.setDpDrivingPoiIds(new ArrayList<>());
        when(sinaiDpPoiService.findShopsByShopIds(any(DpPoiRequest.class))).thenReturn(new ArrayList<>());
        Method method = DrivingPoiService.class.getDeclaredMethod("getDpRelatedShops", DrivingPoi.class);
        method.setAccessible(true);
        List<RelatedShop> relatedShops = (List<RelatedShop>) method.invoke(drivingPoiService, req);
        assertEquals(0, relatedShops.size());
    }

    @Test
    public void testGetDpRelatedShopsWhenDrivingDpShopInfosIsNotEmpty() throws Throwable {
        DrivingPoi req = createDrivingPoi();
        req.setDpDrivingPoiIds(new ArrayList<>(Arrays.asList(1L, 2L, 3L)));
        List<DpPoiDTO> drivingDpShopInfos = Arrays.asList(createDpPoiDTO(1L), createDpPoiDTO(2L), createDpPoiDTO(3L));
        when(sinaiDpPoiService.findShopsByShopIds(any(DpPoiRequest.class))).thenReturn(drivingDpShopInfos);
        Method method = DrivingPoiService.class.getDeclaredMethod("getDpRelatedShops", DrivingPoi.class);
        method.setAccessible(true);
        List<RelatedShop> relatedShops = (List<RelatedShop>) method.invoke(drivingPoiService, req);
        // Expecting 2 related shops since one shopId matches req.getShopId()
        assertEquals(2, relatedShops.size());
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetDpRelatedShopsWhenReqIsNull() throws Throwable {
        Method method = DrivingPoiService.class.getDeclaredMethod("getDpRelatedShops", DrivingPoi.class);
        method.setAccessible(true);
        method.invoke(drivingPoiService, new Object());
    }

    @Test
    public void testGetDpRelatedShopsWhenDrivingDpShopInfosIsEmptyAndReqIsNotNull() throws Throwable {
        DrivingPoi req = createDrivingPoi();
        req.setDpDrivingPoiIds(new ArrayList<>());
        when(sinaiDpPoiService.findShopsByShopIds(any(DpPoiRequest.class))).thenReturn(new ArrayList<>());
        Method method = DrivingPoiService.class.getDeclaredMethod("getDpRelatedShops", DrivingPoi.class);
        method.setAccessible(true);
        List<RelatedShop> relatedShops = (List<RelatedShop>) method.invoke(drivingPoiService, req);
        assertEquals(0, relatedShops.size());
    }

    @Test
    public void testGetDpRelatedShopsWhenDrivingDpShopInfosIsNotEmptyAndReqIsNotNull() throws Throwable {
        DrivingPoi req = createDrivingPoi();
        req.setDpDrivingPoiIds(new ArrayList<>(Arrays.asList(1L, 2L, 3L)));
        List<DpPoiDTO> drivingDpShopInfos = Arrays.asList(createDpPoiDTO(1L), createDpPoiDTO(2L), createDpPoiDTO(3L));
        when(sinaiDpPoiService.findShopsByShopIds(any(DpPoiRequest.class))).thenReturn(drivingDpShopInfos);
        Method method = DrivingPoiService.class.getDeclaredMethod("getDpRelatedShops", DrivingPoi.class);
        method.setAccessible(true);
        List<RelatedShop> relatedShops = (List<RelatedShop>) method.invoke(drivingPoiService, req);
        // Expecting 2 related shops since one shopId matches req.getShopId()
        assertEquals(2, relatedShops.size());
    }

    @Test
    public void testGetDpRelatedShopsWhenDrivingDpShopInfosIsNotEmptyAndReqIsNotNullAndDrivingDpShopInfosIsNotEmpty() throws Throwable {
        DrivingPoi req = createDrivingPoi();
        req.setDpDrivingPoiIds(new ArrayList<>(Arrays.asList(1L, 2L, 3L)));
        List<DpPoiDTO> drivingDpShopInfos = Arrays.asList(createDpPoiDTO(1L), createDpPoiDTO(2L), createDpPoiDTO(3L));
        when(sinaiDpPoiService.findShopsByShopIds(any(DpPoiRequest.class))).thenReturn(drivingDpShopInfos);
        Method method = DrivingPoiService.class.getDeclaredMethod("getDpRelatedShops", DrivingPoi.class);
        method.setAccessible(true);
        List<RelatedShop> relatedShops = (List<RelatedShop>) method.invoke(drivingPoiService, req);
        // Expecting 2 related shops since one shopId matches req.getShopId()
        assertEquals(2, relatedShops.size());
    }
}
