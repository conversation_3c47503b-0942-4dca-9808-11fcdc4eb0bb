package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.style.dto.StyleResponse;
import com.dianping.mobile.mapi.dztgdetail.util.SwitchUtils;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.dianping.tuangou.dztg.bjwrapper.api.PrometheusWrapperService;
import com.dianping.tuangou.dztg.bjwrapper.api.enums.BizTypeEnum;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ParallDealStyleWrapperTest {

    @InjectMocks
    private ParallDealStyleWrapper parallDealStyleWrapper;

    @Mock
    private PrometheusWrapperService prometheusWrapperServiceFuture;

    @Mock
    private Future mockFuture;

    @Test
    public void testPreMtDealDtoListWhenSwitchIsTrueOrDealIdsIsEmpty() throws Throwable {
        try (MockedStatic<SwitchUtils> mockedStatic = mockStatic(SwitchUtils.class)) {
            mockedStatic.when(SwitchUtils::isListDealByIdsDegrade).thenReturn(true);
            assertNull(parallDealStyleWrapper.preMtDealDtoList(Arrays.asList(1, 2, 3)));
            mockedStatic.when(SwitchUtils::isListDealByIdsDegrade).thenReturn(false);
            assertNull(parallDealStyleWrapper.preMtDealDtoList(null));
        }
    }

    @Test
    public void testPreMtDealDtoListWhenSwitchIsFalseAndDealIdsIsNotEmpty() throws Throwable {
        List<Integer> dealIds = Arrays.asList(1, 2, 3);
        try (MockedStatic<SwitchUtils> mockedStatic = mockStatic(SwitchUtils.class);
            MockedStatic<FutureFactory> mockedFutureFactory = mockStatic(FutureFactory.class)) {
            mockedStatic.when(SwitchUtils::isListDealByIdsDegrade).thenReturn(false);
            mockedFutureFactory.when(FutureFactory::getFuture).thenReturn(mockFuture);
            Future future = parallDealStyleWrapper.preMtDealDtoList(dealIds);
            assertNotNull(future);
            verify(prometheusWrapperServiceFuture).listDealByIds(dealIds, BizTypeEnum.MT_DEAL_DETAIL.getBizType());
        }
    }

    @Test
    public void testPreMtDealDtoListWhenSwitchIsFalseAndDealIdsIsNotEmptyButExceptionOccurs() throws Throwable {
        List<Integer> dealIds = Arrays.asList(1, 2, 3);
        try (MockedStatic<SwitchUtils> mockedStatic = mockStatic(SwitchUtils.class)) {
            mockedStatic.when(SwitchUtils::isListDealByIdsDegrade).thenReturn(false);
            doThrow(new RuntimeException()).when(prometheusWrapperServiceFuture).listDealByIds(dealIds, BizTypeEnum.MT_DEAL_DETAIL.getBizType());
            assertNull(parallDealStyleWrapper.preMtDealDtoList(dealIds));
        }
    }

    /**
     * Tests the getStyle method under normal conditions.
     */
    @Test
    public void testGetStyleNormal() throws Throwable {
        // Arrange
        ParallDealStyleWrapper wrapper = new ParallDealStyleWrapper();
        StyleResponse styleResponse = new StyleResponse();
        when(mockFuture.get()).thenReturn(styleResponse);
        // Act
        StyleResponse result = wrapper.getStyle(mockFuture);
        // Assert
        assertEquals(styleResponse, result);
    }
}
