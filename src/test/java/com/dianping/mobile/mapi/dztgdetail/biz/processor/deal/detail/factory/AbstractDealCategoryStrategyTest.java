package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryAbInfo;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryDealModuleInfo;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryParam;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleExtraDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

public class AbstractDealCategoryStrategyTest {

    /**
     * 正常流程，isMt()返回true，应该设置mtModuleKey
     */
    @Test
    public void testResetDealDetailModuleConfig_isMtTrue() throws Throwable {
        // arrange
        AbstractDealCategoryStrategy strategy = Mockito.spy(new TestDealCategoryStrategy());
        // Create mock DealCategoryParam
        ModuleConfigDo configDo = new ModuleConfigDo();
        configDo.setKey("团购详情");
        configDo.setValue("oldValue");
        List<ModuleConfigDo> configDos = new ArrayList<>();
        configDos.add(configDo);
        ModuleExtraDTO extraDTO = mock(ModuleExtraDTO.class);
        when(extraDTO.getModuleConfigDos()).thenReturn(configDos);
        EnvCtx envCtx = mock(EnvCtx.class);
        when(envCtx.isMt()).thenReturn(true);
        DealCategoryParam param = mock(DealCategoryParam.class);
        when(param.getModuleExtraDTO()).thenReturn(extraDTO);
        when(param.getEnvCtx()).thenReturn(envCtx);
        doReturn(true).when(strategy).newDealStyle(param);
        DealCategoryDealModuleInfo moduleInfo = mock(DealCategoryDealModuleInfo.class);
        when(moduleInfo.getMtModuleKey()).thenReturn("mtKey");
        when(moduleInfo.getDpModuleKey()).thenReturn("dpKey");
        doReturn(moduleInfo).when(strategy).buildDealModuleInfo();
        // act
        strategy.resetDealDetailModuleConfig(param);
        // assert
        assertEquals("mtKey", configDo.getValue());
        verify(strategy).buildDealModuleInfo();
    }

    /**
     * 正常流程，isMt()返回false，应该设置dpModuleKey
     */
    @Test
    public void testResetDealDetailModuleConfig_isMtFalse() throws Throwable {
        // arrange
        AbstractDealCategoryStrategy strategy = Mockito.spy(new TestDealCategoryStrategy());
        // Create mock DealCategoryParam
        ModuleConfigDo configDo = new ModuleConfigDo();
        configDo.setKey("团购详情");
        configDo.setValue("oldValue");
        List<ModuleConfigDo> configDos = new ArrayList<>();
        configDos.add(configDo);
        ModuleExtraDTO extraDTO = mock(ModuleExtraDTO.class);
        when(extraDTO.getModuleConfigDos()).thenReturn(configDos);
        EnvCtx envCtx = mock(EnvCtx.class);
        when(envCtx.isMt()).thenReturn(false);
        DealCategoryParam param = mock(DealCategoryParam.class);
        when(param.getModuleExtraDTO()).thenReturn(extraDTO);
        when(param.getEnvCtx()).thenReturn(envCtx);
        doReturn(true).when(strategy).newDealStyle(param);
        DealCategoryDealModuleInfo moduleInfo = mock(DealCategoryDealModuleInfo.class);
        when(moduleInfo.getMtModuleKey()).thenReturn("mtKey");
        when(moduleInfo.getDpModuleKey()).thenReturn("dpKey");
        doReturn(moduleInfo).when(strategy).buildDealModuleInfo();
        // act
        strategy.resetDealDetailModuleConfig(param);
        // assert
        assertEquals("dpKey", configDo.getValue());
        verify(strategy).buildDealModuleInfo();
    }

    /**
     * 多个ModuleConfigDo，只有key为团购详情的被更新
     */
    @Test
    public void testResetDealDetailModuleConfig_multipleConfigsOnlyOneUpdated() throws Throwable {
        // arrange
        AbstractDealCategoryStrategy strategy = Mockito.spy(new TestDealCategoryStrategy());
        // Create mock DealCategoryParam
        ModuleConfigDo configDo1 = new ModuleConfigDo();
        configDo1.setKey("团购详情");
        configDo1.setValue("oldValue1");
        ModuleConfigDo configDo2 = new ModuleConfigDo();
        configDo2.setKey("其他模块");
        configDo2.setValue("oldValue2");
        List<ModuleConfigDo> configDos = Arrays.asList(configDo1, configDo2);
        ModuleExtraDTO extraDTO = mock(ModuleExtraDTO.class);
        when(extraDTO.getModuleConfigDos()).thenReturn(configDos);
        EnvCtx envCtx = mock(EnvCtx.class);
        when(envCtx.isMt()).thenReturn(true);
        DealCategoryParam param = mock(DealCategoryParam.class);
        when(param.getModuleExtraDTO()).thenReturn(extraDTO);
        when(param.getEnvCtx()).thenReturn(envCtx);
        doReturn(true).when(strategy).newDealStyle(param);
        DealCategoryDealModuleInfo moduleInfo = mock(DealCategoryDealModuleInfo.class);
        when(moduleInfo.getMtModuleKey()).thenReturn("mtKey");
        when(moduleInfo.getDpModuleKey()).thenReturn("dpKey");
        doReturn(moduleInfo).when(strategy).buildDealModuleInfo();
        // act
        strategy.resetDealDetailModuleConfig(param);
        // assert
        assertEquals("mtKey", configDo1.getValue());
        assertEquals("oldValue2", configDo2.getValue());
        verify(strategy).buildDealModuleInfo();
    }

    // Helper concrete class for testing
    private static class TestDealCategoryStrategy extends AbstractDealCategoryStrategy {

        @Override
        protected DealCategoryAbInfo buildAbInfo() {
            return null;
        }

        @Override
        protected DealCategoryDealModuleInfo buildDealModuleInfo() {
            return null;
        }

        @Override
        protected boolean customNewDealStyle(DealCategoryParam dealCategoryParam) {
            return false;
        }

        @Override
        public ModuleAbConfig getModuleAbConfig(EnvCtx envCtx) {
            return null;
        }

        @Override
        public ModuleAbConfig getSimilarDealModuleAbConfig(EnvCtx envCtx) {
            return null;
        }

        @Override
        public ModuleAbConfig getTimesDealModuleAbConfig(EnvCtx envCtx) {
            return null;
        }
    }
}
