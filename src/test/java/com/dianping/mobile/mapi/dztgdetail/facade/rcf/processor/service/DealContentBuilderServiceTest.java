package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import static org.junit.Assert.*;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.SpritePicVO;
import com.sankuai.general.product.query.center.client.dto.video.DealGroupVideoDTO;
import com.sankuai.general.product.query.center.client.dto.video.ExtendVideoDTO;
import com.sankuai.general.product.query.center.client.dto.video.SpriteImageDTO;
import java.util.Arrays;
import java.util.Collections;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

public class DealContentBuilderServiceTest {

    private DealContentBuilderService dealContentBuilderService = new DealContentBuilderService();

    @Test
    public void testBuildSpritePicByExtendVideoDtoWhenExtendVideoDTOIsNull() throws Throwable {
        ContentPBO video = new ContentPBO(1, "content");
        dealContentBuilderService.buildSpritePicByExtendVideoDto(video, null);
        assertNull(video.getSpritePic());
    }

    @Test
    public void testBuildSpritePicByExtendVideoDtoWhenSpriteImageListIsEmpty() throws Throwable {
        ContentPBO video = new ContentPBO(1, "content");
        ExtendVideoDTO extendVideoDTO = new ExtendVideoDTO();
        dealContentBuilderService.buildSpritePicByExtendVideoDto(video, extendVideoDTO);
        assertNull(video.getSpritePic());
    }

    @Test
    public void testBuildSpritePicByExtendVideoDtoWhenSpriteImageDTOIsNull() throws Throwable {
        ContentPBO video = new ContentPBO(1, "content");
        ExtendVideoDTO extendVideoDTO = new ExtendVideoDTO();
        // Fix: Use Collections.singletonList(null) to avoid NullPointerException
        extendVideoDTO.setSpriteImageList(Collections.singletonList(null));
        dealContentBuilderService.buildSpritePicByExtendVideoDto(video, extendVideoDTO);
        assertNull(video.getSpritePic());
    }

    @Test
    public void testBuildSpritePicByExtendVideoDtoWhenAllParametersAreNotNull() throws Throwable {
        ContentPBO video = new ContentPBO(1, "content");
        ExtendVideoDTO extendVideoDTO = new ExtendVideoDTO();
        SpriteImageDTO spriteImageDTO = new SpriteImageDTO();
        spriteImageDTO.setPath("path");
        spriteImageDTO.setSubImageCount(1);
        spriteImageDTO.setWidth(100);
        spriteImageDTO.setHeight(100);
        extendVideoDTO.setSpriteImageList(Collections.singletonList(spriteImageDTO));
        dealContentBuilderService.buildSpritePicByExtendVideoDto(video, extendVideoDTO);
        assertNotNull(video.getSpritePic());
        assertEquals("path", video.getSpritePic().getSpritePicUrl());
        assertEquals(1, video.getSpritePic().getTotalCount());
        assertEquals(100, video.getSpritePic().getSpriteCellSize().getWidth());
        assertEquals(100, video.getSpritePic().getSpriteCellSize().getHeight());
        assertEquals(1000, video.getSpritePic().getAllSpriteImageSize().getWidth());
        assertEquals(1000, video.getSpritePic().getAllSpriteImageSize().getHeight());
    }

    @Test
    @Ignore
    public void testAssembleOriginVideoSpritePic_NullInput() throws Throwable {
        dealContentBuilderService.assembleOriginVideoSpritePic(null, null);
    }

    @Test
    @Ignore
    public void testAssembleOriginVideoSpritePic_EmptyExtendVideos() throws Throwable {
        DealGroupVideoDTO dealGroupVideoDTO = new DealGroupVideoDTO();
        ContentPBO video = new ContentPBO(1, "content");
        dealContentBuilderService.assembleOriginVideoSpritePic(dealGroupVideoDTO, video);
    }

    @Test
    @Ignore
    public void testAssembleOriginVideoSpritePic_NoSpriteImageList() throws Throwable {
        DealGroupVideoDTO dealGroupVideoDTO = new DealGroupVideoDTO();
        ExtendVideoDTO extendVideoDTO = new ExtendVideoDTO();
        dealGroupVideoDTO.setExtendVideos(Arrays.asList(extendVideoDTO));
        ContentPBO video = new ContentPBO(1, "content");
        dealContentBuilderService.assembleOriginVideoSpritePic(dealGroupVideoDTO, video);
    }

    @Test
    public void testAssembleOriginVideoSpritePic_WithSpriteImageList() throws Throwable {
        DealGroupVideoDTO dealGroupVideoDTO = new DealGroupVideoDTO();
        ExtendVideoDTO extendVideoDTO = new ExtendVideoDTO();
        SpriteImageDTO spriteImageDTO = new SpriteImageDTO();
        // Initialize necessary fields to avoid NullPointerException
        spriteImageDTO.setSubImageCount(10);
        // Assuming 100 as a valid width for the test
        spriteImageDTO.setWidth(100);
        // Assuming 100 as a valid height for the test
        spriteImageDTO.setHeight(100);
        // Assuming 10 as a valid sub-image width for the test
        spriteImageDTO.setSubImageWidth(10);
        // Assuming 10 as a valid sub-image height for the test
        spriteImageDTO.setSubImageHeight(10);
        // Assuming a valid path for the test
        spriteImageDTO.setPath("valid/path/to/sprite/image");
        extendVideoDTO.setSpriteImageList(Arrays.asList(spriteImageDTO));
        dealGroupVideoDTO.setExtendVideos(Arrays.asList(extendVideoDTO));
        ContentPBO video = new ContentPBO(1, "content");
        dealContentBuilderService.assembleOriginVideoSpritePic(dealGroupVideoDTO, video);
        assertNotNull(video.getSpritePic());
    }



}
