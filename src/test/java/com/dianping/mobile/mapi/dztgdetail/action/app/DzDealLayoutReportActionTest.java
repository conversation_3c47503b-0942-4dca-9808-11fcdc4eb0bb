package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.deal.style.DealPageLayoutService;
import com.dianping.deal.style.dto.laout.DealPageLayoutQueryResponse;
import com.dianping.deal.style.dto.laout.DealPageLayoutUpdateResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealLayoutReportReq;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

/**
 * DzDealLayoutReportAction的validate方法测试类
 */
public class DzDealLayoutReportActionTest {

    @Mock
    private IMobileContext context;

    private DzDealLayoutReportAction dzDealLayoutReportAction;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Mock
    private DealPageLayoutService dealPageLayoutService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        dzDealLayoutReportAction = new DzDealLayoutReportAction();
    }

    /**
     * 测试validate方法，当request为null时
     */
    @Test
    public void testValidateRequestIsNull() {
        // arrange
        DealLayoutReportReq request = null;
        // act
        IMobileResponse response = dzDealLayoutReportAction.validate(request, context);
        // assert
        assertEquals("验证request为null时返回参数错误", Resps.PARAM_ERROR, response);
    }

    /**
     * 测试validate方法，当dealgroupid为null时
     */
    @Test
    public void testValidateDealGroupIdIsNull() {
        // arrange
        DealLayoutReportReq request = new DealLayoutReportReq();
        request.setDealgroupid(null);
        // act
        IMobileResponse response = dzDealLayoutReportAction.validate(request, context);
        // assert
        assertEquals("验证dealgroupid为null时返回参数错误", Resps.PARAM_ERROR, response);
    }

    /**
     * 测试validate方法，当dealgroupid小于等于0时
     */
    @Test
    public void testValidateDealGroupIdIsLessThanOrEqualToZero() {
        // arrange
        DealLayoutReportReq request = new DealLayoutReportReq();
        request.setDealgroupid(0L);
        // act
        IMobileResponse response = dzDealLayoutReportAction.validate(request, context);
        // assert
        assertEquals("验证dealgroupid小于等于0时返回参数错误", Resps.PARAM_ERROR, response);
    }

    /**
     * 测试validate方法，当dealgroupid大于0时
     */
    @Test
    public void testValidateDealGroupIdIsGreaterThanZero() {
        // arrange
        DealLayoutReportReq request = new DealLayoutReportReq();
        request.setDealgroupid(1L);
        // act
        IMobileResponse response = dzDealLayoutReportAction.validate(request, context);
        // assert
        assertEquals("验证dealgroupid大于0时应返回null", null, response);
    }

    /**
     * 测试用户ID小于等于0的情况
     */
    @Test
    public void testExecuteWithUserIdLessThanOrEqualToZero() throws Throwable {
        // arrange
        DealLayoutReportReq request = new DealLayoutReportReq();
        IMobileContext iMobileContext = mock(IMobileContext.class);
        when(iMobileContext.getUserId()).thenReturn(0L);
        // act
        IMobileResponse response = dzDealLayoutReportAction.execute(request, iMobileContext);
        // assert
        assertEquals("应返回用户错误响应", Resps.USER_ERROR, response);
    }

    /**
     * 测试参数非法的情况
     */
    @Test
    public void testExecuteWithIllegalParam() throws Throwable {
        // arrange
        DealLayoutReportReq request = new DealLayoutReportReq();
        IMobileContext iMobileContext = mock(IMobileContext.class);
        when(iMobileContext.getUserId()).thenReturn(1L);
        // 由于buildContextRequest是private方法，我们可以通过修改输入数据或者使用反射来模拟这种情况
        IMobileResponse response = dzDealLayoutReportAction.execute(request, iMobileContext);
        // assert
        assertEquals("应返回参数错误响应", Resps.PARAM_ERROR.getStatusCode(), response.getStatusCode());
    }

    /**
     * 测试查询团单信息失败的情况
     */
    @Test
    public void testExecuteQueryDealInfoFail() throws Throwable {
        // arrange
        DealLayoutReportReq request = new DealLayoutReportReq();
        IMobileContext iMobileContext = mock(IMobileContext.class);
        when(iMobileContext.getUserId()).thenReturn(1L);
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenThrow(new TException());
        // act
        IMobileResponse response = dzDealLayoutReportAction.execute(request, iMobileContext);
        // assert
        assertEquals("应返回系统错误响应", Resps.SYSTEM_ERROR, response);
    }

    /**
     * 测试更新缓存失败的情况
     */
    @Test
    public void testExecuteUpdateCacheFail() throws Throwable {
        // arrange
        DealLayoutReportReq request = new DealLayoutReportReq();
        IMobileContext iMobileContext = mock(IMobileContext.class);
        when(iMobileContext.getUserId()).thenReturn(1L);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(dealGroupDTO);
        DealPageLayoutQueryResponse queryResponse = new DealPageLayoutQueryResponse();
        queryResponse.setSuccess(true);
        DealPageLayoutUpdateResponse updateResponse = new DealPageLayoutUpdateResponse();
        updateResponse.setSuccess(false);
        when(dealPageLayoutService.update(any())).thenReturn(updateResponse);
        // act
        IMobileResponse response = dzDealLayoutReportAction.execute(request, iMobileContext);
        // assert
        assertEquals("应返回系统错误响应", Resps.SYSTEM_ERROR, response);
    }
}
