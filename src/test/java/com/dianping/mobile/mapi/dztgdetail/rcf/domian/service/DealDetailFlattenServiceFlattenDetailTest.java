package com.dianping.mobile.mapi.dztgdetail.rcf.domian.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler.flatten.DealModuleFlattenProcessor;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler.flatten.FlattenProcessorFactory;
import com.dianping.mobile.mapi.dztgdetail.rcf.enums.ModuleType;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealDetailFlattenServiceFlattenDetailTest {

    @InjectMocks
    private DealDetailFlattenService dealDetailFlattenService;

    @Mock
    private FlattenProcessorFactory flattenProcessorFactory;

    /**
     * Test when moduleList is null
     */
    @Test
    public void testFlattenDetail_WhenModuleListNull() {
        // arrange
        JSONObject dealModuleResult = new JSONObject();
        dealModuleResult.put("moduleList", null);
        // act
        dealDetailFlattenService.flattenDetail(dealModuleResult);
        // assert
        verify(flattenProcessorFactory, never()).getFlattenHandler(any());
    }

    /**
     * Test when moduleList is empty
     */
    @Test
    public void testFlattenDetail_WhenModuleListEmpty() {
        // arrange
        JSONObject dealModuleResult = new JSONObject();
        dealModuleResult.put("moduleList", new JSONArray());
        // act
        dealDetailFlattenService.flattenDetail(dealModuleResult);
        // assert
        verify(flattenProcessorFactory, never()).getFlattenHandler(any());
    }

    /**
     * Test normal flow with flatten processor
     */
    @Test
    public void testFlattenDetail_WithFlattenProcessor() {
        // arrange
        JSONObject dealModuleResult = new JSONObject();
        JSONArray moduleList = new JSONArray();
        JSONObject module = new JSONObject();
        module.put("type", ModuleType.attr_type1.name());
        moduleList.add(module);
        dealModuleResult.put("moduleList", moduleList);
        DealModuleFlattenProcessor mockProcessor = mock(DealModuleFlattenProcessor.class);
        when(flattenProcessorFactory.getFlattenHandler(any(ModuleType.class))).thenReturn(mockProcessor);
        // act
        dealDetailFlattenService.flattenDetail(dealModuleResult);
        // assert
        assertTrue(module.containsKey("rcfSkuGroupsModule1Flatten"));
        JSONArray flattenArray = module.getJSONArray("rcfSkuGroupsModule1Flatten");
        assertNotNull(flattenArray);
        verify(mockProcessor).flattenModule(eq(flattenArray), eq(module));
    }
}
