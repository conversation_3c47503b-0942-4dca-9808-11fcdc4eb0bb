package com.dianping.mobile.mapi.dztgdetail.biz.processor;

import com.dianping.lion.common.util.JsonUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealActivityPreProcessor;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.entity.UrlProcessorDztgClient;
import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2024/4/1
 */
@RunWith(MockitoJUnitRunner.class)
public class DealActivityPreProcessorTest {
    @InjectMocks
    private DealActivityPreProcessor dealActivityPreProcessor;

    @Test
    public void testMatchDztgClient() {
        String processMappingConfig = "[\n" +
                "    {\n" +
                "        \"clientSwitch\": 1,\n" +
                "        \"url\": \"dzdealbase.bin\",\n" +
                "        \"processorConfigList\": [\n" +
                "            {\n" +
                "                \"processorName\": \"DealActivityPreProcessor\",\n" +
                "                \"clientList\": [\n" +
                "                    1,\n" +
                "                    2,\n" +
                "                    4,\n" +
                "                    5\n" +
                "                ]\n" +
                "            },\n" +
                "            {\n" +
                "                \"processorName\": \"CardStyleProcessorTest\",\n" +
                "                \"clientList\": [\n" +
                "                    1,\n" +
                "                    2,\n" +
                "                    4,\n" +
                "                    5\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "]";

        List<UrlProcessorDztgClient> urlProcessorDztgClients = JsonUtils.fromJson(processMappingConfig, new TypeReference<List<UrlProcessorDztgClient>>() {});
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setUrlProcessorDztgClientList(urlProcessorDztgClients);
        ctx.getEnvCtx().setRequestURI("/dzdealbase.bin");
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        Processor processor = new DealActivityPreProcessor();
        boolean match = dealActivityPreProcessor.matchDztgClient(ctx, processor);
        Assert.assertTrue(match);
    }

    @Test
    public void testMatchDztgClient_SwitchClose() {
        String processMappingConfig = "[\n" +
                "    {\n" +
                "        \"clientSwitch\": 0,\n" +
                "        \"url\": \"dzdealbase.bin\",\n" +
                "        \"processorConfigList\": [\n" +
                "            {\n" +
                "                \"processorName\": \"DealActivityPreProcessor\",\n" +
                "                \"clientList\": [\n" +
                "                    1,\n" +
                "                    2,\n" +
                "                    4,\n" +
                "                    5\n" +
                "                ]\n" +
                "            },\n" +
                "            {\n" +
                "                \"processorName\": \"CardStyleProcessorTest\",\n" +
                "                \"clientList\": [\n" +
                "                    1,\n" +
                "                    2,\n" +
                "                    4,\n" +
                "                    5\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "]";

        List<UrlProcessorDztgClient> urlProcessorDztgClients = JsonUtils.fromJson(processMappingConfig, new TypeReference<List<UrlProcessorDztgClient>>() {});
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setUrlProcessorDztgClientList(urlProcessorDztgClients);
        ctx.getEnvCtx().setRequestURI("/dzdealbase.bin");
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        Processor processor = new DealActivityPreProcessor();
        boolean match = dealActivityPreProcessor.matchDztgClient(ctx, processor);
        Assert.assertTrue(match);
    }

    @Test
    public void testMatchDztgClient_ConfigNul() {
        String processMappingConfig = "[\n" +
                "    {\n" +
                "        \"clientSwitch\": 1,\n" +
                "        \"url\": \"dzdealbase.bin\",\n" +
                "        \"processorConfigList\": [\n" +
                "            {\n" +
                "                \"processorName\": \"CardStyleProcessorTest\",\n" +
                "                \"clientList\": [\n" +
                "                    1,\n" +
                "                    2,\n" +
                "                    4,\n" +
                "                    5\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "]";

        List<UrlProcessorDztgClient> urlProcessorDztgClients = JsonUtils.fromJson(processMappingConfig, new TypeReference<List<UrlProcessorDztgClient>>() {});
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setUrlProcessorDztgClientList(urlProcessorDztgClients);
        ctx.getEnvCtx().setRequestURI("/dzdealbase.bin");
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        Processor processor = new DealActivityPreProcessor();
        boolean match = dealActivityPreProcessor.matchDztgClient(ctx, processor);
        Assert.assertTrue(match);
    }
}
