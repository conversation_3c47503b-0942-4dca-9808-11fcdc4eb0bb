//package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;
//
//import com.dianping.mobile.mapi.dztgdetail.GenericTest;
//import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.feature.UniversalInsuranceProcess;
//import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
//import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
//import com.dianping.mobile.mapi.dztgdetail.datatype.context.UnifiedCtx;
//import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetDealTinyInfoRequest;
//import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealTinyInfoVO;
//import com.dianping.mobile.mapi.dztgdetail.facade.DealTinyInfoFacade;
//import com.google.common.collect.Lists;
//import com.google.common.collect.Maps;
//import com.sankuai.dztheme.deal.req.DealProductRequest;
//import com.sankuai.meituan.common.json.JSONUtil;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.util.Assert;
//
//import javax.annotation.Resource;
//import java.util.List;
//import java.util.Map;
//import java.util.concurrent.Future;
//
//public class GroupDealWrapperTest extends GenericTest {
//
//    @Autowired
//    private GroupDealWrapper groupDealWrapper;
//
//    @Resource
//    private UniversalInsuranceProcess universalInsuranceProcess;
//
//    @Resource
//    private DealTinyInfoFacade dealTinyInfoFacade;
//
//    @Test
//    public void test1(){
//        List<Long> longs = groupDealWrapper.listPoiIdByDidByCityId(0, 401970858, 20);
//        System.out.println(JSONUtil.toJSONString(longs));
//        Assert.notNull(longs);
//    }
//
//    @Test
//    public void test2() throws Exception {
//        UnifiedCtx unifiedCtx = new UnifiedCtx(new EnvCtx());
//        unifiedCtx.setMtShopId(23005380);
//        unifiedCtx.setDpShopId(23005380);
//        unifiedCtx.setDpShopIdLong(23005380L);
//        unifiedCtx.setMtShopIdLong(23005380L);
//        System.out.println(unifiedCtx.getDpShopIdLong()+unifiedCtx.getMtShopIdLong());
//        EnvCtx envCtx = new EnvCtx();
//        envCtx.setRealMtUserId(1);
//        envCtx.setDpUserId(1);
//        envCtx.setAppDeviceId("");
//        DealCtx ctx = new DealCtx(envCtx);
//        ctx.isMt();
//        ctx.setMtId(403287260);
//        ctx.setDpId(403287260);
//        ctx.setMtLongShopId(152393402);
//        ctx.setDpLongShopId(23005380);
//        ctx.setRealMtCityId(10);
//        ctx.setDpCityId(10);
//        universalInsuranceProcess.prepare(ctx);
//        Object o = ctx.getFutureCtx().getDealThemeFuture().get();
//        System.out.println(JSONUtil.toJSONString(o));
//        Assert.notNull(o);
//    }
//
//    @Test
//    public void test3(){
//        EnvCtx envCtx = new EnvCtx();
//        envCtx.setRealMtUserId(859830518L);
//        envCtx.setDpUserId(859830518L);
//        envCtx.setAppDeviceId("");
//        GetDealTinyInfoRequest getDealTinyInfoRequest = new GetDealTinyInfoRequest();
//        getDealTinyInfoRequest.setDealGroupId("403287260");
//        getDealTinyInfoRequest.setShopId("23005380");
//        getDealTinyInfoRequest.setPageSource("caixi");
//        getDealTinyInfoRequest.setCityId(10);
//        getDealTinyInfoRequest.setLat(-78.44295);
//        getDealTinyInfoRequest.setLng(47.60355);
//        DealTinyInfoVO dealTinyInfo = dealTinyInfoFacade.getDealTinyInfo(getDealTinyInfoRequest, envCtx);
//        System.out.println(JSONUtil.toJSONString(dealTinyInfo));
//        Assert.notNull(dealTinyInfo);
//    }
//
//}
