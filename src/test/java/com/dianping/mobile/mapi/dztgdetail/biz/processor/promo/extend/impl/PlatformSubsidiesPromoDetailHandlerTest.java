package com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.extend.impl;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.PromoDetailEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ServiceGuarantee.DealBestPromoDetailDTO;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dealuser.price.display.api.model.PromoIdentity;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PlatformSubsidiesPromoDetailHandlerTest {

    private PlatformSubsidiesPromoDetailHandler handler = new PlatformSubsidiesPromoDetailHandler();

    @Test
    public void testGetDealBestPromoDetailWhenUsedPromosIsEmpty() throws Throwable {
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        priceDisplayDTO.setUsedPromos(Collections.emptyList());
        DealBestPromoDetailDTO result = handler.getDealBestPromoDetail(priceDisplayDTO);
        assertNull(result);
    }

    @Test
    public void testGetDealBestPromoDetailWhenAllPromosIdentityOrAmountIsNull() throws Throwable {
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(null);
        promoDTO.setAmount(null);
        priceDisplayDTO.setUsedPromos(Arrays.asList(promoDTO));
        DealBestPromoDetailDTO result = handler.getDealBestPromoDetail(priceDisplayDTO);
        assertNull(result);
    }

    @Test
    public void testGetDealBestPromoDetailWhenIdentitySourceTypeIsTwo() throws Throwable {
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(2);
        promoDTO.setIdentity(identity);
        // Ensure the amount is zero to align with the method's logic for returning null
        promoDTO.setAmount(BigDecimal.ZERO);
        priceDisplayDTO.setUsedPromos(Arrays.asList(promoDTO));
        DealBestPromoDetailDTO result = handler.getDealBestPromoDetail(priceDisplayDTO);
        assertNull(result);
    }

    @Test
    public void testGetDealBestPromoDetailWhenIdentitySourceTypeIsNotTwo() throws Throwable {
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(1);
        promoDTO.setIdentity(identity);
        promoDTO.setAmount(BigDecimal.TEN);
        priceDisplayDTO.setUsedPromos(Arrays.asList(promoDTO));
        DealBestPromoDetailDTO result = handler.getDealBestPromoDetail(priceDisplayDTO);
        assertNotNull(result);
        assertEquals("10", result.getPromoAmount().toString());
    }
}
