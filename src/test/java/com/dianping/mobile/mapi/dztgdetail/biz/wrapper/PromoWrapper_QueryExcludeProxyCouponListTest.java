package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.spc.common.Response;
import com.dianping.tgc.open.entity.BatchExProxyCouponResponseDTO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.concurrent.Future;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class PromoWrapper_QueryExcludeProxyCouponListTest {

    @Mock
    private Future future;

    @Mock
    private Response<BatchExProxyCouponResponseDTO> response;

    private PromoWrapper promoWrapper;

    @Before
    public void setUp() {
        promoWrapper = new PromoWrapper();
    }

    /**
     * 测试 future 的结果为 null 的情况
     */
    @Test
    public void testQueryExcludeProxyCouponListFutureResultIsNull() throws Throwable {
        // arrange
        when(future.get()).thenReturn(null);
        // act
        BatchExProxyCouponResponseDTO result = promoWrapper.queryExcludeProxyCouponList(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 future 的结果不为 null，但 isSuccess() 返回 false 的情况
     */
    @Test
    public void testQueryExcludeProxyCouponListFutureResultIsNotSuccess() throws Throwable {
        // arrange
        when(future.get()).thenReturn(response);
        when(response.isSuccess()).thenReturn(false);
        // act
        BatchExProxyCouponResponseDTO result = promoWrapper.queryExcludeProxyCouponList(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 future 的结果不为 null，isSuccess() 返回 true 的情况
     */
    @Test
    public void testQueryExcludeProxyCouponListFutureResultIsSuccess() throws Throwable {
        // arrange
        BatchExProxyCouponResponseDTO expected = new BatchExProxyCouponResponseDTO();
        when(future.get()).thenReturn(response);
        when(response.isSuccess()).thenReturn(true);
        when(response.getResult()).thenReturn(expected);
        // act
        BatchExProxyCouponResponseDTO result = promoWrapper.queryExcludeProxyCouponList(future);
        // assert
        assertEquals(expected, result);
    }
}
