package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.concurrent.Future;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class DealDetailWrapper_GetHasStructedDetailTest {

    @Mock
    private Future future;

    private DealDetailWrapper dealDetailWrapper;

    @Before
    public void setUp() {
        dealDetailWrapper = new DealDetailWrapper();
    }

    /**
     * 测试 Future 对象的结果为 null 的情况
     */
    @Test
    public void testGetHasStructedDetailWhenFutureResultIsNull() throws Throwable {
        // arrange
        when(future.get()).thenReturn(null);
        // act
        boolean result = dealDetailWrapper.getHasStructedDetail(future);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 Future 对象的结果为 true 的情况
     */
    @Test
    public void testGetHasStructedDetailWhenFutureResultIsTrue() throws Throwable {
        // arrange
        when(future.get()).thenReturn(true);
        // act
        boolean result = dealDetailWrapper.getHasStructedDetail(future);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 Future 对象的结果为 false 的情况
     */
    @Test
    public void testGetHasStructedDetailWhenFutureResultIsFalse() throws Throwable {
        // arrange
        when(future.get()).thenReturn(false);
        // act
        boolean result = dealDetailWrapper.getHasStructedDetail(future);
        // assert
        assertFalse(result);
    }
}
