package com.dianping.mobile.mapi.dztgdetail.util;

import org.junit.Assert;
import org.junit.Test;
import org.junit.platform.commons.util.StringUtils;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * @Author: zheng<PERSON><EMAIL>
 * @Date: 2024/6/25
 */
@RunWith(MockitoJUnitRunner.class)
public class ShareIdUtilsTest {

    @Test
    public void testGetShareId() {
        String result = ShareIdUtils.getShareId();
        Assert.assertTrue(StringUtils.isNotBlank(result));
    }
}
