package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RankingLabel;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.mdp.dzrank.scenes.api.RankLabelQuery;
import com.sankuai.mdp.dzrank.scenes.api.request.RankLabelRequest;
import com.sankuai.mdp.dzrank.scenes.api.response.dto.RankingResult;
import com.sankuai.mdp.dzshoplist.rank.api.enums.ClientTypeEnum;
import com.sankuai.mdp.dzshoplist.rank.api.enums.Platform;
import com.sankuai.mdp.dzshoplist.rank.api.response.Response;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class RankWrapperGetRankTest {

    @InjectMocks
    private RankWrapper rankWrapper;

    @Mock
    private Future<Response> future;

    @Mock
    private Response<RankingResult> response;

    @Mock
    private RankingResult rankingResult;

    @Mock
    private RankLabelQuery rankLabelQuery;

    @Mock
    private EnvCtx envCtx;

    @Mock
    private DealGroupDTO dealGroupDTO;

    @Mock
    private List<Long> poiIds;

    @Mock
    private DealGroupCategoryDTO dealGroupCategoryDTO;

    @Before
    public void setUp() {
        // 初始化模拟对象的行为
        // Mock Future object using ThreadLocal
        Future<?> mockFuture = Mockito.mock(Future.class);
        FutureFactory.setFuture(mockFuture);
    }

    @Test
    public void testGetRankResultIsNull() throws Throwable {
        when(response.getResult()).thenReturn(null);
        when(future.get()).thenReturn(response);
        Map<Long, RankingLabel> result = rankWrapper.getRank(future);
        assertEquals(new HashMap<>(), result);
    }

    @Test
    public void testGetRankFirstRankMapIsEmpty() throws Throwable {
        when(rankingResult.getFirstRankMap()).thenReturn(new HashMap<>());
        when(response.getResult()).thenReturn(rankingResult);
        when(future.get()).thenReturn(response);
        Map<Long, RankingLabel> result = rankWrapper.getRank(future);
        assertEquals(new HashMap<>(), result);
    }

    /**
     * 测试当 rankLabelQuery.queryFirstRank 抛出异常时，invokeRank 方法的行为
     */
    @Test(expected = RuntimeException.class)
    public void testInvokeRankWhenQueryFirstRankThrowsException() throws Throwable {
        // arrange
        when(LionConfigUtils.getRankSceneByCategory(anyLong())).thenReturn("valid_scene");
        when(rankLabelQuery.queryFirstRank(any(RankLabelRequest.class))).thenThrow(new RuntimeException("Query failed"));
        // act
        rankWrapper.invokeRank(envCtx, dealGroupDTO, poiIds);
        // assert
        // 期望抛出 RuntimeException
    }
}
