package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.sankuai.general.product.query.center.client.dto.rule.use.DateRangeDTO;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealAttrHelper_HolidayAvailableTest {

    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    /**
     * 测试 holidayAvailable 方法，当 attrs 为空时，应返回 false
     */
    @Test
    public void testHolidayAvailableAttrsIsNull() throws Throwable {
        assertFalse(DealAttrHelper.holidayAvailable(null));
    }

    /**
     * 测试 holidayAvailable 方法，当 attrs 不为空，但 DealAttrKeys.HOLIDAY_AVAILABLE 对应的值为空时，应返回 false
     */
    @Test
    public void testHolidayAvailableValueIsNull() throws Throwable {
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName(DealAttrKeys.HOLIDAY_AVAILABLE);
        assertFalse(DealAttrHelper.holidayAvailable(Collections.singletonList(attributeDTO)));
    }

    /**
     * 测试 holidayAvailable 方法，当 attrs 不为空，DealAttrKeys.HOLIDAY_AVAILABLE 对应的值不为空，但不等于 "1" 时，应返回 false
     */
    @Test
    public void testHolidayAvailableValueIsNotOne() throws Throwable {
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName(DealAttrKeys.HOLIDAY_AVAILABLE);
        attributeDTO.setValue(Arrays.asList("0"));
        assertFalse(DealAttrHelper.holidayAvailable(Collections.singletonList(attributeDTO)));
    }

    /**
     * 测试 holidayAvailable 方法，当 attrs 不为空，DealAttrKeys.HOLIDAY_AVAILABLE 对应的值等于 "1" 时，应返回 true
     */
    @Test
    public void testHolidayAvailableValueIsOne() throws Throwable {
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName(DealAttrKeys.HOLIDAY_AVAILABLE);
        attributeDTO.setValue(Arrays.asList("1"));
        assertTrue(DealAttrHelper.holidayAvailable(Collections.singletonList(attributeDTO)));
    }

    @Test
    public void testAvailableDateRangeWhenListIsEmpty() throws Throwable {
        boolean result = DealAttrHelper.validAvailableDateRange(Collections.emptyList());
        assertFalse(result);
    }

    @Test
    public void testValidAvailableDateRangeWhenAllDateRangesNotContainsCurrentDate() throws Throwable {
        DateRangeDTO dateRangeDTO = new DateRangeDTO();
        dateRangeDTO.setFrom("2023-10-01 00:00:00");
        // 结束时间在当前日期之前
        dateRangeDTO.setTo("2023-10-14 23:59:59");
        boolean result = DealAttrHelper.validAvailableDateRange(Arrays.asList(dateRangeDTO));
        assertFalse(result);
    }

    @Test
    public void testValidAvailableDateRangeWhenListIsEmpty() throws Throwable {
        boolean result = DealAttrHelper.validAvailableDateRange(Collections.emptyList());
        assertFalse(result);
    }
}
