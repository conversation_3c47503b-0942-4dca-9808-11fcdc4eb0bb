package com.dianping.mobile.mapi.dztgdetail.helper;

import com.google.common.collect.Sets;
import org.junit.Test;
import java.math.BigDecimal;
import java.util.Set;

import static org.junit.Assert.assertEquals;

public class PriceHelperTest {

    /**
     * 测试 format 方法，当 price 为整数时
     */
    @Test
    public void testFormatWhenPriceIsInteger() {
        // arrange
        BigDecimal price = new BigDecimal(10);
        // act
        String result = PriceHelper.format(price);
        // assert
        assertEquals("10", result);
    }

    /**
     * 测试 format 方法，当 price 为非整数时
     */
    @Test
    public void testFormatWhenPriceIsNotInteger() {
        // arrange
        BigDecimal price = new BigDecimal(10.5);
        // act
        String result = PriceHelper.format(price);
        // assert
        assertEquals("10.5", result);
    }

    /**
     * 测试 format 方法，当 price 为 null 时
     */
    @Test(expected = NullPointerException.class)
    public void testFormatWhenPriceIsNull() {
        // arrange
        BigDecimal price = null;
        // act
        PriceHelper.format(price);
        // assert is in the annotation
    }

    /**
     * 测试 hidePriceWithQuestionMark 方法，当 price 为空时
     */
    @Test
    public void testHidePriceWithQuestionMarkWhenPriceIsEmpty() {
        // arrange
        String price = "";
        Set<Integer> posSet = Sets.newHashSet();
        posSet.add(1);

        // act
        String result = PriceHelper.hidePriceWithQuestionMark(price, posSet);

        // assert
        assertEquals("", result);
    }

    /**
     * 测试 hidePriceWithQuestionMark 方法，当 posSet 为空时
     */
    @Test
    public void testHidePriceWithQuestionMarkWhenPosSetIsEmpty() {
        // arrange
        String price = "123.45";
        Set<Integer> posSet = Sets.newHashSet();

        // act
        String result = PriceHelper.hidePriceWithQuestionMark(price, posSet);

        // assert
        assertEquals("123.45", result);
    }

    /**
     * 测试 hidePriceWithQuestionMark 方法，当 price 长度为1时
     */
    @Test
    public void testHidePriceWithQuestionMarkWhenPriceLengthIsOne() {
        // arrange
        String price = "1";
        Set<Integer> posSet = Sets.newHashSet();
        posSet.add(1);

        // act
        String result = PriceHelper.hidePriceWithQuestionMark(price, posSet);

        // assert
        assertEquals("?", result);
    }

    /**
     * 测试 hidePriceWithQuestionMark 方法，隐藏价格的第n位
     */
    @Test
    public void testHidePriceWithQuestionMarkHideSpecificPosition() {
        // arrange
        String price = "123.45";
        Set<Integer> posSet = Sets.newHashSet();
        posSet.add(2); // 隐藏第2位

        // act
        String result = PriceHelper.hidePriceWithQuestionMark(price, posSet);

        // assert
        assertEquals("1?3.45", result);
    }

    /**
     * 测试 hidePriceWithQuestionMark 方法，隐藏多个位置的价格
     */
    @Test
    public void testHidePriceWithQuestionMarkHideMultiplePositions() {
        // arrange
        String price = "123.45";
        Set<Integer> posSet = Sets.newHashSet();
        posSet.add(2);
        posSet.add(4);

        // act
        String result = PriceHelper.hidePriceWithQuestionMark(price, posSet);

        // assert
        assertEquals("1?3.?5", result);
    }
}
