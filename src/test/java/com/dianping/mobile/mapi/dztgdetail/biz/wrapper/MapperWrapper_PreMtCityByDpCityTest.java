package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.poi.areacommon.AreaCommonService;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import java.lang.reflect.Field;
import java.util.concurrent.Future;
import static org.mockito.ArgumentMatchers.anyInt;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class MapperWrapper_PreMtCityByDpCityTest {

    private MapperWrapper mapperWrapper;

    private AreaCommonService areaCommonServiceFuture;

    private MockedStatic<FutureFactory> mockedFutureFactory;

    @Before
    public void setUp() throws Exception {
        areaCommonServiceFuture = mock(AreaCommonService.class);
        mapperWrapper = new MapperWrapper();
        // Use reflection to set the private field
        Field field = MapperWrapper.class.getDeclaredField("areaCommonServiceFuture");
        field.setAccessible(true);
        field.set(mapperWrapper, areaCommonServiceFuture);
        mockedFutureFactory = Mockito.mockStatic(FutureFactory.class);
    }

    @After
    public void tearDown() {
        mockedFutureFactory.close();
    }

    /**
     * 测试dpCityId小于等于0的情况
     */
    @Test
    public void testPreMtCityByDpCityDpCityIdLessThanOrEqualToZero() throws Throwable {
        Future result = mapperWrapper.preMtCityByDpCity(0);
        assertNull(result);
    }

    /**
     * 测试dpCityId大于0，且getMtCityByDpCity方法调用成功的情况
     */
    @Test
    public void testPreMtCityByDpCityDpCityIdGreaterThanZeroAndMethodCallSuccess() throws Throwable {
        when(areaCommonServiceFuture.getMtCityByDpCity(anyInt())).thenReturn(1);
        Future mockFuture = mock(Future.class);
        mockedFutureFactory.when(FutureFactory::getFuture).thenReturn(mockFuture);
        Future result = mapperWrapper.preMtCityByDpCity(1);
        assertNotNull(result);
        assertSame(mockFuture, result);
    }

    /**
     * 测试dpCityId大于0，且getMtCityByDpCity方法调用发生异常的情况
     */
    @Test
    public void testPreMtCityByDpCityDpCityIdGreaterThanZeroAndMethodCallException() throws Throwable {
        when(areaCommonServiceFuture.getMtCityByDpCity(anyInt())).thenThrow(new RuntimeException());
        Future result = mapperWrapper.preMtCityByDpCity(1);
        assertNull(result);
    }
}
