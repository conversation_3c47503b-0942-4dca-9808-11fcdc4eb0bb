package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.deal.shop.dto.DealGroupDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.HashMap;
import java.util.Map;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class ShopDealsHelper_RemoveDuplicateDealGroupDTOTest {

    /**
     * 测试removeDuplicateDealGroupDTO0方法，当sameDealGroupDTOMap和otherDpShopId2DealGroupMap都为空时
     */
    @Test
    public void testRemoveDuplicateDealGroupDTO0BothMapsAreNull() {
        // arrange
        Map<Integer, DealGroupDTO> sameDealGroupDTOMap = null;
        Map<Long, Map<Integer, DealGroupDTO>> otherDpShopId2DealGroupMap = null;
        // act
        ShopDealsHelper.removeDuplicateDealGroupDTO0(sameDealGroupDTOMap, otherDpShopId2DealGroupMap);
        // assert
        assertNull(sameDealGroupDTOMap);
        assertNull(otherDpShopId2DealGroupMap);
    }

    /**
     * 测试removeDuplicateDealGroupDTO0方法，当sameDealGroupDTOMap为空，otherDpShopId2DealGroupMap不为空时
     */
    @Test
    public void testRemoveDuplicateDealGroupDTO0SameDealGroupDTOMapIsNull() {
        // arrange
        Map<Integer, DealGroupDTO> sameDealGroupDTOMap = null;
        Map<Long, Map<Integer, DealGroupDTO>> otherDpShopId2DealGroupMap = new HashMap<>();
        // act
        ShopDealsHelper.removeDuplicateDealGroupDTO0(sameDealGroupDTOMap, otherDpShopId2DealGroupMap);
        // assert
        assertNull(sameDealGroupDTOMap);
        assertNotNull(otherDpShopId2DealGroupMap);
    }

    /**
     * 测试removeDuplicateDealGroupDTO0方法，当sameDealGroupDTOMap不为空，otherDpShopId2DealGroupMap为空时
     */
    @Test
    public void testRemoveDuplicateDealGroupDTO0OtherDpShopId2DealGroupMapIsNull() {
        // arrange
        Map<Integer, DealGroupDTO> sameDealGroupDTOMap = new HashMap<>();
        sameDealGroupDTOMap.put(1, new DealGroupDTO());
        Map<Long, Map<Integer, DealGroupDTO>> otherDpShopId2DealGroupMap = null;
        // act
        ShopDealsHelper.removeDuplicateDealGroupDTO0(sameDealGroupDTOMap, otherDpShopId2DealGroupMap);
        // assert
        assertNotNull(sameDealGroupDTOMap);
        assertNull(otherDpShopId2DealGroupMap);
    }

    /**
     * 测试removeDuplicateDealGroupDTO0方法，当sameDealGroupDTOMap和otherDpShopId2DealGroupMap都不为空，但是sameDealGroupDTOMap中的团单ID在otherDpShopId2DealGroupMap的所有门店的团单信息中都不存在时
     */
    @Test
    public void testRemoveDuplicateDealGroupDTO0KeyNotExistsInSubMaps() {
        // arrange
        Map<Integer, DealGroupDTO> sameDealGroupDTOMap = new HashMap<>();
        sameDealGroupDTOMap.put(1, new DealGroupDTO());
        Map<Long, Map<Integer, DealGroupDTO>> otherDpShopId2DealGroupMap = new HashMap<>();
        otherDpShopId2DealGroupMap.put(1L, new HashMap<>());
        // act
        ShopDealsHelper.removeDuplicateDealGroupDTO0(sameDealGroupDTOMap, otherDpShopId2DealGroupMap);
        // assert
        assertNotNull(sameDealGroupDTOMap);
        assertNotNull(otherDpShopId2DealGroupMap);
        assertFalse(otherDpShopId2DealGroupMap.get(1L).containsKey(1));
    }

    /**
     * 测试removeDuplicateDealGroupDTO0方法，当sameDealGroupDTOMap和otherDpShopId2DealGroupMap都不为空，且sameDealGroupDTOMap中的团单ID在otherDpShopId2DealGroupMap的某些门店的团单信息中存在时
     */
    @Test
    public void testRemoveDuplicateDealGroupDTO0KeyExistsInSubMaps() {
        // arrange
        Map<Integer, DealGroupDTO> sameDealGroupDTOMap = new HashMap<>();
        sameDealGroupDTOMap.put(1, new DealGroupDTO());
        Map<Long, Map<Integer, DealGroupDTO>> otherDpShopId2DealGroupMap = new HashMap<>();
        Map<Integer, DealGroupDTO> subMap = new HashMap<>();
        subMap.put(1, new DealGroupDTO());
        otherDpShopId2DealGroupMap.put(1L, subMap);
        // act
        ShopDealsHelper.removeDuplicateDealGroupDTO0(sameDealGroupDTOMap, otherDpShopId2DealGroupMap);
        // assert
        assertNotNull(sameDealGroupDTOMap);
        assertNotNull(otherDpShopId2DealGroupMap);
        assertFalse(otherDpShopId2DealGroupMap.get(1L).containsKey(1));
    }
}
