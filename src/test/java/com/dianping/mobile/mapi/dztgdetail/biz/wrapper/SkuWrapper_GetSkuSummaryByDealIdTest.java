package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.dztheme.deal.DealSkuService;
import com.sankuai.dztheme.deal.req.SkuOptionBatchRequest;
import com.sankuai.dztheme.deal.res.DealSkuSummaryDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Future;
import static org.mockito.ArgumentMatchers.any;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class SkuWrapper_GetSkuSummaryByDealIdTest {

    @InjectMocks
    private SkuWrapper skuWrapper;

    @Mock
    private DealSkuService dealSkuService;

    @Mock
    private Future<Map<Long, DealSkuSummaryDTO>> future;

    @Test
    public void testGetSkuSummaryByDealIdFutureIsNull() throws Throwable {
        long dealId = 1L;
        IdTypeEnum idTypeEnum = IdTypeEnum.DP;
        when(dealSkuService.batchQuerySummary(any(SkuOptionBatchRequest.class))).thenReturn(null);
        DealSkuSummaryDTO result = skuWrapper.getSkuSummaryByDealId(dealId, idTypeEnum);
        assertNull(result);
    }
}
