package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

public class DiscountCardProcessorTest {

    private DiscountCardProcessor discountCardProcessor;

    private DealCtx dealCtx;

    private EnvCtx envCtx;

    @Before
    public void setUp() {
        discountCardProcessor = new DiscountCardProcessor();
        dealCtx = mock(DealCtx.class);
        envCtx = mock(EnvCtx.class);
    }

    /**
     * 测试正常场景：ctx 不为 null，且 ctx.getEnvCtx() 返回的 EnvCtx 对象不为 null，isMainApp() 返回 true
     */
    @Test
    public void testIsEnableWhenCtxAndEnvCtxNotNullAndIsMainAppTrue() throws Throwable {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.isMainApp()).thenReturn(true);
        // act
        boolean result = discountCardProcessor.isEnable(dealCtx);
        // assert
        assertTrue(result);
        verify(dealCtx).getEnvCtx();
        verify(envCtx).isMainApp();
    }

    /**
     * 测试正常场景：ctx 不为 null，且 ctx.getEnvCtx() 返回的 EnvCtx 对象不为 null，isMainApp() 返回 false
     */
    @Test
    public void testIsEnableWhenCtxAndEnvCtxNotNullAndIsMainAppFalse() throws Throwable {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.isMainApp()).thenReturn(false);
        // act
        boolean result = discountCardProcessor.isEnable(dealCtx);
        // assert
        assertFalse(result);
        verify(dealCtx).getEnvCtx();
        verify(envCtx).isMainApp();
    }

    /**
     * 测试异常场景：ctx 为 null，抛出 NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testIsEnableWhenCtxIsNull() throws Throwable {
        // arrange
        dealCtx = null;
        // act
        discountCardProcessor.isEnable(dealCtx);
        // assert
        // 期望抛出 NullPointerException
    }

    /**
     * 测试异常场景：ctx.getEnvCtx() 返回 null，抛出 NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testIsEnableWhenEnvCtxIsNull() throws Throwable {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(null);
        // act
        discountCardProcessor.isEnable(dealCtx);
        // assert
        // 期望抛出 NullPointerException
    }
}
