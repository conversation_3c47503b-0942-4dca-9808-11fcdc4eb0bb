package com.dianping.mobile.mapi.dztgdetail.biz.processor.shop;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ShopTagWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class ShopTagsProcessorPrepareTest {

    @InjectMocks
    private ShopTagsProcessor shopTagsProcessor;

    @Mock
    private ShopTagWrapper shopTagWrapper;

    @Mock
    private FutureCtx futureCtx;

    @Mock
    private Future future;

    @Mock
    private DealCtx ctx;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
    }

    public void prepare(DealCtx ctx) {
        Future future = shopTagWrapper.preGetDpShopTags(ctx.getDpLongShopId());
        ctx.getFutureCtx().setShopTagFuture(future);
    }

    /**
     * Test the normal scenario where a valid shop ID is provided and a non-null Future is returned.
     */
    @Test
    public void testPrepareNormalScenario() throws Throwable {
        // arrange
        long dpLongShopId = 12345L;
        when(ctx.getDpLongShopId()).thenReturn(dpLongShopId);
        when(shopTagWrapper.preGetDpShopTags(dpLongShopId)).thenReturn(future);
        // act
        shopTagsProcessor.prepare(ctx);
        // assert
        verify(shopTagWrapper).preGetDpShopTags(dpLongShopId);
        verify(futureCtx).setShopTagFuture(future);
    }

    /**
     * Test the boundary scenario where the shop ID is null and a null Future is returned.
     */
    @Test
    public void testPrepareBoundaryScenario() throws Throwable {
        // arrange
        // Use a valid long value instead of null
        long dpLongShopId = 0L;
        when(ctx.getDpLongShopId()).thenReturn(dpLongShopId);
        when(shopTagWrapper.preGetDpShopTags(dpLongShopId)).thenReturn(null);
        // act
        shopTagsProcessor.prepare(ctx);
        // assert
        verify(shopTagWrapper).preGetDpShopTags(dpLongShopId);
        verify(futureCtx).setShopTagFuture(null);
    }

    /**
     * Test the exception scenario where an exception is thrown by shopTagWrapper.preGetDpShopTags.
     */
    @Test(expected = Exception.class)
    public void testPrepareExceptionScenario() throws Throwable {
        // arrange
        long dpLongShopId = 12345L;
        when(ctx.getDpLongShopId()).thenReturn(dpLongShopId);
        when(shopTagWrapper.preGetDpShopTags(dpLongShopId)).thenThrow(new Exception("Mock Exception"));
        // act
        shopTagsProcessor.prepare(ctx);
        // assert
        // The exception is expected to be thrown
    }
}
