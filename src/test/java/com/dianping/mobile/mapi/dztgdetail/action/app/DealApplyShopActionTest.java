package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetDealApplyShopRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.shop.DealApplyShopVO;
import com.dianping.mobile.mapi.dztgdetail.facade.DealApplyShopFacade;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DealApplyShopActionTest {

    @InjectMocks
    private DealApplyShopAction dealApplyShopAction;

    @Mock
    private DealApplyShopFacade dealApplyShopFacade;

    @Mock
    private IMobileContext context;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试execute方法，异常情况
     */
    @Test
    public void testExecuteException() throws Exception {
        // arrange
        GetDealApplyShopRequest request = new GetDealApplyShopRequest();
        when(dealApplyShopFacade.queryBestShop(any(GetDealApplyShopRequest.class), anyBoolean(), anyInt())).thenThrow(new RuntimeException("Mock Exception"));

        // act
        IMobileResponse response = dealApplyShopAction.execute(request, context);

        // assert
        verify(dealApplyShopFacade, times(1)).queryBestShop(any(GetDealApplyShopRequest.class), anyBoolean(), anyInt());
        assert response.equals(Resps.SYSTEM_ERROR);
    }

    /**
     * 测试execute方法，当dealApplyShopFacade.queryBestShop返回null时
     */
    @Test
    public void testExecuteWhenQueryBestShopReturnsNull() throws Exception {
        // arrange
        GetDealApplyShopRequest request = new GetDealApplyShopRequest();
        when(dealApplyShopFacade.queryBestShop(any(GetDealApplyShopRequest.class), anyBoolean(), anyInt())).thenReturn(null);

        // act
        IMobileResponse response = dealApplyShopAction.execute(request, context);

        // assert
        verify(dealApplyShopFacade, times(1)).queryBestShop(any(GetDealApplyShopRequest.class), anyBoolean(), anyInt());
        assert response instanceof CommonMobileResponse;
        assert response.getData() == null;
    }

    /**
     * 测试execute方法，当AppCtxHelper.getAppClientType返回特定值时
     */
    @Test
    public void testExecuteWhenGetAppClientTypeReturnsSpecificValue() throws Exception {
        // arrange
        GetDealApplyShopRequest request = new GetDealApplyShopRequest();
        request.setDealGroupId(1L);
        request.setHomeCityId(1);
        request.setGpsCityId(1);
        request.setUserLat(1.0);
        request.setUserLng(1.0);
        DealApplyShopVO expectedResponse = new DealApplyShopVO(1L, 2L);
        when(dealApplyShopFacade.queryBestShop(request, false, 100501)).thenReturn(expectedResponse);

        // act
        IMobileResponse response = dealApplyShopAction.execute(request, context);

        // assert
        verify(dealApplyShopFacade, times(1)).queryBestShop(request, false, 100501);
        assert response instanceof CommonMobileResponse;
        assert response.getData().equals(expectedResponse);
    }

}
