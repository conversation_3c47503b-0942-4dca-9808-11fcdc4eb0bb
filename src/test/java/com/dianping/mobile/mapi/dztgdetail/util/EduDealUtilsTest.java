package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PromoDetailModule;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.beautycontent.creator.application.edu.technicianVideo.dto.EduTechnicianVideoDTO;
import com.sankuai.beautycontent.creator.application.edu.technicianVideo.dto.EduTechnicianVideoListForCDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class EduDealUtilsTest {

    @Mock
    private DealGroupDTO dealGroupDTO;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private FutureCtx futureCtx;

    @Mock
    private Future<RemoteResponse<EduTechnicianVideoListForCDTO>> future;

    @Mock
    RemoteResponse<EduTechnicianVideoListForCDTO> videoList;

    @Mock
    EduTechnicianVideoListForCDTO videoListData;

    @Mock
    DealGroupServiceProjectDTO dealGroupServiceProjectDTO;


    enum Status {
        LEGAL(0, "审核通过"),
        CHECKING(2, "审核中"),
        ILLEGAL(4, "审核驳回");
        public int type;
        public String value;

        Status(int type, String value) {
            this.type = type;
            this.value = value;
        }

    }

    /**
     * 测试getServiceTypeId方法，当DealGroupDTO为null时
     */
    @Test
    public void testGetServiceTypeIdWhenDealGroupDTOIsNull() {
        Long result = EduDealUtils.getServiceTypeId(null);
        assertEquals(null, result);
    }

    /**
     * 测试getServiceTypeId方法，当DealGroupDTO的Category为null时
     */
    @Test
    public void testGetServiceTypeIdWhenCategoryIsNull() {
        when(dealGroupDTO.getCategory()).thenReturn(null);
        Long result = EduDealUtils.getServiceTypeId(dealGroupDTO);
        assertEquals(null, result);
    }

    /**
     * 测试getLegalVideoNum方法，当获取Future结果时抛出异常
     */
    @Test
    public void testGetLegalVideoNumWhenFutureGetNull() throws Exception {
        when(dealCtx.getFutureCtx()).thenReturn(futureCtx);
        when(futureCtx.getEduTechnicianVideoListFuture()).thenReturn(future);
        when(future.get(100, TimeUnit.MILLISECONDS)).thenReturn(null);
        int result = EduDealUtils.getLegalVideoNum(dealCtx);
        assertEquals(0, result);
    }


    /**
     * 测试getLegalVideoNum方法，当获取EduTechnicianVideoListFuture为null时返回0
     */
    @Test
    public void testGetLegalVideoNumWhenFutureNull() {
        when(dealCtx.getFutureCtx()).thenReturn(futureCtx);
        when(futureCtx.getEduTechnicianVideoListFuture()).thenReturn(null);
        int result = EduDealUtils.getLegalVideoNum(dealCtx);
        assertEquals(0, result);
    }


    @Test
    public void testGetLegalVideoNumWhenFutureGetVideoList() throws Exception {
        List<EduTechnicianVideoDTO> eduVideoList = new ArrayList<>();
        eduVideoList.add(new EduTechnicianVideoDTO());
        eduVideoList.add(new EduTechnicianVideoDTO());

        eduVideoList.get(0).setStatus(Status.LEGAL.type);
        eduVideoList.get(1).setStatus(Status.ILLEGAL.type);

        when(dealCtx.getFutureCtx()).thenReturn(futureCtx);
        when(futureCtx.getEduTechnicianVideoListFuture()).thenReturn(future);
        when(future.get(100, TimeUnit.MILLISECONDS)).thenReturn(videoList);
        when(videoList.getData()).thenReturn(videoListData);
        when(videoListData.getVideos()).thenReturn(eduVideoList);

        int result = EduDealUtils.getLegalVideoNum(dealCtx);
        assertEquals(1, result);
    }

    /**
     * 测试 isVocationalEduPlan 方法，dealGroupDTO 为 null 的情况
     */
    @Test
    public void testIsVocationalEduPlanDealGroupDTOIsNull() {
        // arrange
        DealGroupDTO dealGroupDTO = null;

        // act
        boolean result = EduDealUtils.isVocationalEduPlan(dealGroupDTO);

        // assert
        assertFalse(result);
    }

    /**
     * 测试 isVocationalEduPlan 方法，dealGroupDTO.getCategory() 为 null 的情况
     */
    @Test
    public void testIsVocationalEduPlanDealGroupDTOCategoryIsNull() {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setCategory(null);

        // act
        boolean result = EduDealUtils.isVocationalEduPlan(dealGroupDTO);

        // assert
        assertFalse(result);
    }

    /**
     * 测试 isVocationalEduPlan 方法，serviceTypeId 不在 SERVICE_ZERO_PLAN_TYPE_ID_LIST 中的情况
     */
    @Test
    public void testIsVocationalEduPlanServiceTypeIdNotInList() {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setServiceTypeId(999L); // 假设 999 不在 SERVICE_ZERO_PLAN_TYPE_ID_LIST 中
        dealGroupDTO.setCategory(categoryDTO);

        // act
        boolean result = EduDealUtils.isVocationalEduPlan(dealGroupDTO);

        // assert
        assertFalse(result);
    }

    /**
     * 测试 isVocationalEduPlan 方法，serviceTypeId 在 SERVICE_ZERO_PLAN_TYPE_ID_LIST 中的情况
     */
    @Test
    public void testIsVocationalEduPlanServiceTypeIdInList() {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setServiceTypeId(0L);
        dealGroupDTO.setCategory(categoryDTO);

        // act
        boolean result = EduDealUtils.isVocationalEduPlan(dealGroupDTO);

        // assert
        assertTrue(result);
    }

    /**
     * 测试 dealCtx 为 null 的情况
     */
    @Test
    public void testGetShortClassFreeTrailNumCtxIsNull() {
       int result = EduDealUtils.getShortClassFreeTrailNum(null);
        Assert.assertEquals(0, result);
    }

    /**
     * 测试 dealCtx.getDealGroupDTO() 为 null 的情况
     */
    @Test
    public void testGetShortClassFreeTrailNumDealGroupDTOIsNull() {
        when(dealCtx.getDealGroupDTO()).thenReturn(null);
        int result = EduDealUtils.getShortClassFreeTrailNum(dealCtx);
        assertEquals(0, result);
    }

    /**
     * 测试 dealCtx.getDealGroupDTO().getServiceProject().getMustGroups() 为空集合的情况
     */
    @Test
    public void testGetShortClassFreeTrailNumMustGroupsIsEmpty() {
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getServiceProject()).thenReturn(dealGroupServiceProjectDTO);
        when(dealGroupServiceProjectDTO.getMustGroups()).thenReturn(new ArrayList<>());
        int result = EduDealUtils.getShortClassFreeTrailNum(dealCtx);
        Assert.assertEquals(0, result);
    }

    /**
     * 测试正常情况，有短期课，且有数量和次数
     */
    @Test
    public void testGetShortClassFreeTrailNumNormal() {
        // Arrange
        ServiceProjectAttrDTO serviceProjectAttrDTO1 = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO1.setAttrName("count");
        serviceProjectAttrDTO1.setAttrValue("3");

        ServiceProjectAttrDTO serviceProjectAttrDTO2 = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO2.setAttrName("quantity");
        serviceProjectAttrDTO2.setAttrValue("5");

        ServiceProjectAttrDTO serviceProjectAttrDTO3 = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO3.setAttrName("eduCourseType");
        serviceProjectAttrDTO3.setAttrValue("短期课");

        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        serviceProjectDTO.setAttrs(Lists.newArrayList(serviceProjectAttrDTO1, serviceProjectAttrDTO2, serviceProjectAttrDTO3));
        List<ServiceProjectDTO> groups = Lists.newArrayList(serviceProjectDTO);

        MustServiceProjectGroupDTO mustServiceProjectGroupDTO = new MustServiceProjectGroupDTO();
        mustServiceProjectGroupDTO.setGroups(groups);
        List<MustServiceProjectGroupDTO> mustGroups = Lists.newArrayList(mustServiceProjectGroupDTO);

        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getServiceProject()).thenReturn(dealGroupServiceProjectDTO);
        when(dealGroupServiceProjectDTO.getMustGroups()).thenReturn(mustGroups);

        // Act
        int result = EduDealUtils.getShortClassFreeTrailNum(dealCtx);

        // Assert
        Assert.assertEquals(8, result);
    }

    /**
     * 测试有短期课，但属性值为空的情况
     */
    @Test
    public void testGetShortClassFreeTrailNumAttrValueIsEmpty() {
        ServiceProjectAttrDTO serviceProjectAttrDTO1 = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO1.setAttrName("count");
        serviceProjectAttrDTO1.setAttrValue(null);

        ServiceProjectAttrDTO serviceProjectAttrDTO2 = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO2.setAttrName("quantity");
        serviceProjectAttrDTO2.setAttrValue(null);

        ServiceProjectAttrDTO serviceProjectAttrDTO3 = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO3.setAttrName("eduCourseType");
        serviceProjectAttrDTO3.setAttrValue("短期课");

        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        serviceProjectDTO.setAttrs(Lists.newArrayList(serviceProjectAttrDTO1, serviceProjectAttrDTO2, serviceProjectAttrDTO3));
        List<ServiceProjectDTO> groups = Lists.newArrayList(serviceProjectDTO);

        MustServiceProjectGroupDTO mustServiceProjectGroupDTO = new MustServiceProjectGroupDTO();
        mustServiceProjectGroupDTO.setGroups(groups);
        List<MustServiceProjectGroupDTO> mustGroups = Lists.newArrayList(mustServiceProjectGroupDTO);

        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getServiceProject()).thenReturn(dealGroupServiceProjectDTO);
        when(dealGroupServiceProjectDTO.getMustGroups()).thenReturn(mustGroups);

        // Act
        int result = EduDealUtils.getShortClassFreeTrailNum(dealCtx);

        // Assert
        Assert.assertEquals(0, result);
    }

    /**
     * 测试有短期课，但不包含数量和次数属性的情况
     */
    @Test
    public void testGetShortClassFreeTrailNumNoQuantityAndCountAttr() {
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("eduCourseType");
        serviceProjectAttrDTO.setAttrValue("短期课");

        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        serviceProjectDTO.setAttrs(Lists.newArrayList(serviceProjectAttrDTO));
        List<ServiceProjectDTO> groups = Lists.newArrayList(serviceProjectDTO);

        MustServiceProjectGroupDTO mustServiceProjectGroupDTO = new MustServiceProjectGroupDTO();
        mustServiceProjectGroupDTO.setGroups(groups);
        List<MustServiceProjectGroupDTO> mustGroups = Lists.newArrayList(mustServiceProjectGroupDTO);

        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getServiceProject()).thenReturn(dealGroupServiceProjectDTO);
        when(dealGroupServiceProjectDTO.getMustGroups()).thenReturn(mustGroups);

        // Act
        int result = EduDealUtils.getShortClassFreeTrailNum(dealCtx);

        // Assert
        Assert.assertEquals(0, result);
    }

    /**
     * 测试有多个短期课，累加数量和次数
     */
    @Test
    public void testGetShortClassFreeTrailNumMultipleShortClasses() {
        // Arrange
        ServiceProjectAttrDTO serviceProjectAttrDTO1 = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO1.setAttrName("count");
        serviceProjectAttrDTO1.setAttrValue("3");

        ServiceProjectAttrDTO serviceProjectAttrDTO2 = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO2.setAttrName("count");
        serviceProjectAttrDTO2.setAttrValue("2");

        ServiceProjectAttrDTO serviceProjectAttrDTO3 = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO3.setAttrName("quantity");
        serviceProjectAttrDTO3.setAttrValue("2");

        ServiceProjectAttrDTO serviceProjectAttrDTO4 = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO4.setAttrName("quantity");
        serviceProjectAttrDTO4.setAttrValue("1");

        ServiceProjectAttrDTO serviceProjectAttrDTO5 = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO5.setAttrName("eduCourseType");
        serviceProjectAttrDTO5.setAttrValue("短期课");

        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        serviceProjectDTO.setAttrs(Lists.newArrayList(serviceProjectAttrDTO1, serviceProjectAttrDTO2, serviceProjectAttrDTO3, serviceProjectAttrDTO4, serviceProjectAttrDTO5));
        List<ServiceProjectDTO> groups = Lists.newArrayList(serviceProjectDTO);

        MustServiceProjectGroupDTO mustServiceProjectGroupDTO = new MustServiceProjectGroupDTO();
        mustServiceProjectGroupDTO.setGroups(groups);
        List<MustServiceProjectGroupDTO> mustGroups = Lists.newArrayList(mustServiceProjectGroupDTO);

        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getServiceProject()).thenReturn(dealGroupServiceProjectDTO);
        when(dealGroupServiceProjectDTO.getMustGroups()).thenReturn(mustGroups);

        // Act
        int result = EduDealUtils.getShortClassFreeTrailNum(dealCtx);

        // Assert
        Assert.assertEquals(8, result);
    }


    /**
     * 测试 dealCtx 为 null 的情况
     */
    @Test
    public void testIsShorClassCtxIsNull() {
        boolean result = EduDealUtils.isShortClass(null);
        Assert.assertEquals(false, result);
    }

    /**
     * 测试 dealCtx.getDealGroupDTO() 为 null 的情况
     */
    @Test
    public void testIsShorClassDealGroupDTOIsNull() {
        when(dealCtx.getDealGroupDTO()).thenReturn(null);
        boolean result = EduDealUtils.isShortClass(dealCtx);
        assertEquals(false, result);
    }

    /**
     * 测试 dealCtx.getDealGroupDTO().getServiceProject().getMustGroups() 为空集合的情况
     */
    @Test
    public void testIsShortClassMustGroupsIsEmpty() {
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getServiceProject()).thenReturn(dealGroupServiceProjectDTO);
        when(dealGroupServiceProjectDTO.getMustGroups()).thenReturn(new ArrayList<>());
        boolean result = EduDealUtils.isShortClass(dealCtx);
        Assert.assertEquals(false, result);
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testIsShortClassNormal() {
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("eduCourseType");
        serviceProjectAttrDTO.setAttrValue("短期课");

        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        serviceProjectDTO.setAttrs(Lists.newArrayList(serviceProjectAttrDTO));
        List<ServiceProjectDTO> groups = Lists.newArrayList(serviceProjectDTO);

        MustServiceProjectGroupDTO mustServiceProjectGroupDTO = new MustServiceProjectGroupDTO();
        mustServiceProjectGroupDTO.setGroups(groups);
        List<MustServiceProjectGroupDTO> mustGroups = Lists.newArrayList(mustServiceProjectGroupDTO);

        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getServiceProject()).thenReturn(dealGroupServiceProjectDTO);
        when(dealGroupServiceProjectDTO.getMustGroups()).thenReturn(mustGroups);

        // Act
        boolean result = EduDealUtils.isShortClass(dealCtx);

        // Assert
        Assert.assertEquals(true, result);
    }


    /**
     * 测试 dealCtx 为 null 的情况
     */
    @Test
    public void testGetClassDurationCtxIsNull() {
        String result = EduDealUtils.getClassDuration(null);
        Assert.assertEquals(null, result);
    }

    /**
     * 测试 dealCtx.getDealGroupDTO() 为 null 的情况
     */
    @Test
    public void testGetClassDurationDealGroupDTOIsNull() {
        when(dealCtx.getDealGroupDTO()).thenReturn(null);
        String result = EduDealUtils.getClassDuration(dealCtx);
        assertEquals(null, result);
    }

    /**
     * 测试 dealCtx.getDealGroupDTO().getServiceProject().getMustGroups() 为空集合的情况
     */
    @Test
    public void testGetClassDurationMustGroupsIsEmpty() {
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getServiceProject()).thenReturn(dealGroupServiceProjectDTO);
        when(dealGroupServiceProjectDTO.getMustGroups()).thenReturn(new ArrayList<>());
        String result = EduDealUtils.getClassDuration(dealCtx);
        Assert.assertEquals(null, result);
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testGetClassDurationNormal() {
        // Arrange
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("classDuration");
        serviceProjectAttrDTO.setAttrValue("30");

        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        serviceProjectDTO.setAttrs(Lists.newArrayList(serviceProjectAttrDTO));
        List<ServiceProjectDTO> groups = Lists.newArrayList(serviceProjectDTO);

        MustServiceProjectGroupDTO mustServiceProjectGroupDTO = new MustServiceProjectGroupDTO();
        mustServiceProjectGroupDTO.setGroups(groups);
        List<MustServiceProjectGroupDTO> mustGroups = Lists.newArrayList(mustServiceProjectGroupDTO);

        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getServiceProject()).thenReturn(dealGroupServiceProjectDTO);
        when(dealGroupServiceProjectDTO.getMustGroups()).thenReturn(mustGroups);

        // Act
        String result = EduDealUtils.getClassDuration(dealCtx);

        // Assert
        Assert.assertEquals("30", result);
    }


    /**
     * 测试 dealCtx 为 null 的情况
     */
    @Test
    public void testPreCheckShortClassHasButtonCtxIsNull() {
        boolean result = EduDealUtils.preCheckShortClassHasButton(null);
        Assert.assertEquals(false, result);
    }

    /**
     * 测试 dealCtx.getDealGroupDTO() 为 null 的情况
     */
    @Test
    public void testPreCheckShortClassHasButtonDealGroupDTOIsNull() {
        when(dealCtx.getDealGroupDTO()).thenReturn(null);
        boolean result = EduDealUtils.preCheckShortClassHasButton(dealCtx);
        assertEquals(false, result);
    }

    /**
     * 测试 dealCtx.getDealGroupDTO().getServiceProject().getMustGroups() 为空集合的情况
     */
    @Test
    public void testPreCheckShortClassHasButtonMustGroupsIsEmpty() {
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getServiceProject()).thenReturn(dealGroupServiceProjectDTO);
        when(dealGroupServiceProjectDTO.getMustGroups()).thenReturn(new ArrayList<>());
        boolean result = EduDealUtils.preCheckShortClassHasButton(dealCtx);
        Assert.assertEquals(false, result);
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testPreCheckShortClassHasButtonNormal() {
        ServiceProjectAttrDTO serviceProjectAttrDTO1 = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO1.setAttrName("eduCourseType");
        serviceProjectAttrDTO1.setAttrValue("短期课");

        ServiceProjectAttrDTO serviceProjectAttrDTO2 = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO2.setAttrName("bookingInfo");
        serviceProjectAttrDTO2.setAttrValue("是");

        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        serviceProjectDTO.setAttrs(Lists.newArrayList(serviceProjectAttrDTO1, serviceProjectAttrDTO2));
        List<ServiceProjectDTO> groups = Lists.newArrayList(serviceProjectDTO);

        MustServiceProjectGroupDTO mustServiceProjectGroupDTO = new MustServiceProjectGroupDTO();
        mustServiceProjectGroupDTO.setGroups(groups);
        List<MustServiceProjectGroupDTO> mustGroups = Lists.newArrayList(mustServiceProjectGroupDTO);

        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getServiceProject()).thenReturn(dealGroupServiceProjectDTO);
        when(dealGroupServiceProjectDTO.getMustGroups()).thenReturn(mustGroups);

        // Act
        boolean result = EduDealUtils.preCheckShortClassHasButton(dealCtx);

        // Assert
        Assert.assertEquals(true, result);
    }



    /**
     * 缺少bookinginfo
     */
    @Test
    public void testPreCheckShortClassHasButtonWithOutBookingInfo() {
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("eduCourseType");
        serviceProjectAttrDTO.setAttrValue("短期课");


        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        serviceProjectDTO.setAttrs(Lists.newArrayList(serviceProjectAttrDTO));
        List<ServiceProjectDTO> groups = Lists.newArrayList(serviceProjectDTO);

        MustServiceProjectGroupDTO mustServiceProjectGroupDTO = new MustServiceProjectGroupDTO();
        mustServiceProjectGroupDTO.setGroups(groups);
        List<MustServiceProjectGroupDTO> mustGroups = Lists.newArrayList(mustServiceProjectGroupDTO);

        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getServiceProject()).thenReturn(dealGroupServiceProjectDTO);
        when(dealGroupServiceProjectDTO.getMustGroups()).thenReturn(mustGroups);

        // Act
        boolean result = EduDealUtils.preCheckShortClassHasButton(dealCtx);

        // Assert
        Assert.assertEquals(false, result);
    }

    /**
     * 缺少eduCourseType
     */
    @Test
    public void testPreCheckShortClassHasButtonWithOutEduCourseType() {
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("bookingInfo");
        serviceProjectAttrDTO.setAttrValue("是");

        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        serviceProjectDTO.setAttrs(Lists.newArrayList(serviceProjectAttrDTO));
        List<ServiceProjectDTO> groups = Lists.newArrayList(serviceProjectDTO);

        MustServiceProjectGroupDTO mustServiceProjectGroupDTO = new MustServiceProjectGroupDTO();
        mustServiceProjectGroupDTO.setGroups(groups);
        List<MustServiceProjectGroupDTO> mustGroups = Lists.newArrayList(mustServiceProjectGroupDTO);

        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getServiceProject()).thenReturn(dealGroupServiceProjectDTO);
        when(dealGroupServiceProjectDTO.getMustGroups()).thenReturn(mustGroups);

        // Act
        boolean result = EduDealUtils.preCheckShortClassHasButton(dealCtx);

        // Assert
        Assert.assertEquals(false, result);
    }

    /**
     * 课时数为0
     */
    @Test
    public void testModifyDiscountRateStrTimesIsZero() {
        // Arrange
        BigDecimal marketPrice = new BigDecimal("100");
        BigDecimal finalPrice = new BigDecimal("80");
        PromoDetailModule promoDetailModule= new PromoDetailModule();
        ServiceProjectAttrDTO serviceProjectAttrDTO1 = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO1.setAttrName("count");
        serviceProjectAttrDTO1.setAttrValue("0");

        ServiceProjectAttrDTO serviceProjectAttrDTO2 = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO2.setAttrName("quantity");
        serviceProjectAttrDTO2.setAttrValue("0");

        ServiceProjectAttrDTO serviceProjectAttrDTO3 = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO3.setAttrName("eduCourseType");
        serviceProjectAttrDTO3.setAttrValue("短期课");

        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        serviceProjectDTO.setAttrs(Lists.newArrayList(serviceProjectAttrDTO1, serviceProjectAttrDTO2, serviceProjectAttrDTO3));
        List<ServiceProjectDTO> groups = Lists.newArrayList(serviceProjectDTO);

        MustServiceProjectGroupDTO mustServiceProjectGroupDTO = new MustServiceProjectGroupDTO();
        mustServiceProjectGroupDTO.setGroups(groups);
        List<MustServiceProjectGroupDTO> mustGroups = Lists.newArrayList(mustServiceProjectGroupDTO);

        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getServiceProject()).thenReturn(dealGroupServiceProjectDTO);
        when(dealGroupServiceProjectDTO.getMustGroups()).thenReturn(mustGroups);
        // Act
        EduDealUtils.modifyDiscountRateStr(dealCtx, promoDetailModule, marketPrice, finalPrice);

        // Assert
        Assert.assertNull(promoDetailModule.getMarketPromoDiscount());
    }


    /**
     * quantity不为空
     */
    @Test
    public void testModifyDiscountRateStrWithQuantity() {
        // Arrange
        BigDecimal marketPrice = new BigDecimal("100");
        BigDecimal finalPrice = new BigDecimal("80");
        PromoDetailModule promoDetailModule= new PromoDetailModule();
        promoDetailModule.setMarketPromoDiscount("123");

        ServiceProjectAttrDTO serviceProjectAttrDTO1 = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO1.setAttrName("count");
        serviceProjectAttrDTO1.setAttrValue("0");

        ServiceProjectAttrDTO serviceProjectAttrDTO2 = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO2.setAttrName("quantity");
        serviceProjectAttrDTO2.setAttrValue("10");

        ServiceProjectAttrDTO serviceProjectAttrDTO3 = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO3.setAttrName("eduCourseType");
        serviceProjectAttrDTO3.setAttrValue("短期课");

        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        serviceProjectDTO.setAttrs(Lists.newArrayList(serviceProjectAttrDTO1, serviceProjectAttrDTO2, serviceProjectAttrDTO3));
        List<ServiceProjectDTO> groups = Lists.newArrayList(serviceProjectDTO);

        MustServiceProjectGroupDTO mustServiceProjectGroupDTO = new MustServiceProjectGroupDTO();
        mustServiceProjectGroupDTO.setGroups(groups);
        List<MustServiceProjectGroupDTO> mustGroups = Lists.newArrayList(mustServiceProjectGroupDTO);

        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getServiceProject()).thenReturn(dealGroupServiceProjectDTO);
        when(dealGroupServiceProjectDTO.getMustGroups()).thenReturn(mustGroups);

        EduDealUtils.modifyDiscountRateStr(dealCtx, promoDetailModule, marketPrice, finalPrice);

        // Assert
        Assert.assertEquals("123｜8.0/节", promoDetailModule.getMarketPromoDiscount());
    }



    /**
     * count不为空
     */
    @Test
    public void testModifyDiscountRateStrWithCount() {
        // Arrange
        BigDecimal marketPrice = new BigDecimal("100");
        BigDecimal finalPrice = new BigDecimal("80");
        PromoDetailModule promoDetailModule= new PromoDetailModule();
        promoDetailModule.setMarketPromoDiscount("123");


        ServiceProjectAttrDTO serviceProjectAttrDTO1 = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO1.setAttrName("count");
        serviceProjectAttrDTO1.setAttrValue("10");

        ServiceProjectAttrDTO serviceProjectAttrDTO2 = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO2.setAttrName("quantity");
        serviceProjectAttrDTO2.setAttrValue("0");

        ServiceProjectAttrDTO serviceProjectAttrDTO3 = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO3.setAttrName("eduCourseType");
        serviceProjectAttrDTO3.setAttrValue("短期课");

        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        serviceProjectDTO.setAttrs(Lists.newArrayList(serviceProjectAttrDTO1, serviceProjectAttrDTO2, serviceProjectAttrDTO3));
        List<ServiceProjectDTO> groups = Lists.newArrayList(serviceProjectDTO);

        MustServiceProjectGroupDTO mustServiceProjectGroupDTO = new MustServiceProjectGroupDTO();
        mustServiceProjectGroupDTO.setGroups(groups);
        List<MustServiceProjectGroupDTO> mustGroups = Lists.newArrayList(mustServiceProjectGroupDTO);

        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getServiceProject()).thenReturn(dealGroupServiceProjectDTO);
        when(dealGroupServiceProjectDTO.getMustGroups()).thenReturn(mustGroups);

        EduDealUtils.modifyDiscountRateStr(dealCtx, promoDetailModule, marketPrice, finalPrice);

        // Assert
        Assert.assertEquals("123｜8.0/节", promoDetailModule.getMarketPromoDiscount());
    }



    /**
     * DiscountRateStr初始内容为空，count不为空
     */
    @Test
    public void testModifyDiscountRateStrWithDiscountRateStrEmpty() {
        // Arrange
        BigDecimal marketPrice = new BigDecimal("100");
        BigDecimal finalPrice = new BigDecimal("80");
        PromoDetailModule promoDetailModule= new PromoDetailModule();


        ServiceProjectAttrDTO serviceProjectAttrDTO1 = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO1.setAttrName("count");
        serviceProjectAttrDTO1.setAttrValue("10");

        ServiceProjectAttrDTO serviceProjectAttrDTO2 = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO2.setAttrName("quantity");
        serviceProjectAttrDTO2.setAttrValue("0");

        ServiceProjectAttrDTO serviceProjectAttrDTO3 = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO3.setAttrName("eduCourseType");
        serviceProjectAttrDTO3.setAttrValue("短期课");

        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        serviceProjectDTO.setAttrs(Lists.newArrayList(serviceProjectAttrDTO1, serviceProjectAttrDTO2, serviceProjectAttrDTO3));
        List<ServiceProjectDTO> groups = Lists.newArrayList(serviceProjectDTO);

        MustServiceProjectGroupDTO mustServiceProjectGroupDTO = new MustServiceProjectGroupDTO();
        mustServiceProjectGroupDTO.setGroups(groups);
        List<MustServiceProjectGroupDTO> mustGroups = Lists.newArrayList(mustServiceProjectGroupDTO);

        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getServiceProject()).thenReturn(dealGroupServiceProjectDTO);
        when(dealGroupServiceProjectDTO.getMustGroups()).thenReturn(mustGroups);

        EduDealUtils.modifyDiscountRateStr(dealCtx, promoDetailModule, marketPrice, finalPrice);

        // Assert
        Assert.assertEquals("8.0/节", promoDetailModule.getMarketPromoDiscount());
    }


    @Test
    public void testExtractNUM() {
        Assert.assertEquals(10, EduDealUtils.extractNumber("10"));
        Assert.assertEquals(10, EduDealUtils.extractNumber("10节"));
    }

    /**
     * 测试服务类型为空的情况
     */
    @Test
    public void testNotNeedShortClassAsServiceType_ServiceTypeIsEmpty() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Method notNeedShortClassAsServiceType = EduDealUtils.class.getDeclaredMethod("notNeedShortClassAsServiceType", DealCtx.class);
        notNeedShortClassAsServiceType.setAccessible(true);
        // arrange

        // act
        boolean result = (boolean)notNeedShortClassAsServiceType.invoke(EduDealUtils.class, dealCtx);

        // assert
        assertFalse("应当返回 false 当服务类型为空", result);
    }

    /**
     * 测试服务类型不在 NOT_SHOW_SHORT_CLASS_SERVICE_TYPE 集合中
     */
    @Test
    public void testNotNeedShortClassAsServiceType_ServiceTypeNotInList() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Method notNeedShortClassAsServiceType = EduDealUtils.class.getDeclaredMethod("notNeedShortClassAsServiceType", DealCtx.class);
        notNeedShortClassAsServiceType.setAccessible(true);
        // arrange
        List<AttributeDTO> attributeDTOS = new ArrayList<>();
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("service_type");
        attributeDTO.setValue(Lists.newArrayList("notIn"));
        attributeDTOS.addAll(Lists.newArrayList(attributeDTO));
        when(dealCtx.getAttrs()).thenReturn(attributeDTOS);

        // act
        boolean result = (boolean)notNeedShortClassAsServiceType.invoke(EduDealUtils.class, dealCtx);

        // assert
        assertFalse("应当返回 false 当服务类型不在 NOT_SHOW_SHORT_CLASS_SERVICE_TYPE 集合中", result);
    }

    /**
     * 测试服务类型在 NOT_SHOW_SHORT_CLASS_SERVICE_TYPE 集合中
     */
    @Test
    public void testNotNeedShortClassAsServiceType_ServiceTypeInList() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Method notNeedShortClassAsServiceType = EduDealUtils.class.getDeclaredMethod("notNeedShortClassAsServiceType", DealCtx.class);
        notNeedShortClassAsServiceType.setAccessible(true);
        // arrange
        List<AttributeDTO> attributeDTOS = new ArrayList<>();
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("service_type");
        attributeDTO.setValue(Lists.newArrayList("绘画培训"));
        attributeDTOS.addAll(Lists.newArrayList(attributeDTO));
        when(dealCtx.getAttrs()).thenReturn(attributeDTOS);

        // act
        boolean result = (boolean)notNeedShortClassAsServiceType.invoke(EduDealUtils.class, dealCtx);

        // assert
        assertTrue("应当返回 true 当服务类型在 NOT_SHOW_SHORT_CLASS_SERVICE_TYPE 集合中", result);
    }

    /**
     * 测试service的三级分类在 NOT_SHOW_SHORT_CLASS_SERVICE_TYPE 集合中
     */
    @Test
    public void testNotNeedShortClassAsServiceType_ServiceTypeInListAndHasChild() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Method notNeedShortClassAsServiceType = EduDealUtils.class.getDeclaredMethod("notNeedShortClassAsServiceType", DealCtx.class);
        notNeedShortClassAsServiceType.setAccessible(true);
        // arrange
        List<AttributeDTO> attributeDTOS = new ArrayList<>();
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("service_type");
        attributeDTO.setValue(Lists.newArrayList("绘画培训-入门课"));
        attributeDTOS.addAll(Lists.newArrayList(attributeDTO));
        when(dealCtx.getAttrs()).thenReturn(attributeDTOS);

        // act
        boolean result = (boolean)notNeedShortClassAsServiceType.invoke(EduDealUtils.class, dealCtx);

        // assert
        assertTrue("应当返回 true 当服务类型在 NOT_SHOW_SHORT_CLASS_SERVICE_TYPE 集合中", result);
    }
}
