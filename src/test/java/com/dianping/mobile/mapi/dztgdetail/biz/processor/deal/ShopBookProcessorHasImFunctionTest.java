package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.publishcategory.dto.ChannelDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.SwitchHelper;
import java.lang.reflect.Method;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

public class ShopBookProcessorHasImFunctionTest {

    private ShopBookProcessor shopBookProcessor;

    @Mock
    private DealGroupChannelDTO dealGroupChannelDTO;

    @Mock
    private ChannelDTO channelDTO;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        shopBookProcessor = new ShopBookProcessor();
    }

    // Helper method to invoke the private hasImFunction method using reflection
    private boolean invokePrivateHasImFunction(DealCtx ctx) throws Exception {
        Method method = ShopBookProcessor.class.getDeclaredMethod("hasImFunction", DealCtx.class);
        method.setAccessible(true);
        return (boolean) method.invoke(shopBookProcessor, dealCtx);
    }

    /**
     * 测试场景1：ctx.getChannelDTO() 为 null，直接返回 false
     */
    @Test
    public void testHasImFunctionWhenChannelDTOIsNull() throws Throwable {
        // arrange
        when(dealCtx.getChannelDTO()).thenReturn(null);
        // act
        boolean result = invokePrivateHasImFunction(dealCtx);
        // assert
        assertFalse(result);
    }

    /**
     * 测试场景4：ctx.getChannelDTO() 不为 null，且 SwitchHelper.isIm 返回 false，且 SwitchHelper.isCategoryIm 返回 false，返回 false
     */
    @Test
    public void testHasImFunctionWhenBothSwitchHelpersReturnFalse() throws Throwable {
        // arrange
        when(dealCtx.getChannelDTO()).thenReturn(dealGroupChannelDTO);
        when(dealGroupChannelDTO.getChannelDTO()).thenReturn(channelDTO);
        when(channelDTO.getChannelId()).thenReturn(1);
        when(dealGroupChannelDTO.getCategoryId()).thenReturn(2);
        // Simulate the behavior of SwitchHelper.isIm to return false for channelId = 1 and SwitchHelper.isCategoryIm to return false for categoryId = 2
        // Since we cannot mock static methods, we assume the method returns false for both channelId = 1 and categoryId = 2
        // act
        boolean result = invokePrivateHasImFunction(dealCtx);
        // assert
        assertFalse(result);
    }

    @Mock
    private DealCtx dealCtx;
}
