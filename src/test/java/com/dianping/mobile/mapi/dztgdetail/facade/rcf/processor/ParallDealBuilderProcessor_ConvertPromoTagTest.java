package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import com.dianping.lion.client.Lion;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dealuser.price.display.api.model.PromoIdentity;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Constructor;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mockStatic;

@RunWith(MockitoJUnitRunner.class)
public class ParallDealBuilderProcessor_ConvertPromoTagTest {

    @InjectMocks
    private ParallDealBuilderProcessor processor;

    private MockedStatic<Lion> mockedLion;
    @Before
    public void setUp() {
        mockedLion = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        mockedLion.close();
    }

    private PromoIdentity createPromoIdentity() throws Exception {
        Constructor<PromoIdentity> constructor = PromoIdentity.class.getDeclaredConstructor();
        constructor.setAccessible(true);
        return constructor.newInstance();
    }

    @Test
    public void testConvertPromoTagWhenPromoDtoIsNull() throws Throwable {
        PromoDTO promoDTO = null;
        int categoryId = 0;
        String result = processor.convertPromoTag(promoDTO, categoryId);
        assertEquals(null, result);
    }

    @Test
    public void testConvertPromoTagWhenPromoDtoIdentityIsNull() throws Throwable {
        PromoDTO promoDTO = new PromoDTO();
        int categoryId = 0;
        String result = processor.convertPromoTag(promoDTO, categoryId);
        assertEquals("团购优惠", result);
    }

    @Test
    public void testConvertPromoTagForDiscountSellWithMedicalPromoTagControlDisabled() throws Throwable {
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity promoIdentity = createPromoIdentity();
        promoIdentity.setPromoShowType("DISCOUNT_SELL");
        promoDTO.setIdentity(promoIdentity);
        int categoryId = 0;
        mockedLion.when(() -> Lion.getBoolean("com.dianping.mobile.mapi", "com.sankuai.dzu.tpbase.dztgdetailweb.medical.promo.tag.control", false)).thenReturn(false);
        String result = processor.convertPromoTag(promoDTO, categoryId);
        assertEquals("特惠促销", result);
    }
}
