package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.concurrent.Future;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class AbsWrapper_GetFutureResultTest {

    @Mock
    private Future future;

    /**
     * 测试 getFutureResult 方法，当 serviceFuture 为 null 时
     */
    @Test
    public void testGetFutureResultWhenServiceFutureIsNull() throws Throwable {
        // arrange
        AbsWrapper absWrapper = new AbsWrapper() {
        };
        // act
        Object result = absWrapper.getFutureResult(1, null, Object.class);
        // assert
        assertNull(result);
    }

    /**
     * 测试 getFutureResult 方法，当 serviceFuture 不为 null 时
     */
    @Test
    public void testGetFutureResultWhenServiceFutureIsNotNull() throws Throwable {
        // arrange
        AbsWrapper absWrapper = new AbsWrapper() {
        };
        when(future.get()).thenReturn("test");
        // act
        Object result = absWrapper.getFutureResult(1, future, Object.class);
        // assert
        assertEquals("test", result);
    }

    /**
     * 测试 getFutureResult 方法，当 serviceFuture.get() 抛出异常时
     */
    @Test
    public void testGetFutureResultWhenServiceFutureGetThrowsException() throws Throwable {
        // arrange
        AbsWrapper absWrapper = new AbsWrapper() {
        };
        when(future.get()).thenThrow(new InterruptedException());
        // act
        Object result = absWrapper.getFutureResult(1, future, Object.class);
        // assert
        assertNull(result);
    }
}
