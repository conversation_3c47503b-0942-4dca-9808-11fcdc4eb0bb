package com.dianping.mobile.mapi.dztgdetail.rcf.repository.switcher;

import com.alibaba.fastjson.JSON;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.switcher.dto.DealRcfSwitcherResult;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import java.lang.reflect.Field;
import java.util.HashSet;
import java.util.Set;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

/**
 * DealRcfSwitcherService 测试类
 */
public class DealRcfSwitcherServiceTest {

    @InjectMocks
    private DealRcfSwitcherService dealRcfSwitcherService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        DealRcfSwitcherService.CONFIG_LOCAL_CACHE = mock(DealRcfSwitcherService.DealRcfConfigDTO.class);
    }

    /**
     * 测试客户端类型无效时返回默认配置
     */
    @Test
    public void testGetWithInvalidClientType() {
        // arrange
        when(DealRcfSwitcherService.CONFIG_LOCAL_CACHE.isValidClientType(any())).thenReturn(false);

        // act
        DealRcfSwitcherResult result = dealRcfSwitcherService.get(1L,1L, DztgClientTypeEnum.UNKNOWN, 123,"1.0", "1.0");

        // assert
        assertFalse(result.isRender());
        assertFalse(result.isReport());
    }

    /**
     * 测试应用版本无效时返回默认配置
     */
    @Test
    public void testGetWithInvalidAppVersion() {
        // arrange
        when(DealRcfSwitcherService.CONFIG_LOCAL_CACHE.isValidClientType(any())).thenReturn(true);
        when(DealRcfSwitcherService.CONFIG_LOCAL_CACHE.isValidAppVersion(anyString(), any())).thenReturn(false);

        // act
        DealRcfSwitcherResult result = dealRcfSwitcherService.get(1L,1L, DztgClientTypeEnum.MEITUAN_APP, 123,"1.0", "1.0");

        // assert
        assertFalse(result.isRender());
        assertFalse(result.isReport());
    }

    /**
     * 测试用户在白名单中时返回渲染和报告都为true
     */
    @Test
    public void testGetWithUserInWhiteList() {
        // arrange
        when(DealRcfSwitcherService.CONFIG_LOCAL_CACHE.isValidClientType(any())).thenReturn(true);
        when(DealRcfSwitcherService.CONFIG_LOCAL_CACHE.isValidAppVersion(anyString(), any())).thenReturn(true);
        when(DealRcfSwitcherService.CONFIG_LOCAL_CACHE.isUserInWhiteList(anyLong(), any())).thenReturn(true);

        // act
        DealRcfSwitcherResult result = dealRcfSwitcherService.get(1L,1L, DztgClientTypeEnum.MEITUAN_APP, 123,"1.0", "1.0");

        // assert
//        assertTrue(result.isRender());
        assertFalse(result.isReport());
    }

    /**
     * 测试用户不在白名单中且分类ID对应渲染和报告配置为false
     */
    @Test
    public void testGetWithUserNotInWhiteListAndConfigFalse() {
        // arrange
        when(DealRcfSwitcherService.CONFIG_LOCAL_CACHE.isValidClientType(any())).thenReturn(true);
        when(DealRcfSwitcherService.CONFIG_LOCAL_CACHE.isValidAppVersion(anyString(), any())).thenReturn(true);
        when(DealRcfSwitcherService.CONFIG_LOCAL_CACHE.isUserInWhiteList(anyLong(), any())).thenReturn(false);
        when(DealRcfSwitcherService.CONFIG_LOCAL_CACHE.isRender(anyLong())).thenReturn(false);
        when(DealRcfSwitcherService.CONFIG_LOCAL_CACHE.isReport(anyLong())).thenReturn(false);

        // act
        DealRcfSwitcherResult result = dealRcfSwitcherService.get(1L,1L, DztgClientTypeEnum.MEITUAN_APP, 123,"1.0", "1.0");

        // assert
        assertFalse(result.isRender());
        assertFalse(result.isReport());
    }

    /**
     * 测试用户不在白名单中但分类ID对应渲染和报告配置为true
     */
    @Test
    public void testGetWithUserNotInWhiteListAndConfigTrue() {
        // arrange
        when(DealRcfSwitcherService.CONFIG_LOCAL_CACHE.isValidClientType(any())).thenReturn(true);
        when(DealRcfSwitcherService.CONFIG_LOCAL_CACHE.isValidAppVersion(anyString(), any())).thenReturn(true);
        when(DealRcfSwitcherService.CONFIG_LOCAL_CACHE.isUserInWhiteList(anyLong(), any())).thenReturn(false);
        when(DealRcfSwitcherService.CONFIG_LOCAL_CACHE.isRender(anyLong())).thenReturn(true);
        when(DealRcfSwitcherService.CONFIG_LOCAL_CACHE.isReport(anyLong())).thenReturn(true);

        // act
        DealRcfSwitcherResult result = dealRcfSwitcherService.get(1L,1L, DztgClientTypeEnum.MEITUAN_APP, 123,"1.0", "1.0");

        // assert
//        assertTrue(result.isRender());
        assertFalse(result.isReport());
    }

    @Test
    public void testGetWithUser() {
        // arrange
        when(DealRcfSwitcherService.CONFIG_LOCAL_CACHE.isValidClientType(any())).thenReturn(true);
        when(DealRcfSwitcherService.CONFIG_LOCAL_CACHE.isValidAppVersion(anyString(), any())).thenReturn(true);
        when(DealRcfSwitcherService.CONFIG_LOCAL_CACHE.isUserInWhiteList(anyLong(), any())).thenReturn(true);
        when(DealRcfSwitcherService.CONFIG_LOCAL_CACHE.isRender(anyLong())).thenReturn(true);
        when(DealRcfSwitcherService.CONFIG_LOCAL_CACHE.isReport(anyLong())).thenReturn(true);

        // act
        DealRcfSwitcherResult result = dealRcfSwitcherService.get(1L,1L, DztgClientTypeEnum.MEITUAN_APP, 123,"1.0", "1.0");

        // assert
//        assertTrue(result.isRender());
        assertFalse(result.isReport());
    }

    @Test
    public void testGetWithUserListTrue() {
        // arrange
        String json = "{ \"clientTypeFilter\": [ 1 ], \"appVersionFilter\": { \"1\": \"12.27.200\" }, \"mrnVersionFilter\": { \"1\": \"0.6.1865\" }, \"dealCategoryFilter\": false, \"dealCategoryList\": [ 506,1701 ], \"dealCategoryReportFilter\": true, \"dealCategoryReportList\": [], \"dpUserIdList\": [123], \"mtUserIdList\": [123] }";

        DealRcfSwitcherService.DealRcfConfigDTO CONFIG_LOCAL_CACHE = JSON.parseObject(json, DealRcfSwitcherService.DealRcfConfigDTO.class);
        Class clazz = dealRcfSwitcherService.getClass();
        Field field = null;
        try {
            field = clazz.getDeclaredField("CONFIG_LOCAL_CACHE");
            field.setAccessible(true);
            field.set(dealRcfSwitcherService, CONFIG_LOCAL_CACHE);

        } catch (NoSuchFieldException e) {
        } catch (IllegalAccessException e) {
        }
        // act
        DealRcfSwitcherResult result = dealRcfSwitcherService.get(1L,1L, DztgClientTypeEnum.MEITUAN_APP, 123,"12.29.200", "1.0");

        // assert
        assertTrue(result.isRender());
//        assertFalse(result.isReport());
    }

    @Test
    public void testGetWithUserListFalse() {
        // arrange
        String json = "{ \"clientTypeFilter\": [ 1 ], \"appVersionFilter\": { \"1\": \"12.27.200\" }, \"mrnVersionFilter\": { \"1\": \"0.6.1865\" }, \"dealCategoryFilter\": false, \"dealCategoryList\": [ 506,1701 ], \"dealCategoryReportFilter\": true, \"dealCategoryReportList\": [], \"dpUserIdList\": [123], \"mtUserIdList\": [123] }";

        DealRcfSwitcherService.DealRcfConfigDTO CONFIG_LOCAL_CACHE = JSON.parseObject(json, DealRcfSwitcherService.DealRcfConfigDTO.class);
        Class clazz = dealRcfSwitcherService.getClass();
        Field field = null;
        try {
            field = clazz.getDeclaredField("CONFIG_LOCAL_CACHE");
            field.setAccessible(true);
            field.set(dealRcfSwitcherService, CONFIG_LOCAL_CACHE);

        } catch (NoSuchFieldException e) {
        } catch (IllegalAccessException e) {
        }
        // act
        DealRcfSwitcherResult result = dealRcfSwitcherService.get(1L,1L, DztgClientTypeEnum.MEITUAN_APP, 1234,"12.29.200", "1.0");

        // assert
        assertTrue(result.isRender());
//        assertFalse(result.isReport());
    }

    @Test
    public void testWithBlackUserListTrue() {
        // arrange
        String json = "{ \"clientTypeFilter\": [ 1 ], \"appVersionFilter\": { \"1\": \"12.27.200\" }, \"mrnVersionFilter\": { \"1\": \"0.6.1865\" }, \"dealCategoryFilter\": false, \"dealCategoryList\": [ 506,1701 ], \"dealCategoryReportFilter\": true, \"dealCategoryReportList\": [], \"dpUserIdList\": [123], \"mtUserIdList\": [123] }";
        DealRcfSwitcherService.DealRcfConfigDTO CONFIG_LOCAL_CACHE = JSON.parseObject(json, DealRcfSwitcherService.DealRcfConfigDTO.class);

         Set<Long> blackServiceTypeIdList = new HashSet<Long>();
         blackServiceTypeIdList.add(123L);
         Set<Long> blackDealCategoryList = new HashSet<>();
         blackDealCategoryList.add(123L);  
        CONFIG_LOCAL_CACHE.setBlackDealCategoryFilter(true);
        CONFIG_LOCAL_CACHE.setBlackDealCategoryList(blackDealCategoryList);
        CONFIG_LOCAL_CACHE.setBlackServiceTypeIdFilter(true);
        CONFIG_LOCAL_CACHE.setBlackServiceTypeIdList(blackServiceTypeIdList);
        
        boolean result = CONFIG_LOCAL_CACHE.hitBlackList(0L, 123L);
        result = CONFIG_LOCAL_CACHE.hitBlackList(123L, 0L);
        result = CONFIG_LOCAL_CACHE.hitBlackList(123L, 123L);
        Assert.assertTrue(result);
    }


    @Test
    public void testBlackList() {
        // arrange
        String json = "{ \"clientTypeFilter\": [ 1 ], \"appVersionFilter\": { \"1\": \"12.27.200\" }, \"mrnVersionFilter\": { \"1\": \"0.6.1865\" }, \"dealCategoryFilter\": false, \"dealCategoryList\": [ 506,1701 ], \"dealCategoryReportFilter\": true, \"dealCategoryReportList\": [], \"dpUserIdList\": [123], \"mtUserIdList\": [123] }";

        DealRcfSwitcherService.DealRcfConfigDTO CONFIG_LOCAL_CACHE = JSON.parseObject(json, DealRcfSwitcherService.DealRcfConfigDTO.class);
        CONFIG_LOCAL_CACHE.setBlackDealCategoryFilter(true);
        CONFIG_LOCAL_CACHE.setBlackServiceTypeIdFilter(true);
        Set<Long> blackList = new HashSet<Long>();
        blackList.add(123L);
        CONFIG_LOCAL_CACHE.setBlackServiceTypeIdList(blackList);
        CONFIG_LOCAL_CACHE.setBlackDealCategoryList(blackList);
        Class clazz = dealRcfSwitcherService.getClass();
        Field field = null;
        try {
            field = clazz.getDeclaredField("CONFIG_LOCAL_CACHE");
            field.setAccessible(true);
            field.set(dealRcfSwitcherService, CONFIG_LOCAL_CACHE);

        } catch (NoSuchFieldException e) {
        } catch (IllegalAccessException e) {
        }
        // act
        DealRcfSwitcherResult result = dealRcfSwitcherService.get(123L,123L, DztgClientTypeEnum.MEITUAN_APP, 123,"12.29.200", "1.0");

        // assert
        assertFalse(result.isRender());
    }
}
