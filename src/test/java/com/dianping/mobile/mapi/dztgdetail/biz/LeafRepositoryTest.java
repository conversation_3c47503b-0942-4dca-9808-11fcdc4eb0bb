package com.dianping.mobile.mapi.dztgdetail.biz;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.inf.leaf.thrift.IDGen;
import com.sankuai.inf.leaf.thrift.Result;
import com.sankuai.inf.leaf.thrift.Status;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class LeafRepositoryTest {

    @InjectMocks
    private LeafRepository leafRepository;

    @Mock
    private IDGen.Iface idGen;

    private Result successResult;

    private Result failResult;

    private void setUpSuccessResult() {
        successResult = new Result();
        successResult.setStatus(Status.SUCCESS);
        successResult.setId(1L);
    }

    private void setUpFailResult() {
        failResult = new Result();
        failResult.setStatus(Status.EXCEPTION);
        failResult.setId(2L);
    }

    @Test
    public void testBatchGenFinancialConsumeSerialId_BatchNumOverMax() throws Throwable {
        int batchNum = 101;
        List<Long> result = leafRepository.batchGenFinancialConsumeSerialId(batchNum);
        assertNull(result);
    }

    @Test
    public void testBatchGenFinancialConsumeSerialId_TimeoutException() throws Throwable {
        int batchNum = 10;
        when(idGen.getSnowFlakeBatch("nibmkt.couponcomponent.financialconsumeserialno", batchNum)).thenThrow(new RuntimeException("timeout"));
        List<Long> result = leafRepository.batchGenFinancialConsumeSerialId(batchNum);
        assertNull(result);
    }

    @Test
    public void testBatchGenFinancialConsumeSerialId_NonTimeoutException() throws Throwable {
        int batchNum = 10;
        when(idGen.getSnowFlakeBatch("nibmkt.couponcomponent.financialconsumeserialno", batchNum)).thenThrow(new RuntimeException("non-timeout"));
        List<Long> result = leafRepository.batchGenFinancialConsumeSerialId(batchNum);
        assertNull(result);
    }

    @Test
    public void testBatchGenFinancialConsumeSerialId_NotEnoughLeafId() throws Throwable {
        int batchNum = 10;
        Result successResult = new Result();
        successResult.setStatus(Status.SUCCESS);
        successResult.setId(123L);
        Result failResult = new Result();
        failResult.setStatus(Status.EXCEPTION);
        when(idGen.getSnowFlakeBatch("nibmkt.couponcomponent.financialconsumeserialno", batchNum)).thenReturn(Arrays.asList(successResult, failResult));
        List<Long> result = leafRepository.batchGenFinancialConsumeSerialId(batchNum);
        assertNull(result);
    }

    @Test
    public void testBatchGenFinancialConsumeSerialId_Success() throws Throwable {
        // 修改为2，因为我们要生成两个ID
        int batchNum = 2;
        Result successResult = new Result();
        successResult.setStatus(Status.SUCCESS);
        successResult.setId(123L);
        // Correctly simulate the behavior of idGen.getSnowFlakeBatch
        when(idGen.getSnowFlakeBatch("nibmkt.couponcomponent.financialconsumeserialno", batchNum)).thenReturn(Arrays.asList(successResult, successResult));
        List<Long> result = leafRepository.batchGenFinancialConsumeSerialId(batchNum);
        assertEquals(Arrays.asList(123L, 123L), result);
    }

    @Test
    public void testNextFinancialConsumeSerialIdSuccess() throws Throwable {
        setUpSuccessResult();
        when(idGen.getSnowFlake(anyString())).thenReturn(successResult);
        Long result = leafRepository.nextFinancialConsumeSerialId();
        assertEquals(Long.valueOf(1L), result);
    }

    @Test
    public void testNextFinancialConsumeSerialIdFail() throws Throwable {
        setUpFailResult();
        when(idGen.getSnowFlake(anyString())).thenThrow(new RuntimeException("leaf id生成异常, 异常码:" + failResult.getId()));
        Long result = leafRepository.nextFinancialConsumeSerialId();
        assertNull(result);
    }

    @Test
    public void testNextFinancialConsumeSerialIdExceptionNotTimeout() throws Throwable {
        when(idGen.getSnowFlake(anyString())).thenThrow(new RuntimeException());
        Long result = leafRepository.nextFinancialConsumeSerialId();
        assertNull(result);
    }

    @Test
    public void testNextFinancialConsumeSerialIdExceptionTimeout() throws Throwable {
        when(idGen.getSnowFlake(anyString())).thenThrow(new RuntimeException("timeout"));
        Long result = leafRepository.nextFinancialConsumeSerialId();
        assertNull(result);
    }

    @Test
    public void testNextFinancialConsumeSerialIdExceptionTimeoutMaxRetry() throws Throwable {
        when(idGen.getSnowFlake(anyString())).thenThrow(new RuntimeException("timeout"));
        Long result = leafRepository.nextFinancialConsumeSerialId();
        assertNull(result);
    }
}
