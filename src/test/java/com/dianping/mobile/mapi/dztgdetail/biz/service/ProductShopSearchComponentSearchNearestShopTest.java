package com.dianping.mobile.mapi.dztgdetail.biz.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;

import com.dianping.general.unified.search.api.productshopsearch.GeneralProductShopSearchService;
import com.dianping.general.unified.search.api.productshopsearch.enums.ProductShopCollapseFieldEnum;
import com.dianping.general.unified.search.api.productshopsearch.enums.ProductShopCollapseTypeEnum;
import com.dianping.general.unified.search.api.productshopsearch.enums.ProductShopRetrievalExtendFieldEnum;
import com.dianping.general.unified.search.api.productshopsearch.enums.ProductShopSortFieldEnum;
import com.dianping.general.unified.search.api.productshopsearch.enums.ProductShopSortOrderEnum;
import com.dianping.general.unified.search.api.productshopsearch.request.GeneralProductShopSearchRequest;
import com.dianping.general.unified.search.api.productshopsearch.response.GeneralProductShopSearchResponse;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperCacheWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.RelatedRecommendCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedRecommendReq;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ProductShopSearchComponentSearchNearestShopTest {

    @Mock
    private MapperCacheWrapper mapperCacheWrapper;

    @Mock
    private GeneralProductShopSearchService generalProductShopSearchService;

    @InjectMocks
    private ProductShopSearchComponent productShopSearchComponent;

    private RelatedRecommendCtx ctx;

    private RelatedRecommendReq req;

    @Mock
    private EnvCtx mockEnvCtx;

    @Before
    public void setUp() {
        req = new RelatedRecommendReq();
        ctx = new RelatedRecommendCtx();
        ctx.setReq(req);
        ctx.setEnvCtx(mockEnvCtx);
    }

    /**
     * Test when dealIds is empty should return null
     */
    @Test
    public void testSearchNearestShopEmptyDealIds() throws Throwable {
        // arrange
        List<Long> dealIds = Collections.emptyList();
        // act
        GeneralProductShopSearchResponse result = productShopSearchComponent.searchNearestShop4DP(ctx, dealIds);
        // assert
        assertNull(result);
        verify(generalProductShopSearchService, never()).searchProductShops(any());
    }

    /**
     * Test when ctx is null should return null
     */
    @Test
    public void testSearchNearestShopNullCtx() throws Throwable {
        // arrange
        List<Long> dealIds = Arrays.asList(1L, 2L);
        // act
        GeneralProductShopSearchResponse result = productShopSearchComponent.searchNearestShop4DP(null, dealIds);
        // assert
        assertNull(result);
        verify(generalProductShopSearchService, never()).searchProductShops(any());
    }

    /**
     * Test when both lat and lng are valid should set location info
     */
    @Test
    public void testSearchNearestShopWithValidLatLng() throws Throwable {
        // arrange
        List<Long> dealIds = Arrays.asList(1L, 2L);
        req.setUserLat(39.9042);
        req.setUserLng(116.4074);
        GeneralProductShopSearchResponse mockResponse = new GeneralProductShopSearchResponse();
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(mockResponse);
        // act
        GeneralProductShopSearchResponse result = productShopSearchComponent.searchNearestShop4DP(ctx, dealIds);
        // assert
        assertNotNull(result);
        verify(generalProductShopSearchService).searchProductShops(any());
        // Verify location info was set correctly
        ArgumentCaptor<GeneralProductShopSearchRequest> requestCaptor = ArgumentCaptor.forClass(GeneralProductShopSearchRequest.class);
        verify(generalProductShopSearchService).searchProductShops(requestCaptor.capture());
        GeneralProductShopSearchRequest capturedRequest = requestCaptor.getValue();
        assertNotNull(capturedRequest.getLocationInfo());
        assertEquals(BigDecimal.valueOf(39.9042), capturedRequest.getLocationInfo().getLat());
        assertEquals(BigDecimal.valueOf(116.4074), capturedRequest.getLocationInfo().getLng());
    }

    /**
     * Test when cityId is provided should set cityIds in request
     */
    @Test
    public void testSearchNearestShopWithCityId() throws Throwable {
        // arrange
        List<Long> dealIds = Arrays.asList(1L, 2L);
        req.setCityId(10);
        // Mock non-MT environment
        when(mockEnvCtx.isMt()).thenReturn(false);
        GeneralProductShopSearchResponse mockResponse = new GeneralProductShopSearchResponse();
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(mockResponse);
        // act
        GeneralProductShopSearchResponse result = productShopSearchComponent.searchNearestShop4DP(ctx, dealIds);
        // assert
        assertNotNull(result);
        verify(generalProductShopSearchService).searchProductShops(any());
        // Verify cityId was set correctly
        ArgumentCaptor<GeneralProductShopSearchRequest> requestCaptor = ArgumentCaptor.forClass(GeneralProductShopSearchRequest.class);
        verify(generalProductShopSearchService).searchProductShops(requestCaptor.capture());
        GeneralProductShopSearchRequest capturedRequest = requestCaptor.getValue();
        assertNotNull(capturedRequest.getBaseSearchOption().getCityIds());
        assertEquals(1, capturedRequest.getBaseSearchOption().getCityIds().size());
        assertEquals(Long.valueOf(10), capturedRequest.getBaseSearchOption().getCityIds().get(0));
    }

    /**
     * Test when MT cityId is provided should map to DP cityId
     */
    @Test
    public void testSearchNearestShopWithMtCityId() throws Throwable {
        // arrange
        List<Long> dealIds = Arrays.asList(1L, 2L);
        req.setCityId(10);
        // Mock MT environment
        when(mockEnvCtx.isMt()).thenReturn(true);
        when(mapperCacheWrapper.fetchDpCityId(10)).thenReturn(20);
        GeneralProductShopSearchResponse mockResponse = new GeneralProductShopSearchResponse();
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(mockResponse);
        // act
        GeneralProductShopSearchResponse result = productShopSearchComponent.searchNearestShop4DP(ctx, dealIds);
        // assert
        assertNotNull(result);
        verify(mapperCacheWrapper).fetchDpCityId(10);
        verify(generalProductShopSearchService).searchProductShops(any());
        // Verify mapped cityId was set correctly
        ArgumentCaptor<GeneralProductShopSearchRequest> requestCaptor = ArgumentCaptor.forClass(GeneralProductShopSearchRequest.class);
        verify(generalProductShopSearchService).searchProductShops(requestCaptor.capture());
        GeneralProductShopSearchRequest capturedRequest = requestCaptor.getValue();
        assertNotNull(capturedRequest.getBaseSearchOption().getCityIds());
        assertEquals(1, capturedRequest.getBaseSearchOption().getCityIds().size());
        assertEquals(Long.valueOf(20), capturedRequest.getBaseSearchOption().getCityIds().get(0));
    }

    /**
     * Test when service throws exception should return null and log error
     */
    @Test
    public void testSearchNearestShopServiceException() throws Throwable {
        // arrange
        List<Long> dealIds = Arrays.asList(1L, 2L);
        req.setUserLat(39.9042);
        req.setUserLng(116.4074);
        when(generalProductShopSearchService.searchProductShops(any())).thenThrow(new RuntimeException("Test exception"));
        // act
        GeneralProductShopSearchResponse result = productShopSearchComponent.searchNearestShop4DP(ctx, dealIds);
        // assert
        assertNull(result);
        verify(generalProductShopSearchService).searchProductShops(any());
    }

    /**
     * Test when both lat/lng and cityId are provided should prefer lat/lng
     */
    @Test
    public void testSearchNearestShopPreferLatLngOverCityId() throws Throwable {
        // arrange
        List<Long> dealIds = Arrays.asList(1L, 2L);
        req.setUserLat(39.9042);
        req.setUserLng(116.4074);
        req.setCityId(10);
        GeneralProductShopSearchResponse mockResponse = new GeneralProductShopSearchResponse();
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(mockResponse);
        // act
        GeneralProductShopSearchResponse result = productShopSearchComponent.searchNearestShop4DP(ctx, dealIds);
        // assert
        assertNotNull(result);
        verify(generalProductShopSearchService).searchProductShops(any());
        verify(mapperCacheWrapper, never()).fetchDpCityId(anyInt());
        // Verify location info was set correctly and cityId was not used
        ArgumentCaptor<GeneralProductShopSearchRequest> requestCaptor = ArgumentCaptor.forClass(GeneralProductShopSearchRequest.class);
        verify(generalProductShopSearchService).searchProductShops(requestCaptor.capture());
        GeneralProductShopSearchRequest capturedRequest = requestCaptor.getValue();
        assertNotNull(capturedRequest.getLocationInfo());
        assertEquals(BigDecimal.valueOf(39.9042), capturedRequest.getLocationInfo().getLat());
        assertEquals(BigDecimal.valueOf(116.4074), capturedRequest.getLocationInfo().getLng());
    }

    /**
     * Test when lat is zero should use cityId instead
     */
    @Test
    public void testSearchNearestShopZeroLatUsesCityId() throws Throwable {
        // arrange
        List<Long> dealIds = Arrays.asList(1L, 2L);
        req.setUserLat(0.0);
        req.setUserLng(116.4074);
        req.setCityId(10);
        // Mock non-MT environment
        when(mockEnvCtx.isMt()).thenReturn(false);
        GeneralProductShopSearchResponse mockResponse = new GeneralProductShopSearchResponse();
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(mockResponse);
        // act
        GeneralProductShopSearchResponse result = productShopSearchComponent.searchNearestShop4DP(ctx, dealIds);
        // assert
        assertNotNull(result);
        verify(generalProductShopSearchService).searchProductShops(any());
        // Verify cityId was used instead of location
        ArgumentCaptor<GeneralProductShopSearchRequest> requestCaptor = ArgumentCaptor.forClass(GeneralProductShopSearchRequest.class);
        verify(generalProductShopSearchService).searchProductShops(requestCaptor.capture());
        GeneralProductShopSearchRequest capturedRequest = requestCaptor.getValue();
        assertNull(capturedRequest.getLocationInfo());
        assertNotNull(capturedRequest.getBaseSearchOption().getCityIds());
        assertEquals(1, capturedRequest.getBaseSearchOption().getCityIds().size());
        assertEquals(Long.valueOf(10), capturedRequest.getBaseSearchOption().getCityIds().get(0));
    }

    /**
     * Test when lng is zero should use cityId instead
     */
    @Test
    public void testSearchNearestShopZeroLngUsesCityId() throws Throwable {
        // arrange
        List<Long> dealIds = Arrays.asList(1L, 2L);
        req.setUserLat(39.9042);
        req.setUserLng(0.0);
        req.setCityId(10);
        // Mock non-MT environment
        when(mockEnvCtx.isMt()).thenReturn(false);
        GeneralProductShopSearchResponse mockResponse = new GeneralProductShopSearchResponse();
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(mockResponse);
        // act
        GeneralProductShopSearchResponse result = productShopSearchComponent.searchNearestShop4DP(ctx, dealIds);
        // assert
        assertNotNull(result);
        verify(generalProductShopSearchService).searchProductShops(any());
        // Verify cityId was used instead of location
        ArgumentCaptor<GeneralProductShopSearchRequest> requestCaptor = ArgumentCaptor.forClass(GeneralProductShopSearchRequest.class);
        verify(generalProductShopSearchService).searchProductShops(requestCaptor.capture());
        GeneralProductShopSearchRequest capturedRequest = requestCaptor.getValue();
        assertNull(capturedRequest.getLocationInfo());
        assertNotNull(capturedRequest.getBaseSearchOption().getCityIds());
        assertEquals(1, capturedRequest.getBaseSearchOption().getCityIds().size());
        assertEquals(Long.valueOf(10), capturedRequest.getBaseSearchOption().getCityIds().get(0));
    }

    /**
     * Test when cityId mapping returns zero should not set cityIds
     */
    @Test
    public void testSearchNearestShopZeroMappedCityId() throws Throwable {
        // arrange
        List<Long> dealIds = Arrays.asList(1L, 2L);
        req.setCityId(10);
        // Mock MT environment
        when(mockEnvCtx.isMt()).thenReturn(true);
        when(mapperCacheWrapper.fetchDpCityId(10)).thenReturn(0);
        GeneralProductShopSearchResponse mockResponse = new GeneralProductShopSearchResponse();
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(mockResponse);
        // act
        GeneralProductShopSearchResponse result = productShopSearchComponent.searchNearestShop4DP(ctx, dealIds);
        // assert
        assertNotNull(result);
        verify(mapperCacheWrapper).fetchDpCityId(10);
        verify(generalProductShopSearchService).searchProductShops(any());
        // Verify cityId was not set due to zero mapping
        ArgumentCaptor<GeneralProductShopSearchRequest> requestCaptor = ArgumentCaptor.forClass(GeneralProductShopSearchRequest.class);
        verify(generalProductShopSearchService).searchProductShops(requestCaptor.capture());
        GeneralProductShopSearchRequest capturedRequest = requestCaptor.getValue();
        // The cityIds list should not be set or should be empty
        assertTrue(capturedRequest.getBaseSearchOption().getCityIds() == null || capturedRequest.getBaseSearchOption().getCityIds().isEmpty());
    }

    /**
     * Test with single dealId should set correct page size
     */
    @Test
    public void testSearchNearestShopSingleDealId() throws Throwable {
        // arrange
        List<Long> dealIds = Collections.singletonList(1L);
        req.setUserLat(39.9042);
        req.setUserLng(116.4074);
        GeneralProductShopSearchResponse mockResponse = new GeneralProductShopSearchResponse();
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(mockResponse);
        // act
        GeneralProductShopSearchResponse result = productShopSearchComponent.searchNearestShop4DP(ctx, dealIds);
        // assert
        assertNotNull(result);
        verify(generalProductShopSearchService).searchProductShops(any());
        // Verify page size was set to dealIds size
        ArgumentCaptor<GeneralProductShopSearchRequest> requestCaptor = ArgumentCaptor.forClass(GeneralProductShopSearchRequest.class);
        verify(generalProductShopSearchService).searchProductShops(requestCaptor.capture());
        GeneralProductShopSearchRequest capturedRequest = requestCaptor.getValue();
        assertEquals(1, capturedRequest.getPageOption().getPageSize());
    }

    /**
     * Test that collapse option is set correctly to ensure one record per deal
     */
    @Test
    public void testSearchNearestShopCollapseOption() throws Throwable {
        // arrange
        List<Long> dealIds = Arrays.asList(1L, 2L);
        req.setUserLat(39.9042);
        req.setUserLng(116.4074);
        GeneralProductShopSearchResponse mockResponse = new GeneralProductShopSearchResponse();
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(mockResponse);
        // act
        GeneralProductShopSearchResponse result = productShopSearchComponent.searchNearestShop4DP(ctx, dealIds);
        // assert
        assertNotNull(result);
        // Verify collapse option was set correctly
        ArgumentCaptor<GeneralProductShopSearchRequest> requestCaptor = ArgumentCaptor.forClass(GeneralProductShopSearchRequest.class);
        verify(generalProductShopSearchService).searchProductShops(requestCaptor.capture());
        GeneralProductShopSearchRequest capturedRequest = requestCaptor.getValue();
        assertNotNull(capturedRequest.getCollapseOption());
        assertNotNull(capturedRequest.getCollapseOption().getCollapseUnits());
        assertEquals(1, capturedRequest.getCollapseOption().getCollapseUnits().size());
        assertEquals(ProductShopCollapseFieldEnum.PRODUCT_ID.getCode(), capturedRequest.getCollapseOption().getCollapseUnits().get(0).getCollapseField());
        assertEquals(ProductShopCollapseTypeEnum.SORTED_FIELD_FIRST_RECORD, capturedRequest.getCollapseOption().getCollapseUnits().get(0).getCollapseType());
    }

    /**
     * Test that sort option is set correctly to sort by distance
     */
    @Test
    public void testSearchNearestShopSortOption() throws Throwable {
        // arrange
        List<Long> dealIds = Arrays.asList(1L, 2L);
        req.setUserLat(39.9042);
        req.setUserLng(116.4074);
        GeneralProductShopSearchResponse mockResponse = new GeneralProductShopSearchResponse();
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(mockResponse);
        // act
        GeneralProductShopSearchResponse result = productShopSearchComponent.searchNearestShop4DP(ctx, dealIds);
        // assert
        assertNotNull(result);
        // Verify sort option was set correctly
        ArgumentCaptor<GeneralProductShopSearchRequest> requestCaptor = ArgumentCaptor.forClass(GeneralProductShopSearchRequest.class);
        verify(generalProductShopSearchService).searchProductShops(requestCaptor.capture());
        GeneralProductShopSearchRequest capturedRequest = requestCaptor.getValue();
        assertNotNull(capturedRequest.getSortOption());
        assertNotNull(capturedRequest.getSortOption().getSortUnits());
        assertEquals(2, capturedRequest.getSortOption().getSortUnits().size());
        assertEquals(ProductShopSortFieldEnum.SHOP_DISTANCE.getCode(), capturedRequest.getSortOption().getSortUnits().get(0).getSortField());
        assertEquals(ProductShopSortOrderEnum.ASC, capturedRequest.getSortOption().getSortUnits().get(0).getSortOrder());
    }

    /**
     * Test that retrieval option is set correctly to include required fields
     */
    @Test
    public void testSearchNearestShopRetrievalOption() throws Throwable {
        // arrange
        List<Long> dealIds = Arrays.asList(1L, 2L);
        req.setUserLat(39.9042);
        req.setUserLng(116.4074);
        GeneralProductShopSearchResponse mockResponse = new GeneralProductShopSearchResponse();
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(mockResponse);
        // act
        GeneralProductShopSearchResponse result = productShopSearchComponent.searchNearestShop4DP(ctx, dealIds);
        // assert
        assertNotNull(result);
        // Verify retrieval option was set correctly
        ArgumentCaptor<GeneralProductShopSearchRequest> requestCaptor = ArgumentCaptor.forClass(GeneralProductShopSearchRequest.class);
        verify(generalProductShopSearchService).searchProductShops(requestCaptor.capture());
        GeneralProductShopSearchRequest capturedRequest = requestCaptor.getValue();
        assertNotNull(capturedRequest.getRetrievalOption());
        assertNotNull(capturedRequest.getRetrievalOption().getRetrievalUnits());
        assertEquals(3, capturedRequest.getRetrievalOption().getRetrievalUnits().size());
        List<String> retrievalFields = capturedRequest.getRetrievalOption().getRetrievalUnits().stream().map(unit -> unit.getRetrievalExtendField()).collect(Collectors.toList());
        assertTrue(retrievalFields.contains(ProductShopRetrievalExtendFieldEnum.SHOP_DISTANCE_IN_METER.getCode()));
        assertTrue(retrievalFields.contains(ProductShopRetrievalExtendFieldEnum.SHOP_CITY_ID.getCode()));
        assertTrue(retrievalFields.contains(ProductShopRetrievalExtendFieldEnum.SHOP_NAME.getCode()));
    }
}
