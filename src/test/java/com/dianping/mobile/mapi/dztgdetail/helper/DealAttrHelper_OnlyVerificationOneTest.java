package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import java.util.Arrays;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealAttrHelper_OnlyVerificationOneTest {

    /**
     * 测试 onlyVerificationOne 方法，当 attrs 为空的情况
     */
    @Test
    public void testOnlyVerificationOneWhenAttrsIsNull() throws Throwable {
        // arrange
        // act
        boolean result = DealAttrHelper.onlyVerificationOne(null);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 onlyVerificationOne 方法，当 attrs 不为空，但没有 DealAttrKeys.SINGLE_VERIFICATION_QUANTITY_DESC 对应的属性值的情况
     */
    @Test
    public void testOnlyVerificationOneWhenAttrsIsNotEmptyAndNoSingleVerificationQuantityDesc() throws Throwable {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("other");
        attrDTO.setValue(Arrays.asList("other value"));
        // act
        boolean result = DealAttrHelper.onlyVerificationOne(Collections.singletonList(attrDTO));
        // assert
        assertFalse(result);
    }

    /**
     * 测试 onlyVerificationOne 方法，当 attrs 不为空，有 DealAttrKeys.SINGLE_VERIFICATION_QUANTITY_DESC 对应的属性值，但该值不等于 "单次到店仅可核销一次，仅能一人使用" 的情况
     */
    @Test
    public void testOnlyVerificationOneWhenAttrsIsNotEmptyAndHasSingleVerificationQuantityDescAndValueIsNotEqual() throws Throwable {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(DealAttrKeys.SINGLE_VERIFICATION_QUANTITY_DESC);
        attrDTO.setValue(Arrays.asList("other value"));
        // act
        boolean result = DealAttrHelper.onlyVerificationOne(Collections.singletonList(attrDTO));
        // assert
        assertFalse(result);
    }

    /**
     * 测试 onlyVerificationOne 方法，当 attrs 不为空，有 DealAttrKeys.SINGLE_VERIFICATION_QUANTITY_DESC 对应的属性值，且该值等于 "单次到店仅可核销一次，仅能一人使用" 的情况
     */
    @Test
    public void testOnlyVerificationOneWhenAttrsIsNotEmptyAndHasSingleVerificationQuantityDescAndValueIsEqual() throws Throwable {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(DealAttrKeys.SINGLE_VERIFICATION_QUANTITY_DESC);
        attrDTO.setValue(Arrays.asList("单次到店仅可核销一次，仅能一人使用"));
        // act
        boolean result = DealAttrHelper.onlyVerificationOne(Collections.singletonList(attrDTO));
        // assert
        assertTrue(result);
    }

    /**
     * 测试 isVoucherV2 方法，当 attrs 为 null 时
     */
    @Test
    public void testIsVoucherV2WhenAttrsIsNull() throws Throwable {
        // arrange
        // Adjusted to use an empty list instead of null
        // act
        boolean result = DealAttrHelper.isVoucherV2(Collections.emptyList());
        // assert
        assertFalse(result);
    }

    /**
     * 测试 isVoucherV2 方法，当 attrs 不包含 VOUCHER 属性时
     */
    @Test
    public void testIsVoucherV2WhenAttrsNotContainsVoucher() throws Throwable {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("other");
        // act
        boolean result = DealAttrHelper.isVoucherV2(Arrays.asList(attrDTO));
        // assert
        assertFalse(result);
    }

    /**
     * 测试 isVoucherV2 方法，当 attrs 包含 VOUCHER 属性，但是该属性的值不为 VOUCHER_VALUE 时
     */
    @Test
    public void testIsVoucherV2WhenAttrsContainsVoucherButNotVoucherValue() throws Throwable {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(DealAttrKeys.VOUCHER);
        attrDTO.setValue(Arrays.asList("other"));
        // act
        boolean result = DealAttrHelper.isVoucherV2(Arrays.asList(attrDTO));
        // assert
        assertFalse(result);
    }

    /**
     * 测试 isVoucherV2 方法，当 attrs 包含 VOUCHER 属性，并且该属性的值为 VOUCHER_VALUE 时
     */
    @Test
    public void testIsVoucherV2WhenAttrsContainsVoucherAndVoucherValue() throws Throwable {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(DealAttrKeys.VOUCHER);
        attrDTO.setValue(Arrays.asList(DealAttrKeys.VOUCHER_VALUE));
        // act
        boolean result = DealAttrHelper.isVoucherV2(Arrays.asList(attrDTO));
        // assert
        assertTrue(result);
    }
}
