package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.google.common.util.concurrent.SettableFuture;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PoiShopCategoryWrapper_QueryDpPoiDtoTest {

    @InjectMocks
    private PoiShopCategoryWrapper poiShopCategoryWrapper;

    @Mock
    private SettableFuture<List<DpPoiDTO>> future;

    private DpPoiDTO dpPoiDTO;

    @Before
    public void setUp() {
        dpPoiDTO = new DpPoiDTO();
    }

    /**
     * 测试 SettableFuture 对象为 null 的情况
     */
    @Test
    public void testQueryDpPoiDtoFutureIsNull() throws Throwable {
        assertNull(poiShopCategoryWrapper.queryDpPoiDto(null));
    }

    /**
     * 测试 SettableFuture 对象非 null，但获取的结果为空的情况
     */
    @Test
    public void testQueryDpPoiDtoFutureIsNotNullButResultIsEmpty() throws Throwable {
        when(future.get()).thenReturn(null);
        assertNull(poiShopCategoryWrapper.queryDpPoiDto(future));
    }

    /**
     * 测试 SettableFuture 对象非 null，获取的结果非空的情况
     */
    @Test
    public void testQueryDpPoiDtoFutureIsNotNullAndResultIsNotEmpty() throws Throwable {
        List<DpPoiDTO> dpPoiDTOList = Arrays.asList(dpPoiDTO);
        when(future.get()).thenReturn(dpPoiDTOList);
        assertEquals(dpPoiDTO, poiShopCategoryWrapper.queryDpPoiDto(future));
    }
}
