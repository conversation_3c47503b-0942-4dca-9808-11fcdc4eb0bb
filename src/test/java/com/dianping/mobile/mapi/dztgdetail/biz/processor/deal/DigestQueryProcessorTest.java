package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DigestQueryWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import java.util.concurrent.Future;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class DigestQueryProcessorTest {

    @InjectMocks
    private DigestQueryProcessor digestQueryProcessor;

    @Mock
    private DigestQueryWrapper digestQueryWrapper;

    @Mock
    private DealCtx ctx;

    @Mock
    private FutureCtx futureCtx;

    @Mock
    private Future<?> future;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
    }

    /**
     * 测试正常场景：digestQueryWrapper.getDarenVideosFuture(ctx) 成功返回 Future 对象，
     * 并且 ctx.getFutureCtx().setDigestFuture(future) 成功执行。
     */
    @Test
    public void testPrepareNormalCase() throws Throwable {
        // arrange
        when(digestQueryWrapper.getDarenVideosFuture(ctx)).thenReturn(future);
        // act
        digestQueryProcessor.prepare(ctx);
        // assert
        verify(digestQueryWrapper).getDarenVideosFuture(ctx);
        verify(futureCtx).setDigestFuture(future);
    }

    /**
     * 测试异常场景：digestQueryWrapper.getDarenVideosFuture(ctx) 抛出异常。
     */
    @Test(expected = Exception.class)
    public void testPrepareExceptionCase() throws Throwable {
        // arrange
        when(digestQueryWrapper.getDarenVideosFuture(ctx)).thenThrow(new Exception("Mocked exception"));
        // act
        digestQueryProcessor.prepare(ctx);
        // assert
        // 期望抛出异常
    }

    /**
     * 测试异常场景：ctx.getFutureCtx().setDigestFuture(future) 抛出异常。
     */
    @Test(expected = Exception.class)
    public void testPrepareSetFutureExceptionCase() throws Throwable {
        // arrange
        when(digestQueryWrapper.getDarenVideosFuture(ctx)).thenReturn(future);
        doThrow(new Exception("Mocked exception")).when(futureCtx).setDigestFuture(future);
        // act
        digestQueryProcessor.prepare(ctx);
        // assert
        // 期望抛出异常
    }
}
