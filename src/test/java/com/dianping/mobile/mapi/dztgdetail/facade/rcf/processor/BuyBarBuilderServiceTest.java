package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.gmkt.activity.api.enums.PriceStrengthTimeEnum;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.PriceContext;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PurchaseMessageRemindInfo;
import com.dianping.mobile.mapi.dztgdetail.entity.PurchaseMessageConfig;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.BuyBarBuilderService;
import com.dianping.mobile.mapi.dztgdetail.helper.NewBuyBarHelper;
import com.dianping.mobile.mapi.dztgdetail.util.JsonUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.buy.DealGroupBuyRuleDTO;
import com.sankuai.swan.udqs.api.*;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum.MEITUAN_APP;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * @Author: <EMAIL>
 * @Date: 2024/6/10
 */
@RunWith(MockitoJUnitRunner.class)
public class BuyBarBuilderServiceTest {
    @InjectMocks
    private BuyBarBuilderService buyBarBuilderService;
    @Mock
    private SwanQueryService swanQueryService;

    private MockedStatic<Lion> lionMockedStatic;
    private MockedStatic<LionConfigUtils> lionConfigUtilsMocked;
    private MockedStatic<NewBuyBarHelper> newBuyBarHelperMockedStatic;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        buyBarBuilderService = new BuyBarBuilderService();
        lionConfigUtilsMocked = mockStatic(LionConfigUtils.class);
        lionMockedStatic = mockStatic(Lion.class);
        newBuyBarHelperMockedStatic = mockStatic(NewBuyBarHelper.class);
    }

    @After
    public void tearDown() {
        lionConfigUtilsMocked.close();
        lionMockedStatic.close();
        newBuyBarHelperMockedStatic.close();
    }

    @Test
    public void testBuildBuyBarDealActivityQueryFailure() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx dealCtxMock = mock(DealCtx.class);
        DealGroupPBO result = new DealGroupPBO();
        DealBuyBar dealBuyBarMock = mock(DealBuyBar.class);

        DealGroupBaseDTO dealGroupBaseDTO = new DealGroupBaseDTO();
        dealGroupBaseDTO.setMarketPrice(new BigDecimal("100"));
        when(dealCtxMock.getDealGroupBase()).thenReturn(dealGroupBaseDTO);
        when(dealCtxMock.getEnvCtx()).thenReturn(envCtx);
        when(dealCtxMock.getCategoryId()).thenReturn(401);
        DealBuyBtn dealBuyBtn = new DealBuyBtn(false, "测测");
        when(dealBuyBarMock.getBuyBtns()).thenReturn(Lists.newArrayList(dealBuyBtn));
        newBuyBarHelperMockedStatic.when(() -> NewBuyBarHelper.build(eq(dealCtxMock))).thenReturn(dealBuyBarMock);
        lionConfigUtilsMocked.when(LionConfigUtils::getDealBtnPriceStrengthTagCategoryIds).thenReturn(Lists.newArrayList(401));
        lionConfigUtilsMocked.when(LionConfigUtils::getmliveSwitch).thenReturn(false);
        lionConfigUtilsMocked.when(() -> LionConfigUtils.enableRepurchase(anyInt(), anyBoolean(), anyInt())).thenReturn(false);
        lionMockedStatic.when(() -> Lion.getBoolean(anyString(), anyString(), eq(false))).thenReturn(false);

        // act
        buyBarBuilderService.buildBuyBar(dealCtxMock, result);

        // assert
        // 此处应根据实际逻辑添加断言，验证活动查询失败或未查询到活动的处理逻辑是否正确
        List<DealBuyBtn> buyBtns = result.getBuyBar().getBuyBtns();
        assertNotNull(buyBtns.get(buyBtns.size() - 1).getMarketPrice());
    }

    @Test
    public void testBuildBuyBarByMLiveInfo_ToBuy() throws ParseException {
        lionConfigUtilsMocked.when(LionConfigUtils::getmliveSwitch).thenReturn(true);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx ctx = new DealCtx(envCtx);

        DealBuyBar dealBuyBar = JsonUtils.fromJson("{\"styleType\":1,\"buyType\":0,\"buyBtns\":[{\"btnEnable\":true,\"btnTitle\":\"用券抢购\",\"btnDesc\":\"[{\\\"text\\\":\\\"门市价 ￥\\\",\\\"textsize\\\":12,\\\"textcolor\\\":\\\"#FF999999\\\",\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"textstyle\\\":\\\"Default\\\",\\\"strikethrough\\\":false,\\\"underline\\\":false},{\\\"text\\\":\\\"388\\\",\\\"textsize\\\":12,\\\"textcolor\\\":\\\"#FF999999\\\",\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"textstyle\\\":\\\"Default\\\",\\\"strikethrough\\\":true,\\\"underline\\\":false}]\",\"btnTag\":\"共省¥298.1\",\"priceStr\":\"89.9\",\"pricePrefix\":\"券后\",\"redirectUrl\":\"imeituan://www.meituan.com/gc/createorder?dealid=961569983&shopid=1315533376&is_sku=0&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoUA1J884PzNr0ku1LpTuBPMdArH0p9jGeAOoy2XeFgS-mLNlJ47m8l_eZUOj5qhU_y1ggIWw3DCdqBjv_mkv2nZvwC3xyJ7mm9sPxEKwn81qEt_lf9jKTvxfYGlmvOQ25UGb-jU10Mo6AmPNvihLP1zsda-gM_upEx-F0S2EbTznM50obaTajgNr9yLEx-_vnffciAl3pwugH_5iIzywP_lMCJ_8iHJmOrxaX1ivUzQZOxCtaakiDBqlPBUnWvCIIQZ5AXMvjIP-OQSwuTQubP6tyHBxQxzMqCr2t5lZsIgVx8a41Tt-jmTVJVLx11DfS-2uOQjUAZab7XjVP0toNTTWpFWzBXUKLg-CcnPRJc2xGF3kHfWJVpccXcCdKrVny2OsR9Fepdbxq4RUEde7_CzTp7lJ9HQYLoICNdqCKAogA\",\"detailBuyType\":1,\"btnIcons\":[{\"title\":\"共省¥298.1\",\"titleColor\":\"#646464\",\"borderColor\":\"#646464\",\"bgColor\":\"#FFFFFF\",\"style\":\"#646464\",\"type\":1}],\"btnText\":\"￥89.9 立即购买\",\"addShoppingCartStatus\":1,\"priceRuleModule\":{\"priceRuleType\":1,\"priceRuleTags\":[\"团购价\",\"￥89.9\"],\"promoDesc\":\"共省￥298.1\"}}],\"buyBanner\":{\"iconUrl\":\"https://p0.meituan.net/ingee/da8c587fba577ab8d535a2c3a5ab20773446.png\",\"content\":\"[{\\\"text\\\":\\\"你有\\\",\\\"textsize\\\":12,\\\"textcolor\\\":\\\"222222\\\",\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"textstyle\\\":\\\"Default\\\",\\\"strikethrough\\\":false,\\\"underline\\\":false},{\\\"text\\\":\\\"10\\\",\\\"textsize\\\":12,\\\"textcolor\\\":\\\"#FF4B10\\\",\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"textstyle\\\":\\\"Bold\\\",\\\"strikethrough\\\":false,\\\"underline\\\":false},{\\\"text\\\":\\\"元专属优惠券，下单立享\\\",\\\"textsize\\\":12,\\\"textcolor\\\":\\\"222222\\\",\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"textstyle\\\":\\\"Default\\\",\\\"strikethrough\\\":false,\\\"underline\\\":false}]\",\"leadAction\":0,\"show\":true,\"bannerType\":4}}", DealBuyBar.class);
        DealGroupBaseDTO dealGroupBaseDTO = JsonUtils.fromJson("{\"dealGroupId\":961569983,\"dealGroupShortTitle\":\"V·野专业美睫美甲\",\"dealGroupTitleDesc\":\"仅售288元，价值388元【视觉冲击】 可盐可甜狐系美睫！\",\"maxJoin\":0,\"currentJoin\":0,\"defaultPic\":\"https://p0.meituan.net/merchantpic/80ca70fd140513312021a42ffc3c3a41380238.jpg\",\"dealGroupPrice\":288.00,\"marketPrice\":388.00,\"maxPerUser\":0,\"minPerUser\":1,\"status\":1,\"autoRefundSwitch\":1,\"dealGroupPics\":\"https://p0.meituan.net/merchantpic/80ca70fd140513312021a42ffc3c3a41380238.jpg|https://p1.meituan.net/merchantpic/eabf65ca3fa8d6337f1aa647f80d6d7a154900.jpg|https://p1.meituan.net/merchantpic/4662bf4168fe2d1fdd44d632e0c5b8fc174983.jpg|https://p0.meituan.net/merchantpic/f2a0b02f358db52280a5adbcd1f6e5cb139828.jpg\",\"saleChannel\":0,\"salePlatform\":3,\"publishStatus\":1,\"dealGroupType\":1,\"overdueAutoRefund\":true,\"canUseCoupon\":true,\"thirdPartVerify\":false,\"payChannelIDAllowed\":0,\"discountRuleID\":0,\"blockStock\":false,\"publishVersion\":0,\"lottery\":false,\"productTitle\":\"【视觉冲击】 可盐可甜狐系美睫\",\"featureTitle\":\"\",\"deals\":[{\"dealId\":1253132409,\"dealGroupId\":961569983,\"shortTitle\":\"【视觉冲击】 可盐可甜狐系美睫\",\"price\":288.00,\"marketPrice\":388.00,\"maxJoin\":0,\"currentJoin\":0,\"receiptType\":1,\"deliverType\":0,\"dealStatus\":1,\"receiptDateType\":1,\"receiptValidDays\":90,\"provideInvoice\":false,\"thirdPartyId\":0,\"dealGroupPublishVersion\":0}],\"sourceId\":102}", DealGroupBaseDTO.class);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        Date beginDate = sdf.parse("2030-01-01 00:00:00");
        Date endDate = sdf.parse("2100-01-01 00:00:00");
        Date finishDate = sdf.parse("2100-01-01 00:00:00");
        Date receiptBeginDate = sdf.parse("2024-01-01 00:00:00");
        Date receiptEndDate = sdf.parse("2100-01-01 00:00:00");
        dealGroupBaseDTO.setBeginDate(beginDate);
        dealGroupBaseDTO.setEndDate(endDate);
        dealGroupBaseDTO.setFinishDate(finishDate);
        dealGroupBaseDTO.getDeals().forEach(dealBaseDTO -> dealBaseDTO.setReceiptBeginDate(receiptBeginDate));
        dealGroupBaseDTO.getDeals().forEach(dealBaseDTO -> dealBaseDTO.setReceiptEndDate(receiptEndDate));
        ctx.setDealGroupBase(dealGroupBaseDTO);

        buyBarBuilderService.buildBuyBarByMLiveInfo(ctx, dealBuyBar);
        Assert.assertTrue(dealBuyBar.getBuyBtns().get(0).getBtnTitle().equals("待开抢"));
    }

    @Test
    public void testBuildBuyBarByMLiveInfo_LiveToBuy() throws ParseException {
        lionConfigUtilsMocked.when(LionConfigUtils::getmliveSwitch).thenReturn(true);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setRequestSource(RequestSourceEnum.LIVE_STREAM.getSource());

        DealBuyBar dealBuyBar = JsonUtils.fromJson("{\"styleType\":1,\"buyType\":0,\"buyBtns\":[{\"btnEnable\":true,\"btnTitle\":\"用券抢购\",\"btnDesc\":\"[{\\\"text\\\":\\\"门市价 ￥\\\",\\\"textsize\\\":12,\\\"textcolor\\\":\\\"#FF999999\\\",\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"textstyle\\\":\\\"Default\\\",\\\"strikethrough\\\":false,\\\"underline\\\":false},{\\\"text\\\":\\\"388\\\",\\\"textsize\\\":12,\\\"textcolor\\\":\\\"#FF999999\\\",\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"textstyle\\\":\\\"Default\\\",\\\"strikethrough\\\":true,\\\"underline\\\":false}]\",\"btnTag\":\"共省¥298.1\",\"priceStr\":\"89.9\",\"pricePrefix\":\"券后\",\"redirectUrl\":\"imeituan://www.meituan.com/gc/createorder?dealid=961569983&shopid=1315533376&is_sku=0&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoUA1J884PzNr0ku1LpTuBPMdArH0p9jGeAOoy2XeFgS-mLNlJ47m8l_eZUOj5qhU_y1ggIWw3DCdqBjv_mkv2nZvwC3xyJ7mm9sPxEKwn81qEt_lf9jKTvxfYGlmvOQ25UGb-jU10Mo6AmPNvihLP1zsda-gM_upEx-F0S2EbTznM50obaTajgNr9yLEx-_vnffciAl3pwugH_5iIzywP_lMCJ_8iHJmOrxaX1ivUzQZOxCtaakiDBqlPBUnWvCIIQZ5AXMvjIP-OQSwuTQubP6tyHBxQxzMqCr2t5lZsIgVx8a41Tt-jmTVJVLx11DfS-2uOQjUAZab7XjVP0toNTTWpFWzBXUKLg-CcnPRJc2xGF3kHfWJVpccXcCdKrVny2OsR9Fepdbxq4RUEde7_CzTp7lJ9HQYLoICNdqCKAogA\",\"detailBuyType\":1,\"btnIcons\":[{\"title\":\"共省¥298.1\",\"titleColor\":\"#646464\",\"borderColor\":\"#646464\",\"bgColor\":\"#FFFFFF\",\"style\":\"#646464\",\"type\":1}],\"btnText\":\"￥89.9 立即购买\",\"addShoppingCartStatus\":1,\"priceRuleModule\":{\"priceRuleType\":1,\"priceRuleTags\":[\"团购价\",\"￥89.9\"],\"promoDesc\":\"共省￥298.1\"}}],\"buyBanner\":{\"iconUrl\":\"https://p0.meituan.net/ingee/da8c587fba577ab8d535a2c3a5ab20773446.png\",\"content\":\"[{\\\"text\\\":\\\"你有\\\",\\\"textsize\\\":12,\\\"textcolor\\\":\\\"222222\\\",\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"textstyle\\\":\\\"Default\\\",\\\"strikethrough\\\":false,\\\"underline\\\":false},{\\\"text\\\":\\\"10\\\",\\\"textsize\\\":12,\\\"textcolor\\\":\\\"#FF4B10\\\",\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"textstyle\\\":\\\"Bold\\\",\\\"strikethrough\\\":false,\\\"underline\\\":false},{\\\"text\\\":\\\"元专属优惠券，下单立享\\\",\\\"textsize\\\":12,\\\"textcolor\\\":\\\"222222\\\",\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"textstyle\\\":\\\"Default\\\",\\\"strikethrough\\\":false,\\\"underline\\\":false}]\",\"leadAction\":0,\"show\":true,\"bannerType\":4}}", DealBuyBar.class);
        DealGroupBaseDTO dealGroupBaseDTO = JsonUtils.fromJson("{\"dealGroupId\":961569983,\"dealGroupShortTitle\":\"V·野专业美睫美甲\",\"dealGroupTitleDesc\":\"仅售288元，价值388元【视觉冲击】 可盐可甜狐系美睫！\",\"maxJoin\":0,\"currentJoin\":0,\"defaultPic\":\"https://p0.meituan.net/merchantpic/80ca70fd140513312021a42ffc3c3a41380238.jpg\",\"dealGroupPrice\":288.00,\"marketPrice\":388.00,\"maxPerUser\":0,\"minPerUser\":1,\"status\":1,\"autoRefundSwitch\":1,\"dealGroupPics\":\"https://p0.meituan.net/merchantpic/80ca70fd140513312021a42ffc3c3a41380238.jpg|https://p1.meituan.net/merchantpic/eabf65ca3fa8d6337f1aa647f80d6d7a154900.jpg|https://p1.meituan.net/merchantpic/4662bf4168fe2d1fdd44d632e0c5b8fc174983.jpg|https://p0.meituan.net/merchantpic/f2a0b02f358db52280a5adbcd1f6e5cb139828.jpg\",\"saleChannel\":0,\"salePlatform\":3,\"publishStatus\":1,\"dealGroupType\":1,\"overdueAutoRefund\":true,\"canUseCoupon\":true,\"thirdPartVerify\":false,\"payChannelIDAllowed\":0,\"discountRuleID\":0,\"blockStock\":false,\"publishVersion\":0,\"lottery\":false,\"productTitle\":\"【视觉冲击】 可盐可甜狐系美睫\",\"featureTitle\":\"\",\"deals\":[{\"dealId\":1253132409,\"dealGroupId\":961569983,\"shortTitle\":\"【视觉冲击】 可盐可甜狐系美睫\",\"price\":288.00,\"marketPrice\":388.00,\"maxJoin\":0,\"currentJoin\":0,\"receiptType\":1,\"deliverType\":0,\"dealStatus\":1,\"receiptDateType\":1,\"receiptValidDays\":90,\"provideInvoice\":false,\"thirdPartyId\":0,\"dealGroupPublishVersion\":0}],\"sourceId\":102}", DealGroupBaseDTO.class);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        Date beginDate = sdf.parse("2030-01-01 00:00:00");
        Date endDate = sdf.parse("2100-01-01 00:00:00");
        Date finishDate = sdf.parse("2100-01-01 00:00:00");
        Date receiptBeginDate = sdf.parse("2024-01-01 00:00:00");
        Date receiptEndDate = sdf.parse("2100-01-01 00:00:00");
        dealGroupBaseDTO.setBeginDate(beginDate);
        dealGroupBaseDTO.setEndDate(endDate);
        dealGroupBaseDTO.setFinishDate(finishDate);
        dealGroupBaseDTO.getDeals().forEach(dealBaseDTO -> dealBaseDTO.setReceiptBeginDate(receiptBeginDate));
        dealGroupBaseDTO.getDeals().forEach(dealBaseDTO -> dealBaseDTO.setReceiptEndDate(receiptEndDate));
        ctx.setDealGroupBase(dealGroupBaseDTO);

        buyBarBuilderService.buildBuyBarByMLiveInfo(ctx, dealBuyBar);
        Assert.assertTrue(dealBuyBar.getBuyBtns().get(0).getBtnTitle().equals("待开抢"));
    }

    @Test
    public void testBuildBuyBarByMLiveInfo_MLiveChannel() throws ParseException {
        lionConfigUtilsMocked.when(LionConfigUtils::getmliveSwitch).thenReturn(true);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setMLiveChannel(true);

        DealBuyBar dealBuyBar = JsonUtils.fromJson("{\"styleType\":1,\"buyType\":0,\"buyBtns\":[{\"btnEnable\":true,\"btnTitle\":\"用券抢购\",\"btnDesc\":\"[{\\\"text\\\":\\\"门市价 ￥\\\",\\\"textsize\\\":12,\\\"textcolor\\\":\\\"#FF999999\\\",\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"textstyle\\\":\\\"Default\\\",\\\"strikethrough\\\":false,\\\"underline\\\":false},{\\\"text\\\":\\\"388\\\",\\\"textsize\\\":12,\\\"textcolor\\\":\\\"#FF999999\\\",\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"textstyle\\\":\\\"Default\\\",\\\"strikethrough\\\":true,\\\"underline\\\":false}]\",\"btnTag\":\"共省¥298.1\",\"priceStr\":\"89.9\",\"pricePrefix\":\"券后\",\"redirectUrl\":\"imeituan://www.meituan.com/gc/createorder?dealid=961569983&shopid=1315533376&is_sku=0&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoUA1J884PzNr0ku1LpTuBPMdArH0p9jGeAOoy2XeFgS-mLNlJ47m8l_eZUOj5qhU_y1ggIWw3DCdqBjv_mkv2nZvwC3xyJ7mm9sPxEKwn81qEt_lf9jKTvxfYGlmvOQ25UGb-jU10Mo6AmPNvihLP1zsda-gM_upEx-F0S2EbTznM50obaTajgNr9yLEx-_vnffciAl3pwugH_5iIzywP_lMCJ_8iHJmOrxaX1ivUzQZOxCtaakiDBqlPBUnWvCIIQZ5AXMvjIP-OQSwuTQubP6tyHBxQxzMqCr2t5lZsIgVx8a41Tt-jmTVJVLx11DfS-2uOQjUAZab7XjVP0toNTTWpFWzBXUKLg-CcnPRJc2xGF3kHfWJVpccXcCdKrVny2OsR9Fepdbxq4RUEde7_CzTp7lJ9HQYLoICNdqCKAogA\",\"detailBuyType\":1,\"btnIcons\":[{\"title\":\"共省¥298.1\",\"titleColor\":\"#646464\",\"borderColor\":\"#646464\",\"bgColor\":\"#FFFFFF\",\"style\":\"#646464\",\"type\":1}],\"btnText\":\"￥89.9 立即购买\",\"addShoppingCartStatus\":1,\"priceRuleModule\":{\"priceRuleType\":1,\"priceRuleTags\":[\"团购价\",\"￥89.9\"],\"promoDesc\":\"共省￥298.1\"}}],\"buyBanner\":{\"iconUrl\":\"https://p0.meituan.net/ingee/da8c587fba577ab8d535a2c3a5ab20773446.png\",\"content\":\"[{\\\"text\\\":\\\"你有\\\",\\\"textsize\\\":12,\\\"textcolor\\\":\\\"222222\\\",\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"textstyle\\\":\\\"Default\\\",\\\"strikethrough\\\":false,\\\"underline\\\":false},{\\\"text\\\":\\\"10\\\",\\\"textsize\\\":12,\\\"textcolor\\\":\\\"#FF4B10\\\",\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"textstyle\\\":\\\"Bold\\\",\\\"strikethrough\\\":false,\\\"underline\\\":false},{\\\"text\\\":\\\"元专属优惠券，下单立享\\\",\\\"textsize\\\":12,\\\"textcolor\\\":\\\"222222\\\",\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"textstyle\\\":\\\"Default\\\",\\\"strikethrough\\\":false,\\\"underline\\\":false}]\",\"leadAction\":0,\"show\":true,\"bannerType\":4}}", DealBuyBar.class);
        DealGroupBaseDTO dealGroupBaseDTO = JsonUtils.fromJson("{\"dealGroupId\":961569983,\"dealGroupShortTitle\":\"V·野专业美睫美甲\",\"dealGroupTitleDesc\":\"仅售288元，价值388元【视觉冲击】 可盐可甜狐系美睫！\",\"maxJoin\":0,\"currentJoin\":0,\"defaultPic\":\"https://p0.meituan.net/merchantpic/80ca70fd140513312021a42ffc3c3a41380238.jpg\",\"dealGroupPrice\":288.00,\"marketPrice\":388.00,\"maxPerUser\":0,\"minPerUser\":1,\"status\":1,\"autoRefundSwitch\":1,\"dealGroupPics\":\"https://p0.meituan.net/merchantpic/80ca70fd140513312021a42ffc3c3a41380238.jpg|https://p1.meituan.net/merchantpic/eabf65ca3fa8d6337f1aa647f80d6d7a154900.jpg|https://p1.meituan.net/merchantpic/4662bf4168fe2d1fdd44d632e0c5b8fc174983.jpg|https://p0.meituan.net/merchantpic/f2a0b02f358db52280a5adbcd1f6e5cb139828.jpg\",\"saleChannel\":0,\"salePlatform\":3,\"publishStatus\":1,\"dealGroupType\":1,\"overdueAutoRefund\":true,\"canUseCoupon\":true,\"thirdPartVerify\":false,\"payChannelIDAllowed\":0,\"discountRuleID\":0,\"blockStock\":false,\"publishVersion\":0,\"lottery\":false,\"productTitle\":\"【视觉冲击】 可盐可甜狐系美睫\",\"featureTitle\":\"\",\"deals\":[{\"dealId\":1253132409,\"dealGroupId\":961569983,\"shortTitle\":\"【视觉冲击】 可盐可甜狐系美睫\",\"price\":288.00,\"marketPrice\":388.00,\"maxJoin\":0,\"currentJoin\":0,\"receiptType\":1,\"deliverType\":0,\"dealStatus\":1,\"receiptDateType\":1,\"receiptValidDays\":90,\"provideInvoice\":false,\"thirdPartyId\":0,\"dealGroupPublishVersion\":0}],\"sourceId\":102}", DealGroupBaseDTO.class);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        Date beginDate = sdf.parse("2030-01-01 00:00:00");
        Date endDate = sdf.parse("2100-01-01 00:00:00");
        Date finishDate = sdf.parse("2100-01-01 00:00:00");
        Date receiptBeginDate = sdf.parse("2024-01-01 00:00:00");
        Date receiptEndDate = sdf.parse("2100-01-01 00:00:00");
        dealGroupBaseDTO.setBeginDate(beginDate);
        dealGroupBaseDTO.setEndDate(endDate);
        dealGroupBaseDTO.setFinishDate(finishDate);
        dealGroupBaseDTO.getDeals().forEach(dealBaseDTO -> dealBaseDTO.setReceiptBeginDate(receiptBeginDate));
        dealGroupBaseDTO.getDeals().forEach(dealBaseDTO -> dealBaseDTO.setReceiptEndDate(receiptEndDate));
        ctx.setDealGroupBase(dealGroupBaseDTO);

        buyBarBuilderService.buildBuyBarByMLiveInfo(ctx, dealBuyBar);
        Assert.assertTrue(dealBuyBar.getBuyBtns().get(0).getBtnTitle().equals("直播专享"));
    }

    @Test
    public void testRepurchase() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setVersion("12.20.400");
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupBuyRuleDTO buyRuleDTO = new DealGroupBuyRuleDTO();
        buyRuleDTO.setMaxPerUser(1);
        buyBarBuilderService.isPurchaseLimit(buyRuleDTO);
        buyRuleDTO.setMaxPerUser(0);
        buyBarBuilderService.isPurchaseLimit(buyRuleDTO);

        //
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        ctx.setDealGroupDTO(dealGroupDTO);
        DealGroupRuleDTO rule = new DealGroupRuleDTO();
        rule.setBuyRule(buyRuleDTO);
        dealGroupDTO.setRule(rule);

//        buyBarBuilderService.isPurchaseLimitDeal(ctx);

        envCtx.setDztgClientTypeEnum(MEITUAN_APP);
        dealGroupDTO.setRule(null);
//        buyBarBuilderService.isPurchaseLimitDeal(ctx);


        buyBarBuilderService.userOrderCountOverPurchaseLimit(ctx);
        buyBarBuilderService.buildPurchaseMessage();
        buyBarBuilderService.buildPurchaseMessageRemindInfo("","","","");

        Class clazz = buyBarBuilderService.getClass();
        DealGroupPBO result = new DealGroupPBO();
        DealBuyBar buyBar = new  DealBuyBar(DealBuyBar.BuyType.COMMON.type, Lists.newArrayList());
         buyBarBuilderService.buildBuyBarByPurchaseLimit(ctx,buyBar,result);


        DealCtx ctx1 = new DealCtx(envCtx);
        ctx.setEnvCtx(envCtx);
        envCtx.setMtUserId(1L);
        envCtx.setDpUserId(2L);
        envCtx.setClientType(200100);
        SwanQueryService swanQueryService = new SwanQueryService() {
            @Override
            public Result<QueryData> queryByKey(Integer integer, String s, List<Map<String, Object>> list, PageInfo pageInfo) {
                return null;
            }

            @Override
            public Result<QueryData> queryByKey(Integer integer, String s, SwanParam swanParam) {
                return null;
            }
        };
        try {
            Field field = clazz.getDeclaredField("swanQueryService");
            field.setAccessible(true);
            field.set(buyBarBuilderService, swanQueryService);
        } catch (Exception e){
        }
        buyBarBuilderService.getUserOrderCount(ctx1);


        Result<QueryData> result2 = new Result<>();
        result2.setIfSuccess(true);
        QueryData data = new QueryData();
        List<Map<String, Object>> resultSet = Lists.newArrayList();
        Map<String, Object> s = new HashMap<>();
        s.put("value","{\"userordercount\":\"1\"}");
        resultSet.add(s);
        data.setResultSet(resultSet);
        result2.setData(data);
        buyBarBuilderService.getUserOrderCountFromResult(result2);


        Assert.assertNotNull(result);

    }

    /**
     * 测试 isInWhite 方法，当 category 在 categoryIds 列表中时，应返回 true
     */
    @Test
    public void testIsInWhiteCategoryInList() {
        // arrange
        int category = 1;
        lionConfigUtilsMocked.when(LionConfigUtils::getDealBtnPriceStrengthTagCategoryIds).thenReturn(Arrays.asList(1, 2));
        // act
        boolean result = buyBarBuilderService.isInWhite(category);
        // assert
        assertTrue(result);
    }


    private void commonTestSetup(BigDecimal expected, PriceStrengthTimeEnum priceStrengthTime, Map<String, Object> resultSetMap) throws Throwable {
        int dealGroupId = 1;
        Date priceAbilityDate = null;
        Result<QueryData> result = new Result<>();
        result.setIfSuccess(true);
        QueryData queryData = new QueryData();
        List<Map<String, Object>> resultSet = new ArrayList<>();
        if (resultSetMap != null) {
            resultSet.add(resultSetMap);
        }
        queryData.setResultSet(resultSet);
        result.setData(queryData);
        when(swanQueryService.queryByKey(anyInt(), anyString(), any(SwanParam.class))).thenReturn(result);
        BigDecimal actual = buyBarBuilderService.queryPriceAbility(dealGroupId, priceStrengthTime, priceAbilityDate);
        assertEquals(expected, actual);
    }


    /**
     * 测试Lion配置中存在购买按钮消息配置的情况
     */
    @Test
    public void testBuildPurchaseMessage_ConfigExists() throws Throwable {
        // arrange
        Map<String, PurchaseMessageConfig> configMap = new HashMap<>();
        PurchaseMessageConfig config = new PurchaseMessageConfig();
        config.setType("dialog");
        config.setTitle("已达购买次数上限");
        config.setContent("您的历史购买次数已达该商品限制购买次数，可重新选购相似商品");
        config.setConfirmBtnTxt("我知道了");
        configMap.put("purchaseMessage", config);
        lionMockedStatic.when(LionConfigUtils::getPurchaseBtnMessageConfig).thenReturn(configMap);
        // act
        PurchaseMessageRemindInfo result = buyBarBuilderService.buildPurchaseMessage();
        // assert
        assertEquals("dialog", result.getType());
        assertEquals("已达购买次数上限", result.getTitle());
        assertEquals("您的历史购买次数已达该商品限制购买次数，可重新选购相似商品", result.getContent());
        assertEquals("我知道了", result.getConfirmBtnTxt());
    }

    /**
     * 测试Lion配置中不存在购买按钮消息配置的情况
     */
    @Test
    public void testBuildPurchaseMessage_ConfigNotExists() throws Throwable {
        // arrange
        lionMockedStatic.when(LionConfigUtils::getPurchaseBtnMessageConfig).thenReturn(Collections.emptyMap());
        // act
        PurchaseMessageRemindInfo result = buyBarBuilderService.buildPurchaseMessage();
        // assert
        assertEquals("dialog", result.getType());
        assertEquals("已达购买次数上限", result.getTitle());
        assertEquals("您的历史购买次数已达该商品限制购买次数，可重新选购相似商品", result.getContent());
        assertEquals("我知道了", result.getConfirmBtnTxt());
    }


    /**
     * 测试buildPurchaseMessageRemindInfo方法，所有参数为非空字符串的情况
     */
    @Test
    public void testBuildPurchaseMessageRemindInfoAllParamsNotNull() {
        // arrange
        String type = "type";
        String title = "title";
        String content = "content";
        String confirmBtnTxt = "confirmBtnTxt";
        // act
        PurchaseMessageRemindInfo result = buyBarBuilderService.buildPurchaseMessageRemindInfo(type, title, content, confirmBtnTxt);
        // assert
        Assert.assertEquals(type, result.getType());
        Assert.assertEquals(title, result.getTitle());
        Assert.assertEquals(content, result.getContent());
        Assert.assertEquals(confirmBtnTxt, result.getConfirmBtnTxt());
    }

    /**
     * 测试buildPurchaseMessageRemindInfo方法，所有参数为null的情况
     */
    @Test
    public void testBuildPurchaseMessageRemindInfoAllParamsNull() {
        // arrange
        String type = null;
        String title = null;
        String content = null;
        String confirmBtnTxt = null;
        // act
        PurchaseMessageRemindInfo result = buyBarBuilderService.buildPurchaseMessageRemindInfo(type, title, content, confirmBtnTxt);
        // assert
        Assert.assertEquals(type, result.getType());
        Assert.assertEquals(title, result.getTitle());
        Assert.assertEquals(content, result.getContent());
        Assert.assertEquals(confirmBtnTxt, result.getConfirmBtnTxt());
    }

    /**
     * 测试buildPurchaseMessageRemindInfo方法，所有参数为空字符串的情况
     */
    @Test
    public void testBuildPurchaseMessageRemindInfoAllParamsEmpty() {
        // arrange
        String type = "";
        String title = "";
        String content = "";
        String confirmBtnTxt = "";
        // act
        PurchaseMessageRemindInfo result = buyBarBuilderService.buildPurchaseMessageRemindInfo(type, title, content, confirmBtnTxt);
        // assert
        Assert.assertEquals(type, result.getType());
        Assert.assertEquals(title, result.getTitle());
        Assert.assertEquals(content, result.getContent());
        Assert.assertEquals(confirmBtnTxt, result.getConfirmBtnTxt());
    }
}