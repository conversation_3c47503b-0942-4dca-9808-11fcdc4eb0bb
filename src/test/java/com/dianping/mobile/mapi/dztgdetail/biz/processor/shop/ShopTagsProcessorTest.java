package com.dianping.mobile.mapi.dztgdetail.biz.processor.shop;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ShopTagWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.Future;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ShopTagsProcessorTest {

    @InjectMocks
    private ShopTagsProcessor shopTagsProcessor;

    @Mock
    private DealCtx ctx;

    @Mock
    private DealGroupDTO dealGroupDTO;

    @Mock
    private ShopTagWrapper shopTagWrapper;

    private MockedStatic<LionConfigUtils> lionConfigUtilsMockedStatic;
    private MockedStatic<DealCtxHelper> dealCtxHelperMockedStatic;

    @Before
    public void setUp() {
        lionConfigUtilsMockedStatic = mockStatic(LionConfigUtils.class);
        dealCtxHelperMockedStatic = mockStatic(DealCtxHelper.class);
    }

    @After
    public void tearDown() {
        lionConfigUtilsMockedStatic.close();
        dealCtxHelperMockedStatic.close();
    }

    /**
     * 测试场景：dealGroupDTO为null，dpShopIdL小于等于0
     */
    @Test
    public void testIsEnable_DealGroupDTONullAndDpShopIdLEqZero() {
        when(ctx.getDealGroupDTO()).thenReturn(null);
        when(ctx.getDpLongShopId()).thenReturn(0L);

        assertFalse(shopTagsProcessor.isEnable(ctx));
    }

    /**
     * 测试场景：dealGroupDTO不为null，dpShopIdL小于等于0
     */
    @Test
    public void testIsEnable_DealGroupDTONotNullAndDpShopIdLEqZero() {
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(ctx.getDpLongShopId()).thenReturn(0L);

        assertFalse(shopTagsProcessor.isEnable(ctx));
    }

    /**
     * 测试场景：dealGroupDTO为null，dpShopIdL大于0，但配置不启用
     */
    @Test
    public void testIsEnable_DealGroupDTONullAndDpShopIdLGreaterThanZeroConfigDisabled() {
        when(ctx.getDealGroupDTO()).thenReturn(null);
        when(ctx.getDpLongShopId()).thenReturn(1L);
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.getShopIDToTagIDConfig(any())).thenReturn(false);
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.getPerformanceGuaranteeSwitch(any())).thenReturn(false);

        assertFalse(shopTagsProcessor.isEnable(ctx));
    }

    /**
     * 测试场景：dealGroupDTO不为null，dpShopIdL大于0，标签配置启用
     */
    @Test
    public void testIsEnable_DealGroupDTONotNullAndDpShopIdLGreaterThanZeroConfigEnabled_tagConfig() {
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(ctx.getDpLongShopId()).thenReturn(1L);
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.getShopIDToTagIDConfig(any())).thenReturn(true);
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.getPerformanceGuaranteeSwitch(any())).thenReturn(false);

        assertTrue(shopTagsProcessor.isEnable(ctx));
    }

    /**
     * 测试场景：dealGroupDTO不为null，dpShopIdL大于0，履约配置启用
     */
    @Test
    public void testIsEnable_DealGroupDTONotNullAndDpShopIdLGreaterThanZeroConfigEnabled_performanceGuarantee() {
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(ctx.getDpLongShopId()).thenReturn(1L);
        EnvCtx envCtx = mock(EnvCtx.class);
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.isNative()).thenReturn(true);
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.getShopIDToTagIDConfig(any())).thenReturn(false);
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.getPerformanceGuaranteeSwitch(any())).thenReturn(true);

        assertTrue(shopTagsProcessor.isEnable(ctx));
    }

    @Test
    public void testPrepare_withPreorder() {
        when(ctx.getFutureCtx()).thenReturn(new FutureCtx());
        when(shopTagWrapper.preGetDpShopTags(ctx.getDpLongShopId())).thenReturn(null);
        dealCtxHelperMockedStatic.when(() -> DealCtxHelper.isPreOrderDeal(ctx)).thenReturn(true);
        when(shopTagWrapper.preGetShopAvgOrderTime(ctx)).thenReturn(null);
        shopTagsProcessor.prepare(ctx);
        verify(shopTagWrapper, times(1)).preGetDpShopTags(ctx.getDpLongShopId());
        verify(shopTagWrapper, times(1)).preGetShopAvgOrderTime(ctx);
    }

    @Test
    public void testProcess_withPreorder() {
        FutureCtx futureCtx = new FutureCtx();
        futureCtx.setShopAvgOrderTimeFuture(mock(Future.class));
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
        shopTagsProcessor.process(ctx);
        verify(shopTagWrapper, times(1)).getPreOrderFeatDetail(ctx.getFutureCtx().getShopAvgOrderTimeFuture(), ctx.getDpLongShopId());
    }
}
