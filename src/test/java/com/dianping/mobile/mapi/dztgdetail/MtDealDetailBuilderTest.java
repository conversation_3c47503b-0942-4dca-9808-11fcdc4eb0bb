package com.dianping.mobile.mapi.dztgdetail;

import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.FreeDealEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.Pair;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.MtDealDetailBuilder;
import com.dianping.mobile.mapi.dztgdetail.util.ReassuredRepairUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;

@RunWith(MockitoJUnitRunner.class)
public class MtDealDetailBuilderTest {

    @InjectMocks
    private MtDealDetailBuilder mtDealDetailBuilder;

    @Before
    public void setUp() {
        Map<Integer, Pair> pairMap = Maps.newConcurrentMap();
        pairMap.put(MtDealDetailBuilder.TYPE_SPECIAL_REMINDER, new Pair());
        DealGroupBaseDTO dealGroupBaseDto = new DealGroupBaseDTO();
        mtDealDetailBuilder.setMap(pairMap);
        mtDealDetailBuilder.setDealGroupBaseDto(dealGroupBaseDto);
    }

    @Test
    public void testToStructedDetails() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(1);
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx context = new DealCtx(envCtx);
        context.setFreeDeal(true);
        context.setFreeDealType(FreeDealEnum.HOME_DESIGN_BOOKING);
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("free_product_notice");
        attrDTO.setValue(Lists.newArrayList("亮点"));
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        context.setDealGroupDTO(dealGroupDTO);
        dealGroupDTO.setAttrs(Lists.newArrayList(attrDTO));
        List<Pair> structedDetails = mtDealDetailBuilder.toStructedDetails(context, false);
        Assert.assertNotNull(structedDetails);
    }

    @Test
    public void testProcessDealGroupDetailsWithPurchaseNotes() throws Exception {
        // 使用已有的反射方法获取
        Method method = MtDealDetailBuilder.class.getDeclaredMethod("processDealGroupDetails", DealCtx.class, Pair.class);
        method.setAccessible(true);

        // 初始化上下文
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(1);
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx ctx = new DealCtx(envCtx);

        // 初始化DealGroupDTO
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        ctx.setDealGroupDTO(dealGroupDTO);

        // 构造购买须知内容
        String purchaseNotes = "<div>\n" +
                "<div class=\"detail-box\">\n" +
                "<div class=\"purchase-notes\">\n" +
                "<dl>\n" +
                "<dt>有效期</dt>\n" +
                "<dd>\n" +
                "<p class=\"listitem\">\n" +
                "购买后90天内有效\n" +
                "</p>\n" +
                "</dd>\n" +
                "</dl>\n" +
                "<dl>\n" +
                "<dt>预约信息</dt>\n" +
                "<dd>\n" +
                "<p class=\"listitem\">请您提前30分钟预约</p>\n" +
                "</dd>\n" +
                "</dl>\n" +
                "<dl>\n" +
                "<dt>规则提醒</dt>\n" +
                "<dd>\n" +
                "<p class=\"listitem\">可与其他优惠同享</p>\n" +
                "</dd>\n" +
                "</dl>\n" +
                "<dl>\n" +
                "<dt>温馨提示</dt>\n" +
                "<dd>\n" +
                "<p class=\"listitem\">若涉及上门服务，工作人员上门时会主动出示工作证件</p>\n" +
                "<p class=\"listitem\">如需团购券发票，请您在消费时向商户咨询</p>\n" +
                "<p class=\"listitem\">为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！</p>\n" +
                "</dd>\n" +
                "</dl>\n" +
                "</div>\n" +
                "</div>\n" +
                "</div>";

        Pair pair = new Pair();
        pair.setId("购买须知");
        pair.setName(purchaseNotes);

        boolean result = false;
        // 使用 MockedStatic 来 mock 静态方法
        try (MockedStatic<DealAttrHelper> dealAttrHelperMock = Mockito.mockStatic(DealAttrHelper.class);
             MockedStatic<ReassuredRepairUtil> reassuredRepairUtilMock = Mockito.mockStatic(ReassuredRepairUtil.class)) {

            // 设置静态方法行为
            dealAttrHelperMock.when(() -> DealAttrHelper.isWuyoutong(any(DealCtx.class))).thenReturn(true);
            reassuredRepairUtilMock.when(() -> ReassuredRepairUtil.isTagPresent(any(DealCtx.class))).thenReturn(false);

            method.invoke(mtDealDetailBuilder, ctx, pair);
            String name = pair.getName();
            System.out.println(name);
            result = true;
        } catch (Exception e) {
            System.out.println("业务异常: " + e.getCause().getMessage());
            result = false;
        }
        Assert.assertTrue(result);
    }

    @Test
    public void testToStructedDetailsEdu() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(1);
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx context = new DealCtx(envCtx);
        context.setFreeDeal(true);
        context.setFreeDealType(FreeDealEnum.EDU_TRIAL_BOOKING);
        List<Pair> structedDetails = mtDealDetailBuilder.toStructedDetails(context, false);
        Assert.assertNotNull(structedDetails);
    }

    @Test
    public void testProcessDealGroupDetails() throws Exception {
        Method method = MtDealDetailBuilder.class.getDeclaredMethod("processDealGroupDetails", DealCtx.class, Pair.class);
        method.setAccessible(true);
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        ctx.setDealGroupDTO(dealGroupDTO);
        // 创建Pair对象，模拟“购买须知”
        Pair pair = new Pair();
        pair.setId("购买须知");
        pair.setName("<div> <div class=\"detail-box\"> <div class=\"purchase-notes\"> <dl> <dt>有效期</dt> <dd> <p class=\"listitem\"> 购买后90天内有效 </p> </dd> </dl> <dl> <dt>预约信息</dt> <dd> <p class=\"listitem\">请您提前1天预约 </p> </dd> </dl> <dl> <dt>规则提醒</dt> <dd> <p class=\"listitem\">不再与其他优惠同享 </p> </dd> </dl> <dl> <dt>温馨提示</dt> <dd> <p class=\"listitem\">如需团购券发票，请您在消费时向商户咨询</p> <p class=\"listitem\">为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！</p> </dd> </dl> </div> </div> </div>");
        DealGroupServiceProjectDTO dealGroupServiceProjectDTO = new DealGroupServiceProjectDTO();
        List<MustServiceProjectGroupDTO> mustGroups = new ArrayList<>();
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        List<ServiceProjectDTO> groups = new ArrayList<>();
        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        List<ServiceProjectAttrDTO> attrs = new ArrayList<>();
        ServiceProjectAttrDTO attr = new ServiceProjectAttrDTO();
        attr.setAttrName("qizhuangxianzhi");
        attr.setAttrValue("是");
        attrs.add(attr);
        ServiceProjectAttrDTO attr1 = new ServiceProjectAttrDTO();
        attr1.setAttrName("qizhuangxianzhi2");
        attr1.setAttrValue("20");
        attrs.add(attr1);
        ServiceProjectAttrDTO attr2 = new ServiceProjectAttrDTO();
        attr2.setAttrName("priceunit");
        attr2.setAttrValue("㎡");
        attrs.add(attr2);
        ServiceProjectAttrDTO attr3 = new ServiceProjectAttrDTO();
        attr3.setAttrName("zhichiyufujinketui");
        attr3.setAttrValue("是");
        attrs.add(attr3);

        serviceProjectDTO.setAttrs(attrs);
        groups.add(serviceProjectDTO);
        mustGroup.setGroups(groups);
        mustGroups.add(mustGroup);
        dealGroupServiceProjectDTO.setMustGroups(mustGroups);
        dealGroupDTO.setServiceProject(dealGroupServiceProjectDTO);

        // 创建标签
        DealGroupTagDTO tag = new DealGroupTagDTO();
        tag.setId(100218044L); // 假设这是你要检查的标签ID

        // 创建标签列表并添加标签
        List<DealGroupTagDTO> tags = new ArrayList<>();
        tags.add(tag);

        // 创建Pair列表并添加Pair对象
        List<Pair> pairs = new ArrayList<>();
        pairs.add(pair);
        ctx.setStructedDetails(pairs);
        dealGroupDTO.setTags(tags);
        boolean result;
        try {
            method.invoke(mtDealDetailBuilder, ctx, pair);
            result = true;
        } catch (Exception e) {
            result = false;
        }
        Assert.assertTrue(result);
    }
}
