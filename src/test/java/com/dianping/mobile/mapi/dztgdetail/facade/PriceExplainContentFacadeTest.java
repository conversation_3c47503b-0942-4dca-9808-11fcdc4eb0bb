package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.common.remote.dto.CategoryDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.PriceExplainContentRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.PriceExplainContentDTO;
import com.sankuai.beautycontent.dzhealth.medical.dealgroup.consumerecord.api.query.MedicalDealConsumeRecordQueryService;
import com.sankuai.beautycontent.dzhealth.medical.dealgroup.consumerecord.dto.QueryImageUrlDTO;
import com.sankuai.beautycontent.dzhealth.medical.dealgroup.consumerecord.dto.QueryImageUrlResponse;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.mockito.Mockito.*;


public class PriceExplainContentFacadeTest {

    @InjectMocks
    PriceExplainContentFacade priceExplainContentFacade;

    @Mock
    MedicalDealConsumeRecordQueryService medicalDealConsumeRecordQueryService;

    @Mock
    QueryCenterWrapper queryCenterWrapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testNormal() throws Exception {

        EnvCtx ctx = new EnvCtx();
        ctx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);

        PriceExplainContentRequest req = new PriceExplainContentRequest();
        req.setDpDealGroupId(123L);
        req.setDpLongShopId(111L);
        QueryImageUrlResponse response = new QueryImageUrlResponse();
        response.setCode(200);
        QueryImageUrlDTO queryImageUrlDTO = new QueryImageUrlDTO();
        queryImageUrlDTO.setUrl("url");
        response.setData(queryImageUrlDTO);

        DealGroupDTO dealGroupDTO = buildDealGroupDTO();
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(dealGroupDTO);
        MockedStatic<Lion> lionMockedStatic = mockStatic(Lion.class);
        lionMockedStatic.when(() -> Lion.getString(any(), any(), any())).thenReturn("text");

        when(medicalDealConsumeRecordQueryService.queryImageUrl(any())).thenReturn(response);

        PriceExplainContentDTO result = priceExplainContentFacade.getPriceExplainContent(req, ctx);
        assertNotNull(result);

        lionMockedStatic.close();
    }

    private DealGroupDTO buildDealGroupDTO() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
        dealGroupCategoryDTO.setCategoryId(401L);
        dealGroupDTO.setDpDealGroupId(123L);
        dealGroupDTO.setCategory(dealGroupCategoryDTO);
        return dealGroupDTO;
    }

}
