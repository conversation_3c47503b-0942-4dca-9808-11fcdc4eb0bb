package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class DealAttrHelper_IsReservationEmptyTest {
    private List<Long> filterTypes = Arrays.asList(1L, 2L);
    private MockedStatic<Lion> mocked;

    @Before
    public void setUp() {
        mocked = Mockito.mockStatic(Lion.class);
        mocked.when(() -> Lion.getList(Environment.getAppName(), LionConstants.RESERVATION_EMPTY_NOT_DISPLAY_TYPE, Long.class, new ArrayList<>())).thenReturn(filterTypes);
    }

    @After
    public void tearDown() {
        mocked.close();
    }

    /**
     * 测试预约信息为空且团单类目匹配Lion配置的情况
     */
    @Test
    public void testIsReservationEmpty_ReservationEmptyAndCategoryMatch() {
        // arrange
        List<AttributeDTO> attrs = new ArrayList<>();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setCategory(new DealGroupCategoryDTO());
        dealGroupDTO.getCategory().setCategoryId(1L);

        // act
        boolean result = DealAttrHelper.isReservationEmpty(attrs, dealGroupDTO);

        // assert
        assertTrue(result);
    }

    /**
     * 测试预约信息不为空的情况
     */
    @Test
    public void testIsReservationEmpty_ReservationNotEmpty() {
        // arrange
        AttributeDTO attr1 = new AttributeDTO();
        AttributeDTO attr2 = new AttributeDTO();
        AttributeDTO attr3 = new AttributeDTO();
        attr1.setName(DealAttrKeys.RESERVATION); attr1.setValue(Arrays.asList(DealAttrKeys.RESERVATION_VALUE_YES));
        attr2.setName(DealAttrKeys.RESERVATION_2); attr2.setValue(Arrays.asList(DealAttrKeys.RESERVATION_VALUE_YES));
        attr3.setName(DealAttrKeys.RESERVATION_3); attr3.setValue(Arrays.asList(DealAttrKeys.RESERVATION_VALUE_YES));
        List<AttributeDTO> attrs = Arrays.asList(attr1, attr2, attr3);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setCategory(new DealGroupCategoryDTO());
        dealGroupDTO.getCategory().setCategoryId(1L);

        // act
        boolean result = DealAttrHelper.isReservationEmpty(attrs, dealGroupDTO);

        // assert
        assertFalse(result);
    }

    /**
     * 测试团单类目不匹配Lion配置的情况
     */
    @Test
    public void testIsReservationEmpty_CategoryNotMatch() {
        // arrange
        List<AttributeDTO> attrs = new ArrayList<>();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setCategory(new DealGroupCategoryDTO());
        dealGroupDTO.getCategory().setCategoryId(0L);

        // act
        boolean result = DealAttrHelper.isReservationEmpty(attrs, dealGroupDTO);

        // assert
        assertFalse(result);
    }

    /**
     * 测试dealGroupDTO为null的情况
     */
    @Test
    public void testIsReservationEmpty_DealGroupDTONull() {
        // arrange
        List<AttributeDTO> attrs = new ArrayList<>();

        // act
        boolean result = DealAttrHelper.isReservationEmpty(attrs, null);

        // assert
        assertFalse(result);
    }

    /**
     * 测试dealGroupDTO的category为null的情况
     */
    @Test
    public void testIsReservationEmpty_CategoryNull() {
        // arrange
        List<AttributeDTO> attrs = new ArrayList<>();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();

        // act
        boolean result = DealAttrHelper.isReservationEmpty(attrs, dealGroupDTO);

        // assert
        assertFalse(result);
    }

    /**
     * 测试dealGroupDTO的category的categoryId为null的情况
     */
    @Test
    public void testIsReservationEmpty_categoryIdNull() {
        // arrange
        List<AttributeDTO> attrs = new ArrayList<>();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setCategory(new DealGroupCategoryDTO());

        // act
        boolean result = DealAttrHelper.isReservationEmpty(attrs, dealGroupDTO);

        // assert
        assertFalse(result);
    }
}
