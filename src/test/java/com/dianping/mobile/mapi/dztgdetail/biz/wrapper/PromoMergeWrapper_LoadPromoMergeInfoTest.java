package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.pay.promo.reception.service.dto.PromoMergeDTO;
import com.dianping.pay.promo.reception.service.dto.response.PromoReceptionResponse;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.concurrent.Future;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class PromoMergeWrapper_LoadPromoMergeInfoTest {

    @Mock
    private Future future;

    private PromoMergeWrapper promoMergeWrapper = new PromoMergeWrapper();

    /**
     * 测试 Future 结果为 null 的情况
     */
    @Test
    public void testLoadPromoMergeInfoFutureResultIsNull() throws Throwable {
        // arrange
        when(future.get()).thenReturn(null);
        // act
        PromoMergeDTO result = promoMergeWrapper.loadPromoMergeInfo(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 Future 结果不为 null，但 PromoReceptionResponse 的 isSuccess 方法返回 false 的情况
     */
    @Test
    public void testLoadPromoMergeInfoResponseIsNotSuccess() throws Throwable {
        // arrange
        PromoReceptionResponse<PromoMergeDTO> response = new PromoReceptionResponse<>();
        response.setSuccess(false);
        when(future.get()).thenReturn(response);
        // act
        PromoMergeDTO result = promoMergeWrapper.loadPromoMergeInfo(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 Future 结果不为 null，PromoReceptionResponse 的 isSuccess 方法返回 true 的情况
     */
    @Test
    public void testLoadPromoMergeInfoResponseIsSuccess() throws Throwable {
        // arrange
        PromoMergeDTO promoMergeDTO = new PromoMergeDTO();
        PromoReceptionResponse<PromoMergeDTO> response = new PromoReceptionResponse<>();
        response.setSuccess(true);
        response.setResult(promoMergeDTO);
        when(future.get()).thenReturn(response);
        // act
        PromoMergeDTO result = promoMergeWrapper.loadPromoMergeInfo(future);
        // assert
        assertSame(promoMergeDTO, result);
    }
}
