package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.impl;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryParam;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

public class MakeupStyleCategoryStrategyImplNewDealStyleTest {

    private MakeupStyleCategoryStrategyImpl makeupStyleCategoryStrategy;

    @Before
    public void setUp() {
        makeupStyleCategoryStrategy = new MakeupStyleCategoryStrategyImpl();
    }

    @Test
    public void testNewDealStyleDealCategoryParamIsNull() {
        boolean result = makeupStyleCategoryStrategy.newDealStyle(null);
        Assert.assertFalse(result);
    }

    @Test
    public void testNewDealStyleDealGroupDTOIsNull() {
        DealCategoryParam dealCategoryParam = DealCategoryParam.builder().build();
        boolean result = makeupStyleCategoryStrategy.newDealStyle(dealCategoryParam);
        Assert.assertFalse(result);
    }

    @Test
    public void testNewDealStyleServiceTypeIsNull() {
        DealCategoryParam dealCategoryParam = DealCategoryParam.builder().build();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setCategory(new DealGroupCategoryDTO());
        dealCategoryParam.setDealGroupDTO(dealGroupDTO);
        boolean result = makeupStyleCategoryStrategy.newDealStyle(dealCategoryParam);
        Assert.assertFalse(result);
    }

    @Test
    public void testNewDealStyleServiceTypeNotInList() {
        DealCategoryParam dealCategoryParam = DealCategoryParam.builder().build();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
        dealGroupCategoryDTO.setServiceType("其他类型");
        dealGroupDTO.setCategory(dealGroupCategoryDTO);
        dealCategoryParam.setDealGroupDTO(dealGroupDTO);
        boolean result = makeupStyleCategoryStrategy.newDealStyle(dealCategoryParam);
        Assert.assertFalse(result);
    }

    @Test
    public void testNewDealStyleServiceTypeInList() {
        DealCategoryParam dealCategoryParam = DealCategoryParam.builder().build();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
        dealGroupCategoryDTO.setServiceType("日常生活妆");
        dealGroupDTO.setCategory(dealGroupCategoryDTO);
        dealCategoryParam.setDealGroupDTO(dealGroupDTO);
        boolean result = makeupStyleCategoryStrategy.newDealStyle(dealCategoryParam);
        Assert.assertTrue(result);
    }
}
