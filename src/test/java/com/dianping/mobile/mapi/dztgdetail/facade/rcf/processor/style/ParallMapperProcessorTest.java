package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.style;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Arrays;
import java.util.Collections;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import org.mockito.InjectMocks.*;
import org.mockito.Mock.*;

public class ParallMapperProcessorTest {

    @InjectMocks
    private ParallMapperProcessor parallMapperProcessor;

    @Mock
    private MapperWrapper mapperWrapper;

    @Mock
    private Cat cat;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testProcessDealGroupDTOListNotEmpty() throws Exception {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setFutureCtx(new FutureCtx());
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        QueryDealGroupListResult result = new QueryDealGroupListResult();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        // 设置一个非空的dpDealGroupIdInt值
        dealGroupDTO.setDpDealGroupId(12345L);
        result.setList(Lists.newArrayList(dealGroupDTO));
        response.setData(result);
        when(mapperWrapper.getFutureResult(ctx.getFutureCtx().getDpIdFuture())).thenReturn(response);
        when(mapperWrapper.getDpShopIdByMtShopIdLong(ctx.getFutureCtx().getDpShopIdFuture())).thenReturn(1L);
        // act
        parallMapperProcessor.process(ctx);
        // assert
        verify(mapperWrapper, times(1)).getFutureResult(ctx.getFutureCtx().getDpIdFuture());
        verify(mapperWrapper, times(1)).getDpShopIdByMtShopIdLong(ctx.getFutureCtx().getDpShopIdFuture());
    }

    // 其他测试用例保持不变
    @Test(expected = Exception.class)
    public void testProcessException() throws Exception {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setFutureCtx(new FutureCtx());
        when(mapperWrapper.getFutureResult(ctx.getFutureCtx().getDpIdFuture())).thenThrow(new Exception());
        // act
        parallMapperProcessor.process(ctx);
    }
}
