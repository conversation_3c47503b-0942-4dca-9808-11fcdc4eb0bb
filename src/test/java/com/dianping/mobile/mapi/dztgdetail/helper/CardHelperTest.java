package com.dianping.mobile.mapi.dztgdetail.helper;

import com.sankuai.dzcard.navigation.api.dto.CardQualifyEventIdDTO;
import org.junit.Test;
import static org.junit.Assert.*;

public class CardHelperTest {

    /**
     * 测试 holdCard 方法，当传入的 CardQualifyEventIdDTO 对象为 null 时，应返回 false
     */
    @Test
    public void testHoldCardWhenCardIsNull() throws Throwable {
        // arrange
        CardQualifyEventIdDTO card = null;
        // act
        boolean result = CardHelper.holdCard(card);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 holdCard 方法，当传入的 CardQualifyEventIdDTO 对象的 userQualifyStatus 字段不等于1时，应返回 false
     */
    @Test
    public void testHoldCardWhenUserQualifyStatusIsNotOne() throws Throwable {
        // arrange
        CardQualifyEventIdDTO card = new CardQualifyEventIdDTO();
        card.setUserQualifyStatus(0);
        // act
        boolean result = CardHelper.holdCard(card);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 holdCard 方法，当传入的 CardQualifyEventIdDTO 对象的 userQualifyStatus 字段等于1时，应返回 true
     */
    @Test
    public void testHoldCardWhenUserQualifyStatusIsOne() throws Throwable {
        // arrange
        CardQualifyEventIdDTO card = new CardQualifyEventIdDTO();
        card.setUserQualifyStatus(1);
        // act
        boolean result = CardHelper.holdCard(card);
        // assert
        assertTrue(result);
    }
}
