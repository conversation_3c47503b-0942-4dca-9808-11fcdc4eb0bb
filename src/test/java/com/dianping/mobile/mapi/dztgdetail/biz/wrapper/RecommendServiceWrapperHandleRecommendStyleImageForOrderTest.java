package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.entity.Response;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.nailstyle.NailStyleItemVO;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class RecommendServiceWrapperHandleRecommendStyleImageForOrderTest {

    @InjectMocks
    private RecommendServiceWrapper recommendServiceWrapper;

    @Mock
    private ImmersiveImageWrapper immersiveImageWrapper;

    private Response<RecommendResult<RecommendDTO>> response;

    private RecommendResult<RecommendDTO> result;

    private RecommendDTO recommendDTO;

    @Before
    public void setUp() {
        response = new Response<>();
        result = new RecommendResult<>();
        recommendDTO = new RecommendDTO();
        recommendDTO.setItem("testItem");
        result.setSortedResult(Arrays.asList(recommendDTO));
        response.setResult(result);
    }

    private List<NailStyleItemVO> invokePrivateMethod(Object target, String methodName, Object... args) throws Exception {
        Method method = target.getClass().getDeclaredMethod(methodName, Response.class, int.class, Long.class, int.class);
        method.setAccessible(true);
        return (List<NailStyleItemVO>) method.invoke(target, args);
    }

    @Test
    public void testHandleRecommendStyleImageForOrderResponseIsNull() throws Throwable {
        List<NailStyleItemVO> result = invokePrivateMethod(recommendServiceWrapper, "handleRecommendStyleImageForOrder", null, 1, 1L, 1);
        assertNull(result);
    }

    @Test
    public void testHandleRecommendStyleImageForOrderResultIsNull() throws Throwable {
        response.setResult(null);
        List<NailStyleItemVO> result = invokePrivateMethod(recommendServiceWrapper, "handleRecommendStyleImageForOrder", response, 1, 1L, 1);
        assertNull(result);
    }

    @Test
    public void testHandleRecommendStyleImageForOrderSortedResultIsEmpty() throws Throwable {
        result.setSortedResult(Collections.emptyList());
        List<NailStyleItemVO> result = invokePrivateMethod(recommendServiceWrapper, "handleRecommendStyleImageForOrder", response, 1, 1L, 1);
        assertNull(result);
    }

    @Test
    public void testHandleRecommendStyleImageForOrderSortedResultIsNotEmpty() throws Throwable {
        when(immersiveImageWrapper.batchGetRecommendStyleImageForOrder(anyList(), anyString(), anyInt(), anyLong(), anyInt())).thenReturn(Collections.singletonList(new NailStyleItemVO()));
        List<NailStyleItemVO> result = invokePrivateMethod(recommendServiceWrapper, "handleRecommendStyleImageForOrder", response, 1, 1L, 1);
        assertNotNull(result);
        assertEquals(1, result.size());
    }
}
