package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.entity.ActivityConfig;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.rhino.config.LionConfig;
import com.google.common.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.junit.Assert.*;
import static org.mockito.Mockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/9/11 21:11
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({GreyUtils.class, LionConfigUtils.class,Lion.class})
@SuppressStaticInitializationFor("com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils")
public class MemberPriceWrapperTest {

    @InjectMocks
    private MemberPriceWrapper memberPriceWrapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(GreyUtils.class);
        PowerMockito.mockStatic(LionConfigUtils.class);
        PowerMockito.mockStatic(Lion.class);

    }

    @Test
    public void test() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setMpAppId("wxde8ac0a21135c07d");
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP);
        envCtx.setClientType(ClientTypeEnum.mt_weApp.getType());
        DealCtx dealCtx = new DealCtx(envCtx);
        when(GreyUtils.judgeMemberPriceCategory(dealCtx)).thenAnswer((Answer<?>) invocation -> true);
        when(LionConfigUtils.memberCardMiniProgramSwitch()).thenAnswer((Answer<?>) invocation -> true);
        boolean memberPriceProcessorEnable = memberPriceWrapper.isMemberPriceProcessorEnable(dealCtx);
        assertTrue(memberPriceProcessorEnable);
    }
}