package com.dianping.mobile.mapi.dztgdetail.helper;

import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class ImgUrlEncryptHelper_EncryptImgUrl_1_Test {

    /**
     * 测试encryptImgUrl方法，当url为空的情况
     */
    @Test
    public void testEncryptImgUrlWhenUrlIsEmpty() throws Throwable {
        // arrange
        String url = "";
        int w = 100;
        int h = 100;
        // act
        String result = ImgUrlEncryptHelper.encryptImgUrl(url, w, h);
        // assert
        assertEquals(url, result);
    }

    /**
     * 测试encryptImgUrl方法，当url不为空的情况
     */
    @Test
    @Ignore
    public void testEncryptImgUrlWhenUrlIsNotEmpty() throws Throwable {
        // arrange
        String url = "http://www.example.com/image.jpg";
        int w = 100;
        int h = 100;
        // act
        String result = ImgUrlEncryptHelper.encryptImgUrl(url, w, h);
        // assert
        // 由于encryptImgUrl方法内部调用的getEncryptedUrl方法未给出，无法进行具体的断言
    }
}
