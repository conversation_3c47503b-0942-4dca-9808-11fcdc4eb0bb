package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.dzim.cliententry.dto.ClientEntryDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.Future;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DzImWrapper_BatchGetImmersiveImgImUrlTest {

    @Mock
    private Future future;

    /**
     * 测试 Future 对象返回的 ClientEntryDTO 列表为空的情况
     */
    @Test
    public void testBatchGetImmersiveImgImUrlWhenFutureResultIsEmpty() throws Exception {
        // arrange
        DzImWrapper dzImWrapper = new DzImWrapper();
        when(future.get()).thenReturn(null);
        // act
        Map<String, String> result = dzImWrapper.batchGetImmersiveImgImUrl(future);
        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试 Future 对象返回的 ClientEntryDTO 列表不为空，但所有 ClientEntryDTO 对象的 show 属性都为 false 的情况
     */
    @Test
    public void testBatchGetImmersiveImgImUrlWhenAllClientEntryDTOsAreNotShow() throws Exception {
        // arrange
        DzImWrapper dzImWrapper = new DzImWrapper();
        ClientEntryDTO clientEntryDTO = new ClientEntryDTO();
        clientEntryDTO.setShow(false);
        when(future.get()).thenReturn(Arrays.asList(clientEntryDTO));
        // act
        Map<String, String> result = dzImWrapper.batchGetImmersiveImgImUrl(future);
        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试 Future 对象返回的 ClientEntryDTO 列表不为空，且至少有一个 ClientEntryDTO 对象的 show 属性为 true 的情况
     */
    @Test
    public void testBatchGetImmersiveImgImUrlWhenAtLeastOneClientEntryDTOIsShow() throws Exception {
        // arrange
        DzImWrapper dzImWrapper = new DzImWrapper();
        ClientEntryDTO clientEntryDTO1 = new ClientEntryDTO();
        clientEntryDTO1.setShow(false);
        ClientEntryDTO clientEntryDTO2 = new ClientEntryDTO();
        clientEntryDTO2.setShow(true);
        clientEntryDTO2.setSendUnitId("1");
        clientEntryDTO2.setEntryUrl("url1");
        when(future.get()).thenReturn(Arrays.asList(clientEntryDTO1, clientEntryDTO2));
        // act
        Map<String, String> result = dzImWrapper.batchGetImmersiveImgImUrl(future);
        // assert
        assertEquals(1, result.size());
        assertEquals("url1", result.get("1"));
    }
}
