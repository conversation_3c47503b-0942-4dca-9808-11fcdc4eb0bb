package com.dianping.mobile.mapi.dztgdetail.facade.rcf.flash;

import com.alibaba.fastjson.JSON;
import com.dianping.mobile.mapi.dztgdetail.biz.service.cellarservice.compoment.DealDetailCellarComponent;
import com.dianping.mobile.mapi.dztgdetail.biz.service.cellarservice.dealdetail.DealGroupDTOCellarService;
import com.dianping.mobile.mapi.dztgdetail.biz.service.cellarservice.dealdetail.req.DealGroupCacheRequest;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @create 2024/11/11 17:20
 */
@RunWith(MockitoJUnitRunner.class)
public class DealGroupDTOCellarServiceTest {
    @InjectMocks
    private DealGroupDTOCellarService dealGroupDTOCellarService;
    @Mock
    private DealDetailCellarComponent dealDetailCellarService;

    
    @Test
    public void setDealGroupDTOCellarServiceTest() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setCategory(new DealGroupCategoryDTO());
//        Mockito.when(queryCenterWrapper.getDealGroupDTO(future)).thenReturn(dealGroupDTO) ;
        dealGroupDTOCellarService.parseCacheValue("");
        dealGroupDTOCellarService.parseCacheValue(JSON.toJSONString(dealGroupDTO));
        dealGroupDTOCellarService.getValueFromCellar("key");
        DealGroupCacheRequest request = new DealGroupCacheRequest();
        request.setDealGroupId(123);
        request.setMT(true);
        dealGroupDTOCellarService.buildKey(request);
        dealGroupDTOCellarService.saveOrUpdate(request, dealGroupDTO);
        Assert.notNull(dealGroupDTO);
    }
}
