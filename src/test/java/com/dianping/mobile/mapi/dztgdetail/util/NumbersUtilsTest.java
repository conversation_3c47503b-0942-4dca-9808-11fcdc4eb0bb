package com.dianping.mobile.mapi.dztgdetail.util;

import org.junit.Test;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * <AUTHOR>
 * @date 2024-01-17
 * @desc NumbersUtils的测试类
 */
public class NumbersUtilsTest {
    /**
     * 测试 num 为 null 的情况
     */
    @Test
    public void testGreaterThanZeroWhenNumIsNull() throws Throwable {
        // arrange
        Integer num = null;

        // act
        boolean result = NumbersUtils.greaterThanZero(num);

        // assert
        assertFalse(result);
    }

    /**
     * 测试 num 为正数的情况
     */
    @Test
    public void testGreaterThanZeroWhenNumIsPositive() throws Throwable {
        // arrange
        Integer num = 1;

        // act
        boolean result = NumbersUtils.greaterThanZero(num);

        // assert
        assertTrue(result);
    }

    /**
     * 测试 num 为 0 的情况
     */
    @Test
    public void testGreaterThanZeroWhenNumIsZero() throws Throwable {
        // arrange
        Integer num = 0;

        // act
        boolean result = NumbersUtils.greaterThanZero(num);

        // assert
        assertFalse(result);
    }

    /**
     * 测试 num 为负数的情况
     */
    @Test
    public void testGreaterThanZeroWhenNumIsNegative() throws Throwable {
        // arrange
        Integer num = -1;

        // act
        boolean result = NumbersUtils.greaterThanZero(num);

        // assert
        assertFalse(result);
    }
}
