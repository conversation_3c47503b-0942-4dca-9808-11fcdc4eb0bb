package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.sales.common.datatype.ProductParam;
import com.dianping.deal.sales.common.datatype.SalesDisplayDTO;
import com.dianping.deal.sales.common.datatype.SalesDisplayRequest;
import com.dianping.deal.sales.display.api.service.ProductSceneSalesDisplayService;
import com.dianping.deal.sales.display.api.service.ShopSceneSalesDisplayService;
import com.dianping.mobile.mapi.dztgdetail.common.ShopUuidUtils;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.google.common.collect.Lists;
import com.sankuai.feitianplus.data.onedata.api.thrift.domain.QueryResultSync;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;
import java.util.concurrent.Future;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DealStockSaleWrapperTest {

    @InjectMocks
    private DealStockSaleWrapper dealStockSaleWrapper;

    @Mock
    private Future<Map<ProductParam, SalesDisplayDTO>> future;

    @Mock
    private ProductSceneSalesDisplayService productSceneSalesDisplayServiceFuture;

    @Mock
    private ShopSceneSalesDisplayService shopSceneSalesDisplayServiceFuture;

    private MockedStatic<ShopUuidUtils> shopUuidUtilsMockedStatic;

    public DealStockSaleWrapperTest() {
        MockitoAnnotations.initMocks(this);
    }

    private MockedStatic<FutureFactory> futureFactoryMockedStatic;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        shopUuidUtilsMockedStatic = Mockito.mockStatic(ShopUuidUtils.class);
        futureFactoryMockedStatic = mockStatic(FutureFactory.class);
    }

    @After
    public void tearDowm() {
        shopUuidUtilsMockedStatic.close();
        futureFactoryMockedStatic.close();
    }

    /**
     * Test case when the future list is empty.
     */
    @Test
    public void testGetShopSceneSalesDisplayEmptyList() throws Throwable {
        Map<ProductParam, SalesDisplayDTO> result = dealStockSaleWrapper.getShopSceneSalesDisplay(Collections.emptyList());
        assertEquals(new HashMap<>(), result);
    }

    /**
     * Test case when the future list is not empty, but all Future objects are null.
     */
    @Test
    public void testGetShopSceneSalesDisplayAllNull() throws Throwable {
        Map<ProductParam, SalesDisplayDTO> result = dealStockSaleWrapper.getShopSceneSalesDisplay(Arrays.asList(null, null));
        assertEquals(new HashMap<>(), result);
    }

    /**
     * Test case when the future list is not empty, and at least one Future object is not null.
     */
    @Test
    public void testGetShopSceneSalesDisplayNotNull() throws Throwable {
        Map<ProductParam, SalesDisplayDTO> futureResult = new HashMap<>();
        // Assuming a specific setup for futureResult to match expected aggregation logic
        // Assuming constructor and setters as needed
        ProductParam param = new ProductParam();
        // Assuming constructor and setters as needed
        SalesDisplayDTO dto = new SalesDisplayDTO();
        futureResult.put(param, dto);
        when(future.get()).thenReturn(futureResult);
        Map<ProductParam, SalesDisplayDTO> result = dealStockSaleWrapper.getShopSceneSalesDisplay(Collections.singletonList(future));
        assertEquals(futureResult, result);
    }

    /**
     * 测试 singleRequest 为 null 的情况
     */
    @Test
    public void testPreUnifiedStockFutureNullRequest() throws Throwable {
        // arrange
        SalesDisplayRequest singleRequest = null;
        // act
        Future result = dealStockSaleWrapper.preUnifiedStockFuture(singleRequest);
        // assert
        assertNull(result);
    }

    /**
     * 测试 singleRequest 不为 null，且 getSales 方法正常执行的情况
     */
    @Test
    public void testPreUnifiedStockFutureNormal() throws Throwable {
        // arrange
        SalesDisplayRequest singleRequest = new SalesDisplayRequest();
        futureFactoryMockedStatic.when(FutureFactory::getFuture).thenReturn(null);
        // act
        Future result = dealStockSaleWrapper.preUnifiedStockFuture(singleRequest);
        // assert
        verify(productSceneSalesDisplayServiceFuture, times(1)).getSales(singleRequest);
        assertNull(result);
    }

    /**
     * 测试 singleRequest 不为 null，但 getSales 方法执行过程中抛出异常的情况
     */
    @Test
    public void testPreUnifiedStockFutureException() throws Throwable {
        // arrange
        SalesDisplayRequest singleRequest = new SalesDisplayRequest();
        doThrow(new RuntimeException()).when(productSceneSalesDisplayServiceFuture).getSales(singleRequest);
        // act
        Future result = dealStockSaleWrapper.preUnifiedStockFuture(singleRequest);
        // assert
        verify(productSceneSalesDisplayServiceFuture, times(1)).getSales(singleRequest);
        assertNull(result);
    }

    @Test
    public void testPreShopSceneSalesDisplayWhenOnlineDealGroupIdsIsEmpty() throws Throwable {
        List<Long> onlineDealGroupIds = Arrays.asList();
        Long shopId = 1L;
        int cityId = 1;
        int platform = 1;
        List<Future> result = dealStockSaleWrapper.preShopSceneSalesDisplay(onlineDealGroupIds, shopId, cityId, platform);
        assertEquals(0, result.size());
    }

    @Test
    public void testPreShopSceneSalesDisplayWhenOnlineDealGroupIdsIsNotEmptyAndMultiGetSalesSuccess() throws Throwable {
        List<Long> onlineDealGroupIds = Arrays.asList(1L, 2L, 3L);
        Long shopId = 1L;
        int cityId = 1;
        int platform = 1;
        // Assuming the method under test adds a Future to the list for each successful call
        // Mock the behavior
        when(shopSceneSalesDisplayServiceFuture.multiGetSales(any(SalesDisplayRequest.class))).thenReturn(null);
        List<Future> result = dealStockSaleWrapper.preShopSceneSalesDisplay(onlineDealGroupIds, shopId, cityId, platform);
        // Assuming the expected behavior is to have a Future for each partition (or call)
        // Adjust based on actual behavior
        assertEquals(1, result.size());
        verify(shopSceneSalesDisplayServiceFuture, times(1)).multiGetSales(any(SalesDisplayRequest.class));
    }

    @Test
    public void testPreShopSceneSalesDisplayWhenOnlineDealGroupIdsIsNotEmptyAndMultiGetSalesFail() throws Throwable {
        List<Long> onlineDealGroupIds = Arrays.asList(1L, 2L, 3L);
        Long shopId = 1L;
        int cityId = 1;
        int platform = 1;
        doThrow(new RuntimeException()).when(shopSceneSalesDisplayServiceFuture).multiGetSales(any(SalesDisplayRequest.class));
        List<Future> result = dealStockSaleWrapper.preShopSceneSalesDisplay(onlineDealGroupIds, shopId, cityId, platform);
        // Assuming the method under test does not add a Future to the list in case of failure
        assertEquals(0, result.size());
        verify(shopSceneSalesDisplayServiceFuture, times(1)).multiGetSales(any(SalesDisplayRequest.class));
    }

    @Test
    public void testMultiGetBySpuProductFuture() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setDpId(1);
        ctx.setDpCityId(1);
        shopUuidUtilsMockedStatic.when(() -> ShopUuidUtils.getUuidById(1L)).thenReturn("1");
        ctx.setDpLongShopId(1L);
        dealStockSaleWrapper.multiGetBySpuProductFuture(ctx);
        Assert.assertTrue(ctx.getDpCityId() > 0);
    }

    @Test
    public void testGetOrderReserveCount_RespIsSuccess() throws Exception {
        String json = "{\n" +
                "\t\"ifSuccess\":true,\n" +
                "\t\"code\":100,\n" +
                "\t\"data\":\"[[\\\"753522834\\\",\\\"45653\\\"]]\",\n" +
                "\t\"queryResultTitles\":[\n" +
                "\t\t{\n" +
                "\t\t\t\"columnName\":\"product_id\"\n" +
                "\t\t},\n" +
                "\t\t{\n" +
                "\t\t\t\"columnName\":\"all_reserve_order_cnt\"\n" +
                "\t\t}\n" +
                "\t],\n" +
                "\t\"pageInfo\":{\n" +
                "\t\t\"pageType\":0,\n" +
                "\t\t\"pageNo\":0,\n" +
                "\t\t\"totalPage\":0,\n" +
                "\t\t\"pageSize\":0,\n" +
                "\t\t\"totalRecord\":0\n" +
                "\t},\n" +
                "\t\"errorMsg\":\"成功\",\n" +
                "\t\"queryId\":\"a38b7abc-69d4-4095-XXX\"\n" +
                "}";
        QueryResultSync resp = GsonUtils.fromJsonString(json, QueryResultSync.class);
        Future<QueryResultSync> futureMock = mock(Future.class);
        DealCtx ctxMock = mock(DealCtx.class);
        when(ctxMock.getDpId()).thenReturn(753522834);
        when(futureMock.get()).thenReturn(resp);
        long result = dealStockSaleWrapper.getOrderReserveCount(futureMock, ctxMock);
        assertEquals(45653, result);
    }

}
