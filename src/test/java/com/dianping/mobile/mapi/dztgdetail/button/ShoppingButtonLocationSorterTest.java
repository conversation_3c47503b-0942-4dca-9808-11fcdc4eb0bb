package com.dianping.mobile.mapi.dztgdetail.button;

import com.dianping.mobile.mapi.dztgdetail.button.shoppingcart.ShoppingButtonLocationSorter;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
import com.dianping.mobile.mapi.dztgdetail.entity.CostEffectivePinTuan;
import com.dianping.mobile.mapi.dztgdetail.util.JsonUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * @Author: <EMAIL>
 * @Date: 2024/5/15
 */
@RunWith(MockitoJUnitRunner.class)
public class ShoppingButtonLocationSorterTest {
    @InjectMocks
    private ShoppingButtonLocationSorter shoppingButtonLocationSorter;

    @Test
    public void testSort() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealBuyBar buyBar = JsonUtils.fromJson("{\"styleType\":4,\"buyType\":0,\"buyBtns\":[{\"btnEnable\":true,\"btnTitle\":\"立即抢购\",\"btnDesc\":\"[{\\\"text\\\":\\\"门市价 ￥\\\",\\\"textsize\\\":12,\\\"textcolor\\\":\\\"#FF999999\\\",\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"textstyle\\\":\\\"Default\\\",\\\"strikethrough\\\":false,\\\"underline\\\":false},{\\\"text\\\":\\\"111\\\",\\\"textsize\\\":12,\\\"textcolor\\\":\\\"#FF999999\\\",\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"textstyle\\\":\\\"Default\\\",\\\"strikethrough\\\":false,\\\"underline\\\":false}]\",\"priceStr\":\"99\",\"redirectUrl\":\"imeituan://www.meituan.com/gc/createorder?dealid=423970711&shopid=40153772&source=cost_effective&promotionchannel=2&trafficflag=costEffective&is_sku=0&pricecipher=vH4THqgHbPMHcB_0K6VVVgSXECnPRnJE8xuY2dLmjsNUyMi99dKv9h1AB0xAIQ6FQ6jAbr-QUzThOQ3hqf35KuZAhF6dGmBhkYqMDWYn9AV6FrX2ndK8HQ7FAMNogEjgBAFX_EHql7jICtW-eKGZbZ9J0VDY6-ikY4W7nduiQfDHf96nCA9FTuMHc5vKBsicc1U27RcbNwuw9IerIslbk4TUJVAujcFjFd2BfTZN9Nedy9iM58On_Y_HJo6Ne26BQML7xhAi9aIo45JbVo2ECFH_ioGBW2dZEJfzsAcERjNDC3CxamFqKafZou8N5B2mVesBGRQbmVUcB5UMLvnxCr87lT4H2CCbuQZRNOehZqW65nwCIQtE3dgmx0srcP-gGnmVGv7LOiToU20qhgZGSw\",\"detailBuyType\":1,\"btnIcons\":[],\"btnText\":\"￥99 立即购买\",\"addShoppingCartStatus\":1,\"priceRuleModule\":{\"priceRuleType\":1,\"priceRuleTags\":[\"团购价\",\"￥99\"],\"promoDesc\":\"共省￥12\"}},{\"btnEnable\":true,\"btnTitle\":\"直接购买\",\"priceStr\":\"99\",\"redirectUrl\":\"imeituan://www.meituan.com/gc/createorder?dealid=423970711&shopid=40153772&source=cost_effective&promotionchannel=2&trafficflag=costEffective&is_sku=0&pricecipher=vH4THqgHbPMHcB_0K6VVVgSXECnPRnJE8xuY2dLmjsNUyMi99dKv9h1AB0xAIQ6FQ6jAbr-QUzThOQ3hqf35KuZAhF6dGmBhkYqMDWYn9AV6FrX2ndK8HQ7FAMNogEjgBAFX_EHql7jICtW-eKGZbZ9J0VDY6-ikY4W7nduiQfDHf96nCA9FTuMHc5vKBsicc1U27RcbNwuw9IerIslbk4TUJVAujcFjFd2BfTZN9Nedy9iM58On_Y_HJo6Ne26BQML7xhAi9aIo45JbVo2ECFH_ioGBW2dZEJfzsAcERjNDC3CxamFqKafZou8N5B2mVesBGRQbmVUcB5UMLvnxCr87lT4H2CCbuQZRNOehZqW65nwCIQtE3dgmx0srcP-gGnmVGv7LOiToU20qhgZGSw\",\"detailBuyType\":11,\"addShoppingCartStatus\":0,\"priceRuleModule\":{\"priceRuleType\":11}},{\"btnEnable\":true,\"btnTitle\":\"拼团中\",\"priceStr\":\"9\",\"detailBuyType\":11,\"btnIcons\":[{\"title\":\"还差1人成团\",\"titleColor\":\"#FFFFFF\",\"borderColor\":\"\",\"bgColor\":\"#FF2727\",\"style\":\"#FF2727\",\"type\":1}],\"addShoppingCartStatus\":0,\"priceRuleModule\":{\"priceRuleType\":11},\"externalJumpUrl\":\"imeituan://www.meituan.com/mrn?mrn_biz=meishi&mrn_entry=group-pintuan-result&mrn_component=main&orderGroupId=4697254419937404941&scene=share_page\",\"countDownTs\":1715927743000,\"buyBarShareInfo\":{\"jumpUrl\":\"/index/pages/h5/h5?weburl=http%3A%2F%2Fawp.hfe.st.meituan.com%2Fdfe%2Fduo-page%2Fgroup-pintuan-result%2Fweb%2Findex.html%3ForderGroupId%3D4697254419937404941%26scene%3Dshare_page&f_openId=1&f_userId=1&f_token=1&f_userId=1&f_ci=1&f_pos=1&barcol=FF112E&barfcol=FFFFFF&title=特价团购拼团\",\"title\":\"我在拼团中，快来加入我\",\"dealImage\":\"https://p0.meituan.net/dpmerchantpic/34da0b33d9f878a818b28dc1fadeb096286116.png%40500w_400h_1e_1c_1l%7Cwatermark%3D0\",\"templateId\":48}}],\"buyBanner\":{\"iconUrl\":\"https://p0.meituan.net/ingee/fbcd0deada26f83680d6470010a3bc081069.png\",\"content\":\"[{\\\"text\\\":\\\"邀请好友，一起拼团购买，享超低价！\\\",\\\"textsize\\\":12,\\\"textcolor\\\":\\\"#FF2727\\\",\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"textstyle\\\":\\\"Default\\\",\\\"strikethrough\\\":false,\\\"underline\\\":false}]\",\"leadAction\":0,\"show\":true,\"bannerType\":1,\"countDownTs\":1715927743000,\"backGroundColor\":[\"#FFF3F0\",\"#FFF3F0\"]},\"pinTuanMemberModule\":{\"generalInfo\":\"[{\\\"text\\\":\\\"还差\\\",\\\"textsize\\\":11,\\\"textcolor\\\":\\\"#222222\\\",\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"textstyle\\\":\\\"Bold\\\",\\\"strikethrough\\\":false,\\\"underline\\\":false},{\\\"text\\\":\\\"1\\\",\\\"textsize\\\":11,\\\"textcolor\\\":\\\"#222222\\\",\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"textstyle\\\":\\\"Bold\\\",\\\"strikethrough\\\":false,\\\"underline\\\":false},{\\\"text\\\":\\\"人成团\\\",\\\"textsize\\\":11,\\\"textcolor\\\":\\\"#222222\\\",\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"textstyle\\\":\\\"Bold\\\",\\\"strikethrough\\\":false,\\\"underline\\\":false}]\",\"avatars\":[\"https://p0.meituan.net/travelcube/53ed7c702791795af246258f873ebe295918.png\",\"https://p0.meituan.net/travelcube/53ed7c702791795af246258f873ebe295918.png\",\"https://p0.meituan.net/ingee/a4eb8f82456c3c51ea66ffffb73f9ae14106.png\"]}}", DealBuyBar.class);
        CostEffectivePinTuan costEffectivePinTuan = JsonUtils.fromJson("{\"sceneType\":5,\"pinTuanActivityId\":\"29855\",\"shareToken\":\"4697254419937404941\",\"cePinTuanScene\":true,\"pinTuanOpened\":true,\"activePinTuan\":true,\"inPinTuan\":true,\"groupSuccCountMin\":3,\"hasHelpCount\":1,\"avatars\":[\"https://p0.meituan.net/travelcube/53ed7c702791795af246258f873ebe295918.png\",\"https://p0.meituan.net/travelcube/53ed7c702791795af246258f873ebe295918.png\",\"https://p0.meituan.net/ingee/a4eb8f82456c3c51ea66ffffb73f9ae14106.png\"],\"expireTime\":1715927743000,\"ruleInfoPOS\":[{\"title\":\"活动玩法\",\"content\":\"1、售卖渠道\\n美团APP，美团小程序。\\n\\n2、成团规则\\n开团有效时间内，成功邀请好友参与拼团（完成支付）即拼团成功。开团有效时间结束后，如果拼团人数不足，则拼团失败，相应订单自动取消，订单款项3日内全部原路退回。\\n\\n3、参与限制\\n1）发起拼团：活动城市全部用户；\\n2）参与拼团：仅限美食团购新用户（即365天内没有在美食团购下过订单的用户）。\\n\\n4、 温馨提示\\n1）拼团不局限于某一个商品，发起人和参与人下单可拼商品中的任意一个，均可参团；\\n2）如遇拼团过程中无法手动操作退款，拼团失败后自动退款，成功后也可手动退款；\\n3）可拼商品库存有限，抢完为止（商品拼团价格以活动页面每日实际数据为准）。\"},{\"title\":\"活动规则\",\"content\":\"1）同一账号、手机号、身份证号、支付账号、移动设备等均视为同一活动用户。\\n2）活动优惠仅限本账户使用，不得转赠、兑现。\\n3）参与活动用户不得进行虚假交易，不得通过各种方式参与或协助套现美团优惠资源，不得以任何形式的软件或其他恶意方式参与本活动，不得实施违反诚实信用的行为及实施其他非真实活动的作弊行为。如美团平台基于合理理由认定用户存在上述违规行为的，有权取消用户参与本活动的资格，撤销违规交易并收回已发放权益。\\n4）若活动中遭遇大面积作弊、通讯路线故障或计算机大规模瘫痪等原因导致难以继续开展本活动，美团平台保留取消、修改或暂停本活动的权利。\\n5）客服热线电话：10107888。\\n\"}],\"limitNewCustomJoin\":false,\"promotionId\":1900092820,\"pinTuanPassParamConfig\":{\"title\":\"我在拼团中，快来加入我\",\"defaultAvatar\":\"https://p0.meituan.net/ingee/a4eb8f82456c3c51ea66ffffb73f9ae14106.png\",\"templateId\":48},\"helpSuccCountMin\":2}", CostEffectivePinTuan.class);
        ctx.setBuyBar(buyBar);
        ctx.setCostEffectivePinTuan(costEffectivePinTuan);

        shoppingButtonLocationSorter.sort(ctx);
        Assert.assertTrue("直接购买".equals(ctx.getBuyBar().getBuyBtns().get(0).getBtnTitle()));
    }
}
