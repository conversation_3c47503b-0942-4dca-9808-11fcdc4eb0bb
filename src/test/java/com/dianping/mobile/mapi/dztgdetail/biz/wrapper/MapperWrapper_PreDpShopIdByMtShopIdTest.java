package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.poi.relation.service.api.PoiRelationService;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.concurrent.Future;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class MapperWrapper_PreDpShopIdByMtShopIdTest {

    @InjectMocks
    private MapperWrapper mapperWrapper;

    @Mock
    private PoiRelationService poiRelationServiceFuture;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试mtShopId小于等于0的情况
     */
    @Test
    public void testPreDpShopIdByMtShopIdLessThanOrEqualToZero() {
        long mtShopId = 0;
        Future result = mapperWrapper.preDpShopIdByMtShopId(mtShopId);
        assertNull(result);
    }

    /**
     * 测试mtShopId大于0且poiRelationServiceFuture.queryDpByMtIdL(mtShopId)方法调用成功的情况
     */
    @Test
    @Ignore
    public void testPreDpShopIdByMtShopIdGreaterThanZeroAndSuccess() throws Exception {
        long mtShopId = 1;
        Future expected = FutureFactory.getFuture();
        when(poiRelationServiceFuture.queryDpByMtIdL(mtShopId)).thenReturn(null);
        Future result = mapperWrapper.preDpShopIdByMtShopId(mtShopId);
        verify(poiRelationServiceFuture, times(1)).queryDpByMtIdL(mtShopId);
        assertSame(expected, result);
    }

    /**
     * 测试mtShopId大于0且poiRelationServiceFuture.queryDpByMtIdL(mtShopId)方法调用发生异常的情况
     */
    @Test
    public void testPreDpShopIdByMtShopIdGreaterThanZeroAndException() throws Exception {
        long mtShopId = 1;
        when(poiRelationServiceFuture.queryDpByMtIdL(mtShopId)).thenThrow(new RuntimeException());
        Future result = mapperWrapper.preDpShopIdByMtShopId(mtShopId);
        verify(poiRelationServiceFuture, times(1)).queryDpByMtIdL(mtShopId);
        assertNull(result);
    }
}
