package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.ServiceguaranteequeryRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ServiceGuarantee.ServiceGuaranteeDTO;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.google.gson.reflect.TypeToken;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.service.DpPoiService;
import com.sankuai.sinai.data.api.service.MtPoiService;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ServiceGuaranteeQueryFacadeGetServiceGuaranteeDTOByDpShopIdTest {

    @InjectMocks
    private ServiceGuaranteeQueryFacade serviceGuaranteeQueryFacade;

    @Mock
    private DpPoiService sinaiDpPoiService;

    private ServiceguaranteequeryRequest request;

    private EnvCtx envCtx;

    @Mock
    private MtPoiService sinaiMtPoiService;

    @Before
    public void setUp() {
        request = new ServiceguaranteequeryRequest();
        envCtx = new EnvCtx();
    }

    private ServiceGuaranteeDTO invokeGetServiceGuaranteeDTOByDpShopId(Long shopId, Map<Integer, ServiceGuaranteeDTO> serviceGuaranteeDTOMap) throws Exception {
        Method method = ServiceGuaranteeQueryFacade.class.getDeclaredMethod("getServiceGuaranteeDTOByDpShopId", Long.class, Map.class);
        method.setAccessible(true);
        return (ServiceGuaranteeDTO) method.invoke(serviceGuaranteeQueryFacade, shopId, serviceGuaranteeDTOMap);
    }

    @Test
    public void testGetServiceGuaranteeDTOByDpShopIdWhenDpPoiDTOListIsEmpty() throws Throwable {
        when(sinaiDpPoiService.findShopsByShopIds(any())).thenReturn(Collections.emptyList());
        ServiceGuaranteeDTO result = invokeGetServiceGuaranteeDTOByDpShopId(1L, new HashMap<>());
        assertNotNull(result);
    }

    @Test
    public void testGetServiceGuaranteeDTOByDpShopIdWhenDpPoiDTOListIsNotEmptyButMapNotContainsShopId() throws Throwable {
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setShopId(2L);
        when(sinaiDpPoiService.findShopsByShopIds(any())).thenReturn(Collections.singletonList(dpPoiDTO));
        ServiceGuaranteeDTO result = invokeGetServiceGuaranteeDTOByDpShopId(1L, new HashMap<>());
        assertNotNull(result);
    }

    @Test
    public void testGetServiceGuaranteeDTOByDpShopIdWhenDpPoiDTOListIsNotEmptyAndMapContainsShopIdButCityIdIsNull() throws Throwable {
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setShopId(1L);
        dpPoiDTO.setCityId(null);
        when(sinaiDpPoiService.findShopsByShopIds(any())).thenReturn(Collections.singletonList(dpPoiDTO));
        ServiceGuaranteeDTO result = invokeGetServiceGuaranteeDTOByDpShopId(1L, new HashMap<>());
        assertNotNull(result);
    }

    @Test
    public void testGetServiceGuaranteeDTOByDpShopIdWhenDpPoiDTOListIsNotEmptyAndMapContainsShopIdAndCityIdIsNotNullButServiceGuaranteeDTOMapNotContainsCityId() throws Throwable {
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setShopId(1L);
        dpPoiDTO.setCityId(1);
        when(sinaiDpPoiService.findShopsByShopIds(any())).thenReturn(Collections.singletonList(dpPoiDTO));
        Map<Integer, ServiceGuaranteeDTO> serviceGuaranteeDTOMap = new HashMap<>();
        serviceGuaranteeDTOMap.put(-100, new ServiceGuaranteeDTO());
        ServiceGuaranteeDTO result = invokeGetServiceGuaranteeDTOByDpShopId(1L, serviceGuaranteeDTOMap);
        assertNotNull(result);
    }

    @Test
    public void testGetServiceGuaranteeDTOByDpShopIdWhenDpPoiDTOListIsNotEmptyAndMapContainsShopIdAndCityIdIsNotNullAndServiceGuaranteeDTOMapContainsCityId() throws Throwable {
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setShopId(1L);
        dpPoiDTO.setCityId(1);
        when(sinaiDpPoiService.findShopsByShopIds(any())).thenReturn(Collections.singletonList(dpPoiDTO));
        Map<Integer, ServiceGuaranteeDTO> serviceGuaranteeDTOMap = new HashMap<>();
        ServiceGuaranteeDTO serviceGuaranteeDTO = new ServiceGuaranteeDTO();
        serviceGuaranteeDTOMap.put(1, serviceGuaranteeDTO);
        ServiceGuaranteeDTO result = invokeGetServiceGuaranteeDTOByDpShopId(1L, serviceGuaranteeDTOMap);
        assertEquals(serviceGuaranteeDTO, result);
    }

    @Test
    public void testQueryServiceGuaranteeDTO_DealgroupIdNotInWhitelist() throws Throwable {
        request.setDealgroupId(1);
        // Assuming the existence of a method to set the environment or mocking the behavior
        // envCtx.setMt(true); // Removed due to non-existent method
        ServiceGuaranteeDTO result = serviceGuaranteeQueryFacade.queryServiceGuaranteeDTO(request, envCtx);
        assertNotNull(result);
    }

    @Test
    public void testQueryServiceGuaranteeDTO_CityConfigurationEmpty() throws Throwable {
        request.setDealgroupId(1);
        // Assuming the existence of a method to set the environment or mocking the behavior
        // envCtx.setMt(true); // Removed due to non-existent method
        ServiceGuaranteeDTO result = serviceGuaranteeQueryFacade.queryServiceGuaranteeDTO(request, envCtx);
        assertNotNull(result);
    }

    @Test
    public void testQueryServiceGuaranteeDTO_ShopIdNotInCityConfiguration() throws Throwable {
        request.setDealgroupId(1);
        request.setShopidstr("123");
        // Assuming the existence of a method to set the environment or mocking the behavior
        // envCtx.setMt(true); // Removed due to non-existent method
        ServiceGuaranteeDTO result = serviceGuaranteeQueryFacade.queryServiceGuaranteeDTO(request, envCtx);
        assertNotNull(result);
    }
}
