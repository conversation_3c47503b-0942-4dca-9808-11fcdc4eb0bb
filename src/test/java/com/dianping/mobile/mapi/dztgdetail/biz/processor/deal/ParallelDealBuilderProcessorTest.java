package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PriceDisplayWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Cons;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.PriceContext;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.ShopReviewCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CouponItemPackageDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CouponValidStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DisplayPositionEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.Guarantee;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.LayerConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PromoDetailModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ShopPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.PromoActivityInfoVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealstruct.PnPurchaseNoteDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DztgShareModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule;
import com.dianping.mobile.mapi.dztgdetail.entity.CostEffectivePinTuan;
import com.dianping.mobile.mapi.dztgdetail.entity.ReminderExtendConfig;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.ReminderInfoBuilderService;
import com.dianping.mobile.mapi.dztgdetail.helper.ShopReviewHelper;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.IndustryRcfTrackUtils;
import com.dianping.mobile.mapi.dztgdetail.util.JsonUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.beautycard.navigation.api.dto.BeautyMatrixRequestDTO;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import com.sankuai.general.product.query.center.client.dto.rule.buy.DealGroupBuyRuleDTO;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MagicalMemberTagTextDTO;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.APP_KEY;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @since 2023/9/26 14:20
 */
public class ParallelDealBuilderProcessorTest {

    private ParallDealBuilderProcessor parallDealBuilderProcessor;
    private DealCtx ctx;
    private PriceContext priceContext;
    private DealBuyBtn onlyDealBuyBtn;
    ReminderInfoBuilderService reminderInfoBuilderService = new ReminderInfoBuilderService();


    @Mock
    private PriceDisplayDTO mockPriceDisplayDTO;

    private MockedStatic<DealUtils> dealUtilsMockedStatic;

    private MockedStatic<Lion> lionMockedStatic;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        parallDealBuilderProcessor = new ParallDealBuilderProcessor();
        ctx = mock(DealCtx.class);
        priceContext = mock(PriceContext.class);
        onlyDealBuyBtn = new DealBuyBtn(true, "测试按钮");
        dealUtilsMockedStatic = mockStatic(DealUtils.class);
        lionMockedStatic = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        dealUtilsMockedStatic.close();
        lionMockedStatic.close();
    }

    @Test
    public void testSetBusinessStyle() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setRequestSource("mvideo");
        ExtraStyleProcessor processor = new ExtraStyleProcessor();
        processor.setBusinessStyle(ctx);
        Assert.assertNotNull(ctx);
    }

    /**
     * 测试场景：onlyDealBuyBtn.redirectUrl 不为空，但 ctx.getPriceContext().isHasExclusiveDeduction() 为 false
     */
    @Test
    @Ignore
    public void testLimitInfo() {
        // arrange
        String dealGroupJson = "{\"dpDealGroupId\":1021008579,\"mtDealGroupId\":1021008579,\"basic\":{\"categoryId\":501,\"title\":\"复购供给（不限）\",\"brandName\":\"北新泾美发美甲\",\"titleDesc\":\"仅售80元，价值100元复购供给（不限）！\",\"beginSaleDate\":\"2024-03-01 10:52:37\",\"endSaleDate\":\"2025-03-01 10:52:00\",\"status\":1,\"saleChannel\":0,\"salePlatform\":3,\"sourceId\":102,\"tradeType\":3},\"image\":{\"defaultPicPath\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/e65296ec1aad482428e80468867b4b90105930.jpg\",\"allPicPaths\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/e65296ec1aad482428e80468867b4b90105930.jpg\"},\"category\":{\"categoryId\":501,\"serviceType\":\"洗剪吹\",\"serviceTypeId\":809},\"serviceProject\":{\"title\":\"团购详情\",\"salePrice\":\"80.00\",\"marketPrice\":\"100.00\",\"mustGroups\":[{\"groups\":[{\"skuId\":0,\"categoryId\":3111,\"name\":\"洗剪吹\",\"amount\":1,\"marketPrice\":\"100.0\",\"status\":10,\"attrs\":[]}]}],\"optionGroups\":[],\"structType\":\"uniform-structure-table\"},\"channel\":{\"channelId\":5,\"channelEn\":\"beauty\",\"channelCn\":\"丽人\",\"channelGroupId\":2,\"channelGroupEn\":\"general\",\"channelGroupCn\":\"到店综合\"},\"attrs\":[{\"name\":\"product_finish_date\",\"value\":[\"1970-01-01 08:00:00\"],\"source\":0},{\"name\":\"calc_holiday_available\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_business_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_channel_id_allowed\",\"value\":[\"0\"],\"source\":0},{\"name\":\"product_can_use_coupon\",\"value\":[\"true\"],\"source\":0},{\"name\":\"preSaleTag\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_third_party_verify\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_block_stock\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_discount_rule_id\",\"value\":[\"0\"],\"source\":0},{\"name\":\"service_type\",\"value\":[\"洗剪吹\"],\"source\":0},{\"name\":\"reservation_is_needed_or_not\",\"value\":[\"否\"],\"source\":0},{\"name\":\"sys_deal_universal_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"category\",\"value\":[\"50\",\"5001\"],\"source\":0}],\"rule\":{\"buyRule\":{\"maxPerUser\":0,\"minPerUser\":1},\"useRule\":{\"receiptEffectiveDate\":{\"receiptDateType\":1,\"receiptValidDays\":90,\"receiptBeginDate\":\"2024-03-03 21:19:44\",\"receiptEndDate\":\"2024-06-01 23:59:59\",\"showText\":\"购买后90天内有效\"}},\"refundRule\":{\"supportRefundType\":1,\"supportOverdueAutoRefund\":true}},\"displayShopInfo\":{\"dpDisplayShopIds\":[421093568],\"mtDisplayShopIds\":[421093568]},\"tags\":[{\"id\":100075997,\"tagName\":\"不限发型师\"},{\"id\":100005301,\"tagName\":\"剪发\"},{\"id\":100069569,\"tagName\":\"非烫染已更新团单\"},{\"id\":100069583,\"tagName\":\"发型师等级上线后更新团单\"},{\"id\":100019691,\"tagName\":\"美发\"},{\"id\":100086613,\"tagName\":\"洗剪吹爆品待升级\"},{\"id\":10002825,\"tagName\":\"双客户灰度测试-待升级爆品标签\"},{\"id\":10002829,\"tagName\":\"非灰度-美发待升级标签\"},{\"id\":10002849,\"tagName\":\"丽人美发-待升级标签\"}],\"customer\":{\"originCustomerId\":30007829},\"deals\":[{\"dealId\":455262434,\"basic\":{\"title\":\"复购供给（不限）\",\"originTitle\":\"复购供给（不限）\",\"status\":1},\"price\":{\"salePrice\":\"80.00\",\"marketPrice\":\"100.00\",\"version\":5148257492},\"stock\":{\"dpSales\":0,\"dpTotal\":50000000,\"dpRemain\":50000000,\"mtSales\":0,\"mtTotal\":50000000,\"mtRemain\":50000000,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1,\"sharedSales\":0,\"sharedTotal\":100000000,\"sharedRemain\":100000000,\"isSharedSoldOut\":false},\"attrs\":[{\"name\":\"sku_receipt_type\",\"value\":[\"1\"],\"source\":0}],\"dealIdInt\":455262434}],\"price\":{\"salePrice\":\"80.00\",\"marketPrice\":\"100.00\",\"version\":5148257492},\"stock\":{\"dpSales\":0,\"dpTotal\":50000000,\"dpRemain\":50000000,\"mtSales\":0,\"mtTotal\":50000000,\"mtRemain\":50000000,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1},\"detail\":{\"dealGroupPics\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/e65296ec1aad482428e80468867b4b90105930.jpg\",\"images\":[\"https://qcloud.dpfile.com/pc/0K_vwNLgv6lDwsKybFermB4gY7lN8BGx7mmMluTTsAdrytgFll3Uip5GWDVRHukDDkGHbckSnjHySsp3uDLV9w.jpg\"],\"importantPoint\":\"\",\"templateDetailDTOs\":[{\"title\":\"产品介绍\",\"content\":\"<div><p>111</p></div>\\n\\n\",\"type\":5,\"blockId\":0},{\"title\":\"团购详情\",\"content\":\"        <div class=\\\"detail-tit\\\"></div>\\n        <div>\\n            <table width=\\\"100%\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" class=\\\"detail-table\\\">\\n                <thead>\\n                <tr>\\n                    <th width=\\\"50%\\\">名称</th>\\n                    <th width=\\\"25%\\\">数量</th>\\n                    <th width=\\\"25%\\\">单价</th>\\n                </tr>\\n                </thead>\\n                <tbody>\\n                            <tr>\\n                                <td>洗剪吹</td>\\n                                    <td class=\\\"tc\\\">1</td>\\n                                    <td class=\\\"tc\\\">100元</td>\\n                            </tr>\\n                    <tr class=\\\"total\\\">\\n                        <td></td>\\n                        <td class=\\\"tc\\\">总价<br><strong>团购价</strong></td>\\n                        <td class=\\\"tc\\\">100元<br><strong>80元</strong>\\n                        </td>\\n                    </tr>\\n                </tbody>\\n            </table>\\n        </div>\\n\\n\",\"type\":1,\"blockId\":0},{\"title\":\"购买须知\",\"content\":\"<div class=\\\"detail-box\\\">\\n    <div class=\\\"purchase-notes\\\">\\n\\t\\t        <dl>\\n            <dt>有效期</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">\\n    购买后90天内有效\\n        </p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>预约信息</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">无需预约，如遇消费高峰时段您可能需要排队</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>适用人数</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">每张团购券不限使用人数</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>规则提醒</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">可与其他优惠同享\\n</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>温馨提示</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">如需团购券发票，请您在消费时向商户咨询</p>\\n                <p class=\\\"listitem\\\">为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！</p>\\n            </dd>\\n        </dl>\\n    </div>\\n</div>\\n\",\"type\":2,\"blockId\":0}]},\"saleChannelAggregation\":{\"allSupport\":true,\"supportChannels\":[],\"notSupportChannels\":[]},\"purchaseNote\":{\"title\":\"购买须知\",\"modules\":[{\"moduleName\":\"适用时间\",\"icon\":\"https://p0.meituan.net/travelcube/eb358d1a211285207c636aa95594f3751524.png\",\"items\":[{\"itemName\":\"有效期\",\"itemValues\":[{\"type\":1,\"value\":\"购买后90天内有效\"}]}]},{\"moduleName\":\"预约规则\",\"icon\":\"https://p0.meituan.net/travelcube/17303e587e8242902a1bf6ecd3eab10b1029.png\",\"items\":[{\"itemName\":\"\",\"itemValues\":[{\"type\":1,\"value\":\"无需预约，如遇消费高峰时段您可能需要排队\"}]}]},{\"moduleName\":\"适用人数\",\"icon\":\"https://p0.meituan.net/travelcube/1f18aa2c0100e75060daf348283f3c861351.png\",\"items\":[{\"itemName\":\"\",\"itemValues\":[{\"type\":1,\"value\":\"每张团购券不限使用人数\"}]}]},{\"moduleName\":\"其他规则\",\"icon\":\"https://p1.meituan.net/travelcube/4dac95c5f43a52f49b81ece1918925ea858.png\",\"items\":[{\"itemName\":\"\",\"itemValues\":[{\"type\":1,\"value\":\"可与其他优惠同享\"}]}]},{\"moduleName\":\"温馨提示\",\"icon\":\"https://p1.meituan.net/travelcube/09a2f606b1636f8b135b8582e3c0a53d1494.png\",\"items\":[{\"itemName\":\"\",\"itemValues\":[{\"type\":1,\"value\":\"为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！\"}]}]}]},\"dpDealGroupIdInt\":1021008579,\"mtDealGroupIdInt\":1021008579}";
        DealGroupBuyRuleDTO buyRuleDTO = new DealGroupBuyRuleDTO();
        DealGroupDTO dealGroup = JSON.parseObject(dealGroupJson,DealGroupDTO.class);
        List<AttrDTO> attrDTOS = dealGroup.getAttrs();
        AttrDTO attrDTO = new AttrDTO();
        ctx = mock(DealCtx.class);
        DealGroupPBO result = new DealGroupPBO();

        // act
        // 无限制
        buyRuleDTO.setMaxPerUser(0);
        actLimitInfo(buyRuleDTO, dealGroup, result);
        attrDTO.setName("suitable_person_first_not_member");
        List<String> values = Lists.newArrayList();
        values.add("只适用于初次到店非商户会员使用");
        attrDTO.setValue(values);
        attrDTOS.add(attrDTO);
        actLimitInfo(buyRuleDTO, dealGroup, result);
        // 1次
        buyRuleDTO.setMaxPerUser(1);
        actLimitInfo(buyRuleDTO, dealGroup, result);

        // >=2
        buyRuleDTO.setMaxPerUser(2);
        int actResult = actLimitInfo(buyRuleDTO, dealGroup, result);
        attrDTO.setName("");
        actLimitInfo(buyRuleDTO, dealGroup, result);

        // assert
        assertEquals(actResult, 1);
    }
    public int actLimitInfo(DealGroupBuyRuleDTO buyRuleDTO, DealGroupDTO dealGroup, DealGroupPBO result){
        LayerConfig limitInfoMap = parallDealBuilderProcessor.getLimitInfo(buyRuleDTO, dealGroup);
        parallDealBuilderProcessor.buildLimitLayer(ctx, result, limitInfoMap);
        List<Guarantee> limitsExtends = parallDealBuilderProcessor.getLimitsExtend(result);
        parallDealBuilderProcessor.buildLimitExtendByMap(limitsExtends, limitInfoMap);
        return 1;
    }

    /**
     * 测试场景：onlyDealBuyBtn.redirectUrl 不为空，但 ctx.getPriceContext().isHasExclusiveDeduction() 为 false
     */
    @Test
    @Ignore
    public void testPutPromotionChannelParamIfNeeded_noExclusiveDeduction() {
        // arrange
        String originalUrl = "http://www.example.com";
        onlyDealBuyBtn.setRedirectUrl(originalUrl);
        DealGroupPBO result = new DealGroupPBO();
        when(ctx.getPriceContext()).thenReturn(priceContext);
        when(priceContext.isHasExclusiveDeduction()).thenReturn(false);

        // act
        ReminderInfoBuilderService reminderInfoBuilderService = new ReminderInfoBuilderService();
        reminderInfoBuilderService.buildSpringFestivalBanner(ctx, result);

        // assert
        assertEquals(originalUrl, onlyDealBuyBtn.getRedirectUrl());
    }

    @Test
    public void testLimitInfoByCategory() {
        // arrange
        String dealGroupJson = "{\"dpDealGroupId\":1021008579,\"mtDealGroupId\":1021008579,\"basic\":{\"categoryId\":501,\"title\":\"复购供给（不限）\",\"brandName\":\"北新泾美发美甲\",\"titleDesc\":\"仅售80元，价值100元复购供给（不限）！\",\"beginSaleDate\":\"2024-03-01 10:52:37\",\"endSaleDate\":\"2025-03-01 10:52:00\",\"status\":1,\"saleChannel\":0,\"salePlatform\":3,\"sourceId\":102,\"tradeType\":3},\"image\":{\"defaultPicPath\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/e65296ec1aad482428e80468867b4b90105930.jpg\",\"allPicPaths\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/e65296ec1aad482428e80468867b4b90105930.jpg\"},\"category\":{\"categoryId\":501,\"serviceType\":\"洗剪吹\",\"serviceTypeId\":809},\"serviceProject\":{\"title\":\"团购详情\",\"salePrice\":\"80.00\",\"marketPrice\":\"100.00\",\"mustGroups\":[{\"groups\":[{\"skuId\":0,\"categoryId\":3111,\"name\":\"洗剪吹\",\"amount\":1,\"marketPrice\":\"100.0\",\"status\":10,\"attrs\":[]}]}],\"optionGroups\":[],\"structType\":\"uniform-structure-table\"},\"channel\":{\"channelId\":5,\"channelEn\":\"beauty\",\"channelCn\":\"丽人\",\"channelGroupId\":2,\"channelGroupEn\":\"general\",\"channelGroupCn\":\"到店综合\"},\"attrs\":[{\"name\":\"product_finish_date\",\"value\":[\"1970-01-01 08:00:00\"],\"source\":0},{\"name\":\"calc_holiday_available\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_business_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_channel_id_allowed\",\"value\":[\"0\"],\"source\":0},{\"name\":\"product_can_use_coupon\",\"value\":[\"true\"],\"source\":0},{\"name\":\"preSaleTag\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_third_party_verify\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_block_stock\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_discount_rule_id\",\"value\":[\"0\"],\"source\":0},{\"name\":\"service_type\",\"value\":[\"洗剪吹\"],\"source\":0},{\"name\":\"reservation_is_needed_or_not\",\"value\":[\"否\"],\"source\":0},{\"name\":\"sys_deal_universal_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"category\",\"value\":[\"50\",\"5001\"],\"source\":0}],\"rule\":{\"buyRule\":{\"maxPerUser\":0,\"minPerUser\":1},\"useRule\":{\"receiptEffectiveDate\":{\"receiptDateType\":1,\"receiptValidDays\":90,\"receiptBeginDate\":\"2024-03-03 21:19:44\",\"receiptEndDate\":\"2024-06-01 23:59:59\",\"showText\":\"购买后90天内有效\"}},\"refundRule\":{\"supportRefundType\":1,\"supportOverdueAutoRefund\":true}},\"displayShopInfo\":{\"dpDisplayShopIds\":[421093568],\"mtDisplayShopIds\":[421093568]},\"tags\":[{\"id\":100075997,\"tagName\":\"不限发型师\"},{\"id\":100005301,\"tagName\":\"剪发\"},{\"id\":100069569,\"tagName\":\"非烫染已更新团单\"},{\"id\":100069583,\"tagName\":\"发型师等级上线后更新团单\"},{\"id\":100019691,\"tagName\":\"美发\"},{\"id\":100086613,\"tagName\":\"洗剪吹爆品待升级\"},{\"id\":10002825,\"tagName\":\"双客户灰度测试-待升级爆品标签\"},{\"id\":10002829,\"tagName\":\"非灰度-美发待升级标签\"},{\"id\":10002849,\"tagName\":\"丽人美发-待升级标签\"}],\"customer\":{\"originCustomerId\":30007829},\"deals\":[{\"dealId\":455262434,\"basic\":{\"title\":\"复购供给（不限）\",\"originTitle\":\"复购供给（不限）\",\"status\":1},\"price\":{\"salePrice\":\"80.00\",\"marketPrice\":\"100.00\",\"version\":5148257492},\"stock\":{\"dpSales\":0,\"dpTotal\":50000000,\"dpRemain\":50000000,\"mtSales\":0,\"mtTotal\":50000000,\"mtRemain\":50000000,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1,\"sharedSales\":0,\"sharedTotal\":100000000,\"sharedRemain\":100000000,\"isSharedSoldOut\":false},\"attrs\":[{\"name\":\"sku_receipt_type\",\"value\":[\"1\"],\"source\":0}],\"dealIdInt\":455262434}],\"price\":{\"salePrice\":\"80.00\",\"marketPrice\":\"100.00\",\"version\":5148257492},\"stock\":{\"dpSales\":0,\"dpTotal\":50000000,\"dpRemain\":50000000,\"mtSales\":0,\"mtTotal\":50000000,\"mtRemain\":50000000,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1},\"detail\":{\"dealGroupPics\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/e65296ec1aad482428e80468867b4b90105930.jpg\",\"images\":[\"https://qcloud.dpfile.com/pc/0K_vwNLgv6lDwsKybFermB4gY7lN8BGx7mmMluTTsAdrytgFll3Uip5GWDVRHukDDkGHbckSnjHySsp3uDLV9w.jpg\"],\"importantPoint\":\"\",\"templateDetailDTOs\":[{\"title\":\"产品介绍\",\"content\":\"<div><p>111</p></div>\\n\\n\",\"type\":5,\"blockId\":0},{\"title\":\"团购详情\",\"content\":\"        <div class=\\\"detail-tit\\\"></div>\\n        <div>\\n            <table width=\\\"100%\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" class=\\\"detail-table\\\">\\n                <thead>\\n                <tr>\\n                    <th width=\\\"50%\\\">名称</th>\\n                    <th width=\\\"25%\\\">数量</th>\\n                    <th width=\\\"25%\\\">单价</th>\\n                </tr>\\n                </thead>\\n                <tbody>\\n                            <tr>\\n                                <td>洗剪吹</td>\\n                                    <td class=\\\"tc\\\">1</td>\\n                                    <td class=\\\"tc\\\">100元</td>\\n                            </tr>\\n                    <tr class=\\\"total\\\">\\n                        <td></td>\\n                        <td class=\\\"tc\\\">总价<br><strong>团购价</strong></td>\\n                        <td class=\\\"tc\\\">100元<br><strong>80元</strong>\\n                        </td>\\n                    </tr>\\n                </tbody>\\n            </table>\\n        </div>\\n\\n\",\"type\":1,\"blockId\":0},{\"title\":\"购买须知\",\"content\":\"<div class=\\\"detail-box\\\">\\n    <div class=\\\"purchase-notes\\\">\\n\\t\\t        <dl>\\n            <dt>有效期</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">\\n    购买后90天内有效\\n        </p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>预约信息</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">无需预约，如遇消费高峰时段您可能需要排队</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>适用人数</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">每张团购券不限使用人数</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>规则提醒</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">可与其他优惠同享\\n</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>温馨提示</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">如需团购券发票，请您在消费时向商户咨询</p>\\n                <p class=\\\"listitem\\\">为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！</p>\\n            </dd>\\n        </dl>\\n    </div>\\n</div>\\n\",\"type\":2,\"blockId\":0}]},\"saleChannelAggregation\":{\"allSupport\":true,\"supportChannels\":[],\"notSupportChannels\":[]},\"purchaseNote\":{\"title\":\"购买须知\",\"modules\":[{\"moduleName\":\"适用时间\",\"icon\":\"https://p0.meituan.net/travelcube/eb358d1a211285207c636aa95594f3751524.png\",\"items\":[{\"itemName\":\"有效期\",\"itemValues\":[{\"type\":1,\"value\":\"购买后90天内有效\"}]}]},{\"moduleName\":\"预约规则\",\"icon\":\"https://p0.meituan.net/travelcube/17303e587e8242902a1bf6ecd3eab10b1029.png\",\"items\":[{\"itemName\":\"\",\"itemValues\":[{\"type\":1,\"value\":\"无需预约，如遇消费高峰时段您可能需要排队\"}]}]},{\"moduleName\":\"适用人数\",\"icon\":\"https://p0.meituan.net/travelcube/1f18aa2c0100e75060daf348283f3c861351.png\",\"items\":[{\"itemName\":\"\",\"itemValues\":[{\"type\":1,\"value\":\"每张团购券不限使用人数\"}]}]},{\"moduleName\":\"其他规则\",\"icon\":\"https://p1.meituan.net/travelcube/4dac95c5f43a52f49b81ece1918925ea858.png\",\"items\":[{\"itemName\":\"\",\"itemValues\":[{\"type\":1,\"value\":\"可与其他优惠同享\"}]}]},{\"moduleName\":\"温馨提示\",\"icon\":\"https://p1.meituan.net/travelcube/09a2f606b1636f8b135b8582e3c0a53d1494.png\",\"items\":[{\"itemName\":\"\",\"itemValues\":[{\"type\":1,\"value\":\"为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！\"}]}]}]},\"dpDealGroupIdInt\":1021008579,\"mtDealGroupIdInt\":1021008579}";
        DealGroupBuyRuleDTO buyRuleDTO = new DealGroupBuyRuleDTO();
        buyRuleDTO.setMaxPerUser(1);
        DealGroupDTO dealGroup = JSON.parseObject(dealGroupJson,DealGroupDTO.class);

        ctx = mock(DealCtx.class);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setMtUserId(1);
        ctx.setEnvCtx(envCtx);
        DealGroupPBO result = new DealGroupPBO();
        result.setCategoryId(502);
        List<String> buyRules = new ArrayList<>();
        List<String> limits = new ArrayList<>();

        parallDealBuilderProcessor.buildLimitInfo( dealGroup, ctx, result, buyRuleDTO);
        parallDealBuilderProcessor.buildLimitInfoByCategory( dealGroup, ctx, result, buyRuleDTO);
        parallDealBuilderProcessor.buildLimitInfoByRules(result, buyRuleDTO);

        ShopReviewCtx shopReviewCtx = new ShopReviewCtx(ctx.getEnvCtx());

        Long userid; Integer status;
        ShopReviewHelper.filterPicStatusAndOwner(shopReviewCtx, 1L, null);
        ShopReviewHelper.filterPicStatusAndOwner(shopReviewCtx, 1L, 1);
        ShopReviewHelper.filterPicStatusAndOwner(shopReviewCtx, 1L, 2);
        ShopReviewHelper.filterPicStatusAndOwner(shopReviewCtx, 2L, 2);
        // assert
        assertNotNull(limits);
    }

    // 适用商户位置: generalInfo为Cons.FOLD_STYLE
    @Test
    @Ignore
    public void testSetDisplayPositionWithGeneralInfoFoldStyle() throws Exception {
        // 准备测试数据
        DealCtx ctx = new DealCtx(new EnvCtx());
        ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
        moduleConfigsModule.setGeneralInfo(Cons.FOLD_STYLE);
        ctx.setModuleConfigsModule(moduleConfigsModule);
        Method setDisplayPosition = ParallDealBuilderProcessor.class.getDeclaredMethod("setDisplayPosition", DealCtx.class, ShopPBO.class);
        setDisplayPosition.setAccessible(true);

        setRequestSourceAndCategoryId(ctx, RequestSourceEnum.HOME_PAGE.getSource(), 1221);
        ShopPBO shop1 = new ShopPBO();
        setDisplayPosition.invoke(parallDealBuilderProcessor, ctx, shop1);
        assertEquals(DisplayPositionEnum.BEFORE_DETAIL.getCode(), shop1.getDisplayPosition());
    }

    // 适用商户位置: ctx.getRequestSource()为RequestSourceEnum.HOME_PAGE.getSource()
    @Test
    @Ignore
    public void testSetDisplayPositionWithRequestSourceHomePage() throws Exception {
        // 准备测试数据
        DealCtx ctx = new DealCtx(new EnvCtx());
        Method setDisplayPosition = ParallDealBuilderProcessor.class.getDeclaredMethod("setDisplayPosition", DealCtx.class, ShopPBO.class);
        setDisplayPosition.setAccessible(true);

        ShopPBO shop2 = new ShopPBO();
        setRequestSourceAndCategoryId(ctx, RequestSourceEnum.HOME_PAGE.getSource(), 1221);
        setDisplayPosition.invoke(parallDealBuilderProcessor, ctx, shop2);
        assertEquals(DisplayPositionEnum.BEFORE_DETAIL.getCode(), shop2.getDisplayPosition());
    }

    // 适用商户位置: ctx.getRequestSource()为RequestSourceEnum.CAI_XI.getSource()
    @Test
    @Ignore
    public void testSetDisplayPositionWithRequestSourceCaiXi() throws Exception {
        // 准备测试数据
        DealCtx ctx = new DealCtx(new EnvCtx());
        Method setDisplayPosition = ParallDealBuilderProcessor.class.getDeclaredMethod("setDisplayPosition", DealCtx.class, ShopPBO.class);
        setDisplayPosition.setAccessible(true);

        setRequestSourceAndCategoryId(ctx, RequestSourceEnum.CAI_XI.getSource(), 1221);
        ShopPBO shop3 = new ShopPBO();
        setDisplayPosition.invoke(parallDealBuilderProcessor, ctx, shop3);
        assertEquals(DisplayPositionEnum.BEFORE_DETAIL.getCode(), shop3.getDisplayPosition());
    }

    // 适用商户位置: ctx.getRequestSource()不为RequestSourceEnum.HOME_PAGE.getSource()和RequestSourceEnum.CAI_XI.getSource()
    @Test
    @Ignore
    public void testSetDisplayPositionWithRequestSourceOtherSource() throws Exception {
        // 准备测试数据
        DealCtx ctx = new DealCtx(new EnvCtx());
        Method setDisplayPosition = ParallDealBuilderProcessor.class.getDeclaredMethod("setDisplayPosition", DealCtx.class, ShopPBO.class);
        setDisplayPosition.setAccessible(true);

        setRequestSourceAndCategoryId(ctx, "other_source", 1221);
        ShopPBO shop4 = new ShopPBO();
        setDisplayPosition.invoke(parallDealBuilderProcessor, ctx, shop4);
        assertEquals(DisplayPositionEnum.AFTER_DETAIL.getCode(), shop4.getDisplayPosition());
    }

    @Test
    public void buildMatrixRequest() throws Exception {
        PriceDisplayWrapper priceDisplayWrapper = new PriceDisplayWrapper();
        BeautyMatrixRequestDTO matrixRequestDTO = priceDisplayWrapper.buildMatrixRequest(111l, 1,1,"12.20.400", 1,"411");
        assertNotNull(matrixRequestDTO);
    }

    @Test
    public void testCouponWallet() throws Exception {
        String s = "{\"couponPackageList\":[{\"applyIdList\":[\"1892278636\"],\"couponPackageId\":\"cWZRsNOkFJk9VCRqH1gILG8WrOVg6cGth78nyCoY58Q\\u003d\",\"couponPackageType\":1,\"__isset_bit_vector\":[1],\"optionals\":[\"APPLY_ID_LIST\",\"TOKEN\",\"COUPON_PACKAGE_ID\",\"COUPON_PACKAGE_TYPE\"]},{\"applyIdList\":[\"1690013613\"],\"couponPackageId\":\"cWZRsNOkFJk9VCRqH1gILMv/fRFJgcyAy/QazBnVmx0\\u003d\",\"couponPackageType\":1,\"__isset_bit_vector\":[1],\"optionals\":[\"APPLY_ID_LIST\",\"TOKEN\",\"COUPON_PACKAGE_ID\",\"COUPON_PACKAGE_TYPE\"]},{\"applyIdList\":[\"1690013613\"],\"couponPackageId\":\"cWZRsNOkFJk9VCRqH1gILLEcd/gai7H/XBOVX5FfSEE\\u003d\",\"couponPackageType\":1,\"__isset_bit_vector\":[1],\"optionals\":[\"APPLY_ID_LIST\",\"TOKEN\",\"COUPON_PACKAGE_ID\",\"COUPON_PACKAGE_TYPE\"]},{\"applyIdList\":[\"1489772992\"],\"couponPackageId\":\"cWZRsNOkFJk9VCRqH1gILCIeBPUwCniM4tqWUIJtuuk\\u003d\",\"couponPackageType\":1,\"__isset_bit_vector\":[1],\"optionals\":[\"APPLY_ID_LIST\",\"TOKEN\",\"COUPON_PACKAGE_ID\",\"COUPON_PACKAGE_TYPE\"]},{\"applyIdList\":[\"1104690989\"],\"couponPackageId\":\"cWZRsNOkFJk9VCRqH1gILOMvlCn+2bxaE5WBet7ogSY\\u003d\",\"couponPackageType\":1,\"__isset_bit_vector\":[1],\"optionals\":[\"APPLY_ID_LIST\",\"TOKEN\",\"COUPON_PACKAGE_ID\",\"COUPON_PACKAGE_TYPE\"]}],\"optionals\":[\"COUPON_PACKAGE_LIST\"]}";
        ParallDealBuilderProcessor processor = new ParallDealBuilderProcessor();
        List<String> list = processor.buildCouponGroupIdList(s);
        assertNotNull(list);
    }

    @Test
    public void buildInflateCoupon() throws Exception {
        // 准备测试数据
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx ctx = new DealCtx(envCtx);
        PriceContext priceContent = new PriceContext();
        PriceDisplayDTO dealPrice = new PriceDisplayDTO();
        Map<String, String> extendDisplayInfoMap = Maps.newHashMap();
        MagicalMemberTagTextDTO memberTagText = new MagicalMemberTagTextDTO();
        memberTagText.setMagicalMemberCouponTag("testIcon");
        memberTagText.setReduceMoney("200000");
        memberTagText.setStatus("已膨胀");
        memberTagText.setShowType("inflated_poi_has_cannot_inflated_mmc");
        String memberTagTextStr = JSON.toJSONString(memberTagText);
        extendDisplayInfoMap.put("magicalMemberCouponLabel", memberTagTextStr);
        dealPrice.setExtendDisplayInfo(extendDisplayInfoMap);
        priceContent.setDealPromoPrice(dealPrice);
        DealGroupPBO result = new DealGroupPBO();
        result.setPromoDetailModule(new PromoDetailModule());

        Map<Integer, List<PromoDTO>> pricePromoInfoMap = Maps.newHashMap();

        ParallDealBuilderProcessor processor = new ParallDealBuilderProcessor();

        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setCouponId("123141515");
        promoDTO.setStartTime(new Date());
        promoDTO.setEndTime(new Date());
        promoDTO.setAmount(BigDecimal.TEN);
        promoDTO.setPromotionExplanatoryTags(Lists.newArrayList(1,2,3));
        promoDTO.setMinConsumptionAmount(BigDecimal.valueOf(200));

        Map<String, String> promotionOtherInfoMap = Maps.newHashMap();
        promotionOtherInfoMap.put((PromotionPropertyEnum.AFTER_INFLATE_MONEY.getValue()), "10000");
        promotionOtherInfoMap.put((PromotionPropertyEnum.ASSET_TYPE.getValue()), "13444");
        promotionOtherInfoMap.put((PromotionPropertyEnum.NIB_BIZ_LINE.getValue()), "nib_beauty");
        promotionOtherInfoMap.put((PromotionPropertyEnum.BIZ_TOKEN.getValue()), "45135151");
        promotionOtherInfoMap.put((promotionOtherInfoMap.get(PromotionPropertyEnum.CAN_INFLATE.getValue())), "true");
        promotionOtherInfoMap.put((PromotionPropertyEnum.AFTER_INFLATE_REQUIRE_AMOUNT.getValue()), "4446");

        promoDTO.setPromotionOtherInfoMap(promotionOtherInfoMap);
        pricePromoInfoMap.put(1,Lists.newArrayList(promoDTO));
        dealPrice.setPricePromoInfoMap(pricePromoInfoMap);

        // act
        ctx.setPriceContext(priceContent);
        DealGroupChannelDTO dealGroupChannelDTO = new DealGroupChannelDTO();
        dealGroupChannelDTO.setCategoryId(303);
        ctx.setChannelDTO(dealGroupChannelDTO);
        ctx.setRequestSource("poi_page");
        ctx.setGpsCityId(1);
        processor.buildInflateCoupon(ctx);
        //
        assertNull(result.getPromoDetailModule().getInflateCounpon());

    }

    @Test
    public void industryRcfTrack(){
        ParallDealBuilderProcessor processor = new ParallDealBuilderProcessor();
        IndustryRcfTrackUtils.industryRcfTrack(502);
        assertNotNull(processor);
    }

    @Test
    public void industryRcfTrackLog(){
        ParallDealBuilderProcessor processor = new ParallDealBuilderProcessor();
        processor.rcfLog(502);
        assertNotNull(processor);
    }

    // 适用商户位置-教育特团情况
    @Test
    @Ignore
    public void testSetDisplayPositionWithRequestSourceCostEffective() throws Exception {
        // 准备测试数据
        DealCtx ctx = new DealCtx(new EnvCtx());
        Method setDisplayPosition = ParallDealBuilderProcessor.class.getDeclaredMethod("setDisplayPosition", DealCtx.class, ShopPBO.class);
        setDisplayPosition.setAccessible(true);

        setRequestSourceAndCategoryId(ctx, RequestSourceEnum.COST_EFFECTIVE.getSource(), 1221);
        ShopPBO shop5 = new ShopPBO();
        setDisplayPosition.invoke(parallDealBuilderProcessor, ctx, shop5);
        assertEquals(DisplayPositionEnum.BEFORE_DETAIL.getCode(), shop5.getDisplayPosition());
    }
    private void setRequestSourceAndCategoryId(DealCtx ctx, String requestSource, int categoryId) {
        ctx.setRequestSource(requestSource);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(categoryId);
        ctx.setChannelDTO(channelDTO);
    }


    @Test
    public void testAssemblePnPurchaseNoteDTO_hideTips() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_LIVE_WEIXIN_MINIAPP);
        DealCtx ctx = new DealCtx(envCtx);
        String purchaseNoteJson = "{\n" +
                "  \"pnTitle\": \"购买须知\",\n" +
                "  \"pnModules\": [\n" +
                "    {\n" +
                "      \"pnModuleName\": \"适用时间\",\n" +
                "      \"pnIcon\": \"https://p0.meituan.net/travelcube/eb358d1a211285207c636aa95594f3751524.png\",\n" +
                "      \"pnItems\": [\n" +
                "        {\n" +
                "          \"pnItemName\": \"有效时间\",\n" +
                "          \"pnItemValues\": [\n" +
                "            {\n" +
                "              \"pnType\": 1,\n" +
                "              \"pnValue\": \"购买后30天内有效\"\n" +
                "            }\n" +
                "          ]\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"pnModuleName\": \"预约规则\",\n" +
                "      \"pnIcon\": \"https://p0.meituan.net/travelcube/17303e587e8242902a1bf6ecd3eab10b1029.png\",\n" +
                "      \"pnItems\": [\n" +
                "        {\n" +
                "          \"pnItemName\": \"\",\n" +
                "          \"pnItemValues\": [\n" +
                "            {\n" +
                "              \"pnType\": 1,\n" +
                "              \"pnValue\": \"请您提前1天预约\"\n" +
                "            }\n" +
                "          ]\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"pnModuleName\": \"适用人数\",\n" +
                "      \"pnIcon\": \"https://p0.meituan.net/travelcube/1f18aa2c0100e75060daf348283f3c861351.png\",\n" +
                "      \"pnItems\": [\n" +
                "        {\n" +
                "          \"pnItemName\": \"\",\n" +
                "          \"pnItemValues\": [\n" +
                "            {\n" +
                "              \"pnType\": 1,\n" +
                "              \"pnValue\": \"每张团购券最多1人使用\"\n" +
                "            },\n" +
                "            {\n" +
                "              \"pnType\": 1,\n" +
                "              \"pnValue\": \"每位用户仅限购买1份\"\n" +
                "            }\n" +
                "          ]\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"pnModuleName\": \"其他规则\",\n" +
                "      \"pnIcon\": \"https://p1.meituan.net/travelcube/4dac95c5f43a52f49b81ece1918925ea858.png\",\n" +
                "      \"pnItems\": [\n" +
                "        {\n" +
                "          \"pnItemName\": \"\",\n" +
                "          \"pnItemValues\": [\n" +
                "            {\n" +
                "              \"pnType\": 1,\n" +
                "              \"pnValue\": \"不再与其他优惠同享\"\n" +
                "            }\n" +
                "          ]\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"pnModuleName\": \"温馨提示\",\n" +
                "      \"pnIcon\": \"https://p1.meituan.net/travelcube/09a2f606b1636f8b135b8582e3c0a53d1494.png\",\n" +
                "      \"pnItems\": [\n" +
                "        {\n" +
                "          \"pnItemName\": \"\",\n" +
                "          \"pnItemValues\": [\n" +
                "            {\n" +
                "              \"pnType\": 1,\n" +
                "              \"pnValue\": \"如需团购券发票，请您在消费时向商户咨询\"\n" +
                "            },\n" +
                "            {\n" +
                "              \"pnType\": 1,\n" +
                "              \"pnValue\": \"为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！\"\n" +
                "            }\n" +
                "          ]\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ],\n" +
                "  \"subTitle\": \"周一至周日可用\"\n" +
                "}";
        PnPurchaseNoteDTO pnPurchaseNoteDTO = JSON.parseObject(purchaseNoteJson, PnPurchaseNoteDTO.class);
        ctx.setPnPurchaseNoteDTO(pnPurchaseNoteDTO);
        PnPurchaseNoteDTO handledPurchaseNotDTO = parallDealBuilderProcessor.assemblePnPurchaseNoteDTO(ctx);
        assertTrue(handledPurchaseNotDTO.getPnModules().size() == 3);
    }




    @Test
    public void testAssemblePnPurchaseNote_showTips() {
        String pnPurchaseNoteJSON = "{\n" +
                "    \"pnTitle\": \"购买须知\",\n" +
                "    \"pnModules\": [\n" +
                "        {\n" +
                "            \"pnModuleName\": \"适用时间\",\n" +
                "            \"pnIcon\": \"https://p0.meituan.net/travelcube/eb358d1a211285207c636aa95594f3751524.png\",\n" +
                "            \"pnItems\": [\n" +
                "                {\n" +
                "                    \"pnItemName\": \"有效时间\",\n" +
                "                    \"pnItemValues\": [\n" +
                "                        {\n" +
                "                            \"pnType\": 1,\n" +
                "                            \"pnValue\": \"购买后30天内有效\"\n" +
                "                        }\n" +
                "                    ]\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"pnModuleName\": \"预约规则\",\n" +
                "            \"pnIcon\": \"https://p0.meituan.net/travelcube/17303e587e8242902a1bf6ecd3eab10b1029.png\",\n" +
                "            \"pnItems\": [\n" +
                "                {\n" +
                "                    \"pnItemName\": \"\",\n" +
                "                    \"pnItemValues\": [\n" +
                "                        {\n" +
                "                            \"pnType\": 1,\n" +
                "                            \"pnValue\": \"请您提前1天预约\"\n" +
                "                        }\n" +
                "                    ]\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"pnModuleName\": \"适用人数\",\n" +
                "            \"pnIcon\": \"https://p0.meituan.net/travelcube/1f18aa2c0100e75060daf348283f3c861351.png\",\n" +
                "            \"pnItems\": [\n" +
                "                {\n" +
                "                    \"pnItemName\": \"\",\n" +
                "                    \"pnItemValues\": [\n" +
                "                        {\n" +
                "                            \"pnType\": 1,\n" +
                "                            \"pnValue\": \"每张团购券最多1人使用\"\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"pnType\": 1,\n" +
                "                            \"pnValue\": \"每位用户仅限购买1份\"\n" +
                "                        }\n" +
                "                    ]\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"pnModuleName\": \"其他规则\",\n" +
                "            \"pnIcon\": \"https://p1.meituan.net/travelcube/4dac95c5f43a52f49b81ece1918925ea858.png\",\n" +
                "            \"pnItems\": [\n" +
                "                {\n" +
                "                    \"pnItemName\": \"\",\n" +
                "                    \"pnItemValues\": [\n" +
                "                        {\n" +
                "                            \"pnType\": 1,\n" +
                "                            \"pnValue\": \"不再与其他优惠同享\"\n" +
                "                        }\n" +
                "                    ]\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"pnModuleName\": \"温馨提示\",\n" +
                "            \"pnIcon\": \"https://p1.meituan.net/travelcube/09a2f606b1636f8b135b8582e3c0a53d1494.png\",\n" +
                "            \"pnItems\": [\n" +
                "                {\n" +
                "                    \"pnItemName\": \"商家服务\",\n" +
                "                    \"pnItemValues\": [\n" +
                "                        {\n" +
                "                            \"pnType\": 1,\n" +
                "                            \"pnValue\": \"提供免费WiFi\"\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"pnType\": 1,\n" +
                "                            \"pnValue\": \"提供免费停车位\"\n" +
                "                        }\n" +
                "                    ]\n" +
                "                },\n" +
                "                {\n" +
                "                    \"pnItemName\": \"其他提示\",\n" +
                "                    \"pnItemValues\": [\n" +
                "                        {\n" +
                "                            \"pnType\": 1,\n" +
                "                            \"pnValue\": \"收藏门店送艾草一份\"\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"pnType\": 1,\n" +
                "                            \"pnValue\": \"如需团购券发票，请您在消费时向商户咨询\"\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"pnType\": 1,\n" +
                "                            \"pnValue\": \"为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！\"\n" +
                "                        }\n" +
                "                    ]\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ],\n" +
                "    \"subTitle\": \"周一至周日可用\"\n" +
                "}";
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_LIVE_WEIXIN_MINIAPP);
        DealCtx ctx = new DealCtx(envCtx);
        PnPurchaseNoteDTO pnPurchaseNoteDTO = JSON.parseObject(pnPurchaseNoteJSON, PnPurchaseNoteDTO.class);
        ctx.setPnPurchaseNoteDTO(pnPurchaseNoteDTO);
        PnPurchaseNoteDTO handledPurchaseNotDTO = parallDealBuilderProcessor.assemblePnPurchaseNoteDTO(ctx);
        assertTrue(handledPurchaseNotDTO.getPnModules().size() == 4);
    }

    @Test
    public void testBuildNormalPromoDetailInfo0() {
        DealGroupPBO dealGroupPBO = new DealGroupPBO();
        DealCtx mockDealCtx = mock(DealCtx.class);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP);
        PriceDisplayDTO normalPrice = new PriceDisplayDTO();
        when(mockDealCtx.isExternal()).thenReturn(true);
        when(mockDealCtx.getCategoryId()).thenReturn(401);
        when(mockDealCtx.isThirdPArt()).thenReturn(false);
        when(mockDealCtx.isEnableCardStyleV2()).thenReturn(false);
        when(mockDealCtx.getEnvCtx()).thenReturn(envCtx);
        when(mockDealCtx.getRequestSource()).thenReturn("mlive");
        dealUtilsMockedStatic.when(() -> DealUtils.isOldDealDetailStyle(mockDealCtx)).thenReturn(false);

        parallDealBuilderProcessor.buildNormalPromoDetailInfo(mockDealCtx, normalPrice, dealGroupPBO);
        assertNull(dealGroupPBO.getPromoDetailModule());
    }

    @Test
    public void testBuildPinTuanDTO() {
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setCePinTuanScene(true);
        costEffectivePinTuan.setPinTuanActivityId("123");
        costEffectivePinTuan.setShareToken("456");
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setCostEffectivePinTuan(costEffectivePinTuan);

        DealGroupPBO result = new DealGroupPBO();
        DztgShareModule shareModule = new DztgShareModule();
        result.setShareModule(shareModule);
        parallDealBuilderProcessor.buildPinTuanDTO(ctx, result);
        assertTrue(result.getShareModule().getTransParam().getOrderGroupId().equals("456"));
    }

    @Test
    public void testBuildCostEffectivePinTuan() {
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setSceneType(56);
        costEffectivePinTuan.setCePinTuanScene(true);
        costEffectivePinTuan.setPinTuanActivityId("123");
        costEffectivePinTuan.setShareToken("456");
        costEffectivePinTuan.setCePinTuanScene(true);
        costEffectivePinTuan.setPinTuanOpened(true);
        costEffectivePinTuan.setGroupSuccCountMin(10);
        costEffectivePinTuan.setHasHelpCount(5);
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setCostEffectivePinTuan(costEffectivePinTuan);

        PriceContext priceContext = new PriceContext();
        PriceDisplayDTO price = new PriceDisplayDTO();
        price.setPrice(new BigDecimal(10));
        priceContext.setCostEffectivePrice(price);
        ctx.setPriceContext(priceContext);

        PromoActivityInfoVO vo = parallDealBuilderProcessor.buildCostEffectivePintuan(ctx);
        assertTrue(vo.getBonusType().equals("拼团"));
        assertTrue(vo.getStyle() == 0);
    }

    @Test
    public void testBuildSsrExpEnabled() {
        lionMockedStatic.when(() -> Lion.getBoolean(APP_KEY, LionConstants.SSR_EXP_ENABLED, false)).thenReturn(true);
        DealGroupPBO result = new DealGroupPBO();
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP);
        DealCtx ctx = new DealCtx(envCtx);
        parallDealBuilderProcessor.buildSsrExpEnabled(ctx, result);
        assertTrue(result.isSsrExperimentEnabled());
    }

    /**
     * 测试场景：当dealPrice中有神券信息，但券已过期时
     */
    @Test
    public void testBuildCouponItemsWithExpiredMagicalCoupons() {
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setCouponId("123");
        promoDTO.setCouponGroupId("456");
        promoDTO.setAmount(BigDecimal.TEN);
        promoDTO.setStartTime(new Date(System.currentTimeMillis() - 200000));
        promoDTO.setEndTime(new Date(System.currentTimeMillis() - 100000));
        promoDTO.setPromotionExplanatoryTags(Collections.singletonList(1)); // 假设1代表神券
        promoDTO.setMinConsumptionAmount(new BigDecimal(10));

        Map<String,String> promoOtherInfoMap = Maps.newHashMap();
        promoOtherInfoMap.put(PromotionPropertyEnum.ASSET_TYPE.getValue(), "2");
        promoDTO.setPromotionOtherInfoMap(promoOtherInfoMap);

        Map<Integer, List<PromoDTO>> promoMap = new HashMap<>();
        promoMap.put(1, Collections.singletonList(promoDTO)); // 神券类型的优惠信息
        when(mockPriceDisplayDTO.getPricePromoInfoMap()).thenReturn(promoMap);

        CouponItemPackageDTO result = parallDealBuilderProcessor.buildCouponItems(mockPriceDisplayDTO, ctx);

        assertNotNull("结果不应为null", result);
        assertFalse("优惠券列表不应为空", result.getCouponItems().isEmpty());
        assertEquals("优惠券应标记为无效", CouponValidStatusEnum.UN_VALID.getCode(), result.getCouponItems().get(0).getValidStatus());
    }

    /**
     * 测试交易保障团单，标签从商品标签获取, 并根据Lion配置填充详情，定义type为9
     * 场景：当DealUtils.isTradeAssuranceDeal返回true，且LionConfigUtils.getTradeAssuranceLayerConfig返回非空配置时
     */
    @Test
    public void testGetFeaturesLayer_TradeAssuranceDealWithConfig() {
        // arrange
        LayerConfig config = JsonUtils.fromJson("{\n" +
                "    \"desc\": \"若过期未预约会为您全额退款。\\n您在完成拍摄前有权随时停止交易，若未预约具体拍摄时间可全额退款。\",\n" +
                "    \"icon\": \"https://p0.meituan.net/ingee/2860c04f209c5ebe48ad7e05a726de711937.png\",\n" +
                "    \"jumpUrl\": \"https://cube.meituan.com/cube/block/e1f196fa0597/291608/index.html\",\n" +
                "    \"title\": \"未预约随时退\",\n" +
                "    \"type\": 9\n" +
                "  }", LayerConfig.class);

        Map<String, LayerConfig> mockConfigMap = new HashMap<>();
        mockConfigMap.put("mt100200954", config);

        when(DealUtils.isTradeAssuranceDeal(ctx)).thenReturn(true);

        List<AttrDTO> attrs = new ArrayList<>();
        attrs.add(new AttrDTO());
        attrs.get(0).setName("is_trade_assurance");
        attrs.get(0).setValue(Lists.newArrayList("1"));

        List<DealGroupTagDTO> tagDTOs = new ArrayList<>();
        DealGroupTagDTO dealGroupTagDTO = new DealGroupTagDTO();
        dealGroupTagDTO.setId(100200954L);
        dealGroupTagDTO.setTagName("xxx");
        tagDTOs.add(dealGroupTagDTO);

        lionMockedStatic.when(() -> Lion.getMap(APP_KEY, LionConstants.TRADE_ASSURANCE_LAYER_CONFIGS, LayerConfig.class, Collections.emptyMap()))
                .thenReturn(mockConfigMap);

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setTags(tagDTOs);
        dealGroupDTO.setAttrs(attrs);

        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(ctx.isMt()).thenReturn(true);

        // act
        FeaturesLayer result = parallDealBuilderProcessor.getFeaturesLayer(ctx, false);

        // assert
        assertNotNull(result);
        assertNotNull(result.getLayerConfigs());
        assertEquals(1, result.getLayerConfigs().size());
    }

    @Test
    public void testGetUserOrderCount() {
        String envJson = "{\"dpUserId\":835496445,\"dpVirtualUserId\":941343249,\"mtUserId\":284746330,\"mtVirtualUserId\":927637264,\"unionId\":\"095a69ef747d46419703923fc0460fdba168778217023316139\",\"dpId\":\"095a69ef747d46419703923fc0460fdba168778217023316139\",\"uuid\":\"0000000000000095A69EF747D46419703923FC0460FDBA168778217023316139\",\"version\":\"12.29.200\",\"clientType\":200502,\"appDeviceId\":\"095a69ef747d46419703923fc0460fdba168778217023316139\",\"appId\":10,\"mtsiFlag\":\"0\",\"requestURI\":\"/general/platform/dztgdetail/dzdealbase.bin\",\"userAgent\":\"MApi 1.3 (mtscope 12.29.200 appstore; iPhone 14.8 iPhone13,2; a0d0)\",\"userIp\":\"***************\",\"dztgClientTypeEnum\":\"MEITUAN_APP\",\"startTime\":1737365316283,\"meituanClientList\":[\"MEITUAN_APP\",\"MEITUAN_FROM_H5\",\"MEITUAN_MAP_APP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_LIVE_WEIXIN_MINIAPP\"],\"dianpingClientList\":[\"DIANPING_APP\",\"DIANPING_FROM_H5\",\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"],\"merchantClientList\":[\"DPMERCHANT\"],\"mtMiniAppList\":[\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_KUAISHOU_MINIAPP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_LIVE_WEIXIN_MINIAPP\"],\"dpMiniAppList\":[\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"],\"nativeAppList\":[\"MEITUAN_APP\",\"MEITUAN_MAP_APP\",\"DIANPING_APP\"],\"wxMiniList\":[\"MEITUAN_WEIXIN_MINIAPP\",\"DIANPING_WEIXIN_MINIAPP\"],\"fromH5\":false,\"harmony\":false,\"mt\":true,\"apollo\":false,\"login\":true,\"mainApp\":true,\"miniApp\":false,\"thirdPlatform\":false,\"ios\":true,\"dp\":false,\"mtMiniApp\":false,\"dpMiniApp\":false,\"mainWX\":false,\"externalAndNoScene\":false,\"mtLiveMinApp\":false,\"wxMini\":false,\"mainWeb\":false,\"mtWxMainMini\":false,\"virtualUserId\":927637264,\"dpMerchant\":false,\"android\":false,\"userId\":284746330,\"external\":false,\"native\":true}";
        EnvCtx envCtx = new EnvCtx();
        envCtx = JSON.parseObject(envJson, EnvCtx.class);
        DealCtx dealCtx = new DealCtx(envCtx);
        dealCtx.setDpId(123);
        dealCtx.setMtId(123);
        int result =  parallDealBuilderProcessor.getUserOrderCount(dealCtx);
        Assert.assertTrue(result ==  0);
    }

    @Test
    public void testGetCouponNum(){
        Map<String, String> promotionOtherInfoMap = new HashMap<>();
        parallDealBuilderProcessor.getCouponNum(promotionOtherInfoMap);

        promotionOtherInfoMap.put(PromotionPropertyEnum.COUPON_AGGREGATE_NUM.getValue(), "1");
        parallDealBuilderProcessor.getCouponNum(promotionOtherInfoMap);

        promotionOtherInfoMap.put(PromotionPropertyEnum.COUPON_AGGREGATE_NUM.getValue(), "a");
        int result = parallDealBuilderProcessor.getCouponNum(promotionOtherInfoMap);
        Assert.assertTrue(result == 0);
    }
}