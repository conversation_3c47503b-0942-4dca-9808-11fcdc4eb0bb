package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ServiceGuarantee.ServiceGuaranteeDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.service.MtPoiService;
import java.lang.reflect.Method;
import java.util.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ServiceGuaranteeQueryFacadeTest {

    @InjectMocks
    private ServiceGuaranteeQueryFacade serviceGuaranteeQueryFacade;

    @Mock
    private MtPoiService sinaiMtPoiService;

    private Long shopId = 1L;

    private Map<Integer, ServiceGuaranteeDTO> serviceGuaranteeDTOMap;

    @Before
    public void setUp() {
        serviceGuaranteeDTOMap = new HashMap<>();
        ServiceGuaranteeDTO serviceGuaranteeDTO = new ServiceGuaranteeDTO();
        serviceGuaranteeDTOMap.put(1, serviceGuaranteeDTO);
        serviceGuaranteeDTOMap.put(-100, serviceGuaranteeDTO);
    }

    private ServiceGuaranteeDTO invokePrivateMethod(Long shopId, Map<Integer, ServiceGuaranteeDTO> serviceGuaranteeDTOMap) throws Exception {
        Method method = ServiceGuaranteeQueryFacade.class.getDeclaredMethod("getServiceGuaranteeDTOByMtShopId", Long.class, Map.class);
        method.setAccessible(true);
        return (ServiceGuaranteeDTO) method.invoke(serviceGuaranteeQueryFacade, shopId, serviceGuaranteeDTOMap);
    }

    @Test
    public void testGetServiceGuaranteeDTOByMtShopId_MtPoiDTONotExist() throws Throwable {
        when(sinaiMtPoiService.findPoisById(anyList(), anyList())).thenReturn(null);
        ServiceGuaranteeDTO result = invokePrivateMethod(shopId, serviceGuaranteeDTOMap);
        assertNotNull(result);
    }

    @Test
    public void testGetServiceGuaranteeDTOByMtShopId_MtPoiDTOExistButDpCityIdIsNull() throws Throwable {
        MtPoiDTO mtPoiDTO = new MtPoiDTO();
        mtPoiDTO.setDpCityId(null);
        when(sinaiMtPoiService.findPoisById(anyList(), anyList())).thenReturn(Collections.singletonMap(shopId, mtPoiDTO));
        ServiceGuaranteeDTO result = invokePrivateMethod(shopId, serviceGuaranteeDTOMap);
        assertNotNull(result);
    }

    @Test
    public void testGetServiceGuaranteeDTOByMtShopId_MtPoiDTOExistAndDpCityIdExist() throws Throwable {
        MtPoiDTO mtPoiDTO = new MtPoiDTO();
        mtPoiDTO.setDpCityId(1);
        when(sinaiMtPoiService.findPoisById(anyList(), anyList())).thenReturn(Collections.singletonMap(shopId, mtPoiDTO));
        ServiceGuaranteeDTO result = invokePrivateMethod(shopId, serviceGuaranteeDTOMap);
        assertNotNull(result);
    }
}
