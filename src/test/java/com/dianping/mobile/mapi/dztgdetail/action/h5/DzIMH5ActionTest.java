package com.dianping.mobile.mapi.dztgdetail.action.h5;

import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealIMReq;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertNull;
import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * @author: wuwenqiang
 * @create: 2024-10-29
 * @description:
 */
@RunWith(MockitoJUnitRunner.class)
public class DzIMH5ActionTest {
    @InjectMocks
    private DzIMH5Action dzIMH5Action;

    @Test
    public void test_validate_shopIdLongExist() {
        DealIMReq dealIMReq = new DealIMReq();
        dealIMReq.setShopidstr("1");
        dealIMReq.setDealgroupid(1);
        dealIMReq.setClienttype(1);
        IMobileResponse resp = dzIMH5Action.validate(dealIMReq, null);
        assertNull(resp);
    }

    @Test
    public void test_validate_shopIdLongNotExist_shopIdStrEncryptExist() {
        DealIMReq dealIMReq = new DealIMReq();
        dealIMReq.setDealgroupid(1);
        dealIMReq.setClienttype(1);
        dealIMReq.setShopidstrEncrypt("1");
        IMobileResponse resp = dzIMH5Action.validate(dealIMReq, null);
        assertNull(resp);
    }

    @Test
    public void test_validate_shopIdLongNotExist_shopIdEncryptExist() {
        DealIMReq dealIMReq = new DealIMReq();
        dealIMReq.setDealgroupid(1);
        dealIMReq.setClienttype(1);
        dealIMReq.setShopidEncrypt("1");
        IMobileResponse resp = dzIMH5Action.validate(dealIMReq, null);
        assertNull(resp);
    }

    @Test
    public void test_validate_allShopId_NotExist() {
        DealIMReq dealIMReq = new DealIMReq();
        dealIMReq.setDealgroupid(1);
        dealIMReq.setClienttype(1);
        IMobileResponse resp = dzIMH5Action.validate(dealIMReq, null);
        assertEquals(resp, Resps.PARAM_ERROR);
    }
}
