package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import jodd.util.URLDecoder;
import org.apache.commons.lang.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@RunWith(MockitoJUnitRunner.class)
public class DealQueryFacadeGetMliveIdTest {

    private DealQueryFacade dealQueryFacade;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private DealGroupPBO dealGroupPBO;

    @Before
    public void setUp() {
        dealQueryFacade = new DealQueryFacade();
    }

    /**
     * Test case when passParam is blank.
     */
    @Test
    public void testGetMliveId_PassParamIsBlank() throws Throwable {
        // arrange
        String passParam = "";
        // act
        long result = dealQueryFacade.getMliveId(passParam);
        // assert
        assertEquals(0L, result);
    }

    /**
     * Test case when PROMOTE_CHANNEL_INFO is blank.
     */
    @Test
    public void testGetMliveId_PromoteChannelInfoIsBlank() throws Throwable {
        // arrange
        String passParam = "{\"someKey\":\"someValue\"}";
        String decodedPassParam = URLDecoder.decode(passParam);
        JSONObject jsonObject = JSON.parseObject(decodedPassParam);
        // act
        long result = dealQueryFacade.getMliveId(passParam);
        // assert
        assertEquals(0L, result);
    }

    /**
     * Test case when promoteExtend is blank.
     */
    @Test
    public void testGetMliveId_PromoteExtendIsBlank() throws Throwable {
        // arrange
        String passParam = "{\"PROMOTE_CHANNEL_INFO\":\"{}\"}";
        String decodedPassParam = URLDecoder.decode(passParam);
        JSONObject jsonObject = JSON.parseObject(decodedPassParam);
        JSONObject promoteChannelInfo = jsonObject.getJSONObject("PROMOTE_CHANNEL_INFO");
        // act
        long result = dealQueryFacade.getMliveId(passParam);
        // assert
        assertEquals(0L, result);
    }

    /**
     * Test case when mLiveId is not a valid number.
     */
    @Test
    public void testGetMliveId_MliveIdIsNotValidNumber() throws Throwable {
        // arrange
        String passParam = "{\"PROMOTE_CHANNEL_INFO\":{\"promoteExtend\":{\"mLiveId\":\"invalid\"}}}";
        String decodedPassParam = URLDecoder.decode(passParam);
        JSONObject jsonObject = JSON.parseObject(decodedPassParam);
        JSONObject promoteChannelInfo = jsonObject.getJSONObject("PROMOTE_CHANNEL_INFO");
        JSONObject promoteExtend = promoteChannelInfo.getJSONObject("promoteExtend");
        // act
        long result = dealQueryFacade.getMliveId(passParam);
        // assert
        assertEquals(0L, result);
    }

    /**
     * Test case when all fields are valid.
     */
    @Test
    public void testGetMliveId_AllFieldsAreValid() throws Throwable {
        // arrange
        String passParam = "{\"PROMOTE_CHANNEL_INFO\":{\"promoteExtend\":{\"mLiveId\":\"12345\"}}}";
        String decodedPassParam = URLDecoder.decode(passParam);
        JSONObject jsonObject = JSON.parseObject(decodedPassParam);
        JSONObject promoteChannelInfo = jsonObject.getJSONObject("PROMOTE_CHANNEL_INFO");
        JSONObject promoteExtend = promoteChannelInfo.getJSONObject("promoteExtend");
        // act
        long result = dealQueryFacade.getMliveId(passParam);
        // assert
        assertEquals(12345L, result);
    }

    /**
     * Tests when dpShopId is not in gray shop ids list - should not set hegui notice
     * Since we cannot mock Lion.getList, this will test with actual Lion configuration
     */
    @Test
    public void testSetHeguiNoticeWhenShopNotInGrayList() throws Throwable {
        // arrange
        // Use an invalid shop ID that won't be in the whitelist
        long dpShopId = -1L;
        when(dealCtx.getDpLongShopId()).thenReturn(dpShopId);
        // act
        dealQueryFacade.setHeguiNotice(dealCtx);
        // assert
        verify(dealCtx).getDpLongShopId();
        verifyNoMoreInteractions(dealCtx);
        verifyNoInteractions(dealGroupPBO);
    }

    /**
     * Tests when exception occurs during processing - should handle exception gracefully
     */
    @Test
    public void testSetHeguiNoticeWhenExceptionOccurs() throws Throwable {
        // arrange
        when(dealCtx.getDpLongShopId()).thenThrow(new RuntimeException("Test exception"));
        // act
        dealQueryFacade.setHeguiNotice(dealCtx);
        // assert
        verify(dealCtx).getDpLongShopId();
        verifyNoMoreInteractions(dealCtx);
        verifyNoInteractions(dealGroupPBO);
    }

    /**
     * Tests when dealCtx is null - should handle gracefully without NPE
     */
    @Test
    public void testSetHeguiNoticeWhenDealCtxIsNull() throws Throwable {
        // act
        dealQueryFacade.setHeguiNotice(null);
        // assert
        verifyNoInteractions(dealCtx, dealGroupPBO);
    }

    /**
     * Tests when dealCtx.getResult() returns null - should handle gracefully
     */
    @Test
    public void testSetHeguiNoticeWhenResultIsNull() throws Throwable {
        // arrange
        when(dealCtx.getDpLongShopId()).thenReturn(1L);
        // act
        dealQueryFacade.setHeguiNotice(dealCtx);
        // assert
        verify(dealCtx).getDpLongShopId();
        verifyNoMoreInteractions(dealCtx);
        verifyNoInteractions(dealGroupPBO);
    }

    /**
     * Tests when customerId is 0 - should not set hegui notice
     */
    @Test
    public void testSetHeguiNoticeWhenCustomerIdIsZero() throws Throwable {
        // arrange
        long dpShopId = 1L;
        when(dealCtx.getDpLongShopId()).thenReturn(dpShopId);
        // act
        dealQueryFacade.setHeguiNotice(dealCtx);
        // assert
        verify(dealCtx).getDpLongShopId();
        verifyNoMoreInteractions(dealCtx);
        verifyNoInteractions(dealGroupPBO);
    }

    /**
     * Tests when getResult() throws exception - should handle gracefully
     */
    @Test
    public void testSetHeguiNoticeWhenGetResultThrowsException() throws Throwable {
        // arrange
        when(dealCtx.getDpLongShopId()).thenReturn(1L);
        // act
        dealQueryFacade.setHeguiNotice(dealCtx);
        // assert
        verify(dealCtx).getDpLongShopId();
        verifyNoMoreInteractions(dealCtx);
        verifyNoInteractions(dealGroupPBO);
    }

    /**
     * Tests when setHeguiNotice throws exception - should handle gracefully
     */
    @Test
    public void testSetHeguiNoticeWhenSetHeguiNoticeThrowsException() throws Throwable {
        // arrange
        long dpShopId = 1L;
        when(dealCtx.getDpLongShopId()).thenReturn(dpShopId);
        // act
        dealQueryFacade.setHeguiNotice(dealCtx);
        // assert
        verify(dealCtx).getDpLongShopId();
        verifyNoMoreInteractions(dealCtx);
        verifyNoInteractions(dealGroupPBO);
    }

    /**
     * Tests when getDpLongShopId returns 0 - should handle gracefully
     */
    @Test
    public void testSetHeguiNoticeWhenShopIdIsZero() throws Throwable {
        // arrange
        when(dealCtx.getDpLongShopId()).thenReturn(0L);
        // act
        dealQueryFacade.setHeguiNotice(dealCtx);
        // assert
        verify(dealCtx).getDpLongShopId();
        verifyNoMoreInteractions(dealCtx);
        verifyNoInteractions(dealGroupPBO);
    }
}
