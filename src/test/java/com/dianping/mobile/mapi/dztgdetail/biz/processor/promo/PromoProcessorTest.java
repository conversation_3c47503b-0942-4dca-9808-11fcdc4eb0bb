package com.dianping.mobile.mapi.dztgdetail.biz.processor.promo;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.PromoProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PromoWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.sankuai.nibmktproxy.queryclient.proxy.PromotionDTOResult;
import com.sankuai.nibmktproxy.queryclient.proxy.PromotionResponse;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

public class PromoProcessorTest {

    @InjectMocks
    private PromoProcessor promoProcessor;

    @Mock
    private PromoWrapper promoWrapper;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private FutureCtx futureCtx;

    @Mock
    private Future<List<PromoDisplayDTO>> promoFuture;

    @Mock
    private Future<List<PromoDisplayDTO>> discountPromoFuture;

    @Mock
    private Future<PromotionResponse> promoProxyFuture;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(dealCtx.getFutureCtx()).thenReturn(futureCtx);
    }

    /**
     * Test normal flow where all futures return valid data.
     */
    @Test
    public void testProcessNormalFlow() throws Throwable {
        // arrange
        List<PromoDisplayDTO> promoList = Collections.singletonList(new PromoDisplayDTO());
        List<PromoDisplayDTO> discountPromoList = Collections.singletonList(new PromoDisplayDTO());
        PromotionResponse promotionResponse = new PromotionResponse();
        promotionResponse.setStatus(0);
        Map<String, PromotionDTOResult> promotionMap = Collections.singletonMap("key", new PromotionDTOResult());
        when(futureCtx.getPromoFuture()).thenReturn(promoFuture);
        when(futureCtx.getDiscountPromoFuture()).thenReturn(discountPromoFuture);
        when(futureCtx.getPromoProxyFuture()).thenReturn(promoProxyFuture);
        when(promoWrapper.queryPromoDisplayDTO(promoFuture)).thenReturn(promoList);
        when(promoWrapper.queryPromoDisplayDTO(discountPromoFuture)).thenReturn(discountPromoList);
        when(promoProxyFuture.get()).thenReturn(promotionResponse);
        promotionResponse.setPromotionMap(promotionMap);
        // act
        promoProcessor.process(dealCtx);
        // assert
        verify(dealCtx).setPromoList(promoList);
        verify(dealCtx).setDiscountPromoList(discountPromoList);
        verify(dealCtx).setPromotionMap(promotionMap);
    }

    /**
     * Test scenario where promoFuture returns null.
     */
    @Test
    public void testProcessPromoFutureReturnsNull() throws Throwable {
        // arrange
        List<PromoDisplayDTO> discountPromoList = Collections.singletonList(new PromoDisplayDTO());
        PromotionResponse promotionResponse = new PromotionResponse();
        promotionResponse.setStatus(0);
        Map<String, PromotionDTOResult> promotionMap = Collections.singletonMap("key", new PromotionDTOResult());
        when(futureCtx.getPromoFuture()).thenReturn(promoFuture);
        when(futureCtx.getDiscountPromoFuture()).thenReturn(discountPromoFuture);
        when(futureCtx.getPromoProxyFuture()).thenReturn(promoProxyFuture);
        when(promoWrapper.queryPromoDisplayDTO(promoFuture)).thenReturn(null);
        when(promoWrapper.queryPromoDisplayDTO(discountPromoFuture)).thenReturn(discountPromoList);
        when(promoProxyFuture.get()).thenReturn(promotionResponse);
        promotionResponse.setPromotionMap(promotionMap);
        // act
        promoProcessor.process(dealCtx);
        // assert
        verify(dealCtx).setPromoList(null);
        verify(dealCtx).setDiscountPromoList(discountPromoList);
        verify(dealCtx).setPromotionMap(promotionMap);
    }

    /**
     * Test scenario where discountPromoFuture returns null.
     */
    @Test
    public void testProcessDiscountPromoFutureReturnsNull() throws Throwable {
        // arrange
        List<PromoDisplayDTO> promoList = Collections.singletonList(new PromoDisplayDTO());
        PromotionResponse promotionResponse = new PromotionResponse();
        promotionResponse.setStatus(0);
        Map<String, PromotionDTOResult> promotionMap = Collections.singletonMap("key", new PromotionDTOResult());
        when(futureCtx.getPromoFuture()).thenReturn(promoFuture);
        when(futureCtx.getDiscountPromoFuture()).thenReturn(discountPromoFuture);
        when(futureCtx.getPromoProxyFuture()).thenReturn(promoProxyFuture);
        when(promoWrapper.queryPromoDisplayDTO(promoFuture)).thenReturn(promoList);
        when(promoWrapper.queryPromoDisplayDTO(discountPromoFuture)).thenReturn(null);
        when(promoProxyFuture.get()).thenReturn(promotionResponse);
        promotionResponse.setPromotionMap(promotionMap);
        // act
        promoProcessor.process(dealCtx);
        // assert
        verify(dealCtx).setPromoList(promoList);
        verify(dealCtx).setDiscountPromoList(null);
        verify(dealCtx).setPromotionMap(promotionMap);
    }

    /**
     * Test scenario where promoProxyFuture returns null.
     */
    @Test
    public void testProcessPromoProxyFutureReturnsNull() throws Throwable {
        // arrange
        List<PromoDisplayDTO> promoList = Collections.singletonList(new PromoDisplayDTO());
        List<PromoDisplayDTO> discountPromoList = Collections.singletonList(new PromoDisplayDTO());
        when(futureCtx.getPromoFuture()).thenReturn(promoFuture);
        when(futureCtx.getDiscountPromoFuture()).thenReturn(discountPromoFuture);
        when(futureCtx.getPromoProxyFuture()).thenReturn(promoProxyFuture);
        when(promoWrapper.queryPromoDisplayDTO(promoFuture)).thenReturn(promoList);
        when(promoWrapper.queryPromoDisplayDTO(discountPromoFuture)).thenReturn(discountPromoList);
        when(promoProxyFuture.get()).thenReturn(null);
        // act
        promoProcessor.process(dealCtx);
        // assert
        verify(dealCtx).setPromoList(promoList);
        verify(dealCtx).setDiscountPromoList(discountPromoList);
        verify(dealCtx, never()).setPromotionMap(any());
    }

    /**
     * Test scenario where promoProxyFuture returns a response with invalid status.
     */
    @Test
    public void testProcessPromoProxyFutureReturnsInvalidStatus() throws Throwable {
        // arrange
        List<PromoDisplayDTO> promoList = Collections.singletonList(new PromoDisplayDTO());
        List<PromoDisplayDTO> discountPromoList = Collections.singletonList(new PromoDisplayDTO());
        PromotionResponse promotionResponse = new PromotionResponse();
        promotionResponse.setStatus(1);
        when(futureCtx.getPromoFuture()).thenReturn(promoFuture);
        when(futureCtx.getDiscountPromoFuture()).thenReturn(discountPromoFuture);
        when(futureCtx.getPromoProxyFuture()).thenReturn(promoProxyFuture);
        when(promoWrapper.queryPromoDisplayDTO(promoFuture)).thenReturn(promoList);
        when(promoWrapper.queryPromoDisplayDTO(discountPromoFuture)).thenReturn(discountPromoList);
        when(promoProxyFuture.get()).thenReturn(promotionResponse);
        // act
        promoProcessor.process(dealCtx);
        // assert
        verify(dealCtx).setPromoList(promoList);
        verify(dealCtx).setDiscountPromoList(discountPromoList);
        verify(dealCtx, never()).setPromotionMap(any());
    }

    /**
     * Test scenario where an exception occurs while retrieving the PromotionResponse.
     */
    @Test
    public void testProcessExceptionOccurs() throws Throwable {
        // arrange
        List<PromoDisplayDTO> promoList = Collections.singletonList(new PromoDisplayDTO());
        List<PromoDisplayDTO> discountPromoList = Collections.singletonList(new PromoDisplayDTO());
        when(futureCtx.getPromoFuture()).thenReturn(promoFuture);
        when(futureCtx.getDiscountPromoFuture()).thenReturn(discountPromoFuture);
        when(futureCtx.getPromoProxyFuture()).thenReturn(promoProxyFuture);
        when(promoWrapper.queryPromoDisplayDTO(promoFuture)).thenReturn(promoList);
        when(promoWrapper.queryPromoDisplayDTO(discountPromoFuture)).thenReturn(discountPromoList);
        when(promoProxyFuture.get()).thenThrow(new RuntimeException("Test Exception"));
        // act
        promoProcessor.process(dealCtx);
        // assert
        verify(dealCtx).setPromoList(promoList);
        verify(dealCtx).setDiscountPromoList(discountPromoList);
        verify(dealCtx, never()).setPromotionMap(any());
    }
}
