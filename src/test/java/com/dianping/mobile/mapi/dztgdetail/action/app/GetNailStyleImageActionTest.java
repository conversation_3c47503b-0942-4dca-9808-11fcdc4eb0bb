package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetNailStyleImageRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.nailstyle.OrderNailStyleImageVO;
import com.dianping.mobile.mapi.dztgdetail.facade.NailStyleFacade;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GetNailStyleImageActionTest {

    @InjectMocks
    private GetNailStyleImageAction getNailStyleImageAction;

    @Mock
    private NailStyleFacade nailStyleFacade;

    /**
     * 测试正常情况
     */
    @Test
    public void testExecuteNormalCase() throws Throwable {
        // arrange
        GetNailStyleImageRequest request = new GetNailStyleImageRequest();
        request.setDealGroupId(1L);
        OrderNailStyleImageVO orderNailStyleImageVO = new OrderNailStyleImageVO();
        orderNailStyleImageVO.setNailStyleListUrl("http://test");
        when(nailStyleFacade.getOrderNailStyle(any(), any())).thenReturn(orderNailStyleImageVO);
        // act
        IMobileResponse response = getNailStyleImageAction.execute(request, null);

        // assert
        assertNotNull(response);
        assertEquals(orderNailStyleImageVO, response.getData());
    }

    /**
     * 测试异常情况
     */
    @Test
    public void testExecuteExceptionCase() throws Throwable {
        // arrange
        GetNailStyleImageRequest request = new GetNailStyleImageRequest();
        request.setDealGroupId(1L);
        when(nailStyleFacade.getOrderNailStyle(any(), any())).thenThrow(new RuntimeException());
        // act
        IMobileResponse response = getNailStyleImageAction.execute(request, null);

        // assert
        assertNotNull(response);
        assertEquals(Resps.SYSTEM_ERROR, response);
    }

    /**
     * 测试边界情况
     */
    @Test
    public void testExecuteNoDataCase() throws Throwable {
        // arrange
        GetNailStyleImageRequest request = new GetNailStyleImageRequest();
        request.setDealGroupId(0L);
        when(nailStyleFacade.getOrderNailStyle(any(), any())).thenReturn(null);
        // act
        IMobileResponse response = getNailStyleImageAction.execute(request, null);

        // assert
        assertNotNull(response);
        assertEquals(Resps.NoDataResp, response.getData());
    }
}
