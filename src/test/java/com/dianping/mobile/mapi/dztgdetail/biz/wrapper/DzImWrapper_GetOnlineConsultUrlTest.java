package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.dzim.cliententry.dto.ClientEntryDTO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.concurrent.Future;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class DzImWrapper_GetOnlineConsultUrlTest {

    @Mock
    private Future future;

    @Mock
    private ClientEntryDTO clientEntryDTO;

    private DzImWrapper dzImWrapper = new DzImWrapper();

    /**
     * 测试 getOnlineConsultUrl 方法，当 imFuture 为 null 时，应返回 null
     */
    @Test
    public void testGetOnlineConsultUrlWhenImFutureIsNull() throws Throwable {
        // arrange
        Future imFuture = null;
        // act
        String result = dzImWrapper.getOnlineConsultUrl(imFuture);
        // assert
        assertNull(result);
    }

    /**
     * 测试 getOnlineConsultUrl 方法，当 getFutureResult 返回 null 时，应返回 null
     */
    @Test
    public void testGetOnlineConsultUrlWhenGetFutureResultReturnsNull() throws Throwable {
        // arrange
        when(future.get()).thenReturn(null);
        // act
        String result = dzImWrapper.getOnlineConsultUrl(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 getOnlineConsultUrl 方法，当 getFutureResult 返回的 ClientEntryDTO 对象的 isShow 方法返回 false 时，应返回 null
     */
    @Test
    public void testGetOnlineConsultUrlWhenIsShowReturnsFalse() throws Throwable {
        // arrange
        when(future.get()).thenReturn(clientEntryDTO);
        when(clientEntryDTO.isShow()).thenReturn(false);
        // act
        String result = dzImWrapper.getOnlineConsultUrl(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 getOnlineConsultUrl 方法，当 getFutureResult 返回的 ClientEntryDTO 对象的 isShow 方法返回 true 时，应返回 getEntryUrl 的结果
     */
    @Test
    public void testGetOnlineConsultUrlWhenIsShowReturnsTrue() throws Throwable {
        // arrange
        String expectedUrl = "http://example.com";
        when(future.get()).thenReturn(clientEntryDTO);
        when(clientEntryDTO.isShow()).thenReturn(true);
        when(clientEntryDTO.getEntryUrl()).thenReturn(expectedUrl);
        // act
        String result = dzImWrapper.getOnlineConsultUrl(future);
        // assert
        assertEquals(expectedUrl, result);
    }
}
