package com.dianping.mobile.mapi.dztgdetail.util;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.lion.client.Lion;
import com.dianping.lion.facade.LionFacade;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.feature.model.FeatureDetailDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.feature.model.PreOrderFeatureConfigDTO;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.LayerConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.ApplyShopPositionConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.ExhibitImageConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.LeadsDealBarConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.LogUserGrayConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.MultiServiceTypeSwitchConfig;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.apache.commons.lang3.StringUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;

/**
 * <AUTHOR>
 * @date 2024-01-16
 * @desc Lion工具类单测
 */
@RunWith(MockitoJUnitRunner.class)
public class LionConfigUtilsTest {

    private MockedStatic<Lion> mocked;

    private MockedStatic<LionFacade> lionFacadeMocked;

    @Mock
    private Lion lion;

    @Before
    public void setUp() {
        mocked = mockStatic(Lion.class);
        lionFacadeMocked = mockStatic(LionFacade.class);
    }

    @After
    public void teardown() {
        mocked.close();
        lionFacadeMocked.close();
    }

    @Test
    public void testGetImmersiveImageSceneSwitch() {
        Map<String, Boolean> sceneSwitch = Maps.newHashMap();
        sceneSwitch.put("self", true);
        sceneSwitch.put("recommend", true);
        sceneSwitch.put("orderrecommend", true);
        sceneSwitch.put("order", true);
        mocked.when(() -> Lion.getMap(any(), any(), any(), any())).thenReturn(sceneSwitch);
        // 测试场景：sceneCode在Lion配置的Map中存在，且对应的值为true
        assertTrue(LionConfigUtils.getImmersiveImageSceneSwitch("self"));
        // 测试场景：sceneCode在Lion配置的Map中存在，且对应的值为false
        assertTrue(LionConfigUtils.getImmersiveImageSceneSwitch("recommend"));
        // 测试场景：sceneCode在Lion配置的Map中不存在
        assertTrue(LionConfigUtils.getImmersiveImageSceneSwitch("orderrecommend"));
        // 测试场景：sceneCode在Lion配置的Map中不存在
        assertTrue(LionConfigUtils.getImmersiveImageSceneSwitch("order"));
        // 测试场景：sceneCode在Lion配置的Map中不存在
        assertFalse(LionConfigUtils.getImmersiveImageSceneSwitch("scene11"));
    }

    /**
     * 测试categoryId为null的情况
     */
    @Test
    public void testGetRecommendExhibitImageConfig_CategoryIdIsNull() {
        ExhibitImageConfig result = LionConfigUtils.getRecommendExhibitImageConfig(null);
        assertNotNull(result);
    }

    /**
     * 测试categoryId小于等于0的情况
     */
    @Test
    public void testGetRecommendExhibitImageConfig_CategoryIdIsLessThanOrEqualToZero() {
        ExhibitImageConfig result = LionConfigUtils.getRecommendExhibitImageConfig(0);
        assertNotNull(result);
    }

    /**
     * 测试categoryId大于0，但Lion配置中心没有对应的ExhibitImageConfig的情况
     */
    @Test
    public void testGetRecommendExhibitImageConfig_NoExhibitImageConfigInLion() {
        mocked.when(() -> Lion.getMap(any(), any(), any(), any())).thenReturn(Maps.newHashMap());
        ExhibitImageConfig result = LionConfigUtils.getRecommendExhibitImageConfig(1);
        assertNull(result);
    }

    /**
     * 测试categoryId大于0，且Lion配置中心有对应的ExhibitImageConfig的情况
     */
    @Test
    public void testGetRecommendExhibitImageConfig_HasExhibitImageConfigInLion() {
        Map<String, ExhibitImageConfig> exhibitImageConfigMap = Maps.newHashMap();
        ExhibitImageConfig config = new ExhibitImageConfig();
        exhibitImageConfigMap.put("502", config);
        mocked.when(() -> Lion.getMap(any(), any(), any(), any())).thenReturn(exhibitImageConfigMap);
        ExhibitImageConfig result = LionConfigUtils.getRecommendExhibitImageConfig(502);
        assertEquals(config, result);
    }

    /**
     * 测试Lion中存在HOT_NAIL_STYLE_MODULE_SWITCH，且值为true的情况
     */
    @Test
    public void testGetHotNailStyleModuleSwitchTrue() throws Throwable {
        mocked.when(() -> Lion.getBoolean(any(), any(), any())).thenReturn(true);
        // act
        boolean result = LionConfigUtils.getHotNailStyleModuleSwitch();
        // assert
        assertTrue(result);
    }

    @Test
    public void testIsHotNailModuleBlackShopNullShopId() throws Throwable {
        assertFalse(LionConfigUtils.isHotNailModuleBlackShop(null, true));
    }

    @Test
    public void testIsHotNailModuleBlackShopZeroShopId() throws Throwable {
        assertFalse(LionConfigUtils.isHotNailModuleBlackShop(0L, true));
    }

    @Test
    public void testIsHotNailModuleBlackShopEmptyBlackList() throws Throwable {
        mocked.when(() -> Lion.getMap(any(), any(), any(), any())).thenReturn(Maps.newHashMap());
        assertFalse(LionConfigUtils.isHotNailModuleBlackShop(1L, true));
    }

    @Test
    public void testIsHotNailModuleBlackShopInMtBlackList() throws Throwable {
        Map<String, List<String>> blackList = Maps.newHashMap();
        blackList.put("mt", Collections.singletonList("1"));
        mocked.when(() -> Lion.getMap(any(), any(), any(), any())).thenReturn(blackList);
        assertTrue(LionConfigUtils.isHotNailModuleBlackShop(1L, true));
    }

    @Test
    public void testIsHotNailModuleBlackShopInDpBlackList() throws Throwable {
        Map<String, List<String>> blackList = Maps.newHashMap();
        blackList.put("dp", Collections.singletonList("1"));
        mocked.when(() -> Lion.getMap(any(), any(), any(), any())).thenReturn(blackList);
        assertTrue(LionConfigUtils.isHotNailModuleBlackShop(1L, false));
    }

    @Test
    public void testIsHotNailModuleBlackShopNotInBlackList() throws Throwable {
        Map<String, List<String>> blackList = Maps.newHashMap();
        blackList.put("mt", Collections.singletonList("2"));
        blackList.put("dp", Collections.singletonList("2"));
        mocked.when(() -> Lion.getMap(any(), any(), any(), any())).thenReturn(blackList);
        assertFalse(LionConfigUtils.isHotNailModuleBlackShop(1L, true));
        assertFalse(LionConfigUtils.isHotNailModuleBlackShop(1L, false));
    }

    @Test
    public void testAllowDisplayHotNailModuleWithNullCategoryId() {
        assertFalse(LionConfigUtils.allowDisplayHotNailModule(null, "美甲"));
    }

    @Test
    public void testAllowDisplayHotNailModuleWithZeroCategoryId() {
        assertFalse(LionConfigUtils.allowDisplayHotNailModule(0, "美甲"));
    }

    @Test
    public void testAllowDisplayHotNailModuleWithEmptyServiceType() {
        assertFalse(LionConfigUtils.allowDisplayHotNailModule(1, ""));
    }

    @Test
    public void testAllowDisplayHotNailModuleWithValidCategoryIdAndServiceTypeButNoMatchInLion() {
        Map<String, List> map = Maps.newHashMap();
        map.put("502", Collections.singletonList("美甲"));
        mocked.when(() -> Lion.getMap(LionConstants.APP_KEY, LionConstants.ALLOW_HOT_NAIL_STYLE_CATEGORY_IDS, List.class, Collections.emptyMap())).thenReturn(map);
        assertFalse(LionConfigUtils.allowDisplayHotNailModule(502, "美睫"));
    }

    @Test
    public void testAllowDisplayHotNailModuleWithValidCategoryIdAndServiceTypeAndMatchInLion() {
        Map<String, List> map = Maps.newHashMap();
        map.put("502", Collections.singletonList("美甲"));
        mocked.when(() -> Lion.getMap(LionConstants.APP_KEY, LionConstants.ALLOW_HOT_NAIL_STYLE_CATEGORY_IDS, List.class, Collections.emptyMap())).thenReturn(map);
        assertTrue(LionConfigUtils.allowDisplayHotNailModule(502, "美甲"));
    }

    /**
     * 测试 isEduDealCategoryId 方法，当 dealCategoryId 存在于 eduDealCategoryIds 中时，应返回 true
     */
    @Test
    public void testIsEduDealCategoryId_Exist() throws Throwable {
        // arrange
        long dealCategoryId = 1210L;
        List<Long> eduDealCategoryIds = Arrays.asList(1210L, 134013L, 123020L);
        mocked.when(() -> Lion.getList(LionConstants.APP_KEY, LionConstants.EDU_DEAL_CATEGORY_IDS, Long.class, Lists.newArrayList(1210L)))
                .thenReturn(eduDealCategoryIds);

        // act
        boolean result = LionConfigUtils.isEduDealCategoryId(dealCategoryId);

        // assert
        assertTrue(result);
    }

    /**
     * 测试 isEduDealCategoryId 方法，当 dealCategoryId 不存在于 eduDealCategoryIds 中时，应返回 false
     */
    @Test
    public void testIsEduDealCategoryId_NotExist() throws Throwable {
        // arrange
        long dealCategoryId = 9999L;
        List<Long> eduDealCategoryIds = Arrays.asList(1210L, 134013L, 123020L);
        mocked.when(() -> Lion.getList(LionConstants.APP_KEY, LionConstants.EDU_DEAL_CATEGORY_IDS, Long.class, Lists.newArrayList(1210L)))
                .thenReturn(eduDealCategoryIds);

        // act
        boolean result = LionConfigUtils.isEduDealCategoryId(dealCategoryId);

        // assert
        assertFalse(result);
    }

    /**
     * 测试isUseMultiSku方法，当Lion.getMap返回的Map为空或者不包含categoryId对应的key时，方法应返回false
     */
    @Test
    public void testIsUseMultiSku_MapIsEmptyOrNotContainsKey() {
        // arrange
        mocked.when(() -> Lion.getMap(LionConstants.APP_KEY, LionConstants.MULTI_SKU_DEAL_CATEGORY_SERVICE_TYPE_WHITE, MultiServiceTypeSwitchConfig.class, Collections.emptyMap()))
                .thenReturn(Collections.emptyMap());

        // act
        boolean result = LionConfigUtils.isUseMultiSku(1, "serviceType");

        // assert
        assertFalse(result);
    }

    /**
     * 测试isUseMultiSku方法，当Lion.getMap返回的Map包含categoryId对应的key，但isMultiSku方法返回false时，方法应返回false
     */
    @Test
    public void testIsUseMultiSku_IsMultiSkuReturnsFalse() {
        // arrange
        MultiServiceTypeSwitchConfig config = Mockito.mock(MultiServiceTypeSwitchConfig.class);
        Map<String, MultiServiceTypeSwitchConfig> configMap = new HashMap<>();
        configMap.put("1", config);
        mocked.when(() -> Lion.getMap(LionConstants.APP_KEY, LionConstants.MULTI_SKU_DEAL_CATEGORY_SERVICE_TYPE_WHITE, MultiServiceTypeSwitchConfig.class, Collections.emptyMap()))
                .thenReturn(configMap);
        Mockito.when(config.isMultiSku("serviceType")).thenReturn(false);

        // act
        boolean result = LionConfigUtils.isUseMultiSku(1, "serviceType");

        // assert
        assertFalse(result);
    }

    /**
     * 测试isUseMultiSku方法，当Lion.getMap返回的Map包含categoryId对应的key，且isMultiSku方法返回true时，方法应返回true
     */
    @Test
    public void testIsUseMultiSku_IsMultiSkuReturnsTrue() {
        // arrange
        MultiServiceTypeSwitchConfig config = Mockito.mock(MultiServiceTypeSwitchConfig.class);
        Map<String, MultiServiceTypeSwitchConfig> configMap = new HashMap<>();
        configMap.put("1", config);
        mocked.when(() -> Lion.getMap(LionConstants.APP_KEY, LionConstants.MULTI_SKU_DEAL_CATEGORY_SERVICE_TYPE_WHITE, MultiServiceTypeSwitchConfig.class, Collections.emptyMap()))
                .thenReturn(configMap);
        Mockito.when(config.isMultiSku("serviceType")).thenReturn(true);

        // act
        boolean result = LionConfigUtils.isUseMultiSku(1, "serviceType");

        // assert
        assertTrue(result);
    }

    /**
     * 测试isShowCreateOrderLayer方法，当Lion.getMap返回的Map为空或者不包含categoryId对应的key时，方法应返回false
     */
    @Test
    public void testIsShowCreateOrderLayer_MapIsEmptyOrNotContainsKey() {
        // arrange
        mocked.when(() -> Lion.getMap(LionConstants.APP_KEY, LionConstants.SHOW_CREATE_ORDER_LAYER_DEAL_CATEGORY_SERVICE_TYPE_WHITE, MultiServiceTypeSwitchConfig.class, Collections.emptyMap()))
                .thenReturn(Collections.emptyMap());

        // act
        boolean result = LionConfigUtils.isShowCreateOrderLayer(1, "serviceType");

        // assert
        assertFalse(result);
    }

    /**
     * 测试isShowCreateOrderLayer方法，当Lion.getMap返回的Map包含categoryId对应的key，但isMultiSku方法返回false时，方法应返回false
     */
    @Test
    public void testIsShowCreateOrderLayer_IsMultiSkuReturnsFalse() {
        // arrange
        MultiServiceTypeSwitchConfig config = Mockito.mock(MultiServiceTypeSwitchConfig.class);
        Map<String, MultiServiceTypeSwitchConfig> configMap = new HashMap<>();
        configMap.put("1", config);
        mocked.when(() -> Lion.getMap(LionConstants.APP_KEY, LionConstants.SHOW_CREATE_ORDER_LAYER_DEAL_CATEGORY_SERVICE_TYPE_WHITE, MultiServiceTypeSwitchConfig.class, Collections.emptyMap()))
                .thenReturn(configMap);
        Mockito.when(config.isMultiSku("serviceType")).thenReturn(false);

        // act
        boolean result = LionConfigUtils.isShowCreateOrderLayer(1, "serviceType");

        // assert
        assertFalse(result);
    }

    /**
     * 测试isShowCreateOrderLayer方法，当Lion.getMap返回的Map包含categoryId对应的key，且isMultiSku方法返回true时，方法应返回true
     */
    @Test
    public void testIsShowCreateOrderLayer_IsMultiSkuReturnsTrue() {
        // arrange
        MultiServiceTypeSwitchConfig config = Mockito.mock(MultiServiceTypeSwitchConfig.class);
        Map<String, MultiServiceTypeSwitchConfig> configMap = new HashMap<>();
        configMap.put("1", config);
        mocked.when(() -> Lion.getMap(LionConstants.APP_KEY, LionConstants.SHOW_CREATE_ORDER_LAYER_DEAL_CATEGORY_SERVICE_TYPE_WHITE, MultiServiceTypeSwitchConfig.class, Collections.emptyMap()))
                .thenReturn(configMap);
        Mockito.when(config.isMultiSku("serviceType")).thenReturn(true);

        // act
        boolean result = LionConfigUtils.isShowCreateOrderLayer(1, "serviceType");

        // assert
        assertTrue(result);
    }

    /**
     * 测试 serviceTypeId 为 null 的情况
     */
    @Test
    public void testIsEduOnlineDealServiceTypeIdIsNull() {
        assertFalse(LionConfigUtils.isEduOnlineDeal(1, null));
    }

    /**
     * 测试 serviceTypeId 不为 null，但不在教育在线类团购的服务类型 ID 列表中的情况
     */
    @Test
    public void testIsEduOnlineDealServiceTypeIdNotInList() {
        mocked.when(() -> Lion.getList(LionConstants.APP_KEY,
                LionConstants.EDU_ONLINE_DEAL_SERVICE_LEAF_IDS, Long.class, Lists.newArrayList(134013L, 123020L)))
                .thenReturn(Lists.newArrayList(99999L));

        assertFalse(LionConfigUtils.isEduOnlineDeal(1, 999L));
    }

    /**
     * 测试 serviceTypeId 不为 null，且在教育在线类团购的服务类型 ID 列表中的情况
     */
    @Test
    public void testIsEduOnlineDealServiceTypeIdInList() {
        mocked.when(() -> Lion.getList(LionConstants.APP_KEY,
                        LionConstants.EDU_ONLINE_DEAL_SERVICE_LEAF_IDS, Long.class, Lists.newArrayList(134013L, 123020L)))
                .thenReturn(Lists.newArrayList(134013L, 123020L));

        assertTrue(LionConfigUtils.isEduOnlineDeal(1, 134013L));
    }

    /**
     * 测试 getmliveShareableSwitch 方法，当 Lion.getBoolean 能够成功获取到开关状态时，应返回获取到的状态值
     */
    @Test
    public void testGetmliveShareableSwitch_Success() throws Throwable {
        // arrange
        mocked.when(() -> Lion.getBoolean(LionConstants.APP_KEY, LionConstants.MLIVE_CHANNEL_SHAREABLE_SWITCH, true)).thenReturn(false);
        // act
        boolean result = LionConfigUtils.getmliveShareableSwitch();
        // assert
        assertFalse(result);
    }

    /**
     * 测试 getmliveShareableSwitch 方法，当 Lion.getBoolean 获取开关状态失败时，应返回默认值 true
     */
    @Test
    public void testGetmliveShareableSwitch_Failure() throws Throwable {
        // arrange
        mocked.when(() -> Lion.getBoolean(LionConstants.APP_KEY, LionConstants.MLIVE_CHANNEL_SHAREABLE_SWITCH, true)).thenReturn(true);
        // act
        boolean result = LionConfigUtils.getmliveShareableSwitch();
        // assert
        assertTrue(result);
    }

    /**
     * 测试 getmliveSwitch 方法，当 Lion.getBoolean 能获取到值时，返回该值
     */
    @Test
    public void testGetmliveSwitchWhenLionGetBooleanReturnTrue() throws Throwable {
        // arrange
        mocked.when(() -> Lion.getBoolean(LionConstants.APP_KEY, LionConstants.MLIVE_BUY_BAR_SWITCH, false)).thenReturn(true);
        // act
        boolean result = LionConfigUtils.getmliveSwitch();
        // assert
        assertTrue(result);
    }

    /**
     * 测试 getmliveSwitch 方法，当 Lion.getBoolean 无法获取到值时，返回默认值 false
     */
    @Test
    public void testGetmliveSwitchWhenLionGetBooleanReturnFalse() throws Throwable {
        // arrange
        mocked.when(() -> Lion.getBoolean(LionConstants.APP_KEY, LionConstants.MLIVE_BUY_BAR_SWITCH, false)).thenReturn(false);
        // act
        boolean result = LionConfigUtils.getmliveSwitch();
        // assert
        assertFalse(result);
    }

    /**
     * 测试categoryId为null，Map中有"default"的底图链接的情况
     */
    @Test
    public void testGetMtLiveMiniAppAtmosphereImg_CategoryIdIsNull() {
        // arrange
        Map<String, String> map = new HashMap<>();
        map.put("default", "default_img");
        mocked.when(() -> lion.getMap(LionConstants.APP_KEY, LionConstants.MINI_PROGRAM_LIVE_ATMOSPHERE_BASE_IMG,
                String.class, Collections.emptyMap())).thenReturn(map);
        // act
        String result = LionConfigUtils.getMtLiveMiniAppAtmosphereImg(null);
        // assert
        assertEquals("default_img", result);
    }

    /**
     * 测试categoryId为null，Map中没有"default"的底图链接的情况
     */
    @Test
    public void testGetMtLiveMiniAppAtmosphereImg_CategoryIdIsNullAndNoDefaultImg() {
        // arrange
        Map<String, String> map = new HashMap<>();
        mocked.when(() -> lion.getMap(LionConstants.APP_KEY, LionConstants.MINI_PROGRAM_LIVE_ATMOSPHERE_BASE_IMG,
                String.class, Collections.emptyMap())).thenReturn(map);

        // act
        String result = LionConfigUtils.getMtLiveMiniAppAtmosphereImg(null);

        // assert
        assertEquals(StringUtils.EMPTY, result);
    }

    /**
     * 测试categoryId不为null，Map中有对应id的底图链接的情况
     */
    @Test
    public void testGetMtLiveMiniAppAtmosphereImg_CategoryIdIsNotNullAndHasImg() {
        // arrange
        Integer categoryId = 1;
        Map<String, String> map = new HashMap<>();
        map.put("1", "img_1");
        mocked.when(() -> lion.getMap(LionConstants.APP_KEY, LionConstants.MINI_PROGRAM_LIVE_ATMOSPHERE_BASE_IMG,
                String.class, Collections.emptyMap())).thenReturn(map);

        // act
        String result = LionConfigUtils.getMtLiveMiniAppAtmosphereImg(categoryId);

        // assert
        assertEquals("img_1", result);
    }

    @Test
    public void testIsCostEffectiveByWhiteCategory() {
        // 准备测试数据
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(1);
        ctx.setChannelDTO(channelDTO);
        ctx.setRequestSource(RequestSourceEnum.COST_EFFECTIVE.getSource());

        // 设置Lion配置
        Set<Integer> defaultSet = new HashSet<>();
        defaultSet.add(1);
        lionFacadeMocked.when(() -> LionFacade.getSet(LionConstants.COST_EFFECTIVE_SHOP_AT_TOP_CATEGORY_IDS, Integer.TYPE, Collections.emptySet()))
                .thenReturn(defaultSet);

        // 测试场景：ctx不为空，eduCostEffectiveShopAtTopConfig不为空，且请求来源为COST_EFFECTIVE，且categoryId在eduCostEffectiveShopAtTopConfig中
        assertTrue(LionConfigUtils.isCostEffectiveByWhiteCategory(ctx));

        // 测试场景：ctx为空
        assertFalse(LionConfigUtils.isCostEffectiveByWhiteCategory(null));

        // 测试场景：ctx的channelDTO为空
        ctx.setChannelDTO(null);
        assertFalse(LionConfigUtils.isCostEffectiveByWhiteCategory(ctx));

        // 测试场景：请求来源不为COST_EFFECTIVE
        ctx.setChannelDTO(channelDTO);
        ctx.setRequestSource(RequestSourceEnum.CAI_XI.getSource());
        assertFalse(LionConfigUtils.isCostEffectiveByWhiteCategory(ctx));

        // 测试场景：categoryId不在eduCostEffectiveShopAtTopConfig中
        ctx.setRequestSource(RequestSourceEnum.COST_EFFECTIVE.getSource());
        channelDTO.setCategoryId(2);
        assertFalse(LionConfigUtils.isCostEffectiveByWhiteCategory(ctx));

        lionFacadeMocked.when(() -> LionFacade.getSet(LionConstants.COST_EFFECTIVE_SHOP_AT_TOP_CATEGORY_IDS, Integer.TYPE, Collections.emptySet()))
                .thenReturn(Collections.emptySet());
        channelDTO.setCategoryId(1);
        assertFalse(LionConfigUtils.isCostEffectiveByWhiteCategory(ctx));

    }

    /**
     * 测试Lion配置中心有相关配置时，方法能正确返回配置列表
     */
    @Test
    public void testGetApplyShopPositionConfigWithConfig() {
        // arrange
        List<ApplyShopPositionConfig> expected = Arrays.asList(new ApplyShopPositionConfig(), new ApplyShopPositionConfig());
        mocked.when(() -> Lion.getList(anyString(), anyString(), any(), any())).thenReturn(expected);

        // act
        List<ApplyShopPositionConfig> actual = LionConfigUtils.getApplyShopPositionConfig();

        // assert
        assertEquals(expected, actual);
    }

    /**
     * 测试 serviceTypeId 为 null
     * 期望返回false
     */
    @Test
    public void testIsDealCanDegradeMtStyleServiceTypeIdIsNull() {
        assertFalse(LionConfigUtils.isDealCanDegradeMtStyle(null));
    }

    /**
     * 测试 serviceTypeId 不为 null，但 serviceLeafIds 为空的情况
     * 期望返回false
     */
    @Test
    public void testIsDealCanDegradeMtStyleServiceLeafIdsIsEmpty() {
        mocked.when(() -> Lion.getList(LionConstants.APP_KEY, LionConstants.CAN_DEGRADE_MT_STYLE_SERVICE_LEAF_IDS,
                Long.class, Arrays.asList())).thenReturn(Arrays.asList());
        assertFalse(LionConfigUtils.isDealCanDegradeMtStyle(1L));
    }

    /**
     * 测试 serviceTypeId 不为 null，serviceLeafIds 不为空，但不包含 serviceTypeId 的情况
     * 期望返回false
     */
    @Test
    public void testIsDealCanDegradeMtStyleServiceLeafIdsNotContainsServiceTypeId() {
        mocked.when(() -> Lion.getList(LionConstants.APP_KEY, LionConstants.CAN_DEGRADE_MT_STYLE_SERVICE_LEAF_IDS,
                Long.class, Arrays.asList())).thenReturn(Arrays.asList(2L, 3L));
        assertFalse(LionConfigUtils.isDealCanDegradeMtStyle(1L));
    }

    /**
     * 测试 serviceTypeId 不为 null，serviceLeafIds 不为空，且包含 serviceTypeId 的情况
     * 期望返回true
     */
    @Test
    public void testIsDealCanDegradeMtStyleServiceLeafIdsContainsServiceTypeId() {
        mocked.when(() -> Lion.getList(LionConstants.APP_KEY, LionConstants.CAN_DEGRADE_MT_STYLE_SERVICE_LEAF_IDS,
                Long.class, Arrays.asList())).thenReturn(Arrays.asList(1L, 2L, 3L));
        assertTrue(LionConfigUtils.isDealCanDegradeMtStyle(1L));
    }

    /**
     * 测试 getWearableNailStyleConfig 方法在正常情况下能够正确返回配置
     */
    @Test
    public void testGetWearableNailStyleConfigReturnsCorrectConfig() {
        // arrange
        Map<String, String> expectedConfig = new HashMap<>();
        expectedConfig.put("style1", "10");
        expectedConfig.put("style2", "20");
        mocked.when(() ->Lion.getMap(anyString(),anyString(), any(), any())).thenReturn(expectedConfig);

        // act
        Map<String, String> actualConfig = LionConfigUtils.getWearableNailStyleConfig();

        // assert
        assertEquals("应该返回正确的配置", expectedConfig, actualConfig);
    }

    /**
     * 测试getMultiStyleSizeConfig方法，当categoryId和serviceType都为null时
     */
    @Test
    public void testGetMultiStyleSizeConfigWhenCategoryIdAndServiceTypeAreNull() {
        // arrange
        Map<String, Integer> configMap = Collections.singletonMap("default", 10);
        mocked.when(() -> Lion.getMap(anyString(), anyString(), any(), any())).thenReturn(configMap);

        // act
        Integer result = LionConfigUtils.getMultiStyleSizeConfig(null, null);

        // assert
        assertEquals(Integer.valueOf(10), result);
    }


    /**
     * 测试getMultiStyleSizeConfig方法，当categoryId有效且serviceType为空时
     */
    @Test
    public void testGetMultiStyleSizeConfigWhenCategoryIdIsValidAndServiceTypeIsNull() {
        // arrange
        Map<String, Integer> configMap = new HashMap<>();
        configMap.put("1", 15);
        mocked.when(() -> Lion.getMap(anyString(), anyString(), any(), any())).thenReturn(configMap);


        // act
        Integer result = LionConfigUtils.getMultiStyleSizeConfig(1L, "");

        // assert
        assertEquals(Integer.valueOf(10), result);
    }

    /**
     * 测试getMultiStyleSizeConfig方法，当categoryId和serviceType都有效时
     */
    @Test
    public void testGetMultiStyleSizeConfigWhenCategoryIdAndServiceTypeAreValid() {
        // arrange
        Map<String, Integer> configMap = new HashMap<>();
        configMap.put("1.美甲", 20);
        mocked.when(() -> Lion.getMap(anyString(), anyString(), any(), any())).thenReturn(configMap);


        // act
        Integer result = LionConfigUtils.getMultiStyleSizeConfig(1L, "美甲");

        // assert
        assertEquals(Integer.valueOf(20), result);
    }

    /**
     * 测试getWearableNailShopConfig方法，当categoryDTO为null时应返回false
     */
    @Test
    public void testGetWearableNailShopConfig_NullCategoryDTO() {
        // arrange
        DealGroupCategoryDTO categoryDTO = null;

        // act
        boolean result = LionConfigUtils.getShopIDToTagIDConfig(categoryDTO);

        // assert
        assertFalse(result);
    }

    /**
     * 测试getWearableNailShopConfig方法，当categoryDTO非null但Lion返回空列表时应返回false
     */
    @Test
    public void testGetWearableNailShopConfig_EmptyLionList() {
        // arrange
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(505L);
        categoryDTO.setServiceType("按摩");
        mocked.when(() ->Lion.getList(anyString(), anyString(), any(),
                any())).thenReturn(Collections.emptyList());

        // act
        boolean result = LionConfigUtils.getShopIDToTagIDConfig(categoryDTO);

        // assert
        assertFalse(result);
    }

    /**
     * 测试getWearableNailShopConfig方法，当categoryDTO非null且Lion返回的列表包含对应的key时应返回true
     */
    @Test
    public void testGetWearableNailShopConfig_ValidKeyInLionList() {
        // arrange
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(505L);
        categoryDTO.setServiceType("按摩");
        String key = "505.按摩";
        mocked.when(() ->Lion.getList(anyString(), anyString(), any(),
                any())).thenReturn(Arrays.asList(key));

        // act
        boolean result = LionConfigUtils.getShopIDToTagIDConfig(categoryDTO);

        // assert
        assertTrue(result);
    }

    /**
     * 测试getWearableNailShopTagIdConfig方法，当Lion返回非空Map时
     */
    @Test
    public void testGetWearableNailShopTagIdConfig_NonEmptyMap() {
        // arrange
        Map<String, Long> expectedMap = new HashMap<>();
        expectedMap.put("testKey", 1L);
        mocked.when(() -> Lion.getMap(anyString(), anyString(), any(),
               any())).thenReturn(expectedMap);

        // act
        Map<String, Long> result = LionConfigUtils.getWearableNailShopTagIdConfig();

        // assert
        assertEquals("当Lion返回非空Map时，应返回相同的Map", expectedMap, result);
    }

    /**
     * 测试 getDealBtnPriceStrengthTagCategoryIds 方法在正常情况下的行为
     */
    @Test
    public void testGetDealBtnPriceStrengthTagCategoryIdsNormal() throws Throwable {
        // arrange
        mocked.when(() -> Lion.getList(anyString(), anyString(), any(), any())).thenReturn(Arrays.asList(1, 2, 3));
        // act
        List<Integer> result = LionConfigUtils.getDealBtnPriceStrengthTagCategoryIds();
        // assert
        assertEquals(Arrays.asList(1, 2, 3), result);
    }

    /**
     * 测试isMtLiveMiniAppInternal方法，当Lion返回true时
     */
    @Test
    public void testIsMtLiveMiniAppInternalWhenLionReturnsTrue() {
        // arrange
        mocked.when(() -> Lion.getBoolean(anyString(), anyString(), anyBoolean())).thenReturn(true);

        // act
        boolean result = LionConfigUtils.isMtLiveMiniAppInternal();

        // assert
        assertTrue("当Lion返回true时，isMtLiveMiniAppInternal应返回true", result);
    }

    /**
     * 测试isMtLiveMiniAppInternal方法，当Lion返回false时
     */
    @Test
    public void testIsMtLiveMiniAppInternalWhenLionReturnsFalse() {
        // arrange
        mocked.when(() -> Lion.getBoolean(anyString(), anyString(), anyBoolean())).thenReturn(false);

        // act
        boolean result = LionConfigUtils.isMtLiveMiniAppInternal();

        // assert
        assertFalse("当Lion返回false时，isMtLiveMiniAppInternal应返回false", result);
    }

    @Test
    public void testGetTradeAssuranceLayerConfigIsMtTrue() throws Throwable {
        // arrange
        boolean isMt = true;

        // act
        LayerConfig config = JsonUtils.fromJson("{\n" +
                "    \"desc\": \"若过期未预约会为您全额退款。\\n您在完成拍摄前有权随时停止交易，若未预约具体拍摄时间可全额退款。\",\n" +
                "    \"icon\": \"https://p0.meituan.net/ingee/2860c04f209c5ebe48ad7e05a726de711937.png\",\n" +
                "    \"jumpUrl\": \"https://cube.meituan.com/cube/block/e1f196fa0597/291608/index.html\",\n" +
                "    \"title\": \"未预约随时退\",\n" +
                "    \"type\": 9\n" +
                "  }", LayerConfig.class);

        Map<String, LayerConfig> configMap = new HashMap<>();
        configMap.put("mt100200954", config);

        mocked.when(() -> Lion.getMap(anyString(), anyString(), any(), any())).thenReturn(configMap);

        Map<Long, LayerConfig> result = LionConfigUtils.getTradeAssuranceLayerConfig(isMt);

        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(100200954L));
        assertEquals("未预约随时退", result.get(100200954L).getTitle());
    }

    /**
     * 测试 getPerformanceGuaranteeFeatureList，取用团购二级类目
     */
    @Test
    public void test_getPerformanceGuaranteeFeatureList_categoryId() {
        // arrange
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(501L);
        categoryDTO.setServiceTypeId(411L);
        String featureDetailMapStr = "{\n" +
                "  \"501\" : [\n" +
                "    {\n" +
                "      \"id\": 1,\n" +
                "      \"text\": \"不满意重做\",\n" +
                "      \"type\": 1,\n" +
                "      \"style\": \"#BA4E25\",\n" +
                "      \"layerConfig\": {\n" +
                "        \"icon\" : \"xxx\",\n" +
                "        \"title\": \"不满意重做\",\n" +
                "        \"jumpUrl\": \"xxx\",\n" +
                "        \"desc\": \"待定\",\n" +
                "        \"miniJumpUrl\": \"xxx\"\n" +
                "      }\n" +
                "    }\n" +
                "  ],\n" +
                "  \"502.456\": [\n" +
                "    {\n" +
                "      \"id\": 2,\n" +
                "      \"text\": \"比线下省\",\n" +
                "      \"type\": 1,\n" +
                "      \"style\": \"#BA4E25\",\n" +
                "      \"layerConfig\": {\n" +
                "        \"icon\" : \"xxx\",\n" +
                "        \"title\": \"比线下省\",\n" +
                "        \"jumpUrl\": \"xxx\",\n" +
                "        \"desc\": \"待定\",\n" +
                "        \"miniJumpUrl\": \"xxx\"\n" +
                "      }\n" +
                "    }\n" +
                "  ]\n" +
                "}";
        mocked.when(() -> Lion.getString(anyString(), anyString(), anyString())).thenReturn(featureDetailMapStr);

        // act
        List<FeatureDetailDTO> result = LionConfigUtils.getPerformanceGuaranteeFeatureList(categoryDTO);

        // assert
        assertTrue(result.size() == 1);
        assertTrue(result.get(0).getText().equals("不满意重做"));
        assertTrue(result.get(0).getLayerConfig().getDesc().equals("待定"));
    }

    /**
     * 测试 getPerformanceGuaranteeFeatureList，取用服务类型ID
     */
    @Test
    public void test_getPerformanceGuaranteeFeatureDetailMap_categoryIdAndServiceTypeId() {
        // arrange
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(502L);
        categoryDTO.setServiceTypeId(456L);
        String featureDetailMapStr = "{\n" +
                "  \"501\" : [\n" +
                "    {\n" +
                "      \"id\": 1,\n" +
                "      \"text\": \"不满意重做\",\n" +
                "      \"type\": 1,\n" +
                "      \"style\": \"#BA4E25\",\n" +
                "      \"layerConfig\": {\n" +
                "        \"icon\" : \"xxx\",\n" +
                "        \"title\": \"不满意重做\",\n" +
                "        \"jumpUrl\": \"xxx\",\n" +
                "        \"desc\": \"待定\",\n" +
                "        \"miniJumpUrl\": \"xxx\"\n" +
                "      }\n" +
                "    }\n" +
                "  ],\n" +
                "  \"502.456\": [\n" +
                "    {\n" +
                "      \"id\": 2,\n" +
                "      \"text\": \"比线下省\",\n" +
                "      \"type\": 1,\n" +
                "      \"style\": \"#BA4E25\",\n" +
                "      \"layerConfig\": {\n" +
                "        \"icon\" : \"xxx\",\n" +
                "        \"title\": \"比线下省\",\n" +
                "        \"jumpUrl\": \"xxx\",\n" +
                "        \"desc\": \"待定\",\n" +
                "        \"miniJumpUrl\": \"xxx\"\n" +
                "      }\n" +
                "    }\n" +
                "  ]\n" +
                "}";
        mocked.when(() -> Lion.getString(anyString(), anyString(), anyString())).thenReturn(featureDetailMapStr);

        // act
        List<FeatureDetailDTO> result = LionConfigUtils.getPerformanceGuaranteeFeatureList(categoryDTO);

        // assert
        assertTrue(result.size() == 1);
        assertTrue(result.get(0).getText().equals("比线下省"));
        assertTrue(result.get(0).getLayerConfig().getDesc().equals("待定"));
    }

    /**
     * 测试enableShowResvAndBuyBtns方法，当Lion返回空字符串时应返回false
     */
    @Test
    public void testHitLeadsDeal_WithEmptyLionResponse() {
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(505L);
        categoryDTO.setServiceTypeId(12312L);

        mocked.when(() -> Lion.getList(anyString(), anyString(), any(), any())).thenReturn(Lists.newArrayList());

        boolean result = LionConfigUtils.hitLeadsDeal(categoryDTO);
        assertFalse(result);
    }

    /**
     * 测试enableShowResvAndBuyBtns方法，当Lion返回有效配置且包含categoryId时应返回true
     */
    @Test
    public void testHitLeadsDeal_WithValidLionResponseContainingCategoryId() {
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(505L);
        categoryDTO.setServiceTypeId(12312L);

        mocked.when(() -> Lion.getList(anyString(), anyString(), any(), any())).thenReturn(Lists.newArrayList("505.12312"));

        boolean result = LionConfigUtils.hitLeadsDeal(categoryDTO);
        assertTrue(result);
    }

    /**
     * 测试enableShowResvAndBuyBtns方法，当Lion返回有效配置但不包含categoryId时应返回false
     */
    @Test
    public void testEnableShowResvAndBuyBtns_WithValidLionResponseNotContainingCategoryId() {
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(505L);
        categoryDTO.setServiceTypeId(12312L);

        mocked.when(() -> Lion.getList(anyString(), anyString(), any(), any())).thenReturn(Lists.newArrayList("506.123"));

        boolean result = LionConfigUtils.hitLeadsDeal(categoryDTO);
        assertFalse(result);
    }

    @Test
    public void testGetResvAndBuyBarConfig() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx dealCtx = new DealCtx(envCtx);
        DealGroupDTO dealGorupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(506L);
        categoryDTO.setServiceTypeId(12313L);
        dealGorupDTO.setCategory(categoryDTO);
        dealCtx.setDealGroupDTO(dealGorupDTO);

        LeadsDealBarConfig config = new LeadsDealBarConfig();
        config.setBuyBtnTitle("buyBtnTitle");
        Map<String, LeadsDealBarConfig> map = Maps.newHashMap();
        map.put("506.12313", config);
        mocked.when(() -> Lion.getString(anyString(), anyString(), anyString())).thenReturn(JSON.toJSONString(map));

        LeadsDealBarConfig leadsDealBarConfig = LionConfigUtils.getLeadsDealBarConfig(dealCtx);

        assertNotNull(leadsDealBarConfig);
        assertEquals(leadsDealBarConfig.getBuyBtnTitle(), "buyBtnTitle");
    }

    @Test
    public void testGetPreOrderGuaranteeFeatConfig() {
        PreOrderFeatureConfigDTO config = new PreOrderFeatureConfigDTO();
        mocked.when(() -> Lion.getString(anyString(), anyString(), anyString())).thenReturn(JSON.toJSONString(config));
        PreOrderFeatureConfigDTO result = LionConfigUtils.getPreOrderGuaranteeFeatConfig();
        assertNotNull(result);
    }

    @Test
    public void testGetPreOrderGuaranteeFeatConfig_Error() {
        mocked.when(() -> Lion.getString(anyString(), anyString(), anyString())).thenReturn("error");
        PreOrderFeatureConfigDTO result = LionConfigUtils.getPreOrderGuaranteeFeatConfig();
        assertNull(result);
    }

    /**
     * 测试 userId 为 null 的情况
     */
    @Test
    public void testInSnapshotUserIdGrayWithNullUserId() {
        assertFalse("UserId为null时应返回false", LionConfigUtils.inSnapshotUserIdGray(null));
    }

    /**
     * 测试 userId 小于等于 0 的情况
     */
    @Test
    public void testInSnapshotUserIdGrayWithNegativeOrZeroUserId() {
        assertFalse("UserId小于等于0时应返回false", LionConfigUtils.inSnapshotUserIdGray(0L));
    }

    /**
     * 测试 userId 在白名单中的情况
     */
    @Test
    public void testInSnapshotUserIdGrayWithUserIdInWhiteList() {
        LogUserGrayConfig grayConfig = new LogUserGrayConfig(0, 100, Lists.newArrayList(100L));
        grayConfig.setGrayWhiteList(Collections.singletonList(100L));
        mocked.when(() -> Lion.getBean(anyString(), anyString(), any(), any())).thenReturn(grayConfig);
        assertTrue("UserId在白名单中时应返回true", LionConfigUtils.inSnapshotUserIdGray(100L));
    }

    /**
     * 测试 userId 不在白名单且不在灰度范围内的情况
     */
    @Test
    public void testInSnapshotUserIdGrayWithUserIdNotInGrayRange() {
        LogUserGrayConfig grayConfig = new LogUserGrayConfig(0, 100, Collections.emptyList());
        grayConfig.setGrayGroupRange(100);
        grayConfig.setGrayRange(50);
        grayConfig.setGrayWhiteList(Lists.newArrayList(50L));
        mocked.when(() -> Lion.getBean(anyString(), anyString(), any(), any())).thenReturn(grayConfig);
        assertFalse("UserId不在白名单且不在灰度范围内时应返回false", LionConfigUtils.inSnapshotUserIdGray(51L));
    }
}
