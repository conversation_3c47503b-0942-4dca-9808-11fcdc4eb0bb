package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.service.liveroomadmin.LiveRoomRpcService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.concurrent.Future;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.anyString;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import static org.mockito.Mockito.doThrow;

public class PrivateLiveWrapper_PreGetLiveRoomDetailTest {

    @InjectMocks
    private PrivateLiveWrapper privateLiveWrapper;

    @Mock
    private LiveRoomRpcService liveRoomRpcServiceFuture;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试preGetLiveRoomDetail方法，传入的liveId为空的情况
     */
    @Test
    public void testPreGetLiveRoomDetailWithEmptyLiveId() throws Throwable {
        // arrange
        String liveId = "";
        // act
        Future result = privateLiveWrapper.preGetLiveRoomDetail(liveId);
        // assert
        assertNull(result);
    }

    /**
     * 测试preGetLiveRoomDetail方法，传入的liveId不为空，但获取直播间详细信息出现异常的情况
     */
    @Test
    public void testPreGetLiveRoomDetailExceptionCase() throws Throwable {
        // arrange
        String liveId = "123";
        when(liveRoomRpcServiceFuture.getLiveRoomDetail(anyString())).thenThrow(new RuntimeException());
        // act
        Future result = privateLiveWrapper.preGetLiveRoomDetail(liveId);
        // assert
        assertNull(result);
    }
}
