package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageVO;
import com.dianping.mobile.mapi.dztgdetail.entity.ExhibitImageConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.ExhibitTextConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.QueryExhibitImageParam;
import com.dianping.mobile.mapi.dztgdetail.util.JsonUtils;
import com.google.common.collect.Maps;
import com.sankuai.mpmctcontent.query.thrift.api.search.ContentSearchService;
import com.sankuai.mpmctcontent.query.thrift.dto.meta.BatchQueryTagValueResponseDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.search.SearchDetailResponseDTO;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;

import static org.mockito.Mockito.mockStatic;

/**
 * @Author: <EMAIL>
 * @Date: 2024/8/18
 */
@RunWith(MockitoJUnitRunner.class)
public class ContentSearchWrapperTest {
    @InjectMocks
    private ContentSearchWrapper contentSearchWrapper;
    @Mock
    private ContentSearchService contentSearchServiceFuture;
    private MockedStatic<Lion> lionMockedStatic;

    @Before
    public void setUp() {
        lionMockedStatic = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        lionMockedStatic.close();
    }

    @Test
    public void testGetDealGroupStyleImageName() {
        QueryExhibitImageParam param = QueryExhibitImageParam.builder()
                .shopId(1L)
                .start(0)
                .limit(10)
                .infoContentId(11L)
                .categoryId(406)
                .build();
        Mockito.when(contentSearchServiceFuture.searchDetail(Mockito.any())).thenReturn(new SearchDetailResponseDTO());
        contentSearchWrapper.getDealGroupStyleImageName(param);
        Assert.assertTrue(Objects.nonNull(param));
    }

    @Test
    public void testGetImmersiveImageVO() {
        String str = "{\"code\":200,\"lastPage\":false,\"msg\":\"执行成功\",\"nextPageStartPos\":10,\"searchResult\":[{\"dataId\":\"1824372964607442991\",\"dataInfo\":\"{\\\"top\\\": 0, \\\"rank\\\": 1723799376318, \\\"editTime\\\": 1723801569000, \\\"stylePic\\\": [{\\\"url\\\": \\\"https://p1.meituan.net/merchantpic/76ee1f99a2c329d0b9e458562778a7ab73865.jpg\\\", \\\"width\\\": 0, \\\"height\\\": 0}], \\\"styleName\\\": \\\"点多+审核验证\\\", \\\"relateInfo\\\": {\\\"ownerId\\\": 4655434, \\\"mtShopIds\\\": [4655434], \\\"ownerType\\\": 1}, \\\"styleBrand\\\": 502, \\\"styleGroup\\\": [1328630108434], \\\"stylePrice\\\": 345, \\\"styleShape\\\": 201, \\\"styleTheme\\\": [401], \\\"recommended\\\": 1, \\\"relatedDeal\\\": [1026368354, 1026364374], \\\"infoContentId\\\": 1329119109434, \\\"styleMaterial\\\": [301], \\\"sys_createTime\\\": 1723799376000, \\\"sys_dz_mt_dealId\\\": [1026364374, 1026368354]}\"},{\"dataId\":\"1824372919027941406\",\"dataInfo\":\"{\\\"top\\\": 0, \\\"rank\\\": 1723799364290, \\\"editTime\\\": 1723800256000, \\\"stylePic\\\": [{\\\"url\\\": \\\"https://p0.inf.test.sankuai.com/dpmerchantpic/fe925f016c5f60dfa91b336ae4ede7b6183997.jpg\\\", \\\"width\\\": 0, \\\"height\\\": 0}], \\\"styleName\\\": \\\"4\\\", \\\"relateInfo\\\": {\\\"ownerId\\\": 4655434, \\\"mtShopIds\\\": [4655434], \\\"ownerType\\\": 1}, \\\"styleBrand\\\": 503, \\\"styleGroup\\\": [1328998108434], \\\"stylePrice\\\": 4, \\\"styleShape\\\": 201, \\\"styleTheme\\\": [401], \\\"recommended\\\": 1, \\\"relatedDeal\\\": [1022296083, 1022296297, 1022714722, 1022295162], \\\"infoContentId\\\": 1329115109434, \\\"styleMaterial\\\": [302], \\\"sys_createTime\\\": 1723799364000, \\\"sys_dz_mt_dealId\\\": [1022295162, 1022296083, 1022296297, 1022714722]}\"},{\"dataId\":\"1824372309473935370\",\"dataInfo\":\"{\\\"top\\\": 0, \\\"rank\\\": 1723799220221, \\\"editTime\\\": 1723799856000, \\\"stylePic\\\": [{\\\"url\\\": \\\"https://p0.inf.test.sankuai.com/dpmerchantpic/09d8c7e52f44d1b5befa42a97a6cff378501138.jpg\\\", \\\"width\\\": 3904, \\\"height\\\": 2440}], \\\"styleName\\\": \\\"推荐款式2号\\\", \\\"relateInfo\\\": {\\\"ownerId\\\": 4655434, \\\"mtShopIds\\\": [4655434], \\\"ownerType\\\": 1}, \\\"styleBrand\\\": 503, \\\"styleGroup\\\": [1328977108434], \\\"stylePrice\\\": 988, \\\"styleShape\\\": 200, \\\"styleTheme\\\": [401], \\\"recommended\\\": 1, \\\"relatedDeal\\\": [1026368354, 1022714722, 1022296297, 1022294506, 1022295162, 1020196507, 1022294266], \\\"infoContentId\\\": 1329107109434, \\\"styleMaterial\\\": [300, 301], \\\"sys_createTime\\\": 1723799220000, \\\"sys_dz_mt_dealId\\\": [1022294506, 1022295162, 1022294266, 1020196507, 1022296297, 1022714722, 1026368354]}\"},{\"dataId\":\"1824372309482323970\",\"dataInfo\":\"{\\\"top\\\": 0, \\\"rank\\\": 1723799220219, \\\"editTime\\\": 1723800236000, \\\"stylePic\\\": [{\\\"url\\\": \\\"https://p0.inf.test.sankuai.com/dpmerchantpic/060d8d6379989b39db8d7cc69c75304b429038.jpg@3840_2160m\\\", \\\"width\\\": 3840, \\\"height\\\": 2160}], \\\"styleName\\\": \\\"测试推荐款式AA\\\", \\\"relateInfo\\\": {\\\"ownerId\\\": 4655434, \\\"mtShopIds\\\": [4655434], \\\"ownerType\\\": 1}, \\\"styleBrand\\\": 504, \\\"styleGroup\\\": [1328977108434], \\\"stylePrice\\\": 9888, \\\"styleShape\\\": 201, \\\"styleTheme\\\": [400, 403], \\\"recommended\\\": 1, \\\"relatedDeal\\\": [1022714722, 1022296297, 1026364374, 1026368354], \\\"infoContentId\\\": 1329108109434, \\\"styleMaterial\\\": [307, 306, 304], \\\"sys_createTime\\\": 1723799220000, \\\"sys_dz_mt_dealId\\\": [1026364374, 1022296297, 1022714722, 1026368354]}\"},{\"dataId\":\"1824372057773752371\",\"dataInfo\":\"{\\\"top\\\": 0, \\\"rank\\\": 1723799161207, \\\"editTime\\\": 1723799804000, \\\"stylePic\\\": [{\\\"url\\\": \\\"https://p1.meituan.net/dpmerchantpic/b3722776c9e54a97d5bfdd84ae466357860211.png@1656_1480m\\\", \\\"width\\\": 1656, \\\"height\\\": 1480}], \\\"styleName\\\": \\\"3\\\", \\\"relateInfo\\\": {\\\"ownerId\\\": 4655434, \\\"mtShopIds\\\": [4655434], \\\"ownerType\\\": 1}, \\\"styleBrand\\\": 502, \\\"styleGroup\\\": [1328977108434], \\\"stylePrice\\\": 333, \\\"styleShape\\\": 201, \\\"styleTheme\\\": [401], \\\"recommended\\\": 1, \\\"relatedDeal\\\": [1026368354, 1026364374], \\\"infoContentId\\\": 1329105109434, \\\"styleMaterial\\\": [301], \\\"sys_createTime\\\": 1723799161000, \\\"sys_dz_mt_dealId\\\": [1026364374, 1026368354]}\"},{\"dataId\":\"1824372921120899125\",\"dataInfo\":\"{\\\"top\\\": 0, \\\"rank\\\": 1723799364269, \\\"editTime\\\": 1723799748000, \\\"stylePic\\\": [{\\\"url\\\": \\\"https://p0.inf.test.sankuai.com/dpmerchantpic/b677073a4bb21f9f99bf9f8c55f46b4b2185312.jpg@4032_3024m\\\", \\\"width\\\": 4032, \\\"height\\\": 3024}], \\\"styleName\\\": \\\"9\\\", \\\"relateInfo\\\": {\\\"ownerId\\\": 4655434, \\\"mtShopIds\\\": [4655434], \\\"ownerType\\\": 1}, \\\"styleBrand\\\": 504, \\\"styleGroup\\\": [1328977108434, 1328625108434], \\\"stylePrice\\\": 9, \\\"styleShape\\\": 202, \\\"styleTheme\\\": [402], \\\"recommended\\\": 0, \\\"relatedDeal\\\": [1023630756, 1026364374, 1022714722], \\\"infoContentId\\\": 1329111109434, \\\"styleMaterial\\\": [302], \\\"sys_createTime\\\": 1723799364000, \\\"sys_dz_mt_dealId\\\": [1023630756, 1022714722, 1026364374]}\"},{\"dataId\":\"1824372913483071494\",\"dataInfo\":\"{\\\"top\\\": 0, \\\"rank\\\": 1723799364259, \\\"editTime\\\": 1723799838000, \\\"stylePic\\\": [{\\\"url\\\": \\\"https://p0.inf.test.sankuai.com/dpmerchantpic/32291436b5bdad6dc02f174850cd8db0179729.jpg@808_985m\\\", \\\"width\\\": 808, \\\"height\\\": 985}], \\\"styleName\\\": \\\"8\\\", \\\"relateInfo\\\": {\\\"ownerId\\\": 4655434, \\\"mtShopIds\\\": [4655434], \\\"ownerType\\\": 1}, \\\"styleBrand\\\": 513, \\\"styleGroup\\\": [1328977108434], \\\"stylePrice\\\": 8, \\\"styleShape\\\": 201, \\\"styleTheme\\\": [401], \\\"recommended\\\": 0, \\\"relatedDeal\\\": [1022714722, 1023630756, 1022296083], \\\"infoContentId\\\": 1329109109434, \\\"styleMaterial\\\": [302], \\\"sys_createTime\\\": 1723799364000, \\\"sys_dz_mt_dealId\\\": [1022296083, 1023630756, 1022714722]}\"},{\"dataId\":\"1824372915286622249\",\"dataInfo\":\"{\\\"top\\\": 0, \\\"rank\\\": 1723799364281, \\\"editTime\\\": 1723799732000, \\\"stylePic\\\": [{\\\"url\\\": \\\"https://p0.inf.test.sankuai.com/dpmerchantpic/a3bd29b7b355ecce066ae9bd821f19f956980.jpg\\\", \\\"width\\\": 0, \\\"height\\\": 0}], \\\"styleName\\\": \\\"1\\\", \\\"relateInfo\\\": {\\\"ownerId\\\": 4655434, \\\"mtShopIds\\\": [4655434], \\\"ownerType\\\": 1}, \\\"styleBrand\\\": 513, \\\"styleGroup\\\": [1328998108434], \\\"stylePrice\\\": 1, \\\"styleShape\\\": 201, \\\"styleTheme\\\": [401], \\\"recommended\\\": 0, \\\"relatedDeal\\\": [1022296083, 1022296297, 1022714722], \\\"infoContentId\\\": 1329112109434, \\\"styleMaterial\\\": [301, 302], \\\"sys_createTime\\\": 1723799364000, \\\"sys_dz_mt_dealId\\\": [1022296083, 1022296297, 1022714722]}\"},{\"dataId\":\"1824372913478877276\",\"dataInfo\":\"{\\\"top\\\": 0, \\\"rank\\\": 1723799364273, \\\"editTime\\\": 1723799364000, \\\"stylePic\\\": [{\\\"url\\\": \\\"https://p0.inf.test.sankuai.com/dpmerchantpic/fe65ce42be495428f216af7cfd5d69717456741.jpg@3024_4032m\\\", \\\"width\\\": 3024, \\\"height\\\": 4032}], \\\"styleName\\\": \\\"7\\\", \\\"relateInfo\\\": {\\\"ownerId\\\": 4655434, \\\"mtShopIds\\\": [4655434], \\\"ownerType\\\": 1}, \\\"styleBrand\\\": 503, \\\"styleGroup\\\": [1328977108434], \\\"stylePrice\\\": 7, \\\"styleShape\\\": 202, \\\"styleTheme\\\": [401], \\\"recommended\\\": 0, \\\"infoContentId\\\": 1329113109434, \\\"styleMaterial\\\": [302], \\\"sys_createTime\\\": 1723799364000}\"},{\"dataId\":\"1824372919212490803\",\"dataInfo\":\"{\\\"top\\\": 0, \\\"rank\\\": 1723799364290, \\\"editTime\\\": 1723801097000, \\\"stylePic\\\": [{\\\"url\\\": \\\"https://p0.inf.test.sankuai.com/dpmerchantpic/a3bd29b7b355ecce066ae9bd821f19f956980.jpg\\\", \\\"width\\\": 0, \\\"height\\\": 0}], \\\"styleName\\\": \\\"2\\\", \\\"relateInfo\\\": {\\\"ownerId\\\": 4655434, \\\"mtShopIds\\\": [4655434], \\\"ownerType\\\": 1}, \\\"styleBrand\\\": 503, \\\"styleGroup\\\": [1328998108434], \\\"stylePrice\\\": 2, \\\"styleShape\\\": 201, \\\"styleTheme\\\": [401], \\\"recommended\\\": 0, \\\"infoContentId\\\": 1329114109434, \\\"styleMaterial\\\": [301], \\\"sys_createTime\\\": 1723799364000}\"}],\"totalHitCount\":14}";
        SearchDetailResponseDTO responseDTO = JsonUtils.fromJson(str, SearchDetailResponseDTO.class);
        BatchQueryTagValueResponseDTO tagValueResponseDTO = JsonUtils.fromJson("{\"code\":200,\"metaInfoDTOList\":[{\"metaInfoEntityIdDTO\":{\"bizLine\":23,\"entityName\":\"material_glasses_frame_style\"},\"tagValuePathMap\":{\"styleTheme\":[{\"label\":\"明星同款\",\"tagId\":400},{\"label\":\"复古风\",\"tagId\":401},{\"label\":\"适合大脸\",\"tagId\":402},{\"label\":\"素颜神器\",\"tagId\":403},{\"label\":\"显脸小\",\"tagId\":404},{\"label\":\"日系\",\"tagId\":405},{\"label\":\"轻奢风\",\"tagId\":406},{\"label\":\"韩系\",\"tagId\":407},{\"label\":\"斯文风\",\"tagId\":408}],\"styleMaterial\":[{\"label\":\"合金\",\"tagId\":304},{\"label\":\"木材\",\"tagId\":305},{\"label\":\"不锈钢\",\"tagId\":306},{\"label\":\"混合材质\",\"tagId\":307},{\"label\":\"纯钛\",\"tagId\":300},{\"label\":\"β钛\",\"tagId\":301},{\"label\":\"TR材质\",\"tagId\":302},{\"label\":\"板材\",\"tagId\":303}],\"styleBrand\":[{\"label\":\"Amani\",\"tagId\":512},{\"label\":\"Prada\",\"tagId\":513},{\"label\":\"欧克利\",\"tagId\":514},{\"label\":\"GENTLE MONSTER\",\"tagId\":515},{\"label\":\"DIOR\",\"tagId\":516},{\"label\":\"万年龟\",\"tagId\":517},{\"label\":\"夏蒙\",\"tagId\":518},{\"label\":\"海俪恩\",\"tagId\":519},{\"label\":\"暴龙\",\"tagId\":500},{\"label\":\"雷朋\",\"tagId\":501},{\"label\":\"陌森\",\"tagId\":502},{\"label\":\"帕莎\",\"tagId\":503},{\"label\":\"海伦凯勒\",\"tagId\":504},{\"label\":\"aojo\",\"tagId\":505},{\"label\":\"晴姿\",\"tagId\":506},{\"label\":\"LOHO\",\"tagId\":507},{\"label\":\"木九十\",\"tagId\":508},{\"label\":\"派丽蒙\",\"tagId\":509},{\"label\":\"GUCCI\",\"tagId\":510},{\"label\":\"林德伯格\",\"tagId\":511}],\"styleShape\":[{\"label\":\"圆形\",\"tagId\":200},{\"label\":\"椭圆框\",\"tagId\":201},{\"label\":\"半框\",\"tagId\":202},{\"label\":\"方圆框\",\"tagId\":203},{\"label\":\"方框\",\"tagId\":204},{\"label\":\"长方框\",\"tagId\":205},{\"label\":\"多边形框\",\"tagId\":206}]}}],\"msg\":\"执行成功\"}", BatchQueryTagValueResponseDTO.class);
        int categoryId = 406;
        QueryExhibitImageParam param = JsonUtils.fromJson("{\"categoryId\":406,\"clientType\":200502,\"dpDealGroupId\":423970113,\"limit\":10,\"shopId\":4655434,\"start\":0}", QueryExhibitImageParam.class);
        ExhibitImageConfig imageConfig = JsonUtils.fromJson("{\"defaultImageBestScale\":\"3:4\",\"excludeStyleTagKey\":[],\"itemDisplayStyle\":1,\"multiStyleTagKey\":[\"\"],\"title\":\"本团购可参考款式\"}", ExhibitImageConfig.class);
        Map<String ,ExhibitImageConfig> configMap = Maps.newHashMap();
        configMap.put("406", imageConfig);
        lionMockedStatic.when(() -> Lion.getMap(LionConstants.APP_KEY,
                LionConstants.IMMERSIVE_EXHIBIT_IMAGE_CONFIG, ExhibitImageConfig.class)).thenReturn(configMap);


        ExhibitTextConfig textConfig = new ExhibitTextConfig();
        textConfig.setTitle("参考款式");
        textConfig.setShowAllText("查看全部");
        Map<String,ExhibitTextConfig> textConfigMap = Maps.newHashMap();
        textConfigMap.put("406", textConfig);
        lionMockedStatic.when(() ->  Lion.getMap(LionConstants.APP_KEY,
                LionConstants.CATEGORY_EXHIBIT_TITLE_CONFIG, ExhibitTextConfig.class, Collections.EMPTY_MAP)).thenReturn(textConfigMap);

        ImmersiveImageVO vo = contentSearchWrapper.getImmersiveImageVO(responseDTO, tagValueResponseDTO, categoryId, param);
        Assert.assertTrue("本团购可参考款式".equals(vo.getTitle()) && "查看全部".equals(vo.getShowAllText()));
    }

    @Test
    public void test_getEyebrowCaseStyleImage() {
        QueryExhibitImageParam param = QueryExhibitImageParam.builder()
                .shopId(1L)
                .start(0)
                .limit(10)
                .infoContentId(11L)
                .dpDealGroupId(111)
                .categoryId(512)
//                .relatedDeal(111)
                .build();
        Mockito.when(contentSearchServiceFuture.searchDetail(Mockito.any())).thenReturn(new SearchDetailResponseDTO());
        contentSearchWrapper.getDealGroupStyleImageName(param);
        Assert.assertTrue(Objects.nonNull(param));
    }

    @Test
    public void testGetImmersiveImageVO_512() {
        String str = "{\n" + "    \"code\": 200,\n" + "    \"lastPage\": true,\n" + "    \"msg\": \"执行成功\",\n" + "    \"nextPageStartPos\": 0,\n" + "    \"searchResult\": [\n" + "        {\n" + "            \"dataId\": \"1808858951651987456\",\n" + "            \"dataInfo\": \"{\\\"top\\\": 0, \\\"pics\\\": [{\\\"url\\\": \\\"https://p0.inf.test.sankuai.com/dpmerchantpic/4d6f3be3a22a6e3c8296bcb636c36993150503.jpg\\\", \\\"width\\\": 1170, \\\"height\\\": 2532}, {\\\"url\\\": \\\"https://p0.inf.test.sankuai.com/dpmerchantpic/1e4f877faff85a248e0511f2715b697980647.jpg\\\", \\\"width\\\": 720, \\\"height\\\": 960}, {\\\"url\\\": \\\"https://p0.inf.test.sankuai.com/dpmerchantpic/912a8e002ff2ba91557226895c736fdc34045.jpg\\\", \\\"width\\\": 630, \\\"height\\\": 473}], \\\"rank\\\": 1720085409963, \\\"tags\\\": [\\\"粉雾眉\\\", \\\"油性皮肤\\\"], \\\"style\\\": 1, \\\"ugcId\\\": 1325919100085, \\\"authorId\\\": 1007569148, \\\"caseName\\\": \\\"测试1\\\", \\\"editTime\\\": 1720100548000, \\\"position\\\": 1, \\\"authorType\\\": 5, \\\"relateInfo\\\": {\\\"ownerId\\\": 1007569148, \\\"ownerType\\\": 3}, \\\"relatedDeal\\\": {\\\"title\\\": \\\"【渐变雾眉 精致裸妆】轻氧眉\\\", \\\"mtDealId\\\": 1021579590, \\\"marketPrice\\\": \\\"892.00\\\", \\\"relatedMtShopIds\\\": [52433191]}, \\\"infoContentId\\\": 1325959101148, \\\"relatedMtDealId\\\": 1021579590, \\\"relatedArtisanId\\\": 1158065}\"\n" + "        }\n" + "    ],\n" + "    \"totalHitCount\": 1\n" + "}";
        String paramStr = "{\"categoryId\":512,\"dpDealGroupId\":1021579590,\"infoContentId\":0,\"limit\":10,\"mtDealGroupId\":1021579590,\"relatedDeal\":1021579590,\"serviceType\":\"\",\"shopId\":52433191,\"start\":0}";
        int categoryId = 512;

        SearchDetailResponseDTO responseDTO = JsonUtils.fromJson(str, SearchDetailResponseDTO.class);
        QueryExhibitImageParam param = JsonUtils.fromJson(paramStr, QueryExhibitImageParam.class);
        ExhibitImageConfig imageConfig = JsonUtils.fromJson("{\"defaultImageBestScale\":\"3:4\",\"excludeStyleTagKey\":[],\"itemDisplayStyle\":1,\"multiStyleTagKey\":[\"\"],\"title\":\"本团购可参考款式\"}", ExhibitImageConfig.class);
        Map<String, ExhibitImageConfig> configMap = Maps.newHashMap();
        configMap.put("512", imageConfig);
        lionMockedStatic.when(() -> Lion.getMap(LionConstants.APP_KEY, LionConstants.IMMERSIVE_EXHIBIT_IMAGE_CONFIG, ExhibitImageConfig.class)).thenReturn(configMap);


        ExhibitTextConfig textConfig = new ExhibitTextConfig();
        textConfig.setTitle("参考款式");
        textConfig.setShowAllText("查看全部");
        Map<String, ExhibitTextConfig> textConfigMap = Maps.newHashMap();
        textConfigMap.put("512", textConfig);
        lionMockedStatic.when(() -> Lion.getMap(LionConstants.APP_KEY, LionConstants.CATEGORY_EXHIBIT_TITLE_CONFIG, ExhibitTextConfig.class, Collections.EMPTY_MAP)).thenReturn(textConfigMap);

        ImmersiveImageVO vo = contentSearchWrapper.getImmersiveImageVO(responseDTO, null, categoryId, param);
        Assert.assertTrue("本团购可参考款式".equals(vo.getTitle()) && "查看全部".equals(vo.getShowAllText()));
    }
}
