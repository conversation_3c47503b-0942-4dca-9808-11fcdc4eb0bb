package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageVO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.mpmctcontent.application.thrift.dto.content.LoadDealRelatedCaseListRespDTO;
import com.sankuai.mpmctcontent.application.thrift.dto.content.ListItemDTO;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;
import org.junit.Before;

public class ImmersiveImageWrapper_GetImmersiveImageTest {

    @InjectMocks
    private ImmersiveImageWrapper immersiveImageWrapper = new ImmersiveImageWrapper();

    @Mock
    private Future<LoadDealRelatedCaseListRespDTO> future;

    @Mock
    private HaimaWrapper haimaWrapper;

    public ImmersiveImageWrapper_GetImmersiveImageTest() {
        MockitoAnnotations.initMocks(this);
        when(haimaWrapper.queryHotBeautyNailConfig()).thenReturn(Collections.emptyList());
    }

    @Test
    public void testGetImmersiveImageWithDealsWhenDealsAndFutureAreNull() throws Throwable {
        ImmersiveImageVO result = immersiveImageWrapper.getImmersiveImageWithDeals(null, null, 1, 1L, true, 1L, 1L, 1);
        assertNull(result);
    }

    @Test
    public void testGetImmersiveImageWithDealsWhenDealsIsNullAndFutureIsNotNull() throws Throwable {
        when(future.get()).thenReturn(null);
        ImmersiveImageVO result = immersiveImageWrapper.getImmersiveImageWithDeals(null, future, 1, 1L, true, 1L, 1L, 1);
        assertNull(result);
    }

    @Test
    public void testGetImmersiveImageWithDealsWhenDealsIsNotNullAndFutureIsNull() throws Throwable {
        ImmersiveImageVO result = immersiveImageWrapper.getImmersiveImageWithDeals(Collections.singletonList(new DealGroupDealDTO()), null, 1, 1L, true, 1L, 1L, 1);
        assertNull(result);
    }

    @Test
    public void testGetImmersiveImageWithDealsWhenDealsAndFutureAreNotNullAndHandleWearableNailRespReturnsNull() throws Throwable {
        when(future.get()).thenReturn(new LoadDealRelatedCaseListRespDTO());
        ImmersiveImageVO result = immersiveImageWrapper.getImmersiveImageWithDeals(Collections.singletonList(new DealGroupDealDTO()), future, 1, 1L, true, 1L, 1L, 1);
        assertNull(result);
    }
}
