package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.gmkt.activity.api.dto.BatchQueryDealActivityRequest;
import com.dianping.gmkt.activity.api.dto.BatchQueryDealActivityResponse;
import com.dianping.gmkt.activity.api.dto.DealActivityDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealActivityWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import java.lang.reflect.Method;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealPreSaleQueryFacadeGetDealActivityTest {

    @InjectMocks
    private DealPreSaleQueryFacade dealPreSaleQueryFacade;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private DealActivityWrapper dealActivityWrapper;

    @Mock
    private Future future;

    @Mock
    private BatchQueryDealActivityResponse response;

    @Mock
    private DealActivityDTO dealActivityDTO;

    private BatchQueryDealActivityRequest request;

    @Before
    public void setUp() {
        request = new BatchQueryDealActivityRequest();
    }

    private Object invokePrivateMethod(String methodName) throws Exception {
        Method method = DealPreSaleQueryFacade.class.getDeclaredMethod(methodName, BatchQueryDealActivityRequest.class, Future.class);
        method.setAccessible(true);
        return method.invoke(dealPreSaleQueryFacade, request, future);
    }

    @Test
    public void testGetDealActivityResponseIsNull() throws Throwable {
        when(dealGroupWrapper.getFutureResult(future, "", "batchQueryDealActivity")).thenReturn(null);
        assertNull(invokePrivateMethod("getDealActivity"));
    }

    @Test
    public void testGetDealActivityDealActivityDTOIsNull() throws Throwable {
        when(dealGroupWrapper.getFutureResult(future, "", "batchQueryDealActivity")).thenReturn(response);
        when(response.getDealActivityDTO()).thenReturn(null);
        assertNull(invokePrivateMethod("getDealActivity"));
    }

    @Test
    public void testGetDealActivityIsEnableIsFalse() throws Throwable {
        when(dealGroupWrapper.getFutureResult(future, "", "batchQueryDealActivity")).thenReturn(response);
        when(response.getDealActivityDTO()).thenReturn(dealActivityDTO);
        when(dealActivityDTO.isEnable()).thenReturn(false);
        assertNull(invokePrivateMethod("getDealActivity"));
    }

    @Test
    public void testGetDealActivityNormal() throws Throwable {
        when(dealGroupWrapper.getFutureResult(future, "", "batchQueryDealActivity")).thenReturn(response);
        when(response.getDealActivityDTO()).thenReturn(dealActivityDTO);
        when(dealActivityDTO.isEnable()).thenReturn(true);
        assertEquals(dealActivityDTO, invokePrivateMethod("getDealActivity"));
    }
}
