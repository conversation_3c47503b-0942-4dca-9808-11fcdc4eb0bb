package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.style.DealGroupDzxService;
import com.dianping.deal.style.dto.DealGroupDzxInfo;
import com.dianping.lion.client.Lion;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.dianping.tuangou.dztg.bjwrapper.api.PrometheusWrapperService;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.concurrent.Future;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DealStyleWrapperPreMtDealDtoListTest {

    @InjectMocks
    private DealStyleWrapper dealStyleWrapper;

    @Mock
    private PrometheusWrapperService prometheusWrapperServiceFuture;

    @Mock
    private DealGroupDzxService dealGroupDzxServiceFuture;

    @Mock
    private Future future;

    @Mock
    private Lion lion;

    private MockedStatic<FutureFactory> futureFactoryMockedStatic;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        futureFactoryMockedStatic = mockStatic(FutureFactory.class);
    }

    @After
    public void tearDown() {
        futureFactoryMockedStatic.close();
    }

    @Test
    public void testPreMtDealDtoListWhenDealIdsIsEmpty() throws Throwable {
        // Since we cannot mock static methods directly, we assume the behavior of the method under test
        // when dealing with empty dealIds. This test might need to be adjusted based on the actual implementation details.
        Future result = dealStyleWrapper.preMtDealDtoList(Arrays.asList());
        assertNull(result);
    }

    /**
     * 测试 mtDealGroupId 小于等于0 的情况
     */
    @Test
    public void testPrepareGetDealGroupDzxInfo_MtDealGroupIdLessThanOrEqualToZero() throws Throwable {
        // arrange
        int mtDealGroupId = 0;
        int platform = 1;
        // act
        Future result = dealStyleWrapper.prepareGetDealGroupDzxInfo(mtDealGroupId, platform);
        // assert
        assertNull(result);
    }

    /**
     * 测试 mtDealGroupId 大于0，getDealGroupDzxInfo 方法执行成功的情况
     */
    @Test
    public void testPrepareGetDealGroupDzxInfo_Normal() throws Throwable {
        // arrange
        int mtDealGroupId = 1;
        int platform = 1;
        DealGroupDzxInfo mockInfo = new DealGroupDzxInfo();
        when(dealGroupDzxServiceFuture.getDealGroupDzxInfo(anyInt(), anyInt())).thenReturn(mockInfo);
        futureFactoryMockedStatic.when(FutureFactory::getFuture).thenReturn(future);
        // act
        Future result = dealStyleWrapper.prepareGetDealGroupDzxInfo(mtDealGroupId, platform);
        // assert
        assertSame(result, future);
    }

    /**
     * 测试 mtDealGroupId 大于0，getDealGroupDzxInfo 方法执行时抛出异常的情况
     */
    @Test
    public void testPrepareGetDealGroupDzxInfo_Exception() throws Throwable {
        // arrange
        int mtDealGroupId = 1;
        int platform = 1;
        when(dealGroupDzxServiceFuture.getDealGroupDzxInfo(anyInt(), anyInt())).thenThrow(new RuntimeException());
        // act
        Future result = dealStyleWrapper.prepareGetDealGroupDzxInfo(mtDealGroupId, platform);
        // assert
        assertNull(result);
    }

    /**
     * Test getDealGroupDzxInfo method when mtDealGroupId is greater than 0 and prepareGetDealGroupDzxInfo method throws an exception.
     */
    @Test
    public void testGetDealGroupDzxInfoMtDealGroupIdGreaterThanZeroAndPrepareGetDealGroupDzxInfoThrowException() throws Throwable {
        int mtDealGroupId = 1;
        int platform = 1;
        when(dealGroupDzxServiceFuture.getDealGroupDzxInfo(anyInt(), anyInt())).thenThrow(new RuntimeException());
        DealGroupDzxInfo result = dealStyleWrapper.getDealGroupDzxInfo(mtDealGroupId, platform);
        assertNull(result);
    }

    @Test
    public void testIsImmersiveHeaderImagePageSourceIsNull() throws Throwable {
        String pageSource = null;
        boolean result = dealStyleWrapper.isImmersiveHeaderImage(pageSource);
        assertFalse(result);
    }
}
