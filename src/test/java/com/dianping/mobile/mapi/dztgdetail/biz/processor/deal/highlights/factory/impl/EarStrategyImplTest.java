package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.factory.impl;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.factory.AbstractMassageStrategy;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

public class EarStrategyImplTest {

    private EarStrategyImpl earStrategy;

    private String getProtectedConstant(String constantName) throws NoSuchFieldException, IllegalAccessException {
        Field field = AbstractMassageStrategy.class.getDeclaredField(constantName);
        field.setAccessible(true);
        return (String) field.get(null);
    }

    @Test(expected = NullPointerException.class)
    public void testGetToolValueWhenServiceProjectAttrsIsNull() throws Throwable {
        EarStrategyImpl earStrategy = new EarStrategyImpl();
        earStrategy.getToolValue(null);
    }

    @Test
    public void testGetToolValueWhenServiceProjectAttrsIsEmpty() throws Throwable {
        EarStrategyImpl earStrategy = new EarStrategyImpl();
        List<ServiceProjectAttrDTO> serviceProjectAttrs = new ArrayList<>();
        String result = earStrategy.getToolValue(serviceProjectAttrs);
        assertNull(result);
    }

    @Test
    public void testGetToolValueWhenNoRelevantAttr() throws Throwable {
        EarStrategyImpl earStrategy = new EarStrategyImpl();
        List<ServiceProjectAttrDTO> serviceProjectAttrs = new ArrayList<>();
        ServiceProjectAttrDTO dto = new ServiceProjectAttrDTO();
        dto.setAttrName("otherAttr");
        dto.setAttrValue("otherValue");
        serviceProjectAttrs.add(dto);
        String result = earStrategy.getToolValue(serviceProjectAttrs);
        assertNull(result);
    }

    @Test
    public void testGetToolValueWhenMixedToolsIsEmpty() throws Throwable {
        EarStrategyImpl earStrategy = new EarStrategyImpl();
        List<ServiceProjectAttrDTO> serviceProjectAttrs = new ArrayList<>();
        ServiceProjectAttrDTO dto = new ServiceProjectAttrDTO();
        dto.setAttrName(getProtectedConstant("DISPOSABLE_MATERIAL"));
        dto.setAttrValue("");
        serviceProjectAttrs.add(dto);
        String result = earStrategy.getToolValue(serviceProjectAttrs);
        assertNull(result);
    }

    @Test
    public void testGetToolValueWhenMixedToolsIsNotEmptyButNoOneTimeTool() throws Throwable {
        EarStrategyImpl earStrategy = new EarStrategyImpl();
        List<ServiceProjectAttrDTO> serviceProjectAttrs = new ArrayList<>();
        ServiceProjectAttrDTO dto = new ServiceProjectAttrDTO();
        dto.setAttrName(getProtectedConstant("DISPOSABLE_MATERIAL"));
        dto.setAttrValue("otherTool");
        serviceProjectAttrs.add(dto);
        String result = earStrategy.getToolValue(serviceProjectAttrs);
        assertEquals("otherTool", result);
    }

    @Test
    public void testGetToolValueWhenMixedToolsIsNotEmptyAndContainsOneTimeTool() throws Throwable {
        EarStrategyImpl earStrategy = new EarStrategyImpl();
        List<ServiceProjectAttrDTO> serviceProjectAttrs = new ArrayList<>();
        ServiceProjectAttrDTO dto = new ServiceProjectAttrDTO();
        dto.setAttrName(getProtectedConstant("DISPOSABLE_MATERIAL"));
        dto.setAttrValue("otherTool、一次性工具");
        serviceProjectAttrs.add(dto);
        String result = earStrategy.getToolValue(serviceProjectAttrs);
        assertEquals("一次性工具", result);
    }

    @Test
    public void testGetToolValueWhenEarpickingToolIsNotNull() throws Throwable {
        EarStrategyImpl earStrategy = new EarStrategyImpl();
        List<ServiceProjectAttrDTO> serviceProjectAttrs = new ArrayList<>();
        ServiceProjectAttrDTO dto = new ServiceProjectAttrDTO();
        dto.setAttrName(getProtectedConstant("EAR_PICKING_TOOL"));
        dto.setAttrValue("特色采耳工具");
        serviceProjectAttrs.add(dto);
        String result = earStrategy.getToolValue(serviceProjectAttrs);
        assertEquals("特色采耳工具", result);
    }

    @Test
    public void testGetToolValueWhenHotpackToolIsNotNull() throws Throwable {
        EarStrategyImpl earStrategy = new EarStrategyImpl();
        List<ServiceProjectAttrDTO> serviceProjectAttrs = new ArrayList<>();
        ServiceProjectAttrDTO dto = new ServiceProjectAttrDTO();
        dto.setAttrName(getProtectedConstant("HOTPACK_TOOL"));
        dto.setAttrValue("热敷工具");
        serviceProjectAttrs.add(dto);
        String result = earStrategy.getToolValue(serviceProjectAttrs);
        assertEquals("热敷工具", result);
    }

    @Test
    public void testGetToolValueWhenMassageToolIsNotNull() throws Throwable {
        EarStrategyImpl earStrategy = new EarStrategyImpl();
        List<ServiceProjectAttrDTO> serviceProjectAttrs = new ArrayList<>();
        ServiceProjectAttrDTO dto = new ServiceProjectAttrDTO();
        dto.setAttrName(getProtectedConstant("MASSAGE_TOOL"));
        dto.setAttrValue("按摩工具");
        serviceProjectAttrs.add(dto);
        String result = earStrategy.getToolValue(serviceProjectAttrs);
        assertEquals("按摩工具", result);
    }
}
