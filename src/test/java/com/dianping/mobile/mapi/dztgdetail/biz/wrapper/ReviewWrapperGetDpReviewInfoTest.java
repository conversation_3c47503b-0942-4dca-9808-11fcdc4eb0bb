package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.entity.ExhibitReviewInfo;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.dianping.review.professional.ReviewDataV2;
import com.dianping.reviewremote.remote.ReviewServiceV2;
import com.dianping.userremote.base.dto.UserDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import org.apache.commons.collections.CollectionUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ReviewWrapperGetDpReviewInfoTest {

    @Mock
    private ReviewServiceV2 reviewServiceFuture;

    @Mock
    private UserWrapper userWrapper;

    @InjectMocks
    private ReviewWrapper reviewWrapper;

    private List<Long> reviewIds;

    private List<ReviewDataV2> reviewDataList;

    private Map<Long, UserDTO> userMap;

    @Before
    public void setUp() {
        // Common test data
        reviewIds = Lists.newArrayList(1L, 2L, 3L);
        ReviewDataV2 review1 = new ReviewDataV2();
        review1.setReviewIdLong(1L);
        review1.setUserId(101L);
        ReviewDataV2 review2 = new ReviewDataV2();
        review2.setReviewIdLong(2L);
        review2.setUserId(102L);
        reviewDataList = Lists.newArrayList(review1, review2);
        UserDTO user1 = new UserDTO();
        user1.setUserNickName("user1");
        UserDTO user2 = new UserDTO();
        user2.setUserNickName("user2");
        userMap = Maps.newHashMap();
        userMap.put(101L, user1);
        userMap.put(102L, user2);
    }

    /**
     * Test when input reviewIdList is empty
     */
    @Test
    public void testGetDpReviewInfoEmptyInput() throws Throwable {
        // arrange
        List<Long> emptyList = Collections.emptyList();
        // act
        Map<Long, ExhibitReviewInfo> result = reviewWrapper.getDpReviewInfo(emptyList);
        // assert
        assertNull(result);
    }

    /**
     * Test when input reviewIdList size exceeds 100
     */
    @Test
    public void testGetDpReviewInfoLargeInput() throws Throwable {
        // arrange
        List<Long> largeList = Lists.newArrayList();
        for (long i = 1; i <= 101; i++) {
            largeList.add(i);
        }
        // act
        Map<Long, ExhibitReviewInfo> result = reviewWrapper.getDpReviewInfo(largeList);
        // assert
        assertNull(result);
    }

    /**
     * Test successful case with single batch
     */
    @Test
    public void testGetDpReviewInfoSingleBatchSuccess() throws Throwable {
        // arrange
        Future<List<ReviewDataV2>> reviewFuture = mock(Future.class);
        when(reviewFuture.get()).thenReturn(reviewDataList);
        FutureFactory.setFuture(reviewFuture);
        Future<Map<Long, UserDTO>> userFuture = mock(Future.class);
        when(userWrapper.getUserInfos(anyList())).thenReturn(userFuture);
        when(userWrapper.getDpUserModelMap(userFuture)).thenReturn(userMap);
        // act
        Map<Long, ExhibitReviewInfo> result = reviewWrapper.getDpReviewInfo(reviewIds);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("user1", result.get(1L).getUserName());
        assertEquals("user2", result.get(2L).getUserName());
        verify(reviewServiceFuture).findReviewsByIdsV3(reviewIds);
        verify(userWrapper).getUserInfos(anyList());
    }

    /**
     * Test when review service returns empty data
     */
    @Test
    public void testGetDpReviewInfoEmptyReviewData() throws Throwable {
        // arrange
        Future<List<ReviewDataV2>> reviewFuture = mock(Future.class);
        when(reviewFuture.get()).thenReturn(Collections.emptyList());
        FutureFactory.setFuture(reviewFuture);
        // act
        Map<Long, ExhibitReviewInfo> result = reviewWrapper.getDpReviewInfo(reviewIds);
        // assert
        assertNull(result);
        verify(reviewServiceFuture).findReviewsByIdsV3(reviewIds);
        verify(userWrapper, never()).getUserInfos(anyList());
    }

    /**
     * Test when review service throws exception
     */
    @Test
    public void testGetDpReviewInfoReviewServiceException() throws Throwable {
        // arrange
        Future<List<ReviewDataV2>> reviewFuture = mock(Future.class);
        when(reviewFuture.get()).thenThrow(new RuntimeException("Service error"));
        FutureFactory.setFuture(reviewFuture);
        // act
        Map<Long, ExhibitReviewInfo> result = reviewWrapper.getDpReviewInfo(reviewIds);
        // assert
        assertNull(result);
        verify(reviewServiceFuture).findReviewsByIdsV3(reviewIds);
        verify(userWrapper, never()).getUserInfos(anyList());
    }

    /**
     * Test when user service returns empty data
     */
    @Test
    public void testGetDpReviewInfoEmptyUserData() throws Throwable {
        // arrange
        Future<List<ReviewDataV2>> reviewFuture = mock(Future.class);
        when(reviewFuture.get()).thenReturn(reviewDataList);
        FutureFactory.setFuture(reviewFuture);
        Future<Map<Long, UserDTO>> userFuture = mock(Future.class);
        when(userWrapper.getUserInfos(anyList())).thenReturn(userFuture);
        when(userWrapper.getDpUserModelMap(userFuture)).thenReturn(Collections.emptyMap());
        // act
        Map<Long, ExhibitReviewInfo> result = reviewWrapper.getDpReviewInfo(reviewIds);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        // empty username when user not found
        assertEquals("", result.get(1L).getUserName());
        assertEquals("", result.get(2L).getUserName());
    }

    /**
     * Test with multiple batches of reviews
     */
    @Test
    public void testGetDpReviewInfoMultipleBatches() throws Throwable {
        // arrange
        List<Long> largeReviewIds = Lists.newArrayList();
        for (long i = 1; i <= 25; i++) {
            largeReviewIds.add(i);
        }
        // First batch
        List<ReviewDataV2> batch1Data = Lists.newArrayList();
        for (long i = 1; i <= 20; i++) {
            ReviewDataV2 review = new ReviewDataV2();
            review.setReviewIdLong(i);
            review.setUserId(i + 100);
            batch1Data.add(review);
        }
        // Second batch
        List<ReviewDataV2> batch2Data = Lists.newArrayList();
        for (long i = 21; i <= 25; i++) {
            ReviewDataV2 review = new ReviewDataV2();
            review.setReviewIdLong(i);
            review.setUserId(i + 100);
            batch2Data.add(review);
        }
        Future<List<ReviewDataV2>> batch1Future = mock(Future.class);
        when(batch1Future.get()).thenReturn(batch1Data);
        Future<List<ReviewDataV2>> batch2Future = mock(Future.class);
        when(batch2Future.get()).thenReturn(batch2Data);
        // Mock review service to return different futures for different batches
        doAnswer(invocation -> {
            List<Long> ids = invocation.getArgument(0);
            if (ids.size() == 20) {
                FutureFactory.setFuture(batch1Future);
            } else {
                FutureFactory.setFuture(batch2Future);
            }
            return null;
        }).when(reviewServiceFuture).findReviewsByIdsV3(anyList());
        // Mock user service
        Map<Long, UserDTO> userMap = Maps.newHashMap();
        for (long i = 101; i <= 125; i++) {
            UserDTO user = new UserDTO();
            user.setUserNickName("user" + i);
            userMap.put(i, user);
        }
        Future<Map<Long, UserDTO>> userFuture = mock(Future.class);
        when(userWrapper.getUserInfos(anyList())).thenReturn(userFuture);
        when(userWrapper.getDpUserModelMap(userFuture)).thenReturn(userMap);
        // act
        Map<Long, ExhibitReviewInfo> result = reviewWrapper.getDpReviewInfo(largeReviewIds);
        // assert
        assertNotNull(result);
        assertEquals(25, result.size());
        for (long i = 1; i <= 25; i++) {
            assertNotNull(result.get(i));
            assertEquals("user" + (i + 100), result.get(i).getUserName());
        }
        verify(reviewServiceFuture, times(2)).findReviewsByIdsV3(anyList());
    }
}
