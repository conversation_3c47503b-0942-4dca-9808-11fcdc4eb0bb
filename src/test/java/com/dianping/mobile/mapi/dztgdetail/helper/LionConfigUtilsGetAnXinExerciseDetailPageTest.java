package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.google.common.collect.Maps;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

@RunWith(PowerMockRunner.class)
@PrepareForTest({Lion.class, LionConfigUtils.class})
public class LionConfigUtilsGetAnXinExerciseDetailPageTest {

    private static final String MT_URL = "https://cube.meituan.com/cube/block/728e318931ce/336447/index.html";
    private static final String DP_URL = "https://cube.dianping.com/cube/block/728e318931ce/336447/index.html";
    private static final String MT_MONTHLY_URL = "https://cube.meituan.com/cube/block/fc46bf64cbba/342847/index.html";
    private static final String DP_MONTHLY_URL = "https://cube.dianping.com/cube/block/fc46bf64cbba/342847/index.html";

    @Before
    public void setUp() {
        mockStatic(Lion.class);
    }

    @Test
    public void testGetAnXinExerciseDetailPage_Mt_Monthly() {
        // 准备模拟数据
        Map<String, String> configMap = Maps.newHashMap();
        configMap.put("mtMonthJumpUrl", MT_MONTHLY_URL);
        
        // 模拟 getCompensationForRunningAwayConfig 方法
        try (MockedStatic<LionConfigUtils> mockedLionConfigUtils = Mockito.mockStatic(LionConfigUtils.class)) {
            mockedLionConfigUtils.when(() -> LionConfigUtils.getCompensationForRunningAwayConfig())
                    .thenReturn(configMap);
            
            // 允许实际方法调用
            mockedLionConfigUtils.when(() -> LionConfigUtils.getAnXinExerciseDetailPage(true, true))
                    .thenCallRealMethod();

            // 执行测试
            String result = LionConfigUtils.getAnXinExerciseDetailPage(true, true);

            // 验证结果
            assertEquals(MT_MONTHLY_URL, result);
        }
    }

    @Test
    public void testGetAnXinExerciseDetailPage_Dp_Monthly() {
        // 准备模拟数据
        Map<String, String> configMap = Maps.newHashMap();
        configMap.put("dpMonthJumpUrl", DP_MONTHLY_URL);
        
        // 模拟 getCompensationForRunningAwayConfig 方法
        try (MockedStatic<LionConfigUtils> mockedLionConfigUtils = Mockito.mockStatic(LionConfigUtils.class)) {
            mockedLionConfigUtils.when(() -> LionConfigUtils.getCompensationForRunningAwayConfig())
                    .thenReturn(configMap);
            
            // 允许实际方法调用
            mockedLionConfigUtils.when(() -> LionConfigUtils.getAnXinExerciseDetailPage(false, true))
                    .thenCallRealMethod();

            // 执行测试
            String result = LionConfigUtils.getAnXinExerciseDetailPage(false, true);

            // 验证结果
            assertEquals(DP_MONTHLY_URL, result);
        }
    }

    @Test
    public void testGetAnXinExerciseDetailPage_Mt_Monthly_DefaultUrl() {
        // 准备模拟数据 - 没有配置mtMonthJumpUrl
        Map<String, String> configMap = Maps.newHashMap();
        
        // 模拟 getCompensationForRunningAwayConfig 方法
        try (MockedStatic<LionConfigUtils> mockedLionConfigUtils = Mockito.mockStatic(LionConfigUtils.class)) {
            mockedLionConfigUtils.when(() -> LionConfigUtils.getCompensationForRunningAwayConfig())
                    .thenReturn(configMap);
            
            // 允许实际方法调用
            mockedLionConfigUtils.when(() -> LionConfigUtils.getAnXinExerciseDetailPage(true, true))
                    .thenCallRealMethod();

            // 执行测试
            String result = LionConfigUtils.getAnXinExerciseDetailPage(true, true);

            // 验证结果 - 应该返回默认URL
            assertEquals("https://cube.meituan.com/cube/block/fc46bf64cbba/342847/index.html", result);
        }
    }

    @Test
    public void testGetAnXinExerciseDetailPage_Dp_Monthly_DefaultUrl() {
        // 准备模拟数据 - 没有配置dpMonthJumpUrl
        Map<String, String> configMap = Maps.newHashMap();
        
        // 模拟 getCompensationForRunningAwayConfig 方法
        try (MockedStatic<LionConfigUtils> mockedLionConfigUtils = Mockito.mockStatic(LionConfigUtils.class)) {
            mockedLionConfigUtils.when(() -> LionConfigUtils.getCompensationForRunningAwayConfig())
                    .thenReturn(configMap);
            
            // 允许实际方法调用
            mockedLionConfigUtils.when(() -> LionConfigUtils.getAnXinExerciseDetailPage(false, true))
                    .thenCallRealMethod();

            // 执行测试
            String result = LionConfigUtils.getAnXinExerciseDetailPage(false, true);

            // 验证结果 - 应该返回默认URL
            assertEquals("https://cube.dianping.com/cube/block/fc46bf64cbba/342847/index.html", result);
        }
    }
}