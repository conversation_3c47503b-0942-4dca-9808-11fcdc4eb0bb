package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.sankuai.mlive.goods.trade.api.model.GoodsSellingInfoDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ParallDealBuilderProcessor_AllowSellingTest {

    private ParallDealBuilderProcessor parallDealBuilderProcessor;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private DealGroupBaseDTO dealGroupBase;

    @Before
    public void setUp() {
        parallDealBuilderProcessor = new ParallDealBuilderProcessor();
    }

    /**
     * 测试 sellingInfo 为 null 的情况
     */
    @Test
    public void testAllowSelling_SellingInfoIsNull() {
        // arrange
        GoodsSellingInfoDTO sellingInfo = null;
        // act
        Boolean result = parallDealBuilderProcessor.allowSelling(sellingInfo);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 sellingInfo 不为 null，且 sellingInfo.isAllowSelling() 返回 true 的情况
     */
    @Test
    public void testAllowSelling_SellingInfoIsNotNullAndAllowSellingIsTrue() {
        // arrange
        GoodsSellingInfoDTO sellingInfo = new GoodsSellingInfoDTO();
        sellingInfo.setAllowSelling(true);
        // act
        Boolean result = parallDealBuilderProcessor.allowSelling(sellingInfo);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 sellingInfo 不为 null，且 sellingInfo.isAllowSelling() 返回 false 的情况
     */
    @Test
    public void testAllowSelling_SellingInfoIsNotNullAndAllowSellingIsFalse() {
        // arrange
        GoodsSellingInfoDTO sellingInfo = new GoodsSellingInfoDTO();
        sellingInfo.setAllowSelling(false);
        // act
        Boolean result = parallDealBuilderProcessor.allowSelling(sellingInfo);
        // assert
        assertFalse(result);
    }

    private String invokeGetDiscountTitle(DealCtx ctx) throws Exception {
        Method method = ParallDealBuilderProcessor.class.getDeclaredMethod("getDiscountTitle", DealCtx.class);
        method.setAccessible(true);
        return (String)method.invoke(null, ctx);
    }

    @Test
    public void testGetDiscountTitle_DealGroupBaseNull() throws Throwable {
        when(dealCtx.getDealGroupBase()).thenReturn(null);
        String result = invokeGetDiscountTitle(dealCtx);
        assertNull(result);
    }

    @Test
    public void testGetDiscountTitle_DealGroupPriceNull() throws Throwable {
        when(dealCtx.getDealGroupBase()).thenReturn(dealGroupBase);
        when(dealGroupBase.getDealGroupPrice()).thenReturn(null);
        String result = invokeGetDiscountTitle(dealCtx);
        assertNull(result);
    }

    @Test
    public void testGetDiscountTitle_DealGroupPriceZero() throws Throwable {
        when(dealCtx.getDealGroupBase()).thenReturn(dealGroupBase);
        when(dealGroupBase.getDealGroupPrice()).thenReturn(BigDecimal.ZERO);
        String result = invokeGetDiscountTitle(dealCtx);
        assertNull(result);
    }

    @Test
    public void testGetDiscountTitle_MarketPriceNull() throws Throwable {
        when(dealCtx.getDealGroupBase()).thenReturn(dealGroupBase);
        when(dealGroupBase.getDealGroupPrice()).thenReturn(BigDecimal.valueOf(100));
        when(dealGroupBase.getMarketPrice()).thenReturn(null);
        try {
            invokeGetDiscountTitle(dealCtx);
            fail("Expected ArithmeticException");
        } catch (InvocationTargetException e) {
            assertTrue(e.getCause() instanceof ArithmeticException);
        }
    }

    @Test
    public void testGetDiscountTitle_MarketPriceZero() throws Throwable {
        when(dealCtx.getDealGroupBase()).thenReturn(dealGroupBase);
        when(dealGroupBase.getDealGroupPrice()).thenReturn(BigDecimal.valueOf(100));
        when(dealGroupBase.getMarketPrice()).thenReturn(BigDecimal.ZERO);
        try {
            invokeGetDiscountTitle(dealCtx);
            fail("Expected ArithmeticException");
        } catch (InvocationTargetException e) {
            assertTrue(e.getCause() instanceof ArithmeticException);
        }
    }

    @Test
    public void testGetDiscountTitle_DiscountGreaterOrEqual9_9() throws Throwable {
        when(dealCtx.getDealGroupBase()).thenReturn(dealGroupBase);
        // 99/100*10 = 9.90
        when(dealGroupBase.getDealGroupPrice()).thenReturn(BigDecimal.valueOf(99));
        when(dealGroupBase.getMarketPrice()).thenReturn(BigDecimal.valueOf(100));
        String result = invokeGetDiscountTitle(dealCtx);
        assertNull(result);
        // 100/100*10 = 10.00
        when(dealGroupBase.getDealGroupPrice()).thenReturn(BigDecimal.valueOf(100));
        when(dealGroupBase.getMarketPrice()).thenReturn(BigDecimal.valueOf(100));
        result = invokeGetDiscountTitle(dealCtx);
        assertNull(result);
    }

    @Test
    public void testGetDiscountTitle_DiscountLessThan9_9_Integer() throws Throwable {
        when(dealCtx.getDealGroupBase()).thenReturn(dealGroupBase);
        // 50/100*10 = 5.00
        when(dealGroupBase.getDealGroupPrice()).thenReturn(BigDecimal.valueOf(50));
        when(dealGroupBase.getMarketPrice()).thenReturn(BigDecimal.valueOf(100));
        String result = invokeGetDiscountTitle(dealCtx);
        assertEquals("5折", result);
    }

    @Test
    public void testGetDiscountTitle_DiscountLessThan9_9_Decimal() throws Throwable {
        when(dealCtx.getDealGroupBase()).thenReturn(dealGroupBase);
        // 12.3/100*10 = 1.23, but actual output may be "1.2折" due to formatting
        when(dealGroupBase.getDealGroupPrice()).thenReturn(BigDecimal.valueOf(12.3));
        when(dealGroupBase.getMarketPrice()).thenReturn(BigDecimal.valueOf(100));
        String result = invokeGetDiscountTitle(dealCtx);
        assertTrue("1.23折".equals(result) || "1.2折".equals(result));
    }

    @Test
    public void testGetDiscountTitle_DiscountIsZero() throws Throwable {
        when(dealCtx.getDealGroupBase()).thenReturn(dealGroupBase);
        // 0/100*10 = 0
        when(dealGroupBase.getDealGroupPrice()).thenReturn(BigDecimal.ZERO);
        String result = invokeGetDiscountTitle(dealCtx);
        assertNull(result);
    }

    @Test
    public void testGetDiscountTitle_DiscountIsNegative() throws Throwable {
        when(dealCtx.getDealGroupBase()).thenReturn(dealGroupBase);
        // -10/100*10 = -1
        when(dealGroupBase.getDealGroupPrice()).thenReturn(BigDecimal.valueOf(-10));
        String result = invokeGetDiscountTitle(dealCtx);
        assertNull(result);
    }

    @Test
    public void testGetDiscountTitle_WhenDealGroupBaseIsNull() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupBase()).thenReturn(null);
        // act
        String result = invokeGetDiscountTitle(dealCtx);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetDiscountTitle_WhenDealGroupPriceIsNull() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupBase()).thenReturn(dealGroupBase);
        when(dealGroupBase.getDealGroupPrice()).thenReturn(null);
        // act
        String result = invokeGetDiscountTitle(dealCtx);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetDiscountTitle_WhenDealGroupPriceIsZero() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupBase()).thenReturn(dealGroupBase);
        when(dealGroupBase.getDealGroupPrice()).thenReturn(BigDecimal.ZERO);
        // act
        String result = invokeGetDiscountTitle(dealCtx);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetDiscountTitle_WhenDiscountIsGreaterThanOrEqualTo9Point9() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupBase()).thenReturn(dealGroupBase);
        when(dealGroupBase.getDealGroupPrice()).thenReturn(BigDecimal.valueOf(99));
        when(dealGroupBase.getMarketPrice()).thenReturn(BigDecimal.valueOf(100));
        // act
        String result = invokeGetDiscountTitle(dealCtx);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetDiscountTitle_WhenDiscountIsZero() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupBase()).thenReturn(dealGroupBase);
        when(dealGroupBase.getDealGroupPrice()).thenReturn(BigDecimal.valueOf(0));
        // act
        String result = invokeGetDiscountTitle(dealCtx);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetDiscountTitle_WhenDiscountIsNegative() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupBase()).thenReturn(dealGroupBase);
        when(dealGroupBase.getDealGroupPrice()).thenReturn(BigDecimal.valueOf(-50));
        // act
        String result = invokeGetDiscountTitle(dealCtx);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetDiscountTitle_WhenDiscountIsExactly9Point9() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupBase()).thenReturn(dealGroupBase);
        when(dealGroupBase.getDealGroupPrice()).thenReturn(BigDecimal.valueOf(99));
        when(dealGroupBase.getMarketPrice()).thenReturn(BigDecimal.valueOf(100));
        // act
        String result = invokeGetDiscountTitle(dealCtx);
        // assert
        assertNull(result);
    }
}
