package com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler;

import com.dianping.deal.bff.cache.enums.RcfDealBffInterfaceEnum;
import com.dianping.mobile.mapi.dztgdetail.rcf.api.req.DealNativeSnapshotReq;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.dto.DealBffResponseDTO;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.service.DealDetailFlattenService;
import com.dianping.mobile.mapi.dztgdetail.util.ApplicationContextGetBeanHelper;
import org.apache.commons.collections.MapUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.context.ApplicationContext;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2025/1/2 11:00
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({ApplicationContextGetBeanHelper.class, ApplicationContext.class})
public class DealModuleFlattenHandlerTest {

    @InjectMocks
    private DealModuleFlattenHandler dealModuleFlattenHandler;

    @Mock
    private DealDetailFlattenService dealDetailFlattenService;

    @Test
    public void customerProcessTest(){
        DealNativeSnapshotReq request = new DealNativeSnapshotReq();
        Map<RcfDealBffInterfaceEnum, Object> bffResponseMap = new HashMap<>();
        DealBffResponseDTO bffResponse = new DealBffResponseDTO(bffResponseMap);
        dealModuleFlattenHandler.customerProcess(request, bffResponse);
        Assert.assertTrue(MapUtils.isEmpty(bffResponse.getBffResponse()));
    }

    @Test
    public void canProcessTest(){
        DealNativeSnapshotReq request = new DealNativeSnapshotReq();
        Map<RcfDealBffInterfaceEnum, Object> bffResponseMap = new HashMap<>();
        DealBffResponseDTO bffResponse = new DealBffResponseDTO(bffResponseMap);
        boolean result = dealModuleFlattenHandler.canProcess(request, bffResponse);
        Assert.assertFalse(result);
    }
}
