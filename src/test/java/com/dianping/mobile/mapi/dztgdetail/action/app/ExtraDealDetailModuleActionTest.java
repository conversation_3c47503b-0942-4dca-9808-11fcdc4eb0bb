package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.action.AbsActionTest;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.ExtraDealDetailModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.ExtraDealDetailModuleResponse;
import com.dianping.mobile.mapi.dztgdetail.facade.ExtraDealDetailModuleFacade;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import javax.swing.*;

import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class ExtraDealDetailModuleActionTest {
    @InjectMocks
    private ExtraDealDetailModuleAction action;

    @Mock
    private ExtraDealDetailModuleFacade extraDealDetailModuleFacade;

    @Mock
    private IMobileContext context;

    private ExtraDealDetailModuleReq request;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        request = new ExtraDealDetailModuleReq();
        request.setDealGroupId("123");
    }

    /**
     * 测试正常场景
     */
    @Test
    public void testExecuteSuccess() throws Throwable {
        // arrange
        ExtraDealDetailModuleResponse expectedResponse = new ExtraDealDetailModuleResponse();
        when(extraDealDetailModuleFacade.getExtraDealDetailModuleResponse(any(ExtraDealDetailModuleReq.class), any())).thenReturn(expectedResponse);

        // act
        CommonMobileResponse response = (CommonMobileResponse) action.execute(request, null);

        // assert
        assertNotNull(response);
        assertEquals(expectedResponse, response.getData());
    }

    /**
     * 测试异常场景
     */
    @Test
    public void testExecuteException() throws Throwable {
        // arrange
        when(extraDealDetailModuleFacade.getExtraDealDetailModuleResponse(any(ExtraDealDetailModuleReq.class), any())).thenThrow(new RuntimeException("Test Exception"));

        // act
        CommonMobileResponse response = (CommonMobileResponse) action.execute(request, null);

        // assert
        assertNotNull(response);
        assertEquals(Resps.SYSTEM_ERROR, response);
    }
}
