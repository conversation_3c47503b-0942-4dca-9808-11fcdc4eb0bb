package com.dianping.mobile.mapi.dztgdetail.button;

import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.mobile.mapi.dztgdetail.button.pintuan.CostEffectivePinTuanBuilder;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.PriceContext;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.entity.CostEffectivePinTuan;
import com.dianping.mobile.mapi.dztgdetail.util.JsonUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * @Author: <EMAIL>
 * @Date: 2024/5/7
 */
@RunWith(MockitoJUnitRunner.class)
public class CostEffectivePinTuanBuilderTest {
    @InjectMocks
    private CostEffectivePinTuanBuilder costEffectivePinTuanBuilder;

    @Test
    public void testBuildWeakActivePinTuan() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setDealBaseReq(new DealBaseReq());

        PriceContext priceContext = JsonUtils.fromJson("{\"newUser\":true,\"memberDay\":false,\"hasExclusiveDeduction\":false,\"idlePromoPrice\":{\"identity\":{\"spuId\":0,\"productId\":*********,\"productType\":1,\"skuId\":450622291,\"categoryId\":0,\"productIdL\":0,\"skuIdL\":0,\"priceTime\":0,\"priceTrendType\":0,\"lskuId\":450622291,\"lproductId\":*********},\"price\":99.00,\"maxPrice\":99.00,\"basePrice\":99.00,\"marketPrice\":111.00,\"showMarketPrice\":true,\"promoAmount\":0,\"usedPromos\":[],\"morePromos\":[],\"promoTagType\":0,\"pricePowerTagDisplayDTO\":{\"allTagList\":[],\"filteredTagList\":[]}},\"normalPrice\":{\"identity\":{\"spuId\":0,\"productId\":*********,\"productType\":1,\"skuId\":450622291,\"categoryId\":0,\"productIdL\":0,\"skuIdL\":0,\"priceTime\":0,\"priceTrendType\":0,\"lskuId\":450622291,\"lproductId\":*********},\"price\":99.00,\"maxPrice\":99.00,\"basePrice\":99.00,\"marketPrice\":111.00,\"showMarketPrice\":true,\"promoAmount\":0,\"usedPromos\":[],\"morePromos\":[],\"pricePromoInfoMap\":{},\"promoTagType\":0,\"activityDTO\":{\"discountProvider\":0,\"discountClassifyType\":0},\"pricePowerTagDisplayDTO\":{\"allTagList\":[],\"filteredTagList\":[]},\"extendDisplayInfo\":{}},\"costEffectivePrice\":{\"identity\":{\"spuId\":0,\"productId\":*********,\"productType\":1,\"skuId\":450622291,\"categoryId\":0,\"productIdL\":0,\"skuIdL\":0,\"priceTime\":0,\"priceTrendType\":0,\"lskuId\":450622291,\"lproductId\":*********},\"price\":9.00,\"maxPrice\":9.00,\"basePrice\":99.00,\"marketPrice\":111.00,\"showMarketPrice\":false,\"promoAmount\":102.00,\"usedPromos\":[{\"identity\":{\"promoId\":*********,\"promoType\":11,\"promoTypeDesc\":\"团购优惠12元\",\"sourceType\":1,\"promoShowType\":\"DEAL_PROMO\"},\"amount\":12.00,\"tag\":\"团购优惠12元\",\"description\":\"团购优惠12元\",\"priceThrough\":true,\"canAssign\":false,\"extendDesc\":\"团购优惠，下单立省12元\",\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoStatus\":0,\"couponValueType\":0,\"promoTextDTO\":{\"title\":\"团购优惠12元\",\"subTitle\":\"团购优惠，下单立省12元\",\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoDivideType\":\"DEAL_PROMO\"},\"newUser\":false},{\"identity\":{\"promoId\":1900092820,\"promoType\":1,\"promoTypeDesc\":\"美团补贴90元\",\"sourceType\":1,\"promoShowType\":\"MT_SUBSIDY\"},\"amount\":9E+1,\"tag\":\"减90元\",\"priceThrough\":true,\"canAssign\":false,\"extendDesc\":\"美团补贴，下单立省90元，每人总共可享受999次\",\"icon\":\"https://p0.meituan.net/travelcube/4382b3cf4c10bf7ff8d9ef7438c554c64793.png\",\"totalStock\":0,\"remainStock\":0,\"promoIdentity\":\"pinTuan,juhuasuan,exclusiveDeduction\",\"promoStatus\":0,\"couponValueType\":0,\"promoStock\":{\"totalStock\":0,\"remainStock\":0},\"newActivityGroupId\":\"288230376151832799\",\"promoTextDTO\":{\"title\":\"美团补贴90元\",\"subTitle\":\"美团补贴，下单立省90元，每人总共可享受999次\",\"icon\":\"https://p0.meituan.net/travelcube/4382b3cf4c10bf7ff8d9ef7438c554c64793.png\",\"promoDivideType\":\"MT_SUBSIDY\"},\"reductionSaleChannels\":[\"costEffective\"],\"amountShareDetail\":{\"1\":9E+1,\"2\":0},\"promotionExplanatoryTags\":[5],\"promotionOtherInfoMap\":{\"SHARE_HELP_BASE_INFO\":\"{\\\"activityId\\\":29855,\\\"shareAwardPrizeId\\\":0,\\\"shareExpireType\\\":3,\\\"shareJoinCount\\\":0,\\\"shareJoinDayCount\\\":0,\\\"shareLogic\\\":\\\"\\\",\\\"helpSuccCountMin\\\":2,\\\"helpSuccCountMax\\\":99999999,\\\"helpDayCount\\\":0,\\\"helpPersonCount\\\":null,\\\"helpAwardPrizeId\\\":0,\\\"helpLogic\\\":\\\"\\\",\\\"addTime\\\":1715331784000,\\\"shareExpireTimeDetail\\\":\\\"{\\\\\\\"expireTimeDynamic\\\\\\\":*********}\\\",\\\"shareEffectTime\\\":0,\\\"shareHelpType\\\":1,\\\"shareStartTimeDetail\\\":null,\\\"shareStartType\\\":1,\\\"groupSuccCountMin\\\":3}\",\"GROUP_STATUS\":\"2\",\"MATERIAL_INFO_LIST\":\"[{\\\"activityId\\\":29855,\\\"materialId\\\":10228828,\\\"fieldType\\\":\\\"TEXT\\\",\\\"fieldKey\\\":\\\"shareHelpRuleDetail\\\",\\\"fieldName\\\":\\\"活动玩法\\\",\\\"fieldDesc\\\":\\\"0\\\",\\\"fieldValue\\\":\\\"1、售卖渠道\\\\n美团APP，美团小程序。\\\\n\\\\n2、成团规则\\\\n开团有效时间内，成功邀请好友参与拼团（完成支付）即拼团成功。开团有效时间结束后，如果拼团人数不足，则拼团失败，相应订单自动取消，订单款项3日内全部原路退回。\\\\n\\\\n3、参与限制\\\\n1）发起拼团：活动城市全部用户；\\\\n2）参与拼团：仅限美食团购新用户（即365天内没有在美食团购下过订单的用户）。\\\\n\\\\n4、 温馨提示\\\\n1）拼团不局限于某一个商品，发起人和参与人下单可拼商品中的任意一个，均可参团；\\\\n2）如遇拼团过程中无法手动操作退款，拼团失败后自动退款，成功后也可手动退款；\\\\n3）可拼商品库存有限，抢完为止（商品拼团价格以活动页面每日实际数据为准）。\\\",\\\"actionType\\\":null,\\\"actionId\\\":\\\"\\\"},{\\\"activityId\\\":29855,\\\"materialId\\\":10228829,\\\"fieldType\\\":\\\"TEXT\\\",\\\"fieldKey\\\":\\\"shareHelpRuleDetail\\\",\\\"fieldName\\\":\\\"活动规则\\\",\\\"fieldDesc\\\":\\\"1\\\",\\\"fieldValue\\\":\\\"1）同一账号、手机号、身份证号、支付账号、移动设备等均视为同一活动用户。\\\\n2）活动优惠仅限本账户使用，不得转赠、兑现。\\\\n3）参与活动用户不得进行虚假交易，不得通过各种方式参与或协助套现美团优惠资源，不得以任何形式的软件或其他恶意方式参与本活动，不得实施违反诚实信用的行为及实施其他非真实活动的作弊行为。如美团平台基于合理理由认定用户存在上述违规行为的，有权取消用户参与本活动的资格，撤销违规交易并收回已发放权益。\\\\n4）若活动中遭遇大面积作弊、通讯路线故障或计算机大规模瘫痪等原因导致难以继续开展本活动，美团平台保留取消、修改或暂停本活动的权利。\\\\n5）客服热线电话：10107888。\\\\n\\\",\\\"actionType\\\":null,\\\"actionId\\\":\\\"\\\"}]\",\"CONFIG_CHANNELS\":\"[costEffective]\",\"SHARE_HELP_INFO_LIST\":\"[{\\\"shareRecordUserInfos\\\":[{\\\"activityId\\\":29855,\\\"userId\\\":5035686652,\\\"userSource\\\":1,\\\"shareActionId\\\":442193,\\\"shareHelpInfoId\\\":3222,\\\"shareRecordId\\\":354428,\\\"expireTime\\\":1715927743000,\\\"shareEffectTime\\\":1715927743000,\\\"addTime\\\":1715571355000,\\\"userDisplayContent\\\":{\\\"nickName\\\":\\\"GRU445529177\\\",\\\"avatar\\\":\\\"https://p0.meituan.net/travelcube/53ed7c702791795af246258f873ebe295918.png\\\",\\\"currentUser\\\":true},\\\"prizeItemType\\\":null,\\\"prizeItemInfo\\\":null,\\\"status\\\":1}],\\\"helpRecordUserInfos\\\":[{\\\"shareUserId\\\":5035686652,\\\"shareActionId\\\":442193,\\\"activityId\\\":29855,\\\"userId\\\":5091446657,\\\"userSource\\\":1,\\\"helpActionId\\\":451592,\\\"shareHelpInfoId\\\":3222,\\\"helpRecordId\\\":364602,\\\"addTime\\\":1715741479000,\\\"userDisplayContent\\\":{\\\"nickName\\\":\\\"QKU998986877\\\",\\\"avatar\\\":\\\"https://p0.meituan.net/travelcube/53ed7c702791795af246258f873ebe295918.png\\\",\\\"currentUser\\\":false},\\\"prizeItemType\\\":null,\\\"prizeItemInfo\\\":null}],\\\"helpSucc\\\":false,\\\"helpSuccTime\\\":null,\\\"helpPrizeInfoId\\\":0,\\\"shareHelpType4User\\\":1,\\\"shareToken\\\":\\\"4697254419937404941\\\",\\\"helpSuccCountMin\\\":2,\\\"helpSuccCountMax\\\":99999999,\\\"hasHelpCount\\\":1,\\\"latestShareToken\\\":true,\\\"continueShare\\\":null,\\\"groupSuccCountMin\\\":3}]\",\"LIMIT_NEW_CUSTOMER_JOIN\":\"false\",\"PINTUAN_ACTIVITY_ID\":\"29855\"},\"promotionDisplayTextMap\":{},\"newUser\":false}],\"morePromos\":[],\"pricePromoInfoMap\":{},\"extPrices\":[{\"extPriceType\":1,\"extPricePromoAmount\":0,\"extPrice\":99.00,\"extPriceTitle\":\"全网低价\"}],\"promoTag\":\"特惠促销共省¥102\",\"shortPromoTag\":\"共省¥102\",\"promoDiscount\":0.09,\"promoTagType\":0,\"activityDTO\":{\"discountProvider\":2,\"discountClassifyType\":0},\"pricePowerTagDisplayDTO\":{\"allTagList\":[],\"filteredTagList\":[]},\"extendDisplayInfo\":{}},\"dealPromoPrice\":{\"identity\":{\"spuId\":0,\"productId\":*********,\"productType\":1,\"skuId\":450622291,\"categoryId\":0,\"productIdL\":0,\"skuIdL\":0,\"priceTime\":0,\"priceTrendType\":0,\"lskuId\":450622291,\"lproductId\":*********},\"price\":99.00,\"maxPrice\":99.00,\"basePrice\":99.00,\"marketPrice\":111.00,\"showMarketPrice\":false,\"promoAmount\":12.00,\"usedPromos\":[{\"identity\":{\"promoId\":*********,\"promoType\":11,\"promoTypeDesc\":\"团购优惠12元\",\"sourceType\":1,\"promoShowType\":\"DEAL_PROMO\"},\"amount\":12.00,\"tag\":\"团购优惠12元\",\"description\":\"团购优惠12元\",\"priceThrough\":true,\"canAssign\":false,\"extendDesc\":\"团购优惠，下单立省12元\",\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoStatus\":0,\"couponValueType\":0,\"promoTextDTO\":{\"title\":\"团购优惠12元\",\"subTitle\":\"团购优惠，下单立省12元\",\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoDivideType\":\"DEAL_PROMO\"},\"newUser\":false}],\"morePromos\":[],\"pricePromoInfoMap\":{},\"promoTag\":\"特惠促销共省¥12\",\"shortPromoTag\":\"共省¥12\",\"promoDiscount\":0.90,\"promoTagType\":0,\"activityDTO\":{\"discountProvider\":0,\"discountClassifyType\":0},\"pricePowerTagDisplayDTO\":{\"allTagList\":[],\"filteredTagList\":[]},\"extendDisplayInfo\":{}},\"normalPriceCipher\":\"vH4THqgHbPMHcB_0K6VVVgSXECnPRnJE8xuY2dLmjsNUyMi99dKv9h1AB0xAIQ6FQ6jAbr-QUzThOQ3hqf35KuZAhF6dGmBhkYqMDWYn9AVBZxWhM6sJ05n3DdZRD0KKBAFX_EHql7jICtW-eKGZbZ9J0VDY6-ikY4W7nduiQfDHf96nCA9FTuMHc5vKBsicc1U27RcbNwuw9IerIslbk4TUJVAujcFjFd2BfTZN9Nedy9iM58On_Y_HJo6Ne26Bft7HZAQt3Ml0w3btCf9y2kdl4zQGtEWXfRSnUlSuRb84OMEWJ4oqbm26m3gANHuYmUzuO85x3NNOQfPcz0ry1VS1jPjGeB5qWzGUkaxGgXnszua1MkwIZZxoH-EjH54-\",\"dealPromoPriceCipher\":\"vH4THqgHbPMHcB_0K6VVVgSXECnPRnJE8xuY2dLmjsNUyMi99dKv9h1AB0xAIQ6FQ6jAbr-QUzThOQ3hqf35KuZAhF6dGmBhkYqMDWYn9AV6FrX2ndK8HQ7FAMNogEjgBAFX_EHql7jICtW-eKGZbZ9J0VDY6-ikY4W7nduiQfDHf96nCA9FTuMHc5vKBsicc1U27RcbNwuw9IerIslbk4TUJVAujcFjFd2BfTZN9Nedy9iM58On_Y_HJo6Ne26BQML7xhAi9aIo45JbVo2ECFH_ioGBW2dZEJfzsAcERjNDC3CxamFqKafZou8N5B2mtqkwEk1K0tKVyij1qxKqRQP3xXjI7yku0JAPVqCfwaA_gR1OVLCr7gJ_pRxjpaHv3o6mpPQzCyJ06ZHbey95Mg\",\"memberNormalPrice\":false,\"memberPromoDealPrice\":false,\"displayInflateCoupon\":false,\"zuLiaoButtonNewStyle\":false}", PriceContext.class);
        DealGroupBaseDTO dealGroupBaseDTO = JsonUtils.fromJson("{\"dealGroupId\":*********,\"dealGroupShortTitle\":\"宝岛眼镜\",\"dealGroupTitleDesc\":\"仅售99元，价值111元圈圈测试单0927_货架！\",\"maxJoin\":0,\"currentJoin\":0,\"defaultPic\":\"http://p0.meituan.net/dpmerchantpic/34da0b33d9f878a818b28dc1fadeb096286116.png\",\"dealGroupPrice\":99.00,\"marketPrice\":111.00,\"maxPerUser\":0,\"minPerUser\":1,\"status\":1,\"autoRefundSwitch\":1,\"dealGroupPics\":\"http://p0.meituan.net/dpmerchantpic/34da0b33d9f878a818b28dc1fadeb096286116.png\",\"saleChannel\":0,\"salePlatform\":3,\"publishStatus\":1,\"dealGroupType\":1,\"overdueAutoRefund\":true,\"canUseCoupon\":true,\"thirdPartVerify\":false,\"payChannelIDAllowed\":0,\"discountRuleID\":0,\"blockStock\":false,\"publishVersion\":0,\"lottery\":false,\"productTitle\":\"圈圈测试单0927_货架\",\"featureTitle\":\"\",\"deals\":[{\"dealId\":450622291,\"dealGroupId\":*********,\"shortTitle\":\"圈圈测试单0927_货架\",\"price\":99.00,\"marketPrice\":111.00,\"maxJoin\":0,\"currentJoin\":0,\"receiptType\":1,\"deliverType\":0,\"dealStatus\":1,\"receiptDateType\":1,\"receiptValidDays\":90,\"provideInvoice\":false,\"thirdPartyId\":0,\"dealGroupPublishVersion\":0}],\"sourceId\":102}", DealGroupBaseDTO.class);
        CostEffectivePinTuan costEffectivePinTuan = JsonUtils.fromJson("{\"sceneType\":5,\"pinTuanActivityId\":\"29855\",\"shareToken\":\"4697254419937404941\",\"cePinTuanScene\":true,\"pinTuanOpened\":true,\"activePinTuan\":true,\"inPinTuan\":true,\"groupSuccCountMin\":3,\"hasHelpCount\":1,\"avatars\":[\"https://p0.meituan.net/travelcube/53ed7c702791795af246258f873ebe295918.png\",\"https://p0.meituan.net/travelcube/53ed7c702791795af246258f873ebe295918.png\",\"https://p0.meituan.net/ingee/a4eb8f82456c3c51ea66ffffb73f9ae14106.png\"],\"expireTime\":1715927743000,\"ruleInfoPOS\":[{\"title\":\"活动玩法\",\"content\":\"1、售卖渠道\\n美团APP，美团小程序。\\n\\n2、成团规则\\n开团有效时间内，成功邀请好友参与拼团（完成支付）即拼团成功。开团有效时间结束后，如果拼团人数不足，则拼团失败，相应订单自动取消，订单款项3日内全部原路退回。\\n\\n3、参与限制\\n1）发起拼团：活动城市全部用户；\\n2）参与拼团：仅限美食团购新用户（即365天内没有在美食团购下过订单的用户）。\\n\\n4、 温馨提示\\n1）拼团不局限于某一个商品，发起人和参与人下单可拼商品中的任意一个，均可参团；\\n2）如遇拼团过程中无法手动操作退款，拼团失败后自动退款，成功后也可手动退款；\\n3）可拼商品库存有限，抢完为止（商品拼团价格以活动页面每日实际数据为准）。\"},{\"title\":\"活动规则\",\"content\":\"1）同一账号、手机号、身份证号、支付账号、移动设备等均视为同一活动用户。\\n2）活动优惠仅限本账户使用，不得转赠、兑现。\\n3）参与活动用户不得进行虚假交易，不得通过各种方式参与或协助套现美团优惠资源，不得以任何形式的软件或其他恶意方式参与本活动，不得实施违反诚实信用的行为及实施其他非真实活动的作弊行为。如美团平台基于合理理由认定用户存在上述违规行为的，有权取消用户参与本活动的资格，撤销违规交易并收回已发放权益。\\n4）若活动中遭遇大面积作弊、通讯路线故障或计算机大规模瘫痪等原因导致难以继续开展本活动，美团平台保留取消、修改或暂停本活动的权利。\\n5）客服热线电话：10107888。\\n\"}],\"limitNewCustomJoin\":false,\"promotionId\":1900092820,\"pinTuanPassParamConfig\":{\"title\":\"我在拼团中，快来加入我\",\"defaultAvatar\":\"https://p0.meituan.net/ingee/a4eb8f82456c3c51ea66ffffb73f9ae14106.png\",\"templateId\":48},\"helpSuccCountMin\":2}", CostEffectivePinTuan.class);
        ctx.setCostEffectivePinTuan(costEffectivePinTuan);
        ctx.setPriceContext(priceContext);
        ctx.setDealGroupBase(dealGroupBaseDTO);
        ctx.getEnvCtx().setClientType(200502);
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        ctx.setMarketPriceHided(false);
        ctx.setMtId(*********);
        ctx.setMtLongShopId(40153772l);
        ctx.setRequestSource("cost_effective");

        costEffectivePinTuanBuilder.init(new BuilderConfig());
        costEffectivePinTuanBuilder.buildWeakActivePinTuan(ctx);
        Assert.assertTrue("直接购买".equals(ctx.getBuyBar().getBuyBtns().get(0).getBtnTitle()));
        Assert.assertTrue("拼团中".equals(ctx.getBuyBar().getBuyBtns().get(1).getBtnTitle()));
    }
}
