package com.dianping.mobile.mapi.dztgdetail.biz;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Cons;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DealShowTypeEnum;
import com.meituan.service.mobile.prometheus.model.DealModel;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealFieldTransferBizGenShowTypeTest {

    @Mock
    private DealModel dealModel;

    private DealFieldTransferBiz dealFieldTransferBiz = new DealFieldTransferBiz();

    private String invokePrivateMethod(String methodName) throws Exception {
        Method method = DealFieldTransferBiz.class.getDeclaredMethod(methodName, DealModel.class);
        method.setAccessible(true);
        return (String) method.invoke(dealFieldTransferBiz, dealModel);
    }

    private boolean invokePrivateMethod(String methodName, DealModel dealModel) throws Exception {
        Method method = DealFieldTransferBiz.class.getDeclaredMethod(methodName, DealModel.class);
        method.setAccessible(true);
        return (boolean) method.invoke(dealFieldTransferBiz, dealModel);
    }

    @Test
    public void testGenShowTypeWhenPropertiesIsTrue() throws Throwable {
        // arrange
        when(dealModel.getProperties()).thenReturn(1L);
        // act
        String result = invokePrivateMethod("genShowType");
        // assert
        assertEquals(DealShowTypeEnum.WEDDING.getName(), result);
    }

    @Test
    public void testGenShowTypeWhenPropertiesIsFalseAndCatesContains20() throws Throwable {
        // arrange
        when(dealModel.getProperties()).thenReturn(0L);
        when(dealModel.getCates()).thenReturn(Arrays.asList(20));
        // act
        String result = invokePrivateMethod("genShowType");
        // assert
        assertEquals(DealShowTypeEnum.HOTEL.getName(), result);
    }

    @Test
    public void testGenShowTypeWhenPropertiesIsFalseAndCatesNotContains20() throws Throwable {
        // arrange
        when(dealModel.getProperties()).thenReturn(0L);
        when(dealModel.getCates()).thenReturn(Arrays.asList(19));
        // act
        String result = invokePrivateMethod("genShowType");
        // assert
        assertEquals(DealShowTypeEnum.NORMAL.getName(), result);
    }

    @Test
    public void testIsAppointOnlineWhenAttrsIsNull() throws Throwable {
        DealModel dealModel = mock(DealModel.class);
        when(dealModel.getAttrs()).thenReturn(null);
        assertFalse(invokePrivateMethod("isAppointOnline", dealModel));
    }

    @Test
    public void testIsAppointOnlineWhenAttrsHasNoOnlineOrderValue() throws Throwable {
        DealModel dealModel = mock(DealModel.class);
        Map<Integer, String> attrs = new HashMap<>();
        when(dealModel.getAttrs()).thenReturn(attrs);
        assertFalse(invokePrivateMethod("isAppointOnline", dealModel));
    }

    @Test
    public void testIsAppointOnlineWhenOnlineOrderValueIsNull() throws Throwable {
        DealModel dealModel = mock(DealModel.class);
        Map<Integer, String> attrs = new HashMap<>();
        attrs.put(Cons.ATTRS_ONLINE_ORDER, null);
        when(dealModel.getAttrs()).thenReturn(attrs);
        assertFalse(invokePrivateMethod("isAppointOnline", dealModel));
    }

    @Test
    public void testIsAppointOnlineWhenOnlineOrderValueIsN() throws Throwable {
        DealModel dealModel = mock(DealModel.class);
        Map<Integer, String> attrs = new HashMap<>();
        attrs.put(Cons.ATTRS_ONLINE_ORDER, "N");
        when(dealModel.getAttrs()).thenReturn(attrs);
        assertFalse(invokePrivateMethod("isAppointOnline", dealModel));
    }

    @Test
    public void testIsAppointOnlineWhenOnlineOrderValueIsNotN() throws Throwable {
        DealModel dealModel = mock(DealModel.class);
        Map<Integer, String> attrs = new HashMap<>();
        // Assuming 'Y' is a valid value for demonstration
        attrs.put(Cons.ATTRS_ONLINE_ORDER, "Y");
        when(dealModel.getAttrs()).thenReturn(attrs);
        assertTrue(invokePrivateMethod("isAppointOnline", dealModel));
    }
}
