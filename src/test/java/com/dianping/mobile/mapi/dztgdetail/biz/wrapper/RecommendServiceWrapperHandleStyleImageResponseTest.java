package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.entity.Response;
import com.dianping.martgeneral.recommend.api.enums.PlatformEnum;
import com.dianping.martgeneral.recommend.api.service.RecommendService;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageVO;
import com.dianping.mobile.mapi.dztgdetail.entity.QueryRecommendParam;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.google.common.base.Joiner;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class RecommendServiceWrapperHandleStyleImageResponseTest {

    @InjectMocks
    private RecommendServiceWrapper recommendServiceWrapper;

    @Mock
    private ImmersiveImageWrapper immersiveImageWrapper;

    @Mock
    private RecommendService recommendServiceFuture;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private QueryRecommendParam createQueryRecommendParam() {
        return QueryRecommendParam.builder().isMt(true).cityId(1).dpId("dpId").uuid("uuid").originUserId("originUserId").platformEnum(PlatformEnum.MT).latitude(0.0).longitude(0.0).exhibitImgIds(Arrays.asList("imgId1", "imgId2")).filterIds("filterIds").sortType("sortType").flowFlag("flowFlag").shopRatingThreshold("threshold").shopId(1L).start(0).limit(10).clientType(1).categoryId(1).dealGroupPrice("price").isAll("all").dealGroupTagIds("tagIds").dpDealGroupId(1L).build();
    }

    private ImmersiveImageVO invokeHandleStyleImageResponse(Response<RecommendResult<RecommendDTO>> response, QueryRecommendParam param) throws Exception {
        Method method = RecommendServiceWrapper.class.getDeclaredMethod("handleStyleImageResponse", Response.class, QueryRecommendParam.class);
        method.setAccessible(true);
        return (ImmersiveImageVO) method.invoke(recommendServiceWrapper, response, param);
    }

    @Test
    public void testHandleStyleImageResponseWhenResponseIsNull() throws Throwable {
        QueryRecommendParam param = createQueryRecommendParam();
        ImmersiveImageVO result = invokeHandleStyleImageResponse(null, param);
        assertNull(result);
    }

    @Test
    public void testHandleStyleImageResponseWhenResponseResultIsNull() throws Throwable {
        QueryRecommendParam param = createQueryRecommendParam();
        Response<RecommendResult<RecommendDTO>> response = new Response<>();
        response.setResult(null);
        ImmersiveImageVO result = invokeHandleStyleImageResponse(response, param);
        assertNull(result);
    }

    @Test
    public void testHandleStyleImageResponseWhenSortedResultIsEmpty() throws Throwable {
        QueryRecommendParam param = createQueryRecommendParam();
        Response<RecommendResult<RecommendDTO>> response = new Response<>();
        RecommendResult<RecommendDTO> result = new RecommendResult<>();
        result.setSortedResult(Collections.emptyList());
        response.setResult(result);
        ImmersiveImageVO resultVO = invokeHandleStyleImageResponse(response, param);
        assertNull(resultVO);
    }

    @Test
    public void testHandleStyleImageResponseWhenSortedResultIsNotEmpty() throws Throwable {
        QueryRecommendParam param = createQueryRecommendParam();
        Response<RecommendResult<RecommendDTO>> response = new Response<>();
        RecommendResult<RecommendDTO> result = new RecommendResult<>();
        RecommendDTO recommendDTO = new RecommendDTO();
        recommendDTO.setItem("item");
        result.setSortedResult(Arrays.asList(recommendDTO));
        response.setResult(result);
        when(immersiveImageWrapper.batchGetStyleImage(anyList(), anyString(), anyInt(), anyBoolean(), anyLong(), anyLong(), anyInt())).thenReturn(new ImmersiveImageVO());
        ImmersiveImageVO resultVO = invokeHandleStyleImageResponse(response, param);
        assertNotNull(resultVO);
    }

    @Test(expected = RuntimeException.class)
    public void testInvokeRecommendServiceThrowsException() throws Throwable {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum.MEITUAN_APP);
        envCtx.setUuid("test-uuid");
        envCtx.setMtUserId(12345L);
        when(recommendServiceFuture.recommend(any(RecommendParameters.class), eq(RecommendDTO.class))).thenThrow(new RuntimeException("Service error"));
        recommendServiceWrapper.invokeRecommend(1, 31.23, 121.47, 0, 10, envCtx, Collections.singletonList(12345L));
    }
}
