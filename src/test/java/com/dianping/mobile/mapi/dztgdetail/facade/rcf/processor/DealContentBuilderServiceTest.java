package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.haima.client.response.HaimaResponse;
import com.dianping.haima.entity.haima.HaimaConfig;
import com.dianping.haima.entity.haima.HaimaContent;
import com.dianping.lion.client.Lion;
import com.dianping.lion.facade.LionFacade;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.headerpic.DefaultHeaderPicProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.headerpic.HeaderPicProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.HaimaWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ExhibitImageItemVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImageTagVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImageUrlVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.ExhibitContentDTO;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.DealContentBuilderService;
import com.dianping.mobile.mapi.dztgdetail.helper.ImageHelper;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.dianping.mobile.mapi.dztgdetail.util.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.video.DealGroupVideoDTO;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * @Author: <EMAIL>
 * @Date: 2024/5/19
 */
@RunWith(MockitoJUnitRunner.class)
public class DealContentBuilderServiceTest {
    @InjectMocks
    private DealContentBuilderService dealContentBuilderService;
    private MockedStatic<Lion> lionMockedStatic;
    private MockedStatic<LionFacade> lionFacadeMockedStatic;
    @Mock
    private DouHuBiz douHuBiz;
    @Mock
    private HaimaWrapper haimaWrapper;

    @Before
    public void setUp() throws Exception {
        // mock静态方法前，需要调用mockStatic方法
        lionMockedStatic = mockStatic(Lion.class);
        lionFacadeMockedStatic = mockStatic(LionFacade.class);

    }

    @After
    public void teardown() {
        lionMockedStatic.close();
        lionFacadeMockedStatic.close();
    }

    /**
     * 雪碧图测试
     */
    @Test
    public void testSpriteImage() {
        String dealGroupVideoDtoStr = "{\"videoPath\":\"https://msstest-corp.sankuai.com/v1/mss_13ead3cf7c264613878fe7703bf4ce06/dp-merchant-upload/2f1c0b56-c494-4f91-8f30-ee540277f2d4.mp4\",\"videoCoverPath\":\"https://p0.meituan.net/dpmerchantimage/65e2dfc3-de1a-43a7-bfb7-b6c7bf4f991d.jpeg@720h_1280w_2e\",\"videoSize\":115,\"extendVideos\":[{\"path\":\"https://msstest-corp.sankuai.com/v1/mss_13ead3cf7c264613878fe7703bf4ce06/dp-merchant-upload/2f1c0b56-c494-4f91-8f30-ee540277f2d4.mp4\",\"ratio\":\"16:9\",\"coverPath\":\"https://p0.meituan.net/dpmerchantimage/65e2dfc3-de1a-43a7-bfb7-b6c7bf4f991d.jpeg@720h_1280w_2e\"},{\"path\":\"https://msstest-corp.sankuai.com/v1/mss_13ead3cf7c264613878fe7703bf4ce06/dp-merchant-upload/38a9d41c-142c-48a6-a63c-a832d02d3575.mp4\",\"ratio\":\"1280:720\",\"coverPath\":\"https://p0.meituan.net/dpmerchantimage/65e2dfc3-de1a-43a7-bfb7-b6c7bf4f991d.jpeg@720h_1280w_2e\",\"spriteImageList\":[{\"path\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/3127cd91900ec699449d5edaca8554d217569.jpg\",\"width\":2000,\"height\":1120,\"subImageWidth\":200,\"subImageHeight\":112,\"subImageCount\":1}]}]}";
        DealGroupVideoDTO dealGroupVideoDTO = JSON.parseObject(dealGroupVideoDtoStr, DealGroupVideoDTO.class);
        String videoStr = "{\"type\":2,\"content\":\"https://p0.meituan.net/dpmerchantimage/65e2dfc3-de1a-43a7-bfb7-b6c7bf4f991d.jpeg@720h_1280w_2e\",\"videoUrl\":\"https://msstest-corp.sankuai.com/v1/mss_13ead3cf7c264613878fe7703bf4ce06/dp-merchant-upload/38a9d41c-142c-48a6-a63c-a832d02d3575.mp4\",\"desc\":\"当前Wi-Fi环境确定播放？预计花费流量0.11M\",\"scale\":\"16:9\"}";
        ContentPBO video = JSON.parseObject(videoStr, ContentPBO.class);
        dealContentBuilderService.assembleOriginVideoSpritePic(dealGroupVideoDTO, video);
        dealContentBuilderService.buildSpritePicByExtendVideoDto(video, dealGroupVideoDTO.getExtendVideos().get(0));
        String dealGroupDtoStr = "{\"dpDealGroupId\":427885825,\"mtDealGroupId\":427885825,\"basic\":{\"categoryId\":503,\"title\":\"测试丽人美容\",\"brandName\":\"京世沙龙\",\"titleDesc\":\"仅售11元，价值1212元测试丽人美容！\",\"beginSaleDate\":\"2023-12-28 20:03:35\",\"endSaleDate\":\"2024-02-27 00:30:52\",\"status\":1,\"saleChannel\":0,\"salePlatform\":3,\"sourceId\":102,\"tradeType\":3},\"image\":{\"defaultPicPath\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/31f41dbb2a843a123bf74b287b8c8076151867.png\",\"allPicPaths\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/31f41dbb2a843a123bf74b287b8c8076151867.png\",\"videoPath\":\"https://msstest-corp.sankuai.com/v1/mss_13ead3cf7c264613878fe7703bf4ce06/dp-merchant-upload/2f1c0b56-c494-4f91-8f30-ee540277f2d4.mp4\",\"videoCoverPath\":\"https://p0.meituan.net/dpmerchantimage/65e2dfc3-de1a-43a7-bfb7-b6c7bf4f991d.jpeg@720h_1280w_2e\",\"videoSize\":115,\"extendVideos\":[{\"path\":\"https://msstest-corp.sankuai.com/v1/mss_13ead3cf7c264613878fe7703bf4ce06/dp-merchant-upload/38a9d41c-142c-48a6-a63c-a832d02d3575.mp4\",\"ratio\":\"1280:720\",\"coverPath\":\"https://p0.meituan.net/dpmerchantimage/65e2dfc3-de1a-43a7-bfb7-b6c7bf4f991d.jpeg@720h_1280w_2e\",\"spriteImageList\":[{\"path\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/3127cd91900ec699449d5edaca8554d217569.jpg\",\"width\":2000,\"height\":1120,\"subImageWidth\":200,\"subImageHeight\":112,\"subImageCount\":1}]}],\"allVideos\":[{\"videoPath\":\"https://msstest-corp.sankuai.com/v1/mss_13ead3cf7c264613878fe7703bf4ce06/dp-merchant-upload/2f1c0b56-c494-4f91-8f30-ee540277f2d4.mp4\",\"videoCoverPath\":\"https://p0.meituan.net/dpmerchantimage/65e2dfc3-de1a-43a7-bfb7-b6c7bf4f991d.jpeg@720h_1280w_2e\",\"videoSize\":115,\"extendVideos\":[{\"path\":\"https://msstest-corp.sankuai.com/v1/mss_13ead3cf7c264613878fe7703bf4ce06/dp-merchant-upload/2f1c0b56-c494-4f91-8f30-ee540277f2d4.mp4\",\"ratio\":\"16:9\",\"coverPath\":\"https://p0.meituan.net/dpmerchantimage/65e2dfc3-de1a-43a7-bfb7-b6c7bf4f991d.jpeg@720h_1280w_2e\"},{\"path\":\"https://msstest-corp.sankuai.com/v1/mss_13ead3cf7c264613878fe7703bf4ce06/dp-merchant-upload/38a9d41c-142c-48a6-a63c-a832d02d3575.mp4\",\"ratio\":\"1280:720\",\"coverPath\":\"https://p0.meituan.net/dpmerchantimage/65e2dfc3-de1a-43a7-bfb7-b6c7bf4f991d.jpeg@720h_1280w_2e\",\"spriteImageList\":[{\"path\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/3127cd91900ec699449d5edaca8554d217569.jpg\",\"width\":2000,\"height\":1120,\"subImageWidth\":200,\"subImageHeight\":112,\"subImageCount\":1}]}]}]},\"category\":{\"categoryId\":503,\"serviceType\":\"皮肤管理\",\"serviceTypeId\":792},\"serviceProject\":{\"title\":\"团购详情\",\"salePrice\":\"11.00\",\"marketPrice\":\"1212.00\",\"mustGroups\":[{\"groups\":[{\"skuId\":0,\"categoryId\":4038,\"name\":\"2112\",\"amount\":1,\"marketPrice\":\"1212.0\",\"status\":10,\"attrs\":[{\"metaAttrId\":572,\"attrName\":\"duration\",\"chnName\":\"服务时长\",\"attrValue\":\"15分钟\",\"rawAttrValue\":\"15\",\"unit\":\"分钟\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1756,\"attrName\":\"servicestep\",\"chnName\":\"服务步骤\",\"attrValue\":\"[{\\\"subStepName\\\":\\\"卸妆洁面\\\",\\\"stepTime\\\":\\\"15\\\",\\\"equipment\\\":\\\"1212\\\",\\\"product\\\":\\\"1221\\\",\\\"stepDesc\\\":\\\"\\\",\\\"bodyPart\\\":\\\"鼻部\\\",\\\"stepName\\\":\\\"卸妆洁面\\\"}]\",\"rawAttrValue\":\"[{\\\"subStepName\\\":\\\"卸妆洁面\\\",\\\"stepTime\\\":\\\"15\\\",\\\"equipment\\\":\\\"1212\\\",\\\"product\\\":\\\"1221\\\",\\\"stepDesc\\\":\\\"\\\",\\\"bodyPart\\\":\\\"鼻部\\\",\\\"stepName\\\":\\\"卸妆洁面\\\"}]\",\"valueType\":300,\"sequence\":0},{\"metaAttrId\":1037,\"attrName\":\"category1\",\"chnName\":\"一级分类\",\"attrValue\":\"痘肌护理\",\"rawAttrValue\":\"痘肌护理\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1038,\"attrName\":\"category2\",\"chnName\":\"二级分类\",\"attrValue\":\"淡化痘印\",\"rawAttrValue\":\"淡化痘印\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":543,\"attrName\":\"bodyname\",\"chnName\":\"适用部位\",\"attrValue\":\"U区\",\"rawAttrValue\":\"U区\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":3028,\"attrName\":\"applySkin\",\"chnName\":\"适用肤质\",\"attrValue\":\"适用部分肤质\",\"rawAttrValue\":\"适用部分肤质\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":3029,\"attrName\":\"skinType\",\"chnName\":\"适用部分肤质\",\"attrValue\":\"干性/混干肌肤\",\"rawAttrValue\":\"干性/混干肌肤\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":542,\"attrName\":\"serviceeffect\",\"chnName\":\"服务功效\",\"attrValue\":\"补水保湿\",\"rawAttrValue\":\"补水保湿\",\"valueType\":500,\"sequence\":0}]}]}],\"optionGroups\":[],\"structType\":\"uniform-structure-table\"},\"channel\":{\"channelId\":5,\"channelEn\":\"beauty\",\"channelCn\":\"丽人\",\"channelGroupId\":2,\"channelGroupEn\":\"general\",\"channelGroupCn\":\"到店综合\"},\"attrs\":[{\"name\":\"product_finish_date\",\"value\":[\"1970-01-01 08:00:00\"],\"source\":0},{\"name\":\"calc_holiday_available\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_business_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_channel_id_allowed\",\"value\":[\"0\"],\"source\":0},{\"name\":\"product_can_use_coupon\",\"value\":[\"true\"],\"source\":0},{\"name\":\"preSaleTag\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_third_party_verify\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_discount_rule_id\",\"value\":[\"0\"],\"source\":0},{\"name\":\"product_block_stock\",\"value\":[\"false\"],\"source\":0},{\"name\":\"service_type\",\"value\":[\"皮肤管理\"],\"source\":0},{\"name\":\"reservation_is_needed_or_not\",\"value\":[\"否\"],\"source\":0},{\"name\":\"sys_deal_universal_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"category\",\"value\":[\"50\",\"5003\"],\"source\":0}],\"rule\":{\"buyRule\":{\"maxPerUser\":0,\"minPerUser\":1},\"useRule\":{\"receiptEffectiveDate\":{\"receiptDateType\":1,\"receiptValidDays\":90,\"receiptBeginDate\":\"2024-01-22 20:43:44\",\"receiptEndDate\":\"2024-04-21 23:59:59\",\"showText\":\"购买后90天内有效\"}},\"refundRule\":{\"supportRefundType\":1,\"supportOverdueAutoRefund\":true}},\"displayShopInfo\":{\"dpDisplayShopIds\":[23136039],\"mtDisplayShopIds\":[403561]},\"tags\":[{\"id\":501004,\"tagName\":\"祛痘清痘\"},{\"id\":5010,\"tagName\":\"皮肤管理\"},{\"id\":100069568,\"tagName\":\"新皮管\"},{\"id\":100069644,\"tagName\":\"淡化痘印\"},{\"id\":5202,\"tagName\":\"补水保湿\"}],\"customer\":{\"originCustomerId\":976709},\"deals\":[{\"dealId\":453946727,\"basic\":{\"title\":\"测试丽人美容\",\"originTitle\":\"测试丽人美容\",\"status\":1},\"price\":{\"salePrice\":\"11.00\",\"marketPrice\":\"1212.00\",\"version\":5144026433},\"stock\":{\"dpSales\":0,\"dpTotal\":2000000,\"dpRemain\":2000000,\"mtSales\":0,\"mtTotal\":2000000,\"mtRemain\":2000000,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1},\"attrs\":[{\"name\":\"sku_receipt_type\",\"value\":[\"1\"],\"source\":0}],\"dealIdInt\":453946727}],\"price\":{\"salePrice\":\"11.00\",\"marketPrice\":\"1212.00\",\"version\":5144026433},\"stock\":{\"dpSales\":0,\"dpTotal\":2000000,\"dpRemain\":2000000,\"mtSales\":0,\"mtTotal\":2000000,\"mtRemain\":2000000,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1},\"detail\":{\"dealGroupPics\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/31f41dbb2a843a123bf74b287b8c8076151867.png\",\"images\":[\"http://i2.s2.51ping.com/dpmerchantpic/31f41dbb2a843a123bf74b287b8c8076151867.png\"],\"importantPoint\":\"\",\"templateDetailDTOs\":[{\"title\":\"产品介绍\",\"content\":\"<div><p>千万千万</p></div>\\n\\n\",\"type\":5,\"blockId\":0},{\"title\":\"团购详情\",\"content\":\"        <div class=\\\"detail-tit\\\"></div>\\n        <div>\\n            <table width=\\\"100%\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" class=\\\"detail-table\\\">\\n                <thead>\\n                <tr>\\n                    <th width=\\\"50%\\\">名称</th>\\n                    <th width=\\\"25%\\\">数量</th>\\n                    <th width=\\\"25%\\\">单价</th>\\n                </tr>\\n                </thead>\\n                <tbody>\\n                            <tr>\\n                                <td>2112</td>\\n                                    <td class=\\\"tc\\\">1</td>\\n                                    <td class=\\\"tc\\\">1212元</td>\\n                            </tr>\\n                    <tr class=\\\"total\\\">\\n                        <td></td>\\n                        <td class=\\\"tc\\\">总价<br><strong>团购价</strong></td>\\n                        <td class=\\\"tc\\\">1212元<br><strong>11元</strong>\\n                        </td>\\n                    </tr>\\n                </tbody>\\n            </table>\\n                <div><b>详细信息</b><br><b>•2112</b><br>服务时长: 15分钟<br>适用部位: U区<br>适用肤质: 适用部分肤质<br>适用部分肤质: 干性/混干肌肤<br>服务功效: 补水保湿<br><br></div>\\n        </div>\\n\\n\",\"type\":1,\"blockId\":0},{\"title\":\"购买须知\",\"content\":\"<div class=\\\"detail-box\\\">\\n    <div class=\\\"purchase-notes\\\">\\n\\t\\t        <dl>\\n            <dt>有效期</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">\\n    购买后90天内有效\\n        </p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>预约信息</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">无需预约，如遇消费高峰时段您可能需要排队</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>适用人数</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">每张团购券不限使用人数</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>规则提醒</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">不再与其他优惠同享\\n</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>温馨提示</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">团购实际服务时长根据您个人的肤质、身体情况可能略有调整，建议您在消费时向商户确认</p>\\n                <p class=\\\"listitem\\\">如需团购券发票，请您在消费时向商户咨询</p>\\n                <p class=\\\"listitem\\\">为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>效果提醒</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">使用效果因人而异，图片仅供参考</p>\\n            </dd>\\n        </dl>\\n    </div>\\n</div>\\n\",\"type\":2,\"blockId\":0}]},\"dpDealGroupIdInt\":427885825,\"mtDealGroupIdInt\":427885825}";
        DealGroupDTO dealGroupDTO = JSON.parseObject(dealGroupDtoStr, DealGroupDTO.class);
        video.setScale("1280:720");
        assertNotNull(video);
    }

    @Test
    public void testGetContent() {
        Map<String, HeaderPicProcessor> headerPicProcessorMap = new HashMap<>();
        headerPicProcessorMap.put("defaultHeaderPicProcessor", new DefaultHeaderPicProcessor());
        dealContentBuilderService.setHeaderPicProcessorMap(headerPicProcessorMap);
        lionMockedStatic.when(() -> Lion.getBoolean(LionConstants.APP_KEY, LionConstants.HEAD_VIDEO_SWITCH, false)).thenReturn(true);

        Map<String, Integer> sizeMap = new HashMap<>();
        sizeMap.put("width", 960);
        sizeMap.put("width", 540);
        Map<String, Map<String, Integer>> categorySizeMap = new HashMap<>();
        categorySizeMap.put("401", sizeMap);
        lionFacadeMockedStatic.when(() -> LionFacade.get(LionConstants.PICSIZE_CONFIG, new TypeReference<Map<Integer, Map<String, Integer>>>() {
        })).thenReturn("{\"502\":\"beautyHeaderPic\",\"503\":\"bodycareHeaderPicProcessor\",\"504\":\"photoHeaderPicProcessor\",\"1004\":\"photoHeaderPicProcessor\",\"1202\":\"educationHeaderPicProcessor\",\"1203\":\"educationHeaderPicProcessor\",\"1211\":\"educationHeaderPicProcessor\",\"1212\":\"educationHeaderPicProcessor\",\"1213\":\"educationHeaderPicProcessor\",\"1217\":\"educationHeaderPicProcessor\",\"1220\":\"educationHeaderPicProcessor\",\"910\":\"photoHeaderPicProcessor\",\"default\":\"defaultHeaderPicProcessor\"}");
        Map<String, String> config = GsonUtils.fromJsonString("{\"502\":\"beautyHeaderPic\",\"503\":\"bodycareHeaderPicProcessor\",\"504\":\"photoHeaderPicProcessor\",\"1004\":\"photoHeaderPicProcessor\",\"1202\":\"educationHeaderPicProcessor\",\"1203\":\"educationHeaderPicProcessor\",\"1211\":\"educationHeaderPicProcessor\",\"1212\":\"educationHeaderPicProcessor\",\"1213\":\"educationHeaderPicProcessor\",\"1217\":\"educationHeaderPicProcessor\",\"1220\":\"educationHeaderPicProcessor\",\"910\":\"photoHeaderPicProcessor\",\"default\":\"defaultHeaderPicProcessor\"}", new TypeToken<Map<String, String>>() {}.getType());
        lionMockedStatic.when(() -> Lion.getMap(LionConstants.APP_KEY,
                LionConstants.CATEGORY_HEADER_PIC_PROCESSOR_CONFIG, String.class, Collections.emptyMap())).thenReturn(config);

        // 准备测试数据
        String defaultPic = null;
        int width = 100;
        int height = 200;
        boolean isMt = true;
        String expectedPicUrl = "formattedPicUrl";

        // Mock静态方法
        try (MockedStatic<ImageHelper> mockedStatic = Mockito.mockStatic(ImageHelper.class)) {
            mockedStatic.when(() -> ImageHelper.format(defaultPic, width, height, isMt)).thenReturn(expectedPicUrl);
        }

        DealGroupBaseDTO dealGroupBaseDTO = JsonUtils.fromJson("{\"dealGroupId\":607771703,\"dealGroupShortTitle\":\"Homie Barber Shop\",\"dealGroupTitleDesc\":\"仅售238元，价值288元「体验」Barber美式渐变油头男士理发，男女通用！\",\"maxJoin\":0,\"currentJoin\":0,\"defaultPic\":\"\",\"dealGroupPrice\":238.00,\"marketPrice\":288.00,\"maxPerUser\":0,\"minPerUser\":1,\"status\":1,\"autoRefundSwitch\":1,\"dealGroupPics\":\"\",\"saleChannel\":0,\"salePlatform\":3,\"publishStatus\":1,\"dealGroupType\":1,\"overdueAutoRefund\":true,\"canUseCoupon\":true,\"thirdPartVerify\":false,\"payChannelIDAllowed\":0,\"discountRuleID\":0,\"blockStock\":false,\"publishVersion\":0,\"lottery\":false,\"productTitle\":\"「体验」Barber美式渐变油头男士理发\",\"featureTitle\":\"男女通用\",\"deals\":[{\"dealId\":610286927,\"dealGroupId\":607771703,\"shortTitle\":\"「体验」Barber美式渐变油头男士理发\",\"price\":238.00,\"marketPrice\":288.00,\"maxJoin\":0,\"currentJoin\":0,\"receiptType\":1,\"deliverType\":0,\"dealStatus\":1,\"receiptDateType\":1,\"receiptValidDays\":15,\"provideInvoice\":false,\"thirdPartyId\":0,\"dealGroupPublishVersion\":0}],\"sourceId\":102}", DealGroupBaseDTO.class);
        DealGroupPBO dealGroupPBO = JsonUtils.fromJson("{\"dpId\":607771703,\"mtId\":607771703,\"dpDealId\":610286927,\"title\":\"「体验」Barber美式渐变油头男士理发\",\"maxPerUser\":0,\"categoryId\":501,\"type\":0,\"picAspectRatio\":0.0,\"shareAble\":true,\"shopCardState\":0,\"userCardState\":0,\"hasReserveEntrance\":false,\"showNewReserveEntrance\":false,\"moduleConfigsModule\":{\"key\":\"beauty_hair\",\"extraInfo\":\"newtuandeal\",\"generalInfo\":\"card_style_v2\",\"moduleConfigs\":[{\"key\":\"dealdetail_gc_packagedetail\",\"value\":\"custom_structured_module\"}],\"moduleAbConfigs\":[{\"configs\":[{\"expId\":\"exp001049\",\"expResult\":\"exp001049_b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"1d5088f1-42f8-4e5d-a63e-e86088627258\\\",\\\"ab_id\\\":\\\"exp001049_b\\\"}\"}],\"key\":\"dealdetail_gc_packagedetail\"},{\"configs\":[{\"expId\":\"exp000011\",\"expResult\":\"exp000011_b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"2d11af08-1207-4bc0-a1bd-cb7fd86db199\\\",\\\"ab_id\\\":\\\"exp000011_b\\\"}\"}],\"key\":\"GCPlatformModules/picasso_deal_detail_head_module\"}],\"tort\":false,\"dzx\":true,\"dpOrder\":true},\"relatedBehaviorModule\":{\"relatedUserBehaviorItems\":[{\"userName\":\"V**4\",\"userAvatarUrl\":\"https://img.meituan.net/avatar/12029332c13465cf85ae0fb3ba388ee848541.jpg\",\"userBehaviorDesc\":\"1小时前下单了\"},{\"userName\":\"田**9\",\"userAvatarUrl\":\"https://img.meituan.net/relationwxpic/a45f6549c63e3d03edb848d7dcf7966f54810.jpg%4048w_48h_1e_1c_1l%7Cwatermark%3D0\",\"userBehaviorDesc\":\"15小时前下单了\"},{\"userName\":\"z**0\",\"userAvatarUrl\":\"https://p0.meituan.net/ingee/4e78589c0aa6132682faae3f0438c8a4298832.png\",\"userBehaviorDesc\":\"19小时前下单了\"},{\"userName\":\"D**r\",\"userAvatarUrl\":\"https://p0.meituan.net/userheadpicbackend/fc86079fc62e794693f4f6b03a12f3244693.jpg%4048w_48h_1e_1c_1l%7Cwatermark%3D0\",\"userBehaviorDesc\":\"19小时前下单了\"},{\"userName\":\"B**T\",\"userAvatarUrl\":\"https://p0.meituan.net/userheadpicbackend/d4d7a84d65a1520d88660e0fb7c1e0d7125759.jpg%4048w_48h_1e_1c_1l%7Cwatermark%3D0\",\"userBehaviorDesc\":\"21小时前下单了\"},{\"userName\":\"蛋**3\",\"userAvatarUrl\":\"https://p1.meituan.net/relationwxpic/1f4ed9ba60efd088088c155aec20cac23863.jpg%4048w_48h_1e_1c_1l%7Cwatermark%3D0\",\"userBehaviorDesc\":\"22小时前下单了\"}]},\"needLogin\":false,\"bgName\":\"general\",\"serviceType\":\"洗剪吹\",\"serviceTypeId\":809,\"skuId\":\"610286927\",\"hitStructuredPurchaseNote\":false,\"tradeType\":0,\"purchaseLimitDeal\":false,\"meetPurchaseLimit\":false,\"standardDealGroup\":false}", DealGroupPBO.class);
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(501);
        ctx.setChannelDTO(channelDTO);

        DealGroupDTO dealGroupDTO =new DealGroupDTO();
        dealGroupDTO.setCategory(new DealGroupCategoryDTO());
        ctx.setDealGroupDTO(dealGroupDTO);
        List<ContentPBO> contentPBOList = dealContentBuilderService.getContent(dealGroupBaseDTO, true, ctx, dealGroupPBO);
        Assert.assertTrue(contentPBOList.size() == 0);
    }

    @Test
    public void testShowCustomVideo() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setModuleAbConfigs(Lists.newArrayList());
        List<AbConfig> abConfigs = new ArrayList<>();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("b");
        abConfigs.add(abConfig);


        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setConfigs(abConfigs);
        when(douHuBiz.getAbExpResultByUserId(Mockito.any(), Mockito.anyString())).thenReturn(moduleAbConfig);
        boolean result = dealContentBuilderService.showCustomVideo(ctx);
        Assert.assertTrue(result);
    }

    @Test
    public void testAssembleVideo() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(501);
        ctx.setChannelDTO(channelDTO);
        ctx.setEnableCardStyleV2(true);

        DealGroupBaseDTO dealGroupBaseDTO = JsonUtils.fromJson("{\"dealGroupId\":834478951,\"dealGroupShortTitle\":\"乐客LEKE VR科技娱乐空间\",\"dealGroupTitleDesc\":\"仅售58元，价值59元【益智探险】守护星缘堡·巴啦啦能量！\",\"maxJoin\":0,\"currentJoin\":0,\"defaultPic\":\"https://p0.meituan.net/dpmerchantpic/5dc545adfe88ec33b588c0e4637ab433873069.jpg\",\"headVideo\":{\"videoPath\":\"https://s3plus.meituan.net/v1/mss_d9acd3933f3e4491bc67fccfa5d45505/merchant-video/62cb42e7-c601-4252-937f-32521beda139.mp4\",\"videoCoverPath\":\"https://p0.meituan.net/dpmerchantimage/905a5924dd3f19ea03f5c77e8893143361003.jpg\",\"size\":13598},\"dealGroupPrice\":58.00,\"marketPrice\":59.00,\"maxPerUser\":1,\"minPerUser\":1,\"status\":1,\"autoRefundSwitch\":1,\"dealGroupPics\":\"https://p0.meituan.net/dpmerchantpic/5dc545adfe88ec33b588c0e4637ab433873069.jpg\",\"saleChannel\":0,\"salePlatform\":3,\"publishStatus\":1,\"dealGroupType\":1,\"overdueAutoRefund\":true,\"canUseCoupon\":true,\"thirdPartVerify\":false,\"payChannelIDAllowed\":0,\"discountRuleID\":0,\"blockStock\":false,\"publishVersion\":0,\"lottery\":false,\"productTitle\":\"【益智探险】守护星缘堡·巴啦啦能量\",\"featureTitle\":\"\",\"deals\":[{\"dealId\":1081995670,\"dealGroupId\":834478951,\"shortTitle\":\"【益智探险】守护星缘堡·巴啦啦能量\",\"price\":58.00,\"marketPrice\":59.00,\"maxJoin\":0,\"currentJoin\":0,\"receiptType\":1,\"deliverType\":0,\"dealStatus\":1,\"receiptDateType\":1,\"receiptValidDays\":90,\"provideInvoice\":false,\"thirdPartyId\":0,\"dealGroupPublishVersion\":0}],\"sourceId\":102}", DealGroupBaseDTO.class);

        lionMockedStatic.when(() -> Lion.getBoolean(LionConstants.APP_KEY, LionConstants.COMPRESS_VIDEO_COVER_PIC, true)).thenReturn(false);
        lionMockedStatic.when(() -> Lion.getList(LionConstants.APP_KEY,
                LionConstants.DISTRIBUTE_IMAGE_SCALE_WHITELIST, Integer.class, Collections.emptyList())).thenReturn(Lists.newArrayList(501));

        List<ContentPBO> result = new ArrayList<>();
        dealContentBuilderService.assembleVideo(dealGroupBaseDTO, true, ctx, result);
        Assert.assertTrue(result.size() == 1);
    }

    @Test
    public void testHitBlacklist() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setDpId(123);
        ctx.setDpLongShopId(123L);

        HaimaResponse haimaResponse = new HaimaResponse();
        List<HaimaConfig> haimaConfigs = new ArrayList<>();
        HaimaConfig haimaConfig = new HaimaConfig();
        List<HaimaContent> contents = new ArrayList<>();
        HaimaContent haimaContent = new HaimaContent();
        contents.add(haimaContent);
        haimaConfig.setContents(contents);
        haimaConfigs.add(haimaConfig);
        haimaResponse.setData(haimaConfigs);
        when(haimaWrapper.queryHaimaConfig(Mockito.any())).thenReturn(haimaResponse);
        boolean result = dealContentBuilderService.hitBlacklist(ctx);
        Assert.assertTrue(!result);
    }


    @Test
    public void testGetContent_406() {
        Map<String, HeaderPicProcessor> headerPicProcessorMap = new HashMap<>();
        headerPicProcessorMap.put("defaultHeaderPicProcessor", new DefaultHeaderPicProcessor());
        dealContentBuilderService.setHeaderPicProcessorMap(headerPicProcessorMap);
        lionMockedStatic.when(() -> Lion.getBoolean(LionConstants.APP_KEY, LionConstants.HEAD_VIDEO_SWITCH, false)).thenReturn(true);

        Map<String, Integer> sizeMap = new HashMap<>();
        sizeMap.put("width", 960);
        sizeMap.put("width", 540);
        Map<String, Map<String, Integer>> categorySizeMap = new HashMap<>();
        categorySizeMap.put("401", sizeMap);
        lionFacadeMockedStatic.when(() -> LionFacade.get(LionConstants.PICSIZE_CONFIG, new TypeReference<Map<Integer, Map<String, Integer>>>() {
        })).thenReturn("{\"502\":\"beautyHeaderPic\",\"503\":\"bodycareHeaderPicProcessor\",\"504\":\"photoHeaderPicProcessor\",\"1004\":\"photoHeaderPicProcessor\",\"1202\":\"educationHeaderPicProcessor\",\"1203\":\"educationHeaderPicProcessor\",\"1211\":\"educationHeaderPicProcessor\",\"1212\":\"educationHeaderPicProcessor\",\"1213\":\"educationHeaderPicProcessor\",\"1217\":\"educationHeaderPicProcessor\",\"1220\":\"educationHeaderPicProcessor\",\"910\":\"photoHeaderPicProcessor\",\"default\":\"defaultHeaderPicProcessor\"}");
        Map<String, String> config = GsonUtils.fromJsonString("{\"502\":\"beautyHeaderPic\",\"503\":\"bodycareHeaderPicProcessor\",\"504\":\"photoHeaderPicProcessor\",\"1004\":\"photoHeaderPicProcessor\",\"1202\":\"educationHeaderPicProcessor\",\"1203\":\"educationHeaderPicProcessor\",\"1211\":\"educationHeaderPicProcessor\",\"1212\":\"educationHeaderPicProcessor\",\"1213\":\"educationHeaderPicProcessor\",\"1217\":\"educationHeaderPicProcessor\",\"1220\":\"educationHeaderPicProcessor\",\"910\":\"photoHeaderPicProcessor\",\"default\":\"defaultHeaderPicProcessor\"}", new TypeToken<Map<String, String>>() {}.getType());
        lionMockedStatic.when(() -> Lion.getMap(LionConstants.APP_KEY,
                LionConstants.CATEGORY_HEADER_PIC_PROCESSOR_CONFIG, String.class, Collections.emptyMap())).thenReturn(config);

        lionMockedStatic.when(() -> Lion.getList(LionConstants.APP_KEY,
                LionConstants.NEW_EXHIBIT_CAETGORY_IDS, Integer.class, Collections.emptyList())).thenReturn(Arrays.asList(406));

        // 准备测试数据
        String defaultPic = null;
        int width = 100;
        int height = 200;
        boolean isMt = true;
        String expectedPicUrl = "formattedPicUrl";

        // Mock静态方法
        try (MockedStatic<ImageHelper> mockedStatic = Mockito.mockStatic(ImageHelper.class)) {
            mockedStatic.when(() -> ImageHelper.format(defaultPic, width, height, isMt)).thenReturn(expectedPicUrl);
        }

        DealGroupBaseDTO dealGroupBaseDTO = JsonUtils.fromJson("{\"dealGroupId\":607771703,\"dealGroupShortTitle\":\"Homie Barber Shop\",\"dealGroupTitleDesc\":\"仅售238元，价值288元「体验」Barber美式渐变油头男士理发，男女通用！\",\"maxJoin\":0,\"currentJoin\":0,\"defaultPic\":\"\",\"dealGroupPrice\":238.00,\"marketPrice\":288.00,\"maxPerUser\":0,\"minPerUser\":1,\"status\":1,\"autoRefundSwitch\":1,\"dealGroupPics\":\"\",\"saleChannel\":0,\"salePlatform\":3,\"publishStatus\":1,\"dealGroupType\":1,\"overdueAutoRefund\":true,\"canUseCoupon\":true,\"thirdPartVerify\":false,\"payChannelIDAllowed\":0,\"discountRuleID\":0,\"blockStock\":false,\"publishVersion\":0,\"lottery\":false,\"productTitle\":\"「体验」Barber美式渐变油头男士理发\",\"featureTitle\":\"男女通用\",\"deals\":[{\"dealId\":610286927,\"dealGroupId\":607771703,\"shortTitle\":\"「体验」Barber美式渐变油头男士理发\",\"price\":238.00,\"marketPrice\":288.00,\"maxJoin\":0,\"currentJoin\":0,\"receiptType\":1,\"deliverType\":0,\"dealStatus\":1,\"receiptDateType\":1,\"receiptValidDays\":15,\"provideInvoice\":false,\"thirdPartyId\":0,\"dealGroupPublishVersion\":0}],\"sourceId\":102}", DealGroupBaseDTO.class);
        DealGroupPBO dealGroupPBO = JsonUtils.fromJson("{\"dpId\":607771703,\"mtId\":607771703,\"dpDealId\":610286927,\"title\":\"「体验」Barber美式渐变油头男士理发\",\"maxPerUser\":0,\"categoryId\":501,\"type\":0,\"picAspectRatio\":0.0,\"shareAble\":true,\"shopCardState\":0,\"userCardState\":0,\"hasReserveEntrance\":false,\"showNewReserveEntrance\":false,\"moduleConfigsModule\":{\"key\":\"beauty_hair\",\"extraInfo\":\"newtuandeal\",\"generalInfo\":\"card_style_v2\",\"moduleConfigs\":[{\"key\":\"dealdetail_gc_packagedetail\",\"value\":\"custom_structured_module\"}],\"moduleAbConfigs\":[{\"configs\":[{\"expId\":\"exp001049\",\"expResult\":\"exp001049_b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"1d5088f1-42f8-4e5d-a63e-e86088627258\\\",\\\"ab_id\\\":\\\"exp001049_b\\\"}\"}],\"key\":\"dealdetail_gc_packagedetail\"},{\"configs\":[{\"expId\":\"exp000011\",\"expResult\":\"exp000011_b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"2d11af08-1207-4bc0-a1bd-cb7fd86db199\\\",\\\"ab_id\\\":\\\"exp000011_b\\\"}\"}],\"key\":\"GCPlatformModules/picasso_deal_detail_head_module\"}],\"tort\":false,\"dzx\":true,\"dpOrder\":true},\"relatedBehaviorModule\":{\"relatedUserBehaviorItems\":[{\"userName\":\"V**4\",\"userAvatarUrl\":\"https://img.meituan.net/avatar/12029332c13465cf85ae0fb3ba388ee848541.jpg\",\"userBehaviorDesc\":\"1小时前下单了\"},{\"userName\":\"田**9\",\"userAvatarUrl\":\"https://img.meituan.net/relationwxpic/a45f6549c63e3d03edb848d7dcf7966f54810.jpg%4048w_48h_1e_1c_1l%7Cwatermark%3D0\",\"userBehaviorDesc\":\"15小时前下单了\"},{\"userName\":\"z**0\",\"userAvatarUrl\":\"https://p0.meituan.net/ingee/4e78589c0aa6132682faae3f0438c8a4298832.png\",\"userBehaviorDesc\":\"19小时前下单了\"},{\"userName\":\"D**r\",\"userAvatarUrl\":\"https://p0.meituan.net/userheadpicbackend/fc86079fc62e794693f4f6b03a12f3244693.jpg%4048w_48h_1e_1c_1l%7Cwatermark%3D0\",\"userBehaviorDesc\":\"19小时前下单了\"},{\"userName\":\"B**T\",\"userAvatarUrl\":\"https://p0.meituan.net/userheadpicbackend/d4d7a84d65a1520d88660e0fb7c1e0d7125759.jpg%4048w_48h_1e_1c_1l%7Cwatermark%3D0\",\"userBehaviorDesc\":\"21小时前下单了\"},{\"userName\":\"蛋**3\",\"userAvatarUrl\":\"https://p1.meituan.net/relationwxpic/1f4ed9ba60efd088088c155aec20cac23863.jpg%4048w_48h_1e_1c_1l%7Cwatermark%3D0\",\"userBehaviorDesc\":\"22小时前下单了\"}]},\"needLogin\":false,\"bgName\":\"general\",\"serviceType\":\"洗剪吹\",\"serviceTypeId\":809,\"skuId\":\"610286927\",\"hitStructuredPurchaseNote\":false,\"tradeType\":0,\"purchaseLimitDeal\":false,\"meetPurchaseLimit\":false,\"standardDealGroup\":false}", DealGroupPBO.class);
        ExhibitContentDTO exhibitContentDTO = new ExhibitContentDTO();
        exhibitContentDTO.setItems(getItemId());

        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);

        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(406);
        ctx.setChannelDTO(channelDTO);
        ctx.setExhibitContentDTO(exhibitContentDTO);

        DealGroupDTO dealGroupDTO =new DealGroupDTO();
        dealGroupDTO.setCategory(new DealGroupCategoryDTO());
        ctx.setDealGroupDTO(dealGroupDTO);
        List<ContentPBO> contentPBOList = dealContentBuilderService.getContent(dealGroupBaseDTO, true, ctx, dealGroupPBO);
        Assert.assertTrue(contentPBOList.size() == 0);
    }


    public static List<ExhibitImageItemVO> getItemId() {
        List<ExhibitImageItemVO> exhibitItems = new ArrayList<>();
        ExhibitImageItemVO item1 = new ExhibitImageItemVO();
        item1.setItemId("item1");
        item1.setName("款式一");
        item1.setTags(Arrays.asList(new ImageTagVO("标签1", 0), new ImageTagVO("标签2", 1)));
        item1.setUrls(Collections.singletonList(ImageUrlVO.builder()
                .height(720).width(1280).type(0).url("https://example.com/image1.jpg")
                .thumbPic(null).scale("720x1280").imageBestScale("720x1280").hotNailStyle(null)
                .spritePic(null).build()));
        exhibitItems.add(item1);
        ExhibitImageItemVO item2 = new ExhibitImageItemVO();
        item2.setItemId("item2");
        item2.setName("款式二");
        item2.setTags(Collections.singletonList(new ImageTagVO("标签3", 0)));
        item2.setUrls(Collections.singletonList(ImageUrlVO.builder()
                .height(720).width(1280).type(0).url("https://example.com/image2.jpg")
                .thumbPic(null).scale("720x1280").imageBestScale("720x1280").hotNailStyle(null)
                .spritePic(null).build()));
        exhibitItems.add(item2);
        return exhibitItems;
    }
}