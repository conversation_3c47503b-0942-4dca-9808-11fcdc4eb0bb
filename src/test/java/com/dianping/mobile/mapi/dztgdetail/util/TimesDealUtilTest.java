package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.Pair;
import com.dianping.mobile.mapi.dztgdetail.helper.DpDealDetailBuilder2;
import com.dianping.mobile.mapi.dztgdetail.helper.MtDealDetailBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class TimesDealUtilTest {

    /**
     * 测试 dealGroupDTO 为 null 的情况
     */
    @Test
    public void testIsTimesDealDealGroupDTONull() {
        // arrange
        DealGroupDTO dealGroupDTO = null;
        // act
        boolean result = TimesDealUtil.isTimesDeal(dealGroupDTO);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 dealGroupDTO.getBasic() 为 null 的情况
     */
    @Test
    public void testIsTimesDealDealGroupDTOBasicNull() {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setBasic(null);
        // act
        boolean result = TimesDealUtil.isTimesDeal(dealGroupDTO);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 dealGroupDTO.getBasic().getTradeType() 等于 TradeTypeEnum.MULTIPLE_CARD.getCode() 的情况
     */
    @Test
    public void testIsTimesDealTradeTypeEqualsMultipleCard() {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupBasicDTO dealGroupBasicDTO = new DealGroupBasicDTO();
        dealGroupBasicDTO.setTradeType(19);
        dealGroupDTO.setBasic(dealGroupBasicDTO);
        // act
        boolean result = TimesDealUtil.isTimesDeal(dealGroupDTO);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 dealGroupDTO.getBasic().getTradeType() 不等于 TradeTypeEnum.MULTIPLE_CARD.getCode() 的情况
     */
    @Test
    public void testIsTimesDealTradeTypeNotEqualsMultipleCard() {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupBasicDTO dealGroupBasicDTO = new DealGroupBasicDTO();
        dealGroupBasicDTO.setTradeType(2);
        dealGroupDTO.setBasic(dealGroupBasicDTO);
        // act
        boolean result = TimesDealUtil.isTimesDeal(dealGroupDTO);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 dealGroupDTO 为 null 的情况
     */
    @Test
    public void testOnlyVerificationOneDealGroupDTONull() {
        assertFalse(TimesDealUtil.onlyVerificationOne(null));
    }

    /**
     * 测试 dealGroupDTO.getAttrs() 为空集合的情况
     */
    @Test
    public void testOnlyVerificationOneDealGroupDTOEmptyAttrs() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setAttrs(Collections.emptyList());
        boolean b = TimesDealUtil.onlyVerificationOne(dealGroupDTO);
        Assert.assertFalse(b);
    }

    /**
     * 测试 DealAttrHelper.onlyVerificationOne 返回 false 的情况
     */
    @Test
    public void testOnlyVerificationOneDealAttrHelperFalse() {
        DealGroupDTO dealGroupDTO = Mockito.mock(DealGroupDTO.class);
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("single_verification_quantity_desc");
        attrDTO.setValue(Lists.newArrayList("1"));
        dealGroupDTO.setAttrs(Lists.newArrayList(attrDTO));
        boolean b = TimesDealUtil.onlyVerificationOne(dealGroupDTO);
        Assert.assertFalse(b);
    }

    @Test
    public void testParseTimesWhenDealGroupNull() {
        String times = TimesDealUtil.parseTimes(null);
        Assert.assertNull(times);
    }

    @Test
    public void testParseTimesWhenDealGroupNotNull() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        String times = TimesDealUtil.parseTimes(dealGroupDTO);
        Assert.assertNull(times);
    }

    @Test
    public void testParseTimesWhenDealAttrNotEmpty() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<AttrDTO> attrs = Lists.newArrayList();
        AttrDTO e = new AttrDTO();
        e.setName("1");
        e.setValue(Lists.newArrayList(""));
        attrs.add(e);
        dealGroupDTO.setAttrs(attrs);
        String times = TimesDealUtil.parseTimes(dealGroupDTO);
        Assert.assertNull(times);
    }

    @Test
    public void testParseTimesWhenDealAttrError() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<AttrDTO> attrs = Lists.newArrayList();
        AttrDTO e = new AttrDTO();
        e.setName("sys_multi_sale_number");
        e.setValue(Lists.newArrayList("ok"));
        attrs.add(e);
        dealGroupDTO.setAttrs(attrs);
        String times = TimesDealUtil.parseTimes(dealGroupDTO);
        Assert.assertNull(times);
    }

    @Test
    public void testParseTimesWhenDealAttr() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<AttrDTO> attrs = Lists.newArrayList();
        AttrDTO e = new AttrDTO();
        e.setName("sys_multi_sale_number");
        e.setValue(Lists.newArrayList("1"));
        attrs.add(e);
        DealGroupDealDTO deal = new DealGroupDealDTO();
        deal.setAttrs(attrs);
        dealGroupDTO.setDeals(Lists.newArrayList(deal));
        String times = TimesDealUtil.parseTimes(dealGroupDTO);
        Assert.assertNotNull(times);
    }

    @Test
    public void testParseTimesWhenDealAttr2() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupDealDTO deal = new DealGroupDealDTO();
        dealGroupDTO.setDeals(Lists.newArrayList(deal));
        String times = TimesDealUtil.parseTimes(dealGroupDTO);
        Assert.assertNull(times);
    }

    @Test
    public void testAddToMapForTimesDeal() throws Exception {
        DpDealDetailBuilder2 dpDealDetailBuilder2 = new DpDealDetailBuilder2();
        MtDealDetailBuilder mtDealDetailBuilder = new MtDealDetailBuilder();

        Assert.assertNotNull(dpDealDetailBuilder2);
        Assert.assertNotNull(mtDealDetailBuilder);

        dpDealDetailBuilder2.setMap(Maps.newHashMap());
        mtDealDetailBuilder.setMap(Maps.newHashMap());

        dpDealDetailBuilder2.addToMapForTimesDeal(1, "套餐", "每次套餐详情（共3次）", "testHtml");
        mtDealDetailBuilder.addToMapForTimesDeal(1, "套餐", "每次套餐详情（共3次）", "testHtml");
    }

    @Test
    public void testPair() {
        Pair pair = new Pair("团购详情", "每次套餐详情（共3次）", "testHtml", 1, "每次套餐详情（共3次）");
        Assert.assertNotNull(pair);

        pair.setId("id");
        Assert.assertEquals(pair.getId(), "id");


        pair.setID("ID");
        Assert.assertEquals(pair.getID(), "ID");

        pair.setName("name");
        Assert.assertEquals(pair.getName(), "name");

        pair.setType(2);
        Assert.assertEquals(pair.getType(), 2);

        pair.setKey("key");
        Assert.assertEquals(pair.getKey(), "key");
    }

    @Test
    public void testIsMultiTimesCard() {
        Assert.assertTrue(TimesDealUtil.isMultiTimesCard(buildDealCtx()));
    }

    @Test
    public void testGetMultiTimesCardTitle() {
        Assert.assertEquals(TimesDealUtil.getMultiTimesCardTitle(buildDealCtx()), "每次套餐详情（共3次）");
    }

    @Test
    public void test() {
        Assert.assertNotNull(TimesDealUtil.getTimesTitleToHtmlIfNecessary("testHtml", buildDealCtx()));
    }

    private DealCtx buildDealCtx() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setUserAgent("Android");
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);

        DealCtx dealCtx = new DealCtx(envCtx);
        DealGroupDTO dto = new DealGroupDTO();
        dealCtx.setDealGroupDTO(dto);

        DealGroupDealDTO dealDTO = new DealGroupDealDTO();
        dto.setDeals(Collections.singletonList(dealDTO));

        AttrDTO attrDTO = new AttrDTO();
        dealDTO.setAttrs(Collections.singletonList(attrDTO));

        attrDTO.setName("sys_multi_sale_number");
        attrDTO.setValue(Collections.singletonList("3"));

        DealGroupBasicDTO basicDTO = new DealGroupBasicDTO();
        dto.setBasic(basicDTO);

        basicDTO.setTradeType(19);
        return dealCtx;
    }

}
