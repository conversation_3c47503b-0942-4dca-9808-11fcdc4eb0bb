package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * Test cases for {@link QueryCenterProcessor#isEnable}
 */
@RunWith(MockitoJUnitRunner.class)
public class QueryCenterProcessorIsEnableTest {

    private QueryCenterProcessor processor;

    @Mock
    private DealCtx dealCtx;

    @Before
    public void setUp() {
        processor = new QueryCenterProcessor();
    }

    /**
     * Test when ctx.isMt()=true and using ID that should pass grey batch check
     */
    @Test
    public void testIsEnable_MtContext_WithGreyBatchId() throws Throwable {
        // arrange
        when(dealCtx.isMt()).thenReturn(true);
        // ID ending with 25 should be within grey batch ratio
        when(dealCtx.getMtId()).thenReturn(25);
        // act
        boolean result = processor.isEnable(dealCtx);
        // assert
        verify(dealCtx).setUseQueryCenter(result);
        verify(dealCtx).isMt();
        verify(dealCtx).getMtId();
    }

    /**
     * Test when ctx.isMt()=true and using ID that should not pass grey batch check
     */
    @Test
    public void testIsEnable_MtContext_WithNonGreyBatchId() throws Throwable {
        // arrange
        when(dealCtx.isMt()).thenReturn(true);
        // ID ending with 75 should be outside grey batch ratio
        when(dealCtx.getMtId()).thenReturn(75);
        // act
        boolean result = processor.isEnable(dealCtx);
        // assert
        verify(dealCtx).setUseQueryCenter(result);
        verify(dealCtx).isMt();
        verify(dealCtx).getMtId();
    }

    /**
     * Test when ctx.isMt()=false and using ID that should pass grey batch check
     */
    @Test
    public void testIsEnable_DpContext_WithGreyBatchId() throws Throwable {
        // arrange
        when(dealCtx.isMt()).thenReturn(false);
        // ID ending with 25 should be within grey batch ratio
        when(dealCtx.getDpId()).thenReturn(25);
        // act
        boolean result = processor.isEnable(dealCtx);
        // assert
        verify(dealCtx).setUseQueryCenter(result);
        verify(dealCtx).isMt();
        verify(dealCtx).getDpId();
    }

    /**
     * Test when ctx.isMt()=false and using ID that should not pass grey batch check
     */
    @Test
    public void testIsEnable_DpContext_WithNonGreyBatchId() throws Throwable {
        // arrange
        when(dealCtx.isMt()).thenReturn(false);
        // ID ending with 75 should be outside grey batch ratio
        when(dealCtx.getDpId()).thenReturn(75);
        // act
        boolean result = processor.isEnable(dealCtx);
        // assert
        verify(dealCtx).setUseQueryCenter(result);
        verify(dealCtx).isMt();
        verify(dealCtx).getDpId();
    }
}
