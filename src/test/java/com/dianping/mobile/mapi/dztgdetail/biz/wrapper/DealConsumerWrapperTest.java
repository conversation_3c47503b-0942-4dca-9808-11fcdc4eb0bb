package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.clr.content.core.thrift.dto.PlanDTO;
import com.sankuai.clr.content.process.thrift.dto.resp.BatchQueryPlanListRespDTO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.Collections;
import java.util.concurrent.Future;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import org.mockito.InjectMocks;

@RunWith(MockitoJUnitRunner.class)
public class DealConsumerWrapperTest {

    @Mock
    private Future future;

    private DealConsumerWrapper dealConsumerWrapper = new DealConsumerWrapper();

    /**
     * 测试 future 为 null 的情况
     */
    @Test
    public void testGetPlanDTOFutureIsNull() throws Throwable {
        // arrange
        Future future = null;
        // act
        PlanDTO result = dealConsumerWrapper.getPlanDTO(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 future 不为 null，但 getFutureResult 方法抛出异常的情况
     */
    @Test
    public void testGetPlanDTOFutureResultThrowsException() throws Throwable {
        // arrange
        when(future.get()).thenThrow(new InterruptedException());
        // act
        PlanDTO result = dealConsumerWrapper.getPlanDTO(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 future 不为 null，getFutureResult 方法正常返回，但 planList 为 null 的情况
     */
    @Test
    public void testGetPlanDTOPlanListIsNull() throws Throwable {
        // arrange
        BatchQueryPlanListRespDTO batchQueryPlanListRespDTO = mock(BatchQueryPlanListRespDTO.class);
        when(batchQueryPlanListRespDTO.getPlanList()).thenReturn(null);
        when(future.get()).thenReturn(batchQueryPlanListRespDTO);
        // act
        PlanDTO result = dealConsumerWrapper.getPlanDTO(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 future 不为 null，getFutureResult 方法正常返回，planList 不为空的情况
     */
    @Test
    public void testGetPlanDTOPlanListIsNotNull() throws Throwable {
        // arrange
        PlanDTO planDTO = new PlanDTO();
        BatchQueryPlanListRespDTO batchQueryPlanListRespDTO = mock(BatchQueryPlanListRespDTO.class);
        when(batchQueryPlanListRespDTO.getPlanList()).thenReturn(Collections.singletonList(planDTO));
        when(future.get()).thenReturn(batchQueryPlanListRespDTO);
        // act
        PlanDTO result = dealConsumerWrapper.getPlanDTO(future);
        // assert
        assertEquals(planDTO, result);
    }
}
