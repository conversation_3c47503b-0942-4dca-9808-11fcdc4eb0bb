package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.List;
import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class MassageHighlightsV1Processor_GetNewFoodDataBySkuAttrItemTest {

    /**
     * 测试getNewFoodDataBySkuAttrItem方法，当serviceProjectAttrs列表为空时
     */
    @Test
    public void testGetNewFoodDataBySkuAttrItem_EmptyList() throws Throwable {
        // arrange
        List<SkuAttrItemDto> serviceProjectAttrs = new ArrayList<>();
        // act
        String result = MassageHighlightsV1Processor.getNewFoodDataBySkuAttrItem(serviceProjectAttrs);
        // assert
        assertEquals(null, result);
    }

    /**
     * 测试getNewFoodDataBySkuAttrItem方法，当serviceProjectAttrs列表不为空，但没有freeFood属性时
     */
    @Test
    public void testGetNewFoodDataBySkuAttrItem_NoFreeFoodAttr() throws Throwable {
        // arrange
        List<SkuAttrItemDto> serviceProjectAttrs = new ArrayList<>();
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("otherAttr");
        skuAttrItemDto.setAttrValue("otherValue");
        serviceProjectAttrs.add(skuAttrItemDto);
        // act
        String result = MassageHighlightsV1Processor.getNewFoodDataBySkuAttrItem(serviceProjectAttrs);
        // assert
        assertEquals(null, result);
    }

    /**
     * 测试getNewFoodDataBySkuAttrItem方法，当serviceProjectAttrs列表不为空，有freeFood属性，但freeFood的值不在FOOD_NEW_DATA列表中时
     */
    @Test
    public void testGetNewFoodDataBySkuAttrItem_FreeFoodNotInNewData() throws Throwable {
        // arrange
        List<SkuAttrItemDto> serviceProjectAttrs = new ArrayList<>();
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("freeFood");
        skuAttrItemDto.setAttrValue("notInNewData");
        serviceProjectAttrs.add(skuAttrItemDto);
        // act
        String result = MassageHighlightsV1Processor.getNewFoodDataBySkuAttrItem(serviceProjectAttrs);
        // assert
        assertEquals(null, result);
    }

    /**
     * 测试getNewFoodDataBySkuAttrItem方法，当serviceProjectAttrs列表不为空，有freeFood属性，freeFood的值在FOOD_NEW_DATA列表中，但Fruit属性的值为空时
     */
    @Test
    public void testGetNewFoodDataBySkuAttrItem_FreeFoodInNewDataButFruitIsNull() throws Throwable {
        // arrange
        List<SkuAttrItemDto> serviceProjectAttrs = new ArrayList<>();
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("freeFood");
        skuAttrItemDto.setAttrValue("茶点水果");
        serviceProjectAttrs.add(skuAttrItemDto);
        // act
        String result = MassageHighlightsV1Processor.getNewFoodDataBySkuAttrItem(serviceProjectAttrs);
        // assert
        assertEquals("茶点", result);
    }

    /**
     * 测试getNewFoodDataBySkuAttrItem方法，当serviceProjectAttrs列表不为空，有freeFood属性，freeFood的值在FOOD_NEW_DATA列表中，Fruit属性的值不为空时
     */
    @Test
    public void testGetNewFoodDataBySkuAttrItem_FreeFoodInNewDataAndFruitIsNotNull() throws Throwable {
        // arrange
        List<SkuAttrItemDto> serviceProjectAttrs = new ArrayList<>();
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("freeFood");
        skuAttrItemDto.setAttrValue("茶点水果");
        serviceProjectAttrs.add(skuAttrItemDto);
        SkuAttrItemDto fruitAttrItemDto = new SkuAttrItemDto();
        fruitAttrItemDto.setAttrName("Fruit");
        fruitAttrItemDto.setAttrValue("水果");
        serviceProjectAttrs.add(fruitAttrItemDto);
        // act
        String result = MassageHighlightsV1Processor.getNewFoodDataBySkuAttrItem(serviceProjectAttrs);
        // assert
        assertEquals("茶点水果", result);
    }
}
