package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealBasicDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DealUtilsTest {

    @Mock
    private DealCtx ctx;
    @Mock
    private DealGroupCategoryDTO dealGroupCategoryDTO;


    /**
     * 测试DealGroupDTO为null时，返回空字符串
     */
    @Test
    public void testGetDealGroupServiceType_DealGroupIsNull() {
        when(ctx.getDealGroupDTO()).thenReturn(null);

        String result = DealUtils.getDealGroupServiceType(ctx);

        assertEquals(StringUtils.EMPTY, result);
    }

    /**
     * 测试DealGroupCategoryDTO为null时，返回空字符串
     */
    @Test
    public void testGetDealGroupServiceType_DealGroupCategoryIsNull() {
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(null);

        String result = DealUtils.getDealGroupServiceType(ctx);

        assertEquals(StringUtils.EMPTY, result);
    }

    /**
     * 测试DealGroupCategoryDTO的serviceType为非空时，返回该serviceType
     */
    @Test
    public void testGetDealGroupServiceType_ServiceTypeIsNotNull() {
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(dealGroupCategoryDTO);
        when(dealGroupCategoryDTO.getServiceType()).thenReturn("TestServiceType");

        String result = DealUtils.getDealGroupServiceType(ctx);

        assertEquals("TestServiceType", result);
    }

    /**
     * 测试DealGroupCategoryDTO的serviceType为空时，返回空字符串
     */
    @Test
    public void testGetDealGroupServiceType_ServiceTypeIsNull() {
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(dealGroupCategoryDTO);
        when(dealGroupCategoryDTO.getServiceType()).thenReturn(null);

        String result = DealUtils.getDealGroupServiceType(ctx);

        assertEquals(StringUtils.EMPTY, result);
    }

    /**
     * 测试 DealCtx 为 null 的情况
     */
    @Test
    public void testGetDealGroupServiceTypeIdWithNullDealCtx() {
        // arrange
        DealCtx ctx = new DealCtx(null);
        ctx.setDealGroupDTO(null);

        // act
        Long result = DealUtils.getDealGroupServiceTypeId(ctx);

        // assert
        assertEquals(Long.valueOf(0L), result);
    }

    /**
     * 测试 DealGroupDTO 为 null 的情况
     */
    @Test
    public void testGetDealGroupServiceTypeIdWithNullDealGroupDTO() {
        // arrange
        DealCtx ctx = Mockito.mock(DealCtx.class);
        Mockito.when(ctx.getDealGroupDTO()).thenReturn(null);

        // act
        Long result = DealUtils.getDealGroupServiceTypeId(ctx);

        // assert
        assertEquals(Long.valueOf(0L), result);
    }

    /**
     * 测试 DealGroupCategoryDTO 为 null 的情况
     */
    @Test
    public void testGetDealGroupServiceTypeIdWithNullDealGroupCategoryDTO() {
        // arrange
        DealCtx ctx = Mockito.mock(DealCtx.class);
        DealGroupDTO dealGroupDTO = Mockito.mock(DealGroupDTO.class);
        Mockito.when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        Mockito.when(dealGroupDTO.getCategory()).thenReturn(null);

        // act
        Long result = DealUtils.getDealGroupServiceTypeId(ctx);

        // assert
        assertEquals(Long.valueOf(0L), result);
    }

    /**
     * 测试 serviceTypeId 为 null 的情况
     */
    @Test
    public void testGetDealGroupServiceTypeIdWithNullServiceTypeId() {
        // arrange
        DealCtx ctx = Mockito.mock(DealCtx.class);
        DealGroupDTO dealGroupDTO = Mockito.mock(DealGroupDTO.class);
        DealGroupCategoryDTO categoryDTO = Mockito.mock(DealGroupCategoryDTO.class);
        Mockito.when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        Mockito.when(dealGroupDTO.getCategory()).thenReturn(categoryDTO);
        Mockito.when(categoryDTO.getServiceTypeId()).thenReturn(null);

        // act
        Long result = DealUtils.getDealGroupServiceTypeId(ctx);

        // assert
        assertEquals(Long.valueOf(0L), result);
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testGetDealGroupServiceTypeIdWithNormalCase() {
        // arrange
        DealCtx ctx = Mockito.mock(DealCtx.class);
        DealGroupDTO dealGroupDTO = Mockito.mock(DealGroupDTO.class);
        DealGroupCategoryDTO categoryDTO = Mockito.mock(DealGroupCategoryDTO.class);
        Mockito.when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        Mockito.when(dealGroupDTO.getCategory()).thenReturn(categoryDTO);
        Mockito.when(categoryDTO.getServiceTypeId()).thenReturn(12345L);

        // act
        Long result = DealUtils.getDealGroupServiceTypeId(ctx);

        // assert
        assertEquals(Long.valueOf(12345L), result);
    }

    /**
     * 测试 isNewWearableNailDeal 方法，当 dealGroupDTO 为 null 时
     */
    @Test
    public void testIsNewWearableNailDealWithNullDealGroupDTO() {
        DealGroupDTO dealGroupDTO = null;
        assertFalse("dealGroupDTO 为 null 时应返回 false", DealUtils.isNewWearableNailDeal(dealGroupDTO));
    }

    /**
     * 测试 isNewWearableNailDeal 方法，当 categoryDTO 为 null 时
     */
    @Test
    public void testIsNewWearableNailDealWithNullCategoryDTO() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setCategory(null);
        assertFalse("categoryDTO 为 null 时应返回 false", DealUtils.isNewWearableNailDeal(dealGroupDTO));
    }

    /**
     * 测试 isNewWearableNailDeal 方法，当 categoryId 和 serviceType 匹配，但不包含指定属性时
     */
    @Test
    public void testIsNewWearableNailDealWithMatchingCategoryButNoAttr() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(502L);
        categoryDTO.setServiceType("穿戴甲");
        dealGroupDTO.setCategory(categoryDTO);
        dealGroupDTO.setAttrs(new ArrayList<>());
        assertFalse("categoryId 和 serviceType 匹配，但不包含指定属性时应返回 false", DealUtils.isNewWearableNailDeal(dealGroupDTO));
    }

    /**
     * 测试 isNewWearableNailDeal 方法，当 categoryId 和 serviceType 匹配，且包含指定属性时
     */
    @Test
    public void testIsNewWearableNailDealWithMatchingCategoryAndAttr() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(502L);
        categoryDTO.setServiceType("穿戴甲");
        dealGroupDTO.setCategory(categoryDTO);

        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attrDTO = Mockito.mock(AttrDTO.class);
        Mockito.when(attrDTO.getName()).thenReturn("tag_unifyProduct");
        attrs.add(attrDTO);

        dealGroupDTO.setAttrs(attrs);
        assertTrue("categoryId 和 serviceType 匹配，且包含指定属性时应返回 true", DealUtils.isNewWearableNailDeal(dealGroupDTO));
    }

    /**
     * 测试 isNewWearableNailDeal 方法，当 categoryId 不匹配时
     */
    @Test
    public void testIsNewWearableNailDealWithNonMatchingCategoryId() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(501L);
        categoryDTO.setServiceType("穿戴甲");
        dealGroupDTO.setCategory(categoryDTO);
        assertFalse("categoryId 不匹配时应返回 false", DealUtils.isNewWearableNailDeal(dealGroupDTO));
    }

    /**
     * 测试 isNewWearableNailDeal 方法，当 serviceType 不匹配时
     */
    @Test
    public void testIsNewWearableNailDealWithNonMatchingServiceType() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(502L);
        categoryDTO.setServiceType("非穿戴甲");
        dealGroupDTO.setCategory(categoryDTO);
        assertFalse("serviceType 不匹配时应返回 false", DealUtils.isNewWearableNailDeal(dealGroupDTO));
    }

    /**
<<<<<<< HEAD
     * 测试findFirstAttrValue方法，当attrs列表为空时，应返回null
     */
    @Test
    public void testFindFirstAttrValueWhenAttrsIsNull() {
        assertNull(DealUtils.findFirstAttrValue(null, "key"));
    }

    /**
     * 测试findFirstAttrValue方法，当attrs列表不为空，但没有name等于key的AttributeDTO对象时，应返回null
     */
    @Test
    public void testFindFirstAttrValueWhenNoMatchedAttr() {
        AttributeDTO attr = new AttributeDTO();
        attr.setName("otherKey");
        attr.setValue(Arrays.asList("value1", "value2"));
        assertNull(DealUtils.findFirstAttrValue(Collections.singletonList(attr), "key"));
    }

    /**
     * 测试findFirstAttrValue方法，当attrs列表不为空，且有name等于key的AttributeDTO对象，但value列表为空时，应返回null
     */
    @Test
    public void testFindFirstAttrValueWhenValueIsNull() {
        AttributeDTO attr = new AttributeDTO();
        attr.setName("key");
        attr.setValue(null);
        assertNull(DealUtils.findFirstAttrValue(Collections.singletonList(attr), "key"));
    }

    /**
     * 测试findFirstAttrValue方法，当attrs列表不为空，且有name等于key的AttributeDTO对象，且value列表不为空时，应返回value列表的第一个元素
     */
    @Test
    public void testFindFirstAttrValueWhenValueIsNotNull() {
        AttributeDTO attr = new AttributeDTO();
        attr.setName("key");
        attr.setValue(Arrays.asList("value1", "value2"));
        assertEquals("value1", DealUtils.findFirstAttrValue(Collections.singletonList(attr), "key"));
    }

    /**
     * 测试 filterOfflineSku 方法，当 deal 为 null 时，应返回 false
     */
    @Test
    public void testFilterOfflineSkuDealIsNull() {
        // arrange
        DealGroupDealDTO deal = null;
        // act
        boolean result = DealUtils.filterOfflineSku(deal);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 filterOfflineSku 方法，当 deal.basic 为 null 时，应返回 false
     */
    @Test
    public void testFilterOfflineSkuBasicIsNull() {
        // arrange
        DealGroupDealDTO deal = new DealGroupDealDTO();
        deal.setBasic(null);
        // act
        boolean result = DealUtils.filterOfflineSku(deal);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 filterOfflineSku 方法，当 deal.basic.status 不等于 1 时，应返回 false
     */
    @Test
    public void testFilterOfflineSkuStatusNotEqualOne() {
        // arrange
        DealGroupDealDTO deal = new DealGroupDealDTO();
        DealBasicDTO basic = new DealBasicDTO();
        basic.setStatus(0);
        deal.setBasic(basic);
        // act
        boolean result = DealUtils.filterOfflineSku(deal);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 filterOfflineSku 方法，当 deal.basic.status 等于 1 时，应返回 true
     */
    @Test
    public void testFilterOfflineSkuStatusEqualOne() {
        // arrange
        DealGroupDealDTO deal = new DealGroupDealDTO();
        DealBasicDTO basic = new DealBasicDTO();
        basic.setStatus(1);
        deal.setBasic(basic);
        // act
        boolean result = DealUtils.filterOfflineSku(deal);
        // assert
        assertTrue(result);
    }

    /**
     * 测试预览团单，来源为商家预览
     */
    @Test
    public void testIsPreviewDeal_WithMerchantPreviewSource() {
        Mockito.when(ctx.getRequestSource()).thenReturn(RequestSourceEnum.MERCHANT_PREVIEW.getSource());
        assertTrue(DealUtils.isPreviewDeal(ctx));
    }

    /**
     * 测试非预览团单，来源不为商家预览
     */
    @Test
    public void testIsPreviewDeal_WithNonMerchantPreviewSource() {
        Mockito.when(ctx.getRequestSource()).thenReturn("OTHER_SOURCE");
        assertFalse(DealUtils.isPreviewDeal(ctx));
    }

    /**
     * 测试预览团单，来源为空
     */
    @Test
    public void testIsPreviewDeal_WithNullSource() {
        Mockito.when(ctx.getRequestSource()).thenReturn(null);
        assertFalse(DealUtils.isPreviewDeal(ctx));
    }

    public static boolean isHotDeal(List<AttrDTO> attrDTOS) {
        return Optional.ofNullable(attrDTOS).orElse(Lists.newArrayList()).stream()
                .anyMatch(attrDTO -> Objects.equals(attrDTO.getName(), "topPerformingProduct"));
    }

    @Test
    public void testIsHotDeal_WithTopPerformingProduct_ReturnsTrue() throws Throwable {
        // arrange
        AttrDTO hotDealAttr = new AttrDTO();
        hotDealAttr.setName("topPerformingProduct");
        List<AttrDTO> attrDTOS = Collections.singletonList(hotDealAttr);
        // act
        boolean result = DealUtils.isHotDeal(attrDTOS);
        // assert
        assertTrue(result);
    }

    @Test
    public void testIsHotDeal_WithMultipleAttributesIncludingTopPerforming_ReturnsTrue() throws Throwable {
        // arrange
        AttrDTO regularAttr = new AttrDTO();
        regularAttr.setName("regularAttribute");
        AttrDTO hotDealAttr = new AttrDTO();
        hotDealAttr.setName("topPerformingProduct");
        AttrDTO anotherAttr = new AttrDTO();
        anotherAttr.setName("anotherAttribute");
        List<AttrDTO> attrDTOS = Arrays.asList(regularAttr, hotDealAttr, anotherAttr);
        // act
        boolean result = DealUtils.isHotDeal(attrDTOS);
        // assert
        assertTrue(result);
    }

    @Test
    public void testIsHotDeal_WithoutTopPerformingProduct_ReturnsFalse() throws Throwable {
        // arrange
        AttrDTO attr1 = new AttrDTO();
        attr1.setName("attribute1");
        AttrDTO attr2 = new AttrDTO();
        attr2.setName("attribute2");
        List<AttrDTO> attrDTOS = Arrays.asList(attr1, attr2);
        // act
        boolean result = DealUtils.isHotDeal(attrDTOS);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsHotDeal_WithEmptyList_ReturnsFalse() throws Throwable {
        // arrange
        List<AttrDTO> attrDTOS = new ArrayList<>();
        // act
        boolean result = DealUtils.isHotDeal(attrDTOS);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsHotDeal_WithNullList_ReturnsFalse() throws Throwable {
        // arrange
        List<AttrDTO> attrDTOS = null;
        // act
        boolean result = DealUtils.isHotDeal(attrDTOS);
        // assert
        assertFalse(result);
    }

    @Test(expected = NullPointerException.class)
    public void testIsHotDeal_WithNullElement_ThrowsException() throws Throwable {
        // arrange
        AttrDTO attr = new AttrDTO();
        attr.setName("regularAttribute");
        List<AttrDTO> attrDTOS = Arrays.asList(attr, null);
        // act
        DealUtils.isHotDeal(attrDTOS);
        // assert
        // Expecting NullPointerException
    }

    @Test
    public void testIsHotDeal_WithSimilarButNotExactAttributeName_ReturnsFalse() throws Throwable {
        // arrange
        AttrDTO attr1 = new AttrDTO();
        // Case difference
        attr1.setName("TopPerformingProduct");
        AttrDTO attr2 = new AttrDTO();
        // No camel case
        attr2.setName("topperformingproduct");
        AttrDTO attr3 = new AttrDTO();
        // Underscore
        attr3.setName("top_performing_product");
        List<AttrDTO> attrDTOS = Arrays.asList(attr1, attr2, attr3);
        // act
        boolean result = DealUtils.isHotDeal(attrDTOS);
        // assert
        assertFalse(result);
    }
}
