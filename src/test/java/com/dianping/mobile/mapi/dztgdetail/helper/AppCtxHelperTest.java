package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class AppCtxHelperTest {

    @Mock
    private IMobileContext appCtx;

    /**
     * 测试 appCtx 为 null 的情况
     */
    @Test
    public void testIsMeituanClientAppCtxIsNull() {
        assertTrue(AppCtxHelper.isMeituanClient(null));
    }

    /**
     * 测试 appCtx 的 appId 为 396 的情况
     */
    @Test
    public void testIsMeituanClientAppIdIs396() {
        when(appCtx.getAppId()).thenReturn(396);
        assertTrue(AppCtxHelper.isMeituanClient(appCtx));
    }

    /**
     * 测试 appCtx 的 appId 不为 396，但 isMeituanClient 返回 true 的情况
     */
    @Test
    public void testIsMeituanClientIsMeituanClientReturnsTrue() {
        when(appCtx.getAppId()).thenReturn(397);
        when(appCtx.isMeituanClient()).thenReturn(true);
        assertTrue(AppCtxHelper.isMeituanClient(appCtx));
    }

    /**
     * 测试 appCtx 的 appId 不为 396，且 isMeituanClient 返回 false 的情况
     */
    @Test
    public void testIsMeituanClientIsMeituanClientReturnsFalse() {
        when(appCtx.getAppId()).thenReturn(397);
        when(appCtx.isMeituanClient()).thenReturn(false);
        assertFalse(AppCtxHelper.isMeituanClient(appCtx));
    }

    /**
     * 测试 appCtx 为 null 的情况
     */
    @Test
    public void testGetAppClientTypeWhenAppCtxIsNull() {
        int result = AppCtxHelper.getAppClientType(null);
        assertEquals(0, result);
    }

    /**
     * 测试 appCtx 为美团客户端且为 iOS 系统的场景
     */
    @Test
    public void testGetAppClientTypeWhenIsMeituanClientAndIOS() {
        when(appCtx.isMeituanClient()).thenReturn(true);
        when(appCtx.isIOS()).thenReturn(true);
        int result = AppCtxHelper.getAppClientType(appCtx);
        assertEquals(ClientTypeEnum.mt_mainApp_ios.getType(), result);
    }

    /**
     * 测试 appCtx 为美团客户端且为 Android 系统的场景
     */
    @Test
    public void testGetAppClientTypeWhenIsMeituanClientAndAndroid() {
        when(appCtx.isMeituanClient()).thenReturn(true);
        when(appCtx.isIOS()).thenReturn(false);
        int result = AppCtxHelper.getAppClientType(appCtx);
        assertEquals(ClientTypeEnum.mt_mainApp_android.getType(), result);
    }

    /**
     * 测试 appCtx 不为美团客户端且为 iOS 系统的场景
     */
    @Test
    public void testGetAppClientTypeWhenNotMeituanClientAndIOS() {
        when(appCtx.isMeituanClient()).thenReturn(false);
        when(appCtx.isIOS()).thenReturn(true);
        int result = AppCtxHelper.getAppClientType(appCtx);
        assertEquals(ClientTypeEnum.dp_mainApp_ios.getType(), result);
    }

    /**
     * 测试 appCtx 不为美团客户端且为 Android 系统的场景
     */
    @Test
    public void testGetAppClientTypeWhenNotMeituanClientAndAndroid() {
        when(appCtx.isMeituanClient()).thenReturn(false);
        when(appCtx.isIOS()).thenReturn(false);
        int result = AppCtxHelper.getAppClientType(appCtx);
        assertEquals(ClientTypeEnum.dp_mainApp_android.getType(), result);
    }
}
