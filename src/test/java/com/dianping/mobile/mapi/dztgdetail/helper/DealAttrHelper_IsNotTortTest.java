package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.Collections;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class DealAttrHelper_IsNotTortTest {

    /**
     * 测试 isNotTort 方法，当 attrs 列表为空时，应返回 true
     */
    @Test
    public void testIsNotTortWhenAttrsIsNull() throws Throwable {
        // arrange
        // act
        boolean result = DealAttrHelper.isNotTort(null);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 isNotTort 方法，当 attrs 列表不为空，但不包含 DealAttrKeys.TORT 属性时，应返回 true
     */
    @Test
    public void testIsNotTortWhenAttrsNotContainsTort() throws Throwable {
        // arrange
        AttributeDTO attr = new AttributeDTO();
        attr.setName("other");
        attr.setValue(Arrays.asList("value"));
        // act
        boolean result = DealAttrHelper.isNotTort(Collections.singletonList(attr));
        // assert
        assertTrue(result);
    }

    /**
     * 测试 isNotTort 方法，当 attrs 列表包含 DealAttrKeys.TORT 属性，但属性的值不包含 DealAttrKeys.TORT_VALUE 时，应返回 true
     */
    @Test
    public void testIsNotTortWhenAttrsContainsTortButNotContainsValue() throws Throwable {
        // arrange
        AttributeDTO attr = new AttributeDTO();
        attr.setName(DealAttrKeys.TORT);
        attr.setValue(Arrays.asList("value"));
        // act
        boolean result = DealAttrHelper.isNotTort(Collections.singletonList(attr));
        // assert
        assertTrue(result);
    }

    /**
     * 测试 isNotTort 方法，当 attrs 列表包含 DealAttrKeys.TORT 属性，并且属性的值包含 DealAttrKeys.TORT_VALUE 时，应返回 false
     */
    @Test
    public void testIsNotTortWhenAttrsContainsTortAndContainsValue() throws Throwable {
        // arrange
        AttributeDTO attr = new AttributeDTO();
        attr.setName(DealAttrKeys.TORT);
        attr.setValue(Arrays.asList(DealAttrKeys.TORT_VALUE));
        // act
        boolean result = DealAttrHelper.isNotTort(Collections.singletonList(attr));
        // assert
        assertFalse(result);
    }
}
