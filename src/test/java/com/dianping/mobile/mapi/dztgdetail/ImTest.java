/*
package com.dianping.mobile.mapi.dztgdetail;

import com.dianping.dzim.common.enums.ClientTypeEnum;
import com.dianping.lion.shade.org.apache.http.util.Asserts;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DzImWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.sankuai.general.product.query.center.client.builder.model.*;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.enums.AttrSubjectEnum;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import org.apache.curator.shaded.com.google.common.collect.Sets;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.concurrent.Future;

*/
/**
 * Created by yangquan02 on 19/12/23.
 *//*

public class ImTest extends GenericTest {

    @Autowired
    private QueryCenterWrapper queryCenterWrapper;

    @Resource
    private DzImWrapper dzImWrapper;

    @Test
    public void testIm() throws Exception {
        Future future = dzImWrapper.preOnlineConsultUrl(17661020, 400062537, ClientTypeEnum.mt_mainApp_ios.getType());
        String imUrl = dzImWrapper.getOnlineConsultUrl(future);
        Asserts.notBlank(imUrl, "imUrl");
    }

    @Test
    public void test() {
        List<String> keys = Arrays.asList("px_suitable_age", "suitable_crowd", "checkup_sex","px_additional_service","physical_examination_get_result_time");
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet((long) 420678408), IdTypeEnum.DP)
                .dealGroupId(DealGroupIdBuilder.builder().all())
                .basicInfo(DealGroupBasicInfoBuilder.builder().all())
                .attrsByKey(AttrSubjectEnum.DEAL_GROUP, new HashSet<>(keys))
                .detail(DealGroupDetailBuilder.builder().all())
                .dealBasicInfo(DealBasicInfoBuilder.builder().all())
                .category(DealGroupCategoryBuilder.builder().all())
                .dealGroupTag(DealGroupTagBuilder.builder().all())
                .build();
        Future singleDealGroupDtoFuture = queryCenterWrapper.preDealGroupDTO(queryByDealGroupIdRequest);
    }
}
*/
