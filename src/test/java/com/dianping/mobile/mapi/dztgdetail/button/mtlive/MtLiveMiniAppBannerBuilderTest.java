package com.dianping.mobile.mapi.dztgdetail.button.mtlive;

import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MtLiveSaleStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

@RunWith(MockitoJUnitRunner.class)
public class MtLiveMiniAppBannerBuilderTest {
    @InjectMocks
    private MtLiveMiniAppBannerBuilder bannerBuilder;

    @Mock
    private ButtonBuilderChain chain;

    /**
     * 测试构建横幅，当上下文不是美团直播小程序时，不进行任何操作
     */
    @Test
    public void testBuildBanner_NotMtLiveMinApp() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx dealCtx = new DealCtx(envCtx);
        // act
        bannerBuilder.build(dealCtx, chain);
        assertNull(dealCtx.getBuyBar().getBuyBanner());
    }

    /**
     * 测试构建横幅，当团购信息为空时，不进行任何操作
     */
    @Test
    public void testBuildBanner_DealGroupDTOIsNull() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_LIVE_WEIXIN_MINIAPP);
        DealCtx dealCtx = new DealCtx(envCtx);
        dealCtx.setDealGroupDTO(null);
        // act
        bannerBuilder.build(dealCtx, chain);
        // assert
        assertNull(dealCtx.getBuyBar().getBuyBanner());
    }

    /**
     * 测试构建横幅，当当前时间在开始销售时间之前时，设置即将开始的横幅
     */
    @Test
    public void testBuildBanner_BeforeBeginSaleDate() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_LIVE_WEIXIN_MINIAPP);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupBasicDTO dealGroupBasicDTO = new DealGroupBasicDTO();
        LocalDate tomorrow = LocalDate.now().plusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedDateTime = tomorrow.atStartOfDay().format(formatter);
        dealGroupBasicDTO.setBeginSaleDate(formattedDateTime);
        dealGroupDTO.setBasic(dealGroupBasicDTO);
        DealCtx dealCtx = new DealCtx(envCtx);
        dealCtx.setDealGroupDTO(dealGroupDTO);

        // act
        bannerBuilder.build(dealCtx, chain);

        // assert
        assertEquals(dealCtx.getMtLiveSaleStatusEnum(), MtLiveSaleStatusEnum.COMING_SOON);
    }
}
