/*
package com.dianping.mobile.mapi.dztgdetail;

import com.dianping.deal.attribute.service.DealGroupAttributeGetService;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.deal.sales.common.datatype.KeyParam;
import com.dianping.deal.sales.common.datatype.SpuSale;
import com.dianping.deal.volume.query.api.AccumSaleQueryService;
import com.dianping.gmkt.activity.api.enums.PriceStrengthTimeEnum;
import com.dianping.json.facade.JsonFacade;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealBuilderProcessor;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.PreSaleCountDownReq;
import com.dianping.mobile.mapi.dztgdetail.facade.DealPreSaleQueryFacade;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.util.Date;

*/
/**
 * @Author: huqi
 * @Date: 2020/3/12 4:31 下午
 *//*

public class PreSaleTest extends GenericTest {

    @Autowired
    DealPreSaleQueryFacade dealPreSaleQueryFacade;

    @Autowired
    @Qualifier("dealGroupAttributeGetServiceFuture")
    private DealGroupAttributeGetService dealGroupAttributeGetServiceFuture;
    @Resource(name = "accumulateSaleQueryServiceFuture")
    private AccumSaleQueryService accumulateSaleQueryServiceFuture;
    @Resource
    private DealBuilderProcessor dealBuilderProcessor;
    @Test
    public void test(){
        PreSaleCountDownReq request = new PreSaleCountDownReq();
        request.setDealgroupid(402833220L);
        request.setCityid(1);
        EnvCtx ctx = new EnvCtx();
        ctx.setClientType(ClientTypeEnum.dp_mainApp_ios.getType());
        System.out.println(JsonFacade.serialize(dealPreSaleQueryFacade.queryPreSaleCountDown(request,ctx)));
    }
    @Test
    public void loadAttr(){
        System.out.println(JsonFacade.serialize(dealGroupAttributeGetServiceFuture.
                loadDealAttribute(401316816, Lists.newArrayList(DealAttrKeys.PRE_SALE_TAG))));
    }
    @Test
    public void sale(){
        SpuSale groupSale = accumulateSaleQueryServiceFuture.getBySpuProduct(
                new KeyParam(401300217,0,0,0),100, 200, 0);
        System.out.println(JsonFacade.serialize(groupSale));
    }

    @Test
    public void minPrice(){
//        System.out.println(dealBuilderProcessor.isInWhite(401));
        System.out.println(dealBuilderProcessor.queryPriceAbility(401633110, PriceStrengthTimeEnum.SIXTY_DAY,new Date(1590940800000L)));
    }


}
*/
