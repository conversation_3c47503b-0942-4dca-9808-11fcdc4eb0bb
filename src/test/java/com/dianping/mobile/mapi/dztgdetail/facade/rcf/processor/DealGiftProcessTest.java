package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.DealGiftAdaptor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PlayCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.common.model.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.DealGift;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteResponse;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DealGiftProcessTest {

    @Mock
    private DealCtx ctx;

    private PlayExecuteResponse playExecuteResponse;

    @Mock
    private PlayCenterWrapper playCenterWrapper;

    @InjectMocks
    private DealGiftAdaptor dealGiftAdaptor;

    private MockedStatic<Lion> lionMocked;

    @Before
    public void setUp() {
//        MockitoAnnotations.initMocks(this) 跟 @RunWith(MockitoJUnitRunner.class)等价
        String result = "{\n" +
                "    \"1020940683\": [\n" +
                "        {\n" +
                "            \"activityName\": \"联合营销联调001\",\n" +
                "            \"activityToken\": \"KxyH9Pg61INfJYm8LojVpuUSK\",\n" +
                "            \"startTime\": 1713801600000,\n" +
                "            \"endTime\": 1717171199000,\n" +
                "            \"status\": 3,\n" +
                "            \"playInfo\": {\n" +
                "                \"continueTime\": \"72000\",\n" +
                "                \"taskInfoList\": [\n" +
                "                    {\n" +
                "                        \"taskToken\": \"qO0mk8wjNRweIbmDQaWk\",\n" +
                "                        \"status\": 0,\n" +
                "                        \"targetValue\": \"1\",\n" +
                "                        \"progressValue\": \"0\",\n" +
                "                        \"eventType\": 17,\n" +
                "                        \"prizeCount\": 2,\n" +
                "                        \"prizeInfoList\": [\n" +
                "                            {\n" +
                "                                \"prizeAmount\": \"1800\",\n" +
                "                                \"prizeMinConsume\": \"0\",\n" +
                "                                \"prizeType\": 2,\n" +
                "                                \"prizeImage\": \"https://p0.meituan.net/bizoperate/054fdd190166638a275be62158899164151491.png\",\n" +
                "                                \"prizeName\": \"奖品001\"\n" +
                "                            },\n" +
                "                            {\n" +
                "                                \"prizeAmount\": \"1800\",\n" +
                "                                \"prizeMinConsume\": \"0\",\n" +
                "                                \"prizeType\": 2,\n" +
                "                                \"prizeImage\": \"https://p0.meituan.net/bizoperate/054fdd190166638a275be62158899164151491.png\",\n" +
                "                                \"prizeName\": \"奖品001\"\n" +
                "                            }\n" +
                "                        ],\n" +
                "                        \"materialInfoList\": [\n" +
                "                            {\n" +
                "                                \"id\": 18787,\n" +
                "                                \"activityId\": 28914,\n" +
                "                                \"materialId\": 10215580,\n" +
                "                                \"fieldType\": \"TEXT\",\n" +
                "                                \"fieldKey\": \"activityLink\",\n" +
                "                                \"fieldValue\": \"https://123.sankuai.com/\",\n" +
                "                                \"status\": \"EFFECTIVE\",\n" +
                "                                \"addTime\": 1713856111000,\n" +
                "                                \"modTime\": 1713856111000,\n" +
                "                                \"actionType\": \"TASK\",\n" +
                "                                \"actionId\": \"10215574\"\n" +
                "                            },\n" +
                "                            {\n" +
                "                                \"id\": 18788,\n" +
                "                                \"activityId\": 28914,\n" +
                "                                \"materialId\": 10215581,\n" +
                "                                \"fieldType\": \"TEXT\",\n" +
                "                                \"fieldKey\": \"activityUseLink\",\n" +
                "                                \"fieldValue\": \"imeituan://www.meituan.com/food/deal?did=417728990\",\n" +
                "                                \"status\": \"EFFECTIVE\",\n" +
                "                                \"addTime\": 1713856111000,\n" +
                "                                \"modTime\": 1713856111000,\n" +
                "                                \"actionType\": \"TASK\",\n" +
                "                                \"actionId\": \"10215574\"\n" +
                "                            }\n" +
                "                        ]\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"joinTime\": \"1714039503000\",\n" +
                "                \"activityName\": \"联合营销联调001\",\n" +
                "                \"activityToken\": \"KxyH9Pg61INfJYm8LojVpuUSK\",\n" +
                "                \"title\": \"联合营销联调001\",\n" +
                "                \"joinStatus\": 1,\n" +
                "                \"materialInfoList\": [\n" +
                "                    {\n" +
                "                        \"id\": 18785,\n" +
                "                        \"activityId\": 28914,\n" +
                "                        \"materialId\": 10215578,\n" +
                "                        \"fieldType\": \"SIMPLE_RICH_TEXT\",\n" +
                "                        \"fieldKey\": \"activityListBar\",\n" +
                "                        \"fieldValue\": \"[{\\\"context\\\":\\\"111\\\",\\\"color\\\":\\\"#000000\\\"}]\",\n" +
                "                        \"status\": \"EFFECTIVE\",\n" +
                "                        \"addTime\": 1713856111000,\n" +
                "                        \"modTime\": 1713856111000,\n" +
                "                        \"actionId\": \"\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 18786,\n" +
                "                        \"activityId\": 28914,\n" +
                "                        \"materialId\": 10215579,\n" +
                "                        \"fieldType\": \"TEXT\",\n" +
                "                        \"fieldKey\": \"activityRuleDesc\",\n" +
                "                        \"fieldValue\": \"222\",\n" +
                "                        \"status\": \"EFFECTIVE\",\n" +
                "                        \"addTime\": 1713856111000,\n" +
                "                        \"modTime\": 1713856111000,\n" +
                "                        \"actionId\": \"\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 18787,\n" +
                "                        \"activityId\": 28914,\n" +
                "                        \"materialId\": 10215580,\n" +
                "                        \"fieldType\": \"TEXT\",\n" +
                "                        \"fieldKey\": \"activityLink\",\n" +
                "                        \"fieldValue\": \"https://123.sankuai.com/\",\n" +
                "                        \"status\": \"EFFECTIVE\",\n" +
                "                        \"addTime\": 1713856111000,\n" +
                "                        \"modTime\": 1713856111000,\n" +
                "                        \"actionType\": \"TASK\",\n" +
                "                        \"actionId\": \"10215574\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 18788,\n" +
                "                        \"activityId\": 28914,\n" +
                "                        \"materialId\": 10215581,\n" +
                "                        \"fieldType\": \"TEXT\",\n" +
                "                        \"fieldKey\": \"activityUseLink\",\n" +
                "                        \"fieldValue\": \"imeituan://www.meituan.com/food/deal?did=417728990\",\n" +
                "                        \"status\": \"EFFECTIVE\",\n" +
                "                        \"addTime\": 1713856111000,\n" +
                "                        \"modTime\": 1713856111000,\n" +
                "                        \"actionType\": \"TASK\",\n" +
                "                        \"actionId\": \"10215574\"\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"currentTime\": 1714050227509,\n" +
                "                \"activityId\": 28914,\n" +
                "                \"subTitle\": \"活动副标题\",\n" +
                "                \"sceneProductType\": \"23\",\n" +
                "                \"startTime\": 1713801600000,\n" +
                "                \"endTime\": 1717171199000\n" +
                "            }\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        playExecuteResponse  = new PlayExecuteResponse();
        playExecuteResponse.setResult(result);
        lionMocked = mockStatic(Lion.class);
    }

    @After
    public void teardown() {
        lionMocked.close();
    }

    @Test
    public void testIsEnable() {
        int categoryId = 1;
        List<Integer> categoryIds = new ArrayList<>();
        categoryIds.add(1);
        when(ctx.getCategoryId()).thenReturn(categoryId);
        lionMocked.when(() -> Lion.getList(anyString(), anyString(), any())).thenReturn(categoryIds);
        boolean result = dealGiftAdaptor.isEnable(ctx);
        assertTrue(result);
    }

    /**
     * 测试prepare方法，source来自于特团
     */
    @Test
    public void testPrepare_FromCostEffective() {
        // arrange
        MockedStatic<Environment> envMockedStatic = mockStatic(Environment.class);
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setMtId(123);
        ctx.setMtLongShopId(456L);
        ctx.getEnvCtx().setClientType(1);
        ctx.getEnvCtx().setMtUserId(789L);
        ctx.setRequestSource(RequestSourceEnum.COST_EFFECTIVE.getSource());
        Map<String, Long> map = Maps.newHashMap();
        map.put("surpriseGiftPlayId", 1000000000544001L);
        map.put("summerPlayId", 1000000000728008L);
        lionMocked.when(() -> Lion.getMap(LionConstants.APP_KEY, LionConstants.DEAL_GIFT_PLAY_ID_CONFIG, Long.class)).thenReturn(map);
        envMockedStatic.when(() -> Environment.isProductEnv()).thenReturn(false);
        when(playCenterWrapper.queryExecutePlay(ctx, 1000000000728008L)).thenReturn(null);
        when(playCenterWrapper.querySceneExecutePlay(ctx, 1000000000544001L)).thenReturn(null);

        // act
        dealGiftAdaptor.prepare(ctx);

        //assert
        // 暑促活动查询接口和惊喜买赠查询接口均调用1次
        verify(playCenterWrapper, times(1)).queryExecutePlay(ctx, 1000000000728008L);
        verify(playCenterWrapper, times(1)).querySceneExecutePlay(ctx, 1000000000544001L);
        envMockedStatic.close();
    }

    /**
     * 测试prepare方法，source来自于非特团
     */
    @Test
    public void testPrepare_NotFromCostEffective() {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setMtId(123);
        ctx.setMtLongShopId(456L);
        ctx.getEnvCtx().setClientType(1);
        ctx.getEnvCtx().setMtUserId(789L);
        Map<String, Long> map = Maps.newHashMap();
        map.put("surpriseGiftPlayId", 1000000000544001L);
        lionMocked.when(() -> Lion.getMap(LionConstants.APP_KEY, LionConstants.DEAL_GIFT_PLAY_ID_CONFIG, Long.class)).thenReturn(map);
        when(playCenterWrapper.querySceneExecutePlay(ctx, 1000000000544001L)).thenReturn(null);

        // act
        dealGiftAdaptor.prepare(ctx);

        //assert
        // 暑促活动查询接口和惊喜买赠查询接口均调用1次
        verify(playCenterWrapper, times(1)).querySceneExecutePlay(ctx, 1000000000544001L);
    }

    @Test
    public void testProcess_BothPlayActivityNotExist() throws Exception {
        Future playFuture = mock(Future.class);
        Future playActivityFuture = mock(Future.class);
        DealCtx ctx = new DealCtx(new EnvCtx());
        FutureCtx futureCtx = new FutureCtx();
        ctx.setFutureCtx(futureCtx);
        futureCtx.setPlayActivityFuture(playActivityFuture);
        futureCtx.setPlayFuture(playFuture);
        when(playActivityFuture.get(anyLong(), any())).thenReturn(null);
        when(playFuture.get(anyLong(), any())).thenReturn(null);
        when(playCenterWrapper.validSummerPlayExist(any(), any())).thenReturn(false);
//        when(playCenterWrapper.buildGiftsForNewCustomActivity(null)).thenReturn(new ArrayList<>());
        dealGiftAdaptor.process(ctx);
        assertNull(ctx.getDealGifts());
    }


    @Test
    public void testBuildGifts() {
        List<PlayActivityModel> playActivityModels = new ArrayList<>();
        PlayActivityModel playActivityModel = new PlayActivityModel();
        PlayInfoModel playInfoModel = new PlayInfoModel();
        TaskInfoModel taskInfoModel = new TaskInfoModel();
        List<PrizeInfoModel> prizeInfoModels = new ArrayList<>();
        PrizeInfoModel prizeInfoModel = new PrizeInfoModel();
        prizeInfoModels.add(prizeInfoModel);
        taskInfoModel.setPrizeInfoList(prizeInfoModels);
        List<TaskInfoModel> taskInfoModels = new ArrayList<>();
        taskInfoModels.add(taskInfoModel);
        playInfoModel.setTaskInfoList(taskInfoModels);
        playInfoModel.setActivityId(1223L);
        playActivityModel.setPlayInfo(playInfoModel);
        playActivityModels.add(playActivityModel);

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<AttrDTO> attrs = Lists.newArrayList();
        AttrDTO e = new AttrDTO();
        e.setName("sys_multi_sale_number");
        e.setValue(Lists.newArrayList("1"));
        attrs.add(e);
        DealGroupDealDTO deal = new DealGroupDealDTO();
        deal.setAttrs(attrs);
        dealGroupDTO.setDeals(Lists.newArrayList(deal));

        List<DealGift> result = dealGiftAdaptor.buildGifts(playActivityModels);
        assertNotNull(result);
        assertEquals(1, result.size());
    }

}