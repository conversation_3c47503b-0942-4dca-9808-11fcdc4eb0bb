package com.dianping.mobile.mapi.dztgdetail.facade.rcf.flash;

import com.dianping.deal.style.DealDetailStyleFlashService;
import com.dianping.deal.style.dto.flash.DealDetailStyleQueryRequest;
import com.dianping.mobile.mapi.dztgdetail.biz.service.cellarservice.DealModuleDetailCacheBizService;
import com.dianping.mobile.mapi.dztgdetail.biz.service.cellarservice.DealStyleCacheBizService;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FlashDealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FlashFutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealFlashReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.struct.DealDetailStructModuleDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.DealModuleDetailVO;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.flash.DealModuleDetailFlashProcessor;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @create 2024/10/17 17:08
 */
@RunWith(MockitoJUnitRunner.class)
public class DealModuleDetailFlashProcessorTest {

    @InjectMocks
    private DealModuleDetailFlashProcessor dealModuleDetailFlashProcessor;
    @Mock
    private DealModuleDetailCacheBizService dealModuleDetailCacheBizService;
    @Mock
    private DealDetailStyleFlashService dealDetailStyleFlashServiceSync;

    @Mock
    DealStyleCacheBizService dealStyleCacheBizService;
    @Mock
    DealDetailStyleFlashService dealDetailStyleFlashService;
    FlashDealCtx ctx = new FlashDealCtx(new EnvCtx());

    @Before
    public void setUp() throws Exception {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO category = new DealGroupCategoryDTO();
        category.setCategoryId(2L);
        dealGroupDTO.setCategory(category);
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        ctx.setDeviceHeight(22);
        ctx.setRequestSource("dsf");
        ctx.getEnvCtx().setVersion("32132");
        ctx.setFutureCtx(new FlashFutureCtx());
    }

    @Test
    public void prepareTest() {
        // 从缓存获取 定制样式团购详情
        DealFlashReq req = new DealFlashReq();
        Future future = Mockito.mock(Future.class);
//        Mockito.when(dealModuleDetailCacheBizService.buildKey(req)).thenReturn("key");
//        Mockito.when(dealModuleDetailCacheBizService.getCacheValueFuture("key")).thenReturn(future);
        dealModuleDetailFlashProcessor.prepare(ctx);
        Assert.assertNotNull(future);
    }

    @Test
    public void processTest() {
        DealDetailStructModuleDo result = new DealDetailStructModuleDo();
        Future future = Mockito.mock(Future.class);
//        Mockito.when(dealModuleDetailCacheBizService.getCacheValueResult(future)).thenReturn(result) ;
        DealDetailStyleQueryRequest request = new DealDetailStyleQueryRequest();
//        Mockito.when(dealDetailStyleFlashService.query(request)).thenReturn(null);
        dealModuleDetailFlashProcessor.process(ctx);
        Assert.assertNotNull(future);
    }

    @Test
    public void processTest1() {
        DealModuleDetailVO result = dealModuleDetailCacheBizService.getCacheValue("key");
        Assert.assertNull(result);
    }
}
