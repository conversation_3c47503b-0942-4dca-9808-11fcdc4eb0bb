package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.pay.promo.display.api.PromoDisplayService;
import com.dianping.pay.promo.display.api.dto.BatchQueryPromoDisplayRequest;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.concurrent.Future;
import com.dianping.pay.promo.rule.api.dto.Response;
import org.mockito.MockitoAnnotations;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PromoWrapper_BatchPrePromoDisplayDTOTest {

    @InjectMocks
    private PromoWrapper promoWrapper;

    @Mock
    private PromoDisplayService promoDisplayServiceFuture;

    /**
     * Tests the batchPrePromoDisplayDTO method under exception conditions.
     * This test verifies that when the PromoDisplayService throws a RuntimeException,
     * the batchPrePromoDisplayDTO method catches this exception and returns null.
     */
    @Test
    public void testBatchPrePromoDisplayDTOException() throws Throwable {
        // Arrange
        BatchQueryPromoDisplayRequest request = new BatchQueryPromoDisplayRequest();
        when(promoDisplayServiceFuture.batchQueryPromoDisplayDTO(request)).thenThrow(new RuntimeException());
        // Act
        Future result = promoWrapper.batchPrePromoDisplayDTO(request);
        // Assert
        assertNull("The result should be null when an exception occurs", result);
    }
}
