package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEvent;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.IExaminerAbstractHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.glasses.AdultGlassesHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.glasses.ChildGlassesHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.glasses.OnlyOptometryHandler;
import java.util.Map;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class GlassesHighlightV2Processor_OnApplicationEventTest {

    @InjectMocks
    private GlassesHighlightV2Processor glassesHighlightV2Processor;

    @Mock
    private ApplicationContext applicationContext;

    @Before
    public void setUp() {
        when(applicationContext.getBean(AdultGlassesHandler.class)).thenReturn(mock(AdultGlassesHandler.class));
        when(applicationContext.getBean(ChildGlassesHandler.class)).thenReturn(mock(ChildGlassesHandler.class));
        when(applicationContext.getBean(OnlyOptometryHandler.class)).thenReturn(mock(OnlyOptometryHandler.class));
    }

    /**
     * 测试容器启动时，应该将三种类型的眼镜处理器放入 GLASSES_SERVER_TYPE_HANDLER_MAP 这个 Map 中
     */
    @Test
    @Ignore
    public void testOnApplicationEvent() {
        // arrange
        ApplicationEvent applicationEvent = mock(ApplicationEvent.class);
        // act
        glassesHighlightV2Processor.onApplicationEvent(applicationEvent);
        // assert
        // 由于 GLASSES_SERVER_TYPE_HANDLER_MAP 是静态的，我们无法直接访问它来进行断言，所以这里无法添加断言
    }
}
