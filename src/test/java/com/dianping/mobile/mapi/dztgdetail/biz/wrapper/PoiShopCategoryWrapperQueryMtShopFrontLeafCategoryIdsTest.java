package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiShopCategoryWrapper;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.service.MtPoiService;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class PoiShopCategoryWrapperQueryMtShopFrontLeafCategoryIdsTest {

    @InjectMocks
    private PoiShopCategoryWrapper poiShopCategoryWrapper;

    @Mock
    private MtPoiService mtPoiService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testQueryMtShopFrontLeafCategoryIdsWhenPoiMapIsEmpty() throws Exception {
        when(mtPoiService.findPoisById(anyList(), anyList())).thenReturn(Collections.emptyMap());
        List<Integer> result = poiShopCategoryWrapper.queryMtShopFrontLeafCategoryIds(1L);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryMtShopFrontLeafCategoryIdsWhenMtPoiDTOIsNull() throws Exception {
        Map<Long, MtPoiDTO> poiMap = new HashMap<>();
        poiMap.put(1L, null);
        when(mtPoiService.findPoisById(anyList(), anyList())).thenReturn(poiMap);
        List<Integer> result = poiShopCategoryWrapper.queryMtShopFrontLeafCategoryIds(1L);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryMtShopFrontLeafCategoryIdsWhenMtFrontCateIdsIsNull() throws Exception {
        Map<Long, MtPoiDTO> poiMap = new HashMap<>();
        MtPoiDTO mtPoiDTO = new MtPoiDTO();
        mtPoiDTO.setMtFrontCateIds(null);
        poiMap.put(1L, mtPoiDTO);
        when(mtPoiService.findPoisById(anyList(), anyList())).thenReturn(poiMap);
        List<Integer> result = poiShopCategoryWrapper.queryMtShopFrontLeafCategoryIds(1L);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryMtShopFrontLeafCategoryIdsWhenMtFrontCateIdsIsNotNull() throws Exception {
        Map<Long, MtPoiDTO> poiMap = new HashMap<>();
        MtPoiDTO mtPoiDTO = new MtPoiDTO();
        mtPoiDTO.setMtFrontCateIds(Collections.singletonList(1));
        poiMap.put(1L, mtPoiDTO);
        when(mtPoiService.findPoisById(anyList(), anyList())).thenReturn(poiMap);
        List<Integer> result = poiShopCategoryWrapper.queryMtShopFrontLeafCategoryIds(1L);
        assertEquals(Collections.singletonList(1), result);
    }

    @Test
    public void testQueryMtShopFrontLeafCategoryIdsWhenExceptionOccurs() throws Exception {
        when(mtPoiService.findPoisById(anyList(), anyList())).thenThrow(new RuntimeException());
        List<Integer> result = poiShopCategoryWrapper.queryMtShopFrontLeafCategoryIds(1L);
        assertEquals(Collections.emptyList(), result);
    }
}
