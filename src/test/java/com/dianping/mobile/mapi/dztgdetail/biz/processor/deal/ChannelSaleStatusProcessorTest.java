package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.SaleChannelAggregationDTO;
import com.sankuai.general.product.query.center.client.dto.SaleChannelDTO;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

@Ignore
public class ChannelSaleStatusProcessorTest {

    private ChannelSaleStatusProcessor channelSaleStatusProcessor = new ChannelSaleStatusProcessor();

    @Test
    public void testProcessCheckSaleChannelLive() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setDealGroupDTO(TestUtils.createDealGroupDTOWithSaleChannels(10003L));
        // act
        channelSaleStatusProcessor.process(ctx);
        // assert
        assertTrue(ctx.getMLiveChannel());
    }

    @Test
    public void testProcessCheckSaleChannelNonLive() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setDealGroupDTO(TestUtils.createDealGroupDTOWithSaleChannels(10001L, 10002L));
        // act
        channelSaleStatusProcessor.process(ctx);
        // assert
        assertFalse(ctx.getMLiveChannel());
    }

    // ... 其他测试用例保持不变
    @Test
    public void testProcessCheckSaleChannelNoChannels() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setDealGroupDTO(TestUtils.createDealGroupDTOWithSaleChannels());
        // act
        channelSaleStatusProcessor.process(ctx);
        // assert
        assertFalse(ctx.getMLiveChannel());
    }

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        channelSaleStatusProcessor = new ChannelSaleStatusProcessor();
    }

    /**
     * 测试 isEnable 方法，期望返回 true
     */
    @Test
    public void testIsEnable() throws Throwable {
        // arrange
        ChannelSaleStatusProcessor processor = new ChannelSaleStatusProcessor();
        DealCtx ctx = new DealCtx(null);
        // act
        boolean result = processor.isEnable(ctx);
        // assert
        assertTrue(result);
    }
}

class TestUtils {

    static DealGroupDTO createDealGroupDTOWithSaleChannels(Long... channelNos) {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        SaleChannelAggregationDTO saleChannelAggregationDTO = new SaleChannelAggregationDTO();
        List<SaleChannelDTO> supportChannels = new ArrayList<>();
        for (Long channelNo : channelNos) {
            SaleChannelDTO saleChannelDTO = new SaleChannelDTO();
            saleChannelDTO.setChannelNo(channelNo);
            supportChannels.add(saleChannelDTO);
        }
        saleChannelAggregationDTO.setSupportChannels(supportChannels);
        dealGroupDTO.setSaleChannelAggregation(saleChannelAggregationDTO);
        return dealGroupDTO;
    }
}
