package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.sankuai.dzcard.navigation.api.DzCardPromoService;
import com.sankuai.dzcard.navigation.api.dto.FindQualifyEventIdsReqDTO;
import com.sankuai.dzcard.navigation.api.dto.FindQualifyEventIdsRespDTO;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import java.util.concurrent.Future;
import java.util.concurrent.CompletableFuture;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DzCardPromoWrapper_PrepareTest {

    @InjectMocks
    private DzCardPromoWrapper dzCardPromoWrapper;

    @Mock
    private DzCardPromoService cardPromoService;

    private FindQualifyEventIdsRespDTO respDTO;

    private Future<FindQualifyEventIdsRespDTO> futureRespDTO;

    private MockedStatic<FutureFactory> mockedFutureFactory;

    @Before
    public void setUp() {
        respDTO = new FindQualifyEventIdsRespDTO();
        futureRespDTO = CompletableFuture.completedFuture(respDTO);
        mockedFutureFactory = mockStatic(FutureFactory.class);
        mockedFutureFactory.when(() -> FutureFactory.getFuture(FindQualifyEventIdsRespDTO.class)).thenReturn(futureRespDTO);
    }

    @After
    public void tearDown() {
        mockedFutureFactory.close();
    }

    /**
     * 测试prepareV2方法，当isMt为true时
     */
    @Test
    public void testPrepareV2WhenIsMtIsTrue() throws Throwable {
        // arrange
        long shopId = 1L;
        int dealGroupId = 1;
        long userId = 1L;
        boolean isMt = true;
        String unionId = "unionId";
        String pageSource = "pageSource";
        EnvCtx envCtx = new EnvCtx();
        envCtx.setUnionId(unionId);
        when(cardPromoService.findQualifyEventIdsByProductIds(any(FindQualifyEventIdsReqDTO.class))).thenReturn(respDTO);
        // act
        Future<FindQualifyEventIdsRespDTO> result = dzCardPromoWrapper.prepareV2(shopId, dealGroupId, userId, isMt, envCtx, pageSource);
        // assert
        verify(cardPromoService, times(1)).findQualifyEventIdsByProductIds(any(FindQualifyEventIdsReqDTO.class));
        assertEquals(respDTO, result.get());
    }

    /**
     * 测试prepareV2方法，当isMt为false时
     */
    @Test
    public void testPrepareV2WhenIsMtIsFalse() throws Throwable {
        // arrange
        long shopId = 1L;
        int dealGroupId = 1;
        long userId = 1L;
        boolean isMt = false;
        String unionId = "unionId";
        String pageSource = "pageSource";
        EnvCtx envCtx = new EnvCtx();
        envCtx.setUnionId(unionId);
        when(cardPromoService.findQualifyEventIdsByProductIds(any(FindQualifyEventIdsReqDTO.class))).thenReturn(respDTO);
        // act
        Future<FindQualifyEventIdsRespDTO> result = dzCardPromoWrapper.prepareV2(shopId, dealGroupId, userId, isMt, envCtx, pageSource);
        // assert
        verify(cardPromoService, times(1)).findQualifyEventIdsByProductIds(any(FindQualifyEventIdsReqDTO.class));
        assertEquals(respDTO, result.get());
    }

    /**
     * 测试prepareV2方法，当pageSource为LIVE.getStyleName()时
     */
    @Test
    public void testPrepareV2WhenPageSourceIsLive() throws Throwable {
        // arrange
        long shopId = 1L;
        int dealGroupId = 1;
        long userId = 1L;
        boolean isMt = true;
        String unionId = "unionId";
        String pageSource = "LIVE";
        EnvCtx envCtx = new EnvCtx();
        envCtx.setUnionId(unionId);
        when(cardPromoService.findQualifyEventIdsByProductIds(any(FindQualifyEventIdsReqDTO.class))).thenReturn(respDTO);
        // act
        Future<FindQualifyEventIdsRespDTO> result = dzCardPromoWrapper.prepareV2(shopId, dealGroupId, userId, isMt, envCtx, pageSource);
        // assert
        verify(cardPromoService, times(1)).findQualifyEventIdsByProductIds(any(FindQualifyEventIdsReqDTO.class));
        assertEquals(respDTO, result.get());
    }

    /**
     * 测试prepareV2方法，当pageSource不为LIVE.getStyleName()时
     */
    @Test
    public void testPrepareV2WhenPageSourceIsNotLive() throws Throwable {
        // arrange
        long shopId = 1L;
        int dealGroupId = 1;
        long userId = 1L;
        boolean isMt = true;
        String unionId = "unionId";
        String pageSource = "notLive";
        EnvCtx envCtx = new EnvCtx();
        envCtx.setUnionId(unionId);
        when(cardPromoService.findQualifyEventIdsByProductIds(any(FindQualifyEventIdsReqDTO.class))).thenReturn(respDTO);
        // act
        Future<FindQualifyEventIdsRespDTO> result = dzCardPromoWrapper.prepareV2(shopId, dealGroupId, userId, isMt, envCtx, pageSource);
        // assert
        verify(cardPromoService, times(1)).findQualifyEventIdsByProductIds(any(FindQualifyEventIdsReqDTO.class));
        assertEquals(respDTO, result.get());
    }
}
