package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.feature.model.FeatureDetailDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.feature.model.PreOrderFeatureConfigDTO;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ShopCategoryEnum;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.biz.Response;
import com.sankuai.feitianplus.data.onedata.api.thrift.domain.QueryResultSync;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.zdc.tag.apply.api.PoiTagDisplayRPCService;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import com.sankuai.zdc.tag.apply.dto.FindSceneDisplayTagRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ShopTagWrapperTest {

    @Mock
    private PoiTagDisplayRPCService poiTagDisplayRPCServiceFuture;

    @Mock
    private PoiTagDisplayRPCService poiTagDisplayRPCService;

    @InjectMocks
    private ShopTagWrapper shopTagWrapper;

    private MockedStatic<FutureFactory> factoryMockedStatic;

    private MockedStatic<LionConfigUtils> lionConfigUtilsMockedStatic;

    @Before
    public void setUp() throws Exception {
        factoryMockedStatic = mockStatic(FutureFactory.class);
        lionConfigUtilsMockedStatic = mockStatic(LionConfigUtils.class);
    }

    @After
    public void tearDown() throws Exception {
        factoryMockedStatic.close();
        lionConfigUtilsMockedStatic.close();
    }

    /**
     * 测试 preGetDpShopTags 方法，当 dpShopId 为 null 时
     */
    @Test
    public void testPreGetDpShopTagsWithNullDpShopId() {
        // arrange
        Long dpShopId = null;

        // act
        Future result = shopTagWrapper.preGetDpShopTags(dpShopId);

        // assert
        assertNull(result);
    }

    /**
     * 测试 preGetDpShopTags 方法，当 dpShopId 小于等于 0 时
     */
    @Test
    public void testPreGetDpShopTagsWithInvalidDpShopId() {
        // arrange
        Long dpShopId = 0L;

        // act
        Future result = shopTagWrapper.preGetDpShopTags(dpShopId);

        // assert
        assertNull(result);
    }

    /**
     * 测试 preGetDpShopTags 方法，当 dpShopId 有效且方法执行成功时
     */
    @Test
    public void testPreGetDpShopTagsWithValidDpShopId() throws Exception {
        // arrange
        Long dpShopId = 1L;
        Future expectedFuture = mock(Future.class);
        factoryMockedStatic.when(() -> FutureFactory.getFuture()).thenReturn(expectedFuture);
        when(poiTagDisplayRPCServiceFuture.findSceneDisplayTagOfDp(any()))
                .thenReturn(null);

        // act
        Future result = shopTagWrapper.preGetDpShopTags(dpShopId);

        // assert
        assertNotNull(result);
        assertEquals(expectedFuture, result);
    }

    /**
     * 测试 getShopId2TagsMap 当 future 为 null 时
     */
    @Test
    public void testGetShopId2TagsMapFutureIsNull() throws Throwable {
        // arrange
        Future future = null;

        // act
        Map<Long, List<DisplayTagDto>> result = shopTagWrapper.getShopId2TagsMap(future);

        // assert
        assertTrue("结果应为空", result.isEmpty());
    }

    /**
     * 测试 getShopId2TagsMap 当 future 返回的 response 为 null 时
     */
    @Test
    public void testGetShopId2TagsMapResponseIsNull() throws Throwable {
        Future futureMock = mock(Future.class);
        // arrange
        when(futureMock.get()).thenReturn(null);

        // act
        Map<Long, List<DisplayTagDto>> result = shopTagWrapper.getShopId2TagsMap(futureMock);

        // assert
        assertTrue("结果应为空", result.isEmpty());
    }

    /**
     * 测试 getShopId2TagsMap 当 future 返回的 response 不成功时
     */
    @Test
    public void testGetShopId2TagsMapResponseIsNotSuccess() throws Throwable {
        // arrange
        Response<Map<Long, List<DisplayTagDto>>> response = new Response<>(100, "失败");

        Future futureMock = mock(Future.class);
        when(futureMock.get()).thenReturn(response);

        // act
        Map<Long, List<DisplayTagDto>> result = shopTagWrapper.getShopId2TagsMap(futureMock);

        // assert
        assertTrue("结果应为空", result.isEmpty());
    }

    /**
     * 测试 getShopId2TagsMap 当 future 返回的 response 成功时
     */
    @Test
    public void testGetShopId2TagsMapResponseIsSuccess() throws Throwable {
        // arrange
        Response<Map<Long, List<DisplayTagDto>>> response = new Response<>(200, "成功", Maps.newHashMap());
        Future futureMock = mock(Future.class);
        when(futureMock.get()).thenReturn(response);

        // act
        Map<Long, List<DisplayTagDto>> result = shopTagWrapper.getShopId2TagsMap(futureMock);

        // assert
        assertNotNull("结果不应为空", result);
    }

    /**
     * 测试dpShopId为null时返回UNKNOWN
     */
    @Test
    public void testGetWearableNailShopTagDpShopIdIsNull() {
        ShopCategoryEnum result = shopTagWrapper.getWearableNailShopTag(null, new HashMap<>());
        assertEquals(ShopCategoryEnum.UNKNOWN, result);
    }

    /**
     * 测试dpShopId小于等于0时返回UNKNOWN
     */
    @Test
    public void testGetWearableNailShopTagDpShopIdIsInvalid() {
        ShopCategoryEnum result = shopTagWrapper.getWearableNailShopTag(-1L, new HashMap<>());
        assertEquals(ShopCategoryEnum.UNKNOWN, result);
    }

    /**
     * 测试dpShopId2TagMap为空时返回UNKNOWN
     */
    @Test
    public void testGetWearableNailShopTagDpShopId2TagMapIsEmpty() {
        ShopCategoryEnum result = shopTagWrapper.getWearableNailShopTag(1L, Collections.emptyMap());
        assertEquals(ShopCategoryEnum.UNKNOWN, result);
    }

    /**
     * 测试dpShopId2TagMap中不包含dpShopId时返回UNKNOWN
     */
    @Test
    public void testGetWearableNailShopTagDpShopIdNotInMap() {
        Map<Long, List<DisplayTagDto>> dpShopId2TagMap = new HashMap<>();
        dpShopId2TagMap.put(2L, Collections.emptyList());
        ShopCategoryEnum result = shopTagWrapper.getWearableNailShopTag(1L, dpShopId2TagMap);
        assertEquals(ShopCategoryEnum.UNKNOWN, result);
    }

    /**
     * 测试匹配到穿戴甲专卖店标签时返回WEARABLE_NAIL_ONLY
     */
    @Test
    public void testGetWearableNailShopTagMatchWearableNailOnly() {
        Map<Long, List<DisplayTagDto>> dpShopId2TagMap = new HashMap<>();
        DisplayTagDto tag = new DisplayTagDto();
        tag.setTagId(20972L);
        dpShopId2TagMap.put(1L, Collections.singletonList(tag));

        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.getWearableNailShopTagIdConfig()).thenReturn(Collections.singletonMap(
                "wearableNailOnly", 20972L));

        ShopCategoryEnum result = shopTagWrapper.getWearableNailShopTag(1L, dpShopId2TagMap);
        assertEquals(ShopCategoryEnum.WEARABLE_NAIL_ONLY, result);
    }

    /**
     * 测试匹配到穿戴甲寄售店标签时返回WEARABLE_NAIL_RETAIL
     */
    @Test
    public void testGetWearableNailShopTagMatchWearableNailRetail() {
        Map<Long, List<DisplayTagDto>> dpShopId2TagMap = new HashMap<>();
        DisplayTagDto tag = new DisplayTagDto();
        tag.setTagId(21082L);
        dpShopId2TagMap.put(1L, Arrays.asList(new DisplayTagDto(), tag)); // 添加多个标签，确保筛选逻辑正确

        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.getWearableNailShopTagIdConfig()).thenReturn(Collections.singletonMap(
                "wearableNailRetail", 21082L));

        ShopCategoryEnum result = shopTagWrapper.getWearableNailShopTag(1L, dpShopId2TagMap);
        assertEquals(ShopCategoryEnum.WEARABLE_NAIL_RETAIL, result);
    }

    /**
     * 测试未匹配到任何标签时返回UNKNOWN
     */
    @Test
    public void testGetWearableNailShopTagNoMatch() {
        Map<Long, List<DisplayTagDto>> dpShopId2TagMap = new HashMap<>();
        DisplayTagDto tag = new DisplayTagDto();
        tag.setTagId(99999L);
        dpShopId2TagMap.put(1L, Collections.singletonList(tag));

        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.getWearableNailShopTagIdConfig()).thenReturn(new HashMap<String,
                Long>() {{
            put("wearableNailOnly", 20972L);
            put("wearableNailRetail", 21082L);
        }});

        ShopCategoryEnum result = shopTagWrapper.getWearableNailShopTag(1L, dpShopId2TagMap);
        assertEquals(ShopCategoryEnum.UNKNOWN, result);
    }

    /**
     * 测试 getWearableNailShopTag 方法，当 dpShopId 为 null
     */
    @Test
    public void testGetWearableNailShopTagWithNullDpShopId() {
        ShopCategoryEnum result = shopTagWrapper.getWearableNailShopTag(null);
        assertEquals(ShopCategoryEnum.UNKNOWN, result);
    }

    /**
     * 测试 getWearableNailShopTag 方法，当 dpShopId 小于等于 0
     */
    @Test
    public void testGetWearableNailShopTagWithInvalidDpShopId() {
        ShopCategoryEnum result = shopTagWrapper.getWearableNailShopTag(-1L);
        assertEquals(ShopCategoryEnum.UNKNOWN, result);
    }

    /**
     * 测试 getWearableNailShopTag 方法，当响应为空
     */
    @Test
    public void testGetWearableNailShopTagWithNullResponse() throws Exception {
        when(poiTagDisplayRPCService.findSceneDisplayTagOfDp(any(FindSceneDisplayTagRequest.class))).thenReturn(null);
        ShopCategoryEnum result = shopTagWrapper.getWearableNailShopTag(1L);
        assertEquals(ShopCategoryEnum.UNKNOWN, result);
    }

    /**
     * 测试 getWearableNailShopTag 方法，当响应失败
     */
    @Test
    public void testGetWearableNailShopTagWithFailedResponse() throws Exception {
        Response<Map<Long, List<DisplayTagDto>>> response = new Response<>(500, "失败", null);
        when(poiTagDisplayRPCService.findSceneDisplayTagOfDp(any(FindSceneDisplayTagRequest.class))).thenReturn(response);
        ShopCategoryEnum result = shopTagWrapper.getWearableNailShopTag(1L);
        assertEquals(ShopCategoryEnum.UNKNOWN, result);
    }

    /**
     * 测试 getWearableNailShopTag 方法，正常情况
     */
    @Test
    public void testGetWearableNailShopTagWithSuccess() throws Exception {
        Map<Long, List<DisplayTagDto>> data = new HashMap<>();
        DisplayTagDto tagDto = new DisplayTagDto();
        tagDto.setTagId(20972L); // 假设这是穿戴甲专卖店的标签ID
        data.put(1L, Collections.singletonList(tagDto));

        Response<Map<Long, List<DisplayTagDto>>> response = new Response<>(200, "成功", data);
        when(poiTagDisplayRPCService.findSceneDisplayTagOfDp(any(FindSceneDisplayTagRequest.class))).thenReturn(response);

        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.getWearableNailShopTagIdConfig()).thenReturn(new HashMap<String,
                Long>() {{
            put("wearableNailOnly", 20972L);
            put("wearableNailRetail", 21082L);
        }});

        ShopCategoryEnum result = shopTagWrapper.getWearableNailShopTag(1L);
        assertEquals(ShopCategoryEnum.WEARABLE_NAIL_ONLY, result);
    }

    /**
     * 测试 getPerformanceGuaranteeTags 方法，ZDC数据与Lion配置数据不存在交集
     */
    @Test
    public void testGetPerformanceGuaranteeTags_NoIntersection() {
        // arrange
        DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
        Map<Long, List<DisplayTagDto>> dpShopId2TagMap = new HashMap<>();
        DisplayTagDto tagDto = new DisplayTagDto();
        tagDto.setTagId(123L);
        DisplayTagDto tagDto2 = new DisplayTagDto();
        tagDto2.setTagId(456L);
        dpShopId2TagMap.put(1L, Arrays.asList(tagDto, tagDto2));

        FeatureDetailDTO featureDetailDTO = new FeatureDetailDTO();
        featureDetailDTO.setId(222L);
        List<FeatureDetailDTO> featureDetailDTOS = Lists.newArrayList(featureDetailDTO);

        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.getPerformanceGuaranteeFeatureList(any()))
                .thenReturn(featureDetailDTOS);

        // act
        List<FeatureDetailDTO> result = shopTagWrapper.getPerformanceGuaranteeTags(dealGroupCategoryDTO, 1L, dpShopId2TagMap);

        // assert
        assertTrue(CollectionUtils.isEmpty(result));
    }

    /**
     * 测试 getPerformanceGuaranteeTagsMap 方法，ZDC数据与Lion配置数据存在交集
     */
    @Test
    public void testGetPerformanceGuaranteeTagsMap_Intersection() {
        // arrange
        DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
        Map<Long, List<DisplayTagDto>> dpShopId2TagMap = new HashMap<>();
        DisplayTagDto tagDto = new DisplayTagDto();
        tagDto.setTagId(123L);
        DisplayTagDto tagDto2 = new DisplayTagDto();
        tagDto2.setTagId(456L);
        dpShopId2TagMap.put(1L, Arrays.asList(tagDto, tagDto2));

        FeatureDetailDTO featureDetailDTO = new FeatureDetailDTO();
        featureDetailDTO.setId(222L);
        FeatureDetailDTO featureDetailDTO2 = new FeatureDetailDTO();
        featureDetailDTO2.setId(456L);
        featureDetailDTO2.setText("测试文本");
        List<FeatureDetailDTO> featureDetailDTOS = Lists.newArrayList(featureDetailDTO, featureDetailDTO2);

        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.getPerformanceGuaranteeFeatureList(any()))
                .thenReturn(featureDetailDTOS);

        // act
        List<FeatureDetailDTO> result = shopTagWrapper.getPerformanceGuaranteeTags(dealGroupCategoryDTO, 1L, dpShopId2TagMap);

        // assert
        assertTrue(result.size() == 1);
        assertTrue(result.get(0).getId().longValue() == 456L);
        assertTrue(result.get(0).getText().equals("测试文本"));
    }

    @Test
    public void testGetPreOrderFeatDetail_WithoutOrderTime() throws Exception {
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.getPreOrderGuaranteeFeatConfig()).thenReturn(getPreOrderFeatureConfigDTO());
        Future<QueryResultSync> futureMock = mock(Future.class);
        when(futureMock.get()).thenReturn(null);
        List<FeatureDetailDTO> result = shopTagWrapper.getPreOrderFeatDetail(futureMock, 1L);
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("预订必上门", result.get(0).getText());
        assertEquals("上门前3小时随时退", result.get(1).getText());
    }

    @Test
    public void testGetPreOrderFeatDetail_thirtyMinute() throws Exception {
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.getPreOrderGuaranteeFeatConfig()).thenReturn(getPreOrderFeatureConfigDTO());
        Future<QueryResultSync> futureMock = mock(Future.class);
        when(futureMock.get()).thenReturn(getQueryResultSync_Thirty());
        List<FeatureDetailDTO> result = shopTagWrapper.getPreOrderFeatDetail(futureMock, *********L);
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("预订必上门", result.get(0).getText());
        assertEquals("上门前3小时随时退", result.get(1).getText());
        assertEquals("预计30分钟接单", result.get(2).getText());
    }

    @Test
    public void testGetPreOrderFeatDetail_FifteenMinute() throws Exception {
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.getPreOrderGuaranteeFeatConfig()).thenReturn(getPreOrderFeatureConfigDTO());
        Future<QueryResultSync> futureMock = mock(Future.class);
        when(futureMock.get()).thenReturn(getQueryResultSync_Fifteen());
        List<FeatureDetailDTO> result = shopTagWrapper.getPreOrderFeatDetail(futureMock, *********L);
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("预订必上门", result.get(0).getText());
        assertEquals("上门前3小时随时退", result.get(1).getText());
        assertEquals("预计15分钟接单", result.get(2).getText());
    }

    @Test
    public void testGetPreOrderFeatDetail_FiveMinute() throws Exception {
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.getPreOrderGuaranteeFeatConfig()).thenReturn(getPreOrderFeatureConfigDTO());
        Future<QueryResultSync> futureMock = mock(Future.class);
        when(futureMock.get()).thenReturn(getQueryResultSync_Five());
        List<FeatureDetailDTO> result = shopTagWrapper.getPreOrderFeatDetail(futureMock, *********L);
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("预订必上门", result.get(0).getText());
        assertEquals("上门前3小时随时退", result.get(1).getText());
        assertEquals("较快确认", result.get(2).getText());
    }


    private PreOrderFeatureConfigDTO getPreOrderFeatureConfigDTO() {
        String featureDetailStr = "\n" +
                "{\"fixedKeys\":[\"预订必上门\",\"上门前3小时随时退\"],\"featureConfigMap\":{\"预订必上门\":{\"text\":\"预订必上门\",\"type\":0,\"layerConfig\":{\"icon\":\"https://p1.meituan.net/travelcube/607179dd73bc9d0b67e3c541329b26f31361.png\",\"title\":\"预订必上门\",\"desc\":\"预订后会安排工作人员上门服务，上门时会主动出示工作证件\"}},\"上门前3小时随时退\":{\"text\":\"上门前3小时随时退\",\"type\":0,\"layerConfig\":{\"icon\":\"https://p1.meituan.net/travelcube/607179dd73bc9d0b67e3c541329b26f31361.png\",\"title\":\"上门前3小时随时退\",\"desc\":\"预订后服务前3小时可随时退款，3小时后需联系商户取消并协商退款\"}},\"较快确认\":{\"text\":\"较快确认\",\"type\":0,\"layerConfig\":{\"icon\":\"https://p1.meituan.net/travelcube/607179dd73bc9d0b67e3c541329b26f31361.png\",\"title\":\"较快确认\",\"desc\":\"近期大多数订单可在5分钟内确认是否预订成功\"}},\"预计15分钟接单\":{\"text\":\"预计15分钟接单\",\"type\":0,\"layerConfig\":{\"icon\":\"https://p1.meituan.net/travelcube/607179dd73bc9d0b67e3c541329b26f31361.png\",\"title\":\"预计15分钟接单\",\"desc\":\"近期大多数订单可在15分钟内确认是否预订成功\"}},\"预计30分钟接单\":{\"text\":\"预计30分钟接单\",\"type\":0,\"layerConfig\":{\"icon\":\"https://p1.meituan.net/travelcube/607179dd73bc9d0b67e3c541329b26f31361.png\",\"title\":\"预计30分钟接单\",\"desc\":\"近期大多数订单可在30分钟内确认是否预订成功\"}}}}";
        return GsonUtils.fromJsonString(featureDetailStr, PreOrderFeatureConfigDTO.class);
    }

    private QueryResultSync getQueryResultSync_Thirty() {
        String json = "{\n" +
                "\t\"ifSuccess\":true,\n" +
                "\t\"code\":100,\n" +
                "\t\"data\":\"[[\\\"*********\\\",\\\"*********\\\",\\\"921\\\"]]\",\n" +
                "\t\"queryResultTitles\":[\n" +
                "\t\t{\n" +
                "\t\t\t\"columnName\":\"dp_shop_id\"\n" +
                "\t\t},\n" +
                "\t\t{\n" +
                "\t\t\t\"columnName\":\"mt_shop_id\"\n" +
                "\t\t},\n" +
                "\t\t{\n" +
                "\t\t\t\"columnName\":\"avg_accept_oder_time\"\n" +
                "\t\t}\n" +
                "\t],\n" +
                "\t\"pageInfo\":{\n" +
                "\t\t\"pageType\":0,\n" +
                "\t\t\"pageNo\":0,\n" +
                "\t\t\"totalPage\":0,\n" +
                "\t\t\"pageSize\":0,\n" +
                "\t\t\"totalRecord\":0\n" +
                "\t},\n" +
                "\t\"errorMsg\":\"成功\",\n" +
                "\t\"queryId\":\"a38b7abc-69d4-4095-XXX\"\n" +
                "}";
        return GsonUtils.fromJsonString(json, QueryResultSync.class);
    }

    private QueryResultSync getQueryResultSync_Fifteen() {
        String json = "{\n" +
                "\t\"ifSuccess\":true,\n" +
                "\t\"code\":100,\n" +
                "\t\"data\":\"[[\\\"*********\\\",\\\"*********\\\",\\\"840\\\"]]\",\n" +
                "\t\"queryResultTitles\":[\n" +
                "\t\t{\n" +
                "\t\t\t\"columnName\":\"dp_shop_id\"\n" +
                "\t\t},\n" +
                "\t\t{\n" +
                "\t\t\t\"columnName\":\"mt_shop_id\"\n" +
                "\t\t},\n" +
                "\t\t{\n" +
                "\t\t\t\"columnName\":\"avg_accept_oder_time\"\n" +
                "\t\t}\n" +
                "\t],\n" +
                "\t\"pageInfo\":{\n" +
                "\t\t\"pageType\":0,\n" +
                "\t\t\"pageNo\":0,\n" +
                "\t\t\"totalPage\":0,\n" +
                "\t\t\"pageSize\":0,\n" +
                "\t\t\"totalRecord\":0\n" +
                "\t},\n" +
                "\t\"errorMsg\":\"成功\",\n" +
                "\t\"queryId\":\"a38b7abc-69d4-4095-XXX\"\n" +
                "}";
        return GsonUtils.fromJsonString(json, QueryResultSync.class);
    }

    private QueryResultSync getQueryResultSync_Five() {
        String json = "{\n" +
                "\t\"ifSuccess\":true,\n" +
                "\t\"code\":100,\n" +
                "\t\"data\":\"[[\\\"*********\\\",\\\"*********\\\",\\\"200\\\"]]\",\n" +
                "\t\"queryResultTitles\":[\n" +
                "\t\t{\n" +
                "\t\t\t\"columnName\":\"dp_shop_id\"\n" +
                "\t\t},\n" +
                "\t\t{\n" +
                "\t\t\t\"columnName\":\"mt_shop_id\"\n" +
                "\t\t},\n" +
                "\t\t{\n" +
                "\t\t\t\"columnName\":\"avg_accept_oder_time\"\n" +
                "\t\t}\n" +
                "\t],\n" +
                "\t\"pageInfo\":{\n" +
                "\t\t\"pageType\":0,\n" +
                "\t\t\"pageNo\":0,\n" +
                "\t\t\"totalPage\":0,\n" +
                "\t\t\"pageSize\":0,\n" +
                "\t\t\"totalRecord\":0\n" +
                "\t},\n" +
                "\t\"errorMsg\":\"成功\",\n" +
                "\t\"queryId\":\"a38b7abc-69d4-4095-XXX\"\n" +
                "}";
        return GsonUtils.fromJsonString(json, QueryResultSync.class);
    }
}
