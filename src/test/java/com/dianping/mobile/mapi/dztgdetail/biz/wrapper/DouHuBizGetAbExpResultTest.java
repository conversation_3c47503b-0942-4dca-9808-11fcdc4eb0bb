package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.sankuai.douhu.absdk.bean.DouHuRequest;
import com.sankuai.douhu.absdk.bean.DouHuResponse;
import com.sankuai.douhu.absdk.client.DouHuClient;
import com.sankuai.douhu.absdk.enums.ErrorCode;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DouHuBizGetAbExpResultTest {

    @Mock
    private DouHuClient douHuClient;

    @InjectMocks
    private DouHuBiz douHuBiz;

    private DealCtx dealCtx;

    private EnvCtx envCtx;

    @Before
    public void setUp() {
        envCtx = new EnvCtx();
        envCtx.setUnionId("test_union_id");
        envCtx.setUuid("test_uuid");
        envCtx.setDpId("test_dpid");
        dealCtx = new DealCtx(envCtx);
    }

    /**
     * Test when module is null, should return null
     */
    @Test
    public void testGetAbExpResultModuleNull() throws Throwable {
        // arrange
        // no setup needed
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResult(dealCtx, null);
        // assert
        assertNull(result);
    }

    /**
     * Test when module has no mapping in Lion config, expId falls back to module
     */
    @Test
    public void testGetAbExpResultModuleNoLionMapping() throws Throwable {
        // arrange
        String module = "test_module";
        DouHuResponse response = new DouHuResponse();
        response.setCode(ErrorCode.SUCCESS.getCode());
        response.setSk("test_sk_b");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResult(dealCtx, module);
        // assert
        assertNotNull(result);
        assertEquals(module, result.getKey());
        assertEquals("b", result.getConfigs().get(0).getExpResult());
    }

    /**
     * Test MT platform request creation and successful response
     */
    @Test
    public void testGetAbExpResultMtPlatformSuccess() throws Throwable {
        // arrange
        String module = "test_module";
        // Create MT platform context
        DealCtx mtDealCtx = new DealCtx(envCtx) {

            @Override
            public boolean isMt() {
                return true;
            }
        };
        DouHuResponse response = new DouHuResponse();
        response.setCode(ErrorCode.SUCCESS.getCode());
        response.setSk("test_sk_c");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResult(mtDealCtx, module);
        // assert
        assertNotNull(result);
        assertEquals(module, result.getKey());
        assertEquals("c", result.getConfigs().get(0).getExpResult());
    }

    /**
     * Test DP platform request creation and successful response
     */
    @Test
    public void testGetAbExpResultDpPlatformSuccess() throws Throwable {
        // arrange
        String module = "test_module";
        // Create DP platform context
        DealCtx dpDealCtx = new DealCtx(envCtx) {

            @Override
            public boolean isMt() {
                return false;
            }
        };
        DouHuResponse response = new DouHuResponse();
        response.setCode(ErrorCode.SUCCESS.getCode());
        response.setSk("test_sk_a");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResult(dpDealCtx, module);
        // assert
        assertNotNull(result);
        assertEquals(module, result.getKey());
        assertEquals("a", result.getConfigs().get(0).getExpResult());
    }

    /**
     * Test when response is null, should return null
     */
    @Test
    public void testGetAbExpResultNullResponse() throws Throwable {
        // arrange
        String module = "test_module";
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(null);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResult(dealCtx, module);
        // assert
        assertNull(result);
    }

    /**
     * Test when response has blank sk, should return null
     */
    @Test
    public void testGetAbExpResultBlankSk() throws Throwable {
        // arrange
        String module = "test_module";
        DouHuResponse response = new DouHuResponse();
        response.setCode(ErrorCode.SUCCESS.getCode());
        response.setSk("");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResult(dealCtx, module);
        // assert
        assertNull(result);
    }

    /**
     * Test when response has error code, should return null
     */
    @Test
    public void testGetAbExpResultErrorCode() throws Throwable {
        // arrange
        String module = "test_module";
        DouHuResponse response = new DouHuResponse();
        response.setCode(ErrorCode.FAIL.getCode());
        response.setSk("test_sk");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResult(dealCtx, module);
        // assert
        assertNull(result);
    }

    /**
     * Test exception handling
     */
    @Test
    public void testGetAbExpResultException() throws Throwable {
        // arrange
        String module = "test_module";
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenThrow(new RuntimeException("test exception"));
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResult(dealCtx, module);
        // assert
        assertNull(result);
    }

    /**
     * Test different sk patterns (a-j)
     */
    @Test
    public void testGetAbExpResultDifferentSkPatterns() throws Throwable {
        // arrange
        String module = "test_module";
        // Test all sk patterns
        String[] suffixes = { "_b", "_c", "_d", "_e", "_f", "_g", "_h", "_i", "_j", "" };
        String[] expected = { "b", "c", "d", "e", "f", "g", "h", "i", "j", "a" };
        for (int i = 0; i < suffixes.length; i++) {
            DouHuResponse response = new DouHuResponse();
            response.setCode(ErrorCode.SUCCESS.getCode());
            response.setSk("test_sk" + suffixes[i]);
            when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
            // act
            ModuleAbConfig result = douHuBiz.getAbExpResult(dealCtx, module);
            // assert
            assertNotNull(result);
            assertEquals(expected[i], result.getConfigs().get(0).getExpResult());
        }
    }
}
