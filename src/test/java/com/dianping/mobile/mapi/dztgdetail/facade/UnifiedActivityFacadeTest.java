package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DzCardPromoWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.SkuWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.TimesCardWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.ActivityCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedActivityModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.ActivityModuleDTO;
import com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper;
import com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil;
import com.dianping.mobile.mapi.dztgdetail.util.PlusNumUtils;
import com.dianping.pigeon.remoting.invoker.config.annotation.Reference;
import com.dianping.tpfun.product.api.sku.pintuan.dto.BestPinTag;
import com.sankuai.dealuser.price.display.api.PriceDisplayService;
import com.sankuai.dealuser.price.display.api.enums.ProductTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.RequestSceneEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PriceRequest;
import com.sankuai.dealuser.price.display.api.model.PriceResponse;
import com.sankuai.dealuser.price.display.api.model.ProductIdentity;
import com.sankuai.dzcard.navigation.api.dto.CardQualifyEventIdDTO;
import com.sankuai.dzcard.navigation.api.enums.QualifyEventTypeEnum;
import com.sankuai.merchantcard.timescard.exposure.dto.CardSummaryBarDTO;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedActivityFacadeTest {

    @InjectMocks
    private UnifiedActivityFacade unifiedActivityFacade;

    @Mock
    private PriceDisplayService priceDisplayService;

    private PriceResponse<PriceDisplayDTO> response;

    private PriceDisplayDTO priceDisplayDTO;

    private ProductIdentity productIdentity;

    @Mock
    private ActivityCtx ctx;

    @Mock
    private EnvCtx envCtx;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private MapperWrapper mapperWrapper;

    @Mock
    private SkuWrapper skuWrapper;

    @Mock
    private TimesCardWrapper timesCardWrapper;

    @Mock
    private DzCardPromoWrapper cardWrapper;

    @Before
    public void setUp() {
        response = mock(PriceResponse.class);
        priceDisplayDTO = mock(PriceDisplayDTO.class);
        productIdentity = mock(ProductIdentity.class);
    }

    private boolean invokeIsPinTuanDaoGua(PriceResponse<PriceDisplayDTO> response, BigDecimal pinTuanPrice) throws Exception {
        Method method = UnifiedActivityFacade.class.getDeclaredMethod("isPinTuanDaoGua", PriceResponse.class, BigDecimal.class);
        method.setAccessible(true);
        return (boolean) method.invoke(unifiedActivityFacade, response, pinTuanPrice);
    }

    private PriceRequest invokePrivateMethod(String methodName, Object... args) throws Exception {
        Method method = UnifiedActivityFacade.class.getDeclaredMethod(methodName, ActivityCtx.class);
        method.setAccessible(true);
        return (PriceRequest) method.invoke(unifiedActivityFacade, args);
    }

    /**
     * Helper method to invoke the private isValidJoyCard method using reflection.
     */
    private boolean invokePrivateIsValidJoyCard(List<CardQualifyEventIdDTO> cards) throws Exception {
        Method method = UnifiedActivityFacade.class.getDeclaredMethod("isValidJoyCard", List.class);
        method.setAccessible(true);
        return (boolean) method.invoke(null, cards);
    }

    private boolean invokePrivateIsValidMemberCard(List<CardQualifyEventIdDTO> cards) throws Exception {
        // Use reflection to access the private method
        Method method = UnifiedActivityFacade.class.getDeclaredMethod("isValidMemberCard", List.class);
        method.setAccessible(true);
        return (boolean) method.invoke(null, cards);
    }

    private boolean invokePrivateIsValidTimesCard(CardSummaryBarDTO timesCard) throws Exception {
        Method method = UnifiedActivityFacade.class.getDeclaredMethod("isValidTimesCard", CardSummaryBarDTO.class);
        method.setAccessible(true);
        return (boolean) method.invoke(null, timesCard);
    }

    @Test
    public void testIsPinTuanDaoGuaResponseIsNull() throws Throwable {
        assertFalse(invokeIsPinTuanDaoGua(null, BigDecimal.ONE));
    }

    @Test
    public void testIsPinTuanDaoGuaResponseIsNotSuccess() throws Throwable {
        when(response.isSuccess()).thenReturn(false);
        assertFalse(invokeIsPinTuanDaoGua(response, BigDecimal.ONE));
    }

    @Test
    public void testIsPinTuanDaoGuaDataIsNull() throws Throwable {
        when(response.isSuccess()).thenReturn(true);
        when(response.getData()).thenReturn(null);
        assertFalse(invokeIsPinTuanDaoGua(response, BigDecimal.ONE));
    }

    @Test
    public void testIsPinTuanDaoGuaPriceIsLessThanPinTuanPrice() throws Throwable {
        when(response.isSuccess()).thenReturn(true);
        when(response.getData()).thenReturn(priceDisplayDTO);
        when(priceDisplayDTO.getPrice()).thenReturn(BigDecimal.ONE);
        // Corrected assertion based on method logic
        assertTrue(invokeIsPinTuanDaoGua(response, BigDecimal.TEN));
    }

    @Test
    public void testIsPinTuanDaoGuaPriceIsGreaterThanOrEqualToPinTuanPrice() throws Throwable {
        when(response.isSuccess()).thenReturn(true);
        when(response.getData()).thenReturn(priceDisplayDTO);
        when(priceDisplayDTO.getPrice()).thenReturn(BigDecimal.TEN);
        // Corrected assertion based on method logic
        assertFalse(invokeIsPinTuanDaoGua(response, BigDecimal.ONE));
    }

    @Test
    public void testBuildPriceRequestMt() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.getClientType()).thenReturn(1);
        when(envCtx.getUnionId()).thenReturn("unionId");
        when(envCtx.getVersion()).thenReturn("version");
        when(ctx.getMtCityId()).thenReturn(1);
        when(ctx.getMtShopIdLong()).thenReturn(1L);
        // act
        PriceRequest result = invokePrivateMethod("buildPriceRequest", ctx);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getClientEnv().getClientType());
        assertEquals("unionId", result.getClientEnv().getUnionId());
        assertEquals("version", result.getClientEnv().getVersion());
    }

    @Test
    public void testBuildPriceRequestDp() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(false);
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.getClientType()).thenReturn(1);
        when(envCtx.getUnionId()).thenReturn("unionId");
        when(envCtx.getVersion()).thenReturn("version");
        when(ctx.getDpCityId()).thenReturn(1);
        when(ctx.getDpShopIdLong()).thenReturn(1L);
        // act
        PriceRequest result = invokePrivateMethod("buildPriceRequest", ctx);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getClientEnv().getClientType());
        assertEquals("unionId", result.getClientEnv().getUnionId());
        assertEquals("version", result.getClientEnv().getVersion());
    }

    @Test
    public void testBuildPriceRequestNullCtx() throws Throwable {
        // arrange
        ActivityCtx ctx = null;
        // act
        try {
            invokePrivateMethod("buildPriceRequest", ctx);
            fail("Expected an InvocationTargetException with NullPointerException cause");
        } catch (Exception e) {
            // assert
            assertTrue(e instanceof java.lang.reflect.InvocationTargetException);
            assertTrue(e.getCause() instanceof NullPointerException);
        }
    }

    /**
     * Tests initActivityCtx method when EnvCtx's isMt method returns true.
     */
    @Test
    public void testInitActivityCtxIsMtTrue() throws Throwable {
        // Arrange
        UnifiedActivityModuleReq request = mock(UnifiedActivityModuleReq.class);
        EnvCtx envCtx = mock(EnvCtx.class);
        when(envCtx.isMt()).thenReturn(true);
        when(request.getDealGroupId()).thenReturn(1);
        when(request.getCityId()).thenReturn(1);
        when(request.getShopIdLong()).thenReturn(1L);
        Future futureMock = mock(Future.class);
        when(dealGroupWrapper.preDpDealGroupId(anyInt())).thenReturn(futureMock);
        when(mapperWrapper.preDpCityByMtCity(anyInt())).thenReturn(futureMock);
        when(mapperWrapper.preDpShopIdByMtShopId(anyLong())).thenReturn(futureMock);
        // Act
        Method method = UnifiedActivityFacade.class.getDeclaredMethod("initActivityCtx", UnifiedActivityModuleReq.class, EnvCtx.class);
        method.setAccessible(true);
        ActivityCtx result = (ActivityCtx) method.invoke(unifiedActivityFacade, request, envCtx);
        // Assert
        assertNotNull(result);
        verify(dealGroupWrapper, times(1)).preDpDealGroupId(anyInt());
        verify(mapperWrapper, times(1)).preDpCityByMtCity(anyInt());
        verify(mapperWrapper, times(1)).preDpShopIdByMtShopId(anyLong());
    }

    /**
     * Tests initActivityCtx method when EnvCtx's isMt method returns false.
     */
    @Test
    public void testInitActivityCtxIsMtFalse() throws Throwable {
        // Arrange
        UnifiedActivityModuleReq request = mock(UnifiedActivityModuleReq.class);
        EnvCtx envCtx = mock(EnvCtx.class);
        when(envCtx.isMt()).thenReturn(false);
        when(request.getDealGroupId()).thenReturn(1);
        when(request.getCityId()).thenReturn(1);
        when(request.getShopIdLong()).thenReturn(1L);
        // Act
        Method method = UnifiedActivityFacade.class.getDeclaredMethod("initActivityCtx", UnifiedActivityModuleReq.class, EnvCtx.class);
        method.setAccessible(true);
        ActivityCtx result = (ActivityCtx) method.invoke(unifiedActivityFacade, request, envCtx);
        // Assert
        assertNotNull(result);
        verify(dealGroupWrapper, never()).preDpDealGroupId(anyInt());
        verify(mapperWrapper, never()).preDpCityByMtCity(anyInt());
        verify(mapperWrapper, never()).preDpShopIdByMtShopId(anyLong());
    }

    /**
     * 测试当 cards 不为 null 且包含 JOY_CARD 类型时，返回 true
     */
    @Test
    public void testIsValidJoyCard_ContainsJoyCard() throws Throwable {
        // arrange
        CardQualifyEventIdDTO joyCard = new CardQualifyEventIdDTO();
        joyCard.setQualifyEventType(QualifyEventTypeEnum.JOY_CARD.getCode());
        List<CardQualifyEventIdDTO> cards = Arrays.asList(joyCard);
        // act
        boolean result = invokePrivateIsValidJoyCard(cards);
        // assert
        assertTrue(result);
    }

    /**
     * 测试当 cards 不为 null 但不包含 JOY_CARD 类型时，返回 false
     */
    @Test
    public void testIsValidJoyCard_DoesNotContainJoyCard() throws Throwable {
        // arrange
        CardQualifyEventIdDTO otherCard = new CardQualifyEventIdDTO();
        otherCard.setQualifyEventType(QualifyEventTypeEnum.DISCOUNT_CARD.getCode());
        List<CardQualifyEventIdDTO> cards = Arrays.asList(otherCard);
        // act
        boolean result = invokePrivateIsValidJoyCard(cards);
        // assert
        assertFalse(result);
    }

    /**
     * 测试当 cards 为 null 时，返回 false
     */
    @Test
    public void testIsValidJoyCard_NullCards() throws Throwable {
        // arrange
        List<CardQualifyEventIdDTO> cards = null;
        // act
        boolean result = invokePrivateIsValidJoyCard(cards);
        // assert
        assertFalse(result);
    }

    /**
     * 测试当 cards 为空列表时，返回 false
     */
    @Test
    public void testIsValidJoyCard_EmptyCards() throws Throwable {
        // arrange
        List<CardQualifyEventIdDTO> cards = Collections.emptyList();
        // act
        boolean result = invokePrivateIsValidJoyCard(cards);
        // assert
        assertFalse(result);
    }

    /**
     * 测试当 cards 中的 CardQualifyEventIdDTO 对象的 qualifyEventType 为 null 时，返回 false
     */
    @Test
    public void testIsValidJoyCard_NullQualifyEventType() throws Throwable {
        // arrange
        CardQualifyEventIdDTO nullTypeCard = new CardQualifyEventIdDTO();
        // Since qualifyEventType is an int, we cannot set it to null. Instead, we can set it to a default value.
        // Assuming 0 is an invalid or default value
        nullTypeCard.setQualifyEventType(0);
        List<CardQualifyEventIdDTO> cards = Arrays.asList(nullTypeCard);
        // act
        boolean result = invokePrivateIsValidJoyCard(cards);
        // assert
        assertFalse(result);
    }

    /**
     * 测试当 cards 为 null 时，方法应返回 false
     */
    @Test
    public void testIsValidMemberCardWhenCardsIsNull() throws Throwable {
        // arrange
        List<CardQualifyEventIdDTO> cards = null;
        // act
        boolean result = invokePrivateIsValidMemberCard(cards);
        // assert
        assertFalse(result);
    }

    /**
     * 测试当 cards 为空列表时，方法应返回 false
     */
    @Test
    public void testIsValidMemberCardWhenCardsIsEmpty() throws Throwable {
        // arrange
        List<CardQualifyEventIdDTO> cards = Collections.emptyList();
        // act
        boolean result = invokePrivateIsValidMemberCard(cards);
        // assert
        assertFalse(result);
    }

    /**
     * 测试当 cards 包含一个 qualifyEventType 为 DISCOUNT_CARD 的对象时，方法应返回 true
     */
    @Test
    public void testIsValidMemberCardWhenCardsContainsDiscountCard() throws Throwable {
        // arrange
        CardQualifyEventIdDTO card = new CardQualifyEventIdDTO();
        card.setQualifyEventType(QualifyEventTypeEnum.DISCOUNT_CARD.getCode());
        List<CardQualifyEventIdDTO> cards = Collections.singletonList(card);
        // act
        boolean result = invokePrivateIsValidMemberCard(cards);
        // assert
        assertTrue(result);
    }

    /**
     * 测试当 cards 包含一个 qualifyEventType 为 MEMBER_DAY 的对象时，方法应返回 true
     */
    @Test
    public void testIsValidMemberCardWhenCardsContainsMemberDay() throws Throwable {
        // arrange
        CardQualifyEventIdDTO card = new CardQualifyEventIdDTO();
        card.setQualifyEventType(QualifyEventTypeEnum.MEMBER_DAY.getCode());
        List<CardQualifyEventIdDTO> cards = Collections.singletonList(card);
        // act
        boolean result = invokePrivateIsValidMemberCard(cards);
        // assert
        assertTrue(result);
    }

    /**
     * 测试当 cards 包含多个对象，且至少有一个对象的 qualifyEventType 为 DISCOUNT_CARD 或 MEMBER_DAY 时，方法应返回 true
     */
    @Test
    public void testIsValidMemberCardWhenCardsContainsMultipleValidCards() throws Throwable {
        // arrange
        CardQualifyEventIdDTO card1 = new CardQualifyEventIdDTO();
        card1.setQualifyEventType(QualifyEventTypeEnum.DISCOUNT_CARD.getCode());
        CardQualifyEventIdDTO card2 = new CardQualifyEventIdDTO();
        card2.setQualifyEventType(QualifyEventTypeEnum.MEMBER_DAY.getCode());
        CardQualifyEventIdDTO card3 = new CardQualifyEventIdDTO();
        // 无效的 qualifyEventType
        card3.setQualifyEventType(3);
        List<CardQualifyEventIdDTO> cards = Arrays.asList(card1, card2, card3);
        // act
        boolean result = invokePrivateIsValidMemberCard(cards);
        // assert
        assertTrue(result);
    }

    /**
     * 测试当 cards 包含多个对象，但没有一个对象的 qualifyEventType 为 DISCOUNT_CARD 或 MEMBER_DAY 时，方法应返回 false
     */
    @Test
    public void testIsValidMemberCardWhenCardsContainsNoValidCards() throws Throwable {
        // arrange
        CardQualifyEventIdDTO card1 = new CardQualifyEventIdDTO();
        // 无效的 qualifyEventType
        card1.setQualifyEventType(3);
        CardQualifyEventIdDTO card2 = new CardQualifyEventIdDTO();
        // 无效的 qualifyEventType
        card2.setQualifyEventType(4);
        List<CardQualifyEventIdDTO> cards = Arrays.asList(card1, card2);
        // act
        boolean result = invokePrivateIsValidMemberCard(cards);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsValidTimesCard_InvalidTimesCard() throws Throwable {
        // Given
        CardSummaryBarDTO timesCard = new CardSummaryBarDTO();
        timesCard.setPrice(null);
        timesCard.setTimes(0);
        // When
        boolean isValid = invokePrivateIsValidTimesCard(timesCard);
        // Then
        assertFalse(isValid);
    }

    @Test
    public void testIsValidTimesCard_ValidTimesCard() throws Throwable {
        // Given
        CardSummaryBarDTO timesCard = new CardSummaryBarDTO();
        timesCard.setPrice(new BigDecimal("10.00"));
        timesCard.setTimes(1);
        // When
        boolean isValid = invokePrivateIsValidTimesCard(timesCard);
        // Then
        assertTrue(isValid);
    }
}
