package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.pay.promo.display.api.PromoDisplayService;
import com.dianping.pay.promo.display.api.dto.QueryPromoDisplayRequest;
import com.dianping.pay.promo.rule.api.dto.Response;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.sankuai.meituan.common.json.JSONUtil;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.concurrent.Future;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class PromoWrapper_PrePromoDisplayDTOTest {

    @InjectMocks
    private PromoWrapper promoWrapper = new PromoWrapper();

    @Mock
    private PromoDisplayService promoDisplayServiceFuture;

    /**
     * Tests the prePromoDisplayDTO method under exception conditions.
     */
    @Test
    public void testPrePromoDisplayDTOException() throws Throwable {
        // Initialize mocks
        MockitoAnnotations.initMocks(this);
        // Arrange
        QueryPromoDisplayRequest request = new QueryPromoDisplayRequest();
        when(promoDisplayServiceFuture.queryPromoDisplayDTO(request)).thenThrow(new RuntimeException());
        // Act
        Future actualFuture = promoWrapper.prePromoDisplayDTO(request);
        // Assert
        verify(promoDisplayServiceFuture, times(1)).queryPromoDisplayDTO(request);
        assertNull(actualFuture);
    }
}
