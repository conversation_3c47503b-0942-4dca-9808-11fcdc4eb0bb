package com.dianping.mobile.mapi.dztgdetail.helper;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class DateHelperTest {

    /**
     * 测试DealGroupDTO的attrs不包含维修预付团单属性时
     */
    @Test
    public void testParseDate1() {
        // 测试 yyyy-MM-dd 格式
        String dateStr1 = "2023-10-25";
        Date date1 = DateHelper.parseDate(dateStr1);
        assertNotNull(date1);
    }

    @Test
    public void testParseDate2() {
        // 测试 yyyy-MM-dd HH:mm:ss 格式
        String dateStr2 = "2023-10-25 14:30:45";
        Date date2 = DateHelper.parseDate(dateStr2);
        assertNotNull(date2);
    }

    @Test
    public void testParseDate3() {
        // 测试无效格式
        String dateStr3 = "2023/10/25";
        Date date3 = DateHelper.parseDate(dateStr3);
        assertNull(date3);
    }

    @Test
    public void testIsCurrentDateInRange() {
        String dateStr1 = "2023-10-25 14:30:45";
        String dateStr2 = "2024-10-25 14:30:45";
        boolean result = DateHelper.isCurrentDateInRange(dateStr1, dateStr2);
        assertFalse(result);
    }

    @Test
    public void testConvertToLocalDate() {
        LocalDate result = DateHelper.convertToLocalDate(new Date());
        assertNotNull(result);
    }

    /**
     * 测试 convertToLocalDate 方法，传入的 Date 对象可以正常转换为 LocalDate 对象
     */
    @Test
    public void testConvertToLocalDateNormal() {
        // arrange
        Date date = new Date();
        // act
        LocalDate result = DateHelper.convertToLocalDate(date);
        // assert
        assertNotNull(result);
        assertEquals(date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate(), result);
    }

    /**
     * 测试 convertToLocalDate 方法，传入的 Date 对象为 null
     */
    @Test
    public void testConvertToLocalDateNull() {
        // arrange
        Date date = null;
        // act
        LocalDate localDate = DateHelper.convertToLocalDate(date);
        assertNull(localDate);
        // assert
        // expect NullPointerException
    }

    /**
     * 测试当前日期在输入的日期区间内的情况
     */
    @Test
    public void testIsCurrentDateInRange_StartDateBeforeCurrentDate_EndDateAfterCurrentDate() throws Throwable {
        // arrange
        String startDateStr = LocalDate.now().minusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE);
        String endDateStr = LocalDate.now().plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE);
        // act
        boolean result = DateHelper.isCurrentDateInRange(startDateStr, endDateStr);
        // assert
        assertTrue(result);
    }

    /**
     * 测试当前日期不在输入的日期区间内的情况
     */
    @Test
    public void testIsCurrentDateInRange_StartDateAfterCurrentDate_EndDateBeforeCurrentDate() throws Throwable {
        // arrange
        String startDateStr = LocalDate.now().minusDays(2).format(DateTimeFormatter.ISO_LOCAL_DATE);
        String endDateStr = LocalDate.now().minusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE);
        // act
        boolean result = DateHelper.isCurrentDateInRange(startDateStr, endDateStr);
        // assert
        assertFalse(result);
    }

    /**
     * 测试输入的日期字符串格式不正确的情况
     */
    @Test
    public void testIsCurrentDateInRange_InvalidDateFormat() throws Throwable {
        // arrange
        // 无效的日期格式
        String startDateStr = "2023/10/25";
        // 无效的日期格式
        String endDateStr = "2023/10/27";
        // act
        boolean result = DateHelper.isCurrentDateInRange(startDateStr, endDateStr);
        // assert
        assertFalse(result);
    }

    /**
     * 测试输入的日期字符串格式正确，且当前日期在输入的日期区间内的情况
     */
    @Test
    public void testIsCurrentDateInRange_ValidDateFormat_CurrentDateInRange() throws Throwable {
        // arrange
        // 有效的日期格式
        String startDateStr = LocalDate.now().minusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE);
        String endDateStr = LocalDate.now().plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE);
        // act
        boolean result = DateHelper.isCurrentDateInRange(startDateStr, endDateStr);
        // assert
        // 如果日期格式正确且当前日期在区间内，应返回 true
        assertTrue(result);
    }
}
