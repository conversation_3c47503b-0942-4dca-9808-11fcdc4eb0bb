package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.util.RedirectUrls;
import com.dianping.tuangu.dztg.usercenter.api.dto.BatchGetCreateOrderPageUrlDto;
import com.dianping.tuangu.dztg.usercenter.api.dto.Response;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SkuModuleProcessorTest {

    @InjectMocks
    private SkuModuleProcessor skuModuleProcessor;

    private MockedStatic<Lion> lionMockedStatic;

    @Mock
    private Response<Map<String, String>> mockResponse;

    private MockedStatic<RedirectUrls> redirectUrlsMockedStatic;

    @Before
    public void setUp() {
        lionMockedStatic = mockStatic(Lion.class);
        redirectUrlsMockedStatic = mockStatic(RedirectUrls.class);
    }

    @After
    public void tearDown() {
        lionMockedStatic.close();
        redirectUrlsMockedStatic.close();
    }

    // ... 其他测试用例保持不变
    @Test
    public void testProcess_DealGroupDTOIsNull() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        // act
        skuModuleProcessor.process(ctx);
        // assert
        assertNull(ctx.getSkuModule());
    }

    @Test
    public void testProcess_DealsIsNull() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setDealGroupDTO(new DealGroupDTO());
        // act
        skuModuleProcessor.process(ctx);
        // assert
        assertNull(ctx.getSkuModule());
    }

    @Test
    public void testProcess_DealGroupDealDTOIsNull() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setDealGroupDTO(new DealGroupDTO());
        ctx.getDealGroupDTO().setDeals(Arrays.asList(new DealGroupDealDTO()));
        // act
        skuModuleProcessor.process(ctx);
        // assert
        assertNull(ctx.getSkuModule());
    }

    @Test
    public void testProcess_AttrsIsNull() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setDealGroupDTO(new DealGroupDTO());
        ctx.getDealGroupDTO().setDeals(Arrays.asList(new DealGroupDealDTO()));
        // act
        skuModuleProcessor.process(ctx);
        // assert
        assertNull(ctx.getSkuModule());
    }

    /**
     * 测试getDealSaleAttrSort方法，当Lion返回空配置时
     */
    @Test
    public void testGetDealSaleAttrSortWithEmptyConfig() {
        // arrange
        lionMockedStatic.when(() -> Lion.getString(anyString(), anyString())).thenReturn("[]");

        // act
        List<String> result = skuModuleProcessor.getDealSaleAttrSort(1L, "testServiceType");

        // assert
        assertEquals(Lists.newArrayList("styleId", "nailsType", "Jiapianchicun"), result);
    }

    /**
     * 测试getDealSaleAttrSort方法，当Lion返回非空但不匹配的配置时
     */
    @Test
    public void testGetDealSaleAttrSortWithNonMatchingConfig() {
        // arrange
        String configJson = "[{\"serviceTypeId\":2,\"serviceType\":\"otherServiceType\",\"saleAttrSort\":[\"otherAttr\"]}]";
        lionMockedStatic.when(() -> Lion.getString(anyString(), anyString())).thenReturn(configJson);

        // act
        List<String> result = skuModuleProcessor.getDealSaleAttrSort(1L, "testServiceType");

        // assert
        assertEquals(Lists.newArrayList("styleId", "nailsType", "Jiapianchicun"), result);
    }

    /**
     * 测试getDealSaleAttrSort方法，当Lion返回匹配的配置时
     */
    @Test
    public void testGetDealSaleAttrSortWithMatchingConfig() {
        // arrange
        String configJson = "[{\"serviceTypeId\":1,\"serviceType\":\"testServiceType\",\"saleAttrSort\":[\"matchedAttr\"]}]";
        lionMockedStatic.when(() -> Lion.getString(anyString(), anyString())).thenReturn(configJson);

        // act
        List<String> result = skuModuleProcessor.getDealSaleAttrSort(1L, "testServiceType");

        // assert
        assertEquals(Lists.newArrayList("matchedAttr"), result);
    }

    /**
     * 测试getDealSaleAttrSort方法，当Lion返回非空但不匹配的配置时，且三级类目为月子中心
     */
    @Test
    public void testGetDealSaleAttrSortWithNonMatchingConfigPostpartum() {
        // arrange
        String configJson = "[{\"serviceTypeId\":2,\"serviceType\":\"otherServiceType\",\"saleAttrSort\":[\"otherAttr\"]}]";
        lionMockedStatic.when(() -> Lion.getString(anyString(), anyString())).thenReturn(configJson);

        // act
        List<String> result = skuModuleProcessor.getDealSaleAttrSort(126041L, "testServiceType");

        // assert
        assertEquals(Lists.newArrayList("stay_days", "care_mode", "door_to_door_serve_days"), result);
    }

    /**
     * 测试 getOrderUrl 方法，当 mapResponse 为 null 时
     */
    @Test
    public void testGetOrderUrlWithNullResponse() {
        String result = skuModuleProcessor.getOrderUrl(new DealCtx(null), null);
        assertEquals(null, result);
    }

    /**
     * 测试 getOrderUrl 方法，当 mapResponse 的 content 为 null 时
     */
    @Test
    public void testGetOrderUrlWithNullContent() {
        when(mockResponse.getContent()).thenReturn(null);
        DealCtx ctx = null;
        String result = skuModuleProcessor.getOrderUrl(ctx, mockResponse);
        assertEquals(null, result);
    }

    /**
     * 测试 getOrderUrl 方法，当 mapResponse 的 content 为空 Map 时
     */
    @Test
    public void testGetOrderUrlWithEmptyContent() {
        when(mockResponse.getContent()).thenReturn(new HashMap<>());
        DealCtx ctx = null;
        String result = skuModuleProcessor.getOrderUrl(ctx, mockResponse);
        assertEquals(null, result);
    }

    /**
     * 测试 getOrderUrl 方法，当 mapResponse 的 content 包含有效数据时
     */
    @Test
    public void testGetOrderUrlWithValidContent() {
        Map<String, String> content = new HashMap<>();
        content.put("key", "value");
        when(mockResponse.getContent()).thenReturn(content);
        DealCtx ctx = new DealCtx(null);
        String expectedUrl = "filteredUrl";
        redirectUrlsMockedStatic.when(() -> RedirectUrls.filterOrderUrlSkuId(any(DealCtx.class), anyString())).thenReturn(expectedUrl);
        String result = skuModuleProcessor.getOrderUrl(ctx, mockResponse);
        assertEquals(expectedUrl, result);
    }


    @Test
    public void test_fillExtParam_error() {
//        when(mockResponse.getContent()).thenReturn(new HashMap<>());
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setRequestExtParam("\"%7B%22activityToken%22%3A%22bcjaUI6gIHkmGFgXdRJDb7TzL%22%7D\"");
        BatchGetCreateOrderPageUrlDto dto = new BatchGetCreateOrderPageUrlDto();
        skuModuleProcessor.fillExtParam(ctx, dto);
    }

    @Test
    public void test_fillExtParam_not_error() {
//        when(mockResponse.getContent()).thenReturn(new HashMap<>());
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setRequestExtParam("{\"activityToken\":\"11111\"}");
        BatchGetCreateOrderPageUrlDto dto = new BatchGetCreateOrderPageUrlDto();
        skuModuleProcessor.fillExtParam(ctx, dto);
    }

}
