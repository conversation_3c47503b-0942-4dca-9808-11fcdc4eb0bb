package com.dianping.mobile.mapi.dztgdetail.biz;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealStyleStatisticServiceDealStyleStatisticTest {

    @InjectMocks
    private DealStyleStatisticService dealStyleStatisticService;

    @Mock
    private DealGroupPBO result;

    @Mock
    private EnvCtx envCtx;

    @Mock
    private DealBaseReq request;

    private void commonSetup() {
        when(result.getModuleConfigsModule()).thenReturn(new ModuleConfigsModule());
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.DIANPING_APP);
        when(envCtx.getVersion()).thenReturn("1.0");
        when(request.getMrnversion()).thenReturn("2.0");
    }

    @Test
    public void testDealStyleStatisticWithNullModuleConfigsModule() throws Throwable {
        commonSetup();
        when(result.getModuleConfigsModule()).thenReturn(null);
        dealStyleStatisticService.dealStyleStatistic(request, envCtx, result, true, true);
        verify(result, times(2)).getModuleConfigsModule();
    }

    @Test
    public void testDealStyleStatisticWithEmptyGeneralInfo() throws Throwable {
        commonSetup();
        ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
        moduleConfigsModule.setGeneralInfo("");
        when(result.getModuleConfigsModule()).thenReturn(moduleConfigsModule);
        dealStyleStatisticService.dealStyleStatistic(request, envCtx, result, true, true);
        verify(result, times(2)).getModuleConfigsModule();
    }

    @Test
    public void testDealStyleStatisticWithNonEmptyGeneralInfo() throws Throwable {
        commonSetup();
        ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
        moduleConfigsModule.setGeneralInfo("generalInfo");
        when(result.getModuleConfigsModule()).thenReturn(moduleConfigsModule);
        dealStyleStatisticService.dealStyleStatistic(request, envCtx, result, true, true);
        verify(result, times(2)).getModuleConfigsModule();
    }

    @Test
    public void testDealStyleStatisticWithServiceType() throws Throwable {
        commonSetup();
        ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
        moduleConfigsModule.setGeneralInfo("generalInfo");
        when(result.getModuleConfigsModule()).thenReturn(moduleConfigsModule);
        dealStyleStatisticService.dealStyleStatistic(request, envCtx, result, true, true);
        verify(result, times(2)).getModuleConfigsModule();
    }

    @Test
    public void testDealStyleStatisticWithLogSwitch() throws Throwable {
        commonSetup();
        ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
        moduleConfigsModule.setGeneralInfo("generalInfo");
        when(result.getModuleConfigsModule()).thenReturn(moduleConfigsModule);
        dealStyleStatisticService.dealStyleStatistic(request, envCtx, result, true, true);
        verify(result, times(2)).getModuleConfigsModule();
    }
}
