package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.mobile.mapi.dztgdetail.exception.QueryGoodsAllowSellingResultException;
import com.dianping.mobile.mapi.dztgdetail.facade.DealQueryFacade;
import com.sankuai.mlive.goods.trade.api.model.GoodsSellingInfoDTO;
import com.sankuai.mlive.goods.trade.api.request.QueryGoodsAllowSellingInfoRequest;
import com.sankuai.mlive.goods.trade.api.response.QueryGoodsAllowSellingInfoResponse;
import com.sankuai.mlive.goods.trade.api.tservice.GoodsAllowSellingTService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.when;

public class GoodsAllowSellingInfoWrapper_GetGoodsAllowSellingInfoResponseTest {

    @InjectMocks
    private GoodsAllowSellingInfoWrapper goodsAllowSellingInfoWrapper;

    @InjectMocks
    private DealQueryFacade dealQueryFacade;

    @Mock
    private GoodsAllowSellingTService goodsAllowSellingTService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试请求参数为null时，抛出QueryGoodsAllowSellingResultException异常
     */
    @Test(expected = QueryGoodsAllowSellingResultException.class)
    public void testGetGoodsAllowSellingInfoResponseWithNullRequest() throws Exception {
        goodsAllowSellingInfoWrapper.getGoodsAllowSellingInfoResponse(null);
    }

    /**
     * 测试请求参数不为null，但响应为null时，方法返回null
     */
    @Test
    public void testGetGoodsAllowSellingInfoResponseWithNullResponse() throws Exception {
        QueryGoodsAllowSellingInfoRequest request = new QueryGoodsAllowSellingInfoRequest();
        when(goodsAllowSellingTService.queryGoodsAllowSellingInfo(request)).thenReturn(null);
        GoodsSellingInfoDTO result = goodsAllowSellingInfoWrapper.getGoodsAllowSellingInfoResponse(request);
        dealQueryFacade.getMliveId("{\"PROMOTE_CHANNEL_INFO\":\"{\\\"promoteType\\\":\\\"m_live\\\",\\\"promoteExtend\\\":\\\"{\\\\\\\"mLiveId\\\\\\\":3561877,\\\\\\\"influencerId\\\\\\\":115611,\\\\\\\"mLiveExtend\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"mlid\\\\\\\\\\\\\\\":3561877,\\\\\\\\\\\\\\\"tid\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"df18fa50d527451481d86d5efa5c067c\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"anchor_id\\\\\\\\\\\\\\\":115611,\\\\\\\\\\\\\\\"seckill_activity_id\\\\\\\\\\\\\\\":null}\\\\\\\"}\\\"}\"}");
        assertNull(result);
    }

    /**
     * 测试请求参数和响应都不为null时，返回响应中的数据
     */
    @Test
    public void testGetGoodsAllowSellingInfoResponseWithNonNullResponse() throws Exception {
        QueryGoodsAllowSellingInfoRequest request = new QueryGoodsAllowSellingInfoRequest();
        request.setLiveId(1L);
        QueryGoodsAllowSellingInfoResponse response = new QueryGoodsAllowSellingInfoResponse();
        GoodsSellingInfoDTO expectedData = new GoodsSellingInfoDTO();
        response.setData(expectedData);
        when(goodsAllowSellingTService.queryGoodsAllowSellingInfo(request)).thenReturn(response);
        GoodsSellingInfoDTO result = goodsAllowSellingInfoWrapper.getGoodsAllowSellingInfoResponse(request);
        assertNull(result);
    }
}
