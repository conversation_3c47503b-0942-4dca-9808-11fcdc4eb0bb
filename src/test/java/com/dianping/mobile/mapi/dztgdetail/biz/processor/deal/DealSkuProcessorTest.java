package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.lang.reflect.Method;
import java.util.Objects;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealSkuProcessorTest {

    @InjectMocks
    private DealSkuProcessor dealSkuProcessor;

    @Mock
    private DealGroupDTO dealGroupDTO;

    @Mock
    private DealGroupBasicDTO dealGroupBasicDTO;

    // Helper method to invoke the private getServiceType method using reflection
    private String invokePrivateGetServiceType(DealGroupDTO dealGroupDTO) throws Exception {
        Method method = DealSkuProcessor.class.getDeclaredMethod("getServiceType", DealGroupDTO.class);
        method.setAccessible(true);
        return (String) method.invoke(dealSkuProcessor, dealGroupDTO);
    }

    private String getServiceType(DealGroupDTO dealGroupDTO) {
        return dealGroupDTO == null || dealGroupDTO.getCategory() == null ? null : dealGroupDTO.getCategory().getServiceType();
    }

    private int invokePrivateMethod(Object object, String methodName, Class<?>[] parameterTypes, Object[] parameters) throws Throwable {
        try {
            Method method = object.getClass().getDeclaredMethod(methodName, parameterTypes);
            method.setAccessible(true);
            return (int) method.invoke(object, parameters);
        } catch (Exception e) {
            throw e.getCause();
        }
    }

    /**
     * 测试当 dealGroupDTO 为 null 时，方法返回 null
     */
    @Test
    public void testGetServiceTypeWhenDealGroupDTOIsNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = null;
        // act
        String result = invokePrivateGetServiceType(dealGroupDTO);
        // assert
        assertNull(result);
    }

    /**
     * 测试当 dealGroupDTO 不为 null，但 category 为 null 时，方法返回 null
     */
    @Test
    public void testGetServiceTypeWhenCategoryIsNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getCategory()).thenReturn(null);
        // act
        String result = invokePrivateGetServiceType(dealGroupDTO);
        // assert
        assertNull(result);
    }

    /**
     * 测试当 dealGroupDTO 不为 null，且 category 不为 null 时，方法返回 category 中的 serviceType
     */
    @Test
    public void testGetServiceTypeWhenCategoryIsNotNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(category.getServiceType()).thenReturn("按摩");
        // act
        String result = invokePrivateGetServiceType(dealGroupDTO);
        // assert
        assertEquals("按摩", result);
    }

    /**
     * 测试场景：dealGroupDTO 为 null
     */
    @Test
    public void testGetDealSecondCategoryId_DealGroupDTONull() throws Throwable {
        // arrange
        DealSkuProcessor processor = new DealSkuProcessor();
        // act
        int result = invokePrivateMethod(processor, "getDealSecondCategoryId", new Class[] { DealGroupDTO.class }, new Object[] { null });
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试场景：dealGroupDTO 不为 null，但 dealGroupDTO.getBasic() 为 null
     */
    @Test
    public void testGetDealSecondCategoryId_BasicNull() throws Throwable {
        // arrange
        DealSkuProcessor processor = new DealSkuProcessor();
        when(dealGroupDTO.getBasic()).thenReturn(null);
        // act
        int result = invokePrivateMethod(processor, "getDealSecondCategoryId", new Class[] { DealGroupDTO.class }, new Object[] { dealGroupDTO });
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试场景：dealGroupDTO 和 dealGroupDTO.getBasic() 都不为 null，但 dealGroupDTO.getBasic().getCategoryId() 为 null
     */
    @Test
    public void testGetDealSecondCategoryId_CategoryIdNull() throws Throwable {
        // arrange
        DealSkuProcessor processor = new DealSkuProcessor();
        when(dealGroupDTO.getBasic()).thenReturn(dealGroupBasicDTO);
        when(dealGroupBasicDTO.getCategoryId()).thenReturn(null);
        // act
        int result = invokePrivateMethod(processor, "getDealSecondCategoryId", new Class[] { DealGroupDTO.class }, new Object[] { dealGroupDTO });
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试场景：dealGroupDTO、dealGroupDTO.getBasic() 和 dealGroupDTO.getBasic().getCategoryId() 都不为 null
     */
    @Test
    public void testGetDealSecondCategoryId_AllNotNull() throws Throwable {
        // arrange
        DealSkuProcessor processor = new DealSkuProcessor();
        when(dealGroupDTO.getBasic()).thenReturn(dealGroupBasicDTO);
        when(dealGroupBasicDTO.getCategoryId()).thenReturn(123L);
        // act
        int result = invokePrivateMethod(processor, "getDealSecondCategoryId", new Class[] { DealGroupDTO.class }, new Object[] { dealGroupDTO });
        // assert
        assertEquals(123, result);
    }
}
