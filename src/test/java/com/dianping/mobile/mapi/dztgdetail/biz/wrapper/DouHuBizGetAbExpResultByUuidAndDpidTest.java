package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.client.ConfigEvent;
import com.dianping.lion.client.ConfigListener;
import com.dianping.lion.client.Lion;
import com.dianping.lion.facade.LionFacade;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.utils.FaultToleranceUtils;
import com.google.common.collect.Lists;
import com.sankuai.douhu.absdk.bean.DouHuRequest;
import com.sankuai.douhu.absdk.bean.DouHuResponse;
import com.sankuai.douhu.absdk.client.DouHuClient;
import com.sankuai.douhu.absdk.enums.ErrorCode;
import com.sankuai.douhu.absdk.util.DouHuUtil;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

/**
 * Test class for DouHuBiz.getAbExpResultByUuidAndDpid method
 */
public class DouHuBizGetAbExpResultByUuidAndDpidTest {

    @Mock
    private DouHuClient douHuClient;

    private DouHuBiz douHuBiz;

    private DealCtx context;

    private EnvCtx envCtx;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        douHuBiz = new DouHuBiz();
        // Use reflection to set douHuClient
        java.lang.reflect.Field field = DouHuBiz.class.getDeclaredField("douHuClient");
        field.setAccessible(true);
        field.set(douHuBiz, douHuClient);
        envCtx = new EnvCtx();
        context = new DealCtx(envCtx);
    }

    /**
     * Test when module is null
     */
    @Test
    public void testGetAbExpResultByUuidAndDpid_NullModule() throws Throwable {
        // arrange
        envCtx.setUuid("test_uuid");
        envCtx.setDpId("test_dpid");
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResultByUuidAndDpid(context, null);
        // assert
        assertNull(result);
        verifyNoInteractions(douHuClient);
    }

    /**
     * Test successful AB test case with _b suffix
     */
    @Test
    public void testGetAbExpResultByUuidAndDpid_SuccessWithBSuffix() throws Throwable {
        // arrange
        String module = "test_module";
        envCtx.setUuid("test_uuid");
        envCtx.setDpId("test_dpid");
        context.setMtId(1);
        DouHuResponse response = new DouHuResponse();
        response.setCode(ErrorCode.SUCCESS.getCode());
        response.setSk("test_b");
        response.setAbQueryId("query123");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResultByUuidAndDpid(context, module);
        // assert
        assertNotNull(result);
        assertEquals(module, result.getKey());
        assertEquals(1, result.getConfigs().size());
        AbConfig abConfig = result.getConfigs().get(0);
        assertEquals(module, abConfig.getExpId());
        assertEquals("b", abConfig.getExpResult());
        verify(douHuClient).tryAb(any(DouHuRequest.class));
    }

    /**
     * Test successful AB test case with _c suffix
     */
    @Test
    public void testGetAbExpResultByUuidAndDpid_SuccessWithCSuffix() throws Throwable {
        // arrange
        String module = "test_module";
        envCtx.setUuid("test_uuid");
        envCtx.setDpId("test_dpid");
        context.setMtId(0);
        DouHuResponse response = new DouHuResponse();
        response.setCode(ErrorCode.SUCCESS.getCode());
        response.setSk("test_c");
        response.setAbQueryId("query123");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResultByUuidAndDpid(context, module);
        // assert
        assertNotNull(result);
        assertEquals(module, result.getKey());
        assertEquals(1, result.getConfigs().size());
        AbConfig abConfig = result.getConfigs().get(0);
        assertEquals(module, abConfig.getExpId());
        assertEquals("c", abConfig.getExpResult());
        verify(douHuClient).tryAb(any(DouHuRequest.class));
    }

    /**
     * Test when AB test response is null
     */
    @Test
    public void testGetAbExpResultByUuidAndDpid_NullResponse() throws Throwable {
        // arrange
        String module = "test_module";
        envCtx.setUuid("test_uuid");
        envCtx.setDpId("test_dpid");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(null);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResultByUuidAndDpid(context, module);
        // assert
        assertNull(result);
        verify(douHuClient).tryAb(any(DouHuRequest.class));
    }

    /**
     * Test when AB test response has error code
     */
    @Test
    public void testGetAbExpResultByUuidAndDpid_ErrorResponse() throws Throwable {
        // arrange
        String module = "test_module";
        envCtx.setUuid("test_uuid");
        envCtx.setDpId("test_dpid");
        DouHuResponse response = new DouHuResponse();
        response.setCode(ErrorCode.FAIL.getCode());
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResultByUuidAndDpid(context, module);
        // assert
        assertNull(result);
        verify(douHuClient).tryAb(any(DouHuRequest.class));
    }

    /**
     * Test when AB test throws exception
     */
    @Test
    public void testGetAbExpResultByUuidAndDpid_Exception() throws Throwable {
        // arrange
        String module = "test_module";
        envCtx.setUuid("test_uuid");
        envCtx.setDpId("test_dpid");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenThrow(new RuntimeException("Test exception"));
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResultByUuidAndDpid(context, module);
        // assert
        assertNull(result);
        verify(douHuClient).tryAb(any(DouHuRequest.class));
    }

    /**
     * Test when response SK is blank
     */
    @Test
    public void testGetAbExpResultByUuidAndDpid_BlankSK() throws Throwable {
        // arrange
        String module = "test_module";
        envCtx.setUuid("test_uuid");
        envCtx.setDpId("test_dpid");
        DouHuResponse response = new DouHuResponse();
        response.setCode(ErrorCode.SUCCESS.getCode());
        response.setSk("");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResultByUuidAndDpid(context, module);
        // assert
        assertNull(result);
        verify(douHuClient).tryAb(any(DouHuRequest.class));
    }

    /**
     * Test when response SK is null
     */
    @Test
    public void testGetAbExpResultByUuidAndDpid_NullSK() throws Throwable {
        // arrange
        String module = "test_module";
        envCtx.setUuid("test_uuid");
        envCtx.setDpId("test_dpid");
        DouHuResponse response = new DouHuResponse();
        response.setCode(ErrorCode.SUCCESS.getCode());
        response.setSk(null);
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResultByUuidAndDpid(context, module);
        // assert
        assertNull(result);
        verify(douHuClient).tryAb(any(DouHuRequest.class));
    }

    /**
     * Test when response SK is empty
     */
    @Test
    public void testGetAbExpResultByUuidAndDpid_EmptySK() throws Throwable {
        // arrange
        String module = "test_module";
        envCtx.setUuid("test_uuid");
        envCtx.setDpId("test_dpid");
        DouHuResponse response = new DouHuResponse();
        response.setCode(ErrorCode.SUCCESS.getCode());
        response.setSk("");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResultByUuidAndDpid(context, module);
        // assert
        assertNull(result);
        verify(douHuClient).tryAb(any(DouHuRequest.class));
    }
}
