package com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PromoDetailModule;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

public class NearbyDiscountRelatedFixDealIdProcessorSetMarketPromoDiscountTest {

    private NearbyDiscountRelatedFixDealIdProcessor processor;

    private PromoDetailModule promoDetailModule;

    private PriceDisplayDTO priceDisplayDTO;

    @Before
    public void setUp() {
        processor = new NearbyDiscountRelatedFixDealIdProcessor();
        promoDetailModule = new PromoDetailModule();
        priceDisplayDTO = new PriceDisplayDTO();
    }

    private void invokePrivateSetMarketPromoDiscount(NearbyDiscountRelatedFixDealIdProcessor processor, PromoDetailModule promoDetailModule, PriceDisplayDTO priceDisplayDTO) throws Throwable {
        try {
            Method method = NearbyDiscountRelatedFixDealIdProcessor.class.getDeclaredMethod("setMarketPromoDiscount", PromoDetailModule.class, PriceDisplayDTO.class);
            method.setAccessible(true);
            method.invoke(processor, promoDetailModule, priceDisplayDTO);
        } catch (Exception e) {
            throw e.getCause();
        }
    }

    /**
     * 测试当 marketPrice 或 finalPrice 为 null 时，方法直接返回，不设置 marketPromoDiscount
     */
    @Test
    public void testSetMarketPromoDiscount_NullPrice() throws Throwable {
        // arrange
        priceDisplayDTO.setMarketPrice(null);
        priceDisplayDTO.setPrice(null);
        // act
        invokePrivateSetMarketPromoDiscount(processor, promoDetailModule, priceDisplayDTO);
        // assert
        assertNull(promoDetailModule.getMarketPromoDiscount());
    }

    /**
     * 测试当折扣率小于等于 0.1 时，marketPromoDiscount 设置为 "0.1折"
     */
    @Test
    public void testSetMarketPromoDiscount_DiscountRateLessThanOrEqualTo0_1() throws Throwable {
        // arrange
        priceDisplayDTO.setMarketPrice(new BigDecimal("100"));
        priceDisplayDTO.setPrice(new BigDecimal("1"));
        // act
        invokePrivateSetMarketPromoDiscount(processor, promoDetailModule, priceDisplayDTO);
        // assert
        assertEquals("0.1折", promoDetailModule.getMarketPromoDiscount());
    }

    /**
     * 测试当折扣率在 0.1 到 9.9 之间时，marketPromoDiscount 设置为折扣率 + "折"
     */
    @Test
    public void testSetMarketPromoDiscount_DiscountRateBetween0_1And9_9() throws Throwable {
        // arrange
        priceDisplayDTO.setMarketPrice(new BigDecimal("100"));
        priceDisplayDTO.setPrice(new BigDecimal("50"));
        // act
        invokePrivateSetMarketPromoDiscount(processor, promoDetailModule, priceDisplayDTO);
        // assert
        assertEquals("5.0折", promoDetailModule.getMarketPromoDiscount());
    }
}
