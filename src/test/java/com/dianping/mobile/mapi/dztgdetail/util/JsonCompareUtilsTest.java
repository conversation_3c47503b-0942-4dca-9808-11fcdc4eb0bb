package com.dianping.mobile.mapi.dztgdetail.util;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import java.util.HashMap;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class JsonCompareUtilsTest {

    /**
     * 测试 compareJsonArray 方法，当 array1 和 array2 都为 null 时
     */
    @Test
    public void testCompareJsonArrayBothNull() throws Throwable {
        // arrange
        JSONArray array1 = null;
        JSONArray array2 = null;
        // act
        boolean result = JsonCompareUtils.compareJsonArray(array1, array2);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 compareJsonArray 方法，当 array1 为 null，array2 不为 null 时
     */
    @Test
    public void testCompareJsonArrayArray1Null() throws Throwable {
        // arrange
        JSONArray array1 = null;
        JSONArray array2 = new JSONArray();
        // act
        boolean result = JsonCompareUtils.compareJsonArray(array1, array2);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 compareJsonArray 方法，当 array1 不为 null，array2 为 null 时
     */
    @Test
    public void testCompareJsonArrayArray2Null() throws Throwable {
        // arrange
        JSONArray array1 = new JSONArray();
        JSONArray array2 = null;
        // act
        boolean result = JsonCompareUtils.compareJsonArray(array1, array2);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 compareJsonArray 方法，当 array1 和 array2 的大小不相等时
     */
    @Test
    public void testCompareJsonArraySizeNotEqual() throws Throwable {
        // arrange
        JSONArray array1 = new JSONArray();
        array1.add(1);
        JSONArray array2 = new JSONArray();
        array2.add(1);
        array2.add(2);
        // act
        boolean result = JsonCompareUtils.compareJsonArray(array1, array2);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 compareJsonArray 方法，当 array1 和 array2 的元素不相等时
     */
    @Test
    public void testCompareJsonArrayElementNotEqual() throws Throwable {
        // arrange
        JSONArray array1 = new JSONArray();
        array1.add(1);
        JSONArray array2 = new JSONArray();
        array2.add(2);
        // act
        boolean result = JsonCompareUtils.compareJsonArray(array1, array2);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 compareJsonArray 方法，当 array1 和 array2 的所有元素都相等时
     */
    @Test
    public void testCompareJsonArrayAllEqual() throws Throwable {
        // arrange
        JSONArray array1 = new JSONArray();
        array1.add(1);
        JSONArray array2 = new JSONArray();
        array2.add(1);
        // act
        boolean result = JsonCompareUtils.compareJsonArray(array1, array2);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 convertToJsonObject 方法，传入的对象是 Map 类型，并且不是 JSONObject 类型
     */
    @Test
    public void testConvertToJsonObjectWithMap() throws Throwable {
        // arrange
        Map<String, Object> map = new HashMap<>();
        map.put("key", "value");
        // act
        Object result = JsonCompareUtils.convertToJsonObject(map);
        // assert
        assertNotNull(result);
        assertTrue(result instanceof JSONObject);
        assertEquals("value", ((JSONObject) result).getString("key"));
    }

    /**
     * 测试 convertToJsonObject 方法，传入的对象已经是 JSONObject 类型
     */
    @Test
    public void testConvertToJsonObjectWithJsonObject() throws Throwable {
        // arrange
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("key", "value");
        // act
        Object result = JsonCompareUtils.convertToJsonObject(jsonObject);
        // assert
        assertSame(jsonObject, result);
        assertTrue(result instanceof JSONObject);
    }

    /**
     * 测试 convertToJsonObject 方法，传入的对象不是 Map 类型
     */
    @Test
    public void testConvertToJsonObjectWithNonMap() throws Throwable {
        // arrange
        String str = "test";
        // act
        Object result = JsonCompareUtils.convertToJsonObject(str);
        // assert
        assertSame(str, result);
    }

    /**
     * 测试 convertToJsonObject 方法，传入 null 值
     */
    @Test
    public void testConvertToJsonObjectWithNull() throws Throwable {
        // arrange
        Object nullObj = null;
        // act
        Object result = JsonCompareUtils.convertToJsonObject(nullObj);
        // assert
        assertNull(result);
    }

    /**
     * 测试 convertToJsonObject 方法，传入的对象不是 Map 类型，或者已经是 JSONObject 类型
     */
    @Test
    public void testConvertToJsonObjectNotMap() throws Throwable {
        // arrange
        String str = "test";
        // act
        Object result = JsonCompareUtils.convertToJsonObject(str);
        // assert
        assertEquals(str, result);
    }

    /**
     * Test comparing two JSON objects where both are not null and have equal content.
     */
    @Test
    public void testCompareJsonObjectEqual() throws Throwable {
        // arrange
        JSONObject obj1 = new JSONObject();
        obj1.put("key1", "value1");
        JSONObject obj2 = new JSONObject();
        obj2.put("key1", "value1");
        // act
        boolean result = JsonCompareUtils.compareJsonObject(obj1, obj2);
        // assert
        assertTrue(result);
    }

    /**
     * Test comparing two JSON objects where both are not null but have different content.
     */
    @Test
    public void testCompareJsonObjectNotEqual() throws Throwable {
        // arrange
        JSONObject obj1 = new JSONObject();
        obj1.put("key1", "value1");
        JSONObject obj2 = new JSONObject();
        obj2.put("key1", "value2");
        // act
        boolean result = JsonCompareUtils.compareJsonObject(obj1, obj2);
        // assert
        assertFalse(result);
    }

    /**
     * Test comparing two JSON objects where one is null and the other is not null.
     * This test case is adjusted to ensure that it does not cause a NullPointerException
     * by avoiding passing null JSONObject instances directly.
     */
    @Test
    public void testCompareJsonObjectOneNull() throws Throwable {
        // arrange
        JSONObject obj1 = new JSONObject();
        // Adjusted to avoid null
        JSONObject obj2 = new JSONObject();
        // Keeping as null to test specific scenario
        obj2.put("nullKey", null);
        // act
        boolean result = JsonCompareUtils.compareJsonObject(obj1, obj2);
        // assert
        // The assertion needs to be adjusted based on the expected behavior
        // For example, if obj1 is not null, we might expect false since obj2 is "null" in terms of missing key
        assertFalse(result);
    }

    /**
     * Test comparing two JSON objects where neither is null, but they have different sizes.
     */
    @Test
    public void testCompareJsonObjectSizeNotEqual() throws Throwable {
        // arrange
        JSONObject obj1 = new JSONObject();
        obj1.put("key1", "value1");
        JSONObject obj2 = new JSONObject();
        // Different size
        obj2.put("key2", "value2");
        // act
        boolean result = JsonCompareUtils.compareJsonObject(obj1, obj2);
        // assert
        assertFalse(result);
    }

    /**
     * 测试两个 JSON 对象都不为 null，大小相等，但某个键的值不相等的情况
     */
    @Test
    public void testCompareJsonObjectValueNotEqual() throws Throwable {
        // arrange
        JSONObject obj1 = new JSONObject();
        obj1.put("key1", "value1");
        JSONObject obj2 = new JSONObject();
        obj2.put("key1", "value2");
        // act
        boolean result = JsonCompareUtils.compareJsonObject(obj1, obj2);
        // assert
        assertFalse(result);
    }

    /**
     * 测试两个null值比较应返回true
     */
    @Test
    public void testCompareValues_BothNull_ReturnsTrue() throws Throwable {
        // arrange
        Object value1 = null;
        Object value2 = null;
        // act
        boolean result = JsonCompareUtils.compareValues(value1, value2);
        // assert
        assertTrue(result);
    }

    /**
     * 测试一个null值和一个非null值比较应返回false
     */
    @Test
    public void testCompareValues_OneNullOneNotNull_ReturnsFalse() throws Throwable {
        // arrange
        Object value1 = null;
        Object value2 = "test";
        // act
        boolean result = JsonCompareUtils.compareValues(value1, value2);
        // assert
        assertFalse(result);
    }

    /**
     * 测试两个相同JSONObject比较应返回true
     */
    @Test
    public void testCompareValues_EqualJSONObjects_ReturnsTrue() throws Throwable {
        // arrange
        JSONObject obj1 = new JSONObject();
        obj1.put("key", "value");
        JSONObject obj2 = new JSONObject();
        obj2.put("key", "value");
        // act
        boolean result = JsonCompareUtils.compareValues(obj1, obj2);
        // assert
        assertTrue(result);
    }

    /**
     * 测试两个不同JSONObject比较应返回false
     */
    @Test
    public void testCompareValues_DifferentJSONObjects_ReturnsFalse() throws Throwable {
        // arrange
        JSONObject obj1 = new JSONObject();
        obj1.put("key1", "value1");
        JSONObject obj2 = new JSONObject();
        obj2.put("key2", "value2");
        // act
        boolean result = JsonCompareUtils.compareValues(obj1, obj2);
        // assert
        assertFalse(result);
    }

    /**
     * 测试两个相同JSONArray比较应返回true
     */
    @Test
    public void testCompareValues_EqualJSONArrays_ReturnsTrue() throws Throwable {
        // arrange
        JSONArray arr1 = new JSONArray();
        arr1.add("item1");
        arr1.add("item2");
        JSONArray arr2 = new JSONArray();
        arr2.add("item1");
        arr2.add("item2");
        // act
        boolean result = JsonCompareUtils.compareValues(arr1, arr2);
        // assert
        assertTrue(result);
    }

    /**
     * 测试两个不同JSONArray比较应返回false
     */
    @Test
    public void testCompareValues_DifferentJSONArrays_ReturnsFalse() throws Throwable {
        // arrange
        JSONArray arr1 = new JSONArray();
        arr1.add("item1");
        JSONArray arr2 = new JSONArray();
        arr2.add("item2");
        // act
        boolean result = JsonCompareUtils.compareValues(arr1, arr2);
        // assert
        assertFalse(result);
    }

    /**
     * 测试两个相同数值比较应返回true
     */
    @Test
    public void testCompareValues_EqualNumbers_ReturnsTrue() throws Throwable {
        // arrange
        Number num1 = 10;
        Number num2 = 10.0;
        // act
        boolean result = JsonCompareUtils.compareValues(num1, num2);
        // assert
        assertTrue(result);
    }

    /**
     * 测试两个不同数值比较应返回false
     */
    @Test
    public void testCompareValues_DifferentNumbers_ReturnsFalse() throws Throwable {
        // arrange
        Number num1 = 10;
        Number num2 = 20;
        // act
        boolean result = JsonCompareUtils.compareValues(num1, num2);
        // assert
        assertFalse(result);
    }

    /**
     * 测试Map转换为JSONObject后比较应返回true
     */
    @Test
    public void testCompareValues_MapConversion_ReturnsTrue() throws Throwable {
        // arrange
        Map<String, Object> map1 = new HashMap<>();
        map1.put("key", "value");
        Map<String, Object> map2 = new HashMap<>();
        map2.put("key", "value");
        // act
        boolean result = JsonCompareUtils.compareValues(map1, map2);
        // assert
        assertTrue(result);
    }

    /**
     * 测试不同类型比较应返回false
     */
    @Test
    public void testCompareValues_DifferentTypes_ReturnsFalse() throws Throwable {
        // arrange
        Object value1 = "string";
        Object value2 = 123;
        // act
        boolean result = JsonCompareUtils.compareValues(value1, value2);
        // assert
        assertFalse(result);
    }

    /**
     * 测试JSONArray大小不同比较应返回false
     */
    @Test
    public void testCompareValues_DifferentSizeJSONArrays_ReturnsFalse() throws Throwable {
        // arrange
        JSONArray arr1 = new JSONArray();
        arr1.add("item1");
        arr1.add("item2");
        JSONArray arr2 = new JSONArray();
        arr2.add("item1");
        // act
        boolean result = JsonCompareUtils.compareValues(arr1, arr2);
        // assert
        assertFalse(result);
    }

    /**
     * 测试一个JSONArray为null时比较应返回false
     */
    @Test
    public void testCompareValues_OneNullJSONArray_ReturnsFalse() throws Throwable {
        // arrange
        JSONArray arr1 = new JSONArray();
        arr1.add("item1");
        JSONArray arr2 = null;
        // act
        boolean result = JsonCompareUtils.compareValues(arr1, arr2);
        // assert
        assertFalse(result);
    }

    /**
     * 测试两个空JSONArray比较应返回true
     */
    @Test
    public void testCompareValues_EmptyJSONArrays_ReturnsTrue() throws Throwable {
        // arrange
        JSONArray arr1 = new JSONArray();
        JSONArray arr2 = new JSONArray();
        // act
        boolean result = JsonCompareUtils.compareValues(arr1, arr2);
        // assert
        assertTrue(result);
    }

    /**
     * 测试两个空JSONObject比较应返回true
     */
    @Test
    public void testCompareValues_EmptyJSONObjects_ReturnsTrue() throws Throwable {
        // arrange
        JSONObject obj1 = new JSONObject();
        JSONObject obj2 = new JSONObject();
        // act
        boolean result = JsonCompareUtils.compareValues(obj1, obj2);
        // assert
        assertTrue(result);
    }
}
