package com.dianping.mobile.mapi.dztgdetail.common;

import com.dianping.mobile.framework.datatypes.IMobileContext;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.helper.AppCtxHelper;

@RunWith(MockitoJUnitRunner.class)
public class PearlShopServiceImplTest {

    @Mock
    private IMobileContext context;

    private PearlShopServiceImpl pearlShopService = new PearlShopServiceImpl();

    /**
     * 测试 context 为 null 的情况
     */
    @Test
    public void testIsShopUuidToShopIdContextIsNull() throws Throwable {
        assertFalse(pearlShopService.isShopUuidToShopId(null));
    }

    /**
     * 测试 context 的 appId 为 396 的情况
     */
    @Test
    public void testIsShopUuidToShopIdAppIdIs396() throws Throwable {
        when(context.getAppId()).thenReturn(396);
        assertFalse(pearlShopService.isShopUuidToShopId(context));
    }

    /**
     * 测试 context 的 isMeituanClient 方法返回 true 的情况
     */
    @Test
    public void testIsShopUuidToShopIdIsMeituanClientIsTrue() throws Throwable {
        when(context.getAppId()).thenReturn(395);
        when(context.isMeituanClient()).thenReturn(true);
        assertFalse(pearlShopService.isShopUuidToShopId(context));
    }

    /**
     * 测试 context 的 isMeituanClient 方法返回 false 的情况
     */
    @Test
    public void testIsShopUuidToShopIdIsMeituanClientIsFalse() throws Throwable {
        when(context.getAppId()).thenReturn(395);
        when(context.isMeituanClient()).thenReturn(false);
        assertTrue(pearlShopService.isShopUuidToShopId(context));
    }
}
