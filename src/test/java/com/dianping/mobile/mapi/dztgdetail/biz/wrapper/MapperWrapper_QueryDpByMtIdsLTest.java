package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.poi.relation.service.api.PoiRelationService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

public class MapperWrapper_QueryDpByMtIdsLTest {

    @InjectMocks
    private MapperWrapper mapperWrapper;

    @Mock
    private PoiRelationService poiRelationService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 mtShopIds 为空的情况
     */
    @Test
    public void testQueryDpByMtIdsLEmptyMtShopIds() throws Throwable {
        List<Long> mtShopIds = Collections.emptyList();
        Map<Long, List<Long>> result = mapperWrapper.queryDpByMtIdsL(mtShopIds);
        assertEquals(Collections.emptyMap(), result);
    }

    /**
     * 测试 mtShopIds 不为空，且 poiRelationService.queryDpByMtIdsL(mtShopIds) 方法正常返回的情况
     */
    @Test
    public void testQueryDpByMtIdsLNormal() throws Throwable {
        List<Long> mtShopIds = Arrays.asList(1L, 2L, 3L);
        Map<Long, List<Long>> expected = new HashMap<>();
        expected.put(1L, Arrays.asList(10L, 20L, 30L));
        expected.put(2L, Arrays.asList(40L, 50L, 60L));
        expected.put(3L, Arrays.asList(70L, 80L, 90L));
        when(poiRelationService.queryDpByMtIdsL(mtShopIds)).thenReturn(expected);
        Map<Long, List<Long>> result = mapperWrapper.queryDpByMtIdsL(mtShopIds);
        assertEquals(expected, result);
    }
}
