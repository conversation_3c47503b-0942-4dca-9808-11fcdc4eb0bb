package com.dianping.mobile.mapi.dztgdetail.biz.service.cellarservice;

import com.dianping.mobile.mapi.dztgdetail.biz.DealStyleStatisticService;
import com.dianping.mobile.mapi.dztgdetail.entity.DealStyleConfig;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class DealStyleStatisticServiceTest {

    @InjectMocks
    private DealStyleStatisticService dealStyleStatisticService;

    private DealStyleConfig config;

    @Before
    public void setUp() {
        config = new DealStyleConfig();
        config.setMtAppVersion("10.0.0");
        config.setMtMrnVersion("1.0.0");

        config.setDpAppVersion("10.0.0");
        config.setDpMrnVersion("1.0.0");
        config.setDealStyleExclude(Collections.singletonList(""));
        config.setCardStyleExclude(Collections.singletonList("card_style_v2"));
        config.setExtraInfoExclude(Collections.singletonList(""));
        config.setLogAll(false);
    }

    @Test
    public void testIsValidDealStyle_ConfigNull() {
        boolean result = dealStyleStatisticService.hitEventLog(
                true, "", "10.0.0", "1.0.0", "style", "card", "extra", null);
        assertFalse(result);
    }

    @Test
    public void testIsValidDealStyle_LogAllTrue() {
        config.setLogAll(true);
        boolean result = dealStyleStatisticService.hitEventLog(
                true, "", "9.0.0", "0.9.0", "excludeStyle", "excludeCard", "excludeExtra", config);
        assertTrue(result);
    }

    @Test
    public void testIsValidDealStyle_VersionInvalid() {
        boolean result = dealStyleStatisticService.hitEventLog(
                true, "", "9.0.0", "0.9.0", "style", "card", "extra", config);
        assertFalse(result);
    }

    @Test
    public void testIsValidDealStyle_AppVersionValid() {
        boolean result = dealStyleStatisticService.hitEventLog(
                true, "", "10.0.0", "0.9.0", "style", "card", "extra", config);
        assertFalse(result);
    }

    @Test
    public void testIsValidDealStyle_MrnVersionValid() {
        boolean result = dealStyleStatisticService.hitEventLog(
                true, "", "9.0.0", "1.0.0", "style", "card", "extra", config);
        assertFalse(result);
    }

    @Test
    public void testIsValidDealStyle_DealStyleExcluded() {
        boolean result = dealStyleStatisticService.hitEventLog(
                true, "", "10.0.0", "1.0.0", "excludeStyle", "card", "extra", config);
        assertTrue(result);
    }

    @Test
    public void testIsValidDealStyle_CardStyleExcluded() {
        boolean result = dealStyleStatisticService.hitEventLog(
                true, "", "10.0.0", "1.0.0", "style", "excludeCard", "extra", config);
        assertTrue(result);
    }

    @Test
    public void testIsValidDealStyle_ExtraInfoExcluded() {
        boolean result = dealStyleStatisticService.hitEventLog(
                true, "", "10.0.0", "1.0.0", "style", "card", "excludeExtra", config);
        assertTrue(result);
    }

    @Test
    public void testIsValidDealStyle_AllValid() {
        boolean result = dealStyleStatisticService.hitEventLog(
                true, "", "10.0.0", "1.0.0", "style", "card", "extra", config);
        assertTrue(result);
    }

    @Test
    public void testIsValidDealStyle1_AllValid() {
        boolean result = dealStyleStatisticService.hitEventLog(
                true, "", "10.0.0", "1.0.0", "style", "card_style_v2", "extra", config);
        assertFalse(result);
    }

    @Test
    public void testIsValidDealStyle2_AllValid() {
        boolean result = dealStyleStatisticService.hitEventLog(
                true, "", "9.0.0", "0.9.0", "style", "card_style_v2", "extra", config);
        assertFalse(result);
    }
}