package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.unified.coupon.manage.api.UnifiedCouponInfoService;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import com.dianping.unified.coupon.manage.api.enums.UnifiedCouponManageResultCodeEnum;
import com.dianping.unified.coupon.manage.api.request.BatchLoadCouponRequest;
import com.dianping.unified.coupon.manage.api.response.UnifiedCouponManageResponse;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

public class FitnessCrossDealProcessorBatchLoadCouponTest {

    @Mock
    private UnifiedCouponInfoService unifiedCouponInfoService;

    @Mock
    private UnifiedCouponGroupDTO unifiedCouponGroupDTO;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Test case for invalid user ID (userId <= 0)
     */
    @Test
    public void testBatchLoadCouponInvalidUserId() throws Throwable {
        // arrange
        long userId = 0;
        boolean isDp = true;
        List<String> couponIds = Collections.singletonList("coupon1");
        // act
        List<UnifiedCouponDTO> result = processor.batchLoadCoupon(userId, isDp, couponIds);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * Test case for empty coupon list
     */
    @Test
    public void testBatchLoadCouponEmptyCouponList() throws Throwable {
        // arrange
        long userId = 1;
        boolean isDp = true;
        List<String> couponIds = Collections.emptyList();
        // act
        List<UnifiedCouponDTO> result = processor.batchLoadCoupon(userId, isDp, couponIds);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * Test case for service call throwing an exception
     */
    @Test
    public void testBatchLoadCouponServiceCallThrowsException() throws Throwable {
        // arrange
        long userId = 1;
        boolean isDp = true;
        List<String> couponIds = Collections.singletonList("coupon1");
        when(unifiedCouponInfoService.batchLoadCoupon(any(BatchLoadCouponRequest.class))).thenThrow(new RuntimeException("Service exception"));
        // act
        List<UnifiedCouponDTO> result = processor.batchLoadCoupon(userId, isDp, couponIds);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * Test case for null response from service call
     */
    @Test
    public void testBatchLoadCouponNullResponse() throws Throwable {
        // arrange
        long userId = 1;
        boolean isDp = true;
        List<String> couponIds = Collections.singletonList("coupon1");
        when(unifiedCouponInfoService.batchLoadCoupon(any(BatchLoadCouponRequest.class))).thenReturn(null);
        // act
        List<UnifiedCouponDTO> result = processor.batchLoadCoupon(userId, isDp, couponIds);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * Test case for non-success result code in response
     */
    @Test
    public void testBatchLoadCouponNonSuccessResultCode() throws Throwable {
        // arrange
        long userId = 1;
        boolean isDp = true;
        List<String> couponIds = Collections.singletonList("coupon1");
        UnifiedCouponManageResponse<List<UnifiedCouponDTO>> response = new UnifiedCouponManageResponse<>();
        response.setResultCode(UnifiedCouponManageResultCodeEnum.IllegalParam.getCode());
        when(unifiedCouponInfoService.batchLoadCoupon(any(BatchLoadCouponRequest.class))).thenReturn(response);
        // act
        List<UnifiedCouponDTO> result = processor.batchLoadCoupon(userId, isDp, couponIds);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * Test case for valid response with success result code
     */
    @Test
    public void testBatchLoadCouponValidResponse() throws Throwable {
        // arrange
        long userId = 1;
        boolean isDp = true;
        List<String> couponIds = Collections.singletonList("coupon1");
        UnifiedCouponDTO coupon = new UnifiedCouponDTO("coupon1", unifiedCouponGroupDTO);
        UnifiedCouponManageResponse<List<UnifiedCouponDTO>> response = new UnifiedCouponManageResponse<>();
        response.setResultCode(UnifiedCouponManageResultCodeEnum.Success.getCode());
        response.setResult(Collections.singletonList(coupon));
        when(unifiedCouponInfoService.batchLoadCoupon(any(BatchLoadCouponRequest.class))).thenReturn(response);
        // act
        List<UnifiedCouponDTO> result = processor.batchLoadCoupon(userId, isDp, couponIds);
        // assert
        assertEquals(1, result.size());
        assertEquals(coupon, result.get(0));
    }

    /**
     * Test case for valid response with empty result list
     */
    @Test
    public void testBatchLoadCouponValidResponseEmptyResult() throws Throwable {
        // arrange
        long userId = 1;
        boolean isDp = true;
        List<String> couponIds = Collections.singletonList("coupon1");
        UnifiedCouponManageResponse<List<UnifiedCouponDTO>> response = new UnifiedCouponManageResponse<>();
        response.setResultCode(UnifiedCouponManageResultCodeEnum.Success.getCode());
        response.setResult(Collections.emptyList());
        when(unifiedCouponInfoService.batchLoadCoupon(any(BatchLoadCouponRequest.class))).thenReturn(response);
        // act
        List<UnifiedCouponDTO> result = processor.batchLoadCoupon(userId, isDp, couponIds);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    @InjectMocks
    private FitnessCrossDealProcessor processor;
}
