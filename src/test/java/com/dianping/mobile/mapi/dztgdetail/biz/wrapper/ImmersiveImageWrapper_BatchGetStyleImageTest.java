package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ImmersiveImageWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageVO;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.concurrent.Future;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class ImmersiveImageWrapper_BatchGetStyleImageTest {

    @InjectMocks
    private ImmersiveImageWrapper immersiveImageWrapper;

    @Mock
    private Future future;

    /**
     * 测试 itemIds 为空的情况
     */
    @Test
    public void testBatchGetStyleImageItemIdsEmpty() throws Throwable {
        // arrange
        ImmersiveImageVO expected = null;
        // act
        ImmersiveImageVO actual = immersiveImageWrapper.batchGetStyleImage(null, "idType", 1, true, 1L, 1L, 1);
        // assert
        assertEquals(expected, actual);
    }

    /**
     * 测试 preBatchGetStyleImage 返回 null 的情况
     */
    @Test
    public void testBatchGetStyleImagePreBatchGetStyleImageReturnNull() throws Throwable {
        // arrange
        ImmersiveImageVO expected = null;
        // act
        ImmersiveImageVO actual = immersiveImageWrapper.batchGetStyleImage(Arrays.asList("1", "2", "3"), "idType", 1, true, 1L, 1L, 1);
        // assert
        assertEquals(expected, actual);
    }

    /**
     * 测试 getFutureResult 返回 null 的情况
     */
    @Test
    public void testBatchGetStyleImageGetFutureResultReturnNull() throws Throwable {
        // arrange
        ImmersiveImageVO expected = null;
        // act
        ImmersiveImageVO actual = immersiveImageWrapper.batchGetStyleImage(Arrays.asList("1", "2", "3"), "idType", 1, true, 1L, 1L, 1);
        // assert
        assertEquals(expected, actual);
    }

    /**
     * 测试 handleResp 返回 null 的情况
     */
    @Test
    public void testBatchGetStyleImageHandleRespReturnNull() throws Throwable {
        // arrange
        ImmersiveImageVO expected = null;
        // act
        ImmersiveImageVO actual = immersiveImageWrapper.batchGetStyleImage(Arrays.asList("1", "2", "3"), "idType", 1, true, 1L, 1L, 1);
        // assert
        assertEquals(expected, actual);
    }
}
