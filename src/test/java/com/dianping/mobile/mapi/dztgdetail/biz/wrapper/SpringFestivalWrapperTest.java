package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.poi.feature.api.dto.business.TagDto;
import com.dianping.poi.feature.api.service.business.MTPoiAccountDisplayTagBizService;
import com.dianping.poi.feature.api.service.business.PoiAccountDisplayTagBizService;
import java.util.List;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SpringFestivalWrapperTest {

    @InjectMocks
    private SpringFestivalWrapper springFestivalWrapper;

    @Mock
    private PoiAccountDisplayTagBizService poiAccountDisplayTagBizServiceFuture;

    @Mock
    private MTPoiAccountDisplayTagBizService mTPoiAccountDisplayTagBizServiceFuture;

    @Test
    public void testPreSpringFestivalTagsShopIdIsNull() throws Throwable {
        assertNull(springFestivalWrapper.preSpringFestivalTags(true, null));
    }

    @Test
    public void testPreSpringFestivalTagsShopIdIsLessThanOrEqualToZero() throws Throwable {
        assertNull(springFestivalWrapper.preSpringFestivalTags(true, 0L));
    }

    @Test
    public void testPreSpringFestivalTagsIsMTAndFindByAccountAndMtShopIdLThrowsException() throws Throwable {
        when(mTPoiAccountDisplayTagBizServiceFuture.findByAccountAndMtShopIdL(anyString(), anyLong(), any())).thenThrow(new RuntimeException());
        assertNull(springFestivalWrapper.preSpringFestivalTags(true, 1L));
    }

    @Test
    public void testPreSpringFestivalTagsIsNotMTAndFindByAccountAndShopIdLThrowsException() throws Throwable {
        when(poiAccountDisplayTagBizServiceFuture.findByAccountAndShopIdL(anyString(), anyLong(), any())).thenThrow(new RuntimeException());
        assertNull(springFestivalWrapper.preSpringFestivalTags(false, 1L));
    }
}
