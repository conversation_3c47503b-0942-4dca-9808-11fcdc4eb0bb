package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.base.dto.DealGroupVideoDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.headerpic.HeaderPicProcessor;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ContentType;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.helper.ImageHelper;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealContentBuilderServiceGetContentTest {

    @InjectMocks
    private DealContentBuilderService dealContentBuilderService;

    @Mock
    private Map<String, HeaderPicProcessor> headerPicProcessorMap;

    @Mock
    private HeaderPicProcessor headerPicProcessor;

    @Mock
    private DealCtx ctx;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Test when dealGroupBaseDTO is null
     */
    @Test
    public void testGetContent_WhenDealGroupBaseDTOIsNull() throws Throwable {
        // arrange
        DealGroupBaseDTO dealGroupBaseDTO = null;
        DealCtx ctx = new DealCtx(null);
        DealGroupPBO dealGroupPBO = new DealGroupPBO();
        // act
        List<ContentPBO> result = dealContentBuilderService.getContent(dealGroupBaseDTO, false, ctx, dealGroupPBO);
        // assert
        assertNull(result);
    }

    /**
     * Test new wearable nail deal check
     */
    @Test
    public void testGetContent_WhenIsNewWearableNailDeal() throws Throwable {
        // arrange
        DealGroupBaseDTO dealGroupBaseDTO = new DealGroupBaseDTO();
        DealCtx ctx = new DealCtx(null);
        DealGroupPBO dealGroupPBO = new DealGroupPBO();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(502L);
        categoryDTO.setServiceType("穿戴甲");
        dealGroupDTO.setCategory(categoryDTO);
        ctx.setDealGroupDTO(dealGroupDTO);
        // mock header pic processor
        // act
        List<ContentPBO> result = dealContentBuilderService.getContent(dealGroupBaseDTO, false, ctx, dealGroupPBO);
        // assert
        assertNotNull(result);
    }

    /**
     * Test exception handling
     */
    @Test
    public void testGetContent_WhenException() throws Throwable {
        // arrange
        DealGroupBaseDTO dealGroupBaseDTO = new DealGroupBaseDTO();
        // This will cause NullPointerException
        DealCtx ctx = null;
        DealGroupPBO dealGroupPBO = new DealGroupPBO();
        // act
        List<ContentPBO> result = dealContentBuilderService.getContent(dealGroupBaseDTO, false, ctx, dealGroupPBO);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for Scenario 1: ctx.getVrUrl() is blank.
     * Expected Behavior: The method should return immediately without modifying the result list.
     */
    @Test
    public void testAssembleVrInfo_VrUrlIsBlank() throws Throwable {
        // arrange
        when(ctx.getVrUrl()).thenReturn("");
        List<ContentPBO> result = new ArrayList<>();
        // Use reflection to invoke the private method
        Method method = DealContentBuilderService.class.getDeclaredMethod("assembleVrInfo", List.class, DealCtx.class);
        method.setAccessible(true);
        method.invoke(dealContentBuilderService, result, ctx);
        // Reset accessibility
        method.setAccessible(false);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for Scenario 2: ctx.getVrUrl() is not blank, but the result list is empty.
     * Expected Behavior: The method should return immediately without modifying the result list.
     */
    @Test
    public void testAssembleVrInfo_ResultListIsEmpty() throws Throwable {
        // arrange
        when(ctx.getVrUrl()).thenReturn("http://example.com/vr");
        List<ContentPBO> result = new ArrayList<>();
        // Use reflection to invoke the private method
        Method method = DealContentBuilderService.class.getDeclaredMethod("assembleVrInfo", List.class, DealCtx.class);
        method.setAccessible(true);
        method.invoke(dealContentBuilderService, result, ctx);
        // Reset accessibility
        method.setAccessible(false);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for Scenario 3: ctx.getVrUrl() is not blank, the result list is not empty, but the first element is null.
     * Expected Behavior: The method should return immediately without modifying the result list.
     */
    @Test
    public void testAssembleVrInfo_FirstElementIsNull() throws Throwable {
        // arrange
        when(ctx.getVrUrl()).thenReturn("http://example.com/vr");
        List<ContentPBO> result = new ArrayList<>();
        result.add(null);
        // Use reflection to invoke the private method
        Method method = DealContentBuilderService.class.getDeclaredMethod("assembleVrInfo", List.class, DealCtx.class);
        method.setAccessible(true);
        method.invoke(dealContentBuilderService, result, ctx);
        // Reset accessibility
        method.setAccessible(false);
        // assert
        assertNull(result.get(0));
    }

    /**
     * Test case for Scenario 4: ctx.getVrUrl() is not blank, the result list is not empty, the first element is not null,
     * and the type of the first element is ContentType.PIC.
     * Expected Behavior: The method should modify the first element in the result list.
     */
    @Test
    public void testAssembleVrInfo_FirstElementTypeIsPic() throws Throwable {
        // arrange
        when(ctx.getVrUrl()).thenReturn("http://example.com/vr");
        List<ContentPBO> result = new ArrayList<>();
        ContentPBO contentPBO = new ContentPBO(ContentType.PIC.getType(), "content");
        result.add(contentPBO);
        // Use reflection to invoke the private method
        Method method = DealContentBuilderService.class.getDeclaredMethod("assembleVrInfo", List.class, DealCtx.class);
        method.setAccessible(true);
        method.invoke(dealContentBuilderService, result, ctx);
        // Reset accessibility
        method.setAccessible(false);
        // assert
        assertEquals(ContentType.VR.getType(), result.get(0).getType());
        assertEquals("http://example.com/vr", result.get(0).getVrUrl());
        assertEquals("https://img.meituan.net/dpmobile/4c7918c4a63cecccedf8d355d2e04bc251188.webp", result.get(0).getVrIconUrl());
    }

    /**
     * Test case for Scenario 5: ctx.getVrUrl() is not blank, the result list is not empty, the first element is not null,
     * but the type of the first element is not ContentType.PIC.
     * Expected Behavior: The method should not modify the first element in the result list.
     */
    @Test
    public void testAssembleVrInfo_FirstElementTypeIsNotPic() throws Throwable {
        // arrange
        when(ctx.getVrUrl()).thenReturn("http://example.com/vr");
        List<ContentPBO> result = new ArrayList<>();
        ContentPBO contentPBO = new ContentPBO(ContentType.TEXT.getType(), "content");
        result.add(contentPBO);
        // Use reflection to invoke the private method
        Method method = DealContentBuilderService.class.getDeclaredMethod("assembleVrInfo", List.class, DealCtx.class);
        method.setAccessible(true);
        method.invoke(dealContentBuilderService, result, ctx);
        // Reset accessibility
        method.setAccessible(false);
        // assert
        assertEquals(ContentType.TEXT.getType(), result.get(0).getType());
        assertNull(result.get(0).getVrUrl());
        assertNull(result.get(0).getVrIconUrl());
    }
}
