package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;
import com.dianping.deal.style.DealGroupDzxService;
import com.dianping.deal.style.dto.DealGroupDzxInfo;
import com.dianping.deal.style.dto.StyleResponse;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.util.SwitchUtils;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.dianping.tuangou.dztg.bjwrapper.api.PrometheusWrapperService;
import com.dianping.tuangou.dztg.bjwrapper.api.enums.BizTypeEnum;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealStyleWrapperTest {

    @InjectMocks
    private DealStyleWrapper dealStyleWrapper;

    @Mock
    private Future<StyleResponse> future;

    @Mock
    private PrometheusWrapperService prometheusWrapperServiceFuture;

    @Mock
    private Future mockFuture;

    private MockedStatic<SwitchUtils> mockedSwitchUtils;

    private MockedStatic<FutureFactory> mockedFutureFactory;

    @Mock
    private DealGroupDzxService dealGroupDzxServiceFuture;

    public DealStyleWrapperTest() {
        MockitoAnnotations.initMocks(this);
    }

    @Before
    public void setUp() {
        mockedSwitchUtils = mockStatic(SwitchUtils.class);
        mockedFutureFactory = mockStatic(FutureFactory.class);
    }

    @After
    public void tearDown() {
        mockedSwitchUtils.close();
        mockedFutureFactory.close();
    }

    private void setUpCommonMocks() throws Exception {
        DealGroupDzxInfo mockInfo = new DealGroupDzxInfo();
    }

    /**
     * Test getStyle method when styleFuture is not null, but its get method throws an exception.
     * Adjusted to verify that the method can handle exceptions without breaking.
     */
    @Test
    public void testGetStyleWhenGetMethodThrowsException() throws Throwable {
        try {
            // Arrange
            when(future.get()).thenThrow(new RuntimeException());
            // Act
            StyleResponse response = dealStyleWrapper.getStyle(future);
            // Assert
            assertNull("Expected the response to be null when get method throws an exception", response);
        } catch (Exception e) {
            fail("Did not expect the getStyle method to throw an exception");
        }
    }

    /**
     * Test getStyle method when styleFuture is not null and its get method returns normally, should return that result.
     */
    @Test
    public void testGetStyleWhenGetMethodReturnsNormally() throws Throwable {
        // Arrange
        StyleResponse expectedResponse = new StyleResponse();
        when(future.get()).thenReturn(expectedResponse);
        // Act
        StyleResponse actualResponse = dealStyleWrapper.getStyle(future);
        // Assert
        assertEquals("The returned StyleResponse should match the expected response", expectedResponse, actualResponse);
    }

    @Test
    public void testPreMtDealDtoListWhenSwitchIsOn() throws Throwable {
        mockedSwitchUtils.when(SwitchUtils::isListDealByIdsDegrade).thenReturn(true);
        Future result = dealStyleWrapper.preMtDealDtoList(Arrays.asList(1, 2, 3));
        assertNull(result);
    }

    @Test
    public void testPreMtDealDtoListWhenDealIdsIsNull() throws Throwable {
        mockedSwitchUtils.when(SwitchUtils::isListDealByIdsDegrade).thenReturn(false);
        Future result = dealStyleWrapper.preMtDealDtoList(null);
        assertNull(result);
    }

    @Test
    public void testPreMtDealDtoListWhenDealIdsIsNotNullAndSwitchIsOff() throws Throwable {
        mockedSwitchUtils.when(SwitchUtils::isListDealByIdsDegrade).thenReturn(false);
        mockedFutureFactory.when(FutureFactory::getFuture).thenReturn(mockFuture);
        Future result = dealStyleWrapper.preMtDealDtoList(Arrays.asList(1, 2, 3));
        assertNotNull(result);
        verify(prometheusWrapperServiceFuture, times(1)).listDealByIds(anyList(), eq(BizTypeEnum.MT_DEAL_DETAIL.getBizType()));
    }

    @Test
    public void testPreMtDealDtoListWhenExceptionOccurs() throws Throwable {
        mockedSwitchUtils.when(SwitchUtils::isListDealByIdsDegrade).thenReturn(false);
        doThrow(new RuntimeException()).when(prometheusWrapperServiceFuture).listDealByIds(anyList(), eq(BizTypeEnum.MT_DEAL_DETAIL.getBizType()));
        Future result = dealStyleWrapper.preMtDealDtoList(Arrays.asList(1, 2, 3));
        assertNull(result);
    }

    /**
     * 测试 mtDealGroupId 小于等于0 的情况
     */
    @Test
    public void testPrepareGetDealGroupDzxInfo_MtDealGroupIdLessThanOrEqualToZero() throws Throwable {
        Future result = dealStyleWrapper.prepareGetDealGroupDzxInfo(0, 1);
        assertNull(result);
    }

    /**
     * 测试 mtDealGroupId 大于0，getDealGroupDzxInfo 方法正常执行的情况
     */
    @Test
    public void testPrepareGetDealGroupDzxInfo_Normal() throws Throwable {
        // Prepare a mock return value
        DealGroupDzxInfo mockInfo = new DealGroupDzxInfo();
        when(dealGroupDzxServiceFuture.getDealGroupDzxInfo(anyInt(), anyInt())).thenReturn(mockInfo);
        dealStyleWrapper.prepareGetDealGroupDzxInfo(1, 1);
        verify(dealGroupDzxServiceFuture, times(1)).getDealGroupDzxInfo(1, 1);
    }

    /**
     * 测试 mtDealGroupId 大于0，getDealGroupDzxInfo 方法执行时抛出异常的情况
     */
    @Test
    public void testPrepareGetDealGroupDzxInfo_Exception() throws Throwable {
        doThrow(new RuntimeException()).when(dealGroupDzxServiceFuture).getDealGroupDzxInfo(anyInt(), anyInt());
        Future result = dealStyleWrapper.prepareGetDealGroupDzxInfo(1, 1);
        assertNull(result);
        verify(dealGroupDzxServiceFuture, times(1)).getDealGroupDzxInfo(1, 1);
    }

    /**
     * Tests the scenario where pageSource is empty.
     */
    @Test
    public void testIsImmersiveHeaderImageEmpty() throws Throwable {
        String pageSource = "";
        boolean result = dealStyleWrapper.isImmersiveHeaderImage(pageSource);
        assertFalse(result);
    }

    /**
     * Tests the scenario where pageSource is not empty but not in the IMMERSIVE_HEADER_IMAGE_SOURCE list.
     */
    @Test
    public void testIsImmersiveHeaderImageNotInList() throws Throwable {
        String pageSource = "source3";
        boolean result = dealStyleWrapper.isImmersiveHeaderImage(pageSource);
        assertFalse(result);
    }

    @Test
    public void testGetDealGroupDzxInfoMtDealGroupIdLessThanOrEqualToZero() throws Throwable {
        setUpCommonMocks();
        int mtDealGroupId = 0;
        int platform = 1;
        DealGroupDzxInfo result = dealStyleWrapper.getDealGroupDzxInfo(mtDealGroupId, platform);
        assertNull(result);
    }

    @Test
    public void testGetDealGroupDzxInfoMtDealGroupIdGreaterThanZeroAndPrepareGetDealGroupDzxInfoThrowException() throws Throwable {
        when(dealGroupDzxServiceFuture.getDealGroupDzxInfo(anyInt(), anyInt())).thenThrow(new RuntimeException());
        int mtDealGroupId = 1;
        int platform = 1;
        DealGroupDzxInfo result = null;
        try {
            result = dealStyleWrapper.getDealGroupDzxInfo(mtDealGroupId, platform);
        } catch (RuntimeException e) {
            assertNull(result);
        }
    }
}
