package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.clr.content.process.thrift.api.ContentProcessService;
import com.sankuai.clr.content.process.thrift.dto.req.BatchQueryPlanListReqDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.concurrent.Future;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DealConsumerWrapper_QueryDealConsumerInfoTest {

    @InjectMocks
    private DealConsumerWrapper dealConsumerWrapper;

    @Mock
    private ContentProcessService contentProcessServiceFuture;

    @Mock
    private Future mockFuture;

    @Test
    public void testQueryDealConsumerInfoReqDtoIsNull() throws Throwable {
        BatchQueryPlanListReqDTO reqDTO = null;
        Future result = dealConsumerWrapper.queryDealConsumerInfo(reqDTO);
        assertNull(result);
    }

    @Test
    public void testQueryDealConsumerInfoException() throws Throwable {
        BatchQueryPlanListReqDTO reqDTO = new BatchQueryPlanListReqDTO();
        doThrow(new RuntimeException()).when(contentProcessServiceFuture).batchQueryPlanList(reqDTO);
        Future result = dealConsumerWrapper.queryDealConsumerInfo(reqDTO);
        assertNull(result);
    }
}
