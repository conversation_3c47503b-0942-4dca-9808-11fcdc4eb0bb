package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealAttrHelper_IsPreSaleTest {

    private static final String PRE_SALE_TAG = "pre_sale_tag";

    @Test
    public void testIsPreSaleV2_AttrsIsNull() throws Throwable {
        assertFalse(DealAttrHelper.isPreSaleV2(null));
    }

    @Test
    public void testIsPreSaleV2_AttrNotExist() throws Throwable {
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("other");
        assertFalse(DealAttrHelper.isPreSaleV2(Collections.singletonList(attrDTO)));
    }

    @Test
    public void testIsPreSaleV2_AttrValueIsEmpty() throws Throwable {
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(PRE_SALE_TAG);
        attrDTO.setValue(Collections.emptyList());
        assertFalse(DealAttrHelper.isPreSaleV2(Collections.singletonList(attrDTO)));
    }

    @Test
    public void testIsPreSaleV2_AttrValueIsNotYes() throws Throwable {
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(PRE_SALE_TAG);
        attrDTO.setValue(Collections.singletonList("NO"));
        assertFalse(DealAttrHelper.isPreSaleV2(Collections.singletonList(attrDTO)));
    }

    /**
     * 测试 isPreSale 方法，当 attrs 为空时，应返回 false
     */
    @Test
    public void testIsPreSaleWhenAttrsIsNull() {
        // arrange
        List<AttributeDTO> attrs = null;
        // act
        boolean result = DealAttrHelper.isPreSale(attrs);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 isPreSale 方法，当 attrs 不为空，但是没有 PRE_SALE_TAG 属性时，应返回 false
     */
    @Test
    public void testIsPreSaleWhenAttrsHasNoPreSaleTag() {
        // arrange
        List<AttributeDTO> attrs = new ArrayList<>();
        // act
        boolean result = DealAttrHelper.isPreSale(attrs);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 isPreSale 方法，当 attrs 不为空，有 PRE_SALE_TAG 属性，但是该属性的值为空时，应返回 false
     */
    @Test
    public void testIsPreSaleWhenPreSaleTagValueIsEmpty() {
        // arrange
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName(DealAttrKeys.PRE_SALE_TAG);
        attributeDTO.setValue(new ArrayList<>());
        attrs.add(attributeDTO);
        // act
        boolean result = DealAttrHelper.isPreSale(attrs);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 isPreSale 方法，当 attrs 不为空，有 PRE_SALE_TAG 属性，该属性的值不为空，但是不等于 YES 时，应返回 false
     */
    @Test
    public void testIsPreSaleWhenPreSaleTagValueIsNotYes() {
        // arrange
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName(DealAttrKeys.PRE_SALE_TAG);
        attributeDTO.setValue(new ArrayList<>());
        attributeDTO.getValue().add("false");
        attrs.add(attributeDTO);
        // act
        boolean result = DealAttrHelper.isPreSale(attrs);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 isPreSale 方法，当 attrs 不为空，有 PRE_SALE_TAG 属性，该属性的值等于 YES 时，应返回 true
     */
    @Test
    public void testIsPreSaleWhenPreSaleTagValueIsYes() {
        // arrange
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName(DealAttrKeys.PRE_SALE_TAG);
        attributeDTO.setValue(new ArrayList<>());
        attributeDTO.getValue().add("true");
        attrs.add(attributeDTO);
        // act
        boolean result = DealAttrHelper.isPreSale(attrs);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 isVoucher 方法，当 attrs 为 null 或者为空列表时，应返回 false
     */
    @Test
    public void testIsVoucherWhenAttrsIsNull() throws Throwable {
        // arrange
        // act
        boolean result = DealAttrHelper.isVoucher(null);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 isVoucher 方法，当 attrs 中包含 DealAttrKeys.VOUCHER 和 DealAttrKeys.VOUCHER_VALUE 这两个属性时，应返回 true
     */
    @Test
    public void testIsVoucherWhenAttrsContainsVoucherAndVoucherValue() throws Throwable {
        // arrange
        AttributeDTO attributeDTO = new AttributeDTO();
        // Assuming this is the correct constant for the attribute name
        attributeDTO.setName(DealAttrKeys.VOUCHER);
        // Assuming this is the correct constant for the attribute value
        attributeDTO.setValue(Arrays.asList(DealAttrKeys.VOUCHER_VALUE));
        // act
        boolean result = DealAttrHelper.isVoucher(Arrays.asList(attributeDTO));
        // assert
        assertTrue(result);
    }

    /**
     * 测试 isVoucher 方法，当 attrs 中不包含 DealAttrKeys.VOUCHER 和 DealAttrKeys.VOUCHER_VALUE 这两个属性时，应返回 false
     */
    @Test
    public void testIsVoucherWhenAttrsNotContainsVoucherAndVoucherValue() throws Throwable {
        // arrange
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("other");
        attributeDTO.setValue(Arrays.asList("otherValue"));
        // act
        boolean result = DealAttrHelper.isVoucher(Arrays.asList(attributeDTO));
        // assert
        assertFalse(result);
    }
}
