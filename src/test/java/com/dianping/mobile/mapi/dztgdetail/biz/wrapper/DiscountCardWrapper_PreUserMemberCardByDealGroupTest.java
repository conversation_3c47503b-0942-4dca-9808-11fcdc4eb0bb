package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.gm.marketing.member.card.api.dto.GMResponse;
import com.sankuai.merchantcard.discountcard.ProductDetailBarService;
import com.sankuai.merchantcard.discountcard.dto.LoadDealGroupCardBarRequest;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.MockedStatic;
import static org.mockito.Mockito.*;
import java.util.concurrent.Future;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;

@RunWith(MockitoJUnitRunner.class)
public class DiscountCardWrapper_PreUserMemberCardByDealGroupTest {

    @InjectMocks
    private DiscountCardWrapper discountCardWrapper;

    @Mock
    private ProductDetailBarService productDetailBarService;

    @Mock
    private Future mockFuture;

    /**
     * Tests the preUserMemberCardByDealGroup method under normal conditions.
     */
    @Test
    public void testPreUserMemberCardByDealGroupNormal() throws Throwable {
        // arrange
        long userId = 1L;
        int dpDealGroupId = 1;
        boolean isMt = true;
        long dpShopId = 1L;
        // Mock FutureFactory to return a mock Future object using mockStatic
        try (MockedStatic<FutureFactory> mockedStatic = mockStatic(FutureFactory.class)) {
            mockedStatic.when(FutureFactory::getFuture).thenReturn(mockFuture);
            // act
            Future result = discountCardWrapper.preUserMemberCardByDealGroup(userId, dpDealGroupId, isMt, dpShopId);
            // assert
            verify(productDetailBarService, times(1)).loadDealGroupCardBarInfo(any(LoadDealGroupCardBarRequest.class));
            // Verify that the result is not null
            assertNotNull(result);
        }
    }

    /**
     * Tests the preUserMemberCardByDealGroup method under exception conditions.
     */
    @Test
    public void testPreUserMemberCardByDealGroupException() throws Throwable {
        // arrange
        long userId = 1L;
        int dpDealGroupId = 1;
        boolean isMt = true;
        long dpShopId = 1L;
        doThrow(new RuntimeException()).when(productDetailBarService).loadDealGroupCardBarInfo(any(LoadDealGroupCardBarRequest.class));
        // act
        Future result = discountCardWrapper.preUserMemberCardByDealGroup(userId, dpDealGroupId, isMt, dpShopId);
        // assert
        verify(productDetailBarService, times(1)).loadDealGroupCardBarInfo(any(LoadDealGroupCardBarRequest.class));
        // Verify that the result is null when an exception is thrown
        assertNull(result);
    }
}
