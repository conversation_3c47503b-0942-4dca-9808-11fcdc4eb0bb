package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.gmkt.activity.api.dto.DealActivityDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.Collections;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class ActivitiesHelperTest {

    /**
     * 测试dealActivities列表为空的情况
     */
    @Test
    public void testIsContainsSpecActivityDealActivitiesIsNull() {
        assertFalse(ActivitiesHelper.isContainsSpecActivity(null, Arrays.asList(1, 2, 3)));
    }

    /**
     * 测试dealActivities列表不为空，但没有任何DealActivityDTO对象的activityType在activityTypes列表中的情况
     */
    @Test
    public void testIsContainsSpecActivityNoMatch() {
        DealActivityDTO dealActivityDTO = new DealActivityDTO();
        dealActivityDTO.setActivityType(4);
        assertFalse(ActivitiesHelper.isContainsSpecActivity(Arrays.asList(dealActivityDTO), Arrays.asList(1, 2, 3)));
    }

    /**
     * 测试dealActivities列表不为空，且至少有一个DealActivityDTO对象的activityType在activityTypes列表中的情况
     */
    @Test
    public void testIsContainsSpecActivityMatch() {
        DealActivityDTO dealActivityDTO = new DealActivityDTO();
        dealActivityDTO.setActivityType(1);
        assertTrue(ActivitiesHelper.isContainsSpecActivity(Arrays.asList(dealActivityDTO), Arrays.asList(1, 2, 3)));
    }
}
