package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.factory.impl;

import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import junit.framework.TestCase;
import org.junit.runner.RunWith;
import org.junit.Test;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;

/**
 * <AUTHOR>
 * @since 2024/2/27 19:34
 */
@RunWith(MockitoJUnitRunner.class)
public class HeadStrategyImplTest extends TestCase {

    @Test
    public void testGetToolValueReturnsEmptyString() throws Throwable {
        // arrange
        HeadStrategyImpl headStrategy = new HeadStrategyImpl();
        ServiceProjectAttrDTO attr = new ServiceProjectAttrDTO();

        // act
        String resultWithNull = headStrategy.getToolValue(null);
        String resultWithEmptyList = headStrategy.getToolValue(Collections.emptyList());
        String resultWithNonEmptyList = headStrategy.getToolValue(Arrays.asList(attr));

        // assert
        assertEquals("", resultWithNull);
        assertEquals("", resultWithEmptyList);
        assertEquals("", resultWithNonEmptyList);
    }
}