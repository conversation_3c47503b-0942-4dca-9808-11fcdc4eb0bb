package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.meituan.service.mobile.message.group.deal.PoiidListRequestMsg;
import com.meituan.service.mobile.message.group.deal.PoiidListResponseMsg;
import com.meituan.service.mobile.message.group.deal.RPCGroupDealService;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class GroupDealWrapper_ListPoiIdByDidByCityIdTest {

    @InjectMocks
    private GroupDealWrapper groupDealWrapper;

    @Mock
    private RPCGroupDealService.Iface rpcGroupDealService;

    @Test
    public void testListPoiIdByDidByCityIdWhenResponseIsNull() throws Throwable {
        PoiidListRequestMsg poiidListRequestMsg = new PoiidListRequestMsg();
        poiidListRequestMsg.setCityId(1);
        poiidListRequestMsg.setDids(Collections.singletonList(1));
        poiidListRequestMsg.setThreshold(1);
        when(rpcGroupDealService.listPoiidByDids(poiidListRequestMsg)).thenReturn(null);
        List<Long> result = groupDealWrapper.listPoiIdByDidByCityId(1, 1, 1);
        assertNull(result);
    }

    @Test
    public void testListPoiIdByDidByCityIdWhenResponseNotContainsKey() throws Throwable {
        PoiidListRequestMsg poiidListRequestMsg = new PoiidListRequestMsg();
        poiidListRequestMsg.setCityId(1);
        poiidListRequestMsg.setDids(Collections.singletonList(1));
        poiidListRequestMsg.setThreshold(1);
        PoiidListResponseMsg poiidListResponseMsg = new PoiidListResponseMsg();
        Map<Integer, List<Integer>> did2poiidMap = new HashMap<>();
        did2poiidMap.put(2, Collections.singletonList(1));
        poiidListResponseMsg.setDid2poiidMap(did2poiidMap);
        when(rpcGroupDealService.listPoiidByDids(poiidListRequestMsg)).thenReturn(poiidListResponseMsg);
        List<Long> result = groupDealWrapper.listPoiIdByDidByCityId(1, 1, 1);
        assertNull(result);
    }

    @Test
    public void testListPoiIdByDidByCityIdWhenResponseContainsKey() throws Throwable {
        PoiidListRequestMsg poiidListRequestMsg = new PoiidListRequestMsg();
        poiidListRequestMsg.setCityId(1);
        poiidListRequestMsg.setDids(Collections.singletonList(1));
        poiidListRequestMsg.setThreshold(1);
        PoiidListResponseMsg poiidListResponseMsg = new PoiidListResponseMsg();
        Map<Integer, List<Integer>> did2poiidMap = new HashMap<>();
        did2poiidMap.put(1, Collections.singletonList(1));
        poiidListResponseMsg.setDid2poiidMap(did2poiidMap);
        when(rpcGroupDealService.listPoiidByDids(poiidListRequestMsg)).thenReturn(poiidListResponseMsg);
        List<Long> result = groupDealWrapper.listPoiIdByDidByCityId(1, 1, 1);
        assertEquals(Collections.singletonList(1L), result);
    }
}
