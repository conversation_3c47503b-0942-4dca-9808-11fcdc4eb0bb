package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.struct.query.api.entity.dto.DealDetailDto;
import com.dianping.deal.struct.common.dto.Resp;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.concurrent.Future;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class DealGroupWrapper_GetDealDetailResultTest {

    @Mock
    private Future future;

    @Mock
    private Resp<DealDetailDto> resp;

    private DealGroupWrapper dealGroupWrapper = new DealGroupWrapper();

    /**
     * 测试 Future 对象为 null 的情况
     */
    @Test
    public void testGetDealDetailResultFutureIsNull() throws Throwable {
        // arrange
        Future future = null;
        // act
        DealDetailDto result = dealGroupWrapper.getDealDetailResult(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 Future 对象非 null，但 getFutureResult(future) 返回 null 的情况
     */
    @Test
    public void testGetDealDetailResultFutureResultIsNull() throws Throwable {
        // arrange
        when(future.get()).thenReturn(null);
        // act
        DealDetailDto result = dealGroupWrapper.getDealDetailResult(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 Future 对象非 null，getFutureResult(future) 返回的 Resp 对象 isSuccess() 方法返回 false 的情况
     */
    @Test
    public void testGetDealDetailResultFutureResultIsNotSuccess() throws Throwable {
        // arrange
        when(future.get()).thenReturn(resp);
        when(resp.isSuccess()).thenReturn(false);
        // act
        DealDetailDto result = dealGroupWrapper.getDealDetailResult(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 Future 对象非 null，getFutureResult(future) 返回的 Resp 对象 isSuccess() 方法返回 true，但 getContent() 方法返回 null 的情况
     */
    @Test
    public void testGetDealDetailResultFutureResultContentIsNull() throws Throwable {
        // arrange
        when(future.get()).thenReturn(resp);
        when(resp.isSuccess()).thenReturn(true);
        when(resp.getContent()).thenReturn(null);
        // act
        DealDetailDto result = dealGroupWrapper.getDealDetailResult(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 Future 对象非 null，getFutureResult(future) 返回的 Resp 对象 isSuccess() 方法返回 true，getContent() 方法返回非 null 的 DealDetailDto 对象的情况
     */
    @Test
    public void testGetDealDetailResultFutureResultContentIsNotNull() throws Throwable {
        // arrange
        DealDetailDto dealDetailDto = new DealDetailDto();
        when(future.get()).thenReturn(resp);
        when(resp.isSuccess()).thenReturn(true);
        when(resp.getContent()).thenReturn(dealDetailDto);
        // act
        DealDetailDto result = dealGroupWrapper.getDealDetailResult(future);
        // assert
        assertSame(dealDetailDto, result);
    }
}
