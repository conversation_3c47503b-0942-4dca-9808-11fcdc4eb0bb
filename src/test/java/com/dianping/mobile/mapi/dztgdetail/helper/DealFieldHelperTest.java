package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.mobile.mapi.dztgdetail.common.enums.RedeemTypeEnum;
import org.junit.Test;
import static org.junit.Assert.assertEquals;
import org.json.JSONObject;

public class DealFieldHelperTest {

    /**
     * Tests the getRedeemTypeFromHowUse method with a valid JSON string and a "key" value that can be converted to a RedeemTypeEnum.
     */
    @Test
    public void testGetRedeemTypeFromHowUse_ValidJsonAndKey() throws Throwable {
        // arrange
        String howuse = new JSONObject().put("key", "ZX").toString();
        // act
        int result = DealFieldHelper.getRedeemTypeFromHowUse(howuse);
        // assert
        assertEquals(RedeemTypeEnum.DIRECT.getType(), result);
    }

    /**
     * Tests the getRedeemTypeFromHowUse method with a valid JSON string but a "key" value that cannot be converted to a RedeemTypeEnum.
     */
    @Test
    public void testGetRedeemTypeFromHowUse_ValidJsonAndInvalidKey() throws Throwable {
        // arrange
        String howuse = new JSONObject().put("key", "INVALID").toString();
        // act
        int result = DealFieldHelper.getRedeemTypeFromHowUse(howuse);
        // assert
        assertEquals(RedeemTypeEnum.DEFAULT.getType(), result);
    }

    /**
     * Tests the getRedeemTypeFromHowUse method with an invalid JSON string.
     */
    @Test
    public void testGetRedeemTypeFromHowUse_InvalidJson() throws Throwable {
        // arrange
        String howuse = "{invalid json}";
        // act
        int result = DealFieldHelper.getRedeemTypeFromHowUse(howuse);
        // assert
        assertEquals(RedeemTypeEnum.DEFAULT.getType(), result);
    }
}
