package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.EducationDealAttrUtils;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailSpecificModuleVO;
import com.dianping.mobile.mapi.dztgdetail.util.EduDealUtils;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.dianping.technician.common.api.domain.TechnicianResp;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.technician.info.online.dto.OnlineTechWithAttrsDTO;
import com.sankuai.technician.info.online.dto.OnlineTechnicianDTO;
import com.sankuai.technician.info.online.service.OnlineTechQueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;


/**
 * 类的描述
 *
 * @auther: liweilong06
 * @date: 2024/1/17 4:13 下午
 */
@RunWith(MockitoJUnitRunner.class)
public class SpecificModuleHandler_1205UnitTest {

    public static final int SUCESS_CODE = 200;
    @InjectMocks
    private SpecificModuleHandler_1205 specificModuleHandler;

    @Mock
    private OnlineTechQueryService onlineTechQueryService;

    private MockedStatic<EducationDealAttrUtils> educationDealService;

    @Mock
    private DealGroupDTO dealGroupDTO;
    @Mock
    private DealGroupCategoryDTO dealGroupCategoryDTO;
    private MockedStatic<EduDealUtils> eduDealUtilsMockedStatic;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        educationDealService = Mockito.mockStatic(EducationDealAttrUtils.class);
        eduDealUtilsMockedStatic = Mockito.mockStatic(EduDealUtils.class);
    }

    @After
    public void tearDown() {
        educationDealService.close();
        eduDealUtilsMockedStatic.close();
    }

    @NotNull
    private static SpecificModuleCtx getSpecificModuleCtx() {
        SpecificModuleCtx ctx = new SpecificModuleCtx();
        ctx = new SpecificModuleCtx();
        ctx.setDpDealGroupId(123);
        ctx.setDpDealGroupIdLong(123L);
        return ctx;
    }

    @Test
    public void testHandleQueryDealInfoReturnsNull() {
        // arrange
        SpecificModuleCtx ctx = getSpecificModuleCtx();

        // act
        specificModuleHandler.handle(ctx);

        // assert
        assertNull(ctx.getResult());
    }

    @Test
    public void testHandleQueryDealInfoReturnsValidDealGroupDTO() {
        // arrange
        SpecificModuleCtx ctx = getSpecificModuleCtx();
        ctx.setDealGroupDTO(dealGroupDTO);
        // act
        specificModuleHandler.handle(ctx);

        // assert
        assertNotNull(ctx.getResult());
        assertTrue(CollectionUtils.isEmpty(ctx.getResult().getUnits()));
    }

    /**
     * 存在主讲老师,并且正确返回主讲老师
     */
    @Test
    public void testHandleOnlineTechQueryServiceReturnsValidOnlineTechWithAttrsDTO() {
        // arrange
        SpecificModuleCtx ctx = getSpecificModuleCtx();
        eduDealUtilsMockedStatic.when(() -> EduDealUtils.isCivilExam(dealGroupDTO)).thenReturn(true);
        ctx.setDealGroupDTO(dealGroupDTO);

        educationDealService.when(() -> EducationDealAttrUtils.getTeacherIds(any())).thenReturn(Lists.newArrayList(1, 2));

        OnlineTechnicianDTO technicianDTO = new OnlineTechnicianDTO();
        technicianDTO.setName("测试");
        technicianDTO.setTechnicianId(1);
        List<OnlineTechWithAttrsDTO> onlineTechList = new ArrayList<>();
        OnlineTechWithAttrsDTO onlineTech1 = mock(OnlineTechWithAttrsDTO.class);
        OnlineTechWithAttrsDTO onlineTech2 = mock(OnlineTechWithAttrsDTO.class);
        when(onlineTech1.getTechnician()).thenReturn(technicianDTO);
        onlineTechList.add(onlineTech1);
        onlineTechList.add(onlineTech2);
        TechnicianResp<List<OnlineTechWithAttrsDTO>> onlineTechResponse = new TechnicianResp<>();
        onlineTechResponse.setData(onlineTechList);
        onlineTechResponse.setCode(SUCESS_CODE);
        when(onlineTechQueryService.getTechByTechIds(any())).thenReturn(onlineTechResponse);

        MockedStatic<FutureFactory> utilities = Mockito.mockStatic(FutureFactory.class);
        utilities.when(() -> FutureFactory.getFuture()).thenReturn(CompletableFuture.completedFuture(onlineTechResponse));

        // act
        specificModuleHandler.handle(ctx);

        // assert
        assertNotNull(ctx.getResult());
        AssertKeyAndValue(ctx.getResult(), 0, "主讲老师", "测试");
        utilities.close();
    }

    private void AssertKeyAndValue(DealDetailSpecificModuleVO result, int index, String key, String value) {
        Assert.assertTrue(key.equals(result.getUnits().get(index).getTitle()));
        Assert.assertTrue(value.equals(result.getUnits().get(index).getDisplayItems().get(0).getDetail()));
    }


    @Test
    public void testHandleEducationDealServiceReturnsValidClassNum() {
        // arrange
        SpecificModuleCtx ctx = getSpecificModuleCtx();
        ctx.setDealGroupDTO(dealGroupDTO);
        educationDealService.when(() -> EducationDealAttrUtils.getClassNum(any())).thenReturn("10");
        // act
        specificModuleHandler.handle(ctx);

        // assert
        assertNotNull(ctx.getResult());
        AssertKeyAndValue(ctx.getResult(), 0, "课时数", "10");
    }

    @Test
    public void testHandleEducationDealServiceReturnsValidSuitablePeople() {
        // arrange
        SpecificModuleCtx ctx = getSpecificModuleCtx();
        ctx.setDealGroupDTO(dealGroupDTO);
        educationDealService.when(() -> EducationDealAttrUtils.getSuitablePeople(any())).thenReturn("测试");
        // act
        specificModuleHandler.handle(ctx);

        // assert
        assertNotNull(ctx.getResult());
        AssertKeyAndValue(ctx.getResult(), 0, "适用人群", "测试");
    }

    @Test
    public void testHandleEducationDealServiceReturnsValidSuitableClass() {
        // arrange
        SpecificModuleCtx ctx = getSpecificModuleCtx();
        ctx.setDealGroupDTO(dealGroupDTO);
        educationDealService.when(() -> EducationDealAttrUtils.getSuitableClass(any())).thenReturn("测试");
        // act
        specificModuleHandler.handle(ctx);

        // assert
        assertNotNull(ctx.getResult());
        AssertKeyAndValue(ctx.getResult(), 0, "适用阶段", "测试");
    }

    @Test
    public void testHandleEducationDealServiceReturnsValidClassType() {
        // arrange
        SpecificModuleCtx ctx = getSpecificModuleCtx();
        ctx.setDealGroupDTO(dealGroupDTO);
        educationDealService.when(() -> EducationDealAttrUtils.getOrigClassType(any())).thenReturn("测试");
        // act
        specificModuleHandler.handle(ctx);

        // assert
        assertNotNull(ctx.getResult());
        AssertKeyAndValue(ctx.getResult(), 0, "班型", "测试");
    }

    @Test
    public void testHandleEducationDealServiceReturnsSuitableProvince() {
        // arrange
        eduDealUtilsMockedStatic.when(() -> EduDealUtils.isCivilExam(dealGroupDTO)).thenReturn(true);
        SpecificModuleCtx ctx = getSpecificModuleCtx();
        ctx.setDealGroupDTO(dealGroupDTO);
        educationDealService.when(() -> EducationDealAttrUtils.getSuitableProvince(any())).thenReturn(Lists.newArrayList("测试1", "测试2"));
        // act
        specificModuleHandler.handle(ctx);

        // assert
        assertNotNull(ctx.getResult());
        AssertKeyAndValue(ctx.getResult(), 0, "适用省份", "测试1、测试2");
    }

    @Test
    public void testHandleEducationDealServiceReturnsClassStage() {
        // arrange
        eduDealUtilsMockedStatic.when(() -> EduDealUtils.isCivilExam(dealGroupDTO)).thenReturn(true);
        SpecificModuleCtx ctx = getSpecificModuleCtx();
        ctx.setDealGroupDTO(dealGroupDTO);
        educationDealService.when(() -> EducationDealAttrUtils.getClassStage(any())).thenReturn("测试1");
        // act
        specificModuleHandler.handle(ctx);

        // assert
        assertNotNull(ctx.getResult());
        AssertKeyAndValue(ctx.getResult(), 0, "课程阶段", "测试1");
    }

    @Test
    public void testHandleEducationDealServiceReturnsValidAppendService() {
        // arrange
        SpecificModuleCtx ctx = getSpecificModuleCtx();
        ctx.setDealGroupDTO(dealGroupDTO);
        educationDealService.when(() -> EducationDealAttrUtils.getAppendServices(any())).thenReturn(Lists.newArrayList("测试1", "测试2"));
        // act
        specificModuleHandler.handle(ctx);

        // assert
        assertNotNull(ctx.getResult());
        AssertKeyAndValue(ctx.getResult(), 0, "附加服务", "测试1、测试2");
    }

    @Test
    public void testHandleEducationDealServiceReturnsValidClassSafeguard() {
        // arrange
        SpecificModuleCtx ctx = getSpecificModuleCtx();
        ctx.setDealGroupDTO(dealGroupDTO);
        educationDealService.when(() -> EducationDealAttrUtils.getClassSafeguard(any())).thenReturn(Lists.newArrayList("测试1", "测试2"));
        // act
        specificModuleHandler.handle(ctx);

        // assert
        assertNotNull(ctx.getResult());
        AssertKeyAndValue(ctx.getResult(), 0, "课程保障", "测试1、测试2");
    }

    @Test
    public void testHandle_EducationDealServiceReturnsValidGiftProduct() {
        // arrange
        SpecificModuleCtx ctx = getSpecificModuleCtx();
        ctx.setDealGroupDTO(dealGroupDTO);
        educationDealService.when(() -> EducationDealAttrUtils.getGiftProduct(any())).thenReturn(Lists.newArrayList("测试1", "测试2"));
        // act
        specificModuleHandler.handle(ctx);

        // assert
        assertNotNull(ctx.getResult());
        AssertKeyAndValue(ctx.getResult(), 0, "实物赠品", "测试1、测试2");
    }

    /**
     * 当团单没有任何需要展示的属性时,返回空列表
     */
    @Test
    public void testHandle_EducationDealServiceReturnsAllNull() {
        // arrange
        SpecificModuleCtx ctx = getSpecificModuleCtx();
        ctx.setDealGroupDTO(dealGroupDTO);
        educationDealService.when(() -> EducationDealAttrUtils.getClassNum(any())).thenReturn(null);
        educationDealService.when(() -> EducationDealAttrUtils.getSuitablePeople(any())).thenReturn(null);
        educationDealService.when(() -> EducationDealAttrUtils.getSuitableClass(any())).thenReturn(null);
        educationDealService.when(() -> EducationDealAttrUtils.getOrigClassType(any())).thenReturn(null);
        educationDealService.when(() -> EducationDealAttrUtils.getAppendServices(any())).thenReturn(null);
        educationDealService.when(() -> EducationDealAttrUtils.getClassSafeguard(any())).thenReturn(null);
        educationDealService.when(() -> EducationDealAttrUtils.getGiftProduct(any())).thenReturn(null);
        // act
        specificModuleHandler.handle(ctx);

        // assert
        assertNotNull(ctx.getResult());
        assertTrue(CollectionUtils.isEmpty(ctx.getResult().getUnits()));
    }

    @Test
    public void testHandleEducationDealServiceReturnsValidTrainingTime() {
        // arrange
        eduDealUtilsMockedStatic.when(() -> EduDealUtils.isCivilExam(dealGroupDTO)).thenReturn(false);
        SpecificModuleCtx ctx = getSpecificModuleCtx();
        ctx.setDealGroupDTO(dealGroupDTO);
        educationDealService.when(() -> EducationDealAttrUtils.getTrainTime(any())).thenReturn("11");
        // act
        specificModuleHandler.handle(ctx);

        // assert
        assertNotNull(ctx.getResult());
        AssertKeyAndValue(ctx.getResult(), 0, SpecificModuleHandler_1205.ATTR_NAME_TRAIN_TIME, "11");
    }
}
