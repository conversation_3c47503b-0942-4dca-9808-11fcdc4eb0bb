package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.feature;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.feature.model.FeatureDetailDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DzDealThemeWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.LayerConfig;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dztheme.deal.dto.DealProductAttrDTO;
import com.sankuai.dztheme.deal.dto.DealProductDTO;
import com.sankuai.dztheme.deal.res.DealProductResult;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.Map;
import java.util.concurrent.Future;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.springframework.stereotype.Component;

@RunWith(MockitoJUnitRunner.class)
public class UniversalInsuranceProcessTest {

    @InjectMocks
    private UniversalInsuranceProcess universalInsuranceProcess;

    @Mock
    private DzDealThemeWrapper dzDealThemeWrapper;

    @Mock
    private DealCtx dealCtx;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class DealInsurance {

        private boolean available;

        private long packageId;

        private int cost;

        private String insureDescUrl;

        private String mainTitle;

        private String subTitle;
    }

    private DealInsurance invokeExtractInsurance(DealProductDTO dealProductDTO) throws Exception {
        Method method = UniversalInsuranceProcess.class.getDeclaredMethod("extractInsurance", DealProductDTO.class);
        method.setAccessible(true);
        return (DealInsurance) method.invoke(universalInsuranceProcess, dealProductDTO);
    }

    @Test
    public void testExtractInsuranceWhenDealProductDTOIsNull() throws Throwable {
        // Call the method directly with null
        DealInsurance result = invokeExtractInsurance(null);
        assertNull(result);
    }

    @Test
    public void testExtractInsuranceWhenAttrsIsEmpty() throws Throwable {
        DealProductDTO dealProductDTO = new DealProductDTO();
        // Call the method directly with a DealProductDTO that has empty attrs
        DealInsurance result = invokeExtractInsurance(dealProductDTO);
        assertNull(result);
    }

    @Test
    public void testExtractInsuranceWhenNoMatchingAttr() throws Throwable {
        DealProductDTO dealProductDTO = new DealProductDTO();
        DealProductAttrDTO dealProductAttrDTO = new DealProductAttrDTO();
        dealProductAttrDTO.setName("NonMatchingName");
        dealProductAttrDTO.setValue("NonMatchingValue");
        dealProductDTO.setAttrs(Collections.singletonList(dealProductAttrDTO));
        // Call the method directly with a DealProductDTO that has a non-matching attribute
        DealInsurance result = invokeExtractInsurance(dealProductDTO);
        assertNull(result);
    }

    @Test
    public void testExtractInsuranceWhenValueCannotBeDecoded() throws Throwable {
        DealProductDTO dealProductDTO = new DealProductDTO();
        DealProductAttrDTO dealProductAttrDTO = new DealProductAttrDTO();
        dealProductAttrDTO.setName("DealInsuranceInfo");
        dealProductAttrDTO.setValue("invalid json");
        dealProductDTO.setAttrs(Collections.singletonList(dealProductAttrDTO));
        // Call the method directly with a DealProductDTO that has an invalid JSON value
        DealInsurance result = invokeExtractInsurance(dealProductDTO);
        assertNull(result);
    }
}
