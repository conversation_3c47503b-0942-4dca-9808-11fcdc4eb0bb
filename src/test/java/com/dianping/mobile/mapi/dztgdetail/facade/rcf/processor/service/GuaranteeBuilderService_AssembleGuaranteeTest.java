package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.Guarantee;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

public class GuaranteeBuilderService_AssembleGuaranteeTest {

    @InjectMocks
    private GuaranteeBuilderService guaranteeBuilderService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试assembleGuarantee方法，当text为非空字符串时
     */
    @Test
    public void testAssembleGuaranteeWithNonEmptyString() {
        // arrange
        String text = "test";
        // act
        Guarantee result = guaranteeBuilderService.assembleGuarantee(text);
        // assert
        Assert.assertEquals(text, result.getText());
    }

    /**
     * 测试assembleGuarantee方法，当text为空字符串时
     */
    @Test
    public void testAssembleGuaranteeWithEmptyString() {
        // arrange
        String text = "";
        // act
        Guarantee result = guaranteeBuilderService.assembleGuarantee(text);
        // assert
        Assert.assertEquals(text, result.getText());
    }

    /**
     * 测试assembleGuarantee方法，当text为null时
     */
    @Test
    public void testAssembleGuaranteeWithNull() {
        // arrange
        String text = null;
        // act
        Guarantee result = guaranteeBuilderService.assembleGuarantee(text);
        // assert
        Assert.assertNull(result.getText());
    }
}
