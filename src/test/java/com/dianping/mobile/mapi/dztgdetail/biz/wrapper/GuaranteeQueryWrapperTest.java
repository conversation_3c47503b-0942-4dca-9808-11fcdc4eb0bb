package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.nib.price.operation.api.common.response.Response;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class GuaranteeQueryWrapperTest {

    @Mock
    private Future future;

    @Mock
    private Response<List<ObjectGuaranteeTagDTO>> response;

    private GuaranteeQueryWrapper guaranteeQueryWrapper;

    @Before
    public void setUp() {
        guaranteeQueryWrapper = new GuaranteeQueryWrapper();
    }

    /**
     * 测试 future 为 null 的情况
     */
    @Test
    public void testGetGuaranteeTagDTOsFutureIsNull() throws Throwable {
        // arrange
        Future future = null;
        // act
        List<ObjectGuaranteeTagDTO> result = guaranteeQueryWrapper.getGuaranteeTagDTOs(future);
        // assert
        assertEquals(new ArrayList<>(), result);
    }

    /**
     * 测试 future.get() 抛出异常的情况
     */
    @Test
    public void testGetGuaranteeTagDTOsFutureGetThrowsException() throws Throwable {
        // arrange
        when(future.get()).thenThrow(new InterruptedException());
        // act
        List<ObjectGuaranteeTagDTO> result = guaranteeQueryWrapper.getGuaranteeTagDTOs(future);
        // assert
        assertEquals(new ArrayList<>(), result);
    }

    /**
     * 测试 response 为 null 的情况
     */
    @Test
    public void testGetGuaranteeTagDTOsResponseIsNull() throws Throwable {
        // arrange
        when(future.get()).thenReturn(null);
        // act
        List<ObjectGuaranteeTagDTO> result = guaranteeQueryWrapper.getGuaranteeTagDTOs(future);
        // assert
        assertEquals(new ArrayList<>(), result);
    }

    /**
     * 测试 response.isNotSuccess() 为 true 的情况
     */
    @Test
    public void testGetGuaranteeTagDTOsResponseIsNotSuccess() throws Throwable {
        // arrange
        when(future.get()).thenReturn(response);
        when(response.isNotSuccess()).thenReturn(true);
        // act
        List<ObjectGuaranteeTagDTO> result = guaranteeQueryWrapper.getGuaranteeTagDTOs(future);
        // assert
        assertEquals(new ArrayList<>(), result);
    }

    /**
     * 测试 response.isNotSuccess() 为 false 的情况
     */
    @Test
    public void testGetGuaranteeTagDTOsResponseIsSuccess() throws Throwable {
        // arrange
        List<ObjectGuaranteeTagDTO> data = new ArrayList<>();
        data.add(new ObjectGuaranteeTagDTO());
        when(future.get()).thenReturn(response);
        when(response.isNotSuccess()).thenReturn(false);
        when(response.getData()).thenReturn(data);
        // act
        List<ObjectGuaranteeTagDTO> result = guaranteeQueryWrapper.getGuaranteeTagDTOs(future);
        // assert
        assertEquals(data, result);
    }
}
