package com.dianping.mobile.mapi.dztgdetail.biz.service.cellarservice;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.style.DealDetailStyleFlashService;
import com.dianping.deal.style.dto.flash.DealDetailStyleQueryRequest;
import com.dianping.deal.style.dto.flash.DealDetailStyleQueryResponse;
import com.dianping.deal.style.dto.flash.DealDetailStyleUpdateRequest;
import com.dianping.deal.style.dto.flash.DealFlashUpdateRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealFlashReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealStyleBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.struct.DealDetailStructModuleDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.DealModuleDetailVO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;


/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class DealCacheBizServiceTest {

    @InjectMocks
    DealDetailStructCacheBizService dealDetailStructCacheBizService;
    @InjectMocks
    DealModuleDetailCacheBizService dealModuleDetailCacheBizService;
    @InjectMocks
    DealStyleCacheBizService dealStyleCacheBizService;

    String reqStr ="{\"dealgroupid\":925669993,\"poiid\":928040795,\"pageSource\":\"poi_page\",\"mrnversion\":\"0.5.13\",\"deviceheight\":906}";
    DealFlashReq req = JSON.parseObject(reqStr,DealFlashReq.class);

    @Before
    public void setUp() throws Exception {}

    @Test
    public void testDealCacheBizService(){

        DealBaseReq request =new DealBaseReq();
        request.setDealgroupid(925669993);
        request.setPoiidStr("poiid");
        request.setPoiid(11L);
        request.setMrnversion("0.5.13");
        request.setPageSource("pageSource");
        request.setUserlat(1.1);
        request.setUserlng(1.1);
        DealFlashReq req1 = dealDetailStructCacheBizService.convertToDealFlashReq(request);

        DealDetailStyleQueryResponse response = new DealDetailStyleQueryResponse();
        response.setSuccess(Boolean.TRUE);
        Future future = CompletableFuture.completedFuture(response);
        dealDetailStructCacheBizService.getCacheValueResult(future);
        DealDetailStyleQueryRequest request1 = dealDetailStructCacheBizService.buildQueryRequest("k");
        DealFlashUpdateRequest request2 = dealDetailStructCacheBizService.buildCacheRequest("k", "v", "callService", "message");

         dealDetailStructCacheBizService.buildKey(req);
         dealModuleDetailCacheBizService.buildKey(req);
         dealStyleCacheBizService.buildKey(req);

        DealDetailStructModuleDo dealDetailStructModuleDo = new DealDetailStructModuleDo();
        dealDetailStructCacheBizService.parseCacheValue(JSON.toJSONString(dealDetailStructModuleDo));
        dealDetailStructCacheBizService.parseCacheValue(null);

        DealModuleDetailVO dealModuleDetailVO = new DealModuleDetailVO();
        dealModuleDetailCacheBizService.parseCacheValue(JSON.toJSONString(dealModuleDetailVO));
        dealModuleDetailCacheBizService.parseCacheValue(null);

        DealStyleBO dealStyleBO = new DealStyleBO();
        dealStyleCacheBizService.parseCacheValue(JSON.toJSONString(dealStyleBO));
        dealStyleCacheBizService.parseCacheValue(null);

        Assert.assertNotNull(req1);
    }
}
