package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.action.app.DzDealBaseAction;
import org.junit.runner.RunWith;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import org.mockito.InjectMocks.*;
import org.mockito.Mock.*;

@RunWith(MockitoJUnitRunner.class)
public class DzDealBaseAction_ValidateTest {

    @Test
    public void testValidateRequestIsNull() throws Throwable {
        // arrange
        DzDealBaseAction dzDealBaseAction = new DzDealBaseAction();
        IMobileContext iMobileContext = mock(IMobileContext.class);
        // act
        IMobileResponse result = dzDealBaseAction.validate(null, iMobileContext);
        // assert
        assertNotNull(result);
        assertEquals(Resps.PARAM_ERROR, result);
    }

    @Test
    public void testValidateDealGroupIdIsNull() throws Throwable {
        // arrange
        DzDealBaseAction dzDealBaseAction = new DzDealBaseAction();
        IMobileContext iMobileContext = mock(IMobileContext.class);
        DealBaseReq dealBaseReq = new DealBaseReq();
        dealBaseReq.setDealgroupid(null);
        // act
        IMobileResponse result = dzDealBaseAction.validate(dealBaseReq, iMobileContext);
        // assert
        assertNotNull(result);
        assertEquals(Resps.PARAM_ERROR, result);
    }

    @Test
    public void testValidateDealGroupIdIsZero() throws Throwable {
        // arrange
        DzDealBaseAction dzDealBaseAction = new DzDealBaseAction();
        IMobileContext iMobileContext = mock(IMobileContext.class);
        DealBaseReq dealBaseReq = new DealBaseReq();
        dealBaseReq.setDealgroupid(0);
        // act
        IMobileResponse result = dzDealBaseAction.validate(dealBaseReq, iMobileContext);
        // assert
        assertNotNull(result);
        assertEquals(Resps.PARAM_ERROR, result);
    }

    @Test
    public void testValidateDealGroupIdIsPositive() throws Throwable {
        // arrange
        DzDealBaseAction dzDealBaseAction = new DzDealBaseAction();
        IMobileContext iMobileContext = mock(IMobileContext.class);
        DealBaseReq dealBaseReq = new DealBaseReq();
        dealBaseReq.setDealgroupid(1);
        // act
        IMobileResponse result = dzDealBaseAction.validate(dealBaseReq, iMobileContext);
        // assert
        assertNull(result);
    }
}
