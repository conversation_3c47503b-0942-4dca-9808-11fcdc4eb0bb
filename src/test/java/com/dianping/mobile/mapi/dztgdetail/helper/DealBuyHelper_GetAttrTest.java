package com.dianping.mobile.mapi.dztgdetail.helper;

import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.Collections;
import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class DealBuyHelper_GetAttrTest {

    /**
     * Tests the getAttr method when the attrs property of dealDTO is null.
     */
    @Test
    public void testGetAttrWhenAttrsIsNull() throws Throwable {
        // Arrange
        DealGroupDealDTO dealDTO = new DealGroupDealDTO();
        String attrName = "testAttr";
        // Act
        String result = DealBuyHelper.getAttr(dealDTO, attrName);
        // Assert
        assertEquals("", result);
    }

    /**
     * Tests the getAttr method when the attrs property of dealDTO is not null, but there is no AttrDTO object with a name equal to attrName.
     */
    @Test
    public void testGetAttrWhenNoMatchedAttr() throws Throwable {
        // Arrange
        DealGroupDealDTO dealDTO = new DealGroupDealDTO();
        AttrDTO attrDTO = new AttrDTO();
        // Set a name to avoid NullPointerException
        attrDTO.setName("unmatchedAttrName");
        dealDTO.setAttrs(Arrays.asList(attrDTO));
        String attrName = "testAttr";
        // Act
        String result = DealBuyHelper.getAttr(dealDTO, attrName);
        // Assert
        assertEquals("", result);
    }

    /**
     * Tests the getAttr method when the attrs property of dealDTO is not null, there is an AttrDTO object with a name equal to attrName, but its value property is null.
     */
    @Test
    public void testGetAttrWhenAttrValueIsNull() throws Throwable {
        // Arrange
        DealGroupDealDTO dealDTO = new DealGroupDealDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("testAttr");
        // Explicitly set value to null
        attrDTO.setValue(null);
        dealDTO.setAttrs(Arrays.asList(attrDTO));
        String attrName = "testAttr";
        // Act
        String result = DealBuyHelper.getAttr(dealDTO, attrName);
        // Assert
        assertEquals("", result);
    }

    /**
     * Tests the getAttr method when the attrs property of dealDTO is not null, there is an AttrDTO object with a name equal to attrName, and its value property is not null.
     */
    @Test
    public void testGetAttrWhenAttrValueIsNotNull() throws Throwable {
        // Arrange
        DealGroupDealDTO dealDTO = new DealGroupDealDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("testAttr");
        // Set a non-null value
        attrDTO.setValue(Collections.singletonList("testValue"));
        dealDTO.setAttrs(Arrays.asList(attrDTO));
        String attrName = "testAttr";
        // Act
        String result = DealBuyHelper.getAttr(dealDTO, attrName);
        // Assert
        assertEquals("testValue", result);
    }
}
