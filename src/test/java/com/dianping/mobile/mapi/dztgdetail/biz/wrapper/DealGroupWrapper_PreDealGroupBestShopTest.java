package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.shop.DealGroupBestShopQueryService;
import com.dianping.deal.shop.dto.BestShopReq;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import java.util.concurrent.Future;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealGroupWrapper_PreDealGroupBestShopTest {

    @InjectMocks
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private DealGroupBestShopQueryService dealGroupBestShopQueryServiceFuture;

    @Mock
    private Future mockFuture;

    private BestShopReq req;

    @Before
    public void setUp() {
        req = new BestShopReq();
    }

    @Test
    public void testPreDealGroupBestShopWhenReqIsNull() throws Throwable {
        Future result = dealGroupWrapper.preDealGroupBestShop(null);
        assertNull(result);
    }

    @Test
    public void testPreDealGroupBestShopWhenReqIsNotNullAndMethodCallSuccess() throws Throwable {
        // Simulate the service call
        when(dealGroupBestShopQueryServiceFuture.getDealGroupBestShop(req)).thenReturn(null);
        // Mock FutureFactory to return a mock Future object using mockStatic
        try (MockedStatic<FutureFactory> mockedStatic = Mockito.mockStatic(FutureFactory.class)) {
            mockedStatic.when(FutureFactory::getFuture).thenReturn(mockFuture);
            Future result = dealGroupWrapper.preDealGroupBestShop(req);
            // Now, the result should not be null
            assertNotNull(result);
            verify(dealGroupBestShopQueryServiceFuture, times(1)).getDealGroupBestShop(req);
        }
    }

    @Test
    public void testPreDealGroupBestShopWhenReqIsNotNullAndMethodCallFail() throws Throwable {
        when(dealGroupBestShopQueryServiceFuture.getDealGroupBestShop(req)).thenThrow(new RuntimeException());
        Future result = dealGroupWrapper.preDealGroupBestShop(req);
        assertNull(result);
        verify(dealGroupBestShopQueryServiceFuture, times(1)).getDealGroupBestShop(req);
    }
}
