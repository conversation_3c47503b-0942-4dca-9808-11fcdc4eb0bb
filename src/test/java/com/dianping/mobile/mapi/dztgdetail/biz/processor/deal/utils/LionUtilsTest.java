package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mockStatic;

/**
 * LionUtils 单元测试类
 *
 * <AUTHOR>
 * @date 2025/3/5
 */
@RunWith(MockitoJUnitRunner.class)
public class LionUtilsTest {

    private MockedStatic<Lion> lionMockedStatic;
    private MockedStatic<Environment> environmentMockedStatic;

    @Before
    public void setUp() {
        lionMockedStatic = mockStatic(Lion.class);
        environmentMockedStatic = mockStatic(Environment.class);
    }

    @After
    public void tearDown() {
        lionMockedStatic.close();
        environmentMockedStatic.close();
    }

    /**
     * 测试 rhinoLimitSwitch 方法，当 Lion.getBoolean 返回 true 时
     */
    @Test
    public void testRhinoLimitSwitchReturnTrue() {
        // arrange
        String appName = "test-app";
        String configKey = "com.sankuai.dzu.tpbase.dztgdetailweb.rhino.limit.switch.custom";

        environmentMockedStatic.when(Environment::getAppName).thenReturn(appName);
        lionMockedStatic.when(() -> Lion.getBoolean(appName, configKey, false)).thenReturn(true);

        // act
        boolean result = LionUtils.rhinoLimitSwitch();

        // assert
        assertTrue("当Lion配置返回true时，rhinoLimitSwitch应返回true", result);
    }

    /**
     * 测试 rhinoLimitSwitch 方法，当 Lion.getBoolean 返回 false 时
     */
    @Test
    public void testRhinoLimitSwitchReturnFalse() {
        // arrange
        String appName = "test-app";
        String configKey = "com.sankuai.dzu.tpbase.dztgdetailweb.rhino.limit.switch.custom";

        environmentMockedStatic.when(Environment::getAppName).thenReturn(appName);
        lionMockedStatic.when(() -> Lion.getBoolean(appName, configKey, false)).thenReturn(false);

        // act
        boolean result = LionUtils.rhinoLimitSwitch();

        // assert
        assertFalse("当Lion配置返回false时，rhinoLimitSwitch应返回false", result);
    }

    /**
     * 测试 rhinoLimitSwitch 方法，当 Environment.getAppName() 返回 null 时
     */
    @Test
    public void testRhinoLimitSwitchWithNullAppName() {
        // arrange
        String configKey = "com.sankuai.dzu.tpbase.dztgdetailweb.rhino.limit.switch.custom";

        environmentMockedStatic.when(Environment::getAppName).thenReturn(null);
        lionMockedStatic.when(() -> Lion.getBoolean(null, configKey, false)).thenReturn(false);

        // act
        boolean result = LionUtils.rhinoLimitSwitch();

        // assert
        assertFalse("当appName为null时，rhinoLimitSwitch应返回false", result);
    }

    /**
     * 测试 rhinoLimitSwitch 方法，当 Environment.getAppName() 返回空字符串时
     */
    @Test
    public void testRhinoLimitSwitchWithEmptyAppName() {
        // arrange
        String appName = "";
        String configKey = "com.sankuai.dzu.tpbase.dztgdetailweb.rhino.limit.switch.custom";

        environmentMockedStatic.when(Environment::getAppName).thenReturn(appName);
        lionMockedStatic.when(() -> Lion.getBoolean(appName, configKey, false)).thenReturn(false);

        // act
        boolean result = LionUtils.rhinoLimitSwitch();

        // assert
        assertFalse("当appName为空字符串时，rhinoLimitSwitch应返回false", result);
    }

    /**
     * 测试 rhinoLimitSwitch 方法，当 Lion.getBoolean 抛出异常时，应返回默认值 false
     */
    @Test
    public void testRhinoLimitSwitchWithException() {
        // arrange
        String appName = "test-app";
        String configKey = "com.sankuai.dzu.tpbase.dztgdetailweb.rhino.limit.switch.custom";

        environmentMockedStatic.when(Environment::getAppName).thenReturn(appName);
        lionMockedStatic.when(() -> Lion.getBoolean(appName, configKey, false))
                .thenThrow(new RuntimeException("Lion配置获取异常"));

        // act
        boolean result = LionUtils.rhinoLimitSwitch();

        // assert
        assertFalse("当Lion配置获取异常时，rhinoLimitSwitch应返回默认值false", result);
    }

    /**
     * 测试 rhinoLimitSwitch 方法，当 Environment.getAppName() 抛出异常时，应返回默认值 false
     */
    @Test
    public void testRhinoLimitSwitchWithEnvironmentException() {
        // arrange
        environmentMockedStatic.when(Environment::getAppName)
                .thenThrow(new RuntimeException("Environment获取异常"));

        // act
        boolean result = LionUtils.rhinoLimitSwitch();

        // assert
        assertFalse("当Environment获取异常时，rhinoLimitSwitch应返回默认值false", result);
    }

    /**
     * 测试 rhinoLimitSwitch 方法，验证使用的配置键是否正确
     */
    @Test
    public void testRhinoLimitSwitchConfigKey() {
        // arrange
        String appName = "mapi-dztgdetail-web";
        String expectedConfigKey = "com.sankuai.dzu.tpbase.dztgdetailweb.rhino.limit.switch.custom";

        environmentMockedStatic.when(Environment::getAppName).thenReturn(appName);
        lionMockedStatic.when(() -> Lion.getBoolean(appName, expectedConfigKey, false)).thenReturn(true);

        // act
        boolean result = LionUtils.rhinoLimitSwitch();

        // assert
        assertTrue("应该使用正确的配置键获取Lion配置", result);

        // verify
        lionMockedStatic.verify(() -> Lion.getBoolean(appName, expectedConfigKey, false));
    }

    /**
     * 测试 rhinoLimitSwitch 方法，验证默认值为 false
     */
    @Test
    public void testRhinoLimitSwitchDefaultValue() {
        // arrange
        String appName = "test-app";
        String configKey = "com.sankuai.dzu.tpbase.dztgdetailweb.rhino.limit.switch.custom";

        environmentMockedStatic.when(Environment::getAppName).thenReturn(appName);
        // 不设置Lion.getBoolean的返回值，让其使用默认值
        lionMockedStatic.when(() -> Lion.getBoolean(appName, configKey, false)).thenReturn(false);

        // act
        boolean result = LionUtils.rhinoLimitSwitch();

        // assert
        assertFalse("rhinoLimitSwitch的默认值应该是false", result);

        // verify 验证调用时使用的默认值参数
        lionMockedStatic.verify(() -> Lion.getBoolean(appName, configKey, false));
    }
}

