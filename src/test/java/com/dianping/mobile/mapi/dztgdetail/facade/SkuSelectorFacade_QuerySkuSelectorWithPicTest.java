package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DzDealSkuSelectRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.Response;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.sku.SkuSelectorWithPicVO;
import com.sankuai.dztheme.deal.DealSkuService;
import com.sankuai.dztheme.deal.res.DealSkuOptionDTO;
import com.sankuai.dztheme.deal.res.SkuAttrMetaDTO;
import com.sankuai.dztheme.deal.res.SkuItemDTO;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Collections;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import org.junit.Before;
import com.sankuai.dztheme.deal.req.SkuOptionRequest;

public class SkuSelectorFacade_QuerySkuSelectorWithPicTest {

    @InjectMocks
    private SkuSelectorFacade skuSelectorFacade = new SkuSelectorFacade();

    @Mock
    private DealSkuService dealSkuService = mock(DealSkuService.class);

    public SkuSelectorFacade_QuerySkuSelectorWithPicTest() {
        MockitoAnnotations.initMocks(this);
    }

    private DzDealSkuSelectRequest createRequestWithValidDealGroupId() {
        DzDealSkuSelectRequest request = new DzDealSkuSelectRequest();
        request.setDealgroupid("12345");
        return request;
    }

    @Test
    public void testQuerySkuSelectorWithPicDealSkuOptionIsNull() throws Throwable {
        DzDealSkuSelectRequest request = createRequestWithValidDealGroupId();
        EnvCtx envCtx = new EnvCtx();
        when(dealSkuService.querySkuOptions(any())).thenReturn(null);
        Response<SkuSelectorWithPicVO> response = skuSelectorFacade.querySkuSelectorWithPic(request, envCtx);
        assertTrue(response.isSuccess());
    }

    @Test
    public void testQuerySkuSelectorWithPicSkuItemListIsEmpty() throws Throwable {
        DzDealSkuSelectRequest request = createRequestWithValidDealGroupId();
        EnvCtx envCtx = new EnvCtx();
        DealSkuOptionDTO dealSkuOptionDTO = new DealSkuOptionDTO();
        dealSkuOptionDTO.setSkuItemList(Collections.emptyList());
        when(dealSkuService.querySkuOptions(any())).thenReturn(dealSkuOptionDTO);
        Response<SkuSelectorWithPicVO> response = skuSelectorFacade.querySkuSelectorWithPic(request, envCtx);
        assertTrue(response.isSuccess());
        assertNotNull(response.getResult());
    }

    @Test
    public void testQuerySkuSelectorWithPicSkuAttrMetaListIsEmpty() throws Throwable {
        DzDealSkuSelectRequest request = createRequestWithValidDealGroupId();
        EnvCtx envCtx = new EnvCtx();
        DealSkuOptionDTO dealSkuOptionDTO = new DealSkuOptionDTO();
        dealSkuOptionDTO.setSkuAttrMetaList(Collections.emptyList());
        when(dealSkuService.querySkuOptions(any())).thenReturn(dealSkuOptionDTO);
        Response<SkuSelectorWithPicVO> response = skuSelectorFacade.querySkuSelectorWithPic(request, envCtx);
        assertTrue(response.isSuccess());
        assertNotNull(response.getResult());
    }

    @Test
    public void testQuerySkuSelectorWithPicAllListsAreEmpty() throws Throwable {
        DzDealSkuSelectRequest request = createRequestWithValidDealGroupId();
        EnvCtx envCtx = new EnvCtx();
        DealSkuOptionDTO dealSkuOptionDTO = new DealSkuOptionDTO();
        dealSkuOptionDTO.setSkuItemList(Collections.emptyList());
        dealSkuOptionDTO.setSkuAttrMetaList(Collections.emptyList());
        when(dealSkuService.querySkuOptions(any())).thenReturn(dealSkuOptionDTO);
        Response<SkuSelectorWithPicVO> response = skuSelectorFacade.querySkuSelectorWithPic(request, envCtx);
        assertTrue(response.isSuccess());
        assertNotNull(response.getResult());
    }

    @Test
    public void testQuerySkuSelectorWithPicBuildSalesAttrToSkuBasicInfoIsNull() throws Throwable {
        DzDealSkuSelectRequest request = createRequestWithValidDealGroupId();
        EnvCtx envCtx = new EnvCtx();
        DealSkuOptionDTO dealSkuOptionDTO = new DealSkuOptionDTO();
        dealSkuOptionDTO.setSkuItemList(Collections.singletonList(new SkuItemDTO()));
        when(dealSkuService.querySkuOptions(any())).thenReturn(dealSkuOptionDTO);
        Response<SkuSelectorWithPicVO> response = skuSelectorFacade.querySkuSelectorWithPic(request, envCtx);
        assertTrue(response.isSuccess());
    }

    @Test
    public void testQuerySkuSelectorWithPicBuildSkuSalesAttrWithPicIsNull() throws Throwable {
        DzDealSkuSelectRequest request = createRequestWithValidDealGroupId();
        EnvCtx envCtx = new EnvCtx();
        DealSkuOptionDTO dealSkuOptionDTO = new DealSkuOptionDTO();
        dealSkuOptionDTO.setSkuAttrMetaList(Collections.singletonList(new SkuAttrMetaDTO()));
        when(dealSkuService.querySkuOptions(any())).thenReturn(dealSkuOptionDTO);
        Response<SkuSelectorWithPicVO> response = skuSelectorFacade.querySkuSelectorWithPic(request, envCtx);
        assertTrue(response.isSuccess());
    }
}
