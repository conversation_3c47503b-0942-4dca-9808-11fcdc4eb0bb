package com.dianping.mobile.mapi.dztgdetail.biz.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;
import com.dianping.deal.shop.dto.DealGroupShop;
import com.dianping.general.unified.search.api.productshopsearch.GeneralProductShopSearchService;
import com.dianping.general.unified.search.api.productshopsearch.dto.ProductShopSearchDTO;
import com.dianping.general.unified.search.api.productshopsearch.dto.RetrievalInfoDTO;
import com.dianping.general.unified.search.api.productshopsearch.enums.ProductShopRetrievalExtendFieldEnum;
import com.dianping.general.unified.search.api.productshopsearch.response.GeneralProductShopSearchResponse;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperCacheWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.RelatedRecommendCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedRecommendReq;
import java.util.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductShopSearchComponentSearchNearestShopConvertTest {

    @Mock
    private GeneralProductShopSearchService generalProductShopSearchService;

    @Mock
    private MapperCacheWrapper mapperCacheWrapper;

    @InjectMocks
    private ProductShopSearchComponent productShopSearchComponent;

    // Helper method to create a valid RelatedRecommendCtx
    private RelatedRecommendCtx createValidCtx() {
        RelatedRecommendCtx ctx = new RelatedRecommendCtx();
        RelatedRecommendReq req = new RelatedRecommendReq();
        req.setCityId(1);
        ctx.setReq(req);
        EnvCtx envCtx = new EnvCtx();
        ctx.setEnvCtx(envCtx);
        return ctx;
    }

    // Helper method to create test DTO
    private ProductShopSearchDTO createTestDTO(long productId, long shopId, String shopName, String distance) {
        ProductShopSearchDTO dto = new ProductShopSearchDTO();
        dto.setProductId(productId);
        dto.setShopId(shopId);
        List<RetrievalInfoDTO> retrievalInfos = new ArrayList<>();
        if (shopName != null) {
            RetrievalInfoDTO nameInfo = new RetrievalInfoDTO();
            nameInfo.setRetrievalExtendField(ProductShopRetrievalExtendFieldEnum.SHOP_NAME.getCode());
            nameInfo.setValues(Collections.singletonList(shopName));
            retrievalInfos.add(nameInfo);
        }
        if (distance != null) {
            RetrievalInfoDTO distanceInfo = new RetrievalInfoDTO();
            distanceInfo.setRetrievalExtendField(ProductShopRetrievalExtendFieldEnum.SHOP_DISTANCE_IN_METER.getCode());
            distanceInfo.setValues(Collections.singletonList(distance));
            retrievalInfos.add(distanceInfo);
        }
        dto.setRetrievalInfos(retrievalInfos);
        return dto;
    }

    /**
     * Test when response contains empty result list
     */
    @Test
    public void testSearchNearestShopConvertEmptyResult() throws Throwable {
        // arrange
        RelatedRecommendCtx ctx = createValidCtx();
        List<Long> dealIds = Arrays.asList(1L, 2L);
        GeneralProductShopSearchResponse response = GeneralProductShopSearchResponse.createSuccessResponse(Collections.emptyList());
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(response);
        // act
        Map<Integer, DealGroupShop> result = productShopSearchComponent.searchNearestShopConvert(ctx, dealIds);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test with valid response containing one shop
     */
    @Test
    public void testSearchNearestShopConvertSingleShop() throws Throwable {
        // arrange
        RelatedRecommendCtx ctx = createValidCtx();
        List<Long> dealIds = Arrays.asList(1L);
        ProductShopSearchDTO dto = createTestDTO(1L, 100L, "Test Shop", "100");
        GeneralProductShopSearchResponse response = GeneralProductShopSearchResponse.createSuccessResponse(Collections.singletonList(dto));
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(response);
        // act
        Map<Integer, DealGroupShop> result = productShopSearchComponent.searchNearestShopConvert(ctx, dealIds);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealGroupShop shop = result.get(1);
        assertNotNull(shop);
        assertEquals(1, shop.getDealGroupId());
        assertEquals(100L, shop.getLongShopId());
        assertEquals("Test Shop", shop.getShopName());
        assertEquals("100", shop.getDistance());
    }

    /**
     * Test with multiple shops in response
     */
    @Test
    public void testSearchNearestShopConvertMultipleShops() throws Throwable {
        // arrange
        RelatedRecommendCtx ctx = createValidCtx();
        List<Long> dealIds = Arrays.asList(1L, 2L);
        ProductShopSearchDTO dto1 = createTestDTO(1L, 100L, "Shop 1", "100");
        ProductShopSearchDTO dto2 = createTestDTO(2L, 200L, "Shop 2", "200");
        GeneralProductShopSearchResponse response = GeneralProductShopSearchResponse.createSuccessResponse(Arrays.asList(dto1, dto2));
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(response);
        // act
        Map<Integer, DealGroupShop> result = productShopSearchComponent.searchNearestShopConvert(ctx, dealIds);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        DealGroupShop shop1 = result.get(1);
        assertNotNull(shop1);
        assertEquals(1, shop1.getDealGroupId());
        assertEquals("Shop 1", shop1.getShopName());
        DealGroupShop shop2 = result.get(2);
        assertNotNull(shop2);
        assertEquals(2, shop2.getDealGroupId());
        assertEquals("Shop 2", shop2.getShopName());
    }

    /**
     * Test when retrievalInfos is missing required fields
     */
    @Test
    public void testSearchNearestShopConvertMissingFields() throws Throwable {
        // arrange
        RelatedRecommendCtx ctx = createValidCtx();
        List<Long> dealIds = Arrays.asList(1L);
        ProductShopSearchDTO dto = new ProductShopSearchDTO();
        dto.setProductId(1L);
        dto.setShopId(100L);
        dto.setRetrievalInfos(Collections.emptyList());
        GeneralProductShopSearchResponse response = GeneralProductShopSearchResponse.createSuccessResponse(Collections.singletonList(dto));
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(response);
        // act
        Map<Integer, DealGroupShop> result = productShopSearchComponent.searchNearestShopConvert(ctx, dealIds);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealGroupShop shop = result.get(1);
        assertNotNull(shop);
        assertEquals(1, shop.getDealGroupId());
        assertEquals(100L, shop.getLongShopId());
        assertNull(shop.getShopName());
    }

    /**
     * Test when retrievalInfos is null
     */
    @Test
    public void testSearchNearestShopConvertNullRetrievalInfos() throws Throwable {
        // arrange
        RelatedRecommendCtx ctx = createValidCtx();
        List<Long> dealIds = Arrays.asList(1L);
        ProductShopSearchDTO dto = new ProductShopSearchDTO();
        dto.setProductId(1L);
        dto.setShopId(100L);
        dto.setRetrievalInfos(null);
        GeneralProductShopSearchResponse response = GeneralProductShopSearchResponse.createSuccessResponse(Collections.singletonList(dto));
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(response);
        // act
        Map<Integer, DealGroupShop> result = productShopSearchComponent.searchNearestShopConvert(ctx, dealIds);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealGroupShop shop = result.get(1);
        assertNotNull(shop);
        assertEquals(1, shop.getDealGroupId());
        assertEquals(100L, shop.getLongShopId());
        assertNull(shop.getShopName());
    }

    /**
     * Test with special characters in shop name
     */
    @Test
    public void testSearchNearestShopConvertSpecialChars() throws Throwable {
        // arrange
        RelatedRecommendCtx ctx = createValidCtx();
        List<Long> dealIds = Arrays.asList(1L);
        ProductShopSearchDTO dto = createTestDTO(1L, 100L, "Shop & Café - 测试", "100");
        GeneralProductShopSearchResponse response = GeneralProductShopSearchResponse.createSuccessResponse(Collections.singletonList(dto));
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(response);
        // act
        Map<Integer, DealGroupShop> result = productShopSearchComponent.searchNearestShopConvert(ctx, dealIds);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealGroupShop shop = result.get(1);
        assertEquals("Shop & Café - 测试", shop.getShopName());
    }

    /**
     * Test with duplicate dealGroupIds - later entry should overwrite
     */
    @Test
    public void testSearchNearestShopConvertDuplicateDealGroupIds() throws Throwable {
        // arrange
        RelatedRecommendCtx ctx = createValidCtx();
        List<Long> dealIds = Arrays.asList(1L, 1L);
        ProductShopSearchDTO dto1 = createTestDTO(1L, 100L, "Shop 1", "100");
        ProductShopSearchDTO dto2 = createTestDTO(1L, 200L, "Shop 2", "200");
        GeneralProductShopSearchResponse response = GeneralProductShopSearchResponse.createSuccessResponse(Arrays.asList(dto1, dto2));
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(response);
        // act
        Map<Integer, DealGroupShop> result = productShopSearchComponent.searchNearestShopConvert(ctx, dealIds);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealGroupShop shop = result.get(1);
        assertNotNull(shop);
        // Should be the last one processed
        assertEquals(200L, shop.getLongShopId());
        assertEquals("Shop 2", shop.getShopName());
    }

    /**
     * Test with retrieval info containing empty values list
     */
    @Test
    public void testSearchNearestShopConvertEmptyValues() throws Throwable {
        // arrange
        RelatedRecommendCtx ctx = createValidCtx();
        List<Long> dealIds = Arrays.asList(1L);
        ProductShopSearchDTO dto = new ProductShopSearchDTO();
        dto.setProductId(1L);
        dto.setShopId(100L);
        RetrievalInfoDTO nameInfo = new RetrievalInfoDTO();
        nameInfo.setRetrievalExtendField(ProductShopRetrievalExtendFieldEnum.SHOP_NAME.getCode());
        // Empty values list
        nameInfo.setValues(Collections.emptyList());
        dto.setRetrievalInfos(Collections.singletonList(nameInfo));
        GeneralProductShopSearchResponse response = GeneralProductShopSearchResponse.createSuccessResponse(Collections.singletonList(dto));
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(response);
        // act
        Map<Integer, DealGroupShop> result = productShopSearchComponent.searchNearestShopConvert(ctx, dealIds);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealGroupShop shop = result.get(1);
        assertNotNull(shop);
        // Should be null due to empty values
        assertNull(shop.getShopName());
    }

    /**
     * Test with large productId values
     */
    @Test
    public void testSearchNearestShopConvertLargeProductId() throws Throwable {
        // arrange
        RelatedRecommendCtx ctx = createValidCtx();
        List<Long> dealIds = Arrays.asList(Long.MAX_VALUE);
        ProductShopSearchDTO dto = createTestDTO(Long.MAX_VALUE, 100L, "Test Shop", "100");
        GeneralProductShopSearchResponse response = GeneralProductShopSearchResponse.createSuccessResponse(Collections.singletonList(dto));
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(response);
        // act
        Map<Integer, DealGroupShop> result = productShopSearchComponent.searchNearestShopConvert(ctx, dealIds);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealGroupShop shop = result.get((int) Long.MAX_VALUE);
        assertNotNull(shop);
        assertEquals((int) Long.MAX_VALUE, shop.getDealGroupId());
    }

    /**
     * Test when ctx has valid request but no coordinates and no cityId
     */
    @Test
    public void testSearchNearestShopConvertNoLocationInfo() throws Throwable {
        // arrange
        RelatedRecommendCtx ctx = new RelatedRecommendCtx();
        RelatedRecommendReq req = new RelatedRecommendReq();
        // No coordinates and no cityId set
        ctx.setReq(req);
        EnvCtx envCtx = new EnvCtx();
        ctx.setEnvCtx(envCtx);
        List<Long> dealIds = Arrays.asList(1L);
        ProductShopSearchDTO dto = createTestDTO(1L, 100L, "Test Shop", "100");
        GeneralProductShopSearchResponse response = GeneralProductShopSearchResponse.createSuccessResponse(Collections.singletonList(dto));
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(response);
        // act
        Map<Integer, DealGroupShop> result = productShopSearchComponent.searchNearestShopConvert(ctx, dealIds);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealGroupShop shop = result.get(1);
        assertNotNull(shop);
        assertEquals("Test Shop", shop.getShopName());
    }

    /**
     * Test when ctx has coordinates
     */
    @Test
    public void testSearchNearestShopConvertWithCoordinates() throws Throwable {
        // arrange
        RelatedRecommendCtx ctx = new RelatedRecommendCtx();
        RelatedRecommendReq req = new RelatedRecommendReq();
        req.setUserLat(39.9042);
        req.setUserLng(116.4074);
        ctx.setReq(req);
        EnvCtx envCtx = new EnvCtx();
        ctx.setEnvCtx(envCtx);
        List<Long> dealIds = Arrays.asList(1L);
        ProductShopSearchDTO dto = createTestDTO(1L, 100L, "Test Shop", "100");
        GeneralProductShopSearchResponse response = GeneralProductShopSearchResponse.createSuccessResponse(Collections.singletonList(dto));
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(response);
        // act
        Map<Integer, DealGroupShop> result = productShopSearchComponent.searchNearestShopConvert(ctx, dealIds);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealGroupShop shop = result.get(1);
        assertNotNull(shop);
        assertEquals("Test Shop", shop.getShopName());
    }
}
