package com.dianping.mobile.mapi.dztgdetail.biz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.lion.client.Lion;
import com.dianping.lion.common.util.JsonUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.CardStyleConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.ExpResultConfig;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum.MEITUAN_APP;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DouHuServiceTest {

    @InjectMocks
    private DouHuService douHuService;

    @Mock
    private DouHuBiz douHuBiz;

    @Mock
    private DealCtx ctx;

    private MockedStatic<Lion> lionMockedStatic;
    private MockedStatic<LionConfigUtils> lionConfigUtilsMockedStatic;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        lionMockedStatic = mockStatic(Lion.class);
        lionConfigUtilsMockedStatic = mockStatic(LionConfigUtils.class);
    }

    @After
    public void tearDown() {
        lionMockedStatic.close();
        lionConfigUtilsMockedStatic.close();
    }

    @Test
    public void testHitEnableCardStyleV2WhenModuleAbConfigIsNull() {
        assertFalse(douHuService.hitEnableCardStyleV2(null));
    }

    @Test
    public void testHitEnableCardStyleV2WhenExpResultNotInExpectedSks() {
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpId("test");
        abConfig.setExpResult("a");
        moduleAbConfig.setConfigs(Arrays.asList(abConfig));

        Map<String, String> expId2ExpectedSks = new HashMap<>();
        expId2ExpectedSks.put("test", "b");
        lionMockedStatic.when(() -> Lion.getMap(anyString(), anyString(), any(), any())).thenReturn(expId2ExpectedSks);
        assertFalse(douHuService.hitEnableCardStyleV2(moduleAbConfig));
    }

    @Test
    public void testCompareSameShopPriceStyleAbConfig() {
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpId("test");
        abConfig.setExpResult("a");
        moduleAbConfig.setConfigs(Arrays.asList(abConfig));

        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(200100);
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(501);
        ctx.setChannelDTO(channelDTO);
        douHuService.getCompareSameShopPriceStyleAbConfigByDealCtx(ctx);
        douHuService.getCompareSameShopPriceStyleAbConfigByEnvCtx(501, envCtx);
        ExpResultConfig expResultConfig = new ExpResultConfig();
        expResultConfig.setEnableStyle(true);
        expResultConfig.setAllPassStyle("t1");
        douHuService.getTabStyle(expResultConfig, ctx, moduleAbConfig);

        List<AbConfig> configs = Lists.newArrayList();
        configs.add(abConfig);
        moduleAbConfig.setConfigs(configs);
        douHuService.getTabStyle(expResultConfig, ctx, moduleAbConfig);

        Map<String, String> key2Style = Maps.newHashMap();
        key2Style.put("501","t1");
        expResultConfig.setKey2Style(key2Style);
        expResultConfig.setAllPassStyle("");
        douHuService.getTabStyle(expResultConfig, ctx, moduleAbConfig);


        douHuService.getTabStyle(expResultConfig, ctx, null);

        assertFalse(douHuService.hitEnableCardStyleV2(moduleAbConfig));
    }

    @Test
    public void testEnableCardStyleV2() {
        CardStyleConfig cardStyleConfig = JsonUtils.fromJson("{\n" +
                "    \"enableCardStyle\": true,\n" +
                "    \"enableClientType\": [\n" +
                "        1,\n" +
                "        4,\n" +
                "        11\n" +
                "    ],\n" +
                "    \"category2ExpId\": {\n" +
                "        \"mt502\": \"exp002675\",\n" +
                "        \"dp502\": \"exp003168\"\n" +
                "    },\n" +
                "    \"allPass\": false\n" +
                "}", CardStyleConfig.class);
        lionMockedStatic.when(() -> Lion.getBean("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.card.style.v2.config", CardStyleConfig.class)).thenReturn(cardStyleConfig);

        CardStyleConfig miniCardStyleConfig = JsonUtils.fromJson("{\n" +
                "    \"enableCardStyle\": true,\n" +
                "    \"enableClientType\": [\n" +
                "        2,\n" +
                "        5,\n" +
                "        11\n" +
                "    ],\n" +
                "    \"category2ExpId\": {\n" +
                "        \"mt502\": \"exp002675\",\n" +
                "        \"dp502\": \"exp003168\"\n" +
                "    },\n" +
                "    \"allPass\": false\n" +
                "}", CardStyleConfig.class);
        lionMockedStatic.when(() -> Lion.getBean("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.card.style.non.app.config", CardStyleConfig.class)).thenReturn(miniCardStyleConfig);

        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(MEITUAN_APP);
        envCtx.setClientType(200502);
        envCtx.setVersion("12.11.400");

        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpId("exp002675");
        abConfig.setExpResult("b");
        moduleAbConfig.setConfigs(Arrays.asList(abConfig));
        ModuleAbConfig result = douHuService.enableCardStyleV2(envCtx, 50200, "0.5.7");
        assertTrue(Objects.isNull(result));
    }

    @Test
    public void testHitGetGlassDealDetailExpResult() {
        MockitoAnnotations.initMocks(this);
        // 准备环境上下文
        EnvCtx envCtx = new EnvCtx();
        envCtx.setUuid("1");
        envCtx.setDztgClientTypeEnum(MEITUAN_APP);

        // 模拟ModuleAbConfig和DouHuBiz的行为
        ModuleAbConfig moduleAbConfig = mock(ModuleAbConfig.class);

        // 执行测试方法
        String result = douHuService.getGlassDealDetailExpResult(1,envCtx);

        // 验证结果
        assertNull(result);
    }

    /**
     * 测试场景：当categoryId不在允许的列表中时，应返回null
     */
    @Test
    public void testGetAbResultForComparePriceAssistant_CategoryIdNotAllowed() {
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getCategoryId()).thenReturn(999); // 不在允许的列表中
        lionMockedStatic.when(() -> Lion.getBoolean(anyString(), anyString(), anyBoolean()))
                        .thenReturn(true);
        lionConfigUtilsMockedStatic.when(LionConfigUtils::getComparePriceAssistantExpConfig).thenReturn(new HashMap<String, String>() {
            {
                put("mt1", "expId");
            }
        });

        ModuleAbConfig result = douHuService.getAbResultForComparePriceAssistant(ctx);

        assertNull(result);
    }

    /**
     * 测试场景：当comparePriceSwitch为false时，应返回null
     */
    @Test
    public void testGetAbResultForPriceTrend_CompareComparePriceSwitchOff() {
        lionMockedStatic.when(() -> Lion.getBoolean(anyString(), anyString(), anyBoolean()))
                .thenReturn(true);

        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isMt()).thenReturn(true);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setUnionId("unionId");
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(ctx.getCategoryId()).thenReturn(1); // 在允许的列表中
        lionConfigUtilsMockedStatic.when(LionConfigUtils::getComparePriceAssistantExpConfig).thenReturn(new HashMap<String, String>() {
            {
                put("mt1", "expId");
            }
        });

        when(douHuBiz.getAbByUnionIdAndExpId(anyString(), anyString(), anyString(), anyBoolean())).thenReturn(null);

        ModuleAbConfig result = douHuService.getAbResultForComparePriceAssistant(ctx);

        assertNull(result);
    }

    /**
     * 测试场景：当所有条件满足时，应调用douHuBiz.getAbByUnionId并返回结果
     */
    @Test
    public void testGetAbResultForComparePriceAssistant_AllConditionsMet() {
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.getCategoryId()).thenReturn(1); // 在允许的列表中
        when(ctx.isMt()).thenReturn(true);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setUnionId("unionId");
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        lionConfigUtilsMockedStatic.when(LionConfigUtils::getComparePriceAssistantExpConfig).thenReturn(new HashMap<String, String>() {
            {
                put("mt1", "expId");
            }
        });
        lionMockedStatic.when(() -> Lion.getBoolean(LionConstants.APP_KEY, LionConstants.COMPARE_PRICE_AB_TEST_SWITCH, false)).thenReturn(true);

        ModuleAbConfig expectedModuleAbConfig = new ModuleAbConfig();
        when(douHuBiz.getAbByUnionIdAndExpId(anyString(), anyString(), anyString(), anyBoolean())).thenReturn(expectedModuleAbConfig);

        ModuleAbConfig result = douHuService.getAbResultForComparePriceAssistant(ctx);

        assertEquals(expectedModuleAbConfig, result);
    }

    @Test
    public void testGetPhotoAbResult() {
        String moduleAbConfigsJson = "[{\"key\":\"DPPhotoDealDetailExp\",\"configs\":[{\"expId\":\"EXP2024092000001\",\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"3778410d-e0a5-47a0-b3f8-849a78c92228\\\",\\\"ab_id\\\":\\\"EXP2024092000001_b\\\"}\"}]}]";
        List<ModuleAbConfig> moduleAbConfigs = JSON.parseObject(moduleAbConfigsJson, new TypeReference<List<ModuleAbConfig>>() {});
        when(ctx.getModuleAbConfigs()).thenReturn(moduleAbConfigs);
        when(ctx.isMt()).thenReturn(false);
        String photoAbResult = douHuService.getPhotoAbResult(ctx);
        assertNotNull(photoAbResult);
    }

    @Test
    public void testGetPhotoAbResultV2() {
        String moduleAbConfigsJson = "[{\"key\":\"DPPhotoDealDetailExpV2\",\"configs\":[{\"expId\":\"EXP2024092000001\",\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"3778410d-e0a5-47a0-b3f8-849a78c92228\\\",\\\"ab_id\\\":\\\"EXP2024092000001_b\\\"}\"}]}]";
        List<ModuleAbConfig> moduleAbConfigs = JSON.parseObject(moduleAbConfigsJson, new TypeReference<List<ModuleAbConfig>>() {});
        when(ctx.getModuleAbConfigs()).thenReturn(moduleAbConfigs);
        when(ctx.isMt()).thenReturn(false);
        String photoAbResult = douHuService.getPhotoAbResult(ctx);
        assertNotNull(photoAbResult);
    }
    /**
     * Test getAbResultByExpKey when expKey doesn't match
     * Should return default group value "a"
     */
    @Test
    public void testGetAbResultByExpKey_NoMatchingKey() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        String expKey = "testKey";
        List<ModuleAbConfig> moduleAbConfigs = Lists.newArrayList();
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setKey("differentKey");
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("someResult");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        moduleAbConfigs.add(moduleAbConfig);
        ctx.setModuleAbConfigs(moduleAbConfigs);
        // act
        String result = douHuService.getAbResultByExpKey(ctx, expKey);
        // assert
        assertEquals("a", result);
    }

    /**
     * Test getAbResultByExpKey when configs list is empty
     * Should return default group value "a"
     */
    @Test
    public void testGetAbResultByExpKey_EmptyConfigs() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        String expKey = "testKey";
        List<ModuleAbConfig> moduleAbConfigs = Lists.newArrayList();
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setKey(expKey);
        moduleAbConfig.setConfigs(Collections.emptyList());
        moduleAbConfigs.add(moduleAbConfig);
        ctx.setModuleAbConfigs(moduleAbConfigs);
        // act
        String result = douHuService.getAbResultByExpKey(ctx, expKey);
        // assert
        assertEquals("a", result);
    }
    /**
     * Test getAbResultByExpKey when expKey matches and configs exists
     * Should return the first config's expResult
     */
    @Test
    public void testGetAbResultByExpKey_MatchingKeyWithConfigs() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        String expKey = "testKey";
        String expectedResult = "testResult";
        List<ModuleAbConfig> moduleAbConfigs = Lists.newArrayList();
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setKey(expKey);
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult(expectedResult);
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        moduleAbConfigs.add(moduleAbConfig);
        ctx.setModuleAbConfigs(moduleAbConfigs);
        // act
        String result = douHuService.getAbResultByExpKey(ctx, expKey);
        // assert
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetTabStyleAllPassStyleIsBlankAndKey2StyleIsNotEmptyAndMatch() throws Throwable {
        ExpResultConfig expResultConfig = new ExpResultConfig();
        expResultConfig.setEnableStyle(true);
        // Adjusted the key to match the format "expId_expResult"
        expResultConfig.setKey2Style(Collections.singletonMap("1_testStyle", "testStyle"));
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpId("1");
        // This is crucial for matching
        abConfig.setExpResult("testStyle");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        assertEquals("testStyle", douHuService.getTabStyle(expResultConfig, 1, moduleAbConfig));
    }

    /**
     * 测试场景：当ModuleAbConfig为null时，应返回false
     */
    @Test
    public void testShowPriceTrend_WhenModuleAbConfigIsNull() {
        // arrange
        DealCtx ctx = mock(DealCtx.class);

        lionMockedStatic.when(() -> Lion.getBoolean(anyString(), anyString(), anyBoolean()))
                .thenReturn(false);

        // act
        boolean result = douHuService.showPriceTrend(ctx);

        // assert
        assertFalse(result);
    }

    /**
     * 测试场景：当ModuleAbConfig不为null，但configs为空时，应返回false
     */
    @Test
    public void testShowPriceTrend_WhenConfigsIsEmpty() {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getCategoryId()).thenReturn(1);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setUnionId("unionId");
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        lionMockedStatic.when(() -> Lion.getBoolean(anyString(), anyString(), anyBoolean()))
                .thenReturn(true);
        lionConfigUtilsMockedStatic.when(LionConfigUtils::getComparePriceAssistantExpConfig)
                .thenReturn(new HashMap<String, String>() {
                    {
                        put("mt1", "expId");
                    }
                });
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setConfigs(new ArrayList<>());
        when(douHuBiz.getAbByUnionIdAndExpId(anyString(), anyString(), anyString(), anyBoolean()))
                .thenReturn(moduleAbConfig);

        // act
        boolean result = douHuService.showPriceTrend(ctx);

        // assert
        assertFalse(result);
    }

    /**
     * 测试场景：当ModuleAbConfig不为null，configs不为空，但expResult不为d或c时，应返回false
     */
    @Test
    public void testShowPriceTrend_WhenExpResultIsNotDOrC() {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getCategoryId()).thenReturn(1);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setUnionId("unionId");
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        lionMockedStatic.when(() -> Lion.getBoolean(anyString(), anyString(), anyBoolean()))
                .thenReturn(true);
        lionConfigUtilsMockedStatic.when(LionConfigUtils::getComparePriceAssistantExpConfig)
                .thenReturn(new HashMap<String, String>() {
                    {
                        put("mt1", "expId");
                    }
                });
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        List<AbConfig> abConfigs = new ArrayList<>();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("b");
        abConfigs.add(abConfig);
        moduleAbConfig.setConfigs(abConfigs);
        when(douHuBiz.getAbByUnionIdAndExpId(anyString(), anyString(), anyString(), anyBoolean()))
                .thenReturn(moduleAbConfig);
        // act
        boolean result = douHuService.showPriceTrend(ctx);

        // assert
        assertFalse(result);
    }

    /**
     * 测试场景：当ModuleAbConfig不为null，configs不为空，且expResult为d时，应返回true
     */
    @Test
    public void testShowPriceTrend_WhenExpResultIsD() {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getCategoryId()).thenReturn(1);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setUnionId("unionId");
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        lionMockedStatic.when(() -> Lion.getBoolean(anyString(), anyString(), anyBoolean()))
                .thenReturn(true);
        lionConfigUtilsMockedStatic.when(LionConfigUtils::getComparePriceAssistantExpConfig)
                .thenReturn(new HashMap<String, String>() {
                    {
                        put("mt1", "expId");
                    }
                });
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        List<AbConfig> abConfigs = new ArrayList<>();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("d");
        abConfigs.add(abConfig);
        moduleAbConfig.setConfigs(abConfigs);
        when(douHuBiz.getAbByUnionIdAndExpId(anyString(), anyString(), anyString(), anyBoolean()))
                .thenReturn(moduleAbConfig);
        // act
        boolean result = douHuService.showPriceTrend(ctx);

        // assert
        assertTrue(result);
    }

    /**
     * 测试场景：当ModuleAbConfig不为null，configs不为空，且expResult为c时，应返回true
     */
    @Test
    public void testShowPriceTrend_WhenExpResultIsC() {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getCategoryId()).thenReturn(1);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setUnionId("unionId");
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        lionMockedStatic.when(() -> Lion.getBoolean(anyString(), anyString(), anyBoolean()))
                .thenReturn(true);
        lionConfigUtilsMockedStatic.when(LionConfigUtils::getComparePriceAssistantExpConfig)
                .thenReturn(new HashMap<String, String>() {
                    {
                        put("mt1", "expId");
                    }
                });
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        List<AbConfig> abConfigs = new ArrayList<>();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("c");
        abConfigs.add(abConfig);
        moduleAbConfig.setConfigs(abConfigs);
        when(douHuBiz.getAbByUnionIdAndExpId(anyString(), anyString(), anyString(), anyBoolean()))
                .thenReturn(moduleAbConfig);
        // act
        boolean result = douHuService.showPriceTrend(ctx);

        // assert
        assertTrue(result);
    }

    /**
     * 测试场景：当AB测试配置为空时，应返回false
     */
    @Test
    public void testShowComparePrice_WhenAbConfigIsNull() {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getCategoryId()).thenReturn(1);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setUnionId("unionId");
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        lionMockedStatic.when(() -> Lion.getBoolean(anyString(), anyString(), anyBoolean()))
                .thenReturn(true);
        lionConfigUtilsMockedStatic.when(LionConfigUtils::getComparePriceAssistantExpConfig)
                .thenReturn(new HashMap<String, String>() {
                    {
                        put("mt1", "expId");
                    }
                });
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setConfigs(new ArrayList<>());
        when(douHuBiz.getAbByUnionIdAndExpId(anyString(), anyString(), anyString(), anyBoolean()))
                .thenReturn(moduleAbConfig);

        boolean result = douHuService.showComparePrice(ctx);

        assertFalse(result);
    }

    /**
     * 测试场景：当AB测试配置非空且configs非空且expResult为"c"时，应返回true
     */
    @Test
    public void testShowComparePrice_WhenExpResultIsC() {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getCategoryId()).thenReturn(1);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setUnionId("unionId");
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        lionMockedStatic.when(() -> Lion.getBoolean(anyString(), anyString(), anyBoolean()))
                .thenReturn(true);
        lionConfigUtilsMockedStatic.when(LionConfigUtils::getComparePriceAssistantExpConfig)
                .thenReturn(new HashMap<String, String>() {
                    {
                        put("mt1", "expId");
                    }
                });
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        List<AbConfig> abConfigs = new ArrayList<>();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("c");
        abConfigs.add(abConfig);
        moduleAbConfig.setConfigs(abConfigs);
        when(douHuBiz.getAbByUnionIdAndExpId(anyString(), anyString(), anyString(), anyBoolean()))
                .thenReturn(moduleAbConfig);
        boolean result = douHuService.showComparePrice(ctx);

        assertTrue(result);
    }

    /**
     * 测试 hitEnableCardStyleV2 方法，当 ModuleAbConfig 为 null 时
     */
    @Test
    public void testHitEnableCardStyleV2_ModuleAbConfigIsNull() {
        // arrange
        ModuleAbConfig moduleAbConfig = null;
        String requestSource = "caixi";

        // act
        boolean result = douHuService.hitEnableCardStyleV2(moduleAbConfig, requestSource);

        // assert
        assertFalse(result);
    }

    /**
     * 测试 hitEnableCardStyleV2 方法，当 requestSource 来自 TRADE_SNAPSHOT 时
     */
    @Test
    public void testHitEnableCardStyleV2_RequestSourceIsTradeSnapshot() {
        // arrange
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpId("testExpId");
        abConfig.setExpResult("d");
        moduleAbConfig.setConfigs(Arrays.asList(abConfig));
        String requestSource = "trade_snapshot";

        // act
        boolean result = douHuService.hitEnableCardStyleV2(moduleAbConfig, requestSource);

        // assert
        assertTrue(result);
    }

    /**
     * 测试 hitEnableCardStyleV2 方法，当 expResult 包含在 expId2ExpectedSks 映射中时
     */
    @Test
    public void testHitEnableCardStyleV2_ExpResultIsContained() {
        // arrange
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpId("testExpId");
        abConfig.setExpResult("b");
        moduleAbConfig.setConfigs(Arrays.asList(abConfig));
        String requestSource = "OTHER_SOURCE";

        // act
        boolean result = douHuService.hitEnableCardStyleV2(moduleAbConfig, requestSource);

        // assert
        assertTrue(result);
    }

    /**
     * 测试 hitEnableCardStyleV2 方法，当 expResult 不包含在 expId2ExpectedSks 映射中时
     */
    @Test
    public void testHitEnableCardStyleV2_ExpResultIsNotContained() {
        // arrange
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpId("testExpId");
        abConfig.setExpResult("d");
        moduleAbConfig.setConfigs(Arrays.asList(abConfig));
        String requestSource = "OTHER_SOURCE";

        // act
        boolean result = douHuService.hitEnableCardStyleV2(moduleAbConfig, requestSource);

        // assert
        assertFalse(result);
    }
    
    @Test
    public void testGetNativeDealDetailAbTestResult(){
        EnvCtx ctx = new EnvCtx();
        ctx.setDztgClientTypeEnum( DztgClientTypeEnum.MEITUAN_APP);
        ctx.setUnionId("unionId");
        ModuleAbConfig result = douHuService.getNativeDealDetailAbTestResult(ctx);
        Assert.assertNull(result);
    }

    @Test
    public void testIsHitRepairPayAbTest_WithEmptyModuleAbConfigs() {
        Mockito.when(ctx.getModuleAbConfigs()).thenReturn(Collections.emptyList());
        assertFalse("Expected false when ModuleAbConfigs is empty", DouHuService.isHitRepairPayAbTest(ctx));
    }

    @Test
    public void testIsHitRepairPayAbTest_WithNoMatchingModuleName() {
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setKey("NonMatchingModule");
        Mockito.when(ctx.getModuleAbConfigs()).thenReturn(Collections.singletonList(moduleAbConfig));
        assertFalse("Expected false when no matching moduleName found", DouHuService.isHitRepairPayAbTest(ctx));
    }

    @Test
    public void testIsHitRepairPayAbTest_WithNonMatchingExpResult() {
        ModuleAbConfig moduleAbConfig = createModuleAbConfig("MtRepairPayModule", "a");
        Mockito.when(ctx.getModuleAbConfigs()).thenReturn(Collections.singletonList(moduleAbConfig));
        Mockito.when(ctx.isMt()).thenReturn(true);
        assertFalse("Expected false when expResult is not 'b'", DouHuService.isHitRepairPayAbTest(ctx));
    }

    @Test
    public void testIsHitRepairPayAbTest_WithMatchingExpResult() {
        ModuleAbConfig moduleAbConfig = createModuleAbConfig("MtRepairPayModule", "b");
        Mockito.when(ctx.getModuleAbConfigs()).thenReturn(Collections.singletonList(moduleAbConfig));
        Mockito.when(ctx.isMt()).thenReturn(true);
        assertTrue("Expected true when expResult is 'b'", DouHuService.isHitRepairPayAbTest(ctx));
    }

    private ModuleAbConfig createModuleAbConfig(String key, String expResult) {
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setKey(key);
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult(expResult);
        moduleAbConfig.setConfigs(Arrays.asList(abConfig));
        return moduleAbConfig;
    }
}
