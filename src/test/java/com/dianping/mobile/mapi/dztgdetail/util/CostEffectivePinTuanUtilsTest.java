package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.entity.CostEffectivePinTuan;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * @Author: <EMAIL>
 * @Date: 2024/5/8
 */
@RunWith(MockitoJUnitRunner.class)
public class CostEffectivePinTuanUtilsTest {

    @Test
    public void testIsCePinTuaScene() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setCePinTuanScene(true);
        ctx.setCostEffectivePinTuan(costEffectivePinTuan);
        Assert.assertTrue(CostEffectivePinTuanUtils.isCePinTuaScene(ctx));
    }

    @Test
    public void testPinTuanOpened() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setCePinTuanScene(true);
        costEffectivePinTuan.setPinTuanOpened(true);
        ctx.setCostEffectivePinTuan(costEffectivePinTuan);
        Assert.assertTrue(CostEffectivePinTuanUtils.pinTuanOpened(ctx));
    }

    @Test
    public void testActivePinTuan() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setCePinTuanScene(true);
        costEffectivePinTuan.setPinTuanOpened(true);
        costEffectivePinTuan.setActivePinTuan(true);
        ctx.setCostEffectivePinTuan(costEffectivePinTuan);
        Assert.assertTrue(CostEffectivePinTuanUtils.activePinTuan(ctx));
    }

    @Test
    public void testEnhancedStyle() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setCePinTuanScene(true);
        costEffectivePinTuan.setPinTuanOpened(true);
        costEffectivePinTuan.setSceneType(56);
        ctx.setCostEffectivePinTuan(costEffectivePinTuan);
        Assert.assertTrue(CostEffectivePinTuanUtils.enhancedStyle(ctx));
    }

    @Test
    public void testGetNeedMemberCount() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setHelpSuccCountMin(10);
        costEffectivePinTuan.setHasHelpCount(5);
        ctx.setCostEffectivePinTuan(costEffectivePinTuan);
        Assert.assertTrue(CostEffectivePinTuanUtils.getNeedMemberCount(ctx) == 5);
    }

    @Test
    public void testGetWxShareJumpUrl() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setShareToken("1234567890");
        ctx.setCostEffectivePinTuan(costEffectivePinTuan);
        String result = CostEffectivePinTuanUtils.getWxShareJumpUrl(ctx);
        Assert.assertTrue(result.contains("1234567890"));
    }

    @Test
    public void testGetPinTuanResultPageUrl() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx ctx = new DealCtx(envCtx);
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setShareToken("1234567890");
        ctx.setCostEffectivePinTuan(costEffectivePinTuan);
        String result = CostEffectivePinTuanUtils.getPinTuanResultPageUrl(ctx);
        Assert.assertTrue(result.contains("1234567890&scene=share_page"));
    }
}
