package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.when;
import com.dianping.pigeon.remoting.ServiceFactory;
import com.dianping.pigeon.remoting.common.domain.CallMethod;
import com.dianping.pigeon.remoting.common.domain.GenericType;
import com.dianping.pigeon.remoting.common.service.GenericService;
import com.dianping.pigeon.remoting.invoker.config.InvokerConfig;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class ProductDetailWrapperAfterPropertiesSetTest {

    private ProductDetailWrapper productDetailWrapper;

    @Mock
    private GenericService service;

    @Before
    public void setUp() {
        productDetailWrapper = new ProductDetailWrapper();
    }

    /**
     * 测试afterPropertiesSet方法正常初始化
     * 验证service字段被正确设置
     */
    @Test
    public void testAfterPropertiesSetSuccess() throws Throwable {
        // arrange
        ReflectionTestUtils.setField(productDetailWrapper, "service", service);
        // act
        productDetailWrapper.afterPropertiesSet();
        // assert
        GenericService service = (GenericService) ReflectionTestUtils.getField(productDetailWrapper, "service");
        assertNotNull("Service should not be null", service);
    }
}
