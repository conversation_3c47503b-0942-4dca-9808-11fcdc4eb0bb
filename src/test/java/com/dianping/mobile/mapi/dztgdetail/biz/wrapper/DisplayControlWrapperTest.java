package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse.Builder;
import com.sankuai.beautycontent.security.displaycontrol.api.DisplayControlService;
import com.sankuai.beautycontent.security.displaycontrol.request.DisplayControlRequest;
import com.sankuai.beautycontent.security.displaycontrol.response.DisplayControlResponse;
import java.lang.reflect.Constructor;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DisplayControlWrapperTest {

    @InjectMocks
    private DisplayControlWrapper displayControlWrapper;

    @Mock
    private Future future;

    @Mock
    private RemoteResponse<DisplayControlResponse> remoteResponse;

    @Mock
    private DisplayControlResponse displayControlResponse;

    @Mock
    private DisplayControlService displayControlServiceFuture;

    @Mock
    private FutureFactory futureFactory;

    private RemoteResponse<DisplayControlResponse> createMockRemoteResponse(int code, String msg, DisplayControlResponse data) throws Exception {
        // Use reflection to create an instance of the private Builder class
        Constructor<RemoteResponse.Builder> constructor = RemoteResponse.Builder.class.getDeclaredConstructor();
        constructor.setAccessible(true);
        RemoteResponse.Builder<DisplayControlResponse> builder = constructor.newInstance();
        // Set the fields using reflection
        builder.getClass().getDeclaredField("code").set(builder, code);
        builder.getClass().getDeclaredField("msg").set(builder, msg);
        builder.getClass().getDeclaredField("data").set(builder, data);
        // Build the RemoteResponse object
        return builder.build();
    }

    /**
     * 测试 getDisplayCheckResponse 方法，当 getFutureResult 返回 null 时
     */
    @Test
    public void testGetDisplayCheckResponseWhenFutureResultIsNull() throws Throwable {
        // arrange
        when(displayControlWrapper.getFutureResult(future)).thenReturn(null);
        // act
        DisplayControlResponse result = displayControlWrapper.getDisplayCheckResponse(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 getDisplayCheckResponse 方法，当 getFutureResult 返回的结果 isSuccess 为 false 时
     */
    @Test
    public void testGetDisplayCheckResponseWhenFutureResultIsNotSuccess() throws Throwable {
        // arrange
        when(displayControlWrapper.getFutureResult(future)).thenReturn(remoteResponse);
        when(remoteResponse.isSuccess()).thenReturn(false);
        // act
        DisplayControlResponse result = displayControlWrapper.getDisplayCheckResponse(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 getDisplayCheckResponse 方法，当 getFutureResult 返回的结果 isSuccess 为 true 时
     */
    @Test
    public void testGetDisplayCheckResponseWhenFutureResultIsSuccess() throws Throwable {
        // arrange
        when(displayControlWrapper.getFutureResult(future)).thenReturn(remoteResponse);
        when(remoteResponse.isSuccess()).thenReturn(true);
        when(remoteResponse.getData()).thenReturn(displayControlResponse);
        // act
        DisplayControlResponse result = displayControlWrapper.getDisplayCheckResponse(future);
        // assert
        assertEquals(displayControlResponse, result);
    }

    /**
     * Test preDisplayCheck method for exception case
     */
    @Test
    public void testPreDisplayCheckException() throws Throwable {
        // arrange
        int dpId = 1;
        int cityId = 1;
        long shopId = 1L;
        boolean isFromMt = true;
        // Mock the displayCheck method to throw an exception
        when(displayControlServiceFuture.displayCheck(any(DisplayControlRequest.class))).thenThrow(new RuntimeException());
        // act
        Future<RemoteResponse<DisplayControlResponse>> result = displayControlWrapper.preDisplayCheck(dpId, cityId, shopId, isFromMt);
        // assert
        assertNull(result);
        verify(displayControlServiceFuture, times(1)).displayCheck(any(DisplayControlRequest.class));
    }
}
