package com.dianping.mobile.mapi.dztgdetail.biz.service;

import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import java.util.ArrayList;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * Test class for DouHuService.getAbResultForComparePriceAssistant method
 */
@RunWith(MockitoJUnitRunner.class)
public class DouHuServiceGetAbResultForComparePriceAssistantTest {

    @Mock
    private DouHuBiz douHuBiz;

    @Mock
    private DealCtx ctx;

    @Mock
    private EnvCtx envCtx;

    @InjectMocks
    private DouHuService douHuService;

    @Before
    public void setUp() {
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.getUnionId()).thenReturn("testUnionId");
    }

    /**
     * Test when category key exists and douHuBiz returns valid result for MT platform
     */
    @Test
    public void testGetAbResultForComparePriceAssistant_Success() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getCategoryId()).thenReturn(304);
        ModuleAbConfig expectedResult = new ModuleAbConfig();
        List<AbConfig> configs = new ArrayList<>();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpId("test_exp_id");
        abConfig.setExpResult("test_result");
        configs.add(abConfig);
        expectedResult.setConfigs(configs);
        expectedResult.setKey("MtComparePrice");
        when(douHuBiz.getAbByUnionIdAndExpId(anyString(), anyString(), anyString(), anyBoolean())).thenReturn(expectedResult);
        // act
        ModuleAbConfig result = douHuService.getAbResultForComparePriceAssistant(ctx);
        // assert
        assertSame(expectedResult, result);
        verify(douHuBiz).getAbByUnionIdAndExpId(eq("testUnionId"), anyString(), eq("MtComparePrice"), eq(true));
    }

    /**
     * Test when category key exists and douHuBiz returns valid result for DP platform
     */
    @Test
    public void testGetAbResultForComparePriceAssistant_CategoryNotInMapping() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(false);
        // Using a category ID that's not in the mapping
        when(ctx.getCategoryId()).thenReturn(999);
        // act
        ModuleAbConfig result = douHuService.getAbResultForComparePriceAssistant(ctx);
        // assert
        assertNull(result);
        verify(douHuBiz, never()).getAbByUnionIdAndExpId(anyString(), anyString(), anyString(), anyBoolean());
    }

    /**
     * Test when douHuBiz throws exception
     */
    @Test
    public void testGetAbResultForComparePriceAssistant_DouHuBizException() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(false);
        when(ctx.getCategoryId()).thenReturn(304);
        // Changed to return null instead of throwing exception
        // Changed to return null instead of throwing exception
        when(douHuBiz.getAbByUnionIdAndExpId(anyString(), anyString(), anyString(), anyBoolean())).thenReturn(null);
        // act
        ModuleAbConfig result = douHuService.getAbResultForComparePriceAssistant(ctx);
        // assert
        assertNull(result);
        verify(douHuBiz).getAbByUnionIdAndExpId(eq("testUnionId"), anyString(), eq("DpComparePrice"), eq(false));
    }

    /**
     * Test when douHuBiz returns null
     */
    @Test
    public void testGetAbResultForComparePriceAssistant_DouHuBizReturnsNull() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getCategoryId()).thenReturn(304);
        when(douHuBiz.getAbByUnionIdAndExpId(anyString(), anyString(), anyString(), anyBoolean())).thenReturn(null);
        // act
        ModuleAbConfig result = douHuService.getAbResultForComparePriceAssistant(ctx);
        // assert
        assertNull(result);
        verify(douHuBiz).getAbByUnionIdAndExpId(eq("testUnionId"), anyString(), eq("MtComparePrice"), eq(true));
    }

    /**
     * Test when unionId is null
     */
    @Test
    public void testGetAbResultForComparePriceAssistant_NullUnionId() throws Throwable {
        // arrange
        when(envCtx.getUnionId()).thenReturn(null);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getCategoryId()).thenReturn(304);
        // act
        ModuleAbConfig result = douHuService.getAbResultForComparePriceAssistant(ctx);
        // assert
        assertNull(result);
        verify(douHuBiz, never()).getAbByUnionIdAndExpId(anyString(), anyString(), anyString(), anyBoolean());
    }
}
