package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.pay.promo.reception.service.PayPromoMergeService;
import com.dianping.pay.promo.reception.service.dto.request.PromoMergeRequest;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import org.junit.After;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.concurrent.Future;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PromoMergeWrapperTest {

    @InjectMocks
    private PromoMergeWrapper promoMergeWrapper;

    @Mock
    private PayPromoMergeService payPromoMergeRemoteServiceFuture;

    @Mock
    private Future mockFuture;

    private MockedStatic<FutureFactory> mockedStaticFutureFactory;

    private void setUpMockStatic() {
        mockedStaticFutureFactory = mockStatic(FutureFactory.class);
        mockedStaticFutureFactory.when(FutureFactory::getFuture).thenReturn(mockFuture);
    }

    private void tearDownMockStatic() {
        mockedStaticFutureFactory.close();
    }

    /**
     * 测试 prePayPromoMergeInfo 方法，当 mergePromo 方法正常执行时
     */
    @Test
    public void testPrePayPromoMergeInfo_Normal() throws Throwable {
        setUpMockStatic();
        try {
            // arrange
            PromoMergeRequest request = new PromoMergeRequest();
            // act
            Future result = promoMergeWrapper.prePayPromoMergeInfo(request);
            // assert
            verify(payPromoMergeRemoteServiceFuture, times(1)).mergePromo(request);
            assertNotNull(result);
            // Ensure the returned future is the one we mocked
            assertEquals(mockFuture, result);
        } finally {
            tearDownMockStatic();
        }
    }

    /**
     * 测试 prePayPromoMergeInfo 方法，当 mergePromo 方法抛出异常时
     */
    @Test
    public void testPrePayPromoMergeInfo_Exception() throws Throwable {
        setUpMockStatic();
        try {
            // arrange
            PromoMergeRequest request = new PromoMergeRequest();
            doThrow(new RuntimeException()).when(payPromoMergeRemoteServiceFuture).mergePromo(request);
            // act
            Future result = promoMergeWrapper.prePayPromoMergeInfo(request);
            // assert
            verify(payPromoMergeRemoteServiceFuture, times(1)).mergePromo(request);
            assertNull(result);
        } finally {
            tearDownMockStatic();
        }
    }
}
