package com.dianping.mobile.mapi.dztgdetail;

import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.FreeDealEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.Pair;
import com.dianping.mobile.mapi.dztgdetail.helper.DpDealDetailBuilder2;
import com.dianping.mobile.mapi.dztgdetail.helper.MtDealDetailBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class DPDealDetailBuilderTest {

    @InjectMocks
    private DpDealDetailBuilder2 dpDealDetailBuilder2;

    @Before
    public void setUp() {
        Map<Integer, Pair> pairMap = Maps.newConcurrentMap();
        pairMap.put(DpDealDetailBuilder2.TYPE_SPECIAL_REMINDER, new Pair());
        DealGroupBaseDTO dealGroupBaseDto = new DealGroupBaseDTO();
        dpDealDetailBuilder2.setMap(pairMap);
        dpDealDetailBuilder2.setDealGroupBaseDto(dealGroupBaseDto);
    }

    @Test
    public void testToStructedDetails() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(4);
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        DealCtx context = new DealCtx(envCtx);
        context.setFreeDeal(true);
        context.setFreeDealType(FreeDealEnum.HOME_DESIGN_BOOKING);
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("free_product_notice");
        attrDTO.setValue(Lists.newArrayList("亮点"));
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        context.setDealGroupDTO(dealGroupDTO);
        dealGroupDTO.setAttrs(Lists.newArrayList(attrDTO));
        List<Pair> structedDetails = dpDealDetailBuilder2.toStructedDetails(false, context);
        Assert.assertNotNull(structedDetails);
    }

    @Test
    public void testToStructedDetailsEdu() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(4);
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        DealCtx context = new DealCtx(envCtx);
        context.setFreeDeal(true);
        context.setFreeDealType(FreeDealEnum.EDU_TRIAL_BOOKING);
        List<Pair> structedDetails = dpDealDetailBuilder2.toStructedDetails(false, context);
        Assert.assertNotNull(structedDetails);
    }
}
