package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.helper.NewBuyBarHelper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.util.EduDealUtils;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class NewBuyBarHelperUnitTest {

    private MockedStatic<EduDealUtils> eduDealUtils;

    @Before
    public void setUp() {
        eduDealUtils = Mockito.mockStatic(EduDealUtils.class);
    }

    @After
    public void tearDown() {
        eduDealUtils.close();
    }
    /**
     * 测试 setEduOnlineDealBuyButton 方法，当 dealCtx 为 null 时
     */
    @Test
    public void testSetEduOnlineDealBuyButton_DealCtxIsNull() throws Throwable {
        // arrange
        DealCtx dealCtx = null;
        DealBuyBar buyBar = new DealBuyBar(0, null);

        // act
        NewBuyBarHelper.setEduOnlineDealBuyButton(dealCtx, buyBar);

        // assert
        assertTrue(CollectionUtils.isEmpty(buyBar.getBuyBtns()));
    }

    /**
     * 测试 setEduOnlineDealBuyButton 方法，当 buyBar 为 null 时
     */
    @Test
    public void testSetEduOnlineDealBuyButton_BuyBarIsNull() throws Throwable {
        // arrange
        DealCtx dealCtx = new DealCtx(new EnvCtx());
        DealBuyBar buyBar = null;

        // act
        NewBuyBarHelper.setEduOnlineDealBuyButton(dealCtx, buyBar);

        // assert
        assertNull(buyBar);
    }

    /**
     * 测试 setEduOnlineDealBuyButton 方法，当 buyBar 的 buyBtns 为空时
     */
    @Test
    public void testSetEduOnlineDealBuyButton_BuyBtnsIsEmpty() throws Throwable {
        // arrange
        DealCtx dealCtx = new DealCtx(new EnvCtx());
        DealBuyBar buyBar = new DealBuyBar(0, null);
        buyBar.setBuyBtns(new ArrayList<>());

        // act
        NewBuyBarHelper.setEduOnlineDealBuyButton(dealCtx, buyBar);

        // assert
        assertTrue(CollectionUtils.isEmpty(buyBar.getBuyBtns()));
    }

    /**
     * 测试 setEduOnlineDealBuyButton 方法，当 dealCtx 的 envCtx 为 null 时
     */
    @Test
    public void testSetEduOnlineDealBuyButton_EnvCtxIsNull() throws Throwable {
        // arrange
        DealCtx dealCtx = new DealCtx(null);
        DealBuyBar buyBar = new DealBuyBar(0, null);
        List<DealBuyBtn> buyBtns = new ArrayList<>();
        buyBtns.add(new DealBuyBtn(true, "test"));
        buyBar.setBuyBtns(buyBtns);

        // act
        NewBuyBarHelper.setEduOnlineDealBuyButton(dealCtx, buyBar);

        // assert
        assertNull(buyBar.getBuyBtns().get(0).getPriceStr());
        assertNull(buyBar.getBuyBtns().get(0).getPricePostfix());
    }

    /**
     * 测试 setEduOnlineDealBuyButton 方法，当 dealCtx 的 envCtx 不为 null，但 clientType 不是 微信 时
     */
    @Test
    public void testSetEduOnlineDealBuyButton_ClientTypeIsMainWX() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        DealCtx dealCtx = new DealCtx(envCtx);
        DealBuyBar buyBar = new DealBuyBar(0, null);
        List<DealBuyBtn> buyBtns = new ArrayList<>();
        DealBuyBtn dealBuyBtn = new DealBuyBtn(true, "test");
        dealBuyBtn.setPriceStr("10");
        dealBuyBtn.setPricePostfix("起");
        buyBtns.add(dealBuyBtn);
        buyBar.setBuyBtns(buyBtns);

        // act
        NewBuyBarHelper.setEduOnlineDealBuyButton(dealCtx, buyBar);

        // assert
        assertNull(buyBar.getBuyBtns().get(0).getPriceStr());
        assertNull(buyBar.getBuyBtns().get(0).getPricePostfix());
        assertNull(buyBar.getBuyBtns().get(0).getBlockMsg());
    }

    /**
     * 测试 setEduOnlineDealBuyButton 方法，当 dealCtx 的 envCtx 不为 null，且 clientType 是 微信 时
     */
    @Test
    public void testSetEduOnlineDealBuyButton_ClientTypeIsNotMainWX() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP);
        DealCtx dealCtx = new DealCtx(envCtx);
        DealBuyBar buyBar = new DealBuyBar(0, null);
        List<DealBuyBtn> buyBtns = new ArrayList<>();
        DealBuyBtn dealBuyBtn = new DealBuyBtn(true, "test");
        dealBuyBtn.setPriceStr("10");
        dealBuyBtn.setPricePostfix("起");
        buyBtns.add(dealBuyBtn);
        buyBar.setBuyBtns(buyBtns);

        // act
        NewBuyBarHelper.setEduOnlineDealBuyButton(dealCtx, buyBar);

        // assert
        assertNotNull(buyBar.getBuyBtns().get(0).getPriceStr());
        assertNotNull(buyBar.getBuyBtns().get(0).getPricePostfix());
        assertEquals("为保证您的用户体验，请至“美团/点评”APP购买", buyBar.getBuyBtns().get(0).getBlockMsg());
    }

    @Test
    public void testModifyEduButtonsForSpecialDealNotSpecialDeal() {
        // arrange
        DealCtx dealCtx = mock(DealCtx.class);
        DealBuyBar buyBar = new DealBuyBar(0, Lists.newArrayList());
        when(dealCtx.getBuyBar()).thenReturn(buyBar);

        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);

        // act
        NewBuyBarHelper.modifyEduButtonsForSpecialDeal(dealCtx);

        // assert
        // 无需断言，因为没有异常抛出即表示测试通过
        assertTrue(CollectionUtils.isEmpty(buyBar.getBuyBtns()));
    }

    /**
     * 测试团单是职业教育规划类团单的情况
     */
    @Test
    public void testModifyEduButtonsForSpecialDealVocationalEduPlan() {
        // arrange
        DealCtx dealCtx = mock(DealCtx.class);
        DealBuyBar buyBar = new DealBuyBar(0, Lists.newArrayList());
        DealBuyBtn dealBuyBtn = mock(DealBuyBtn.class);
        when(dealCtx.getBuyBar()).thenReturn(buyBar);

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);

        eduDealUtils.when(()->EduDealUtils.isVocationalEduPlan(dealGroupDTO)).thenReturn(true);
        eduDealUtils.when(()->EduDealUtils.isVocationalEduCourseOrCamp(dealGroupDTO)).thenReturn(false);

        // act
        NewBuyBarHelper.modifyEduButtonsForSpecialDeal(dealCtx);

        // assert
        assertTrue(CollectionUtils.isEmpty(buyBar.getBuyBtns()));
    }

    /**
     * 测试团单是职业教育课程或集训营类团单的情况
     */
    @Test
    public void testModifyEduButtonsForSpecialDealVocationalBtnSizeEq2() {
        // arrange
        DealCtx dealCtx = mock(DealCtx.class);
        DealBuyBar buyBar = new DealBuyBar(1,null);
        buyBar.setBuyBtns(Lists.newArrayList(null, null));
        when(dealCtx.getBuyBar()).thenReturn(buyBar);

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);

        eduDealUtils.when(()->EduDealUtils.isVocationalEduPlan(dealGroupDTO)).thenReturn(false);
        eduDealUtils.when(()->EduDealUtils.isVocationalEduCourseOrCamp(dealGroupDTO)).thenReturn(true);

        // act
        NewBuyBarHelper.modifyEduButtonsForSpecialDeal(dealCtx);

        // assert
        assertEquals(2, dealCtx.getBuyBar().getStyleType());
    }

    /**
     * 测试团单是职业教育课程或集训营类团单的情况
     */
    @Test
    public void testModifyEduButtonsForSpecialDealVocationalBtnSizeEq1() {
        // arrange
        DealCtx dealCtx = mock(DealCtx.class);
        DealBuyBar buyBar = new DealBuyBar(1,null);
        buyBar.setBuyBtns(Lists.newArrayList((DealBuyBtn) null));
        when(dealCtx.getBuyBar()).thenReturn(buyBar);

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);

        eduDealUtils.when(()->EduDealUtils.isVocationalEduPlan(dealGroupDTO)).thenReturn(false);
        eduDealUtils.when(()->EduDealUtils.isVocationalEduCourseOrCamp(dealGroupDTO)).thenReturn(true);

        // act
        NewBuyBarHelper.modifyEduButtonsForSpecialDeal(dealCtx);

        // assert
        assertEquals(0, dealCtx.getBuyBar().getStyleType());
    }

}
