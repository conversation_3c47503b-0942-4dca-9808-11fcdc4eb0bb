package com.dianping.mobile.mapi.dztgdetail.biz.service;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.PhotoDealDetailConstants.DOUHU_BLANK_GROUP;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.constants.PhotoDealDetailConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DouHuServiceCheckAppVersionTest {

    @Mock
    private EnvCtx envCtx;

    private DouHuService douHuService = new DouHuService();

    @Mock
    private DealCtx ctx;

    /**
     * 测试checkAppVersion方法，当envCtx的版本小于iosVersion和androidVersion时，应返回false
     */
    @Test
    public void testCheckAppVersion_LessThanBoth() throws Throwable {
        // arrange
        when(envCtx.getVersion()).thenReturn("1.0.0");
        // act
        Method method = DouHuService.class.getDeclaredMethod("checkAppVersion", EnvCtx.class, boolean.class, boolean.class, String.class, String.class);
        method.setAccessible(true);
        boolean result = (boolean) method.invoke(douHuService, envCtx, true, true, "2.0.0", "2.0.0");
        // assert
        assertFalse(result);
    }

    /**
     * 测试checkAppVersion方法，当envCtx的版本大于等于iosVersion时，应返回true
     */
    @Test
    public void testCheckAppVersion_GreaterThanOrEqualToIosVersion() throws Throwable {
        // arrange
        when(envCtx.getVersion()).thenReturn("2.0.0");
        // act
        Method method = DouHuService.class.getDeclaredMethod("checkAppVersion", EnvCtx.class, boolean.class, boolean.class, String.class, String.class);
        method.setAccessible(true);
        boolean result = (boolean) method.invoke(douHuService, envCtx, true, true, "2.0.0", "1.0.0");
        // assert
        assertTrue(result);
    }

    /**
     * 测试checkAppVersion方法，当envCtx的版本大于等于androidVersion时，应返回true
     */
    @Test
    public void testCheckAppVersion_GreaterThanOrEqualToAndroidVersion() throws Throwable {
        // arrange
        when(envCtx.getVersion()).thenReturn("2.0.0");
        // act
        Method method = DouHuService.class.getDeclaredMethod("checkAppVersion", EnvCtx.class, boolean.class, boolean.class, String.class, String.class);
        method.setAccessible(true);
        boolean result = (boolean) method.invoke(douHuService, envCtx, true, true, "1.0.0", "2.0.0");
        // assert
        assertTrue(result);
    }

    /**
     * Test when moduleAbConfigs is null, should return default blank group
     */
    @Test
    public void testGetAbResultByExpKeyWhenModuleAbConfigsIsNull() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.getModuleAbConfigs()).thenReturn(null);
        DouHuService service = new DouHuService();
        String expKey = "testKey";
        // act
        String result = service.getAbResultByExpKey(ctx, expKey);
        // assert
        assertEquals(DOUHU_BLANK_GROUP, result);
    }

    /**
     * Test when moduleAbConfigs is empty, should return default blank group
     */
    @Test
    public void testGetAbResultByExpKeyWhenModuleAbConfigsIsEmpty() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.getModuleAbConfigs()).thenReturn(Collections.emptyList());
        DouHuService service = new DouHuService();
        String expKey = "testKey";
        // act
        String result = service.getAbResultByExpKey(ctx, expKey);
        // assert
        assertEquals(DOUHU_BLANK_GROUP, result);
    }

    /**
     * Test when no matching expKey is found, should return default blank group
     */
    @Test
    public void testGetAbResultByExpKeyWhenNoMatchingKey() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        List<ModuleAbConfig> configs = new ArrayList<>();
        ModuleAbConfig config = new ModuleAbConfig();
        config.setKey("otherKey");
        configs.add(config);
        when(ctx.getModuleAbConfigs()).thenReturn(configs);
        DouHuService service = new DouHuService();
        String expKey = "testKey";
        // act
        String result = service.getAbResultByExpKey(ctx, expKey);
        // assert
        assertEquals(DOUHU_BLANK_GROUP, result);
    }

    /**
     * Test when matching expKey is found but configs is empty, should return default blank group
     */
    @Test
    public void testGetAbResultByExpKeyWhenMatchingKeyButEmptyConfigs() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        List<ModuleAbConfig> configs = new ArrayList<>();
        ModuleAbConfig config = new ModuleAbConfig();
        config.setKey("testKey");
        config.setConfigs(Collections.emptyList());
        configs.add(config);
        when(ctx.getModuleAbConfigs()).thenReturn(configs);
        DouHuService service = new DouHuService();
        String expKey = "testKey";
        // act
        String result = service.getAbResultByExpKey(ctx, expKey);
        // assert
        assertEquals(DOUHU_BLANK_GROUP, result);
    }

    /**
     * Test when matching expKey is found with non-empty configs, should return first config's expResult
     */
    @Test
    public void testGetAbResultByExpKeyWhenMatchingKeyWithConfigs() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        List<ModuleAbConfig> configs = new ArrayList<>();
        ModuleAbConfig config = new ModuleAbConfig();
        config.setKey("testKey");
        List<AbConfig> abConfigs = new ArrayList<>();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("testResult");
        abConfigs.add(abConfig);
        config.setConfigs(abConfigs);
        configs.add(config);
        when(ctx.getModuleAbConfigs()).thenReturn(configs);
        DouHuService service = new DouHuService();
        String expKey = "testKey";
        // act
        String result = service.getAbResultByExpKey(ctx, expKey);
        // assert
        assertEquals("testResult", result);
    }

    /**
     * Test with multiple moduleAbConfigs, should return correct expResult for matching key
     */
    @Test
    public void testGetAbResultByExpKeyWithMultipleConfigs() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        List<ModuleAbConfig> configs = new ArrayList<>();
        // Add non-matching config
        ModuleAbConfig config1 = new ModuleAbConfig();
        config1.setKey("otherKey");
        configs.add(config1);
        // Add matching config
        ModuleAbConfig config2 = new ModuleAbConfig();
        config2.setKey("testKey");
        List<AbConfig> abConfigs = new ArrayList<>();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("expectedResult");
        abConfigs.add(abConfig);
        config2.setConfigs(abConfigs);
        configs.add(config2);
        when(ctx.getModuleAbConfigs()).thenReturn(configs);
        DouHuService service = new DouHuService();
        String expKey = "testKey";
        // act
        String result = service.getAbResultByExpKey(ctx, expKey);
        // assert
        assertEquals("expectedResult", result);
    }

    /**
     * Test when Lion config contains the category and context is MT
     */
    @Test
    public void testGetPhotoAbResult_MtContextWithMatchingCategory() throws Throwable {
        // arrange
        DealGroupCategoryDTO category = new DealGroupCategoryDTO();
        category.setCategoryId(505L);
        category.setServiceType("massage");
        DealGroupDTO dealGroup = new DealGroupDTO();
        dealGroup.setCategory(category);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroup);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setKey(PhotoDealDetailConstants.MT_DOUHU_KEY_V2);
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("test_result_v2");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        when(ctx.getModuleAbConfigs()).thenReturn(Arrays.asList(moduleAbConfig));
        // act
        String result = douHuService.getPhotoAbResult(ctx);
        // assert
        assertEquals("test_result_v2", result);
    }

    /**
     * Test when Lion config doesn't contain the category and context is DP
     */
    @Test
    public void testGetPhotoAbResult_DpContextWithNonMatchingCategory() throws Throwable {
        // arrange
        DealGroupCategoryDTO category = new DealGroupCategoryDTO();
        category.setCategoryId(505L);
        category.setServiceType("massage");
        DealGroupDTO dealGroup = new DealGroupDTO();
        dealGroup.setCategory(category);
        when(ctx.isMt()).thenReturn(false);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroup);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setKey(PhotoDealDetailConstants.DP_DOUHU_KEY_V2);
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("test_result_v2");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        when(ctx.getModuleAbConfigs()).thenReturn(Arrays.asList(moduleAbConfig));
        // act
        String result = douHuService.getPhotoAbResult(ctx);
        // assert
        assertEquals("test_result_v2", result);
    }

    /**
     * Test when Lion config returns empty list
     */
    @Test
    public void testGetPhotoAbResult_EmptyLionConfig() throws Throwable {
        // arrange
        DealGroupCategoryDTO category = new DealGroupCategoryDTO();
        category.setCategoryId(505L);
        category.setServiceType("massage");
        DealGroupDTO dealGroup = new DealGroupDTO();
        dealGroup.setCategory(category);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroup);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setKey(PhotoDealDetailConstants.MT_DOUHU_KEY_V2);
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("test_result_v2");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        when(ctx.getModuleAbConfigs()).thenReturn(Arrays.asList(moduleAbConfig));
        // act
        String result = douHuService.getPhotoAbResult(ctx);
        // assert
        assertEquals("test_result_v2", result);
    }

    /**
     * Test when deal group category is null
     */
    @Test
    public void testGetPhotoAbResult_NullCategory() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(false);
        when(ctx.getDealGroupDTO()).thenReturn(null);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setKey(PhotoDealDetailConstants.DP_DOUHU_KEY_V2);
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult(PhotoDealDetailConstants.DOUHU_BLANK_GROUP);
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        when(ctx.getModuleAbConfigs()).thenReturn(Arrays.asList(moduleAbConfig));
        // act
        String result = douHuService.getPhotoAbResult(ctx);
        // assert
        assertEquals(PhotoDealDetailConstants.DOUHU_BLANK_GROUP, result);
    }

    /**
     * Test when moduleAbConfigs is empty
     */
    @Test
    public void testGetPhotoAbResult_EmptyModuleAbConfigs() throws Throwable {
        // arrange
        DealGroupCategoryDTO category = new DealGroupCategoryDTO();
        category.setCategoryId(505L);
        category.setServiceType("massage");
        DealGroupDTO dealGroup = new DealGroupDTO();
        dealGroup.setCategory(category);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroup);
        when(ctx.getModuleAbConfigs()).thenReturn(Collections.emptyList());
        // act
        String result = douHuService.getPhotoAbResult(ctx);
        // assert
        assertEquals(PhotoDealDetailConstants.DOUHU_BLANK_GROUP, result);
    }
}
