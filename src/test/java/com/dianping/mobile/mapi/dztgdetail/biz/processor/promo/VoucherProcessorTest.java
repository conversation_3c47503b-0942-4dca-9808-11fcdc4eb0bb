package com.dianping.mobile.mapi.dztgdetail.biz.processor.promo;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.tgc.open.entity.BizIdType;
import com.dianping.tgc.open.entity.CouponBizIdQueryRequest;
import java.lang.reflect.Method;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class VoucherProcessorTest {

    @InjectMocks
    private VoucherProcessor voucherProcessor;

    private CouponBizIdQueryRequest invokePrivateWrapCouponBizIdQueryRequest(DealCtx ctx) throws Exception {
        Method method = VoucherProcessor.class.getDeclaredMethod("wrapCouponBizIdQueryRequest", DealCtx.class);
        method.setAccessible(true);
        return (CouponBizIdQueryRequest) method.invoke(voucherProcessor, ctx);
    }

    /**
     * Test case for wrapping CouponBizIdQueryRequest when context is from Meituan
     */
    @Test
    public void testWrapCouponBizIdQueryRequest_MeituanContext() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        EnvCtx envCtx = mock(EnvCtx.class);
        when(ctx.getDpId()).thenReturn(123);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.getMtUserId()).thenReturn(456L);
        // act
        CouponBizIdQueryRequest result = invokePrivateWrapCouponBizIdQueryRequest(ctx);
        // assert
        assertEquals(123, result.getBizId());
        assertEquals((long) BizIdType.DEAL_GROUP_ID.getCode(), (long) result.getBizIdType());
        assertTrue(result.isMt());
        // Explicitly convert Long to long
        assertEquals(456L, result.getUserId().longValue());
    }

    /**
     * Test case for wrapping CouponBizIdQueryRequest when context is from Dianping
     */
    @Test
    public void testWrapCouponBizIdQueryRequest_DianpingContext() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        EnvCtx envCtx = mock(EnvCtx.class);
        when(ctx.getDpId()).thenReturn(123);
        when(ctx.isMt()).thenReturn(false);
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.getDpUserId()).thenReturn(789L);
        // act
        CouponBizIdQueryRequest result = invokePrivateWrapCouponBizIdQueryRequest(ctx);
        // assert
        assertEquals(123, result.getBizId());
        assertEquals((long) BizIdType.DEAL_GROUP_ID.getCode(), (long) result.getBizIdType());
        assertFalse(result.isMt());
        // Explicitly convert Long to long
        assertEquals(789L, result.getUserId().longValue());
    }

    /**
     * Test case for wrapping CouponBizIdQueryRequest with zero values
     */
    @Test
    public void testWrapCouponBizIdQueryRequest_ZeroValues() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        EnvCtx envCtx = mock(EnvCtx.class);
        when(ctx.getDpId()).thenReturn(0);
        when(ctx.isMt()).thenReturn(false);
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.getDpUserId()).thenReturn(0L);
        // act
        CouponBizIdQueryRequest result = invokePrivateWrapCouponBizIdQueryRequest(ctx);
        // assert
        assertEquals(0, result.getBizId());
        assertEquals((long) BizIdType.DEAL_GROUP_ID.getCode(), (long) result.getBizIdType());
        assertFalse(result.isMt());
        // Explicitly convert Long to long
        assertEquals(0L, result.getUserId().longValue());
    }

    /**
     * Test case for wrapping CouponBizIdQueryRequest with null EnvCtx
     */
    @Test
    public void testWrapCouponBizIdQueryRequest_NullEnvCtx() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        // Initialize EnvCtx with default values
        EnvCtx envCtx = new EnvCtx();
        when(ctx.getDpId()).thenReturn(123);
        when(ctx.isMt()).thenReturn(true);
        // Ensure EnvCtx is not null
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        // act
        CouponBizIdQueryRequest result = invokePrivateWrapCouponBizIdQueryRequest(ctx);
        // assert
        assertEquals(123, result.getBizId());
        assertEquals((long) BizIdType.DEAL_GROUP_ID.getCode(), (long) result.getBizIdType());
        assertTrue(result.isMt());
        // Explicitly convert Long to long
        // Default userId should be 0
        assertEquals(0L, result.getUserId().longValue());
    }

    /**
     * Test isEnable when dpId > 0
     * Should return true
     */
    @Test
    public void testIsEnable_WhenDpIdGreaterThanZero_ShouldReturnTrue() {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.getDpId()).thenReturn(1);
        // act
        boolean result = voucherProcessor.isEnable(ctx);
        // assert
        assertTrue(result);
    }

    /**
     * Test isEnable when dpId = 0
     * Should return false
     */
    @Test
    public void testIsEnable_WhenDpIdEqualsZero_ShouldReturnFalse() {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.getDpId()).thenReturn(0);
        // act
        boolean result = voucherProcessor.isEnable(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * Test isEnable when dpId < 0
     * Should return false
     */
    @Test
    public void testIsEnable_WhenDpIdLessThanZero_ShouldReturnFalse() {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.getDpId()).thenReturn(-1);
        // act
        boolean result = voucherProcessor.isEnable(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * Test isEnable when context is null
     * Should handle null case
     */
    @Test(expected = NullPointerException.class)
    public void testIsEnable_WhenContextIsNull_ShouldHandleNullCase() {
        // arrange
        DealCtx ctx = null;
        // act
        voucherProcessor.isEnable(ctx);
        // Note: Test expects NullPointerException to be thrown
    }
}
