package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Tests for ImmersiveImageWrapper.getContentFusionDetailById method
 */
public class ImmersiveImageWrapper_GetContentFusionDetailByIdTest {

    private ImmersiveImageWrapper wrapper;

    @Before
    public void setUp() {
        wrapper = new ImmersiveImageWrapper();
    }

    /**
     * Test getContentFusionDetailById when contentFusionList is null
     */
    @Test
    public void testGetContentFusionDetailByIdWithNullList() {
        ImmersiveImageWrapper.ContentFusion result = wrapper.getContentFusionDetailById("1", "oldId", null);
        assertNull(result);
    }

    /**
     * Test getContentFusionDetailById when contentFusionList is empty
     */
    @Test
    public void testGetContentFusionDetailByIdWithEmptyList() {
        ImmersiveImageWrapper.ContentFusion result = wrapper.getContentFusionDetailById("1", "oldId", Collections.emptyList());
        assertNull(result);
    }

    /**
     * Test getContentFusionDetailById when no matching id found
     */
    @Test
    public void testGetContentFusionDetailByIdWhenNoMatchId() {
        ImmersiveImageWrapper.ContentFusion contentFusion = new ImmersiveImageWrapper.ContentFusion();
        ImmersiveImageWrapper.ContentInfo contentInfo = new ImmersiveImageWrapper.ContentInfo();
        contentInfo.setOldId(2L);
        contentFusion.setContentInfo(contentInfo);
        List<ImmersiveImageWrapper.ContentFusion> list = new ArrayList<>();
        list.add(contentFusion);
        ImmersiveImageWrapper.ContentFusion result = wrapper.getContentFusionDetailById("1", "oldId", list);
        assertNull(result);
    }

    /**
     * Test getContentFusionDetailById with matching oldId
     */
    @Test
    public void testGetContentFusionDetailByIdWithMatchingOldId() {
        ImmersiveImageWrapper.ContentFusion contentFusion = new ImmersiveImageWrapper.ContentFusion();
        ImmersiveImageWrapper.ContentInfo contentInfo = new ImmersiveImageWrapper.ContentInfo();
        contentInfo.setOldId(1L);
        contentFusion.setContentInfo(contentInfo);
        List<ImmersiveImageWrapper.ContentFusion> list = new ArrayList<>();
        list.add(contentFusion);
        ImmersiveImageWrapper.ContentFusion result = wrapper.getContentFusionDetailById("1", "oldId", list);
        assertNotNull(result);
        assertEquals(Long.valueOf(1), result.getContentInfo().getOldId());
    }

    /**
     * Test getContentFusionDetailById with matching contentId
     */
    @Test
    public void testGetContentFusionDetailByIdWithMatchingContentId() {
        ImmersiveImageWrapper.ContentFusion contentFusion = new ImmersiveImageWrapper.ContentFusion();
        ImmersiveImageWrapper.ContentInfo contentInfo = new ImmersiveImageWrapper.ContentInfo();
        contentInfo.setContentId(1L);
        contentFusion.setContentInfo(contentInfo);
        List<ImmersiveImageWrapper.ContentFusion> list = new ArrayList<>();
        list.add(contentFusion);
        ImmersiveImageWrapper.ContentFusion result = wrapper.getContentFusionDetailById("1", "contentId", list);
        assertNotNull(result);
        assertEquals(Long.valueOf(1), result.getContentInfo().getContentId());
    }
}
