package com.dianping.mobile.mapi.dztgdetail.biz.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.general.unified.search.api.productshopsearch.GeneralProductShopSearchService;
import com.dianping.general.unified.search.api.productshopsearch.dto.option.BaseSearchOption;
import com.dianping.general.unified.search.api.productshopsearch.enums.ProductBizTypeEnum;
import com.dianping.general.unified.search.api.productshopsearch.enums.ProductStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperCacheWrapper;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductShopSearchComponentInitTest {

    @Mock
    private MapperCacheWrapper mockMapperCacheWrapper;

    @Mock
    private GeneralProductShopSearchService mockGeneralProductShopSearchService;

    @InjectMocks
    private ProductShopSearchComponent productShopSearchComponent;

    private BaseSearchOption invokePrivateInitMethod(List<Long> productIds) throws Exception {
        Method initMethod = ProductShopSearchComponent.class.getDeclaredMethod("init", List.class);
        initMethod.setAccessible(true);
        return (BaseSearchOption) initMethod.invoke(null, productIds);
    }

    /**
     * 测试私有init方法 - 空列表输入
     */
    @Test
    public void testPrivateInitMethod_WithEmptyList_ShouldHandleCorrectly() throws Throwable {
        // 准备测试数据
        List<Long> emptyList = Collections.emptyList();
        // 通过反射调用私有方法
        Method initMethod = ProductShopSearchComponent.class.getDeclaredMethod("init", List.class);
        initMethod.setAccessible(true);
        BaseSearchOption result = (BaseSearchOption) initMethod.invoke(null, emptyList);
        // 验证结果
        assertNotNull("BaseSearchOption实例不应为null", result);
        assertTrue("产品ID列表应为空", result.getProductIds().isEmpty());
        assertEquals("其他属性应保持默认设置", Collections.singletonList(ProductBizTypeEnum.DEALGROUP), result.getProductBizTypes());
    }

    /**
     * 测试私有init方法 - null输入
     */
    @Test
    public void testPrivateInitMethod_WithNullInput_ShouldHandleCorrectly() throws Throwable {
        // 通过反射调用私有方法
        Method initMethod = ProductShopSearchComponent.class.getDeclaredMethod("init", List.class);
        initMethod.setAccessible(true);
        BaseSearchOption result = (BaseSearchOption) initMethod.invoke(null, new Object[] { null });
        // 验证结果
        assertNotNull("BaseSearchOption实例不应为null", result);
        assertNull("产品ID应为null", result.getProductIds());
        assertEquals("其他属性应保持默认设置", Collections.singletonList(ProductBizTypeEnum.DEALGROUP), result.getProductBizTypes());
    }

    /**
     * 测试依赖注入
     */
    @Test
    public void testDependencyInjection_ShouldInjectAllDependencies() throws Throwable {
        // 验证MapperCacheWrapper注入
        Field mapperCacheWrapperField = ProductShopSearchComponent.class.getDeclaredField("mapperCacheWrapper");
        mapperCacheWrapperField.setAccessible(true);
        assertNotNull("mapperCacheWrapper应被注入", mapperCacheWrapperField.get(productShopSearchComponent));
        // 验证GeneralProductShopSearchService注入
        Field searchServiceField = ProductShopSearchComponent.class.getDeclaredField("generalProductShopSearchService");
        searchServiceField.setAccessible(true);
        assertNotNull("generalProductShopSearchService应被注入", searchServiceField.get(productShopSearchComponent));
    }

    /**
     * 测试多次调用init方法返回不同实例
     */
    @Test
    public void testPrivateInitMethod_CalledMultipleTimes_ShouldReturnNewInstances() throws Throwable {
        // 准备测试数据
        List<Long> productIds = Arrays.asList(2001L, 2002L);
        // 通过反射调用私有方法两次
        Method initMethod = ProductShopSearchComponent.class.getDeclaredMethod("init", List.class);
        initMethod.setAccessible(true);
        BaseSearchOption result1 = (BaseSearchOption) initMethod.invoke(null, productIds);
        BaseSearchOption result2 = (BaseSearchOption) initMethod.invoke(null, productIds);
        // 验证结果
        assertNotSame("每次调用应返回新实例", result1, result2);
        assertEquals("两个实例应有相同产品ID", result1.getProductIds(), result2.getProductIds());
    }

    /**
     * 测试多次调用是否返回新实例
     * 验证每次调用都返回新的BaseSearchOption实例
     */
    @Test
    public void testInit_CalledMultipleTimes_ShouldReturnNewInstances() throws Throwable {
        // 准备测试数据
        List<Long> productIds = Arrays.asList(2001L, 2002L);
        // 执行方法
        BaseSearchOption result1 = invokePrivateInitMethod(productIds);
        BaseSearchOption result2 = invokePrivateInitMethod(productIds);
        // 验证结果
        assertNotSame("每次调用应返回新实例", result1, result2);
        assertEquals("两个实例应有相同产品ID", result1.getProductIds(), result2.getProductIds());
    }
}
