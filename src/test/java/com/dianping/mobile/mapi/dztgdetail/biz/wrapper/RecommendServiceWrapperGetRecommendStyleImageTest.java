package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.entity.Response;
import com.dianping.martgeneral.recommend.api.enums.PlatformEnum;
import com.dianping.martgeneral.recommend.api.service.RecommendService;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageVO;
import com.dianping.mobile.mapi.dztgdetail.entity.QueryRecommendParam;
import com.google.common.collect.Lists;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class RecommendServiceWrapperGetRecommendStyleImageTest {

    @InjectMocks
    private RecommendServiceWrapper recommendServiceWrapper;

    @Mock
    private RecommendService recommendServiceFuture;
    @Mock
    private PoiClientWrapper poiClientWrapper;

    @Before
    public void setUp() {
        // Mock the recommendService to return a valid Response object
        Response<RecommendResult<RecommendDTO>> response = new Response<>();
        RecommendResult<RecommendDTO> recommendResult = new RecommendResult<>();
        response.setResult(recommendResult);
        response.setSuccess(true);
        // Mock the poiClientWrapper to return null
        when(poiClientWrapper.getMtPoiDTO(anyLong(), anyList())).thenReturn(null);
        // Mock the recommendServiceFuture to return a valid Response object
        when(recommendServiceFuture.recommend(any(), eq(RecommendDTO.class))).thenReturn(response);
    }

    private QueryRecommendParam createQueryRecommendParam(boolean isMt, Integer cityId, String dpId, String uuid, String originUserId, PlatformEnum platformEnum, Double latitude, Double longitude, java.util.List<String> exhibitImgIds, String filterIds, String sortType, String flowFlag, String shopRatingThreshold, Long shopId, Integer start, Integer limit, Integer clientType, Integer categoryId, String dealGroupPrice, String isAll, String dealGroupTagIds, Long dpDealGroupId) {
        return QueryRecommendParam.builder().isMt(isMt).cityId(cityId).dpId(dpId).uuid(uuid).originUserId(originUserId).platformEnum(platformEnum).latitude(latitude).longitude(longitude).exhibitImgIds(exhibitImgIds).filterIds(filterIds).sortType(sortType).flowFlag(flowFlag).shopRatingThreshold(shopRatingThreshold).shopId(shopId).start(start).limit(limit).clientType(clientType).categoryId(categoryId).dealGroupPrice(dealGroupPrice).isAll(isAll).dealGroupTagIds(dealGroupTagIds).dpDealGroupId(dpDealGroupId).build();
    }

    @Test
    public void testGetRecommendStyleImageMtTypeAndMtPoiDTOIsNull() throws Throwable {
        QueryRecommendParam param = createQueryRecommendParam(true, 1, "dpId", "uuid", "originUserId", PlatformEnum.DP, 39.9042, 116.4074, Lists.newArrayList("exhibitImgId1", "exhibitImgId2"), "filterIds", "sortType", "flowFlag", "shopRatingThreshold", 1L, 0, 10, 1, 1, "dealGroupPrice", "isAll", "dealGroupTagIds", 1L);
        ImmersiveImageVO result = recommendServiceWrapper.getRecommendStyleImage(param);
        assertNull(result);
    }

    @Test
    public void testGetRecommendStyleImageNotMtType() throws Throwable {
        QueryRecommendParam param = createQueryRecommendParam(false, 1, "dpId", "uuid", "originUserId", PlatformEnum.DP, 39.9042, 116.4074, Lists.newArrayList("exhibitImgId1", "exhibitImgId2"), "filterIds", "sortType", "flowFlag", "shopRatingThreshold", 1L, 0, 10, 1, 1, "dealGroupPrice", "isAll", "dealGroupTagIds", 1L);
        ImmersiveImageVO result = recommendServiceWrapper.getRecommendStyleImage(param);
        assertNull(result);
    }

}
