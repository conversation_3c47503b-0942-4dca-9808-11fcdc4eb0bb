package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.gmkt.activity.api.dto.BatchQueryDealActivityRequest;
import com.dianping.gmkt.activity.api.service.DealActivityQueryService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.concurrent.Future;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.*;

public class DealActivityWrapper_PrepareDealActivityTest {

    @InjectMocks
    private DealActivityWrapper dealActivityWrapper;

    @Mock
    private DealActivityQueryService dealActivityQueryService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 prepareDealActivity 方法，当 request 为 null 时
     */
    @Test
    public void testPrepareDealActivityRequestIsNull() throws Throwable {
        // arrange
        BatchQueryDealActivityRequest request = null;
        // act
        Future result = dealActivityWrapper.prepareDealActivity(request);
        // assert
        assertNull(result);
    }

    /**
     * 测试 prepareDealActivity 方法，当 request 不为 null，且 dealActivityQueryService.batchQueryDealActivity(request) 方法正常执行时
     */
    @Test
    public void testPrepareDealActivityNormal() throws Throwable {
        // arrange
        BatchQueryDealActivityRequest request = new BatchQueryDealActivityRequest();
        // act
        Future result = dealActivityWrapper.prepareDealActivity(request);
        // assert
        verify(dealActivityQueryService, times(1)).batchQueryDealActivity(request);
    }

    /**
     * 测试 prepareDealActivity 方法，当 request 不为 null，但 dealActivityQueryService.batchQueryDealActivity(request) 方法执行时抛出异常时
     */
    @Test
    public void testPrepareDealActivityException() throws Throwable {
        // arrange
        BatchQueryDealActivityRequest request = new BatchQueryDealActivityRequest();
        doThrow(new RuntimeException()).when(dealActivityQueryService).batchQueryDealActivity(request);
        // act
        Future result = dealActivityWrapper.prepareDealActivity(request);
        // assert
        assertNull(result);
    }
}
