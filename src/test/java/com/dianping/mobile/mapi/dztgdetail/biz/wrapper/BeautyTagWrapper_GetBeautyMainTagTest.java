package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Future;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class BeautyTagWrapper_GetBeautyMainTagTest {

    @Mock
    private Future future;

    private BeautyTagWrapper beautyTagWrapper;

    @Before
    public void setUp() {
        beautyTagWrapper = new BeautyTagWrapper();
    }

    /**
     * 测试Future列表为空的情况
     */
    @Test
    public void testGetBeautyMainTagWithEmptyList() throws Throwable {
        // arrange
        Map<Integer, String> expected = new HashMap<>();
        // act
        Map<Integer, String> actual = beautyTagWrapper.getBeautyMainTag(Collections.emptyList());
        // assert
        assertEquals(expected, actual);
    }

    /**
     * 测试Future列表不为空，但所有Future对象都为空的情况
     */
    @Test
    public void testGetBeautyMainTagWithAllNullFutures() throws Throwable {
        // arrange
        Map<Integer, String> expected = new HashMap<>();
        // act
        Map<Integer, String> actual = beautyTagWrapper.getBeautyMainTag(Arrays.asList(null, null, null));
        // assert
        assertEquals(expected, actual);
    }

    /**
     * 测试Future列表不为空，且包含非空的Future对象的情况
     */
    @Test
    public void testGetBeautyMainTagWithNonNullFutures() throws Throwable {
        // arrange
        Map<Integer, String> expected = new HashMap<>();
        expected.put(1, "test");
        when(beautyTagWrapper.getFutureResult(future)).thenReturn(expected);
        // act
        Map<Integer, String> actual = beautyTagWrapper.getBeautyMainTag(Collections.singletonList(future));
        // assert
        assertEquals(expected, actual);
    }
}
