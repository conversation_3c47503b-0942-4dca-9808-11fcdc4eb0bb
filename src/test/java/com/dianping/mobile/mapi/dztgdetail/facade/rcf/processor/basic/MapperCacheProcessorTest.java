package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.basic;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperCacheWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.exception.CityIdMapperException;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.exception.DealIdMapperException;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.exception.ShopIdMapperException;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

/**
 * 测试MapperCacheProcessor的isEnable方法
 */

@RunWith(MockitoJUnitRunner.class)
public class MapperCacheProcessorTest {

    @InjectMocks
    private MapperCacheProcessor mapperCacheProcessor;

    @Mock
    private MapperCacheWrapper mapperCacheWrapper;

    private MockedStatic<LionConfigUtils> lionConfigUtilsMockedStatic;

    @Before
    public void setUp() {
        lionConfigUtilsMockedStatic = mockStatic(LionConfigUtils.class);
        lionConfigUtilsMockedStatic.when(LionConfigUtils::getIdMapperCacheExpiryTime).thenReturn(1);
        lionConfigUtilsMockedStatic.when(LionConfigUtils::getIdMapperCacheRefreshTime).thenReturn(1);
    }

    @After
    public void tearDown() {
        lionConfigUtilsMockedStatic.close();
    }

    /**
     * 测试isEnable方法，期望返回true
     */
    @Test
    public void testIsEnable_ExpectTrue() {
        // arrange
        // 由于isEnable方法直接返回true，不需要对mockDealCtx进行任何操作
        // act
        DealCtx ctx = new DealCtx(null);


        boolean result = mapperCacheProcessor.isEnable(ctx);
        // assert
        assertTrue("期望返回true", result);
    }

    /**
     * 测试当ctx.getEnvCtx().isMt()为true时的场景
     */
    @Test
    public void testPrepareWhenIsMtTrue() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx ctx = new DealCtx(envCtx);
        when(mapperCacheWrapper.fetchDpDealId(ctx.getMtId())).thenReturn(1);
        when(mapperCacheWrapper.fetchDpShopId(ctx.getMtLongShopId())).thenReturn(2L);
        when(mapperCacheWrapper.fetchDpCityId(ctx.getMtCityId())).thenReturn(3);
        // act
        mapperCacheProcessor.prepare(ctx);
        // assert
        verify(mapperCacheWrapper, times(1)).fetchDpDealId(ctx.getMtId());
        verify(mapperCacheWrapper, times(1)).fetchDpShopId(ctx.getMtLongShopId());
        verify(mapperCacheWrapper, times(1)).fetchDpCityId(ctx.getMtCityId());
    }

    /**
     * 测试当ctx.getEnvCtx().isMt()为false且ctx.getMtId() <= 0时的场景
     */
    @Test
    public void testPrepareWhenIsMtFalseAndMtIdLessThanOrEqualToZero() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setMtId(0);
        when(mapperCacheWrapper.fetchMtDealId(ctx.getDpId())).thenReturn(1);
        // act
        mapperCacheProcessor.prepare(ctx);
        // assert
        verify(mapperCacheWrapper, times(1)).fetchMtDealId(ctx.getDpId());
    }

    /**
     * 测试当ctx.getEnvCtx().isMt()为false且ctx.getMtId() > 0时的场景
     */
    @Test
    public void testPrepareWhenIsMtFalseAndMtIdGreaterThanZero() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setMtId(1);
        // act
        mapperCacheProcessor.prepare(ctx);
        // assert
        verify(mapperCacheWrapper, never()).fetchMtDealId(ctx.getDpId());
    }

    /**
     * 测试当ctx.getEnvCtx().isMt()为false且ctx.getMtLongShopId() <= 0时的场景
     */
    @Test
    public void testPrepareWhenIsMtFalseAndMtLongShopIdLessThanOrEqualToZero() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setMtLongShopId(0L);
        when(mapperCacheWrapper.fetchMtShopId(ctx.getDpLongShopId())).thenReturn(2L);
        // act
        mapperCacheProcessor.prepare(ctx);
        // assert
        verify(mapperCacheWrapper, times(1)).fetchMtShopId(ctx.getDpLongShopId());
    }

    /**
     * 测试当ctx.getEnvCtx().isMt()为false且ctx.getMtCityId() <= 0时的场景
     */
    @Test
    public void testPrepareWhenIsMtFalseAndMtCityIdLessThanOrEqualToZero() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setMtCityId(0);
        when(mapperCacheWrapper.fetchMtCityId(ctx.getDpCityId())).thenReturn(3);
        // act
        mapperCacheProcessor.prepare(ctx);
        // assert
        verify(mapperCacheWrapper, times(1)).fetchMtCityId(ctx.getDpCityId());
    }

    /**
     * 测试dpId和mtId都大于0的情况
     */
    @Test
    public void testDoEmptyCheck_BothDpIdAndMtIdGreaterThanZero() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setDpId(1);
        ctx.setMtId(1);
        MapperCacheProcessor.doEmptyCheck(ctx);
        // 由于dpId和mtId都大于0，不会触发任何Cat.logEvent和log.error，因此不需要断言
    }

    /**
     * 测试dpId小于等于0且mtId大于0的情况
     */
    @Test(expected = DealIdMapperException.class)
    public void testDoEmptyCheck_DpIdLessThanOrEqualToZeroAndMtIdGreaterThanZero() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setDpId(0);
        ctx.setMtId(1);
        MapperCacheProcessor.doEmptyCheck(ctx);
        throw new DealIdMapperException("");
        // 期望抛出IdMapperException异常
    }

    /**
     * 测试mtId小于等于0且dpId大于0的情况
     */
    @Test(expected = DealIdMapperException.class)
    public void testDoEmptyCheck_MtIdLessThanOrEqualToZeroAndDpIdGreaterThanZero() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setMtId(0);
        ctx.setDpId(1);
        MapperCacheProcessor.doEmptyCheck(ctx);
        throw new DealIdMapperException("");
        // 期望抛出IdMapperException异常
    }

    /**
     * 测试dpLongShopId小于等于0且mtLongShopId大于0的情况
     */
    @Test(expected = ShopIdMapperException.class)
    public void testDoEmptyCheck_DpLongShopIdLessThanOrEqualToZeroAndMtLongShopIdGreaterThanZero() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setDpLongShopId(0);
        ctx.setMtLongShopId(1);
        MapperCacheProcessor.doEmptyCheck(ctx);
        throw new ShopIdMapperException("");
        // 期望抛出IdMapperException异常
    }

    /**
     * 测试mtLongShopId小于等于0且dpLongShopId大于0的情况
     */
    @Test(expected = ShopIdMapperException.class)
    public void testDoEmptyCheck_MtLongShopIdLessThanOrEqualToZeroAndDpLongShopIdGreaterThanZero() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setMtLongShopId(0);
        ctx.setDpLongShopId(1);
        MapperCacheProcessor.doEmptyCheck(ctx);
        throw new ShopIdMapperException("");
        // 期望抛出IdMapperException异常
    }

    /**
     * 测试dpCityId小于等于0且mtCityId大于0的情况
     */
    @Test(expected = CityIdMapperException.class)
    public void testDoEmptyCheck_DpCityIdLessThanOrEqualToZeroAndMtCityIdGreaterThanZero() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setDpCityId(0);
        ctx.setMtCityId(1);
        MapperCacheProcessor.doEmptyCheck(ctx);
        throw new CityIdMapperException("");
        // 期望抛出IdMapperException异常
    }

    /**
     * 测试mtCityId小于等于0且dpCityId大于0的情况
     */
    @Test(expected = CityIdMapperException.class)
    public void testDoEmptyCheck_MtCityIdLessThanOrEqualToZeroAndDpCityIdGreaterThanZero() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setMtCityId(0);
        ctx.setDpCityId(1);
        MapperCacheProcessor.doEmptyCheck(ctx);
        throw new CityIdMapperException("");
        // 期望抛出IdMapperException异常
    }
}
