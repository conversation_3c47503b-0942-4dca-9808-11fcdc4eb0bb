package com.dianping.mobile.mapi.dztgdetail.helper;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.detail.dto.DealGroupDetailDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.Pair;
import com.dianping.mobile.mapi.dztgdetail.entity.PurchaseNoteStructuredConfig;
import com.dianping.pigeon.remoting.common.codec.json.JacksonUtils;
import com.meituan.trip.resilience.internal.org.apache.commons.collections4.CollectionUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;

/**
 * @Author: <EMAIL>
 * @Date: 2024/1/24
 */
public class MtDealDetailBuilderTest {
    private DealGroupDetailDTO dealGroupDetailDto;
    private DealGroupBaseDTO dealGroupBaseDto;
    private MtDealDetailBuilder mtDealDetailBuilder;
    @Mock
    private DealCtx dealCtx;
    @Before
    public void setUp() throws Exception {
        mtDealDetailBuilder = new MtDealDetailBuilder();
        mtDealDetailBuilder.setDealGroupDetailDto(new DealGroupDetailDTO());
        mtDealDetailBuilder.setDealGroupBaseDto(dealGroupBaseDto);
    }

    /**
     * 测试 map 为 null 的情况
     */
    @Test
    public void testInitMap() {
        mtDealDetailBuilder.setMap(null);
        // act
        PurchaseNoteStructuredConfig config = JSON.parseObject("{\"grayLevel\":1,\"enableClientType\":[1,2,4,5],\"dealGroupIds\":[42831621,42715386],\"category2ExpId\":{\"mt502\":\"exp002675\",\"dp502\":\"exp002676\",\"mt303\":\"exp001872\",\"dp303\":\"exp001891\"},\"requestUnStructuredContent\":false}", PurchaseNoteStructuredConfig.class);
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setPNSConfig(config);
        mtDealDetailBuilder.initMap(ctx);

        // assert
        assertEquals(0, mtDealDetailBuilder.getMap().size());
    }

    @Test
    public void testRemoveToMap() {
        // arrange
        Map<Integer, Pair> map = new HashMap<>();
        map.put(2, new Pair());
        mtDealDetailBuilder.setMap(map);

        PurchaseNoteStructuredConfig config = JSON.parseObject("{\"grayLevel\":1,\"enableClientType\":[1,2,4,5],\"dealGroupIds\":[42831621,42715386],\"category2ExpId\":{\"mt502\":\"exp002675\",\"dp502\":\"exp002676\",\"mt303\":\"exp001872\",\"dp303\":\"exp001891\"},\"requestUnStructuredContent\":false}", PurchaseNoteStructuredConfig.class);
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setPNSConfig(config);
        // act
        mtDealDetailBuilder.removeToMap(ctx);

        // assert
        assertEquals(0, mtDealDetailBuilder.getMap().size());
    }

    @Test
    public void testToStructedDetails() {
        DealGroupDetailDTO dealGroupDetailDto = JacksonUtils.deserialize(dealGroupDetailDtoJson,
                DealGroupDetailDTO.class);
        DealGroupBaseDTO dealGroupBaseDto = JacksonUtils.deserialize(dealGroupBaseDtoJson, DealGroupBaseDTO.class);
        MtDealDetailBuilder dealDetailBuilder = new MtDealDetailBuilder();
        dealDetailBuilder.setDealGroupBaseDto(dealGroupBaseDto);
        dealDetailBuilder.setDealGroupDetailDto(dealGroupDetailDto);
        dealDetailBuilder.setEnableStructuredDetails(true);
        DealCtx ctx = buildDealCtx();

        Map<String, String> categoryId2ReminderSummaryMap = Lion.getMap("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.categoryId2ReminderSummaryMap", String.class, new HashMap<>());

        List<Pair> structedDetails = dealDetailBuilder.toStructedDetails(ctx, false);
        assert CollectionUtils.isNotEmpty(structedDetails);
    }

    private DealCtx buildDealCtx() {

        DealGroupDTO dealGroupDTO = JacksonUtils.deserialize(dealGroupDTOJson, DealGroupDTO.class);
        List<com.dianping.deal.attribute.dto.AttributeDTO> attrs = JacksonUtils.deserialize(attrsJson, List.class);
        EnvCtx envCtx = JacksonUtils.deserialize(envCtxJson, EnvCtx.class);
        PurchaseNoteStructuredConfig pNSConfig = JacksonUtils.deserialize(pNSConfigJson,
                PurchaseNoteStructuredConfig.class);
        DealGroupChannelDTO dealGroupChannelDTO = JacksonUtils.deserialize(dealGroupChannelDTOJson,
                DealGroupChannelDTO.class);

        DealCtx ctx = new DealCtx(envCtx);
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.setAttrs(attrs);
        ctx.setPNSConfig(pNSConfig);
        ctx.setDpDealId(0);
        ctx.setFreeDeal(false);
        ctx.setFreeDealType(null);
        ctx.setHitStructuredPurchaseNote(true);
        ctx.setChannelDTO(dealGroupChannelDTO);
        return ctx;
    }

    private static final String dealGroupDetailDtoJson = "{\"@class\":\"com.dianping.deal.detail.dto.DealGroupDetailDTO\",\"dealGroupId\":**********,\"dealGroupPics\":\"https://p1.meituan.net/merchantpic/1b504a76b72a2bf2e3e301cef1b467b1134715.jpg\",\"images\":[\"java.util.ArrayList\",[\"https://p1.meituan.net/merchantpic/1b504a76b72a2bf2e3e301cef1b467b1134715.jpg%40450w_280h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\"]],\"info\":null,\"importantPoint\":\"\",\"specialPoint\":null,\"productInfo\":null,\"editorInfo\":null,\"memberInfo\":null,\"shopInfo\":null,\"editorTeam\":null,\"summary\":null,\"templateDetailDTOs\":[\"java.util.ArrayList\",[{\"@class\":\"com.dianping.deal.detail.dto.DealGroupTemplateDetailDTO\",\"id\":**********,\"title\":\"产品介绍\",\"content\":\"<div>全身精油</div>\\n<p class=\\\"listitem\\\"></p>\\n<p class=\\\"explain\\\"></p>\\n<div class=\\\"img\\\"><img src=\\\"https://p0.meituan.net/merchantpic/39c6c9688ff28a7573095c6e7987d0c5164439.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /></div>\\n\\n\",\"type\":5,\"blockId\":0,\"mixedContents\":null,\"imageContents\":null},{\"@class\":\"com.dianping.deal.detail.dto.DealGroupTemplateDetailDTO\",\"id\":**********,\"title\":\"团购详情\",\"content\":\"        <div class=\\\"detail-tit\\\"></div>\\n        <div>\\n            <table width=\\\"100%\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" class=\\\"detail-table\\\">\\n                <thead>\\n                <tr>\\n                    <th width=\\\"50%\\\">名称</th>\\n                    <th width=\\\"25%\\\">数量</th>\\n                    <th width=\\\"25%\\\">单价</th>\\n                </tr>\\n                </thead>\\n                <tbody>\\n                            <tr>\\n                                <td>精油SPA</td>\\n                                    <td class=\\\"tc\\\">1</td>\\n                            </tr>\\n                    <tr class=\\\"total\\\">\\n                        <td></td>\\n                        <td class=\\\"tc\\\">总价<br><strong>团购价</strong></td>\\n                        <td class=\\\"tc\\\">599元<br><strong>1000元</strong>\\n                        </td>\\n                    </tr>\\n                </tbody>\\n            </table>\\n        </div>\\n\\n\",\"type\":1,\"blockId\":0,\"mixedContents\":null,\"imageContents\":null},{\"@class\":\"com.dianping.deal.detail.dto.DealGroupTemplateDetailDTO\",\"id\":**********,\"title\":\"购买须知\",\"content\":\"<div class=\\\"detail-box\\\">\\n    <div class=\\\"purchase-notes\\\">\\n\\t\\t        <dl>\\n            <dt>有效期</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">\\n    购买后90天内有效\\n        </p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>预约信息</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">无需预约，如遇消费高峰时段您可能需要排队</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>适用人数</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">每张团购券最多3人使用</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>规则提醒</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">不再与其他优惠同享</p>\\n                <p class=\\\"listitem\\\">单次到店可核销多次，可多人使用</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>温馨提示</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">如需团购券发票，请您在消费时向商户咨询</p>\\n                <p class=\\\"listitem\\\">为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！</p>\\n                <p class=\\\"listitem\\\">购买后若全部未使用，在有效期内可申请全额退款；过期自动将未使用部分退款；若部分已使用，则不可退款。</p>\\n            </dd>\\n        </dl>\\n    </div>\\n</div>\\n\",\"type\":2,\"blockId\":0,\"mixedContents\":null,\"imageContents\":null}]]}";
    private static final String dealGroupBaseDtoJson = "{\"@class\":\"com.dianping.deal.base.dto.DealGroupBaseDTO\",\"dealGroupId\":**********,\"dealGroupShortTitle\":\"桃花醉·养生·SPA\",\"dealGroupTitleDesc\":\"仅售1000元，价值1797元【三人行】解压释放《漫步云端》！\",\"maxJoin\":0,\"currentJoin\":0,\"defaultPic\":\"https://p1.meituan.net/merchantpic/1b504a76b72a2bf2e3e301cef1b467b1134715.jpg\",\"headVideo\":null,\"dealGroupPrice\":[\"java.math.BigDecimal\",1000.00],\"marketPrice\":[\"java.math.BigDecimal\",1797.00],\"beginDate\":[\"java.util.Date\",1713508123000],\"endDate\":[\"java.util.Date\",1758902400000],\"finishDate\":[\"java.util.Date\",0],\"maxPerUser\":0,\"minPerUser\":1,\"status\":1,\"autoRefundSwitch\":1,\"dealGroupPics\":\"https://p1.meituan.net/merchantpic/1b504a76b72a2bf2e3e301cef1b467b1134715.jpg\",\"saleChannel\":0,\"salePlatform\":3,\"publishStatus\":1,\"dealGroupType\":1,\"overdueAutoRefund\":true,\"canUseCoupon\":true,\"thirdPartVerify\":false,\"payChannelIDAllowed\":0,\"discountRuleID\":0,\"blockStock\":false,\"publishVersion\":0,\"lottery\":false,\"productTitle\":\"【三人行】解压释放《漫步云端》\",\"featureTitle\":\"\",\"deals\":[\"java.util.ArrayList\",[{\"@class\":\"com.dianping.deal.base.dto.DealBaseDTO\",\"dealId\":**********,\"dealGroupId\":**********,\"shortTitle\":\"【三人行】解压释放《男神专享》\",\"price\":[\"java.math.BigDecimal\",1098.00],\"marketPrice\":[\"java.math.BigDecimal\",1797.00],\"cost\":null,\"maxJoin\":0,\"currentJoin\":0,\"receiptType\":1,\"deliverType\":0,\"dealStatus\":0,\"receiptDateType\":1,\"receiptValidDays\":90,\"receiptBeginDate\":[\"java.util.Date\",1732782892000],\"receiptEndDate\":[\"java.util.Date\",1740585599000],\"refundDate\":null,\"provideInvoice\":false,\"thirdPartyId\":0,\"dealGroupPublishVersion\":0},{\"@class\":\"com.dianping.deal.base.dto.DealBaseDTO\",\"dealId\":**********,\"dealGroupId\":**********,\"shortTitle\":\"【三人行】解压释放《漫步云端》\",\"price\":[\"java.math.BigDecimal\",1000.00],\"marketPrice\":[\"java.math.BigDecimal\",1797.00],\"cost\":null,\"maxJoin\":0,\"currentJoin\":0,\"receiptType\":1,\"deliverType\":0,\"dealStatus\":1,\"receiptDateType\":1,\"receiptValidDays\":90,\"receiptBeginDate\":[\"java.util.Date\",1732782892000],\"receiptEndDate\":[\"java.util.Date\",1740585599000],\"refundDate\":null,\"provideInvoice\":false,\"thirdPartyId\":0,\"dealGroupPublishVersion\":0}]],\"sourceId\":102}";
    private static final String dealGroupDTOJson = "{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupDTO\",\"dpDealGroupId\":**********,\"mtDealGroupId\":**********,\"basic\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO\",\"categoryId\":303,\"title\":\"【三人行】解压释放《漫步云端》\",\"brandName\":\"桃花醉·养生·SPA\",\"titleDesc\":\"仅售1000元，价值1797元【三人行】解压释放《漫步云端》！\",\"beginSaleDate\":\"2024-04-19 14:28:43\",\"endSaleDate\":\"2025-09-27 00:00:00\",\"status\":1,\"saleChannel\":0,\"salePlatform\":3,\"sourceId\":102,\"tradeType\":19},\"image\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupImageDTO\",\"defaultPicPath\":\"https://p1.meituan.net/merchantpic/1b504a76b72a2bf2e3e301cef1b467b1134715.jpg\",\"allPicPaths\":\"https://p1.meituan.net/merchantpic/1b504a76b72a2bf2e3e301cef1b467b1134715.jpg\",\"videoPath\":null,\"videoCoverPath\":null,\"videoSize\":null,\"extendVideos\":null,\"allVideos\":null},\"category\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO\",\"categoryId\":303,\"serviceType\":\"精油SPA\",\"serviceTypeId\":108009},\"bgBu\":null,\"serviceProject\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO\",\"title\":\"团购详情\",\"salePrice\":\"1000.0\",\"marketPrice\":\"599.0\",\"mustGroups\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO\",\"groups\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO\",\"skuId\":0,\"categoryId\":2104613,\"name\":\"精油SPA\",\"amount\":1,\"marketPrice\":null,\"status\":10,\"attrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":3108,\"attrName\":\"serviceTechnique\",\"chnName\":\"服务手法\",\"attrValue\":\"精油SPA\",\"rawAttrValue\":\"精油SPA\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":3022,\"attrName\":\"serviceProcessArrayNew\",\"chnName\":\"服务流程\",\"attrValue\":\"[{\\\"servicemethod\\\":\\\"全身 \\\",\\\"stepTime\\\":\\\"60\\\"}]\",\"rawAttrValue\":\"[{\\\"servicemethod\\\":\\\"全身 \\\",\\\"stepTime\\\":\\\"60\\\"}]\",\"unit\":null,\"valueType\":300,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":2441,\"attrName\":\"serviceDurationInt\",\"chnName\":\"服务时长\",\"attrValue\":\"60\",\"rawAttrValue\":\"60\",\"unit\":null,\"valueType\":401,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":3109,\"attrName\":\"serviceBodyRange\",\"chnName\":\"具体服务部位\",\"attrValue\":\"肩颈、腰背、背部、其他\",\"rawAttrValue\":\"肩颈、腰背、背部、其他\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":184471,\"attrName\":\"bodyRegion\",\"chnName\":\"服务部位范围\",\"attrValue\":\"全身\",\"rawAttrValue\":\"全身\",\"unit\":null,\"valueType\":500,\"sequence\":0}]]}]]}]],\"optionGroups\":[\"java.util.ArrayList\",[]],\"structType\":\"uniform-structure-table\"},\"channel\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupChannelDTO\",\"channelId\":3,\"channelEn\":\"joy\",\"channelCn\":\"休闲娱乐\",\"channelGroupId\":2,\"channelGroupEn\":\"general\",\"channelGroupCn\":\"到店综合\"},\"attrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_finish_date\",\"value\":[\"java.util.ArrayList\",[\"1970-01-01 08:00:00\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"calc_holiday_available\",\"value\":[\"java.util.ArrayList\",[\"1\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_business_type\",\"value\":[\"java.util.ArrayList\",[\"1\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_channel_id_allowed\",\"value\":[\"java.util.ArrayList\",[\"0\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_can_use_coupon\",\"value\":[\"java.util.ArrayList\",[\"true\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"preSaleTag\",\"value\":[\"java.util.ArrayList\",[\"false\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_third_party_verify\",\"value\":[\"java.util.ArrayList\",[\"false\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_block_stock\",\"value\":[\"java.util.ArrayList\",[\"false\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_discount_rule_id\",\"value\":[\"java.util.ArrayList\",[\"0\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"service_type\",\"value\":[\"java.util.ArrayList\",[\"精油SPA\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"tag_unifyProduct\",\"value\":[\"java.util.ArrayList\",[\"0\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"single_verification_quantity_desc\",\"value\":[\"java.util.ArrayList\",[\"单次到店可核销多次，可多人使用\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"limit_the_number_of_users\",\"value\":[\"java.util.ArrayList\",[\"3\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"reservation_is_needed_or_not\",\"value\":[\"java.util.ArrayList\",[\"否\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"sys_deal_universal_type\",\"value\":[\"java.util.ArrayList\",[\"1\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"category\",\"value\":[\"java.util.ArrayList\",[\"30\",\"3006\"]],\"source\":0,\"cnName\":null,\"type\":null}]],\"rule\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO\",\"buyRule\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.rule.buy.DealGroupBuyRuleDTO\",\"maxPerUser\":0,\"minPerUser\":1},\"useRule\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO\",\"receiptEffectiveDate\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.rule.use.ReceiptEffectiveDateDTO\",\"receiptDateType\":1,\"receiptValidDays\":90,\"receiptBeginDate\":\"2024-11-28 16:34:52\",\"receiptEndDate\":\"2025-02-26 23:59:59\",\"showText\":\"购买后90天内有效\"},\"availableDate\":null,\"disableDate\":null},\"bookingRule\":null,\"refundRule\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.rule.refund.DealGroupRefundRuleDTO\",\"supportRefundType\":1,\"supportOverdueAutoRefund\":true}},\"displayShopInfo\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupDisplayShopDTO\",\"dpDisplayShopIds\":[\"java.util.ArrayList\",[711899863]],\"mtDisplayShopIds\":[\"java.util.ArrayList\",[711899863]]},\"verifyShopInfo\":null,\"tags\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100000043,\"tagName\":\"推油\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100000036,\"tagName\":\"推拿\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100001020,\"tagName\":\"油压\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100000037,\"tagName\":\"SPA\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100002048,\"tagName\":\"精油\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100069840,\"tagName\":\"全身\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100069836,\"tagName\":\"其他\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100069835,\"tagName\":\"腰背\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100069834,\"tagName\":\"肩颈\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100056896,\"tagName\":\"30-60分钟\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100007272,\"tagName\":\"足疗按摩虚拟节点\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100006171,\"tagName\":\"SPA\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100036029,\"tagName\":\"部位\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100036030,\"tagName\":\"全身\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100036033,\"tagName\":\"背部\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100036043,\"tagName\":\"肩颈\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100036044,\"tagName\":\"项目功效\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100036045,\"tagName\":\"养生\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100090589,\"tagName\":\"60分钟全身精油SPA待升级\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100310117,\"tagName\":\"艾灸\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100295313,\"tagName\":\"精油SPA\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100292942,\"tagName\":\"肩颈\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100304260,\"tagName\":\"按摩\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100303255,\"tagName\":\"走罐\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100300329,\"tagName\":\"拔罐减肥\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100305297,\"tagName\":\"全身SPA\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100292947,\"tagName\":\"中医推拿\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100312100,\"tagName\":\"肩颈SPA\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100298349,\"tagName\":\"肩颈按摩\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100312080,\"tagName\":\"推拿按摩\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100305300,\"tagName\":\"头部按摩\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100304561,\"tagName\":\"正骨\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100303620,\"tagName\":\"养气血\"}]],\"customer\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupCustomerDTO\",\"originCustomerId\":43902963,\"platformCustomerId\":1105105829},\"regions\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupRegionDTO\",\"dpCityId\":1,\"mtCityId\":10}]],\"notice\":null,\"notice2b\":null,\"deals\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO\",\"dealId\":**********,\"basic\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.deal.DealBasicDTO\",\"title\":\"【三人行】解压释放《漫步云端》\",\"originTitle\":\"【三人行】解压释放《漫步云端》\",\"thirdPartyId\":null,\"status\":1,\"thirdPartyDealId\":null},\"price\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.PriceDTO\",\"salePrice\":\"1000.00\",\"marketPrice\":\"1797.00\",\"version\":11035348979,\"status\":null,\"prePayPrice\":null,\"finalPayPrice\":null},\"stock\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StockDTO\",\"dpSales\":4,\"dpTotal\":2000000,\"dpRemain\":1999996,\"mtSales\":6,\"mtTotal\":2000000,\"mtRemain\":1999994,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1,\"sharedSales\":0,\"sharedTotal\":0,\"sharedRemain\":0,\"isSharedSoldOut\":false},\"attrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"sys_multi_sale_number\",\"value\":[\"java.util.ArrayList\",[\"3\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"sku_receipt_type\",\"value\":[\"java.util.ArrayList\",[\"1\"]],\"source\":0,\"cnName\":null,\"type\":null}]],\"dealTimeStockDTO\":null,\"dealTimeStockPlanDTO\":null,\"rule\":null,\"image\":null,\"displayShop\":null,\"dealDelivery\":null,\"shopStocks\":null,\"dealIdInt\":**********}]],\"price\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.PriceDTO\",\"salePrice\":\"1000.00\",\"marketPrice\":\"1797.00\",\"version\":11035348979,\"status\":null,\"prePayPrice\":null,\"finalPayPrice\":null},\"stock\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StockDTO\",\"dpSales\":4,\"dpTotal\":2000000,\"dpRemain\":1999996,\"mtSales\":6,\"mtTotal\":2000000,\"mtRemain\":1999994,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1,\"sharedSales\":null,\"sharedTotal\":null,\"sharedRemain\":null,\"isSharedSoldOut\":null},\"detail\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.detail.DealGroupDetailDTO\",\"dealGroupPics\":\"https://p1.meituan.net/merchantpic/1b504a76b72a2bf2e3e301cef1b467b1134715.jpg\",\"images\":[\"java.util.ArrayList\",[\"https://p1.meituan.net/merchantpic/1b504a76b72a2bf2e3e301cef1b467b1134715.jpg%40450w_280h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\"]],\"info\":null,\"importantPoint\":\"\",\"specialPoint\":null,\"productInfo\":null,\"editorInfo\":null,\"memberInfo\":null,\"shopInfo\":null,\"editorTeam\":null,\"summary\":null,\"templateDetailDTOs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.detail.DealGroupTemplateDetailDTO\",\"title\":\"产品介绍\",\"content\":\"<div>全身精油</div>\\n<p class=\\\"listitem\\\"></p>\\n<p class=\\\"explain\\\"></p>\\n<div class=\\\"img\\\"><img src=\\\"https://p0.meituan.net/merchantpic/39c6c9688ff28a7573095c6e7987d0c5164439.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /></div>\\n\\n\",\"type\":5,\"blockId\":0,\"mixedContents\":null,\"imageContents\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.detail.DealGroupTemplateDetailDTO\",\"title\":\"团购详情\",\"content\":\"        <div class=\\\"detail-tit\\\"></div>\\n        <div>\\n            <table width=\\\"100%\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" class=\\\"detail-table\\\">\\n                <thead>\\n                <tr>\\n                    <th width=\\\"50%\\\">名称</th>\\n                    <th width=\\\"25%\\\">数量</th>\\n                    <th width=\\\"25%\\\">单价</th>\\n                </tr>\\n                </thead>\\n                <tbody>\\n                            <tr>\\n                                <td>精油SPA</td>\\n                                    <td class=\\\"tc\\\">1</td>\\n                            </tr>\\n                    <tr class=\\\"total\\\">\\n                        <td></td>\\n                        <td class=\\\"tc\\\">总价<br><strong>团购价</strong></td>\\n                        <td class=\\\"tc\\\">599元<br><strong>1000元</strong>\\n                        </td>\\n                    </tr>\\n                </tbody>\\n            </table>\\n        </div>\\n\\n\",\"type\":1,\"blockId\":0,\"mixedContents\":null,\"imageContents\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.detail.DealGroupTemplateDetailDTO\",\"title\":\"购买须知\",\"content\":\"<div class=\\\"detail-box\\\">\\n    <div class=\\\"purchase-notes\\\">\\n\\t\\t        <dl>\\n            <dt>有效期</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">\\n    购买后90天内有效\\n        </p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>预约信息</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">无需预约，如遇消费高峰时段您可能需要排队</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>适用人数</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">每张团购券最多3人使用</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>规则提醒</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">不再与其他优惠同享</p>\\n                <p class=\\\"listitem\\\">单次到店可核销多次，可多人使用</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>温馨提示</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">如需团购券发票，请您在消费时向商户咨询</p>\\n                <p class=\\\"listitem\\\">为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！</p>\\n                <p class=\\\"listitem\\\">购买后若全部未使用，在有效期内可申请全额退款；过期自动将未使用部分退款；若部分已使用，则不可退款。</p>\\n            </dd>\\n        </dl>\\n    </div>\\n</div>\\n\",\"type\":2,\"blockId\":0,\"mixedContents\":null,\"imageContents\":null}]]},\"spu\":null,\"standardServiceProject\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectDTO\",\"mustGroups\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectGroupDTO\",\"serviceProjectItems\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectItemDTO\",\"serviceProjectName\":\"精油SPA\",\"standardAttribute\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeDTO\",\"attrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"serviceProcessArrayNew\",\"attrCnName\":\"服务流程\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":1,\"simpleValues\":null,\"complexValues\":\"[{\\\"attrs\\\":[{\\\"attrName\\\":\\\"servicemethod\\\",\\\"attrCnName\\\":\\\"服务方式\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"全身 \\\"]}]},{\\\"attrName\\\":\\\"stepTime\\\",\\\"attrCnName\\\":\\\"步骤耗时\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"60.0\\\"]}]}],\\\"cpvObjectId\\\":20437879}]\"}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"skuCateId\",\"attrCnName\":\"项目分类\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"2104613\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"bodyRegion\",\"attrCnName\":\"服务部位范围\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"全身\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"serviceTechnique\",\"attrCnName\":\"服务手法\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"精油SPA\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"serviceBodyRange\",\"attrCnName\":\"具体服务部位\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"[\\\"肩颈\\\",\\\"腰背\\\",\\\"背部\\\",\\\"其他\\\"]\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"serviceDurationInt\",\"attrCnName\":\"服务时长\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"60\"]],\"complexValues\":null}]]}]],\"cpvObjectId\":20369521,\"cpvObjectVersion\":8},\"marketPrice\":null,\"amount\":null}]],\"optionalCount\":0}]],\"optionalGroups\":[\"java.util.ArrayList\",[]]},\"extendImage\":null,\"combines\":null,\"saleChannelAggregation\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.SaleChannelAggregationDTO\",\"allSupport\":true,\"supportChannels\":[\"java.util.ArrayList\",[]],\"notSupportChannels\":[\"java.util.ArrayList\",[]]},\"purchaseNote\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.PurchaseNoteDTO\",\"title\":\"购买须知\",\"modules\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"适用时间\",\"icon\":\"https://p0.meituan.net/travelcube/eb358d1a211285207c636aa95594f3751524.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"有效时间\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"购买后90天内有效\"}]]}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"预约规则\",\"icon\":\"https://p0.meituan.net/travelcube/17303e587e8242902a1bf6ecd3eab10b1029.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"无需预约，如遇消费高峰时段您可能需要排队\"}]]}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"适用人数\",\"icon\":\"https://p0.meituan.net/travelcube/1f18aa2c0100e75060daf348283f3c861351.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"单次到店可核销多次\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"每张团购券最多3人使用\"}]]}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"其他规则\",\"icon\":\"https://p1.meituan.net/travelcube/4dac95c5f43a52f49b81ece1918925ea858.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"不再与其他优惠同享\"}]]}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"温馨提示\",\"icon\":\"https://p1.meituan.net/travelcube/09a2f606b1636f8b135b8582e3c0a53d1494.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"如需团购券发票，请您在消费时向商户咨询\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"购买后若全部未使用，在有效期内可申请全额退款；过期自动将未使用部分退款；若部分已使用，则不可退款。\"}]]}]]}]]},\"dpDealGroupIdInt\":**********,\"mtDealGroupIdInt\":**********}";
    private static final String attrsJson = "[\"java.util.ArrayList\",[{\"@class\":\"com.dianping.deal.attribute.dto.AttributeDTO\",\"name\":\"product_finish_date\",\"value\":[\"java.util.ArrayList\",[\"1970-01-01 08:00:00\"]],\"source\":0},{\"@class\":\"com.dianping.deal.attribute.dto.AttributeDTO\",\"name\":\"calc_holiday_available\",\"value\":[\"java.util.ArrayList\",[\"1\"]],\"source\":0},{\"@class\":\"com.dianping.deal.attribute.dto.AttributeDTO\",\"name\":\"product_business_type\",\"value\":[\"java.util.ArrayList\",[\"1\"]],\"source\":0},{\"@class\":\"com.dianping.deal.attribute.dto.AttributeDTO\",\"name\":\"product_channel_id_allowed\",\"value\":[\"java.util.ArrayList\",[\"0\"]],\"source\":0},{\"@class\":\"com.dianping.deal.attribute.dto.AttributeDTO\",\"name\":\"product_can_use_coupon\",\"value\":[\"java.util.ArrayList\",[\"true\"]],\"source\":0},{\"@class\":\"com.dianping.deal.attribute.dto.AttributeDTO\",\"name\":\"preSaleTag\",\"value\":[\"java.util.ArrayList\",[\"false\"]],\"source\":0},{\"@class\":\"com.dianping.deal.attribute.dto.AttributeDTO\",\"name\":\"product_third_party_verify\",\"value\":[\"java.util.ArrayList\",[\"false\"]],\"source\":0},{\"@class\":\"com.dianping.deal.attribute.dto.AttributeDTO\",\"name\":\"product_block_stock\",\"value\":[\"java.util.ArrayList\",[\"false\"]],\"source\":0},{\"@class\":\"com.dianping.deal.attribute.dto.AttributeDTO\",\"name\":\"product_discount_rule_id\",\"value\":[\"java.util.ArrayList\",[\"0\"]],\"source\":0},{\"@class\":\"com.dianping.deal.attribute.dto.AttributeDTO\",\"name\":\"service_type\",\"value\":[\"java.util.ArrayList\",[\"精油SPA\"]],\"source\":0},{\"@class\":\"com.dianping.deal.attribute.dto.AttributeDTO\",\"name\":\"tag_unifyProduct\",\"value\":[\"java.util.ArrayList\",[\"0\"]],\"source\":0},{\"@class\":\"com.dianping.deal.attribute.dto.AttributeDTO\",\"name\":\"single_verification_quantity_desc\",\"value\":[\"java.util.ArrayList\",[\"单次到店可核销多次，可多人使用\"]],\"source\":0},{\"@class\":\"com.dianping.deal.attribute.dto.AttributeDTO\",\"name\":\"limit_the_number_of_users\",\"value\":[\"java.util.ArrayList\",[\"3\"]],\"source\":0},{\"@class\":\"com.dianping.deal.attribute.dto.AttributeDTO\",\"name\":\"reservation_is_needed_or_not\",\"value\":[\"java.util.ArrayList\",[\"否\"]],\"source\":0},{\"@class\":\"com.dianping.deal.attribute.dto.AttributeDTO\",\"name\":\"sys_deal_universal_type\",\"value\":[\"java.util.ArrayList\",[\"1\"]],\"source\":0},{\"@class\":\"com.dianping.deal.attribute.dto.AttributeDTO\",\"name\":\"category\",\"value\":[\"java.util.ArrayList\",[\"30\",\"3006\"]],\"source\":0}]]";
    private static final String envCtxJson = "{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx\",\"dpUserId\":**********,\"dpVirtualUserId\":**********,\"mtUserId\":**********,\"mtVirtualUserId\":**********,\"unionId\":\"23a1cdf8d2794ca79a6e52948ea22228a171946598025470584\",\"dpId\":\"23a1cdf8d2794ca79a6e52948ea22228a171946598025470584\",\"uuid\":\"0000000000000F44351DD90D94BB49DDFF6EB3854AC8EA171946598023583060\",\"version\":\"12.27.200\",\"clientType\":200501,\"mpAppId\":null,\"mpSource\":null,\"openId\":null,\"appDeviceId\":\"23a1cdf8d2794ca79a6e52948ea22228a171946598025470584\",\"appId\":10,\"mtsiFlag\":null,\"requestURI\":\"/general/platform/dztgdetail/dzdealbase.bin\",\"userAgent\":\"MApi 1.3 (com.sankuai.meituan 12.27.200 meituaninternaltest PJE110; Android 14)\",\"userIp\":\"*************\",\"dztgClientTypeEnum\":\"MEITUAN_APP\",\"thirdDztgClientTypeEnum\":null,\"startTime\":1732782892635,\"meituanClientList\":[\"java.util.ArrayList\",[\"MEITUAN_APP\",\"MEITUAN_FROM_H5\",\"MEITUAN_MAP_APP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_LIVE_WEIXIN_MINIAPP\"]],\"dianpingClientList\":[\"java.util.ArrayList\",[\"DIANPING_APP\",\"DIANPING_FROM_H5\",\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"]],\"merchantClientList\":[\"java.util.ArrayList\",[\"DPMERCHANT\"]],\"mtMiniAppList\":[\"java.util.ArrayList\",[\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_KUAISHOU_MINIAPP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_LIVE_WEIXIN_MINIAPP\"]],\"dpMiniAppList\":[\"java.util.ArrayList\",[\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"]],\"nativeAppList\":[\"java.util.ArrayList\",[\"MEITUAN_APP\",\"MEITUAN_MAP_APP\",\"DIANPING_APP\"]],\"wxMiniList\":[\"java.util.ArrayList\",[\"MEITUAN_WEIXIN_MINIAPP\",\"DIANPING_WEIXIN_MINIAPP\"]],\"virtualUserId\":**********,\"dpMerchant\":false,\"android\":true,\"fromH5\":false,\"harmony\":false,\"mt\":true,\"apollo\":false,\"login\":true,\"mainApp\":true,\"miniApp\":false,\"thirdPlatform\":false,\"ios\":false,\"dp\":false,\"mtMiniApp\":false,\"dpMiniApp\":false,\"mainWX\":false,\"externalAndNoScene\":false,\"mtLiveMinApp\":false,\"wxMini\":false,\"mainWeb\":false,\"userId\":**********,\"external\":false,\"native\":true}";
    private static final String pNSConfigJson = "{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.entity.PurchaseNoteStructuredConfig\",\"grayLevel\":1,\"enableClientType\":[\"java.util.ArrayList\",[1,2,4,5,14]],\"dealGroupIds\":[\"java.util.ArrayList\",[**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,942637959,942601533,942582888]],\"category2ExpId\":{\"@class\":\"java.util.LinkedHashMap\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt2009\\\"}\":\"exp003154\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp2009\\\"}\":\"exp003155\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt501\\\"}\":\"exp003181\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp501\\\"}\":\"exp003179\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt303\\\"}\":\"exp003198\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp303\\\"}\":\"exp003197\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt304\\\"}\":\"exp003198\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp304\\\"}\":\"exp003197\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt502\\\"}\":\"exp003198\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp502\\\"}\":\"exp003197\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt506\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt503\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1604\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1612\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1613\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1614\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1615\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1616\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1617\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1618\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1619\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1620\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1621\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1622\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1623\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1624\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1625\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1626\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1627\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1628\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1629\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1630\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1313\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1314\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1601\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1602\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1603\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1605\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1606\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1607\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1608\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1609\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt1610\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt301\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt302\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt308\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt314\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt311\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt318\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt320\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt454\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt802\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt803\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt319\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt323\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt306\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt316\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt315\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt505\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt509\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt511\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt514\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"mt515\\\"}\":\"exp003296\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp503\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp506\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1604\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1612\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1613\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1614\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1615\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1616\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1617\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1618\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1619\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1620\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1621\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1622\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1623\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1624\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1625\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1626\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1627\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1628\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1629\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1630\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1313\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1314\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1601\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1602\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1603\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1605\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1606\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1607\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1608\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1609\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp1610\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp301\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp302\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp308\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp314\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp311\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp318\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp320\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp454\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp802\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp803\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp319\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp323\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp306\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp316\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp315\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp505\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp509\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp511\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp514\\\"}\":\"exp003295\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dp515\\\"}\":\"exp003295\"},\"requestUnStructuredContent\":true}";
    private static final String dealGroupChannelDTOJson = "{\"@class\":\"com.dianping.deal.publishcategory.dto.DealGroupChannelDTO\",\"dealGroupId\":**********,\"categoryId\":303,\"channelDTO\":{\"@class\":\"com.dianping.deal.publishcategory.dto.ChannelDTO\",\"channelId\":3,\"channelEn\":\"joy\",\"channelCn\":\"休闲娱乐\",\"channelGroupId\":2,\"channelGroupEn\":\"general\",\"channelGroupCn\":\"到店综合\"}}";

}
