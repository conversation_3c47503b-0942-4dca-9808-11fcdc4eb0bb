package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.mobile.mapi.dztgdetail.biz.hotstyle.NailStyleService;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetNailStyleImageRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.nailstyle.OrderNailStyleImageVO;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class NailStyleFacade_GetOrderNailStyleTest {

    @InjectMocks
    private NailStyleFacade nailStyleFacade;

    @Mock
    private NailStyleService nailStyleService;

    private MockedStatic<LionConfigUtils> mockedLionConfigUtils;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        mockedLionConfigUtils = mockStatic(LionConfigUtils.class);
    }

    @After
    public void tearDown() {
        mockedLionConfigUtils.close();
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetOrderNailStyleInvalidRequest() throws Throwable {
        GetNailStyleImageRequest request = new GetNailStyleImageRequest();
        request.setDealGroupId(-1L);
        request.setShopId(0L);
        nailStyleFacade.getOrderNailStyle(request, new EnvCtx());
    }
}
