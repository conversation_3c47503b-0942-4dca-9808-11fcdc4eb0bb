package com.dianping.mobile.mapi.dztgdetail.availabletime.impl;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;

import static org.junit.Assert.*;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/12/6 16:36
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest()
public class WineBarAvailableTimeStrategyTest {

    @InjectMocks
    private WineBarAvailableTimeStrategy wineBarAvailableTimeStrategy;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testWineBarAvailableTime() throws InvocationTargetException, IllegalAccessException {
        String avaTime = "周一至周四19:00-21:30";
        List<String> avaTimeList = Lists.newArrayList();
        avaTimeList.add(avaTime);

        Method method = PowerMockito.method(WineBarAvailableTimeStrategy.class, "getTimeOfDayFormAvailableTime");
        String result = (String) method.invoke(wineBarAvailableTimeStrategy,avaTimeList);


        Assert.assertNotNull(result);
    }

    @Test
    public void testGetTimeOfDayFormAvailableTime() throws InvocationTargetException, IllegalAccessException {
        String avaTime = "周一至周四19:00-21:30";
        avaTime = "";
        List<String> avaTimeList = Lists.newArrayList();
        avaTimeList.add(avaTime);
        Method method = PowerMockito.method(WineBarAvailableTimeStrategy.class, "getTimeOfDayFormAvailableTime");
        String result = (String) method.invoke(wineBarAvailableTimeStrategy,avaTimeList);

        avaTime = "周一至周四 19:00-21:30";
        avaTimeList.add(avaTime);
        result = (String) method.invoke(wineBarAvailableTimeStrategy,avaTimeList);
        Assert.assertNotNull(result);
    }

    @Test
    public void testGetTimeOfDayFormAvailableTimeNull() throws InvocationTargetException, IllegalAccessException {
        String avaTime = null;
        List<String> avaTimeList = Lists.newArrayList();
        avaTimeList.add(avaTime);
        Method method = PowerMockito.method(WineBarAvailableTimeStrategy.class, "getTimeOfDayFormAvailableTime");
        String result = (String) method.invoke(wineBarAvailableTimeStrategy,avaTimeList);
        Assert.assertNotNull(result);
    }

}