package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DigestQueryWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.sankuai.mpmctcontent.query.thrift.dto.digest.DigestInfoItemDTO;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class DigestQueryV2ProcessorProcessTest {

    private DigestQueryV2Processor processor;

    @Mock
    private DigestQueryWrapper digestQueryWrapper;

    @Mock
    private FutureCtx futureCtx;

    @Mock
    private Future future;

    @Mock
    private DealCtx dealCtx;

    @Before
    public void setUp() {
        processor = new DigestQueryV2Processor();
        // Use ReflectionTestUtils to set the private field
        ReflectionTestUtils.setField(processor, "digestQueryWrapper", digestQueryWrapper);
        when(dealCtx.getFutureCtx()).thenReturn(futureCtx);
    }

    /**
     * Test case for process method when usePlatformHeadPicFuture is null
     */
    @Test
    public void testProcessWhenUsePlatformHeadPicFutureIsNull() throws Throwable {
        // arrange
        when(futureCtx.getUsePlatformHeadPicFuture()).thenReturn(null);
        // act
        processor.process(dealCtx);
        // assert
        verify(futureCtx, atLeastOnce()).getUsePlatformHeadPicFuture();
        verify(digestQueryWrapper, never()).getDigestInfoItemDTO(any(), anyLong());
        verify(dealCtx, never()).setUsePlatformHeadPic(any());
    }

    /**
     * Test case for process method when data JSON is null
     */
    @Test
    public void testProcessWhenDataJsonIsNull() throws Throwable {
        // arrange
        when(futureCtx.getUsePlatformHeadPicFuture()).thenReturn(future);
        when(dealCtx.getMtLongShopId()).thenReturn(123456L);
        DigestInfoItemDTO digestInfoItemDTO = mock(DigestInfoItemDTO.class);
        when(digestQueryWrapper.getDigestInfoItemDTO(future, 123456L)).thenReturn(digestInfoItemDTO);
        when(digestInfoItemDTO.getData()).thenReturn(null);
        // act
        processor.process(dealCtx);
        // assert
        verify(futureCtx, atLeastOnce()).getUsePlatformHeadPicFuture();
        verify(digestQueryWrapper, times(1)).getDigestInfoItemDTO(future, 123456L);
        verify(digestInfoItemDTO, times(1)).getData();
        verify(dealCtx, never()).setUsePlatformHeadPic(any());
    }

    /**
     * Test case for process method when data JSON is empty
     */
    @Test
    public void testProcessWhenDataJsonIsEmpty() throws Throwable {
        // arrange
        when(futureCtx.getUsePlatformHeadPicFuture()).thenReturn(future);
        when(dealCtx.getMtLongShopId()).thenReturn(123456L);
        DigestInfoItemDTO digestInfoItemDTO = mock(DigestInfoItemDTO.class);
        when(digestQueryWrapper.getDigestInfoItemDTO(future, 123456L)).thenReturn(digestInfoItemDTO);
        when(digestInfoItemDTO.getData()).thenReturn("");
        // act
        processor.process(dealCtx);
        // assert
        verify(futureCtx, atLeastOnce()).getUsePlatformHeadPicFuture();
        verify(digestQueryWrapper, times(1)).getDigestInfoItemDTO(future, 123456L);
        verify(digestInfoItemDTO, times(1)).getData();
        verify(dealCtx, never()).setUsePlatformHeadPic(any());
    }

    /**
     * Test case for process method when data JSON is invalid
     * This test uses a try-catch block to handle the JSON parsing exception
     */
    @Test
    public void testProcessWhenDataJsonIsInvalid() throws Throwable {
        // arrange
        when(futureCtx.getUsePlatformHeadPicFuture()).thenReturn(future);
        when(dealCtx.getMtLongShopId()).thenReturn(123456L);
        DigestInfoItemDTO digestInfoItemDTO = mock(DigestInfoItemDTO.class);
        when(digestQueryWrapper.getDigestInfoItemDTO(future, 123456L)).thenReturn(digestInfoItemDTO);
        when(digestInfoItemDTO.getData()).thenReturn("invalid json");
        // act & assert
        try {
            processor.process(dealCtx);
        } catch (Exception e) {
            // Expected exception
            verify(futureCtx, atLeastOnce()).getUsePlatformHeadPicFuture();
            verify(digestQueryWrapper, times(1)).getDigestInfoItemDTO(future, 123456L);
            verify(digestInfoItemDTO, times(1)).getData();
            verify(dealCtx, never()).setUsePlatformHeadPic(any());
        }
    }

    /**
     * Test case for process method when dataJSONObject is empty JSON
     * In this case, getBoolean("usePlatformHeadPic") returns null, not false
     */
    @Test
    public void testProcessWhenDataJSONObjectIsEmpty() throws Throwable {
        // arrange
        when(futureCtx.getUsePlatformHeadPicFuture()).thenReturn(future);
        when(dealCtx.getMtLongShopId()).thenReturn(123456L);
        DigestInfoItemDTO digestInfoItemDTO = mock(DigestInfoItemDTO.class);
        when(digestQueryWrapper.getDigestInfoItemDTO(future, 123456L)).thenReturn(digestInfoItemDTO);
        when(digestInfoItemDTO.getData()).thenReturn("{}");
        // act
        processor.process(dealCtx);
        // assert
        verify(futureCtx, atLeastOnce()).getUsePlatformHeadPicFuture();
        verify(digestQueryWrapper, times(1)).getDigestInfoItemDTO(future, 123456L);
        verify(digestInfoItemDTO, times(1)).getData();
        // When the JSON is empty, getBoolean returns null, so setUsePlatformHeadPic is called with null
        verify(dealCtx, times(1)).setUsePlatformHeadPic(null);
    }

    /**
     * Test case for process method when usePlatformHeadPic is true
     */
    @Test
    public void testProcessWhenUsePlatformHeadPicIsTrue() throws Throwable {
        // arrange
        when(futureCtx.getUsePlatformHeadPicFuture()).thenReturn(future);
        when(dealCtx.getMtLongShopId()).thenReturn(123456L);
        DigestInfoItemDTO digestInfoItemDTO = mock(DigestInfoItemDTO.class);
        when(digestQueryWrapper.getDigestInfoItemDTO(future, 123456L)).thenReturn(digestInfoItemDTO);
        when(digestInfoItemDTO.getData()).thenReturn("{\"usePlatformHeadPic\":true}");
        // act
        processor.process(dealCtx);
        // assert
        verify(futureCtx, atLeastOnce()).getUsePlatformHeadPicFuture();
        verify(digestQueryWrapper, times(1)).getDigestInfoItemDTO(future, 123456L);
        verify(digestInfoItemDTO, times(1)).getData();
        verify(dealCtx, times(1)).setUsePlatformHeadPic(true);
    }

    /**
     * Test case for process method when usePlatformHeadPic is false
     */
    @Test
    public void testProcessWhenUsePlatformHeadPicIsFalse() throws Throwable {
        // arrange
        when(futureCtx.getUsePlatformHeadPicFuture()).thenReturn(future);
        when(dealCtx.getMtLongShopId()).thenReturn(123456L);
        DigestInfoItemDTO digestInfoItemDTO = mock(DigestInfoItemDTO.class);
        when(digestQueryWrapper.getDigestInfoItemDTO(future, 123456L)).thenReturn(digestInfoItemDTO);
        when(digestInfoItemDTO.getData()).thenReturn("{\"usePlatformHeadPic\":false}");
        // act
        processor.process(dealCtx);
        // assert
        verify(futureCtx, atLeastOnce()).getUsePlatformHeadPicFuture();
        verify(digestQueryWrapper, times(1)).getDigestInfoItemDTO(future, 123456L);
        verify(digestInfoItemDTO, times(1)).getData();
        verify(dealCtx, times(1)).setUsePlatformHeadPic(false);
    }

    /**
     * Test case for process method when usePlatformHeadPic field is missing in JSON
     * In this case, getBoolean("usePlatformHeadPic") returns null, not false
     */
    @Test
    public void testProcessWhenUsePlatformHeadPicFieldIsMissing() throws Throwable {
        // arrange
        when(futureCtx.getUsePlatformHeadPicFuture()).thenReturn(future);
        when(dealCtx.getMtLongShopId()).thenReturn(123456L);
        DigestInfoItemDTO digestInfoItemDTO = mock(DigestInfoItemDTO.class);
        when(digestQueryWrapper.getDigestInfoItemDTO(future, 123456L)).thenReturn(digestInfoItemDTO);
        when(digestInfoItemDTO.getData()).thenReturn("{\"otherField\":\"value\"}");
        // act
        processor.process(dealCtx);
        // assert
        verify(futureCtx, atLeastOnce()).getUsePlatformHeadPicFuture();
        verify(digestQueryWrapper, times(1)).getDigestInfoItemDTO(future, 123456L);
        verify(digestInfoItemDTO, times(1)).getData();
        // When the field is missing, getBoolean returns null, so setUsePlatformHeadPic is called with null
        verify(dealCtx, times(1)).setUsePlatformHeadPic(null);
    }

    /**
     * Test case for process method with complete happy path
     */
    @Test
    public void testProcessCompleteHappyPath() throws Throwable {
        // arrange
        when(futureCtx.getUsePlatformHeadPicFuture()).thenReturn(future);
        when(dealCtx.getMtLongShopId()).thenReturn(123456L);
        DigestInfoItemDTO digestInfoItemDTO = mock(DigestInfoItemDTO.class);
        when(digestQueryWrapper.getDigestInfoItemDTO(future, 123456L)).thenReturn(digestInfoItemDTO);
        when(digestInfoItemDTO.getData()).thenReturn("{\"usePlatformHeadPic\":true, \"otherField\":\"value\"}");
        // act
        processor.process(dealCtx);
        // assert
        verify(futureCtx, atLeastOnce()).getUsePlatformHeadPicFuture();
        verify(digestQueryWrapper, times(1)).getDigestInfoItemDTO(future, 123456L);
        verify(digestInfoItemDTO, times(1)).getData();
        verify(dealCtx, times(1)).setUsePlatformHeadPic(true);
    }
}
