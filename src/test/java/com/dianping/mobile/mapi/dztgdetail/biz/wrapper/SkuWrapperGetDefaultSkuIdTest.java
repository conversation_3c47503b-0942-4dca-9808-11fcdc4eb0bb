package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DefaultSelectDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class SkuWrapperGetDefaultSkuIdTest {

    @Mock
    private DealGroupQueryService dealGroupQueryService;

    @InjectMocks
    private SkuWrapper skuWrapper = Mockito.spy(new SkuWrapper());

    private DealBaseReq req;

    private EnvCtx envCtx;

    @Before
    public void setUp() {
        req = new DealBaseReq();
        envCtx = new EnvCtx();
        // Reset mock for each test
        Mockito.reset(dealGroupQueryService);
    }

    /**
     * Helper method to setup valid service response
     */
    private void setupValidServiceResponse(Long dealId) throws Exception {
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        QueryDealGroupListResult resultData = new QueryDealGroupListResult();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DefaultSelectDTO defaultSelectDTO = new DefaultSelectDTO();
        defaultSelectDTO.setDealId(dealId);
        dealGroupDTO.setDefaultSelectDTO(defaultSelectDTO);
        resultData.setList(Collections.singletonList(dealGroupDTO));
        response.setData(resultData);
        when(dealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(response);
    }

    /**
     * Test case when request contains valid skuId
     */
    @Test
    public void testGetDefaultSkuIdWithValidSkuId() throws Throwable {
        // arrange
        req.setDealgroupid(123);
        req.setSkuId("456");
        // act
        String skuId = skuWrapper.getDefaultSkuId(req, envCtx);
        // assert
        assertEquals("456", skuId);
        verifyNoInteractions(dealGroupQueryService);
    }

    /**
     * Test case when request contains invalid (non-numeric) skuId
     */
    @Test
    public void testGetDefaultSkuIdWithInvalidSkuId() throws Throwable {
        // arrange
        req.setDealgroupid(123);
        req.setSkuId("invalid");
        setupValidServiceResponse(789L);
        // act
        String skuId = skuWrapper.getDefaultSkuId(req, envCtx);
        // assert
        assertEquals("789", skuId);
        verify(dealGroupQueryService).queryByDealGroupIds(any(QueryByDealGroupIdRequest.class));
    }

    /**
     * Test case when service returns null response
     */
    @Test
    public void testGetDefaultSkuIdWithNullServiceResponse() throws Throwable {
        // arrange
        req.setDealgroupid(123);
        when(dealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(null);
        // act
        String skuId = skuWrapper.getDefaultSkuId(req, envCtx);
        // assert
        assertEquals(StringUtils.EMPTY, skuId);
        verify(dealGroupQueryService).queryByDealGroupIds(any(QueryByDealGroupIdRequest.class));
    }

    /**
     * Test case when service returns empty list in response
     */
    @Test
    public void testGetDefaultSkuIdWithEmptyListResponse() throws Throwable {
        // arrange
        req.setDealgroupid(123);
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        QueryDealGroupListResult resultData = new QueryDealGroupListResult();
        resultData.setList(Collections.emptyList());
        response.setData(resultData);
        when(dealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(response);
        // act
        String skuId = skuWrapper.getDefaultSkuId(req, envCtx);
        // assert
        assertEquals(StringUtils.EMPTY, skuId);
        verify(dealGroupQueryService).queryByDealGroupIds(any(QueryByDealGroupIdRequest.class));
    }

    /**
     * Test case when service returns valid response with dealId
     */
    @Test
    public void testGetDefaultSkuIdWithValidDealId() throws Throwable {
        // arrange
        req.setDealgroupid(123);
        setupValidServiceResponse(789L);
        // act
        String skuId = skuWrapper.getDefaultSkuId(req, envCtx);
        // assert
        assertEquals("789", skuId);
        verify(dealGroupQueryService).queryByDealGroupIds(any(QueryByDealGroupIdRequest.class));
    }

    /**
     * Test case when service response is missing defaultSelectDTO
     */
    @Test
    public void testGetDefaultSkuIdWithMissingDefaultSelectDTO() throws Throwable {
        // arrange
        req.setDealgroupid(123);
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        QueryDealGroupListResult resultData = new QueryDealGroupListResult();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        resultData.setList(Collections.singletonList(dealGroupDTO));
        response.setData(resultData);
        when(dealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(response);
        // act
        String skuId = skuWrapper.getDefaultSkuId(req, envCtx);
        // assert
        assertEquals(StringUtils.EMPTY, skuId);
        verify(dealGroupQueryService).queryByDealGroupIds(any(QueryByDealGroupIdRequest.class));
    }

    /**
     * Test case when service throws exception
     */
    @Test
    public void testGetDefaultSkuIdWhenServiceThrowsException() throws Throwable {
        // arrange
        req.setDealgroupid(123);
        when(dealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenThrow(new RuntimeException("Service error"));
        // act
        String skuId = skuWrapper.getDefaultSkuId(req, envCtx);
        // assert
        assertEquals(StringUtils.EMPTY, skuId);
        verify(dealGroupQueryService).queryByDealGroupIds(any(QueryByDealGroupIdRequest.class));
    }

    /**
     * Test case for MT platform (envCtx.isMt() = true)
     */
    @Test
    public void testGetDefaultSkuIdForMTPlatform() throws Throwable {
        // arrange
        req.setDealgroupid(123);
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        setupValidServiceResponse(789L);
        // act
        String skuId = skuWrapper.getDefaultSkuId(req, envCtx);
        // assert
        assertEquals("789", skuId);
        verify(dealGroupQueryService).queryByDealGroupIds(argThat(request -> request.getIdType().equals(IdTypeEnum.MT.getCode())));
    }

    /**
     * Test case for DP platform (envCtx.isMt() = false)
     */
    @Test
    public void testGetDefaultSkuIdForDPPlatform() throws Throwable {
        // arrange
        req.setDealgroupid(123);
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        setupValidServiceResponse(789L);
        // act
        String skuId = skuWrapper.getDefaultSkuId(req, envCtx);
        // assert
        assertEquals("789", skuId);
        verify(dealGroupQueryService).queryByDealGroupIds(argThat(request -> request.getIdType().equals(IdTypeEnum.DP.getCode())));
    }
}
