package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.HaimaWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CommonAttrVO;
import com.dianping.mobile.mapi.dztgdetail.entity.CleaningProductLionConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.LifeClearHaiMaConfigDto;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试LifeClearHighlightsProcessor的prepare和process方法
 */
public class LifeClearHighlightsProcessorTest {

    @InjectMocks
    private LifeClearHighlightsProcessor lifeClearHighlightsProcessor;

    @Mock
    private HaimaWrapper haimaWrapper;

    @Mock
    private Lion lion;


    private LifeClearHighlightsProcessor processor;
    private DealGroupDTO dealGroupDTO;
    private CleaningProductLionConfig config;

    @Before
    public void setUp() {
        processor = new LifeClearHighlightsProcessor();
        dealGroupDTO = new DealGroupDTO();
        config = new CleaningProductLionConfig();
        MockitoAnnotations.initMocks(this);
    }


    @Test
    public void convertDtoToVONull() throws Throwable {
        List<LifeClearHaiMaConfigDto> dtoList = null;
        List<CommonAttrVO> result = lifeClearHighlightsProcessor.convertDtoToVO(null);
        assertNull(result);
    }


    @Test
    public void convertDtoToVONotNull() throws Throwable {
        // 创建示例 dto 对象
        LifeClearHaiMaConfigDto dto1 = new LifeClearHaiMaConfigDto();
        dto1.setTitle("服务人数");
        dto1.setContent("1人");
        LifeClearHaiMaConfigDto dto2 = new LifeClearHaiMaConfigDto();
        dto2.setTitle("服务内容");
        dto2.setContent("清洁、陪伴");
        // 将 dto 对象添加到 List 中
        List<LifeClearHaiMaConfigDto> dtoList = new ArrayList<>();
        dtoList.add(dto1);
        dtoList.add(dto2);
        List<CommonAttrVO> result = lifeClearHighlightsProcessor.convertDtoToVO(dtoList);
        assertNotNull(result);
    }


    @Test
    public void isSelfConfigFalse() throws Throwable {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        CleaningProductLionConfig config = new CleaningProductLionConfig();
        Boolean result = lifeClearHighlightsProcessor.isSelf(dealGroupDTO, config);
        assertNotNull(result);
    }

    @Test
    public void isSelfDtoFalse() throws Throwable {

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("testConfigId");
        List<String> values = new ArrayList<>();
        values.add("否");
        attrDTO.setValue(values);
        attrs.add(attrDTO);
        dealGroupDTO.setAttrs(attrs);

        // 创建 CleaningProductLionConfig 对象
        CleaningProductLionConfig config = new CleaningProductLionConfig();
        config.isEnable();
        config.setSceneKey("exampleScene");
        config.setConfigId("config123");
        config.setContentId("content456");
        config.setExcludeIds(Arrays.asList("id1", "id2", "id3"));
        config.setExcludeNames(Arrays.asList("name1", "name2", "name3"));
        config.setFirstTabName("firstTab");
        Boolean result = lifeClearHighlightsProcessor.isSelf(dealGroupDTO, config);
        assertNotNull(result);
    }

    @Test
    public void isSelfTrue() throws Throwable {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("testConfigId");
        List<String> values = new ArrayList<>();
        values.add("否");
        attrDTO.setValue(values);
        attrs.add(attrDTO);
        dealGroupDTO.setAttrs(attrs);

        CleaningProductLionConfig config = new CleaningProductLionConfig();
        config.setConfigId("testConfigId");
        Boolean result = lifeClearHighlightsProcessor.isSelf(dealGroupDTO, config);
        assertNotNull(result);
    }


    /**
     * 测试属性匹配，属性值不包含"否"
     */
    @Test
    public void testIsAttributeMatching_AttrValueNotContainsFalse() {
        AttrDTO attrDTO = Mockito.mock(AttrDTO.class);
        Mockito.when(attrDTO.getName()).thenReturn("configId");
        Mockito.when(attrDTO.getValue()).thenReturn(Collections.singletonList("是"));
        dealGroupDTO.setAttrs(Collections.singletonList(attrDTO));
        config.setConfigId("configId");

        boolean result = processor.isAttributeMatching(dealGroupDTO, config, LifeClearHighlightsProcessor.DISABLE_SELF_OWN_PRODUCT);

        assertTrue(result);
    }

    /**
     * 测试属性匹配，属性值包含"否"
     */
    @Test
    public void testIsAttributeMatching_AttrValueContainsFalse() {
        AttrDTO attrDTO = Mockito.mock(AttrDTO.class);
        Mockito.when(attrDTO.getName()).thenReturn("configId");
        Mockito.when(attrDTO.getValue()).thenReturn(Arrays.asList("是", LifeClearHighlightsProcessor.DISABLE_SELF_OWN_PRODUCT));
        dealGroupDTO.setAttrs(Collections.singletonList(attrDTO));
        config.setConfigId("configId");

        boolean result = processor.isAttributeMatching(dealGroupDTO, config, LifeClearHighlightsProcessor.DISABLE_SELF_OWN_PRODUCT);

        assertFalse(result);
    }

    /**
     * 测试属性匹配，属性列表为空
     */
    @Test
    public void testIsAttributeMatching_AttrListEmpty() {
        dealGroupDTO.setAttrs(Collections.emptyList());
        config.setConfigId("configId");

        boolean result = processor.isAttributeMatching(dealGroupDTO, config, LifeClearHighlightsProcessor.DISABLE_SELF_OWN_PRODUCT);

        assertFalse(result);
    }

    /**
     * 测试属性匹配，属性值为空
     */
    @Test
    public void testIsAttributeMatching_AttrValueEmpty() {
        AttrDTO attrDTO = Mockito.mock(AttrDTO.class);
        Mockito.when(attrDTO.getName()).thenReturn("configId");
        Mockito.when(attrDTO.getValue()).thenReturn(Collections.emptyList());
        dealGroupDTO.setAttrs(Collections.singletonList(attrDTO));
        config.setConfigId("configId");

        boolean result = processor.isAttributeMatching(dealGroupDTO, config, LifeClearHighlightsProcessor.DISABLE_SELF_OWN_PRODUCT);

        assertFalse(result);
    }

    /**
     * 测试属性匹配，未找到匹配的属性名
     */
    @Test
    public void testIsAttributeMatching_AttrNameNotMatch() {
        AttrDTO attrDTO = Mockito.mock(AttrDTO.class);
        Mockito.when(attrDTO.getName()).thenReturn("otherConfigId");
        Mockito.when(attrDTO.getValue()).thenReturn(Collections.singletonList("是"));
        dealGroupDTO.setAttrs(Collections.singletonList(attrDTO));
        config.setConfigId("configId");

        boolean result = processor.isAttributeMatching(dealGroupDTO, config, LifeClearHighlightsProcessor.DISABLE_SELF_OWN_PRODUCT);

        assertFalse(result);
    }


}
