package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.ActivityCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.ActivityModuleDTO;
import com.dianping.tpfun.product.api.sku.pintuan.dto.BestPinTag;
import com.sankuai.dealuser.price.display.api.PriceDisplayService;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PriceRequest;
import com.sankuai.dealuser.price.display.api.model.PriceResponse;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class UnifiedActivityFacadeBuildUnifiedActivityModuleTest {

    @InjectMocks
    private UnifiedActivityFacade unifiedActivityFacade;

    @Mock
    private PriceDisplayService priceDisplayService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private ActivityModuleDTO invokeBuildUnifiedActivityModule(EnvCtx envCtx, ActivityCtx activityCtx, BestPinTag pinProductBrief) throws Exception {
        Method method = UnifiedActivityFacade.class.getDeclaredMethod("buildUnifiedActivityModule", EnvCtx.class, ActivityCtx.class, BestPinTag.class);
        method.setAccessible(true);
        return (ActivityModuleDTO) method.invoke(unifiedActivityFacade, envCtx, activityCtx, pinProductBrief);
    }

    /**
     * Test case for invalid BestPinTag
     */
    @Test
    public void testBuildUnifiedActivityModuleInvalidBestPinTag() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        ActivityCtx activityCtx = new ActivityCtx(envCtx);
        BestPinTag pinProductBrief = new BestPinTag();
        // act
        ActivityModuleDTO result = invokeBuildUnifiedActivityModule(envCtx, activityCtx, pinProductBrief);
        // assert
        assertNull(result);
    }

    /**
     * Test case for valid BestPinTag but no shop ID
     */
    @Test
    public void testBuildUnifiedActivityModuleValidBestPinTagNoShopId() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        ActivityCtx activityCtx = new ActivityCtx(envCtx);
        BestPinTag pinProductBrief = new BestPinTag();
        pinProductBrief.setId(1);
        pinProductBrief.setPinPersonNum(2);
        pinProductBrief.setPrice(new BigDecimal("100.00"));
        // act
        ActivityModuleDTO result = invokeBuildUnifiedActivityModule(envCtx, activityCtx, pinProductBrief);
        // assert
        assertNotNull(result);
        assertEquals("活动", result.getTitle());
        assertEquals("[{\"backgroundcolor\":\"#FFFFFF\",\"strikethrough\":false,\"text\":\"2人拼团 \",\"textcolor\":\"#111111\",\"textsize\":13,\"textstyle\":\"Default\",\"underline\":false},{\"backgroundcolor\":\"#FFFFFF\",\"strikethrough\":false,\"text\":\"￥100\",\"textcolor\":\"#FF6633\",\"textsize\":13,\"textstyle\":\"Default\",\"underline\":false}]", result.getDesc());
    }

    /**
     * Test case for valid BestPinTag with shop ID and price check fails
     */
    @Test
    public void testBuildUnifiedActivityModuleValidBestPinTagWithShopIdPriceCheckFails() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        ActivityCtx activityCtx = new ActivityCtx(envCtx);
        activityCtx.setMtShopIdLong(12345L);
        BestPinTag pinProductBrief = new BestPinTag();
        pinProductBrief.setId(1);
        pinProductBrief.setPinPersonNum(2);
        pinProductBrief.setPrice(new BigDecimal("100.00"));
        PriceResponse<PriceDisplayDTO> priceResponse = new PriceResponse<>(200, "Success", new PriceDisplayDTO());
        // Ensure price is set
        priceResponse.getData().setPrice(new BigDecimal("90.00"));
        when(priceDisplayService.queryPrice(any(PriceRequest.class))).thenReturn(priceResponse);
        // act
        ActivityModuleDTO result = invokeBuildUnifiedActivityModule(envCtx, activityCtx, pinProductBrief);
        // assert
        assertNull(result);
    }

    /**
     * Test case for exception during price query
     */
    @Test(expected = Exception.class)
    public void testBuildUnifiedActivityModuleExceptionDuringPriceQuery() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        ActivityCtx activityCtx = new ActivityCtx(envCtx);
        activityCtx.setMtShopIdLong(12345L);
        BestPinTag pinProductBrief = new BestPinTag();
        pinProductBrief.setId(1);
        pinProductBrief.setPinPersonNum(2);
        pinProductBrief.setPrice(new BigDecimal("100.00"));
        when(priceDisplayService.queryPrice(any(PriceRequest.class))).thenThrow(new Exception("Price query failed"));
        // act
        invokeBuildUnifiedActivityModule(envCtx, activityCtx, pinProductBrief);
        // assert
        // Exception is expected
    }
}
