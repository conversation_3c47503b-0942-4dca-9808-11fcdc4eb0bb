package com.dianping.mobile.mapi.dztgdetail.action;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.datatypes.MobileHeader;
import com.dianping.mobile.framework.datatypes.token.UserStatusResult;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.util.useragent.UserAgentUtils;
import com.sankuai.pearl.framework.context.MobileContextAdaptor;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AbsActionInitEnvCtxV2Test {

    @Mock
    private IMobileContext appCtx;

    @Mock
    private HttpServletRequest request;

    @Mock
    private MobileHeader mobileHeader;

    private TestAbsAction absAction = new TestAbsAction();

    @Before
    public void setUp() {
        when(appCtx.getRequest()).thenReturn(request);
        when(appCtx.getHeader()).thenReturn(mobileHeader);
        when(mobileHeader.getUuid()).thenReturn("test-uuid");
        when(mobileHeader.getDpid()).thenReturn("test-dpid");
        when(appCtx.getAppDeviceId()).thenReturn("test-device");
        when(appCtx.getVersion()).thenReturn("default-version");
        // 默认设置为非美团和非点评客户端
        when(appCtx.isMeituanClient()).thenReturn(false);
        when(appCtx.isDianpingClient()).thenReturn(false);
        when(appCtx.getAppId()).thenReturn(0);
    }

    private class TestAbsAction extends AbsAction<IMobileRequest> {

        @Override
        protected IMobileResponse validate(IMobileRequest request, IMobileContext context) {
            return null;
        }

        @Override
        protected IMobileResponse execute(IMobileRequest request, IMobileContext context) {
            return null;
        }

        @Override
        protected List<ClientInfoRule> getRule() {
            return new ArrayList<>();
        }
    }

    /**
     * Test case for null appCtx
     */
    @Test
    public void testInitEnvCtxV2_NullAppCtx() throws Throwable {
        // act
        EnvCtx result = absAction.initEnvCtxV2(null);
        // assert
        assertNotNull(result);
        assertEquals(0, result.getDpUserId());
        assertEquals(0, result.getMtUserId());
    }

    /**
     * Test case for Meituan app client
     */
    @Test
    public void testInitEnvCtxV2_MeituanApp() throws Throwable {
        // arrange
        when(appCtx.isMeituanClient()).thenReturn(true);
        when(appCtx.isIOS()).thenReturn(true);
        when(appCtx.getUserId()).thenReturn(123L);
        when(appCtx.getVersion()).thenReturn("10.0.0");
        // act
        EnvCtx result = absAction.initEnvCtxV2(appCtx);
        // assert
        assertEquals(DztgClientTypeEnum.MEITUAN_APP, result.getDztgClientTypeEnum());
        assertEquals(123L, result.getMtUserId());
        assertEquals("test-uuid", result.getUuid());
        assertEquals("10.0.0", result.getVersion());
    }

    /**
     * Test case for Dianping app client
     */
    @Test
    public void testInitEnvCtxV2_DianpingApp() throws Throwable {
        // arrange
        when(appCtx.isDianpingClient()).thenReturn(true);
        when(appCtx.isMeituanClient()).thenReturn(false);
        when(appCtx.getUserId()).thenReturn(456L);
        when(appCtx.getVersion()).thenReturn("9.0.0");
        // act
        EnvCtx result = absAction.initEnvCtxV2(appCtx);
        // assert
        assertEquals(DztgClientTypeEnum.DIANPING_APP, result.getDztgClientTypeEnum());
        assertEquals(456L, result.getDpUserId());
        assertEquals("test-uuid", result.getUuid());
        assertEquals("9.0.0", result.getVersion());
    }

    /**
     * Test case for internal client with all headers
     */
    @Test
    public void testInitEnvCtxV2_InternalWithAllHeaders() throws Throwable {
        // arrange
        MobileContextAdaptor mobileContext = mock(MobileContextAdaptor.class);
        when(mobileContext.getRequest()).thenReturn(request);
        when(mobileContext.getHeader()).thenReturn(mobileHeader);
        when(mobileContext.getVersion()).thenReturn("3.0.0");
        when(mobileContext.getHeader("pragma-unionid")).thenReturn("test-union");
        when(mobileContext.isDianpingClient()).thenReturn(true);
        when(mobileContext.isMeituanClient()).thenReturn(false);
        // act
        EnvCtx result = absAction.initEnvCtxV2(mobileContext);
        // assert
        assertEquals("3.0.0", result.getVersion());
        assertEquals("test-union", result.getUnionId());
        assertEquals("test-uuid", result.getUuid());
    }
}
