package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.lang.reflect.Method;
import org.apache.commons.lang3.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

/**
 * Test class for the method private String getOriginPic(String origin)
 */
public class SinaiProcessorGetOriginPicTest {

    private SinaiProcessor sinaiProcessor;

    @Before
    public void setUp() {
        sinaiProcessor = new SinaiProcessor();
    }

    /**
     * Helper method to invoke the private method getOriginPic using reflection.
     */
    private String invokeGetOriginPic(String origin) throws Exception {
        Method method = SinaiProcessor.class.getDeclaredMethod("getOriginPic", String.class);
        method.setAccessible(true);
        return (String) method.invoke(sinaiProcessor, origin);
    }

    /**
     * Test case for when the input is null or empty.
     * Expected output: ""
     */
    @Test
    public void testGetOriginPic_InputIsNull() throws Throwable {
        // arrange
        String origin = null;
        // act
        String result = invokeGetOriginPic(origin);
        // assert
        assertEquals("", result);
    }

    /**
     * Test case for when the input is an empty string.
     * Expected output: ""
     */
    @Test
    public void testGetOriginPic_InputIsEmpty() throws Throwable {
        // arrange
        String origin = "";
        // act
        String result = invokeGetOriginPic(origin);
        // assert
        assertEquals("", result);
    }

    /**
     * Test case for when the input does not contain "%40".
     * Expected output: The original input string.
     */
    @Test
    public void testGetOriginPic_InputDoesNotContainPercent40() throws Throwable {
        // arrange
        String origin = "example.com/image.jpg";
        // act
        String result = invokeGetOriginPic(origin);
        // assert
        assertEquals(origin, result);
    }

    /**
     * Test case for when the input contains "%40" and the split result has less than 2 parts.
     * Expected output: The original input string.
     */
    @Test
    public void testGetOriginPic_InputContainsPercent40ButLessThan2Parts() throws Throwable {
        // arrange
        String origin = "example.com%40";
        // act
        String result = invokeGetOriginPic(origin);
        // assert
        assertEquals(origin, result);
    }

    /**
     * Test case for when the input contains "%40" and the split result has 2 or more parts.
     * Expected output: The first part of the split result concatenated with "%40300w_0e_1l".
     */
    @Test
    public void testGetOriginPic_InputContainsPercent40AndMoreThan2Parts() throws Throwable {
        // arrange
        String origin = "example.com%40image.jpg";
        String expected = "example.com%40300w_0e_1l";
        // act
        String result = invokeGetOriginPic(origin);
        // assert
        assertEquals(expected, result);
    }
}
