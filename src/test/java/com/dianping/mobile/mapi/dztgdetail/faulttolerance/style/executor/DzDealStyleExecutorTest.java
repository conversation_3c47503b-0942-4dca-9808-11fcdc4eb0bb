package com.dianping.mobile.mapi.dztgdetail.faulttolerance.style.executor;

import com.dianping.deal.style.DealDetailStyleFlashService;
import com.dianping.mobile.mapi.dztgdetail.biz.service.cellarservice.DealStyleCacheBizService;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealFlashReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealStyleBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DztgModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.util.Assert;

import java.util.List;

/**
 * @desc 团详样式处理器
 */
@RunWith(MockitoJUnitRunner.class)
public class DzDealStyleExecutorTest {
    
    @InjectMocks
    private DzDealStyleExecutor dealStyleExecutor;

    @InjectMocks
    private DealStyleCacheBizService cacheBizService;

    @Mock
    private DealDetailStyleFlashService dealDetailStyleFlashService;

    /**
     * 过滤无需缓存的字段
     * @param 
     * @return
     */
    @Test
    public void cacheValueFilterTest(){
        DealStyleBO bo = new DealStyleBO();
        ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
        List<DztgModuleAbConfig> moduleAbConfigs = Lists.newArrayList();
        moduleConfigsModule.setModuleAbConfigs(moduleAbConfigs);
        bo.setModuleConfigsModule(moduleConfigsModule);
        DealStyleBO result = dealStyleExecutor.cacheValueFilter(bo);
        cacheBizService.saveOrUpdate(new DealFlashReq(),  result, "", "");
        Assert.isNull(result.getModuleConfigsModule().getModuleAbConfigs());
    }
}
