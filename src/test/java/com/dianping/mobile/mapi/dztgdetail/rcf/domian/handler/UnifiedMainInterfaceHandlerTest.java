package com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler;

import static org.junit.Assert.*;
import com.dianping.deal.bff.cache.enums.RcfDealBffInterfaceEnum;
import com.dianping.mobile.mapi.dztgdetail.rcf.api.req.DealNativeSnapshotReq;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.dto.DealBffResponseDTO;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedMainInterfaceHandlerTest {

    private UnifiedMainInterfaceHandler handler = new UnifiedMainInterfaceHandler();

    /**
     * 测试 bffResponse 为 null 的情况
     */
    @Test
    public void testCanProcessBffResponseIsNull() {
        // arrange
        DealNativeSnapshotReq request = new DealNativeSnapshotReq();
        DealBffResponseDTO bffResponse = null;
        // act
        boolean result = handler.canProcess(request, bffResponse);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 bffResponse 不为 null，但不包含 RcfDealBffInterfaceEnum.unifiedmaininterface 对应的响应的情况
     */
    @Test
    public void testCanProcessBffResponseNotContainsUnifiedMainInterface() {
        // arrange
        DealNativeSnapshotReq request = new DealNativeSnapshotReq();
        Map<RcfDealBffInterfaceEnum, Object> bffResponseMap = new HashMap<>();
        bffResponseMap.put(RcfDealBffInterfaceEnum.dzdealbase, new Object());
        DealBffResponseDTO bffResponse = new DealBffResponseDTO(bffResponseMap);
        // act
        boolean result = handler.canProcess(request, bffResponse);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 bffResponse 不为 null，且包含 RcfDealBffInterfaceEnum.unifiedmaininterface 对应的响应的情况
     */
    @Test
    public void testCanProcessBffResponseContainsUnifiedMainInterface() {
        // arrange
        DealNativeSnapshotReq request = new DealNativeSnapshotReq();
        Map<RcfDealBffInterfaceEnum, Object> bffResponseMap = new HashMap<>();
        bffResponseMap.put(RcfDealBffInterfaceEnum.unifiedmaininterface, new Object());
        DealBffResponseDTO bffResponse = new DealBffResponseDTO(bffResponseMap);
        // act
        boolean result = handler.canProcess(request, bffResponse);
        // assert
        assertTrue(result);
    }
}
