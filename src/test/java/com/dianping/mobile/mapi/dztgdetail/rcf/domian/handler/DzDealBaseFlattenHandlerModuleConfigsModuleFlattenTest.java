package com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.junit.*;

public class DzDealBaseFlattenHandlerModuleConfigsModuleFlattenTest {

    private DzDealBaseHandler handler;

    @Before
    public void setUp() {
        handler = new DzDealBaseHandler();
    }

    @Test(expected = NullPointerException.class)
    public void testModuleConfigsModuleFlattenDealBaseIsNull() throws Throwable {
        JSONObject dealBase = null;
        handler.moduleConfigsModuleFlatten(dealBase);
    }

    @Test
    public void testModuleConfigsModuleFlattenModuleConfigsModuleIsNull() throws Throwable {
        JSONObject dealBase = new JSONObject();
        handler.moduleConfigsModuleFlatten(dealBase);
        Assert.assertNull(dealBase.get("rcfModuleConfigs"));
    }

    @Test
    public void testModuleConfigsModuleFlattenModuleConfigsIsNull() throws Throwable {
        JSONObject dealBase = new JSONObject();
        JSONObject moduleConfigsModule = new JSONObject();
        dealBase.put("moduleConfigsModule", moduleConfigsModule);
        handler.moduleConfigsModuleFlatten(dealBase);
        Assert.assertNull(dealBase.get("rcfModuleConfigs"));
    }

    @Test
    public void testModuleConfigsModuleFlattenKeyNotEquals() throws Throwable {
        JSONObject dealBase = new JSONObject();
        JSONObject moduleConfigsModule = new JSONObject();
        JSONArray moduleConfigs = new JSONArray();
        JSONObject moduleConfig = new JSONObject();
        moduleConfig.put("key", "otherKey");
        moduleConfig.put("value", "otherValue");
        moduleConfigs.add(moduleConfig);
        moduleConfigsModule.put("moduleConfigs", moduleConfigs);
        dealBase.put("moduleConfigsModule", moduleConfigsModule);
        handler.moduleConfigsModuleFlatten(dealBase);
        JSONObject rcfModuleConfigs = (JSONObject) dealBase.get("rcfModuleConfigs");
        Assert.assertNotNull(rcfModuleConfigs);
        Assert.assertTrue(rcfModuleConfigs.isEmpty());
    }

    @Test
    public void testModuleConfigsModuleFlattenKeyEquals() throws Throwable {
        JSONObject dealBase = new JSONObject();
        JSONObject moduleConfigsModule = new JSONObject();
        JSONArray moduleConfigs = new JSONArray();
        JSONObject moduleConfig = new JSONObject();
        moduleConfig.put("key", "dealdetail_gc_packagedetail");
        moduleConfig.put("value", "uniform-structure-table-b");
        moduleConfigs.add(moduleConfig);
        moduleConfigsModule.put("moduleConfigs", moduleConfigs);
        dealBase.put("moduleConfigsModule", moduleConfigsModule);
        handler.moduleConfigsModuleFlatten(dealBase);
        JSONObject rcfModuleConfigs = (JSONObject) dealBase.get("rcfModuleConfigs");
        Assert.assertNotNull(rcfModuleConfigs);
        Assert.assertEquals("uniform-structure-table-b", rcfModuleConfigs.get("dealdetail_gc_packagedetail"));
    }
}
