package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.attribute.dto.AttributeDTO;
// Import DealAttrKeys
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.sankuai.general.product.query.center.client.dto.rule.use.AvailableDurationDateDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DateRangeDTO;
import java.util.Arrays;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealAttrHelper_AllCanUseTest {

    /**
     * 测试 allCanUse 方法，当 attrs 为空时，应返回 false
     */
    @Test
    public void testAllCanUseWhenAttrsIsNull() throws Throwable {
        // arrange
        // act
        boolean result = DealAttrHelper.allCanUse(null);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 allCanUse 方法，当 attrs 不为空，但是 attrs 中没有 DealAttrKeys.ALL_CAN_USE 对应的属性时，应返回 false
     */
    @Test
    public void testAllCanUseWhenAttrsHasNoAllCanUse() throws Throwable {
        // arrange
        AttributeDTO attr = new AttributeDTO();
        attr.setName("other");
        attr.setValue(Arrays.asList("no"));
        // act
        boolean result = DealAttrHelper.allCanUse(Collections.singletonList(attr));
        // assert
        assertFalse(result);
    }

    /**
     * 测试 allCanUse 方法，当 attrs 不为空，attrs 中有 DealAttrKeys.ALL_CAN_USE 对应的属性，但是属性值为空时，应返回 false
     */
    @Test
    public void testAllCanUseWhenAllCanUseValueIsNull() throws Throwable {
        // arrange
        AttributeDTO attr = new AttributeDTO();
        // Corrected reference to DealAttrKeys
        attr.setName(DealAttrKeys.ALL_CAN_USE);
        attr.setValue(null);
        // act
        boolean result = DealAttrHelper.allCanUse(Collections.singletonList(attr));
        // assert
        assertFalse(result);
    }

    /**
     * 测试 allCanUse 方法，当 attrs 不为空，attrs 中有 DealAttrKeys.ALL_CAN_USE 对应的属性，属性值不为空，但是属性值不等于 "是"时，应返回 false
     */
    @Test
    public void testAllCanUseWhenAllCanUseValueIsNotYes() throws Throwable {
        // arrange
        AttributeDTO attr = new AttributeDTO();
        // Corrected reference to DealAttrKeys
        attr.setName(DealAttrKeys.ALL_CAN_USE);
        attr.setValue(Arrays.asList("no"));
        // act
        boolean result = DealAttrHelper.allCanUse(Collections.singletonList(attr));
        // assert
        assertFalse(result);
    }

    /**
     * 测试 allCanUse 方法，当 attrs 不为空，attrs 中有 DealAttrKeys.ALL_CAN_USE 对应的属性，属性值不为空，属性值等于 "是"时，应返回 true
     */
    @Test
    public void testAllCanUseWhenAllCanUseValueIsYes() throws Throwable {
        // arrange
        AttributeDTO attr = new AttributeDTO();
        // Corrected reference to DealAttrKeys
        attr.setName(DealAttrKeys.ALL_CAN_USE);
        attr.setValue(Arrays.asList("是"));
        // act
        boolean result = DealAttrHelper.allCanUse(Collections.singletonList(attr));
        // assert
        assertTrue(result);
    }

    @Test
    public void testValidSpecifiedDurationDateListNullList() throws Throwable {
        assertFalse(DealAttrHelper.validSpecifiedDurationDateList(null));
    }

    @Test
    public void testValidSpecifiedDurationDateListNullElement() throws Throwable {
        assertFalse(DealAttrHelper.validSpecifiedDurationDateList(Collections.singletonList(null)));
    }

    @Test
    public void testValidSpecifiedDurationDateListEmptyAvailableDateRangeDTOS() throws Throwable {
        AvailableDurationDateDTO availableDurationDateDTO = mock(AvailableDurationDateDTO.class);
        when(availableDurationDateDTO.getAvailableDateRangeDTOS()).thenReturn(Collections.emptyList());
        assertFalse(DealAttrHelper.validSpecifiedDurationDateList(Collections.singletonList(availableDurationDateDTO)));
    }

    @Test
    public void testValidSpecifiedDurationDateListInvalidAvailableDateRangeDTOS() throws Throwable {
        AvailableDurationDateDTO availableDurationDateDTO = mock(AvailableDurationDateDTO.class);
        DateRangeDTO dateRangeDTO = mock(DateRangeDTO.class);
        when(availableDurationDateDTO.getAvailableDateRangeDTOS()).thenReturn(Collections.singletonList(dateRangeDTO));
        // Corrected date format to "yyyy-MM-dd"
        when(dateRangeDTO.getFrom()).thenReturn("2023-09-30");
        // Corrected date format to "yyyy-MM-dd"
        when(dateRangeDTO.getTo()).thenReturn("2023-09-30");
        assertFalse(DealAttrHelper.validSpecifiedDurationDateList(Collections.singletonList(availableDurationDateDTO)));
    }
}
