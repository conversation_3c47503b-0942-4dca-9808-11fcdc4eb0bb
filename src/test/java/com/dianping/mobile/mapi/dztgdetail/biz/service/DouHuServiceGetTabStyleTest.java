package com.dianping.mobile.mapi.dztgdetail.biz.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.ExpResultConfig;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DouHuServiceGetTabStyleTest {

    @InjectMocks
    @Spy
    private DouHuService douHuService;

    @Mock
    private ExpResultConfig expResultConfig;

    @Mock
    private DealCtx ctx;

    /**
     * Test when expResultConfig is null
     */
    @Test
    public void testGetTabStyleExpResultConfigNull() throws Throwable {
        // arrange
        when(ctx.getCategoryId()).thenReturn(123);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        // act
        String result = douHuService.getTabStyle(null, ctx, moduleAbConfig);
        // assert
        assertNull(result);
        verify(douHuService).getTabStyle(null, 123, moduleAbConfig);
    }

    /**
     * Test when enableStyle is false
     */
    @Test
    public void testGetTabStyleEnableStyleFalse() throws Throwable {
        // arrange
        when(expResultConfig.isEnableStyle()).thenReturn(false);
        when(ctx.getCategoryId()).thenReturn(123);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        // act
        String result = douHuService.getTabStyle(expResultConfig, ctx, moduleAbConfig);
        // assert
        assertNull(result);
        verify(douHuService).getTabStyle(expResultConfig, 123, moduleAbConfig);
    }

    /**
     * Test when allPassStyle is not blank
     */
    @Test
    public void testGetTabStyleAllPassStyleExists() throws Throwable {
        // arrange
        when(expResultConfig.isEnableStyle()).thenReturn(true);
        when(expResultConfig.getAllPassStyle()).thenReturn("all_pass_style");
        when(ctx.getCategoryId()).thenReturn(123);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        // act
        String result = douHuService.getTabStyle(expResultConfig, ctx, moduleAbConfig);
        // assert
        assertEquals("all_pass_style", result);
        verify(douHuService).getTabStyle(expResultConfig, 123, moduleAbConfig);
    }

    /**
     * Test when moduleAbConfig is valid and key2Style exists
     */
    @Test
    public void testGetTabStyleModuleAbConfigValid() throws Throwable {
        // arrange
        Map<String, String> key2Style = new HashMap<>();
        key2Style.put("exp1_1", "style_a");
        when(expResultConfig.isEnableStyle()).thenReturn(true);
        when(expResultConfig.getAllPassStyle()).thenReturn("");
        when(expResultConfig.getKey2Style()).thenReturn(key2Style);
        when(ctx.getCategoryId()).thenReturn(123);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        List<AbConfig> configs = new ArrayList<>();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpId("exp1");
        abConfig.setExpResult("1");
        configs.add(abConfig);
        moduleAbConfig.setConfigs(configs);
        // act
        String result = douHuService.getTabStyle(expResultConfig, ctx, moduleAbConfig);
        // assert
        assertEquals("style_a", result);
        verify(douHuService).getTabStyle(expResultConfig, 123, moduleAbConfig);
    }

    /**
     * Test when key2Style exists with matching categoryId
     */
    @Test
    public void testGetTabStyleCategoryIdBased() throws Throwable {
        // arrange
        int categoryId = 123;
        Map<String, String> key2Style = new HashMap<>();
        key2Style.put(String.valueOf(categoryId), "category_style");
        when(ctx.getCategoryId()).thenReturn(categoryId);
        // Create ModuleAbConfig without valid configs
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        doReturn("category_style").when(douHuService).getTabStyle(expResultConfig, categoryId, moduleAbConfig);
        // act
        String result = douHuService.getTabStyle(expResultConfig, ctx, moduleAbConfig);
        // assert
        assertEquals("category_style", result);
        verify(douHuService).getTabStyle(expResultConfig, categoryId, moduleAbConfig);
    }

    /**
     * Test when no conditions match
     */
    @Test
    public void testGetTabStyleNoMatch() throws Throwable {
        // arrange
        when(expResultConfig.isEnableStyle()).thenReturn(true);
        when(expResultConfig.getAllPassStyle()).thenReturn("");
        when(expResultConfig.getKey2Style()).thenReturn(null);
        when(ctx.getCategoryId()).thenReturn(123);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        // act
        String result = douHuService.getTabStyle(expResultConfig, ctx, moduleAbConfig);
        // assert
        assertNull(result);
        verify(douHuService).getTabStyle(expResultConfig, 123, moduleAbConfig);
    }
}
