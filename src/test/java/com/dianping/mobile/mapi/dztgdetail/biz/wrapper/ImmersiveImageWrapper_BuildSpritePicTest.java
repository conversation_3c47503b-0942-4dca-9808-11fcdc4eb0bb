package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.mobile.mapi.dztgdetail.common.enums.ContentType;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImageUrlVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.SpritePicVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.nailstyle.NailStylePlcVO;
import com.dianping.mobile.mapi.dztgdetail.entity.ExhibitImageConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.HaiMaNailFilterConfig;
import com.sankuai.mpmctcontent.application.thrift.dto.content.DisplayItem;
import com.sankuai.mpmctcontent.application.thrift.dto.content.RatioInfoDTO;
import com.sankuai.mpmctcontent.application.thrift.dto.content.VideoSpriteDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.style.enums.MediaTypeEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

@RunWith(MockitoJUnitRunner.class)
public class ImmersiveImageWrapper_BuildSpritePicTest {

    @InjectMocks
    private ImmersiveImageWrapper immersiveImageWrapper;

    @Mock
    private HaimaWrapper haimaWrapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 DisplayItem 对象为 null 的情况
     */
    @Test
    public void testBuildSpritePicDisplayItemIsNull() {
        SpritePicVO result = immersiveImageWrapper.buildSpritePic(null);
        assertNull(result);
    }

    /**
     * 测试 DisplayItem 对象的 videoDuration 属性为 null 的情况
     */
    @Test
    public void testBuildSpritePicVideoDurationIsNull() {
        DisplayItem item = new DisplayItem();
        item.setVideoSprite(new VideoSpriteDTO());
        SpritePicVO result = immersiveImageWrapper.buildSpritePic(item);
        assertNull(result);
    }

    /**
     * 测试 DisplayItem 对象的 videoSprite 属性为 null 的情况
     */
    @Test
    public void testBuildSpritePicVideoSpriteIsNull() {
        DisplayItem item = new DisplayItem();
        item.setVideoDuration("100");
        SpritePicVO result = immersiveImageWrapper.buildSpritePic(item);
        assertNull(result);
    }

    /**
     * 测试 DisplayItem 对象及其 videoDuration 和 videoSprite 属性都不为 null 的情况
     */
    @Test
    public void testBuildSpritePicAllNotNull() {
        DisplayItem item = new DisplayItem();
        item.setVideoDuration("100");
        VideoSpriteDTO videoSprite = new VideoSpriteDTO();
        item.setVideoSprite(videoSprite);
        SpritePicVO result = immersiveImageWrapper.buildSpritePic(item);
        assertNotNull(result);
    }

    private ImageUrlVO invokeGetImmersiveImageUrlVO(DisplayItem item, ExhibitImageConfig config,
            List<String> themeNames, boolean isMt, List<HaiMaNailFilterConfig> nailFilterConfigs) throws Exception {
        Method method = ImmersiveImageWrapper.class.getDeclaredMethod("getImmersiveImageUrlVO", DisplayItem.class,
                ExhibitImageConfig.class, List.class, boolean.class, List.class);
        method.setAccessible(true);
        return (ImageUrlVO)method.invoke(immersiveImageWrapper, item, config, themeNames, isMt, nailFilterConfigs);
    }

    private NailStylePlcVO invokeGetNailStylePlcForImmersiveImg(List<String> themeNames, ExhibitImageConfig config,
            boolean isMt, List<HaiMaNailFilterConfig> nailFilterConfigs) throws Exception {
        Method method = ImmersiveImageWrapper.class.getDeclaredMethod("getNailStylePlcForImmersiveImg", List.class,
                ExhibitImageConfig.class, boolean.class, List.class);
        method.setAccessible(true);
        return (NailStylePlcVO)method.invoke(immersiveImageWrapper, themeNames, config, isMt, nailFilterConfigs);
    }

    @Test
    public void testGetImmersiveImageUrlVO_NullItem() throws Throwable {
        // arrange
        DisplayItem item = null;
        ExhibitImageConfig config = new ExhibitImageConfig();
        List<String> themeNames = new ArrayList<>();
        boolean isMt = true;
        List<HaiMaNailFilterConfig> nailFilterConfigs = new ArrayList<>();
        // act
        ImageUrlVO result = invokeGetImmersiveImageUrlVO(item, config, themeNames, isMt, nailFilterConfigs);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetImmersiveImageUrlVO_PictureType() throws Throwable {
        // arrange
        DisplayItem item = new DisplayItem();
        item.setPicUrl("http://example.com/pic.jpg");
        item.setSubPicUrl("http://example.com/subpic.jpg");
        item.setType(MediaTypeEnum.PIC.type());
        ExhibitImageConfig config = new ExhibitImageConfig();
        config.setDefaultImageBestScale("1:1");
        List<String> themeNames = new ArrayList<>();
        boolean isMt = true;
        List<HaiMaNailFilterConfig> nailFilterConfigs = new ArrayList<>();
        // act
        ImageUrlVO result = invokeGetImmersiveImageUrlVO(item, config, themeNames, isMt, nailFilterConfigs);
        // assert
        assertNotNull(result);
        assertEquals(ContentType.PIC.getType(), result.getType());
        assertEquals("http://example.com/pic.jpg", result.getUrl());
        assertEquals("http://example.com/subpic.jpg", result.getThumbPic());
        assertEquals("1:1", result.getImageBestScale());
    }

    @Test
    public void testGetImmersiveImageUrlVO_VideoType() throws Throwable {
        // arrange
        DisplayItem item = new DisplayItem();
        item.setPicUrl("http://example.com/pic.jpg");
        item.setSubPicUrl("http://example.com/subpic.jpg");
        item.setVideoUrl("http://example.com/video.mp4");
        item.setType(MediaTypeEnum.VIDEO.type());
        ExhibitImageConfig config = new ExhibitImageConfig();
        config.setDefaultImageBestScale("16:9");
        List<String> themeNames = new ArrayList<>();
        boolean isMt = true;
        List<HaiMaNailFilterConfig> nailFilterConfigs = new ArrayList<>();
        // act
        ImageUrlVO result = invokeGetImmersiveImageUrlVO(item, config, themeNames, isMt, nailFilterConfigs);
        // assert
        assertNotNull(result);
        assertEquals(ContentType.VIDEO.getType(), result.getType());
        assertEquals("http://example.com/video.mp4", result.getUrl());
        assertEquals("http://example.com/pic.jpg", result.getThumbPic());
        assertEquals("16:9", result.getImageBestScale());
    }

    @Test
    public void testGetImmersiveImageUrlVO_WithRatioInfo() throws Throwable {
        // arrange
        DisplayItem item = new DisplayItem();
        item.setPicUrl("http://example.com/pic.jpg");
        item.setSubPicUrl("http://example.com/subpic.jpg");
        item.setType(MediaTypeEnum.PIC.type());
        RatioInfoDTO ratioInfo = new RatioInfoDTO();
        ratioInfo.setWidth(1600);
        ratioInfo.setHeight(900);
        item.setRatioInfo(ratioInfo);
        ExhibitImageConfig config = new ExhibitImageConfig();
        config.setDefaultImageBestScale("1:1");
        List<String> themeNames = new ArrayList<>();
        boolean isMt = true;
        List<HaiMaNailFilterConfig> nailFilterConfigs = new ArrayList<>();
        // act
        ImageUrlVO result = invokeGetImmersiveImageUrlVO(item, config, themeNames, isMt, nailFilterConfigs);
        // assert
        assertNotNull(result);
        assertEquals(1600, result.getWidth());
        assertEquals(900, result.getHeight());
        assertEquals("16:9", result.getImageBestScale());
    }

    @Test
    public void testGetImmersiveImageUrlVO_NullRatioInfo() throws Throwable {
        // arrange
        DisplayItem item = new DisplayItem();
        item.setPicUrl("http://example.com/pic.jpg");
        item.setSubPicUrl("http://example.com/subpic.jpg");
        item.setType(MediaTypeEnum.PIC.type());
        item.setRatioInfo(null);
        ExhibitImageConfig config = new ExhibitImageConfig();
        config.setDefaultImageBestScale("4:3");
        List<String> themeNames = new ArrayList<>();
        boolean isMt = true;
        List<HaiMaNailFilterConfig> nailFilterConfigs = new ArrayList<>();
        // act
        ImageUrlVO result = invokeGetImmersiveImageUrlVO(item, config, themeNames, isMt, nailFilterConfigs);
        // assert
        assertNotNull(result);
        assertEquals(0, result.getWidth());
        assertEquals(0, result.getHeight());
        assertEquals("4:3", result.getImageBestScale());
    }

    @Test
    public void testGetImmersiveImageUrlVO_WithSpritePic() throws Throwable {
        // arrange
        DisplayItem item = new DisplayItem();
        item.setPicUrl("http://example.com/pic.jpg");
        item.setSubPicUrl("http://example.com/subpic.jpg");
        item.setType(MediaTypeEnum.VIDEO.type());
        VideoSpriteDTO videoSprite = new VideoSpriteDTO();
        videoSprite.setVideoSpriteUrl("http://example.com/sprite.jpg");
        RatioInfoDTO spriteRatioInfo = new RatioInfoDTO();
        spriteRatioInfo.setWidth(100);
        spriteRatioInfo.setHeight(100);
        videoSprite.setRatioInfo(spriteRatioInfo);
        item.setVideoSprite(videoSprite);
        item.setVideoDuration("120");
        ExhibitImageConfig config = new ExhibitImageConfig();
        config.setDefaultImageBestScale("16:9");
        List<String> themeNames = new ArrayList<>();
        boolean isMt = true;
        List<HaiMaNailFilterConfig> nailFilterConfigs = new ArrayList<>();
        // act
        ImageUrlVO result = invokeGetImmersiveImageUrlVO(item, config, themeNames, isMt, nailFilterConfigs);
        // assert
        assertNotNull(result);
        assertNotNull(result.getSpritePic());
        assertEquals("http://example.com/sprite.jpg", result.getSpritePic().getSpritePicUrl());
        assertEquals(10, result.getSpritePic().getRow());
        assertEquals(10, result.getSpritePic().getColumn());
    }

    @Test
    public void testGetImmersiveImageUrlVO_WithNailStylePlc() throws Throwable {
        // arrange
        DisplayItem item = new DisplayItem();
        item.setPicUrl("http://example.com/pic.jpg");
        item.setSubPicUrl("http://example.com/subpic.jpg");
        item.setType(MediaTypeEnum.PIC.type());
        ExhibitImageConfig config = new ExhibitImageConfig();
        config.setDefaultImageBestScale("1:1");
        config.setPlcMoreStyleText("More Styles");
        config.setPlcMoreStyleIcon("style_icon.png");
        config.setDpPlcMoreStyleUrl("http://example.com/more_styles");
        List<String> themeNames = Arrays.asList("Theme1");
        boolean isMt = false;
        HaiMaNailFilterConfig nailFilterConfig = new HaiMaNailFilterConfig();
        nailFilterConfig.setName("Theme1");
        nailFilterConfig.setId("123");
        List<HaiMaNailFilterConfig> nailFilterConfigs = Collections.singletonList(nailFilterConfig);
        // Mock the private method getNailStylePlcForImmersiveImg
        NailStylePlcVO nailStylePlcVO = new NailStylePlcVO();
        nailStylePlcVO.setMoreStyleText("More Styles");
        nailStylePlcVO.setMoreStyleIcon("style_icon.png");
        nailStylePlcVO.setMoreStyleUrl("http://example.com/more_styles");
        nailStylePlcVO.setStyleTheme("Theme1");
        // act
        ImageUrlVO result = invokeGetImmersiveImageUrlVO(item, config, themeNames, isMt, nailFilterConfigs);
        // assert
        assertNotNull(result);
        assertNotNull(result.getHotNailStyle());
        assertEquals("More Styles", result.getHotNailStyle().getMoreStyleText());
        assertEquals("style_icon.png", result.getHotNailStyle().getMoreStyleIcon());
        assertEquals("http://example.com/more_styles", result.getHotNailStyle().getMoreStyleUrl());
        assertEquals("Theme1", result.getHotNailStyle().getStyleTheme());
    }

    @Test
    public void testGetImmersiveImageUrlVO_EmptyThemeNames() throws Throwable {
        // arrange
        DisplayItem item = new DisplayItem();
        item.setPicUrl("http://example.com/pic.jpg");
        item.setSubPicUrl("http://example.com/subpic.jpg");
        item.setType(MediaTypeEnum.PIC.type());
        ExhibitImageConfig config = new ExhibitImageConfig();
        config.setDefaultImageBestScale("1:1");
        // Empty themeNames
        List<String> themeNames = new ArrayList<>();
        boolean isMt = true;
        List<HaiMaNailFilterConfig> nailFilterConfigs = new ArrayList<>();
        // act
        ImageUrlVO result = invokeGetImmersiveImageUrlVO(item, config, themeNames, isMt, nailFilterConfigs);
        // assert
        assertNotNull(result);
        assertNull(result.getHotNailStyle());
    }

    @Test
    public void testGetNailStylePlcForImmersiveImg_WithMatchingTheme() throws Throwable {
        // arrange
        List<String> themeNames = Arrays.asList("Theme1");
        ExhibitImageConfig config = new ExhibitImageConfig();
        config.setPlcMoreStyleText("More Styles");
        config.setPlcMoreStyleIcon("style_icon.png");
        config.setDpPlcMoreStyleUrl("http://example.com/more_styles");
        boolean isMt = false;
        HaiMaNailFilterConfig nailFilterConfig = new HaiMaNailFilterConfig();
        nailFilterConfig.setName("Theme1");
        nailFilterConfig.setId("123");
        List<HaiMaNailFilterConfig> nailFilterConfigs = Collections.singletonList(nailFilterConfig);
        // act
        NailStylePlcVO result = invokeGetNailStylePlcForImmersiveImg(themeNames, config, isMt, nailFilterConfigs);
        // assert
        assertNotNull(result);
        assertEquals("More Styles", result.getMoreStyleText());
        assertEquals("style_icon.png", result.getMoreStyleIcon());
        assertEquals("http://example.com/more_styles", result.getMoreStyleUrl());
        assertEquals("Theme1", result.getStyleTheme());
    }
}
