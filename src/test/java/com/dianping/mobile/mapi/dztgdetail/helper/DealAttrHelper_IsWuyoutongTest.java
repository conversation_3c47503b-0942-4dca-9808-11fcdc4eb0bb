package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.util.VersionUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.mockStatic;

@RunWith(MockitoJUnitRunner.class)
public class DealAttrHelper_IsWuyoutongTest {

    private MockedStatic<Lion> mockedLion;

    private MockedStatic<VersionUtils> mockedVersionUtils;

    @Before
    public void setUp() {
        mockedLion = mockStatic(Lion.class);
        mockedVersionUtils = mockStatic(VersionUtils.class);
    }

    @After
    public void tearDown() {
        if (mockedLion != null) {
            mockedLion.close();
        }
        if (mockedVersionUtils != null) {
            mockedVersionUtils.close();
        }
    }

    @Test
    public void testIsWuyoutongWithEmptyInput() throws Throwable {
        assertFalse(DealAttrHelper.isWuyoutong(null, "1.0.0"));
        assertFalse(DealAttrHelper.isWuyoutong(Collections.emptyList(), "1.0.0"));
        assertFalse(DealAttrHelper.isWuyoutong(Arrays.asList(new AttributeDTO()), null));
    }

    @Test
    public void testIsWuyoutongWithLionFailure() throws Throwable {
        mockedLion.when(() -> Lion.getString("appName", "WUYOUTONG_MIN_MRNVERSION")).thenReturn("");
        assertFalse(DealAttrHelper.isWuyoutong(Arrays.asList(new AttributeDTO()), "1.0.0"));
    }

    @Test
    public void testIsWuyoutongWithVersionNotGreaterOrEqual() throws Throwable {
        mockedLion.when(() -> Lion.getString("appName", "WUYOUTONG_MIN_MRNVERSION")).thenReturn("2.0.0");
        mockedVersionUtils.when(() -> VersionUtils.isGreatEqualThan("1.0.0", "2.0.0")).thenReturn(false);
        assertFalse(DealAttrHelper.isWuyoutong(Arrays.asList(new AttributeDTO()), "1.0.0"));
    }

    @Test
    public void testIsWuyoutongWithNoMatchedAttribute() throws Throwable {
        mockedLion.when(() -> Lion.getString("appName", "WUYOUTONG_MIN_MRNVERSION")).thenReturn("1.0.0");
        mockedVersionUtils.when(() -> VersionUtils.isGreatEqualThan("1.0.0", "1.0.0")).thenReturn(true);
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("otherKey");
        attributeDTO.setValue(Arrays.asList("otherValue"));
        assertFalse(DealAttrHelper.isWuyoutong(Arrays.asList(attributeDTO), "1.0.0"));
    }
}
