package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.BaseDisplayItemVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailDisplayUnitVO;
import com.dianping.mobile.mapi.dztgdetail.entity.EyeInspectionInstruction;
import com.dianping.mobile.mapi.dztgdetail.entity.EyeServiceProcessNode;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class SpecificModuleHandler_1604Test {

    private SpecificModuleHandler_1604 specificModuleHandler_1604 = new SpecificModuleHandler_1604();

    private Method buildInspectionInstructionsMethod;

    private SpecificModuleHandler_1604 specificModuleHandler = new SpecificModuleHandler_1604();

    @Before
    public void setUp() throws Exception {
        buildInspectionInstructionsMethod = SpecificModuleHandler_1604.class.getDeclaredMethod("buildInspectionInstructions", List.class);
        buildInspectionInstructionsMethod.setAccessible(true);
    }

    private DealDetailDisplayUnitVO invokeBuildInspectionInstructions(List<ServiceProjectAttrDTO> serviceProjectAttributes) throws Exception {
        return (DealDetailDisplayUnitVO) buildInspectionInstructionsMethod.invoke(specificModuleHandler_1604, serviceProjectAttributes);
    }

    private String invokeGetServiceProjectAttrValue(List<ServiceProjectAttrDTO> serviceProjectAttributes, String attrName) throws Exception {
        Method method = SpecificModuleHandler_1604.class.getDeclaredMethod("getServiceProjectAttrValue", List.class, String.class);
        method.setAccessible(true);
        return (String) method.invoke(specificModuleHandler_1604, serviceProjectAttributes, attrName);
    }

    private DealDetailDisplayUnitVO invokeBuildServiceProcess(List<ServiceProjectAttrDTO> serviceProjectAttributes) throws Exception {
        Method method = SpecificModuleHandler_1604.class.getDeclaredMethod("buildServiceProcess", List.class);
        method.setAccessible(true);
        return (DealDetailDisplayUnitVO) method.invoke(specificModuleHandler, serviceProjectAttributes);
    }

    @Test(expected = InvocationTargetException.class)
    public void testBuildInspectionInstructionsWhenServiceProjectAttributesIsNull() throws Throwable {
        List<ServiceProjectAttrDTO> serviceProjectAttributes = null;
        invokeBuildInspectionInstructions(serviceProjectAttributes);
    }

    @Test
    public void testBuildInspectionInstructionsWhenServiceProjectAttributesNotContainsInspectionInstructionsArray() throws Throwable {
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("other");
        serviceProjectAttrDTO.setAttrValue("value");
        List<ServiceProjectAttrDTO> serviceProjectAttributes = Collections.singletonList(serviceProjectAttrDTO);
        DealDetailDisplayUnitVO result = invokeBuildInspectionInstructions(serviceProjectAttributes);
        assertNull(result);
    }

    @Test
    public void testBuildInspectionInstructionsWhenServiceProjectAttributesContainsInspectionInstructionsArrayButValueIsEmpty() throws Throwable {
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("inspectionInstructionsArray");
        serviceProjectAttrDTO.setAttrValue("");
        List<ServiceProjectAttrDTO> serviceProjectAttributes = Collections.singletonList(serviceProjectAttrDTO);
        DealDetailDisplayUnitVO result = invokeBuildInspectionInstructions(serviceProjectAttributes);
        assertNull(result);
    }

    @Test(expected = InvocationTargetException.class)
    public void testBuildInspectionInstructionsWhenServiceProjectAttributesContainsInspectionInstructionsArrayButValueIsInvalid() throws Throwable {
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("inspectionInstructionsArray");
        serviceProjectAttrDTO.setAttrValue("invalid json");
        List<ServiceProjectAttrDTO> serviceProjectAttributes = Collections.singletonList(serviceProjectAttrDTO);
        invokeBuildInspectionInstructions(serviceProjectAttributes);
    }

    @Test
    public void testBuildInspectionInstructionsWhenServiceProjectAttributesContainsInspectionInstructionsArrayAndValueIsValid() throws Throwable {
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("inspectionInstructionsArray");
        serviceProjectAttrDTO.setAttrValue("[{\"content\":\"test\"}]");
        List<ServiceProjectAttrDTO> serviceProjectAttributes = Collections.singletonList(serviceProjectAttrDTO);
        DealDetailDisplayUnitVO result = invokeBuildInspectionInstructions(serviceProjectAttributes);
        assertNotNull(result);
        assertEquals("tips", result.getType());
        assertEquals("到店检查前须知", result.getTitle());
        assertEquals(1, result.getDisplayItems().size());
        assertEquals("test", result.getDisplayItems().get(0).getDesc());
    }

    @Test
    public void testGetServiceProjectAttrValueWhenListIsEmpty() throws Throwable {
        String result = invokeGetServiceProjectAttrValue(Collections.emptyList(), "attrName");
        assertEquals("", result);
    }

    @Test
    public void testGetServiceProjectAttrValueWhenNoMatchedAttrName() throws Throwable {
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("otherName");
        serviceProjectAttrDTO.setAttrValue("");
        String result = invokeGetServiceProjectAttrValue(Arrays.asList(serviceProjectAttrDTO), "attrName");
        assertEquals("", result);
    }

    @Test
    public void testGetServiceProjectAttrValueWhenAllAttrValueIsEmpty() throws Throwable {
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("attrName");
        serviceProjectAttrDTO.setAttrValue("");
        String result = invokeGetServiceProjectAttrValue(Arrays.asList(serviceProjectAttrDTO), "attrName");
        assertEquals("", result);
    }

    @Test
    public void testGetServiceProjectAttrValueWhenAttrValueIsNotEmpty() throws Throwable {
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("attrName");
        serviceProjectAttrDTO.setAttrValue("value");
        String result = invokeGetServiceProjectAttrValue(Arrays.asList(serviceProjectAttrDTO), "attrName");
        assertEquals("value", result);
    }

    @Test(expected = java.lang.reflect.InvocationTargetException.class)
    public void testBuildServiceProcessWhenServiceProjectAttributesIsNull() throws Throwable {
        List<ServiceProjectAttrDTO> serviceProjectAttributes = null;
        invokeBuildServiceProcess(serviceProjectAttributes);
    }

    @Test
    public void testBuildServiceProcessWhenServiceProjectAttributesNotContainsStandardServiceProcess() throws Throwable {
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("other");
        serviceProjectAttrDTO.setAttrValue("value");
        List<ServiceProjectAttrDTO> serviceProjectAttributes = Collections.singletonList(serviceProjectAttrDTO);
        DealDetailDisplayUnitVO result = invokeBuildServiceProcess(serviceProjectAttributes);
        assertNull(result);
    }

    @Test
    public void testBuildServiceProcessWhenServiceProjectAttributesContainsStandardServiceProcessButValueIsInvalid() throws Throwable {
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("standardServiceProcess");
        serviceProjectAttrDTO.setAttrValue("[{\"processName\":\"node1\",\"processDescription\":\"desc1\"}]");
        List<ServiceProjectAttrDTO> serviceProjectAttributes = Collections.singletonList(serviceProjectAttrDTO);
        DealDetailDisplayUnitVO result = invokeBuildServiceProcess(serviceProjectAttributes);
        assertNotNull(result);
    }

    @Test
    public void testBuildServiceProcessWhenServiceProjectAttributesContainsStandardServiceProcessAndValueIsValidButNodesIsEmpty() throws Throwable {
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("standardServiceProcess");
        serviceProjectAttrDTO.setAttrValue("[]");
        List<ServiceProjectAttrDTO> serviceProjectAttributes = Collections.singletonList(serviceProjectAttrDTO);
        DealDetailDisplayUnitVO result = invokeBuildServiceProcess(serviceProjectAttributes);
        assertNull(result);
    }

    @Test
    public void testBuildServiceProcessWhenServiceProjectAttributesContainsStandardServiceProcessAndValueIsValidAndNodesIsNotEmpty() throws Throwable {
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("standardServiceProcess");
        serviceProjectAttrDTO.setAttrValue("[{\"processName\":\"node1\",\"processDescription\":\"desc1\"}]");
        List<ServiceProjectAttrDTO> serviceProjectAttributes = Collections.singletonList(serviceProjectAttrDTO);
        DealDetailDisplayUnitVO result = invokeBuildServiceProcess(serviceProjectAttributes);
        assertNotNull(result);
        assertEquals("process", result.getType());
        assertEquals("服务流程", result.getTitle());
        assertEquals(1, result.getDisplayItems().size());
        BaseDisplayItemVO item = result.getDisplayItems().get(0);
        assertEquals("node1", item.getName());
        assertEquals("desc1", item.getDesc());
    }
}
