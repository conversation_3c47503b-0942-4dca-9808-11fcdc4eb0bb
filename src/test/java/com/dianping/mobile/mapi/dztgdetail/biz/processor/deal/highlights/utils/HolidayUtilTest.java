package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.utils;

import com.sankuai.meituan.holiday.thrift.dict.LunarFestivalType;
import com.sankuai.meituan.holiday.thrift.dict.SolarFestivalType;
import com.sankuai.meituan.holiday.thrift.struct.MtHoliday;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class HolidayUtilTest {

    /**
     * 测试正常场景
     */
    @Test
    public void testGetHolidayListOfRange_Normal() {
        // arrange
        Date begin = new Date(121, Calendar.JANUARY, 1);
        Date end = new Date(121, Calendar.JANUARY, 10);

        // act
        List<MtHoliday> holidays = HolidayUtil.getHolidayListOfRange(begin, end);

        // assert
        Assert.assertEquals(1, holidays.size());
        Assert.assertEquals(SolarFestivalType.NEW_YEAR, holidays.get(0).getSolarFestivalType());
        Assert.assertEquals(LunarFestivalType.NOT_FESTIVAL, holidays.get(0).getLunarFestivalType());
    }

    /**
     * 测试异常场景：begin 日期晚于 end 日期
     */
    @Test
    public void testGetHolidayListOfRange_BeginAfterEnd() {
        // arrange
        Date begin = new Date(121, Calendar.JANUARY, 10);
        Date end = new Date(121, Calendar.JANUARY, 1);

        // act
        List<MtHoliday> holidayListOfRange = HolidayUtil.getHolidayListOfRange(begin, end);
        // assert
        Assert.assertEquals(holidayListOfRange, Collections.emptyList());
    }

    /**
     * 测试异常场景：begin 日期为空
     */
    @Test
    public void testGetHolidayListOfRange_BeginIsNull() {
        // arrange
        Date begin = null;
        Date end = new Date(121, Calendar.JANUARY, 1);
        // act
        List<MtHoliday> holidayListOfRange = HolidayUtil.getHolidayListOfRange(begin, end);
        // assert
        Assert.assertEquals(holidayListOfRange, Collections.emptyList());
    }

    /**
     * 测试异常场景：end 日期为空
     */
    @Test
    public void testGetHolidayListOfRange_EndIsNull() {
        // arrange
        Date begin = new Date(121, Calendar.JANUARY, 1);
        Date end = null;
        // act
        List<MtHoliday> holidayListOfRange = HolidayUtil.getHolidayListOfRange(begin, end);
        // assert
        Assert.assertEquals(holidayListOfRange, Collections.emptyList());
    }

    /**
     * 测试边界场景：begin 和 end 日期相同
     */
    @Test
    public void testGetHolidayListOfRange_BeginEqualsEnd() {
        // arrange
        Date begin = new Date(121, Calendar.JANUARY, 1);
        Date end = new Date(121, Calendar.JANUARY, 1);

        // act
        List<MtHoliday> holidays = HolidayUtil.getHolidayListOfRange(begin, end);

        // assert
        Assert.assertEquals(1, holidays.size());
        Assert.assertEquals(SolarFestivalType.NEW_YEAR, holidays.get(0).getSolarFestivalType());
        Assert.assertEquals(LunarFestivalType.NOT_FESTIVAL, holidays.get(0).getLunarFestivalType());
    }

    /**
     * 测试边界场景：begin 和 end 日期跨越多个年份
     */
    @Test
    public void testGetHolidayListOfRange_MultiYear() {
        // arrange
        Date begin = new Date(120, Calendar.DECEMBER, 31);
        Date end = new Date(121, Calendar.JANUARY, 2);

        // act
        List<MtHoliday> holidays = HolidayUtil.getHolidayListOfRange(begin, end);

        // assert
        Assert.assertEquals(1, holidays.size());
        Assert.assertEquals(SolarFestivalType.NEW_YEAR, holidays.get(0).getSolarFestivalType());
    }

    /**
     * 测试正常场景：begin 和 end 日期在同一年
     */
    @Test
    public void testGetHolidayListOfRange_SameYear() {
        // arrange
        Date begin = new Date(121, Calendar.JANUARY, 1);
        Date end = new Date(121, Calendar.DECEMBER, 31);
        // act
        List<MtHoliday> holidays = HolidayUtil.getHolidayListOfRange(begin, end);
        // assert
        Assert.assertEquals(10, holidays.size());
        Assert.assertEquals(SolarFestivalType.NEW_YEAR, holidays.get(0).getSolarFestivalType());
        Assert.assertEquals(LunarFestivalType.NEW_YEAR, holidays.get(1).getLunarFestivalType());
        Assert.assertEquals(SolarFestivalType.VALENTINE, holidays.get(2).getSolarFestivalType());
        Assert.assertEquals(LunarFestivalType.QINGMING, holidays.get(3).getLunarFestivalType());
        Assert.assertEquals(SolarFestivalType.MAY_DAY, holidays.get(4).getSolarFestivalType());
        Assert.assertEquals(SolarFestivalType.CHILDREN_DAY, holidays.get(5).getSolarFestivalType());
        Assert.assertEquals(LunarFestivalType.DRAGON_BOAT, holidays.get(6).getLunarFestivalType());
        Assert.assertEquals(LunarFestivalType.MID_AUTUMN, holidays.get(7).getLunarFestivalType());
        Assert.assertEquals(SolarFestivalType.NATIONAL_DAY, holidays.get(8).getSolarFestivalType());
        Assert.assertEquals(SolarFestivalType.CHRISTMAS, holidays.get(9).getSolarFestivalType());
    }

    /**
     * 测试正常场景：begin 和 end 日期跨越多年
     */
    @Test
    public void testGetHolidayListOfRange_MultiYear_2() {
        // arrange
        Date begin = new Date(121, Calendar.JANUARY, 1);
        Date end = new Date(122, Calendar.DECEMBER, 31);
        // act
        List<MtHoliday> holidays = HolidayUtil.getHolidayListOfRange(begin, end);
        // assert
        Assert.assertEquals(20, holidays.size());
        Assert.assertEquals(SolarFestivalType.NEW_YEAR, holidays.get(0).getSolarFestivalType());
        Assert.assertEquals(LunarFestivalType.NEW_YEAR, holidays.get(1).getLunarFestivalType());
        Assert.assertEquals(SolarFestivalType.VALENTINE, holidays.get(2).getSolarFestivalType());
        Assert.assertEquals(LunarFestivalType.QINGMING, holidays.get(3).getLunarFestivalType());
        Assert.assertEquals(SolarFestivalType.MAY_DAY, holidays.get(4).getSolarFestivalType());
        Assert.assertEquals(SolarFestivalType.CHILDREN_DAY, holidays.get(5).getSolarFestivalType());
        Assert.assertEquals(LunarFestivalType.DRAGON_BOAT, holidays.get(6).getLunarFestivalType());
        Assert.assertEquals(LunarFestivalType.MID_AUTUMN, holidays.get(7).getLunarFestivalType());
        Assert.assertEquals(SolarFestivalType.NATIONAL_DAY, holidays.get(8).getSolarFestivalType());
        Assert.assertEquals(SolarFestivalType.CHRISTMAS, holidays.get(9).getSolarFestivalType());
    }
}
