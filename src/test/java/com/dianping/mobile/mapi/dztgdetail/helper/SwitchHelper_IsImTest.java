package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.lion.client.Lion;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.Collections;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mockStatic;

@RunWith(MockitoJUnitRunner.class)
public class SwitchHelper_IsImTest {

    private MockedStatic<Lion> mockedStaticLion;

    @Before
    public void setUp() {
        // Initialize the mocked static block before each test
        mockedStaticLion = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        // Close the mocked static block after each test to ensure isolation
        mockedStaticLion.close();
    }

    /**
     * Tests the isIm method when the imChannelId list is empty and channelId is not in the imChannelId list, expecting false.
     */
    @Test
    public void testIsImWhenImChannelIdListIsEmptyAndChannelIdNotInList() throws Throwable {
        // Mock the Lion.getList method to return an empty list
        mockedStaticLion.when(() -> Lion.getList("IM_CHANNEL_IDS", Integer.class)).thenReturn(Collections.emptyList());
        // Call the method under test
        boolean result = SwitchHelper.isIm(1);
        // Assert that the result is false
        assertFalse(result);
    }

    /**
     * Tests the isIm method when the imChannelId list is not empty and channelId is not in the imChannelId list, expecting false.
     */
    @Test
    public void testIsImWhenImChannelIdListIsNotEmptyAndChannelIdNotInList() throws Throwable {
        // Mock the Lion.getList method to return a list that does not contain the channelId
        mockedStaticLion.when(() -> Lion.getList("IM_CHANNEL_IDS", Integer.class)).thenReturn(Arrays.asList(2, 3, 4));
        // Call the method under test
        boolean result = SwitchHelper.isIm(1);
        // Assert that the result is false
        assertFalse(result);
    }
}
