package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.CouponCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedGetCouponReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.coupon.UnifiedGetCouponModule;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.UnifiedCouponWrapper;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.Arrays;
import java.util.concurrent.Future;
import static org.junit.Assert.*;
import org.junit.*;
import org.mockito.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedCouponFacadeTest {

    @InjectMocks
    private UnifiedCouponFacade unifiedCouponFacade;

    @Mock
    private UnifiedCouponWrapper unifiedCouponWrapper;

    @Mock
    private UnifiedGetCouponReq request;

    @Mock
    private EnvCtx envCtx;

    @Mock
    private IMobileContext iMobileContext;

    @Mock
    private Future<String> future;

    @Before
    public void setUp() {
        when(unifiedCouponWrapper.preMerchantCouponIssue(any(CouponCtx.class))).thenReturn(future);
        when(unifiedCouponWrapper.getMerchantCouponIssue(future)).thenReturn("result");
    }

    @Test
    public void testQueryUnifiedGetCouponModuleNormal() throws Throwable {
        when(request.getCouponGroupId()).thenReturn("groupId");
        when(unifiedCouponWrapper.queryIssuedCouponList(envCtx, "groupId")).thenReturn(Arrays.asList());
        UnifiedGetCouponModule result = unifiedCouponFacade.queryUnifiedGetCouponModule(request, envCtx, iMobileContext);
        assertTrue(result.isSuccess());
        assertEquals("领券成功", result.getMessage());
    }

    @Test
    public void testQueryUnifiedGetCouponModuleAlreadyGot() throws Throwable {
        UnifiedCouponGroupDTO unifiedCouponGroupDTO = new UnifiedCouponGroupDTO();
        UnifiedCouponDTO unifiedCouponDTO = new UnifiedCouponDTO("couponId", unifiedCouponGroupDTO);
        when(request.getCouponGroupId()).thenReturn("groupId");
        when(unifiedCouponWrapper.queryIssuedCouponList(envCtx, "groupId")).thenReturn(Arrays.asList(unifiedCouponDTO));
        UnifiedGetCouponModule result = unifiedCouponFacade.queryUnifiedGetCouponModule(request, envCtx, iMobileContext);
        assertTrue(result.isSuccess());
        assertEquals("您已经领过啦", result.getMessage());
    }

    @Test(expected = Exception.class)
    public void testQueryUnifiedGetCouponModuleException() throws Throwable {
        when(unifiedCouponWrapper.queryIssuedCouponList(envCtx, "groupId")).thenThrow(new Exception());
        unifiedCouponFacade.queryUnifiedGetCouponModule(request, envCtx, iMobileContext);
    }
}
