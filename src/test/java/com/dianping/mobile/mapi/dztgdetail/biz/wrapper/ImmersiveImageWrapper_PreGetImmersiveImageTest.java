package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.entity.QueryExhibitImageFilterParam;
import com.sankuai.mpmctcontent.application.thrift.dto.content.LoadClassicContentFilterReqDTO;
import org.junit.rules.ExpectedException;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Future;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.mpmctcontent.application.thrift.api.content.DealDetailPageGWService;
import java.lang.reflect.Field;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

public class ImmersiveImageWrapper_PreGetImmersiveImageTest {

    @Mock
    private DealDetailPageGWService dealDetailPageGWServiceFuture;

    @InjectMocks
    private ImmersiveImageWrapper immersiveImageWrapper;

    private AutoCloseable closeable;

    private MockedStatic<Lion> mockedLion;

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @Before
    public void setUp() throws Exception {
        closeable = MockitoAnnotations.openMocks(this);
        mockedLion = mockStatic(Lion.class);
    }

    @After
    public void tearDown() throws Exception {
        closeable.close();
        mockedLion.close();
    }

    private QueryExhibitImageFilterParam createParam(Integer categoryId) {
        return QueryExhibitImageFilterParam.builder().clientType(1).shopId(1L).bizType(1).subBizType(1).externalBizId(1).externalBizIdType(1).categoryId(categoryId).build();
    }

    @Test
    public void testPreGetImmersiveImageFilter_CategoryIdIncluded() throws Throwable {
        QueryExhibitImageFilterParam param = createParam(1);
        List<Integer> categoryIds = Arrays.asList(1, 2, 3);
        mockedLion.when(() -> Lion.getList(LionConstants.APP_KEY, LionConstants.IMMERSIVE_EXHIBIT_IMAGE_SHOW_FILTER_CATEGORY_IDS_CONFIG, Integer.class, Arrays.asList())).thenReturn(categoryIds);
        when(dealDetailPageGWServiceFuture.loadDealFilter(any(LoadClassicContentFilterReqDTO.class))).thenReturn(null);
        immersiveImageWrapper.preGetImmersiveImageFilter(param);
        verify(dealDetailPageGWServiceFuture, times(1)).loadDealFilter(any(LoadClassicContentFilterReqDTO.class));
    }

    @Test
    public void testPreGetImmersiveImageFilter_CategoryIdNotIncluded() throws Throwable {
        QueryExhibitImageFilterParam param = createParam(4);
        List<Integer> categoryIds = Arrays.asList(1, 2, 3);
        mockedLion.when(() -> Lion.getList(LionConstants.APP_KEY, LionConstants.IMMERSIVE_EXHIBIT_IMAGE_SHOW_FILTER_CATEGORY_IDS_CONFIG, Integer.class, Arrays.asList())).thenReturn(categoryIds);
        Future result = immersiveImageWrapper.preGetImmersiveImageFilter(param);
        verify(dealDetailPageGWServiceFuture, never()).loadDealFilter(any(LoadClassicContentFilterReqDTO.class));
        assertNull("Expected null to be returned", result);
    }
}
