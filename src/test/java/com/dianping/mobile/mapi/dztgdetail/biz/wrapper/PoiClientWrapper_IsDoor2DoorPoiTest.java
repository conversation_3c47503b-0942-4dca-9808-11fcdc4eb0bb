package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.poi.biz.api.dto.PoiBizAttrGetDTO;
import com.dianping.poi.biz.api.dto.PoiBizAttrValueDTO;
import com.dianping.poi.biz.api.service.PoiBizService;
import com.dianping.poi.base.common.enums.PoiIdTypeEnum;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

public class PoiClientWrapper_IsDoor2DoorPoiTest {

    @InjectMocks
    private PoiClientWrapper poiClientWrapper;

    @Mock
    private PoiBizService poiBizService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testIsDoor2DoorPoiWhenGetPoiBizAttrThrowsException() throws Exception {
        when(poiBizService.getPoiBizAttr(anyLong(), any(PoiIdTypeEnum.class), anyList())).thenThrow(new RuntimeException());
        assertFalse(poiClientWrapper.isDoor2DoorPoi(1L));
    }

    @Test
    public void testIsDoor2DoorPoiWhenPoiBizAttrIsEmpty() throws Exception {
        when(poiBizService.getPoiBizAttr(anyLong(), any(PoiIdTypeEnum.class), anyList())).thenReturn(Collections.emptyMap());
        assertFalse(poiClientWrapper.isDoor2DoorPoi(1L));
    }

    @Test
    public void testIsDoor2DoorPoiWhenAttrValueMapIsEmpty() throws Exception {
        Map<Integer, PoiBizAttrValueDTO> poiBizAttr = new HashMap<>();
        poiBizAttr.put(1001, new PoiBizAttrValueDTO());
        when(poiBizService.getPoiBizAttr(anyLong(), any(PoiIdTypeEnum.class), anyList())).thenReturn(poiBizAttr);
        assertFalse(poiClientWrapper.isDoor2DoorPoi(1L));
    }

    @Test
    public void testIsDoor2DoorPoiWhenDoor2DoorAttrIsNotPureDoor2Door() throws Exception {
        Map<Integer, PoiBizAttrValueDTO> poiBizAttr = new HashMap<>();
        Map<Integer, String> attrValueMap = new HashMap<>();
        attrValueMap.put(1001001, "notPureDoor2Door");
        PoiBizAttrValueDTO poiBizAttrValueDTO = new PoiBizAttrValueDTO();
        poiBizAttrValueDTO.setAttrValueMap(attrValueMap);
        poiBizAttr.put(1001, poiBizAttrValueDTO);
        when(poiBizService.getPoiBizAttr(anyLong(), any(PoiIdTypeEnum.class), anyList())).thenReturn(poiBizAttr);
        assertFalse(poiClientWrapper.isDoor2DoorPoi(1L));
    }

    @Test
    public void testIsDoor2DoorPoiWhenDoor2DoorAttrIsPureDoor2Door() throws Exception {
        Map<Integer, PoiBizAttrValueDTO> poiBizAttr = new HashMap<>();
        Map<Integer, String> attrValueMap = new HashMap<>();
        attrValueMap.put(1001001, "2");
        PoiBizAttrValueDTO poiBizAttrValueDTO = new PoiBizAttrValueDTO();
        poiBizAttrValueDTO.setAttrValueMap(attrValueMap);
        poiBizAttr.put(1001, poiBizAttrValueDTO);
        when(poiBizService.getPoiBizAttr(anyLong(), any(PoiIdTypeEnum.class), anyList())).thenReturn(poiBizAttr);
        assertTrue(poiClientWrapper.isDoor2DoorPoi(1L));
    }
}
