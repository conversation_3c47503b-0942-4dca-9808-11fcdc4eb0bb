package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.deal.detail.enums.PlatformEnum;
import com.dianping.mobile.mapi.dztgdetail.biz.immersiveimage.ImmersiveImageService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageFilterRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageFilterVO;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class ImmersiveImageFilterFacadeGetImmersiveImageFilterTest {

    @InjectMocks
    private ImmersiveImageFilterFacade immersiveImageFilterFacade;

    @Mock
    private ImmersiveImageService immersiveImageService;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private List<ImmersiveImageService> immersiveImageServices;

    private GetImmersiveImageFilterRequest request;

    private EnvCtx envCtx;

    private void initializeMocks() {
        // Assuming ImmersiveImageFilterFacade has a setter for immersiveImageServices
        // or you can use reflection to set the field if it's private
        ReflectionTestUtils.setField(immersiveImageFilterFacade, "immersiveImageServices", Arrays.asList(immersiveImageService));
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetImmersiveImageFilterDealGroupIdInvalid() throws Throwable {
        initializeMocks();
        request = new GetImmersiveImageFilterRequest();
        request.setDealGroupId(-1);
        request.setShopId(1L);
        envCtx = new EnvCtx();
        immersiveImageFilterFacade.getImmersiveImageFilter(request, envCtx);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetImmersiveImageFilterShopIdInvalid() throws Throwable {
        initializeMocks();
        request = new GetImmersiveImageFilterRequest();
        request.setDealGroupId(1);
        request.setShopId(-1L);
        envCtx = new EnvCtx();
        immersiveImageFilterFacade.getImmersiveImageFilter(request, envCtx);
    }

    // Note: The following tests are limited by the inability to mock or inject the list of ImmersiveImageService instances
    // directly into the ImmersiveImageFilterFacade due to the constraints provided.
    // The NullPointerException issue suggests a need for such an initialization, which cannot be directly addressed here.
    @Test
    public void testGetImmersiveImageFilterEnvCtxIsMtTrue() throws Throwable {
        initializeMocks();
        request = new GetImmersiveImageFilterRequest();
        request.setDealGroupId(1);
        request.setShopId(1L);
        envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum.MEITUAN_APP);
        when(dealGroupWrapper.getCategoryId(any(Integer.class))).thenReturn(1);
        when(immersiveImageService.getCategoryIds()).thenReturn(Arrays.asList(1));
        when(immersiveImageService.getImmersiveImageFilter(any(GetImmersiveImageFilterRequest.class))).thenReturn(new ImmersiveImageFilterVO());
        ImmersiveImageFilterVO result = immersiveImageFilterFacade.getImmersiveImageFilter(request, envCtx);
        assertNotNull(result);
    }

    @Test
    public void testGetImmersiveImageFilterEnvCtxIsMtFalse() throws Throwable {
        initializeMocks();
        request = new GetImmersiveImageFilterRequest();
        request.setDealGroupId(1);
        request.setShopId(1L);
        envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum.DIANPING_APP);
        when(dealGroupWrapper.getCategoryId(any(Integer.class))).thenReturn(1);
        when(immersiveImageService.getCategoryIds()).thenReturn(Arrays.asList(1));
        when(immersiveImageService.getImmersiveImageFilter(any(GetImmersiveImageFilterRequest.class))).thenReturn(new ImmersiveImageFilterVO());
        ImmersiveImageFilterVO result = immersiveImageFilterFacade.getImmersiveImageFilter(request, envCtx);
        assertNotNull(result);
    }

    @Test
    public void testGetImmersiveImageFilterCategoryIdNotExist() throws Throwable {
        initializeMocks();
        request = new GetImmersiveImageFilterRequest();
        request.setDealGroupId(1);
        request.setShopId(1L);
        envCtx = new EnvCtx();
        when(dealGroupWrapper.getCategoryId(any(Integer.class))).thenReturn(1);
        when(immersiveImageService.getCategoryIds()).thenReturn(Collections.emptyList());
        ImmersiveImageFilterVO result = immersiveImageFilterFacade.getImmersiveImageFilter(request, envCtx);
        assertNull(result);
    }
}
