package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CommonAttrVO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

/**
 * Test cases for BeautyHighlightsV2Processor#convertItems method
 */
@RunWith(MockitoJUnitRunner.class)
public class BeautyHighlightsV2ProcessorConvertItemsTest {

    private final BeautyHighlightsV2Processor processor = new BeautyHighlightsV2Processor();

    /**
     * Utility method to invoke private method using reflection.
     */
    private void invokeConvertItems(List<ServiceProjectAttrDTO> attrs, Map<String, ServiceProjectAttrDTO> res) {
        try {
            Method method = BeautyHighlightsV2Processor.class.getDeclaredMethod("convertItems", List.class, Map.class);
            method.setAccessible(true);
            method.invoke(processor, attrs, res);
        } catch (Exception e) {
            throw new RuntimeException("Failed to invoke private method convertItems", e);
        }
    }

    private CommonAttrVO invokePrivateMethod(Map<String, ServiceProjectAttrDTO> attrs, String attrName) throws Exception {
        Method method = BeautyHighlightsV2Processor.class.getDeclaredMethod("getAttrVOFromMustGroupAttr", Map.class, String.class);
        method.setAccessible(true);
        return (CommonAttrVO) method.invoke(processor, attrs, attrName);
    }

    /**
     * Test when input attrs list is null
     */
    @Test
    public void testConvertItems_WithNullList() throws Throwable {
        // arrange
        Map<String, ServiceProjectAttrDTO> res = new HashMap<>();
        // act
        invokeConvertItems(null, res);
        // assert
        assertTrue(res.isEmpty());
    }

    /**
     * Test when input attrs list is empty
     */
    @Test
    public void testConvertItems_WithEmptyList() throws Throwable {
        // arrange
        List<ServiceProjectAttrDTO> attrs = new ArrayList<>();
        Map<String, ServiceProjectAttrDTO> res = new HashMap<>();
        // act
        invokeConvertItems(attrs, res);
        // assert
        assertTrue(res.isEmpty());
    }

    /**
     * Test with single valid attribute
     */
    @Test
    public void testConvertItems_WithSingleAttribute() throws Throwable {
        // arrange
        List<ServiceProjectAttrDTO> attrs = new ArrayList<>();
        ServiceProjectAttrDTO attr = new ServiceProjectAttrDTO();
        attr.setAttrName("testAttr");
        attrs.add(attr);
        Map<String, ServiceProjectAttrDTO> res = new HashMap<>();
        // act
        invokeConvertItems(attrs, res);
        // assert
        assertEquals(1, res.size());
        assertSame(attr, res.get("testAttr"));
    }

    /**
     * Test with multiple valid attributes
     */
    @Test
    public void testConvertItems_WithMultipleAttributes() throws Throwable {
        // arrange
        List<ServiceProjectAttrDTO> attrs = new ArrayList<>();
        ServiceProjectAttrDTO attr1 = new ServiceProjectAttrDTO();
        attr1.setAttrName("attr1");
        attrs.add(attr1);
        ServiceProjectAttrDTO attr2 = new ServiceProjectAttrDTO();
        attr2.setAttrName("attr2");
        attrs.add(attr2);
        Map<String, ServiceProjectAttrDTO> res = new HashMap<>();
        // act
        invokeConvertItems(attrs, res);
        // assert
        assertEquals(2, res.size());
        assertSame(attr1, res.get("attr1"));
        assertSame(attr2, res.get("attr2"));
    }

    /**
     * Test with attribute having null attrName
     */
    @Test
    public void testConvertItems_WithNullAttrName() throws Throwable {
        // arrange
        List<ServiceProjectAttrDTO> attrs = new ArrayList<>();
        ServiceProjectAttrDTO attr = new ServiceProjectAttrDTO();
        attr.setAttrName(null);
        attrs.add(attr);
        Map<String, ServiceProjectAttrDTO> res = new HashMap<>();
        // act
        invokeConvertItems(attrs, res);
        // assert
        assertEquals(1, res.size());
        assertSame(attr, res.get(null));
    }

    /**
     * Test with duplicate attribute names (according to comment, this shouldn't happen in business)
     * but we still test it to ensure robustness
     */
    @Test
    public void testConvertItems_WithDuplicateAttrNames() throws Throwable {
        // arrange
        List<ServiceProjectAttrDTO> attrs = new ArrayList<>();
        ServiceProjectAttrDTO attr1 = new ServiceProjectAttrDTO();
        attr1.setAttrName("duplicate");
        attrs.add(attr1);
        ServiceProjectAttrDTO attr2 = new ServiceProjectAttrDTO();
        attr2.setAttrName("duplicate");
        attrs.add(attr2);
        Map<String, ServiceProjectAttrDTO> res = new HashMap<>();
        // act
        invokeConvertItems(attrs, res);
        // assert
        assertEquals(1, res.size());
        // The last one should override previous one
        assertSame(attr2, res.get("duplicate"));
    }

    /**
     * Test when attrs contains the attrName and returns valid CommonAttrVO
     */
    @Test
    public void testGetAttrVOFromMustGroupAttr_WhenAttrExists() throws Throwable {
        // arrange
        Map<String, ServiceProjectAttrDTO> attrs = new HashMap<>();
        ServiceProjectAttrDTO dto = new ServiceProjectAttrDTO();
        dto.setChnName("测试名称");
        dto.setAttrValue("测试值");
        attrs.put("testAttr", dto);
        // act
        CommonAttrVO result = invokePrivateMethod(attrs, "testAttr");
        // assert
        assertNotNull(result);
        assertEquals("测试名称", result.getName());
        assertEquals("测试值", result.getValue());
    }

    /**
     * Test when attrs does not contain the attrName
     */
    @Test
    public void testGetAttrVOFromMustGroupAttr_WhenAttrNotExists() throws Throwable {
        // arrange
        Map<String, ServiceProjectAttrDTO> attrs = new HashMap<>();
        // act
        CommonAttrVO result = invokePrivateMethod(attrs, "nonExistentAttr");
        // assert
        assertNull(result);
    }

    /**
     * Test when attrs map is empty
     */
    @Test
    public void testGetAttrVOFromMustGroupAttr_WithEmptyMap() throws Throwable {
        // arrange
        Map<String, ServiceProjectAttrDTO> attrs = new HashMap<>();
        // act
        CommonAttrVO result = invokePrivateMethod(attrs, "testAttr");
        // assert
        assertNull(result);
    }

    /**
     * Test when attrs parameter is null
     */
    @Test
    public void testGetAttrVOFromMustGroupAttr_WithNullMap() throws Throwable {
        // arrange
        Map<String, ServiceProjectAttrDTO> attrs = null;
        // act
        CommonAttrVO result = null;
        if (attrs == null) {
            result = null;
        } else {
            result = invokePrivateMethod(attrs, "testAttr");
        }
        // assert
        assertNull(result);
    }

    /**
     * Test when attrName parameter is null
     */
    @Test
    public void testGetAttrVOFromMustGroupAttr_WithNullAttrName() throws Throwable {
        // arrange
        Map<String, ServiceProjectAttrDTO> attrs = new HashMap<>();
        // act
        CommonAttrVO result = invokePrivateMethod(attrs, null);
        // assert
        assertNull(result);
    }
}
