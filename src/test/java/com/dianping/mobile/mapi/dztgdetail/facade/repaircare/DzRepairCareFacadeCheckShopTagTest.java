package com.dianping.mobile.mapi.dztgdetail.facade.repaircare;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.athena.biz.Response;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDisplayShopDTO;
import com.sankuai.zdc.tag.apply.api.PoiTagDisplayRPCService;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import com.sankuai.zdc.tag.apply.dto.FindSceneDisplayTagRequest;
import java.lang.reflect.Method;
import java.util.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DzRepairCareFacadeCheckShopTagTest {

    @InjectMocks
    private DzRepairCareFacade dzRepairCareFacade;

    @Mock
    private PoiTagDisplayRPCService poiTagDisplayRPCService;

    private boolean invokeCheckShopTag(DealGroupDTO dealGroupDTO) throws Exception {
        Method method = DzRepairCareFacade.class.getDeclaredMethod("checkShopTag", DealGroupDTO.class);
        method.setAccessible(true);
        return (boolean) method.invoke(dzRepairCareFacade, dealGroupDTO);
    }

    /**
     * Test case when dealGroupDTO is null
     */
    @Test
    public void testCheckShopTag_NullDealGroupDTO() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = null;
        // act
        boolean result = invokeCheckShopTag(dealGroupDTO);
        // assert
        assertFalse(result);
    }

    /**
     * Test case when displayShopInfo is null
     */
    @Test
    public void testCheckShopTag_NullDisplayShopInfo() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDisplayShopInfo(null);
        // act
        boolean result = invokeCheckShopTag(dealGroupDTO);
        // assert
        assertFalse(result);
    }

    /**
     * Test case when mtDisplayShopIds is empty
     */
    @Test
    public void testCheckShopTag_EmptyMtDisplayShopIds() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupDisplayShopDTO displayShopDTO = new DealGroupDisplayShopDTO();
        displayShopDTO.setMtDisplayShopIds(Collections.emptyList());
        dealGroupDTO.setDisplayShopInfo(displayShopDTO);
        // act
        boolean result = invokeCheckShopTag(dealGroupDTO);
        // assert
        assertFalse(result);
    }

    /**
     * Test case when RPC service throws exception
     */
    @Test
    public void testCheckShopTag_RpcServiceThrowsException() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupDisplayShopDTO displayShopDTO = new DealGroupDisplayShopDTO();
        List<Long> shopIds = Arrays.asList(1L, 2L, 3L);
        displayShopDTO.setMtDisplayShopIds(shopIds);
        dealGroupDTO.setDisplayShopInfo(displayShopDTO);
        when(poiTagDisplayRPCService.findSceneDisplayTagOfMt(any(FindSceneDisplayTagRequest.class))).thenThrow(new RuntimeException("RPC error"));
        // act
        boolean result = invokeCheckShopTag(dealGroupDTO);
        // assert
        assertFalse(result);
        verify(poiTagDisplayRPCService).findSceneDisplayTagOfMt(any(FindSceneDisplayTagRequest.class));
    }

    /**
     * Test case when response is null
     */
    @Test
    public void testCheckShopTag_NullResponse() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupDisplayShopDTO displayShopDTO = new DealGroupDisplayShopDTO();
        List<Long> shopIds = Arrays.asList(1L, 2L, 3L);
        displayShopDTO.setMtDisplayShopIds(shopIds);
        dealGroupDTO.setDisplayShopInfo(displayShopDTO);
        when(poiTagDisplayRPCService.findSceneDisplayTagOfMt(any(FindSceneDisplayTagRequest.class))).thenReturn(null);
        // act
        boolean result = invokeCheckShopTag(dealGroupDTO);
        // assert
        assertFalse(result);
        verify(poiTagDisplayRPCService).findSceneDisplayTagOfMt(any(FindSceneDisplayTagRequest.class));
    }

    /**
     * Test case when response is not successful
     */
    @Test
    public void testCheckShopTag_ResponseNotSuccess() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupDisplayShopDTO displayShopDTO = new DealGroupDisplayShopDTO();
        List<Long> shopIds = Arrays.asList(1L, 2L, 3L);
        displayShopDTO.setMtDisplayShopIds(shopIds);
        dealGroupDTO.setDisplayShopInfo(displayShopDTO);
        Response<Map<Long, List<DisplayTagDto>>> response = new Response<>(500, "Error");
        when(poiTagDisplayRPCService.findSceneDisplayTagOfMt(any(FindSceneDisplayTagRequest.class))).thenReturn(response);
        // act
        boolean result = invokeCheckShopTag(dealGroupDTO);
        // assert
        assertFalse(result);
        verify(poiTagDisplayRPCService).findSceneDisplayTagOfMt(any(FindSceneDisplayTagRequest.class));
    }

    /**
     * Test case when response data is empty
     */
    @Test
    public void testCheckShopTag_EmptyResponseData() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupDisplayShopDTO displayShopDTO = new DealGroupDisplayShopDTO();
        List<Long> shopIds = Arrays.asList(1L, 2L, 3L);
        displayShopDTO.setMtDisplayShopIds(shopIds);
        dealGroupDTO.setDisplayShopInfo(displayShopDTO);
        Response<Map<Long, List<DisplayTagDto>>> response = new Response<>(200, "Success", Collections.emptyMap());
        when(poiTagDisplayRPCService.findSceneDisplayTagOfMt(any(FindSceneDisplayTagRequest.class))).thenReturn(response);
        // act
        boolean result = invokeCheckShopTag(dealGroupDTO);
        // assert
        assertFalse(result);
        verify(poiTagDisplayRPCService).findSceneDisplayTagOfMt(any(FindSceneDisplayTagRequest.class));
    }

    /**
     * Test case when all shops have empty tag lists
     */
    @Test
    public void testCheckShopTag_AllShopsHaveEmptyTagLists() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupDisplayShopDTO displayShopDTO = new DealGroupDisplayShopDTO();
        List<Long> shopIds = Arrays.asList(1L, 2L, 3L);
        displayShopDTO.setMtDisplayShopIds(shopIds);
        dealGroupDTO.setDisplayShopInfo(displayShopDTO);
        Map<Long, List<DisplayTagDto>> data = new HashMap<>();
        data.put(1L, Collections.emptyList());
        data.put(2L, Collections.emptyList());
        data.put(3L, Collections.emptyList());
        Response<Map<Long, List<DisplayTagDto>>> response = new Response<>(200, "Success", data);
        when(poiTagDisplayRPCService.findSceneDisplayTagOfMt(any(FindSceneDisplayTagRequest.class))).thenReturn(response);
        // act
        boolean result = invokeCheckShopTag(dealGroupDTO);
        // assert
        assertFalse(result);
        verify(poiTagDisplayRPCService).findSceneDisplayTagOfMt(any(FindSceneDisplayTagRequest.class));
    }

    /**
     * Test case when at least one shop has tags
     */
    @Test
    public void testCheckShopTag_OneShopHasTags() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupDisplayShopDTO displayShopDTO = new DealGroupDisplayShopDTO();
        List<Long> shopIds = Arrays.asList(1L, 2L, 3L);
        displayShopDTO.setMtDisplayShopIds(shopIds);
        dealGroupDTO.setDisplayShopInfo(displayShopDTO);
        Map<Long, List<DisplayTagDto>> data = new HashMap<>();
        data.put(1L, Collections.emptyList());
        data.put(2L, Arrays.asList(new DisplayTagDto()));
        data.put(3L, Collections.emptyList());
        Response<Map<Long, List<DisplayTagDto>>> response = new Response<>(200, "Success", data);
        when(poiTagDisplayRPCService.findSceneDisplayTagOfMt(any(FindSceneDisplayTagRequest.class))).thenReturn(response);
        // act
        boolean result = invokeCheckShopTag(dealGroupDTO);
        // assert
        assertTrue(result);
        verify(poiTagDisplayRPCService).findSceneDisplayTagOfMt(any(FindSceneDisplayTagRequest.class));
    }

    /**
     * Test case when more than 20 shop IDs are provided (should limit to 20)
     */
    @Test
    public void testCheckShopTag_MoreThan20ShopIds() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupDisplayShopDTO displayShopDTO = new DealGroupDisplayShopDTO();
        // Create a list with 25 shop IDs
        List<Long> shopIds = new ArrayList<>();
        for (int i = 1; i <= 25; i++) {
            shopIds.add((long) i);
        }
        displayShopDTO.setMtDisplayShopIds(shopIds);
        dealGroupDTO.setDisplayShopInfo(displayShopDTO);
        Map<Long, List<DisplayTagDto>> data = new HashMap<>();
        data.put(1L, Arrays.asList(new DisplayTagDto()));
        Response<Map<Long, List<DisplayTagDto>>> response = new Response<>(200, "Success", data);
        when(poiTagDisplayRPCService.findSceneDisplayTagOfMt(any(FindSceneDisplayTagRequest.class))).thenReturn(response);
        // act
        boolean result = invokeCheckShopTag(dealGroupDTO);
        // assert
        assertTrue(result);
        // Verify that the request contains only 20 shop IDs
        verify(poiTagDisplayRPCService).findSceneDisplayTagOfMt(argThat(request -> request.getShopIds() != null && request.getShopIds().size() == 20));
    }
}
