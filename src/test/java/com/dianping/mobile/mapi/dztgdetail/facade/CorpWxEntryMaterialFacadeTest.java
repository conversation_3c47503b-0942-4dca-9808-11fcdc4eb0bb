package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetcorpwxentrymaterialRequest;
import com.sankuai.dz.srcm.flow.dto.FlowEntryWxMaterialRequest;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.concurrent.Future;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiShopCategoryWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class CorpWxEntryMaterialFacadeTest {

    @InjectMocks
    private CorpWxEntryMaterialFacade corpWxEntryMaterialFacade;

    @Mock
    private PoiShopCategoryWrapper poiShopCategoryWrapper;

    @Mock
    private MapperWrapper mapperWrapper;

    @Mock
    private Future<Long> futureLong;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test(expected = NullPointerException.class)
    public void testBuildRequestException() throws Throwable {
        // arrange
        GetcorpwxentrymaterialRequest req = null;
        EnvCtx ctx = null;
        // act
        corpWxEntryMaterialFacade.buildRequest(req, ctx);
        // assert
        // expect an exception to be thrown
    }
}
