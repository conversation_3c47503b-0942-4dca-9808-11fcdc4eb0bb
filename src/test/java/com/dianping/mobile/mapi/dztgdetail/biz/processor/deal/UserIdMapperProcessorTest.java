package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.sankuai.wpt.user.merge.query.thrift.message.FlattedBindRelation;
import com.sankuai.wpt.user.merge.query.thrift.message.FlattedBindRelationAggregateResp;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.Future;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class UserIdMapperProcessorTest {

    @InjectMocks
    private UserIdMapperProcessor userIdMapperProcessor;

    @Mock
    private MapperWrapper mapperWrapper;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private Future futureMock;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testProcessIsMtTrue() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx ctx = new DealCtx(envCtx);
        // act
        userIdMapperProcessor.process(ctx);
        // assert
        verify(mapperWrapper, never()).getMtCityByDpCity(any());
        verify(mapperWrapper, never()).getMtUserIdByFuture(any());
        verify(dealGroupWrapper, never()).batchMtDealGroupIdByDp(any());
    }

    @Test
    public void testPreBatchMtDealGroupIdByDp() {
        DealGroupWrapper dealGroupWrapper1 = new DealGroupWrapper();
        dealGroupWrapper1.preMtDealGroupIdByDp(411080967);
        Future futures = dealGroupWrapper1.preMtDealGroupIdByDp(-1);
        assertNull(futures);
    }

    /**
     * 测试当ctx为美团用户且用户已登录的情况
     */
    @Test
    public void testPrepare_MtUserLoggedIn() {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        ctx.getEnvCtx().setMtUserId(123L);
        ctx.setFutureCtx(new FutureCtx());
        when(mapperWrapper.preUserInfoByMtUserId(123L)).thenReturn(futureMock);
        // act
        userIdMapperProcessor.prepare(ctx);
        // assert
        assertNotNull(ctx);
    }

    /**
     * 测试当ctx为美团用户但用户未登录的情况
     */
    @Test
    public void testPrepare_MtUserNotLoggedIn() {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        ctx.getEnvCtx().setMtUserId(0L);
        ctx.setFutureCtx(new FutureCtx());
        // act
        userIdMapperProcessor.prepare(ctx);
        // assert
        assertNotNull(ctx);
    }

    /**
     * 测试当ctx为点评用户且用户已登录的情况
     */
    @Test
    public void testPrepare_DpUserLoggedIn() {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        ctx.getEnvCtx().setDpUserId(123L);
        ctx.setFutureCtx(new FutureCtx());
        when(mapperWrapper.preUserInfoByDpUserId(123L)).thenReturn(futureMock);
        // act
        userIdMapperProcessor.prepare(ctx);
        // assert
        assertNotNull(ctx);
    }

    /**
     * 测试当ctx为点评用户但用户未登录的情况
     */
    @Test
    public void testPrepare_DpUserNotLoggedIn() {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        ctx.getEnvCtx().setDpUserId(0L);
        ctx.setFutureCtx(new FutureCtx());
        // act
        userIdMapperProcessor.prepare(ctx);
        // assert
        Cat.logEvent("UserIdMapperProcessor", "dp_user_not_login");
        assertNotNull(ctx);
    }

    /**
     * 测试场景：UserIdMapperFuture为null
     */
    @Test
    public void testProcess_UserIdMapperFutureIsNull() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setFutureCtx(new FutureCtx());
        userIdMapperProcessor.process(ctx);
        assertNull(ctx.getFutureCtx().getUserIdMapperFuture());
    }

    /**
     * 测试场景：userIdMapperResponse为null
     */
    @Test
    public void testProcess_UserIdMapperResponseIsNull() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        ctx.getEnvCtx().setDpUserId(0L);
        ctx.setFutureCtx(new FutureCtx());
        ctx.getFutureCtx().setUserIdMapperFuture(mock(Future.class));
        when(mapperWrapper.getFutureResult(ctx.getFutureCtx().getUserIdMapperFuture())).thenReturn(null);
        userIdMapperProcessor.process(ctx);
        assertNotNull(ctx.getFutureCtx().getUserIdMapperFuture());
    }

    /**
     * 测试场景：userIdMapperResponse.isSuccess()为false
     */
    @Test
    public void testProcess_ResponseFail() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        ctx.getEnvCtx().setDpUserId(0L);
        ctx.setFutureCtx(new FutureCtx());
        ctx.getFutureCtx().setUserIdMapperFuture(mock(Future.class));
        FlattedBindRelationAggregateResp response = new FlattedBindRelationAggregateResp();
        response.setSuccess(false);
        when(mapperWrapper.getFutureResult(ctx.getFutureCtx().getUserIdMapperFuture())).thenReturn(response);
        userIdMapperProcessor.process(ctx);
        assertNotNull(ctx.getFutureCtx().getUserIdMapperFuture());
    }

    /**
     * 测试场景：flattedAggregateData为null
     */
    @Test
    public void testProcess_ResponseDataIsNull() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        ctx.getEnvCtx().setDpUserId(0L);
        ctx.setFutureCtx(new FutureCtx());
        ctx.getFutureCtx().setUserIdMapperFuture(mock(Future.class));
        FlattedBindRelationAggregateResp response = new FlattedBindRelationAggregateResp();
        response.setSuccess(true);
        when(mapperWrapper.getFutureResult(ctx.getFutureCtx().getUserIdMapperFuture())).thenReturn(response);
        userIdMapperProcessor.process(ctx);
        assertNotNull(ctx.getFutureCtx().getUserIdMapperFuture());
    }

    /**
     * 测试场景：flattedAggregateData为null
     */
    @Test
    public void testProcess_ResponseDataDp() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        ctx.getEnvCtx().setDpUserId(0L);
        ctx.setFutureCtx(new FutureCtx());
        ctx.getFutureCtx().setUserIdMapperFuture(mock(Future.class));
        FlattedBindRelationAggregateResp response = new FlattedBindRelationAggregateResp();
        FlattedBindRelation flattedBindRelation = new FlattedBindRelation();
        flattedBindRelation.setTargetRealUserId(123);
        response.setFlattedAggregateData(flattedBindRelation);
        response.setSuccess(true);
        when(mapperWrapper.getFutureResult(ctx.getFutureCtx().getUserIdMapperFuture())).thenReturn(response);
        userIdMapperProcessor.process(ctx);
        assertNotNull(ctx.getFutureCtx().getUserIdMapperFuture());
    }

    /**
     * 测试场景：flattedAggregateData为null
     */
    @Test
    public void testProcess_ResponseDataMt() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        ctx.getEnvCtx().setDpUserId(0L);
        ctx.setFutureCtx(new FutureCtx());
        ctx.getFutureCtx().setUserIdMapperFuture(mock(Future.class));
        FlattedBindRelationAggregateResp response = new FlattedBindRelationAggregateResp();
        FlattedBindRelation flattedBindRelation = new FlattedBindRelation();
        flattedBindRelation.setMtRealUserId(123);
        response.setFlattedAggregateData(flattedBindRelation);
        response.setSuccess(true);
        when(mapperWrapper.getFutureResult(ctx.getFutureCtx().getUserIdMapperFuture())).thenReturn(response);
        userIdMapperProcessor.process(ctx);
        assertNotNull(ctx.getFutureCtx().getUserIdMapperFuture());
    }

}
