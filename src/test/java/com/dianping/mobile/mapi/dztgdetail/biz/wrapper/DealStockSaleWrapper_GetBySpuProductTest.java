package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.sales.common.datatype.KeyParam;
import com.dianping.deal.sales.common.datatype.SpuSale;
import com.dianping.deal.volume.query.api.AccumSaleQueryService;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.concurrent.Future;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DealStockSaleWrapper_GetBySpuProductTest {

    @InjectMocks
    private DealStockSaleWrapper dealStockSaleWrapper;

    @Mock
    private AccumSaleQueryService accumulateSaleQueryServiceFuture;

    @Mock
    private Future mockFuture;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 getBySpuProduct 方法，正常情况
     */
    @Test
    public void testGetBySpuProductNormal() throws Throwable {
        try (MockedStatic<FutureFactory> mockedStatic = mockStatic(FutureFactory.class)) {
            // arrange
            int dpId = 1;
            SpuSale spuSale = new SpuSale();
            when(accumulateSaleQueryServiceFuture.getBySpuProduct(any(KeyParam.class), anyInt(), anyInt(), anyInt())).thenReturn(spuSale);
            mockedStatic.when(FutureFactory::getFuture).thenReturn(mockFuture);
            // act
            Future result = dealStockSaleWrapper.getBySpuProduct(dpId);
            // assert
            verify(accumulateSaleQueryServiceFuture, times(1)).getBySpuProduct(any(KeyParam.class), anyInt(), anyInt(), anyInt());
            assertNotNull(result);
            assertEquals(mockFuture, result);
        }
    }

    /**
     * 测试 getBySpuProduct 方法，异常情况
     */
    @Test
    public void testGetBySpuProductException() throws Throwable {
        // arrange
        int dpId = 1;
        when(accumulateSaleQueryServiceFuture.getBySpuProduct(any(KeyParam.class), anyInt(), anyInt(), anyInt())).thenThrow(new RuntimeException());
        // act
        Future result = dealStockSaleWrapper.getBySpuProduct(dpId);
        // assert
        verify(accumulateSaleQueryServiceFuture, times(1)).getBySpuProduct(any(KeyParam.class), anyInt(), anyInt(), anyInt());
        assertNull(result);
    }
}
