package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CouponTypeEnum;
import java.util.Arrays;
import java.util.Collections;
import com.sankuai.dealuser.price.display.api.enums.PromotionExplanatoryTagEnum;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;

public class ParallDealBuilderProcessor_GetCouponTypeTest {

    /**
     * Tests getCouponType method when promotionExplanatoryTags contains the code for MAGICAL_MEMBER_COUPON.
     */
    @Test
    public void testGetCouponTypeContainsMagicMemberCoupon() throws Throwable {
        // Arrange
        int expected = CouponTypeEnum.PAY_COUPON.getCode();
        // Act
        int actual = ParallDealBuilderProcessor.getCouponType(Arrays.asList(PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON.getCode()));
        // Assert
        Assert.assertEquals(expected, actual);
    }

    /**
     * Tests getCouponType method when promotionExplanatoryTags contains the code for MAGICAL_MEMBER_COUPON_FREE.
     */
    @Test
    public void testGetCouponTypeContainsMagicMemberCouponFree() throws Throwable {
        // Arrange
        int expected = CouponTypeEnum.FREE_COUPON.getCode();
        // Act
        int actual = ParallDealBuilderProcessor.getCouponType(Arrays.asList(PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON_FREE.getCode()));
        // Assert
        Assert.assertEquals(expected, actual);
    }

    /**
     * Tests getCouponType method when promotionExplanatoryTags does not contain the codes for MAGICAL_MEMBER_COUPON or MAGICAL_MEMBER_COUPON_FREE.
     */
    @Test
    public void testGetCouponTypeNotContainsMagicMemberCouponOrMagicMemberCouponFree() throws Throwable {
        // Arrange
        int expected = CouponTypeEnum.UN_KNOWN.getCode();
        // Act
        int actual = ParallDealBuilderProcessor.getCouponType(Collections.emptyList());
        // Assert
        Assert.assertEquals(expected, actual);
    }

    /**
     * Tests getCouponType method when promotionExplanatoryTags is empty.
     * Adjusted to pass an empty list instead of null to avoid NullPointerException.
     */
    @Test
    public void testGetCouponTypeEmpty() throws Throwable {
        // Arrange
        int expected = CouponTypeEnum.UN_KNOWN.getCode();
        // Act
        int actual = ParallDealBuilderProcessor.getCouponType(Collections.emptyList());
        // Assert
        Assert.assertEquals(expected, actual);
    }
}
