package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.Pair;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor;
import com.dianping.mobile.mapi.dztgdetail.helper.ReassuredRepairHelp;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertNotNull;

@RunWith(MockitoJUnitRunner.class)
public class ReassuredRepairHelpTest {

    @InjectMocks
    private ReassuredRepairHelp reassuredRepairHelp;


    @Test
    public void testBuildHomeRenovationReminders() throws Exception{
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        ctx.setDealGroupDTO(dealGroupDTO);
        // 创建Pair对象，模拟“购买须知”
        Pair pair = new Pair();
        pair.setId("购买须知");
        pair.setName("<div> <div class=\"detail-box\"> <div class=\"purchase-notes\"> <dl> <dt>有效期</dt> <dd> <p class=\"listitem\"> 购买后90天内有效 </p> </dd> </dl> <dl> <dt>预约信息</dt> <dd> <p class=\"listitem\">请您提前1天预约 </p> </dd> </dl> <dl> <dt>规则提醒</dt> <dd> <p class=\"listitem\">不再与其他优惠同享 </p> </dd> </dl> <dl> <dt>温馨提示</dt> <dd> <p class=\"listitem\">如需团购券发票，请您在消费时向商户咨询</p> <p class=\"listitem\">为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！</p> </dd> </dl> </div> </div> </div>");
        DealGroupServiceProjectDTO dealGroupServiceProjectDTO = new DealGroupServiceProjectDTO();
        List<MustServiceProjectGroupDTO> mustGroups = new ArrayList<>();
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        List<ServiceProjectDTO> groups = new ArrayList<>();
        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        List<ServiceProjectAttrDTO> attrs = new ArrayList<>();
        ServiceProjectAttrDTO attr = new ServiceProjectAttrDTO();
        attr.setAttrName("qizhuangxianzhi");
        attr.setAttrValue("是");
        attrs.add(attr);
        ServiceProjectAttrDTO attr1 = new ServiceProjectAttrDTO();
        attr1.setAttrName("qizhuangxianzhi2");
        attr1.setAttrValue("20");
        attrs.add(attr1);
        ServiceProjectAttrDTO attr2 = new ServiceProjectAttrDTO();
        attr2.setAttrName("priceunit");
        attr2.setAttrValue("㎡");
        attrs.add(attr2);
        ServiceProjectAttrDTO attr3 = new ServiceProjectAttrDTO();
        attr3.setAttrName("zhichiyufujinketui");
        attr3.setAttrValue("是");
        attrs.add(attr3);

        serviceProjectDTO.setAttrs(attrs);
        groups.add(serviceProjectDTO);
        mustGroup.setGroups(groups);
        mustGroups.add(mustGroup);
        dealGroupServiceProjectDTO.setMustGroups(mustGroups);
        dealGroupDTO.setServiceProject(dealGroupServiceProjectDTO);

        // 创建标签
        DealGroupTagDTO tag = new DealGroupTagDTO();
        tag.setId(100218044L); // 假设这是你要检查的标签ID

        // 创建标签列表并添加标签
        List<DealGroupTagDTO> tags = new ArrayList<>();
        tags.add(tag);

        // 创建Pair列表并添加Pair对象
        List<Pair> pairs = new ArrayList<>();
        pairs.add(pair);
        ctx.setStructedDetails(pairs);
        dealGroupDTO.setTags(tags);
        DealGroupPBO dealGroupPBO = new DealGroupPBO();
        List<String> reminders = new ArrayList<>();
        List<String> reassuredRepairReminders = ReassuredRepairHelp.getReassuredRepairReminders(ctx, reminders);
        assertNotNull(reassuredRepairReminders);
    }


}
