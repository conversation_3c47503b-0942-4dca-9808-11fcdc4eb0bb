package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.impl;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryAbInfo;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryDealModuleInfo;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryParam;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * @author: created by hang.yu on 2024/4/25 15:10
 */
@RunWith(MockitoJUnitRunner.class)
public class ElectricVehicleCategoryStrategyImplTest {

    @InjectMocks
    private ElectricVehicleCategoryStrategyImpl electricVehicleCategoryStrategy;

    @Test
    public void buildAbInfo() {
        DealCategoryAbInfo result = electricVehicleCategoryStrategy.buildAbInfo();
        Assert.assertNull(result);
    }

    @Test
    public void buildDealModuleInfo() {
        DealCategoryDealModuleInfo result = electricVehicleCategoryStrategy.buildDealModuleInfo();
        Assert.assertNotNull(result);
    }

    @Test
    public void customNewDealStyle() {
        DealCategoryParam dealCategoryParam = DealCategoryParam.builder().build();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("service_type");
        attrDTO.setValue(Lists.newArrayList("电动车购车代金券"));
        dealGroupDTO.setAttrs(Lists.newArrayList(attrDTO));
        dealCategoryParam.setDealGroupDTO(dealGroupDTO);
        boolean b = electricVehicleCategoryStrategy.customNewDealStyle(dealCategoryParam);
        Assert.assertTrue(b);
    }

    @Test
    public void newDealStyle(){
        boolean result = electricVehicleCategoryStrategy.newDealStyle(null);
        Assert.assertFalse(result);

        DealCategoryParam param = DealCategoryParam.builder().build();
        result = electricVehicleCategoryStrategy.newDealStyle(param);
        Assert.assertFalse(result);

        param.setDealGroupDTO(new DealGroupDTO());
        result = electricVehicleCategoryStrategy.newDealStyle(param);
        Assert.assertFalse(result);

        param.setEnvCtx(new EnvCtx());
        result = electricVehicleCategoryStrategy.newDealStyle(param);
        Assert.assertFalse(result);


        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        param.setEnvCtx(envCtx);
        result = electricVehicleCategoryStrategy.newDealStyle(param);
        Assert.assertFalse(result);
    }

    @Test
    public void getSimilarDealModuleAbConfig() {
        ModuleAbConfig result = electricVehicleCategoryStrategy.getSimilarDealModuleAbConfig(null);
        Assert.assertNull(result);
    }

}