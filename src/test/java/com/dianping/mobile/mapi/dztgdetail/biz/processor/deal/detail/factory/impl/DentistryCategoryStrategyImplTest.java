package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.impl;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryAbInfo;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryDealModuleInfo;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DentistryCategoryStrategyImplTest {

    @InjectMocks
    private DentistryCategoryStrategyImpl dentistryCategoryStrategy;

    @Mock
    private DouHuBiz douHuBiz;

    @Test
    public void testBuildAbInfo() {
        DealCategoryAbInfo result = dentistryCategoryStrategy.buildAbInfo();
        Assert.assertNull(result);
    }

    @Test
    public void testBuildDealModuleInfo() {
        DealCategoryDealModuleInfo result = dentistryCategoryStrategy.buildDealModuleInfo();
        Assert.assertNull(result);
    }

    @Test
    public void testCustomNewDealStyle() {
        boolean result = dentistryCategoryStrategy.customNewDealStyle(null);
        Assert.assertFalse(result);
    }

    @Test
    public void testGetTimesDealModuleAbConfig() {
        ModuleAbConfig result = dentistryCategoryStrategy.getTimesDealModuleAbConfig(null);
        Assert.assertNull(result);
    }

    @Test
    public void getSimilarDealModuleAbConfig() {
        when(douHuBiz.getAbExpResult(any(EnvCtx.class), anyString()))
                .thenReturn(new ModuleAbConfig());
        ModuleAbConfig result = dentistryCategoryStrategy.getSimilarDealModuleAbConfig(new EnvCtx());
        Assert.assertNotNull(result);
    }
}
