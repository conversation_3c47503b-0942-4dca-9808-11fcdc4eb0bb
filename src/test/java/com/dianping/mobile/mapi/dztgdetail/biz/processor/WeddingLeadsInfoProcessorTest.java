package com.dianping.mobile.mapi.dztgdetail.biz.processor;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealBookWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.sankuai.clr.content.process.thrift.dto.ShopBookInfoProcessDTO;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * @author: zhangyuan103
 * @create: 2025-03-12
 * @description:
 */
@RunWith(MockitoJUnitRunner.class)
public class WeddingLeadsInfoProcessorTest {
    @InjectMocks
    private WeddingLeadsInfoProcessor weddingLeadsInfoProcessor;

    @Mock
    private DealBookWrapper dealBookWrapper;

    private MockedStatic<DealUtils> mockedDealUtils;
    @Before
    public void setUp() {
        mockedDealUtils = Mockito.mockStatic(DealUtils.class);
    }

    @After
    public void after() {
        mockedDealUtils.close();
    }

    @Test
    public void tesIsEnable() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        mockedDealUtils.when(() -> DealUtils.isWeddingLeadsDeal((DealCtx) any())).thenReturn(true);
        assertTrue(weddingLeadsInfoProcessor.isEnable(ctx));
    }

    @Test
    public void testProcessNull1() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setFutureCtx(null);
        // act
        weddingLeadsInfoProcessor.process(ctx);
        // assert
        assertFalse(ctx.isHasBookBenefit());
    }

    @Test
    public void testProcessNormal() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        CompletableFuture future = CompletableFuture.completedFuture(null);
        ctx.getFutureCtx().setShopBookInfoFuture(future);
        ShopBookInfoProcessDTO shopBookInfoProcessDTO = new ShopBookInfoProcessDTO();
        shopBookInfoProcessDTO.setStatus(1);
        when(dealBookWrapper.queryShopBookInfo(any())).thenReturn(shopBookInfoProcessDTO);
        // act
        weddingLeadsInfoProcessor.process(ctx);
        // assert
        assertTrue(ctx.isHasBookBenefit());
    }

    @Test
    public void testProcessNull2() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        CompletableFuture future = CompletableFuture.completedFuture(null);
        ctx.getFutureCtx().setShopBookInfoFuture(future);
        when(dealBookWrapper.queryShopBookInfo(any())).thenReturn(null);
        // act
        weddingLeadsInfoProcessor.process(ctx);
        // assert
        assertFalse(ctx.isHasBookBenefit());
    }
}
