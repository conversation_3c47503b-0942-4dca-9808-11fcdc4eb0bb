package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.publishcategory.DealGroupPublishCategoryQueryService;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DzImWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.SkuWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Cons;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealIMReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedDealsReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupImVo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedDeals;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.utils.FaultToleranceUtils;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DealQueryFacadeTest {

    @InjectMocks
    private DealQueryFacade dealQueryFacade;
    @Mock
    private DzImWrapper dzImWrapper;

    @Mock
    private SkuWrapper skuWrapper;


    private DealBaseReq dealBaseReq;

    private DealCtx dealCtx;

    private DealGroupDTO dealGroupDTO;

    private DealGroupBaseDTO dealGroupBase;

    @Mock
    private DealGroupPublishCategoryQueryService dealGroupPublishCategoryQueryService;

    private MockedStatic<FaultToleranceUtils> faultToleranceUtilsMockedStatic;

    @Before
    public void setUp() {
        dealBaseReq = new DealBaseReq();
        // 设置默认的dealgroupid值
        dealBaseReq.setDealgroupid(1);
        EnvCtx envCtx = new EnvCtx();
        dealCtx = new DealCtx(envCtx);
        dealGroupDTO = mock(DealGroupDTO.class);
        dealGroupBase = mock(DealGroupBaseDTO.class);
        faultToleranceUtilsMockedStatic = mockStatic(FaultToleranceUtils.class);
        DouHuService douHuService = new DouHuService();
        DouHuBiz douHuBiz = new DouHuBiz();
        try {
            // 获取对象的Class对象
            Class<?> clazz = dealQueryFacade.getClass();
            Class<?> dhclazz = douHuService.getClass();
            // 获取指定属性名的Field对象
            Field field = clazz.getDeclaredField("douHuService");
            Field dhfield = dhclazz.getDeclaredField("douHuBiz");
            // 如果属性是私有的，我们需要取消Java的访问检查
            // 这样才能对私有属性进行操作
            field.setAccessible(true);
            dhfield.setAccessible(true);
            // 给属性设置新的值
            field.set(dealQueryFacade, douHuService);
            dhfield.set(douHuService, douHuBiz);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
        }
    }

    @After
    public void tearDown() {
        faultToleranceUtilsMockedStatic.close();
    }

    private DealIMReq createRequest() {
        DealIMReq request = new DealIMReq();
        request.setShopidstr("1");
        request.setDealgroupid(1);
        return request;
    }

    private EnvCtx createEnvCtx(boolean isMt) {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(isMt ? com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum.MEITUAN_APP.getCode() : com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum.DIANPING_APP.getCode());
        return envCtx;
    }

    @Test
    public void testInitDealsBaseData() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setMLiveId(1);
        ctx.setDealGroupDTO(dealGroupDTO);
        DealBaseReq req = new DealBaseReq();
        req.setPoiidStr("1");
        req.setDealgroupid(1);
        req.setPass_param("{\"PROMOTE_CHANNEL_INFO\":\"{\\\"promoteType\\\":\\\"m_live\\\",\\\"promoteExtend\\\":\\\"{\\\\\\\"mLiveId\\\\\\\":5046629,\\\\\\\"influencerId\\\\\\\":29905,\\\\\\\"mLiveExtend\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"mlid\\\\\\\\\\\\\\\":5046629,\\\\\\\\\\\\\\\"tid\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"074f57e23e4446ef89a6bbd2dd312327\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"anchor_id\\\\\\\\\\\\\\\":29905,\\\\\\\\\\\\\\\"seckill_activity_id\\\\\\\\\\\\\\\":null,\\\\\\\\\\\\\\\"live_status\\\\\\\\\\\\\\\":null}\\\\\\\"}\\\"}\",\"commissionFree\":\"50001\"}");
        DealCtx returnCtx = dealQueryFacade.initDealsBaseData(req, envCtx);
        Assert.assertTrue(returnCtx.getMLiveInfoVo().getMLiveId() == 5046629L);
    }

    @Test
    public void testCompareSameShopPriceStyleAb() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setMLiveId(1);
        ctx.setDealGroupDTO(dealGroupDTO);
        DealBaseReq req = new DealBaseReq();
        req.setPoiidStr("1");
        req.setDealgroupid(1);
        req.setPass_param("{\"PROMOTE_CHANNEL_INFO\":\"{\\\"promoteType\\\":\\\"m_live\\\",\\\"promoteExtend\\\":\\\"{\\\\\\\"mLiveId\\\\\\\":5046629,\\\\\\\"influencerId\\\\\\\":29905,\\\\\\\"mLiveExtend\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"mlid\\\\\\\\\\\\\\\":5046629,\\\\\\\\\\\\\\\"tid\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"074f57e23e4446ef89a6bbd2dd312327\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"anchor_id\\\\\\\\\\\\\\\":29905,\\\\\\\\\\\\\\\"seckill_activity_id\\\\\\\\\\\\\\\":null,\\\\\\\\\\\\\\\"live_status\\\\\\\\\\\\\\\":null}\\\\\\\"}\\\"}\",\"commissionFree\":\"50001\"}");
        DealCtx returnCtx = dealQueryFacade.initDealsBaseData(req, envCtx);
        RelatedDeals deals = new RelatedDeals();
        deals.setGeneralInfo(Cons.CARD_STYLE);
        dealQueryFacade.buildCompareSameShopPriceStyleAb(envCtx, deals, 501);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        List<AbConfig> configs = Lists.newArrayList();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("a");
        configs.add(abConfig);
        moduleAbConfig.setConfigs(configs);
        dealQueryFacade.buildCompareSameShopPriceStyleAb(envCtx, deals, 501);
        Assert.assertTrue(returnCtx.getMLiveInfoVo().getMLiveId() == 5046629L);
    }

    @Test
    public void testQueryRelatedDealsEnvCtxVersionNotSatisfy() throws Throwable {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setVersion("1.0.0");
        RelatedDealsReq request = new RelatedDealsReq();
        RelatedDeals result = dealQueryFacade.queryRelatedDeals(request, envCtx);
        assertNull(result);
    }

    @Test
    public void testQueryRelatedDealsBaseDataInitFailed() throws Throwable {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setVersion("2.0.0");
        RelatedDealsReq request = new RelatedDealsReq();
        RelatedDeals result = dealQueryFacade.queryRelatedDeals(request, envCtx);
        assertNull(result);
    }

    @Test
    public void testQueryRelatedDealsRelateDealsIsNull() throws Throwable {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setVersion("2.0.0");
        RelatedDealsReq request = new RelatedDealsReq();
        RelatedDeals result = dealQueryFacade.queryRelatedDeals(request, envCtx);
        assertNull(result);
    }

    @Test
    public void testQueryRelatedDealsDealTabHolderIsNull() throws Throwable {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setVersion("2.0.0");
        RelatedDealsReq request = new RelatedDealsReq();
        RelatedDeals result = dealQueryFacade.queryRelatedDeals(request, envCtx);
        assertNull(result);
    }

    @Test
    public void testQueryDealGroupIMWhenDpDealGroupIdOrDpShopIdIsLessThanOrEqualToZero() throws Throwable {
        DealIMReq request = createRequest();
        request.setDealgroupid(0);
        EnvCtx envCtx = createEnvCtx(true);
        DealGroupImVo result = dealQueryFacade.queryDealGroupIM(request, envCtx);
        assertNotNull(result);
        assertNull(result.getImUrl());
    }

    @Test
    public void testQueryDealGroupIMWhenChannelDoesNotHaveImFunction() throws Throwable {
        DealIMReq request = createRequest();
        request.setDealgroupid(1);
        EnvCtx envCtx = createEnvCtx(true);
        when(dealGroupPublishCategoryQueryService.getDealGroupChannelById(anyInt())).thenReturn(new DealGroupChannelDTO());
        DealGroupImVo result = dealQueryFacade.queryDealGroupIM(request, envCtx);
        assertNotNull(result);
        assertNull(result.getImUrl());
    }

    @Test
    public void testQueryDealGroupIMWhenGetDealGroupChannelByIdThrowsException() throws Throwable {
        DealIMReq request = createRequest();
        request.setDealgroupid(1);
        EnvCtx envCtx = createEnvCtx(true);

        faultToleranceUtilsMockedStatic.when(() -> FaultToleranceUtils.addException(anyString(), any()));
        DealGroupImVo result = dealQueryFacade.queryDealGroupIM(request, envCtx);
        assertNotNull(result);
    }
}
