package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.mobile.mapi.dztgdetail.common.enums.MtTerm;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.List;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class DealFieldHelper_GenMtTermListFromTermStrTest {

    /**
     * 测试 genMtTermListFromTermStr 方法，当 terms 为空或者只包含空格时，应返回一个空的 MtTerm 列表
     */
    @Test
    public void testGenMtTermListFromTermStr_EmptyOrBlank() {
        // arrange
        String terms = " ";
        // act
        List<MtTerm> result = DealFieldHelper.genMtTermListFromTermStr(terms);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 genMtTermListFromTermStr 方法，当 terms 不是一个有效的 JSONArray 字符串时，应返回一个空的 MtTerm 列表
     */
    @Test
    public void testGenMtTermListFromTermStr_InvalidJsonArray() {
        // arrange
        String terms = "{invalid json}";
        // act
        List<MtTerm> result = DealFieldHelper.genMtTermListFromTermStr(terms);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 genMtTermListFromTermStr 方法，当 terms 是一个有效的 JSONArray 字符串，但是其中的 JSONObject 不是一个有效的 MtTerm 对象时，应返回一个空的 MtTerm 列表
     */
    @Test
    public void testGenMtTermListFromTermStr_InvalidMtTerm() {
        // arrange
        String terms = "[{\"title\":\"title\",\"content\":\"content\"}]";
        // act
        List<MtTerm> result = DealFieldHelper.genMtTermListFromTermStr(terms);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 genMtTermListFromTermStr 方法，当 terms 是一个有效的 JSONArray 字符串，其中的 JSONObject 是一个有效的 MtTerm 对象时，应返回一个包含一个 MtTerm 对象的列表
     */
    @Test
    public void testGenMtTermListFromTermStr_ValidMtTerm() {
        // arrange
        String terms = "[{\"title\":\"title\",\"content\":[\"content1\",\"content2\"]}]";
        // act
        List<MtTerm> result = DealFieldHelper.genMtTermListFromTermStr(terms);
        // assert
        assertEquals(1, result.size());
        assertEquals("title", result.get(0).getTitle());
        assertEquals(2, result.get(0).getContents().size());
        assertEquals("content1", result.get(0).getContents().get(0));
        assertEquals("content2", result.get(0).getContents().get(1));
    }
}
