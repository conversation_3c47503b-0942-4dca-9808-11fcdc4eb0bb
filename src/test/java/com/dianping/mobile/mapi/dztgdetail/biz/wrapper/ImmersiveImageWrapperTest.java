package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ExhibitImageItemVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageFilterVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.nailstyle.NailStyleItemVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.ExhibitContentDTO;
import com.dianping.mobile.mapi.dztgdetail.entity.*;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.metadataeav.process.api.metadatamodel.MetaDataModelLoadService;
import com.sankuai.metadataeav.process.dto.metadatamodel.LoadMetaDataModelRespDTO;
import com.sankuai.metadataeav.process.dto.metadatamodel.MetaDataAttributeDTO;
import com.sankuai.metadataeav.process.dto.metadatamodel.MetaDataAttributeOptionDTO;
import com.sankuai.metadataeav.process.dto.metadatamodel.MetaDataModelDTO;
import com.sankuai.mpmctcontent.application.thrift.api.content.DealDetailPageGWService;
import com.sankuai.mpmctcontent.application.thrift.dto.content.DisplayItem;
import com.sankuai.mpmctcontent.application.thrift.dto.content.ListItemDTO;
import com.sankuai.mpmctcontent.application.thrift.dto.content.LoadClassicContentFilterRespDTO;
import com.sankuai.mpmctcontent.application.thrift.dto.content.LoadDealRelatedCaseListRespDTO;
import com.sankuai.mpmctcontent.application.thrift.dto.content.TagDescDTO;
import com.sankuai.mpmctcontent.query.thrift.api.ContentFusion2CService;
import com.sankuai.mpmctcontent.query.thrift.api.search.ContentSearchService;
import com.sankuai.mpmctcontent.query.thrift.dto.aggre.ContentFusionDetailDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.aggre.SearchFusionInfoReqDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.aggre.SearchFusionInfoRespDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.meta.BatchQueryTagValueResponseDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.search.SearchDataDetailDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.search.SearchDetailResponseDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.search.SearchRequestDTO;
import com.sankuai.mpmctmvacommon.resource.response.CommonRespDTO;
import com.sankuai.mpmctmvacommon.resource.response.constant.CommonRespCodeEnum;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ImmersiveImageWrapperTest {

    @InjectMocks
    private ImmersiveImageWrapper immersiveImageWrapper;

    @Mock
    private MetaDataModelLoadService metaDataModelLoadService;

    @Mock
    private ContentFusion2CService contentFusion2CService;

    @Mock
    private DealDetailPageGWService dealDetailPageGWServiceFuture;

    @Mock
    private ContentSearchService contentSearchService;

    private MockedStatic<LionConfigUtils> lionConfigUtilsMocked;

    private MockedStatic<Lion> lionMocked;

    private MockedStatic<ContextStore> contextStoreMocked;

    private ImmersiveImageWrapper wrapper = new ImmersiveImageWrapper();

    @Mock
    private Future<LoadClassicContentFilterRespDTO> future;

    @Mock
    private ListItemDTO item;

    @Mock
    private DealGroupDealDTO deal;

    @Mock
    private ExhibitImageConfig config;

    @Mock
    private HaimaWrapper haimaWrapper;

    @Before
    public void setUp() {
        lionConfigUtilsMocked = mockStatic(LionConfigUtils.class);
        contextStoreMocked = mockStatic(ContextStore.class);
        lionMocked = mockStatic(Lion.class);
    }

    @After
    public void teardown() {
        lionConfigUtilsMocked.close();
        contextStoreMocked.close();
        lionMocked.close();
    }

    @Test
    public void testGetStyleMetaData_Normal() {
        // arrange
        List<String> tagFields = Arrays.asList("theme", "color");
        LoadMetaDataModelRespDTO respDTO = new LoadMetaDataModelRespDTO();
        MetaDataModelDTO metaDataModelDTO = new MetaDataModelDTO();
        MetaDataAttributeDTO metaDataAttributeDTO1 = new MetaDataAttributeDTO();
        metaDataAttributeDTO1.setId("1");
        metaDataAttributeDTO1.setName("theme");
        MetaDataAttributeDTO metaDataAttributeDTO2 = new MetaDataAttributeDTO();
        metaDataAttributeDTO2.setId("2");
        metaDataAttributeDTO2.setName("color");
        MetaDataAttributeOptionDTO metaDataAttributeOptionDTO1 = new MetaDataAttributeOptionDTO();
        metaDataAttributeOptionDTO1.setAttributeId(1);
        metaDataAttributeOptionDTO1.setValue(2023);
        metaDataAttributeOptionDTO1.setLabel("秋冬美甲");
        MetaDataAttributeOptionDTO metaDataAttributeOptionDTO2 = new MetaDataAttributeOptionDTO();
        metaDataAttributeOptionDTO2.setAttributeId(2);
        metaDataAttributeOptionDTO2.setValue(2020);
        metaDataAttributeOptionDTO2.setLabel("红色");
        metaDataModelDTO.setAttributeList(Arrays.asList(metaDataAttributeDTO1, metaDataAttributeDTO2));
        metaDataModelDTO.setAttributeOptionList(Arrays.asList(metaDataAttributeOptionDTO1, metaDataAttributeOptionDTO2));
        respDTO.setData(metaDataModelDTO);
        when(metaDataModelLoadService.loadMetaDataModel(any())).thenReturn(respDTO);
        // act
        Map<Integer, String> result = immersiveImageWrapper.getStyleMetaData(tagFields);
        // assert
        assertEquals(2, result.size());
        assertEquals("秋冬美甲", result.get(2023));
        assertEquals("红色", result.get(2020));
    }

    @Test
    public void testGetStyleMetaData_EmptyTagFields() {
        // arrange
        List<String> tagFields = Collections.emptyList();
        // act
        Map<Integer, String> result = immersiveImageWrapper.getStyleMetaData(tagFields);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试itemIds为空的情况
     */
    @Test
    public void testPreBatchGetStyleImageWithEmptyItemIds() throws Throwable {
        // arrange
        String idType = "contentId";
        Integer categoryId = 1;
        // act
        Future result = immersiveImageWrapper.preBatchGetStyleImage(null, idType, categoryId);
        // assert
        assertNull(result);
    }

    /**
     * 测试ExhibitImageConfig对象为null的情况
     */
    @Test
    public void testPreBatchGetStyleImageWithNullExhibitImageConfig() throws Throwable {
        // arrange
        String idType = "contentId";
        Integer categoryId = null;
        // act
        Future result = immersiveImageWrapper.preBatchGetStyleImage(Arrays.asList("1"), idType, categoryId);
        // assert
        assertNull(result);
    }

    /**
     * 测试contentFusion2CService.searchFusionInfo(request)方法执行成功的情况
     */
    @Test
    public void testPreBatchGetStyleImageWithSuccess() throws Throwable {
        // arrange
        String idType = "contentId";
        Integer categoryId = 1;
        lionConfigUtilsMocked.when(() -> LionConfigUtils.getRecommendExhibitImageConfig(anyInt())).thenReturn(new ExhibitImageConfig());
        Future mock = mock(Future.class);
        contextStoreMocked.when(ContextStore::getFuture).thenReturn(mock);
        // act
        Future result = immersiveImageWrapper.preBatchGetStyleImage(Arrays.asList("1"), idType, categoryId);
        assertNotNull(result);
    }

    /**
     * 测试contentFusion2CService.searchFusionInfo(request)方法执行时抛出异常的情况
     */
    @Test
    public void testPreBatchGetStyleImageWithException() throws Throwable {
        // arrange
        String idType = "contentId";
        Integer categoryId = 1;
        lionConfigUtilsMocked.when(() -> LionConfigUtils.getRecommendExhibitImageConfig(anyInt())).thenReturn(new ExhibitImageConfig());
        doThrow(new RuntimeException()).when(contentFusion2CService).searchFusionInfo(any(SearchFusionInfoReqDTO.class));
        // act
        Future result = immersiveImageWrapper.preBatchGetStyleImage(Arrays.asList("1"), idType, categoryId);
        // assert
        assertNull(result);
        verify(contentFusion2CService, times(1)).searchFusionInfo(any(SearchFusionInfoReqDTO.class));
    }

    @Test
    public void testGetContentFusionDetailByIdWhenListIsNull() {
        ImmersiveImageWrapper wrapper = new ImmersiveImageWrapper();
        ImmersiveImageWrapper.ContentFusion result = wrapper.getContentFusionDetailById("1", "oldId", null);
        assertNull(result);
    }

    @Test
    public void testGetContentFusionDetailByIdWhenContentInfoIsNull() {
        ImmersiveImageWrapper wrapper = new ImmersiveImageWrapper();
        ImmersiveImageWrapper.ContentFusion contentFusion = new ImmersiveImageWrapper.ContentFusion();
        ImmersiveImageWrapper.ContentFusion result = wrapper.getContentFusionDetailById("1", "oldId", Collections.singletonList(contentFusion));
        assertNull(result);
    }

    @Test
    public void testGetContentFusionDetailByIdWhenNoMatchId() {
        ImmersiveImageWrapper wrapper = new ImmersiveImageWrapper();
        ImmersiveImageWrapper.ContentFusion contentFusion = new ImmersiveImageWrapper.ContentFusion();
        ImmersiveImageWrapper.ContentInfo contentInfo = new ImmersiveImageWrapper.ContentInfo();
        contentInfo.setOldId(2L);
        contentFusion.setContentInfo(contentInfo);
        ImmersiveImageWrapper.ContentFusion result = wrapper.getContentFusionDetailById("1", "oldId", Collections.singletonList(contentFusion));
        assertNull(result);
    }

    @Test
    public void testGetContentFusionDetailByIdWhenMatchOldId() {
        ImmersiveImageWrapper wrapper = new ImmersiveImageWrapper();
        ImmersiveImageWrapper.ContentFusion contentFusion = new ImmersiveImageWrapper.ContentFusion();
        ImmersiveImageWrapper.ContentInfo contentInfo = new ImmersiveImageWrapper.ContentInfo();
        contentInfo.setOldId(1L);
        contentFusion.setContentInfo(contentInfo);
        ImmersiveImageWrapper.ContentFusion result = wrapper.getContentFusionDetailById("1", "oldId", Collections.singletonList(contentFusion));
        assertEquals(contentFusion, result);
    }

    @Test
    public void testGetContentFusionDetailByIdWhenMatchContentId() {
        ImmersiveImageWrapper wrapper = new ImmersiveImageWrapper();
        ImmersiveImageWrapper.ContentFusion contentFusion = new ImmersiveImageWrapper.ContentFusion();
        ImmersiveImageWrapper.ContentInfo contentInfo = new ImmersiveImageWrapper.ContentInfo();
        contentInfo.setContentId(1L);
        contentFusion.setContentInfo(contentInfo);
        ImmersiveImageWrapper.ContentFusion result = wrapper.getContentFusionDetailById("1", "contentId", Collections.singletonList(contentFusion));
        assertEquals(contentFusion, result);
    }

    /**
     * 测试 batchGetRecommendStyleImageForOrder 方法，当 itemIds 为空时，应返回 null
     */
    @Test
    public void testBatchGetRecommendStyleImageForOrderWithEmptyItemIds() throws Throwable {
        // arrange
        List<String> itemIds = Collections.emptyList();
        String idType = "oldId";
        Integer categoryId = 1;
        Long dpDealGroupId = 1L;
        int display = 1;
        // act
        List<NailStyleItemVO> result = immersiveImageWrapper.batchGetRecommendStyleImageForOrder(itemIds, idType, categoryId, dpDealGroupId, display);
        // assert
        assertNull(result);
    }

    /**
     * 测试 batchGetRecommendStyleImageForOrder 方法，当 contentFusion2CService.searchFusionInfo 抛出异常时，应返回 null
     */
    @Test
    public void testBatchGetRecommendStyleImageForOrderWithException() throws Throwable {
        // arrange
        List<String> itemIds = Arrays.asList("1", "2", "3");
        String idType = "oldId";
        Integer categoryId = 1;
        Long dpDealGroupId = 1L;
        int display = 1;
        // act
        List<NailStyleItemVO> result = immersiveImageWrapper.batchGetRecommendStyleImageForOrder(itemIds, idType, categoryId, dpDealGroupId, display);
        // assert
        assertNull(result);
    }

    /**
     * 测试 batchGetRecommendStyleImageForOrder 方法，当 oldItemIds 为空时，应返回 null
     */
    @Test
    public void testBatchGetRecommendStyleImageForOrderWithEmptyOldItemIds() throws Throwable {
        // arrange
        List<String> itemIds = Arrays.asList("1", "2", "3");
        String idType = "test";
        Integer categoryId = 1;
        Long dpDealGroupId = 1L;
        int display = 1;
        // act
        List<NailStyleItemVO> result = immersiveImageWrapper.batchGetRecommendStyleImageForOrder(Collections.emptyList(), idType, categoryId, dpDealGroupId, display);
        // assert
        assertNull(result);
    }

    /**
     * 测试 batchGetRecommendStyleImageForOrder 方法，当所有参数正常时，应返回正常结果
     */
    @Test
    public void testBatchGetRecommendStyleImageForOrderWithNormalParameters() throws Throwable {
        // arrange
        List<String> itemIds = Arrays.asList("1", "2", "3");
        String idType = "oldId";
        Integer categoryId = 1;
        Long dpDealGroupId = 1L;
        int display = 1;
        lionConfigUtilsMocked.when(() -> LionConfigUtils.getRecommendExhibitImageConfig(1)).thenReturn(new ExhibitImageConfig());
        Future mock = mock(Future.class);
        contextStoreMocked.when(ContextStore::getFuture).thenReturn(mock);
        SearchFusionInfoRespDTO respDTO = new SearchFusionInfoRespDTO();
        CommonRespDTO commonRespDTO = new CommonRespDTO();
        commonRespDTO.setCode(CommonRespCodeEnum.CODE_200.code());
        respDTO.setCommonResp(commonRespDTO);
        List<ContentFusionDetailDTO> detailDTOS = Lists.newArrayList();
        ContentFusionDetailDTO contentFusionDetailDTO = new ContentFusionDetailDTO();
        detailDTOS.add(contentFusionDetailDTO);
        respDTO.setContentFusionDetailDTOList(detailDTOS);
        when(mock.get()).thenReturn(respDTO);
        // act
        List<NailStyleItemVO> result = immersiveImageWrapper.batchGetRecommendStyleImageForOrder(itemIds, idType, categoryId, dpDealGroupId, display);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testGeDealtRelatedStyleImageWhenFutureIsNull() throws Throwable {
        // arrange
        QueryExhibitImageParam param = QueryExhibitImageParam.builder().categoryId(1).build();
        EnvCtx envCtx = new EnvCtx();
        Map<String, ExhibitImageConfig> exhibitImageConfigMap = Maps.newHashMap();
        lionMocked.when(() -> Lion.getMap(anyString(), anyString(), any(), any())).thenReturn(exhibitImageConfigMap);
        // act
        List<NailStyleItemVO> result = immersiveImageWrapper.geDealtRelatedStyleImage(param, envCtx, 1);
        // assert
        assertNull(result);
    }

    @Test(expected = Exception.class)
    public void testGeDealtRelatedStyleImageWhenGetImmersiveImageResponseThrowsException() throws Throwable {
        // arrange
        QueryExhibitImageParam param = QueryExhibitImageParam.builder().build();
        EnvCtx envCtx = new EnvCtx();
        Future mock = mock(Future.class);
        Map<String, ExhibitImageConfig> exhibitImageConfigMap = Maps.newHashMap();
        lionMocked.when(() -> Lion.getMap(anyString(), anyString(), any(), any())).thenReturn(exhibitImageConfigMap);
        when(immersiveImageWrapper.preGetImmersiveImage(any(), any())).thenReturn(mock);
        when(mock.get()).thenThrow(new RuntimeException());
        // act
        List<NailStyleItemVO> nailStyleItemVOS = immersiveImageWrapper.geDealtRelatedStyleImage(param, envCtx, 1);
        assertTrue(CollectionUtils.isEmpty(nailStyleItemVOS));
    }

    @Test
    public void testGeDealtRelatedStyleImageWhenAllMethodsExecuteNormally() throws Throwable {
        // arrange
        QueryExhibitImageParam param = QueryExhibitImageParam.builder().categoryId(502).build();
        EnvCtx envCtx = new EnvCtx();
        Future mock = mock(Future.class);
        Map<String, ExhibitImageConfig> exhibitImageConfigMap = Maps.newHashMap();
        exhibitImageConfigMap.put("502", new ExhibitImageConfig());
        lionMocked.when(() -> Lion.getMap(anyString(), anyString(), any(), any())).thenReturn(exhibitImageConfigMap);
        contextStoreMocked.when(ContextStore::getFuture).thenReturn(mock);
        LoadDealRelatedCaseListRespDTO respDTO = new LoadDealRelatedCaseListRespDTO();
        ListItemDTO listItemDTO = new ListItemDTO();
        DisplayItem displayItem = new DisplayItem();
        displayItem.setType(0);
        displayItem.setPicUrl("http://test");
        listItemDTO.setDisplayItems(Collections.singletonList(displayItem));
        respDTO.setItemList(Collections.singletonList(listItemDTO));
        when(mock.get()).thenReturn(respDTO);
        // act
        List<NailStyleItemVO> result = immersiveImageWrapper.geDealtRelatedStyleImage(param, envCtx, 1);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testEyeLashMakeUp() {
        QueryExhibitImageParam queryExhibitImageParam = QueryExhibitImageParam.builder().categoryId(1234).serviceType("化妆").build();
        ImmersiveImageWrapper wrapper = new ImmersiveImageWrapper();
        wrapper.getConfigKey(queryExhibitImageParam);
        queryExhibitImageParam.setServiceType("美睫");
        String result = wrapper.getConfigKey(queryExhibitImageParam);
        assertNotNull(result);
    }

    /**
     * 测试infoContentId为null时返回空字符串
     */
    @Test
    public void testGetDealGroupStyleImageNameWithNullInfoContentId() {
        String result = immersiveImageWrapper.getDealGroupStyleImageName(null);
        assertTrue(StringUtils.isEmpty(result));
    }

    /**
     * 测试infoContentId小于等于0时返回空字符串
     */
    @Test
    public void testGetDealGroupStyleImageNameWithInvalidInfoContentId() {
        String result = immersiveImageWrapper.getDealGroupStyleImageName(-1L);
        assertTrue(StringUtils.isEmpty(result));
    }

    /**
     * 测试搜索服务返回null时返回空字符串
     */
    @Test
    public void testGetDealGroupStyleImageNameWithNullResponse() throws Exception {
        when(contentSearchService.searchDetail(any(SearchRequestDTO.class))).thenReturn(null);
        String result = immersiveImageWrapper.getDealGroupStyleImageName(1L);
        assertTrue(StringUtils.isEmpty(result));
    }

    /**
     * 测试搜索服务返回非200状态码时返回空字符串
     */
    @Test
    public void testGetDealGroupStyleImageNameWithNon200Response() throws Exception {
        SearchDetailResponseDTO responseDTO = new SearchDetailResponseDTO();
        responseDTO.setCode(404);
        when(contentSearchService.searchDetail(any(SearchRequestDTO.class))).thenReturn(responseDTO);
        String result = immersiveImageWrapper.getDealGroupStyleImageName(1L);
        assertTrue(StringUtils.isEmpty(result));
    }

    /**
     * 测试搜索结果为空时返回空字符串
     */
    @Test
    public void testGetDealGroupStyleImageNameWithEmptySearchResult() throws Exception {
        SearchDetailResponseDTO responseDTO = new SearchDetailResponseDTO();
        responseDTO.setCode(200);
        when(contentSearchService.searchDetail(any(SearchRequestDTO.class))).thenReturn(responseDTO);
        String result = immersiveImageWrapper.getDealGroupStyleImageName(1L);
        assertTrue(StringUtils.isEmpty(result));
    }

    /**
     * 测试正常情况下返回款式名称
     */
    @Test
    public void testGetDealGroupStyleImageNameWithValidResponse() throws Exception {
        SearchDetailResponseDTO responseDTO = new SearchDetailResponseDTO();
        responseDTO.setCode(200);
        ImmersiveImageWrapper.DealRelatedStyleImage styleImage = new ImmersiveImageWrapper.DealRelatedStyleImage();
        styleImage.setName("StyleName");
        SearchDataDetailDTO searchDataDetailDTO = new SearchDataDetailDTO();
        searchDataDetailDTO.setDataInfo(JSON.toJSONString(styleImage));
        responseDTO.setSearchResult(Arrays.asList(searchDataDetailDTO));
        when(contentSearchService.searchDetail(any(SearchRequestDTO.class))).thenReturn(responseDTO);
        String result = immersiveImageWrapper.getDealGroupStyleImageName(1L);
        assertEquals("StyleName", result);
    }

    /**
     * 测试getImmersiveImageWithDeals方法，当deals为空时应返回null
     */
    @Test
    public void testGetImmersiveImageWithDealsWhenDealsIsEmpty() throws Throwable {
        // arrange
        List<DealGroupDealDTO> deals = Collections.emptyList();
        Future<LoadDealRelatedCaseListRespDTO> future = null;
        int categoryId = 1;
        Long serviceTypeId = 1L;
        boolean isMt = true;
        Long dealGroupId = 1L;
        Long shopId = 1L;
        Integer status = 1;
        // act
        ImmersiveImageVO result = immersiveImageWrapper.getImmersiveImageWithDeals(deals, future, categoryId, serviceTypeId, isMt, dealGroupId, shopId, status);
        // assert
        assertNull(result);
    }

    /**
     * 测试getImmersiveImageWithDeals方法，当future为null时应返回null
     */
    @Test
    public void testGetImmersiveImageWithDealsWhenFutureIsNull() throws Throwable {
        // arrange
        List<DealGroupDealDTO> deals = Collections.singletonList(new DealGroupDealDTO());
        Future<LoadDealRelatedCaseListRespDTO> future = null;
        int categoryId = 1;
        Long serviceTypeId = 1L;
        boolean isMt = true;
        Long dealGroupId = 1L;
        Long shopId = 1L;
        Integer status = 1;
        // act
        ImmersiveImageVO result = immersiveImageWrapper.getImmersiveImageWithDeals(deals, future, categoryId, serviceTypeId, isMt, dealGroupId, shopId, status);
        // assert
        assertNull(result);
    }

    /**
     * 测试getImmersiveImageWithDeals方法，当future返回有效数据时
     */
    @Test
    public void testGetImmersiveImageWithDealsWhenFutureReturnsValidData() throws Throwable {
        // arrange
        List<DealGroupDealDTO> deals = Collections.singletonList(new DealGroupDealDTO());
        LoadDealRelatedCaseListRespDTO respDTO = new LoadDealRelatedCaseListRespDTO();
        Future mockFuture = mock(Future.class);
        when(mockFuture.get()).thenReturn(respDTO);
        int categoryId = 1;
        Long serviceTypeId = 1L;
        boolean isMt = true;
        Long dealGroupId = 1L;
        Long shopId = 1L;
        Integer status = 1;
        // act
        ImmersiveImageVO result = immersiveImageWrapper.getImmersiveImageWithDeals(deals, mockFuture, categoryId, serviceTypeId, isMt, dealGroupId, shopId, status);
        // assert
        assertNull(result);
    }

    /**
     * 测试 handleWearableNailResp 方法，当 respDTO 为 null 时
     */
    @Test
    public void testHandleWearableNailRespWithNullRespDTO() {
        // arrange
        LoadDealRelatedCaseListRespDTO respDTO = null;
        List<DealGroupDealDTO> deals = Collections.emptyList();
        int categoryId = 1;
        Long serviceTypeId = 1L;
        boolean isMt = true;
        Long dealGroupId = 1L;
        Long shopId = 1L;
        Integer status = 1;
        // act
        ImmersiveImageVO result = immersiveImageWrapper.handleWearableNailResp(respDTO, deals, categoryId, serviceTypeId, isMt, dealGroupId, shopId, status);
        // assert
        assertNull(result);
    }

    /**
     * 测试 handleWearableNailResp 方法，当 respDTO.getItemList() 为空时
     */
    @Test
    public void testHandleWearableNailRespWithEmptyItemList() {
        // arrange
        LoadDealRelatedCaseListRespDTO mocked = mock(LoadDealRelatedCaseListRespDTO.class);
        when(mocked.getItemList()).thenReturn(Collections.emptyList());
        List<DealGroupDealDTO> deals = Collections.emptyList();
        int categoryId = 1;
        Long serviceTypeId = 1L;
        boolean isMt = true;
        Long dealGroupId = 1L;
        Long shopId = 1L;
        Integer status = 1;
        // act
        ImmersiveImageVO result = immersiveImageWrapper.handleWearableNailResp(mocked, deals, categoryId, serviceTypeId, isMt, dealGroupId, shopId, status);
        // assert
        assertNull(result);
    }

    /**
     * 测试 serviceType 是 "美睫"、"化妆" 或 "穿戴甲" 中的任何一个的情况
     */
    @Test
    public void testGetConfigKeyServiceTypeIsValid() {
        QueryExhibitImageParam param = QueryExhibitImageParam.builder().categoryId(1234).serviceType("美睫").build();
        String result = wrapper.getConfigKey(param);
        assertEquals("1234-美睫", result);
    }

    /**
     * 测试 serviceType 不是 "美睫"、"化妆" 或 "穿戴甲" 中的任何一个的情况
     */
    @Test
    public void testGetConfigKeyServiceTypeIsInvalid() {
        QueryExhibitImageParam param = QueryExhibitImageParam.builder().categoryId(1234).serviceType("美甲").build();
        String result = wrapper.getConfigKey(param);
        assertEquals("1234", result);
    }

    /**
     * 测试 serviceType 是 null 的情况
     */
    @Test
    public void testGetConfigKeyServiceTypeIsNull() {
        QueryExhibitImageParam param = QueryExhibitImageParam.builder().categoryId(1234).serviceType(null).build();
        String result = wrapper.getConfigKey(param);
        assertEquals("1234", result);
    }

    @Test
    public void testGetImmersiveImageFilterWhenFutureIsNull() throws Throwable {
        QueryExhibitImageFilterParam param = QueryExhibitImageFilterParam.builder().clientType(1).shopId(1L).bizType(1).subBizType(1).externalBizId(1).externalBizIdType(1).categoryId(1).build();
        ImmersiveImageFilterVO result = immersiveImageWrapper.getImmersiveImageFilter(param);
        assertNull(result);
    }

    /**
     * Tests the scenario where the future is null.
     */
    @Test
    public void testGetImmersiveImageFilterFutureIsNull() throws Throwable {
        // Arrange
        Future<LoadClassicContentFilterRespDTO> future = null;
        int categoryId = 1;
        // Act
        ImmersiveImageFilterVO result = immersiveImageWrapper.getImmersiveImageFilter(future, categoryId);
        // Assert
        assertNull(result);
    }

    /**
     * Tests the scenario where the future is not null, but the response from the future is null.
     */
    @Test
    public void testGetImmersiveImageFilterFutureIsNotNullButRespIsNull() throws Throwable {
        // Arrange
        int categoryId = 1;
        when(future.get()).thenReturn(null);
        // Act
        ImmersiveImageFilterVO result = immersiveImageWrapper.getImmersiveImageFilter(future, categoryId);
        // Assert
        assertNull(result);
    }

    /**
     * Tests the scenario where the future is not null and the response from the future is not null.
     * Since we cannot mock or directly test the private method handleFilterResp, this test ensures
     * that the method getImmersiveImageFilter does not throw an exception when a non-null future is provided.
     */
    @Test
    public void testGetImmersiveImageFilterFutureIsNotNullRespIsNotNull() throws Throwable {
        // Arrange
        int categoryId = 1;
        LoadClassicContentFilterRespDTO respDTO = mock(LoadClassicContentFilterRespDTO.class);
        when(future.get()).thenReturn(respDTO);
        // Act and Assert
        // Since the behavior of handleFilterResp is internal and cannot be directly tested,
        // we focus on ensuring that the method getImmersiveImageFilter completes without throwing exceptions.
        // Specific outcomes depend on the internal logic of handleFilterResp and the state of respDTO.
        try {
            ImmersiveImageFilterVO result = immersiveImageWrapper.getImmersiveImageFilter(future, categoryId);
            // Additional assertions can be made here if there are known outcomes based on the setup.
        } catch (Exception e) {
            fail("Method getImmersiveImageFilter threw an exception: " + e.getMessage());
        }
    }

    /**
     * 测试 future 不为 null，但 getImmersiveImageFilterResponse(future) 返回 null 的情况
     */
    @Test
    public void testGetImmersiveImageFilterFutureIsNotNullButRespDTOIsNull() throws Throwable {
        // arrange
        int categoryId = 1;
        when(future.get()).thenReturn(null);
        // act
        ImmersiveImageFilterVO result = immersiveImageWrapper.getImmersiveImageFilter(future, categoryId);
        // assert
        assertNull(result);
    }

    /**
     * Test method for {@link ImmersiveImageWrapper#buildImmersiveImageWithDeals} when respDTO is null.
     */
    @Test
    public void testBuildImmersiveImageWithDealsWhenRespDTOIsNull() throws Throwable {
        LoadDealRelatedCaseListRespDTO respDTO = null;
        List<ExhibitImageItemVO> items = new ArrayList<>();
        ExhibitImageConfig exhibitImageConfig = new ExhibitImageConfig();
        int categoryId = 1;
        assertNull("Expected null when respDTO is null", immersiveImageWrapper.buildImmersiveImageWithDeals(respDTO, items, exhibitImageConfig, categoryId));
    }

    /**
     * Test method for {@link ImmersiveImageWrapper#buildImmersiveImageWithDeals} when item list is empty.
     */
    @Test
    public void testBuildImmersiveImageWithDealsWhenItemListIsEmpty() throws Throwable {
        LoadDealRelatedCaseListRespDTO respDTO = new LoadDealRelatedCaseListRespDTO();
        respDTO.setItemList(new ArrayList<>());
        respDTO.setRecordCount(0);
        respDTO.setStartIndex(0);
        respDTO.setIsEnd(true);
        respDTO.setNextStartIndex(0);
        List<ExhibitImageItemVO> items = new ArrayList<>();
        ExhibitImageConfig exhibitImageConfig = new ExhibitImageConfig();
        int categoryId = 1;
        assertNull("Expected null when item list is empty", immersiveImageWrapper.buildImmersiveImageWithDeals(respDTO, items, exhibitImageConfig, categoryId));
    }

    /**
     * Test method for {@link ImmersiveImageWrapper#buildImmersiveImageWithDeals} when item list is not empty.
     */
    @Test
    public void testBuildImmersiveImageWithDealsWhenItemListIsNotEmpty() throws Throwable {
        LoadDealRelatedCaseListRespDTO respDTO = new LoadDealRelatedCaseListRespDTO();
        List<ListItemDTO> itemList = new ArrayList<>();
        itemList.add(new ListItemDTO());
        respDTO.setItemList(itemList);
        respDTO.setRecordCount(1);
        respDTO.setStartIndex(0);
        respDTO.setIsEnd(false);
        respDTO.setNextStartIndex(1);
        List<ExhibitImageItemVO> items = new ArrayList<>();
        ExhibitImageConfig exhibitImageConfig = new ExhibitImageConfig();
        // Properly initialize exhibitImageConfig to prevent NullPointerException
        exhibitImageConfig.setItemDisplayStyle(1);
        int categoryId = 1;
        assertNotNull("Expected non-null when item list is not empty", immersiveImageWrapper.buildImmersiveImageWithDeals(respDTO, items, exhibitImageConfig, categoryId));
    }

    /**
     * Test method for {@link ImmersiveImageWrapper#buildImmersiveImageWithDeals} when ExhibitImageConfig is null.
     */
    @Test
    public void testBuildImmersiveImageWithDealsWhenExhibitImageConfigIsNull() throws Throwable {
        LoadDealRelatedCaseListRespDTO respDTO = new LoadDealRelatedCaseListRespDTO();
        respDTO.setRecordCount(1);
        respDTO.setStartIndex(0);
        respDTO.setIsEnd(false);
        respDTO.setNextStartIndex(1);
        List<ExhibitImageItemVO> items = new ArrayList<>();
        // Explicitly testing with null ExhibitImageConfig
        ExhibitImageConfig exhibitImageConfig = null;
        int categoryId = 1;
        assertNull("Expected null when ExhibitImageConfig is null", immersiveImageWrapper.buildImmersiveImageWithDeals(respDTO, items, exhibitImageConfig, categoryId));
    }

    @Test
    public void testGetWearableNailDealItemsWhenItemListIsEmpty() throws Throwable {
        List<ExhibitImageItemVO> result = immersiveImageWrapper.getWearableNailDealItems(Collections.emptyList(), Arrays.asList(deal), config, true);
        assertNull("Expected null result when itemList is empty", result);
    }

    @Test
    public void testGetWearableNailDealItemsWithValidInputs() throws Throwable {
        when(item.getTags()).thenReturn(Arrays.asList(new TagDescDTO("relatedSkus", "[\"sku1\"]", 1)));
        when(haimaWrapper.queryHotBeautyNailConfig()).thenReturn(Collections.emptyList());
        List<ExhibitImageItemVO> result = immersiveImageWrapper.getWearableNailDealItems(Arrays.asList(item), Arrays.asList(deal), config, true);
        assertNotNull("Result should not be null with valid inputs", result);
        assertFalse("Result should not be empty with valid inputs", result.isEmpty());
    }

    @Test
    public void testGetWearableNailDealItemsWhenItemListIsNull() throws Throwable {
        List<ExhibitImageItemVO> result = immersiveImageWrapper.getWearableNailDealItems(null, Arrays.asList(deal), config, true);
        assertNull("Expected null result when itemList is null", result);
    }

    /**
     * 测试获取指定款式置顶的款式数据，非穿戴甲的正常情况
     */
    @Test
    public void test_GetImmersiveImageWithTopItem_Success() {
        // arrange
        String searchDetailRespStr = "{\"code\":200,\"lastPage\":true,\"msg\":\"执行成功\",\"nextPageStartPos\":0,\"searchResult\":[{\"dataId\":\"1818844522868674594\",\"dataInfo\":\"{\\\"top\\\": 1, \\\"name\\\": \\\"拼图拼图游戏test\\\", \\\"rank\\\": 0, \\\"type\\\": [1732], \\\"color\\\": [1905, 1724, 1726], \\\"style\\\": [1738], \\\"theme\\\": [2017, 2020, 2018], \\\"picture\\\": \\\"http://p0.meituan.net/tuanstyle/48641f81ed29b0e745a6deae7ca72d36336236.jpg\\\", \\\"decorate\\\": [1747], \\\"editTime\\\": 1706774070000, \\\"position\\\": [1974], \\\"relatedDeal\\\": [410840454, 406024870, 420021922, 427972303, 1020727042, 1020846985, 1020873249, 1028341525, 1028523317, 1028346457], \\\"oldContentId\\\": 532520, \\\"infoContentId\\\": 1061487027148}\"},{\"dataId\":\"1820733520243376226\",\"dataInfo\":\"{\\\"top\\\": 0, \\\"name\\\": \\\"啦啦啦啦909\\\", \\\"rank\\\": 0, \\\"type\\\": [1733], \\\"color\\\": [1724], \\\"picture\\\": \\\"https://p0.inf.test.sankuai.com/dpmerchantpic/ec8a9ab945c4c9e17cb7ddabb1b87a7210791.jpg\\\", \\\"editTime\\\": 1714464445000, \\\"position\\\": [1975], \\\"relatedDeal\\\": [1028346457], \\\"oldContentId\\\": 569048, \\\"infoContentId\\\": 1293581027148}\"},{\"dataId\":\"1820709202906103902\",\"dataInfo\":\"{\\\"top\\\": 0, \\\"name\\\": \\\"美甲\\\", \\\"rank\\\": 0, \\\"type\\\": [1733], \\\"color\\\": [1722, 1724], \\\"style\\\": [1740], \\\"element\\\": [1963], \\\"picture\\\": \\\"https://p0.meituan.net/dpmerchantpic/2f55b61a4f280e01b686d1cfdf65f20486063.jpg\\\", \\\"decorate\\\": [1742], \\\"editTime\\\": 1706755626000, \\\"position\\\": [1975], \\\"relatedDeal\\\": [1028346457], \\\"oldContentId\\\": 568990, \\\"infoContentId\\\": 1253881027148}\"},{\"dataId\":\"1820733527340138582\",\"dataInfo\":\"{\\\"top\\\": 1, \\\"name\\\": \\\"哈哈哈哈哈哈哈哈哈\\\", \\\"rank\\\": 0, \\\"type\\\": [1736], \\\"color\\\": [1724], \\\"theme\\\": [1906], \\\"element\\\": [1963], \\\"picture\\\": \\\"http://p0.meituan.net/tuanstyle/64159de6909f5ddfe5b8c0f66854029a982511.jpg\\\", \\\"decorate\\\": [1745], \\\"editTime\\\": 1655285679000, \\\"position\\\": [1974], \\\"relatedDeal\\\": [414440310, 406023943, 415371739, 1028346457], \\\"oldContentId\\\": 532526, \\\"infoContentId\\\": 1061690027148}\"},{\"dataId\":\"1820709201811390521\",\"dataInfo\":\"{\\\"top\\\": 0, \\\"name\\\": \\\"Fff\\\", \\\"rank\\\": 0, \\\"type\\\": [1733], \\\"color\\\": [1731], \\\"style\\\": [1739], \\\"theme\\\": [1908, 1909, 1940], \\\"picture\\\": \\\"http://p0.meituan.net/tuanstyle/c9b9b1ddfb676cfddee38e2b5770b9ec775026.jpg\\\", \\\"decorate\\\": [1742], \\\"editTime\\\": 1655139225000, \\\"position\\\": [1719], \\\"relatedDeal\\\": [414440310, 414456098, 406023943, 1028346457], \\\"oldContentId\\\": 533212, \\\"infoContentId\\\": 1051268027148}\"},{\"dataId\":\"1819219959507619901\",\"dataInfo\":\"{\\\"top\\\": 1, \\\"name\\\": \\\"魔镜魔镜魔镜魔镜\\\", \\\"rank\\\": 0, \\\"type\\\": [1732], \\\"color\\\": [1721], \\\"style\\\": [1738], \\\"theme\\\": [1906], \\\"picture\\\": \\\"http://p1.meituan.net/tuanstyle/80e225d2836815357c8df7ce2e9252ef311002.jpg\\\", \\\"decorate\\\": [1744], \\\"editTime\\\": 1652356189000, \\\"position\\\": [1718], \\\"relatedDeal\\\": [406023943, 1028518971, 1028346457], \\\"oldContentId\\\": 532514, \\\"infoContentId\\\": 1061689027148}\"}],\"totalHitCount\":6}";
        String tagsRespStr = "{\"code\":200,\"metaInfoDTOList\":[{\"metaInfoEntityIdDTO\":{\"bizLine\":23,\"entityName\":\"material_manicureStyle\"},\"tagValuePathMap\":{\"decorate\":[{\"label\":\"玻璃纸\",\"tagId\":1964},{\"label\":\"金银线/箔\",\"tagId\":1741},{\"label\":\"钻\",\"tagId\":1742},{\"label\":\"宝石\",\"tagId\":1743},{\"label\":\"蝴蝶结\",\"tagId\":1999},{\"label\":\"亮片\",\"tagId\":1744},{\"label\":\"贝壳\",\"tagId\":1745},{\"label\":\"珍珠\",\"tagId\":1746},{\"label\":\"干花\",\"tagId\":1747},{\"label\":\"链条\",\"tagId\":1750},{\"label\":\"铆钉\",\"tagId\":1751},{\"label\":\"魔镜粉\",\"tagId\":1752},{\"label\":\"蕾丝\",\"tagId\":1754},{\"label\":\"糖果纸\",\"tagId\":1755}],\"color\":[{\"label\":\"银色\",\"tagId\":1728},{\"label\":\"灰色\",\"tagId\":1729},{\"label\":\"金色\",\"tagId\":1730},{\"label\":\"紫色\",\"tagId\":1731},{\"label\":\"绿色\",\"tagId\":1904},{\"label\":\"棕色\",\"tagId\":1905},{\"label\":\"彩色\",\"tagId\":1720},{\"label\":\"白色\",\"tagId\":1721},{\"label\":\"橙色\",\"tagId\":1977},{\"label\":\"蓝色\",\"tagId\":1722},{\"label\":\"黑色\",\"tagId\":1723},{\"label\":\"红色\",\"tagId\":1724},{\"label\":\"粉色\",\"tagId\":1725},{\"label\":\"裸色\",\"tagId\":1726},{\"label\":\"黄色\",\"tagId\":1727}],\"style\":[{\"label\":\"简约\",\"tagId\":2000},{\"label\":\"复古\",\"tagId\":2001},{\"label\":\"温柔\",\"tagId\":2002},{\"label\":\"酷飒\",\"tagId\":2003},{\"label\":\"小清新\",\"tagId\":2004},{\"label\":\"欧美\",\"tagId\":2005},{\"label\":\"可爱\",\"tagId\":2006},{\"label\":\"通勤\",\"tagId\":2007},{\"label\":\"文艺\",\"tagId\":2008},{\"label\":\"华丽\",\"tagId\":2009},{\"label\":\"日系\",\"tagId\":1738},{\"label\":\"ins风\",\"tagId\":1740}],\"theme\":[{\"label\":\"圣诞美甲\",\"tagId\":2016},{\"label\":\"万圣节美甲\",\"tagId\":2017},{\"label\":\"秋冬美甲\",\"tagId\":2020},{\"label\":\"指尖芭蕾\",\"tagId\":1908},{\"label\":\"桃桃乌龙\",\"tagId\":1909},{\"label\":\"海洋冰蓝\",\"tagId\":1941},{\"label\":\"珍珠云贝母\",\"tagId\":1910},{\"label\":\"缤纷色彩\",\"tagId\":1942},{\"label\":\"温柔气质\",\"tagId\":1911},{\"label\":\"仙女裸色系\",\"tagId\":1943},{\"label\":\"果冻冰透\",\"tagId\":1944},{\"label\":\"棋盘格美甲\",\"tagId\":1945},{\"label\":\"晶石猫眼\",\"tagId\":1946},{\"label\":\"ins可爱风\",\"tagId\":1947},{\"label\":\"浪漫渐变\",\"tagId\":1948},{\"label\":\"秋日美甲\",\"tagId\":2012},{\"label\":\"复古法式\",\"tagId\":1949},{\"label\":\"焦糖琥珀\",\"tagId\":2013},{\"label\":\"格纹线条\",\"tagId\":2014},{\"label\":\"甜酷辣妹\",\"tagId\":2015}],\"position\":[{\"label\":\"手部\",\"tagId\":1718},{\"label\":\"足部\",\"tagId\":1719}],\"type\":[{\"label\":\"纯色\",\"tagId\":1732},{\"label\":\"跳色\",\"tagId\":1733},{\"label\":\"渐变\",\"tagId\":1734},{\"label\":\"晕染\",\"tagId\":1735},{\"label\":\"猫眼\",\"tagId\":1736},{\"label\":\"彩绘\",\"tagId\":1737},{\"label\":\"法式\",\"tagId\":1961}],\"element\":[{\"label\":\"水波纹\",\"tagId\":1984},{\"label\":\"玫瑰\",\"tagId\":1985},{\"label\":\"毛衣纹\",\"tagId\":1986},{\"label\":\"蝴蝶\",\"tagId\":1987},{\"label\":\"几何线条\",\"tagId\":1988},{\"label\":\"毛呢纹\",\"tagId\":1989},{\"label\":\"格纹\",\"tagId\":1990},{\"label\":\"雏菊\",\"tagId\":1991},{\"label\":\"花卉/朵\",\"tagId\":1992},{\"label\":\"动物\",\"tagId\":1993},{\"label\":\"云朵\",\"tagId\":1994},{\"label\":\"泫雅花朵\",\"tagId\":1963},{\"label\":\"大理石纹\",\"tagId\":1995},{\"label\":\"水果\",\"tagId\":1996},{\"label\":\"植物\",\"tagId\":1997},{\"label\":\"字母\",\"tagId\":1998},{\"label\":\"波点\",\"tagId\":1978},{\"label\":\"棋盘格\",\"tagId\":1979},{\"label\":\"爱心\",\"tagId\":1980},{\"label\":\"卡通动漫\",\"tagId\":1981},{\"label\":\"水滴\",\"tagId\":1982},{\"label\":\"雪花\",\"tagId\":1983}]}}],\"msg\":\"执行成功\"}";
        SearchDetailResponseDTO searchDetailResp = JSON.parseObject(searchDetailRespStr, SearchDetailResponseDTO.class);
        BatchQueryTagValueResponseDTO tagsResp = JSON.parseObject(tagsRespStr, BatchQueryTagValueResponseDTO.class);
        Future detailFuture = CompletableFuture.completedFuture(searchDetailResp);
        Future tagsFuture = CompletableFuture.completedFuture(tagsResp);
        ExhibitImageConfig config = new ExhibitImageConfig();
        config.setMultiStyleTagKey(Lists.newArrayList("theme", "style", "element",
                "decorate", "type", "color", "position"));
        config.setItemDisplayStyle(0);
        Map<String, ExhibitImageConfig> exhibitImageConfigMap = Maps.newHashMap();
        exhibitImageConfigMap.put("502", config);
        lionMocked.when(() -> Lion.getMap(LionConstants.APP_KEY,
                LionConstants.IMMERSIVE_EXHIBIT_IMAGE_CONFIG, ExhibitImageConfig.class)).thenReturn(exhibitImageConfigMap);
        lionMocked.when(() -> Lion.getMap(LionConstants.APP_KEY,
                LionConstants.CATEGORY_EXHIBIT_TITLE_CONFIG, ExhibitTextConfig.class, Collections.EMPTY_MAP)).thenReturn(Maps.newHashMap());
        lionConfigUtilsMocked.when(() -> LionConfigUtils.enableExhibitSourceBeautyNailImmersiveImage()).thenReturn(false);
        when(haimaWrapper.queryHotBeautyNailConfig()).thenReturn(Lists.newArrayList());
        // act
        ImmersiveImageVO result = immersiveImageWrapper.getImmersiveImageWithTopItem(detailFuture, tagsFuture, 502, false);

        // assert
        assertNotNull(result);
        assertEquals(6, result.getItems().size());
        assertEquals("532520", result.getItems().get(0).getItemId());
        assertEquals("拼图拼图游戏test", result.getItems().get(0).getName());
    }




    @Test
    public void test_checkMrnVersion_0514() throws Throwable {
        String immersiveImageConfigStr = "{\"categoryList\":[504,910,921,2003,456,502,512],\"dpLink\":\"dianping://mrn?mrn_biz=gc&mrn_entry=vg-max-deal-style-gallery&mrn_component=DealStyleGallery\",\"enable\":true,\"mrnVersion\":\"0.5.14\",\"mtLink\":\"imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=vg-max-deal-style-gallery&mrn_component=DealStyleGallery\"}";
        String exhibitContentDTOStr = "{\"itemDisplayStyle\":2,\"items\":[{\"itemId\":\"1325959101148\",\"name\":\"测试1\",\"tags\":[{\"name\":\"粉雾眉\",\"style\":0},{\"name\":\"油性皮肤\",\"style\":0}],\"urls\":[{\"height\":2532,\"imageBestScale\":\"9:16\",\"type\":0,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/4d6f3be3a22a6e3c8296bcb636c36993150503.jpg\",\"width\":1170},{\"height\":960,\"imageBestScale\":\"3:4\",\"type\":0,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/1e4f877faff85a248e0511f2715b697980647.jpg\",\"width\":720},{\"height\":473,\"imageBestScale\":\"4:3\",\"type\":0,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/912a8e002ff2ba91557226895c736fdc34045.jpg\",\"width\":630}]}],\"merchantStyle\":false,\"recordCount\":1,\"serviceTypeId\":0,\"showAllText\":\"全部风格\",\"title\":\"可拍风格\"}";
        ImmersiveImageConfig immersiveImageConfig = JSON.parseObject(immersiveImageConfigStr, ImmersiveImageConfig.class);
        ExhibitContentDTO exhibitContentDTO = JSON.parseObject(exhibitContentDTOStr, ExhibitContentDTO.class);
        lionMocked.when(() -> Lion.getBean(LionConstants.APP_KEY,
                LionConstants.IMMERSIVE_IMAGE_CONFIG, ImmersiveImageConfig.class, null))
                .thenReturn(immersiveImageConfig);
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(512);
        ctx.setChannelDTO(channelDTO);
        ctx.setMrnVersion("0.5.13");
        ExhibitContentDTO exhibitContentDTO1= immersiveImageWrapper.jumpUrlToShopPage(ctx, exhibitContentDTO);
        assertNull(exhibitContentDTO1);
    }

    @Test
    public void test_checkMrnVersion_0513() throws Throwable {
        String immersiveImageConfigStr = "{\"categoryList\":[504,910,921,2003,456,502,512],\"dpLink\":\"dianping://mrn?mrn_biz=gc&mrn_entry=vg-max-deal-style-gallery&mrn_component=DealStyleGallery\",\"enable\":true,\"mrnVersion\":\"0.5.14\",\"mtLink\":\"imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=vg-max-deal-style-gallery&mrn_component=DealStyleGallery\"}";
        String exhibitContentDTOStr = "{\"itemDisplayStyle\":2,\"items\":[{\"itemId\":\"1325959101148\",\"name\":\"测试1\",\"tags\":[{\"name\":\"粉雾眉\",\"style\":0},{\"name\":\"油性皮肤\",\"style\":0}],\"urls\":[{\"height\":2532,\"imageBestScale\":\"9:16\",\"type\":0,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/4d6f3be3a22a6e3c8296bcb636c36993150503.jpg\",\"width\":1170},{\"height\":960,\"imageBestScale\":\"3:4\",\"type\":0,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/1e4f877faff85a248e0511f2715b697980647.jpg\",\"width\":720},{\"height\":473,\"imageBestScale\":\"4:3\",\"type\":0,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/912a8e002ff2ba91557226895c736fdc34045.jpg\",\"width\":630}]}],\"merchantStyle\":false,\"recordCount\":1,\"serviceTypeId\":0,\"showAllText\":\"全部风格\",\"title\":\"可拍风格\"}";
        ImmersiveImageConfig immersiveImageConfig = JSON.parseObject(immersiveImageConfigStr, ImmersiveImageConfig.class);
        ExhibitContentDTO exhibitContentDTO = JSON.parseObject(exhibitContentDTOStr, ExhibitContentDTO.class);
        lionMocked.when(() -> Lion.getBean(LionConstants.APP_KEY,
                        LionConstants.IMMERSIVE_IMAGE_CONFIG, ImmersiveImageConfig.class, null))
                .thenReturn(immersiveImageConfig);
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(512);
        ctx.setChannelDTO(channelDTO);
        ctx.setMrnVersion("0.5.14");
        ExhibitContentDTO exhibitContentDTO1= immersiveImageWrapper.jumpUrlToShopPage(ctx, exhibitContentDTO);
        assertNull(exhibitContentDTO1);
    }


    @Test
    public void test_checkMrnVersion_0514_910() throws Throwable {
        String immersiveImageConfigStr = "{\"categoryList\":[504,910,921,2003,456,502,512],\"dpLink\":\"dianping://mrn?mrn_biz=gc&mrn_entry=vg-max-deal-style-gallery&mrn_component=DealStyleGallery\",\"enable\":true,\"mrnVersion\":\"0.5.14\",\"mtLink\":\"imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=vg-max-deal-style-gallery&mrn_component=DealStyleGallery\"}";
        String exhibitContentDTOStr = "{\"itemDisplayStyle\":2,\"items\":[{\"itemId\":\"1325959101148\",\"name\":\"测试1\",\"tags\":[{\"name\":\"粉雾眉\",\"style\":0},{\"name\":\"油性皮肤\",\"style\":0}],\"urls\":[{\"height\":2532,\"imageBestScale\":\"9:16\",\"type\":0,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/4d6f3be3a22a6e3c8296bcb636c36993150503.jpg\",\"width\":1170},{\"height\":960,\"imageBestScale\":\"3:4\",\"type\":0,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/1e4f877faff85a248e0511f2715b697980647.jpg\",\"width\":720},{\"height\":473,\"imageBestScale\":\"4:3\",\"type\":0,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/912a8e002ff2ba91557226895c736fdc34045.jpg\",\"width\":630}]}],\"merchantStyle\":false,\"recordCount\":1,\"serviceTypeId\":0,\"showAllText\":\"全部风格\",\"title\":\"可拍风格\"}";
        ImmersiveImageConfig immersiveImageConfig = JSON.parseObject(immersiveImageConfigStr, ImmersiveImageConfig.class);
        ExhibitContentDTO exhibitContentDTO = JSON.parseObject(exhibitContentDTOStr, ExhibitContentDTO.class);
        lionMocked.when(() -> Lion.getBean(LionConstants.APP_KEY,
                        LionConstants.IMMERSIVE_IMAGE_CONFIG, ImmersiveImageConfig.class, null))
                .thenReturn(immersiveImageConfig);
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(910);
        ctx.setChannelDTO(channelDTO);
        ctx.setMrnVersion("0.5.14");
        ExhibitContentDTO exhibitContentDTO1= immersiveImageWrapper.jumpUrlToShopPage(ctx, exhibitContentDTO);
        assertNotNull(exhibitContentDTO1);
    }
}
