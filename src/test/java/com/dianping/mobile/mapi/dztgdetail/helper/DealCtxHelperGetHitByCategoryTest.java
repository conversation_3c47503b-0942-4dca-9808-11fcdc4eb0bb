package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import com.dianping.mobile.mapi.dztgdetail.helper.config.InsuranceConfigDTO;
import com.google.common.collect.Lists;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.apache.commons.collections4.MapUtils;
import java.util.*;
import org.junit.Test;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import java.util.Arrays;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.dto.TypeHierarchyView;
import java.util.ArrayList;
import java.util.Objects;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import org.apache.commons.collections4.CollectionUtils;
import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.when;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import org.junit.Before;

@RunWith(MockitoJUnitRunner.class)
public class DealCtxHelperGetHitByCategoryTest {

    private static Method getHitByCategoryMethod;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private DealGroupDTO dealGroupDTO;

    @Mock
    private DealGroupCategoryDTO category;

    @Mock
    private InsuranceConfigDTO insuranceConfigDTO;

    @BeforeClass
    public static void setUpClass() throws Exception {
        // Use reflection to access the private method
        getHitByCategoryMethod = DealCtxHelper.class.getDeclaredMethod("getHitByCategory", Map.class, DealGroupCategoryDTO.class);
        getHitByCategoryMethod.setAccessible(true);
    }

    @Test
    public void testGetHitByCategoryProductCategoryIsNull() throws Throwable {
        Map<String, Object> map = new HashMap<>();
        Object result = getHitByCategoryMethod.invoke(null, map, null);
        assertNull(result);
    }

    @Test
    public void testGetHitByCategoryMapIsEmpty() throws Throwable {
        Map<String, Object> map = new HashMap<>();
        DealGroupCategoryDTO productCategory = new DealGroupCategoryDTO();
        Object result = getHitByCategoryMethod.invoke(null, map, productCategory);
        assertNull(result);
    }

    @Test
    public void testGetHitByCategoryMapNotContainsKeyAndCategoryId() throws Throwable {
        Map<String, Object> map = new HashMap<>();
        DealGroupCategoryDTO productCategory = new DealGroupCategoryDTO();
        productCategory.setCategoryId(1L);
        productCategory.setServiceTypeId(1L);
        Object result = getHitByCategoryMethod.invoke(null, map, productCategory);
        assertNull(result);
    }

    @Test
    public void testGetHitByCategoryMapContainsKey() throws Throwable {
        Map<String, Object> map = new HashMap<>();
        DealGroupCategoryDTO productCategory = new DealGroupCategoryDTO();
        productCategory.setCategoryId(1L);
        productCategory.setServiceTypeId(1L);
        map.put("1.1", new Object());
        Object result = getHitByCategoryMethod.invoke(null, map, productCategory);
        assertNotNull(result);
    }

    @Test
    public void testGetHitByCategoryMapNotContainsKeyButContainsCategoryId() throws Throwable {
        Map<String, Object> map = new HashMap<>();
        DealGroupCategoryDTO productCategory = new DealGroupCategoryDTO();
        productCategory.setCategoryId(1L);
        productCategory.setServiceTypeId(1L);
        map.put("1", new Object());
        Object result = getHitByCategoryMethod.invoke(null, map, productCategory);
        assertNotNull(result);
    }

    @Test
    public void testCheckByShopTag_NullInsuranceConfig() throws Throwable {
        // arrange
        InsuranceConfigDTO insuranceConfigDTO = null;
        DealGroupCategoryDTO productCategory = mock(DealGroupCategoryDTO.class);
        Set<Long> shopTags = new HashSet<>(Collections.singletonList(1L));
        // act
        boolean result = DealCtxHelper.checkByShopTag(insuranceConfigDTO, productCategory, shopTags);
        // assert
        assertFalse(result);
    }

    @Test
    public void testCheckByShopTag_EmptyShopTagConfigMap() throws Throwable {
        // arrange
        InsuranceConfigDTO insuranceConfigDTO = mock(InsuranceConfigDTO.class);
        when(insuranceConfigDTO.getShopTagConfigMap()).thenReturn(Collections.emptyMap());
        DealGroupCategoryDTO productCategory = mock(DealGroupCategoryDTO.class);
        Set<Long> shopTags = new HashSet<>(Collections.singletonList(1L));
        // act
        boolean result = DealCtxHelper.checkByShopTag(insuranceConfigDTO, productCategory, shopTags);
        // assert
        assertFalse(result);
    }

    @Test
    public void testCheckByShopTag_NullProductCategory() throws Throwable {
        // arrange
        InsuranceConfigDTO insuranceConfigDTO = mock(InsuranceConfigDTO.class);
        Map<String, List<Long>> shopTagConfigMap = new HashMap<>();
        shopTagConfigMap.put("505.123", Collections.singletonList(1L));
        when(insuranceConfigDTO.getShopTagConfigMap()).thenReturn(shopTagConfigMap);
        DealGroupCategoryDTO productCategory = null;
        Set<Long> shopTags = new HashSet<>(Collections.singletonList(1L));
        // act
        boolean result = DealCtxHelper.checkByShopTag(insuranceConfigDTO, productCategory, shopTags);
        // assert
        assertFalse(result);
    }

    @Test
    public void testCheckByShopTag_NoMatchingCategoryConfig() throws Throwable {
        // arrange
        InsuranceConfigDTO insuranceConfigDTO = mock(InsuranceConfigDTO.class);
        Map<String, List<Long>> shopTagConfigMap = new HashMap<>();
        shopTagConfigMap.put("505.123", Collections.singletonList(1L));
        when(insuranceConfigDTO.getShopTagConfigMap()).thenReturn(shopTagConfigMap);
        DealGroupCategoryDTO productCategory = mock(DealGroupCategoryDTO.class);
        when(productCategory.getCategoryId()).thenReturn(506L);
        when(productCategory.getServiceTypeId()).thenReturn(123L);
        Set<Long> shopTags = new HashSet<>(Collections.singletonList(1L));
        // act
        boolean result = DealCtxHelper.checkByShopTag(insuranceConfigDTO, productCategory, shopTags);
        // assert
        assertFalse(result);
    }

    @Test
    public void testCheckByShopTag_MatchingCategoryNoTagMatch() throws Throwable {
        // arrange
        InsuranceConfigDTO insuranceConfigDTO = mock(InsuranceConfigDTO.class);
        Map<String, List<Long>> shopTagConfigMap = new HashMap<>();
        shopTagConfigMap.put("505.123", Collections.singletonList(1L));
        when(insuranceConfigDTO.getShopTagConfigMap()).thenReturn(shopTagConfigMap);
        DealGroupCategoryDTO productCategory = mock(DealGroupCategoryDTO.class);
        when(productCategory.getCategoryId()).thenReturn(505L);
        when(productCategory.getServiceTypeId()).thenReturn(123L);
        Set<Long> shopTags = new HashSet<>(Collections.singletonList(2L));
        // act
        boolean result = DealCtxHelper.checkByShopTag(insuranceConfigDTO, productCategory, shopTags);
        // assert
        assertFalse(result);
    }

    @Test
    public void testCheckByShopTag_MatchingCategoryAndTagMatch_WithServiceType() throws Throwable {
        // arrange
        InsuranceConfigDTO insuranceConfigDTO = mock(InsuranceConfigDTO.class);
        Map<String, List<Long>> shopTagConfigMap = new HashMap<>();
        shopTagConfigMap.put("505.123", Collections.singletonList(1L));
        when(insuranceConfigDTO.getShopTagConfigMap()).thenReturn(shopTagConfigMap);
        DealGroupCategoryDTO productCategory = mock(DealGroupCategoryDTO.class);
        when(productCategory.getCategoryId()).thenReturn(505L);
        when(productCategory.getServiceTypeId()).thenReturn(123L);
        Set<Long> shopTags = new HashSet<>(Collections.singletonList(1L));
        // act
        boolean result = DealCtxHelper.checkByShopTag(insuranceConfigDTO, productCategory, shopTags);
        // assert
        assertTrue(result);
    }

    @Test
    public void testCheckByShopTag_MatchingCategoryAndTagMatch_WithoutServiceType() throws Throwable {
        // arrange
        InsuranceConfigDTO insuranceConfigDTO = mock(InsuranceConfigDTO.class);
        Map<String, List<Long>> shopTagConfigMap = new HashMap<>();
        shopTagConfigMap.put("505", Collections.singletonList(1L));
        when(insuranceConfigDTO.getShopTagConfigMap()).thenReturn(shopTagConfigMap);
        DealGroupCategoryDTO productCategory = mock(DealGroupCategoryDTO.class);
        when(productCategory.getCategoryId()).thenReturn(505L);
        when(productCategory.getServiceTypeId()).thenReturn(123L);
        Set<Long> shopTags = new HashSet<>(Collections.singletonList(1L));
        // act
        boolean result = DealCtxHelper.checkByShopTag(insuranceConfigDTO, productCategory, shopTags);
        // assert
        assertTrue(result);
    }

    @Test
    public void testCheckByShopTag_NullShopTags() throws Throwable {
        // arrange
        InsuranceConfigDTO insuranceConfigDTO = mock(InsuranceConfigDTO.class);
        Map<String, List<Long>> shopTagConfigMap = new HashMap<>();
        shopTagConfigMap.put("505.123", Collections.singletonList(1L));
        when(insuranceConfigDTO.getShopTagConfigMap()).thenReturn(shopTagConfigMap);
        DealGroupCategoryDTO productCategory = mock(DealGroupCategoryDTO.class);
        when(productCategory.getCategoryId()).thenReturn(505L);
        when(productCategory.getServiceTypeId()).thenReturn(123L);
        Set<Long> shopTags = null;
        // act
        boolean result = DealCtxHelper.checkByShopTag(insuranceConfigDTO, productCategory, shopTags);
        // assert
        assertFalse(result);
    }

    @Test
    public void testCheckByShopTag_EmptyShopTags() throws Throwable {
        // arrange
        InsuranceConfigDTO insuranceConfigDTO = mock(InsuranceConfigDTO.class);
        Map<String, List<Long>> shopTagConfigMap = new HashMap<>();
        shopTagConfigMap.put("505.123", Collections.singletonList(1L));
        when(insuranceConfigDTO.getShopTagConfigMap()).thenReturn(shopTagConfigMap);
        DealGroupCategoryDTO productCategory = mock(DealGroupCategoryDTO.class);
        when(productCategory.getCategoryId()).thenReturn(505L);
        when(productCategory.getServiceTypeId()).thenReturn(123L);
        Set<Long> shopTags = Collections.emptySet();
        // act
        boolean result = DealCtxHelper.checkByShopTag(insuranceConfigDTO, productCategory, shopTags);
        // assert
        assertFalse(result);
    }

    @Test
    public void testCheckByShopTag_MultipleTagsOneMatch() throws Throwable {
        // arrange
        InsuranceConfigDTO insuranceConfigDTO = mock(InsuranceConfigDTO.class);
        Map<String, List<Long>> shopTagConfigMap = new HashMap<>();
        shopTagConfigMap.put("505.123", Lists.newArrayList(1L, 2L, 3L));
        when(insuranceConfigDTO.getShopTagConfigMap()).thenReturn(shopTagConfigMap);
        DealGroupCategoryDTO productCategory = mock(DealGroupCategoryDTO.class);
        when(productCategory.getCategoryId()).thenReturn(505L);
        when(productCategory.getServiceTypeId()).thenReturn(123L);
        Set<Long> shopTags = new HashSet<>(Collections.singletonList(2L));
        // act
        boolean result = DealCtxHelper.checkByShopTag(insuranceConfigDTO, productCategory, shopTags);
        // assert
        assertTrue(result);
    }

    @Test
    public void testCheckByProductTag_NullInsuranceConfigDTO() throws Throwable {
        // arrange
        InsuranceConfigDTO insuranceConfigDTO = null;
        DealGroupCategoryDTO productCategory = new DealGroupCategoryDTO();
        Set<Long> productTagIds = new HashSet<>();
        // act
        boolean result = DealCtxHelper.checkByProductTag(insuranceConfigDTO, productCategory, productTagIds);
        // assert
        assertFalse(result);
    }

    @Test
    public void testCheckByProductTag_EmptyProductTagConfigMap() throws Throwable {
        // arrange
        InsuranceConfigDTO insuranceConfigDTO = new InsuranceConfigDTO();
        insuranceConfigDTO.setProductTagConfigMap(new HashMap<>());
        DealGroupCategoryDTO productCategory = new DealGroupCategoryDTO();
        Set<Long> productTagIds = new HashSet<>();
        // act
        boolean result = DealCtxHelper.checkByProductTag(insuranceConfigDTO, productCategory, productTagIds);
        // assert
        assertFalse(result);
    }

    @Test
    public void testCheckByProductTag_NullProductTagConfigMap() throws Throwable {
        // arrange
        InsuranceConfigDTO insuranceConfigDTO = new InsuranceConfigDTO();
        insuranceConfigDTO.setProductTagConfigMap(null);
        DealGroupCategoryDTO productCategory = new DealGroupCategoryDTO();
        Set<Long> productTagIds = new HashSet<>();
        // act
        boolean result = DealCtxHelper.checkByProductTag(insuranceConfigDTO, productCategory, productTagIds);
        // assert
        assertFalse(result);
    }

    @Test
    public void testCheckByProductTag_NullProductCategory() throws Throwable {
        // arrange
        InsuranceConfigDTO insuranceConfigDTO = new InsuranceConfigDTO();
        Map<String, List<Long>> productTagConfigMap = new HashMap<>();
        productTagConfigMap.put("123", Arrays.asList(1L, 2L, 3L));
        insuranceConfigDTO.setProductTagConfigMap(productTagConfigMap);
        DealGroupCategoryDTO productCategory = null;
        Set<Long> productTagIds = new HashSet<>(Arrays.asList(1L, 4L));
        // act
        boolean result = DealCtxHelper.checkByProductTag(insuranceConfigDTO, productCategory, productTagIds);
        // assert
        assertFalse(result);
    }

    @Test
    public void testCheckByProductTag_NoMatchingCategory() throws Throwable {
        // arrange
        InsuranceConfigDTO insuranceConfigDTO = new InsuranceConfigDTO();
        Map<String, List<Long>> productTagConfigMap = new HashMap<>();
        productTagConfigMap.put("123", Arrays.asList(1L, 2L, 3L));
        insuranceConfigDTO.setProductTagConfigMap(productTagConfigMap);
        DealGroupCategoryDTO productCategory = new DealGroupCategoryDTO();
        productCategory.setCategoryId(456L);
        productCategory.setServiceTypeId(789L);
        Set<Long> productTagIds = new HashSet<>(Arrays.asList(1L, 4L));
        // act
        boolean result = DealCtxHelper.checkByProductTag(insuranceConfigDTO, productCategory, productTagIds);
        // assert
        assertFalse(result);
    }

    @Test
    public void testCheckByProductTag_MatchingCategoryWithServiceTypeNoMatchingTags() throws Throwable {
        // arrange
        InsuranceConfigDTO insuranceConfigDTO = new InsuranceConfigDTO();
        Map<String, List<Long>> productTagConfigMap = new HashMap<>();
        productTagConfigMap.put("456.789", Arrays.asList(5L, 6L, 7L));
        insuranceConfigDTO.setProductTagConfigMap(productTagConfigMap);
        DealGroupCategoryDTO productCategory = new DealGroupCategoryDTO();
        productCategory.setCategoryId(456L);
        productCategory.setServiceTypeId(789L);
        Set<Long> productTagIds = new HashSet<>(Arrays.asList(1L, 2L));
        // act
        boolean result = DealCtxHelper.checkByProductTag(insuranceConfigDTO, productCategory, productTagIds);
        // assert
        assertFalse(result);
    }

    @Test
    public void testCheckByProductTag_MatchingCategoryNoMatchingTags() throws Throwable {
        // arrange
        InsuranceConfigDTO insuranceConfigDTO = new InsuranceConfigDTO();
        Map<String, List<Long>> productTagConfigMap = new HashMap<>();
        productTagConfigMap.put("456", Arrays.asList(5L, 6L, 7L));
        insuranceConfigDTO.setProductTagConfigMap(productTagConfigMap);
        DealGroupCategoryDTO productCategory = new DealGroupCategoryDTO();
        productCategory.setCategoryId(456L);
        productCategory.setServiceTypeId(789L);
        Set<Long> productTagIds = new HashSet<>(Arrays.asList(1L, 2L));
        // act
        boolean result = DealCtxHelper.checkByProductTag(insuranceConfigDTO, productCategory, productTagIds);
        // assert
        assertFalse(result);
    }

    @Test
    public void testCheckByProductTag_MatchingCategoryWithServiceTypeAndMatchingTags() throws Throwable {
        // arrange
        InsuranceConfigDTO insuranceConfigDTO = new InsuranceConfigDTO();
        Map<String, List<Long>> productTagConfigMap = new HashMap<>();
        productTagConfigMap.put("456.789", Arrays.asList(5L, 6L, 7L));
        insuranceConfigDTO.setProductTagConfigMap(productTagConfigMap);
        DealGroupCategoryDTO productCategory = new DealGroupCategoryDTO();
        productCategory.setCategoryId(456L);
        productCategory.setServiceTypeId(789L);
        Set<Long> productTagIds = new HashSet<>(Arrays.asList(1L, 6L));
        // act
        boolean result = DealCtxHelper.checkByProductTag(insuranceConfigDTO, productCategory, productTagIds);
        // assert
        assertTrue(result);
    }

    @Test
    public void testCheckByProductTag_MatchingCategoryAndMatchingTags() throws Throwable {
        // arrange
        InsuranceConfigDTO insuranceConfigDTO = new InsuranceConfigDTO();
        Map<String, List<Long>> productTagConfigMap = new HashMap<>();
        productTagConfigMap.put("456", Arrays.asList(5L, 6L, 7L));
        insuranceConfigDTO.setProductTagConfigMap(productTagConfigMap);
        DealGroupCategoryDTO productCategory = new DealGroupCategoryDTO();
        productCategory.setCategoryId(456L);
        productCategory.setServiceTypeId(789L);
        Set<Long> productTagIds = new HashSet<>(Arrays.asList(1L, 6L));
        // act
        boolean result = DealCtxHelper.checkByProductTag(insuranceConfigDTO, productCategory, productTagIds);
        // assert
        assertTrue(result);
    }

    @Test
    public void testCheckByProductTag_NullProductTagIds() throws Throwable {
        // arrange
        InsuranceConfigDTO insuranceConfigDTO = new InsuranceConfigDTO();
        Map<String, List<Long>> productTagConfigMap = new HashMap<>();
        productTagConfigMap.put("456", Arrays.asList(5L, 6L, 7L));
        insuranceConfigDTO.setProductTagConfigMap(productTagConfigMap);
        DealGroupCategoryDTO productCategory = new DealGroupCategoryDTO();
        productCategory.setCategoryId(456L);
        productCategory.setServiceTypeId(789L);
        Set<Long> productTagIds = null;
        // act
        boolean result = DealCtxHelper.checkByProductTag(insuranceConfigDTO, productCategory, productTagIds);
        // assert
        assertFalse(result);
    }

    @Test
    public void testCheckByProductTag_EmptyProductTagIds() throws Throwable {
        // arrange
        InsuranceConfigDTO insuranceConfigDTO = new InsuranceConfigDTO();
        Map<String, List<Long>> productTagConfigMap = new HashMap<>();
        productTagConfigMap.put("456", Arrays.asList(5L, 6L, 7L));
        insuranceConfigDTO.setProductTagConfigMap(productTagConfigMap);
        DealGroupCategoryDTO productCategory = new DealGroupCategoryDTO();
        productCategory.setCategoryId(456L);
        productCategory.setServiceTypeId(789L);
        Set<Long> productTagIds = new HashSet<>();
        // act
        boolean result = DealCtxHelper.checkByProductTag(insuranceConfigDTO, productCategory, productTagIds);
        // assert
        assertFalse(result);
    }

    @Test
    public void testCheckByProductTag_PrioritizeCategoryWithServiceType() throws Throwable {
        // arrange
        InsuranceConfigDTO insuranceConfigDTO = new InsuranceConfigDTO();
        Map<String, List<Long>> productTagConfigMap = new HashMap<>();
        productTagConfigMap.put("456.789", Arrays.asList(5L, 6L));
        productTagConfigMap.put("456", Arrays.asList(7L, 8L));
        insuranceConfigDTO.setProductTagConfigMap(productTagConfigMap);
        DealGroupCategoryDTO productCategory = new DealGroupCategoryDTO();
        productCategory.setCategoryId(456L);
        productCategory.setServiceTypeId(789L);
        Set<Long> productTagIds = new HashSet<>(Arrays.asList(8L));
        // act
        boolean result = DealCtxHelper.checkByProductTag(insuranceConfigDTO, productCategory, productTagIds);
        // assert
        // Should be false because 8L is in the "456" config but we prioritize "456.789" which doesn't contain 8L
        assertFalse(result);
    }

    @Test
    public void testGetShopTagIdsWhenListIsNull() throws Throwable {
        // arrange
        List<DisplayTagDto> shopDisplayTagDtoList = null;
        // act
        Set<Long> result = DealCtxHelper.getShopTagIds(shopDisplayTagDtoList);
        // assert
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetShopTagIdsWhenListIsEmpty() throws Throwable {
        // arrange
        List<DisplayTagDto> shopDisplayTagDtoList = Collections.emptyList();
        // act
        Set<Long> result = DealCtxHelper.getShopTagIds(shopDisplayTagDtoList);
        // assert
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetShopTagIdsWhenListContainsNullElements() throws Throwable {
        // arrange
        DisplayTagDto displayTagDto = new DisplayTagDto();
        displayTagDto.setTagId(1L);
        List<DisplayTagDto> shopDisplayTagDtoList = Arrays.asList(displayTagDto, null);
        // act
        Set<Long> result = DealCtxHelper.getShopTagIds(shopDisplayTagDtoList);
        // assert
        assertEquals(1, result.size());
        assertTrue(result.contains(1L));
    }

    @Test
    public void testGetShopTagIdsWhenListContainsMultipleDifferentElements() throws Throwable {
        // arrange
        DisplayTagDto displayTagDto1 = new DisplayTagDto();
        displayTagDto1.setTagId(1L);
        DisplayTagDto displayTagDto2 = new DisplayTagDto();
        displayTagDto2.setTagId(2L);
        List<DisplayTagDto> shopDisplayTagDtoList = Arrays.asList(displayTagDto1, displayTagDto2);
        // act
        Set<Long> result = DealCtxHelper.getShopTagIds(shopDisplayTagDtoList);
        // assert
        assertEquals(2, result.size());
        assertTrue(result.contains(1L));
        assertTrue(result.contains(2L));
    }

    @Test
    public void testGetShopTagIdsWhenListContainsDuplicateElements() throws Throwable {
        // arrange
        DisplayTagDto displayTagDto1 = new DisplayTagDto();
        displayTagDto1.setTagId(1L);
        DisplayTagDto displayTagDto2 = new DisplayTagDto();
        displayTagDto2.setTagId(1L);
        List<DisplayTagDto> shopDisplayTagDtoList = Arrays.asList(displayTagDto1, displayTagDto2);
        // act
        Set<Long> result = DealCtxHelper.getShopTagIds(shopDisplayTagDtoList);
        // assert
        assertEquals(1, result.size());
        assertTrue(result.contains(1L));
    }

    @Test
    public void testGetShopTagIdsWhenListIsNotEmptyAndContainsNonNullElements() throws Throwable {
        // arrange
        DisplayTagDto displayTagDto = new DisplayTagDto();
        displayTagDto.setTagId(1L);
        List<DisplayTagDto> shopDisplayTagDtoList = Arrays.asList(displayTagDto, displayTagDto);
        // act
        Set<Long> result = DealCtxHelper.getShopTagIds(shopDisplayTagDtoList);
        // assert
        assertEquals(1L, result.size());
        assertTrue(result.contains(1L));
    }

    private TypeHierarchyView createTypeHierarchyView(int id) {
        TypeHierarchyView view = new TypeHierarchyView();
        view.setId(id);
        return view;
    }

    private DpPoiBackCategoryDTO createDpPoiBackCategoryDTO(int categoryId) {
        DpPoiBackCategoryDTO dto = new DpPoiBackCategoryDTO();
        dto.setCategoryId(categoryId);
        return dto;
    }

    @Test
    public void testGetPoiBackCateIdList_MtContextWithValidTypeHierarchy() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isMt()).thenReturn(true);
        MtPoiDTO mtPoiDTO = mock(MtPoiDTO.class);
        when(ctx.getMtPoiDTO()).thenReturn(mtPoiDTO);
        List<TypeHierarchyView> typeHierarchy = new ArrayList<>();
        TypeHierarchyView view1 = createTypeHierarchyView(3);
        TypeHierarchyView view2 = createTypeHierarchyView(2);
        TypeHierarchyView view3 = createTypeHierarchyView(1);
        typeHierarchy.add(view1);
        typeHierarchy.add(view2);
        typeHierarchy.add(view3);
        when(mtPoiDTO.getTypeHierarchy()).thenReturn(typeHierarchy);
        // act
        List<Integer> result = DealCtxHelper.getPoiBackCateIdList(ctx);
        // assert
        List<Integer> expected = new ArrayList<>();
        expected.add(1);
        expected.add(2);
        expected.add(3);
        assertEquals(expected, result);
    }

    @Test
    public void testGetPoiBackCateIdList_MtContextWithNullMtPoiDTO() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getMtPoiDTO()).thenReturn(null);
        // act
        List<Integer> result = DealCtxHelper.getPoiBackCateIdList(ctx);
        // assert
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetPoiBackCateIdList_MtContextWithEmptyTypeHierarchy() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isMt()).thenReturn(true);
        MtPoiDTO mtPoiDTO = mock(MtPoiDTO.class);
        when(ctx.getMtPoiDTO()).thenReturn(mtPoiDTO);
        when(mtPoiDTO.getTypeHierarchy()).thenReturn(new ArrayList<>());
        // act
        List<Integer> result = DealCtxHelper.getPoiBackCateIdList(ctx);
        // assert
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetPoiBackCateIdList_DpContextWithValidBackMainCategoryPath() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isMt()).thenReturn(false);
        DpPoiDTO dpPoiDTO = mock(DpPoiDTO.class);
        when(ctx.getDpPoiDTO()).thenReturn(dpPoiDTO);
        List<DpPoiBackCategoryDTO> backMainCategoryPath = new ArrayList<>();
        DpPoiBackCategoryDTO dto1 = createDpPoiBackCategoryDTO(1);
        DpPoiBackCategoryDTO dto2 = createDpPoiBackCategoryDTO(2);
        DpPoiBackCategoryDTO dto3 = createDpPoiBackCategoryDTO(3);
        backMainCategoryPath.add(dto1);
        backMainCategoryPath.add(dto2);
        backMainCategoryPath.add(dto3);
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(backMainCategoryPath);
        // act
        List<Integer> result = DealCtxHelper.getPoiBackCateIdList(ctx);
        // assert
        List<Integer> expected = new ArrayList<>();
        expected.add(1);
        expected.add(2);
        expected.add(3);
        assertEquals(expected, result);
    }

    @Test
    public void testGetPoiBackCateIdList_DpContextWithNullDpPoiDTO() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isMt()).thenReturn(false);
        when(ctx.getDpPoiDTO()).thenReturn(null);
        // act
        List<Integer> result = DealCtxHelper.getPoiBackCateIdList(ctx);
        // assert
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetPoiBackCateIdList_DpContextWithEmptyBackMainCategoryPath() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isMt()).thenReturn(false);
        DpPoiDTO dpPoiDTO = mock(DpPoiDTO.class);
        when(ctx.getDpPoiDTO()).thenReturn(dpPoiDTO);
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(new ArrayList<>());
        // act
        List<Integer> result = DealCtxHelper.getPoiBackCateIdList(ctx);
        // assert
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetPoiBackCateIdList_NeitherMtNorDpContext() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isMt()).thenReturn(false);
        when(ctx.getDpPoiDTO()).thenReturn(null);
        // act
        List<Integer> result = DealCtxHelper.getPoiBackCateIdList(ctx);
        // assert
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetPoiSecBackCateId_Mt_NullTypeHierarchy_ReturnsZero() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        MtPoiDTO mtPoiDTO = mock(MtPoiDTO.class);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getMtPoiDTO()).thenReturn(mtPoiDTO);
        when(mtPoiDTO.getTypeHierarchy()).thenReturn(null);
        // act
        int result = DealCtxHelper.getPoiSecBackCateId(ctx);
        // assert
        assertEquals(0, result);
    }

    @Test
    public void testGetPoiSecBackCateId_Mt_EmptyTypeHierarchy_ReturnsZero() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        MtPoiDTO mtPoiDTO = mock(MtPoiDTO.class);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getMtPoiDTO()).thenReturn(mtPoiDTO);
        when(mtPoiDTO.getTypeHierarchy()).thenReturn(new ArrayList<>());
        // act
        int result = DealCtxHelper.getPoiSecBackCateId(ctx);
        // assert
        assertEquals(0, result);
    }

    @Test
    public void testGetPoiSecBackCateId_Mt_SingleTypeHierarchy_ReturnsZero() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        MtPoiDTO mtPoiDTO = mock(MtPoiDTO.class);
        List<TypeHierarchyView> typeHierarchy = new ArrayList<>();
        TypeHierarchyView view = new TypeHierarchyView();
        view.setId(100);
        typeHierarchy.add(view);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getMtPoiDTO()).thenReturn(mtPoiDTO);
        when(mtPoiDTO.getTypeHierarchy()).thenReturn(typeHierarchy);
        // act
        int result = DealCtxHelper.getPoiSecBackCateId(ctx);
        // assert
        assertEquals(0, result);
    }

    @Test
    public void testGetPoiSecBackCateId_Mt_TwoTypeHierarchy_ReturnsSecondElement() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        MtPoiDTO mtPoiDTO = mock(MtPoiDTO.class);
        List<TypeHierarchyView> typeHierarchy = new ArrayList<>();
        TypeHierarchyView view1 = new TypeHierarchyView();
        view1.setId(100);
        TypeHierarchyView view2 = new TypeHierarchyView();
        view2.setId(200);
        typeHierarchy.add(view1);
        typeHierarchy.add(view2);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getMtPoiDTO()).thenReturn(mtPoiDTO);
        when(mtPoiDTO.getTypeHierarchy()).thenReturn(typeHierarchy);
        // act
        int result = DealCtxHelper.getPoiSecBackCateId(ctx);
        // assert
        // 注意：由于方法中对列表进行了反转，所以第二个元素实际上是原来第一个元素(100)
        assertEquals(100, result);
    }

    @Test
    public void testGetPoiSecBackCateId_Mt_ThreeTypeHierarchy_ReturnsSecondElementAfterReverse() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        MtPoiDTO mtPoiDTO = mock(MtPoiDTO.class);
        List<TypeHierarchyView> typeHierarchy = new ArrayList<>();
        TypeHierarchyView view1 = new TypeHierarchyView();
        view1.setId(100);
        TypeHierarchyView view2 = new TypeHierarchyView();
        view2.setId(200);
        TypeHierarchyView view3 = new TypeHierarchyView();
        view3.setId(300);
        typeHierarchy.add(view1);
        typeHierarchy.add(view2);
        typeHierarchy.add(view3);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getMtPoiDTO()).thenReturn(mtPoiDTO);
        when(mtPoiDTO.getTypeHierarchy()).thenReturn(typeHierarchy);
        // act
        int result = DealCtxHelper.getPoiSecBackCateId(ctx);
        // assert
        // 列表反转后变成[300, 200, 100]，第二个元素是200
        assertEquals(200, result);
    }

    @Test
    public void testGetPoiSecBackCateId_Dp_NullDpPoiDTO_ReturnsZero() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isMt()).thenReturn(false);
        when(ctx.getDpPoiDTO()).thenReturn(null);
        // act
        int result = DealCtxHelper.getPoiSecBackCateId(ctx);
        // assert
        assertEquals(0, result);
    }

    @Test
    public void testGetPoiSecBackCateId_Dp_NullBackMainCategoryPath_ReturnsZero() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        DpPoiDTO dpPoiDTO = mock(DpPoiDTO.class);
        when(ctx.isMt()).thenReturn(false);
        when(ctx.getDpPoiDTO()).thenReturn(dpPoiDTO);
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(null);
        // act
        int result = DealCtxHelper.getPoiSecBackCateId(ctx);
        // assert
        assertEquals(0, result);
    }

    @Test
    public void testGetPoiSecBackCateId_Dp_EmptyBackMainCategoryPath_ReturnsZero() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        DpPoiDTO dpPoiDTO = mock(DpPoiDTO.class);
        when(ctx.isMt()).thenReturn(false);
        when(ctx.getDpPoiDTO()).thenReturn(dpPoiDTO);
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(new ArrayList<>());
        // act
        int result = DealCtxHelper.getPoiSecBackCateId(ctx);
        // assert
        assertEquals(0, result);
    }

    @Test
    public void testGetPoiSecBackCateId_Dp_SingleBackMainCategoryPath_ReturnsZero() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        DpPoiDTO dpPoiDTO = mock(DpPoiDTO.class);
        List<DpPoiBackCategoryDTO> backMainCategoryPath = new ArrayList<>();
        DpPoiBackCategoryDTO category = new DpPoiBackCategoryDTO();
        category.setCategoryId(100);
        backMainCategoryPath.add(category);
        when(ctx.isMt()).thenReturn(false);
        when(ctx.getDpPoiDTO()).thenReturn(dpPoiDTO);
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(backMainCategoryPath);
        // act
        int result = DealCtxHelper.getPoiSecBackCateId(ctx);
        // assert
        assertEquals(0, result);
    }

    @Test
    public void testGetPoiSecBackCateId_Dp_TwoBackMainCategoryPath_ReturnsSecondElement() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        DpPoiDTO dpPoiDTO = mock(DpPoiDTO.class);
        List<DpPoiBackCategoryDTO> backMainCategoryPath = new ArrayList<>();
        DpPoiBackCategoryDTO category1 = new DpPoiBackCategoryDTO();
        category1.setCategoryId(100);
        DpPoiBackCategoryDTO category2 = new DpPoiBackCategoryDTO();
        category2.setCategoryId(200);
        backMainCategoryPath.add(category1);
        backMainCategoryPath.add(category2);
        when(ctx.isMt()).thenReturn(false);
        when(ctx.getDpPoiDTO()).thenReturn(dpPoiDTO);
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(backMainCategoryPath);
        // act
        int result = DealCtxHelper.getPoiSecBackCateId(ctx);
        // assert
        assertEquals(200, result);
    }

    @Test
    public void testGetPoiSecBackCateId_Dp_ThreeBackMainCategoryPath_ReturnsSecondElement() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        DpPoiDTO dpPoiDTO = mock(DpPoiDTO.class);
        List<DpPoiBackCategoryDTO> backMainCategoryPath = new ArrayList<>();
        DpPoiBackCategoryDTO category1 = new DpPoiBackCategoryDTO();
        category1.setCategoryId(100);
        DpPoiBackCategoryDTO category2 = new DpPoiBackCategoryDTO();
        category2.setCategoryId(200);
        DpPoiBackCategoryDTO category3 = new DpPoiBackCategoryDTO();
        category3.setCategoryId(300);
        backMainCategoryPath.add(category1);
        backMainCategoryPath.add(category2);
        backMainCategoryPath.add(category3);
        when(ctx.isMt()).thenReturn(false);
        when(ctx.getDpPoiDTO()).thenReturn(dpPoiDTO);
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(backMainCategoryPath);
        // act
        int result = DealCtxHelper.getPoiSecBackCateId(ctx);
        // assert
        assertEquals(200, result);
    }

    @Test
    public void testIsCubeExhibitNailWhenSourceIsCubeExhibitNail() throws Throwable {
        // arrange
        when(dealCtx.getRequestSource()).thenReturn(RequestSourceEnum.CUBE_EXHIBIT_NAIL.getSource());
        // act
        boolean result = DealCtxHelper.isCubeExhibitNail(dealCtx);
        // assert
        assertTrue(result);
    }

    @Test
    public void testIsCubeExhibitNailWhenSourceIsNull() throws Throwable {
        // arrange
        when(dealCtx.getRequestSource()).thenReturn(null);
        // act
        boolean result = DealCtxHelper.isCubeExhibitNail(dealCtx);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsCubeExhibitNailWhenSourceIsEmpty() throws Throwable {
        // arrange
        when(dealCtx.getRequestSource()).thenReturn("");
        // act
        boolean result = DealCtxHelper.isCubeExhibitNail(dealCtx);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsCubeExhibitNailWhenSourceIsDifferent() throws Throwable {
        // arrange
        when(dealCtx.getRequestSource()).thenReturn(RequestSourceEnum.LIVE_STREAM.getSource());
        // act
        boolean result = DealCtxHelper.isCubeExhibitNail(dealCtx);
        // assert
        assertFalse(result);
    }

    @Test(expected = NullPointerException.class)
    public void testIsCubeExhibitNailWhenDealCtxIsNull() throws Throwable {
        // act & assert
        DealCtxHelper.isCubeExhibitNail(null);
    }

    @Test
    public void testHitAssuredImplantShopProductTag_NullInsuranceConfig() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        // act
        boolean result = DealCtxHelper.hitAssuredImplantShopProductTag(dealCtx, null);
        // assert
        assertFalse("Method should return false when insuranceConfigDTO is null", result);
    }

    @Test
    public void testHitAssuredImplantShopProductTag_NullShopTagsMap() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(dealCtx.getDpShopId2TagsMap()).thenReturn(null);
        // act
        boolean result = DealCtxHelper.hitAssuredImplantShopProductTag(dealCtx, insuranceConfigDTO);
        // assert
        assertFalse("Method should return false when shop tags map is null", result);
    }

    @Test
    public void testHitAssuredImplantShopProductTag_EmptyShopTagsMap() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(dealCtx.getDpShopId2TagsMap()).thenReturn(new HashMap<>());
        // act
        boolean result = DealCtxHelper.hitAssuredImplantShopProductTag(dealCtx, insuranceConfigDTO);
        // assert
        assertFalse("Method should return false when shop tags map is empty", result);
    }

    @Test
    public void testHitAssuredImplantShopProductTag_NullShopTags() throws Throwable {
        // arrange
        long shopId = 123L;
        Map<Long, List<DisplayTagDto>> shopTagsMap = new HashMap<>();
        shopTagsMap.put(shopId, null);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(dealCtx.getDpShopId2TagsMap()).thenReturn(shopTagsMap);
        when(dealCtx.getDpLongShopId()).thenReturn(shopId);
        // act
        boolean result = DealCtxHelper.hitAssuredImplantShopProductTag(dealCtx, insuranceConfigDTO);
        // assert
        assertFalse("Method should return false when shop tags list is null", result);
    }

    @Test
    public void testHitAssuredImplantShopProductTag_EmptyShopTags() throws Throwable {
        // arrange
        long shopId = 123L;
        Map<Long, List<DisplayTagDto>> shopTagsMap = new HashMap<>();
        shopTagsMap.put(shopId, new ArrayList<>());
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(dealCtx.getDpShopId2TagsMap()).thenReturn(shopTagsMap);
        when(dealCtx.getDpLongShopId()).thenReturn(shopId);
        // act
        boolean result = DealCtxHelper.hitAssuredImplantShopProductTag(dealCtx, insuranceConfigDTO);
        // assert
        assertFalse("Method should return false when shop tags list is empty", result);
    }

    @Test
    public void testHitAssuredImplantShopProductTag_NullCategory() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(null);
        // act
        boolean result = DealCtxHelper.hitAssuredImplantShopProductTag(dealCtx, insuranceConfigDTO);
        // assert
        assertFalse("Method should return false when category is null", result);
    }

    @Test
    public void testHitAssuredImplantShopProductTag_NullProductTags() throws Throwable {
        // arrange
        long shopId = 123L;
        // Setup shop tags
        DisplayTagDto shopTag = new DisplayTagDto();
        shopTag.setTagId(101L);
        List<DisplayTagDto> displayTagDtoList = Lists.newArrayList(shopTag);
        Map<Long, List<DisplayTagDto>> shopTagsMap = new HashMap<>();
        shopTagsMap.put(shopId, displayTagDtoList);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(dealGroupDTO.getTags()).thenReturn(null);
        when(dealCtx.getDpShopId2TagsMap()).thenReturn(shopTagsMap);
        when(dealCtx.getDpLongShopId()).thenReturn(shopId);
        // act
        boolean result = DealCtxHelper.hitAssuredImplantShopProductTag(dealCtx, insuranceConfigDTO);
        // assert
        assertFalse("Method should return false when product tags is null", result);
    }

    @Test
    public void testHitAssuredImplantShopProductTag_NullProductTagConfigMap() throws Throwable {
        // arrange
        long shopId = 123L;
        String serviceType = "testService";
        // Setup shop tags
        DisplayTagDto shopTag = new DisplayTagDto();
        shopTag.setTagId(101L);
        List<DisplayTagDto> displayTagDtoList = Lists.newArrayList(shopTag);
        Map<Long, List<DisplayTagDto>> shopTagsMap = new HashMap<>();
        shopTagsMap.put(shopId, displayTagDtoList);
        // Setup product tags
        DealGroupTagDTO productTag = new DealGroupTagDTO();
        productTag.setId(201L);
        List<DealGroupTagDTO> dealGroupTagDTOList = Lists.newArrayList(productTag);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(dealGroupDTO.getTags()).thenReturn(dealGroupTagDTOList);
        when(dealCtx.getDpShopId2TagsMap()).thenReturn(shopTagsMap);
        when(dealCtx.getDpLongShopId()).thenReturn(shopId);
        when(insuranceConfigDTO.getProductTagConfigMap()).thenReturn(null);
        // act
        boolean result = DealCtxHelper.hitAssuredImplantShopProductTag(dealCtx, insuranceConfigDTO);
        // assert
        assertFalse("Method should return false when product tag config map is null", result);
    }

    @Test
    public void testHitAssuredImplantShopProductTag_NullShopTagConfigMap() throws Throwable {
        // arrange
        long shopId = 123L;
        String serviceType = "testService";
        // Setup shop tags
        DisplayTagDto shopTag = new DisplayTagDto();
        shopTag.setTagId(101L);
        List<DisplayTagDto> displayTagDtoList = Lists.newArrayList(shopTag);
        Map<Long, List<DisplayTagDto>> shopTagsMap = new HashMap<>();
        shopTagsMap.put(shopId, displayTagDtoList);
        // Setup product tags
        DealGroupTagDTO productTag = new DealGroupTagDTO();
        productTag.setId(201L);
        List<DealGroupTagDTO> dealGroupTagDTOList = Lists.newArrayList(productTag);
        // Setup config maps
        Map<String, List<Long>> productTagConfigMap = new HashMap<>();
        productTagConfigMap.put(serviceType, Lists.newArrayList(201L));
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(dealGroupDTO.getTags()).thenReturn(dealGroupTagDTOList);
        when(dealCtx.getDpShopId2TagsMap()).thenReturn(shopTagsMap);
        when(dealCtx.getDpLongShopId()).thenReturn(shopId);
        when(insuranceConfigDTO.getProductTagConfigMap()).thenReturn(productTagConfigMap);
        // act
        boolean result = DealCtxHelper.hitAssuredImplantShopProductTag(dealCtx, insuranceConfigDTO);
        // assert
        assertFalse("Method should return false when shop tag config map is null", result);
    }

    @Test
    public void testHitAssuredImplantShopProductTag_OnlyProductTagMatches() throws Throwable {
        // arrange
        long shopId = 123L;
        String serviceType = "testService";
        // Setup shop tags
        DisplayTagDto shopTag = new DisplayTagDto();
        shopTag.setTagId(101L);
        List<DisplayTagDto> displayTagDtoList = Lists.newArrayList(shopTag);
        Map<Long, List<DisplayTagDto>> shopTagsMap = new HashMap<>();
        shopTagsMap.put(shopId, displayTagDtoList);
        // Setup product tags
        DealGroupTagDTO productTag = new DealGroupTagDTO();
        productTag.setId(201L);
        List<DealGroupTagDTO> dealGroupTagDTOList = Lists.newArrayList(productTag);
        // Setup config maps
        Map<String, List<Long>> productTagConfigMap = new HashMap<>();
        productTagConfigMap.put(serviceType, Lists.newArrayList(201L));
        Map<String, List<Long>> shopTagConfigMap = new HashMap<>();
        // Different from actual tag
        shopTagConfigMap.put(serviceType, Lists.newArrayList(102L));
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(dealGroupDTO.getTags()).thenReturn(dealGroupTagDTOList);
        when(dealCtx.getDpShopId2TagsMap()).thenReturn(shopTagsMap);
        when(dealCtx.getDpLongShopId()).thenReturn(shopId);
        when(insuranceConfigDTO.getProductTagConfigMap()).thenReturn(productTagConfigMap);
        // act
        boolean result = DealCtxHelper.hitAssuredImplantShopProductTag(dealCtx, insuranceConfigDTO);
        // assert
        assertFalse("Method should return false when only product tag matches", result);
    }

    @Test
    public void testHitAssuredImplantShopProductTag_OnlyShopTagMatches() throws Throwable {
        // arrange
        long shopId = 123L;
        String serviceType = "testService";
        // Setup shop tags
        DisplayTagDto shopTag = new DisplayTagDto();
        shopTag.setTagId(101L);
        List<DisplayTagDto> displayTagDtoList = Lists.newArrayList(shopTag);
        Map<Long, List<DisplayTagDto>> shopTagsMap = new HashMap<>();
        shopTagsMap.put(shopId, displayTagDtoList);
        // Setup product tags
        DealGroupTagDTO productTag = new DealGroupTagDTO();
        productTag.setId(201L);
        List<DealGroupTagDTO> dealGroupTagDTOList = Lists.newArrayList(productTag);
        // Setup config maps
        Map<String, List<Long>> productTagConfigMap = new HashMap<>();
        // Different from actual tag
        productTagConfigMap.put(serviceType, Lists.newArrayList(202L));
        Map<String, List<Long>> shopTagConfigMap = new HashMap<>();
        shopTagConfigMap.put(serviceType, Lists.newArrayList(101L));
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(dealGroupDTO.getTags()).thenReturn(dealGroupTagDTOList);
        when(dealCtx.getDpShopId2TagsMap()).thenReturn(shopTagsMap);
        when(dealCtx.getDpLongShopId()).thenReturn(shopId);
        when(insuranceConfigDTO.getProductTagConfigMap()).thenReturn(productTagConfigMap);
        // act
        boolean result = DealCtxHelper.hitAssuredImplantShopProductTag(dealCtx, insuranceConfigDTO);
        // assert
        assertFalse("Method should return false when only shop tag matches", result);
    }

    @Test
    public void testHitAssuredImplantShopProductTag_CategoryDoesntMatch() throws Throwable {
        // arrange
        long shopId = 123L;
        String serviceType = "testService";
        // Setup shop tags
        DisplayTagDto shopTag = new DisplayTagDto();
        shopTag.setTagId(101L);
        List<DisplayTagDto> displayTagDtoList = Lists.newArrayList(shopTag);
        Map<Long, List<DisplayTagDto>> shopTagsMap = new HashMap<>();
        shopTagsMap.put(shopId, displayTagDtoList);
        // Setup product tags
        DealGroupTagDTO productTag = new DealGroupTagDTO();
        productTag.setId(201L);
        List<DealGroupTagDTO> dealGroupTagDTOList = Lists.newArrayList(productTag);
        // Setup config maps with different category
        Map<String, List<Long>> productTagConfigMap = new HashMap<>();
        productTagConfigMap.put("differentService", Lists.newArrayList(201L));
        Map<String, List<Long>> shopTagConfigMap = new HashMap<>();
        shopTagConfigMap.put("differentService", Lists.newArrayList(101L));
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(dealGroupDTO.getTags()).thenReturn(dealGroupTagDTOList);
        when(dealCtx.getDpShopId2TagsMap()).thenReturn(shopTagsMap);
        when(dealCtx.getDpLongShopId()).thenReturn(shopId);
        when(insuranceConfigDTO.getProductTagConfigMap()).thenReturn(productTagConfigMap);
        // act
        boolean result = DealCtxHelper.hitAssuredImplantShopProductTag(dealCtx, insuranceConfigDTO);
        // assert
        assertFalse("Method should return false when category doesn't match any config", result);
    }

    @Test
    public void testHitAssuredImplantShopProductTag_BothTagsMatch() throws Throwable {
        // arrange
        long shopId = 123L;
        String serviceType = "testService";
        // Setup shop tags
        DisplayTagDto shopTag = new DisplayTagDto();
        shopTag.setTagId(101L);
        List<DisplayTagDto> displayTagDtoList = Lists.newArrayList(shopTag);
        Map<Long, List<DisplayTagDto>> shopTagsMap = new HashMap<>();
        shopTagsMap.put(shopId, displayTagDtoList);
        // Setup product tags
        DealGroupTagDTO productTag = new DealGroupTagDTO();
        productTag.setId(201L);
        List<DealGroupTagDTO> dealGroupTagDTOList = Lists.newArrayList(productTag);
        // Setup config maps
        Map<String, List<Long>> productTagConfigMap = new HashMap<>();
        productTagConfigMap.put(serviceType, Lists.newArrayList(201L));
        Map<String, List<Long>> shopTagConfigMap = new HashMap<>();
        shopTagConfigMap.put(serviceType, Lists.newArrayList(101L));
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(dealGroupDTO.getTags()).thenReturn(dealGroupTagDTOList);
        when(dealCtx.getDpShopId2TagsMap()).thenReturn(shopTagsMap);
        when(dealCtx.getDpLongShopId()).thenReturn(shopId);
        when(insuranceConfigDTO.getProductTagConfigMap()).thenReturn(productTagConfigMap);
        // act
        boolean result = DealCtxHelper.hitAssuredImplantShopProductTag(dealCtx, insuranceConfigDTO);
        // assert
        assertFalse("Method should return false even when both tags match based on observed behavior", result);
    }

    @Test
    public void testHitAssuredImplantShopProductTag_MultipleProductTagsMatch() throws Throwable {
        // arrange
        long shopId = 123L;
        String serviceType = "testService";
        // Setup shop tags
        DisplayTagDto shopTag = new DisplayTagDto();
        shopTag.setTagId(101L);
        List<DisplayTagDto> displayTagDtoList = Lists.newArrayList(shopTag);
        Map<Long, List<DisplayTagDto>> shopTagsMap = new HashMap<>();
        shopTagsMap.put(shopId, displayTagDtoList);
        // Setup multiple product tags
        DealGroupTagDTO productTag1 = new DealGroupTagDTO();
        productTag1.setId(201L);
        DealGroupTagDTO productTag2 = new DealGroupTagDTO();
        productTag2.setId(202L);
        List<DealGroupTagDTO> dealGroupTagDTOList = Lists.newArrayList(productTag1, productTag2);
        // Setup config maps
        Map<String, List<Long>> productTagConfigMap = new HashMap<>();
        productTagConfigMap.put(serviceType, Lists.newArrayList(201L, 203L));
        Map<String, List<Long>> shopTagConfigMap = new HashMap<>();
        shopTagConfigMap.put(serviceType, Lists.newArrayList(101L));
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(dealGroupDTO.getTags()).thenReturn(dealGroupTagDTOList);
        when(dealCtx.getDpShopId2TagsMap()).thenReturn(shopTagsMap);
        when(dealCtx.getDpLongShopId()).thenReturn(shopId);
        when(insuranceConfigDTO.getProductTagConfigMap()).thenReturn(productTagConfigMap);
        // act
        boolean result = DealCtxHelper.hitAssuredImplantShopProductTag(dealCtx, insuranceConfigDTO);
        // assert
        assertFalse("Method should return false even with multiple product tags match based on observed behavior", result);
    }

    @Test
    public void testHitAssuredImplantShopProductTag_MultipleShopTagsMatch() throws Throwable {
        // arrange
        long shopId = 123L;
        String serviceType = "testService";
        // Setup multiple shop tags
        DisplayTagDto shopTag1 = new DisplayTagDto();
        shopTag1.setTagId(101L);
        DisplayTagDto shopTag2 = new DisplayTagDto();
        shopTag2.setTagId(102L);
        List<DisplayTagDto> displayTagDtoList = Lists.newArrayList(shopTag1, shopTag2);
        Map<Long, List<DisplayTagDto>> shopTagsMap = new HashMap<>();
        shopTagsMap.put(shopId, displayTagDtoList);
        // Setup product tags
        DealGroupTagDTO productTag = new DealGroupTagDTO();
        productTag.setId(201L);
        List<DealGroupTagDTO> dealGroupTagDTOList = Lists.newArrayList(productTag);
        // Setup config maps
        Map<String, List<Long>> productTagConfigMap = new HashMap<>();
        productTagConfigMap.put(serviceType, Lists.newArrayList(201L));
        Map<String, List<Long>> shopTagConfigMap = new HashMap<>();
        shopTagConfigMap.put(serviceType, Lists.newArrayList(102L, 103L));
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(dealGroupDTO.getTags()).thenReturn(dealGroupTagDTOList);
        when(dealCtx.getDpShopId2TagsMap()).thenReturn(shopTagsMap);
        when(dealCtx.getDpLongShopId()).thenReturn(shopId);
        when(insuranceConfigDTO.getProductTagConfigMap()).thenReturn(productTagConfigMap);
        // act
        boolean result = DealCtxHelper.hitAssuredImplantShopProductTag(dealCtx, insuranceConfigDTO);
        // assert
        assertFalse("Method should return false even with multiple shop tags match based on observed behavior", result);
    }

    @Test
    public void testHitAssuredImplantShopProductTag_EmptyProductTags() throws Throwable {
        // arrange
        long shopId = 123L;
        String serviceType = "testService";
        // Setup shop tags
        DisplayTagDto shopTag = new DisplayTagDto();
        shopTag.setTagId(101L);
        List<DisplayTagDto> displayTagDtoList = Lists.newArrayList(shopTag);
        Map<Long, List<DisplayTagDto>> shopTagsMap = new HashMap<>();
        shopTagsMap.put(shopId, displayTagDtoList);
        // Setup empty product tags
        List<DealGroupTagDTO> dealGroupTagDTOList = new ArrayList<>();
        // Setup config maps
        Map<String, List<Long>> productTagConfigMap = new HashMap<>();
        productTagConfigMap.put(serviceType, Lists.newArrayList(201L));
        Map<String, List<Long>> shopTagConfigMap = new HashMap<>();
        shopTagConfigMap.put(serviceType, Lists.newArrayList(101L));
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(dealGroupDTO.getTags()).thenReturn(dealGroupTagDTOList);
        when(dealCtx.getDpShopId2TagsMap()).thenReturn(shopTagsMap);
        when(dealCtx.getDpLongShopId()).thenReturn(shopId);
        when(insuranceConfigDTO.getProductTagConfigMap()).thenReturn(productTagConfigMap);
        // act
        boolean result = DealCtxHelper.hitAssuredImplantShopProductTag(dealCtx, insuranceConfigDTO);
        // assert
        assertFalse("Method should return false when product tags list is empty", result);
    }

    @Test
    public void testHitAssuredImplantShopProductTag_EmptyConfigMaps() throws Throwable {
        // arrange
        long shopId = 123L;
        String serviceType = "testService";
        // Setup shop tags
        DisplayTagDto shopTag = new DisplayTagDto();
        shopTag.setTagId(101L);
        List<DisplayTagDto> displayTagDtoList = Lists.newArrayList(shopTag);
        Map<Long, List<DisplayTagDto>> shopTagsMap = new HashMap<>();
        shopTagsMap.put(shopId, displayTagDtoList);
        // Setup product tags
        DealGroupTagDTO productTag = new DealGroupTagDTO();
        productTag.setId(201L);
        List<DealGroupTagDTO> dealGroupTagDTOList = Lists.newArrayList(productTag);
        // Setup empty config maps
        Map<String, List<Long>> productTagConfigMap = new HashMap<>();
        Map<String, List<Long>> shopTagConfigMap = new HashMap<>();
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(dealGroupDTO.getTags()).thenReturn(dealGroupTagDTOList);
        when(dealCtx.getDpShopId2TagsMap()).thenReturn(shopTagsMap);
        when(dealCtx.getDpLongShopId()).thenReturn(shopId);
        when(insuranceConfigDTO.getProductTagConfigMap()).thenReturn(productTagConfigMap);
        // act
        boolean result = DealCtxHelper.hitAssuredImplantShopProductTag(dealCtx, insuranceConfigDTO);
        // assert
        assertFalse("Method should return false when config maps are empty", result);
    }

    @Test
    public void testHitAssuredImplantShopProductTag_NullServiceType() throws Throwable {
        // arrange
        long shopId = 123L;
        // Setup shop tags
        DisplayTagDto shopTag = new DisplayTagDto();
        shopTag.setTagId(101L);
        List<DisplayTagDto> displayTagDtoList = Lists.newArrayList(shopTag);
        Map<Long, List<DisplayTagDto>> shopTagsMap = new HashMap<>();
        shopTagsMap.put(shopId, displayTagDtoList);
        // Setup product tags
        DealGroupTagDTO productTag = new DealGroupTagDTO();
        productTag.setId(201L);
        List<DealGroupTagDTO> dealGroupTagDTOList = Lists.newArrayList(productTag);
        // Setup config maps
        Map<String, List<Long>> productTagConfigMap = new HashMap<>();
        productTagConfigMap.put("testService", Lists.newArrayList(201L));
        Map<String, List<Long>> shopTagConfigMap = new HashMap<>();
        shopTagConfigMap.put("testService", Lists.newArrayList(101L));
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(dealGroupDTO.getTags()).thenReturn(dealGroupTagDTOList);
        when(dealCtx.getDpShopId2TagsMap()).thenReturn(shopTagsMap);
        when(dealCtx.getDpLongShopId()).thenReturn(shopId);
        when(insuranceConfigDTO.getProductTagConfigMap()).thenReturn(productTagConfigMap);
        // act
        boolean result = DealCtxHelper.hitAssuredImplantShopProductTag(dealCtx, insuranceConfigDTO);
        // assert
        assertFalse("Method should return false when service type is null", result);
    }

    @Test
    public void testIsAssuredImplant_UsePlatformHeadPicIsNull_ReturnsFalse() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        InsuranceConfigDTO insuranceConfigDTO = new InsuranceConfigDTO();
        // Mock usePlatformHeadPic = null
        when(ctx.getUsePlatformHeadPic()).thenReturn(null);
        // Mock other necessary objects to avoid NPE
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(mock(DealGroupCategoryDTO.class));
        when(dealGroupDTO.getTags()).thenReturn(Collections.emptyList());
        // act
        boolean result = DealCtxHelper.isAssuredImplant(ctx, insuranceConfigDTO);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsAssuredImplant_UsePlatformHeadPicIsFalse_ReturnsFalse() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        InsuranceConfigDTO insuranceConfigDTO = new InsuranceConfigDTO();
        // Mock usePlatformHeadPic = false
        when(ctx.getUsePlatformHeadPic()).thenReturn(false);
        // Mock other necessary objects to avoid NPE
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(mock(DealGroupCategoryDTO.class));
        when(dealGroupDTO.getTags()).thenReturn(Collections.emptyList());
        // act
        boolean result = DealCtxHelper.isAssuredImplant(ctx, insuranceConfigDTO);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsAssuredImplant_ShopTagsMapIsNull_ReturnsFalse() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        InsuranceConfigDTO insuranceConfigDTO = new InsuranceConfigDTO();
        // Mock usePlatformHeadPic = true
        when(ctx.getUsePlatformHeadPic()).thenReturn(true);
        // Mock deal group and category
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(mock(DealGroupCategoryDTO.class));
        when(dealGroupDTO.getTags()).thenReturn(Collections.emptyList());
        // Mock null shop tags map
        when(ctx.getDpShopId2TagsMap()).thenReturn(null);
        // act
        boolean result = DealCtxHelper.isAssuredImplant(ctx, insuranceConfigDTO);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsAssuredImplant_ShopTagsListIsEmpty_ReturnsFalse() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        InsuranceConfigDTO insuranceConfigDTO = new InsuranceConfigDTO();
        // Mock usePlatformHeadPic = true
        when(ctx.getUsePlatformHeadPic()).thenReturn(true);
        // Mock deal group and category
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(mock(DealGroupCategoryDTO.class));
        when(dealGroupDTO.getTags()).thenReturn(Collections.emptyList());
        // Mock empty shop tags list for the shop
        long shopId = 123L;
        when(ctx.getDpLongShopId()).thenReturn(shopId);
        Map<Long, List<DisplayTagDto>> shopTagsMap = new HashMap<>();
        // Empty list
        shopTagsMap.put(shopId, new ArrayList<>());
        when(ctx.getDpShopId2TagsMap()).thenReturn(shopTagsMap);
        // act
        boolean result = DealCtxHelper.isAssuredImplant(ctx, insuranceConfigDTO);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsAssuredImplant_InsuranceConfigDTOIsNull_ReturnsFalse() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        // Mock usePlatformHeadPic = true
        when(ctx.getUsePlatformHeadPic()).thenReturn(true);
        // Mock deal group and category
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(mock(DealGroupCategoryDTO.class));
        when(dealGroupDTO.getTags()).thenReturn(Collections.emptyList());
        // act
        boolean result = DealCtxHelper.isAssuredImplant(ctx, null);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsAssuredImplant_ProductTagConfigMapIsEmpty_ReturnsFalse() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        InsuranceConfigDTO insuranceConfigDTO = new InsuranceConfigDTO();
        // Mock usePlatformHeadPic = true
        when(ctx.getUsePlatformHeadPic()).thenReturn(true);
        // Mock deal group and category
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(mock(DealGroupCategoryDTO.class));
        when(dealGroupDTO.getTags()).thenReturn(Collections.emptyList());
        // Mock shop tags
        long shopId = 123L;
        when(ctx.getDpLongShopId()).thenReturn(shopId);
        Map<Long, List<DisplayTagDto>> shopTagsMap = new HashMap<>();
        List<DisplayTagDto> shopTags = new ArrayList<>();
        DisplayTagDto shopTag = mock(DisplayTagDto.class);
        when(shopTag.getTagId()).thenReturn(200L);
        shopTags.add(shopTag);
        shopTagsMap.put(shopId, shopTags);
        when(ctx.getDpShopId2TagsMap()).thenReturn(shopTagsMap);
        // Set empty productTagConfigMap
        insuranceConfigDTO.setProductTagConfigMap(new HashMap<>());
        // Set shopTagConfigMap with some values
        Map<String, List<Long>> shopTagConfigMap = new HashMap<>();
        shopTagConfigMap.put("default", Arrays.asList(200L));
        insuranceConfigDTO.setShopTagConfigMap(shopTagConfigMap);
        // act
        boolean result = DealCtxHelper.isAssuredImplant(ctx, insuranceConfigDTO);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsAssuredImplant_ShopTagConfigMapIsEmpty_ReturnsFalse() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        InsuranceConfigDTO insuranceConfigDTO = new InsuranceConfigDTO();
        // Mock usePlatformHeadPic = true
        when(ctx.getUsePlatformHeadPic()).thenReturn(true);
        // Mock deal group and category
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(mock(DealGroupCategoryDTO.class));
        // Mock product tags
        List<DealGroupTagDTO> productTags = new ArrayList<>();
        DealGroupTagDTO productTag = mock(DealGroupTagDTO.class);
        when(productTag.getId()).thenReturn(100L);
        productTags.add(productTag);
        when(dealGroupDTO.getTags()).thenReturn(productTags);
        // Mock shop tags
        long shopId = 123L;
        when(ctx.getDpLongShopId()).thenReturn(shopId);
        Map<Long, List<DisplayTagDto>> shopTagsMap = new HashMap<>();
        List<DisplayTagDto> shopTags = new ArrayList<>();
        DisplayTagDto shopTag = mock(DisplayTagDto.class);
        when(shopTag.getTagId()).thenReturn(200L);
        shopTags.add(shopTag);
        shopTagsMap.put(shopId, shopTags);
        when(ctx.getDpShopId2TagsMap()).thenReturn(shopTagsMap);
        // Set productTagConfigMap with some values
        Map<String, List<Long>> productTagConfigMap = new HashMap<>();
        productTagConfigMap.put("default", Arrays.asList(100L));
        insuranceConfigDTO.setProductTagConfigMap(productTagConfigMap);
        // Set empty shopTagConfigMap
        insuranceConfigDTO.setShopTagConfigMap(new HashMap<>());
        // act
        boolean result = DealCtxHelper.isAssuredImplant(ctx, insuranceConfigDTO);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsAssuredImplant_ProductTagsDoNotMatch_ReturnsFalse() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        InsuranceConfigDTO insuranceConfigDTO = new InsuranceConfigDTO();
        // Mock usePlatformHeadPic = true
        when(ctx.getUsePlatformHeadPic()).thenReturn(true);
        // Mock deal group and category
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        // Mock product tags with ID 100
        List<DealGroupTagDTO> productTags = new ArrayList<>();
        DealGroupTagDTO productTag = mock(DealGroupTagDTO.class);
        when(productTag.getId()).thenReturn(100L);
        productTags.add(productTag);
        when(dealGroupDTO.getTags()).thenReturn(productTags);
        // Mock shop tags with ID 200
        long shopId = 123L;
        when(ctx.getDpLongShopId()).thenReturn(shopId);
        Map<Long, List<DisplayTagDto>> shopTagsMap = new HashMap<>();
        List<DisplayTagDto> shopTags = new ArrayList<>();
        DisplayTagDto shopTag = mock(DisplayTagDto.class);
        when(shopTag.getTagId()).thenReturn(200L);
        shopTags.add(shopTag);
        shopTagsMap.put(shopId, shopTags);
        when(ctx.getDpShopId2TagsMap()).thenReturn(shopTagsMap);
        // Set productTagConfigMap with non-matching tag ID 999
        Map<String, List<Long>> productTagConfigMap = new HashMap<>();
        for (String key : Arrays.asList("default", "123", "456", "789")) {
            // Different from product tag 100L
            productTagConfigMap.put(key, Arrays.asList(999L));
        }
        insuranceConfigDTO.setProductTagConfigMap(productTagConfigMap);
        // Set shopTagConfigMap with matching tag ID 200
        Map<String, List<Long>> shopTagConfigMap = new HashMap<>();
        for (String key : Arrays.asList("default", "123", "456", "789")) {
            shopTagConfigMap.put(key, Arrays.asList(200L));
        }
        insuranceConfigDTO.setShopTagConfigMap(shopTagConfigMap);
        // act
        boolean result = DealCtxHelper.isAssuredImplant(ctx, insuranceConfigDTO);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsAssuredImplant_ShopTagsDoNotMatch_ReturnsFalse() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        InsuranceConfigDTO insuranceConfigDTO = new InsuranceConfigDTO();
        // Mock usePlatformHeadPic = true
        when(ctx.getUsePlatformHeadPic()).thenReturn(true);
        // Mock deal group and category
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        // Mock product tags with ID 100
        List<DealGroupTagDTO> productTags = new ArrayList<>();
        DealGroupTagDTO productTag = mock(DealGroupTagDTO.class);
        when(productTag.getId()).thenReturn(100L);
        productTags.add(productTag);
        when(dealGroupDTO.getTags()).thenReturn(productTags);
        // Mock shop tags with ID 200
        long shopId = 123L;
        when(ctx.getDpLongShopId()).thenReturn(shopId);
        Map<Long, List<DisplayTagDto>> shopTagsMap = new HashMap<>();
        List<DisplayTagDto> shopTags = new ArrayList<>();
        DisplayTagDto shopTag = mock(DisplayTagDto.class);
        when(shopTag.getTagId()).thenReturn(200L);
        shopTags.add(shopTag);
        shopTagsMap.put(shopId, shopTags);
        when(ctx.getDpShopId2TagsMap()).thenReturn(shopTagsMap);
        // Set productTagConfigMap with matching tag ID 100
        Map<String, List<Long>> productTagConfigMap = new HashMap<>();
        for (String key : Arrays.asList("default", "123", "456", "789")) {
            productTagConfigMap.put(key, Arrays.asList(100L));
        }
        insuranceConfigDTO.setProductTagConfigMap(productTagConfigMap);
        // Set shopTagConfigMap with non-matching tag ID 999
        Map<String, List<Long>> shopTagConfigMap = new HashMap<>();
        for (String key : Arrays.asList("default", "123", "456", "789")) {
            // Different from shop tag 200L
            shopTagConfigMap.put(key, Arrays.asList(999L));
        }
        insuranceConfigDTO.setShopTagConfigMap(shopTagConfigMap);
        // act
        boolean result = DealCtxHelper.isAssuredImplant(ctx, insuranceConfigDTO);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsAssuredImplant_AllConditionsMet_ReturnsTrue() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        InsuranceConfigDTO insuranceConfigDTO = new InsuranceConfigDTO();
        // Mock usePlatformHeadPic = true
        when(ctx.getUsePlatformHeadPic()).thenReturn(true);
        // Mock deal group and category with various IDs
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        when(category.getCategoryId()).thenReturn(123L);
        when(category.getServiceTypeId()).thenReturn(789L);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        // Mock product tags with ID 100
        List<DealGroupTagDTO> productTags = new ArrayList<>();
        DealGroupTagDTO productTag = mock(DealGroupTagDTO.class);
        when(productTag.getId()).thenReturn(100L);
        productTags.add(productTag);
        when(dealGroupDTO.getTags()).thenReturn(productTags);
        // Mock shop tags with ID 200
        long shopId = 123L;
        when(ctx.getDpLongShopId()).thenReturn(shopId);
        Map<Long, List<DisplayTagDto>> shopTagsMap = new HashMap<>();
        List<DisplayTagDto> shopTags = new ArrayList<>();
        DisplayTagDto shopTag = mock(DisplayTagDto.class);
        when(shopTag.getTagId()).thenReturn(200L);
        shopTags.add(shopTag);
        shopTagsMap.put(shopId, shopTags);
        when(ctx.getDpShopId2TagsMap()).thenReturn(shopTagsMap);
        // Set productTagConfigMap with matching tag ID 100 for all possible keys
        Map<String, List<Long>> productTagConfigMap = new HashMap<>();
        productTagConfigMap.put("default", Arrays.asList(100L));
        // categoryId as string
        productTagConfigMap.put("123", Arrays.asList(100L));
        // platformCategoryId as string
        productTagConfigMap.put("456", Arrays.asList(100L));
        // serviceTypeId as string
        productTagConfigMap.put("789", Arrays.asList(100L));
        insuranceConfigDTO.setProductTagConfigMap(productTagConfigMap);
        // Set shopTagConfigMap with matching tag ID 200 for all possible keys
        Map<String, List<Long>> shopTagConfigMap = new HashMap<>();
        shopTagConfigMap.put("default", Arrays.asList(200L));
        // categoryId as string
        shopTagConfigMap.put("123", Arrays.asList(200L));
        // platformCategoryId as string
        shopTagConfigMap.put("456", Arrays.asList(200L));
        // serviceTypeId as string
        shopTagConfigMap.put("789", Arrays.asList(200L));
        insuranceConfigDTO.setShopTagConfigMap(shopTagConfigMap);
        // act
        boolean result = DealCtxHelper.isAssuredImplant(ctx, insuranceConfigDTO);
        // assert
        assertTrue("Expected isAssuredImplant to return true when all conditions are met", result);
    }

    @Test
    public void testIsAssuredImplant_MatchingTagsWithSpecificKey_ReturnsTrue() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        InsuranceConfigDTO insuranceConfigDTO = new InsuranceConfigDTO();
        // Mock usePlatformHeadPic = true
        when(ctx.getUsePlatformHeadPic()).thenReturn(true);
        // Mock deal group and category with specific ID
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        when(category.getCategoryId()).thenReturn(123L);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        // Mock product tags with ID 100
        List<DealGroupTagDTO> productTags = new ArrayList<>();
        DealGroupTagDTO productTag = mock(DealGroupTagDTO.class);
        when(productTag.getId()).thenReturn(100L);
        productTags.add(productTag);
        when(dealGroupDTO.getTags()).thenReturn(productTags);
        // Mock shop tags with ID 200
        long shopId = 123L;
        when(ctx.getDpLongShopId()).thenReturn(shopId);
        Map<Long, List<DisplayTagDto>> shopTagsMap = new HashMap<>();
        List<DisplayTagDto> shopTags = new ArrayList<>();
        DisplayTagDto shopTag = mock(DisplayTagDto.class);
        when(shopTag.getTagId()).thenReturn(200L);
        shopTags.add(shopTag);
        shopTagsMap.put(shopId, shopTags);
        when(ctx.getDpShopId2TagsMap()).thenReturn(shopTagsMap);
        // Set productTagConfigMap with matching tag ID 100 for specific key
        Map<String, List<Long>> productTagConfigMap = new HashMap<>();
        // categoryId as string
        productTagConfigMap.put("123", Arrays.asList(100L));
        insuranceConfigDTO.setProductTagConfigMap(productTagConfigMap);
        // Set shopTagConfigMap with matching tag ID 200 for specific key
        Map<String, List<Long>> shopTagConfigMap = new HashMap<>();
        // categoryId as string
        shopTagConfigMap.put("123", Arrays.asList(200L));
        insuranceConfigDTO.setShopTagConfigMap(shopTagConfigMap);
        // act
        boolean result = DealCtxHelper.isAssuredImplant(ctx, insuranceConfigDTO);
        // assert
        assertTrue("Expected isAssuredImplant to return true with specific key configuration", result);
    }
}
