package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.poi.areacommon.AreaCommonService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

public class MapperWrapper_FetchDpCityByMtCityTest {

    @InjectMocks
    private MapperWrapper mapperWrapper;

    @Mock
    private AreaCommonService areaCommonService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试mtCityId小于等于0的情况
     */
    @Test
    public void testFetchDpCityByMtCityLessThanOrEqualToZero() {
        int mtCityId = 0;
        Integer result = mapperWrapper.fetchDpCityByMtCity(mtCityId);
        assertEquals(Integer.valueOf(0), result);
    }

    /**
     * 测试mtCityId大于0且getDpCityByMtCity方法正常返回的情况
     */
    @Test
    public void testFetchDpCityByMtCityGreaterThanZeroAndNormalReturn() throws Exception {
        int mtCityId = 1;
        Integer expectedDpCityId = 2;
        when(areaCommonService.getDpCityByMtCity(mtCityId)).thenReturn(expectedDpCityId);
        Integer result = mapperWrapper.fetchDpCityByMtCity(mtCityId);
        assertEquals(expectedDpCityId, result);
    }
}
