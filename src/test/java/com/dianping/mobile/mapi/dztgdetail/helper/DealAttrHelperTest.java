package com.dianping.mobile.mapi.dztgdetail.helper;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.entity.PrepayCategoryConfig;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.*;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DealAttrHelperTest {

    @Mock
    private DealGroupDTO mockDealGroupDTO;
    @Mock
    private DealGroupServiceProjectDTO mockDealGroupServiceProjectDTO;
    @Mock
    private DealGroupCategoryDTO dealGroupCategoryDTO;
    @Mock
    private MustServiceProjectGroupDTO mockMustServiceProjectGroupDTO;
    @Mock
    private ServiceProjectDTO mockServiceProjectDTO;

    @Mock
    private DealGroupDTO dealGroupDTO;

    @Mock
    private DealGroupUseRuleDTO useRuleDTO;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private PrepayCategoryConfig prepayCategoryConfig;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 onlyVerificationOne 方法，当 attrs 为空的情况
     */
    @Test
    public void testOnlyVerificationOneWhenAttrsIsNull() throws Throwable {
        // arrange
        // act
        boolean result = DealAttrHelper.onlyVerificationOne(null);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 onlyVerificationOne 方法，当 attrs 不为空，但是没有 DealAttrKeys.SINGLE_VERIFICATION_QUANTITY_DESC 对应的属性值的情况
     */
    @Test
    public void testOnlyVerificationOneWhenAttrsIsNotEmptyButNoSingleVerificationQuantityDesc() throws Throwable {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("otherKey");
        attrDTO.setValue(Arrays.asList("otherValue"));
        // act
        boolean result = DealAttrHelper.onlyVerificationOne(Collections.singletonList(attrDTO));
        // assert
        assertFalse(result);
    }

    /**
     * 测试 onlyVerificationOne 方法，当 attrs 不为空，有 DealAttrKeys.SINGLE_VERIFICATION_QUANTITY_DESC 对应的属性值，但是该值不等于 "单次到店仅可核销一次，仅能一人使用" 的情况
     */
    @Test
    public void testOnlyVerificationOneWhenAttrsIsNotEmptyAndHasSingleVerificationQuantityDescButValueIsNotEqual() throws Throwable {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(DealAttrKeys.SINGLE_VERIFICATION_QUANTITY_DESC);
        attrDTO.setValue(Arrays.asList("otherValue"));
        // act
        boolean result = DealAttrHelper.onlyVerificationOne(Collections.singletonList(attrDTO));
        // assert
        assertFalse(result);
    }

    /**
     * 测试 onlyVerificationOne 方法，当 attrs 不为空，有 DealAttrKeys.SINGLE_VERIFICATION_QUANTITY_DESC 对应的属性值，且该值等于 "单次到店仅可核销一次，仅能一人使用" 的情况
     */
    @Test
    public void testOnlyVerificationOneWhenAttrsIsNotEmptyAndHasSingleVerificationQuantityDescAndValueIsEqual() throws Throwable {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(DealAttrKeys.SINGLE_VERIFICATION_QUANTITY_DESC);
        attrDTO.setValue(Arrays.asList("单次到店仅可核销一次，仅能一人使用"));
        // act
        boolean result = DealAttrHelper.onlyVerificationOne(Collections.singletonList(attrDTO));
        // assert
        assertTrue(result);
    }

    /**
     * 测试 getTimes 方法，当 attrs 为空的情况
     */
    @Test
    public void testGetTimesWhenAttrsIsNull() throws Throwable {
        // arrange
        // act
        String result = DealAttrHelper.getTimes(null);
        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试 getTimes 方法，当 attrs 不为空，但不包含 DealAttrKeys.SYS_MULTI_SALE_NUMBER 对应的属性
     */
    @Test
    public void testGetTimesWhenAttrsNotContainKey() throws Throwable {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("otherKey");
        attrDTO.setValue(Arrays.asList("value1", "value2"));
        // act
        String result = DealAttrHelper.getTimes(Collections.singletonList(attrDTO));
        // assert
        Assert.assertNotNull(result);
    }

    /**
     * 测试 getTimes 方法，当 attrs 不为空，包含 DealAttrKeys.SYS_MULTI_SALE_NUMBER 对应的属性，但属性值为空
     */
    @Test
    public void testGetTimesWhenAttrValueIsEmpty() throws Throwable {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(DealAttrKeys.SYS_MULTI_SALE_NUMBER);
        attrDTO.setValue(Collections.emptyList());
        // act
        String result = DealAttrHelper.getTimes(Collections.singletonList(attrDTO));
        // assert
        Assert.assertNotNull(result);
    }

    /**
     * 测试 getTimes 方法，当 attrs 不为空，包含 DealAttrKeys.SYS_MULTI_SALE_NUMBER 对应的属性，且属性值不为空
     */
    @Test
    public void testGetTimesWhenAttrValueIsNotEmpty() throws Throwable {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(DealAttrKeys.SYS_MULTI_SALE_NUMBER);
        attrDTO.setValue(Arrays.asList("value1", "value2"));
        // act
        String result = DealAttrHelper.getTimes(Collections.singletonList(attrDTO));
        // assert
        Assert.assertNotNull(result);
    }

    @Test
    public void testIsToHomeFixDealWithMatchedCondition() {
        Long hasValueServiceTypeId = 306L;
        Long hasNoValueServiceTypeId = 307L;
        when(mockDealGroupDTO.getServiceProject()).thenReturn(mockDealGroupServiceProjectDTO);
        when(mockDealGroupDTO.getCategory()).thenReturn(dealGroupCategoryDTO);

        when(mockDealGroupServiceProjectDTO.getMustGroups()).thenReturn(Arrays.asList(mockMustServiceProjectGroupDTO));
        when(mockMustServiceProjectGroupDTO.getGroups()).thenReturn(Arrays.asList(mockServiceProjectDTO));

        List<String> toHomeFeeAttrs = Arrays.asList("toHomeFeeAttr");
        String notEqualAttrValue = "一口价";
        try (MockedStatic<Lion> mocked = Mockito.mockStatic(Lion.class)) {
            mocked.when(() -> Lion.getList(Environment.getAppName(), "com.sankuai.dzu.tpbase.dztgdetailweb.life.fix.tohome.service.project.key", String.class, new ArrayList<>())).thenReturn(toHomeFeeAttrs);
            mocked.when(() -> Lion.getString(Environment.getAppName(), "com.sankuai.dzu.tpbase.dztgdetailweb.life.fix.tohome.deal.notEqual.attr.value", "一口价")).thenReturn(notEqualAttrValue);
            mocked.when(() -> Lion.getList(Environment.getAppName(), "com.sankuai.dzu.tpbase.dztgdetailweb.life.fix.tohome.category", Long.class, new ArrayList<>())).thenReturn(Arrays.asList(hasValueServiceTypeId));
            mocked.when(() -> Lion.getList(Environment.getAppName(), "com.sankuai.dzu.tpbase.dztgdetailweb.life.tohome.hasnovalue.deal.category", Long.class, new ArrayList<>())).thenReturn(Arrays.asList(hasNoValueServiceTypeId));

            ServiceProjectAttrDTO fixedPriceAttr = new ServiceProjectAttrDTO();
            fixedPriceAttr.setAttrName("toHomeFeeAttr");
            fixedPriceAttr.setAttrValue("一口价");
            when(mockServiceProjectDTO.getAttrs()).thenReturn(Arrays.asList(fixedPriceAttr));
            when(dealGroupCategoryDTO.getServiceTypeId()).thenReturn(hasValueServiceTypeId);
            assertFalse(DealAttrHelper.isToHomeFixDeal(mockDealGroupDTO));


            ServiceProjectAttrDTO notFixedPriceAttr = new ServiceProjectAttrDTO();
            notFixedPriceAttr.setAttrName("toHomeFeeAttr");
            notFixedPriceAttr.setAttrValue("上门费");
            when(mockServiceProjectDTO.getAttrs()).thenReturn(Arrays.asList(notFixedPriceAttr));
            when(dealGroupCategoryDTO.getServiceTypeId()).thenReturn(hasValueServiceTypeId);
            assertTrue(DealAttrHelper.isToHomeFixDeal(mockDealGroupDTO));

            ServiceProjectAttrDTO hasNoValue = new ServiceProjectAttrDTO();
            hasNoValue.setAttrName("toHomeFeeAttr2");
            hasNoValue.setAttrValue("");
            when(mockServiceProjectDTO.getAttrs()).thenReturn(Arrays.asList(hasNoValue));
            when(dealGroupCategoryDTO.getServiceTypeId()).thenReturn(hasNoValueServiceTypeId);
            assertTrue(DealAttrHelper.isToHomeFixDeal(mockDealGroupDTO));
        }
    }

    /**
     * 测试DealGroupDTO为null时
     */
    @Test
    public void testIsRepairPrepayDeal_NullDealGroupDTO() {
        assertFalse(DealAttrHelper.isRepairPrepayDeal(null));
    }

    /**
     * 测试DealGroupDTO的attrs为null时
     */
    @Test
    public void testIsRepairPrepayDeal_NullAttrs() {
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        Mockito.when(dealGroupDTO.getAttrs()).thenReturn(null);
        assertFalse(DealAttrHelper.isRepairPrepayDeal(dealGroupDTO));
    }

    /**
     * 测试DealGroupDTO的attrs为空列表时
     */
    @Test
    public void testIsRepairPrepayDeal_EmptyAttrs() {
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);

        Mockito.when(dealGroupDTO.getAttrs()).thenReturn(Collections.emptyList());
        assertFalse(DealAttrHelper.isRepairPrepayDeal(dealGroupDTO));
    }

    /**
     * 测试DealGroupDTO的attrs不包含维修预付团单属性时
     */
    @Test
    public void testIsRepairPrepayDeal_AttrsNotContainRepairPrepay() {
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);

        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("非维修预付团单属性");
        attrDTO.setValue(Collections.singletonList("值"));
        Mockito.when(dealGroupDTO.getAttrs()).thenReturn(Collections.singletonList(attrDTO));
        assertFalse(DealAttrHelper.isRepairPrepayDeal(dealGroupDTO));
    }

    @Test
    public void testJudgeDisableUsable() {
        // 模拟 judgeUsable 返回 false

        assertFalse(DealAttrHelper.judgeDisableUsable(dealGroupDTO));
    }

    @Test
    public void testJudgeUsable_NoRule() {
        // 模拟没有规则

        assertTrue(DealAttrHelper.judgeUsable(dealGroupDTO));
    }

    @Test
    public void testJudgeUsable_DisableDateRange() {
        // 模拟禁用日期范围


        // 假设当前日期在范围内
        assertTrue(DealAttrHelper.judgeUsable(dealGroupDTO));
    }

    @Test
    @Ignore
    public void testJudgeUsable_DisableDays() {
        // 模拟禁用星期几


        assertTrue(DealAttrHelper.judgeUsable(dealGroupDTO));
    }

    @Test
    public void testJudgeUsable_CycleAvailableDate() {
        // 模拟周期可用日期

        assertTrue(DealAttrHelper.judgeUsable(dealGroupDTO));
    }

    @Test
    public void testJudgeUsable_SpecifiedDurationDate() {
        // 模拟指定日期可用

        assertTrue(DealAttrHelper.judgeUsable(dealGroupDTO));
    }

    @Test
    public void testValidSpecifiedDurationDateList() {
        // 模拟指定日期可用
        DateRangeDTO dateRangeDTO = mock(DateRangeDTO.class);
        when(dateRangeDTO.getFrom()).thenReturn("2023-10-01");
        when(dateRangeDTO.getTo()).thenReturn("2023-10-31");

        AvailableDurationDateDTO availableDurationDateDTO = mock(AvailableDurationDateDTO.class);
        when(availableDurationDateDTO.getAvailableDateRangeDTOS()).thenReturn(Collections.singletonList(dateRangeDTO));

        List<AvailableDurationDateDTO> specifiedDurationDateList = Collections.singletonList(availableDurationDateDTO);

        assertFalse(DealAttrHelper.validSpecifiedDurationDateList(specifiedDurationDateList));
    }

    @Test
    public void testValidAvailableDateRange() {
        // 模拟可用日期范围
        DateRangeDTO dateRangeDTO = mock(DateRangeDTO.class);
        when(dateRangeDTO.getFrom()).thenReturn("2023-10-01");
        when(dateRangeDTO.getTo()).thenReturn("2023-10-31");

        List<DateRangeDTO> availableDateRangeDTOS = Collections.singletonList(dateRangeDTO);

        assertFalse(DealAttrHelper.validAvailableDateRange(availableDateRangeDTOS));
    }

    @Test
    public void testUsable(){
        String json = "{\"dpDealGroupId\":1027836234,\"mtDealGroupId\":1027836234,\"basic\":{\"categoryId\":502,\"title\":\"【回归】餐综搭售联测综团7\",\"brandName\":\"诗泥SPA\",\"titleDesc\":\"仅售177元，价值373元【回归】餐综搭售联测综团7！\",\"beginSaleDate\":\"2024-07-21 17:24:14\",\"endSaleDate\":\"2025-07-23 17:37:50\",\"status\":1,\"saleChannel\":0,\"salePlatform\":3,\"sourceId\":102,\"tradeType\":3,\"platformCategoryId\":80502},\"image\":{\"defaultPicPath\":\"https://p0.meituan.net/dpdeal/789ec325e6ea65abacaffd6b5630ce7899564.jpg\",\"allPicPaths\":\"https://p0.meituan.net/dpdeal/789ec325e6ea65abacaffd6b5630ce7899564.jpg\"},\"category\":{\"categoryId\":502,\"serviceType\":\"美甲\",\"serviceTypeId\":651,\"platformCategoryId\":80502},\"serviceProject\":{\"title\":\"团购详情\",\"salePrice\":\"177.00\",\"marketPrice\":\"373.00\",\"mustGroups\":[{\"groups\":[{\"skuId\":0,\"categoryId\":4040,\"name\":\"\",\"amount\":1,\"marketPrice\":\"200.0\",\"status\":10,\"attrs\":[{\"metaAttrId\":2157,\"attrName\":\"productContent\",\"chnName\":\"服务内容\",\"attrValue\":\"精修死皮、指甲修型、甲面抛光\",\"rawAttrValue\":\"精修死皮、指甲修型、甲面抛光\",\"valueType\":500,\"sequence\":0}]},{\"skuId\":0,\"categoryId\":4041,\"name\":\"\",\"amount\":1,\"marketPrice\":\"106.0\",\"status\":10,\"attrs\":[{\"metaAttrId\":2549,\"attrName\":\"nailPlateType\",\"chnName\":\"甲片类型\",\"attrValue\":\"全贴甲片\",\"rawAttrValue\":\"全贴甲片\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2550,\"attrName\":\"nailPlateCount\",\"chnName\":\"甲片数量\",\"attrValue\":\"10\",\"rawAttrValue\":\"10\",\"valueType\":401,\"sequence\":0}]},{\"skuId\":0,\"categoryId\":4043,\"name\":\"\",\"amount\":1,\"marketPrice\":\"67.0\",\"status\":10,\"attrs\":[{\"metaAttrId\":1038,\"attrName\":\"category2\",\"chnName\":\"二级分类\",\"attrValue\":\"款式美甲\",\"rawAttrValue\":\"款式美甲\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1080,\"attrName\":\"styleType\",\"chnName\":\"款式类型\",\"attrValue\":\"不限款式类型（款式随便做、饰品随便贴）\",\"rawAttrValue\":\"不限款式类型（款式随便做、饰品随便贴）\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2554,\"attrName\":\"givenDecorations\",\"chnName\":\"赠送饰品\",\"attrValue\":\"否\",\"rawAttrValue\":\"否\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2559,\"attrName\":\"sealCoat\",\"chnName\":\"封层\",\"attrValue\":\"亮面\",\"rawAttrValue\":\"亮面\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2157,\"attrName\":\"productContent\",\"chnName\":\"服务内容\",\"attrValue\":\"全手款式任选\",\"rawAttrValue\":\"全手款式任选\",\"valueType\":500,\"sequence\":0}]}]}],\"optionGroups\":[],\"structType\":\"uniform-structure-table\"},\"channel\":{\"channelId\":5,\"channelEn\":\"beauty\",\"channelCn\":\"丽人\",\"channelGroupId\":2,\"channelGroupEn\":\"general\",\"channelGroupCn\":\"到店综合\"},\"attrs\":[{\"name\":\"product_finish_date\",\"value\":[\"1970-01-01 08:00:00\"],\"source\":0},{\"name\":\"calc_holiday_available\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_business_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_channel_id_allowed\",\"value\":[\"0\"],\"source\":0},{\"name\":\"product_can_use_coupon\",\"value\":[\"true\"],\"source\":0},{\"name\":\"preSaleTag\",\"value\":[\"false\"],\"source\":0},{\"name\":\"nail_additional_item\",\"value\":[\"7天内免费修补\"],\"source\":0},{\"name\":\"product_third_party_verify\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_block_stock\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_discount_rule_id\",\"value\":[\"0\"],\"source\":0},{\"name\":\"service_type\",\"value\":[\"美甲\"],\"source\":0},{\"name\":\"reservation_is_needed_or_not\",\"value\":[\"否\"],\"source\":0},{\"name\":\"sys_deal_universal_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"category\",\"value\":[\"50\",\"5002\"],\"source\":0}],\"rule\":{\"buyRule\":{\"maxPerUser\":0,\"minPerUser\":1},\"useRule\":{\"receiptEffectiveDate\":{\"receiptDateType\":1,\"receiptValidDays\":90,\"receiptBeginDate\":\"2025-01-15 10:38:57\",\"receiptEndDate\":\"2025-04-15 23:59:59\",\"showText\":\"购买后90天内有效\"}},\"refundRule\":{\"supportRefundType\":1,\"supportOverdueAutoRefund\":true}},\"displayShopInfo\":{\"dpDisplayShopIds\":[18693527],\"mtDisplayShopIds\":[5068676]},\"tags\":[{\"id\":100003319,\"tagName\":\"美甲\"},{\"id\":100082049,\"tagName\":\"本甲自助美甲待升级\"},{\"id\":100082050,\"tagName\":\"全贴美甲自助美甲待升级\"},{\"id\":100069628,\"tagName\":\"款式任选单\"},{\"id\":100003334,\"tagName\":\"猫眼\"},{\"id\":100003342,\"tagName\":\"法式美甲\"},{\"id\":100075374,\"tagName\":\"7天内免费修补\"},{\"id\":100005325,\"tagName\":\"跳色\"},{\"id\":100076022,\"tagName\":\"自助美甲\"},{\"id\":100003337,\"tagName\":\"晕染\"},{\"id\":100005324,\"tagName\":\"渐变\"},{\"id\":100075024,\"tagName\":\"全量款式美甲\"},{\"id\":100220896,\"tagName\":\"可复购团单\"},{\"id\":100005312,\"tagName\":\"美甲\"},{\"id\":100069574,\"tagName\":\"美甲已升级\"},{\"id\":100003347,\"tagName\":\"晕染美甲\"},{\"id\":100075375,\"tagName\":\"手部\"},{\"id\":100005329,\"tagName\":\"彩绘美甲\"},{\"id\":100076777,\"tagName\":\"含甲片/延长甲\"},{\"id\":100003331,\"tagName\":\"彩绘\"},{\"id\":100003343,\"tagName\":\"渐变美甲\"},{\"id\":100203969,\"tagName\":\"非穿戴甲团单\"},{\"id\":100003346,\"tagName\":\"跳色美甲\"},{\"id\":100003345,\"tagName\":\"猫眼美甲\"},{\"id\":100203952,\"tagName\":\"本甲款式任选轻奢款\"},{\"id\":100214928,\"tagName\":\"本甲款式任选简约款\"},{\"id\":100317817,\"tagName\":\"美甲美睫团购\"}],\"customer\":{\"originCustomerId\":976709,\"platformCustomerId\":1007569148},\"regions\":[{\"dpCityId\":1,\"mtCityId\":10},{\"dpCityId\":10,\"mtCityId\":40}],\"deals\":[{\"dealId\":458421827,\"basic\":{\"title\":\"【回归】餐综搭售联测综团7\",\"originTitle\":\"【回归】餐综搭售联测综团7\",\"status\":1},\"price\":{\"salePrice\":\"177.00\",\"marketPrice\":\"373.00\",\"version\":5162760502},\"stock\":{\"dpSales\":15,\"dpTotal\":50000000,\"dpRemain\":49999985,\"mtSales\":15,\"mtTotal\":50000000,\"mtRemain\":49999985,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1,\"sharedSales\":30,\"sharedTotal\":100000000,\"sharedRemain\":99999970,\"isSharedSoldOut\":false},\"attrs\":[{\"name\":\"sku_receipt_type\",\"value\":[\"1\"],\"source\":0}],\"dealIdInt\":458421827}],\"price\":{\"salePrice\":\"177.00\",\"marketPrice\":\"373.00\",\"version\":5162760502},\"stock\":{\"dpSales\":15,\"dpTotal\":50000000,\"dpRemain\":49999985,\"mtSales\":15,\"mtTotal\":50000000,\"mtRemain\":49999985,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1},\"detail\":{\"dealGroupPics\":\"https://p0.meituan.net/dpdeal/789ec325e6ea65abacaffd6b5630ce7899564.jpg\",\"images\":[\"https://p0.meituan.net/dpdeal/789ec325e6ea65abacaffd6b5630ce7899564.jpg%40450w_280h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\"],\"importantPoint\":\"\",\"templateDetailDTOs\":[{\"title\":\"产品介绍\",\"content\":\"<p class=\\\"listitem\\\"></p>\\n<p class=\\\"explain\\\"></p>\\n<div class=\\\"img\\\"><img src=\\\"https://p0.meituan.net/dpdeal/052fb0abc245b9de1441d43fd9e7fd29291813.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /></div>\\n<p class=\\\"listitem\\\"></p>\\n<p class=\\\"explain\\\"></p>\\n<div class=\\\"img\\\"><img src=\\\"https://p0.meituan.net/dpdeal/83d72a4c88dd6d13a7325b0d115159b8145535.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /></div>\\n<p class=\\\"listitem\\\"></p>\\n<p class=\\\"explain\\\"></p>\\n<div class=\\\"img\\\"><img src=\\\"https://p1.meituan.net/dpdeal/b6731f239685866a580449ddb8dcf571109396.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /></div>\\n\\n\",\"type\":5,\"blockId\":0},{\"title\":\"团购详情\",\"content\":\"        <div class=\\\"detail-tit\\\"></div>\\n        <div>\\n            <table width=\\\"100%\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" class=\\\"detail-table\\\">\\n                <thead>\\n                <tr>\\n                    <th width=\\\"50%\\\">名称</th>\\n                    <th width=\\\"25%\\\">数量</th>\\n                    <th width=\\\"25%\\\">单价</th>\\n                </tr>\\n                </thead>\\n                <tbody>\\n                            <tr>\\n                                <td></td>\\n                                    <td class=\\\"tc\\\">1</td>\\n                                    <td class=\\\"tc\\\">200元</td>\\n                            </tr>\\n                            <tr>\\n                                <td></td>\\n                                    <td class=\\\"tc\\\">1</td>\\n                                    <td class=\\\"tc\\\">106元</td>\\n                            </tr>\\n                            <tr>\\n                                <td></td>\\n                                    <td class=\\\"tc\\\">1</td>\\n                                    <td class=\\\"tc\\\">67元</td>\\n                            </tr>\\n                    <tr class=\\\"total\\\">\\n                        <td></td>\\n                        <td class=\\\"tc\\\">总价<br><strong>团购价</strong></td>\\n                        <td class=\\\"tc\\\">373元<br><strong>177元</strong>\\n                        </td>\\n                    </tr>\\n                </tbody>\\n            </table>\\n        </div>\\n<div><p>温馨提示</p><p>1、团单图片仅供参考 具体效果以实际产品效果为主可自带参考图</p><p>2.不包括复杂手绘 全手彩绘 复杂雕刻 多色晕染 极细钢珠 小众定制款）我们能满足的款式尽最大能力满足</p><p>4、本套餐包7天内免费修补 （除人为损坏和审美改变的非质量问题将不免费售后）</p><p>5、高峰期有可能出现等位情况 来之前可以跟客服打电话确定时间哦 谢谢理解</p></div>\\n\\n\",\"type\":1,\"blockId\":0},{\"title\":\"购买须知\",\"content\":\"<div class=\\\"detail-box\\\">\\n    <div class=\\\"purchase-notes\\\">\\n\\t\\t        <dl>\\n            <dt>有效期</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">\\n    购买后90天内有效\\n        </p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>预约信息</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">无需预约，如遇消费高峰时段您可能需要排队</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>适用人数</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">每张团购券最多1人使用</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>规则提醒</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">可与其他优惠同享\\n</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>温馨提示</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">如需团购券发票，请您在消费时向商户咨询</p>\\n                <p class=\\\"listitem\\\">为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！</p>\\n            </dd>\\n        </dl>\\n    </div>\\n</div>\\n\",\"type\":2,\"blockId\":0}]},\"saleChannelAggregation\":{\"allSupport\":true,\"supportChannels\":[],\"notSupportChannels\":[]},\"purchaseNote\":{\"title\":\"购买须知\",\"modules\":[{\"moduleName\":\"适用时间\",\"icon\":\"https://p0.meituan.net/travelcube/eb358d1a211285207c636aa95594f3751524.png\",\"items\":[{\"itemName\":\"有效时间\",\"itemValues\":[{\"type\":1,\"value\":\"购买后90天内有效\"}]}]},{\"moduleName\":\"预约规则\",\"icon\":\"https://p0.meituan.net/travelcube/17303e587e8242902a1bf6ecd3eab10b1029.png\",\"items\":[{\"itemName\":\"\",\"itemValues\":[{\"type\":1,\"value\":\"无需预约，如遇消费高峰时段您可能需要排队\"}]}]},{\"moduleName\":\"适用人数\",\"icon\":\"https://p0.meituan.net/travelcube/1f18aa2c0100e75060daf348283f3c861351.png\",\"items\":[{\"itemName\":\"\",\"itemValues\":[{\"type\":1,\"value\":\"每张团购券最多1人使用\"}]}]},{\"moduleName\":\"其他规则\",\"icon\":\"https://p1.meituan.net/travelcube/4dac95c5f43a52f49b81ece1918925ea858.png\",\"items\":[{\"itemName\":\"\",\"itemValues\":[{\"type\":1,\"value\":\"可与其他优惠同享\"}]}]},{\"moduleName\":\"温馨提示\",\"icon\":\"https://p1.meituan.net/travelcube/09a2f606b1636f8b135b8582e3c0a53d1494.png\",\"items\":[{\"itemName\":\"\",\"itemValues\":[{\"type\":1,\"value\":\"如需团购券发票，请您在消费时向商户咨询\"},{\"type\":1,\"value\":\"为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！\"}]}]}]},\"dpDealGroupIdInt\":1027836234,\"mtDealGroupIdInt\":1027836234}";
        DealGroupDTO dto = JSON.parseObject(json, DealGroupDTO.class);
        DealGroupRuleDTO rule = new DealGroupRuleDTO();
        DealGroupUseRuleDTO useRule = new DealGroupUseRuleDTO();
        DisableDateDTO disableDate = new DisableDateDTO();
        List<DateRangeDTO> disableDateRangeDTOS = new ArrayList<>();
        DateRangeDTO dateRange = new DateRangeDTO();
        dateRange.setFrom("2024-10-11 18:00:00");
        dateRange.setTo("2024-10-17 18:00:00");
        disableDateRangeDTOS.add(dateRange);
        disableDate.setDisableDateRangeDTOS(disableDateRangeDTOS);
        List<Integer> disableDays = new ArrayList<>();
        disableDays.add(1);
        disableDays.add(2);
        disableDate.setDisableDays(disableDays);
        useRule.setDisableDate(disableDate);
        rule.setUseRule(useRule);
        dto.setRule(rule);

        AvailableDateDTO availableDate = new AvailableDateDTO();
        availableDate.setAvailableType(0);
        List<CycleAvailableDateDTO> cycleAvailableDateList = new ArrayList<>();
        CycleAvailableDateDTO cycleAvailableDateDTO = new CycleAvailableDateDTO();
        List<Integer> availableDays = new ArrayList<>();
        availableDays.add(1);
        availableDays.add(2);
        cycleAvailableDateDTO.setAvailableDays(availableDays);
        cycleAvailableDateList.add(cycleAvailableDateDTO);
        availableDate.setCycleAvailableDateList(cycleAvailableDateList);
        useRule.setAvailableDate(availableDate);
        DealAttrHelper.judgeDisableUsable(dto);


        List<DateRangeDTO> ableDateRangeDTOS = new ArrayList<>();
        dateRange.setFrom("2024-10-11 18:00:00");
        dateRange.setTo("2024-10-17 18:00:00");
        ableDateRangeDTOS.add(dateRange);
        List<AvailableDurationDateDTO> specifiedDurationDateList = new ArrayList<>();
        AvailableDurationDateDTO availableDurationDateDTO = new AvailableDurationDateDTO();
        availableDurationDateDTO.setAvailableDateRangeDTOS(ableDateRangeDTOS);
        specifiedDurationDateList.add(availableDurationDateDTO);
        availableDate.setSpecifiedDurationDateList(specifiedDurationDateList);
        availableDate.setAvailableType(1);
        boolean result = DealAttrHelper.judgeDisableUsable(dto);
        Assert.assertTrue(result);


    }
}
