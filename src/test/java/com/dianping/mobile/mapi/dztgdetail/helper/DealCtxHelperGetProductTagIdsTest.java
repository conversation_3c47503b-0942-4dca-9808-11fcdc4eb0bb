package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import java.util.List;
import java.util.Set;
import org.junit.Test;
import org.mockito.Mockito;

public class DealCtxHelperGetProductTagIdsTest {

    /**
     * Test getProductTagIds with null input list
     * Should return empty HashSet when input is null
     */
    @Test
    public void testGetProductTagIds_NullInput() throws Throwable {
        // arrange
        List<DealGroupTagDTO> tags = null;
        // act
        Set<Long> result = DealCtxHelper.getProductTagIds(tags);
        // assert
        assertNotNull("Result should not be null", result);
        assertTrue("Result should be empty", result.isEmpty());
        assertEquals("Result should be empty set", 0, result.size());
    }

    /**
     * Test getProductTagIds with empty input list
     * Should return empty HashSet when input list is empty
     */
    @Test
    public void testGetProductTagIds_EmptyInput() throws Throwable {
        // arrange
        List<DealGroupTagDTO> tags = Lists.newArrayList();
        // act
        Set<Long> result = DealCtxHelper.getProductTagIds(tags);
        // assert
        assertNotNull("Result should not be null", result);
        assertTrue("Result should be empty", result.isEmpty());
        assertEquals("Result should be empty set", 0, result.size());
    }

    /**
     * Test getProductTagIds with list containing only null elements
     * Should return empty HashSet when all elements are null
     */
    @Test
    public void testGetProductTagIds_AllNullElements() throws Throwable {
        // arrange
        List<DealGroupTagDTO> tags = Lists.newArrayList();
        tags.add(null);
        tags.add(null);
        // act
        Set<Long> result = DealCtxHelper.getProductTagIds(tags);
        // assert
        assertNotNull("Result should not be null", result);
        assertTrue("Result should be empty when all elements are null", result.isEmpty());
        assertEquals("Result should be empty set", 0, result.size());
    }

    /**
     * Test getProductTagIds with list containing valid elements
     * Should return set with all tag IDs when all elements are valid
     */
    @Test
    public void testGetProductTagIds_ValidElements() throws Throwable {
        // arrange
        DealGroupTagDTO tag1 = Mockito.mock(DealGroupTagDTO.class);
        DealGroupTagDTO tag2 = Mockito.mock(DealGroupTagDTO.class);
        DealGroupTagDTO tag3 = Mockito.mock(DealGroupTagDTO.class);
        when(tag1.getId()).thenReturn(100L);
        when(tag2.getId()).thenReturn(200L);
        when(tag3.getId()).thenReturn(300L);
        List<DealGroupTagDTO> tags = Lists.newArrayList(tag1, tag2, tag3);
        // act
        Set<Long> result = DealCtxHelper.getProductTagIds(tags);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Result should contain 3 elements", 3, result.size());
        assertTrue("Result should contain tag ID 100", result.contains(100L));
        assertTrue("Result should contain tag ID 200", result.contains(200L));
        assertTrue("Result should contain tag ID 300", result.contains(300L));
    }

    /**
     * Test getProductTagIds with list containing mixed null and valid elements
     * Should return set with only valid tag IDs, filtering out null elements
     */
    @Test
    public void testGetProductTagIds_MixedNullAndValidElements() throws Throwable {
        // arrange
        DealGroupTagDTO tag1 = Mockito.mock(DealGroupTagDTO.class);
        DealGroupTagDTO tag2 = Mockito.mock(DealGroupTagDTO.class);
        when(tag1.getId()).thenReturn(100L);
        when(tag2.getId()).thenReturn(200L);
        List<DealGroupTagDTO> tags = Lists.newArrayList();
        tags.add(tag1);
        tags.add(null);
        tags.add(tag2);
        tags.add(null);
        // act
        Set<Long> result = DealCtxHelper.getProductTagIds(tags);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Result should contain 2 elements", 2, result.size());
        assertTrue("Result should contain tag ID 100", result.contains(100L));
        assertTrue("Result should contain tag ID 200", result.contains(200L));
        assertFalse("Result should not contain null", result.contains(null));
    }

    /**
     * Test getProductTagIds with single valid element
     * Should return set with single tag ID
     */
    @Test
    public void testGetProductTagIds_SingleValidElement() throws Throwable {
        // arrange
        DealGroupTagDTO tag = Mockito.mock(DealGroupTagDTO.class);
        when(tag.getId()).thenReturn(123L);
        List<DealGroupTagDTO> tags = Lists.newArrayList(tag);
        // act
        Set<Long> result = DealCtxHelper.getProductTagIds(tags);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Result should contain 1 element", 1, result.size());
        assertTrue("Result should contain tag ID 123", result.contains(123L));
    }

    /**
     * Test getProductTagIds with duplicate tag IDs
     * Should return set with unique tag IDs (Set behavior removes duplicates)
     */
    @Test
    public void testGetProductTagIds_DuplicateTagIds() throws Throwable {
        // arrange
        DealGroupTagDTO tag1 = Mockito.mock(DealGroupTagDTO.class);
        DealGroupTagDTO tag2 = Mockito.mock(DealGroupTagDTO.class);
        DealGroupTagDTO tag3 = Mockito.mock(DealGroupTagDTO.class);
        when(tag1.getId()).thenReturn(100L);
        // Same ID as tag1
        when(tag2.getId()).thenReturn(100L);
        when(tag3.getId()).thenReturn(200L);
        List<DealGroupTagDTO> tags = Lists.newArrayList(tag1, tag2, tag3);
        // act
        Set<Long> result = DealCtxHelper.getProductTagIds(tags);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Result should contain 2 unique elements", 2, result.size());
        assertTrue("Result should contain tag ID 100", result.contains(100L));
        assertTrue("Result should contain tag ID 200", result.contains(200L));
    }

    /**
     * Test getProductTagIds with tag having null ID
     * The method doesn't filter out null IDs, only null tag objects
     */
    @Test
    public void testGetProductTagIds_TagWithNullId() throws Throwable {
        // arrange
        DealGroupTagDTO tag1 = Mockito.mock(DealGroupTagDTO.class);
        DealGroupTagDTO tag2 = Mockito.mock(DealGroupTagDTO.class);
        when(tag1.getId()).thenReturn(null);
        when(tag2.getId()).thenReturn(200L);
        List<DealGroupTagDTO> tags = Lists.newArrayList(tag1, tag2);
        // act
        Set<Long> result = DealCtxHelper.getProductTagIds(tags);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Result should contain 2 elements", 2, result.size());
        assertTrue("Result should contain tag ID 200", result.contains(200L));
        assertTrue("Result should contain null ID", result.contains(null));
    }
}
