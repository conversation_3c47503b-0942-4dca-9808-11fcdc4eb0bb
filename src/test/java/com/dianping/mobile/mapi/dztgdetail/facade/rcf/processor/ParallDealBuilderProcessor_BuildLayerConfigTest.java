package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.LayerConfig;
import com.sankuai.general.product.query.center.client.dto.rule.buy.DealGroupBuyRuleDTO;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.Mockito;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ParallDealBuilderProcessor_BuildLayerConfigTest {

    private ParallDealBuilderProcessor parallDealBuilderProcessor;

    @Before
    public void setUp() {
        parallDealBuilderProcessor = new ParallDealBuilderProcessor();
    }

    @Test
    public void testBuildLayerConfigBothNull() throws Throwable {
        LayerConfig result = parallDealBuilderProcessor.buildLayerConfig(null, null);
        assertNull(result);
    }

    @Test
    public void testBuildLayerConfigLayerConfigNull() throws Throwable {
        DealGroupBuyRuleDTO buyRuleDTO = new DealGroupBuyRuleDTO();
        LayerConfig result = parallDealBuilderProcessor.buildLayerConfig(null, buyRuleDTO);
        assertNull(result);
    }

    @Test
    public void testBuildLayerConfigBuyRuleDTONull() throws Throwable {
        LayerConfig layerConfig = new LayerConfig();
        LayerConfig result = parallDealBuilderProcessor.buildLayerConfig(layerConfig, null);
        assertEquals(layerConfig, result);
    }

    @Test
    public void testBuildLayerConfigMaxPerUserNull() throws Throwable {
        LayerConfig layerConfig = new LayerConfig();
        layerConfig.setTitle("title");
        layerConfig.setDesc("desc");
        DealGroupBuyRuleDTO buyRuleDTO = new DealGroupBuyRuleDTO();
        LayerConfig result = parallDealBuilderProcessor.buildLayerConfig(layerConfig, buyRuleDTO);
        // Corrected expectation
        assertEquals("title", result.getTitle());
        assertEquals("desc", result.getDesc());
    }

    @Test
    public void testBuildLayerConfigNormal() throws Throwable {
        LayerConfig layerConfig = new LayerConfig();
        layerConfig.setTitle("title %d");
        layerConfig.setDesc("desc %d");
        DealGroupBuyRuleDTO buyRuleDTO = new DealGroupBuyRuleDTO();
        buyRuleDTO.setMaxPerUser(10);
        LayerConfig result = parallDealBuilderProcessor.buildLayerConfig(layerConfig, buyRuleDTO);
        assertEquals("title 10", result.getTitle());
        assertEquals("desc 10", result.getDesc());
    }
}
