package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.util.TimesDealUtil;
import com.dianping.pigeon.remoting.common.codec.json.JacksonUtils;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Collections;
import java.util.List;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.*;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PromoDetailModule;
import static org.powermock.api.mockito.PowerMockito.when;
import org.mockito.InjectMocks;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/12/31 11:28
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({TimesDealUtil.class, Lion.class})
public class PromoDetailModuleBuilderService_PowerMock_Test {

    @InjectMocks
    private PromoDetailModuleBuilderService promoDetailModuleBuilderService;
    private DealCtx ctx;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试异常场景1：DealCtx 为空
     */
    @Test
    public void testBuildTimes1() {
        PromoDetailModule promoDetailModule = null;
        promoDetailModuleBuilderService.buildTimesDealPromoInfo(ctx, promoDetailModule);
        assert promoDetailModule == null;
    }

    /**
     * 测试异常场景2：PromoDetailModule 为空
     */
    @Test
    public void testBuildTimes2() {
        // arrange
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        // act
        promoDetailModuleBuilderService.buildTimesDealPromoInfo(ctx, promoDetailModule);
        assertNotNull(promoDetailModule);
    }

    /**
     * 测试异常场景3：DealGroupDTO 不是次卡团购
     */
    @Test
    public void testBuildTimes3() {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupBasicDTO basic = new DealGroupBasicDTO();
        basic.setTradeType(1);
        dealGroupDTO.setBasic(basic);
        ctx.setDealGroupDTO(dealGroupDTO);
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        promoDetailModule.setPromoPrice("100");
        // act
        promoDetailModuleBuilderService.buildTimesDealPromoInfo(ctx, promoDetailModule);
        assertNotNull(promoDetailModule);
    }

    /**
     * 测试异常场景4：promoDetailModule 的 promoPrice 为空
     */
    @Test
    public void testBuildTimes4() {
        PowerMockito.mockStatic(TimesDealUtil.class);
        PowerMockito.mockStatic(Lion.class);
        PowerMockito.when(TimesDealUtil.isTimesDeal(any())).thenAnswer((Answer<Boolean>)invocation -> true);
        PowerMockito.when(TimesDealUtil.parseTimes(any())).thenAnswer((Answer<String>)invocation -> "3");
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupBasicDTO basic = new DealGroupBasicDTO();
        basic.setTradeType(19);
        dealGroupDTO.setBasic(basic);
        ctx.setDealGroupDTO(dealGroupDTO);
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        promoDetailModule.setPromoPrice("100");
        DealGroupDTO dealGroup = new DealGroupDTO();
        DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
        dealGroupCategoryDTO.setCategoryId(512L);
        dealGroupCategoryDTO.setServiceTypeId(13730L);
        dealGroup.setCategory(dealGroupCategoryDTO);
        ctx.setDealGroupDTO(dealGroup);

        when(Lion.getList(LionConstants.APP_KEY, QUALITY_EDU_CATION_BY_CATEGORY_IDS, Integer.class,
                Collections.emptyList())).thenAnswer((Answer<?>)invocation -> Lists.newArrayList(512));
        when(Lion.getList(LionConstants.APP_KEY, QUALITY_EDU_CATION_BY_SERVICE_TYPE_IDS, Long.class,
                Collections.emptyList())).thenAnswer((Answer<?>)invocation -> Lists.newArrayList(1373L));

        promoDetailModuleBuilderService.buildTimesDealPromoInfo(ctx, promoDetailModule);
        assertNotNull(promoDetailModule);

    }

    /**
     * 测试正常场景
     */
    @Test
    public void testBuildTimesDealPromoInfoNormal() {
        PowerMockito.mockStatic(Lion.class);
        PowerMockito.mockStatic(TimesDealUtil.class);
        PowerMockito.when(TimesDealUtil.isTimesDeal(any())).thenAnswer((Answer<Boolean>)invocation -> true);
        PowerMockito.when(TimesDealUtil.parseTimes(any())).thenAnswer((Answer<String>)invocation -> "3");

        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupBasicDTO basic = new DealGroupBasicDTO();
        basic.setTradeType(19);
        dealGroupDTO.setBasic(basic);
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(DealAttrKeys.SYS_MULTI_SALE_NUMBER);
        attrDTO.setValue(Lists.newArrayList("3"));
        List<DealGroupDealDTO> deals = Lists.newArrayList();
        DealGroupDealDTO e = new DealGroupDealDTO();
        e.setAttrs(Lists.newArrayList(attrDTO));
        deals.add(e);
        dealGroupDTO.setDeals(deals);
        ctx.setDealGroupDTO(dealGroupDTO);
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        promoDetailModule.setPromoPrice("300");

        List<ModuleAbConfig> moduleAbConfigs = Lists.newArrayList();
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setKey("DpTimesDealExp");

        AbConfig abConfig = new AbConfig();
        abConfig.setExpId("");
        abConfig.setExpResult("b");
        abConfig.setExpBiInfo("");
        moduleAbConfig.setConfigs(Lists.newArrayList(abConfig));
        moduleAbConfigs.add(moduleAbConfig);
        ctx.setModuleAbConfigs(moduleAbConfigs);

        com.sankuai.general.product.query.center.client.dto.DealGroupDTO dealGroup = new com.sankuai.general.product.query.center.client.dto.DealGroupDTO();
        DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
        dealGroupCategoryDTO.setCategoryId(512L);
        dealGroupCategoryDTO.setServiceTypeId(13730L);
        dealGroup.setCategory(dealGroupCategoryDTO);
        ctx.setDealGroupDTO(dealGroup);

        when(Lion.getList(LionConstants.APP_KEY, QUALITY_EDU_CATION_BY_CATEGORY_IDS, Integer.class,
                Collections.emptyList())).thenAnswer((Answer<?>)invocation -> Lists.newArrayList(512));
        when(Lion.getList(LionConstants.APP_KEY, QUALITY_EDU_CATION_BY_SERVICE_TYPE_IDS, Long.class,
                Collections.emptyList())).thenAnswer((Answer<?>)invocation -> Lists.newArrayList(1373L));
        // act
        promoDetailModuleBuilderService.buildTimesDealPromoInfo(ctx, promoDetailModule);
        // assert
        assertEquals(promoDetailModule.getCopies(), "/3次");

        ctx.setMrnVersion("0.5.8");
        // act
        promoDetailModuleBuilderService.buildTimesDealPromoInfo(ctx, promoDetailModule);
        // assert
        assertEquals(promoDetailModule.getPricePrefix(), "单次");
    }

    /**
     * 测试新增的团购次卡主价格描述部分新增的逻辑
     */
    @Test
    public void testBuildTimesDealPromoInfoNormalPro() {
        PowerMockito.mockStatic(Lion.class);

        com.sankuai.general.product.query.center.client.dto.DealGroupDTO dealGroupDTO = JacksonUtils
                .deserialize(dealGroupDTOJson, com.sankuai.general.product.query.center.client.dto.DealGroupDTO.class);
        PromoDetailModule promoDetailModule = JacksonUtils.deserialize(promoDetailModuleJsonForExpress,
                PromoDetailModule.class);
        List<ModuleAbConfig> moduleAbConfigs = JacksonUtils.deserialize(moduleAbConfigsJsonForExpress, List.class);

        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.setModuleAbConfigs(moduleAbConfigs);
        ctx.setMrnVersion("0.5.8");

        when(Lion.getList(LionConstants.APP_KEY, QUALITY_EDU_CATION_BY_CATEGORY_IDS, Integer.class,
                Collections.emptyList()))
                .thenAnswer((Answer<?>)invocation -> Lists.newArrayList(1202, 1203, 1211, 1212, 1213, 1217, 1220));
        when(Lion.getList(LionConstants.APP_KEY, QUALITY_EDU_CATION_BY_SERVICE_TYPE_IDS, Long.class,
                Collections.emptyList()))
                .thenAnswer((Answer<?>)invocation -> Lists.newArrayList(2022, 2023, 2000, 2001, 2031));
        when(Lion.getBoolean(APP_KEY, TIMES_DEAL_OPTIMIZE_SWITCH, false)).thenAnswer((Answer<?>)invocation -> true);

        promoDetailModuleBuilderService.buildTimesDealPromoInfo(ctx, promoDetailModule);

        assert promoDetailModule.isExpressOptimize();
    }

    /**
     * 测试走命中足疗行业特殊实验并且教育行业部分的逻辑
     */
    @Test
    public void testBuildTimesDealPromoInfoNormalProMassage() {
        PowerMockito.mockStatic(Lion.class);

        PromoDetailModule promoDetailModule = JacksonUtils.deserialize(promoDetailModuleJsonForExpress,
                PromoDetailModule.class);
        List<ModuleAbConfig> moduleAbConfigs = JacksonUtils.deserialize(moduleAbConfigsJsonForExpress, List.class);
        com.sankuai.general.product.query.center.client.dto.DealGroupDTO dealGroupDTO = JacksonUtils
                .deserialize(dealGroupDTOJson, com.sankuai.general.product.query.center.client.dto.DealGroupDTO.class);

        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.setModuleAbConfigs(moduleAbConfigs);
        DealGroupChannelDTO dealGroupChannelDTO = new DealGroupChannelDTO();
        dealGroupChannelDTO.setCategoryId(1203);
        ctx.setChannelDTO(dealGroupChannelDTO);

        when(Lion.getList(LionConstants.APP_KEY, QUALITY_EDU_CATION_BY_CATEGORY_IDS, Integer.class,
                Collections.emptyList()))
                .thenAnswer((Answer<?>)invocation -> Lists.newArrayList(1202, 1203, 1211, 1212, 1213, 1217, 1220));
        when(Lion.getList(LionConstants.APP_KEY, QUALITY_EDU_CATION_BY_SERVICE_TYPE_IDS, Long.class,
                Collections.emptyList()))
                .thenAnswer((Answer<?>)invocation -> Lists.newArrayList(2022, 2023, 2000, 2001, 2031));
        when(Lion.getBoolean(APP_KEY, TIMES_DEAL_OPTIMIZE_SWITCH, false)).thenAnswer((Answer<?>)invocation -> true);

        promoDetailModuleBuilderService.buildTimesDealPromoInfo(ctx, promoDetailModule);

        assert "/2节".equals(promoDetailModule.getCopies());
    }

    /**
     * 团购次卡c端表达优化mock数据
     */
    private static final String dealGroupDTOJson = "{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupDTO\",\"dpDealGroupId\":**********,\"mtDealGroupId\":**********,\"basic\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO\",\"categoryId\":303,\"title\":\"【热点】cs-多次卡60分钟｜60分钟足疗\",\"brandName\":\"申亚足疗洗脚馆\",\"titleDesc\":\"仅售198元，价值376元【热点】cs-多次卡60分钟｜60分钟足疗！\",\"beginSaleDate\":\"2024-04-29 14:26:12\",\"endSaleDate\":\"2025-04-29 14:25:48\",\"status\":1,\"saleChannel\":0,\"salePlatform\":3,\"sourceId\":102,\"tradeType\":19,\"platformCategoryId\":80303},\"image\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupImageDTO\",\"defaultPicPath\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/64adc5518c7c0694cebaabe9967f1d6f55489.jpg\",\"allPicPaths\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/64adc5518c7c0694cebaabe9967f1d6f55489.jpg\",\"videoPath\":null,\"videoCoverPath\":null,\"videoSize\":null,\"extendVideos\":null,\"allVideos\":null},\"category\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO\",\"categoryId\":303,\"serviceType\":\"足疗\",\"serviceTypeId\":106010,\"platformCategoryId\":80303},\"bgBu\":null,\"serviceProject\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO\",\"title\":\"团购详情\",\"salePrice\":\"198.00\",\"marketPrice\":\"188.00\",\"mustGroups\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO\",\"groups\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO\",\"skuId\":0,\"categoryId\":2104542,\"name\":\"足疗\",\"amount\":1,\"marketPrice\":\"188.0\",\"status\":10,\"attrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":3022,\"attrName\":\"serviceProcessArrayNew\",\"chnName\":\"服务流程\",\"attrValue\":\"[{\\\"servicemethod\\\":\\\"洗脚\\\",\\\"stepTime\\\":10},{\\\"servicemethod\\\":\\\"足底按摩\\\",\\\"stepTime\\\":50}]\",\"rawAttrValue\":\"[{\\\"servicemethod\\\":\\\"洗脚\\\",\\\"stepTime\\\":10},{\\\"servicemethod\\\":\\\"足底按摩\\\",\\\"stepTime\\\":50}]\",\"unit\":null,\"valueType\":300,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":2441,\"attrName\":\"serviceDurationInt\",\"chnName\":\"服务时长\",\"attrValue\":\"60\",\"rawAttrValue\":\"60\",\"unit\":null,\"valueType\":401,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":3109,\"attrName\":\"serviceBodyRange\",\"chnName\":\"具体服务部位\",\"attrValue\":\"足部\",\"rawAttrValue\":\"足部\",\"unit\":null,\"valueType\":500,\"sequence\":0}]]}]]}]],\"optionGroups\":[\"java.util.ArrayList\",[]],\"structType\":\"uniform-structure-table\"},\"channel\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupChannelDTO\",\"channelId\":3,\"channelEn\":\"joy\",\"channelCn\":\"休闲娱乐\",\"channelGroupId\":2,\"channelGroupEn\":\"general\",\"channelGroupCn\":\"到店综合\"},\"attrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_finish_date\",\"value\":[\"java.util.ArrayList\",[\"1970-01-01 08:00:00\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"calc_holiday_available\",\"value\":[\"java.util.ArrayList\",[\"1\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_business_type\",\"value\":[\"java.util.ArrayList\",[\"1\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_channel_id_allowed\",\"value\":[\"java.util.ArrayList\",[\"0\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_can_use_coupon\",\"value\":[\"java.util.ArrayList\",[\"true\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"preSaleTag\",\"value\":[\"java.util.ArrayList\",[\"false\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_third_party_verify\",\"value\":[\"java.util.ArrayList\",[\"false\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_block_stock\",\"value\":[\"java.util.ArrayList\",[\"false\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_discount_rule_id\",\"value\":[\"java.util.ArrayList\",[\"0\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"service_type\",\"value\":[\"java.util.ArrayList\",[\"足疗\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"single_verification_quantity_desc\",\"value\":[\"java.util.ArrayList\",[\"单次到店可核销多次，可多人使用\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"reservation_is_needed_or_not\",\"value\":[\"java.util.ArrayList\",[\"否\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"sys_deal_universal_type\",\"value\":[\"java.util.ArrayList\",[\"1\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"sys_multi_sale_type\",\"value\":[\"java.util.ArrayList\",[\"2\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"category\",\"value\":[\"java.util.ArrayList\",[\"30\",\"3006\"]],\"source\":0,\"cnName\":null,\"type\":null}]],\"rule\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO\",\"buyRule\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.rule.buy.DealGroupBuyRuleDTO\",\"maxPerUser\":0,\"minPerUser\":1},\"useRule\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO\",\"receiptEffectiveDate\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.rule.use.ReceiptEffectiveDateDTO\",\"receiptDateType\":1,\"receiptValidDays\":90,\"receiptBeginDate\":\"2024-12-31 10:57:14\",\"receiptEndDate\":\"2025-03-31 23:59:59\",\"showText\":\"购买后90天内有效\"},\"availableDate\":null,\"disableDate\":null},\"bookingRule\":null,\"refundRule\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.rule.refund.DealGroupRefundRuleDTO\",\"supportRefundType\":1,\"supportOverdueAutoRefund\":true}},\"displayShopInfo\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupDisplayShopDTO\",\"dpDisplayShopIds\":[\"java.util.ArrayList\",[436822180]],\"mtDisplayShopIds\":[\"java.util.ArrayList\",[436822180]]},\"verifyShopInfo\":null,\"tags\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100056896,\"tagName\":\"30-60分钟\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100069830,\"tagName\":\"足部\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100054254,\"tagName\":\"足疗\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100069834,\"tagName\":\"肩颈\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100007272,\"tagName\":\"足疗按摩虚拟节点\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100007216,\"tagName\":\"足疗\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100001016,\"tagName\":\"足疗\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100002042,\"tagName\":\"足浴\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100036029,\"tagName\":\"部位\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100036041,\"tagName\":\"足底\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100088195,\"tagName\":\"60分钟足疗\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100090587,\"tagName\":\"60分钟足疗爆品待升级\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":10014354,\"tagName\":\"1\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":10014401,\"tagName\":\"测试\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100208256,\"tagName\":\"测试\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100210963,\"tagName\":\"茶饮\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100229628,\"tagName\":\"测试测试\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100277515,\"tagName\":\"按摩/足疗-团单\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":10064338,\"tagName\":\"批量团泛1\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":10058438,\"tagName\":\"批量团泛1\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100313053,\"tagName\":\"推拿按摩\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100311049,\"tagName\":\"肩颈\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100312080,\"tagName\":\"推拿按摩\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100292942,\"tagName\":\"肩颈\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100305297,\"tagName\":\"全身SPA\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100304260,\"tagName\":\"按摩\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100305300,\"tagName\":\"头部按摩\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100298349,\"tagName\":\"肩颈按摩\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":10062440,\"tagName\":\"批量团泛1+2rules\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100299949,\"tagName\":\"按摩足疗多次省团购标签\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100090843,\"tagName\":\"重复2\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100090840,\"tagName\":\"批量新增示例V1\"}]],\"customer\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupCustomerDTO\",\"originCustomerId\":30014800,\"platformCustomerId\":1025027226},\"regions\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupRegionDTO\",\"dpCityId\":1,\"mtCityId\":10},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupRegionDTO\",\"dpCityId\":10,\"mtCityId\":40}]],\"notice\":null,\"notice2b\":null,\"deals\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO\",\"dealId\":456397428,\"basic\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.deal.DealBasicDTO\",\"title\":\"【热点】cs-多次卡60分钟｜60分钟足疗\",\"originTitle\":\"【热点】cs-多次卡60分钟｜60分钟足疗\",\"thirdPartyId\":null,\"status\":1,\"thirdPartyDealId\":null},\"price\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.PriceDTO\",\"salePrice\":\"198.00\",\"marketPrice\":\"376.00\",\"version\":5156857358,\"status\":null,\"prePayPrice\":null,\"finalPayPrice\":null},\"stock\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StockDTO\",\"dpSales\":0,\"dpTotal\":100000000,\"dpRemain\":100000000,\"mtSales\":3,\"mtTotal\":100000000,\"mtRemain\":99999997,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1,\"sharedSales\":0,\"sharedTotal\":0,\"sharedRemain\":0,\"isSharedSoldOut\":false},\"attrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"sys_multi_sale_number\",\"value\":[\"java.util.ArrayList\",[\"2\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"sku_receipt_type\",\"value\":[\"java.util.ArrayList\",[\"1\"]],\"source\":0,\"cnName\":null,\"type\":null}]],\"dealTimeStockDTO\":null,\"dealTimeStockPlanDTO\":null,\"rule\":null,\"image\":null,\"displayShop\":null,\"dealDelivery\":null,\"shopStocks\":null,\"bizDealId\":null,\"weeklyPricePlan\":null,\"dateTimePrice\":null,\"periodPrice\":null,\"bizDealIdInt\":null,\"dealIdInt\":456397428}]],\"price\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.PriceDTO\",\"salePrice\":\"198.00\",\"marketPrice\":\"376.00\",\"version\":5156857358,\"status\":null,\"prePayPrice\":null,\"finalPayPrice\":null},\"stock\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StockDTO\",\"dpSales\":0,\"dpTotal\":100000000,\"dpRemain\":100000000,\"mtSales\":3,\"mtTotal\":100000000,\"mtRemain\":99999997,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1,\"sharedSales\":null,\"sharedTotal\":null,\"sharedRemain\":null,\"isSharedSoldOut\":null},\"detail\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.detail.DealGroupDetailDTO\",\"dealGroupPics\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/64adc5518c7c0694cebaabe9967f1d6f55489.jpg\",\"images\":[\"java.util.ArrayList\",[\"https://qcloud.dpfile.com/pc/hTTA-sPDiyJ2FuynKBOQHGBSgVexF81vMu8bYD6644YXsK2GDcQR4Q4B4l_lBHjlTxq4kKRPxu2Gq1lq24bRpA.jpg\"]],\"info\":null,\"importantPoint\":\"\",\"specialPoint\":null,\"productInfo\":null,\"editorInfo\":null,\"memberInfo\":null,\"shopInfo\":null,\"editorTeam\":null,\"summary\":null,\"templateDetailDTOs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.detail.DealGroupTemplateDetailDTO\",\"title\":\"产品介绍\",\"content\":\"<div><p>阿达阿达是的</p></div>\\n\\n\",\"type\":5,\"blockId\":0,\"mixedContents\":null,\"imageContents\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.detail.DealGroupTemplateDetailDTO\",\"title\":\"团购详情\",\"content\":\"        <div class=\\\"detail-tit\\\"></div>\\n        <div>\\n            <table width=\\\"100%\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" class=\\\"detail-table\\\">\\n                <thead>\\n                <tr>\\n                    <th width=\\\"50%\\\">名称</th>\\n                    <th width=\\\"25%\\\">数量</th>\\n                    <th width=\\\"25%\\\">单价</th>\\n                </tr>\\n                </thead>\\n                <tbody>\\n                            <tr>\\n                                <td>足疗</td>\\n                                    <td class=\\\"tc\\\">1</td>\\n                                    <td class=\\\"tc\\\">188元</td>\\n                            </tr>\\n                    <tr class=\\\"total\\\">\\n                        <td></td>\\n                        <td class=\\\"tc\\\">总价<br><strong>团购价</strong></td>\\n                        <td class=\\\"tc\\\">188元<br><strong>198元</strong>\\n                        </td>\\n                    </tr>\\n                </tbody>\\n            </table>\\n        </div>\\n\\n\",\"type\":1,\"blockId\":0,\"mixedContents\":null,\"imageContents\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.detail.DealGroupTemplateDetailDTO\",\"title\":\"购买须知\",\"content\":\"<div class=\\\"detail-box\\\">\\n    <div class=\\\"purchase-notes\\\">\\n\\t\\t        <dl>\\n            <dt>有效期</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">\\n    购买后90天内有效\\n        </p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>预约信息</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">无需预约，如遇消费高峰时段您可能需要排队</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>适用人数</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">每张团购券最多1人使用</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>规则提醒</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">可与其他优惠同享\\n</p>\\n                <p class=\\\"listitem\\\">单次到店可核销多次，可多人使用</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>温馨提示</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">如需团购券发票，请您在消费时向商户咨询</p>\\n                <p class=\\\"listitem\\\">为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！</p>\\n                <p class=\\\"listitem\\\">购买后若全部未使用，在有效期内可申请全额退款；过期自动将未使用部分退款；若部分已使用，则不可退款。</p>\\n            </dd>\\n        </dl>\\n    </div>\\n</div>\\n\",\"type\":2,\"blockId\":0,\"mixedContents\":null,\"imageContents\":null}]]},\"spu\":null,\"standardServiceProject\":null,\"extendImage\":null,\"combines\":null,\"saleChannelAggregation\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.SaleChannelAggregationDTO\",\"allSupport\":true,\"supportChannels\":[\"java.util.ArrayList\",[]],\"notSupportChannels\":[\"java.util.ArrayList\",[]]},\"purchaseNote\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.PurchaseNoteDTO\",\"title\":\"购买须知\",\"modules\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"适用时间\",\"icon\":\"https://p0.meituan.net/travelcube/eb358d1a211285207c636aa95594f3751524.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"有效时间\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"购买后90天内有效\"}]]}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"预约规则\",\"icon\":\"https://p0.meituan.net/travelcube/17303e587e8242902a1bf6ecd3eab10b1029.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"无需预约，如遇消费高峰时段您可能需要排队\"}]]}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"适用人数\",\"icon\":\"https://p0.meituan.net/travelcube/1f18aa2c0100e75060daf348283f3c861351.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"单次到店可核销多次\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"每张团购券最多1人使用\"}]]}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"其他规则\",\"icon\":\"https://p1.meituan.net/travelcube/4dac95c5f43a52f49b81ece1918925ea858.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"可与其他优惠同享\"}]]}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"温馨提示\",\"icon\":\"https://p1.meituan.net/travelcube/09a2f606b1636f8b135b8582e3c0a53d1494.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"如需团购券发票，请您在消费时向商户咨询\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！\"}]]}]]}]]},\"bizProductId\":null,\"resourceInfos\":null,\"thirdPartyInfo\":null,\"dpDealGroupIdInt\":**********,\"mtDealGroupIdInt\":**********}";
    private static final String promoDetailModuleJsonForExpress = "{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PromoDetailModule\",\"priceDisplayText\":null,\"priceDisplayType\":0,\"descriptionTags\":null,\"dealGroupPrice\":\"198\",\"preSaleDealGroupPrice\":null,\"promoPrice\":\"198\",\"promoPriceDesc\":null,\"pricePostfix\":null,\"discountRate\":null,\"discountRateDescription\":null,\"reductionPromo\":\"0\",\"reductionPromoDetails\":null,\"bestPromoDetails\":null,\"atmosphereBarPromoList\":null,\"generalPromoDetailList\":null,\"couponPromo\":\"178\",\"totalPromo\":null,\"presalePromo\":null,\"marketPricePromo\":\"178\",\"networkLowestPrice\":null,\"marketPrice\":\"376\",\"showMarketPrice\":false,\"showBestPromoDetails\":false,\"finalPrice\":null,\"inflateCounpon\":null,\"perPrice\":null,\"bestPromoDetailsStyleInfo\":null,\"marketPromoDiscount\":null,\"promoDesc\":null,\"promoTags\":null,\"couponList\":[\"java.util.ArrayList\",[]],\"exposureList\":[\"java.util.ArrayList\",[]],\"promoActivityList\":[\"java.util.ArrayList\",[{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.PromoActivityInfoVO\",\"bonusType\":\"省\",\"text\":\"[{\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"strikethrough\\\":false,\\\"text\\\":\\\"金融券test环境使用\\\",\\\"textcolor\\\":\\\"#222222\\\",\\\"textsize\\\":12,\\\"textstyle\\\":\\\"Default\\\",\\\"underline\\\":false}]\",\"style\":0,\"leadUrl\":\"https://stable.pay.test.sankuai.com/portal/bindcard/bindcard.html?merchant_no=1&ext_dim_stat_entry=1&callback_type=close_webview&_mtcq=0&utm_source=pay_app-pay-banner410419_540904&campaignId=1477039\",\"shortText\":\"[{\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"strikethrough\\\":false,\\\"text\\\":\\\"金融券test环境使用\\\",\\\"textcolor\\\":\\\"#222222\\\",\\\"textsize\\\":12,\\\"textstyle\\\":\\\"Default\\\",\\\"underline\\\":false}]\"}]],\"dealGifts\":null,\"promoAbstractList\":[\"java.util.ArrayList\",[\"下单赠无门槛10元券\",\"团购优惠178元\",\"[{\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"strikethrough\\\":false,\\\"text\\\":\\\"金融券test环境使用\\\",\\\"textcolor\\\":\\\"#222222\\\",\\\"textsize\\\":12,\\\"textstyle\\\":\\\"Default\\\",\\\"underline\\\":false}]\"]],\"priceStrengthDesc\":null,\"showPriceCompareEntrance\":false,\"singlePrice\":null,\"copies\":null,\"pricePrefix\":null,\"singleTimePrice\":null,\"multiPrice\":null,\"promoNewStyle\":false,\"maskedPromoPrice\":null,\"promoStock\":null,\"expressOptimize\":false,\"timesUnit\":null}";
    private static final String moduleAbConfigsJsonForExpress = "[\"java.util.ArrayList\",[{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MTSalesGeneralSection\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp001434\",\"expResult\":\"a\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"070dccc8-3920-42db-9e13-66119c6d5863\\\",\\\"ab_id\\\":\\\"exp001434_a\\\"}\",\"useNewStyle\":false}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MTZuLiaoShowMarketPrice\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp001418\",\"expResult\":\"exp001418_b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"2866ba46-6216-411e-9419-504e72c3e946\\\",\\\"ab_id\\\":\\\"exp001418_b\\\"}\",\"useNewStyle\":false}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MtCouponAlleviate2Exp\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"EXP2024082700008\",\"expResult\":\"c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"d5d2782a-3401-4b59-aa54-f91838332601\\\",\\\"ab_id\\\":\\\"EXP2024082700008_c\\\"}\",\"useNewStyle\":false}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MTMassageOverNight\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp001760\",\"expResult\":\"a\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"f94b94ad-7210-4f42-b2cd-78bfb057e934\\\",\\\"ab_id\\\":\\\"exp001760_a\\\"}\",\"useNewStyle\":false}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MTJoyCardPriceExp\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp000594\",\"expResult\":\"exp000594_a\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"6b90ce4e-994a-49fe-87b2-645d0a4adede\\\",\\\"ab_id\\\":\\\"exp000594_a\\\"}\",\"useNewStyle\":false}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MTShoppingCartBuyBar\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp001510\",\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"6f30c3b1-919e-4f65-960e-2a654b0263ed\\\",\\\"ab_id\\\":\\\"exp001510_b\\\"}\",\"useNewStyle\":true}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MTShoppingCartBuyBarNew\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp001692\",\"expResult\":\"a\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"acada8f0-e191-4b49-8607-dd9309889540\\\",\\\"ab_id\\\":\\\"exp001692_a\\\"}\",\"useNewStyle\":false}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MTCouponBar\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp001707\",\"expResult\":\"c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"735d04c4-aef3-4ce2-8198-1ae726e66058\\\",\\\"ab_id\\\":\\\"exp001707_c\\\"}\",\"useNewStyle\":false}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MtPurchaseNoteStructure\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp001872\",\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"c1828da2-c6de-4683-98d7-9e9da55bfcaa\\\",\\\"ab_id\\\":\\\"exp001872_b\\\"}\",\"useNewStyle\":true}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"CardStyleAB_V2_MT_zuliao\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp001797\",\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"38a4331f-fcdf-484f-910a-9d7d9b60424a\\\",\\\"ab_id\\\":\\\"exp001797_b\\\"}\",\"useNewStyle\":true}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MTMassageOverNight\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp001760\",\"expResult\":\"a\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"f94b94ad-7210-4f42-b2cd-78bfb057e934\\\",\\\"ab_id\\\":\\\"exp001760_a\\\"}\",\"useNewStyle\":false}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MtComparePrice\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp001940\",\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"ac0cc2f0-361b-40bf-9dec-8e00b309f8cd\\\",\\\"ab_id\\\":\\\"exp001940_b\\\"}\",\"useNewStyle\":true}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MTMassageNewStyle\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp001893\",\"expResult\":\"a\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"6dc89d91-2053-4020-81e6-4f85f2b943c8\\\",\\\"ab_id\\\":\\\"exp001893_a\\\"}\",\"useNewStyle\":false}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MtTimesDealExp\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp001237\",\"expResult\":\"a\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"5c7f7af0-7de1-4012-9da9-eb56e59aaa5f\\\",\\\"ab_id\\\":\\\"exp001237_a\\\"}\",\"useNewStyle\":false}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MtCouponAlleviate1Exp\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"EXP2024082700008\",\"expResult\":\"c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"0bdc758d-0593-4170-916b-7c624e47203c\\\",\\\"ab_id\\\":\\\"EXP2024082700008_c\\\"}\",\"useNewStyle\":false}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MtExpressOptimizeExp\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"EXP2024122400001\",\"expResult\":\"c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"6bd97b09-7401-4014-8e80-ab002765f651\\\",\\\"ab_id\\\":\\\"EXP2024122400001_c\\\"}\",\"useNewStyle\":false}]]}]]";

}
