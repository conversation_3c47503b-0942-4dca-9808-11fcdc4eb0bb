package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryAbInfo;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryDealModuleInfo;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryParam;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.factory.MassageFactory;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleExtraDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.OptionalServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

/**
 * @author: created by hang.yu on 2024/4/29 17:20
 */
@RunWith(MockitoJUnitRunner.class)
public class MassageCategoryStrategyImplTest {

    @InjectMocks
    private MassageCategoryStrategyImpl massageCategoryStrategy;

    @Mock
    private DouHuBiz douHuBiz;

    private MassageCategoryStrategyImpl massageCategoryStrategyImpl = new MassageCategoryStrategyImpl();

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private ServiceProjectDTO invokePrivateMethod(String methodName, DealGroupServiceProjectDTO dealGroupServiceProject) throws Exception {
        Method method = MassageCategoryStrategyImpl.class.getDeclaredMethod(methodName, DealGroupServiceProjectDTO.class);
        method.setAccessible(true);
        return (ServiceProjectDTO) method.invoke(massageCategoryStrategyImpl, dealGroupServiceProject);
    }

    @Test
    public void getSimilarDealModuleAbConfig() {
        when(douHuBiz.getAbExpResult(any(EnvCtx.class), anyString())).thenReturn(new ModuleAbConfig());
        ModuleAbConfig result = massageCategoryStrategy.getSimilarDealModuleAbConfig(new EnvCtx());
        Assert.assertNotNull(result);
    }

    @Test
    public void testGetCombinationServiceProjectWhenDealGroupServiceProjectIsNull() throws Throwable {
        ServiceProjectDTO result = invokePrivateMethod("getCombinationServiceProject", null);
        assertNull(result);
    }

    @Test
    public void testGetCombinationServiceProjectWhenMustGroupsAndOptionGroupsAreEmpty() throws Throwable {
        DealGroupServiceProjectDTO dealGroupServiceProject = new DealGroupServiceProjectDTO();
        ServiceProjectDTO result = invokePrivateMethod("getCombinationServiceProject", dealGroupServiceProject);
        assertNull(result);
    }

    @Test
    public void testGetCombinationServiceProjectWhenMustGroupsIsNotEmptyAndOptionGroupsIsEmpty() throws Throwable {
        DealGroupServiceProjectDTO dealGroupServiceProject = new DealGroupServiceProjectDTO();
        MustServiceProjectGroupDTO mustServiceProjectGroupDTO = new MustServiceProjectGroupDTO();
        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        mustServiceProjectGroupDTO.setGroups(Arrays.asList(serviceProjectDTO));
        dealGroupServiceProject.setMustGroups(Arrays.asList(mustServiceProjectGroupDTO));
        ServiceProjectDTO result = invokePrivateMethod("getCombinationServiceProject", dealGroupServiceProject);
        assertEquals(serviceProjectDTO, result);
    }

    @Test
    public void testGetCombinationServiceProjectWhenMustGroupsIsEmptyAndOptionGroupsIsNotEmpty() throws Throwable {
        DealGroupServiceProjectDTO dealGroupServiceProject = new DealGroupServiceProjectDTO();
        OptionalServiceProjectGroupDTO optionalServiceProjectGroupDTO = new OptionalServiceProjectGroupDTO();
        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        optionalServiceProjectGroupDTO.setGroups(Arrays.asList(serviceProjectDTO));
        dealGroupServiceProject.setOptionGroups(Arrays.asList(optionalServiceProjectGroupDTO));
        ServiceProjectDTO result = invokePrivateMethod("getCombinationServiceProject", dealGroupServiceProject);
        assertEquals(serviceProjectDTO, result);
    }

    @Test
    public void testGetCombinationServiceProjectWhenMustGroupsAndOptionGroupsAreNotEmpty() throws Throwable {
        DealGroupServiceProjectDTO dealGroupServiceProject = new DealGroupServiceProjectDTO();
        MustServiceProjectGroupDTO mustServiceProjectGroupDTO = new MustServiceProjectGroupDTO();
        ServiceProjectDTO serviceProjectDTO1 = new ServiceProjectDTO();
        mustServiceProjectGroupDTO.setGroups(Arrays.asList(serviceProjectDTO1));
        dealGroupServiceProject.setMustGroups(Arrays.asList(mustServiceProjectGroupDTO));
        OptionalServiceProjectGroupDTO optionalServiceProjectGroupDTO = new OptionalServiceProjectGroupDTO();
        ServiceProjectDTO serviceProjectDTO2 = new ServiceProjectDTO();
        optionalServiceProjectGroupDTO.setGroups(Arrays.asList(serviceProjectDTO2));
        dealGroupServiceProject.setOptionGroups(Arrays.asList(optionalServiceProjectGroupDTO));
        ServiceProjectDTO result = invokePrivateMethod("getCombinationServiceProject", dealGroupServiceProject);
        assertEquals(serviceProjectDTO1, result);
    }

    @Test
    public void testGetCombinationServiceProjectWhenMustGroupsAndOptionGroupsAreNotEmptyButListIsEmpty() throws Throwable {
        DealGroupServiceProjectDTO dealGroupServiceProject = new DealGroupServiceProjectDTO();
        MustServiceProjectGroupDTO mustServiceProjectGroupDTO = new MustServiceProjectGroupDTO();
        mustServiceProjectGroupDTO.setGroups(Collections.emptyList());
        dealGroupServiceProject.setMustGroups(Arrays.asList(mustServiceProjectGroupDTO));
        OptionalServiceProjectGroupDTO optionalServiceProjectGroupDTO = new OptionalServiceProjectGroupDTO();
        optionalServiceProjectGroupDTO.setGroups(Collections.emptyList());
        dealGroupServiceProject.setOptionGroups(Arrays.asList(optionalServiceProjectGroupDTO));
        ServiceProjectDTO result = invokePrivateMethod("getCombinationServiceProject", dealGroupServiceProject);
        assertNull(result);
    }

    /**
     * 测试 buildDealModuleInfo 方法
     */
    @Test
    public void testBuildDealModuleInfo() throws Throwable {
        // arrange
        MassageCategoryStrategyImpl massageCategoryStrategy = new MassageCategoryStrategyImpl();
        // act
        DealCategoryDealModuleInfo result = massageCategoryStrategy.buildDealModuleInfo();
        // assert
        assertEquals("gcdealdetail_newtuandealtab_foot_tuandetail", result.getMtModuleKey());
        assertEquals("tuandeal_newtuandealtab_foot_tuandetail", result.getDpModuleKey());
    }

    /**
     * 测试 buildAbInfo 方法
     */
    @Test
    public void testBuildAbInfo() throws Throwable {
        // arrange
        MassageCategoryStrategyImpl massageCategoryStrategy = new MassageCategoryStrategyImpl();
        // act
        DealCategoryAbInfo result = massageCategoryStrategy.buildAbInfo();
        // assert
        assertNotNull(result);
        assertEquals("MTMassageNewStyle", result.getMtModule());
        assertEquals("DPMassageNewStyle", result.getDpModule());
        assertEquals(new HashSet<>(java.util.Arrays.asList("a", "b", "c", "d")), result.getSkList());
    }

    /**
     * 测试场景：dealCategoryParam.getDealGroupDTO().getServiceProject() 为 null
     */
    @Test
    public void testCustomNewDealStyleServiceProjectNull() throws Throwable {
        // arrange
        DealCategoryParam dealCategoryParam = DealCategoryParam.builder().dealGroupDTO(new DealGroupDTO()).envCtx(new EnvCtx()).moduleExtraDTO(new ModuleExtraDTO()).build();
        // act
        boolean result = massageCategoryStrategy.customNewDealStyle(dealCategoryParam);
        // assert
        assertFalse(result);
    }

    /**
     * 测试场景：单品 ServiceProjectDTO 为 null
     */
    @Test
    public void testCustomNewDealStyleSingleServiceProjectNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupServiceProjectDTO serviceProjectDTO = new DealGroupServiceProjectDTO();
        dealGroupDTO.setServiceProject(serviceProjectDTO);
        DealCategoryParam dealCategoryParam = DealCategoryParam.builder().dealGroupDTO(dealGroupDTO).envCtx(new EnvCtx()).moduleExtraDTO(new ModuleExtraDTO()).build();
        // act
        boolean result = massageCategoryStrategy.customNewDealStyle(dealCategoryParam);
        // assert
        assertFalse(result);
    }

    /**
     * 测试场景：单品 ServiceProjectDTO 的 categoryId 不在 MassageFactory.SINGLE_STANDARD_MASSAGE_CATEGORY_IDS 中
     */
    @Test
    public void testCustomNewDealStyleSingleServiceProjectCategoryIdNotInList() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupServiceProjectDTO serviceProjectDTO = new DealGroupServiceProjectDTO();
        ServiceProjectDTO singleServiceProject = new ServiceProjectDTO();
        singleServiceProject.setCategoryId(999L);
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        mustGroup.setGroups(Collections.singletonList(singleServiceProject));
        serviceProjectDTO.setMustGroups(Collections.singletonList(mustGroup));
        dealGroupDTO.setServiceProject(serviceProjectDTO);
        DealCategoryParam dealCategoryParam = DealCategoryParam.builder().dealGroupDTO(dealGroupDTO).envCtx(new EnvCtx()).moduleExtraDTO(new ModuleExtraDTO()).build();
        // act
        boolean result = massageCategoryStrategy.customNewDealStyle(dealCategoryParam);
        // assert
        assertFalse(result);
    }

    /**
     * 测试场景：单品 ServiceProjectDTO 的 attrs 为空
     */
    @Test
    public void testCustomNewDealStyleSingleServiceProjectAttrsEmpty() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupServiceProjectDTO serviceProjectDTO = new DealGroupServiceProjectDTO();
        ServiceProjectDTO singleServiceProject = new ServiceProjectDTO();
        singleServiceProject.setCategoryId(1L);
        singleServiceProject.setAttrs(Collections.emptyList());
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        mustGroup.setGroups(Collections.singletonList(singleServiceProject));
        serviceProjectDTO.setMustGroups(Collections.singletonList(mustGroup));
        dealGroupDTO.setServiceProject(serviceProjectDTO);
        DealCategoryParam dealCategoryParam = DealCategoryParam.builder().dealGroupDTO(dealGroupDTO).envCtx(new EnvCtx()).moduleExtraDTO(new ModuleExtraDTO()).build();
        // act
        boolean result = massageCategoryStrategy.customNewDealStyle(dealCategoryParam);
        // assert
        assertFalse(result);
    }

    /**
     * 测试场景：组合套餐 ServiceProjectDTO 的 categoryId 不在 MassageFactory.COMBINATION_STANDARD_MASSAGE_CATEGORY_IDS 中
     */
    @Test
    public void testCustomNewDealStyleCombinationServiceProjectCategoryIdNotInList() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupServiceProjectDTO serviceProjectDTO = new DealGroupServiceProjectDTO();
        ServiceProjectDTO combinationServiceProject = new ServiceProjectDTO();
        combinationServiceProject.setCategoryId(999L);
        OptionalServiceProjectGroupDTO optionGroup = new OptionalServiceProjectGroupDTO();
        optionGroup.setGroups(Collections.singletonList(combinationServiceProject));
        serviceProjectDTO.setOptionGroups(Collections.singletonList(optionGroup));
        dealGroupDTO.setServiceProject(serviceProjectDTO);
        DealCategoryParam dealCategoryParam = DealCategoryParam.builder().dealGroupDTO(dealGroupDTO).envCtx(new EnvCtx()).moduleExtraDTO(new ModuleExtraDTO()).build();
        // act
        boolean result = massageCategoryStrategy.customNewDealStyle(dealCategoryParam);
        // assert
        assertFalse(result);
    }
}
