package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.GuaranteeQueryWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.FeaturesLayer;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.Guarantee;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.GuaranteeBuilderService;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.pigeon.remoting.common.codec.json.JacksonUtils;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.GuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.TagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTagNameEnum;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.*;
import java.util.concurrent.Future;

import static com.dianping.deal.common.enums.ClientTypeEnum.dp_tgApp_ios;
import static com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum.MEITUAN_APP;
import static com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP;
import static com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTypeEnum.ANXIN_MEDICAL_GUARANTEE;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
@PrepareForTest(value = {GreyUtils.class, Lion.class})
public class GuaranteeQueryProcessorTest {

    private DealCtx ctx;
    private EnvCtx envCtx;

    @InjectMocks
    private GuaranteeQueryProcessor processor;

    @InjectMocks
    private ParallDealBuilderProcessor parallDealBuilderProcessor;

    @Mock
    private GuaranteeQueryWrapper guaranteeQueryWrapper;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    private MockedStatic<Lion> lionMockedStatic;

    @InjectMocks
    private GuaranteeBuilderService guaranteeBuilderService;

    @Before
    public void setUp() {
        envCtx = new EnvCtx();
        ctx = new DealCtx(envCtx);
        envCtx.setClientType(dp_tgApp_ios.getType());
        lionMockedStatic = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        if (lionMockedStatic != null) {
            lionMockedStatic.close();
        }
    }

    /**
     * 测试prepare方法，当dpLongShopId小于0的情况
     */
    @Test
    public void testPrepare() {
        // arrange
        String json = "[\n" +
                "    {\n" +
                "      \"object\": {\n" +
                "        \"objectId\": \"1033759740\",\n" +
                "        \"objectType\": 1,\n" +
                "        \"ext\": null\n" +
                "      },\n" +
                "      \"priceProtectionTag\": null,\n" +
                "      \"bestPriceGuaranteeTagDTO\": null,\n" +
                "      \"guaranteeTag\": {\n" +
                "        \"guaranteeTagNames\": [\n" +
                "          7\n" +
                "        ]\n" +
                "      },\n" +
                "      \"guaranteeType\": 6,\n" +
                "      \"tags\": [\n" +
                "        {\n" +
                "          \"code\": 7, " +
                "          \"name\": \"安心学\"\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]";
        List<ObjectGuaranteeTagDTO> objectGuaranteeTagDTOS = JSON.parseArray(json, ObjectGuaranteeTagDTO.class);

        // act
        processor.prepare(ctx);
        processor.processAnXinXue(ctx, objectGuaranteeTagDTOS);
        FeaturesLayer featuresLayer = parallDealBuilderProcessor.getFeaturesLayer(ctx,false);
        parallDealBuilderProcessor.buildTimesDealRemind(ctx);

        DealGroupBaseDTO dealGroupBase = new DealGroupBaseDTO();
        dealGroupBase.setAutoRefundSwitch(1);
        ctx.setDealGroupBase(dealGroupBase);

        FutureCtx futureCtx = new FutureCtx();
        Future mockFuture = mock(Future.class);
        futureCtx.setDealGroupThirdPartyFuture(mockFuture);
        ctx.setFutureCtx(futureCtx);

        lionMockedStatic.when(() -> Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb.physical.exam.third.party.shops", String.class, new ArrayList<>()))
                .thenReturn(Collections.singletonList("dp123"));
        lionMockedStatic.when(() -> Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb.physical.exam.reserve.online.shop.config", String.class, Collections.emptyList()))
                .thenReturn(Collections.emptyList());

        guaranteeBuilderService.getGuarantee(ctx, false);

        // assert
        assertTrue(ctx.isAnXinXue());
    }

    @Test
    public void testProcessAnXinExercise() {
        List<ObjectGuaranteeTagDTO> objectGuaranteeTagDTOS = JacksonUtils.deserialize(jsonData, List.class);
        processor.processAnXinExercise(ctx, objectGuaranteeTagDTOS);
        assert ctx.isAnXinExercise();
    }

    @Test
    public void testBuildRunAwayCompensationGuarantees() {
        List<Guarantee> guarantees = Lists.newArrayList();
        String icon = "icon";
        guaranteeBuilderService.buildRunAwayCompensationGuarantees(guarantees, icon);
        assertTrue(guarantees.size() == 2);
    }

    @Test
    public void testBuildRunAwayCompensation() {
        FeaturesLayer featuresLayer = new FeaturesLayer();
        featuresLayer.setLayerConfigs(new ArrayList<>());
        Map<String,String> config = new HashMap<>();
        parallDealBuilderProcessor.buildRunAwayCompensation(featuresLayer, config);
        assertTrue(featuresLayer.getLayerConfigs().size() == 3);
    }

    /**
     * 测试场景：当objectGuaranteeTagDTOS为空时
     */
    @Test
    public void testProcessWhenObjectGuaranteeTagDTOsIsEmpty() {
        DealCtx ctx = mock(DealCtx.class);
        Future mockFuture = mock(Future.class);
        FutureCtx mockFutureCtx = mock(FutureCtx.class);
        when(ctx.getFutureCtx()).thenReturn(mockFutureCtx);
        when(mockFutureCtx.getGuaranteeQueryFuture()).thenReturn(mockFuture);
        when(guaranteeQueryWrapper.getGuaranteeTagDTOs(mockFuture)).thenReturn(Collections.emptyList());

        processor.process(ctx);

        assertNull(ctx.getSafeImplantTag());
    }


    /**
     * 测试当objectGuaranteeTagDTOS为空时的情况
     */
    @Test
    public void testProcessSafeMedicalTagWithEmptyList() {
        DealCtx ctx = new DealCtx(null);
        List<ObjectGuaranteeTagDTO> objectGuaranteeTagDTOS = new ArrayList<>();

        processor.processSafeMedicalTag(ctx, objectGuaranteeTagDTOS);

        assertTrue("安心医疗标签列表应为空", ctx.getSafeMedicalTag().isEmpty());
    }

    /**
     * 测试当objectGuaranteeTagDTOS包含null元素时的情况
     */
    @Test
    public void testProcessSafeMedicalTagWithNullElement() {
        DealCtx ctx = new DealCtx(null);
        List<ObjectGuaranteeTagDTO> objectGuaranteeTagDTOS = new ArrayList<>();
        objectGuaranteeTagDTOS.add(null);

        processor.processSafeMedicalTag(ctx, objectGuaranteeTagDTOS);

        assertTrue("安心医疗标签列表应为空", ctx.getSafeMedicalTag().isEmpty());
    }
    /**
     * 测试当objectGuaranteeTagDTOS包含有效数据时的情况
     */
    @Test
    public void testProcessSafeMedicalTagWithValidData() {
        DealCtx ctx = new DealCtx(null);
        List<ObjectGuaranteeTagDTO> objectGuaranteeTagDTOS = new ArrayList<>();
        ObjectGuaranteeTagDTO tagDTO = new ObjectGuaranteeTagDTO();
        List<TagDTO> tags = new ArrayList<>();
        TagDTO tag = new TagDTO();
        tag.setCode(GuaranteeTagNameEnum.ANXIN_MEDICAL_IMPLANT_GUARANTEE.getCode());
        tags.add(tag);
        tagDTO.setTags(tags);
        tagDTO.setGuaranteeType(ANXIN_MEDICAL_GUARANTEE.getCode());
        GuaranteeTagDTO guaranteeTagDTO = new GuaranteeTagDTO();
        guaranteeTagDTO.setGuaranteeTagNames(Collections.singletonList(1));
        tagDTO.setGuaranteeTag(guaranteeTagDTO);
        objectGuaranteeTagDTOS.add(tagDTO);

        processor.processSafeMedicalTag(ctx, objectGuaranteeTagDTOS);

        assertFalse("安心医疗标签列表不应为空", ctx.getSafeMedicalTag().isEmpty());
        assertEquals("安心医疗标签列表应包含一个元素", 1, ctx.getSafeMedicalTag().size());
        assertTrue("安心医疗标签列表应包含预期的标签代码", ctx.getSafeMedicalTag().contains(tag.getCode()));
    }

    /**
     * 测试场景：guaranteeTagDTOS为空列表
     */
    @Test
    public void testProcessSafeImplantTag_EmptyList() {
        List<ObjectGuaranteeTagDTO> guaranteeTagDTOS = new ArrayList<>();

        processor.processSafeImplantTag(ctx, guaranteeTagDTOS);

        assertNull("安心医疗标签列表应为空", ctx.getSafeImplantTag());
    }

    /**
     * 测试场景：guaranteeTagDTOS包含一个符合条件的TagDTO
     */
    @Test
    public void testProcessSafeImplantTag_ValidTag() {
        List<ObjectGuaranteeTagDTO> guaranteeTagDTOS = new ArrayList<>();
        ObjectGuaranteeTagDTO dto = mock(ObjectGuaranteeTagDTO.class);
        List<TagDTO> tags = new ArrayList<>();
        TagDTO tag = new TagDTO();
        tag.setCode(GuaranteeTagNameEnum.REASSURING_DENTAL_IMPLANT_3_YEARS_GUARANTEE.getCode());
        tags.add(tag);
        when(dto.getTags()).thenReturn(tags);
        guaranteeTagDTOS.add(dto);

        processor.processSafeImplantTag(ctx, guaranteeTagDTOS);

        assertNotNull("放心种植不为空", ctx.getSafeImplantTag());
    }

    private static final String jsonData = "[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO\",\"object\":{\"@class\":\"com.sankuai.nib.price.operation.api.guarantee.common.dto.GuaranteeObjectQueryDTO\",\"objectId\":\"1033759740\",\"objectType\":1,\"ext\":null},\"priceProtectionTag\":null,\"bestPriceGuaranteeTagDTO\":null,\"guaranteeTag\":{\"@class\":\"com.sankuai.nib.price.operation.api.guarantee.common.dto.GuaranteeTagDTO\",\"guaranteeTagNames\":[\"java.util.ArrayList\",[7]]},\"guaranteeType\":8,\"tags\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.nib.price.operation.api.guarantee.common.dto.TagDTO\",\"code\":7,\"name\":\"安心学\"}]]}]]";

    /**
     * 测试getGuarantee方法中的isHit3c分支
     * 当环境为主App、serviceTypeId在3c类目列表中、且有3c标签时应该返回3c认证保障
     */
    @Test
    public void testGetGuarantee_IsHit3c_ShouldReturn3cGuarantee() {
        // arrange
        // 设置主App环境
        envCtx.setDztgClientTypeEnum(MEITUAN_APP);
        ctx = new DealCtx(envCtx);

        // 设置dealGroupDTO with 3c标签和serviceTypeId
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setServiceTypeId(100L); // 设置serviceTypeId
        dealGroupDTO.setCategory(categoryDTO);

        // 添加3c标签
        DealGroupTagDTO tag3c = new DealGroupTagDTO();
        tag3c.setId(100383225L); // 3c标签ID
        List<DealGroupTagDTO> tags = Arrays.asList(tag3c);
        dealGroupDTO.setTags(tags);

        ctx.setDealGroupDTO(dealGroupDTO);

        // 设置必要的依赖
        DealGroupBaseDTO dealGroupBase = new DealGroupBaseDTO();
        dealGroupBase.setAutoRefundSwitch(0);
        ctx.setDealGroupBase(dealGroupBase);

        // mock LionConfigUtils
        lionMockedStatic.when(LionConfigUtils::get3cServiceTypeList)
                .thenReturn(Arrays.asList(100L)); // serviceTypeId在3c列表中
        lionMockedStatic.when(LionConfigUtils::get3cIcon)
                .thenReturn("https://test.icon.com/3c.png");

        // act
        List<Guarantee> guarantees = guaranteeBuilderService.getGuarantee(ctx, false);

        // assert
        assertNotNull("保障列表不应为空", guarantees);
        assertFalse("保障列表不应为空", guarantees.isEmpty());

    }

    /**
     * 测试getGuarantee方法中的isHit3c分支
     * 当不是主App时，不应该返回3c认证保障
     */
    @Test
    public void testGetGuarantee_IsHit3c_NotMainApp_ShouldNotReturn3cGuarantee() {
        // arrange
        // 设置非主App环境
        envCtx.setDztgClientTypeEnum(MEITUAN_WEIXIN_MINIAPP);
        ctx = new DealCtx(envCtx);

        // 设置dealGroupDTO with 3c标签和serviceTypeId
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setServiceTypeId(100L);
        dealGroupDTO.setCategory(categoryDTO);

        // 添加3c标签
        DealGroupTagDTO tag3c = new DealGroupTagDTO();
        tag3c.setId(100383225L);
        List<DealGroupTagDTO> tags = Arrays.asList(tag3c);
        dealGroupDTO.setTags(tags);

        ctx.setDealGroupDTO(dealGroupDTO);

        // 设置必要的依赖
        DealGroupBaseDTO dealGroupBase = new DealGroupBaseDTO();
        dealGroupBase.setAutoRefundSwitch(0);
        ctx.setDealGroupBase(dealGroupBase);

        // mock LionConfigUtils
        lionMockedStatic.when(LionConfigUtils::get3cServiceTypeList)
                .thenReturn(Arrays.asList(100L));


        // act
        List<Guarantee> guarantees = guaranteeBuilderService.getGuarantee(ctx, false);

        // assert
        assertNotNull("保障列表不应为空", guarantees);

        // 验证不包含3c认证保障
        boolean has3cGuarantee = guarantees.stream()
                .anyMatch(g -> g.getIcon() != null && g.getIcon().contains("3c"));
        assertFalse("不应该包含3c认证保障", has3cGuarantee);

    }


}
