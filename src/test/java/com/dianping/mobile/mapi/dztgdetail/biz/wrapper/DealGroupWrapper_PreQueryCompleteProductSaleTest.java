package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.sale.api.dto.QueryProductSaleRequest;
import com.dianping.deal.sale.api.service.ProductSaleQueryService;
import com.dianping.deal.sale.api.enums.SourceEnum;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Arrays;
import java.util.concurrent.Future;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class DealGroupWrapper_PreQueryCompleteProductSaleTest {

    @InjectMocks
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private ProductSaleQueryService productSaleQueryServiceFuture;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 preQueryCompleteProductSale 方法，正常情况
     */
    @Test
    public void testPreQueryCompleteProductSaleNormal() throws Throwable {
        // arrange
        long dpDealGroupId = 1L;
        QueryProductSaleRequest saleRequest = new QueryProductSaleRequest();
        // Corrected to use List<Long>
        saleRequest.setProductIds(Arrays.asList(dpDealGroupId));
        // Corrected to use List<Integer>
        saleRequest.setSources(Arrays.asList(SourceEnum.DpPayOrder.getSource(), SourceEnum.MtPayOrder.getSource()));
        // Mocking the method call to return null as Future is not the focus here
        when(productSaleQueryServiceFuture.multiQueryCompleteProductSale(any(QueryProductSaleRequest.class))).thenReturn(null);
        // act
        Future future = dealGroupWrapper.preQueryCompleteProductSale(dpDealGroupId);
        // assert
        verify(productSaleQueryServiceFuture, times(1)).multiQueryCompleteProductSale(any(QueryProductSaleRequest.class));
    }

    /**
     * 测试 preQueryCompleteProductSale 方法，异常情况
     */
    @Test(expected = Exception.class)
    public void testPreQueryCompleteProductSaleException() throws Throwable {
        // arrange
        long dpDealGroupId = 1L;
        doThrow(new Exception()).when(productSaleQueryServiceFuture).multiQueryCompleteProductSale(any(QueryProductSaleRequest.class));
        // act
        dealGroupWrapper.preQueryCompleteProductSale(dpDealGroupId);
        // assert
        verify(productSaleQueryServiceFuture, times(1)).multiQueryCompleteProductSale(any(QueryProductSaleRequest.class));
    }
}
