package com.dianping.mobile.mapi.dztgdetail.common;

import com.alibaba.fastjson.JSON;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ShopIdUuidDTO;
import com.sankuai.athena.inf.cache2.CacheClient;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.service.DpPoiService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ShopUuidUtilsV2MockTest {

    @InjectMocks
    private ShopUuidUtilsV2 shopUuidUtilsV2;

    @Mock
    private DpPoiService sinaiDpPoiService;

    @Mock
    private static CacheClient cacheClient;

    @Before
    public void init(){
        MockitoAnnotations.initMocks(this);
        this.shopUuidUtilsV2.init();
    }

    @Test
    public void test1() throws Exception {
        List<DpPoiDTO> list = new ArrayList<>();
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setUuid("qwerewqqwq");
        dpPoiDTO.setShopId(1234L);
        list.add(dpPoiDTO);
        when(sinaiDpPoiService.findShopsByShopIds(any())).thenReturn(list);
        ShopIdUuidDTO uuidDtoByIdForLong = shopUuidUtilsV2.getUuidDtoByIdForLong(1234L);
        System.out.println(JSON.toJSONString(uuidDtoByIdForLong));
        Assert.assertNotNull(uuidDtoByIdForLong);
    }

    @Test
    public void test2() throws Exception {
        ShopIdUuidDTO uuidDtoById = shopUuidUtilsV2.getUuidDtoById(0L);
        Assert.assertNull(uuidDtoById);
    }

    @Test
    public void test3() throws Exception {
        ShopIdUuidDTO uuidDtoById = shopUuidUtilsV2.getUuidDtoById(1234L);
        Assert.assertNull(uuidDtoById);
    }

    @Test
    public void test4(){
        Long shopIdByUuid = shopUuidUtilsV2.getShopIdByUuid("");
        assertEquals(0L, shopIdByUuid.longValue());
    }


}
