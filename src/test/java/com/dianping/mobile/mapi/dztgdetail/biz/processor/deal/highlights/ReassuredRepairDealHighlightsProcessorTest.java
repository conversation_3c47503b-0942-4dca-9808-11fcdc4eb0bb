package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;

import static com.dianping.mobile.mapi.dztgdetail.util.ReassuredRepairUtil.LABEL_ID;
import static org.junit.Assert.assertTrue;

/**
 * 测试ReassuredRepairDealHighlightsProcessor的process方法
 */
@RunWith(MockitoJUnitRunner.class)
public class ReassuredRepairDealHighlightsProcessorTest {

    private ReassuredRepairDealHighlightsProcessor processor;

    @Before
    public void setUp() {
        processor = new ReassuredRepairDealHighlightsProcessor();
    }

    /**
     * 测试交易组不存在的情况
     */
    @Test
    public void testProcessDealGroupNotExist() {
        // arrange
        // ctx.getDealGroupDTO()已在setUp中设置为null
        DealCtx ctx = new DealCtx(null);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        ctx.setDealGroupDTO(dealGroupDTO);
        DealGroupTagDTO tag = new DealGroupTagDTO();
        dealGroupDTO.setTags(Arrays.asList(tag));
        boolean flag;
        // act
        try {
            processor.process(ctx);
            flag = true;
        } catch (Exception e) {
            flag = false;
        }
        assertTrue(flag);
    }

    /**
     * 测试交易组存在但标签为空的情况
     */
    @Test
    public void testProcessDealGroupExistTagsEmpty() {
        DealCtx ctx = new DealCtx(null);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        ctx.setDealGroupDTO(dealGroupDTO);
        DealGroupTagDTO tag = new DealGroupTagDTO();
        dealGroupDTO.setTags(Arrays.asList(tag));
        // arrange
        boolean flag;
        // act
        try {
            processor.process(ctx);
            flag = true;
        } catch (Exception e) {
            flag = false;
        }
        assertTrue(flag);
    }

    /**
     * 测试交易组存在，标签不包含LABEL_ID的情况
     */
    @Test
    public void testProcessDealGroupExistTagsNotContainLabelId() {
        // arrange
        DealCtx ctx = new DealCtx(null);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        ctx.setDealGroupDTO(dealGroupDTO);
        DealGroupTagDTO tag = new DealGroupTagDTO();
        dealGroupDTO.setTags(Arrays.asList(tag));
        tag.setId(124L);
        boolean flag;
        // act
        try {
            processor.process(ctx);
            flag = true;
        } catch (Exception e) {
            flag = false;
        }
        assertTrue(flag);
    }

    /**
     * 测试交易组存在，标签包含LABEL_ID，且需要预约的情况
     */
    @Test
    public void testProcessDealGroupExistTagsContainLabelIdNeedReservation() {
        DealCtx ctx = new DealCtx(null);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        ctx.setDealGroupDTO(dealGroupDTO);
        DealGroupTagDTO tag = new DealGroupTagDTO();
        dealGroupDTO.setTags(Arrays.asList(tag));
        tag.setId(LABEL_ID);
        // arrange
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("reservation_is_needed_or_not_2");
        attributeDTO.setValue(Collections.singletonList("是"));
        ctx.setAttrs(Collections.singletonList(attributeDTO));
        boolean flag;
        // act
        try {
            processor.process(ctx);
            flag = true;
        } catch (Exception e) {
            flag = false;
        }
        assertTrue(flag);
    }

    /**
     * 测试交易组存在，标签包含LABEL_ID，且不需要预约的情况
     */
    @Test
    public void testProcessDealGroupExistTagsContainLabelIdNoReservation() {
        // arrange
        DealCtx ctx = new DealCtx(null);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        ctx.setDealGroupDTO(dealGroupDTO);
        DealGroupTagDTO tag = new DealGroupTagDTO();
        dealGroupDTO.setTags(Arrays.asList(tag));
        tag.setId(LABEL_ID);
        boolean flag;
        // act
        try {
            processor.process(ctx);
            flag = true;
        } catch (Exception e) {
            flag = false;
        }
        assertTrue(flag);
    }
}
