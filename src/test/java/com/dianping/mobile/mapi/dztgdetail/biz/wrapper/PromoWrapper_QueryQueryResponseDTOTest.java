package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.spc.common.Response;
import com.dianping.tgc.open.entity.QueryResponseDTO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.concurrent.Future;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class PromoWrapper_QueryQueryResponseDTOTest {

    @Mock
    private Future future;

    @Mock
    private Response<QueryResponseDTO> response;

    private PromoWrapper promoWrapper = new PromoWrapper();

    /**
     * 测试 Future 结果为 null 的情况
     */
    @Test
    public void testQueryQueryResponseDTOFutureResultIsNull() throws Throwable {
        // arrange
        when(future.get()).thenReturn(null);
        // act
        QueryResponseDTO result = promoWrapper.queryQueryResponseDTO(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 Future 结果不为 null，但 Response 的 isSuccess 方法返回 false 的情况
     */
    @Test
    public void testQueryQueryResponseDTOResponseIsNotSuccess() throws Throwable {
        // arrange
        when(future.get()).thenReturn(response);
        when(response.isSuccess()).thenReturn(false);
        // act
        QueryResponseDTO result = promoWrapper.queryQueryResponseDTO(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 Future 结果不为 null，Response 的 isSuccess 方法返回 true 的情况
     */
    @Test
    public void testQueryQueryResponseDTOResponseIsSuccess() throws Throwable {
        // arrange
        QueryResponseDTO queryResponseDTO = new QueryResponseDTO();
        when(future.get()).thenReturn(response);
        when(response.isSuccess()).thenReturn(true);
        when(response.getResult()).thenReturn(queryResponseDTO);
        // act
        QueryResponseDTO result = promoWrapper.queryQueryResponseDTO(future);
        // assert
        assertSame(queryResponseDTO, result);
    }
}
