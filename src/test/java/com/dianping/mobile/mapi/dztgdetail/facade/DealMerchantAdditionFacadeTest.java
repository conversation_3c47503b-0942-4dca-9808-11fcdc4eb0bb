package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DzDealMerchantInfoRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.Response;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealMerchantInfoBO;
import com.sankuai.meituan.charity.merchant.main.sdk.WelfareDocFacade;
import com.sankuai.meituan.charity.merchant.main.sdk.dto.request.DzVpoiReq;
import com.sankuai.meituan.charity.merchant.main.sdk.dto.response.DzProductDocResp;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.thrift.TException;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.concurrent.Future;

import static org.mockito.ArgumentMatchers.any;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

public class DealMerchantAdditionFacadeTest {

    @InjectMocks
    private DealMerchantAdditionFacade dealMerchantAdditionFacade;

    @Mock
    private WelfareDocFacade welfareDocFacade;

    @Mock
    private MapperWrapper mapperWrapper;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Test
    public void testBuildMtMerchantAddition() throws TException {
        MockitoAnnotations.openMocks(this);
        // 初始化请求和环境上下文
        DzDealMerchantInfoRequest request = new DzDealMerchantInfoRequest();
        request.setShopId("123");
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);

        // 准备mock的返回值
        Future future = Mockito.mock(Future.class);
        DzProductDocResp mockResp = new DzProductDocResp();
        mockResp.setSignCharity(true);
        mockResp.setText("这是一个公益商家");
        when(welfareDocFacade.queryDzProductDoc(any(DzVpoiReq.class))).thenReturn(mockResp);
        when(queryCenterWrapper.preDealGroupDTO(any())).thenReturn(future);
        when(queryCenterWrapper.getDealGroupDTO(future)).thenReturn(null);

        // 调用方法
        Response<DealMerchantInfoBO> response = dealMerchantAdditionFacade.buildMerchantAddition(request, envCtx);

        // 验证结果
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getResult().getWelfare());
        assertEquals("这是一个公益商家", response.getResult().getWelfare().getDesc());
        assertEquals("公益商家", response.getResult().getWelfare().getTitle());

        // 验证是否调用了mock的方法
        Mockito.verify(welfareDocFacade).queryDzProductDoc(any(DzVpoiReq.class));
    }

    @Test
    public void testBuildDpMerchantAddition() throws TException {
        MockitoAnnotations.openMocks(this);
        // 初始化请求和环境上下文
        DzDealMerchantInfoRequest request = new DzDealMerchantInfoRequest();
        request.setShopId("123");
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);

        // 准备mock的返回值
        Future future = Mockito.mock(Future.class);
        DzProductDocResp mockResp = new DzProductDocResp();
        mockResp.setSignCharity(true);
        mockResp.setText("这是一个公益商家");
        when(welfareDocFacade.queryDzProductDoc(any(DzVpoiReq.class))).thenReturn(mockResp);
        when(queryCenterWrapper.preDealGroupDTO(any())).thenReturn(future);
        when(queryCenterWrapper.getDealGroupDTO(future)).thenReturn(null);
        when(mapperWrapper.getMtShopIdByDpShopIdLong(anyLong())).thenReturn(6L);

        // 调用方法
        Response<DealMerchantInfoBO> response = dealMerchantAdditionFacade.buildMerchantAddition(request, envCtx);

        // 验证结果
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getResult().getWelfare());
        assertEquals("这是一个公益商家", response.getResult().getWelfare().getDesc());
        assertEquals("公益商家", response.getResult().getWelfare().getTitle());

        // 验证是否调用了mock的方法
        Mockito.verify(welfareDocFacade).queryDzProductDoc(any(DzVpoiReq.class));
    }
}
