package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.gmkt.activity.api.request.QuerySecKillSceneByMaterialRequest;
import com.dianping.gmkt.activity.api.response.QuerySecKillSceneByMaterialResponse;
import com.dianping.gmkt.activity.api.service.SecKillService;
import com.dianping.gmkt.event.datapools.api.model.seckill.SeckillSceneSimpleDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.product.shelf.common.dto.Response;
import com.dianping.product.shelf.common.dto.activity.ActivityDetailDTO;
import com.dianping.product.shelf.common.request.subjectManage.SubjectConfigQueryRequest;
import com.dianping.product.shelf.query.api.ActivityShelfQueryService;
import com.dianping.product.shelf.query.api.SubjectConfigManageService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DealActivityWrapperTest {

    @InjectMocks
    private DealActivityWrapper dealActivityWrapper;

    @Mock
    private Future future;

    @Mock
    private QuerySecKillSceneByMaterialResponse response;

    @Mock
    private SecKillService secKillServiceFuture;

    @Mock
    private ActivityShelfQueryService activityShelfQueryServiceFuture;

    @Mock
    private SubjectConfigManageService subjectConfigServiceFuture;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetSecKillSceneByMaterialFutureIsNull() throws Throwable {
        Map<Integer, List<SeckillSceneSimpleDTO>> result = dealActivityWrapper.getSecKillSceneByMaterial(null);
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testGetSecKillSceneByMaterialFutureResultIsNull() throws Throwable {
        when(dealActivityWrapper.getFutureResult(future)).thenReturn(null);
        Map<Integer, List<SeckillSceneSimpleDTO>> result = dealActivityWrapper.getSecKillSceneByMaterial(future);
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testGetSecKillSceneByMaterialNormal() throws Throwable {
        when(dealActivityWrapper.getFutureResult(future)).thenReturn(response);
        when(response.getMaterialIdSceneDTOMap()).thenReturn(Collections.singletonMap(1, Collections.singletonList(new SeckillSceneSimpleDTO())));
        Map<Integer, List<SeckillSceneSimpleDTO>> result = dealActivityWrapper.getSecKillSceneByMaterial(future);
        assertEquals(1, result.size());
    }

    /**
     * 测试preSecKillSceneByMaterial方法，当secKillSceneByMaterialRequest为null时
     */
    @Test
    public void testPreSecKillSceneByMaterialRequestIsNull() throws Throwable {
        // arrange
        QuerySecKillSceneByMaterialRequest secKillSceneByMaterialRequest = null;
        // act
        Future result = dealActivityWrapper.preSecKillSceneByMaterial(secKillSceneByMaterialRequest);
        // assert
        assertNull(result);
    }

    /**
     * 测试preSecKillSceneByMaterial方法，当secKillSceneByMaterialRequest不为null，且方法调用成功时
     */
    @Test
    public void testPreSecKillSceneByMaterialSuccess() throws Throwable {
        // arrange
        QuerySecKillSceneByMaterialRequest secKillSceneByMaterialRequest = new QuerySecKillSceneByMaterialRequest();
        // act
        Future result = dealActivityWrapper.preSecKillSceneByMaterial(secKillSceneByMaterialRequest);
        // assert
        verify(secKillServiceFuture, times(1)).querySecKillSceneByMaterial(secKillSceneByMaterialRequest);
    }

    /**
     * 测试preSecKillSceneByMaterial方法，当secKillSceneByMaterialRequest不为null，但方法调用时抛出异常时
     */
    @Test
    public void testPreSecKillSceneByMaterialException() throws Throwable {
        // arrange
        QuerySecKillSceneByMaterialRequest secKillSceneByMaterialRequest = new QuerySecKillSceneByMaterialRequest();
        doThrow(new RuntimeException()).when(secKillServiceFuture).querySecKillSceneByMaterial(secKillSceneByMaterialRequest);
        // act
        Future result = dealActivityWrapper.preSecKillSceneByMaterial(secKillSceneByMaterialRequest);
        // assert
        assertNull(result);
    }

    /**
     * 测试preQuerySpecialValueDeal方法，正常执行
     */
    @Test
    public void testPreQuerySpecialValueDealNormal() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        when(activityShelfQueryServiceFuture.queryOnlineActivity(any())).thenReturn(null);
        // act
        Future result = dealActivityWrapper.preQuerySpecialValueDeal(ctx);
        // assert
        verify(activityShelfQueryServiceFuture, times(1)).queryOnlineActivity(any());
    }

    @Test
    public void testHasSpecialValueDealNotSpecial() throws Throwable {
        // arrange
        Response<ActivityDetailDTO> resp = new Response<>();
        resp.setSuccess(false);
        CompletableFuture<Response<ActivityDetailDTO>> future = CompletableFuture.completedFuture(resp);
        // act
        boolean result = dealActivityWrapper.hasSpecialValueDeal(future);
        // assert
        assertEquals(false, result);
    }

    @Test
    public void testHasSpecialValueDealSpecial() throws Throwable {
        ActivityDetailDTO activityDetailDTO = new ActivityDetailDTO();
        activityDetailDTO.setActivityId(1);
        Response<ActivityDetailDTO> resp = Response.createSuccessResponse(activityDetailDTO);
        CompletableFuture<Response<ActivityDetailDTO>> future = CompletableFuture.completedFuture(resp);
        // act
        boolean result = dealActivityWrapper.hasSpecialValueDeal(future);
        // assert
        assertEquals(true, result);
    }

    /**
     * 测试getActivityDealDetail方法，当Future结果的Response不成功时
     */
    @Test
    public void testGetActivityDealDetailResponseNotSuccess() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        Response<ActivityDetailDTO> resp = Response.createFailedResponse("error", null);

        when(activityShelfQueryServiceFuture.queryOnlineActivity(any())).thenReturn(resp);
        when(dealActivityWrapper.getFutureResult(future)).thenReturn(null);
        // act
        ActivityDetailDTO result = dealActivityWrapper.getActivityDealDetail(ctx);
        // assert
        assertNull(result);
    }

    /**
     * 测试preQueryDealActivityConfig方法，当request为null时
     */
    @Test
    public void testPreQueryDealActivityConfigRequestIsNull() throws Throwable {
        // arrange
        SubjectConfigQueryRequest request = null;
        // act
        Future result = dealActivityWrapper.preQueryDealActivityConfig(request);
        // assert
        assertNull(result);
    }

    /**
     * 测试preQueryDealActivityConfig方法，正常执行
     */
    @Test
    public void testPreQueryDealActivityConfigNormal() throws Throwable {
        // arrange
        SubjectConfigQueryRequest request = new SubjectConfigQueryRequest();
        when(subjectConfigServiceFuture.subjectConfigQuery(any())).thenReturn(null);
        // act
        Future result = dealActivityWrapper.preQueryDealActivityConfig(request);
        // assert
        verify(subjectConfigServiceFuture, times(1)).subjectConfigQuery(request);
    }


}

