package com.dianping.mobile.mapi.dztgdetail.button;

import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.PriceContext;
import com.sankuai.dealuser.price.display.api.model.PrePayPriceDetailDTO;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PrePayDealButtonBuilderTest {
    @Mock
    private ButtonBuilderChain chain;
    @Mock
    private PriceDisplayDTO priceDisplayDTO;
    @Mock
    private PrePayPriceDetailDTO prePayPriceDetail;
    @Mock
    private PriceContext priceContext;

    @InjectMocks
    private PrePayDealButtonBuilder builder;


    /**
     * 测试正常情况下按钮构建
     */
    @Test
    public void testDoBuildNormalCase() throws Throwable {

        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx dealCtx = new DealCtx(envCtx);
        DealGroupBaseDTO dealGroupBaseDTO = new DealGroupBaseDTO();
        dealGroupBaseDTO.setDealGroupPrice(new BigDecimal("200.0"));
        dealCtx.setDealGroupBase(dealGroupBaseDTO);
        dealCtx.setMarketPriceHided(true);
        dealCtx.setEnableCardStyleV2(true);
        dealCtx.setPriceContext(priceContext);

        // arrange
        when(priceContext.getDealPromoPrice()).thenReturn(priceDisplayDTO);
        when(priceDisplayDTO.getPrePayPriceDetail()).thenReturn(prePayPriceDetail);
        when(prePayPriceDetail.getPrePayActualPrice()).thenReturn(new BigDecimal("100.0"));

        // act
        builder.doBuild(dealCtx, chain);

        // assert
        assertNotNull(dealCtx.getBuyBar());
        assertEquals("立即支付", dealCtx.getBuyBar().getBuyBtns().get(0).getBtnTitle());
        assertEquals("预付款¥100元", dealCtx.getBuyBar().getBuyBtns().get(0).getBtnSubTitle());
    }

    /**
     * 测试价格信息为空的情况
     */
    @Test
    public void testDoBuildPriceInfoNull() throws Throwable {

        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx dealCtx = new DealCtx(envCtx);
        DealGroupBaseDTO dealGroupBaseDTO = new DealGroupBaseDTO();
        dealGroupBaseDTO.setDealGroupPrice(new BigDecimal("200.0"));
        dealCtx.setDealGroupBase(dealGroupBaseDTO);
        dealCtx.setEnableCardStyleV2(true);
        dealCtx.setPriceContext(null);

        // act
        builder.doBuild(dealCtx, chain);

        // assert
        assertTrue(dealCtx.getBuyBar().getBuyBtns().isEmpty());
    }

    /**
     * 测试点评小程序且配置关闭的情况
     */
    @Test
    public void testDoBuildDpMiniAppConfigClosed() throws Throwable {
        try (MockedStatic<Lion> mockedLion = Mockito.mockStatic(Lion.class)) {

            EnvCtx envCtx = new EnvCtx();
            envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP);
            DealCtx dealCtx = new DealCtx(envCtx);
            DealGroupBaseDTO dealGroupBaseDTO = new DealGroupBaseDTO();
            dealGroupBaseDTO.setDealGroupPrice(new BigDecimal("200.0"));
            dealCtx.setDealGroupBase(dealGroupBaseDTO);
            dealCtx.setMarketPriceHided(true);
            dealCtx.setEnableCardStyleV2(false);
            dealCtx.setPriceContext(priceContext);

            // arrange
            when(priceContext.getNormalPrice()).thenReturn(priceDisplayDTO);
            when(priceDisplayDTO.getPrePayPriceDetail()).thenReturn(prePayPriceDetail);
            when(prePayPriceDetail.getPrePayActualPrice()).thenReturn(new BigDecimal("100.0"));

            mockedLion.when(() -> Lion.getBoolean(Mockito.anyString(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(false);
            // act
            builder.doBuild(dealCtx, chain);

            // assert
            assertEquals("为保证您的用户体验，请至“美团/点评”APP购买", dealCtx.getBuyBar().getBuyBtns().get(0).getBlockMsg());
        }
    }
}
