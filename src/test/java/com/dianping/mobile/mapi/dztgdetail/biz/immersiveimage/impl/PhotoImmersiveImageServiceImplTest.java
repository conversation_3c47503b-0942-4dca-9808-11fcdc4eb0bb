package com.dianping.mobile.mapi.dztgdetail.biz.immersiveimage.impl;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ImmersiveImageWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageFilterRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageFilterVO;
import com.dianping.mobile.mapi.dztgdetail.entity.QueryExhibitImageFilterParam;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.when;
import static org.mockito.ArgumentMatchers.any;

public class PhotoImmersiveImageServiceImplTest {

    @InjectMocks
    private PhotoImmersiveImageServiceImpl photoImmersiveImageService;

    @Mock
    private ImmersiveImageWrapper immersiveImageWrapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 getImmersiveImageFilter 方法，当 categoryId 为 null
     */
    @Test
    public void testGetImmersiveImageFilterWhenCategoryIdIsNull() {
        GetImmersiveImageFilterRequest request = new GetImmersiveImageFilterRequest();
        ImmersiveImageFilterVO result = photoImmersiveImageService.getImmersiveImageFilter(request);
        assertNull(result);
    }

    /**
     * 测试 getImmersiveImageFilter 方法，当 categoryId 不为 null，且 immersiveImageWrapper.getImmersiveImageFilter 返回非 null 值
     */
    @Test
    public void testGetImmersiveImageFilterWhenCategoryIdIsNotNullAndWrapperReturnsNonNull() {
        GetImmersiveImageFilterRequest request = new GetImmersiveImageFilterRequest();
        request.setCategoryId(504);
        when(immersiveImageWrapper.getImmersiveImageFilter(any(QueryExhibitImageFilterParam.class))).thenReturn(new ImmersiveImageFilterVO());
        ImmersiveImageFilterVO result = photoImmersiveImageService.getImmersiveImageFilter(request);
        assertNotNull(result);
    }

    /**
     * 测试 getImmersiveImageFilter 方法，当 categoryId 不为 null，且 immersiveImageWrapper.getImmersiveImageFilter 返回 null 值
     */
    @Test
    public void testGetImmersiveImageFilterWhenCategoryIdIsNotNullAndWrapperReturnsNull() {
        GetImmersiveImageFilterRequest request = new GetImmersiveImageFilterRequest();
        request.setCategoryId(504);
        when(immersiveImageWrapper.getImmersiveImageFilter(any(QueryExhibitImageFilterParam.class))).thenReturn(null);
        ImmersiveImageFilterVO result = photoImmersiveImageService.getImmersiveImageFilter(request);
        assertNull(result);
    }
}
