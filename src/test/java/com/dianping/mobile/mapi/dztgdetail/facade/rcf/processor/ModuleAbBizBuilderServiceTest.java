package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.DealCategoryFactory;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.DealRepurchaseConfig;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.ModuleAbBuilderService;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.PromoInfoHelper;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import deps.redis.clients.util.CollectionUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.APP_KEY;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.COMPARE_PRICE_CATEGORY_ALLOW;
import static com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum.MEITUAN_APP;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * @Author: <EMAIL>
 * @Date: 2024/6/9
 */
@RunWith(MockitoJUnitRunner.class)
public class ModuleAbBizBuilderServiceTest {
    @Mock
    private DouHuBiz douHuBiz;
    @InjectMocks
    private ModuleAbBuilderService moduleAbBuilderService;
    @Mock
    private DealCategoryFactory dealCategoryFactory;
    @Mock
    private DouHuService douHuService;

    private MockedStatic<Lion> mockedStatic;

    private MockedStatic<LionConfigUtils> lionConfigUtilsMocked;

    private MockedStatic<PromoInfoHelper> promoInfoHelperMockedStatic;

    @Before
    public void setUp() {
        mockedStatic = mockStatic(Lion.class);
        lionConfigUtilsMocked = mockStatic(LionConfigUtils.class);
        promoInfoHelperMockedStatic = mockStatic(PromoInfoHelper.class);
    }

    @After
    public void teardown() {
        mockedStatic.close();
        lionConfigUtilsMocked.close();
        promoInfoHelperMockedStatic.close();
    }

    @Test
    public void testGetModuleAbConfigs() {
        List<ModuleAbConfig> moduleAbConfigs = Lists.newArrayList();
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setKey("1");
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("b");
        abConfig.setExpBiInfo("b");
        abConfig.setExpId("111");
        moduleAbConfig.setConfigs(Lists.newArrayList(abConfig));
        moduleAbConfigs.add(moduleAbConfig);

        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setDpLongShopId(1L);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(303);
        ctx.setChannelDTO(channelDTO);

        mockedStatic.when(() -> Lion.getList(APP_KEY, COMPARE_PRICE_CATEGORY_ALLOW, Integer.class, Lists.newArrayList())).thenReturn(Lists.newArrayList(303));
        mockedStatic.when(() -> Lion.getBoolean(LionConstants.APP_KEY, LionConstants.COMPARE_PRICE_AB_TEST_SWITCH, false)).thenReturn(true);

        Map<String, List> hotMap = new HashMap<>();
        hotMap.put("dp111", Lists.newArrayList(1L));
        mockedStatic.when(() -> Lion.getMap(LionConstants.APP_KEY, LionConstants.HOT_NAIL_MODULE_BLACK_LIST_SHOP, List.class, Collections.emptyMap())).thenReturn(hotMap);

        Mockito.when(douHuBiz.getAbByUnionId(ctx.getEnvCtx().getUnionId(), "DPZuLiaoHuoJiaDirectPurchase", ctx.isMt())).thenReturn(new ModuleAbConfig());
        Mockito.when(douHuBiz.getAbByUnionId(ctx.getEnvCtx().getUnionId(), "MTShoppingCartBuyBar", ctx.isMt())).thenReturn(new ModuleAbConfig());
        Mockito.when(douHuBiz.getAbByUnionId(ctx.getEnvCtx().getUnionId(), "MTShoppingCartBuyBarNew", ctx.isMt())).thenReturn(new ModuleAbConfig());
        Mockito.when(douHuBiz.getAbcByUnionId(ctx.getEnvCtx().getUnionId(), "DPCouponBar", ctx.isMt())).thenReturn(new ModuleAbConfig());
        Mockito.when(douHuBiz.getAbExpResult(ctx, ctx.isMt() ? "MtCouponAlleviate1Exp" : "DpCouponAlleviate1Exp")).thenReturn(new ModuleAbConfig());
//        Mockito.when(douHuBiz.getAbByUnionId(ctx.getEnvCtx().getUnionId(), "DpComparePrice", ctx.isMt())).thenReturn(new ModuleAbConfig());

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
        dealGroupCategoryDTO.setCategoryId(303L);
        dealGroupCategoryDTO.setServiceType("serviceType");
        dealGroupDTO.setCategory(dealGroupCategoryDTO);
        ctx.setDealGroupDTO(dealGroupDTO);

        Map<String, List> nailMap = new HashMap<>();
        nailMap.put("303", Lists.newArrayList("serviceType"));
        mockedStatic.when(() -> Lion.getMap(LionConstants.APP_KEY,
                LionConstants.ALLOW_HOT_NAIL_STYLE_CATEGORY_IDS, List.class, Collections.emptyMap())).thenReturn(nailMap);
//        Mockito.when(douHuBiz.getAbByUnionId(ctx.getEnvCtx().getUnionId(), "DpHotNailModule", ctx.isMt())).thenReturn(new ModuleAbConfig());

        Mockito.when(dealCategoryFactory.getModuleAbConfig(Mockito.any(), Mockito.anyLong())).thenReturn(new ModuleAbConfig());

        ctx.setShowReservationAbConfig(new ModuleAbConfig());

        DealRepurchaseConfig dealRepurchaseConfig = new DealRepurchaseConfig();
        dealRepurchaseConfig.setEnableSwitch(true);
        dealRepurchaseConfig.setCategoryIds(Lists.newArrayList(303));
        mockedStatic.when(() -> Lion.getBean(APP_KEY,
                LionConstants.REPURCHASE_CONFIG, DealRepurchaseConfig.class)).thenReturn(dealRepurchaseConfig);
//        Mockito.when(douHuBiz.getAbExpResultByUuidAndDpid(ctx, "DpRepurchaseShelfExp")).thenReturn(new ModuleAbConfig());

        Mockito.when(dealCategoryFactory.getSimilarDealModuleAbConfig(Mockito.any(), Mockito.anyLong())).thenReturn(new ModuleAbConfig());
        Mockito.when(douHuService.getCompareSameShopPriceStyleAbConfigByDealCtx(ctx)).thenReturn(new ModuleAbConfig());

        List<ModuleAbConfig> result = moduleAbBuilderService.getModuleAbConfigs(ctx);
        Assert.assertTrue(CollectionUtils.isNotEmpty(result));
    }

    /**
     * 商户ID在热门款式模块的黑名单中
     */
    @Test
    public void testGetHotNailModuleAbConfig_ShopInBlackList() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(MEITUAN_APP);
        DealCtx dealCtx = new DealCtx(envCtx);
        long shopIdL = 1L;
        dealCtx.setMtLongShopId(shopIdL);
        lionConfigUtilsMocked.when(() -> LionConfigUtils.isHotNailModuleBlackShop(anyLong(), anyBoolean())).thenReturn(true);
        ModuleAbConfig result = moduleAbBuilderService.getHotNailModuleAbConfig(dealCtx);
        assertNull(result);
    }

    /**
     * 商户ID不在热门款式模块的黑名单中，但 DealGroupDTO 对象为空
     */
    @Test
    public void testGetHotNailModuleAbConfig_DealGroupDTOIsNull() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(MEITUAN_APP);
        DealCtx dealCtx = new DealCtx(envCtx);
        long shopIdL = 1L;
        dealCtx.setMtLongShopId(shopIdL);
        lionConfigUtilsMocked.when(() -> LionConfigUtils.isHotNailModuleBlackShop(anyLong(), anyBoolean())).thenReturn(false);
        ModuleAbConfig result = moduleAbBuilderService.getHotNailModuleAbConfig(dealCtx);
        assertNull(result);
    }

    /**
     * 商户ID不在热门款式模块的黑名单中，DealGroupDTO 对象不为空，但 DealGroupCategoryDTO 对象为空
     */
    @Test
    public void testGetHotNailModuleAbConfig_DealGroupCategoryDTOIsNull() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(MEITUAN_APP);
        DealCtx dealCtx = new DealCtx(envCtx);
        long shopIdL = 1L;
        dealCtx.setMtLongShopId(shopIdL);
        dealCtx.setDealGroupDTO(new DealGroupDTO());
        lionConfigUtilsMocked.when(() -> LionConfigUtils.isHotNailModuleBlackShop(anyLong(), anyBoolean())).thenReturn(false);
        ModuleAbConfig result = moduleAbBuilderService.getHotNailModuleAbConfig(dealCtx);
        assertNull(result);
    }

    /**
     * 商户ID不在热门款式模块的黑名单中，DealGroupDTO 和 DealGroupCategoryDTO 对象都不为空，但团单 categoryId 不在展示类目中
     */
    @Test
    public void testGetHotNailModuleAbConfig_CategoryIdNotInDisplayCategories() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(MEITUAN_APP);
        DealCtx dealCtx = new DealCtx(envCtx);
        long shopIdL = 1L;
        dealCtx.setMtLongShopId(shopIdL);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(502L);
        categoryDTO.setServiceType("美睫");
        dealGroupDTO.setCategory(categoryDTO);
        dealCtx.setDealGroupDTO(dealGroupDTO);
        lionConfigUtilsMocked.when(() -> LionConfigUtils.isHotNailModuleBlackShop(anyLong(), anyBoolean())).thenReturn(false);
        lionConfigUtilsMocked.when(() -> LionConfigUtils.allowDisplayHotNailModule(anyInt(), anyString())).thenReturn(false);
        ModuleAbConfig result = moduleAbBuilderService.getHotNailModuleAbConfig(dealCtx);
        assertNull(result);
    }


    @Test
    public void testGetSimilarDealModuleAbConfig(){
        EnvCtx envCtx = new EnvCtx();
        ModuleAbConfig value = new ModuleAbConfig();
        when(dealCategoryFactory.getSimilarDealModuleAbConfig(any(EnvCtx.class), anyLong())).thenReturn(value);
        ModuleAbConfig result = moduleAbBuilderService.getSimilarDealModuleAbConfig(envCtx, 1L);
        Assert.assertEquals(value, result);
    }

}
