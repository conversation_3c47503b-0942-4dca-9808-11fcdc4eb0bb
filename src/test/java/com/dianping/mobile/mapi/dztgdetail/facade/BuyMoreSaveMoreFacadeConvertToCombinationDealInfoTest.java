package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.mobile.mapi.dztgdetail.entity.CombinationDealInfo;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class BuyMoreSaveMoreFacadeConvertToCombinationDealInfoTest {

    @InjectMocks
    private BuyMoreSaveMoreFacade buyMoreSaveMoreFacade;

    private List<RecommendDTO> recommendDTOS;

    @Before
    public void setUp() {
        recommendDTOS = new ArrayList<>();
    }

    @Test
    public void testConvertToCombinationDealInfoEmptyRecommendDTOS() {
        List<CombinationDealInfo> result = buyMoreSaveMoreFacade.convertToCombinationDealInfo(recommendDTOS);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testConvertToCombinationDealInfoEmptyBizData() {
        RecommendDTO recommendDTO = new RecommendDTO();
        recommendDTO.setBizData(new HashMap<>());
        recommendDTOS.add(recommendDTO);
        List<CombinationDealInfo> result = buyMoreSaveMoreFacade.convertToCombinationDealInfo(recommendDTOS);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testConvertToCombinationDealInfoInvalidIsValid() {
        Map<String, Object> bizData = new HashMap<>();
        bizData.put("is_valid", "0");
        RecommendDTO recommendDTO = new RecommendDTO();
        recommendDTO.setBizData(bizData);
        recommendDTOS.add(recommendDTO);
        List<CombinationDealInfo> result = buyMoreSaveMoreFacade.convertToCombinationDealInfo(recommendDTOS);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testConvertToCombinationDealInfoValidIsValid() {
        Map<String, Object> bizData = new HashMap<>();
        bizData.put("is_valid", "1");
        bizData.put("productId_a", 1);
        bizData.put("productId_b", 2);
        RecommendDTO recommendDTO = new RecommendDTO();
        recommendDTO.setBizData(bizData);
        recommendDTOS.add(recommendDTO);
        List<CombinationDealInfo> result = buyMoreSaveMoreFacade.convertToCombinationDealInfo(recommendDTOS);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getMainDealId());
        assertEquals(2, result.get(0).getBindingDealId());
    }
}
