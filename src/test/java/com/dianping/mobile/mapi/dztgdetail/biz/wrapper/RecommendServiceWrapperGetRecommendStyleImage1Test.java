package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.entity.Response;
import com.dianping.martgeneral.recommend.api.enums.PlatformEnum;
import com.dianping.martgeneral.recommend.api.service.RecommendService;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageVO;
import com.dianping.mobile.mapi.dztgdetail.entity.QueryRecommendParam;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class RecommendServiceWrapperGetRecommendStyleImage1Test {

    @InjectMocks
    private RecommendServiceWrapper recommendServiceWrapper;

    @Mock
    private Future<Response<RecommendResult<RecommendDTO>>> future;

    @Mock
    private Response<RecommendResult<RecommendDTO>> response;

    @Mock
    private RecommendResult<RecommendDTO> recommendResult;

    @Mock
    private RecommendService recommendServiceFuture;

    @Mock
    private PoiClientWrapper poiClientWrapper;

    @Mock
    private ImmersiveImageWrapper immersiveImageWrapper;

    private QueryRecommendParam createQueryRecommendParam() {
        return QueryRecommendParam.builder().isMt(true).cityId(1).dpId("dpId").uuid("uuid").platformEnum(PlatformEnum.DP).latitude(1.0).longitude(1.0).exhibitImgIds(Collections.singletonList("exhibitImgId")).shopId(1L).start(1).limit(10).clientType(1).categoryId(1).dpDealGroupId(1L).build();
    }

    @Test
    public void testGetRecommendStyleImageNullFutureOrParam() throws Throwable {
        Method method = RecommendServiceWrapper.class.getDeclaredMethod("getRecommendStyleImage", Future.class, QueryRecommendParam.class);
        method.setAccessible(true);
        assertNull(method.invoke(recommendServiceWrapper, (Future<?>) null, (QueryRecommendParam) null));
    }

    @Test
    public void testGetRecommendStyleImageNullResponse() throws Throwable {
        when(future.get()).thenReturn(null);
        Method method = RecommendServiceWrapper.class.getDeclaredMethod("getRecommendStyleImage", Future.class, QueryRecommendParam.class);
        method.setAccessible(true);
        assertNull(method.invoke(recommendServiceWrapper, future, createQueryRecommendParam()));
    }

    @Test
    public void testGetRecommendStyleImageNullResult() throws Throwable {
        when(future.get()).thenReturn(response);
        when(response.getResult()).thenReturn(null);
        Method method = RecommendServiceWrapper.class.getDeclaredMethod("getRecommendStyleImage", Future.class, QueryRecommendParam.class);
        method.setAccessible(true);
        assertNull(method.invoke(recommendServiceWrapper, future, createQueryRecommendParam()));
    }

    @Test
    public void testGetRecommendStyleImageNullImmersiveImageVO() throws Throwable {
        when(future.get()).thenReturn(response);
        when(response.getResult()).thenReturn(recommendResult);
        when(recommendResult.getSortedResult()).thenReturn(null);
        Method method = RecommendServiceWrapper.class.getDeclaredMethod("getRecommendStyleImage", Future.class, QueryRecommendParam.class);
        method.setAccessible(true);
        assertNull(method.invoke(recommendServiceWrapper, future, createQueryRecommendParam()));
    }

    @Test
    public void testGetRecommendStyleImageNonNullImmersiveImageVO() throws Throwable {
        when(future.get()).thenReturn(response);
        when(response.getResult()).thenReturn(recommendResult);
        when(recommendResult.getSortedResult()).thenReturn(Collections.singletonList(new RecommendDTO()));
        when(immersiveImageWrapper.batchGetStyleImage(anyList(), anyString(), anyInt(), anyBoolean(), anyLong(), anyLong(), anyInt())).thenReturn(new ImmersiveImageVO());
        Method method = RecommendServiceWrapper.class.getDeclaredMethod("getRecommendStyleImage", Future.class, QueryRecommendParam.class);
        method.setAccessible(true);
        assertNotNull(method.invoke(recommendServiceWrapper, future, createQueryRecommendParam()));
    }
}
