package com.dianping.mobile.mapi.dztgdetail.action.h5;

import com.dianping.mobile.framework.base.datatypes.SimpleMsg;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.action.app.BuyMoreSaveMoreRecommendAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.BuyMoreSaveMoreReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.ExtraDealDetailModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.PriceExplainContentRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.ExtraDealDetailModuleResponse;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.PriceExplainContentDTO;
import com.dianping.mobile.mapi.dztgdetail.facade.BuyMoreSaveMoreFacade;
import com.dianping.mobile.mapi.dztgdetail.facade.PriceExplainContentFacade;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.Resps.SYSTEM_ERROR;
import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

public class PriceExplainContentH5ActionTest {
    @InjectMocks
    private PriceExplainContentH5Action action;

    @Mock
    private PriceExplainContentFacade facade;

    @Mock
    private IMobileContext context;

    @Mock
    private PriceExplainContentRequest request;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试异常场景
     */
    @Test
    public void testExecuteException() throws Throwable {
        // arrange
        when(facade.getPriceExplainContent(any(PriceExplainContentRequest.class), any())).thenThrow(new RuntimeException());
        // act
        IMobileResponse response = action.execute(request, null);
        // assert
        assertEquals(Resps.SYSTEM_ERROR, response);
    }

    /**
     * 测试正常场景
     */
    @Test
    public void testExecuteSuccess() throws Throwable {
        // arrange
        PriceExplainContentDTO expectedResponse = new PriceExplainContentDTO();
        when(facade.getPriceExplainContent(any(PriceExplainContentRequest.class), any())).thenReturn(expectedResponse);

        // act
        CommonMobileResponse response = (CommonMobileResponse) action.execute(request, null);

        // assert
        assertNotNull(response);
        Assertions.assertEquals(expectedResponse, response.getData());
    }

}
