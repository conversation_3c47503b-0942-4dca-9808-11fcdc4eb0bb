package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Sets;
import com.sankuai.athena.inf.cache2.CacheClient;
import com.sankuai.athena.inf.cache2.CacheKey;
import com.sankuai.athena.inf.cache2.loader.DataLoader;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupIdBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.reflect.Whitebox;

@RunWith(MockitoJUnitRunner.class)
public class MapperCacheWrapperFetchMtCityIdTest {

    @InjectMocks
    private MapperCacheWrapper mapperCacheWrapper;

    @Mock
    private CacheClient cacheClient;

    @Mock
    private MapperWrapper mapperWrapper;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    private static final String REDIS_DEAL_MAPPER_DEAL_ID = "mapper_deal_id";

    private CacheClient originalCacheClient;

    @Before
    public void setUp() {
        originalCacheClient = Whitebox.getInternalState(MapperCacheWrapper.class, "cacheClient");
        Whitebox.setInternalState(MapperCacheWrapper.class, "cacheClient", cacheClient);
    }

    @After
    public void tearDown() {
        Whitebox.setInternalState(MapperCacheWrapper.class, "cacheClient", originalCacheClient);
    }

    @Test
    public void testFetchMtCityIdWithDpCityIdLessThanOrEqualToZero() throws Throwable {
        // Test scenario where dpCityId is less than or equal to zero
        int dpCityId = 0;
        int result = mapperCacheWrapper.fetchMtCityId(dpCityId);
        assertEquals("Expected result for dpCityId <= 0", 0, result);
    }

    @Test
    public void testFetchMtCityIdWithCacheMiss() throws Throwable {
        // Test scenario where a cache miss occurs
        int dpCityId = 1;
        CompletableFuture<Object> future = CompletableFuture.completedFuture(null);
        int result = mapperCacheWrapper.fetchMtCityId(dpCityId);
        assertEquals("Expected result for cache miss", 0, result);
    }

    /**
     * Test when dpDealGroupId is <= 0
     * Should return 0 without calling cache
     */
    @Test
    public void testFetchMtDealIdWhenDpDealGroupIdIsZeroOrNegative() throws Throwable {
        // arrange
        int dpDealGroupId = 0;
        // act
        int result = mapperCacheWrapper.fetchMtDealId(dpDealGroupId);
        // assert
        assertEquals(0, result);
        verify(cacheClient, never()).asyncGetReadThrough(any(), any(), any(), anyInt(), anyInt());
    }

    /**
     * Test when cache returns valid value
     * Should return the cached value without fallback
     */
    @Test
    public void testFetchMtDealIdWhenCacheReturnsValidValue() throws Throwable {
        // arrange
        int dpDealGroupId = 123;
        int expectedMtDealId = 456;
        doAnswer(invocation -> {
            DataLoader<Integer> dataLoader = invocation.getArgument(2);
            return CompletableFuture.completedFuture(expectedMtDealId);
        }).when(cacheClient).asyncGetReadThrough(any(), any(), any(), anyInt(), anyInt());
        // act
        int result = mapperCacheWrapper.fetchMtDealId(dpDealGroupId);
        // assert
        assertEquals(expectedMtDealId, result);
        verify(queryCenterWrapper, never()).getDealGroupDTO(any(QueryByDealGroupIdRequest.class));
    }
}
