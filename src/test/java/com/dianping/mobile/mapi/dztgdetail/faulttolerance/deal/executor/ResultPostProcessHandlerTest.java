package com.dianping.mobile.mapi.dztgdetail.faulttolerance.deal.executor;

import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.FitnessCrossBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PriceDisplayModuleDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PromoDetailModule;
import org.junit.Assert;
import org.junit.Test;

import java.util.Collections;

public class ResultPostProcessHandlerTest {

    @Test
    @SuppressWarnings("unchecked")
    public void test() {
        DealCtx ctx = buildDealCtx();
        ResultPostProcessHandler.getInstance().execute(ctx.getResult(), ctx);

        PromoDetailModule promoDetailModule = ctx.getResult().getPromoDetailModule();
        PriceDisplayModuleDo priceDisplayModuleDo = ctx.getResult().getPriceDisplayModuleDo();

        Assert.assertEquals(promoDetailModule.getPromoPrice(), "0");
        Assert.assertEquals(promoDetailModule.getFinalPrice(), "0");
        Assert.assertEquals(priceDisplayModuleDo.getPrice(), "0");
        Assert.assertNull(promoDetailModule.getPromoAbstractList());
        Assert.assertNull(promoDetailModule.getCouponList());
        Assert.assertNull(promoDetailModule.getPromoActivityList());
        Assert.assertNull(promoDetailModule.getBestPromoDetails());
        Assert.assertNull(promoDetailModule.getMarketPromoDiscount());
        Assert.assertNull(promoDetailModule.getMarketPrice());
    }

    /**
     * 构建dealctx
     */
    public DealCtx buildDealCtx() {
        DealGroupPBO dealGroupPBO = new DealGroupPBO();
        dealGroupPBO.setPromoDetailModule(new PromoDetailModule());

        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setFitnessCrossBO(FitnessCrossBO.builder().build());
        ctx.setResult(dealGroupPBO);

        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("dealGroupFitnessPassConfig");
        attributeDTO.setValue(Collections.singletonList("fitnessPass"));
        ctx.setAttrs(Collections.singletonList(attributeDTO));
        return ctx;
    }

}