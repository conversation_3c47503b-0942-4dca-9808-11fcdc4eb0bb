package com.dianping.mobile.mapi.dztgdetail.facade;

import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.Lion;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.entity.Response;
import com.dianping.martgeneral.recommend.api.service.RecommendService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.*;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.BuyMoreSaveMoreReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.BuyMoreSaveMoreVO;
import com.dianping.mobile.mapi.dztgdetail.entity.CombinationDealInfo;
import com.dianping.mobile.mapi.dztgdetail.util.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayCenterService;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteResponse;
import org.apache.thrift.TException;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;
import java.util.concurrent.Future;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class BuyMoreSaveMoreFacadeNewTest {

    @InjectMocks
    private BuyMoreSaveMoreFacade buyMoreSaveMoreFacade;
    @Mock
    private DouHuBiz douHuBiz;
    @Mock
    private PoiShopCategoryWrapper poiShopCategoryWrapper;
    @Mock
    private PriceDisplayWrapper priceDisplayWrapper;
    @Mock
    private DzDealThemeWrapper dzDealThemeWrapper;
    @Mock
    private QueryCenterWrapper queryCenterWrapper;
    @Mock
    private DealStockSaleWrapper dealStockSaleWrapper;
    @Mock
    private Future<QueryDealGroupListResponse> queryCenterFuture;
    @Mock
    private RecommendService recommendService;
    @Mock
    private PlayCenterService.Iface playCenterService;

    private RecommendServiceWrapper recommendServiceWrapper;

    @Mock
    private MapperWrapper mapperWrapper;

    @Mock
    private PackBlackListWrapper packBlackListWrapper;

    @Mock
    private List<Future> futures;

    private MockedStatic<Lion> lionMockedStatic;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);

        // 创建真实的 RecommendServiceWrapper 实例
        recommendServiceWrapper = new RecommendServiceWrapper();

        // 手动注入 mock 的依赖到 RecommendServiceWrapper
        ReflectionTestUtils.setField(recommendServiceWrapper, "recommendService", recommendService);
        ReflectionTestUtils.setField(recommendServiceWrapper, "playCenterService", playCenterService);

        // 将 RecommendServiceWrapper 注入到 BuyMoreSaveMoreFacade
        ReflectionTestUtils.setField(buyMoreSaveMoreFacade, "recommendServiceWrapper", recommendServiceWrapper);
        lionMockedStatic = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        lionMockedStatic.close();
    }

    @Test
    public void testConvertToCombinationDealInfoEmptyList() {
        List<RecommendDTO> recommendDTOS = new ArrayList<>();
        List<CombinationDealInfo> result = buyMoreSaveMoreFacade.convertToCombinationDealInfo(recommendDTOS);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testConvertToCombinationDealInfoEmptyBizData() {
        List<RecommendDTO> recommendDTOS = new ArrayList<>();
        RecommendDTO recommendDTO = new RecommendDTO();
        recommendDTO.setBizData(new HashMap<>());
        recommendDTOS.add(recommendDTO);
        List<CombinationDealInfo> result = buyMoreSaveMoreFacade.convertToCombinationDealInfo(recommendDTOS);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testConvertToCombinationDealInfoInvalidBizData() {
        List<RecommendDTO> recommendDTOS = new ArrayList<>();
        RecommendDTO recommendDTO = new RecommendDTO();
        Map<String, Object> bizData = new HashMap<>();
        bizData.put("productId_a", 1);
        bizData.put("productId_b", 2);
        bizData.put("is_valid", "0");
        recommendDTO.setBizData(bizData);
        recommendDTOS.add(recommendDTO);
        List<CombinationDealInfo> result = buyMoreSaveMoreFacade.convertToCombinationDealInfo(recommendDTOS);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testConvertToCombinationDealInfoValidBizData() {
        List<RecommendDTO> recommendDTOS = new ArrayList<>();
        RecommendDTO recommendDTO = new RecommendDTO();
        Map<String, Object> bizData = new HashMap<>();
        bizData.put("productId_a", 1);
        bizData.put("productId_b", 2);
        bizData.put("is_valid", "1");
        recommendDTO.setBizData(bizData);
        recommendDTOS.add(recommendDTO);
        List<CombinationDealInfo> result = buyMoreSaveMoreFacade.convertToCombinationDealInfo(recommendDTOS);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getMainDealId());
        assertEquals(2, result.get(0).getBindingDealId());
    }

    @Test
    public void testBuildDealProductRequest() {
        EnvCtx ctx = new EnvCtx();
        ctx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealProductRequest request = buyMoreSaveMoreFacade.buildDealProductRequest(ctx, new ArrayList<>(), 1L);
        Assert.assertTrue(Objects.nonNull(request));
    }

    @Test
    public void testGetRecommendCombineDeal() throws JsonProcessingException, TException {
        // 构建req
        BuyMoreSaveMoreReq buyMoreSaveMoreReq = new BuyMoreSaveMoreReq();
        buyMoreSaveMoreReq.setSourceType(1);
        buyMoreSaveMoreReq.setPoiidStr("10086");
        buyMoreSaveMoreReq.setStart(0);
        buyMoreSaveMoreReq.setLimit(10);
        buyMoreSaveMoreReq.setCityId(1);
        buyMoreSaveMoreReq.setSource("poi_page");
        // 构建ctx
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        envCtx.setUnionId("000fjfjfj");
        // MOCK
        when(poiShopCategoryWrapper.queryShopCategoryIds(anyLong(), anyBoolean())).thenReturn(Sets.newHashSet());
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("c");
        abConfig.setExpBiInfo("cccc");
        moduleAbConfig.setConfigs(Lists.newArrayList(abConfig));
        when(douHuBiz.getAbExpResult((EnvCtx) any(), anyString())).thenReturn(moduleAbConfig);

        lionMockedStatic.when(() -> Lion.getMap(LionConstants.APP_KEY, LionConstants.PAGESOURCE_TO_ORDER_TRAFFICFLAG,
                String.class, Collections.emptyMap())).thenReturn(new HashMap<>());

        JSONObject jsonObject = new JSONObject();
        List<Map<String, String>> sortedResult = Lists.newArrayList();
        Map<String, String> data = Maps.newHashMap();
        Map<String, String> bizData = Maps.newHashMap();
        bizData.put("is_valid", "1");
        bizData.put("isSameShop", "1");
        bizData.put("productId_a", "239129312030");
        bizData.put("productId_b", "2391294");
        data.put("item", "239129312030_2391294");
        data.put("bizData", JsonUtils.toJson(bizData));

        sortedResult.add(data);
        jsonObject.put("sortedResult", sortedResult);

        PlayExecuteResponse playExecuteResponse = new PlayExecuteResponse();
        playExecuteResponse.setResult(jsonObject.toJSONString());
        when(playCenterService.executePlay(any())).thenReturn(playExecuteResponse);


        List<RecommendDTO> recommendDTOS = Lists.newArrayList();
        RecommendDTO recommendDTO = new RecommendDTO();
        Map<String, Object> bizDataMap = Maps.newHashMap();
        bizDataMap.put("productId_a", "239129312030");
        bizDataMap.put("productId_b", "2391293");
        bizDataMap.put("is_valid", "1");
        recommendDTO.setBizData(bizDataMap);
        recommendDTO.setItem("239129312030_2391293");
        recommendDTOS.add(recommendDTO);

        Response<RecommendResult<RecommendDTO>> resultResponse = new Response<>();
        RecommendResult<RecommendDTO> result = new RecommendResult<>();
        result.setSortedResult(recommendDTOS);
        resultResponse.setResult(result);
        when(recommendService.recommend(any(), eq(RecommendDTO.class))).thenReturn(resultResponse);


        // 后面的Mock实在写不动了，大家再接再厉
        when(priceDisplayWrapper.getBuyMoreSaveMorePriceInfo(anyInt(), any(), anyLong(), any(), any())).thenReturn(null);
        when(dzDealThemeWrapper.preQueryDealSubTitle(any())).thenReturn(null);
        when(queryCenterWrapper.preDealGroupDTO(any())).thenReturn(queryCenterFuture);
        when(queryCenterWrapper.getDealGroupDTOs(queryCenterFuture)).thenReturn(null);
        when(dealStockSaleWrapper.preUnifiedStocksFuture(any())).thenReturn(null);
        when(packBlackListWrapper.prePackBlackLis(any(),any())).thenReturn(futures);



        // 执行
        BuyMoreSaveMoreVO buyMoreSaveMoreVO = buyMoreSaveMoreFacade.getRecommendCombineDeal(buyMoreSaveMoreReq, envCtx);

        // ASSERT
        assertEquals(buyMoreSaveMoreVO.getExpBiInfo(), "cccc");
    }
}
