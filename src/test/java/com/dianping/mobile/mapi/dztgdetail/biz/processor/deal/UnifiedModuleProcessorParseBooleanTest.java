package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.DealCategoryFactory;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryParam;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleExtraDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedModuleProcessorParseBooleanTest {

    private UnifiedModuleProcessor processor = new UnifiedModuleProcessor();

    @InjectMocks
    private UnifiedModuleProcessor unifiedModuleProcessor;

    @Mock
    private DealCtx ctx;

    @Mock
    private ModuleExtraDTO moduleExtraDTO;

    @Mock
    private DealGroupDTO dealGroupDTO;

    @Mock
    private DealCategoryFactory dealCategoryFactory;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
    }

    private boolean invokeParseBoolean(String input) throws Throwable {
        try {
            UnifiedModuleProcessor processor = new UnifiedModuleProcessor();
            Method method = UnifiedModuleProcessor.class.getDeclaredMethod("parseBoolean", String.class);
            method.setAccessible(true);
            return (boolean) method.invoke(processor, input);
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            throw e.getCause();
        }
    }

    private boolean parseBoolean(String string) {
        if (org.apache.commons.lang3.StringUtils.isBlank(string)) {
            return false;
        } else {
            return Boolean.parseBoolean(string);
        }
    }

    private int invokePrivateMethod(UnifiedModuleProcessor processor, String methodName, DealGroupDTO dealGroupDTO) throws Throwable {
        try {
            Method method = UnifiedModuleProcessor.class.getDeclaredMethod(methodName, DealGroupDTO.class);
            method.setAccessible(true);
            return (int) method.invoke(processor, dealGroupDTO);
        } catch (Exception e) {
            throw e.getCause();
        }
    }

    /**
     * 测试字符串为 null 的情况
     */
    @Test
    public void testParseBooleanNullString() throws Throwable {
        // arrange
        String input = null;
        // act
        boolean result = invokeParseBoolean(input);
        // assert
        assertFalse(result);
    }

    /**
     * 测试字符串为空字符串的情况
     */
    @Test
    public void testParseBooleanEmptyString() throws Throwable {
        // arrange
        String input = "";
        // act
        boolean result = invokeParseBoolean(input);
        // assert
        assertFalse(result);
    }

    /**
     * 测试字符串仅包含空白字符的情况
     */
    @Test
    public void testParseBooleanBlankString() throws Throwable {
        // arrange
        String input = "   ";
        // act
        boolean result = invokeParseBoolean(input);
        // assert
        assertFalse(result);
    }

    /**
     * 测试字符串为 "true" 的情况
     */
    @Test
    public void testParseBooleanTrueString() throws Throwable {
        // arrange
        String input = "true";
        // act
        boolean result = invokeParseBoolean(input);
        // assert
        assertTrue(result);
    }

    /**
     * 测试字符串为 "false" 的情况
     */
    @Test
    public void testParseBooleanFalseString() throws Throwable {
        // arrange
        String input = "false";
        // act
        boolean result = invokeParseBoolean(input);
        // assert
        assertFalse(result);
    }

    /**
     * 测试字符串为其他非布尔值的情况
     */
    @Test
    public void testParseBooleanNonBooleanString() throws Throwable {
        // arrange
        String input = "random";
        // act
        boolean result = invokeParseBoolean(input);
        // assert
        assertFalse(result);
    }

    /**
     * 测试字符串为 "TRUE"（大写）的情况
     */
    @Test
    public void testParseBooleanTrueStringUpperCase() throws Throwable {
        // arrange
        String input = "TRUE";
        // act
        boolean result = invokeParseBoolean(input);
        // assert
        assertTrue(result);
    }

    /**
     * 测试字符串为 "FALSE"（大写）的情况
     */
    @Test
    public void testParseBooleanFalseStringUpperCase() throws Throwable {
        // arrange
        String input = "FALSE";
        // act
        boolean result = invokeParseBoolean(input);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 dealGroupDTO.getDeals() 为空的情况
     */
    @Test
    public void testGetThirdPartyId_EmptyDeals() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getDeals()).thenReturn(Collections.emptyList());
        // act
        int result = invokePrivateMethod(processor, "getThirdPartyId", dealGroupDTO);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试 dealGroupDTO.getDeals() 不为空，但所有 dealDTO 都不满足条件的情况
     */
    @Test
    public void testGetThirdPartyId_NoValidDealDTO() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDealDTO dealDTO = mock(DealGroupDealDTO.class);
        when(dealGroupDTO.getDeals()).thenReturn(Collections.singletonList(dealDTO));
        when(dealDTO.getBasic()).thenReturn(null);
        // act
        int result = invokePrivateMethod(processor, "getThirdPartyId", dealGroupDTO);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试 dealDTO 为 null 的情况
     */
    @Test
    public void testGetThirdPartyId_NullDealDTO() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getDeals()).thenReturn(Collections.singletonList(null));
        // act
        int result = invokePrivateMethod(processor, "getThirdPartyId", dealGroupDTO);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试 dealDTO.getBasic() 为 null 的情况
     */
    @Test
    public void testGetThirdPartyId_NullBasicDTO() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDealDTO dealDTO = mock(DealGroupDealDTO.class);
        when(dealGroupDTO.getDeals()).thenReturn(Collections.singletonList(dealDTO));
        when(dealDTO.getBasic()).thenReturn(null);
        // act
        int result = invokePrivateMethod(processor, "getThirdPartyId", dealGroupDTO);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试 process 方法在 queryUnifiedModuleExtraDTO 抛出异常的情况下的执行
     */
    @Test(expected = Exception.class)
    public void testProcessWhenQueryUnifiedModuleExtraDTOThrowsException() throws Throwable {
        // arrange
        when(unifiedModuleProcessor.queryUnifiedModuleExtraDTO(ctx)).thenThrow(new Exception("Test Exception"));
        doNothing().when(dealCategoryFactory).resetDealDetailModuleConfig(any(DealCategoryParam.class));
        // act
        unifiedModuleProcessor.process(ctx);
        // assert
        // 期望抛出异常
    }
}
