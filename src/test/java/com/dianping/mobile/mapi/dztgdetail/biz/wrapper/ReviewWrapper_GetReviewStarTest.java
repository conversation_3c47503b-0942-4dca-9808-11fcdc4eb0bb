package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.when;
import com.dianping.reviewremote.remote.dto.ReviewStarDistributionDTO;
import java.util.Arrays;
import java.util.concurrent.Future;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

public class ReviewWrapper_GetReviewStarTest {

    private ReviewWrapper reviewWrapper;

    private Future future;

    @Before
    public void setUp() {
        reviewWrapper = new ReviewWrapper();
        future = Mockito.mock(Future.class);
    }

    /**
     * 测试 getReviewStar 方法，当 Future 对象返回的 ReviewStarDistributionDTO 列表为空时
     */
    @Test
    public void testGetReviewStarWhenListIsEmpty() throws Throwable {
        // arrange
        when(future.get()).thenReturn(null);
        // act
        ReviewStarDistributionDTO result = reviewWrapper.getReviewStar(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 getReviewStar 方法，当 Future 对象返回的 ReviewStarDistributionDTO 列表不为空时
     */
    @Test
    public void testGetReviewStarWhenListIsNotEmpty() throws Throwable {
        // arrange
        ReviewStarDistributionDTO dto = new ReviewStarDistributionDTO(1L);
        when(future.get()).thenReturn(Arrays.asList(dto));
        // act
        ReviewStarDistributionDTO result = reviewWrapper.getReviewStar(future);
        // assert
        assertSame(dto, result);
    }
}
