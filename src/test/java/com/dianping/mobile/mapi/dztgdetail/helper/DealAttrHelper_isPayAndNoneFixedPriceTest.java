package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import static org.junit.Assert.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealAttrHelper_isPayAndNoneFixedPriceTest {
    private List<Long> filterTypes = Arrays.asList(1L, 2L);
    private MockedStatic<Lion> mocked;

    @Before
    public void setUp() {
        mocked = Mockito.mockStatic(Lion.class);
        mocked.when(() -> Lion.getList(Environment.getAppName(), "com.sankuai.dzu.tpbase.dztgdetailweb.life.dealGroupCategory", Long.class, new ArrayList<>())).thenReturn(filterTypes);
    }

    @After
    public void tearDown() {
        mocked.close();
    }

    /**
     * 测试dealGroupDTO为null时，方法返回false
     */
    @Test
    public void testIsPayAndNoneFixedPrice_DealGroupIsNull() {
        assertFalse(DealAttrHelper.isPayAndNotOnePrice(null));
    }

    /**
     * 测试dealGroupDTO的category为null时，方法返回false
     */
    @Test
    public void testIsPayAndNoneFixedPrice_CategoryIsNull() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setCategory(new DealGroupCategoryDTO());
        dealGroupDTO.getCategory().setCategoryId(null);
        assertFalse(DealAttrHelper.isPayAndNotOnePrice(dealGroupDTO));
    }

    /**
     * 测试dealGroupDTO的category的categoryId不在filterTypes中时，方法返回false
     */
    @Test
    public void testIsPayAndNotOnePrice_CategoryIdNotInFilterTypes() {
        // 准备mock数据
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setCategory(new DealGroupCategoryDTO());
        dealGroupDTO.getCategory().setCategoryId(0L);
        
        assertFalse(DealAttrHelper.isPayAndNotOnePrice(dealGroupDTO));
    }

    /**
     * 测试支付方式不为2时，方法返回false
     */
    @Test
    public void testIsPayAndNoneFixedPrice_PayMethodNot2() {
        // 准备mock数据
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setCategory(new DealGroupCategoryDTO());
        dealGroupDTO.getCategory().setCategoryId(1L);
        AttrDTO attr = new AttrDTO();
        attr.setName("pay_method"); attr.setValue(Arrays.asList("1"));
        dealGroupDTO.setAttrs(Arrays.asList(attr));
        assertFalse(DealAttrHelper.isPayAndNotOnePrice(dealGroupDTO));
    }

    /**
     * 测试支付方式为2，但电脑维修商品类型和其他分商品类型均为"一口价"时，方法返回false
     */
    @Test
    public void testIsPayAndNoneFixedPrice_PayMethod2AndFixedPrice() {
        // 准备mock数据
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setCategory(new DealGroupCategoryDTO());
        dealGroupDTO.getCategory().setCategoryId(1L);
        AttrDTO attr = new AttrDTO();
        attr.setName("pay_method"); attr.setValue(Arrays.asList("2"));
        attr.setName("repair_project_type"); attr.setValue(Arrays.asList("一口价"));
        dealGroupDTO.setAttrs(Arrays.asList(attr));

        assertFalse(DealAttrHelper.isPayAndNotOnePrice(dealGroupDTO));
    }

    /**
     * 测试支付方式为2，且至少一个商品类型不为"一口价"时，方法返回true
     */
    @Test
    public void testIsPayAndNoneFixedPrice_PayMethod2AndNotAllFixedPrice() {
        // 准备mock数据
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setCategory(new DealGroupCategoryDTO());
        dealGroupDTO.getCategory().setCategoryId(1L);
        AttrDTO attr1 = new AttrDTO();
        attr1.setName("pay_method"); attr1.setValue(Arrays.asList("2"));
        AttrDTO attr2 = new AttrDTO();
        attr2.setName("repair_project_type"); attr2.setValue(Arrays.asList("非一口价"));
        dealGroupDTO.setAttrs(Arrays.asList(attr1, attr2));

        assertTrue(DealAttrHelper.isPayAndNotOnePrice(dealGroupDTO));
    }
}
