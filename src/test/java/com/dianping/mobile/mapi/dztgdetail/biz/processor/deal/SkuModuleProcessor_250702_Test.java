package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2025/7/3 11:00
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({ Lion.class })
public class SkuModuleProcessor_250702_Test {

    @InjectMocks
    private SkuModuleProcessor skuModuleProcessor;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(Lion.class);
    }

    @Test
    public void test() throws InvocationTargetException, IllegalAccessException {
        AttrDTO attrDTO = Mockito.mock(AttrDTO.class);
        when(attrDTO.getName()).thenReturn("ClassCount");
        when(attrDTO.getValue()).thenReturn(Lists.newArrayList("12测试"));
        Method method = PowerMockito.method(SkuModuleProcessor.class, "getEduMusicAttr");
        String result = (String) method.invoke(skuModuleProcessor,attrDTO);
        assert result.contains("节课");
    }

}