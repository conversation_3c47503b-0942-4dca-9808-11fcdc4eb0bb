package com.dianping.mobile.mapi.dztgdetail.biz.processor;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.BeautyTattoConfigWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.TattooPrecautionsVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.TattooQAsVO;
import com.google.common.collect.Lists;
import com.sankuai.beautycontent.function.tattoo.dto.AftercareFAQsDTO;
import com.sankuai.beautycontent.function.tattoo.dto.FaqDTO;
import com.sankuai.beautycontent.function.tattoo.dto.StepDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.BeautyTattoConfigDataProcessor;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BeautyTattoConfigDataProcessorTest {

    @InjectMocks
    BeautyTattoConfigDataProcessor beautyTattoConfigDataProcessor;

    @Mock
    BeautyTattoConfigWrapper beautyTattoConfigWrapper;

    /**
     * CategoryID错误
     */
    @Test
    public void testErrorCategoryId(){
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroup = new DealGroupDTO();
        DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
        dealGroupCategoryDTO.setCategoryId(1L);
        dealGroup.setCategory(dealGroupCategoryDTO);
        ctx.setDealGroupDTO(dealGroup);
        beautyTattoConfigDataProcessor.prepare(ctx);
        Assert.assertTrue(ctx.getFutureCtx().getBeautyTattooFuture() == null);
    }


    /**
     * TypeID错误
     */
    @Test
    public void testErrorTypeId(){
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroup = new DealGroupDTO();
        DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
        dealGroupCategoryDTO.setCategoryId(512L);
        dealGroupCategoryDTO.setServiceTypeId(1373L);
        dealGroup.setCategory(dealGroupCategoryDTO);
        List<DealGroupTagDTO> tagDTOs = new ArrayList<>();
        DealGroupTagDTO dealGroupTagDTO = new DealGroupTagDTO();
        dealGroupTagDTO.setId(1L);
        tagDTOs.add(dealGroupTagDTO);
        dealGroup.setTags(tagDTOs);
        ctx.setDealGroupDTO(dealGroup);
        beautyTattoConfigDataProcessor.prepare(ctx);
        Assert.assertTrue(ctx.getFutureCtx().getBeautyTattooFuture() == null);
    }

    /**
     * 通过二三级分类正常流程,上游无法返回
     */
    @Test
    public void testNormal() throws ClassNotFoundException, InstantiationException, IllegalAccessException, NoSuchFieldException {
        // 创建BeautyTattoConfigDataProcessor对象
        Class<?> beautyTattoConfigDataProcessorClass = Class.forName("com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.BeautyTattoConfigDataProcessor");
        BeautyTattoConfigDataProcessor beautyTattoConfigDataProcessor1 = (BeautyTattoConfigDataProcessor) beautyTattoConfigDataProcessorClass.newInstance();

        // 注入BeautyTattoConfigWrapper类
        Class<?> beautyTattoConfigWrapperClass = Class.forName("com.dianping.mobile.mapi.dztgdetail.biz.wrapper.BeautyTattoConfigWrapper");
        BeautyTattoConfigWrapper beautyTattoConfigWrapper = (BeautyTattoConfigWrapper) beautyTattoConfigWrapperClass.newInstance();
        Field beautyTattoConfigWrapperField = beautyTattoConfigDataProcessorClass.getDeclaredField("beautyTattoConfigWrapper");
        beautyTattoConfigWrapperField.setAccessible(true);
        beautyTattoConfigWrapperField.set(beautyTattoConfigDataProcessor1, beautyTattoConfigWrapper);

        //

        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroup = new DealGroupDTO();
        DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
        dealGroupCategoryDTO.setCategoryId(512L);
        dealGroup.setCategory(dealGroupCategoryDTO);
        List<DealGroupTagDTO> tagDTOs = new ArrayList<>();
        DealGroupTagDTO dealGroupTagDTO = new DealGroupTagDTO();
        dealGroupTagDTO.setId(100211620L);
        tagDTOs.add(dealGroupTagDTO);
        dealGroup.setTags(tagDTOs);
        ctx.setDealGroupDTO(dealGroup);
        beautyTattoConfigDataProcessor1.prepare(ctx);
        Assert.assertTrue(ctx.getFutureCtx().getBeautyTattooFuture() == null);
    }

    /**
     * erviceTypeId() == 137013的正常流程,上游无法返回
     */
    @Test
    public void testNormal2() throws ClassNotFoundException, InstantiationException, IllegalAccessException, NoSuchFieldException {
        // 创建BeautyTattoConfigDataProcessor对象
        Class<?> beautyTattoConfigDataProcessorClass = Class.forName("com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.BeautyTattoConfigDataProcessor");
        BeautyTattoConfigDataProcessor beautyTattoConfigDataProcessor1 = (BeautyTattoConfigDataProcessor) beautyTattoConfigDataProcessorClass.newInstance();

        // 注入BeautyTattoConfigWrapper类
        Class<?> beautyTattoConfigWrapperClass = Class.forName("com.dianping.mobile.mapi.dztgdetail.biz.wrapper.BeautyTattoConfigWrapper");
        BeautyTattoConfigWrapper beautyTattoConfigWrapper = (BeautyTattoConfigWrapper) beautyTattoConfigWrapperClass.newInstance();
        Field beautyTattoConfigWrapperField = beautyTattoConfigDataProcessorClass.getDeclaredField("beautyTattoConfigWrapper");
        beautyTattoConfigWrapperField.setAccessible(true);
        beautyTattoConfigWrapperField.set(beautyTattoConfigDataProcessor1, beautyTattoConfigWrapper);

        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroup = new DealGroupDTO();
        DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
        dealGroupCategoryDTO.setCategoryId(512L);
        dealGroupCategoryDTO.setServiceTypeId(137013L);
        dealGroup.setCategory(dealGroupCategoryDTO);
        List<DealGroupTagDTO> tagDTOs = new ArrayList<>();
        dealGroup.setTags(tagDTOs);
        ctx.setDealGroupDTO(dealGroup);
        beautyTattoConfigDataProcessor1.prepare(ctx);
        Assert.assertTrue(ctx.getFutureCtx().getBeautyTattooFuture() == null);
    }

    /**
     * 验证decoder
     */
    @Test
    public void testDecoder(){
        AftercareFAQsDTO aftercareFAQsDTO = new AftercareFAQsDTO();

        // 设置tagId
        aftercareFAQsDTO.setTagId(1);

        // 设置steps
        LinkedHashMap<String, List<String>> steps = new LinkedHashMap<>();
        List<String> day1Steps = new ArrayList<>();
        day1Steps.add("Step 1");
        day1Steps.add("Step 2");
        steps.put("Day 1", day1Steps);
        List<String> day2Steps = new ArrayList<>();
        day2Steps.add("Step 3");
        day2Steps.add("Step 4");
        steps.put("Day 2", day2Steps);
        aftercareFAQsDTO.setSteps(steps);

        // 设置faqs
        List<FaqDTO> faqs = new ArrayList<>();
        FaqDTO faq1 = new FaqDTO();
        faq1.setQuestion("Question 1");
        faq1.setAnswer("Answer 1");
        faqs.add(faq1);
        FaqDTO faq2 = new FaqDTO();
        faq2.setQuestion("Question 2");
        faq2.setAnswer("Answer 2");
        faqs.add(faq2);
        aftercareFAQsDTO.setFaqs(faqs);
        List<StepDTO> stepsDTO = Lists.newArrayList();
        StepDTO stepDTO = new StepDTO();
        stepDTO.setTitle("title");
        List<String> content = Lists.newArrayList();
        content.add("content");
        stepDTO.setContent(content);
        stepsDTO.add(stepDTO);
        aftercareFAQsDTO.setStepsDTO(stepsDTO);

//        System.out.println(aftercareFAQsDTO);
        TattooPrecautionsVO tattooPrecautionsVO = beautyTattoConfigDataProcessor.buildTattooPrecautionsVO(aftercareFAQsDTO);
        TattooQAsVO tattooQAsVO = beautyTattoConfigDataProcessor.buildTattooQAsVO(aftercareFAQsDTO);
        Assert.assertNotNull(tattooQAsVO);

    }

    @Test
    public void testHashMap(){
        Map<String, String> map = new LinkedHashMap<>();
        map.put("1", "1");
        map.put("2", "2");
        map.put("5", "5");
        map.put("3", "3");
        map.put("4", "4");
        for(Map.Entry<String, String> entry : map.entrySet()){
            System.out.println(entry.getKey()+entry.getValue());
        }
        Assert.assertNotNull(map);

    }

    @Test
    public void testCategoryNull(){
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroup = new DealGroupDTO();
        ctx.setDealGroupDTO(dealGroup);
        beautyTattoConfigDataProcessor.prepare(ctx);
        assert ctx.getDealGroupDTO().getCategory() == null;
    }
}
