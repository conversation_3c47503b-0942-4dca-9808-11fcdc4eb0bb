package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CommonAttrVO;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.OptionalServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BeautyHighlightsProcessorTest {

    @InjectMocks
    private BeautyHighlightsProcessor beautyHighlightsProcessor;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    private DealCtx ctx;

    @Mock
    private DealCtx mockDealCtx;

    @Mock
    private Logger logger;

    @Before
    public void setUp() {
        EnvCtx envCtx = new EnvCtx();
        ctx = new DealCtx(envCtx);
        ctx.setFutureCtx(new FutureCtx());
    }

    private List<ServiceProjectDTO> invokeGetServiceProjectSku(DealGroupDTO dealGroupDTO, int skuId) throws Exception {
        Method method = BeautyHighlightsProcessor.class.getDeclaredMethod("getServiceProjectSku", DealGroupDTO.class, int.class);
        method.setAccessible(true);
        return (List<ServiceProjectDTO>) method.invoke(beautyHighlightsProcessor, dealGroupDTO, skuId);
    }

    /**
     * Helper method to create DealGroupTagDTO with specified ID
     */
    private DealGroupTagDTO createDealGroupTagDTO(Long id) {
        DealGroupTagDTO tag = mock(DealGroupTagDTO.class);
        when(tag.getId()).thenReturn(id);
        return tag;
    }

    /**
     * Helper method to invoke the private getDealTags method using reflection
     */
    private List<Long> invokeGetDealTags(DealGroupDTO dealGroup) throws Exception {
        Method method = BeautyHighlightsProcessor.class.getDeclaredMethod("getDealTags", DealGroupDTO.class);
        method.setAccessible(true);
        return (List<Long>) method.invoke(beautyHighlightsProcessor, dealGroup);
    }

    private String invokePrivateMethod(BeautyHighlightsProcessor processor, String methodName, DealGroupDTO dealGroupDTO, int skuId, String attrName) throws Exception {
        Method method = BeautyHighlightsProcessor.class.getDeclaredMethod(methodName, DealGroupDTO.class, int.class, String.class);
        method.setAccessible(true);
        return (String) method.invoke(beautyHighlightsProcessor, dealGroupDTO, skuId, attrName);
    }

    /**
     * 测试正常情况，ctx.isMt() 返回 true
     */
    @Test
    public void testPrepareIsMtTrue() throws Throwable {
        // arrange
        ctx.setMtId(1);
        when(queryCenterWrapper.preDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(mock(Future.class));
        // act
        beautyHighlightsProcessor.prepare(ctx);
        // assert
        verify(queryCenterWrapper, times(1)).preDealGroupDTO(any(QueryByDealGroupIdRequest.class));
    }

    /**
     * 测试正常情况，ctx.isMt() 返回 false
     */
    @Test
    public void testPrepareIsMtFalse() throws Throwable {
        // arrange
        ctx.setDpId(1);
        when(queryCenterWrapper.preDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(mock(Future.class));
        // act
        beautyHighlightsProcessor.prepare(ctx);
        // assert
        verify(queryCenterWrapper, times(1)).preDealGroupDTO(any(QueryByDealGroupIdRequest.class));
    }

    /**
     * 测试 queryCenterWrapper.preDealGroupDTO(request) 抛出异常的情况
     */
    @Test(expected = RuntimeException.class)
    public void testPrepareQueryCenterWrapperThrowsException() throws Throwable {
        // arrange
        ctx.setMtId(1);
        when(queryCenterWrapper.preDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenThrow(RuntimeException.class);
        // act
        beautyHighlightsProcessor.prepare(ctx);
        // assert
        verify(queryCenterWrapper, times(1)).preDealGroupDTO(any(QueryByDealGroupIdRequest.class));
    }

    /**
     * 测试场景：dealGroup为null
     */
    @Test
    public void testBuildWearableNail_DealGroupIsNull() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setDealGroupDTO(null);
        beautyHighlightsProcessor.buildWearableNail(ctx);
        assertNull("HighlightsModule should be null when dealGroup is null", ctx.getHighlightsModule());
    }

    /**
     * 测试场景：dealGroup不为null，但所有属性均为空
     */
    @Test
    public void testBuildWearableNail_AllAttributesAreEmpty() {
        DealGroupDTO dealGroup = mock(DealGroupDTO.class);
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setDealGroupDTO(dealGroup);
        beautyHighlightsProcessor.buildWearableNail(ctx);
        assertNull("HighlightsModule should be null when all attributes are empty", ctx.getHighlightsModule());
    }

    /**
     * 测试场景：dealGroup不为null，isFreeWearingAtStore为true
     */
    @Test
    public void testBuildWearableNail_IsFreeWearingAtStoreTrue() {
        DealGroupDTO dealGroup = mock(DealGroupDTO.class);
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr1 = new AttrDTO();
        attr1.setName("isFreeWearingAtStore");
        attr1.setValue(Lists.newArrayList("true"));
        attrs.add(attr1);
        AttrDTO attr2 = new AttrDTO();
        attr2.setName("wearingNailsType");
        attr2.setValue(Lists.newArrayList("机器穿戴甲"));
        attrs.add(attr2);
        AttrDTO attr3 = new AttrDTO();
        attr3.setName("nail_additional_item");
        attr3.setValue(Lists.newArrayList("赠佩戴工具包"));
        attrs.add(attr3);
        when(dealGroup.getAttrs()).thenReturn(attrs);
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setDealGroupDTO(dealGroup);
        beautyHighlightsProcessor.buildWearableNail(ctx);
        assertNotNull("HighlightsModule should not be null when isFreeWearingAtStore is true", ctx.getHighlightsModule());
        assertEquals(3, ctx.getHighlightsModule().getAttrs().size());
    }

    /**
     * Test when serviceProject is null in dealGroupDTO
     */
    @Test
    public void testGetServiceProjectSku_NullServiceProject() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getServiceProject()).thenReturn(null);
        int skuId = 123;
        // act
        List<ServiceProjectDTO> result = invokeGetServiceProjectSku(dealGroupDTO, skuId);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test when both mustGroups and optionGroups are empty
     */
    @Test
    public void testGetServiceProjectSku_EmptyGroups() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupServiceProjectDTO serviceProject = mock(DealGroupServiceProjectDTO.class);
        when(dealGroupDTO.getServiceProject()).thenReturn(serviceProject);
        when(serviceProject.getMustGroups()).thenReturn(Lists.newArrayList());
        when(serviceProject.getOptionGroups()).thenReturn(Lists.newArrayList());
        int skuId = 123;
        // act
        List<ServiceProjectDTO> result = invokeGetServiceProjectSku(dealGroupDTO, skuId);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test when no service projects match the skuId
     */
    @Test
    public void testGetServiceProjectSku_NoMatchingProjects() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupServiceProjectDTO serviceProject = mock(DealGroupServiceProjectDTO.class);
        MustServiceProjectGroupDTO mustGroup = mock(MustServiceProjectGroupDTO.class);
        ServiceProjectDTO projectDTO = mock(ServiceProjectDTO.class);
        when(dealGroupDTO.getServiceProject()).thenReturn(serviceProject);
        when(serviceProject.getMustGroups()).thenReturn(Lists.newArrayList(mustGroup));
        when(mustGroup.getGroups()).thenReturn(Lists.newArrayList(projectDTO));
        when(projectDTO.getCategoryId()).thenReturn(456L);
        int skuId = 123;
        // act
        List<ServiceProjectDTO> result = invokeGetServiceProjectSku(dealGroupDTO, skuId);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test when there are matching service projects from mustGroups
     */
    @Test
    public void testGetServiceProjectSku_MatchingMustProjects() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupServiceProjectDTO serviceProject = mock(DealGroupServiceProjectDTO.class);
        MustServiceProjectGroupDTO mustGroup = mock(MustServiceProjectGroupDTO.class);
        ServiceProjectDTO projectDTO = mock(ServiceProjectDTO.class);
        when(dealGroupDTO.getServiceProject()).thenReturn(serviceProject);
        when(serviceProject.getMustGroups()).thenReturn(Lists.newArrayList(mustGroup));
        when(mustGroup.getGroups()).thenReturn(Lists.newArrayList(projectDTO));
        when(projectDTO.getCategoryId()).thenReturn(123L);
        int skuId = 123;
        // act
        List<ServiceProjectDTO> result = invokeGetServiceProjectSku(dealGroupDTO, skuId);
        // assert
        assertEquals(1, result.size());
        assertEquals(projectDTO, result.get(0));
    }

    /**
     * Test when there are matching service projects from optionGroups
     */
    @Test
    public void testGetServiceProjectSku_MatchingOptionalProjects() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupServiceProjectDTO serviceProject = mock(DealGroupServiceProjectDTO.class);
        OptionalServiceProjectGroupDTO optionalGroup = mock(OptionalServiceProjectGroupDTO.class);
        ServiceProjectDTO projectDTO = mock(ServiceProjectDTO.class);
        when(dealGroupDTO.getServiceProject()).thenReturn(serviceProject);
        when(serviceProject.getMustGroups()).thenReturn(Lists.newArrayList());
        when(serviceProject.getOptionGroups()).thenReturn(Lists.newArrayList(optionalGroup));
        when(optionalGroup.getGroups()).thenReturn(Lists.newArrayList(projectDTO));
        when(projectDTO.getCategoryId()).thenReturn(123L);
        int skuId = 123;
        // act
        List<ServiceProjectDTO> result = invokeGetServiceProjectSku(dealGroupDTO, skuId);
        // assert
        assertEquals(1, result.size());
        assertEquals(projectDTO, result.get(0));
    }

    /**
     * Test when there are multiple matching service projects from both mustGroups and optionGroups
     */
    @Test
    public void testGetServiceProjectSku_MultipleMatchingProjects() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupServiceProjectDTO serviceProject = mock(DealGroupServiceProjectDTO.class);
        MustServiceProjectGroupDTO mustGroup = mock(MustServiceProjectGroupDTO.class);
        OptionalServiceProjectGroupDTO optionalGroup = mock(OptionalServiceProjectGroupDTO.class);
        ServiceProjectDTO projectDTO1 = mock(ServiceProjectDTO.class);
        ServiceProjectDTO projectDTO2 = mock(ServiceProjectDTO.class);
        when(dealGroupDTO.getServiceProject()).thenReturn(serviceProject);
        when(serviceProject.getMustGroups()).thenReturn(Lists.newArrayList(mustGroup));
        when(serviceProject.getOptionGroups()).thenReturn(Lists.newArrayList(optionalGroup));
        when(mustGroup.getGroups()).thenReturn(Lists.newArrayList(projectDTO1));
        when(optionalGroup.getGroups()).thenReturn(Lists.newArrayList(projectDTO2));
        when(projectDTO1.getCategoryId()).thenReturn(123L);
        when(projectDTO2.getCategoryId()).thenReturn(123L);
        int skuId = 123;
        // act
        List<ServiceProjectDTO> result = invokeGetServiceProjectSku(dealGroupDTO, skuId);
        // assert
        assertEquals(2, result.size());
        assertTrue(result.contains(projectDTO1));
        assertTrue(result.contains(projectDTO2));
    }

    /**
     * Test when dealGroup is null
     */
    @Test
    public void testGetDealTags_WhenDealGroupNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroup = null;
        // act
        List<Long> result = invokeGetDealTags(dealGroup);
        // assert
        assertTrue("Should return empty list when dealGroup is null", result.isEmpty());
    }

    /**
     * Test when tags list is null
     */
    @Test
    public void testGetDealTags_WhenTagsNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroup = mock(DealGroupDTO.class);
        when(dealGroup.getTags()).thenReturn(null);
        // act
        List<Long> result = invokeGetDealTags(dealGroup);
        // assert
        assertTrue("Should return empty list when tags is null", result.isEmpty());
    }

    /**
     * Test when tags list is empty
     */
    @Test
    public void testGetDealTags_WhenTagsEmpty() throws Throwable {
        // arrange
        DealGroupDTO dealGroup = mock(DealGroupDTO.class);
        when(dealGroup.getTags()).thenReturn(new ArrayList<>());
        // act
        List<Long> result = invokeGetDealTags(dealGroup);
        // assert
        assertTrue("Should return empty list when tags is empty", result.isEmpty());
    }

    /**
     * Test with valid tags containing no duplicates
     */
    @Test
    public void testGetDealTags_WithValidUniqueTagIds() throws Throwable {
        // arrange
        DealGroupDTO dealGroup = mock(DealGroupDTO.class);
        List<DealGroupTagDTO> tags = Arrays.asList(createDealGroupTagDTO(1L), createDealGroupTagDTO(2L), createDealGroupTagDTO(3L));
        when(dealGroup.getTags()).thenReturn(tags);
        // act
        List<Long> result = invokeGetDealTags(dealGroup);
        // assert
        assertEquals("Should return list with 3 tag IDs", 3, result.size());
        assertEquals("Should contain tag ID 1", Long.valueOf(1L), result.get(0));
        assertEquals("Should contain tag ID 2", Long.valueOf(2L), result.get(1));
        assertEquals("Should contain tag ID 3", Long.valueOf(3L), result.get(2));
    }

    /**
     * Test with valid tags containing duplicates
     */
    @Test
    public void testGetDealTags_WithDuplicateTagIds() throws Throwable {
        // arrange
        DealGroupDTO dealGroup = mock(DealGroupDTO.class);
        List<DealGroupTagDTO> tags = Arrays.asList(createDealGroupTagDTO(1L), createDealGroupTagDTO(1L), createDealGroupTagDTO(2L));
        when(dealGroup.getTags()).thenReturn(tags);
        // act
        List<Long> result = invokeGetDealTags(dealGroup);
        // assert
        assertEquals("Should return list with 2 unique tag IDs", 2, result.size());
        assertEquals("Should contain tag ID 1", Long.valueOf(1L), result.get(0));
        assertEquals("Should contain tag ID 2", Long.valueOf(2L), result.get(1));
    }

    /**
     * Test case: Successfully get attribute value when service project and attribute exist
     */
    @Test
    public void testGetAttrValFromServiceProject_Success() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        ServiceProjectDTO serviceProject = new ServiceProjectDTO();
        serviceProject.setCategoryId(123L);
        ServiceProjectAttrDTO attr = new ServiceProjectAttrDTO();
        attr.setAttrName("test_attr");
        attr.setAttrValue("test_value");
        serviceProject.setAttrs(Arrays.asList(attr));
        DealGroupServiceProjectDTO serviceProjectDTO = new DealGroupServiceProjectDTO();
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        mustGroup.setGroups(Arrays.asList(serviceProject));
        serviceProjectDTO.setMustGroups(Arrays.asList(mustGroup));
        dealGroupDTO.setServiceProject(serviceProjectDTO);
        // act
        String result = invokePrivateMethod(beautyHighlightsProcessor, "getAttrValFromServiceProject", dealGroupDTO, 123, "test_attr");
        // assert
        assertEquals("test_value", result);
    }

    /**
     * Test case: Return null when service project not found
     */
    @Test
    public void testGetAttrValFromServiceProject_NoMatchingServiceProject() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        ServiceProjectDTO serviceProject = new ServiceProjectDTO();
        serviceProject.setCategoryId(123L);
        DealGroupServiceProjectDTO serviceProjectDTO = new DealGroupServiceProjectDTO();
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        mustGroup.setGroups(Arrays.asList(serviceProject));
        serviceProjectDTO.setMustGroups(Arrays.asList(mustGroup));
        dealGroupDTO.setServiceProject(serviceProjectDTO);
        // act
        String result = invokePrivateMethod(beautyHighlightsProcessor, "getAttrValFromServiceProject", dealGroupDTO, 456, "test_attr");
        // assert
        assertNull(result);
    }

    /**
     * Test case: Return null when attribute not found in service project
     */
    @Test
    public void testGetAttrValFromServiceProject_NoMatchingAttribute() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        ServiceProjectDTO serviceProject = new ServiceProjectDTO();
        serviceProject.setCategoryId(123L);
        ServiceProjectAttrDTO attr = new ServiceProjectAttrDTO();
        attr.setAttrName("other_attr");
        attr.setAttrValue("test_value");
        serviceProject.setAttrs(Arrays.asList(attr));
        DealGroupServiceProjectDTO serviceProjectDTO = new DealGroupServiceProjectDTO();
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        mustGroup.setGroups(Arrays.asList(serviceProject));
        serviceProjectDTO.setMustGroups(Arrays.asList(mustGroup));
        dealGroupDTO.setServiceProject(serviceProjectDTO);
        // act
        String result = invokePrivateMethod(beautyHighlightsProcessor, "getAttrValFromServiceProject", dealGroupDTO, 123, "test_attr");
        // assert
        assertNull(result);
    }

    /**
     * Test case: Return null when service project is null
     */
    @Test
    public void testGetAttrValFromServiceProject_NullServiceProject() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setServiceProject(null);
        // act
        String result = invokePrivateMethod(beautyHighlightsProcessor, "getAttrValFromServiceProject", dealGroupDTO, 123, "test_attr");
        // assert
        assertNull(result);
    }

    /**
     * Test case: Return null when attributes list is null
     */
    @Test
    public void testGetAttrValFromServiceProject_NullAttributes() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        ServiceProjectDTO serviceProject = new ServiceProjectDTO();
        serviceProject.setCategoryId(123L);
        serviceProject.setAttrs(null);
        DealGroupServiceProjectDTO serviceProjectDTO = new DealGroupServiceProjectDTO();
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        mustGroup.setGroups(Arrays.asList(serviceProject));
        serviceProjectDTO.setMustGroups(Arrays.asList(mustGroup));
        dealGroupDTO.setServiceProject(serviceProjectDTO);
        // act
        String result = invokePrivateMethod(beautyHighlightsProcessor, "getAttrValFromServiceProject", dealGroupDTO, 123, "test_attr");
        // assert
        assertNull(result);
    }

    /**
     * Test case: Return null when optional groups exist but no matching service project
     */
    @Test
    public void testGetAttrValFromServiceProject_WithOptionalGroups() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        ServiceProjectDTO serviceProject = new ServiceProjectDTO();
        serviceProject.setCategoryId(123L);
        DealGroupServiceProjectDTO serviceProjectDTO = new DealGroupServiceProjectDTO();
        OptionalServiceProjectGroupDTO optionalGroup = new OptionalServiceProjectGroupDTO();
        optionalGroup.setGroups(Arrays.asList(serviceProject));
        serviceProjectDTO.setOptionGroups(Arrays.asList(optionalGroup));
        dealGroupDTO.setServiceProject(serviceProjectDTO);
        // act
        String result = invokePrivateMethod(beautyHighlightsProcessor, "getAttrValFromServiceProject", dealGroupDTO, 456, "test_attr");
        // assert
        assertNull(result);
    }

    /**
     * Test case: Return null when both must and optional groups are empty
     */
    @Test
    public void testGetAttrValFromServiceProject_EmptyGroups() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupServiceProjectDTO serviceProjectDTO = new DealGroupServiceProjectDTO();
        serviceProjectDTO.setMustGroups(new ArrayList<>());
        serviceProjectDTO.setOptionGroups(new ArrayList<>());
        dealGroupDTO.setServiceProject(serviceProjectDTO);
        // act
        String result = invokePrivateMethod(beautyHighlightsProcessor, "getAttrValFromServiceProject", dealGroupDTO, 123, "test_attr");
        // assert
        assertNull(result);
    }

    /**
     * Test case: DealGroupDTO with null ServiceProject
     * Expected: No highlights module should be set
     */
    @Test
    public void testBuild4Nursing_NullServiceProject() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setServiceProject(null);
        ctx.setDealGroupDTO(dealGroupDTO);
        // Use reflection to invoke the private method
        Method method = BeautyHighlightsProcessor.class.getDeclaredMethod("build4Nursing", DealCtx.class);
        method.setAccessible(true);
        method.invoke(beautyHighlightsProcessor, ctx);
        // assert
        assertNull(ctx.getHighlightsModule());
    }

    @Test
    public void testBuildBeautyHighlights_Category501_HotDyeingServiceType() throws Throwable {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(501L);
        categoryDTO.setServiceType("烫染");
        dealGroupDTO.setCategory(categoryDTO);
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.setDpId(12345);
        ctx.setMtId(67890);
        beautyHighlightsProcessor.buildBeautyHighlights(ctx);
        assertNull(ctx.getHighlightsModule());
    }

    @Test
    public void testBuildBeautyHighlights_Category501_NursingServiceType() throws Throwable {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(501L);
        categoryDTO.setServiceType("护理");
        dealGroupDTO.setCategory(categoryDTO);
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.setDpId(12345);
        ctx.setMtId(67890);
        beautyHighlightsProcessor.buildBeautyHighlights(ctx);
        assertNull(ctx.getHighlightsModule());
    }

    @Test
    public void testBuildBeautyHighlights_Category502_EyelashServiceType() throws Throwable {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(502L);
        categoryDTO.setServiceType("美睫");
        dealGroupDTO.setCategory(categoryDTO);
        // Use subclass with tagId set
        DealGroupTagDTO tag = new DealGroupTagDTOWithId(100005310L);
        List<DealGroupTagDTO> tags = new ArrayList<>();
        tags.add(tag);
        dealGroupDTO.setTags(tags);
        // Add attrs
        AttrDTO attr = new AttrDTO();
        attr.setName("eyelash_suit_part");
        attr.setValue(Collections.singletonList("测试值"));
        List<AttrDTO> attrs = new ArrayList<>();
        attrs.add(attr);
        dealGroupDTO.setAttrs(attrs);
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.setDpId(12345);
        ctx.setMtId(67890);
        beautyHighlightsProcessor.buildBeautyHighlights(ctx);
        assertNotNull(ctx.getHighlightsModule());
        assertEquals("simple", ctx.getHighlightsModule().getStyle());
        assertEquals("测试值", ctx.getHighlightsModule().getContent());
    }

    @Test
    public void testBuildBeautyHighlights_Category502_NewWearableNailDeal() throws Throwable {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(502L);
        categoryDTO.setServiceType("穿戴甲");
        dealGroupDTO.setCategory(categoryDTO);
        // Add tag_unifyProduct attr to match DealUtils.isNewWearableNailDeal
        AttrDTO attr = new AttrDTO();
        attr.setName("tag_unifyProduct");
        attr.setValue(Collections.singletonList("1"));
        List<AttrDTO> attrs = new ArrayList<>();
        attrs.add(attr);
        // Add other attrs for buildWearableNail
        AttrDTO attr1 = new AttrDTO();
        attr1.setName("isFreeWearingAtStore");
        attr1.setValue(Collections.singletonList("true"));
        attrs.add(attr1);
        AttrDTO attr2 = new AttrDTO();
        attr2.setName("wearingNailsType");
        attr2.setValue(Collections.singletonList("测试类型"));
        attrs.add(attr2);
        AttrDTO attr3 = new AttrDTO();
        attr3.setName("nail_additional_item");
        attr3.setValue(Collections.singletonList("测试附赠项目"));
        attrs.add(attr3);
        dealGroupDTO.setAttrs(attrs);
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.setDpId(12345);
        ctx.setMtId(67890);
        beautyHighlightsProcessor.buildBeautyHighlights(ctx);
        assertNotNull(ctx.getHighlightsModule());
        assertEquals("struct", ctx.getHighlightsModule().getStyle());
        assertEquals(3, ctx.getHighlightsModule().getAttrs().size());
        List<CommonAttrVO> attrVOs = ctx.getHighlightsModule().getAttrs();
        assertEquals("增值服务", attrVOs.get(0).getName());
        assertEquals("到店免费佩戴", attrVOs.get(0).getValue());
        assertEquals("穿戴甲类型", attrVOs.get(1).getName());
        assertEquals("测试类型", attrVOs.get(1).getValue());
        assertEquals("附赠项目", attrVOs.get(2).getName());
        assertEquals("测试附赠项目", attrVOs.get(2).getValue());
    }

    @Test
    public void testBuildBeautyHighlights_ExceptionHandling() throws Throwable {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setDpId(12345);
        ctx.setMtId(67890);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(501L);
        categoryDTO.setServiceType("烫染");
        when(dealGroupDTO.getCategory()).thenReturn(categoryDTO);
        when(dealGroupDTO.getAttrs()).thenThrow(new RuntimeException("Test exception"));
        ctx.setDealGroupDTO(dealGroupDTO);
        Field loggerField = BeautyHighlightsProcessor.class.getSuperclass().getDeclaredField("logger");
        loggerField.setAccessible(true);
        loggerField.set(beautyHighlightsProcessor, logger);
        beautyHighlightsProcessor.buildBeautyHighlights(ctx);
        verify(logger, times(1)).error(eq("buildBeautyHighlights-error,dpDealId:{},mtDealId:{}"), eq(12345), eq(67890),
                any(RuntimeException.class));
    }

    @Test
    public void testBuildBeautyHighlights_ExceptionHandling_NullPointerException() throws Throwable {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setDpId(11111);
        ctx.setMtId(22222);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setCategory(null);
        ctx.setDealGroupDTO(dealGroupDTO);
        Field loggerField = BeautyHighlightsProcessor.class.getSuperclass().getDeclaredField("logger");
        loggerField.setAccessible(true);
        loggerField.set(beautyHighlightsProcessor, logger);
        beautyHighlightsProcessor.buildBeautyHighlights(ctx);
        verify(logger, times(1)).error(eq("buildBeautyHighlights-error,dpDealId:{},mtDealId:{}"), eq(11111), eq(22222),
                any(NullPointerException.class));
    }

    @Test
    public void testBuildBeautyHighlights_NullDealGroup() throws Throwable {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setDpId(12345);
        ctx.setMtId(67890);
        ctx.setDealGroupDTO(null);
        beautyHighlightsProcessor.buildBeautyHighlights(ctx);
        assertNull(ctx.getHighlightsModule());
    }

    public static class DealGroupTagDTOWithId extends DealGroupTagDTO {

        public DealGroupTagDTOWithId(long tagId) {
            try {
                Field f = DealGroupTagDTO.class.getDeclaredField("tagId");
                f.setAccessible(true);
                f.set(this, tagId);
            } catch (Exception e) {
                // fallback: try "id"
                try {
                    Field f = DealGroupTagDTO.class.getDeclaredField("id");
                    f.setAccessible(true);
                    f.set(this, tagId);
                } catch (Exception ignore) {
                }
            }
        }

        // This method is only for test, not used by production code
        public Long getTagIdForTest() {
            try {
                Field f = DealGroupTagDTO.class.getDeclaredField("tagId");
                f.setAccessible(true);
                return (Long)f.get(this);
            } catch (Exception e) {
                try {
                    Field f = DealGroupTagDTO.class.getDeclaredField("id");
                    f.setAccessible(true);
                    return (Long)f.get(this);
                } catch (Exception ignore) {
                }
            }
            return null;
        }
    }
}
