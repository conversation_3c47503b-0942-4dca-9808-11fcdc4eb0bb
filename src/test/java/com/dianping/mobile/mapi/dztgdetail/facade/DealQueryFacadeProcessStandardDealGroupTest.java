package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

public class DealQueryFacadeProcessStandardDealGroupTest {

    private DealQueryFacade dealQueryFacade;

    private DealCtx dealCtx;

    private DealGroupPBO dealGroupPBO;

    @Before
    public void setUp() {
        dealQueryFacade = new DealQueryFacade();
        dealCtx = mock(DealCtx.class);
        dealGroupPBO = mock(DealGroupPBO.class);
        when(dealCtx.getResult()).thenReturn(dealGroupPBO);
    }

    /**
     * Test case for standard deal group with category ID 502 and service type "美甲".
     * The sale description should be prefixed with "本地".
     */
    @Test
    public void testProcessStandardDealGroup_Category502_ServiceTypeNail() throws Throwable {
        // arrange
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attr1 = new AttributeDTO();
        attr1.setName("standardDealGroup");
        attr1.setValue(Arrays.asList("1"));
        attrs.add(attr1);
        AttributeDTO attr2 = new AttributeDTO();
        attr2.setName("service_type");
        attr2.setValue(Arrays.asList("美甲"));
        attrs.add(attr2);
        when(dealCtx.getAttrs()).thenReturn(attrs);
        when(dealCtx.getCategoryId()).thenReturn(502);
        when(dealGroupPBO.getSaleDesc()).thenReturn("美甲服务");
        // act
        dealQueryFacade.processStandardDealGroup(dealCtx);
        // assert
        verify(dealGroupPBO).setStandardDealGroup(true);
        verify(dealGroupPBO).setSaleDesc("本地美甲服务");
    }

    /**
     * Test case for standard deal group with category ID 503 and service type "SPA按摩".
     * The title should be prefixed with "【美团标准服务】".
     */
    @Test
    public void testProcessStandardDealGroup_Category503_ServiceTypeSPA() throws Throwable {
        // arrange
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attr1 = new AttributeDTO();
        attr1.setName("standardDealGroup");
        attr1.setValue(Arrays.asList("1"));
        attrs.add(attr1);
        AttributeDTO attr2 = new AttributeDTO();
        attr2.setName("service_type");
        attr2.setValue(Arrays.asList("SPA按摩"));
        attrs.add(attr2);
        when(dealCtx.getAttrs()).thenReturn(attrs);
        when(dealCtx.getCategoryId()).thenReturn(503);
        when(dealGroupPBO.getTitle()).thenReturn("SPA按摩服务");
        // act
        dealQueryFacade.processStandardDealGroup(dealCtx);
        // assert
        verify(dealGroupPBO).setStandardDealGroup(true);
        verify(dealGroupPBO).setTitle("【美团标准服务】SPA按摩服务");
    }

    /**
     * Test case for standard deal group with category ID 502 but service type is not "美甲".
     * No modification should be made to the sale description.
     */
    @Test
    public void testProcessStandardDealGroup_Category502_ServiceTypeNotNail() throws Throwable {
        // arrange
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attr1 = new AttributeDTO();
        attr1.setName("standardDealGroup");
        attr1.setValue(Arrays.asList("1"));
        attrs.add(attr1);
        AttributeDTO attr2 = new AttributeDTO();
        attr2.setName("service_type");
        attr2.setValue(Arrays.asList("其他服务"));
        attrs.add(attr2);
        when(dealCtx.getAttrs()).thenReturn(attrs);
        when(dealCtx.getCategoryId()).thenReturn(502);
        when(dealGroupPBO.getSaleDesc()).thenReturn("其他服务");
        // act
        dealQueryFacade.processStandardDealGroup(dealCtx);
        // assert
        verify(dealGroupPBO).setStandardDealGroup(true);
        verify(dealGroupPBO, never()).setSaleDesc(anyString());
    }

    /**
     * Test case for standard deal group with category ID 503 but service type is not "SPA按摩".
     * No modification should be made to the title.
     */
    @Test
    public void testProcessStandardDealGroup_Category503_ServiceTypeNotSPA() throws Throwable {
        // arrange
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attr1 = new AttributeDTO();
        attr1.setName("standardDealGroup");
        attr1.setValue(Arrays.asList("1"));
        attrs.add(attr1);
        AttributeDTO attr2 = new AttributeDTO();
        attr2.setName("service_type");
        attr2.setValue(Arrays.asList("其他服务"));
        attrs.add(attr2);
        when(dealCtx.getAttrs()).thenReturn(attrs);
        when(dealCtx.getCategoryId()).thenReturn(503);
        when(dealGroupPBO.getTitle()).thenReturn("其他服务");
        // act
        dealQueryFacade.processStandardDealGroup(dealCtx);
        // assert
        verify(dealGroupPBO).setStandardDealGroup(true);
        verify(dealGroupPBO, never()).setTitle(anyString());
    }

    /**
     * Test case for standard deal group with category ID neither 502 nor 503.
     * No modification should be made to either the sale description or the title.
     */
    @Test
    public void testProcessStandardDealGroup_CategoryNeither502Nor503() throws Throwable {
        // arrange
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attr1 = new AttributeDTO();
        attr1.setName("standardDealGroup");
        attr1.setValue(Arrays.asList("1"));
        attrs.add(attr1);
        AttributeDTO attr2 = new AttributeDTO();
        attr2.setName("service_type");
        attr2.setValue(Arrays.asList("其他服务"));
        attrs.add(attr2);
        when(dealCtx.getAttrs()).thenReturn(attrs);
        when(dealCtx.getCategoryId()).thenReturn(504);
        when(dealGroupPBO.getSaleDesc()).thenReturn("其他服务");
        when(dealGroupPBO.getTitle()).thenReturn("其他服务");
        // act
        dealQueryFacade.processStandardDealGroup(dealCtx);
        // assert
        verify(dealGroupPBO).setStandardDealGroup(true);
        verify(dealGroupPBO, never()).setSaleDesc(anyString());
        verify(dealGroupPBO, never()).setTitle(anyString());
    }

    /**
     * Test case for non-standard deal group.
     * No modifications should be made, and the standardDealGroup flag should remain false.
     */
    @Test
    public void testProcessStandardDealGroup_NonStandardDealGroup() throws Throwable {
        // arrange
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attr1 = new AttributeDTO();
        attr1.setName("standardDealGroup");
        attr1.setValue(Arrays.asList("0"));
        attrs.add(attr1);
        when(dealCtx.getAttrs()).thenReturn(attrs);
        // act
        dealQueryFacade.processStandardDealGroup(dealCtx);
        // assert
        verify(dealGroupPBO, never()).setStandardDealGroup(anyBoolean());
        verify(dealGroupPBO, never()).setSaleDesc(anyString());
        verify(dealGroupPBO, never()).setTitle(anyString());
    }

    /**
     * Test case for null attributes.
     * The deal group should not be considered standard.
     */
    @Test
    public void testProcessStandardDealGroup_NullAttributes() throws Throwable {
        // arrange
        when(dealCtx.getAttrs()).thenReturn(null);
        // act
        dealQueryFacade.processStandardDealGroup(dealCtx);
        // assert
        verify(dealGroupPBO, never()).setStandardDealGroup(anyBoolean());
        verify(dealGroupPBO, never()).setSaleDesc(anyString());
        verify(dealGroupPBO, never()).setTitle(anyString());
    }

    /**
     * Test case for empty attributes.
     * The deal group should not be considered standard.
     */
    @Test
    public void testProcessStandardDealGroup_EmptyAttributes() throws Throwable {
        // arrange
        when(dealCtx.getAttrs()).thenReturn(new ArrayList<>());
        // act
        dealQueryFacade.processStandardDealGroup(dealCtx);
        // assert
        verify(dealGroupPBO, never()).setStandardDealGroup(anyBoolean());
        verify(dealGroupPBO, never()).setSaleDesc(anyString());
        verify(dealGroupPBO, never()).setTitle(anyString());
    }

    /**
     * Test case for null sale description or title.
     * No modification should be made to the sale description or title.
     */
    @Test
    public void testProcessStandardDealGroup_NullSaleDescOrTitle() throws Throwable {
        // arrange
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attr1 = new AttributeDTO();
        attr1.setName("standardDealGroup");
        attr1.setValue(Arrays.asList("1"));
        attrs.add(attr1);
        AttributeDTO attr2 = new AttributeDTO();
        attr2.setName("service_type");
        attr2.setValue(Arrays.asList("美甲"));
        attrs.add(attr2);
        when(dealCtx.getAttrs()).thenReturn(attrs);
        when(dealCtx.getCategoryId()).thenReturn(502);
        when(dealGroupPBO.getSaleDesc()).thenReturn(null);
        when(dealGroupPBO.getTitle()).thenReturn(null);
        // act
        dealQueryFacade.processStandardDealGroup(dealCtx);
        // assert
        verify(dealGroupPBO).setStandardDealGroup(true);
        verify(dealGroupPBO, never()).setSaleDesc(anyString());
        verify(dealGroupPBO, never()).setTitle(anyString());
    }
}
