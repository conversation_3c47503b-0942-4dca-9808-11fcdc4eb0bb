package com.dianping.mobile.mapi.dztgdetail.biz.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DouHuService_GetRepurchaseShelfExpResultTest {

    @InjectMocks
    private DouHuService douHuService;

    @Mock
    private DouHuBiz douHuBiz;

    @Mock
    private EnvCtx envCtx;

    private String unionId = "testUnionId";

    private String pageSource = "testPageSource";

    private boolean isMt = true;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Test getRepurchaseShelfExpResult method when EnvCtx is considered as non-MT client.
     */
    @Test
    public void testGetRepurchaseShelfExpResultForNonMtClient() throws Throwable {
        when(envCtx.isMt()).thenReturn(false);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        when(douHuBiz.getAbExpResultByUuidAndDpid(any(), eq("DpRepurchaseShelfExp"))).thenReturn(moduleAbConfig);
        when(douHuBiz.getExpResult(moduleAbConfig)).thenReturn("DP Result");
        String result = douHuService.getRepurchaseShelfExpResult(envCtx);
        assertEquals("DP Result", result);
    }

    @Test
    public void testGetMiniVisionStyleAbTestSwitch_PageSourceNotFromMiniVision() throws Throwable {
        pageSource = "notFromMiniVision";
        Method method = DouHuService.class.getDeclaredMethod("getMiniVisionStyleAbTestSwitch", String.class, String.class, boolean.class);
        method.setAccessible(true);
        ModuleAbConfig result = (ModuleAbConfig) method.invoke(douHuService, unionId, pageSource, isMt);
        assertNull(result);
    }

    @Test
    public void testGetMiniVisionStyleAbTestSwitch_IsMtFalse() throws Throwable {
        isMt = false;
        Method method = DouHuService.class.getDeclaredMethod("getMiniVisionStyleAbTestSwitch", String.class, String.class, boolean.class);
        method.setAccessible(true);
        ModuleAbConfig result = (ModuleAbConfig) method.invoke(douHuService, unionId, pageSource, isMt);
        assertNull(result);
    }

    @Test
    public void testGetMiniVisionStyleAbTestSwitch_ExpIdIsNull() throws Throwable {
        Method method = DouHuService.class.getDeclaredMethod("getMiniVisionStyleAbTestSwitch", String.class, String.class, boolean.class);
        method.setAccessible(true);
        ModuleAbConfig result = (ModuleAbConfig) method.invoke(douHuService, unionId, pageSource, isMt);
        assertNull(result);
    }

    @Test
    public void testGetMiniVisionStyleAbTestSwitch_GetAbByUnionIdAndExpIdReturnNull() throws Throwable {
        Method method = DouHuService.class.getDeclaredMethod("getMiniVisionStyleAbTestSwitch", String.class, String.class, boolean.class);
        method.setAccessible(true);
        ModuleAbConfig result = (ModuleAbConfig) method.invoke(douHuService, unionId, pageSource, isMt);
        assertNull(result);
    }

    /**
     * 测试 getMagicCouponEnhancementAbTestResult 方法
     * 场景：当客户端类型为点评APP时（非美团客户端），应返回 null，且不调用 douHuBiz
     */
    @Test
    public void testGetMagicCouponEnhancementAbTestResultWhenIsMtIsFalse() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        // act
        ModuleAbConfig result = douHuService.getMagicCouponEnhancementAbTestResult(envCtx);
        // assert
        assertNull(result);
        verify(douHuBiz, never()).getAbExpResult(eq(envCtx), eq("MtMagicCouponEnhancementModule"));
    }

    /**
     * 测试 getMagicCouponEnhancementAbTestResult 方法
     * 场景：当客户端类型为美团APP时，应调用 douHuBiz 并返回其结果
     */
    @Test
    public void testGetMagicCouponEnhancementAbTestResultWhenIsMtIsTrue() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        ModuleAbConfig expected = new ModuleAbConfig();
        when(douHuBiz.getAbExpResult(eq(envCtx), eq("MtMagicalCouponStyleEnhancementV2"))).thenReturn(expected);
        // act
        ModuleAbConfig result = douHuService.getMagicCouponEnhancementAbTestResult(envCtx);
        // assert
        assertEquals(expected, result);
        verify(douHuBiz).getAbExpResult(eq(envCtx), eq("MtMagicalCouponStyleEnhancementV2"));
    }

    /**
     * 测试 getMagicCouponEnhancementAbTestResult 方法
     * 场景：当客户端类型为美团APP，但 douHuBiz 返回 null 时
     */
    @Test
    public void testGetMagicCouponEnhancementAbTestResultWhenDouHuBizReturnsNull() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        when(douHuBiz.getAbExpResult(eq(envCtx), eq("MtMagicalCouponStyleEnhancementV2"))).thenReturn(null);
        // act
        ModuleAbConfig result = douHuService.getMagicCouponEnhancementAbTestResult(envCtx);
        // assert
        assertNull(result);
        verify(douHuBiz).getAbExpResult(eq(envCtx), eq("MtMagicalCouponStyleEnhancementV2"));
    }
}
