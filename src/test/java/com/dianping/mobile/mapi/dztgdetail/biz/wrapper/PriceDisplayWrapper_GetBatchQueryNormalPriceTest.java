package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.mobile.mapi.dztgdetail.tab.bo.BaseData;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.BaseLoadParam;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PriceResponse;
import com.sankuai.dealuser.price.display.api.model.ProductIdentity;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PriceDisplayWrapper_GetBatchQueryNormalPriceTest {

    @InjectMocks
    private PriceDisplayWrapper priceDisplayWrapper;

    @Mock
    private Future future;

    @Mock
    private BaseLoadParam baseLoadParam;

    @Mock
    private EnvCtx envCtx;

    @Mock
    private BaseData baseData;

    @Mock
    private PriceResponse<Map<Long, List<PriceDisplayDTO>>> priceResponse;

    @Mock
    private PriceDisplayDTO priceDisplayDTO;

    @Test
    public void testGetBatchQueryNormalPriceFutureIsNull() throws Throwable {
        when(baseLoadParam.getEnvCtx()).thenReturn(envCtx);
        when(baseLoadParam.getBaseData()).thenReturn(baseData);
        Map<Integer, PriceDisplayDTO> result = priceDisplayWrapper.getBatchQueryNormalPrice(baseLoadParam, null);
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testGetBatchQueryNormalPriceFutureIsNotNullButGetThrowsException() throws Throwable {
        when(baseLoadParam.getEnvCtx()).thenReturn(envCtx);
        when(baseLoadParam.getBaseData()).thenReturn(baseData);
        when(future.get(anyLong(), any())).thenThrow(new InterruptedException());
        Map<Integer, PriceDisplayDTO> result = priceDisplayWrapper.getBatchQueryNormalPrice(baseLoadParam, future);
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testGetBatchQueryNormalPricePriceResponseIsNull() throws Throwable {
        when(baseLoadParam.getEnvCtx()).thenReturn(envCtx);
        when(baseLoadParam.getBaseData()).thenReturn(baseData);
        when(future.get(anyLong(), any())).thenReturn(null);
        Map<Integer, PriceDisplayDTO> result = priceDisplayWrapper.getBatchQueryNormalPrice(baseLoadParam, future);
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testGetBatchQueryNormalPricePriceResponseCodeIsNot200() throws Throwable {
        when(baseLoadParam.getEnvCtx()).thenReturn(envCtx);
        when(baseLoadParam.getBaseData()).thenReturn(baseData);
        when(future.get(anyLong(), any())).thenReturn(priceResponse);
        when(priceResponse.getCode()).thenReturn(400);
        Map<Integer, PriceDisplayDTO> result = priceDisplayWrapper.getBatchQueryNormalPrice(baseLoadParam, future);
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testGetBatchQueryNormalPricePriceResponseDataIsNull() throws Throwable {
        when(baseLoadParam.getEnvCtx()).thenReturn(envCtx);
        when(baseLoadParam.getBaseData()).thenReturn(baseData);
        when(future.get(anyLong(), any())).thenReturn(priceResponse);
        when(priceResponse.getCode()).thenReturn(200);
        when(priceResponse.getData()).thenReturn(null);
        Map<Integer, PriceDisplayDTO> result = priceDisplayWrapper.getBatchQueryNormalPrice(baseLoadParam, future);
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testGetBatchQueryNormalPricePriceResponseDataIsNotNullButShopIdNotExists() throws Throwable {
        when(baseLoadParam.getEnvCtx()).thenReturn(envCtx);
        when(baseLoadParam.getBaseData()).thenReturn(baseData);
        when(future.get(anyLong(), any())).thenReturn(priceResponse);
        when(priceResponse.getCode()).thenReturn(200);
        when(priceResponse.getData()).thenReturn(new HashMap<>());
        Map<Integer, PriceDisplayDTO> result = priceDisplayWrapper.getBatchQueryNormalPrice(baseLoadParam, future);
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testGetBatchQueryNormalPricePriceResponseDataIsNotNullAndShopIdExistsButEnvCtxIsMt() throws Throwable {
        when(baseLoadParam.getEnvCtx()).thenReturn(envCtx);
        when(baseLoadParam.getBaseData()).thenReturn(baseData);
        when(future.get(anyLong(), any())).thenReturn(priceResponse);
        when(priceResponse.getCode()).thenReturn(200);
        Map<Long, List<PriceDisplayDTO>> data = new HashMap<>();
        data.put(1L, Collections.singletonList(priceDisplayDTO));
        when(priceResponse.getData()).thenReturn(data);
        Map<Integer, PriceDisplayDTO> result = priceDisplayWrapper.getBatchQueryNormalPrice(baseLoadParam, future);
        assertEquals(Collections.emptyMap(), result);
    }
}
