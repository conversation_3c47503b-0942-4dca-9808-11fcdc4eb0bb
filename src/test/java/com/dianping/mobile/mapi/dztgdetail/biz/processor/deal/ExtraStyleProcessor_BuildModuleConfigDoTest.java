package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.deal.style.protocol.ExtraStyleResponse;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealStyleWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DealStyle;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.MTTemplateKey;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule;
import com.dianping.mobile.mapi.dztgdetail.util.DealVersionUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({DealVersionUtils.class, LionConfigUtils.class})
@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*"})
public class ExtraStyleProcessor_BuildModuleConfigDoTest {

    private ExtraStyleProcessor extraStyleProcessor;

    @Before
    public void setUp() {
        extraStyleProcessor = new ExtraStyleProcessor();
    }

    /**
     * 测试 buildModuleConfigDo 方法，key 和 value 都是正常的字符串
     */
    @Test
    public void testBuildModuleConfigDoNormal() {
        // arrange
        String key = "key";
        String value = "value";
        // act
        ModuleConfigDo result = extraStyleProcessor.buildModuleConfigDo(key, value);
        // assert
        assertNotNull(result);
        assertEquals(key, result.getKey());
        assertEquals(value, result.getValue());
    }

    /**
     * 测试 buildModuleConfigDo 方法，key 或 value 是空字符串
     */
    @Test
    public void testBuildModuleConfigDoEmpty() {
        // arrange
        String key = "";
        String value = "value";
        // act
        ModuleConfigDo result = extraStyleProcessor.buildModuleConfigDo(key, value);
        // assert
        assertNotNull(result);
        assertEquals(key, result.getKey());
        assertEquals(value, result.getValue());
    }

    /**
     * 测试 buildModuleConfigDo 方法，key 或 value 是 null
     */
    @Test
    public void testBuildModuleConfigDoNull() {
        // arrange
        String key = null;
        String value = "value";
        // act
        ModuleConfigDo result = extraStyleProcessor.buildModuleConfigDo(key, value);
        // assert
        assertNotNull(result);
        assertEquals(key, result.getKey());
        assertEquals(value, result.getValue());
    }

    private void injectDependencies(ExtraStyleProcessor processor, DealStyleWrapper dealStyleWrapper, DouHuBiz douHuBiz,
            DouHuService douHuService) throws Exception {
        // Use reflection to inject dependencies
        injectField(processor, "dealStyleWrapper", dealStyleWrapper);
        injectField(processor, "douHuBiz", douHuBiz);
        injectField(processor, "douHuService", douHuService);
    }

    private void injectField(Object target, String fieldName, Object value) throws Exception {
        // Try to find the field in the class hierarchy
        Class<?> clazz = target.getClass();
        Field field = null;
        while (clazz != null && field == null) {
            try {
                field = clazz.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass();
            }
        }
        if (field == null) {
            throw new NoSuchFieldException("Field " + fieldName + " not found in class hierarchy");
        }
        field.setAccessible(true);
        field.set(target, value);
    }

    @Test
    public void testProcessWithNullMtTemplateKey() throws Throwable {
        // arrange
        ExtraStyleProcessor processor = new ExtraStyleProcessor();
        // Inject mocks using reflection
        DealStyleWrapper dealStyleWrapper = mock(DealStyleWrapper.class);
        DouHuBiz douHuBiz = mock(DouHuBiz.class);
        DouHuService douHuService = mock(DouHuService.class);
        injectDependencies(processor, dealStyleWrapper, douHuBiz, douHuService);
        // Create spy to stub methods
        ExtraStyleProcessor spyProcessor = spy(processor);
        // Create context
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setFutureCtx(new FutureCtx());
        // Spy context to control isMt
        DealCtx spyCtx = spy(ctx);
        doReturn(true).when(spyCtx).isMt();
        // Mock getExtraStyleResponse
        ExtraStyleResponse extraStyleResp = mock(ExtraStyleResponse.class);
        when(dealStyleWrapper.getExtraStyleResponse(any())).thenReturn(extraStyleResp);
        // Mock generateMtTemplateKey to return null
        doReturn(null).when(spyProcessor).generateMtTemplateKey(any(), any());
        // act
        spyProcessor.process(spyCtx);
        // assert
        verify(spyProcessor).generateMtTemplateKey(any(), any());
        assertNull(spyCtx.getMtTemplateKey());
    }

    @Test
    public void testProcessWithModuleConfigsAndLyyModuleConfigsNotEmpty() throws Throwable {
        // arrange
        ExtraStyleProcessor processor = new ExtraStyleProcessor();
        // Inject mocks using reflection
        DealStyleWrapper dealStyleWrapper = mock(DealStyleWrapper.class);
        DouHuBiz douHuBiz = mock(DouHuBiz.class);
        DouHuService douHuService = mock(DouHuService.class);
        injectDependencies(processor, dealStyleWrapper, douHuBiz, douHuService);
        // Create spy to stub methods
        ExtraStyleProcessor spyProcessor = spy(processor);
        // Create context
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setFutureCtx(new FutureCtx());
        // Set lyyuserid to trigger getLyyModuleConfigs
        ctx.setLyyuserid("lyy");
        // Spy context to control isMt
        DealCtx spyCtx = spy(ctx);
        doReturn(true).when(spyCtx).isMt();
        // Mock getExtraStyleResponse
        ExtraStyleResponse extraStyleResp = mock(ExtraStyleResponse.class);
        when(dealStyleWrapper.getExtraStyleResponse(any())).thenReturn(extraStyleResp);
        // Mock generateMtTemplateKey to return MTTemplateKey with non-empty moduleConfigs
        MTTemplateKey mtTemplateKey = new MTTemplateKey();
        List<ModuleConfigDo> moduleConfigs = new ArrayList<>();
        moduleConfigs.add(new ModuleConfigDo());
        mtTemplateKey.setModuleConfigs(moduleConfigs);
        doReturn(mtTemplateKey).when(spyProcessor).generateMtTemplateKey(any(), any());
        // act
        spyProcessor.process(spyCtx);
        // assert
        assertNotNull(spyCtx.getMtTemplateKey());
        assertTrue(spyCtx.getMtTemplateKey().getModuleConfigs().size() > 1);
    }

    @Test
    public void testProcessWithEmptyModuleConfigsAndNonEmptyLyyModuleConfigs() throws Throwable {
        // arrange
        ExtraStyleProcessor processor = new ExtraStyleProcessor();
        // Inject mocks using reflection
        DealStyleWrapper dealStyleWrapper = mock(DealStyleWrapper.class);
        DouHuBiz douHuBiz = mock(DouHuBiz.class);
        DouHuService douHuService = mock(DouHuService.class);
        injectDependencies(processor, dealStyleWrapper, douHuBiz, douHuService);
        // Create spy to stub methods
        ExtraStyleProcessor spyProcessor = spy(processor);
        // Create context
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setFutureCtx(new FutureCtx());
        // Set lyyuserid to trigger getLyyModuleConfigs
        ctx.setLyyuserid("lyy");
        // Spy context to control isMt
        DealCtx spyCtx = spy(ctx);
        doReturn(true).when(spyCtx).isMt();
        // Mock getExtraStyleResponse
        ExtraStyleResponse extraStyleResp = mock(ExtraStyleResponse.class);
        when(dealStyleWrapper.getExtraStyleResponse(any())).thenReturn(extraStyleResp);
        // Mock generateMtTemplateKey to return MTTemplateKey with empty moduleConfigs
        MTTemplateKey mtTemplateKey = new MTTemplateKey();
        mtTemplateKey.setModuleConfigs(null);
        doReturn(mtTemplateKey).when(spyProcessor).generateMtTemplateKey(any(), any());
        // act
        spyProcessor.process(spyCtx);
        // assert
        assertNotNull(spyCtx.getMtTemplateKey());
        assertNotNull(spyCtx.getMtTemplateKey().getModuleConfigs());
        assertFalse(spyCtx.getMtTemplateKey().getModuleConfigs().isEmpty());
    }

    @Test
    public void testProcessForDpPath() throws Throwable {
        // arrange
        ExtraStyleProcessor processor = new ExtraStyleProcessor();
        // Inject mocks using reflection
        DealStyleWrapper dealStyleWrapper = mock(DealStyleWrapper.class);
        DouHuBiz douHuBiz = mock(DouHuBiz.class);
        DouHuService douHuService = mock(DouHuService.class);
        injectDependencies(processor, dealStyleWrapper, douHuBiz, douHuService);
        // Create spy to stub methods
        ExtraStyleProcessor spyProcessor = spy(processor);
        // Create context
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setFutureCtx(new FutureCtx());
        // Spy context to control isMt
        DealCtx spyCtx = spy(ctx);
        // Not MT, so DP branch
        doReturn(false).when(spyCtx).isMt();
        // Mock getExtraStyleResponse
        ExtraStyleResponse extraStyleResp = mock(ExtraStyleResponse.class);
        when(dealStyleWrapper.getExtraStyleResponse(any())).thenReturn(extraStyleResp);
        // Mock generateDpDealStyle
        DealStyle dealStyle = new DealStyle("key");
        doReturn(dealStyle).when(spyProcessor).generateDpDealStyle(any(), any());
        // Create a ModuleConfigsModule
        ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
        // We need to make the real process method run, but we need to intercept it
        // to set up the context before the real method completes
        // Let's use a custom implementation that first sets up the context
        doAnswer(invocation -> {
            // Call the real method first
            invocation.callRealMethod();
            // Then set the dealStyle and moduleConfigsModule
            spyCtx.setDealStyle(dealStyle);
            spyCtx.setModuleConfigsModule(moduleConfigsModule);
            return null;
        }).when(spyProcessor).process(spyCtx);
        // act
        spyProcessor.process(spyCtx);
        // assert
        assertEquals(dealStyle, spyCtx.getDealStyle());
    }

    @Test
    public void testProcessWhenIsJuShengQianBigPhotoReturnsTrueAndDealExtraTypesIsNull() throws Throwable {
        // arrange
        ExtraStyleProcessor processor = new ExtraStyleProcessor();
        // Inject mocks using reflection
        DealStyleWrapper dealStyleWrapper = mock(DealStyleWrapper.class);
        DouHuBiz douHuBiz = mock(DouHuBiz.class);
        DouHuService douHuService = mock(DouHuService.class);
        injectDependencies(processor, dealStyleWrapper, douHuBiz, douHuService);
        // Create spy to stub methods
        ExtraStyleProcessor spyProcessor = spy(processor);
        // Create context
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setFutureCtx(new FutureCtx());
        // Set dealExtraTypes to null
        ctx.setDealExtraTypes(null);
        // Spy context to control isMt
        DealCtx spyCtx = spy(ctx);
        doReturn(true).when(spyCtx).isMt();
        // Mock getExtraStyleResponse
        ExtraStyleResponse extraStyleResp = mock(ExtraStyleResponse.class);
        when(dealStyleWrapper.getExtraStyleResponse(any())).thenReturn(extraStyleResp);
        // Mock generateMtTemplateKey
        MTTemplateKey mtTemplateKey = new MTTemplateKey();
        mtTemplateKey.setModuleConfigs(new ArrayList<>());
        doReturn(mtTemplateKey).when(spyProcessor).generateMtTemplateKey(any(), any());
        // Create a ModuleConfigsModule that will trigger isJuShengQianBigPhoto to return true
        ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
        List<ModuleConfigDo> configs = new ArrayList<>();
        ModuleConfigDo config = new ModuleConfigDo();
        config.setKey("GCPlatformModules/picasso_deal_detail_head_module");
        config.setValue("xxxjushengqian");
        configs.add(config);
        moduleConfigsModule.setModuleConfigs(configs);
        // We need to make the real process method run, but we need to intercept it
        // to set up the context before the real method completes
        // Let's use a custom implementation that first sets up the context
        doAnswer(invocation -> {
            // Call the real method first
            invocation.callRealMethod();
            // Then set the moduleConfigsModule
            spyCtx.setModuleConfigsModule(moduleConfigsModule);
            // Then add the JUSHENGQIAN_DEAL to dealExtraTypes
            if (spyCtx.getDealExtraTypes() == null) {
                spyCtx.setDealExtraTypes(Lists.newArrayList());
            }
            spyCtx.getDealExtraTypes().add("JUSHENGQIAN_DEAL");
            return null;
        }).when(spyProcessor).process(spyCtx);
        // act
        spyProcessor.process(spyCtx);
        // assert
        assertNotNull(spyCtx.getDealExtraTypes());
        assertTrue(spyCtx.getDealExtraTypes().contains("JUSHENGQIAN_DEAL"));
    }
}
