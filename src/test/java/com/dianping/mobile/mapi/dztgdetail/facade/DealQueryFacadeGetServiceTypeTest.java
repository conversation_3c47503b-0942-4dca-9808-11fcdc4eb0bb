package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.publishcategory.DealGroupPublishCategoryQueryService;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.deal.shop.DealShopQueryService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DzImWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealIMReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupImVo;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;
import org.apache.commons.lang.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

/**
 * Test class for DealQueryFacade.getServiceType method
 */
@RunWith(MockitoJUnitRunner.class)
public class DealQueryFacadeGetServiceTypeTest {

    @InjectMocks
    private DealQueryFacade dealQueryFacade;

    @Mock
    private DealShopQueryService dealShopQueryService;

    private DealQueryFacade facade;

    @Mock
    private MapperWrapper mapperWrapper;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private DealGroupPublishCategoryQueryService dealGroupPublishCategoryQueryService;

    @Mock
    private DzImWrapper dzImWrapper;

    @Before
    public void setUp() {
        facade = new DealQueryFacade();
    }

    private String invokePrivateMethod(DealQueryFacade instance, String methodName, List<AttributeDTO> attributeDTOS) throws Exception {
        Method method = DealQueryFacade.class.getDeclaredMethod(methodName, List.class);
        method.setAccessible(true);
        return (String) method.invoke(instance, attributeDTOS);
    }

    // Helper method to invoke private methods using reflection
    private List<Long> invokePrivateMethod(DealQueryFacade instance, String methodName, long dpShopId, boolean isDp) throws Exception {
        Method method = DealQueryFacade.class.getDeclaredMethod(methodName, long.class, boolean.class);
        method.setAccessible(true);
        return (List<Long>) method.invoke(instance, dpShopId, isDp);
    }

    private String invokeGetStringFromJson(String json, String key) throws Exception {
        Method method = DealQueryFacade.class.getDeclaredMethod("getStringFromJson", String.class, String.class);
        method.setAccessible(true);
        try {
            return (String) method.invoke(facade, json, key);
        } catch (InvocationTargetException e) {
            Throwable cause = e.getCause();
            if (cause instanceof JSONException) {
                throw (JSONException) cause;
            }
            throw e;
        }
    }

    /**
     * Test when input attribute list is null
     */
    @Test
    public void testGetServiceType_NullList() throws Throwable {
        // arrange
        List<AttributeDTO> attributeDTOS = null;
        // act
        String result = invokePrivateMethod(dealQueryFacade, "getServiceType", attributeDTOS);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when input attribute list is empty
     */
    @Test
    public void testGetServiceType_EmptyList() throws Throwable {
        // arrange
        List<AttributeDTO> attributeDTOS = new ArrayList<>();
        // act
        String result = invokePrivateMethod(dealQueryFacade, "getServiceType", attributeDTOS);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when attribute list contains null element
     */
    @Test
    public void testGetServiceType_ListContainsNull() throws Throwable {
        // arrange
        List<AttributeDTO> attributeDTOS = new ArrayList<>();
        attributeDTOS.add(null);
        // act
        String result = invokePrivateMethod(dealQueryFacade, "getServiceType", attributeDTOS);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when attribute name doesn't match service_type
     */
    @Test
    public void testGetServiceType_NoMatchingAttribute() throws Throwable {
        // arrange
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("other_type");
        attributeDTO.setValue(Arrays.asList("value1"));
        // act
        String result = invokePrivateMethod(dealQueryFacade, "getServiceType", Collections.singletonList(attributeDTO));
        // assert
        assertEquals("", result);
    }

    /**
     * Test when matching attribute has null value list
     */
    @Test
    public void testGetServiceType_MatchingAttributeNullValue() throws Throwable {
        // arrange
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("service_type");
        attributeDTO.setValue(null);
        // act
        String result = invokePrivateMethod(dealQueryFacade, "getServiceType", Collections.singletonList(attributeDTO));
        // assert
        assertEquals("", result);
    }

    /**
     * Test when matching attribute has empty value list
     */
    @Test
    public void testGetServiceType_MatchingAttributeEmptyValue() throws Throwable {
        // arrange
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("service_type");
        attributeDTO.setValue(new ArrayList<>());
        // act
        String result = invokePrivateMethod(dealQueryFacade, "getServiceType", Collections.singletonList(attributeDTO));
        // assert
        assertEquals("", result);
    }

    /**
     * Test successful case with valid service type value
     */
    @Test
    public void testGetServiceType_ValidServiceType() throws Throwable {
        // arrange
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("service_type");
        attributeDTO.setValue(Arrays.asList("type1", "type2"));
        // act
        String result = invokePrivateMethod(dealQueryFacade, "getServiceType", Collections.singletonList(attributeDTO));
        // assert
        assertEquals("type1", result);
    }

    /**
     * Test when multiple matching attributes exist (should return first match)
     */
    @Test
    public void testGetServiceType_MultipleMatches() throws Throwable {
        // arrange
        AttributeDTO attributeDTO1 = new AttributeDTO();
        attributeDTO1.setName("service_type");
        attributeDTO1.setValue(Arrays.asList("type1"));
        AttributeDTO attributeDTO2 = new AttributeDTO();
        attributeDTO2.setName("service_type");
        attributeDTO2.setValue(Arrays.asList("type2"));
        List<AttributeDTO> attributeDTOS = Arrays.asList(attributeDTO1, attributeDTO2);
        // act
        String result = invokePrivateMethod(dealQueryFacade, "getServiceType", attributeDTOS);
        // assert
        assertEquals("type1", result);
    }

    /**
     * Test the normal scenario where the method retrieves multiple batches of deal group IDs.
     */
    @Test
    public void testGetOnlineDealGroupIdsNormalScenario() throws Throwable {
        // arrange
        long dpShopId = 12345L;
        boolean isDp = true;
        when(dealShopQueryService.querySaleDealGroupId(dpShopId, isDp ? 100 : 200, 0, 50)).thenReturn(Arrays.asList(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L, 9L, 10L, 11L, 12L, 13L, 14L, 15L, 16L, 17L, 18L, 19L, 20L, 21L, 22L, 23L, 24L, 25L, 26L, 27L, 28L, 29L, 30L, 31L, 32L, 33L, 34L, 35L, 36L, 37L, 38L, 39L, 40L, 41L, 42L, 43L, 44L, 45L, 46L, 47L, 48L, 49L, 50L));
        when(dealShopQueryService.querySaleDealGroupId(dpShopId, isDp ? 100 : 200, 50, 50)).thenReturn(Collections.emptyList());
        // act
        List<Long> result = invokePrivateMethod(dealQueryFacade, "getOnlineDealGroupIds", dpShopId, isDp);
        // assert
        assertEquals(50, result.size());
        verify(dealShopQueryService, times(2)).querySaleDealGroupId(anyLong(), anyInt(), anyInt(), anyInt());
    }

    /**
     * Test the empty result scenario where no deal group IDs are available.
     */
    @Test
    public void testGetOnlineDealGroupIdsEmptyResultScenario() throws Throwable {
        // arrange
        long dpShopId = 12345L;
        boolean isDp = true;
        when(dealShopQueryService.querySaleDealGroupId(dpShopId, isDp ? 100 : 200, 0, 50)).thenReturn(Collections.emptyList());
        // act
        List<Long> result = invokePrivateMethod(dealQueryFacade, "getOnlineDealGroupIds", dpShopId, isDp);
        // assert
        assertEquals(Collections.emptyList(), result);
        verify(dealShopQueryService, times(1)).querySaleDealGroupId(anyLong(), anyInt(), anyInt(), anyInt());
    }

    /**
     * Test the partial result scenario where all deal group IDs are retrieved in a single batch.
     */
    @Test
    public void testGetOnlineDealGroupIdsPartialResultScenario() throws Throwable {
        // arrange
        long dpShopId = 12345L;
        boolean isDp = true;
        when(dealShopQueryService.querySaleDealGroupId(dpShopId, isDp ? 100 : 200, 0, 50)).thenReturn(Arrays.asList(1L, 2L, 3L));
        // act
        List<Long> result = invokePrivateMethod(dealQueryFacade, "getOnlineDealGroupIds", dpShopId, isDp);
        // assert
        assertEquals(Arrays.asList(1L, 2L, 3L), result);
        verify(dealShopQueryService, times(1)).querySaleDealGroupId(anyLong(), anyInt(), anyInt(), anyInt());
    }

    /**
     * Test the exception scenario where the service call throws an exception.
     */
    @Test(expected = RuntimeException.class)
    public void testGetOnlineDealGroupIdsExceptionScenario() throws Throwable {
        // arrange
        long dpShopId = 12345L;
        boolean isDp = true;
        when(dealShopQueryService.querySaleDealGroupId(dpShopId, isDp ? 100 : 200, 0, 50)).thenThrow(new RuntimeException("Service call failed"));
        // act
        try {
            invokePrivateMethod(dealQueryFacade, "getOnlineDealGroupIds", dpShopId, isDp);
        } catch (Exception e) {
            if (e.getCause() instanceof RuntimeException) {
                throw (RuntimeException) e.getCause();
            }
            throw e;
        }
        // assert
        // Expecting an exception to be thrown
    }

    /**
     * Test when json parameter is null
     */
    @Test
    public void testGetStringFromJson_NullJson() throws Throwable {
        // arrange
        String json = null;
        String key = "testKey";
        // act
        String result = invokeGetStringFromJson(json, key);
        // assert
        Assert.assertEquals(StringUtils.EMPTY, result);
    }

    /**
     * Test when json parameter is empty string
     */
    @Test
    public void testGetStringFromJson_EmptyJson() throws Throwable {
        // arrange
        String json = "";
        String key = "testKey";
        // act
        String result = invokeGetStringFromJson(json, key);
        // assert
        Assert.assertEquals(StringUtils.EMPTY, result);
    }

    /**
     * Test when json parameter is blank string
     */
    @Test
    public void testGetStringFromJson_BlankJson() throws Throwable {
        // arrange
        String json = "   ";
        String key = "testKey";
        // act
        String result = invokeGetStringFromJson(json, key);
        // assert
        Assert.assertEquals(StringUtils.EMPTY, result);
    }

    /**
     * Test when json is valid and key exists with non-empty value
     */
    @Test
    public void testGetStringFromJson_ValidJsonAndKeyExists() throws Throwable {
        // arrange
        String json = "{\"testKey\":\"testValue\"}";
        String key = "testKey";
        // act
        String result = invokeGetStringFromJson(json, key);
        // assert
        Assert.assertEquals("testValue", result);
    }

    /**
     * Test when json is valid but key doesn't exist
     */
    @Test
    public void testGetStringFromJson_ValidJsonButKeyNotExists() throws Throwable {
        // arrange
        String json = "{\"anotherKey\":\"testValue\"}";
        String key = "testKey";
        // act
        String result = invokeGetStringFromJson(json, key);
        // assert
        Assert.assertEquals(StringUtils.EMPTY, result);
    }

    /**
     * Test when json is valid but value for key is null
     */
    @Test
    public void testGetStringFromJson_ValidJsonButValueIsNull() throws Throwable {
        // arrange
        String json = "{\"testKey\":null}";
        String key = "testKey";
        // act
        String result = invokeGetStringFromJson(json, key);
        // assert
        Assert.assertEquals(StringUtils.EMPTY, result);
    }

    /**
     * Test when json is valid but value for key is empty string
     */
    @Test
    public void testGetStringFromJson_ValidJsonButValueIsEmpty() throws Throwable {
        // arrange
        String json = "{\"testKey\":\"\"}";
        String key = "testKey";
        // act
        String result = invokeGetStringFromJson(json, key);
        // assert
        Assert.assertEquals(StringUtils.EMPTY, result);
    }

    /**
     * Test when json is invalid format
     */
    @Test(expected = JSONException.class)
    public void testGetStringFromJson_InvalidJson() throws Throwable {
        // arrange
        String json = "invalid json";
        String key = "testKey";
        // act
        invokeGetStringFromJson(json, key);
    }

    /**
     * Test when key parameter is null
     */
    @Test
    public void testGetStringFromJson_NullKey() throws Throwable {
        // arrange
        String json = "{\"testKey\":\"testValue\"}";
        String key = null;
        // act
        String result = invokeGetStringFromJson(json, key);
        // assert
        Assert.assertEquals(StringUtils.EMPTY, result);
    }

    /**
     * Test MT platform path with valid IDs
     */
    @Test
    public void testQueryDealGroupIM_MtPlatform_ValidIds() throws Throwable {
        // arrange
        DealIMReq request = mock(DealIMReq.class);
        when(request.getDealgroupid()).thenReturn(123);
        when(request.getShopIdLong()).thenReturn(456L);
        EnvCtx envCtx = mock(EnvCtx.class);
        when(envCtx.isMt()).thenReturn(true);
        when(envCtx.getClientType()).thenReturn(1);
        Future future = mock(Future.class);
        when(mapperWrapper.preDpShopIdByMtShopId(anyLong())).thenReturn(future);
        when(mapperWrapper.getDpShopIdByMtShopIdLong(any())).thenReturn(789L);
        when(dealGroupWrapper.getDpDealGroupId(anyInt())).thenReturn(321);
        DealGroupChannelDTO channelDTO = mock(DealGroupChannelDTO.class);
        when(dealGroupPublishCategoryQueryService.getDealGroupChannelById(anyInt())).thenReturn(channelDTO);
        Future consultFuture = mock(Future.class);
        when(dzImWrapper.preOnlineConsultUrl(anyLong(), anyInt(), anyInt())).thenReturn(consultFuture);
        when(dzImWrapper.getOnlineConsultUrl(any())).thenReturn("test_url");
        // act
        DealGroupImVo result = dealQueryFacade.queryDealGroupIM(request, envCtx);
        // assert
        assertNotNull(result);
        assertEquals("test_url", result.getImUrl());
        verify(mapperWrapper).preDpShopIdByMtShopId(456L);
        verify(dealGroupWrapper).getDpDealGroupId(123);
        verify(dealGroupPublishCategoryQueryService).getDealGroupChannelById(321);
    }

    /**
     * Test invalid IDs scenario
     */
    @Test
    public void testQueryDealGroupIM_InvalidIds() throws Throwable {
        // arrange
        DealIMReq request = mock(DealIMReq.class);
        when(request.getDealgroupid()).thenReturn(0);
        when(request.getShopIdLong()).thenReturn(0L);
        EnvCtx envCtx = mock(EnvCtx.class);
        when(envCtx.isMt()).thenReturn(true);
        when(dealGroupWrapper.getDpDealGroupId(0)).thenReturn(0);
        // act
        DealGroupImVo result = dealQueryFacade.queryDealGroupIM(request, envCtx);
        // assert
        assertNotNull(result);
        assertNull(result.getImUrl());
        verify(dealGroupPublishCategoryQueryService, never()).getDealGroupChannelById(anyInt());
    }

    /**
     * Test channel validation failure
     */
    @Test
    public void testQueryDealGroupIM_ChannelValidationFails() throws Throwable {
        // arrange
        DealIMReq request = mock(DealIMReq.class);
        when(request.getDealgroupid()).thenReturn(123);
        when(request.getShopIdLong()).thenReturn(456L);
        EnvCtx envCtx = mock(EnvCtx.class);
        when(envCtx.isMt()).thenReturn(false);
        DealGroupChannelDTO channelDTO = mock(DealGroupChannelDTO.class);
        when(dealGroupPublishCategoryQueryService.getDealGroupChannelById(anyInt())).thenReturn(channelDTO);
        // act
        DealGroupImVo result = dealQueryFacade.queryDealGroupIM(request, envCtx);
        // assert
        assertNotNull(result);
        assertNull(result.getImUrl());
        verify(dealGroupPublishCategoryQueryService).getDealGroupChannelById(123);
    }

    /**
     * Test exception handling in getDealGroupChannelById
     */
    @Test
    public void testQueryDealGroupIM_ChannelQueryException() throws Throwable {
        // arrange
        DealIMReq request = mock(DealIMReq.class);
        when(request.getDealgroupid()).thenReturn(123);
        when(request.getShopIdLong()).thenReturn(456L);
        EnvCtx envCtx = mock(EnvCtx.class);
        when(envCtx.isMt()).thenReturn(false);
        when(dealGroupPublishCategoryQueryService.getDealGroupChannelById(anyInt())).thenThrow(new RuntimeException("Test exception"));
        // act
        DealGroupImVo result = dealQueryFacade.queryDealGroupIM(request, envCtx);
        // assert
        assertNotNull(result);
        assertNull(result.getImUrl());
        verify(dealGroupPublishCategoryQueryService).getDealGroupChannelById(123);
    }
}
