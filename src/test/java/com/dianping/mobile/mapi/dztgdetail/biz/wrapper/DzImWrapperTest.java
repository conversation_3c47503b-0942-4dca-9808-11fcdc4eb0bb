package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.sankuai.dzim.cliententry.ClientEntryService;
import com.sankuai.dzim.cliententry.dto.BatchClientEntryWithSendUnitReqDTO;
import com.sankuai.dzim.cliententry.dto.ClientEntryDTO;
import com.sankuai.dzim.cliententry.dto.ClientEntryReqDTO;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DzImWrapperTest {

    @InjectMocks
    private DzImWrapper dzImWrapper;

    @Mock
    private ClientEntryService clientEntryService;

    private MockedStatic<FutureFactory> mocked;

    @Mock
    private Future future;

    @Before
    public void setUp() throws Exception {
        mocked = mockStatic(FutureFactory.class);
    }

    @After
    public void teardown() {
        this.mocked.close();
    }

    /**
     * 测试dpShopId无效的情况
     */
    @Test
    public void testPreBatchGetImmersiveImgImUrlWithInvalidDpShopId() throws Exception {
        Future result = dzImWrapper.preBatchGetImmersiveImgImUrl(-1L, Arrays.asList(1L, 2L, 3L), 1);
        assertNull(result);
    }

    /**
     * 测试contentIds为空的情况
     */
    @Test
    public void testPreBatchGetImmersiveImgImUrlWithEmptyContentIds() throws Exception {
        Future result = dzImWrapper.preBatchGetImmersiveImgImUrl(1L, null, 1);
        assertNull(result);
    }

    /**
     * 测试clientEntryService调用异常的情况
     */
    @Test
    public void testPreBatchGetImmersiveImgImUrlWithServiceException() throws Exception {
        when(clientEntryService.batchGetClientEntryWithSendUnit(any(BatchClientEntryWithSendUnitReqDTO.class))).thenThrow(new RuntimeException());
        Future result = dzImWrapper.preBatchGetImmersiveImgImUrl(1L, Arrays.asList(1L, 2L, 3L), 1);
        assertNull(result);
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testPreBatchGetImmersiveImgImUrl() throws Exception {
        when(clientEntryService.batchGetClientEntryWithSendUnit(any(BatchClientEntryWithSendUnitReqDTO.class))).thenReturn(Collections.emptyList());
        Future mockFuture = mock(Future.class);
        mocked.when(FutureFactory::getFuture).thenReturn(mockFuture);
        Future result = dzImWrapper.preBatchGetImmersiveImgImUrl(1L, Arrays.asList(1L, 2L, 3L), 1);
        assertNotNull(result);
    }

    @Test
    public void testBatchGetImmersiveImgImUrlWhenFutureResultIsEmpty() throws Exception {
        // arrange
        DzImWrapper dzImWrapper = new DzImWrapper();
        when(future.get()).thenReturn(null);
        // act
        Map<String, String> result = dzImWrapper.batchGetImmersiveImgImUrl(future);
        // assert
        assertEquals(0, result.size());
    }

    @Test
    public void testBatchGetImmersiveImgImUrlWhenAllClientEntryDTOIsNotShow() throws Exception {
        // arrange
        DzImWrapper dzImWrapper = new DzImWrapper();
        ClientEntryDTO clientEntryDTO1 = new ClientEntryDTO();
        clientEntryDTO1.setSendUnitId("1");
        clientEntryDTO1.setEntryUrl("url1");
        clientEntryDTO1.setShow(false);
        ClientEntryDTO clientEntryDTO2 = new ClientEntryDTO();
        clientEntryDTO2.setSendUnitId("2");
        clientEntryDTO2.setEntryUrl("url2");
        clientEntryDTO2.setShow(false);
        List<ClientEntryDTO> clientEntryDTOList = Arrays.asList(clientEntryDTO1, clientEntryDTO2);
        when(future.get()).thenReturn(clientEntryDTOList);
        // act
        Map<String, String> result = dzImWrapper.batchGetImmersiveImgImUrl(future);
        // assert
        assertEquals(0, result.size());
    }

    @Test
    public void testBatchGetImmersiveImgImUrlWhenSomeClientEntryDTOIsShow() throws Exception {
        // arrange
        DzImWrapper dzImWrapper = new DzImWrapper();
        ClientEntryDTO clientEntryDTO1 = new ClientEntryDTO();
        clientEntryDTO1.setSendUnitId("1");
        clientEntryDTO1.setEntryUrl("url1");
        clientEntryDTO1.setShow(false);
        ClientEntryDTO clientEntryDTO2 = new ClientEntryDTO();
        clientEntryDTO2.setSendUnitId("2");
        clientEntryDTO2.setEntryUrl("url2");
        clientEntryDTO2.setShow(true);
        List<ClientEntryDTO> clientEntryDTOList = Arrays.asList(clientEntryDTO1, clientEntryDTO2);
        when(future.get()).thenReturn(clientEntryDTOList);
        // act
        Map<String, String> result = dzImWrapper.batchGetImmersiveImgImUrl(future);
        // assert
        assertEquals(1, result.size());
        assertEquals("url2", result.get("2"));
    }

    /**
     * 测试preOnlineConsultUrl方法，当dpPoiId或dpDealGroupId小于等于0时，应返回null
     */
    @Test
    public void testPreOnlineConsultUrlWithInvalidInput() throws Throwable {
        long dpPoiId = -1;
        int dpDealGroupId = -1;
        int clientType = 1;
        Future result = dzImWrapper.preOnlineConsultUrl(dpPoiId, dpDealGroupId, clientType);
        assertNull(result);
    }

    /**
     * 测试preOnlineConsultUrl方法，当clientEntryService.getClientEntry方法调用成功时，应返回Future对象
     * Note: Adjusted to verify clientEntryService.getClientEntry is called due to inability to mock FutureFactory.getFuture()
     */
    @Test
    public void testPreOnlineConsultUrlWithSuccess() throws Throwable {
        long dpPoiId = 1;
        int dpDealGroupId = 1;
        int clientType = 1;
        ClientEntryDTO clientEntryDTO = new ClientEntryDTO();
        when(clientEntryService.getClientEntry(any(ClientEntryReqDTO.class))).thenReturn(clientEntryDTO);
        // Note: Removed mocking of FutureFactory.getFuture() due to limitations
        dzImWrapper.preOnlineConsultUrl(dpPoiId, dpDealGroupId, clientType);
        // Verify that getClientEntry is called with the correct parameters
        verify(clientEntryService).getClientEntry(any(ClientEntryReqDTO.class));
    }

    /**
     * 测试preOnlineConsultUrl方法，当clientEntryService.getClientEntry方法调用过程中发生异常时，应返回null
     */
    @Test
    public void testPreOnlineConsultUrlWithException() throws Throwable {
        long dpPoiId = 1;
        int dpDealGroupId = 1;
        int clientType = 1;
        doThrow(new RuntimeException()).when(clientEntryService).getClientEntry(any(ClientEntryReqDTO.class));
        Future result = dzImWrapper.preOnlineConsultUrl(dpPoiId, dpDealGroupId, clientType);
        assertNull(result);
    }
}
