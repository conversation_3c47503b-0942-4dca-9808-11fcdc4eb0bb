package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.carnation.dto.DealGroupDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.meituan.nibscp.common.api.enums.TradeTypeEnum;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ResvStatusProcessorIsEnableTest {

    private ResvStatusProcessor resvStatusProcessor;

    @Mock
    private Lion lion;

    @Mock
    private DealCtx ctx;

    private DealGroupDTO dealGroupDTO;

    private DealGroupBasicDTO basic;

    @Before
    public void setUp() {
        resvStatusProcessor = new ResvStatusProcessor();
        dealGroupDTO = new DealGroupDTO();
        basic = new DealGroupBasicDTO();
    }

    /**
     * Test case where DealGroupDTO is null.
     */
    @Test
    public void testIsEnableDealGroupDTONull() throws Throwable {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(null);
        // act
        boolean result = resvStatusProcessor.isEnable(ctx);
        // assert
        assertFalse(result);
    }
}
