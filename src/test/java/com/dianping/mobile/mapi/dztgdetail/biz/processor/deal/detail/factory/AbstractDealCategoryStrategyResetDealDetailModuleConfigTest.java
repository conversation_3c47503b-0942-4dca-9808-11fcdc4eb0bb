package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryAbInfo;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryDealModuleInfo;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryParam;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleExtraDTO;
import java.util.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractDealCategoryStrategyResetDealDetailModuleConfigTest {

    // Concrete test subclass
    private static class TestDealCategoryStrategy extends AbstractDealCategoryStrategy {

        private boolean newDealStyleResult = false;

        private DealCategoryDealModuleInfo dealModuleInfo;

        private boolean buildDealModuleInfoCalled = false;

        private boolean handleDealDetailCpvModuleKeyCalled = false;

        public void setNewDealStyleResult(boolean result) {
            this.newDealStyleResult = result;
        }

        public void setDealModuleInfo(DealCategoryDealModuleInfo info) {
            this.dealModuleInfo = info;
        }

        @Override
        protected DealCategoryAbInfo buildAbInfo() {
            return null;
        }

        @Override
        protected DealCategoryDealModuleInfo buildDealModuleInfo() {
            buildDealModuleInfoCalled = true;
            return dealModuleInfo;
        }

        @Override
        protected boolean customNewDealStyle(DealCategoryParam dealCategoryParam) {
            return newDealStyleResult;
        }

        @Override
        public ModuleAbConfig getModuleAbConfig(EnvCtx envCtx) {
            return null;
        }

        @Override
        public ModuleAbConfig getSimilarDealModuleAbConfig(EnvCtx envCtx) {
            return null;
        }

        @Override
        public ModuleAbConfig getTimesDealModuleAbConfig(EnvCtx envCtx) {
            return null;
        }
        // Since handleDealDetailCpvModuleKey is private in the parent class, we can't override it
        // Instead, we'll use a spy to track its calls
    }

    /**
     * dealCategoryParam is null, should return immediately
     */
    @Test
    public void testResetDealDetailModuleConfig_NullParam() throws Throwable {
        // arrange
        TestDealCategoryStrategy strategy = spy(new TestDealCategoryStrategy());
        // act
        strategy.resetDealDetailModuleConfig(null);
        // assert
        verify(strategy, never()).buildDealModuleInfo();
    }

    /**
     * dealCategoryParam.getModuleExtraDTO() is null, should return immediately
     */
    @Test
    public void testResetDealDetailModuleConfig_NullModuleExtraDTO() throws Throwable {
        // arrange
        TestDealCategoryStrategy strategy = spy(new TestDealCategoryStrategy());
        DealCategoryParam param = DealCategoryParam.builder().moduleExtraDTO(null).build();
        // act
        strategy.resetDealDetailModuleConfig(param);
        // assert
        verify(strategy, never()).buildDealModuleInfo();
    }

    /**
     * dealCategoryParam.getModuleExtraDTO().getModuleConfigDos() is empty, should return immediately
     */
    @Test
    public void testResetDealDetailModuleConfig_EmptyModuleConfigDos() throws Throwable {
        // arrange
        TestDealCategoryStrategy strategy = spy(new TestDealCategoryStrategy());
        ModuleExtraDTO extraDTO = new ModuleExtraDTO();
        extraDTO.setModuleConfigDos(Collections.emptyList());
        DealCategoryParam param = DealCategoryParam.builder().moduleExtraDTO(extraDTO).build();
        // act
        strategy.resetDealDetailModuleConfig(param);
        // assert
        verify(strategy, never()).buildDealModuleInfo();
    }

    /**
     * dealCategoryParam.getEnvCtx() is null, should return immediately
     */
    @Test
    public void testResetDealDetailModuleConfig_NullEnvCtx() throws Throwable {
        // arrange
        TestDealCategoryStrategy strategy = spy(new TestDealCategoryStrategy());
        ModuleExtraDTO extraDTO = new ModuleExtraDTO();
        extraDTO.setModuleConfigDos(Arrays.asList(new ModuleConfigDo()));
        DealCategoryParam param = DealCategoryParam.builder().moduleExtraDTO(extraDTO).envCtx(null).build();
        // act
        strategy.resetDealDetailModuleConfig(param);
        // assert
        verify(strategy, never()).buildDealModuleInfo();
    }

    /**
     * newDealStyle returns false, should call handleDealDetailCpvModuleKey and return
     */
    @Test
    public void testResetDealDetailModuleConfig_NewDealStyleFalse() throws Throwable {
        // arrange
        TestDealCategoryStrategy strategy = spy(new TestDealCategoryStrategy());
        strategy.setNewDealStyleResult(false);
        ModuleConfigDo configDo = new ModuleConfigDo();
        configDo.setKey("anyKey");
        ModuleExtraDTO extraDTO = new ModuleExtraDTO();
        extraDTO.setModuleConfigDos(Arrays.asList(configDo));
        EnvCtx envCtx = mock(EnvCtx.class);
        DealCategoryParam param = DealCategoryParam.builder().moduleExtraDTO(extraDTO).envCtx(envCtx).build();
        // act
        strategy.resetDealDetailModuleConfig(param);
        // assert
        verify(strategy, never()).buildDealModuleInfo();
    }

    /**
     * newDealStyle returns true, moduleConfigDos contains a module with key != DEAL_DETAIL
     */
    @Test
    public void testResetDealDetailModuleConfig_NoDealDetailKey() throws Throwable {
        // arrange
        TestDealCategoryStrategy strategy = spy(new TestDealCategoryStrategy());
        strategy.setNewDealStyleResult(true);
        // Prepare module info
        DealCategoryDealModuleInfo info = DealCategoryDealModuleInfo.builder().mtModuleKey("mtKey").dpModuleKey("dpKey").build();
        strategy.setDealModuleInfo(info);
        // Prepare module config
        ModuleConfigDo configDo = new ModuleConfigDo();
        configDo.setKey("not_deal_detail");
        configDo.setValue("oldValue");
        ModuleExtraDTO extraDTO = new ModuleExtraDTO();
        extraDTO.setModuleConfigDos(Arrays.asList(configDo));
        // Prepare envCtx
        EnvCtx envCtx = mock(EnvCtx.class);
        DealCategoryParam param = DealCategoryParam.builder().moduleExtraDTO(extraDTO).envCtx(envCtx).build();
        // act
        strategy.resetDealDetailModuleConfig(param);
        // assert
        verify(strategy, never()).buildDealModuleInfo();
        assertEquals("oldValue", configDo.getValue());
    }
}
