package com.dianping.mobile.mapi.dztgdetail.util.useragent;

import com.sankuai.hotel.login.authenticate.api.helper.DefaultUserAgentParser;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class UserAgentUtilsTest {

    @Mock
    private DefaultUserAgentParser userAgentParser;

    @Test
    public void testIsApolloWhenUserAgentIsNull() {
        Boolean result = UserAgentUtils.isApollo(null);
        assertFalse(result);
    }

    @Test
    public void testIsBeamAppWhenUserAgentIsNull() throws Throwable {
        // arrange
        String userAgent = null;
        // act
        boolean result = UserAgentUtils.isBeamApp(userAgent);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsBeamAppWhenUserAgentMatchesAndroidRegex() throws Throwable {
        // arrange
        String userAgent = "beam/com.meituan.android.beam/1.0.0 App/1.0.0 (Linux; U; Android 4.4.4; zh-CN; wv) Beam/1.0.0";
        // act
        boolean result = UserAgentUtils.isBeamApp(userAgent);
        // assert
        assertTrue(result);
    }

    @Test
    public void testIsBeamAppWhenUserAgentMatchesIosRegex() throws Throwable {
        // arrange
        String userAgent = "Beam/1.0.0 (iPhone; Beam/1.0.0) Version/1.0.0 CFNetwork/11.0.3 Darwin/********.1";
        // act
        boolean result = UserAgentUtils.isBeamApp(userAgent);
        // assert
        assertTrue(result);
    }

    @Test
    public void testIsBeamAppWhenUserAgentDoesNotMatchAnyRegex() throws Throwable {
        // arrange
        String userAgent = "not a beam app";
        // act
        boolean result = UserAgentUtils.isBeamApp(userAgent);
        // assert
        assertFalse(result);
    }
}
