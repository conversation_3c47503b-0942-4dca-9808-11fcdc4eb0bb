package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.PreOrderParamBuilderService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertFalse;

/**
 * @author: zhangyuan103
 * @create: 2025-03-20
 * @description:
 */
@RunWith(MockitoJUnitRunner.class)
public class PreOrderParamBuilderServiceTest {
    @InjectMocks
    private PreOrderParamBuilderService preOrderParamBuilderService;

    @Test
    public void testBuildPreOrderParam() {
        DealCtx dealCtx = Mockito.mock(DealCtx.class);
        Mockito.when(dealCtx.isPreOderDeal()).thenReturn(true);
        DealGroupPBO dealGroupPBO = new DealGroupPBO();
        preOrderParamBuilderService.buildPreOrderParam(dealCtx, dealGroupPBO);
        assertFalse(dealGroupPBO.isHasReserveEntrance());
    }
}
