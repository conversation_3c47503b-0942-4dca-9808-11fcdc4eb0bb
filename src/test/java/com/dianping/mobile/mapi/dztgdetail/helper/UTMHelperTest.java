package com.dianping.mobile.mapi.dztgdetail.helper;

import org.junit.Test;
import static org.junit.Assert.*;

public class UTMHelperTest {

    /**
     * Test when utmCampaign is null.
     * Expecting a NullPointerException due to the current implementation.
     */
    @Test(expected = NullPointerException.class)
    public void testConvertUTMCampaign2AppNull() throws Throwable {
        // arrange
        String utmCampaign = null;
        // act
        UTMHelper.convertUTMCampaign2App(utmCampaign);
        // assert is handled by the expected exception
    }

    /**
     * Test when utmCampaign is an empty string.
     */
    @Test
    public void testConvertUTMCampaign2AppEmpty() throws Throwable {
        // arrange
        String utmCampaign = "";
        // act
        String result = UTMHelper.convertUTMCampaign2App(utmCampaign);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when utmCampaign does not contain 'A'.
     */
    @Test
    public void testConvertUTMCampaign2AppNoA() throws Throwable {
        // arrange
        String utmCampaign = "B";
        // act
        String result = UTMHelper.convertUTMCampaign2App(utmCampaign);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when utmCampaign contains an 'A' but no string between 'A-Z'.
     */
    @Test
    public void testConvertUTMCampaign2AppNoApp() throws Throwable {
        // arrange
        String utmCampaign = "A";
        // act
        String result = UTMHelper.convertUTMCampaign2App(utmCampaign);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when utmCampaign contains an 'A' and has a string between 'A-Z'.
     */
    @Test
    public void testConvertUTMCampaign2AppWithApp() throws Throwable {
        // arrange
        String utmCampaign = "AtestA";
        // act
        String result = UTMHelper.convertUTMCampaign2App(utmCampaign);
        // assert
        assertEquals("test", result);
    }
}
