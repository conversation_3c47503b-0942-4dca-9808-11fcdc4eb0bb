package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.Arrays;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import com.sankuai.dealuser.price.display.api.enums.PromoIdentityEnum;
import org.junit.Before;

public class PriceDisplayWrapper_HasExclusiveDeductionTest {

    // Rule 3: Avoid using BeforeClass and Before annotations for setup.
    private PriceDisplayWrapper priceDisplayWrapper;

    @Test
    public void testHasExclusiveDeduction_NormalPriceIsNull() throws Throwable {
        PriceDisplayWrapper priceDisplayWrapper = new PriceDisplayWrapper();
        assertFalse(priceDisplayWrapper.hasExclusiveDeduction(null));
    }

    @Test
    public void testHasExclusiveDeduction_UsedPromosIsEmpty() throws Throwable {
        PriceDisplayWrapper priceDisplayWrapper = new PriceDisplayWrapper();
        PriceDisplayDTO normalPrice = Mockito.mock(PriceDisplayDTO.class);
        Mockito.when(normalPrice.getUsedPromos()).thenReturn(null);
        assertFalse(priceDisplayWrapper.hasExclusiveDeduction(normalPrice));
    }

    @Test
    public void testHasExclusiveDeduction_NoExclusiveDeduction() throws Throwable {
        PriceDisplayWrapper priceDisplayWrapper = new PriceDisplayWrapper();
        PriceDisplayDTO normalPrice = Mockito.mock(PriceDisplayDTO.class);
        PromoDTO promoDTO = Mockito.mock(PromoDTO.class);
        Mockito.when(promoDTO.getPromoIdentity()).thenReturn("OTHER");
        Mockito.when(normalPrice.getUsedPromos()).thenReturn(Arrays.asList(promoDTO));
        assertFalse(priceDisplayWrapper.hasExclusiveDeduction(normalPrice));
    }
}
