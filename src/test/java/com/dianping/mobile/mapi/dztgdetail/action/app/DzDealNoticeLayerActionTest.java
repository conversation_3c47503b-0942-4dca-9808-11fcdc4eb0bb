package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealNoticeLayerReq;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.refEq;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @create 2024/10/10 16:17
 */
@RunWith(MockitoJUnitRunner.class)
public class DzDealNoticeLayerActionTest {
    @Mock
    private IMobileContext iMobileContext;
    DzDealNoticeLayerAction action = new DzDealNoticeLayerAction();

    DealNoticeLayerReq request =new DealNoticeLayerReq();

    @Before
    public void setUp() {
        request.setDealgroupid(123421345);
    }
    @Test(expected = Exception.class)
    public void testExecuteException() throws Exception {
        // arrange
        action.validate(request, iMobileContext);
        action.execute(request, iMobileContext);
        action.getRule();
        Assert.assertNotNull(request);
    }
    @Test(expected = Exception.class)
    public void testExecuteInitEnvCtxException() throws Throwable {
        when(iMobileContext.getRequest()).thenThrow(new Exception());
        action.execute(request, iMobileContext);
    }
}
