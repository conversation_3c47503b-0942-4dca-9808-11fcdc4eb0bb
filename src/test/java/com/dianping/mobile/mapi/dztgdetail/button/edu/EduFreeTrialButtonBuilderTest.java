package com.dianping.mobile.mapi.dztgdetail.button.edu;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.EducationDealAttrUtils;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
import com.dianping.mobile.mapi.dztgdetail.util.EduDealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class EduFreeTrialButtonBuilderTest {

    @Mock
    private DealCtx context;

    @Mock
    private ButtonBuilderChain chain;

    @Mock
    private DealBuyBar dealBuyBar;

    @Mock
    private DealGroupDTO dealGroupDTO;

    private MockedStatic<LionConfigUtils> lionConfigUtils;

    private MockedStatic<EduDealUtils> eduDealUtils;

    private MockedStatic<EducationDealAttrUtils> eduAttrUtils;

    private EduFreeTrialButtonBuilder builder = new EduFreeTrialButtonBuilder();

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        lionConfigUtils = Mockito.mockStatic(LionConfigUtils.class);
        eduDealUtils = Mockito.mockStatic(EduDealUtils.class);
        eduAttrUtils = Mockito.mockStatic(EducationDealAttrUtils.class);
        context.setBuyBar(dealBuyBar);
        context.setDealGroupDTO(dealGroupDTO);
    }

    @After
    public void tearDown() {
        lionConfigUtils.close();
        eduDealUtils.close();
        eduAttrUtils.close();
    }

    /**
     * 测试 doBuild 方法，当 deal 是在线教育 deal 并且有合法的视频列表时, build方法和addButton方法应该被调用一次。
     */
    @Test
    public void testDoBuildOnlineEduDealWithLegalVideoList() {
        // arrange
        when(context.getBuyBar()).thenReturn(dealBuyBar);

        eduDealUtils.when(()-> EduDealUtils.isEduOnlineCourseDeal(any())).thenReturn(true);
        eduDealUtils.when(() -> EduDealUtils.getServiceTypeId(any())).thenReturn(1L);
        eduDealUtils.when(() -> EduDealUtils.getLegalVideoNum(any())).thenReturn(1);

        when(context.isMt()).thenReturn(true);

        // act
        builder.doBuild(context, chain);

        // assert
        verify(context, times(1)).addButton(any());
        verify(chain, times(1)).build(context);
    }

    /**
     * 测试 doBuild 方法，当 deal 不是在线教育 deal 或者没有合法的视频列表时, build方法调用一次，addButton方法不应该被调用。
     */
    @Test
    public void testDoBuildNotOnlineEduDealOrNoLegalVideoList() throws Throwable {
        // arrange
        when(context.getDealGroupDTO()).thenReturn(new DealGroupDTO());
        lionConfigUtils.when(()-> LionConfigUtils.isEduOnlineDeal(anyInt(), any())).thenReturn(true);
        eduDealUtils.when(() -> EduDealUtils.getServiceTypeId(context.getDealGroupDTO())).thenReturn(1L);
        eduDealUtils.when(() -> EduDealUtils.getLegalVideoNum(context)).thenReturn(0);

        // act
        builder.doBuild(context, chain);

        // assert
        verify(context, times(0)).addButton(any());
        verify(chain, times(1)).build(context);
    }

    /**
     * 测试 doBuild 方法，当 deal 是正价课或集训营 并且有免费试听时, build方法和addButton方法应该被调用一次。
     */
    @Test
    public void testDoBuildVocationalEduCourseOrCampWithLegalTrialVideo() {
        // arrange
        when(context.getBuyBar()).thenReturn(dealBuyBar);

        eduDealUtils.when(()-> EduDealUtils.isVocationalEduCourseOrCamp(any())).thenReturn(true);
        eduDealUtils.when(() -> EduDealUtils.getServiceTypeId(any())).thenReturn(1L);
        eduDealUtils.when(() -> EduDealUtils.getLegalVideoNum(any())).thenReturn(1);
        eduAttrUtils.when(() -> EducationDealAttrUtils.hasFreeAudition(any())).thenReturn(true);

        // act
        builder.doBuild(context, chain);

        // assert
        verify(context, times(1)).addButton(any());
        verify(chain, times(1)).build(context);
    }

    /**
     * 测试 doBuild 方法，当 deal 不是在线教育 deal 或者没有合法的视频列表时, build方法调用一次，addButton方法不应该被调用。
     */
    @Test
    public void testDoBuildNotOnlineEduDealWithNoLegalTrialVideo() throws Throwable {
        // arrange

        eduDealUtils.when(()-> EduDealUtils.isVocationalEduCourseOrCamp(any())).thenReturn(true);
        eduDealUtils.when(() -> EduDealUtils.getServiceTypeId(any())).thenReturn(1L);
        eduAttrUtils.when(() -> EducationDealAttrUtils.hasFreeAudition(any())).thenReturn(false);

        // act
        builder.doBuild(context, chain);

        // assert
        verify(context, times(0)).addButton(any());
        verify(chain, times(1)).build(context);
    }

    /**
     * 测试 doBuild 方法，当 deal 符合短期课预约课条件时，addButton和build方法应该都被调用一次
     */
    @Test
    public void testDoBuildQualifyShortClassFreeTrailTrue() {
        // arrange
        when(context.getBuyBar()).thenReturn(dealBuyBar);
        eduDealUtils.when(()-> EduDealUtils.checkShortClassHasButton(any())).thenReturn(true);

        // act
        builder.doBuild(context, chain);

        // assert
        verify(context, times(1)).addButton(any());
        verify(chain, times(1)).build(context);
    }
}
