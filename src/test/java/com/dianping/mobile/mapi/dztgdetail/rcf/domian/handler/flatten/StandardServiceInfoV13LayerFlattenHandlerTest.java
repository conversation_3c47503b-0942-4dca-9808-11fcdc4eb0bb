package com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler.flatten;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.mobile.mapi.dztgdetail.rcf.enums.ModuleType;
import com.dianping.mobile.mapi.dztgdetail.util.ApplicationContextGetBeanHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.Objects;


/**
 * <AUTHOR>
 * @create 2024/12/25 11:16
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({ApplicationContextGetBeanHelper.class, ApplicationContext.class})
public class StandardServiceInfoV13LayerFlattenHandlerTest{
    @InjectMocks
    private StandardServiceInfoV13LayerFlattenHandler flattenHandler;

    @Test
    public void getType() {
        Assert.assertTrue(ModuleType.standard_service_info_v1_3layer == flattenHandler.getType());
    }

    @Test
    public void flattenModule() {
        String json = "{\"standardServiceModel\":null,\"videoModel\":null,\"skuGroupsModel2\":null,\"name\":null,\"type\":\"standard_service_info_v1_3layer\",\"subTitle\":null,\"descModel\":null,\"priceModel\":null,\"titleModel\":null,\"skuGroupsModel1\":[{\"dealSkuList\":[{\"subTitle\":\"60分钟\",\"popup\":null,\"desc\":null,\"items\":[{\"icon\":null,\"config\":null,\"type\":0,\"valueAttrs\":null,\"picValues\":null,\"value\":\"全身（含头部、肩颈、腰背、四肢）\",\"name\":\"服务部位\",\"rightText\":null,\"extraExplain\":null,\"originalPrice\":null,\"closeTitle\":null,\"openTitle\":null,\"showAll\":false},{\"icon\":null,\"config\":null,\"type\":2,\"valueAttrs\":[{\"info\":[\"20分钟\"],\"values\":null,\"name\":\"肩颈精油SPA\",\"popup\":null},{\"info\":[\"20分钟\"],\"values\":null,\"name\":\"腰背精油SPA\",\"popup\":null},{\"info\":[\"15分钟\"],\"values\":null,\"name\":\"四肢精油SPA\",\"popup\":null},{\"info\":[\"5分钟\"],\"values\":null,\"name\":\"头部按摩\",\"popup\":null}],\"picValues\":null,\"value\":null,\"name\":\"服务流程\",\"rightText\":null,\"extraExplain\":null,\"originalPrice\":null,\"closeTitle\":null,\"openTitle\":null,\"showAll\":false}],\"copies\":null,\"price\":null,\"title\":\"全身精油SPA\",\"icon\":null,\"originalPrice\":null,\"tag\":null,\"jumpUrl\":null,\"type\":null,\"rightText\":null,\"fullOccupy\":null}],\"title\":null,\"titleStyle\":null}],\"dealStructAttrsModel1\":null,\"dealStructAttrsModel2\":null,\"extraExplain\":null,\"showNum\":0,\"foldStr\":null,\"dotType\":0,\"jumpUrl\":null,\"dealDetailModuleList2\":null,\"subTitleItems\":null}";
        JSONObject module  = JSON.parseObject(json);
        JSONArray rcfSkuGroupsModule1Flatten = new JSONArray();
        flattenHandler.flattenModule(rcfSkuGroupsModule1Flatten, module);
        Assert.assertFalse(rcfSkuGroupsModule1Flatten.isEmpty());
    }
}
