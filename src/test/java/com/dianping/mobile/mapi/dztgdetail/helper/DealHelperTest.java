package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.deal.stock.dto.ProductGroupStock;
import com.dianping.json.facade.JsonFacade;
import com.dianping.lion.client.Lion;
import java.util.ArrayList;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealHelperTest {

    private MockedStatic<Lion> lionMockedStatic;

    private MockedStatic<JsonFacade> jsonFacadeMockedStatic;

    private static final int MT_PLATFORM = 1;

    private static final int DP_PLATFORM = 2;

    @Before
    public void setUp() {
        lionMockedStatic = mockStatic(Lion.class);
        jsonFacadeMockedStatic = mockStatic(JsonFacade.class);
    }

    @After
    public void tearDown() {
        lionMockedStatic.close();
        jsonFacadeMockedStatic.close();
    }

    /**
     * 测试 getSelectDealConfig 方法，当 configStr 为空时
     */
    @Test
    public void testGetSelectDealConfigWhenConfigStrIsEmpty() throws Throwable {
        // arrange
        lionMockedStatic.when(() -> Lion.get(anyString())).thenReturn("");
        // act
        SelectDealConfig result = DealHelper.getSelectDealConfig();
        // assert
        assertNull(result);
    }

    /**
     * 测试 getSelectDealConfig 方法，当 configStr 不为空，但反序列化结果为 null 时
     */
    @Test
    public void testGetSelectDealConfigWhenConfigStrIsNotEmptyButDeserializeResultIsNull() throws Throwable {
        // arrange
        lionMockedStatic.when(() -> Lion.get(anyString())).thenReturn("configStr");
        jsonFacadeMockedStatic.when(() -> JsonFacade.deserialize(anyString(), any(Class.class))).thenReturn(null);
        // act
        SelectDealConfig result = DealHelper.getSelectDealConfig();
        // assert
        assertNull(result);
    }

    /**
     * 测试 getSelectDealConfig 方法，当 configStr 不为空，反序列化结果不为 null，但 dpDealGroupIds 和 mtDealGroupIds 都为 null 时
     */
    @Test
    public void testGetSelectDealConfigWhenConfigStrIsNotEmptyAndDeserializeResultIsNotNullButDpDealGroupIdsAndMtDealGroupIdsAreNull() throws Throwable {
        // arrange
        SelectDealConfig config = new SelectDealConfig();
        lionMockedStatic.when(() -> Lion.get(anyString())).thenReturn("configStr");
        jsonFacadeMockedStatic.when(() -> JsonFacade.deserialize(anyString(), any(Class.class))).thenReturn(config);
        // act
        SelectDealConfig result = DealHelper.getSelectDealConfig();
        // assert
        assertNull(result);
    }

    /**
     * 测试 getSelectDealConfig 方法，当 configStr 不为空，反序列化结果不为 null，dpDealGroupIds 或 mtDealGroupIds 不为 null 时
     */
    @Test
    public void testGetSelectDealConfigWhenConfigStrIsNotEmptyAndDeserializeResultIsNotNullAndDpDealGroupIdsOrMtDealGroupIdsIsNotNull() throws Throwable {
        // arrange
        SelectDealConfig config = new SelectDealConfig();
        config.setDpDealGroupIds(new ArrayList<>());
        lionMockedStatic.when(() -> Lion.get(anyString())).thenReturn("configStr");
        jsonFacadeMockedStatic.when(() -> JsonFacade.deserialize(anyString(), any(Class.class))).thenReturn(config);
        // act
        SelectDealConfig result = DealHelper.getSelectDealConfig();
        // assert
        assertNotNull(result);
        assertEquals(config, result);
    }

    @Test
    public void testIsSoldOutDealGroupStockIsNull() throws Throwable {
        int clientType = MT_PLATFORM;
        ProductGroupStock dealGroupStock = null;
        boolean result = DealHelper.isSoldOut(clientType, dealGroupStock);
        assertFalse("Expected isSoldOut to return false when dealGroupStock is null", result);
    }

    @Test
    public void testIsSoldOutMtPlatformAndNotMtSoldOut() throws Throwable {
        int clientType = MT_PLATFORM;
        ProductGroupStock dealGroupStock = mock(ProductGroupStock.class);
        boolean result = DealHelper.isSoldOut(clientType, dealGroupStock);
        assertFalse("Expected isSoldOut to return false for MT_PLATFORM when MT is not sold out", result);
    }

    @Test
    public void testIsSoldOutNotMtPlatformAndDpSoldOut() throws Throwable {
        int clientType = DP_PLATFORM;
        ProductGroupStock dealGroupStock = mock(ProductGroupStock.class);
        when(dealGroupStock.isDpSoldOut()).thenReturn(true);
        boolean result = DealHelper.isSoldOut(clientType, dealGroupStock);
        assertTrue("Expected isSoldOut to return true for DP_PLATFORM when DP is sold out", result);
    }

    @Test
    public void testIsSoldOutNotMtPlatformAndNotDpSoldOut() throws Throwable {
        int clientType = DP_PLATFORM;
        ProductGroupStock dealGroupStock = mock(ProductGroupStock.class);
        when(dealGroupStock.isDpSoldOut()).thenReturn(false);
        boolean result = DealHelper.isSoldOut(clientType, dealGroupStock);
        assertFalse("Expected isSoldOut to return false for DP_PLATFORM when DP is not sold out", result);
    }
}
