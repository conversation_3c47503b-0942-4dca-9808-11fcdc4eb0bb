package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.attribute.dto.AttributeDTO;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealStyleWrapperGetStandardDealGroupAttrValueTest {

    private DealStyleWrapper dealStyleWrapper = new DealStyleWrapper();

    private String invokePrivateMethod(String methodName, List<AttributeDTO> attributeDTOS, String mrnVersion) throws Exception {
        Method method = DealStyleWrapper.class.getDeclaredMethod(methodName, List.class, String.class);
        method.setAccessible(true);
        return (String) method.invoke(dealStyleWrapper, attributeDTOS, mrnVersion);
    }

    @Test
    public void testGetStandardDealGroupAttrValueWhenAttributeDTOSIsEmpty() throws Throwable {
        String result = invokePrivateMethod("getStandardDealGroupAttrValue", Collections.emptyList(), "1.0");
        assertEquals("", result);
    }

    @Test
    public void testGetStandardDealGroupAttrValueWhenIsWuyoutongReturnsFalseAndNoMatchedAttributeDTO() throws Throwable {
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("other");
        attributeDTO.setValue(Arrays.asList("1"));
        String result = invokePrivateMethod("getStandardDealGroupAttrValue", Arrays.asList(attributeDTO), "1.0");
        assertEquals("", result);
    }

    @Test
    public void testGetStandardDealGroupAttrValueWhenIsWuyoutongReturnsFalseAndMatchedAttributeDTO() throws Throwable {
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("standardDealGroup");
        attributeDTO.setValue(Arrays.asList("1"));
        String result = invokePrivateMethod("getStandardDealGroupAttrValue", Arrays.asList(attributeDTO), "1.0");
        assertEquals("1", result);
    }

    @Test
    public void testGetStandardDealGroupAttrValueWhenIsWuyoutongReturnsTrue() throws Throwable {
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("standardDealGroup");
        attributeDTO.setValue(Arrays.asList("1"));
        String result = invokePrivateMethod("getStandardDealGroupAttrValue", Arrays.asList(attributeDTO), "1.0");
        assertEquals("1", result);
    }
}
