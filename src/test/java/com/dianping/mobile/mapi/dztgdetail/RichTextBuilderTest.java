package com.dianping.mobile.mapi.dztgdetail;

import com.dianping.deal.common.builder.RichTextBuilder;
import org.junit.Ignore;
import org.junit.Test;

public class RichTextBuilderTest {
    @Test
    @Ignore
    public void richRichTextBuilderTest() {
        int textSize = 24;
        RichTextBuilder.TextItem textItem = new RichTextBuilder.TextItem();
        textItem.setTextsize(textSize);
        textItem.setTextstyle(RichTextBuilder.TextStyle.DEFAULT.getStyle());
        textItem.setTextcolor("222222");

        RichTextBuilder.TextItem highlightItem = new RichTextBuilder.TextItem();
        highlightItem.setTextcolor("#FF4B10");
        highlightItem.setTextsize(textSize);
        highlightItem.setTextstyle(RichTextBuilder.TextStyle.DEFAULT.getStyle());
        RichTextBuilder richTextBuilder = new RichTextBuilder("你有56元专属优惠券，下单立享，仅剩10天", "56|10")
                .setDefaultTextItem(textItem)
                .setHltTextItem(highlightItem)
                .build();
        System.out.println(
                richTextBuilder.toString()
        );
    }
}
