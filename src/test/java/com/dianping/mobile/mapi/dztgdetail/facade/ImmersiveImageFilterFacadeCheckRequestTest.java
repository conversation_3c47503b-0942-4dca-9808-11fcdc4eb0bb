package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageFilterRequest;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ImmersiveImageFilterFacadeCheckRequestTest {

    private ImmersiveImageFilterFacade immersiveImageFilterFacade = new ImmersiveImageFilterFacade();

    private void invokeCheckRequest(GetImmersiveImageFilterRequest request) throws Throwable {
        Method method = ImmersiveImageFilterFacade.class.getDeclaredMethod("checkRequest", GetImmersiveImageFilterRequest.class);
        method.setAccessible(true);
        try {
            method.invoke(immersiveImageFilterFacade, request);
        } catch (InvocationTargetException e) {
            // Unwrap the InvocationTargetException to get the cause
            throw e.getCause();
        }
    }

    @Test(expected = NullPointerException.class)
    public void testCheckRequestNullRequest() throws Throwable {
        invokeCheckRequest(null);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testCheckRequestNullDealGroupId() throws Throwable {
        GetImmersiveImageFilterRequest request = new GetImmersiveImageFilterRequest();
        request.setShopId(1L);
        invokeCheckRequest(request);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testCheckRequestInvalidDealGroupId() throws Throwable {
        GetImmersiveImageFilterRequest request = new GetImmersiveImageFilterRequest();
        request.setDealGroupId(0);
        request.setShopId(1L);
        invokeCheckRequest(request);
    }

    // Note: The following tests are limited by the inability to mock or inject the list of ImmersiveImageService instances
    @Test(expected = IllegalArgumentException.class)
    public void testCheckRequestNullShopId() throws Throwable {
        GetImmersiveImageFilterRequest request = new GetImmersiveImageFilterRequest();
        request.setDealGroupId(1);
        invokeCheckRequest(request);
    }

    // directly into the ImmersiveImageFilterFacade due to the constraints provided.
    // The NullPointerException issue suggests a need for such an initialization, which cannot be directly addressed here.
    @Test(expected = IllegalArgumentException.class)
    public void testCheckRequestInvalidShopId() throws Throwable {
        GetImmersiveImageFilterRequest request = new GetImmersiveImageFilterRequest();
        request.setDealGroupId(1);
        request.setShopId(0L);
        invokeCheckRequest(request);
    }

    @Test
    public void testCheckRequestValidRequest() throws Throwable {
        GetImmersiveImageFilterRequest request = new GetImmersiveImageFilterRequest();
        request.setDealGroupId(1);
        request.setShopId(1L);
        invokeCheckRequest(request);
        assertTrue("Method should complete without throwing an exception for a valid request.", true);
    }
}
