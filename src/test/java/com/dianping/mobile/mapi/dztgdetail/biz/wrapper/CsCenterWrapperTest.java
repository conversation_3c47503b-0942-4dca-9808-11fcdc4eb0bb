package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.dianping.csc.center.engine.access.dto.AccessRequestDTO;
import com.dianping.csc.center.engine.access.dto.AccessResponseDTO;
import com.dianping.csc.center.engine.access.service.AccessInService;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.constants.CsCenterAccessAppKey;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class CsCenterWrapperTest {

    @InjectMocks
    private CsCenterWrapper csCenterWrapper;

    @Mock
    private Future<AccessResponseDTO> mockFuture;

    @Mock
    private AccessResponseDTO mockResponse;

    @Mock
    private AccessInService cscAccessInServiceFuture;

    private DealCtx dealCtx;

    private EnvCtx envCtx;

    @Before
    public void setUp() {
        envCtx = new EnvCtx();
        dealCtx = new DealCtx(envCtx);
    }

    /**
     * Test when future parameter is null
     */
    @Test
    public void testGetCsCenterUrl_WhenFutureIsNull() {
        // arrange
        Future<AccessResponseDTO> nullFuture = null;
        // act
        String result = csCenterWrapper.getCsCenterUrl(nullFuture);
        // assert
        assertNull(result);
    }

    /**
     * Test when future.get() throws ExecutionException
     */
    @Test
    public void testGetCsCenterUrl_WhenFutureThrowsException() throws Exception {
        // arrange
        when(mockFuture.get()).thenThrow(new ExecutionException("Test exception", new RuntimeException()));
        // act
        String result = csCenterWrapper.getCsCenterUrl(mockFuture);
        // assert
        assertNull(result);
    }

    /**
     * Test when AccessResponseDTO is null
     */
    @Test
    public void testGetCsCenterUrl_WhenResponseIsNull() throws Exception {
        // arrange
        when(mockFuture.get()).thenReturn(null);
        // act
        String result = csCenterWrapper.getCsCenterUrl(mockFuture);
        // assert
        assertNull(result);
    }

    /**
     * Test when isSuccess() returns false
     */
    @Test
    public void testGetCsCenterUrl_WhenNotSuccess() throws Exception {
        // arrange
        when(mockFuture.get()).thenReturn(mockResponse);
        when(mockResponse.isSuccess()).thenReturn(false);
        // act
        String result = csCenterWrapper.getCsCenterUrl(mockFuture);
        // assert
        assertNull(result);
    }

    /**
     * Test happy path - when everything succeeds
     */
    @Test
    public void testGetCsCenterUrl_WhenSuccessful() throws Exception {
        // arrange
        String expectedUrl = "http://test.url";
        when(mockFuture.get()).thenReturn(mockResponse);
        when(mockResponse.isSuccess()).thenReturn(true);
        when(mockResponse.getUrl()).thenReturn(expectedUrl);
        // act
        String result = csCenterWrapper.getCsCenterUrl(mockFuture);
        // assert
        assertEquals(expectedUrl, result);
    }

    /**
     * Test when getUrl() returns null even with success true
     */
    @Test
    public void testGetCsCenterUrl_WhenSuccessButUrlIsNull() throws Exception {
        // arrange
        when(mockFuture.get()).thenReturn(mockResponse);
        when(mockResponse.isSuccess()).thenReturn(true);
        when(mockResponse.getUrl()).thenReturn(null);
        // act
        String result = csCenterWrapper.getCsCenterUrl(mockFuture);
        // assert
        assertNull(result);
    }

    /**
     * Test prepareAccessIn with cleaning self-own deal and Meituan APP client
     */
    @Test
    public void testPrepareAccessIn_CleaningSelfOwnDeal_MeituanApp() throws Throwable {
        // arrange
        dealCtx = spy(dealCtx);
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        envCtx.setHarmony(false);
        when(dealCtx.isCleaningSelfOwnDeal()).thenReturn(true);
        when(dealCtx.getDealId4P()).thenReturn(123);
        when(dealCtx.getLongPoiId4PFromReq()).thenReturn(456L);
        when(dealCtx.getUserId4P()).thenReturn(789L);
        when(dealCtx.getCityId4P()).thenReturn(1);
        // act
        csCenterWrapper.prepareAccessIn(dealCtx);
        // assert
        verify(cscAccessInServiceFuture).accessIn(argThat(req -> req.getAppKey().equals(CsCenterAccessAppKey.CLEANING_DEAL_DETAIL_MT_APP) && req.getOpenId().equals("789") && req.getLocCity().equals("1")));
    }

    /**
     * Test prepareAccessIn with carefree deal and Dianping APP client
     */
    @Test
    public void testPrepareAccessIn_CareFreeDeal_DianpingApp() throws Throwable {
        // arrange
        dealCtx = spy(dealCtx);
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        envCtx.setHarmony(false);
        when(dealCtx.isCareFreeDeal()).thenReturn(true);
        when(dealCtx.getDealId4P()).thenReturn(123);
        when(dealCtx.getLongPoiId4PFromReq()).thenReturn(456L);
        when(dealCtx.getUserId4P()).thenReturn(789L);
        when(dealCtx.getCityId4P()).thenReturn(1);
        // act
        csCenterWrapper.prepareAccessIn(dealCtx);
        // assert
        verify(cscAccessInServiceFuture).accessIn(argThat(req -> req.getAppKey().equals(CsCenterAccessAppKey.CAREFREE_DEAL_DETAIL_DP_APP) && req.getOpenId().equals("789") && req.getLocCity().equals("1")));
    }

    /**
     * Test prepareAccessIn with cleaning self-own deal and WAP client
     */
    @Test
    public void testPrepareAccessIn_CleaningSelfOwnDeal_WapClient() throws Throwable {
        // arrange
        dealCtx = spy(dealCtx);
        envCtx.setClientType(ClientTypeEnum.mt_wap.getType());
        when(dealCtx.isCleaningSelfOwnDeal()).thenReturn(true);
        when(dealCtx.getDealId4P()).thenReturn(123);
        when(dealCtx.getLongPoiId4PFromReq()).thenReturn(456L);
        when(dealCtx.getUserId4P()).thenReturn(789L);
        when(dealCtx.getCityId4P()).thenReturn(1);
        // act
        csCenterWrapper.prepareAccessIn(dealCtx);
        // assert
        verify(cscAccessInServiceFuture).accessIn(argThat(req -> req.getAppKey().equals(CsCenterAccessAppKey.CLEANING_DEAL_DETAIL_MT_I) && req.getOpenId().equals("789") && req.getLocCity().equals("1")));
    }

    /**
     * Test prepareAccessIn when service throws exception
     */
    @Test
    public void testPrepareAccessIn_ServiceException() throws Throwable {
        // arrange
        dealCtx = spy(dealCtx);
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        when(dealCtx.isCleaningSelfOwnDeal()).thenReturn(true);
        when(dealCtx.getDealId4P()).thenReturn(123);
        when(dealCtx.getLongPoiId4PFromReq()).thenReturn(456L);
        when(dealCtx.getUserId4P()).thenReturn(789L);
        when(dealCtx.getCityId4P()).thenReturn(1);
        when(cscAccessInServiceFuture.accessIn(any())).thenThrow(new RuntimeException("Service error"));
        // act
        Future result = csCenterWrapper.prepareAccessIn(dealCtx);
        // assert
        assertNull(result);
        verify(cscAccessInServiceFuture).accessIn(any());
    }

    /**
     * Test prepareAccessIn with null context
     */
    @Test(expected = NullPointerException.class)
    public void testPrepareAccessIn_NullContext() throws Throwable {
        csCenterWrapper.prepareAccessIn(null);
    }

    /**
     * Test prepareAccessIn with harmony client
     */
    @Test
    public void testPrepareAccessIn_HarmonyClient() throws Throwable {
        // arrange
        dealCtx = spy(dealCtx);
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        envCtx.setHarmony(true);
        when(dealCtx.isCleaningSelfOwnDeal()).thenReturn(true);
        when(dealCtx.getDealId4P()).thenReturn(123);
        when(dealCtx.getLongPoiId4PFromReq()).thenReturn(456L);
        when(dealCtx.getUserId4P()).thenReturn(789L);
        when(dealCtx.getCityId4P()).thenReturn(1);
        // act
        csCenterWrapper.prepareAccessIn(dealCtx);
        // assert
        verify(cscAccessInServiceFuture).accessIn(argThat(req -> req.getAppKey().equals(CsCenterAccessAppKey.CLEANING_DEAL_DETAIL_MT_HM_APP) && req.getOpenId().equals("789") && req.getLocCity().equals("1")));
    }
}
