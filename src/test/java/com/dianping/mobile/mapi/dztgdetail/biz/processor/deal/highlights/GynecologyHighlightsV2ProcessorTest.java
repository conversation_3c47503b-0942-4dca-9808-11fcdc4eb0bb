package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.glasses.factory.GlassesFactory;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.gynecology.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CommonAttrVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.highlights.DztgHighlightsModule;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.GynecologyHighlightsV2Processor.GYNECOLOGY_SERVER_TYPE_HANDLER_MAP;
import static groovy.util.GroovyTestCase.assertEquals;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GynecologyHighlightsV2ProcessorTest {

    @InjectMocks
    private GynecologyHighlightsV2Processor gynecologyHighlightsV2Processor;

    @Mock
    ApplicationContext appCtx;
    @Mock
    GlassesFactory glassesFactory;

    @Before
    public void setUp() throws Exception {
        reset(appCtx);
        GYNECOLOGY_SERVER_TYPE_HANDLER_MAP.put("早孕检查",new EarlyPregnancyCheckHandler());
        GYNECOLOGY_SERVER_TYPE_HANDLER_MAP.put("妇科综合检查", new ComprehensiveGynecologicalExaminationHandler());
        GYNECOLOGY_SERVER_TYPE_HANDLER_MAP.put("妇科专项检查", new ComprehensiveGynecologicalExaminationHandler());
        GYNECOLOGY_SERVER_TYPE_HANDLER_MAP.put("疾病检查", new ComprehensiveGynecologicalExaminationHandler());
        GYNECOLOGY_SERVER_TYPE_HANDLER_MAP.put("癌症筛查", new ComprehensiveGynecologicalExaminationHandler());
        GYNECOLOGY_SERVER_TYPE_HANDLER_MAP.put("备孕", new PreparePregnancyHandler());
        GYNECOLOGY_SERVER_TYPE_HANDLER_MAP.put("人流", new InducedAbortionHandler());
        GYNECOLOGY_SERVER_TYPE_HANDLER_MAP.put("产检/排畸", new AntenatalExaminationHandler());
        GYNECOLOGY_SERVER_TYPE_HANDLER_MAP.put("产后修复", new PostpartumRepairHandler());
        GYNECOLOGY_SERVER_TYPE_HANDLER_MAP.put("产后服务", new PostpartumRepairHandler());
        glassesFactory.init();
    }

    /**
     * 测试beforeBuild方法，当dealGroupDTO为null时
     */
    @Test
    public void testBeforeBuildWhenDealGroupDTOIsNull() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());

        // act
        gynecologyHighlightsV2Processor.beforeBuild(ctx);

        // assert
        assertNull(ctx.getHighlightsModule());
    }

    /**
     * 测试beforeBuild方法，当处理器为null时
     */
    @Test
    public void testBeforeBuildWhenHandlerIsNull() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        ctx.setDealGroupDTO(dealGroupDTO);

        // act
        gynecologyHighlightsV2Processor.beforeBuild(ctx);

        // assert
        assertNull(ctx.getHighlightsModule());
    }

    /**
     * 测试afterBuild方法，当attrs的数量小于2时
     */
    @Test
    public void testAfterBuildWhenAttrsSizeLessThan2() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        DztgHighlightsModule highlightsModule = new DztgHighlightsModule();
        highlightsModule.setAttrs(new ArrayList<>());
        ctx.setHighlightsModule(highlightsModule);

        // act
        gynecologyHighlightsV2Processor.afterBuild(ctx);

        // assert
        assertNull(ctx.getHighlightsModule());
    }

    /**
     * 测试getHighlightsIdentify方法，当DztgHighlightsModule为null时
     */
    @Test
    public void testGetHighlightsIdentify_ModuleIsNull() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.getHighlightsModule()).thenReturn(null);

        // act
        String result = gynecologyHighlightsV2Processor.getHighlightsIdentify(ctx);

        // assert
        assertNull(result);
    }

    /**
     * 测试getHighlightsIdentify方法，当DztgHighlightsModule不为null时
     */
    @Test
    public void testGetHighlightsIdentify_ModuleIsNotNull() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        DztgHighlightsModule module = mock(DztgHighlightsModule.class);
        when(ctx.getHighlightsModule()).thenReturn(module);
        when(module.getIdentify()).thenReturn("test_identify");

        // act
        String result = gynecologyHighlightsV2Processor.getHighlightsIdentify(ctx);

        // assert
        assertEquals("test_identify", result);
    }

    /**
     * 测试getHighlightsStyle方法，当DztgHighlightsModule为null时
     */
    @Test
    public void testGetHighlightsStyle_ModuleIsNull() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.getHighlightsModule()).thenReturn(null);

        // act
        String result = gynecologyHighlightsV2Processor.getHighlightsStyle(ctx);

        // assert
        assertNull(result);
    }

    /**
     * 测试getHighlightsStyle方法，当DztgHighlightsModule不为null时
     */
    @Test
    public void testGetHighlightsStyle_ModuleIsNotNull() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        DztgHighlightsModule module = mock(DztgHighlightsModule.class);
        when(ctx.getHighlightsModule()).thenReturn(module);
        when(module.getStyle()).thenReturn("test_style");

        // act
        String result = gynecologyHighlightsV2Processor.getHighlightsStyle(ctx);

        // assert
        assertEquals("test_style", result);
    }

    /**
     * 测试getHighlightsContent方法，当DztgHighlightsModule为null时
     */
    @Test
    public void testGetHighlightsContent_ModuleIsNull() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.getHighlightsModule()).thenReturn(null);

        // act
        String result = gynecologyHighlightsV2Processor.getHighlightsContent(ctx);

        // assert
        assertNull(result);
    }

    /**
     * 测试getHighlightsContent方法，当DztgHighlightsModule不为null时
     */
    @Test
    public void testGetHighlightsContent_ModuleIsNotNull() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        DztgHighlightsModule module = mock(DztgHighlightsModule.class);
        when(ctx.getHighlightsModule()).thenReturn(module);
        when(module.getContent()).thenReturn("test_content");

        // act
        String result = gynecologyHighlightsV2Processor.getHighlightsContent(ctx);

        // assert
        assertEquals("test_content", result);
    }

    /**
     * 测试getHighlightsAttrs方法，当DztgHighlightsModule为null时
     */
    @Test
    public void testGetHighlightsAttrs_ModuleIsNull() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.getHighlightsModule()).thenReturn(null);

        // act
        List<CommonAttrVO> result = gynecologyHighlightsV2Processor.getHighlightsAttrs(ctx);

        // assert
        assertNull(result);
    }

    /**
     * 测试getHighlightsAttrs方法，当DztgHighlightsModule不为null时
     */
    @Test
    public void testGetHighlightsAttrs_ModuleIsNotNull() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        DztgHighlightsModule module = mock(DztgHighlightsModule.class);
        List<CommonAttrVO> attrs = Arrays.asList(new CommonAttrVO(), new CommonAttrVO());
        when(ctx.getHighlightsModule()).thenReturn(module);
        when(module.getAttrs()).thenReturn(attrs);

        // act
        List<CommonAttrVO> result = gynecologyHighlightsV2Processor.getHighlightsAttrs(ctx);

        // assert
        assertEquals(attrs, result);
    }

    /**
     * 测试 newDztgHighlightsModel 方法
     */
    @Test
    public void testNewDztgHighlightsModel() throws Throwable {

        // act
        DztgHighlightsModule result = gynecologyHighlightsV2Processor.newDztgHighlightsModel();

        // assert
        assertNotNull(result);
        assertEquals("struct", result.getStyle());
        assertNotNull(result.getAttrs());
        assertTrue(result.getAttrs().isEmpty());
    }
}
