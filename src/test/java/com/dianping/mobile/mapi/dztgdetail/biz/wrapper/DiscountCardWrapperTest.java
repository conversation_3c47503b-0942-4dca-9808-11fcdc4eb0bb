package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.gm.marketing.member.card.api.dto.GMResponse;
import com.dianping.gm.marketing.member.card.api.dto.membercard.UserMemberCardInfoDTO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.InjectMocks;
import java.util.concurrent.Future;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DiscountCardWrapperTest {

    @Mock
    private Future future;

    // Using constructor to initialize DiscountCardWrapper
    private DiscountCardWrapper discountCardWrapper = new DiscountCardWrapper();

    /**
     * 测试 Future 结果为 null 的情况
     */
    @Test
    public void testLoadUserMemberCardByDealGroupFutureResultIsNull() throws Throwable {
        // arrange
        when(future.get()).thenReturn(null);
        // act
        UserMemberCardInfoDTO result = discountCardWrapper.loadUserMemberCardByDealGroup(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 Future 结果不为 null，但 code 不等于 GMResponse.SUCCESS_CODE 的情况
     */
    @Test
    public void testLoadUserMemberCardByDealGroupFutureResultCodeIsNotSuccess() throws Throwable {
        // arrange
        GMResponse<UserMemberCardInfoDTO> response = new GMResponse<>();
        // Assuming -1 is not a SUCCESS_CODE
        response.setCode(-1);
        when(future.get()).thenReturn(response);
        // act
        UserMemberCardInfoDTO result = discountCardWrapper.loadUserMemberCardByDealGroup(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 Future 结果不为 null，且 code 等于 GMResponse.SUCCESS_CODE 的情况
     */
    @Test
    public void testLoadUserMemberCardByDealGroupFutureResultCodeIsSuccess() throws Throwable {
        // arrange
        UserMemberCardInfoDTO userMemberCardInfoDTO = new UserMemberCardInfoDTO();
        GMResponse<UserMemberCardInfoDTO> response = new GMResponse<>();
        response.setCode(GMResponse.SUCCESS_CODE);
        response.setData(userMemberCardInfoDTO);
        when(future.get()).thenReturn(response);
        // act
        UserMemberCardInfoDTO result = discountCardWrapper.loadUserMemberCardByDealGroup(future);
        // assert
        assertSame(userMemberCardInfoDTO, result);
    }
}
