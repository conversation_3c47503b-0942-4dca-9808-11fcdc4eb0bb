package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

@RunWith(MockitoJUnitRunner.class)
public class PoiShopCategoryWrapper_GetBackSecondMainCategoryTest {

    @InjectMocks
    private PoiShopCategoryWrapper poiShopCategoryWrapper;

    private DpPoiDTO dpPoiDTO;

    @Before
    public void setUp() {
        dpPoiDTO = new DpPoiDTO();
    }

    /**
     * 测试 dpPoiDTO 为 null 的情况
     */
    @Test
    public void testGetBackSecondMainCategoryDpPoiDTONull() {
        assertNull(poiShopCategoryWrapper.getBackSecondMainCategory(null));
    }

    /**
     * 测试 dpPoiDTO 不为 null，但 backMainCategoryPath 为空的情况
     */
    @Test
    public void testGetBackSecondMainCategoryBackMainCategoryPathEmpty() {
        assertNull(poiShopCategoryWrapper.getBackSecondMainCategory(dpPoiDTO));
    }

    /**
     * 测试 backMainCategoryPath 不为空，但没有 categoryLevel 为 2 的元素的情况
     */
    @Test
    public void testGetBackSecondMainCategoryNoSecondLevelCategory() {
        DpPoiBackCategoryDTO dpPoiBackCategoryDTO = new DpPoiBackCategoryDTO();
        dpPoiBackCategoryDTO.setCategoryLevel(1);
        dpPoiDTO.setBackMainCategoryPath(Arrays.asList(dpPoiBackCategoryDTO));
        assertNull(poiShopCategoryWrapper.getBackSecondMainCategory(dpPoiDTO));
    }

    /**
     * 测试 backMainCategoryPath 不为空，且有 categoryLevel 为 2 的元素的情况
     */
    @Test
    public void testGetBackSecondMainCategoryHasSecondLevelCategory() {
        DpPoiBackCategoryDTO dpPoiBackCategoryDTO1 = new DpPoiBackCategoryDTO();
        dpPoiBackCategoryDTO1.setCategoryLevel(1);
        dpPoiBackCategoryDTO1.setCategoryId(1);
        DpPoiBackCategoryDTO dpPoiBackCategoryDTO2 = new DpPoiBackCategoryDTO();
        dpPoiBackCategoryDTO2.setCategoryLevel(2);
        dpPoiBackCategoryDTO2.setCategoryId(2);
        dpPoiDTO.setBackMainCategoryPath(Arrays.asList(dpPoiBackCategoryDTO1, dpPoiBackCategoryDTO2));
        assertEquals(Integer.valueOf(2), poiShopCategoryWrapper.getBackSecondMainCategory(dpPoiDTO));
    }
}
