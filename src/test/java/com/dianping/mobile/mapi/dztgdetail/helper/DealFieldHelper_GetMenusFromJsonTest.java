package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.mobile.mapi.dztgdetail.common.enums.RedeemTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.MtMenu;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.List;
import static org.junit.Assert.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DealMenuTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.MtMenuDetail;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DealFieldHelper_GetMenusFromJsonTest {

    @Test
    public void testGetMenusFromJsonMenuIsNull() throws Throwable {
        String menu = null;
        int redeemType = RedeemTypeEnum.THIRD_PARTY.getType();
        List<MtMenu> result = DealFieldHelper.getMenusFromJson(menu, redeemType);
        assertNull(result);
    }

    @Test
    public void testGetMenusFromJsonMenuIsEmpty() throws Throwable {
        String menu = "[]";
        int redeemType = RedeemTypeEnum.THIRD_PARTY.getType();
        List<MtMenu> result = DealFieldHelper.getMenusFromJson(menu, redeemType);
        assertNull(result);
    }

    @Test
    public void testGetMenusFromJsonSubtypeIsInvalid() throws Throwable {
        String menu = "[[\"invalid\"]]";
        int redeemType = RedeemTypeEnum.THIRD_PARTY.getType();
        List<MtMenu> result = DealFieldHelper.getMenusFromJson(menu, redeemType);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetMenusFromJsonSubtypeIsTitleOrSubtitle() throws Throwable {
        String menu = "[[{\"subtype\":1,\"content\":\"title\"}]]";
        int redeemType = RedeemTypeEnum.THIRD_PARTY.getType();
        List<MtMenu> result = DealFieldHelper.getMenusFromJson(menu, redeemType);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("title", result.get(0).getTitle());
    }

    @Test
    public void testGetMenusFromJsonSubtypeIsDetail() throws Throwable {
        String menu = "[[{\"subtype\":2,\"content\":\"detail\",\"total\":\"100\",\"specification\":\"spec\"}],[{\"subtype\":1,\"content\":\"title\"}]]";
        int redeemType = RedeemTypeEnum.THIRD_PARTY.getType();
        List<MtMenu> result = DealFieldHelper.getMenusFromJson(menu, redeemType);
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("detail", result.get(0).getMenuDetails().get(0).getContent());
        assertEquals("¥100", result.get(0).getMenuDetails().get(0).getTotalPrice());
        assertEquals("spec", result.get(0).getMenuDetails().get(0).getSpecification());
    }

    @Test
    public void testGetMenusFromJsonRedeemTypeIsThirdPartyAndResultSizeIsGreaterThanZero() throws Throwable {
        String menu = "[[{\"subtype\":1,\"content\":\"title\"}]]";
        int redeemType = RedeemTypeEnum.THIRD_PARTY.getType();
        List<MtMenu> result = DealFieldHelper.getMenusFromJson(menu, redeemType);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("温馨提示：本单需要到第三方网站兑换后才能使用，建议在电脑上进行操作", result.get(0).getHint());
    }

    @Test
    public void testGetMenusFromJsonRedeemTypeIsNotThirdPartyOrResultSizeIsZero() throws Throwable {
        String menu = "[[{\"subtype\":1,\"content\":\"title\"}]]";
        int redeemType = RedeemTypeEnum.THIRD_PARTY.getType();
        List<MtMenu> result = DealFieldHelper.getMenusFromJson(menu, redeemType);
        assertNotNull(result);
        assertNotNull(result.get(0).getHint());
    }
}
