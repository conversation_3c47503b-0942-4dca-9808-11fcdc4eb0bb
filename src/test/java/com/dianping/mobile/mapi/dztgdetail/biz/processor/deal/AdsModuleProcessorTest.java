package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.base.dto.DealBaseDTO;
import com.dianping.deal.stock.dto.ProductStock;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealSelect;
import com.sankuai.general.product.query.center.client.dto.StockDTO;
import java.math.BigDecimal;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AdsModuleProcessorTest {

    private AdsModuleProcessor processor = new AdsModuleProcessor();

    private AdsModuleProcessor adsModuleProcessor;

    @Before
    public void setUp() {
        adsModuleProcessor = new AdsModuleProcessor();
    }

    @Test
    public void testFormatDealSelectNew_BothNull() throws Throwable {
        DealBaseDTO dealBaseDto = null;
        StockDTO stockDto = null;
        DealSelect result = processor.formatDealSelectNew(dealBaseDto, stockDto);
        assertNotNull(result);
    }

    @Test
    public void testFormatDealSelectNew_DealBaseDtoNotNull() throws Throwable {
        DealBaseDTO dealBaseDto = new DealBaseDTO();
        dealBaseDto.setDealId(1);
        dealBaseDto.setShortTitle("Test Deal");
        dealBaseDto.setPrice(new BigDecimal("10.00"));
        dealBaseDto.setDeliverType(1);
        dealBaseDto.setProvideInvoice(true);
        StockDTO stockDto = null;
        DealSelect result = processor.formatDealSelectNew(dealBaseDto, stockDto);
        assertEquals(1, result.getId());
        assertEquals("Test Deal", result.getTitle());
        assertEquals(new BigDecimal("10.00"), result.getPrice());
        assertEquals(1, result.getProvideInvoice());
        assertEquals(2, result.getDealType());
    }

    @Test
    public void testFormatDealSelectNew_StockDtoNotNull() throws Throwable {
        DealBaseDTO dealBaseDto = null;
        StockDTO stockDto = new StockDTO();
        stockDto.setDpSales(100);
        // Initialize dpTotal to avoid NullPointerException
        stockDto.setDpTotal(200);
        stockDto.setIsDpSoldOut(false);
        DealSelect result = processor.formatDealSelectNew(dealBaseDto, stockDto);
        assertEquals(100, result.getCount());
        assertEquals(200, result.getMaxJoin());
        assertEquals(DealSelect.DEAL_STATUS_AVAILABLE, result.getStatus());
    }

    @Test
    public void testFormatDealSelectNew_StockDtoSoldOut() throws Throwable {
        DealBaseDTO dealBaseDto = null;
        StockDTO stockDto = new StockDTO();
        stockDto.setIsDpSoldOut(true);
        // Initialize dpSales to avoid NullPointerException
        stockDto.setDpSales(0);
        // Initialize dpTotal to avoid NullPointerException
        stockDto.setDpTotal(0);
        DealSelect result = processor.formatDealSelectNew(dealBaseDto, stockDto);
        assertEquals(DealSelect.DEAL_STATUS_SOLDOUT, result.getStatus());
    }

    @Test
    public void testFormatDealSelectNew_BothNotNullStockNotSoldOut() throws Throwable {
        DealBaseDTO dealBaseDto = new DealBaseDTO();
        dealBaseDto.setDealId(2);
        dealBaseDto.setShortTitle("Another Deal");
        dealBaseDto.setPrice(new BigDecimal("20.00"));
        dealBaseDto.setDeliverType(0);
        dealBaseDto.setProvideInvoice(false);
        StockDTO stockDto = new StockDTO();
        stockDto.setDpSales(50);
        // Initialize dpTotal to avoid NullPointerException
        stockDto.setDpTotal(150);
        stockDto.setIsDpSoldOut(false);
        DealSelect result = processor.formatDealSelectNew(dealBaseDto, stockDto);
        assertEquals(2, result.getId());
        assertEquals("Another Deal", result.getTitle());
        assertEquals(new BigDecimal("20.00"), result.getPrice());
        assertEquals(0, result.getProvideInvoice());
        assertEquals(1, result.getDealType());
        assertEquals(50, result.getCount());
        assertEquals(150, result.getMaxJoin());
        assertEquals(DealSelect.DEAL_STATUS_AVAILABLE, result.getStatus());
    }

    @Test
    public void testFormatDealSelectNew_BothNotNullStockSoldOut() throws Throwable {
        DealBaseDTO dealBaseDto = new DealBaseDTO();
        dealBaseDto.setDealId(3);
        dealBaseDto.setShortTitle("Sold Out Deal");
        dealBaseDto.setPrice(new BigDecimal("30.00"));
        dealBaseDto.setDeliverType(0);
        dealBaseDto.setProvideInvoice(true);
        StockDTO stockDto = new StockDTO();
        stockDto.setIsDpSoldOut(true);
        // Initialize dpSales to avoid NullPointerException
        stockDto.setDpSales(0);
        // Initialize dpTotal to avoid NullPointerException
        stockDto.setDpTotal(0);
        DealSelect result = processor.formatDealSelectNew(dealBaseDto, stockDto);
        assertEquals(3, result.getId());
        assertEquals("Sold Out Deal", result.getTitle());
        assertEquals(new BigDecimal("30.00"), result.getPrice());
        assertEquals(1, result.getProvideInvoice());
        assertEquals(1, result.getDealType());
        assertEquals(DealSelect.DEAL_STATUS_SOLDOUT, result.getStatus());
    }

    /**
     * Tests formatDealSelectNew method when both dealBaseDto and stockDto are not null.
     */
    @Test
    public void testFormatDealSelectNew_DealBaseDtoAndStockDtoNotNull() throws Throwable {
        // Arrange
        DealBaseDTO dealBaseDto = new DealBaseDTO();
        dealBaseDto.setDealId(1);
        dealBaseDto.setShortTitle("title");
        dealBaseDto.setDeliverType(1);
        dealBaseDto.setProvideInvoice(true);
        dealBaseDto.setPrice(new BigDecimal("100.00"));
        ProductStock stockDto = new ProductStock();
        stockDto.setDpSales(10);
        stockDto.setDpTotal(20);
        stockDto.setDpSoldOut(false);
        // Act
        DealSelect dealSelect = adsModuleProcessor.formatDealSelectNew(dealBaseDto, stockDto);
        // Assert
        assertNotNull(dealSelect);
        assertEquals(1, dealSelect.getId());
        assertEquals("title", dealSelect.getTitle());
        assertEquals("", dealSelect.getGroupTitle());
        assertEquals(true, dealSelect.isDeliver());
        assertEquals(2, dealSelect.getDealType());
        assertEquals(1, dealSelect.getProvideInvoice());
        assertEquals(new BigDecimal("100.00"), dealSelect.getPrice());
        assertEquals(10, dealSelect.getCount());
        assertEquals(20, dealSelect.getMaxJoin());
        assertEquals(DealSelect.DEAL_STATUS_AVAILABLE, dealSelect.getStatus());
    }

    /**
     * Tests formatDealSelectNew method when dealBaseDto is not null and stockDto is null.
     */
    @Test
    public void testFormatDealSelectNew_DealBaseDtoNotNullAndStockDtoNull() throws Throwable {
        // Arrange
        DealBaseDTO dealBaseDto = new DealBaseDTO();
        dealBaseDto.setDealId(1);
        dealBaseDto.setShortTitle("title");
        dealBaseDto.setDeliverType(1);
        dealBaseDto.setProvideInvoice(true);
        dealBaseDto.setPrice(new BigDecimal("100.00"));
        // Act
        // Cast null to ProductStock
        DealSelect dealSelect = adsModuleProcessor.formatDealSelectNew(dealBaseDto, (ProductStock) null);
        // Assert
        assertNotNull(dealSelect);
        assertEquals(1, dealSelect.getId());
        assertEquals("title", dealSelect.getTitle());
        assertEquals("", dealSelect.getGroupTitle());
        assertEquals(true, dealSelect.isDeliver());
        assertEquals(2, dealSelect.getDealType());
        assertEquals(1, dealSelect.getProvideInvoice());
        assertEquals(new BigDecimal("100.00"), dealSelect.getPrice());
    }

    /**
     * Tests formatDealSelectNew method when dealBaseDto is null and stockDto is not null.
     */
    @Test
    public void testFormatDealSelectNew_DealBaseDtoNullAndStockDtoNotNull() throws Throwable {
        // Arrange
        ProductStock stockDto = new ProductStock();
        stockDto.setDpSales(10);
        stockDto.setDpTotal(20);
        stockDto.setDpSoldOut(false);
        // Act
        // Cast null to DealBaseDTO
        DealSelect dealSelect = adsModuleProcessor.formatDealSelectNew((DealBaseDTO) null, stockDto);
        // Assert
        assertNotNull(dealSelect);
        assertEquals(10, dealSelect.getCount());
        assertEquals(20, dealSelect.getMaxJoin());
        assertEquals(DealSelect.DEAL_STATUS_AVAILABLE, dealSelect.getStatus());
    }

    /**
     * Tests formatDealSelectNew method when both dealBaseDto and stockDto are null.
     */
    @Test
    public void testFormatDealSelectNew_DealBaseDtoAndStockDtoNull() throws Throwable {
        // Act
        // Cast both to their respective types
        DealSelect dealSelect = adsModuleProcessor.formatDealSelectNew((DealBaseDTO) null, (ProductStock) null);
        // Assert
        assertNotNull(dealSelect);
    }
}
