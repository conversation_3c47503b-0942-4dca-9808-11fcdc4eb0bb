package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ReviewWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GoodReviewReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.GoodReviewPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.reviewremote.remote.dto.ReviewStarDistributionDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.concurrent.Future;
import java.util.List;
import java.util.ArrayList;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;
import org.junit.Test;

@RunWith(MockitoJUnitRunner.class)
public class GoodReviewFacade_QueryDpGoodReviewTest {

    @InjectMocks
    private GoodReviewFacade goodReviewFacade;

    @Mock
    private ReviewWrapper reviewWrapper;

    @Mock
    private DouHuBiz douHuBiz;

    /**
     * 测试 reviewStar 为 null 的情况
     */
    @Test
    public void testQueryDpGoodReviewWhenReviewStarIsNull() throws Throwable {
        // arrange
        GoodReviewReq req = new GoodReviewReq();
        EnvCtx envCtx = new EnvCtx();
        int categoryId = 1;
        Future future = mock(Future.class);
        when(reviewWrapper.preReviewStarByReferIds(anyInt())).thenReturn(future);
        when(reviewWrapper.getReviewStar(future)).thenReturn(null);
        // act
        GoodReviewPBO result = goodReviewFacade.queryDpGoodReview(req, envCtx, categoryId);
        // assert
        assertNotNull(result);
    }

    /**
     * 测试 reviewStar 不为 null 的情况
     */
    @Test
    public void testQueryDpGoodReviewWhenReviewStarIsNotNull() throws Throwable {
        // arrange
        GoodReviewReq req = new GoodReviewReq();
        EnvCtx envCtx = new EnvCtx();
        int categoryId = 1;
        Future future = mock(Future.class);
        ReviewStarDistributionDTO reviewStar = new ReviewStarDistributionDTO(1L);
        when(reviewWrapper.preReviewStarByReferIds(anyInt())).thenReturn(future);
        when(reviewWrapper.getReviewStar(future)).thenReturn(reviewStar);
        // Mocking douHuBiz to avoid NullPointerException
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        List<AbConfig> abConfigs = new ArrayList<>();
        AbConfig abConfig = new AbConfig();
        // Assuming "a" is a valid result for the test
        abConfig.setExpResult("a");
        abConfigs.add(abConfig);
        moduleAbConfig.setConfigs(abConfigs);
        // act
        GoodReviewPBO result = goodReviewFacade.queryDpGoodReview(req, envCtx, categoryId);
        // assert
        assertNotNull(result);
    }
}
