package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.deal.shop.dto.BestShopReq;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.vc.sdk.concurrent.future.FutureUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.concurrent.Future;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/11/12 16:02
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest()
public class PreviewBestShopProcessorTest {
    @InjectMocks
    private PreviewBestShopProcessor previewBestShopProcessor;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testSetBestShop() throws InvocationTargetException, IllegalAccessException {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        envCtx.setClientType(1);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setFutureCtx(new FutureCtx());

        when(dealGroupWrapper.preDealGroupBestShop(any(BestShopReq.class))).thenReturn(null);

        Method method = PowerMockito.method(PreviewBestShopProcessor.class, "setBestShop");
        method.invoke(previewBestShopProcessor,ctx);


        assert ctx.getFutureCtx().getBestShopFuture() == null;
    }

}