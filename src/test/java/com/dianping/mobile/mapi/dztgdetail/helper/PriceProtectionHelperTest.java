package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.*;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.BestPriceGuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.PriceProtectionTagDTO;
import org.junit.Assert;
import org.junit.Test;

public class PriceProtectionHelperTest {

    /**
     * 测试ObjectGuaranteeTagDTO对象为null的情况
     */
    @Test
    public void testCheckBestPriceGuaranteeInfoValidObjectNull() {
        // arrange
        ObjectGuaranteeTagDTO objectGuaranteeTagDTO = null;
        // act
        boolean result = PriceProtectionHelper.checkBestPriceGuaranteeInfoValid(objectGuaranteeTagDTO);
        // assert
        Assert.assertFalse(result);
    }

    /**
     * 测试ObjectGuaranteeTagDTO对象不为null，但其BestPriceGuaranteeTagDTO属性为null的情况
     */
    @Test
    public void testCheckBestPriceGuaranteeInfoValidBestPriceNull() {
        // arrange
        ObjectGuaranteeTagDTO objectGuaranteeTagDTO = new ObjectGuaranteeTagDTO();
        // act
        boolean result = PriceProtectionHelper.checkBestPriceGuaranteeInfoValid(objectGuaranteeTagDTO);
        // assert
        Assert.assertFalse(result);
    }

    /**
     * 测试ObjectGuaranteeTagDTO对象不为null，其BestPriceGuaranteeTagDTO属性不为null，但valid属性为null的情况
     */
    @Test
    public void testCheckBestPriceGuaranteeInfoValidValidNull() {
        // arrange
        ObjectGuaranteeTagDTO objectGuaranteeTagDTO = new ObjectGuaranteeTagDTO();
        objectGuaranteeTagDTO.setBestPriceGuaranteeTagDTO(new BestPriceGuaranteeTagDTO());
        // act
        boolean result = PriceProtectionHelper.checkBestPriceGuaranteeInfoValid(objectGuaranteeTagDTO);
        // assert
        Assert.assertFalse(result);
    }

    /**
     * 测试ObjectGuaranteeTagDTO对象不为null，其BestPriceGuaranteeTagDTO属性不为null，valid属性不为null，但其值为false的情况
     */
    @Test
    public void testCheckBestPriceGuaranteeInfoValidValidFalse() {
        // arrange
        ObjectGuaranteeTagDTO objectGuaranteeTagDTO = new ObjectGuaranteeTagDTO();
        BestPriceGuaranteeTagDTO bestPriceGuaranteeTagDTO = new BestPriceGuaranteeTagDTO();
        bestPriceGuaranteeTagDTO.setValid(false);
        objectGuaranteeTagDTO.setBestPriceGuaranteeTagDTO(bestPriceGuaranteeTagDTO);
        // act
        boolean result = PriceProtectionHelper.checkBestPriceGuaranteeInfoValid(objectGuaranteeTagDTO);
        // assert
        Assert.assertFalse(result);
    }

    /**
     * 测试ObjectGuaranteeTagDTO对象不为null，其BestPriceGuaranteeTagDTO属性不为null，valid属性不为null，其值为true的情况
     */
    @Test
    public void testCheckBestPriceGuaranteeInfoValidValidTrue() {
        // arrange
        ObjectGuaranteeTagDTO objectGuaranteeTagDTO = new ObjectGuaranteeTagDTO();
        BestPriceGuaranteeTagDTO bestPriceGuaranteeTagDTO = new BestPriceGuaranteeTagDTO();
        bestPriceGuaranteeTagDTO.setValid(true);
        objectGuaranteeTagDTO.setBestPriceGuaranteeTagDTO(bestPriceGuaranteeTagDTO);
        // act
        boolean result = PriceProtectionHelper.checkBestPriceGuaranteeInfoValid(objectGuaranteeTagDTO);
        // assert
        Assert.assertTrue(result);
    }

    /**
     * 测试ObjectGuaranteeTagDTO对象为null的情况
     */
    @Test
    public void testCheckPriceProtectionValidObjectGuaranteeTagDTONull() {
        assertFalse(PriceProtectionHelper.checkPriceProtectionValid(null));
    }

    /**
     * 测试ObjectGuaranteeTagDTO对象不为null，但PriceProtectionTagDTO对象为null的情况
     */
    @Test
    public void testCheckPriceProtectionValidPriceProtectionTagDTONull() {
        ObjectGuaranteeTagDTO objectGuaranteeTagDTO = new ObjectGuaranteeTagDTO();
        assertFalse(PriceProtectionHelper.checkPriceProtectionValid(objectGuaranteeTagDTO));
    }

    /**
     * 测试ObjectGuaranteeTagDTO对象和PriceProtectionTagDTO对象都不为null，但PriceProtectionTagDTO对象的valid属性为null的情况
     */
    @Test
    public void testCheckPriceProtectionValidValidNull() {
        ObjectGuaranteeTagDTO objectGuaranteeTagDTO = new ObjectGuaranteeTagDTO();
        PriceProtectionTagDTO priceProtectionTagDTO = new PriceProtectionTagDTO();
        objectGuaranteeTagDTO.setPriceProtectionTag(priceProtectionTagDTO);
        assertFalse(PriceProtectionHelper.checkPriceProtectionValid(objectGuaranteeTagDTO));
    }

    /**
     * 测试ObjectGuaranteeTagDTO对象和PriceProtectionTagDTO对象都不为null，PriceProtectionTagDTO对象的valid属性为true，但validityDays属性为null的情况
     */
    @Test
    public void testCheckPriceProtectionValidValidityDaysNull() {
        ObjectGuaranteeTagDTO objectGuaranteeTagDTO = new ObjectGuaranteeTagDTO();
        PriceProtectionTagDTO priceProtectionTagDTO = new PriceProtectionTagDTO();
        priceProtectionTagDTO.setValid(true);
        objectGuaranteeTagDTO.setPriceProtectionTag(priceProtectionTagDTO);
        assertFalse(PriceProtectionHelper.checkPriceProtectionValid(objectGuaranteeTagDTO));
    }

    /**
     * 测试ObjectGuaranteeTagDTO对象和PriceProtectionTagDTO对象都不为null，PriceProtectionTagDTO对象的valid属性为true，validityDays属性不为null的情况
     */
    @Test
    public void testCheckPriceProtectionValidValid() {
        ObjectGuaranteeTagDTO objectGuaranteeTagDTO = new ObjectGuaranteeTagDTO();
        PriceProtectionTagDTO priceProtectionTagDTO = new PriceProtectionTagDTO();
        priceProtectionTagDTO.setValid(true);
        priceProtectionTagDTO.setValidityDays(10);
        objectGuaranteeTagDTO.setPriceProtectionTag(priceProtectionTagDTO);
        assertTrue(PriceProtectionHelper.checkPriceProtectionValid(objectGuaranteeTagDTO));
    }
}
