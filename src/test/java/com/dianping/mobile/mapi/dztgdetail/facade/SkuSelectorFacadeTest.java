package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.sku.SalesAttrToSkuBasicInfoDO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.sku.SkuSalesAttrExt;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.sku.SkuSalesAttrWithPicDO;
import com.sankuai.dztheme.deal.res.AttrValueDTO;
import com.sankuai.dztheme.deal.res.SkuAttrDTO;
import com.sankuai.dztheme.deal.res.SkuAttrMetaDTO;
import com.sankuai.dztheme.deal.res.SkuItemDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

/**
 * SkuSelectorFacade的单元测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class SkuSelectorFacadeTest {

    @InjectMocks
    private SkuSelectorFacade skuSelectorFacade;

    @Mock
    private EnvCtx envCtx;

    @Before
    public void setUp() {

    }

    /**
     * 测试buildSkuSalesAttrExt方法，当输入参数为null时
     */
    @Test
    public void testBuildSkuSalesAttrExtInputIsNull() {
        // arrange
        com.sankuai.dztheme.deal.res.SkuSalesAttrExt input = null;

        // act
        SkuSalesAttrExt result = skuSelectorFacade.buildSkuSalesAttrExt(input);

        // assert
        assertNull("当输入参数为null时，应返回null", result);
    }

    /**
     * 测试buildSkuSalesAttrExt方法，当输入参数不为null时
     */
    @Test
    public void testBuildSkuSalesAttrExtInputIsNotNull() {
        // arrange
        com.sankuai.dztheme.deal.res.SkuSalesAttrExt input = new com.sankuai.dztheme.deal.res.SkuSalesAttrExt();
        input.setDesc("描述");
        input.setUrl("http://example.com");

        // act
        SkuSalesAttrExt result = skuSelectorFacade.buildSkuSalesAttrExt(input);

        // assert
        assertEquals("描述", result.getDesc());
        assertEquals("http://example.com", result.getUrl());
    }

    /**
     * 测试 buildSalesAttrToSkuBasicInfo 方法，当 skuItemDTO 为 null 时
     */
    @Test
    public void testBuildSalesAttrToSkuBasicInfo_WithNullSkuItemDTO() {
        SkuItemDTO skuItemDTO = new SkuItemDTO();
        SalesAttrToSkuBasicInfoDO result = skuSelectorFacade.buildSalesAttrToSkuBasicInfo(skuItemDTO, new EnvCtx());
        assertNull("结果应为 null", result);
    }

    /**
     * 测试 buildSalesAttrToSkuBasicInfo 方法，当 skuItemDTO.getSkuAttrList() 为空时
     */
    @Test
    public void testBuildSalesAttrToSkuBasicInfo_WithEmptySkuAttrList() {
        SkuItemDTO skuItemDTO = new SkuItemDTO();
        skuItemDTO.setSkuAttrList(new ArrayList<>());

        SalesAttrToSkuBasicInfoDO result = skuSelectorFacade.buildSalesAttrToSkuBasicInfo(skuItemDTO, new EnvCtx());
        assertNull("结果应为 null", result);
    }

    /**
     * 测试 buildSalesAttrToSkuBasicInfo 方法，正常情况
     */
    @Test
    public void testBuildSalesAttrToSkuBasicInfo_WithValidInput() {
        SkuItemDTO skuItemDTO = new SkuItemDTO();
        skuItemDTO.setSkuId("skuId");
        skuItemDTO.setSkuHeadPic("skuHeadPic");
        skuItemDTO.setMtStock(10);
        skuItemDTO.setDpStock(20);
        skuItemDTO.setSalesAttrInfoKey("salesAttrInfoKey");
        skuItemDTO.setSkuCode("skuCode");

        List<SkuAttrDTO> skuAttrList = new ArrayList<>();
        SkuAttrDTO skuAttrDTO = new SkuAttrDTO();
        skuAttrDTO.setAttrName("attrName");
        skuAttrDTO.setAttrCnName("attrCnName");
        AttrValueDTO attrValueDTO = new AttrValueDTO();
        attrValueDTO.setCode("code");
        attrValueDTO.setId("id");
        skuAttrDTO.setAttrValueDTO(attrValueDTO);
        skuAttrList.add(skuAttrDTO);
        skuItemDTO.setSkuAttrList(skuAttrList);



        when(envCtx.isMt()).thenReturn(true);

        SalesAttrToSkuBasicInfoDO result = skuSelectorFacade.buildSalesAttrToSkuBasicInfo(skuItemDTO, envCtx);

        assertNotNull("结果不应为 null", result);
        assertEquals("skuId", result.getSkuBasicInfo().getSkuId());
        assertEquals("skuHeadPic", result.getSkuBasicInfo().getSkuHeadPic());
        assertEquals(10, result.getSkuBasicInfo().getStock());
        assertEquals(1, result.getSkuBasicInfo().getAttrList().size());
        assertEquals("attrName", result.getSkuBasicInfo().getAttrList().get(0).getName());
        assertEquals("attrCnName", result.getSkuBasicInfo().getAttrList().get(0).getCnName());
        assertEquals("code", result.getSkuBasicInfo().getAttrList().get(0).getValue());
        assertEquals("id", result.getSkuBasicInfo().getAttrList().get(0).getAttrId());
        assertEquals("skuCode", result.getSalesAttrInfo());
        assertEquals("salesAttrInfoKey", result.getSalesAttrInfoKey());
    }

    /**
     * 测试 buildSkuSalesAttrWithPic 方法，当 skuAttrMetaDTO 为 null 时
     */
    @Test
    public void testBuildSkuSalesAttrWithPicWhenSkuAttrMetaDTOIsNull() {
        SkuSalesAttrWithPicDO result = skuSelectorFacade.buildSkuSalesAttrWithPic(new SkuAttrMetaDTO());
        assertNull(result);
    }

    /**
     * 测试 buildSkuSalesAttrWithPic 方法，当 optionValueList 为空时
     */
    @Test
    public void testBuildSkuSalesAttrWithPicWhenOptionValueListIsEmpty() {
        SkuAttrMetaDTO skuAttrMetaDTO = new SkuAttrMetaDTO();
        skuAttrMetaDTO.setOptionValueList(new ArrayList<>());

        SkuSalesAttrWithPicDO result = skuSelectorFacade.buildSkuSalesAttrWithPic(skuAttrMetaDTO);
        assertNull(result);
    }

    /**
     * 测试 buildSkuSalesAttrWithPic 方法，正常情况
     */
    @Test
    public void testBuildSkuSalesAttrWithPicNormal() {
        SkuAttrMetaDTO skuAttrMetaDTO = new SkuAttrMetaDTO();
        skuAttrMetaDTO.setAttrName("attrName");
        skuAttrMetaDTO.setAttrCnName("attrCnName");
        skuAttrMetaDTO.setAttrDesc("attrDesc");
        skuAttrMetaDTO.setAttrType(1);

        List<AttrValueDTO> optionValueList = new ArrayList<>();
        AttrValueDTO attrValueDTO = new AttrValueDTO();
        attrValueDTO.setCode("code");
        attrValueDTO.setDesc("desc");
        attrValueDTO.setId("id");
        optionValueList.add(attrValueDTO);
        skuAttrMetaDTO.setOptionValueList(optionValueList);

        com.sankuai.dztheme.deal.res.SkuSalesAttrExt skuSalesAttrExt = new com.sankuai.dztheme.deal.res.SkuSalesAttrExt();
        skuSalesAttrExt.setUrl("url");
        skuSalesAttrExt.setDesc("extDesc");
        skuAttrMetaDTO.setSkuSalesAttrExt(skuSalesAttrExt);

        SkuSalesAttrWithPicDO result = skuSelectorFacade.buildSkuSalesAttrWithPic(skuAttrMetaDTO);

        assertNotNull(result);
        assertEquals("attrName", result.getSkuSalesAttrName());
        assertEquals("attrCnName", result.getSkuSalesAttrCnName());
        assertEquals(1, result.getAttrType());
        assertNotNull(result.getSkuSalesAttrValues());
        assertEquals(1, result.getSkuSalesAttrValues().size());
        assertEquals("code", result.getSkuSalesAttrValues().get(0).getCode());
        assertEquals("desc", result.getSkuSalesAttrValues().get(0).getDesc());
        assertEquals("id", result.getSkuSalesAttrValues().get(0).getAttrId());
        assertNotNull(result.getSkuSalesAttrExt());
        assertEquals("url", result.getSkuSalesAttrExt().getUrl());
        assertEquals("extDesc", result.getSkuSalesAttrExt().getDesc());
    }
}
