package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.idmapper.api.dto.IdMapper;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.concurrent.Future;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class DealGroupWrapper_GetDpDealGroupIdTest {

    @Mock
    private Future future;

    @Mock
    private IdMapper idMapper;

    private DealGroupWrapper dealGroupWrapper = new DealGroupWrapper();

    /**
     * 测试 getDpDealGroupId 方法，当 mapperFuture 为 null 时
     */
    @Test
    public void testGetDpDealGroupIdWhenMapperFutureIsNull() throws Throwable {
        // arrange
        Future mapperFuture = null;
        // act
        int result = dealGroupWrapper.getDpDealGroupId(mapperFuture);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试 getDpDealGroupId 方法，当 mapperFuture 不为 null，但 getFutureResult 返回 null 时
     */
    @Test
    public void testGetDpDealGroupIdWhenGetFutureResultReturnsNull() throws Throwable {
        // arrange
        when(future.get()).thenReturn(null);
        // act
        int result = dealGroupWrapper.getDpDealGroupId(future);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试 getDpDealGroupId 方法，当 mapperFuture 不为 null，getFutureResult 返回的 IdMapper 对象不为 null 时
     */
    @Test
    public void testGetDpDealGroupIdWhenGetFutureResultReturnsNonNullIdMapper() throws Throwable {
        // arrange
        when(future.get()).thenReturn(idMapper);
        when(idMapper.getDpDealGroupID()).thenReturn(123);
        // act
        int result = dealGroupWrapper.getDpDealGroupId(future);
        // assert
        assertEquals(123, result);
    }
}
