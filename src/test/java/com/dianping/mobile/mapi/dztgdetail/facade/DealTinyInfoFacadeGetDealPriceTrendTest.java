package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DzDealThemeWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.HaimaWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiClientWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PriceRangeQueryWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.SkuWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetDealTinyInfoRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealPriceTrendVO;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.dztheme.deal.res.DealSkuSummaryDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import java.util.concurrent.Future;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealTinyInfoFacadeGetDealPriceTrendTest {

    @InjectMocks
    private DealTinyInfoFacade dealTinyInfoFacade;

    @Mock
    private DzDealThemeWrapper dzDealThemeWrapper;

    @Mock
    private PriceRangeQueryWrapper priceRangeQueryWrapper;

    @Mock
    private PoiClientWrapper poiClientWrapper;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private SkuWrapper skuWrapper;

    @Mock
    private HaimaWrapper haimaWrapper;

    @Mock
    private DouHuService douHuService;

    private GetDealTinyInfoRequest request;

    private EnvCtx envCtx;

    @Before
    public void setUp() {
        request = new GetDealTinyInfoRequest();
        envCtx = new EnvCtx();
    }

    /**
     * Test invalid request parameters where dealGroupId is null.
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGetDealPriceTrendInvalidDealGroupId() throws Throwable {
        // Arrange
        request.setDealGroupId(null);
        request.setShopId("123");
        request.setPageSource("deal");
        // Act
        dealTinyInfoFacade.getDealPriceTrend(request, envCtx);
        // Assert
        // Expected IllegalArgumentException
    }

    /**
     * Test SKU summary retrieval failure where skuWrapper returns null.
     */
    @Test
    public void testGetDealPriceTrendSkuSummaryRetrievalFailure() throws Throwable {
        // Arrange
        request.setDealGroupId("1");
        request.setShopId("123");
        request.setPageSource("deal");
        // Set client type to MT
        envCtx.setClientType(VCClientTypeEnum.MT_APP.getCode());
        when(skuWrapper.getSkuSummaryByDealId(1, IdTypeEnum.DP)).thenReturn(null);
        // Act
        DealPriceTrendVO result = dealTinyInfoFacade.getDealPriceTrend(request, envCtx);
        // Assert
        assertNull(result);
        verify(skuWrapper).getSkuSummaryByDealId(1, IdTypeEnum.DP);
    }

    /**
     * Test pre-query failure where dzDealThemeWrapper throws an exception.
     */
    @Test(expected = RuntimeException.class)
    public void testGetDealPriceTrendPreQueryFailed() throws Throwable {
        // Arrange
        request.setDealGroupId("1");
        request.setShopId("123");
        request.setPageSource("deal");
        // Set client type to MT
        envCtx.setClientType(VCClientTypeEnum.MT_APP.getCode());
        DealSkuSummaryDTO skuSummary = new DealSkuSummaryDTO();
        skuSummary.setDefaultSkuId(1L);
        when(skuWrapper.getSkuSummaryByDealId(1, IdTypeEnum.DP)).thenReturn(skuSummary);
        when(dzDealThemeWrapper.preQueryDealProduct(any())).thenThrow(new RuntimeException());
        // Act
        dealTinyInfoFacade.getDealPriceTrend(request, envCtx);
        // Assert
        // Expected RuntimeException
    }

    /**
     * Test future result retrieval failure where dzDealThemeWrapper returns null.
     */
    @Test
    public void testGetDealPriceTrendFutureResultRetrievalFailure() throws Throwable {
        // Arrange
        request.setDealGroupId("1");
        request.setShopId("123");
        request.setPageSource("deal");
        // Set client type to MT
        envCtx.setClientType(VCClientTypeEnum.MT_APP.getCode());
        DealSkuSummaryDTO skuSummary = new DealSkuSummaryDTO();
        skuSummary.setDefaultSkuId(1L);
        when(skuWrapper.getSkuSummaryByDealId(1, IdTypeEnum.DP)).thenReturn(skuSummary);
        Future<DealProductResult> future = mock(Future.class);
        when(dzDealThemeWrapper.preQueryDealProduct(any())).thenReturn(future);
        when(dzDealThemeWrapper.getFutureResult(future)).thenReturn(null);
        // Act
        DealPriceTrendVO result = dealTinyInfoFacade.getDealPriceTrend(request, envCtx);
        // Assert
        assertNull(result);
        verify(skuWrapper).getSkuSummaryByDealId(1, IdTypeEnum.DP);
        verify(dzDealThemeWrapper).preQueryDealProduct(any());
        verify(dzDealThemeWrapper).getFutureResult(future);
    }
}
