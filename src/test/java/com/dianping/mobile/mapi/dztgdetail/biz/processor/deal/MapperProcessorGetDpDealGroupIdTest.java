package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import java.lang.reflect.Method;
import java.util.concurrent.Future;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class MapperProcessorGetDpDealGroupIdTest {

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @InjectMocks
    private MapperProcessor mapperProcessor;

    private DealCtx ctx;

    @Before
    public void setUp() {
        EnvCtx envCtx = new EnvCtx();
        ctx = new DealCtx(envCtx);
    }

    private int invokePrivateGetDpDealGroupId(DealCtx ctx) throws Exception {
        Method method = MapperProcessor.class.getDeclaredMethod("getDpDealGroupId", DealCtx.class);
        method.setAccessible(true);
        return (int) method.invoke(mapperProcessor, ctx);
    }

    /**
     * Test case for MT context with successful fetch of DealGroupDTO.
     */
    @Test
    public void testGetDpDealGroupId_MTContext_SuccessfulFetch() throws Throwable {
        // arrange
        // Set MT ID to indicate MT context
        ctx.setMtId(123);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDpDealGroupId(456L);
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(dealGroupDTO);
        // act
        int result = invokePrivateGetDpDealGroupId(ctx);
        // assert
        assertEquals(456, result);
        verify(queryCenterWrapper).getDealGroupDTO(any(QueryByDealGroupIdRequest.class));
    }

    /**
     * Test case for DP context with successful fetch of DealGroupDTO.
     */
    @Test
    public void testGetDpDealGroupId_DPContext_SuccessfulFetch() throws Throwable {
        // arrange
        // Set DP ID to indicate DP context
        ctx.setDpId(789);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDpDealGroupId(101L);
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(dealGroupDTO);
        // act
        int result = invokePrivateGetDpDealGroupId(ctx);
        // assert
        assertEquals(101, result);
        verify(queryCenterWrapper).getDealGroupDTO(any(QueryByDealGroupIdRequest.class));
    }

    /**
     * Test case for DP context with null DealGroupDTO.
     */
    @Test
    public void testGetDpDealGroupId_DPContext_NullDealGroupDTO() throws Throwable {
        // arrange
        // Set DP ID to indicate DP context
        ctx.setDpId(789);
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(null);
        when(dealGroupWrapper.preDpDealGroupId(anyInt())).thenReturn(mock(Future.class));
        when(dealGroupWrapper.getDpDealGroupId(any(Future.class))).thenReturn(202);
        // act
        int result = invokePrivateGetDpDealGroupId(ctx);
        // assert
        assertEquals(202, result);
        verify(queryCenterWrapper).getDealGroupDTO(any(QueryByDealGroupIdRequest.class));
        verify(dealGroupWrapper).preDpDealGroupId(anyInt());
        verify(dealGroupWrapper).getDpDealGroupId(any(Future.class));
    }

    /**
     * Test case for MT context with invalid DealGroupDTO (dpDealGroupIdInt is null).
     */
    @Test
    public void testGetDpDealGroupId_MTContext_InvalidDealGroupDTO() throws Throwable {
        // arrange
        // Set MT ID to indicate MT context
        ctx.setMtId(123);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDpDealGroupId(null);
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(dealGroupDTO);
        when(dealGroupWrapper.preDpDealGroupId(anyInt())).thenReturn(mock(Future.class));
        when(dealGroupWrapper.getDpDealGroupId(any(Future.class))).thenReturn(303);
        // act
        int result = invokePrivateGetDpDealGroupId(ctx);
        // assert
        assertEquals(303, result);
        verify(queryCenterWrapper).getDealGroupDTO(any(QueryByDealGroupIdRequest.class));
        verify(dealGroupWrapper).preDpDealGroupId(anyInt());
        verify(dealGroupWrapper).getDpDealGroupId(any(Future.class));
    }
}
