package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.poi.relation.service.api.PoiRelationService;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.concurrent.Future;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class MapperWrapper_PreMtShopIdByDpShopIdTest {

    @InjectMocks
    private MapperWrapper mapperWrapper;

    @Mock
    private PoiRelationService poiRelationServiceFuture;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试dpShopId小于等于0的情况
     */
    @Test
    public void testPreMtShopIdByDpShopIdLessThanOrEqualToZero() {
        long dpShopId = 0;
        Future result = mapperWrapper.preMtShopIdByDpShopId(dpShopId);
        assertNull(result);
    }

    /**
     * 测试dpShopId大于0且方法调用成功的情况
     */
    @Test
    @Ignore
    public void testPreMtShopIdByDpShopIdGreaterThanZeroAndMethodCallSuccess() throws Exception {
        long dpShopId = 1;
        Future expected = FutureFactory.getFuture();
        when(poiRelationServiceFuture.queryMtByDpIdL(dpShopId)).thenReturn(null);
        Future result = mapperWrapper.preMtShopIdByDpShopId(dpShopId);
        verify(poiRelationServiceFuture, times(1)).queryMtByDpIdL(dpShopId);
        assertSame(expected, result);
    }

    /**
     * 测试dpShopId大于0且方法调用发生异常的情况
     */
    @Test
    public void testPreMtShopIdByDpShopIdGreaterThanZeroAndMethodCallThrowException() throws Exception {
        long dpShopId = 1;
        when(poiRelationServiceFuture.queryMtByDpIdL(dpShopId)).thenThrow(new RuntimeException());
        Future result = mapperWrapper.preMtShopIdByDpShopId(dpShopId);
        assertNull(result);
    }
}
