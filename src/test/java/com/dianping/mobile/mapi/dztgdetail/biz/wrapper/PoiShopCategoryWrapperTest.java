package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Sets;
import com.google.common.util.concurrent.SettableFuture;
import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.service.DpPoiService;
import com.sankuai.sinai.data.api.service.MtPoiService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PoiShopCategoryWrapperTest {

    @InjectMocks
    private PoiShopCategoryWrapper poiShopCategoryWrapper;

    @Mock
    private SettableFuture future;

    @Mock
    private DpPoiDTO dpPoiDTO;

    @Mock
    private DpPoiBackCategoryDTO dpPoiBackCategoryDTO;

    @Mock
    private DpPoiService dpPoiServiceFuture;


    @Mock
    private DpPoiService dpPoiService;

    @Mock
    private MtPoiService mtPoiService;

    @Before
    public void setUp() {
        when(dpPoiBackCategoryDTO.getCategoryId()).thenReturn(1);
    }

    @Test
    public void testQueryPoiShopCategoryIdsFutureIsNull() throws Throwable {
        Set<Integer> result = poiShopCategoryWrapper.queryPoiShopCategoryIds(null);
        assertEquals(new HashSet<>(), result);
    }

    @Test
    public void testQueryPoiShopCategoryIdsDpPoiDTOListIsEmpty() throws Throwable {
        when(future.get()).thenReturn(Lists.newArrayList());
        Set<Integer> result = poiShopCategoryWrapper.queryPoiShopCategoryIds(future);
        assertEquals(new HashSet<>(), result);
    }

    @Test
    public void testQueryPoiShopCategoryIdsBackMainCategoryPathIsEmpty() throws Throwable {
        when(future.get()).thenReturn(Lists.newArrayList(dpPoiDTO));
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(Lists.newArrayList());
        Set<Integer> result = poiShopCategoryWrapper.queryPoiShopCategoryIds(future);
        assertEquals(new HashSet<>(), result);
    }

    @Test
    public void testQueryPoiShopCategoryIdsBackMainCategoryPathIsNotEmpty() throws Throwable {
        when(future.get()).thenReturn(Lists.newArrayList(dpPoiDTO));
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(Lists.newArrayList(dpPoiBackCategoryDTO));
        Set<Integer> result = poiShopCategoryWrapper.queryPoiShopCategoryIds(future);
        Set<Integer> expected = Sets.newHashSet(1);
        assertEquals(expected, result);
    }

    @Test
    public void testPreDpPoiDtoNormal() throws Throwable {
        // arrange
        long dpShopId = 1L;
        List<String> fields = Arrays.asList("backMainCategoryPath");
        SettableFuture future = SettableFuture.create();
        // Assuming ContextStore.getSettableFuture() is correctly mocked elsewhere or refactored out
        // act
        SettableFuture result = poiShopCategoryWrapper.preDpPoiDto(dpShopId, fields);
        // assert
        verify(dpPoiServiceFuture, times(1)).findShopsByShopIds(any(DpPoiRequest.class));
        // Assuming the result is correctly asserted elsewhere or the design is refactored
    }

    @Test
    public void testPreDpPoiDtoException() throws Throwable {
        // arrange
        long dpShopId = 1L;
        List<String> fields = Arrays.asList("backMainCategoryPath");
        doThrow(new RuntimeException()).when(dpPoiServiceFuture).findShopsByShopIds(any(DpPoiRequest.class));
        // act
        SettableFuture result = poiShopCategoryWrapper.preDpPoiDto(dpShopId, fields);
        // assert
        verify(dpPoiServiceFuture, times(1)).findShopsByShopIds(any(DpPoiRequest.class));
        assertNull(result);
    }

    /**
     * 测试 queryShopCategoryIds 方法，当 shopId 为 null 时
     */
    @Test
    public void testQueryShopCategoryIdsWithNullShopId() {
        // arrange
        Long shopId = null;
        boolean isMt = true;

        // act
        Set<Integer> result = poiShopCategoryWrapper.queryShopCategoryIds(shopId, isMt);

        // assert
        assertEquals("结果应为空集合", Collections.emptySet(), result);
    }

    /**
     * 测试 queryShopCategoryIds 方法，当 shopId 小于等于 0 时
     */
    @Test
    public void testQueryShopCategoryIdsWithInvalidShopId() {
        // arrange
        Long shopId = 0L;
        boolean isMt = false;

        // act
        Set<Integer> result = poiShopCategoryWrapper.queryShopCategoryIds(shopId, isMt);

        // assert
        assertEquals("结果应为空集合", Collections.emptySet(), result);
    }

    /**
     * 测试 queryShopCategoryIds 方法，当 isMt 为 true 且 MtPoiService 返回有效数据时
     */
    @Test
    public void testQueryShopCategoryIdsWithValidMtData() throws Exception {
        // arrange
        Long shopId = 1L;
        boolean isMt = true;
        MtPoiDTO mtPoiDTO = new MtPoiDTO();
        DpPoiBackCategoryDTO dpPoiBackCategoryDTO = new DpPoiBackCategoryDTO();
        dpPoiBackCategoryDTO.setCategoryId(100);
        mtPoiDTO.setDpBackCategoryList(Collections.singletonList(dpPoiBackCategoryDTO));
        when(mtPoiService.findPoisById(any(), any())).thenReturn(Collections.singletonMap(shopId, mtPoiDTO));

        // act
        Set<Integer> result = poiShopCategoryWrapper.queryShopCategoryIds(shopId, isMt);

        // assert
        assertEquals("结果应包含一个类目ID", new HashSet<>(Collections.singletonList(100)), result);
    }

    /**
     * 测试 queryShopCategoryIds 方法，当 isMt 为 false 且 DpPoiService 返回有效数据时
     */
    @Test
    public void testQueryShopCategoryIdsWithValidDpData() throws Exception {
        // arrange
        Long shopId = 1L;
        boolean isMt = false;
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        DpPoiBackCategoryDTO dpPoiBackCategoryDTO = new DpPoiBackCategoryDTO();
        dpPoiBackCategoryDTO.setCategoryId(200);
        dpPoiDTO.setBackMainCategoryPath(Collections.singletonList(dpPoiBackCategoryDTO));
        when(dpPoiService.findShopsByShopIds(any())).thenReturn(Collections.singletonList(dpPoiDTO));

        // act
        Set<Integer> result = poiShopCategoryWrapper.queryShopCategoryIds(shopId, isMt);

        // assert
        assertEquals("结果应包含一个类目ID", new HashSet<>(Collections.singletonList(200)), result);
    }

    /**
     * 测试 queryShopCategoryIds 方法，当 isMt 为 true 但 MtPoiService 抛出异常时
     */
    @Test
    public void testQueryShopCategoryIdsWithMtServiceException() throws Exception {
        // arrange
        Long shopId = 1L;
        boolean isMt = true;
        when(mtPoiService.findPoisById(any(), any())).thenThrow(new RuntimeException("服务异常"));

        // act
        Set<Integer> result = poiShopCategoryWrapper.queryShopCategoryIds(shopId, isMt);

        // assert
        assertEquals("结果应为空集合", Collections.emptySet(), result);
    }

    /**
     * 测试 queryShopCategoryIds 方法，当 isMt 为 false 但 DpPoiService 抛出异常时
     */
    @Test
    public void testQueryShopCategoryIdsWithDpServiceException() throws Exception {
        // arrange
        Long shopId = 1L;
        boolean isMt = false;
        when(dpPoiService.findShopsByShopIds(any())).thenThrow(new RuntimeException("服务异常"));

        // act
        Set<Integer> result = poiShopCategoryWrapper.queryShopCategoryIds(shopId, isMt);

        // assert
        assertEquals("结果应为空集合", Collections.emptySet(), result);
    }
}
