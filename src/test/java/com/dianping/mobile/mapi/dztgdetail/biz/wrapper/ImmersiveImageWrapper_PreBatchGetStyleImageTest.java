package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.mobile.mapi.dztgdetail.entity.ExhibitImageConfig;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.sankuai.mpmctcontent.query.thrift.api.ContentFusion2CService;
import com.sankuai.mpmctcontent.query.thrift.dto.aggre.SearchFusionInfoReqDTO;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Arrays;
import java.util.concurrent.Future;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import org.junit.runner.RunWith;
import static org.junit.Assert.*;
import org.junit.*;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ImmersiveImageWrapper_PreBatchGetStyleImageTest {

    @InjectMocks
    private ImmersiveImageWrapper immersiveImageWrapper;

    @Mock
    private ContentFusion2CService contentFusion2CService;

    @Mock
    private LionConfigUtils lionConfigUtils;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试款式id列表为空的情况
     */
    @Test
    public void testPreBatchGetStyleImageWithEmptyItemIds() throws Throwable {
        // arrange
        Future result = immersiveImageWrapper.preBatchGetStyleImage(null, "contentId", 1);
        // assert
        assertNull(result);
    }
}
