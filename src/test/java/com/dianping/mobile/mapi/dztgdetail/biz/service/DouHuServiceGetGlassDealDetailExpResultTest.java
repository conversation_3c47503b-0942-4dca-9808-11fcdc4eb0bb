package com.dianping.mobile.mapi.dztgdetail.biz.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DouHuServiceGetGlassDealDetailExpResultTest {

    @InjectMocks
    private DouHuService douHuService;

    @Mock
    private DouHuBiz douHuBiz;

    private EnvCtx envCtx;

    private int cityId;

    @Before
    public void setUp() {
        envCtx = new EnvCtx();
        cityId = 1;
    }

    /**
     * 测试 getGlassDealDetailExpResult 方法，正常情况
     */
    @Test
    public void testGetGlassDealDetailExpResultNormal() throws Throwable {
        // arrange
        envCtx.setUuid("1");
        envCtx.setDztgClientTypeEnum(com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum.MEITUAN_APP);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setConfigs(java.util.Collections.singletonList(new com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig()));
        when(douHuBiz.getAbByCityIdAndUuidAndDpId(cityId, new DealCtx(envCtx), "MtGlassDealDetailExp")).thenReturn(moduleAbConfig);
        when(douHuBiz.getExpResult(moduleAbConfig)).thenReturn("expResult");
        // act
        String result = douHuService.getGlassDealDetailExpResult(cityId, envCtx);
        // assert
        assertEquals("expResult", result);
    }

    /**
     * 测试 getGlassDealDetailExpResult 方法，异常情况
     */
    @Test
    public void testGetGlassDealDetailExpResultException() throws Throwable {
        // arrange
        envCtx.setUuid("1");
        envCtx.setDztgClientTypeEnum(com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum.MEITUAN_APP);
        when(douHuBiz.getAbByCityIdAndUuidAndDpId(cityId, new DealCtx(envCtx), "MtGlassDealDetailExp")).thenReturn(null);
        // act
        String result = douHuService.getGlassDealDetailExpResult(cityId, envCtx);
        // assert
        assertNull(result);
    }
}
