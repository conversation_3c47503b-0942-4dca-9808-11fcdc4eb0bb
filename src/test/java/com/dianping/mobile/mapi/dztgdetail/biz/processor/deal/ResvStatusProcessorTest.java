package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.sankuai.clr.content.process.thrift.api.ResvQueryService;
import com.sankuai.clr.content.process.thrift.dto.req.QueryStockReqDTO;
import com.sankuai.clr.content.process.thrift.dto.resp.QueryStockRespDTO;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ResvStatusProcessorTest {

    @InjectMocks
    private ResvStatusProcessor resvStatusProcessor;

    @Mock
    private ResvQueryService resvQueryService;

    @Mock
    private DealCtx ctx;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test the normal scenario where the stockItemStatus is 1.
     */
    @Test
    public void testProcessStockItemStatusIs1() throws Throwable {
        // arrange
        QueryStockRespDTO respDTO = new QueryStockRespDTO();
        respDTO.setStockItemStatus(1);
        when(resvQueryService.queryStock(any(QueryStockReqDTO.class))).thenReturn(respDTO);
        // act
        resvStatusProcessor.process(ctx);
        // assert
        verify(ctx).setIsCanResv(true);
    }

    /**
     * Test the normal scenario where the stockItemStatus is 2.
     */
    @Test
    public void testProcessStockItemStatusIs2() throws Throwable {
        // arrange
        QueryStockRespDTO respDTO = new QueryStockRespDTO();
        respDTO.setStockItemStatus(2);
        when(resvQueryService.queryStock(any(QueryStockReqDTO.class))).thenReturn(respDTO);
        // act
        resvStatusProcessor.process(ctx);
        // assert
        verify(ctx).setIsCanResv(false);
    }

    /**
     * Test the normal scenario where the stockItemStatus is null.
     */
    @Test
    public void testProcessStockItemStatusIsNull() throws Throwable {
        // arrange
        QueryStockRespDTO respDTO = new QueryStockRespDTO();
        respDTO.setStockItemStatus(null);
        when(resvQueryService.queryStock(any(QueryStockReqDTO.class))).thenReturn(respDTO);
        // act
        resvStatusProcessor.process(ctx);
        // assert
        verify(ctx).setIsCanResv(false);
    }

    /**
     * Test the boundary scenario where the response is null.
     */
    @Test
    public void testProcessResponseIsNull() throws Throwable {
        // arrange
        when(resvQueryService.queryStock(any(QueryStockReqDTO.class))).thenReturn(null);
        // act
        resvStatusProcessor.process(ctx);
        // assert
        verify(ctx).setIsCanResv(false);
    }

    /**
     * Test the exception scenario where a TException is thrown.
     */
    @Test
    public void testProcessThrowsTException() throws Throwable {
        // arrange
        when(resvQueryService.queryStock(any(QueryStockReqDTO.class))).thenThrow(new TException("Mock TException"));
        // act
        resvStatusProcessor.process(ctx);
        // assert
        verify(ctx).setIsCanResv(false);
        // No need to verify Cat.logError as we are not validating logs
    }
}
