package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.button.BuilderChainConfig;
import com.dianping.mobile.mapi.dztgdetail.button.BuilderConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 测试 NewBuyBarHelper 的 build 方法
 */
@RunWith(MockitoJUnitRunner.class)
public class NewBuyBarHelperTest2 {

    @Mock
    private DealCtx mockDealCtx;

    private MockedStatic<Lion> lionMockedStatic;

    @Before
    public void setUp() {
        lionMockedStatic = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        lionMockedStatic.close();
    }

    /**
     * 测试 buildLogMetricForBuyBarTag 方法，当 categoryStrList 为空时，不应该添加任何标签。
     */
    @Test
    public void testBuildLogMetricForBuyBarTag_WithEmptyCategoryStrList() throws Throwable {
        // arrange
        lionMockedStatic.when(()->Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb.bottom.bar.log.category.config", String.class)).thenReturn(null);
        Map<String, String> tags = new HashMap<>();
        // act
        NewBuyBarHelper.buildLogMetricForBuyBarTag(mockDealCtx, tags);
        // assert
        assertTrue("Tags map should be empty when categoryStrList is empty", tags.isEmpty());
    }

    /**
     * 测试 buildLogMetricForBuyBarTag 方法，当 categoryStrList 不为空，但 ctx 中的 DealGroupDTO 为 null 时，不应该添加任何标签。
     */
    @Test
    public void testBuildLogMetricForBuyBarTag_WithNonNullCategoryStrListAndNullDealGroupDTO() throws Throwable {
        // arrange
        List<String> categoryStrList = Lists.newArrayList("1:2");
        lionMockedStatic.when(()->Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb.bottom.bar.log.category.config", String.class)).thenReturn(categoryStrList);
        when(mockDealCtx.getDealGroupDTO()).thenReturn(null);
        Map<String, String> tags = new HashMap<>();
        // act
        NewBuyBarHelper.buildLogMetricForBuyBarTag(mockDealCtx, tags);;
        // assert
        assertTrue("Tags map should be empty when DealGroupDTO is null", tags.isEmpty());
    }

    /**
     * 测试 buildLogMetricForBuyBarTag 方法，当 categoryStrList 包含 ctx 中的 DealGroupDTO 的 category 时，应该添加相应的标签。
     */
    @Test
    public void testBuildLogMetricForBuyBarTag_WithMatchingCategory() throws Throwable {
        // arrange
        List<String> categoryStrList = Lists.newArrayList("1:2");
        lionMockedStatic.when(()->Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb.bottom.bar.log.category.config", String.class)).thenReturn(categoryStrList);
        DealGroupDTO mockDealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(1L);
        categoryDTO.setServiceTypeId(2L);
        when(mockDealGroupDTO.getCategory()).thenReturn(categoryDTO);
        when(mockDealCtx.getDealGroupDTO()).thenReturn(mockDealGroupDTO);
        Map<String, String> tags = new HashMap<>();
        // act
        NewBuyBarHelper.buildLogMetricForBuyBarTag(mockDealCtx, tags);
        // assert
        assertEquals("1:2", tags.get("category"));
    }

    // 更多测试用例可以按照上述模式继续添加，以覆盖所有可能的场景和分支

    /**
     * 测试 buildLogMetricForBuyBarTag 方法，当 categoryStrList 不包含 ctx 中的 DealGroupDTO 的 category 时，不应该添加任何标签。
     */
    @Test
    public void testBuildLogMetricForBuyBarTag_WithNonMatchingCategory() throws Throwable {
        // arrange
        List<String> categoryStrList = Lists.newArrayList("3:4");
        lionMockedStatic.when(()->Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb.bottom.bar.log.category.config", String.class)).thenReturn(categoryStrList);
        DealGroupDTO mockDealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(1L);
        categoryDTO.setPlatformCategoryId(2L);
        when(mockDealGroupDTO.getCategory()).thenReturn(categoryDTO);
        when(mockDealCtx.getDealGroupDTO()).thenReturn(mockDealGroupDTO);
        Map<String, String> tags = new HashMap<>();
        // act
        NewBuyBarHelper.buildLogMetricForBuyBarTag(mockDealCtx, tags);
        // assert
        assertTrue("Tags map should be empty when category does not match", tags.isEmpty());
    }
}
