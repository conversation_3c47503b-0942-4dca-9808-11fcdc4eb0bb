package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.pay.promo.display.api.PromoDisplayService;
import com.dianping.pay.promo.display.api.dto.QueryPromoDisplayRequest;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.pay.promo.rule.api.dto.Response;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PromoWrapper_GetPromoDisplayDTOTest {

    @InjectMocks
    private PromoWrapper promoWrapper;

    @Mock
    private PromoDisplayService promoDisplayServiceFuture;

    // Simulated response codes
    private static final int SUCCESS_CODE = 0;

    private static final int FAIL_CODE = 1;

    @Test
    public void testGetPromoDisplayDTOWhenPrePromoDisplayDTOThrowsException() throws Throwable {
        QueryPromoDisplayRequest request = new QueryPromoDisplayRequest();
        when(promoDisplayServiceFuture.queryPromoDisplayDTO(request)).thenThrow(new RuntimeException());
        assertNull(promoWrapper.getPromoDisplayDTO(request));
    }
}
