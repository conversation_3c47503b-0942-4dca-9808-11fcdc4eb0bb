package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealAttrHelper_IsSupportShopServiceTest {

    private static final String SUPPORT_SHOP_SERVICE = "supportShopService";

    @Test
    public void testIsSupportShopServiceWhenAttrsIsNull() throws Throwable {
        boolean result = DealAttrHelper.isSupportShopService(null);
        assertFalse(result);
    }

    @Test
    public void testIsSupportShopServiceWhenAttrsHasNoSupportShopService() throws Throwable {
        AttributeDTO attr = new AttributeDTO();
        attr.setName("other");
        attr.setValue(Collections.singletonList("no"));
        boolean result = DealAttrHelper.isSupportShopService(Arrays.asList(attr));
        assertFalse(result);
    }

    @Test
    public void testIsSupportShopServiceWhenSupportShopServiceValueIsEmpty() throws Throwable {
        AttributeDTO attr = new AttributeDTO();
        attr.setName(SUPPORT_SHOP_SERVICE);
        attr.setValue(Collections.emptyList());
        boolean result = DealAttrHelper.isSupportShopService(Arrays.asList(attr));
        assertFalse(result);
    }

    @Test
    public void testIsSupportShopServiceWhenSupportShopServiceValueIsNotYes() throws Throwable {
        AttributeDTO attr = new AttributeDTO();
        attr.setName(SUPPORT_SHOP_SERVICE);
        attr.setValue(Collections.singletonList("no"));
        boolean result = DealAttrHelper.isSupportShopService(Arrays.asList(attr));
        assertFalse(result);
    }

    /**
     * 测试 isRepairPrepayDeal 方法，当 dealGroupDTO 为 null 时
     */
    @Test
    public void testIsRepairPrepayDeal_NullDealGroupDTO() throws Throwable {
        assertFalse(DealAttrHelper.isRepairPrepayDeal(null));
    }

    /**
     * 测试 isRepairPrepayDeal 方法，当 dealGroupDTO 不为 null，但 attrs 为 null 时
     */
    @Test
    public void testIsRepairPrepayDeal_NullAttrs() throws Throwable {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        assertFalse(DealAttrHelper.isRepairPrepayDeal(dealGroupDTO));
    }

    /**
     * 测试 isRepairPrepayDeal 方法，当 dealGroupDTO 不为 null，attrs 不为 null，但 attrs 中没有 null 属性时
     */
    @Test
    public void testIsRepairPrepayDeal_NoNullAttrs() throws Throwable {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        assertFalse(DealAttrHelper.isRepairPrepayDeal(dealGroupDTO));
    }

    /**
     * 测试 isRepairPrepayDeal 方法，当 dealGroupDTO 不为 null，attrs 不为 null，attrs 中有 null 属性时
     */
    @Test
    public void testIsRepairPrepayDeal_HasNullAttr() throws Throwable {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setAttrs(null);
        assertFalse(DealAttrHelper.isRepairPrepayDeal(dealGroupDTO));
    }

    /**
     * 测试 isRepairPrepayDeal 方法，当 dealGroupDTO 不为 null，attrs 不为 null，attrs 中没有 null 属性，isRepairPrepayDeal 方法返回 true 时
     */
    @Test
    public void testIsRepairPrepayDeal_True() throws Throwable {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<AttrDTO> attrs = new ArrayList<>(Arrays.asList(new AttrDTO(), new AttrDTO()));
        // Set up the attribute to match the criteria
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("computer_project_type");
        attrDTO.setValue(Arrays.asList("上门费"));
        attrs.add(attrDTO);
        dealGroupDTO.setAttrs(attrs);
        assertTrue(DealAttrHelper.isRepairPrepayDeal(dealGroupDTO));
    }

    /**
     * 测试 isRepairPrepayDeal 方法，当 dealGroupDTO 不为 null，attrs 不为 null，attrs 中没有 null 属性，isRepairPrepayDeal 方法返回 false 时
     */
    @Test
    public void testIsRepairPrepayDeal_False() throws Throwable {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<AttrDTO> attrs = new ArrayList<>(Arrays.asList(new AttrDTO(), new AttrDTO()));
        dealGroupDTO.setAttrs(attrs);
        assertFalse(DealAttrHelper.isRepairPrepayDeal(dealGroupDTO));
    }
}
