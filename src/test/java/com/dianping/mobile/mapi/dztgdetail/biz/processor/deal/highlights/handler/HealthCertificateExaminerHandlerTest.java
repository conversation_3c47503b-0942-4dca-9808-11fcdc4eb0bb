package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.highlights.DztgHighlightsModule;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class HealthCertificateExaminerHandlerTest {

    private HealthCertificateExaminerHandler handler;

    private List<AttrDTO> attrs;

    @InjectMocks
    private HealthCertificateExaminerHandler healthCertificateExaminerHandler;

    @Mock
    private DztgHighlightsModule module;

    @Before
    public void setUp() {
        handler = new HealthCertificateExaminerHandler();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("physical_examination_report_type");
        attrDTO.setValue(Arrays.asList("报告类型1", "报告类型2"));
        AttrDTO attrDTO1 = new AttrDTO();
        attrDTO1.setName("include_physical_cards");
        attrDTO1.setValue(Collections.singletonList("是"));
        AttrDTO attrDTO2 = new AttrDTO();
        attrDTO2.setName("include_a_training_certificate");
        attrDTO2.setValue(Collections.singletonList("是"));
        attrs = Arrays.asList(attrDTO, attrDTO1, attrDTO2);
    }

    private void invokeBuildCertificateType(List<AttrDTO> attrs) throws Exception {
        Method method = HealthCertificateExaminerHandler.class.getDeclaredMethod("buildCertificateType", List.class, DztgHighlightsModule.class);
        method.setAccessible(true);
        method.invoke(healthCertificateExaminerHandler, attrs, module);
    }

    @Test
    public void testBuildCertificateDesc_AllAttrs() {
        List<String> result = handler.buildCertificateDesc(attrs);
        assertEquals(Arrays.asList("报告类型1", "报告类型2", "实体卡", "培训证"), result);
    }

    @Test
    public void testBuildCertificateDesc_NoAttrs() {
        attrs = Collections.emptyList();
        List<String> result = handler.buildCertificateDesc(attrs);
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试 buildCertificateType 方法，当 attrs 列表为空时
     */
    @Test
    public void testBuildCertificateTypeWhenAttrsIsEmpty() throws Throwable {
        // arrange
        List<AttrDTO> attrs = new ArrayList<>();
        // act
        invokeBuildCertificateType(attrs);
        // assert
        verify(module, times(1)).getAttrs();
    }

    /**
     * 测试 buildCertificateType 方法，当 attrs 列表不为空，但 buildCertificateDesc 返回的列表为空时
     */
    @Test
    public void testBuildCertificateTypeWhenAttrsIsNotEmptyButBuildCertificateDescReturnsEmpty() throws Throwable {
        // arrange
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("physical_examination_report_type");
        attrs.add(attrDTO);
        // act
        invokeBuildCertificateType(attrs);
        // assert
        verify(module, times(1)).getAttrs();
    }

    /**
     * 测试 buildCertificateType 方法，当 attrs 列表不为空，且 buildCertificateDesc 返回的列表也不为空时
     */
    @Test
    public void testBuildCertificateTypeWhenAttrsIsNotEmptyAndBuildCertificateDescReturnsNotEmpty() throws Throwable {
        // arrange
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("physical_examination_report_type");
        // Ensure the list is modifiable
        attrDTO.setValue(new ArrayList<>(java.util.Arrays.asList("电子报告")));
        attrs.add(attrDTO);
        // act
        invokeBuildCertificateType(attrs);
        // assert
        verify(module, times(1)).getAttrs();
    }

    /**
     * 测试 newDztgHighlightsModel 方法
     */
    @Test
    public void testNewDztgHighlightsModel() throws Throwable {
        // arrange
        HealthCertificateExaminerHandler handler = new HealthCertificateExaminerHandler();
        // act
        DztgHighlightsModule module = handler.newDztgHighlightsModel();
        // assert
        assertNotNull(module);
        assertEquals("struct", module.getStyle());
        assertNotNull(module.getAttrs());
        assertEquals(0, module.getAttrs().size());
    }
}
