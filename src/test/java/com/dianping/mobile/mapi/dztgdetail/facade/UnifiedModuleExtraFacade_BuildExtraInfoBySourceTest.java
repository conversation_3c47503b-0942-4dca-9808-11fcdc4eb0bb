package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedModuleExtraReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedModuleExtraFacade_BuildExtraInfoBySourceTest {

    @InjectMocks
    private UnifiedModuleExtraFacade unifiedModuleExtraFacade;

    private UnifiedModuleExtraReq request;

    private Map<String, ModuleConfigDo> pagesourceMapKeyValueMap;

    @Before
    public void setUp() {
        request = new UnifiedModuleExtraReq();
        pagesourceMapKeyValueMap = Collections.singletonMap("create_order_preview", new ModuleConfigDo());
    }

    @Test
    public void testBuildExtraInfoBySourceRequestIsNull() throws Throwable {
        List<ModuleConfigDo> result = unifiedModuleExtraFacade.buildExtraInfoBySource(null);
        assertNull(result);
    }

    @Test
    public void testBuildExtraInfoBySourceNotFromCreateOrderPreview() throws Throwable {
        request.setPageSource("not_create_order_preview");
        List<ModuleConfigDo> result = unifiedModuleExtraFacade.buildExtraInfoBySource(request);
        assertNull(result);
    }

    @Test
    public void testBuildExtraInfoBySourceFromCreateOrderPreview() throws Throwable {
        request.setPageSource("create_order_preview");
        try (MockedStatic<LionConfigUtils> mockedStatic = mockStatic(LionConfigUtils.class)) {
            mockedStatic.when(LionConfigUtils::getPagesourceMapKeyValueMap).thenReturn(pagesourceMapKeyValueMap);
            List<ModuleConfigDo> result = unifiedModuleExtraFacade.buildExtraInfoBySource(request);
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(pagesourceMapKeyValueMap.get(request.getPageSource()), result.get(0));
        }
    }
}
