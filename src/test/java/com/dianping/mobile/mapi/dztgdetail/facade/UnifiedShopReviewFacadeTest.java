package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.review.professional.Star;
import com.dianping.userremote.base.dto.UserDTO;
import com.dianping.cip.growth.mana.api.dto.response.UserManaDTO;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DetailTagService;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ReviewWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.UserWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.ShopReviewCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedShopReviewReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.ReviewDetailDO;
import com.dianping.review.professional.ReviewDataV2;
import com.dianping.ugc.proxyService.remote.dto.AnonymousUserInfo;
import com.dianping.vipremote.vo.UserInfoForAppVO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import com.dianping.core.type.PageModel;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.Future;

import static org.junit.Assert.*;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedShopReviewFacadeTest {

    @InjectMocks
    private UnifiedShopReviewFacade unifiedShopReviewFacade;

    @Mock
    private DouHuService douHuService;

    @Mock
    private DetailTagService detailTagService;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Mock
    private UserWrapper userWrapper;

    @Mock
    private ReviewWrapper reviewWrapper;

    @Before
    public void setUp() {
    }

    /**
     * 测试getDisplayCount方法，当enableCardStyleV2返回false时
     */
    @Test
    public void testGetDisplayCountWhenEnableCardStyleV2ReturnsFalse() {
        UnifiedShopReviewReq request = mock(UnifiedShopReviewReq.class);
        EnvCtx envCtx = mock(EnvCtx.class);
        IMobileContext iMobileContext = mock(IMobileContext.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);

//        when(douHuService.enableCardStyleV2(any(EnvCtx.class), anyInt(), anyString())).thenReturn(new ModuleAbConfig());

        int result = unifiedShopReviewFacade.getDisplayCount(request, envCtx, iMobileContext, dealGroupDTO);

        assertEquals("Expected TWO_SHOP_REVIEW but got different value", 2, result);
    }

    /**
     * 测试getDisplayCount方法，当enableCardStyleV2返回true且reviewIsTopTab返回true时
     */
    @Test
    public void testGetDisplayCountWhenEnableCardStyleV2ReturnsTrueAndReviewIsTopTabReturnsTrue() {
        UnifiedShopReviewReq request = mock(UnifiedShopReviewReq.class);
        EnvCtx envCtx = mock(EnvCtx.class);
        IMobileContext iMobileContext = mock(IMobileContext.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO dealGroupCategoryDTO = mock(DealGroupCategoryDTO.class);
        dealGroupCategoryDTO.setCategoryId(502L);
        dealGroupCategoryDTO.setServiceType("穿戴甲");
        dealGroupCategoryDTO.setServiceTypeId(111L);
        when(dealGroupDTO.getCategory()).thenReturn(dealGroupCategoryDTO);
        ModuleAbConfig moduleAbConfig = mock(ModuleAbConfig.class);
//        when(douHuService.enableCardStyleV2(any(EnvCtx.class), anyInt(), anyString())).thenReturn(moduleAbConfig);
//        when(douHuService.hitEnableCardStyleV2(moduleAbConfig)).thenReturn(true);
//        when(detailTagService.reviewIsTopTab(any(), any(EnvCtx.class), any(IMobileContext.class))).thenReturn(true);

        int result = unifiedShopReviewFacade.getDisplayCount(request, envCtx, iMobileContext, dealGroupDTO);

        assertEquals("Expected ONE_SHOP_REVIEW but got different value", 2, result);
    }

    /**
     * 测试 getDealGroupDTO 方法，当 QueryCenterWrapper 返回正常结果时
     */
    @Test
    public void testGetDealGroupDTOSuccess() throws Throwable {
        // arrange
        UnifiedShopReviewReq request = new UnifiedShopReviewReq();
        EnvCtx envCtx = new EnvCtx();
        DealGroupDTO expected = new DealGroupDTO();
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(expected);

        // act
        DealGroupDTO result = unifiedShopReviewFacade.getDealGroupDTO(request, envCtx);

        // assert
        assertEquals(expected, result);
    }

    /**
     * 测试 getDealGroupDTO 方法，当 QueryCenterWrapper 抛出 TException 异常时
     */
    @Test
    public void testGetDealGroupDTOThrowsTException() throws Throwable {
        // arrange
        UnifiedShopReviewReq request = new UnifiedShopReviewReq();
        EnvCtx envCtx = new EnvCtx();
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenThrow(new TException());

        // act
        DealGroupDTO result = unifiedShopReviewFacade.getDealGroupDTO(request, envCtx);

        // assert
        assertNull(result);
    }

    @Test
    public void testBuildDpReviewDOListWithNullPageModel() {
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx(new EnvCtx());
        PageModel pageModel = null;
        boolean supply = false;

        List<ReviewDetailDO> result = unifiedShopReviewFacade.buildDpReviewDOList(shopReviewCtx, pageModel, supply);

        assertTrue(result.isEmpty());
    }

    /**
     * 测试 buildDpReviewDOList 方法，当 pageModel.records 为空时，应返回空列表
     */
    @Test
    public void testBuildDpReviewDOListWithEmptyRecords() throws Exception {
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx(new EnvCtx());
        PageModel pageModel = new PageModel();
        pageModel.setRecords(Collections.emptyList());
        boolean supply = false;

        List<ReviewDetailDO> result = unifiedShopReviewFacade.buildDpReviewDOList(shopReviewCtx, pageModel, supply);

        assertTrue(result.isEmpty());
    }

    /**
     * 测试 buildDpReviewDOList 方法，当存在匿名用户信息时，应正确处理匿名用户信息
     */
    @Test
    public void testBuildDpReviewDOListWithAnonymousUserInfo() throws Exception {
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx(new EnvCtx());
        PageModel pageModel = new PageModel();
        List<ReviewDataV2> reviewDataV2List = new ArrayList<>();
        ReviewDataV2 reviewDataV2 = new ReviewDataV2();
        reviewDataV2.setReviewIdLong(1L);
        reviewDataV2.setShopId(1L);
        reviewDataV2.setUserId(1L);
        reviewDataV2.setAnonymous(true);
        reviewDataV2.setStar(new Star());
        reviewDataV2.setFlowerTotal(1);
        reviewDataV2.setFollowNoteNo(1);
        reviewDataV2List.add(reviewDataV2);
        pageModel.setRecords(reviewDataV2List);

        boolean supply = false;

        // Mock 数据准备
        Future anonymousUserInfoFuture = mock(Future.class);
        when(reviewWrapper.getAnonymousUserInfoFuture(any())).thenReturn(anonymousUserInfoFuture);
        when(reviewWrapper.getFutureResult(anonymousUserInfoFuture)).thenReturn(Collections.singletonMap(1L, new AnonymousUserInfo()));

        Future reviewBrowsCountsFuture = mock(Future.class);
        when(reviewWrapper.getReviewsBrowseCountFutureV2(any())).thenReturn(reviewBrowsCountsFuture);
        when(reviewWrapper.getFutureResult(reviewBrowsCountsFuture)).thenReturn(Collections.singletonMap(1L, 1));

        Future userFuture = mock(Future.class);
        when(userWrapper.getUserInfos(any())).thenReturn(userFuture);
        when(userWrapper.getFutureResult(userFuture)).thenReturn(Collections.singletonMap(1L, new UserDTO()));

        Future vipFuture = mock(Future.class);
        when(reviewWrapper.getUserVipInfoFuture(any())).thenReturn(vipFuture);
        when(reviewWrapper.getFutureResult(vipFuture)).thenReturn(Collections.singletonMap(1L, new UserInfoForAppVO()));

        Future userGrowthFuture = mock(Future.class);
        when(userWrapper.batchQueryUserGrowth(any())).thenReturn(userGrowthFuture);
        when(userWrapper.getFutureResult(userGrowthFuture)).thenReturn(Collections.singletonMap(1L, new UserManaDTO()));

        // 执行测试方法
        List<ReviewDetailDO> result = unifiedShopReviewFacade.buildDpReviewDOList(shopReviewCtx, pageModel, supply);

        // 验证结果
        assertNotNull(result);
        // 更多的断言可以根据实际情况添加，这里只是示例
    }
}
