package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.mobile.mapi.dztgdetail.biz.immersiveimage.ImmersiveImageService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ExhibitImageSceneEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageVO;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ImmersiveImageFacadeTest {

    @InjectMocks
    private ImmersiveImageFacade immersiveImageFacade;

    @Mock
    private ImmersiveImageService immersiveImageService;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private MapperWrapper mapperWrapper;

    private GetImmersiveImageRequest createValidRequest() {
        GetImmersiveImageRequest request = new GetImmersiveImageRequest();
        request.setDealGroupId(1);
        request.setLimit(10);
        request.setStart(0);
        request.setShopId(1L);
        request.setSceneCode(ExhibitImageSceneEnum.SELF.getSceneCode());
        return request;
    }

    private void mockDependenciesForValidRequest(GetImmersiveImageRequest request, EnvCtx envCtx) {
        when(dealGroupWrapper.getCategoryId(anyInt())).thenReturn(1);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetImmersiveImageInvalidRequest() throws Throwable {
        GetImmersiveImageRequest request = new GetImmersiveImageRequest();
        EnvCtx envCtx = new EnvCtx();
        immersiveImageFacade.getImmersiveImage(request, envCtx);
    }

    @Test
    public void testGetImmersiveImageEmptySceneCode() throws Throwable {
        GetImmersiveImageRequest request = createValidRequest();
        request.setSceneCode(null);
        EnvCtx envCtx = new EnvCtx();
        mockDependenciesForValidRequest(request, envCtx);
        request.setMtDealGroupId(1L);
        Mockito.when(mapperWrapper.getMtIdByDpIdMtIdMapper(any())).thenReturn(1);
        try (MockedStatic<LionConfigUtils> mockedStatic = mockStatic(LionConfigUtils.class)) {
            mockedStatic.when(() -> LionConfigUtils.getImmersiveImageSceneSwitch(anyString())).thenReturn(false);
            ImmersiveImageVO result = immersiveImageFacade.getImmersiveImage(request, envCtx);
            assertNull(result);
        }
    }
}
