package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.BeautyTechGroupService;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.OptionalServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Test cases for BeautyHighlightsV2Processor#getServiceProjectSku
 */
@RunWith(MockitoJUnitRunner.class)
public class BeautyHighlightsV2ProcessorGetServiceProjectSkuTest {

    @InjectMocks
    private BeautyHighlightsV2Processor processor;

    @Mock
    private BeautyTechGroupService beautyTechGroupService;

    /**
     * Helper method to invoke private methods using reflection.
     */
    @SuppressWarnings("unchecked")
    private <T> T invokePrivateMethod(Object instance, String methodName, Object... args) throws Exception {
        Method method = instance.getClass().getDeclaredMethod(methodName, DealGroupDTO.class, int.class);
        method.setAccessible(true);
        return (T) method.invoke(instance, args);
    }

    /**
     * Test when serviceProject in dealGroupDTO is null
     */
    @Test
    public void testGetServiceProjectSku_NullServiceProject() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        int skuId = 123;
        // act
        List<ServiceProjectDTO> result = invokePrivateMethod(processor, "getServiceProjectSku", dealGroupDTO, skuId);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test when both mustGroups and optionGroups are empty
     */
    @Test
    public void testGetServiceProjectSku_EmptyGroups() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupServiceProjectDTO serviceProject = new DealGroupServiceProjectDTO();
        serviceProject.setMustGroups(new ArrayList<>());
        serviceProject.setOptionGroups(new ArrayList<>());
        dealGroupDTO.setServiceProject(serviceProject);
        int skuId = 123;
        // act
        List<ServiceProjectDTO> result = invokePrivateMethod(processor, "getServiceProjectSku", dealGroupDTO, skuId);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test when there are matching service projects in mustGroups
     */
    @Test
    public void testGetServiceProjectSku_MatchingMustGroups() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupServiceProjectDTO serviceProject = new DealGroupServiceProjectDTO();
        // Create matching service project
        ServiceProjectDTO matchingProject = new ServiceProjectDTO();
        matchingProject.setCategoryId(123L);
        // Create must group
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        mustGroup.setGroups(Lists.newArrayList(matchingProject));
        serviceProject.setMustGroups(Lists.newArrayList(mustGroup));
        serviceProject.setOptionGroups(new ArrayList<>());
        dealGroupDTO.setServiceProject(serviceProject);
        int skuId = 123;
        // act
        List<ServiceProjectDTO> result = invokePrivateMethod(processor, "getServiceProjectSku", dealGroupDTO, skuId);
        // assert
        assertEquals(1, result.size());
        assertEquals(Long.valueOf(123), result.get(0).getCategoryId());
    }

    /**
     * Test when there are matching service projects in optionGroups
     */
    @Test
    public void testGetServiceProjectSku_MatchingOptionGroups() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupServiceProjectDTO serviceProject = new DealGroupServiceProjectDTO();
        // Create matching service project
        ServiceProjectDTO matchingProject = new ServiceProjectDTO();
        matchingProject.setCategoryId(123L);
        // Create option group
        OptionalServiceProjectGroupDTO optionGroup = new OptionalServiceProjectGroupDTO();
        optionGroup.setGroups(Lists.newArrayList(matchingProject));
        serviceProject.setMustGroups(new ArrayList<>());
        serviceProject.setOptionGroups(Lists.newArrayList(optionGroup));
        dealGroupDTO.setServiceProject(serviceProject);
        int skuId = 123;
        // act
        List<ServiceProjectDTO> result = invokePrivateMethod(processor, "getServiceProjectSku", dealGroupDTO, skuId);
        // assert
        assertEquals(1, result.size());
        assertEquals(Long.valueOf(123), result.get(0).getCategoryId());
    }

    /**
     * Test when there are no matching service projects
     */
    @Test
    public void testGetServiceProjectSku_NoMatches() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupServiceProjectDTO serviceProject = new DealGroupServiceProjectDTO();
        // Create non-matching service project
        ServiceProjectDTO nonMatchingProject = new ServiceProjectDTO();
        nonMatchingProject.setCategoryId(456L);
        // Create must group
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        mustGroup.setGroups(Lists.newArrayList(nonMatchingProject));
        serviceProject.setMustGroups(Lists.newArrayList(mustGroup));
        serviceProject.setOptionGroups(new ArrayList<>());
        dealGroupDTO.setServiceProject(serviceProject);
        int skuId = 123;
        // act
        List<ServiceProjectDTO> result = invokePrivateMethod(processor, "getServiceProjectSku", dealGroupDTO, skuId);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test when there are both matching and non-matching service projects
     */
    @Test
    public void testGetServiceProjectSku_MixedMatches() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupServiceProjectDTO serviceProject = new DealGroupServiceProjectDTO();
        // Create matching and non-matching projects
        ServiceProjectDTO matchingProject = new ServiceProjectDTO();
        matchingProject.setCategoryId(123L);
        ServiceProjectDTO nonMatchingProject = new ServiceProjectDTO();
        nonMatchingProject.setCategoryId(456L);
        // Create must group with mixed projects
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        mustGroup.setGroups(Lists.newArrayList(matchingProject, nonMatchingProject));
        serviceProject.setMustGroups(Lists.newArrayList(mustGroup));
        serviceProject.setOptionGroups(new ArrayList<>());
        dealGroupDTO.setServiceProject(serviceProject);
        int skuId = 123;
        // act
        List<ServiceProjectDTO> result = invokePrivateMethod(processor, "getServiceProjectSku", dealGroupDTO, skuId);
        // assert
        assertEquals(1, result.size());
        assertEquals(Long.valueOf(123), result.get(0).getCategoryId());
    }

    @Test
    public void testGetHighlightsContentWithException() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        DealGroupDTO dealGroup = mock(DealGroupDTO.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroup);
        when(dealGroup.getCategory()).thenReturn(category);
        // Beauty SPA category
        when(category.getCategoryId()).thenReturn(503L);
        when(dealGroup.getDpDealGroupId()).thenReturn(12345L);
        when(dealGroup.getMtDealGroupId()).thenReturn(67890L);
        // Throw exception when getOtherContent is called
        when(dealGroup.getAttrs()).thenThrow(new RuntimeException("Test exception"));
        // act
        String result = processor.getHighlightsContent(ctx);
        // assert
        assertNull(result);
        // Verify that the exception was logged
        verify(dealGroup).getDpDealGroupId();
        verify(dealGroup).getMtDealGroupId();
    }

    @Test
    public void testGetHighlightsContentWithExceptionForNursingCategory() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        DealGroupDTO dealGroup = mock(DealGroupDTO.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroup);
        when(dealGroup.getCategory()).thenReturn(category);
        // Hair category
        when(category.getCategoryId()).thenReturn(501L);
        // Nursing service type
        when(category.getServiceType()).thenReturn("护理");
        when(dealGroup.getDpDealGroupId()).thenReturn(12345L);
        when(dealGroup.getMtDealGroupId()).thenReturn(67890L);
        // Throw exception when getNursingContent is called
        doThrow(new NullPointerException("Test NPE")).when(dealGroup).getServiceProject();
        // act
        String result = processor.getHighlightsContent(ctx);
        // assert
        assertNull(result);
        // Verify that the exception was logged
        verify(dealGroup).getDpDealGroupId();
        verify(dealGroup).getMtDealGroupId();
    }

    @Test
    public void testGetHighlightsContentWithExceptionForEyelashesCategory() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        DealGroupDTO dealGroup = mock(DealGroupDTO.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroup);
        when(dealGroup.getCategory()).thenReturn(category);
        // Nail and eyelashes category
        when(category.getCategoryId()).thenReturn(502L);
        // Eyelashes service type
        when(category.getServiceType()).thenReturn("美睫");
        when(dealGroup.getDpDealGroupId()).thenReturn(12345L);
        when(dealGroup.getMtDealGroupId()).thenReturn(67890L);
        // Throw exception when getEyelashesContent is called
        doThrow(new IllegalArgumentException("Test IAE")).when(dealGroup).getTags();
        // act
        String result = processor.getHighlightsContent(ctx);
        // assert
        assertNull(result);
        // Verify that the exception was logged
        verify(dealGroup).getDpDealGroupId();
        verify(dealGroup).getMtDealGroupId();
    }
}
