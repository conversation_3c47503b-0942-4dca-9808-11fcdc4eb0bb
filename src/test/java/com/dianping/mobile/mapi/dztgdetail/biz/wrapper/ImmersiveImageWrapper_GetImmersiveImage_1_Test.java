package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageVO;
import com.dianping.mobile.mapi.dztgdetail.entity.ExhibitImageConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.HaiMaNailFilterConfig;
import com.sankuai.mpmctcontent.application.thrift.dto.content.LoadDealRelatedCaseListRespDTO;
import com.sankuai.mpmctcontent.application.thrift.dto.content.ListItemDTO;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ImmersiveImageWrapper_GetImmersiveImage_1_Test {

    @InjectMocks
    private ImmersiveImageWrapper immersiveImageWrapper;

    @Mock
    private Future<LoadDealRelatedCaseListRespDTO> future;

    @Mock
    private LoadDealRelatedCaseListRespDTO respDTO;

    @Mock
    private ExhibitImageConfig exhibitImageConfig;

    @Mock
    private HaimaWrapper haimaWrapper;

    @Test
    public void testGetImmersiveImageFutureIsNull() throws Throwable {
        // Arrange
        Future<LoadDealRelatedCaseListRespDTO> future = null;
        int categoryId = 1;
        boolean isMt = true;
        // Act
        ImmersiveImageVO result = immersiveImageWrapper.getImmersiveImage(future, categoryId, isMt);
        // Assert
        assertNull(result);
    }

    @Test
    public void testGetImmersiveImageResponseIsNull() throws Throwable {
        // Arrange
        when(future.get()).thenReturn(null);
        int categoryId = 1;
        boolean isMt = true;
        // Act
        ImmersiveImageVO result = immersiveImageWrapper.getImmersiveImage(future, categoryId, isMt);
        // Assert
        assertNull(result);
    }

    @Test
    public void testGetImmersiveImageHandleRespIsNull() throws Throwable {
        // Arrange
        when(future.get()).thenReturn(respDTO);
        int categoryId = 1;
        boolean isMt = true;
        // Act
        ImmersiveImageVO result = immersiveImageWrapper.getImmersiveImage(future, categoryId, isMt);
        // Assert
        assertNull(result);
    }
}
