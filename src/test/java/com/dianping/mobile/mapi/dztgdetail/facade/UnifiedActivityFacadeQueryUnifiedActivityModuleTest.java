package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DzCardPromoWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.SkuWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.TimesCardWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.ActivityCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedActivityModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.ActivityModuleDTO;
import com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil;
import com.dianping.mobile.mapi.dztgdetail.util.PlusNumUtils;
import com.dianping.pigeon.remoting.invoker.config.annotation.Reference;
import com.dianping.tpfun.product.api.sku.pintuan.dto.BestPinTag;
import com.sankuai.dealuser.price.display.api.PriceDisplayService;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PriceRequest;
import com.sankuai.dealuser.price.display.api.model.PriceResponse;
import com.sankuai.dzcard.navigation.api.dto.CardQualifyEventIdDTO;
import com.sankuai.dzcard.navigation.api.enums.QualifyEventTypeEnum;
import com.sankuai.merchantcard.timescard.exposure.dto.CardSummaryBarDTO;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

public class UnifiedActivityFacadeQueryUnifiedActivityModuleTest {

    @InjectMocks
    private UnifiedActivityFacade unifiedActivityFacade;

    @Mock
    private SkuWrapper skuWrapper;

    @Mock
    private MapperWrapper mapperWrapper;

    @Mock
    private TimesCardWrapper timesCardWrapper;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private DzCardPromoWrapper cardWrapper;

    @Mock
    @Reference
    private PriceDisplayService priceDisplayService;

    @Mock
    private IMobileContext iMobileContext;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Test when `envCtx.isMt()` is true but validations fail.
     */
    @Test
    public void testQueryUnifiedActivityModule_MtContext_ValidationsFail() throws Throwable {
        // arrange
        UnifiedActivityModuleReq request = new UnifiedActivityModuleReq();
        EnvCtx envCtx = new EnvCtx();
        // Set client type to Meituan
        envCtx.setClientType(2);
        ActivityCtx activityCtx = new ActivityCtx(envCtx);
        when(skuWrapper.prepareBestPin(anyInt(), any(ActivityCtx.class))).thenReturn(mock(Future.class));
        when(timesCardWrapper.preTimesCardsV2(anyInt(), anyLong(), anyInt(), anyLong())).thenReturn(mock(Future.class));
        when(cardWrapper.prepareV2(anyLong(), anyInt(), anyLong(), anyBoolean(), any(EnvCtx.class), anyString())).thenReturn(mock(Future.class));
        BestPinTag bestPinTag = new BestPinTag();
        bestPinTag.setPinPersonNum(2);
        bestPinTag.setPrice(new BigDecimal("100.00"));
        when(skuWrapper.getBestPin(any(Future.class))).thenReturn(bestPinTag);
        CardSummaryBarDTO timesCard = new CardSummaryBarDTO();
        // Invalid times card
        timesCard.setPrice(null);
        when(timesCardWrapper.queryTimesCard(any(Future.class))).thenReturn(timesCard);
        // Invalid cards
        List<CardQualifyEventIdDTO> cards = Collections.emptyList();
        when(cardWrapper.resolve(any(Future.class))).thenReturn(cards);
        // act
        ActivityModuleDTO result = unifiedActivityFacade.queryUnifiedActivityModule(request, envCtx, iMobileContext);
        // assert
        assertNull(result);
    }

    /**
     * Test when `envCtx.isMt()` is false but validations fail.
     */
    @Test
    public void testQueryUnifiedActivityModule_DpContext_ValidationsFail() throws Throwable {
        // arrange
        UnifiedActivityModuleReq request = new UnifiedActivityModuleReq();
        EnvCtx envCtx = new EnvCtx();
        // Set client type to Dianping
        envCtx.setClientType(1);
        ActivityCtx activityCtx = new ActivityCtx(envCtx);
        when(skuWrapper.prepareBestPin(anyInt(), any(ActivityCtx.class))).thenReturn(mock(Future.class));
        when(timesCardWrapper.preTimesCardsV2(anyInt(), anyLong(), anyInt(), anyLong())).thenReturn(mock(Future.class));
        when(cardWrapper.prepareV2(anyLong(), anyInt(), anyLong(), anyBoolean(), any(EnvCtx.class), anyString())).thenReturn(mock(Future.class));
        BestPinTag bestPinTag = new BestPinTag();
        bestPinTag.setPinPersonNum(2);
        bestPinTag.setPrice(new BigDecimal("100.00"));
        when(skuWrapper.getBestPin(any(Future.class))).thenReturn(bestPinTag);
        CardSummaryBarDTO timesCard = new CardSummaryBarDTO();
        // Invalid times card
        timesCard.setPrice(null);
        when(timesCardWrapper.queryTimesCard(any(Future.class))).thenReturn(timesCard);
        // Invalid cards
        List<CardQualifyEventIdDTO> cards = Collections.emptyList();
        when(cardWrapper.resolve(any(Future.class))).thenReturn(cards);
        // act
        ActivityModuleDTO result = unifiedActivityFacade.queryUnifiedActivityModule(request, envCtx, iMobileContext);
        // assert
        assertNull(result);
    }

    /**
     * Test when `priceDisplayService.queryPrice` is called but returns an invalid response.
     */
    @Test
    public void testQueryUnifiedActivityModule_PriceDisplayService_InvalidResponse() throws Throwable {
        // arrange
        UnifiedActivityModuleReq request = new UnifiedActivityModuleReq();
        EnvCtx envCtx = new EnvCtx();
        // Set client type to Meituan
        envCtx.setClientType(2);
        ActivityCtx activityCtx = new ActivityCtx(envCtx);
        activityCtx.setMtShopIdLong(12345L);
        when(skuWrapper.prepareBestPin(anyInt(), any(ActivityCtx.class))).thenReturn(mock(Future.class));
        when(timesCardWrapper.preTimesCardsV2(anyInt(), anyLong(), anyInt(), anyLong())).thenReturn(mock(Future.class));
        when(cardWrapper.prepareV2(anyLong(), anyInt(), anyLong(), anyBoolean(), any(EnvCtx.class), anyString())).thenReturn(mock(Future.class));
        BestPinTag bestPinTag = new BestPinTag();
        bestPinTag.setPinPersonNum(2);
        bestPinTag.setPrice(new BigDecimal("100.00"));
        when(skuWrapper.getBestPin(any(Future.class))).thenReturn(bestPinTag);
        CardSummaryBarDTO timesCard = new CardSummaryBarDTO();
        timesCard.setPrice(new BigDecimal("50.00"));
        timesCard.setTimes(5);
        when(timesCardWrapper.queryTimesCard(any(Future.class))).thenReturn(timesCard);
        List<CardQualifyEventIdDTO> cards = Collections.singletonList(new CardQualifyEventIdDTO());
        cards.get(0).setQualifyEventType(QualifyEventTypeEnum.DISCOUNT_CARD.getCode());
        when(cardWrapper.resolve(any(Future.class))).thenReturn(cards);
        PriceResponse<PriceDisplayDTO> priceResponse = new PriceResponse<>(400, "Invalid Request", null);
        when(priceDisplayService.queryPrice(any(PriceRequest.class))).thenReturn(priceResponse);
        // act
        ActivityModuleDTO result = unifiedActivityFacade.queryUnifiedActivityModule(request, envCtx, iMobileContext);
        // assert
        assertNull(result);
    }
}
