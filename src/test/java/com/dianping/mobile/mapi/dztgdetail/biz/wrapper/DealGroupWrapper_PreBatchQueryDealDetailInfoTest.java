package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.struct.query.api.DealDetailStructuredQueryService;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Future;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

public class DealGroupWrapper_PreBatchQueryDealDetailInfoTest {

    @InjectMocks
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private DealDetailStructuredQueryService dealDetailStructuredQueryServiceFuture;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试dpDealGroupIds为空的情况
     */
    @Test
    public void testPreBatchQueryDealDetailInfoEmptyList() throws Throwable {
        // arrange
        List<Integer> dpDealGroupIds = Arrays.asList();
        // act
        List<Future> result = dealGroupWrapper.preBatchQueryDealDetailInfo(dpDealGroupIds);
        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试dpDealGroupIds不为空，但其大小小于50的情况
     */
    @Test
    public void testPreBatchQueryDealDetailInfoLessThan50() throws Throwable {
        // arrange
        List<Integer> dpDealGroupIds = Arrays.asList(1, 2, 3);
        // act
        List<Future> result = dealGroupWrapper.preBatchQueryDealDetailInfo(dpDealGroupIds);
        // assert
        assertEquals(1, result.size());
        verify(dealDetailStructuredQueryServiceFuture, times(1)).batchQueryDealDetailInfo(dpDealGroupIds);
    }

    /**
     * 测试dpDealGroupIds不为空，且其大小等于50的情况
     */
    @Test
    public void testPreBatchQueryDealDetailInfoExactly50() throws Throwable {
        // arrange
        List<Integer> dpDealGroupIds = Arrays.asList(new Integer[50]);
        // act
        List<Future> result = dealGroupWrapper.preBatchQueryDealDetailInfo(dpDealGroupIds);
        // assert
        assertEquals(1, result.size());
        verify(dealDetailStructuredQueryServiceFuture, times(1)).batchQueryDealDetailInfo(dpDealGroupIds);
    }

    /**
     * 测试dpDealGroupIds不为空，且其大小大于50的情况
     */
    @Test
    public void testPreBatchQueryDealDetailInfoMoreThan50() throws Throwable {
        // arrange
        List<Integer> dpDealGroupIds = Arrays.asList(new Integer[51]);
        // act
        List<Future> result = dealGroupWrapper.preBatchQueryDealDetailInfo(dpDealGroupIds);
        // assert
        assertEquals(2, result.size());
        verify(dealDetailStructuredQueryServiceFuture, times(2)).batchQueryDealDetailInfo(anyList());
    }
}
