package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.meituan.service.mobile.sinai.client.PoiClientL;
import com.meituan.service.mobile.sinai.client.model.PoiModelL;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import static org.mockito.ArgumentMatchers.anyList;
import com.meituan.mobile.sinai.base.exception.PoiThriftException;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;

public class PoiClientWrapper_BatchPoiInfoTest {

    @InjectMocks
    private PoiClientWrapper poiClientWrapper;

    @Mock
    private PoiClientL poiClientL;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testBatchPoiInfoLWithEmptyInput() throws Throwable {
        List<PoiModelL> result = poiClientWrapper.batchPoiInfoL(null, Arrays.asList("field1", "field2"));
        assertEquals(Collections.emptyList(), result);
        result = poiClientWrapper.batchPoiInfoL(Arrays.asList(1L, 2L, 3L), null);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testBatchPoiInfoLWithLessThan50PoiIds() throws Throwable {
        when(poiClientL.listPoisL(anyList(), anyList())).thenReturn(Collections.singletonList(new PoiModelL()));
        List<PoiModelL> result = poiClientWrapper.batchPoiInfoL(Arrays.asList(1L, 2L, 3L), Arrays.asList("field1", "field2"));
        assertEquals(1, result.size());
    }

    @Test
    public void testBatchPoiInfoLWithMoreThan50PoiIds() throws Throwable {
        List<PoiModelL> mockPoiModelList = new ArrayList<>();
        for (int i = 0; i < 50; i++) {
            mockPoiModelList.add(new PoiModelL());
        }
        when(poiClientL.listPoisL(anyList(), anyList())).thenReturn(mockPoiModelList);
        List<PoiModelL> result = poiClientWrapper.batchPoiInfoL(Arrays.asList(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L, 9L, 10L, 11L, 12L, 13L, 14L, 15L, 16L, 17L, 18L, 19L, 20L, 21L, 22L, 23L, 24L, 25L, 26L, 27L, 28L, 29L, 30L, 31L, 32L, 33L, 34L, 35L, 36L, 37L, 38L, 39L, 40L, 41L, 42L, 43L, 44L, 45L, 46L, 47L, 48L, 49L, 50L), Arrays.asList("field1", "field2"));
        assertEquals(50, result.size());
    }

    @Test
    public void testBatchPoiInfoLWithException() throws Throwable {
        when(poiClientL.listPoisL(anyList(), anyList())).thenThrow(new RuntimeException());
        List<PoiModelL> result = poiClientWrapper.batchPoiInfoL(Arrays.asList(1L, 2L, 3L), Arrays.asList("field1", "field2"));
        assertEquals(Collections.emptyList(), result);
    }
}
