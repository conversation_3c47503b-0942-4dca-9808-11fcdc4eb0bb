package com.dianping.mobile.mapi.dztgdetail.mq;

import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.mq.dto.ProductTrafficEntity;
import com.dianping.mobile.mapi.dztgdetail.mq.dto.SamplingConfig;
import com.meituan.mafka.client.bean.MafkaProducer;
import com.meituan.mafka.client.producer.AsyncProducerResult;
import com.meituan.mafka.client.producer.FutureCallback;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.meituan.mtrace.Tracer;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class TrafficDiffSamplingServiceTest {

    @InjectMocks
    private TrafficDiffSamplingService trafficDiffSamplingService;

    @Mock
    private MafkaProducer trafficDiffProducer;

    @Mock
    private DealBaseReq req;

    @Mock
    private EnvCtx envCtx;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private ProducerResult producerResult;

    @Mock
    private AsyncProducerResult asyncProducerResult;

    private MockedStatic<Lion> lionMockedStatic;
    private MockedStatic<Cat> catMockedStatic;
    private MockedStatic<Tracer> tracerMockedStatic;
    private MockedStatic<Environment> environmentMockedStatic;

    private Map<String, String> headerMap;

    @Before
    public void setUp() {
        lionMockedStatic = mockStatic(Lion.class);
        catMockedStatic = mockStatic(Cat.class);
        tracerMockedStatic = mockStatic(Tracer.class);
        environmentMockedStatic = mockStatic(Environment.class);

        headerMap = new HashMap<>();
        headerMap.put("User-Agent", "Android");
        headerMap.put("Content-Type", "application/json");
    }

    @After
    public void tearDown() {
        lionMockedStatic.close();
        catMockedStatic.close();
        tracerMockedStatic.close();
        environmentMockedStatic.close();
    }

    @Test
    public void testSendMessage_NullReq() {
        // Test null req parameter
        trafficDiffSamplingService.sendMessage(null, envCtx, dealCtx, headerMap);

        // Verify no interactions with mocked dependencies
        verifyNoInteractions(trafficDiffProducer);
    }

    @Test
    public void testSendMessage_NullEnvCtx() {
        // Test null envCtx parameter
        trafficDiffSamplingService.sendMessage(req, null, dealCtx, headerMap);

        verifyNoInteractions(trafficDiffProducer);
    }

    @Test
    public void testSendMessage_NullDealCtx() {
        // Test null dealCtx parameter
        trafficDiffSamplingService.sendMessage(req, envCtx, null, headerMap);

        verifyNoInteractions(trafficDiffProducer);
    }

    @Test
    public void testSendMessage_EmptyHeaderMap() {
        // Test empty headerMap parameter
        trafficDiffSamplingService.sendMessage(req, envCtx, dealCtx, new HashMap<>());

        verifyNoInteractions(trafficDiffProducer);
    }

    @Test
    public void testSendMessage_IsTestTraffic() {
        // Test pressure test traffic filtering
        tracerMockedStatic.when(Tracer::isTest).thenReturn(true);

        trafficDiffSamplingService.sendMessage(req, envCtx, dealCtx, headerMap);

        verifyNoInteractions(trafficDiffProducer);
    }

    @Test
    public void testSendMessage_DiffReplayFlag() {
        // Test replay request filtering
        tracerMockedStatic.when(Tracer::isTest).thenReturn(false);
        when(req.getDiffReplayFlag()).thenReturn(1);

        trafficDiffSamplingService.sendMessage(req, envCtx, dealCtx, headerMap);

        verifyNoInteractions(trafficDiffProducer);
    }

    @Test
    public void testSendMessage_SourceBlacklist() {
        // Test source blacklist filtering
        tracerMockedStatic.when(Tracer::isTest).thenReturn(false);
        when(req.getDiffReplayFlag()).thenReturn(0);
        when(req.getPageSource()).thenReturn("BML");

        List<String> blackList = Arrays.asList("BML", "snapshot", "preview");
        lionMockedStatic.when(() -> Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb", "product.page.diff.source.blacklist", String.class))
                .thenReturn(blackList);

        trafficDiffSamplingService.sendMessage(req, envCtx, dealCtx, headerMap);

        verifyNoInteractions(trafficDiffProducer);
    }

    @Test
    public void testSendMessage_MissingAssociationParams_ProductEnv() {
        // Test missing association parameters in production environment
        tracerMockedStatic.when(Tracer::isTest).thenReturn(false);
        environmentMockedStatic.when(Environment::isProductEnv).thenReturn(true);
        when(req.getDiffReplayFlag()).thenReturn(0);
//        when(req.getPageSource()).thenReturn("poi_page");
        when(req.getDiffUniqueId()).thenReturn("");

        lionMockedStatic.when(() -> Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb", "product.page.diff.source.blacklist", String.class))
                .thenReturn(Collections.emptyList());

        trafficDiffSamplingService.sendMessage(req, envCtx, dealCtx, headerMap);

//        catMockedStatic.verify(() -> Cat.logEvent("Pricecipher", "poi_page"));
        verifyNoInteractions(trafficDiffProducer);
    }

    @Test
    public void testSendMessage_MissingAssociationParams_UnknownPageSource() {
        // Test missing association parameters with unknown page source
        tracerMockedStatic.when(Tracer::isTest).thenReturn(false);
        environmentMockedStatic.when(Environment::isProductEnv).thenReturn(true);
        when(req.getDiffReplayFlag()).thenReturn(0);
        when(req.getPageSource()).thenReturn("");
        when(req.getDiffUniqueId()).thenReturn("");

        lionMockedStatic.when(() -> Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb", "product.page.diff.source.blacklist", String.class))
                .thenReturn(Collections.emptyList());

        trafficDiffSamplingService.sendMessage(req, envCtx, dealCtx, headerMap);

//        catMockedStatic.verify(() -> Cat.logEvent("Pricecipher", "unknown"));
        verifyNoInteractions(trafficDiffProducer);
    }

    @Test
    public void testSendMessage_SamplingDisabled() throws Exception {
        // Test sampling disabled
        setupBasicMocks();

        SamplingConfig samplingConfig = new SamplingConfig();
        samplingConfig.setEnable(false);
        lionMockedStatic.when(() -> Lion.getBean("com.sankuai.dzshoppingguide.detail.gateway", "product.page.diff.sampling.config", SamplingConfig.class))
                .thenReturn(samplingConfig);

        trafficDiffSamplingService.sendMessage(req, envCtx, dealCtx, headerMap);

//        catMockedStatic.verify(() -> Cat.logEvent("Pricecipher", "Pass"));
        verifyNoInteractions(trafficDiffProducer);
    }

    @Test
    public void testSendMessage_CategoryNotInWhitelist() throws Exception {
        // Test category not in whitelist
        setupBasicMocks();

        SamplingConfig samplingConfig = new SamplingConfig();
        samplingConfig.setEnable(true);
        samplingConfig.setAllCategory(false);
        samplingConfig.setCategoryIds(Arrays.asList(1, 2, 3));
        lionMockedStatic.when(() -> Lion.getBean("com.sankuai.dzshoppingguide.detail.gateway", "product.page.diff.sampling.config", SamplingConfig.class))
                .thenReturn(samplingConfig);

        when(dealCtx.getCategoryId()).thenReturn(999);

        trafficDiffSamplingService.sendMessage(req, envCtx, dealCtx, headerMap);

        verifyNoInteractions(trafficDiffProducer);
    }

    @Test
    public void testSendMessage_SamplingRatioNotMet() throws Exception {
        // Test sampling ratio not met
        setupBasicMocks();

        SamplingConfig samplingConfig = new SamplingConfig();
        samplingConfig.setEnable(true);
        samplingConfig.setAllCategory(true);
        samplingConfig.setRatio(1); // Very low ratio
        lionMockedStatic.when(() -> Lion.getBean("com.sankuai.dzshoppingguide.detail.gateway", "product.page.diff.sampling.config", SamplingConfig.class))
                .thenReturn(samplingConfig);

        when(req.getDiffUniqueId()).thenReturn("test_unique_id_with_high_hash");

        trafficDiffSamplingService.sendMessage(req, envCtx, dealCtx, headerMap);

        verifyNoInteractions(trafficDiffProducer);
    }

    @Test
    public void testSendMessage_Success() throws Exception {
        // Test successful message sending
        setupBasicMocks();
        setupSuccessfulSampling();

        when(trafficDiffProducer.sendAsyncMessage(anyString(), anyString(), any(FutureCallback.class)))
                .thenReturn(producerResult);
        when(producerResult.getProducerStatus()).thenReturn(ProducerStatus.SEND_OK);

        trafficDiffSamplingService.sendMessage(req, envCtx, dealCtx, headerMap);

        verify(trafficDiffProducer).sendAsyncMessage(anyString(), anyString(), any(FutureCallback.class));
//        catMockedStatic.verify(() -> Cat.logEvent("Pricecipher", "Pass"));
    }


    @Test
    public void testSendMessage_ProducerException() throws Exception {
        // Test producer exception
        setupBasicMocks();
        setupSuccessfulSampling();

        when(trafficDiffProducer.sendAsyncMessage(anyString(), anyString(), any(FutureCallback.class)))
                .thenThrow(new RuntimeException("Mafka error"));

        trafficDiffSamplingService.sendMessage(req, envCtx, dealCtx, headerMap);

        catMockedStatic.verify(() -> Cat.logEvent("TrafficDiff", "Error"));
    }

    @Test
    public void testFutureCallback_OnSuccess() throws Exception {
        // Test FutureCallback onSuccess
        setupBasicMocks();
        setupSuccessfulSampling();

        ArgumentCaptor<FutureCallback> callbackCaptor = ArgumentCaptor.forClass(FutureCallback.class);
        when(trafficDiffProducer.sendAsyncMessage(anyString(), anyString(), callbackCaptor.capture()))
                .thenReturn(producerResult);
        when(producerResult.getProducerStatus()).thenReturn(ProducerStatus.SEND_OK);

        trafficDiffSamplingService.sendMessage(req, envCtx, dealCtx, headerMap);

        FutureCallback callback = callbackCaptor.getValue();
        callback.onSuccess(asyncProducerResult);

        catMockedStatic.verify(() -> Cat.logEvent("TrafficDiff", "Success"));
    }

    @Test
    public void testFutureCallback_OnFailure() throws Exception {
        // Test FutureCallback onFailure
        setupBasicMocks();
        setupSuccessfulSampling();

        ArgumentCaptor<FutureCallback> callbackCaptor = ArgumentCaptor.forClass(FutureCallback.class);
        when(trafficDiffProducer.sendAsyncMessage(anyString(), anyString(), callbackCaptor.capture()))
                .thenReturn(producerResult);
        when(producerResult.getProducerStatus()).thenReturn(ProducerStatus.SEND_OK);

        trafficDiffSamplingService.sendMessage(req, envCtx, dealCtx, headerMap);

        FutureCallback callback = callbackCaptor.getValue();
        callback.onFailure(new RuntimeException("Callback error"));

        // Should log error but not throw exception
    }

    @Test
    public void testBuildJoinKey_WithDiffUniqueId() throws Exception {
        // Test buildJoinKey with diffUniqueId
        when(req.getDiffUniqueId()).thenReturn("unique_id_123");

        Method method = TrafficDiffSamplingService.class.getDeclaredMethod("buildJoinKey", DealBaseReq.class);
        method.setAccessible(true);
        String result = (String) method.invoke(trafficDiffSamplingService, req);

        assertEquals("unique_id_123", result);
    }

    @Test
    public void testBuildJoinKey_NonProductEnv() throws Exception {
        // Test buildJoinKey in non-production environment
        when(req.getDiffUniqueId()).thenReturn("");
        when(req.getDealgroupid()).thenReturn(12345);
        when(req.getPoiid()).thenReturn(67890L);
        environmentMockedStatic.when(Environment::isProductEnv).thenReturn(false);

        Method method = TrafficDiffSamplingService.class.getDeclaredMethod("buildJoinKey", DealBaseReq.class);
        method.setAccessible(true);
        String result = (String) method.invoke(trafficDiffSamplingService, req);

        assertEquals("12345_67890", result);
    }


    @Test
    public void testParseTrafficSource() throws Exception {
        Method method = TrafficDiffSamplingService.class.getDeclaredMethod("parseTrafficSource", String.class);
        method.setAccessible(true);

        // Test various traffic sources
        assertEquals(Integer.valueOf(1), method.invoke(trafficDiffSamplingService, "poi_page"));
        assertEquals(Integer.valueOf(2), method.invoke(trafficDiffSamplingService, "cost_effective"));
        assertEquals(Integer.valueOf(3), method.invoke(trafficDiffSamplingService, "caixi"));
        assertEquals(Integer.valueOf(3), method.invoke(trafficDiffSamplingService, "homepage"));
        assertEquals(Integer.valueOf(4), method.invoke(trafficDiffSamplingService, "newyouhuima"));
        assertEquals(Integer.valueOf(5), method.invoke(trafficDiffSamplingService, "mlive"));
        assertEquals(Integer.valueOf(7), method.invoke(trafficDiffSamplingService, "other_source"));
        assertEquals(Integer.valueOf(6), method.invoke(trafficDiffSamplingService, ""));
        assertEquals(Integer.valueOf(6), method.invoke(trafficDiffSamplingService, (String) null));
    }

    @Test
    public void testParseClient() throws Exception {
        Method method = TrafficDiffSamplingService.class.getDeclaredMethod("parseClient", EnvCtx.class);
        method.setAccessible(true);

        // Test main app
        when(envCtx.judgeMainApp()).thenReturn(true);
        assertEquals(Integer.valueOf(1), method.invoke(trafficDiffSamplingService, envCtx));

        // Test WeChat mini program
        when(envCtx.judgeMainApp()).thenReturn(false);
        when(envCtx.isWxMini()).thenReturn(true);
        assertEquals(Integer.valueOf(2), method.invoke(trafficDiffSamplingService, envCtx));

        // Test other
        when(envCtx.judgeMainApp()).thenReturn(false);
        when(envCtx.isWxMini()).thenReturn(false);
        assertEquals(Integer.valueOf(3), method.invoke(trafficDiffSamplingService, envCtx));
    }

    @Test
    public void testParseOs() throws Exception {
        Method method = TrafficDiffSamplingService.class.getDeclaredMethod("parseOs", EnvCtx.class, Map.class);
        method.setAccessible(true);

        // Test Harmony OS
        when(envCtx.isHarmony()).thenReturn(true);
        assertEquals(Integer.valueOf(3), method.invoke(trafficDiffSamplingService, envCtx, headerMap));

        // Test Android
        when(envCtx.isHarmony()).thenReturn(false);
        Map<String, String> androidHeaders = new HashMap<>();
        androidHeaders.put("User-Agent", "Android device");
        assertEquals(Integer.valueOf(1), method.invoke(trafficDiffSamplingService, envCtx, androidHeaders));

        // Test iOS (default)
        when(envCtx.isHarmony()).thenReturn(false);
        Map<String, String> iosHeaders = new HashMap<>();
        iosHeaders.put("User-Agent", "iOS device");
        assertEquals(Integer.valueOf(2), method.invoke(trafficDiffSamplingService, envCtx, iosHeaders));

        // Test empty user agent
        when(envCtx.isHarmony()).thenReturn(false);
        Map<String, String> emptyHeaders = new HashMap<>();
        assertEquals(Integer.valueOf(0), method.invoke(trafficDiffSamplingService, envCtx, emptyHeaders));
    }

    @Test
    public void testBuildRequestParamMap() throws Exception {
        // Test buildRequestParamMap with various parameters
        setupRequestMockData();

        Method method = TrafficDiffSamplingService.class.getDeclaredMethod("buildRequestParamMap", DealBaseReq.class);
        method.setAccessible(true);
        Map<String, Object> result = (Map<String, Object>) method.invoke(trafficDiffSamplingService, req);

        // Verify key parameters are included
        assertEquals(12345, result.get("dealgroupid"));
        assertEquals("sku123", result.get("skuid"));
        assertEquals(67890L, result.get("poiid"));
        assertEquals(1, result.get("cityid"));
        assertEquals(2, result.get("gpscityid"));
        assertEquals(3, result.get("clienttype"));
        assertEquals(116.123, result.get("userlng"));
        assertEquals(39.456, result.get("userlat"));
        assertEquals("1", result.get("diffReplayFlag"));
//        assertEquals("poi_page", result.get("pagesource"));
    }

    @Test
    public void testBuildTrafficEntity() throws Exception {
        // Test buildTrafficEntity
        setupRequestMockData();
        when(dealCtx.getCategoryId()).thenReturn(100);
        when(envCtx.isDp()).thenReturn(true);
        when(envCtx.judgeMainApp()).thenReturn(true);
        when(envCtx.isHarmony()).thenReturn(false);

        Method method = TrafficDiffSamplingService.class.getDeclaredMethod("buildTrafficEntity",
                DealBaseReq.class, EnvCtx.class, DealCtx.class, Map.class, String.class);
        method.setAccessible(true);
        ProductTrafficEntity result = (ProductTrafficEntity) method.invoke(trafficDiffSamplingService,
                req, envCtx, dealCtx, headerMap, "test_join_key");

        // Verify entity fields
        assertEquals(Integer.valueOf(1), result.getProductType());
        assertEquals(Long.valueOf(12345), result.getProductId());
        assertEquals(Integer.valueOf(2), result.getPageType());
        assertEquals("test_join_key", result.getJoinKey());
        assertEquals(Integer.valueOf(100), result.getCategoryId());
//        assertEquals(Integer.valueOf(1), result.getTrafficSource());
        assertEquals(Integer.valueOf(1), result.getPlatform());
        assertEquals(Integer.valueOf(1), result.getClient());
        assertNotNull(result.getRequestParam());
        assertNotNull(result.getRequestHeader());
    }

    @Test
    public void testGetFromHeader_CaseInsensitive() throws Exception {
        Method method = TrafficDiffSamplingService.class.getDeclaredMethod("getFromHeader", Map.class, String.class);
        method.setAccessible(true);

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("user-agent", "test-agent");
        headers.put("AUTHORIZATION", "Bearer token");

        // Test exact match
        assertEquals("application/json", method.invoke(trafficDiffSamplingService, headers, "Content-Type"));

        // Test lowercase match
        assertEquals("test-agent", method.invoke(trafficDiffSamplingService, headers, "User-Agent"));

        // Test uppercase match
        assertEquals("Bearer token", method.invoke(trafficDiffSamplingService, headers, "authorization"));

        // Test not found
        assertEquals("", method.invoke(trafficDiffSamplingService, headers, "Not-Found"));
    }

    @Test
    public void testShouldSampling_NullConfig() throws Exception {
        // Test shouldSampling with null config
        lionMockedStatic.when(() -> Lion.getBean("com.sankuai.dzshoppingguide.detail.gateway", "product.page.diff.sampling.config", SamplingConfig.class))
                .thenReturn(null);

        Method method = TrafficDiffSamplingService.class.getDeclaredMethod("shouldSampling", DealCtx.class, String.class);
        method.setAccessible(true);
        boolean result = (boolean) method.invoke(trafficDiffSamplingService, dealCtx, "test_key");

        assertFalse(result);
    }

    @Test
    public void testShouldSampling_EmptyCategoryIds() throws Exception {
        // Test shouldSampling with empty category IDs
        SamplingConfig samplingConfig = new SamplingConfig();
        samplingConfig.setEnable(true);
        samplingConfig.setAllCategory(false);
        samplingConfig.setCategoryIds(Collections.emptyList());
        lionMockedStatic.when(() -> Lion.getBean("com.sankuai.dzshoppingguide.detail.gateway", "product.page.diff.sampling.config", SamplingConfig.class))
                .thenReturn(samplingConfig);


        Method method = TrafficDiffSamplingService.class.getDeclaredMethod("shouldSampling", DealCtx.class, String.class);
        method.setAccessible(true);
        boolean result = (boolean) method.invoke(trafficDiffSamplingService, dealCtx, "test_key");

        assertFalse(result);
    }

    @Test
    public void testShouldSampling_CategoryInWhitelist() throws Exception {
        // Test shouldSampling with category in whitelist
        SamplingConfig samplingConfig = new SamplingConfig();
        samplingConfig.setEnable(true);
        samplingConfig.setAllCategory(false);
        samplingConfig.setCategoryIds(Arrays.asList(1, 2, 3));
        samplingConfig.setRatio(10000); // 100% sampling
        lionMockedStatic.when(() -> Lion.getBean("com.sankuai.dzshoppingguide.detail.gateway", "product.page.diff.sampling.config", SamplingConfig.class))
                .thenReturn(samplingConfig);

        when(dealCtx.getCategoryId()).thenReturn(2);

        Method method = TrafficDiffSamplingService.class.getDeclaredMethod("shouldSampling", DealCtx.class, String.class);
        method.setAccessible(true);
        boolean result = (boolean) method.invoke(trafficDiffSamplingService, dealCtx, "test_key");

        assertTrue(result);
    }

    @Test
    public void testBuildRequestParamMap_NullValues() throws Exception {
        // Test buildRequestParamMap with null values
        when(req.getDealgroupid()).thenReturn(null);
        when(req.getSkuId()).thenReturn(null);
        when(req.getPoiid()).thenReturn(null);
        when(req.getCityid()).thenReturn(null);
        when(req.getGpsCityId()).thenReturn(null);
        when(req.getClienttype()).thenReturn(null);
        when(req.getUserlng()).thenReturn(null);
        when(req.getUserlat()).thenReturn(null);
        when(req.getCityLongitude()).thenReturn(null);
        when(req.getCityLatitude()).thenReturn(null);
        when(req.getPosition()).thenReturn(null);
        when(req.getRegionid()).thenReturn(null);
        when(req.getExpResults()).thenReturn(null);
        when(req.getLyyuserid()).thenReturn(null);
        when(req.getGpsCoordinateType()).thenReturn(null);
        when(req.getPageSource()).thenReturn(null);
        when(req.getFromPage()).thenReturn(null);
        when(req.getConvertcolor()).thenReturn(null);
        when(req.getEventpromochannel()).thenReturn(null);
        when(req.getPass_param()).thenReturn(null);
        when(req.getChannel()).thenReturn(null);
        when(req.getExtParam()).thenReturn(null);
        when(req.getCx()).thenReturn(null);
        when(req.getMrnversion()).thenReturn(null);
        when(req.getDealParam()).thenReturn(null);
        when(req.getMliveAbTestArgs()).thenReturn(null);
        when(req.getPrivateLiveId()).thenReturn(null);
        when(req.getSceneType()).thenReturn(null);
        when(req.getPintuanActivityId()).thenReturn(null);
        when(req.getOrderGroupId()).thenReturn(null);
        when(req.getInfoContentId()).thenReturn(null);
        when(req.getMmcinflate()).thenReturn(null);
        when(req.getMmcuse()).thenReturn(null);
        when(req.getMmcbuy()).thenReturn(null);
        when(req.getMmcfree()).thenReturn(null);
        when(req.getWxVersion()).thenReturn(null);
        when(req.getOfflineCode()).thenReturn(null);
        when(req.getDistributionParam()).thenReturn(null);
        when(req.getUserDistributionParam()).thenReturn(null);
        when(req.getMmcPkgVersion()).thenReturn(null);

        Method method = TrafficDiffSamplingService.class.getDeclaredMethod("buildRequestParamMap", DealBaseReq.class);
        method.setAccessible(true);
        Map<String, Object> result = (Map<String, Object>) method.invoke(trafficDiffSamplingService, req);

        // Should only contain the hardcoded diffReplayFlag
        assertEquals("1", result.get("diffReplayFlag"));
        assertEquals(1, result.size());
    }

    @Test
    public void testBuildTrafficEntity_MtPlatform() throws Exception {
        // Test buildTrafficEntity with MT platform
        setupRequestMockData();
        when(dealCtx.getCategoryId()).thenReturn(100);
        when(envCtx.isDp()).thenReturn(false); // MT platform
        when(envCtx.judgeMainApp()).thenReturn(false);
        when(envCtx.isWxMini()).thenReturn(true);
        when(envCtx.isHarmony()).thenReturn(true);

        Method method = TrafficDiffSamplingService.class.getDeclaredMethod("buildTrafficEntity",
                DealBaseReq.class, EnvCtx.class, DealCtx.class, Map.class, String.class);
        method.setAccessible(true);
        ProductTrafficEntity result = (ProductTrafficEntity) method.invoke(trafficDiffSamplingService,
                req, envCtx, dealCtx, headerMap, "test_join_key");

        // Verify MT platform specific values
        assertEquals(Integer.valueOf(2), result.getPlatform()); // MT platform
        assertEquals(Integer.valueOf(2), result.getClient()); // WeChat mini program
        assertEquals(Integer.valueOf(3), result.getOs()); // Harmony OS
    }

    @Test
    public void testParseTrafficSource_CaseInsensitive() throws Exception {
        Method method = TrafficDiffSamplingService.class.getDeclaredMethod("parseTrafficSource", String.class);
        method.setAccessible(true);

        // Test case insensitive matching
        assertEquals(Integer.valueOf(1), method.invoke(trafficDiffSamplingService, "POI_PAGE"));
        assertEquals(Integer.valueOf(2), method.invoke(trafficDiffSamplingService, "COST_EFFECTIVE"));
        assertEquals(Integer.valueOf(3), method.invoke(trafficDiffSamplingService, "CAIXI"));
        assertEquals(Integer.valueOf(3), method.invoke(trafficDiffSamplingService, "HOMEPAGE"));
        assertEquals(Integer.valueOf(4), method.invoke(trafficDiffSamplingService, "NEWYOUHUIMA"));
        assertEquals(Integer.valueOf(5), method.invoke(trafficDiffSamplingService, "MLIVE"));
    }

    @Test
    public void testSendMessage_NonProductEnvWithValidParams() throws Exception {
        // Test non-production environment with valid parameters
        tracerMockedStatic.when(Tracer::isTest).thenReturn(false);
        environmentMockedStatic.when(Environment::isProductEnv).thenReturn(false);
        when(req.getDiffReplayFlag()).thenReturn(0);
//        when(req.getPageSource()).thenReturn("poi_page");
        when(req.getDiffUniqueId()).thenReturn("");

        lionMockedStatic.when(() -> Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb", "product.page.diff.source.blacklist", String.class))
                .thenReturn(Collections.emptyList());

        setupSuccessfulSampling();

        when(trafficDiffProducer.sendAsyncMessage(anyString(), anyString(), any(FutureCallback.class)))
                .thenReturn(producerResult);
        when(producerResult.getProducerStatus()).thenReturn(ProducerStatus.SEND_OK);

        trafficDiffSamplingService.sendMessage(req, envCtx, dealCtx, headerMap);

//        catMockedStatic.verify(() -> Cat.logEvent("Pricecipher", "Pass"));
        verify(trafficDiffProducer).sendAsyncMessage(anyString(), anyString(), any(FutureCallback.class));
    }

    @Test
    public void testSendMessage_ProductEnvWithValidParams() throws Exception {
        // Test production environment with valid parameters
        tracerMockedStatic.when(Tracer::isTest).thenReturn(false);
        environmentMockedStatic.when(Environment::isProductEnv).thenReturn(true);
        when(req.getDiffReplayFlag()).thenReturn(0);
//        when(req.getPageSource()).thenReturn("poi_page");
        when(req.getDiffUniqueId()).thenReturn("valid_unique_id");

        lionMockedStatic.when(() -> Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb", "product.page.diff.source.blacklist", String.class))
                .thenReturn(Collections.emptyList());

        setupSuccessfulSampling();

        when(trafficDiffProducer.sendAsyncMessage(anyString(), anyString(), any(FutureCallback.class)))
                .thenReturn(producerResult);
        when(producerResult.getProducerStatus()).thenReturn(ProducerStatus.SEND_OK);

        trafficDiffSamplingService.sendMessage(req, envCtx, dealCtx, headerMap);

//        catMockedStatic.verify(() -> Cat.logEvent("Pricecipher", "Pass"));
        verify(trafficDiffProducer).sendAsyncMessage(anyString(), anyString(), any(FutureCallback.class));
    }

//    @Test
//    public void testSendMessage_ProductEnvWithPricecipherOnly() throws Exception {
//        // Test production environment with only pricecipher (no diffUniqueId)
//        tracerMockedStatic.when(Tracer::isTest).thenReturn(false);
//        environmentMockedStatic.when(Environment::isProductEnv).thenReturn(true);
//        when(req.getDiffReplayFlag()).thenReturn(0);
//        when(req.getPageSource()).thenReturn("poi_page");
//        when(req.getDiffUniqueId()).thenReturn("");
//
//        lionMockedStatic.when(() -> Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb", "product.page.diff.source.blacklist", String.class))
//                .thenReturn(Collections.emptyList());
//
//        setupSuccessfulSampling();
//
//        when(trafficDiffProducer.sendAsyncMessage(anyString(), anyString(), any(FutureCallback.class)))
//                .thenReturn(producerResult);
//        when(producerResult.getProducerStatus()).thenReturn(ProducerStatus.SEND_OK);
//
//        trafficDiffSamplingService.sendMessage(req, envCtx, dealCtx, headerMap);
//
//        verify(trafficDiffProducer).sendAsyncMessage(anyString(), anyString(), any(FutureCallback.class));
//    }

    @Test
    public void testBuildJoinKey_NullValues() throws Exception {
        // Test buildJoinKey with null values
        when(req.getDiffUniqueId()).thenReturn("");
        when(req.getDealgroupid()).thenReturn(null);
        when(req.getPoiid()).thenReturn(null);
        environmentMockedStatic.when(Environment::isProductEnv).thenReturn(false);

        Method method = TrafficDiffSamplingService.class.getDeclaredMethod("buildJoinKey", DealBaseReq.class);
        method.setAccessible(true);
        String result = (String) method.invoke(trafficDiffSamplingService, req);

        assertEquals("null_null", result);
    }

    @Test
    public void testBuildJoinKey_ProductEnvNullValues() throws Exception {
        // Test buildJoinKey in production environment with null values
        when(req.getDiffUniqueId()).thenReturn("");
        when(req.getDealgroupid()).thenReturn(null);
        environmentMockedStatic.when(Environment::isProductEnv).thenReturn(true);

        Method method = TrafficDiffSamplingService.class.getDeclaredMethod("buildJoinKey", DealBaseReq.class);
        method.setAccessible(true);
        String result = (String) method.invoke(trafficDiffSamplingService, req);

        assertEquals("null_null", result);
    }

    @Test
    public void testParseOs_NullUserAgent() throws Exception {
        Method method = TrafficDiffSamplingService.class.getDeclaredMethod("parseOs", EnvCtx.class, Map.class);
        method.setAccessible(true);

        // Test null user agent
        when(envCtx.isHarmony()).thenReturn(false);
        Map<String, String> nullHeaders = new HashMap<>();
        nullHeaders.put("User-Agent", null);
        assertEquals(Integer.valueOf(0), method.invoke(trafficDiffSamplingService, envCtx, nullHeaders));
    }

    @Test
    public void testSendMessage_NullHeaderMap() {
        // Test null headerMap parameter
        trafficDiffSamplingService.sendMessage(req, envCtx, dealCtx, null);

        verifyNoInteractions(trafficDiffProducer);
    }

    @Test
    public void testSendMessage_DiffReplayFlagNull() {
        // Test null diffReplayFlag (should not filter)
        tracerMockedStatic.when(Tracer::isTest).thenReturn(false);
        when(req.getDiffReplayFlag()).thenReturn(null);
//        when(req.getPageSource()).thenReturn("poi_page");

        lionMockedStatic.when(() -> Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb", "product.page.diff.source.blacklist", String.class))
                .thenReturn(Collections.emptyList());

        // Should continue processing (not return early)
        environmentMockedStatic.when(Environment::isProductEnv).thenReturn(false);
        when(req.getDiffUniqueId()).thenReturn("test_id");

        SamplingConfig samplingConfig = new SamplingConfig();
        samplingConfig.setEnable(false); // Will cause early return in shouldSampling
        lionMockedStatic.when(() -> Lion.getBean("com.sankuai.dzshoppingguide.detail.gateway", "product.page.diff.sampling.config", SamplingConfig.class))
                .thenReturn(samplingConfig);

        trafficDiffSamplingService.sendMessage(req, envCtx, dealCtx, headerMap);

//        catMockedStatic.verify(() -> Cat.logEvent("Pricecipher", "Pass"));
        verifyNoInteractions(trafficDiffProducer);
    }

    @Test
    public void testSendMessage_EmptyBlacklist() {
        // Test with empty blacklist (should not filter)
        tracerMockedStatic.when(Tracer::isTest).thenReturn(false);
        when(req.getDiffReplayFlag()).thenReturn(0);
//        when(req.getPageSource()).thenReturn("some_source");

        lionMockedStatic.when(() -> Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb", "product.page.diff.source.blacklist", String.class))
                .thenReturn(Collections.emptyList());

        // Should continue processing
        environmentMockedStatic.when(Environment::isProductEnv).thenReturn(false);
        when(req.getDiffUniqueId()).thenReturn("test_id");

        SamplingConfig samplingConfig = new SamplingConfig();
        samplingConfig.setEnable(false);
        lionMockedStatic.when(() -> Lion.getBean("com.sankuai.dzshoppingguide.detail.gateway", "product.page.diff.sampling.config", SamplingConfig.class))
                .thenReturn(samplingConfig);

        trafficDiffSamplingService.sendMessage(req, envCtx, dealCtx, headerMap);

//        catMockedStatic.verify(() -> Cat.logEvent("Pricecipher", "Pass"));
        verifyNoInteractions(trafficDiffProducer);
    }

    @Test
    public void testSendMessage_NullBlacklist() {
        // Test with null blacklist (should not filter)
        tracerMockedStatic.when(Tracer::isTest).thenReturn(false);
        when(req.getDiffReplayFlag()).thenReturn(0);
//        when(req.getPageSource()).thenReturn("some_source");

        lionMockedStatic.when(() -> Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb", "product.page.diff.source.blacklist", String.class))
                .thenReturn(null);

        // Should continue processing
        environmentMockedStatic.when(Environment::isProductEnv).thenReturn(false);
        when(req.getDiffUniqueId()).thenReturn("test_id");

        SamplingConfig samplingConfig = new SamplingConfig();
        samplingConfig.setEnable(false);
        lionMockedStatic.when(() -> Lion.getBean("com.sankuai.dzshoppingguide.detail.gateway", "product.page.diff.sampling.config", SamplingConfig.class))
                .thenReturn(samplingConfig);

        trafficDiffSamplingService.sendMessage(req, envCtx, dealCtx, headerMap);

//        catMockedStatic.verify(() -> Cat.logEvent("Pricecipher", "Pass"));
        verifyNoInteractions(trafficDiffProducer);
    }

    private void setupBasicMocks() {
        tracerMockedStatic.when(Tracer::isTest).thenReturn(false);
        environmentMockedStatic.when(Environment::isProductEnv).thenReturn(false);
        when(req.getDiffReplayFlag()).thenReturn(0);
//        when(req.getPageSource()).thenReturn("poi_page");
        when(req.getDiffUniqueId()).thenReturn("test_unique_id");

        lionMockedStatic.when(() -> Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb", "product.page.diff.source.blacklist", String.class))
                .thenReturn(Collections.emptyList());
    }

    private void setupSuccessfulSampling() {
        SamplingConfig samplingConfig = new SamplingConfig();
        samplingConfig.setEnable(true);
        samplingConfig.setAllCategory(true);
        samplingConfig.setRatio(10000); // 100% sampling
        lionMockedStatic.when(() -> Lion.getBean("com.sankuai.dzshoppingguide.detail.gateway", "product.page.diff.sampling.config", SamplingConfig.class))
                .thenReturn(samplingConfig);

        when(dealCtx.getCategoryId()).thenReturn(1);
        setupRequestMockData();
    }

    private void setupRequestMockData() {
        when(req.getDealgroupid()).thenReturn(12345);
        when(req.getSkuId()).thenReturn("sku123");
        when(req.getPoiid()).thenReturn(67890L);
        when(req.getCityid()).thenReturn(1);
        when(req.getGpsCityId()).thenReturn(2);
        when(req.getClienttype()).thenReturn(3);
        when(req.getUserlng()).thenReturn(116.123);
        when(req.getUserlat()).thenReturn(39.456);
        when(req.getCityLongitude()).thenReturn(116.456);
        when(req.getCityLatitude()).thenReturn(39.789);
        when(req.getPosition()).thenReturn("position1");
        when(req.getRegionid()).thenReturn("100");
        when(req.getExpResults()).thenReturn("exp1,exp2");
        when(req.getLyyuserid()).thenReturn("999");
        when(req.getGpsCoordinateType()).thenReturn("GCJ02");
//        when(req.getPageSource()).thenReturn("poi_page");
        when(req.getFromPage()).thenReturn("homepage");
        when(req.getConvertcolor()).thenReturn(true);
        when(req.getEventpromochannel()).thenReturn("channel1");
        when(req.getPass_param()).thenReturn("pass123");
        when(req.getChannel()).thenReturn("android");
        when(req.getExtParam()).thenReturn("ext123");
        when(req.getCx()).thenReturn("cx123");
        when(req.getMrnversion()).thenReturn("1.0.0");
        when(req.getDealParam()).thenReturn("deal123");
        when(req.getMliveAbTestArgs()).thenReturn("mlive123");
        when(req.getPrivateLiveId()).thenReturn("live123");
        when(req.getSceneType()).thenReturn(1);
        when(req.getPintuanActivityId()).thenReturn("pintuan123");
        when(req.getOrderGroupId()).thenReturn("order123");
        when(req.getInfoContentId()).thenReturn(1213L);
        when(req.getMmcinflate()).thenReturn(1);
        when(req.getMmcuse()).thenReturn(2);
        when(req.getMmcbuy()).thenReturn(3);
        when(req.getMmcfree()).thenReturn(4);
        when(req.getWxVersion()).thenReturn("wx1.0");
        when(req.getOfflineCode()).thenReturn("offline123");
        when(req.getDistributionParam()).thenReturn("dist123");
        when(req.getUserDistributionParam()).thenReturn("userdist123");
        when(req.getMmcPkgVersion()).thenReturn("pkg1.0");
    }
}

