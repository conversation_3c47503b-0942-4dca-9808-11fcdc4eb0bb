package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.publishcategory.DealGroupPublishCategoryQueryService;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.concurrent.Future;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class DealGroupWrapper_PreDealGroupPublishCategoryByIdTest {

    @InjectMocks
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private DealGroupPublishCategoryQueryService dealGroupPublishCategoryQueryServiceFuture;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 preDealGroupPublishCategoryById 方法，异常情况
     */
    @Test
    public void testPreDealGroupPublishCategoryByIdException() throws Throwable {
        // arrange
        int dpId = 1;
        when(dealGroupPublishCategoryQueryServiceFuture.getPublishCategory(dpId)).thenThrow(new RuntimeException());
        // act
        Future result = dealGroupWrapper.preDealGroupPublishCategoryById(dpId);
        // assert
        verify(dealGroupPublishCategoryQueryServiceFuture, times(1)).getPublishCategory(dpId);
        assertNull(result);
    }
}
