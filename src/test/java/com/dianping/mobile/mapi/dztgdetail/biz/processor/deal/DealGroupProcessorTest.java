package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.publishcategory.dto.ChannelDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealGroupProcessorTest {

    @InjectMocks
    private DealGroupProcessor dealGroupProcessor;

    @Mock
    private DealGroupDealDTO dealDTO;

    @Mock
    private DealCtx ctx;

    @Mock
    private DealGroupDTO dealGroupDTO;

    @Mock
    private DealGroupCategoryDTO category;

    private DealGroupProcessor processor = new DealGroupProcessor();

    /**
     * Helper method to invoke the private parseBoolean method using reflection.
     */
    private boolean invokeParseBoolean(DealGroupProcessor processor, String input) throws Throwable {
        try {
            Method method = DealGroupProcessor.class.getDeclaredMethod("parseBoolean", String.class);
            method.setAccessible(true);
            return (boolean) method.invoke(processor, input);
        } catch (Exception e) {
            throw e.getCause();
        }
    }

    private boolean parseBoolean(String string) {
        if (StringUtils.isBlank(string)) {
            return false;
        } else {
            return Boolean.parseBoolean(string);
        }
    }

    private String invokePrivateGetAttr(DealGroupDealDTO dealDTO, String attrName) throws Throwable {
        Method method = DealGroupProcessor.class.getDeclaredMethod("getAttr", DealGroupDealDTO.class, String.class);
        method.setAccessible(true);
        return (String) method.invoke(dealGroupProcessor, dealDTO, attrName);
    }

    private int parseInt(Integer integer) {
        if (integer != null) {
            return integer;
        } else {
            return 0;
        }
    }

    // Helper method to invoke private method using reflection
    private Object invokePrivateMethod(Object object, String methodName, Object... args) throws Throwable {
        try {
            Method method = object.getClass().getDeclaredMethod(methodName, DealCtx.class);
            method.setAccessible(true);
            return method.invoke(object, args);
        } catch (Exception e) {
            throw e.getCause();
        }
    }

    private String getServiceType(DealCtx ctx) {
        return ctx.getDealGroupDTO() == null || ctx.getDealGroupDTO().getCategory() == null ? null : ctx.getDealGroupDTO().getCategory().getServiceType();
    }

    /**
     * Test case for parseBoolean with a null string.
     */
    @Test
    public void testParseBoolean_NullString() throws Throwable {
        // arrange
        DealGroupProcessor processor = new DealGroupProcessor();
        String input = null;
        // act
        boolean result = invokeParseBoolean(processor, input);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for parseBoolean with an empty string.
     */
    @Test
    public void testParseBoolean_EmptyString() throws Throwable {
        // arrange
        DealGroupProcessor processor = new DealGroupProcessor();
        String input = "";
        // act
        boolean result = invokeParseBoolean(processor, input);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for parseBoolean with a whitespace string.
     */
    @Test
    public void testParseBoolean_WhitespaceString() throws Throwable {
        // arrange
        DealGroupProcessor processor = new DealGroupProcessor();
        String input = " ";
        // act
        boolean result = invokeParseBoolean(processor, input);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for parseBoolean with the string "true".
     */
    @Test
    public void testParseBoolean_TrueString() throws Throwable {
        // arrange
        DealGroupProcessor processor = new DealGroupProcessor();
        String input = "true";
        // act
        boolean result = invokeParseBoolean(processor, input);
        // assert
        assertTrue(result);
    }

    /**
     * Test case for parseBoolean with the string "false".
     */
    @Test
    public void testParseBoolean_FalseString() throws Throwable {
        // arrange
        DealGroupProcessor processor = new DealGroupProcessor();
        String input = "false";
        // act
        boolean result = invokeParseBoolean(processor, input);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for parseBoolean with the string "TRUE" (uppercase).
     */
    @Test
    public void testParseBoolean_TrueStringUppercase() throws Throwable {
        // arrange
        DealGroupProcessor processor = new DealGroupProcessor();
        String input = "TRUE";
        // act
        boolean result = invokeParseBoolean(processor, input);
        // assert
        assertTrue(result);
    }

    /**
     * Test case for parseBoolean with the string "FALSE" (uppercase).
     */
    @Test
    public void testParseBoolean_FalseStringUppercase() throws Throwable {
        // arrange
        DealGroupProcessor processor = new DealGroupProcessor();
        String input = "FALSE";
        // act
        boolean result = invokeParseBoolean(processor, input);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for parseBoolean with the string "True" (mixed case).
     */
    @Test
    public void testParseBoolean_TrueStringMixedCase() throws Throwable {
        // arrange
        DealGroupProcessor processor = new DealGroupProcessor();
        String input = "True";
        // act
        boolean result = invokeParseBoolean(processor, input);
        // assert
        assertTrue(result);
    }

    /**
     * Test case for parseBoolean with the string "False" (mixed case).
     */
    @Test
    public void testParseBoolean_FalseStringMixedCase() throws Throwable {
        // arrange
        DealGroupProcessor processor = new DealGroupProcessor();
        String input = "False";
        // act
        boolean result = invokeParseBoolean(processor, input);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for parseBoolean with an invalid string "yes".
     */
    @Test
    public void testParseBoolean_InvalidStringYes() throws Throwable {
        // arrange
        DealGroupProcessor processor = new DealGroupProcessor();
        String input = "yes";
        // act
        boolean result = invokeParseBoolean(processor, input);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for parseBoolean with an invalid string "no".
     */
    @Test
    public void testParseBoolean_InvalidStringNo() throws Throwable {
        // arrange
        DealGroupProcessor processor = new DealGroupProcessor();
        String input = "no";
        // act
        boolean result = invokeParseBoolean(processor, input);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for parseBoolean with an invalid string "1".
     */
    @Test
    public void testParseBoolean_InvalidStringOne() throws Throwable {
        // arrange
        DealGroupProcessor processor = new DealGroupProcessor();
        String input = "1";
        // act
        boolean result = invokeParseBoolean(processor, input);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for parseBoolean with an invalid string "0".
     */
    @Test
    public void testParseBoolean_InvalidStringZero() throws Throwable {
        // arrange
        DealGroupProcessor processor = new DealGroupProcessor();
        String input = "0";
        // act
        boolean result = invokeParseBoolean(processor, input);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for parseBoolean with an invalid string "random".
     */
    @Test
    public void testParseBoolean_InvalidStringRandom() throws Throwable {
        // arrange
        DealGroupProcessor processor = new DealGroupProcessor();
        String input = "random";
        // act
        boolean result = invokeParseBoolean(processor, input);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for when attrs is null.
     */
    @Test
    public void testGetAttrWhenAttrsIsNull() throws Throwable {
        // arrange
        when(dealDTO.getAttrs()).thenReturn(null);
        // act
        String result = invokePrivateGetAttr(dealDTO, "someAttr");
        // assert
        assertEquals("", result);
    }

    /**
     * Test case for when attrs is empty.
     */
    @Test
    public void testGetAttrWhenAttrsIsEmpty() throws Throwable {
        // arrange
        when(dealDTO.getAttrs()).thenReturn(Collections.emptyList());
        // act
        String result = invokePrivateGetAttr(dealDTO, "someAttr");
        // assert
        assertEquals("", result);
    }

    /**
     * Test case for when no matching attrName is found.
     */
    @Test
    public void testGetAttrWhenNoMatchingAttrName() throws Throwable {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("otherAttr");
        attrDTO.setValue(Arrays.asList("value1", "value2"));
        when(dealDTO.getAttrs()).thenReturn(Arrays.asList(attrDTO));
        // act
        String result = invokePrivateGetAttr(dealDTO, "someAttr");
        // assert
        assertEquals("", result);
    }

    /**
     * Test case for when matching attrName is found but value list is empty.
     */
    @Test
    public void testGetAttrWhenMatchingAttrNameWithEmptyValueList() throws Throwable {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("someAttr");
        attrDTO.setValue(Collections.emptyList());
        when(dealDTO.getAttrs()).thenReturn(Arrays.asList(attrDTO));
        // act
        String result = invokePrivateGetAttr(dealDTO, "someAttr");
        // assert
        assertEquals("", result);
    }

    /**
     * Test case for when matching attrName is found and value list is not empty.
     */
    @Test
    public void testGetAttrWhenMatchingAttrNameWithNonEmptyValueList() throws Throwable {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("someAttr");
        attrDTO.setValue(Arrays.asList("value1", "value2"));
        when(dealDTO.getAttrs()).thenReturn(Arrays.asList(attrDTO));
        // act
        String result = invokePrivateGetAttr(dealDTO, "someAttr");
        // assert
        assertEquals("value1", result);
    }

    /**
     * Test parseInt with positive number
     */
    @Test
    public void testParseInt_WithPositiveNumber() throws Throwable {
        // arrange
        DealGroupProcessor processor = new DealGroupProcessor();
        Integer input = 123;
        // act
        int result = parseInt(input);
        // assert
        assertEquals(123, result);
    }

    /**
     * Test parseInt with negative number
     */
    @Test
    public void testParseInt_WithNegativeNumber() throws Throwable {
        // arrange
        DealGroupProcessor processor = new DealGroupProcessor();
        Integer input = -456;
        // act
        int result = parseInt(input);
        // assert
        assertEquals(-456, result);
    }

    /**
     * Test parseInt with zero
     */
    @Test
    public void testParseInt_WithZero() throws Throwable {
        // arrange
        DealGroupProcessor processor = new DealGroupProcessor();
        Integer input = 0;
        // act
        int result = parseInt(input);
        // assert
        assertEquals(0, result);
    }

    /**
     * Test parseInt with null input
     */
    @Test
    public void testParseInt_WithNull() throws Throwable {
        // arrange
        DealGroupProcessor processor = new DealGroupProcessor();
        Integer input = null;
        // act
        int result = parseInt(input);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试正常场景：dealGroupDTO 不为 null，且 dealGroupDTO.getAttrs() 不为 null，且 attrs 列表不为空
     */
    @Test
    public void testTrans2OldAttrDTONormalCase() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("name");
        // Corrected to use List<String>
        attrDTO.setValue(Collections.singletonList("value"));
        // Corrected to use Integer
        attrDTO.setSource(1);
        attrs.add(attrDTO);
        when(dealGroupDTO.getAttrs()).thenReturn(attrs);
        DealGroupProcessor processor = new DealGroupProcessor();
        // act
        Method method = DealGroupProcessor.class.getDeclaredMethod("trans2OldAttrDTO", DealGroupDTO.class);
        method.setAccessible(true);
        List<AttributeDTO> result = (List<AttributeDTO>) method.invoke(processor, dealGroupDTO);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("name", result.get(0).getName());
        assertEquals(Collections.singletonList("value"), result.get(0).getValue());
        assertEquals(1, result.get(0).getSource());
    }

    /**
     * 测试正常场景：dealGroupDTO 不为 null，且 dealGroupDTO.getAttrs() 不为 null，但 attrs 列表为空
     */
    @Test
    public void testTrans2OldAttrDTOEmptyAttrs() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getAttrs()).thenReturn(Collections.emptyList());
        DealGroupProcessor processor = new DealGroupProcessor();
        // act
        Method method = DealGroupProcessor.class.getDeclaredMethod("trans2OldAttrDTO", DealGroupDTO.class);
        method.setAccessible(true);
        List<AttributeDTO> result = (List<AttributeDTO>) method.invoke(processor, dealGroupDTO);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试边界场景：dealGroupDTO 为 null
     */
    @Test
    public void testTrans2OldAttrDTONullDealGroupDTO() throws Throwable {
        // arrange
        DealGroupProcessor processor = new DealGroupProcessor();
        // act
        Method method = DealGroupProcessor.class.getDeclaredMethod("trans2OldAttrDTO", DealGroupDTO.class);
        method.setAccessible(true);
        List<AttributeDTO> result = (List<AttributeDTO>) method.invoke(processor, (DealGroupDTO) null);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试边界场景：dealGroupDTO 不为 null，但 dealGroupDTO.getAttrs() 为 null
     */
    @Test
    public void testTrans2OldAttrDTONullAttrs() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getAttrs()).thenReturn(null);
        DealGroupProcessor processor = new DealGroupProcessor();
        // act
        Method method = DealGroupProcessor.class.getDeclaredMethod("trans2OldAttrDTO", DealGroupDTO.class);
        method.setAccessible(true);
        List<AttributeDTO> result = (List<AttributeDTO>) method.invoke(processor, dealGroupDTO);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试正常情况：dealGroupDTO 和 dealGroupDTO.getChannel() 都不为 null
     */
    @Test
    public void testTrans2OldChannelDTONormalCase() throws Throwable {
        // arrange
        DealGroupProcessor processor = new DealGroupProcessor();
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        com.sankuai.general.product.query.center.client.dto.DealGroupChannelDTO channelDTO = mock(com.sankuai.general.product.query.center.client.dto.DealGroupChannelDTO.class);
        when(dealGroupDTO.getChannel()).thenReturn(channelDTO);
        when(dealGroupDTO.getDpDealGroupIdInt()).thenReturn(123);
        when(channelDTO.getChannelId()).thenReturn(456);
        when(channelDTO.getChannelEn()).thenReturn("en");
        when(channelDTO.getChannelCn()).thenReturn("cn");
        when(channelDTO.getChannelGroupId()).thenReturn(789);
        when(channelDTO.getChannelGroupEn()).thenReturn("groupEn");
        when(channelDTO.getChannelGroupCn()).thenReturn("groupCn");
        // act
        DealGroupChannelDTO result = processor.trans2OldChannelDTO(dealGroupDTO);
        // assert
        assertNotNull(result);
        assertEquals(123, result.getDealGroupId());
        assertEquals(456, result.getChannelDTO().getChannelId());
        assertEquals("en", result.getChannelDTO().getChannelEn());
        assertEquals("cn", result.getChannelDTO().getChannelCn());
        assertEquals(789, result.getChannelDTO().getChannelGroupId());
        assertEquals("groupEn", result.getChannelDTO().getChannelGroupEn());
        assertEquals("groupCn", result.getChannelDTO().getChannelGroupCn());
    }

    /**
     * 测试异常情况：dealGroupDTO 为 null
     */
    @Test
    public void testTrans2OldChannelDTONullDealGroupDTO() throws Throwable {
        // arrange
        DealGroupProcessor processor = new DealGroupProcessor();
        // act
        DealGroupChannelDTO result = processor.trans2OldChannelDTO(null);
        // assert
        assertNull(result);
    }

    /**
     * 测试异常情况：dealGroupDTO.getChannel() 为 null
     */
    @Test
    public void testTrans2OldChannelDTONullChannel() throws Throwable {
        // arrange
        DealGroupProcessor processor = new DealGroupProcessor();
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getChannel()).thenReturn(null);
        // act
        DealGroupChannelDTO result = processor.trans2OldChannelDTO(dealGroupDTO);
        // assert
        assertNull(result);
    }

    /**
     * 测试场景：ctx 为 null
     * 预期结果：抛出 NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testGetServiceTypeCtxIsNull() throws Throwable {
        // arrange
        DealCtx nullCtx = null;
        // act
        invokePrivateMethod(processor, "getServiceType", nullCtx);
        // assert
        // 预期抛出 NullPointerException
    }

    /**
     * 测试场景：ctx.getDealGroupDTO() 为 null
     * 预期结果：返回 null
     */
    @Test
    public void testGetServiceTypeDealGroupDTOIsNull() throws Throwable {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(null);
        // act
        String result = (String) invokePrivateMethod(processor, "getServiceType", ctx);
        // assert
        assertNull(result);
    }

    /**
     * 测试场景：ctx.getDealGroupDTO().getCategory() 为 null
     * 预期结果：返回 null
     */
    @Test
    public void testGetServiceTypeCategoryIsNull() throws Throwable {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(null);
        // act
        String result = (String) invokePrivateMethod(processor, "getServiceType", ctx);
        // assert
        assertNull(result);
    }

    /**
     * 测试场景：ctx.getDealGroupDTO().getCategory().getServiceType() 不为 null
     * 预期结果：返回 serviceType 的值
     */
    @Test
    public void testGetServiceTypeServiceTypeIsNotNull() throws Throwable {
        // arrange
        String expectedServiceType = "SERVICE_TYPE";
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(category.getServiceType()).thenReturn(expectedServiceType);
        // act
        String result = (String) invokePrivateMethod(processor, "getServiceType", ctx);
        // assert
        assertEquals(expectedServiceType, result);
    }
}
