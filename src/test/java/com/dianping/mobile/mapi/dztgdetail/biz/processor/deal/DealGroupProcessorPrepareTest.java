package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import java.util.concurrent.Future;
import org.junit.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class DealGroupProcessorPrepareTest {

    @InjectMocks
    private DealGroupProcessor dealGroupProcessor;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private DealCtx ctx;

    @Mock
    private Future channelFuture;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(ctx.getFutureCtx()).thenReturn(mock(FutureCtx.class));
    }

    /**
     * 测试当 enableQueryCenterForMainApi 返回 true 时，prepare 方法直接返回，不执行后续逻辑
     */
    @Test
    public void testPrepareWhenQueryCenterEnabled() throws Throwable {
        // arrange
        when(GreyUtils.enableQueryCenterForMainApi(ctx)).thenReturn(true);
        // act
        dealGroupProcessor.prepare(ctx);
        // assert
        verify(dealGroupWrapper, never()).preDealGroupChannelById(anyInt());
        verify(dealGroupWrapper, never()).preDealGroupBase(anyInt());
        verify(dealGroupWrapper, never()).preAttrs(anyInt(), anyList());
    }

    /**
     * 测试当 enableQueryCenterForMainApi 返回 false 时，prepare 方法正常执行并设置 Future
     */
    @Test
    public void testPrepareWhenQueryCenterDisabled() throws Throwable {
        // arrange
        when(GreyUtils.enableQueryCenterForMainApi(ctx)).thenReturn(false);
        when(ctx.getDpId()).thenReturn(123);
        when(dealGroupWrapper.preDealGroupChannelById(eq(123))).thenReturn(channelFuture);
        when(dealGroupWrapper.preDealGroupBase(eq(123))).thenReturn(channelFuture);
        when(dealGroupWrapper.preAttrs(eq(123), anyList())).thenReturn(channelFuture);
        // act
        dealGroupProcessor.prepare(ctx);
        // assert
        verify(dealGroupWrapper).preDealGroupChannelById(123);
        verify(dealGroupWrapper).preDealGroupBase(123);
        verify(dealGroupWrapper).preAttrs(eq(123), anyList());
        verify(ctx.getFutureCtx()).setChannelFuture(channelFuture);
        verify(ctx.getFutureCtx()).setDealGroupFuture(channelFuture);
        verify(ctx.getFutureCtx()).setAttrFuture(channelFuture);
    }

    /**
     * 测试当 preDealGroupChannelById 抛出异常时，prepare 方法继续执行并设置其他 Future
     */
    @Test(expected = RuntimeException.class)
    public void testPrepareWhenPreDealGroupChannelByIdThrowsException() throws Throwable {
        // arrange
        when(GreyUtils.enableQueryCenterForMainApi(ctx)).thenReturn(false);
        when(ctx.getDpId()).thenReturn(123);
        when(dealGroupWrapper.preDealGroupChannelById(eq(123))).thenThrow(new RuntimeException("Error"));
        when(dealGroupWrapper.preDealGroupBase(eq(123))).thenReturn(channelFuture);
        when(dealGroupWrapper.preAttrs(eq(123), anyList())).thenReturn(channelFuture);
        // act
        dealGroupProcessor.prepare(ctx);
        // assert
        verify(dealGroupWrapper).preDealGroupChannelById(123);
        verify(dealGroupWrapper).preDealGroupBase(123);
        verify(dealGroupWrapper).preAttrs(eq(123), anyList());
        verify(ctx.getFutureCtx(), never()).setChannelFuture(any());
        verify(ctx.getFutureCtx()).setDealGroupFuture(channelFuture);
        verify(ctx.getFutureCtx()).setAttrFuture(channelFuture);
    }

    /**
     * 测试当 preDealGroupBase 抛出异常时，prepare 方法继续执行并设置其他 Future
     */
    @Test(expected = RuntimeException.class)
    public void testPrepareWhenPreDealGroupBaseThrowsException() throws Throwable {
        // arrange
        when(GreyUtils.enableQueryCenterForMainApi(ctx)).thenReturn(false);
        when(ctx.getDpId()).thenReturn(123);
        when(dealGroupWrapper.preDealGroupChannelById(eq(123))).thenReturn(channelFuture);
        when(dealGroupWrapper.preDealGroupBase(eq(123))).thenThrow(new RuntimeException("Error"));
        when(dealGroupWrapper.preAttrs(eq(123), anyList())).thenReturn(channelFuture);
        // act
        dealGroupProcessor.prepare(ctx);
        // assert
        verify(dealGroupWrapper).preDealGroupChannelById(123);
        verify(dealGroupWrapper).preDealGroupBase(123);
        verify(dealGroupWrapper).preAttrs(eq(123), anyList());
        verify(ctx.getFutureCtx()).setChannelFuture(channelFuture);
        verify(ctx.getFutureCtx(), never()).setDealGroupFuture(any());
        verify(ctx.getFutureCtx()).setAttrFuture(channelFuture);
    }

    /**
     * 测试当 preAttrs 抛出异常时，prepare 方法继续执行并设置其他 Future
     */
    @Test(expected = RuntimeException.class)
    public void testPrepareWhenPreAttrsThrowsException() throws Throwable {
        // arrange
        when(GreyUtils.enableQueryCenterForMainApi(ctx)).thenReturn(false);
        when(ctx.getDpId()).thenReturn(123);
        when(dealGroupWrapper.preDealGroupChannelById(eq(123))).thenReturn(channelFuture);
        when(dealGroupWrapper.preDealGroupBase(eq(123))).thenReturn(channelFuture);
        when(dealGroupWrapper.preAttrs(eq(123), anyList())).thenThrow(new RuntimeException("Error"));
        // act
        dealGroupProcessor.prepare(ctx);
        // assert
        verify(dealGroupWrapper).preDealGroupChannelById(123);
        verify(dealGroupWrapper).preDealGroupBase(123);
        verify(dealGroupWrapper).preAttrs(eq(123), anyList());
        verify(ctx.getFutureCtx()).setChannelFuture(channelFuture);
        verify(ctx.getFutureCtx()).setDealGroupFuture(channelFuture);
        verify(ctx.getFutureCtx(), never()).setAttrFuture(any());
    }

}
