package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.attribute.dto.AttributeDTO;
import java.util.Arrays;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealAttrHelper_PartHolidayAvailableTest {

    /**
     * 测试 attrs 为 null 的情况
     */
    @Test
    public void testPartHolidayAvailableNullAttrs() throws Throwable {
        assertFalse(DealAttrHelper.partHolidayAvailable(null));
    }

    /**
     * 测试 attrs 为空列表的情况
     */
    @Test
    public void testPartHolidayAvailableEmptyAttrs() throws Throwable {
        assertFalse(DealAttrHelper.partHolidayAvailable(Collections.emptyList()));
    }

    /**
     * 测试 attrs 不包含 HOLIDAY_AVAILABLE 属性的情况
     */
    @Test
    public void testPartHolidayAvailableNoHolidayAvailable() throws Throwable {
        AttributeDTO attr = new AttributeDTO();
        attr.setName("OTHER");
        attr.setValue(Arrays.asList("1", "2", "3"));
        assertFalse(DealAttrHelper.partHolidayAvailable(Collections.singletonList(attr)));
    }

    /**
     * 测试 attrs 包含 HOLIDAY_AVAILABLE 属性，但其值不包含 "0"、"101" 和 "102" 的情况
     */
    @Test
    public void testPartHolidayAvailableInvalidHolidayAvailable() throws Throwable {
        AttributeDTO attr = new AttributeDTO();
        // Corrected reference
        attr.setName(com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys.HOLIDAY_AVAILABLE);
        attr.setValue(Arrays.asList("1", "2", "3"));
        assertFalse(DealAttrHelper.partHolidayAvailable(Collections.singletonList(attr)));
    }

    /**
     * 测试 attrs 包含 HOLIDAY_AVAILABLE 属性，其值包含 "0"、"101" 和 "102" 的情况
     */
    @Test
    public void testPartHolidayAvailableValidHolidayAvailable() throws Throwable {
        AttributeDTO attr = new AttributeDTO();
        // Corrected reference
        attr.setName(com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys.HOLIDAY_AVAILABLE);
        attr.setValue(Arrays.asList("0", "101", "102"));
        assertTrue(DealAttrHelper.partHolidayAvailable(Collections.singletonList(attr)));
    }

    /**
     * Test case when HOLIDAY_AVAILABLE has multiple values
     * Expected to return false
     */
    @Test
    public void testAllDayAvailable_MultipleValues_ReturnsFalse() throws Throwable {
        // arrange
        AttributeDTO attr = new AttributeDTO();
        // Ensure the name matches the key in DealAttrKeys
        // Assuming DealAttrKeys.HOLIDAY_AVAILABLE is "HOLIDAY_AVAILABLE"
        attr.setName("HOLIDAY_AVAILABLE");
        // Ensure the value is a list containing multiple strings
        attr.setValue(Arrays.asList("1", "2"));
        // act
        boolean result = DealAttrHelper.allDayAvailable(Collections.singletonList(attr));
        // assert
        assertFalse("Should return false when HOLIDAY_AVAILABLE has multiple values", result);
    }

    /**
     * Test case when HOLIDAY_AVAILABLE has single value not equal to "1"
     * Expected to return false
     */
    @Test
    public void testAllDayAvailable_SingleValueNotOne_ReturnsFalse() throws Throwable {
        // arrange
        AttributeDTO attr = new AttributeDTO();
        // Ensure the name matches the key in DealAttrKeys
        // Assuming DealAttrKeys.HOLIDAY_AVAILABLE is "HOLIDAY_AVAILABLE"
        attr.setName("HOLIDAY_AVAILABLE");
        // Ensure the value is a list containing a single string not equal to "1"
        attr.setValue(Collections.singletonList("0"));
        // act
        boolean result = DealAttrHelper.allDayAvailable(Collections.singletonList(attr));
        // assert
        assertFalse("Should return false when HOLIDAY_AVAILABLE has single value not '1'", result);
    }
}
