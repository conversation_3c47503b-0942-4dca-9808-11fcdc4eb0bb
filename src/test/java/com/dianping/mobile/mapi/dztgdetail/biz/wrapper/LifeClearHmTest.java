package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.dianping.appkit.constants.BizGroup;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.haima.client.HaimaClient;
import com.dianping.haima.client.request.HaimaRequest;
import com.dianping.haima.client.response.HaimaResponse;
import com.dianping.haima.entity.haima.HaimaConfig;
import com.dianping.haima.entity.haima.HaimaContent;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.entity.*;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
@RunWith(MockitoJUnitRunner.class)
public class LifeClearHmTest {

    @InjectMocks
    private HaimaWrapper haimaWrapper;

    @Mock
    private HaimaClient haimaClient;

    private MockedStatic<Lion> mocked;

    @Mock
    private HaimaWrapper haimaWrapperMock;

    @Before
    public void setUp() {
        mocked = mockStatic(Lion.class);
    }

    @After
    public void teardown() {
        this.mocked.close();
    }

    private void setUpCommonMocks(boolean isMT, HaimaResponse mockResponse) {
        HaimaRequest request = new HaimaRequest();
        request.setBizGroup(isMT ? BizGroup.MEITUAN : BizGroup.DIANPING);
    }

    /**
     * 正常情况的测试用例
     */
    @Test
    public void testQueryNailFilterConfigNormal() throws Throwable {
        // arrange
        NailFilterLionConfig config = new NailFilterLionConfig();
        config.setSceneKey("nail_filter_config");
        config.setConfigId("configId");
        config.setContentId("2030");
        config.setExcludeIds(Arrays.asList("100", "1000"));
        mocked.when(() -> Lion.getBean(anyString(), anyString(), any())).thenReturn(config);
        HaimaResponse response = new HaimaResponse();
        response.setStatusCode(200);
        HaimaConfig haimaConfig = new HaimaConfig();
        HaimaContent content = new HaimaContent();
        content.setContentId(Integer.parseInt(config.getContentId()));
        content.setExtJson("{\"third_tab_detail\": \"[{}]\", \"second_tab_id\": \"secondTabId\"}");
        haimaConfig.setContents(Collections.singletonList(content));
        response.setData(Collections.singletonList(haimaConfig));
        when(haimaClient.query(any())).thenReturn(response);
        // act
        List<HaiMaNailFilterConfig> result = haimaWrapper.queryNailFilterConfig();
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    /**
     * 测试queryNailFilterConfig方法，当HaimaResponse为null时
     */
    @Test
    public void testQueryNailFilterConfigWhenResponseIsNull() throws Throwable {
        mocked.when(() -> Lion.getBean(anyString(), anyString(), any())).thenReturn(new NailFilterLionConfig());
        // arrange
        when(haimaClient.query(any())).thenReturn(null);
        // act
        List<HaiMaNailFilterConfig> result = haimaWrapper.queryNailFilterConfig();
        // assert
        assertNull(result);
    }

    /**
     *
     */
    @Test
    public void testInsurance() {
        String json = "{\n" + "    \"statusCode\": 206,\n" + "    \"data\": [\n" + "        {\n" + "            \"scene\": \"youlexian\",\n" + "            \"configId\": 297182,\n" + "            \"eleGroup\": 0,\n" + "            \"eleOrder\": 1,\n" + "            \"extJson\": \"{}\",\n" + "            \"contents\": [\n" + "                {\n" + "                    \"configId\": 297182,\n" + "                    \"contentId\": 2977432,\n" + "                    \"extJson\": \"{\\\"back_cat1_code\\\":\\\"\\\",\\\"start_time\\\":\\\"2023.12.01\\\",\\\"city\\\":\\\"\\\",\\\"end_time\\\":\\\"\\\",\\\"front_cat0_code\\\":\\\"\\\",\\\"dim_cat1_id\\\":\\\"1805\\\",\\\"dim_cat1_name\\\":\\\"台球\\\",\\\"status\\\":\\\"保障中\\\",\\\"back_cat0_code\\\":\\\"\\\",\\\"order\\\":0}\",\n" + "                    \"createTime\": 1712827300000,\n" + "                    \"updateTime\": 1712827300000\n" + "                }\n" + "            ],\n" + "            \"createTime\": 1712827300000,\n" + "            \"updateTime\": 1712827300000\n" + "        }\n" + "    ],\n" + "    \"message\": \"当前数据为RC数据!\",\n" + "    \"extraInfo\": {\n" + "        \"isTesterUser\": false,\n" + "        \"isNewProcess\": true,\n" + "        \"packageId\": 574791,\n" + "        \"isRc\": true,\n" + "        \"originPackageId\": 574791,\n" + "        \"CacheType\": \"LocalCache\"\n" + "    },\n" + "    \"success\": true\n" + "}";
        HaimaResponse fake = JsonCodec.decode(json, HaimaResponse.class);
        when(haimaClient.query(any())).thenReturn(fake);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(DztgClientTypeEnum.MEITUAN_APP.getCode());
        DealGroupChannelDTO dealGroupChannelDTO = new DealGroupChannelDTO();
        dealGroupChannelDTO.setCategoryId(1805);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setChannelDTO(dealGroupChannelDTO);
        boolean hit = !haimaWrapper.ifHitInsurance(ctx);//团详情页游乐险标签下掉了
        assertTrue(hit);
    }

    /**
     * 测试queryNailFilterConfig方法，当HaimaResponse的isSuccess为false时
     */
    @Test
    public void testQueryNailFilterConfigWhenResponseIsNotSuccess() throws Throwable {
        mocked.when(() -> Lion.getBean(anyString(), anyString(), any())).thenReturn(new NailFilterLionConfig());
        // arrange
        HaimaResponse response = new HaimaResponse();
        response.setStatusCode(100);
        when(haimaClient.query(any())).thenReturn(response);
        // act
        List<HaiMaNailFilterConfig> result = haimaWrapper.queryNailFilterConfig();
        // assert
        assertNull(result);
    }

    /**
     * 测试queryNailFilterConfig方法，当HaimaResponse的data为空时
     */
    @Test
    public void testQueryNailFilterConfigWhenResponseDataIsEmpty() throws Throwable {
        mocked.when(() -> Lion.getBean(anyString(), anyString(), any())).thenReturn(new NailFilterLionConfig());
        // arrange
        HaimaResponse response = new HaimaResponse();
        response.setStatusCode(200);
        response.setData(Collections.emptyList());
        when(haimaClient.query(any())).thenReturn(response);
        // act
        List<HaiMaNailFilterConfig> result = haimaWrapper.queryNailFilterConfig();
        // assert
        assertNull(result);
    }

    /**
     * 测试queryNailFilterConfig方法，当HaimaConfig的contents为空时
     */
    @Test
    public void testQueryNailFilterConfigWhenContentsIsEmpty() throws Throwable {
        mocked.when(() -> Lion.getBean(anyString(), anyString(), any())).thenReturn(new NailFilterLionConfig());
        // arrange
        HaimaResponse response = new HaimaResponse();
        response.setStatusCode(200);
        HaimaConfig haimaConfig = new HaimaConfig();
        haimaConfig.setContents(Collections.emptyList());
        response.setData(Collections.singletonList(haimaConfig));
        when(haimaClient.query(any())).thenReturn(response);
        // act
        List<HaiMaNailFilterConfig> result = haimaWrapper.queryNailFilterConfig();
        // assert
        assertNull(result);
    }

    /**
     * 测试海马配置的热门款式为空的情况
     */
    @Test
    public void testQueryHotNailStyle_HaiMaNailFilterConfigsIsEmpty() throws Throwable {
        mocked.when(() -> Lion.getBean(anyString(), anyString(), any())).thenReturn(new NailFilterLionConfig());
        // arrange
        HaimaResponse response = new HaimaResponse();
        response.setStatusCode(200);
        response.setData(new ArrayList<>());
        when(haimaClient.query(any(HaimaRequest.class))).thenReturn(response);
        // act
        List<HaiMaNailFilterConfig> result = haimaWrapper.queryHotBeautyNailConfig();
        // assert
        assertNull(result);
    }

    /**
     * 测试海马配置的热门款式不为空的情况
     */
    @Test
    public void testQueryHotNailStyle_HaiMaNailFilterConfigsIsNotEmpty() throws Throwable {
        // arrange
        NailFilterLionConfig config = new NailFilterLionConfig();
        config.setSceneKey("nail_filter_config");
        config.setConfigId("162448");
        config.setContentId("2932789");
        config.setExcludeIds(Arrays.asList("100", "1000"));
        config.setExcludeNames(Lists.newArrayList("全部", "我的收藏"));
        config.setFirstTabName("美甲");
        mocked.when(() -> Lion.getBean(anyString(), anyString(), any())).thenReturn(config);
        HaimaResponse response = new HaimaResponse();
        response.setStatusCode(200);
        HaimaConfig haimaConfig = new HaimaConfig();
        haimaConfig.setExtJson("{\n" + "    \"first_tab_id\": \"1890\",\n" + "    \"first_tab_sort\": \"2\",\n" + "    \"first_tab_name\": \"美甲\"\n" + "}");
        HaimaContent content = new HaimaContent();
        content.setConfigId(162448);
        content.setContentId(2932789);
        content.setExtJson("{\n" + "    \"third_tab_detail\": \"[ { \\\"name\\\":\\\"我的收藏\\\", \\\"id\\\":\\\"1000\\\" }, { " + "\\\"name\\\":\\\"全部\\\", \\\"id\\\":\\\"100\\\" },  { " + "\\\"name\\\":\\\"猫眼美甲\\\", \\\"id\\\":\\\"1946\\\", \\\"filterIds\\\":\\\"1946\\\", \\\"icon\\\":\\\"https://p0.meituan.net/travelcube/fb0b54037531fd093b3f339ddb6f2899547254.jpg\\\" } ]\",\n" + "    \"second_tab_id\": \"*********\"\n" + "}");
        haimaConfig.setContents(Collections.singletonList(content));
        response.setData(Collections.singletonList(haimaConfig));
        when(haimaClient.query(any())).thenReturn(response);
        // act
        List<HaiMaNailFilterConfig> result = haimaWrapper.queryHotBeautyNailConfig();
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    /**
     * 测试场景：queryInsuranceCategories 返回 null
     */
    @Test
    public void testGetStrategyIfHitInsuranceWhenQueryInsuranceCategoriesReturnNull() throws Throwable {
        // arrange
        // act
        String result = haimaWrapper.getStrategyIfHitInsurance(true, 1);
        // assert
        assertNull(result);
    }

    /**
     * 测试场景：queryInsuranceCategories 返回的列表为空
     */
    @Test
    public void testGetStrategyIfHitInsuranceWhenQueryInsuranceCategoriesReturnEmptyList() throws Throwable {
        // arrange
        // act
        String result = haimaWrapper.getStrategyIfHitInsurance(true, 1);
        // assert
        assertNull(result);
    }

    /**
     * 测试场景：queryInsuranceCategories 返回的列表不包含 category
     */
    @Test
    public void testGetStrategyIfHitInsuranceWhenQueryInsuranceCategoriesReturnListNotContainCategory() throws Throwable {
        // arrange
        // act
        String result = haimaWrapper.getStrategyIfHitInsurance(true, 1);
        // assert
        assertNull(result);
    }

    @Test
    public void testQueryInsuranceCategoriesResponseNull() throws Throwable {
        // Setup mock response as null
        setUpCommonMocks(true, null);
        List<Integer> result = haimaWrapper.queryInsuranceCategories(true);
        assertNull(result);
    }

    @Test
    public void testQueryInsuranceCategoriesResponseNotSuccess() throws Throwable {
        // Setup mock response with unsuccessful status code
        HaimaResponse response = new HaimaResponse(400);
        setUpCommonMocks(true, response);
        List<Integer> result = haimaWrapper.queryInsuranceCategories(true);
        assertNull(result);
    }

    @Test
    public void testQueryInsuranceCategoriesDataEmpty() throws Throwable {
        // Setup mock response with empty data
        HaimaResponse response = new HaimaResponse(200, Collections.emptyList(), "", null);
        setUpCommonMocks(true, response);
        List<Integer> result = haimaWrapper.queryInsuranceCategories(true);
        assertNull(result);
    }


    @Test
    public void cleaningProductInformationTrue() throws Throwable {
        CleaningProductLionConfig config = new CleaningProductLionConfig();
        String str = "{\n" +
                "  \"isEnable\": true,\n" +
                "  \"sceneKey\": \"ty_test02\",\n" +
                "  \"configId\": \"self_own_product\",\n" +
                "  \"contentId\": \"exampleContentId\",\n" +
                "  \"excludeIds\": [\n" +
                "    \"id1\",\n" +
                "    \"id2\",\n" +
                "    \"id3\"\n" +
                "  ],\n" +
                "  \"excludeNames\": [\n" +
                "    \"name1\",\n" +
                "    \"name2\",\n" +
                "    \"name3\"\n" +
                "  ],\n" +
                "  \"firstTabName\": \"exampleFirstTabName\"\n" +
                "}\n";
        config = JSON.parseObject(str, CleaningProductLionConfig.class);
        List<LifeClearHaiMaConfig> result = haimaWrapper.cleaningProductInformation(config);
        assertNull(result);
    }

    @Test
    public void cleaningProductInformationTrue1() throws Throwable {
        HaimaResponse config = new HaimaResponse();
        String str = "{\n" +
                "  \"statusCode\": 200,\n" +
                "  \"message\": null,\n" +
                "  \"data\": [\n" +
                "    {\n" +
                "      \"scene\": \"ty_test02\",\n" +
                "      \"configId\": 100670,\n" +
                "      \"extJson\": \"{\\\"Service_Items\\\":\\\"3小时陪玩\\\",\\\"Service_order\\\":\\\"2\\\"}\",\n" +
                "      \"extJsonMap\": {\n" +
                "        \"Service_Items\": \"3小时陪玩\",\n" +
                "        \"Service_order\": \"2\"\n" +
                "      },\n" +
                "      \"contents\": [\n" +
                "        {\n" +
                "          \"configId\": 100670,\n" +
                "          \"contentId\": 759459,\n" +
                "          \"createTime\": 1722174980000,\n" +
                "          \"updateTime\": 1722174980000,\n" +
                "          \"content\": \"{\\\"Service_title\\\":\\\"服务人数\\\",\\\"city\\\":\\\"\\\",\\\"Service_content\\\":\\\"1人\\\",\\\"order\\\":0}\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"configId\": 100670,\n" +
                "          \"contentId\": 759460,\n" +
                "          \"createTime\": 1722174980000,\n" +
                "          \"updateTime\": 1722174980000,\n" +
                "          \"content\": \"{\\\"Service_title\\\":\\\"服务内容\\\",\\\"city\\\":\\\"\\\",\\\"Service_content\\\":\\\"桌游,网游,单机\\\",\\\"order\\\":0}\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"createTime\": 1722174980000,\n" +
                "      \"updateTime\": 1722175380000\n" +
                "    },\n" +
                "    {\n" +
                "      \"scene\": \"ty_test02\",\n" +
                "      \"configId\": 100671,\n" +
                "      \"extJson\": \"{\\\"Service_Items\\\":\\\"4小时K歌\\\",\\\"Service_order\\\":\\\"3\\\"}\",\n" +
                "      \"extJsonMap\": {\n" +
                "        \"Service_Items\": \"4小时K歌\",\n" +
                "        \"Service_order\": \"3\"\n" +
                "      },\n" +
                "      \"contents\": [\n" +
                "        {\n" +
                "          \"configId\": 100671,\n" +
                "          \"contentId\": 759461,\n" +
                "          \"createTime\": 1722175380000,\n" +
                "          \"updateTime\": 1722175380000,\n" +
                "          \"content\": \"{\\\"Service_title\\\":\\\"服务人员\\\",\\\"city\\\":\\\"\\\",\\\"Service_content\\\":\\\"2人\\\",\\\"order\\\":0}\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"configId\": 100671,\n" +
                "          \"contentId\": 759462,\n" +
                "          \"createTime\": 1722175380000,\n" +
                "          \"updateTime\": 1722175380000,\n" +
                "          \"content\": \"{\\\"Service_title\\\":\\\"服务内容\\\",\\\"city\\\":\\\"\\\",\\\"Service_content\\\":\\\"唱歌，才艺表演\\\",\\\"order\\\":0}\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"createTime\": 1722175380000,\n" +
                "      \"updateTime\": 1722175380000\n" +
                "    },\n" +
                "    {\n" +
                "      \"scene\": \"ty_test02\",\n" +
                "      \"configId\": 100669,\n" +
                "      \"extJson\": \"{\\\"Service_Items\\\":\\\"保洁自营\\\",\\\"Service_order\\\":\\\"1\\\"}\",\n" +
                "      \"extJsonMap\": {\n" +
                "        \"Service_Items\": \"保洁自营\",\n" +
                "        \"Service_order\": \"1\"\n" +
                "      },\n" +
                "      \"contents\": [\n" +
                "        {\n" +
                "          \"configId\": 100669,\n" +
                "          \"contentId\": 759457,\n" +
                "          \"createTime\": 1722174980000,\n" +
                "          \"updateTime\": 1722174980000,\n" +
                "          \"content\": \"{\\\"Service_title\\\":\\\"服务人员\\\",\\\"city\\\":\\\"\\\",\\\"Service_content\\\":\\\"1人\\\",\\\"order\\\":0}\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"configId\": 100669,\n" +
                "          \"contentId\": 759458,\n" +
                "          \"createTime\": 1722174980000,\n" +
                "          \"updateTime\": 1722335533000,\n" +
                "          \"content\": \"{\\\"Service_title\\\":\\\"服务内容\\\",\\\"city\\\":\\\"\\\",\\\"Service_content\\\":\\\"厨房,餐厅,卧室,测试房间\\\",\\\"order\\\":0}\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"createTime\": 1722174980000,\n" +
                "      \"updateTime\": 1722404089000\n" +
                "    }\n" +
                "  ],\n" +
                "  \"extraInfo\": {\n" +
                "    \"isTesterUser\": false,\n" +
                "    \"isNewProcess\": true,\n" +
                "    \"pkgUpdateTime\": 1722404089000,\n" +
                "    \"checksum\": \"5835b68bf15cc5bf800e00befca02312\",\n" +
                "    \"packageId\": 419116,\n" +
                "    \"cacheType\": \"LOCALCache\",\n" +
                "    \"bizCacheGroup\": 0,\n" +
                "    \"originPackageId\": 419116\n" +
                "  },\n" +
                "  \"success\": true\n" +
                "}";
        config = JSON.parseObject(str, HaimaResponse.class);
        List<LifeClearHaiMaConfig> result = haimaWrapper.getLifeClearHaiMaiConfigs(config);
        assertNotNull(result);
    }


    @Test
    public void getLifeClearHaiMaiConfigDtosTrue() throws Throwable {
        HaimaConfig item = new HaimaConfig();
        String str = "{\n" +
                "  \"scene\": \"ty_test02\",\n" +
                "  \"configId\": 100670,\n" +
                "  \"extJson\": \"{\\\"Service_Items\\\":\\\"3小时陪玩\\\",\\\"Service_order\\\":\\\"2\\\"}\",\n" +
                "  \"extJsonMap\": {\n" +
                "    \"Service_Items\": \"3小时陪玩\",\n" +
                "    \"Service_order\": \"2\"\n" +
                "  },\n" +
                "  \"contents\": [\n" +
                "    {\n" +
                "      \"configId\": 100670,\n" +
                "      \"contentId\": 759459,\n" +
                "      \"createTime\": 1722174980000,\n" +
                "      \"updateTime\": 1722174980000,\n" +
                "      \"content\": \"{\\\"Service_title\\\":\\\"服务人数\\\",\\\"city\\\":\\\"\\\",\\\"Service_content\\\":\\\"1人\\\",\\\"order\\\":0}\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"configId\": 100670,\n" +
                "      \"contentId\": 759460,\n" +
                "      \"createTime\": 1722174980000,\n" +
                "      \"updateTime\": 1722174980000,\n" +
                "      \"content\": \"{\\\"Service_title\\\":\\\"服务内容\\\",\\\"city\\\":\\\"\\\",\\\"Service_content\\\":\\\"桌游,网游,单机\\\",\\\"order\\\":0}\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"createTime\": 1722174980000,\n" +
                "  \"updateTime\": 1722175380000\n" +
                "}";
        item = JSON.parseObject(str, HaimaConfig.class);
        item.getContents().get(0).setExtJson("{\n" +
                "    \"Service_title\": \"服务人数\",\n" +
                "    \"city\": \"\",\n" +
                "    \"Service_content\": \"1人\",\n" +
                "    \"order\": 0\n" +
                "}");
        item.getContents().get(1).setExtJson("{\n" +
                "    \"Service_title\": \"服务人数\",\n" +
                "    \"city\": \"\",\n" +
                "    \"Service_content\": \"1人\",\n" +
                "    \"order\": 0\n" +
                "}");
        List<LifeClearHaiMaConfigDto> result = haimaWrapper.getLifeClearHaiMaiConfigDtos(item);


        String str1 = "{\n" +
                "  \"isEnable\": true,\n" +
                "  \"sceneKey\": \"exampleScene\",\n" +
                "  \"configId\": \"config123\",\n" +
                "  \"contentId\": \"content456\",\n" +
                "  \"excludeIds\": [\"id1\", \"id2\", \"id3\"],\n" +
                "  \"excludeNames\": [\"name1\", \"name2\", \"name3\"],\n" +
                "  \"firstTabName\": \"firstTab\"\n" +
                "}\n";
        JSON.parseObject(str1, CleaningProductLionConfig.class);
        ;
        String str2 = "{\n" +
                "  \"Service_title\": \"服务人数\",\n" +
                "  \"Service_content\": \"1人\"\n" +
                "}\n";
        JSON.parseObject(str2, LifeClearHaiMaConfigDto.class);
        String str3 = "{\n" +
                "  \"Service_Items\": \"3小时陪玩\",\n" +
                "  \"Service_order\": \"2\",\n" +
                "  \"Service_title\": \"综合服务\",\n" +
                "  \"Service_content\": \"提供多种服务\",\n" +
                "  \"haiMaList\": [\n" +
                "    {\n" +
                "      \"Service_title\": \"服务人数\",\n" +
                "      \"Service_content\": \"1人\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"Service_title\": \"服务内容\",\n" +
                "      \"Service_content\": \"清洁、陪伴\"\n" +
                "    }\n" +
                "  ]\n" +
                "}\n";
        JSON.parseObject(str3, LifeClearHaiMaConfig.class);

        String str4 = "{\n" +
                "  \"Service_title\": \"服务人员\",\n" +
                "  \"Service_content\": \"多种服务内容\"\n" +
                "}\n";
        JSON.parseObject(str4, LifeClearHaiMaConfig.class);

        assertNotNull(result);
    }
}
