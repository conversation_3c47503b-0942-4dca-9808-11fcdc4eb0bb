package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.detail.dto.DealGroupAntiFleeOrderDTO;
import com.dianping.deal.detail.dto.Response;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.concurrent.Future;
import static org.mockito.Mockito.when;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class DealGroupWrapper_QueryDealGroupAntiFleeOrderInfoTest {

    @InjectMocks
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private Future future;

    @Mock
    private Response<DealGroupAntiFleeOrderDTO> response;

    @Mock
    private DealGroupAntiFleeOrderDTO dealGroupAntiFleeOrderDTO;

    @Before
    public void setUp() {
        when(dealGroupWrapper.getFutureResult(future)).thenReturn(response);
    }

    /**
     * 测试 queryDealGroupAntiFleeOrderInfo 方法，当 future 的结果为 null 时
     */
    @Test
    public void testQueryDealGroupAntiFleeOrderInfoWhenFutureResultIsNull() throws Throwable {
        // arrange
        when(dealGroupWrapper.getFutureResult(future)).thenReturn(null);
        // act
        DealGroupAntiFleeOrderDTO result = dealGroupWrapper.queryDealGroupAntiFleeOrderInfo(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 queryDealGroupAntiFleeOrderInfo 方法，当 future 的结果不为 null，但 code 不等于 1 时
     */
    @Test
    public void testQueryDealGroupAntiFleeOrderInfoWhenCodeIsNotOne() throws Throwable {
        // arrange
        when(response.getCode()).thenReturn(0);
        // act
        DealGroupAntiFleeOrderDTO result = dealGroupWrapper.queryDealGroupAntiFleeOrderInfo(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 queryDealGroupAntiFleeOrderInfo 方法，当 future 的结果不为 null，且 code 等于 1 时
     */
    @Test
    public void testQueryDealGroupAntiFleeOrderInfoWhenCodeIsOne() throws Throwable {
        // arrange
        when(response.getCode()).thenReturn(1);
        when(response.getData()).thenReturn(dealGroupAntiFleeOrderDTO);
        // act
        DealGroupAntiFleeOrderDTO result = dealGroupWrapper.queryDealGroupAntiFleeOrderInfo(future);
        // assert
        assertSame(dealGroupAntiFleeOrderDTO, result);
    }
}
