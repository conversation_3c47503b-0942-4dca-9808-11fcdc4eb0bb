package com.dianping.mobile.mapi.dztgdetail.facade.repaircare;

import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DzRepairCareModule;
import com.dianping.mobile.mapi.dztgdetail.entity.repaircare.CategoryPicConfig;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.google.common.collect.Lists;
import com.sankuai.athena.biz.Response;
import com.sankuai.general.product.query.center.client.dto.*;
import com.sankuai.general.product.query.center.client.enums.ResponseCodeEnum;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import com.sankuai.zdc.tag.apply.api.PoiTagDisplayRPCService;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import org.checkerframework.checker.units.qual.A;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class DzRepairCareFacadeTest {

    private MockedStatic<LionConfigUtils> lionConfigUtilsMockedStatic;

    @Mock
    private DealGroupQueryService queryCenterDealGroupQueryService;

    @Mock
    private PoiTagDisplayRPCService poiTagDisplayRPCService;

    @InjectMocks
    private DzRepairCareFacade dzRepairCareFacade;

    @Before
    public void setUp() {
        lionConfigUtilsMockedStatic = Mockito.mockStatic(LionConfigUtils.class);
        MockitoAnnotations.initMocks(this);
    }

    @After
    public void after() {
        lionConfigUtilsMockedStatic.close();
    }

    /**
     * 测试getDzRepairCareModule方法，当查询服务返回null时
     */
    @Test
    public void testGetDzRepairCareModule_QueryServiceReturnsNull() throws Exception {
        // arrange
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(null);

        // act
        DzRepairCareModule result = dzRepairCareFacade.getDzRepairCareModule(123);

        // assert
        assertNull(result);
    }

    /**
     * 测试getDzRepairCareModule方法，当查询服务返回非成功状态码时
     */
    @Test
    public void testGetDzRepairCareModule_QueryServiceReturnsFailure() throws Exception {
        // arrange
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(ResponseCodeEnum.INVALID_PARAM.getCode());
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(response);

        // act
        DzRepairCareModule result = dzRepairCareFacade.getDzRepairCareModule(123);

        // assert
        assertNull(result);
    }

    /**
     * 测试getDzRepairCareModule方法，当查询服务返回空列表时
     */
    @Test
    public void testGetDzRepairCareModule_QueryServiceReturnsEmptyList() throws Exception {
        // arrange
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(ResponseCodeEnum.SUCCESS.getCode());
        response.setData(new QueryDealGroupListResult());
        response.getData().setList(Collections.emptyList());
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(response);

        // act
        DzRepairCareModule result = dzRepairCareFacade.getDzRepairCareModule(123);

        // assert
        assertNull(result);
    }

    /**
     * 测试getDzRepairCareModule方法，当查询服务返回有效数据但不支持尾款支付时
     */
    @Test
    public void testGetDzRepairCareModule_NotSupportFinalPayment() throws Exception {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setAttrs(Collections.emptyList()); // 不包含尾款支付属性
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(ResponseCodeEnum.SUCCESS.getCode());
        QueryDealGroupListResult data = new QueryDealGroupListResult();
        data.setList(Collections.singletonList(dealGroupDTO));
        response.setData(data);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(response);

        // act
        DzRepairCareModule result = dzRepairCareFacade.getDzRepairCareModule(123);

        // assert
        assertNull(result);
    }

    /**
     * 测试getDzRepairCareModule方法，当查询服务返回有效数据且支持尾款支付但类目不匹配时
     */
    @Test
    public void testGetDzRepairCareModule_CategoryNotMatch() throws Exception {
        DealGroupDisplayShopDTO displayShopInfo = new DealGroupDisplayShopDTO();
        displayShopInfo.setMtDisplayShopIds(Lists.newArrayList(1L, 2L, 3L));
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("pay_method");
        attrDTO.setValue(Collections.singletonList("2"));
        DealGroupCategoryDTO dealgroupCategoryDTO = new DealGroupCategoryDTO();
        dealgroupCategoryDTO.setCategoryId(449L);
        dealgroupCategoryDTO.setServiceTypeId(123L);
        DealGroupBasicDTO basicDTO = new DealGroupBasicDTO();
        basicDTO.setCategoryId(449L);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setAttrs(Collections.singletonList(attrDTO));
        dealGroupDTO.setCategory(dealgroupCategoryDTO);
        dealGroupDTO.setBasic(basicDTO);
        dealGroupDTO.setDisplayShopInfo(displayShopInfo);
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(ResponseCodeEnum.SUCCESS.getCode());
        QueryDealGroupListResult data = new QueryDealGroupListResult();
        data.setList(Collections.singletonList(dealGroupDTO));
        response.setData(data);
        Map<Long, List<DisplayTagDto>> tagMap = new HashMap<>();
        tagMap.put(1L, Lists.newArrayList(new DisplayTagDto()));
        tagMap.put(2L, Lists.newArrayList(new DisplayTagDto()));
        tagMap.put(3L, Lists.newArrayList(new DisplayTagDto()));
        Response<Map<Long, List<DisplayTagDto>>> tagResp = new Response<>(200, "成功", tagMap);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(response);
        when(poiTagDisplayRPCService.findSceneDisplayTagOfMt(any())).thenReturn(tagResp);
        //lion配置
        CategoryPicConfig categoryPicConfig = new CategoryPicConfig();
        categoryPicConfig.setPic("testPic");
        categoryPicConfig.setJumpUrl("testUrl");
        Map<String, CategoryPicConfig> repairCareCategoryPicConfig = new HashMap<>();
        repairCareCategoryPicConfig.put("449", categoryPicConfig);
        lionConfigUtilsMockedStatic.when(LionConfigUtils::getRepairCareCategoryPicConfig).thenReturn(repairCareCategoryPicConfig);
        // act
        DzRepairCareModule result = dzRepairCareFacade.getDzRepairCareModule(123);
        // assert
        assertEquals("testUrl", result.getJumpUrl());
        assertEquals("testPic", result.getPicture());
    }
}
