package com.dianping.mobile.mapi.dztgdetail.biz.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import com.dianping.mobile.mapi.dztgdetail.biz.service.bo.DrivingPoi;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedShop;
import com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper;
import com.dianping.mobile.mapi.dztgdetail.util.PoiShopUtil;
import com.dianping.poi.relation.service.api.PoiRelationService;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.service.DpPoiService;
import com.sankuai.sinai.data.api.service.MtPoiService;
import com.sankuai.zdc.apply.api.DrivingPoiQueryService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DrivingPoiServiceGetDrivingShopsTest {

    @InjectMocks
    private DrivingPoiService drivingPoiService;

    @Mock
    private PoiRelationService poiRelationService;

    @Mock
    private DrivingPoiQueryService drivingPoiQueryService;

    @Mock
    private DpPoiService sinaiDpPoiService;

    @Mock
    private MtPoiService sinaiMtPoiService;

    private DrivingPoi req;

    @Before
    public void setUp() {
        req = DrivingPoi.builder().shopId(1L).publishCategory(404).serviceTypeAttr(new ArrayList<>()).build();
        req.getServiceTypeAttr().add("小车");
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetDrivingShopsShopIdIsZero() throws Throwable {
        req.setShopId(0L);
        drivingPoiService.getDrivingShops(req);
    }

    @Test
    public void testGetDrivingShopsNotEnableDrivingShop() throws Throwable {
        req.setPublishCategory(405);
        List<RelatedShop> result = drivingPoiService.getDrivingShops(req);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetDrivingShopsDpPoiIdIsZero() throws Throwable {
        List<RelatedShop> result = drivingPoiService.getDrivingShops(req);
        assertEquals(1, result.size());
        assertEquals("商家暂未提供练车场地信息，请与商家确认", result.get(0).getShopInvalidText());
    }

    @Test
    public void testGetDrivingShopsDpPoiId2DpDrivingPoiIdsIsEmpty() throws Throwable {
        when(drivingPoiQueryService.queryShop2DrivingFieldMap(any())).thenReturn(new HashMap<>());
        List<RelatedShop> result = drivingPoiService.getDrivingShops(req);
        assertEquals(1, result.size());
        assertEquals("商家暂未提供练车场地信息，请与商家确认", result.get(0).getShopInvalidText());
    }

    @Test
    public void testGetDrivingShopsNormal() throws Throwable {
        Map<Long, List<Long>> dpPoiId2DpDrivingPoiIds = new HashMap<>();
        dpPoiId2DpDrivingPoiIds.put(1L, new ArrayList<>());
        when(drivingPoiQueryService.queryShop2DrivingFieldMap(any())).thenReturn(dpPoiId2DpDrivingPoiIds);
        List<RelatedShop> result = drivingPoiService.getDrivingShops(req);
        assertEquals(1, result.size());
    }
}
