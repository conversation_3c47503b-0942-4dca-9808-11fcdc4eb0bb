package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.basic;

import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.deal.shop.dto.BestShopSimpleDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDisplayShopDTO;
import org.jetbrains.annotations.NotNull;
import org.junit.After;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;

import static org.mockito.Mockito.*;

/**
 * 测试ParallBestShopProcessor的prepare方法
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest(value = {GreyUtils.class, Lion.class})
public class ParallBestShopProcessorTest {

    @InjectMocks
    private ParallBestShopProcessor parallBestShopProcessor;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    private MockedStatic<GreyUtils> greyUtilsMockedStatic;

    private MockedStatic<Lion> lionMockedStatic;

    @Before
    public void setUp() {
        greyUtilsMockedStatic = mockStatic(GreyUtils.class);
        lionMockedStatic = mockStatic(Lion.class);


    }

    @After
    public void teardown() {
        greyUtilsMockedStatic.close();
        lionMockedStatic.close();
    }

    /**
     * 测试当GreyUtils.enableQueryCenterForMainApi返回false时抛出IllegalArgumentException
     */
    @Test(expected = IllegalArgumentException.class)
    public void testPrepare_WhenGreyUtilsReturnsFalse_ThrowsIllegalArgumentException() {
        DealCtx ctx = getBuildCtx();
        // arrange
        greyUtilsMockedStatic.when(() -> GreyUtils.enableQueryCenterForMainApi(ctx)).thenReturn(false);
        // act
        parallBestShopProcessor.prepare(ctx);
    }

    /**
     * 测试当switchAllToFastBestShop返回true且isNeedDiff返回true时，调用doFastBestShopPre和doBestShopWithoutShopIdPre
     */
    @Ignore
    @Test
    public void testPrepare_WhenSwitchAllToFastBestShopAndIsNeedDiff_ThenCallDoFastBestShopPreAndDoBestShopWithoutShopIdPre() {
        DealCtx ctx = getBuildCtx();
        // arrange
        greyUtilsMockedStatic.when(() -> GreyUtils.enableQueryCenterForMainApi(any())).thenReturn(true);
        lionMockedStatic.when(() -> Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.fast.best.shop.switcher", false)).thenReturn(true);
        lionMockedStatic.when(() -> Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.fast.best.shop.diff.switcher", true)).thenReturn(true);
        when(dealGroupWrapper.preDealGroupBestShopFastly(any())).thenReturn(null);
        when(dealGroupWrapper.preDealGroupBestShop(any())).thenReturn(null);
        // act
        parallBestShopProcessor.prepare(ctx);
        // assert
        assert true;
    }

    /**
     * 测试当switchAllToFastBestShop返回true且isNeedDiff返回false时，只调用doFastBestShopPre
     */
    @Ignore
    @Test
    public void testPrepare_WhenSwitchAllToFastBestShopAndNotIsNeedDiff_ThenCallDoFastBestShopPre() {
        DealCtx ctx = getBuildCtx();
        // arrange
        greyUtilsMockedStatic.when(() -> GreyUtils.enableQueryCenterForMainApi(any())).thenReturn(true);
        lionMockedStatic.when(() -> Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.fast.best.shop.switcher", false)).thenReturn(true);
        lionMockedStatic.when(() -> Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.fast.best.shop.diff.switcher", true)).thenReturn(false);
        when(dealGroupWrapper.preDealGroupBestShopFastly(any())).thenReturn(null);
        when(dealGroupWrapper.preDealGroupBestShop(any())).thenReturn(null);
        // act
        parallBestShopProcessor.prepare(ctx);
        // assert
        assert true;
    }

    /**
     * 测试当switchAllToFastBestShop返回false时，只调用doBestShopPre
     */
    @Ignore
    @Test
    public void testPrepare_WhenNotSwitchAllToFastBestShop_ThenCallDoBestShopPre() {
        DealCtx ctx = getBuildCtx();
        // arrange
        greyUtilsMockedStatic.when(() -> GreyUtils.enableQueryCenterForMainApi(any())).thenReturn(true);
        lionMockedStatic.when(() -> Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.fast.best.shop.switcher", false)).thenReturn(false);
        when(dealGroupWrapper.preDealGroupBestShopFastly(any())).thenReturn(null);
        when(dealGroupWrapper.preDealGroupBestShop(any())).thenReturn(null);
        // act
        parallBestShopProcessor.prepare(ctx);
        // assert
        assert true;
    }

    /**
     * 测试场景：当DealCtx中的FastBestShopFuture不为null时，执行新的最佳门店处理逻辑
     */
    @Test
    public void testProcessWithFastBestShopFutureNotNull() {
        // arrange
        DealCtx ctx = getBuildCtx();

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        // 模拟没有显示门店信息
        dealGroupDTO.setDisplayShopInfo(null);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        // act
        parallBestShopProcessor.process(ctx);
        // assert
        verify(ctx, times(1)).setNeedFillBestShop(true);
    }

    private static @NotNull DealCtx getBuildCtx() {
        DealCtx ctx = mock(DealCtx.class);
        BestShopSimpleDTO bestShopSimpleDTO = new BestShopSimpleDTO();
        bestShopSimpleDTO.setDpShopId(123123123L);
        CompletableFuture<BestShopSimpleDTO> bestShopSimpleDTOCF = CompletableFuture.completedFuture(bestShopSimpleDTO);
        BestShopDTO bestShopDTO = new BestShopDTO();
        bestShopDTO.setMtShopId(123123123L);
        bestShopDTO.setDpShopId(123123123L);
        CompletableFuture<BestShopDTO> bestShopDTOCF = CompletableFuture.completedFuture(bestShopDTO);
        FutureCtx futureCtx = new FutureCtx();
        futureCtx.setBestShopFuture(bestShopDTOCF);
        futureCtx.setFastBestShopFuture(bestShopSimpleDTOCF);
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
        EnvCtx envCtx = new EnvCtx();
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        return ctx;
    }

    /**
     * 测试场景：当DealCtx中的FastBestShopFuture为null时，执行旧的最佳门店处理逻辑
     */
    @Test
    public void testProcessWithFastBestShopFutureNull() {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        BestShopDTO bestShopDTO = new BestShopDTO();
        bestShopDTO.setMtShopId(123123123L);
        bestShopDTO.setDpShopId(123123123L);
        CompletableFuture<BestShopDTO> bestShopDTOCF = CompletableFuture.completedFuture(bestShopDTO);
        FutureCtx futureCtx = new FutureCtx();
        futureCtx.setBestShopFuture(bestShopDTOCF);
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
        EnvCtx envCtx = new EnvCtx();
        when(ctx.getEnvCtx()).thenReturn(envCtx);

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        // 模拟没有显示门店信息
        dealGroupDTO.setDisplayShopInfo(null);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        // act
        parallBestShopProcessor.process(ctx);
        // assert
        verify(ctx, never()).setNeedFillBestShop(true);
    }

    /**
     * 测试场景：当DealCtx中的FastBestShopFuture不为null，且DealGroupDTO中有显示门店信息
     */
    @Test
    public void testProcessWithDisplayShopIds() {
        // arrange
        DealCtx ctx = getBuildCtx();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupDisplayShopDTO displayShopInfo = new DealGroupDisplayShopDTO();
        displayShopInfo.setDpDisplayShopIds(Lists.newArrayList(123123L));
        displayShopInfo.setMtDisplayShopIds(Lists.newArrayList(123123L));
        dealGroupDTO.setDisplayShopInfo(displayShopInfo);
        dealGroupDTO.getDisplayShopInfo().setDpDisplayShopIds(Arrays.asList(1L, 2L, 3L));
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        // act
        parallBestShopProcessor.process(ctx);
        // assert
        verify(ctx, times(1)).setNeedFillBestShop(true);
    }

    /**
     * 测试场景：当DealCtx中的FastBestShopFuture为null，且DealGroupDTO中没有显示门店信息
     */
    @Test
    public void testProcessWithNoDisplayShopIds() {
        // arrange
        DealCtx ctx = getBuildCtx();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDisplayShopInfo(new DealGroupDisplayShopDTO());
        dealGroupDTO.getDisplayShopInfo().setDpDisplayShopIds(Collections.emptyList());
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        // act
        parallBestShopProcessor.process(ctx);
        // assert
        verify(ctx, times(1)).setNeedFillBestShop(true);
    }
}
