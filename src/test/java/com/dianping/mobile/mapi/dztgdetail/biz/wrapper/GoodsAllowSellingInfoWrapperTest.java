package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.mlive.goods.trade.api.model.GoodsSellingInfoDTO;
import com.sankuai.mlive.goods.trade.api.request.QueryGoodsAllowSellingInfoRequest;
import com.sankuai.mlive.goods.trade.api.response.QueryGoodsAllowSellingInfoResponse;
import com.sankuai.mlive.goods.trade.api.tservice.GoodsAllowSellingTService;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.when;

public class GoodsAllowSellingInfoWrapperTest {

    @InjectMocks
    private GoodsAllowSellingInfoWrapper goodsAllowSellingInfoWrapper;

    @Mock
    private GoodsAllowSellingTService goodsAllowSellingTService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试请求参数为null时，方法抛出异常
     */
    @Test(expected = com.dianping.mobile.mapi.dztgdetail.exception.QueryGoodsAllowSellingResultException.class)
    public void testGetGoodsAllowSellingInfoResponseWithNullRequest() throws TException {
        goodsAllowSellingInfoWrapper.getGoodsAllowSellingInfoResponse(null);
    }

    /**
     * 测试请求参数不为null，但liveId小于等于0时，方法返回null
     */
    @Test
    public void testGetGoodsAllowSellingInfoResponseWithZeroLiveId() throws TException {
        QueryGoodsAllowSellingInfoRequest request = new QueryGoodsAllowSellingInfoRequest();
        request.setLiveId(0);
        assertNull(goodsAllowSellingInfoWrapper.getGoodsAllowSellingInfoResponse(request));
    }

    /**
     * 测试请求参数不为null，liveId大于0，但服务返回null时，方法返回null
     */
    @Test
    public void testGetGoodsAllowSellingInfoResponseWithNullResponse() throws TException {
        QueryGoodsAllowSellingInfoRequest request = new QueryGoodsAllowSellingInfoRequest();
        request.setLiveId(1);
        when(goodsAllowSellingTService.queryGoodsAllowSellingInfo(request)).thenReturn(null);
        assertNull(goodsAllowSellingInfoWrapper.getGoodsAllowSellingInfoResponse(request));
    }

    /**
     * 测试请求参数不为null，liveId大于0，服务返回非null对象时，方法返回该对象中的data字段
     */
    @Test
    public void testGetGoodsAllowSellingInfoResponseWithNonNullResponse() throws TException {
        QueryGoodsAllowSellingInfoRequest request = new QueryGoodsAllowSellingInfoRequest();
        request.setLiveId(1);
        QueryGoodsAllowSellingInfoResponse response = new QueryGoodsAllowSellingInfoResponse();
        GoodsSellingInfoDTO data = new GoodsSellingInfoDTO();
        response.setData(data);
        when(goodsAllowSellingTService.queryGoodsAllowSellingInfo(request)).thenReturn(response);
        assertNull(goodsAllowSellingInfoWrapper.getGoodsAllowSellingInfoResponse(request));
    }
}
