package com.dianping.mobile.mapi.dztgdetail.helper;

import org.junit.Test;
import java.math.BigDecimal;
import static org.junit.Assert.assertEquals;

public class PriceHelper_DropLastZeroTest {

    /**
     * 测试 dropLastZero 方法，输入为整数
     */
    @Test
    public void testDropLastZeroInt() {
        // arrange
        BigDecimal price = new BigDecimal(123);
        // act
        String result = PriceHelper.dropLastZero(price);
        // assert
        assertEquals("123", result);
    }

    /**
     * 测试 dropLastZero 方法，输入为小数，小数点后只有一位
     */
    @Test
    public void testDropLastZeroOneDecimal() {
        // arrange
        BigDecimal price = new BigDecimal(123.4);
        // act
        String result = PriceHelper.dropLastZero(price);
        // assert
        assertEquals("123.4", result);
    }

    /**
     * 测试 dropLastZero 方法，输入为小数，小数点后有两位
     */
    @Test
    public void testDropLastZeroTwoDecimals() {
        // arrange
        BigDecimal price = new BigDecimal(123.45);
        // act
        String result = PriceHelper.dropLastZero(price);
        // assert
        assertEquals("123.45", result);
    }
}
