package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.GoodsAllowSellingInfoWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.MLiveInfoVo;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.SaleChannelAggregationDTO;
import com.sankuai.general.product.query.center.client.dto.SaleChannelDTO;
import com.sankuai.mlive.goods.trade.api.model.GoodsSellingInfoDTO;
import com.sankuai.mlive.goods.trade.api.request.QueryGoodsAllowSellingInfoRequest;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ChannelSaleStatusProcessorIsFromMLiveTest {

    @InjectMocks
    private ChannelSaleStatusProcessor processor;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private GoodsAllowSellingInfoWrapper goodsAllowSellingInfoWrapper;

    private DealCtx ctx;

    @Before
    public void setUp() {
        ctx = new DealCtx(null);
        ctx.setMLiveInfoVo(new MLiveInfoVo());
        ctx.setDealBaseReq(new DealBaseReq());
    }

    // Helper method to invoke the private isFromMLive method using reflection
    private boolean invokePrivateIsFromMLive(DealCtx dealCtx) throws Exception {
        Method method = ChannelSaleStatusProcessor.class.getDeclaredMethod("isFromMLive", DealCtx.class);
        method.setAccessible(true);
        return (boolean) method.invoke(processor, dealCtx);
    }

    private Map<String, String> getDealParamMap(String dealParam) {
        if (dealParam == null) {
            return new HashMap<>();
        }
        // Assuming dealParam is a JSON string, parse it into a map
        // This is a simplified example, you may need a proper JSON parser
        Map<String, String> map = new HashMap<>();
        if (dealParam.startsWith("{") && dealParam.endsWith("}")) {
            String[] pairs = dealParam.substring(1, dealParam.length() - 1).split(",");
            for (String pair : pairs) {
                String[] keyValue = pair.split(":");
                if (keyValue.length == 2) {
                    map.put(keyValue[0].replace("\"", "").trim(), keyValue[1].replace("\"", "").trim());
                }
            }
        }
        return map;
    }

    private boolean invokeValidQueryParam(DealCtx ctx) throws Exception {
        Method method = ChannelSaleStatusProcessor.class.getDeclaredMethod("validQueryParam", DealCtx.class);
        method.setAccessible(true);
        return (boolean) method.invoke(processor, ctx);
    }

    /**
     * Test when requestSource is "mlive" and mLiveId is 0
     */
    @Test
    public void testIsFromMLive_WhenRequestSourceIsMLiveAndMLiveIdIsZero() throws Throwable {
        // arrange
        when(dealCtx.getRequestSource()).thenReturn("mlive");
        // act
        boolean result = invokePrivateIsFromMLive(dealCtx);
        // assert
        assertTrue(result);
    }

    /**
     * Test when requestSource is not "mlive" but mLiveId is positive
     */
    @Test
    public void testIsFromMLive_WhenRequestSourceNotMLiveButMLiveIdPositive() throws Throwable {
        // arrange
        when(dealCtx.getRequestSource()).thenReturn("other");
        when(dealCtx.getMLiveId()).thenReturn(123L);
        // act
        boolean result = invokePrivateIsFromMLive(dealCtx);
        // assert
        assertTrue(result);
    }

    /**
     * Test when both requestSource is "mlive" and mLiveId is positive
     */
    @Test
    public void testIsFromMLive_WhenBothConditionsAreTrue() throws Throwable {
        // arrange
        when(dealCtx.getRequestSource()).thenReturn("mlive");
        // act
        boolean result = invokePrivateIsFromMLive(dealCtx);
        // assert
        assertTrue(result);
    }

    /**
     * Test when requestSource is not "mlive" and mLiveId is 0
     */
    @Test
    public void testIsFromMLive_WhenBothConditionsAreFalse() throws Throwable {
        // arrange
        when(dealCtx.getRequestSource()).thenReturn("other");
        when(dealCtx.getMLiveId()).thenReturn(0L);
        // act
        boolean result = invokePrivateIsFromMLive(dealCtx);
        // assert
        assertFalse(result);
    }

    /**
     * Test when requestSource is null and mLiveId is 0
     */
    @Test
    public void testIsFromMLive_WhenRequestSourceIsNullAndMLiveIdIsZero() throws Throwable {
        // arrange
        when(dealCtx.getRequestSource()).thenReturn(null);
        when(dealCtx.getMLiveId()).thenReturn(0L);
        // act
        boolean result = invokePrivateIsFromMLive(dealCtx);
        // assert
        assertFalse(result);
    }

    /**
     * Test when requestSource is null but mLiveId is positive
     */
    @Test
    public void testIsFromMLive_WhenRequestSourceIsNullButMLiveIdPositive() throws Throwable {
        // arrange
        when(dealCtx.getRequestSource()).thenReturn(null);
        when(dealCtx.getMLiveId()).thenReturn(123L);
        // act
        boolean result = invokePrivateIsFromMLive(dealCtx);
        // assert
        assertTrue(result);
    }

    /**
     * Test the isEnable method when the method always returns true.
     */
    @Test
    public void testIsEnableAlwaysReturnsTrue() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        // act
        boolean result = processor.isEnable(ctx);
        // assert
        assertTrue("The isEnable method should always return true", result);
    }

    /**
     * Test case: Both goodsType and mLiveId are valid positive numbers
     * Expected: Should return true
     */
    @Test
    public void testValidQueryParam_ValidParameters_ReturnsTrue() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        ctx.setDealParam("{\"goodsTypeId\":\"123\"}");
        ctx.setMLiveId(456L);
        // act
        boolean result = invokeValidQueryParam(ctx);
        // assert
        assertTrue(result);
    }

    /**
     * Test case: goodsType is 0 and mLiveId is positive
     * Expected: Should return false
     */
    @Test
    public void testValidQueryParam_ZeroGoodsType_ReturnsFalse() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        ctx.setDealParam("{\"goodsTypeId\":\"0\"}");
        ctx.setMLiveId(456L);
        // act
        boolean result = invokeValidQueryParam(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: goodsType is positive but mLiveId is 0
     * Expected: Should return false
     */
    @Test
    public void testValidQueryParam_ZeroMLiveId_ReturnsFalse() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        ctx.setDealParam("{\"goodsTypeId\":\"123\"}");
        ctx.setMLiveId(0L);
        // act
        boolean result = invokeValidQueryParam(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: Empty dealParam map
     * Expected: Should return false
     */
    @Test
    public void testValidQueryParam_EmptyDealParam_ReturnsFalse() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        ctx.setDealParam("{}");
        ctx.setMLiveId(456L);
        // act
        boolean result = invokeValidQueryParam(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: Invalid goodsTypeId format (non-numeric)
     * Expected: Should return false
     */
    @Test
    public void testValidQueryParam_InvalidGoodsTypeFormat_ReturnsFalse() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        ctx.setDealParam("{\"goodsTypeId\":\"abc\"}");
        ctx.setMLiveId(456L);
        // act
        boolean result = invokeValidQueryParam(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: Null dealParam
     * Expected: Should return false
     */
    @Test
    public void testValidQueryParam_NullDealParam_ReturnsFalse() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        ctx.setDealParam(null);
        ctx.setMLiveId(456L);
        // act
        boolean result = invokeValidQueryParam(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: Negative goodsTypeId
     * Expected: Should return false
     */
    @Test
    public void testValidQueryParam_NegativeGoodsType_ReturnsFalse() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        ctx.setDealParam("{\"goodsTypeId\":\"-1\"}");
        ctx.setMLiveId(456L);
        // act
        boolean result = invokeValidQueryParam(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: Both parameters are negative
     * Expected: Should return false
     */
    @Test
    public void testValidQueryParam_BothParametersNegative_ReturnsFalse() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        ctx.setDealParam("{\"goodsTypeId\":\"-1\"}");
        ctx.setMLiveId(-1L);
        // act
        boolean result = invokeValidQueryParam(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for MLive channel with valid parameters
     */
    @Test
    public void testProcess_WithValidMLiveParameters() throws Throwable {
        // arrange
        ctx.setRequestSource(RequestSourceEnum.LIVE_STREAM.getSource());
        ctx.setMLiveId(123L);
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("goodsTypeId", "1");
        ctx.setDealParam(JSON.toJSONString(paramMap));
        ctx.setMtId(456);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        SaleChannelAggregationDTO aggregationDTO = new SaleChannelAggregationDTO();
        SaleChannelDTO channelDTO = new SaleChannelDTO();
        channelDTO.setChannelNo(10003L);
        aggregationDTO.setSupportChannels(Arrays.asList(channelDTO));
        dealGroupDTO.setSaleChannelAggregation(aggregationDTO);
        ctx.setDealGroupDTO(dealGroupDTO);
        GoodsSellingInfoDTO sellingInfoDTO = new GoodsSellingInfoDTO();
        when(goodsAllowSellingInfoWrapper.getGoodsAllowSellingInfoResponse(any())).thenReturn(sellingInfoDTO);
        // act
        processor.process(ctx);
        // assert
        assertTrue(ctx.getMLiveChannel());
        assertEquals("1", ctx.getMLiveInfoVo().getGoodsTypeId());
        assertEquals(sellingInfoDTO, ctx.getMliveSellingInfo());
        verify(goodsAllowSellingInfoWrapper).getGoodsAllowSellingInfoResponse(any());
    }

    /**
     * Test case for non-MLive channel
     */
    @Test
    public void testProcess_NonMLiveChannel() throws Throwable {
        // arrange
        ctx.setRequestSource("other");
        ctx.setMLiveId(0L);
        // act
        processor.process(ctx);
        // assert
        assertFalse(ctx.getMLiveChannel());
        try {
            verify(goodsAllowSellingInfoWrapper, never()).getGoodsAllowSellingInfoResponse(any());
        } catch (TException e) {
            fail("Unexpected TException: " + e.getMessage());
        }
    }

    /**
     * Test case for invalid goodsTypeId parameter
     */
    @Test
    public void testProcess_InvalidGoodsTypeId() throws Throwable {
        // arrange
        ctx.setRequestSource(RequestSourceEnum.LIVE_STREAM.getSource());
        ctx.setMLiveId(123L);
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("goodsTypeId", "invalid");
        ctx.setDealParam(JSON.toJSONString(paramMap));
        // act
        processor.process(ctx);
        // assert
        try {
            verify(goodsAllowSellingInfoWrapper, never()).getGoodsAllowSellingInfoResponse(any());
        } catch (TException e) {
            fail("Unexpected TException: " + e.getMessage());
        }
    }

    /**
     * Test case for exception handling
     */
    @Test
    public void testProcess_ExceptionHandling() throws Throwable {
        // arrange
        ctx.setRequestSource(RequestSourceEnum.LIVE_STREAM.getSource());
        ctx.setMLiveId(123L);
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("goodsTypeId", "1");
        ctx.setDealParam(JSON.toJSONString(paramMap));
        when(goodsAllowSellingInfoWrapper.getGoodsAllowSellingInfoResponse(any())).thenThrow(new RuntimeException("Test exception"));
        // act
        processor.process(ctx);
        // assert
        assertTrue(ctx.isQueryCenterHasError());
    }

    /**
     * Test case for empty sale channel
     */
    @Test
    public void testProcess_EmptySaleChannel() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        SaleChannelAggregationDTO aggregationDTO = new SaleChannelAggregationDTO();
        dealGroupDTO.setSaleChannelAggregation(aggregationDTO);
        ctx.setDealGroupDTO(dealGroupDTO);
        // act
        processor.process(ctx);
        // assert
        assertFalse(ctx.getMLiveChannel());
    }

    /**
     * Test case for null DealGroupDTO
     */
    @Test
    public void testProcess_NullDealGroupDTO() throws Throwable {
        // arrange
        ctx.setDealGroupDTO(null);
        // act
        processor.process(ctx);
        // assert
        assertFalse(ctx.getMLiveChannel());
    }
}
