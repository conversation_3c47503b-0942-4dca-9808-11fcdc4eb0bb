package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.*;
import org.junit.Test;

public class LocationHelperTest {

    /**
     * 测试纬度在有效范围内的情况
     */
    @Test
    public void testIsValidLatitude_ValidRange() throws Throwable {
        // arrange
        double latitude = 0.0000;
        // act
        boolean result = LocationHelper.isValidLatitude(latitude);
        // assert
        assertTrue(result);
    }

    /**
     * 测试纬度不在有效范围内的情况
     */
    @Test
    public void testIsValidLatitude_InvalidRange() throws Throwable {
        // arrange
        double latitude = -91.0000;
        // act
        boolean result = LocationHelper.isValidLatitude(latitude);
        // assert
        assertFalse(result);
    }

    /**
     * Tests the isValidPoint method when lat or lng equals 0, it should return true
     * if the other value is within a valid range, as per the method's logic.
     */
    @Test
    public void testIsValidPointWhenLatOrLngIsZero() throws Throwable {
        // Both lat and lng are 0, so it's invalid
        assertFalse(LocationHelper.isValidPoint(0, 0));
        // lng is valid, so the point is considered valid
        assertTrue(LocationHelper.isValidPoint(0, 1));
        // lat is valid, so the point is considered valid
        assertTrue(LocationHelper.isValidPoint(1, 0));
    }

    /**
     * Tests the isValidPoint method when lat and lng are within valid ranges, it should return true.
     */
    @Test
    public void testIsValidPointWhenLatAndLngAreValid() throws Throwable {
        assertTrue(LocationHelper.isValidPoint(45, 45));
    }

    /**
     * Tests the isValidPoint method when lat or lng is not within valid ranges, it should return false.
     */
    @Test
    public void testIsValidPointWhenLatOrLngIsNotValid() throws Throwable {
        assertFalse(LocationHelper.isValidPoint(-91, 45));
        assertFalse(LocationHelper.isValidPoint(45, -181));
    }

    /**
     * 测试经度在有效范围内的情况
     */
    @Test
    public void testIsValidLongitude_ValidRange() throws Throwable {
        // arrange
        double longitude = 100.0000;
        // act
        boolean result = LocationHelper.isValidLongitude(longitude);
        // assert
        assertTrue(result);
    }

    /**
     * 测试经度不在有效范围内的情况
     */
    @Test
    public void testIsValidLongitude_InvalidRange() throws Throwable {
        // arrange
        double longitude = 200.0000;
        // act
        boolean result = LocationHelper.isValidLongitude(longitude);
        // assert
        assertFalse(result);
    }

    /**
     * 测试经度为最小值的情况
     */
    @Test
    public void testIsValidLongitude_MinValue() throws Throwable {
        // arrange
        double longitude = -180.0000;
        // act
        boolean result = LocationHelper.isValidLongitude(longitude);
        // assert
        assertTrue(result);
    }

    /**
     * 测试经度为最大值的情况
     */
    @Test
    public void testIsValidLongitude_MaxValue() throws Throwable {
        // arrange
        double longitude = 180.0000;
        // act
        boolean result = LocationHelper.isValidLongitude(longitude);
        // assert
        assertTrue(result);
    }
}
