package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.lion.Environment;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.VersionUtils;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

public class UnifiedModuleProcessorBuildKeyTest {

    private DealCtx ctx;

    @Before
    public void setUp() {
        ctx = Mockito.mock(DealCtx.class);
        EnvCtx envCtx = Mockito.mock(EnvCtx.class);
        when(ctx.getEnvCtx()).thenReturn(envCtx);
    }

    /**
     * Test case for Meituan context with publishCategoryId 501 and isBeautyOlderVersion true.
     */
    @Test
    public void testBuildKeyMeituanBeautyOlderVersion() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getEnvCtx().getVersion()).thenReturn("11.18.400");
        when(ctx.isExternal()).thenReturn(false);
        // act
        String result = UnifiedModuleProcessor.buildKey(ctx, 501);
        // assert
        assertEquals("mt501_old", result);
    }

    /**
     * Test case for Meituan context with publishCategoryId 1210 and isEduOnlineDeal true.
     */
    @Test
    public void testBuildKeyMeituanEduOnlineDeal() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(true);
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("service_type_leaf_id");
        attributeDTO.setValue(Collections.singletonList("134013"));
        when(ctx.getAttributes()).thenReturn(Collections.singletonList(attributeDTO));
        // act
        String result = UnifiedModuleProcessor.buildKey(ctx, 1210);
        // assert
        assertEquals("mt1210_online", result);
    }

    /**
     * Test case for Dianping context with publishCategoryId 500 and no additional suffixes.
     */
    @Test
    public void testBuildKeyDianpingNoSuffix() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(false);
        // act
        String result = UnifiedModuleProcessor.buildKey(ctx, 500);
        // assert
        assertEquals("dp500", result);
    }

    /**
     * Test case for Dianping context with publishCategoryId 414 and isWuyoutong false.
     */
    @Test
    public void testBuildKeyDianpingWuyoutongFalse() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(false);
        when(ctx.getAttributes()).thenReturn(Collections.emptyList());
        // Version less than the threshold
        when(ctx.getMrnVersion()).thenReturn("10.58.0");
        // act
        String result = UnifiedModuleProcessor.buildKey(ctx, 414);
        // assert
        assertEquals("dp414", result);
    }

    /**
     * Test case for Meituan context with publishCategoryId 1210 and isEduOnlineDeal false.
     */
    @Test
    public void testBuildKeyMeituanEduOnlineDealFalse() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(true);
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("service_type_leaf_id");
        // Not in the list of online service leaf ids
        attributeDTO.setValue(Collections.singletonList("123456"));
        when(ctx.getAttributes()).thenReturn(Collections.singletonList(attributeDTO));
        // act
        String result = UnifiedModuleProcessor.buildKey(ctx, 1210);
        // assert
        assertEquals("mt1210", result);
    }
}
