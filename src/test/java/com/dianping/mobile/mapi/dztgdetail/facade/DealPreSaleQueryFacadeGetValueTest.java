package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealPreSaleQueryFacadeGetValueTest {

    private DealPreSaleQueryFacade dealPreSaleQueryFacade = new DealPreSaleQueryFacade();

    private String invokePrivateMethod(String methodName, Object... args) throws Exception {
        Method method = DealPreSaleQueryFacade.class.getDeclaredMethod(methodName, Map.class, String.class);
        method.setAccessible(true);
        return (String) method.invoke(dealPreSaleQueryFacade, args);
    }

    /**
     * 测试getValue方法，当Map为空时，应返回null
     */
    @Test
    public void testGetValueWhenMapIsNull() throws Throwable {
        // arrange
        Map<String, String> map = null;
        String key = "testKey";
        // act
        String result = invokePrivateMethod("getValue", map, key);
        // assert
        assertNull(result);
    }

    /**
     * 测试getValue方法，当Map不为空，但是没有与key对应的value时，应返回null
     */
    @Test
    public void testGetValueWhenMapIsNotEmptyButNoKey() throws Throwable {
        // arrange
        Map<String, String> map = new HashMap<>();
        map.put("anotherKey", "anotherValue");
        String key = "testKey";
        // act
        String result = invokePrivateMethod("getValue", map, key);
        // assert
        assertNull(result);
    }

    /**
     * 测试getValue方法，当Map不为空，且有与key对应的value时，应返回该value
     */
    @Test
    public void testGetValueWhenMapIsNotEmptyAndHasKey() throws Throwable {
        // arrange
        Map<String, String> map = new HashMap<>();
        String key = "testKey";
        String expectedValue = "testValue";
        map.put(key, expectedValue);
        // act
        String result = invokePrivateMethod("getValue", map, key);
        // assert
        assertEquals(expectedValue, result);
    }
}
