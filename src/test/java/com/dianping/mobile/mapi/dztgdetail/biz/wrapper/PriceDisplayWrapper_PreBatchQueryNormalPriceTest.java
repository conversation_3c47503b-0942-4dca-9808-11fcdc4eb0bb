package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.BaseLoadParam;
import com.sankuai.dealuser.price.display.api.PriceDisplayService;
import com.sankuai.dealuser.price.display.api.model.BatchPriceRequest;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PriceResponse;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.slf4j.Logger;

@RunWith(MockitoJUnitRunner.class)
public class PriceDisplayWrapper_PreBatchQueryNormalPriceTest {

    @InjectMocks
    private PriceDisplayWrapper priceDisplayWrapper;

    @Mock
    private PriceDisplayService priceDisplayService;

    private BaseLoadParam param;

    private BatchPriceRequest batchPriceRequest;

    @Mock
    private Logger logger;

    @Before
    public void setUp() {
        param = mock(BaseLoadParam.class);
        batchPriceRequest = mock(BatchPriceRequest.class);
    }

    /**
     * Tests the preBatchQueryNormalPrice method when the param parameter is null.
     */
    @Test(expected = NullPointerException.class)
    public void testPreBatchQueryNormalPriceParamNull() throws Throwable {
        // Arrange
        BaseLoadParam nullParam = null;
        // Act
        priceDisplayWrapper.preBatchQueryNormalPrice(nullParam);
    }

    /**
     * Test getting price secret info successfully
     */
    @Test
    @SuppressWarnings("unchecked")
    public void testGetPriceSecretInfo_Success() throws Throwable {
        // arrange
        Future<PriceResponse<Map<Long, List<PriceDisplayDTO>>>> priceFuture = mock(Future.class);
        PriceResponse<Map<Long, List<PriceDisplayDTO>>> priceResponse = mock(PriceResponse.class);
        String expectedSecretInfo = "testSecretInfo";
        when(priceFuture.get()).thenReturn(priceResponse);
        when(priceResponse.getPriceSecretInfo()).thenReturn(expectedSecretInfo);
        // act
        String result = priceDisplayWrapper.getPriceSecretInfo(priceFuture);
        // assert
        Assert.assertEquals(expectedSecretInfo, result);
    }

    /**
     * Test getting price secret info when price response is null
     */
    @Test
    @SuppressWarnings("unchecked")
    public void testGetPriceSecretInfo_NullResponse() throws Throwable {
        // arrange
        Future<PriceResponse<Map<Long, List<PriceDisplayDTO>>>> priceFuture = mock(Future.class);
        when(priceFuture.get()).thenReturn(null);
        // act
        String result = priceDisplayWrapper.getPriceSecretInfo(priceFuture);
        // assert
        Assert.assertNull(result);
    }
}
