package com.dianping.mobile.mapi.dztgdetail.tab.postprocess;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.DealTab;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.DealTabHolder;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class CfgBasedRelatePostProcessorPostProcessAfterRelateTest {

    @InjectMocks
    private CfgBasedRelatePostProcessor processor;

    private DealTabHolder dealTabHolder;

    private List<DealTab> relatedTabs;

    @Before
    public void setUp() {
        dealTabHolder = new DealTabHolder();
        relatedTabs = new ArrayList<>();
        relatedTabs.add(new DealTab());
        dealTabHolder.setRelatedTabs(relatedTabs);
    }

    @Test
    public void testPostProcessAfterRelateWithNullConfig() throws Throwable {
        int publishCategory = 1;
        processor.postProcessAfterRelate(publishCategory, dealTabHolder);
        assertEquals("Related tabs should not be modified when no configuration matches", 1, dealTabHolder.getRelatedTabs().size());
    }

    @Test
    public void testPostProcessAfterRelateWithLessTabs() throws Throwable {
        int publishCategory = 1;
        processor.postProcessAfterRelate(publishCategory, dealTabHolder);
        assertEquals("Related tabs should be truncated to match the tab size", 1, dealTabHolder.getRelatedTabs().size());
    }

    @Test
    public void testPostProcessAfterRelateWithMoreTabs() throws Throwable {
        int publishCategory = 1;
        processor.postProcessAfterRelate(publishCategory, dealTabHolder);
        assertEquals("Related tabs should be truncated to match the tab size", 1, dealTabHolder.getRelatedTabs().size());
    }
}
