package com.dianping.mobile.mapi.dztgdetail.biz.service;

import com.dianping.mobile.mapi.dztgdetail.datatype.req.GoodReviewReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedModuleExtraReq;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DetailTagService_ConvertToUnifiedModuleExtraReqTest {

    @InjectMocks
    private DetailTagService detailTagService;

    /**
     * Tests convertToUnifiedModuleExtraReq method with a non-null input.
     */
    @Test
    public void testConvertToUnifiedModuleExtraReq_NotNull() throws Throwable {
        // Arrange
        GoodReviewReq req = new GoodReviewReq();
        req.setDealgroupid(1);
        req.setMrnVersion("1.0.0");
        // Act
        UnifiedModuleExtraReq result = detailTagService.convertToUnifiedModuleExtraReq(req);
        // Assert
        assertNotNull(result);
        assertEquals(req.getDealgroupid(), result.getDealGroupId());
        assertEquals("", result.getExpResults());
        assertEquals(-1, result.getCityId().intValue());
        assertEquals(req.getMrnVersion(), result.getMrnVersion());
    }

    /**
     * Tests convertToUnifiedModuleExtraReq method with a null input.
     */
    @Test(expected = NullPointerException.class)
    public void testConvertToUnifiedModuleExtraReq_Null() throws Throwable {
        // Act
        // Explicitly casting null to GoodReviewReq
        detailTagService.convertToUnifiedModuleExtraReq((GoodReviewReq) null);
        // Assert
        // Expect NullPointerException
    }
}
