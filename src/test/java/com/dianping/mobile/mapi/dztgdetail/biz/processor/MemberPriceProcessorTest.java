package com.dianping.mobile.mapi.dztgdetail.biz.processor;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.MemberPriceProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MemberPriceWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.sankuai.mpmctmember.process.common.enums.MemberChargeTypeEnum;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.MemberBaseInfoDTO;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.MemberDiscountInfoDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2024/7/6
 * @since mapi-dztgdetail-web
 */
@RunWith(MockitoJUnitRunner.class)
public class MemberPriceProcessorTest {

    @InjectMocks
    MemberPriceProcessor memberPriceProcessor;

    @Mock
    MemberPriceWrapper memberPriceWrapper;

    @Test
    public void getMemberExclusiveTest() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        when(memberPriceWrapper.isMemberPriceProcessorEnable(any())).thenReturn(true);
        memberPriceProcessor.isEnable(ctx);
        memberPriceProcessor.prepare(ctx);
        Assert.assertNull(ctx.getFutureCtx().getMemberDiscountInfoRespDTOFuture());
    }

    @Test
    public void getMemberExclusiveTest2() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        memberPriceProcessor.process(ctx);
        memberPriceProcessor.prepare(ctx);
        Assert.assertNull(ctx.getFutureCtx().getMemberDiscountInfoRespDTOFuture());
    }

    @Test
    public void getMemberExclusiveTest1() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        boolean result = memberPriceWrapper.isMemberPriceProcessorEnable(ctx);
        assertFalse(result);

    }

    /**
     * 测试 notJoinMemberChargeCard 当 memberDiscountInfoDTO 为 null 时
     */
    @Test
    public void testNotJoinMemberChargeCardWhenMemberDiscountInfoDTOIsNull() {
        // arrange
        MemberDiscountInfoDTO memberDiscountInfoDTO = null;

        // act
        boolean result = memberPriceProcessor.notJoinMemberChargeCard(memberDiscountInfoDTO);

        // assert
        assertTrue("应当返回 true 当 memberDiscountInfoDTO 为 null", result);
    }

    /**
     * 测试 notJoinMemberChargeCard 当 memberBaseInfo 为 null 时
     */
    @Test
    public void testNotJoinMemberChargeCardWhenMemberBaseInfoIsNull() {
        // arrange
        MemberDiscountInfoDTO memberDiscountInfoDTO = new MemberDiscountInfoDTO();

        // act
        boolean result = memberPriceProcessor.notJoinMemberChargeCard(memberDiscountInfoDTO);

        // assert
        assertTrue("应当返回 true 当 memberBaseInfo 为 null", result);
    }

    /**
     * 测试 notJoinMemberChargeCard 当 chargeType 为 CHARGE 且 member 为 false 时
     */
    @Test
    public void testNotJoinMemberChargeCardWhenChargeTypeIsChargeAndMemberIsFalse() {
        // arrange
        MemberBaseInfoDTO memberBaseInfo = new MemberBaseInfoDTO();
        memberBaseInfo.setMember(false);
        MemberDiscountInfoDTO memberDiscountInfoDTO = new MemberDiscountInfoDTO();
        memberDiscountInfoDTO.setMemberBaseInfo(memberBaseInfo);
        memberDiscountInfoDTO.setChargeType(MemberChargeTypeEnum.CHARGE.getCode());

        // act
        boolean result = memberPriceProcessor.notJoinMemberChargeCard(memberDiscountInfoDTO);

        // assert
        assertTrue("应当返回 true 当 chargeType 为 CHARGE 且 member 为 false", result);
    }

    /**
     * 测试 notJoinMemberChargeCard 当 chargeType 不为 CHARGE 时
     */
    @Test
    public void testNotJoinMemberChargeCardWhenChargeTypeIsNotCharge() {
        // arrange
        MemberBaseInfoDTO memberBaseInfo = new MemberBaseInfoDTO();
        memberBaseInfo.setMember(true);
        MemberDiscountInfoDTO memberDiscountInfoDTO = new MemberDiscountInfoDTO();
        memberDiscountInfoDTO.setMemberBaseInfo(memberBaseInfo);
        memberDiscountInfoDTO.setChargeType(MemberChargeTypeEnum.FREE.getCode());

        // act
        boolean result = memberPriceProcessor.notJoinMemberChargeCard(memberDiscountInfoDTO);

        // assert
        assertFalse("应当返回 false 当 chargeType 不为 CHARGE", result);
    }

    /**
     * 测试 notJoinMemberChargeCard 当 chargeType 为 CHARGE 且 member 为 true 时
     */
    @Test
    public void testNotJoinMemberChargeCardWhenChargeTypeIsChargeAndMemberIsTrue() {
        // arrange
        MemberBaseInfoDTO memberBaseInfo = new MemberBaseInfoDTO();
        memberBaseInfo.setMember(true);
        MemberDiscountInfoDTO memberDiscountInfoDTO = new MemberDiscountInfoDTO();
        memberDiscountInfoDTO.setMemberBaseInfo(memberBaseInfo);
        memberDiscountInfoDTO.setChargeType(MemberChargeTypeEnum.CHARGE.getCode());

        // act
        boolean result = memberPriceProcessor.notJoinMemberChargeCard(memberDiscountInfoDTO);

        // assert
        assertFalse("应当返回 false 当 chargeType 为 CHARGE 且 member 为 true", result);
    }
}
