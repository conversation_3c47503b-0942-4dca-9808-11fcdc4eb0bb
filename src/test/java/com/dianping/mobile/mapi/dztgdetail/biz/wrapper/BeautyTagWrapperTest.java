package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.beauty.tag.service.BeautySearchService;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Future;
import java.util.HashMap;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class BeautyTagWrapperTest {

    @InjectMocks
    private BeautyTagWrapper beautyTagWrapper;

    @Mock
    private BeautySearchService beautySearchServiceFuture;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试preBeautyMainTag方法，当dpDealGroupIds为空时
     */
    @Test
    public void testPreBeautyMainTagWhenDpDealGroupIdsIsEmpty() throws Throwable {
        // arrange
        List<Integer> dpDealGroupIds = Arrays.asList();
        // act
        List<Future> result = beautyTagWrapper.preBeautyMainTag(dpDealGroupIds);
        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试preBeautyMainTag方法，当dpDealGroupIds不为空且multiLoadBeautyMainDealTag方法正常执行时
     */
    @Test
    public void testPreBeautyMainTagWhenDpDealGroupIdsIsNotEmptyAndMultiLoadBeautyMainDealTagWorksNormally() throws Throwable {
        // arrange
        List<Integer> dpDealGroupIds = Arrays.asList(1, 2, 3);
        when(beautySearchServiceFuture.multiLoadBeautyMainDealTag(anyList())).thenReturn(new HashMap<>());
        // act
        List<Future> result = beautyTagWrapper.preBeautyMainTag(dpDealGroupIds);
        // assert
        assertEquals(1, result.size());
        verify(beautySearchServiceFuture, times(1)).multiLoadBeautyMainDealTag(anyList());
    }

    /**
     * 测试preBeautyMainTag方法，当dpDealGroupIds不为空但multiLoadBeautyMainDealTag方法执行时抛出异常时
     */
    @Test
    public void testPreBeautyMainTagWhenDpDealGroupIdsIsNotEmptyAndMultiLoadBeautyMainDealTagThrowsException() throws Throwable {
        // arrange
        List<Integer> dpDealGroupIds = Arrays.asList(1, 2, 3);
        when(beautySearchServiceFuture.multiLoadBeautyMainDealTag(anyList())).thenThrow(new RuntimeException());
        // act
        List<Future> result = beautyTagWrapper.preBeautyMainTag(dpDealGroupIds);
        // assert
        assertEquals(0, result.size());
        verify(beautySearchServiceFuture, times(1)).multiLoadBeautyMainDealTag(anyList());
    }
}
