package com.dianping.mobile.mapi.dztgdetail.biz.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DouHuService_GetExpResultTest {

    @InjectMocks
    private DouHuService douHuService;

    private ModuleAbConfig moduleAbConfig;

    private AbConfig abConfig;

    @Mock
    private DouHuBiz douHuBiz;

    @Before
    public void setUp() {
        abConfig = new AbConfig();
        abConfig.setExpResult("test");
        moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setConfigs(Arrays.asList(abConfig));
    }

    /**
     * Helper method to inject mock DouHuBiz into DouHuService
     */
    private void injectDouHuBiz(DouHuService service, DouHuBiz mockBiz) throws Exception {
        Field douHuBizField = DouHuService.class.getDeclaredField("douHuBiz");
        douHuBizField.setAccessible(true);
        douHuBizField.set(service, mockBiz);
    }

    @Test
    public void testGetExpResultWhenModuleAbConfigIsNull() {
        String result = douHuService.getExpResult(null);
        assertNull(result);
    }

    @Test
    public void testGetExpResultWhenConfigsIsEmpty() {
        moduleAbConfig.setConfigs(null);
        String result = douHuService.getExpResult(moduleAbConfig);
        assertNull(result);
    }

    @Test
    public void testGetExpResultWhenFirstConfigIsNull() {
        moduleAbConfig.setConfigs(Arrays.asList(null, abConfig));
        String result = douHuService.getExpResult(moduleAbConfig);
        assertNull(result);
    }

    @Test
    public void testGetExpResultWhenAllValid() {
        String result = douHuService.getExpResult(moduleAbConfig);
        assertEquals("test", result);
    }

    /**
     * Test buildCatStr when DealGroupDTO is null
     */
    @Test
    public void testBuildCatStrWithNullDealGroupDTO() throws Throwable {
        // arrange
        DouHuService service = new DouHuService();
        DealCtx ctx = new DealCtx(null);
        ctx.setDealGroupDTO(null);
        // act
        String result = service.buildCatStr(ctx);
        // assert
        assertEquals("", result);
    }

    /**
     * Test buildCatStr when Category is null
     */
    @Test
    public void testBuildCatStrWithNullCategory() throws Throwable {
        // arrange
        DouHuService service = new DouHuService();
        DealCtx ctx = new DealCtx(null);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setCategory(null);
        ctx.setDealGroupDTO(dealGroupDTO);
        // act
        String result = service.buildCatStr(ctx);
        // assert
        assertEquals("", result);
    }

    /**
     * Test buildCatStr with valid input data
     */
    @Test
    public void testBuildCatStrWithValidData() throws Throwable {
        // arrange
        DouHuService service = new DouHuService();
        DealCtx ctx = new DealCtx(null);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO category = new DealGroupCategoryDTO();
        category.setCategoryId(123L);
        category.setServiceType("test-service");
        dealGroupDTO.setCategory(category);
        ctx.setDealGroupDTO(dealGroupDTO);
        // act
        String result = service.buildCatStr(ctx);
        // assert
        assertEquals("123:test-service", result);
    }

    /**
     * Tests getMiniVisionStyleExpResult method when getMiniVisionStyleAbTestSwitch returns non-null and expResult is "b", should return true.
     */
    @Test
    public void testGetMiniVisionStyleExpResultExpResultB() throws Throwable {
        // Arrange
        String unionId = "testUnionId";
        // Ensure this matches the condition in getMiniVisionStyleAbTestSwitch
        String pageSource = "mini_vision";
        boolean isMt = true;
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("b");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        when(douHuBiz.getAbByUnionIdAndExpId(eq(unionId), anyString(), anyString(), eq(isMt))).thenReturn(moduleAbConfig);
        // Act
        boolean result = douHuService.getMiniVisionStyleExpResult(unionId, pageSource, isMt);
        // Assert
        assertTrue(result);
    }

    /**
     * Tests getMiniVisionStyleExpResult method when getMiniVisionStyleAbTestSwitch returns null, should return false.
     */
    @Test
    public void testGetMiniVisionStyleExpResultReturnNull() throws Throwable {
        // Arrange
        String unionId = "testUnionId";
        String pageSource = "testPageSource";
        boolean isMt = true;
        // Act
        boolean result = douHuService.getMiniVisionStyleExpResult(unionId, pageSource, isMt);
        // Assert
        assertFalse(result);
    }

    /**
     * Tests getMiniVisionStyleExpResult method when getMiniVisionStyleAbTestSwitch returns non-null but expResult is not "b", should return false.
     */
    @Test
    public void testGetMiniVisionStyleExpResultExpResultNotB() throws Throwable {
        // Arrange
        String unionId = "testUnionId";
        // Ensure this matches the condition in getMiniVisionStyleAbTestSwitch
        String pageSource = "mini_vision";
        boolean isMt = true;
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("notB");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        when(douHuBiz.getAbByUnionIdAndExpId(eq(unionId), anyString(), anyString(), eq(isMt))).thenReturn(moduleAbConfig);
        // Act
        boolean result = douHuService.getMiniVisionStyleExpResult(unionId, pageSource, isMt);
        // Assert
        assertFalse(result);
    }

    /**
     * Test getRepurchaseShelfExpResult with a null EnvCtx.
     */
    @Test(expected = NullPointerException.class)
    public void testGetRepurchaseShelfExpResult_NullEnvCtx() throws Throwable {
        // arrange
        DouHuService douHuService = new DouHuService();
        EnvCtx envCtx = null;
        // act
        douHuService.getRepurchaseShelfExpResult(envCtx);
        // assert
        // Expecting NullPointerException
    }

    /**
     * Test getRepurchaseShelfExpResult with a non-null EnvCtx and isMt true.
     */
    @Test
    public void testGetRepurchaseShelfExpResult_EnvCtxIsMtTrue() throws Throwable {
        // arrange
        DouHuService douHuService = new DouHuService();
        DouHuBiz mockDouHuBiz = mock(DouHuBiz.class);
        injectDouHuBiz(douHuService, mockDouHuBiz);
        EnvCtx envCtx = mock(EnvCtx.class);
        when(envCtx.isMt()).thenReturn(true);
        when(envCtx.getUuid()).thenReturn("uuid");
        ModuleAbConfig expectedModuleAbConfig = new ModuleAbConfig();
        when(mockDouHuBiz.getAbByUuId("uuid", "MtRepurchaseShelfExp", true)).thenReturn(expectedModuleAbConfig);
        when(mockDouHuBiz.getExpResult(expectedModuleAbConfig)).thenReturn("ExpectedResult");
        // act
        String result = douHuService.getRepurchaseShelfExpResult(envCtx);
        // assert
        assertEquals("ExpectedResult", result);
        verify(mockDouHuBiz).getAbByUuId("uuid", "MtRepurchaseShelfExp", true);
        verify(mockDouHuBiz).getExpResult(expectedModuleAbConfig);
    }

    /**
     * Test getRepurchaseShelfExpResult with a non-null EnvCtx and isMt false.
     */
    @Test
    public void testGetRepurchaseShelfExpResult_EnvCtxIsMtFalse() throws Throwable {
        // arrange
        DouHuService douHuService = new DouHuService();
        DouHuBiz mockDouHuBiz = mock(DouHuBiz.class);
        injectDouHuBiz(douHuService, mockDouHuBiz);
        EnvCtx envCtx = mock(EnvCtx.class);
        when(envCtx.isMt()).thenReturn(false);
        ModuleAbConfig expectedModuleAbConfig = new ModuleAbConfig();
        when(mockDouHuBiz.getAbExpResultByUuidAndDpid(any(DealCtx.class), eq("DpRepurchaseShelfExp"))).thenReturn(expectedModuleAbConfig);
        when(mockDouHuBiz.getExpResult(expectedModuleAbConfig)).thenReturn("ExpectedResult");
        // act
        String result = douHuService.getRepurchaseShelfExpResult(envCtx);
        // assert
        assertEquals("ExpectedResult", result);
        verify(mockDouHuBiz).getAbExpResultByUuidAndDpid(any(DealCtx.class), eq("DpRepurchaseShelfExp"));
        verify(mockDouHuBiz).getExpResult(expectedModuleAbConfig);
    }

    /**
     * Test getRepurchaseShelfExpResult when DouHuBiz returns null for ModuleAbConfig.
     */
    @Test
    public void testGetRepurchaseShelfExpResult_NullModuleAbConfig() throws Throwable {
        // arrange
        DouHuService douHuService = new DouHuService();
        DouHuBiz mockDouHuBiz = mock(DouHuBiz.class);
        injectDouHuBiz(douHuService, mockDouHuBiz);
        EnvCtx envCtx = mock(EnvCtx.class);
        when(envCtx.isMt()).thenReturn(true);
        when(envCtx.getUuid()).thenReturn("uuid");
        when(mockDouHuBiz.getAbByUuId("uuid", "MtRepurchaseShelfExp", true)).thenReturn(null);
        // act
        String result = douHuService.getRepurchaseShelfExpResult(envCtx);
        // assert
        assertNull(result);
        verify(mockDouHuBiz).getAbByUuId("uuid", "MtRepurchaseShelfExp", true);
        verify(mockDouHuBiz, never()).getExpResult(any(ModuleAbConfig.class));
    }
}
