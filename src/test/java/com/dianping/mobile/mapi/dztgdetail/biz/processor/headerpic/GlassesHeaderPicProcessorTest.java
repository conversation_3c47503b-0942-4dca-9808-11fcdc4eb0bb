package com.dianping.mobile.mapi.dztgdetail.biz.processor.headerpic;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.ExhibitContentDTO;
import com.dianping.mobile.mapi.dztgdetail.entity.HeaderPicExhibitShowRuleConfig;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.google.common.collect.Lists;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.mockito.Mockito.mockStatic;

/**
 * @Author: <EMAIL>
 * @Date: 2024/8/16
 */
@RunWith(MockitoJUnitRunner.class)
public class GlassesHeaderPicProcessorTest {
    @InjectMocks
    private GlassesHeaderPicProcessor glassesHeaderPicProcessor;

    private MockedStatic<LionConfigUtils> lionConfigUtilsMockedStatic;

    @Before
    public void setUp() {
        lionConfigUtilsMockedStatic = mockStatic(LionConfigUtils.class);
    }

    @After
    public void tearDown() {
        lionConfigUtilsMockedStatic.close();
    }
    @Test
    public void testFillPicScale() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.hasCustomHeaderPic(Mockito.anyString(), Mockito.anyInt())).thenReturn(true);
        List<ContentPBO> result = Lists.newArrayList();
        ContentPBO contentPBO = new ContentPBO(1, "");
        contentPBO.setType(1);
        result.add(contentPBO);
        DealGroupPBO dealGroupPBO = new DealGroupPBO();

        ExhibitContentDTO exhibitContentDTO = new ExhibitContentDTO();
        exhibitContentDTO.setRecordCount(10);
        ctx.setExhibitContentDTO(exhibitContentDTO);

        HeaderPicExhibitShowRuleConfig ruleConfig = new HeaderPicExhibitShowRuleConfig(false, false);
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.getHeaderPicShowRule(ctx.getCategoryId())).thenReturn(ruleConfig);

        glassesHeaderPicProcessor.fillPicScale(ctx, result, dealGroupPBO);
        Assert.assertNotNull(result.get(0).getScale().equals("3:4"));
    }
}
