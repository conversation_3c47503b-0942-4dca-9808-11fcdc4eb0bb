package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ContentType;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ImageScaleEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.DigestInfoDTO;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealContentBuilderServiceBuildShowDarenVideoTest {

    @Spy
    @InjectMocks
    private DealContentBuilderService dealContentBuilderService;

    @Mock
    private DealCtx ctx;

    @Mock
    private DigestInfoDTO digestInfoDTO;

    /**
     * 测试当showCustomVideo返回true且hitBlacklist返回false时，应该添加视频内容到结果列表
     * 视频宽高比大于1时，应该设置为16:9比例
     */
    @Test
    public void testBuildShowDarenVideoShouldAddVideoWhenShowCustomVideoTrueAndNotHitBlacklistWithWideVideo() throws Throwable {
        // arrange
        List<ContentPBO> result = new ArrayList<>();
        doReturn(true).when(dealContentBuilderService).showCustomVideo(ctx);
        doReturn(false).when(dealContentBuilderService).hitBlacklist(ctx);
        when(ctx.getDigestInfoDTO()).thenReturn(digestInfoDTO);
        // 10MB
        when(digestInfoDTO.getVideoSize()).thenReturn(10485760);
        when(digestInfoDTO.getVideoFrameUrl()).thenReturn("http://example.com/frame.jpg");
        when(digestInfoDTO.getVideoUrl()).thenReturn("http://example.com/video.mp4");
        when(digestInfoDTO.getVideoWidth()).thenReturn(1920);
        when(digestInfoDTO.getVideoHeight()).thenReturn(1080);
        // act
        Method method = DealContentBuilderService.class.getDeclaredMethod("buildShowDarenVideo", List.class, DealCtx.class);
        method.setAccessible(true);
        method.invoke(dealContentBuilderService, result, ctx);
        // assert
        assertEquals(1, result.size());
        ContentPBO video = result.get(0);
        assertEquals(ContentType.VIDEO.getType(), video.getType());
        assertEquals("http://example.com/frame.jpg", video.getContent());
        assertEquals("http://example.com/video.mp4", video.getVideoUrl());
        assertEquals("当前Wi-Fi环境确定播放？预计花费流量10.00M", video.getDesc());
        assertEquals(ImageScaleEnum.SIXTEEN_TO_NINE.getScale(), video.getScale());
        verify(dealContentBuilderService).showCustomVideo(ctx);
        verify(dealContentBuilderService).hitBlacklist(ctx);
        verify(ctx).getDigestInfoDTO();
    }

    /**
     * 测试当showCustomVideo返回true且hitBlacklist返回false时，应该添加视频内容到结果列表
     * 视频宽高比小于等于1时，应该设置为3:4比例
     */
    @Test
    public void testBuildShowDarenVideoShouldAddVideoWhenShowCustomVideoTrueAndNotHitBlacklistWithTallVideo() throws Throwable {
        // arrange
        List<ContentPBO> result = new ArrayList<>();
        doReturn(true).when(dealContentBuilderService).showCustomVideo(ctx);
        doReturn(false).when(dealContentBuilderService).hitBlacklist(ctx);
        when(ctx.getDigestInfoDTO()).thenReturn(digestInfoDTO);
        // 5MB
        when(digestInfoDTO.getVideoSize()).thenReturn(5242880);
        when(digestInfoDTO.getVideoFrameUrl()).thenReturn("http://example.com/frame.jpg");
        when(digestInfoDTO.getVideoUrl()).thenReturn("http://example.com/video.mp4");
        when(digestInfoDTO.getVideoWidth()).thenReturn(1080);
        when(digestInfoDTO.getVideoHeight()).thenReturn(1920);
        // act
        Method method = DealContentBuilderService.class.getDeclaredMethod("buildShowDarenVideo", List.class, DealCtx.class);
        method.setAccessible(true);
        method.invoke(dealContentBuilderService, result, ctx);
        // assert
        assertEquals(1, result.size());
        ContentPBO video = result.get(0);
        assertEquals(ContentType.VIDEO.getType(), video.getType());
        assertEquals("http://example.com/frame.jpg", video.getContent());
        assertEquals("http://example.com/video.mp4", video.getVideoUrl());
        assertEquals("当前Wi-Fi环境确定播放？预计花费流量5.00M", video.getDesc());
        assertEquals(ImageScaleEnum.THREE_TO_FOUR.getScale(), video.getScale());
    }

    /**
     * 测试当showCustomVideo返回true且hitBlacklist返回false时，应该添加视频内容到结果列表
     * 视频宽高比等于1时，应该设置为3:4比例
     */
    @Test
    public void testBuildShowDarenVideoShouldAddVideoWhenShowCustomVideoTrueAndNotHitBlacklistWithSquareVideo() throws Throwable {
        // arrange
        List<ContentPBO> result = new ArrayList<>();
        doReturn(true).when(dealContentBuilderService).showCustomVideo(ctx);
        doReturn(false).when(dealContentBuilderService).hitBlacklist(ctx);
        when(ctx.getDigestInfoDTO()).thenReturn(digestInfoDTO);
        // 2MB
        when(digestInfoDTO.getVideoSize()).thenReturn(2097152);
        when(digestInfoDTO.getVideoFrameUrl()).thenReturn("http://example.com/frame.jpg");
        when(digestInfoDTO.getVideoUrl()).thenReturn("http://example.com/video.mp4");
        when(digestInfoDTO.getVideoWidth()).thenReturn(1080);
        when(digestInfoDTO.getVideoHeight()).thenReturn(1080);
        // act
        Method method = DealContentBuilderService.class.getDeclaredMethod("buildShowDarenVideo", List.class, DealCtx.class);
        method.setAccessible(true);
        method.invoke(dealContentBuilderService, result, ctx);
        // assert
        assertEquals(1, result.size());
        ContentPBO video = result.get(0);
        assertEquals(ContentType.VIDEO.getType(), video.getType());
        assertEquals("http://example.com/frame.jpg", video.getContent());
        assertEquals("http://example.com/video.mp4", video.getVideoUrl());
        assertEquals("当前Wi-Fi环境确定播放？预计花费流量2.00M", video.getDesc());
        assertEquals(ImageScaleEnum.THREE_TO_FOUR.getScale(), video.getScale());
    }

    /**
     * 测试当showCustomVideo返回false时，不应该添加任何内容到结果列表
     */
    @Test
    public void testBuildShowDarenVideoShouldNotAddVideoWhenShowCustomVideoFalse() throws Throwable {
        // arrange
        List<ContentPBO> result = new ArrayList<>();
        doReturn(false).when(dealContentBuilderService).showCustomVideo(ctx);
        // act
        Method method = DealContentBuilderService.class.getDeclaredMethod("buildShowDarenVideo", List.class, DealCtx.class);
        method.setAccessible(true);
        method.invoke(dealContentBuilderService, result, ctx);
        // assert
        assertEquals(0, result.size());
        verify(dealContentBuilderService).showCustomVideo(ctx);
        verify(dealContentBuilderService, never()).hitBlacklist(ctx);
        verify(ctx, never()).getDigestInfoDTO();
    }

    /**
     * 测试当showCustomVideo返回true但hitBlacklist返回true时，不应该添加任何内容到结果列表
     */
    @Test
    public void testBuildShowDarenVideoShouldNotAddVideoWhenShowCustomVideoTrueButHitBlacklist() throws Throwable {
        // arrange
        List<ContentPBO> result = new ArrayList<>();
        doReturn(true).when(dealContentBuilderService).showCustomVideo(ctx);
        doReturn(true).when(dealContentBuilderService).hitBlacklist(ctx);
        // act
        Method method = DealContentBuilderService.class.getDeclaredMethod("buildShowDarenVideo", List.class, DealCtx.class);
        method.setAccessible(true);
        method.invoke(dealContentBuilderService, result, ctx);
        // assert
        assertEquals(0, result.size());
        verify(dealContentBuilderService).showCustomVideo(ctx);
        verify(dealContentBuilderService).hitBlacklist(ctx);
        verify(ctx, never()).getDigestInfoDTO();
    }

    /**
     * 测试当showCustomVideo返回false且hitBlacklist返回true时，不应该添加任何内容到结果列表
     */
    @Test
    public void testBuildShowDarenVideoShouldNotAddVideoWhenShowCustomVideoFalseAndHitBlacklist() throws Throwable {
        // arrange
        List<ContentPBO> result = new ArrayList<>();
        doReturn(false).when(dealContentBuilderService).showCustomVideo(ctx);
        // act
        Method method = DealContentBuilderService.class.getDeclaredMethod("buildShowDarenVideo", List.class, DealCtx.class);
        method.setAccessible(true);
        method.invoke(dealContentBuilderService, result, ctx);
        // assert
        assertEquals(0, result.size());
        verify(dealContentBuilderService).showCustomVideo(ctx);
        verify(dealContentBuilderService, never()).hitBlacklist(ctx);
        verify(ctx, never()).getDigestInfoDTO();
    }

    /**
     * 测试视频大小为0字节时的流量计算
     */
    @Test
    public void testBuildShowDarenVideoWithZeroVideoSize() throws Throwable {
        // arrange
        List<ContentPBO> result = new ArrayList<>();
        doReturn(true).when(dealContentBuilderService).showCustomVideo(ctx);
        doReturn(false).when(dealContentBuilderService).hitBlacklist(ctx);
        when(ctx.getDigestInfoDTO()).thenReturn(digestInfoDTO);
        when(digestInfoDTO.getVideoSize()).thenReturn(0);
        when(digestInfoDTO.getVideoFrameUrl()).thenReturn("http://example.com/frame.jpg");
        when(digestInfoDTO.getVideoUrl()).thenReturn("http://example.com/video.mp4");
        when(digestInfoDTO.getVideoWidth()).thenReturn(1920);
        when(digestInfoDTO.getVideoHeight()).thenReturn(1080);
        // act
        Method method = DealContentBuilderService.class.getDeclaredMethod("buildShowDarenVideo", List.class, DealCtx.class);
        method.setAccessible(true);
        method.invoke(dealContentBuilderService, result, ctx);
        // assert
        assertEquals(1, result.size());
        ContentPBO video = result.get(0);
        assertEquals("当前Wi-Fi环境确定播放？预计花费流量0.00M", video.getDesc());
    }

    /**
     * 测试视频宽度为0时的比例计算（边界情况）
     */
    @Test
    public void testBuildShowDarenVideoWithZeroVideoWidth() throws Throwable {
        // arrange
        List<ContentPBO> result = new ArrayList<>();
        doReturn(true).when(dealContentBuilderService).showCustomVideo(ctx);
        doReturn(false).when(dealContentBuilderService).hitBlacklist(ctx);
        when(ctx.getDigestInfoDTO()).thenReturn(digestInfoDTO);
        when(digestInfoDTO.getVideoSize()).thenReturn(1048576);
        when(digestInfoDTO.getVideoFrameUrl()).thenReturn("http://example.com/frame.jpg");
        when(digestInfoDTO.getVideoUrl()).thenReturn("http://example.com/video.mp4");
        when(digestInfoDTO.getVideoWidth()).thenReturn(0);
        when(digestInfoDTO.getVideoHeight()).thenReturn(1080);
        // act
        Method method = DealContentBuilderService.class.getDeclaredMethod("buildShowDarenVideo", List.class, DealCtx.class);
        method.setAccessible(true);
        method.invoke(dealContentBuilderService, result, ctx);
        // assert
        assertEquals(1, result.size());
        ContentPBO video = result.get(0);
        // 当宽度为0时，比例为0，小于等于1，应该设置为3:4
        assertEquals(ImageScaleEnum.THREE_TO_FOUR.getScale(), video.getScale());
    }

    /**
     * 测试视频高度为0时的比例计算（边界情况）
     * 这种情况下会抛出ArithmeticException，但由于使用了BigDecimal的CEILING模式，实际会处理
     */
    @Test
    public void testBuildShowDarenVideoWithZeroVideoHeight() throws Throwable {
        // arrange
        List<ContentPBO> result = new ArrayList<>();
        doReturn(true).when(dealContentBuilderService).showCustomVideo(ctx);
        doReturn(false).when(dealContentBuilderService).hitBlacklist(ctx);
        when(ctx.getDigestInfoDTO()).thenReturn(digestInfoDTO);
        when(digestInfoDTO.getVideoSize()).thenReturn(1048576);
        when(digestInfoDTO.getVideoFrameUrl()).thenReturn("http://example.com/frame.jpg");
        when(digestInfoDTO.getVideoUrl()).thenReturn("http://example.com/video.mp4");
        when(digestInfoDTO.getVideoWidth()).thenReturn(1920);
        when(digestInfoDTO.getVideoHeight()).thenReturn(0);
        // act & assert
        Method method = DealContentBuilderService.class.getDeclaredMethod("buildShowDarenVideo", List.class, DealCtx.class);
        method.setAccessible(true);
        try {
            method.invoke(dealContentBuilderService, result, ctx);
            fail("Expected ArithmeticException to be thrown");
        } catch (Exception e) {
            // 期望抛出异常，因为除以0
            assertTrue(e.getCause() instanceof ArithmeticException);
        }
    }

    /**
     * 测试当结果列表已有内容时，视频应该被添加到列表末尾
     */
    @Test
    public void testBuildShowDarenVideoShouldAppendToExistingList() throws Throwable {
        // arrange
        List<ContentPBO> result = new ArrayList<>();
        ContentPBO existingContent = new ContentPBO(ContentType.TEXT.getType(), "existing content");
        result.add(existingContent);
        doReturn(true).when(dealContentBuilderService).showCustomVideo(ctx);
        doReturn(false).when(dealContentBuilderService).hitBlacklist(ctx);
        when(ctx.getDigestInfoDTO()).thenReturn(digestInfoDTO);
        when(digestInfoDTO.getVideoSize()).thenReturn(1048576);
        when(digestInfoDTO.getVideoFrameUrl()).thenReturn("http://example.com/frame.jpg");
        when(digestInfoDTO.getVideoUrl()).thenReturn("http://example.com/video.mp4");
        when(digestInfoDTO.getVideoWidth()).thenReturn(1920);
        when(digestInfoDTO.getVideoHeight()).thenReturn(1080);
        // act
        Method method = DealContentBuilderService.class.getDeclaredMethod("buildShowDarenVideo", List.class, DealCtx.class);
        method.setAccessible(true);
        method.invoke(dealContentBuilderService, result, ctx);
        // assert
        assertEquals(2, result.size());
        assertEquals(existingContent, result.get(0));
        ContentPBO video = result.get(1);
        assertEquals(ContentType.VIDEO.getType(), video.getType());
        assertEquals("http://example.com/frame.jpg", video.getContent());
        assertEquals("http://example.com/video.mp4", video.getVideoUrl());
    }
}
