package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.gm.bonus.exposure.api.dto.BEResponse;
import com.dianping.gm.bonus.exposure.api.dto.CommonBonusExposureDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.List;
import java.util.concurrent.Future;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

@RunWith(MockitoJUnitRunner.class)
public class GeneralMarketingWrapper_QueryBonusExposureTest {

    @InjectMocks
    private GeneralMarketingWrapper generalMarketingWrapper;

    @Mock
    private Future<BEResponse<List<CommonBonusExposureDTO>>> future;

    @Mock
    private BEResponse<List<CommonBonusExposureDTO>> response;

    @Mock
    private List<CommonBonusExposureDTO> data;

    // Test case when future input is null
    @Test
    public void testQueryBonusExposureFutureIsNull() throws Throwable {
        assertNull(generalMarketingWrapper.queryBonusExposure(null));
    }

    // Test case when the response from future.get() is null
    @Test
    public void testQueryBonusExposureResponseIsNull() throws Throwable {
        doReturn(null).when(future).get();
        assertNull(generalMarketingWrapper.queryBonusExposure(future));
    }

    // Test case when the response from future.get() indicates failure
    @Test
    public void testQueryBonusExposureResponseIsNotSuccess() throws Throwable {
        doReturn(response).when(future).get();
        when(response.isSuccess()).thenReturn(false);
        assertNull(generalMarketingWrapper.queryBonusExposure(future));
    }

    // Test case when the response from future.get() indicates success
    @Test
    public void testQueryBonusExposureResponseIsSuccess() throws Throwable {
        doReturn(response).when(future).get();
        when(response.isSuccess()).thenReturn(true);
        when(response.getData()).thenReturn(data);
        assertSame(data, generalMarketingWrapper.queryBonusExposure(future));
    }
}
