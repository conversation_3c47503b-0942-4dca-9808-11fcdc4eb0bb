package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEvent;
import static org.mockito.Mockito.when;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.DefaultExaminerHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.EntryExaminerHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.HealthCertificateExaminerHandler;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class MedicExaminerHighlightsV2Processor_OnApplicationEventTest {

    @InjectMocks
    private MedicExaminerHighlightsV2Processor medicExaminerHighlightsV2Processor;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private DefaultExaminerHandler defaultExaminerHandler;

    @Mock
    private EntryExaminerHandler entryExaminerHandler;

    @Mock
    private HealthCertificateExaminerHandler healthCertificateExaminerHandler;

    @Mock
    private ApplicationEvent applicationEvent;

    @Before
    public void setUp() {
        when(applicationContext.getBean(DefaultExaminerHandler.class)).thenReturn(defaultExaminerHandler);
        when(applicationContext.getBean(EntryExaminerHandler.class)).thenReturn(entryExaminerHandler);
        when(applicationContext.getBean(HealthCertificateExaminerHandler.class)).thenReturn(healthCertificateExaminerHandler);
    }

    /**
     * 测试 onApplicationEvent 方法
     */
    @Test
    public void testOnApplicationEvent() {
        // arrange
        // 无需准备数据
        // act
        medicExaminerHighlightsV2Processor.onApplicationEvent(applicationEvent);
        // assert
        assertEquals(defaultExaminerHandler, MedicExaminerHighlightsV2Processor.EXAMINER_SERVER_TYPE_V2_HANDLER_MAP.get("默认"));
        assertEquals(entryExaminerHandler, MedicExaminerHighlightsV2Processor.EXAMINER_SERVER_TYPE_V2_HANDLER_MAP.get("入职体检"));
        assertEquals(healthCertificateExaminerHandler, MedicExaminerHighlightsV2Processor.EXAMINER_SERVER_TYPE_V2_HANDLER_MAP.get("健康证检查"));
    }
}
