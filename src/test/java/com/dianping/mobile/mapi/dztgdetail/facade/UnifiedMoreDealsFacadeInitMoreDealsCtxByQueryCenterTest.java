package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.MobileHeader;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.MoreDealsCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedMoreDealsReq;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import java.lang.reflect.Method;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

public class UnifiedMoreDealsFacadeInitMoreDealsCtxByQueryCenterTest {

    @InjectMocks
    private UnifiedMoreDealsFacade unifiedMoreDealsFacade;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Mock
    private IMobileContext iMobileContext;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // Mock the iMobileContext to return a non-null header with a non-null newToken
        MobileHeader mobileHeader = new MobileHeader();
        mobileHeader.setNewToken("testToken");
        when(iMobileContext.getHeader()).thenReturn(mobileHeader);
    }

    private MoreDealsCtx invokePrivateMethod(UnifiedMoreDealsReq request, EnvCtx envCtx, IMobileContext iMobileContext) throws Exception {
        Method method = UnifiedMoreDealsFacade.class.getDeclaredMethod("initMoreDealsCtxByQueryCenter", UnifiedMoreDealsReq.class, EnvCtx.class, IMobileContext.class);
        method.setAccessible(true);
        return (MoreDealsCtx) method.invoke(unifiedMoreDealsFacade, request, envCtx, iMobileContext);
    }

    /**
     * 测试当 request.getDealGroupId() 为 null 时，dealGroupID 默认为 0
     */
    @Test
    public void testInitMoreDealsCtxByQueryCenter_DealGroupIdNull() throws Throwable {
        // arrange
        UnifiedMoreDealsReq request = new UnifiedMoreDealsReq();
        request.setDealGroupId(null);
        EnvCtx envCtx = new EnvCtx();
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(new DealGroupDTO());
        // act
        MoreDealsCtx moreDealsCtx = invokePrivateMethod(request, envCtx, iMobileContext);
        // assert
        assertEquals(0, moreDealsCtx.getDpId());
    }

    /**
     * 测试当 envCtx.isMt() 为 true 时，设置 mtId, mtShopIdLong 和 mtCityId
     */
    @Test
    public void testInitMoreDealsCtxByQueryCenter_EnvCtxIsMt() throws Throwable {
        // arrange
        UnifiedMoreDealsReq request = new UnifiedMoreDealsReq();
        request.setDealGroupId(123);
        // Set shopIdStr to simulate setting shopIdLong
        request.setShopIdStr("456");
        request.setCityId(789);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(new DealGroupDTO());
        // act
        MoreDealsCtx moreDealsCtx = invokePrivateMethod(request, envCtx, iMobileContext);
        // assert
        assertEquals(123, moreDealsCtx.getMtId());
        assertEquals(456L, moreDealsCtx.getMtShopIdLong());
        assertEquals(789, moreDealsCtx.getMtCityId());
    }

    /**
     * 测试当 envCtx.isMt() 为 false 时，设置 dpCityId, dpId 和 dpShopIdLong
     */
    @Test
    public void testInitMoreDealsCtxByQueryCenter_EnvCtxIsNotMt() throws Throwable {
        // arrange
        UnifiedMoreDealsReq request = new UnifiedMoreDealsReq();
        request.setDealGroupId(123);
        // Set shopIdStr to simulate setting shopIdLong
        request.setShopIdStr("456");
        request.setCityId(789);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(new DealGroupDTO());
        // act
        MoreDealsCtx moreDealsCtx = invokePrivateMethod(request, envCtx, iMobileContext);
        // assert
        assertEquals(123, moreDealsCtx.getDpId());
        assertEquals(456L, moreDealsCtx.getDpShopIdLong());
        assertEquals(789, moreDealsCtx.getDpCityId());
    }

    /**
     * 测试当 queryCenterWrapper.getDealGroupDTO 返回 null 时，channel 为 null
     */
    @Test
    public void testInitMoreDealsCtxByQueryCenter_DealGroupDTONull() throws Throwable {
        // arrange
        UnifiedMoreDealsReq request = new UnifiedMoreDealsReq();
        request.setDealGroupId(123);
        EnvCtx envCtx = new EnvCtx();
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(null);
        // act
        MoreDealsCtx moreDealsCtx = invokePrivateMethod(request, envCtx, iMobileContext);
        // assert
        assertNull(moreDealsCtx.getChannel());
    }

    /**
     * 测试当 queryCenterWrapper.getDealGroupDTO 抛出异常时，方法抛出异常
     */
    @Test(expected = Exception.class)
    public void testInitMoreDealsCtxByQueryCenter_ExceptionThrown() throws Throwable {
        // arrange
        UnifiedMoreDealsReq request = new UnifiedMoreDealsReq();
        request.setDealGroupId(123);
        EnvCtx envCtx = new EnvCtx();
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenThrow(new Exception("Test Exception"));
        // act
        invokePrivateMethod(request, envCtx, iMobileContext);
        // assert
        // Exception is expected
    }
}
