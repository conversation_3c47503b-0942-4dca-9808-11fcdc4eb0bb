package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.mobile.mapi.dztgdetail.biz.hotstyle.NailStyleService;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetHotNailStyleRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.nailstyle.HotNailStyleModuleVO;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.MockedStatic;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class NailStyleFacade_GetHotNailStyleTest {

    @InjectMocks
    private NailStyleFacade nailStyleFacade;

    @Mock
    private NailStyleService nailStyleService;

    /**
     * 测试 GetHotNailStyleRequest 对象无效的情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGetHotNailStyle_InvalidRequest() throws Throwable {
        GetHotNailStyleRequest request = new GetHotNailStyleRequest();
        EnvCtx envCtx = new EnvCtx();
        nailStyleFacade.getHotNailStyle(request, envCtx);
    }

    /**
     * 测试美甲热门款式模块开关关闭的情况
     */
    @Test
    public void testGetHotNailStyle_SwitchOff() throws Throwable {
        try (MockedStatic<LionConfigUtils> mockedStatic = mockStatic(LionConfigUtils.class)) {
            mockedStatic.when(LionConfigUtils::getHotNailStyleModuleSwitch).thenReturn(false);
            GetHotNailStyleRequest request = new GetHotNailStyleRequest();
            request.setDealGroupId(1L);
            request.setShopId(1L);
            EnvCtx envCtx = new EnvCtx();
            HotNailStyleModuleVO result = nailStyleFacade.getHotNailStyle(request, envCtx);
            verify(nailStyleService, never()).queryHotNailStyle(request, envCtx);
            assertNull(result);
        }
    }

    /**
     * 测试美甲热门款式模块开关打开的情况
     */
    @Test
    public void testGetHotNailStyle_SwitchOn() throws Throwable {
        try (MockedStatic<LionConfigUtils> mockedStatic = mockStatic(LionConfigUtils.class)) {
            mockedStatic.when(LionConfigUtils::getHotNailStyleModuleSwitch).thenReturn(true);
            GetHotNailStyleRequest request = new GetHotNailStyleRequest();
            request.setDealGroupId(1L);
            request.setShopId(1L);
            EnvCtx envCtx = new EnvCtx();
            HotNailStyleModuleVO expected = new HotNailStyleModuleVO();
            when(nailStyleService.queryHotNailStyle(request, envCtx)).thenReturn(expected);
            HotNailStyleModuleVO result = nailStyleFacade.getHotNailStyle(request, envCtx);
            verify(nailStyleService).queryHotNailStyle(request, envCtx);
            assertSame(expected, result);
        }
    }
}
