package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.factory.impl;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.glasses.factory.impl.GlassesDataStrategyImpl;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CommonAttrVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.highlights.DztgHighlightsModule;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GlassesDataStrategyImplTest {

    @InjectMocks
    private GlassesDataStrategyImpl glassesWayStrategy;

    @Mock
    private DealCtx ctx;

    @Mock
    private DealGroupDTO dealGroupDTO;

    /**
     * 测试buildMoudle方法，当DealGroupDTO对象和属性列表都不为空，且属性列表中存在名为"lens_function"的属性值等于"ture"时
     */
    @Test
    public void testBuildMoudleNormalCase() throws Throwable {
        // arrange
        ctx = new DealCtx(new EnvCtx());
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("whether_eyeglass_prescription");
        attrDTO.setValue(Arrays.asList("是"));
        ctx.setDealGroupDTO(dealGroupDTO);
        when(dealGroupDTO.getAttrs()).thenReturn(Arrays.asList(attrDTO));
        DztgHighlightsModule dztgHighlightsModule = new DztgHighlightsModule();
        dztgHighlightsModule.setAttrs(new ArrayList<CommonAttrVO>());
        ctx.setHighlightsModule(dztgHighlightsModule);


        // act
        glassesWayStrategy.buildMoudle(ctx);

        // assert
        assertEquals(1, ctx.getHighlightsModule().getAttrs().size());
        assertEquals("配镜数据", ctx.getHighlightsModule().getAttrs().get(0).getName());
        assertEquals("提供", ctx.getHighlightsModule().getAttrs().get(0).getValue());
    }

    @Test
    public void testBuildMoudleAttrNull() throws Throwable {
        // arrange
        ctx = new DealCtx(new EnvCtx());
        AttrDTO attrDTO = new AttrDTO();
        ctx.setDealGroupDTO(null);
        DztgHighlightsModule dztgHighlightsModule = new DztgHighlightsModule();
        dztgHighlightsModule.setAttrs(new ArrayList<CommonAttrVO>());
        ctx.setHighlightsModule(dztgHighlightsModule);

        // act
        glassesWayStrategy.buildMoudle(ctx);

        // assert
        assertEquals(0,ctx.getHighlightsModule().getAttrs().size());
    }
}