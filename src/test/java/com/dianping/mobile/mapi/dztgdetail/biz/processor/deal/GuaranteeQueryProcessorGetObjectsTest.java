package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.ChannelDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.GuaranteeObjectQueryDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.UserInfoDTO;
import com.sankuai.nib.price.operation.common.guarantee.enums.ChannelNoEnum;
import com.sankuai.nib.price.operation.common.guarantee.enums.PlatformEnum;
import com.sankuai.nib.price.operation.common.guarantee.enums.TerminalTypeEnum;
import java.util.Map;
import java.util.Set;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class GuaranteeQueryProcessorGetObjectsTest {

    @InjectMocks
    private GuaranteeQueryProcessor processor;

    /**
     * Test getObjects when ptId is null
     */
    @Test
    public void testGetObjects_WhenPtIdIsNull() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.getPtId()).thenReturn(null);
        // act
        Set<GuaranteeObjectQueryDTO> result = processor.getObjects(ctx);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test getObjects when ptId has value but not dental category
     */
    @Test
    public void testGetObjects_WhenPtIdHasValue_NotDentalCategory() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        Long ptId = 123L;
        when(ctx.getPtId()).thenReturn(ptId);
        // Not dental category (506)
        when(ctx.getCategoryId()).thenReturn(100);
        // act
        Set<GuaranteeObjectQueryDTO> result = processor.getObjects(ctx);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        GuaranteeObjectQueryDTO dto = result.iterator().next();
        assertEquals(String.valueOf(ptId), dto.getObjectId());
        // PRODUCT.getCode() = 1
        assertEquals(1, dto.getObjectType().intValue());
        assertNotNull(dto.getExt());
        assertTrue(dto.getExt().isEmpty());
    }

    /**
     * Test getObjects when ptId has value and is dental category
     */
    @Test
    public void testGetObjects_WhenPtIdHasValue_DentalCategory() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        Long ptId = 123L;
        long shopId = 456L;
        when(ctx.getPtId()).thenReturn(ptId);
        // Dental category
        when(ctx.getCategoryId()).thenReturn(506);
        when(ctx.getMtLongShopId()).thenReturn(shopId);
        // act
        Set<GuaranteeObjectQueryDTO> result = processor.getObjects(ctx);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        GuaranteeObjectQueryDTO dto = result.iterator().next();
        assertEquals(String.valueOf(ptId), dto.getObjectId());
        assertEquals(1, dto.getObjectType().intValue());
        Map<String, String> ext = dto.getExt();
        assertNotNull(ext);
        assertFalse(ext.isEmpty());
        assertEquals(String.valueOf(shopId), ext.get("poiId"));
    }

    /**
     * Test getObjects with maximum ptId value
     */
    @Test
    public void testGetObjects_WhenPtIdIsMaxValue() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        Long ptId = Long.MAX_VALUE;
        when(ctx.getPtId()).thenReturn(ptId);
        when(ctx.getCategoryId()).thenReturn(100);
        // act
        Set<GuaranteeObjectQueryDTO> result = processor.getObjects(ctx);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        GuaranteeObjectQueryDTO dto = result.iterator().next();
        assertEquals(String.valueOf(ptId), dto.getObjectId());
        assertEquals(1, dto.getObjectType().intValue());
        assertNotNull(dto.getExt());
        assertTrue(dto.getExt().isEmpty());
    }

    /**
     * Test getObjects with minimum ptId value
     */
    @Test
    public void testGetObjects_WhenPtIdIsMinValue() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        Long ptId = Long.MIN_VALUE;
        when(ctx.getPtId()).thenReturn(ptId);
        when(ctx.getCategoryId()).thenReturn(100);
        // act
        Set<GuaranteeObjectQueryDTO> result = processor.getObjects(ctx);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        GuaranteeObjectQueryDTO dto = result.iterator().next();
        assertEquals(String.valueOf(ptId), dto.getObjectId());
        assertEquals(1, dto.getObjectType().intValue());
        assertNotNull(dto.getExt());
        assertTrue(dto.getExt().isEmpty());
    }

    /**
     * Test getObjects when dental category but shop id is 0
     */
    @Test
    public void testGetObjects_WhenDentalCategory_ShopIdZero() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        Long ptId = 123L;
        when(ctx.getPtId()).thenReturn(ptId);
        // Dental category
        when(ctx.getCategoryId()).thenReturn(506);
        when(ctx.getMtLongShopId()).thenReturn(0L);
        // act
        Set<GuaranteeObjectQueryDTO> result = processor.getObjects(ctx);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        GuaranteeObjectQueryDTO dto = result.iterator().next();
        assertEquals(String.valueOf(ptId), dto.getObjectId());
        assertEquals(1, dto.getObjectType().intValue());
        Map<String, String> ext = dto.getExt();
        assertNotNull(ext);
        assertFalse(ext.isEmpty());
        assertEquals("0", ext.get("poiId"));
    }

    /**
     * Test getObjects when ptId is null
     */
    @Test
    public void testGetObjectsWhenPtIdIsNull() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.getPtId()).thenReturn(null);
        // act
        Set<GuaranteeObjectQueryDTO> result = processor.getObjects(ctx);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test getObjects when ptId has value
     */
    @Test
    public void testGetObjectsWhenPtIdHasValue() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        Long ptId = 123L;
        when(ctx.getPtId()).thenReturn(ptId);
        // Not 506 to avoid ext map logic
        when(ctx.getCategoryId()).thenReturn(100);
        // act
        Set<GuaranteeObjectQueryDTO> result = processor.getObjects(ctx);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        GuaranteeObjectQueryDTO dto = result.iterator().next();
        assertEquals(String.valueOf(ptId), dto.getObjectId());
        // PRODUCT.getCode() = 1
        assertEquals(1, dto.getObjectType().intValue());
        assertNotNull(dto.getExt());
        assertTrue(dto.getExt().isEmpty());
    }

    /**
     * Test live streaming channel
     */
    @Test
    public void testGetUserInfoDTO_LiveStreamingChannel() throws Throwable {
        // arrange
        DealCtx ctx = Mockito.mock(DealCtx.class);
        EnvCtx envCtx = Mockito.mock(EnvCtx.class);
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(ctx.getRequestSource()).thenReturn(RequestSourceEnum.LIVE_STREAM.getSource());
        GuaranteeQueryProcessor processor = new GuaranteeQueryProcessor();
        // act
        UserInfoDTO result = processor.getUserInfoDTO(ctx);
        // assert
        assertEquals(ChannelNoEnum.LIVE_STREAMING.getCode(), result.getChannel().getChannelNo());
    }
}
