package com.dianping.mobile.mapi.dztgdetail;

import com.alibaba.fastjson.JSONObject;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MultiSkuExpBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.RecommendServiceWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.BuyMoreSaveMoreCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.BuyMoreSaveMoreReq;
import com.dianping.mobile.mapi.dztgdetail.entity.CombinationDealInfo;
import com.dianping.mobile.mapi.dztgdetail.entity.SkuSummary;
import com.dianping.mobile.mapi.dztgdetail.facade.BuyMoreSaveMoreFacade;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.StockDTO;
import org.jetbrains.annotations.Nullable;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

/**
 * @Author: <EMAIL>
 * @Date: 2023/8/18
 */
@RunWith(MockitoJUnitRunner.class)
public class BuyMoreSaveMoreTest {
    @InjectMocks
    private BuyMoreSaveMoreFacade buyMoreSaveMoreFacade;
    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @InjectMocks
    private RecommendServiceWrapper recommendServiceWrapper;

    @Mock
    private MultiSkuExpBiz multiSkuExpBiz;

    private List<DealGroupDTO> dealGroupDTOs;
    /**
     * 测试正常场景
     */
    @Test
    @Ignore
    public void testFilterDealGroupDTOS_Normal() {
        // arrange
        boolean isMt = true;
        dealGroupDTOs = new ArrayList<>();
        DealGroupDTO dealGroupDTO1 = new DealGroupDTO();
        DealGroupBasicDTO dealGroupBasicDTO1 = new DealGroupBasicDTO();
        dealGroupBasicDTO1.setBeginSaleDate("2000-01-01 00:00:00");
        dealGroupBasicDTO1.setEndSaleDate("2200-01-01 00:00:00");
        dealGroupBasicDTO1.setStatus(1);
        dealGroupDTO1.setBasic(dealGroupBasicDTO1);
        StockDTO stockDTO = new StockDTO();
        stockDTO.setIsDpSoldOut(false);
        stockDTO.setIsMtSoldOut(false);
        dealGroupDTO1.setStock(stockDTO);
        dealGroupDTOs.add(dealGroupDTO1);
        // act
        List<DealGroupDTO> result = buyMoreSaveMoreFacade.filterDealGroupDTOS(dealGroupDTOs, isMt);
        // assert
        assertEquals(1, result.size());
    }
    /**
     * 测试异常场景
     */
    @Test
    public void testFilterDealGroupDTOS_InvalidProducts() {
        // arrange
        // 修改 dealGroupDTOs 列表，使所有商品都不满足过滤条件
        boolean isMt = true;
        dealGroupDTOs = new ArrayList<>();
        DealGroupDTO dealGroupDTO1 = new DealGroupDTO();
        DealGroupBasicDTO dealGroupBasicDTO1 = new DealGroupBasicDTO();
        dealGroupBasicDTO1.setBeginSaleDate("2000-01-01 00:00:00");
        dealGroupBasicDTO1.setEndSaleDate("2200-01-01 00:00:00");
        dealGroupBasicDTO1.setStatus(0);
        dealGroupDTO1.setBasic(dealGroupBasicDTO1);
        StockDTO stockDTO = new StockDTO();
        stockDTO.setIsDpSoldOut(false);
        stockDTO.setIsMtSoldOut(false);
        dealGroupDTO1.setStock(stockDTO);
        dealGroupDTOs.add(dealGroupDTO1);
        // act
        List<DealGroupDTO> result = buyMoreSaveMoreFacade.filterDealGroupDTOS(dealGroupDTOs, isMt);
        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试 getDealGroupSkuSummary 方法，当 dealGroupId 为 null 或小于等于 0 时，应返回 null。
     */
    @Test
    public void testGetDealGroupSkuSummary_InvalidDealGroupId() {
        // arrange
        Integer dealGroupId = 0;
        boolean isMt = true;

        // act
        SkuSummary result = buyMoreSaveMoreFacade.getDealGroupSkuSummary(dealGroupId, isMt);

        // assert
        assertNull(result);
    }

    /**
     * 测试 getDealGroupSkuSummary 方法，当 dealGroupId 有效且 isMt 为 true 时，应正确调用 dealGroupWrapper 和 multiSkuExpBiz。
     */
    @Test
    public void testGetDealGroupSkuSummary_ValidDealGroupIdIsMtTrue() throws Exception {
        // arrange
        Integer dealGroupId = 1;
        boolean isMt = true;
        Integer dpDealGroupId = 2;
        SkuSummary expectedSkuSummary = new SkuSummary();
        when(dealGroupWrapper.getDpDealGroupId(dealGroupId)).thenReturn(dpDealGroupId);
        when(multiSkuExpBiz.getSkuSummaryFromCacheWithFailOver(dpDealGroupId)).thenReturn(expectedSkuSummary);

        // act
        SkuSummary result = buyMoreSaveMoreFacade.getDealGroupSkuSummary(dealGroupId, isMt);

        // assert
        assertEquals(expectedSkuSummary, result);
    }

    /**
     * 测试 getDealGroupSkuSummary 方法，当 dealGroupId 有效且 isMt 为 false 时，应正确调用 multiSkuExpBiz。
     */
    @Test
    public void testGetDealGroupSkuSummary_ValidDealGroupIdIsMtFalse() throws Exception {
        // arrange
        Integer dealGroupId = 1;
        boolean isMt = false;
        SkuSummary expectedSkuSummary = new SkuSummary();
        when(multiSkuExpBiz.getSkuSummaryFromCacheWithFailOver(dealGroupId)).thenReturn(expectedSkuSummary);

        // act
        SkuSummary result = buyMoreSaveMoreFacade.getDealGroupSkuSummary(dealGroupId, isMt);

        // assert
        assertEquals(expectedSkuSummary, result);
    }

    /**
     * 测试 getDealGroupSkuSummary 方法，当 multiSkuExpBiz 抛出异常时，应返回 null。
     */
    @Test
    public void testGetDealGroupSkuSummary_ExceptionThrown() throws Exception {
        // arrange
        Integer dealGroupId = 1;
        boolean isMt = true;
        when(dealGroupWrapper.getDpDealGroupId(dealGroupId)).thenThrow(new RuntimeException());

        // act
        SkuSummary result = buyMoreSaveMoreFacade.getDealGroupSkuSummary(dealGroupId, isMt);

        // assert
        assertNull(result);
    }

}