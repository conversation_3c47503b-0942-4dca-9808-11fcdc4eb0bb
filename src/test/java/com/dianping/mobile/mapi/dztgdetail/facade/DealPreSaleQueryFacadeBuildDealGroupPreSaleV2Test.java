package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.account.utils.util.LionConfigUtils;
import com.dianping.gmkt.activity.api.dto.DealActivityDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.PreSaleCountDownReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CountDownPBO;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealPreSaleQueryFacadeBuildDealGroupPreSaleV2Test {

    @InjectMocks
    private DealPreSaleQueryFacade dealPreSaleQueryFacade;

    @Mock
    private PreSaleCountDownReq request;

    private List<AttrDTO> attrs;

    @Mock
    private DealGroupBasicDTO dealGroupBaseDTO;

    @Mock
    private DealActivityDTO dto;

    @Before
    public void setUp() {
        attrs = new ArrayList<>();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("pre_sale_tag");
        attrDTO.setValue(Arrays.asList("true"));
        attrs.add(attrDTO);
    }

    private CountDownPBO invokePrivateMethod(String methodName, Object... args) throws Exception {
        Method method = DealPreSaleQueryFacade.class.getDeclaredMethod(methodName, PreSaleCountDownReq.class, List.class, DealGroupBasicDTO.class);
        method.setAccessible(true);
        return (CountDownPBO) method.invoke(dealPreSaleQueryFacade, args);
    }

    private Object invokePrivateMethod(Object target, String methodName, Class<?>[] parameterTypes, Object... parameters) throws Exception {
        Method method = target.getClass().getDeclaredMethod(methodName, parameterTypes);
        method.setAccessible(true);
        return method.invoke(target, parameters);
    }

    @Test
    public void testBuildDealGroupPreSaleV2_NotPreSaleV2() throws Throwable {
        // Clear the list to simulate no pre-sale tag
        attrs.clear();
        CountDownPBO result = invokePrivateMethod("buildDealGroupPreSaleV2", request, attrs, dealGroupBaseDTO);
        assertNull(result);
    }

    @Test
    public void testBuildDealGroupPreSaleV2_NoCountDownProperty() throws Throwable {
        CountDownPBO result = invokePrivateMethod("buildDealGroupPreSaleV2", request, attrs, dealGroupBaseDTO);
        assertNull(result);
    }

    @Test
    public void testBuildDealGroupPreSaleV2_DealGroupBaseDTONull() throws Throwable {
        CountDownPBO result = invokePrivateMethod("buildDealGroupPreSaleV2", request, attrs, null);
        assertNull(result);
    }

    @Test
    public void testBuildDealGroupPreSaleV2_EndDateBefore48Hours() throws Throwable {
        CountDownPBO result = invokePrivateMethod("buildDealGroupPreSaleV2", request, attrs, dealGroupBaseDTO);
        assertNull(result);
    }

    @Test
    public void testBuildDealGroupPreSaleV2_EndDateAfterNow() throws Throwable {
        CountDownPBO result = invokePrivateMethod("buildDealGroupPreSaleV2", request, attrs, dealGroupBaseDTO);
        assertNull(result);
    }

    @Test
    public void testBuildUnifiedActivityDtoIsNull() throws Throwable {
        CountDownPBO result = (CountDownPBO) invokePrivateMethod(dealPreSaleQueryFacade, "buildUnifiedActivity", new Class<?>[] { DealActivityDTO.class, boolean.class }, null, true);
        assertNull(result);
    }
}
