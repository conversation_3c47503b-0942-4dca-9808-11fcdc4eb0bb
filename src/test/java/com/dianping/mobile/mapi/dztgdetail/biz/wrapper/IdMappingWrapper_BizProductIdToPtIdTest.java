package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.mpproduct.idservice.api.response.BizProductIdConvertResponse;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Future;
import org.mockito.InjectMocks;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class IdMappingWrapper_BizProductIdToPtIdTest {

    @Mock
    private Future<BizProductIdConvertResponse> future;

    @Mock
    private BizProductIdConvertResponse response;

    private IdMappingWrapper idMappingWrapper = new IdMappingWrapper();

    /**
     * 测试 future 为 null 的情况
     */
    @Test
    public void testBizProductIdToPtIdFutureIsNull() throws Throwable {
        // arrange
        Future<BizProductIdConvertResponse> future = null;
        // act
        Map<Long, Long> result = idMappingWrapper.bizProductIdToPtId(future);
        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试 getFutureResult 返回 null 的情况
     */
    @Test
    public void testBizProductIdToPtIdResponseIsNull() throws Throwable {
        // arrange
        when(future.get()).thenReturn(null);
        // act
        Map<Long, Long> result = idMappingWrapper.bizProductIdToPtId(future);
        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试 response.isSuccess 返回 false 的情况
     */
    @Test
    public void testBizProductIdToPtIdResponseIsNotSuccess() throws Throwable {
        // arrange
        when(future.get()).thenReturn(response);
        when(response.isSuccess()).thenReturn(false);
        // act
        Map<Long, Long> result = idMappingWrapper.bizProductIdToPtId(future);
        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试 response.getBizProductIdConvertResult 返回 null 的情况
     */
    @Test
    public void testBizProductIdToPtIdResultIsNull() throws Throwable {
        // arrange
        when(future.get()).thenReturn(response);
        when(response.isSuccess()).thenReturn(true);
        when(response.getBizProductIdConvertResult()).thenReturn(null);
        // act
        Map<Long, Long> result = idMappingWrapper.bizProductIdToPtId(future);
        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testBizProductIdToPtIdNormal() throws Throwable {
        // arrange
        Map<Long, Long> expectedMap = new HashMap<>();
        expectedMap.put(1L, 2L);
        when(future.get()).thenReturn(response);
        when(response.isSuccess()).thenReturn(true);
        when(response.getBizProductIdConvertResult()).thenReturn(expectedMap);
        // act
        Map<Long, Long> result = idMappingWrapper.bizProductIdToPtId(future);
        // assert
        assertEquals(1, result.size());
        assertEquals(Long.valueOf(2), result.get(1L));
    }
}
