package com.dianping.mobile.mapi.dztgdetail.helper;

import com.meituan.service.mobile.group.geo.bean.CityInfo;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.Collections;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.DealFields.GLOBAL_CITY_ID;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealFields;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DealFieldTransferHelper_ProduceRangeNameTest {

    // Correctly reference the actual global city ID.
    private static final int GLOBAL_CITY_ID = DealFields.GLOBAL_CITY_ID;

    @Test
    public void testProduceRangeNameWhenCityIdsIsEmpty() throws Throwable {
        String result = DealFieldTransferHelper.produceRangeName(Collections.emptyList(), null, "basicRangeName");
        assertEquals("basicRangeName", result);
    }

    @Test
    public void testProduceRangeNameWhenCurrentCityIsNullAndCityIdsSizeIsGreaterThanOne() throws Throwable {
        String result = DealFieldTransferHelper.produceRangeName(Arrays.asList(1, 2), null, "basicRangeName");
        assertEquals("多城市", result);
    }

    @Test
    public void testProduceRangeNameWhenCurrentCityIsNullAndCityIdsSizeIsOne() throws Throwable {
        String result = DealFieldTransferHelper.produceRangeName(Collections.singletonList(1), null, "basicRangeName");
        assertEquals("basicRangeName", result);
    }

    @Test
    public void testProduceRangeNameWhenCityIdsSizeIsGreaterThanOneAndCurrentCityIsNotNull() throws Throwable {
        CityInfo cityInfo = new CityInfo();
        cityInfo.setName("北京");
        String result = DealFieldTransferHelper.produceRangeName(Arrays.asList(1, 2), cityInfo, "basicRangeName");
        assertEquals("北京等", result);
    }

    @Test
    public void testProduceRangeNameWhenCityIdsSizeIsOneAndCurrentCityIsNotNull() throws Throwable {
        CityInfo cityInfo = new CityInfo();
        cityInfo.setName("北京");
        String result = DealFieldTransferHelper.produceRangeName(Collections.singletonList(1), cityInfo, "basicRangeName");
        assertEquals("basicRangeName", result);
    }

    @Test
    public void testProduceRangeNameWhenCityIdsSizeIsGreaterThanOne() throws Throwable {
        CityInfo cityInfo = new CityInfo();
        cityInfo.setName("北京");
        String result = DealFieldTransferHelper.produceRangeName(Arrays.asList(1, 2), cityInfo, "basicRangeName");
        assertEquals("北京等", result);
    }

    @Test
    public void testProduceRangeNameWhenCityIdsSizeIsOne() throws Throwable {
        CityInfo cityInfo = new CityInfo();
        cityInfo.setName("北京");
        String result = DealFieldTransferHelper.produceRangeName(Collections.singletonList(1), cityInfo, "basicRangeName");
        assertEquals("basicRangeName", result);
    }
}
