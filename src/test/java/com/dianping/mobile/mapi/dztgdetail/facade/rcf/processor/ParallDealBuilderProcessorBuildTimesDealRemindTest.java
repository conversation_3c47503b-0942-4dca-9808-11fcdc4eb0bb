package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import static com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum.MEITUAN_APP;
import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.when;
import com.alibaba.fastjson.JSON;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.base.dto.DealBaseDTO;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.LifeClearUtil;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.ParallDealBuilderProcessorUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.QualityEducationUtil;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.FreeDealEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.LEInsuranceAgreementEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.StyleTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealstruct.PnPurchaseNoteDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealstruct.PurchaseNoteModuleDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.dianping.mobile.mapi.dztgdetail.entity.CleaningProductLionConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.CostEffectivePinTuan;
import com.dianping.mobile.mapi.dztgdetail.entity.FreeDealConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.LifeClearHaiMaConfig;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.NewBuyBarHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.PriceProtectionHelper;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.EduDealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.TimesDealUtil;
import com.dianping.pigeon.remoting.common.codec.json.JacksonUtils;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import com.sankuai.mlive.goods.trade.api.model.GoodsSellingInfoDTO;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import org.apache.commons.lang.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * <AUTHOR>
 * @since 2023/9/26 14:41
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({ Lion.class, LionConfigUtils.class, DealBuyHelper.class, EduDealUtils.class, DealUtils.class, PriceProtectionHelper.class, NewBuyBarHelper.class, TimesDealUtil.class })
public class ParallDealBuilderProcessorBuildTimesDealRemindTest {

    @InjectMocks
    private ParallDealBuilderProcessor parallDealBuilderProcessor;

    private DealCtx ctx;

    @Mock
    private DealBuyBar dealBuyBar;

    private DealGroupPBO result;

    @Mock
    private EnvCtx envCtx;

    @Mock
    private FeaturesLayer featuresLayer;

    @Mock
    private DouHuBiz douHuBiz;

    @InjectMocks
    private QualityEducationUtil qualityEducationUtil;

    @InjectMocks
    private LifeClearUtil lifeClearUtil;

    private static final String magicPromoJsonNoLimit = "{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PromoDTO\",\"identity\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PromoIdentity\",\"promoId\":1544834328,\"promoType\":5,\"promoTypeDesc\":\"吃喝玩乐红包 \",\"sourceType\":1,\"promoShowType\":\"MAGICAL_MEMBER_PLATFORM_COUPON\"},\"amount\":[\"java.math.BigDecimal\",5],\"afterPromoPrice\":null,\"tag\":null,\"description\":null,\"consumeTimeDesc\":null,\"priceThrough\":true,\"canAssign\":false,\"couponAssignStatus\":null,\"detail\":null,\"extendDesc\":\"吃喝玩乐红包 \",\"startTime\":[\"java.util.Date\",1723901715000],\"endTime\":[\"java.util.Date\",1726502399000],\"promoDiscount\":null,\"promoQuantityLimit\":null,\"icon\":\"https://p0.meituan.net/travelcube/d5ba4048dc741674e6497f4c0c9ef55e1173.png\",\"couponTitle\":\"吃喝玩乐红包 \",\"totalStock\":null,\"remainStock\":null,\"priceLimitDesc\":\"无门槛\",\"minConsumptionAmount\":[\"java.math.BigDecimal\",0],\"useTimeDesc\":\"2024.08.17-2024.09.16\",\"promoIdentity\":\"magicalMemberCoupon\",\"promoStatus\":0,\"couponGroupId\":\"1544834328\",\"couponId\":\"401957102593951526\",\"couponValueType\":0,\"couponValueText\":\"5\",\"effectiveStartTime\":null,\"effectiveEndTime\":null,\"marketTag\":null,\"useType\":null,\"combinationId\":null,\"promoStock\":null,\"newActivityGroupId\":null,\"promoTextDTO\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PromoTextDTO\",\"title\":\"5元无门槛券\",\"subTitle\":\"吃喝玩乐红包 \",\"icon\":\"https://p0.meituan.net/travelcube/d5ba4048dc741674e6497f4c0c9ef55e1173.png\",\"promoDivideType\":\"MAGICAL_MEMBER_PLATFORM_COUPON\",\"promoDivideTypeDesc\":\"神会员平台券\",\"shelfTitle\":null,\"shelfSubTitle\":null,\"shelfIcon\":null,\"atmosphereBarIcon\":null,\"atmosphereBarText\":null,\"atmosphereBarButtonText\":null,\"atmosphereBarButtonUrl\":null,\"promoStatusText\":null},\"reductionSaleChannels\":null,\"amountShareDetail\":{\"@class\":\"java.util.HashMap\",\"{\\\"@class\\\":\\\"java.lang.Integer\\\",\\\"value\\\":1}\":[\"java.math.BigDecimal\",5],\"{\\\"@class\\\":\\\"java.lang.Integer\\\",\\\"value\\\":2}\":[\"java.math.BigDecimal\",0]},\"promotionExplanatoryTags\":[\"java.util.ArrayList\",[3]],\"promotionOtherInfoMap\":{\"@class\":\"java.util.HashMap\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"ASSIGNED_STATUS\\\"}\":\"ALREADY_ASSIGNED\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"COUPON_PACKAGE_ID\\\"}\":\"DOQZGcd1IgRdGTg43J8TWGAH/W4dkrcFIidROAFcDVA=\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"ASSET_TYPE\\\"}\":\"1\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"TSP_COUPON_GROUP_ID\\\"}\":\"22270241211401\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"TSP_COUPON_ID\\\"}\":\"1266521391341\"},\"promotionDisplayTextMap\":{\"@class\":\"java.util.HashMap\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"topLeftIcon\\\"}\":\"https://p0.meituan.net/ingee/6f4c76f970849530aadf296332059e0d11217.png\"},\"newUser\":false}";

    private static final String magicPromoJson = "{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PromoDTO\",\"identity\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PromoIdentity\",\"promoId\":1110952426,\"promoType\":5,\"promoTypeDesc\":\"丽人通用神券\",\"sourceType\":1,\"promoShowType\":\"MAGICAL_MEMBER_PLATFORM_COUPON\"},\"amount\":[\"java.math.BigDecimal\",13],\"afterPromoPrice\":null,\"tag\":null,\"description\":null,\"consumeTimeDesc\":null,\"priceThrough\":true,\"canAssign\":false,\"couponAssignStatus\":null,\"detail\":null,\"extendDesc\":\"丽人通用神券\",\"startTime\":[\"java.util.Date\",1725593261000],\"endTime\":[\"java.util.Date\",1725679661000],\"promoDiscount\":null,\"promoQuantityLimit\":null,\"icon\":\"https://p0.meituan.net/travelcube/d5ba4048dc741674e6497f4c0c9ef55e1173.png\",\"couponTitle\":\"丽人通用神券\",\"totalStock\":null,\"remainStock\":null,\"priceLimitDesc\":\"满89可用\",\"minConsumptionAmount\":[\"java.math.BigDecimal\",89],\"useTimeDesc\":\"一天内过期\",\"promoIdentity\":\"freeMagicalMemberCoupon\",\"promoStatus\":0,\"couponGroupId\":\"1110952426\",\"couponId\":\"401957102737635687\",\"couponValueType\":2,\"couponValueText\":\"13\",\"effectiveStartTime\":null,\"effectiveEndTime\":null,\"marketTag\":null,\"useType\":null,\"combinationId\":null,\"promoStock\":null,\"newActivityGroupId\":null,\"promoTextDTO\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PromoTextDTO\",\"title\":\"满89减13\",\"subTitle\":\"丽人通用神券\",\"icon\":\"https://p0.meituan.net/travelcube/d5ba4048dc741674e6497f4c0c9ef55e1173.png\",\"promoDivideType\":\"MAGICAL_MEMBER_PLATFORM_COUPON\",\"promoDivideTypeDesc\":\"神会员平台券\",\"shelfTitle\":null,\"shelfSubTitle\":null,\"shelfIcon\":null,\"atmosphereBarIcon\":null,\"atmosphereBarText\":null,\"atmosphereBarButtonText\":null,\"atmosphereBarButtonUrl\":null,\"promoStatusText\":\"未膨胀\"},\"reductionSaleChannels\":null,\"amountShareDetail\":{\"@class\":\"java.util.HashMap\",\"{\\\"@class\\\":\\\"java.lang.Integer\\\",\\\"value\\\":1}\":[\"java.math.BigDecimal\",13],\"{\\\"@class\\\":\\\"java.lang.Integer\\\",\\\"value\\\":2}\":[\"java.math.BigDecimal\",0]},\"promotionExplanatoryTags\":[\"java.util.ArrayList\",[4]],\"promotionOtherInfoMap\":{\"@class\":\"java.util.HashMap\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"CAN_INFLATE\\\"}\":\"true\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"BIZ_TOKEN\\\"}\":\"C2zK8qd7+g7BqJCgt5/rFjmoL8S9ENvuj9cIAdveXIUKq1PtYMumUuGebmnLvDbY\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"ASSIGNED_STATUS\\\"}\":\"ALREADY_ASSIGNED\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"AFTER_INFLATE_REQUIRE_AMOUNT\\\"}\":\"8900\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"ASSET_TYPE\\\"}\":\"2\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"AFTER_INFLATE_MONEY\\\"}\":\"1500\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"NIB_BIZ_LINE\\\"}\":\"202002\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"QUERY_INFLATE_FLAG\\\"}\":\"true\"},\"promotionDisplayTextMap\":{\"@class\":\"java.util.HashMap\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"promotionButtonText\\\"}\":\"免费膨胀\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"topLeftIcon\\\"}\":\"https://p0.meituan.net/ingee/0b375017c95f811adfb233e0774d4f524980.png\"},\"newUser\":false}";

    @Mock
    private DealCtx dealCtx;

    @Mock
    private DealGroupDTO dealGroupDTO;

    @Test
    public void isQualityEducationCategoryTest_true() {
        try (MockedStatic<Lion> mockedStatic = Mockito.mockStatic(Lion.class)) {
            DealCtx ctx = new DealCtx(new EnvCtx());
            DealGroupDTO dealGroup = new DealGroupDTO();
            DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
            dealGroupCategoryDTO.setCategoryId(512L);
            dealGroupCategoryDTO.setServiceTypeId(1373L);
            dealGroup.setCategory(dealGroupCategoryDTO);
            List<DealGroupTagDTO> tagDTOs = new ArrayList<>();
            DealGroupTagDTO dealGroupTagDTO = new DealGroupTagDTO();
            dealGroupTagDTO.setId(1L);
            tagDTOs.add(dealGroupTagDTO);
            dealGroup.setTags(tagDTOs);
            ctx.setDealGroupDTO(dealGroup);
            // 模拟返回一个包含512的列表
            // 模拟返回一个包含512的列表
            when(Lion.getList(anyString(), eq(LionConstants.QUALITY_EDU_CATION_BY_CATEGORY_IDS), eq(Integer.class), anyList())).thenReturn(Arrays.asList(512));
            // 模拟返回一个包含1373L的列表
            // 模拟返回一个包含1373L的列表
            when(Lion.getList(anyString(), eq(LionConstants.QUALITY_EDU_CATION_BY_SERVICE_TYPE_IDS), eq(Long.class), anyList())).thenReturn(Arrays.asList(1373L));
            Boolean bl = qualityEducationUtil.isQualityEducationCategory(ctx);
            Assert.assertFalse(bl);
        }
    }

    @Test
    public void testIsQualityEducationCategory_CategoryIdMatch_ServiceTypeIdNotMatch() {
        try (MockedStatic<Lion> mockedStatic = Mockito.mockStatic(Lion.class)) {
            // 初始化上下文和类目信息
            DealCtx ctx = new DealCtx(new EnvCtx());
            DealGroupDTO dealGroup = new DealGroupDTO();
            DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
            dealGroupCategoryDTO.setCategoryId(512L);
            dealGroupCategoryDTO.setServiceTypeId(13730L);
            dealGroup.setCategory(dealGroupCategoryDTO);
            List<DealGroupTagDTO> tagDTOs = new ArrayList<>();
            DealGroupTagDTO dealGroupTagDTO = new DealGroupTagDTO();
            dealGroupTagDTO.setId(1L);
            tagDTOs.add(dealGroupTagDTO);
            dealGroup.setTags(tagDTOs);
            ctx.setDealGroupDTO(dealGroup);
            // 配置模拟行为：二级类目符合条件
            PowerMockito.mockStatic(Lion.class);
            // 模拟返回一个包含512的列表
            // 模拟返回一个包含512的列表
            when(Lion.getList(anyString(), eq(LionConstants.QUALITY_EDU_CATION_BY_CATEGORY_IDS), eq(Integer.class), anyList())).thenReturn(Arrays.asList(0));
            // 模拟返回一个包含1373L的列表
            // 模拟返回一个包含1373L的列表
            when(Lion.getList(anyString(), eq(LionConstants.QUALITY_EDU_CATION_BY_SERVICE_TYPE_IDS), eq(Long.class), anyList())).thenReturn(Arrays.asList(1373L));
            // 断言：验证是否为优质教育类目
            assertTrue(qualityEducationUtil.isQualityEducationCategory(ctx));
        }
    }

    /**
     * Test when deal is a times deal with multiple verifications and not anxinxue
     */
    @Test
    public void testBuildTimesDealRemind_TimesDeal_MultipleVerification_NotAnxinxue() {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getBasic()).thenReturn(mock(com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO.class));
        when(dealGroupDTO.getBasic().getTradeType()).thenReturn(19);
        when(dealGroupDTO.getAttrs()).thenReturn(Arrays.asList());
        when(dealCtx.isAnXinXue()).thenReturn(false);
        // act
        List<String> result = parallDealBuilderProcessor.buildTimesDealRemind(dealCtx);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("可单次核销多份", result.get(0));
        assertEquals("仅支持整单退", result.get(1));
    }

    /**
     * Test when deal is a times deal and is anxinxue
     */
    @Test
    public void testBuildTimesDealRemind_TimesDeal_Anxinxue() {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getBasic()).thenReturn(mock(com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO.class));
        when(dealGroupDTO.getBasic().getTradeType()).thenReturn(19);
        when(dealGroupDTO.getAttrs()).thenReturn(Arrays.asList());
        when(dealCtx.isAnXinXue()).thenReturn(true);
        // act
        List<String> result = parallDealBuilderProcessor.buildTimesDealRemind(dealCtx);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("可单次核销多份", result.get(0));
    }
}
