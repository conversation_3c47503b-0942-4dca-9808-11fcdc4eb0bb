package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.ContextRefreshedEvent;

@RunWith(MockitoJUnitRunner.class)
public class DealDetailSpecificModuleHandlerContainerTest {

    @InjectMocks
    private DealDetailSpecificModuleHandlerContainer dealDetailSpecificModuleHandlerContainer;

    @Mock
    private ApplicationContext context;

    @Mock
    private ContextRefreshedEvent contextRefreshedEvent;

    @Mock
    private DealDetailSpecificModuleHandler dealDetailSpecificModuleHandler;

    /**
     * 测试 onApplicationEvent 方法，正常情况
     */
    @Test
    public void testOnApplicationEventNormal() throws Throwable {
        // arrange
        Map<String, DealDetailSpecificModuleHandler> map = new HashMap<>();
        map.put("test", dealDetailSpecificModuleHandler);
        when(context.getBeansOfType(DealDetailSpecificModuleHandler.class)).thenReturn(map);
        // act
        dealDetailSpecificModuleHandlerContainer.onApplicationEvent(contextRefreshedEvent);
        // assert
        verify(context, times(1)).getBeansOfType(DealDetailSpecificModuleHandler.class);
    }

    /**
     * 测试 onApplicationEvent 方法，异常情况
     */
    @Test(expected = BeansException.class)
    public void testOnApplicationEventException() throws Throwable {
        // arrange
        when(context.getBeansOfType(DealDetailSpecificModuleHandler.class)).thenThrow(new BeansException("No beans of type 'DealDetailSpecificModuleHandler' are defined") {
        });
        // act
        dealDetailSpecificModuleHandlerContainer.onApplicationEvent(contextRefreshedEvent);
        // assert
        verify(context, times(1)).getBeansOfType(DealDetailSpecificModuleHandler.class);
    }
}
