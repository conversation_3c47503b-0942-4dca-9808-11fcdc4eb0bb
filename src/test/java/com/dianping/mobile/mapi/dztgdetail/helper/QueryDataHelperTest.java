package com.dianping.mobile.mapi.dztgdetail.helper;

import com.sankuai.feitianplus.data.onedata.api.thrift.domain.QueryDataTitle;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

/**
 * @author: wuwenqiang
 * @create: 2024-11-28
 * @description:
 */
@RunWith(MockitoJUnitRunner.class)
public class QueryDataHelperTest {
    /**
     * 测试 getQueryDataMapList 方法，正常场景
     */
    @Test
    public void testGetQueryDataMapListNormal() throws Throwable {
        // arrange
        QueryDataTitle title1 = new QueryDataTitle();
        title1.setColumnName("name");
        QueryDataTitle title2 = new QueryDataTitle();
        title2.setColumnName("age");
        List<QueryDataTitle> titles = Arrays.asList(title1, title2);
        List<List<String>> dataList = Arrays.asList(Arrays.asList("John", "30"), Arrays.asList("Doe", "25"));

        // act
        List<Map<String, String>> result = QueryDataHelper.getQueryDataMapList(titles, dataList);

        // assert
        assertEquals(2, result.size());
        assertEquals("John", result.get(0).get("name"));
        assertEquals("30", result.get(0).get("age"));
        assertEquals("Doe", result.get(1).get("name"));
        assertEquals("25", result.get(1).get("age"));
    }

    /**
     * 测试 getQueryDataMapList 方法，dataList 为空
     */
    @Test
    public void testGetQueryDataMapListDataListEmpty() throws Throwable {
        // arrange
        QueryDataTitle title1 = new QueryDataTitle();
        title1.setColumnName("name");
        QueryDataTitle title2 = new QueryDataTitle();
        title2.setColumnName("age");
        List<QueryDataTitle> titles = Arrays.asList(title1, title2);

        // act
        List<Map<String, String>> result = QueryDataHelper.getQueryDataMapList(titles, Collections.emptyList());

        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 getQueryDataMapList 方法，queryDataTitles 为空
     */
    @Test
    public void testGetQueryDataMapListTitlesEmpty() throws Throwable {
        // arrange
        List<List<String>> dataList = Arrays.asList(Arrays.asList("John", "30"), Arrays.asList("Doe", "25"));

        // act
        List<Map<String, String>> result = QueryDataHelper.getQueryDataMapList(Collections.emptyList(), dataList);

        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 getQueryDataMapList 方法，dataList 中的某个 List<String> 与 queryDataTitles 大小不匹配
     */
    @Test
    public void testGetQueryDataMapListSizeMismatch() throws Throwable {
        // arrange
        QueryDataTitle title1 = new QueryDataTitle();
        title1.setColumnName("name");
        QueryDataTitle title2 = new QueryDataTitle();
        title2.setColumnName("age");
        List<QueryDataTitle> titles = Arrays.asList(title1, title2);
        List<List<String>> dataList = Arrays.asList(Arrays.asList("John"), Arrays.asList("Doe", "25"));

        // act
        List<Map<String, String>> result = QueryDataHelper.getQueryDataMapList(titles, dataList);

        // assert
        assertEquals(1, result.size());
        assertEquals("Doe", result.get(0).get("name"));
        assertEquals("25", result.get(0).get("age"));
    }
}
