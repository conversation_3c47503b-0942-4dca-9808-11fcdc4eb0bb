package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedModuleExtraReq;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedModuleExtraFacade_IsFromCreateOrderPreviewTest {

    @InjectMocks
    private UnifiedModuleExtraFacade unifiedModuleExtraFacade;

    private UnifiedModuleExtraReq request;

    @Before
    public void setUp() {
        request = new UnifiedModuleExtraReq();
    }

    /**
     * 测试 request 为 null 的情况
     */
    @Test
    public void testIsFromCreateOrderPreviewRequestIsNull() {
        assertFalse(unifiedModuleExtraFacade.isFromCreateOrderPreview(null));
    }

    /**
     * 测试 request 不为 null，但 pageSource 不等于 CREATE_ORDER_PREVIEW 的情况
     */
    @Test
    public void testIsFromCreateOrderPreviewPageSourceNotEqual() {
        request.setPageSource("other_source");
        assertFalse(unifiedModuleExtraFacade.isFromCreateOrderPreview(request));
    }

    /**
     * 测试 request 不为 null，且 pageSource 等于 CREATE_ORDER_PREVIEW 的情况
     */
    @Test
    public void testIsFromCreateOrderPreviewPageSourceEqual() {
        request.setPageSource(RequestSourceEnum.CREATE_ORDER_PREVIEW.getSource());
        assertTrue(unifiedModuleExtraFacade.isFromCreateOrderPreview(request));
    }
}
