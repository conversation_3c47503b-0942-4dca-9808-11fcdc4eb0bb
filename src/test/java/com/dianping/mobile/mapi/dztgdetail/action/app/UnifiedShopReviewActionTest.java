package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedShopReviewReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.UnifiedShopReviewList;
import com.dianping.mobile.mapi.dztgdetail.facade.UnifiedShopReviewFacade;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.mockito.Mockito.*;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

public class UnifiedShopReviewActionTest {

    @InjectMocks
    private UnifiedShopReviewAction unifiedShopReviewAction;

    @Mock
    private UnifiedShopReviewFacade unifiedShopReviewFacade;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testExecuteResultIsNull() throws Throwable {
        // arrange
        UnifiedShopReviewReq request = new UnifiedShopReviewReq();
        IMobileContext context = mock(IMobileContext.class);
        when(unifiedShopReviewFacade.queryUnifiedShopReviewList(any(), any(), any())).thenReturn(null);
        // act
        IMobileResponse response = unifiedShopReviewAction.execute(request, context);
        // assert
        assertEquals(Resps.NoDataResp.getStatusCode(), response.getStatusCode());
    }

    @Test
    public void testExecuteTopTitleIsEmpty() throws Throwable {
        // arrange
        UnifiedShopReviewReq request = new UnifiedShopReviewReq();
        IMobileContext context = mock(IMobileContext.class);
        UnifiedShopReviewList result = new UnifiedShopReviewList();
        result.setTopTitle("");
        when(unifiedShopReviewFacade.queryUnifiedShopReviewList(any(), any(), any())).thenReturn(result);
        // act
        IMobileResponse response = unifiedShopReviewAction.execute(request, context);
        // assert
        assertEquals(Resps.NoDataResp.getStatusCode(), response.getStatusCode());
    }
}
