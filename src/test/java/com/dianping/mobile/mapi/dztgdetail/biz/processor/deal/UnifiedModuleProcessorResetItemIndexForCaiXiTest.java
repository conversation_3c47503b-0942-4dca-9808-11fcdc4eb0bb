package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedModuleProcessorResetItemIndexForCaiXiTest {

    private UnifiedModuleProcessor processor;

    @Mock
    private DealCtx ctx;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        processor = new UnifiedModuleProcessor();
    }

    /**
     * Scenario 1: The result list is empty.
     * Expected: Return the original result list.
     */
    @Test
    public void testResetItemIndexForCaiXiEmptyResult() throws Throwable {
        // arrange
        List<ModuleConfigDo> result = Collections.emptyList();
        int publishCategoryId = 123;
        // act
        List<ModuleConfigDo> sortedResult = processor.resetItemIndexForCaiXi(ctx, publishCategoryId, result);
        // assert
        assertEquals(result, sortedResult);
    }

    /**
     * Scenario 2: The request source is not from CAIXI.
     * Expected: Return the original result list.
     */
    @Test
    public void testResetItemIndexForCaiXiNotFromCaixi() throws Throwable {
        // arrange
        List<ModuleConfigDo> result = Arrays.asList(new ModuleConfigDo(), new ModuleConfigDo());
        int publishCategoryId = 123;
        when(ctx.getRequestSource()).thenReturn("OTHER_SOURCE");
        // act
        List<ModuleConfigDo> sortedResult = processor.resetItemIndexForCaiXi(ctx, publishCategoryId, result);
        // assert
        assertEquals(result, sortedResult);
    }

    /**
     * Scenario 3: The result list is not empty, and the request source is from CAIXI, but the sort list is empty.
     * Expected: Return the original result list.
     */
    @Test
    public void testResetItemIndexForCaiXiEmptySortList() throws Throwable {
        // arrange
        List<ModuleConfigDo> result = Arrays.asList(new ModuleConfigDo(), new ModuleConfigDo());
        int publishCategoryId = 123;
        when(ctx.getRequestSource()).thenReturn("CAIXI");
        // act
        List<ModuleConfigDo> sortedResult = processor.resetItemIndexForCaiXi(ctx, publishCategoryId, result);
        // assert
        assertEquals(result, sortedResult);
    }

    /**
     * Scenario 4: The result list is not empty, the request source is from CAIXI, and the sort list is not empty.
     * Expected: Return the sorted result list based on the sortList.
     */
    @Test
    public void testResetItemIndexForCaiXiValidSortList() throws Throwable {
        // arrange
        ModuleConfigDo config1 = new ModuleConfigDo();
        config1.setKey("key1");
        ModuleConfigDo config2 = new ModuleConfigDo();
        config2.setKey("key2");
        List<ModuleConfigDo> result = Arrays.asList(config1, config2);
        int publishCategoryId = 123;
        when(ctx.getRequestSource()).thenReturn("CAIXI");
        // act
        List<ModuleConfigDo> sortedResult = processor.resetItemIndexForCaiXi(ctx, publishCategoryId, result);
        // assert
        assertEquals("key1", sortedResult.get(0).getKey());
        assertEquals("key2", sortedResult.get(1).getKey());
    }
}
