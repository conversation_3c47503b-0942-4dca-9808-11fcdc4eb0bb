package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.ReviewDetailDO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.ReviewUserModel;
import com.dianping.review.professional.ReviewDataV2;
import com.dianping.review.professional.ReviewPic;
import com.dianping.review.professional.Star;
import com.dianping.reviewremote.remote.utils.ReviewUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ShopReviewHelper_ReviewDataToReviewDetailDOTest {

    @Mock
    private ReviewDataV2 reviewData;

    @Mock
    private ReviewUserModel reviewUserModel;

    @Mock
    private Star star;

    private Map<Long, ReviewUserModel> reviewUserModelMap;

    private MockedStatic<ReviewUtils> reviewUtilsMockedStatic;

    @Before
    public void setUp() {
        reviewUserModelMap = new HashMap<>();
        reviewUserModelMap.put(1L, reviewUserModel);
        reviewUtilsMockedStatic = mockStatic(ReviewUtils.class);
        when(reviewData.getStar()).thenReturn(star);
        when(star.getAccurateValue()).thenReturn(5);
        when(reviewData.getReferType()).thenReturn(1);
        when(reviewData.getAddTime()).thenReturn(new java.util.Date());
        when(reviewData.getReviewBody()).thenReturn("Sample review body");
        when(reviewData.getReviewIdLong()).thenReturn(12345L);
        when(reviewData.getFlowerTotal()).thenReturn(10);
        when(reviewData.getFollowNoteNo()).thenReturn(5);
        when(reviewData.getScoreList()).thenReturn(new ArrayList<>());
        when(reviewData.getExpenseInfoList()).thenReturn(new ArrayList<>());
        when(reviewData.getExtInfoList()).thenReturn(new ArrayList<>());
        when(reviewData.getReviewPics()).thenReturn(new ArrayList<>());
        when(reviewData.getUserId()).thenReturn(1L);
    }

    @After
    public void tearDown() {
        reviewUtilsMockedStatic.close();
    }

    @Test
    public void testReviewDataToReviewDetailDOReviewPicListIsEmpty() throws Throwable {
        when(reviewData.getReviewPics()).thenReturn(new ArrayList<>());
        ReviewDetailDO result = ShopReviewHelper.reviewDataToReviewDetailDO(reviewData, reviewUserModelMap, true);
        assertNotNull(result);
        assertTrue(result.getReviewPicList().isEmpty());
    }

    @Test
    public void testReviewDataToReviewDetailDOReviewPicListContainsNull() throws Throwable {
        List<ReviewPic> reviewPicList = new ArrayList<>();
        reviewPicList.add(null);
        when(reviewData.getReviewPics()).thenReturn(reviewPicList);
        ReviewDetailDO result = ShopReviewHelper.reviewDataToReviewDetailDO(reviewData, reviewUserModelMap, true);
        assertNotNull(result);
        assertTrue(result.getReviewPicList().isEmpty());
    }

    @Test
    public void testReviewDataToReviewDetailDOReviewPicListContainsMoreThanNineElements() throws Throwable {
        List<ReviewPic> reviewPicList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            reviewPicList.add(new ReviewPic());
        }
        when(reviewData.getReviewPics()).thenReturn(reviewPicList);
        ReviewDetailDO result = ShopReviewHelper.reviewDataToReviewDetailDO(reviewData, reviewUserModelMap, true);
        assertNotNull(result);
        assertEquals(9, result.getReviewPicList().size());
    }

    @Test
    public void testReviewDataToReviewDetailDOReviewUserModelMapIsEmpty() throws Throwable {
        ReviewDetailDO result = ShopReviewHelper.reviewDataToReviewDetailDO(reviewData, new HashMap<>(), true);
        assertNotNull(result);
        assertNull(result.getReviewUserModel());
    }

    @Test
    public void testReviewDataToReviewDetailDOReviewUserModelMapDoesNotContainUserId() throws Throwable {
        when(reviewData.getUserId()).thenReturn(2L);
        ReviewDetailDO result = ShopReviewHelper.reviewDataToReviewDetailDO(reviewData, reviewUserModelMap, true);
        assertNotNull(result);
        assertNull(result.getReviewUserModel());
    }

    @Test
    public void testReviewDataToReviewDetailDOReviewUserModelMapContainsUserId() throws Throwable {
        when(reviewData.getUserId()).thenReturn(1L);
        ReviewDetailDO result = ShopReviewHelper.reviewDataToReviewDetailDO(reviewData, reviewUserModelMap, true);
        assertNotNull(result);
        assertEquals(reviewUserModel, result.getReviewUserModel());
    }

    @Test
    public void testReviewDataToReviewDetailDOSupplyIsFalse() throws Throwable {
        ReviewDetailDO result = ShopReviewHelper.reviewDataToReviewDetailDO(reviewData, reviewUserModelMap, false);
        assertNotNull(result);
        assertNotNull(result.getDetailUrl());
        assertTrue(result.getDetailUrl().contains("dianping://reviewdetail"));
    }
}
