package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.degrade.util.JsonCodec;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealstruct.PnPurchaseNoteDTO;
import com.dianping.mobile.mapi.dztgdetail.entity.PurchaseNoteStructuredConfig;
import com.dianping.pigeon.remoting.common.codec.json.JacksonUtils;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.*;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.PurchaseNoteDTO;
import com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO;
import lombok.Data;
import org.jetbrains.annotations.NotNull;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;

import java.util.Collections;
import java.util.Objects;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.*;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * @Author: <EMAIL>
 * @Date: 2024/1/18
 */
@RunWith(MockitoJUnitRunner.class)
public class DealDetailProcessorTest {
    @InjectMocks
    private DealDetailProcessor dealDetailProcessor;
    @Mock
    private DouHuBiz douHuBiz;

    private MockedStatic<Lion> lionMockedStatic;
    @Before
    public void setUp() {
        lionMockedStatic = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        lionMockedStatic.close();
    }

    @Test
    public void testHitStructuredPurchaseNote() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        envCtx.setUnionId("e931975d53f94652a9aee6cac17acca80000000000001453000");

        DealCtx ctx = new DealCtx(envCtx);
        ctx.setEnvCtx(envCtx);
        ctx.setUseQueryCenter(true);
        ctx.setQueryCenterHasError(false);
        ctx.setDpId(427153864);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(303);
        ctx.setChannelDTO(channelDTO);

        ModuleAbConfig moduleAbConfig = JSON.parseObject("{\"key\":\"DpPurchaseNoteStructure\",\"configs\":[{\"expId\":\"exp001891\",\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"1e0c4cfa-4f2f-4f0b-8a3e-cded75679626\\\",\\\"ab_id\\\":\\\"exp001891_b\\\"}\",\"useNewStyle\":true}]}", ModuleAbConfig.class);
        when(douHuBiz.getAbByUnionIdAndExpId(anyString(), anyString(), anyString(), anyBoolean())).thenReturn(moduleAbConfig);

        PurchaseNoteStructuredConfig config = JSON.parseObject("{\"grayLevel\":1,\"enableClientType\":[1,2,4,5],\"dealGroupIds\":[42831621,42715386],\"category2ExpId\":{\"mt502\":\"exp002675\",\"dp502\":\"exp002676\",\"mt303\":\"exp001872\",\"dp303\":\"exp001891\"},\"requestUnStructuredContent\":true}", PurchaseNoteStructuredConfig.class);
        lionMockedStatic.when(() -> Lion.getBean(anyString(), anyString(), Mockito.any())).thenReturn(config);

        assertTrue(dealDetailProcessor.hitStructuredPurchaseNote(ctx));

    }

    @Test
    public void testFillPurchaseNoteStructureInfo() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupDTO dealGroupDTO = JSON.parseObject("{\"dpDealGroupId\":427153864,\"mtDealGroupId\":427153864,\"basic\":{\"categoryId\":303,\"title\":\"30分钟采耳\",\"brandName\":\"足疗ID不一致门店\",\"titleDesc\":\"仅售118元，价值188元30分钟采耳，免费WiFi！\",\"beginSaleDate\":\"2023-12-15 16:17:53\",\"endSaleDate\":\"2025-01-17 17:13:19\",\"status\":1,\"saleChannel\":0,\"salePlatform\":3,\"sourceId\":102,\"tradeType\":3},\"image\":{\"defaultPicPath\":\"https://p0.meituan.net/dpmerchantpic/62d543291c7a44f08cb29917ba787a04303221.jpg\",\"allPicPaths\":\"https://p0.meituan.net/dpmerchantpic/62d543291c7a44f08cb29917ba787a04303221.jpg\"},\"category\":{\"categoryId\":303,\"serviceType\":\"采耳\",\"serviceTypeId\":109016},\"serviceProject\":{\"title\":\"团购详情\",\"salePrice\":\"118.00\",\"marketPrice\":\"188.00\",\"mustGroups\":[{\"groups\":[{\"skuId\":0,\"categoryId\":2104615,\"name\":\"采耳\",\"amount\":1,\"marketPrice\":\"188.0\",\"status\":10,\"attrs\":[{\"metaAttrId\":3108,\"attrName\":\"serviceTechnique\",\"chnName\":\"服务手法\",\"attrValue\":\"采耳\",\"rawAttrValue\":\"采耳\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":3022,\"attrName\":\"serviceProcessArrayNew\",\"chnName\":\"服务流程\",\"attrValue\":\"[{\\\"servicemethod\\\":\\\"采耳\\\",\\\"stepTime\\\":30}]\",\"rawAttrValue\":\"[{\\\"servicemethod\\\":\\\"采耳\\\",\\\"stepTime\\\":30}]\",\"valueType\":300,\"sequence\":0},{\"metaAttrId\":2441,\"attrName\":\"serviceDurationInt\",\"chnName\":\"服务时长\",\"attrValue\":\"30\",\"rawAttrValue\":\"30\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":3109,\"attrName\":\"serviceBodyRange\",\"chnName\":\"具体服务部位\",\"attrValue\":\"耳部\",\"rawAttrValue\":\"耳部\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":3015,\"attrName\":\"serviceMaterialAndTool\",\"chnName\":\"服务工具/材料\",\"attrValue\":\"选择服务工具/材料\",\"rawAttrValue\":\"选择服务工具/材料\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":3112,\"attrName\":\"earpickingTool\",\"chnName\":\"特色采耳工具\",\"attrValue\":\"一次性采耳工具\",\"rawAttrValue\":\"一次性采耳工具\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":3113,\"attrName\":\"unclassifiedTools\",\"chnName\":\"其他工具\",\"attrValue\":\"电动按摩床\",\"rawAttrValue\":\"电动按摩床\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":3030,\"attrName\":\"freeFood\",\"chnName\":\"免费餐食\",\"attrValue\":\"茶点水果\",\"rawAttrValue\":\"茶点水果\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":404171,\"attrName\":\"Fruit\",\"chnName\":\"水果\",\"attrValue\":\"水果拼盘\",\"rawAttrValue\":\"水果拼盘\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":404173,\"attrName\":\"TeaWater\",\"chnName\":\"茶水\",\"attrValue\":\"养生茶\",\"rawAttrValue\":\"养生茶\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":404176,\"attrName\":\"Snack\",\"chnName\":\"零食\",\"attrValue\":\"零食拼盘\",\"rawAttrValue\":\"零食拼盘\",\"valueType\":500,\"sequence\":0}]}]}],\"optionGroups\":[],\"structType\":\"uniform-structure-table\"},\"channel\":{\"channelId\":3,\"channelEn\":\"joy\",\"channelCn\":\"休闲娱乐\",\"channelGroupId\":2,\"channelGroupEn\":\"general\",\"channelGroupCn\":\"到店综合\"},\"attrs\":[{\"name\":\"product_finish_date\",\"value\":[\"1970-01-01 08:00:00\"],\"source\":0},{\"name\":\"calc_holiday_available\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_business_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"standardDealGroupKey\",\"value\":[\"MinutesEarCleaning\"],\"source\":0},{\"name\":\"product_channel_id_allowed\",\"value\":[\"0\"],\"source\":0},{\"name\":\"product_can_use_coupon\",\"value\":[\"true\"],\"source\":0},{\"name\":\"preSaleTag\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_third_party_verify\",\"value\":[\"false\"],\"source\":0},{\"name\":\"standardDealGroup\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_discount_rule_id\",\"value\":[\"0\"],\"source\":0},{\"name\":\"product_block_stock\",\"value\":[\"false\"],\"source\":0},{\"name\":\"service_type\",\"value\":[\"采耳\"],\"source\":0},{\"name\":\"reservation_is_needed_or_not\",\"value\":[\"是\"],\"source\":0},{\"name\":\"sys_deal_universal_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"category\",\"value\":[\"30\",\"3006\"],\"source\":0}],\"rule\":{\"buyRule\":{\"maxPerUser\":1,\"minPerUser\":1},\"useRule\":{\"receiptEffectiveDate\":{\"receiptDateType\":1,\"receiptValidDays\":90,\"receiptBeginDate\":\"2024-01-19 20:02:09\",\"receiptEndDate\":\"2024-04-18 23:59:59\",\"showText\":\"购买后90天内有效\"}},\"refundRule\":{\"supportRefundType\":1,\"supportOverdueAutoRefund\":true}},\"displayShopInfo\":{\"dpDisplayShopIds\":[447394734,606982776,606982779,207053329,606982778,606982773],\"mtDisplayShopIds\":[447394734,606982776,606982779,307053329,606982778,606982773]},\"tags\":[{\"id\":100002040,\"tagName\":\"采耳\"},{\"id\":100055938,\"tagName\":\"小于30分钟\"},{\"id\":100069833,\"tagName\":\"耳部\"},{\"id\":100053229,\"tagName\":\"采耳\"},{\"id\":100053330,\"tagName\":\"采耳\"},{\"id\":100007272,\"tagName\":\"足疗按摩虚拟节点\"},{\"id\":100007216,\"tagName\":\"足疗\"},{\"id\":100001016,\"tagName\":\"足疗\"},{\"id\":100002042,\"tagName\":\"足浴\"},{\"id\":100007217,\"tagName\":\"保健理疗\"},{\"id\":100036029,\"tagName\":\"部位\"},{\"id\":100036032,\"tagName\":\"耳部\"},{\"id\":100036044,\"tagName\":\"项目功效\"},{\"id\":100036045,\"tagName\":\"养生\"}],\"customer\":{\"originCustomerId\":41233564},\"deals\":[{\"dealId\":453418427,\"basic\":{\"title\":\"30分钟采耳\",\"originTitle\":\"30分钟采耳\",\"status\":1},\"price\":{\"salePrice\":\"118.00\",\"marketPrice\":\"188.00\",\"version\":5144021247},\"stock\":{\"dpSales\":0,\"dpTotal\":2000000,\"dpRemain\":2000000,\"mtSales\":0,\"mtTotal\":2000000,\"mtRemain\":2000000,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1},\"attrs\":[{\"name\":\"sku_receipt_type\",\"value\":[\"1\"],\"source\":0}],\"dealIdInt\":453418427}],\"price\":{\"salePrice\":\"118.00\",\"marketPrice\":\"188.00\",\"version\":5144021247},\"stock\":{\"dpSales\":0,\"dpTotal\":2000000,\"dpRemain\":2000000,\"mtSales\":0,\"mtTotal\":2000000,\"mtRemain\":2000000,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1},\"detail\":{\"dealGroupPics\":\"https://p0.meituan.net/dpmerchantpic/62d543291c7a44f08cb29917ba787a04303221.jpg\",\"images\":[\"https://p0.meituan.net/dpmerchantpic/62d543291c7a44f08cb29917ba787a04303221.jpg%40450w_280h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\"],\"importantPoint\":\"\",\"templateDetailDTOs\":[{\"title\":\"产品介绍\",\"content\":\"<p class=\\\"listitem\\\"></p>\\n<p class=\\\"explain\\\"></p>\\n<div class=\\\"img\\\"><img src=\\\"https://p1.meituan.net/dpmerchantpic/cdbb82de03ba20fdeaf63954ad3ba0e91318187.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /></div>\\n\\n\",\"type\":5,\"blockId\":0},{\"title\":\"团购详情\",\"content\":\"        <div class=\\\"detail-tit\\\"></div>\\n        <div>\\n            <table width=\\\"100%\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" class=\\\"detail-table\\\">\\n                <thead>\\n                <tr>\\n                    <th width=\\\"50%\\\">名称</th>\\n                    <th width=\\\"25%\\\">数量</th>\\n                    <th width=\\\"25%\\\">单价</th>\\n                </tr>\\n                </thead>\\n                <tbody>\\n                            <tr>\\n                                <td>采耳</td>\\n                                    <td class=\\\"tc\\\">1</td>\\n                                    <td class=\\\"tc\\\">188元</td>\\n                            </tr>\\n                    <tr class=\\\"total\\\">\\n                        <td></td>\\n                        <td class=\\\"tc\\\">总价<br><strong>团购价</strong></td>\\n                        <td class=\\\"tc\\\">188元<br><strong>118元</strong>\\n                        </td>\\n                    </tr>\\n                </tbody>\\n            </table>\\n        </div>\\n<div><p>补充说明</p></div>\\n\\n\",\"type\":1,\"blockId\":0},{\"title\":\"购买须知\",\"content\":\"<div class=\\\"detail-box\\\">\\n    <div class=\\\"purchase-notes\\\">\\n\\t\\t        <dl>\\n            <dt>有效期</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">\\n    购买后90天内有效\\n        </p>\\n                <p class=\\\"listitem\\\">团购券请您于有效期内验证，验证后长期有效</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>预约信息</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">为保证您的消费体验，请您每天13:00前预约</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>使用凭证</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">会员凭150010801652入场</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>适用人数</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">每张团购券最多1人使用</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>适用人群</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">只适用于6-29岁的人群体验</p>\\n                <p class=\\\"listitem\\\">只适用于初次到店非商户会员使用</p>\\n                <p class=\\\"listitem\\\">特殊人群：心脏病不宜体验</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>规则提醒</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">只适用于在大堂内使用</p>\\n                <p class=\\\"listitem\\\">团购券可分8次体验，商家将提供凭证，凭证可转借他人使用，消费按人次计</p>\\n                <p class=\\\"listitem\\\">每个部位限体验6次，相同部位可重复体验</p>\\n                <p class=\\\"listitem\\\">如需淋浴，需另付888元/人</p>\\n                <p class=\\\"listitem\\\">中途可去其他店使用</p>\\n                <p class=\\\"listitem\\\">支持指定技师，需另付点钟费1888</p>\\n                <p class=\\\"listitem\\\">不再与其他优惠同享\\n</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>商家服务</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">提供免费WiFi</p>\\n                <p class=\\\"listitem\\\">提供收费停车位</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>温馨提示</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">请遵守规则</p>\\n                <p class=\\\"listitem\\\">规范着装</p>\\n                <p class=\\\"listitem\\\">如需团购券发票，请您在消费时向商户咨询</p>\\n                <p class=\\\"listitem\\\">为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！</p>\\n            </dd>\\n        </dl>\\n    </div>\\n</div>\\n\",\"type\":2,\"blockId\":0}]},\"purchaseNote\":{\"title\":\"购买须知\",\"modules\":[{\"moduleName\":\"适用时间\",\"items\":[{\"itemName\":\"有效期\",\"itemValues\":[{\"type\":1,\"value\":\"购买后90天内有效\"}]}]},{\"moduleName\":\"适用人群\",\"items\":[{\"itemValues\":[{\"type\":1,\"value\":\"只适用于6-29岁的人群体验\"},{\"type\":1,\"value\":\"只适用于初次到店非商户会员使用\"},{\"type\":1,\"value\":\"特殊人群：心脏病不宜体验\"}]}]},{\"moduleName\":\"适用人数\",\"items\":[{\"itemName\":\"使用人数\",\"itemValues\":[{\"type\":1,\"value\":\"每张团购券最多1人使用\"}]}]},{\"moduleName\":\"预约规则\",\"items\":[{\"itemName\":\"\",\"itemValues\":[{\"type\":1,\"value\":\"为保证您的消费体验，请您每天13:00前预约\"}]}]},{\"moduleName\":\"商家服务\",\"items\":[{\"itemName\":\"\",\"itemValues\":[{\"type\":1,\"value\":\"提供免费WiFi\"}]}]},{\"moduleName\":\"温馨提示\",\"items\":[{\"itemName\":\"\",\"itemValues\":[{\"type\":1,\"value\":\"请遵守规则\"},{\"type\":1,\"value\":\"规范着装\"},{\"type\":1,\"value\":\"如需团购券发票，请您在消费时向商户咨询\"},{\"type\":1,\"value\":\"为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！\"}]}]},{\"moduleName\":\"其他规则\",\"items\":[{\"itemName\":\"使用凭证\",\"itemValues\":[{\"type\":1,\"value\":\"会员凭150010801652入场\"}]},{\"itemName\":\"规则提醒\",\"itemValues\":[{\"type\":1,\"value\":\"只适用于在大堂内使用\"},{\"type\":1,\"value\":\"团购券可分8次体验，商家将提供凭证，凭证可转借他人使用，消费按人次计\"},{\"type\":1,\"value\":\"每个部位限体验6次，相同部位可重复体验\"},{\"type\":1,\"value\":\"如需淋浴，需另付888元/人\"},{\"type\":1,\"value\":\"中途可去其他店使用\"},{\"type\":1,\"value\":\"支持指定技师，需另付点钟费1888\"},{\"type\":1,\"value\":\"不再与其他优惠同享\\n\"}]}]}]},\"dpDealGroupIdInt\":427153864,\"mtDealGroupIdInt\":427153864}", DealGroupDTO.class);
        ctx.setDealGroupDTO(dealGroupDTO);
        dealDetailProcessor.fillPurchaseNoteStructureInfo(ctx);
        assertTrue(Objects.nonNull(ctx.getPnPurchaseNoteDTO()));
    }

    /**
     * 测试非结构化服务类型
     */
    @Test
    public void testHitStructuredPurchaseNote_UnstructuredServiceType() {
        DealCtx dealCtx = mock(DealCtx.class);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(304L);
        categoryDTO.setServiceTypeId(1000L);
        dealGroupDTO.setCategory(categoryDTO);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealCtx.isMt()).thenReturn(true);
        when(dealCtx.getCategoryId()).thenReturn(304);

        PurchaseNoteStructuredConfig config = new PurchaseNoteStructuredConfig();
        config.setGrayLevel(1);
        config.setEnableClientType(Lists.newArrayList(1, 2, 4, 5));
        config.setUnstructuredServiceType(Lists.newArrayList("mt304.1000"));
        lionMockedStatic.when(() -> Lion.getBean(anyString(), anyString(), Mockito.any()))
                        .thenReturn(config);


        assertFalse(dealDetailProcessor.hitStructuredPurchaseNote(dealCtx));
    }

    @Test
    public void testHitStructuredPurchaseNote_dpMini() {
        DealCtx dealCtx = buildDealCtx();
        lionMockedStatic.when(() -> Lion.getBoolean(anyString(), anyString(), Mockito.any())).thenReturn(true);
        dealDetailProcessor.fillPurchaseNoteStructureInfo(dealCtx);

        assert dealCtx.isHasPurchaseNoteTable();

    }

    private @NotNull DealCtx buildDealCtx() {
        DealGroupDTO dealGroupDTO = JacksonUtils.deserialize(dataJson, DealGroupDTO.class);
        EnvCtx envCtx = new EnvCtx();
        // 1,2,4,5
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP);
        DealCtx dealCtx = new DealCtx(envCtx);
        dealCtx.setDealGroupDTO(dealGroupDTO);
        return dealCtx;
    }

    @Test
    public void testSetHasPurchaseNoteTable() throws InvocationTargetException, IllegalAccessException {
        DealCtx dealCtx = buildDealCtx();
        PurchaseNoteDTO purchaseNoteDTO = JacksonUtils.deserialize(sourcePnPurchaseDTOJson, PurchaseNoteDTO.class);
        Method method = PowerMockito.method(DealDetailProcessor.class, "setHasPurchaseNoteTable");

        lionMockedStatic.when(() -> Lion.getBoolean(anyString(), anyString(), Mockito.any())).thenReturn(true)
                .thenReturn(true).thenReturn(true);

        // 正常情况
        method.invoke(dealDetailProcessor, purchaseNoteDTO, dealCtx);
        assert dealCtx.isHasPurchaseNoteTable();

        // items中有null元素
        PurchaseNoteDTO purchaseNoteDTO1 = JacksonUtils.deserialize(sourcePnPurchaseDTOJson, PurchaseNoteDTO.class);
        List<StandardDisplayModuleDTO> modules = purchaseNoteDTO1.getModules();
        StandardDisplayModuleDTO standardDisplayModuleDTO = modules.get(0);
        standardDisplayModuleDTO.getItems().add(null);
        method.invoke(dealDetailProcessor, purchaseNoteDTO1, dealCtx);
        assert dealCtx.isHasPurchaseNoteTable();

        // itemValues中有null元素
        PurchaseNoteDTO purchaseNoteDTO2 = JacksonUtils.deserialize(sourcePnPurchaseDTOJson, PurchaseNoteDTO.class);
        List<StandardDisplayModuleDTO> modules1 = purchaseNoteDTO2.getModules();
        StandardDisplayModuleDTO standardDisplayModuleDTO1 = modules1.get(0);
        standardDisplayModuleDTO1.getItems().get(0).getItemValues().add(null);
        method.invoke(dealDetailProcessor, purchaseNoteDTO2, dealCtx);
        assert dealCtx.isHasPurchaseNoteTable();
    }

    /**
     * 根据lion内容获取详细的已经开启结构化购买须知的类目信息,实验信息
     */
    @Test
    public void getStructuredPurchaseNoteCategories() {
        PurchaseNoteStructuredConfig config = JsonCodec.decode(configJson, PurchaseNoteStructuredConfig.class);
        Map<String, String> category2ExpId = config.getCategory2ExpId();

        List<category2ExpIdDTO> category2ExpIdDTOS = category2ExpId.entrySet().stream().map(entry -> {
            category2ExpIdDTO category2ExpIdDTO = new category2ExpIdDTO();
            String key = entry.getKey();
            String value = entry.getValue();
            String letters = extractLetters(key);
            int lettersIndex = key.indexOf(letters);
            String numbers = key.substring(lettersIndex + 2);
            category2ExpIdDTO.setPlatform(letters);
            category2ExpIdDTO.setCategoryId(Integer.parseInt(numbers));
            category2ExpIdDTO.setExpId(value);
            return category2ExpIdDTO;
        }).collect(Collectors.toList());

        Map<String, List<category2ExpIdDTO>> groupResult = category2ExpIdDTOS.stream()
                .collect(Collectors.groupingBy(category2ExpIdDTO::getPlatform));

        List<category2ExpIdDTO> dpList = groupResult.get("dp");
        List<category2ExpIdDTO> mtList = groupResult.get("mt");

        Map<String, Object> dpJsonObject = getStructCategoryInfo(dpList);
        Map<String, Object> mtJsonObject = getStructCategoryInfo(mtList);

        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put("dp", dpJsonObject);
        resultMap.put("mt", mtJsonObject);
        assert resultMap.size() == 2;
    }

    // 输出各平台的结构化的类目,以及实验id(去重后)
    private Map<String, Object> getStructCategoryInfo(List<category2ExpIdDTO> list) {
        list.sort(Comparator.comparing(category2ExpIdDTO::getCategoryId));
        List<Integer> categories = Lists.newArrayList();
        Set<String> expIds = Sets.newHashSet();
        list.forEach(item -> {
            categories.add(item.getCategoryId());
            expIds.add(item.getExpId());
        });
        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put("categories", categories);
        resultMap.put("expIds", expIds);
        return resultMap;
    }

    private String extractLetters(String input) {
        return input.replaceAll("[^a-zA-Z]", "");
    }

    private static final String sourcePnPurchaseDTOJson = "{\"@class\":\"com.sankuai.general.product.query.center.client.dto.PurchaseNoteDTO\",\"title\":\"购买须知\",\"modules\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"适用时间\",\"icon\":\"https://p0.meituan.net/travelcube/eb358d1a211285207c636aa95594f3751524.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"有效时间\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"购买后90天内有效\"}]]}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"预约规则\",\"icon\":\"https://p0.meituan.net/travelcube/17303e587e8242902a1bf6ecd3eab10b1029.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"无需预约，如遇消费高峰时段您可能需要排队\"}]]}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"适用人数\",\"icon\":\"https://p0.meituan.net/travelcube/1f18aa2c0100e75060daf348283f3c861351.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"单次到店可核销多次\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"每张团购券最多1人使用\"}]]}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"其他规则\",\"icon\":\"https://p1.meituan.net/travelcube/4dac95c5f43a52f49b81ece1918925ea858.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"不再与其他优惠同享\"}]]}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"温馨提示\",\"icon\":\"https://p1.meituan.net/travelcube/09a2f606b1636f8b135b8582e3c0a53d1494.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"如需团购券发票，请您在消费时向商户咨询\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！\"}]]}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"退款规则\",\"icon\":\"https://p0.meituan.net/dztgdetailimages/aec8c981af1fe8e19dffb7f6748363152480.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"支持随时退、过期退\"}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"若次数未使用，可随时退全部实付金额\"}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"若发生核销后再申请退款，剩余所有未核销次数将一起退款，本商品为阶梯定价品，退款金额须按照如下规则进行计算\"}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":4,\"value\":\"{\\\"columns\\\":[{\\\"type\\\":1,\\\"value\\\":\\\"累计使用次数\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"单次价格\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"预计可退金额\\\"}],\\\"rows\\\":[{\\\"cells\\\":[{\\\"type\\\":1,\\\"value\\\":\\\"1\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"12.00\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"88.00\\\"}]},{\\\"cells\\\":[{\\\"type\\\":1,\\\"value\\\":\\\"2\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"9.78\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"78.22\\\"}]},{\\\"cells\\\":[{\\\"type\\\":1,\\\"value\\\":\\\"3\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"9.78\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"68.44\\\"}]},{\\\"cells\\\":[{\\\"type\\\":1,\\\"value\\\":\\\"4\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"9.78\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"58.66\\\"}]},{\\\"cells\\\":[{\\\"type\\\":1,\\\"value\\\":\\\"5\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"9.78\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"48.88\\\"}]},{\\\"cells\\\":[{\\\"type\\\":1,\\\"value\\\":\\\"6\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"9.78\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"39.10\\\"}]},{\\\"cells\\\":[{\\\"type\\\":1,\\\"value\\\":\\\"7\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"9.78\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"29.32\\\"}]},{\\\"cells\\\":[{\\\"type\\\":1,\\\"value\\\":\\\"8\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"9.78\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"19.54\\\"}]},{\\\"cells\\\":[{\\\"type\\\":1,\\\"value\\\":\\\"9\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"9.77\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"9.77\\\"}]},{\\\"cells\\\":[{\\\"type\\\":1,\\\"value\\\":\\\"10\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"9.77\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"0.00\\\"}]}],\\\"desc\\\":\\\"此价格仅供参考，如涉及营销优惠，会把优惠金额按比例分摊到单次价格中扣除，若购买时使用了优惠券，仅在整单退款且优惠券未过期时，返还优惠券\\\"}\"}]]}]]}]]}";

    private static final String configJson = "{\"grayLevel\":1,\"enableClientType\":[1,2,4,5,14],\"dealGroupIds\":[1020076937,1059396452,1059365375,942637959,942601533,942582888],\"category2ExpId\":{\"mt2009\":\"exp003154\",\"dp2009\":\"exp003155\",\"mt501\":\"exp003181\",\"dp501\":\"exp003179\",\"mt303\":\"exp003198\",\"dp303\":\"exp003197\",\"mt304\":\"exp003198\",\"dp304\":\"exp003197\",\"mt502\":\"exp003198\",\"dp502\":\"exp003197\",\"mt406\":\"exp003198\",\"dp406\":\"exp003197\",\"mt503\":\"exp003296\",\"mt506\":\"exp003296\",\"mt1604\":\"exp003296\",\"mt1612\":\"exp003296\",\"mt1613\":\"exp003296\",\"mt1614\":\"exp003296\",\"mt1615\":\"exp003296\",\"mt1616\":\"exp003296\",\"mt1617\":\"exp003296\",\"mt1618\":\"exp003296\",\"mt1619\":\"exp003296\",\"mt1620\":\"exp003296\",\"mt1621\":\"exp003296\",\"mt1622\":\"exp003296\",\"mt1623\":\"exp003296\",\"mt1624\":\"exp003296\",\"mt1625\":\"exp003296\",\"mt1626\":\"exp003296\",\"mt1627\":\"exp003296\",\"mt1628\":\"exp003296\",\"mt1629\":\"exp003296\",\"mt1630\":\"exp003296\",\"mt1313\":\"exp003296\",\"mt1314\":\"exp003296\",\"mt1601\":\"exp003296\",\"mt1602\":\"exp003296\",\"mt1603\":\"exp003296\",\"mt1605\":\"exp003296\",\"mt1606\":\"exp003296\",\"mt1607\":\"exp003296\",\"mt1608\":\"exp003296\",\"mt1609\":\"exp003296\",\"mt1610\":\"exp003296\",\"mt301\":\"exp003296\",\"mt302\":\"exp003296\",\"mt308\":\"exp003296\",\"mt314\":\"exp003296\",\"mt311\":\"exp003296\",\"mt318\":\"exp003296\",\"mt320\":\"exp003296\",\"mt454\":\"exp003296\",\"mt802\":\"exp003296\",\"mt803\":\"exp003296\",\"mt319\":\"exp003296\",\"mt323\":\"exp003296\",\"mt306\":\"exp003296\",\"mt316\":\"exp003296\",\"mt315\":\"exp003296\",\"mt505\":\"exp003296\",\"mt509\":\"exp003296\",\"mt511\":\"exp003296\",\"mt514\":\"exp003296\",\"mt515\":\"exp003296\",\"dp503\":\"exp003295\",\"dp506\":\"exp003295\",\"dp1604\":\"exp003295\",\"dp1612\":\"exp003295\",\"dp1613\":\"exp003295\",\"dp1614\":\"exp003295\",\"dp1615\":\"exp003295\",\"dp1616\":\"exp003295\",\"dp1617\":\"exp003295\",\"dp1618\":\"exp003295\",\"dp1619\":\"exp003295\",\"dp1620\":\"exp003295\",\"dp1621\":\"exp003295\",\"dp1622\":\"exp003295\",\"dp1623\":\"exp003295\",\"dp1624\":\"exp003295\",\"dp1625\":\"exp003295\",\"dp1626\":\"exp003295\",\"dp1627\":\"exp003295\",\"dp1628\":\"exp003295\",\"dp1629\":\"exp003295\",\"dp1630\":\"exp003295\",\"dp1313\":\"exp003295\",\"dp1314\":\"exp003295\",\"dp1601\":\"exp003295\",\"dp1602\":\"exp003295\",\"dp1603\":\"exp003295\",\"dp1605\":\"exp003295\",\"dp1606\":\"exp003295\",\"dp1607\":\"exp003295\",\"dp1608\":\"exp003295\",\"dp1609\":\"exp003295\",\"dp1610\":\"exp003295\",\"dp301\":\"exp003295\",\"dp302\":\"exp003295\",\"dp308\":\"exp003295\",\"dp314\":\"exp003295\",\"dp311\":\"exp003295\",\"dp318\":\"exp003295\",\"dp320\":\"exp003295\",\"dp454\":\"exp003295\",\"dp802\":\"exp003295\",\"dp803\":\"exp003295\",\"dp319\":\"exp003295\",\"dp323\":\"exp003295\",\"dp306\":\"exp003295\",\"dp316\":\"exp003295\",\"dp315\":\"exp003295\",\"dp505\":\"exp003295\",\"dp509\":\"exp003295\",\"dp511\":\"exp003295\",\"dp514\":\"exp003295\",\"dp515\":\"exp003295\",\"mt312\":\"exp003154\",\"dp312\":\"exp003155\"},\"requestUnStructuredContent\":true,\"unstructuredServiceType\":[\"mt304.141042\",\"dp304.141042\",\"mt311.141043\",\"dp311.141043\",\"mt1002.129048\",\"dp1002.129048\"]}";

    private static final String dataJson = "{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupDTO\",\"dpDealGroupId\":1035701478,\"mtDealGroupId\":1035701478,\"basic\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO\",\"categoryId\":303,\"title\":\"多次卡测试5.1\",\"brandName\":\"申亚足疗洗脚馆\",\"titleDesc\":\"仅售500元，价值940元多次卡测试5.1！\",\"beginSaleDate\":\"2024-12-12 17:06:00\",\"endSaleDate\":\"2025-09-24 15:52:39\",\"status\":1,\"saleChannel\":0,\"salePlatform\":3,\"sourceId\":102,\"tradeType\":19,\"platformCategoryId\":80303},\"image\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupImageDTO\",\"defaultPicPath\":\"https://p0.meituan.net/dpmerchantpic/ae875ca52c144780df6b852bdb27fcf5302146.jpg\",\"allPicPaths\":\"https://p0.meituan.net/dpmerchantpic/ae875ca52c144780df6b852bdb27fcf5302146.jpg\",\"videoPath\":null,\"videoCoverPath\":null,\"videoSize\":null,\"extendVideos\":null,\"allVideos\":null},\"category\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO\",\"categoryId\":303,\"serviceType\":\"足疗\",\"serviceTypeId\":106010,\"platformCategoryId\":80303},\"bgBu\":null,\"serviceProject\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO\",\"title\":\"团购详情\",\"salePrice\":\"500.0\",\"marketPrice\":\"188.0\",\"mustGroups\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO\",\"groups\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO\",\"skuId\":0,\"categoryId\":2104542,\"name\":\"足疗\",\"amount\":1,\"marketPrice\":null,\"status\":10,\"attrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":3022,\"attrName\":\"serviceProcessArrayNew\",\"chnName\":\"服务流程\",\"attrValue\":\"[{\\\"servicemethod\\\":\\\"泡脚\\\",\\\"stepTime\\\":\\\"10\\\"},{\\\"servicemethod\\\":\\\"足部按摩\\\",\\\"stepTime\\\":\\\"50\\\"}]\",\"rawAttrValue\":\"[{\\\"servicemethod\\\":\\\"泡脚\\\",\\\"stepTime\\\":\\\"10\\\"},{\\\"servicemethod\\\":\\\"足部按摩\\\",\\\"stepTime\\\":\\\"50\\\"}]\",\"unit\":null,\"valueType\":300,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":2441,\"attrName\":\"serviceDurationInt\",\"chnName\":\"服务时长\",\"attrValue\":\"60\",\"rawAttrValue\":\"60\",\"unit\":null,\"valueType\":401,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":3015,\"attrName\":\"serviceMaterialAndTool\",\"chnName\":\"服务工具/材料\",\"attrValue\":\"选择服务工具/材料\",\"rawAttrValue\":\"选择服务工具/材料\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":3018,\"attrName\":\"footbathBucket\",\"chnName\":\"泡脚桶\",\"attrValue\":\"木桶\",\"rawAttrValue\":\"木桶\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":3019,\"attrName\":\"footbathMaterial\",\"chnName\":\"泡脚包\",\"attrValue\":\"草本包\",\"rawAttrValue\":\"草本包\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":3030,\"attrName\":\"freeFood\",\"chnName\":\"免费餐食\",\"attrValue\":\"茶点水果\",\"rawAttrValue\":\"茶点水果\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":3109,\"attrName\":\"serviceBodyRange\",\"chnName\":\"具体服务部位\",\"attrValue\":\"足部\",\"rawAttrValue\":\"足部\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":404171,\"attrName\":\"Fruit\",\"chnName\":\"水果\",\"attrValue\":\"水果拼盘\",\"rawAttrValue\":\"水果拼盘\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":404173,\"attrName\":\"TeaWater\",\"chnName\":\"茶水\",\"attrValue\":\"养生茶\",\"rawAttrValue\":\"养生茶\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":404176,\"attrName\":\"Snack\",\"chnName\":\"零食\",\"attrValue\":\"零食拼盘\",\"rawAttrValue\":\"零食拼盘\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":2730,\"attrName\":\"skuCateId\",\"chnName\":\"项目分类\",\"attrValue\":\"2104542\",\"rawAttrValue\":\"2104542\",\"unit\":null,\"valueType\":402,\"sequence\":0}]]}]]}]],\"optionGroups\":[\"java.util.ArrayList\",[]],\"structType\":\"uniform-structure-table\"},\"channel\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupChannelDTO\",\"channelId\":3,\"channelEn\":\"joy\",\"channelCn\":\"休闲娱乐\",\"channelGroupId\":2,\"channelGroupEn\":\"general\",\"channelGroupCn\":\"到店综合\"},\"attrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_finish_date\",\"value\":[\"java.util.ArrayList\",[\"1970-01-01 08:00:00\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"calc_holiday_available\",\"value\":[\"java.util.ArrayList\",[\"1\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_business_type\",\"value\":[\"java.util.ArrayList\",[\"1\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_channel_id_allowed\",\"value\":[\"java.util.ArrayList\",[\"0\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_can_use_coupon\",\"value\":[\"java.util.ArrayList\",[\"true\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"preSaleTag\",\"value\":[\"java.util.ArrayList\",[\"false\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_third_party_verify\",\"value\":[\"java.util.ArrayList\",[\"false\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_block_stock\",\"value\":[\"java.util.ArrayList\",[\"false\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_discount_rule_id\",\"value\":[\"java.util.ArrayList\",[\"0\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"service_type\",\"value\":[\"java.util.ArrayList\",[\"足疗\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"tag_unifyProduct\",\"value\":[\"java.util.ArrayList\",[\"0\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"single_verification_quantity_desc\",\"value\":[\"java.util.ArrayList\",[\"单次到店可核销多次，可多人使用\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"limit_the_number_of_users\",\"value\":[\"java.util.ArrayList\",[\"1\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"reservation_is_needed_or_not\",\"value\":[\"java.util.ArrayList\",[\"否\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"sys_deal_universal_type\",\"value\":[\"java.util.ArrayList\",[\"1\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"sys_multi_sale_type\",\"value\":[\"java.util.ArrayList\",[\"2\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"category\",\"value\":[\"java.util.ArrayList\",[\"30\",\"3006\"]],\"source\":0,\"cnName\":null,\"type\":null}]],\"rule\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO\",\"buyRule\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.rule.buy.DealGroupBuyRuleDTO\",\"maxPerUser\":0,\"minPerUser\":1},\"useRule\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO\",\"receiptEffectiveDate\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.rule.use.ReceiptEffectiveDateDTO\",\"receiptDateType\":1,\"receiptValidDays\":90,\"receiptBeginDate\":\"2024-12-13 13:58:11\",\"receiptEndDate\":\"2025-03-13 23:59:59\",\"showText\":\"购买后90天内有效\"},\"availableDate\":null,\"disableDate\":null},\"bookingRule\":null,\"refundRule\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.rule.refund.DealGroupRefundRuleDTO\",\"supportRefundType\":1,\"supportOverdueAutoRefund\":true}},\"displayShopInfo\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupDisplayShopDTO\",\"dpDisplayShopIds\":[\"java.util.ArrayList\",[436822180]],\"mtDisplayShopIds\":[\"java.util.ArrayList\",[436822180]]},\"verifyShopInfo\":null,\"tags\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100056896,\"tagName\":\"30-60分钟\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100069830,\"tagName\":\"足部\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100054254,\"tagName\":\"足疗\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100069834,\"tagName\":\"肩颈\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100007272,\"tagName\":\"足疗按摩虚拟节点\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100007216,\"tagName\":\"足疗\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100001016,\"tagName\":\"足疗\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100002042,\"tagName\":\"足浴\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100036044,\"tagName\":\"项目功效\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100036045,\"tagName\":\"养生\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100088195,\"tagName\":\"60分钟足疗\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100090587,\"tagName\":\"60分钟足疗爆品待升级\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":10014354,\"tagName\":\"1\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":10014401,\"tagName\":\"测试\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100208256,\"tagName\":\"测试\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100210963,\"tagName\":\"茶饮\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100234788,\"tagName\":\"水果\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100220071,\"tagName\":\"小吃\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100231862,\"tagName\":\"茶饮\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100229628,\"tagName\":\"测试测试\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":10064338,\"tagName\":\"批量团泛1\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":10058438,\"tagName\":\"批量团泛1\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100313053,\"tagName\":\"推拿按摩\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100311049,\"tagName\":\"肩颈\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100312080,\"tagName\":\"推拿按摩\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100292942,\"tagName\":\"肩颈\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100305297,\"tagName\":\"全身SPA\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100304260,\"tagName\":\"按摩\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100305300,\"tagName\":\"头部按摩\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100298349,\"tagName\":\"肩颈按摩\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":10062440,\"tagName\":\"批量团泛1+2rules\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100090843,\"tagName\":\"重复2\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100090840,\"tagName\":\"批量新增示例V1\"}]],\"customer\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupCustomerDTO\",\"originCustomerId\":30014800,\"platformCustomerId\":1025027226},\"regions\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupRegionDTO\",\"dpCityId\":1,\"mtCityId\":10},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupRegionDTO\",\"dpCityId\":10,\"mtCityId\":40}]],\"notice\":null,\"notice2b\":null,\"deals\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO\",\"dealId\":461876074,\"basic\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.deal.DealBasicDTO\",\"title\":\"多次卡测试5.1\",\"originTitle\":\"多次卡测试5.1\",\"thirdPartyId\":null,\"status\":1,\"thirdPartyDealId\":null},\"price\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.PriceDTO\",\"salePrice\":\"500.00\",\"marketPrice\":\"940.00\",\"version\":5180498263,\"status\":null,\"prePayPrice\":null,\"finalPayPrice\":null},\"stock\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StockDTO\",\"dpSales\":0,\"dpTotal\":100000000,\"dpRemain\":100000000,\"mtSales\":0,\"mtTotal\":100000000,\"mtRemain\":100000000,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1,\"sharedSales\":0,\"sharedTotal\":0,\"sharedRemain\":0,\"isSharedSoldOut\":false},\"attrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"sys_multi_sale_number\",\"value\":[\"java.util.ArrayList\",[\"5\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"sku_receipt_type\",\"value\":[\"java.util.ArrayList\",[\"1\"]],\"source\":0,\"cnName\":null,\"type\":null}]],\"dealTimeStockDTO\":null,\"dealTimeStockPlanDTO\":null,\"rule\":null,\"image\":null,\"displayShop\":null,\"dealDelivery\":null,\"shopStocks\":null,\"bizDealId\":null,\"weeklyPricePlan\":null,\"dateTimePrice\":null,\"periodPrice\":null,\"dealIdInt\":461876074,\"bizDealIdInt\":null}]],\"price\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.PriceDTO\",\"salePrice\":\"500.00\",\"marketPrice\":\"940.00\",\"version\":5180498263,\"status\":null,\"prePayPrice\":null,\"finalPayPrice\":null},\"stock\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StockDTO\",\"dpSales\":0,\"dpTotal\":100000000,\"dpRemain\":100000000,\"mtSales\":0,\"mtTotal\":100000000,\"mtRemain\":100000000,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1,\"sharedSales\":null,\"sharedTotal\":null,\"sharedRemain\":null,\"isSharedSoldOut\":null},\"detail\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.detail.DealGroupDetailDTO\",\"dealGroupPics\":\"https://p0.meituan.net/dpmerchantpic/ae875ca52c144780df6b852bdb27fcf5302146.jpg\",\"images\":[\"java.util.ArrayList\",[\"https://p0.meituan.net/dpmerchantpic/ae875ca52c144780df6b852bdb27fcf5302146.jpg%40450w_280h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\"]],\"info\":null,\"importantPoint\":\"\",\"specialPoint\":null,\"productInfo\":null,\"editorInfo\":null,\"memberInfo\":null,\"shopInfo\":null,\"editorTeam\":null,\"summary\":null,\"templateDetailDTOs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.detail.DealGroupTemplateDetailDTO\",\"title\":\"产品介绍\",\"content\":\"<p class=\\\"listitem\\\"></p>\\n<p class=\\\"explain\\\"></p>\\n<div class=\\\"img\\\"><img src=\\\"https://p1.meituan.net/dpmerchantpic/dd4b0949e4026a9db10f23f590affe4d1221267.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /></div>\\n\\n\",\"type\":5,\"blockId\":0,\"mixedContents\":null,\"imageContents\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.detail.DealGroupTemplateDetailDTO\",\"title\":\"团购详情\",\"content\":\"        <div class=\\\"detail-tit\\\"></div>\\n        <div>\\n            <table width=\\\"100%\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" class=\\\"detail-table\\\">\\n                <thead>\\n                <tr>\\n                    <th width=\\\"50%\\\">名称</th>\\n                    <th width=\\\"25%\\\">数量</th>\\n                    <th width=\\\"25%\\\">单价</th>\\n                </tr>\\n                </thead>\\n                <tbody>\\n                            <tr>\\n                                <td>足疗</td>\\n                                    <td class=\\\"tc\\\">1</td>\\n                            </tr>\\n                    <tr class=\\\"total\\\">\\n                        <td></td>\\n                        <td class=\\\"tc\\\">总价<br><strong>团购价</strong></td>\\n                        <td class=\\\"tc\\\">188元<br><strong>500元</strong>\\n                        </td>\\n                    </tr>\\n                </tbody>\\n            </table>\\n        </div>\\n\\n\",\"type\":1,\"blockId\":0,\"mixedContents\":null,\"imageContents\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.detail.DealGroupTemplateDetailDTO\",\"title\":\"购买须知\",\"content\":\"<div class=\\\"detail-box\\\">\\n    <div class=\\\"purchase-notes\\\">\\n\\t\\t        <dl>\\n            <dt>有效期</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">\\n    购买后90天内有效\\n        </p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>预约信息</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">无需预约，如遇消费高峰时段您可能需要排队</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>适用人数</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">每张团购券最多1人使用</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>规则提醒</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">不再与其他优惠同享</p>\\n                <p class=\\\"listitem\\\">单次到店可核销多次，可多人使用</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>温馨提示</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">如需团购券发票，请您在消费时向商户咨询</p>\\n                <p class=\\\"listitem\\\">为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！</p>\\n                <p class=\\\"listitem\\\">购买后若全部未使用，在有效期内可申请全额退款；过期自动将未使用部分退款；若部分已使用，则不可退款。</p>\\n            </dd>\\n        </dl>\\n    </div>\\n</div>\\n\",\"type\":2,\"blockId\":0,\"mixedContents\":null,\"imageContents\":null}]]},\"spu\":null,\"standardServiceProject\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectDTO\",\"mustGroups\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectGroupDTO\",\"serviceProjectItems\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectItemDTO\",\"serviceProjectName\":\"足疗\",\"standardAttribute\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeDTO\",\"attrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"serviceProcessArrayNew\",\"attrCnName\":\"服务流程\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":1,\"simpleValues\":null,\"complexValues\":\"[{\\\"attrs\\\":[{\\\"attrName\\\":\\\"servicemethod\\\",\\\"attrCnName\\\":\\\"服务方式\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"泡脚\\\"]}]},{\\\"attrName\\\":\\\"stepTime\\\",\\\"attrCnName\\\":\\\"步骤耗时\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"10.0\\\"]}]}],\\\"cpvObjectId\\\":20425656},{\\\"attrs\\\":[{\\\"attrName\\\":\\\"servicemethod\\\",\\\"attrCnName\\\":\\\"服务方式\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"足部按摩\\\"]}]},{\\\"attrName\\\":\\\"stepTime\\\",\\\"attrCnName\\\":\\\"步骤耗时\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"50.0\\\"]}]}],\\\"cpvObjectId\\\":20425656}]\"}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"skuCateId\",\"attrCnName\":\"项目分类\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"2104542\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"TeaWater\",\"attrCnName\":\"茶水\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"[\\\"养生茶\\\"]\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"freeFood\",\"attrCnName\":\"免费餐食\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"茶点水果\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"Fruit\",\"attrCnName\":\"水果\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"[\\\"水果拼盘\\\"]\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"Snack\",\"attrCnName\":\"零食\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"[\\\"零食拼盘\\\"]\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"footbathBucket\",\"attrCnName\":\"泡脚桶\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"木桶\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"serviceMaterialAndTool\",\"attrCnName\":\"服务工具/材料\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"选择服务工具/材料\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"footbathMaterial\",\"attrCnName\":\"泡脚包\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"[\\\"草本包\\\"]\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"serviceDurationInt\",\"attrCnName\":\"服务时长\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"60\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"serviceBodyRange\",\"attrCnName\":\"具体服务部位\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"[\\\"足部\\\"]\"]],\"complexValues\":null}]]}]],\"cpvObjectId\":20306068,\"cpvObjectVersion\":null},\"marketPrice\":null,\"amount\":null}]],\"optionalCount\":0}]],\"optionalGroups\":[\"java.util.ArrayList\",[]]},\"extendImage\":null,\"combines\":null,\"saleChannelAggregation\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.SaleChannelAggregationDTO\",\"allSupport\":true,\"supportChannels\":[\"java.util.ArrayList\",[]],\"notSupportChannels\":[\"java.util.ArrayList\",[]]},\"purchaseNote\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.PurchaseNoteDTO\",\"title\":\"购买须知\",\"modules\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"适用时间\",\"icon\":\"https://p0.meituan.net/travelcube/eb358d1a211285207c636aa95594f3751524.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"有效时间\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"购买后90天内有效\"}]]}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"预约规则\",\"icon\":\"https://p0.meituan.net/travelcube/17303e587e8242902a1bf6ecd3eab10b1029.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"无需预约，如遇消费高峰时段您可能需要排队\"}]]}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"适用人数\",\"icon\":\"https://p0.meituan.net/travelcube/1f18aa2c0100e75060daf348283f3c861351.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"单次到店可核销多次\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"每张团购券最多1人使用\"}]]}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"其他规则\",\"icon\":\"https://p1.meituan.net/travelcube/4dac95c5f43a52f49b81ece1918925ea858.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"不再与其他优惠同享\"}]]}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"温馨提示\",\"icon\":\"https://p1.meituan.net/travelcube/09a2f606b1636f8b135b8582e3c0a53d1494.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"如需团购券发票，请您在消费时向商户咨询\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"购买后若全部未使用，在有效期内可申请全额退款；过期自动将未使用部分退款；若部分已使用，则不可退款。\"}]]}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"退款规则\",\"icon\":\"https://p0.meituan.net/travelcube/eb358d1a211285207c636aa95594f3751524.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"支持随时退、过期退\"}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"若次数未使用，可随时退全部实付金额\"}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"若发生核销后再申请退款，剩余所有未核销次数将一起退款，本商品为阶梯定价品，退款金额须按照如下规则进行计算\"}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":4,\"value\":\"{\\\"columns\\\":[{\\\"type\\\":1,\\\"value\\\":\\\"累计使用次数\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"单次价格\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"预计可退金额\\\"}],\\\"rows\\\":[{\\\"cells\\\":[{\\\"type\\\":1,\\\"value\\\":\\\"1\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"118.00\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"382.00\\\"}]},{\\\"cells\\\":[{\\\"type\\\":1,\\\"value\\\":\\\"2\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"95.50\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"286.50\\\"}]},{\\\"cells\\\":[{\\\"type\\\":1,\\\"value\\\":\\\"3\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"95.50\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"191.00\\\"}]},{\\\"cells\\\":[{\\\"type\\\":1,\\\"value\\\":\\\"4\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"95.50\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"95.50\\\"}]},{\\\"cells\\\":[{\\\"type\\\":1,\\\"value\\\":\\\"5\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"95.50\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"0.00\\\"}]}],\\\"desc\\\":\\\"此价格仅供参考，如涉及营销优惠，会把优惠金额按比例分摊到单次价格中扣除，若购买时使用了优惠券，仅在整单退款且优惠券未过期时，返还优惠券\\\"}\"}]]}]]}]]},\"bizProductId\":null,\"resourceInfos\":null,\"thirdPartyInfo\":null,\"mtDealGroupIdInt\":1035701478,\"dpDealGroupIdInt\":1035701478}";

    @Data
    class category2ExpIdDTO {
        private String platform;
        private int categoryId;
        private String expId;
    }

    /**
     * 测试预订团单结构化购买须知
     */
    @Test
    public void testFillPurchaseNoteStructureInfo_PreOrderDeal() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setCustomerId(123L);
        ctx.setRequestSource(RequestSourceEnum.PRE_ORDER_DEAL.getSource());
        ctx.setDealGroupDTO(dealGroupDTO);
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(409L);
        dealGroupDTO.setCategory(categoryDTO);
        lionMockedStatic.when(() -> Lion.getList(APP_KEY, PRE_ORDER_CATEGORY_IDS, String.class, Collections.emptyList())).thenReturn(Arrays.asList("409", "459"));
        PurchaseNoteDTO purchaseNoteDTO = new PurchaseNoteDTO();
        dealGroupDTO.setPurchaseNote(purchaseNoteDTO);
        purchaseNoteDTO.setTitle("购买须知");
        StandardDisplayModuleDTO moduleDTO = new StandardDisplayModuleDTO();
        purchaseNoteDTO.setModules(Collections.singletonList(moduleDTO));
        moduleDTO.setModuleName("预约模块");
        StandardDisplayItemDTO itemDTO = new StandardDisplayItemDTO();
        moduleDTO.setItems(Collections.singletonList(itemDTO));
        itemDTO.setItemName("预约项目");
        StandardDisplayValueDTO valueDTO = new StandardDisplayValueDTO();
        itemDTO.setItemValues(Collections.singletonList(valueDTO));
        valueDTO.setType(1);
        valueDTO.setValue("预约内容");

        // act
        dealDetailProcessor.fillPurchaseNoteStructureInfo(ctx);

        // assert
        PnPurchaseNoteDTO res = ctx.getPnPurchaseNoteDTO();
        assertTrue(Objects.nonNull(res));
        assertEquals("预订须知", res.getPnTitle());
        assertEquals("预订模块", res.getPnModules().get(0).getPnModuleName());
        assertEquals("预订项目", res.getPnModules().get(0).getPnItems().get(0).getPnItemName());
        assertEquals("预订内容", res.getPnModules().get(0).getPnItems().get(0).getPnItemValues().get(0).getPnValue());
    }

}
