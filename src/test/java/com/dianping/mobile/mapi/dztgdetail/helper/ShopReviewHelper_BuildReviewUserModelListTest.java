package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.*;
import com.dianping.cip.growth.mana.api.dto.response.UserManaDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.ReviewUserModel;
import com.dianping.userremote.base.dto.UserDTO;
import com.dianping.vipremote.vo.UserInfoForAppVO;
import java.util.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ShopReviewHelper_BuildReviewUserModelListTest {

    @Test
    public void testBuildReviewUserModelListEmptyUserIds() throws Throwable {
        List<Long> userIds = new ArrayList<>();
        Map<Long, UserDTO> userDTOMap = new HashMap<>();
        Map<Long, UserManaDTO> userGrowthDTOMap = new HashMap<>();
        Map<Long, UserInfoForAppVO> vipUserMap = new HashMap<>();
        Map<Long, ReviewUserModel> result = ShopReviewHelper.buildReviewUserModelList(userIds, userDTOMap, userGrowthDTOMap, vipUserMap);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testBuildReviewUserModelListNoUserInfo() throws Throwable {
        List<Long> userIds = Arrays.asList(1L, 2L, 3L);
        Map<Long, UserDTO> userDTOMap = new HashMap<>();
        Map<Long, UserManaDTO> userGrowthDTOMap = new HashMap<>();
        Map<Long, UserInfoForAppVO> vipUserMap = new HashMap<>();
        Map<Long, ReviewUserModel> result = ShopReviewHelper.buildReviewUserModelList(userIds, userDTOMap, userGrowthDTOMap, vipUserMap);
        assertEquals(3, result.size());
        for (Long userId : userIds) {
            ReviewUserModel model = result.get(userId);
            assertNotNull(model);
            assertEquals(userId.longValue(), model.getUserIdL());
            assertNull(model.getUserName());
            assertNull(model.getAvatar());
            assertNull(model.getVipIcon());
            assertNull(model.getUserLevel());
        }
    }

    @Test
    public void testBuildReviewUserModelListPartialUserInfo() throws Throwable {
        List<Long> userIds = Arrays.asList(1L, 2L, 3L);
        Map<Long, UserDTO> userDTOMap = new HashMap<>();
        userDTOMap.put(1L, new UserDTO());
        Map<Long, UserManaDTO> userGrowthDTOMap = new HashMap<>();
        userGrowthDTOMap.put(2L, new UserManaDTO());
        Map<Long, UserInfoForAppVO> vipUserMap = new HashMap<>();
        vipUserMap.put(3L, new UserInfoForAppVO());
        Map<Long, ReviewUserModel> result = ShopReviewHelper.buildReviewUserModelList(userIds, userDTOMap, userGrowthDTOMap, vipUserMap);
        assertEquals(3, result.size());
        for (Long userId : userIds) {
            ReviewUserModel model = result.get(userId);
            assertNotNull(model);
            assertEquals(userId.longValue(), model.getUserIdL());
            // Assertions for userName, avatar, vipIcon, and userLevel are omitted for brevity
        }
    }

    @Test
    public void testBuildReviewUserModelListAllUserInfo() throws Throwable {
        List<Long> userIds = Arrays.asList(1L, 2L, 3L);
        Map<Long, UserDTO> userDTOMap = new HashMap<>();
        UserDTO userDTO = new UserDTO();
        userDTO.setUserNickName("test");
        userDTO.setBigFace("face");
        userDTOMap.put(1L, userDTO);
        Map<Long, UserManaDTO> userGrowthDTOMap = new HashMap<>();
        UserManaDTO userManaDTO = new UserManaDTO();
        userManaDTO.setRoundurl("level");
        userGrowthDTOMap.put(2L, userManaDTO);
        Map<Long, UserInfoForAppVO> vipUserMap = new HashMap<>();
        UserInfoForAppVO userInfoForAppVO = new UserInfoForAppVO();
        userInfoForAppVO.setPic("vip");
        vipUserMap.put(3L, userInfoForAppVO);
        Map<Long, ReviewUserModel> result = ShopReviewHelper.buildReviewUserModelList(userIds, userDTOMap, userGrowthDTOMap, vipUserMap);
        assertEquals(3, result.size());
        for (Long userId : userIds) {
            ReviewUserModel model = result.get(userId);
            assertNotNull(model);
            assertEquals(userId.longValue(), model.getUserIdL());
            // Assertions for userName, avatar, vipIcon, and userLevel are omitted for brevity
        }
    }
}
