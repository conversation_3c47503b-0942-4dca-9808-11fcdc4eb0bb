package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.gmkt.activity.api.dto.DealActivityDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealActivityWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class DealActivityPreProcessorProcessTest {

    @InjectMocks
    private DealActivityPreProcessor dealActivityPreProcessor;

    @Mock
    private DealActivityWrapper dealActivityWrapper;

    @Mock
    private Future<List<DealActivityDTO>> dealPreActivitiesFuture;

    @Mock
    private FutureCtx futureCtx;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
    }

    /**
     * 正常场景：dealPreActivitiesFuture 不为空，且 queryDealActivity 返回非空的 DealActivityDTO 列表
     */
    @Test
    public void testProcessNormalCaseWithNonEmptyList() throws Throwable {
        // arrange
        List<DealActivityDTO> dealActivities = Collections.singletonList(new DealActivityDTO());
        when(futureCtx.getDealPreActivitiesFuture()).thenReturn(dealPreActivitiesFuture);
        when(dealActivityWrapper.queryDealActivity(dealPreActivitiesFuture, ctx)).thenReturn(dealActivities);
        // act
        dealActivityPreProcessor.process(ctx);
        // assert
        verify(ctx).setDealActivities(dealActivities);
    }

    /**
     * 正常场景：dealPreActivitiesFuture 不为空，但 queryDealActivity 返回空的 DealActivityDTO 列表
     */
    @Test
    public void testProcessNormalCaseWithEmptyList() throws Throwable {
        // arrange
        List<DealActivityDTO> dealActivities = Collections.emptyList();
        when(futureCtx.getDealPreActivitiesFuture()).thenReturn(dealPreActivitiesFuture);
        when(dealActivityWrapper.queryDealActivity(dealPreActivitiesFuture, ctx)).thenReturn(dealActivities);
        // act
        dealActivityPreProcessor.process(ctx);
        // assert
        verify(ctx).setDealActivities(dealActivities);
    }

    /**
     * 异常场景：queryDealActivity 方法内部抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testProcessExceptionCaseWithQueryException() throws Throwable {
        // arrange
        when(futureCtx.getDealPreActivitiesFuture()).thenReturn(dealPreActivitiesFuture);
        when(dealActivityWrapper.queryDealActivity(dealPreActivitiesFuture, ctx)).thenThrow(new RuntimeException());
        // act
        dealActivityPreProcessor.process(ctx);
        // assert
        // 期望抛出 RuntimeException
    }

    @Mock
    private DealCtx ctx;
}
