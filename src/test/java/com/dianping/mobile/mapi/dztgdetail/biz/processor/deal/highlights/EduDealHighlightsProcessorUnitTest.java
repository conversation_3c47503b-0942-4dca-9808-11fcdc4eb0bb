package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.DealCategoryEnum;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.EducationDealAttrUtils;
import com.dianping.mobile.mapi.dztgdetail.common.constants.EduConstant;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.highlights.DztgHighlightsModule;
import com.dianping.mobile.mapi.dztgdetail.util.EduDealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.technician.common.api.domain.TechnicianResp;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.technician.info.online.dto.OnlineTechWithAttrsDTO;
import com.sankuai.technician.info.online.dto.OnlineTechnicianDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Future;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;


/**
 * @auther: liweilong06
 * @date: 2024/1/17 3:01 下午
 */
@RunWith(MockitoJUnitRunner.class)
public class EduDealHighlightsProcessorUnitTest {

    public static final int SUCCESS_CODE = 200;
    @InjectMocks
    private EduDealHighlightsProcessor processor;

    private MockedStatic<EducationDealAttrUtils> educationDealService;

    private MockedStatic<LionConfigUtils> lionConfigUtilsMockedStatic;

    private MockedStatic<EduDealUtils> eduDealUtilsMockedStatic;

    private DealGroupDTO dealGroupDTO;
    private DealCtx ctx;
    private DealGroupCategoryDTO dealGroupCategoryDTO;
    private FutureCtx futureCtx;
    private DztgClientTypeEnum dztgClientTypeEnum;
    private EnvCtx envCtx;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        dztgClientTypeEnum = DztgClientTypeEnum.MEITUAN_APP;
        envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(dztgClientTypeEnum);
        ctx = new DealCtx(envCtx);
        dealGroupDTO = mock(DealGroupDTO.class);
        dealGroupCategoryDTO = mock(DealGroupCategoryDTO.class);
        futureCtx = mock(FutureCtx.class);
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.setFutureCtx(futureCtx);

        when(dealGroupDTO.getCategory()).thenReturn(dealGroupCategoryDTO);

        educationDealService = Mockito.mockStatic(EducationDealAttrUtils.class);
        lionConfigUtilsMockedStatic = Mockito.mockStatic(LionConfigUtils.class);
        eduDealUtilsMockedStatic = Mockito.mockStatic(EduDealUtils.class);
    }

    @After
    public void tearDown() {
        educationDealService.close();
        lionConfigUtilsMockedStatic.close();
        eduDealUtilsMockedStatic.close();
    }

    /**
     * 测试团单不是教育在线团单的情况
     * 预期：不处理
     */
    @Test
    public void testProcessNotEduOnlineDeal() {
        // arrange
        when(dealGroupCategoryDTO.getServiceTypeId()).thenReturn(1L);
        when(dealGroupCategoryDTO.getCategoryId()).thenReturn(DealCategoryEnum.EDU_1210.getDealCategoryId());

        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.isEduOnlineDeal(anyInt(), anyLong())).thenReturn(false);

        // act
        processor.process(ctx);

        // assert
        DztgHighlightsModule highlightsModule = ctx.getHighlightsModule();
        Assert.assertTrue(highlightsModule == null);
    }

    /**
     * 测试团单是教育在线团单，但获取主讲老师信息失败的情况
     * 预期：不处理
     */
    @Test
    public void testProcessEduOnlineDealGetTeacherFailed() {
        // arrange
        when(dealGroupCategoryDTO.getServiceTypeId()).thenReturn(1L);
        when(dealGroupCategoryDTO.getCategoryId()).thenReturn(DealCategoryEnum.EDU_1210.getDealCategoryId());

        educationDealService.when(() -> EducationDealAttrUtils.getSubject(dealGroupDTO)).thenReturn("医学");
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.isEduOnlineDeal(anyInt(), anyLong())).thenReturn(true);

        Future future = mock(Future.class);
        when(ctx.getFutureCtx().getEduOnlineTeacherInfoFuture()).thenReturn(future);

        // act
        processor.process(ctx);

        // assert
        DztgHighlightsModule highlightsModule = ctx.getHighlightsModule();
        Assert.assertTrue(highlightsModule == null);
    }

    /**
     * 测试团单是教育在线团单，获取主讲老师信息成功，但老师数量大于1的情况
     * 预期：只展示第一个老师,并且追加等
     */
    @Test
    public void testProcessEduOnlineDealGetTeacherSuccessMoreThanOne() throws Exception {
        // arrange
        when(dealGroupCategoryDTO.getServiceTypeId()).thenReturn(1L);
        when(dealGroupCategoryDTO.getCategoryId()).thenReturn(DealCategoryEnum.EDU_1210.getDealCategoryId());

        educationDealService.when(() -> EducationDealAttrUtils.getSubject(dealGroupDTO)).thenReturn("医学");
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.isEduOnlineDeal(anyInt(), anyLong())).thenReturn(true);

        Future future = mock(Future.class);
        when(ctx.getFutureCtx().getEduOnlineTeacherInfoFuture()).thenReturn(future);


        List<OnlineTechWithAttrsDTO> onlineTechList = new ArrayList<>();
        OnlineTechWithAttrsDTO onlineTech1 = mock(OnlineTechWithAttrsDTO.class);
        OnlineTechnicianDTO technicianDTO1 = new OnlineTechnicianDTO();
        technicianDTO1.setName("测试");
        when(onlineTech1.getTechnician()).thenReturn(technicianDTO1);
        OnlineTechWithAttrsDTO onlineTech2 = mock(OnlineTechWithAttrsDTO.class);
        onlineTechList.add(onlineTech1);
        onlineTechList.add(onlineTech2);
        TechnicianResp<List<OnlineTechWithAttrsDTO>> onlineTechResponse = new TechnicianResp<>();
        onlineTechResponse.setData(onlineTechList);
        onlineTechResponse.setCode(SUCCESS_CODE);
        when(future.get(anyLong(), any())).thenReturn(onlineTechResponse);

        // act
        processor.process(ctx);

        // assert
        DztgHighlightsModule highlightsModule = ctx.getHighlightsModule();
        Assert.assertTrue(highlightsModule != null && CollectionUtils.isNotEmpty(highlightsModule.getAttrs()));
        assertKeyAndValue(highlightsModule, 0, "主讲老师", "测试等");
    }

    /**
     * 测试团单是教育在线团单，获取主讲老师信息成功，老师数量等于1的情况
     * 预期：展示一个老师
     */
    @Test
    public void testProcessEduOnlineDealGetTeacherSuccessOne() throws Exception {
        // arrange
        when(dealGroupCategoryDTO.getServiceTypeId()).thenReturn(1L);
        when(dealGroupCategoryDTO.getCategoryId()).thenReturn(DealCategoryEnum.EDU_1210.getDealCategoryId());

        educationDealService.when(() -> EducationDealAttrUtils.getSubject(dealGroupDTO)).thenReturn("医学");
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.isEduOnlineDeal(anyInt(), anyLong())).thenReturn(true);

        Future future = mock(Future.class);
        when(ctx.getFutureCtx().getEduOnlineTeacherInfoFuture()).thenReturn(future);

        OnlineTechnicianDTO technicianDTO = new OnlineTechnicianDTO();
        technicianDTO.setName("测试");
        List<OnlineTechWithAttrsDTO> onlineTechList = new ArrayList<>();
        OnlineTechWithAttrsDTO onlineTech1 = mock(OnlineTechWithAttrsDTO.class);
        when(onlineTech1.getTechnician()).thenReturn(technicianDTO);
        onlineTechList.add(onlineTech1);
        TechnicianResp<List<OnlineTechWithAttrsDTO>> onlineTechResponse = new TechnicianResp<>();
        onlineTechResponse.setData(onlineTechList);
        onlineTechResponse.setCode(SUCCESS_CODE);
        when(future.get(anyLong(), any())).thenReturn(onlineTechResponse);

        // act
        processor.process(ctx);

        // assert
        DztgHighlightsModule highlightsModule = ctx.getHighlightsModule();
        Assert.assertTrue(highlightsModule != null && CollectionUtils.isNotEmpty(highlightsModule.getAttrs()));
        assertKeyAndValue(highlightsModule, 0, "主讲老师", "测试");
    }

    private void assertKeyAndValue(DztgHighlightsModule highlightsModule, int index, String key, String value) {
        Assert.assertTrue(key.equals(highlightsModule.getAttrs().get(index).getName()));
        Assert.assertTrue(value.equals(highlightsModule.getAttrs().get(index).getValue()));
    }

    /**
     * 测试团单是教育的考研在线团单，获取课时数、班型、附加服务、课程保障、实物赠品、适用阶段等信息成功的情况
     * 预期：展示课时数、班型、附加服务、课程保障、适用阶段信息
     */
    @Test
    public void testProcessEduOnlineDealGetOtherInfoSuccess() {
        // arrange
        when(dealGroupCategoryDTO.getServiceTypeId()).thenReturn(1L);
        when(dealGroupCategoryDTO.getCategoryId()).thenReturn(DealCategoryEnum.EDU_1210.getDealCategoryId());

        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.isEduOnlineDeal(anyInt(), anyLong())).thenReturn(true);

        educationDealService.when(() -> EducationDealAttrUtils.getSubject(dealGroupDTO)).thenReturn("医学");
        educationDealService.when(() -> EducationDealAttrUtils.getClassNum(dealGroupDTO)).thenReturn("10");
        educationDealService.when(() -> EducationDealAttrUtils.getOrigClassType(dealGroupDTO)).thenReturn("1对1");
        educationDealService.when(() -> EducationDealAttrUtils.getAppendServices(dealGroupDTO)).thenReturn(Arrays.asList("附加服务1"));
        educationDealService.when(() -> EducationDealAttrUtils.getClassSafeguard(dealGroupDTO)).thenReturn(Arrays.asList("课程保障1"));
        educationDealService.when(() -> EducationDealAttrUtils.getGiftProduct(dealGroupDTO)).thenReturn(Arrays.asList("实物赠品1"));
        educationDealService.when(() -> EducationDealAttrUtils.getSuitableClass(dealGroupDTO)).thenReturn("适用阶段1");

        // act
        processor.process(ctx);

        // assert
        DztgHighlightsModule highlightsModule = ctx.getHighlightsModule();
        Assert.assertTrue(highlightsModule != null && CollectionUtils.isNotEmpty(highlightsModule.getAttrs()));
        assertKeyAndValue(highlightsModule, 0, "课时", "10");
        assertKeyAndValue(highlightsModule, 1, "班型", "1对1");
        assertKeyAndValue(highlightsModule, 2, "附加服务", "附加服务1");
        assertKeyAndValue(highlightsModule, 3, "课程保障", "课程保障1");
        assertKeyAndValue(highlightsModule, 4, "实物赠品", "实物赠品1");
    }

    /**
     * 测试团单是教育的考研在线团单，获取课时数、班型、附加服务、课程保障、实物赠品、适用阶段等信息成功的情况
     * 预期:展示课时数、班型、附加服务、课程保障、实物赠品、适用阶段信息
     */
    @Test
    public void testProcessEduOnlineDealGetOtherInfoSuccessForMoreServices() {
        // arrange
        when(dealGroupCategoryDTO.getServiceTypeId()).thenReturn(1L);
        when(dealGroupCategoryDTO.getCategoryId()).thenReturn(DealCategoryEnum.EDU_1210.getDealCategoryId());

        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.isEduOnlineDeal(anyInt(), anyLong())).thenReturn(true);

        educationDealService.when(() -> EducationDealAttrUtils.getSubject(dealGroupDTO)).thenReturn("医学");
        educationDealService.when(() -> EducationDealAttrUtils.getClassNum(dealGroupDTO)).thenReturn(null);
        educationDealService.when(() -> EducationDealAttrUtils.getOrigClassType(dealGroupDTO)).thenReturn(null);
        educationDealService.when(() -> EducationDealAttrUtils.getAppendServices(dealGroupDTO)).thenReturn(Arrays.asList("附加服务1"));
        educationDealService.when(() -> EducationDealAttrUtils.getClassSafeguard(dealGroupDTO)).thenReturn(Arrays.asList("课程保障1"));
        educationDealService.when(() -> EducationDealAttrUtils.getGiftProduct(dealGroupDTO)).thenReturn(Arrays.asList("实物赠品1"));
        educationDealService.when(() -> EducationDealAttrUtils.getSuitableClass(dealGroupDTO)).thenReturn("适用阶段1");

        // act
        processor.process(ctx);

        // assert
        DztgHighlightsModule highlightsModule = ctx.getHighlightsModule();
        Assert.assertTrue(highlightsModule != null && CollectionUtils.isNotEmpty(highlightsModule.getAttrs()));
        assertKeyAndValue(highlightsModule, 0, "附加服务", "附加服务1");
        assertKeyAndValue(highlightsModule, 1, "课程保障", "课程保障1");
        assertKeyAndValue(highlightsModule, 2, "实物赠品", "实物赠品1");
        assertKeyAndValue(highlightsModule, 3, "适用阶段", "适用阶段1");
    }

    /**
     * 测试团单是教育考研在线团单，获取所有信息失败的情况
     * 预期：不展示信息,不报错
     */
    @Test
    public void testProcessEduOnlineDealGetOtherInfoFailed() {
        // arrange
        when(dealGroupCategoryDTO.getServiceTypeId()).thenReturn(1L);
        when(dealGroupCategoryDTO.getCategoryId()).thenReturn(DealCategoryEnum.EDU_1210.getDealCategoryId());

        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.isEduOnlineDeal(anyInt(), anyLong())).thenReturn(true);

        educationDealService.when(() -> EducationDealAttrUtils.getSubject(dealGroupDTO)).thenReturn("医学");
        educationDealService.when(() -> EducationDealAttrUtils.getClassNum(dealGroupDTO)).thenReturn(null);
        educationDealService.when(() -> EducationDealAttrUtils.getOrigClassType(dealGroupDTO)).thenReturn(null);
        educationDealService.when(() -> EducationDealAttrUtils.getAppendServices(dealGroupDTO)).thenReturn(null);
        educationDealService.when(() -> EducationDealAttrUtils.getClassSafeguard(dealGroupDTO)).thenReturn(null);
        educationDealService.when(() -> EducationDealAttrUtils.getGiftProduct(dealGroupDTO)).thenReturn(null);
        educationDealService.when(() -> EducationDealAttrUtils.getSuitableClass(dealGroupDTO)).thenReturn(null);
        // act
        processor.process(ctx);
        // assert
        DztgHighlightsModule highlightsModule = ctx.getHighlightsModule();
        Assert.assertTrue(highlightsModule == null);
    }

    /**
     * 测试团单是教育考研旧版团单
     * 预期：不展示信息,不报错
     */
    @Test
    public void testProcessEduOnlineDealAsOldDeal() {
        // arrange
        when(dealGroupCategoryDTO.getServiceTypeId()).thenReturn(1L);
        when(dealGroupCategoryDTO.getCategoryId()).thenReturn(DealCategoryEnum.EDU_1210.getDealCategoryId());

        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.isEduOnlineDeal(anyInt(), anyLong())).thenReturn(true);

        educationDealService.when(() -> EducationDealAttrUtils.getSubject(dealGroupDTO)).thenReturn(null);
        // act
        processor.process(ctx);
        // assert
        DztgHighlightsModule highlightsModule = ctx.getHighlightsModule();
        Assert.assertTrue(highlightsModule == null);
    }

    /**
     * 测试团单是教育的考公在线团单，获取课时数、班型、附加服务、课程保障、实物赠品、适用阶段等信息成功的情况
     * 预期：展示课时数、班型、附加服务、课程保障、适用阶段信息
     */
    @Test
    public void testProcessEduKaoGongOnlineDealGetOtherInfoSuccess() {
        // arrange
        eduDealUtilsMockedStatic.when(() -> EduDealUtils.isCivilExam(dealGroupDTO)).thenReturn(true);
        when(dealGroupCategoryDTO.getServiceTypeId()).thenReturn(EduConstant.EXAM_CIVIL_SERVICE_TYPE_ID_LIST.get(0));
        when(dealGroupCategoryDTO.getCategoryId()).thenReturn(DealCategoryEnum.EDU_1205.getDealCategoryId());

        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.isEduOnlineDeal(anyInt(), anyLong())).thenReturn(true);

        educationDealService.when(() -> EducationDealAttrUtils.getClassNum(dealGroupDTO)).thenReturn("10");
        educationDealService.when(() -> EducationDealAttrUtils.getOrigClassType(dealGroupDTO)).thenReturn("1对1");
        educationDealService.when(() -> EducationDealAttrUtils.getClassStage(dealGroupDTO)).thenReturn("课程阶段1");
        educationDealService.when(() -> EducationDealAttrUtils.getAppendServices(dealGroupDTO)).thenReturn(Arrays.asList("附加服务1"));
        educationDealService.when(() -> EducationDealAttrUtils.getClassSafeguard(dealGroupDTO)).thenReturn(Arrays.asList("课程保障1"));

        educationDealService.when(() -> EducationDealAttrUtils.getSuitableProvince(dealGroupDTO)).thenReturn(Arrays.asList("适用省份1"));
        educationDealService.when(() -> EducationDealAttrUtils.getSuitableClass(dealGroupDTO)).thenReturn("适用阶段1");
        educationDealService.when(() -> EducationDealAttrUtils.getSuitablePeople(dealGroupDTO)).thenReturn("适用人群1");
        educationDealService.when(() -> EducationDealAttrUtils.getGiftProduct(dealGroupDTO)).thenReturn(Arrays.asList("实物赠品1"));

        // act
        processor.process(ctx);

        // assert
        DztgHighlightsModule highlightsModule = ctx.getHighlightsModule();
        Assert.assertTrue(highlightsModule != null && CollectionUtils.isNotEmpty(highlightsModule.getAttrs()));
        assertKeyAndValue(highlightsModule, 0, "课时", "10");
        assertKeyAndValue(highlightsModule, 1, "班型", "1对1");
        assertKeyAndValue(highlightsModule, 2, "课程阶段", "课程阶段1");
        assertKeyAndValue(highlightsModule, 3, "附加服务", "附加服务1");
        assertKeyAndValue(highlightsModule, 4, "课程保障", "课程保障1");
    }

    /**
     * 测试团单是教育的考公在线团单，获取课时数、班型、附加服务、课程保障、实物赠品、适用阶段等信息成功的情况
     * 预期:展示课时数、班型、附加服务、课程保障、实物赠品、适用阶段信息
     */
    @Test
    public void testProcessEduKaoGongOnlineDealGetOtherInfoSuccessForMoreServices() {
        // arrange
        eduDealUtilsMockedStatic.when(() -> EduDealUtils.isCivilExam(dealGroupDTO)).thenReturn(true);
        when(dealGroupCategoryDTO.getServiceTypeId()).thenReturn(EduConstant.EXAM_CIVIL_SERVICE_TYPE_ID_LIST.get(0));
        when(dealGroupCategoryDTO.getCategoryId()).thenReturn(DealCategoryEnum.EDU_1205.getDealCategoryId());

        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.isEduOnlineDeal(anyInt(), anyLong())).thenReturn(true);

        educationDealService.when(() -> EducationDealAttrUtils.getClassNum(dealGroupDTO)).thenReturn(null);
        educationDealService.when(() -> EducationDealAttrUtils.getOrigClassType(dealGroupDTO)).thenReturn(null);
        educationDealService.when(() -> EducationDealAttrUtils.getClassStage(dealGroupDTO)).thenReturn(null);
        educationDealService.when(() -> EducationDealAttrUtils.getAppendServices(dealGroupDTO)).thenReturn(null);
        educationDealService.when(() -> EducationDealAttrUtils.getClassSafeguard(dealGroupDTO)).thenReturn(null);

        educationDealService.when(() -> EducationDealAttrUtils.getSuitableProvince(dealGroupDTO)).thenReturn(Arrays.asList("适用省份1"));
        educationDealService.when(() -> EducationDealAttrUtils.getSuitableClass(dealGroupDTO)).thenReturn("适用阶段1");
        educationDealService.when(() -> EducationDealAttrUtils.getSuitablePeople(dealGroupDTO)).thenReturn("适用人群1");
        educationDealService.when(() -> EducationDealAttrUtils.getGiftProduct(dealGroupDTO)).thenReturn(Arrays.asList("实物赠品1"));

        // act
        processor.process(ctx);

        // assert
        DztgHighlightsModule highlightsModule = ctx.getHighlightsModule();
        Assert.assertTrue(highlightsModule != null && CollectionUtils.isNotEmpty(highlightsModule.getAttrs()));
        assertKeyAndValue(highlightsModule, 0, "适用省份", "适用省份1");
        assertKeyAndValue(highlightsModule, 1, "适用阶段", "适用阶段1");
        assertKeyAndValue(highlightsModule, 2, "适用人群", "适用人群1");
        assertKeyAndValue(highlightsModule, 3, "实物赠品", "实物赠品1");
    }

    /**
     * 测试团单是教育考公在线团单，获取所有信息失败的情况
     * 预期：不展示信息,不报错
     */
    @Test
    public void testProcessEduKaoGongOnlineDealGetOtherInfoFailed() {
        // arrange
        when(dealGroupCategoryDTO.getServiceTypeId()).thenReturn(1L);
        when(dealGroupCategoryDTO.getCategoryId()).thenReturn(DealCategoryEnum.EDU_1205.getDealCategoryId());

        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.isEduOnlineDeal(anyInt(), anyLong())).thenReturn(true);

        educationDealService.when(() -> EducationDealAttrUtils.getClassNum(dealGroupDTO)).thenReturn(null);
        educationDealService.when(() -> EducationDealAttrUtils.getOrigClassType(dealGroupDTO)).thenReturn(null);
        educationDealService.when(() -> EducationDealAttrUtils.getClassStage(dealGroupDTO)).thenReturn(null);
        educationDealService.when(() -> EducationDealAttrUtils.getAppendServices(dealGroupDTO)).thenReturn(null);
        educationDealService.when(() -> EducationDealAttrUtils.getClassSafeguard(dealGroupDTO)).thenReturn(null);
        educationDealService.when(() -> EducationDealAttrUtils.getSuitableProvince(dealGroupDTO)).thenReturn(null);
        educationDealService.when(() -> EducationDealAttrUtils.getSuitableClass(dealGroupDTO)).thenReturn(null);
        educationDealService.when(() -> EducationDealAttrUtils.getSuitablePeople(dealGroupDTO)).thenReturn(null);
        educationDealService.when(() -> EducationDealAttrUtils.getGiftProduct(dealGroupDTO)).thenReturn(null);
        // act
        processor.process(ctx);
        // assert
        DztgHighlightsModule highlightsModule = ctx.getHighlightsModule();
        Assert.assertTrue(highlightsModule == null);
    }

    @Test
    public void testGetExamMaterialWithZeroMaterials() {
        // arrange
        when(EducationDealAttrUtils.getExamMaterialNum(dealGroupDTO)).thenReturn(0);

        // act
        String result = processor.getExamMaterial(dealGroupDTO);

        // assert
        assertEquals(null, result);
    }

    /**
     * 测试考试材料数量大于0的情况
     */
    @Test
    public void testGetExamMaterialWithPositiveMaterials() {
        // arrange
        int materialNum = 5;
        when(EducationDealAttrUtils.getExamMaterialNum(dealGroupDTO)).thenReturn(materialNum);

        // act
        String result = processor.getExamMaterial(dealGroupDTO);

        // assert
        assertEquals(String.valueOf(materialNum), result);
    }

    /**
     * 测试考试材料数量为负数的情况
     */
    @Test
    public void testGetExamMaterialWithNegativeMaterials() {
        // arrange
        int materialNum = -3;
        when(EducationDealAttrUtils.getExamMaterialNum(dealGroupDTO)).thenReturn(materialNum);

        // act
        String result = processor.getExamMaterial(dealGroupDTO);

        // assert
        assertEquals(String.valueOf(materialNum), result);
    }


    @Test
    public void testBuildForShortClassNormal() {
        // arrange
        educationDealService.when(() -> EducationDealAttrUtils.getEduSuitableAge(dealGroupDTO)).thenReturn("30岁-40岁");
        educationDealService.when(() -> EducationDealAttrUtils.getEduSuitableLevel(dealGroupDTO)).thenReturn("零基础入门");

        eduDealUtilsMockedStatic.when(() -> EduDealUtils.isShortClass(ctx)).thenReturn(true);
        eduDealUtilsMockedStatic.when(() -> EduDealUtils.getShortClassFreeTrailNum(ctx)).thenReturn(5);
        eduDealUtilsMockedStatic.when(() -> EduDealUtils.getClassDuration(ctx)).thenReturn("40");

        // act
        processor.process(ctx);

        // assert
        DztgHighlightsModule highlightsModule = ctx.getHighlightsModule();
        Assert.assertTrue(highlightsModule != null && CollectionUtils.isNotEmpty(highlightsModule.getAttrs()));
        assertKeyAndValue(highlightsModule, 0, "适用人群", "30-40岁 零基础入门");
        assertKeyAndValue(highlightsModule, 1, "课程安排", "5节课 40分钟/节");
    }

    @Test
    public void testBuildForShortClassWithOutClassDurationAndSuitableLevel() {
        // arrange
        educationDealService.when(() -> EducationDealAttrUtils.getEduSuitableAge(dealGroupDTO)).thenReturn("30岁-40岁");

        eduDealUtilsMockedStatic.when(() -> EduDealUtils.isShortClass(ctx)).thenReturn(true);
        eduDealUtilsMockedStatic.when(() -> EduDealUtils.getShortClassFreeTrailNum(ctx)).thenReturn(5);

        // act
        processor.process(ctx);

        // assert
        DztgHighlightsModule highlightsModule = ctx.getHighlightsModule();
        Assert.assertTrue(highlightsModule != null && CollectionUtils.isNotEmpty(highlightsModule.getAttrs()));
        assertKeyAndValue(highlightsModule, 0, "适用人群", "30-40岁");
        assertKeyAndValue(highlightsModule, 1, "课程安排", "5节课");
    }

    @Test
    public void testBuildForShortClassWithOutNumAndSuitableAge() {
        // arrange
        educationDealService.when(() -> EducationDealAttrUtils.getEduSuitableLevel(dealGroupDTO)).thenReturn("零基础入门");

        eduDealUtilsMockedStatic.when(() -> EduDealUtils.isShortClass(ctx)).thenReturn(true);
        eduDealUtilsMockedStatic.when(() -> EduDealUtils.getShortClassFreeTrailNum(ctx)).thenReturn(0);
        eduDealUtilsMockedStatic.when(() -> EduDealUtils.getClassDuration(ctx)).thenReturn("40");

        // act
        processor.process(ctx);

        // assert
        DztgHighlightsModule highlightsModule = ctx.getHighlightsModule();
        Assert.assertTrue(highlightsModule != null && CollectionUtils.isNotEmpty(highlightsModule.getAttrs()));
        assertKeyAndValue(highlightsModule, 0, "适用人群", "零基础入门");
        assert (highlightsModule.getAttrs().size() == 1);
    }

    @Test
    public void testBuild_nightSchool_returnAll() {
        List<AttrDTO> dealAttrs = Mockito.mock(List.class);
        when(dealGroupDTO.getAttrs()).thenReturn(dealAttrs);
        when(dealGroupCategoryDTO.getCategoryId()).thenReturn(DealCategoryEnum.EDU_1226.getDealCategoryId());
        educationDealService.when(() -> EducationDealAttrUtils.getFormatAttrByName(dealAttrs, EduDealHighlightsProcessor.ATTR_ALDULT_AGE)).thenReturn("适用年龄1");
        educationDealService.when(() -> EducationDealAttrUtils.getFormatAttrByName(dealAttrs, EduDealHighlightsProcessor.ATTR_APPLICATION_STAGE_SELECT)).thenReturn("适用阶段1");
        educationDealService.when(() -> EducationDealAttrUtils.getFormatAttrByName(dealAttrs, EduDealHighlightsProcessor.ATTR_NIGHT_SCHOOL_CLASS_TYPE)).thenReturn("班级人数1");
        eduDealUtilsMockedStatic.when(() -> EduDealUtils.getClassNumFromSkus(ctx, false)).thenReturn(20);

        // act
        processor.process(ctx);

        // assert
        DztgHighlightsModule highlightsModule = ctx.getHighlightsModule();
        Assert.assertTrue(highlightsModule != null && CollectionUtils.isNotEmpty(highlightsModule.getAttrs()));
        assertKeyAndValue(highlightsModule, 0, "适用年龄", "适用年龄1");
        assertKeyAndValue(highlightsModule, 1, "适用阶段", "适用阶段1");
        assertKeyAndValue(highlightsModule, 2, "课次数", "20节");
        assertKeyAndValue(highlightsModule, 3, "班级人数", "班级人数1");
    }

    /**
     * 测试团单是职业培训的非考公在线团单，获取课时数、班型、附加服务、课程保障、实物赠品、适用阶段等信息成功的情况
     */
    @Test
    public void testProcess_VocationTrainingNotKaoGongOnlineDealGetOtherInfo_Success() {
        // arrange
        when(dealGroupCategoryDTO.getServiceTypeId()).thenReturn(1L);
        when(dealGroupCategoryDTO.getCategoryId()).thenReturn(DealCategoryEnum.EDU_1205.getDealCategoryId());

        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.isEduOnlineDeal(anyInt(), anyLong())).thenReturn(true);

        educationDealService.when(() -> EducationDealAttrUtils.getClassNum(dealGroupDTO)).thenReturn("10");
        educationDealService.when(() -> EducationDealAttrUtils.getTrainTime(dealGroupDTO)).thenReturn("11");
        educationDealService.when(() -> EducationDealAttrUtils.getOrigClassType(dealGroupDTO)).thenReturn("1对1");
        educationDealService.when(() -> EducationDealAttrUtils.getAppendServices(dealGroupDTO)).thenReturn(Arrays.asList("附加服务1"));
        educationDealService.when(() -> EducationDealAttrUtils.getClassSafeguard(dealGroupDTO)).thenReturn(Arrays.asList("课程保障1"));

        educationDealService.when(() -> EducationDealAttrUtils.getGiftProduct(dealGroupDTO)).thenReturn(Arrays.asList("实物赠品1"));
        educationDealService.when(() -> EducationDealAttrUtils.getSuitableClass(dealGroupDTO)).thenReturn("适用阶段1");
        educationDealService.when(() -> EducationDealAttrUtils.getSuitablePeople(dealGroupDTO)).thenReturn("适用人群1");

        // act
        processor.process(ctx);

        // assert
        DztgHighlightsModule highlightsModule = ctx.getHighlightsModule();
        Assert.assertTrue(highlightsModule != null && CollectionUtils.isNotEmpty(highlightsModule.getAttrs()));
        assertKeyAndValue(highlightsModule, 0, "集训时长", "11");
        assertKeyAndValue(highlightsModule, 1, "班型", "1对1");
        assertKeyAndValue(highlightsModule, 2, "附加服务", "附加服务1");
        assertKeyAndValue(highlightsModule, 3, "课程保障", "课程保障1");
        assertKeyAndValue(highlightsModule, 4, "实物赠品", "实物赠品1");
    }

}
