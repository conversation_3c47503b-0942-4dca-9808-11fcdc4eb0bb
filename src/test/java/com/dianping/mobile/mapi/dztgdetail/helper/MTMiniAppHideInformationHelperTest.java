package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MTMiniAppHideInformationHelperTest {

    @Mock
    private EnvCtx envCtx;

    /**
     * 测试用户在美团小程序登录，不需要隐藏信息
     */
    @Test
    public void testNeedHideInformation_UserLoginInMTMiniApp() {
        when(envCtx.isMtMiniApp()).thenReturn(true);
        when(envCtx.isLogin()).thenReturn(true);
        assertFalse(MTMiniAppHideInformationHelper.needHideInformation(envCtx));
    }

    /**
     * 测试用户在美团小程序未登录，需要隐藏信息
     */
    @Test
    public void testNeedHideInformation_UserNotLoginInMTMiniApp() {
        when(envCtx.isMtMiniApp()).thenReturn(true);
        when(envCtx.isLogin()).thenReturn(false);
        assertTrue(MTMiniAppHideInformationHelper.needHideInformation(envCtx));
    }

    /**
     * 测试当前环境不是美团小程序，不需要隐藏信息
     */
    @Test
    public void testNeedHideInformation_NotMTMiniApp() {
        when(envCtx.isMtMiniApp()).thenReturn(false);
        assertFalse(MTMiniAppHideInformationHelper.needHideInformation(envCtx));
    }
}
