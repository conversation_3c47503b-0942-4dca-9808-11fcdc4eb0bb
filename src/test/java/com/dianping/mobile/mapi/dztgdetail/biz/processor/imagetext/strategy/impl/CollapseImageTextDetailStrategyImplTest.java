package com.dianping.mobile.mapi.dztgdetail.biz.processor.imagetext.strategy.impl;

import com.dianping.mobile.mapi.dztgdetail.common.enums.ImageTextStrategyEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ContentDetailPBO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

/**
 * CollapseImageTextDetailStrategyImpl 类的单元测试
 */
@RunWith(MockitoJUnitRunner.class)
public class CollapseImageTextDetailStrategyImplTest {

    @InjectMocks
    private CollapseImageTextDetailStrategyImpl collapseImageTextDetailStrategyImpl;

    @Before
    public void setUp() {
    }

    /**
     * 测试 getStrategyName 方法，期望返回 COLLAPSE
     */
    @Test
    public void testGetStrategyNameExpectCollapse() {
        // arrange (此处无需初始化数据，因为测试的方法不依赖外部输入)

        // act
        ImageTextStrategyEnum result = collapseImageTextDetailStrategyImpl.getStrategyName();

        // assert
        assertEquals("测试 getStrategyName 方法，期望返回 COLLAPSE", ImageTextStrategyEnum.COLLAPSE, result);
    }

    /**
     * 测试内容详情为空时的情况
     */
    @Test
    public void testGetContentDetailWithEmptyContents() {
        DealGroupDTO dealGroupDTO = Mockito.mock(DealGroupDTO.class);
        List<ContentPBO> contents = new ArrayList<>();
        int foldThreshold = 3;

        ContentDetailPBO result = collapseImageTextDetailStrategyImpl.getContentDetail(dealGroupDTO, contents, foldThreshold);

        assertNotNull(result);
        assertTrue(result.getContents().isEmpty());
        assertTrue(result.isFold());
        assertEquals("图文详情", result.getTitle());
        assertEquals(foldThreshold, result.getFoldThreshold());
    }

    /**
     * 测试内容详情不为空，但小于折叠阈值的情况
     */
    @Test
    public void testGetContentDetailWithContentsLessThanThreshold() {
        DealGroupDTO dealGroupDTO = Mockito.mock(DealGroupDTO.class);
        List<ContentPBO> contents = new ArrayList<>();
        contents.add(new ContentPBO(0, "test"));
        int foldThreshold = 3;

        ContentDetailPBO result = collapseImageTextDetailStrategyImpl.getContentDetail(dealGroupDTO, contents, foldThreshold);

        assertNotNull(result);
        assertFalse(result.getContents().isEmpty());
        assertTrue(result.isFold()); // 注意：这里的预期结果应根据实际逻辑调整
        assertEquals("图文详情", result.getTitle());
        assertEquals(foldThreshold, result.getFoldThreshold());
    }

    /**
     * 测试内容详情不为空，且等于折叠阈值的情况
     */
    @Test
    public void testGetContentDetailWithContentsEqualToThreshold() {
        DealGroupDTO dealGroupDTO = Mockito.mock(DealGroupDTO.class);
        List<ContentPBO> contents = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            contents.add(new ContentPBO(0, "test" + i));
        }
        int foldThreshold = 3;

        ContentDetailPBO result = collapseImageTextDetailStrategyImpl.getContentDetail(dealGroupDTO, contents, foldThreshold);

        assertNotNull(result);
        assertFalse(result.getContents().isEmpty());
        assertTrue(result.isFold()); // 注意：这里的预期结果应根据实际逻辑调整
        assertEquals("图文详情", result.getTitle());
        assertEquals(foldThreshold, result.getFoldThreshold());
    }

    /**
     * 测试内容详情不为空，且大于折叠阈值的情况
     */
    @Test
    public void testGetContentDetailWithContentsGreaterThanThreshold() {
        DealGroupDTO dealGroupDTO = Mockito.mock(DealGroupDTO.class);
        List<ContentPBO> contents = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            contents.add(new ContentPBO(0, "test" + i));
        }
        int foldThreshold = 3;

        ContentDetailPBO result = collapseImageTextDetailStrategyImpl.getContentDetail(dealGroupDTO, contents, foldThreshold);

        assertNotNull(result);
        assertFalse(result.getContents().isEmpty());
        assertTrue(result.isFold());
        assertEquals("图文详情", result.getTitle());
        assertEquals(foldThreshold, result.getFoldThreshold());
    }
}
