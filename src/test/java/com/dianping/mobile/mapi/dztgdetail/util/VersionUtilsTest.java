package com.dianping.mobile.mapi.dztgdetail.util;

import org.junit.Assert;
import org.junit.Test;

public class VersionUtilsTest {

    /**
     * 测试两个版本号都为空时的情况
     */
    @Test
    public void testIsLessAndEqualThanBothEmpty() {
        // arrange
        String versionA = "";
        String versionB = "";

        // act
        boolean result = VersionUtils.isLessAndEqualThan(versionA, versionB);

        // assert
        Assert.assertFalse("当两个版本号都为空时，应返回false", result);
    }

    /**
     * 测试第一个版本号为空，第二个版本号非空时的情况
     */
    @Test
    public void testIsLessAndEqualThanFirstEmpty() {
        // arrange
        String versionA = "";
        String versionB = "0.5.8";

        // act
        boolean result = VersionUtils.isLessAndEqualThan(versionA, versionB);

        // assert
        Assert.assertFalse("当第一个版本号为空，第二个版本号非空时，应返回false", result);
    }

    /**
     * 测试第一个版本号非空，第二个版本号为空时的情况
     */
    @Test
    public void testIsLessAndEqualThanSecondEmpty() {
        // arrange
        String versionA = "0.5.8";
        String versionB = "";

        // act
        boolean result = VersionUtils.isLessAndEqualThan(versionA, versionB);

        // assert
        Assert.assertFalse("当第一个版本号非空，第二个版本号为空时，应返回false", result);
    }

    /**
     * 测试两个版本号相等时的情况
     */
    @Test
    public void testIsLessAndEqualThanEqualVersions() {
        // arrange
        String versionA = "0.5.8";
        String versionB = "0.5.8";

        // act
        boolean result = VersionUtils.isLessAndEqualThan(versionA, versionB);

        // assert
        Assert.assertTrue("当两个版本号相等时，应返回true", result);
    }

    /**
     * 测试第一个版本号小于第二个版本号时的情况
     */
    @Test
    public void testIsLessAndEqualThanLessVersion() {
        // arrange
        String versionA = "0.5.8";
        String versionB = "0.5.9";

        // act
        boolean result = VersionUtils.isLessAndEqualThan(versionA, versionB);

        // assert
        Assert.assertTrue("当第一个版本号小于第二个版本号时，应返回true", result);
    }

    /**
     * 测试第一个版本号大于第二个版本号时的情况
     */
    @Test
    public void testIsLessAndEqualThanGreaterVersion() {
        // arrange
        String versionA = "0.5.9";
        String versionB = "0.5.8";

        // act
        boolean result = VersionUtils.isLessAndEqualThan(versionA, versionB);

        // assert
        Assert.assertFalse("当第一个版本号大于第二个版本号时，应返回false", result);
    }
}
