package com.dianping.mobile.mapi.dztgdetail.helper;

import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import org.junit.runner.RunWith;
import static org.junit.Assert.*;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.Collections;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DealAttrHelper_GetServiceTypeTest {

    // Assuming "service_type" is the actual key used in DealAttrKeys.SERVICE_TYPE
    private static final String SERVICE_TYPE_KEY = "service_type";

    /**
     * Tests getServiceType method when attrs is null.
     */
    @Test
    public void testGetServiceTypeWhenAttrsIsNull() throws Throwable {
        // Act
        String result = DealAttrHelper.getServiceType(null);
        // Assert
        assertNull(result);
    }

    /**
     * Tests getServiceType method when attrs is an empty list.
     */
    @Test
    public void testGetServiceTypeWhenAttrsIsEmpty() throws Throwable {
        // Act
        String result = DealAttrHelper.getServiceType(Collections.emptyList());
        // Assert
        assertNull(result);
    }

    /**
     * Tests getServiceType method when attrs does not contain SERVICE_TYPE attribute.
     */
    @Test
    public void testGetServiceTypeWhenAttrsNotContainsServiceType() throws Throwable {
        // Arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("other");
        attrDTO.setValue(Arrays.asList("value1", "value2"));
        // Act
        String result = DealAttrHelper.getServiceType(Arrays.asList(attrDTO));
        // Assert
        // Corrected assertion to expect an empty string
        assertEquals("", result);
    }

    /**
     * Tests getServiceType method when attrs contains SERVICE_TYPE attribute but its value is empty.
     */
    @Test
    public void testGetServiceTypeWhenServiceTypeAttrValueIsEmpty() throws Throwable {
        // Arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("service_type");
        attrDTO.setValue(Collections.emptyList());
        // Act
        String result = DealAttrHelper.getServiceType(Arrays.asList(attrDTO));
        // Assert
        assertEquals("", result);
    }

    /**
     * Tests getServiceType method when attrs contains SERVICE_TYPE attribute and its value is not empty.
     */
    @Test
    public void testGetServiceTypeWhenServiceTypeAttrValueIsNotEmpty() throws Throwable {
        // Arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("service_type");
        attrDTO.setValue(Arrays.asList("value1"));
        // Act
        String result = DealAttrHelper.getServiceType(Arrays.asList(attrDTO));
        // Assert
        assertEquals("value1", result);
    }
}
