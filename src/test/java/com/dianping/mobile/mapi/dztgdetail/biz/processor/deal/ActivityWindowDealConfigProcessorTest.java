package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealActivityWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.product.shelf.common.dto.Response;
import com.dianping.product.shelf.common.dto.activity.ActivityDetailDTO;
import com.dianping.product.shelf.common.dto.subjectConfig.ConfigDTO;
import com.dianping.product.shelf.common.dto.subjectConfig.SubjectConfigDTO;
import com.dianping.product.shelf.common.dto.subjectConfig.SubjectConfigDTOList;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @create 2024-11-27-15:12
 */
@RunWith(MockitoJUnitRunner.class)
public class ActivityWindowDealConfigProcessorTest {

    @InjectMocks
    private ActivityWindowDealConfigProcessor processor;

    @Mock
    private DealActivityWrapper dealActivityWrapper;

    @Mock
    private Future future;

    private DealCtx ctx;

    private ActivityDetailDTO activityDetailDTO;

    @Before
    public void setUp() {
        activityDetailDTO = new ActivityDetailDTO();
        activityDetailDTO.setActivityId(1);

        ctx = new DealCtx(new EnvCtx());
        ctx.setDpLongShopId(123L);
        ctx.setDpId(1);
        FutureCtx futureCtx = new FutureCtx();
        futureCtx.setActivityDealConfigFuture(future);
        ctx.setFutureCtx(futureCtx);
    }

    /**
     * 测试prepare方法，正常情况
     */
    @Test
    public void testPrepareNormal() {
        // arrange
        when(dealActivityWrapper.getActivityDealDetail(ctx)).thenReturn(activityDetailDTO);
        when(dealActivityWrapper.preQueryDealActivityConfig(any())).thenReturn(future);

        // act
        processor.prepare(ctx);

        // assert
        verify(dealActivityWrapper, times(1)).preQueryDealActivityConfig(any());
    }

    /**
     * 测试场景：正常处理流程
     */
    @Test
    public void testProcessNormal() {

        // arrange
        Response<SubjectConfigDTOList> response = new Response<>();
        SubjectConfigDTOList subjectConfigDTOList = new SubjectConfigDTOList();
        List<SubjectConfigDTO> subjectConfigDTOs = new ArrayList<>();
        SubjectConfigDTO subjectConfigDTO = new SubjectConfigDTO();
        List<ConfigDTO> configDTOs = new ArrayList<>();
        configDTOs.add(new ConfigDTO());
        subjectConfigDTO.setConfigDTOList(configDTOs);
        subjectConfigDTOs.add(subjectConfigDTO);
        subjectConfigDTOList.setSubjectConfigDTOList(subjectConfigDTOs);
        response.setContent(subjectConfigDTOList);
        response.setSuccess(true);

        when(dealActivityWrapper.getFutureResult(ctx.getFutureCtx().getActivityDealConfigFuture())).thenReturn(response);

        // act
        processor.process(ctx);

        // assert
        assertNotNull(ctx.getActivityWindowDealConfigs());
    }

    /**
     * 测试场景：Null
     */
    @Test
    public void testProcessResponseNull() {


        // arrange
        Response<SubjectConfigDTOList> response = new Response<>();
        response.setContent(null);
        response.setSuccess(true);

        when(dealActivityWrapper.getFutureResult(future)).thenReturn(response);

        // act
        processor.process(ctx);

        // assert
        assertNull(ctx.getActivityWindowDealConfigs());
    }


}
