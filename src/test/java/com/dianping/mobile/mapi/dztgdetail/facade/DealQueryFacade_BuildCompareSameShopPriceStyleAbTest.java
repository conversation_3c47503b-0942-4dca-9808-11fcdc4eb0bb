package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedDeals;
import com.dianping.mobile.mapi.dztgdetail.entity.ExpResultConfig;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealQueryFacade_BuildCompareSameShopPriceStyleAbTest {

    @InjectMocks
    private DealQueryFacade dealQueryFacade;

    @Mock
    private DouHuService douHuService;

    private ModuleAbConfig moduleAbConfig;

    private RelatedDeals relatedDeals;

    private Integer categoryId;

    @Before
    public void setUp() {
        moduleAbConfig = new ModuleAbConfig();
        relatedDeals = new RelatedDeals();
        categoryId = 1;
    }

    /**
     * 测试实验结果为"a"、"b"或"d"的情况
     */
    @Test
    public void testBuildCompareSameShopPriceStyleAbExpResultIsAOrBOrD() throws Throwable {
        try (MockedStatic<LionConfigUtils> mockedLionConfigUtils = mockStatic(LionConfigUtils.class)) {
            when(douHuService.getExpResult(moduleAbConfig)).thenReturn("a");
            mockedLionConfigUtils.when(LionConfigUtils::getExpResultConfig).thenReturn(new ExpResultConfig());
            when(douHuService.getTabStyle(any(), anyInt(), any())).thenReturn("style");
            dealQueryFacade.buildCompareSameShopPriceStyleAb(moduleAbConfig, relatedDeals, categoryId);
            verify(douHuService, times(1)).getTabStyle(any(), anyInt(), any());
        }
    }

    /**
     * 测试实验结果不为"a"、"b"或"d"的情况
     */
    @Test
    public void testBuildCompareSameShopPriceStyleAbExpResultIsNotAOrBOrD() throws Throwable {
        when(douHuService.getExpResult(moduleAbConfig)).thenReturn("c");
        dealQueryFacade.buildCompareSameShopPriceStyleAb(moduleAbConfig, relatedDeals, categoryId);
        verify(douHuService, never()).getTabStyle(any(), anyInt(), any());
    }
}
