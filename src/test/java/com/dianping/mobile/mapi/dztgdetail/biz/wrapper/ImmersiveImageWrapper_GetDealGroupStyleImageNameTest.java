package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ImmersiveImageWrapper;
import com.sankuai.mpmctcontent.query.thrift.api.search.ContentSearchService;
import com.sankuai.mpmctcontent.query.thrift.dto.search.SearchDetailResponseDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.search.SearchDataDetailDTO;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Collections;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class ImmersiveImageWrapper_GetDealGroupStyleImageNameTest {

    @InjectMocks
    private ImmersiveImageWrapper immersiveImageWrapper;

    @Mock
    private ContentSearchService contentSearchService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetDealGroupStyleImageNameWithNullInfoContentId() throws Exception {
        String result = immersiveImageWrapper.getDealGroupStyleImageName(null);
        assertEquals("", result);
    }

    @Test
    public void testGetDealGroupStyleImageNameWithNegativeInfoContentId() throws Exception {
        String result = immersiveImageWrapper.getDealGroupStyleImageName(-1L);
        assertEquals("", result);
    }

    @Test
    public void testGetDealGroupStyleImageNameWithNullSearchResponse() throws Exception {
        when(contentSearchService.searchDetail(any())).thenReturn(null);
        String result = immersiveImageWrapper.getDealGroupStyleImageName(1L);
        assertEquals("", result);
    }

    @Test
    public void testGetDealGroupStyleImageNameWithNon200StatusCode() throws Exception {
        SearchDetailResponseDTO responseDTO = new SearchDetailResponseDTO();
        responseDTO.setCode(500);
        when(contentSearchService.searchDetail(any())).thenReturn(responseDTO);
        String result = immersiveImageWrapper.getDealGroupStyleImageName(1L);
        assertEquals("", result);
    }

    @Test
    public void testGetDealGroupStyleImageNameWithEmptySearchResult() throws Exception {
        SearchDetailResponseDTO responseDTO = new SearchDetailResponseDTO();
        responseDTO.setCode(200);
        responseDTO.setSearchResult(Collections.emptyList());
        when(contentSearchService.searchDetail(any())).thenReturn(responseDTO);
        String result = immersiveImageWrapper.getDealGroupStyleImageName(1L);
        assertEquals("", result);
    }

    @Test
    public void testGetDealGroupStyleImageNameWithAllDataInfoEmpty() throws Exception {
        SearchDetailResponseDTO responseDTO = new SearchDetailResponseDTO();
        responseDTO.setCode(200);
        SearchDataDetailDTO searchDataDetailDTO = new SearchDataDetailDTO();
        searchDataDetailDTO.setDataInfo("");
        responseDTO.setSearchResult(Collections.singletonList(searchDataDetailDTO));
        when(contentSearchService.searchDetail(any())).thenReturn(responseDTO);
        String result = immersiveImageWrapper.getDealGroupStyleImageName(1L);
        assertEquals("", result);
    }

    @Test
    public void testGetDealGroupStyleImageNameWithValidDataInfo() throws Exception {
        SearchDetailResponseDTO responseDTO = new SearchDetailResponseDTO();
        responseDTO.setCode(200);
        SearchDataDetailDTO searchDataDetailDTO = new SearchDataDetailDTO();
        searchDataDetailDTO.setDataInfo("{\"name\":\"test\"}");
        responseDTO.setSearchResult(Collections.singletonList(searchDataDetailDTO));
        when(contentSearchService.searchDetail(any())).thenReturn(responseDTO);
        String result = immersiveImageWrapper.getDealGroupStyleImageName(1L);
        assertEquals("test", result);
    }
}
