package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DzCardPromoWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MiniProgramSceneEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.PriceContext;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.sankuai.dzcard.navigation.api.dto.CardQualifyEventIdDTO;
import com.sankuai.dzcard.navigation.api.dto.FindQualifyEventIdsRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Collections;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest(value = {GreyUtils.class, Lion.class})
public class JoyDiscountCardProcessorTest {

    @Mock
    private DealCtx ctx;

    @Mock
    private EnvCtx envCtx;

    @Mock
    private PriceContext priceContext;

    @Mock
    private FutureCtx futureCtx;

    @InjectMocks
    private JoyDiscountCardProcessor processor;

    @InjectMocks
    private JoyDiscountCardProcessor joyDiscountCardProcessor;

    @Mock
    private DzCardPromoWrapper wrapper;

    @Before
    public void setUp() {
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
        when(ctx.getPriceContext()).thenReturn(priceContext);
    }

    /**
     * 测试场景：非外部请求，应该启用
     */
    @Test
    public void testIsEnable_NotExternal() {
        // arrange
        when(ctx.isExternal()).thenReturn(false);
        // act
        boolean result = processor.isEnable(ctx);
        // assert
        assertTrue(result);
    }

    /**
     * 测试场景：外部请求且启用
     */
    @Test
    public void testIsEnable_ExternalAndEnabled() {
        // arrange
        when(ctx.isExternal()).thenReturn(true);
        when(envCtx.isExternalAndEnabled(MiniProgramSceneEnum.JOY_CARD.getPromoScene())).thenReturn(true);
        // act
        boolean result = processor.isEnable(ctx);
        // assert
        assertTrue(result);
    }

    /**
     * 测试场景：外部请求但未启用
     */
    @Test
    public void testIsEnable_ExternalAndNotEnabled() {
        // arrange
        when(ctx.isExternal()).thenReturn(true);
        when(envCtx.isExternalAndEnabled(MiniProgramSceneEnum.JOY_CARD.getPromoScene())).thenReturn(false);
        // act
        boolean result = processor.isEnable(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * 测试prepare方法，当dpLongShopId大于0的情况
     */
    @Test
    public void testPrepareDpLongShopIdGreaterThanZero() {
        // arrange
        when(ctx.getDpLongShopId()).thenReturn(1L);
        // act
        joyDiscountCardProcessor.prepare(ctx);
        // assert
        verify(ctx, times(1)).getDpLongShopId();
        verify(wrapper, times(1)).prepare(ctx);
        verify(wrapper, times(1)).prepareUserState(ctx);
    }

    /**
     * 测试prepare方法，当dpLongShopId等于0的情况
     */
    @Test
    public void testPrepareDpLongShopIdEqualsZero() {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.getDpLongShopId()).thenReturn(0L);
        // act
        joyDiscountCardProcessor.prepare(ctx);
        // assert
        verify(ctx, times(1)).getDpLongShopId();
        verifyNoMoreInteractions(ctx);
        verifyZeroInteractions(wrapper);
    }

    /**
     * 测试prepare方法，当dpLongShopId小于0的情况
     */
    @Test
    public void testPrepareDpLongShopIdLessThanZero() {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.getDpLongShopId()).thenReturn(-1L);
        // act
        joyDiscountCardProcessor.prepare(ctx);
        // assert
        verify(ctx, times(1)).getDpLongShopId();
        verifyNoMoreInteractions(ctx);
        verifyZeroInteractions(wrapper);
    }

    /**
     * 测试 process 方法，当 cards 为空时的场景
     */
    @Test
    public void testProcessWhenCardsIsEmpty() {
        // arrange
        when(wrapper.resolve(ctx.getFutureCtx().getDzCardFuture())).thenReturn(Collections.emptyList());
        // act
        joyDiscountCardProcessor.process(ctx);
        // assert
        verify(wrapper, times(1)).resolve(ctx.getFutureCtx().getDzCardFuture());
        // 由于 cards 为空，不会进一步调用 removeMemberCardIfExistedMemberDayCard 和 decisionUseCard 方法
    }

    /**
     * 测试 process 方法，当 cards 不为空时的场景
     */
    @Test
    public void testProcessWhenCardsIsNotEmpty() {
        // arrange
        Future future = CompletableFuture.completedFuture(new FindQualifyEventIdsRespDTO());
        when(ctx.getFutureCtx().getDzCardFuture()).thenReturn(future);
        when(wrapper.resolve(ctx.getFutureCtx().getDzCardFuture())).thenReturn(Collections.singletonList(new CardQualifyEventIdDTO()));
        // act
        joyDiscountCardProcessor.process(ctx);
        // assert
        verify(wrapper, times(1)).resolve(ctx.getFutureCtx().getDzCardFuture());
        // 由于 cards 不为空，会进一步调用 removeMemberCardIfExistedMemberDayCard 和 decisionUseCard 方法，但这些方法的具体行为不在这个测试用例的范围内
    }

    /**
     * 测试 process 方法，当 resolveUserState 抛出异常时的场景
     */
    @Test(expected = RuntimeException.class)
    public void testProcessWhenResolveUserStateThrowsException() {
        // arrange
        when(wrapper.resolveUserState(ctx.getFutureCtx().getUserStateFuture())).thenThrow(new RuntimeException());
        // act
        joyDiscountCardProcessor.process(ctx);
        // assert
        // 期望抛出 RuntimeException
    }
}
