package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.deal.sale.api.dto.ProductSaleDto;
import com.dianping.deal.sale.api.enums.SourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealRelatedBehaviorItem;
import com.dianping.userremote.base.dto.UserDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/11/12 14:56
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest()
public class DealRelatedBehaviorProcessorTest {

    @InjectMocks
    private DealRelatedBehaviorProcessor dealRelatedBehaviorProcessor;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testConvertProductSale2RelatedBehavior() throws InvocationTargetException, IllegalAccessException {
        boolean isMt = true;
        ProductSaleDto productSaleDto = new ProductSaleDto();
        productSaleDto.setSource(SourceEnum.MtPayOrder.getSource());
        productSaleDto.setSuccessTime(new Date());
        List<ProductSaleDto> productSales = Lists.newArrayList(productSaleDto);
        Map<Long, UserModel> mtUserModelMap = Maps.newHashMap();
        Map<Long, UserDTO> dpUserModelMap = Maps.newHashMap();

        Method method = PowerMockito.method(DealRelatedBehaviorProcessor.class, "convertProductSale2RelatedBehavior");
        List<DealRelatedBehaviorItem> result = (List<DealRelatedBehaviorItem>)method
                .invoke(dealRelatedBehaviorProcessor, isMt, productSales, mtUserModelMap, dpUserModelMap);
        assert CollectionUtils.isEmpty(result);

        productSaleDto.setSource(SourceEnum.DpPayOrder.getSource());
        productSaleDto.setDpUserId(123L);
        List<DealRelatedBehaviorItem> result1 = (List<DealRelatedBehaviorItem>)method
                .invoke(dealRelatedBehaviorProcessor, isMt, productSales, mtUserModelMap, dpUserModelMap);
        assert CollectionUtils.isEmpty(result1);
    }

}