package com.dianping.mobile.mapi.dztgdetail.facade.rcf.flash;

import com.alibaba.fastjson.JSON;
import com.dianping.mobile.mapi.dztgdetail.biz.service.cellarservice.compoment.DealDetailCellarComponent;
import com.dianping.mobile.mapi.dztgdetail.biz.service.cellarservice.dealdetail.DealGroupDTOCellarService;
import com.dianping.mobile.mapi.dztgdetail.biz.service.cellarservice.dealdetail.req.DealGroupCacheRequest;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FlashDealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FlashFutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.struct.DealDetailStructModuleDo;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.flash.DealFlashQueryCenterProcessor;
import com.google.common.collect.Sets;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupCategoryBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupIdBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.Future;

/**
 */
@RunWith(MockitoJUnitRunner.class)
public class DealFlashQueryCenterProcessorTest {
    @InjectMocks
    DealFlashQueryCenterProcessor dealFlashQueryCenterProcessor;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Mock
    private DealGroupQueryService queryCenterDealGroupQueryService;

    @Mock
    private DealGroupQueryService queryCenterDealGroupQueryServiceFuture;

    @Mock
    private DealGroupDTOCellarService dealGroupDTOCellarService;
    @Mock
    private DealDetailCellarComponent dealDetailCellarService;

    FlashDealCtx ctx = new FlashDealCtx(new EnvCtx());
    @Before
    public void setUp() throws Exception {
        ctx.setFutureCtx(new FlashFutureCtx());
    }


    @Test
    public void prepareTest() throws TException {
        // 从缓存获取 通用样式团购详情
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.builder().dealGroupIds(Sets.newHashSet((long) ctx.getDealGroupId()), ctx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP).dealGroupId(DealGroupIdBuilder.builder().all()).category(DealGroupCategoryBuilder.builder().all()).build();
        Future future = Mockito.mock(Future.class);
        QueryByDealGroupIdRequest request = new QueryByDealGroupIdRequest();
//        Mockito.when(queryCenterDealGroupQueryServiceFuture.queryByDealGroupIds(request)).thenReturn(null);
//        Mockito.when(queryCenterWrapper.preDealGroupDTO(queryByDealGroupIdRequest)).thenReturn(future);
        dealFlashQueryCenterProcessor.prepare(ctx);
        Assert.assertNotNull(future);
    }

    @Test
    public void processTest() throws TException {
        DealDetailStructModuleDo result = new DealDetailStructModuleDo();
        Future future = Mockito.mock(Future.class);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setCategory(new DealGroupCategoryDTO());
//        Mockito.when(queryCenterWrapper.getDealGroupDTO(future)).thenReturn(dealGroupDTO) ;
        ctx.setDealGroupId(1);
        dealFlashQueryCenterProcessor.process(ctx);
        dealGroupDTOCellarService.parseCacheValue("");
        dealGroupDTOCellarService.parseCacheValue(JSON.toJSONString(dealGroupDTO));
        dealGroupDTOCellarService.getValueFromCellar("key");
        DealGroupCacheRequest request = new DealGroupCacheRequest();
        request.setDealGroupId(ctx.getDealGroupId());
        request.setMT(ctx.isMt());
        dealGroupDTOCellarService.buildKey(request);
        dealGroupDTOCellarService.saveOrUpdate(request, dealGroupDTO);
        Assert.assertNotNull(dealGroupDTO);
    }
}
