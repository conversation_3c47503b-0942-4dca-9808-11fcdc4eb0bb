package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PromoDetailModule;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.PriceDTO;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.util.Date;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class ParallDealBuilderProcessor_NotMeetSellingTimeTest {

    private ParallDealBuilderProcessor parallDealBuilderProcessor;

    @Before
    public void setUp() {
        parallDealBuilderProcessor = new ParallDealBuilderProcessor();
    }

    /**
     * 测试 notMeetSellingTime 方法，当 dealGroupBase 为 null 时，应返回 false
     */
    @Test
    public void testNotMeetSellingTime_DealGroupBaseIsNull() {
        assertFalse(parallDealBuilderProcessor.notMeetSellingTime(null));
    }

    /**
     * 测试 notMeetSellingTime 方法，当 dealGroupBase 的 beginDate 为 null 时，应返回 false
     */
    @Test
    public void testNotMeetSellingTime_BeginDateIsNull() {
        DealGroupBaseDTO dealGroupBase = new DealGroupBaseDTO();
        assertFalse(parallDealBuilderProcessor.notMeetSellingTime(dealGroupBase));
    }

    /**
     * 测试 notMeetSellingTime 方法，当 dealGroupBase 的 beginDate 早于当前时间时，应返回 false
     */
    @Test
    public void testNotMeetSellingTime_BeginDateIsBeforeNow() {
        DealGroupBaseDTO dealGroupBase = new DealGroupBaseDTO();
        dealGroupBase.setBeginDate(new Date(System.currentTimeMillis() - 1000));
        assertFalse(parallDealBuilderProcessor.notMeetSellingTime(dealGroupBase));
    }

    /**
     * 测试 notMeetSellingTime 方法，当 dealGroupBase 的 beginDate 晚于当前时间时，应返回 true
     */
    @Test
    public void testNotMeetSellingTime_BeginDateIsAfterNow() {
        DealGroupBaseDTO dealGroupBase = new DealGroupBaseDTO();
        dealGroupBase.setBeginDate(new Date(System.currentTimeMillis() + 1000));
        assertTrue(parallDealBuilderProcessor.notMeetSellingTime(dealGroupBase));
    }

    @Test
    public void testSetPreviewMarketPromoDiscount() throws Exception {
        Class clazz = parallDealBuilderProcessor.getClass();
        Method method = clazz.getDeclaredMethod("setPreviewMarketPromoDiscount", PromoDetailModule.class, DealGroupDTO.class);
        method.setAccessible(true);
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        PriceDTO price = new PriceDTO();
        price.setMarketPrice("18");
        price.setSalePrice("9");
        dealGroupDTO.setPrice(price);
        method.invoke(parallDealBuilderProcessor, promoDetailModule, dealGroupDTO);
        promoDetailModule.getMarketPromoDiscount();
        Assert.assertTrue(StringUtils.equals("5.0折", promoDetailModule.getMarketPromoDiscount()));
    }
}
