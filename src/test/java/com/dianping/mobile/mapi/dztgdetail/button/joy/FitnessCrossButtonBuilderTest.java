package com.dianping.mobile.mapi.dztgdetail.button.joy;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.enums.FitnessCrossIdentityEnum;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderFactory;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.FitnessCrossBO;
import com.dianping.mobile.mapi.dztgdetail.helper.NewBuyBarHelper;
import org.junit.Assert;
import org.junit.Test;

public class FitnessCrossButtonBuilderTest {

    @Test
    public void test() {
        ButtonBuilderChain chain = ButtonBuilderFactory.newButtonBuilderChain(NewBuyBarHelper.buildFitnessCrossChainConfig());
        DealCtx ctx = buildDealCtx();
        chain.build(ctx);
        Assert.assertNotNull(ctx.getBuyBar());
    }

    private DealCtx buildDealCtx() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setVersion("12.20.400");

        DealCtx dealCtx = new DealCtx(envCtx);

        FitnessCrossBO fitnessCrossBO = FitnessCrossBO.builder()
                .identityEnum(FitnessCrossIdentityEnum.TOURIST)
                .hasAvailableCoupon(false)
                .config(Lion.getBean("com.sankuai.dzu.tpbase.dztgdetailweb", "com.sankuai.dzu.tpbase.dztgdetailweb.fitnesscross.text.config", FitnessCrossBO.Config.class))
                .build();

        dealCtx.setFitnessCrossBO(fitnessCrossBO);
        return dealCtx;
    }

}