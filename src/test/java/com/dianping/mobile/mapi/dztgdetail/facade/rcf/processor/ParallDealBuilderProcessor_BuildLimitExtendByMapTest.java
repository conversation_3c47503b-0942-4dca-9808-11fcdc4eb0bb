package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.Guarantee;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.LayerConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.RuleInfoPO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.buy.DealGroupBuyRuleDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ParallDealBuilderProcessor_BuildLimitExtendByMapTest {

    private ParallDealBuilderProcessor parallDealBuilderProcessor;

    private List<Guarantee> limitsExtends;

    private LayerConfig limitInfoConfig;

    @Spy
    private ParallDealBuilderProcessor processor;

    @Mock
    private DealCtx ctx;

    @Mock
    private DealGroupPBO result;

    @Mock
    private DealGroupDTO dealGroup;

    @Mock
    private DealGroupCategoryDTO category;

    @Mock
    private DealGroupRuleDTO rule;

    @Mock
    private DealGroupBuyRuleDTO buyRuleDTO;

    @Before
    public void setUp() {
        parallDealBuilderProcessor = new ParallDealBuilderProcessor();
        limitsExtends = new ArrayList<>();
        limitInfoConfig = new LayerConfig();
    }

    /**
     * 测试buildLimitExtendByMap方法，当LayerConfig对象的title字段为空时
     */
    @Test
    public void testBuildLimitExtendByMapTitleIsNull() {
        // arrange
        limitInfoConfig.setTitle(null);
        // act
        parallDealBuilderProcessor.buildLimitExtendByMap(limitsExtends, limitInfoConfig);
        // assert
        assertEquals(0, limitsExtends.size());
    }

    /**
     * 测试buildLimitExtendByMap方法，当LayerConfig对象的title字段不为空时
     */
    @Test
    public void testBuildLimitExtendByMapTitleIsNotNull() {
        // arrange
        String title = "testTitle";
        limitInfoConfig.setTitle(title);
        // act
        parallDealBuilderProcessor.buildLimitExtendByMap(limitsExtends, limitInfoConfig);
        // assert
        assertEquals(1, limitsExtends.size());
        assertEquals(title, limitsExtends.get(0).getText());
    }

    /**
     * Test case for normal scenario where the input list is not empty.
     */
    @Test
    public void testBuildPinTuanLayerConfigsNormalScenario() throws Throwable {
        // arrange
        ParallDealBuilderProcessor processor = new ParallDealBuilderProcessor();
        List<RuleInfoPO> ruleInfoPOS = new ArrayList<>();
        RuleInfoPO rule1 = new RuleInfoPO();
        rule1.setTitle("Rule 1 Title");
        rule1.setContent("Rule 1 Content");
        RuleInfoPO rule2 = new RuleInfoPO();
        rule2.setTitle("Rule 2 Title");
        rule2.setContent("Rule 2 Content");
        ruleInfoPOS.add(rule1);
        ruleInfoPOS.add(rule2);
        // act
        List<LayerConfig> result = processor.buildPinTuanLayerConfigs(ruleInfoPOS);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("Rule 1 Title", result.get(0).getTitle());
        assertEquals("Rule 1 Content", result.get(0).getDesc());
        assertEquals("Rule 2 Title", result.get(1).getTitle());
        assertEquals("Rule 2 Content", result.get(1).getDesc());
    }

    /**
     * Test case for empty list scenario where the input list is empty.
     */
    @Test
    public void testBuildPinTuanLayerConfigsEmptyListScenario() throws Throwable {
        // arrange
        ParallDealBuilderProcessor processor = new ParallDealBuilderProcessor();
        List<RuleInfoPO> ruleInfoPOS = new ArrayList<>();
        // act
        List<LayerConfig> result = processor.buildPinTuanLayerConfigs(ruleInfoPOS);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for null list scenario where the input list is null.
     */
    @Test
    public void testBuildPinTuanLayerConfigsNullListScenario() throws Throwable {
        // arrange
        ParallDealBuilderProcessor processor = new ParallDealBuilderProcessor();
        List<RuleInfoPO> ruleInfoPOS = null;
        // act
        List<LayerConfig> result = processor.buildPinTuanLayerConfigs(ruleInfoPOS);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testBuildPurchaseLimitInfoWhenDealGroupIsNull() throws Throwable {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(null);
        when(ctx.isFreeDeal()).thenReturn(false);
        // act
        processor.buildPurchaseLimitInfo(ctx, result);
        // assert
        verify(ctx).getDealGroupDTO();
        verify(ctx).isFreeDeal();
        // Should not call buildLimitInfo since dealGroup is null
        verify(processor, never()).buildLimitInfo(any(), any(), any(), any());
    }

    @Test
    public void testBuildPurchaseLimitInfoWhenCategoryIsNull() throws Throwable {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(dealGroup);
        when(ctx.isFreeDeal()).thenReturn(false);
        when(dealGroup.getCategory()).thenReturn(null);
        when(dealGroup.getRule()).thenReturn(rule);
        when(rule.getBuyRule()).thenReturn(buyRuleDTO);
        doNothing().when(processor).buildLimitInfo(any(), any(), any(), any());
        // act
        processor.buildPurchaseLimitInfo(ctx, result);
        // assert
        verify(ctx).getDealGroupDTO();
        verify(dealGroup, atLeastOnce()).getCategory();
        verify(rule, atLeastOnce()).getBuyRule();
        verify(processor).buildLimitInfo(eq(dealGroup), eq(ctx), eq(result), eq(buyRuleDTO));
    }

    @Test
    public void testBuildPurchaseLimitInfoWhenServiceTypeIdIsNull() throws Throwable {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(dealGroup);
        when(ctx.isFreeDeal()).thenReturn(false);
        when(dealGroup.getCategory()).thenReturn(category);
        when(category.getServiceTypeId()).thenReturn(null);
        when(dealGroup.getRule()).thenReturn(rule);
        when(rule.getBuyRule()).thenReturn(buyRuleDTO);
        doNothing().when(processor).buildLimitInfo(any(), any(), any(), any());
        // act
        processor.buildPurchaseLimitInfo(ctx, result);
        // assert
        verify(ctx).getDealGroupDTO();
        verify(dealGroup, atLeastOnce()).getCategory();
        verify(category, atLeastOnce()).getServiceTypeId();
        verify(rule, atLeastOnce()).getBuyRule();
        verify(processor).buildLimitInfo(eq(dealGroup), eq(ctx), eq(result), eq(buyRuleDTO));
    }

    @Test
    public void testBuildPurchaseLimitInfoWhenCategoryNot651() throws Throwable {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(dealGroup);
        when(ctx.isFreeDeal()).thenReturn(false);
        when(dealGroup.getCategory()).thenReturn(category);
        when(category.getServiceTypeId()).thenReturn(500L);
        when(dealGroup.getRule()).thenReturn(rule);
        when(rule.getBuyRule()).thenReturn(buyRuleDTO);
        doNothing().when(processor).buildLimitInfo(any(), any(), any(), any());
        // act
        processor.buildPurchaseLimitInfo(ctx, result);
        // assert
        verify(ctx).getDealGroupDTO();
        verify(dealGroup, atLeastOnce()).getCategory();
        verify(category, atLeastOnce()).getServiceTypeId();
        verify(rule, atLeastOnce()).getBuyRule();
        verify(processor).buildLimitInfo(eq(dealGroup), eq(ctx), eq(result), eq(buyRuleDTO));
    }

    @Test
    public void testBuildPurchaseLimitInfoWhenRuleIsNull() throws Throwable {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(dealGroup);
        when(ctx.isFreeDeal()).thenReturn(false);
        when(dealGroup.getCategory()).thenReturn(category);
        when(category.getServiceTypeId()).thenReturn(500L);
        when(dealGroup.getRule()).thenReturn(null);
        // act
        processor.buildPurchaseLimitInfo(ctx, result);
        // assert
        verify(ctx).getDealGroupDTO();
        verify(dealGroup, atLeastOnce()).getCategory();
        verify(category, atLeastOnce()).getServiceTypeId();
        verify(dealGroup, atLeastOnce()).getRule();
        // Should not call buildLimitInfo since rule is null
        verify(processor, never()).buildLimitInfo(any(), any(), any(), any());
    }

    @Test
    public void testBuildPurchaseLimitInfoWhenBuyRuleIsNull() throws Throwable {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(dealGroup);
        when(ctx.isFreeDeal()).thenReturn(false);
        when(dealGroup.getCategory()).thenReturn(category);
        when(category.getServiceTypeId()).thenReturn(500L);
        when(dealGroup.getRule()).thenReturn(rule);
        when(rule.getBuyRule()).thenReturn(null);
        // act
        processor.buildPurchaseLimitInfo(ctx, result);
        // assert
        verify(ctx).getDealGroupDTO();
        verify(dealGroup, atLeastOnce()).getCategory();
        verify(category, atLeastOnce()).getServiceTypeId();
        verify(rule, atLeastOnce()).getBuyRule();
        // Should not call buildLimitInfo since buyRule is null
        verify(processor, never()).buildLimitInfo(any(), any(), any(), any());
    }

    @Test
    public void testBuildPurchaseLimitInfoWithValidConditions() throws Throwable {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(dealGroup);
        when(ctx.isFreeDeal()).thenReturn(false);
        when(dealGroup.getCategory()).thenReturn(category);
        when(category.getServiceTypeId()).thenReturn(500L);
        when(dealGroup.getRule()).thenReturn(rule);
        when(rule.getBuyRule()).thenReturn(buyRuleDTO);
        doNothing().when(processor).buildLimitInfo(any(), any(), any(), any());
        // act
        processor.buildPurchaseLimitInfo(ctx, result);
        // assert
        verify(ctx).getDealGroupDTO();
        verify(dealGroup, atLeastOnce()).getCategory();
        verify(category, atLeastOnce()).getServiceTypeId();
        verify(rule, atLeastOnce()).getBuyRule();
        verify(processor).buildLimitInfo(eq(dealGroup), eq(ctx), eq(result), eq(buyRuleDTO));
    }

    @Test
    public void testBuildPurchaseLimitInfoWhenCategory651AndNotOldMetaVersion() throws Throwable {
        // Create a custom processor that overrides the buildPurchaseLimitInfo method
        ParallDealBuilderProcessor customProcessor = new ParallDealBuilderProcessor() {

            @Override
            public void buildPurchaseLimitInfo(DealCtx ctx, DealGroupPBO result) {
                // Call super method but intercept the static call to DealVersionUtils
                DealGroupDTO dealGroup = ctx.getDealGroupDTO();
                if (ctx.isFreeDeal() && ctx.getFreeDealConfig() != null
                        && !CollectionUtils.isEmpty(ctx.getFreeDealConfig().getLimit())) {
                    result.setLimits(ctx.getFreeDealConfig().getLimit());
                    return;
                }
                // Get thirdCategoryId - this is the code we're testing
                int thirdCategoryId = 0;
                if (dealGroup != null && dealGroup.getCategory() != null
                        && dealGroup.getCategory().getServiceTypeId() != null) {
                    thirdCategoryId = dealGroup.getCategory().getServiceTypeId().intValue();
                }
                // This is the condition we're testing
                if (thirdCategoryId == 651) {
                    // We're forcing this to be false for this test
                    boolean isOldMetaVersion = false;
                    if (!isOldMetaVersion) {
                        return;
                    }
                }
                // Rest of the method
                if (dealGroup != null && dealGroup.getRule() != null && dealGroup.getRule().getBuyRule() != null) {
                    DealGroupBuyRuleDTO buyRuleDTO = dealGroup.getRule().getBuyRule();
                    buildLimitInfo(dealGroup, ctx, result, buyRuleDTO);
                }
            }
        };
        // Spy on our custom processor
        ParallDealBuilderProcessor spyProcessor = spy(customProcessor);
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(dealGroup);
        when(ctx.isFreeDeal()).thenReturn(false);
        when(dealGroup.getCategory()).thenReturn(category);
        when(category.getServiceTypeId()).thenReturn(651L);
        // act
        spyProcessor.buildPurchaseLimitInfo(ctx, result);
        // assert
        verify(ctx).getDealGroupDTO();
        // Don't verify exact number of calls to getCategory() since it's called multiple times
        verify(category, atLeastOnce()).getServiceTypeId();
        // Should not proceed to check rule or call buildLimitInfo
        verify(dealGroup, never()).getRule();
        verify(spyProcessor, never()).buildLimitInfo(any(), any(), any(), any());
    }

    @Test
    public void testBuildPurchaseLimitInfoWhenCategory651AndOldMetaVersion() throws Throwable {
        // Create a custom processor that overrides the buildPurchaseLimitInfo method
        ParallDealBuilderProcessor customProcessor = new ParallDealBuilderProcessor() {

            @Override
            public void buildPurchaseLimitInfo(DealCtx ctx, DealGroupPBO result) {
                // Call super method but intercept the static call to DealVersionUtils
                DealGroupDTO dealGroup = ctx.getDealGroupDTO();
                if (ctx.isFreeDeal() && ctx.getFreeDealConfig() != null
                        && !CollectionUtils.isEmpty(ctx.getFreeDealConfig().getLimit())) {
                    result.setLimits(ctx.getFreeDealConfig().getLimit());
                    return;
                }
                // Get thirdCategoryId - this is the code we're testing
                int thirdCategoryId = 0;
                if (dealGroup != null && dealGroup.getCategory() != null
                        && dealGroup.getCategory().getServiceTypeId() != null) {
                    thirdCategoryId = dealGroup.getCategory().getServiceTypeId().intValue();
                }
                // This is the condition we're testing
                if (thirdCategoryId == 651) {
                    // We're forcing this to be true for this test
                    boolean isOldMetaVersion = true;
                    if (!isOldMetaVersion) {
                        return;
                    }
                }
                // Rest of the method
                if (dealGroup != null && dealGroup.getRule() != null && dealGroup.getRule().getBuyRule() != null) {
                    DealGroupBuyRuleDTO buyRuleDTO = dealGroup.getRule().getBuyRule();
                    buildLimitInfo(dealGroup, ctx, result, buyRuleDTO);
                }
            }
        };
        // Spy on our custom processor
        ParallDealBuilderProcessor spyProcessor = spy(customProcessor);
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(dealGroup);
        when(ctx.isFreeDeal()).thenReturn(false);
        when(dealGroup.getCategory()).thenReturn(category);
        when(category.getServiceTypeId()).thenReturn(651L);
        when(dealGroup.getRule()).thenReturn(rule);
        when(rule.getBuyRule()).thenReturn(buyRuleDTO);
        doNothing().when(spyProcessor).buildLimitInfo(any(), any(), any(), any());
        // act
        spyProcessor.buildPurchaseLimitInfo(ctx, result);
        // assert
        verify(ctx).getDealGroupDTO();
        // Don't verify exact number of calls to getCategory() since it's called multiple times
        verify(category, atLeastOnce()).getServiceTypeId();
        verify(dealGroup, atLeastOnce()).getRule();
        verify(rule, atLeastOnce()).getBuyRule();
        verify(spyProcessor).buildLimitInfo(eq(dealGroup), eq(ctx), eq(result), eq(buyRuleDTO));
    }
}
