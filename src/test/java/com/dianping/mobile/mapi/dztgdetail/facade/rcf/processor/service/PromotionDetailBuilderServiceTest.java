package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.mobile.mapi.dztgdetail.biz.LeafRepository;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.DztgCouponInfo;
import com.dianping.mobile.mapi.dztgdetail.util.PromoInfoHelper;
import com.sankuai.dealuser.price.display.api.enums.CouponAssignStatusEnum;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dealuser.price.display.api.model.PromoIdentity;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2024-06-25
 * @desc 测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class PromotionDetailBuilderServiceTest {

    @InjectMocks
    private PromotionDetailBuilderService promotionDetailBuilderService;

    @Mock
    private LeafRepository leafRepository;

    private MockedStatic<PromoInfoHelper> promoInfoHelperMockedStatic;

    @Before
    public void setUp() {
        promoInfoHelperMockedStatic = mockStatic(PromoInfoHelper.class);
    }

    @After
    public void tearDown() {
        promoInfoHelperMockedStatic.close();
    }

    @Test
    public void testConvertPromoDTO2DztgCouponInfoNull() throws Throwable {
        PromoDTO coupon = null;
        assertNull(promotionDetailBuilderService.convertPromoDTO2DztgCouponInfo(coupon, null));
    }

    @Test
    public void testConvertPromoDTO2DztgCouponInfoIdentityNull() throws Throwable {
        PromoDTO coupon = new PromoDTO();
        assertNull(promotionDetailBuilderService.convertPromoDTO2DztgCouponInfo(coupon, null));
    }

    @Test
    public void testConvertPromoDTO2DztgCouponInfoCouponGroupIdNull() throws Throwable {
        PromoDTO coupon = new PromoDTO();
        coupon.setIdentity(new PromoIdentity(1));
        assertNull(promotionDetailBuilderService.convertPromoDTO2DztgCouponInfo(coupon, null));
    }



    @Test
    public void testConvertPromoDTO2DztgCouponInfoAmountAndTitleNull() throws Throwable {
        PromoDTO coupon = new PromoDTO();
        coupon.setIdentity(new PromoIdentity(1));
        coupon.setCouponGroupId("1");
        coupon.setCouponId("1");
        assertNull(promotionDetailBuilderService.convertPromoDTO2DztgCouponInfo(coupon, null));
    }

    @Test
    public void testConvertPromoDTO2DztgCouponInfoNormal() throws Throwable {
        PromoDTO coupon = new PromoDTO();
        coupon.setIdentity(new PromoIdentity(1));
        coupon.setCouponGroupId("1");
        coupon.setCouponId("1");
        coupon.setExtendDesc("desc");
        coupon.setCouponValueText("10");
        coupon.setAmount(BigDecimal.TEN);
        assertNotNull(promotionDetailBuilderService.convertPromoDTO2DztgCouponInfo(coupon, null));
    }

    @Test
    public void testCoupon() {
        PromoDTO coupon = new PromoDTO();
        coupon.setCouponId("4444444");
        coupon.setExtendDesc("ceshi");
        coupon.setAmount(BigDecimal.TEN);
        coupon.setCouponValueText("shiyuan");
        promotionDetailBuilderService.convertPromoDTO2DztgCouponInfo(coupon, null);
        assertNotNull(coupon);
    }

    /**
     * 测试 PromoDTO 为 GOVERNMENT_CONSUME_COUPON 类型，未分配状态，且 leafRepository 返回有效 ID 的情况
     */
    @Test
    public void testConvertPromoDTO2DztgCouponInfo_GovernmentConsumeCouponUnAssigned() {
        // arrange
        PromoDTO coupon = new PromoDTO();
        coupon.setIdentity(new PromoIdentity(11));
        coupon.getIdentity().setPromoType(PromoTypeEnum.GOVERNMENT_CONSUME_COUPON.getType());
        coupon.setCouponGroupId("456");
        coupon.setAmount(new BigDecimal(100));
        coupon.setCouponValueText("100元");
        coupon.setExtendDesc("政府消费券");
        coupon.setCouponAssignStatus(CouponAssignStatusEnum.UN_ASSIGNED.getCode());
        when(leafRepository.batchGenFinancialConsumeSerialId(1)).thenReturn(Arrays.asList(789L));
        promoInfoHelperMockedStatic.when(() -> PromoInfoHelper.getFinanceExtPackageSecretKey(any()))
                .thenReturn("abc");
        // act
        DztgCouponInfo result = promotionDetailBuilderService.convertPromoDTO2DztgCouponInfo(coupon, null);

        // assert
        assertEquals(Long.valueOf(456), result.getCouponGroupId());
        assertEquals("789", result.getSerialno());
        assertEquals(Integer.valueOf(0), result.getStatus());
    }
}
