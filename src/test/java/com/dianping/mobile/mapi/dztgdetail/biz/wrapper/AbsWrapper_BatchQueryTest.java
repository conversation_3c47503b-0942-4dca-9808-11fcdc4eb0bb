package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.google.common.base.Function;
import com.google.common.collect.Lists;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.List;
import java.util.concurrent.Future;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import org.junit.*;
import com.google.common.collect.Maps;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class AbsWrapper_BatchQueryTest {

    @Mock
    private Function<String, Future> function;

    @Mock
    private Future future;

    @Test
    public void testBatchQueryRequestsIsNull() throws Throwable {
        AbsWrapper absWrapper = new AbsWrapper() {

            @Override
            public Object getFutureResult(Future future) {
                return new Object();
            }
        };
        List<String> requests = null;
        List<Object> result = absWrapper.batchQuery(requests, function);
        assertEquals(0, result.size());
    }

    @Test
    public void testBatchQueryResultMapIsNull() throws Throwable {
        AbsWrapper absWrapper = new AbsWrapper() {

            @Override
            public Object getFutureResult(Future future) {
                return new Object();
            }
        };
        List<String> requests = Lists.newArrayList("request1", "request2");
        when(function.apply("request1")).thenReturn(future);
        when(function.apply("request2")).thenReturn(future);
        List<Object> result = absWrapper.batchQuery(requests, function);
        // Corrected expectation
        assertEquals(2, result.size());
    }

    @Test
    public void testBatchQueryNoValueInResultMap() throws Throwable {
        AbsWrapper absWrapper = new AbsWrapper() {

            @Override
            public Object getFutureResult(Future future) {
                return new Object();
            }
        };
        List<String> requests = Lists.newArrayList("request1", "request2");
        when(function.apply("request1")).thenReturn(future);
        when(function.apply("request2")).thenReturn(future);
        List<Object> result = absWrapper.batchQuery(requests, function);
        // Corrected expectation
        assertEquals(2, result.size());
    }

    @Test
    public void testBatchQueryValueInResultMap() throws Throwable {
        AbsWrapper absWrapper = new AbsWrapper() {

            @Override
            public Object getFutureResult(Future future) {
                return new Object();
            }
        };
        List<String> requests = Lists.newArrayList("request1", "request2");
        when(function.apply("request1")).thenReturn(future);
        when(function.apply("request2")).thenReturn(future);
        List<Object> result = absWrapper.batchQuery(requests, function);
        assertEquals(2, result.size());
    }
}
