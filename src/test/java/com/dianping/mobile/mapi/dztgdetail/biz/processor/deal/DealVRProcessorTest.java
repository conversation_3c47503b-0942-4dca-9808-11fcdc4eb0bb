package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DigestQueryWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.InterestWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.DigestConfinementVrDTO;
import com.sankuai.interest.core.thrift.remote.dto.InterestCalculateResponse;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.Future;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @create 2024-11-27-15:43
 */
@RunWith(MockitoJUnitRunner.class)
public class DealVRProcessorTest {

    @Mock
    private InterestWrapper interestWrapper;

    @Mock
    private DigestQueryWrapper digestQueryWrapper;

    @InjectMocks
    private DealVRProcessor processor;

    private DealCtx ctx;

    @Mock
    private Future future;


    @Before
    public void setUp() {
        EnvCtx envCtx = new EnvCtx();
        ctx = new DealCtx(envCtx);
        FutureCtx futureCtx = new FutureCtx();
        futureCtx.setShopInterestFuture(future);
        futureCtx.setVrInfoFuture(future);
        ctx.setFutureCtx(futureCtx);
    }

    /**
     * 测试场景：来自H5
     */
    @Test
    public void testIsEnableFromH5() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setFromH5(true);
        DealCtx ctx = new DealCtx(envCtx);
        assertFalse(processor.isEnable(ctx));
    }

    /**
     * 测试场景：类目ID不在指定列表中
     */
    @Test
    public void testIsEnableCategoryIdNotInList() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setFromH5(false);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setChannelDTO(null);

        assertFalse(processor.isEnable(ctx));
    }

    /**
     * 测试场景：没有关键属性
     */
    @Test
    public void testIsEnableNoKeyAttr() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setFromH5(false);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(1011);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setChannelDTO(channelDTO);

        assertFalse(processor.isEnable(ctx));
    }

    /**
     * 测试场景：正常情况
     */
    @Test
    public void testProcessNormal() {
        InterestCalculateResponse interestResp = new InterestCalculateResponse();
        interestResp.setResult(true);
        when(interestWrapper.getFutureResult(future)).thenReturn(interestResp);

        DigestConfinementVrDTO vrInfo = new DigestConfinementVrDTO();
        vrInfo.setVrUrl("testUrl");
        when(digestQueryWrapper.getVRInfo(any(), any())).thenReturn(vrInfo);

        processor.process(ctx);
        assertTrue(ctx.getVrUrl().contains("testUrl"));
    }


}
