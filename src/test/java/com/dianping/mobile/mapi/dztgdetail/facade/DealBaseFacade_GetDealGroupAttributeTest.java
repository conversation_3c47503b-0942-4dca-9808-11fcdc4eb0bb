package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;
import java.util.concurrent.Future;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.*;

public class DealBaseFacade_GetDealGroupAttributeTest {

    @InjectMocks
    private DealBaseFacade dealBaseFacade;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetDealGroupAttributeServiceFutureIsNull() throws Exception {
        assertNull(dealBaseFacade.getDealGroupAttribute(1, null));
    }

    @Test
    public void testGetDealGroupAttributeResultIsNull() throws Exception {
        when(dealGroupWrapper.getDealGroupAttrs(any(Future.class))).thenReturn(null);
        assertNull(dealBaseFacade.getDealGroupAttribute(1, mock(Future.class)));
    }

    @Test
    public void testGetDealGroupAttributeResultNotContainsKey() throws Exception {
        Map<Integer, List<AttributeDTO>> result = new HashMap<>();
        result.put(2, Collections.emptyList());
        when(dealGroupWrapper.getDealGroupAttrs(any(Future.class))).thenReturn(result);
        assertNull(dealBaseFacade.getDealGroupAttribute(1, mock(Future.class)));
    }

    @Test
    public void testGetDealGroupAttributeResultContainsKey() throws Exception {
        List<AttributeDTO> expected = Collections.singletonList(new AttributeDTO());
        Map<Integer, List<AttributeDTO>> result = new HashMap<>();
        result.put(1, expected);
        when(dealGroupWrapper.getDealGroupAttrs(any(Future.class))).thenReturn(result);
        assertSame(expected, dealBaseFacade.getDealGroupAttribute(1, mock(Future.class)));
    }

    @Test
    public void testGetDealGroupAttributeExceptionOccurred() throws Exception {
        when(dealGroupWrapper.getDealGroupAttrs(any(Future.class))).thenThrow(new RuntimeException());
        assertNull(dealBaseFacade.getDealGroupAttribute(1, mock(Future.class)));
    }
}
