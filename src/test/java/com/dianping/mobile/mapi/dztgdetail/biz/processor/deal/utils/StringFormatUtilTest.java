package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils;

import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/11/12 10:40
 */
public class StringFormatUtilTest {

    @Test
    public void test() {
        String sourStr = "imeituan://www.meituan.com/mrn?mrn_biz=gcbu&mrn_entry=mrn-medical-healthcheck-item-add&mrn_component=healthcheck-item-add&shopId={shopId}&platform={platform}&productId={productId}&cityId={cityId}&productType=1&feeType=2&additionalBizScene=1&shelfSceneCode=physical_examination_addingitem_shelf&userLng={userLng}&userLat={userLat}&gpsCoordinateType={gpsCoordinateType}";
        Map<String, Object> map = new HashMap<>();
        map.put("platform", 2);
        map.put("shopId", 123456);
        map.put("productId", 123456);
        map.put("cityId", 1);
        map.put("userLat", String.format("%.7f", 120.3));
        map.put("userLng", String.format("%.7f", 30.4));
        map.put("gpsCoordinateType", 1);
        map.put("shelfSceneCode", "shelfSceneCode");
        map.put("categoryId", 401);
        String format = StringFormatUtil.format(sourStr, map);
        assert !StringUtils.isEmpty(format);
    }

    @Test
    public void testException() {
        // 没有复现
        String sourStr = "imeituan://www.meituan.com/mrn?mrn_biz=gcbu&\n"
                + "mrn_entry=mrn-play-bath-addprodetail&mrn_component=bath-addprodetail&shopId={shopId}&platform={platform}&productId={productId}&cityId={cityId}&productType=1&shelfSceneCode={shelfSceneCode}&userLng={userLng}\n"
                + "&userLat={userLat}&gpsCoordinateType={gpsCoordinateType}&categoryId={categoryId}";
        Map<String, Object> map = new HashMap<>();
        map.put("platform", 2);
        map.put("shopId", 1773570383);
        map.put("productId", 848480871);
        map.put("cityId", 139);
        map.put("userLat", String.format("%.7f", 40.7839331));
        map.put("userLng", String.format("%.7f", 111.5400931));
        map.put("gpsCoordinateType", "GCJ02");
        map.put("shelfSceneCode", "entertainment_bath_addingitem_shelf");
        map.put("categoryId", 304);
        String format = StringFormatUtil.format(sourStr, map);
        assert !StringUtils.isEmpty(format);
    }

    @Test
    public void testException2() {
        // 没有复现
        String sourStr = "imeituan://www.meituan.com/mrn?mrn_biz=gcbu&mrn_entry=mrn-play-bath-addprodetail&mrn_component=bath-addprodetail&shopId={shopId}&platform={platform}&productId={productId}&cityId={cityId}&productType=1&shelfSceneCode={shelfSceneCode}&userLng={userLng}&userLat={userLat}&gpsCoordinateType={gpsCoordinateType}&categoryId={categoryId},\n";
        Map<String, Object> map = new HashMap<>();
        map.put("platform", 2);
        map.put("shopId", 1773570383);
        map.put("productId", 848480871);
        map.put("cityId", 139);
        map.put("userLat", String.format("%.7f", 40.7839331));
        map.put("userLng", String.format("%.7f", 120.1877212));
        map.put("gpsCoordinateType", "GCJ02");
        map.put("shelfSceneCode", "entertainment_bath_addingitem_shelf");
        map.put("categoryId", 304);
        String format = StringFormatUtil.format(sourStr, map);
        assert !StringUtils.isEmpty(format);
    }

}