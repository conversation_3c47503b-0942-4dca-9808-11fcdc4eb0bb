/*
package com.dianping.mobile.mapi.dztgdetail;

import com.alibaba.fastjson.JSON;
import com.sankuai.trade.general.reserve.response.ReserveResponse;
import com.sankuai.trade.general.reserve.service.ShopAndProductReserveStatusService;
import org.junit.Test;

import javax.annotation.Resource;

*/
/**
 * <AUTHOR>
 * @date 2021-04-15-4:43 下午
 *//*

public class OnlineReservationTagTest extends GenericTest {
    @Resource
    private ShopAndProductReserveStatusService shopAndProductReserveStatusService;

    @Test
    public void testTagRes(){
        int dpGroupId = 405042921;
        ReserveResponse<Boolean> booleanReserveResponse = shopAndProductReserveStatusService.judgeProductOneOfShopReserve(dpGroupId);
        System.out.println(JSON.toJSONString(booleanReserveResponse));
    }
}
*/
