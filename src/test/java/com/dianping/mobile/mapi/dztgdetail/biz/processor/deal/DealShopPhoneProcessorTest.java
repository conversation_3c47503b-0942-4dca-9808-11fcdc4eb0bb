package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiPhoneWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

public class DealShopPhoneProcessorTest {

    private DealShopPhoneProcessor dealShopPhoneProcessor;

    @Mock
    private PoiPhoneWrapper poiPhoneWrapper;

    @Mock
    private DealCtx ctx;

    @Mock
    private FutureCtx futureCtx;

    @Mock
    private Future<?> poiPhoneFuture;

    private DealShopPhoneProcessor processor = new DealShopPhoneProcessor();

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        dealShopPhoneProcessor = new DealShopPhoneProcessor();
        // Use reflection to set the private field
        Field field = DealShopPhoneProcessor.class.getDeclaredField("poiPhoneWrapper");
        field.setAccessible(true);
        field.set(dealShopPhoneProcessor, poiPhoneWrapper);
    }

    /**
     * 使用反射调用私有方法
     */
    private boolean invokeIsValidPhNo(String ph) throws Exception {
        Method method = DealShopPhoneProcessor.class.getDeclaredMethod("isValidPhNo", String.class);
        method.setAccessible(true);
        return (boolean) method.invoke(processor, ph);
    }

    /**
     * 测试正常场景：ctx 不为 null，且 poiPhoneWrapper.preparePoiPhone(ctx) 返回一个非 null 的 Future 对象。
     */
    @Test
    public void testPrepareNormalCase() throws Throwable {
        // arrange
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
        when(poiPhoneWrapper.preparePoiPhone(ctx)).thenReturn(poiPhoneFuture);
        // act
        dealShopPhoneProcessor.prepare(ctx);
        // assert
        verify(poiPhoneWrapper).preparePoiPhone(ctx);
        verify(futureCtx).setPoiPhoneFuture(poiPhoneFuture);
    }

    /**
     * 测试异常场景：ctx.getFutureCtx() 返回 null，调用 setPoiPhoneFuture 会抛出 NullPointerException。
     */
    @Test(expected = NullPointerException.class)
    public void testPrepareFutureCtxIsNull() throws Throwable {
        // arrange
        when(ctx.getFutureCtx()).thenReturn(null);
        when(poiPhoneWrapper.preparePoiPhone(ctx)).thenReturn(poiPhoneFuture);
        // act
        dealShopPhoneProcessor.prepare(ctx);
        // assert
        // 预期抛出 NullPointerException
    }

    /**
     * 测试空值输入
     */
    @Test
    public void testIsValidPhNo_NullInput() throws Throwable {
        // arrange
        String ph = null;
        // act
        boolean result = invokeIsValidPhNo(ph);
        // assert
        assertFalse(result);
    }

    /**
     * 测试空字符串输入
     */
    @Test
    public void testIsValidPhNo_EmptyString() throws Throwable {
        // arrange
        String ph = "";
        // act
        boolean result = invokeIsValidPhNo(ph);
        // assert
        assertFalse(result);
    }

    /**
     * 测试空白字符串输入
     */
    @Test
    public void testIsValidPhNo_BlankString() throws Throwable {
        // arrange
        String ph = "   ";
        // act
        boolean result = invokeIsValidPhNo(ph);
        // assert
        assertFalse(result);
    }

    /**
     * 测试有效电话号码（带分隔符）
     */
    @Test
    public void testIsValidPhNo_ValidPhoneWithSeparator() throws Throwable {
        // arrange
        String ph = "123-456789";
        // act
        boolean result = invokeIsValidPhNo(ph);
        // assert
        assertTrue(result);
    }

    /**
     * 测试有效电话号码（无分隔符）
     */
    @Test
    public void testIsValidPhNo_ValidPhoneWithoutSeparator() throws Throwable {
        // arrange
        String ph = "123456789";
        // act
        boolean result = invokeIsValidPhNo(ph);
        // assert
        assertTrue(result);
    }

    /**
     * 测试无效电话号码（分隔符前数字不足）
     */
    @Test
    public void testIsValidPhNo_InvalidPhoneShortBeforeSeparator() throws Throwable {
        // arrange
        String ph = "0-554935";
        // act
        boolean result = invokeIsValidPhNo(ph);
        // assert
        assertFalse(result);
    }

    /**
     * 测试无效电话号码（分隔符后数字不足）
     */
    @Test
    public void testIsValidPhNo_InvalidPhoneShortAfterSeparator() throws Throwable {
        // arrange
        String ph = "123-456";
        // act
        boolean result = invokeIsValidPhNo(ph);
        // assert
        assertFalse(result);
    }

    /**
     * 测试无效电话号码（无分隔符且数字不足）
     */
    @Test
    public void testIsValidPhNo_InvalidPhoneShortWithoutSeparator() throws Throwable {
        // arrange
        String ph = "1234";
        // act
        boolean result = invokeIsValidPhNo(ph);
        // assert
        assertFalse(result);
    }
}
