package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ShopPBO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class DealCompareHelper_DifferShopInfoTest {

    /**
     * 测试新旧ShopPBO对象都为null的情况
     */
    @Test
    public void testDifferShopInfoBothNull() throws Throwable {
        // arrange
        ShopPBO newShopInfo = null;
        ShopPBO oldShopInfo = null;
        int dealId = 1;
        // act
        boolean result = DealCompareHelper.differShopInfo(newShopInfo, oldShopInfo, dealId);
        // assert
        assertTrue(result);
    }

    /**
     * 测试新ShopPBO对象为null，旧ShopPBO对象不为null的情况
     */
    @Test
    public void testDifferShopInfoNewNull() throws Throwable {
        // arrange
        ShopPBO newShopInfo = null;
        ShopPBO oldShopInfo = new ShopPBO();
        int dealId = 1;
        // act
        boolean result = DealCompareHelper.differShopInfo(newShopInfo, oldShopInfo, dealId);
        // assert
        assertFalse(result);
    }

    /**
     * 测试新ShopPBO对象不为null，旧ShopPBO对象为null的情况
     */
    @Test
    public void testDifferShopInfoOldNull() throws Throwable {
        // arrange
        ShopPBO newShopInfo = new ShopPBO();
        ShopPBO oldShopInfo = null;
        int dealId = 1;
        // act
        boolean result = DealCompareHelper.differShopInfo(newShopInfo, oldShopInfo, dealId);
        // assert
        assertFalse(result);
    }

    /**
     * 测试新旧ShopPBO对象都不为null，但它们的某些字段不同的情况
     */
    @Test
    public void testDifferShopInfoFieldsDifferent() throws Throwable {
        // arrange
        ShopPBO newShopInfo = new ShopPBO();
        newShopInfo.setShopId(1L);
        ShopPBO oldShopInfo = new ShopPBO();
        oldShopInfo.setShopId(2L);
        int dealId = 1;
        // act
        boolean result = DealCompareHelper.differShopInfo(newShopInfo, oldShopInfo, dealId);
        // assert
        assertFalse(result);
    }

    /**
     * 测试新旧ShopPBO对象都不为null，且它们的所有字段都相同的情况
     */
    @Test
    public void testDifferShopInfoFieldsSame() throws Throwable {
        // arrange
        ShopPBO newShopInfo = new ShopPBO();
        newShopInfo.setShopId(1L);
        ShopPBO oldShopInfo = new ShopPBO();
        oldShopInfo.setShopId(1L);
        int dealId = 1;
        // act
        boolean result = DealCompareHelper.differShopInfo(newShopInfo, oldShopInfo, dealId);
        // assert
        assertTrue(result);
    }
}
