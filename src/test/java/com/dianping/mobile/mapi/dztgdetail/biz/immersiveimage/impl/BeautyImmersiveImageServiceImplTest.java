package com.dianping.mobile.mapi.dztgdetail.biz.immersiveimage.impl;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.QueryCenterProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ImmersiveImageWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.RecommendServiceWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ShopTagWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ExhibitImageSceneEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ShopCategoryEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageVO;
import com.dianping.mobile.mapi.dztgdetail.entity.QueryExhibitImageParam;
import com.dianping.mobile.mapi.dztgdetail.entity.QueryRecommendParam;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import org.apache.thrift.TException;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BeautyImmersiveImageServiceImplTest {

    @InjectMocks
    private BeautyImmersiveImageServiceImpl beautyImmersiveImageService;

    @Mock
    private ImmersiveImageWrapper immersiveImageWrapper;

    @Mock
    private RecommendServiceWrapper recommendServiceWrapper;

    @Mock
    private DouHuBiz douHuBiz;

    @Mock
    private ShopTagWrapper shopTagWrapper;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;


    private MockedStatic<QueryCenterProcessor> queryCenterProcessorMockedStatic;

    private MockedStatic<DealUtils> dealUtilsMockedStatic;


    @Before
    public void setUp() {
        queryCenterProcessorMockedStatic = mockStatic(QueryCenterProcessor.class);
        dealUtilsMockedStatic = mockStatic(DealUtils.class);
    }

    @After
    public void tearDown() {
        queryCenterProcessorMockedStatic.close();
        dealUtilsMockedStatic.close();
    }

    /**
     * 测试 getImmersiveImage 方法，场景代码为 RECOMMEND，对照组
     */
    @Test
    public void testGetImmersiveImageRecommendControlGroup() {
        GetImmersiveImageRequest request = new GetImmersiveImageRequest();
        request.setSceneCode(ExhibitImageSceneEnum.RECOMMEND.getSceneCode());
        request.setDealGroupId(1);
        EnvCtx envCtx = new EnvCtx();

        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("b");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));

        when(douHuBiz.getAbByUnionId(any(), any(), anyBoolean())).thenReturn(moduleAbConfig);
        ImmersiveImageVO result = beautyImmersiveImageService.getImmersiveImage(request, envCtx);

        assertNotNull(result);
    }

    /**
     * 测试 getImmersiveImage 方法，场景代码为 RECOMMEND，实验组
     */
    @Test
    public void testGetImmersiveImageRecommendExperimentGroup() throws TException {
        GetImmersiveImageRequest request = new GetImmersiveImageRequest();
        request.setSceneCode(ExhibitImageSceneEnum.RECOMMEND.getSceneCode());
        request.setDealGroupId(1);
        EnvCtx envCtx = new EnvCtx();

        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("a");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));

        when(douHuBiz.getAbByUnionId(any(), anyString(), anyBoolean())).thenReturn(moduleAbConfig);
        when(recommendServiceWrapper.getRecommendStyleImage(any())).thenReturn(new ImmersiveImageVO());
        Future mockFuture = mock(Future.class);
        when(queryCenterWrapper.preDealGroupDTO(any())).thenReturn(mock(Future.class));
        ImmersiveImageVO result = beautyImmersiveImageService.getImmersiveImage(request, envCtx);

        assertNotNull(result);
    }

    /**
     * 测试 getImmersiveImage 方法，场景代码为 ORDER，对照组
     */
    @Test
    public void testGetImmersiveImageOrderControlGroup() throws TException {
        GetImmersiveImageRequest request = new GetImmersiveImageRequest();
        request.setSceneCode(ExhibitImageSceneEnum.ORDER.getSceneCode());
        request.setDealGroupId(1);
        EnvCtx envCtx = new EnvCtx();

        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("b");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));

        when(douHuBiz.getAbByUnionId(any(), anyString(), anyBoolean())).thenReturn(moduleAbConfig);
        Future mockFuture = mock(Future.class);

        ImmersiveImageVO result = beautyImmersiveImageService.getImmersiveImage(request, envCtx);

        assertNotNull(result);
    }

    /**
     * 测试 getImmersiveImage 方法，场景代码为 ORDER，实验组
     */
    @Test
    public void testGetImmersiveImageOrderExperimentGroup() throws TException {
        GetImmersiveImageRequest request = new GetImmersiveImageRequest();
        request.setSceneCode(ExhibitImageSceneEnum.ORDER.getSceneCode());
        request.setDealGroupId(1);
        EnvCtx envCtx = new EnvCtx();

        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("a");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));

        when(douHuBiz.getAbByUnionId(any(), anyString(), anyBoolean())).thenReturn(moduleAbConfig);

        queryCenterProcessorMockedStatic.when(QueryCenterProcessor::getQueryCenterDealGroupAttrKey)
                .thenReturn(Sets.newHashSet("styleId"));
        when(queryCenterWrapper.preDealGroupDTO(any())).thenReturn(mock(Future.class));

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(502L);
        categoryDTO.setServiceType("美甲");
        categoryDTO.setServiceTypeId(1L);
        dealGroupDTO.setCategory(categoryDTO);
        dealGroupDTO.setDeals(Lists.newArrayList(new DealGroupDealDTO()));

        when(queryCenterWrapper.getDealGroupDTO(any(Future.class))).thenReturn(dealGroupDTO);

        when(immersiveImageWrapper.getImmersiveImage(any(), any())).thenReturn(new ImmersiveImageVO());

        ImmersiveImageVO result = beautyImmersiveImageService.getImmersiveImage(request, envCtx);

        assertNotNull(result);
    }

    /**
     * 测试 getImmersiveImage 方法，场景代码为 SELF，对照组
     */
    @Test
    public void testGetImmersiveImageSelfControlGroup() throws TException {
        GetImmersiveImageRequest request = new GetImmersiveImageRequest();
        request.setSceneCode(ExhibitImageSceneEnum.SELF.getSceneCode());
        request.setDealGroupId(1);
        EnvCtx envCtx = new EnvCtx();

        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("b");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));

        when(douHuBiz.getAbByUnionId(any(), anyString(), anyBoolean())).thenReturn(moduleAbConfig);

        queryCenterProcessorMockedStatic.when(QueryCenterProcessor::getQueryCenterDealGroupAttrKey)
                .thenReturn(Sets.newHashSet("styleId"));
        when(queryCenterWrapper.preDealGroupDTO(any())).thenReturn(mock(Future.class));

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(502L);
        categoryDTO.setServiceType("美甲");
        categoryDTO.setServiceTypeId(1L);
        dealGroupDTO.setCategory(categoryDTO);
        dealGroupDTO.setDeals(Lists.newArrayList(new DealGroupDealDTO()));

        when(queryCenterWrapper.getDealGroupDTO(any(Future.class))).thenReturn(dealGroupDTO);

        when(immersiveImageWrapper.getImmersiveImage(any(QueryExhibitImageParam.class), any(EnvCtx.class)))
                .thenReturn(new ImmersiveImageVO());
        ImmersiveImageVO result = beautyImmersiveImageService.getImmersiveImage(request, envCtx);

        assertNotNull(result);
    }

    /**
     * 测试 getImmersiveImage 方法，场景代码为 SELF，实验组
     */
    @Test
    public void testGetImmersiveImageSelfExperimentGroup() throws TException {
        GetImmersiveImageRequest request = new GetImmersiveImageRequest();
        request.setSceneCode(ExhibitImageSceneEnum.SELF.getSceneCode());
        request.setDealGroupId(1);
        request.setMtDealGroupId(1L);
        request.setDpDealGroupId(1L);
        EnvCtx envCtx = new EnvCtx();

        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("a");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));

        when(douHuBiz.getAbByUnionId(any(), anyString(), anyBoolean())).thenReturn(moduleAbConfig);
        queryCenterProcessorMockedStatic.when(QueryCenterProcessor::getQueryCenterDealGroupAttrKey)
                .thenReturn(Sets.newHashSet("styleId"));
        when(queryCenterWrapper.preDealGroupDTO(any())).thenReturn(mock(Future.class));

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(502L);
        categoryDTO.setServiceType("穿戴甲");
        categoryDTO.setServiceTypeId(1L);
        dealGroupDTO.setCategory(categoryDTO);
        dealGroupDTO.setDeals(Lists.newArrayList(new DealGroupDealDTO()));

        when(queryCenterWrapper.getDealGroupDTO(any(Future.class))).thenReturn(dealGroupDTO);

        when(shopTagWrapper.getWearableNailShopTag(any())).thenReturn(ShopCategoryEnum.UNKNOWN);

        dealUtilsMockedStatic.when(() -> DealUtils.isNewWearableNailDeal(any(DealGroupDTO.class))).thenReturn(true);
        when(immersiveImageWrapper.getImmersiveImageWithDeals(any(List.class),
                any(QueryExhibitImageParam.class),
                any(EnvCtx.class), any(), any()))
                .thenReturn(new ImmersiveImageVO());

        ImmersiveImageVO result = beautyImmersiveImageService.getImmersiveImage(request, envCtx);

        assertNotNull(result);
    }

    /**
     * 测试 getImmersiveImage 方法，场景代码为其他值，对照组
     */
    @Test
    public void testGetImmersiveImageOtherControlGroup() throws TException {
        GetImmersiveImageRequest request = new GetImmersiveImageRequest();
        request.setSceneCode("OTHER");
        request.setDealGroupId(1);
        EnvCtx envCtx = new EnvCtx();

        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("b");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));


        ImmersiveImageVO result = beautyImmersiveImageService.getImmersiveImage(request, envCtx);
        assertNull(result);
    }

    /**
     * 测试 getImmersiveImage 方法，场景代码为推荐，实验组
     */
    @Test
    public void testGetImmersiveImageRecommendExperimentalGroup() throws TException {
        GetImmersiveImageRequest request = new GetImmersiveImageRequest();
        request.setSceneCode(ExhibitImageSceneEnum.RECOMMEND.getSceneCode());
        request.setDealGroupId(1);

        EnvCtx envCtx = new EnvCtx();
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("a");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        when(douHuBiz.getAbByUnionId(any(), anyString(), anyBoolean())).thenReturn(moduleAbConfig);
        when(queryCenterWrapper.preDealGroupDTO(any())).thenReturn(mock(Future.class));
        when(recommendServiceWrapper.getRecommendStyleImage(any(QueryRecommendParam.class)))
                .thenReturn(new ImmersiveImageVO());
        ImmersiveImageVO result = beautyImmersiveImageService.getImmersiveImage(request, envCtx);
        assertNotNull(result);
    }


    /**
     * 测试 getImmersiveImage 方法，场景代码为订单详情页，实验组
     */
    @Test
    public void testGetImmersiveImageOrderDetailExperimentalGroup() throws TException {
        GetImmersiveImageRequest request = new GetImmersiveImageRequest();
        request.setSceneCode(ExhibitImageSceneEnum.ORDER.getSceneCode());
        request.setDealGroupId(1);
        EnvCtx envCtx = new EnvCtx();
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("a");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));

        when(douHuBiz.getAbByUnionId(any(), anyString(), anyBoolean())).thenReturn(moduleAbConfig);

        queryCenterProcessorMockedStatic.when(QueryCenterProcessor::getQueryCenterDealGroupAttrKey)
                .thenReturn(Sets.newHashSet("styleId"));
        when(queryCenterWrapper.preDealGroupDTO(any())).thenReturn(mock(Future.class));

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(502L);
        categoryDTO.setServiceType("美甲");
        categoryDTO.setServiceTypeId(1L);
        dealGroupDTO.setCategory(categoryDTO);
        dealGroupDTO.setDeals(Lists.newArrayList(new DealGroupDealDTO()));

        when(queryCenterWrapper.getDealGroupDTO(any(Future.class))).thenReturn(dealGroupDTO);

        when(immersiveImageWrapper.getImmersiveImage(any(), any())).thenReturn(new ImmersiveImageVO());
        ImmersiveImageVO result = beautyImmersiveImageService.getImmersiveImage(request, envCtx);
        assertNotNull(result);
    }

    /**
     * 测试 getImmersiveImage 方法，场景代码为订单详情页，对照组
     */
    @Test
    public void testGetImmersiveImageOrderDetailControlGroup() throws TException {
        GetImmersiveImageRequest request = new GetImmersiveImageRequest();
        request.setSceneCode(ExhibitImageSceneEnum.ORDER.getSceneCode());
        request.setDealGroupId(1);
        EnvCtx envCtx = new EnvCtx();
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("b");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));

        when(douHuBiz.getAbByUnionId(any(), anyString(), anyBoolean())).thenReturn(moduleAbConfig);
        Future mockFuture = mock(Future.class);

        ImmersiveImageVO result = beautyImmersiveImageService.getImmersiveImage(request, envCtx);
        assertNotNull(result);
    }

    @Test
    public void testGetImmersiveImage() throws TException {
        // 创建参数对象
        GetImmersiveImageRequest request = new GetImmersiveImageRequest();
        request.setDealGroupId(1);
        request.setSceneCode(ExhibitImageSceneEnum.SELF.getSceneCode());
        EnvCtx envCtx = new EnvCtx();

        // 创建返回的ImmersiveImageVO对象
        ImmersiveImageVO immersiveImageVO = new ImmersiveImageVO();
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("a");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));

        when(douHuBiz.getAbByUnionId(any(), anyString(), anyBoolean())).thenReturn(moduleAbConfig);

        queryCenterProcessorMockedStatic.when(QueryCenterProcessor::getQueryCenterDealGroupAttrKey)
                .thenReturn(Sets.newHashSet("styleId"));
        when(queryCenterWrapper.preDealGroupDTO(any())).thenReturn(mock(Future.class));

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(502L);
        categoryDTO.setServiceType("美甲");
        categoryDTO.setServiceTypeId(1L);
        dealGroupDTO.setCategory(categoryDTO);
        dealGroupDTO.setDeals(Lists.newArrayList(new DealGroupDealDTO()));

        when(queryCenterWrapper.getDealGroupDTO(any(Future.class))).thenReturn(dealGroupDTO);

        // 设置mock对象的行为
        when(immersiveImageWrapper.getImmersiveImage(any(), any())).thenReturn(immersiveImageVO);

        // 调用被测试方法
        ImmersiveImageVO result = beautyImmersiveImageService.getImmersiveImage(request, envCtx);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getModuleAbConfigs().size());
        assertEquals(moduleAbConfig, result.getModuleAbConfigs().get(0));
    }

    /**
     * 测试 getFirstAttrValFromAttr 方法，当 dealGroup 为 null 时
     */
    @Test
    public void testGetFirstAttrValFromAttrWhenDealGroupIsNull() {
        String result = beautyImmersiveImageService.getFirstAttrValFromAttr(null, "attrName");
        assertNull("Result should be null when dealGroup is null", result);
    }

    /**
     * 测试 getFirstAttrValFromAttr 方法，当 attrs 为空时
     */
    @Test
    public void testGetFirstAttrValFromAttrWhenAttrsIsEmpty() {
        DealGroupDTO dealGroup = Mockito.mock(DealGroupDTO.class);
        Mockito.when(dealGroup.getAttrs()).thenReturn(Collections.emptyList());

        String result = beautyImmersiveImageService.getFirstAttrValFromAttr(dealGroup, "attrName");
        assertNull("Result should be null when attrs is empty", result);
    }

    /**
     * 测试 getFirstAttrValFromAttr 方法，当 attrName 不存在时
     */
    @Test
    public void testGetFirstAttrValFromAttrWhenAttrNameNotExist() {
        DealGroupDTO dealGroup = Mockito.mock(DealGroupDTO.class);
        AttrDTO attr = new AttrDTO();
        attr.setName("otherName");
        attr.setValue(Arrays.asList("value1", "value2"));
        Mockito.when(dealGroup.getAttrs()).thenReturn(Collections.singletonList(attr));

        String result = beautyImmersiveImageService.getFirstAttrValFromAttr(dealGroup, "attrName");
        assertNull("Result should be null when attrName does not exist", result);
    }

    /**
     * 测试 getFirstAttrValFromAttr 方法，当 attrName 存在时
     */
    @Test
    public void testGetFirstAttrValFromAttrWhenAttrNameExist() {
        DealGroupDTO dealGroup = Mockito.mock(DealGroupDTO.class);
        AttrDTO attr = new AttrDTO();
        attr.setName("attrName");
        attr.setValue(Arrays.asList("value1", "value2"));
        Mockito.when(dealGroup.getAttrs()).thenReturn(Collections.singletonList(attr));

        String result = beautyImmersiveImageService.getFirstAttrValFromAttr(dealGroup, "attrName");
        assertEquals("Result should be 'value1' when attrName exists", "value1", result);
    }

    /**
     * 测试 getFirstAttrValFromAttr 方法，当 attrName 存在但没有值时
     */
    @Test
    public void testGetFirstAttrValFromAttrWhenAttrNameExistButNoValue() {
        DealGroupDTO dealGroup = Mockito.mock(DealGroupDTO.class);
        AttrDTO attr = new AttrDTO();
        attr.setName("attrName");
        attr.setValue(Collections.emptyList());
        Mockito.when(dealGroup.getAttrs()).thenReturn(Collections.singletonList(attr));

        String result = beautyImmersiveImageService.getFirstAttrValFromAttr(dealGroup, "attrName");
        assertNull("Result should be null when attrName exists but has no value", result);
    }
}
