package com.dianping.mobile.mapi.dztgdetail.util;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDisplayShopCheckDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupShopRelationCheckResultDTO;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;
import org.mockito.Mockito;

/**
 * Test cases for DealProductUtils.checkShopExist(DealGroupDTO, Long, boolean)
 */
public class DealProductUtilsCheckShopExistTest {

    /**
     * Case: dealGroupDTO.getDisplayShopCheckResult() returns null.
     * Should return false.
     */
    @Test
    public void testCheckShopExist_DisplayShopCheckResultNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(null);
        // act
        boolean result = DealProductUtils.checkShopExist(dealGroupDTO, 123L, true);
        // assert
        assertFalse(result);
    }

    /**
     * Case: mt=true, getMtDisplayShopCheckResult() returns null.
     * Should return false.
     */
    @Test
    public void testCheckShopExist_MtTrue_MtDisplayShopCheckResultNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDisplayShopCheckDTO displayShopCheckDTO = mock(DealGroupDisplayShopCheckDTO.class);
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        when(displayShopCheckDTO.getMtDisplayShopCheckResult()).thenReturn(null);
        // act
        boolean result = DealProductUtils.checkShopExist(dealGroupDTO, 123L, true);
        // assert
        assertFalse(result);
    }

    /**
     * Case: mt=false, getDpDisplayShopCheckResult() returns null.
     * Should return false.
     */
    @Test
    public void testCheckShopExist_MtFalse_DpDisplayShopCheckResultNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDisplayShopCheckDTO displayShopCheckDTO = mock(DealGroupDisplayShopCheckDTO.class);
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        when(displayShopCheckDTO.getDpDisplayShopCheckResult()).thenReturn(null);
        // act
        boolean result = DealProductUtils.checkShopExist(dealGroupDTO, 123L, false);
        // assert
        assertFalse(result);
    }

    /**
     * Case: mt=true, getMtDisplayShopCheckResult() returns empty list.
     * Should return false.
     */
    @Test
    public void testCheckShopExist_MtTrue_MtDisplayShopCheckResultEmpty() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDisplayShopCheckDTO displayShopCheckDTO = mock(DealGroupDisplayShopCheckDTO.class);
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        when(displayShopCheckDTO.getMtDisplayShopCheckResult()).thenReturn(Collections.emptyList());
        // act
        boolean result = DealProductUtils.checkShopExist(dealGroupDTO, 123L, true);
        // assert
        assertFalse(result);
    }

    /**
     * Case: mt=false, getDpDisplayShopCheckResult() returns empty list.
     * Should return false.
     */
    @Test
    public void testCheckShopExist_MtFalse_DpDisplayShopCheckResultEmpty() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDisplayShopCheckDTO displayShopCheckDTO = mock(DealGroupDisplayShopCheckDTO.class);
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        when(displayShopCheckDTO.getDpDisplayShopCheckResult()).thenReturn(Collections.emptyList());
        // act
        boolean result = DealProductUtils.checkShopExist(dealGroupDTO, 123L, false);
        // assert
        assertFalse(result);
    }

    /**
     * Case: mt=true, getMtDisplayShopCheckResult() returns list with only null elements.
     * Should return false.
     */
    @Test
    public void testCheckShopExist_MtTrue_MtDisplayShopCheckResultAllNulls() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDisplayShopCheckDTO displayShopCheckDTO = mock(DealGroupDisplayShopCheckDTO.class);
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        List<DealGroupShopRelationCheckResultDTO> list = Arrays.asList(null, null);
        when(displayShopCheckDTO.getMtDisplayShopCheckResult()).thenReturn(list);
        // act
        boolean result = DealProductUtils.checkShopExist(dealGroupDTO, 123L, true);
        // assert
        assertFalse(result);
    }

    /**
     * Case: mt=false, getDpDisplayShopCheckResult() returns list with only null elements.
     * Should return false.
     */
    @Test
    public void testCheckShopExist_MtFalse_DpDisplayShopCheckResultAllNulls() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDisplayShopCheckDTO displayShopCheckDTO = mock(DealGroupDisplayShopCheckDTO.class);
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        List<DealGroupShopRelationCheckResultDTO> list = Arrays.asList(null, null);
        when(displayShopCheckDTO.getDpDisplayShopCheckResult()).thenReturn(list);
        // act
        boolean result = DealProductUtils.checkShopExist(dealGroupDTO, 123L, false);
        // assert
        assertFalse(result);
    }

    /**
     * Case: mt=true, getMtDisplayShopCheckResult() returns list with one element, but relationCheckSuccess is not TRUE.
     * Should return false.
     */
    @Test
    public void testCheckShopExist_MtTrue_MtDisplayShopCheckResultNotSuccess() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDisplayShopCheckDTO displayShopCheckDTO = mock(DealGroupDisplayShopCheckDTO.class);
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        DealGroupShopRelationCheckResultDTO dto = mock(DealGroupShopRelationCheckResultDTO.class);
        when(dto.getRelationCheckSuccess()).thenReturn(Boolean.FALSE);
        when(dto.getShopId()).thenReturn(123L);
        List<DealGroupShopRelationCheckResultDTO> list = Collections.singletonList(dto);
        when(displayShopCheckDTO.getMtDisplayShopCheckResult()).thenReturn(list);
        // act
        boolean result = DealProductUtils.checkShopExist(dealGroupDTO, 123L, true);
        // assert
        assertFalse(result);
    }

    /**
     * Case: mt=false, getDpDisplayShopCheckResult() returns list with one element, but relationCheckSuccess is not TRUE.
     * Should return false.
     */
    @Test
    public void testCheckShopExist_MtFalse_DpDisplayShopCheckResultNotSuccess() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDisplayShopCheckDTO displayShopCheckDTO = mock(DealGroupDisplayShopCheckDTO.class);
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        DealGroupShopRelationCheckResultDTO dto = mock(DealGroupShopRelationCheckResultDTO.class);
        // not TRUE
        when(dto.getRelationCheckSuccess()).thenReturn(null);
        when(dto.getShopId()).thenReturn(123L);
        List<DealGroupShopRelationCheckResultDTO> list = Collections.singletonList(dto);
        when(displayShopCheckDTO.getDpDisplayShopCheckResult()).thenReturn(list);
        // act
        boolean result = DealProductUtils.checkShopExist(dealGroupDTO, 123L, false);
        // assert
        assertFalse(result);
    }

    /**
     * Case: mt=true, getMtDisplayShopCheckResult() returns list with one element, relationCheckSuccess is TRUE, but shopId does not match.
     * Should return false.
     */
    @Test
    public void testCheckShopExist_MtTrue_MtDisplayShopCheckResultShopIdNotMatch() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDisplayShopCheckDTO displayShopCheckDTO = mock(DealGroupDisplayShopCheckDTO.class);
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        DealGroupShopRelationCheckResultDTO dto = mock(DealGroupShopRelationCheckResultDTO.class);
        when(dto.getRelationCheckSuccess()).thenReturn(Boolean.TRUE);
        when(dto.getShopId()).thenReturn(999L);
        List<DealGroupShopRelationCheckResultDTO> list = Collections.singletonList(dto);
        when(displayShopCheckDTO.getMtDisplayShopCheckResult()).thenReturn(list);
        // act
        boolean result = DealProductUtils.checkShopExist(dealGroupDTO, 123L, true);
        // assert
        assertFalse(result);
    }

    /**
     * Case: mt=false, getDpDisplayShopCheckResult() returns list with one element, relationCheckSuccess is TRUE, but shopId does not match.
     * Should return false.
     */
    @Test
    public void testCheckShopExist_MtFalse_DpDisplayShopCheckResultShopIdNotMatch() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDisplayShopCheckDTO displayShopCheckDTO = mock(DealGroupDisplayShopCheckDTO.class);
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        DealGroupShopRelationCheckResultDTO dto = mock(DealGroupShopRelationCheckResultDTO.class);
        when(dto.getRelationCheckSuccess()).thenReturn(Boolean.TRUE);
        when(dto.getShopId()).thenReturn(999L);
        List<DealGroupShopRelationCheckResultDTO> list = Collections.singletonList(dto);
        when(displayShopCheckDTO.getDpDisplayShopCheckResult()).thenReturn(list);
        // act
        boolean result = DealProductUtils.checkShopExist(dealGroupDTO, 123L, false);
        // assert
        assertFalse(result);
    }

    /**
     * Case: mt=true, getMtDisplayShopCheckResult() returns list with one element, relationCheckSuccess is TRUE, shopId matches.
     * Should return true.
     */
    @Test
    public void testCheckShopExist_MtTrue_MtDisplayShopCheckResultMatch() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDisplayShopCheckDTO displayShopCheckDTO = mock(DealGroupDisplayShopCheckDTO.class);
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        DealGroupShopRelationCheckResultDTO dto = mock(DealGroupShopRelationCheckResultDTO.class);
        when(dto.getRelationCheckSuccess()).thenReturn(Boolean.TRUE);
        when(dto.getShopId()).thenReturn(123L);
        List<DealGroupShopRelationCheckResultDTO> list = Collections.singletonList(dto);
        when(displayShopCheckDTO.getMtDisplayShopCheckResult()).thenReturn(list);
        // act
        boolean result = DealProductUtils.checkShopExist(dealGroupDTO, 123L, true);
        // assert
        assertTrue(result);
    }

    /**
     * Case: mt=false, getDpDisplayShopCheckResult() returns list with one element, relationCheckSuccess is TRUE, shopId matches.
     * Should return true.
     */
    @Test
    public void testCheckShopExist_MtFalse_DpDisplayShopCheckResultMatch() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDisplayShopCheckDTO displayShopCheckDTO = mock(DealGroupDisplayShopCheckDTO.class);
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        DealGroupShopRelationCheckResultDTO dto = mock(DealGroupShopRelationCheckResultDTO.class);
        when(dto.getRelationCheckSuccess()).thenReturn(Boolean.TRUE);
        when(dto.getShopId()).thenReturn(123L);
        List<DealGroupShopRelationCheckResultDTO> list = Collections.singletonList(dto);
        when(displayShopCheckDTO.getDpDisplayShopCheckResult()).thenReturn(list);
        // act
        boolean result = DealProductUtils.checkShopExist(dealGroupDTO, 123L, false);
        // assert
        assertTrue(result);
    }

    /**
     * Case: mt=true, getMtDisplayShopCheckResult() returns list with multiple elements, only one matches (relationCheckSuccess TRUE and shopId matches).
     * Should return true.
     */
    @Test
    public void testCheckShopExist_MtTrue_MtDisplayShopCheckResultMultipleOneMatch() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDisplayShopCheckDTO displayShopCheckDTO = mock(DealGroupDisplayShopCheckDTO.class);
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        DealGroupShopRelationCheckResultDTO dto1 = mock(DealGroupShopRelationCheckResultDTO.class);
        when(dto1.getRelationCheckSuccess()).thenReturn(Boolean.FALSE);
        when(dto1.getShopId()).thenReturn(123L);
        DealGroupShopRelationCheckResultDTO dto2 = mock(DealGroupShopRelationCheckResultDTO.class);
        when(dto2.getRelationCheckSuccess()).thenReturn(Boolean.TRUE);
        when(dto2.getShopId()).thenReturn(999L);
        DealGroupShopRelationCheckResultDTO dto3 = mock(DealGroupShopRelationCheckResultDTO.class);
        when(dto3.getRelationCheckSuccess()).thenReturn(Boolean.TRUE);
        when(dto3.getShopId()).thenReturn(123L);
        List<DealGroupShopRelationCheckResultDTO> list = Arrays.asList(dto1, dto2, dto3);
        when(displayShopCheckDTO.getMtDisplayShopCheckResult()).thenReturn(list);
        // act
        boolean result = DealProductUtils.checkShopExist(dealGroupDTO, 123L, true);
        // assert
        assertTrue(result);
    }

    /**
     * Case: mt=false, getDpDisplayShopCheckResult() returns list with multiple elements, none matches.
     * Should return false.
     */
    @Test
    public void testCheckShopExist_MtFalse_DpDisplayShopCheckResultMultipleNoneMatch() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDisplayShopCheckDTO displayShopCheckDTO = mock(DealGroupDisplayShopCheckDTO.class);
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        DealGroupShopRelationCheckResultDTO dto1 = mock(DealGroupShopRelationCheckResultDTO.class);
        when(dto1.getRelationCheckSuccess()).thenReturn(Boolean.FALSE);
        when(dto1.getShopId()).thenReturn(123L);
        DealGroupShopRelationCheckResultDTO dto2 = mock(DealGroupShopRelationCheckResultDTO.class);
        when(dto2.getRelationCheckSuccess()).thenReturn(Boolean.TRUE);
        when(dto2.getShopId()).thenReturn(999L);
        List<DealGroupShopRelationCheckResultDTO> list = Arrays.asList(dto1, dto2);
        when(displayShopCheckDTO.getDpDisplayShopCheckResult()).thenReturn(list);
        // act
        boolean result = DealProductUtils.checkShopExist(dealGroupDTO, 888L, false);
        // assert
        assertFalse(result);
    }

    /**
     * Case: mt=true, getMtDisplayShopCheckResult() returns list with one element, relationCheckSuccess is TRUE, shopId is null, input shopId is also null.
     * Should return true.
     */
    @Test
    public void testCheckShopExist_MtTrue_MtDisplayShopCheckResultShopIdNullMatch() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDisplayShopCheckDTO displayShopCheckDTO = mock(DealGroupDisplayShopCheckDTO.class);
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        DealGroupShopRelationCheckResultDTO dto = mock(DealGroupShopRelationCheckResultDTO.class);
        when(dto.getRelationCheckSuccess()).thenReturn(Boolean.TRUE);
        when(dto.getShopId()).thenReturn(null);
        List<DealGroupShopRelationCheckResultDTO> list = Collections.singletonList(dto);
        when(displayShopCheckDTO.getMtDisplayShopCheckResult()).thenReturn(list);
        // act
        boolean result = DealProductUtils.checkShopExist(dealGroupDTO, null, true);
        // assert
        assertTrue(result);
    }

    /**
     * Case: mt=false, getDpDisplayShopCheckResult() returns list with one element, relationCheckSuccess is TRUE, shopId is null, input shopId is not null.
     * Should return false.
     */
    @Test
    public void testCheckShopExist_MtFalse_DpDisplayShopCheckResultShopIdNullNoMatch() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDisplayShopCheckDTO displayShopCheckDTO = mock(DealGroupDisplayShopCheckDTO.class);
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        DealGroupShopRelationCheckResultDTO dto = mock(DealGroupShopRelationCheckResultDTO.class);
        when(dto.getRelationCheckSuccess()).thenReturn(Boolean.TRUE);
        when(dto.getShopId()).thenReturn(null);
        List<DealGroupShopRelationCheckResultDTO> list = Collections.singletonList(dto);
        when(displayShopCheckDTO.getDpDisplayShopCheckResult()).thenReturn(list);
        // act
        boolean result = DealProductUtils.checkShopExist(dealGroupDTO, 123L, false);
        // assert
        assertFalse(result);
    }
}
