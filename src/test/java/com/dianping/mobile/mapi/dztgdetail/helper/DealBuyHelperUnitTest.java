package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * 类的描述
 *
 * @auther: liweilong06
 * @date: 2024/1/17 8:57 下午
 */

@RunWith(MockitoJUnitRunner.class)
public class DealBuyHelperUnitTest {

    private DealGroupDTO dealGroupDTO;
    private DealCtx ctx;
    private DealGroupCategoryDTO dealGroupCategoryDTO;
    private FutureCtx futureCtx;
    private DztgClientTypeEnum dztgClientTypeEnum;
    private EnvCtx envCtx;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        dztgClientTypeEnum = DztgClientTypeEnum.MEITUAN_APP;
        envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(dztgClientTypeEnum);
        ctx = new DealCtx(envCtx);
        dealGroupDTO = mock(DealGroupDTO.class);
        dealGroupCategoryDTO = mock(DealGroupCategoryDTO.class);
        futureCtx = mock(FutureCtx.class);
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.setFutureCtx(futureCtx);

    }

    @Test
    public void test_isEduOnlineCourseDeal_null() {
        MockedStatic<LionConfigUtils> utilities = Mockito.mockStatic(LionConfigUtils.class);
        utilities.when(() -> LionConfigUtils.isEduOnlineDeal(anyInt(), anyLong())).thenReturn(true);
        Assert.assertTrue(DealBuyHelper.isEduOnlineCourseDeal(null) == false);
        utilities.close();
    }

    @Test
    public void test_isEduOnlineCourseDeal_true() {
        MockedStatic<LionConfigUtils> utilities = Mockito.mockStatic(LionConfigUtils.class);
        utilities.when(() -> LionConfigUtils.isEduOnlineDeal(anyInt(), anyLong())).thenReturn(true);
        when(dealGroupDTO.getCategory()).thenReturn(dealGroupCategoryDTO);
        when(dealGroupCategoryDTO.getServiceTypeId()).thenReturn(1123L);
        Assert.assertTrue(DealBuyHelper.isEduOnlineCourseDeal(ctx));
        utilities.close();
    }

    @Test
    public void test_isEduOnlineCourseDeal_false() {
        MockedStatic<LionConfigUtils> utilities = Mockito.mockStatic(LionConfigUtils.class);
        utilities.when(() -> LionConfigUtils.isEduOnlineDeal(anyInt(),  anyLong())).thenReturn(false);
        Assert.assertTrue(DealBuyHelper.isEduOnlineCourseDeal(ctx) == false);
        utilities.close();
    }

}
