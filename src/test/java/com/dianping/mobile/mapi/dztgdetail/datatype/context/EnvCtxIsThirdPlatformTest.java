package com.dianping.mobile.mapi.dztgdetail.datatype.context;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

public class EnvCtxIsThirdPlatformTest {

    /**
     * 测试 isThirdPlatform 方法，当 dztgClientTypeEnum 等于 THIRD_PLATFORM 时，应返回 true
     */
    @Test
    public void testIsThirdPlatformWhenDztgClientTypeEnumIsThirdPlatform() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.THIRD_PLATFORM);
        // act
        boolean result = envCtx.isThirdPlatform();
        // assert
        assertTrue(result);
    }

    /**
     * 测试 isThirdPlatform 方法，当 dztgClientTypeEnum 不等于 THIRD_PLATFORM 时，应返回 false
     */
    @Test
    public void testIsThirdPlatformWhenDztgClientTypeEnumIsNotThirdPlatform() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        // Assuming MEITUAN_APP is a valid enum constant for demonstration. Replace with an actual constant as needed.
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        // act
        boolean result = envCtx.isThirdPlatform();
        // assert
        assertFalse(result);
    }
}
