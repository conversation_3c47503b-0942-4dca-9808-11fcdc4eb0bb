package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.general.product.query.center.client.dto.deal.rule.readjustPriceRule.standardPriceRule.StandardPriceRuleItemDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class MassageHighlightsV2Processor_GetNewFoodDataByServiceProjectAttrTest {

    private final MassageHighlightsV2Processor processor = new MassageHighlightsV2Processor();

    /**
     * Helper method to invoke the private method using reflection
     */
    private String invokePrivateMethod(List<StandardPriceRuleItemDTO> items) throws Exception {
        Method method = MassageHighlightsV2Processor.class.getDeclaredMethod("getOverNightValueByItems", List.class);
        method.setAccessible(true);
        return (String) method.invoke(processor, items);
    }

    private String invokePrivateMethod(List<ServiceProjectAttrDTO> attrs, String key) throws Exception {
        // Use reflection to access the private method
        Method method = MassageHighlightsV2Processor.class.getDeclaredMethod("getAttrValueByServiceProjectAttr", List.class, String.class);
        method.setAccessible(true);
        return (String) method.invoke(null, attrs, key);
    }

    /**
     * 测试serviceProjectAttrs为空的情况
     */
    @Test
    public void testGetNewFoodDataByServiceProjectAttr_EmptyList() {
        assertNull(MassageHighlightsV2Processor.getNewFoodDataByServiceProjectAttr(Collections.emptyList()));
    }

    /**
     * 测试serviceProjectAttrs中不存在"freeFood"属性的情况
     */
    @Test
    public void testGetNewFoodDataByServiceProjectAttr_NoFreeFood() {
        ServiceProjectAttrDTO attrDTO = new ServiceProjectAttrDTO();
        attrDTO.setAttrName("other");
        attrDTO.setAttrValue("other");
        assertNull(MassageHighlightsV2Processor.getNewFoodDataByServiceProjectAttr(Arrays.asList(attrDTO)));
    }

    /**
     * 测试serviceProjectAttrs中存在"freeFood"属性，但其值不在FOOD_NEW_DATA集合中的情况
     */
    @Test
    public void testGetNewFoodDataByServiceProjectAttr_FreeFoodNotInNewData() {
        ServiceProjectAttrDTO attrDTO = new ServiceProjectAttrDTO();
        attrDTO.setAttrName("freeFood");
        attrDTO.setAttrValue("notInNewData");
        assertNull(MassageHighlightsV2Processor.getNewFoodDataByServiceProjectAttr(Arrays.asList(attrDTO)));
    }

    /**
     * 测试serviceProjectAttrs中存在"freeFood"属性，其值是"茶点水果"，但列表中不存在"Fruit"属性的情况
     */
    @Test
    public void testGetNewFoodDataByServiceProjectAttr_FreeFoodIsTeaAndNoFruit() {
        ServiceProjectAttrDTO attrDTO = new ServiceProjectAttrDTO();
        attrDTO.setAttrName("freeFood");
        attrDTO.setAttrValue("茶点水果");
        assertEquals("茶点", MassageHighlightsV2Processor.getNewFoodDataByServiceProjectAttr(Arrays.asList(attrDTO)));
    }

    /**
     * 测试serviceProjectAttrs中存在"freeFood"属性，其值是"茶点水果"，且列表中存在"Fruit"属性的情况
     */
    @Test
    public void testGetNewFoodDataByServiceProjectAttr_FreeFoodIsTeaAndHasFruit() {
        ServiceProjectAttrDTO attrDTO1 = new ServiceProjectAttrDTO();
        attrDTO1.setAttrName("freeFood");
        attrDTO1.setAttrValue("茶点水果");
        ServiceProjectAttrDTO attrDTO2 = new ServiceProjectAttrDTO();
        attrDTO2.setAttrName("Fruit");
        attrDTO2.setAttrValue("Fruit");
        assertEquals("茶点水果", MassageHighlightsV2Processor.getNewFoodDataByServiceProjectAttr(Arrays.asList(attrDTO1, attrDTO2)));
    }

    /**
     * Test when input list is null
     */
    @Test
    public void testGetOverNightValueByItems_NullList() throws Throwable {
        // arrange
        List<StandardPriceRuleItemDTO> items = null;
        // act
        String result = invokePrivateMethod(items);
        // assert
        assertNull(result);
    }

    /**
     * Test when input list is empty
     */
    @Test
    public void testGetOverNightValueByItems_EmptyList() throws Throwable {
        // arrange
        List<StandardPriceRuleItemDTO> items = Collections.emptyList();
        // act
        String result = invokePrivateMethod(items);
        // assert
        assertNull(result);
    }

    /**
     * Test when list contains no matching item
     */
    @Test
    public void testGetOverNightValueByItems_NoMatch() throws Throwable {
        // arrange
        StandardPriceRuleItemDTO item = new StandardPriceRuleItemDTO();
        item.setIdentityKey("otherKey");
        List<StandardPriceRuleItemDTO> items = Collections.singletonList(item);
        // act
        String result = invokePrivateMethod(items);
        // assert
        assertNull(result);
    }

    /**
     * Test when list contains one matching item
     */
    @Test
    public void testGetOverNightValueByItems_SingleMatch() throws Throwable {
        // arrange
        StandardPriceRuleItemDTO item = new StandardPriceRuleItemDTO();
        item.setIdentityKey("overNightService");
        List<StandardPriceRuleItemDTO> items = Collections.singletonList(item);
        // act
        String result = invokePrivateMethod(items);
        // assert
        assertEquals("可过夜", result);
    }

    /**
     * Test when list contains multiple items including one match
     */
    @Test
    public void testGetOverNightValueByItems_MultipleItemsWithMatch() throws Throwable {
        // arrange
        StandardPriceRuleItemDTO item1 = new StandardPriceRuleItemDTO();
        item1.setIdentityKey("otherKey1");
        StandardPriceRuleItemDTO item2 = new StandardPriceRuleItemDTO();
        item2.setIdentityKey("overNightService");
        StandardPriceRuleItemDTO item3 = new StandardPriceRuleItemDTO();
        item3.setIdentityKey("otherKey2");
        List<StandardPriceRuleItemDTO> items = Arrays.asList(item1, item2, item3);
        // act
        String result = invokePrivateMethod(items);
        // assert
        assertEquals("可过夜", result);
    }

    /**
     * Test when list contains multiple non-matching items
     */
    @Test
    public void testGetOverNightValueByItems_MultipleItemsNoMatch() throws Throwable {
        // arrange
        StandardPriceRuleItemDTO item1 = new StandardPriceRuleItemDTO();
        item1.setIdentityKey("otherKey1");
        StandardPriceRuleItemDTO item2 = new StandardPriceRuleItemDTO();
        item2.setIdentityKey("otherKey2");
        List<StandardPriceRuleItemDTO> items = Arrays.asList(item1, item2);
        // act
        String result = invokePrivateMethod(items);
        // assert
        assertNull(result);
    }

    /**
     * Test when key is null
     */
    @Test
    public void testGetAttrValueByServiceProjectAttr_NullKey() throws Throwable {
        // arrange
        List<ServiceProjectAttrDTO> attrs = Collections.emptyList();
        String key = null;
        // act
        String result = invokePrivateMethod(attrs, key);
        // assert
        assertNull(result);
    }

    /**
     * Test when key is empty string
     */
    @Test
    public void testGetAttrValueByServiceProjectAttr_EmptyKey() throws Throwable {
        // arrange
        List<ServiceProjectAttrDTO> attrs = Collections.emptyList();
        String key = "";
        // act
        String result = invokePrivateMethod(attrs, key);
        // assert
        assertNull(result);
    }

    /**
     * Test when attribute list is empty
     */
    @Test
    public void testGetAttrValueByServiceProjectAttr_EmptyList() throws Throwable {
        // arrange
        List<ServiceProjectAttrDTO> attrs = Collections.emptyList();
        String key = "testKey";
        // act
        String result = invokePrivateMethod(attrs, key);
        // assert
        assertNull(result);
    }

    /**
     * Test when key doesn't match any attributes
     */
    @Test
    public void testGetAttrValueByServiceProjectAttr_NoMatch() throws Throwable {
        // arrange
        List<ServiceProjectAttrDTO> attrs = new ArrayList<>();
        ServiceProjectAttrDTO attr = new ServiceProjectAttrDTO();
        attr.setAttrName("otherKey");
        attr.setAttrValue("value");
        attrs.add(attr);
        String key = "testKey";
        // act
        String result = invokePrivateMethod(attrs, key);
        // assert
        assertNull(result);
    }

    /**
     * Test when there is one matching attribute
     */
    @Test
    public void testGetAttrValueByServiceProjectAttr_SingleMatch() throws Throwable {
        // arrange
        List<ServiceProjectAttrDTO> attrs = new ArrayList<>();
        ServiceProjectAttrDTO attr = new ServiceProjectAttrDTO();
        attr.setAttrName("testKey");
        attr.setAttrValue("expectedValue");
        attrs.add(attr);
        String key = "testKey";
        // act
        String result = invokePrivateMethod(attrs, key);
        // assert
        assertEquals("expectedValue", result);
    }

    /**
     * Test when there are multiple matching attributes (should return first match)
     */
    @Test
    public void testGetAttrValueByServiceProjectAttr_MultipleMatches() throws Throwable {
        // arrange
        ServiceProjectAttrDTO attr1 = new ServiceProjectAttrDTO();
        attr1.setAttrName("testKey");
        attr1.setAttrValue("firstValue");
        ServiceProjectAttrDTO attr2 = new ServiceProjectAttrDTO();
        attr2.setAttrName("testKey");
        attr2.setAttrValue("secondValue");
        List<ServiceProjectAttrDTO> attrs = Arrays.asList(attr1, attr2);
        String key = "testKey";
        // act
        String result = invokePrivateMethod(attrs, key);
        // assert
        assertEquals("firstValue", result);
    }
}
