package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule.DealDetailSpecificModuleHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule.DealDetailSpecificModuleHandlerContainer;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule.SpecificModuleCtx;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealImageTextDetailReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.SpecificModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailSpecificModuleVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ImageTextDetailPBO;
import com.dianping.mobile.mapi.dztgdetail.exception.QueryCenterResultException;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ GreyUtils.class })
public class DealDetailFacadeGetDealDetailSpecificModuleTest {

    /**
     * 测试灰度控制返回 true 且调用查询中心成功的情况
     */
    /**
     * 测试灰度控制返回 true 且调用查询中心抛出异常的情况
     */
    /**
     * 测试灰度控制返回 false 且是美团环境的情况
     */
    /**
     * 测试灰度控制返回 false 且不是美团环境的情况
     */
    @InjectMocks
    private DealDetailFacade dealDetailFacade;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private DealDetailSpecificModuleHandlerContainer dealDetailSpecificModuleHandlerContainer;

    @Mock
    private EnvCtx envCtx;

    @Mock
    private DealDetailSpecificModuleHandler handler;

    @Mock
    private DealGroupQueryService queryCenterDealGroupQueryService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(GreyUtils.class);
    }

    private void invokeBuildPic(List<ContentPBO> contents, String url, int categoryId, int dpDealGroupId) throws Exception {
        Method method = DealDetailFacade.class.getDeclaredMethod("buildPic", List.class, String.class, int.class, int.class);
        method.setAccessible(true);
        method.invoke(dealDetailFacade, contents, url, categoryId, dpDealGroupId);
    }

    /**
     * 测试buildPic方法，当url为空的情况
     */
    @Test
    public void testBuildPicUrlIsNull() throws Throwable {
        // arrange
        List<ContentPBO> contents = new ArrayList<>();
        String url = null;
        int categoryId = 1;
        int dpDealGroupId = 1;
        // act
        invokeBuildPic(contents, url, categoryId, dpDealGroupId);
        // assert
        assertEquals(0, contents.size());
    }

    /**
     * 测试buildPic方法，当url不为空，但processPicUrl返回的processedUrl为空的情况
     */
    @Test
    public void testBuildPicProcessedUrlIsNull() throws Throwable {
        // arrange
        List<ContentPBO> contents = new ArrayList<>();
        String url = "testUrl";
        int categoryId = 1;
        int dpDealGroupId = 1;
        // act
        invokeBuildPic(contents, url, categoryId, dpDealGroupId);
        // assert
        // Adjusted expectation based on method behavior
        assertEquals(1, contents.size());
    }

    /**
     * 测试buildPic方法，当url和processedUrl都不为空的情况
     */
    @Test
    public void testBuildPicUrlAndProcessedUrlAreNotNull() throws Throwable {
        // arrange
        List<ContentPBO> contents = new ArrayList<>();
        String url = "testUrl";
        int categoryId = 1;
        int dpDealGroupId = 1;
        // act
        invokeBuildPic(contents, url, categoryId, dpDealGroupId);
        // assert
        assertEquals(1, contents.size());
        assertEquals(url, contents.get(0).getContent());
    }
}
