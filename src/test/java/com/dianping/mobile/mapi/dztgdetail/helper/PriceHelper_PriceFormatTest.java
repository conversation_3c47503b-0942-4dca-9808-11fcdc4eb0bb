package com.dianping.mobile.mapi.dztgdetail.helper;

import org.junit.Test;
import static org.junit.Assert.*;

public class PriceHelper_PriceFormatTest {

    /**
     * 测试正常价格
     */
    @Test
    public void testPriceFormatNormalPrice() {
        // arrange
        double price = 123.45;
        // act
        String result = PriceHelper.priceFormat(price);
        // assert
        assertEquals("123.45", result);
    }

    /**
     * 测试整数价格
     */
    @Test
    public void testPriceFormatIntegerPrice() {
        // arrange
        double price = 123;
        // act
        String result = PriceHelper.priceFormat(price);
        // assert
        assertEquals("123", result);
    }

    /**
     * 测试零价格
     */
    @Test
    public void testPriceFormatZeroPrice() {
        // arrange
        double price = 0;
        // act
        String result = PriceHelper.priceFormat(price);
        // assert
        assertEquals("0", result);
    }

    /**
     * 测试小于一元的 price
     */
    @Test
    public void testPriceFormatLessThanOnePrice() {
        // arrange
        double price = 0.01;
        // act
        String result = PriceHelper.priceFormat(price);
        // assert
        assertEquals("0.01", result);
    }
}
