package com.dianping.mobile.mapi.dztgdetail.biz.service.recommend;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.Environment;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.entity.Response;
import com.dianping.martgeneral.recommend.api.service.RecommendService;
import com.dianping.mobile.mapi.dztgdetail.biz.service.RelatedRecommendService;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Constants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.model.RecommendShopBaseInfoModel;
import com.dianping.mobile.mapi.dztgdetail.common.model.RecommendUserBaseInfoModel;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.RelatedRecommendCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedRecommendReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RecommendItemDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedDealPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedRecommendVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedShopPBO;
import com.google.common.collect.Lists;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

/**
 * @Author: <EMAIL>
 * @Date: 2024/11/15
 */
@RunWith(MockitoJUnitRunner.class)
public class CrossShopRecommendHandlerTest {
    @InjectMocks
    private CrossShopRecommendHandler crossShopRecommendHandler;
    @Mock
    private RelatedRecommendService relatedRecommendService;
    @Mock
    private LeCrossRecommendHandler leCrossRecommendHandler;
    @Mock(name = "generalRecommendService")
    private RecommendService recommendService;
    private MockedStatic<Environment> environmentMockedStatic;

    @Before
    public void setUp() {
        environmentMockedStatic = Mockito.mockStatic(Environment.class);
    }

    @After
    public void tearDown() {
        environmentMockedStatic.close();
    }

    @Test
    public void testValidate() {
        RelatedRecommendCtx relatedRecommendCtx = RelatedRecommendCtx.builder().build();
        RelatedRecommendReq req = new RelatedRecommendReq();
        req.setDealGroupId(123);
        req.setStart(1);
        req.setLimit(10);
        req.setShopIdStr("1");
        req.setShopIdStrEncrypt("abc");
        relatedRecommendCtx.setReq(req);
        Assert.assertTrue(crossShopRecommendHandler.validate(relatedRecommendCtx));
    }

    @Test
    public void testGetResult() {
        environmentMockedStatic.when(() -> Environment.isTestEnv()).thenReturn(false);
        RelatedRecommendCtx ctx = JSON.parseObject("{\"dealGroupDTO\":{\"basic\":{\"categoryId\":502},\"category\":{\"categoryId\":502},\"dpDealGroupId\":**********,\"dpDealGroupIdInt\":**********,\"mtDealGroupId\":**********,\"mtDealGroupIdInt\":**********},\"envCtx\":{\"android\":false,\"apollo\":false,\"appDeviceId\":\"e931975d53f94652a9aee6cac17acca80000000000001453000\",\"appId\":10,\"clientType\":200502,\"dianpingClientList\":[\"DIANPING_APP\",\"DIANPING_FROM_H5\",\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"],\"dp\":false,\"dpId\":\"e931975d53f94652a9aee6cac17acca80000000000001453000\",\"dpMerchant\":false,\"dpMiniApp\":false,\"dpMiniAppList\":[\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"],\"dpUserId\":0,\"dpVirtualUserId\":0,\"dztgClientTypeEnum\":\"MEITUAN_APP\",\"external\":false,\"externalAndNoScene\":false,\"fromH5\":false,\"ios\":true,\"login\":true,\"mainApp\":true,\"mainWX\":false,\"mainWeb\":false,\"meituanClientList\":[\"MEITUAN_APP\",\"MEITUAN_FROM_H5\",\"MEITUAN_MAP_APP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_LIVE_WEIXIN_MINIAPP\"],\"merchantClientList\":[\"DPMERCHANT\"],\"miniApp\":false,\"mt\":true,\"mtLiveMinApp\":false,\"mtMiniApp\":false,\"mtMiniAppList\":[\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_KUAISHOU_MINIAPP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_LIVE_WEIXIN_MINIAPP\"],\"mtUserId\":**********,\"mtVirtualUserId\":0,\"native\":true,\"nativeAppList\":[\"MEITUAN_APP\",\"MEITUAN_MAP_APP\",\"DIANPING_APP\"],\"requestURI\":\"/general/platform/dztgdetail/crossshoprecommend.bin\",\"startTime\":1731641022044,\"thirdPlatform\":false,\"unionId\":\"e931975d53f94652a9aee6cac17acca80000000000001453000\",\"userAgent\":\"MApi 1.3 (mtscope 12.25.400 appstore; iPhone 16.2 iPhone13,2; a0d0)\",\"userId\":**********,\"userIp\":\"***************\",\"uuid\":\"0000000000000BF83E0F7C51E4E65A63EC4EA35305780A162880001982084426\",\"version\":\"12.25.400\",\"virtualUserId\":0,\"wxMini\":false,\"wxMiniList\":[\"MEITUAN_WEIXIN_MINIAPP\",\"DIANPING_WEIXIN_MINIAPP\"]},\"relatedRecommendConfig\":{\"dealDisplayMaxValue\":\"3\",\"useAdHangPrice\":\"false\",\"similarItemText\":\"相似好价，比出来的好价 >\",\"inShopRecommendModuleButtonText\":\"查看全部（%s个）>\"},\"req\":{\"cityId\":10,\"dealGroupId\":**********,\"gpsCityId\":0,\"limit\":10,\"shopIdStr\":\"*********\",\"start\":1,\"userLat\":31.27130171133249,\"userLng\":121.************},\"result\":{\"isEnd\":false,\"nextStartIndex\":0,\"recordCount\":0,\"startIndex\":0}}", RelatedRecommendCtx.class);
        Mockito.when(relatedRecommendService.preBuildShopBaseInfoModel(Mockito.any())).thenReturn(new RecommendShopBaseInfoModel());
        Mockito.when(relatedRecommendService.preBuildUserBaseInfoModel(Mockito.any())).thenReturn(new RecommendUserBaseInfoModel());
        RecommendShopBaseInfoModel shopBaseInfoModel = new RecommendShopBaseInfoModel();
        shopBaseInfoModel.setBackCategory(Lists.newArrayList(1));
        RecommendUserBaseInfoModel userBaseInfoModel = new RecommendUserBaseInfoModel();
        Mockito.when(relatedRecommendService.doBuildShopBaseInfoModel(Mockito.any(), Mockito.any())).thenReturn(shopBaseInfoModel);
        Mockito.when(relatedRecommendService.doBuildUserBaseInfoModel(Mockito.any(), Mockito.any())).thenReturn(userBaseInfoModel);

        Response<RecommendResult<Object>> resultResponse = new Response<RecommendResult<Object>>();
        RecommendResult<Object> recommendResult = new RecommendResult<>();
        recommendResult.setTotalSize(100);
        recommendResult.setSortedResult(Lists.newArrayList(new RecommendDTO()));
        resultResponse.setResult(recommendResult);
        Mockito.when(recommendService.recommend(Mockito.any(), Mockito.any())).thenReturn(resultResponse);
        Assert.assertTrue(Objects.nonNull(crossShopRecommendHandler.getResult(ctx)));
    }

    @Test
    public void testFill() {
        RelatedRecommendCtx relatedRecommendCtx = JSON.parseObject("{\"dealGroupDTO\":{\"basic\":{\"categoryId\":502},\"category\":{\"categoryId\":502},\"dpDealGroupId\":**********,\"dpDealGroupIdInt\":**********,\"mtDealGroupId\":**********,\"mtDealGroupIdInt\":**********},\"envCtx\":{\"android\":false,\"apollo\":false,\"appDeviceId\":\"e931975d53f94652a9aee6cac17acca80000000000001453000\",\"appId\":10,\"clientType\":200502,\"dianpingClientList\":[\"DIANPING_APP\",\"DIANPING_FROM_H5\",\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"],\"dp\":false,\"dpId\":\"e931975d53f94652a9aee6cac17acca80000000000001453000\",\"dpMerchant\":false,\"dpMiniApp\":false,\"dpMiniAppList\":[\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"],\"dpUserId\":0,\"dpVirtualUserId\":0,\"dztgClientTypeEnum\":\"MEITUAN_APP\",\"external\":false,\"externalAndNoScene\":false,\"fromH5\":false,\"ios\":true,\"login\":true,\"mainApp\":true,\"mainWX\":false,\"mainWeb\":false,\"meituanClientList\":[\"MEITUAN_APP\",\"MEITUAN_FROM_H5\",\"MEITUAN_MAP_APP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_LIVE_WEIXIN_MINIAPP\"],\"merchantClientList\":[\"DPMERCHANT\"],\"miniApp\":false,\"mt\":true,\"mtLiveMinApp\":false,\"mtMiniApp\":false,\"mtMiniAppList\":[\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_KUAISHOU_MINIAPP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_LIVE_WEIXIN_MINIAPP\"],\"mtUserId\":**********,\"mtVirtualUserId\":0,\"native\":true,\"nativeAppList\":[\"MEITUAN_APP\",\"MEITUAN_MAP_APP\",\"DIANPING_APP\"],\"requestURI\":\"/general/platform/dztgdetail/crossshoprecommend.bin\",\"startTime\":1731641022044,\"thirdPlatform\":false,\"unionId\":\"e931975d53f94652a9aee6cac17acca80000000000001453000\",\"userAgent\":\"MApi 1.3 (mtscope 12.25.400 appstore; iPhone 16.2 iPhone13,2; a0d0)\",\"userId\":**********,\"userIp\":\"***************\",\"uuid\":\"0000000000000BF83E0F7C51E4E65A63EC4EA35305780A162880001982084426\",\"version\":\"12.25.400\",\"virtualUserId\":0,\"wxMini\":false,\"wxMiniList\":[\"MEITUAN_WEIXIN_MINIAPP\",\"DIANPING_WEIXIN_MINIAPP\"]},\"relatedRecommendConfig\":{\"dealDisplayMaxValue\":\"3\",\"useAdHangPrice\":\"false\",\"similarItemText\":\"相似好价，比出来的好价 >\",\"inShopRecommendModuleButtonText\":\"查看全部（%s个）>\"},\"req\":{\"cityId\":10,\"dealGroupId\":**********,\"gpsCityId\":0,\"limit\":10,\"shopIdStr\":\"*********\",\"start\":1,\"userLat\":31.27130171133249,\"userLng\":121.************},\"result\":{\"isEnd\":false,\"nextStartIndex\":0,\"recordCount\":0,\"startIndex\":0}}", RelatedRecommendCtx.class);
        RecommendResult<RecommendDTO> recommendResult = JSON.parseObject("{\"processId\":0,\"sortedResult\":[{\"item\":\"**********\",\"type\":\"Deal\"},{\"item\":\"**********\",\"type\":\"Deal\"},{\"item\":\"*********\",\"type\":\"Poi\"},{\"item\":\"*********\",\"type\":\"Poi\"}],\"totalSize\":10}", new TypeReference<RecommendResult<RecommendDTO>>() {});
        List<RelatedDealPBO> relatedDealPBOList = JSON.parseObject("[{\"dealContents\":[{\"content\":\"https://p0.meituan.net/travelcube/761530f8b4ff4cbbbb7967b2ce4b3e76229337.jpg\",\"type\":1}],\"detailUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=**********&poiid=*********\",\"dpId\":0,\"itemTextInfos\":[],\"mtId\":**********,\"promoDetailModule\":{\"marketPrice\":\"128\",\"marketPromoDiscount\":\"6.9折\",\"priceDisplayType\":0,\"promoNewStyle\":false,\"promoPrice\":\"88\",\"showBestPromoDetails\":false,\"showMarketPrice\":false,\"showPriceCompareEntrance\":false},\"shop\":{\"buyBarIconType\":0,\"displayPosition\":1,\"hideAddrEnable\":false,\"hideStars\":false,\"lat\":0.0,\"lng\":0.0,\"lyyShop\":false,\"shopBizType\":0,\"shopCategoryId\":0,\"shopId\":*********,\"shopNum\":0,\"shopPower\":0,\"shopType\":0},\"title\":\"纯色美甲\"},{\"dealContents\":[{\"content\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/6be573d85903c56b7326a91c0382e6d4166669.jpg\",\"type\":1}],\"detailUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=**********&poiid=*********\",\"dpId\":0,\"itemTextInfos\":[],\"mtId\":**********,\"promoDetailModule\":{\"marketPrice\":\"100\",\"marketPromoDiscount\":\"3.0折\",\"priceDisplayType\":0,\"promoNewStyle\":false,\"promoPrice\":\"30\",\"showBestPromoDetails\":false,\"showMarketPrice\":false,\"showPriceCompareEntrance\":false},\"shop\":{\"buyBarIconType\":0,\"displayPosition\":1,\"hideAddrEnable\":false,\"hideStars\":false,\"lat\":0.0,\"lng\":0.0,\"lyyShop\":false,\"shopBizType\":0,\"shopCategoryId\":0,\"shopId\":*********,\"shopNum\":0,\"shopPower\":0,\"shopType\":0},\"title\":\"xxx｜33分钟足疗\"}]", new TypeReference<List<RelatedDealPBO>>() {});
//        Mockito.when(relatedRecommendService.fillDealGroupInfo(relatedRecommendCtx, Lists.newArrayList(**********))).thenReturn(relatedRecommendCtx);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        relatedRecommendCtx.setEnvCtx(envCtx);
        Mockito.when(relatedRecommendService.getCrossShopDeals(Mockito.any(), Mockito.any())).thenReturn(relatedDealPBOList);
        List<RelatedShopPBO> relatedShopPBOS = JSON.parseObject("[{\"avgPrice\":\"\",\"distanceDesc\":\"距您16km\",\"fiveScore\":\"0.0\",\"mainRegionName\":\"虹桥火车站/机场\",\"recommendInfo\":{\"recommendSource\":0},\"shopId\":*********,\"shopName\":\"KTV新版上单门店\",\"shopPic\":\"http://p1.meituan.net/searchscenerec/a1f99544c203750af25ccf87395cfb49400458.png%40300w_225h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\",\"shopPower\":0,\"shopUrl\":\"imeituan://www.meituan.com/gc/poi/detail?id=*********&mmcinflate=&mmcuse=&mmcbuy=&mmcfree=&channelType=\"},{\"avgPrice\":\"\",\"distanceDesc\":\"距您15km\",\"fiveScore\":\"0.0\",\"mainRegionName\":\"北新泾/淞虹路\",\"recommendInfo\":{\"recommendSource\":0},\"shopId\":*********,\"shopName\":\"供应链自动化专用-丽人美甲门店\",\"shopPic\":\"http://p1.meituan.net/searchscenerec/92e8bc86c05b07e3b0bbd85a83d5206c279007.png%40300w_225h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\",\"shopPower\":0,\"shopUrl\":\"imeituan://www.meituan.com/gc/poi/detail?id=*********&mmcinflate=&mmcuse=&mmcbuy=&mmcfree=&channelType=\"}]", new TypeReference<List<RelatedShopPBO>>() {});
        Mockito.when(relatedRecommendService.getShops(Mockito.any(), Mockito.any())).thenReturn(relatedShopPBOS);
        Mockito.when(leCrossRecommendHandler.buildLeCrossRecommendFuture(Mockito.any())).thenReturn(false);
        Response<RecommendResult<RecommendDTO>> response = new Response<>();
        response.setResult(recommendResult);
        RelatedRecommendVO relatedRecommendVO = crossShopRecommendHandler.fill(relatedRecommendCtx, response);
        Assert.assertTrue(relatedRecommendVO.getRecommendItemList().size() == 4);
    }

    @Test
    public void testFillSimilarRecommendItemInfo() {
        Boolean similarRecommendItemMap = true;
        RelatedRecommendCtx relatedRecommendCtx = JSON.parseObject("{\"dealGroupDTO\":{\"basic\":{\"categoryId\":502},\"category\":{\"categoryId\":502},\"dpDealGroupId\":**********,\"dpDealGroupIdInt\":**********,\"mtDealGroupId\":**********,\"mtDealGroupIdInt\":**********},\"envCtx\":{\"android\":false,\"apollo\":false,\"appDeviceId\":\"e931975d53f94652a9aee6cac17acca80000000000001453000\",\"appId\":10,\"clientType\":200502,\"dianpingClientList\":[\"DIANPING_APP\",\"DIANPING_FROM_H5\",\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"],\"dp\":false,\"dpId\":\"e931975d53f94652a9aee6cac17acca80000000000001453000\",\"dpMerchant\":false,\"dpMiniApp\":false,\"dpMiniAppList\":[\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"],\"dpUserId\":0,\"dpVirtualUserId\":0,\"dztgClientTypeEnum\":\"MEITUAN_APP\",\"external\":false,\"externalAndNoScene\":false,\"fromH5\":false,\"ios\":true,\"login\":true,\"mainApp\":true,\"mainWX\":false,\"mainWeb\":false,\"meituanClientList\":[\"MEITUAN_APP\",\"MEITUAN_FROM_H5\",\"MEITUAN_MAP_APP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_LIVE_WEIXIN_MINIAPP\"],\"merchantClientList\":[\"DPMERCHANT\"],\"miniApp\":false,\"mt\":true,\"mtLiveMinApp\":false,\"mtMiniApp\":false,\"mtMiniAppList\":[\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_KUAISHOU_MINIAPP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_LIVE_WEIXIN_MINIAPP\"],\"mtUserId\":**********,\"mtVirtualUserId\":0,\"native\":true,\"nativeAppList\":[\"MEITUAN_APP\",\"MEITUAN_MAP_APP\",\"DIANPING_APP\"],\"requestURI\":\"/general/platform/dztgdetail/crossshoprecommend.bin\",\"startTime\":1731641022044,\"thirdPlatform\":false,\"unionId\":\"e931975d53f94652a9aee6cac17acca80000000000001453000\",\"userAgent\":\"MApi 1.3 (mtscope 12.25.400 appstore; iPhone 16.2 iPhone13,2; a0d0)\",\"userId\":**********,\"userIp\":\"***************\",\"uuid\":\"0000000000000BF83E0F7C51E4E65A63EC4EA35305780A162880001982084426\",\"version\":\"12.25.400\",\"virtualUserId\":0,\"wxMini\":false,\"wxMiniList\":[\"MEITUAN_WEIXIN_MINIAPP\",\"DIANPING_WEIXIN_MINIAPP\"]},\"relatedRecommendConfig\":{\"dealDisplayMaxValue\":\"3\",\"useAdHangPrice\":\"false\",\"similarItemText\":\"相似好价，比出来的好价 >\",\"inShopRecommendModuleButtonText\":\"查看全部（%s个）>\"},\"req\":{\"cityId\":10,\"dealGroupId\":**********,\"gpsCityId\":0,\"limit\":10,\"shopIdStr\":\"*********\",\"start\":1,\"userLat\":31.27130171133249,\"userLng\":121.************},\"result\":{\"isEnd\":false,\"nextStartIndex\":0,\"recordCount\":0,\"startIndex\":0}}", RelatedRecommendCtx.class);
        RecommendItemDTO recommendItemDTO = new RecommendItemDTO();
        RelatedDealPBO dealPBO = new RelatedDealPBO();
        dealPBO.setItemTextInfos(new ArrayList<>());
        recommendItemDTO.setDealPBO(dealPBO);
        crossShopRecommendHandler.fillSimilarRecommendItemInfo(relatedRecommendCtx, recommendItemDTO, similarRecommendItemMap);
        Assert.assertTrue(Objects.nonNull(recommendItemDTO.getDealPBO().getItemTextInfos()));
    }

    @Test
    public void testFillAdCustomDealInfo() {
        RelatedRecommendCtx relatedRecommendCtx = JSON.parseObject("{\"dealGroupDTO\":{\"basic\":{\"categoryId\":502},\"category\":{\"categoryId\":502},\"dpDealGroupId\":**********,\"dpDealGroupIdInt\":**********,\"mtDealGroupId\":**********,\"mtDealGroupIdInt\":**********},\"envCtx\":{\"android\":false,\"apollo\":false,\"appDeviceId\":\"e931975d53f94652a9aee6cac17acca80000000000001453000\",\"appId\":10,\"clientType\":200502,\"dianpingClientList\":[\"DIANPING_APP\",\"DIANPING_FROM_H5\",\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"],\"dp\":false,\"dpId\":\"e931975d53f94652a9aee6cac17acca80000000000001453000\",\"dpMerchant\":false,\"dpMiniApp\":false,\"dpMiniAppList\":[\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"],\"dpUserId\":0,\"dpVirtualUserId\":0,\"dztgClientTypeEnum\":\"MEITUAN_APP\",\"external\":false,\"externalAndNoScene\":false,\"fromH5\":false,\"ios\":true,\"login\":true,\"mainApp\":true,\"mainWX\":false,\"mainWeb\":false,\"meituanClientList\":[\"MEITUAN_APP\",\"MEITUAN_FROM_H5\",\"MEITUAN_MAP_APP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_LIVE_WEIXIN_MINIAPP\"],\"merchantClientList\":[\"DPMERCHANT\"],\"miniApp\":false,\"mt\":true,\"mtLiveMinApp\":false,\"mtMiniApp\":false,\"mtMiniAppList\":[\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_KUAISHOU_MINIAPP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_LIVE_WEIXIN_MINIAPP\"],\"mtUserId\":**********,\"mtVirtualUserId\":0,\"native\":true,\"nativeAppList\":[\"MEITUAN_APP\",\"MEITUAN_MAP_APP\",\"DIANPING_APP\"],\"requestURI\":\"/general/platform/dztgdetail/crossshoprecommend.bin\",\"startTime\":1731641022044,\"thirdPlatform\":false,\"unionId\":\"e931975d53f94652a9aee6cac17acca80000000000001453000\",\"userAgent\":\"MApi 1.3 (mtscope 12.25.400 appstore; iPhone 16.2 iPhone13,2; a0d0)\",\"userId\":**********,\"userIp\":\"***************\",\"uuid\":\"0000000000000BF83E0F7C51E4E65A63EC4EA35305780A162880001982084426\",\"version\":\"12.25.400\",\"virtualUserId\":0,\"wxMini\":false,\"wxMiniList\":[\"MEITUAN_WEIXIN_MINIAPP\",\"DIANPING_WEIXIN_MINIAPP\"]},\"relatedRecommendConfig\":{\"dealDisplayMaxValue\":\"3\",\"useAdHangPrice\":\"false\",\"similarItemText\":\"相似好价，比出来的好价 >\",\"inShopRecommendModuleButtonText\":\"查看全部（%s个）>\"},\"req\":{\"cityId\":10,\"dealGroupId\":**********,\"gpsCityId\":0,\"limit\":10,\"shopIdStr\":\"*********\",\"start\":1,\"userLat\":31.27130171133249,\"userLng\":121.************},\"result\":{\"isEnd\":false,\"nextStartIndex\":0,\"recordCount\":0,\"startIndex\":0}}", RelatedRecommendCtx.class);
        RelatedDealPBO dealPBO = new RelatedDealPBO();
        dealPBO.setDpId(1);
        dealPBO.setMtId(1);
        Map<Integer, Map<String, Object>> adDealGroupIdMap = new HashMap<>();
        Map<String, Object> adDealInfoMap = new HashMap<>();
        adDealInfoMap.put("adCreative", new HashMap<String, String>() {
            {
                put("1", "1");
                put("imagemark", "人气精选");
            }
        });
        adDealGroupIdMap.put(1, adDealInfoMap);
        crossShopRecommendHandler.fillAdCustomDealInfo(relatedRecommendCtx, dealPBO, adDealGroupIdMap);
        Assert.assertTrue(dealPBO.getItemTextInfos().size() == 2);
    }

    @Test
    public void testFillAdCustomShopInfo() {
        RelatedRecommendCtx relatedRecommendCtx = JSON.parseObject("{\"dealGroupDTO\":{\"basic\":{\"categoryId\":502},\"category\":{\"categoryId\":502},\"dpDealGroupId\":**********,\"dpDealGroupIdInt\":**********,\"mtDealGroupId\":**********,\"mtDealGroupIdInt\":**********},\"envCtx\":{\"android\":false,\"apollo\":false,\"appDeviceId\":\"e931975d53f94652a9aee6cac17acca80000000000001453000\",\"appId\":10,\"clientType\":200502,\"dianpingClientList\":[\"DIANPING_APP\",\"DIANPING_FROM_H5\",\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"],\"dp\":false,\"dpId\":\"e931975d53f94652a9aee6cac17acca80000000000001453000\",\"dpMerchant\":false,\"dpMiniApp\":false,\"dpMiniAppList\":[\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"],\"dpUserId\":0,\"dpVirtualUserId\":0,\"dztgClientTypeEnum\":\"MEITUAN_APP\",\"external\":false,\"externalAndNoScene\":false,\"fromH5\":false,\"ios\":true,\"login\":true,\"mainApp\":true,\"mainWX\":false,\"mainWeb\":false,\"meituanClientList\":[\"MEITUAN_APP\",\"MEITUAN_FROM_H5\",\"MEITUAN_MAP_APP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_LIVE_WEIXIN_MINIAPP\"],\"merchantClientList\":[\"DPMERCHANT\"],\"miniApp\":false,\"mt\":true,\"mtLiveMinApp\":false,\"mtMiniApp\":false,\"mtMiniAppList\":[\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_KUAISHOU_MINIAPP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_LIVE_WEIXIN_MINIAPP\"],\"mtUserId\":**********,\"mtVirtualUserId\":0,\"native\":true,\"nativeAppList\":[\"MEITUAN_APP\",\"MEITUAN_MAP_APP\",\"DIANPING_APP\"],\"requestURI\":\"/general/platform/dztgdetail/crossshoprecommend.bin\",\"startTime\":1731641022044,\"thirdPlatform\":false,\"unionId\":\"e931975d53f94652a9aee6cac17acca80000000000001453000\",\"userAgent\":\"MApi 1.3 (mtscope 12.25.400 appstore; iPhone 16.2 iPhone13,2; a0d0)\",\"userId\":**********,\"userIp\":\"***************\",\"uuid\":\"0000000000000BF83E0F7C51E4E65A63EC4EA35305780A162880001982084426\",\"version\":\"12.25.400\",\"virtualUserId\":0,\"wxMini\":false,\"wxMiniList\":[\"MEITUAN_WEIXIN_MINIAPP\",\"DIANPING_WEIXIN_MINIAPP\"]},\"relatedRecommendConfig\":{\"dealDisplayMaxValue\":\"3\",\"useAdHangPrice\":\"false\",\"similarItemText\":\"相似好价，比出来的好价 >\",\"inShopRecommendModuleButtonText\":\"查看全部（%s个）>\"},\"req\":{\"cityId\":10,\"dealGroupId\":**********,\"gpsCityId\":0,\"limit\":10,\"shopIdStr\":\"*********\",\"start\":1,\"userLat\":31.27130171133249,\"userLng\":121.************},\"result\":{\"isEnd\":false,\"nextStartIndex\":0,\"recordCount\":0,\"startIndex\":0}}", RelatedRecommendCtx.class);
        RelatedShopPBO shopPBO = new RelatedShopPBO();
        shopPBO.setShopId(1L);
        Map<Long, Map<String, Object>> adDealGroupIdMap = new HashMap<>();
        Map<String, Object> adShopInfoMap = new HashMap<>();
        adShopInfoMap.put("adCreative", new HashMap<String, String>() {
            {
                put("1", "1");
                put("imagemark", "人气精选");
            }
        });
        adDealGroupIdMap.put(1L, adShopInfoMap);
        shopPBO.setItemTextInfos(new ArrayList<>());
        crossShopRecommendHandler.fillAdCustomShopInfo(relatedRecommendCtx, shopPBO, adDealGroupIdMap);
        Assert.assertTrue(shopPBO.getItemTextInfos().size() == 2);
    }

    @Test
    public void testBuildAdExpInfo() {
        String expInfo= "{\"key\":\"MtRecommendStrategy\",\"configs\":[{\"expId\":\"EXP2024100800006\",\"expResult\":\"b\",\"expBiInfo\":{\"query_id\":\"bf097eb5-8105-4c69-864a-342fa1a63820\",\"ab_id\":\"EXP2024100800006_b\"}}]}";
        RelatedRecommendReq req = new RelatedRecommendReq();
        req.setExpInfo(expInfo);
        String result = crossShopRecommendHandler.buildAdExpInfo(req);
        Assert.assertTrue("MtRecommendStrategy^EXP2024100800006_b".equals(result));
    }

    @Test
    public void testFillItemReportInfo() {
        Map<String, String> adCreativeMap = new HashMap<>();
        adCreativeMap.put(Constants.AD_LX_KEY, "ad_lx");
        Map<String, Object> bizDataMap = new HashMap<>();
        bizDataMap.put(Constants.AD_FEEDBACK_KEY, "ad_feedback");
        bizDataMap.put(Constants.AD_REQUEST_ID_KEY, "ad_request_id");
        bizDataMap.put("adCreative", adCreativeMap);
        RecommendDTO recommendDTO = new RecommendDTO();
        recommendDTO.setBizData(bizDataMap);

        RecommendItemDTO recommendItemDTO = new RecommendItemDTO();
        crossShopRecommendHandler.fillItemReportInfo(recommendDTO, recommendItemDTO);
        Assert.assertTrue(Objects.nonNull(recommendItemDTO.getAdReportDTO()));
    }

    @Test
    public void testFill_true() {
        RelatedRecommendCtx relatedRecommendCtx = JSON.parseObject("{\"dealGroupDTO\":{\"basic\":{\"categoryId\":502},\"category\":{\"categoryId\":502},\"dpDealGroupId\":**********,\"dpDealGroupIdInt\":**********,\"mtDealGroupId\":**********,\"mtDealGroupIdInt\":**********},\"envCtx\":{\"android\":false,\"apollo\":false,\"appDeviceId\":\"e931975d53f94652a9aee6cac17acca80000000000001453000\",\"appId\":10,\"clientType\":200502,\"dianpingClientList\":[\"DIANPING_APP\",\"DIANPING_FROM_H5\",\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"],\"dp\":false,\"dpId\":\"e931975d53f94652a9aee6cac17acca80000000000001453000\",\"dpMerchant\":false,\"dpMiniApp\":false,\"dpMiniAppList\":[\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"],\"dpUserId\":0,\"dpVirtualUserId\":0,\"dztgClientTypeEnum\":\"MEITUAN_APP\",\"external\":false,\"externalAndNoScene\":false,\"fromH5\":false,\"ios\":true,\"login\":true,\"mainApp\":true,\"mainWX\":false,\"mainWeb\":false,\"meituanClientList\":[\"MEITUAN_APP\",\"MEITUAN_FROM_H5\",\"MEITUAN_MAP_APP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_LIVE_WEIXIN_MINIAPP\"],\"merchantClientList\":[\"DPMERCHANT\"],\"miniApp\":false,\"mt\":true,\"mtLiveMinApp\":false,\"mtMiniApp\":false,\"mtMiniAppList\":[\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_KUAISHOU_MINIAPP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_LIVE_WEIXIN_MINIAPP\"],\"mtUserId\":**********,\"mtVirtualUserId\":0,\"native\":true,\"nativeAppList\":[\"MEITUAN_APP\",\"MEITUAN_MAP_APP\",\"DIANPING_APP\"],\"requestURI\":\"/general/platform/dztgdetail/crossshoprecommend.bin\",\"startTime\":1731641022044,\"thirdPlatform\":false,\"unionId\":\"e931975d53f94652a9aee6cac17acca80000000000001453000\",\"userAgent\":\"MApi 1.3 (mtscope 12.25.400 appstore; iPhone 16.2 iPhone13,2; a0d0)\",\"userId\":**********,\"userIp\":\"***************\",\"uuid\":\"0000000000000BF83E0F7C51E4E65A63EC4EA35305780A162880001982084426\",\"version\":\"12.25.400\",\"virtualUserId\":0,\"wxMini\":false,\"wxMiniList\":[\"MEITUAN_WEIXIN_MINIAPP\",\"DIANPING_WEIXIN_MINIAPP\"]},\"relatedRecommendConfig\":{\"dealDisplayMaxValue\":\"3\",\"useAdHangPrice\":\"false\",\"similarItemText\":\"相似好价，比出来的好价 >\",\"inShopRecommendModuleButtonText\":\"查看全部（%s个）>\"},\"req\":{\"cityId\":10,\"dealGroupId\":**********,\"gpsCityId\":0,\"limit\":10,\"shopIdStr\":\"*********\",\"start\":1,\"userLat\":31.27130171133249,\"userLng\":121.************},\"result\":{\"isEnd\":false,\"nextStartIndex\":0,\"recordCount\":0,\"startIndex\":0}}", RelatedRecommendCtx.class);
        RecommendResult<RecommendDTO> recommendResult = JSON.parseObject("{\"processId\":0,\"sortedResult\":[{\"item\":\"**********\",\"type\":\"Deal\"},{\"item\":\"**********\",\"type\":\"Deal\"},{\"item\":\"*********\",\"type\":\"Poi\"},{\"item\":\"*********\",\"type\":\"Poi\"}],\"totalSize\":10}", new TypeReference<RecommendResult<RecommendDTO>>() {});
        List<RelatedDealPBO> relatedDealPBOList = JSON.parseObject("[{\"dealContents\":[{\"content\":\"https://p0.meituan.net/travelcube/761530f8b4ff4cbbbb7967b2ce4b3e76229337.jpg\",\"type\":1}],\"detailUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=**********&poiid=*********\",\"dpId\":0,\"itemTextInfos\":[],\"mtId\":**********,\"promoDetailModule\":{\"marketPrice\":\"128\",\"marketPromoDiscount\":\"6.9折\",\"priceDisplayType\":0,\"promoNewStyle\":false,\"promoPrice\":\"88\",\"showBestPromoDetails\":false,\"showMarketPrice\":false,\"showPriceCompareEntrance\":false},\"shop\":{\"buyBarIconType\":0,\"displayPosition\":1,\"hideAddrEnable\":false,\"hideStars\":false,\"lat\":0.0,\"lng\":0.0,\"lyyShop\":false,\"shopBizType\":0,\"shopCategoryId\":0,\"shopId\":*********,\"shopNum\":0,\"shopPower\":0,\"shopType\":0},\"title\":\"纯色美甲\"},{\"dealContents\":[{\"content\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/6be573d85903c56b7326a91c0382e6d4166669.jpg\",\"type\":1}],\"detailUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=**********&poiid=*********\",\"dpId\":0,\"itemTextInfos\":[],\"mtId\":**********,\"promoDetailModule\":{\"marketPrice\":\"100\",\"marketPromoDiscount\":\"3.0折\",\"priceDisplayType\":0,\"promoNewStyle\":false,\"promoPrice\":\"30\",\"showBestPromoDetails\":false,\"showMarketPrice\":false,\"showPriceCompareEntrance\":false},\"shop\":{\"buyBarIconType\":0,\"displayPosition\":1,\"hideAddrEnable\":false,\"hideStars\":false,\"lat\":0.0,\"lng\":0.0,\"lyyShop\":false,\"shopBizType\":0,\"shopCategoryId\":0,\"shopId\":*********,\"shopNum\":0,\"shopPower\":0,\"shopType\":0},\"title\":\"xxx｜33分钟足疗\"}]", new TypeReference<List<RelatedDealPBO>>() {});
//        Mockito.when(relatedRecommendService.fillDealGroupInfo(relatedRecommendCtx, Lists.newArrayList(**********))).thenReturn(relatedRecommendCtx);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        relatedRecommendCtx.setEnvCtx(envCtx);
        Mockito.when(relatedRecommendService.getCrossShopDeals(Mockito.any(), Mockito.any())).thenReturn(relatedDealPBOList);
        List<RelatedShopPBO> relatedShopPBOS = JSON.parseObject("[{\"avgPrice\":\"\",\"distanceDesc\":\"距您16km\",\"fiveScore\":\"0.0\",\"mainRegionName\":\"虹桥火车站/机场\",\"recommendInfo\":{\"recommendSource\":0},\"shopId\":*********,\"shopName\":\"KTV新版上单门店\",\"shopPic\":\"http://p1.meituan.net/searchscenerec/a1f99544c203750af25ccf87395cfb49400458.png%40300w_225h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\",\"shopPower\":0,\"shopUrl\":\"imeituan://www.meituan.com/gc/poi/detail?id=*********&mmcinflate=&mmcuse=&mmcbuy=&mmcfree=&channelType=\"},{\"avgPrice\":\"\",\"distanceDesc\":\"距您15km\",\"fiveScore\":\"0.0\",\"mainRegionName\":\"北新泾/淞虹路\",\"recommendInfo\":{\"recommendSource\":0},\"shopId\":*********,\"shopName\":\"供应链自动化专用-丽人美甲门店\",\"shopPic\":\"http://p1.meituan.net/searchscenerec/92e8bc86c05b07e3b0bbd85a83d5206c279007.png%40300w_225h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\",\"shopPower\":0,\"shopUrl\":\"imeituan://www.meituan.com/gc/poi/detail?id=*********&mmcinflate=&mmcuse=&mmcbuy=&mmcfree=&channelType=\"}]", new TypeReference<List<RelatedShopPBO>>() {});
        Mockito.when(relatedRecommendService.getShops(Mockito.any(), Mockito.any())).thenReturn(relatedShopPBOS);
        Mockito.when(leCrossRecommendHandler.buildLeCrossRecommendFuture(Mockito.any())).thenReturn(true);
        Mockito.when(leCrossRecommendHandler.leCrossAddRecommend(Mockito.any(),Mockito.any())).thenReturn(relatedRecommendCtx.getResult().getRecommendItemList());
        Response<RecommendResult<RecommendDTO>> response = new Response<>();
        response.setResult(recommendResult);
        RelatedRecommendVO relatedRecommendVO = crossShopRecommendHandler.fill(relatedRecommendCtx, response);
        Assert.assertTrue(relatedRecommendVO.getRecommendItemList().size() == 4);
    }
}
