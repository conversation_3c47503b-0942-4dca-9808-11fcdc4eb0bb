package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.DealCategoryEnum;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.EducationDealAttrUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.BaseDisplayItemVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailDisplayUnitVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailSpecificModuleVO;
import com.dianping.mobile.mapi.dztgdetail.util.EduDealUtils;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.dianping.technician.common.api.domain.TechnicianResp;
import com.dianping.tpfun.product.api.sku.aggregate.dto.AttrDTO;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.technician.info.online.dto.OnlineTechWithAttrsDTO;
import com.sankuai.technician.info.online.service.OnlineTechQueryService;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

public class SpecificModuleHandler_1205BuildUnitsForCivilExamTest {

    @InjectMocks
    private SpecificModuleHandler_1205 specificModuleHandler;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Mock
    private OnlineTechQueryService onlineTechQueryService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试所有模块信息为空的情况
     */
    @Test
    public void testBuildUnitsForCivilExamAllModulesNull() throws Throwable {
        // arrange
        SpecificModuleCtx context = mock(SpecificModuleCtx.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(context.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(EducationDealAttrUtils.getTeacherIds(dealGroupDTO)).thenReturn(null);
        when(EducationDealAttrUtils.getSuitablePeople(dealGroupDTO)).thenReturn(null);
        when(EducationDealAttrUtils.getGiftProduct(dealGroupDTO)).thenReturn(null);
        when(EducationDealAttrUtils.getSuitableClass(dealGroupDTO)).thenReturn(null);
        when(EducationDealAttrUtils.getClassSafeguard(dealGroupDTO)).thenReturn(null);
        when(EducationDealAttrUtils.getSuitableProvince(dealGroupDTO)).thenReturn(null);
        when(EducationDealAttrUtils.getClassNum(dealGroupDTO)).thenReturn(null);
        when(EducationDealAttrUtils.getAppendServices(dealGroupDTO)).thenReturn(null);
        when(EducationDealAttrUtils.getOrigClassType(dealGroupDTO)).thenReturn(null);
        when(EducationDealAttrUtils.getClassStage(dealGroupDTO)).thenReturn(null);
        // act
        Method method = SpecificModuleHandler_1205.class.getDeclaredMethod("buildUnitsForCivilExam", SpecificModuleCtx.class);
        method.setAccessible(true);
        List<DealDetailDisplayUnitVO> result = (List<DealDetailDisplayUnitVO>) method.invoke(specificModuleHandler, context);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试异常流程，某些私有方法抛出异常
     */
    @Test(expected = Exception.class)
    public void testBuildUnitsForCivilExamExceptionThrown() throws Throwable {
        // arrange
        SpecificModuleCtx context = mock(SpecificModuleCtx.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(context.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(EducationDealAttrUtils.getTeacherIds(dealGroupDTO)).thenThrow(new Exception("Test Exception"));
        // act
        Method method = SpecificModuleHandler_1205.class.getDeclaredMethod("buildUnitsForCivilExam", SpecificModuleCtx.class);
        method.setAccessible(true);
        method.invoke(specificModuleHandler, context);
        // assert
        // Exception is expected
    }
}
