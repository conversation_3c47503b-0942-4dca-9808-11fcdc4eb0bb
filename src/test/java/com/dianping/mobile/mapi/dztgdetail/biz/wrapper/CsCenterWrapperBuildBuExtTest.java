package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import java.lang.reflect.Method;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class CsCenterWrapperBuildBuExtTest {

    @Mock
    private DealCtx mockDealCtx;

    private CsCenterWrapper csCenterWrapper;

    private Method buildBuExtMethod;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        csCenterWrapper = new CsCenterWrapper();
        buildBuExtMethod = CsCenterWrapper.class.getDeclaredMethod("buildBuExt", DealCtx.class);
        buildBuExtMethod.setAccessible(true);
    }

    /**
     * Test normal scenario where both getDealId4P and getLongPoiId4PFromReq return valid values.
     */
    @Test
    public void testBuildBuExtNormalScenario() throws Throwable {
        // arrange
        when(mockDealCtx.getDealId4P()).thenReturn(12345);
        when(mockDealCtx.getLongPoiId4PFromReq()).thenReturn(67890L);
        // act
        String result = (String) buildBuExtMethod.invoke(csCenterWrapper, mockDealCtx);
        // assert
        JSONObject expectedJson = new JSONObject();
        expectedJson.put("merchandiseId", "12345");
        expectedJson.put("shopId", "67890");
        assertEquals(expectedJson.toJSONString(), result);
    }

    /**
     * Test scenario where DealCtx is null.
     */
    @Test(expected = NullPointerException.class)
    public void testBuildBuExtNullContext() throws Throwable {
        // arrange
        DealCtx nullDealCtx = null;
        try {
            // act
            buildBuExtMethod.invoke(csCenterWrapper, nullDealCtx);
        } catch (Exception e) {
            throw e.getCause();
        }
        // assert
        // Expecting NullPointerException
    }

    /**
     * Test scenario where getDealId4P returns 0 (minimum value for int)
     */
    @Test
    public void testBuildBuExtZeroDealId() throws Throwable {
        // arrange
        when(mockDealCtx.getDealId4P()).thenReturn(0);
        when(mockDealCtx.getLongPoiId4PFromReq()).thenReturn(67890L);
        // act
        String result = (String) buildBuExtMethod.invoke(csCenterWrapper, mockDealCtx);
        // assert
        JSONObject expectedJson = new JSONObject();
        expectedJson.put("merchandiseId", "0");
        expectedJson.put("shopId", "67890");
        assertEquals(expectedJson.toJSONString(), result);
    }

    /**
     * Test scenario where getLongPoiId4PFromReq returns 0 (minimum value for long)
     */
    @Test
    public void testBuildBuExtZeroShopId() throws Throwable {
        // arrange
        when(mockDealCtx.getDealId4P()).thenReturn(12345);
        when(mockDealCtx.getLongPoiId4PFromReq()).thenReturn(0L);
        // act
        String result = (String) buildBuExtMethod.invoke(csCenterWrapper, mockDealCtx);
        // assert
        JSONObject expectedJson = new JSONObject();
        expectedJson.put("merchandiseId", "12345");
        expectedJson.put("shopId", "0");
        assertEquals(expectedJson.toJSONString(), result);
    }

    /**
     * Test scenario where both getDealId4P and getLongPoiId4PFromReq return maximum values
     */
    @Test
    public void testBuildBuExtMaxValues() throws Throwable {
        // arrange
        when(mockDealCtx.getDealId4P()).thenReturn(Integer.MAX_VALUE);
        when(mockDealCtx.getLongPoiId4PFromReq()).thenReturn(Long.MAX_VALUE);
        // act
        String result = (String) buildBuExtMethod.invoke(csCenterWrapper, mockDealCtx);
        // assert
        JSONObject expectedJson = new JSONObject();
        expectedJson.put("merchandiseId", String.valueOf(Integer.MAX_VALUE));
        expectedJson.put("shopId", String.valueOf(Long.MAX_VALUE));
        assertEquals(expectedJson.toJSONString(), result);
    }

    /**
     * Test scenario where both getDealId4P and getLongPoiId4PFromReq return minimum values
     */
    @Test
    public void testBuildBuExtMinValues() throws Throwable {
        // arrange
        when(mockDealCtx.getDealId4P()).thenReturn(Integer.MIN_VALUE);
        when(mockDealCtx.getLongPoiId4PFromReq()).thenReturn(Long.MIN_VALUE);
        // act
        String result = (String) buildBuExtMethod.invoke(csCenterWrapper, mockDealCtx);
        // assert
        JSONObject expectedJson = new JSONObject();
        expectedJson.put("merchandiseId", String.valueOf(Integer.MIN_VALUE));
        expectedJson.put("shopId", String.valueOf(Long.MIN_VALUE));
        assertEquals(expectedJson.toJSONString(), result);
    }
}
