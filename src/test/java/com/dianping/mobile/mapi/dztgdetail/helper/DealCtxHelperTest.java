package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.config.InsuranceConfigDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.APP_KEY;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.PRE_ORDER_CATEGORY_IDS;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * @author: wuwenqiang
 * @create: 2024-12-17
 * @description:
 */
@RunWith(MockitoJUnitRunner.class)
public class DealCtxHelperTest {
    @Mock
    private DealCtx dealCtx;

    private MockedStatic<Lion> lionMockedStatic;
    @Before
    public void setUp() {
        lionMockedStatic = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        lionMockedStatic.close();
    }


    @Test
    public void testIsPreOrderDeal_JudgeMainAppTrue() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(dealCtx.getRequestSource()).thenReturn(RequestSourceEnum.PRE_ORDER_DEAL.getSource());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(409L);
        dealGroupDTO.setCategory(categoryDTO);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        lionMockedStatic.when(() -> Lion.getList(APP_KEY, PRE_ORDER_CATEGORY_IDS, String.class, Collections.emptyList())).thenReturn(Arrays.asList("409", "459"));

        boolean result = DealCtxHelper.isPreOrderDeal(dealCtx);
        assertTrue(result);
    }

    @Test
    public void testIsPreOrderDeal_RequestSourceNotMatch() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(dealCtx.getRequestSource()).thenReturn("OTHER_SOURCE");

        boolean result = DealCtxHelper.isPreOrderDeal(dealCtx);
        assertFalse(result);
    }

    // ========== isAssuredImplant 方法测试 ==========

    @Test
    public void testIsAssuredImplant_AllConditionsMet_ReturnsTrue() {
        // 准备测试数据
        DealCtx ctx = createDealCtx();
        InsuranceConfigDTO insuranceConfigDTO = createInsuranceConfigDTO();

        // 设置 usePlatformHeadPic 为 true
        ctx.setUsePlatformHeadPic(true);

        // 设置团单信息
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO category = new DealGroupCategoryDTO();
        category.setCategoryId(100L);
        category.setServiceTypeId(1L);
        dealGroupDTO.setCategory(category);

        // 设置商品标签
        DealGroupTagDTO productTag = new DealGroupTagDTO();
        productTag.setId(1001L);
        dealGroupDTO.setTags(Arrays.asList(productTag));
        ctx.setDealGroupDTO(dealGroupDTO);

        // 设置门店标签
        DisplayTagDto shopTag = new DisplayTagDto();
        shopTag.setTagId(2001L);
        Map<Long, List<DisplayTagDto>> shopTagsMap = new HashMap<>();
        shopTagsMap.put(123L, Arrays.asList(shopTag));
        ctx.setDpShopId2TagsMap(shopTagsMap);
        ctx.setDpLongShopId(123L);

        // 执行测试
        boolean result = DealCtxHelper.isAssuredImplant(ctx, insuranceConfigDTO);

        // 验证结果
        assertTrue(result);
    }

    @Test
    public void testIsAssuredImplant_UsePlatformHeadPicNull_ReturnsFalse() {
        // 准备测试数据
        DealCtx ctx = createDealCtx();
        InsuranceConfigDTO insuranceConfigDTO = createInsuranceConfigDTO();

        // 设置 usePlatformHeadPic 为 null
        ctx.setUsePlatformHeadPic(null);

        // 设置其他必要数据
        setupBasicDealData(ctx);

        // 执行测试
        boolean result = DealCtxHelper.isAssuredImplant(ctx, insuranceConfigDTO);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void testIsAssuredImplant_UsePlatformHeadPicFalse_ReturnsFalse() {
        // 准备测试数据
        DealCtx ctx = createDealCtx();
        InsuranceConfigDTO insuranceConfigDTO = createInsuranceConfigDTO();

        // 设置 usePlatformHeadPic 为 false
        ctx.setUsePlatformHeadPic(false);

        // 设置其他必要数据
        setupBasicDealData(ctx);

        // 执行测试
        boolean result = DealCtxHelper.isAssuredImplant(ctx, insuranceConfigDTO);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void testIsAssuredImplant_ShopTagsMapEmpty_ReturnsFalse() {
        // 准备测试数据
        DealCtx ctx = createDealCtx();
        InsuranceConfigDTO insuranceConfigDTO = createInsuranceConfigDTO();

        // 设置 usePlatformHeadPic 为 true
        ctx.setUsePlatformHeadPic(true);

        // 设置团单信息但不设置门店标签
        setupBasicDealData(ctx);
        ctx.setDpShopId2TagsMap(new HashMap<>());

        // 执行测试
        boolean result = DealCtxHelper.isAssuredImplant(ctx, insuranceConfigDTO);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void testIsAssuredImplant_ShopTagsMapNull_ReturnsFalse() {
        // 准备测试数据
        DealCtx ctx = createDealCtx();
        InsuranceConfigDTO insuranceConfigDTO = createInsuranceConfigDTO();

        // 设置 usePlatformHeadPic 为 true
        ctx.setUsePlatformHeadPic(true);

        // 设置团单信息但不设置门店标签
        setupBasicDealData(ctx);
        ctx.setDpShopId2TagsMap(null);

        // 执行测试
        boolean result = DealCtxHelper.isAssuredImplant(ctx, insuranceConfigDTO);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void testIsAssuredImplant_ShopTagsListEmpty_ReturnsFalse() {
        // 准备测试数据
        DealCtx ctx = createDealCtx();
        InsuranceConfigDTO insuranceConfigDTO = createInsuranceConfigDTO();

        // 设置 usePlatformHeadPic 为 true
        ctx.setUsePlatformHeadPic(true);

        // 设置团单信息
        setupBasicDealData(ctx);

        // 设置空的门店标签列表
        Map<Long, List<DisplayTagDto>> shopTagsMap = new HashMap<>();
        shopTagsMap.put(123L, new ArrayList<>());
        ctx.setDpShopId2TagsMap(shopTagsMap);
        ctx.setDpLongShopId(123L);

        // 执行测试
        boolean result = DealCtxHelper.isAssuredImplant(ctx, insuranceConfigDTO);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void testIsAssuredImplant_ProductTagNotMatch_ReturnsFalse() {
        // 准备测试数据
        DealCtx ctx = createDealCtx();
        InsuranceConfigDTO insuranceConfigDTO = createInsuranceConfigDTO();

        // 设置 usePlatformHeadPic 为 true
        ctx.setUsePlatformHeadPic(true);

        // 设置团单信息，但商品标签不匹配
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO category = new DealGroupCategoryDTO();
        category.setCategoryId(100L);
        category.setServiceTypeId(1L);
        dealGroupDTO.setCategory(category);

        // 设置不匹配的商品标签
        DealGroupTagDTO productTag = new DealGroupTagDTO();
        productTag.setId(9999L); // 不在配置中的标签ID
        dealGroupDTO.setTags(Arrays.asList(productTag));
        ctx.setDealGroupDTO(dealGroupDTO);

        // 设置门店标签
        DisplayTagDto shopTag = new DisplayTagDto();
        shopTag.setTagId(2001L);
        Map<Long, List<DisplayTagDto>> shopTagsMap = new HashMap<>();
        shopTagsMap.put(123L, Arrays.asList(shopTag));
        ctx.setDpShopId2TagsMap(shopTagsMap);
        ctx.setDpLongShopId(123L);

        // 执行测试
        boolean result = DealCtxHelper.isAssuredImplant(ctx, insuranceConfigDTO);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void testIsAssuredImplant_ShopTagNotMatch_ReturnsFalse() {
        // 准备测试数据
        DealCtx ctx = createDealCtx();
        InsuranceConfigDTO insuranceConfigDTO = createInsuranceConfigDTO();

        // 设置 usePlatformHeadPic 为 true
        ctx.setUsePlatformHeadPic(true);

        // 设置团单信息
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO category = new DealGroupCategoryDTO();
        category.setCategoryId(100L);
        category.setServiceTypeId(1L);
        dealGroupDTO.setCategory(category);

        // 设置匹配的商品标签
        DealGroupTagDTO productTag = new DealGroupTagDTO();
        productTag.setId(1001L);
        dealGroupDTO.setTags(Arrays.asList(productTag));
        ctx.setDealGroupDTO(dealGroupDTO);

        // 设置不匹配的门店标签
        DisplayTagDto shopTag = new DisplayTagDto();
        shopTag.setTagId(9999L); // 不在配置中的标签ID
        Map<Long, List<DisplayTagDto>> shopTagsMap = new HashMap<>();
        shopTagsMap.put(123L, Arrays.asList(shopTag));
        ctx.setDpShopId2TagsMap(shopTagsMap);
        ctx.setDpLongShopId(123L);

        // 执行测试
        boolean result = DealCtxHelper.isAssuredImplant(ctx, insuranceConfigDTO);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void testIsAssuredImplant_InsuranceConfigNull_ReturnsFalse() {
        // 准备测试数据
        DealCtx ctx = createDealCtx();

        // 设置 usePlatformHeadPic 为 true
        ctx.setUsePlatformHeadPic(true);

        // 设置其他必要数据
        setupBasicDealData(ctx);

        // 执行测试，传入 null 的 insuranceConfigDTO
        boolean result = DealCtxHelper.isAssuredImplant(ctx, null);

        // 验证结果
        assertFalse(result);
    }

    // ========== 辅助方法 ==========

    private DealCtx createDealCtx() {
        EnvCtx envCtx = new EnvCtx();
        return new DealCtx(envCtx);
    }

    private InsuranceConfigDTO createInsuranceConfigDTO() {
        InsuranceConfigDTO config = new InsuranceConfigDTO();

        // 设置商品标签配置
        Map<String, List<Long>> productTagConfigMap = new HashMap<>();
        productTagConfigMap.put("100", Arrays.asList(1001L, 1002L));
        productTagConfigMap.put("100.1", Arrays.asList(1001L, 1003L));
        config.setProductTagConfigMap(productTagConfigMap);

        // 设置门店标签配置
        Map<String, List<Long>> shopTagConfigMap = new HashMap<>();
        shopTagConfigMap.put("100", Arrays.asList(2001L, 2002L));
        shopTagConfigMap.put("100.1", Arrays.asList(2001L, 2003L));
        config.setShopTagConfigMap(shopTagConfigMap);

        return config;
    }

    private void setupBasicDealData(DealCtx ctx) {
        // 设置团单信息
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO category = new DealGroupCategoryDTO();
        category.setCategoryId(100L);
        category.setServiceTypeId(1L);
        dealGroupDTO.setCategory(category);

        // 设置商品标签
        DealGroupTagDTO productTag = new DealGroupTagDTO();
        productTag.setId(1001L);
        dealGroupDTO.setTags(Arrays.asList(productTag));
        ctx.setDealGroupDTO(dealGroupDTO);

        // 设置门店标签
        DisplayTagDto shopTag = new DisplayTagDto();
        shopTag.setTagId(2001L);
        Map<Long, List<DisplayTagDto>> shopTagsMap = new HashMap<>();
        shopTagsMap.put(123L, Arrays.asList(shopTag));
        ctx.setDpShopId2TagsMap(shopTagsMap);
        ctx.setDpLongShopId(123L);
    }

}
