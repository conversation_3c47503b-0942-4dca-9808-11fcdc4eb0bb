package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealCompareHelper_DifferDpCityIdTest {

    /**
     * 测试两个城市ID相等的情况
     */
    @Test
    public void testDifferDpCityIdEqual() throws Throwable {
        // arrange
        int newCityId = 1;
        int oldCityId = 1;
        int dealId = 1;
        // act
        boolean result = DealCompareHelper.differDpCityId(newCityId, oldCityId, dealId);
        // assert
        assertTrue(result);
    }

    /**
     * 测试两个城市ID不相等的情况
     */
    @Test
    public void testDifferDpCityIdNotEqual() throws Throwable {
        // arrange
        int newCityId = 1;
        int oldCityId = 2;
        int dealId = 1;
        // act
        boolean result = DealCompareHelper.differDpCityId(newCityId, oldCityId, dealId);
        // assert
        assertFalse(result);
    }
}
