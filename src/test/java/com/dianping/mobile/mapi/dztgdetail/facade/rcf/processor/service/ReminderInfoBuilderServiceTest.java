package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.Guarantee;
import com.dianping.mobile.mapi.dztgdetail.entity.ReminderExtendConfig;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.unavailable.UnavailabelDate;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.unavailable.UnavailableDateConfigService;
import com.dianping.mobile.mapi.dztgdetail.helper.DateHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.AvailableDateDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.AvailableDurationDateDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.CycleAvailableDateDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DateRangeDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DisableDateDTO;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDate;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ReminderInfoBuilderServiceTest {

    @InjectMocks
    private ReminderInfoBuilderService reminderInfoBuilderService;

    @Mock
    private UnavailableDateConfigService dateConfig;

    private MockedStatic<LionConfigUtils> lionConfigUtilsMockedStatic;
    private MockedStatic<DealAttrHelper> dealAttrHelperMockedStatic;
    private MockedStatic<DateHelper> dateHelperMockedStatic;

    @Before
    public void setUp() {
        lionConfigUtilsMockedStatic = mockStatic(LionConfigUtils.class);
        dealAttrHelperMockedStatic = mockStatic(DealAttrHelper.class);
        dateHelperMockedStatic = mockStatic(DateHelper.class);
    }

    @After
    public void tearDown() {
        if (lionConfigUtilsMockedStatic != null) {
            lionConfigUtilsMockedStatic.close();
        }
        if (dealAttrHelperMockedStatic != null) {
            dealAttrHelperMockedStatic.close();
        }
        if (dateHelperMockedStatic != null) {
            dateHelperMockedStatic.close();
        }
    }

    @Test
    public void testBuildSpringFestivalBanner_ValidConfigButDisabled() {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setDealGroupDTO(new DealGroupDTO());
        DealGroupPBO result = new DealGroupPBO();

        ReminderExtendConfig config = new ReminderExtendConfig();
        Map<String, String> reminderInfo = new HashMap<>();
        reminderInfo.put("text", "春节不打烊");
        reminderInfo.put("disableText", "春节期间不可用");
        reminderInfo.put("icon", "icon_url");
        reminderInfo.put("type", "1");
        reminderInfo.put("style", "#FF0000");
        config.setReminderInfo(reminderInfo);

        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.getReminderExtendInfo("springFestival"))
                .thenReturn(config);
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.validSpringFestivalConfig(config))
                .thenReturn(true);
        dealAttrHelperMockedStatic.when(() -> DealAttrHelper.judgeDisableUsable(any(DealGroupDTO.class)))
                .thenReturn(true);

        // act
        reminderInfoBuilderService.buildSpringFestivalBanner(ctx, result);

        // assert
        assertNotNull(result.getReminderExtend());
        assertEquals(1, result.getReminderExtend().size());
        assertEquals("春节期间不可用", result.getReminderExtend().get(0).getText());
    }

    @Test
    public void testBuildSpringFestivalBanner_InvalidConfig() {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setDealGroupDTO(new DealGroupDTO());
        DealGroupPBO result = new DealGroupPBO();

        ReminderExtendConfig config = new ReminderExtendConfig();
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.getReminderExtendInfo("springFestival"))
                .thenReturn(config);
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.validSpringFestivalConfig(config))
                .thenReturn(false);

        // act
        reminderInfoBuilderService.buildSpringFestivalBanner(ctx, result);

        // assert
        // 由于配置无效，会调用handleDisabledDate方法，这里主要验证不会因为配置问题抛异常
        assertNotNull(result);
    }

    @Test
    public void testHitAvailableTime_NullInput() {
        // act
        boolean result = reminderInfoBuilderService.hitAvailableTime(null);

        // assert
        assertTrue(result);
    }

    @Test
    public void testHitAvailableTime_CycleType() {
        // arrange
        AvailableDateDTO availableDateDTO = new AvailableDateDTO();
        availableDateDTO.setAvailableType(0);

        CycleAvailableDateDTO cycleDate = new CycleAvailableDateDTO();
        cycleDate.setAvailableDays(Arrays.asList(1, 2, 3, 4, 5)); // 工作日
        availableDateDTO.setCycleAvailableDateList(Arrays.asList(cycleDate));

        // Mock当前是周一(1)
        ReminderInfoBuilderService spyService = spy(reminderInfoBuilderService);
        doReturn(1).when(spyService).dayOfWeek();

        // act
        boolean result = spyService.hitAvailableTime(availableDateDTO);

        // assert
        assertTrue(result);
    }

    @Test
    public void testHitAvailableTime_SpecifiedDateType() {
        // arrange
        AvailableDateDTO availableDateDTO = new AvailableDateDTO();
        availableDateDTO.setAvailableType(1);

        AvailableDurationDateDTO durationDate = new AvailableDurationDateDTO();
        DateRangeDTO dateRange = new DateRangeDTO();
        dateRange.setFrom("2024-01-01");
        dateRange.setTo("2024-12-31");
        durationDate.setAvailableDateRangeDTOS(Arrays.asList(dateRange));
        availableDateDTO.setSpecifiedDurationDateList(Arrays.asList(durationDate));

        dateHelperMockedStatic.when(() -> DateHelper.isCurrentDateInRange("2024-01-01", "2024-12-31"))
                .thenReturn(true);

        // act
        boolean result = reminderInfoBuilderService.hitAvailableTime(availableDateDTO);

        // assert
        assertTrue(result);
    }

    @Test
    public void testValidSpecifiedDurationDateList_ValidRange() {
        // arrange
        AvailableDurationDateDTO durationDate = new AvailableDurationDateDTO();
        DateRangeDTO dateRange = new DateRangeDTO();
        dateRange.setFrom("2024-01-01");
        dateRange.setTo("2024-12-31");
        durationDate.setAvailableDateRangeDTOS(Arrays.asList(dateRange));

        dateHelperMockedStatic.when(() -> DateHelper.isCurrentDateInRange("2024-01-01", "2024-12-31"))
                .thenReturn(true);

        // act
        boolean result = reminderInfoBuilderService.validSpecifiedDurationDateList(Arrays.asList(durationDate));

        // assert
        assertTrue(result);
    }

    @Test
    public void testValidSpecifiedDurationDateList_NullInput() {
        // act
        boolean result = reminderInfoBuilderService.validSpecifiedDurationDateList(null);

        // assert
        assertFalse(result);
    }

    @Test
    public void testValidAvailableDateRange_ValidRange() {
        // arrange
        DateRangeDTO dateRange = new DateRangeDTO();
        dateRange.setFrom("2024-01-01");
        dateRange.setTo("2024-12-31");

        dateHelperMockedStatic.when(() -> DateHelper.isCurrentDateInRange("2024-01-01", "2024-12-31"))
                .thenReturn(true);

        // act
        boolean result = ReminderInfoBuilderService.validAvailableDateRange(Arrays.asList(dateRange));

        // assert
        assertTrue(result);
    }

    /**
     * Test handleDisabledDate method - hit weekday unavailable
     */
    @Test
    public void testHandleDisabledDate_HitWeekdayUnavailable() {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();

        // 设置 TimeRange3 属性
        AttrDTO timeRangeAttr = new AttrDTO();
        timeRangeAttr.setName("TimeRange3");
        timeRangeAttr.setValue(Arrays.asList("周一,周二,周三,周四,周五")); // 工作日可用
        dealGroupDTO.setAttrs(Arrays.asList(timeRangeAttr));
        ctx.setDealGroupDTO(dealGroupDTO);

        List<Guarantee> reminderExtend = new ArrayList<>();

        // Mock AvailableTimeHelper
        try (MockedStatic<com.dianping.mobile.mapi.dztgdetail.helper.AvailableTimeHelper> availableTimeHelperMock =
                     mockStatic(com.dianping.mobile.mapi.dztgdetail.helper.AvailableTimeHelper.class)) {

            availableTimeHelperMock.when(() ->
                            com.dianping.mobile.mapi.dztgdetail.helper.AvailableTimeHelper.getWeekDayInt(Collections.singletonList("周一,周二,周三,周四,周五")))
                    .thenReturn(Arrays.asList(1, 2, 3, 4, 5));
            availableTimeHelperMock.when(() ->
                            com.dianping.mobile.mapi.dztgdetail.helper.AvailableTimeHelper.getTodayWeekDay())
                    .thenReturn(6); // 今天是周六，不在可用范围内

            // act
            reminderInfoBuilderService.handleDisabledDate(reminderExtend, ctx);

            // assert
            assertEquals(1, reminderExtend.size());
            assertEquals("该团购今日无法在门店使用，具体情况请联系门店确认", reminderExtend.get(0).getText());
        }
    }

    /**
     * Test handleDisabledDate method - hit week and holiday unavailable
     */
    @Test
    public void testHandleDisabledDate_HitWeekAndHolidayUnavailable() {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();

        // 设置规则
        DealGroupRuleDTO rule = new DealGroupRuleDTO();
        DealGroupUseRuleDTO useRule = new DealGroupUseRuleDTO();
        DisableDateDTO disableDate = new DisableDateDTO();
        disableDate.setDisableDays(Arrays.asList(1, 8)); // 周一和节假日不可用
        useRule.setDisableDate(disableDate);
        rule.setUseRule(useRule);
        dealGroupDTO.setRule(rule);
        dealGroupDTO.setAttrs(new ArrayList<>());
        ctx.setDealGroupDTO(dealGroupDTO);

        List<Guarantee> reminderExtend = new ArrayList<>();

        // Mock 配置
        Map<String, UnavailabelDate> configMap = new HashMap<>();
        when(dateConfig.getConfig()).thenReturn(configMap);

        ReminderInfoBuilderService spyService = spy(reminderInfoBuilderService);
        doReturn(true).when(spyService).hitWeekAndHolidayUnavailable(any(), any());

        // act
        spyService.handleDisabledDate(reminderExtend, ctx);

        // assert
        assertEquals(1, reminderExtend.size());
        assertEquals("该团购今日无法在门店使用，具体情况请联系门店确认", reminderExtend.get(0).getText());
    }

    /**
     * Test handleDisabledDate method - hit custom unavailable
     */
    @Test
    public void testHandleDisabledDate_HitCustomUnavailable() {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();

        // 设置规则
        DealGroupRuleDTO rule = new DealGroupRuleDTO();
        DealGroupUseRuleDTO useRule = new DealGroupUseRuleDTO();
        DisableDateDTO disableDate = new DisableDateDTO();

        DateRangeDTO dateRange = new DateRangeDTO();
        dateRange.setFrom("2024-01-01");
        dateRange.setTo("2024-12-31");
        disableDate.setDisableDateRangeDTOS(Arrays.asList(dateRange));
        disableDate.setDisableDays(new ArrayList<>());

        useRule.setDisableDate(disableDate);
        rule.setUseRule(useRule);
        dealGroupDTO.setRule(rule);
        dealGroupDTO.setAttrs(new ArrayList<>());
        ctx.setDealGroupDTO(dealGroupDTO);

        List<Guarantee> reminderExtend = new ArrayList<>();

        ReminderInfoBuilderService spyService = spy(reminderInfoBuilderService);
        doReturn(true).when(spyService).hitCustomUnavailable(any());

        // act
        spyService.handleDisabledDate(reminderExtend, ctx);

        // assert
        assertEquals(1, reminderExtend.size());
        assertEquals("该团购今日无法在门店使用，具体情况请联系门店确认", reminderExtend.get(0).getText());
    }

    /**
     * Test handleDisabledDate method - hit weekend unavailable
     */
    @Test
    public void testHandleDisabledDate_HitWeekendUnavailable() {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setSkuId("12345");

        DealGroupDTO dealGroupDTO = new DealGroupDTO();

        // 设置 deal 属性
        DealGroupDealDTO deal = new DealGroupDealDTO();
        deal.setDealId(12345L);

        AttrDTO weekendTimeAttr = new AttrDTO();
        weekendTimeAttr.setName("weekendTime");
        weekendTimeAttr.setValue(Arrays.asList("[1,2,3,4,5]")); // 只有工作日可用
        deal.setAttrs(Arrays.asList(weekendTimeAttr));

        dealGroupDTO.setDeals(Arrays.asList(deal));
        dealGroupDTO.setAttrs(new ArrayList<>());

        // 设置空的规则以跳过前面的判断
        DealGroupRuleDTO rule = new DealGroupRuleDTO();
        DealGroupUseRuleDTO useRule = new DealGroupUseRuleDTO();
        DisableDateDTO disableDate = new DisableDateDTO();
        disableDate.setDisableDays(new ArrayList<>());
        disableDate.setDisableDateRangeDTOS(new ArrayList<>());
        useRule.setDisableDate(disableDate);
        rule.setUseRule(useRule);
        dealGroupDTO.setRule(rule);

        ctx.setDealGroupDTO(dealGroupDTO);

        List<Guarantee> reminderExtend = new ArrayList<>();

        ReminderInfoBuilderService spyService = spy(reminderInfoBuilderService);
        doReturn(true).when(spyService).hitWeekendUnavailable("[1,2,3,4,5]");

        // act
        spyService.handleDisabledDate(reminderExtend, ctx);

        // assert
        assertEquals(1, reminderExtend.size());
        assertEquals("该团购今日无法在门店使用，具体情况请联系门店确认", reminderExtend.get(0).getText());
    }

    /**
     * Test handleDisabledDate method - no unavailable conditions hit
     */
    @Test
    public void testHandleDisabledDate_NoUnavailableConditions() {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setSkuId("12345");

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setAttrs(new ArrayList<>());
        dealGroupDTO.setDeals(new ArrayList<>());

        // 设置空的规则
        DealGroupRuleDTO rule = new DealGroupRuleDTO();
        DealGroupUseRuleDTO useRule = new DealGroupUseRuleDTO();
        DisableDateDTO disableDate = new DisableDateDTO();
        disableDate.setDisableDays(new ArrayList<>());
        disableDate.setDisableDateRangeDTOS(new ArrayList<>());
        useRule.setDisableDate(disableDate);
        rule.setUseRule(useRule);
        dealGroupDTO.setRule(rule);

        ctx.setDealGroupDTO(dealGroupDTO);

        List<Guarantee> reminderExtend = new ArrayList<>();

        // act
        reminderInfoBuilderService.handleDisabledDate(reminderExtend, ctx);

        // assert
        assertEquals(0, reminderExtend.size()); // 没有命中任何不可用条件
    }

    @Test
    public void testValidAvailableDateRange_EmptyList() {
        // act
        boolean result = ReminderInfoBuilderService.validAvailableDateRange(Collections.emptyList());

        // assert
        assertFalse(result);
    }

    @Test
    public void testHitWeekendUnavailable_Available() {
        // arrange
        String weekendTime = "[1,2,3,4,5]"; // 工作日可用
        ReminderInfoBuilderService spyService = spy(reminderInfoBuilderService);
        doReturn(1).when(spyService).dayOfWeek(); // 当前是周一

        // act
        boolean result = spyService.hitWeekendUnavailable(weekendTime);

        // assert
        assertFalse(result); // 周一可用，所以不命中不可用
    }

    @Test
    public void testHitWeekendUnavailable_Unavailable() {
        // arrange
        String weekendTime = "[1,2,3,4,5]"; // 工作日可用
        ReminderInfoBuilderService spyService = spy(reminderInfoBuilderService);
        doReturn(6).when(spyService).dayOfWeek(); // 当前是周六

        // act
        boolean result = spyService.hitWeekendUnavailable(weekendTime);

        // assert
        assertTrue(result); // 周六不可用，所以命中不可用
    }

    @Test
    public void testHitWeekendUnavailable_BlankInput() {
        // act
        boolean result = reminderInfoBuilderService.hitWeekendUnavailable("");

        // assert
        assertFalse(result);
    }

    @Test
    public void testHitCustomUnavailable_InRange() {
        // arrange
        DateRangeDTO dateRange = new DateRangeDTO();
        dateRange.setFrom("2024-01-01");
        dateRange.setTo("2024-12-31");

        dateHelperMockedStatic.when(() -> DateHelper.isCurrentDateInRange("2024-01-01", "2024-12-31"))
                .thenReturn(true);

        // act
        boolean result = reminderInfoBuilderService.hitCustomUnavailable(Arrays.asList(dateRange));

        // assert
        assertTrue(result);
    }

    @Test
    public void testHitCustomUnavailable_NotInRange() {
        // arrange
        DateRangeDTO dateRange = new DateRangeDTO();
        dateRange.setFrom("2024-01-01");
        dateRange.setTo("2024-01-31");

        dateHelperMockedStatic.when(() -> DateHelper.isCurrentDateInRange("2024-01-01", "2024-01-31"))
                .thenReturn(false);

        // act
        boolean result = reminderInfoBuilderService.hitCustomUnavailable(Arrays.asList(dateRange));

        // assert
        assertFalse(result);
    }

    @Test
    public void testHitCustomUnavailable_EmptyList() {
        // act
        boolean result = reminderInfoBuilderService.hitCustomUnavailable(Collections.emptyList());

        // assert
        assertFalse(result);
    }

    @Test
    public void testHitWeekAndHolidayUnavailable_HitWeekDay() {
        // arrange
        List<Integer> disableDays = Arrays.asList(1, 8); // 周一不可用，8是节假日标识
        Map<String, UnavailabelDate> unavailabelDateMap = new HashMap<>();

        ReminderInfoBuilderService spyService = spy(reminderInfoBuilderService);
        doReturn(1).when(spyService).dayOfWeek(); // 当前是周一

        // act
        boolean result = spyService.hitWeekAndHolidayUnavailable(disableDays, unavailabelDateMap);

        // assert
        assertTrue(result); // 命中周一不可用
    }

    @Test
    public void testHitWeekAndHolidayUnavailable_HitHoliday() {
        // arrange
        List<Integer> disableDays = Arrays.asList(8); // 8是节假日标识
        Map<String, UnavailabelDate> unavailabelDateMap = new HashMap<>();
        UnavailabelDate holidayDate = new UnavailabelDate();
        holidayDate.setFrom("2024-01-01");
        holidayDate.setTo("2024-01-03");
        unavailabelDateMap.put("8", holidayDate);

        ReminderInfoBuilderService spyService = spy(reminderInfoBuilderService);
        doReturn(2).when(spyService).dayOfWeek(); // 当前是周二，不在周不可用范围内

        dateHelperMockedStatic.when(() -> DateHelper.isCurrentDateInRange("2024-01-01", "2024-01-03"))
                .thenReturn(true);

        // act
        boolean result = spyService.hitWeekAndHolidayUnavailable(disableDays, unavailabelDateMap);

        // assert
        assertTrue(result); // 命中节假日不可用
    }

    @Test
    public void testHitWeekAndHolidayUnavailable_EmptyDisableDays() {
        // arrange
        Map<String, UnavailabelDate> unavailabelDateMap = new HashMap<>();

        // act
        boolean result = reminderInfoBuilderService.hitWeekAndHolidayUnavailable(Collections.emptyList(), unavailabelDateMap);

        // assert
        assertFalse(result);
    }

    @Test
    public void testDayOfWeek() {
        // act
        int dayOfWeek = reminderInfoBuilderService.dayOfWeek();

        // assert
        assertTrue(dayOfWeek >= 1 && dayOfWeek <= 7);
    }

    @Test
    public void testProcessDisabledTips() {
        // act
        Guarantee result = reminderInfoBuilderService.processDisabledTips();

        // assert
        assertNotNull(result);
        assertEquals("该团购今日无法在门店使用，具体情况请联系门店确认", result.getText());
        assertEquals("", result.getIcon());
        assertEquals(1, result.getType());
        assertEquals("#222222", result.getStyle());
    }

    @Test
    public void testBuildGuarantee() {
        // act
        Guarantee guarantee = reminderInfoBuilderService.buildGuarantee("test text", "test icon", 2, "#FFFFFF");

        // assert
        assertNotNull(guarantee);
        assertEquals("test text", guarantee.getText());
        assertEquals("test icon", guarantee.getIcon());
        assertEquals(2, guarantee.getType());
        assertEquals("#FFFFFF", guarantee.getStyle());
    }

    @Test
    public void testGetType_ValidNumber() {
        // act
        int result = reminderInfoBuilderService.getType("123");

        // assert
        assertEquals(123, result);
    }

    @Test
    public void testGetType_InvalidNumber() {
        // act
        int result = reminderInfoBuilderService.getType("abc");

        // assert
        assertEquals(-1, result);
    }

    @Test
    public void testGetType_NullInput() {
        // act
        int result = reminderInfoBuilderService.getType(null);

        // assert
        assertEquals(-1, result);
    }

    @Test
    public void testGetDisplayText_SpringFestivalDisabled() {
        // arrange
        Map<String, String> reminderInfoMap = new HashMap<>();
        reminderInfoMap.put("disableText", "春节期间不可用");
        reminderInfoMap.put("text", "春节不打烊");

        // act
        String result = reminderInfoBuilderService.getDisplayText(reminderInfoMap, true, true);

        // assert
        assertEquals("春节期间不可用", result);
    }

    @Test
    public void testGetDisplayText_SpringFestivalEnabled() {
        // arrange
        Map<String, String> reminderInfoMap = new HashMap<>();
        reminderInfoMap.put("disableText", "春节期间不可用");
        reminderInfoMap.put("text", "春节不打烊");

        // act
        String result = reminderInfoBuilderService.getDisplayText(reminderInfoMap, true, false);

        // assert
        assertEquals("春节不打烊", result);
    }

    @Test
    public void testGetDisplayText_NotSpringFestival() {
        // arrange
        Map<String, String> reminderInfoMap = new HashMap<>();
        reminderInfoMap.put("disableText", "春节期间不可用");
        reminderInfoMap.put("text", "春节不打烊");

        // act
        String result = reminderInfoBuilderService.getDisplayText(reminderInfoMap, false, true);

        // assert
        assertEquals("春节不打烊", result);
    }

    @Test
    public void testIsNumeric_ValidInteger() {
        // act
        boolean result = ReminderInfoBuilderService.isNumeric("123");

        // assert
        assertTrue(result);
    }

    @Test
    public void testIsNumeric_ValidDecimal() {
        // act
        boolean result = ReminderInfoBuilderService.isNumeric("123.45");

        // assert
        assertTrue(result);
    }

    @Test
    public void testIsNumeric_ValidNegative() {
        // act
        boolean result = ReminderInfoBuilderService.isNumeric("-123");

        // assert
        assertTrue(result);
    }

    @Test
    public void testIsNumeric_InvalidString() {
        // act
        boolean result = ReminderInfoBuilderService.isNumeric("abc");

        // assert
        assertFalse(result);
    }

    @Test
    public void testIsNumeric_NullInput() {
        // act
        boolean result = ReminderInfoBuilderService.isNumeric(null);

        // assert
        assertFalse(result);
    }

    @Test
    public void testIsNumeric_EmptyString() {
        // act
        boolean result = ReminderInfoBuilderService.isNumeric("");

        // assert
        assertFalse(result);
    }

    @Test
    public void testBuildSpringFestivalBanner(){
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupPBO result = new DealGroupPBO();
        ReminderInfoBuilderService reminderInfoBuilderService = new ReminderInfoBuilderService();
        reminderInfoBuilderService.buildSpringFestivalBanner(ctx, result);
        Assert.assertNotNull(result);
    }

    @Test
    public void testGetType(){
        int type = reminderInfoBuilderService.getType("1");
        Assert.assertTrue(type == 1);
    }

    @Test
    public void testGetDisplayText(){
        Map<String, String> reminderInfoMap = new HashMap<>();
        reminderInfoMap.put("disableText", "disableText");
        reminderInfoMap.put("text", "text");
        boolean springFestivalDisabled = true;
        String displayText = reminderInfoBuilderService.getDisplayText(reminderInfoMap, true, springFestivalDisabled);
        springFestivalDisabled = false;
        displayText = reminderInfoBuilderService.getDisplayText(reminderInfoMap, false, springFestivalDisabled);
        Assert.assertNotNull(displayText);
    }

    @Test
    public void testValidSpringFestivalConfig(){
        Map<String, String> reminderInfoMap = new HashMap<>();
        reminderInfoMap.put("disableText", "disableText");
        reminderInfoMap.put("text", "text");
        ReminderExtendConfig config = new ReminderExtendConfig();
        config.setReminderInfo(reminderInfoMap);
        config.setStartTime("2024-02-05");
        config.setEndTime("2025-02-05");
        Boolean result = LionConfigUtils.validSpringFestivalConfig(config);
        Assert.assertFalse(result);
    }
}
