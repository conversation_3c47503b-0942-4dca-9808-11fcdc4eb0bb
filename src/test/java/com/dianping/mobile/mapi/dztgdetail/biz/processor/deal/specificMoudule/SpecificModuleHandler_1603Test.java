package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule;

import static groovy.util.GroovyTestCase.assertEquals;
import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailSpecificModuleVO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class SpecificModuleHandler_1603Test {

    @Mock
    private SpecificModuleCtx mockCtx;

    private SpecificModuleHandler_1603 handler;

    private SpecificModuleHandler_1603 specificModuleHandler_1603 = new SpecificModuleHandler_1603();

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        handler = new SpecificModuleHandler_1603();
    }

    private String invokePrivateMethod(List<ServiceProjectAttrDTO> serviceProjectAttributes, String attrName) throws Exception {
        Method method = SpecificModuleHandler_1603.class.getDeclaredMethod("getServiceProjectAttrValue", List.class, String.class);
        method.setAccessible(true);
        return (String) method.invoke(specificModuleHandler_1603, serviceProjectAttributes, attrName);
    }

    /**
     * 测试正常场景下的服务流程构建
     */
    @Test
    public void testBuildResultNormalCase() throws Throwable {
        // arrange
        String json = "[{\"processName\":\"检查\",\"processDescription\":\"进行初步检查\"}]";
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("standardServiceProcess");
        serviceProjectAttrDTO.setAttrValue(json);
        List<ServiceProjectAttrDTO> attrs = Collections.singletonList(serviceProjectAttrDTO);
        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        serviceProjectDTO.setAttrs(attrs);
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        mustGroup.setGroups(Collections.singletonList(serviceProjectDTO));
        DealGroupServiceProjectDTO serviceProject = new DealGroupServiceProjectDTO();
        serviceProject.setMustGroups(Collections.singletonList(mustGroup));
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setServiceProject(serviceProject);
        when(mockCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        // act
        DealDetailSpecificModuleVO result = handler.buildResult(mockCtx);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getUnits().size());
        assertEquals("服务流程", result.getUnits().get(0).getTitle());
        assertEquals(1, result.getUnits().get(0).getDisplayItems().size());
        assertEquals("检查", result.getUnits().get(0).getDisplayItems().get(0).getName());
    }

    /**
     * 测试服务项目属性为空的场景
     */
    @Test
    public void testBuildResultWithEmptyAttributes() throws Throwable {
        // arrange
        when(mockCtx.getDealGroupDTO()).thenReturn(null);
        // act
        DealDetailSpecificModuleVO result = handler.buildResult(mockCtx);
        // assert
        assertNotNull(result);
        assertTrue(result.getUnits().isEmpty());
    }

    /**
     * 测试服务流程属性缺失的场景
     */
    @Test
    public void testBuildResultWithMissingServiceProcess() throws Throwable {
        // arrange
        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        // Empty attributes
        serviceProjectDTO.setAttrs(Lists.newArrayList());
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        mustGroup.setGroups(Collections.singletonList(serviceProjectDTO));
        DealGroupServiceProjectDTO serviceProject = new DealGroupServiceProjectDTO();
        serviceProject.setMustGroups(Collections.singletonList(mustGroup));
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setServiceProject(serviceProject);
        when(mockCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        // act
        DealDetailSpecificModuleVO result = handler.buildResult(mockCtx);
        // assert
        assertNotNull(result);
        assertTrue(result.getUnits().isEmpty());
    }

    @Test
    public void testGetServiceProjectAttrValueWhenListIsEmpty() throws Throwable {
        String attrName = "testAttr";
        String result = invokePrivateMethod(Collections.emptyList(), attrName);
        assertEquals("", result);
    }

    @Test
    public void testGetServiceProjectAttrValueWhenNoMatchedAttrName() throws Throwable {
        String attrName = "testAttr";
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("otherAttr");
        String result = invokePrivateMethod(Arrays.asList(serviceProjectAttrDTO), attrName);
        assertEquals("", result);
    }

    @Test
    public void testGetServiceProjectAttrValueWhenAttrValueIsNull() throws Throwable {
        String attrName = "testAttr";
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName(attrName);
        serviceProjectAttrDTO.setAttrValue(null);
        String result = invokePrivateMethod(Arrays.asList(serviceProjectAttrDTO), attrName);
        assertEquals("", result);
    }

    @Test
    public void testGetServiceProjectAttrValueWhenAttrValueIsNotNull() throws Throwable {
        String attrName = "testAttr";
        String attrValue = "testValue";
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName(attrName);
        serviceProjectAttrDTO.setAttrValue(attrValue);
        String result = invokePrivateMethod(Arrays.asList(serviceProjectAttrDTO), attrName);
        assertEquals(attrValue, result);
    }
}
