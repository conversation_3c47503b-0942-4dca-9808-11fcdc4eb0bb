package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PromoDetailModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.SpecialParamsForPayVO;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupChannelDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.text.ParseException;
import java.text.SimpleDateFormat;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

public class SpecialParamsForPayBuilderServiceTest {

    @Mock
    private DealCtx mockDealCtx;
    @Mock
    private DealGroupPBO mockDealGroupPBO;

    private SpecialParamsForPayBuilderService serviceUnderTest;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        serviceUnderTest = new SpecialParamsForPayBuilderService();
    }

    /**
     * 测试正常情况下的参数构建
     */
    @Test
    public void testBuildNormalCase() throws ParseException {
        // arrange
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        promoDetailModule.setPromoPrice("100");
        when(mockDealGroupPBO.getPromoDetailModule()).thenReturn(promoDetailModule);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupBasicDTO basicDTO = new DealGroupBasicDTO();
        basicDTO.setBeginSaleDate("2023-04-01 00:00:00");
        basicDTO.setEndSaleDate("2023-04-30 23:59:59");
        dealGroupDTO.setBasic(basicDTO);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setChannelId(1);
        dealGroupDTO.setChannel(channelDTO);
        when(mockDealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);

        // act
        SpecialParamsForPayVO result = serviceUnderTest.build(mockDealCtx, mockDealGroupPBO);

        // assert
        assertNotNull(result);
        assertEquals(10000, result.getPayFeeCent());
        assertEquals("1", result.getFirstCategoryId());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        assertEquals(String.valueOf(simpleDateFormat.parse("2023-04-01 00:00:00").getTime()), result.getStartTimestamp());
        assertEquals(String.valueOf(simpleDateFormat.parse("2023-04-30 23:59:59").getTime()), result.getEndTimestamp());
    }

    /**
     * 测试当解析日期时抛出异常的情况
     */
    @Test
    public void testBuildWithParseException() {

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupBasicDTO basicDTO = new DealGroupBasicDTO();
        basicDTO.setBeginSaleDate("invalid date");
        basicDTO.setEndSaleDate("2023-04-30 23:59:59");
        dealGroupDTO.setBasic(basicDTO);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setChannelId(1);
        dealGroupDTO.setChannel(channelDTO);
        when(mockDealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);

        // act
        SpecialParamsForPayVO result = serviceUnderTest.build(mockDealCtx, mockDealGroupPBO);

        // assert
        assertNull(result);
    }

    /**
     * 测试当传入的DealCtx或DealGroupPBO为null时的情况
     */
    @Test
    public void testBuildWithNullInputs() {
        // act
        SpecialParamsForPayVO resultWithNullCtx = serviceUnderTest.build(null, mockDealGroupPBO);
        SpecialParamsForPayVO resultWithNullPBO = serviceUnderTest.build(mockDealCtx, null);

        // assert
        assertNull(resultWithNullCtx);
        assertNull(resultWithNullPBO);
    }

    /**
     * 测试当传入的DealCtx或DealGroupPBO为null时的情况
     */
    @Test
    public void testBuildWithInvalidNumber() {
        // arrange
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        promoDetailModule.setPromoPrice("10??0");
        when(mockDealGroupPBO.getPromoDetailModule()).thenReturn(promoDetailModule);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupBasicDTO basicDTO = new DealGroupBasicDTO();
        basicDTO.setBeginSaleDate("2023-04-01 00:00:00");
        basicDTO.setEndSaleDate("2023-04-30 23:59:59");
        dealGroupDTO.setBasic(basicDTO);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setChannelId(1);
        dealGroupDTO.setChannel(channelDTO);
        when(mockDealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);

        SpecialParamsForPayVO result = serviceUnderTest.build(mockDealCtx, mockDealGroupPBO);

        // assert
        assert (result.getPayFeeCent() == 0);
    }

    /**
     * 测试当传入的DealCtx或DealGroupPBO为null时的情况
     */
    @Test
    public void testBuildWithInvalidNumber2() {
        // arrange
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        promoDetailModule.setPromoPrice("100.01");
        when(mockDealGroupPBO.getPromoDetailModule()).thenReturn(promoDetailModule);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupBasicDTO basicDTO = new DealGroupBasicDTO();
        basicDTO.setBeginSaleDate("2023-04-01 00:00:00");
        basicDTO.setEndSaleDate("2023-04-30 23:59:59");
        dealGroupDTO.setBasic(basicDTO);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setChannelId(1);
        dealGroupDTO.setChannel(channelDTO);
        when(mockDealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);

        SpecialParamsForPayVO result = serviceUnderTest.build(mockDealCtx, mockDealGroupPBO);

        // assert
        assert (result.getPayFeeCent() == 10001);
    }

}
