package com.dianping.mobile.mapi.dztgdetail.biz.processor.productdetail;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule;
import com.dianping.mobile.mapi.dztgdetail.util.CommonModuleUtil;
import com.dianping.mobile.mapi.dztgdetail.util.DealVersionUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductStructDetailConfigProcessorTest {

    @InjectMocks
    private ProductStructDetailConfigProcessor processor;

    private MockedStatic<CommonModuleUtil> commonModuleUtilMockedStatic;
    private MockedStatic<DealVersionUtils> dealVersionUtilsMockedStatic;

    @Before
    public void setUp() {
        commonModuleUtilMockedStatic = mockStatic(CommonModuleUtil.class);
        dealVersionUtilsMockedStatic = mockStatic(DealVersionUtils.class);
    }

    @After
    public void tearDown() {
        commonModuleUtilMockedStatic.close();
        dealVersionUtilsMockedStatic.close();
    }

    @Test
    public void testProcess_DPPlatform_Success() {
        // Setup
        commonModuleUtilMockedStatic.when(() -> CommonModuleUtil.getModuleKeys(Mockito.any(), Mockito.any()))
                .thenReturn(Lists.newArrayList("module_detail_structured_detail"));
        dealVersionUtilsMockedStatic.when(() -> DealVersionUtils.isOldMetaVersion(Mockito.any(), Mockito.anyString()))
                .thenReturn(false);
        EnvCtx envCtx = new EnvCtx();
        DealCtx dealCtx = new DealCtx(envCtx);
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP); // Set as DP platform
        ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
        dealCtx.setModuleConfigsModule(moduleConfigsModule);

        // Execute
        processor.process(dealCtx);

        // Verify
        assertNotNull(dealCtx.getModuleConfigsModule().getModuleConfigs());
        assertEquals(1, dealCtx.getModuleConfigsModule().getModuleConfigs().size());
        assertEquals("tuandeal_gc_packagedetail", dealCtx.getModuleConfigsModule().getModuleConfigs().get(0).getKey());
        assertEquals("unified_structured_module", dealCtx.getModuleConfigsModule().getModuleConfigs().get(0).getValue());
    }

    @Test
    public void testProcess_MTPlatform_Success() {
        // Setup
        commonModuleUtilMockedStatic.when(() -> CommonModuleUtil.getModuleKeys(Mockito.any(), Mockito.any()))
                .thenReturn(Lists.newArrayList("module_detail_structured_detail"));
        dealVersionUtilsMockedStatic.when(() -> DealVersionUtils.isOldMetaVersion(Mockito.any(), Mockito.anyString()))
                .thenReturn(false);

        EnvCtx envCtx = new EnvCtx();
        DealCtx dealCtx = new DealCtx(envCtx);
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP); // Set as DP platform
        ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
        dealCtx.setModuleConfigsModule(moduleConfigsModule);

        // Execute
        processor.process(dealCtx);

        // Verify
        assertNotNull(dealCtx.getModuleConfigsModule().getModuleConfigs());
        assertEquals(1, dealCtx.getModuleConfigsModule().getModuleConfigs().size());
        assertEquals("dealdetail_gc_packagedetail", dealCtx.getModuleConfigsModule().getModuleConfigs().get(0).getKey());
        assertEquals("unified_structured_module", dealCtx.getModuleConfigsModule().getModuleConfigs().get(0).getValue());
    }

    @Test
    public void testProcess_OldMetaVersion_NoChange() {
        // Setup
        commonModuleUtilMockedStatic.when(() -> CommonModuleUtil.getModuleKeys(Mockito.any(), Mockito.any()))
                .thenReturn(Lists.newArrayList("module_detail_structured_detail"));
        dealVersionUtilsMockedStatic.when(() -> DealVersionUtils.isOldMetaVersion(Mockito.any(), Mockito.anyString()))
                .thenReturn(true);
        
        DealCtx dealCtx = new DealCtx(new EnvCtx());
        ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
        dealCtx.setModuleConfigsModule(moduleConfigsModule);

        // Execute
        processor.process(dealCtx);

        // Verify
        assertNull(dealCtx.getModuleConfigsModule().getModuleConfigs());
    }

    @Test
    public void testProcess_NoStructuredDetailModule_NoChange() {
        // Setup
        commonModuleUtilMockedStatic.when(() -> CommonModuleUtil.getModuleKeys(Mockito.any(), Mockito.any()))
                .thenReturn(Lists.newArrayList("other_module"));
        dealVersionUtilsMockedStatic.when(() -> DealVersionUtils.isOldMetaVersion(Mockito.any(), Mockito.anyString()))
                .thenReturn(false);
        
        DealCtx dealCtx = new DealCtx(new EnvCtx());
        ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
        dealCtx.setModuleConfigsModule(moduleConfigsModule);

        // Execute
        processor.process(dealCtx);

        // Verify
        assertNull(dealCtx.getModuleConfigsModule().getModuleConfigs());
    }

    @Test
    public void testProcess_ExistingConfig_UpdateValue() {
        // Setup
        commonModuleUtilMockedStatic.when(() -> CommonModuleUtil.getModuleKeys(Mockito.any(), Mockito.any()))
                .thenReturn(Lists.newArrayList("module_detail_structured_detail"));
        dealVersionUtilsMockedStatic.when(() -> DealVersionUtils.isOldMetaVersion(Mockito.any(), Mockito.anyString()))
                .thenReturn(false);
        
        DealCtx dealCtx = new DealCtx(new EnvCtx());
        ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
        ModuleConfigDo existingConfig = new ModuleConfigDo();
        existingConfig.setKey("tuandeal_gc_packagedetail");
        existingConfig.setValue("old_value");
        moduleConfigsModule.setModuleConfigs(Lists.newArrayList(existingConfig));
        dealCtx.setModuleConfigsModule(moduleConfigsModule);

        // Execute
        processor.process(dealCtx);

        // Verify
        assertNotNull(dealCtx.getModuleConfigsModule().getModuleConfigs());
        assertEquals(1, dealCtx.getModuleConfigsModule().getModuleConfigs().size());
        assertEquals("tuandeal_gc_packagedetail", dealCtx.getModuleConfigsModule().getModuleConfigs().get(0).getKey());
        assertEquals("unified_structured_module", dealCtx.getModuleConfigsModule().getModuleConfigs().get(0).getValue());
    }

    @Test
    public void testProcess_EmptyModuleKeys_NoChange() {
        // Setup
        commonModuleUtilMockedStatic.when(() -> CommonModuleUtil.getModuleKeys(Mockito.any(), Mockito.any()))
                .thenReturn(Lists.newArrayList());
        dealVersionUtilsMockedStatic.when(() -> DealVersionUtils.isOldMetaVersion(Mockito.any(), Mockito.anyString()))
                .thenReturn(false);
        
        DealCtx dealCtx = new DealCtx(new EnvCtx());
        ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
        dealCtx.setModuleConfigsModule(moduleConfigsModule);

        // Execute
        processor.process(dealCtx);

        // Verify
        assertNull(dealCtx.getModuleConfigsModule().getModuleConfigs());
    }
}
