package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.common.ResponseDTO;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.LiveRoomDistributionInfo;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.request.LiveRoomInfoRequest;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.service.liveroomadmin.LiveRoomRpcService;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.Future;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveWrapperTest {

    @InjectMocks
    private PrivateLiveWrapper privateLiveWrapper;

    @Mock
    private LiveRoomRpcService liveRoomRpcServiceFuture;

    private MockedStatic<FutureFactory> factoryMocked;

    @Before
    public void setUp() {
        factoryMocked = mockStatic(FutureFactory.class);
    }

    @After
    public void tearDown() {
        factoryMocked.close();
    }


    /**
     * 测试preGetLiveRoomDetail方法，传入的liveId不为空，且获取直播间详细信息没有出现异常的情况
     */
    @Test
    public void testPreGetLiveRoomDetailNormalCase() throws Throwable {
        // arrange
        String liveId = "123";
        when(liveRoomRpcServiceFuture.getLiveRoomDetail(liveId)).thenReturn(null);
        Future mockFuture = mock(Future.class);
        factoryMocked.when(FutureFactory::getFuture).thenReturn(mockFuture);
        // act
        Future result = privateLiveWrapper.preGetLiveRoomDetail(liveId);

        // assert
        assertNotNull(result);
    }

    /**
     * 测试preGetLiveRoomDetail方法，传入的liveId为空的情况
     */
    @Test
    public void testPreGetLiveRoomDetailEmptyLiveIdCase() throws Throwable {
        // arrange
        String liveId = "";

        // act
        Future result = privateLiveWrapper.preGetLiveRoomDetail(liveId);

        // assert
        assertNull(result);
    }

    @Test
    public void testPreQueryLiveDistributionInfo_WithValidLiveId() {
        // Arrange
        FutureCtx futureCtx = new FutureCtx();
        String liveId = "validLiveId";

        // Act
        Future future = privateLiveWrapper.preQueryLiveDistributionInfo(liveId);
        // Assert
        assertNull(future);
    }
    @Test
    public void testPreQueryLiveDistributionInfo_WithBlankLiveId() {
        // Arrange
        String liveId = "";
        // Act
        Future future = privateLiveWrapper.preQueryLiveDistributionInfo(liveId);
        // Assert
        assertNull(future);
        verify(liveRoomRpcServiceFuture, never()).queryLiveDistributionInfo(any(LiveRoomInfoRequest.class));
    }
    @Test
    public void testPreQueryLiveDistributionInfo_WithException() {
        // Arrange
        String liveId = "validLiveId";
        doThrow(new RuntimeException("Test Exception")).when(liveRoomRpcServiceFuture).queryLiveDistributionInfo(any(LiveRoomInfoRequest.class));
        // Act
        Future future = privateLiveWrapper.preQueryLiveDistributionInfo(liveId);
        // Assert
        assertNull(future);
        verify(liveRoomRpcServiceFuture, times(1)).queryLiveDistributionInfo(any(LiveRoomInfoRequest.class));
    }
    @Test
    public void testBuildLiveRoomInfoRequest() {
        // Arrange
        String liveId = "validLiveId";
        // Act
        LiveRoomInfoRequest request = privateLiveWrapper.buildLiveRoomInfoRequest(liveId);
        // Assert
        assertNotNull(request);
        assertEquals(liveId, request.getLiveId());
    }
}
