package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.gynecology;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class InducedAbortionHandlerTest {
    @Mock
    private DealGroupDTO dto;

    @Mock
    private DealCtx ctx;

    @InjectMocks
    InducedAbortionHandler inducedAbortionHandler;


    /*
     * */
    @Test
    public void testNormal() {
        when(ctx.getDealGroupDTO()).thenReturn(dto);
        inducedAbortionHandler.execute(ctx);

        assertNull(ctx.getHighlightsModule());
    }
}
