package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.sankuai.wpt.user.merge.query.thrift.message.BindRelation;
import com.sankuai.wpt.user.merge.query.thrift.message.BindRelationResp;
import com.sankuai.wpt.user.merge.query.thrift.message.UserIdModel;
import com.sankuai.wpt.user.merge.query.thrift.message.UserMergeQueryService;
import org.apache.thrift.TException;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class UserAccountWrapperTest {

    @InjectMocks
    private UserAccountWrapper userAccountWrapper;

    @Mock
    private UserMergeQueryService.Iface rpcMergeQueryService;

    private MockedStatic<Lion> lionMockedStatic;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        lionMockedStatic = mockStatic(Lion.class);
    }

    @After
    public void teardown() {
        lionMockedStatic.close();
    }

    // Removed @Before annotation and setUp method
    private BindRelationResp createBindRelationResp() {
        BindRelationResp bindRelationResp = new BindRelationResp();
        bindRelationResp.setSuccess(true);
        BindRelation bindRelation = new BindRelation();
        // Corrected class name
        UserIdModel userIdModel = new UserIdModel();
        userIdModel.setId(1L);
        bindRelation.setMtUserId(userIdModel);
        bindRelationResp.setData(bindRelation);
        return bindRelationResp;
    }

    @Test
    public void testGetVirtualBindMtUserIdByDpUserIdNull() throws Throwable {
        assertNull(userAccountWrapper.getVirtualBindMtUserIdByDpUserId(null));
    }

    @Test
    public void testGetVirtualBindMtUserIdByDpUserIdZero() throws Throwable {
        assertNull(userAccountWrapper.getVirtualBindMtUserIdByDpUserId(0L));
    }

    @Test
    public void testGetVirtualBindMtUserIdByDpUserIdException() throws Throwable {
        doThrow(TException.class).when(rpcMergeQueryService).getVirtualBindByDpUserId(anyLong());
        assertNull(userAccountWrapper.getVirtualBindMtUserIdByDpUserId(1L));
    }

    @Test
    public void testGetVirtualBindMtUserIdByDpUserIdRespNull() throws Throwable {
        when(rpcMergeQueryService.getVirtualBindByDpUserId(anyLong())).thenReturn(null);
        assertNull(userAccountWrapper.getVirtualBindMtUserIdByDpUserId(1L));
    }

    @Test
    public void testGetVirtualBindMtUserIdByDpUserIdSuccessFalse() throws Throwable {
        BindRelationResp bindRelationResp = createBindRelationResp();
        bindRelationResp.setSuccess(false);
        when(rpcMergeQueryService.getVirtualBindByDpUserId(anyLong())).thenReturn(bindRelationResp);
        assertNull(userAccountWrapper.getVirtualBindMtUserIdByDpUserId(1L));
    }

    @Test
    public void testGetVirtualBindMtUserIdByDpUserIdMtUserIdNull() throws Throwable {
        BindRelationResp bindRelationResp = createBindRelationResp();
        bindRelationResp.getData().setMtUserId(null);
        when(rpcMergeQueryService.getVirtualBindByDpUserId(anyLong())).thenReturn(bindRelationResp);
        assertNull(userAccountWrapper.getVirtualBindMtUserIdByDpUserId(1L));
    }

    @Test
    public void testGetVirtualBindMtUserIdByDpUserIdMtUserIdIdZero() throws Throwable {
        BindRelationResp bindRelationResp = createBindRelationResp();
        bindRelationResp.getData().getMtUserId().setId(0L);
        when(rpcMergeQueryService.getVirtualBindByDpUserId(anyLong())).thenReturn(bindRelationResp);
        assertNull(userAccountWrapper.getVirtualBindMtUserIdByDpUserId(1L));
    }

    @Test
    public void testGetVirtualBindMtUserIdByDpUserIdNormal() throws Throwable {
        BindRelationResp bindRelationResp = createBindRelationResp();
        when(rpcMergeQueryService.getVirtualBindByDpUserId(anyLong())).thenReturn(bindRelationResp);
        assertEquals(1L, userAccountWrapper.getVirtualBindMtUserIdByDpUserId(1L).longValue());
    }

    /**
     * 测试 getRealBindByMtUserId 方法，当 rpcMergeQueryService.getRealBindByMtUserId 返回的 BindRelationResp 对象非空，调用成功，且数据非空时，应返回数据
     */
    @Test
    public void testGetRealBindByMtUserId_Success() throws TException {
        // arrange
        long mtUserId = 123L;
        BindRelation bindRelation = new BindRelation();
        BindRelationResp resp = new BindRelationResp();
        resp.setSuccess(true);
        resp.setData(bindRelation);
        when(rpcMergeQueryService.getRealBindByMtUserId(mtUserId)).thenReturn(resp);
        // act
        BindRelation result = userAccountWrapper.getRealBindByMtUserId(mtUserId);
        // assert
        assertEquals(bindRelation, result);
    }

    /**
     * 测试 getRealBindByMtUserId 方法，当 rpcMergeQueryService.getRealBindByMtUserId 返回的 BindRelationResp 对象为空时，应返回 null
     */
    @Test
    public void testGetRealBindByMtUserId_NullResp() throws TException {
        // arrange
        long mtUserId = 123L;
        when(rpcMergeQueryService.getRealBindByMtUserId(mtUserId)).thenReturn(null);
        // act
        BindRelation result = userAccountWrapper.getRealBindByMtUserId(mtUserId);
        // assert
        assertNull(result);
    }

    /**
     * 测试 getRealBindByMtUserId 方法，当 rpcMergeQueryService.getRealBindByMtUserId 返回的 BindRelationResp 对象非空，但调用失败时，应返回 null
     */
    @Test
    public void testGetRealBindByMtUserId_Failed() throws TException {
        // arrange
        long mtUserId = 123L;
        BindRelationResp resp = new BindRelationResp();
        resp.setSuccess(false);
        when(rpcMergeQueryService.getRealBindByMtUserId(mtUserId)).thenReturn(resp);
        // act
        BindRelation result = userAccountWrapper.getRealBindByMtUserId(mtUserId);
        // assert
        assertNull(result);
    }

    /**
     * 测试 getRealBindByMtUserId 方法，当 rpcMergeQueryService.getRealBindByMtUserId 返回的 BindRelationResp 对象非空，调用成功，但数据为空时，应返回 null
     */
    @Test
    public void testGetRealBindByMtUserId_NullData() throws TException {
        // arrange
        long mtUserId = 123L;
        BindRelationResp resp = new BindRelationResp();
        resp.setSuccess(true);
        resp.setData(null);
        when(rpcMergeQueryService.getRealBindByMtUserId(mtUserId)).thenReturn(resp);
        // act
        BindRelation result = userAccountWrapper.getRealBindByMtUserId(mtUserId);
        // assert
        assertNull(result);
    }

    /**
     * 测试环境为美团，直接返回美团用户ID
     */
    @Test
    public void testGetMtUserIdForShopMember_EnvIsMt() throws Throwable {
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        ctx.getEnvCtx().setMtUserId(123L);
        long result = userAccountWrapper.getMtUserIdForShopMember(ctx);
        assertEquals(123L, result);
    }

    /**
     * 测试环境为点评，使用虚拟用户ID，且虚拟用户ID大于0
     */
    @Test
    public void testGetMtUserIdForShopMember_EnvIsDp_UseVirtualUserId() throws Throwable {
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        ctx.getEnvCtx().setMtVirtualUserId(456L);
        lionMockedStatic.when(() -> Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.use.ctx.virtual.user.id", false))
                .thenReturn(true);
        long result = userAccountWrapper.getMtUserIdForShopMember(ctx);
        assertEquals(456L, result);
    }

    /**
     * 测试环境为点评，不使用虚拟用户ID，通过rpcMergeQueryService获取绑定关系
     */
    @Test
    public void testGetMtUserIdForShopMember_EnvIsDp_NotUseVirtualUserId() throws Throwable {
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        ctx.getEnvCtx().setDpUserId(789L);
        lionMockedStatic.when(() -> Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.use.ctx.virtual.user.id", false))
                .thenReturn(false);
        BindRelationResp relationResp = new BindRelationResp();
        BindRelation mtUserId = new BindRelation();
        UserIdModel userIdModel = new UserIdModel();
        userIdModel.setId(789L);
        mtUserId.setMtUserId(userIdModel);
        relationResp.setSuccess(true);
        relationResp.setData(mtUserId);
        when(rpcMergeQueryService.getVirtualBindByDpUserId(789L)).thenReturn(relationResp);
        long result = userAccountWrapper.getMtUserIdForShopMember(ctx);
        assertEquals(789L, result);
    }

    /**
     * 测试环境为点评，不使用虚拟用户ID，rpcMergeQueryService抛出异常
     */
    @Test(expected = Exception.class)
    public void testGetMtUserIdForShopMember_EnvIsDp_NotUseVirtualUserId_Exception() throws Throwable {
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        ctx.getEnvCtx().setDpUserId(789L);
        lionMockedStatic.when(() -> Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.use.ctx.virtual.user.id", false))
                .thenReturn(false);
        when(rpcMergeQueryService.getVirtualBindByDpUserId(789L)).thenThrow(new Exception());
        userAccountWrapper.getMtUserIdForShopMember(ctx);
    }

    /**
     * 测试环境为点评，不使用虚拟用户ID，rpcMergeQueryService返回null
     */
    @Test
    public void testGetMtUserIdForShopMember_EnvIsDp_NotUseVirtualUserId_NullResponse() throws Throwable {
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        ctx.getEnvCtx().setDpUserId(789L);
        lionMockedStatic.when(() -> Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.use.ctx.virtual.user.id", false))
                .thenReturn(false);
        when(rpcMergeQueryService.getVirtualBindByDpUserId(789L)).thenReturn(null);
        long result = userAccountWrapper.getMtUserIdForShopMember(ctx);
        assertEquals(0L, result);
    }

    /**
     * 测试环境为点评，不使用虚拟用户ID，rpcMergeQueryService返回失败响应
     */
    @Test
    public void testGetMtUserIdForShopMember_EnvIsDp_NotUseVirtualUserId_FailureResponse() throws Throwable {
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        ctx.getEnvCtx().setDpUserId(789L);
        lionMockedStatic.when(() -> Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.use.ctx.virtual.user.id", false))
                .thenReturn(false);
        BindRelationResp relationResp = new BindRelationResp();
        relationResp.setSuccess(false);
        when(rpcMergeQueryService.getVirtualBindByDpUserId(789L)).thenReturn(relationResp);
        long result = userAccountWrapper.getMtUserIdForShopMember(ctx);
        assertEquals(0L, result);
    }
}
