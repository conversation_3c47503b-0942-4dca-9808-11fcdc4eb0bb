package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperCacheWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.dianping.mobile.mapi.dztgdetail.util.RedisClientUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Sets;
import com.sankuai.athena.inf.cache2.CacheClient;
import com.sankuai.athena.inf.cache2.CacheKey;
import com.sankuai.athena.inf.cache2.loader.DataLoader;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupIdBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class MapperCacheWrapperGetDpByMtShopIdFutureTest {

    @InjectMocks
    private MapperCacheWrapper mapperCacheWrapper;

    @Mock
    private MapperWrapper mapperWrapper;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private CacheClient cacheClient;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private CompletableFuture<Object> createFailedFuture(Throwable ex) {
        CompletableFuture<Object> future = new CompletableFuture<>();
        future.completeExceptionally(ex);
        return future;
    }

    /**
     * 测试 dpShopId 为 null 的情况
     */
    @Test(expected = IllegalStateException.class)
    public void testGetDpByMtShopIdFuture_withNullDpShopId() throws Throwable {
        // arrange
        when(mapperWrapper.getDpByMtShopId(1L)).thenReturn(null);
        // act
        mapperCacheWrapper.getDpByMtShopIdFuture(1L, new CacheKey("category"));
    }

    /**
     * 测试 dpShopId 小于等于 0 的情况
     */
    @Test(expected = IllegalStateException.class)
    public void testGetDpByMtShopIdFuture_withInvalidDpShopId() throws Throwable {
        // arrange
        when(mapperWrapper.getDpByMtShopId(1L)).thenReturn(0L);
        // act
        mapperCacheWrapper.getDpByMtShopIdFuture(1L, new CacheKey("category"));
    }

    /**
     * 测试 key 为 null 的情况
     */
    @Test(expected = IllegalStateException.class)
    public void testGetDpByMtShopIdFuture_withNullKey() throws Throwable {
        // arrange
        when(mapperWrapper.getDpByMtShopId(1L)).thenReturn(1L);
        // act
        mapperCacheWrapper.getDpByMtShopIdFuture(1L, null);
    }

    /**
     * 测试 dpShopId 和 key 都有效的情况
     */
    @Test
    public void testGetDpByMtShopIdFuture_withValidDpShopIdAndKey() throws Throwable {
        // arrange
        when(mapperWrapper.getDpByMtShopId(1L)).thenReturn(1L);
        // act
        CompletableFuture<Long> result = mapperCacheWrapper.getDpByMtShopIdFuture(1L, new CacheKey("category"));
        // assert
        assertEquals(Long.valueOf(1L), result.get());
    }

    /**
     * 测试正常场景 - 返回有效的dpCityId
     */
    @Test
    public void testGetDpCityIdByMtCityIdFuture_normal() throws Throwable {
        // arrange
        int mtCityId = 1;
        int expectedDpCityId = 2;
        CacheKey cacheKey = new CacheKey("category", "params");
        Mockito.when(mapperWrapper.fetchDpCityByMtCity(mtCityId)).thenReturn(expectedDpCityId);
        // act
        CompletableFuture<Integer> result = mapperCacheWrapper.getDpCityIdByMtCityIdFuture(mtCityId, cacheKey);
        // assert
        Assert.assertEquals(expectedDpCityId, result.get().intValue());
        Mockito.verify(mapperWrapper).fetchDpCityByMtCity(mtCityId);
    }

    /**
     * 测试异常场景 - dpCityId为null
     */
    @Test(expected = IllegalStateException.class)
    public void testGetDpCityIdByMtCityIdFuture_nullDpCityId() throws Throwable {
        // arrange
        int mtCityId = 1;
        CacheKey cacheKey = new CacheKey("category", "params");
        Mockito.when(mapperWrapper.fetchDpCityByMtCity(mtCityId)).thenReturn(null);
        // act & assert
        mapperCacheWrapper.getDpCityIdByMtCityIdFuture(mtCityId, cacheKey);
    }

    /**
     * 测试异常场景 - dpCityId小于等于0
     */
    @Test(expected = IllegalStateException.class)
    public void testGetDpCityIdByMtCityIdFuture_zeroDpCityId() throws Throwable {
        // arrange
        int mtCityId = 1;
        CacheKey cacheKey = new CacheKey("category", "params");
        Mockito.when(mapperWrapper.fetchDpCityByMtCity(mtCityId)).thenReturn(0);
        // act & assert
        mapperCacheWrapper.getDpCityIdByMtCityIdFuture(mtCityId, cacheKey);
    }

    /**
     * 测试异常场景 - CacheKey为null
     */
    @Test(expected = IllegalStateException.class)
    public void testGetDpCityIdByMtCityIdFuture_nullCacheKey() throws Throwable {
        // arrange
        int mtCityId = 1;
        int dpCityId = 2;
        Mockito.when(mapperWrapper.fetchDpCityByMtCity(mtCityId)).thenReturn(dpCityId);
        // act & assert
        mapperCacheWrapper.getDpCityIdByMtCityIdFuture(mtCityId, null);
    }

    /**
     * 测试异常场景 - mapperWrapper.fetchDpCityByMtCity抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testGetDpCityIdByMtCityIdFuture_mapperException() throws Throwable {
        // arrange
        int mtCityId = 1;
        CacheKey cacheKey = new CacheKey("category", "params");
        Mockito.when(mapperWrapper.fetchDpCityByMtCity(mtCityId)).thenThrow(new RuntimeException("test exception"));
        // act & assert
        mapperCacheWrapper.getDpCityIdByMtCityIdFuture(mtCityId, cacheKey);
    }

    /**
     * 测试边界场景 - dpCityId等于1(最小有效值)
     */
    @Test
    public void testGetDpCityIdByMtCityIdFuture_minValidDpCityId() throws Throwable {
        // arrange
        int mtCityId = 1;
        // 最小有效值
        int expectedDpCityId = 1;
        CacheKey cacheKey = new CacheKey("category", "params");
        Mockito.when(mapperWrapper.fetchDpCityByMtCity(mtCityId)).thenReturn(expectedDpCityId);
        // act
        CompletableFuture<Integer> result = mapperCacheWrapper.getDpCityIdByMtCityIdFuture(mtCityId, cacheKey);
        // assert
        Assert.assertEquals(expectedDpCityId, result.get().intValue());
        Mockito.verify(mapperWrapper).fetchDpCityByMtCity(mtCityId);
    }

    /**
     * 测试边界场景 - dpCityId等于Integer.MAX_VALUE(最大有效值)
     */
    @Test
    public void testGetDpCityIdByMtCityIdFuture_maxValidDpCityId() throws Throwable {
        // arrange
        int mtCityId = 1;
        // 最大有效值
        int expectedDpCityId = Integer.MAX_VALUE;
        CacheKey cacheKey = new CacheKey("category", "params");
        Mockito.when(mapperWrapper.fetchDpCityByMtCity(mtCityId)).thenReturn(expectedDpCityId);
        // act
        CompletableFuture<Integer> result = mapperCacheWrapper.getDpCityIdByMtCityIdFuture(mtCityId, cacheKey);
        // assert
        Assert.assertEquals(expectedDpCityId, result.get().intValue());
        Mockito.verify(mapperWrapper).fetchDpCityByMtCity(mtCityId);
    }

    /**
     * Test when input mtDealGroupId is invalid (<=0)
     * Should return 0 immediately without any service calls
     */
    @Test
    public void testGetDpDealGroupIdInvalidInput() throws Throwable {
        // arrange
        int invalidMtDealGroupId = 0;
        // act
        int result = mapperCacheWrapper.getDpDealGroupId(invalidMtDealGroupId);
        // assert
        assertEquals(0, result);
        verifyNoInteractions(queryCenterWrapper);
        verifyNoInteractions(dealGroupWrapper);
    }

    /**
     * Test when queryCenter returns valid DealGroupDTO with dpDealGroupIdInt
     * Should return the dpDealGroupIdInt directly
     */
    @Test
    public void testGetDpDealGroupIdSuccessWithValidResult() throws Throwable {
        // arrange
        int mtDealGroupId = 123;
        int expectedDpId = 456;
        DealGroupDTO mockResult = new DealGroupDTO();
        mockResult.setDpDealGroupId((long) expectedDpId);
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(mockResult);
        // act
        int result = mapperCacheWrapper.getDpDealGroupId(mtDealGroupId);
        // assert
        assertEquals(expectedDpId, result);
        verify(queryCenterWrapper).getDealGroupDTO(any(QueryByDealGroupIdRequest.class));
        verifyNoInteractions(dealGroupWrapper);
    }

    /**
     * Test when queryCenter returns null result
     * Should fallback to dealGroupWrapper
     */
    @Test
    public void testGetDpDealGroupIdWhenQueryReturnsNull() throws Throwable {
        // arrange
        int mtDealGroupId = 123;
        int expectedDpId = 789;
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(null);
        Future mockFuture = mock(Future.class);
        when(dealGroupWrapper.preDpDealGroupId(mtDealGroupId)).thenReturn(mockFuture);
        when(dealGroupWrapper.getDpDealGroupId(mockFuture)).thenReturn(expectedDpId);
        // act
        int result = mapperCacheWrapper.getDpDealGroupId(mtDealGroupId);
        // assert
        assertEquals(expectedDpId, result);
        verify(queryCenterWrapper).getDealGroupDTO(any(QueryByDealGroupIdRequest.class));
        verify(dealGroupWrapper).preDpDealGroupId(mtDealGroupId);
        verify(dealGroupWrapper).getDpDealGroupId(mockFuture);
    }

    /**
     * Test when queryCenter throws TException
     * Should fallback to dealGroupWrapper
     */
    @Test
    public void testGetDpDealGroupIdWhenQueryThrowsException() throws Throwable {
        // arrange
        int mtDealGroupId = 123;
        int expectedDpId = 789;
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenThrow(new TException("Test exception"));
        Future mockFuture = mock(Future.class);
        when(dealGroupWrapper.preDpDealGroupId(mtDealGroupId)).thenReturn(mockFuture);
        when(dealGroupWrapper.getDpDealGroupId(mockFuture)).thenReturn(expectedDpId);
        // act
        int result = mapperCacheWrapper.getDpDealGroupId(mtDealGroupId);
        // assert
        assertEquals(expectedDpId, result);
        verify(queryCenterWrapper).getDealGroupDTO(any(QueryByDealGroupIdRequest.class));
        verify(dealGroupWrapper).preDpDealGroupId(mtDealGroupId);
        verify(dealGroupWrapper).getDpDealGroupId(mockFuture);
    }

    /**
     * Test when queryCenter returns DealGroupDTO but dpDealGroupIdInt is null
     * Should fallback to dealGroupWrapper
     */
    @Test
    public void testGetDpDealGroupIdWhenDpIdIsNull() throws Throwable {
        // arrange
        int mtDealGroupId = 123;
        int expectedDpId = 789;
        DealGroupDTO mockResult = new DealGroupDTO();
        // dpDealGroupIdInt will be null
        mockResult.setDpDealGroupId(null);
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(mockResult);
        Future mockFuture = mock(Future.class);
        when(dealGroupWrapper.preDpDealGroupId(mtDealGroupId)).thenReturn(mockFuture);
        when(dealGroupWrapper.getDpDealGroupId(mockFuture)).thenReturn(expectedDpId);
        // act
        int result = mapperCacheWrapper.getDpDealGroupId(mtDealGroupId);
        // assert
        assertEquals(expectedDpId, result);
        verify(queryCenterWrapper).getDealGroupDTO(any(QueryByDealGroupIdRequest.class));
        verify(dealGroupWrapper).preDpDealGroupId(mtDealGroupId);
        verify(dealGroupWrapper).getDpDealGroupId(mockFuture);
    }

    /**
     * Test case for valid input scenario
     * - dpShopId is valid
     * - key is not null
     * - mapperWrapper returns valid mtShopId
     */
    @Test
    public void testGetShopIdByDpShopIdFuture_withValidInput() throws Throwable {
        // arrange
        long dpShopId = 12345L;
        long expectedMtShopId = 67890L;
        CacheKey key = new CacheKey("test");
        when(mapperWrapper.getMtShopIdByDpShopIdLong(dpShopId)).thenReturn(expectedMtShopId);
        // act
        CompletableFuture<Long> result = mapperCacheWrapper.getShopIdByDpShopIdFuture(dpShopId, key);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("MtShopId should match expected value", expectedMtShopId, result.get().longValue());
        verify(mapperWrapper).getMtShopIdByDpShopIdLong(dpShopId);
        verifyNoMoreInteractions(mapperWrapper);
    }

    /**
     * Test case for invalid mtShopId scenario
     * - mapperWrapper returns mtShopId <= 0
     */
    @Test(expected = IllegalStateException.class)
    public void testGetShopIdByDpShopIdFuture_withInvalidMtShopId() throws Throwable {
        // arrange
        long dpShopId = 12345L;
        CacheKey key = new CacheKey("test");
        when(mapperWrapper.getMtShopIdByDpShopIdLong(dpShopId)).thenReturn(0L);
        // act & assert
        mapperCacheWrapper.getShopIdByDpShopIdFuture(dpShopId, key);
    }

    /**
     * Test case for null key scenario
     */
    @Test(expected = IllegalStateException.class)
    public void testGetShopIdByDpShopIdFuture_withNullKey() throws Throwable {
        // arrange
        long dpShopId = 12345L;
        when(mapperWrapper.getMtShopIdByDpShopIdLong(dpShopId)).thenReturn(67890L);
        // act & assert
        mapperCacheWrapper.getShopIdByDpShopIdFuture(dpShopId, null);
    }

    /**
     * Test case for maximum dpShopId value
     */
    @Test
    public void testGetShopIdByDpShopIdFuture_withMaxValueDpShopId() throws Throwable {
        // arrange
        long dpShopId = Long.MAX_VALUE;
        long expectedMtShopId = 67890L;
        CacheKey key = new CacheKey("test");
        when(mapperWrapper.getMtShopIdByDpShopIdLong(dpShopId)).thenReturn(expectedMtShopId);
        // act
        CompletableFuture<Long> result = mapperCacheWrapper.getShopIdByDpShopIdFuture(dpShopId, key);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("MtShopId should match expected value", expectedMtShopId, result.get().longValue());
        verify(mapperWrapper).getMtShopIdByDpShopIdLong(dpShopId);
        verifyNoMoreInteractions(mapperWrapper);
    }

    /**
     * Test case for negative mtShopId scenario
     */
    @Test(expected = IllegalStateException.class)
    public void testGetShopIdByDpShopIdFuture_withNegativeMtShopId() throws Throwable {
        // arrange
        long dpShopId = 12345L;
        CacheKey key = new CacheKey("test");
        when(mapperWrapper.getMtShopIdByDpShopIdLong(dpShopId)).thenReturn(-1L);
        // act & assert
        mapperCacheWrapper.getShopIdByDpShopIdFuture(dpShopId, key);
    }

    /**
     * Test case for exception handling when mapperWrapper throws exception
     */
    @Test(expected = RuntimeException.class)
    public void testGetShopIdByDpShopIdFuture_whenMapperThrowsException() throws Throwable {
        // arrange
        long dpShopId = 12345L;
        CacheKey key = new CacheKey("test");
        when(mapperWrapper.getMtShopIdByDpShopIdLong(dpShopId)).thenThrow(new RuntimeException("Mapper error"));
        // act & assert
        mapperCacheWrapper.getShopIdByDpShopIdFuture(dpShopId, key);
    }

    /**
     * Test case for minimum valid dpShopId value
     */
    @Test
    public void testGetShopIdByDpShopIdFuture_withMinValueDpShopId() throws Throwable {
        // arrange
        long dpShopId = 1L;
        long expectedMtShopId = 67890L;
        CacheKey key = new CacheKey("test");
        when(mapperWrapper.getMtShopIdByDpShopIdLong(dpShopId)).thenReturn(expectedMtShopId);
        // act
        CompletableFuture<Long> result = mapperCacheWrapper.getShopIdByDpShopIdFuture(dpShopId, key);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("MtShopId should match expected value", expectedMtShopId, result.get().longValue());
        verify(mapperWrapper).getMtShopIdByDpShopIdLong(dpShopId);
        verifyNoMoreInteractions(mapperWrapper);
    }

    /**
     * Test case for zero dpShopId value
     */
    @Test
    public void testGetShopIdByDpShopIdFuture_withZeroDpShopId() throws Throwable {
        // arrange
        long dpShopId = 0L;
        long expectedMtShopId = 67890L;
        CacheKey key = new CacheKey("test");
        when(mapperWrapper.getMtShopIdByDpShopIdLong(dpShopId)).thenReturn(expectedMtShopId);
        // act
        CompletableFuture<Long> result = mapperCacheWrapper.getShopIdByDpShopIdFuture(dpShopId, key);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("MtShopId should match expected value", expectedMtShopId, result.get().longValue());
        verify(mapperWrapper).getMtShopIdByDpShopIdLong(dpShopId);
        verifyNoMoreInteractions(mapperWrapper);
    }

    /**
     * Test case for dpShopId less than or equal to zero
     */
    @Test
    public void testGetMtShopIdFromCacheDpShopIdLessThanOrEqualToZero() throws Throwable {
        CompletableFuture<Long> result = mapperCacheWrapper.getMtShopIdFromCache(0L);
        assertEquals(Long.valueOf(0), result.get());
    }

    /**
     * 测试输入dpShopId小于等于0的情况
     */
    @Test
    public void testFetchMtShopIdWithInvalidDpShopId() throws Throwable {
        // arrange
        long dpShopId = 0L;
        // act
        long result = mapperCacheWrapper.fetchMtShopId(dpShopId);
        // assert
        assertEquals(0L, result);
        verifyNoInteractions(cacheClient, mapperWrapper);
    }

    /**
     * Test case for dpDealGroupId <= 0 boundary condition
     */
    @Test
    public void testGetMtDealGroupId_WhenDpDealGroupIdIsZero() throws Throwable {
        // arrange
        int dpDealGroupId = 0;
        // act
        int result = mapperCacheWrapper.getMtDealGroupId(dpDealGroupId);
        // assert
        assertEquals(0, result);
        verifyNoInteractions(queryCenterWrapper);
        verifyNoInteractions(dealGroupWrapper);
    }

    /**
     * Test case for successful query from queryCenterWrapper
     */
    @Test
    public void testGetMtDealGroupId_WhenQueryCenterReturnsValidData() throws Throwable {
        // arrange
        int dpDealGroupId = 123;
        int expectedMtDealGroupId = 456;
        DealGroupDTO mockDto = new DealGroupDTO();
        mockDto.setMtDealGroupId((long) expectedMtDealGroupId);
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(mockDto);
        // act
        int result = mapperCacheWrapper.getMtDealGroupId(dpDealGroupId);
        // assert
        assertEquals(expectedMtDealGroupId, result);
        verify(queryCenterWrapper).getDealGroupDTO(any(QueryByDealGroupIdRequest.class));
        verifyNoInteractions(dealGroupWrapper);
    }

    /**
     * Test case when queryCenterWrapper throws exception
     */
    @Test
    public void testGetMtDealGroupId_WhenQueryCenterThrowsException() throws Throwable {
        // arrange
        int dpDealGroupId = 123;
        int expectedMtDealGroupId = 789;
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenThrow(new TException("Test exception"));
        Future mockFuture = mock(Future.class);
        when(dealGroupWrapper.preMtDealGroupIdByDp(dpDealGroupId)).thenReturn(mockFuture);
        when(dealGroupWrapper.getMtDealGroupId(mockFuture)).thenReturn(expectedMtDealGroupId);
        // act
        int result = mapperCacheWrapper.getMtDealGroupId(dpDealGroupId);
        // assert
        assertEquals(expectedMtDealGroupId, result);
        verify(queryCenterWrapper).getDealGroupDTO(any(QueryByDealGroupIdRequest.class));
        verify(dealGroupWrapper).preMtDealGroupIdByDp(dpDealGroupId);
        verify(dealGroupWrapper).getMtDealGroupId(mockFuture);
    }

    /**
     * Test case when queryCenterWrapper returns null DealGroupDTO
     */
    @Test
    public void testGetMtDealGroupId_WhenQueryCenterReturnsNull() throws Throwable {
        // arrange
        int dpDealGroupId = 123;
        int expectedMtDealGroupId = 789;
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(null);
        Future mockFuture = mock(Future.class);
        when(dealGroupWrapper.preMtDealGroupIdByDp(dpDealGroupId)).thenReturn(mockFuture);
        when(dealGroupWrapper.getMtDealGroupId(mockFuture)).thenReturn(expectedMtDealGroupId);
        // act
        int result = mapperCacheWrapper.getMtDealGroupId(dpDealGroupId);
        // assert
        assertEquals(expectedMtDealGroupId, result);
        verify(queryCenterWrapper).getDealGroupDTO(any(QueryByDealGroupIdRequest.class));
        verify(dealGroupWrapper).preMtDealGroupIdByDp(dpDealGroupId);
        verify(dealGroupWrapper).getMtDealGroupId(mockFuture);
    }

    /**
     * Test case when DealGroupDTO exists but mtDealGroupId is null
     */
    @Test
    public void testGetMtDealGroupId_WhenMtDealGroupIdIsNull() throws Throwable {
        // arrange
        int dpDealGroupId = 123;
        int expectedMtDealGroupId = 789;
        DealGroupDTO mockDto = new DealGroupDTO();
        mockDto.setMtDealGroupId(null);
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(mockDto);
        Future mockFuture = mock(Future.class);
        when(dealGroupWrapper.preMtDealGroupIdByDp(dpDealGroupId)).thenReturn(mockFuture);
        when(dealGroupWrapper.getMtDealGroupId(mockFuture)).thenReturn(expectedMtDealGroupId);
        // act
        int result = mapperCacheWrapper.getMtDealGroupId(dpDealGroupId);
        // assert
        assertEquals(expectedMtDealGroupId, result);
        verify(queryCenterWrapper).getDealGroupDTO(any(QueryByDealGroupIdRequest.class));
        verify(dealGroupWrapper).preMtDealGroupIdByDp(dpDealGroupId);
        verify(dealGroupWrapper).getMtDealGroupId(mockFuture);
    }

    /**
     * Test case for invalid input with zero value
     */
    @Test
    public void testGetDpDealIdFromCache_InvalidInputZero() throws Throwable {
        // Given
        int mtDealGroupId = 0;
        // When
        CompletableFuture<Integer> result = mapperCacheWrapper.getDpDealIdFromCache(mtDealGroupId);
        // Then
        assertNotNull(result);
        assertEquals(0, result.get().intValue());
    }

    /**
     * Test case for invalid input with negative value
     */
    @Test
    public void testGetDpDealIdFromCache_InvalidInputNegative() throws Throwable {
        // Given
        int mtDealGroupId = -1;
        // When
        CompletableFuture<Integer> result = mapperCacheWrapper.getDpDealIdFromCache(mtDealGroupId);
        // Then
        assertNotNull(result);
        assertEquals(0, result.get().intValue());
    }
}
