package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.lion.client.Lion;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mockStatic;

@RunWith(MockitoJUnitRunner.class)
public class ShopReviewHelper_MtShopReviewNeedSupplyTest {

    private MockedStatic<Lion> lionMockedStatic;

    @Before
    public void setUp() {
        lionMockedStatic = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        lionMockedStatic.close();
    }

    /**
     * 测试 mtShopReviewNeedSupply 方法，当 Lion.getBooleanValue 返回 true 时
     */
    @Test
    public void testMtShopReviewNeedSupplyReturnTrue() throws Throwable {
            // arrange
        lionMockedStatic.when(() -> Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.mt.shop.review.supply.enable",
                false)).thenReturn(true);
        // act
        boolean result = ShopReviewHelper.mtShopReviewNeedSupply();
        // assert
        assertTrue(result);
    }

    /**
     * 测试 mtShopReviewNeedSupply 方法，当 Lion.getBooleanValue 返回 false 时
     */
    @Test
    public void testMtShopReviewNeedSupplyReturnFalse() throws Throwable {
        // arrange
        lionMockedStatic.when(() -> Lion.getBooleanValue("com.sankuai.dzu.tpbase.dztgdetailweb.mt.shop.review.supply.enable",
                false)).thenReturn(false);
        // act
        boolean result = ShopReviewHelper.mtShopReviewNeedSupply();
        // assert
        assertFalse(result);
    }
}
