package com.dianping.mobile.mapi.dztgdetail.biz.service;

import com.dianping.deal.idmapper.api.DealIdMapperService;
import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.dianping.deal.sales.common.datatype.KeyParam;
import com.dianping.deal.sales.common.datatype.SalesDisplayDTO;
import com.dianping.deal.shop.dto.DealGroupShop;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.RelatedRecommendCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedRecommendReq;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.SettableFuture;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.deal.shop.dto.DealGroupShopSearchRequest;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperCacheWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MoreDealsWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import org.apache.commons.collections4.MapUtils;
import org.junit.After;
import org.mockito.MockedStatic;

@RunWith(MockitoJUnitRunner.class)
public class RelatedRecommendServiceTest {

    @InjectMocks
    private RelatedRecommendService relatedRecommendService;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;
    @Mock
    private PoiClientWrapper poiClientWrapper;
    @Mock
    private MapperWrapper mapperWrapper;
    @Mock
    private MapperCacheWrapper mapperCacheWrapper;
    @Mock
    private RgcServiceWrapper rgcServiceWrapper;
    @Mock
    private PoiShopCategoryWrapper poiShopCategoryWrapper;
    @Mock
    private DealStockSaleWrapper dealStockSaleWrapper;
    @Mock
    private DealIdMapperService dealIdMapperService;
    @Mock
    private MoreDealsWrapper moreDealsWrapper;
    @Mock
    private PriceDisplayWrapper priceDisplayWrapper;
    @Mock
    private RecommendServiceWrapper recommendServiceWrapper;
    @Mock
    private RankWrapper rankWrapper;
    @Mock
    private DzDealThemeWrapper dzDealThemeWrapper;

    @Mock
    private ProductShopSearchComponent productShopSearchComponent;


    @Test
    public void testFillDealGroupInfoWithMtWxMini() {
        // 1. 准备测试数据
        // 创建RelatedRecommendCtx对象
        RelatedRecommendCtx ctx = new RelatedRecommendCtx();
        // 设置EnvCtx，模拟美团微信小程序环境
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP);
        envCtx.setAppDeviceId("test_device_id");
        envCtx.setMtUserId(123456L);
        envCtx.setDpUserId(0L);
        envCtx.setVersion("1.0.0");
        ctx.setEnvCtx(envCtx);
        // 设置DealCtx
        DealCtx dealCtx = new DealCtx(envCtx);
        dealCtx.setDpLongShopId(1000L);
        dealCtx.setMtLongShopId(2000L);
        dealCtx.setRealMtCityId(10);
        dealCtx.setDpCityId(1);
        ctx.setDealCtx(dealCtx);
        // 设置请求参数
        RelatedRecommendReq req = new RelatedRecommendReq();
        req.setCityId(10);
        req.setDealGroupId(12345);
        req.setShopIdStr("2000");
        req.setUserLat(31.2);
        req.setUserLng(121.5);
        ctx.setReq(req);
        // 2. Mock依赖
        // 模拟getDealGroupFuture返回
        Mockito.when(queryCenterWrapper.preDealGroupDTO(Mockito.any())).thenReturn(SettableFuture.create());
        // 模拟getStocksFuture返回
        Mockito.when(dealStockSaleWrapper.preUnifiedStocksFuture(Mockito.any())).thenReturn(SettableFuture.create());

        // 模拟getMtDpDIdBiMap返回
        List<IdMapper> idMappers = Lists.newArrayList();
        IdMapper idMapper = new IdMapper();
        idMapper.setMtDealGroupID(12345);
        idMapper.setDpDealGroupID(54321);
        idMappers.add(idMapper);
        Mockito.when(dealIdMapperService.queryByMtDealGroupIds(Mockito.anyList())).thenReturn(idMappers);

        // 模拟getMtByDpShopIds返回
        Map<Long, List<Long>> dpMtShopIds = new HashMap<>();
        dpMtShopIds.put(1000L, Lists.newArrayList(2000L));
        Mockito.when(mapperWrapper.getMtByDpShopIds(Mockito.anyList())).thenReturn(dpMtShopIds);

        // 模拟getDealThemeFuture返回
        Mockito.when(dzDealThemeWrapper.preQueryDealProduct(Mockito.any())).thenReturn(SettableFuture.create());
        DealProductResult dealProductResult = new DealProductResult();

        // 模拟getPriceFuture返回
        Mockito.when(priceDisplayWrapper.prepareByRequest(Mockito.any())).thenReturn(SettableFuture.create());

        // 模拟getDealGroupDTOs返回
        List<DealGroupDTO> dealGroupDTOs = Lists.newArrayList();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setMtDealGroupId(12345L);
        dealGroupDTO.setDpDealGroupId(54321L);
        dealGroupDTOs.add(dealGroupDTO);


        // 模拟getFutureResult返回
        // 3. 执行测试方法
        RelatedRecommendCtx result = relatedRecommendService.fillDealGroupInfo(ctx, Lists.newArrayList(12345), true);
        assertNull(result);
    }

    @Test
    public void testGetDealGroupShopMap_OldPath_EmptyDealGroupIds() throws Throwable {
        // arrange
        RelatedRecommendReq req = new RelatedRecommendReq();
        RelatedRecommendCtx ctx = new RelatedRecommendCtx();
        EnvCtx envCtx = new EnvCtx();
        ctx.setEnvCtx(envCtx);
        List<Integer> dealGroupIds = Collections.emptyList();
        BiMap<Integer, Integer> mtDpDIdBiMap = HashBiMap.create();
        // act
        Map<Integer, DealGroupShop> result = relatedRecommendService.getDealGroupShopMap(req, ctx, dealGroupIds, mtDpDIdBiMap);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(moreDealsWrapper, never()).getDealGroupShops(any(DealGroupShopSearchRequest.class));
    }

    @Test
    public void testGetDealGroupShopMap_OldPath_BothQueriesEmpty() throws Throwable {
        // arrange
        RelatedRecommendReq req = new RelatedRecommendReq();
        req.setUserLat(31.2);
        req.setUserLng(121.5);
        RelatedRecommendCtx ctx = new RelatedRecommendCtx();
        EnvCtx envCtx = new EnvCtx();
        ctx.setEnvCtx(envCtx);
        List<Integer> dealGroupIds = Arrays.asList(101, 102, 103);
        BiMap<Integer, Integer> mtDpDIdBiMap = HashBiMap.create();
        Map<Integer, DealGroupShop> emptyResult = Collections.emptyMap();
        // Both calls return empty
        // Both calls return empty
        when(moreDealsWrapper.getDealGroupShops(any(DealGroupShopSearchRequest.class))).thenReturn(emptyResult).thenReturn(emptyResult);
        // act
        Map<Integer, DealGroupShop> result = relatedRecommendService.getDealGroupShopMap(req, ctx, dealGroupIds, mtDpDIdBiMap);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        // Verify both calls
        verify(moreDealsWrapper, times(2)).getDealGroupShops(any(DealGroupShopSearchRequest.class));
    }

    @Test
    public void testGetDealGroupShopMap_OldPath_MT_EmptyBiMap() throws Throwable {
        // arrange
        RelatedRecommendReq req = new RelatedRecommendReq();
        req.setUserLat(31.2);
        req.setUserLng(121.5);
        req.setCityId(1);
        RelatedRecommendCtx ctx = new RelatedRecommendCtx();
        EnvCtx mockEnvCtx = Mockito.mock(EnvCtx.class);
        when(mockEnvCtx.isMt()).thenReturn(true);
        ctx.setEnvCtx(mockEnvCtx);
        List<Integer> dealGroupIds = Arrays.asList(1, 2, 3);
        BiMap<Integer, Integer> mtDpDIdBiMap = HashBiMap.create();
        // Empty BiMap
        // act
        Map<Integer, DealGroupShop> result = relatedRecommendService.getDealGroupShopMap(req, ctx, dealGroupIds, mtDpDIdBiMap);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(moreDealsWrapper, never()).getDealGroupShops(any(DealGroupShopSearchRequest.class));
    }

    @Test
    public void testGetDealGroupShopMap_OldPath_ExceptionInGetDealGroupShops() throws Throwable {
        // arrange
        RelatedRecommendReq req = new RelatedRecommendReq();
        req.setUserLat(31.2);
        req.setUserLng(121.5);
        RelatedRecommendCtx ctx = new RelatedRecommendCtx();
        EnvCtx envCtx = new EnvCtx();
        ctx.setEnvCtx(envCtx);
        List<Integer> dealGroupIds = Arrays.asList(101, 102, 103);
        BiMap<Integer, Integer> mtDpDIdBiMap = HashBiMap.create();
        when(moreDealsWrapper.getDealGroupShops(any(DealGroupShopSearchRequest.class))).thenThrow(new RuntimeException("Test exception"));
        // act & assert - Exception should be thrown
        try {
            relatedRecommendService.getDealGroupShopMap(req, ctx, dealGroupIds, mtDpDIdBiMap);
        } catch (RuntimeException e) {
            assertEquals("Test exception", e.getMessage());
        }
        verify(moreDealsWrapper).getDealGroupShops(any(DealGroupShopSearchRequest.class));
    }
}
