package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleExtraDTO;
import org.junit.runner.RunWith;
import static org.junit.Assert.*;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.List;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedModuleExtraFacade_EduFreeDealModuleRenameTest {

    private UnifiedModuleExtraFacade unifiedModuleExtraFacade;

    /**
     * 测试 eduFreeDealModuleRename 方法，当 ModuleExtraDTO 对象为 null 时
     */
    @Test(expected = NullPointerException.class)
    public void testEduFreeDealModuleRenameWhenModuleExtraDTOIsNull() throws Throwable {
        UnifiedModuleExtraFacade unifiedModuleExtraFacade = new UnifiedModuleExtraFacade();
        unifiedModuleExtraFacade.eduFreeDealModuleRename(null);
    }

    /**
     * 测试 eduFreeDealModuleRename 方法，当 ModuleExtraDTO 对象的 moduleConfigDos 列表为空时
     */
    @Test
    public void testEduFreeDealModuleRenameWhenModuleConfigDosIsEmpty() throws Throwable {
        UnifiedModuleExtraFacade unifiedModuleExtraFacade = new UnifiedModuleExtraFacade();
        ModuleExtraDTO moduleExtraDTO = new ModuleExtraDTO();
        // Initialize the list to avoid NullPointerException
        moduleExtraDTO.setModuleConfigDos(new ArrayList<>());
        ModuleExtraDTO result = unifiedModuleExtraFacade.eduFreeDealModuleRename(moduleExtraDTO);
        assertNotNull(result);
        assertTrue(result.getModuleConfigDos().isEmpty());
    }

    /**
     * 测试 eduFreeDealModuleRename 方法，当 ModuleExtraDTO 对象的 moduleConfigDos 列表不为空，但列表中的所有 ModuleConfigDo 对象的 key 属性值都不等于 "团购详情" 和 "购买须知" 时
     */
    @Test
    public void testEduFreeDealModuleRenameWhenModuleConfigDosIsNotEmptyButKeysAreNotMatched() throws Throwable {
        UnifiedModuleExtraFacade unifiedModuleExtraFacade = new UnifiedModuleExtraFacade();
        ModuleExtraDTO moduleExtraDTO = new ModuleExtraDTO();
        ModuleConfigDo moduleConfigDo1 = new ModuleConfigDo();
        moduleConfigDo1.setKey("其他详情");
        ModuleConfigDo moduleConfigDo2 = new ModuleConfigDo();
        moduleConfigDo2.setKey("其他须知");
        ArrayList<ModuleConfigDo> moduleConfigDos = new ArrayList<>();
        moduleConfigDos.add(moduleConfigDo1);
        moduleConfigDos.add(moduleConfigDo2);
        moduleExtraDTO.setModuleConfigDos(moduleConfigDos);
        ModuleExtraDTO result = unifiedModuleExtraFacade.eduFreeDealModuleRename(moduleExtraDTO);
        assertNotNull(result);
        assertEquals("其他详情", result.getModuleConfigDos().get(0).getKey());
        assertEquals("其他须知", result.getModuleConfigDos().get(1).getKey());
    }

    /**
     * 测试 eduFreeDealModuleRename 方法，当 ModuleExtraDTO 对象的 moduleConfigDos 列表不为空，列表中的某些 ModuleConfigDo 对象的 key 属性值等于 "团购详情"，某些等于 "购买须知"，某些既不等于 "团购详情" 也不等于 "购买须知" 时
     */
    @Test
    public void testEduFreeDealModuleRenameWhenModuleConfigDosIsNotEmptyAndKeysAreMixed() throws Throwable {
        UnifiedModuleExtraFacade unifiedModuleExtraFacade = new UnifiedModuleExtraFacade();
        ModuleExtraDTO moduleExtraDTO = new ModuleExtraDTO();
        ModuleConfigDo moduleConfigDo1 = new ModuleConfigDo();
        moduleConfigDo1.setKey("团购详情");
        ModuleConfigDo moduleConfigDo2 = new ModuleConfigDo();
        moduleConfigDo2.setKey("其他须知");
        ModuleConfigDo moduleConfigDo3 = new ModuleConfigDo();
        moduleConfigDo3.setKey("购买须知");
        ArrayList<ModuleConfigDo> moduleConfigDos = new ArrayList<>();
        moduleConfigDos.add(moduleConfigDo1);
        moduleConfigDos.add(moduleConfigDo2);
        moduleConfigDos.add(moduleConfigDo3);
        moduleExtraDTO.setModuleConfigDos(moduleConfigDos);
        ModuleExtraDTO result = unifiedModuleExtraFacade.eduFreeDealModuleRename(moduleExtraDTO);
        assertNotNull(result);
        assertEquals("课程详情", result.getModuleConfigDos().get(0).getKey());
        assertEquals("其他须知", result.getModuleConfigDos().get(1).getKey());
        assertEquals("报名须知", result.getModuleConfigDos().get(2).getKey());
    }
}
