package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.swan.udqs.api.QueryData;
import com.sankuai.swan.udqs.api.Result;
import com.sankuai.swan.udqs.api.SwanUniteQueryService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class SwanWrapperGetReviewPhraseListTest {

    @InjectMocks
    private SwanWrapper swanWrapper;

    @Mock
    private SwanUniteQueryService swanUniteQueryService;

    private String dpShopId;

    private Result<QueryData> queryDataResult;

    private QueryData queryData;

    @Before
    public void setUp() {
        dpShopId = "123";
        queryDataResult = new Result<>();
        queryData = new QueryData();
    }

    @Test
    public void testGetReviewPhraseListDpShopIdIsNotNumeric() throws Throwable {
        dpShopId = "abc";
        List<String> result = swanWrapper.getReviewPhraseList(dpShopId);
        assertEquals(new ArrayList<>(), result);
    }

    @Test
    public void testGetReviewPhraseListSwanQueryReturnEmptyResultSet() throws Throwable {
        queryDataResult.setIfSuccess(true);
        queryDataResult.setData(queryData);
        queryData.setResultSet(new ArrayList<>());
        assertTrue(true);
    }
}
