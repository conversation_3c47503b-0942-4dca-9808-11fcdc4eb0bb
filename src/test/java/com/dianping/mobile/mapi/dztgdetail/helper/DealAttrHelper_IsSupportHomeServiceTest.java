package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealAttrHelper_IsSupportHomeServiceTest {

    private static final String SUPPORT_HOME_SERVICE = "supportHomeService";

    @Test
    public void testIsSupportHomeServiceWhenAttrsIsNull() throws Throwable {
        boolean result = DealAttrHelper.isSupportHomeService(null);
        assertFalse("Expected false when attrs is null", result);
    }

    @Test
    public void testIsSupportHomeServiceWhenAttrsHasNoSupportHomeService() throws Throwable {
        AttributeDTO attr = new AttributeDTO();
        attr.setName("other");
        attr.setValue(Collections.singletonList("no"));
        boolean result = DealAttrHelper.isSupportHomeService(Arrays.asList(attr));
        assertFalse("Expected false when attrs has no supportHomeService", result);
    }

    @Test
    public void testIsSupportHomeServiceWhenSupportHomeServiceValueIsEmpty() throws Throwable {
        AttributeDTO attr = new AttributeDTO();
        attr.setName(SUPPORT_HOME_SERVICE);
        attr.setValue(Collections.emptyList());
        boolean result = DealAttrHelper.isSupportHomeService(Arrays.asList(attr));
        assertFalse("Expected false when supportHomeService value is empty", result);
    }

    @Test
    public void testIsSupportHomeServiceWhenSupportHomeServiceValueIsNotYes() throws Throwable {
        AttributeDTO attr = new AttributeDTO();
        attr.setName(SUPPORT_HOME_SERVICE);
        attr.setValue(Collections.singletonList("no"));
        boolean result = DealAttrHelper.isSupportHomeService(Arrays.asList(attr));
        assertFalse("Expected false when supportHomeService value is not '是'", result);
    }

    /**
     * Test when serviceProjectMustGroups is empty
     */
    @Test
    public void testIsToHomeFixDealWithEmptyServiceProjectMustGroups() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupServiceProjectDTO serviceProjectDTO = new DealGroupServiceProjectDTO();
        serviceProjectDTO.setMustGroups(new ArrayList<>());
        dealGroupDTO.setServiceProject(serviceProjectDTO);
        // act
        boolean result = DealAttrHelper.isToHomeFixDeal(dealGroupDTO);
        // assert
        assertFalse(result);
    }

    /**
     * Test when first element in serviceProjectMustGroups is null
     */
    @Test
    public void testIsToHomeFixDealWithNullFirstServiceProjectMustGroup() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupServiceProjectDTO serviceProjectDTO = new DealGroupServiceProjectDTO();
        List<MustServiceProjectGroupDTO> mustGroups = Arrays.asList(null, new MustServiceProjectGroupDTO());
        serviceProjectDTO.setMustGroups(mustGroups);
        dealGroupDTO.setServiceProject(serviceProjectDTO);
        // act
        boolean result = DealAttrHelper.isToHomeFixDeal(dealGroupDTO);
        // assert
        assertFalse(result);
    }

    /**
     * Test when groups is empty
     */
    @Test
    public void testIsToHomeFixDealWithEmptyGroups() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupServiceProjectDTO serviceProjectDTO = new DealGroupServiceProjectDTO();
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        mustGroup.setGroups(new ArrayList<>());
        serviceProjectDTO.setMustGroups(Collections.singletonList(mustGroup));
        dealGroupDTO.setServiceProject(serviceProjectDTO);
        // act
        boolean result = DealAttrHelper.isToHomeFixDeal(dealGroupDTO);
        // assert
        assertFalse(result);
    }

    /**
     * Test when first element in groups is null
     */
    @Test
    public void testIsToHomeFixDealWithNullFirstGroup() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupServiceProjectDTO serviceProjectDTO = new DealGroupServiceProjectDTO();
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        mustGroup.setGroups(Arrays.asList(null, new ServiceProjectDTO()));
        serviceProjectDTO.setMustGroups(Collections.singletonList(mustGroup));
        dealGroupDTO.setServiceProject(serviceProjectDTO);
        // act
        boolean result = DealAttrHelper.isToHomeFixDeal(dealGroupDTO);
        // assert
        assertFalse(result);
    }

    /**
     * Test when attrs is empty
     */
    @Test
    public void testIsToHomeFixDealWithEmptyAttrs() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupServiceProjectDTO serviceProjectDTO = new DealGroupServiceProjectDTO();
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        ServiceProjectDTO serviceProject = new ServiceProjectDTO();
        serviceProject.setAttrs(new ArrayList<>());
        mustGroup.setGroups(Collections.singletonList(serviceProject));
        serviceProjectDTO.setMustGroups(Collections.singletonList(mustGroup));
        dealGroupDTO.setServiceProject(serviceProjectDTO);
        // act
        boolean result = DealAttrHelper.isToHomeFixDeal(dealGroupDTO);
        // assert
        assertFalse(result);
    }

    /**
     * Test when judgeType is null
     */
    @Test
    public void testIsToHomeFixDealWithNullJudgeType() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupServiceProjectDTO serviceProjectDTO = new DealGroupServiceProjectDTO();
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        ServiceProjectDTO serviceProject = new ServiceProjectDTO();
        ServiceProjectAttrDTO attr = new ServiceProjectAttrDTO();
        serviceProject.setAttrs(Collections.singletonList(attr));
        mustGroup.setGroups(Collections.singletonList(serviceProject));
        serviceProjectDTO.setMustGroups(Collections.singletonList(mustGroup));
        dealGroupDTO.setServiceProject(serviceProjectDTO);
        dealGroupDTO.setCategory(new DealGroupCategoryDTO());
        // act
        boolean result = DealAttrHelper.isToHomeFixDeal(dealGroupDTO);
        // assert
        assertFalse(result);
    }
}
