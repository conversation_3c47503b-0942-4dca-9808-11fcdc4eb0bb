package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.dztheme.deal.operatorpage.DealProductBizQueryService;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.concurrent.Future;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.*;

public class DzDealThemeWrapperTest {

    @InjectMocks
    private DzDealThemeWrapper dzDealThemeWrapper;

    @Mock
    private DealProductBizQueryService dealProductBizQueryService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 request 为 null 的情况
     */
    @Test
    public void testPreQueryDealSubTitleRequestIsNull() throws Throwable {
        // arrange
        DealProductRequest request = null;
        // act
        Future result = dzDealThemeWrapper.preQueryDealSubTitle(request);
        // assert
        assertNull(result);
    }

    /**
     * 测试 request 不为 null，且 dealProductBizQueryService.queryDealSubtitle(request) 方法正常执行的情况
     */
    @Test
    public void testPreQueryDealSubTitleRequestIsNotNullAndQueryDealSubtitleSuccess() throws Throwable {
        // arrange
        DealProductRequest request = new DealProductRequest();
        // act
        Future result = dzDealThemeWrapper.preQueryDealSubTitle(request);
        // assert
        verify(dealProductBizQueryService, times(1)).queryDealSubtitle(request);
    }

    /**
     * 测试 request 不为 null，但 dealProductBizQueryService.queryDealSubtitle(request) 方法执行时抛出异常的情况
     */
    @Test
    public void testPreQueryDealSubTitleRequestIsNotNullAndQueryDealSubtitleThrowsException() throws Throwable {
        // arrange
        DealProductRequest request = new DealProductRequest();
        doThrow(new RuntimeException()).when(dealProductBizQueryService).queryDealSubtitle(request);
        // act
        Future result = dzDealThemeWrapper.preQueryDealSubTitle(request);
        // assert
        assertNull(result);
    }
}
