package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.impl;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryParam;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.entity.ImageTextStrategyRule;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PhotoCategoryStrategyImplMatchingRuleTest {

    private PhotoCategoryStrategyImpl photoCategoryStrategy = new PhotoCategoryStrategyImpl();

    private PhotoCategoryStrategyImpl photoCategoryStrategyImpl;

    @Mock
    private LionConfigUtils lionConfigUtils;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        photoCategoryStrategyImpl = new PhotoCategoryStrategyImpl();
    }

    private boolean invokeMatchingRule(ImageTextStrategyRule rule, int categoryId, String serviceType) throws Exception {
        Method method = PhotoCategoryStrategyImpl.class.getDeclaredMethod("matchingRule", ImageTextStrategyRule.class, int.class, String.class);
        method.setAccessible(true);
        return (boolean) method.invoke(photoCategoryStrategy, rule, categoryId, serviceType);
    }

    @Test
    public void testMatchingRuleBothEmpty() throws Throwable {
        ImageTextStrategyRule rule = new ImageTextStrategyRule();
        int categoryId = 1;
        String serviceType = "serviceType";
        assertFalse(invokeMatchingRule(rule, categoryId, serviceType));
    }

    @Test
    public void testMatchingRuleCategoryIdsEmpty() throws Throwable {
        ImageTextStrategyRule rule = new ImageTextStrategyRule();
        rule.setServiceTypes(Arrays.asList("serviceType"));
        int categoryId = 1;
        String serviceType = "serviceType";
        assertFalse(invokeMatchingRule(rule, categoryId, serviceType));
    }

    @Test
    public void testMatchingRuleServiceTypesEmpty() throws Throwable {
        ImageTextStrategyRule rule = new ImageTextStrategyRule();
        rule.setCategoryIds(Arrays.asList(1));
        int categoryId = 1;
        String serviceType = "serviceType";
        assertFalse(invokeMatchingRule(rule, categoryId, serviceType));
    }

    @Test
    public void testMatchingRuleCategoryIdsNotContainsCategoryId() throws Throwable {
        ImageTextStrategyRule rule = new ImageTextStrategyRule();
        rule.setCategoryIds(Arrays.asList(2));
        rule.setServiceTypes(Arrays.asList("serviceType"));
        int categoryId = 1;
        String serviceType = "serviceType";
        assertFalse(invokeMatchingRule(rule, categoryId, serviceType));
    }

    @Test
    public void testMatchingRuleServiceTypesNotContainsServiceType() throws Throwable {
        ImageTextStrategyRule rule = new ImageTextStrategyRule();
        rule.setCategoryIds(Arrays.asList(1));
        rule.setServiceTypes(Arrays.asList("otherServiceType"));
        int categoryId = 1;
        String serviceType = "serviceType";
        assertFalse(invokeMatchingRule(rule, categoryId, serviceType));
    }

    @Test
    public void testMatchingRuleBothContains() throws Throwable {
        ImageTextStrategyRule rule = new ImageTextStrategyRule();
        rule.setCategoryIds(Arrays.asList(1));
        rule.setServiceTypes(Arrays.asList("serviceType"));
        int categoryId = 1;
        String serviceType = "serviceType";
        assertTrue(invokeMatchingRule(rule, categoryId, serviceType));
    }

    /**
     * 测试当 dealCategoryParam 为 null 时，返回 false
     */
    @Test
    public void testNewDealStyleWithNullDealCategoryParam() throws Throwable {
        // arrange
        DealCategoryParam dealCategoryParam = null;
        // act
        boolean result = photoCategoryStrategyImpl.newDealStyle(dealCategoryParam);
        // assert
        assertFalse(result);
    }

    /**
     * 测试当 dealGroupDTO 为 null 时，返回 false
     */
    @Test
    public void testNewDealStyleWithNullDealGroupDTO() throws Throwable {
        // arrange
        DealCategoryParam dealCategoryParam = DealCategoryParam.builder().dealGroupDTO(null).build();
        // act
        boolean result = photoCategoryStrategyImpl.newDealStyle(dealCategoryParam);
        // assert
        assertFalse(result);
    }
}
