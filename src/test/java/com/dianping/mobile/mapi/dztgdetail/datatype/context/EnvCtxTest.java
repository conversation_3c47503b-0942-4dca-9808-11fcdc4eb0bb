package com.dianping.mobile.mapi.dztgdetail.datatype.context;

import com.alibaba.fastjson.JSON;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.util.useragent.UserAgentUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class EnvCtxTest {

    /**
     * 测试 isThirdPlatform 方法，当 dztgClientTypeEnum 等于 THIRD_PLATFORM 时，应返回 true
     */
    @Test
    public void testIsThirdPlatformWhenDztgClientTypeEnumIsThirdPlatform() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx = JSON.parseObject("{\"android\":false,\"apollo\":false,\"appDeviceId\":\"c05bcc487c8b4d8584b872d8dccebc24a170233661538604446\",\"appId\":4,\"clientType\":100502,\"dianpingClientList\":[\"DIANPING_APP\",\"DIANPING_FROM_H5\",\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"],\"dpMerchant\":false,\"dpMiniApp\":false,\"dpMiniAppList\":[\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"],\"dpUserId\":0,\"dztgClientTypeEnum\":\"THIRD_PLATFORM\",\"external\":true,\"externalAndNoScene\":true,\"fromH5\":false,\"ios\":true,\"login\":false,\"mainApp\":true,\"mainWX\":false,\"mainWeb\":false,\"meituanClientList\":[\"MEITUAN_APP\",\"MEITUAN_FROM_H5\",\"MEITUAN_MAP_APP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_WEIXIN_MINIAPP\"],\"merchantClientList\":[\"DPMERCHANT\"],\"miniApp\":false,\"mt\":false,\"mtMiniApp\":false,\"mtMiniAppList\":[\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_KUAISHOU_MINIAPP\",\"MEITUAN_WANWU_MINIAPP\"],\"mtUserId\":0,\"mtsiFlag\":\"0\",\"native\":false,\"nativeAppList\":[\"MEITUAN_APP\",\"MEITUAN_MAP_APP\",\"DIANPING_APP\"],\"realMtUserId\":0,\"requestURI\":\"/general/platform/dztgdetail/dzdealbase.bin\",\"startTime\":1705561819558,\"thirdDztgClientTypeEnum\":\"APOLLO\",\"thirdPlatform\":true,\"unionId\":\"c05bcc487c8b4d8584b872d8dccebc24a170233661538604446\",\"userAgent\":\"MApi 1.4 (dpappolo 5.6.1 appstore; iPhone 14.8 iPhone13,2)\",\"userIp\":\"***************\",\"uuid\":\"c05bcc487c8b4d8584b872d8dccebc24a170233661538604446\",\"version\":\"11.4.10\"}",EnvCtx.class);
        // act
        boolean result = envCtx.isThirdPlatform();
        // assert
        assertTrue(result);
    }

    /**
     * 测试 isThirdPlatform 方法，当 dztgClientTypeEnum 不等于 THIRD_PLATFORM 时，应返回 false
     */
    @Test
    public void testIsThirdPlatformWhenDztgClientTypeEnumIsNotThirdPlatform() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        // act
        boolean result = envCtx.isThirdPlatform();
        UserAgentUtils.isApollo("MApi 1.4 (dpappolo 5.6.1 appstore; iPhone 14.8 iPhone13,2)");
        // assert
        assertFalse(result);
    }

    /**
     * 测试 isApollo 方法，当 dztgClientTypeEnum 等于 APOLLO 时，应返回 true
     */
    @Test
    public void testIsApolloWhenDztgClientTypeEnumIsApollo() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.APOLLO);
        // act
        boolean result = envCtx.isApollo();
        // assert
        assertTrue(result);
    }

    /**
     * 测试 isApollo 方法，当 dztgClientTypeEnum 不等于 APOLLO 时，应返回 false
     */
    @Test
    public void testIsApolloWhenDztgClientTypeEnumIsNotApollo() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        // act
        boolean result = envCtx.isApollo();
        // assert
        assertFalse(result);
    }

    /**
     * 测试 isApollo 方法，当 dztgClientTypeEnum 为 null 时，应返回 false
     */
    @Test
    public void testIsApolloWhenDztgClientTypeEnumIsNull() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(null);
        // act
        boolean result = envCtx.isApollo();
        // assert
        assertFalse(result);
    }

    /**
     * 测试 isMtLiveMinApp 方法，当 dztgClientTypeEnum 等于 meituan_LIVE_WEIXIN_MINIAPP 时，应返回 true
     */
    @Test
    public void testIsMtLiveMinAppWhenDztgClientTypeEnumIsAmznLiveWeixinMiniApp() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_LIVE_WEIXIN_MINIAPP);

        // act
        boolean result = envCtx.isMtLiveMinApp();

        // assert
        assertTrue(result);
    }

    /**
     * 测试 isMtLiveMinApp 方法，当 dztgClientTypeEnum 等于 meituan_LIVE_ORDER_WEIXIN_MINIAPP 时，应返回 true
     */
    @Test
    public void testIsMtLiveMinAppWhenDztgClientTypeEnumIsAmznLiveOrderWeixinMiniApp() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_LIVE_ORDER_WEIXIN_MINIAPP);

        // act
        boolean result = envCtx.isMtLiveMinApp();

        // assert
        assertTrue(result);
    }

    /**
     * 测试 isMtLiveMinApp 方法，当 dztgClientTypeEnum 不等于 meituan_LIVE_WEIXIN_MINIAPP 且不等于 meituan_LIVE_ORDER_WEIXIN_MINIAPP 时，应返回 false
     */
    @Test
    public void testIsMtLiveMinAppWhenDztgClientTypeEnumIsNotAmznLiveWeixinMiniAppAndAmznLiveOrderWeixinMiniApp() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);

        // act
        boolean result = envCtx.isMtLiveMinApp();

        // assert
        assertFalse(result);
    }
}
