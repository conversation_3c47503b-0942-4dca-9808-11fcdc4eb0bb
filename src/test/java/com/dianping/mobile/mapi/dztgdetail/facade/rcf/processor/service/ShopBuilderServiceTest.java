package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiClientWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ShopCategoryEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ShopPBO;
import com.dianping.poi.bizhour.BizHourForecastService;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Test class for ShopBuilderService
 */
@RunWith(MockitoJUnitRunner.class)
public class ShopBuilderServiceTest {

    @InjectMocks
    private ShopBuilderService shopBuilderService;

    @Mock
    private DouHuService douHuService;

    @Mock
    private PoiClientWrapper poiClientWrapper;

    @Mock
    private MapperWrapper mapperWrapper;

    @Mock
    private BestShopDTO bestShop;

    @Mock
    private DealCtx ctx;

    @Mock
    private EnvCtx envCtx;

    private Method getPhoneNosMethod;

    @Mock
    private BizHourForecastService bizHourForecastService;

    @Before
    public void setUp() throws Exception {
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        getPhoneNosMethod = ShopBuilderService.class.getDeclaredMethod("getPhoneNos", BestShopDTO.class, DealCtx.class);
        getPhoneNosMethod.setAccessible(true);
    }

    /**
     * Test when main app with valid poi phones
     */
    @Test
    public void testGetPhoneNos_MainAppWithValidPoiPhones() throws Throwable {
        // arrange
        List<String> expectedPhones = Arrays.asList("1234567890", "0987654321");
        when(envCtx.isMainApp()).thenReturn(true);
        when(ctx.getPoiPhones()).thenReturn(expectedPhones);
        // act
        List<String> result = (List<String>) getPhoneNosMethod.invoke(shopBuilderService, bestShop, ctx);
        // assert
        assertEquals("Phone numbers should match", expectedPhones, result);
        verify(envCtx).isMainApp();
        verify(ctx, atLeastOnce()).getPoiPhones();
    }

    /**
     * Test when main app with empty poi phones
     */
    @Test
    public void testGetPhoneNos_MainAppWithEmptyPoiPhones() throws Throwable {
        // arrange
        List<String> expectedPhones = Arrays.asList("1234567890", "0987654321");
        when(envCtx.isMainApp()).thenReturn(true);
        when(ctx.getPoiPhones()).thenReturn(Collections.emptyList());
        when(bestShop.getPhoneNos()).thenReturn(expectedPhones);
        // act
        List<String> result = (List<String>) getPhoneNosMethod.invoke(shopBuilderService, bestShop, ctx);
        // assert
        assertEquals("Should fall back to bestShop phone numbers", expectedPhones, result);
        verify(bestShop).getPhoneNos();
    }

    /**
     * Test when main app with null poi phones
     */
    @Test
    public void testGetPhoneNos_MainAppWithNullPoiPhones() throws Throwable {
        // arrange
        List<String> expectedPhones = Arrays.asList("1234567890", "0987654321");
        when(envCtx.isMainApp()).thenReturn(true);
        when(ctx.getPoiPhones()).thenReturn(null);
        when(bestShop.getPhoneNos()).thenReturn(expectedPhones);
        // act
        List<String> result = (List<String>) getPhoneNosMethod.invoke(shopBuilderService, bestShop, ctx);
        // assert
        assertEquals("Should fall back to bestShop phone numbers", expectedPhones, result);
        verify(bestShop).getPhoneNos();
    }

    /**
     * Test when not main app with valid best shop phones
     */
    @Test
    public void testGetPhoneNos_NotMainAppWithValidBestShopPhones() throws Throwable {
        // arrange
        List<String> expectedPhones = Arrays.asList("1234567890", "0987654321");
        when(envCtx.isMainApp()).thenReturn(false);
        when(bestShop.getPhoneNos()).thenReturn(expectedPhones);
        // act
        List<String> result = (List<String>) getPhoneNosMethod.invoke(shopBuilderService, bestShop, ctx);
        // assert
        assertEquals("Should use bestShop phone numbers", expectedPhones, result);
        verify(bestShop).getPhoneNos();
    }

    /**
     * Test when not main app with empty best shop phones
     */
    @Test
    public void testGetPhoneNos_NotMainAppWithEmptyBestShopPhones() throws Throwable {
        // arrange
        when(envCtx.isMainApp()).thenReturn(false);
        when(bestShop.getPhoneNos()).thenReturn(Collections.emptyList());
        // act
        List<String> result = (List<String>) getPhoneNosMethod.invoke(shopBuilderService, bestShop, ctx);
        // assert
        assertEquals("Should return empty list", Collections.emptyList(), result);
        verify(bestShop).getPhoneNos();
    }

    /**
     * Test when not main app with null best shop phones
     */
    @Test
    public void testGetPhoneNos_NotMainAppWithNullBestShopPhones() throws Throwable {
        // arrange
        when(envCtx.isMainApp()).thenReturn(false);
        when(bestShop.getPhoneNos()).thenReturn(null);
        // act
        List<String> result = (List<String>) getPhoneNosMethod.invoke(shopBuilderService, bestShop, ctx);
        // assert
        assertNull("Should return null for null phones", result);
        verify(bestShop).getPhoneNos();
    }

    /**
     * Test cleaning self own deal case
     */
    @Test
    public void testGetPhoneNos_CleaningSelfOwnDeal() throws Throwable {
        // arrange
        when(ctx.isCleaningSelfOwnDeal()).thenReturn(true);
        // act
        List<String> result = (List<String>) getPhoneNosMethod.invoke(shopBuilderService, bestShop, ctx);
        // assert
        assertEquals("Should return empty list for cleaning self own deal", Collections.emptyList(), result);
        verify(ctx).isCleaningSelfOwnDeal();
    }

    /**
     * Test care free deal case
     */
    @Test
    public void testGetPhoneNos_CareFreeDeal() throws Throwable {
        // arrange
        when(ctx.isCareFreeDeal()).thenReturn(true);
        // act
        List<String> result = (List<String>) getPhoneNosMethod.invoke(shopBuilderService, bestShop, ctx);
        // assert
        assertEquals("Should return empty list for care free deal", Collections.emptyList(), result);
        verify(ctx).isCareFreeDeal();
    }

    /**
     * Test when phone numbers need filtering
     */
    @Test
    public void testGetPhoneNos_FilterPhoneNumbers() throws Throwable {
        // arrange
        List<String> inputPhones = Arrays.asList("4001234567", "8001234567", "1234567890");
        when(envCtx.isMainApp()).thenReturn(false);
        when(bestShop.getPhoneNos()).thenReturn(inputPhones);
        when(ctx.getPoiBackCategoryIds()).thenReturn(new HashSet<>(Collections.singletonList(1)));
        // act
        List<String> result = (List<String>) getPhoneNosMethod.invoke(shopBuilderService, bestShop, ctx);
        // assert
        assertEquals("Should return all phone numbers when no filtering", inputPhones, result);
        verify(bestShop).getPhoneNos();
        verify(ctx, atLeastOnce()).getPoiBackCategoryIds();
    }

    @Test
    public void testGetShopPBOWithNullContext() throws Throwable {
        // arrange
        DealCtx ctx = null;
        // act
        ShopPBO result = shopBuilderService.getShopPBO(ctx);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetShopPBOWithNullBestShopResp() throws Throwable {
        // arrange
        DealCtx ctx = createBasicDealCtx(true);
        ctx.setBestShopResp(null);
        // act
        ShopPBO result = shopBuilderService.getShopPBO(ctx);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetShopPBOWithMtPlatform() throws Throwable {
        // arrange
        DealCtx ctx = createBasicDealCtx(true);
        BestShopDTO bestShop = createBestShopDTO(123456L, 654321L);
        ctx.setBestShopResp(bestShop);
        ctx.setMtLongShopId(123456L);
        // act
        ShopPBO result = shopBuilderService.getShopPBO(ctx);
        // assert
        assertNotNull(result);
        assertEquals(123456L, result.getShopId());
    }

    @Test
    public void testGetShopPBOWithMtWeixinMiniApp() throws Throwable {
        // arrange
        DealCtx ctx = createBasicDealCtx(true);
        EnvCtx envCtx = ctx.getEnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP);
        BestShopDTO bestShop = createBestShopDTO(123456L, 654321L);
        bestShop.setLat(30.0);
        bestShop.setLng(120.0);
        bestShop.setGlat(31.0);
        bestShop.setGlng(121.0);
        ctx.setBestShopResp(bestShop);
        // act
        ShopPBO result = shopBuilderService.getShopPBO(ctx);
        // assert
        assertNotNull(result);
        assertEquals(bestShop.getGlat(), result.getLat(), 0.001);
        assertEquals(bestShop.getGlng(), result.getLng(), 0.001);
    }

    @Test
    public void testGetShopPBOWithDpPlatform() throws Throwable {
        // arrange
        DealCtx ctx = createBasicDealCtx(false);
        BestShopDTO bestShop = createBestShopDTO(123456L, 654321L);
        bestShop.setLat(30.0);
        bestShop.setLng(120.0);
        bestShop.setGlat(31.0);
        bestShop.setGlng(121.0);
        ctx.setBestShopResp(bestShop);
        // act
        ShopPBO result = shopBuilderService.getShopPBO(ctx);
        // assert
        assertNotNull(result);
        assertEquals(bestShop.getGlat(), result.getLat(), 0.001);
        assertEquals(bestShop.getGlng(), result.getLng(), 0.001);
    }

    @Test
    public void testGetShopPBOWithMultipleShops() throws Throwable {
        // arrange
        DealCtx ctx = createBasicDealCtx(false);
        BestShopDTO bestShop = createBestShopDTO(123456L, 654321L);
        bestShop.setTotalShopsNum(5);
        ctx.setBestShopResp(bestShop);
        // act
        ShopPBO result = shopBuilderService.getShopPBO(ctx);
        // assert
        assertNotNull(result);
        assertEquals("5家门店适用", result.getShopListDesc());
    }

    @Test
    public void testGetShopPBOWithBeamApp() throws Throwable {
        // arrange
        DealCtx ctx = createBasicDealCtx(true);
        EnvCtx envCtx = ctx.getEnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.BEAM_APP);
        BestShopDTO bestShop = createBestShopDTO(123456L, 654321L);
        bestShop.setTotalShopsNum(5);
        ctx.setBestShopResp(bestShop);
        // act
        ShopPBO result = shopBuilderService.getShopPBO(ctx);
        // assert
        assertNotNull(result);
        assertEquals("", result.getShopListUrl());
        assertEquals("", result.getShopListDesc());
        assertTrue(result.isLyyShop());
    }

    @Test
    public void testGetShopPBOWithShopCategoryEnum() throws Throwable {
        // arrange
        DealCtx ctx = createBasicDealCtx(false);
        BestShopDTO bestShop = createBestShopDTO(123456L, 654321L);
        ctx.setBestShopResp(bestShop);
        ctx.setShopCategoryEnum(ShopCategoryEnum.WEARABLE_NAIL_ONLY);
        // act
        ShopPBO result = shopBuilderService.getShopPBO(ctx);
        // assert
        assertNotNull(result);
        assertEquals(ShopCategoryEnum.WEARABLE_NAIL_ONLY.getCode(), result.getShopCategoryId());
    }

    @Test
    public void testGetShopPBOWithDoorToDoorService() throws Throwable {
        // arrange
        DealCtx ctx = createBasicDealCtx(false);
        BestShopDTO bestShop = createBestShopDTO(123456L, 654321L);
        bestShop.setDistance("500m");
        ctx.setBestShopResp(bestShop);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(123);
        ctx.setChannelDTO(channelDTO);
        // act
        ShopPBO result = shopBuilderService.getShopPBO(ctx);
        // assert
        assertNotNull(result);
        assertEquals("距您500m", result.getDistanceDesc());
        assertEquals("500m", result.getDistance());
    }

    private DealCtx createBasicDealCtx(boolean isMt) {
        EnvCtx envCtx = new EnvCtx();
        if (isMt) {
            envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        } else {
            envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        }
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setRequestSource("normal");
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        ctx.setDealGroupDTO(dealGroupDTO);
        return ctx;
    }

    private BestShopDTO createBestShopDTO(long mtShopId, long dpShopId) {
        BestShopDTO bestShop = new BestShopDTO();
        bestShop.setMtShopId(mtShopId);
        bestShop.setDpShopId(dpShopId);
        bestShop.setShopName("Test Shop");
        bestShop.setAddress("Test Address");
        bestShop.setPhoneNos(Arrays.asList("123456789"));
        bestShop.setShopPower(45);
        bestShop.setTotalShopsNum(1);
        return bestShop;
    }
}
