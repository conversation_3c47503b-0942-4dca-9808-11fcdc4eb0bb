package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule.DealDetailSpecificModuleHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule.DealDetailSpecificModuleHandlerContainer;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule.SpecificModuleCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.SpecificModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailSpecificModuleVO;
import com.dianping.mobile.mapi.dztgdetail.exception.QueryCenterResultException;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealDetailFacadeGetDealDetailSpecificModuleFromQueryCenterTest {

    @InjectMocks
    private DealDetailFacade dealDetailFacade;

    @Mock
    private DealGroupQueryService queryCenterDealGroupQueryService;

    @Mock
    private DealDetailSpecificModuleHandlerContainer dealDetailSpecificModuleHandlerContainer;

    private SpecificModuleReq request;

    private EnvCtx envCtx;

    @Before
    public void setUp() {
        request = new SpecificModuleReq();
        request.setDealgroupid(123);
        envCtx = new EnvCtx();
    }

    /**
     * Test successful case with valid response and handler
     */
    @Test
    public void testGetDealDetailSpecificModuleFromQueryCenter_Success() throws Throwable {
        // arrange
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(0);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(100L);
        dealGroupDTO.setCategory(categoryDTO);
        QueryDealGroupListResult resultData = new QueryDealGroupListResult();
        resultData.setList(Collections.singletonList(dealGroupDTO));
        response.setData(resultData);
        DealDetailSpecificModuleHandler handler = mock(DealDetailSpecificModuleHandler.class);
        DealDetailSpecificModuleVO expectedResult = new DealDetailSpecificModuleVO();
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(response);
        when(dealDetailSpecificModuleHandlerContainer.getHandler("100")).thenReturn(handler);
        doAnswer(invocation -> {
            SpecificModuleCtx ctx = invocation.getArgument(0);
            ctx.setResult(expectedResult);
            return null;
        }).when(handler).handle(any(SpecificModuleCtx.class));
        // act
        Method method = DealDetailFacade.class.getDeclaredMethod("getDealDetailSpecificModuleFromQueryCenter", SpecificModuleReq.class, EnvCtx.class);
        method.setAccessible(true);
        DealDetailSpecificModuleVO result = (DealDetailSpecificModuleVO) method.invoke(dealDetailFacade, request, envCtx);
        // assert
        assertNotNull(result);
        assertEquals(expectedResult, result);
    }

    /**
     * Test when query center throws exception
     */
    @Test
    public void testGetDealDetailSpecificModuleFromQueryCenter_QueryThrowsException() throws Throwable {
        // arrange
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenThrow(new RuntimeException("Query failed"));
        // act
        Method method = DealDetailFacade.class.getDeclaredMethod("getDealDetailSpecificModuleFromQueryCenter", SpecificModuleReq.class, EnvCtx.class);
        method.setAccessible(true);
        try {
            method.invoke(dealDetailFacade, request, envCtx);
            fail("Expected QueryCenterResultException");
        } catch (InvocationTargetException e) {
            assertTrue(e.getCause() instanceof QueryCenterResultException);
        }
    }

    /**
     * Test when query center returns null response
     */
    @Test
    public void testGetDealDetailSpecificModuleFromQueryCenter_NullResponse() throws Throwable {
        // arrange
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(null);
        // act
        Method method = DealDetailFacade.class.getDeclaredMethod("getDealDetailSpecificModuleFromQueryCenter", SpecificModuleReq.class, EnvCtx.class);
        method.setAccessible(true);
        try {
            method.invoke(dealDetailFacade, request, envCtx);
            fail("Expected QueryCenterResultException");
        } catch (InvocationTargetException e) {
            assertTrue(e.getCause() instanceof QueryCenterResultException);
        }
    }

    /**
     * Test when query center returns non-zero code
     */
    @Test
    public void testGetDealDetailSpecificModuleFromQueryCenter_NonZeroCode() throws Throwable {
        // arrange
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(1);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(response);
        // act
        Method method = DealDetailFacade.class.getDeclaredMethod("getDealDetailSpecificModuleFromQueryCenter", SpecificModuleReq.class, EnvCtx.class);
        method.setAccessible(true);
        try {
            method.invoke(dealDetailFacade, request, envCtx);
            fail("Expected QueryCenterResultException");
        } catch (InvocationTargetException e) {
            assertTrue(e.getCause() instanceof QueryCenterResultException);
        }
    }

    /**
     * Test when query center returns empty result list
     */
    @Test
    public void testGetDealDetailSpecificModuleFromQueryCenter_EmptyResultList() throws Throwable {
        // arrange
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(0);
        QueryDealGroupListResult resultData = new QueryDealGroupListResult();
        resultData.setList(Collections.emptyList());
        response.setData(resultData);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(response);
        // act
        Method method = DealDetailFacade.class.getDeclaredMethod("getDealDetailSpecificModuleFromQueryCenter", SpecificModuleReq.class, EnvCtx.class);
        method.setAccessible(true);
        try {
            method.invoke(dealDetailFacade, request, envCtx);
            fail("Expected QueryCenterResultException");
        } catch (InvocationTargetException e) {
            assertTrue(e.getCause() instanceof QueryCenterResultException);
        }
    }

    /**
     * Test when query center returns null result in list
     */
    @Test
    public void testGetDealDetailSpecificModuleFromQueryCenter_NullResultInList() throws Throwable {
        // arrange
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(0);
        QueryDealGroupListResult resultData = new QueryDealGroupListResult();
        resultData.setList(Collections.singletonList(null));
        response.setData(resultData);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(response);
        // act
        Method method = DealDetailFacade.class.getDeclaredMethod("getDealDetailSpecificModuleFromQueryCenter", SpecificModuleReq.class, EnvCtx.class);
        method.setAccessible(true);
        try {
            method.invoke(dealDetailFacade, request, envCtx);
            fail("Expected QueryCenterResultException");
        } catch (InvocationTargetException e) {
            assertTrue(e.getCause() instanceof QueryCenterResultException);
        }
    }

    /**
     * Test when no handler found for category
     */
    @Test
    public void testGetDealDetailSpecificModuleFromQueryCenter_NoHandlerFound() throws Throwable {
        // arrange
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(0);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(100L);
        dealGroupDTO.setCategory(categoryDTO);
        QueryDealGroupListResult resultData = new QueryDealGroupListResult();
        resultData.setList(Collections.singletonList(dealGroupDTO));
        response.setData(resultData);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(response);
        when(dealDetailSpecificModuleHandlerContainer.getHandler("100")).thenReturn(null);
        // act
        Method method = DealDetailFacade.class.getDeclaredMethod("getDealDetailSpecificModuleFromQueryCenter", SpecificModuleReq.class, EnvCtx.class);
        method.setAccessible(true);
        DealDetailSpecificModuleVO result = (DealDetailSpecificModuleVO) method.invoke(dealDetailFacade, request, envCtx);
        // assert
        assertNull(result);
    }

    /**
     * Test when handler returns null result
     */
    @Test
    public void testGetDealDetailSpecificModuleFromQueryCenter_HandlerReturnsNull() throws Throwable {
        // arrange
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(0);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(100L);
        dealGroupDTO.setCategory(categoryDTO);
        QueryDealGroupListResult resultData = new QueryDealGroupListResult();
        resultData.setList(Collections.singletonList(dealGroupDTO));
        response.setData(resultData);
        DealDetailSpecificModuleHandler handler = mock(DealDetailSpecificModuleHandler.class);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(response);
        when(dealDetailSpecificModuleHandlerContainer.getHandler("100")).thenReturn(handler);
        doAnswer(invocation -> {
            SpecificModuleCtx ctx = invocation.getArgument(0);
            ctx.setResult(null);
            return null;
        }).when(handler).handle(any(SpecificModuleCtx.class));
        // act
        Method method = DealDetailFacade.class.getDeclaredMethod("getDealDetailSpecificModuleFromQueryCenter", SpecificModuleReq.class, EnvCtx.class);
        method.setAccessible(true);
        DealDetailSpecificModuleVO result = (DealDetailSpecificModuleVO) method.invoke(dealDetailFacade, request, envCtx);
        // assert
        assertNull(result);
    }

    /**
     * Test with null category in response
     */
    @Test
    public void testGetDealDetailSpecificModuleFromQueryCenter_NullCategory() throws Throwable {
        // arrange
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(0);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setCategory(null);
        QueryDealGroupListResult resultData = new QueryDealGroupListResult();
        resultData.setList(Collections.singletonList(dealGroupDTO));
        response.setData(resultData);
        DealDetailSpecificModuleHandler handler = mock(DealDetailSpecificModuleHandler.class);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(response);
        when(dealDetailSpecificModuleHandlerContainer.getHandler("0")).thenReturn(handler);
        // act
        Method method = DealDetailFacade.class.getDeclaredMethod("getDealDetailSpecificModuleFromQueryCenter", SpecificModuleReq.class, EnvCtx.class);
        method.setAccessible(true);
        method.invoke(dealDetailFacade, request, envCtx);
        // assert
        verify(dealDetailSpecificModuleHandlerContainer).getHandler("0");
    }
}
