package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ToHomeFeeDisplayJudgeType;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealAttrHelper_NeedReservationTest {

    private static final String APP_NAME = "test-app";

    private ToHomeFeeDisplayJudgeType invokePrivateIsLifeToDoorCategory(DealGroupDTO dealGroupDTO) throws Exception {
        Method method = DealAttrHelper.class.getDeclaredMethod("isLifeToDoorCategory", DealGroupDTO.class);
        method.setAccessible(true);
        return (ToHomeFeeDisplayJudgeType) method.invoke(null, dealGroupDTO);
    }

    private void setStaticField(Class<?> clazz, String fieldName, Object value) throws Exception {
        Field field = clazz.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(null, value);
    }

    private Lion mockConfigManager(List<Long> toHomeFixServiceTypes, List<Long> noValueServiceTypes) throws Exception {
        Lion lion = mock(Lion.class);
        when(lion.getList(eq(APP_NAME), eq("com.sankuai.dzu.tpbase.dztgdetailweb.life.fix.tohome.category"), eq(Long.class), anyList())).thenReturn(toHomeFixServiceTypes);
        when(lion.getList(eq(APP_NAME), eq("com.sankuai.dzu.tpbase.dztgdetailweb.life.tohome.hasnovalue.deal.category"), eq(Long.class), anyList())).thenReturn(noValueServiceTypes);
        return lion;
    }

    /**
     * 测试 needReservation 方法，当 attrs 为 null 时，应返回 false
     */
    @Test
    public void testNeedReservationWhenAttrsIsNull() throws Throwable {
        // arrange
        // act
        boolean result = DealAttrHelper.needReservation(null);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 needReservation 方法，当 attrs 为空列表时，应返回 false
     */
    @Test
    public void testNeedReservationWhenAttrsIsEmpty() throws Throwable {
        // arrange
        // act
        boolean result = DealAttrHelper.needReservation(Collections.emptyList());
        // assert
        assertFalse(result);
    }

    /**
     * 测试 needReservation 方法，当 attrs 不包含 RESERVATION、RESERVATION_2 或 RESERVATION_3 这三个属性中的任何一个时，应返回 false
     */
    @Test
    public void testNeedReservationWhenAttrsNotContainsReservation() throws Throwable {
        // arrange
        AttributeDTO attr = new AttributeDTO();
        attr.setName("other");
        attr.setValue(Arrays.asList("value"));
        // act
        boolean result = DealAttrHelper.needReservation(Arrays.asList(attr));
        // assert
        assertFalse(result);
    }

    /**
     * 测试 needReservation 方法，当 attrs 包含上述属性，但其值不为 RESERVATION_VALUE_YES 时，应返回 false
     */
    @Test
    public void testNeedReservationWhenAttrValueNotYes() throws Throwable {
        // arrange
        AttributeDTO attr = new AttributeDTO();
        attr.setName(DealAttrKeys.RESERVATION);
        attr.setValue(Arrays.asList("no"));
        // act
        boolean result = DealAttrHelper.needReservation(Arrays.asList(attr));
        // assert
        assertFalse(result);
    }

    /**
     * 测试 needReservation 方法，当 attrs 包含 RESERVATION、RESERVATION_2 或 RESERVATION_3 这三个属性中的任何一个，并且其值为 RESERVATION_VALUE_YES 时，应返回 true
     */
    @Test
    public void testNeedReservationWhenAttrValueIsYes() throws Throwable {
        // arrange
        AttributeDTO attr = new AttributeDTO();
        attr.setName(DealAttrKeys.RESERVATION);
        attr.setValue(Arrays.asList(DealAttrKeys.RESERVATION_VALUE_YES));
        // act
        boolean result = DealAttrHelper.needReservation(Arrays.asList(attr));
        // assert
        assertTrue(result);
    }

    /**
     * Test when dealGroupDTO is null
     */
    @Test
    public void testIsLifeToDoorCategory_NullDealGroupDTO() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = null;
        // act
        ToHomeFeeDisplayJudgeType result = invokePrivateIsLifeToDoorCategory(dealGroupDTO);
        // assert
        assertNull(result);
    }

    /**
     * Test when category is null
     */
    @Test
    public void testIsLifeToDoorCategory_NullCategory() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setCategory(null);
        // act
        ToHomeFeeDisplayJudgeType result = invokePrivateIsLifeToDoorCategory(dealGroupDTO);
        // assert
        assertNull(result);
    }

    /**
     * Test when serviceTypeId is null
     */
    @Test
    public void testIsLifeToDoorCategory_NullServiceTypeId() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO category = new DealGroupCategoryDTO();
        category.setServiceTypeId(null);
        dealGroupDTO.setCategory(category);
        // act
        ToHomeFeeDisplayJudgeType result = invokePrivateIsLifeToDoorCategory(dealGroupDTO);
        // assert
        assertNull(result);
    }
}
