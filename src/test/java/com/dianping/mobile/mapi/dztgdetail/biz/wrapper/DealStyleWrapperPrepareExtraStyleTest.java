package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.style.protocol.ExtraStyleRequest;
import com.dianping.deal.style.protocol.ExtraStyleResponse;
import com.dianping.deal.style.spi.ExtraStyleService;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.MTTemplateKey;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

public class DealStyleWrapperPrepareExtraStyleTest {

    @InjectMocks
    private DealStyleWrapper dealStyleWrapper;

    @Mock
    private ExtraStyleService extraStyleServiceFuture;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private EnvCtx envCtx;

    @Mock
    private MTTemplateKey mtTemplateKey;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.getVersion()).thenReturn("1.0.0");
        when(dealCtx.getMtTemplateKey()).thenReturn(mtTemplateKey);
        when(mtTemplateKey.getKey()).thenReturn("templateKey");
    }

    /**
     * Test case for service throwing exception scenario.
     */
    @Test
    public void testPrepareExtraStyleServiceThrowsException() throws Throwable {
        // arrange
        when(dealCtx.isMt()).thenReturn(true);
        when(extraStyleServiceFuture.getExtraStyle(any(ExtraStyleRequest.class))).thenThrow(new RuntimeException("Service Exception"));
        // act
        Future<ExtraStyleResponse> result = dealStyleWrapper.prepareExtraStyle(dealCtx);
        // assert
        assertNull(result);
        verify(extraStyleServiceFuture, times(1)).getExtraStyle(any(ExtraStyleRequest.class));
    }
}
