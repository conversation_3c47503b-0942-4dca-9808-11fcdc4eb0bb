package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.utils;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.EducationDealAttrUtils;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;


/**
 * 类的描述
 *
 * @auther: liweilong06
 * @date: 2024/1/16 11:27 下午
 */
@RunWith(MockitoJUnitRunner.class)
public class EducationDealAttrUtilsUnitTest {

    public static final String OTHER_ATTR = "other_attr";
    public static final String TECH_IDS_JSON = "[1,2]";
    public static final String NON_CLASS_TYPE = "non_class_type";
    public static final String EDU_CLASS_TYPE = "edu_class_type";
    public static final String EDU_CLASS_TYPE1 = "edu_class_type";
    public static final String CLASS_TYPE_WITH_FIVE_SELECT = "class_type_with_five_select";

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    //@Test
    @Ignore // 这个是用来生成配置的
    public void test_createAttrConfig() {
        List<String> origConfigs = Lists.newArrayList("times_available_all","product_finish_date",
                "support_shop_service","female_only","oral_dentistry_tuanxiang_rule","product_channel_id_allowed",
                "pet-extra","eduSuitableAge","tort","product_can_use_coupon","preSaleTag","product_discount_rule_id",
                "sys_deal_universal_type","reservation_number","reservation_is_needed_or_not_2","reservation_is_needed_or_not_3",
                "voucher_limit_of_using_2","rawDealGroupPrice","calc_holiday_available","product_business_type",
                "voucher_limit_of_using_1","product_third_party_verify","tooth_suit_people","standardDealGroup",
                "product_block_stock","service_type","support_home_service","hide_type","isselecteddeal","reservation_is_needed_or_not",
                "category","token_time_use_limit_1","warmUpStartTime","usingStockPlan","limit_of_using_each_eye",
                "available_time","reservation_policy","standardDealGroupKey","tag_unifyProduct","whetherToProvideClothing",
                "dressNum","IsMakeupProvided","printPhotos","IsTheFilmPresentedFree","photoPlateAttachCount","photoCount",
                "ShootingScene","ShootingMethod","mall_dpshops","range_of_shop_mall");
        List<String> needAddAttr = Lists.newArrayList("edu_class_type", "class_type_with_five_select",
                "class_type_with_customize_select", "class_type_five", "class_type_with_three_select", "class_type",
                "for_the_crowd", "course_trial", "course_stage", "physical_gift", "course_plan",
                "additional_service", "course_assure", "refund_rule", "refund_learn_time");

        for (String attr : needAddAttr) {
            if (origConfigs.contains(attr)) {
                continue;
            }
            origConfigs.add(attr);
        }
        System.out.println(JsonCodec.encodeWithUTF8(origConfigs));
    }

    @Test
    public void testGetTeacherIdsDealGroupDTONull() {
        assertNull(EducationDealAttrUtils.getTeacherIds(null));
    }

    @Test
    public void testGetTeacherIdsDealGroupDTOAttrsEmpty() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        assertNull(EducationDealAttrUtils.getTeacherIds(dealGroupDTO));
    }

    @Test
    public void testGetTeacherIdsDealGroupDTOAttrCourseTrialNotFound() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(OTHER_ATTR);
        dealGroupDTO.setAttrs(Arrays.asList(attrDTO));
        assertNull(EducationDealAttrUtils.getTeacherIds(dealGroupDTO));
    }

    @Test
    public void testGetTeacherIdsDealGroupDTOAttrCourseTrialEmpty() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(EducationDealAttrUtils.TEACHER_LIST_ATTR);
        attrDTO.setValue(Lists.newArrayList());
        dealGroupDTO.setAttrs(Arrays.asList(attrDTO));
        assertNull(EducationDealAttrUtils.getTeacherIds(dealGroupDTO));
    }

    @Test
    public void testGetTeacherIdsDealGroupDTOAttrCourseTrialValidCourseTrialsEmpty() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(EducationDealAttrUtils.TEACHER_LIST_ATTR);
        attrDTO.setValue(Lists.newArrayList());
        dealGroupDTO.setAttrs(Arrays.asList(attrDTO));
        assertNull(EducationDealAttrUtils.getTeacherIds(dealGroupDTO));
    }

    @Test
    public void testGetTeacherIdsDealGroupDTOAttrCourseTrialValidCourseTrialsNotEmpty() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(EducationDealAttrUtils.TEACHER_LIST_ATTR);
        attrDTO.setValue(Lists.newArrayList(TECH_IDS_JSON));
        dealGroupDTO.setAttrs(Arrays.asList(attrDTO));
        List<Integer> expectedTeacherIds = Arrays.asList(1, 2);
        assertEquals(expectedTeacherIds, EducationDealAttrUtils.getTeacherIds(dealGroupDTO));
    }

    /**
     * 测试 dealGroupDTO 为 null 的情况
     */
    @Test
    public void testGetOrigClassTypeDealGroupDTONull() {
        String result = EducationDealAttrUtils.getOrigClassType(null);
        assertNull(result);
    }

    /**
     * 测试 dealGroupDTO 的属性列表为空的情况
     */
    @Test
    public void testGetOrigClassTypeEmptyAttrs() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setAttrs(Collections.emptyList());
        String result = EducationDealAttrUtils.getOrigClassType(dealGroupDTO);
        assertNull(result);
    }

    /**
     * 测试 dealGroupDTO 的属性列表中没有班型属性的情况
     */
    @Test
    public void testGetOrigClassTypeNoClassTypeAttr() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        AttrDTO attr = new AttrDTO();
        attr.setName(NON_CLASS_TYPE);
        attr.setValue(Collections.singletonList("value"));
        dealGroupDTO.setAttrs(Collections.singletonList(attr));
        String result = EducationDealAttrUtils.getOrigClassType(dealGroupDTO);
        assertNull(result);
    }

    /**
     * 测试 dealGroupDTO 的属性列表中有一个班型属性，且属性值非空的情况
     */
    @Test
    public void testGetOrigClassTypeSingleClassTypeAttr() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        AttrDTO attr = new AttrDTO();
        attr.setName(EDU_CLASS_TYPE);
        attr.setValue(Collections.singletonList("value"));
        dealGroupDTO.setAttrs(Collections.singletonList(attr));
        String result = EducationDealAttrUtils.getOrigClassType(dealGroupDTO);
        assertEquals("value", result);
    }

    /**
     * 测试 dealGroupDTO 的属性列表中有多个班型属性，第一个非空属性值应该被返回的情况
     */
    @Test
    public void testGetOrigClassTypeMultipleClassTypeAttrs() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        AttrDTO attr1 = new AttrDTO();
        attr1.setName(EDU_CLASS_TYPE1);
        attr1.setValue(Collections.singletonList("value1"));

        AttrDTO attr2 = new AttrDTO();
        attr2.setName(CLASS_TYPE_WITH_FIVE_SELECT);
        attr2.setValue(Collections.singletonList("value2"));

        dealGroupDTO.setAttrs(Arrays.asList(attr1, attr2));
        String result = EducationDealAttrUtils.getOrigClassType(dealGroupDTO);
        assertEquals("value1", result);
    }

    @Test
    public void testGetAttrValuesFromJsonValueDealGroupDTONull() {
        assertNull(EducationDealAttrUtils.getAttrValuesFromJsonValue(null, EducationDealAttrUtils.COURSE_PLAN));
    }

    @Test
    public void testGetAttrValuesFromJsonValueAttrsNull() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        assertNull(EducationDealAttrUtils.getAttrValuesFromJsonValue(dealGroupDTO, EducationDealAttrUtils.COURSE_PLAN));
    }

    @Test
    public void testGetAttrValuesFromJsonValueAttrNameNotExist() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(EducationDealAttrUtils.COURSE_PLAN);
        attrDTO.setValue(Arrays.asList("[\"plan1\",\"plan2\"]"));
        dealGroupDTO.setAttrs(Collections.singletonList(attrDTO));
        assertNull(EducationDealAttrUtils.getAttrValuesFromJsonValue(dealGroupDTO, "not_exist_attr"));
    }

    @Test
    public void testGetAttrValuesFromJsonValueAttrValueNull() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(EducationDealAttrUtils.COURSE_PLAN);
        dealGroupDTO.setAttrs(Collections.singletonList(attrDTO));
        assertNull(EducationDealAttrUtils.getAttrValuesFromJsonValue(dealGroupDTO, EducationDealAttrUtils.COURSE_PLAN));
    }

    @Test
    public void testGetAttrValuesFromJsonValueAttrValueInvalidJson() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(EducationDealAttrUtils.COURSE_PLAN);
        attrDTO.setValue(Arrays.asList("invalid_json"));
        dealGroupDTO.setAttrs(Collections.singletonList(attrDTO));
        assertNull(EducationDealAttrUtils.getAttrValuesFromJsonValue(dealGroupDTO, EducationDealAttrUtils.COURSE_PLAN));
    }

    @Test
    public void testGetAttrValuesFromJsonValueAttrValueValidJson() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(EducationDealAttrUtils.COURSE_PLAN);
        attrDTO.setValue(Arrays.asList("[\"plan1\",\"plan2\"]"));
        dealGroupDTO.setAttrs(Collections.singletonList(attrDTO));
        assertEquals(Arrays.asList("plan1", "plan2"), EducationDealAttrUtils.getAttrValuesFromJsonValue(dealGroupDTO, EducationDealAttrUtils.COURSE_PLAN));
    }

    /**
     * 正常情况：dealGroupDTO 包含名称为 ATTR_GIFT 的属性，且属性值可以被解码为非空的 List<EduCourseGiftProduct> 对象
     */
    @Test
    public void testGetGiftProductNormalCase() {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr = new AttrDTO();
        attr.setName(EducationDealAttrUtils.ATTR_GIFT);
        attr.setValue(Lists.newArrayList("[{\"gift_name\":\"礼品1\"},{\"gift_name\":\"礼品2\"}]"));
        attrs.add(attr);
        dealGroupDTO.setAttrs(attrs);

        // act
        List<String> result = EducationDealAttrUtils.getGiftProduct(dealGroupDTO);

        // assert
        assertEquals(Arrays.asList("礼品1", "礼品2"), result);
    }

    /**
     * 异常情况1：dealGroupDTO 为 null
     */
    @Test
    public void testGetGiftProductDealGroupDTONull() {
        // arrange

        // act
        List<String> result = EducationDealAttrUtils.getGiftProduct(null);

        // assert
        assertNull(result);
    }

    /**
     * 异常情况2：dealGroupDTO 不包含名称为 ATTR_GIFT 的属性
     */
    @Test
    public void testGetGiftProductNoAttrGift() {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setAttrs(Lists.newArrayList());

        // act
        List<String> result = EducationDealAttrUtils.getGiftProduct(dealGroupDTO);

        // assert
        assertNull(result);
    }

    /**
     * 异常情况3：dealGroupDTO 包含名称为 ATTR_GIFT 的属性，但属性值为空或无法被解码为 List<EduCourseGiftProduct> 对象
     */
    @Test
    public void testGetGiftProductInvalidAttrValue() {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr = new AttrDTO();
        attr.setName(EducationDealAttrUtils.ATTR_GIFT);
        attr.setValue(Lists.newArrayList("invalid_json"));
        attrs.add(attr);
        dealGroupDTO.setAttrs(attrs);

        // act
        List<String> result = EducationDealAttrUtils.getGiftProduct(dealGroupDTO);

        // assert
        assertNull(result);
    }

    /**
     * 边界情况：dealGroupDTO 包含名称为 ATTR_GIFT 的属性，且属性值可以被解码为一个空的 List<EduCourseGiftProduct> 对象
     */
    @Test
    public void testGetGiftProductEmptyGiftProductList() {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr = new AttrDTO();
        attr.setName(EducationDealAttrUtils.ATTR_GIFT);
        attr.setValue(Lists.newArrayList("[]"));
        attrs.add(attr);
        dealGroupDTO.setAttrs(attrs);

        // act
        List<String> result = EducationDealAttrUtils.getGiftProduct(dealGroupDTO);

        // assert
        assertNull(result);
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testGetClassNumNormal() {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr = new AttrDTO();
        attr.setName(EducationDealAttrUtils.COURSE_PLAN);
        attr.setValue(Lists.newArrayList("[{\"course_time_num\": 10}, {\"course_time_num\": 20}]"));
        attrs.add(attr);
        dealGroupDTO.setAttrs(attrs);

        // act
        String result = EducationDealAttrUtils.getClassNum(dealGroupDTO);

        // assert
        String expectedClassNum = "30";
        assertEquals(expectedClassNum, result);
    }

    /**
     * 测试异常情况1：dealGroupDTO 为 null
     */
    @Test
    public void testGetClassNumDealGroupDTONull() {
        // arrange
        DealGroupDTO dealGroupDTO = null;

        // act
        String result = EducationDealAttrUtils.getClassNum(dealGroupDTO);

        // assert
        assertNull(result);
    }

    /**
     * 测试异常情况2：dealGroupDTO 中不包含 COURSE_PLAN 属性
     */
    @Test
    public void testGetClassNumNoCoursePlanAttr() {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr = new AttrDTO();
        attr.setName("other_attr");
        attr.setValue(Lists.newArrayList("[{\"course_time_num\": 10}, {\"course_time_num\": 20}]"));
        attrs.add(attr);
        dealGroupDTO.setAttrs(attrs);

        // act
        String result = EducationDealAttrUtils.getClassNum(dealGroupDTO);

        // assert
        assertNull(result);
    }

    /**
     * 测试异常情况3：dealGroupDTO 中包含 COURSE_PLAN 属性，但属性值为空或无效的 JSON 字符串
     */
    @Test
    public void testGetClassNumInvalidCoursePlanAttrValue() {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr = new AttrDTO();
        attr.setName(EducationDealAttrUtils.COURSE_PLAN);
        attr.setValue(Lists.newArrayList("invalid_json"));
        attrs.add(attr);
        dealGroupDTO.setAttrs(attrs);

        // act
        String result = EducationDealAttrUtils.getClassNum(dealGroupDTO);

        // assert
        assertNull(result);
    }


    /**
     * 正常情况
     */
    @Test
    public void testGetExamMaterialNumNormal() {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        AttrDTO attrDTO = mock(AttrDTO.class);
        when(attrDTO.getName()).thenReturn(EducationDealAttrUtils.MATERIAL_LIST);
        when(attrDTO.getValue()).thenReturn(Lists.newArrayList("[{\"materialName\": 2}, {\"materialName\": 3}]"));
        when(dealGroupDTO.getAttrs()).thenReturn(Collections.singletonList(attrDTO));

        // act
        int materialNum = EducationDealAttrUtils.getExamMaterialNum(dealGroupDTO);

        // assert
        assertEquals(2, materialNum);
    }


    /**
     * dealGroupDTO 不包含 MATERIAL_LIST 属性
     */
    @Test
    public void testGetExamMaterialNumNoMaterialListAttr() {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getAttrs()).thenReturn(Collections.emptyList());

        // act
        int materialNum = EducationDealAttrUtils.getExamMaterialNum(dealGroupDTO);

        // assert
        assertEquals(0, materialNum);
    }

    /**
     * MATERIAL_LIST 属性值为空或无效的 JSON 字符串
     */
    @Test
    public void testGetExamMaterialNumInvalidJson() {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        AttrDTO attrDTO = mock(AttrDTO.class);
        when(attrDTO.getName()).thenReturn(EducationDealAttrUtils.MATERIAL_LIST);
        when(attrDTO.getValue()).thenReturn(Lists.newArrayList(""));
        when(dealGroupDTO.getAttrs()).thenReturn(Collections.singletonList(attrDTO));

        // act
        int materialNum = EducationDealAttrUtils.getExamMaterialNum(dealGroupDTO);

        // assert
        assertEquals(0, materialNum);
    }

    /**
     * ExamMaterial 列表为空
     */
    @Test
    public void testGetExamMaterialNumEmptyList() {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        AttrDTO attrDTO = mock(AttrDTO.class);
        when(attrDTO.getName()).thenReturn(EducationDealAttrUtils.MATERIAL_LIST);
        when(attrDTO.getValue()).thenReturn(Lists.newArrayList("[]"));
        when(dealGroupDTO.getAttrs()).thenReturn(Collections.singletonList(attrDTO));

        // act
        int materialNum = EducationDealAttrUtils.getExamMaterialNum(dealGroupDTO);

        // assert
        assertEquals(0, materialNum);
    }

    /**
     * ExamMaterial 列表中的某个元素为 null 或其 copyNum 为 null
     */
    @Test
    public void testGetExamMaterialNumNullElement() {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        AttrDTO attrDTO = mock(AttrDTO.class);
        when(attrDTO.getName()).thenReturn(EducationDealAttrUtils.MATERIAL_LIST);
        when(attrDTO.getValue()).thenReturn(Lists.newArrayList("[{\"materialName\": 2}, null, {\"materialName\": null}]"));
        when(dealGroupDTO.getAttrs()).thenReturn(Collections.singletonList(attrDTO));

        // act
        int materialNum = EducationDealAttrUtils.getExamMaterialNum(dealGroupDTO);

        // assert
        assertEquals(1, materialNum);
    }

    @Test
    public void testGetStudyRoomFree() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr1 = new AttrDTO();
        attr1.setName(EducationDealAttrUtils.STUDY_ROOM);
        attr1.setValue(Lists.newArrayList("有"));
        attrs.add(attr1);

        AttrDTO attr2 = new AttrDTO();
        attr2.setName(EducationDealAttrUtils.FREE_STUDY_ROOM);
        attr2.setValue(Lists.newArrayList("是"));
        attrs.add(attr2);
        dealGroupDTO.setAttrs(attrs);

        // act
        String result = EducationDealAttrUtils.getStudyRoom(dealGroupDTO);

        // assert
        String expectedClassNum = "免费使用";
        assertEquals(expectedClassNum, result);
    }


    @Test
    public void testGetStudyRoomPay() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr1 = new AttrDTO();
        attr1.setName(EducationDealAttrUtils.STUDY_ROOM);
        attr1.setValue(Lists.newArrayList("有"));
        attrs.add(attr1);

        AttrDTO attr2 = new AttrDTO();
        attr2.setName(EducationDealAttrUtils.FREE_STUDY_ROOM);
        attr2.setValue(Lists.newArrayList("否"));
        attrs.add(attr2);
        dealGroupDTO.setAttrs(attrs);

        // act
        String result = EducationDealAttrUtils.getStudyRoom(dealGroupDTO);

        // assert
        String expectedClassNum = "付费使用";
        assertEquals(expectedClassNum, result);
    }
}
