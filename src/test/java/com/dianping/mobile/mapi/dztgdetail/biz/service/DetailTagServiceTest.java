package com.dianping.mobile.mapi.dztgdetail.biz.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedModuleExtraReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedShopReviewReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleExtraDTO;
import com.dianping.mobile.mapi.dztgdetail.facade.UnifiedModuleExtraFacade;
import java.util.Arrays;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DetailTagServiceTest {

    @InjectMocks
    private DetailTagService detailTagService;

    @Mock
    private UnifiedModuleExtraFacade unifiedModuleExtraFacade;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testReviewIsTopTabWhenQueryUnifiedModuleExtraDTOThrowsException() throws Exception {
        // arrange
        UnifiedModuleExtraReq request = new UnifiedModuleExtraReq();
        EnvCtx envCtx = new EnvCtx();
        IMobileContext iMobileContext = mock(IMobileContext.class);
        when(unifiedModuleExtraFacade.queryUnifiedModuleExtraDTO(request, envCtx, iMobileContext)).thenThrow(new RuntimeException());
        // act
        Boolean result = detailTagService.reviewIsTopTab(request, envCtx, iMobileContext);
        // assert
        assertFalse(result);
    }

    @Test
    public void testReviewIsTopTabWhenQueryUnifiedModuleExtraDTOReturnsNull() throws Exception {
        // arrange
        UnifiedModuleExtraReq request = new UnifiedModuleExtraReq();
        EnvCtx envCtx = new EnvCtx();
        IMobileContext iMobileContext = mock(IMobileContext.class);
        when(unifiedModuleExtraFacade.queryUnifiedModuleExtraDTO(request, envCtx, iMobileContext)).thenReturn(null);
        // act
        Boolean result = detailTagService.reviewIsTopTab(request, envCtx, iMobileContext);
        // assert
        assertFalse(result);
    }

    @Test
    public void testReviewIsTopTabWhenModuleConfigDosIsEmpty() throws Exception {
        // arrange
        UnifiedModuleExtraReq request = new UnifiedModuleExtraReq();
        EnvCtx envCtx = new EnvCtx();
        IMobileContext iMobileContext = mock(IMobileContext.class);
        ModuleExtraDTO moduleExtraDTO = new ModuleExtraDTO();
        when(unifiedModuleExtraFacade.queryUnifiedModuleExtraDTO(request, envCtx, iMobileContext)).thenReturn(moduleExtraDTO);
        // act
        Boolean result = detailTagService.reviewIsTopTab(request, envCtx, iMobileContext);
        // assert
        assertFalse(result);
    }

    @Test
    public void testReviewIsTopTabWhenFirstModuleConfigDoKeyIsNotReview() throws Exception {
        // arrange
        UnifiedModuleExtraReq request = new UnifiedModuleExtraReq();
        EnvCtx envCtx = new EnvCtx();
        IMobileContext iMobileContext = mock(IMobileContext.class);
        ModuleExtraDTO moduleExtraDTO = new ModuleExtraDTO();
        ModuleConfigDo moduleConfigDo = new ModuleConfigDo();
        moduleConfigDo.setKey("other");
        moduleExtraDTO.setModuleConfigDos(Arrays.asList(moduleConfigDo));
        when(unifiedModuleExtraFacade.queryUnifiedModuleExtraDTO(request, envCtx, iMobileContext)).thenReturn(moduleExtraDTO);
        // act
        Boolean result = detailTagService.reviewIsTopTab(request, envCtx, iMobileContext);
        // assert
        assertFalse(result);
    }

    @Test
    public void testReviewIsTopTabWhenFirstModuleConfigDoKeyIsReview() throws Exception {
        // arrange
        UnifiedModuleExtraReq request = new UnifiedModuleExtraReq();
        EnvCtx envCtx = new EnvCtx();
        IMobileContext iMobileContext = mock(IMobileContext.class);
        ModuleExtraDTO moduleExtraDTO = new ModuleExtraDTO();
        ModuleConfigDo moduleConfigDo = new ModuleConfigDo();
        moduleConfigDo.setKey("网友评价");
        moduleExtraDTO.setModuleConfigDos(Arrays.asList(moduleConfigDo));
        when(unifiedModuleExtraFacade.queryUnifiedModuleExtraDTO(request, envCtx, iMobileContext)).thenReturn(moduleExtraDTO);
        // act
        Boolean result = detailTagService.reviewIsTopTab(request, envCtx, iMobileContext);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 convertToUnifiedModuleExtraReq 方法，当 req 不为 null 时
     */
    @Test
    public void testConvertToUnifiedModuleExtraReq_NotNull() throws Throwable {
        // arrange
        UnifiedShopReviewReq req = new UnifiedShopReviewReq();
        req.setDealGroupId(1);
        req.setMrnVersion("1.0.0");
        // act
        UnifiedModuleExtraReq result = detailTagService.convertToUnifiedModuleExtraReq(req);
        // assert
        assertNotNull(result);
        assertEquals(req.getDealGroupId(), result.getDealGroupId());
        assertEquals("", result.getExpResults());
        assertEquals(-1, result.getCityId().intValue());
        assertEquals(req.getMrnVersion(), result.getMrnVersion());
    }

    /**
     * 测试 convertToUnifiedModuleExtraReq 方法，当 req 为 null 时
     */
    @Test(expected = NullPointerException.class)
    public void testConvertToUnifiedModuleExtraReq_Null() throws Throwable {
        // arrange
        // act
        // Explicit cast to resolve ambiguity
        detailTagService.convertToUnifiedModuleExtraReq((UnifiedShopReviewReq) null);
        // assert
        // expect NullPointerException
    }
}
