package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.mobile.mapi.dztgdetail.GenericTest;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.BeautyHighlightsV2Processor;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CommonAttrVO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import javax.annotation.Resource;
import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

/**
 * <AUTHOR>
 * @date 2024/1/14
 */
@RunWith(MockitoJUnitRunner.class)
public class BeautyHighlightsV2ProcessorTest {

    @InjectMocks
    private BeautyHighlightsV2Processor beautyHighlightsV2Processor;


    /**
     * 测试 queryCenterWrapper.preDealGroupDTO(request) 抛出异常的情况
     */
    @Test
    public void testEyeLashMAkeUp() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setMtId(1);
        String dealGroupJson = "{\"attrs\":[{\"name\":\"product_finish_date\",\"source\":0,\"value\":[\"1970-01-01 08:00:00\"]},{\"name\":\"years_of_employment_of_makeup_artist\",\"source\":0,\"value\":[\"2年及以下\"]},{\"name\":\"calc_holiday_available\",\"source\":0,\"value\":[\"1\"]},{\"name\":\"product_business_type\",\"source\":0,\"value\":[\"1\"]},{\"name\":\"free_for_double_eyelid_tapes\",\"source\":0,\"value\":[\"含双眼皮贴\"]},{\"name\":\"product_channel_id_allowed\",\"source\":0,\"value\":[\"0\"]},{\"name\":\"other_free_services\",\"source\":0,\"value\":[\"一次性化妆包\",\"茶饮\"]},{\"name\":\"product_can_use_coupon\",\"source\":0,\"value\":[\"true\"]},{\"name\":\"hairstyle_service\",\"source\":0,\"value\":[\"含造型\"]},{\"name\":\"makeup_service_time\",\"source\":0,\"value\":[\"60分钟\"]},{\"name\":\"preSaleTag\",\"source\":0,\"value\":[\"false\"]},{\"name\":\"product_third_party_verify\",\"source\":0,\"value\":[\"false\"]},{\"name\":\"product_block_stock\",\"source\":0,\"value\":[\"false\"]},{\"name\":\"product_discount_rule_id\",\"source\":0,\"value\":[\"0\"]},{\"name\":\"service_type\",\"source\":0,\"value\":[\"化妆\"]},{\"name\":\"reservation_is_needed_or_not\",\"source\":0,\"value\":[\"否\"]},{\"name\":\"sys_deal_universal_type\",\"source\":0,\"value\":[\"1\"]},{\"name\":\"free_for_eyebrow_trimming\",\"source\":0,\"value\":[\"含修眉\"]},{\"name\":\"free_for_false_eyelash\",\"source\":0,\"value\":[\"含单簇假睫毛\"]},{\"name\":\"category\",\"source\":0,\"value\":[\"50\",\"5002\"]},{\"name\":\"makeup_service_type\",\"source\":0,\"value\":[\"到店\"]}],\"basic\":{\"beginSaleDate\":\"2024-02-29 14:35:38\",\"brandName\":\"美甲·指甲\",\"categoryId\":502,\"endSaleDate\":\"2025-02-28 14:25:15\",\"saleChannel\":0,\"salePlatform\":3,\"sourceId\":102,\"status\":1,\"title\":\"化妆测试1\",\"titleDesc\":\"仅售99元，价值597元化妆测试1！\",\"tradeType\":3},\"category\":{\"categoryId\":502,\"serviceType\":\"化妆\",\"serviceTypeId\":694},\"channel\":{\"channelCn\":\"丽人\",\"channelEn\":\"beauty\",\"channelGroupCn\":\"到店综合\",\"channelGroupEn\":\"general\",\"channelGroupId\":2,\"channelId\":5},\"customer\":{\"originCustomerId\":281032},\"deals\":[{\"attrs\":[{\"name\":\"sku_receipt_type\",\"source\":0,\"value\":[\"1\"]}],\"basic\":{\"originTitle\":\"化妆测试1\",\"status\":1,\"title\":\"化妆测试1\"},\"dealId\":1275030711,\"dealIdInt\":1275030711,\"price\":{\"marketPrice\":\"597.00\",\"salePrice\":\"99.00\",\"version\":7608828583},\"stock\":{\"dpRemain\":100000000,\"dpSales\":0,\"dpTotal\":100000000,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"isSharedSoldOut\":false,\"mtRemain\":100000000,\"mtSales\":0,\"mtTotal\":100000000,\"sharedRemain\":0,\"sharedSales\":0,\"sharedTotal\":0,\"status\":1}}],\"detail\":{\"dealGroupPics\":\"https://p1.meituan.net/dpmerchantpic/08156b6547bcb5add8de83bd31406234172880.png\",\"images\":[\"https://p1.meituan.net/dpmerchantpic/08156b6547bcb5add8de83bd31406234172880.png%40450w_280h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\"],\"importantPoint\":\"\",\"templateDetailDTOs\":[{\"blockId\":0,\"content\":\"<p class=\\\"listitem\\\"></p>\\n<p class=\\\"explain\\\"></p>\\n<div class=\\\"img\\\"><img src=\\\"https://p1.meituan.net/dpdeal/2772a55f06c197ab62318199d95a997179341.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /></div>\\n\\n\",\"title\":\"产品介绍\",\"type\":5},{\"blockId\":0,\"content\":\"        <div class=\\\"detail-tit\\\"></div>\\n        <div>\\n            <table width=\\\"100%\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" class=\\\"detail-table\\\">\\n                <thead>\\n                <tr>\\n                    <th width=\\\"50%\\\">名称</th>\\n                    <th width=\\\"25%\\\">数量</th>\\n                    <th width=\\\"25%\\\">单价</th>\\n                </tr>\\n                </thead>\\n                <tbody>\\n                            <tr>\\n                                <td>妆前护肤</td>\\n                                    <td class=\\\"tc\\\">1</td>\\n                                    <td class=\\\"tc\\\">199元</td>\\n                            </tr>\\n                            <tr>\\n                                <td>发型/造型</td>\\n                                    <td class=\\\"tc\\\">1</td>\\n                                    <td class=\\\"tc\\\">199元</td>\\n                            </tr>\\n                            <tr>\\n                                <td>化妆服务</td>\\n                                    <td class=\\\"tc\\\">1</td>\\n                                    <td class=\\\"tc\\\">199元</td>\\n                            </tr>\\n                    <tr class=\\\"total\\\">\\n                        <td></td>\\n                        <td class=\\\"tc\\\">总价<br><strong>团购价</strong></td>\\n                        <td class=\\\"tc\\\">597元<br><strong>99元</strong>\\n                        </td>\\n                    </tr>\\n                </tbody>\\n            </table>\\n        </div>\\n\\n\",\"title\":\"团购详情\",\"type\":1},{\"blockId\":0,\"content\":\"<div class=\\\"detail-box\\\">\\n    <div class=\\\"purchase-notes\\\">\\n\\t\\t        <dl>\\n            <dt>有效期</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">\\n    购买后90天内有效\\n        </p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>预约信息</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">无需预约，如遇消费高峰时段您可能需要排队</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>适用人数</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">每张团购券不限使用人数</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>规则提醒</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">不再与其他优惠同享\\n</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>温馨提示</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">如需团购券发票，请您在消费时向商户咨询</p>\\n                <p class=\\\"listitem\\\">为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！</p>\\n            </dd>\\n        </dl>\\n    </div>\\n</div>\\n\",\"title\":\"购买须知\",\"type\":2}]},\"displayShopInfo\":{\"dpDisplayShopIds\":[8909415,599047504,1279780282],\"mtDisplayShopIds\":[2422743,599047504,1279780282]},\"dpDealGroupId\":1032790805,\"dpDealGroupIdInt\":1032790805,\"image\":{\"allPicPaths\":\"https://p1.meituan.net/dpmerchantpic/08156b6547bcb5add8de83bd31406234172880.png\",\"defaultPicPath\":\"https://p1.meituan.net/dpmerchantpic/08156b6547bcb5add8de83bd31406234172880.png\"},\"mtDealGroupId\":1032790805,\"mtDealGroupIdInt\":1032790805,\"price\":{\"marketPrice\":\"597.00\",\"salePrice\":\"99.00\",\"version\":7608828583},\"rule\":{\"buyRule\":{\"maxPerUser\":0,\"minPerUser\":1},\"refundRule\":{\"supportOverdueAutoRefund\":true,\"supportRefundType\":1},\"useRule\":{\"receiptEffectiveDate\":{\"receiptBeginDate\":\"2024-03-04 17:09:46\",\"receiptDateType\":1,\"receiptEndDate\":\"2024-06-02 23:59:59\",\"receiptValidDays\":90,\"showText\":\"购买后90天内有效\"}}},\"saleChannelAggregation\":{\"allSupport\":true,\"notSupportChannels\":[],\"supportChannels\":[]},\"serviceProject\":{\"marketPrice\":\"597.00\",\"mustGroups\":[{\"groups\":[{\"amount\":1,\"attrs\":[{\"attrName\":\"serviceContent\",\"attrValue\":\"卸妆、洁面、爽肤水、精华、乳液、面霜、防晒、隔离\",\"chnName\":\"服务内容\",\"metaAttrId\":2443,\"rawAttrValue\":\"卸妆、洁面、爽肤水、精华、乳液、面霜、防晒、隔离\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"SkincareProductType\",\"attrValue\":\"一线品牌护肤品、海外潮牌护肤品、国产护肤品\",\"chnName\":\"护肤品类型\",\"metaAttrId\":571177,\"rawAttrValue\":\"一线品牌护肤品、海外潮牌护肤品、国产护肤品\",\"sequence\":0,\"valueType\":500}],\"categoryId\":2105804,\"marketPrice\":\"199.0\",\"name\":\"妆前护肤\",\"skuId\":0,\"status\":10},{\"amount\":1,\"attrs\":[{\"attrName\":\"serviceContent\",\"attrValue\":\"简单梳理\",\"chnName\":\"服务内容\",\"metaAttrId\":2443,\"rawAttrValue\":\"简单梳理\",\"sequence\":0,\"valueType\":500}],\"categoryId\":2105803,\"marketPrice\":\"199.0\",\"name\":\"发型/造型\",\"skuId\":0,\"status\":10},{\"amount\":1,\"attrs\":[{\"attrName\":\"category2\",\"attrValue\":\"主题定制妆\",\"chnName\":\"二级分类\",\"metaAttrId\":1038,\"rawAttrValue\":\"主题定制妆\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"applyscene\",\"attrValue\":\"约会妆、面试妆、领证妆、证件照妆、职场妆、艺考妆、通勤妆、毕业妆、商务妆、相亲妆、派对妆、蹦迪妆、戏曲京剧妆、Cosplay妆、汉服妆、节日妆、万圣节妆、圣诞妆、新年妆、迪士尼妆、上镜妆、写真妆、舞台妆、主持人妆、晚宴妆、舞会妆、拉丁舞妆、新娘妆、订婚妆、伴娘妆、新郎妆、妈妈妆、男士妆\",\"chnName\":\"适用场景\",\"metaAttrId\":852,\"rawAttrValue\":\"约会妆、面试妆、领证妆、证件照妆、职场妆、艺考妆、通勤妆、毕业妆、商务妆、相亲妆、派对妆、蹦迪妆、戏曲京剧妆、Cosplay妆、汉服妆、节日妆、万圣节妆、圣诞妆、新年妆、迪士尼妆、上镜妆、写真妆、舞台妆、主持人妆、晚宴妆、舞会妆、拉丁舞妆、新娘妆、订婚妆、伴娘妆、新郎妆、妈妈妆、男士妆\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"cosmeticType\",\"attrValue\":\"一线品牌化妆品、海外潮牌化妆品、国产化妆品\",\"chnName\":\"化妆品类型\",\"metaAttrId\":3051,\"rawAttrValue\":\"一线品牌化妆品、海外潮牌化妆品、国产化妆品\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"CosmeticsBrand\",\"attrValue\":\"阿玛尼、Dior、香奈儿、圣罗兰、Bobbi Brown、兰蔻、CPB、雅诗兰黛、植村秀、MAC、MAKE UP FOR EVER、TomFord、NARS、纪梵希、Urban Decay、毛戈平、花西子、3CE\",\"chnName\":\"化妆品品牌\",\"metaAttrId\":571147,\"rawAttrValue\":\"阿玛尼、Dior、香奈儿、圣罗兰、Bobbi Brown、兰蔻、CPB、雅诗兰黛、植村秀、MAC、MAKE UP FOR EVER、TomFord、NARS、纪梵希、Urban Decay、毛戈平、花西子、3CE\",\"sequence\":0,\"valueType\":500}],\"categoryId\":2105805,\"marketPrice\":\"199.0\",\"name\":\"化妆服务\",\"skuId\":0,\"status\":10}]}],\"optionGroups\":[],\"salePrice\":\"99.00\",\"structType\":\"uniform-structure-table\",\"title\":\"团购详情\"},\"stock\":{\"dpRemain\":100000000,\"dpSales\":0,\"dpTotal\":100000000,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"mtRemain\":100000000,\"mtSales\":0,\"mtTotal\":100000000,\"status\":1},\"tags\":[{\"id\":100005311,\"tagName\":\"化妆\"}]}";
        DealGroupDTO dealGroupDTO = JSON.parseObject(dealGroupJson, DealGroupDTO.class);
        ctx.setDealGroupDTO(dealGroupDTO);
        // act
        List<CommonAttrVO> result = beautyHighlightsV2Processor.getMakeupAttrs(ctx);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testTattooShopping() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setMtId(1);
        String dealGroupJson ="{\"dpDealGroupId\":1094756332,\"mtDealGroupId\":1094756332,\"basic\":{\"categoryId\":512,\"title\":\"纹绣结构化测试\",\"brandName\":\"丽人冷启动-美睫纹绣纹身丽人其他\",\"titleDesc\":\"仅售900元，价值988元纹绣结构化测试！\",\"beginSaleDate\":\"2024-06-12 11:54:23\",\"endSaleDate\":\"2024-09-06 00:00:00\",\"status\":1,\"saleChannel\":0,\"salePlatform\":3,\"sourceId\":102,\"tradeType\":3},\"image\":{\"defaultPicPath\":\"https://p0.meituan.net/dpmerchantpic/919712b0887559b04819b848c14203c174750.jpg\",\"allPicPaths\":\"https://p0.meituan.net/dpmerchantpic/919712b0887559b04819b848c14203c174750.jpg\"},\"category\":{\"categoryId\":512,\"serviceType\":\"纹眉\",\"serviceTypeId\":137013},\"serviceProject\":{\"title\":\"团购详情\",\"salePrice\":\"900.00\",\"marketPrice\":\"988.00\",\"mustGroups\":[{\"groups\":[{\"skuId\":0,\"categoryId\":2106228,\"name\":\"纹眉\",\"amount\":1,\"marketPrice\":\"988.0\",\"status\":10,\"attrs\":[{\"metaAttrId\":355827,\"attrName\":\"ProjectClassification\",\"chnName\":\"项目分类\",\"attrValue\":\"线条眉\",\"rawAttrValue\":\"线条眉\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":604,\"attrName\":\"suitCrowds\",\"chnName\":\"适用人群\",\"attrValue\":\"眉毛基础条件好、断眉\",\"rawAttrValue\":\"眉毛基础条件好、断眉\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":965133,\"attrName\":\"peise\",\"chnName\":\"配色\",\"attrValue\":\"二色渐变\",\"rawAttrValue\":\"二色渐变\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":965134,\"attrName\":\"ranliaochandi\",\"chnName\":\"染料产地\",\"attrValue\":\"国产\",\"rawAttrValue\":\"国产\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1756,\"attrName\":\"servicestep\",\"chnName\":\"服务步骤\",\"attrValue\":\"[{\\\"stepName\\\":\\\"眉形设计\\\",\\\"stepDesc\\\":\\\"测试\\\"},{\\\"stepName\\\":\\\"舒缓护理\\\",\\\"stepDesc\\\":\\\"测试\\\"},{\\\"stepName\\\":\\\"操作上色\\\",\\\"stepDesc\\\":\\\"测试\\\"},{\\\"stepName\\\":\\\"术后修护\\\",\\\"stepDesc\\\":\\\"测试\\\"}]\",\"rawAttrValue\":\"[{\\\"stepName\\\":\\\"眉形设计\\\",\\\"stepDesc\\\":\\\"测试\\\"},{\\\"stepName\\\":\\\"舒缓护理\\\",\\\"stepDesc\\\":\\\"测试\\\"},{\\\"stepName\\\":\\\"操作上色\\\",\\\"stepDesc\\\":\\\"测试\\\"},{\\\"stepName\\\":\\\"术后修护\\\",\\\"stepDesc\\\":\\\"测试\\\"}]\",\"valueType\":300,\"sequence\":0},{\"metaAttrId\":2586,\"attrName\":\"grant\",\"chnName\":\"是否提供免费补色服务\",\"attrValue\":\"赠送\",\"rawAttrValue\":\"赠送\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":965136,\"attrName\":\"buseshijian\",\"chnName\":\"补色时间\",\"attrValue\":\"3\",\"rawAttrValue\":\"3\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":965137,\"attrName\":\"mianfeibuse\",\"chnName\":\"免费补色次数\",\"attrValue\":\"1\",\"rawAttrValue\":\"1\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1390,\"attrName\":\"serviceDuration\",\"chnName\":\"服务时长\",\"attrValue\":\"120\",\"rawAttrValue\":\"120\",\"valueType\":500,\"sequence\":0}]}]}],\"optionGroups\":[],\"structType\":\"uniform-structure-table\"},\"channel\":{\"channelId\":5,\"channelEn\":\"beauty\",\"channelCn\":\"丽人\",\"channelGroupId\":2,\"channelGroupEn\":\"general\",\"channelGroupCn\":\"到店综合\"},\"attrs\":[{\"name\":\"product_finish_date\",\"value\":[\"1970-01-01 08:00:00\"],\"source\":0},{\"name\":\"calc_holiday_available\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_business_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_channel_id_allowed\",\"value\":[\"0\"],\"source\":0},{\"name\":\"product_can_use_coupon\",\"value\":[\"true\"],\"source\":0},{\"name\":\"preSaleTag\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_third_party_verify\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_block_stock\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_discount_rule_id\",\"value\":[\"0\"],\"source\":0},{\"name\":\"service_type\",\"value\":[\"纹眉\"],\"source\":0},{\"name\":\"reservation_is_needed_or_not\",\"value\":[\"否\"],\"source\":0},{\"name\":\"sys_deal_universal_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"category\",\"value\":[\"50\",\"5012\"],\"source\":0}],\"rule\":{\"buyRule\":{\"maxPerUser\":0,\"minPerUser\":1},\"useRule\":{\"receiptEffectiveDate\":{\"receiptDateType\":1,\"receiptValidDays\":90,\"receiptBeginDate\":\"2024-06-14 16:18:05\",\"receiptEndDate\":\"2024-09-12 23:59:59\",\"showText\":\"购买后90天内有效\"}},\"refundRule\":{\"supportRefundType\":1,\"supportOverdueAutoRefund\":true}},\"displayShopInfo\":{\"dpDisplayShopIds\":[1099031287],\"mtDisplayShopIds\":[1099031287]},\"tags\":[{\"id\":100003400,\"tagName\":\"纹绣\"}],\"customer\":{\"originCustomerId\":41887509},\"deals\":[{\"dealId\":1324227205,\"basic\":{\"title\":\"纹绣结构化测试\",\"originTitle\":\"纹绣结构化测试\",\"status\":1},\"price\":{\"salePrice\":\"900.00\",\"marketPrice\":\"988.00\",\"version\":8807540954},\"stock\":{\"dpSales\":0,\"dpTotal\":100000000,\"dpRemain\":100000000,\"mtSales\":0,\"mtTotal\":100000000,\"mtRemain\":100000000,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1,\"sharedSales\":0,\"sharedTotal\":0,\"sharedRemain\":0,\"isSharedSoldOut\":false},\"attrs\":[{\"name\":\"sku_receipt_type\",\"value\":[\"1\"],\"source\":0}],\"dealIdInt\":1324227205}],\"price\":{\"salePrice\":\"900.00\",\"marketPrice\":\"988.00\",\"version\":8807540954},\"stock\":{\"dpSales\":0,\"dpTotal\":100000000,\"dpRemain\":100000000,\"mtSales\":0,\"mtTotal\":100000000,\"mtRemain\":100000000,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1},\"detail\":{\"dealGroupPics\":\"https://p0.meituan.net/dpmerchantpic/919712b0887559b04819b848c14203c174750.jpg\",\"images\":[\"https://p0.meituan.net/dpmerchantpic/919712b0887559b04819b848c14203c174750.jpg%40450w_280h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\"],\"importantPoint\":\"\",\"templateDetailDTOs\":[{\"title\":\"产品介绍\",\"content\":\"<div><p>纹绣结构化测试</p></div>\\n\\n\",\"type\":5,\"blockId\":0},{\"title\":\"团购详情\",\"content\":\"        <div class=\\\"detail-tit\\\"></div>\\n        <div>\\n            <table width=\\\"100%\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" class=\\\"detail-table\\\">\\n                <thead>\\n                <tr>\\n                    <th width=\\\"50%\\\">名称</th>\\n                    <th width=\\\"25%\\\">数量</th>\\n                    <th width=\\\"25%\\\">单价</th>\\n                </tr>\\n                </thead>\\n                <tbody>\\n                            <tr>\\n                                <td>纹眉</td>\\n                                    <td class=\\\"tc\\\">1</td>\\n                                    <td class=\\\"tc\\\">988元</td>\\n                            </tr>\\n                    <tr class=\\\"total\\\">\\n                        <td></td>\\n                        <td class=\\\"tc\\\">总价<br><strong>团购价</strong></td>\\n                        <td class=\\\"tc\\\">988元<br><strong>900元</strong>\\n                        </td>\\n                    </tr>\\n                </tbody>\\n            </table>\\n        </div>\\n\\n\",\"type\":1,\"blockId\":0},{\"title\":\"购买须知\",\"content\":\"<div class=\\\"detail-box\\\">\\n    <div class=\\\"purchase-notes\\\">\\n\\t\\t        <dl>\\n            <dt>有效期</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">\\n    购买后90天内有效\\n        </p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>预约信息</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">无需预约，如遇消费高峰时段您可能需要排队</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>适用人数</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">每张团购券不限使用人数</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>规则提醒</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">不再与其他优惠同享\\n</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>温馨提示</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">如需团购券发票，请您在消费时向商户咨询</p>\\n                <p class=\\\"listitem\\\">为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！</p>\\n            </dd>\\n        </dl>\\n    </div>\\n</div>\\n\",\"type\":2,\"blockId\":0}]},\"saleChannelAggregation\":{\"allSupport\":true,\"supportChannels\":[],\"notSupportChannels\":[]},\"dpDealGroupIdInt\":1094756332,\"mtDealGroupIdInt\":1094756332}";
        DealGroupDTO dealGroupDTO = JSON.parseObject(dealGroupJson, DealGroupDTO.class);
        ctx.setDealGroupDTO(dealGroupDTO);
        // act
        List<CommonAttrVO> result = beautyHighlightsV2Processor.getTattooShoppingGuideAttrs(ctx);
        // assert
        assertNotNull(result);
    }
}
