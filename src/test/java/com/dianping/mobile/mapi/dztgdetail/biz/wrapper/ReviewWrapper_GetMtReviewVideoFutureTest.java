package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ReviewWrapper;
import com.dianping.ugc.pic.remote.service.VideoService;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Future;
import static org.mockito.ArgumentMatchers.anyList;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class ReviewWrapper_GetMtReviewVideoFutureTest {

    @InjectMocks
    private ReviewWrapper reviewWrapper;

    @Mock
    private VideoService mtReviewVideoServiceFuture;

    @Mock
    private Future mockFuture;

    private MockedStatic<FutureFactory> mockedFutureFactory;

    public ReviewWrapper_GetMtReviewVideoFutureTest() {
        MockitoAnnotations.initMocks(this);
    }

    @Before
    public void setUp() {
        mockedFutureFactory = mockStatic(FutureFactory.class);
    }

    @After
    public void tearDown() {
        mockedFutureFactory.close();
    }

    /**
     * 测试 videos 为空的情况
     */
    @Test
    public void testGetMtReviewVideoFutureVideosIsNull() throws Throwable {
        // arrange
        List<Long> videos = null;
        // act
        Future result = reviewWrapper.getMtReviewVideoFuture(videos);
        // assert
        assertNull(result);
    }

    /**
     * 测试 videos 不为空，且 mtReviewVideoServiceFuture.batchLoadVideoData(Lists.newArrayList(videos)) 方法正常执行的情况
     */
    @Test
    public void testGetMtReviewVideoFutureVideosIsNotNullAndMethodExecutesNormally() throws Throwable {
        // arrange
        List<Long> videos = Arrays.asList(1L, 2L, 3L);
        // Assuming this method returns null for simplicity
        when(mtReviewVideoServiceFuture.batchLoadVideoData(anyList())).thenReturn(null);
        // Mocking FutureFactory to return a mock Future
        mockedFutureFactory.when(FutureFactory::getFuture).thenReturn(mockFuture);
        // act
        Future result = reviewWrapper.getMtReviewVideoFuture(videos);
        // assert
        assertNotNull(result);
        verify(mtReviewVideoServiceFuture, times(1)).batchLoadVideoData(anyList());
    }

    /**
     * 测试 videos 不为空，且 mtReviewVideoServiceFuture.batchLoadVideoData(Lists.newArrayList(videos)) 方法抛出异常的情况
     */
    @Test
    public void testGetMtReviewVideoFutureVideosIsNotNullAndMethodThrowsException() throws Throwable {
        // arrange
        List<Long> videos = Arrays.asList(1L, 2L, 3L);
        doThrow(new RuntimeException()).when(mtReviewVideoServiceFuture).batchLoadVideoData(anyList());
        // act
        Future result = reviewWrapper.getMtReviewVideoFuture(videos);
        // assert
        assertNull(result);
        verify(mtReviewVideoServiceFuture, times(1)).batchLoadVideoData(anyList());
    }
}
