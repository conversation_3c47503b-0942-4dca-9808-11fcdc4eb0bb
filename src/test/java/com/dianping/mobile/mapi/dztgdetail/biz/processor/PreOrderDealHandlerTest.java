package com.dianping.mobile.mapi.dztgdetail.biz.processor;


import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.HaimaWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PreOrderWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.sankuai.fbi.lifeevent.reserverpcapi.dto.ProductShopReserveSupportStatusDTO;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

/**
 * @author: zhangyuan103
 * @create: 2025-04-11
 * @description:
 */
@RunWith(MockitoJUnitRunner.class)
public class PreOrderDealHandlerTest {

    @InjectMocks
    private PreOrderDealHandler preOrderDealHandler;

    @Mock
    private HaimaWrapper haimaWrapper;

    @Mock
    private PreOrderWrapper preOrderWrapper;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private FutureCtx futureCtx;

    private MockedStatic<DealUtils> mockedDealUtils;

    @Before
    public void setUp() {
        mockedDealUtils = Mockito.mockStatic(DealUtils.class);
        when(dealCtx.getFutureCtx()).thenReturn(futureCtx);
    }

    @After
    public void after() {
        mockedDealUtils.close();
    }

    @Test
    public void testIsEnable_WhenAllConditionsMet_ShouldReturnTrue() {
        // arrange
        Long shopId = 123L;
        when(dealCtx.getDpLongShopId()).thenReturn(shopId);
        mockedDealUtils.when(() -> DealUtils.isPreOrderDealForCateAndPromotion(dealCtx)).thenReturn(true);
        when(haimaWrapper.isHitPreOrderDpShopId(shopId)).thenReturn(true);

        // act
        boolean result = preOrderDealHandler.isEnable(dealCtx);

        // assert
        assertTrue(result);
    }

    @Test
    public void testPrepare_ShouldSetFuture() {
        // arrange
        CompletableFuture<ProductShopReserveSupportStatusDTO> future = new CompletableFuture<>();
        when(preOrderWrapper.preProductShopReserve(dealCtx)).thenReturn(future);

        // act
        preOrderDealHandler.prepare(dealCtx);

        // assert
        verify(futureCtx).setPreOrderDealRpcFuture(future);
    }

    @Test
    public void testProcess_WhenFutureCtxIsNull_ShouldReturn() {
        // arrange
        when(dealCtx.getFutureCtx()).thenReturn(null);

        // act
        preOrderDealHandler.process(dealCtx);

        // assert
        verify(preOrderWrapper, never()).queryProductShopReserve(any());
    }

    @Test
    public void testProcess_WhenRpcFutureIsNull_ShouldReturn() {
        // arrange
        when(futureCtx.getPreOrderDealRpcFuture()).thenReturn(null);

        // act
        preOrderDealHandler.process(dealCtx);

        // assert
        verify(preOrderWrapper, never()).queryProductShopReserve(any());
    }

    @Test
    public void testProcess_WhenDtoIsNull_ShouldReturn() {
        // arrange
        CompletableFuture<ProductShopReserveSupportStatusDTO> future = new CompletableFuture<>();
        when(futureCtx.getPreOrderDealRpcFuture()).thenReturn(future);
        when(preOrderWrapper.queryProductShopReserve(future)).thenReturn(null);

        // act
        preOrderDealHandler.process(dealCtx);

        // assert
        verify(dealCtx, never()).setPreOderDeal(anyBoolean());
    }

    @Test
    public void testProcess_WhenDtoIsValid_ShouldSetPreOrderDeal() {
        // arrange
        CompletableFuture<ProductShopReserveSupportStatusDTO> future = new CompletableFuture<>();
        when(futureCtx.getPreOrderDealRpcFuture()).thenReturn(future);

        ProductShopReserveSupportStatusDTO dto = new ProductShopReserveSupportStatusDTO();
        dto.setReserveEnable(true);
        when(preOrderWrapper.queryProductShopReserve(future)).thenReturn(dto);

        // act
        preOrderDealHandler.process(dealCtx);

        // assert
        verify(dealCtx).setPreOderDeal(true);
    }
}
