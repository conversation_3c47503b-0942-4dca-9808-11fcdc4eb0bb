package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailDisplayUnitVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailSpecificModuleVO;
import com.dianping.mobile.mapi.dztgdetail.entity.EyeServiceProcessNode;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class SpecificModuleHandler_1603HandleTest {

    @InjectMocks
    private SpecificModuleHandler_1603 specificModuleHandler_1603;

    @Mock
    private DealGroupDTO dealGroupDTO;

    @Mock
    private DealGroupServiceProjectDTO dealGroupServiceProjectDTO;

    @Mock
    private MustServiceProjectGroupDTO mustServiceProjectGroupDTO;

    @Mock
    private ServiceProjectDTO serviceProjectDTO;

    @Mock
    private ServiceProjectAttrDTO serviceProjectAttrDTO;

    /**
     * 测试 handle 方法，正常场景
     */
    @Test
    public void testHandleNormal() throws Throwable {
        // arrange
        DealDetailSpecificModuleVO expected = new DealDetailSpecificModuleVO();
        expected.setUnits(Collections.singletonList(new DealDetailDisplayUnitVO()));
        when(mockCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getServiceProject()).thenReturn(dealGroupServiceProjectDTO);
        when(dealGroupServiceProjectDTO.getMustGroups()).thenReturn(Collections.singletonList(mustServiceProjectGroupDTO));
        when(mustServiceProjectGroupDTO.getGroups()).thenReturn(Collections.singletonList(serviceProjectDTO));
        when(serviceProjectDTO.getAttrs()).thenReturn(Collections.singletonList(serviceProjectAttrDTO));
        // act
        specificModuleHandler_1603.handle(mockCtx);
        // assert
        verify(mockCtx, times(1)).setResult(any(DealDetailSpecificModuleVO.class));
    }

    /**
     * 测试 handle 方法，ctx 参数为空
     */
    @Test(expected = NullPointerException.class)
    public void testHandleCtxIsNull() throws Throwable {
        // arrange
        // act
        specificModuleHandler_1603.handle(null);
        // assert
    }

    /**
     * 测试 handle 方法，buildResult(ctx) 方法抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testHandleBuildResultThrowsException() throws Throwable {
        // arrange
        when(mockCtx.getDealGroupDTO()).thenThrow(new RuntimeException());
        // act
        specificModuleHandler_1603.handle(mockCtx);
        // assert
    }

    @Mock
    private SpecificModuleCtx mockCtx;
}
