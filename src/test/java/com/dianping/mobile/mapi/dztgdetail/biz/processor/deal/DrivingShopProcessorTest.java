package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.beauty.zone.remote.dto.AttributeDTO;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.SwitchHelper;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

public class DrivingShopProcessorTest {

    @InjectMocks
    private DrivingShopProcessor drivingShopProcessor;

    @Mock
    private DealCtx ctx;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Test case where DP shop ID is less than or equal to 0.
     */
    @Test
    public void testIsEnableDpShopIdLessThanOrEqualToZero() throws Throwable {
        // arrange
        when(ctx.getDpLongShopId()).thenReturn(0L);
        // act
        boolean result = drivingShopProcessor.isEnable(ctx);
        // assert
        assertFalse(result);
    }
}
