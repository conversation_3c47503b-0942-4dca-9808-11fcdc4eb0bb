package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.client.Lion;
import com.dianping.lion.facade.LionFacade;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.sankuai.douhu.absdk.bean.DouHuRequest;
import com.sankuai.douhu.absdk.bean.DouHuResponse;
import com.sankuai.douhu.absdk.client.DouHuClient;
import com.sankuai.douhu.absdk.enums.ErrorCode;
import com.sankuai.douhu.absdk.util.DouHuUtil;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DouHuBizGetAbExpResultByUuidAndDpidV2Test {

    @Mock
    private DouHuClient douHuClient;

    @InjectMocks
    private DouHuBiz douHuBiz;

    private DealCtx dealCtx;

    private EnvCtx envCtx;

    @Before
    public void setUp() {
        envCtx = new EnvCtx();
        envCtx.setUuid("test-uuid");
        envCtx.setDpId("test-dpid");
        dealCtx = mock(DealCtx.class);
    }

    /**
     * Test when module is null
     */
    @Test
    public void testGetAbExpResultByUuidAndDpidV2WithNullModule() throws Throwable {
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResultByUuidAndDpidV2(dealCtx, null);
        // assert
        assertNull(result);
        verifyNoInteractions(douHuClient);
    }
}
