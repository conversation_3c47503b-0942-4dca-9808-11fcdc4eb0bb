package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedDeals;
import com.dianping.mobile.mapi.dztgdetail.tab.RelateDeals;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealQueryFacadeCardStyleEnvironmentPassTest {

    private DealQueryFacade facade;

    @Mock
    private RelatedDeals relatedDeals;

    @InjectMocks
    private DealQueryFacade dealQueryFacade = new DealQueryFacade();

    @Before
    public void setUp() {
        facade = new DealQueryFacade();
    }

    private boolean invokePrivateMethod(Object target, String methodName, Object... args) throws Throwable {
        Method method = DealQueryFacade.class.getDeclaredMethod(methodName, String.class, EnvCtx.class);
        method.setAccessible(true);
        return (boolean) method.invoke(target, args);
    }

    private List<ModuleAbConfig> invokePrivateGetModuleAbConfigs(RelatedDeals deals) throws Throwable {
        try {
            Method method = DealQueryFacade.class.getDeclaredMethod("getModuleAbConfigs", RelatedDeals.class);
            method.setAccessible(true);
            return (List<ModuleAbConfig>) method.invoke(dealQueryFacade, deals);
        } catch (Exception e) {
            throw e.getCause();
        }
    }

    private List<ModuleAbConfig> getModuleAbConfigs(RelatedDeals deals) {
        List<ModuleAbConfig> moduleAbConfigs = deals.getModuleAbConfigs();
        if (CollectionUtils.isEmpty(moduleAbConfigs)) {
            moduleAbConfigs = new ArrayList<>();
            deals.setModuleAbConfigs(moduleAbConfigs);
        }
        return moduleAbConfigs;
    }

    /**
     * 测试 envCtx 为 null 的情况
     */
    @Test
    public void testCardStyleEnvironmentPass_EnvCtxIsNull() throws Throwable {
        // arrange
        String mrnVersion = "0.5.3";
        // act
        boolean result = invokePrivateMethod(facade, "cardStyleEnvironmentPass", mrnVersion, null);
        // assert
        assertFalse(result);
    }

    /**
     * 测试美团客户端，版本号大于等于 12.11.200，mrnVersion 大于等于 0.5.3
     */
    @Test
    public void testCardStyleEnvironmentPass_MeituanAppVersionAndMrnVersionValid() throws Throwable {
        // arrange
        String mrnVersion = "0.5.3";
        EnvCtx envCtx = mock(EnvCtx.class);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_APP);
        when(envCtx.getVersion()).thenReturn("12.11.200");
        // act
        boolean result = invokePrivateMethod(facade, "cardStyleEnvironmentPass", mrnVersion, envCtx);
        // assert
        assertTrue(result);
    }

    /**
     * 测试美团客户端，版本号小于 12.11.200
     */
    @Test
    public void testCardStyleEnvironmentPass_MeituanAppVersionInvalid() throws Throwable {
        // arrange
        String mrnVersion = "0.5.3";
        EnvCtx envCtx = mock(EnvCtx.class);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_APP);
        when(envCtx.getVersion()).thenReturn("12.10.200");
        // act
        boolean result = invokePrivateMethod(facade, "cardStyleEnvironmentPass", mrnVersion, envCtx);
        // assert
        assertFalse(result);
    }

    /**
     * 测试美团客户端，版本号大于等于 12.11.200，mrnVersion 小于 0.5.3
     */
    @Test
    public void testCardStyleEnvironmentPass_MeituanAppMrnVersionInvalid() throws Throwable {
        // arrange
        String mrnVersion = "0.5.2";
        EnvCtx envCtx = mock(EnvCtx.class);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_APP);
        when(envCtx.getVersion()).thenReturn("12.11.200");
        // act
        boolean result = invokePrivateMethod(facade, "cardStyleEnvironmentPass", mrnVersion, envCtx);
        // assert
        assertFalse(result);
    }

    /**
     * 测试点评客户端，版本号大于等于 11.4.0，mrnVersion 大于等于 0.5.3
     */
    @Test
    public void testCardStyleEnvironmentPass_DianpingAppVersionAndMrnVersionValid() throws Throwable {
        // arrange
        String mrnVersion = "0.5.3";
        EnvCtx envCtx = mock(EnvCtx.class);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.DIANPING_APP);
        when(envCtx.getVersion()).thenReturn("11.4.0");
        // act
        boolean result = invokePrivateMethod(facade, "cardStyleEnvironmentPass", mrnVersion, envCtx);
        // assert
        assertTrue(result);
    }

    /**
     * 测试点评客户端，版本号小于 11.4.0
     */
    @Test
    public void testCardStyleEnvironmentPass_DianpingAppVersionInvalid() throws Throwable {
        // arrange
        String mrnVersion = "0.5.3";
        EnvCtx envCtx = mock(EnvCtx.class);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.DIANPING_APP);
        when(envCtx.getVersion()).thenReturn("11.3.0");
        // act
        boolean result = invokePrivateMethod(facade, "cardStyleEnvironmentPass", mrnVersion, envCtx);
        // assert
        assertFalse(result);
    }

    /**
     * 测试点评客户端，版本号大于等于 11.4.0，mrnVersion 小于 0.5.3
     */
    @Test
    public void testCardStyleEnvironmentPass_DianpingAppMrnVersionInvalid() throws Throwable {
        // arrange
        String mrnVersion = "0.5.2";
        EnvCtx envCtx = mock(EnvCtx.class);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.DIANPING_APP);
        when(envCtx.getVersion()).thenReturn("11.4.0");
        // act
        boolean result = invokePrivateMethod(facade, "cardStyleEnvironmentPass", mrnVersion, envCtx);
        // assert
        assertFalse(result);
    }

    /**
     * 测试其他客户端类型
     */
    @Test
    public void testCardStyleEnvironmentPass_OtherClientType() throws Throwable {
        // arrange
        String mrnVersion = "0.5.3";
        EnvCtx envCtx = mock(EnvCtx.class);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP);
        // act
        boolean result = invokePrivateMethod(facade, "cardStyleEnvironmentPass", mrnVersion, envCtx);
        // assert
        assertFalse(result);
    }

    /**
     * 测试当 moduleAbConfigs 不为空且不为 null 时，直接返回 moduleAbConfigs 列表
     */
    @Test
    public void testGetModuleAbConfigsWhenModuleAbConfigsIsNotEmpty() throws Throwable {
        // arrange
        List<ModuleAbConfig> expectedModuleAbConfigs = new ArrayList<>();
        expectedModuleAbConfigs.add(new ModuleAbConfig());
        when(relatedDeals.getModuleAbConfigs()).thenReturn(expectedModuleAbConfigs);
        // act
        List<ModuleAbConfig> result = invokePrivateGetModuleAbConfigs(relatedDeals);
        // assert
        assertEquals(expectedModuleAbConfigs, result);
        verify(relatedDeals, never()).setModuleAbConfigs(anyList());
    }

    /**
     * 测试当 moduleAbConfigs 为空时，创建一个新的 ArrayList 并设置回 RelatedDeals 对象
     */
    @Test
    public void testGetModuleAbConfigsWhenModuleAbConfigsIsEmpty() throws Throwable {
        // arrange
        when(relatedDeals.getModuleAbConfigs()).thenReturn(null);
        // act
        List<ModuleAbConfig> result = invokePrivateGetModuleAbConfigs(relatedDeals);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(relatedDeals).setModuleAbConfigs(result);
    }

    /**
     * 测试当 RelatedDeals 对象为 null 时，抛出 NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testGetModuleAbConfigsWhenRelatedDealsIsNull() throws Throwable {
        // arrange
        RelatedDeals nullRelatedDeals = null;
        // act
        invokePrivateGetModuleAbConfigs(nullRelatedDeals);
        // assert
        // 期望抛出 NullPointerException
    }
}
