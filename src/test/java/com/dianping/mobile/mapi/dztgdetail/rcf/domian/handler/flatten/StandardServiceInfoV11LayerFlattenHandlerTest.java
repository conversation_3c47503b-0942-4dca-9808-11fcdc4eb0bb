package com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler.flatten;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.mobile.mapi.dztgdetail.rcf.enums.ModuleType;
import com.dianping.mobile.mapi.dztgdetail.util.ApplicationContextGetBeanHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.Objects;


/**
 * <AUTHOR>
 * @create 2024/12/25 11:16
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({ApplicationContextGetBeanHelper.class, ApplicationContext.class})
public class StandardServiceInfoV11LayerFlattenHandlerTest {
    @InjectMocks
    private StandardServiceInfoV11LayerFlattenHandler flattenHandler;

    @Test
    public void getType() {
        Assert.assertTrue(ModuleType.standard_service_info_v1_1layer == flattenHandler.getType());
    }

    @Test
    public void flattenModule() {
        String json = "{\"dotType\":0,\"showNum\":0,\"name\":\"服务设施\",\"skuGroupsModel1\":[{\"dealSkuList\":[{\"icon\":\"https://p1.meituan.net/travelcube/40ea3961df9de704ef23df8570bc33361494.png\",\"title\":\"汗蒸\",\"items\":[{\"showAll\":false,\"icon\":\"https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png\",\"name\":\"湿蒸\",\"type\":0},{\"showAll\":false,\"icon\":\"https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png\",\"name\":\"干蒸\",\"type\":0}]},{\"icon\":\"https://p0.meituan.net/travelcube/284969b440784da1f6f90718625418711135.png\",\"title\":\"洗护用品\",\"items\":[{\"showAll\":false,\"icon\":\"https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png\",\"name\":\"一次性剃须刀\",\"type\":0},{\"showAll\":false,\"icon\":\"https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png\",\"name\":\"一次性牙刷\",\"type\":0},{\"showAll\":false,\"icon\":\"https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png\",\"name\":\"一次性内裤\",\"type\":0},{\"showAll\":false,\"icon\":\"https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png\",\"name\":\"浴巾(高温消毒)\",\"type\":0},{\"showAll\":false,\"icon\":\"https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png\",\"name\":\"浴服(高温消毒)\",\"type\":0},{\"showAll\":false,\"icon\":\"https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png\",\"name\":\"洗发水\",\"type\":0},{\"showAll\":false,\"icon\":\"https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png\",\"name\":\"沐浴露\",\"type\":0},{\"showAll\":false,\"icon\":\"https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png\",\"name\":\"洗面奶\",\"type\":0},{\"showAll\":false,\"icon\":\"https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png\",\"name\":\"吹风机\",\"type\":0},{\"showAll\":false,\"icon\":\"https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png\",\"name\":\"护肤品\",\"type\":0}]}]}],\"type\":\"standard_facility_v1_2layer\"}";
        JSONObject module  = JSON.parseObject(json);
        JSONArray rcfSkuGroupsModule1Flatten = new JSONArray();
        flattenHandler.flattenModule(rcfSkuGroupsModule1Flatten, module);
        Assert.assertFalse(rcfSkuGroupsModule1Flatten.isEmpty());

    }
}
