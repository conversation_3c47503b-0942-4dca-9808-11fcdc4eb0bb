package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.pay.promo.rule.api.dto.Response;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class PromoWrapper_GetPromoDisplayDTO_1_Test {

    @Mock
    private Future future;

    private PromoWrapper promoWrapper;

    @Before
    public void setUp() {
        promoWrapper = new PromoWrapper();
    }

    @Test
    public void testGetPromoDisplayDTOMapFutureIsNull() throws Throwable {
        Map<Integer, PromoDisplayDTO> result = promoWrapper.getPromoDisplayDTOMap(null);
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testGetPromoDisplayDTOMapResponseIsNull() throws Throwable {
        when(future.get()).thenReturn(null);
        Map<Integer, PromoDisplayDTO> result = promoWrapper.getPromoDisplayDTOMap(future);
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testGetPromoDisplayDTOMapResultIsEmpty() throws Throwable {
        Response<Map<Integer, List<PromoDisplayDTO>>> response = new Response<>();
        response.setResult(Collections.emptyMap());
        when(future.get()).thenReturn(response);
        Map<Integer, PromoDisplayDTO> result = promoWrapper.getPromoDisplayDTOMap(future);
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testGetPromoDisplayDTOMapValueIsEmpty() throws Throwable {
        Map<Integer, List<PromoDisplayDTO>> promoMaps = new HashMap<>();
        promoMaps.put(1, Collections.emptyList());
        Response<Map<Integer, List<PromoDisplayDTO>>> response = new Response<>();
        response.setResult(promoMaps);
        when(future.get()).thenReturn(response);
        Map<Integer, PromoDisplayDTO> result = promoWrapper.getPromoDisplayDTOMap(future);
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testGetPromoDisplayDTOMapPromoDisplayDTOIsNotEnableOrPriceLineThrough() throws Throwable {
        PromoDisplayDTO promoDisplayDTO = new PromoDisplayDTO();
        promoDisplayDTO.setEnable(false);
        promoDisplayDTO.setPriceLineThrough(false);
        Map<Integer, List<PromoDisplayDTO>> promoMaps = new HashMap<>();
        promoMaps.put(1, Collections.singletonList(promoDisplayDTO));
        Response<Map<Integer, List<PromoDisplayDTO>>> response = new Response<>();
        response.setResult(promoMaps);
        when(future.get()).thenReturn(response);
        Map<Integer, PromoDisplayDTO> result = promoWrapper.getPromoDisplayDTOMap(future);
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testGetPromoDisplayDTOMapPromoDisplayDTOIsEnableAndPriceLineThrough() throws Throwable {
        PromoDisplayDTO promoDisplayDTO = new PromoDisplayDTO();
        promoDisplayDTO.setEnable(true);
        promoDisplayDTO.setPriceLineThrough(true);
        Map<Integer, PromoDisplayDTO> expectedMap = new HashMap<>();
        expectedMap.put(1, promoDisplayDTO);
        Response<Map<Integer, List<PromoDisplayDTO>>> response = new Response<>();
        response.setResult(Collections.singletonMap(1, Collections.singletonList(promoDisplayDTO)));
        when(future.get()).thenReturn(response);
        Map<Integer, PromoDisplayDTO> result = promoWrapper.getPromoDisplayDTOMap(future);
        assertEquals(expectedMap, result);
    }
}
