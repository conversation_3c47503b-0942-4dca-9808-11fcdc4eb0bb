package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.DigestConfinementVrDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.DigestInfoDTO;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.mpmctcontent.query.thrift.api.digest.DigestQueryService;
import com.sankuai.mpmctcontent.query.thrift.dto.digest.DigestInfoItemDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.digest.QueryDigestRequestDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.digest.QueryDigestResponseDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.digest.internal.QueryDigestResponseReader;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Test cases for DigestQueryWrapper.getDarenVideos
 */
@RunWith(MockitoJUnitRunner.class)
public class DigestQueryWrapperTest {

    // Removed setUp and tearDown methods as per the instructions
    @InjectMocks
    private DigestQueryWrapper digestQueryWrapper;
    @Mock
    private DigestQueryService digestQueryService;

    private MockedStatic<QueryDigestRequestDTO> digestRequestDTOMockedStatic;

    private Future futureMock;

    @Before
    public void setup() {
        digestRequestDTOMockedStatic = Mockito.mockStatic(QueryDigestRequestDTO.class);
    }

    @After
    public void tearDown() {
        digestRequestDTOMockedStatic.close();
    }

    @Test
    public void testGetDarenVideosFutureIsNull() throws Throwable {
        DigestQueryWrapper digestQueryWrapper = new DigestQueryWrapper();
        Future future = null;
        int dpDealId = 123;
        DigestInfoDTO result = digestQueryWrapper.getDarenVideos(future, dpDealId);
        assertNull(result);
    }

    @Test
    public void testGetDarenVideosResponseCodeNot200() throws Throwable {
        DigestQueryWrapper digestQueryWrapper = new DigestQueryWrapper();
        Future futureMock = mock(Future.class);
        int dpDealId = 123;
        QueryDigestResponseDTO responseDTO = new QueryDigestResponseDTO();
        when(futureMock.get()).thenReturn(responseDTO);
        DigestInfoDTO result = digestQueryWrapper.getDarenVideos(futureMock, dpDealId);
        assertNull(result);
    }

    @Test
    public void testGetDarenVideosDigestItemIsNull() throws Throwable {
        DigestQueryWrapper digestQueryWrapper = new DigestQueryWrapper();
        Future futureMock = mock(Future.class);
        int dpDealId = 123;
        QueryDigestResponseDTO responseDTO = mock(QueryDigestResponseDTO.class);
        when(futureMock.get()).thenReturn(responseDTO);
        try (MockedStatic<QueryDigestResponseReader> mockedStatic = Mockito.mockStatic(QueryDigestResponseReader.class)) {
            QueryDigestResponseReader reader = mock(QueryDigestResponseReader.class);
            mockedStatic.when(() -> QueryDigestResponseReader.read(responseDTO)).thenReturn(reader);
//            when(reader.getDigestItemById("31_1", String.valueOf(dpDealId))).thenReturn(null);
            DigestInfoDTO result = digestQueryWrapper.getDarenVideos(futureMock, dpDealId);
            assertNull(result);
        }
    }

    @Test
    public void testGetDarenVideosDataIsBlank() throws Throwable {
        DigestQueryWrapper digestQueryWrapper = new DigestQueryWrapper();
        Future futureMock = mock(Future.class);
        int dpDealId = 123;
        QueryDigestResponseDTO responseDTO = mock(QueryDigestResponseDTO.class);
        when(futureMock.get()).thenReturn(responseDTO);
        try (MockedStatic<QueryDigestResponseReader> mockedStatic = Mockito.mockStatic(QueryDigestResponseReader.class)) {
            QueryDigestResponseReader reader = mock(QueryDigestResponseReader.class);
            mockedStatic.when(() -> QueryDigestResponseReader.read(responseDTO)).thenReturn(reader);
            DigestInfoItemDTO digestInfoItemDTO = new DigestInfoItemDTO();
            digestInfoItemDTO.setData("");
//            when(reader.getDigestItemById("31_1", String.valueOf(dpDealId))).thenReturn(digestInfoItemDTO);
            DigestInfoDTO result = digestQueryWrapper.getDarenVideos(futureMock, dpDealId);
            assertNull(result);
        }
    }

    @Test
    public void testGetDarenVideosSuccess() throws Throwable {
        DigestQueryWrapper digestQueryWrapper = new DigestQueryWrapper();
        Future futureMock = mock(Future.class);
        int dpDealId = 123;
        String jsonData = "{\"videoWidth\":1920,\"videoUrl\":\"http://example.com/video.mp4\",\"dpDealId\":123,\"videoFrameUrl\":\"http://example.com/frame.jpg\",\"videoSize\":1048576,\"videoHeight\":1080}";
        QueryDigestResponseDTO responseDTO = mock(QueryDigestResponseDTO.class);
        // Ensure the response code is 200
        when(responseDTO.getCode()).thenReturn(200);
        when(futureMock.get()).thenReturn(responseDTO);
        try (MockedStatic<QueryDigestResponseReader> mockedStatic = Mockito.mockStatic(QueryDigestResponseReader.class)) {
            QueryDigestResponseReader reader = mock(QueryDigestResponseReader.class);
            mockedStatic.when(() -> QueryDigestResponseReader.read(responseDTO)).thenReturn(reader);
            DigestInfoItemDTO digestInfoItemDTO = new DigestInfoItemDTO();
            digestInfoItemDTO.setData(jsonData);
            when(reader.getDigestItemById("31_1", String.valueOf(dpDealId))).thenReturn(digestInfoItemDTO);
            DigestInfoDTO result = digestQueryWrapper.getDarenVideos(futureMock, dpDealId);
            assertNotNull(result);
            assertEquals(1920, result.getVideoWidth());
            assertEquals("http://example.com/video.mp4", result.getVideoUrl());
            assertEquals(123, result.getDpDealId());
            assertEquals("http://example.com/frame.jpg", result.getVideoFrameUrl());
            assertEquals(1048576, result.getVideoSize());
            assertEquals(1080, result.getVideoHeight());
        }
    }

    @Test
    public void testGetSafeImplantFutureIsNull() throws Throwable {
        DigestQueryWrapper digestQueryWrapper = new DigestQueryWrapper();
        Future future = null;
        Long mtShopId = 123L;
        DigestInfoDTO result = digestQueryWrapper.getSafeImplantTags(future, mtShopId);
        assertNull(result);
    }

    @Test
    public void testGetSafeImplantResponseCodeNot200() throws Throwable {
        DigestQueryWrapper digestQueryWrapper = new DigestQueryWrapper();
        Future futureMock = mock(Future.class);
        Long mtShopId = 123L;
        QueryDigestResponseDTO responseDTO = new QueryDigestResponseDTO();
        when(futureMock.get()).thenReturn(responseDTO);
        DigestInfoDTO result = digestQueryWrapper.getSafeImplantTags(futureMock, mtShopId);
        assertNull(result);
    }

    @Test
    public void testGetSafeImplantDigestItemIsNull() throws Throwable {
        DigestQueryWrapper digestQueryWrapper = new DigestQueryWrapper();
        Future futureMock = mock(Future.class);
        Long mtShopId = 123L;
        QueryDigestResponseDTO responseDTO = mock(QueryDigestResponseDTO.class);
        when(futureMock.get()).thenReturn(responseDTO);
        try (MockedStatic<QueryDigestResponseReader> mockedStatic = Mockito.mockStatic(QueryDigestResponseReader.class)) {
            QueryDigestResponseReader reader = mock(QueryDigestResponseReader.class);
            mockedStatic.when(() -> QueryDigestResponseReader.read(responseDTO)).thenReturn(reader);
//            when(reader.getDigestItemById("56_1", String.valueOf(mtShopId))).thenReturn(null);
            DigestInfoDTO result = digestQueryWrapper.getSafeImplantTags(futureMock, mtShopId);
            assertNull(result);
        }
    }

    @Test
    public void testGetSafeImplantDataIsBlank() throws Throwable {
        DigestQueryWrapper digestQueryWrapper = new DigestQueryWrapper();
        Future futureMock = mock(Future.class);
        Long mtShopId = 123L;
        QueryDigestResponseDTO responseDTO = mock(QueryDigestResponseDTO.class);
        when(futureMock.get()).thenReturn(responseDTO);
        try (MockedStatic<QueryDigestResponseReader> mockedStatic = Mockito.mockStatic(QueryDigestResponseReader.class)) {
            QueryDigestResponseReader reader = mock(QueryDigestResponseReader.class);
            mockedStatic.when(() -> QueryDigestResponseReader.read(responseDTO)).thenReturn(reader);
            DigestInfoItemDTO digestInfoItemDTO = new DigestInfoItemDTO();
            digestInfoItemDTO.setData("");
//            when(reader.getDigestItemById("56_1", String.valueOf(mtShopId))).thenReturn(digestInfoItemDTO);
            DigestInfoDTO result = digestQueryWrapper.getSafeImplantTags(futureMock, mtShopId);
            assertNull(result);
        }
    }

    @Test
    public void testGetSafeImplantSuccess() throws Throwable {
        DigestQueryWrapper digestQueryWrapper = new DigestQueryWrapper();
        Future futureMock = mock(Future.class);
        Long mtShopId = 123L;
        String jsonData = "{\"videoWidth\":1920,\"videoUrl\":\"http://example.com/video.mp4\",\"dpDealId\":123,\"videoFrameUrl\":\"http://example.com/frame.jpg\",\"videoSize\":1048576,\"videoHeight\":1080}";
        QueryDigestResponseDTO responseDTO = mock(QueryDigestResponseDTO.class);
        // Ensure the response code is 200
        when(responseDTO.getCode()).thenReturn(200);
        when(futureMock.get()).thenReturn(responseDTO);
        try (MockedStatic<QueryDigestResponseReader> mockedStatic = Mockito.mockStatic(QueryDigestResponseReader.class)) {
            QueryDigestResponseReader reader = mock(QueryDigestResponseReader.class);
            mockedStatic.when(() -> QueryDigestResponseReader.read(responseDTO)).thenReturn(reader);
            DigestInfoItemDTO digestInfoItemDTO = new DigestInfoItemDTO();
            digestInfoItemDTO.setData(jsonData);
            when(reader.getDigestItemById("56_1", String.valueOf(mtShopId))).thenReturn(digestInfoItemDTO);
            DigestInfoDTO result = digestQueryWrapper.getSafeImplantTags(futureMock, mtShopId);
            assertNotNull(result);
            assertEquals(1920, result.getVideoWidth());
            assertEquals("http://example.com/video.mp4", result.getVideoUrl());
            assertEquals(123, result.getDpDealId());
            assertEquals("http://example.com/frame.jpg", result.getVideoFrameUrl());
            assertEquals(1048576, result.getVideoSize());
            assertEquals(1080, result.getVideoHeight());
        }
    }


    @Test
    public void testGetSafeDentureFutureIsNull() throws Throwable {
        DigestQueryWrapper digestQueryWrapper = new DigestQueryWrapper();
        Future future = null;
        Long mtShopId = 123L;
        DigestInfoDTO result = digestQueryWrapper.getSafeDentureTags(future, mtShopId);
        assertNull(result);
    }

    @Test
    public void testGetSafeDentureResponseCodeNot200() throws Throwable {
        DigestQueryWrapper digestQueryWrapper = new DigestQueryWrapper();
        Future futureMock = mock(Future.class);
        Long mtShopId = 123L;
        QueryDigestResponseDTO responseDTO = new QueryDigestResponseDTO();
        when(futureMock.get()).thenReturn(responseDTO);
        DigestInfoDTO result = digestQueryWrapper.getSafeDentureTags(futureMock, mtShopId);
        assertNull(result);
    }

    @Test
    public void testGetSafeDentureDigestItemIsNull() throws Throwable {
        DigestQueryWrapper digestQueryWrapper = new DigestQueryWrapper();
        Future futureMock = mock(Future.class);
        Long mtShopId = 123L;
        QueryDigestResponseDTO responseDTO = mock(QueryDigestResponseDTO.class);
        when(futureMock.get()).thenReturn(responseDTO);
        try (MockedStatic<QueryDigestResponseReader> mockedStatic = Mockito.mockStatic(QueryDigestResponseReader.class)) {
            QueryDigestResponseReader reader = mock(QueryDigestResponseReader.class);
            mockedStatic.when(() -> QueryDigestResponseReader.read(responseDTO)).thenReturn(reader);
//            when(reader.getDigestItemById("57_1", String.valueOf(mtShopId))).thenReturn(null);
            DigestInfoDTO result = digestQueryWrapper.getSafeDentureTags(futureMock, mtShopId);
            assertNull(result);
        }
    }

    @Test
    public void testGetSafeDentureDataIsBlank() throws Throwable {
        DigestQueryWrapper digestQueryWrapper = new DigestQueryWrapper();
        Future futureMock = mock(Future.class);
        Long mtShopId = 123L;
        QueryDigestResponseDTO responseDTO = mock(QueryDigestResponseDTO.class);
        when(futureMock.get()).thenReturn(responseDTO);
        try (MockedStatic<QueryDigestResponseReader> mockedStatic = Mockito.mockStatic(QueryDigestResponseReader.class)) {
            QueryDigestResponseReader reader = mock(QueryDigestResponseReader.class);
            mockedStatic.when(() -> QueryDigestResponseReader.read(responseDTO)).thenReturn(reader);
            DigestInfoItemDTO digestInfoItemDTO = new DigestInfoItemDTO();
            digestInfoItemDTO.setData("");
//            when(reader.getDigestItemById("57_1", String.valueOf(mtShopId))).thenReturn(digestInfoItemDTO);
            DigestInfoDTO result = digestQueryWrapper.getSafeDentureTags(futureMock, mtShopId);
            assertNull(result);
        }
    }

    @Test
    public void testGetSafeDentureSuccess() throws Throwable {
        DigestQueryWrapper digestQueryWrapper = new DigestQueryWrapper();
        Future futureMock = mock(Future.class);
        Long mtShopId = 123L;
        String jsonData = "{\"videoWidth\":1920,\"videoUrl\":\"http://example.com/video.mp4\",\"dpDealId\":123,\"videoFrameUrl\":\"http://example.com/frame.jpg\",\"videoSize\":1048576,\"videoHeight\":1080}";
        QueryDigestResponseDTO responseDTO = mock(QueryDigestResponseDTO.class);
        // Ensure the response code is 200
        when(responseDTO.getCode()).thenReturn(200);
        when(futureMock.get()).thenReturn(responseDTO);
        try (MockedStatic<QueryDigestResponseReader> mockedStatic = Mockito.mockStatic(QueryDigestResponseReader.class)) {
            QueryDigestResponseReader reader = mock(QueryDigestResponseReader.class);
            mockedStatic.when(() -> QueryDigestResponseReader.read(responseDTO)).thenReturn(reader);
            DigestInfoItemDTO digestInfoItemDTO = new DigestInfoItemDTO();
            digestInfoItemDTO.setData(jsonData);
            when(reader.getDigestItemById("57_1", String.valueOf(mtShopId))).thenReturn(digestInfoItemDTO);
            DigestInfoDTO result = digestQueryWrapper.getSafeDentureTags(futureMock, mtShopId);
            assertNotNull(result);
            assertEquals(1920, result.getVideoWidth());
            assertEquals("http://example.com/video.mp4", result.getVideoUrl());
            assertEquals(123, result.getDpDealId());
            assertEquals("http://example.com/frame.jpg", result.getVideoFrameUrl());
            assertEquals(1048576, result.getVideoSize());
            assertEquals(1080, result.getVideoHeight());
        }
    }

    @Test
    public void testGetRoomVRFutureDealGroupIsNull() {
        DigestQueryWrapper digestQueryWrapper = new DigestQueryWrapper();
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.getDealGroupDTO()).thenReturn(null);

        Future result = digestQueryWrapper.getRoomVRFuture(ctx);

        assertNull(result);
    }

    @Test
    public void testGetRoomVRFutureRoomIdIsBlank() {
        DigestQueryWrapper digestQueryWrapper = new DigestQueryWrapper();
        DealCtx ctx = mock(DealCtx.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        try (MockedStatic<AttributeUtils> mockedStatic = Mockito.mockStatic(AttributeUtils.class)) {
            mockedStatic.when(() -> AttributeUtils.getFirstValueV2(dealGroupDTO.getAttrs(), DealAttrKeys.CONFINEMENT_ROOM_ID)).thenReturn("");

            Future result = digestQueryWrapper.getRoomVRFuture(ctx);

            assertNull(result);
        }
    }


    @Test
    public void testGetVRInfoFutureIsNull() {
        DigestQueryWrapper digestQueryWrapper = new DigestQueryWrapper();
        Future future = null;
        DealCtx ctx = mock(DealCtx.class);

        DigestConfinementVrDTO result = digestQueryWrapper.getVRInfo(future, ctx);

        assertNull(result);
    }

    @Test
    public void testGetVRInfoDataIsBlank() throws ExecutionException, InterruptedException {
        DigestQueryWrapper digestQueryWrapper = new DigestQueryWrapper();
        Future futureMock = mock(Future.class);
        DealCtx ctx = mock(DealCtx.class);
        QueryDigestResponseDTO responseDTO = mock(QueryDigestResponseDTO.class);
        when(futureMock.get()).thenReturn(responseDTO);
        when(responseDTO.getCode()).thenReturn(200); // Simulate success response code
        try (MockedStatic<QueryDigestResponseReader> mockedStatic = Mockito.mockStatic(QueryDigestResponseReader.class)) {
            QueryDigestResponseReader reader = mock(QueryDigestResponseReader.class);
            mockedStatic.when(() -> QueryDigestResponseReader.read(responseDTO)).thenReturn(reader);
//            when(reader.getDigestItemById(anyString(), anyString())).thenReturn(null); // Simulate no data

            DigestConfinementVrDTO result = digestQueryWrapper.getVRInfo(futureMock, ctx);

            assertNull(result);
        }
    }

    @Test
    public void testGetVRInfoNormal() throws Throwable {
        DigestQueryWrapper digestQueryWrapper = new DigestQueryWrapper();
        Future futureMock = mock(Future.class);
        DealCtx ctx = mock(DealCtx.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        String roomId = "123";
        QueryDigestResponseDTO responseDTO = mock(QueryDigestResponseDTO.class);
        when(futureMock.get()).thenReturn(responseDTO);
        when(responseDTO.getCode()).thenReturn(200); // Simulate success response code
        try (MockedStatic<AttributeUtils> attributeUtilsMockedStatic = Mockito.mockStatic(AttributeUtils.class)) {
            attributeUtilsMockedStatic.when(() -> AttributeUtils.getFirstValueV2(dealGroupDTO.getAttrs(), DealAttrKeys.CONFINEMENT_ROOM_ID)).thenReturn(roomId);
            try (MockedStatic<QueryDigestResponseReader> mockedStatic = Mockito.mockStatic(QueryDigestResponseReader.class)) {
                QueryDigestResponseReader reader = mock(QueryDigestResponseReader.class);
                mockedStatic.when(() -> QueryDigestResponseReader.read(responseDTO)).thenReturn(reader);
                DigestInfoItemDTO digestInfoItemDTO = new DigestInfoItemDTO();
                String jsonData = "{\"vrUrl\":\"http://example.com/vr.mp4\"}";
                digestInfoItemDTO.setData(jsonData);
                when(reader.getDigestItemById("76_1", roomId)).thenReturn(digestInfoItemDTO);

                DigestConfinementVrDTO result = digestQueryWrapper.getVRInfo(futureMock, ctx);

                assertNotNull(result);
                assertEquals("http://example.com/vr.mp4", result.getVrUrl());
            }
        }
    }

    @Test
    public void testGetExhibitInfoFuture() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        Mockito.when(digestQueryService.queryDigest(Mockito.any())).thenReturn(null);
        QueryDigestRequestDTO queryDigestRequestDTO = new QueryDigestRequestDTO();
        digestRequestDTOMockedStatic.when(() -> QueryDigestRequestDTO.build(anyString(), Mockito.anyList())).thenReturn(queryDigestRequestDTO);
        Future result = digestQueryWrapper.getExhibitInfoFuture(ctx, Lists.newArrayList("1"));
        assertNull(result);
        Future result1 = digestQueryWrapper.getExhibitInfoFuture(ctx, null);
        assertNull(result1);
    }
}
