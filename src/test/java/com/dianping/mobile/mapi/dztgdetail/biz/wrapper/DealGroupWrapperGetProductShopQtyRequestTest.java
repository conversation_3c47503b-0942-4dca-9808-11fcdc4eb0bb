package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.general.unified.search.api.productshopsearch.dto.option.BaseSearchOption;
import com.dianping.general.unified.search.api.productshopsearch.dto.option.CountOption;
import com.dianping.general.unified.search.api.productshopsearch.dto.unit.CountUnit;
import com.dianping.general.unified.search.api.productshopsearch.enums.ProductBizTypeEnum;
import com.dianping.general.unified.search.api.productshopsearch.enums.ProductShopCountTypeEnum;
import com.dianping.general.unified.search.api.productshopsearch.enums.ProductShopGroupByFieldEnum;
import com.dianping.general.unified.search.api.productshopsearch.enums.ProductShopSearchIdTypeEnum;
import com.dianping.general.unified.search.api.productshopsearch.request.GeneralProductShopCountRequest;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import java.lang.reflect.Method;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealGroupWrapperGetProductShopQtyRequestTest {

    private DealGroupWrapper dealGroupWrapper;

    private GeneralProductShopCountRequest invokePrivateMethod(long dealGroupId, boolean mt) throws Exception {
        Method method = DealGroupWrapper.class.getDeclaredMethod("getProductShopQtyRequest", long.class, boolean.class);
        method.setAccessible(true);
        return (GeneralProductShopCountRequest) method.invoke(new DealGroupWrapper(), dealGroupId, mt);
    }

    @Test
    public void testGetProductShopQtyRequest_MtTrue_NewCountType() throws Throwable {
        long dealGroupId = 12345L;
        GeneralProductShopCountRequest request = invokePrivateMethod(dealGroupId, true);
        assertNotNull(request);
        assertEquals(ProductShopSearchIdTypeEnum.MT_PP, request.getIdPlatform());
        BaseSearchOption searchOption = request.getBaseSearchOption();
        assertEquals(Collections.singletonList(ProductBizTypeEnum.DEALGROUP), searchOption.getProductBizTypes());
        assertEquals(Collections.singletonList(dealGroupId), searchOption.getProductIds());
        assertTrue(searchOption.getShopCanDisplay());
        CountOption countOption = request.getCountOption();
        assertEquals(1, countOption.getCountUnits().size());
        CountUnit countUnit = countOption.getCountUnits().get(0);
        assertEquals("productShopQty", countUnit.getCountKey());
        assertEquals(ProductShopGroupByFieldEnum.PRODUCT_ID.getCode(), countUnit.getGroupByFields().iterator().next());
        // Without mocking Lion, we can only verify the default behavior
        // which is DISTINCT_COUNT when Lion throws exception
        assertEquals(ProductShopCountTypeEnum.DISTINCT_COUNT, countUnit.getCountType());
    }

    @Test
    public void testGetProductShopQtyRequest_MtFalse_OldCountType() throws Throwable {
        long dealGroupId = 67890L;
        GeneralProductShopCountRequest request = invokePrivateMethod(dealGroupId, false);
        assertNotNull(request);
        assertEquals(ProductShopSearchIdTypeEnum.DP_BP, request.getIdPlatform());
        BaseSearchOption searchOption = request.getBaseSearchOption();
        assertEquals(Collections.singletonList(ProductBizTypeEnum.DEALGROUP), searchOption.getProductBizTypes());
        assertEquals(Collections.singletonList(dealGroupId), searchOption.getProductIds());
        assertTrue(searchOption.getShopCanDisplay());
        CountOption countOption = request.getCountOption();
        assertEquals(1, countOption.getCountUnits().size());
        CountUnit countUnit = countOption.getCountUnits().get(0);
        assertEquals("productShopQty", countUnit.getCountKey());
        assertEquals(ProductShopGroupByFieldEnum.PRODUCT_ID.getCode(), countUnit.getGroupByFields().iterator().next());
        assertEquals(ProductShopCountTypeEnum.DISTINCT_COUNT, countUnit.getCountType());
    }

    @Test
    public void testGetProductShopQtyRequest_ZeroDealGroupId() throws Throwable {
        long dealGroupId = 0L;
        GeneralProductShopCountRequest request = invokePrivateMethod(dealGroupId, true);
        assertNotNull(request);
        assertEquals(Collections.singletonList(0L), request.getBaseSearchOption().getProductIds());
    }

    @Test
    public void testGetProductShopQtyRequest_NegativeDealGroupId() throws Throwable {
        long dealGroupId = -12345L;
        GeneralProductShopCountRequest request = invokePrivateMethod(dealGroupId, false);
        assertNotNull(request);
        assertEquals(Collections.singletonList(-12345L), request.getBaseSearchOption().getProductIds());
    }

    @Test
    public void testGetProductShopQtyRequest_LionException() throws Throwable {
        long dealGroupId = 12345L;
        GeneralProductShopCountRequest request = invokePrivateMethod(dealGroupId, true);
        assertNotNull(request);
        CountUnit countUnit = request.getCountOption().getCountUnits().get(0);
        // Without mocking Lion to throw exception, we can only verify the default behavior
        assertEquals(ProductShopCountTypeEnum.DISTINCT_COUNT, countUnit.getCountType());
    }
}
