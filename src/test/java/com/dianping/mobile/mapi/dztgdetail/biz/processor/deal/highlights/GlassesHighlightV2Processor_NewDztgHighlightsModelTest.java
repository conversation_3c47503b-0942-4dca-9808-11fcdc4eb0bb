package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CommonAttrVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.highlights.DztgHighlightsModule;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.List;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class GlassesHighlightV2Processor_NewDztgHighlightsModelTest {

    private GlassesHighlightV2Processor glassesHighlightV2Processor;

    @Before
    public void setUp() {
        glassesHighlightV2Processor = new GlassesHighlightV2Processor();
    }

    /**
     * 测试 newDztgHighlightsModel 方法
     */
    @Test
    public void testNewDztgHighlightsModel() throws Throwable {
        // act
        DztgHighlightsModule result = glassesHighlightV2Processor.newDztgHighlightsModel();
        // assert
        assertNotNull(result);
        assertEquals("struct", result.getStyle());
        assertNotNull(result.getAttrs());
        assertTrue(result.getAttrs().isEmpty());
    }
}
