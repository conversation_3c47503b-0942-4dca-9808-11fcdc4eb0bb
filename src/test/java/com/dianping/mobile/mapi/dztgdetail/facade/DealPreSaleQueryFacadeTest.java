package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.account.utils.util.LionConfigUtils;
import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.gmkt.activity.api.dto.BatchQueryDealActivityRequest;
import com.dianping.gmkt.activity.api.dto.BatchQueryDealActivityResponse;
import com.dianping.gmkt.activity.api.enums.RequestSource;
import com.dianping.gmkt.activity.enums.AppPlatform;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealActivityWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.PreSaleCountDownReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CountDownPBO;
import com.dianping.mobile.mapi.dztgdetail.exception.QueryCenterResultException;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealPreSaleQueryFacadeTest {

    @InjectMocks
    private DealPreSaleQueryFacade dealPreSaleQueryFacade;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private Future future;

    @Mock
    private PreSaleCountDownReq request;

    @Mock
    private EnvCtx ctx;

    @Mock
    private DealGroupQueryService queryCenterDealGroupQueryService;

    @Mock
    private DealActivityWrapper dealActivityWrapper;

    @Mock
    private LionWrapper lionWrapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private boolean invokeIsPreSale(Future future) throws Exception {
        Method method = DealPreSaleQueryFacade.class.getDeclaredMethod("isPreSale", Future.class);
        method.setAccessible(true);
        return (boolean) method.invoke(dealPreSaleQueryFacade, future);
    }

    private BatchQueryDealActivityRequest invokePrivateMethod(String methodName, Object... args) throws Exception {
        Method method = DealPreSaleQueryFacade.class.getDeclaredMethod(methodName, PreSaleCountDownReq.class, EnvCtx.class);
        method.setAccessible(true);
        return (BatchQueryDealActivityRequest) method.invoke(dealPreSaleQueryFacade, args);
    }

    // Utility method to invoke private methods using reflection
    private boolean invokePrivateMethod(Object object, String methodName) throws Exception {
        Method method = object.getClass().getDeclaredMethod(methodName);
        method.setAccessible(true);
        return (boolean) method.invoke(object);
    }

    @Test
    public void testIsPreSaleFutureResultIsNull() throws Throwable {
        when(dealGroupWrapper.getFutureResult(future)).thenReturn(null);
        assertFalse(invokeIsPreSale(future));
    }

    @Test
    public void testIsPreSaleAttributeListIsEmpty() throws Throwable {
        when(dealGroupWrapper.getFutureResult(future)).thenReturn(Collections.emptyList());
        assertFalse(invokeIsPreSale(future));
    }

    @Test
    public void testIsPreSaleAttributeListIsNotEmptyButNoPreSaleTag() throws Throwable {
        when(dealGroupWrapper.getFutureResult(future)).thenReturn(Collections.singletonList(new AttributeDTO()));
        assertFalse(invokeIsPreSale(future));
    }

    @Test
    public void testIsPreSaleAttributeListIsNotEmptyAndHasPreSaleTag() throws Throwable {
        AttributeDTO attributeDTO = new AttributeDTO();
        // Ensure the correct key is used
        attributeDTO.setName(DealAttrKeys.PRE_SALE_TAG);
        attributeDTO.setValue(Collections.singletonList("true"));
        when(dealGroupWrapper.getFutureResult(future)).thenReturn(Collections.singletonList(attributeDTO));
        assertTrue(invokeIsPreSale(future));
    }

    @Test
    public void testIsPreSaleFutureIsNull() throws Throwable {
        assertFalse(invokeIsPreSale(null));
    }

    /**
     * Tests the buildBatchQueryDealActivityReq method when EnvCtx.isMt() returns true.
     */
    @Test
    public void testBuildBatchQueryDealActivityReqWhenIsMtIsTrue() throws Throwable {
        // Arrange
        when(ctx.isMt()).thenReturn(true);
        when(request.getDealgroupid()).thenReturn(123L);
        when(request.getCityid()).thenReturn(456);
        // Correctly mock ctx.getUserId() to return mtUserId
        when(ctx.getMtUserId()).thenReturn(456L);
        // Act
        BatchQueryDealActivityRequest result = invokePrivateMethod("buildBatchQueryDealActivityReq", request, ctx);
        // Assert
        assertNotNull(result);
        assertEquals(AppPlatform.MT, result.getAppPlatform());
        assertEquals(RequestSource.TuanDetail, result.getSource());
        assertEquals(Long.valueOf(456), result.getUserIdL());
    }

    /**
     * Tests the buildBatchQueryDealActivityReq method when EnvCtx.isMt() returns false.
     */
    @Test
    public void testBuildBatchQueryDealActivityReqWhenIsMtIsFalse() throws Throwable {
        // Arrange
        when(ctx.isMt()).thenReturn(false);
        when(request.getDealgroupid()).thenReturn(123L);
        when(request.getCityid()).thenReturn(456);
        // Correctly mock ctx.getUserId() to return dpUserId
        when(ctx.getDpUserId()).thenReturn(123L);
        // Act
        BatchQueryDealActivityRequest result = invokePrivateMethod("buildBatchQueryDealActivityReq", request, ctx);
        // Assert
        assertNotNull(result);
        assertEquals(AppPlatform.DP, result.getAppPlatform());
        assertEquals(RequestSource.TuanDetail, result.getSource());
        assertEquals(Long.valueOf(123), result.getUserIdL());
    }

    /**
     * Test scenario where the query response is null.
     */
    @Test(expected = QueryCenterResultException.class)
    public void testQueryPreSaleCountDownFromQueryCenter_QueryResponseIsNull() throws Throwable {
        // arrange
        PreSaleCountDownReq request = new PreSaleCountDownReq();
        request.setDealgroupid(12345L);
        EnvCtx ctx = new EnvCtx();
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(null);
        // act
        dealPreSaleQueryFacade.queryPreSaleCountDownFromQueryCenter(request, ctx);
        // assert
        // Exception is expected
    }

    /**
     * Test scenario where the query response code is not 0.
     */
    @Test(expected = QueryCenterResultException.class)
    public void testQueryPreSaleCountDownFromQueryCenter_QueryResponseCodeNotZero() throws Throwable {
        // arrange
        PreSaleCountDownReq request = new PreSaleCountDownReq();
        request.setDealgroupid(12345L);
        EnvCtx ctx = new EnvCtx();
        QueryDealGroupListResponse queryDealGroupListResponse = new QueryDealGroupListResponse();
        queryDealGroupListResponse.setCode(1);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(queryDealGroupListResponse);
        // act
        dealPreSaleQueryFacade.queryPreSaleCountDownFromQueryCenter(request, ctx);
        // assert
        // Exception is expected
    }

    /**
     * Test scenario where the deal group list is empty.
     */
    @Test(expected = QueryCenterResultException.class)
    public void testQueryPreSaleCountDownFromQueryCenter_DealGroupListIsEmpty() throws Throwable {
        // arrange
        PreSaleCountDownReq request = new PreSaleCountDownReq();
        request.setDealgroupid(12345L);
        EnvCtx ctx = new EnvCtx();
        QueryDealGroupListResponse queryDealGroupListResponse = new QueryDealGroupListResponse();
        queryDealGroupListResponse.setCode(0);
        QueryDealGroupListResult result = new QueryDealGroupListResult();
        result.setList(Collections.emptyList());
        queryDealGroupListResponse.setData(result);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(queryDealGroupListResponse);
        // act
        dealPreSaleQueryFacade.queryPreSaleCountDownFromQueryCenter(request, ctx);
        // assert
        // Exception is expected
    }

    /**
     * Test scenario where the first element in the deal group list is null.
     */
    @Test(expected = QueryCenterResultException.class)
    public void testQueryPreSaleCountDownFromQueryCenter_DealGroupListFirstElementIsNull() throws Throwable {
        // arrange
        PreSaleCountDownReq request = new PreSaleCountDownReq();
        request.setDealgroupid(12345L);
        EnvCtx ctx = new EnvCtx();
        QueryDealGroupListResponse queryDealGroupListResponse = new QueryDealGroupListResponse();
        queryDealGroupListResponse.setCode(0);
        QueryDealGroupListResult result = new QueryDealGroupListResult();
        result.setList(Collections.singletonList(null));
        queryDealGroupListResponse.setData(result);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(queryDealGroupListResponse);
        // act
        dealPreSaleQueryFacade.queryPreSaleCountDownFromQueryCenter(request, ctx);
        // assert
        // Exception is expected
    }

    // Wrapper class for Lion to allow mocking
    private static class LionWrapper {

        public boolean getBooleanValue(String key, boolean defaultValue) {
            return Lion.getBooleanValue(key, defaultValue);
        }
    }

    /**
     * 测试 activityEnable 方法，当 Lion.getBooleanValue 返回 true 时
     */
    @Test
    public void testActivityEnableWhenLionGetBooleanValueReturnsTrue() throws Throwable {
        // arrange
        // act
        boolean result = invokePrivateMethod(dealPreSaleQueryFacade, "activityEnable");
        // assert
        assertTrue(result);
    }
}
