package com.dianping.mobile.mapi.dztgdetail.biz.processor;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.NavBarSearchWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiShopCategoryWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.sankuai.dzviewscene.sug.merger.service.enums.PlatformEnum;
import com.sankuai.dzviewscene.sug.merger.service.request.SugMergerRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.NavBarSearchModuleVO;
import java.util.List;
import org.junit.Test;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class NavBarSearchProcessorTest {

    @InjectMocks
    private NavBarSearchProcessor processor;

    @Mock
    private NavBarSearchWrapper navBarSearchWrapper;

    @Mock
    private PoiShopCategoryWrapper poiShopCategoryWrapper;

    @Mock
    private DouHuService douHuService;

    private ModuleAbConfig createModuleAbConfig(String expResult) {
        ModuleAbConfig config = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult(expResult);
        config.setConfigs(Collections.singletonList(abConfig));
        return config;
    }

    /**
     * Test case: Search bar should not be shown when AB test result is null
     */
    @Test
    public void testPrepare_WhenSearchBarNotShown() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        when(douHuService.getNavbarSearchAbTestResult(any())).thenReturn(null);
        // act
        processor.prepare(ctx);
        // assert
        verify(navBarSearchWrapper, never()).prepareNaviSearch(any());
    }

    /**
     * Test case: Search bar should not be shown when AB test result doesn't match expected values
     */
    @Test
    public void testPrepare_WhenAbConfigDoesNotMatchExpectedValues() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ModuleAbConfig config = createModuleAbConfig("a");
        when(douHuService.getNavbarSearchAbTestResult(any())).thenReturn(config);
        // act
        processor.prepare(ctx);
        // assert
        verify(navBarSearchWrapper, never()).prepareNaviSearch(any());
    }

    /**
     * Test case: When shop category IDs are empty
     */
    @Test
    public void testPrepare_WhenShopCategoryIdsEmpty() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setMtLongShopId(123L);
        ModuleAbConfig config = createModuleAbConfig("c");
        when(douHuService.getNavbarSearchAbTestResult(any())).thenReturn(config);
        // act
        processor.prepare(ctx);
        // assert
        verify(navBarSearchWrapper, never()).prepareNaviSearch(any());
    }

    /**
     * Test case: Exception handling during preparation
     */
    @Test
    public void testPrepare_ExceptionHandling() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setMtLongShopId(123L);
        ModuleAbConfig config = createModuleAbConfig("c");
        when(douHuService.getNavbarSearchAbTestResult(any())).thenReturn(config);
        // act
        processor.prepare(ctx);
        // assert
        verify(navBarSearchWrapper, never()).prepareNaviSearch(any());
    }

    /**
     * Test case: Invalid shop ID scenario
     */
    @Test
    public void testPrepare_InvalidShopId() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setMtLongShopId(0L);
        ModuleAbConfig config = createModuleAbConfig("c");
        when(douHuService.getNavbarSearchAbTestResult(any())).thenReturn(config);
        // act
        processor.prepare(ctx);
        // assert
        verify(navBarSearchWrapper, never()).prepareNaviSearch(any());
    }

    @Test
    public void testProcess_WhenNavBarSearchModuleVOIsNull() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        when(navBarSearchWrapper.queryNaviSearch(ctx)).thenReturn(null);
        // act
        processor.process(ctx);
        // assert
        assertNull(ctx.getNavBarSearchModuleVO());
        verify(douHuService, never()).getNavbarSearchAbTestResult(any());
    }

    @Test
    public void testProcess_WhenShowFixTextIsTrue() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        NavBarSearchModuleVO moduleVO = new NavBarSearchModuleVO();
        ModuleAbConfig abConfig = new ModuleAbConfig();
        List<AbConfig> configs = new ArrayList<>();
        AbConfig config = new AbConfig();
        config.setExpResult("d");
        configs.add(config);
        abConfig.setConfigs(configs);
        when(navBarSearchWrapper.queryNaviSearch(ctx)).thenReturn(moduleVO);
        when(douHuService.getNavbarSearchAbTestResult(any())).thenReturn(abConfig);
        // act
        processor.process(ctx);
        // assert
        assertNotNull(ctx.getNavBarSearchModuleVO());
        assertEquals("搜索商家、品类或商圈", ctx.getNavBarSearchModuleVO().getText());
        assertTrue(ctx.getNavBarSearchModuleVO().getJumpUrl().contains("imeituan://www.meituan.com/mrn"));
    }

    @Test
    public void testProcess_WhenTextAndJumpUrlAreBlank() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        NavBarSearchModuleVO moduleVO = new NavBarSearchModuleVO();
        moduleVO.setText("");
        moduleVO.setJumpUrl("");
        ModuleAbConfig abConfig = new ModuleAbConfig();
        abConfig.setConfigs(Collections.emptyList());
        when(navBarSearchWrapper.queryNaviSearch(ctx)).thenReturn(moduleVO);
        when(douHuService.getNavbarSearchAbTestResult(any())).thenReturn(abConfig);
        // act
        processor.process(ctx);
        // assert
        assertNotNull(ctx.getNavBarSearchModuleVO());
        assertEquals("搜索商家、品类或商圈", ctx.getNavBarSearchModuleVO().getText());
        assertTrue(ctx.getNavBarSearchModuleVO().getJumpUrl().contains("imeituan://www.meituan.com/mrn"));
    }

    @Test
    public void testProcess_WhenTextAndJumpUrlExist() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        NavBarSearchModuleVO moduleVO = new NavBarSearchModuleVO();
        moduleVO.setText("Original Text");
        moduleVO.setJumpUrl("Original URL");
        ModuleAbConfig abConfig = new ModuleAbConfig();
        abConfig.setConfigs(Collections.emptyList());
        when(navBarSearchWrapper.queryNaviSearch(ctx)).thenReturn(moduleVO);
        when(douHuService.getNavbarSearchAbTestResult(any())).thenReturn(abConfig);
        // act
        processor.process(ctx);
        // assert
        assertNotNull(ctx.getNavBarSearchModuleVO());
        assertEquals("Original Text", ctx.getNavBarSearchModuleVO().getText());
        assertEquals("Original URL", ctx.getNavBarSearchModuleVO().getJumpUrl());
    }

    @Test
    public void testProcess_WhenTextExistsButJumpUrlIsBlankAndShowFixTextFalse() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        NavBarSearchModuleVO moduleVO = new NavBarSearchModuleVO();
        moduleVO.setText("Original Text");
        moduleVO.setJumpUrl("");
        ModuleAbConfig abConfig = new ModuleAbConfig();
        abConfig.setConfigs(Collections.emptyList());
        when(navBarSearchWrapper.queryNaviSearch(ctx)).thenReturn(moduleVO);
        when(douHuService.getNavbarSearchAbTestResult(any())).thenReturn(abConfig);
        // act
        processor.process(ctx);
        // assert
        assertNotNull(ctx.getNavBarSearchModuleVO());
        assertEquals("Original Text", ctx.getNavBarSearchModuleVO().getText());
        assertEquals("", ctx.getNavBarSearchModuleVO().getJumpUrl());
    }

    @Test
    public void testProcess_WhenModuleAbConfigIsNull() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        NavBarSearchModuleVO moduleVO = new NavBarSearchModuleVO();
        moduleVO.setText("Original Text");
        moduleVO.setJumpUrl("Original URL");
        when(navBarSearchWrapper.queryNaviSearch(ctx)).thenReturn(moduleVO);
        when(douHuService.getNavbarSearchAbTestResult(any())).thenReturn(null);
        // act
        processor.process(ctx);
        // assert
        assertNotNull(ctx.getNavBarSearchModuleVO());
        assertEquals("Original Text", ctx.getNavBarSearchModuleVO().getText());
        assertEquals("Original URL", ctx.getNavBarSearchModuleVO().getJumpUrl());
    }
}
