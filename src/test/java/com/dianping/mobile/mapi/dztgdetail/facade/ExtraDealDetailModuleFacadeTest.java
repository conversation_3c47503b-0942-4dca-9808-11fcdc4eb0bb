package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.deal.shop.dto.BestShopReq;
import com.dianping.lion.client.Lion;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.ExtraDealDetailModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.DigestInfoDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.ExtraDealDetailModuleResponse;
import com.dianping.mobile.mapi.dztgdetail.entity.ExtraDealDetailModuleConfig;
import com.dianping.mobile.mapi.dztgdetail.util.OralDealUtils;
import com.google.common.collect.Maps;
import com.meituan.mdp.boot.starter.util.MdpEnvUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDisplayShopDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.GuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.MedicalConstant.*;
import static com.dianping.mobile.mapi.dztgdetail.util.OralDealUtils.getSafeTreatZdcId;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ExtraDealDetailModuleFacadeTest {

    @InjectMocks
    private ExtraDealDetailModuleFacade extraDealDetailModuleFacade;

    @Mock
    private PoiShopCategoryWrapper poiShopCategoryWrapper;
    @Mock
    private PoiClientWrapper poiClientWrapper;
    @Mock
    private DealGroupWrapper dealGroupWrapper;
    @Mock
    private ShopTagWrapper shopTagWrapper;
    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Mock
    MapperWrapper mapperWrapper;

    @Mock
    DigestQueryWrapper digestQueryWrapper;

    @Mock
    GuaranteeQueryWrapper guaranteeQueryWrapper;

    @Mock
    private DouHuService douHuService;
    private static final Long SAFE_PTOMETRY_TAGID_TEST = 7405L;

    private List<DisplayTagDto> displayTagDtos;

    private Map<Long, List<DisplayTagDto>> dpShopId2TagsMap;

    private List<DealGroupTagDTO> tags;

    private MockedStatic<MdpEnvUtils> mdpEnvUtilsMockedStatic;
    private MockedStatic<Lion> lionMockedStatic;
    @Before
    public void setUp() {
        dpShopId2TagsMap = new HashMap<>();
        displayTagDtos = new ArrayList<>();
        tags = new ArrayList<>();
        mdpEnvUtilsMockedStatic = mockStatic(MdpEnvUtils.class);
        lionMockedStatic = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        mdpEnvUtilsMockedStatic.close();
        lionMockedStatic.close();
    }

    /**
     * 测试场景：当找到的最佳门店ID为null时，应返回null。
     */
    @Test
    public void testGetExtraDealDetailModuleResponse_BestShopIdIsNull() throws Throwable {
        // arrange
        ExtraDealDetailModuleReq req = mock(ExtraDealDetailModuleReq.class);
        EnvCtx ctx = mock(EnvCtx.class);
        when(dealGroupWrapper.preDealGroupBestShop(any(BestShopReq.class))).thenReturn(mock(Future.class));
        when(req.getDealGroupId()).thenReturn("123");
        when(req.getShopId()).thenReturn("123");
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDisplayShopInfo(new DealGroupDisplayShopDTO());
        dealGroupDTO.getDisplayShopInfo().setDpDisplayShopIds(Lists.newArrayList(123L));
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(dealGroupDTO);
        // act
        ExtraDealDetailModuleResponse result = extraDealDetailModuleFacade.getExtraDealDetailModuleResponse(req, ctx);

        // assert
        assertNull(result);
    }

    /**
     * 测试场景：当找到的最佳门店ID不为null，但后续处理返回null时，应返回null。
     */
    @Test
    public void testGetExtraDealDetailModuleResponse_ProcessingReturnsNull() throws Throwable {
        // arrange
        ExtraDealDetailModuleReq req = mock(ExtraDealDetailModuleReq.class);
        EnvCtx ctx = mock(EnvCtx.class);
        BestShopDTO bestShopDTO = new BestShopDTO();
        bestShopDTO.setDpShopId(123L);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDisplayShopInfo(new DealGroupDisplayShopDTO());
        dealGroupDTO.getDisplayShopInfo().setDpDisplayShopIds(Lists.newArrayList(123L));

        when(req.getDealGroupId()).thenReturn("123");
        when(req.getShopId()).thenReturn("123");
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(dealGroupDTO);
        when(dealGroupWrapper.preDealGroupBestShop(any(BestShopReq.class))).thenReturn(mock(Future.class));
        when(poiClientWrapper.getDpPoiDTO(anyLong(), anyList())).thenReturn(null);

        // act
        ExtraDealDetailModuleResponse result = extraDealDetailModuleFacade.getExtraDealDetailModuleResponse(req, ctx);

        // assert
        assertNull(result);
    }

    /**
     * 测试场景：当找到的最佳门店ID不为null，并且后续处理也成功时，应返回非null。
     */
    @Test
    public void testGetExtraDealDetailModuleResponse_Success() throws Throwable {
        // arrange
        ExtraDealDetailModuleReq req = mock(ExtraDealDetailModuleReq.class);

        EnvCtx ctx = mock(EnvCtx.class);
        BestShopDTO bestShopDTO = new BestShopDTO();
        bestShopDTO.setDpShopId(123L);
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setShopId(123L);
        DisplayTagDto displayTagDto = new DisplayTagDto();
        displayTagDto.setTagId(SAFE_PTOMETRY_TAGID_TEST);

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDisplayShopInfo(new DealGroupDisplayShopDTO());
        dealGroupDTO.getDisplayShopInfo().setDpDisplayShopIds(Lists.newArrayList(123L));

        DisplayTagDto tagDto = new DisplayTagDto();
        tagDto.setTagId(SAFE_PTOMETRY_TAGID_TEST);
        displayTagDtos.add(tagDto);
        dpShopId2TagsMap.put(123L, displayTagDtos);


        when(req.getDealGroupId()).thenReturn("123");
        when(req.getShopId()).thenReturn("123");
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(dealGroupDTO);
        when(dealGroupWrapper.preDealGroupBestShop(any(BestShopReq.class))).thenReturn(mock(Future.class));
        when(poiClientWrapper.getDpPoiDTO(anyLong(), anyList())).thenReturn(dpPoiDTO);
        when(poiShopCategoryWrapper.getBackSecondMainCategory(any(DpPoiDTO.class))).thenReturn(155);
        when(shopTagWrapper.preGetDpShopTags(anyLong())).thenReturn(mock(Future.class));
        when(shopTagWrapper.getShopId2TagsMap(any())).thenReturn(dpShopId2TagsMap);
        when(douHuService.getGlassDealDetailExpResult(anyInt(),any(EnvCtx.class))).thenReturn("b");
        mdpEnvUtilsMockedStatic.when(MdpEnvUtils::isTestEnv).thenReturn(true);
        ExtraDealDetailModuleConfig extraDealDetailModuleConfig = new ExtraDealDetailModuleConfig();
        List<ExtraDealDetailModuleConfig.AttrListConfig> attrList = new ArrayList<>();
        ExtraDealDetailModuleConfig.AttrListConfig attrListConfig = new ExtraDealDetailModuleConfig.AttrListConfig();
        attrListConfig.setPic("pic");
        attrList.add(attrListConfig);
        extraDealDetailModuleConfig.setAttrList(attrList);
        Map<String, ExtraDealDetailModuleConfig> map = Maps.newHashMap();
        map.put("155", extraDealDetailModuleConfig);
        lionMockedStatic.when(() -> Lion.getMap(anyString(), anyString(), any()))
                .thenReturn(map);

        // act
        ExtraDealDetailModuleResponse result = extraDealDetailModuleFacade.getExtraDealDetailModuleResponse(req, ctx);

        // assert
        assertNotNull(result);
    }

    /**
     * 测试场景：齿科当找到的最佳门店ID不为null，并且后续处理也成功时，应返回非null。
     */
    @Test
    public void testTeethGetExtraDealDetailModuleResponse_Success() throws Throwable {
        // arrange
        ExtraDealDetailModuleReq req = mock(ExtraDealDetailModuleReq.class);

        EnvCtx ctx = new EnvCtx();
        BestShopDTO bestShopDTO = new BestShopDTO();
        bestShopDTO.setDpShopId(123L);
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setShopId(123L);
        DisplayTagDto displayTagDto = new DisplayTagDto();
        displayTagDto.setTagId(7440L);

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDisplayShopInfo(new DealGroupDisplayShopDTO());
        dealGroupDTO.getDisplayShopInfo().setDpDisplayShopIds(Lists.newArrayList(123L));
        dealGroupDTO.setTags(tags);

        DisplayTagDto tagDto = new DisplayTagDto();
        tagDto.setTagId(7440L);
        displayTagDtos.add(tagDto);
        dpShopId2TagsMap.put(123L, displayTagDtos);



        DigestInfoDTO safeTreatDigest = new DigestInfoDTO();
        safeTreatDigest.setImplantQualityGuaranteeDurationType(1);
        safeTreatDigest.setImplantQualityGuaranteeDuration(1);
        safeTreatDigest.setMaterialShedFreeRefillDurationType(1);
        safeTreatDigest.setMaterialShedFreeRefillDuration(1);
        safeTreatDigest.setPostoperativeFreeReviewServiceDurationType(1);
        safeTreatDigest.setPostoperativeFreeReviewServiceDuration(1);
        safeTreatDigest.setPostoperativeFreeReviewServiceCountType(1);
        safeTreatDigest.setPostoperativeFreeReviewServiceCount(1);
        safeTreatDigest.setShedFreeReplantGuaranteeDurationType(1);
        safeTreatDigest.setShedFreeReplantGuaranteeDuration(1);

        ObjectGuaranteeTagDTO objectGuaranteeTagDTO = new ObjectGuaranteeTagDTO();
        objectGuaranteeTagDTO.setGuaranteeType(3);
        GuaranteeTagDTO guaranteeTagDTO = new GuaranteeTagDTO();
        guaranteeTagDTO.setGuaranteeTagNames(Lists.newArrayList(1));
        objectGuaranteeTagDTO.setGuaranteeTag(guaranteeTagDTO);


       MockedStatic<OralDealUtils> oralDealUtilsMockedStatic = Mockito.mockStatic(OralDealUtils.class);
        when(OralDealUtils.getSafeTreatZdcId(any())).thenReturn(7440L);
        when(digestQueryWrapper.getSafeImplantFuture(anyLong())).thenReturn(mock(Future.class));
        when(digestQueryWrapper.getSafeImplantTags(any(),anyLong())).thenReturn(safeTreatDigest);

        when(guaranteeQueryWrapper.preGetGuaranteeTagDTOs(any())).thenReturn(mock(Future.class));
        when(guaranteeQueryWrapper.getGuaranteeTagDTOs(any())).thenReturn(Lists.newArrayList(objectGuaranteeTagDTO));

        when(req.getDealGroupId()).thenReturn("123");
        when(req.getShopId()).thenReturn("123");
        when(mapperWrapper.getMtShopIdByDpShopIdLong(anyLong())).thenReturn(123L);
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(dealGroupDTO);
        when(dealGroupWrapper.preDealGroupBestShop(any(BestShopReq.class))).thenReturn(mock(Future.class));
        when(poiClientWrapper.getDpPoiDTO(anyLong(), anyList())).thenReturn(dpPoiDTO);
        when(poiShopCategoryWrapper.getBackSecondMainCategory(any(DpPoiDTO.class))).thenReturn(46);
        when(shopTagWrapper.preGetDpShopTags(anyLong())).thenReturn(mock(Future.class));
        when(shopTagWrapper.getShopId2TagsMap(any())).thenReturn(dpShopId2TagsMap);
        mdpEnvUtilsMockedStatic.when(MdpEnvUtils::isTestEnv).thenReturn(true);
        ExtraDealDetailModuleConfig extraDealDetailModuleConfig = new ExtraDealDetailModuleConfig();
        List<ExtraDealDetailModuleConfig.AttrListConfig> attrList = new ArrayList<>();
        ExtraDealDetailModuleConfig.AttrListConfig attrListConfig = new ExtraDealDetailModuleConfig.AttrListConfig();
        attrListConfig.setPic("pic");
        attrList.add(attrListConfig);
        extraDealDetailModuleConfig.setAttrList(attrList);
        Map<String, ExtraDealDetailModuleConfig> map = Maps.newHashMap();
        map.put("46-种植牙", extraDealDetailModuleConfig);
        lionMockedStatic.when(() -> Lion.getMap(anyString(), anyString(), any()))
                .thenReturn(map);
        // act
        ExtraDealDetailModuleResponse result = extraDealDetailModuleFacade.getExtraDealDetailModuleResponse(req, ctx);

        oralDealUtilsMockedStatic.close();
        // assert
        assertNotNull(result);
    }
}