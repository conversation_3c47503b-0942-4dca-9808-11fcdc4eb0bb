package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.swan.udqs.api.QueryData;
import com.sankuai.swan.udqs.api.Result;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class ParallDealBuilderProcessor_GetUserOrderCountFromResultTest {

    private ParallDealBuilderProcessor processor;

    @Before
    public void setUp() {
        processor = new ParallDealBuilderProcessor();
    }

    @After
    public void tearDown() {
        processor = null;
    }

    /**
     * Test getUserOrderCountFromResult when result is null.
     */
    @Test
    public void testGetUserOrderCountFromResultResultIsNull() {
        Result<QueryData> result = null;
        int output = processor.getUserOrderCountFromResult(result);
        assertEquals(0, output);
    }

    /**
     * Test getUserOrderCountFromResult when result is not successful.
     */
    @Test
    public void testGetUserOrderCountFromResultResultIsNotSuccessful() {
        Result<QueryData> result = mock(Result.class);
        when(result.isIfSuccess()).thenReturn(false);
        int output = processor.getUserOrderCountFromResult(result);
        assertEquals(0, output);
    }

    /**
     * Test getUserOrderCountFromResult when result data is null.
     */
    @Test
    public void testGetUserOrderCountFromResultDataIsNull() {
        Result<QueryData> result = mock(Result.class);
        when(result.isIfSuccess()).thenReturn(true);
        when(result.getData()).thenReturn(null);
        int output = processor.getUserOrderCountFromResult(result);
        assertEquals(0, output);
    }

    /**
     * Test getUserOrderCountFromResult when resultSet is empty.
     */
    @Test
    public void testGetUserOrderCountFromResultResultSetIsEmpty() {
        Result<QueryData> result = mock(Result.class);
        QueryData queryData = mock(QueryData.class);
        when(result.isIfSuccess()).thenReturn(true);
        when(result.getData()).thenReturn(queryData);
        when(queryData.getResultSet()).thenReturn(Collections.emptyList());
        int output = processor.getUserOrderCountFromResult(result);
        assertEquals(0, output);
    }

    /**
     * Test getUserOrderCountFromResult when valueStr is blank.
     */
    @Test
    public void testGetUserOrderCountFromResultValueStrIsBlank() {
        Result<QueryData> result = mock(Result.class);
        QueryData queryData = mock(QueryData.class);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("value", "");
        when(result.isIfSuccess()).thenReturn(true);
        when(result.getData()).thenReturn(queryData);
        when(queryData.getResultSet()).thenReturn(Collections.singletonList(resultMap));
        int output = processor.getUserOrderCountFromResult(result);
        assertEquals(0, output);
    }

    /**
     * Test getUserOrderCountFromResult when userOrderCountStr is not numeric.
     */
    @Test
    public void testGetUserOrderCountFromResultUserOrderCountStrIsNotNumeric() {
        Result<QueryData> result = mock(Result.class);
        QueryData queryData = mock(QueryData.class);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("value", "{\"userordercount\":\"abc\"}");
        when(result.isIfSuccess()).thenReturn(true);
        when(result.getData()).thenReturn(queryData);
        when(queryData.getResultSet()).thenReturn(Collections.singletonList(resultMap));
        int output = processor.getUserOrderCountFromResult(result);
        assertEquals(0, output);
    }

    /**
     * Test getUserOrderCountFromResult when userOrderCountStr is numeric.
     */
    @Test
    public void testGetUserOrderCountFromResultUserOrderCountStrIsNumeric() {
        Result<QueryData> result = mock(Result.class);
        QueryData queryData = mock(QueryData.class);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("value", "{\"userordercount\":\"10\"}");
        when(result.isIfSuccess()).thenReturn(true);
        when(result.getData()).thenReturn(queryData);
        when(queryData.getResultSet()).thenReturn(Collections.singletonList(resultMap));
        int output = processor.getUserOrderCountFromResult(result);
        assertEquals(10, output);
    }
}
