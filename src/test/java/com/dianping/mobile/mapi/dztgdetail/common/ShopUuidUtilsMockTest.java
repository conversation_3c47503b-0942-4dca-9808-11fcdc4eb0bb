package com.dianping.mobile.mapi.dztgdetail.common;

import com.dianping.lion.client.Lion;
import com.dianping.shopremote.remote.ShopUuidService;
import com.dianping.shopremote.remote.dto.ShopUuidDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ShopUuidUtilsMockTest {

    @Mock
    private ShopUuidService shopUuidService;

    @Before
    public void setUp() {

    }

    /**
     * 测试配置项为true的情况
     */
    @Test
    public void testGetShopIdByUuidConfigIsTrue() {
        Long shopId = ShopUuidUtils.getShopIdByUuid("uuid");
        assertEquals(0L, shopId.longValue());
    }

    @Test
    public void test() {
        String uuid = ShopUuidUtils.getUuidById(0);
        assertEquals("", uuid);
    }

    @Test
    public void test1() {
        String uuid = ShopUuidUtils.getUuidByIdLong(0L);
        assertEquals("", uuid);
    }

}
