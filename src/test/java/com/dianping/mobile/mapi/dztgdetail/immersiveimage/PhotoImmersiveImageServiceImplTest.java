package com.dianping.mobile.mapi.dztgdetail.immersiveimage;

import com.dianping.deal.detail.enums.PlatformEnum;
import com.dianping.mobile.mapi.dztgdetail.biz.immersiveimage.impl.PhotoImmersiveImageServiceImpl;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ImmersiveImageWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageFilterRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageFilterVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.SpritePicVO;
import com.dianping.mobile.mapi.dztgdetail.entity.QueryExhibitImageFilterParam;
import com.dianping.mobile.mapi.dztgdetail.entity.QueryExhibitImageParam;
import com.sankuai.mpmctcontent.application.thrift.dto.content.DisplayItem;
import com.sankuai.mpmctcontent.application.thrift.dto.content.RatioInfoDTO;
import com.sankuai.mpmctcontent.application.thrift.dto.content.VideoSpriteDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @since 2023/10/26
 * 摄影服务测试用例
 */
@RunWith(MockitoJUnitRunner.class)
public class PhotoImmersiveImageServiceImplTest {
    @InjectMocks
    private PhotoImmersiveImageServiceImpl photoImmersiveImageService;

    @Mock
    private ImmersiveImageWrapper immersiveImageWrapper;

    @Test
    public void testGetCategoryIds() {
        PhotoImmersiveImageServiceImpl service = new PhotoImmersiveImageServiceImpl();
        List<Integer> categoryIds = service.getCategoryIds();
        assertEquals(Integer.valueOf(504), categoryIds.get(0));
        assertEquals(Integer.valueOf(1004), categoryIds.get(1));
    }

    /**
     * 测试 getImmersiveImage 方法，正常情况
     */
    @Test
    public void testGetImmersiveImageNormal() {
        GetImmersiveImageRequest request = new GetImmersiveImageRequest();
        request.setDealGroupId(1);
        request.setStart(0);
        request.setLimit(10);
        request.setShopId(1L);
        request.setDealGroupType(PlatformEnum.MEI_TUAN.getType());
        request.setCategoryId(504);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        // mock
        when(immersiveImageWrapper.getImmersiveImage(any(QueryExhibitImageParam.class), any(EnvCtx.class))).thenReturn(new ImmersiveImageVO());
        ImmersiveImageVO result = photoImmersiveImageService.getImmersiveImage(request, envCtx);
        assertNotNull(result);
    }

    /**
     * 测试 getImmersiveImage 方法，当 dealGroupType 为非美团类型
     */
    @Test
    public void testGetImmersiveImageNonMeiTuanType() {
        GetImmersiveImageRequest request = new GetImmersiveImageRequest();
        request.setDealGroupId(1);
        request.setStart(0);
        request.setLimit(10);
        request.setShopId(1L);
        request.setCategoryId(504);
        request.setDealGroupType(PlatformEnum.DIAN_PING.getType());
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);

        when(immersiveImageWrapper.getImmersiveImage(any(QueryExhibitImageParam.class), any(EnvCtx.class))).thenReturn(new ImmersiveImageVO());
        ImmersiveImageVO result = photoImmersiveImageService.getImmersiveImage(request, envCtx);
        assertNotNull(result);
    }

    /**
     * 测试 getImmersiveImage 方法，当 dealGroupType 为非美团类型
     */
    @Test
    public void testGetImmersiveImageContainFilter() {
        GetImmersiveImageRequest request = new GetImmersiveImageRequest();
        request.setDealGroupId(420872998);
        request.setStart(0);
        request.setLimit(10);
        request.setShopId(15968337L);
        request.setCategoryId(504);
        request.setDealGroupType(PlatformEnum.DIAN_PING.getType());

        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        when(immersiveImageWrapper.getImmersiveImage(any(QueryExhibitImageParam.class), any(EnvCtx.class))).thenReturn(new ImmersiveImageVO());
        ImmersiveImageVO result = photoImmersiveImageService.getImmersiveImage(request, envCtx);
        assertNotNull(result);
    }

    /**
     * 测试 getImmersiveImageFilter 方法，当 dealGroupType 为非美团类型
     */
    @Test
    public void testGetImmersiveImageFilter() {
        GetImmersiveImageFilterRequest request = new GetImmersiveImageFilterRequest();
        request.setDealGroupId(420872998);
        request.setShopId(15968337L);
        request.setCategoryId(504);
        request.setDealGroupType(PlatformEnum.DIAN_PING.getType());
        when(immersiveImageWrapper.getImmersiveImageFilter(any(QueryExhibitImageFilterParam.class))).thenReturn(new ImmersiveImageFilterVO());
        ImmersiveImageFilterVO result = photoImmersiveImageService.getImmersiveImageFilter(request);
        assertNotNull(result);
    }
}
