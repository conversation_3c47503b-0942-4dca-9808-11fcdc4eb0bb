package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;


import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.WxNameQueryWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.AppTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.common.ResponseDTO;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomtoconsumer.GetUserCodeRequest;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomtoconsumer.UserCodeAndNickNameDTO;
import java.util.concurrent.Future;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WxNameQueryProcessorTest {

    @InjectMocks
    private WxNameQueryProcessor wxNameQueryProcessor;

    @Mock
    private WxNameQueryWrapper wxNameQueryWrapper;

    @Mock
    private DealCtx ctx;

    @Mock
    private DealBaseReq dealBaseReq;

    @Mock
    private FutureCtx futureCtx;

    @Mock
    private EnvCtx envCtx;

    private static final String METHOD_SIGNATURE = "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.WxNameQueryProcessor.prepare(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)";

    @Mock
    private ResponseDTO<UserCodeAndNickNameDTO> response;

    @Mock
    private UserCodeAndNickNameDTO userCodeAndNickNameDTO;

    @Mock
    private Future<ResponseDTO<UserCodeAndNickNameDTO>> future;

    private MockedStatic<LionConfigUtils> mockedLionConfigUtils;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // Mock EnvCtx
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.getMtUserId()).thenReturn(123L);
        when(envCtx.getUnionId()).thenReturn("test-union-id");
        // Mock DealBaseReq
        when(dealBaseReq.getPrivateLiveId()).thenReturn("test-live-id");
        when(dealBaseReq.getUserDistributionParam()).thenReturn("test-user-param");
        when(dealBaseReq.getDistributionParam()).thenReturn("test-dist-param");
        mockedLionConfigUtils = Mockito.mockStatic(LionConfigUtils.class);
    }

    @After
    public void after() {
        mockedLionConfigUtils.close();
    }




    @Test
    public void testPrepareDealBaseReqIsNotNull() throws Throwable {
        // arrange
        when(ctx.getDealBaseReq()).thenReturn(dealBaseReq);
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
        when(wxNameQueryWrapper.getUserCodeAndNickName(any(GetUserCodeRequest.class))).thenReturn(future);
       // when(ctx.isMtLiveMinApp()).thenReturn(true);
        mockedLionConfigUtils.when(() -> LionConfigUtils.getWxNameQuerySwitch()).thenReturn(true);
        // act
        wxNameQueryProcessor.prepare(ctx);
        // assert
        verify(ctx, atLeastOnce()).getDealBaseReq();
        verify(ctx, atLeastOnce()).getEnvCtx();
        verify(envCtx).getMtUserId();
        verify(envCtx).getUnionId();
        verify(dealBaseReq).getPrivateLiveId();
        verify(dealBaseReq).getUserDistributionParam();
        verify(dealBaseReq).getDistributionParam();
        verify(wxNameQueryWrapper).getUserCodeAndNickName(argThat(request -> request.getMtUserId() == 123L && request.getMiniProgramType() == AppTypeEnum.MT.getValue() && request.getUnionId().equals("test-union-id") && request.getLiveId().equals("test-live-id") && request.getUserCode().equals("test-user-param") && request.getDistributionParam().equals("test-dist-param")));
        verify(futureCtx).setDistributionRpcFuture(future);
    }



    @Test
    public void testPrepareFutureCtxIsNull() throws Throwable {
        // arrange
        when(ctx.getDealBaseReq()).thenReturn(dealBaseReq);
        when(ctx.getFutureCtx()).thenReturn(null);
        when(wxNameQueryWrapper.getUserCodeAndNickName(any(GetUserCodeRequest.class))).thenReturn(future);
//        when(ctx.isMtLiveMinApp()).thenReturn(true);
        mockedLionConfigUtils.when(() -> LionConfigUtils.getWxNameQuerySwitch()).thenReturn(true);
        // act
        try {
            wxNameQueryProcessor.prepare(ctx);
        } catch (NullPointerException e) {
            // Expected NPE, ignore it
        }
        // verify
        verify(ctx, atLeastOnce()).getDealBaseReq();
        verify(ctx, atLeastOnce()).getEnvCtx();
        verify(ctx).getFutureCtx();
        verify(wxNameQueryWrapper).getUserCodeAndNickName(any(GetUserCodeRequest.class));
    }

    /**
     * 测试场景：当ctx.isMt()为true，且wxNameQueryWrapper.getFutureResult返回成功响应，且响应数据非空时
     */
    @Test
    public void testProcess_WhenIsMtTrueAndResponseSuccessAndDataNotNull() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
//        when(ctx.isMtLiveMinApp()).thenReturn(true);
        mockedLionConfigUtils.when(() -> LionConfigUtils.getWxNameQuerySwitch()).thenReturn(true);
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
        when(ctx.getFutureCtx().getDistributionRpcFuture()).thenReturn(future);

        ResponseDTO<UserCodeAndNickNameDTO> responseDTO = new ResponseDTO<>();
        responseDTO.setCode(200);
        UserCodeAndNickNameDTO data = new UserCodeAndNickNameDTO();
        data.setNickName("TestNickName");
        data.setUserCode("TestUserCode");
        responseDTO.setData(data);

        when(wxNameQueryWrapper.getFutureResult(future)).thenReturn(responseDTO);

        // act
        wxNameQueryProcessor.process(ctx);

        // assert
        verify(ctx, times(1)).setWxName("TestNickName");
        verify(ctx, times(1)).setUserDistributionParam("TestUserCode");
    }
}

