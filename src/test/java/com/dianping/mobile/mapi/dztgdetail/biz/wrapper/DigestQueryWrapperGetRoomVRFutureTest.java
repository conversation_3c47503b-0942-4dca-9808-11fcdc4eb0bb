package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import com.google.common.collect.Lists;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.mpmctcontent.query.thrift.api.digest.DigestQueryService;
import com.sankuai.mpmctcontent.query.thrift.dto.digest.QueryDigestRequestDTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;
import org.apache.commons.lang3.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(MockitoJUnitRunner.class)
@PrepareForTest({ ContextStore.class })
public class DigestQueryWrapperGetRoomVRFutureTest {

    @Mock
    private DigestQueryService digestQueryService;

    @InjectMocks
    private DigestQueryWrapper digestQueryWrapper;

    @Mock
    private Future<?> mockFuture;

    @Mock
    private DealCtx ctx;

    @Mock
    private DealGroupDTO dealGroupDTO;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(ContextStore.class);
    }

    /**
     * Test exception handling when digestQueryService throws an exception
     */
    @Test
    public void testGetRoomVRFuture_ExceptionHandling() throws Throwable {
        // arrange
        String roomId = "123456";
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr = new AttrDTO();
        attr.setName(DealAttrKeys.CONFINEMENT_ROOM_ID);
        List<String> values = new ArrayList<>();
        values.add(roomId);
        attr.setValue(values);
        attrs.add(attr);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getAttrs()).thenReturn(attrs);
        doThrow(new RuntimeException("Test exception")).when(digestQueryService).queryDigest(any(QueryDigestRequestDTO.class));
        // act
        Future<?> result = digestQueryWrapper.getRoomVRFuture(ctx);
        // assert
        verify(digestQueryService).queryDigest(any(QueryDigestRequestDTO.class));
        assertNull(result);
    }

    /**
     * Test when dealGroupDTO is null
     */
    @Test
    public void testGetRoomVRFuture_NullDealGroupDTO() throws Throwable {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(null);
        // act
        Future<?> result = digestQueryWrapper.getRoomVRFuture(ctx);
        // assert
        assertNull(result);
        verify(digestQueryService, never()).queryDigest(any(QueryDigestRequestDTO.class));
    }

    /**
     * Test when roomId is blank
     */
    @Test
    public void testGetRoomVRFuture_BlankRoomId() throws Throwable {
        // arrange
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr = new AttrDTO();
        attr.setName(DealAttrKeys.CONFINEMENT_ROOM_ID);
        List<String> values = new ArrayList<>();
        // blank roomId
        values.add("");
        attr.setValue(values);
        attrs.add(attr);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getAttrs()).thenReturn(attrs);
        // act
        Future<?> result = digestQueryWrapper.getRoomVRFuture(ctx);
        // assert
        assertNull(result);
        verify(digestQueryService, never()).queryDigest(any(QueryDigestRequestDTO.class));
    }
}
