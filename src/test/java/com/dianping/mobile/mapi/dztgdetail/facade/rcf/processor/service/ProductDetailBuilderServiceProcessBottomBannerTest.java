package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBanner;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.action.extend.redirect.SuckBottomRedirectActionVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.banner.BottomBarTopBannerVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.BottomBarActionDataVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.backgroud.BottomBarBackgroundVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.rich.content.TextRichContentVO;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductDetailBuilderServiceProcessBottomBannerTest {

    @InjectMocks
    private ProductDetailBuilderService productDetailBuilderService;

    private Method processBottomBannerMethod;

    @Before
    public void setUp() throws Exception {
        processBottomBannerMethod = ProductDetailBuilderService.class.getDeclaredMethod("processBottomBanner", BottomBarTopBannerVO.class, DealGroupPBO.class);
        processBottomBannerMethod.setAccessible(true);
    }

    private void invokeProcessBottomBanner(BottomBarTopBannerVO bannerVO, DealGroupPBO result) throws Exception {
        processBottomBannerMethod.invoke(productDetailBuilderService, bannerVO, result);
    }

    /**
     * Test case: When input parameters are null
     * Expected: Method should return early without any processing
     */
    @Test
    public void testProcessBottomBanner_NullInputs() throws Throwable {
        // Test with null bannerVO
        DealGroupPBO result1 = new DealGroupPBO();
        DealBuyBar buyBar1 = new DealBuyBar(1, null);
        result1.setBuyBar(buyBar1);
        invokeProcessBottomBanner(null, result1);
        assertNull("Banner should not be created when bannerVO is null", result1.getBuyBar().getBuyBanner());
        // Test with null result
        invokeProcessBottomBanner(new BottomBarTopBannerVO(), null);
        // No exception should be thrown
        // Test with both null
        invokeProcessBottomBanner(null, null);
        // No exception should be thrown
    }

    /**
     * Test case: When result.getBuyBar() returns null
     * Expected: Method should return early without processing
     */
    @Test
    public void testProcessBottomBanner_NullBuyBar() throws Throwable {
        BottomBarTopBannerVO bannerVO = new BottomBarTopBannerVO();
        bannerVO.setBannerType(1);
        DealGroupPBO result = new DealGroupPBO();
        result.setBuyBar(null);
        invokeProcessBottomBanner(bannerVO, result);
        assertNull("BuyBar should remain null", result.getBuyBar());
    }

    /**
     * Test case: When banner type is 8 (magic coupon) but banner data is empty
     * Expected: Should still create banner with correct type
     */
    @Test
    public void testProcessBottomBanner_MagicCouponTypeEmptyData() throws Throwable {
        BottomBarTopBannerVO bannerVO = new BottomBarTopBannerVO();
        bannerVO.setBannerType(8);
        // Setup background
        BottomBarBackgroundVO background = new BottomBarBackgroundVO();
        background.setColors(Arrays.asList("#FFFFFF"));
        bannerVO.setBackground(background);
        // Setup action data
        SuckBottomRedirectActionVO actionData = new SuckBottomRedirectActionVO();
        actionData.setActionType(5);
        bannerVO.setActionData(actionData);
        // Empty banner data
        bannerVO.setBannerData(new ArrayList<>());
        DealGroupPBO result = new DealGroupPBO();
        result.setBuyBar(new DealBuyBar(1, null));
        invokeProcessBottomBanner(bannerVO, result);
        DealBuyBanner banner = result.getBuyBar().getBuyBanner();
        assertNotNull("Banner should be created", banner);
        assertEquals("Banner type should be 8", 8, banner.getBannerType());
        assertTrue("Banner should be shown", banner.isShow());
        assertEquals("Background color should match", Arrays.asList("#FFFFFF"), banner.getBackGroundColor());
    }

    /**
     * Test case: When banner type is 1 (country subsidies)
     * Expected: Should process country subsidies banner
     */
    @Test
    public void testProcessBottomBanner_CountrySubsidiesType() throws Throwable {
        BottomBarTopBannerVO bannerVO = new BottomBarTopBannerVO();
        bannerVO.setBannerType(1);
        // Setup background
        BottomBarBackgroundVO background = new BottomBarBackgroundVO();
        background.setColors(Arrays.asList("#00FF00"));
        bannerVO.setBackground(background);
        DealGroupPBO result = new DealGroupPBO();
        result.setBuyBar(new DealBuyBar(1, null));
        invokeProcessBottomBanner(bannerVO, result);
        DealBuyBanner banner = result.getBuyBar().getBuyBanner();
        assertNotNull("Banner should be created", banner);
        assertEquals("Banner type should be 1", 1, banner.getBannerType());
        assertTrue("Banner should be shown", banner.isShow());
        assertEquals("Background color should match", Arrays.asList("#00FF00"), banner.getBackGroundColor());
    }

    /**
     * Test case: When banner type is neither 1 nor 8
     * Expected: No banner processing should occur
     */
    @Test
    public void testProcessBottomBanner_OtherBannerType() throws Throwable {
        BottomBarTopBannerVO bannerVO = new BottomBarTopBannerVO();
        // Some other type
        bannerVO.setBannerType(3);
        DealGroupPBO result = new DealGroupPBO();
        result.setBuyBar(new DealBuyBar(1, null));
        invokeProcessBottomBanner(bannerVO, result);
        assertNull("No banner should be created for unsupported type", result.getBuyBar().getBuyBanner());
    }

    /**
     * Test case: When banner has null background
     * Expected: Should still process banner correctly
     */
    @Test
    public void testProcessBottomBanner_NullBackground() throws Throwable {
        BottomBarTopBannerVO bannerVO = new BottomBarTopBannerVO();
        bannerVO.setBannerType(1);
        bannerVO.setBackground(null);
        DealGroupPBO result = new DealGroupPBO();
        result.setBuyBar(new DealBuyBar(1, null));
        invokeProcessBottomBanner(bannerVO, result);
        DealBuyBanner banner = result.getBuyBar().getBuyBanner();
        assertNotNull("Banner should be created even with null background", banner);
        assertEquals("Banner type should be 1", 1, banner.getBannerType());
        assertTrue("Banner should be shown", banner.isShow());
        assertNull("Background color should be null", banner.getBackGroundColor());
    }

    /**
     * Test case: Complete happy path with valid inputs
     * Expected: Should process banner correctly
     */
    @Test
    public void testProcessBottomBanner_HappyPath() throws Throwable {
        BottomBarTopBannerVO bannerVO = new BottomBarTopBannerVO();
        bannerVO.setBannerType(1);
        // Setup background
        BottomBarBackgroundVO background = new BottomBarBackgroundVO();
        background.setColors(Arrays.asList("#00FF00"));
        bannerVO.setBackground(background);
        // Setup banner data
        TextRichContentVO textContent = new TextRichContentVO();
        textContent.setText("国家补贴城市,当前定位北京");
        bannerVO.setBannerData(Collections.singletonList(textContent));
        DealGroupPBO result = new DealGroupPBO();
        result.setBuyBar(new DealBuyBar(1, null));
        invokeProcessBottomBanner(bannerVO, result);
        DealBuyBanner banner = result.getBuyBar().getBuyBanner();
        assertNotNull("Banner should be created", banner);
        assertEquals("Banner type should be 1", 1, banner.getBannerType());
        assertTrue("Banner should be shown", banner.isShow());
        assertEquals("Background color should match", Arrays.asList("#00FF00"), banner.getBackGroundColor());
    }
}
