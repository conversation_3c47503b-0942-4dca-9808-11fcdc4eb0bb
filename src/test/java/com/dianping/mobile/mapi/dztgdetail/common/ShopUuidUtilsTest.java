//package com.dianping.mobile.mapi.dztgdetail.common;
//
//import com.dianping.mobile.mapi.dztgdetail.GenericTest;
//import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ShopIdUuidDTO;
//import com.google.common.collect.Maps;
//import com.sankuai.meituan.common.json.JSONUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Test;
//import org.springframework.util.Assert;
//
//import java.util.Map;
//
//@Slf4j
//public class ShopUuidUtilsTest  extends GenericTest {
//
//    @Test
//    public void testGetUuidDtoByIdForLong(){
//        ShopIdUuidDTO uuidDtoByIdForLong = ShopUuidUtilsV2.getUuidDtoByIdForLong(73591142L);
//        System.out.println(JSONUtil.toJSONString(uuidDtoByIdForLong));
//        Assert.notNull(uuidDtoByIdForLong);
//    }
//
//    @Test
//    public void getUuidByIdTest(){
//        //上线新增category配置  mapper_shop_uuid_new
//        String uuidById = ShopUuidUtils.getUuidById(606915617L);
//        System.out.println("uuidById:"+uuidById);
//        Assert.notNull(uuidById);
//    }
//
//    @Test
//    public void getUuidByIdLongTest(){
//        String uuidByIdLong = ShopUuidUtils.getUuidByIdLong(73591142L);
//        System.out.println("uuidById:"+uuidByIdLong);
//        Assert.notNull(uuidByIdLong);
//
//    }
//
//    @Test
//    public void getShopIdByUuidTest(){
//        Long shopId = ShopUuidUtils.getShopIdByUuid("k65YkZ7itAC0KkwI");
//        System.out.println(shopId);
//        Assert.notNull(shopId);
//    }
//
//}
