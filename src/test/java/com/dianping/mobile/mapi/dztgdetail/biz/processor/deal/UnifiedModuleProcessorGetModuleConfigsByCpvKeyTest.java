package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedModuleProcessorGetModuleConfigsByCpvKeyTest {

    private UnifiedModuleProcessor processor;

    private Method getModuleConfigsByCpvKeyMethod;

    @Before
    public void setUp() throws Exception {
        processor = new UnifiedModuleProcessor();
        getModuleConfigsByCpvKeyMethod = UnifiedModuleProcessor.class.getDeclaredMethod("getModuleConfigsByCpvKey", Map.class, String.class);
        getModuleConfigsByCpvKeyMethod.setAccessible(true);
    }

    /**
     * Test when moduleConfigMaps is null
     */
    @Test
    public void testGetModuleConfigsByCpvKeyWithNullModuleConfigMaps() throws Throwable {
        // arrange
        Map<String, List<ModuleConfigDo>> moduleConfigMaps = null;
        String key = "123";
        // act
        List<ModuleConfigDo> result = (List<ModuleConfigDo>) getModuleConfigsByCpvKeyMethod.invoke(processor, moduleConfigMaps, key);
        // assert
        assertNull(result);
    }

    /**
     * Test when key is null
     */
    @Test
    public void testGetModuleConfigsByCpvKeyWithNullKey() throws Throwable {
        // arrange
        Map<String, List<ModuleConfigDo>> moduleConfigMaps = new HashMap<>();
        String key = null;
        // act
        List<ModuleConfigDo> result = (List<ModuleConfigDo>) getModuleConfigsByCpvKeyMethod.invoke(processor, moduleConfigMaps, key);
        // assert
        assertNull(result);
    }

    /**
     * Test when key is empty string
     */
    @Test
    public void testGetModuleConfigsByCpvKeyWithEmptyKey() throws Throwable {
        // arrange
        Map<String, List<ModuleConfigDo>> moduleConfigMaps = new HashMap<>();
        String key = "";
        // act
        List<ModuleConfigDo> result = (List<ModuleConfigDo>) getModuleConfigsByCpvKeyMethod.invoke(processor, moduleConfigMaps, key);
        // assert
        assertNull(result);
    }

    /**
     * Test when moduleConfigMaps is empty
     */
    @Test
    public void testGetModuleConfigsByCpvKeyWithEmptyModuleConfigMaps() throws Throwable {
        // arrange
        Map<String, List<ModuleConfigDo>> moduleConfigMaps = new HashMap<>();
        String key = "123";
        // act
        List<ModuleConfigDo> result = (List<ModuleConfigDo>) getModuleConfigsByCpvKeyMethod.invoke(processor, moduleConfigMaps, key);
        // assert
        assertNull(result);
    }

    /**
     * Test when entry key is empty
     */
    @Test
    public void testGetModuleConfigsByCpvKeyWithEmptyEntryKey() throws Throwable {
        // arrange
        Map<String, List<ModuleConfigDo>> moduleConfigMaps = new HashMap<>();
        List<ModuleConfigDo> configList = Lists.newArrayList(new ModuleConfigDo());
        moduleConfigMaps.put("", configList);
        String key = "123";
        // act
        List<ModuleConfigDo> result = (List<ModuleConfigDo>) getModuleConfigsByCpvKeyMethod.invoke(processor, moduleConfigMaps, key);
        // assert
        assertNull(result);
    }

    /**
     * Test when key matches exactly one of the categories in an entry
     */
    @Test
    public void testGetModuleConfigsByCpvKeyWithExactMatch() throws Throwable {
        // arrange
        Map<String, List<ModuleConfigDo>> moduleConfigMaps = new HashMap<>();
        List<ModuleConfigDo> configList = Lists.newArrayList(new ModuleConfigDo());
        moduleConfigMaps.put("123", configList);
        String key = "123";
        // act
        List<ModuleConfigDo> result = (List<ModuleConfigDo>) getModuleConfigsByCpvKeyMethod.invoke(processor, moduleConfigMaps, key);
        // assert
        assertSame(configList, result);
    }

    /**
     * Test when key matches one of multiple categories in an entry
     */
    @Test
    public void testGetModuleConfigsByCpvKeyWithMultipleCategoriesMatch() throws Throwable {
        // arrange
        Map<String, List<ModuleConfigDo>> moduleConfigMaps = new HashMap<>();
        List<ModuleConfigDo> configList = Lists.newArrayList(new ModuleConfigDo());
        moduleConfigMaps.put("123,456,789", configList);
        String key = "456";
        // act
        List<ModuleConfigDo> result = (List<ModuleConfigDo>) getModuleConfigsByCpvKeyMethod.invoke(processor, moduleConfigMaps, key);
        // assert
        assertSame(configList, result);
    }

    /**
     * Test when key doesn't match any category in any entry
     */
    @Test
    public void testGetModuleConfigsByCpvKeyWithNoMatch() throws Throwable {
        // arrange
        Map<String, List<ModuleConfigDo>> moduleConfigMaps = new HashMap<>();
        List<ModuleConfigDo> configList = Lists.newArrayList(new ModuleConfigDo());
        moduleConfigMaps.put("123,456,789", configList);
        String key = "999";
        // act
        List<ModuleConfigDo> result = (List<ModuleConfigDo>) getModuleConfigsByCpvKeyMethod.invoke(processor, moduleConfigMaps, key);
        // assert
        assertNull(result);
    }

    /**
     * Test when key matches a category in the second entry
     */
    @Test
    public void testGetModuleConfigsByCpvKeyWithMatchInSecondEntry() throws Throwable {
        // arrange
        Map<String, List<ModuleConfigDo>> moduleConfigMaps = new HashMap<>();
        List<ModuleConfigDo> configList1 = Lists.newArrayList(new ModuleConfigDo());
        List<ModuleConfigDo> configList2 = Lists.newArrayList(new ModuleConfigDo());
        moduleConfigMaps.put("123,456", configList1);
        moduleConfigMaps.put("789,101", configList2);
        String key = "789";
        // act
        List<ModuleConfigDo> result = (List<ModuleConfigDo>) getModuleConfigsByCpvKeyMethod.invoke(processor, moduleConfigMaps, key);
        // assert
        assertSame(configList2, result);
    }

    /**
     * Test when there are multiple entries with empty values
     */
    @Test
    public void testGetModuleConfigsByCpvKeyWithMultipleEmptyEntries() throws Throwable {
        // arrange
        Map<String, List<ModuleConfigDo>> moduleConfigMaps = Maps.newHashMap();
        moduleConfigMaps.put("", Lists.newArrayList());
        moduleConfigMaps.put(null, Lists.newArrayList());
        moduleConfigMaps.put("123", Lists.newArrayList(new ModuleConfigDo()));
        String key = "123";
        // act
        List<ModuleConfigDo> result = (List<ModuleConfigDo>) getModuleConfigsByCpvKeyMethod.invoke(processor, moduleConfigMaps, key);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
    }
}
