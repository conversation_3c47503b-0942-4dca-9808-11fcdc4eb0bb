package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.IExaminerAbstractHandler;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

public class MedicExaminerHighlightsProcessorBuildExaminerHighlightsTest {

    private MedicExaminerHighlightsProcessor processor;

    private DealCtx ctx;

    private DealGroupDTO dealGroupDTO;

    private DealGroupCategoryDTO category;

    private IExaminerAbstractHandler handler;

    @Before
    public void setUp() {
        processor = new MedicExaminerHighlightsProcessor();
        ctx = mock(DealCtx.class);
        dealGroupDTO = mock(DealGroupDTO.class);
        category = mock(DealGroupCategoryDTO.class);
        handler = mock(IExaminerAbstractHandler.class);
        // Clear the static map before each test
        MedicExaminerHighlightsProcessor.EXAMINER_SERVER_TYPE_HANDLER_MAP.clear();
    }

    /**
     * 测试 DealGroupDTO 为空的情况
     */
    @Test
    public void testBuildExaminerHighlightsDealGroupDTONull() throws Throwable {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(null);
        // act
        processor.buildExaminerHighlights(ctx);
        // assert
        verify(ctx, times(1)).getDealGroupDTO();
        verifyNoMoreInteractions(ctx);
    }

    /**
     * 测试 DealGroupDTO 不为空，但 category 为空的情况
     */
    @Test
    public void testBuildExaminerHighlightsCategoryNull() throws Throwable {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(null);
        // act
        processor.buildExaminerHighlights(ctx);
        // assert
        verify(ctx, times(1)).getDealGroupDTO();
        verify(dealGroupDTO, times(1)).getCategory();
        verifyNoMoreInteractions(ctx, dealGroupDTO);
    }

    /**
     * 测试 DealGroupDTO 不为空，category 不为空，但 serviceType 为空的情况
     */
    @Test
    public void testBuildExaminerHighlightsServiceTypeNull() throws Throwable {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(category.getServiceType()).thenReturn(null);
        // act
        processor.buildExaminerHighlights(ctx);
        // assert
        verify(ctx, times(1)).getDealGroupDTO();
        verify(dealGroupDTO, times(1)).getCategory();
        verify(category, times(1)).getServiceType();
        verifyNoMoreInteractions(ctx, dealGroupDTO, category);
    }

    /**
     * 测试 DealGroupDTO 不为空，category 不为空，serviceType 不为空，但 handler 为空的情况
     */
    @Test
    public void testBuildExaminerHighlightsHandlerNull() throws Throwable {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(category.getServiceType()).thenReturn("testServiceType");
        // act
        processor.buildExaminerHighlights(ctx);
        // assert
        verify(ctx, times(1)).getDealGroupDTO();
        verify(dealGroupDTO, times(1)).getCategory();
        verify(category, times(1)).getServiceType();
        verifyNoMoreInteractions(ctx, dealGroupDTO, category);
    }

    /**
     * 测试 DealGroupDTO 不为空，category 不为空，serviceType 不为空，handler 不为空的情况
     */
    @Test
    public void testBuildExaminerHighlightsHandlerNotNull() throws Throwable {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(category.getServiceType()).thenReturn("testServiceType");
        MedicExaminerHighlightsProcessor.EXAMINER_SERVER_TYPE_HANDLER_MAP.put("testServiceType", handler);
        // act
        processor.buildExaminerHighlights(ctx);
        // assert
        verify(ctx, times(1)).getDealGroupDTO();
        verify(dealGroupDTO, times(1)).getCategory();
        verify(category, times(1)).getServiceType();
        verify(handler, times(1)).execute(ctx);
        verifyNoMoreInteractions(ctx, dealGroupDTO, category, handler);
    }
}
