package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.martgeneral.recommend.api.enums.PlatformEnum;
import com.dianping.martgeneral.recommend.api.service.RecommendService;
import com.dianping.mobile.mapi.dztgdetail.entity.QueryRecommendParam;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.concurrent.Future;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RecommendServiceWrapperTest {

    @InjectMocks
    private RecommendServiceWrapper recommendServiceWrapper;

    @Mock
    private RecommendService recommendServiceFuture;

    private QueryRecommendParam param;

    private MockedStatic<FutureFactory> mocked;

    @Before
    public void setUp() {
        param = QueryRecommendParam.builder().platformEnum(PlatformEnum.DP).originUserId("123456").limit(10).start(0)
                .uuid("uuid").dpId("dpId").cityId(1).latitude(30.0).longitude(120.0)
                .exhibitImgIds(Arrays.asList("1", "2", "3")).sortType("AI").shopRatingThreshold("0").flowFlag("002")
                .filterIds("2030").dealGroupPrice("170.00").isAll("1").dealGroupTagIds("1,2").build();

        mocked = mockStatic(FutureFactory.class);
    }

    @After
    public void teardown() {
        this.mocked.close();
    }

    /**
     * 测试 preGetRecommendStyleImage 方法，所有属性都有值
     */
    @Test
    public void testPreGetRecommendStyleImageAllProperties() throws Throwable {
        // arrange
        Future mockFuture = mock(Future.class);
        mocked.when(FutureFactory::getFuture).thenReturn(mockFuture);
        when(recommendServiceFuture.recommend(any(), any())).thenReturn(null);
        // act
        Future future = recommendServiceWrapper.preGetRecommendStyleImage(param);
        // assert
        assertNotNull(future);
    }

    /**
     * 测试 preGetRecommendStyleImage 方法，部分属性为 null
     */
    @Test
    public void testPreGetRecommendStyleImageSomePropertiesNull() throws Throwable {
        // arrange
        param.setPlatformEnum(null);
        param.setOriginUserId(null);
        // arrange
        Future mockFuture = mock(Future.class);
        mocked.when(FutureFactory::getFuture).thenReturn(mockFuture);
        when(recommendServiceFuture.recommend(any(), any())).thenReturn(null);
        // act
        Future future = recommendServiceWrapper.preGetRecommendStyleImage(param);
        // assert
        assertNotNull(future);
    }

    /**
     * 测试 preGetRecommendStyleImage 方法，所有属性都为 null
     */
    @Test
    public void testPreGetRecommendStyleImageAllPropertiesNull() throws Throwable {
        // arrange
        param = QueryRecommendParam.builder().build();
        // arrange
        Future mockFuture = mock(Future.class);
        mocked.when(FutureFactory::getFuture).thenReturn(mockFuture);
        when(recommendServiceFuture.recommend(any(), any())).thenReturn(null);
        // act
        Future future = recommendServiceWrapper.preGetRecommendStyleImage(param);
        // assert
        assertNotNull(future);
    }
}
