package com.dianping.mobile.mapi.dztgdetail.util;

import static org.junit.Assert.*;
import java.util.HashMap;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MapComparatorTest {

    /**
     * 测试两个 Map 对象为同一对象的情况
     */
    @Test
    public void testDeepCompareSameObject() throws Throwable {
        Map<String, String> map = new HashMap<>();
        map.put("key", "value");
        assertTrue(MapComparator.deepCompare(map, map, null, null, false));
    }

    /**
     * 测试两个 Map 对象都为 null 的情况
     */
    @Test
    public void testDeepCompareBothNull() throws Throwable {
        // Assuming the method returns true when both inputs are null based on the observed behavior
        assertTrue(MapComparator.deepCompare(null, null, null, null, false));
    }

    /**
     * 测试两个 Map 对象的键集合不一致的情况
     */
    @Test
    public void testDeepCompareKeySetNotEqual() throws Throwable {
        Map<String, String> map1 = new HashMap<>();
        map1.put("key1", "value1");
        Map<String, String> map2 = new HashMap<>();
        map2.put("key2", "value2");
        assertFalse(MapComparator.deepCompare(map1, map2, null, null, false));
    }

    /**
     * 测试两个 Map 对象的键集合一致，但存在任何一个键的值不相等的情况
     */
    @Test
    public void testDeepCompareValueNotEqual() throws Throwable {
        Map<String, String> map1 = new HashMap<>();
        map1.put("key", "value1");
        Map<String, String> map2 = new HashMap<>();
        map2.put("key", "value2");
        assertFalse(MapComparator.deepCompare(map1, map2, null, null, null, false));
    }

    /**
     * 测试两个 Map 对象的键集合一致，且所有键的值都相等的情况
     */
    @Test
    public void testDeepCompareEqual() throws Throwable {
        Map<String, String> map1 = new HashMap<>();
        map1.put("key", "value");
        Map<String, String> map2 = new HashMap<>();
        map2.put("key", "value");
        assertTrue(MapComparator.deepCompare(map1, map2, null, null, null, false));
    }
}
