package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.highlights.DztgHighlightsModule;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Future;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class BeautyHighlightsProcessorProcessTest {

    @InjectMocks
    @Spy
    private BeautyHighlightsProcessor processor;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private FutureCtx futureCtx;

    @Mock
    private Future future;

    @InjectMocks
    private BeautyHighlightsProcessor beautyHighlightsProcessor;

    @Before
    public void setUp() {
        when(dealCtx.getFutureCtx()).thenReturn(futureCtx);
        when(futureCtx.getSingleDealGroupDtoFuture()).thenReturn(future);
    }

    /**
     * Test successful processing with valid DealGroupDTO
     */
    @Test
    public void testProcess_SuccessfulCase() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        when(queryCenterWrapper.getDealGroupDTO(future)).thenReturn(dealGroupDTO);
        // act
        processor.process(dealCtx);
        // assert
        verify(dealCtx).setDealGroupDTO(dealGroupDTO);
        verify(dealCtx, never()).setQueryCenterHasError(true);
        verify(processor).buildBeautyHighlights(dealCtx);
    }

    /**
     * Test processing when getDealGroupDTO throws exception
     */
    @Test
    public void testProcess_WhenGetDealGroupDTOThrowsException() throws Throwable {
        // arrange
        when(queryCenterWrapper.getDealGroupDTO(future)).thenThrow(new RuntimeException("Test exception"));
        // act
        processor.process(dealCtx);
        // assert
        verify(dealCtx).setQueryCenterHasError(true);
        verify(dealCtx).setDealGroupDTO(null);
        verify(processor).buildBeautyHighlights(dealCtx);
    }

    /**
     * Test processing when getDealGroupDTO returns null
     */
    @Test
    public void testProcess_WhenDealGroupDTOIsNull() throws Throwable {
        // arrange
        when(queryCenterWrapper.getDealGroupDTO(future)).thenReturn(null);
        // act
        processor.process(dealCtx);
        // assert
        verify(dealCtx).setDealGroupDTO(null);
        verify(dealCtx, never()).setQueryCenterHasError(true);
        verify(processor).buildBeautyHighlights(dealCtx);
    }

    /**
     * Test processing when FutureCtx returns null future
     */
    @Test
    public void testProcess_WhenFutureIsNull() throws Throwable {
        // arrange
        when(futureCtx.getSingleDealGroupDtoFuture()).thenReturn(null);
        // act
        processor.process(dealCtx);
        // assert
        verify(dealCtx).setDealGroupDTO(null);
        verify(processor).buildBeautyHighlights(dealCtx);
    }

    /**
     * Test when selling_point attribute exists with valid value
     */
    @Test
    public void testBuild4Other_WithValidSellingPoint() throws Throwable {
        // arrange
        DealGroupDTO dealGroup = new DealGroupDTO();
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr = new AttrDTO();
        attr.setName("selling_point");
        attr.setValue(Arrays.asList("Test selling point"));
        attrs.add(attr);
        dealGroup.setAttrs(attrs);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroup);
        // Use reflection to invoke the private method
        Method build4OtherMethod = BeautyHighlightsProcessor.class.getDeclaredMethod("build4Other", DealCtx.class);
        build4OtherMethod.setAccessible(true);
        build4OtherMethod.invoke(beautyHighlightsProcessor, dealCtx);
        // assert
        verify(dealCtx, times(1)).setHighlightsModule(argThat(module -> module != null && "simple".equals(module.getStyle()) && "Test selling point".equals(module.getContent())));
    }

    /**
     * Test when selling_point attribute exists but has empty value list
     */
    @Test
    public void testBuild4Other_WithEmptySellingPointValue() throws Throwable {
        // arrange
        DealGroupDTO dealGroup = new DealGroupDTO();
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr = new AttrDTO();
        attr.setName("selling_point");
        attr.setValue(new ArrayList<>());
        attrs.add(attr);
        dealGroup.setAttrs(attrs);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroup);
        // Use reflection to invoke the private method
        Method build4OtherMethod = BeautyHighlightsProcessor.class.getDeclaredMethod("build4Other", DealCtx.class);
        build4OtherMethod.setAccessible(true);
        build4OtherMethod.invoke(beautyHighlightsProcessor, dealCtx);
        // assert
        verify(dealCtx, never()).setHighlightsModule(any());
    }

    /**
     * Test when selling_point attribute doesn't exist
     */
    @Test
    public void testBuild4Other_WithoutSellingPoint() throws Throwable {
        // arrange
        DealGroupDTO dealGroup = new DealGroupDTO();
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr = new AttrDTO();
        attr.setName("other_attr");
        attr.setValue(Arrays.asList("Some value"));
        attrs.add(attr);
        dealGroup.setAttrs(attrs);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroup);
        // Use reflection to invoke the private method
        Method build4OtherMethod = BeautyHighlightsProcessor.class.getDeclaredMethod("build4Other", DealCtx.class);
        build4OtherMethod.setAccessible(true);
        build4OtherMethod.invoke(beautyHighlightsProcessor, dealCtx);
        // assert
        verify(dealCtx, never()).setHighlightsModule(any());
    }

    /**
     * Test when DealGroupDTO has null attributes list
     */
    @Test
    public void testBuild4Other_WithNullAttrs() throws Throwable {
        // arrange
        DealGroupDTO dealGroup = new DealGroupDTO();
        dealGroup.setAttrs(null);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroup);
        // Use reflection to invoke the private method
        Method build4OtherMethod = BeautyHighlightsProcessor.class.getDeclaredMethod("build4Other", DealCtx.class);
        build4OtherMethod.setAccessible(true);
        build4OtherMethod.invoke(beautyHighlightsProcessor, dealCtx);
        // assert
        verify(dealCtx, never()).setHighlightsModule(any());
    }

    /**
     * Test when DealGroupDTO is null
     */
    @Test
    public void testBuild4Other_WithNullDealGroup() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(null);
        // Use reflection to invoke the private method
        Method build4OtherMethod = BeautyHighlightsProcessor.class.getDeclaredMethod("build4Other", DealCtx.class);
        build4OtherMethod.setAccessible(true);
        try {
            build4OtherMethod.invoke(beautyHighlightsProcessor, dealCtx);
        } catch (Exception e) {
            // Handle the exception if needed
            assertTrue(e.getCause() instanceof NullPointerException);
        }
        // assert
        verify(dealCtx, never()).setHighlightsModule(any());
    }
}
