package com.dianping.mobile.mapi.dztgdetail.facade.rcf.fetcher;

import com.dianping.deal.style.DealPageLayoutService;
import com.dianping.deal.style.dto.laout.*;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.sankuai.athena.inf.AthenaInf;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

public class DealLayoutFetcherTest {

    @InjectMocks
    private DealLayoutFetcher service;

    @Mock
    private DealPageLayoutService dealPageLayoutService;

    @InjectMocks
    private DealLayoutFetcher dealLayoutFetcher;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试preBatchQueryLayout方法，当RPC调用成功且返回成功时
     */
    @Test
    public void testPreBatchQueryLayout_Success() throws Throwable {
        // arrange
        DealPageLayoutBatchQueryRequest request = new DealPageLayoutBatchQueryRequest();
        Map<Long, DealPageLayoutDTO> expectedMap = new HashMap<>();
        DealPageLayoutBatchQueryResponse response = new DealPageLayoutBatchQueryResponse();
        response.setMtDealGroupIdLayoutMap(expectedMap);
        when(dealPageLayoutService.batchQuery(request)).thenReturn(response);
        // act
        Future resultFuture = service.preBatchQueryLayout(request);
        // assert
        assertNull(resultFuture);
    }


    /**
     * 测试preQueryLayout方法，当服务返回异常时
     */
    @Test
    public void testPreQueryLayoutSuccessWithData() throws ExecutionException, InterruptedException {
        DealPageLayoutQueryRequest request = new DealPageLayoutQueryRequest();
        DealPageLayoutQueryResponse response = new DealPageLayoutQueryResponse();
        when(dealPageLayoutService.query(request)).thenReturn(response);
        Future resultFuture = dealLayoutFetcher.preQueryLayout(request);
        assertNull(resultFuture);
    }

}
