package com.dianping.mobile.mapi.dztgdetail;

import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.deal.publishcategory.dto.ChannelDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.deal.stock.dto.ProductGroupStock;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderFactory;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
import com.dianping.mobile.mapi.dztgdetail.entity.CostEffectivePinTuan;
import com.dianping.mobile.mapi.dztgdetail.helper.NewBuyBarHelper;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.dealuser.price.display.api.enums.ProductTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.ProductIdentity;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dealuser.price.display.api.model.PromoIdentity;
import com.sankuai.dzcard.navigation.api.dto.CardQualifyEventIdDTO;
import com.sankuai.merchantcard.timescard.exposure.dto.CardSummaryBarDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * 2020/3/16 8:15 下午
 */
public class PriceServiceTests {

    private DealGroupBaseDTO buildDeal() {
        DealGroupBaseDTO dealBase = new DealGroupBaseDTO();
        dealBase.setMarketPrice(BigDecimal.valueOf(100));
        dealBase.setDealGroupPrice(BigDecimal.valueOf(66));
        dealBase.setBeginDate(new Date());
        dealBase.setEndDate(DateUtils.addDays(new Date(), 100));
        dealBase.setStatus(1);
        return dealBase;
    }

    private DealCtx buildCtx() {
        ProductGroupStock dealGroupStock = new ProductGroupStock();

        EnvCtx env = new EnvCtx();
        env.setVersion("10.28.0");
        env.setClientType(ClientTypeEnum.dp_mainApp_ios.getType());

        DealCtx ctx = new DealCtx(env);
        ctx.setDealGroupStock(dealGroupStock);
        ctx.setPromoList(Lists.newArrayList());
        ctx.setMtId(122);
        ctx.setDpId(122);
        ctx.setMtShopId(123);
        ctx.setDpShopId(123);
        ctx.setPoiBackCategoryIds(Sets.newHashSet());
        ctx.setDealGroupBase(buildDeal());
        ctx.getPriceContext().setNormalPrice(buildNormalPrice(ctx.getDealGroupBase()));
        return ctx;
    }

    private PriceDisplayDTO buildNormalPrice(DealGroupBaseDTO dealGroupBaseDTO) {
        return buildPrice(dealGroupBaseDTO, null);
    }

    private PriceDisplayDTO buildNormalPromoPrice(DealGroupBaseDTO dealGroupBaseDTO) {
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(new PromoIdentity(0, PromoTypeEnum.IDLE_PROMO.getType()));
        promoDTO.setAmount(new BigDecimal(10));
        promoDTO.setDescription("下单减10，限购5单");
        promoDTO.setTag("已减10");

        return buildPrice(dealGroupBaseDTO, promoDTO);
    }

    private PriceDisplayDTO buildIdlePromoPrice(DealGroupBaseDTO dealGroupBaseDTO) {
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(new PromoIdentity(0, PromoTypeEnum.IDLE_PROMO.getType()));
        promoDTO.setAmount(new BigDecimal(11));
        promoDTO.setDescription("限时优惠周二至周四");
        promoDTO.setConsumeTimeDesc("周二至周四");
        promoDTO.setTag("已减11");

        return buildPrice(dealGroupBaseDTO, promoDTO);
    }

    private PriceDisplayDTO buildMemberCardPrice(DealGroupBaseDTO dealGroupBaseDTO) {
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(new PromoIdentity(0, PromoTypeEnum.DISCOUNT_CARD.getType()));
        promoDTO.setAmount(new BigDecimal(12));
        promoDTO.setDescription("会员卡");
        promoDTO.setTag("已省12");

        return buildPrice(dealGroupBaseDTO, promoDTO);
    }

    private PriceDisplayDTO buildPrice(DealGroupBaseDTO dealGroupBaseDTO, PromoDTO promoDTO) {
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        priceDisplayDTO.setIdentity(new ProductIdentity(123, ProductTypeEnum.DEAL.getType()));
        priceDisplayDTO.setBasePrice(dealGroupBaseDTO.getDealGroupPrice());
        priceDisplayDTO.setMarketPrice(dealGroupBaseDTO.getMarketPrice());
        priceDisplayDTO.setPrice(dealGroupBaseDTO.getDealGroupPrice());
        priceDisplayDTO.setMaxPrice(dealGroupBaseDTO.getDealGroupPrice());
        priceDisplayDTO.setPromoAmount(BigDecimal.ZERO);
        if (promoDTO != null) {
            priceDisplayDTO.setPrice(dealGroupBaseDTO.getDealGroupPrice().subtract(promoDTO.getAmount()));
            priceDisplayDTO.setPromoAmount(promoDTO.getAmount());
            priceDisplayDTO.setUsedPromos(Lists.newArrayList(promoDTO));
        }
        return priceDisplayDTO;
    }

    private CardQualifyEventIdDTO buildJouCard(boolean hold) {
        CardQualifyEventIdDTO card = new CardQualifyEventIdDTO();
        card.setQualifyEventId(1);
        card.setQualifyEventType(3);
        card.setUserQualifyStatus(hold ? 1 : 0);
        return card;
    }

    private CardQualifyEventIdDTO buildMemberCard(boolean hold) {
        CardQualifyEventIdDTO card = new CardQualifyEventIdDTO();
        card.setQualifyEventId(1);
        card.setQualifyEventType(1);
        card.setUserQualifyStatus(hold ? 1 : 0);
        return card;
    }

    private CardQualifyEventIdDTO buildMemberDayCard(boolean hold) {
        CardQualifyEventIdDTO card = new CardQualifyEventIdDTO();
        card.setQualifyEventId(1);
        card.setQualifyEventType(2);
        card.setUserQualifyStatus(hold ? 1 : 0);
        return card;
    }

    private CardSummaryBarDTO buildTimesCard() {
        CardSummaryBarDTO card = new CardSummaryBarDTO();
        card.setBtnTag("随时退");
        card.setPrice(BigDecimal.valueOf(55));
        card.setProductId(99999);
        card.setTimes(3);
        card.setChannelSource(1);

        return card;
    }

    private PinProductBrief buildAssembleDeal() {
        PinProductBrief result = new PinProductBrief();
        result.setProductId(7777);
        result.setPrice(new BigDecimal(44));
        result.setUrl("http://pintuan");
        result.setPinPersonNum(3);

        return result;
    }

    private DealGroupChannelDTO buildDealChannel() {

        ChannelDTO channelDTO = new ChannelDTO();
        channelDTO.setChannelId(5);

        DealGroupChannelDTO channel = new DealGroupChannelDTO();
        channel.setCategoryId(500);
        channel.setChannelDTO(channelDTO);

        return channel;
    }

    //-----------------拼团-------------------//

    @Test
    public void normalPriceTest() {
        DealCtx ctx = buildCtx();
        ctx.setDealBaseReq(new DealBaseReq());
        ctx.setCostEffectivePinTuan(new CostEffectivePinTuan());
        ctx.getPriceContext().setNormalPrice(buildNormalPrice(ctx.getDealGroupBase()));

        ButtonBuilderChain chain = ButtonBuilderFactory.newButtonBuilderChain(NewBuyBarHelper.buildJoyChainConfig());
        chain.build(ctx);

        DealBuyBar buyBar = ctx.getBuyBar();

        assert buyBar.getBuyType() == DealBuyBar.BuyType.COMMON.type;
        assert buyBar.getBuyBtns().size() == 1;
        assert buyBar.getBuyBtns().get(0).getPriceStr().equals("66");
        assert buyBar.getBuyBtns().get(0).getBtnTitle().equals("立即抢购");
        assert CollectionUtils.isEmpty(buyBar.getBuyBtns().get(0).getBtnIcons());
    }

    @Test
    public void normalPromoPriceTest() {
        DealCtx ctx = buildCtx();
        ctx.setDealBaseReq(new DealBaseReq());
        ctx.setCostEffectivePinTuan(new CostEffectivePinTuan());
        ctx.getPriceContext().setNormalPrice(buildNormalPromoPrice(ctx.getDealGroupBase()));

        ButtonBuilderChain chain = ButtonBuilderFactory.newButtonBuilderChain(NewBuyBarHelper.buildJoyChainConfig());
        chain.build(ctx);

        DealBuyBar buyBar = ctx.getBuyBar();

        assert buyBar.getBuyType() == DealBuyBar.BuyType.COMMON.type;
        assert buyBar.getBuyBtns().size() == 1;
        assert buyBar.getBuyBtns().get(0).getPriceStr().equals("56");
        assert buyBar.getBuyBtns().get(0).getBtnTitle().equals("立即抢购");
        assert CollectionUtils.isNotEmpty(buyBar.getBuyBtns().get(0).getBtnIcons());
    }

    @Test
    public void holdMemberCardPriceTest() {
        DealCtx ctx = buildCtx();
        ctx.setDealBaseReq(new DealBaseReq());
        ctx.setCostEffectivePinTuan(new CostEffectivePinTuan());
        ctx.getPriceContext().setDcCardMemberCard(buildMemberCard(true));
        ctx.getPriceContext().setDcCardMemberPrice(buildMemberCardPrice(ctx.getDealGroupBase()));

        ButtonBuilderChain chain = ButtonBuilderFactory.newButtonBuilderChain(NewBuyBarHelper.buildJoyChainConfig());
        chain.build(ctx);

        DealBuyBar buyBar = ctx.getBuyBar();

        assert buyBar.getBuyType() == DealBuyBar.BuyType.DISCOUNTCARD.type;
        assert buyBar.getBuyBtns().size() == 1;
        assert buyBar.getBuyBtns().get(0).getPriceStr().equals("54");
        assert buyBar.getBuyBtns().get(0).getBtnTitle().equals("会员价");
        assert CollectionUtils.isNotEmpty(buyBar.getBuyBtns().get(0).getBtnIcons());
        assert buyBar.getBuyBtns().get(0).getBtnIcons().size() == 2;
    }

    @Test
    public void memberCardPriceTest() {
        DealCtx ctx = buildCtx();
        ctx.setDealBaseReq(new DealBaseReq());
        ctx.setCostEffectivePinTuan(new CostEffectivePinTuan());
        ctx.getPriceContext().setDcCardMemberCard(buildMemberCard(false));
        ctx.getPriceContext().setDcCardMemberPrice(buildMemberCardPrice(ctx.getDealGroupBase()));
        ctx.getPriceContext().setNormalPrice(buildNormalPrice(ctx.getDealGroupBase()));

        ButtonBuilderChain chain = ButtonBuilderFactory.newButtonBuilderChain(NewBuyBarHelper.buildJoyChainConfig());
        chain.build(ctx);

        DealBuyBar buyBar = ctx.getBuyBar();

        assert buyBar.getBuyType() == DealBuyBar.BuyType.DISCOUNTCARD.type;
        assert buyBar.getBuyBtns().size() == 2;
        assert buyBar.getBuyBtns().get(0).getPriceStr().equals("54");
        assert buyBar.getBuyBtns().get(0).getBtnTitle().equals("会员价");
        assert CollectionUtils.isNotEmpty(buyBar.getBuyBtns().get(0).getBtnIcons());
        assert buyBar.getBuyBtns().get(0).getBtnIcons().size() == 1;

        assert buyBar.getBuyBtns().get(1).getPriceStr().equals("66");
        assert buyBar.getBuyBtns().get(1).getBtnTitle().equals("团购价");
        assert CollectionUtils.isEmpty(buyBar.getBuyBtns().get(1).getBtnIcons());
    }

    @Test
    public void timesCardTest() {
        DealCtx ctx = buildCtx();
        ctx.setDealBaseReq(new DealBaseReq());
        ctx.setCostEffectivePinTuan(new CostEffectivePinTuan());
        ctx.getPriceContext().setNormalPrice(buildNormalPrice(ctx.getDealGroupBase()));
        ctx.setTimesCard(buildTimesCard());

        ButtonBuilderChain chain = ButtonBuilderFactory.newButtonBuilderChain(NewBuyBarHelper.buildJoyChainConfig());
        chain.build(ctx);

        DealBuyBar buyBar = ctx.getBuyBar();

        assert buyBar.getBuyType() == DealBuyBar.BuyType.TIMESCARD.type;
        assert buyBar.getBuyBtns().size() == 2;
        assert buyBar.getBuyBtns().get(0).getPriceStr().equals("55");
        assert buyBar.getBuyBtns().get(0).getBtnTitle().equals("购买3次");
        assert buyBar.getBuyBtns().get(0).getPricePostfix().equals("/次");
        assert CollectionUtils.isNotEmpty(buyBar.getBuyBtns().get(0).getBtnIcons());
        assert buyBar.getBuyBtns().get(0).getBtnIcons().get(0).getTitle().equals("随时退");

        assert buyBar.getBuyBtns().get(1).getPriceStr().equals("66");
        assert buyBar.getBuyBtns().get(1).getBtnTitle().equals("购买1次");
        assert CollectionUtils.isEmpty(buyBar.getBuyBtns().get(1).getBtnIcons());

    }

   /* @Test
    public void assembleDealTest() {
        DealCtx ctx = buildCtx();
        ctx.getPriceContext().setNormalPrice(buildNormalPrice(ctx.getDealGroupBase()));
        ctx.setPinProductBrief(buildAssembleDeal());

        ButtonBuilderChain chain = ButtonBuilderFactory.newButtonBuilderChain(NewBuyBarHelper.buildJoyChainConfig());
        chain.build(ctx);

        DealBuyBar buyBar = ctx.getBuyBar();

        assert buyBar.getBuyType() == DealBuyBar.BuyType.PINPOOL.type;
        assert buyBar.getBuyBtns().size() == 2;
        assert buyBar.getBuyBtns().get(0).getPriceStr().equals("44");
        assert buyBar.getBuyBtns().get(0).getBtnTitle().equals("3人拼团");
        assert CollectionUtils.isNotEmpty(buyBar.getBuyBtns().get(0).getBtnIcons());
        assert buyBar.getBuyBtns().get(0).getBtnIcons().get(0).getTitle().equals("已减22");

        assert buyBar.getBuyBtns().get(1).getPriceStr().equals("66");
        assert buyBar.getBuyBtns().get(1).getBtnTitle().equals("单独购买");
        assert CollectionUtils.isEmpty(buyBar.getBuyBtns().get(1).getBtnIcons());

    }
*/
    @Test
    public void idlePromoTest() {
        DealCtx ctx = buildCtx();
        ctx.setDealBaseReq(new DealBaseReq());
        ctx.setCostEffectivePinTuan(new CostEffectivePinTuan());
        ctx.getPriceContext().setIdlePromoPrice(buildIdlePromoPrice(ctx.getDealGroupBase()));

        ButtonBuilderChain chain = ButtonBuilderFactory.newButtonBuilderChain(NewBuyBarHelper.buildJoyChainConfig());
        chain.build(ctx);

        DealBuyBar buyBar = ctx.getBuyBar();

        assert buyBar.getBuyType() == DealBuyBar.BuyType.IDLE_PROMO.type;
        assert buyBar.getBuyBtns().size() == 2;
        assert buyBar.getBuyBtns().get(0).getPriceStr().equals("55");
        assert buyBar.getBuyBtns().get(0).getBtnTitle().equals("周二至周四可用");
        assert CollectionUtils.isNotEmpty(buyBar.getBuyBtns().get(0).getBtnIcons());
        assert buyBar.getBuyBtns().get(0).getBtnIcons().get(0).getTitle().equals("已减11");

        assert buyBar.getBuyBtns().get(1).getPriceStr().equals("66");
        assert buyBar.getBuyBtns().get(1).getBtnTitle().equals("立即抢购");
        assert CollectionUtils.isEmpty(buyBar.getBuyBtns().get(1).getBtnIcons());

    }

    @Test
    public void holdMemberCardAndTimesCardPriceTest() {
        DealCtx ctx = buildCtx();
        ctx.setDealBaseReq(new DealBaseReq());
        ctx.setCostEffectivePinTuan(new CostEffectivePinTuan());
        ctx.getPriceContext().setDcCardMemberCard(buildMemberCard(true));
        ctx.getPriceContext().setDcCardMemberPrice(buildMemberCardPrice(ctx.getDealGroupBase()));
        ctx.setTimesCard(buildTimesCard());

        ButtonBuilderChain chain = ButtonBuilderFactory.newButtonBuilderChain(NewBuyBarHelper.buildJoyChainConfig());
        chain.build(ctx);

        DealBuyBar buyBar = ctx.getBuyBar();

        assert buyBar.getBuyType() == DealBuyBar.BuyType.DISCOUNTCARD.type;
        assert buyBar.getBuyBtns().size() == 1;
        assert buyBar.getBuyBtns().get(0).getPriceStr().equals("54");
        assert buyBar.getBuyBtns().get(0).getBtnTitle().equals("会员价");
        assert CollectionUtils.isNotEmpty(buyBar.getBuyBtns().get(0).getBtnIcons());
        assert buyBar.getBuyBtns().get(0).getBtnIcons().size() == 2;
    }

    @Test
    public void holdMemberCardAndAssembleDealPriceTest() {
        DealCtx ctx = buildCtx();
        ctx.setDealBaseReq(new DealBaseReq());
        ctx.setCostEffectivePinTuan(new CostEffectivePinTuan());
        ctx.getPriceContext().setDcCardMemberCard(buildMemberCard(true));
        ctx.getPriceContext().setDcCardMemberPrice(buildMemberCardPrice(ctx.getDealGroupBase()));
        ctx.setPinProductBrief(buildAssembleDeal());

        ButtonBuilderChain chain = ButtonBuilderFactory.newButtonBuilderChain(NewBuyBarHelper.buildJoyChainConfig());
        chain.build(ctx);

        DealBuyBar buyBar = ctx.getBuyBar();

        assert buyBar.getBuyType() == DealBuyBar.BuyType.DISCOUNTCARD.type;
        assert buyBar.getBuyBtns().size() == 1;
        assert buyBar.getBuyBtns().get(0).getPriceStr().equals("54");
        assert buyBar.getBuyBtns().get(0).getBtnTitle().equals("会员价");
        assert CollectionUtils.isNotEmpty(buyBar.getBuyBtns().get(0).getBtnIcons());
        assert buyBar.getBuyBtns().get(0).getBtnIcons().size() == 2;
    }

    @Test
    public void memberCardAndTimesCardPriceTest() {
        DealCtx ctx = buildCtx();
        ctx.setDealBaseReq(new DealBaseReq());
        ctx.setCostEffectivePinTuan(new CostEffectivePinTuan());
        ctx.getPriceContext().setDcCardMemberCard(buildMemberCard(false));
        ctx.getPriceContext().setDcCardMemberPrice(buildMemberCardPrice(ctx.getDealGroupBase()));
        ctx.setTimesCard(buildTimesCard());

        ButtonBuilderChain chain = ButtonBuilderFactory.newButtonBuilderChain(NewBuyBarHelper.buildJoyChainConfig());
        chain.build(ctx);

        DealBuyBar buyBar = ctx.getBuyBar();

        assert buyBar.getBuyType() == DealBuyBar.BuyType.DISCOUNTCARD.type;
        assert buyBar.getBuyBtns().size() == 2;
        assert buyBar.getBuyBtns().get(0).getPriceStr().equals("54");
        assert buyBar.getBuyBtns().get(0).getBtnTitle().equals("会员价");
        assert CollectionUtils.isNotEmpty(buyBar.getBuyBtns().get(0).getBtnIcons());
        assert buyBar.getBuyBtns().get(0).getBtnIcons().size() == 1;

        assert buyBar.getBuyBtns().get(1).getPriceStr().equals("66");
        assert buyBar.getBuyBtns().get(1).getBtnTitle().equals("团购价");
        assert CollectionUtils.isEmpty(buyBar.getBuyBtns().get(1).getBtnIcons());
    }

    @Test
    public void memberCardAndAssembleDealPriceTest() {
        DealCtx ctx = buildCtx();
        ctx.setDealBaseReq(new DealBaseReq());
        ctx.setCostEffectivePinTuan(new CostEffectivePinTuan());
        ctx.getPriceContext().setDcCardMemberCard(buildMemberCard(false));
        ctx.getPriceContext().setDcCardMemberPrice(buildMemberCardPrice(ctx.getDealGroupBase()));
        ctx.setPinProductBrief(buildAssembleDeal());

        ButtonBuilderChain chain = ButtonBuilderFactory.newButtonBuilderChain(NewBuyBarHelper.buildJoyChainConfig());
        chain.build(ctx);

        DealBuyBar buyBar = ctx.getBuyBar();

        assert buyBar.getBuyType() == DealBuyBar.BuyType.DISCOUNTCARD.type;
        assert buyBar.getBuyBtns().size() == 2;
        assert buyBar.getBuyBtns().get(0).getPriceStr().equals("54");
        assert buyBar.getBuyBtns().get(0).getBtnTitle().equals("会员价");
        assert CollectionUtils.isNotEmpty(buyBar.getBuyBtns().get(0).getBtnIcons());
        assert buyBar.getBuyBtns().get(0).getBtnIcons().size() == 1;

        assert buyBar.getBuyBtns().get(1).getPriceStr().equals("66");
        assert buyBar.getBuyBtns().get(1).getBtnTitle().equals("团购价");
        assert CollectionUtils.isEmpty(buyBar.getBuyBtns().get(1).getBtnIcons());
    }

    @Test
    public void memberCardAndJoyCardPriceOldUserTest() {
        DealCtx ctx = buildCtx();
        ctx.setDealBaseReq(new DealBaseReq());
        ctx.setCostEffectivePinTuan(new CostEffectivePinTuan());
        ctx.getPriceContext().setDcCardMemberCard(buildMemberCard(false));
        ctx.getPriceContext().setDcCardMemberPrice(buildMemberCardPrice(ctx.getDealGroupBase()));
        ctx.setPinProductBrief(buildAssembleDeal());
        ctx.getPriceContext().setNewUser(false);

        ButtonBuilderChain chain = ButtonBuilderFactory.newButtonBuilderChain(NewBuyBarHelper.buildJoyChainConfig());
        chain.build(ctx);

        DealBuyBar buyBar = ctx.getBuyBar();

        assert buyBar.getBuyType() == DealBuyBar.BuyType.DISCOUNTCARD.type;
        assert buyBar.getBuyBtns().size() == 2;
        assert buyBar.getBuyBtns().get(0).getPriceStr().equals("54");
        assert buyBar.getBuyBtns().get(0).getBtnTitle().equals("会员价");
        assert CollectionUtils.isNotEmpty(buyBar.getBuyBtns().get(0).getBtnIcons());
        assert buyBar.getBuyBtns().get(0).getBtnIcons().size() == 1;

        assert buyBar.getBuyBtns().get(1).getPriceStr().equals("66");
        assert buyBar.getBuyBtns().get(1).getBtnTitle().equals("团购价");
        assert CollectionUtils.isEmpty(buyBar.getBuyBtns().get(1).getBtnIcons());
    }

}
