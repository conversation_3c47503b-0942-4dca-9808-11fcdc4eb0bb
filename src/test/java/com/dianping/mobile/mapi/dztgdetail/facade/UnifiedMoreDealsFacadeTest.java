package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.deal.idmapper.api.DealIdMapperService;
import com.dianping.deal.publishcategory.DealGroupPublishCategoryQueryService;
import com.dianping.deal.publishcategory.dto.ChannelDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.deal.publishcategory.enums.ChannelGroupEnum;
import com.dianping.deal.shop.dto.DealGroupDTO;
import com.dianping.deal.shop.dto.DealGroupSalesDTO;
import com.dianping.deal.shop.dto.ShopOnlineDealGroup;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MoreDealsWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PriceDisplayWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PromoWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.MoreDealsCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedMoreDealsReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.more.MoreItemDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.more.UnifiedMoreList;
import com.dianping.mobile.mapi.dztgdetail.entity.DealRecModuleOfflineList;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.ImageUtils;
import com.dianping.mobile.mapi.dztgdetail.util.SalesConfusionUtil;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.dealuser.price.display.api.model.BatchPriceRequest;
import com.sankuai.dealuser.price.display.api.model.ClientEnv;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.ProductIdentity;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedMoreDealsFacadeTest {

    private UnifiedMoreDealsFacade facade;

    private MoreDealsCtx ctx;

    private Map<Integer, DealGroupDTO> shopOnlineDealGroup;

    private Map<Integer, PromoDisplayDTO> promoMap;

    private Map<Integer, PriceDisplayDTO> priceDisplayMap;

    @InjectMocks
    private UnifiedMoreDealsFacade unifiedMoreDealsFacade;

    @Mock
    private MoreDealsWrapper moreDealsWrapper;

    @Mock
    private PromoWrapper promoWrapper;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Mock
    private DealIdMapperService dealIdMapperService;

    @Mock
    private PriceDisplayWrapper priceDisplayWrapper;

    @Mock
    private IMobileContext iMobileContext;

    @Mock
    private EnvCtx envCtx;

    @Mock
    private UnifiedMoreDealsReq request;

    @Before
    public void setUp() {
        facade = new UnifiedMoreDealsFacade();
        ctx = new MoreDealsCtx(null);
        ctx.setDpId(1);
        shopOnlineDealGroup = new HashMap<>();
        promoMap = new HashMap<>();
        priceDisplayMap = new HashMap<>();
    }

    // Helper method to invoke the private buildDpMoreItemDTO method using reflection
    private List<MoreItemDTO> invokePrivateBuildDpMoreItemDTO(Map<Integer, DealGroupDTO> shopOnlineDealGroup, Map<Integer, PromoDisplayDTO> promoMap, MoreDealsCtx ctx, Map<Integer, PriceDisplayDTO> priceDisplayMap) throws Exception {
        Method method = UnifiedMoreDealsFacade.class.getDeclaredMethod("buildDpMoreItemDTO", Map.class, Map.class, MoreDealsCtx.class, Map.class);
        method.setAccessible(true);
        return (List<MoreItemDTO>) method.invoke(facade, shopOnlineDealGroup, promoMap, ctx, priceDisplayMap);
    }

    private List<ProductIdentity> invokeTransform(ShopOnlineDealGroup shopOnlineDealGroup) throws Exception {
        Method method = UnifiedMoreDealsFacade.class.getDeclaredMethod("transform", ShopOnlineDealGroup.class);
        method.setAccessible(true);
        return (List<ProductIdentity>) method.invoke(unifiedMoreDealsFacade, shopOnlineDealGroup);
    }

    private MoreDealsCtx createMoreDealsCtxWithEnvCtx(long dpUserId) {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDpUserId(dpUserId);
        return new MoreDealsCtx(envCtx);
    }

    private BatchPriceRequest invokeBuildRequest(MoreDealsCtx moreDealsCtx, Map<Long, ShopOnlineDealGroup> products) throws Exception {
        Method method = UnifiedMoreDealsFacade.class.getDeclaredMethod("buildRequest", MoreDealsCtx.class, Map.class);
        method.setAccessible(true);
        return (BatchPriceRequest) method.invoke(unifiedMoreDealsFacade, moreDealsCtx, products);
    }

    /**
     * 测试 shopOnlineDealGroup 为空的情况
     */
    @Test
    public void testBuildDpMoreItemDTO_EmptyShopOnlineDealGroup() throws Throwable {
        // arrange
        shopOnlineDealGroup = Collections.emptyMap();
        // act
        List<MoreItemDTO> result = invokePrivateBuildDpMoreItemDTO(shopOnlineDealGroup, promoMap, ctx, priceDisplayMap);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 key 等于 ctx.getDpId() 的情况
     */
    @Test
    public void testBuildDpMoreItemDTO_KeyEqualsDpId() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        // Ensure price and marketPrice are not null
        dealGroupDTO.setPrice(BigDecimal.valueOf(100));
        dealGroupDTO.setMarketPrice(BigDecimal.valueOf(200));
        shopOnlineDealGroup.put(1, dealGroupDTO);
        // act
        List<MoreItemDTO> result = invokePrivateBuildDpMoreItemDTO(shopOnlineDealGroup, promoMap, ctx, priceDisplayMap);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 DealGroupDTO 为空的情况
     */
    @Test
    public void testBuildDpMoreItemDTO_NullDealGroupDTO() throws Throwable {
        // arrange
        shopOnlineDealGroup.put(2, null);
        // act
        List<MoreItemDTO> result = invokePrivateBuildDpMoreItemDTO(shopOnlineDealGroup, promoMap, ctx, priceDisplayMap);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 DealGroupSalesDTO 为空的情况
     */
    @Test
    public void testBuildDpMoreItemDTO_NullDealGroupSalesDTO() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        // Ensure price and marketPrice are not null
        dealGroupDTO.setPrice(BigDecimal.valueOf(100));
        dealGroupDTO.setMarketPrice(BigDecimal.valueOf(200));
        shopOnlineDealGroup.put(2, dealGroupDTO);
        // act
        List<MoreItemDTO> result = invokePrivateBuildDpMoreItemDTO(shopOnlineDealGroup, promoMap, ctx, priceDisplayMap);
        // assert
        assertEquals(1, result.size());
        assertEquals("已售0", result.get(0).getSalesDesc());
    }

    /**
     * 测试 salesTag 不包含 "+" 的情况
     */
    @Test
    public void testBuildDpMoreItemDTO_SalesTagWithoutPlus() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        // Ensure price and marketPrice are not null
        dealGroupDTO.setPrice(BigDecimal.valueOf(100));
        dealGroupDTO.setMarketPrice(BigDecimal.valueOf(200));
        DealGroupSalesDTO salesDTO = new DealGroupSalesDTO();
        salesDTO.setSalesTag("100");
        salesDTO.setSales(100);
        dealGroupDTO.setDealGroupSalesDTO(salesDTO);
        shopOnlineDealGroup.put(2, dealGroupDTO);
        // act
        List<MoreItemDTO> result = invokePrivateBuildDpMoreItemDTO(shopOnlineDealGroup, promoMap, ctx, priceDisplayMap);
        // assert
        assertEquals(1, result.size());
        assertEquals("100+", result.get(0).getSalesDesc());
    }

    /**
     * 测试 salesTag 包含 "+" 的情况
     */
    @Test
    public void testBuildDpMoreItemDTO_SalesTagWithPlus() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        // Ensure price and marketPrice are not null
        dealGroupDTO.setPrice(BigDecimal.valueOf(100));
        dealGroupDTO.setMarketPrice(BigDecimal.valueOf(200));
        DealGroupSalesDTO salesDTO = new DealGroupSalesDTO();
        salesDTO.setSalesTag("100+");
        salesDTO.setSales(100);
        dealGroupDTO.setDealGroupSalesDTO(salesDTO);
        shopOnlineDealGroup.put(2, dealGroupDTO);
        // act
        List<MoreItemDTO> result = invokePrivateBuildDpMoreItemDTO(shopOnlineDealGroup, promoMap, ctx, priceDisplayMap);
        // assert
        assertEquals(1, result.size());
        assertEquals("100+", result.get(0).getSalesDesc());
    }

    /**
     * 测试 priceDisplayMap 包含当前 key 的情况
     */
    @Test
    public void testBuildDpMoreItemDTO_PriceDisplayMapContainsKey() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        // Ensure price and marketPrice are not null
        dealGroupDTO.setPrice(BigDecimal.valueOf(100));
        dealGroupDTO.setMarketPrice(BigDecimal.valueOf(200));
        dealGroupDTO.setImageUrl("imageUrl");
        shopOnlineDealGroup.put(2, dealGroupDTO);
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        priceDisplayDTO.setPromoTag("promoTag");
        priceDisplayDTO.setPrice(BigDecimal.valueOf(90));
        priceDisplayMap.put(2, priceDisplayDTO);
        // act
        List<MoreItemDTO> result = invokePrivateBuildDpMoreItemDTO(shopOnlineDealGroup, promoMap, ctx, priceDisplayMap);
        // assert
        assertEquals(1, result.size());
        assertEquals("promoTag", result.get(0).getItemCampaignTag());
        assertEquals(90.0, result.get(0).getCurrentPrice(), 0.001);
    }

    /**
     * 测试 promoMap 包含当前 key 的情况
     */
    @Test
    public void testBuildDpMoreItemDTO_PromoMapContainsKey() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        // Ensure price and marketPrice are not null
        dealGroupDTO.setPrice(BigDecimal.valueOf(100));
        dealGroupDTO.setMarketPrice(BigDecimal.valueOf(200));
        dealGroupDTO.setImageUrl("imageUrl");
        shopOnlineDealGroup.put(2, dealGroupDTO);
        PromoDisplayDTO promoDisplayDTO = new PromoDisplayDTO();
        promoDisplayDTO.setTag("promoTag");
        promoDisplayDTO.setPromoAmount(BigDecimal.valueOf(10));
        promoDisplayDTO.setPriceLineThrough(true);
        promoMap.put(2, promoDisplayDTO);
        ChannelDTO channelDTO = new ChannelDTO();
        channelDTO.setChannelGroupId(ChannelGroupEnum.GENERAL_TYPE.getChannelGroupId());
        DealGroupChannelDTO channel = new DealGroupChannelDTO();
        channel.setChannelDTO(channelDTO);
        ctx.setChannel(channel);
        // act
        List<MoreItemDTO> result = invokePrivateBuildDpMoreItemDTO(shopOnlineDealGroup, promoMap, ctx, priceDisplayMap);
        // assert
        assertEquals(1, result.size());
        assertEquals("promoTag", result.get(0).getItemCampaignTag());
        assertEquals(90.0, result.get(0).getCurrentPrice(), 0.001);
    }

    /**
     * 测试 promoMap 不包含当前 key 的情况
     */
    @Test
    public void testBuildDpMoreItemDTO_PromoMapNotContainsKey() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        // Ensure price and marketPrice are not null
        dealGroupDTO.setPrice(BigDecimal.valueOf(100));
        dealGroupDTO.setMarketPrice(BigDecimal.valueOf(200));
        dealGroupDTO.setImageUrl("imageUrl");
        shopOnlineDealGroup.put(2, dealGroupDTO);
        // act
        List<MoreItemDTO> result = invokePrivateBuildDpMoreItemDTO(shopOnlineDealGroup, promoMap, ctx, priceDisplayMap);
        // assert
        assertEquals(1, result.size());
        assertNull(result.get(0).getItemCampaignTag());
        assertEquals(100.0, result.get(0).getCurrentPrice(), 0.001);
    }

    @Test
    public void testTransformDealGroupsIsEmpty() throws Throwable {
        ShopOnlineDealGroup shopOnlineDealGroup = new ShopOnlineDealGroup();
        shopOnlineDealGroup.setDealGroups(new ArrayList<>());
        List<ProductIdentity> result = invokeTransform(shopOnlineDealGroup);
        assertEquals(0, result.size());
    }

    @Test
    public void testTransformDealGroupsContainsOneElement() throws Throwable {
        ShopOnlineDealGroup shopOnlineDealGroup = new ShopOnlineDealGroup();
        List<DealGroupDTO> dealGroups = new ArrayList<>();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDealGroupId(1);
        dealGroups.add(dealGroupDTO);
        shopOnlineDealGroup.setDealGroups(dealGroups);
        List<ProductIdentity> result = invokeTransform(shopOnlineDealGroup);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getProductId());
    }

    @Test
    public void testTransformDealGroupsContainsMoreThanTwentyElements() throws Throwable {
        ShopOnlineDealGroup shopOnlineDealGroup = new ShopOnlineDealGroup();
        List<DealGroupDTO> dealGroups = new ArrayList<>();
        for (int i = 0; i < 25; i++) {
            DealGroupDTO dealGroupDTO = new DealGroupDTO();
            dealGroupDTO.setDealGroupId(i);
            dealGroups.add(dealGroupDTO);
        }
        shopOnlineDealGroup.setDealGroups(dealGroups);
        List<ProductIdentity> result = invokeTransform(shopOnlineDealGroup);
        assertEquals(20, result.size());
        for (int i = 0; i < 20; i++) {
            assertEquals(i, result.get(i).getProductId());
        }
    }

    @Test
    public void testBuildRequestNormalCase() throws Throwable {
        // arrange
        MoreDealsCtx moreDealsCtx = createMoreDealsCtxWithEnvCtx(456L);
        moreDealsCtx.setDpCityId(123);
        Map<Long, ShopOnlineDealGroup> products = new HashMap<>();
        ShopOnlineDealGroup dealGroup = new ShopOnlineDealGroup();
        dealGroup.setDealGroups(Lists.newArrayList(new DealGroupDTO()));
        products.put(1L, dealGroup);
        // act
        BatchPriceRequest request = invokeBuildRequest(moreDealsCtx, products);
        // assert
        assertNotNull(request);
        assertEquals(123, request.getClientEnv().getCityId());
        assertEquals(ClientTypeEnum.dp_mainApp_ios.getType(), request.getClientEnv().getClientType());
        assertEquals(456L, request.getUserId());
        assertEquals(400000, request.getScene());
        assertEquals(1, request.getLongShopId2ProductIds().size());
    }

    @Test
    public void testBuildRequestNullMoreDealsCtx() throws Throwable {
        // arrange
        Map<Long, ShopOnlineDealGroup> products = new HashMap<>();
        ShopOnlineDealGroup dealGroup = new ShopOnlineDealGroup();
        dealGroup.setDealGroups(Lists.newArrayList(new DealGroupDTO()));
        products.put(1L, dealGroup);
        // act
        try {
            invokeBuildRequest(null, products);
            fail("Expected NullPointerException to be thrown");
        } catch (Exception e) {
            // assert
            assertTrue(e instanceof java.lang.reflect.InvocationTargetException);
            assertTrue(e.getCause() instanceof NullPointerException);
        }
    }

    @Test
    public void testBuildRequestNullProducts() throws Throwable {
        // arrange
        MoreDealsCtx moreDealsCtx = createMoreDealsCtxWithEnvCtx(456L);
        // act
        try {
            invokeBuildRequest(moreDealsCtx, null);
            fail("Expected NullPointerException to be thrown");
        } catch (Exception e) {
            // assert
            assertTrue(e instanceof java.lang.reflect.InvocationTargetException);
            assertTrue(e.getCause() instanceof NullPointerException);
        }
    }

    @Test
    public void testBuildRequestEmptyProducts() throws Throwable {
        // arrange
        MoreDealsCtx moreDealsCtx = createMoreDealsCtxWithEnvCtx(456L);
        moreDealsCtx.setDpCityId(123);
        Map<Long, ShopOnlineDealGroup> products = new HashMap<>();
        // act
        BatchPriceRequest request = invokeBuildRequest(moreDealsCtx, products);
        // assert
        assertNotNull(request);
        assertEquals(123, request.getClientEnv().getCityId());
        assertEquals(ClientTypeEnum.dp_mainApp_ios.getType(), request.getClientEnv().getClientType());
        assertEquals(456L, request.getUserId());
        assertEquals(400000, request.getScene());
        assertEquals(0, request.getLongShopId2ProductIds().size());
    }

    @Test
    public void testBuildRequestBoundaryValues() throws Throwable {
        // arrange
        MoreDealsCtx moreDealsCtx = createMoreDealsCtxWithEnvCtx(Long.MAX_VALUE);
        moreDealsCtx.setDpCityId(0);
        Map<Long, ShopOnlineDealGroup> products = new HashMap<>();
        ShopOnlineDealGroup dealGroup = new ShopOnlineDealGroup();
        dealGroup.setDealGroups(Lists.newArrayList(new DealGroupDTO()));
        products.put(1L, dealGroup);
        // act
        BatchPriceRequest request = invokeBuildRequest(moreDealsCtx, products);
        // assert
        assertNotNull(request);
        assertEquals(0, request.getClientEnv().getCityId());
        assertEquals(ClientTypeEnum.dp_mainApp_ios.getType(), request.getClientEnv().getClientType());
        assertEquals(Long.MAX_VALUE, request.getUserId());
        assertEquals(400000, request.getScene());
        assertEquals(1, request.getLongShopId2ProductIds().size());
    }
}
