package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.common.model.FilterPlayActivityModel;
import com.dianping.mobile.mapi.dztgdetail.common.model.PlayActivityModel;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.DealGift;
import com.google.common.collect.Lists;
import com.meituan.service.mobile.mtthrift.callback.OctoThriftCallback;
import com.sankuai.beautycontent.beautylaunchapi.model.enums.ClientTypeEnum;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayCenterService;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteRequest;
import com.sankuai.mktplay.center.mkt.play.center.client.UserSourceEnum;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.concurrent.Future;

import static org.junit.Assert.*;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * @author: wuwenqiang
 * @create: 2024-08-07
 * @description:
 */
@RunWith(MockitoJUnitRunner.class)
public class PlayCenterWrapperTest {

    @Mock
    private DealCtx ctx;

    @Mock
    private EnvCtx envCtx;

    @Mock
    private PlayCenterService.AsyncIface playCenterServiceFuture;

    private String sceneExecutePlayResult;
    private String executePlayResult;

    @InjectMocks
    private PlayCenterWrapper playCenterWrapper;


    @Before
    public void setUp() {
        sceneExecutePlayResult = "{\n" +
                "    \"1020940683\": [\n" +
                "        {\n" +
                "            \"activityName\": \"联合营销联调001\",\n" +
                "            \"activityToken\": \"KxyH9Pg61INfJYm8LojVpuUSK\",\n" +
                "            \"startTime\": 1713801600000,\n" +
                "            \"endTime\": 1717171199000,\n" +
                "            \"status\": 3,\n" +
                "            \"playInfo\": {\n" +
                "                \"continueTime\": \"72000\",\n" +
                "                \"taskInfoList\": [\n" +
                "                    {\n" +
                "                        \"taskToken\": \"qO0mk8wjNRweIbmDQaWk\",\n" +
                "                        \"status\": 0,\n" +
                "                        \"targetValue\": \"1\",\n" +
                "                        \"progressValue\": \"0\",\n" +
                "                        \"eventType\": 17,\n" +
                "                        \"prizeCount\": 2,\n" +
                "                        \"prizeInfoList\": [\n" +
                "                            {\n" +
                "                                \"prizeAmount\": \"1800\",\n" +
                "                                \"prizeMinConsume\": \"0\",\n" +
                "                                \"prizeType\": 2,\n" +
                "                                \"prizeImage\": \"https://p0.meituan.net/bizoperate/054fdd190166638a275be62158899164151491.png\",\n" +
                "                                \"prizeName\": \"奖品001\"\n" +
                "                            },\n" +
                "                            {\n" +
                "                                \"prizeAmount\": \"1800\",\n" +
                "                                \"prizeMinConsume\": \"0\",\n" +
                "                                \"prizeType\": 2,\n" +
                "                                \"prizeImage\": \"https://p0.meituan.net/bizoperate/054fdd190166638a275be62158899164151491.png\",\n" +
                "                                \"prizeName\": \"奖品001\"\n" +
                "                            }\n" +
                "                        ],\n" +
                "                        \"materialInfoList\": [\n" +
                "                            {\n" +
                "                                \"id\": 18787,\n" +
                "                                \"activityId\": 28914,\n" +
                "                                \"materialId\": 10215580,\n" +
                "                                \"fieldType\": \"TEXT\",\n" +
                "                                \"fieldKey\": \"activityLink\",\n" +
                "                                \"fieldValue\": \"https://123.sankuai.com/\",\n" +
                "                                \"status\": \"EFFECTIVE\",\n" +
                "                                \"addTime\": 1713856111000,\n" +
                "                                \"modTime\": 1713856111000,\n" +
                "                                \"actionType\": \"TASK\",\n" +
                "                                \"actionId\": \"10215574\"\n" +
                "                            },\n" +
                "                            {\n" +
                "                                \"id\": 18788,\n" +
                "                                \"activityId\": 28914,\n" +
                "                                \"materialId\": 10215581,\n" +
                "                                \"fieldType\": \"TEXT\",\n" +
                "                                \"fieldKey\": \"activityUseLink\",\n" +
                "                                \"fieldValue\": \"imeituan://www.meituan.com/food/deal?did=417728990\",\n" +
                "                                \"status\": \"EFFECTIVE\",\n" +
                "                                \"addTime\": 1713856111000,\n" +
                "                                \"modTime\": 1713856111000,\n" +
                "                                \"actionType\": \"TASK\",\n" +
                "                                \"actionId\": \"10215574\"\n" +
                "                            }\n" +
                "                        ]\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"joinTime\": \"1714039503000\",\n" +
                "                \"activityName\": \"联合营销联调001\",\n" +
                "                \"activityToken\": \"KxyH9Pg61INfJYm8LojVpuUSK\",\n" +
                "                \"title\": \"联合营销联调001\",\n" +
                "                \"joinStatus\": 1,\n" +
                "                \"materialInfoList\": [\n" +
                "                    {\n" +
                "                        \"id\": 18785,\n" +
                "                        \"activityId\": 28914,\n" +
                "                        \"materialId\": 10215578,\n" +
                "                        \"fieldType\": \"SIMPLE_RICH_TEXT\",\n" +
                "                        \"fieldKey\": \"activityListBar\",\n" +
                "                        \"fieldValue\": \"[{\\\"context\\\":\\\"111\\\",\\\"color\\\":\\\"#000000\\\"}]\",\n" +
                "                        \"status\": \"EFFECTIVE\",\n" +
                "                        \"addTime\": 1713856111000,\n" +
                "                        \"modTime\": 1713856111000,\n" +
                "                        \"actionId\": \"\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 18786,\n" +
                "                        \"activityId\": 28914,\n" +
                "                        \"materialId\": 10215579,\n" +
                "                        \"fieldType\": \"TEXT\",\n" +
                "                        \"fieldKey\": \"activityRuleDesc\",\n" +
                "                        \"fieldValue\": \"222\",\n" +
                "                        \"status\": \"EFFECTIVE\",\n" +
                "                        \"addTime\": 1713856111000,\n" +
                "                        \"modTime\": 1713856111000,\n" +
                "                        \"actionId\": \"\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 18787,\n" +
                "                        \"activityId\": 28914,\n" +
                "                        \"materialId\": 10215580,\n" +
                "                        \"fieldType\": \"TEXT\",\n" +
                "                        \"fieldKey\": \"activityLink\",\n" +
                "                        \"fieldValue\": \"https://123.sankuai.com/\",\n" +
                "                        \"status\": \"EFFECTIVE\",\n" +
                "                        \"addTime\": 1713856111000,\n" +
                "                        \"modTime\": 1713856111000,\n" +
                "                        \"actionType\": \"TASK\",\n" +
                "                        \"actionId\": \"10215574\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 18788,\n" +
                "                        \"activityId\": 28914,\n" +
                "                        \"materialId\": 10215581,\n" +
                "                        \"fieldType\": \"TEXT\",\n" +
                "                        \"fieldKey\": \"activityUseLink\",\n" +
                "                        \"fieldValue\": \"imeituan://www.meituan.com/food/deal?did=417728990\",\n" +
                "                        \"status\": \"EFFECTIVE\",\n" +
                "                        \"addTime\": 1713856111000,\n" +
                "                        \"modTime\": 1713856111000,\n" +
                "                        \"actionType\": \"TASK\",\n" +
                "                        \"actionId\": \"10215574\"\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"currentTime\": 1714050227509,\n" +
                "                \"activityId\": 28914,\n" +
                "                \"subTitle\": \"活动副标题\",\n" +
                "                \"sceneProductType\": \"23\",\n" +
                "                \"startTime\": 1713801600000,\n" +
                "                \"endTime\": 1717171199000\n" +
                "            }\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        MockitoAnnotations.initMocks(this);
        executePlayResult = "{\"activityId\":29061,\"playId\":1000000000728008,\"activeRequestUnit\":[{\"rowId\":1022067202,\"properties\":{\"bizcode\":\"nib.general.groupbuy\",\"dealId\":\"1022067202\",\"departmentType\":\"8\"}},{\"rowId\":607521888,\"properties\":{\"bizcode\":\"nib.general.groupbuy\",\"poiId\":\"607521888\",\"departmentType\":\"8\"}}],\"subTitle\":\"活动副标题\",\"taskInfo\":{\"taskToken\":\"aX0m6JbCJqL9KU1k4pSq\",\"taskUserRecordToken\":\"M2kwNzB3dVh2dVFIMGpaSG9r\",\"taskEndTime\":1728500772085,\"prizeCount\":1,\"prizeInfoList\":[{\"id\":25906,\"prizeInfoId\":10217052,\"activityId\":29061,\"prizeType\":\"OPT_COUPON\",\"prizeItem\":\"602530686\",\"prizeMoney\":\"\",\"prizeMinConsume\":\"\",\"status\":\"EFFECTIVE\",\"addTime\":1714015975000,\"modTime\":1714272579000,\"image\":\"https://p0.meituan.net/bizoperate/4cc975c0b8a5cce3a52bde3a0b8b8e59211059.png\",\"prizeName\":\"到综赠品美团\"}]},\"materialInfoList\":[{\"id\":19054,\"activityId\":29061,\"materialId\":10217028,\"fieldType\":\"TEXT\",\"fieldKey\":\"activityAtmosphereImg\",\"fieldValue\":\"https://p0.meituan.net/bizoperate/e7043511e837b12156d0585c1854469919448.png\",\"status\":\"EFFECTIVE\",\"addTime\":1714014730000,\"modTime\":1714014730000,\"actionType\":\"TASK\",\"actionId\":\"10217026\"}]}";
    }

    /**
     * 测试querySceneExecutePlay成功场景
     */
    @Test
    public void testQuerySceneExecutePlay_Success() throws Exception {
        // arrange
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        doNothing().when(playCenterServiceFuture).sceneExecutePlay(any(), any());
        // act
        playCenterWrapper.querySceneExecutePlay(ctx, 123L);
        // assert
        verify(playCenterServiceFuture, times(1)).sceneExecutePlay(any(), any());
    }

    /**
     * 测试queryExecutePlay成功场景
     */
    @Test
    public void testQueryExecutePlay_Success() throws Exception {
        // arrange
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        doNothing().when(playCenterServiceFuture).executePlay(any(), any());
        // act
        playCenterWrapper.queryExecutePlay(ctx, 123L);
        // assert
        verify(playCenterServiceFuture, times(1)).executePlay(any(), any());
    }

    @Test
    public void testConvertPlayActivityModelMapEmptyString() {
        Map<Long, List<PlayActivityModel>> result = playCenterWrapper.convertPlayActivityModelMap("");
        assertTrue(result.isEmpty());
    }

    @Test
    public void testConvertPlayActivityModelMapNull() throws Throwable {
        Map<Long, List<PlayActivityModel>> result = playCenterWrapper.convertPlayActivityModelMap(null);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testConvertPlayActivityModelMapInvalidJson() throws Throwable {
        Map<Long, List<PlayActivityModel>> result = playCenterWrapper.convertPlayActivityModelMap("invalid json");
        assertTrue(result.isEmpty());
    }

    @Test
    public void testConvertPlayActivityModelMapNullKeyOrValue() throws Throwable {
        String json = "{\"1\":null,\"2\":[]}";
        Map<Long, List<PlayActivityModel>> result = playCenterWrapper.convertPlayActivityModelMap(json);
        assertFalse("Expected key 1 to not exist due to null value", result.containsKey(1L));
        assertTrue("Expected key 2 to exist", result.containsKey(2L));
        assertTrue("Expected list for key 2 to be empty", result.get(2L).isEmpty());
    }

    @Test
    public void testConvertPlayActivityModelMap() {
        Map<Long, List<PlayActivityModel>> map = playCenterWrapper.convertPlayActivityModelMap(sceneExecutePlayResult);
        assertNotNull(map);
        assertEquals(1, map.size());
    }

    @Test
    public void testConvert2FilterPlayActivityModel_Success() {
        FilterPlayActivityModel result = playCenterWrapper.convert2FilterPlayActivityModel(executePlayResult);
        assertNotNull(result);
        assertEquals("29061", result.getActivityId());
        assertNotNull(result.getTaskInfo());
        assertNotNull(result.getMaterialInfoList());
    }




    /**
     * 测试formatDate方法，输入时间戳为0
     */
    @Test
    public void testFormatDateZeroTimestamp() {
        long timestamp = 0L; // 1970.01.01 08:00:00 GMT+8
        String result = PlayCenterWrapper.formatDate(timestamp);
        Assert.assertEquals("1970.01.01", result);
    }

    /**
     * 测试formatDate方法，输入一个负数时间戳
     */
    @Test
    public void testFormatDateNegativeTimestamp() {
        long timestamp = -1609502400000L; // 时间戳为负，代表1970年1月1日之前的日期
        String result = PlayCenterWrapper.formatDate(timestamp);
        Assert.assertNotNull(result);
    }


    @Test
    public void testBuildGiftsForNewCustomActivity() {
        String str ="{\n" +
                "   \"continueTime\": \"86400\",\n" +
                "   \"hasParticipate\": true,\n" +
                "   \"taskInfoList\": [\n" +
                "       {\n" +
                "           \"taskToken\": \"kj0TeWLb6pcELLqk1yeO\",\n" +
                "           \"status\": 1,\n" +
                "           \"targetValue\": \"0\",\n" +
                "           \"progressValue\": \"0\",\n" +
                "           \"eventType\": 17,\n" +
                "           \"prizeCount\": 1,\n" +
                "           \"prizeInfoList\": [\n" +
                "               {\n" +
                "                   \"prizeAmount\": \"1000\",\n" +
                "                   \"prizeMinConsume\": \"0\",\n" +
                "                   \"prizeType\": 2,\n" +
                "                   \"prizeImage\": \"http://p0.meituan.net/bizoperate/defb40aa44789acb139ee22cb0831965400867.jpg\",\n" +
                "                   \"prizeName\": \"无门槛10元券\",\n" +
                "                   \"couponType\": \"0\",\n" +
                "                   \"prizeItem\": \"1557234342\"\n" +
                "               }\n" +
                "           ],\n" +
                "           \"materialInfoList\": [\n" +
                "               \n" +
                "           ]\n" +
                "       },\n" +
                "       {\n" +
                "           \"taskToken\": \"LD1Cq9NkCdw3pd9eJYY0\",\n" +
                "           \"status\": 0,\n" +
                "           \"targetValue\": \"1\",\n" +
                "           \"progressValue\": \"0\",\n" +
                "           \"eventType\": 17,\n" +
                "           \"prizeCount\": 1,\n" +
                "           \"prizeInfoList\": [\n" +
                "               {\n" +
                "                   \"prizeAmount\": \"500\",\n" +
                "                   \"prizeMinConsume\": \"0\",\n" +
                "                   \"prizeType\": 2,\n" +
                "                   \"prizeImage\": \"http://p0.meituan.net/bizoperate/defb40aa44789acb139ee22cb0831965400867.jpg\",\n" +
                "                   \"prizeName\": \"无门槛5元券\",\n" +
                "                   \"couponType\": \"0\",\n" +
                "                   \"prizeItem\": \"1932627459\"\n" +
                "               }\n" +
                "           ],\n" +
                "           \"materialInfoList\": [\n" +
                "               \n" +
                "           ]\n" +
                "       },\n" +
                "       {\n" +
                "           \"taskToken\": \"mC2Uz8SAsJLMmmdM4s9q\",\n" +
                "           \"status\": 0,\n" +
                "           \"targetValue\": \"2\",\n" +
                "           \"progressValue\": \"0\",\n" +
                "           \"eventType\": 17,\n" +
                "           \"prizeCount\": 1,\n" +
                "           \"prizeInfoList\": [\n" +
                "               {\n" +
                "                   \"prizeAmount\": \"500\",\n" +
                "                   \"prizeMinConsume\": \"3500\",\n" +
                "                   \"prizeType\": 2,\n" +
                "                   \"prizeImage\": \"http://p0.meituan.net/bizoperate/defb40aa44789acb139ee22cb0831965400867.jpg\",\n" +
                "                   \"prizeName\": \"满30-5元券\",\n" +
                "                   \"couponType\": \"0\",\n" +
                "                   \"prizeItem\": \"1542028478\"\n" +
                "               }\n" +
                "           ],\n" +
                "           \"materialInfoList\": [\n" +
                "               \n" +
                "           ]\n" +
                "       }\n" +
                "   ],\n" +
                "   \"activityId\": 119217,\n" +
                "   \"canParticipate\": false,\n" +
                "   \"activityToken\": \"ND3zFHsvw48TQQMxl94MMREan\",\n" +
                "   \"materialInfoList\": [\n" +
                "       {\n" +
                "           \"id\": 14029,\n" +
                "           \"activityId\": 119217,\n" +
                "           \"materialId\": 11063814,\n" +
                "           \"fieldType\": \"TEXT\",\n" +
                "           \"fieldKey\": \"activityJumpUrl\",\n" +
                "           \"status\": \"EFFECTIVE\",\n" +
                "           \"addTime\": 1726842655000,\n" +
                "           \"modTime\": 1726842655000,\n" +
                "           \"actionId\": \"\"\n" +
                "       },\n" +
                "       {\n" +
                "           \"id\": 14030,\n" +
                "           \"activityId\": 119217,\n" +
                "           \"materialId\": 11044855,\n" +
                "           \"fieldType\": \"TEXT\",\n" +
                "           \"fieldKey\": \"activityJumpUrl\",\n" +
                "           \"status\": \"EFFECTIVE\",\n" +
                "           \"addTime\": 1726842655000,\n" +
                "           \"modTime\": 1726842655000,\n" +
                "           \"actionId\": \"\"\n" +
                "       },\n" +
                "       {\n" +
                "           \"id\": 14031,\n" +
                "           \"activityId\": 119217,\n" +
                "           \"materialId\": 11093192,\n" +
                "           \"fieldType\": \"TEXT\",\n" +
                "           \"fieldKey\": \"activityJumpUrl\",\n" +
                "           \"status\": \"EFFECTIVE\",\n" +
                "           \"addTime\": 1726842655000,\n" +
                "           \"modTime\": 1726842655000,\n" +
                "           \"actionId\": \"\"\n" +
                "       }\n" +
                "   ]\n" +
                "}";
        List<DealGift> result = PlayCenterWrapper.buildGiftsForNewCustomActivity(str);
        Assert.assertNotNull(result);
    }

    /**
     * 测试formatDate方法，输入一个负数时间戳
     */
    @Test
    public void testbuildGiftsForNewCustomActivityNotNull() {
    String str ="{\n" +
            "    \"continueTime\": \"86400\",\n" +
            "    \"hasParticipate\":true,\n" +
            "    \"taskInfoList\": [\n" +
            "        {\n" +
            "            \"taskToken\": \"kj0TeWLb6pcELLqk1yeO\",\n" +
            "            \"status\": 1,\n" +
            "            \"targetValue\": \"0\",\n" +
            "            \"progressValue\": \"0\",\n" +
            "            \"eventType\": 17,\n" +
            "            \"prizeCount\": 1,\n" +
            "            \"prizeInfoList\": [\n" +
            "                {\n" +
            "                    \"prizeAmount\": \"1000\",\n" +
            "                    \"prizeMinConsume\": \"0\",\n" +
            "                    \"prizeType\": 2,\n" +
            "                    \"prizeImage\": \"http://p0.meituan.net/bizoperate/defb40aa44789acb139ee22cb0831965400867.jpg\",\n" +
            "                    \"prizeName\": \"无门槛10元券\",\n" +
            "                    \"couponType\": \"0\",\n" +
            "                    \"prizeItem\": \"1557234342\"\n" +
            "                }\n" +
            "            ],\n" +
            "            \"materialInfoList\": [\n" +
            "                \n" +
            "            ]\n" +
            "        },\n" +
            "        {\n" +
            "            \"taskToken\": \"LD1Cq9NkCdw3pd9eJYY0\",\n" +
            "            \"status\": 0,\n" +
            "            \"targetValue\": \"1\",\n" +
            "            \"progressValue\": \"0\",\n" +
            "            \"eventType\": 17,\n" +
            "            \"prizeCount\": 1,\n" +
            "            \"prizeInfoList\": [\n" +
            "                {\n" +
            "                    \"prizeAmount\": \"500\",\n" +
            "                    \"prizeMinConsume\": \"0\",\n" +
            "                    \"prizeType\": 2,\n" +
            "                    \"prizeImage\": \"http://p0.meituan.net/bizoperate/defb40aa44789acb139ee22cb0831965400867.jpg\",\n" +
            "                    \"prizeName\": \"无门槛5元券\",\n" +
            "                    \"couponType\": \"0\",\n" +
            "                    \"prizeItem\": \"1932627459\"\n" +
            "                }\n" +
            "            ],\n" +
            "            \"materialInfoList\": [\n" +
            "                \n" +
            "            ]\n" +
            "        },\n" +
            "        {\n" +
            "            \"taskToken\": \"mC2Uz8SAsJLMmmdM4s9q\",\n" +
            "            \"status\": 0,\n" +
            "            \"targetValue\": \"2\",\n" +
            "            \"progressValue\": \"0\",\n" +
            "            \"eventType\": 17,\n" +
            "            \"prizeCount\": 1,\n" +
            "            \"prizeInfoList\": [\n" +
            "                {\n" +
            "                    \"prizeAmount\": \"500\",\n" +
            "                    \"prizeMinConsume\": \"3500\",\n" +
            "                    \"prizeType\": 2,\n" +
            "                    \"prizeImage\": \"http://p0.meituan.net/bizoperate/defb40aa44789acb139ee22cb0831965400867.jpg\",\n" +
            "                    \"prizeName\": \"满30-5元券\",\n" +
            "                    \"couponType\": \"0\",\n" +
            "                    \"prizeItem\": \"1542028478\"\n" +
            "                }\n" +
            "            ],\n" +
            "            \"materialInfoList\": [\n" +
            "                \n" +
            "            ]\n" +
            "        }\n" +
            "    ],\n" +
            "    \"activityId\": 119217,\n" +
            "    \"canParticipate\": false,\n" +
            "    \"activityToken\": \"ND3zFHsvw48TQQMxl94MMREan\",\n" +
            "    \"materialInfoList\": [\n" +
            "        {\n" +
            "            \"id\": 14029,\n" +
            "            \"activityId\": 119217,\n" +
            "            \"materialId\": 11063814,\n" +
            "            \"fieldType\": \"TEXT\",\n" +
            "            \"fieldKey\": \"activityJumpUrl\",\n" +
            "            \"status\": \"EFFECTIVE\",\n" +
            "            \"addTime\": 1726842655000,\n" +
            "            \"modTime\": 1726842655000,\n" +
            "            \"actionId\": \"\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 14030,\n" +
            "            \"activityId\": 119217,\n" +
            "            \"materialId\": 11044855,\n" +
            "            \"fieldType\": \"TEXT\",\n" +
            "            \"fieldKey\": \"activityJumpUrl\",\n" +
            "            \"status\": \"EFFECTIVE\",\n" +
            "            \"addTime\": 1726842655000,\n" +
            "            \"modTime\": 1726842655000,\n" +
            "            \"actionId\": \"\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 14031,\n" +
            "            \"activityId\": 119217,\n" +
            "            \"materialId\": 11093192,\n" +
            "            \"fieldType\": \"TEXT\",\n" +
            "            \"fieldKey\": \"activityJumpUrl\",\n" +
            "            \"status\": \"EFFECTIVE\",\n" +
            "            \"addTime\": 1726842655000,\n" +
            "            \"modTime\": 1726842655000,\n" +
            "            \"actionId\": \"\"\n" +
            "        }\n" +
            "    ]\n" +
            "}";
        List<DealGift> result = PlayCenterWrapper.buildGiftsForNewCustomActivity(str);
        Assert.assertNotNull(result);
    }


    @Test
    public void testBuildNewCustomExecutePlay() {
        when(ctx.getRealMtCityId()).thenReturn(1);
//        when(ctx.isMt()).thenReturn(true);
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.getClientType()).thenReturn(ClientTypeEnum.IOS.ordinal());
        when(ctx.getRequestExtParam()).thenReturn("sampleToken");
//        when(ctx.getRequestSource()).thenReturn(String.valueOf(RequestSourceEnum.NEW_CUSTOMER_ACTIVITY));

        PlayCenterWrapper pa = new PlayCenterWrapper();
        Map<String, String> result = pa.buildNewCustomExecutePlay(ctx);

        assertNotNull(result);
        assertEquals("1", result.get("cityId"));
        assertEquals(String.valueOf(0), result.get("platformType"));
        assertEquals("1", result.get("osType"));
        assertNotNull(result.get("activeRequestUnit"));
//        assertEquals("sampleToken", result.get("activityToken"));
    }


    @Test
    public void getUserIdByType_MT() {
        EnvCtx envCtx1 = new EnvCtx();
        envCtx1.setMtUserId(123L);
        envCtx1.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        ctx = new DealCtx(envCtx1);
        long id = playCenterWrapper.getUserIdByType(ctx);
        System.out.println(id);
        assertEquals(123, id);
    }

    @Test
    public void getUserIdByType_DP() {
        EnvCtx envCtx1 = new EnvCtx();
        envCtx1.setDpUserId(456L);
        envCtx1.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        ctx = new DealCtx(envCtx1);
        long id = playCenterWrapper.getUserIdByType(ctx);
        System.out.println(id);
        assertEquals(456L, id);
    }


}
