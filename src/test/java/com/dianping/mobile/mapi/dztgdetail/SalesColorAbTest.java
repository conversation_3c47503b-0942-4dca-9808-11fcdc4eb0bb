package com.dianping.mobile.mapi.dztgdetail;

import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2021-01-08-6:11 下午
 */
public class SalesColorAbTest extends GenericTest {
    @Autowired
    private DouHuBiz douHuBiz;

/*    @Test
    public void test() {
        DealCtx dealCtx = buildCtx();
        String module = dealCtx.isMt() ? "MTSalesColorExp" : "DPSalesColorExp";
        ModuleAbConfig moduleAbConfig = douHuBiz.getAbExpResult(dealCtx, module);
        System.out.println(moduleAbConfig.toString());
        String saleDesc = "半年消费12345";
        if (moduleAbConfig != null && moduleAbConfig.getConfigs().get(0).isUseNewStyle()) {
            System.out.println(JsonLabelUtil.salesColorJson(dealCtx.isMt(), saleDesc, saleDesc));
        }
    }*/

    private DealCtx buildCtx() {
        EnvCtx env = new EnvCtx();
        DealCtx dealCtx = new DealCtx(env);
        env.setUnionId("38d47d5897d64590bae5404cfe5e1a17a155753697429183702");
        env.setClientType(ClientTypeEnum.dp_web.getType());
        return dealCtx;
    }

    @Test
    @Ignore
    public void testShopAtTop() {
        DealCtx dealCtx = buildCtx();
        String module = "MTShopAtTopExp";
        ModuleAbConfig moduleAbConfig = douHuBiz.getAbExpResult(dealCtx, module);
        System.out.println(moduleAbConfig.toString());
        if (moduleAbConfig != null && moduleAbConfig.getConfigs().get(0).isUseNewStyle()) {
            System.out.println("新版");
        }
    }
}

