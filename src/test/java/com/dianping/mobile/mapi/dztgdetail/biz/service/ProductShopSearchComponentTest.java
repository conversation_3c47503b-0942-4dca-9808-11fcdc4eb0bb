package com.dianping.mobile.mapi.dztgdetail.biz.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.general.unified.search.api.productshopsearch.dto.ProductShopSearchDTO;
import com.dianping.general.unified.search.api.productshopsearch.dto.RetrievalInfoDTO;
import com.dianping.general.unified.search.api.productshopsearch.enums.ProductShopRetrievalExtendFieldEnum;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductShopSearchComponentTest {

    private ProductShopSearchComponent productShopSearchComponent = new ProductShopSearchComponent();

    @Test
    public void testGetRetrievalValWhenRetrievalInfosIsNull() throws Throwable {
        ProductShopSearchDTO dto = new ProductShopSearchDTO();
        dto.setRetrievalInfos(null);
        Method method = ProductShopSearchComponent.class.getDeclaredMethod("getRetrievalVal", ProductShopSearchDTO.class, ProductShopRetrievalExtendFieldEnum.class);
        method.setAccessible(true);
        String result = (String) method.invoke(productShopSearchComponent, dto, ProductShopRetrievalExtendFieldEnum.TOP_PERFORMING_SPU_ID);
        assertNull(result);
    }

    @Test
    public void testGetRetrievalValWhenNoMatchedRetrievalExtendField() throws Throwable {
        ProductShopSearchDTO dto = new ProductShopSearchDTO();
        RetrievalInfoDTO retrievalInfoDTO = new RetrievalInfoDTO();
        retrievalInfoDTO.setRetrievalExtendField("other");
        dto.setRetrievalInfos(Collections.singletonList(retrievalInfoDTO));
        Method method = ProductShopSearchComponent.class.getDeclaredMethod("getRetrievalVal", ProductShopSearchDTO.class, ProductShopRetrievalExtendFieldEnum.class);
        method.setAccessible(true);
        String result = (String) method.invoke(productShopSearchComponent, dto, ProductShopRetrievalExtendFieldEnum.TOP_PERFORMING_SPU_ID);
        assertNull(result);
    }

    @Test
    public void testGetRetrievalValWhenValuesIsNull() throws Throwable {
        ProductShopSearchDTO dto = new ProductShopSearchDTO();
        RetrievalInfoDTO retrievalInfoDTO = new RetrievalInfoDTO();
        retrievalInfoDTO.setRetrievalExtendField(ProductShopRetrievalExtendFieldEnum.TOP_PERFORMING_SPU_ID.getCode());
        retrievalInfoDTO.setValues(null);
        dto.setRetrievalInfos(Collections.singletonList(retrievalInfoDTO));
        Method method = ProductShopSearchComponent.class.getDeclaredMethod("getRetrievalVal", ProductShopSearchDTO.class, ProductShopRetrievalExtendFieldEnum.class);
        method.setAccessible(true);
        String result = (String) method.invoke(productShopSearchComponent, dto, ProductShopRetrievalExtendFieldEnum.TOP_PERFORMING_SPU_ID);
        assertNull(result);
    }

    @Test
    public void testGetRetrievalValWhenValuesIsNotNull() throws Throwable {
        ProductShopSearchDTO dto = new ProductShopSearchDTO();
        RetrievalInfoDTO retrievalInfoDTO = new RetrievalInfoDTO();
        retrievalInfoDTO.setRetrievalExtendField(ProductShopRetrievalExtendFieldEnum.TOP_PERFORMING_SPU_ID.getCode());
        retrievalInfoDTO.setValues(Arrays.asList("value1", "value2"));
        dto.setRetrievalInfos(Collections.singletonList(retrievalInfoDTO));
        Method method = ProductShopSearchComponent.class.getDeclaredMethod("getRetrievalVal", ProductShopSearchDTO.class, ProductShopRetrievalExtendFieldEnum.class);
        method.setAccessible(true);
        String result = (String) method.invoke(productShopSearchComponent, dto, ProductShopRetrievalExtendFieldEnum.TOP_PERFORMING_SPU_ID);
        assertEquals("value1", result);
    }
}
