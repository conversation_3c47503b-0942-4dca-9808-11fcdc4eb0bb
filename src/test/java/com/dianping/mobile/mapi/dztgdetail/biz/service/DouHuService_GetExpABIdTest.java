package com.dianping.mobile.mapi.dztgdetail.biz.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.ExpResultConfig;
import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DouHuService_GetExpABIdTest {

    @InjectMocks
    private DouHuService douHuService;

    @Mock
    private EnvCtx envCtx;

    @Mock
    private ExpResultConfig expResultConfig;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private ModuleAbConfig createModuleAbConfigInstance() throws Exception {
        Constructor<ModuleAbConfig> constructor = ModuleAbConfig.class.getDeclaredConstructor();
        constructor.setAccessible(true);
        return constructor.newInstance();
    }

    /**
     * 测试 moduleAbConfig 为 null 的情况
     */
    @Test
    public void testGetExpABIdNullModuleAbConfig() {
        assertNull(douHuService.getExpABId(null));
    }

    /**
     * 测试 moduleAbConfig 的 configs 为空的情况
     */
    @Test
    public void testGetExpABIdEmptyConfigs() {
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        assertNull(douHuService.getExpABId(moduleAbConfig));
    }

    /**
     * 测试 configs 的第一个元素为 null 的情况
     */
    @Test
    public void testGetExpABIdNullFirstConfig() {
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setConfigs(Arrays.asList(null, new AbConfig()));
        assertNull(douHuService.getExpABId(moduleAbConfig));
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testGetExpABIdNormal() {
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpId("expId");
        abConfig.setExpResult("expResult");
        moduleAbConfig.setConfigs(Arrays.asList(abConfig));
        assertEquals("expId_expResult", douHuService.getExpABId(moduleAbConfig));
    }

    @Test
    public void testEnvironmentPassEnvCtxIsNull() throws Throwable {
        Method method = DouHuService.class.getDeclaredMethod("environmentPass", EnvCtx.class, String.class, boolean.class);
        method.setAccessible(true);
        assertFalse((boolean) method.invoke(douHuService, null, "0.5.7", false));
    }

    @Test
    public void testEnvironmentPassDztgClientTypeEnumIsMeituanApp() throws Throwable {
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_APP);
        when(envCtx.isIos()).thenReturn(true);
        when(envCtx.isAndroid()).thenReturn(true);
        when(envCtx.getVersion()).thenReturn("12.11.400");
        Method method = DouHuService.class.getDeclaredMethod("environmentPass", EnvCtx.class, String.class, boolean.class);
        method.setAccessible(true);
        assertTrue((boolean) method.invoke(douHuService, envCtx, "0.5.7", false));
    }

    @Test
    public void testEnvironmentPassDztgClientTypeEnumIsDianpingApp() throws Throwable {
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.DIANPING_APP);
        when(envCtx.isIos()).thenReturn(true);
        when(envCtx.isAndroid()).thenReturn(true);
        when(envCtx.getVersion()).thenReturn("11.4.10");
        Method method = DouHuService.class.getDeclaredMethod("environmentPass", EnvCtx.class, String.class, boolean.class);
        method.setAccessible(true);
        assertTrue((boolean) method.invoke(douHuService, envCtx, "0.5.7", false));
    }

    @Test
    public void testEnvironmentPassDztgClientTypeEnumIsThirdPlatform() throws Throwable {
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.THIRD_PLATFORM);
        // Mocking the behavior of isThirdPlatform() method
        when(envCtx.isThirdPlatform()).thenReturn(true);
        Method method = DouHuService.class.getDeclaredMethod("environmentPass", EnvCtx.class, String.class, boolean.class);
        method.setAccessible(true);
        assertTrue((boolean) method.invoke(douHuService, envCtx, "0.5.7", false));
    }

    @Test
    public void testEnvironmentPassInNonAppClientList() throws Throwable {
        Method method = DouHuService.class.getDeclaredMethod("environmentPass", EnvCtx.class, String.class, boolean.class);
        method.setAccessible(true);
        assertTrue((boolean) method.invoke(douHuService, envCtx, "0.5.7", true));
    }

    @Test
    public void testEnvironmentPassOther() throws Throwable {
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.UNKNOWN);
        Method method = DouHuService.class.getDeclaredMethod("environmentPass", EnvCtx.class, String.class, boolean.class);
        method.setAccessible(true);
        assertFalse((boolean) method.invoke(douHuService, envCtx, "0.5.7", false));
    }

    @Test
    public void testGetTabStyleExpResultConfigNotEnableStyle() throws Throwable {
        ModuleAbConfig moduleAbConfig = createModuleAbConfigInstance();
        when(expResultConfig.isEnableStyle()).thenReturn(false);
        assertNull(douHuService.getTabStyle(expResultConfig, 1, moduleAbConfig));
    }

    @Test
    public void testGetTabStyleAllPassStyleIsNotBlank() throws Throwable {
        ModuleAbConfig moduleAbConfig = createModuleAbConfigInstance();
        when(expResultConfig.isEnableStyle()).thenReturn(true);
        when(expResultConfig.getAllPassStyle()).thenReturn("allPassStyle");
        assertEquals("allPassStyle", douHuService.getTabStyle(expResultConfig, 1, moduleAbConfig));
    }

    @Test
    public void testGetTabStyleKey2StyleIsEmpty() throws Throwable {
        ModuleAbConfig moduleAbConfig = createModuleAbConfigInstance();
        when(expResultConfig.isEnableStyle()).thenReturn(true);
        when(expResultConfig.getAllPassStyle()).thenReturn("");
        when(expResultConfig.getKey2Style()).thenReturn(Collections.emptyMap());
        assertNull(douHuService.getTabStyle(expResultConfig, 1, moduleAbConfig));
    }
}
