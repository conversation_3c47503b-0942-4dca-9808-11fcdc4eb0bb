package com.dianping.mobile.mapi.dztgdetail.biz;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.CityServiceWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealFields;
import com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam;
import com.meituan.service.mobile.group.geo.bean.CityInfo;
import com.meituan.service.mobile.prometheus.model.DealModel;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealFieldChangerBizAlterRangeTest {

    @InjectMocks
    private DealFieldChangerBiz dealFieldChangerBiz;

    @Mock
    private CityServiceWrapper cityServiceWrapper;

    private DealModel deal;

    private CityInfo currentCity;

    @Mock
    private MtCommonParam mtCommonParam;

    @Before
    public void setUp() {
        deal = new DealModel();
        currentCity = new CityInfo();
    }

    private void invokeAlterRange(DealModel deal, int currentCityId) throws Exception {
        Method method = DealFieldChangerBiz.class.getDeclaredMethod("alterRange", DealModel.class, int.class);
        method.setAccessible(true);
        method.invoke(dealFieldChangerBiz, deal, currentCityId);
    }

    private void initializeMocks() {
        MockitoAnnotations.initMocks(this);
        // Mocking cityServiceWrapper.getCityById to return a valid CityInfo object
    }

    @Test
    public void testAlterRangeDealIsWuliu() throws Throwable {
        deal.setCtype(1);
        invokeAlterRange(deal, 1);
        // 物流单不调用 cityServiceWrapper.getCityById
        verify(cityServiceWrapper, never()).getCityById(anyInt());
        // 验证 range 未被修改
        assertEquals(null, deal.getRange());
    }

    @Test
    public void testAlterRangeCityIdsIsEmpty() throws Throwable {
        deal.setCtype(0);
        deal.setCityIds(Collections.emptyList());
        String oldRange = "Old Range";
        deal.setRange(oldRange);
        invokeAlterRange(deal, 1);
        // 验证 cityServiceWrapper.getCityById 被调用
        verify(cityServiceWrapper, times(1)).getCityById(1);
        // 验证 range 未被修改
        assertEquals(oldRange, deal.getRange());
    }

    @Test
    public void testAlterRangeCurrentCityIsNullAndCityIdsSizeGreaterThanOne() throws Throwable {
        deal.setCtype(0);
        deal.setCityIds(Arrays.asList(1, 2));
        when(cityServiceWrapper.getCityById(1)).thenReturn(null);
        invokeAlterRange(deal, 1);
        // 验证 cityServiceWrapper.getCityById 被调用
        verify(cityServiceWrapper, times(1)).getCityById(1);
        // 验证 range 被修改为 "多城市"
        assertEquals("多城市", deal.getRange());
    }

    @Test
    public void testAlterRangeCurrentCityIsNullAndCityIdsSizeEqualsOne() throws Throwable {
        deal.setCtype(0);
        deal.setCityIds(Collections.singletonList(1));
        when(cityServiceWrapper.getCityById(1)).thenReturn(null);
        String oldRange = "Old Range";
        deal.setRange(oldRange);
        invokeAlterRange(deal, 1);
        // 验证 cityServiceWrapper.getCityById 被调用
        verify(cityServiceWrapper, times(1)).getCityById(1);
        // 验证 range 未被修改
        assertEquals(oldRange, deal.getRange());
    }

    @Test
    public void testAlterRangeCityIdsContainsGlobalCityId() throws Throwable {
        deal.setCtype(0);
        deal.setCityIds(Collections.singletonList(DealFields.GLOBAL_CITY_ID));
        when(cityServiceWrapper.getCityById(1)).thenReturn(currentCity);
        invokeAlterRange(deal, 1);
        // 验证 cityServiceWrapper.getCityById 被调用
        verify(cityServiceWrapper, times(1)).getCityById(1);
        // 验证 range 被修改为 "全国"
        assertEquals("全国", deal.getRange());
    }

    @Test
    public void testAlterRangeCityIdsNotContainsGlobalCityIdAndSizeGreaterThanOne() throws Throwable {
        deal.setCtype(0);
        deal.setCityIds(Arrays.asList(1, 2));
        when(cityServiceWrapper.getCityById(1)).thenReturn(currentCity);
        invokeAlterRange(deal, 1);
        // 验证 cityServiceWrapper.getCityById 被调用
        verify(cityServiceWrapper, times(1)).getCityById(1);
        // 验证 range 被修改为 "当前城市名等"
        assertEquals(currentCity.getName() + "等", deal.getRange());
    }

    @Test
    public void testAlterRangeCityIdsNotContainsGlobalCityIdAndSizeEqualsOne() throws Throwable {
        deal.setCtype(0);
        deal.setCityIds(Collections.singletonList(1));
        when(cityServiceWrapper.getCityById(1)).thenReturn(currentCity);
        String oldRange = "Old Range";
        deal.setRange(oldRange);
        invokeAlterRange(deal, 1);
        // 验证 cityServiceWrapper.getCityById 被调用
        verify(cityServiceWrapper, times(1)).getCityById(1);
        // 验证 range 未被修改
        assertEquals(oldRange, deal.getRange());
    }

    @Test
    public void testChangeDealDealIsNull() throws Throwable {
        initializeMocks();
        dealFieldChangerBiz.changeDeal(null, 1, Arrays.asList(DealFields.DEAL_FIELD_VOICE), mtCommonParam);
        verifyZeroInteractions(mtCommonParam);
    }

    @Test
    public void testChangeDealRowFieldsIsEmpty() throws Throwable {
        initializeMocks();
        DealModel deal = new DealModel();
        dealFieldChangerBiz.changeDeal(deal, 1, Collections.emptyList(), mtCommonParam);
        verifyZeroInteractions(mtCommonParam);
    }

    @Test
    public void testChangeDealRowFieldsNotContainsVoiceAndMenu() throws Throwable {
        initializeMocks();
        DealModel deal = new DealModel();
        deal.setNobooking(1);
        when(mtCommonParam.getVersion()).thenReturn("5.2");
        dealFieldChangerBiz.changeDeal(deal, 1, Arrays.asList(DealFields.DEAL_FIELD_VOICE), mtCommonParam);
        verify(mtCommonParam, times(1)).getVersion();
    }

    @Test
    public void testChangeDealRowFieldsContainsVoiceAndNotContainsMenu() throws Throwable {
        initializeMocks();
        DealModel deal = new DealModel();
        deal.setNobooking(1);
        when(mtCommonParam.getVersion()).thenReturn("5.2");
        dealFieldChangerBiz.changeDeal(deal, 1, Arrays.asList(DealFields.DEAL_FIELD_VOICE, DealFields.DEAL_FIELD_MENU), mtCommonParam);
        verify(mtCommonParam, times(1)).getVersion();
    }

    @Test
    public void testChangeDealRowFieldsContainsMenuAndNotContainsVoice() throws Throwable {
        initializeMocks();
        DealModel deal = new DealModel();
        deal.setNobooking(1);
        when(mtCommonParam.getVersion()).thenReturn("5.2");
        dealFieldChangerBiz.changeDeal(deal, 1, Arrays.asList(DealFields.DEAL_FIELD_VOICE, DealFields.DEAL_FIELD_MENU), mtCommonParam);
        verify(mtCommonParam, times(1)).getVersion();
    }

    @Test
    public void testChangeDealRowFieldsContainsVoiceAndMenu() throws Throwable {
        initializeMocks();
        DealModel deal = new DealModel();
        deal.setNobooking(1);
        when(mtCommonParam.getVersion()).thenReturn("5.2");
        dealFieldChangerBiz.changeDeal(deal, 1, Arrays.asList(DealFields.DEAL_FIELD_VOICE, DealFields.DEAL_FIELD_MENU), mtCommonParam);
        verify(mtCommonParam, times(1)).getVersion();
    }

    @Test
    public void testChangeDealNobookingIsNot1() throws Throwable {
        initializeMocks();
        DealModel deal = new DealModel();
        deal.setNobooking(1);
        // Ensure version is greater than or equal to "5.1"
        when(mtCommonParam.getVersion()).thenReturn("5.1");
        dealFieldChangerBiz.changeDeal(deal, 1, Arrays.asList(DealFields.DEAL_FIELD_VOICE, DealFields.DEAL_FIELD_MENU), mtCommonParam);
        verify(mtCommonParam, times(1)).getVersion();
    }

    @Test
    public void testChangeDealNobookingIs1ButVersionLessThan51() throws Throwable {
        initializeMocks();
        DealModel deal = new DealModel();
        deal.setNobooking(1);
        when(mtCommonParam.getVersion()).thenReturn("5.0");
        dealFieldChangerBiz.changeDeal(deal, 1, Arrays.asList(DealFields.DEAL_FIELD_VOICE, DealFields.DEAL_FIELD_MENU), mtCommonParam);
        verify(mtCommonParam, times(1)).getVersion();
    }

    @Test
    public void testChangeDealNobookingIs1AndVersionGreaterThan51() throws Throwable {
        initializeMocks();
        DealModel deal = new DealModel();
        deal.setNobooking(1);
        when(mtCommonParam.getVersion()).thenReturn("5.2");
        dealFieldChangerBiz.changeDeal(deal, 1, Arrays.asList(DealFields.DEAL_FIELD_VOICE, DealFields.DEAL_FIELD_MENU), mtCommonParam);
        verify(mtCommonParam, times(1)).getVersion();
    }
}
