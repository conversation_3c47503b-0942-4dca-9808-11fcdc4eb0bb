package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.mobile.mapi.dztgdetail.common.enums.ShopCategoryEnum;
import com.sankuai.athena.biz.Response;
import com.sankuai.zdc.tag.apply.api.PoiTagDisplayRPCService;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import com.sankuai.zdc.tag.apply.dto.FindSceneDisplayTagRequest;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class ShopTagWrapper_GetWearableNailShopTagTest {

    @InjectMocks
    private ShopTagWrapper shopTagWrapper;

    @Mock
    private PoiTagDisplayRPCService poiTagDisplayRPCService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 getWearableNailShopTag 方法，当 dpShopId 为空
     */
    @Test
    public void testGetWearableNailShopTagWithNullDpShopId() throws Throwable {
        ShopCategoryEnum result = shopTagWrapper.getWearableNailShopTag(null);
        assertEquals(ShopCategoryEnum.UNKNOWN, result);
    }

    /**
     * 测试 getWearableNailShopTag 方法，当 dpShopId 小于等于0
     */
    @Test
    public void testGetWearableNailShopTagWithNegativeDpShopId() throws Throwable {
        ShopCategoryEnum result = shopTagWrapper.getWearableNailShopTag(-1L);
        assertEquals(ShopCategoryEnum.UNKNOWN, result);
    }

    /**
     * 测试 getWearableNailShopTag 方法，当响应为空
     */
    @Test
    public void testGetWearableNailShopTagWithNullResponse() throws Throwable {
        when(poiTagDisplayRPCService.findSceneDisplayTagOfDp(any(FindSceneDisplayTagRequest.class))).thenReturn(null);
        ShopCategoryEnum result = shopTagWrapper.getWearableNailShopTag(1L);
        assertEquals(ShopCategoryEnum.UNKNOWN, result);
    }

    /**
     * 测试 getWearableNailShopTag 方法，当响应不成功
     */
    @Test
    public void testGetWearableNailShopTagWithUnsuccessfulResponse() throws Throwable {
        Response<Map<Long, List<DisplayTagDto>>> response = new Response<>(1, "failed");
        when(poiTagDisplayRPCService.findSceneDisplayTagOfDp(any(FindSceneDisplayTagRequest.class))).thenReturn(response);
        ShopCategoryEnum result = shopTagWrapper.getWearableNailShopTag(1L);
        assertEquals(ShopCategoryEnum.UNKNOWN, result);
    }

    /**
     * 测试 getWearableNailShopTag 方法，当响应成功，但是数据为空
     */
    @Test
    public void testGetWearableNailShopTagWithEmptyData() throws Throwable {
        Response<Map<Long, List<DisplayTagDto>>> response = new Response<>(0, "success", new HashMap<>());
        when(poiTagDisplayRPCService.findSceneDisplayTagOfDp(any(FindSceneDisplayTagRequest.class))).thenReturn(response);
        ShopCategoryEnum result = shopTagWrapper.getWearableNailShopTag(1L);
        assertEquals(ShopCategoryEnum.UNKNOWN, result);
    }
}
