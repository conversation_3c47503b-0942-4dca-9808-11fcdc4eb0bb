package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.*;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.After;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealAttrHelper_GetAttributeValuesTest {

    private MockedStatic<Lion> lionMock;

    private boolean invokePrivateMethod(AttributeDTO attributeDTO) throws Exception {
        Method method = DealAttrHelper.class.getDeclaredMethod("isWuyoutongAttribute", AttributeDTO.class);
        method.setAccessible(true);
        return (boolean) method.invoke(null, attributeDTO);
    }

    /**
     * 测试getAttributeValues方法，当attributeDtoList为null时
     */
    @Test
    public void testGetAttributeValuesWhenListIsNull() throws Throwable {
        // arrange
        List<AttributeDTO> attributeDtoList = null;
        String key = "testKey";
        // act
        List<String> result = DealAttrHelper.getAttributeValues(attributeDtoList, key);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试getAttributeValues方法，当attributeDtoList为空列表时
     */
    @Test
    public void testGetAttributeValuesWhenListIsEmpty() throws Throwable {
        // arrange
        List<AttributeDTO> attributeDtoList = Collections.emptyList();
        String key = "testKey";
        // act
        List<String> result = DealAttrHelper.getAttributeValues(attributeDtoList, key);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试getAttributeValues方法，当attributeDtoList不为空，但没有name属性与key相等的AttributeDTO对象时
     */
    @Test
    public void testGetAttributeValuesWhenNoMatchedName() throws Throwable {
        // arrange
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("otherKey");
        attributeDTO.setValue(Arrays.asList("value1", "value2"));
        List<AttributeDTO> attributeDtoList = Arrays.asList(attributeDTO);
        String key = "testKey";
        // act
        List<String> result = DealAttrHelper.getAttributeValues(attributeDtoList, key);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试getAttributeValues方法，当attributeDtoList不为空，且有name属性与key相等的AttributeDTO对象时
     */
    @Test
    public void testGetAttributeValuesWhenMatchedName() throws Throwable {
        // arrange
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("testKey");
        attributeDTO.setValue(Arrays.asList("value1", "value2"));
        List<AttributeDTO> attributeDtoList = Arrays.asList(attributeDTO);
        String key = "testKey";
        // act
        List<String> result = DealAttrHelper.getAttributeValues(attributeDtoList, key);
        // assert
        assertEquals(Arrays.asList("value1", "value2"), result);
    }

    /**
     * 测试getAttributeValuesV2方法，当attributeDtoList为null时
     */
    @Test
    public void testGetAttributeValuesV2WhenAttributeDtoListIsNull() {
        // arrange
        List<AttrDTO> attributeDtoList = null;
        String key = "testKey";
        // act
        List<String> result = DealAttrHelper.getAttributeValuesV2(attributeDtoList, key);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试getAttributeValuesV2方法，当attributeDtoList不为空，但没有AttrDTO对象的name属性与key相等时
     */
    @Test
    public void testGetAttributeValuesV2WhenNoMatchedKey() {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("otherKey");
        attrDTO.setValue(Arrays.asList("value1", "value2"));
        List<AttrDTO> attributeDtoList = Arrays.asList(attrDTO);
        String key = "testKey";
        // act
        List<String> result = DealAttrHelper.getAttributeValuesV2(attributeDtoList, key);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试getAttributeValuesV2方法，当attributeDtoList不为空，且有AttrDTO对象的name属性与key相等时
     */
    @Test
    public void testGetAttributeValuesV2WhenMatchedKey() {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("testKey");
        attrDTO.setValue(Arrays.asList("value1", "value2"));
        List<AttrDTO> attributeDtoList = Arrays.asList(attrDTO);
        String key = "testKey";
        // act
        List<String> result = DealAttrHelper.getAttributeValuesV2(attributeDtoList, key);
        // assert
        assertEquals(Arrays.asList("value1", "value2"), result);
    }

    @Test
    public void testIsWuyoutongAttribute_NullAttribute() throws Throwable {
        AttributeDTO attributeDTO = null;
        boolean result = invokePrivateMethod(attributeDTO);
        assertFalse(result);
    }

    @Test
    public void testIsWuyoutongAttribute_EmptyName() throws Throwable {
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("");
        attributeDTO.setValue(Arrays.asList("value1"));
        boolean result = invokePrivateMethod(attributeDTO);
        assertFalse(result);
    }

    @Test
    public void testIsWuyoutongAttribute_EmptyValue() throws Throwable {
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("standardDealGroupKey");
        attributeDTO.setValue(new ArrayList<>());
        boolean result = invokePrivateMethod(attributeDTO);
        assertFalse(result);
    }

    @Test
    public void testIsWuyoutongAttribute_WrongName() throws Throwable {
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("wrongName");
        attributeDTO.setValue(Arrays.asList("value1"));
        boolean result = invokePrivateMethod(attributeDTO);
        assertFalse(result);
    }
}
