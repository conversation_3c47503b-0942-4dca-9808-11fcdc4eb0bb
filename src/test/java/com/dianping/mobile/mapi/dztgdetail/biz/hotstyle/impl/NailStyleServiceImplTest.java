package com.dianping.mobile.mapi.dztgdetail.biz.hotstyle.impl;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.HaimaWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ImmersiveImageWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.RecommendServiceWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetHotNailStyleRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetNailStyleImageRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ActionTextVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ExhibitImageItemVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImageUrlVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.nailstyle.HotNailStyleModuleVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.nailstyle.NailStyleItemVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.nailstyle.OrderNailStyleImageVO;
import com.dianping.mobile.mapi.dztgdetail.entity.HaiMaNailFilterConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.HotNailStyleConfig;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class NailStyleServiceImplTest {

    @InjectMocks
    private NailStyleServiceImpl nailStyleService;

    @Mock
    private HaimaWrapper haimaWrapper;

    @Mock
    private RecommendServiceWrapper recommendServiceWrapper;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Mock
    private DouHuBiz douHuBiz;

    @Mock
    private ImmersiveImageWrapper immersiveImageWrapper;

    @Mock
    private Future future;

    private MockedStatic<LionConfigUtils> lionConfigUtilsMocked;

    private MockedStatic<Lion> lionMocked;

    private GetNailStyleImageRequest request;
    private EnvCtx envCtx;

    @Before
    public void setUp() {
        request = new GetNailStyleImageRequest();
        request.setDealGroupId(1L);
        request.setShopId(1L);
        request.setCityId(1);

        envCtx = new EnvCtx();

        lionConfigUtilsMocked = mockStatic(LionConfigUtils.class);
        lionMocked = mockStatic(Lion.class);
    }

    @After
    public void teardown() {
        lionConfigUtilsMocked.close();
        lionMocked.close();
    }

    /**
     * 正常情况
     */
    @Test
    public void testQueryOrderNailStyleNormal() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
        dealGroupCategoryDTO.setCategoryId(1L);
        dealGroupDTO.setCategory(dealGroupCategoryDTO);

        // 设置 AB 实验结果为 "a"
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("a");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));

        lionConfigUtilsMocked.when(() -> LionConfigUtils.allowDisplayHotNailModule(any(), any())).thenReturn(true);
        when(queryCenterWrapper.preDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(future);
        when(queryCenterWrapper.getDealGroupDTO(future)).thenReturn(dealGroupDTO);
        when(douHuBiz.getAbByUnionId(any(), any(), anyBoolean())).thenReturn(moduleAbConfig);
        Map<String, Integer> orderNailStyleNumMap = Maps.newHashMap();
        orderNailStyleNumMap.put("start", 0);
        orderNailStyleNumMap.put("limit", 1);
        orderNailStyleNumMap.put("display", 1);
        lionConfigUtilsMocked.when(LionConfigUtils::getOrderNailStyleNumConfig).thenReturn(orderNailStyleNumMap);
        
        List<NailStyleItemVO> nailStyleItems = Lists.newArrayList();
        NailStyleItemVO item = new NailStyleItemVO();
        item.setItemId("123");
        item.setPicUrl("xxxx");
        nailStyleItems.add(item);
        when(immersiveImageWrapper.geDealtRelatedStyleImage(any(), any(), anyInt())).thenReturn(nailStyleItems);

        // act
        OrderNailStyleImageVO result = nailStyleService.queryOrderNailStyle(request, envCtx);

        // assert
        assertNotNull(result);
    }

    /**
     * 测试商家在黑名单中的情况
     */
    @Test
    public void testQueryHotNailStyle_ShopInBlackList() throws Throwable {
        // arrange
        GetHotNailStyleRequest request = new GetHotNailStyleRequest();
        request.setShopId(1L);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);

        // 设置 AB 实验结果为 "a"
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("a");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        when(douHuBiz.getAbByUnionId(any(), any(), anyBoolean())).thenReturn(moduleAbConfig);

        // 设置商家在黑名单中
        lionConfigUtilsMocked.when(() -> LionConfigUtils.isHotNailModuleBlackShop(anyLong(), anyBoolean()))
                .thenReturn(true);
        // act
        HotNailStyleModuleVO result = nailStyleService.queryHotNailStyle(request, envCtx);
        // assert
        assertNull(result);
    }

    /**
     * 测试团单信息为空的情况
     */
    @Test
    public void testQueryHotNailStyle_DealGroupIsNull() throws Throwable {
        // arrange
        GetHotNailStyleRequest request = new GetHotNailStyleRequest();
        request.setDealGroupId(1L);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        // 设置 AB 实验结果为 "a"
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("a");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        when(douHuBiz.getAbByUnionId(any(), any(), anyBoolean())).thenReturn(moduleAbConfig);

        // 设置商家在黑名单中
        lionConfigUtilsMocked.when(() -> LionConfigUtils.isHotNailModuleBlackShop(anyLong(), anyBoolean())).thenReturn(false);
        // 设置团单信息为空
        Future mockFuture = mock(Future.class);
        when(queryCenterWrapper.preDealGroupDTO(any(QueryByDealGroupIdRequest.class)))
                .thenReturn(mockFuture);
        when(queryCenterWrapper.getDealGroupDTO(mockFuture)).thenReturn(null);
        // act
        HotNailStyleModuleVO result = nailStyleService.queryHotNailStyle(request, envCtx);
        // assert
        assertNull(result);
    }

    /**
     * 测试团单不是美甲团单的情况
     */
    @Test
    public void testQueryHotNailStyle_NotBeautyNailDealGroup() throws Throwable {
        // arrange
        GetHotNailStyleRequest request = new GetHotNailStyleRequest();
        request.setDealGroupId(1L);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
        dealGroupCategoryDTO.setCategoryId(999L);
        dealGroupDTO.setCategory(dealGroupCategoryDTO);
        // 设置 AB 实验结果为 "a"
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("a");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        when(douHuBiz.getAbByUnionId(any(), any(), anyBoolean())).thenReturn(moduleAbConfig);

        // 设置商家在黑名单中
        lionConfigUtilsMocked.when(() -> LionConfigUtils.isHotNailModuleBlackShop(anyLong(), anyBoolean())).thenReturn(false);
        lionConfigUtilsMocked.when(() -> LionConfigUtils.allowDisplayHotNailModule(any(), any())).thenReturn(false);
        // 设置团单信息
        Future mockFuture = mock(Future.class);
        when(queryCenterWrapper.preDealGroupDTO(any(QueryByDealGroupIdRequest.class)))
                .thenReturn(mockFuture);
        when(queryCenterWrapper.getDealGroupDTO(mockFuture)).thenReturn(dealGroupDTO);
        // act
        HotNailStyleModuleVO result = nailStyleService.queryHotNailStyle(request, envCtx);
        // assert
        assertNull(result);
    }

    /**
     * 测试海马配置的热门款式为空的情况
     */
    @Test
    public void testQueryHotNailStyle_HaiMaNailFilterConfigsIsEmpty() throws Throwable {
        // arrange
        GetHotNailStyleRequest request = new GetHotNailStyleRequest();
        request.setDealGroupId(1L);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
        dealGroupCategoryDTO.setCategoryId(505L);
        dealGroupDTO.setCategory(dealGroupCategoryDTO);
        // 设置 AB 实验结果为 "a"
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("a");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        when(douHuBiz.getAbByUnionId(any(), any(), anyBoolean())).thenReturn(moduleAbConfig);

        // 设置商家在黑名单中
        lionConfigUtilsMocked.when(() -> LionConfigUtils.isHotNailModuleBlackShop(anyLong(), anyBoolean())).thenReturn(false);
        lionConfigUtilsMocked.when(() -> LionConfigUtils.allowDisplayHotNailModule(any(), any())).thenReturn(true);
        // 设置团单信息
        Future mockFuture = mock(Future.class);
        when(queryCenterWrapper.preDealGroupDTO(any(QueryByDealGroupIdRequest.class)))
                .thenReturn(mockFuture);
        when(queryCenterWrapper.getDealGroupDTO(mockFuture)).thenReturn(dealGroupDTO);
        // 设置海马配置的热门款式为空
        when(haimaWrapper.queryHotBeautyNailConfig()).thenReturn(new ArrayList<>());
        // act
        HotNailStyleModuleVO result = nailStyleService.queryHotNailStyle(request, envCtx);
        // assert
        assertNull(result);
    }

    /**
     * 测试 AB 实验结果为 "a" 的情况
     */
    @Test
    public void testQueryHotNailStyle_ABTestResultA() throws Throwable {
        // arrange
        GetHotNailStyleRequest request = new GetHotNailStyleRequest();
        request.setDealGroupId(1L);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
        dealGroupCategoryDTO.setCategoryId(505L);
        dealGroupDTO.setCategory(dealGroupCategoryDTO);
        // 设置商家在黑名单中
        lionConfigUtilsMocked.when(() -> LionConfigUtils.isHotNailModuleBlackShop(anyLong(), anyBoolean())).thenReturn(false);
        lionConfigUtilsMocked.when(() -> LionConfigUtils.allowDisplayHotNailModule(any(), any())).thenReturn(true);
        // 团单信息
        Future mockFuture = mock(Future.class);
        when(queryCenterWrapper.preDealGroupDTO(any(QueryByDealGroupIdRequest.class)))
                .thenReturn(mockFuture);
        when(queryCenterWrapper.getDealGroupDTO(mockFuture)).thenReturn(dealGroupDTO);

        HotNailStyleConfig hotNailStyleConfig = new HotNailStyleConfig();
        ActionTextVO actionTextVO = new ActionTextVO();
        hotNailStyleConfig.setMtMoreStyleUrl("%s-%s");
        hotNailStyleConfig.setDpMoreStyleUrl("%s-%s");
        hotNailStyleConfig.setActionText(actionTextVO);
        lionMocked.when(() -> Lion.getBean(anyString(), anyString(), any())).thenReturn(hotNailStyleConfig);
        // 设置 AB 实验结果为 "a"
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("a");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        when(douHuBiz.getAbByUnionId(any(), any(), anyBoolean())).thenReturn(moduleAbConfig);
        // 设置海马配置的热门款式
        List<HaiMaNailFilterConfig> nailFilterConfigs = new ArrayList<>();
        HaiMaNailFilterConfig haiMaNailFilterConfig = new HaiMaNailFilterConfig();
        haiMaNailFilterConfig.setName("热门款式");
        haiMaNailFilterConfig.setId("1");
        haiMaNailFilterConfig.setFilterIds("1,2,3");
        haiMaNailFilterConfig.setSecondTabId("100075023");
        haiMaNailFilterConfig.setIcon("https://example.com/icon.png");
        nailFilterConfigs.add(haiMaNailFilterConfig);
        nailFilterConfigs.add(haiMaNailFilterConfig);
        nailFilterConfigs.add(haiMaNailFilterConfig);
        nailFilterConfigs.add(haiMaNailFilterConfig);

        when(haimaWrapper.queryHotBeautyNailConfig()).thenReturn(nailFilterConfigs);


        // act
        HotNailStyleModuleVO result = nailStyleService.queryHotNailStyle(request, envCtx);
        // assert
        assertNotNull(result);
        assertEquals("theme", result.getDisplayStyle());
    }

    /**
     * 测试 AB 实验结果为 "b" 的情况
     */
    @Test
    public void testQueryHotNailStyle_ABTestResultB() throws Throwable {
        // arrange
        GetHotNailStyleRequest request = new GetHotNailStyleRequest();
        request.setDealGroupId(1L);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
        dealGroupCategoryDTO.setCategoryId(505L);
        dealGroupDTO.setCategory(dealGroupCategoryDTO);
        // 设置商家在黑名单中
        lionConfigUtilsMocked.when(() -> LionConfigUtils.isHotNailModuleBlackShop(anyLong(), anyBoolean())).thenReturn(false);
        lionConfigUtilsMocked.when(() -> LionConfigUtils.allowDisplayHotNailModule(any(), any())).thenReturn(true);
        // 团单信息
        Future mockFuture = mock(Future.class);
        when(queryCenterWrapper.preDealGroupDTO(any(QueryByDealGroupIdRequest.class)))
                .thenReturn(mockFuture);
        when(queryCenterWrapper.getDealGroupDTO(mockFuture)).thenReturn(dealGroupDTO);

        HotNailStyleConfig hotNailStyleConfig = new HotNailStyleConfig();
        ActionTextVO actionTextVO = new ActionTextVO();
        hotNailStyleConfig.setMtMoreStyleUrl("%s-%s");
        hotNailStyleConfig.setDpMoreStyleUrl("%s-%s");
        hotNailStyleConfig.setActionText(actionTextVO);
        lionMocked.when(() -> Lion.getBean(anyString(), anyString(), any())).thenReturn(hotNailStyleConfig);
        // 设置 AB 实验结果为 "a"
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("b");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        when(douHuBiz.getAbByUnionId(any(), any(), anyBoolean())).thenReturn(moduleAbConfig);
        // 设置海马配置的热门款式
        List<HaiMaNailFilterConfig> nailFilterConfigs = new ArrayList<>();
        HaiMaNailFilterConfig haiMaNailFilterConfig = new HaiMaNailFilterConfig();
        haiMaNailFilterConfig.setName("热门款式");
        haiMaNailFilterConfig.setId("1");
        haiMaNailFilterConfig.setFilterIds("1,2,3");
        haiMaNailFilterConfig.setSecondTabId("100075023");
        haiMaNailFilterConfig.setIcon("https://example.com/icon.png");
        nailFilterConfigs.add(haiMaNailFilterConfig);
        nailFilterConfigs.add(haiMaNailFilterConfig);
        nailFilterConfigs.add(haiMaNailFilterConfig);
        nailFilterConfigs.add(haiMaNailFilterConfig);

        when(haimaWrapper.queryHotBeautyNailConfig()).thenReturn(nailFilterConfigs);
        ImmersiveImageVO recommendStyleImage = new ImmersiveImageVO();
        ExhibitImageItemVO item = new ExhibitImageItemVO();
        item.setUrls(Collections.singletonList(ImageUrlVO.builder().url("http://test.jpg").build()));
        List<ExhibitImageItemVO> items = new ArrayList<>(20);
        items.add(item);
        for (int i = 0; i < 19; i ++) {
            items.add(new ExhibitImageItemVO());
        }
        recommendStyleImage.setItems(items);
        // 设置推荐的热门美甲款式
        when(recommendServiceWrapper.getRecommendStyleImage(any())).thenReturn(recommendStyleImage);

        // act
        HotNailStyleModuleVO result = nailStyleService.queryHotNailStyle(request, envCtx);
        // assert
        assertNotNull(result);
        assertEquals("tag", result.getDisplayStyle());
    }

    /**
     * 测试 AB 实验结果为其他值的情况
     */
    @Test
    public void testQueryHotNailStyle_ABTestResultC() throws Throwable {
        // arrange
        GetHotNailStyleRequest request = new GetHotNailStyleRequest();
        request.setDealGroupId(1L);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
        dealGroupCategoryDTO.setCategoryId(505L);
        dealGroupDTO.setCategory(dealGroupCategoryDTO);
        // 设置商家在黑名单中
        lionConfigUtilsMocked.when(() -> LionConfigUtils.isHotNailModuleBlackShop(anyLong(), anyBoolean())).thenReturn(false);
        lionConfigUtilsMocked.when(() -> LionConfigUtils.allowDisplayHotNailModule(any(), any())).thenReturn(true);

        HotNailStyleConfig hotNailStyleConfig = new HotNailStyleConfig();
        ActionTextVO actionTextVO = new ActionTextVO();
        hotNailStyleConfig.setMtMoreStyleUrl("%s-%s");
        hotNailStyleConfig.setDpMoreStyleUrl("%s-%s");
        hotNailStyleConfig.setActionText(actionTextVO);
        lionMocked.when(() -> Lion.getBean(anyString(), anyString(), any())).thenReturn(hotNailStyleConfig);
        // 设置 AB 实验结果为 "c"
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("c");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        when(douHuBiz.getAbByUnionId(any(), any(), anyBoolean())).thenReturn(moduleAbConfig);
        lionConfigUtilsMocked.when(LionConfigUtils::getHotNailCompareExpReportSwitch).thenReturn(true);
        // act
        HotNailStyleModuleVO result = nailStyleService.queryHotNailStyle(request, envCtx);
        // assert
        assertNull(result);
    }
}
