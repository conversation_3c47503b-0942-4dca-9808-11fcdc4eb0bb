package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.concurrent.Future;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class MapperWrapper_GetDpCityByMtCityTest {

    @Mock
    private Future future;

    private MapperWrapper mapperWrapper;

    @Before
    public void setUp() {
        mapperWrapper = new MapperWrapper();
    }

    /**
     * 测试 future 为 null 的情况
     */
    @Test
    public void testGetDpCityByMtCityFutureIsNull() {
        int result = mapperWrapper.getDpCityByMtCity(null);
        assertEquals(0, result);
    }

    /**
     * 测试 future 不为 null，但 getFutureResult 返回 null 的情况
     */
    @Test
    public void testGetDpCityByMtCityFutureResultIsNull() throws Exception {
        when(future.get()).thenReturn(null);
        int result = mapperWrapper.getDpCityByMtCity(future);
        assertEquals(0, result);
    }

    /**
     * 测试 future 不为 null，且 getFutureResult 返回非 null 的情况
     */
    @Test
    public void testGetDpCityByMtCityFutureResultIsNotNull() throws Exception {
        when(future.get()).thenReturn(123);
        int result = mapperWrapper.getDpCityByMtCity(future);
        assertEquals(123, result);
    }
}
