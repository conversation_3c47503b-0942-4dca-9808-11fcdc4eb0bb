package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.deal.style.protocol.ExtraStyleResponse;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealStyleWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.MTTemplateKey;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule;
import com.google.common.collect.Maps;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.Future;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;

@RunWith(MockitoJUnitRunner.class)
public class ExtraStyleProcessorTest {

    @InjectMocks
    private ExtraStyleProcessor extraStyleProcessor;

    @Mock
    private DealStyleWrapper dealStyleWrapper;


    private MockedStatic<Lion> lionMocked;

    @Before
    public void setUp() {
        lionMocked = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        lionMocked.close();
    }

    /**
     * 测试 process 方法，ctx.isMt() 为 false
     */
    @Test
    public void testProcessCtxIsNotMt() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_LIVE_WEIXIN_MINIAPP);
        DealCtx ctx = new DealCtx(envCtx);
        MTTemplateKey mtTemplateKey = new MTTemplateKey();
        mtTemplateKey.setDegrade(true);
        ctx.setMtTemplateKey(mtTemplateKey);
        FutureCtx futureCtx = new FutureCtx();
        Future mockFuture = mock(Future.class);
        futureCtx.setExtraStyleFuture(mockFuture);
        ExtraStyleResponse extraStyleResp = mock(ExtraStyleResponse.class);
        lionMocked.when(() -> Lion.getMap(anyString(), anyString(), any(), any())).thenReturn(Maps.newHashMap());
        // act
        extraStyleProcessor.process(ctx);
        ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
        extraStyleProcessor.getModuleConfig(moduleConfigsModule);
        extraStyleProcessor.buildModuleConfigDo("key",  "value");

        assertNotNull(ctx.getModuleConfigsModule());
    }
}
