package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import org.apache.commons.lang3.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealGroupProcessorParseIntTest {

    private DealGroupProcessor dealGroupProcessor = new DealGroupProcessor();

    private int invokePrivateParseInt(String input) throws Throwable {
        Method method = DealGroupProcessor.class.getDeclaredMethod("parseInt", String.class);
        method.setAccessible(true);
        try {
            return (int) method.invoke(dealGroupProcessor, input);
        } catch (InvocationTargetException e) {
            throw e.getCause();
        }
    }

    private int parseInt(String string) {
        if (StringUtils.isNumeric(string)) {
            return Integer.parseInt(string);
        } else {
            return 0;
        }
    }

    /**
     * 测试输入字符串为有效数字的情况
     */
    @Test
    public void testParseIntValidNumber() throws Throwable {
        // arrange
        String input = "123";
        // act
        int result = invokePrivateParseInt(input);
        // assert
        assertEquals(123, result);
    }

    /**
     * 测试输入字符串为空字符串的情况
     */
    @Test
    public void testParseIntEmptyString() throws Throwable {
        // arrange
        String input = "";
        // act
        int result = invokePrivateParseInt(input);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试输入字符串为null的情况
     */
    @Test
    public void testParseIntNullString() throws Throwable {
        // arrange
        String input = null;
        // act
        int result = invokePrivateParseInt(input);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试输入字符串为负数的情况
     */
    @Test
    public void testParseIntNegativeNumber() throws Throwable {
        // arrange
        String input = "-123";
        // act
        int result = invokePrivateParseInt(input);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试输入字符串为小数的情况
     */
    @Test
    public void testParseIntDecimalNumber() throws Throwable {
        // arrange
        String input = "123.45";
        // act
        int result = invokePrivateParseInt(input);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试输入字符串超出Integer范围的情况
     */
    @Test(expected = NumberFormatException.class)
    public void testParseIntOutOfRangeNumber() throws Throwable {
        // arrange
        String input = "2147483648";
        // act
        int result = invokePrivateParseInt(input);
        // assert
        // Expecting NumberFormatException to be thrown
    }

    /**
     * 测试输入字符串包含非数字字符的情况
     */
    @Test
    public void testParseIntNonNumericString() throws Throwable {
        // arrange
        String input = "abc";
        // act
        int result = invokePrivateParseInt(input);
        // assert
        assertEquals(0, result);
    }
}
