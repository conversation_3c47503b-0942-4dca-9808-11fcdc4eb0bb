package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.DealCategoryFactory;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Test cases for ModuleAbBuilderService#getTimesDealAbConfig
 */
@RunWith(MockitoJUnitRunner.class)
public class ModuleAbBuilderServiceGetTimesDealAbConfigTest {

    @InjectMocks
    private ModuleAbBuilderService moduleAbBuilderService;

    @Mock
    private DealCategoryFactory dealCategoryFactory;

    @Mock
    private DouHuBiz douHuBiz;

    @Mock
    private DouHuService douHuService;

    private DealCtx ctx;

    private EnvCtx envCtx;

    private DealGroupDTO dealGroupDTO;

    private DealGroupCategoryDTO category;

    @Before
    public void setUp() {
        envCtx = new EnvCtx();
        ctx = new DealCtx(envCtx);
        dealGroupDTO = new DealGroupDTO();
        category = new DealGroupCategoryDTO();
        dealGroupDTO.setCategory(category);
        ctx.setDealGroupDTO(dealGroupDTO);
    }

    private ModuleAbConfig invokePrivateGetTimesDealAbConfig(DealCtx ctx) throws Exception {
        Method method = ModuleAbBuilderService.class.getDeclaredMethod("getTimesDealAbConfig", DealCtx.class);
        method.setAccessible(true);
        return (ModuleAbConfig) method.invoke(moduleAbBuilderService, ctx);
    }

    /**
     * Test when dealGroupDTO is null
     */
    @Test
    public void testGetTimesDealAbConfig_WhenDealGroupDTONull() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setDealGroupDTO(null);
        // act
        ModuleAbConfig result = invokePrivateGetTimesDealAbConfig(ctx);
        // assert
        assertNull(result);
    }

    /**
     * Test when dealGroupDTO.basic is null
     */
    @Test
    public void testGetTimesDealAbConfig_WhenBasicNull() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setBasic(null);
        ctx.setDealGroupDTO(dealGroupDTO);
        // act
        ModuleAbConfig result = invokePrivateGetTimesDealAbConfig(ctx);
        // assert
        assertNull(result);
    }

    /**
     * Test when tradeType is not TIMES_CARD(19)
     */
    @Test
    public void testGetTimesDealAbConfig_WhenNotTimesDeal() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupBasicDTO basic = new DealGroupBasicDTO();
        // Not times card type
        basic.setTradeType(1);
        dealGroupDTO.setBasic(basic);
        ctx.setDealGroupDTO(dealGroupDTO);
        // act
        ModuleAbConfig result = invokePrivateGetTimesDealAbConfig(ctx);
        // assert
        assertNull(result);
    }

    /**
     * Test when it is a times deal
     */
    @Test
    public void testGetTimesDealAbConfig_WhenIsTimesDeal() throws Throwable {
        // arrange
        // Mock the DealCtx object
        DealCtx ctx = mock(DealCtx.class);
        EnvCtx envCtx = new EnvCtx();
        // Stub getEnvCtx() to return a real EnvCtx object
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupBasicDTO basic = new DealGroupBasicDTO();
        // Times card type
        basic.setTradeType(19);
        dealGroupDTO.setBasic(basic);
        // Stub getDealGroupDTO() to return the dealGroupDTO
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        // Mock the category ID to avoid NPE
        when(ctx.getCategoryId()).thenReturn(123);
        ModuleAbConfig expectedConfig = new ModuleAbConfig();
        when(dealCategoryFactory.getTimesDealModuleAbConfig(envCtx, 123L)).thenReturn(expectedConfig);
        // act
        ModuleAbConfig result = invokePrivateGetTimesDealAbConfig(ctx);
        // assert
        assertSame(expectedConfig, result);
        verify(dealCategoryFactory).getTimesDealModuleAbConfig(envCtx, 123L);
    }

    /**
     * Test case for empty module configs list initialization
     */
    @Test
    public void testGetModuleAbConfigs_InitializeEmptyList() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setModuleAbConfigs(null);
        // act
        List<ModuleAbConfig> result = moduleAbBuilderService.getModuleAbConfigs(ctx);
        // assert
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    /**
     * Test case for existing module configs list
     */
    @Test
    public void testGetModuleAbConfigs_WithExistingList() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        List<ModuleAbConfig> existingList = new ArrayList<>();
        ModuleAbConfig existingConfig = new ModuleAbConfig();
        existingList.add(existingConfig);
        ctx.setModuleAbConfigs(existingList);
        // act
        List<ModuleAbConfig> result = moduleAbBuilderService.getModuleAbConfigs(ctx);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(existingConfig, result.get(0));
    }

    /**
     * Test when dealGroupDTO is null should return null
     */
    @Test
    public void testGetHotNailModuleAbConfigWhenDealGroupDTONull() throws Throwable {
        // arrange
        ctx.setDealGroupDTO(null);
        // act
        ModuleAbConfig result = moduleAbBuilderService.getHotNailModuleAbConfig(ctx);
        // assert
        assertNull("Result should be null when dealGroupDTO is null", result);
    }

    /**
     * Test when category is null should return null
     */
    @Test
    public void testGetHotNailModuleAbConfigWhenCategoryNull() throws Throwable {
        // arrange
        dealGroupDTO.setCategory(null);
        // act
        ModuleAbConfig result = moduleAbBuilderService.getHotNailModuleAbConfig(ctx);
        // assert
        assertNull("Result should be null when category is null", result);
    }

    /**
     * Test when categoryId is null should return null
     */
    @Test
    public void testGetHotNailModuleAbConfigWhenCategoryIdNull() throws Throwable {
        // arrange
        category.setCategoryId(null);
        category.setServiceType("nail");
        // act
        ModuleAbConfig result = moduleAbBuilderService.getHotNailModuleAbConfig(ctx);
        // assert
        assertNull("Result should be null when categoryId is null", result);
    }

    /**
     * Test when serviceType is null should return null
     */
    @Test
    public void testGetHotNailModuleAbConfigWhenServiceTypeNull() throws Throwable {
        // arrange
        category.setCategoryId(505L);
        category.setServiceType(null);
        // act
        ModuleAbConfig result = moduleAbBuilderService.getHotNailModuleAbConfig(ctx);
        // assert
        assertNull("Result should be null when serviceType is null", result);
    }

    /**
     * Test when unionId is null should return null
     */
    @Test
    public void testGetHotNailModuleAbConfigWhenUnionIdNull() throws Throwable {
        // arrange
        category.setCategoryId(505L);
        category.setServiceType("nail");
        envCtx.setUnionId(null);
        // act
        ModuleAbConfig result = moduleAbBuilderService.getHotNailModuleAbConfig(ctx);
        // assert
        assertNull("Result should be null when unionId is null", result);
    }

    /**
     * Test when douHuBiz throws exception should return null
     */
    @Test
    public void testGetHotNailModuleAbConfigWhenExceptionThrown() throws Throwable {
        // arrange
        category.setCategoryId(505L);
        category.setServiceType("nail");
        envCtx.setUnionId("test-union-id");
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        // act
        ModuleAbConfig result = moduleAbBuilderService.getHotNailModuleAbConfig(ctx);
        // assert
        assertNull("Result should be null when exception is thrown", result);
    }

    /**
     * Test when douHuBiz returns null should return null
     */
    @Test
    public void testGetHotNailModuleAbConfigWhenDouHuBizReturnsNull() throws Throwable {
        // arrange
        category.setCategoryId(505L);
        category.setServiceType("nail");
        envCtx.setUnionId("test-union-id");
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        // act
        ModuleAbConfig result = moduleAbBuilderService.getHotNailModuleAbConfig(ctx);
        // assert
        assertNull("Result should be null when douHuBiz returns null", result);
    }

    /**
     * Test when douHuBiz returns empty config should return null
     */
    @Test
    public void testGetHotNailModuleAbConfigWhenEmptyConfig() throws Throwable {
        // arrange
        category.setCategoryId(505L);
        category.setServiceType("nail");
        envCtx.setUnionId("test-union-id");
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        ModuleAbConfig emptyConfig = new ModuleAbConfig();
        // act
        ModuleAbConfig result = moduleAbBuilderService.getHotNailModuleAbConfig(ctx);
        // assert
        assertNull("Result should be null when douHuBiz returns empty config", result);
    }

    @Before
    public void setup() {
        // Setup default null returns for all service methods
        when(douHuService.getAbResultForComparePriceAssistant(any())).thenReturn(null);
        when(douHuBiz.getAbByUnionId(anyString(), anyString(), anyBoolean())).thenReturn(null);
        when(dealCategoryFactory.getModuleAbConfig(any(), any())).thenReturn(null);
        when(douHuBiz.getAbExpResult(any(DealCtx.class), anyString())).thenReturn(null);
        when(douHuService.getNativeDealDetailAbTestResult(any())).thenReturn(null);
        when(douHuService.getRepairPayAbTestResult(any())).thenReturn(null);
        when(douHuService.getMagicCouponEnhancementAbTestResult(any())).thenReturn(null);
        when(douHuService.getNavbarSearchAbTestResult(any())).thenReturn(null);
    }

    private DealCtx getBaseDealCtx() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx dealCtx = new DealCtx(envCtx);
        dealCtx.setModuleAbConfigs(new ArrayList<>());
        dealCtx.setCardStyleAbConfig(null);
        dealCtx.setCardStyleAbV2Config(null);
        dealCtx.setDealGroupDTO(null);
        return dealCtx;
    }

    @Test
    public void testGetModuleAbConfigs_AddPetStyleConfig() throws Throwable {
        // arrange
        DealCtx dealCtx = getBaseDealCtx();
        dealCtx.getEnvCtx().setUnionId("testUnionId");
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(1701L);
        dealGroupDTO.setCategory(categoryDTO);
        dealCtx.setDealGroupDTO(dealGroupDTO);
        ModuleAbConfig petStyleConfig = new ModuleAbConfig();
        when(douHuBiz.getAbcByUnionId(anyString(), anyString(), anyBoolean())).thenReturn(petStyleConfig);
        // act
        List<ModuleAbConfig> result = moduleAbBuilderService.getModuleAbConfigs(dealCtx);
        // assert
        assertEquals(1, result.size());
        assertEquals(petStyleConfig, result.get(0));
    }

    @Test
    public void testGetModuleAbConfigs_AddCardStyleAbConfig() throws Throwable {
        // arrange
        DealCtx dealCtx = getBaseDealCtx();
        ModuleAbConfig cardStyleAbConfig = new ModuleAbConfig();
        dealCtx.setCardStyleAbConfig(cardStyleAbConfig);
        // act
        List<ModuleAbConfig> result = moduleAbBuilderService.getModuleAbConfigs(dealCtx);
        // assert
        assertEquals(1, result.size());
        assertEquals(cardStyleAbConfig, result.get(0));
    }

    @Test
    public void testGetModuleAbConfigs_AddCardStyleAbV2Config() throws Throwable {
        // arrange
        DealCtx dealCtx = getBaseDealCtx();
        ModuleAbConfig cardStyleAbV2Config = new ModuleAbConfig();
        dealCtx.setCardStyleAbV2Config(cardStyleAbV2Config);
        // act
        List<ModuleAbConfig> result = moduleAbBuilderService.getModuleAbConfigs(dealCtx);
        // assert
        assertEquals(1, result.size());
        assertEquals(cardStyleAbV2Config, result.get(0));
    }

    @Test
    public void testGetModuleAbConfigs_AddComparePriceAbConfig() throws Throwable {
        // arrange
        DealCtx dealCtx = getBaseDealCtx();
        ModuleAbConfig comparePriceAbConfig = new ModuleAbConfig();
        when(douHuService.getAbResultForComparePriceAssistant(dealCtx)).thenReturn(comparePriceAbConfig);
        // act
        List<ModuleAbConfig> result = moduleAbBuilderService.getModuleAbConfigs(dealCtx);
        // assert
        assertEquals(1, result.size());
        assertEquals(comparePriceAbConfig, result.get(0));
    }

    @Test
    public void testGetModuleAbConfigs_AddTimesDealAbConfig() throws Throwable {
        // arrange
        DealCtx dealCtx = getBaseDealCtx();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupBasicDTO basicDTO = new DealGroupBasicDTO();
        // TIMES_CARD value
        basicDTO.setTradeType(19);
        dealGroupDTO.setBasic(basicDTO);
        dealCtx.setDealGroupDTO(dealGroupDTO);
        ModuleAbConfig timesDealAbConfig = new ModuleAbConfig();
        when(dealCategoryFactory.getTimesDealModuleAbConfig(any(EnvCtx.class), any(Long.class))).thenReturn(timesDealAbConfig);
        // act
        List<ModuleAbConfig> result = moduleAbBuilderService.getModuleAbConfigs(dealCtx);
        // assert
        assertEquals(1, result.size());
        assertEquals(timesDealAbConfig, result.get(0));
    }

    @Test
    public void testGetModuleAbConfigs_AddGlassesExhibitAbConfig() throws Throwable {
        // arrange
        DealCtx dealCtx = getBaseDealCtx();
        ModuleAbConfig glassesExhibitAbConfig = new ModuleAbConfig();
        dealCtx.setGlassesExhibitAbConfig(glassesExhibitAbConfig);
        // act
        List<ModuleAbConfig> result = moduleAbBuilderService.getModuleAbConfigs(dealCtx);
        // assert
        assertEquals(1, result.size());
        assertEquals(glassesExhibitAbConfig, result.get(0));
    }

    @Test
    public void testGetModuleAbConfigs_AddNativeDealDetailAbConfig() throws Throwable {
        // arrange
        DealCtx dealCtx = getBaseDealCtx();
        ModuleAbConfig nativeDealDetailAbConfig = new ModuleAbConfig();
        when(douHuService.getNativeDealDetailAbTestResult(any(EnvCtx.class))).thenReturn(nativeDealDetailAbConfig);
        // act
        List<ModuleAbConfig> result = moduleAbBuilderService.getModuleAbConfigs(dealCtx);
        // assert
        assertEquals(1, result.size());
        assertEquals(nativeDealDetailAbConfig, result.get(0));
    }

    @Test
    public void testGetModuleAbConfigs_AddRepairPayAbConfig() throws Throwable {
        // arrange
        DealCtx dealCtx = getBaseDealCtx();
        ModuleAbConfig repairPayAbConfig = new ModuleAbConfig();
        when(douHuService.getRepairPayAbTestResult(any(EnvCtx.class))).thenReturn(repairPayAbConfig);
        // act
        List<ModuleAbConfig> result = moduleAbBuilderService.getModuleAbConfigs(dealCtx);
        // assert
        assertEquals(1, result.size());
        assertEquals(repairPayAbConfig, result.get(0));
    }

    @Test
    public void testGetModuleAbConfigs_AddMagicCouponEnhancementAbConfig() throws Throwable {
        // arrange
        DealCtx dealCtx = getBaseDealCtx();
        dealCtx.setHasSuperCouponScene(true);
        ModuleAbConfig magicCouponEnhancementAbConfig = new ModuleAbConfig();
        when(douHuService.getMagicCouponEnhancementAbTestResult(any(EnvCtx.class))).thenReturn(magicCouponEnhancementAbConfig);
        // act
        List<ModuleAbConfig> result = moduleAbBuilderService.getModuleAbConfigs(dealCtx);
        // assert
        assertEquals(1, result.size());
        assertEquals(magicCouponEnhancementAbConfig, result.get(0));
    }

    @Test
    public void testGetModuleAbConfigs_AddSearchNavBarAbConfig() throws Throwable {
        // arrange
        DealCtx dealCtx = getBaseDealCtx();
        ModuleAbConfig searchNavBarAbConfig = new ModuleAbConfig();
        when(douHuService.getNavbarSearchAbTestResult(any(EnvCtx.class))).thenReturn(searchNavBarAbConfig);
        // act
        List<ModuleAbConfig> result = moduleAbBuilderService.getModuleAbConfigs(dealCtx);
        // assert
        assertEquals(1, result.size());
        assertEquals(searchNavBarAbConfig, result.get(0));
    }
}
