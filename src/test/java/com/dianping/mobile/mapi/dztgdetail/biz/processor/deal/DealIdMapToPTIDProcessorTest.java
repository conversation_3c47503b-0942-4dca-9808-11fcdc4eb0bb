package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.IdMappingWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Future;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

public class DealIdMapToPTIDProcessorTest {

    private DealIdMapToPTIDProcessor processor;

    private IdMappingWrapper idMappingWrapper;

    private DealCtx ctx;

    private FutureCtx futureCtx;

    @Before
    public void setUp() {
        idMappingWrapper = Mockito.mock(IdMappingWrapper.class);
        processor = new DealIdMapToPTIDProcessor();
        processor.idMappingWrapper = idMappingWrapper;
        ctx = Mockito.mock(DealCtx.class);
        futureCtx = Mockito.mock(FutureCtx.class);
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
    }

    /**
     * 测试 ctx.isMt() 为 true 的情况
     */
    @Test
    public void testProcessWhenIsMtTrue() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getMtId()).thenReturn(123);
        // act
        processor.process(ctx);
        // assert
        verify(ctx).setPtId(123L);
    }

    /**
     * 测试 ctx.isMt() 为 false 且 productIdToPtIdMap 不为 null 的情况
     */
    @Test
    public void testProcessWhenIsMtFalseAndMapNotNull() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(false);
        Future<Map<Long, Long>> future = Mockito.mock(Future.class);
        when(futureCtx.getBizProductIdToPtIdFuture()).thenReturn(future);
        Map<Long, Long> productIdToPtIdMap = new HashMap<>();
        productIdToPtIdMap.put(456L, 789L);
        when(idMappingWrapper.bizProductIdToPtId(future)).thenReturn(productIdToPtIdMap);
        when(ctx.getDpId()).thenReturn(456);
        // act
        processor.process(ctx);
        // assert
        verify(ctx).setPtId(789L);
    }

    /**
     * 测试 ctx.isMt() 为 false 且 productIdToPtIdMap 为 null 的情况
     */
    @Test
    public void testProcessWhenIsMtFalseAndMapNull() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(false);
        Future<Map<Long, Long>> future = Mockito.mock(Future.class);
        when(futureCtx.getBizProductIdToPtIdFuture()).thenReturn(future);
        when(idMappingWrapper.bizProductIdToPtId(future)).thenReturn(null);
        when(ctx.getDpId()).thenReturn(456);
        // act
        processor.process(ctx);
        // assert
        verify(ctx, never()).setPtId(anyLong());
    }

    /**
     * 测试 ctx.isMt() 为 false 且 productIdToPtIdMap 不包含 dpId 的情况
     */
    @Test
    public void testProcessWhenIsMtFalseAndMapDoesNotContainDpId() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(false);
        Future<Map<Long, Long>> future = Mockito.mock(Future.class);
        when(futureCtx.getBizProductIdToPtIdFuture()).thenReturn(future);
        Map<Long, Long> productIdToPtIdMap = new HashMap<>();
        productIdToPtIdMap.put(123L, 789L);
        when(idMappingWrapper.bizProductIdToPtId(future)).thenReturn(productIdToPtIdMap);
        when(ctx.getDpId()).thenReturn(456);
        // act
        processor.process(ctx);
        // assert
        verify(ctx, never()).setPtId(anyLong());
    }

    /**
     * 测试 ctx.isMt() 为 false 且 future 为 null 的情况
     */
    @Test
    public void testProcessWhenIsMtFalseAndFutureNull() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(false);
        when(futureCtx.getBizProductIdToPtIdFuture()).thenReturn(null);
        when(ctx.getDpId()).thenReturn(456);
        // act
        processor.process(ctx);
        // assert
        verify(ctx, never()).setPtId(anyLong());
    }

    /**
     * 测试 ctx.isMt() 为 true 且 mtId 为 0 的情况
     */
    @Test
    public void testProcessWhenIsMtTrueAndMtIdZero() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getMtId()).thenReturn(0);
        // act
        processor.process(ctx);
        // assert
        verify(ctx).setPtId(0L);
    }

    /**
     * 测试 ctx.isMt() 为 true 且 mtId 为 Integer.MAX_VALUE 的情况
     */
    @Test
    public void testProcessWhenIsMtTrueAndMtIdMaxValue() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getMtId()).thenReturn(Integer.MAX_VALUE);
        // act
        processor.process(ctx);
        // assert
        verify(ctx).setPtId((long) Integer.MAX_VALUE);
    }

    /**
     * 测试 ctx.isMt() 为 false 且 dpId 为 0 的情况
     */
    @Test
    public void testProcessWhenIsMtFalseAndDpIdZero() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(false);
        Future<Map<Long, Long>> future = Mockito.mock(Future.class);
        when(futureCtx.getBizProductIdToPtIdFuture()).thenReturn(future);
        Map<Long, Long> productIdToPtIdMap = new HashMap<>();
        productIdToPtIdMap.put(0L, 789L);
        when(idMappingWrapper.bizProductIdToPtId(future)).thenReturn(productIdToPtIdMap);
        when(ctx.getDpId()).thenReturn(0);
        // act
        processor.process(ctx);
        // assert
        verify(ctx).setPtId(789L);
    }

    /**
     * 测试 ctx.isMt() 为 false 且 dpId 为 Integer.MAX_VALUE 的情况
     */
    @Test
    public void testProcessWhenIsMtFalseAndDpIdMaxValue() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(false);
        Future<Map<Long, Long>> future = Mockito.mock(Future.class);
        when(futureCtx.getBizProductIdToPtIdFuture()).thenReturn(future);
        Map<Long, Long> productIdToPtIdMap = new HashMap<>();
        productIdToPtIdMap.put((long) Integer.MAX_VALUE, 789L);
        when(idMappingWrapper.bizProductIdToPtId(future)).thenReturn(productIdToPtIdMap);
        when(ctx.getDpId()).thenReturn(Integer.MAX_VALUE);
        // act
        processor.process(ctx);
        // assert
        verify(ctx).setPtId(789L);
    }
}
