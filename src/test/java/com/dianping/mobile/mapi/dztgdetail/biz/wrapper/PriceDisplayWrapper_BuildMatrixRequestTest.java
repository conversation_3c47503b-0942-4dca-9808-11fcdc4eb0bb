package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.common.enums.ClientTypeEnum;
import com.sankuai.beautycard.navigation.api.dto.BeautyMatrixRequestDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

@RunWith(MockitoJUnitRunner.class)
public class PriceDisplayWrapper_BuildMatrixRequestTest {

    private PriceDisplayWrapper priceDisplayWrapper;

    @Before
    public void setUp() {
        priceDisplayWrapper = new PriceDisplayWrapper();
    }

    /**
     * 测试buildMatrixRequest方法，当clientType为mt_mainApp_ios.getType()时
     */
    @Test
    public void testBuildMatrixRequestWhenClientTypeIsMtMainAppIos() {
        // arrange
        long userId = 111L;
        int cityId = 1;
        int clientType = ClientTypeEnum.mt_mainApp_ios.getType();
        String appVersion = "12.20.400";
        int platform = 1;
        String unionId = "411";
        // act
        BeautyMatrixRequestDTO result = priceDisplayWrapper.buildMatrixRequest(userId, cityId, clientType, appVersion, platform, unionId);
        // assert
        assertNotNull(result);
        assertEquals(205, result.getClientType());
    }

    /**
     * 测试buildMatrixRequest方法，当clientType为mt_mainApp_android.getType()时
     */
    @Test
    public void testBuildMatrixRequestWhenClientTypeIsMtMainAppAndroid() {
        // arrange
        long userId = 111L;
        int cityId = 1;
        int clientType = ClientTypeEnum.mt_mainApp_android.getType();
        String appVersion = "12.20.400";
        int platform = 1;
        String unionId = "411";
        // act
        BeautyMatrixRequestDTO result = priceDisplayWrapper.buildMatrixRequest(userId, cityId, clientType, appVersion, platform, unionId);
        // assert
        assertNotNull(result);
        assertEquals(206, result.getClientType());
    }

    /**
     * 测试buildMatrixRequest方法，当clientType为其他值时
     */
    @Test
    public void testBuildMatrixRequestWhenClientTypeIsOther() {
        // arrange
        long userId = 111L;
        int cityId = 1;
        int clientType = -1;
        String appVersion = "12.20.400";
        int platform = 1;
        String unionId = "411";
        // act
        BeautyMatrixRequestDTO result = priceDisplayWrapper.buildMatrixRequest(userId, cityId, clientType, appVersion, platform, unionId);
        // assert
        assertNotNull(result);
        assertEquals(207, result.getClientType());
    }
}
