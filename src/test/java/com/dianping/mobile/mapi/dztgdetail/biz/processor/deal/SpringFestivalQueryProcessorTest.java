package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.SpringFestivalWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.poi.feature.api.dto.business.TagDto;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class SpringFestivalQueryProcessorTest {

    @InjectMocks
    private SpringFestivalQueryProcessor processor;

    @Mock
    private SpringFestivalWrapper springFestivalWrapper;

    /**
     * Test prepare() when platform is Meituan
     */
    @Test
    public void testPrepare_WhenMeituan() {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        FutureCtx futureCtx = mock(FutureCtx.class);
        Future mockFuture = mock(Future.class);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getMtLongShopId()).thenReturn(123L);
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
        when(springFestivalWrapper.preSpringFestivalTags(true, 123L)).thenReturn(mockFuture);
        // act
        processor.prepare(ctx);
        // assert
        verify(springFestivalWrapper).preSpringFestivalTags(true, 123L);
        verify(futureCtx).setSpringFestivalTagsFuture(mockFuture);
    }

    /**
     * Test prepare() when platform is Dianping
     */
    @Test
    public void testPrepare_WhenDianping() {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        FutureCtx futureCtx = mock(FutureCtx.class);
        Future mockFuture = mock(Future.class);
        when(ctx.isMt()).thenReturn(false);
        when(ctx.getDpLongShopId()).thenReturn(456L);
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
        when(springFestivalWrapper.preSpringFestivalTags(false, 456L)).thenReturn(mockFuture);
        // act
        processor.prepare(ctx);
        // assert
        verify(springFestivalWrapper).preSpringFestivalTags(false, 456L);
        verify(futureCtx).setSpringFestivalTagsFuture(mockFuture);
    }

    /**
     * Test prepare() when Future is null
     */
    @Test
    public void testPrepare_WhenFutureIsNull() {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        FutureCtx futureCtx = mock(FutureCtx.class);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getMtLongShopId()).thenReturn(123L);
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
        when(springFestivalWrapper.preSpringFestivalTags(true, 123L)).thenReturn(null);
        // act
        processor.prepare(ctx);
        // assert
        verify(springFestivalWrapper).preSpringFestivalTags(true, 123L);
        verify(futureCtx).setSpringFestivalTagsFuture(null);
    }

    /**
     * Test prepare() when shopId is 0
     */
    @Test
    public void testPrepare_WhenShopIdIsZero() {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        FutureCtx futureCtx = mock(FutureCtx.class);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getMtLongShopId()).thenReturn(0L);
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
        when(springFestivalWrapper.preSpringFestivalTags(true, 0L)).thenReturn(null);
        // act
        processor.prepare(ctx);
        // assert
        verify(springFestivalWrapper).preSpringFestivalTags(true, 0L);
        verify(futureCtx).setSpringFestivalTagsFuture(null);
    }

    /**
     * Test case: Tags list is null
     * Expected: Should not set spring festival tag
     */
    @Test
    public void testProcess_WhenTagsListIsNull() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        FutureCtx futureCtx = new FutureCtx();
        ctx.setFutureCtx(futureCtx);
        Future future = mock(Future.class);
        futureCtx.setSpringFestivalTagsFuture(future);
        when(springFestivalWrapper.getSpringFestivalTags(any())).thenReturn(null);
        // act
        processor.process(ctx);
        // assert
        verify(springFestivalWrapper).getSpringFestivalTags(future);
        assertFalse(ctx.isSpringFestivalTag());
    }

    /**
     * Test case: Tags list is empty
     * Expected: Should not set spring festival tag
     */
    @Test
    public void testProcess_WhenTagsListIsEmpty() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        FutureCtx futureCtx = new FutureCtx();
        ctx.setFutureCtx(futureCtx);
        Future future = mock(Future.class);
        futureCtx.setSpringFestivalTagsFuture(future);
        when(springFestivalWrapper.getSpringFestivalTags(any())).thenReturn(Collections.emptyList());
        // act
        processor.process(ctx);
        // assert
        verify(springFestivalWrapper).getSpringFestivalTags(future);
        assertFalse(ctx.isSpringFestivalTag());
    }

    /**
     * Test case: Tags list contains spring festival tag
     * Expected: Should set spring festival tag to true
     */
    @Test
    public void testProcess_WhenTagsContainSpringFestivalTag() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        FutureCtx futureCtx = new FutureCtx();
        ctx.setFutureCtx(futureCtx);
        Future future = mock(Future.class);
        futureCtx.setSpringFestivalTagsFuture(future);
        List<TagDto> tags = new ArrayList<>();
        TagDto springFestivalTag = new TagDto();
        springFestivalTag.setTagId(326565L);
        tags.add(springFestivalTag);
        when(springFestivalWrapper.getSpringFestivalTags(any())).thenReturn(tags);
        // act
        processor.process(ctx);
        // assert
        verify(springFestivalWrapper).getSpringFestivalTags(future);
        assertTrue(ctx.isSpringFestivalTag());
    }

    /**
     * Test case: Tags list does not contain spring festival tag
     * Expected: Should set spring festival tag to false
     */
    @Test
    public void testProcess_WhenTagsDoNotContainSpringFestivalTag() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        FutureCtx futureCtx = new FutureCtx();
        ctx.setFutureCtx(futureCtx);
        Future future = mock(Future.class);
        futureCtx.setSpringFestivalTagsFuture(future);
        List<TagDto> tags = new ArrayList<>();
        TagDto otherTag = new TagDto();
        otherTag.setTagId(999999L);
        tags.add(otherTag);
        when(springFestivalWrapper.getSpringFestivalTags(any())).thenReturn(tags);
        // act
        processor.process(ctx);
        // assert
        verify(springFestivalWrapper).getSpringFestivalTags(future);
        assertFalse(ctx.isSpringFestivalTag());
    }
}
