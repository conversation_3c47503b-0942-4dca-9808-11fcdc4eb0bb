package com.dianping.mobile.mapi.dztgdetail.biz.service;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.CardStyleConfig;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.mockStatic;

@RunWith(MockitoJUnitRunner.class)
public class DouHuService_EnableCardStyleV2Test {

    @InjectMocks
    private DouHuService douHuService;

    @Mock
    private DouHuBiz douHuBiz;

    private MockedStatic<Lion> lionMockedStatic;
    @Before
    public void setUp() {
        lionMockedStatic = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        lionMockedStatic.close();
    }

    @Test
    public void testEnableCardStyleV2_CardStyleConfigIsNull() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        int publishCategoryId = 502;
        String mrnVersion = "0.5.7";
        lionMockedStatic.when(() -> Lion.getBean("com.sankuai.dzu.tpbase.dztgdetailweb", "com.sankuai.dzu.tpbase.dztgdetailweb" +
                ".card.style.v2.config", CardStyleConfig.class)).thenReturn(null);
        lionMockedStatic.when(() -> Lion.getBean("com.sankuai.dzu.tpbase.dztgdetailweb", "com.sankuai.dzu.tpbase.dztgdetailweb" +
                ".card.style.non.app.config", CardStyleConfig.class)).thenReturn(null);
        // act
        ModuleAbConfig result = douHuService.enableCardStyleV2(envCtx, publishCategoryId, mrnVersion);
        // assert
        assertNull(result);
    }

    @Test
    public void testEnableCardStyleV2_ClientTypeNotInList() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        // Set a client type that is not in the list
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.UNKNOWN);
        int publishCategoryId = 502;
        String mrnVersion = "0.5.7";
        CardStyleConfig cardStyleConfig = new CardStyleConfig();
        cardStyleConfig.setEnableCardStyle(true);
        cardStyleConfig.setEnableClientType(Arrays.asList(1, 4, 11));
        lionMockedStatic.when(() -> Lion.getBean("com.sankuai.dzu.tpbase.dztgdetailweb", "com.sankuai.dzu.tpbase.dztgdetailweb.card" +
                ".style.v2.config", CardStyleConfig.class)).thenReturn(cardStyleConfig);
        lionMockedStatic.when(() -> Lion.getBean("com.sankuai.dzu.tpbase.dztgdetailweb", "com.sankuai.dzu.tpbase.dztgdetailweb.card" +
                ".style.non.app.config", CardStyleConfig.class)).thenReturn(cardStyleConfig);
        // act
        ModuleAbConfig result = douHuService.enableCardStyleV2(envCtx, publishCategoryId, mrnVersion);
        // assert
        assertNull(result);
    }
}
