package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.ReviewPicDTO;
import com.dianping.mobile.mapi.dztgdetail.helper.ShopReviewHelper;
import com.dianping.ugc.pic.remote.dto.MtReviewPicInfo;
import com.dianping.ugc.pic.remote.dto.VideoData;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class ShopReviewHalperTest {

    /**
     * 测试 dealGroupDTO.getBasic() 为 null 的情况
     */
    @Test
    public void testIsTimesDealDealGroupDTOBasicNull() {
        // arrange
        List<VideoData> videoDataList = new ArrayList<>();
        VideoData videoData = new VideoData();
        videoData.setCover("cover");
        videoDataList.add(videoData);
        List<MtReviewPicInfo > mtReviewPicInfoList = new ArrayList<>();
        // act
        List<ReviewPicDTO> result = ShopReviewHelper.buildMtReviewPicList(videoDataList, mtReviewPicInfoList);
        // assert
        assertNotNull(result);
    }

}
