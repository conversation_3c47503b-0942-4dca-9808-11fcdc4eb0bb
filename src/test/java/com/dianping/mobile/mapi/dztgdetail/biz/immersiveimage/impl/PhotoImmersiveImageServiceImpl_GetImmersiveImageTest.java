package com.dianping.mobile.mapi.dztgdetail.biz.immersiveimage.impl;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ImmersiveImageWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageVO;
import com.dianping.mobile.mapi.dztgdetail.entity.QueryExhibitImageParam;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class PhotoImmersiveImageServiceImpl_GetImmersiveImageTest {

    @InjectMocks
    private PhotoImmersiveImageServiceImpl photoImmersiveImageService;

    @Mock
    private ImmersiveImageWrapper immersiveImageWrapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 getImmersiveImage 方法，正常情况
     */
    @Test
    public void testGetImmersiveImageNormal() {
        GetImmersiveImageRequest request = new GetImmersiveImageRequest();
        request.setCategoryId(504);
        request.setDealGroupId(1);
        request.setStart(0);
        request.setLimit(10);
        request.setShopId(1L);
        request.setSelectValue("test");
        EnvCtx envCtx = new EnvCtx();
        ImmersiveImageVO expected = new ImmersiveImageVO();
        when(immersiveImageWrapper.getImmersiveImage(any(QueryExhibitImageParam.class), any(EnvCtx.class))).thenReturn(expected);
        ImmersiveImageVO result = photoImmersiveImageService.getImmersiveImage(request, envCtx);
        assertNotNull(result);
    }

    /**
     * 测试 getImmersiveImage 方法，异常情况
     */
    @Test(expected = RuntimeException.class)
    public void testGetImmersiveImageException() {
        GetImmersiveImageRequest request = new GetImmersiveImageRequest();
        request.setCategoryId(504);
        request.setDealGroupId(1);
        request.setStart(0);
        request.setLimit(10);
        request.setShopId(1L);
        request.setSelectValue("test");
        EnvCtx envCtx = new EnvCtx();
        when(immersiveImageWrapper.getImmersiveImage(any(QueryExhibitImageParam.class), any(EnvCtx.class))).thenThrow(new RuntimeException());
        photoImmersiveImageService.getImmersiveImage(request, envCtx);
    }
}
