package com.dianping.mobile.mapi.dztgdetail.availabletime.impl;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.pigeon.remoting.common.codec.json.JacksonUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import org.apache.commons.lang.StringUtils;
import org.mockito.InjectMocks;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;

import static org.junit.Assert.*;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/12/12 15:18
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest()
public class KTVAvailableTimeStrategyTest {

    @InjectMocks
    private KTVAvailableTimeStrategy ktvAvailableTimeStrategy;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    // 正常情况
    @Test
    public void testGetAvailableTime() {
        DealCtx dealCtx = PowerMockito.mock(DealCtx.class);
        when(dealCtx.getDealGroupDTO()).thenReturn(buildDealGroupDTO());
        String availableTime = ktvAvailableTimeStrategy.getAvailableTime(dealCtx);
        assert StringUtils.isNotBlank(availableTime);
    }
    // serviceProject为null

    @Test
    public void testGetAvailableTime1() {
        DealGroupDTO dealGroupDTO = buildDealGroupDTO();
        dealGroupDTO.setServiceProject(null);
        DealCtx dealCtx = PowerMockito.mock(DealCtx.class);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        String availableTime = ktvAvailableTimeStrategy.getAvailableTime(dealCtx);
        assert StringUtils.isBlank(availableTime);
    }
    // mustGroups为null
    @Test
    public void testGetAvailableTime2() {
        DealGroupDTO dealGroupDTO = buildDealGroupDTO();

        DealGroupServiceProjectDTO serviceProject = dealGroupDTO.getServiceProject();
        serviceProject.setMustGroups(null);

        DealCtx dealCtx = PowerMockito.mock(DealCtx.class);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        String availableTime = ktvAvailableTimeStrategy.getAvailableTime(dealCtx);
        assert StringUtils.isBlank(availableTime);
    }

    // groups为null
    @Test
    public void testGetAvailableTime3() {
        DealGroupDTO dealGroupDTO = buildDealGroupDTO();

        DealGroupServiceProjectDTO serviceProject = dealGroupDTO.getServiceProject();
        List<MustServiceProjectGroupDTO> mustGroups = serviceProject.getMustGroups();
        mustGroups.forEach(item -> item.setGroups(null));

        DealCtx dealCtx = PowerMockito.mock(DealCtx.class);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        String availableTime = ktvAvailableTimeStrategy.getAvailableTime(dealCtx);
        assert StringUtils.isBlank(availableTime);
    }

    // attrs为null
    @Test
    public void testGetAvailableTime4() {
        DealGroupDTO dealGroupDTO = buildDealGroupDTO();

        DealGroupServiceProjectDTO serviceProject = dealGroupDTO.getServiceProject();
        List<MustServiceProjectGroupDTO> mustGroups = serviceProject.getMustGroups();
        mustGroups.forEach(item -> {
            List<ServiceProjectDTO> groups = item.getGroups();
            groups.forEach(group -> {
                group.setAttrs(null);
            });
        });


        DealCtx dealCtx = PowerMockito.mock(DealCtx.class);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        String availableTime = ktvAvailableTimeStrategy.getAvailableTime(dealCtx);
        assert StringUtils.isBlank(availableTime);
    }

    // attrs中有null元素
    @Test
    public void testGetAvailableTime5() {
        DealGroupDTO dealGroupDTO = buildDealGroupDTO();

        DealGroupServiceProjectDTO serviceProject = dealGroupDTO.getServiceProject();
        List<MustServiceProjectGroupDTO> mustGroups = serviceProject.getMustGroups();
        mustGroups.forEach(item -> {
            List<ServiceProjectDTO> groups = item.getGroups();
            groups.forEach(group -> {
                List<ServiceProjectAttrDTO> attrs = group.getAttrs();
                attrs.add(null);
            });
        });

        DealCtx dealCtx = PowerMockito.mock(DealCtx.class);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        String availableTime = ktvAvailableTimeStrategy.getAvailableTime(dealCtx);
        assert StringUtils.isBlank(availableTime);
    }



    private DealGroupDTO buildDealGroupDTO() {
        return JacksonUtils.deserialize(dataJson, DealGroupDTO.class);
    }

    private static final String dataJson = "{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupDTO\",\"dpDealGroupId\":1097857335,\"mtDealGroupId\":1097857335,\"basic\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO\",\"categoryId\":301,\"title\":\"下午场小包3小时+饮料4瓶/周一至周五可用\",\"brandName\":\"MK·量贩式KTV\",\"titleDesc\":\"仅售78元，价值184元下午场小包3小时+饮料4瓶/周一至周五可用！\",\"beginSaleDate\":\"2024-06-15 14:53:34\",\"endSaleDate\":\"2025-06-14 00:00:00\",\"status\":1,\"saleChannel\":0,\"salePlatform\":3,\"sourceId\":102,\"tradeType\":3,\"platformCategoryId\":80301},\"image\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupImageDTO\",\"defaultPicPath\":\"https://p0.meituan.net/dpmerchantpic/080dccbeaf81da9197b12a8fe486ec2f92196.jpg\",\"allPicPaths\":\"https://p0.meituan.net/dpmerchantpic/080dccbeaf81da9197b12a8fe486ec2f92196.jpg|https://p0.meituan.net/dpmerchantpic/219467fb1a78df83273d8c8d3398b7fb94832.jpg|https://p0.meituan.net/dpmerchantpic/19affa8861ed97fff69afdc5209dcfba107649.png\",\"videoPath\":null,\"videoCoverPath\":null,\"videoSize\":null,\"extendVideos\":null,\"allVideos\":null},\"category\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO\",\"categoryId\":301,\"serviceType\":\"欢唱和软饮类套餐\",\"serviceTypeId\":116006,\"platformCategoryId\":80301},\"bgBu\":null,\"serviceProject\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO\",\"title\":\"团购详情\",\"salePrice\":\"78.0\",\"marketPrice\":\"184.0\",\"mustGroups\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO\",\"groups\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO\",\"skuId\":0,\"categoryId\":2104693,\"name\":\"矿泉水4份\",\"amount\":1,\"marketPrice\":\"26.0\",\"status\":10,\"attrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":154239,\"attrName\":\"softDrinkContent\",\"chnName\":\"软饮内容\",\"attrValue\":\"矿泉水\",\"rawAttrValue\":\"矿泉水\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":154241,\"attrName\":\"softDrinkCount\",\"chnName\":\"软饮份数\",\"attrValue\":\"4\",\"rawAttrValue\":\"4\",\"unit\":null,\"valueType\":401,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":154242,\"attrName\":\"softDrinkUnit\",\"chnName\":\"数量单位\",\"attrValue\":\"份\",\"rawAttrValue\":\"份\",\"unit\":null,\"valueType\":500,\"sequence\":0}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO\",\"skuId\":0,\"categoryId\":2104691,\"name\":\"[小包白天档3小时]\",\"amount\":1,\"marketPrice\":\"158.0\",\"status\":10,\"attrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":554,\"attrName\":\"minpeoplenum\",\"chnName\":\"最小人数\",\"attrValue\":\"1\",\"rawAttrValue\":\"1\",\"unit\":null,\"valueType\":401,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":555,\"attrName\":\"maxpeoplenum\",\"chnName\":\"最大人数\",\"attrValue\":\"5\",\"rawAttrValue\":\"5\",\"unit\":null,\"valueType\":401,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":11,\"attrName\":\"ktv_roomtype\",\"chnName\":\"包型\",\"attrValue\":\"小包\",\"rawAttrValue\":\"小包\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":573,\"attrName\":\"period\",\"chnName\":\"时段\",\"attrValue\":\"白天档\",\"rawAttrValue\":\"白天档\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":571,\"attrName\":\"start_time\",\"chnName\":\"开始时间\",\"attrValue\":\"10:00\",\"rawAttrValue\":\"10:00\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":161367,\"attrName\":\"end_time\",\"chnName\":\"结束时间\",\"attrValue\":\"18:00\",\"rawAttrValue\":\"18:00\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":1633,\"attrName\":\"timeDuration\",\"chnName\":\"适用时长\",\"attrValue\":\"3\",\"rawAttrValue\":\"3\",\"unit\":null,\"valueType\":400,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":1471,\"attrName\":\"unit\",\"chnName\":\"时长单位\",\"attrValue\":\"小时\",\"rawAttrValue\":\"小时\",\"unit\":null,\"valueType\":500,\"sequence\":0}]]}]]}]],\"optionGroups\":[\"java.util.ArrayList\",[]],\"structType\":\"uniform-structure-table\"},\"channel\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupChannelDTO\",\"channelId\":18,\"channelEn\":\"ktv\",\"channelCn\":\"KTV\",\"channelGroupId\":2,\"channelGroupEn\":\"general\",\"channelGroupCn\":\"到店综合\"},\"attrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_finish_date\",\"value\":[\"java.util.ArrayList\",[\"1970-01-01 08:00:00\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"calc_holiday_available\",\"value\":[\"java.util.ArrayList\",[\"0\",\"1006\",\"1007\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_business_type\",\"value\":[\"java.util.ArrayList\",[\"1\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_channel_id_allowed\",\"value\":[\"java.util.ArrayList\",[\"0\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_can_use_coupon\",\"value\":[\"java.util.ArrayList\",[\"true\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"preSaleTag\",\"value\":[\"java.util.ArrayList\",[\"false\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_third_party_verify\",\"value\":[\"java.util.ArrayList\",[\"false\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_block_stock\",\"value\":[\"java.util.ArrayList\",[\"false\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_discount_rule_id\",\"value\":[\"java.util.ArrayList\",[\"0\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"service_type\",\"value\":[\"java.util.ArrayList\",[\"欢唱和软饮类套餐\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"tag_unifyProduct\",\"value\":[\"java.util.ArrayList\",[\"0\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"sys_deal_universal_type\",\"value\":[\"java.util.ArrayList\",[\"1\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"reservation_is_needed_or_not\",\"value\":[\"java.util.ArrayList\",[\"否\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"category\",\"value\":[\"java.util.ArrayList\",[\"30\",\"3002\"]],\"source\":0,\"cnName\":null,\"type\":null}]],\"rule\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO\",\"buyRule\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.rule.buy.DealGroupBuyRuleDTO\",\"maxPerUser\":0,\"minPerUser\":1},\"useRule\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO\",\"receiptEffectiveDate\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.rule.use.ReceiptEffectiveDateDTO\",\"receiptDateType\":1,\"receiptValidDays\":90,\"receiptBeginDate\":\"2024-12-10 20:01:04\",\"receiptEndDate\":\"2025-03-10 23:59:59\",\"showText\":\"购买后90天内有效\"},\"availableDate\":null,\"disableDate\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.rule.use.DisableDateDTO\",\"disableDays\":[\"java.util.ArrayList\",[6,7]],\"disableDateRangeDTOS\":null}},\"bookingRule\":null,\"refundRule\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.rule.refund.DealGroupRefundRuleDTO\",\"supportRefundType\":1,\"supportOverdueAutoRefund\":true}},\"displayShopInfo\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupDisplayShopDTO\",\"dpDisplayShopIds\":[\"java.util.ArrayList\",[1465483823]],\"mtDisplayShopIds\":[\"java.util.ArrayList\",[1465483823]]},\"verifyShopInfo\":null,\"tags\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":1000000251,\"tagName\":\"小包\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100065120,\"tagName\":\"软饮\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":1000000246,\"tagName\":\"KTV\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100019841,\"tagName\":\"包厢\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100205353,\"tagName\":\"小包\"}]],\"customer\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupCustomerDTO\",\"originCustomerId\":44975991,\"platformCustomerId\":1109922012},\"regions\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupRegionDTO\",\"dpCityId\":158,\"mtCityId\":233}]],\"notice\":null,\"notice2b\":null,\"deals\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO\",\"dealId\":1325618620,\"basic\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.deal.DealBasicDTO\",\"title\":\"下午场小包3小时+饮料4瓶/周一至周五可用\",\"originTitle\":\"下午场小包3小时+饮料4瓶/周一至周五可用\",\"thirdPartyId\":null,\"status\":1,\"thirdPartyDealId\":null},\"price\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.PriceDTO\",\"salePrice\":\"78.00\",\"marketPrice\":\"184.00\",\"version\":11036416503,\"status\":null,\"prePayPrice\":null,\"finalPayPrice\":null},\"stock\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StockDTO\",\"dpSales\":15,\"dpTotal\":100000000,\"dpRemain\":99999985,\"mtSales\":878,\"mtTotal\":100000000,\"mtRemain\":99999122,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1,\"sharedSales\":0,\"sharedTotal\":0,\"sharedRemain\":0,\"isSharedSoldOut\":false},\"attrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"sku_receipt_type\",\"value\":[\"java.util.ArrayList\",[\"1\"]],\"source\":0,\"cnName\":null,\"type\":null}]],\"dealTimeStockDTO\":null,\"dealTimeStockPlanDTO\":null,\"rule\":null,\"image\":null,\"displayShop\":null,\"dealDelivery\":null,\"shopStocks\":null,\"bizDealId\":null,\"weeklyPricePlan\":null,\"dateTimePrice\":null,\"periodPrice\":null,\"bizDealIdInt\":null,\"dealIdInt\":1325618620}]],\"price\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.PriceDTO\",\"salePrice\":\"78.00\",\"marketPrice\":\"184.00\",\"version\":11036416503,\"status\":null,\"prePayPrice\":null,\"finalPayPrice\":null},\"stock\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StockDTO\",\"dpSales\":15,\"dpTotal\":100000000,\"dpRemain\":99999985,\"mtSales\":878,\"mtTotal\":100000000,\"mtRemain\":99999122,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1,\"sharedSales\":null,\"sharedTotal\":null,\"sharedRemain\":null,\"isSharedSoldOut\":null},\"detail\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.detail.DealGroupDetailDTO\",\"dealGroupPics\":\"https://p0.meituan.net/dpmerchantpic/080dccbeaf81da9197b12a8fe486ec2f92196.jpg|https://p0.meituan.net/dpmerchantpic/219467fb1a78df83273d8c8d3398b7fb94832.jpg|https://p0.meituan.net/dpmerchantpic/19affa8861ed97fff69afdc5209dcfba107649.png\",\"images\":[\"java.util.ArrayList\",[\"https://p0.meituan.net/dpmerchantpic/080dccbeaf81da9197b12a8fe486ec2f92196.jpg%40450w_280h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\",\"https://p0.meituan.net/dpmerchantpic/219467fb1a78df83273d8c8d3398b7fb94832.jpg%40450w_280h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\",\"https://p0.meituan.net/dpmerchantpic/19affa8861ed97fff69afdc5209dcfba107649.png%40450w_280h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\"]],\"info\":null,\"importantPoint\":\"\",\"specialPoint\":null,\"productInfo\":null,\"editorInfo\":null,\"memberInfo\":null,\"shopInfo\":null,\"editorTeam\":null,\"summary\":null,\"templateDetailDTOs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.detail.DealGroupTemplateDetailDTO\",\"title\":\"产品介绍\",\"content\":\"<p class=\\\"listitem\\\"></p>\\n<p class=\\\"explain\\\"></p>\\n<div class=\\\"img\\\"><img src=\\\"https://p0.meituan.net/merchantpic/c2c5df0be26189e0c386abd6a47e82864231320.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /></div>\\n<p class=\\\"listitem\\\"></p>\\n<p class=\\\"explain\\\"></p>\\n<div class=\\\"img\\\"><img src=\\\"https://p1.meituan.net/merchantpic/2f2617ba8f469161533db0d72c9e93983191348.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /></div>\\n<p class=\\\"listitem\\\"></p>\\n<p class=\\\"explain\\\"></p>\\n<div class=\\\"img\\\"><img src=\\\"https://p0.meituan.net/merchantpic/b147a5e31eec18a92ab45aa8953bfff83284640.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /></div>\\n<p class=\\\"listitem\\\"></p>\\n<p class=\\\"explain\\\"></p>\\n<div class=\\\"img\\\"><img src=\\\"https://p0.meituan.net/merchantpic/38de5dc940e347888177957503356dbf3558474.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /></div>\\n<p class=\\\"listitem\\\"></p>\\n<p class=\\\"explain\\\"></p>\\n<div class=\\\"img\\\"><img src=\\\"https://p0.meituan.net/merchantpic/064fdce2b240c6ffdf68a2d4bed5576a2666811.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /></div>\\n<p class=\\\"listitem\\\"></p>\\n<p class=\\\"explain\\\"></p>\\n<div class=\\\"img\\\"><img src=\\\"https://p0.meituan.net/merchantpic/7d57a7c857163757fecf8dda87f30e195349067.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /></div>\\n<p class=\\\"listitem\\\"></p>\\n<p class=\\\"explain\\\"></p>\\n<div class=\\\"img\\\"><img src=\\\"https://p1.meituan.net/merchantpic/9d180261201520c498a8d5baa241e0553483238.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /></div>\\n\\n\",\"type\":5,\"blockId\":0,\"mixedContents\":null,\"imageContents\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.detail.DealGroupTemplateDetailDTO\",\"title\":\"团购详情\",\"content\":\"        <div class=\\\"detail-tit\\\"></div>\\n        <div>\\n            <table width=\\\"100%\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" class=\\\"detail-table\\\">\\n                <thead>\\n                <tr>\\n                    <th width=\\\"50%\\\">名称</th>\\n                    <th width=\\\"25%\\\">数量</th>\\n                    <th width=\\\"25%\\\">单价</th>\\n                </tr>\\n                </thead>\\n                <tbody>\\n                            <tr>\\n                                <td>矿泉水4份</td>\\n                                    <td class=\\\"tc\\\">1</td>\\n                                    <td class=\\\"tc\\\">26元</td>\\n                            </tr>\\n                            <tr>\\n                                <td>[小包白天档3小时]</td>\\n                                    <td class=\\\"tc\\\">1</td>\\n                                    <td class=\\\"tc\\\">158元</td>\\n                            </tr>\\n                    <tr class=\\\"total\\\">\\n                        <td></td>\\n                        <td class=\\\"tc\\\">总价<br><strong>团购价</strong></td>\\n                        <td class=\\\"tc\\\">184元<br><strong>78元</strong>\\n                        </td>\\n                    </tr>\\n                </tbody>\\n            </table>\\n        </div>\\n\\n\",\"type\":1,\"blockId\":0,\"mixedContents\":null,\"imageContents\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.detail.DealGroupTemplateDetailDTO\",\"title\":\"购买须知\",\"content\":\"<div class=\\\"detail-box\\\">\\n    <div class=\\\"purchase-notes\\\">\\n\\t\\t        <dl>\\n            <dt>有效期</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">\\n    购买后90天内有效\\n        </p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>除外日期</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">每周六、周日不可用</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>预约信息</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">无需预约，如遇消费高峰时段您可能需要排队</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>规则提醒</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">不再与其他优惠同享</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>温馨提示</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">如需团购券发票，请您在消费时向商户咨询</p>\\n                <p class=\\\"listitem\\\">为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！</p>\\n                <p class=\\\"listitem\\\">本着为未成年人的健康成长创造良好的社会环境原则，本产品依法不对未成年人售卖</p>\\n            </dd>\\n        </dl>\\n    </div>\\n</div>\\n\",\"type\":2,\"blockId\":0,\"mixedContents\":null,\"imageContents\":null}]]},\"spu\":null,\"standardServiceProject\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectDTO\",\"mustGroups\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectGroupDTO\",\"serviceProjectItems\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectItemDTO\",\"serviceProjectName\":\"矿泉水4份\",\"standardAttribute\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeDTO\",\"attrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"skuCateId\",\"attrCnName\":\"项目分类\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"2104693\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"serviceLimit\",\"attrCnName\":\"是否无限畅饮\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"否\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"softDrinkContent\",\"attrCnName\":\"软饮内容\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"矿泉水\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"softDrinkCount\",\"attrCnName\":\"软饮份数\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"4\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"softDrinkUnit\",\"attrCnName\":\"数量单位\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"份\"]],\"complexValues\":null}]]}]],\"cpvObjectId\":20410427,\"cpvObjectVersion\":11},\"marketPrice\":\"26.0\",\"amount\":1},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectItemDTO\",\"serviceProjectName\":\"[小包白天档3小时]\",\"standardAttribute\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeDTO\",\"attrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"maxpeoplenum\",\"attrCnName\":\"最大人数\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"5\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"skuCateId\",\"attrCnName\":\"项目分类\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"2104691\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"start_time\",\"attrCnName\":\"开始时间\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"10:00\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"period\",\"attrCnName\":\"时段\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"[\\\"白天档\\\"]\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"unit\",\"attrCnName\":\"时长单位\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"小时\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"end_time\",\"attrCnName\":\"结束时间\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"18:00\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"ktv_roomtype\",\"attrCnName\":\"包型\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"小包\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"timeDuration\",\"attrCnName\":\"适用时长\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"3\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"minpeoplenum\",\"attrCnName\":\"最小人数\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"1\"]],\"complexValues\":null}]]}]],\"cpvObjectId\":20410426,\"cpvObjectVersion\":10},\"marketPrice\":\"158.0\",\"amount\":1}]],\"optionalCount\":0}]],\"optionalGroups\":[\"java.util.ArrayList\",[]]},\"extendImage\":null,\"combines\":null,\"saleChannelAggregation\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.SaleChannelAggregationDTO\",\"allSupport\":true,\"supportChannels\":[\"java.util.ArrayList\",[]],\"notSupportChannels\":[\"java.util.ArrayList\",[]]},\"purchaseNote\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.PurchaseNoteDTO\",\"title\":\"购买须知\",\"modules\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"适用时间\",\"icon\":\"https://p0.meituan.net/travelcube/eb358d1a211285207c636aa95594f3751524.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"有效时间\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"购买后90天内有效\"}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"除外日期\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"每周六、周日不可用\"}]]}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"预约规则\",\"icon\":\"https://p0.meituan.net/travelcube/17303e587e8242902a1bf6ecd3eab10b1029.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"无需预约，如遇消费高峰时段您可能需要排队\"}]]}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"其他规则\",\"icon\":\"https://p1.meituan.net/travelcube/4dac95c5f43a52f49b81ece1918925ea858.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"不再与其他优惠同享\"}]]}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"温馨提示\",\"icon\":\"https://p1.meituan.net/travelcube/09a2f606b1636f8b135b8582e3c0a53d1494.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"如需团购券发票，请您在消费时向商户咨询\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"本着为未成年人的健康成长创造良好的社会环境原则，本产品依法不对未成年人售卖\"}]]}]]}]]},\"bizProductId\":null,\"resourceInfos\":null,\"thirdPartyInfo\":null,\"dpDealGroupIdInt\":1097857335,\"mtDealGroupIdInt\":1097857335}";

}