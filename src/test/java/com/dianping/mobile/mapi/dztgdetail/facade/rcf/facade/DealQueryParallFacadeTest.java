package com.dianping.mobile.mapi.dztgdetail.facade.rcf.facade;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.ProcessHandler;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.Response;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.facade.DealQueryFacade;
import com.dianping.mobile.mapi.dztgdetail.mq.TrafficDiffSamplingService;
import com.sankuai.nibpt.transparentvalidator.util.TransparentValidatorUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;

import java.util.HashMap;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 测试DealQueryParallFacade的doStyleCf方法
 */
@RunWith(MockitoJUnitRunner.class)
public class DealQueryParallFacadeTest {

    @InjectMocks
    private DealQueryParallFacade dealQueryParallFacade;

    @Mock
    private DealQueryFacade dealQueryFacade;

    @Mock
    private ProcessHandler<DealCtx> dealStyleBaseQueryHandler;

    @Mock
    private ProcessHandler<DealCtx> dealStyleOtherQueryHandler;

    @Mock
    private ProcessHandler<DealCtx> dealPriceQueryHandler;

    @Mock
    private ProcessHandler<DealCtx> dealPriceOtherQueryHandler;

    @Mock
    private ProcessHandler<DealCtx> dealPromoBaseQueryHandler;

    @Mock
    private ProcessHandler<DealCtx> dealPromoOtherQueryHandler;

    @Mock
    private Transaction transaction;

    @Mock
    private TrafficDiffSamplingService samplingService;

    private MockedStatic<Cat> catMockedStatic;

    private MockedStatic<CompletableFuture> completableFutureMockedStatic;

    private DealBaseReq req;

    private EnvCtx envCtx;

    @Mock
    private ProcessHandler<DealCtx> parallDealBuilderHandler;

    @Mock
    private CompletableFuture<DealCtx> priceCf;

    @Mock
    private CompletableFuture<DealCtx> styleCf;

    @Mock
    private CompletableFuture<DealCtx> promoCf;

    @Mock
    private DealCtx dealCtx;

    @Before
    public void setUp() {
        catMockedStatic = mockStatic(Cat.class);
        completableFutureMockedStatic = mockStatic(CompletableFuture.class);
    }

    @After
    public void tearDown() {
        catMockedStatic.close();
        completableFutureMockedStatic.close();
    }

    /**
     * 测试doStyleCf方法正常执行的场景
     */
    @Test
    public void testDoStyleCfNormalExecution() {
        // arrange
        DealBaseReq req = mock(DealBaseReq.class);
        EnvCtx envCtx = mock(EnvCtx.class);
        DealCtx dealCtx = mock(DealCtx.class);
        when(dealQueryFacade.initDealsBaseData(req, envCtx)).thenReturn(dealCtx);
        catMockedStatic.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
        // act
        DealCtx result = dealQueryParallFacade.doStyleCf(req, envCtx);
        // assert
        verify(dealStyleBaseQueryHandler, times(1)).preThenProc(dealCtx);
        verify(dealStyleOtherQueryHandler, times(1)).preThenProc(dealCtx);
        verify(transaction, times(3)).complete();
        assertEquals(dealCtx, result);
    }

    /**
     * 测试doPriceCf方法正常执行的场景
     */
    @Test
    public void testDoPriceCfNormalExecution() {
        // arrange
        DealBaseReq req = mock(DealBaseReq.class);
        EnvCtx envCtx = mock(EnvCtx.class);
        DealCtx dealCtx = mock(DealCtx.class);
        when(dealQueryFacade.initDealsBaseData(req, envCtx)).thenReturn(dealCtx);
        catMockedStatic.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
        // act
        DealCtx result = dealQueryParallFacade.doPriceCf(req, envCtx);
        // assert
        verify(dealPriceQueryHandler).preThenProc(dealCtx);
        verify(dealPriceOtherQueryHandler).preThenProc(dealCtx);
        verify(transaction, times(3)).complete();
        verify(dealQueryFacade).processMarketPriceDisplayStatus(req, dealCtx);
        assertEquals(dealCtx, result);
    }

    /**
     * 测试doPromoCf方法正常流程
     */
    @Test
    public void testDoPromoCfNormalFlow() {
        // arrange
        DealBaseReq req = mock(DealBaseReq.class);
        EnvCtx envCtx = mock(EnvCtx.class);
        DealCtx dealCtx = mock(DealCtx.class);
        when(dealQueryFacade.initDealsBaseData(req, envCtx)).thenReturn(dealCtx);
        catMockedStatic.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
        // act
        DealCtx result = dealQueryParallFacade.doPromoCf(req, envCtx);
        // assert
        verify(dealPromoBaseQueryHandler).preThenProc(dealCtx);
        verify(dealPromoOtherQueryHandler).preThenProc(dealCtx);
        verify(transaction, times(3)).complete();
        assertEquals(dealCtx, result);
    }

    /**
     * 测试queryDealGroup方法，异常情况
     */
    @Test(expected = Exception.class)
    public void testQueryDealGroupException() throws Throwable {
        dealQueryParallFacade.queryDealGroup(req, envCtx, new HashMap<>());
        // assert is handled by expected exception
    }

    /**
     * 测试doBuildResponse方法，当CompletableFuture正常完成时
     */
    @Test
    public void testDoBuildResponseWhenCompletableFutureCompletesNormally() throws Exception {
        DealBaseReq dealBaseReq = new DealBaseReq();
        EnvCtx envCtx = new EnvCtx();
        catMockedStatic.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
        // arrange
        when(priceCf.get()).thenReturn(dealCtx);
        when(styleCf.get()).thenReturn(dealCtx);
        when(promoCf.get()).thenReturn(dealCtx);
        doNothing().when(parallDealBuilderHandler).preThenProc(any(DealCtx.class));
        MockedStatic<TransparentValidatorUtils> transparentValidatorUtilsMockedStatic = mockStatic(TransparentValidatorUtils.class);
        transparentValidatorUtilsMockedStatic.when(() -> TransparentValidatorUtils.needReport(anyLong())).thenReturn(true);
        // act
        Response<DealGroupPBO> response = dealQueryParallFacade.doBuildResponse(dealBaseReq, envCtx, priceCf, styleCf, promoCf, new HashMap<>());
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
    }

    /**
     * 测试doBuildResponse方法，当CompletableFuture正常完成时
     */
    @Test
    public void testDoBuildResponseWhenCompletableFutureCompletesException() throws Exception {
        catMockedStatic.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
        // act
        Response<DealGroupPBO> response = dealQueryParallFacade.doBuildResponse(null, null, priceCf, styleCf, promoCf, new HashMap<>());
        // assert
        assertNull(response);
    }

}
