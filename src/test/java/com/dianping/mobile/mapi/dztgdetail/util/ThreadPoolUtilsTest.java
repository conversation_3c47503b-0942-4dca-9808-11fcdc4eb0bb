package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.cat.thread.pool.CatExecutorServiceTraceWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ThreadPoolNameEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.mock;

@RunWith(MockitoJUnitRunner.class)
public class ThreadPoolUtilsTest {

    /**
     * 测试 getCatExecutor 方法，当传入的枚举值是 PRICE_EXECUTOR 时，应返回 PRICE_EXECUTOR 线程池。
     */
    @Test
    public void testGetCatExecutorPriceExecutor() {
        // arrange
        ThreadPoolNameEnum threadPoolNameEnum = ThreadPoolNameEnum.PRICE_EXECUTOR;

        // act
        CatExecutorServiceTraceWrapper result = ThreadPoolUtils.getCatExecutor(threadPoolNameEnum);

        // assert
        assertNotNull(result);
        result.shutdown();
    }

    /**
     * 测试 getCatExecutor 方法，当传入的枚举值是 STYLE_EXECUTOR 时，应返回 STYLE_EXECUTOR 线程池。
     */
    @Test
    public void testGetCatExecutorStyleExecutor() {
        // arrange
        ThreadPoolNameEnum threadPoolNameEnum = ThreadPoolNameEnum.STYLE_EXECUTOR;

        // act
        CatExecutorServiceTraceWrapper result = ThreadPoolUtils.getCatExecutor(threadPoolNameEnum);

        // assert
        assertNotNull(result);
        result.shutdown();
    }

    /**
     * 测试 getCatExecutor 方法，当传入的枚举值是 PROMO_EXECUTOR 时，应返回 PROMOTION_EXECUTOR 线程池。
     */
    @Test
    public void testGetCatExecutorPromoExecutor() {
        // arrange
        ThreadPoolNameEnum threadPoolNameEnum = ThreadPoolNameEnum.PROMO_EXECUTOR;

        // act
        CatExecutorServiceTraceWrapper result = ThreadPoolUtils.getCatExecutor(threadPoolNameEnum);

        // assert
        assertNotNull(result);
        result.shutdown();
    }

    /**
     * 测试 getCatExecutor 方法，当传入的枚举值是其他值时，应返回 DEFAULT_EXECUTOR 线程池。
     */
    @Test
    public void testGetCatExecutorDefaultExecutor() {
        // arrange
        ThreadPoolNameEnum threadPoolNameEnum = mock(ThreadPoolNameEnum.class);

        // act
        CatExecutorServiceTraceWrapper result = ThreadPoolUtils.getCatExecutor(threadPoolNameEnum);

        // assert
        assertNotNull(result);
        result.shutdown();
    }
}
