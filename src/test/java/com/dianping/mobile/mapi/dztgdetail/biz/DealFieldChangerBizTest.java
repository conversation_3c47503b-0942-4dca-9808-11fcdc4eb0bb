package com.dianping.mobile.mapi.dztgdetail.biz;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.meituan.service.mobile.prometheus.model.DealModel;
import java.lang.reflect.Method;
import org.json.JSONException;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

public class DealFieldChangerBizTest {

    private DealFieldChangerBiz dealFieldChangerBiz;

    @Before
    public void setUp() {
        dealFieldChangerBiz = new DealFieldChangerBiz();
    }

    private void invokeAlterMenu(DealModel deal) throws Exception {
        Method alterMenuMethod = DealFieldChangerBiz.class.getDeclaredMethod("alterMenu", DealModel.class);
        alterMenuMethod.setAccessible(true);
        alterMenuMethod.invoke(dealFieldChangerBiz, deal);
    }

    /**
     * 测试 menu 为空的情况
     */
    @Test
    public void testAlterMenuWhenMenuIsNull() throws Throwable {
        // arrange
        DealModel deal = new DealModel();
        deal.setMenu(null);
        // act
        invokeAlterMenu(deal);
        // assert
        assertEquals(null, deal.getMenu());
    }

    /**
     * 测试 menu 为 "[[]]" 的情况
     */
    @Test
    public void testAlterMenuWhenMenuIsEmpty() throws Throwable {
        // arrange
        DealModel deal = new DealModel();
        deal.setMenu("[[]]");
        // act
        invokeAlterMenu(deal);
        // assert
        assertEquals("[[]]", deal.getMenu());
    }
}
