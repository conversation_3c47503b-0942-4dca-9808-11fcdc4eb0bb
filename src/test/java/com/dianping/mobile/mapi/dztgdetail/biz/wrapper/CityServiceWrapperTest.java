package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.meituan.service.mobile.group.geo.bean.CityInfo;
import com.meituan.service.mobile.group.geo.service.CityService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class CityServiceWrapperTest {

    @InjectMocks
    private CityServiceWrapper cityServiceWrapper;

    @Mock
    private CityService cityService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试getCityById方法，正常情况
     */
    @Test
    public void testGetCityByIdNormal() throws Throwable {
        // arrange
        int currentCityId = 1;
        CityInfo cityInfo = new CityInfo();
        when(cityService.getCityById(currentCityId)).thenReturn(cityInfo);
        // act
        CityInfo result = cityServiceWrapper.getCityById(currentCityId);
        // assert
        assertEquals(cityInfo, result);
        verify(cityService, times(1)).getCityById(currentCityId);
    }

    /**
     * 测试getCityById方法，异常情况
     */
    @Test
    public void testGetCityByIdException() throws Throwable {
        // arrange
        int currentCityId = 1;
        when(cityService.getCityById(currentCityId)).thenThrow(new RuntimeException());
        // act
        CityInfo result = cityServiceWrapper.getCityById(currentCityId);
        // assert
        assertNull(result);
        verify(cityService, times(1)).getCityById(currentCityId);
    }
}
