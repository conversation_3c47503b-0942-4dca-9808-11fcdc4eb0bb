package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Collections;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.mock;

@RunWith(MockitoJUnitRunner.class)
public class ResvDealBuyHelperTest {

    private DealCtx ctx;
    @Before
    public void setUp() {
        EnvCtx envCtx = new EnvCtx();
        ctx = new DealCtx(envCtx);
        DealGroupBaseDTO dealGroupBaseDTO = new DealGroupBaseDTO();
        dealGroupBaseDTO.setDealGroupPrice(new BigDecimal("100"));
        ctx.setDealGroupBase(dealGroupBaseDTO);
    }

    /**
     * 测试 getResvButton 方法，当 ctx.getIsCanResv() 为 true，但 dealGroupDealDTO 为 null 时
     */
    @Test
    public void testGetResvButton_IsCanResvFalse() {
        // arrange
        ctx.setIsCanResv(true);

        DealGroupDealDTO dealGroupDealDTO = new DealGroupDealDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("retailPriceStyle");

        dealGroupDealDTO.setAttrs(Lists.newArrayList(attrDTO));
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        ctx.setDealGroupDTO(dealGroupDTO);

        // act
        DealBuyBtn result = DealBuyHelper.getResvButton(ctx);

        // assert
        assertNull(result);
    }
    /**
     * 测试 getResvButton 方法，当 ctx.getIsCanResv() 为 true，dealGroupDealDTO 不为 null，但 retailPriceStyle 不为 "1" 时
     */
    @Test
    public void testGetResvButton_RetailPriceStyleNot1() {
        // arrange
        ctx.setIsCanResv(true);

        DealGroupDealDTO dealGroupDealDTO = new DealGroupDealDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("retailPriceStyle");
        attrDTO.setValue(Lists.newArrayList("2"));
        dealGroupDealDTO.setAttrs(Lists.newArrayList(attrDTO));
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDeals(Collections.singletonList(dealGroupDealDTO));
        ctx.setDealGroupDTO(dealGroupDTO);

        // act
        DealBuyBtn result = DealBuyHelper.getResvButton(ctx);

        // assert
        assertEquals("预约接种", result.getBtnTitle());
        assertNull(result.getBtnSubTitle());
    }

    /**
     * 测试 getResvButton 方法，当 ctx.getIsCanResv() 为 true，dealGroupDealDTO 不为 null，且 retailPriceStyle 为 "1" 时
     */
    @Test
    public void testGetResvButton_RetailPriceStyle1() {
        // arrange
        ctx.setIsCanResv(true);

        DealGroupDealDTO dealGroupDealDTO = new DealGroupDealDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("retailPriceStyle");
        attrDTO.setValue(Lists.newArrayList("1"));
        dealGroupDealDTO.setAttrs(Lists.newArrayList(attrDTO));
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDeals(Collections.singletonList(dealGroupDealDTO));
        ctx.setDealGroupDTO(dealGroupDTO);


        // act
        DealBuyBtn result = DealBuyHelper.getResvButton(ctx);

        // assert
        assertEquals("预约接种", result.getBtnTitle());
        assertEquals("到门诊支付¥100", result.getBtnSubTitle());
    }
}