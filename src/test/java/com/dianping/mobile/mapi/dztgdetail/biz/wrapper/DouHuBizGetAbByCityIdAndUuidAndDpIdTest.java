package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.sankuai.douhu.absdk.bean.DouHuRequest;
import com.sankuai.douhu.absdk.bean.DouHuResponse;
import com.sankuai.douhu.absdk.client.DouHuClient;
import com.sankuai.douhu.absdk.enums.ErrorCode;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DouHuBizGetAbByCityIdAndUuidAndDpIdTest {

    @Mock
    private DouHuClient douHuClient;

    @InjectMocks
    private DouHuBiz douHuBiz;

    private DealCtx dealCtx;

    private EnvCtx envCtx;

    @Before
    public void setUp() {
        envCtx = new EnvCtx();
        envCtx.setUuid("test-uuid");
        envCtx.setDpId("test-dpid");
        dealCtx = new DealCtx(envCtx);
        dealCtx.setDpId(123);
        dealCtx.setMtId(456);
    }

    /**
     * Test when module is null, should return null
     */
    @Test
    public void testGetAbByCityIdAndUuidAndDpId_ModuleNull() throws Throwable {
        // arrange
        Integer cityId = 1;
        // act
        ModuleAbConfig result = douHuBiz.getAbByCityIdAndUuidAndDpId(cityId, dealCtx, null);
        // assert
        assertNull(result);
        verifyNoInteractions(douHuClient);
    }

    /**
     * Test when AB test returns null response
     */
    @Test
    public void testGetAbByCityIdAndUuidAndDpId_NullResponse() throws Throwable {
        // arrange
        Integer cityId = 1;
        String module = "test-module";
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(null);
        // act
        ModuleAbConfig result = douHuBiz.getAbByCityIdAndUuidAndDpId(cityId, dealCtx, module);
        // assert
        assertNull(result);
        verify(douHuClient).tryAb(any(DouHuRequest.class));
    }

    /**
     * Test when AB test returns error response
     */
    @Test
    public void testGetAbByCityIdAndUuidAndDpId_ErrorResponse() throws Throwable {
        // arrange
        Integer cityId = 1;
        String module = "test-module";
        DouHuResponse response = new DouHuResponse();
        response.setCode(ErrorCode.FAIL.getCode());
        response.setSk("");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbByCityIdAndUuidAndDpId(cityId, dealCtx, module);
        // assert
        assertNull(result);
        verify(douHuClient).tryAb(any(DouHuRequest.class));
    }

    /**
     * Test when exception occurs during AB test call
     */
    @Test
    public void testGetAbByCityIdAndUuidAndDpId_Exception() throws Throwable {
        // arrange
        Integer cityId = 1;
        String module = "test-module";
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenThrow(new RuntimeException("test exception"));
        // act
        ModuleAbConfig result = douHuBiz.getAbByCityIdAndUuidAndDpId(cityId, dealCtx, module);
        // assert
        assertNull(result);
        verify(douHuClient).tryAb(any(DouHuRequest.class));
    }

    /**
     * Test successful AB test with empty sk in response
     */
    @Test
    public void testGetAbByCityIdAndUuidAndDpId_EmptySk() throws Throwable {
        // arrange
        Integer cityId = 1;
        String module = "test-module";
        DouHuResponse response = new DouHuResponse();
        response.setCode(ErrorCode.SUCCESS.getCode());
        response.setSk("");
        response.setAbQueryId("query-id");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbByCityIdAndUuidAndDpId(cityId, dealCtx, module);
        // assert
        assertNull(result);
        verify(douHuClient).tryAb(any(DouHuRequest.class));
    }
}
