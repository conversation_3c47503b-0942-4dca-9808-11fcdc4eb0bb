package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.gmkt.activity.api.enums.PriceStrengthTimeEnum;
import com.sankuai.swan.udqs.api.QueryData;
import com.sankuai.swan.udqs.api.Result;
import com.sankuai.swan.udqs.api.SwanQueryService;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import java.math.BigDecimal;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class DealBuilderProcessor_QueryPriceAbilityTest {

    @InjectMocks
    private DealBuilderProcessor dealBuilderProcessor;

    @Mock
    private SwanQueryService swanQueryService;

    private int dealGroupId = 1;

    private PriceStrengthTimeEnum priceStrengthTime = PriceStrengthTimeEnum.THIRTY_DAY;

    private Date priceAbilityDate = new Date();

    @Before
    public void setUp() {
        when(swanQueryService.queryByKey(anyInt(), anyString(), any())).thenReturn(new Result());
    }

    @Test
    public void testQueryPriceAbilityWhenResultSetIsEmpty() {
        Result<QueryData> result = new Result<>();
        result.setIfSuccess(true);
        QueryData queryData = new QueryData();
        queryData.setResultSet(Collections.emptyList());
        result.setData(queryData);
        when(swanQueryService.queryByKey(anyInt(), anyString(), any())).thenReturn(result);
        BigDecimal actual = dealBuilderProcessor.queryPriceAbility(dealGroupId, priceStrengthTime, priceAbilityDate);
        assertNull(actual);
    }

    @Test
    public void testQueryPriceAbilityWhenResultSetHasNoMinPrice() {
        Result<QueryData> result = new Result<>();
        result.setIfSuccess(true);
        QueryData queryData = new QueryData();
        Map<String, Object> map = new HashMap<>();
        map.put("minSalesPrice30d", null);
        queryData.setResultSet(Collections.singletonList(map));
        result.setData(queryData);
        when(swanQueryService.queryByKey(anyInt(), anyString(), any())).thenReturn(result);
        BigDecimal actual = dealBuilderProcessor.queryPriceAbility(dealGroupId, priceStrengthTime, priceAbilityDate);
        assertNull(actual);
    }

    @Test
    public void testQueryPriceAbilityWhenResultSetHasMinPrice() {
        Result<QueryData> result = new Result<>();
        result.setIfSuccess(true);
        QueryData queryData = new QueryData();
        Map<String, Object> map = new HashMap<>();
        map.put("minSalesPrice30d", 100.0);
        queryData.setResultSet(Collections.singletonList(map));
        result.setData(queryData);
        when(swanQueryService.queryByKey(anyInt(), anyString(), any())).thenReturn(result);
        BigDecimal actual = dealBuilderProcessor.queryPriceAbility(dealGroupId, priceStrengthTime, priceAbilityDate);
        assertEquals(BigDecimal.valueOf(100.0).setScale(2, BigDecimal.ROUND_HALF_UP), actual);
    }

    @Test
    public void testQueryPriceAbilityWhenResultIsNull() {
        when(swanQueryService.queryByKey(anyInt(), anyString(), any())).thenReturn(null);
        BigDecimal actual = dealBuilderProcessor.queryPriceAbility(dealGroupId, priceStrengthTime, priceAbilityDate);
        assertNull(actual);
    }

    @Test
    public void testQueryPriceAbilityWhenResultIsNotSuccess() {
        Result<QueryData> result = new Result<>();
        result.setIfSuccess(false);
        when(swanQueryService.queryByKey(anyInt(), anyString(), any())).thenReturn(result);
        BigDecimal actual = dealBuilderProcessor.queryPriceAbility(dealGroupId, priceStrengthTime, priceAbilityDate);
        assertNull(actual);
    }

    @Test
    public void testQueryPriceAbilityWhenResultDataIsNull() {
        Result<QueryData> result = new Result<>();
        result.setIfSuccess(true);
        result.setData(null);
        when(swanQueryService.queryByKey(anyInt(), anyString(), any())).thenReturn(result);
        BigDecimal actual = dealBuilderProcessor.queryPriceAbility(dealGroupId, priceStrengthTime, priceAbilityDate);
        assertNull(actual);
    }
}
