//package com.dianping.mobile.mapi.dztgdetail.facade;
//
//import static org.junit.Assert.assertEquals;
//import static org.junit.Assert.assertNull;
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.Mockito.mock;
//import static org.mockito.Mockito.when;
//
//import com.dianping.deal.shop.dto.BestShopDTO;
//import com.dianping.deal.shop.dto.BestShopReq;
//import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
//import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
//import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
//import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetcorpwxentrymaterialRequest;
//import com.dianping.mobile.mapi.dztgdetail.util.DealProductUtils;
//import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
//import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
//
//import java.util.concurrent.Future;
//
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockitoAnnotations;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.modules.junit4.PowerMockRunner;
//
//@RunWith(PowerMockRunner.class)
//@PrepareForTest({ DealProductUtils.class })
//public class CorpWxEntryMaterialFacadeFindbestShopIdTest {
//
//    @Mock
//    private QueryCenterWrapper queryCenterWrapper;
//
//    @Mock
//    private DealGroupWrapper dealGroupWrapper;
//
//    @InjectMocks
//    private CorpWxEntryMaterialFacade corpWxEntryMaterialFacade;
//
//    @Before
//    public void setUp() {
//        MockitoAnnotations.initMocks(this);
//        PowerMockito.mockStatic(DealProductUtils.class);
//    }
//
//    /**
//     * Test case when shopId is null and best shop is found
//     * Expected: Return the best shop ID from the future result
//     */
//    @Test
//    public void testFindbestShopId_WithNullShopIdAndBestShopFound() throws Throwable {
//        // arrange
//        GetcorpwxentrymaterialRequest req = new GetcorpwxentrymaterialRequest();
//        req.setShopIdForLong(null);
//        req.setDealgroupid("789");
//        req.setCityid(1);
//        req.setUserLat(31.2304);
//        req.setUserlng(121.4737);
//        EnvCtx ctx = mock(EnvCtx.class);
//        when(ctx.isMt()).thenReturn(false);
//        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
//        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(dealGroupDTO);
//        Future shopFuture = mock(Future.class);
//        when(dealGroupWrapper.preDealGroupBestShop(any(BestShopReq.class))).thenReturn(shopFuture);
//        BestShopDTO bestShop = mock(BestShopDTO.class);
//        Long bestShopId = 888L;
//        when(bestShop.getDpShopId()).thenReturn(bestShopId);
//        when(dealGroupWrapper.getFutureResult(shopFuture)).thenReturn(bestShop);
//        // act
//        Long result = corpWxEntryMaterialFacade.findbestShopId(req, ctx);
//        // assert
//        assertEquals(bestShopId, result);
//    }
//
//    /**
//     * Test case when shopId is null and best shop is not found
//     * Expected: Return null
//     */
//    @Test
//    public void testFindbestShopId_WithNullShopIdAndBestShopNotFound() throws Throwable {
//        // arrange
//        GetcorpwxentrymaterialRequest req = new GetcorpwxentrymaterialRequest();
//        req.setShopIdForLong(null);
//        req.setDealgroupid("789");
//        req.setCityid(1);
//        req.setUserLat(31.2304);
//        req.setUserlng(121.4737);
//        EnvCtx ctx = mock(EnvCtx.class);
//        when(ctx.isMt()).thenReturn(false);
//        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
//        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(dealGroupDTO);
//        Future shopFuture = mock(Future.class);
//        when(dealGroupWrapper.preDealGroupBestShop(any(BestShopReq.class))).thenReturn(shopFuture);
//        // Best shop is null
//        when(dealGroupWrapper.getFutureResult(shopFuture)).thenReturn(null);
//        // act
//        Long result = corpWxEntryMaterialFacade.findbestShopId(req, ctx);
//        // assert
//        assertNull(result);
//    }
//
//}
