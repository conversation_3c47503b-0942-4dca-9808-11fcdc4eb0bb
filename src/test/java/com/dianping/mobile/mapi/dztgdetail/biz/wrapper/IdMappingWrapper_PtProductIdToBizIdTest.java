package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.mpproduct.idservice.api.model.BizProductIdDTO;
import com.sankuai.mpproduct.idservice.api.response.ProductIdConvertResponse;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Future;
import org.mockito.InjectMocks;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class IdMappingWrapper_PtProductIdToBizIdTest {

    @Mock
    private Future future;

    @Mock
    private ProductIdConvertResponse response;

    private IdMappingWrapper idMappingWrapper = new IdMappingWrapper();

    @Test
    public void testPtProductIdToBizIdFutureIsNull() throws Throwable {
        Map<Long, Long> actual = idMappingWrapper.ptProductIdToBizId(null);
        assertEquals(Collections.emptyMap(), actual);
    }

    @Test
    public void testPtProductIdToBizIdResponseIsNull() throws Throwable {
        when(future.get()).thenReturn(null);
        Map<Long, Long> actual = idMappingWrapper.ptProductIdToBizId(future);
        assertEquals(Collections.emptyMap(), actual);
    }

    @Test
    public void testPtProductIdToBizIdResponseIsNotSuccess() throws Throwable {
        when(future.get()).thenReturn(response);
        when(response.isSuccess()).thenReturn(false);
        Map<Long, Long> actual = idMappingWrapper.ptProductIdToBizId(future);
        assertEquals(Collections.emptyMap(), actual);
    }

    @Test
    public void testPtProductIdToBizIdResultIsNull() throws Throwable {
        when(future.get()).thenReturn(response);
        when(response.isSuccess()).thenReturn(true);
        when(response.getProductIdConvertResult()).thenReturn(null);
        Map<Long, Long> actual = idMappingWrapper.ptProductIdToBizId(future);
        assertEquals(Collections.emptyMap(), actual);
    }

    @Test
    public void testPtProductIdToBizIdResultIsEmpty() throws Throwable {
        when(future.get()).thenReturn(response);
        when(response.isSuccess()).thenReturn(true);
        when(response.getProductIdConvertResult()).thenReturn(Collections.emptyMap());
        Map<Long, Long> actual = idMappingWrapper.ptProductIdToBizId(future);
        assertEquals(Collections.emptyMap(), actual);
    }

    @Test
    public void testPtProductIdToBizIdResultIsNotEmpty() throws Throwable {
        Map<Long, BizProductIdDTO> map = new HashMap<>();
        map.put(1L, new BizProductIdDTO(2L, 1));
        when(future.get()).thenReturn(response);
        when(response.isSuccess()).thenReturn(true);
        when(response.getProductIdConvertResult()).thenReturn(map);
        Map<Long, Long> actual = idMappingWrapper.ptProductIdToBizId(future);
        Map<Long, Long> expected = new HashMap<>();
        expected.put(1L, 2L);
        assertEquals(expected, actual);
    }
}
