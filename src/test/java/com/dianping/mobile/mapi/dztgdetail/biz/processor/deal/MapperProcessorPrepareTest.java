package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class MapperProcessorPrepareTest {

    @InjectMocks
    private MapperProcessor mapperProcessor;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private MapperWrapper mapperWrapper;

    @Mock
    private Future<?> dealIdMapperFuture;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Before
    public void setUp() {
        when(dealCtx.getMtId()).thenReturn(123);
        when(dealCtx.getMtLongShopId()).thenReturn(456L);
        when(dealCtx.getMtCityId()).thenReturn(789);
    }

    /**
     * 测试当 enableQueryCenterForMainApi 返回 true 时，调用 prepareByQueryCenter 方法
     */
    @Test
    public void testPrepareWhenEnableQueryCenterForMainApiReturnsTrue() throws Throwable {
        // arrange
        when(GreyUtils.enableQueryCenterForMainApi(dealCtx)).thenReturn(true);
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(new DealGroupDTO());
        // act
        mapperProcessor.prepare(dealCtx);
        // assert
        verify(dealCtx, times(1)).setDpId(anyInt());
        verify(dealCtx, times(1)).setDpShopId(anyInt());
        verify(dealCtx, times(1)).setDpLongShopId(anyLong());
        verify(dealCtx, times(1)).setDpCityId(anyInt());
    }

    /**
     * 测试当 enableQueryCenterForMainApi 返回 false 时，通过 dealGroupWrapper 和 mapperWrapper 获取数据并设置到 ctx 中
     */
    @Test
    public void testPrepareWhenEnableQueryCenterForMainApiReturnsFalse() throws Throwable {
        // arrange
        when(GreyUtils.enableQueryCenterForMainApi(dealCtx)).thenReturn(false);
        when(dealGroupWrapper.preDpDealGroupId(123)).thenReturn(dealIdMapperFuture);
        when(mapperWrapper.preDpShopIdByMtShopId(456L)).thenReturn(dealIdMapperFuture);
        when(mapperWrapper.preDpCityByMtCity(789)).thenReturn(dealIdMapperFuture);
        when(dealGroupWrapper.getDpDealGroupId(dealIdMapperFuture)).thenReturn(101);
        when(mapperWrapper.getDpShopIdByMtShopIdLong(dealIdMapperFuture)).thenReturn(202L);
        when(mapperWrapper.getDpCityByMtCity(dealIdMapperFuture)).thenReturn(303);
        // act
        mapperProcessor.prepare(dealCtx);
        // assert
        verify(dealCtx, times(1)).setDpId(101);
        verify(dealCtx, times(1)).setDpShopId(202);
        verify(dealCtx, times(1)).setDpLongShopId(202L);
        verify(dealCtx, times(1)).setDpCityId(303);
    }

    @Mock
    private DealCtx dealCtx;

}
