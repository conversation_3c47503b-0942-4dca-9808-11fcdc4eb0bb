package com.dianping.mobile.mapi.dztgdetail.biz.processor.promo;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.lang.reflect.Method;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

/**
 * 测试 PromoExposureProcessor 类的 isThu 方法
 */
@RunWith(MockitoJUnitRunner.class)
public class PromoExposureProcessorTest {

    /**
     * 使用反射调用 PromoExposureProcessor 类的私有方法 isThu
     */
    private boolean invokeIsThuMethod(LocalDateTime now) throws Exception {
        PromoExposureProcessor processor = new PromoExposureProcessor();
        Method method = PromoExposureProcessor.class.getDeclaredMethod("isThu", LocalDateTime.class);
        method.setAccessible(true);
        return (boolean) method.invoke(processor, now);
    }

    /**
     * 测试 isThu 方法，当传入的日期是星期四时，返回 true
     */
    @Test
    public void testIsThuWhenThursday() throws Throwable {
        // arrange
        // 2023年10月5日是星期四
        LocalDateTime thursday = LocalDateTime.of(2023, 10, 5, 12, 0);
        // act
        boolean result = invokeIsThuMethod(thursday);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 isThu 方法，当传入的日期不是星期四时，返回 false
     */
    @Test
    public void testIsThuWhenNotThursday() throws Throwable {
        // arrange
        // 2023年10月6日是星期五
        LocalDateTime friday = LocalDateTime.of(2023, 10, 6, 12, 0);
        // act
        boolean result = invokeIsThuMethod(friday);
        // assert
        assertFalse(result);
    }
}
