package com.dianping.mobile.mapi.dztgdetail.rcf.deallayout;

import com.alibaba.fastjson.JSONObject;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.deallayout.DealLayoutService;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.deallayout.dto.ModuleItem;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.pageconfig.DealDetailPageConfigService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;


import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @create 2024/11/28 11:30
 */
@RunWith(MockitoJUnitRunner.class)
public class DealLayoutServiceTest {
    String key;
    String extraInfo;
    String generalInfo;
    Map<String, String> params;
    @InjectMocks
    private DealLayoutService dealLayoutService;
    @Mock
    private DealDetailPageConfigService dealDetailPageConfigService;


    @Test
    public void testDealLayoutService() {
        key = "key";
        extraInfo = "extraInfo";
        generalInfo = "generalInfo";
        params = new HashMap<String, String>();
        dealLayoutService.getDealLayout(key, extraInfo, generalInfo, params);
        params.put("dealConfig", "cross_buy");
        Map<String, ModuleItem> result= dealLayoutService.getDealLayout(key, extraInfo, generalInfo, params);
        Assert.assertNotNull(result);
    }

    @Test
    public void testDealDetailPageConfigService() {
        String config = "{\n" +
                "    \"tuandeal_newtuandealtab_beauty_hair_buyrules\": [\n" +
                "        [\n" +
                "            {\n" +
                "                \"key\": \"local_dealdetail_purchase_notes_module\",\n" +
                "                \"priority\": 3\n" +
                "            },\n" +
                "            {\n" +
                "                \"key\": \"local_dealdetail_public_welfare_module\",\n" +
                "                \"priority\": 3\n" +
                "            },\n" +
                "            {\n" +
                "                \"key\": \"local_dealdetail_npssurvey_module\",\n" +
                "                \"priority\": 3\n" +
                "            },\n" +
                "            {\n" +
                "                \"key\": \"local_dealdetail_shop_info_module\",\n" +
                "                \"priority\": 3\n" +
                "            },\n" +
                "            {\n" +
                "                \"key\": \"local_dealdetail_consulor_module\",\n" +
                "                \"priority\": 3\n" +
                "            }\n" +
                "        ],\n" +
                "        [\n" +
                "            {\n" +
                "                \"key\": \"local_dealdetail_hair_dye_works_module\",\n" +
                "                \"priority\": 3\n" +
                "            }\n" +
                "        ]\n" +
                "    ]\n" +
                "}";
        JSONObject jsonObject = JSONObject.parseObject(config);
        Map<String , ModuleItem> result = dealDetailPageConfigService.processPageConfig("tuandeal_newtuandealtab_beauty_hair_buyrules", jsonObject);
        Assert.assertNotNull(result);
    }

    @Test
    public void testParseDealDetailTabConfigs() {
        String config = "{\n" +
                "    \"tuandeal_newtuandealtab_beauty_hair_buyrules\": [\n" +
                "        [\n" +
                "            {\n" +
                "                \"key\": \"local_dealdetail_purchase_notes_module\",\n" +
                "                \"priority\": 3\n" +
                "            },\n" +
                "            {\n" +
                "                \"key\": \"local_dealdetail_public_welfare_module\",\n" +
                "                \"priority\": 3\n" +
                "            },\n" +
                "            {\n" +
                "                \"key\": \"local_dealdetail_npssurvey_module\",\n" +
                "                \"priority\": 3\n" +
                "            },\n" +
                "            {\n" +
                "                \"key\": \"local_dealdetail_shop_info_module\",\n" +
                "                \"priority\": 3\n" +
                "            },\n" +
                "            {\n" +
                "                \"key\": \"local_dealdetail_consulor_module\",\n" +
                "                \"priority\": 3\n" +
                "            }\n" +
                "        ],\n" +
                "        [\n" +
                "            {\n" +
                "                \"key\": \"local_dealdetail_hair_dye_works_module\",\n" +
                "                \"priority\": 3\n" +
                "            }\n" +
                "        ]\n" +
                "    ]\n" +
                "}";
        Class clazz = DealLayoutService.class;

        try {
            Method method = clazz.getDeclaredMethod("parseDealDetailTabConfigs", String.class);
            method.setAccessible(true);
            method.invoke(dealLayoutService, config);
            Assert.assertNotNull(dealLayoutService.fetchDealDetailTabConfig("tuandeal_newtuandealtab_beauty_hair_buyrules"));
        } catch (NoSuchMethodException e) {
        } catch (InvocationTargetException e) {
        } catch (IllegalAccessException e) {
        }

    }
}
