package com.dianping.mobile.mapi.dztgdetail.biz.processor.productdetail;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

import com.dianping.mobile.mapi.dztgdetail.util.CommonModuleUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.CommonModuleWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericProductDetailPageResponse;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;

@RunWith(PowerMockRunner.class)
@PrepareForTest({Lion.class})
@PowerMockIgnore({"javax.management.*", "javax.crypto.*"})
public class ProductDetailCommonModuleProcessorTest {

    @InjectMocks
    private ProductDetailCommonModuleProcessor processor;

    @Mock
    private CommonModuleWrapper commonModuleWrapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(Lion.class);
    }

    @Test
    public void testIsEnable_WithValidModuleKeys() {
        // Prepare test data
        DealCtx ctx = Mockito.mock(DealCtx.class);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO category = new DealGroupCategoryDTO();
        category.setCategoryId(123L);
        dealGroupDTO.setCategory(category);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);

        // Mock Lion configuration
        Map<Long, List<String>> categoryModule = new HashMap<>();
        categoryModule.put(123L, Arrays.asList("module1", "module2"));

        CommonModuleUtil.CommonConfig config = new CommonModuleUtil.CommonConfig();
        config.setCategoryModule(categoryModule);
        config.setServiceTypeModule(new HashMap<>());

        PowerMockito.when(Lion.getBean(anyString(), anyString(), any())).thenAnswer( invocation -> config);

        // Execute and verify
        boolean result = processor.isEnable(ctx);
        assertTrue(!result);
    }

    @Test
    public void testIsEnable_WithNoModuleKeys() {
        // Prepare test data
        DealCtx ctx = Mockito.mock(DealCtx.class);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);

        // Execute and verify
        boolean result = processor.isEnable(ctx);
        assertFalse(result);
    }

    @Test
    @SuppressWarnings("unchecked")
    public void testPrepare() {
        // Prepare test data
        DealCtx ctx = Mockito.mock(DealCtx.class);
        FutureCtx futureCtx = new FutureCtx();
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
        when(ctx.getEnvCtx()).thenReturn(envCtx);

        CompletableFuture<String> future = CompletableFuture.completedFuture("test");
        when(commonModuleWrapper.preQueryCommonModule(any())).thenAnswer(answer -> future);

        // Execute
        processor.prepare(ctx);

        // Verify
        verify(commonModuleWrapper).preQueryCommonModule(any());
        assertNotNull(ctx.getFutureCtx().getCommomModuleFuture());
    }

    @Test
    @SuppressWarnings("unchecked")
    public void testProcess_WithValidResponse() {
        // Prepare test data
        DealCtx ctx = Mockito.mock(DealCtx.class);
        FutureCtx futureCtx = new FutureCtx();
        Future<String> future = Mockito.mock(Future.class);
        futureCtx.setCommomModuleFuture(future);
        when(ctx.getFutureCtx()).thenReturn(futureCtx);

        GenericProductDetailPageResponse mockResponse = new GenericProductDetailPageResponse();
        String jsonResponse = JSONObject.toJSONString(mockResponse);
        when(commonModuleWrapper.getFutureResult(any(Future.class))).thenReturn(jsonResponse);

        // Execute
        processor.process(ctx);

        // Verify
        verify(commonModuleWrapper).getFutureResult((Future) any());
        verify(ctx).setCommonModuleResponse(any());
    }

    @Test
    public void testProcess_WithNullFuture() {
        // Prepare test data
        DealCtx ctx = Mockito.mock(DealCtx.class);
        FutureCtx futureCtx = new FutureCtx();
        when(ctx.getFutureCtx()).thenReturn(futureCtx);

        // Execute
        processor.process(ctx);

        // Verify
        verify(commonModuleWrapper, never()).getFutureResult((Future) any());
        verify(ctx, never()).setCommonModuleResponse(any());
    }

    @Test
    @SuppressWarnings("unchecked")
    public void testProcess_WithEmptyResponse() {
        // Prepare test data
        DealCtx ctx = Mockito.mock(DealCtx.class);
        FutureCtx futureCtx = new FutureCtx();
        Future<String> future = Mockito.mock(Future.class);
        futureCtx.setCommomModuleFuture(future);
        when(ctx.getFutureCtx()).thenReturn(futureCtx);

        when(commonModuleWrapper.getFutureResult(any(Future.class))).thenReturn("");

        // Execute
        processor.process(ctx);

        // Verify
        verify(commonModuleWrapper).getFutureResult((Future) any());
        verify(ctx, never()).setCommonModuleResponse(any());
    }
} 