package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

/**
 * Test class for ExaminerAbstractHandler.getDisplayGender method
 */
public class ExaminerAbstractHandlerGetDisplayGenderTest {

    private final TestExaminerHandler handler = new TestExaminerHandler();

    private class TestExaminerHandler extends ExaminerAbstractHandler {

        @Override
        public void execute(DealCtx ctx) {
            // Not needed for testing getDisplayGender
        }
    }

    /**
     * Test when input list is null
     */
    @Test
    public void testGetDisplayGender_NullList() {
        // arrange
        List<String> genderList = null;
        // act
        String result = handler.getDisplayGender(genderList);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when input list is empty
     */
    @Test
    public void testGetDisplayGender_EmptyList() {
        // arrange
        List<String> genderList = new ArrayList<>();
        // act
        String result = handler.getDisplayGender(genderList);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when first element is "通用"
     */
    @Test
    public void testGetDisplayGender_Universal() {
        // arrange
        List<String> genderList = Arrays.asList("通用", "男", "女");
        // act
        String result = handler.getDisplayGender(genderList);
        // assert
        assertEquals("男女通用", result);
    }

    /**
     * Test when first element is specific gender
     */
    @Test
    public void testGetDisplayGender_SpecificGender() {
        // arrange
        List<String> genderList = Arrays.asList("男", "通用");
        // act
        String result = handler.getDisplayGender(genderList);
        // assert
        assertEquals("男", result);
    }

    /**
     * Test when list has single element
     */
    @Test
    public void testGetDisplayGender_SingleElement() {
        // arrange
        List<String> genderList = Arrays.asList("女");
        // act
        String result = handler.getDisplayGender(genderList);
        // assert
        assertEquals("女", result);
    }
}
