package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.detail.dto.DealGroupDetailDTO;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MtDetailProductType;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.Pair;
import org.junit.*;
import java.util.List;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import java.util.ArrayList;
import com.dianping.mobile.mapi.dztgdetail.common.enums.FreeDealEnum;
import org.mockito.Mockito;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.runner.RunWith.*;

/**
 * 测试MtDealDetailBuilder类的toStructedDetails方法
 */
public class MtDealDetailBuilder_ToStructedDetailsTest {

    private MtDealDetailBuilder builder = new MtDealDetailBuilder();

    private EnvCtx envCtx = new EnvCtx();

    private DealCtx dealCtx = new DealCtx(envCtx);

    private DealGroupBaseDTO dealGroupBaseDto = new DealGroupBaseDTO();

    private DealGroupDetailDTO dealGroupDetailDto = new DealGroupDetailDTO();

    private void prepareDealCtx(boolean hasImportantPoint) {
        dealGroupBaseDto.setDealGroupType(MtDetailProductType.PRODUCT_TYPE_NORMAL);
        dealGroupDetailDto.setInfo("Some info");
        if (hasImportantPoint) {
            dealGroupDetailDto.setImportantPoint("Important point");
        } else {
            dealGroupDetailDto.setImportantPoint(null);
        }
        dealCtx.setDealGroupBase(dealGroupBaseDto);
        dealCtx.setDealGroupDetailDTO(dealGroupDetailDto);
    }

    /**
     * 测试toStructedDetails方法，当dealCtx为null时
     */
    @Test(expected = NullPointerException.class)
    public void testToStructedDetailsWithNullDealCtx() {
        boolean hasStructedDetail = false;
        builder.toStructedDetails(null, hasStructedDetail);
    }

    /**
     * 测试toStructedDetails方法，当dealCtx非空且dealGroupBaseDto为null时
     */
    @Test(expected = NullPointerException.class)
    public void testToStructedDetailsWithNullDealGroupBaseDto() {
        dealCtx.setDealGroupBase(null);
        boolean hasStructedDetail = false;
        builder.toStructedDetails(dealCtx, hasStructedDetail);
    }

    /**
     * 测试toStructedDetails方法，当dealCtx非空且dealGroupDetailDto为null时
     */
    @Test(expected = NullPointerException.class)
    public void testToStructedDetailsWithNullDealGroupDetailDto() {
        dealCtx.setDealGroupDetailDTO(null);
        boolean hasStructedDetail = false;
        builder.toStructedDetails(dealCtx, hasStructedDetail);
    }
}
