package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.token.UserStatusResult;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.CollaborativeRequest;
import com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam;
import com.meituan.service.mobile.message.recommend.CollaborativeResponseMessage;
import com.meituan.service.mobile.message.recommend.RESULT_CODE;
import com.meituan.service.mobile.message.recommend.RPCRecommendService;
import org.apache.thrift.TException;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RecommendWrapperTest {

    @InjectMocks
    private RecommendWrapper recommendWrapper;

    @Mock
    private RPCRecommendService.Iface recommendService;

    @Mock
    private IMobileContext context;

    @Mock
    private UserStatusResult userStatusResult;

    private CollaborativeRequest request;

    private MtCommonParam mtCommonParam;

    private MockedStatic<Lion> lionMockedStatic;

    @Before
    public void setUp() {
        request = new CollaborativeRequest();
        // Mocking context to return a default value
        when(context.getParameter(anyString())).thenReturn("1");
        // Mocking user status to return a valid user ID
        when(context.getUserStatus()).thenReturn(userStatusResult);
        when(userStatusResult.getMtUserId()).thenReturn(12345L);
        mtCommonParam = new MtCommonParam(context);
        lionMockedStatic = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        lionMockedStatic.close();
    }

    @Test
    public void testGetCollaborativeRecommendSwitchOff() throws Throwable {
        // arrange
        lionMockedStatic.when(() -> Lion.getBooleanValue(LionConstants.COLLABORATIVE_RECOMMEND_SWITCH, true)).thenReturn(false);
        // act
        CollaborativeResponseMessage result = recommendWrapper.getCollaborativeRecommend(request, mtCommonParam);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetCollaborativeRecommendServiceReturnNull() throws Throwable {
        // arrange
        lionMockedStatic.when(() -> Lion.getBooleanValue(LionConstants.COLLABORATIVE_RECOMMEND_SWITCH, true)).thenReturn(true);
        when(recommendService.getCollaborativeRecommend(any())).thenReturn(null);
        // act
        CollaborativeResponseMessage result = recommendWrapper.getCollaborativeRecommend(request, mtCommonParam);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetCollaborativeRecommendServiceReturnFailure() throws Throwable {
        // arrange
        lionMockedStatic.when(() -> Lion.getBooleanValue(LionConstants.COLLABORATIVE_RECOMMEND_SWITCH, true)).thenReturn(true);
        CollaborativeResponseMessage response = new CollaborativeResponseMessage();
        response.setCode(RESULT_CODE.ERROR_SERVER_INTERNAL);
        when(recommendService.getCollaborativeRecommend(any())).thenReturn(response);
        // act
        CollaborativeResponseMessage result = recommendWrapper.getCollaborativeRecommend(request, mtCommonParam);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetCollaborativeRecommendServiceReturnSuccess() throws Throwable {
        // arrange
        lionMockedStatic.when(() -> Lion.getBooleanValue(LionConstants.COLLABORATIVE_RECOMMEND_SWITCH, true)).thenReturn(true);
        CollaborativeResponseMessage response = new CollaborativeResponseMessage();
        response.setCode(RESULT_CODE.SUCCESS);
        when(recommendService.getCollaborativeRecommend(any())).thenReturn(response);
        // act
        CollaborativeResponseMessage result = recommendWrapper.getCollaborativeRecommend(request, mtCommonParam);
        // assert
        assertSame(response, result);
    }

    @Test
    public void testGetCollaborativeRecommendServiceThrowException() throws Throwable {
        // arrange
        lionMockedStatic.when(() -> Lion.getBooleanValue(LionConstants.COLLABORATIVE_RECOMMEND_SWITCH, true)).thenReturn(true);
        when(recommendService.getCollaborativeRecommend(any())).thenThrow(TException.class);
        // act
        CollaborativeResponseMessage result = recommendWrapper.getCollaborativeRecommend(request, mtCommonParam);
        // assert
        assertNull(result);
    }
}
