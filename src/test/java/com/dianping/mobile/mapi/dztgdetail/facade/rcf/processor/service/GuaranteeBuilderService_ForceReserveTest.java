package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.lion.client.Lion;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.mockStatic;

@RunWith(MockitoJUnitRunner.class)
public class GuaranteeBuilderService_ForceReserveTest {

    private GuaranteeBuilderService guaranteeBuilderService = new GuaranteeBuilderService();

    private MockedStatic<Lion> lionMockedStatic;

    @Before
    public void setUp() {
        lionMockedStatic = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        lionMockedStatic.close();
    }

    /**
     * Tests the scenario where the Lion configuration list is empty.
     */
    @Test
    public void testForceReserveWhenConfigIsEmpty() throws Throwable {
        lionMockedStatic.when(() -> Lion.getList("com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants" +
                ".FORCE_BOOKING_PUBLISH_CATEGORY", Integer.class, Collections.emptyList())).thenReturn(Collections.emptyList());
        assertFalse(guaranteeBuilderService.forceReserve(1));
    }

    /**
     * Tests the scenario where the Lion configuration list is not empty but does not contain the input categoryId.
     */
    @Test
    public void testForceReserveWhenConfigNotContainsCategoryId() throws Throwable {
        lionMockedStatic.when(() -> Lion.getList("com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants" +
                ".FORCE_BOOKING_PUBLISH_CATEGORY", Integer.class, Collections.emptyList())).thenReturn(Arrays.asList(2, 3, 4));
        assertFalse(guaranteeBuilderService.forceReserve(1));
    }
}
