package com.dianping.mobile.mapi.dztgdetail.biz.immersiveimage.impl;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.QueryCenterProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageFilterRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageFilterVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageVO;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Objects;

import static org.mockito.Mockito.mockStatic;

/**
 * @Author: <EMAIL>
 * @Date: 2024/8/11
 */
@RunWith(MockitoJUnitRunner.class)
public class GlassesImmersiveIMageServiceImplTest {

    @InjectMocks
    private GlassesImmersiveImageServiceImpl glassesImmersiveImageService;

    @Mock
    private ContentSearchWrapper contentSearchWrapper;
    @Mock
    private MapperWrapper mapperWrapper;

    private MockedStatic<QueryCenterProcessor> queryCenterProcessorMockedStatic;

    private MockedStatic<DealUtils> dealUtilsMockedStatic;


    @Before
    public void setUp() {
        queryCenterProcessorMockedStatic = mockStatic(QueryCenterProcessor.class);
        dealUtilsMockedStatic = mockStatic(DealUtils.class);
    }

    @After
    public void tearDown() {
        queryCenterProcessorMockedStatic.close();
        dealUtilsMockedStatic.close();
    }

    @Test
    public void testGetImmersiveImage() {
        GetImmersiveImageRequest request = new GetImmersiveImageRequest();
        request.setCategoryId(406);
        request.setDealGroupId(1);
        request.setStart(0);
        request.setLimit(10);
        request.setShopId(1L);
        request.setMtDealGroupId(1L);
        EnvCtx ctx = new EnvCtx();
        request.setInfoContentId(1L);
        ctx.setClientType(1);
        ImmersiveImageVO vo = new ImmersiveImageVO();
        vo.setTitle("title");
        Mockito.when(contentSearchWrapper.getImmersiveImage(Mockito.any())).thenReturn(vo);
        Mockito.when(mapperWrapper.getMtShopIdByDpShopIdLong(Mockito.anyLong())).thenReturn(1L);
        ImmersiveImageVO result = glassesImmersiveImageService.getImmersiveImage(request, ctx);
        Assert.assertTrue(result.getTitle().equals(vo.getTitle()));
    }

    @Test
    public void testImmersiveImageFilter() {
        ImmersiveImageFilterVO filterVO = glassesImmersiveImageService.getImmersiveImageFilter(new GetImmersiveImageFilterRequest());
        Assert.assertTrue(Objects.isNull(filterVO));
    }
}
