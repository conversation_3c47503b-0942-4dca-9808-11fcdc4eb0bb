package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

public class MapperProcessorTest {

    private MapperProcessor mapperProcessor;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private EnvCtx envCtx;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        mapperProcessor = new MapperProcessor();
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
    }

    /**
     * 正常场景：环境是美团环境，且美团ID大于0，返回 true
     */
    @Test
    public void testIsEnableWhenMtAndMtIdGreaterThanZero() throws Throwable {
        // arrange
        when(envCtx.isMt()).thenReturn(true);
        when(dealCtx.getMtId()).thenReturn(1);
        // act
        boolean result = mapperProcessor.isEnable(dealCtx);
        // assert
        assertTrue(result);
    }

    /**
     * 正常场景：环境不是美团环境，返回 false
     */
    @Test
    public void testIsEnableWhenNotMt() throws Throwable {
        // arrange
        when(envCtx.isMt()).thenReturn(false);
        when(dealCtx.getMtId()).thenReturn(1);
        // act
        boolean result = mapperProcessor.isEnable(dealCtx);
        // assert
        assertFalse(result);
    }

    /**
     * 正常场景：环境是美团环境，但美团ID小于等于0，返回 false
     */
    @Test
    public void testIsEnableWhenMtAndMtIdLessThanOrEqualToZero() throws Throwable {
        // arrange
        when(envCtx.isMt()).thenReturn(true);
        when(dealCtx.getMtId()).thenReturn(0);
        // act
        boolean result = mapperProcessor.isEnable(dealCtx);
        // assert
        assertFalse(result);
    }

    /**
     * 边界场景：美团ID等于0，返回 false
     */
    @Test
    public void testIsEnableWhenMtIdEqualsZero() throws Throwable {
        // arrange
        when(envCtx.isMt()).thenReturn(true);
        when(dealCtx.getMtId()).thenReturn(0);
        // act
        boolean result = mapperProcessor.isEnable(dealCtx);
        // assert
        assertFalse(result);
    }

    /**
     * 边界场景：美团ID等于1，返回 true
     */
    @Test
    public void testIsEnableWhenMtIdEqualsOne() throws Throwable {
        // arrange
        when(envCtx.isMt()).thenReturn(true);
        when(dealCtx.getMtId()).thenReturn(1);
        // act
        boolean result = mapperProcessor.isEnable(dealCtx);
        // assert
        assertTrue(result);
    }

    /**
     * 异常场景：ctx 为 null，抛出 NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testIsEnableWhenCtxIsNull() throws Throwable {
        // arrange
        DealCtx nullCtx = null;
        // act
        mapperProcessor.isEnable(nullCtx);
        // assert
        // 预期抛出 NullPointerException
    }

    /**
     * 异常场景：ctx.getEnvCtx() 为 null，抛出 NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testIsEnableWhenEnvCtxIsNull() throws Throwable {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(null);
        // act
        mapperProcessor.isEnable(dealCtx);
        // assert
        // 预期抛出 NullPointerException
    }
}
