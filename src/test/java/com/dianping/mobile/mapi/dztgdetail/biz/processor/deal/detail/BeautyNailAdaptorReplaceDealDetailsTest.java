package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.Pair;
import com.dianping.mobile.mapi.dztgdetail.helper.MtDealDetailBuilder;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class BeautyNailAdaptorReplaceDealDetailsTest {

    private BeautyNailAdaptor beautyNailAdaptor = new BeautyNailAdaptor();

    /**
     * Utility method to invoke private method using reflection.
     */
    private void invokeReplaceDealDetails(String content, List<Pair> structedDetails, boolean isMt) throws Exception {
        Method method = BeautyNailAdaptor.class.getDeclaredMethod("replaceDealDetails", String.class, List.class, boolean.class);
        method.setAccessible(true);
        method.invoke(beautyNailAdaptor, content, structedDetails, isMt);
    }

    /**
     * Test case when content is blank.
     */
    @Test
    public void testReplaceDealDetails_ContentIsBlank() throws Throwable {
        // arrange
        String content = "";
        List<Pair> structedDetails = new ArrayList<>();
        boolean isMt = false;
        // act
        invokeReplaceDealDetails(content, structedDetails, isMt);
        // assert
        assertEquals(0, structedDetails.size());
    }

    /**
     * Test case when structedDetails is null.
     */
    @Test
    public void testReplaceDealDetails_StructedDetailsIsNull() throws Throwable {
        // arrange
        String content = "Some content";
        List<Pair> structedDetails = null;
        boolean isMt = false;
        // act
        invokeReplaceDealDetails(content, structedDetails, isMt);
        // assert
        assertNull(structedDetails);
    }

    /**
     * Test case when structedDetails is empty.
     */
    @Test
    public void testReplaceDealDetails_StructedDetailsIsEmpty() throws Throwable {
        // arrange
        String content = "Some content";
        List<Pair> structedDetails = new ArrayList<>();
        boolean isMt = false;
        // act
        invokeReplaceDealDetails(content, structedDetails, isMt);
        // assert
        assertEquals(1, structedDetails.size());
        assertEquals("团购详情", structedDetails.get(0).getID());
        assertEquals(content, structedDetails.get(0).getName());
    }

    /**
     * Test case when structedDetails contains a matching Pair.
     */
    @Test
    public void testReplaceDealDetails_StructedDetailsContainsMatchingPair() throws Throwable {
        // arrange
        String content = "Some content";
        List<Pair> structedDetails = new ArrayList<>();
        Pair pair = new Pair("团购详情", "Old content", 0);
        structedDetails.add(pair);
        boolean isMt = false;
        // act
        invokeReplaceDealDetails(content, structedDetails, isMt);
        // assert
        assertEquals(1, structedDetails.size());
        assertEquals("团购详情", structedDetails.get(0).getID());
        assertEquals(content, structedDetails.get(0).getName());
    }

    /**
     * Test case when structedDetails does not contain a matching Pair.
     */
    @Test
    public void testReplaceDealDetails_StructedDetailsDoesNotContainMatchingPair() throws Throwable {
        // arrange
        String content = "Some content";
        List<Pair> structedDetails = new ArrayList<>();
        Pair pair = new Pair("Other ID", "Other content", 0);
        structedDetails.add(pair);
        boolean isMt = false;
        // act
        invokeReplaceDealDetails(content, structedDetails, isMt);
        // assert
        assertEquals(2, structedDetails.size());
        assertEquals("团购详情", structedDetails.get(0).getID());
        assertEquals(content, structedDetails.get(0).getName());
    }

    /**
     * Test case when isMt is true.
     */
    @Test
    public void testReplaceDealDetails_IsMtTrue() throws Throwable {
        // arrange
        String content = "Some content";
        List<Pair> structedDetails = new ArrayList<>();
        boolean isMt = true;
        // act
        invokeReplaceDealDetails(content, structedDetails, isMt);
        // assert
        assertEquals(1, structedDetails.size());
        assertEquals(MtDealDetailBuilder.DETAIL_KEY, structedDetails.get(0).getID());
        assertEquals(content, structedDetails.get(0).getName());
    }

    /**
     * Test case when isMt is false.
     */
    @Test
    public void testReplaceDealDetails_IsMtFalse() throws Throwable {
        // arrange
        String content = "Some content";
        List<Pair> structedDetails = new ArrayList<>();
        boolean isMt = false;
        // act
        invokeReplaceDealDetails(content, structedDetails, isMt);
        // assert
        assertEquals(1, structedDetails.size());
        assertEquals("团购详情", structedDetails.get(0).getID());
        assertEquals(content, structedDetails.get(0).getName());
    }
}
