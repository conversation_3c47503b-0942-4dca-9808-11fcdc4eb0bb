package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.sankuai.general.product.query.center.client.builder.model.PurchaseNoteBuilder;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.PurchaseNoteSceneEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class QueryCenterProcessorFillSelfDealInfoTest {

    private Method fillSelfDealInfoMethod;

    private QueryCenterProcessor processor;

    @InjectMocks
    private QueryCenterProcessor queryCenterProcessor;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Mock
    private Future future;

    @Before
    public void setUp() throws Exception {
        processor = new QueryCenterProcessor();
        fillSelfDealInfoMethod = QueryCenterProcessor.class.getDeclaredMethod("fillSelfDealInfo", DealCtx.class, DealGroupDTO.class);
        fillSelfDealInfoMethod.setAccessible(true);
    }

    /**
     * Test fillSelfDealInfo with empty attributes list
     */
    @Test
    public void testFillSelfDealInfo_EmptyAttrs() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setAttrs(new ArrayList<>());
        // act
        fillSelfDealInfoMethod.invoke(processor, ctx, dealGroupDTO);
        // assert
        assertFalse(ctx.isCleaningSelfOwnDeal());
        assertFalse(ctx.isCareFreeDeal());
    }

    /**
     * Test fillSelfDealInfo with null attributes list
     */
    @Test
    public void testFillSelfDealInfo_NullAttrs() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setAttrs(null);
        // act
        fillSelfDealInfoMethod.invoke(processor, ctx, dealGroupDTO);
        // assert
        assertFalse(ctx.isCleaningSelfOwnDeal());
        assertFalse(ctx.isCareFreeDeal());
    }

    /**
     * Test fillSelfDealInfo with cleaning self own deal attribute set to "是"
     */
    @Test
    public void testFillSelfDealInfo_CleaningSelfOwnDealYes() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("self_own_product");
        attrDTO.setValue(Arrays.asList("是"));
        dealGroupDTO.setAttrs(Arrays.asList(attrDTO));
        // act
        fillSelfDealInfoMethod.invoke(processor, ctx, dealGroupDTO);
        // assert
        assertTrue(ctx.isCleaningSelfOwnDeal());
        assertFalse(ctx.isCareFreeDeal());
    }

    /**
     * Test fillSelfDealInfo with cleaning self own deal attribute set to other value
     */
    @Test
    public void testFillSelfDealInfo_CleaningSelfOwnDealNo() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("self_own_product");
        attrDTO.setValue(Arrays.asList("否"));
        dealGroupDTO.setAttrs(Arrays.asList(attrDTO));
        // act
        fillSelfDealInfoMethod.invoke(processor, ctx, dealGroupDTO);
        // assert
        assertFalse(ctx.isCleaningSelfOwnDeal());
        assertFalse(ctx.isCareFreeDeal());
    }

    /**
     * Test fillSelfDealInfo with piping service deal group attribute but empty Lion config
     */
    @Test
    public void testFillSelfDealInfo_PipingServiceDealGroupEmptyConfig() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("standardDealGroupKey");
        attrDTO.setValue(Arrays.asList("value1"));
        dealGroupDTO.setAttrs(Arrays.asList(attrDTO));
        // act
        fillSelfDealInfoMethod.invoke(processor, ctx, dealGroupDTO);
        // assert
        assertFalse(ctx.isCareFreeDeal());
        assertFalse(ctx.isCleaningSelfOwnDeal());
    }

    /**
     * Test fillSelfDealInfo with empty attribute values
     */
    @Test
    public void testFillSelfDealInfo_EmptyAttributeValues() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("standardDealGroupKey");
        attrDTO.setValue(new ArrayList<>());
        dealGroupDTO.setAttrs(Arrays.asList(attrDTO));
        // act
        fillSelfDealInfoMethod.invoke(processor, ctx, dealGroupDTO);
        // assert
        assertFalse(ctx.isCareFreeDeal());
        assertFalse(ctx.isCleaningSelfOwnDeal());
    }

    /**
     * Test fillSelfDealInfo with null attribute values
     */
    @Test
    public void testFillSelfDealInfo_NullAttributeValues() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("standardDealGroupKey");
        attrDTO.setValue(null);
        dealGroupDTO.setAttrs(Arrays.asList(attrDTO));
        // act
        fillSelfDealInfoMethod.invoke(processor, ctx, dealGroupDTO);
        // assert
        assertFalse(ctx.isCareFreeDeal());
        assertFalse(ctx.isCleaningSelfOwnDeal());
    }

    /**
     * Test prepare method when skuId is empty
     */
    @Test
    public void testPrepareWhenSkuIdIsEmpty() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setSkuId("");
        when(queryCenterWrapper.preDealGroupDTO(any())).thenReturn(future);
        // act
        queryCenterProcessor.prepare(ctx);
        // assert
        verify(queryCenterWrapper).preDealGroupDTO(any());
    }

    /**
     * Test prepare method when skuId has value
     */
    @Test
    public void testPrepareWhenSkuIdHasValue() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setSkuId("123");
        ctx.setDpLongShopId(456L);
        ctx.setMtLongShopId(789L);
        when(queryCenterWrapper.preDealGroupDTO(any())).thenReturn(future);
        // act
        queryCenterProcessor.prepare(ctx);
        // assert
        ArgumentCaptor<QueryByDealGroupIdRequest> requestCaptor = ArgumentCaptor.forClass(QueryByDealGroupIdRequest.class);
        verify(queryCenterWrapper).preDealGroupDTO(requestCaptor.capture());
        PurchaseNoteBuilder expectedBuilder = PurchaseNoteBuilder.builder().all().scenes(Collections.singletonList(PurchaseNoteSceneEnum.PRODUCT_PURCHASE_NOTE)).dpShopId(456L).mtShopId(789L).dealId(123L);
    }
}
