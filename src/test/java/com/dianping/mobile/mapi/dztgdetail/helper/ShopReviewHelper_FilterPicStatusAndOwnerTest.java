package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.mobile.mapi.dztgdetail.common.enums.PicVideoStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.ShopReviewCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class ShopReviewHelper_FilterPicStatusAndOwnerTest {

    /**
     * 测试status为null的情况
     */
    @Test
    public void testFilterPicStatusAndOwnerStatusIsNull() {
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx(new EnvCtx());
        Long userid = 1L;
        Integer status = null;
        Boolean result = ShopReviewHelper.filterPicStatusAndOwner(shopReviewCtx, userid, status);
        assertTrue(result);
    }

    /**
     * 测试status等于NORMAL的代码的情况
     */
    @Test
    public void testFilterPicStatusAndOwnerStatusIsNormal() {
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx(new EnvCtx());
        Long userid = 1L;
        Integer status = PicVideoStatusEnum.NORMAL.code;
        Boolean result = ShopReviewHelper.filterPicStatusAndOwner(shopReviewCtx, userid, status);
        assertFalse(result);
    }

    /**
     * 测试status等于AUDIT的代码，并且userid等于shopReviewCtx中的mtUserId的情况
     */
    @Test
    public void testFilterPicStatusAndOwnerStatusIsAuditAndUseridIsMtUserId() {
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx(new EnvCtx());
        shopReviewCtx.getEnvCtx().setMtUserId(1L);
        Long userid = 1L;
        Integer status = PicVideoStatusEnum.AUDIT.code;
        Boolean result = ShopReviewHelper.filterPicStatusAndOwner(shopReviewCtx, userid, status);
        assertFalse(result);
    }

    /**
     * 测试status等于AUDIT的代码，但userid不等于shopReviewCtx中的mtUserId的情况
     */
    @Test
    public void testFilterPicStatusAndOwnerStatusIsAuditAndUseridIsNotMtUserId() {
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx(new EnvCtx());
        shopReviewCtx.getEnvCtx().setMtUserId(1L);
        Long userid = 2L;
        Integer status = PicVideoStatusEnum.AUDIT.code;
        Boolean result = ShopReviewHelper.filterPicStatusAndOwner(shopReviewCtx, userid, status);
        assertTrue(result);
    }
}
