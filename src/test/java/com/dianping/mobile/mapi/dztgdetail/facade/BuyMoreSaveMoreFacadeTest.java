package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BindingSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.entity.CombinationDealInfo;
import com.sankuai.dealuser.price.display.api.enums.ExtPriceTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.TyingSaleTypeEnum;
import com.sankuai.dealuser.price.display.api.model.ExtPriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.TyingSaleDTO;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class BuyMoreSaveMoreFacadeTest {

    private BuyMoreSaveMoreFacade buyMoreSaveMoreFacade;

    private BuyMoreSaveMoreFacade facade = new BuyMoreSaveMoreFacade();

    @Before
    public void setUp() {
        buyMoreSaveMoreFacade = new BuyMoreSaveMoreFacade();
    }

    private String invokeGetCardTitle(Map<Integer, DealGroupDTO> dealGroupDTOMap, Integer mainDealId, Integer bindingDealId) throws Exception {
        Method method = BuyMoreSaveMoreFacade.class.getDeclaredMethod("getCardTitle", Map.class, Integer.class, Integer.class);
        method.setAccessible(true);
        return (String) method.invoke(facade, dealGroupDTOMap, mainDealId, bindingDealId);
    }

    @Test
    public void testConvertToCombinationDealInfoEmptyList() {
        List<RecommendDTO> recommendDTOS = new ArrayList<>();
        List<CombinationDealInfo> result = buyMoreSaveMoreFacade.convertToCombinationDealInfo(recommendDTOS);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testConvertToCombinationDealInfoEmptyBizData() {
        List<RecommendDTO> recommendDTOS = new ArrayList<>();
        RecommendDTO recommendDTO = new RecommendDTO();
        recommendDTO.setBizData(new HashMap<>());
        recommendDTOS.add(recommendDTO);
        List<CombinationDealInfo> result = buyMoreSaveMoreFacade.convertToCombinationDealInfo(recommendDTOS);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testConvertToCombinationDealInfoInvalidBizData() {
        List<RecommendDTO> recommendDTOS = new ArrayList<>();
        RecommendDTO recommendDTO = new RecommendDTO();
        Map<String, Object> bizData = new HashMap<>();
        bizData.put("productId_a", 1);
        bizData.put("productId_b", 2);
        bizData.put("is_valid", "0");
        recommendDTO.setBizData(bizData);
        recommendDTOS.add(recommendDTO);
        List<CombinationDealInfo> result = buyMoreSaveMoreFacade.convertToCombinationDealInfo(recommendDTOS);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testConvertToCombinationDealInfoValidBizData() {
        List<RecommendDTO> recommendDTOS = new ArrayList<>();
        RecommendDTO recommendDTO = new RecommendDTO();
        Map<String, Object> bizData = new HashMap<>();
        bizData.put("productId_a", 1);
        bizData.put("productId_b", 2);
        bizData.put("is_valid", "1");
        recommendDTO.setBizData(bizData);
        recommendDTOS.add(recommendDTO);
        List<CombinationDealInfo> result = buyMoreSaveMoreFacade.convertToCombinationDealInfo(recommendDTOS);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getMainDealId());
        assertEquals(2, result.get(0).getBindingDealId());
    }

    @Test
    public void testBuildDealProductRequest() {
        EnvCtx ctx = new EnvCtx();
        ctx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealProductRequest request = buyMoreSaveMoreFacade.buildDealProductRequest(ctx, new ArrayList<>(), 1L);
        Assert.assertTrue(Objects.nonNull(request));
    }

    /**
     * 测试 dealGroupDTOMap 不包含 mainDealId 的情况
     */
    @Test
    public void testGetCardTitleMainDealIdNotInMap() throws Throwable {
        // arrange
        Map<Integer, DealGroupDTO> dealGroupDTOMap = new HashMap<>();
        Integer mainDealId = 1;
        Integer bindingDealId = 2;
        // act
        String result = invokeGetCardTitle(dealGroupDTOMap, mainDealId, bindingDealId);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试 dealGroupDTOMap 不包含 bindingDealId 的情况
     */
    @Test
    public void testGetCardTitleBindingDealIdNotInMap() throws Throwable {
        // arrange
        Map<Integer, DealGroupDTO> dealGroupDTOMap = new HashMap<>();
        Integer mainDealId = 1;
        Integer bindingDealId = 2;
        dealGroupDTOMap.put(mainDealId, mock(DealGroupDTO.class));
        // act
        String result = invokeGetCardTitle(dealGroupDTOMap, mainDealId, bindingDealId);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试 mainDealId 对应的 DealGroupDTO 的 serviceType 为空的情况
     */
    @Test
    public void testGetCardTitleMainDealServiceTypeIsBlank() throws Throwable {
        // arrange
        Map<Integer, DealGroupDTO> dealGroupDTOMap = new HashMap<>();
        Integer mainDealId = 1;
        Integer bindingDealId = 2;
        DealGroupDTO mainDealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO mainDealCategory = mock(DealGroupCategoryDTO.class);
        when(mainDealGroupDTO.getCategory()).thenReturn(mainDealCategory);
        when(mainDealCategory.getServiceType()).thenReturn("");
        dealGroupDTOMap.put(mainDealId, mainDealGroupDTO);
        dealGroupDTOMap.put(bindingDealId, mock(DealGroupDTO.class));
        // act
        String result = invokeGetCardTitle(dealGroupDTOMap, mainDealId, bindingDealId);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试 bindingDealId 对应的 DealGroupDTO 的 serviceType 为空的情况
     */
    @Test
    public void testGetCardTitleBindingDealServiceTypeIsBlank() throws Throwable {
        // arrange
        Map<Integer, DealGroupDTO> dealGroupDTOMap = new HashMap<>();
        Integer mainDealId = 1;
        Integer bindingDealId = 2;
        DealGroupDTO bindingDealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO bindingDealCategory = mock(DealGroupCategoryDTO.class);
        when(bindingDealGroupDTO.getCategory()).thenReturn(bindingDealCategory);
        when(bindingDealCategory.getServiceType()).thenReturn("");
        DealGroupDTO mainDealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO mainDealCategory = mock(DealGroupCategoryDTO.class);
        when(mainDealGroupDTO.getCategory()).thenReturn(mainDealCategory);
        when(mainDealCategory.getServiceType()).thenReturn("按摩");
        dealGroupDTOMap.put(mainDealId, mainDealGroupDTO);
        dealGroupDTOMap.put(bindingDealId, bindingDealGroupDTO);
        // act
        String result = invokeGetCardTitle(dealGroupDTOMap, mainDealId, bindingDealId);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试 mainDealServiceType 和 bindingDealServiceType 相同且为 "其他" 的情况
     */
    @Test
    public void testGetCardTitleServiceTypeSameAndIsQiTa() throws Throwable {
        // arrange
        Map<Integer, DealGroupDTO> dealGroupDTOMap = new HashMap<>();
        Integer mainDealId = 1;
        Integer bindingDealId = 2;
        DealGroupDTO mainDealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO mainDealCategory = mock(DealGroupCategoryDTO.class);
        when(mainDealGroupDTO.getCategory()).thenReturn(mainDealCategory);
        when(mainDealCategory.getServiceType()).thenReturn("其他");
        DealGroupDTO bindingDealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO bindingDealCategory = mock(DealGroupCategoryDTO.class);
        when(bindingDealGroupDTO.getCategory()).thenReturn(bindingDealCategory);
        when(bindingDealCategory.getServiceType()).thenReturn("其他");
        dealGroupDTOMap.put(mainDealId, mainDealGroupDTO);
        dealGroupDTOMap.put(bindingDealId, bindingDealGroupDTO);
        // act
        String result = invokeGetCardTitle(dealGroupDTOMap, mainDealId, bindingDealId);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试 mainDealServiceType 和 bindingDealServiceType 相同且不为 "其他" 的情况
     */
    @Test
    public void testGetCardTitleServiceTypeSameAndNotQiTa() throws Throwable {
        // arrange
        Map<Integer, DealGroupDTO> dealGroupDTOMap = new HashMap<>();
        Integer mainDealId = 1;
        Integer bindingDealId = 2;
        DealGroupDTO mainDealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO mainDealCategory = mock(DealGroupCategoryDTO.class);
        when(mainDealGroupDTO.getCategory()).thenReturn(mainDealCategory);
        when(mainDealCategory.getServiceType()).thenReturn("按摩");
        DealGroupDTO bindingDealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO bindingDealCategory = mock(DealGroupCategoryDTO.class);
        when(bindingDealGroupDTO.getCategory()).thenReturn(bindingDealCategory);
        when(bindingDealCategory.getServiceType()).thenReturn("按摩");
        dealGroupDTOMap.put(mainDealId, mainDealGroupDTO);
        dealGroupDTOMap.put(bindingDealId, bindingDealGroupDTO);
        // act
        String result = invokeGetCardTitle(dealGroupDTOMap, mainDealId, bindingDealId);
        // assert
        assertEquals("按摩套餐", result);
    }

    /**
     * 测试 mainDealServiceType 为 "其他" 的情况
     */
    @Test
    public void testGetCardTitleMainDealServiceTypeIsQiTa() throws Throwable {
        // arrange
        Map<Integer, DealGroupDTO> dealGroupDTOMap = new HashMap<>();
        Integer mainDealId = 1;
        Integer bindingDealId = 2;
        DealGroupDTO mainDealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO mainDealCategory = mock(DealGroupCategoryDTO.class);
        when(mainDealGroupDTO.getCategory()).thenReturn(mainDealCategory);
        when(mainDealCategory.getServiceType()).thenReturn("其他");
        DealGroupDTO bindingDealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO bindingDealCategory = mock(DealGroupCategoryDTO.class);
        when(bindingDealGroupDTO.getCategory()).thenReturn(bindingDealCategory);
        when(bindingDealCategory.getServiceType()).thenReturn("按摩");
        dealGroupDTOMap.put(mainDealId, mainDealGroupDTO);
        dealGroupDTOMap.put(bindingDealId, bindingDealGroupDTO);
        // act
        String result = invokeGetCardTitle(dealGroupDTOMap, mainDealId, bindingDealId);
        // assert
        assertEquals("按摩套餐", result);
    }

    /**
     * 测试 bindingDealServiceType 为 "其他" 的情况
     */
    @Test
    public void testGetCardTitleBindingDealServiceTypeIsQiTa() throws Throwable {
        // arrange
        Map<Integer, DealGroupDTO> dealGroupDTOMap = new HashMap<>();
        Integer mainDealId = 1;
        Integer bindingDealId = 2;
        DealGroupDTO mainDealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO mainDealCategory = mock(DealGroupCategoryDTO.class);
        when(mainDealGroupDTO.getCategory()).thenReturn(mainDealCategory);
        when(mainDealCategory.getServiceType()).thenReturn("按摩");
        DealGroupDTO bindingDealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO bindingDealCategory = mock(DealGroupCategoryDTO.class);
        when(bindingDealGroupDTO.getCategory()).thenReturn(bindingDealCategory);
        when(bindingDealCategory.getServiceType()).thenReturn("其他");
        dealGroupDTOMap.put(mainDealId, mainDealGroupDTO);
        dealGroupDTOMap.put(bindingDealId, bindingDealGroupDTO);
        // act
        String result = invokeGetCardTitle(dealGroupDTOMap, mainDealId, bindingDealId);
        // assert
        assertEquals("按摩套餐", result);
    }

    /**
     * 测试 mainDealServiceType 和 bindingDealServiceType 都不为 "其他" 的情况
     */
    @Test
    public void testGetCardTitleServiceTypeNotQiTa() throws Throwable {
        // arrange
        Map<Integer, DealGroupDTO> dealGroupDTOMap = new HashMap<>();
        Integer mainDealId = 1;
        Integer bindingDealId = 2;
        DealGroupDTO mainDealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO mainDealCategory = mock(DealGroupCategoryDTO.class);
        when(mainDealGroupDTO.getCategory()).thenReturn(mainDealCategory);
        when(mainDealCategory.getServiceType()).thenReturn("按摩");
        DealGroupDTO bindingDealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO bindingDealCategory = mock(DealGroupCategoryDTO.class);
        when(bindingDealGroupDTO.getCategory()).thenReturn(bindingDealCategory);
        when(bindingDealCategory.getServiceType()).thenReturn("足疗");
        dealGroupDTOMap.put(mainDealId, mainDealGroupDTO);
        dealGroupDTOMap.put(bindingDealId, bindingDealGroupDTO);
        // act
        String result = invokeGetCardTitle(dealGroupDTOMap, mainDealId, bindingDealId);
        // assert
        assertEquals("按摩+足疗", result);
    }

    /**
     * 测试返回的字符串中包含 "套餐套餐" 的情况
     */
    @Test
    public void testGetCardTitleContainsTaoCanTaoCan() throws Throwable {
        // arrange
        Map<Integer, DealGroupDTO> dealGroupDTOMap = new HashMap<>();
        Integer mainDealId = 1;
        Integer bindingDealId = 2;
        DealGroupDTO mainDealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO mainDealCategory = mock(DealGroupCategoryDTO.class);
        when(mainDealGroupDTO.getCategory()).thenReturn(mainDealCategory);
        when(mainDealCategory.getServiceType()).thenReturn("按摩");
        DealGroupDTO bindingDealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO bindingDealCategory = mock(DealGroupCategoryDTO.class);
        when(bindingDealGroupDTO.getCategory()).thenReturn(bindingDealCategory);
        when(bindingDealCategory.getServiceType()).thenReturn("按摩");
        dealGroupDTOMap.put(mainDealId, mainDealGroupDTO);
        dealGroupDTOMap.put(bindingDealId, bindingDealGroupDTO);
        // act
        String result = invokeGetCardTitle(dealGroupDTOMap, mainDealId, bindingDealId);
        // assert
        assertEquals("按摩套餐", result);
    }

    /**
     * Test empty input string
     */
    @Test
    public void testConvertToCombinationDealInfo_EmptyInput() {
        // arrange
        String json = "";
        // act
        List<CombinationDealInfo> result = facade.convertToCombinationDealInfo(json);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test JSON without sortedResult field
     */
    @Test
    public void testConvertToCombinationDealInfo_NoSortedResult() {
        // arrange
        String json = "{\"otherField\": \"value\"}";
        // act
        List<CombinationDealInfo> result = facade.convertToCombinationDealInfo(json);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when is_valid is not "1"
     */
    @Test
    public void testConvertToCombinationDealInfo_InvalidIsValid() {
        // arrange
        String json = "{\"sortedResult\":[{\"bizData\":{\"is_valid\":\"0\",\"isSameShop\":\"1\"}}]}";
        // act
        List<CombinationDealInfo> result = facade.convertToCombinationDealInfo(json);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when isSameShop is not 1
     */
    @Test
    public void testConvertToCombinationDealInfo_InvalidIsSameShop() {
        // arrange
        String json = "{\"sortedResult\":[{\"bizData\":{\"is_valid\":\"1\",\"isSameShop\":\"0\"}}]}";
        // act
        List<CombinationDealInfo> result = facade.convertToCombinationDealInfo(json);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test successful conversion with valid data
     */
    @Test
    public void testConvertToCombinationDealInfo_Success() {
        // arrange
        String json = "{\"sortedResult\":[{\"bizData\":{" + "\"is_valid\":\"1\"," + "\"isSameShop\":\"1\"," + "\"combinationId\":\"123\"," + "\"productId_a\":\"456\"," + "\"productId_b\":\"789\"" + "}}]}";
        // act
        List<CombinationDealInfo> result = facade.convertToCombinationDealInfo(json);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        CombinationDealInfo info = result.get(0);
        assertEquals("123", info.getItemId());
        assertEquals(456, info.getMainDealId());
        assertEquals(789, info.getBindingDealId());
        assertEquals(BindingSourceEnum.MANUAL.getValue(), info.getBindingSource());
    }

    /**
     * Test case for normal scenario where priceDisplayDTO is not null,
     * extPrices contains Tying_Sale, and bindingDealCombinationInfoMap contains the bindingDealId.
     */
    @Test
    public void testBuildDealCombinationInfoPrice_NormalScenario() throws Throwable {
        // arrange
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        ExtPriceDisplayDTO extPriceDisplayDTO = new ExtPriceDisplayDTO();
        extPriceDisplayDTO.setExtPriceType(ExtPriceTypeEnum.Tying_Sale.getType());
        extPriceDisplayDTO.setExtPricePromoAmount(new BigDecimal("10.00"));
        extPriceDisplayDTO.setExtPrice(new BigDecimal("100.00"));
        TyingSaleDTO tyingSaleDTO = new TyingSaleDTO();
        tyingSaleDTO.setTyingSaleType(TyingSaleTypeEnum.DEAL_GROUP.getType());
        tyingSaleDTO.setTyingSaleProductId(123);
        tyingSaleDTO.setTyingSaleSkuId(456L);
        extPriceDisplayDTO.setTyingSaleDTOS(Collections.singletonList(tyingSaleDTO));
        priceDisplayDTO.setExtPrices(Collections.singletonList(extPriceDisplayDTO));
        Map<Integer, CombinationDealInfo> bindingDealCombinationInfoMap = new HashMap<>();
        CombinationDealInfo combinationDealInfo = new CombinationDealInfo();
        bindingDealCombinationInfoMap.put(123, combinationDealInfo);
        BuyMoreSaveMoreFacade facade = new BuyMoreSaveMoreFacade();
        // act
        facade.buildDealCombinationInfoPrice(priceDisplayDTO, bindingDealCombinationInfoMap);
        // assert
        assertEquals(new BigDecimal("100.00"), combinationDealInfo.getExtPrice());
        assertEquals(new BigDecimal("10.00"), combinationDealInfo.getExtPricePromoAmount());
        assertEquals(456L, combinationDealInfo.getTyingSaleSkuId());
    }

    /**
     * Test case for boundary scenario where priceDisplayDTO is not null but extPrices is empty.
     */
    @Test
    public void testBuildDealCombinationInfoPrice_EmptyExtPrices() throws Throwable {
        // arrange
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        priceDisplayDTO.setExtPrices(Collections.emptyList());
        Map<Integer, CombinationDealInfo> bindingDealCombinationInfoMap = new HashMap<>();
        CombinationDealInfo combinationDealInfo = new CombinationDealInfo();
        bindingDealCombinationInfoMap.put(123, combinationDealInfo);
        BuyMoreSaveMoreFacade facade = new BuyMoreSaveMoreFacade();
        // act
        facade.buildDealCombinationInfoPrice(priceDisplayDTO, bindingDealCombinationInfoMap);
        // assert
        assertNull(combinationDealInfo.getExtPrice());
        assertNull(combinationDealInfo.getExtPricePromoAmount());
        assertEquals(0L, combinationDealInfo.getTyingSaleSkuId());
    }

    /**
     * Test case for boundary scenario where extPrices contains ExtPriceDisplayDTO but extPriceType is not Tying_Sale.
     */
    @Test
    public void testBuildDealCombinationInfoPrice_NonTyingSaleExtPriceType() throws Throwable {
        // arrange
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        ExtPriceDisplayDTO extPriceDisplayDTO = new ExtPriceDisplayDTO();
        extPriceDisplayDTO.setExtPriceType(ExtPriceTypeEnum.LowestPrice_In_NetWork.getType());
        priceDisplayDTO.setExtPrices(Collections.singletonList(extPriceDisplayDTO));
        Map<Integer, CombinationDealInfo> bindingDealCombinationInfoMap = new HashMap<>();
        CombinationDealInfo combinationDealInfo = new CombinationDealInfo();
        bindingDealCombinationInfoMap.put(123, combinationDealInfo);
        BuyMoreSaveMoreFacade facade = new BuyMoreSaveMoreFacade();
        // act
        facade.buildDealCombinationInfoPrice(priceDisplayDTO, bindingDealCombinationInfoMap);
        // assert
        assertNull(combinationDealInfo.getExtPrice());
        assertNull(combinationDealInfo.getExtPricePromoAmount());
        assertEquals(0L, combinationDealInfo.getTyingSaleSkuId());
    }

    /**
     * Test case for exception scenario where priceDisplayDTO is null.
     */
    @Test
    public void testBuildDealCombinationInfoPrice_NullPriceDisplayDTO() throws Throwable {
        // arrange
        PriceDisplayDTO priceDisplayDTO = null;
        Map<Integer, CombinationDealInfo> bindingDealCombinationInfoMap = new HashMap<>();
        CombinationDealInfo combinationDealInfo = new CombinationDealInfo();
        bindingDealCombinationInfoMap.put(123, combinationDealInfo);
        BuyMoreSaveMoreFacade facade = new BuyMoreSaveMoreFacade();
        // act
        facade.buildDealCombinationInfoPrice(priceDisplayDTO, bindingDealCombinationInfoMap);
        // assert
        assertNull(combinationDealInfo.getExtPrice());
        assertNull(combinationDealInfo.getExtPricePromoAmount());
        assertEquals(0L, combinationDealInfo.getTyingSaleSkuId());
    }

    /**
     * Test case for boundary scenario where bindingDealCombinationInfoMap does not contain the bindingDealId.
     */
    @Test
    public void testBuildDealCombinationInfoPrice_BindingDealIdNotInMap() throws Throwable {
        // arrange
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        ExtPriceDisplayDTO extPriceDisplayDTO = new ExtPriceDisplayDTO();
        extPriceDisplayDTO.setExtPriceType(ExtPriceTypeEnum.Tying_Sale.getType());
        extPriceDisplayDTO.setExtPricePromoAmount(new BigDecimal("10.00"));
        extPriceDisplayDTO.setExtPrice(new BigDecimal("100.00"));
        TyingSaleDTO tyingSaleDTO = new TyingSaleDTO();
        tyingSaleDTO.setTyingSaleType(TyingSaleTypeEnum.DEAL_GROUP.getType());
        tyingSaleDTO.setTyingSaleProductId(123);
        tyingSaleDTO.setTyingSaleSkuId(456L);
        extPriceDisplayDTO.setTyingSaleDTOS(Collections.singletonList(tyingSaleDTO));
        priceDisplayDTO.setExtPrices(Collections.singletonList(extPriceDisplayDTO));
        Map<Integer, CombinationDealInfo> bindingDealCombinationInfoMap = new HashMap<>();
        CombinationDealInfo combinationDealInfo = new CombinationDealInfo();
        // Different bindingDealId
        bindingDealCombinationInfoMap.put(456, combinationDealInfo);
        BuyMoreSaveMoreFacade facade = new BuyMoreSaveMoreFacade();
        // act
        facade.buildDealCombinationInfoPrice(priceDisplayDTO, bindingDealCombinationInfoMap);
        // assert
        assertNull(combinationDealInfo.getExtPrice());
        assertNull(combinationDealInfo.getExtPricePromoAmount());
        assertEquals(0L, combinationDealInfo.getTyingSaleSkuId());
    }
}
