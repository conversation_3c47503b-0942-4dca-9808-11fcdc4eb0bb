package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.geo.map.entity.GeoPoint;
import com.dianping.mobile.mapi.dztgdetail.biz.service.GeoBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiClientWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.PhoneNumberHelper;
import com.dianping.mobile.mapi.dztgdetail.util.GEOUtils;
import com.dianping.tuangou.dztg.bjwrapper.api.dto.MtPoiDto;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDisplayShopDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import java.util.*;
import java.util.concurrent.Future;
import org.apache.commons.collections.CollectionUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperCacheWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiShopCategoryWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.QueryParams;
import com.google.common.util.concurrent.SettableFuture;
import java.util.Arrays;
import java.util.List;
import org.apache.curator.shaded.com.google.common.collect.Lists;

/**
 * Unit tests for SinaiProcessor.fillBestShopInfo
 */
@RunWith(MockitoJUnitRunner.class)
public class SinaiProcessorFillBestShopInfoTest {

    @InjectMocks
    private SinaiProcessor sinaiProcessor;

    @Mock
    private PoiClientWrapper poiClientWrapper;

    @Mock
    private GeoBiz geoBiz;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    private DealCtx ctx;

    private FutureCtx futureCtx;

    // Helper to create a minimal DealCtx with BestShopDTO
    private DealCtx createCtxWithBestShop() {
        DealCtx ctx = new DealCtx(new com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx());
        ctx.setBestShopResp(new BestShopDTO());
        ctx.setFutureCtx(new FutureCtx());
        return ctx;
    }

    /**
     * dpPoiDTO is null, should return immediately, no changes to ctx.bestShopResp
     */
    @Test
    public void testFillBestShopInfo_DpPoiDTONull() throws Throwable {
        DealCtx ctx = createCtxWithBestShop();
        BestShopDTO before = ctx.getBestShopResp();
        // act
        // Use reflection to call private method
        java.lang.reflect.Method m = SinaiProcessor.class.getDeclaredMethod("fillBestShopInfo", DpPoiDTO.class, DealCtx.class);
        m.setAccessible(true);
        m.invoke(sinaiProcessor, (DpPoiDTO) null, ctx);
        // assert
        // No changes expected
        assertSame(before, ctx.getBestShopResp());
    }

    /**
     * ctx.getBestShopResp() is null, should return immediately, nothing happens
     */
    @Test
    public void testFillBestShopInfo_BestShopRespNull() throws Throwable {
        DealCtx ctx = new DealCtx(new com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx());
        ctx.setBestShopResp(null);
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        // act
        java.lang.reflect.Method m = SinaiProcessor.class.getDeclaredMethod("fillBestShopInfo", DpPoiDTO.class, DealCtx.class);
        m.setAccessible(true);
        m.invoke(sinaiProcessor, dpPoiDTO, ctx);
        // assert
        // No exception, nothing to assert, just ensure no NPE
        assertNull(ctx.getBestShopResp());
    }

    /**
     * Normal case: all fields set, ctx.isMt() == true, displayShopInfo present, mtDisplayShopIds present, mtPoiDtos present, dealShopQtyBySearch > 0, valid user lat/lng, geoBiz returns point
     */
    @Test
    public void testFillBestShopInfo_MtBranch_AllFields() throws Throwable {
        // arrange
        DealCtx ctx = createCtxWithBestShop();
        ctx.setUserlat(30.0);
        ctx.setUserlng(120.0);
        ctx.setMtId(123);
        ctx.setDpId(456);
        // isMt() returns true
        DealCtx spyCtx = spy(ctx);
        doReturn(true).when(spyCtx).isMt();
        // dpPoiDTO with all fields
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setShopName("TestShop");
        dpPoiDTO.setShopType(2);
        dpPoiDTO.setBranchName("BranchA");
        List<com.sankuai.sinai.data.api.dto.NormPhone> normPhones = new ArrayList<>();
        com.sankuai.sinai.data.api.dto.NormPhone phone = new com.sankuai.sinai.data.api.dto.NormPhone();
        phone.setEntity("12345678");
        phone.setAreaCode("021");
        phone.setBranch("001");
        normPhones.add(phone);
        dpPoiDTO.setNormPhones(normPhones);
        dpPoiDTO.setShopPower(5);
        dpPoiDTO.setAddress("Addr");
        dpPoiDTO.setCrossRoad("Cross");
        dpPoiDTO.setLat(31.0);
        dpPoiDTO.setLng(121.0);
        dpPoiDTO.setDefaultPic("pic@origin");
        // displayShopInfo
        DealGroupDisplayShopDTO displayShopInfo = new DealGroupDisplayShopDTO();
        List<Long> mtDisplayShopIds = Arrays.asList(1L, 2L, 3L);
        displayShopInfo.setMtDisplayShopIds(mtDisplayShopIds);
        // dealGroupDTO
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getDisplayShopInfo()).thenReturn(displayShopInfo);
        spyCtx.setDealGroupDTO(dealGroupDTO);
        // futureCtx
        FutureCtx futureCtx = mock(FutureCtx.class);
        Future mtShowTypeDTOFuture = mock(Future.class);
        when(futureCtx.getMtShowTypeDTOFuture()).thenReturn(mtShowTypeDTOFuture);
        spyCtx.setFutureCtx(futureCtx);
        // poiClientWrapper.getFutureResult returns mtPoiDtos
        List<MtPoiDto> mtPoiDtos = new ArrayList<>();
        MtPoiDto mtPoiDto = new MtPoiDto();
        mtPoiDto.setShowType("mtType");
        mtPoiDtos.add(mtPoiDto);
        when(poiClientWrapper.<List<MtPoiDto>>getFutureResult(eq(mtShowTypeDTOFuture))).thenReturn(mtPoiDtos);
        // dealGroupWrapper.getDealShopQtyBySearch returns 10
        when(dealGroupWrapper.getDealShopQtyBySearch(any(), eq(123L))).thenReturn(10L);
        // LocationHelper.isValidPoint: userlat/userlng are valid, so distanceStr will be called
        // GEOUtils.distanceStr: static, so real method will be called
        // geoBiz.googleToGps returns a GeoPoint
        GeoPoint geoPoint = new GeoPoint(32.0, 122.0);
        when(geoBiz.googleToGps(any())).thenReturn(geoPoint);
        // act
        java.lang.reflect.Method m = SinaiProcessor.class.getDeclaredMethod("fillBestShopInfo", DpPoiDTO.class, DealCtx.class);
        m.setAccessible(true);
        m.invoke(sinaiProcessor, dpPoiDTO, spyCtx);
        // assert
        BestShopDTO bestShop = spyCtx.getBestShopResp();
        assertEquals("TestShop", bestShop.getShopName());
        assertEquals(2, bestShop.getShopType());
        assertEquals("BranchA", bestShop.getBranchName());
        assertEquals(Collections.singletonList("021-12345678-001"), bestShop.getPhoneNos());
        assertEquals(5, bestShop.getShopPower());
        assertEquals("Addr(Cross)", bestShop.getAddress());
        assertEquals(31.0, bestShop.getGlat(), 0.0001);
        assertEquals(121.0, bestShop.getGlng(), 0.0001);
        // totalShopsNum set by dealShopQtyBySearch
        assertEquals(10, bestShop.getTotalShopsNum());
        // showType set by mtPoiDtos
        assertEquals("mtType", bestShop.getShowType());
        // distance set
        assertNotNull(bestShop.getDistance());
        // lat/lng set by geoBiz
        assertEquals(32.0, bestShop.getLat(), 0.0001);
        assertEquals(122.0, bestShop.getLng(), 0.0001);
        // shopPic set by getOriginPic
        assertEquals("pic@origin", bestShop.getShopPic());
    }

    /**
     * Normal case: isMt() == false, displayShopInfo present, dpDisplayShopIds present, dealShopQtyBySearch = 0, no mtPoiDtos, userlat/lng invalid, geoBiz returns null
     */
    @Test
    public void testFillBestShopInfo_DpBranch_NoQty_NoMtPoiDto_InvalidUserLoc() throws Throwable {
        // arrange
        DealCtx ctx = createCtxWithBestShop();
        ctx.setUserlat(0.0);
        ctx.setUserlng(0.0);
        ctx.setMtId(0);
        ctx.setDpId(789);
        // isMt() returns false
        DealCtx spyCtx = spy(ctx);
        doReturn(false).when(spyCtx).isMt();
        // dpPoiDTO with some fields
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setShopName("Shop2");
        // should default to 0
        dpPoiDTO.setShopType(null);
        dpPoiDTO.setBranchName("BranchB");
        // should default to empty list
        dpPoiDTO.setNormPhones(null);
        // should default to 0
        dpPoiDTO.setShopPower(null);
        dpPoiDTO.setAddress("Addr2");
        // should not append
        dpPoiDTO.setCrossRoad("");
        // should default to 0
        dpPoiDTO.setLat(null);
        // should default to 0
        dpPoiDTO.setLng(null);
        dpPoiDTO.setDefaultPic("pic2@origin");
        // displayShopInfo
        DealGroupDisplayShopDTO displayShopInfo = new DealGroupDisplayShopDTO();
        List<Long> dpDisplayShopIds = Arrays.asList(10L, 20L);
        displayShopInfo.setDpDisplayShopIds(dpDisplayShopIds);
        // dealGroupDTO
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getDisplayShopInfo()).thenReturn(displayShopInfo);
        spyCtx.setDealGroupDTO(dealGroupDTO);
        // futureCtx
        FutureCtx futureCtx = mock(FutureCtx.class);
        Future mtShowTypeDTOFuture = mock(Future.class);
        spyCtx.setFutureCtx(futureCtx);
        // poiClientWrapper.getFutureResult returns null (no mtPoiDtos)
        // dealGroupWrapper.getDealShopQtyBySearch returns 0
        when(dealGroupWrapper.getDealShopQtyBySearch(any(), eq(789L))).thenReturn(0L);
        // geoBiz.googleToGps returns null
        when(geoBiz.googleToGps(any())).thenReturn(null);
        // act
        java.lang.reflect.Method m = SinaiProcessor.class.getDeclaredMethod("fillBestShopInfo", DpPoiDTO.class, DealCtx.class);
        m.setAccessible(true);
        m.invoke(sinaiProcessor, dpPoiDTO, spyCtx);
        // assert
        BestShopDTO bestShop = spyCtx.getBestShopResp();
        assertEquals("Shop2", bestShop.getShopName());
        assertEquals(0, bestShop.getShopType());
        assertEquals("BranchB", bestShop.getBranchName());
        assertEquals(Collections.emptyList(), bestShop.getPhoneNos());
        assertEquals(0, bestShop.getShopPower());
        assertEquals("Addr2", bestShop.getAddress());
        assertEquals(0.0, bestShop.getGlat(), 0.0001);
        assertEquals(0.0, bestShop.getGlng(), 0.0001);
        // totalShopsNum set by dpDisplayShopIds.size()
        assertEquals(2, bestShop.getTotalShopsNum());
        // showType not set (no mtPoiDtos)
        assertNull(bestShop.getShowType());
        // distance not set (invalid userlat/lng)
        assertNull(bestShop.getDistance());
        // lat/lng not set (geoBiz returns null)
        assertEquals(0.0, bestShop.getLat(), 0.0001);
        assertEquals(0.0, bestShop.getLng(), 0.0001);
        // shopPic set by getOriginPic
        assertEquals("pic2@origin", bestShop.getShopPic());
    }

    /**
     * displayShopInfo is null, so no totalShopsNum set from displayShopInfo
     */
    @Test
    public void testFillBestShopInfo_DisplayShopInfoNull() throws Throwable {
        // arrange
        DealCtx ctx = createCtxWithBestShop();
        ctx.setUserlat(0.0);
        ctx.setUserlng(0.0);
        ctx.setMtId(0);
        ctx.setDpId(100);
        // isMt() returns false
        DealCtx spyCtx = spy(ctx);
        doReturn(false).when(spyCtx).isMt();
        // dpPoiDTO
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setShopName("Shop3");
        dpPoiDTO.setShopType(1);
        dpPoiDTO.setBranchName("BranchC");
        dpPoiDTO.setNormPhones(new ArrayList<>());
        dpPoiDTO.setShopPower(3);
        dpPoiDTO.setAddress("Addr3");
        dpPoiDTO.setCrossRoad(null);
        dpPoiDTO.setLat(0.0);
        dpPoiDTO.setLng(0.0);
        dpPoiDTO.setDefaultPic("pic3@origin");
        // dealGroupDTO with null displayShopInfo
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getDisplayShopInfo()).thenReturn(null);
        spyCtx.setDealGroupDTO(dealGroupDTO);
        // futureCtx
        FutureCtx futureCtx = mock(FutureCtx.class);
        Future mtShowTypeDTOFuture = mock(Future.class);
        Future dealShopQtyBySearchFuture = mock(Future.class);
        when(futureCtx.getDealShopQtyBySearchFuture()).thenReturn(dealShopQtyBySearchFuture);
        spyCtx.setFutureCtx(futureCtx);
        // dealGroupWrapper.getDealShopQtyBySearch returns 0
        when(dealGroupWrapper.getDealShopQtyBySearch(eq(dealShopQtyBySearchFuture), eq(100L))).thenReturn(0L);
        // geoBiz.googleToGps returns null
        when(geoBiz.googleToGps(any())).thenReturn(null);
        // act
        java.lang.reflect.Method m = SinaiProcessor.class.getDeclaredMethod("fillBestShopInfo", DpPoiDTO.class, DealCtx.class);
        m.setAccessible(true);
        m.invoke(sinaiProcessor, dpPoiDTO, spyCtx);
        // assert
        BestShopDTO bestShop = spyCtx.getBestShopResp();
        assertEquals("Shop3", bestShop.getShopName());
        assertEquals(1, bestShop.getShopType());
        assertEquals("BranchC", bestShop.getBranchName());
        assertEquals(Collections.emptyList(), bestShop.getPhoneNos());
        assertEquals(3, bestShop.getShopPower());
        assertEquals("Addr3", bestShop.getAddress());
        assertEquals(0.0, bestShop.getGlat(), 0.0001);
        assertEquals(0.0, bestShop.getGlng(), 0.0001);
        // totalShopsNum not set (remains default 0)
        assertEquals(0, bestShop.getTotalShopsNum());
        // shopPic set by getOriginPic
        assertEquals("pic3@origin", bestShop.getShopPic());
    }

    /**
     * displayShopInfo present, but mtDisplayShopIds/dpDisplayShopIds is empty, so totalShopsNum not set from there
     */
    @Test
    public void testFillBestShopInfo_DisplayShopIdsEmpty() throws Throwable {
        // arrange
        DealCtx ctx = createCtxWithBestShop();
        ctx.setUserlat(0.0);
        ctx.setUserlng(0.0);
        ctx.setMtId(0);
        ctx.setDpId(200);
        // isMt() returns true
        DealCtx spyCtx = spy(ctx);
        doReturn(true).when(spyCtx).isMt();
        // dpPoiDTO
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setShopName("Shop4");
        dpPoiDTO.setShopType(1);
        dpPoiDTO.setBranchName("BranchD");
        dpPoiDTO.setNormPhones(new ArrayList<>());
        dpPoiDTO.setShopPower(2);
        dpPoiDTO.setAddress("Addr4");
        dpPoiDTO.setCrossRoad(null);
        dpPoiDTO.setLat(0.0);
        dpPoiDTO.setLng(0.0);
        dpPoiDTO.setDefaultPic("pic4@origin");
        // displayShopInfo with empty mtDisplayShopIds
        DealGroupDisplayShopDTO displayShopInfo = new DealGroupDisplayShopDTO();
        displayShopInfo.setMtDisplayShopIds(new ArrayList<>());
        // dealGroupDTO
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getDisplayShopInfo()).thenReturn(displayShopInfo);
        spyCtx.setDealGroupDTO(dealGroupDTO);
        // futureCtx
        FutureCtx futureCtx = mock(FutureCtx.class);
        Future mtShowTypeDTOFuture = mock(Future.class);
        when(futureCtx.getMtShowTypeDTOFuture()).thenReturn(mtShowTypeDTOFuture);
        Future dealShopQtyBySearchFuture = mock(Future.class);
        when(futureCtx.getDealShopQtyBySearchFuture()).thenReturn(dealShopQtyBySearchFuture);
        spyCtx.setFutureCtx(futureCtx);
        // dealGroupWrapper.getDealShopQtyBySearch returns 0
        when(dealGroupWrapper.getDealShopQtyBySearch(eq(dealShopQtyBySearchFuture), eq(0L))).thenReturn(0L);
        // geoBiz.googleToGps returns null
        when(geoBiz.googleToGps(any())).thenReturn(null);
        // act
        java.lang.reflect.Method m = SinaiProcessor.class.getDeclaredMethod("fillBestShopInfo", DpPoiDTO.class, DealCtx.class);
        m.setAccessible(true);
        m.invoke(sinaiProcessor, dpPoiDTO, spyCtx);
        // assert
        BestShopDTO bestShop = spyCtx.getBestShopResp();
        assertEquals("Shop4", bestShop.getShopName());
        assertEquals(1, bestShop.getShopType());
        assertEquals("BranchD", bestShop.getBranchName());
        assertEquals(Collections.emptyList(), bestShop.getPhoneNos());
        assertEquals(2, bestShop.getShopPower());
        assertEquals("Addr4", bestShop.getAddress());
        assertEquals(0.0, bestShop.getGlat(), 0.0001);
        assertEquals(0.0, bestShop.getGlng(), 0.0001);
        // totalShopsNum not set (remains default 0)
        assertEquals(0, bestShop.getTotalShopsNum());
        // shopPic set by getOriginPic
        assertEquals("pic4@origin", bestShop.getShopPic());
    }

    /**
     * getOriginPic: origin contains "%40", should transform
     */
    @Test
    public void testFillBestShopInfo_OriginPicTransform() throws Throwable {
        // arrange
        DealCtx ctx = createCtxWithBestShop();
        ctx.setUserlat(0.0);
        ctx.setUserlng(0.0);
        ctx.setMtId(0);
        ctx.setDpId(300);
        // isMt() returns false
        DealCtx spyCtx = spy(ctx);
        doReturn(false).when(spyCtx).isMt();
        // dpPoiDTO with defaultPic containing "%40"
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setShopName("Shop5");
        dpPoiDTO.setShopType(1);
        dpPoiDTO.setBranchName("BranchE");
        dpPoiDTO.setNormPhones(new ArrayList<>());
        dpPoiDTO.setShopPower(1);
        dpPoiDTO.setAddress("Addr5");
        dpPoiDTO.setCrossRoad(null);
        dpPoiDTO.setLat(0.0);
        dpPoiDTO.setLng(0.0);
        dpPoiDTO.setDefaultPic("pic5%40origin");
        // displayShopInfo with empty dpDisplayShopIds
        DealGroupDisplayShopDTO displayShopInfo = new DealGroupDisplayShopDTO();
        displayShopInfo.setDpDisplayShopIds(new ArrayList<>());
        // dealGroupDTO
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getDisplayShopInfo()).thenReturn(displayShopInfo);
        spyCtx.setDealGroupDTO(dealGroupDTO);
        // futureCtx
        FutureCtx futureCtx = mock(FutureCtx.class);
        Future mtShowTypeDTOFuture = mock(Future.class);
        Future dealShopQtyBySearchFuture = mock(Future.class);
        when(futureCtx.getDealShopQtyBySearchFuture()).thenReturn(dealShopQtyBySearchFuture);
        spyCtx.setFutureCtx(futureCtx);
        // dealGroupWrapper.getDealShopQtyBySearch returns 0
        when(dealGroupWrapper.getDealShopQtyBySearch(eq(dealShopQtyBySearchFuture), eq(300L))).thenReturn(0L);
        // geoBiz.googleToGps returns null
        when(geoBiz.googleToGps(any())).thenReturn(null);
        // act
        java.lang.reflect.Method m = SinaiProcessor.class.getDeclaredMethod("fillBestShopInfo", DpPoiDTO.class, DealCtx.class);
        m.setAccessible(true);
        m.invoke(sinaiProcessor, dpPoiDTO, spyCtx);
        // assert
        BestShopDTO bestShop = spyCtx.getBestShopResp();
        // shopPic should be transformed
        assertEquals("pic5%40300w_0e_1l", bestShop.getShopPic());
    }

    /**
     * mtPoiDtos is not empty but first element is null, so showType not set
     */
    @Test
    public void testFillBestShopInfo_MtPoiDtosFirstNull() throws Throwable {
        // arrange
        DealCtx ctx = createCtxWithBestShop();
        ctx.setUserlat(0.0);
        ctx.setUserlng(0.0);
        ctx.setMtId(400);
        ctx.setDpId(0);
        // isMt() returns true
        DealCtx spyCtx = spy(ctx);
        doReturn(true).when(spyCtx).isMt();
        // dpPoiDTO
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setShopName("Shop6");
        dpPoiDTO.setShopType(1);
        dpPoiDTO.setBranchName("BranchF");
        dpPoiDTO.setNormPhones(new ArrayList<>());
        dpPoiDTO.setShopPower(1);
        dpPoiDTO.setAddress("Addr6");
        dpPoiDTO.setCrossRoad(null);
        dpPoiDTO.setLat(0.0);
        dpPoiDTO.setLng(0.0);
        dpPoiDTO.setDefaultPic("pic6@origin");
        // displayShopInfo with mtDisplayShopIds
        DealGroupDisplayShopDTO displayShopInfo = new DealGroupDisplayShopDTO();
        displayShopInfo.setMtDisplayShopIds(Arrays.asList(1L));
        // dealGroupDTO
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getDisplayShopInfo()).thenReturn(displayShopInfo);
        spyCtx.setDealGroupDTO(dealGroupDTO);
        // futureCtx
        FutureCtx futureCtx = mock(FutureCtx.class);
        Future mtShowTypeDTOFuture = mock(Future.class);
        when(futureCtx.getMtShowTypeDTOFuture()).thenReturn(mtShowTypeDTOFuture);
        Future dealShopQtyBySearchFuture = mock(Future.class);
        when(futureCtx.getDealShopQtyBySearchFuture()).thenReturn(dealShopQtyBySearchFuture);
        spyCtx.setFutureCtx(futureCtx);
        // poiClientWrapper.getFutureResult returns list with first element null
        List<MtPoiDto> mtPoiDtos = new ArrayList<>();
        mtPoiDtos.add(null);
        when(poiClientWrapper.<List<MtPoiDto>>getFutureResult(eq(mtShowTypeDTOFuture))).thenReturn(mtPoiDtos);
        // dealGroupWrapper.getDealShopQtyBySearch returns 0
        when(dealGroupWrapper.getDealShopQtyBySearch(eq(dealShopQtyBySearchFuture), eq(400L))).thenReturn(0L);
        // geoBiz.googleToGps returns null
        when(geoBiz.googleToGps(any())).thenReturn(null);
        // act
        java.lang.reflect.Method m = SinaiProcessor.class.getDeclaredMethod("fillBestShopInfo", DpPoiDTO.class, DealCtx.class);
        m.setAccessible(true);
        m.invoke(sinaiProcessor, dpPoiDTO, spyCtx);
        // assert
        BestShopDTO bestShop = spyCtx.getBestShopResp();
        // showType should not be set
        assertNull(bestShop.getShowType());
    }

    /**
     * dealGroupDTO is null, so displayShopInfo is null, no totalShopsNum set from displayShopInfo
     */
    @Test
    public void testFillBestShopInfo_DealGroupDTONull() throws Throwable {
        // arrange
        DealCtx ctx = createCtxWithBestShop();
        ctx.setUserlat(0.0);
        ctx.setUserlng(0.0);
        ctx.setMtId(0);
        ctx.setDpId(500);
        // isMt() returns false
        DealCtx spyCtx = spy(ctx);
        doReturn(false).when(spyCtx).isMt();
        // dpPoiDTO
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setShopName("Shop7");
        dpPoiDTO.setShopType(1);
        dpPoiDTO.setBranchName("BranchG");
        dpPoiDTO.setNormPhones(new ArrayList<>());
        dpPoiDTO.setShopPower(1);
        dpPoiDTO.setAddress("Addr7");
        dpPoiDTO.setCrossRoad(null);
        dpPoiDTO.setLat(0.0);
        dpPoiDTO.setLng(0.0);
        dpPoiDTO.setDefaultPic("pic7@origin");
        // dealGroupDTO is null
        spyCtx.setDealGroupDTO(null);
        // futureCtx
        FutureCtx futureCtx = mock(FutureCtx.class);
        Future mtShowTypeDTOFuture = mock(Future.class);
        Future dealShopQtyBySearchFuture = mock(Future.class);
        when(futureCtx.getDealShopQtyBySearchFuture()).thenReturn(dealShopQtyBySearchFuture);
        spyCtx.setFutureCtx(futureCtx);
        // dealGroupWrapper.getDealShopQtyBySearch returns 0
        when(dealGroupWrapper.getDealShopQtyBySearch(eq(dealShopQtyBySearchFuture), eq(500L))).thenReturn(0L);
        // geoBiz.googleToGps returns null
        when(geoBiz.googleToGps(any())).thenReturn(null);
        // act
        java.lang.reflect.Method m = SinaiProcessor.class.getDeclaredMethod("fillBestShopInfo", DpPoiDTO.class, DealCtx.class);
        m.setAccessible(true);
        m.invoke(sinaiProcessor, dpPoiDTO, spyCtx);
        // assert
        BestShopDTO bestShop = spyCtx.getBestShopResp();
        // totalShopsNum not set (remains default 0)
        assertEquals(0, bestShop.getTotalShopsNum());
    }

    /**
     * dealShopQtyBySearch returns negative, should not set totalShopsNum
     */
    @Test
    public void testFillBestShopInfo_DealShopQtyBySearchNegative() throws Throwable {
        // arrange
        DealCtx ctx = createCtxWithBestShop();
        ctx.setUserlat(0.0);
        ctx.setUserlng(0.0);
        ctx.setMtId(600);
        ctx.setDpId(0);
        // isMt() returns true
        DealCtx spyCtx = spy(ctx);
        doReturn(true).when(spyCtx).isMt();
        // dpPoiDTO
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setShopName("Shop8");
        dpPoiDTO.setShopType(1);
        dpPoiDTO.setBranchName("BranchH");
        dpPoiDTO.setNormPhones(new ArrayList<>());
        dpPoiDTO.setShopPower(1);
        dpPoiDTO.setAddress("Addr8");
        dpPoiDTO.setCrossRoad(null);
        dpPoiDTO.setLat(0.0);
        dpPoiDTO.setLng(0.0);
        dpPoiDTO.setDefaultPic("pic8@origin");
        // displayShopInfo with mtDisplayShopIds
        DealGroupDisplayShopDTO displayShopInfo = new DealGroupDisplayShopDTO();
        displayShopInfo.setMtDisplayShopIds(Arrays.asList(1L, 2L));
        // dealGroupDTO
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getDisplayShopInfo()).thenReturn(displayShopInfo);
        spyCtx.setDealGroupDTO(dealGroupDTO);
        // futureCtx
        FutureCtx futureCtx = mock(FutureCtx.class);
        Future mtShowTypeDTOFuture = mock(Future.class);
        when(futureCtx.getMtShowTypeDTOFuture()).thenReturn(mtShowTypeDTOFuture);
        Future dealShopQtyBySearchFuture = mock(Future.class);
        when(futureCtx.getDealShopQtyBySearchFuture()).thenReturn(dealShopQtyBySearchFuture);
        spyCtx.setFutureCtx(futureCtx);
        // dealGroupWrapper.getDealShopQtyBySearch returns -1
        when(dealGroupWrapper.getDealShopQtyBySearch(eq(dealShopQtyBySearchFuture), eq(600L))).thenReturn(-1L);
        // geoBiz.googleToGps returns null
        when(geoBiz.googleToGps(any())).thenReturn(null);
        // act
        java.lang.reflect.Method m = SinaiProcessor.class.getDeclaredMethod("fillBestShopInfo", DpPoiDTO.class, DealCtx.class);
        m.setAccessible(true);
        m.invoke(sinaiProcessor, dpPoiDTO, spyCtx);
        // assert
        BestShopDTO bestShop = spyCtx.getBestShopResp();
        // totalShopsNum should be set by mtDisplayShopIds.size()
        assertEquals(2, bestShop.getTotalShopsNum());
    }

    /**
     * getOriginPic: origin is blank, should return empty string
     */
    @Test
    public void testFillBestShopInfo_OriginPicBlank() throws Throwable {
        // arrange
        DealCtx ctx = createCtxWithBestShop();
        ctx.setUserlat(0.0);
        ctx.setUserlng(0.0);
        ctx.setMtId(0);
        ctx.setDpId(700);
        // isMt() returns false
        DealCtx spyCtx = spy(ctx);
        doReturn(false).when(spyCtx).isMt();
        // dpPoiDTO with blank defaultPic
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setShopName("Shop9");
        dpPoiDTO.setShopType(1);
        dpPoiDTO.setBranchName("BranchI");
        dpPoiDTO.setNormPhones(new ArrayList<>());
        dpPoiDTO.setShopPower(1);
        dpPoiDTO.setAddress("Addr9");
        dpPoiDTO.setCrossRoad(null);
        dpPoiDTO.setLat(0.0);
        dpPoiDTO.setLng(0.0);
        dpPoiDTO.setDefaultPic("");
        // displayShopInfo with empty dpDisplayShopIds
        DealGroupDisplayShopDTO displayShopInfo = new DealGroupDisplayShopDTO();
        displayShopInfo.setDpDisplayShopIds(new ArrayList<>());
        // dealGroupDTO
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getDisplayShopInfo()).thenReturn(displayShopInfo);
        spyCtx.setDealGroupDTO(dealGroupDTO);
        // futureCtx
        FutureCtx futureCtx = mock(FutureCtx.class);
        Future mtShowTypeDTOFuture = mock(Future.class);
        Future dealShopQtyBySearchFuture = mock(Future.class);
        when(futureCtx.getDealShopQtyBySearchFuture()).thenReturn(dealShopQtyBySearchFuture);
        spyCtx.setFutureCtx(futureCtx);
        // dealGroupWrapper.getDealShopQtyBySearch returns 0
        when(dealGroupWrapper.getDealShopQtyBySearch(eq(dealShopQtyBySearchFuture), eq(700L))).thenReturn(0L);
        // geoBiz.googleToGps returns null
        when(geoBiz.googleToGps(any())).thenReturn(null);
        // act
        java.lang.reflect.Method m = SinaiProcessor.class.getDeclaredMethod("fillBestShopInfo", DpPoiDTO.class, DealCtx.class);
        m.setAccessible(true);
        m.invoke(sinaiProcessor, dpPoiDTO, spyCtx);
        // assert
        BestShopDTO bestShop = spyCtx.getBestShopResp();
        // shopPic should be empty string
        assertEquals("", bestShop.getShopPic());
    }

    /**
     * getOriginPic: origin contains "%40" but split result has less than 2 parts, should return original
     */
    @Test
    public void testFillBestShopInfo_OriginPicTransformInvalidSplit() throws Throwable {
        // arrange
        DealCtx ctx = createCtxWithBestShop();
        ctx.setUserlat(0.0);
        ctx.setUserlng(0.0);
        ctx.setMtId(0);
        ctx.setDpId(800);
        // isMt() returns false
        DealCtx spyCtx = spy(ctx);
        doReturn(false).when(spyCtx).isMt();
        // dpPoiDTO with defaultPic containing "%40" but invalid split
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setShopName("Shop10");
        dpPoiDTO.setShopType(1);
        dpPoiDTO.setBranchName("BranchJ");
        dpPoiDTO.setNormPhones(new ArrayList<>());
        dpPoiDTO.setShopPower(1);
        dpPoiDTO.setAddress("Addr10");
        dpPoiDTO.setCrossRoad(null);
        dpPoiDTO.setLat(0.0);
        dpPoiDTO.setLng(0.0);
        dpPoiDTO.setDefaultPic("pic10%40");
        // displayShopInfo with empty dpDisplayShopIds
        DealGroupDisplayShopDTO displayShopInfo = new DealGroupDisplayShopDTO();
        displayShopInfo.setDpDisplayShopIds(new ArrayList<>());
        // dealGroupDTO
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getDisplayShopInfo()).thenReturn(displayShopInfo);
        spyCtx.setDealGroupDTO(dealGroupDTO);
        // futureCtx
        FutureCtx futureCtx = mock(FutureCtx.class);
        Future mtShowTypeDTOFuture = mock(Future.class);
        Future dealShopQtyBySearchFuture = mock(Future.class);
        when(futureCtx.getDealShopQtyBySearchFuture()).thenReturn(dealShopQtyBySearchFuture);
        spyCtx.setFutureCtx(futureCtx);
        // dealGroupWrapper.getDealShopQtyBySearch returns 0
        when(dealGroupWrapper.getDealShopQtyBySearch(eq(dealShopQtyBySearchFuture), eq(800L))).thenReturn(0L);
        // geoBiz.googleToGps returns null
        when(geoBiz.googleToGps(any())).thenReturn(null);
        // act
        java.lang.reflect.Method m = SinaiProcessor.class.getDeclaredMethod("fillBestShopInfo", DpPoiDTO.class, DealCtx.class);
        m.setAccessible(true);
        m.invoke(sinaiProcessor, dpPoiDTO, spyCtx);
        // assert
        BestShopDTO bestShop = spyCtx.getBestShopResp();
        // shopPic should be original string
        assertEquals("pic10%40", bestShop.getShopPic());
    }

    /**
     * generateAddr: crossRoad is null, should return address only
     */
    @Test
    public void testFillBestShopInfo_GenerateAddrCrossRoadNull() throws Throwable {
        // arrange
        DealCtx ctx = createCtxWithBestShop();
        ctx.setUserlat(0.0);
        ctx.setUserlng(0.0);
        ctx.setMtId(0);
        ctx.setDpId(900);
        // isMt() returns false
        DealCtx spyCtx = spy(ctx);
        doReturn(false).when(spyCtx).isMt();
        // dpPoiDTO with null crossRoad
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setShopName("Shop11");
        dpPoiDTO.setShopType(1);
        dpPoiDTO.setBranchName("BranchK");
        dpPoiDTO.setNormPhones(new ArrayList<>());
        dpPoiDTO.setShopPower(1);
        dpPoiDTO.setAddress("Addr11");
        dpPoiDTO.setCrossRoad(null);
        dpPoiDTO.setLat(0.0);
        dpPoiDTO.setLng(0.0);
        dpPoiDTO.setDefaultPic("pic11@origin");
        // displayShopInfo with empty dpDisplayShopIds
        DealGroupDisplayShopDTO displayShopInfo = new DealGroupDisplayShopDTO();
        displayShopInfo.setDpDisplayShopIds(new ArrayList<>());
        // dealGroupDTO
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getDisplayShopInfo()).thenReturn(displayShopInfo);
        spyCtx.setDealGroupDTO(dealGroupDTO);
        // futureCtx
        FutureCtx futureCtx = mock(FutureCtx.class);
        Future mtShowTypeDTOFuture = mock(Future.class);
        Future dealShopQtyBySearchFuture = mock(Future.class);
        when(futureCtx.getDealShopQtyBySearchFuture()).thenReturn(dealShopQtyBySearchFuture);
        spyCtx.setFutureCtx(futureCtx);
        // dealGroupWrapper.getDealShopQtyBySearch returns 0
        when(dealGroupWrapper.getDealShopQtyBySearch(eq(dealShopQtyBySearchFuture), eq(900L))).thenReturn(0L);
        // geoBiz.googleToGps returns null
        when(geoBiz.googleToGps(any())).thenReturn(null);
        // act
        java.lang.reflect.Method m = SinaiProcessor.class.getDeclaredMethod("fillBestShopInfo", DpPoiDTO.class, DealCtx.class);
        m.setAccessible(true);
        m.invoke(sinaiProcessor, dpPoiDTO, spyCtx);
        // assert
        BestShopDTO bestShop = spyCtx.getBestShopResp();
        // address should be address only
        assertEquals("Addr11", bestShop.getAddress());
    }

    /**
     * mtPoiDtos is empty list, so showType not set
     */
    @Test
    public void testFillBestShopInfo_MtPoiDtosEmpty() throws Throwable {
        // arrange
        DealCtx ctx = createCtxWithBestShop();
        ctx.setUserlat(0.0);
        ctx.setUserlng(0.0);
        ctx.setMtId(1000);
        ctx.setDpId(0);
        // isMt() returns true
        DealCtx spyCtx = spy(ctx);
        doReturn(true).when(spyCtx).isMt();
        // dpPoiDTO
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setShopName("Shop12");
        dpPoiDTO.setShopType(1);
        dpPoiDTO.setBranchName("BranchL");
        dpPoiDTO.setNormPhones(new ArrayList<>());
        dpPoiDTO.setShopPower(1);
        dpPoiDTO.setAddress("Addr12");
        dpPoiDTO.setCrossRoad(null);
        dpPoiDTO.setLat(0.0);
        dpPoiDTO.setLng(0.0);
        dpPoiDTO.setDefaultPic("pic12@origin");
        // displayShopInfo with mtDisplayShopIds
        DealGroupDisplayShopDTO displayShopInfo = new DealGroupDisplayShopDTO();
        displayShopInfo.setMtDisplayShopIds(Arrays.asList(1L));
        // dealGroupDTO
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getDisplayShopInfo()).thenReturn(displayShopInfo);
        spyCtx.setDealGroupDTO(dealGroupDTO);
        // futureCtx
        FutureCtx futureCtx = mock(FutureCtx.class);
        Future mtShowTypeDTOFuture = mock(Future.class);
        when(futureCtx.getMtShowTypeDTOFuture()).thenReturn(mtShowTypeDTOFuture);
        Future dealShopQtyBySearchFuture = mock(Future.class);
        when(futureCtx.getDealShopQtyBySearchFuture()).thenReturn(dealShopQtyBySearchFuture);
        spyCtx.setFutureCtx(futureCtx);
        // poiClientWrapper.getFutureResult returns empty list
        List<MtPoiDto> mtPoiDtos = new ArrayList<>();
        when(poiClientWrapper.<List<MtPoiDto>>getFutureResult(eq(mtShowTypeDTOFuture))).thenReturn(mtPoiDtos);
        // dealGroupWrapper.getDealShopQtyBySearch returns 0
        when(dealGroupWrapper.getDealShopQtyBySearch(eq(dealShopQtyBySearchFuture), eq(1000L))).thenReturn(0L);
        // geoBiz.googleToGps returns null
        when(geoBiz.googleToGps(any())).thenReturn(null);
        // act
        java.lang.reflect.Method m = SinaiProcessor.class.getDeclaredMethod("fillBestShopInfo", DpPoiDTO.class, DealCtx.class);
        m.setAccessible(true);
        m.invoke(sinaiProcessor, dpPoiDTO, spyCtx);
        // assert
        BestShopDTO bestShop = spyCtx.getBestShopResp();
        // showType should not be set
        assertNull(bestShop.getShowType());
    }

    @Before
    public void setUp() {
        ctx = mock(DealCtx.class, RETURNS_DEEP_STUBS);
        futureCtx = new FutureCtx();
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
    }

    @Test
    public void testPrepare_MtAndNeedFillBestShop_MtShopIdPresent() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getMtId()).thenReturn(123);
        when(ctx.isNeedFillBestShop()).thenReturn(true);
        when(ctx.getDpLongShopId()).thenReturn(456L);
        when(ctx.getMtLongShopId()).thenReturn(789L);
        // 不能mock SettableFuture，因为它是final类
        SettableFuture<Object> dpPoiDtoFuture = SettableFuture.create();
        Future<?> mtPoiShowTypeDTOFuture = mock(Future.class);
        Future<?> mtPoiDTOFuture = mock(Future.class);
        Future<?> dealShopQtyFuture = mock(Future.class);
        // 创建mock对象
        DealGroupWrapper dealGroupWrapper = mock(DealGroupWrapper.class);
        PoiShopCategoryWrapper poiShopCategoryWrapper = mock(PoiShopCategoryWrapper.class);
        PoiClientWrapper poiClientWrapper = mock(PoiClientWrapper.class);
        // 使用doReturn/when语法来避免泛型问题
        doReturn(dealShopQtyFuture).when(dealGroupWrapper).preDealShopQtyBySearchFuture(123, true);
        doReturn(dpPoiDtoFuture).when(poiShopCategoryWrapper).preDpPoiDto(456L, SinaiProcessor.SINAI_DP_POI_FIELDS_BEST_SHOP);
        doReturn(mtPoiShowTypeDTOFuture).when(poiClientWrapper).preMtPoiWrappers(789L);
        doReturn(mtPoiDTOFuture).when(poiClientWrapper).preMtPoiDTO(eq(789L), anyList());
        // 创建一个测试用的SinaiProcessor子类
        TestSinaiProcessor processor = new TestSinaiProcessor(dealGroupWrapper, poiShopCategoryWrapper, poiClientWrapper);
        // act
        processor.prepare(ctx);
        // assert
        assertSame(dealShopQtyFuture, futureCtx.getDealShopQtyBySearchFuture());
        assertSame(dpPoiDtoFuture, futureCtx.getDpPoiDtoFuture());
        assertSame(mtPoiShowTypeDTOFuture, futureCtx.getMtShowTypeDTOFuture());
        assertSame(mtPoiDTOFuture, futureCtx.getMtPoiDTOFuture());
        verify(dealGroupWrapper).preDealShopQtyBySearchFuture(123, true);
        verify(poiShopCategoryWrapper).preDpPoiDto(456L, SinaiProcessor.SINAI_DP_POI_FIELDS_BEST_SHOP);
        verify(poiClientWrapper).preMtPoiWrappers(789L);
        verify(poiClientWrapper).preMtPoiDTO(eq(789L), anyList());
        assertEquals(0, processor.fulfillMtShopIdCallCount);
    }

    @Test
    public void testPrepare_DpAndNeedFillBestShop_MtShopIdEmpty() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(false);
        when(ctx.getDpId()).thenReturn(222);
        when(ctx.isNeedFillBestShop()).thenReturn(true);
        when(ctx.getDpLongShopId()).thenReturn(333L);
        when(ctx.getMtLongShopId()).thenReturn(0L);
        // 不能mock SettableFuture，因为它是final类
        SettableFuture<Object> dpPoiDtoFuture = SettableFuture.create();
        Future<?> dealShopQtyFuture = mock(Future.class);
        // 创建mock对象
        DealGroupWrapper dealGroupWrapper = mock(DealGroupWrapper.class);
        PoiShopCategoryWrapper poiShopCategoryWrapper = mock(PoiShopCategoryWrapper.class);
        PoiClientWrapper poiClientWrapper = mock(PoiClientWrapper.class);
        // 使用doReturn/when语法来避免泛型问题
        doReturn(dealShopQtyFuture).when(dealGroupWrapper).preDealShopQtyBySearchFuture(222, false);
        doReturn(dpPoiDtoFuture).when(poiShopCategoryWrapper).preDpPoiDto(333L, SinaiProcessor.SINAI_DP_POI_FIELDS_BEST_SHOP);
        // 创建一个测试用的SinaiProcessor子类
        TestSinaiProcessor processor = new TestSinaiProcessor(dealGroupWrapper, poiShopCategoryWrapper, poiClientWrapper);
        // act
        processor.prepare(ctx);
        // assert
        assertSame(dealShopQtyFuture, futureCtx.getDealShopQtyBySearchFuture());
        assertSame(dpPoiDtoFuture, futureCtx.getDpPoiDtoFuture());
        assertNull(futureCtx.getMtShowTypeDTOFuture());
        assertNull(futureCtx.getMtPoiDTOFuture());
        verify(dealGroupWrapper).preDealShopQtyBySearchFuture(222, false);
        verify(poiShopCategoryWrapper).preDpPoiDto(333L, SinaiProcessor.SINAI_DP_POI_FIELDS_BEST_SHOP);
        verify(poiClientWrapper, never()).preMtPoiWrappers(anyLong());
        verify(poiClientWrapper, never()).preMtPoiDTO(anyLong(), anyList());
        assertEquals(1, processor.fulfillMtShopIdCallCount);
        assertSame(ctx, processor.lastFulfillMtShopIdCtx);
    }

    @Test
    public void testPrepare_MtAndNotNeedFillBestShop() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getMtId()).thenReturn(101);
        when(ctx.isNeedFillBestShop()).thenReturn(false);
        when(ctx.getDpLongShopId()).thenReturn(202L);
        when(ctx.getMtLongShopId()).thenReturn(303L);
        // 不能mock SettableFuture，因为它是final类
        SettableFuture<Object> dpPoiDtoFuture = SettableFuture.create();
        Future<?> mtPoiDTOFuture = mock(Future.class);
        Future<?> dealShopQtyFuture = mock(Future.class);
        // 创建mock对象
        DealGroupWrapper dealGroupWrapper = mock(DealGroupWrapper.class);
        PoiShopCategoryWrapper poiShopCategoryWrapper = mock(PoiShopCategoryWrapper.class);
        PoiClientWrapper poiClientWrapper = mock(PoiClientWrapper.class);
        // 使用doReturn/when语法来避免泛型问题
        doReturn(dealShopQtyFuture).when(dealGroupWrapper).preDealShopQtyBySearchFuture(101, true);
        doReturn(dpPoiDtoFuture).when(poiShopCategoryWrapper).preDpPoiDto(202L, QueryParams.SINAI_DP_POI_FIELDS);
        doReturn(mtPoiDTOFuture).when(poiClientWrapper).preMtPoiDTO(eq(303L), anyList());
        // 创建一个测试用的SinaiProcessor子类
        TestSinaiProcessor processor = new TestSinaiProcessor(dealGroupWrapper, poiShopCategoryWrapper, poiClientWrapper);
        // act
        processor.prepare(ctx);
        // assert
        assertSame(dealShopQtyFuture, futureCtx.getDealShopQtyBySearchFuture());
        assertSame(dpPoiDtoFuture, futureCtx.getDpPoiDtoFuture());
        assertSame(mtPoiDTOFuture, futureCtx.getMtPoiDTOFuture());
        assertNull(futureCtx.getMtShowTypeDTOFuture());
        verify(dealGroupWrapper).preDealShopQtyBySearchFuture(101, true);
        verify(poiShopCategoryWrapper).preDpPoiDto(202L, QueryParams.SINAI_DP_POI_FIELDS);
        verify(poiClientWrapper).preMtPoiDTO(eq(303L), anyList());
        verify(poiClientWrapper, never()).preMtPoiWrappers(anyLong());
        assertEquals(0, processor.fulfillMtShopIdCallCount);
    }

    @Test
    public void testPrepare_DpAndNotNeedFillBestShop() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(false);
        when(ctx.getDpId()).thenReturn(111);
        when(ctx.isNeedFillBestShop()).thenReturn(false);
        when(ctx.getDpLongShopId()).thenReturn(222L);
        // 不能mock SettableFuture，因为它是final类
        SettableFuture<Object> dpPoiDtoFuture = SettableFuture.create();
        Future<?> dealShopQtyFuture = mock(Future.class);
        // 创建mock对象
        DealGroupWrapper dealGroupWrapper = mock(DealGroupWrapper.class);
        PoiShopCategoryWrapper poiShopCategoryWrapper = mock(PoiShopCategoryWrapper.class);
        PoiClientWrapper poiClientWrapper = mock(PoiClientWrapper.class);
        // 使用doReturn/when语法来避免泛型问题
        doReturn(dealShopQtyFuture).when(dealGroupWrapper).preDealShopQtyBySearchFuture(111, false);
        doReturn(dpPoiDtoFuture).when(poiShopCategoryWrapper).preDpPoiDto(222L, QueryParams.SINAI_DP_POI_FIELDS);
        // 创建一个测试用的SinaiProcessor子类
        TestSinaiProcessor processor = new TestSinaiProcessor(dealGroupWrapper, poiShopCategoryWrapper, poiClientWrapper);
        // act
        processor.prepare(ctx);
        // assert
        assertSame(dealShopQtyFuture, futureCtx.getDealShopQtyBySearchFuture());
        assertSame(dpPoiDtoFuture, futureCtx.getDpPoiDtoFuture());
        assertNull(futureCtx.getMtPoiDTOFuture());
        assertNull(futureCtx.getMtShowTypeDTOFuture());
        verify(dealGroupWrapper).preDealShopQtyBySearchFuture(111, false);
        verify(poiShopCategoryWrapper).preDpPoiDto(222L, QueryParams.SINAI_DP_POI_FIELDS);
        verify(poiClientWrapper, never()).preMtPoiDTO(anyLong(), anyList());
        verify(poiClientWrapper, never()).preMtPoiWrappers(anyLong());
        assertEquals(1, processor.fulfillMtShopIdCallCount);
        assertSame(ctx, processor.lastFulfillMtShopIdCtx);
    }

    @Test
    public void testPrepare_DealGroupWrapperThrowsException() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getMtId()).thenReturn(1);
        // 创建mock对象
        DealGroupWrapper dealGroupWrapper = mock(DealGroupWrapper.class);
        PoiShopCategoryWrapper poiShopCategoryWrapper = mock(PoiShopCategoryWrapper.class);
        PoiClientWrapper poiClientWrapper = mock(PoiClientWrapper.class);
        // 使用doThrow来模拟异常，这样可以避免实际抛出异常
        doThrow(new RuntimeException("mock exception")).when(dealGroupWrapper).preDealShopQtyBySearchFuture(1, true);
        // 创建一个测试用的SinaiProcessor子类
        TestSinaiProcessor processor = new TestSinaiProcessor(dealGroupWrapper, poiShopCategoryWrapper, poiClientWrapper);
        try {
            // act
            processor.prepare(ctx);
            fail("Expected RuntimeException was not thrown");
        } catch (RuntimeException e) {
            // 预期会抛出异常
            assertEquals("mock exception", e.getMessage());
        }
        // assert
        assertNull(futureCtx.getDealShopQtyBySearchFuture());
        // 由于异常，后续流程不会执行
        verify(poiShopCategoryWrapper, never()).preDpPoiDto(anyLong(), anyList());
        verify(poiClientWrapper, never()).preMtPoiDTO(anyLong(), anyList());
        verify(poiClientWrapper, never()).preMtPoiWrappers(anyLong());
        assertEquals(0, processor.fulfillMtShopIdCallCount);
    }

    @Test
    public void testPrepare_PoiShopCategoryWrapperThrowsException() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(false);
        when(ctx.getDpId()).thenReturn(10);
        when(ctx.isNeedFillBestShop()).thenReturn(true);
        when(ctx.getDpLongShopId()).thenReturn(20L);
        Future<?> dealShopQtyFuture = mock(Future.class);
        // 创建mock对象
        DealGroupWrapper dealGroupWrapper = mock(DealGroupWrapper.class);
        PoiShopCategoryWrapper poiShopCategoryWrapper = mock(PoiShopCategoryWrapper.class);
        PoiClientWrapper poiClientWrapper = mock(PoiClientWrapper.class);
        // 使用doReturn/when语法来避免泛型问题
        doReturn(dealShopQtyFuture).when(dealGroupWrapper).preDealShopQtyBySearchFuture(10, false);
        // 使用doThrow来模拟异常，这样可以避免实际抛出异常
        doThrow(new RuntimeException("mock exception")).when(poiShopCategoryWrapper).preDpPoiDto(20L, SinaiProcessor.SINAI_DP_POI_FIELDS_BEST_SHOP);
        // 创建一个测试用的SinaiProcessor子类
        TestSinaiProcessor processor = new TestSinaiProcessor(dealGroupWrapper, poiShopCategoryWrapper, poiClientWrapper);
        try {
            // act
            processor.prepare(ctx);
            fail("Expected RuntimeException was not thrown");
        } catch (RuntimeException e) {
            // 预期会抛出异常
            assertEquals("mock exception", e.getMessage());
        }
        // assert
        assertSame(dealShopQtyFuture, futureCtx.getDealShopQtyBySearchFuture());
        assertNull(futureCtx.getDpPoiDtoFuture());
        assertEquals(0, processor.fulfillMtShopIdCallCount);
    }

    private static class TestSinaiProcessor extends SinaiProcessor {

        private final DealGroupWrapper dealGroupWrapper;

        private final PoiShopCategoryWrapper poiShopCategoryWrapper;

        private final PoiClientWrapper poiClientWrapper;

        public int fulfillMtShopIdCallCount = 0;

        public DealCtx lastFulfillMtShopIdCtx = null;

        public TestSinaiProcessor(DealGroupWrapper dealGroupWrapper, PoiShopCategoryWrapper poiShopCategoryWrapper, PoiClientWrapper poiClientWrapper) {
            this.dealGroupWrapper = dealGroupWrapper;
            this.poiShopCategoryWrapper = poiShopCategoryWrapper;
            this.poiClientWrapper = poiClientWrapper;
        }

        @Override
        public void fulfillMtShopId(DealCtx ctx) {
            fulfillMtShopIdCallCount++;
            lastFulfillMtShopIdCtx = ctx;
        }

        @Override
        public void prepare(DealCtx ctx) {
            // 插入门店数量查询
            int dealGroupId = ctx.isMt() ? ctx.getMtId() : ctx.getDpId();
            Future<?> future = dealGroupWrapper.preDealShopQtyBySearchFuture(dealGroupId, ctx.isMt());
            ctx.getFutureCtx().setDealShopQtyBySearchFuture(future);
            //判断是否要填充bestShop的信息
            SettableFuture dpPoiDtoFuture;
            if (ctx.isNeedFillBestShop()) {
                dpPoiDtoFuture = poiShopCategoryWrapper.preDpPoiDto(ctx.getDpLongShopId(), SINAI_DP_POI_FIELDS_BEST_SHOP);
                if (ctx.getMtLongShopId() > 0) {
                    Future mtPoiShowTypeDTOFuture = poiClientWrapper.preMtPoiWrappers(ctx.getMtLongShopId());
                    ctx.getFutureCtx().setMtShowTypeDTOFuture(mtPoiShowTypeDTOFuture);
                }
            } else {
                //SINAI_DP_POI_FIELDS_BEST_SHOP完全包含SINAI_DP_POI_FIELDS，不知道这里为啥要做区分
                dpPoiDtoFuture = poiShopCategoryWrapper.preDpPoiDto(ctx.getDpLongShopId(), QueryParams.SINAI_DP_POI_FIELDS);
            }
            ctx.getFutureCtx().setDpPoiDtoFuture(dpPoiDtoFuture);
            if (ctx.isMt()) {
                Future mtPoiDTOFuture = poiClientWrapper.preMtPoiDTO(ctx.getMtLongShopId(), Lists.newArrayList("mtAvgScore", "typeHierarchy", "frontCateHierarchy"));
                ctx.getFutureCtx().setMtPoiDTOFuture(mtPoiDTOFuture);
            } else {
                fulfillMtShopId(ctx);
            }
        }
    }
}
