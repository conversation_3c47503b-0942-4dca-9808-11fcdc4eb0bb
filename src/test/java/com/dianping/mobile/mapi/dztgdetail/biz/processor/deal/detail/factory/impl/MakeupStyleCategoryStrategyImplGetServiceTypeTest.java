package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.impl;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.lang.reflect.Method;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

public class MakeupStyleCategoryStrategyImplGetServiceTypeTest {

    private MakeupStyleCategoryStrategyImpl makeupStyleCategoryStrategy = new MakeupStyleCategoryStrategyImpl();

    /**
     * 测试 getServiceType 方法，当 dealGroupDTO 为 null 时
     */
    @Test
    public void testGetServiceTypeWhenDealGroupDTOIsNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = null;
        // act
        Method getServiceTypeMethod = MakeupStyleCategoryStrategyImpl.class.getDeclaredMethod("getServiceType", DealGroupDTO.class);
        getServiceTypeMethod.setAccessible(true);
        String result = (String) getServiceTypeMethod.invoke(makeupStyleCategoryStrategy, dealGroupDTO);
        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试 getServiceType 方法，当 dealGroupDTO 不为 null，但 dealGroupDTO.getCategory() 为 null 时
     */
    @Test
    public void testGetServiceTypeWhenCategoryIsNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        // act
        Method getServiceTypeMethod = MakeupStyleCategoryStrategyImpl.class.getDeclaredMethod("getServiceType", DealGroupDTO.class);
        getServiceTypeMethod.setAccessible(true);
        String result = (String) getServiceTypeMethod.invoke(makeupStyleCategoryStrategy, dealGroupDTO);
        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试 getServiceType 方法，当 dealGroupDTO 和 dealGroupDTO.getCategory() 都不为 null，但 dealGroupDTO.getCategory().getServiceType() 为 null 时
     */
    @Test
    public void testGetServiceTypeWhenServiceTypeIsNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO category = new DealGroupCategoryDTO();
        dealGroupDTO.setCategory(category);
        // act
        Method getServiceTypeMethod = MakeupStyleCategoryStrategyImpl.class.getDeclaredMethod("getServiceType", DealGroupDTO.class);
        getServiceTypeMethod.setAccessible(true);
        String result = (String) getServiceTypeMethod.invoke(makeupStyleCategoryStrategy, dealGroupDTO);
        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试 getServiceType 方法，当 dealGroupDTO、dealGroupDTO.getCategory() 和 dealGroupDTO.getCategory().getServiceType() 都不为 null 时
     */
    @Test
    public void testGetServiceTypeWhenAllNotNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO category = new DealGroupCategoryDTO();
        category.setServiceType("testServiceType");
        dealGroupDTO.setCategory(category);
        // act
        Method getServiceTypeMethod = MakeupStyleCategoryStrategyImpl.class.getDeclaredMethod("getServiceType", DealGroupDTO.class);
        getServiceTypeMethod.setAccessible(true);
        String result = (String) getServiceTypeMethod.invoke(makeupStyleCategoryStrategy, dealGroupDTO);
        // assert
        Assert.assertEquals("testServiceType", result);
    }
}
