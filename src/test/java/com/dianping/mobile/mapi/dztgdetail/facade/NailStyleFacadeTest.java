package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.mobile.mapi.dztgdetail.biz.hotstyle.NailStyleService;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetHotNailStyleRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.nailstyle.HotNailStyleModuleVO;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class NailStyleFacadeTest {

    @InjectMocks
    private NailStyleFacade nailStyleFacade;

    @Mock
    private NailStyleService nailStyleService;

    private GetHotNailStyleRequest request;
    private EnvCtx envCtx;
    private HotNailStyleModuleVO hotNailStyleModuleVO;

    private MockedStatic<LionConfigUtils> mocked;
    @Before
    public void setUp() {
        request = new GetHotNailStyleRequest();
        request.setDealGroupId(1L);
        request.setShopId(1L);

        envCtx = new EnvCtx();

        hotNailStyleModuleVO = new HotNailStyleModuleVO();
        mocked = mockStatic(LionConfigUtils.class);
    }

    @After
    public void teardown() {
        this.mocked.close();
    }

    /**
     * 测试美甲热门款式模块开关关闭的情况
     */
    @Test
    public void testGetHotNailStyle_SwitchOff() {
        mocked.when(LionConfigUtils::getHotNailStyleModuleSwitch).thenReturn(false);

        HotNailStyleModuleVO result = nailStyleFacade.getHotNailStyle(request, envCtx);

        assertNull(result);
    }

    /**
     * 测试美甲热门款式模块开关打开的情况
     */
    @Test
    public void testGetHotNailStyle_SwitchOn() {
        mocked.when(LionConfigUtils::getHotNailStyleModuleSwitch).thenReturn(true);
        when(nailStyleService.queryHotNailStyle(request, envCtx)).thenReturn(hotNailStyleModuleVO);

        HotNailStyleModuleVO result = nailStyleFacade.getHotNailStyle(request, envCtx);

        assertSame(hotNailStyleModuleVO, result);
    }

    /**
     * 测试团单ID为null的情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGetHotNailStyle_NullDealGroupId() {
        request.setDealGroupId(null);
        nailStyleFacade.getHotNailStyle(request, envCtx);
    }

    /**
     * 测试团单ID为0的情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGetHotNailStyle_ZeroDealGroupId() {
        request.setDealGroupId(0L);
        nailStyleFacade.getHotNailStyle(request, envCtx);
    }

    /**
     * 测试门店ID为null的情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGetHotNailStyle_NullShopId() {
        request.setShopId(null);
        nailStyleFacade.getHotNailStyle(request, envCtx);
    }

    /**
     * 测试门店ID为0的情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGetHotNailStyle_ZeroShopId() {
        request.setShopId(0L);
        nailStyleFacade.getHotNailStyle(request, envCtx);
    }
}
