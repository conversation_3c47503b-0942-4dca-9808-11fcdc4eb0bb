package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.GoodsAllowSellingInfoWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.MLiveInfoVo;
import com.sankuai.mlive.goods.trade.api.model.GoodsSellingInfoDTO;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ChannelSaleStatusProcessor_ProcessTest {

    @InjectMocks
    private ChannelSaleStatusProcessor channelSaleStatusProcessor;

    @Mock
    private GoodsAllowSellingInfoWrapper goodsAllowSellingInfoWrapper;

    private DealCtx ctx;

    @Before
    public void setUp() {
        // 创建EnvCtx对象
        EnvCtx envCtx = new EnvCtx();
        // 使用带参数的构造函数创建DealCtx对象
        ctx = new DealCtx(envCtx);
    }

    /**
     * 测试正常情况，请求来源为 "mlive"，且 mLiveId 大于 0
     */
    @Test
    @Ignore
    public void testProcess_NormalCase() throws Throwable {
        // arrange
        ctx.setRequestSource("mlive");
        ctx.setMLiveId(1L);
        ctx.setMLiveInfoVo(new MLiveInfoVo());
        GoodsSellingInfoDTO goodsSellingInfoDTO = new GoodsSellingInfoDTO();
        when(goodsAllowSellingInfoWrapper.getGoodsAllowSellingInfoResponse(any())).thenReturn(goodsSellingInfoDTO);
        // act
        channelSaleStatusProcessor.process(ctx);
        // assert
        verify(goodsAllowSellingInfoWrapper, times(1)).getGoodsAllowSellingInfoResponse(any());
        assertSame(goodsSellingInfoDTO, ctx.getMliveSellingInfo());
    }

    /**
     * 测试请求来源不为 "mlive" 且 mLiveId 小于等于 0 的情况
     */
    @Test
    @Ignore
    public void testProcess_NotMliveAndMLiveIdLessThanOrEqualToZero() throws Throwable {
        // arrange
        ctx.setRequestSource("not_mlive");
        ctx.setMLiveId(0L);
        // act
        channelSaleStatusProcessor.process(ctx);
        // assert
        verify(goodsAllowSellingInfoWrapper, never()).getGoodsAllowSellingInfoResponse(any());
    }

    /**
     * 测试调用 getGoodsAllowSellingInfoResponse 方法时发生异常的情况
     */
    @Test
    @Ignore
    public void testProcess_ExceptionWhenGetGoodsAllowSellingInfoResponse() throws Throwable {
        // arrange
        ctx.setRequestSource("mlive");
        ctx.setMLiveId(1L);
        when(goodsAllowSellingInfoWrapper.getGoodsAllowSellingInfoResponse(any())).thenThrow(new RuntimeException("Test exception"));
        // act
        channelSaleStatusProcessor.process(ctx);
        // assert
        verify(goodsAllowSellingInfoWrapper, times(1)).getGoodsAllowSellingInfoResponse(any());
        assertTrue(ctx.isQueryCenterHasError());
    }
}
