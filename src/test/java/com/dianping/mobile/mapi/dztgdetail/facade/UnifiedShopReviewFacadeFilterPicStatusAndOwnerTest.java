package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.PicVideoStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.ShopReviewCtx;
import java.lang.reflect.Method;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedShopReviewFacadeFilterPicStatusAndOwnerTest {

    @Mock
    private ShopReviewCtx shopReviewCtx;

    @Mock
    private EnvCtx envCtx;

    @InjectMocks
    private UnifiedShopReviewFacade unifiedShopReviewFacade;

    private Boolean invokeFilterPicStatusAndOwner(ShopReviewCtx shopReviewCtx, Long userid, Integer status) throws Exception {
        Method method = UnifiedShopReviewFacade.class.getDeclaredMethod("filterPicStatusAndOwner", ShopReviewCtx.class, Long.class, Integer.class);
        method.setAccessible(true);
        return (Boolean) method.invoke(unifiedShopReviewFacade, shopReviewCtx, userid, status);
    }

    @Test
    public void testFilterPicStatusAndOwnerStatusIsNull() throws Throwable {
        assertTrue(invokeFilterPicStatusAndOwner(shopReviewCtx, 1L, null));
    }

    @Test
    public void testFilterPicStatusAndOwnerStatusIsNormal() throws Throwable {
        assertFalse(invokeFilterPicStatusAndOwner(shopReviewCtx, 1L, PicVideoStatusEnum.NORMAL.code));
    }

    @Test
    public void testFilterPicStatusAndOwnerStatusIsAuditAndUseridEqualsMtUserId() throws Throwable {
        when(shopReviewCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.getMtUserId()).thenReturn(1L);
        assertFalse(invokeFilterPicStatusAndOwner(shopReviewCtx, 1L, PicVideoStatusEnum.AUDIT.code));
    }

    @Test
    public void testFilterPicStatusAndOwnerStatusIsAuditAndUseridNotEqualsMtUserId() throws Throwable {
        when(shopReviewCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.getMtUserId()).thenReturn(1L);
        assertTrue(invokeFilterPicStatusAndOwner(shopReviewCtx, 2L, PicVideoStatusEnum.AUDIT.code));
    }

    @Test
    public void testFilterPicStatusAndOwnerStatusIsNotNormalOrAudit() throws Throwable {
        assertTrue(invokeFilterPicStatusAndOwner(shopReviewCtx, 1L, 3));
    }
}
