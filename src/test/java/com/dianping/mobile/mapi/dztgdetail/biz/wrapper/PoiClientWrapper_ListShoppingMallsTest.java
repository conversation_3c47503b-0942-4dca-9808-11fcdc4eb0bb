package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiClientWrapper;
import com.meituan.service.mobile.sinai.client.PoiClientL;
import com.meituan.service.mobile.sinai.client.model.PoiModelL;
import java.util.Arrays;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class PoiClientWrapper_ListShoppingMallsTest {

    @InjectMocks
    private PoiClientWrapper poiClientWrapper;

    @Mock
    private PoiClientL poiClientL;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 listShoppingMalls 方法，正常情况
     */
    @Test
    public void testListShoppingMallsNormal() throws Throwable {
        // arrange
        List<Long> ids = Arrays.asList(1L, 2L, 3L);
        List<String> fields = Arrays.asList("field1", "field2", "field3");
        List<PoiModelL> expected = Arrays.asList(new PoiModelL(), new PoiModelL());
        when(poiClientL.listShoppingMallsL(ids, fields)).thenReturn(expected);
        // act
        List<PoiModelL> actual = poiClientWrapper.listShoppingMalls(ids, fields);
        // assert
        assertEquals(expected, actual);
        verify(poiClientL, times(1)).listShoppingMallsL(ids, fields);
    }

    /**
     * 测试 listShoppingMalls 方法，异常情况
     */
    @Test
    public void testListShoppingMallsException() throws Throwable {
        // arrange
        List<Long> ids = Arrays.asList(1L, 2L, 3L);
        List<String> fields = Arrays.asList("field1", "field2", "field3");
        when(poiClientL.listShoppingMallsL(ids, fields)).thenThrow(new RuntimeException());
        // act
        List<PoiModelL> actual = poiClientWrapper.listShoppingMalls(ids, fields);
        // assert
        assertNull(actual);
        verify(poiClientL, times(1)).listShoppingMallsL(ids, fields);
    }
}
