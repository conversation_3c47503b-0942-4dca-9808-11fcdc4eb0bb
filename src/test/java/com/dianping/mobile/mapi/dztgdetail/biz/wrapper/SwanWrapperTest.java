package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.swan.udqs.api.QueryData;
import com.sankuai.swan.udqs.api.Result;
import com.sankuai.swan.udqs.api.SwanParam;
import com.sankuai.swan.udqs.api.SwanUniteQueryService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SwanWrapperTest {

    @InjectMocks
    private SwanWrapper swanWrapper;

    @Mock
    private SwanUniteQueryService swanUniteQueryService;

    private int bizType = 1;

    private String queryKey = "queryKey";

    private Map<String, Object> requestParams = new HashMap<>();

    @Before
    public void setUp() {
        requestParams.put("key", "value");
    }

    @Test
    public void testGetReviewPhraseListDpShopIdIsNotNumeric() throws Throwable {
        String dpShopId = "abc";
        List<String> result = swanWrapper.getReviewPhraseList(dpShopId);
        assertEquals(new ArrayList<>(), result);
    }

    @Test
    public void testSwanQueryFailure() throws Throwable {
        Result<QueryData> result = new Result<>();
        result.setIfSuccess(false);
        Map<String, Object> actual = swanWrapper.swanQuery(bizType, queryKey, requestParams);
        assertEquals(new HashMap<>(), actual);
    }

    @Test
    public void testSwanQueryNullData() throws Throwable {
        Result<QueryData> result = new Result<>();
        result.setIfSuccess(true);
        result.setData(null);
        Map<String, Object> actual = swanWrapper.swanQuery(bizType, queryKey, requestParams);
        assertEquals(new HashMap<>(), actual);
    }

    @Test
    public void testSwanQueryEmptyResultSet() throws Throwable {
        Result<QueryData> result = new Result<>();
        result.setIfSuccess(true);
        QueryData queryData = new QueryData();
        result.setData(queryData);
        queryData.setResultSet(null);
        Map<String, Object> actual = swanWrapper.swanQuery(bizType, queryKey, requestParams);
        assertEquals(new HashMap<>(), actual);
    }
}
