package com.dianping.mobile.mapi.dztgdetail.util;

import org.junit.Assert;
import org.junit.Test;
import static org.junit.Assert.*;

public class JsonLabelUtilTest {

    /**
     * 测试 getMtLiveMiniAppBuyBannerJson 方法，当 content 为空时
     */
    @Test
    public void testGetMtLiveMiniAppBuyBannerJsonContentIsNull() throws Throwable {
        // arrange
        String content = null;

        // act
        String result = JsonLabelUtil.getMtLiveMiniAppBuyBannerJson(content);

        // assert
        assertNull(result);
    }

    /**
     * 测试 getMtLiveMiniAppBuyBannerJson 方法，当 content 为空字符串时
     */
    @Test
    public void testGetMtLiveMiniAppBuyBannerJsonContentIsEmpty() throws Throwable {
        // arrange
        String content = "";

        // act
        String result = JsonLabelUtil.getMtLiveMiniAppBuyBannerJson(content);

        // assert
        assertNull(result);
    }

    /**
     * 测试 getMtLiveMiniAppBuyBannerJson 方法，当 content 不为空时
     */
    @Test
    public void testGetMtLiveMiniAppBuyBannerJsonContentIsNotEmpty() throws Throwable {
        // arrange
        String content = "test content";

        // act
        String result = JsonLabelUtil.getMtLiveMiniAppBuyBannerJson(content);

        // assert
        assertNotNull(result);
        assertTrue(result.contains(content));
    }

    @Test
    public void testBuildCostEffectivePinTuanBannerJson() {
        String bannerText = "横幅标题";
        String result = JsonLabelUtil.buildCostEffectivePinTuanBannerJson(bannerText);
        Assert.assertTrue(result.contains(bannerText));
    }
}
