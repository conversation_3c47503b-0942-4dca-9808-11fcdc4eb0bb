package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealTechCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.utils.FaultToleranceUtils;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.slf4j.Logger;

/**
 * Test cases for QueryCenterProcessor.process method
 */
@RunWith(MockitoJUnitRunner.class)
public class QueryCenterProcessorProcessTest {

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Mock
    private Logger logger;

    @InjectMocks
    private QueryCenterProcessor processor;

    private DealCtx ctx;

    private Future futureMock;

    @Before
    public void setup() {
        MockitoAnnotations.openMocks(this);
        // Setup common test objects
        EnvCtx envCtx = new EnvCtx();
        ctx = new DealCtx(envCtx);
        ctx.setDealBaseReq(new DealBaseReq());
        futureMock = mock(Future.class);
        FutureCtx futureCtx = new FutureCtx();
        futureCtx.setSingleDealGroupDtoFuture(futureMock);
        ctx.setFutureCtx(futureCtx);
        ctx.setDealTechCtx(new DealTechCtx());
    }

    /**
     * Test process method with fillSelfDealInfo when dealGroupDTO has cleaning self own attribute
     */
    @Test
    public void testProcess_FillSelfDealInfo_WithCleaningSelfOwnAttr() throws Throwable {
        // Arrange
        DealGroupDTO dealGroupDTO = createDealGroupDTOWithCategory();
        ArrayList<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr = new AttrDTO();
        attr.setName("self_own_product");
        attr.setValue(Collections.singletonList("是"));
        attrs.add(attr);
        when(dealGroupDTO.getAttrs()).thenReturn(attrs);
        when(queryCenterWrapper.getDealGroupDTO(any(Future.class))).thenReturn(dealGroupDTO);
        // Act
        processor.process(ctx);
        // Assert
        verify(queryCenterWrapper).getDealGroupDTO(any(Future.class));
        assertTrue("Should be marked as cleaning self own deal", ctx.isCleaningSelfOwnDeal());
        assertFalse("Should not be marked as care free deal", ctx.isCareFreeDeal());
        assertEquals("DealGroupDTO should be set", dealGroupDTO, ctx.getDealGroupDTO());
    }

    /**
     * Test process method with fillSelfDealInfo when dealGroupDTO has no attributes
     */
    @Test
    public void testProcess_FillSelfDealInfo_WithEmptyAttrs() throws Throwable {
        // Arrange
        DealGroupDTO dealGroupDTO = createDealGroupDTOWithCategory();
        when(dealGroupDTO.getAttrs()).thenReturn(new ArrayList<>());
        when(queryCenterWrapper.getDealGroupDTO(any(Future.class))).thenReturn(dealGroupDTO);
        // Act
        processor.process(ctx);
        // Assert
        verify(queryCenterWrapper).getDealGroupDTO(any(Future.class));
        assertFalse("Should not be marked as cleaning self own deal", ctx.isCleaningSelfOwnDeal());
        assertFalse("Should not be marked as care free deal", ctx.isCareFreeDeal());
        assertEquals("DealGroupDTO should be set", dealGroupDTO, ctx.getDealGroupDTO());
    }

    /**
     * Helper method to create a DealGroupDTO with required fields
     */
    private DealGroupDTO createDealGroupDTOWithCategory() {
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        // Setup Category
        DealGroupCategoryDTO categoryDTO = mock(DealGroupCategoryDTO.class);
        when(categoryDTO.getCategoryId()).thenReturn(123L);
        when(dealGroupDTO.getCategory()).thenReturn(categoryDTO);
        // Setup Basic
        DealGroupBasicDTO basicDTO = mock(DealGroupBasicDTO.class);
        // Setup empty attrs by default
        when(dealGroupDTO.getAttrs()).thenReturn(new ArrayList<>());
        return dealGroupDTO;
    }
}
