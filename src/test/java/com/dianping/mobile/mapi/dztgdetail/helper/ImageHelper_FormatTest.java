package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.piccentercloud.display.api.PictureUrlGenerator;
import com.dianping.piccentercloud.display.api.PictureVisitParams;
import com.dianping.piccentercloud.display.api.enums.PictureVisitPattern;
import com.dianping.piccentercloud.display.api.enums.WaterMark;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;
import java.lang.reflect.Field;

@RunWith(MockitoJUnitRunner.class)
public class ImageHelper_FormatTest {

    @Mock
    private PictureUrlGenerator pictureUrlGenerator;

    private void setPictureUrlGenerator(PictureUrlGenerator generator) throws Exception {
        Field field = ImageHelper.class.getDeclaredField("pictureUrlGenerator");
        field.setAccessible(true);
        field.set(null, generator);
    }

    /**
     * 测试 key 为空的情况
     */
    @Test
    public void testFormatKeyIsEmpty() throws Throwable {
        String result = ImageHelper.format("", 1, 1);
        assertEquals("", result);
    }
}
