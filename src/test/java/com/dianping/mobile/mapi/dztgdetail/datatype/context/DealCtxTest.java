package com.dianping.mobile.mapi.dztgdetail.datatype.context;

import com.alibaba.fastjson.JSON;
import org.mockito.Mockito;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

public class DealCtxTest {

    private DealCtx dealCtx;

    private EnvCtx envCtx;

    @Before
    public void setUp() {
        envCtx = Mockito.mock(EnvCtx.class);
        // envCtx = JSON.parseObject("{\"android\":false,\"apollo\":false,\"appDeviceId\":\"c05bcc487c8b4d8584b872d8dccebc24a170233661538604446\",\"appId\":4,\"clientType\":100502,\"dianpingClientList\":[\"DIANPING_APP\",\"DIANPING_FROM_H5\",\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"],\"dpMerchant\":false,\"dpMiniApp\":false,\"dpMiniAppList\":[\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"],\"dpUserId\":0,\"dztgClientTypeEnum\":\"THIRD_PLATFORM\",\"external\":true,\"externalAndNoScene\":true,\"fromH5\":false,\"ios\":true,\"login\":false,\"mainApp\":true,\"mainWX\":false,\"mainWeb\":false,\"meituanClientList\":[\"MEITUAN_APP\",\"MEITUAN_FROM_H5\",\"MEITUAN_MAP_APP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_WEIXIN_MINIAPP\"],\"merchantClientList\":[\"DPMERCHANT\"],\"miniApp\":false,\"mt\":false,\"mtMiniApp\":false,\"mtMiniAppList\":[\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_KUAISHOU_MINIAPP\",\"MEITUAN_WANWU_MINIAPP\"],\"mtUserId\":0,\"mtsiFlag\":\"0\",\"native\":false,\"nativeAppList\":[\"MEITUAN_APP\",\"MEITUAN_MAP_APP\",\"DIANPING_APP\"],\"realMtUserId\":0,\"requestURI\":\"/general/platform/dztgdetail/dzdealbase.bin\",\"startTime\":1705561819558,\"thirdDztgClientTypeEnum\":\"APOLLO\",\"thirdPlatform\":true,\"unionId\":\"c05bcc487c8b4d8584b872d8dccebc24a170233661538604446\",\"userAgent\":\"MApi 1.4 (dpappolo 5.6.1 appstore; iPhone 14.8 iPhone13,2)\",\"userIp\":\"***************\",\"uuid\":\"c05bcc487c8b4d8584b872d8dccebc24a170233661538604446\",\"version\":\"11.4.10\"}",EnvCtx.class);
        dealCtx = new DealCtx(envCtx);
    }

    /**
     * 测试 isThirdPArt 方法，当 envCtx 是第三方平台时，应返回 true
     */
    @Test
    public void testIsThirdPArtWhenEnvCtxIsThirdPlatform() {
        // arrange
        Mockito.when(envCtx.isThirdPlatform()).thenReturn(true);
        // act
        boolean result = dealCtx.isThirdPArt();
        // assert
        assertTrue(result);
    }

    /**
     * 测试 isThirdPArt 方法，当 envCtx 不是第三方平台时，应返回 false
     */
    @Test
    public void testIsThirdPArtWhenEnvCtxIsNotThirdPlatform() {
        // arrange
        Mockito.when(envCtx.isThirdPlatform()).thenReturn(false);
        // act
        boolean result = dealCtx.isThirdPArt();

        // assert
        assertFalse(result);
    }
}
