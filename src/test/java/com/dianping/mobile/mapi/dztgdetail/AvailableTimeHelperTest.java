package com.dianping.mobile.mapi.dztgdetail;

import com.alibaba.fastjson.JSON;
import com.dianping.mobile.mapi.dztgdetail.availabletime.AvailableTimeStrategyFactory;
import com.dianping.mobile.mapi.dztgdetail.availabletime.impl.KTVAvailableTimeStrategy;
import com.dianping.mobile.mapi.dztgdetail.common.enums.AvailableTimeStrategyEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.AvailableTimeHelper;
import com.dianping.mobile.mapi.dztgdetail.util.ApplicationContextGetBeanHelper;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.context.ApplicationContext;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ApplicationContextGetBeanHelper.class, ApplicationContext.class})
public class AvailableTimeHelperTest {

    @Test
    public void testDisableDateDocSuccession() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Method getDisableDateDoc = AvailableTimeHelper.class.getDeclaredMethod("getDisableDateDoc", List.class);
        getDisableDateDoc.setAccessible(true);
        List<Integer> req = new ArrayList<>();
        req.add(5);
        req.add(6);
        req.add(7);
        String result = (String) getDisableDateDoc.invoke(null, req);
        System.out.println(result);
        Assert.assertEquals("周一至周四", result);
    }

    @Test
    public void testDisableDateDocUnSuccession() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Method getDisableDateDoc = AvailableTimeHelper.class.getDeclaredMethod("getDisableDateDoc", List.class);
        getDisableDateDoc.setAccessible(true);
        List<Integer> req = new ArrayList<>();
        req.add(5);
        String result = (String) getDisableDateDoc.invoke(null, req);
        System.out.println(result);
        Assert.assertEquals("部分日期", result);
    }

    @Test
    public void testDisableDateDocSingle() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Method getDisableDateDoc = AvailableTimeHelper.class.getDeclaredMethod("getDisableDateDoc", List.class);
        getDisableDateDoc.setAccessible(true);
        List<Integer> req = new ArrayList<>();
        req.add(5);
        req.add(6);
        req.add(7);
        req.add(1);
        req.add(3);
        req.add(4);
        String result = (String) getDisableDateDoc.invoke(null, req);
        System.out.println(result);
        Assert.assertEquals("周二", result);
    }

    @Test
    public void testAvailableDateDocSuccession() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Method getDisableDateDoc = AvailableTimeHelper.class.getDeclaredMethod("getAvailableDateDoc", List.class, List.class);
        getDisableDateDoc.setAccessible(true);
        List<Integer> req = new ArrayList<>();
        req.add(5);
        req.add(6);
        req.add(7);
        List<Integer> req1 = new ArrayList<>();
        String result = (String) getDisableDateDoc.invoke(null, req, req1);
        System.out.println(result);
        Assert.assertEquals("周五至周日", result);
    }

    @Test
    public void testAvailableDateDocUnSuccession() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Method getDisableDateDoc = AvailableTimeHelper.class.getDeclaredMethod("getAvailableDateDoc", List.class, List.class);
        getDisableDateDoc.setAccessible(true);
        List<Integer> req = new ArrayList<>();
        req.add(1);
        req.add(3);
        List<Integer> req1 = new ArrayList<>();
        req1.add(101);
        String result = (String) getDisableDateDoc.invoke(null, req, req1);
        System.out.println(result);
        Assert.assertEquals("部分日期", result);
    }

    @Test
    public void testAvailableDateDocSingle() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Method getDisableDateDoc = AvailableTimeHelper.class.getDeclaredMethod("getAvailableDateDoc", List.class, List.class);
        getDisableDateDoc.setAccessible(true);
        List<Integer> req = new ArrayList<>();
        req.add(4);
        List<Integer> req1 = new ArrayList<>();
        String result = (String) getDisableDateDoc.invoke(null, req, req1);
        System.out.println(result);
        Assert.assertEquals("周四", result);
    }

    @Test
    public void testAvailableDateDocWithDisable() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Method getDisableDateDoc = AvailableTimeHelper.class.getDeclaredMethod("getAvailableDateDoc", List.class, List.class);
        getDisableDateDoc.setAccessible(true);
        List<Integer> req = new ArrayList<>();
        req.add(1);
        req.add(2);
        List<Integer> req1 = new ArrayList<>();
        req1.add(101);
        String result = (String) getDisableDateDoc.invoke(null, req, req1);
        System.out.println(result);
        Assert.assertEquals("部分日期", result);
    }

    /**
     * 测试正则表达式效果
     *
     * @throws NoSuchMethodException
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     */
    @Test
    public void testCheckTimeOfDayFormat() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Method getCheckTimeOfDayFormat = AvailableTimeHelper.class.getDeclaredMethod("checkTimeOfDayFormat", String.class);
        getCheckTimeOfDayFormat.setAccessible(true);
        String req = "00:00-23:59";
        Boolean result = (Boolean) getCheckTimeOfDayFormat.invoke(null, req);
        Assert.assertEquals(true, result);

        req = "00:00-23:59";
        result = (Boolean) getCheckTimeOfDayFormat.invoke(null, req);
        Assert.assertEquals(true, result);

        req = "abc";
        result = (Boolean) getCheckTimeOfDayFormat.invoke(null, req);
        Assert.assertEquals(false, result);

        req = "00:00-24:59";
        result = (Boolean) getCheckTimeOfDayFormat.invoke(null, req);
        Assert.assertEquals(false, result);

        req = "0o:00-23:59";
        result = (Boolean) getCheckTimeOfDayFormat.invoke(null, req);
        Assert.assertEquals(false, result);
    }

    @Test
    public void testTimeOfDaySingle() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        List<String> timeStrs = new ArrayList<>();
        timeStrs.add("00:08-10:14");
        timeStrs.add("00:00-03:14");
        timeStrs.add("10:02-20:11");

        List<String[]> startEndTimes = new ArrayList<>();
        for (String timeStr : timeStrs) {
            startEndTimes.add(timeStr.split("-"));
        }
        Method getFinalStartEndTime = AvailableTimeHelper.class.getDeclaredMethod("getFinalStartEndTime", List.class);
        getFinalStartEndTime.setAccessible(true);
        List<String[]> req = startEndTimes;
        String[] req1 = (String[]) getFinalStartEndTime.invoke(null, req);
        Assert.assertEquals(2, req1.length);

        Method getTimeDoc = AvailableTimeHelper.class.getDeclaredMethod("getTimeDoc", String[].class);
        getTimeDoc.setAccessible(true);
        String result = (String) getTimeDoc.invoke(null, (Object) req1);
        System.out.println(result);

        Assert.assertEquals("00:00-20:11", result);
    }

    @Test
    public void testTimeOfDayAll() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        List<String> timeStrs = new ArrayList<>();
        timeStrs.add("00:08-10:14");
        timeStrs.add("00:00-03:14");
        timeStrs.add("10:02-20:11");
        timeStrs.add("11:02-23:59");


        List<String[]> startEndTimes = new ArrayList<>();
        for (String timeStr : timeStrs) {
            startEndTimes.add(timeStr.split("-"));
        }
        Method getFinalStartEndTime = AvailableTimeHelper.class.getDeclaredMethod("getFinalStartEndTime", List.class);
        getFinalStartEndTime.setAccessible(true);
        List<String[]> req = startEndTimes;
        String[] req1 = (String[]) getFinalStartEndTime.invoke(null, req);
        Assert.assertEquals(2, req1.length);

        Method getTimeDoc = AvailableTimeHelper.class.getDeclaredMethod("getTimeDoc", String[].class);
        getTimeDoc.setAccessible(true);
        String result = (String) getTimeDoc.invoke(null, (Object) req1);
        System.out.println(result);

        Assert.assertEquals("全天", result);
    }

    @Test
    public void testTimeOfDayPart() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        List<String> timeStrs = new ArrayList<>();
        timeStrs.add("00:08-10:14");
        timeStrs.add("00:00-03:14");
        timeStrs.add("11:02-23:59");


        List<String[]> startEndTimes = new ArrayList<>();
        for (String timeStr : timeStrs) {
            startEndTimes.add(timeStr.split("-"));
        }
        Method getFinalStartEndTime = AvailableTimeHelper.class.getDeclaredMethod("getFinalStartEndTime", List.class);
        getFinalStartEndTime.setAccessible(true);
        List<String[]> req = startEndTimes;
        String[] req1 = (String[]) getFinalStartEndTime.invoke(null, req);
        Assert.assertEquals(0, req1.length);

        Method getTimeDoc = AvailableTimeHelper.class.getDeclaredMethod("getTimeDoc", String[].class);
        getTimeDoc.setAccessible(true);
        String result = (String) getTimeDoc.invoke(null, (Object) req1);
        System.out.println(result);

        Assert.assertEquals("部分时段", result);
    }

    @Mock
    private KTVAvailableTimeStrategy ktvAvailableTimeStrategy;

    @Mock
    private AvailableTimeStrategyFactory availableTimeStrategyFactory;
    @Test
    public void testGetCustomAvailable() {
        PowerMockito.mockStatic(ApplicationContextGetBeanHelper.class);
        when(ApplicationContextGetBeanHelper.getBean(AvailableTimeStrategyFactory.class))
                .thenAnswer((Answer<?>)invocation -> availableTimeStrategyFactory);
        when(availableTimeStrategyFactory.getStrategy(any(DealCtx.class))).thenReturn(ktvAvailableTimeStrategy);
        when(ktvAvailableTimeStrategy.getStrategyType()).thenReturn(AvailableTimeStrategyEnum.WIN_BAR_STRATEGY);
        when(ktvAvailableTimeStrategy.getAvailableTime(any(DealCtx.class))).thenReturn("部分适用");
        DealCtx mock = Mockito.mock(DealCtx.class);
        String customAvailableTimeOfDays = AvailableTimeHelper.getCustomAvailableTimeOfDays(mock);
        assert StringUtils.equals("部分适用", customAvailableTimeOfDays);
    }

    // @Test
    // public void testCustomReminderInfo() {
    // DealGroupDTO dto =
    // JSON.parseObject("{\"dpDealGroupId\":698116219,\"mtDealGroupId\":698116219,\"basic\":{\"categoryId\":304,\"title\":\"【白天专场】经典足道/中式保健二选一（免门票）\",\"brandName\":\"万泉河洗浴中心\",\"titleDesc\":\"仅售199元，价值399元【白天专场】经典足道/中式保健二选一（免门票）！\",\"beginSaleDate\":\"2021-05-11
    // 18:18:42\",\"endSaleDate\":\"2025-01-10
    // 00:00:00\",\"status\":1,\"saleChannel\":0,\"salePlatform\":3,\"sourceId\":102,\"tradeType\":3},\"image\":{\"defaultPicPath\":\"https://p0.meituan.net/dpmerchantpic/ed1e712965e8904048520ea89ee261ed77311.jpg\",\"allPicPaths\":\"https://p0.meituan.net/dpmerchantpic/ed1e712965e8904048520ea89ee261ed77311.jpg\"},\"category\":{\"categoryId\":304,\"serviceType\":\"店内服务\",\"serviceTypeId\":691},\"serviceProject\":{\"title\":\"团购详情\",\"salePrice\":\"199.00\",\"marketPrice\":\"399.00\",\"mustGroups\":[],\"optionGroups\":[{\"optionalCount\":1,\"groups\":[{\"skuId\":0,\"categoryId\":897,\"name\":\"经典足道（免门票）\",\"amount\":1,\"marketPrice\":\"399.0\",\"status\":10,\"attrs\":[{\"metaAttrId\":537,\"attrName\":\"content\",\"chnName\":\"项目描述\",\"attrValue\":\"赠送：水果、小吃、酸奶\\n使用时间：10:00-18:00\",\"rawAttrValue\":\"赠送：水果、小吃、酸奶\\n使用时间：10:00-18:00\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":642,\"attrName\":\"spuCategory\",\"chnName\":\"服务类型\",\"attrValue\":\"足疗\",\"rawAttrValue\":\"足疗\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":439913,\"attrName\":\"ServicePosition\",\"chnName\":\"服务部位\",\"attrValue\":\"足部\",\"rawAttrValue\":\"足部\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1390,\"attrName\":\"serviceDuration\",\"chnName\":\"服务时长\",\"attrValue\":\"70分钟\",\"rawAttrValue\":\"70\",\"unit\":\"分钟\",\"valueType\":500,\"sequence\":0}]},{\"skuId\":0,\"categoryId\":897,\"name\":\"中式保健\",\"amount\":1,\"marketPrice\":\"399.0\",\"status\":10,\"attrs\":[{\"metaAttrId\":537,\"attrName\":\"content\",\"chnName\":\"项目描述\",\"attrValue\":\"赠送：水果、小吃、酸奶\\n使用时间：10:00-18:00\",\"rawAttrValue\":\"赠送：水果、小吃、酸奶\\n使用时间：10:00-18:00\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":642,\"attrName\":\"spuCategory\",\"chnName\":\"服务类型\",\"attrValue\":\"推拿/按摩\",\"rawAttrValue\":\"推拿/按摩\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":439913,\"attrName\":\"ServicePosition\",\"chnName\":\"服务部位\",\"attrValue\":\"全身\",\"rawAttrValue\":\"全身\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1390,\"attrName\":\"serviceDuration\",\"chnName\":\"服务时长\",\"attrValue\":\"60分钟\",\"rawAttrValue\":\"60\",\"unit\":\"分钟\",\"valueType\":500,\"sequence\":0}]}]}],\"structType\":\"uniform-structure-table\"},\"channel\":{\"channelId\":3,\"channelEn\":\"joy\",\"channelCn\":\"休闲娱乐\",\"channelGroupId\":2,\"channelGroupEn\":\"general\",\"channelGroupCn\":\"到店综合\"},\"attrs\":[{\"name\":\"product_finish_date\",\"value\":[\"1970-01-01
    // 08:00:00\"],\"source\":0},{\"name\":\"calc_holiday_available\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_business_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_channel_id_allowed\",\"value\":[\"0\"],\"source\":0},{\"name\":\"product_can_use_coupon\",\"value\":[\"true\"],\"source\":0},{\"name\":\"preSaleTag\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_third_party_verify\",\"value\":[\"true\"],\"source\":0},{\"name\":\"product_block_stock\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_discount_rule_id\",\"value\":[\"0\"],\"source\":0},{\"name\":\"service_type\",\"value\":[\"店内服务\"],\"source\":0},{\"name\":\"reservation_is_needed_or_not\",\"value\":[\"否\"],\"source\":0},{\"name\":\"sys_deal_universal_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"category\",\"value\":[\"30\",\"3005\"],\"source\":0}],\"rule\":{\"buyRule\":{\"maxPerUser\":0,\"minPerUser\":1},\"useRule\":{\"receiptEffectiveDate\":{\"receiptDateType\":1,\"receiptValidDays\":30,\"receiptBeginDate\":\"2024-02-05
    // 14:37:03\",\"receiptEndDate\":\"2024-03-06
    // 23:59:59\",\"showText\":\"购买后30天内有效\"},\"availableDate\":{\"availableType\":0,\"cycleAvailableDateList\":[{\"availableDays\":[1],\"availableTimeRangePerDay\":[{\"from\":\"1970-01-01
    // 10:00:00\",\"to\":\"1970-01-01
    // 18:00:00\"}]},{\"availableDays\":[2],\"availableTimeRangePerDay\":[{\"from\":\"1970-01-01
    // 10:00:00\",\"to\":\"1970-01-01
    // 18:00:00\"}]},{\"availableDays\":[3],\"availableTimeRangePerDay\":[{\"from\":\"1970-01-01
    // 10:00:00\",\"to\":\"1970-01-01
    // 18:00:00\"}]},{\"availableDays\":[4],\"availableTimeRangePerDay\":[{\"from\":\"1970-01-01
    // 10:00:00\",\"to\":\"1970-01-01
    // 18:00:00\"}]},{\"availableDays\":[5],\"availableTimeRangePerDay\":[{\"from\":\"1970-01-01
    // 10:00:00\",\"to\":\"1970-01-01
    // 18:00:00\"}]},{\"availableDays\":[6],\"availableTimeRangePerDay\":[{\"from\":\"1970-01-01
    // 10:00:00\",\"to\":\"1970-01-01
    // 18:00:00\"}]},{\"availableDays\":[7],\"availableTimeRangePerDay\":[{\"from\":\"1970-01-01
    // 10:00:00\",\"to\":\"1970-01-01
    // 18:00:00\"}]}]}},\"refundRule\":{\"supportRefundType\":1,\"supportOverdueAutoRefund\":true}},\"displayShopInfo\":{\"dpDisplayShopIds\":[837252121],\"mtDisplayShopIds\":[837252121]},\"tags\":[{\"id\":100054242,\"tagName\":\"成人\"},{\"id\":100054246,\"tagName\":\"含按摩\"},{\"id\":100053227,\"tagName\":\"通用\"},{\"id\":100054241,\"tagName\":\"单人\"},{\"id\":100054247,\"tagName\":\"足疗\"}],\"customer\":{\"originCustomerId\":40417119},\"deals\":[{\"dealId\":765932650,\"basic\":{\"title\":\"【白天专场】经典足道/中式保健二选一（免门票）\",\"originTitle\":\"【白天专场】经典足道/中式保健二选一（免门票）\",\"thirdPartyId\":166,\"status\":1},\"price\":{\"salePrice\":\"199.00\",\"marketPrice\":\"399.00\",\"version\":7281292059},\"stock\":{\"dpSales\":82,\"dpTotal\":2000000,\"dpRemain\":1999918,\"mtSales\":275,\"mtTotal\":2000000,\"mtRemain\":1999725,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1,\"sharedSales\":0,\"sharedTotal\":0,\"sharedRemain\":0,\"isSharedSoldOut\":false},\"attrs\":[{\"name\":\"sku_receipt_type\",\"value\":[\"3\"],\"source\":0}],\"dealIdInt\":765932650}],\"price\":{\"salePrice\":\"199.00\",\"marketPrice\":\"399.00\",\"version\":7281292059},\"stock\":{\"dpSales\":82,\"dpTotal\":2000000,\"dpRemain\":1999918,\"mtSales\":275,\"mtTotal\":2000000,\"mtRemain\":1999725,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1},\"detail\":{\"dealGroupPics\":\"https://p0.meituan.net/dpmerchantpic/ed1e712965e8904048520ea89ee261ed77311.jpg\",\"images\":[\"https://p0.meituan.net/dpmerchantpic/ed1e712965e8904048520ea89ee261ed77311.jpg%40450w_280h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\"],\"importantPoint\":\"\",\"templateDetailDTOs\":[{\"title\":\"产品介绍\",\"content\":\"<p
    // class=\\\"listitem\\\"></p>\\n<p class=\\\"explain\\\"></p>\\n<div class=\\\"img\\\"><img
    // src=\\\"https://p0.meituan.net/dpmerchantpic/c2995089855c117a5a06a6a20f34d044374328.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\"
    // /></div>\\n<p class=\\\"listitem\\\"></p>\\n<p class=\\\"explain\\\"></p>\\n<div class=\\\"img\\\"><img
    // src=\\\"https://p1.meituan.net/dpmerchantpic/942a5f3859c3ccb6e6d805f025da662d1413249.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\"
    // /></div>\\n\\n\",\"type\":5,\"blockId\":0},{\"title\":\"团购详情\",\"content\":\" <div
    // class=\\\"detail-tit\\\"></div>\\n <div>\\n <table width=\\\"100%\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\"
    // class=\\\"detail-table\\\">\\n <thead>\\n <tr>\\n <th width=\\\"50%\\\">名称</th>\\n <th
    // width=\\\"25%\\\">数量</th>\\n <th width=\\\"25%\\\">单价</th>\\n </tr>\\n </thead>\\n <tbody>\\n <tr>\\n <th
    // colspan=\\\"3\\\">以下2选1</th>\\n </tr>\\n <tr>\\n <td>经典足道（免门票）</td>\\n <td class=\\\"tc\\\">1</td>\\n <td
    // class=\\\"tc\\\">399元</td>\\n </tr>\\n <tr>\\n <td>中式保健</td>\\n <td class=\\\"tc\\\">1</td>\\n <td
    // class=\\\"tc\\\">399元</td>\\n </tr>\\n <tr class=\\\"total\\\">\\n <td></td>\\n <td
    // class=\\\"tc\\\">总价<br><strong>团购价</strong></td>\\n <td class=\\\"tc\\\">399元<br><strong>199元</strong>\\n
    // </td>\\n </tr>\\n </tbody>\\n </table>\\n
    // </div>\\n<div><p>【包含】</p><p>白天专场，使用时间：10:00-18:00</p><p>经典足道70分钟/中式保健60分钟二选一；免单人门票；</p><p>赠送：水果、小吃、酸奶</p><p>温馨提示：以结账时间为准；</p><p>门票含洗浴，消毒毛巾，消毒拖鞋，洗发水，沐浴露，牙刷，牙膏，剃胡刀，桑拿，消毒浴巾，消毒浴服，休息大厅，wifi共计24小时。</p><p>【特别提示】</p><p>-
    // 无需预约，消费高峰可能需要等位；</p><p>- 进店先消费，离店时验证电子票；</p><p>- 儿童1.2米（不含）以下购买儿童浴资，1.2米（含）以上购买成人浴资；</p><p>-
    // 休息大厅过夜须携带本人有效身份证件并登记（一人一证）；</p><p>- 24:00—凌晨6:00在店加收过夜费50元/位；</p><p>- 提供免费的无线网，停车收费（详情咨询前台）；</p><p>-
    // 免费提供更衣柜、洗浴用品、浴巾浴服、拖鞋等不可带走；</p><p>-
    // 本票不能与店内其它优惠同时使用；</p></div>\\n\\n\",\"type\":1,\"blockId\":0},{\"title\":\"购买须知\",\"content\":\"<div
    // class=\\\"detail-box\\\">\\n <div class=\\\"purchase-notes\\\">\\n\\t\\t <dl>\\n <dt>有效期</dt>\\n <dd>\\n <p
    // class=\\\"listitem\\\">\\n 购买后30天内有效\\n </p>\\n </dd>\\n </dl>\\n <dl>\\n <dt>使用时间</dt>\\n <dd>\\n <p
    // class=\\\"listitem\\\">周一至周日 10:00-18:00可用</p>\\n </dd>\\n </dl>\\n <dl>\\n <dt>验证方式</dt>\\n <dd>\\n <p
    // class=\\\"listitem\\\">离店时验证团购券</p>\\n </dd>\\n </dl>\\n <dl>\\n <dt>预约信息</dt>\\n <dd>\\n <p
    // class=\\\"listitem\\\">无需预约，如遇消费高峰时段您可能需要排队</p>\\n </dd>\\n </dl>\\n <dl>\\n <dt>适用人数</dt>\\n <dd>\\n <p
    // class=\\\"listitem\\\">每张团购券最多1人使用</p>\\n </dd>\\n </dl>\\n <dl>\\n <dt>规则提醒</dt>\\n <dd>\\n <p
    // class=\\\"listitem\\\">需您当日一次性体验完毕所有项目</p>\\n <p class=\\\"listitem\\\">不再与其他优惠同享\\n</p>\\n </dd>\\n </dl>\\n
    // <dl>\\n <dt>温馨提示</dt>\\n <dd>\\n <p class=\\\"listitem\\\">如需团购券发票，请您在消费时向商户咨询</p>\\n <p
    // class=\\\"listitem\\\">为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！</p>\\n </dd>\\n </dl>\\n
    // </div>\\n</div>\\n\",\"type\":2,\"blockId\":0}]},\"saleChannelAggregation\":{\"allSupport\":true,\"supportChannels\":[],\"notSupportChannels\":[]},\"dpDealGroupIdInt\":698116219,\"mtDealGroupIdInt\":698116219}",
    // DealGroupDTO.class);
    // DealCtx dealCtx = new DealCtx(new EnvCtx());
    // dealCtx.setDealGroupDTO(dto);
    // AvailableTimeHelper.getCustomAvailableTimeOfDays(dealCtx);
    // dto =
    // JSON.parseObject("{\"dpDealGroupId\":698116219,\"mtDealGroupId\":698116219,\"basic\":{\"categoryId\":318,\"title\":\"【白天专场】经典足道/中式保健二选一（免门票）\",\"brandName\":\"万泉河洗浴中心\",\"titleDesc\":\"仅售199元，价值399元【白天专场】经典足道/中式保健二选一（免门票）！\",\"beginSaleDate\":\"2021-05-11
    // 18:18:42\",\"endSaleDate\":\"2025-01-10
    // 00:00:00\",\"status\":1,\"saleChannel\":0,\"salePlatform\":3,\"sourceId\":102,\"tradeType\":3},\"image\":{\"defaultPicPath\":\"https://p0.meituan.net/dpmerchantpic/ed1e712965e8904048520ea89ee261ed77311.jpg\",\"allPicPaths\":\"https://p0.meituan.net/dpmerchantpic/ed1e712965e8904048520ea89ee261ed77311.jpg\"},\"category\":{\"categoryId\":304,\"serviceType\":\"店内服务\",\"serviceTypeId\":691},\"serviceProject\":{\"title\":\"团购详情\",\"salePrice\":\"199.00\",\"marketPrice\":\"399.00\",\"mustGroups\":[],\"optionGroups\":[{\"optionalCount\":1,\"groups\":[{\"skuId\":0,\"categoryId\":897,\"name\":\"经典足道（免门票）\",\"amount\":1,\"marketPrice\":\"399.0\",\"status\":10,\"attrs\":[{\"metaAttrId\":537,\"attrName\":\"content\",\"chnName\":\"项目描述\",\"attrValue\":\"赠送：水果、小吃、酸奶\\n使用时间：10:00-18:00\",\"rawAttrValue\":\"赠送：水果、小吃、酸奶\\n使用时间：10:00-18:00\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":642,\"attrName\":\"spuCategory\",\"chnName\":\"服务类型\",\"attrValue\":\"足疗\",\"rawAttrValue\":\"足疗\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":439913,\"attrName\":\"ServicePosition\",\"chnName\":\"服务部位\",\"attrValue\":\"足部\",\"rawAttrValue\":\"足部\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1390,\"attrName\":\"serviceDuration\",\"chnName\":\"服务时长\",\"attrValue\":\"70分钟\",\"rawAttrValue\":\"70\",\"unit\":\"分钟\",\"valueType\":500,\"sequence\":0}]},{\"skuId\":0,\"categoryId\":897,\"name\":\"中式保健\",\"amount\":1,\"marketPrice\":\"399.0\",\"status\":10,\"attrs\":[{\"metaAttrId\":537,\"attrName\":\"content\",\"chnName\":\"项目描述\",\"attrValue\":\"赠送：水果、小吃、酸奶\\n使用时间：10:00-18:00\",\"rawAttrValue\":\"赠送：水果、小吃、酸奶\\n使用时间：10:00-18:00\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":642,\"attrName\":\"spuCategory\",\"chnName\":\"服务类型\",\"attrValue\":\"推拿/按摩\",\"rawAttrValue\":\"推拿/按摩\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":439913,\"attrName\":\"ServicePosition\",\"chnName\":\"服务部位\",\"attrValue\":\"全身\",\"rawAttrValue\":\"全身\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1390,\"attrName\":\"serviceDuration\",\"chnName\":\"服务时长\",\"attrValue\":\"60分钟\",\"rawAttrValue\":\"60\",\"unit\":\"分钟\",\"valueType\":500,\"sequence\":0}]}]}],\"structType\":\"uniform-structure-table\"},\"channel\":{\"channelId\":3,\"channelEn\":\"joy\",\"channelCn\":\"休闲娱乐\",\"channelGroupId\":2,\"channelGroupEn\":\"general\",\"channelGroupCn\":\"到店综合\"},\"attrs\":[{\"name\":\"product_finish_date\",\"value\":[\"1970-01-01
    // 08:00:00\"],\"source\":0},{\"name\":\"calc_holiday_available\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_business_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_channel_id_allowed\",\"value\":[\"0\"],\"source\":0},{\"name\":\"product_can_use_coupon\",\"value\":[\"true\"],\"source\":0},{\"name\":\"preSaleTag\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_third_party_verify\",\"value\":[\"true\"],\"source\":0},{\"name\":\"product_block_stock\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_discount_rule_id\",\"value\":[\"0\"],\"source\":0},{\"name\":\"service_type\",\"value\":[\"店内服务\"],\"source\":0},{\"name\":\"reservation_is_needed_or_not\",\"value\":[\"否\"],\"source\":0},{\"name\":\"sys_deal_universal_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"category\",\"value\":[\"30\",\"3005\"],\"source\":0}],\"rule\":{\"buyRule\":{\"maxPerUser\":0,\"minPerUser\":1},\"useRule\":{\"receiptEffectiveDate\":{\"receiptDateType\":1,\"receiptValidDays\":30,\"receiptBeginDate\":\"2024-02-05
    // 14:37:03\",\"receiptEndDate\":\"2024-03-06
    // 23:59:59\",\"showText\":\"购买后30天内有效\"},\"availableDate\":{\"availableType\":0,\"cycleAvailableDateList\":[{\"availableDays\":[1],\"availableTimeRangePerDay\":[{\"from\":\"1970-01-01
    // 10:00:00\",\"to\":\"1970-01-01
    // 18:00:00\"}]},{\"availableDays\":[2],\"availableTimeRangePerDay\":[{\"from\":\"1970-01-01
    // 10:00:00\",\"to\":\"1970-01-01
    // 18:00:00\"}]},{\"availableDays\":[3],\"availableTimeRangePerDay\":[{\"from\":\"1970-01-01
    // 10:00:00\",\"to\":\"1970-01-01
    // 18:00:00\"}]},{\"availableDays\":[4],\"availableTimeRangePerDay\":[{\"from\":\"1970-01-01
    // 10:00:00\",\"to\":\"1970-01-01
    // 18:00:00\"}]},{\"availableDays\":[5],\"availableTimeRangePerDay\":[{\"from\":\"1970-01-01
    // 10:00:00\",\"to\":\"1970-01-01
    // 18:00:00\"}]},{\"availableDays\":[6],\"availableTimeRangePerDay\":[{\"from\":\"1970-01-01
    // 10:00:00\",\"to\":\"1970-01-01
    // 18:00:00\"}]},{\"availableDays\":[7],\"availableTimeRangePerDay\":[{\"from\":\"1970-01-01
    // 10:00:00\",\"to\":\"1970-01-01
    // 18:00:00\"}]}]}},\"refundRule\":{\"supportRefundType\":1,\"supportOverdueAutoRefund\":true}},\"displayShopInfo\":{\"dpDisplayShopIds\":[837252121],\"mtDisplayShopIds\":[837252121]},\"tags\":[{\"id\":100054242,\"tagName\":\"成人\"},{\"id\":100054246,\"tagName\":\"含按摩\"},{\"id\":100053227,\"tagName\":\"通用\"},{\"id\":100054241,\"tagName\":\"单人\"},{\"id\":100054247,\"tagName\":\"足疗\"}],\"customer\":{\"originCustomerId\":40417119},\"deals\":[{\"dealId\":765932650,\"basic\":{\"title\":\"【白天专场】经典足道/中式保健二选一（免门票）\",\"originTitle\":\"【白天专场】经典足道/中式保健二选一（免门票）\",\"thirdPartyId\":166,\"status\":1},\"price\":{\"salePrice\":\"199.00\",\"marketPrice\":\"399.00\",\"version\":7281292059},\"stock\":{\"dpSales\":82,\"dpTotal\":2000000,\"dpRemain\":1999918,\"mtSales\":275,\"mtTotal\":2000000,\"mtRemain\":1999725,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1,\"sharedSales\":0,\"sharedTotal\":0,\"sharedRemain\":0,\"isSharedSoldOut\":false},\"attrs\":[{\"name\":\"sku_receipt_type\",\"value\":[\"3\"],\"source\":0}],\"dealIdInt\":765932650}],\"price\":{\"salePrice\":\"199.00\",\"marketPrice\":\"399.00\",\"version\":7281292059},\"stock\":{\"dpSales\":82,\"dpTotal\":2000000,\"dpRemain\":1999918,\"mtSales\":275,\"mtTotal\":2000000,\"mtRemain\":1999725,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1},\"detail\":{\"dealGroupPics\":\"https://p0.meituan.net/dpmerchantpic/ed1e712965e8904048520ea89ee261ed77311.jpg\",\"images\":[\"https://p0.meituan.net/dpmerchantpic/ed1e712965e8904048520ea89ee261ed77311.jpg%40450w_280h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\"],\"importantPoint\":\"\",\"templateDetailDTOs\":[{\"title\":\"产品介绍\",\"content\":\"<p
    // class=\\\"listitem\\\"></p>\\n<p class=\\\"explain\\\"></p>\\n<div class=\\\"img\\\"><img
    // src=\\\"https://p0.meituan.net/dpmerchantpic/c2995089855c117a5a06a6a20f34d044374328.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\"
    // /></div>\\n<p class=\\\"listitem\\\"></p>\\n<p class=\\\"explain\\\"></p>\\n<div class=\\\"img\\\"><img
    // src=\\\"https://p1.meituan.net/dpmerchantpic/942a5f3859c3ccb6e6d805f025da662d1413249.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\"
    // /></div>\\n\\n\",\"type\":5,\"blockId\":0},{\"title\":\"团购详情\",\"content\":\" <div
    // class=\\\"detail-tit\\\"></div>\\n <div>\\n <table width=\\\"100%\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\"
    // class=\\\"detail-table\\\">\\n <thead>\\n <tr>\\n <th width=\\\"50%\\\">名称</th>\\n <th
    // width=\\\"25%\\\">数量</th>\\n <th width=\\\"25%\\\">单价</th>\\n </tr>\\n </thead>\\n <tbody>\\n <tr>\\n <th
    // colspan=\\\"3\\\">以下2选1</th>\\n </tr>\\n <tr>\\n <td>经典足道（免门票）</td>\\n <td class=\\\"tc\\\">1</td>\\n <td
    // class=\\\"tc\\\">399元</td>\\n </tr>\\n <tr>\\n <td>中式保健</td>\\n <td class=\\\"tc\\\">1</td>\\n <td
    // class=\\\"tc\\\">399元</td>\\n </tr>\\n <tr class=\\\"total\\\">\\n <td></td>\\n <td
    // class=\\\"tc\\\">总价<br><strong>团购价</strong></td>\\n <td class=\\\"tc\\\">399元<br><strong>199元</strong>\\n
    // </td>\\n </tr>\\n </tbody>\\n </table>\\n
    // </div>\\n<div><p>【包含】</p><p>白天专场，使用时间：10:00-18:00</p><p>经典足道70分钟/中式保健60分钟二选一；免单人门票；</p><p>赠送：水果、小吃、酸奶</p><p>温馨提示：以结账时间为准；</p><p>门票含洗浴，消毒毛巾，消毒拖鞋，洗发水，沐浴露，牙刷，牙膏，剃胡刀，桑拿，消毒浴巾，消毒浴服，休息大厅，wifi共计24小时。</p><p>【特别提示】</p><p>-
    // 无需预约，消费高峰可能需要等位；</p><p>- 进店先消费，离店时验证电子票；</p><p>- 儿童1.2米（不含）以下购买儿童浴资，1.2米（含）以上购买成人浴资；</p><p>-
    // 休息大厅过夜须携带本人有效身份证件并登记（一人一证）；</p><p>- 24:00—凌晨6:00在店加收过夜费50元/位；</p><p>- 提供免费的无线网，停车收费（详情咨询前台）；</p><p>-
    // 免费提供更衣柜、洗浴用品、浴巾浴服、拖鞋等不可带走；</p><p>-
    // 本票不能与店内其它优惠同时使用；</p></div>\\n\\n\",\"type\":1,\"blockId\":0},{\"title\":\"购买须知\",\"content\":\"<div
    // class=\\\"detail-box\\\">\\n <div class=\\\"purchase-notes\\\">\\n\\t\\t <dl>\\n <dt>有效期</dt>\\n <dd>\\n <p
    // class=\\\"listitem\\\">\\n 购买后30天内有效\\n </p>\\n </dd>\\n </dl>\\n <dl>\\n <dt>使用时间</dt>\\n <dd>\\n <p
    // class=\\\"listitem\\\">周一至周日 10:00-18:00可用</p>\\n </dd>\\n </dl>\\n <dl>\\n <dt>验证方式</dt>\\n <dd>\\n <p
    // class=\\\"listitem\\\">离店时验证团购券</p>\\n </dd>\\n </dl>\\n <dl>\\n <dt>预约信息</dt>\\n <dd>\\n <p
    // class=\\\"listitem\\\">无需预约，如遇消费高峰时段您可能需要排队</p>\\n </dd>\\n </dl>\\n <dl>\\n <dt>适用人数</dt>\\n <dd>\\n <p
    // class=\\\"listitem\\\">每张团购券最多1人使用</p>\\n </dd>\\n </dl>\\n <dl>\\n <dt>规则提醒</dt>\\n <dd>\\n <p
    // class=\\\"listitem\\\">需您当日一次性体验完毕所有项目</p>\\n <p class=\\\"listitem\\\">不再与其他优惠同享\\n</p>\\n </dd>\\n </dl>\\n
    // <dl>\\n <dt>温馨提示</dt>\\n <dd>\\n <p class=\\\"listitem\\\">如需团购券发票，请您在消费时向商户咨询</p>\\n <p
    // class=\\\"listitem\\\">为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！</p>\\n </dd>\\n </dl>\\n
    // </div>\\n</div>\\n\",\"type\":2,\"blockId\":0}]},\"saleChannelAggregation\":{\"allSupport\":true,\"supportChannels\":[],\"notSupportChannels\":[]},\"dpDealGroupIdInt\":698116219,\"mtDealGroupIdInt\":698116219}",
    // DealGroupDTO.class);
    // dealCtx.setDealGroupDTO(dto);
    // Assert.assertEquals("部分时段", AvailableTimeHelper.getCustomAvailableTimeOfDays(dealCtx));
    // }
}
