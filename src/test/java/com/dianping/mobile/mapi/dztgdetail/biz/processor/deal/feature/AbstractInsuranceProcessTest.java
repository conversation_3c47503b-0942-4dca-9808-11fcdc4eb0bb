package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.feature;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.feature.model.FeatureDetailDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.HaimaWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.LayerConfig;
import java.lang.reflect.Field;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

public class AbstractInsuranceProcessTest {

    @Mock
    private DealCtx ctx;

    @Mock
    private FeatureDetailDTO featureDetailDTO;

    @Mock
    private FeatureDetailDTO finalFeatureDetailDTO;

    @Mock
    private HaimaWrapper haimaWrapper;

    @Spy
    private AbstractInsuranceProcess abstractInsuranceProcess;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        abstractInsuranceProcess = spy(new AbstractInsuranceProcess() {

            @Override
            protected FeatureDetailDTO displayFeature(DealCtx ctx) {
                return featureDetailDTO;
            }

            @Override
            public void prepare(DealCtx ctx) {
                // No-op implementation for testing
            }
        });
        // Use reflection to inject the mocked haimaWrapper
        Field haimaWrapperField = AbstractInsuranceProcess.class.getDeclaredField("haimaWrapper");
        haimaWrapperField.setAccessible(true);
        haimaWrapperField.set(abstractInsuranceProcess, haimaWrapper);
    }

    /**
     * Test case when displayFeature returns null
     */
    @Test
    public void testProcessDisplayFeatureReturnsNull() throws Throwable {
        // arrange
        doReturn(null).when(abstractInsuranceProcess).displayFeature(ctx);
        // act
        abstractInsuranceProcess.process(ctx);
        // assert
        verify(ctx, never()).setFeatureDetailDTO(any());
    }

    /**
     * Test case when displayFeature returns a FeatureDetailDTO with null layerConfig
     */
    @Test
    public void testProcessDisplayFeatureReturnsFeatureDetailDTOWithNullLayerConfig() throws Throwable {
        // arrange
        doReturn(featureDetailDTO).when(abstractInsuranceProcess).displayFeature(ctx);
        when(featureDetailDTO.getLayerConfig()).thenReturn(null);
        // act
        abstractInsuranceProcess.process(ctx);
        // assert
        verify(ctx, never()).setFeatureDetailDTO(any());
    }

    /**
     * Test case when displayFeature returns a valid FeatureDetailDTO and assembleFeatureModule returns null
     */
    @Test
    public void testProcessAssembleFeatureModuleReturnsNull() throws Throwable {
        // arrange
        doReturn(featureDetailDTO).when(abstractInsuranceProcess).displayFeature(ctx);
        when(featureDetailDTO.getLayerConfig()).thenReturn(mock(LayerConfig.class));
        // Mock haimaWrapper behavior
        when(haimaWrapper.ifHitInsurance(ctx)).thenReturn(false);
        doReturn(null).when(abstractInsuranceProcess).assembleFeatureModule(ctx, featureDetailDTO);
        // act
        abstractInsuranceProcess.process(ctx);
        // assert
        verify(ctx, never()).setFeatureDetailDTO(any());
    }

    /**
     * Test case when displayFeature returns a valid FeatureDetailDTO and assembleFeatureModule returns a non-null FeatureDetailDTO
     */
    @Test
    public void testProcessAssembleFeatureModuleReturnsNonNullFeatureDetailDTO() throws Throwable {
        // arrange
        doReturn(featureDetailDTO).when(abstractInsuranceProcess).displayFeature(ctx);
        when(featureDetailDTO.getLayerConfig()).thenReturn(mock(LayerConfig.class));
        // Mock haimaWrapper behavior
        when(haimaWrapper.ifHitInsurance(ctx)).thenReturn(true);
        doReturn(finalFeatureDetailDTO).when(abstractInsuranceProcess).assembleFeatureModule(ctx, featureDetailDTO);
        // act
        abstractInsuranceProcess.process(ctx);
        // assert
        verify(ctx).setFeatureDetailDTO(finalFeatureDetailDTO);
    }
}
