package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.datatypes.MobileHeader;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DzDealMerchantInfoRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.Response;
import com.dianping.mobile.mapi.dztgdetail.facade.DealMerchantAdditionFacade;
import com.sankuai.meituan.charity.merchant.main.sdk.dto.response.DzProductDocResp;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import javax.servlet.http.HttpServletRequest;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

public class DealMerchantAdditionalActionTest {
    @Mock
    private IMobileContext iMobileContext;

    @Mock
    private DzDealMerchantInfoRequest request;

    @InjectMocks
    private DealMerchantAdditionalAction additionalAction;

    private MockedStatic<Lion> lionMockedStatic;

    @Mock
    private DealMerchantAdditionFacade merchantAdditionFacade;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        lionMockedStatic = mockStatic(Lion.class);
        when(request.getDealId()).thenReturn("123");
        when(request.getShopId()).thenReturn("456");
    }

    @After
    public void tearDown() {
        lionMockedStatic.close();
    }

    @Test
    public void testValidateShouldReturnNull() {
        lionMockedStatic.when(() -> Lion.getBoolean(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(Boolean.TRUE);
        IMobileResponse validate = additionalAction.validate(request, iMobileContext);
        Assert.assertNull(validate);
    }

    @Test
    public void testValidateShouldReturnError() {
        lionMockedStatic.when(() -> Lion.getBoolean(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(Boolean.FALSE);
        IMobileResponse validate = additionalAction.validate(request, iMobileContext);
        Assert.assertEquals(validate.getStatusCode().getCode(), 200);
    }

    @Test(expected = Exception.class)
    public void testExecuteInitEnvCtxException() {
        when(iMobileContext.getRequest()).thenThrow(new Exception());
        additionalAction.execute(request, iMobileContext);
    }

    @Test
    public void testExecuteShouldReturnNull() {
        DzProductDocResp dzProductDocResp = Mockito.mock(DzProductDocResp.class);
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        MobileHeader header = Mockito.mock(MobileHeader.class);

        when(dzProductDocResp.getSignCharity()).thenReturn(Boolean.TRUE);
        when(iMobileContext.getRequest()).thenReturn(request);
        when(request.getHeader(any())).thenReturn("");
        when(iMobileContext.getHeader()).thenReturn(header);
        when(iMobileContext.isMeituanClient()).thenReturn(true);
        when(merchantAdditionFacade.buildMerchantAddition(any(), any())).thenReturn(Response.createSuccessResponse(null));

        IMobileResponse execute = additionalAction.execute(this.request, iMobileContext);
        Assert.assertNull(execute.getData());
    }
}
