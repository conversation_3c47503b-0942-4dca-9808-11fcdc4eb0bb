package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.voucher.query.api.dto.VoucherDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import java.util.concurrent.FutureTask;
import com.dianping.ts.settle.common.api.util.SettleUtil;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.Arrays;

/**
 * CommissionWrapper单元测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class CommissionWrapperTest {

    private CommissionWrapper commissionWrapper = new CommissionWrapper();

    // 测试resolve方法，当dealCtx的commissionFuture为null时
    @Test
    public void testResolve_WhenCommissionFutureIsNull() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx dealCtx = new DealCtx(envCtx);
        commissionWrapper.resolve(dealCtx);
        assertNull("CommissionRate应为null", dealCtx.getCommissionRate());
    }

    // 测试resolve方法，当commissionFuture返回空列表时
    @Test
    public void testResolve_WhenCommissionFutureReturnsEmptyList() throws ExecutionException, InterruptedException {
        EnvCtx envCtx = new EnvCtx();
        DealCtx dealCtx = new DealCtx(envCtx);
        Future<List<VoucherDTO>> commissionFuture = mock(Future.class);
        when(commissionFuture.get()).thenReturn(Collections.emptyList());
        dealCtx.getFutureCtx().setCommissionFuture(commissionFuture);
        commissionWrapper.resolve(dealCtx);
        assertNull("CommissionRate应为null", dealCtx.getCommissionRate());
    }

    // 测试resolve方法，当commissionFuture返回的列表中VoucherDTO的dealPrice为0时
    @Test
    public void testResolve_WhenVoucherDealPriceIsZero() throws ExecutionException, InterruptedException {
        EnvCtx envCtx = new EnvCtx();
        DealCtx dealCtx = new DealCtx(envCtx);
        VoucherDTO voucherDTO = new VoucherDTO();
        voucherDTO.setDealPrice(BigDecimal.ZERO);
        Future<List<VoucherDTO>> commissionFuture = mock(Future.class);
        when(commissionFuture.get()).thenReturn(Collections.singletonList(voucherDTO));
        dealCtx.getFutureCtx().setCommissionFuture(commissionFuture);
        commissionWrapper.resolve(dealCtx);
        assertEquals("CommissionRate应为0", BigDecimal.ZERO, dealCtx.getCommissionRate());
    }
}
