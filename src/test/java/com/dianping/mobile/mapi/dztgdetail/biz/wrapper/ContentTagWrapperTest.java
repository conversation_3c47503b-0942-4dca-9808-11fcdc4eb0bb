package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import com.sankuai.mpmctcontent.query.thrift.api.meta.MetaInfoService;
import com.sankuai.mpmctcontent.query.thrift.dto.meta.BatchQueryTagValueRequestDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.meta.BatchQueryTagValueResponseDTO;
import java.util.ArrayList;
import java.util.Objects;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;

/**
 * @Author: <EMAIL>
 * @Date: 2024/8/18
 */
@RunWith(MockitoJUnitRunner.class)
public class ContentTagWrapperTest {

    @InjectMocks
    private ContentTagWrapper contentTagWrapper;

    @Mock
    private MetaInfoService metaInfoService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetContentTagFuture() {
        BatchQueryTagValueRequestDTO requestDTO = new BatchQueryTagValueRequestDTO();
        requestDTO.setEntityIdList(new ArrayList<>());
        Mockito.when((metaInfoService.batchQueryTagValueMetaInfo(Mockito.any()))).thenReturn(new BatchQueryTagValueResponseDTO());
        contentTagWrapper.getContentTagFuture(requestDTO);
        Assert.assertTrue(Objects.nonNull(requestDTO));
    }

    /**
     * Tests getContentTagFuture method under exception conditions.
     */
    @Test
    public void testGetContentTagFutureException() throws Throwable {
        // Arrange
        BatchQueryTagValueRequestDTO requestDTO = new BatchQueryTagValueRequestDTO();
        requestDTO.setEntityIdList(new ArrayList<>());
        when(metaInfoService.batchQueryTagValueMetaInfo(any(BatchQueryTagValueRequestDTO.class))).thenThrow(new RuntimeException());
        // Act
        Future future = contentTagWrapper.getContentTagFuture(requestDTO);
        // Assert
        Assert.assertNull(future);
    }
}
