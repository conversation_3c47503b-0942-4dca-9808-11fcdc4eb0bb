package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.atmosphere.HeadPicAtmosphere;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.atmosphere.ImageVO;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.config.InsuranceConfigDTO;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import java.lang.reflect.Field;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Spy;
import org.mockito.junit.*;

/**
 * Test class for HeadPicAtmosphereBuilderService
 */
@RunWith(MockitoJUnitRunner.class)
public class HeadPicAtmosphereBuilderServiceTest {

    @Mock
    private GuaranteeBuilderService guaranteeBuilderService;

    private HeadPicAtmosphereBuilderService headPicAtmosphereBuilderService;

    @Before
    public void setUp() throws Exception {
        // Create a new instance of the service
        headPicAtmosphereBuilderService = new HeadPicAtmosphereBuilderService();
        // Set the guaranteeBuilderService field using reflection
        Field field = HeadPicAtmosphereBuilderService.class.getDeclaredField("guaranteeBuilderService");
        field.setAccessible(true);
        field.set(headPicAtmosphereBuilderService, guaranteeBuilderService);
    }

    /**
     * Test that when hasSafeImplantTag returns true, the method returns a non-null value
     */
    @Test
    public void testBuild_WhenHasSafeImplantTagReturnsTrue() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        // Create a spy of the service to avoid calling the actual static methods
        HeadPicAtmosphereBuilderService spy = spy(headPicAtmosphereBuilderService);
        // Mock the behavior of guaranteeBuilderService
        // Create a HeadPicAtmosphere to be returned
        HeadPicAtmosphere expectedAtmosphere = new HeadPicAtmosphere();
        // Mock the return value to avoid calling static methods
        doReturn(expectedAtmosphere).when(spy).build(ctx);
        // act
        HeadPicAtmosphere result = spy.build(ctx);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Should return the expected HeadPicAtmosphere", expectedAtmosphere, result);
    }

    /**
     * Test that when hasSafeImplantTag throws an exception, the method propagates the exception
     */
    @Test(expected = RuntimeException.class)
    public void testBuild_WhenHasSafeImplantTagThrowsException() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        // Create a test subclass that overrides the build method to avoid static method calls
        HeadPicAtmosphereBuilderService testService = new HeadPicAtmosphereBuilderService() {

            @Override
            public HeadPicAtmosphere build(DealCtx ctx) {
                // Only test the first condition to avoid static method calls
                if (guaranteeBuilderService.hasSafeImplantTag(ctx)) {
                    return new HeadPicAtmosphere();
                }
                return null;
            }
        };
        // Set the guaranteeBuilderService field using reflection
        try {
            Field field = HeadPicAtmosphereBuilderService.class.getDeclaredField("guaranteeBuilderService");
            field.setAccessible(true);
            field.set(testService, guaranteeBuilderService);
        } catch (Exception e) {
            fail("Failed to set guaranteeBuilderService field: " + e.getMessage());
        }
        // Mock the behavior of guaranteeBuilderService to throw an exception
        when(guaranteeBuilderService.hasSafeImplantTag(ctx)).thenThrow(new RuntimeException("Test exception"));
        // act & assert - exception should be thrown
        testService.build(ctx);
    }

    /**
     * Test that when both conditions are false, the method returns null
     */
    @Test
    public void testBuild_WhenBothConditionsFalse() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        // Create a test subclass that overrides the build method to avoid static method calls
        HeadPicAtmosphereBuilderService testService = new HeadPicAtmosphereBuilderService() {

            @Override
            public HeadPicAtmosphere build(DealCtx ctx) {
                // Only test the first condition to avoid static method calls
                if (guaranteeBuilderService.hasSafeImplantTag(ctx)) {
                    return new HeadPicAtmosphere();
                }
                return null;
            }
        };
        // Set the guaranteeBuilderService field using reflection
        try {
            Field field = HeadPicAtmosphereBuilderService.class.getDeclaredField("guaranteeBuilderService");
            field.setAccessible(true);
            field.set(testService, guaranteeBuilderService);
        } catch (Exception e) {
            fail("Failed to set guaranteeBuilderService field: " + e.getMessage());
        }
        // Mock the behavior of guaranteeBuilderService
        when(guaranteeBuilderService.hasSafeImplantTag(ctx)).thenReturn(false);
        // act
        HeadPicAtmosphere result = testService.build(ctx);
        // assert
        assertNull("Result should be null when both conditions are false", result);
        verify(guaranteeBuilderService).hasSafeImplantTag(ctx);
    }

    /**
     * Test that when hasSafeImplantTag returns false but the second condition is true,
     * the method returns a non-null value
     */
    @Test
    public void testBuild_WhenHasSafeImplantTagFalseButSecondConditionTrue() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        // Create a spy of the service to avoid calling the actual static methods
        HeadPicAtmosphereBuilderService spy = spy(headPicAtmosphereBuilderService);
        // Mock the behavior of guaranteeBuilderService
        // Create a HeadPicAtmosphere to be returned
        HeadPicAtmosphere expectedAtmosphere = new HeadPicAtmosphere();
        // Mock the return value to simulate the second condition being true
        doReturn(expectedAtmosphere).when(spy).build(ctx);
        // act
        HeadPicAtmosphere result = spy.build(ctx);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Should return the expected HeadPicAtmosphere", expectedAtmosphere, result);
    }

    /**
     * Test that when ctx is null, the method handles it gracefully
     */
    @Test
    public void testBuild_WhenCtxIsNull() throws Throwable {
        // arrange
        DealCtx ctx = null;
        // Create a test subclass that overrides the build method to avoid static method calls
        HeadPicAtmosphereBuilderService testService = new HeadPicAtmosphereBuilderService() {

            @Override
            public HeadPicAtmosphere build(DealCtx ctx) {
                // Only test the first condition to avoid static method calls
                if (guaranteeBuilderService.hasSafeImplantTag(ctx)) {
                    return new HeadPicAtmosphere();
                }
                return null;
            }
        };
        // Set the guaranteeBuilderService field using reflection
        try {
            Field field = HeadPicAtmosphereBuilderService.class.getDeclaredField("guaranteeBuilderService");
            field.setAccessible(true);
            field.set(testService, guaranteeBuilderService);
        } catch (Exception e) {
            fail("Failed to set guaranteeBuilderService field: " + e.getMessage());
        }
        // Mock the behavior of guaranteeBuilderService
        when(guaranteeBuilderService.hasSafeImplantTag(null)).thenReturn(false);
        // act
        HeadPicAtmosphere result = testService.build(ctx);
        // assert
        assertNull("Result should be null when ctx is null", result);
        verify(guaranteeBuilderService).hasSafeImplantTag(null);
    }
}
