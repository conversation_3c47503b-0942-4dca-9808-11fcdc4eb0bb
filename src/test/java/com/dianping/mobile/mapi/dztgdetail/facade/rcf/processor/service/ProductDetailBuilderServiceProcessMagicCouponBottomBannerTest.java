package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBanner;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.action.extend.buy.SuckBottomBuyActionVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.action.extend.none.SuckBottomDoNothingActionVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.action.extend.redirect.SuckBottomRedirectActionVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.banner.BottomBarTopBannerVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.enums.BottomBarActionTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.backgroud.BottomBarBackgroundVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.rich.content.RichContentVO;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ProductDetailBuilderServiceProcessMagicCouponBottomBannerTest {

    private Method processMagicCouponBottomBannerMethod;

    @InjectMocks
    private ProductDetailBuilderService productDetailBuilderService;

    @Before
    public void setUp() throws Exception {
        processMagicCouponBottomBannerMethod = ProductDetailBuilderService.class.getDeclaredMethod("processMagicCouponBottomBanner", BottomBarTopBannerVO.class, DealGroupPBO.class);
        processMagicCouponBottomBannerMethod.setAccessible(true);
    }

    private void invokePrivateMethod(BottomBarTopBannerVO barTopBannerVO, DealGroupPBO result) throws Exception {
        try {
            processMagicCouponBottomBannerMethod.invoke(productDetailBuilderService, barTopBannerVO, result);
        } catch (Exception e) {
            if (e.getCause() instanceof NullPointerException) {
                // 预期行为，不做处理
                return;
            }
            throw e;
        }
    }

    /**
     * 测试用例：Result对象为null但barTopBannerVO有有效数据
     * 预期：方法应处理null结果而不修改输入对象
     */
    @Test
    public void testProcessMagicCouponBottomBanner_ResultNull() throws Throwable {
        BottomBarTopBannerVO barTopBannerVO = new BottomBarTopBannerVO();
        BottomBarBackgroundVO background = new BottomBarBackgroundVO();
        background.setColors(Arrays.asList("color1", "color2"));
        barTopBannerVO.setBackground(background);
        SuckBottomRedirectActionVO actionData = new SuckBottomRedirectActionVO();
        actionData.setActionType(BottomBarActionTypeEnum.FLOATING_LAYER.getCode());
        actionData.setUrl("test-url");
        barTopBannerVO.setActionData(actionData);
        invokePrivateMethod(barTopBannerVO, null);
        assertNotNull("背景不应为null", barTopBannerVO.getBackground());
        assertNotNull("动作数据不应为null", barTopBannerVO.getActionData());
        assertEquals("背景颜色应保持不变", Arrays.asList("color1", "color2"), barTopBannerVO.getBackground().getColors());
    }

    /**
     * 测试用例：result.getBuyBar()返回null但其他数据有效
     * 预期：方法应处理null buyBar而不修改输入对象
     */
    @Test
    public void testProcessMagicCouponBottomBanner_BuyBarNull() throws Throwable {
        BottomBarTopBannerVO barTopBannerVO = new BottomBarTopBannerVO();
        BottomBarBackgroundVO background = new BottomBarBackgroundVO();
        background.setColors(Arrays.asList("color1", "color2"));
        barTopBannerVO.setBackground(background);
        SuckBottomRedirectActionVO actionData = new SuckBottomRedirectActionVO();
        actionData.setActionType(BottomBarActionTypeEnum.FLOATING_LAYER.getCode());
        actionData.setUrl("test-url");
        barTopBannerVO.setActionData(actionData);
        DealGroupPBO result = new DealGroupPBO();
        result.setBuyBar(null);
        invokePrivateMethod(barTopBannerVO, result);
        assertNotNull("结果对象不应为null", result);
        assertNull("BuyBar应保持为null", result.getBuyBar());
        assertNotNull("背景不应为null", barTopBannerVO.getBackground());
        assertEquals("背景颜色应保持不变", Arrays.asList("color1", "color2"), barTopBannerVO.getBackground().getColors());
    }

    /**
     * 测试用例：有效FLOATING_LAYER动作类型
     * 预期：正确创建并设置DealBuyBanner属性
     */
    @Test
    public void testProcessMagicCouponBottomBanner_FloatingLayer() throws Throwable {
        BottomBarTopBannerVO barTopBannerVO = new BottomBarTopBannerVO();
        BottomBarBackgroundVO background = new BottomBarBackgroundVO();
        background.setColors(Arrays.asList("color1", "color2"));
        barTopBannerVO.setBackground(background);
        SuckBottomRedirectActionVO actionData = new SuckBottomRedirectActionVO();
        actionData.setActionType(BottomBarActionTypeEnum.FLOATING_LAYER.getCode());
        actionData.setUrl("test-url");
        actionData.setText("test-text");
        actionData.setIcon("test-icon");
        barTopBannerVO.setActionData(actionData);
        DealGroupPBO result = new DealGroupPBO();
        result.setBuyBar(new DealBuyBar(1, new ArrayList<>()));
        invokePrivateMethod(barTopBannerVO, result);
        DealBuyBanner banner = result.getBuyBar().getBuyBanner();
        assertNotNull("DealBuyBanner不应为null", banner);
        assertEquals("动作类型应匹配", actionData.getActionType(), banner.getLeadAction());
        assertEquals("URL应匹配", actionData.getUrl(), banner.getLeadUrl());
        assertEquals("文本应匹配", actionData.getText(), banner.getLeadText());
        assertEquals("文本颜色应为#555555", "#555555", banner.getLeadTextColor());
        assertEquals("图标URL应匹配", actionData.getIcon(), banner.getIconUrl());
    }

    /**
     * 测试用例：有效SUCK_BOTTOM_NOTHING动作类型
     * 预期：正确创建并设置DealBuyBanner属性
     */
    @Test
    public void testProcessMagicCouponBottomBanner_SuckBottomNothing() throws Throwable {
        BottomBarTopBannerVO barTopBannerVO = new BottomBarTopBannerVO();
        BottomBarBackgroundVO background = new BottomBarBackgroundVO();
        background.setColors(Arrays.asList("color1", "color2"));
        barTopBannerVO.setBackground(background);
        SuckBottomDoNothingActionVO actionData = new SuckBottomDoNothingActionVO();
        actionData.setIcon("nothing-icon");
        barTopBannerVO.setActionData(actionData);
        DealGroupPBO result = new DealGroupPBO();
        result.setBuyBar(new DealBuyBar(1, new ArrayList<>()));
        invokePrivateMethod(barTopBannerVO, result);
        DealBuyBanner banner = result.getBuyBar().getBuyBanner();
        assertNotNull("DealBuyBanner不应为null", banner);
        assertEquals("动作类型应为NOTHING", BottomBarActionTypeEnum.NOTHING.getCode(), banner.getLeadAction());
        assertEquals("文本颜色应为#555555", "#555555", banner.getLeadTextColor());
        assertEquals("图标URL应匹配", actionData.getIcon(), banner.getIconUrl());
    }

    /**
     * 测试用例：有效SUCK_BOTTOM_BUY动作类型
     * 预期：正确创建并设置DealBuyBanner属性
     */
    @Test
    public void testProcessMagicCouponBottomBanner_SuckBottomBuy() throws Throwable {
        BottomBarTopBannerVO barTopBannerVO = new BottomBarTopBannerVO();
        BottomBarBackgroundVO background = new BottomBarBackgroundVO();
        background.setColors(Arrays.asList("color1", "color2"));
        barTopBannerVO.setBackground(background);
        SuckBottomBuyActionVO actionData = new SuckBottomBuyActionVO();
        actionData.setUrl("buy-url");
        actionData.setText("buy-text");
        actionData.setIcon("buy-icon");
        barTopBannerVO.setActionData(actionData);
        DealGroupPBO result = new DealGroupPBO();
        result.setBuyBar(new DealBuyBar(1, new ArrayList<>()));
        invokePrivateMethod(barTopBannerVO, result);
        DealBuyBanner banner = result.getBuyBar().getBuyBanner();
        assertNotNull("DealBuyBanner不应为null", banner);
        assertEquals("动作类型应为BUY", BottomBarActionTypeEnum.BUY.getCode(), banner.getLeadAction());
        assertEquals("URL应匹配", actionData.getUrl(), banner.getLeadUrl());
        assertEquals("文本应匹配", actionData.getText(), banner.getLeadText());
        assertEquals("文本颜色应为#555555", "#555555", banner.getLeadTextColor());
        assertEquals("图标URL应匹配", actionData.getIcon(), banner.getIconUrl());
    }

    /**
     * 测试用例：FLOATING_LAYER动作类型但actionData为null
     * 预期：不创建DealBuyBanner
     */
    @Test
    public void testProcessMagicCouponBottomBanner_FloatingLayer_NullActionData() throws Throwable {
        BottomBarTopBannerVO barTopBannerVO = new BottomBarTopBannerVO();
        BottomBarBackgroundVO background = new BottomBarBackgroundVO();
        background.setColors(Arrays.asList("color1", "color2"));
        barTopBannerVO.setBackground(background);
        barTopBannerVO.setActionData(null);
        DealGroupPBO result = new DealGroupPBO();
        result.setBuyBar(new DealBuyBar(1, new ArrayList<>()));
        invokePrivateMethod(barTopBannerVO, result);
        assertNull("DealBuyBanner应为null", result.getBuyBar().getBuyBanner());
    }

    /**
     * 测试用例：SUCK_BOTTOM_NOTHING动作类型但actionData为null
     * 预期：不创建DealBuyBanner
     */
    @Test
    public void testProcessMagicCouponBottomBanner_SuckBottomNothing_NullActionData() throws Throwable {
        BottomBarTopBannerVO barTopBannerVO = new BottomBarTopBannerVO();
        BottomBarBackgroundVO background = new BottomBarBackgroundVO();
        background.setColors(Arrays.asList("color1", "color2"));
        barTopBannerVO.setBackground(background);
        barTopBannerVO.setActionData(null);
        DealGroupPBO result = new DealGroupPBO();
        result.setBuyBar(new DealBuyBar(1, new ArrayList<>()));
        invokePrivateMethod(barTopBannerVO, result);
        assertNull("DealBuyBanner应为null", result.getBuyBar().getBuyBanner());
    }

    /**
     * 测试用例：SUCK_BOTTOM_BUY动作类型但actionData为null
     * 预期：不创建DealBuyBanner
     */
    @Test
    public void testProcessMagicCouponBottomBanner_SuckBottomBuy_NullActionData() throws Throwable {
        BottomBarTopBannerVO barTopBannerVO = new BottomBarTopBannerVO();
        BottomBarBackgroundVO background = new BottomBarBackgroundVO();
        background.setColors(Arrays.asList("color1", "color2"));
        barTopBannerVO.setBackground(background);
        barTopBannerVO.setActionData(null);
        DealGroupPBO result = new DealGroupPBO();
        result.setBuyBar(new DealBuyBar(1, new ArrayList<>()));
        invokePrivateMethod(barTopBannerVO, result);
        assertNull("DealBuyBanner应为null", result.getBuyBar().getBuyBanner());
    }

    /**
     * 测试用例：无效动作类型
     * 预期：不创建DealBuyBanner
     */
    @Test
    public void testProcessMagicCouponBottomBanner_InvalidActionType() throws Throwable {
        BottomBarTopBannerVO barTopBannerVO = new BottomBarTopBannerVO();
        BottomBarBackgroundVO background = new BottomBarBackgroundVO();
        background.setColors(Arrays.asList("color1", "color2"));
        barTopBannerVO.setBackground(background);
        SuckBottomRedirectActionVO actionData = new SuckBottomRedirectActionVO();
        // 无效动作类型
        actionData.setActionType(-1);
        barTopBannerVO.setActionData(actionData);
        DealGroupPBO result = new DealGroupPBO();
        result.setBuyBar(new DealBuyBar(1, new ArrayList<>()));
        invokePrivateMethod(barTopBannerVO, result);
        assertNull("DealBuyBanner应为null", result.getBuyBar().getBuyBanner());
    }
}
