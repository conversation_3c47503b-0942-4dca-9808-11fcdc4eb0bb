package com.dianping.mobile.mapi.dztgdetail.biz.processor.mtlive;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PrivateLiveWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.common.ResponseDTO;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.LiveRoomDetail;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.LiveRoomDistributionInfo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.Future;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLivePassParamProcessorTest {

    @InjectMocks
    private PrivateLivePassParamProcessor privateLivePassParamProcessor;

    @Mock
    private PrivateLiveWrapper privateLiveWrapper;

    private DealCtx ctx;

    private DealBaseReq req;

    @Mock
    private Future future;

    private FutureCtx futureCtx;

    @Before
    public void setUp() {
        ctx = new DealCtx(null);
        req = new DealBaseReq();
        req.setPrivateLiveId("123");
        ctx.setDealBaseReq(req);
        futureCtx = new FutureCtx();
        ctx.setFutureCtx(futureCtx);
    }

    @Test
    public void testPrepareWhenDealBaseReqIsNotNull() {
        when(privateLiveWrapper.preQueryLiveDistributionInfo(req.getPrivateLiveId())).thenReturn(future);
        privateLivePassParamProcessor.prepare(ctx);
        assertNotNull(ctx.getFutureCtx().getPrivateLiveDistributionInfoFuture());
    }

    @Test
    public void testIsEnableWhenIsMtLiveMinAppReturnsTrue() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_LIVE_WEIXIN_MINIAPP);
        ctx.setEnvCtx(envCtx);
        boolean result = privateLivePassParamProcessor.isEnable(ctx);
        assertTrue(result);
    }

    @Test
    public void testIsEnableWhenIsMtLiveMinAppReturnsFalse() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        ctx.setEnvCtx(envCtx);
        boolean result = privateLivePassParamProcessor.isEnable(ctx);
        assertFalse(result);
    }

    @Test
    public void testPrepareWhenDealBaseReqIsNull() {
        ctx.setDealBaseReq(null);
        privateLivePassParamProcessor.prepare(ctx);
        assertNull(ctx.getDealBaseReq());
    }

    @Test
    public void testProcessWhenLiveRoomDetailIsNotNull() {
        futureCtx.setPrivateLiveDistributionInfoFuture(future);
        ResponseDTO<LiveRoomDistributionInfo> responseDTO  = new ResponseDTO<LiveRoomDistributionInfo>();
        responseDTO.setData(new LiveRoomDistributionInfo());
        when(privateLiveWrapper.getFutureResult(future)).thenReturn(responseDTO);
        privateLivePassParamProcessor.process(ctx);
        assertNotNull(ctx.getLiveRoomDistributionInfo());
    }
}
