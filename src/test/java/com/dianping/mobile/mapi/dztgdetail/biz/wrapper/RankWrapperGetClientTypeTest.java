package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.sankuai.mdp.dzshoplist.rank.api.enums.ClientTypeEnum;
import java.lang.reflect.Method;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

public class RankWrapperGetClientTypeTest {

    private RankWrapper rankWrapper = new RankWrapper();

    private Integer invokePrivateMethod(DztgClientTypeEnum dztgClientTypeEnum) throws Exception {
        Method method = RankWrapper.class.getDeclaredMethod("getClientType", DztgClientTypeEnum.class);
        method.setAccessible(true);
        return (Integer) method.invoke(rankWrapper, dztgClientTypeEnum);
    }

    @Test
    public void testGetClientTypeWhenDztgClientTypeEnumIsMeituanApp() throws Throwable {
        DztgClientTypeEnum dztgClientTypeEnum = DztgClientTypeEnum.MEITUAN_APP;
        Integer result = invokePrivateMethod(dztgClientTypeEnum);
        Assert.assertEquals(ClientTypeEnum.APP.getType(), result.intValue());
    }

    @Test
    public void testGetClientTypeWhenDztgClientTypeEnumIsDianpingApp() throws Throwable {
        DztgClientTypeEnum dztgClientTypeEnum = DztgClientTypeEnum.DIANPING_APP;
        Integer result = invokePrivateMethod(dztgClientTypeEnum);
        Assert.assertEquals(ClientTypeEnum.APP.getType(), result.intValue());
    }

    @Test
    public void testGetClientTypeWhenDztgClientTypeEnumIsMeituanWeixinMiniApp() throws Throwable {
        DztgClientTypeEnum dztgClientTypeEnum = DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP;
        Integer result = invokePrivateMethod(dztgClientTypeEnum);
        Assert.assertEquals(ClientTypeEnum.WX_XCX.getType(), result.intValue());
    }

    @Test
    public void testGetClientTypeWhenDztgClientTypeEnumIsDianpingWeixinMiniApp() throws Throwable {
        DztgClientTypeEnum dztgClientTypeEnum = DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP;
        Integer result = invokePrivateMethod(dztgClientTypeEnum);
        Assert.assertEquals(ClientTypeEnum.WX_XCX.getType(), result.intValue());
    }

    @Test
    public void testGetClientTypeWhenDztgClientTypeEnumIsOther() throws Throwable {
        DztgClientTypeEnum dztgClientTypeEnum = DztgClientTypeEnum.UNKNOWN;
        Integer result = invokePrivateMethod(dztgClientTypeEnum);
        Assert.assertEquals(ClientTypeEnum.H5.getType(), result.intValue());
    }
}
