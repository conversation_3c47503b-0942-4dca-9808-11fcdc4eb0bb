package com.dianping.mobile.mapi.dztgdetail.biz.processor.headerpic;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ContentType;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

public class DefaultHeaderPicProcessorFillPicScaleTest {

    private DefaultHeaderPicProcessor processor;

    @Mock
    private LionConfigUtils lionConfigUtils;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        processor = new DefaultHeaderPicProcessor();
    }

    /**
     * Test when the result list is empty.
     */
    @Test
    public void testFillPicScaleEmptyResultList() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        List<ContentPBO> result = new ArrayList<>();
        DealGroupPBO dealGroupPBO = new DealGroupPBO();
        // act
        processor.fillPicScale(ctx, result, dealGroupPBO);
        // assert
        assertEquals(0, result.size());
    }

    /**
     * Test when the result list contains no PIC or VIDEO types.
     */
    @Test
    public void testFillPicScaleNoPicOrVideoTypes() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        List<ContentPBO> result = new ArrayList<>();
        result.add(new ContentPBO(ContentType.TEXT.getType(), "Text Content"));
        DealGroupPBO dealGroupPBO = new DealGroupPBO();
        // act
        processor.fillPicScale(ctx, result, dealGroupPBO);
        // assert
        assertEquals(1, result.size());
        assertEquals(null, result.get(0).getScale());
    }
}
