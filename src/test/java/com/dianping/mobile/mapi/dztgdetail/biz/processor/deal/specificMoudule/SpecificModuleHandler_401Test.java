package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule;

import static com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.HealthCertificateExaminerHandler.ATTR_PHYSICAL_EXAMINATION_GET_RESULT_TIME;
import static com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.HealthCertificateExaminerHandler.ATTR_PHYSICAL_EXAMINATION_REPORT_TYPE;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anySetOf;
import static org.mockito.Mockito.*;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.HealthCertificateExaminerHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule.SpecificModuleCtx;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule.SpecificModuleHandler_401;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailDisplayUnitVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailSpecificModuleVO;
import com.dianping.mobile.mapi.dztgdetail.entity.PhysicalExamCheckItemDetailConfig;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.lang.reflect.Method;
import java.util.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class SpecificModuleHandler_401Test {

    @InjectMocks
    private SpecificModuleHandler_401 specificModuleHandler_401;

    @Mock
    private SpecificModuleCtx specificModuleCtx;

    @Mock
    private DealGroupDTO dealGroupDTO;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Mock
    private HealthCertificateExaminerHandler healthCertificateExaminerHandler;

    @Mock
    private Map<Long, List<SkuAttrItemDto>> productCategoryId2SkuAttrMap;

    @Mock
    private Map<Long, PhysicalExamCheckItemDetailConfig> tagId2DetailConfig;

    @Before
    public void setUp() {
        when(specificModuleCtx.getDpDealGroupId()).thenReturn(1);
    }

    private List<String> invokePrivateMethod(long tagId, Map<Long, List<SkuAttrItemDto>> productCategoryId2SkuAttrMap, Map<Long, PhysicalExamCheckItemDetailConfig> tagId2DetailConfig) throws Exception {
        Method method = SpecificModuleHandler_401.class.getDeclaredMethod("getCheckItemValuesByTagId", long.class, Map.class, Map.class);
        method.setAccessible(true);
        return (List<String>) method.invoke(specificModuleHandler_401, tagId, productCategoryId2SkuAttrMap, tagId2DetailConfig);
    }

    private void setUpCommonMocks() {
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        PhysicalExamCheckItemDetailConfig detailConfig = new PhysicalExamCheckItemDetailConfig();
        Map<String, List<String>> productCategoryId2DetailListMap = new HashMap<>();
        List<String> detailList = new ArrayList<>();
        detailList.add("configValue");
        productCategoryId2DetailListMap.put("1", detailList);
        detailConfig.setProductCategoryId2DetailListMap(productCategoryId2DetailListMap);
    }

    /**
     * 测试buildHealthCertificateModule方法，attrName为CERTIFICATE_TIME
     */
    @Test
    public void testBuildHealthCertificateModuleCertificateType() throws Throwable {
        // arrange
        String attrName = SpecificModuleHandler_401.CERTIFICATE_TIME;
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        when(specificModuleHandler_401.queryDealInfo(specificModuleCtx)).thenReturn(dealGroupDTO).thenThrow(new RuntimeException());
        when(healthCertificateExaminerHandler.initReportTypes(dealGroupDTO.getAttrs(), ATTR_PHYSICAL_EXAMINATION_REPORT_TYPE)).thenReturn(Optional.of(Lists.newArrayList("纸质报告")));
        // act
        DealDetailSpecificModuleVO result = (DealDetailSpecificModuleVO) ReflectionTestUtils.invokeMethod(specificModuleHandler_401, "buildHealthCertificateModule", specificModuleCtx, attrName);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getUnits().size());
        assertEquals("出证时间", result.getUnits().get(0).getTitle());
    }

    /**
     * 测试buildHealthCertificateModule方法，CERTIFICATE_MODE
     */
    @Test
    public void testBuildHealthCertificateModuleCertificateTime() throws Throwable {
        // arrange
        // arrange
        String attrName = SpecificModuleHandler_401.CERTIFICATE_MODE;
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        when(specificModuleHandler_401.queryDealInfo(specificModuleCtx)).thenReturn(dealGroupDTO).thenThrow(new RuntimeException());
        when(healthCertificateExaminerHandler.initReportTypes(dealGroupDTO.getAttrs(), ATTR_PHYSICAL_EXAMINATION_REPORT_TYPE)).thenReturn(Optional.of(Lists.newArrayList("纸质报告")));
        // act
        DealDetailSpecificModuleVO result = (DealDetailSpecificModuleVO) ReflectionTestUtils.invokeMethod(specificModuleHandler_401, "buildHealthCertificateModule", specificModuleCtx, attrName);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getUnits().size());
        assertEquals("出证方式", result.getUnits().get(0).getTitle());
    }

    /**
     * 测试 buildModuleVOAndFillItems 方法，传入一个 DealDetailDisplayUnitVO 对象，返回一个 DealDetailSpecificModuleVO 对象
     */
    @Test
    public void testBuildModuleVOAndFillItems() throws Throwable {
        // arrange
        SpecificModuleHandler_401 specificModuleHandler_401 = new SpecificModuleHandler_401();
        DealDetailDisplayUnitVO unit = new DealDetailDisplayUnitVO();
        // Use reflection to access the private method
        Method method = SpecificModuleHandler_401.class.getDeclaredMethod("buildModuleVOAndFillItems", DealDetailDisplayUnitVO.class);
        method.setAccessible(true);
        // act
        DealDetailSpecificModuleVO result = (DealDetailSpecificModuleVO) method.invoke(specificModuleHandler_401, unit);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getUnits().size());
        assertEquals(unit, result.getUnits().get(0));
    }

    @Test
    public void testGetCheckItemValuesByTagId_NoDetailConfig() throws Throwable {
        when(tagId2DetailConfig.get(anyLong())).thenReturn(null);
        List<String> result = invokePrivateMethod(1L, productCategoryId2SkuAttrMap, tagId2DetailConfig);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetCheckItemValuesByTagId_NoAttrItemDtos() throws Throwable {
        when(tagId2DetailConfig.get(anyLong())).thenReturn(new PhysicalExamCheckItemDetailConfig());
        List<String> result = invokePrivateMethod(1L, productCategoryId2SkuAttrMap, tagId2DetailConfig);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetCheckItemValuesByTagId_NoConfigValue() throws Throwable {
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrValue("otherValue");
        when(tagId2DetailConfig.get(anyLong())).thenReturn(new PhysicalExamCheckItemDetailConfig());
        List<String> result = invokePrivateMethod(1L, productCategoryId2SkuAttrMap, tagId2DetailConfig);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetCheckItemValuesByTagId_ConfigValueNotInCurrentValueList() throws Throwable {
        setUpCommonMocks();
        List<String> result = invokePrivateMethod(1L, productCategoryId2SkuAttrMap, tagId2DetailConfig);
        assertEquals(0, result.size());
    }
}
