package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealPriceTrendVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealTinyInfoVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PriceTrendVO;
import com.sankuai.dealuser.price.display.api.enums.PricePowerTagEnum;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class DealTinyInfoFacadeConvertTinyDealToPriceTrendTest {

    private DealTinyInfoFacade dealTinyInfoFacade = new DealTinyInfoFacade();

    private List<PriceTrendVO> invokePrivateMethod(String methodName, List<PriceTrendVO> dealPriceTrend, Date fewDaysBeforeDate) throws Exception {
        java.lang.reflect.Method method = DealTinyInfoFacade.class.getDeclaredMethod(methodName, List.class, Date.class);
        method.setAccessible(true);
        return (List<PriceTrendVO>) method.invoke(dealTinyInfoFacade, dealPriceTrend, fewDaysBeforeDate);
    }

    private int invokePrivateMethod(String methodName, String pricePowerTag) throws Exception {
        Method method = DealTinyInfoFacade.class.getDeclaredMethod(methodName, String.class);
        method.setAccessible(true);
        return (int) method.invoke(dealTinyInfoFacade, pricePowerTag);
    }

    private BigDecimal invokeCalcDiscount(BigDecimal finalPrice, BigDecimal marketPrice) throws Exception {
        Method method = DealTinyInfoFacade.class.getDeclaredMethod("calcDiscount", BigDecimal.class, BigDecimal.class);
        method.setAccessible(true);
        return (BigDecimal) method.invoke(dealTinyInfoFacade, finalPrice, marketPrice);
    }

    /**
     * 测试convertTinyDealToPriceTrend方法，当DealTinyInfoVO对象为null时，应返回null
     */
    @Test
    public void testConvertTinyDealToPriceTrendWhenTinyDealIsNull() throws Throwable {
        // arrange
        DealTinyInfoVO tinyDeal = null;
        // act
        Method method = DealTinyInfoFacade.class.getDeclaredMethod("convertTinyDealToPriceTrend", DealTinyInfoVO.class);
        method.setAccessible(true);
        DealPriceTrendVO result = (DealPriceTrendVO) method.invoke(dealTinyInfoFacade, tinyDeal);
        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试convertTinyDealToPriceTrend方法，当DealTinyInfoVO对象不为null时，应返回一个DealPriceTrendVO对象，其属性值与DealTinyInfoVO对象的属性值相同
     */
    @Test
    public void testConvertTinyDealToPriceTrendWhenTinyDealIsNotNull() throws Throwable {
        // arrange
        DealTinyInfoVO tinyDeal = new DealTinyInfoVO();
        tinyDeal.setDealGroupId(1);
        tinyDeal.setTitle("title");
        tinyDeal.setSubTitle("subTitle");
        tinyDeal.setSaleTag("saleTag");
        tinyDeal.setHeadPic("headPic");
        tinyDeal.setPricePowerTag(Arrays.asList("pricePowerTag1", "pricePowerTag2"));
        tinyDeal.setDiscount("discount");
        tinyDeal.setFinalPrice("finalPrice");
        tinyDeal.setMarketPrice("marketPrice");
        // act
        Method method = DealTinyInfoFacade.class.getDeclaredMethod("convertTinyDealToPriceTrend", DealTinyInfoVO.class);
        method.setAccessible(true);
        DealPriceTrendVO result = (DealPriceTrendVO) method.invoke(dealTinyInfoFacade, tinyDeal);
        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(tinyDeal.getDealGroupId(), result.getDealGroupId());
        Assert.assertEquals(tinyDeal.getTitle(), result.getTitle());
        Assert.assertEquals(tinyDeal.getSubTitle(), result.getSubTitle());
        Assert.assertEquals(tinyDeal.getSaleTag(), result.getSaleTag());
        Assert.assertEquals(tinyDeal.getHeadPic(), result.getHeadPic());
        Assert.assertEquals(tinyDeal.getPricePowerTag(), result.getPricePowerTag());
        Assert.assertEquals(tinyDeal.getDiscount(), result.getDiscount());
        Assert.assertEquals(tinyDeal.getFinalPrice(), result.getFinalPrice());
        Assert.assertEquals(tinyDeal.getMarketPrice(), result.getMarketPrice());
    }

    // Other test cases remain unchanged
    @Test
    public void testGetPriceTrendsWhenFirstElementDateIsBeforeFewDaysBeforeDate() throws Throwable {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date fewDaysBeforeDate = sdf.parse("2022-01-01");
        PriceTrendVO priceTrendVO1 = new PriceTrendVO("2021-12-31", new BigDecimal("100"));
        PriceTrendVO priceTrendVO2 = new PriceTrendVO("2022-01-02", new BigDecimal("200"));
        List<PriceTrendVO> dealPriceTrend = new ArrayList<>(Arrays.asList(priceTrendVO1, priceTrendVO2));
        List<PriceTrendVO> result = invokePrivateMethod("getPriceTrends", dealPriceTrend, fewDaysBeforeDate);
        // Expected size is 2
        assertEquals(2, result.size());
        assertEquals("2022-01-01", result.get(0).getDate());
        // Price should be from the next element
        assertEquals("200", result.get(0).getPrice().toString());
        assertEquals("2022-01-02", result.get(1).getDate());
        assertEquals("200", result.get(1).getPrice().toString());
    }

    @Test
    public void testGetPriceTrendsWhenDealPriceTrendIsEmpty() throws Throwable {
        List<PriceTrendVO> result = invokePrivateMethod("getPriceTrends", new ArrayList<>(), new Date());
        assertNull(result);
    }

    @Test
    public void testGetPricePowerTagDayWhenPricePowerTagIsLowestPriceInRecent30Days() throws Throwable {
        String pricePowerTag = PricePowerTagEnum.LOWEST_PRICE_IN_RECENT_30_DAYS.getDesc();
        int result = invokePrivateMethod("getPricePowerTagDay", pricePowerTag);
        Assert.assertEquals(30, result);
    }

    @Test
    public void testGetPricePowerTagDayWhenPricePowerTagIsLowestPriceInRecent60Days() throws Throwable {
        String pricePowerTag = PricePowerTagEnum.LOWEST_PRICE_IN_RECENT_60_DAYS.getDesc();
        int result = invokePrivateMethod("getPricePowerTagDay", pricePowerTag);
        Assert.assertEquals(60, result);
    }

    @Test
    public void testGetPricePowerTagDayWhenPricePowerTagIsLowestPriceInRecent90Days() throws Throwable {
        String pricePowerTag = PricePowerTagEnum.LOWEST_PRICE_IN_RECENT_90_DAYS.getDesc();
        int result = invokePrivateMethod("getPricePowerTagDay", pricePowerTag);
        Assert.assertEquals(90, result);
    }

    @Test
    public void testGetPricePowerTagDayWhenPricePowerTagIsLowestPriceInRecent180Days() throws Throwable {
        String pricePowerTag = PricePowerTagEnum.LOWEST_PRICE_IN_RECENT_180_DAYS.getDesc();
        int result = invokePrivateMethod("getPricePowerTagDay", pricePowerTag);
        Assert.assertEquals(180, result);
    }

    @Test
    public void testGetPricePowerTagDayWhenPricePowerTagIsLowestPriceInRecent365Days() throws Throwable {
        String pricePowerTag = PricePowerTagEnum.LOWEST_PRICE_IN_RECENT_365_DAYS.getDesc();
        int result = invokePrivateMethod("getPricePowerTagDay", pricePowerTag);
        Assert.assertEquals(365, result);
    }

    @Test
    public void testGetPricePowerTagDayWhenPricePowerTagIsOther() throws Throwable {
        String pricePowerTag = "other";
        int result = invokePrivateMethod("getPricePowerTagDay", pricePowerTag);
        Assert.assertEquals(0, result);
    }

    @Test
    public void testCalcDiscountBothNull() throws Throwable {
        BigDecimal finalPrice = null;
        BigDecimal marketPrice = null;
        BigDecimal result = invokeCalcDiscount(finalPrice, marketPrice);
        assertNull(result);
    }

    @Test
    public void testCalcDiscountFinalPriceNull() throws Throwable {
        BigDecimal finalPrice = null;
        BigDecimal marketPrice = new BigDecimal("100");
        BigDecimal result = invokeCalcDiscount(finalPrice, marketPrice);
        assertNull(result);
    }

    @Test
    public void testCalcDiscountMarketPriceNull() throws Throwable {
        BigDecimal finalPrice = new BigDecimal("100");
        BigDecimal marketPrice = null;
        BigDecimal result = invokeCalcDiscount(finalPrice, marketPrice);
        assertNull(result);
    }

    @Test
    public void testCalcDiscountBothNotNull() throws Throwable {
        BigDecimal finalPrice = new BigDecimal("100");
        BigDecimal marketPrice = new BigDecimal("100");
        BigDecimal result = invokeCalcDiscount(finalPrice, marketPrice);
        // Use compareTo for comparison to handle scale correctly
        assertEquals(0, result.compareTo(new BigDecimal("10.0")));
    }
}
