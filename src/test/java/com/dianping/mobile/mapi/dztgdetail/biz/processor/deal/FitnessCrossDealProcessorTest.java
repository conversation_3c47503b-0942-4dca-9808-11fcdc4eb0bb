package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.alibaba.fastjson.JSON;
import com.dianping.account.MeituanUserService;
import com.dianping.account.UserAccountService;
import com.dianping.account.dto.MeituanUserInfoDTO;
import com.dianping.account.dto.UserAccountDTO;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.tuangu.dztg.usercenter.api.CreateOrderPageUrlService;
import com.dianping.tuangu.dztg.usercenter.api.dto.Response;
import com.dianping.tuangu.dztg.usercenter.api.request.BatchGetCreateOrderPageUrlReq;
import com.dianping.unified.coupon.manage.api.UnifiedCouponInfoService;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.request.BatchLoadCouponRequest;
import com.dianping.unified.coupon.manage.api.response.UnifiedCouponManageResponse;
import com.google.common.collect.Lists;
import com.sankuai.dzcard.fulfill.api.MemberCardQueryService;
import com.sankuai.dzcard.fulfill.api.dto.MemberCardDTO;
import com.sankuai.dzcard.fulfill.api.enums.MemberCardResponseCode;
import com.sankuai.dzcard.fulfill.api.request.PageQueryMemberCardRequest;
import com.sankuai.dzcard.fulfill.api.request.QueryMemberCardRequest;
import com.sankuai.dzcard.fulfill.api.response.MemberCardResponse;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.List;
import java.util.Map;

public class FitnessCrossDealProcessorTest {

    @Mock
    private MemberCardQueryService memberCardQueryService;
    @Mock
    private UnifiedCouponInfoService unifiedCouponInfoService;
    @Mock
    private CreateOrderPageUrlService createOrderPageUrlService;
    @InjectMocks
    private FitnessCrossDealProcessor processor;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        Mockito.doReturn(buildMemberCardDTOS()).when(memberCardQueryService).pageQueryMemberCard(Mockito.any(PageQueryMemberCardRequest.class));
        Mockito.doReturn(buildMemberCardDTOS()).when(memberCardQueryService).queryMemberCard(Mockito.any(QueryMemberCardRequest.class));
        Mockito.doReturn(buildUnifiedCouponDTOS()).when(unifiedCouponInfoService).batchLoadCoupon(Mockito.any(BatchLoadCouponRequest.class));
        Mockito.doReturn(buildOrderUrlResponse()).when(createOrderPageUrlService).batchGetCreateOrderPageUrl(Mockito.any(BatchGetCreateOrderPageUrlReq.class));
    }

    @Test
    public void test() {
        DealCtx ctx = buildDealCtx();
        processor.prepare(ctx);
        processor.process(ctx);
        Assert.assertNotNull(ctx.getFitnessCrossBO());
    }

    /**
     * 构建dealctx
     */
    private DealCtx buildDealCtx() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        envCtx.setMtUserId(123L);
        DealCtx ctx = new DealCtx(envCtx);
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("dealGroupFitnessPassConfig");
        attributeDTO.setValue(Collections.singletonList("fitnessPass"));
        ctx.setAttrs(Collections.singletonList(attributeDTO));
        return ctx;
    }

    private MemberCardResponse<List<MemberCardDTO>> buildMemberCardDTOS() {
        String json = "{\n" +
                "    \"addTime\": 1717591331000,\n" +
                "    \"cardName\": \"健身通券包\",\n" +
                "    \"cardTemplateId\": 17750,\n" +
                "    \"cardTemplateType\": 11,\n" +
                "    \"dpUserId\": 0,\n" +
                "    \"id\": 421241,\n" +
                "    \"mtUserId\": 5108960236,\n" +
                "    \"orderId\": \"*******************\",\n" +
                "    \"phoneNo\": \"E56c96GoYEr5173\",\n" +
                "    \"platform\": 2,\n" +
                "    \"rights\": [\n" +
                "        {\n" +
                "            \"attrs\": {\n" +
                "                \"couponEndTime\": \"2024-12-02 23:59:59\",\n" +
                "                \"couponToken\": \"PI_******************\",\n" +
                "                \"couponBeginTime\": \"2024-06-05 20:42:11\",\n" +
                "                \"couponGroupId\": \"1557340677\",\n" +
                "                \"couponPrice\": \"999.00\"\n" +
                "            },\n" +
                "            \"id\": 1749078,\n" +
                "            \"memberCardId\": 421241,\n" +
                "            \"referRightTemplateId\": \"1557340677\",\n" +
                "            \"referUserRightId\": \"******************\",\n" +
                "            \"rightType\": 8,\n" +
                "            \"status\": 7\n" +
                "        },\n" +
                "        {\n" +
                "            \"attrs\": {\n" +
                "                \"couponEndTime\": \"2024-12-02 23:59:59\",\n" +
                "                \"couponBeginTime\": \"2024-06-05 20:44:07\",\n" +
                "                \"couponGroupId\": \"1557340677\"\n" +
                "            },\n" +
                "            \"id\": 1749080,\n" +
                "            \"memberCardId\": 421241,\n" +
                "            \"referRightTemplateId\": \"1557340677\",\n" +
                "            \"referUserRightId\": \"******************\",\n" +
                "            \"rightType\": 8,\n" +
                "            \"status\": 7\n" +
                "        },\n" +
                "        {\n" +
                "            \"attrs\": {\n" +
                "                \"couponEndTime\": \"2024-12-03 23:59:59\",\n" +
                "                \"couponBeginTime\": \"2024-06-06 11:02:12\",\n" +
                "                \"couponGroupId\": \"1557340677\"\n" +
                "            },\n" +
                "            \"id\": 1749843,\n" +
                "            \"memberCardId\": 421241,\n" +
                "            \"referRightTemplateId\": \"1557340677\",\n" +
                "            \"referUserRightId\": \"******************\",\n" +
                "            \"rightType\": 8,\n" +
                "            \"status\": 7\n" +
                "        },\n" +
                "        {\n" +
                "            \"attrs\": {\n" +
                "                \"couponEndTime\": \"2024-12-03 23:59:59\",\n" +
                "                \"couponBeginTime\": \"2024-06-06 15:00:46\",\n" +
                "                \"couponGroupId\": \"1557340677\"\n" +
                "            },\n" +
                "            \"id\": 1749866,\n" +
                "            \"memberCardId\": 421241,\n" +
                "            \"referRightTemplateId\": \"1557340677\",\n" +
                "            \"referUserRightId\": \"******************\",\n" +
                "            \"rightType\": 8,\n" +
                "            \"status\": 7\n" +
                "        },\n" +
                "        {\n" +
                "            \"attrs\": {\n" +
                "                \"couponEndTime\": \"2024-12-03 23:59:59\",\n" +
                "                \"couponBeginTime\": \"2024-06-06 20:36:48\",\n" +
                "                \"couponGroupId\": \"1557340677\"\n" +
                "            },\n" +
                "            \"id\": 1749941,\n" +
                "            \"memberCardId\": 421241,\n" +
                "            \"referRightTemplateId\": \"1557340677\",\n" +
                "            \"referUserRightId\": \"******************\",\n" +
                "            \"rightType\": 8,\n" +
                "            \"status\": 7\n" +
                "        },\n" +
                "        {\n" +
                "            \"attrs\": {\n" +
                "                \"couponEndTime\": \"2024-12-03 23:59:59\",\n" +
                "                \"couponBeginTime\": \"2024-06-06 11:02:12\",\n" +
                "                \"couponGroupId\": \"1557340677\",\n" +
                "                \"couponCount\": \"0\"\n" +
                "            },\n" +
                "            \"id\": 1755144,\n" +
                "            \"memberCardId\": 421241,\n" +
                "            \"referRightTemplateId\": \"1557340677\",\n" +
                "            \"referUserRightId\": \"******************\",\n" +
                "            \"rightType\": 8,\n" +
                "            \"status\": 7\n" +
                "        },\n" +
                "        {\n" +
                "            \"attrs\": {\n" +
                "                \"couponEndTime\": \"2024-12-09 23:59:59\",\n" +
                "                \"couponBeginTime\": \"2024-06-12 17:01:44\",\n" +
                "                \"couponGroupId\": \"1557340677\",\n" +
                "                \"couponCount\": \"0\"\n" +
                "            },\n" +
                "            \"id\": 1755279,\n" +
                "            \"memberCardId\": 421241,\n" +
                "            \"referRightTemplateId\": \"1557340677\",\n" +
                "            \"referUserRightId\": \"******************\",\n" +
                "            \"rightType\": 8,\n" +
                "            \"status\": 5\n" +
                "        }\n" +
                "    ],\n" +
                "    \"shopId\": 0,\n" +
                "    \"status\": 3,\n" +
                "    \"useBeginTime\": 1717591331000,\n" +
                "    \"useEndTime\": 1733155199000\n" +
                "}";
        MemberCardDTO memberCardDTO = JSON.parseObject(json, MemberCardDTO.class);
        List<MemberCardDTO> memberCardDTOS = Lists.newArrayList(memberCardDTO);
        MemberCardResponse<List<MemberCardDTO>> response = new MemberCardResponse<>();
        response.setResultCode(MemberCardResponseCode.SUCCESS.getCode());
        response.setSuccess(true);
        response.setResult(Collections.emptyList());
        return response;
    }

    private UnifiedCouponManageResponse<List<UnifiedCouponDTO>> buildUnifiedCouponDTOS() {
        String json = "{\"addDate\":1717677418000,\"available\":false,\"beginTime\":1717677408000,\"compressUnifiedCouponId\":******************,\"couponDetailUrl\":\"http://test.i.meituan.com/group/coupondetail/detail.html?couponid=******************\",\"couponGroupDTO\":{\"activityNum\":\"TESTFORCOUPONT949R00374A2823067$55301\",\"addDate\":1717067456000,\"admins\":\"liquanfang\",\"androidFrom\":\"11.11.200\",\"androidTo\":\"0\",\"author\":\"liquanfang\",\"authorUserId\":21005356,\"beginTime\":1716998400000,\"bindSwitcher\":0,\"budgetAmount\":999999.00,\"businessCouponType\":0,\"calculateNoDiscountAmount\":false,\"cardLimit\":99,\"categoryList\":[],\"cityIdList\":[],\"clientVersion\":\"\",\"couponGroupId\":1557340677,\"couponGroupName\":\"健身通0折劵测试\",\"couponGroupType\":15,\"couponProductList\":[],\"couponUserTypeList\":[],\"dailyStock\":0,\"dimensionStockId\":0,\"discountAmount\":999.00,\"discountAmountType\":4,\"discountCouponDTO\":{\"accuracy\":3,\"discount\":0,\"discountBaseType\":1,\"discountMaxAmount\":999,\"priceLimit\":0,\"roundingMode\":1},\"discountRange\":{},\"draftDTO\":{\"couponGroupId\":0,\"couponGroupStatus\":0,\"couponType\":0,\"draftId\":11976743,\"draftType\":0,\"pregenerate\":false,\"status\":0,\"stock\":0},\"draftId\":0,\"endTime\":1719763199000,\"expireType\":2,\"extraInfoMap\":{\"allPlatformsCoupon\":\"true\",\"usePercentageNotifyThreshold\":\"{\\\"pushSwitch\\\":true,\\\"alertType\\\":3,\\\"remindDTOList\\\":[{\\\"threshold\\\":\\\"10\\\",\\\"interval\\\":\\\"10\\\",\\\"unit\\\":2}]}\",\"customParams\":\"{\\\"bodyBuildZeroDiscountFlag\\\":\\\"true\\\",\\\"dzSceneTag\\\":\\\"bodyBuildCouponPack\\\"}\",\"budgetConfig\":\"{\\\"bearMode\\\":1}\",\"overlayType\":\"platformCoupon_excl_merchantCoupon_platformDeduction\",\"buAttributes\":\"1021\",\"groupDynamicBindRuleInfo\":\"{\\\"businessDynamicBindRuleMap\\\":{}}\",\"templateId\":\"1000034\",\"bizLine\":\"joy\",\"sieveEngineRules\":\"{\\\"180029\\\":{\\\"product_w\\\":[***********,***********]}}\",\"couponCommonTag\":\"bodyBuildZeroDiscountCoupon\",\"stockNotEnoughNotifyThreshold\":\"[{\\\"max\\\":\\\"10\\\",\\\"min\\\":\\\"0\\\",\\\"interval\\\":\\\"10\\\"}]\",\"bgType\":\"0\",\"offlineChannels\":\"bodyBuildCouponPack\",\"truncateFloatTime\":\"1\",\"commonGroupType\":\"2\",\"dpAppVersion\":\"{\\\"androidFrom\\\":\\\"10.50.0\\\",\\\"iosFrom\\\":\\\"10.50.0\\\"}\",\"saveSource\":\"4\",\"frontValueType\":\"1\",\"consumeAmountLimit\":\"999999\",\"redirectLink\":\"{\\\"mtAppLink\\\":\\\"imeituan://www.meituan.com/gc/mrn?mrn_biz=gcbu&mrn_entry=homepage&mrn_component=mrn-gc-joyhomebigfun&templateKey=nib.general.joy_v3&metrics_start_time=1717056400856.133&pfb_category_preload_strategy=B1\\\",\\\"dpAppLink\\\":\\\"dianping://gcmrn?mrn_biz=gcbu&mrn_entry=homepage&mrn_component=mrn-gc-joyhomebigfun&templateKey=nib.general.joy_v3\\\"}\"},\"floatDay\":365,\"floatTimeUnit\":0,\"iphoneFrom\":\"11.11.200\",\"iphoneTo\":\"0\",\"isPregenerate\":0,\"issueSourceLimit\":[\"DZ_BODYBUILDZERODISCOUNT_COUPON\"],\"mComment\":\"\",\"maxPerUser\":99,\"mtCampaignID\":0,\"notUseInHoliday\":0,\"passUserId\":0,\"payChannelList\":[],\"paymentRuleId\":127903,\"platformList\":[8,64],\"priceLimit\":0.00,\"priceLimitType\":0,\"priceRange\":{},\"productAmountLimit\":0,\"productCodeList\":[180029],\"published\":true,\"redirectLink\":\"imeituan://www.meituan.com/gc/mrn?mrn_biz=gcbu&mrn_entry=homepage&mrn_component=mrn-gc-joyhomebigfun&templateKey=nib.general.joy_v3&metrics_start_time=1717056400856.133&pfb_category_preload_strategy=B1\",\"remainConsumeStock\":0,\"remainDailyStock\":0,\"remainStock\":0,\"riskComment\":\"\",\"source\":\"com.sankuai.mpmkt.coupon.operationweb\",\"startFloatDay\":0,\"startFloatHour\":0,\"startFloatTimeUnit\":0,\"status\":13,\"stepPriceList\":[],\"stock\":10000,\"stockPushSwitch\":0,\"stockUnitType\":0,\"stopIssueFlag\":0,\"suitUserType\":1,\"surpriseCoupon\":false,\"target\":3,\"unavailableDateType\":0,\"unifiedCouponGroupId\":\"1557340677\",\"userTypeList\":[],\"valueType\":4},\"endTime\":1733241599000,\"extraType\":0,\"mtCouponId\":0,\"oldCouponId\":0,\"outBizId\":\"4953981785615520836\",\"productType\":180029,\"reduceProductCount\":0,\"status\":1,\"unifiedCouponBudgetDTO\":{\"merchantDiscountAmount\":0,\"platDiscountAmount\":999.00},\"unifiedCouponDisplayDTO\":{\"subTitle\":\"满0元可用\",\"title\":\"0.0折\"},\"unifiedCouponId\":\"******************\",\"used\":true,\"usedDate\":1718176815000,\"userId\":5108960236,\"userType\":\"MT\"}";
        UnifiedCouponDTO unifiedCouponDTO = JSON.parseObject(json, UnifiedCouponDTO.class);
        UnifiedCouponManageResponse<List<UnifiedCouponDTO>> response = new UnifiedCouponManageResponse<>();
        response.setSuccess(true);
        response.setResultCode(0);
        response.setResult(Collections.singletonList(unifiedCouponDTO));
        return response;
    }

    private Response<Map<String, String>> buildOrderUrlResponse() {
        String json = "{\"code\":\"0000\",\"content\":{\"421935814\":\"imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=gcsubmitordermrnmodules-unify&mrn_component=dealsubmitorderpage-popup&mrn_min_version=0.0.540&dealid=421935814&shopid=607098045&skuid=&isTransparent=true&hideLoading=true&mrn_transparent=true&mrn_hideloading=true&mrn_hideNextNavBar=true&pagesource=dealGroupDetail&expid=floatingLayer\"},\"msg\":\"成功\",\"success\":true}";
        Response<Map<String, String>> response = JSON.parseObject(json, Response.class);
        return response;
    }

}