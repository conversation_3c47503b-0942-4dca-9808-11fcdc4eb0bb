package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.mobile.mapi.dztgdetail.common.enums.ShopCategoryEnum;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.*;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ShopTagWrapper_GetWearableNailShopTag_1_Test {

    private ShopTagWrapper shopTagWrapper;

    private MockedStatic<LionConfigUtils> mockedStaticLionConfigUtils;

    @Before
    public void setUp() {
        shopTagWrapper = new ShopTagWrapper();
        mockedStaticLionConfigUtils = mockStatic(LionConfigUtils.class);
    }

    @After
    public void tearDown() {
        mockedStaticLionConfigUtils.close();
    }

    /**
     * 测试dpShopId为null时返回UNKNOWN
     */
    @Test
    public void testGetWearableNailShopTagDpShopIdIsNull() throws Throwable {
        ShopCategoryEnum result = shopTagWrapper.getWearableNailShopTag(null, new HashMap<>());
        assertEquals(ShopCategoryEnum.UNKNOWN, result);
    }

    /**
     * 测试dpShopId2TagMap为空时返回UNKNOWN
     */
    @Test
    public void testGetWearableNailShopTagDpShopId2TagMapIsEmpty() throws Throwable {
        ShopCategoryEnum result = shopTagWrapper.getWearableNailShopTag(1L, new HashMap<>());
        assertEquals(ShopCategoryEnum.UNKNOWN, result);
    }

    /**
     * 测试dpShopId2TagMap中没有与dpShopId对应的标签列表时返回UNKNOWN
     */
    @Test
    public void testGetWearableNailShopTagDpShopId2TagMapNotContainsDpShopId() throws Throwable {
        Map<Long, List<DisplayTagDto>> dpShopId2TagMap = new HashMap<>();
        dpShopId2TagMap.put(2L, new ArrayList<>());
        ShopCategoryEnum result = shopTagWrapper.getWearableNailShopTag(1L, dpShopId2TagMap);
        assertEquals(ShopCategoryEnum.UNKNOWN, result);
    }

    /**
     * 测试dpShopId2TagMap中有与dpShopId对应的标签列表，但是没有tagId为wearableNailOnly或wearableNailRetail的标签时返回UNKNOWN
     */
    @Test
    public void testGetWearableNailShopTagDpShopId2TagMapContainsDpShopIdButNoWearableNailOnlyOrWearableNailRetail() throws Throwable {
        Map<Long, List<DisplayTagDto>> dpShopId2TagMap = new HashMap<>();
        DisplayTagDto displayTagDto = new DisplayTagDto();
        displayTagDto.setTagId(1L);
        dpShopId2TagMap.put(1L, Collections.singletonList(displayTagDto));
        ShopCategoryEnum result = shopTagWrapper.getWearableNailShopTag(1L, dpShopId2TagMap);
        assertEquals(ShopCategoryEnum.UNKNOWN, result);
    }

    /**
     * 测试dpShopId2TagMap中有与dpShopId对应的标签列表，并且有tagId为wearableNailOnly或wearableNailRetail的标签时返回对应的ShopCategoryEnum
     */
    @Test
    public void testGetWearableNailShopTagDpShopId2TagMapContainsDpShopIdAndWearableNailOnlyOrWearableNailRetail() throws Throwable {
        Map<Long, List<DisplayTagDto>> dpShopId2TagMap = new HashMap<>();
        DisplayTagDto displayTagDto = new DisplayTagDto();
        displayTagDto.setTagId(20972L);
        dpShopId2TagMap.put(1L, Collections.singletonList(displayTagDto));
        Map<String, Long> wearableNailShopTagIdMap = new HashMap<>();
        wearableNailShopTagIdMap.put("wearableNailOnly", 20972L);
        wearableNailShopTagIdMap.put("wearableNailRetail", 21082L);
        mockedStaticLionConfigUtils.when(LionConfigUtils::getWearableNailShopTagIdConfig).thenReturn(wearableNailShopTagIdMap);
        ShopCategoryEnum result = shopTagWrapper.getWearableNailShopTag(1L, dpShopId2TagMap);
        assertEquals(ShopCategoryEnum.WEARABLE_NAIL_ONLY, result);
    }
}
