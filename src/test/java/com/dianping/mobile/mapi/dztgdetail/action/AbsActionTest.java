package com.dianping.mobile.mapi.dztgdetail.action;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.MobileHeader;
import com.dianping.mobile.framework.datatypes.token.UserStatusResult;
import com.dianping.mobile.gatekeeper.api.model.protocol.ClientInfo;
import com.dianping.mobile.gatekeeper.api.model.protocol.UserInfo;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.maoyan.mtrace.Tracer;
import com.sankuai.pearl.framework.context.MobileContextAdaptor;
import com.sankuai.pearl.framework.context.ProtocolContext;
import com.sankuai.pearl.framework.enums.MiniProgramType;
import javax.servlet.http.HttpServletRequest;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

@RunWith(MockitoJUnitRunner.class)
public class AbsActionTest {

    @Mock
    private IMobileContext appCtx;

    @Mock
    private HttpServletRequest request;

    private AbsAction absAction;

    private MockedStatic<Tracer> tracerMockedStatic;

    @Before
    public void setUp() {
        absAction = mock(AbsAction.class, CALLS_REAL_METHODS);
        when(appCtx.getRequest()).thenReturn(request);
        when(appCtx.getHeader()).thenReturn(mock(MobileHeader.class));
        tracerMockedStatic = mockStatic(Tracer.class);
    }

    @After
    public void tearDown() throws Exception {
        tracerMockedStatic.close();
    }

    @Test
    public void testInitEnvCtxWhenUserStatusIsNotNull() {
        // arrange
        UserStatusResult userStatus = mock(UserStatusResult.class);
        when(userStatus.getUserId()).thenReturn(1L);
        when(appCtx.getUserStatus()).thenReturn(userStatus);
        tracerMockedStatic.when(() -> Tracer.putContext(anyString(), anyString())).thenReturn("");
        // act
        EnvCtx result = absAction.initEnvCtx(appCtx);
        // assert
        assertEquals(1L, result.getDpUserId());
    }

    @Test
    public void testInitEnvCtxWhenUserStatusIsNull() {
        // arrange
        when(appCtx.getUserStatus()).thenReturn(null);
        tracerMockedStatic.when(() -> Tracer.putContext(anyString(), anyString())).thenReturn("");
        // act
        EnvCtx result = absAction.initEnvCtx(appCtx);
        // assert
        assertEquals(0, result.getDpUserId());
    }

    @Test
    public void testInitEnvCtxWhenAppCtxIsNotNullButMethodsReturnDefaultValues() {
        // arrange
        when(appCtx.isMeituanClient()).thenReturn(false);
        when(appCtx.isDianpingClient()).thenReturn(false);
        when(appCtx.getAppId()).thenReturn(0);
        when(appCtx.getMiniProgramType()).thenReturn(MiniProgramType.Unknown);
        when(appCtx.getUserAgent()).thenReturn("");
        when(appCtx.getUserStatus()).thenReturn(null);
        tracerMockedStatic.when(() -> Tracer.putContext(anyString(), anyString())).thenReturn("");
        // act
        EnvCtx result = absAction.initEnvCtx(appCtx);
        // assert
        assertEquals(DztgClientTypeEnum.UNKNOWN, result.getDztgClientTypeEnum());
    }

    @Test
    public void testInitEnvCtxWhenAppCtxIsMeituanClient() {
        // arrange
        when(appCtx.isMeituanClient()).thenReturn(true);
        when(appCtx.isIOS()).thenReturn(true);
        tracerMockedStatic.when(() -> Tracer.putContext(anyString(), anyString())).thenReturn("");
        // act
        EnvCtx result = absAction.initEnvCtx(appCtx);
        // assert
        assertEquals(DztgClientTypeEnum.MEITUAN_APP, result.getDztgClientTypeEnum());
    }

    @Test
    public void testInitEnvCtxWhenAppCtxIsDianpingClient() {
        // arrange
        when(appCtx.isDianpingClient()).thenReturn(true);
        when(appCtx.isIOS()).thenReturn(true);
        tracerMockedStatic.when(() -> Tracer.putContext(anyString(), anyString())).thenReturn("");
        // act
        EnvCtx result = absAction.initEnvCtx(appCtx);
        // assert
        assertEquals(DztgClientTypeEnum.DIANPING_APP, result.getDztgClientTypeEnum());
    }

    @Test
    public void testInitEnvCtxWhenAppCtxIsExternalRequest() {
        // arrange
        when(appCtx.isDianpingClient()).thenReturn(true);
        when(appCtx.isIOS()).thenReturn(true);
        when(appCtx.getVersion()).thenReturn("1.0.0");
        tracerMockedStatic.when(() -> Tracer.putContext(anyString(), anyString())).thenReturn("");
        // act
        EnvCtx result = absAction.initEnvCtx(appCtx);
        // assert
        assertEquals("1.0.0", result.getVersion());
    }

    @Test
    public void testInitEnvCtxWhenUserStatusIsNotNullAndUserIdIsNotDefault() {
        // arrange
        UserStatusResult userStatus = mock(UserStatusResult.class);
        when(userStatus.getUserId()).thenReturn(2L);
        when(appCtx.getUserStatus()).thenReturn(userStatus);
        tracerMockedStatic.when(() -> Tracer.putContext(anyString(), anyString())).thenReturn("");
        // act
        EnvCtx result = absAction.initEnvCtx(appCtx);
        // assert
        assertEquals(2L, result.getDpUserId());
    }

    /**
     * 测试 initEnvCtx 方法，当 appCtx 为 null 时
     */
    @Test
    public void testInitEnvCtxWhenAppCtxIsNull() throws Throwable {
        // arrange
        appCtx = null;
        // act
        EnvCtx result = absAction.initEnvCtx(appCtx);
        // assert
        assertNotNull(result);
    }

    /**
     * 测试 initEnvCtx 方法，当 appCtx 的 mpAppId 为 wxe955ef83bdcc9f82 时
     */
    @Test
    public void testInitEnvCtxWhenMpAppIdIsMtLive() throws Throwable {
        // arrange
        when(appCtx.getRequest().getHeader("mpAppId")).thenReturn("wxe955ef83bdcc9f82");
        tracerMockedStatic.when(() -> Tracer.putContext(anyString(), anyString())).thenReturn("");
        // act
        EnvCtx result = absAction.initEnvCtx(appCtx);
        // assert
        assertEquals(DztgClientTypeEnum.MEITUAN_LIVE_WEIXIN_MINIAPP, result.getDztgClientTypeEnum());
    }

    /**
     * 测试initEnvCtxV2方法，当appCtx为null时
     */
    @Test
    public void testInitEnvCtxV2WithNullAppCtx() {
        // arrange
        IMobileContext appCtx = null;
        // act
        EnvCtx result = absAction.initEnvCtxV2(appCtx);
        EnvCtx resultWithOutUserInfo = absAction.initEnvCtxWithOutUserInfo(appCtx);
        // assert
        assertNotNull(result);
        assertNotNull(resultWithOutUserInfo);
    }

    /**
     * 测试initEnvCtxV2方法，当appCtx非null且isFromH5为true时
     */
    @Test
    public void testInitEnvCtxV2WithIsFromH5True() {
        // arrange
        when(appCtx.isMeituanClient()).thenReturn(true);
        // act
        EnvCtx result = absAction.initEnvCtxV2(appCtx);
        // assert
        assertEquals(DztgClientTypeEnum.MEITUAN_APP, result.getDztgClientTypeEnum());
    }

    /**
     * 测试initEnvCtxV2方法，当appCtx非null且isFromH5为false时
     */
    @Test
    public void testInitEnvCtxV2WithIsFromH5False() {
        // arrange
        when(appCtx.getAppId()).thenReturn(396);
        // act
        EnvCtx result = absAction.initEnvCtxV2(appCtx);
        // assert
        assertEquals(DztgClientTypeEnum.MEITUAN_MAP_APP, result.getDztgClientTypeEnum());
    }

    /**
     * 测试initEnvCtxV2方法，当appCtx非null且isFromH5为false且appId不为396时
     */
    @Test
    public void testInitEnvCtxV2WithIsFromH5FalseAndAppIdNot396() {
        // arrange
        when(appCtx.getAppId()).thenReturn(100);
        // act
        EnvCtx result = absAction.initEnvCtxV2(appCtx);
        // assert
        assertEquals(DztgClientTypeEnum.UNKNOWN, result.getDztgClientTypeEnum());
    }

    /**
     * 测试initEnvCtxV2方法，当appCtx非null且isFromH5为false且appId为396且UserAgent为Dpmerchant时
     */
    @Test
    public void testInitEnvCtxV2WithIsFromH5FalseAndAppId396AndUserAgentDpmerchant() {
        // arrange
        when(appCtx.getAppId()).thenReturn(396);
        when(appCtx.getUserAgent()).thenReturn("Dpmerchant");
        // act
        EnvCtx result = absAction.initEnvCtxV2(appCtx);
        // assert
        assertEquals(DztgClientTypeEnum.MEITUAN_MAP_APP, result.getDztgClientTypeEnum());
    }

    @Test
    public void testInitEnvCtxV2WithApollo() {
        // arrange
        when(appCtx.getUserAgent()).thenReturn("MApi 1.4 (dpappolo 5.6.1 appstore; iPhone 14.8 iPhone13,2)");
        // act
        EnvCtx result = absAction.initEnvCtxV2(appCtx);
        // assert
        assertEquals(DztgClientTypeEnum.THIRD_PLATFORM, result.getDztgClientTypeEnum());
    }
    @Test
    public void testInitEnvCtxV2Renew() {
        // arrange
        when(appCtx.getUserAgent()).thenReturn("MApi 1.4 (dpappolo 5.6.1 appstore; iPhone 14.8 iPhone13,2)");
        // act
        EnvCtx result = absAction.initEnvCtxV2(appCtx);
        Class clazz = AbsAction.class;
        try {
            Method renewUserStatus = clazz.getDeclaredMethod("renewUserStatus", IMobileContext.class, long.class);
            Method renewUserInfo = clazz.getDeclaredMethod("renewUserInfo", IMobileContext.class, long.class);

            renewUserStatus.setAccessible(true);
            renewUserInfo.setAccessible(true);

            MobileContextAdaptor mobileContextAdaptor = new MobileContextAdaptor();
            ProtocolContext protocolContext = new ProtocolContext();
            ClientInfo clientInfo = new ClientInfo();
            clientInfo.setUserInfo(new UserInfo());
            protocolContext.setClientInfo(clientInfo);
            mobileContextAdaptor.setProtocolContext(protocolContext);
            renewUserStatus.invoke(absAction, mobileContextAdaptor, 1L);
            renewUserInfo.invoke(absAction, mobileContextAdaptor, 1L);
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e);
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        // assert
        assertEquals(DztgClientTypeEnum.THIRD_PLATFORM, result.getDztgClientTypeEnum());
    }
}
