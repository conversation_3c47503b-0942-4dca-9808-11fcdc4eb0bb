package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.BeautyTechGroupService;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.highlights.DztgHighlightsModule;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.dianping.technician.dto.shopsearch.TechCard;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

public class BeautyHighlightsProcessorBuildBeautyHighlightsTest {

    private BeautyHighlightsProcessor processor;

    @Mock
    private DealCtx ctx;

    @Mock
    private DealGroupDTO dealGroupDTO;

    @Mock
    private DealGroupCategoryDTO category;

    @Mock
    private BeautyTechGroupService beautyTechGroupService;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        processor = new BeautyHighlightsProcessor();
        // Use reflection to set the private field
        Field beautyTechGroupServiceField = BeautyHighlightsProcessor.class.getDeclaredField("beautyTechGroupService");
        beautyTechGroupServiceField.setAccessible(true);
        beautyTechGroupServiceField.set(processor, beautyTechGroupService);
    }

    /**
     * Test case when DealGroupDTO is null.
     */
    @Test
    public void testBuildBeautyHighlightsDealGroupIsNull() throws Throwable {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(null);
        // act
        processor.buildBeautyHighlights(ctx);
        // assert
        verify(ctx, never()).setHighlightsModule(any(DztgHighlightsModule.class));
    }

    /**
     * Test case when category ID is 503, 509, or 511.
     */
    @Test
    public void testBuildBeautyHighlightsCategory503509511() throws Throwable {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(category.getCategoryId()).thenReturn(503L);
        // Mock the necessary attributes for build4Other
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("selling_point");
        attrDTO.setValue(Arrays.asList("selling_point_value"));
        when(dealGroupDTO.getAttrs()).thenReturn(Arrays.asList(attrDTO));
        // act
        processor.buildBeautyHighlights(ctx);
        // assert
        verify(ctx, times(1)).setHighlightsModule(any(DztgHighlightsModule.class));
    }
}
