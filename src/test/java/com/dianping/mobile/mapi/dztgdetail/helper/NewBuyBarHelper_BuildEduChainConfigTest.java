package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.mobile.mapi.dztgdetail.button.BuilderChainConfig;
import com.dianping.mobile.mapi.dztgdetail.button.BuilderConfig;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

@RunWith(MockitoJUnitRunner.class)
public class NewBuyBarHelper_BuildEduChainConfigTest {

    /**
     * Tests the buildEduChainConfig method.
     */
    @Test
    @Ignore
    public void testBuildEduChainConfig() throws Throwable {
        // Arrange
        // No data setup required
        // Act
        BuilderChainConfig result = NewBuyBarHelper.buildEduChainConfig();
        // Assert
        assertNotNull(result);
        List<BuilderConfig> builderConfigs = result.getBuilderConfigs();
        // Assuming buildXiYuChainConfig adds a specific number of configurations, plus one additional configuration
        // for EduFreeTrialButtonBuilder. The exact number should match the implementation details of buildXiYuChainConfig.
        // This number needs to be verified against the actual implementation.
        // This number should be updated based on the actual number of configurations added by buildXiYuChainConfig plus one for EduFreeTrialButtonBuilder.
        int expectedConfigSize = 22;
        assertEquals("The number of builder configurations does not match the expected value.", expectedConfigSize, builderConfigs.size());
        // Corrected to expect the fully qualified class name of EduFreeTrialButtonBuilder
        assertEquals("The first builder configuration name does not match the expected value.", "com.dianping.mobile.mapi.dztgdetail.button.edu.EduFreeTrialButtonBuilder", builderConfigs.get(0).getBuilderName());
    }
}
