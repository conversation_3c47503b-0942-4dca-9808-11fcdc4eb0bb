package com.dianping.mobile.mapi.dztgdetail.biz.processor;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealBookWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.sankuai.clr.content.process.gateway.thrift.dto.leads.LoadLeadsInfoRespDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

/**
 * @author: wuwenqiang
 * @create: 2024-09-23
 * @description:
 */
@RunWith(MockitoJUnitRunner.class)
public class LeadsInfoProcessorTest {
    @InjectMocks
    private LeadsInfoProcessor leadsInfoProcessor;

    @Mock
    private DealBookWrapper dealBookWrapper;

    @Test
    public void testProcessNormal() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        CompletableFuture future = CompletableFuture.completedFuture(null);
        ctx.getFutureCtx().setSpecialValueFuture(future);
        ctx.getFutureCtx().setLeadsInfoFuture(future);
        when(dealBookWrapper.queryLoadLeadsInfo(any())).thenReturn(new LoadLeadsInfoRespDTO());
        // act
        leadsInfoProcessor.process(ctx);
        // assert
        assertNotNull(ctx.getLeadsInfo());
    }
}
