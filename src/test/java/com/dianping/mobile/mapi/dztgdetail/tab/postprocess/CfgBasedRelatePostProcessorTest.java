package com.dianping.mobile.mapi.dztgdetail.tab.postprocess;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.DealTab;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.DealTabHolder;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mockStatic;

@RunWith(MockitoJUnitRunner.class)
public class CfgBasedRelatePostProcessorTest {

    @InjectMocks
    private CfgBasedRelatePostProcessor processor;

    private MockedStatic<Lion> lionMockedStatic;

    @Before
    public void setUp() {
        lionMockedStatic = mockStatic(Lion.class);
    }

    @After
    public void tearDown() throws Exception {
        lionMockedStatic.close();
    }

    @Test
    public void testPostProcessAfterRelateWithEmptyRelatedTabs() throws Throwable {
        int publishCategory = 1;
        DealTabHolder dealTabHolder = new DealTabHolder();
        // Ensure relatedTabs is initialized
        dealTabHolder.setRelatedTabs(new ArrayList<>());
        processor.postProcessAfterRelate(publishCategory, dealTabHolder);
        assertEquals("Related tabs should remain empty when no configuration matches", 0, dealTabHolder.getRelatedTabs().size());
    }

    @Test
    public void testPostProcessAfterRelateWithNonNullRelatedTabs() throws Throwable {
        int publishCategory = 2;
        DealTabHolder dealTabHolder = new DealTabHolder();
        ArrayList<DealTab> relatedTabs = new ArrayList<>();
        // Adding a DealTab to simulate non-empty relatedTabs
        relatedTabs.add(new DealTab());
        // Ensure relatedTabs is initialized with non-null value
        dealTabHolder.setRelatedTabs(relatedTabs);
        processor.postProcessAfterRelate(publishCategory, dealTabHolder);
        assertEquals("Related tabs should not be modified when no configuration matches", 1, dealTabHolder.getRelatedTabs().size());
    }

    @Test
    public void testPostProcessAfterRelateMatchCfgReturnNull() throws Throwable {
            lionMockedStatic.when(() -> Lion.getStringValue("com.sankuai.dzu.tpbase.dztgdetailweb.tab.cfg.new")).thenReturn(null);
            int publishCategory = 1;
            DealTabHolder dealTabHolder = new DealTabHolder();
            // Ensure relatedTabs is initialized
            dealTabHolder.setRelatedTabs(new ArrayList<>());
            processor.postProcessAfterRelate(publishCategory, dealTabHolder);
            assertEquals("Related tabs should remain empty when configuration returns null", 0, dealTabHolder.getRelatedTabs().size());
    }
}
