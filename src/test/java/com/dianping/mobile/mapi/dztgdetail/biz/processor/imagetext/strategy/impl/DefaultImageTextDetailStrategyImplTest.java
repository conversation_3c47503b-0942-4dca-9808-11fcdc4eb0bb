package com.dianping.mobile.mapi.dztgdetail.biz.processor.imagetext.strategy.impl;

import com.dianping.mobile.mapi.dztgdetail.common.enums.ImageTextStrategyEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ContentDetailPBO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class DefaultImageTextDetailStrategyImplTest {

    @InjectMocks
    private DefaultImageTextDetailStrategyImpl defaultImageTextDetailStrategyImpl;

    @Before
    public void setUp() {
    }

    /**
     * 测试getStrategyName方法返回默认策略
     */
    @Test
    public void testGetStrategyNameReturnsDefault() {
        // arrange
        // 无需特别安排

        // act
        ImageTextStrategyEnum result = defaultImageTextDetailStrategyImpl.getStrategyName();

        // assert
        Assert.assertEquals("方法应返回默认策略", ImageTextStrategyEnum.DEFAULT, result);
    }

    /**
     * 测试getContentDetail方法，当dealGroupDTO和contents都不为空时
     */
    @Test
    public void testGetContentDetailWithValidInputs() {
        // arrange
        List<ContentPBO> contents = new ArrayList<>();
        int threshold = 5;

        DealGroupDTO dealGroupDTO = new DealGroupDTO();

        // act
        ContentDetailPBO contentDetail = defaultImageTextDetailStrategyImpl.getContentDetail(dealGroupDTO, contents, threshold);

        // assert
        Assert.assertEquals(contents, contentDetail.getContents());
        Assert.assertEquals(contents.size() + 1, contentDetail.getFoldThreshold());
    }
}
