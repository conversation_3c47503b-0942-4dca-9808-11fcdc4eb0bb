package com.dianping.mobile.mapi.dztgdetail.facade.rcf.flash;

import com.dianping.cat.Cat;
import com.dianping.deal.style.DealPageLayoutService;
import com.dianping.deal.style.dto.laout.DealPageLayoutConfigDTO;
import com.dianping.deal.style.dto.laout.DealPageLayoutDTO;
import com.dianping.deal.style.dto.laout.DealPageLayoutQueryRequest;
import com.dianping.deal.style.dto.laout.DealPageLayoutQueryResponse;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FlashDealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FlashFutureCtx;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.fetcher.DealLayoutFetcher;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.flash.DealLayoutProcessor;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.util.Assert;

import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

/**
 * @Author: guangyujie
 * @Date: 2024/10/25 10:49
 */
@RunWith(MockitoJUnitRunner.class)
public class DealLayoutProcessorTest {

    @InjectMocks
    private DealLayoutProcessor dealLayoutProcessor;
    
    @Mock
    private DealLayoutFetcher dealLayoutFetcher;
    @Mock
    private DealPageLayoutService dealPageLayoutService;

    FlashDealCtx ctx = new FlashDealCtx(new EnvCtx());

    @Before
    public void setUp() throws Exception {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO category = new DealGroupCategoryDTO();
        category.setCategoryId(2L);
        dealGroupDTO.setCategory(category);
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        ctx.setDeviceHeight(22);
        ctx.setRequestSource("dsf");
        ctx.getEnvCtx().setVersion("32132");
        ctx.setFutureCtx(new FlashFutureCtx());
    }


    @Test
    public void process() {
        DealPageLayoutQueryResponse response = new DealPageLayoutQueryResponse();
        response.setSuccess(true);
        response.setLayout(new DealPageLayoutDTO());
        Future future = CompletableFuture.completedFuture(response);
        ctx.getFutureCtx().setDealPageLayoutFuture(future);
        dealLayoutProcessor.process(ctx);
        Assert.notNull(response);
    }

}
