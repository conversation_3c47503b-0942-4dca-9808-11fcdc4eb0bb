package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.detail.dto.DealGroupTemplateDetailDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.imagetext.strategy.ImageTextDetailHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealImageTextDetailReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ImageTextDetailPBO;
import com.dianping.mobile.mapi.dztgdetail.exception.QueryCenterResultException;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Future;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

public class DealDetailFacadeQueryImageTextFromQueryCenterTest {

    @InjectMocks
    private DealDetailFacade dealDetailFacade;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private DealGroupQueryService queryCenterDealGroupQueryService;

    @Mock
    private ImageTextDetailHandler imageTextDetailHandler;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Test when an exception occurs while fetching DealGroupDTO.
     */
    @Test(expected = QueryCenterResultException.class)
    public void testQueryImageTextFromQueryCenterExceptionFetchingDealGroupDTO() throws Throwable {
        // arrange
        DealImageTextDetailReq req = new DealImageTextDetailReq();
        req.setDealgroupid(123);
        EnvCtx envCtx = new EnvCtx();
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenThrow(new TException("Error fetching DealGroupDTO"));
        // act
        dealDetailFacade.queryImageTextFromQueryCenter(req, envCtx);
    }
}
