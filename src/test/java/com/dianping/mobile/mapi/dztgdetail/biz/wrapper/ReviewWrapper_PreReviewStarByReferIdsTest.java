package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.dianping.reviewremote.remote.ReviewDealGroupServiceV2;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import java.util.concurrent.Future;

import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

public class ReviewWrapper_PreReviewStarByReferIdsTest {

    @InjectMocks
    private ReviewWrapper reviewWrapper;

    @Mock
    private ReviewDealGroupServiceV2 reviewDealGroupServiceV2Future;

    private MockedStatic<FutureFactory> futureFactoryMockedStatic;
    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        futureFactoryMockedStatic = mockStatic(FutureFactory.class);
    }

    @After
    public void tearDown() {
        futureFactoryMockedStatic.close();
    }

    /**
     * 测试 dpId 小于等于0 的情况
     */
    @Test
    public void testPreReviewStarByReferIdsDpIdLessThanOrEqualToZero() {
        Future result = reviewWrapper.preReviewStarByReferIds(0);
        assertNull(result);
    }

    /**
     * 测试 dpId 大于0，getReviewStarByReferIds 方法执行正常的情况
     */
    @Test
    public void testPreReviewStarByReferIdsDpIdGreaterThanZeroAndMethodExecuteNormally() {
        Future expected = FutureFactory.getFuture();
        futureFactoryMockedStatic.when(FutureFactory::getFuture).thenReturn(expected);
        when(reviewDealGroupServiceV2Future.getReviewStarByReferIds(anyList())).thenReturn(null);
        Future result = reviewWrapper.preReviewStarByReferIds(1);
        assertSame(expected, result);
    }

    /**
     * 测试 dpId 大于0，getReviewStarByReferIds 方法执行过程中抛出异常的情况
     */
    @Test
    public void testPreReviewStarByReferIdsDpIdGreaterThanZeroAndMethodExecuteWithException() {
        futureFactoryMockedStatic.when(FutureFactory::getFuture).thenReturn(null);
        when(reviewDealGroupServiceV2Future.getReviewStarByReferIds(anyList())).thenThrow(new RuntimeException());
        Future result = reviewWrapper.preReviewStarByReferIds(1);
        assertNull(result);
    }
}
