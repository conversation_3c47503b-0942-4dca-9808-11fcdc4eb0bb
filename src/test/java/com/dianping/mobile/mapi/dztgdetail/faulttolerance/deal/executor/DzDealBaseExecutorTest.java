package com.dianping.mobile.mapi.dztgdetail.faulttolerance.deal.executor;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.deal.publishcategory.enums.ChannelGroupEnum;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.base.datatypes.StatusCode;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.MobileHeader;
import com.dianping.mobile.mapi.dztgdetail.biz.CreateOrderPageUrlBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.DealStyleStatisticService;
import com.dianping.mobile.mapi.dztgdetail.common.DpVersion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.CatEvents;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.Response;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule;
import com.dianping.mobile.mapi.dztgdetail.facade.DealQueryFacade;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.facade.DealQueryParallFacade;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.utils.FaultToleranceUtils;
import com.dianping.mobile.mapi.dztgdetail.helper.SwitchHelper;
import com.dianping.mobile.mapi.dztgdetail.util.AntiCrawlerUtils;
import com.dianping.mobile.mapi.dztgdetail.util.CatUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LogUtils;
import com.dianping.mobile.mapi.dztgdetail.util.VersionUtils;
import com.dianping.mobile.mapi.dztgdetail.util.dinner.DinnerDealUtils;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mafka.client.bean.MafkaProducer;
import com.sankuai.nibpt.unionlogger.UnionLoggerContext;
import java.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

/**
 * Test class for DzDealBaseExecutor#getExecuteResult
 */
@RunWith(MockitoJUnitRunner.class)
public class DzDealBaseExecutorTest {

    @InjectMocks
    private DzDealBaseExecutor executor;

    @Mock
    private DealQueryFacade dealQueryFacade;

    @Mock
    private DealQueryParallFacade dealQueryParallFacade;

    @Mock
    private CreateOrderPageUrlBiz createOrderPageUrlBiz;

    @Mock
    private DealStyleStatisticService dealStyleStatisticService;

    @Mock
    private MafkaProducer itemBrowseProducer;

    @Mock
    private IMobileContext iMobileContext;

    @Mock
    private EnvCtx envCtx;

    @Mock
    private DealBaseReq request;

    @Mock
    private MobileHeader mobileHeader;

    private static final String TEST_REGION_ID = "testRegion";

    private static final String TEST_PAGE_SOURCE = "testPageSource";

    private static final String TEST_MRN_VERSION = "0.5.6";

    private static final int TEST_CATEGORY_ID = 123;

    private static final int TEST_CLIENT_TYPE = 1;

    private static final long TEST_MT_USER_ID = 123456L;

    private static final long TEST_DP_USER_ID = 654321L;

    @Before
    public void setUp() {
        // Setup common mock behaviors
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_APP);
        when(envCtx.getUnionId()).thenReturn("unionId");
        when(envCtx.isMt()).thenReturn(true);
        when(envCtx.isLogin()).thenReturn(true);
        when(envCtx.getMtUserId()).thenReturn(TEST_MT_USER_ID);
        when(request.getRegionid()).thenReturn(TEST_REGION_ID);
        when(request.getPageSource()).thenReturn(TEST_PAGE_SOURCE);
        when(request.getMrnversion()).thenReturn(TEST_MRN_VERSION);
        when(request.getDealgroupid()).thenReturn(111);
        when(request.getCityid()).thenReturn(10);
        when(request.getLyyuserid()).thenReturn(null);
        when(request.getEventpromochannel()).thenReturn(null);
        when(request.getPass_param()).thenReturn(null);
    }

    /**
     * Test login required case
     */
    @Test
    public void testGetExecuteResult_LoginRequired() throws Throwable {
        // arrange
        when(envCtx.isLogin()).thenReturn(false);
        when(request.getMrnversion()).thenReturn("0.5.6");
        // act
        CommonMobileResponse result = executor.getExecuteResult(request, iMobileContext, envCtx, false);
        // assert
        assertNotNull("Result should not be null", result);
        Object resultData = result.getData();
        assertNotNull("Result data should not be null", resultData);
        assertTrue("Result data should be instance of DealGroupPBO", resultData instanceof DealGroupPBO);
        assertTrue("NeedLogin should be true", ((DealGroupPBO) resultData).isNeedLogin());
    }

    /**
     * Test Rhino reject case
     */
    @Test
    public void testGetExecuteResult_RhinoReject() throws Throwable {
        // arrange
        Response<DealGroupPBO> response = new Response<>();
        response.setCode(Response.RespCode.RHINO_REJECT.getVal());
        when(dealQueryParallFacade.queryDealGroup(eq(request), eq(envCtx), any())).thenReturn(response);
        // act
        CommonMobileResponse result = executor.getExecuteResult(request, iMobileContext, envCtx, false);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Should return SYSTEM_BUSY", Resps.SYSTEM_BUSY, result);
    }

    /**
     * Test null response result
     */
    @Test
    public void testGetExecuteResult_NullResult() throws Throwable {
        // arrange
        Response<DealGroupPBO> response = createSuccessResponse(null);
        when(dealQueryParallFacade.queryDealGroup(eq(request), eq(envCtx), any())).thenReturn(response);
        // act
        CommonMobileResponse result = executor.getExecuteResult(request, iMobileContext, envCtx, false);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Should return SYSTEM_ERROR", Resps.SYSTEM_ERROR, result);
    }

    /**
     * Test exception handling
     */
    @Test
    public void testGetExecuteResult_Exception() throws Throwable {
        // arrange
        when(dealQueryParallFacade.queryDealGroup(eq(request), eq(envCtx), any())).thenThrow(new RuntimeException("Test exception"));
        // act
        CommonMobileResponse result = executor.getExecuteResult(request, iMobileContext, envCtx, false);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Should return SYSTEM_ERROR", Resps.SYSTEM_ERROR, result);
    }

    private DealGroupPBO createTestDealGroupPBO() {
        DealGroupPBO dealGroupPBO = new DealGroupPBO();
        dealGroupPBO.setCategoryId(TEST_CATEGORY_ID);
        dealGroupPBO.setBuyBar(new DealBuyBar(1, Lists.newArrayList()));
        dealGroupPBO.setModuleConfigsModule(null);
        dealGroupPBO.setTitle("testTitle");
        dealGroupPBO.setMtId(123);
        dealGroupPBO.setDpId(456);
        return dealGroupPBO;
    }

    private Response<DealGroupPBO> createSuccessResponse(DealGroupPBO dealGroupPBO) {
        Response<DealGroupPBO> response = new Response<>();
        response.setCode(Response.RespCode.SUCCESS.getVal());
        response.setResult(dealGroupPBO);
        return response;
    }
}
