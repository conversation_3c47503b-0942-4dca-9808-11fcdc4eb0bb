package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.entity.PrepayCategoryConfig;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealAttrHelper_AllHolidayDisableTest {

    private static final String HOLIDAY_AVAILABLE = "holidayAvailable";

    @Mock
    private DealGroupDTO dealGroupDTO;

    @Mock
    private DealGroupCategoryDTO dealGroupCategoryDTO;

    @Mock
    private PrepayCategoryConfig prepayCategoryConfig;

    @Test
    public void testAllHolidayDisableWhenAttrsIsNull() throws Throwable {
        boolean result = DealAttrHelper.allHolidayDisable(null);
        assertFalse(result);
    }

    @Test
    public void testAllHolidayDisableWhenAttrsIsEmpty() throws Throwable {
        boolean result = DealAttrHelper.allHolidayDisable(Collections.emptyList());
        assertFalse(result);
    }

    @Test
    public void testAllHolidayDisableWhenNoHolidayAvailable() throws Throwable {
        AttributeDTO attr = new AttributeDTO();
        attr.setName("OTHER");
        attr.setValue(Arrays.asList("1", "2", "3"));
        boolean result = DealAttrHelper.allHolidayDisable(Collections.singletonList(attr));
        assertFalse(result);
    }

    @Test
    public void testAllHolidayDisableWhenHolidayAvailableButNotAllZero() throws Throwable {
        AttributeDTO attr = new AttributeDTO();
        attr.setName(HOLIDAY_AVAILABLE);
        attr.setValue(Arrays.asList("1", "0", "0"));
        boolean result = DealAttrHelper.allHolidayDisable(Collections.singletonList(attr));
        assertFalse(result);
    }

    /**
     * 测试 dealGroupDTO 为 null 的情况
     */
    @Test
    public void testIsPrepayDealDealGroupDTONull() throws Throwable {
        assertFalse(DealAttrHelper.isPrepayDeal(null));
    }

    /**
     * 测试 dealGroupDTO 的 category 为 null 的情况
     */
    @Test
    public void testIsPrepayDealCategoryNull() throws Throwable {
        when(dealGroupDTO.getCategory()).thenReturn(null);
        assertFalse(DealAttrHelper.isPrepayDeal(dealGroupDTO));
    }

    /**
     * 测试 dealGroupDTO 的 category.categoryId 为 null 的情况
     */
    @Test
    public void testIsPrepayDealCategoryIdNull() throws Throwable {
        when(dealGroupDTO.getCategory()).thenReturn(dealGroupCategoryDTO);
        when(dealGroupCategoryDTO.getCategoryId()).thenReturn(null);
        assertFalse(DealAttrHelper.isPrepayDeal(dealGroupDTO));
    }

    /**
     * 测试 dealGroupDTO 不满足 isRepairPrepayDeal 条件的情况
     */
    @Test
    public void testIsPrepayDealNotRepairPrepayDeal() throws Throwable {
        when(dealGroupDTO.getCategory()).thenReturn(dealGroupCategoryDTO);
        when(dealGroupCategoryDTO.getCategoryId()).thenReturn(1L);
        assertFalse(DealAttrHelper.isPrepayDeal(dealGroupDTO));
    }

    /**
     * 测试 dealGroupDTO 满足 isRepairPrepayDeal 条件，但 config.getCategory2TextMap().containsKey(categoryId) 返回 false 的情况
     */
    @Test
    public void testIsPrepayDealRepairPrepayDealButNotContainsKey() throws Throwable {
        when(dealGroupDTO.getCategory()).thenReturn(dealGroupCategoryDTO);
        when(dealGroupCategoryDTO.getCategoryId()).thenReturn(1L);
        Map<String, String> category2TextMap = new HashMap<>();
        category2TextMap.put("2", "test");
        assertFalse(DealAttrHelper.isPrepayDeal(dealGroupDTO));
    }
}
