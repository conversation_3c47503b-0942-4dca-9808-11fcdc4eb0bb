package com.dianping.mobile.mapi.dztgdetail.biz.processor.productdetail;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.CommonModuleWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.util.CommonModuleUtil;
import com.sankuai.dz.product.detail.gateway.api.bff.request.PageConfigRoutingKey;
import com.sankuai.dz.product.detail.gateway.spi.dto.CustomParam;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.GpsCoordinateTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.reflect.Whitebox;

@RunWith(MockitoJUnitRunner.class)
public class ProductDetailCommonModuleProcessorBuildProductDetailPageRequest1Test {

    @InjectMocks
    private ProductDetailCommonModuleProcessor processor;

    @Mock
    private CommonModuleWrapper commonModuleWrapper;

    /**
     * Helper method to invoke private method using reflection
     */
    private <T> T invokePrivateMethod(Object object, String methodName, Object... args) throws Exception {
        Method method = object.getClass().getDeclaredMethod(methodName, DealCtx.class);
        method.setAccessible(true);
        return (T) method.invoke(object, args);
    }

    /**
     * Helper method to set field value using reflection
     */
    private void setField(Object object, String fieldName, Object value) throws Exception {
        Field field = object.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(object, value);
    }

    /**
     * Test building request for Meituan APP client
     */
    @Test
    public void testBuildProductDetailPageRequest_MeituanApp() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        ctx.getEnvCtx().setUuid("test-uuid");
        // Set fields via reflection
        setField(ctx, "dpCityId", 1);
        setField(ctx, "dpDealId", 100);
        setField(ctx, "userlat", 31.2);
        setField(ctx, "userlng", 121.5);
        setField(ctx, "cityLatitude", 31.0);
        setField(ctx, "cityLongitude", 121.0);
        // act
        ProductDetailPageRequest request = invokePrivateMethod(processor, "buildProductDetailPageRequest", ctx);
        // assert
        assertNotNull(request);
        assertEquals(ClientTypeEnum.MT_APP.getCode(), request.getClientType());
        assertEquals("test-uuid", request.getShepherdGatewayParam().getDeviceId());
        assertEquals(GpsCoordinateTypeEnum.GCJ02.getCode(), request.getGpsCoordinateType());
        assertEquals("ios", request.getShepherdGatewayParam().getMobileOSType());
    }

    /**
     * Test building request for Dianping APP client
     */
    @Test
    public void testBuildProductDetailPageRequest_DianpingApp() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        ctx.getEnvCtx().setDpId("dp-123");
        // Set fields via reflection
        setField(ctx, "dpCityId", 1);
        // act
        ProductDetailPageRequest request = invokePrivateMethod(processor, "buildProductDetailPageRequest", ctx);
        // assert
        assertNotNull(request);
        assertEquals(ClientTypeEnum.DP_APP.getCode(), request.getClientType());
        assertEquals("dp-123", request.getShepherdGatewayParam().getDeviceId());
    }

    /**
     * Test building request with category information
     */
    @Test
    public void testBuildProductDetailPageRequest_WithCategory() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        when(category.getCategoryId()).thenReturn(200L);
        when(category.getServiceTypeId()).thenReturn(300L);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        setField(ctx, "dealGroupDTO", dealGroupDTO);
        // act
        ProductDetailPageRequest request = invokePrivateMethod(processor, "buildProductDetailPageRequest", ctx);
        // assert
        assertNotNull(request);
        // Verify PageConfigRoutingKey
        PageConfigRoutingKey routingKey = request.getPageConfigRoutingKey();
        assertEquals(200, routingKey.getProductSecondCategoryId());
        assertEquals(300, routingKey.getProductThirdCategoryId());
        // Since we can't directly access CustomParam's internal map, we'll verify the request is properly built
        assertEquals(ProductTypeEnum.DEAL.getCode(), request.getProductType());
    }

    /**
     * Test building request for Meituan Mini Program client
     */
    @Test
    public void testBuildProductDetailPageRequest_MeituanMiniProgram() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP);
        setField(ctx, "wxVersion", "8.0.0");
        // act
        ProductDetailPageRequest request = invokePrivateMethod(processor, "buildProductDetailPageRequest", ctx);
        // assert
        assertNotNull(request);
        assertEquals(ClientTypeEnum.MT_XCX.getCode(), request.getClientType());
        assertEquals("8.0.0", request.getShepherdGatewayParam().getCsecversionname());
    }

    /**
     * Test building request with shop information
     */
    @Test
    public void testBuildProductDetailPageRequest_WithShopInfo() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        setField(ctx, "dpLongShopId", 88888L);
        setField(ctx, "skuId", "666");
        // act
        ProductDetailPageRequest request = invokePrivateMethod(processor, "buildProductDetailPageRequest", ctx);
        // assert
        assertNotNull(request);
        assertEquals(88888L, request.getPoiId());
        assertEquals(666L, request.getSkuId());
    }

    /**
     * Test building request with null category
     */
    @Test
    public void testBuildProductDetailPageRequest_NullCategory() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getCategory()).thenReturn(null);
        setField(ctx, "dealGroupDTO", dealGroupDTO);
        // act
        ProductDetailPageRequest request = invokePrivateMethod(processor, "buildProductDetailPageRequest", ctx);
        // assert
        assertNotNull(request);
        // Verify the request is properly built even with null category
        assertEquals(ProductTypeEnum.DEAL.getCode(), request.getProductType());
        // Verify PageConfigRoutingKey is still set with default values
        PageConfigRoutingKey routingKey = request.getPageConfigRoutingKey();
        assertNotNull(routingKey);
    }

    /**
     * Test building request with all user information
     */
    @Test
    public void testBuildProductDetailPageRequest_WithUserInfo() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.getEnvCtx().setMtUserId(10000L);
        ctx.getEnvCtx().setDpUserId(20000L);
        ctx.getEnvCtx().setMtVirtualUserId(30000L);
        ctx.getEnvCtx().setDpVirtualUserId(40000L);
        ctx.getEnvCtx().setUnionId("union-123");
        ctx.getEnvCtx().setVersion("9.9.9");
        // act
        ProductDetailPageRequest request = invokePrivateMethod(processor, "buildProductDetailPageRequest", ctx);
        // assert
        assertNotNull(request);
        ShepherdGatewayParam param = request.getShepherdGatewayParam();
        assertEquals(10000L, param.getMtUserId());
        assertEquals(20000L, param.getDpUserId());
        assertEquals(30000L, param.getMtVirtualUserId());
        assertEquals(40000L, param.getDpVirtualUserId());
        assertEquals("union-123", param.getUnionid());
        assertEquals("9.9.9", param.getAppVersion());
    }

    /**
     * Test building request with request source
     */
    @Test
    public void testBuildProductDetailPageRequest_WithRequestSource() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        setField(ctx, "requestSource", "test-source");
        setField(ctx, "cx", "test-cx");
        setField(ctx, "gpsCityId", 123);
        // act
        ProductDetailPageRequest request = invokePrivateMethod(processor, "buildProductDetailPageRequest", ctx);
        // assert
        assertNotNull(request);
        assertEquals("test-source", request.getPageSource());
        assertEquals("test-cx", request.getCx());
        assertEquals(123, request.getGpsCityId());
    }

    /**
     * Test building request with Dianping Weixin Mini Program client
     */
    @Test
    public void testBuildProductDetailPageRequest_DianpingWeixinMiniProgram() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP);
        // act
        ProductDetailPageRequest request = invokePrivateMethod(processor, "buildProductDetailPageRequest", ctx);
        // assert
        assertNotNull(request);
        assertEquals(ClientTypeEnum.DP_XCX.getCode(), request.getClientType());
    }

    /**
     * Test building request with unknown client type
     */
    @Test
    public void testBuildProductDetailPageRequest_UnknownClientType() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.getEnvCtx().setDztgClientTypeEnum(null);
        // act
        ProductDetailPageRequest request = invokePrivateMethod(processor, "buildProductDetailPageRequest", ctx);
        // assert
        assertNotNull(request);
        assertEquals(ClientTypeEnum.UNKNOWN.getCode(), request.getClientType());
    }
}
