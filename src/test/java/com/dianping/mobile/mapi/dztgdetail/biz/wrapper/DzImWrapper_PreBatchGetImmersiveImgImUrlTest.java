package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.dzim.cliententry.ClientEntryService;
import com.sankuai.dzim.cliententry.dto.BatchClientEntryWithSendUnitReqDTO;
import com.sankuai.dzim.cliententry.dto.ClientEntryDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Future;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DzImWrapper_PreBatchGetImmersiveImgImUrlTest {

    @InjectMocks
    private DzImWrapper dzImWrapper;

    @Mock
    private ClientEntryService clientEntryService;

    /**
     * 测试dpShopId无效的情况
     */
    @Test
    public void testPreBatchGetImmersiveImgImUrlWithInvalidDpShopId() throws Throwable {
        Future result = dzImWrapper.preBatchGetImmersiveImgImUrl(-1L, Arrays.asList(1L, 2L, 3L), 1);
        assertNull(result);
    }

    /**
     * 测试contentIds为空的情况
     */
    @Test
    public void testPreBatchGetImmersiveImgImUrlWithEmptyContentIds() throws Throwable {
        Future result = dzImWrapper.preBatchGetImmersiveImgImUrl(1L, Arrays.asList(), 1);
        assertNull(result);
    }

    /**
     * 测试clientEntryService.batchGetClientEntryWithSendUnit方法抛出异常的情况
     */
    @Test
    public void testPreBatchGetImmersiveImgImUrlWithException() throws Throwable {
        doThrow(new RuntimeException()).when(clientEntryService).batchGetClientEntryWithSendUnit(any(BatchClientEntryWithSendUnitReqDTO.class));
        Future result = dzImWrapper.preBatchGetImmersiveImgImUrl(1L, Arrays.asList(1L, 2L, 3L), 1);
        assertNull(result);
    }
}
