package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.tag.dto.ProductTag;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import org.mockito.InjectMocks;

public class ProductTagWrapper_QueryDealGroupTagTest {

    @Mock
    private Future futureMock;

    private ProductTagWrapper productTagWrapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        productTagWrapper = new ProductTagWrapper();
    }

    @After
    public void tearDown() {
        reset(futureMock);
    }

    @Test
    public void testQueryDealGroupTagFutureIsNull() throws Throwable {
        Long productId = 1L;
        List<Long> result = productTagWrapper.queryDealGroupTag(productId, null);
        assertTrue("Result should be empty when future is null", result.isEmpty());
    }

    @Test
    public void testQueryDealGroupTagFutureResultIsEmpty() throws Throwable {
        Long productId = 1L;
        when(futureMock.get()).thenReturn(new HashMap<>());
        List<Long> result = productTagWrapper.queryDealGroupTag(productId, futureMock);
        assertTrue("Result should be empty when future result is empty", result.isEmpty());
    }

    @Test
    public void testQueryDealGroupTagProductIdNotInFutureResult() throws Throwable {
        Long productId = 1L;
        Map<Long, List<ProductTag>> futureResult = new HashMap<>();
        futureResult.put(2L, Collections.singletonList(new ProductTag()));
        when(futureMock.get()).thenReturn(futureResult);
        List<Long> result = productTagWrapper.queryDealGroupTag(productId, futureMock);
        assertTrue("Result should be empty when productId is not in the future result", result.isEmpty());
    }

    @Test
    public void testQueryDealGroupTagProductTagsPresentAndValid() throws Throwable {
        Long productId = 1L;
        ProductTag validTag = new ProductTag();
        validTag.setTagId(10L);
        validTag.setStatus(1);
        Map<Long, List<ProductTag>> futureResult = new HashMap<>();
        futureResult.put(productId, Collections.singletonList(validTag));
        when(futureMock.get()).thenReturn(futureResult);
        List<Long> result = productTagWrapper.queryDealGroupTag(productId, futureMock);
        assertFalse("Result should not be empty when valid product tags are present", result.isEmpty());
        assertEquals("Result should contain the valid tag id", Long.valueOf(10L), result.stream().findFirst().orElse(null));
    }

    @Test
    public void testQueryDealGroupTagProductTagsPresentButInvalid() throws Throwable {
        Long productId = 1L;
        ProductTag invalidTag = new ProductTag();
        invalidTag.setTagId(10L);
        invalidTag.setStatus(0);
        Map<Long, List<ProductTag>> futureResult = new HashMap<>();
        futureResult.put(productId, Collections.singletonList(invalidTag));
        when(futureMock.get()).thenReturn(futureResult);
        List<Long> result = productTagWrapper.queryDealGroupTag(productId, futureMock);
        assertTrue("Result should be empty when product tags are present but invalid", result.isEmpty());
    }
}
