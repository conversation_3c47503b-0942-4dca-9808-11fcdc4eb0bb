package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.DecryptBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DzImWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ProductTagWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.SkuWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.ShopUuidUtils;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Cons;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedDeals;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.DecryptVO;
import com.dianping.mobile.mapi.dztgdetail.entity.ExpResultConfig;
import com.dianping.mobile.mapi.dztgdetail.tab.RelateDeals;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealQueryFacade_BuildCompareSameShopPriceStyleAb_1_Test {

    @InjectMocks
    private DealQueryFacade dealQueryFacade;

    @Mock
    private DouHuService douHuService;

    @Mock
    private LionConfigUtils lionConfigUtils;

    @Mock
    private SkuWrapper skuWrapper;


    @Mock
    private DecryptBiz decryptBiz;

    @Mock
    private GreyUtils greyUtils;

    @Mock
    private ProductTagWrapper productTagWrapper;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private DealBaseReq req;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private DealGroupBaseDTO dealGroupBase;

    @Mock
    private DzImWrapper dzImWrapper;

    @Mock
    private Future future;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    // Helper method to invoke private method using reflection
    private boolean invokePrivateMethod(DealQueryFacade facade, String methodName, List<AttributeDTO> attributeDTOS) throws Throwable {
        try {
            Method method = DealQueryFacade.class.getDeclaredMethod(methodName, List.class);
            method.setAccessible(true);
            return (boolean) method.invoke(facade, attributeDTOS);
        } catch (Exception e) {
            throw e.getCause();
        }
    }

    private int invokePrivateMethod(Object object, String methodName, Object... args) throws Throwable {
        Method method = object.getClass().getDeclaredMethod(methodName, DealGroupDTO.class);
        method.setAccessible(true);
        return (int) method.invoke(object, args);
    }

    /**
     * 测试场景：deals 的 generalInfo 不等于 Cons.CARD_STYLE
     */
    @Test
    public void testBuildCompareSameShopPriceStyleAb_GeneralInfoNotCardStyle() throws Throwable {
        EnvCtx envCtx = new EnvCtx();
        RelatedDeals deals = new RelatedDeals();
        deals.setGeneralInfo("NotCardStyle");
        dealQueryFacade.buildCompareSameShopPriceStyleAb(envCtx, deals, 1);
        verify(douHuService, never()).getCompareSameShopPriceStyleAbConfigByEnvCtx(anyInt(), any(EnvCtx.class));
    }

    /**
     * 测试场景：deals 的 generalInfo 等于 Cons.CARD_STYLE，并且 douHuService.getCompareSameShopPriceStyleAbConfigByEnvCtx 返回 null
     */
    @Test
    public void testBuildCompareSameShopPriceStyleAb_GeneralInfoCardStyleAndConfigNull() throws Throwable {
        EnvCtx envCtx = new EnvCtx();
        RelatedDeals deals = new RelatedDeals();
        // Ensure this matches the constant value
        deals.setGeneralInfo(Cons.CARD_STYLE);
        when(douHuService.getCompareSameShopPriceStyleAbConfigByEnvCtx(anyInt(), any(EnvCtx.class))).thenReturn(null);
        dealQueryFacade.buildCompareSameShopPriceStyleAb(envCtx, deals, 1);
        verify(douHuService).getCompareSameShopPriceStyleAbConfigByEnvCtx(anyInt(), any(EnvCtx.class));
    }

    /**
     * 测试场景：deals 的 generalInfo 等于 Cons.CARD_STYLE，并且 douHuService.getCompareSameShopPriceStyleAbConfigByEnvCtx 返回非 null 的 ModuleAbConfig 对象
     */
    @Test
    public void testBuildCompareSameShopPriceStyleAb_GeneralInfoCardStyleAndConfigNotNull() throws Throwable {
        EnvCtx envCtx = new EnvCtx();
        RelatedDeals deals = new RelatedDeals();
        // Ensure this matches the constant value
        deals.setGeneralInfo(Cons.CARD_STYLE);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        when(douHuService.getCompareSameShopPriceStyleAbConfigByEnvCtx(anyInt(), any(EnvCtx.class))).thenReturn(moduleAbConfig);
        dealQueryFacade.buildCompareSameShopPriceStyleAb(envCtx, deals, 1);
        verify(douHuService).getCompareSameShopPriceStyleAbConfigByEnvCtx(anyInt(), any(EnvCtx.class));
    }

    /**
     * Test case for empty list.
     */
    @Test
    public void testIsStandardDealGroupEmptyList() throws Throwable {
        // arrange
        DealQueryFacade facade = new DealQueryFacade();
        List<AttributeDTO> attributeDTOS = Collections.emptyList();
        // act
        boolean result = invokePrivateMethod(facade, "isStandardDealGroup", attributeDTOS);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for null list.
     */
    @Test
    public void testIsStandardDealGroupNullList() throws Throwable {
        // arrange
        DealQueryFacade facade = new DealQueryFacade();
        List<AttributeDTO> attributeDTOS = null;
        // act
        boolean result = invokePrivateMethod(facade, "isStandardDealGroup", attributeDTOS);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for attribute not found.
     */
    @Test
    public void testIsStandardDealGroupAttributeNotFound() throws Throwable {
        // arrange
        DealQueryFacade facade = new DealQueryFacade();
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("otherAttribute");
        attributeDTO.setValue(Arrays.asList("value1", "value2"));
        List<AttributeDTO> attributeDTOS = Collections.singletonList(attributeDTO);
        // act
        boolean result = invokePrivateMethod(facade, "isStandardDealGroup", attributeDTOS);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for attribute found, value does not contain "1".
     */
    @Test
    public void testIsStandardDealGroupAttributeFoundValueDoesNotContain1() throws Throwable {
        // arrange
        DealQueryFacade facade = new DealQueryFacade();
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("standardDealGroup");
        attributeDTO.setValue(Arrays.asList("value1", "value2"));
        List<AttributeDTO> attributeDTOS = Collections.singletonList(attributeDTO);
        // act
        boolean result = invokePrivateMethod(facade, "isStandardDealGroup", attributeDTOS);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for attribute found, value contains "1".
     */
    @Test
    public void testIsStandardDealGroupAttributeFoundValueContains1() throws Throwable {
        // arrange
        DealQueryFacade facade = new DealQueryFacade();
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("standardDealGroup");
        attributeDTO.setValue(Arrays.asList("value1", "1"));
        List<AttributeDTO> attributeDTOS = Collections.singletonList(attributeDTO);
        // act
        boolean result = invokePrivateMethod(facade, "isStandardDealGroup", attributeDTOS);
        // assert
        assertTrue(result);
    }

    /**
     * Test case for null attribute in list.
     */
    @Test
    public void testIsStandardDealGroupNullAttributeInList() throws Throwable {
        // arrange
        DealQueryFacade facade = new DealQueryFacade();
        List<AttributeDTO> attributeDTOS = Arrays.asList(null, new AttributeDTO());
        // act
        boolean result = invokePrivateMethod(facade, "isStandardDealGroup", attributeDTOS);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for attribute with null value.
     */
    @Test
    public void testIsStandardDealGroupAttributeWithNullValue() throws Throwable {
        // arrange
        DealQueryFacade facade = new DealQueryFacade();
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("standardDealGroup");
        attributeDTO.setValue(null);
        List<AttributeDTO> attributeDTOS = Collections.singletonList(attributeDTO);
        // act
        boolean result = invokePrivateMethod(facade, "isStandardDealGroup", attributeDTOS);
        // assert
        assertFalse(result);
    }

    /**
     * Test case when experiment result is "c" (not "a", "b", or "d").
     */
    @Test
    public void testBuildCompareSameShopPriceStyleAb_ExpResultC_NoAction() throws Throwable {
        // arrange
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        RelatedDeals deals = new RelatedDeals();
        deals.setModuleAbConfigs(new java.util.ArrayList<>());
        Integer categoryId = 1;
        when(douHuService.getExpResult(moduleAbConfig)).thenReturn("c");
        // act
        dealQueryFacade.buildCompareSameShopPriceStyleAb(moduleAbConfig, deals, categoryId);
        // assert
        assertNull(deals.getTabStyle());
        assertTrue(deals.getModuleAbConfigs().isEmpty());
    }

    /**
     * Test case when douHuService.getExpResult throws an exception.
     */
    @Test(expected = RuntimeException.class)
    public void testBuildCompareSameShopPriceStyleAb_ExpResultException() throws Throwable {
        // arrange
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        RelatedDeals deals = new RelatedDeals();
        deals.setModuleAbConfigs(new java.util.ArrayList<>());
        Integer categoryId = 1;
        when(douHuService.getExpResult(moduleAbConfig)).thenThrow(new RuntimeException("Service error"));
        // act
        dealQueryFacade.buildCompareSameShopPriceStyleAb(moduleAbConfig, deals, categoryId);
        // assert
        // Exception is expected
    }

    /**
     * 测试 getMliveId 方法返回有效值
     */
    @Test
    public void testInitDealsBaseData_GetMliveIdReturnsValidValue() throws Throwable {
        // arrange
        DealBaseReq req = new DealBaseReq();
        req.setPass_param("{\"PROMOTE_CHANNEL_INFO\":{\"promoteExtend\":{\"mLiveId\":\"123\"}}}");
        // Initialize dealgroupid to avoid NullPointerException
        req.setDealgroupid(123);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        // Mock the getMliveId method to return a valid value
        // when(dealQueryFacade.getMliveId("{\"PROMOTE_CHANNEL_INFO\":{\"promoteExtend\":{\"mLiveId\":\"123\"}}}")).thenReturn(123L);
        // act
        DealCtx dealCtx = dealQueryFacade.initDealsBaseData(req, envCtx);
        // assert
        assertEquals(123L, dealCtx.getMLiveId());
    }

    /**
     * Test case for null DealGroupDTO.
     */
    @Test
    public void testGetPublishCategoryId_NullDealGroupDTO() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = null;
        // act
        int result = invokePrivateMethod(dealQueryFacade, "getPublishCategoryId", dealGroupDTO);
        // assert
        assertEquals(0, result);
    }

    /**
     * Test case for null Category in DealGroupDTO.
     */
    @Test
    public void testGetPublishCategoryId_NullCategory() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        // act
        int result = invokePrivateMethod(dealQueryFacade, "getPublishCategoryId", dealGroupDTO);
        // assert
        assertEquals(0, result);
    }

    /**
     * Test case for null CategoryId in Category.
     */
    @Test
    public void testGetPublishCategoryId_NullCategoryId() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        // act
        int result = invokePrivateMethod(dealQueryFacade, "getPublishCategoryId", dealGroupDTO);
        // assert
        assertEquals(0, result);
    }

    /**
     * Test case for valid CategoryId in Category.
     */
    @Test
    public void testGetPublishCategoryId_ValidCategoryId() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO category = new DealGroupCategoryDTO();
        dealGroupDTO.setCategory(category);
        category.setCategoryId(123L);
        // act
        int result = invokePrivateMethod(dealQueryFacade, "getPublishCategoryId", dealGroupDTO);
        // assert
        assertEquals(123, result);
    }

    /**
     * 测试场景1：deals.getGeneralInfo() 等于 Cons.CARD_STYLE，douHuService.getCompareSameShopPriceStyleAbConfigByEnvCtx 返回非空 ModuleAbConfig
     */
    @Test
    public void testBuildCompareSameShopPriceStyleAb_WhenGeneralInfoEqualsCardStyle_AndModuleAbConfigNotNull() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        RelatedDeals deals = mock(RelatedDeals.class);
        Integer categoryId = 1;
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        when(deals.getGeneralInfo()).thenReturn(Cons.CARD_STYLE);
        when(douHuService.getCompareSameShopPriceStyleAbConfigByEnvCtx(categoryId, envCtx)).thenReturn(moduleAbConfig);
        // act
        dealQueryFacade.buildCompareSameShopPriceStyleAb(envCtx, deals, categoryId);
        // assert
        verify(douHuService).getCompareSameShopPriceStyleAbConfigByEnvCtx(categoryId, envCtx);
        verify(deals).getGeneralInfo();
    }

    /**
     * 测试场景2：deals.getGeneralInfo() 等于 Cons.CARD_STYLE，douHuService.getCompareSameShopPriceStyleAbConfigByEnvCtx 返回空 ModuleAbConfig
     */
    @Test
    public void testBuildCompareSameShopPriceStyleAb_WhenGeneralInfoEqualsCardStyle_AndModuleAbConfigNull() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        RelatedDeals deals = mock(RelatedDeals.class);
        Integer categoryId = 1;
        when(deals.getGeneralInfo()).thenReturn(Cons.CARD_STYLE);
        when(douHuService.getCompareSameShopPriceStyleAbConfigByEnvCtx(categoryId, envCtx)).thenReturn(null);
        // act
        dealQueryFacade.buildCompareSameShopPriceStyleAb(envCtx, deals, categoryId);
        // assert
        verify(douHuService).getCompareSameShopPriceStyleAbConfigByEnvCtx(categoryId, envCtx);
        verify(deals).getGeneralInfo();
    }

    /**
     * 测试场景3：deals.getGeneralInfo() 不等于 Cons.CARD_STYLE，方法直接返回，不执行任何操作
     */
    @Test
    public void testBuildCompareSameShopPriceStyleAb_WhenGeneralInfoNotEqualsCardStyle() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        RelatedDeals deals = mock(RelatedDeals.class);
        Integer categoryId = 1;
        when(deals.getGeneralInfo()).thenReturn("OTHER_STYLE");
        // act
        dealQueryFacade.buildCompareSameShopPriceStyleAb(envCtx, deals, categoryId);
        // assert
        verify(deals).getGeneralInfo();
        verifyNoMoreInteractions(douHuService);
    }

    /**
     * Test case for null input where req is null.
     */
    @Test
    public void testProcessMarketPriceDisplayStatus_NullReq() throws Throwable {
        // arrange
        DealCtx dealCtx = mock(DealCtx.class);
        // act
        dealQueryFacade.processMarketPriceDisplayStatus(null, dealCtx);
        // assert
        verify(dealCtx, never()).setMarketPriceHided(anyBoolean());
        verify(dealCtx, never()).setMarketPriceNormalButtonHide(anyBoolean());
    }

    /**
     * Test case for null input where dealCtx is null.
     */
    @Test
    public void testProcessMarketPriceDisplayStatus_NullDealCtx() throws Throwable {
        // arrange
        DealBaseReq req = mock(DealBaseReq.class);
        // act
        dealQueryFacade.processMarketPriceDisplayStatus(req, null);
        // assert
        verify(req, never()).getDealgroupid();
        verify(dealGroupWrapper, never()).getDpDealGroupId(anyInt());
    }
}
