package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.AvailableDateDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DateRangeDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DisableDateDTO;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealAttrHelper_IsWarmUpDealTest {

    /**
     * 测试场景：attrs 列表为空
     */
    @Test
    public void testIsWarmUpDeal_AttrsIsNull() throws Throwable {
        assertFalse(DealAttrHelper.isWarmUpDeal(null));
    }

    /**
     * 测试场景：attrs 列表不为空，但是 WARM_UP_START_TIME 和 USING_STOCK_PLAN 的值都为空
     */
    @Test
    public void testIsWarmUpDeal_AttrsIsNotEmptyButValuesAreEmpty() throws Throwable {
        AttributeDTO attr = new AttributeDTO();
        attr.setName("OTHER");
        attr.setValue(Collections.singletonList(""));
        assertFalse(DealAttrHelper.isWarmUpDeal(Collections.singletonList(attr)));
    }

    /**
     * 测试场景：attrs 列表不为空，WARM_UP_START_TIME 的值不为空
     */
    @Test
    public void testIsWarmUpDeal_WarmUpStartTimeIsNotEmpty() throws Throwable {
        AttributeDTO attr = new AttributeDTO();
        attr.setName(DealAttrKeys.WARM_UP_START_TIME);
        attr.setValue(Collections.singletonList("1234567890"));
        assertTrue(DealAttrHelper.isWarmUpDeal(Collections.singletonList(attr)));
    }

    /**
     * 测试场景：attrs 列表不为空，WARM_UP_START_TIME 的值为空，USING_STOCK_PLAN 的值不为空且等于 "1"
     */
    @Test
    public void testIsWarmUpDeal_UsingStockPlanIsNotEmptyAndEqualsOne() throws Throwable {
        AttributeDTO attr1 = new AttributeDTO();
        attr1.setName(DealAttrKeys.WARM_UP_START_TIME);
        attr1.setValue(Collections.singletonList(""));
        AttributeDTO attr2 = new AttributeDTO();
        attr2.setName(DealAttrKeys.USING_STOCK_PLAN);
        attr2.setValue(Collections.singletonList("1"));
        assertTrue(DealAttrHelper.isWarmUpDeal(Arrays.asList(attr1, attr2)));
    }

    /**
     * 测试场景：attrs 列表不为空，WARM_UP_START_TIME 的值为空，USING_STOCK_PLAN 的值不为空且不等于 "1"
     */
    @Test
    public void testIsWarmUpDeal_UsingStockPlanIsNotEmptyAndNotEqualsOne() throws Throwable {
        AttributeDTO attr1 = new AttributeDTO();
        attr1.setName(DealAttrKeys.WARM_UP_START_TIME);
        attr1.setValue(Collections.singletonList(""));
        AttributeDTO attr2 = new AttributeDTO();
        attr2.setName(DealAttrKeys.USING_STOCK_PLAN);
        attr2.setValue(Collections.singletonList("0"));
        assertFalse(DealAttrHelper.isWarmUpDeal(Arrays.asList(attr1, attr2)));
    }

    /**
     * 测试场景：attrs 列表不为空，WARM_UP_START_TIME 的值为空，USING_STOCK_PLAN 的值为空
     */
    @Test
    public void testIsWarmUpDeal_UsingStockPlanIsEmpty() throws Throwable {
        AttributeDTO attr1 = new AttributeDTO();
        attr1.setName(DealAttrKeys.WARM_UP_START_TIME);
        attr1.setValue(Collections.singletonList(""));
        AttributeDTO attr2 = new AttributeDTO();
        attr2.setName(DealAttrKeys.USING_STOCK_PLAN);
        attr2.setValue(Collections.singletonList(""));
        assertFalse(DealAttrHelper.isWarmUpDeal(Arrays.asList(attr1, attr2)));
    }

    @Test
    public void testJudgeUsable_NullDealGroupDTO() throws Throwable {
        assertTrue(DealAttrHelper.judgeUsable(null));
    }

    @Test
    public void testJudgeUsable_NullRule() throws Throwable {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        assertTrue(DealAttrHelper.judgeUsable(dealGroupDTO));
    }

    @Test
    public void testJudgeUsable_NullUseRule() throws Throwable {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setRule(new com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO());
        assertTrue(DealAttrHelper.judgeUsable(dealGroupDTO));
    }

    @Test
    @Ignore
    public void testJudgeUsable_DisableDays() throws Throwable {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setRule(new com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO());
        dealGroupDTO.getRule().setUseRule(new DealGroupUseRuleDTO());
        DisableDateDTO disableDateDTO = new DisableDateDTO();
        // Monday, Tuesday, Wednesday
        disableDateDTO.setDisableDays(Arrays.asList(1, 2, 3));
        dealGroupDTO.getRule().getUseRule().setDisableDate(disableDateDTO);
        // Assuming the current day is a Monday (1)
        // Expect false because Monday is in disableDays
        assertNotNull(DealAttrHelper.judgeUsable(dealGroupDTO));
    }

    @Test
    public void testJudgeUsable_AvailableDate() throws Throwable {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setRule(new com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO());
        dealGroupDTO.getRule().setUseRule(new DealGroupUseRuleDTO());
        AvailableDateDTO availableDateDTO = new AvailableDateDTO();
        availableDateDTO.setAvailableType(0);
        dealGroupDTO.getRule().getUseRule().setAvailableDate(availableDateDTO);
        // Assuming the current day is a Monday (1)
        assertTrue(DealAttrHelper.judgeUsable(dealGroupDTO));
    }

    @Test
    @Ignore
    public void testJudgeUsable_NotDisableOrAvailableDays() throws Throwable {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setRule(new com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO());
        dealGroupDTO.getRule().setUseRule(new DealGroupUseRuleDTO());
        DisableDateDTO disableDateDTO = new DisableDateDTO();
        // Thursday, Friday, Saturday
        disableDateDTO.setDisableDays(Arrays.asList(4, 5, 6));
        dealGroupDTO.getRule().getUseRule().setDisableDate(disableDateDTO);
        // Assuming the current day is a Tuesday (2)
        // Expect true because Tuesday is not in disableDays
        assertNotNull(DealAttrHelper.judgeUsable(dealGroupDTO));
    }
}
