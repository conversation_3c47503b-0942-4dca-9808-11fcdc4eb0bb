package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.service.DpPoiService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class PoiClientWrapper_GetDpPoiDTOTest {

    @InjectMocks
    private PoiClientWrapper poiClientWrapper;

    @Mock
    private DpPoiService dpPoiService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 getDpPoiDTO 方法，当 dpPoiService.findShopsByShopIds 返回非空列表时，应返回列表中的第一个元素
     */
    @Test
    public void testGetDpPoiDTO_NonEmptyList() throws Throwable {
        // arrange
        long dpShopId = 1L;
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        try {
            when(dpPoiService.findShopsByShopIds(any(DpPoiRequest.class))).thenReturn(Arrays.asList(dpPoiDTO));
        } catch (Exception e) {
            // Handle or log the exception as needed for your use case
        }
        // act
        DpPoiDTO result = poiClientWrapper.getDpPoiDTO(dpShopId, Collections.emptyList());
        // assert
        assertEquals(dpPoiDTO, result);
    }

    /**
     * 测试 getDpPoiDTO 方法，当 dpPoiService.findShopsByShopIds 返回空列表时，应返回 null
     */
    @Test
    public void testGetDpPoiDTO_EmptyList() throws Throwable {
        // arrange
        long dpShopId = 1L;
        try {
            when(dpPoiService.findShopsByShopIds(any(DpPoiRequest.class))).thenReturn(Collections.emptyList());
        } catch (Exception e) {
            // Handle or log the exception as needed for your use case
        }
        // act
        DpPoiDTO result = poiClientWrapper.getDpPoiDTO(dpShopId, Collections.emptyList());
        // assert
        assertNull(result);
    }
}
