package com.dianping.mobile.mapi.dztgdetail.biz.service;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ Lion.class })
public class DouHuServiceHitEnableCardStyleV2Test {

    private DouHuService douHuService;

    @Before
    public void setUp() {
        douHuService = new DouHuService();
        PowerMockito.mockStatic(Lion.class);
    }

    /**
     * 测试 moduleAbConfig 为 null 且 requestSource 不是 TRADE_SNAPSHOT 的情况
     */
    @Test
    public void testHitEnableCardStyleV2_NullModuleAbConfigAndNotTradeSnapshot() throws Throwable {
        // arrange
        String requestSource = "OTHER_SOURCE";
        // act
        boolean result = douHuService.hitEnableCardStyleV2(null, requestSource);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 moduleAbConfig 为 null 但 requestSource 是 TRADE_SNAPSHOT 的情况
     */
    @Test
    public void testHitEnableCardStyleV2_NullModuleAbConfigButIsTradeSnapshot() throws Throwable {
        // arrange
        String requestSource = RequestSourceEnum.TRADE_SNAPSHOT.getSource();
        // act
        boolean result = douHuService.hitEnableCardStyleV2(null, requestSource);
        // assert
        assertTrue(result);
    }
}
