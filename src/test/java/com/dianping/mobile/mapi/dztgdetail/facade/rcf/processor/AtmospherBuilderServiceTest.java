package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.PriceContext;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.AtmospherBuilderService;
import com.dianping.mobile.mapi.dztgdetail.util.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Mockito.mockStatic;

/**
 * @Author: <EMAIL>
 * @Date: 2024/6/10
 */
@RunWith(MockitoJUnitRunner.class)
public class AtmospherBuilderServiceTest {
    @InjectMocks
    private AtmospherBuilderService atmospherBuilderService;
    @Mock
    private DouHuBiz douHuBiz;

    private MockedStatic<Lion> lionMockedStatic;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        lionMockedStatic = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        lionMockedStatic.close();
    }

    @Test
    public void testBuildAtmospherBar() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(200502);
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(303);
        ctx.setChannelDTO(channelDTO);
        ctx.setPriceContext(new PriceContext());
        DealGroupBaseDTO dealGroupBaseDTO = new DealGroupBaseDTO();
        dealGroupBaseDTO.setDealGroupPrice(new BigDecimal("100"));
        dealGroupBaseDTO.setMarketPrice(new BigDecimal("200"));
        ctx.setDealGroupBase(dealGroupBaseDTO);

        DealGroupPBO dealGroupPBO = JsonUtils.fromJson("{\"abConfigModel\":{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"ed18c747-b8f5-4fef-8caf-b5b4b707e227\\\",\\\"ab_id\\\":\\\"exp000447_a\\\"}\",\"expId\":\"exp000447\",\"expResult\":\"a\",\"useNewStyle\":false}],\"key\":\"MTSalesColorExp\"},\"bgName\":\"general\",\"categoryId\":303,\"dealContents\":[{\"content\":\"https://p0.meituan.net/merchantpic/26252927e384eb75a8b6c1fad6f69ef139667.jpg%40960w_540h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D2%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\",\"scale\":\"16:9\",\"type\":1}],\"dpDealId\":1228833347,\"dpId\":*********,\"features\":[\"买贵必赔\",\"随时退\",\"过期退\"],\"guarantee\":[{\"icon\":\"https://p1.meituan.net/travelcube/409cdc9bf49ebac30967bf41d62665781754.png\",\"style\":\"#8E3C12\",\"text\":\"买贵必赔\",\"type\":1},{\"text\":\"随时退\",\"type\":0},{\"text\":\"过期退\",\"type\":0}],\"hasReserveEntrance\":false,\"hitStructuredPurchaseNote\":false,\"maxPerUser\":0,\"meetPurchaseLimit\":false,\"moduleAbConfigs\":[{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"5f922c02-073d-4227-81a5-08d1ae9da1e3\\\",\\\"ab_id\\\":\\\"exp002291_b\\\"}\",\"expId\":\"exp002291\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"MTSalesCaliberIsQueryNew\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"0778b9da-0cb1-4cce-9034-80e0c00417ef\\\",\\\"ab_id\\\":\\\"exp002027_b\\\"}\",\"expId\":\"exp002027\",\"expResult\":\"exp002027_b\",\"useNewStyle\":false}],\"key\":\"MTZuLiaoShowMarketPrice\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"c3cb06f6-a298-4436-9f76-6d08a366b7b5\\\",\\\"ab_id\\\":\\\"exp002597_a\\\"}\",\"expId\":\"exp002597\",\"expResult\":\"a\",\"useNewStyle\":false}],\"key\":\"MTMassageOverNight\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"765b63a7-3ee9-41ed-8a6d-d3dcaa132ce4\\\",\\\"ab_id\\\":\\\"exp000686_b\\\"}\",\"expId\":\"exp000686\",\"expResult\":\"exp000686_b\",\"useNewStyle\":false}],\"key\":\"MTJoyCardPriceExp\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"5ab84623-7a34-4cc0-a6a5-64c2c1ce6546\\\",\\\"ab_id\\\":\\\"exp001930_a\\\"}\",\"expId\":\"exp001930\",\"expResult\":\"a\",\"useNewStyle\":false}],\"key\":\"MTZuLiaoHuoJiaDirectPurchase\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"cba0a626-8139-48f8-98d5-4c4700a0b144\\\",\\\"ab_id\\\":\\\"exp002193_b\\\"}\",\"expId\":\"exp002193\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"MTShoppingCartBuyBar\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"32ea92f4-5e42-45e4-938c-a4c6640f5ee3\\\",\\\"ab_id\\\":\\\"exp002463_a\\\"}\",\"expId\":\"exp002463\",\"expResult\":\"a\",\"useNewStyle\":false}],\"key\":\"MTShoppingCartBuyBarNew\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"b5622837-bda7-483e-8c6f-e84e8d078388\\\",\\\"ab_id\\\":\\\"exp002529_c\\\"}\",\"expId\":\"exp002529\",\"expResult\":\"c\",\"useNewStyle\":false}],\"key\":\"MTCouponBar\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"f5b87e0f-70e9-4316-86a6-e607e13e7c05\\\",\\\"ab_id\\\":\\\"exp003198_b\\\"}\",\"expId\":\"exp003198\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"MtPurchaseNoteStructure\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"140e44d8-a515-4969-96be-1a5e0e2f68af\\\",\\\"ab_id\\\":\\\"exp002833_b\\\"}\",\"expId\":\"exp002833\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"MTBeautyCouponBag\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"108fbce5-deaa-44b9-ace8-4142584146ce\\\",\\\"ab_id\\\":\\\"exp002908_b\\\"}\",\"expId\":\"exp002908\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"CardStyleABV2\"},{\"$ref\":\"$.moduleAbConfigs[2]\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"08ee13ce-37a1-4973-9005-7d8efcf696bf\\\",\\\"ab_id\\\":\\\"exp002836_a\\\"}\",\"expId\":\"exp002836\",\"expResult\":\"a\",\"useNewStyle\":false}],\"key\":\"MtComparePrice\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"8cf20f67-81e5-4439-a1e7-d332f92b6f0d\\\",\\\"ab_id\\\":\\\"exp002922_b\\\"}\",\"expId\":\"exp002922\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"MTMassageNewStyle\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"eb38294d-3d9f-4824-b660-084fad136db4\\\",\\\"ab_id\\\":\\\"exp003218_b\\\"}\",\"expId\":\"exp003218\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"MTMassageSimilarDeal\"}],\"moduleConfigsModule\":{\"dpOrder\":true,\"dzx\":true,\"extraInfo\":\"newtuandeal\",\"generalInfo\":\"card_style_v2\",\"key\":\"joy_massage\",\"moduleAbConfigs\":[{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"17712883-aec5-4a0a-bcaa-f1ae97649403\\\",\\\"ab_id\\\":\\\"exp000019_b\\\"}\",\"expId\":\"exp000019\",\"expResult\":\"exp000019_b\"}],\"key\":\"GCPlatformModules/picasso_deal_detail_head_module\"}],\"moduleConfigs\":[{\"key\":\"dealdetail_gc_packagedetail\",\"value\":\"custom_structured_module\"}],\"tort\":false},\"mtId\":*********,\"needLogin\":false,\"picAspectRatio\":0.0,\"purchaseLimitDeal\":false,\"relatedBehaviorModule\":{\"relatedUserBehaviorItems\":[{\"userAvatarUrl\":\"https://img.meituan.net/avatar/f8ead0d4a02c3d8d675e7bf5f52cc41b148310.jpg\",\"userBehaviorDesc\":\"21分钟前下单了\",\"userName\":\"差**1\"},{\"userAvatarUrl\":\"https://p0.meituan.net/ingee/4e78589c0aa6132682faae3f0438c8a4298832.png\",\"userBehaviorDesc\":\"2小时前下单了\",\"userName\":\"Z**9\"},{\"userAvatarUrl\":\"https://p0.meituan.net/ingee/4e78589c0aa6132682faae3f0438c8a4298832.png\",\"userBehaviorDesc\":\"3小时前下单了\",\"userName\":\"Y**8\"}]},\"saleDesc\":\"年售600+\",\"saleDescStr\":\"年售600+\",\"serviceType\":\"精油SPA\",\"serviceTypeId\":108009,\"shareAble\":true,\"shop\":{\"address\":\"博兴路1670号\",\"avgPrice\":\"¥381/人\",\"branchName\":\"金桥店\",\"businessHour\":\"周一至周日 12:00-02:00\",\"businessState\":\"营业中\",\"buyBarIconType\":0,\"displayPosition\":1,\"distance\":\"6.60km\",\"distanceDesc\":\"距您6.60km\",\"hideAddrEnable\":false,\"hideStars\":false,\"imUrl\":\"imeituan://www.meituan.com/web?url=https%3A%2F%2Fg.meituan.com%2Farche%2Fdzbiz%2Fnode-im-h5%2Findex.html%3FtoUid%3Dsk1RpSn5eey7CII9c%26clientType%3D200502%26chatType%3D0%26bizId%3D*********%26sendUnitType%3D6\",\"lat\":31.277164533847692,\"lng\":121.59739642305158,\"lyyShop\":false,\"mapUrl\":\"imeituan://www.meituan.com/mapchannel/poi/detail?mapsource=gc&overseas=0&coordtype=0&poi_id=1700905151&latitude=31.277164533847692&longitude=121.59739642305158\",\"phoneNos\":[\"***********\"],\"shopBizType\":0,\"shopCategoryId\":0,\"shopId\":1700905151,\"shopName\":\"魅享养生SPA\",\"shopNum\":1,\"shopPic\":\"http://p0.meituan.net/dpmerchantpic/241d56455bc27c9fcde88d602ff4009540700.jpg%40300w_225h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\",\"shopPower\":50,\"shopType\":30,\"shopUrl\":\"imeituan://www.meituan.com/gc/poi/detail?id=1700905151\",\"showType\":\"entertainment\"},\"shopCardState\":0,\"showNewReserveEntrance\":false,\"skuId\":\"1228833347\",\"skuModule\":{\"skuAttrCnNameList\":[],\"skuAttrValueList\":[],\"url\":\"imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=gcsubmitordermrnmodules-unify&mrn_component=dealsubmitorderpage-popup&mrn_min_version=0.0.540&dealid=*********&shopid=1700905151&skuid=&isTransparent=true&hideLoading=true&mrn_transparent=true&mrn_hideloading=true&mrn_hideNextNavBar=true&pagesource=dealGroupDetail&is_sku=0&source=poi_page&expid=exp002597_a\"},\"specialFeatures\":[],\"ssrExperimentEnabled\":false,\"standardDealGroup\":false,\"title\":\"主题|60分钟全身精油SPA\",\"tradeType\":0,\"type\":0,\"userCardState\":0}", DealGroupPBO.class);

        Map<String, String> atmosphereConfig = Maps.newHashMap();
        atmosphereConfig.put("spu_atmosphere_enable", "true");
        atmosphereConfig.put("spu_atmosphere_bgurl", "xxx");
        lionMockedStatic.when(() -> Lion.getMap(LionConstants.APP_KEY, LionConstants.STANDARD_PRODUCT_ATMOSPHERE_CONFIG, String.class, Collections.emptyMap())).thenReturn(atmosphereConfig);
        atmospherBuilderService.buildAtmosphereBar(ctx, dealGroupPBO);
        Assert.assertTrue(Objects.nonNull(dealGroupPBO));
    }

    @Test
    public void testBuildSpuDealAtmosphereBarAndSetSaleStatus() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(200502);
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(303);
        ctx.setChannelDTO(channelDTO);
        ctx.setPriceContext(new PriceContext());
        ctx.setMrnVersion("0.5.12");
        DealGroupBaseDTO dealGroupBaseDTO = new DealGroupBaseDTO();
        dealGroupBaseDTO.setDealGroupPrice(new BigDecimal("100"));
        dealGroupBaseDTO.setMarketPrice(new BigDecimal("200"));
        ctx.setDealGroupBase(dealGroupBaseDTO);

        DealGroupPBO dealGroupPBO = JsonUtils.fromJson("{\"abConfigModel\":{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"ed18c747-b8f5-4fef-8caf-b5b4b707e227\\\",\\\"ab_id\\\":\\\"exp000447_a\\\"}\",\"expId\":\"exp000447\",\"expResult\":\"a\",\"useNewStyle\":false}],\"key\":\"MTSalesColorExp\"},\"bgName\":\"general\",\"categoryId\":303,\"dealContents\":[{\"content\":\"https://p0.meituan.net/merchantpic/26252927e384eb75a8b6c1fad6f69ef139667.jpg%40960w_540h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D2%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\",\"scale\":\"16:9\",\"type\":1}],\"dpDealId\":1228833347,\"dpId\":*********,\"features\":[\"买贵必赔\",\"随时退\",\"过期退\"],\"guarantee\":[{\"icon\":\"https://p1.meituan.net/travelcube/409cdc9bf49ebac30967bf41d62665781754.png\",\"style\":\"#8E3C12\",\"text\":\"买贵必赔\",\"type\":1},{\"text\":\"随时退\",\"type\":0},{\"text\":\"过期退\",\"type\":0}],\"hasReserveEntrance\":false,\"hitStructuredPurchaseNote\":false,\"maxPerUser\":0,\"meetPurchaseLimit\":false,\"moduleAbConfigs\":[{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"5f922c02-073d-4227-81a5-08d1ae9da1e3\\\",\\\"ab_id\\\":\\\"exp002291_b\\\"}\",\"expId\":\"exp002291\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"MTSalesCaliberIsQueryNew\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"0778b9da-0cb1-4cce-9034-80e0c00417ef\\\",\\\"ab_id\\\":\\\"exp002027_b\\\"}\",\"expId\":\"exp002027\",\"expResult\":\"exp002027_b\",\"useNewStyle\":false}],\"key\":\"MTZuLiaoShowMarketPrice\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"c3cb06f6-a298-4436-9f76-6d08a366b7b5\\\",\\\"ab_id\\\":\\\"exp002597_a\\\"}\",\"expId\":\"exp002597\",\"expResult\":\"a\",\"useNewStyle\":false}],\"key\":\"MTMassageOverNight\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"765b63a7-3ee9-41ed-8a6d-d3dcaa132ce4\\\",\\\"ab_id\\\":\\\"exp000686_b\\\"}\",\"expId\":\"exp000686\",\"expResult\":\"exp000686_b\",\"useNewStyle\":false}],\"key\":\"MTJoyCardPriceExp\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"5ab84623-7a34-4cc0-a6a5-64c2c1ce6546\\\",\\\"ab_id\\\":\\\"exp001930_a\\\"}\",\"expId\":\"exp001930\",\"expResult\":\"a\",\"useNewStyle\":false}],\"key\":\"MTZuLiaoHuoJiaDirectPurchase\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"cba0a626-8139-48f8-98d5-4c4700a0b144\\\",\\\"ab_id\\\":\\\"exp002193_b\\\"}\",\"expId\":\"exp002193\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"MTShoppingCartBuyBar\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"32ea92f4-5e42-45e4-938c-a4c6640f5ee3\\\",\\\"ab_id\\\":\\\"exp002463_a\\\"}\",\"expId\":\"exp002463\",\"expResult\":\"a\",\"useNewStyle\":false}],\"key\":\"MTShoppingCartBuyBarNew\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"b5622837-bda7-483e-8c6f-e84e8d078388\\\",\\\"ab_id\\\":\\\"exp002529_c\\\"}\",\"expId\":\"exp002529\",\"expResult\":\"c\",\"useNewStyle\":false}],\"key\":\"MTCouponBar\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"f5b87e0f-70e9-4316-86a6-e607e13e7c05\\\",\\\"ab_id\\\":\\\"exp003198_b\\\"}\",\"expId\":\"exp003198\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"MtPurchaseNoteStructure\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"140e44d8-a515-4969-96be-1a5e0e2f68af\\\",\\\"ab_id\\\":\\\"exp002833_b\\\"}\",\"expId\":\"exp002833\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"MTBeautyCouponBag\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"108fbce5-deaa-44b9-ace8-4142584146ce\\\",\\\"ab_id\\\":\\\"exp002908_b\\\"}\",\"expId\":\"exp002908\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"CardStyleABV2\"},{\"$ref\":\"$.moduleAbConfigs[2]\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"08ee13ce-37a1-4973-9005-7d8efcf696bf\\\",\\\"ab_id\\\":\\\"exp002836_a\\\"}\",\"expId\":\"exp002836\",\"expResult\":\"a\",\"useNewStyle\":false}],\"key\":\"MtComparePrice\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"8cf20f67-81e5-4439-a1e7-d332f92b6f0d\\\",\\\"ab_id\\\":\\\"exp002922_b\\\"}\",\"expId\":\"exp002922\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"MTMassageNewStyle\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"eb38294d-3d9f-4824-b660-084fad136db4\\\",\\\"ab_id\\\":\\\"exp003218_b\\\"}\",\"expId\":\"exp003218\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"MTMassageSimilarDeal\"}],\"moduleConfigsModule\":{\"dpOrder\":true,\"dzx\":true,\"extraInfo\":\"newtuandeal\",\"generalInfo\":\"card_style_v2\",\"key\":\"joy_massage\",\"moduleAbConfigs\":[{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"17712883-aec5-4a0a-bcaa-f1ae97649403\\\",\\\"ab_id\\\":\\\"exp000019_b\\\"}\",\"expId\":\"exp000019\",\"expResult\":\"exp000019_b\"}],\"key\":\"GCPlatformModules/picasso_deal_detail_head_module\"}],\"moduleConfigs\":[{\"key\":\"dealdetail_gc_packagedetail\",\"value\":\"custom_structured_module\"}],\"tort\":false},\"mtId\":*********,\"needLogin\":false,\"picAspectRatio\":0.0,\"purchaseLimitDeal\":false,\"relatedBehaviorModule\":{\"relatedUserBehaviorItems\":[{\"userAvatarUrl\":\"https://img.meituan.net/avatar/f8ead0d4a02c3d8d675e7bf5f52cc41b148310.jpg\",\"userBehaviorDesc\":\"21分钟前下单了\",\"userName\":\"差**1\"},{\"userAvatarUrl\":\"https://p0.meituan.net/ingee/4e78589c0aa6132682faae3f0438c8a4298832.png\",\"userBehaviorDesc\":\"2小时前下单了\",\"userName\":\"Z**9\"},{\"userAvatarUrl\":\"https://p0.meituan.net/ingee/4e78589c0aa6132682faae3f0438c8a4298832.png\",\"userBehaviorDesc\":\"3小时前下单了\",\"userName\":\"Y**8\"}]},\"saleDesc\":\"年售600+\",\"saleDescStr\":\"年售600+\",\"serviceType\":\"精油SPA\",\"serviceTypeId\":108009,\"shareAble\":true,\"shop\":{\"address\":\"博兴路1670号\",\"avgPrice\":\"¥381/人\",\"branchName\":\"金桥店\",\"businessHour\":\"周一至周日 12:00-02:00\",\"businessState\":\"营业中\",\"buyBarIconType\":0,\"displayPosition\":1,\"distance\":\"6.60km\",\"distanceDesc\":\"距您6.60km\",\"hideAddrEnable\":false,\"hideStars\":false,\"imUrl\":\"imeituan://www.meituan.com/web?url=https%3A%2F%2Fg.meituan.com%2Farche%2Fdzbiz%2Fnode-im-h5%2Findex.html%3FtoUid%3Dsk1RpSn5eey7CII9c%26clientType%3D200502%26chatType%3D0%26bizId%3D*********%26sendUnitType%3D6\",\"lat\":31.277164533847692,\"lng\":121.59739642305158,\"lyyShop\":false,\"mapUrl\":\"imeituan://www.meituan.com/mapchannel/poi/detail?mapsource=gc&overseas=0&coordtype=0&poi_id=1700905151&latitude=31.277164533847692&longitude=121.59739642305158\",\"phoneNos\":[\"***********\"],\"shopBizType\":0,\"shopCategoryId\":0,\"shopId\":1700905151,\"shopName\":\"魅享养生SPA\",\"shopNum\":1,\"shopPic\":\"http://p0.meituan.net/dpmerchantpic/241d56455bc27c9fcde88d602ff4009540700.jpg%40300w_225h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\",\"shopPower\":50,\"shopType\":30,\"shopUrl\":\"imeituan://www.meituan.com/gc/poi/detail?id=1700905151\",\"showType\":\"entertainment\"},\"shopCardState\":0,\"showNewReserveEntrance\":false,\"skuId\":\"1228833347\",\"skuModule\":{\"skuAttrCnNameList\":[],\"skuAttrValueList\":[],\"url\":\"imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=gcsubmitordermrnmodules-unify&mrn_component=dealsubmitorderpage-popup&mrn_min_version=0.0.540&dealid=*********&shopid=1700905151&skuid=&isTransparent=true&hideLoading=true&mrn_transparent=true&mrn_hideloading=true&mrn_hideNextNavBar=true&pagesource=dealGroupDetail&is_sku=0&source=poi_page&expid=exp002597_a\"},\"specialFeatures\":[],\"ssrExperimentEnabled\":false,\"standardDealGroup\":false,\"title\":\"主题|60分钟全身精油SPA\",\"tradeType\":0,\"type\":0,\"userCardState\":0}", DealGroupPBO.class);

        Map<String, String> configMap = Maps.newHashMap();
        configMap.put("spu_atmosphere_bgurl", "xxx");
        configMap.put("spu_atmosphere_enable", "true");
        AttributeDTO mtssAttrDTO = new AttributeDTO();
        mtssAttrDTO.setName("mtss_ref_spu_id");
        mtssAttrDTO.setValue(Lists.newArrayList("123"));
        AttributeDTO spuSceneTypeAttrDTO = new AttributeDTO();
        spuSceneTypeAttrDTO.setName("spuSceneType");
        spuSceneTypeAttrDTO.setValue(Lists.newArrayList("17"));
        List<AttributeDTO> attrs = Lists.newArrayList();
        attrs.add(mtssAttrDTO);
        attrs.add(spuSceneTypeAttrDTO);
        ctx.setAttrs(attrs);
        lionMockedStatic.when(() -> Lion.getMap(LionConstants.APP_KEY, LionConstants.STANDARD_PRODUCT_ATMOSPHERE_CONFIG, String.class, Collections.emptyMap())).thenReturn(configMap);

        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setKey("DpSuperDealAtmosphereExp");
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("b");
        abConfig.setExpId("EXP2024101100002");
        abConfig.setExpBiInfo("EXP2024101100002_b");
        moduleAbConfig.setConfigs(Lists.newArrayList(abConfig));
        Mockito.when(douHuBiz.getAbExpResultByUuidAndDpid(Mockito.any(), Mockito.anyString())).thenReturn(moduleAbConfig);
        Mockito.when(douHuBiz.getExpResult(Mockito.any())).thenReturn("b");
        ctx.setModuleAbConfigs(new ArrayList<>());

        atmospherBuilderService.buildAtmosphereBar(ctx, dealGroupPBO);
        Assert.assertTrue(dealGroupPBO.getDealAtmosphereBarModules().size() > 0);
    }

    @Test
    public void testBuildDealAtmosphereBarAndSetSaleStatus() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(200502);
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(303);
        ctx.setChannelDTO(channelDTO);
        ctx.setPriceContext(new PriceContext());
        DealGroupBaseDTO dealGroupBaseDTO = new DealGroupBaseDTO();
        dealGroupBaseDTO.setDealGroupPrice(new BigDecimal("100"));
        dealGroupBaseDTO.setMarketPrice(new BigDecimal("200"));
        ctx.setDealGroupBase(dealGroupBaseDTO);

        DealGroupPBO dealGroupPBO = JsonUtils.fromJson("{\"abConfigModel\":{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"ed18c747-b8f5-4fef-8caf-b5b4b707e227\\\",\\\"ab_id\\\":\\\"exp000447_a\\\"}\",\"expId\":\"exp000447\",\"expResult\":\"a\",\"useNewStyle\":false}],\"key\":\"MTSalesColorExp\"},\"bgName\":\"general\",\"categoryId\":303,\"dealContents\":[{\"content\":\"https://p0.meituan.net/merchantpic/26252927e384eb75a8b6c1fad6f69ef139667.jpg%40960w_540h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D2%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\",\"scale\":\"16:9\",\"type\":1}],\"dpDealId\":1228833347,\"dpId\":*********,\"features\":[\"买贵必赔\",\"随时退\",\"过期退\"],\"guarantee\":[{\"icon\":\"https://p1.meituan.net/travelcube/409cdc9bf49ebac30967bf41d62665781754.png\",\"style\":\"#8E3C12\",\"text\":\"买贵必赔\",\"type\":1},{\"text\":\"随时退\",\"type\":0},{\"text\":\"过期退\",\"type\":0}],\"hasReserveEntrance\":false,\"hitStructuredPurchaseNote\":false,\"maxPerUser\":0,\"meetPurchaseLimit\":false,\"moduleAbConfigs\":[{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"5f922c02-073d-4227-81a5-08d1ae9da1e3\\\",\\\"ab_id\\\":\\\"exp002291_b\\\"}\",\"expId\":\"exp002291\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"MTSalesCaliberIsQueryNew\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"0778b9da-0cb1-4cce-9034-80e0c00417ef\\\",\\\"ab_id\\\":\\\"exp002027_b\\\"}\",\"expId\":\"exp002027\",\"expResult\":\"exp002027_b\",\"useNewStyle\":false}],\"key\":\"MTZuLiaoShowMarketPrice\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"c3cb06f6-a298-4436-9f76-6d08a366b7b5\\\",\\\"ab_id\\\":\\\"exp002597_a\\\"}\",\"expId\":\"exp002597\",\"expResult\":\"a\",\"useNewStyle\":false}],\"key\":\"MTMassageOverNight\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"765b63a7-3ee9-41ed-8a6d-d3dcaa132ce4\\\",\\\"ab_id\\\":\\\"exp000686_b\\\"}\",\"expId\":\"exp000686\",\"expResult\":\"exp000686_b\",\"useNewStyle\":false}],\"key\":\"MTJoyCardPriceExp\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"5ab84623-7a34-4cc0-a6a5-64c2c1ce6546\\\",\\\"ab_id\\\":\\\"exp001930_a\\\"}\",\"expId\":\"exp001930\",\"expResult\":\"a\",\"useNewStyle\":false}],\"key\":\"MTZuLiaoHuoJiaDirectPurchase\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"cba0a626-8139-48f8-98d5-4c4700a0b144\\\",\\\"ab_id\\\":\\\"exp002193_b\\\"}\",\"expId\":\"exp002193\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"MTShoppingCartBuyBar\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"32ea92f4-5e42-45e4-938c-a4c6640f5ee3\\\",\\\"ab_id\\\":\\\"exp002463_a\\\"}\",\"expId\":\"exp002463\",\"expResult\":\"a\",\"useNewStyle\":false}],\"key\":\"MTShoppingCartBuyBarNew\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"b5622837-bda7-483e-8c6f-e84e8d078388\\\",\\\"ab_id\\\":\\\"exp002529_c\\\"}\",\"expId\":\"exp002529\",\"expResult\":\"c\",\"useNewStyle\":false}],\"key\":\"MTCouponBar\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"f5b87e0f-70e9-4316-86a6-e607e13e7c05\\\",\\\"ab_id\\\":\\\"exp003198_b\\\"}\",\"expId\":\"exp003198\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"MtPurchaseNoteStructure\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"140e44d8-a515-4969-96be-1a5e0e2f68af\\\",\\\"ab_id\\\":\\\"exp002833_b\\\"}\",\"expId\":\"exp002833\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"MTBeautyCouponBag\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"108fbce5-deaa-44b9-ace8-4142584146ce\\\",\\\"ab_id\\\":\\\"exp002908_b\\\"}\",\"expId\":\"exp002908\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"CardStyleABV2\"},{\"$ref\":\"$.moduleAbConfigs[2]\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"08ee13ce-37a1-4973-9005-7d8efcf696bf\\\",\\\"ab_id\\\":\\\"exp002836_a\\\"}\",\"expId\":\"exp002836\",\"expResult\":\"a\",\"useNewStyle\":false}],\"key\":\"MtComparePrice\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"8cf20f67-81e5-4439-a1e7-d332f92b6f0d\\\",\\\"ab_id\\\":\\\"exp002922_b\\\"}\",\"expId\":\"exp002922\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"MTMassageNewStyle\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"eb38294d-3d9f-4824-b660-084fad136db4\\\",\\\"ab_id\\\":\\\"exp003218_b\\\"}\",\"expId\":\"exp003218\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"MTMassageSimilarDeal\"}],\"moduleConfigsModule\":{\"dpOrder\":true,\"dzx\":true,\"extraInfo\":\"newtuandeal\",\"generalInfo\":\"card_style_v2\",\"key\":\"joy_massage\",\"moduleAbConfigs\":[{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"17712883-aec5-4a0a-bcaa-f1ae97649403\\\",\\\"ab_id\\\":\\\"exp000019_b\\\"}\",\"expId\":\"exp000019\",\"expResult\":\"exp000019_b\"}],\"key\":\"GCPlatformModules/picasso_deal_detail_head_module\"}],\"moduleConfigs\":[{\"key\":\"dealdetail_gc_packagedetail\",\"value\":\"custom_structured_module\"}],\"tort\":false},\"mtId\":*********,\"needLogin\":false,\"picAspectRatio\":0.0,\"purchaseLimitDeal\":false,\"relatedBehaviorModule\":{\"relatedUserBehaviorItems\":[{\"userAvatarUrl\":\"https://img.meituan.net/avatar/f8ead0d4a02c3d8d675e7bf5f52cc41b148310.jpg\",\"userBehaviorDesc\":\"21分钟前下单了\",\"userName\":\"差**1\"},{\"userAvatarUrl\":\"https://p0.meituan.net/ingee/4e78589c0aa6132682faae3f0438c8a4298832.png\",\"userBehaviorDesc\":\"2小时前下单了\",\"userName\":\"Z**9\"},{\"userAvatarUrl\":\"https://p0.meituan.net/ingee/4e78589c0aa6132682faae3f0438c8a4298832.png\",\"userBehaviorDesc\":\"3小时前下单了\",\"userName\":\"Y**8\"}]},\"saleDesc\":\"年售600+\",\"saleDescStr\":\"年售600+\",\"serviceType\":\"精油SPA\",\"serviceTypeId\":108009,\"shareAble\":true,\"shop\":{\"address\":\"博兴路1670号\",\"avgPrice\":\"¥381/人\",\"branchName\":\"金桥店\",\"businessHour\":\"周一至周日 12:00-02:00\",\"businessState\":\"营业中\",\"buyBarIconType\":0,\"displayPosition\":1,\"distance\":\"6.60km\",\"distanceDesc\":\"距您6.60km\",\"hideAddrEnable\":false,\"hideStars\":false,\"imUrl\":\"imeituan://www.meituan.com/web?url=https%3A%2F%2Fg.meituan.com%2Farche%2Fdzbiz%2Fnode-im-h5%2Findex.html%3FtoUid%3Dsk1RpSn5eey7CII9c%26clientType%3D200502%26chatType%3D0%26bizId%3D*********%26sendUnitType%3D6\",\"lat\":31.277164533847692,\"lng\":121.59739642305158,\"lyyShop\":false,\"mapUrl\":\"imeituan://www.meituan.com/mapchannel/poi/detail?mapsource=gc&overseas=0&coordtype=0&poi_id=1700905151&latitude=31.277164533847692&longitude=121.59739642305158\",\"phoneNos\":[\"***********\"],\"shopBizType\":0,\"shopCategoryId\":0,\"shopId\":1700905151,\"shopName\":\"魅享养生SPA\",\"shopNum\":1,\"shopPic\":\"http://p0.meituan.net/dpmerchantpic/241d56455bc27c9fcde88d602ff4009540700.jpg%40300w_225h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\",\"shopPower\":50,\"shopType\":30,\"shopUrl\":\"imeituan://www.meituan.com/gc/poi/detail?id=1700905151\",\"showType\":\"entertainment\"},\"shopCardState\":0,\"showNewReserveEntrance\":false,\"skuId\":\"1228833347\",\"skuModule\":{\"skuAttrCnNameList\":[],\"skuAttrValueList\":[],\"url\":\"imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=gcsubmitordermrnmodules-unify&mrn_component=dealsubmitorderpage-popup&mrn_min_version=0.0.540&dealid=*********&shopid=1700905151&skuid=&isTransparent=true&hideLoading=true&mrn_transparent=true&mrn_hideloading=true&mrn_hideNextNavBar=true&pagesource=dealGroupDetail&is_sku=0&source=poi_page&expid=exp002597_a\"},\"specialFeatures\":[],\"ssrExperimentEnabled\":false,\"standardDealGroup\":false,\"title\":\"主题|60分钟全身精油SPA\",\"tradeType\":0,\"type\":0,\"userCardState\":0}", DealGroupPBO.class);
        dealGroupPBO.setDealAtmosphereBarModules(new ArrayList<>());

        atmospherBuilderService.buildDealAtmosphereBarAndSetSaleStatus(ctx, dealGroupPBO);
        Assert.assertTrue(dealGroupPBO.getDealAtmosphereBarModules().size() == 1);
    }

    @Test
    public void testBuildMemberExclusiveAtmosphereBar() {
        DealGroupPBO dealGroupPBO = JsonUtils.fromJson("{\"abConfigModel\":{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"ed18c747-b8f5-4fef-8caf-b5b4b707e227\\\",\\\"ab_id\\\":\\\"exp000447_a\\\"}\",\"expId\":\"exp000447\",\"expResult\":\"a\",\"useNewStyle\":false}],\"key\":\"MTSalesColorExp\"},\"bgName\":\"general\",\"categoryId\":303,\"dealContents\":[{\"content\":\"https://p0.meituan.net/merchantpic/26252927e384eb75a8b6c1fad6f69ef139667.jpg%40960w_540h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D2%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\",\"scale\":\"16:9\",\"type\":1}],\"dpDealId\":1228833347,\"dpId\":*********,\"features\":[\"买贵必赔\",\"随时退\",\"过期退\"],\"guarantee\":[{\"icon\":\"https://p1.meituan.net/travelcube/409cdc9bf49ebac30967bf41d62665781754.png\",\"style\":\"#8E3C12\",\"text\":\"买贵必赔\",\"type\":1},{\"text\":\"随时退\",\"type\":0},{\"text\":\"过期退\",\"type\":0}],\"hasReserveEntrance\":false,\"hitStructuredPurchaseNote\":false,\"maxPerUser\":0,\"meetPurchaseLimit\":false,\"moduleAbConfigs\":[{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"5f922c02-073d-4227-81a5-08d1ae9da1e3\\\",\\\"ab_id\\\":\\\"exp002291_b\\\"}\",\"expId\":\"exp002291\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"MTSalesCaliberIsQueryNew\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"0778b9da-0cb1-4cce-9034-80e0c00417ef\\\",\\\"ab_id\\\":\\\"exp002027_b\\\"}\",\"expId\":\"exp002027\",\"expResult\":\"exp002027_b\",\"useNewStyle\":false}],\"key\":\"MTZuLiaoShowMarketPrice\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"c3cb06f6-a298-4436-9f76-6d08a366b7b5\\\",\\\"ab_id\\\":\\\"exp002597_a\\\"}\",\"expId\":\"exp002597\",\"expResult\":\"a\",\"useNewStyle\":false}],\"key\":\"MTMassageOverNight\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"765b63a7-3ee9-41ed-8a6d-d3dcaa132ce4\\\",\\\"ab_id\\\":\\\"exp000686_b\\\"}\",\"expId\":\"exp000686\",\"expResult\":\"exp000686_b\",\"useNewStyle\":false}],\"key\":\"MTJoyCardPriceExp\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"5ab84623-7a34-4cc0-a6a5-64c2c1ce6546\\\",\\\"ab_id\\\":\\\"exp001930_a\\\"}\",\"expId\":\"exp001930\",\"expResult\":\"a\",\"useNewStyle\":false}],\"key\":\"MTZuLiaoHuoJiaDirectPurchase\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"cba0a626-8139-48f8-98d5-4c4700a0b144\\\",\\\"ab_id\\\":\\\"exp002193_b\\\"}\",\"expId\":\"exp002193\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"MTShoppingCartBuyBar\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"32ea92f4-5e42-45e4-938c-a4c6640f5ee3\\\",\\\"ab_id\\\":\\\"exp002463_a\\\"}\",\"expId\":\"exp002463\",\"expResult\":\"a\",\"useNewStyle\":false}],\"key\":\"MTShoppingCartBuyBarNew\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"b5622837-bda7-483e-8c6f-e84e8d078388\\\",\\\"ab_id\\\":\\\"exp002529_c\\\"}\",\"expId\":\"exp002529\",\"expResult\":\"c\",\"useNewStyle\":false}],\"key\":\"MTCouponBar\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"f5b87e0f-70e9-4316-86a6-e607e13e7c05\\\",\\\"ab_id\\\":\\\"exp003198_b\\\"}\",\"expId\":\"exp003198\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"MtPurchaseNoteStructure\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"140e44d8-a515-4969-96be-1a5e0e2f68af\\\",\\\"ab_id\\\":\\\"exp002833_b\\\"}\",\"expId\":\"exp002833\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"MTBeautyCouponBag\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"108fbce5-deaa-44b9-ace8-4142584146ce\\\",\\\"ab_id\\\":\\\"exp002908_b\\\"}\",\"expId\":\"exp002908\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"CardStyleABV2\"},{\"$ref\":\"$.moduleAbConfigs[2]\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"08ee13ce-37a1-4973-9005-7d8efcf696bf\\\",\\\"ab_id\\\":\\\"exp002836_a\\\"}\",\"expId\":\"exp002836\",\"expResult\":\"a\",\"useNewStyle\":false}],\"key\":\"MtComparePrice\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"8cf20f67-81e5-4439-a1e7-d332f92b6f0d\\\",\\\"ab_id\\\":\\\"exp002922_b\\\"}\",\"expId\":\"exp002922\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"MTMassageNewStyle\"},{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"eb38294d-3d9f-4824-b660-084fad136db4\\\",\\\"ab_id\\\":\\\"exp003218_b\\\"}\",\"expId\":\"exp003218\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"MTMassageSimilarDeal\"}],\"moduleConfigsModule\":{\"dpOrder\":true,\"dzx\":true,\"extraInfo\":\"newtuandeal\",\"generalInfo\":\"card_style_v2\",\"key\":\"joy_massage\",\"moduleAbConfigs\":[{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"17712883-aec5-4a0a-bcaa-f1ae97649403\\\",\\\"ab_id\\\":\\\"exp000019_b\\\"}\",\"expId\":\"exp000019\",\"expResult\":\"exp000019_b\"}],\"key\":\"GCPlatformModules/picasso_deal_detail_head_module\"}],\"moduleConfigs\":[{\"key\":\"dealdetail_gc_packagedetail\",\"value\":\"custom_structured_module\"}],\"tort\":false},\"mtId\":*********,\"needLogin\":false,\"picAspectRatio\":0.0,\"purchaseLimitDeal\":false,\"relatedBehaviorModule\":{\"relatedUserBehaviorItems\":[{\"userAvatarUrl\":\"https://img.meituan.net/avatar/f8ead0d4a02c3d8d675e7bf5f52cc41b148310.jpg\",\"userBehaviorDesc\":\"21分钟前下单了\",\"userName\":\"差**1\"},{\"userAvatarUrl\":\"https://p0.meituan.net/ingee/4e78589c0aa6132682faae3f0438c8a4298832.png\",\"userBehaviorDesc\":\"2小时前下单了\",\"userName\":\"Z**9\"},{\"userAvatarUrl\":\"https://p0.meituan.net/ingee/4e78589c0aa6132682faae3f0438c8a4298832.png\",\"userBehaviorDesc\":\"3小时前下单了\",\"userName\":\"Y**8\"}]},\"saleDesc\":\"年售600+\",\"saleDescStr\":\"年售600+\",\"serviceType\":\"精油SPA\",\"serviceTypeId\":108009,\"shareAble\":true,\"shop\":{\"address\":\"博兴路1670号\",\"avgPrice\":\"¥381/人\",\"branchName\":\"金桥店\",\"businessHour\":\"周一至周日 12:00-02:00\",\"businessState\":\"营业中\",\"buyBarIconType\":0,\"displayPosition\":1,\"distance\":\"6.60km\",\"distanceDesc\":\"距您6.60km\",\"hideAddrEnable\":false,\"hideStars\":false,\"imUrl\":\"imeituan://www.meituan.com/web?url=https%3A%2F%2Fg.meituan.com%2Farche%2Fdzbiz%2Fnode-im-h5%2Findex.html%3FtoUid%3Dsk1RpSn5eey7CII9c%26clientType%3D200502%26chatType%3D0%26bizId%3D*********%26sendUnitType%3D6\",\"lat\":31.277164533847692,\"lng\":121.59739642305158,\"lyyShop\":false,\"mapUrl\":\"imeituan://www.meituan.com/mapchannel/poi/detail?mapsource=gc&overseas=0&coordtype=0&poi_id=1700905151&latitude=31.277164533847692&longitude=121.59739642305158\",\"phoneNos\":[\"***********\"],\"shopBizType\":0,\"shopCategoryId\":0,\"shopId\":1700905151,\"shopName\":\"魅享养生SPA\",\"shopNum\":1,\"shopPic\":\"http://p0.meituan.net/dpmerchantpic/241d56455bc27c9fcde88d602ff4009540700.jpg%40300w_225h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\",\"shopPower\":50,\"shopType\":30,\"shopUrl\":\"imeituan://www.meituan.com/gc/poi/detail?id=1700905151\",\"showType\":\"entertainment\"},\"shopCardState\":0,\"showNewReserveEntrance\":false,\"skuId\":\"1228833347\",\"skuModule\":{\"skuAttrCnNameList\":[],\"skuAttrValueList\":[],\"url\":\"imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=gcsubmitordermrnmodules-unify&mrn_component=dealsubmitorderpage-popup&mrn_min_version=0.0.540&dealid=*********&shopid=1700905151&skuid=&isTransparent=true&hideLoading=true&mrn_transparent=true&mrn_hideloading=true&mrn_hideNextNavBar=true&pagesource=dealGroupDetail&is_sku=0&source=poi_page&expid=exp002597_a\"},\"specialFeatures\":[],\"ssrExperimentEnabled\":false,\"standardDealGroup\":false,\"title\":\"主题|60分钟全身精油SPA\",\"tradeType\":0,\"type\":0,\"userCardState\":0}", DealGroupPBO.class);
        atmospherBuilderService.buildMemberExclusiveAtmosphereBar(dealGroupPBO);
        Assert.assertTrue(dealGroupPBO.getDealAtmosphereBarModules().size() == 1);
    }

    @Test
    public void testHitDisplayAtmosphereExp() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setKey("DpSuperDealAtmosphereExp");
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("b");
        abConfig.setExpId("EXP2024101100002");
        abConfig.setExpBiInfo("EXP2024101100002_b");
        moduleAbConfig.setConfigs(Lists.newArrayList(abConfig));
        Mockito.when(douHuBiz.getAbExpResultByUuidAndDpid(Mockito.any(), Mockito.anyString())).thenReturn(moduleAbConfig);
        Mockito.when(douHuBiz.getExpResult(Mockito.any())).thenReturn("b");
        ctx.setModuleAbConfigs(new ArrayList<>());
        Assert.assertTrue(atmospherBuilderService.hitDisplayAtmosphereExp(ctx));
    }
}
