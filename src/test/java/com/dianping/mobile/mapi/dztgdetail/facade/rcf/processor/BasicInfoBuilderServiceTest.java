package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.SkuCtx;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.BasicInfoBuilderService;
import com.dianping.mobile.mapi.dztgdetail.util.JsonUtils;
import com.google.common.collect.Lists;
import com.sankuai.beautycontent.security.displaycontrol.response.DisplayControlResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2024/5/19
 */
@RunWith(MockitoJUnitRunner.class)
public class BasicInfoBuilderServiceTest {
    @InjectMocks
    private BasicInfoBuilderService basicInfoBuilderService;

    @Test
    public void testGetDefaultSkuId() {
        DealCtx dealCtx = new DealCtx(new EnvCtx());
        SkuCtx skuCtx = new SkuCtx();
        skuCtx.setSkuId("610286927");
        dealCtx.setSkuCtx(skuCtx);
        String result = basicInfoBuilderService.getDefaultSkuId(dealCtx);
        Assert.assertTrue(result.equals("610286927"));
    }

    @Test
    public void testGetDefaultSkuIdByDealId() {
        DealCtx dealCtx = new DealCtx(new EnvCtx());
        DealGroupBaseDTO dealGroupBaseDTO = JsonUtils.fromJson("{\"dealGroupId\":607771703,\"dealGroupShortTitle\":\"Homie Barber Shop\",\"dealGroupTitleDesc\":\"仅售238元，价值288元「体验」Barber美式渐变油头男士理发，男女通用！\",\"maxJoin\":0,\"currentJoin\":0,\"defaultPic\":\"\",\"dealGroupPrice\":238.00,\"marketPrice\":288.00,\"maxPerUser\":0,\"minPerUser\":1,\"status\":1,\"autoRefundSwitch\":1,\"dealGroupPics\":\"\",\"saleChannel\":0,\"salePlatform\":3,\"publishStatus\":1,\"dealGroupType\":1,\"overdueAutoRefund\":true,\"canUseCoupon\":true,\"thirdPartVerify\":false,\"payChannelIDAllowed\":0,\"discountRuleID\":0,\"blockStock\":false,\"publishVersion\":0,\"lottery\":false,\"productTitle\":\"「体验」Barber美式渐变油头男士理发\",\"featureTitle\":\"男女通用\",\"deals\":[{\"dealId\":610286927,\"dealGroupId\":607771703,\"shortTitle\":\"「体验」Barber美式渐变油头男士理发\",\"price\":238.00,\"marketPrice\":288.00,\"maxJoin\":0,\"currentJoin\":0,\"receiptType\":1,\"deliverType\":0,\"dealStatus\":1,\"receiptDateType\":1,\"receiptValidDays\":15,\"provideInvoice\":false,\"thirdPartyId\":0,\"dealGroupPublishVersion\":0}],\"sourceId\":102}", DealGroupBaseDTO.class);
        dealCtx.setDealGroupBase(dealGroupBaseDTO);
        String result = basicInfoBuilderService.getDefaultSkuId(dealCtx);
        Assert.assertTrue(result.equals("610286927"));
    }

    @Test
    public void testGetTitle() {
        DealCtx dealCtx = new DealCtx(new EnvCtx());
        DealGroupBaseDTO dealGroupBaseDTO = JsonUtils.fromJson("{\"dealGroupId\":607771703,\"dealGroupShortTitle\":\"Homie Barber Shop\",\"dealGroupTitleDesc\":\"仅售238元，价值288元「体验」Barber美式渐变油头男士理发，男女通用！\",\"maxJoin\":0,\"currentJoin\":0,\"defaultPic\":\"\",\"dealGroupPrice\":238.00,\"marketPrice\":288.00,\"maxPerUser\":0,\"minPerUser\":1,\"status\":1,\"autoRefundSwitch\":1,\"dealGroupPics\":\"\",\"saleChannel\":0,\"salePlatform\":3,\"publishStatus\":1,\"dealGroupType\":1,\"overdueAutoRefund\":true,\"canUseCoupon\":true,\"thirdPartVerify\":false,\"payChannelIDAllowed\":0,\"discountRuleID\":0,\"blockStock\":false,\"publishVersion\":0,\"lottery\":false,\"productTitle\":\"「体验」Barber美式渐变油头男士理发\",\"featureTitle\":\"男女通用\",\"deals\":[{\"dealId\":610286927,\"dealGroupId\":607771703,\"shortTitle\":\"「体验」Barber美式渐变油头男士理发\",\"price\":238.00,\"marketPrice\":288.00,\"maxJoin\":0,\"currentJoin\":0,\"receiptType\":1,\"deliverType\":0,\"dealStatus\":1,\"receiptDateType\":1,\"receiptValidDays\":15,\"provideInvoice\":false,\"thirdPartyId\":0,\"dealGroupPublishVersion\":0}],\"sourceId\":102}", DealGroupBaseDTO.class);
        dealCtx.setDealGroupBase(dealGroupBaseDTO);
        String title = basicInfoBuilderService.getTitle(dealCtx);
        Assert.assertTrue(title.equals("「体验」Barber美式渐变油头男士理发"));
    }

    @Test
    public void testGetEduPrefix_Young() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        DisplayControlResponse displayControlResponse = new DisplayControlResponse();
        displayControlResponse.setResult(1);
        ctx.setDisplayControlResponse(displayControlResponse);

        List<AttributeDTO> attributeDTOS = new ArrayList<>();
        AttributeDTO eduSuitableAge = new AttributeDTO();
        eduSuitableAge.setName("eduSuitableAge");
        eduSuitableAge.setValue(Lists.newArrayList("10岁-15岁"));

        AttributeDTO femaleOnly = new AttributeDTO();
        femaleOnly.setName("female_only");
        femaleOnly.setValue(Lists.newArrayList("是"));
        attributeDTOS.add(eduSuitableAge);
        attributeDTOS.add(femaleOnly);
        ctx.setAttrs(attributeDTOS);
        String result = basicInfoBuilderService.getEduPrefix(ctx);
        Assert.assertTrue("【青少】".equals(result));
    }

    @Test
    public void testGetEduPrefix_Female() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        DisplayControlResponse displayControlResponse = new DisplayControlResponse();
        displayControlResponse.setResult(1);
        ctx.setDisplayControlResponse(displayControlResponse);

        List<AttributeDTO> attributeDTOS = new ArrayList<>();
        AttributeDTO eduSuitableAge = new AttributeDTO();
        eduSuitableAge.setName("eduSuitableAge");
        eduSuitableAge.setValue(Lists.newArrayList("20岁及以上"));

        AttributeDTO femaleOnly = new AttributeDTO();
        femaleOnly.setName("female_only");
        femaleOnly.setValue(Lists.newArrayList("是"));
        attributeDTOS.add(eduSuitableAge);
        attributeDTOS.add(femaleOnly);
        ctx.setAttrs(attributeDTOS);
        String result = basicInfoBuilderService.getEduPrefix(ctx);
        Assert.assertTrue("【女性班】".equals(result));
    }
}
