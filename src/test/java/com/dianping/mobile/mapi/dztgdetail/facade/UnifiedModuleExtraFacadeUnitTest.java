package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.UnifiedCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedModuleExtraReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleExtraDTO;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.google.common.collect.Lists;
import com.google.common.reflect.TypeToken;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedModuleExtraFacadeUnitTest {

    @InjectMocks
    private UnifiedModuleExtraFacade unifiedModuleExtraFacade;
    @Mock
    private DouHuService douHuService;

    private UnifiedModuleExtraReq request;
    private UnifiedCtx unifiedCtx;
    private List<ModuleConfigDo> result;

    private MockedStatic<Lion> utilities;

    private int publishCategoryId;

    private static final String RESULT_JSON = "[{\"key\":\"团购详情\",\"value\":\"tuandeal_newtuandealtab_online_education_tuandetail\"},{\"key\":\"授课老师\",\"value\":\"tuandeal_newtuandealtab_online_education_teachers\"},{\"key\":\"购买须知\",\"value\":\"tuandeal_newtuandealtab_education_buyrules\"},{\"key\":\"网友评价\",\"value\":\"tuandeal_newtuandealtab_reviews\"}]";

    private static final String CONFIG_JSON = "{\"dp1210_online\":[\"授课老师\",\"团购详情\",\"购买须知\",\"网友评价\"],\"mt1210_online\":[\"授课老师\",\"团购详情\",\"购买须知\",\"网友评价\"]}";

    @Before
    public void setUp() {
        request = new UnifiedModuleExtraReq();
        unifiedCtx = new UnifiedCtx(new EnvCtx());
        setAttrs();
        result = GsonUtils.fromJsonString(RESULT_JSON, new TypeToken<List<ModuleConfigDo>>(){}.getType());
        utilities = mockStatic(Lion.class);
        List<Long> eduDealCategoryIds = Arrays.asList(1210L, 134013L, 123020L);
        utilities.when(() -> Lion.getList(LionConstants.APP_KEY, LionConstants.EDU_DEAL_CATEGORY_IDS, Long.class, Lists.newArrayList(1210L)))
                .thenReturn(eduDealCategoryIds);
        utilities.when(() -> Lion.getList(LionConstants.APP_KEY, LionConstants.EDU_ONLINE_DEAL_SERVICE_LEAF_IDS, Long.class, Lists.newArrayList(134013L, 123020L)))
                .thenReturn(Lists.newArrayList(134013L, 123020L));
        publishCategoryId = 1210;
    }

    private void setAttrs() {
        List<AttributeDTO> attributeDTOS = new ArrayList<>();
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName(UnifiedModuleExtraFacade.ATTR_SERVICE_TYPE_LEAF_ID);
        attributeDTO.setValue(Lists.newArrayList("134013"));
        attributeDTOS.add(attributeDTO);
        unifiedCtx.setAttributes(attributeDTOS);
    }

    @After
    public void after() {
        utilities.close();
    }

    /**
     * 测试场景：结果列表为空，不需要重置排序
     */
    @Test
    public void testResetItemIndexForCaiXi_ResultListIsEmpty() {
        utilities.when(() -> Lion.getString(anyString(), anyString(), anyString())).thenReturn("");
        List<ModuleConfigDo> actual = unifiedModuleExtraFacade.resetItemIndexForCaiXi(request, unifiedCtx, publishCategoryId, null);
        assertEquals(null, actual);
    }

    /**
     * 测试场景：结果列表非空，但请求的页面来源不是"CAIXI"，不需要重置排序
     */
    @Test
    public void testResetItemIndexForCaiXi_PageSourceIsNotCAIXI() {
        request.setPageSource("NOT_CAI_XI");
        List<ModuleConfigDo> actual = unifiedModuleExtraFacade.resetItemIndexForCaiXi(request, unifiedCtx, publishCategoryId, result);
        assertEquals(result, actual);
    }

    /**
     * 测试场景：结果列表非空，请求的页面来源是"CAIXI"，但排序列表为空，不需要重置排序
     */
    @Test
    public void testResetItemIndexForCaiXi_SortListIsEmpty() {
        request.setPageSource("CAI_XI");
        utilities.when(() -> Lion.getString(anyString(), anyString(), anyString())).thenReturn(CONFIG_JSON);
        List<ModuleConfigDo> actual = unifiedModuleExtraFacade.resetItemIndexForCaiXi(request, unifiedCtx, publishCategoryId, result);
        assertEquals(result, actual);
    }

    /**
     * 测试场景：结果列表非空，请求的页面来源是"CAIXI"，排序列表非空，需要重置排序
     */
    @Test
    public void testResetItemIndexForCaiXi_NeedResetSort() {
        request.setPageSource(RequestSourceEnum.CAI_XI.getSource());
        utilities.when(() -> Lion.getString(anyString(), anyString(), anyString())).thenReturn(CONFIG_JSON);
        List<ModuleConfigDo> actual = unifiedModuleExtraFacade.resetItemIndexForCaiXi(request, unifiedCtx, publishCategoryId, result);

        assertTrue(actual.size() == 4);
        assertTrue(actual.get(0).getKey().equals("授课老师"));
        assertTrue(actual.get(1).getKey().equals("团购详情"));
        assertTrue(actual.get(2).getKey().equals("购买须知"));
        assertTrue(actual.get(3).getKey().equals("网友评价"));
    }


    @Test
    public void testEduFreeDealModuleRename() {
        ModuleExtraDTO moduleExtraDTO = new ModuleExtraDTO();
        ModuleConfigDo moduleConfigDo1 = new ModuleConfigDo();
        moduleConfigDo1.setKey("团购详情");
        ModuleConfigDo moduleConfigDo2 = new ModuleConfigDo();
        moduleConfigDo2.setKey("购买须知");
        List<ModuleConfigDo> moduleConfigDos = Lists.newArrayList(moduleConfigDo1, moduleConfigDo2);
        moduleExtraDTO.setModuleConfigDos(moduleConfigDos);
        ModuleExtraDTO moduleExtraDTO1 = unifiedModuleExtraFacade.eduFreeDealModuleRename(moduleExtraDTO);
        assertNotNull(moduleExtraDTO1);
    }

    @Test
    public void testGetModuleConfigDoList() {
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        when(douHuService.hitEnableCardStyleV2(any())).thenReturn(true);
        String value = "[{\"categories\":\"mt1904,dp1904\",\"configs\":[{\"configKey\":\"服务详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"领取须知\",\"configValue\":\"gcdealdetail_newtuandealtab_photography_buyrules\"},{\"configKey\":\"适用门店\",\"configValue\":\"gcdealdetail_newtuandealtab_shopinfo\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]} ,{\"categories\":\"mt447,dp447\",\"configs\":[{\"configKey\":\"服务详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"领取须知\",\"configValue\":\"gcdealdetail_newtuandealtab_photography_buyrules\"},{\"configKey\":\"适用门店\",\"configValue\":\"gcdealdetail_newtuandealtab_shopinfo\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]} ,{\"categories\":\"mt504,mt910\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_photography_buyrules\"},{\"configKey\":\"适用门店\",\"configValue\":\"gcdealdetail_newtuandealtab_shopinfo\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"dp504,dp910\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_photography_buyrules\"},{\"configKey\":\"适用门店\",\"configValue\":\"tuandeal_newtuandealtab_shopinfo\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp501\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_beauty_hairwithtech_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_beauty_hair_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp501_old\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_beauty_hairwithtech_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_beauty_hair_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp516\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_beauty_hair_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp502\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp503\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_beauty_spa_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp1204\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_education_shengxue_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_education_shengxue_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp1004\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_baby_photoedutg_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_baby_photoedutg_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp405\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_flower_all_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp505,dp509,dp511,dp512,dp513,dp514,dp705\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_beauty_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp507\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_beauty_yoga_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp404,dp412,dp1201,dp1202,dp1203,dp1206,dp1207,dp1208,dp1209,dp1210,dp1211,dp1212,dp1213\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_education_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_education_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp1210_online\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_online_education_tuandetail\"},{\"configKey\":\"授课老师\",\"configValue\":\"tuandeal_newtuandealtab_online_education_teachers\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_education_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp301\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_ktv_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp403,dp407,dp437,dp409,dp413,dp414,dp415,dp417,dp448,dp436,dp436,dp445,dp452,dp450\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_easylife_structure_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_easylife_structure_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp401\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_medicinehealthcheck_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp506\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_dentis_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_dentis_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp453\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_tuandetail_xlzx\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"mt501\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_beauty_hairwithtech_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_beauty_hair_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt501_old\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_beauty_hairwithtech_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_beauty_hair_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt516\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_beauty_hair_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt502\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_beauty_nail_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt503\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_beauty_spa_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt1004\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_children_photoedutg_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_children_photoedutg_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt405\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_flower_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt505,mt509,mt511,mt512,mt513,mt514,mt705\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_beauty_default_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt507\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_beauty_yoga_default_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt404,mt412,mt1201,mt1202,mt1203,mt1206,mt1207,mt1208,mt1209,mt1210,mt1211,mt1212,mt1213\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_education_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt1210_online\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_online_education_tuandetail\"},{\"configKey\":\"授课老师\",\"configValue\":\"gcdealdetail_newtuandealtab_online_education_teachers\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_education_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt301\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_ktv_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt1204\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_education_shengxue_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt401\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_medicinehealthcheck_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_medicine_healthcheck_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt506\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_dentis_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_dentis_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt448\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_easylife_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_easylife_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt1701\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_pet_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_pet_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt453\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail_xlzx\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"dp1701\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_pet_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_pet_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"mt322\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_game_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"dp312\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"mt312\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"dp1702,dp1704\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_pet_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"mt1702,mt1704\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_pet_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt401_new\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_medicinehealthcheck_itemsdetail\"},{\"configKey\":\"服务流程\",\"configValue\":\"gcdealdetail_newtuandealtab_medicinehealthcheck_service_process\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_medicinehealthcheck_reviews\"}]},{\"categories\":\"dp401_new\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_medicinehealthcheck_itemsdetail\"},{\"configKey\":\"服务流程\",\"configValue\":\"tuandeal_newtuandealtab_medicinehealthcheck_service_process\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_medicinehealthcheck_reviews\"}]},{\"categories\":\"mt401_standard\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_medicinehealthcheck_newitemsdetail\"},{\"configKey\":\"服务流程\",\"configValue\":\"gcdealdetail_newtuandealtab_medicinehealthcheck_service_process\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_medicinehealthcheck_reviews\"}]},{\"categories\":\"dp401_standard\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_medicinehealthcheck_newitemsdetail\"},{\"configKey\":\"服务流程\",\"configValue\":\"tuandeal_newtuandealtab_medicinehealthcheck_service_process\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_medicinehealthcheck_reviews\"}]},{\"categories\":\"mt1604_standard\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_medicine_eye_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"dp1604_standard\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_medicine_eye_newtuandeal\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp712\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_mall_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"mt712\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_mall_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"dp414_wuyoutong\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_tuandetail\"},{\"configKey\":\"评价模块\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"},{\"configKey\":\"常见问题\",\"configValue\":\"tuandeal_newtuandealtab_qa\"}]},{\"categories\":\"mt414_wuyoutong\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"评价模块\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"},{\"configKey\":\"常见问题\",\"configValue\":\"gcdealdetail_newtuandealtab_qa\"}]},{\"categories\":\"dp2001,dp2002,dp2003,dp2004,dp2005,dp2006,dp2007,dp1004,dp504\",\"configs\":[{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"},{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_buyrules\"}]},{\"categories\":\"mt2001,mt2002,mt2003,mt2004,mt2005,mt2006,mt2007,mt1004,mt504\",\"configs\":[{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"},{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_buyrules\"}]},{\"categories\":\"dp1611_resv\",\"configs\":[{\"configKey\":\"疫苗详情\",\"configValue\":\"tuandeal_newtuandealtab_tuandetail\"},{\"configKey\":\"预约须知\",\"configValue\":\"tuandeal_newtuandealtab_buyrules\"}]},{\"categories\":\"mt1611_resv\",\"configs\":[{\"configKey\":\"疫苗详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"预约须知\",\"configValue\":\"gcdealdetail_newtuandealtab_buyrules\"}]}]";
        utilities.when(() -> Lion.getStringValue(any())).thenReturn(value);

        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        envCtx.setClientType(1);
        UnifiedCtx unifiedCtx =  new UnifiedCtx(envCtx);
        List<ModuleConfigDo> moduleConfigDoList =
                unifiedModuleExtraFacade.getModuleConfigDoList(new UnifiedModuleExtraReq(), unifiedCtx, 1611, false,
                        false, true, false);
        assertNotNull(moduleConfigDoList);
    }
}
