package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericModuleResponse;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericProductDetailPageResponse;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;

@RunWith(MockitoJUnitRunner.class)
public class GuaranteeBuilderServiceGetCommonModuleGuaranteeTest {

    @InjectMocks
    private GuaranteeBuilderService guaranteeBuilderService;

    private Object invokePrivateMethod(DealCtx ctx) throws Exception {
        Method method = GuaranteeBuilderService.class.getDeclaredMethod("getCommonModuleGuarantee", DealCtx.class);
        method.setAccessible(true);
        return method.invoke(guaranteeBuilderService, ctx);
    }

    /**
     * Test case for getCommonModuleGuarantee when context has null response
     */
    @Test
    public void testGetCommonModuleGuaranteeWithNullResponse() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.getCommonModuleResponse()).thenReturn(null);
        // act
        Object result = invokePrivateMethod(ctx);
        // assert
        assertNull(result);
    }

    /**
     * Test case for getCommonModuleGuarantee when context has empty module response map
     */
    @Test
    public void testGetCommonModuleGuaranteeWithEmptyModuleResponse() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        GenericProductDetailPageResponse response = mock(GenericProductDetailPageResponse.class);
        when(ctx.getCommonModuleResponse()).thenReturn(response);
        when(response.getModuleResponse()).thenReturn(new HashMap<>());
        // act
        Object result = invokePrivateMethod(ctx);
        // assert
        assertNull(result);
    }

    /**
     * Test case for getCommonModuleGuarantee when module response exists but guarantee info is null
     */
    @Test
    public void testGetCommonModuleGuaranteeWithNullGuaranteeInfo() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        GenericProductDetailPageResponse response = mock(GenericProductDetailPageResponse.class);
        Map<String, GenericModuleResponse> moduleResponseMap = new HashMap<>();
        GenericModuleResponse guaranteeInfoResponse = mock(GenericModuleResponse.class);
        moduleResponseMap.put("module_detail_guarantee_info_tag", guaranteeInfoResponse);
        when(ctx.getCommonModuleResponse()).thenReturn(response);
        when(response.getModuleResponse()).thenReturn(moduleResponseMap);
        when(guaranteeInfoResponse.getModuleVO()).thenReturn(null);
        // act
        Object result = invokePrivateMethod(ctx);
        // assert
        assertNull(result);
    }

    /**
     * Test case for getCommonModuleGuarantee when guarantee info exists and is valid
     */
    @Test
    public void testGetCommonModuleGuaranteeWithValidGuaranteeInfo() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        GenericProductDetailPageResponse response = mock(GenericProductDetailPageResponse.class);
        Map<String, GenericModuleResponse> moduleResponseMap = new HashMap<>();
        GenericModuleResponse guaranteeInfoResponse = mock(GenericModuleResponse.class);
        moduleResponseMap.put("module_detail_guarantee_info_tag", guaranteeInfoResponse);
        // Create a JSON object that can be parsed to ProductDetailGuaranteeVO
        JSONObject moduleVO = new JSONObject();
        moduleVO.put("title", "Guarantee Title");
        when(ctx.getCommonModuleResponse()).thenReturn(response);
        when(response.getModuleResponse()).thenReturn(moduleResponseMap);
        when(guaranteeInfoResponse.getModuleVO()).thenReturn(moduleVO);
        // act
        Object result = invokePrivateMethod(ctx);
        // assert
        assertNotNull(result);
    }

    /**
     * Test case for getCommonModuleGuarantee when an exception occurs during processing
     */
    @Test
    public void testGetCommonModuleGuaranteeWithException() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        // Mock to throw exception when getCommonModuleResponse is called
        when(ctx.getCommonModuleResponse()).thenThrow(new RuntimeException("Test exception"));
        // act
        Object result = invokePrivateMethod(ctx);
        // assert
        assertNull(result);
        // Verify the method was called
        verify(ctx).getCommonModuleResponse();
    }

    /**
     * Test case for getCommonModuleGuarantee when module response map is null
     */
    @Test
    public void testGetCommonModuleGuaranteeWithNullModuleResponseMap() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        GenericProductDetailPageResponse response = mock(GenericProductDetailPageResponse.class);
        when(ctx.getCommonModuleResponse()).thenReturn(response);
        when(response.getModuleResponse()).thenReturn(null);
        // act
        Object result = invokePrivateMethod(ctx);
        // assert
        assertNull(result);
    }

    /**
     * Test case for getCommonModuleGuarantee when guarantee info module key doesn't exist
     */
    @Test
    public void testGetCommonModuleGuaranteeWithMissingGuaranteeInfoKey() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        GenericProductDetailPageResponse response = mock(GenericProductDetailPageResponse.class);
        Map<String, GenericModuleResponse> moduleResponseMap = new HashMap<>();
        // Add a different module key, not the guarantee info one
        moduleResponseMap.put("some_other_module_key", mock(GenericModuleResponse.class));
        when(ctx.getCommonModuleResponse()).thenReturn(response);
        when(response.getModuleResponse()).thenReturn(moduleResponseMap);
        // act
        Object result = invokePrivateMethod(ctx);
        // assert
        assertNull(result);
    }

    /**
     * Test case for getCommonModuleGuarantee when JSON parsing throws an exception
     */
    @Test
    public void testGetCommonModuleGuaranteeWithJsonParsingException() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        GenericProductDetailPageResponse response = mock(GenericProductDetailPageResponse.class);
        Map<String, GenericModuleResponse> moduleResponseMap = new HashMap<>();
        GenericModuleResponse guaranteeInfoResponse = mock(GenericModuleResponse.class);
        moduleResponseMap.put("module_detail_guarantee_info_tag", guaranteeInfoResponse);
        when(ctx.getCommonModuleResponse()).thenReturn(response);
        when(response.getModuleResponse()).thenReturn(moduleResponseMap);
        when(guaranteeInfoResponse.getModuleVO()).thenReturn(new JSONObject());
        // Create a problematic JSON object that will cause parsing issues
        // Instead of mocking static methods, we'll make the moduleVO.getModuleVO() throw an exception
        when(guaranteeInfoResponse.getModuleVO()).thenThrow(new RuntimeException("JSON parsing error"));
        // act
        Object result = invokePrivateMethod(ctx);
        // assert
        assertNull(result);
    }
}
