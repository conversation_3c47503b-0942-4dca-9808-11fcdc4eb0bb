package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.mobile.mapi.dztgdetail.biz.immersiveimage.ImmersiveImageService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageFilterRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageFilterVO;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import static org.mockito.Mockito.*;
import java.util.Arrays;
import static org.mockito.ArgumentMatchers.any;
import java.util.List;
import java.lang.reflect.Field;
import com.dianping.deal.detail.enums.PlatformEnum;
import static org.junit.Assert.*;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ImmersiveImageFilterFacadeTest {

    @InjectMocks
    private ImmersiveImageFilterFacade immersiveImageFilterFacade;

    @Mock
    private ImmersiveImageService immersiveImageService;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Test(expected = IllegalArgumentException.class)
    public void testGetImmersiveImageFilterDealGroupIdInvalid() throws Throwable {
        GetImmersiveImageFilterRequest request = new GetImmersiveImageFilterRequest();
        request.setDealGroupId(-1);
        request.setShopId(1L);
        EnvCtx envCtx = new EnvCtx();
        immersiveImageFilterFacade.getImmersiveImageFilter(request, envCtx);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetImmersiveImageFilterShopIdInvalid() throws Throwable {
        GetImmersiveImageFilterRequest request = new GetImmersiveImageFilterRequest();
        request.setDealGroupId(1);
        request.setShopId(-1L);
        EnvCtx envCtx = new EnvCtx();
        immersiveImageFilterFacade.getImmersiveImageFilter(request, envCtx);
    }
    // Note: The following tests are limited by the inability to mock or inject the list of ImmersiveImageService instances
    // directly into the ImmersiveImageFilterFacade due to the constraints provided.
    // The NullPointerException issue suggests a need for such an initialization, which cannot be directly addressed here.
}
