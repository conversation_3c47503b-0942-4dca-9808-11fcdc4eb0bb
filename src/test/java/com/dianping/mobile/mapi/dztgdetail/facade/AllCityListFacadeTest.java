package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.gis.remote.dto.CityInfoDTO;
import com.dianping.gis.remote.service.CityInfoService;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.city.AllCityVO;
import com.google.common.collect.Lists;
import com.meituan.service.mobile.group.geo.bean.CityInfo;
import com.meituan.service.mobile.group.geo.service.CityService;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.List;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class AllCityListFacadeTest {

    @InjectMocks
    private AllCityListFacade allCityListFacade;

    @Mock
    private CityInfoService dpCityInfoService;
    @Mock
    private CityService mtCityService;


    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void test_mt() {

        List<CityInfo> cityInfoList = Lists.newArrayList();
        CityInfo cityInfo = new CityInfo();
        cityInfo.setName("北京");
        cityInfo.setId(1);
        cityInfo.setFirstChar("a");
        cityInfoList.add(cityInfo);
        when(mtCityService.listOpenCities()).thenReturn(cityInfoList);


        // 调用方法
        AllCityVO allCityVO = allCityListFacade.getAllCityList(true);

        // 验证结果
        assertNotNull(allCityVO);
        assertNotNull(allCityVO.getCityClusters());

        assertEquals(allCityVO.getCityClusters().size(),1);
        assertEquals("a", allCityVO.getCityClusters().get(0).getFirstLetter());
    }


    @Test
    public void test_dp() {

        List<CityInfoDTO> cityInfoList = Lists.newArrayList();
        CityInfoDTO cityInfo = new CityInfoDTO();
        cityInfo.setCityName("北京");
        cityInfo.setCityId(1);
        cityInfo.setCityPyName("aaaa");
        cityInfoList.add(cityInfo);
        when(dpCityInfoService.getInlandCityList(eq(0), anyInt())).thenReturn(cityInfoList);
        when(dpCityInfoService.getInlandCityList(eq(1000), anyInt())).thenReturn(null);



        // 调用方法
        AllCityVO allCityVO = allCityListFacade.getAllCityList(false);

        // 验证结果
        assertNotNull(allCityVO);
        assertNotNull(allCityVO.getCityClusters());

        assertEquals(allCityVO.getCityClusters().size(),1);
        assertEquals("a", allCityVO.getCityClusters().get(0).getFirstLetter());
    }


}
