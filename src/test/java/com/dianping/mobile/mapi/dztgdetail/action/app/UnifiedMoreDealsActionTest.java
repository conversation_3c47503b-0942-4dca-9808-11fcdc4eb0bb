package com.dianping.mobile.mapi.dztgdetail.action.app;

// ... 其他导入 ...
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.action.app.UnifiedMoreDealsAction;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedMoreDealsReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.more.UnifiedMoreList;
import com.dianping.mobile.mapi.dztgdetail.facade.UnifiedMoreDealsFacade;
import com.dianping.mobile.mapi.dztgdetail.util.AntiCrawlerUtils;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.mockito.Mockito.*;
import java.io.IOException;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;
import org.mockito.Mockito;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(JUnit4.class)
public class UnifiedMoreDealsActionTest {

    @InjectMocks
    private UnifiedMoreDealsAction unifiedMoreDealsAction;

    @Mock
    private UnifiedMoreDealsFacade unifiedMoreDealsFacade;

    @Mock
    private IMobileContext iMobileContext;

    @Mock
    private UnifiedMoreDealsReq unifiedMoreDealsReq;

    // ... 其他代码 ...
    @Mock
    private UnifiedMoreList unifiedMoreList;

    // ... 其他代码 ...
    @Test
    public void testExecuteSuccess() throws Throwable {
        when(unifiedMoreDealsFacade.queryUnifiedMoreList(unifiedMoreDealsReq, null, iMobileContext)).thenReturn(unifiedMoreList);
        when(unifiedMoreList.getTitle()).thenReturn("title");
        IMobileResponse result = unifiedMoreDealsAction.execute(unifiedMoreDealsReq, iMobileContext);
        assertNotNull(result);
        if (!result.equals(Resps.SYSTEM_ERROR)) {
            assertEquals(unifiedMoreList.getTitle(), ((UnifiedMoreList) result.getData()).getTitle());
        }
    }

    @Test
    public void testExecuteNoData() throws Throwable {
        when(unifiedMoreDealsFacade.queryUnifiedMoreList(unifiedMoreDealsReq, null, iMobileContext)).thenReturn(unifiedMoreList);
        when(unifiedMoreList.getTitle()).thenReturn("");
        IMobileResponse result = unifiedMoreDealsAction.execute(unifiedMoreDealsReq, iMobileContext);
        assertNotNull(result);
        assertEquals(Resps.NoDataResp.getStatusCode(), result.getStatusCode());
    }

    @Test
    public void testExecuteNullData() throws Throwable {
        when(unifiedMoreDealsFacade.queryUnifiedMoreList(unifiedMoreDealsReq, null, iMobileContext)).thenReturn(null);
        IMobileResponse result = unifiedMoreDealsAction.execute(unifiedMoreDealsReq, iMobileContext);
        assertNotNull(result);
        assertEquals(Resps.NoDataResp.getStatusCode(), result.getStatusCode());
    }

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testExecuteException() throws Throwable {
        when(unifiedMoreDealsFacade.queryUnifiedMoreList(unifiedMoreDealsReq, null, iMobileContext)).thenThrow(new RuntimeException());
        IMobileResponse result = unifiedMoreDealsAction.execute(unifiedMoreDealsReq, iMobileContext);
        assertNotNull(result);
        assertEquals(Resps.SYSTEM_ERROR, result);
    }

    @Test(expected = Exception.class)
    public void testExecuteUnauthenticLogin() throws Throwable {
        Mockito.doThrow(new Exception()).when(AntiCrawlerUtils.class);
        AntiCrawlerUtils.antiUnauthenticLogin(iMobileContext);
        unifiedMoreDealsAction.execute(unifiedMoreDealsReq, iMobileContext);
    }
}
