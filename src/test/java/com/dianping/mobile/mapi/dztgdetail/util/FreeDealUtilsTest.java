package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.enums.FreeDealEnum;
import com.dianping.mobile.mapi.dztgdetail.entity.FreeDealConfig;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;

@RunWith(MockitoJUnitRunner.class)
public class FreeDealUtilsTest {

    private MockedStatic<Lion> mockedLion;

    @Before
    public void setUp() {
        mockedLion = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        if (mockedLion != null) {
            mockedLion.close();
        }
    }

    /**
     * 测试 getFreeDealConfig 方法，当 freeDealType 为 null 时应返回 null。
     */
    @Test
    public void testGetFreeDealConfig_NullFreeDealType() {
        // arrange
        FreeDealEnum freeDealType = null;
        // act
        FreeDealConfig result = FreeDealUtils.getFreeDealConfig(freeDealType);
        // assert
        assertNull("当 freeDealType 为 null 时，应返回 null。", result);
    }

    /**
     * 测试 getFreeDealConfig 方法，当 Lion 返回空字符串时应返回 null。
     * 修复：确保在测试结束后关闭 MockedStatic 对象。
     */
    @Test
    public void testGetFreeDealConfig_BlankLionConfig() {
        // arrange
        FreeDealEnum freeDealType = FreeDealEnum.HOME_DESIGN_BOOKING;
        mockedLion.when(() -> Lion.getString(anyString(), anyString())).thenReturn("");
        // act
        FreeDealConfig result = FreeDealUtils.getFreeDealConfig(freeDealType);
        // assert
        assertNull("当 Lion 返回空字符串时，应返回 null。", result);
    }

    /**
     * 测试 getFreeDealConfig 方法，当 Lion 返回有效配置时应返回 FreeDealConfig 对象。
     * 修复：确保在测试结束后关闭 MockedStatic 对象。
     */
    @Test
    public void testGetFreeDealConfig_ValidLionConfig() {
        // arrange
        FreeDealEnum freeDealType = FreeDealEnum.HOME_DESIGN_BOOKING;
        String validConfig = "{\"priceTags\":[],\"saleDescPrefix\":\"\",\"buttonText\":\"\",\"mtAppSchema\":\"\",\"mtH5Schema\":\"\",\"dpAppSchema\":\"\",\"dpH5Schema\":\"\",\"emptyButtonText\":\"\",\"limit\":[],\"showMarketPrice\":false}";
        mockedLion.when(() -> Lion.getString(anyString(), anyString())).thenReturn(validConfig);
        // act
        FreeDealConfig result = FreeDealUtils.getFreeDealConfig(freeDealType);
        // assert
        assertNotNull("当 Lion 返回有效配置时，应返回 FreeDealConfig 对象。", result);
    }
}
