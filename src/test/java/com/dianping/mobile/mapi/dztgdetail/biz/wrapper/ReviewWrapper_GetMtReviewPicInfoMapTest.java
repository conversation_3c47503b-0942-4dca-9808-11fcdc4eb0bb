package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.ugc.pic.remote.dto.MtReviewPicInfo;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import static org.mockito.Mockito.when;
import com.google.common.collect.Lists;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class ReviewWrapper_GetMtReviewPicInfoMapTest {

    @Mock
    private Future future;

    private ReviewWrapper reviewWrapper = new ReviewWrapper();

    /**
     * 测试 getMtReviewPicInfoMap 方法，当 mtReviewPicFuture 为 null 时
     */
    @Test
    public void testGetMtReviewPicInfoMapWhenFutureIsNull() throws Throwable {
        // arrange
        Future mtReviewPicFuture = null;
        // act
        Map<Long, MtReviewPicInfo> result = reviewWrapper.getMtReviewPicInfoMap(mtReviewPicFuture);
        // assert
        assertEquals(Collections.emptyMap(), result);
    }

    /**
     * 测试 getMtReviewPicInfoMap 方法，当 mtReviewPicFuture 不为 null，但获取的结果为空列表时
     */
    @Test
    public void testGetMtReviewPicInfoMapWhenResultIsEmpty() throws Throwable {
        // arrange
        when(future.get()).thenReturn(Collections.emptyList());
        // act
        Map<Long, MtReviewPicInfo> result = reviewWrapper.getMtReviewPicInfoMap(future);
        // assert
        assertEquals(Collections.emptyMap(), result);
    }

    /**
     * 测试 getMtReviewPicInfoMap 方法，当 mtReviewPicFuture 不为 null，获取的结果为非空列表时
     */
    @Test
    public void testGetMtReviewPicInfoMapWhenResultIsNotEmpty() throws Throwable {
        // arrange
        MtReviewPicInfo mtReviewPicInfo = new MtReviewPicInfo();
        mtReviewPicInfo.setPicId(1L);
        when(future.get()).thenReturn(Lists.newArrayList(mtReviewPicInfo));
        // act
        Map<Long, MtReviewPicInfo> result = reviewWrapper.getMtReviewPicInfoMap(future);
        // assert
        assertEquals(1, result.size());
        assertEquals(mtReviewPicInfo, result.get(1L));
    }
}
