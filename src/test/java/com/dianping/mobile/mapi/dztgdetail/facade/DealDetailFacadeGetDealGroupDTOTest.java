package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.deal.detail.calc.enums.MixedContentType;
import com.dianping.deal.detail.dto.DealGroupTemplateDetailDTO;
import com.dianping.deal.detail.dto.MixedContent;
import com.dianping.deal.detail.enums.BlockTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.DealCategoryFactory;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule.DealDetailSpecificModuleHandlerContainer;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.imagetext.strategy.ImageTextDetailHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.HaimaWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealImageTextDetailReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ContentDetailPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ImageTextDetailPBO;
import com.dianping.mobile.mapi.dztgdetail.exception.QueryCenterResultException;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DealDetailFacadeGetDealGroupDTOTest {

    @InjectMocks
    private DealDetailFacade dealDetailFacade;

    @Mock
    private DealGroupQueryService queryCenterDealGroupQueryService;

    private DealImageTextDetailReq req;

    private EnvCtx envCtx;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private DealDetailSpecificModuleHandlerContainer dealDetailSpecificModuleHandlerContainer;

    @Mock
    private DealCategoryFactory dealCategoryFactory;

    @Mock
    private HaimaWrapper haimaWrapper;

    @Mock
    private ImageTextDetailHandler imageTextDetailHandler;

    @Before
    public void setUp() {
        req = new DealImageTextDetailReq();
        req.setDealgroupid(123456);
        envCtx = new EnvCtx();
    }

    // Helper method to invoke the private getDealGroupDTO method using reflection
    private DealGroupDTO invokePrivateGetDealGroupDTO(DealImageTextDetailReq req, EnvCtx envCtx) throws Exception {
        Method method = DealDetailFacade.class.getDeclaredMethod("getDealGroupDTO", DealImageTextDetailReq.class, EnvCtx.class);
        method.setAccessible(true);
        return (DealGroupDTO) method.invoke(dealDetailFacade, req, envCtx);
    }

    /**
     * Test successful query with valid response
     */
    @Test
    public void testGetDealGroupDTO_Success() throws Throwable {
        // arrange
        DealGroupDTO expectedDTO = new DealGroupDTO();
        expectedDTO.setDpDealGroupId(123456L);
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(0);
        response.setData(new QueryDealGroupListResult());
        response.getData().setList(Arrays.asList(expectedDTO));
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(response);
        // act
        DealGroupDTO result = invokePrivateGetDealGroupDTO(req, envCtx);
        // assert
        assertNotNull(result);
        assertEquals(expectedDTO.getDpDealGroupId(), result.getDpDealGroupId());
    }

    /**
     * Test when query service returns null response
     */
    @Test
    public void testGetDealGroupDTO_NullResponse() throws Throwable {
        // arrange
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(null);
        // act
        try {
            invokePrivateGetDealGroupDTO(req, envCtx);
            fail("Expected QueryCenterResultException");
        } catch (InvocationTargetException e) {
            // assert
            assertTrue(e.getCause() instanceof QueryCenterResultException);
        }
    }

    /**
     * Test when query service returns non-zero code
     */
    @Test
    public void testGetDealGroupDTO_NonZeroCode() throws Throwable {
        // arrange
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(1);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(response);
        // act
        try {
            invokePrivateGetDealGroupDTO(req, envCtx);
            fail("Expected QueryCenterResultException");
        } catch (InvocationTargetException e) {
            // assert
            assertTrue(e.getCause() instanceof QueryCenterResultException);
        }
    }

    /**
     * Test when response data list is empty
     */
    @Test
    public void testGetDealGroupDTO_EmptyList() throws Throwable {
        // arrange
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(0);
        response.setData(new QueryDealGroupListResult());
        response.getData().setList(Collections.emptyList());
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(response);
        // act
        try {
            invokePrivateGetDealGroupDTO(req, envCtx);
            fail("Expected QueryCenterResultException");
        } catch (InvocationTargetException e) {
            // assert
            assertTrue(e.getCause() instanceof QueryCenterResultException);
        }
    }

    /**
     * Test when first item in response list is null
     */
    @Test
    public void testGetDealGroupDTO_NullFirstItem() throws Throwable {
        // arrange
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(0);
        response.setData(new QueryDealGroupListResult());
        response.getData().setList(Arrays.asList((DealGroupDTO) null));
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(response);
        // act
        try {
            invokePrivateGetDealGroupDTO(req, envCtx);
            fail("Expected QueryCenterResultException");
        } catch (InvocationTargetException e) {
            // assert
            assertTrue(e.getCause() instanceof QueryCenterResultException);
        }
    }

    /**
     * Test when service throws TException
     */
    @Test
    public void testGetDealGroupDTO_ServiceException() throws Throwable {
        // arrange
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenThrow(new TException("Service error"));
        // act
        try {
            invokePrivateGetDealGroupDTO(req, envCtx);
            fail("Expected TException");
        } catch (InvocationTargetException e) {
            // assert
            assertTrue(e.getCause() instanceof TException);
        }
    }

    @Test
    public void testQueryImageTextFromQueryCenter_NullDealGroupBase() throws Throwable {
        // arrange
        DealImageTextDetailReq req = new DealImageTextDetailReq();
        req.setDealgroupid(123);
        EnvCtx envCtx = new EnvCtx();
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(0);
        QueryDealGroupListResult result = new QueryDealGroupListResult();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDpDealGroupId(123L);
        // Null basic
        dealGroupDTO.setBasic(null);
        result.setList(Collections.singletonList(dealGroupDTO));
        response.setData(result);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(response);
        // act
        ImageTextDetailPBO resultPBO = dealDetailFacade.queryImageTextFromQueryCenter(req, envCtx);
        // assert
        assertNotNull(resultPBO);
    }

    @Test
    public void testQueryImageTextFromQueryCenter_StatusLessThanOne() throws Throwable {
        // arrange
        DealImageTextDetailReq req = new DealImageTextDetailReq();
        req.setDealgroupid(123);
        EnvCtx envCtx = new EnvCtx();
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(0);
        QueryDealGroupListResult result = new QueryDealGroupListResult();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDpDealGroupId(123L);
        DealGroupBasicDTO basicDTO = new DealGroupBasicDTO();
        // Status < 1
        basicDTO.setStatus(0);
        dealGroupDTO.setBasic(basicDTO);
        result.setList(Collections.singletonList(dealGroupDTO));
        response.setData(result);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(response);
        // act
        ImageTextDetailPBO resultPBO = dealDetailFacade.queryImageTextFromQueryCenter(req, envCtx);
        // assert
        assertNotNull(resultPBO);
    }

    @Test
    public void testQueryImageTextFromQueryCenter_EmptyTemplateDetailDTOs() throws Throwable {
        // arrange
        DealImageTextDetailReq req = new DealImageTextDetailReq();
        req.setDealgroupid(123);
        EnvCtx envCtx = new EnvCtx();
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(0);
        QueryDealGroupListResult result = new QueryDealGroupListResult();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDpDealGroupId(123L);
        DealGroupBasicDTO basicDTO = new DealGroupBasicDTO();
        basicDTO.setStatus(1);
        dealGroupDTO.setBasic(basicDTO);
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(100L);
        dealGroupDTO.setCategory(categoryDTO);
        result.setList(Collections.singletonList(dealGroupDTO));
        response.setData(result);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(response);
        Future<?> mockFuture = mock(Future.class);
        when(dealGroupWrapper.preDealGroupObjectDetail(anyInt(), anyBoolean())).thenReturn(mockFuture);
        when(dealGroupWrapper.getFutureResult(mockFuture)).thenReturn(Collections.emptyList());
        // act
        ImageTextDetailPBO resultPBO = dealDetailFacade.queryImageTextFromQueryCenter(req, envCtx);
        // assert
        assertNotNull(resultPBO);
    }

    @Test
    public void testQueryImageTextFromQueryCenter_HideDetailV2True() throws Throwable {
        // arrange
        DealImageTextDetailReq req = new DealImageTextDetailReq();
        req.setDealgroupid(123);
        EnvCtx envCtx = new EnvCtx();
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(0);
        QueryDealGroupListResult result = new QueryDealGroupListResult();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDpDealGroupId(123L);
        DealGroupBasicDTO basicDTO = new DealGroupBasicDTO();
        basicDTO.setStatus(1);
        dealGroupDTO.setBasic(basicDTO);
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(100L);
        dealGroupDTO.setCategory(categoryDTO);
        // Add voucher attribute
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(DealAttrKeys.VOUCHER);
        attrDTO.setValue(Collections.singletonList(DealAttrKeys.VOUCHER_VALUE));
        attrs.add(attrDTO);
        dealGroupDTO.setAttrs(attrs);
        result.setList(Collections.singletonList(dealGroupDTO));
        response.setData(result);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(response);
        Future<?> mockFuture = mock(Future.class);
        when(dealGroupWrapper.preDealGroupObjectDetail(anyInt(), anyBoolean())).thenReturn(mockFuture);
        List<DealGroupTemplateDetailDTO> templateDetailDTOs = new ArrayList<>();
        DealGroupTemplateDetailDTO templateDetailDTO = new DealGroupTemplateDetailDTO();
        templateDetailDTO.setType(BlockTypeEnum.PRODUCT_INFO.getBlockType());
        templateDetailDTOs.add(templateDetailDTO);
        when(dealGroupWrapper.getFutureResult(mockFuture)).thenReturn(templateDetailDTOs);
        // Mock the imageTextDetailHandler to return a specific result
        ImageTextDetailPBO expectedResult = new ImageTextDetailPBO();
        when(imageTextDetailHandler.buildImageTextDetail(any(), any(), any(), any())).thenReturn(expectedResult);
        // act
        ImageTextDetailPBO resultPBO = dealDetailFacade.queryImageTextFromQueryCenter(req, envCtx);
        // assert
        assertNotNull(resultPBO);
    }

    @Test
    public void testQueryImageTextFromQueryCenter_MissingProductOptional() throws Throwable {
        // arrange
        DealImageTextDetailReq req = new DealImageTextDetailReq();
        req.setDealgroupid(123);
        EnvCtx envCtx = new EnvCtx();
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(0);
        QueryDealGroupListResult result = new QueryDealGroupListResult();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDpDealGroupId(123L);
        DealGroupBasicDTO basicDTO = new DealGroupBasicDTO();
        basicDTO.setStatus(1);
        dealGroupDTO.setBasic(basicDTO);
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(100L);
        dealGroupDTO.setCategory(categoryDTO);
        result.setList(Collections.singletonList(dealGroupDTO));
        response.setData(result);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(response);
        Future<?> mockFuture = mock(Future.class);
        when(dealGroupWrapper.preDealGroupObjectDetail(anyInt(), anyBoolean())).thenReturn(mockFuture);
        List<DealGroupTemplateDetailDTO> templateDetailDTOs = new ArrayList<>();
        DealGroupTemplateDetailDTO templateDetailDTO = new DealGroupTemplateDetailDTO();
        // Not PRODUCT_INFO
        templateDetailDTO.setType(BlockTypeEnum.DETAIL_INFO.getBlockType());
        templateDetailDTOs.add(templateDetailDTO);
        when(dealGroupWrapper.getFutureResult(mockFuture)).thenReturn(templateDetailDTOs);
        // act
        ImageTextDetailPBO resultPBO = dealDetailFacade.queryImageTextFromQueryCenter(req, envCtx);
        // assert
        assertNotNull(resultPBO);
    }

    @Test
    public void testQueryImageTextFromQueryCenter_NormalFlow() throws Throwable {
        // arrange
        DealImageTextDetailReq req = new DealImageTextDetailReq();
        req.setDealgroupid(123);
        req.setPageSource("test_source");
        EnvCtx envCtx = new EnvCtx();
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(0);
        QueryDealGroupListResult result = new QueryDealGroupListResult();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDpDealGroupId(123L);
        DealGroupBasicDTO basicDTO = new DealGroupBasicDTO();
        basicDTO.setStatus(1);
        dealGroupDTO.setBasic(basicDTO);
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(100L);
        dealGroupDTO.setCategory(categoryDTO);
        // Add topPerformingProduct attribute
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("topPerformingProduct");
        attrDTO.setValue(Collections.singletonList("true"));
        attrs.add(attrDTO);
        dealGroupDTO.setAttrs(attrs);
        result.setList(Collections.singletonList(dealGroupDTO));
        response.setData(result);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(response);
        Future<?> mockFuture = mock(Future.class);
        when(dealGroupWrapper.preDealGroupObjectDetail(anyInt(), anyBoolean())).thenReturn(mockFuture);
        List<DealGroupTemplateDetailDTO> templateDetailDTOs = new ArrayList<>();
        DealGroupTemplateDetailDTO templateDetailDTO = new DealGroupTemplateDetailDTO();
        templateDetailDTO.setType(BlockTypeEnum.PRODUCT_INFO.getBlockType());
        // Add mixed contents
        List<MixedContent> mixedContents = new ArrayList<>();
        MixedContent mixedContent = new MixedContent();
        mixedContent.setType(MixedContentType.TEXT.name());
        mixedContent.setContent("Test content");
        mixedContents.add(mixedContent);
        templateDetailDTO.setMixedContents(mixedContents);
        templateDetailDTOs.add(templateDetailDTO);
        when(dealGroupWrapper.getFutureResult(mockFuture)).thenReturn(templateDetailDTOs);
        // Mock result
        ImageTextDetailPBO expectedResult = new ImageTextDetailPBO();
        List<ContentDetailPBO> contentDetails = new ArrayList<>();
        ContentDetailPBO contentDetailPBO = new ContentDetailPBO();
        contentDetailPBO.setTitle("Test Title");
        contentDetailPBO.setContents(new ArrayList<>());
        contentDetails.add(contentDetailPBO);
        expectedResult.setContents(contentDetails);
        when(imageTextDetailHandler.buildImageTextDetail(any(), any(), any(), any())).thenReturn(expectedResult);
        // act
        ImageTextDetailPBO resultPBO = dealDetailFacade.queryImageTextFromQueryCenter(req, envCtx);
        // assert
        assertNotNull(resultPBO);
        verify(imageTextDetailHandler).buildImageTextDetail(any(), any(), any(), any());
    }

    @Test
    public void testQueryImageTextFromQueryCenter_NullQueryResponse() throws Throwable {
        // arrange
        DealImageTextDetailReq req = new DealImageTextDetailReq();
        req.setDealgroupid(123);
        EnvCtx envCtx = new EnvCtx();
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(null);
        // act & assert
        try {
            dealDetailFacade.queryImageTextFromQueryCenter(req, envCtx);
            fail("Expected QueryCenterResultException");
        } catch (QueryCenterResultException e) {
            assertTrue(e.getMessage().contains("queryCenter returns null"));
        }
    }

    @Test
    public void testQueryImageTextFromQueryCenter_NonZeroResponseCode() throws Throwable {
        // arrange
        DealImageTextDetailReq req = new DealImageTextDetailReq();
        req.setDealgroupid(123);
        EnvCtx envCtx = new EnvCtx();
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        // Non-zero code
        response.setCode(1);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(response);
        // act & assert
        try {
            dealDetailFacade.queryImageTextFromQueryCenter(req, envCtx);
            fail("Expected QueryCenterResultException");
        } catch (QueryCenterResultException e) {
            assertTrue(e.getMessage().contains("queryCenter not success"));
        }
    }

    @Test
    public void testQueryImageTextFromQueryCenter_EmptyResponseList() throws Throwable {
        // arrange
        DealImageTextDetailReq req = new DealImageTextDetailReq();
        req.setDealgroupid(123);
        EnvCtx envCtx = new EnvCtx();
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(0);
        QueryDealGroupListResult result = new QueryDealGroupListResult();
        result.setList(Collections.emptyList());
        response.setData(result);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(response);
        // act & assert
        try {
            dealDetailFacade.queryImageTextFromQueryCenter(req, envCtx);
            fail("Expected QueryCenterResultException");
        } catch (QueryCenterResultException e) {
            assertTrue(e.getMessage().contains("queryDealGroupListResponse.getData().getList() is empty"));
        }
    }

    @Test
    public void testQueryImageTextFromQueryCenter_NullFirstItem() throws Throwable {
        // arrange
        DealImageTextDetailReq req = new DealImageTextDetailReq();
        req.setDealgroupid(123);
        EnvCtx envCtx = new EnvCtx();
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(0);
        QueryDealGroupListResult result = new QueryDealGroupListResult();
        result.setList(Collections.singletonList(null));
        response.setData(result);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(response);
        // act & assert
        try {
            dealDetailFacade.queryImageTextFromQueryCenter(req, envCtx);
            fail("Expected QueryCenterResultException");
        } catch (QueryCenterResultException e) {
            assertTrue(e.getMessage().contains("queryDealGroupListResponse.getData().getList().get(0) is null"));
        }
    }
}
