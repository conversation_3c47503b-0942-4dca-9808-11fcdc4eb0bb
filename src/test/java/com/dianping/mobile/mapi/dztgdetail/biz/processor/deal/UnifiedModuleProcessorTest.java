package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleExtraDTO;
import java.lang.reflect.Method;
import java.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedModuleProcessorTest {

    @InjectMocks
    private UnifiedModuleProcessor unifiedModuleProcessor;

    @Mock
    private DouHuService douHuService;

    @Mock
    private DealCtx ctx;

    private UnifiedModuleProcessor processor = new UnifiedModuleProcessor();

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private String invokeBuildKeyWithCategoryAndServiceType(DealCtx ctx, int publishCategoryId, String serviceType) throws Exception {
        Method method = UnifiedModuleProcessor.class.getDeclaredMethod("buildKeyWithCategoryAndServiceType", DealCtx.class, int.class, String.class);
        method.setAccessible(true);
        try {
            return (String) method.invoke(processor, ctx, publishCategoryId, serviceType);
        } catch (Exception e) {
            if (e.getCause() instanceof NullPointerException) {
                throw (NullPointerException) e.getCause();
            }
            throw e;
        }
    }

    private static Boolean fromCAIXI(String requestSource) {
        if (RequestSourceEnum.CAI_XI.getSource().equals(requestSource) || RequestSourceEnum.HOME_PAGE.getSource().equals(requestSource)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private boolean invokePrivateMethod(DealCtx ctx, List<ModuleConfigDo> result) throws Exception {
        Method method = UnifiedModuleProcessor.class.getDeclaredMethod("isNeedResetItemIndex", DealCtx.class, List.class);
        method.setAccessible(true);
        return (boolean) method.invoke(processor, ctx, result);
    }

    // Helper method to invoke private method using reflection
    @SuppressWarnings("unchecked")
    private List<ModuleConfigDo> invokePrivateMethod(UnifiedModuleProcessor processor, String methodName, DealCtx ctx, Map<String, List<ModuleConfigDo>> moduleConfigMaps, String categoriesKey) throws Throwable {
        try {
            Method method = UnifiedModuleProcessor.class.getDeclaredMethod(methodName, DealCtx.class, Map.class, String.class);
            method.setAccessible(true);
            return (List<ModuleConfigDo>) method.invoke(processor, ctx, moduleConfigMaps, categoriesKey);
        } catch (Exception e) {
            throw e.getCause();
        }
    }

    private Boolean invokeFromCAIXI(String requestSource) throws Exception {
        Method method = UnifiedModuleProcessor.class.getDeclaredMethod("fromCAIXI", String.class);
        method.setAccessible(true);
        return (Boolean) method.invoke(null, requestSource);
    }

    /**
     * 测试 publishCategoryId <= 0 时返回 null
     */
    @Test
    public void testBuildModuleExtraDTOWhenPublishCategoryIdIsZero() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        int publishCategoryId = 0;
        // act
        ModuleExtraDTO result = unifiedModuleProcessor.buildModuleExtraDTO(ctx, publishCategoryId, false, false, false, false, null);
        // assert
        assertNull(result);
    }

    /**
     * 测试场景：ctx.isMt() 返回 true，serviceType 不为空
     */
    @Test
    public void testBuildKeyWithCategoryAndServiceType_MtServiceTypeNotEmpty() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(true);
        int publishCategoryId = 123;
        String serviceType = "massage";
        // act
        String result = invokeBuildKeyWithCategoryAndServiceType(ctx, publishCategoryId, serviceType);
        // assert
        assertEquals("mt123_massage", result);
    }

    /**
     * 测试场景：ctx.isMt() 返回 true，serviceType 为空
     */
    @Test
    public void testBuildKeyWithCategoryAndServiceType_MtServiceTypeEmpty() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(true);
        int publishCategoryId = 123;
        String serviceType = "";
        // act
        String result = invokeBuildKeyWithCategoryAndServiceType(ctx, publishCategoryId, serviceType);
        // assert
        assertEquals("mt123", result);
    }

    /**
     * 测试场景：ctx.isMt() 返回 false，serviceType 不为空
     */
    @Test
    public void testBuildKeyWithCategoryAndServiceType_DpServiceTypeNotEmpty() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(false);
        int publishCategoryId = 456;
        String serviceType = "spa";
        // act
        String result = invokeBuildKeyWithCategoryAndServiceType(ctx, publishCategoryId, serviceType);
        // assert
        assertEquals("dp456_spa", result);
    }

    /**
     * 测试场景：ctx.isMt() 返回 false，serviceType 为空
     */
    @Test
    public void testBuildKeyWithCategoryAndServiceType_DpServiceTypeEmpty() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(false);
        int publishCategoryId = 456;
        String serviceType = "";
        // act
        String result = invokeBuildKeyWithCategoryAndServiceType(ctx, publishCategoryId, serviceType);
        // assert
        assertEquals("dp456", result);
    }

    /**
     * 测试场景：ctx.isMt() 返回 true，serviceType 为空白字符串
     */
    @Test
    public void testBuildKeyWithCategoryAndServiceType_MtServiceTypeBlank() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(true);
        int publishCategoryId = 123;
        String serviceType = "   ";
        // act
        String result = invokeBuildKeyWithCategoryAndServiceType(ctx, publishCategoryId, serviceType);
        // assert
        assertEquals("mt123", result);
    }

    /**
     * 测试场景：ctx.isMt() 返回 false，serviceType 为空白字符串
     */
    @Test
    public void testBuildKeyWithCategoryAndServiceType_DpServiceTypeBlank() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(false);
        int publishCategoryId = 456;
        String serviceType = "   ";
        // act
        String result = invokeBuildKeyWithCategoryAndServiceType(ctx, publishCategoryId, serviceType);
        // assert
        assertEquals("dp456", result);
    }

    /**
     * 测试场景：ctx.isMt() 返回 true，publishCategoryId 为负数
     */
    @Test
    public void testBuildKeyWithCategoryAndServiceType_MtNegativePublishCategoryId() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(true);
        int publishCategoryId = -123;
        String serviceType = "massage";
        // act
        String result = invokeBuildKeyWithCategoryAndServiceType(ctx, publishCategoryId, serviceType);
        // assert
        assertEquals("mt-123_massage", result);
    }

    /**
     * 测试场景：ctx.isMt() 返回 false，publishCategoryId 为负数
     */
    @Test
    public void testBuildKeyWithCategoryAndServiceType_DpNegativePublishCategoryId() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(false);
        int publishCategoryId = -456;
        String serviceType = "spa";
        // act
        String result = invokeBuildKeyWithCategoryAndServiceType(ctx, publishCategoryId, serviceType);
        // assert
        assertEquals("dp-456_spa", result);
    }

    /**
     * 测试场景：ctx 为 null，期望抛出 NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testBuildKeyWithCategoryAndServiceType_NullCtx() throws Throwable {
        // arrange
        DealCtx ctx = null;
        int publishCategoryId = 123;
        String serviceType = "massage";
        // act
        invokeBuildKeyWithCategoryAndServiceType(ctx, publishCategoryId, serviceType);
        // assert
        // 期望抛出 NullPointerException
    }

    /**
     * 场景1：result 列表为空，requestSource 不是 CAIXI 或 HOME_PAGE
     */
    @Test
    public void testIsNeedResetItemIndex_EmptyResult_NotCAIXI() throws Throwable {
        // arrange
        List<ModuleConfigDo> result = Collections.emptyList();
        // act
        boolean needReset = invokePrivateMethod(ctx, result);
        // assert
        assertFalse(needReset);
    }

    /**
     * 场景2：result 列表为空，requestSource 是 CAIXI 或 HOME_PAGE
     */
    @Test
    public void testIsNeedResetItemIndex_EmptyResult_IsCAIXI() throws Throwable {
        // arrange
        List<ModuleConfigDo> result = Collections.emptyList();
        // act
        boolean needReset = invokePrivateMethod(ctx, result);
        // assert
        assertFalse(needReset);
    }

    /**
     * 场景3：result 列表非空，requestSource 不是 CAIXI 或 HOME_PAGE
     */
    @Test
    public void testIsNeedResetItemIndex_NonEmptyResult_NotCAIXI() throws Throwable {
        // arrange
        List<ModuleConfigDo> result = Arrays.asList(new ModuleConfigDo(), new ModuleConfigDo());
        when(ctx.getRequestSource()).thenReturn("OTHER_SOURCE");
        // act
        boolean needReset = invokePrivateMethod(ctx, result);
        // assert
        assertFalse(needReset);
    }

    /**
     * 场景4：result 列表非空，requestSource 是 CAIXI 或 HOME_PAGE
     */
    @Test
    public void testIsNeedResetItemIndex_NonEmptyResult_IsCAIXI() throws Throwable {
        // arrange
        List<ModuleConfigDo> result = Arrays.asList(new ModuleConfigDo(), new ModuleConfigDo());
        when(ctx.getRequestSource()).thenReturn(RequestSourceEnum.CAI_XI.getSource());
        // act
        boolean needReset = invokePrivateMethod(ctx, result);
        // assert
        assertTrue(needReset);
    }

    /**
     * Scenario 1: The categoriesKey is found in one of the keys in moduleConfigMaps.
     */
    @Test
    public void testGetModuleConfigsFromCategoriesKey_CategoriesKeyFound() throws Throwable {
        // arrange
        Map<String, List<ModuleConfigDo>> moduleConfigMaps = new HashMap<>();
        List<ModuleConfigDo> expectedConfigs = Arrays.asList(new ModuleConfigDo(), new ModuleConfigDo());
        moduleConfigMaps.put("key1,key2", expectedConfigs);
        String categoriesKey = "key1";
        // act
        List<ModuleConfigDo> result = invokePrivateMethod(processor, "getModuleConfigsFromCategoriesKey", ctx, moduleConfigMaps, categoriesKey);
        // assert
        assertEquals(expectedConfigs, result);
    }

    /**
     * Scenario 2: The categoriesKey is not found in any of the keys in moduleConfigMaps, and ctx.isMt() returns true.
     */
    @Test
    public void testGetModuleConfigsFromCategoriesKey_CategoriesKeyNotFound_IsMtTrue() throws Throwable {
        // arrange
        Map<String, List<ModuleConfigDo>> moduleConfigMaps = new HashMap<>();
        List<ModuleConfigDo> expectedConfigs = Arrays.asList(new ModuleConfigDo());
        moduleConfigMaps.put("mt", expectedConfigs);
        String categoriesKey = "key1";
        when(ctx.isMt()).thenReturn(true);
        // act
        List<ModuleConfigDo> result = invokePrivateMethod(processor, "getModuleConfigsFromCategoriesKey", ctx, moduleConfigMaps, categoriesKey);
        // assert
        assertEquals(expectedConfigs, result);
    }

    /**
     * Scenario 3: The categoriesKey is not found in any of the keys in moduleConfigMaps, and ctx.isMt() returns false.
     */
    @Test
    public void testGetModuleConfigsFromCategoriesKey_CategoriesKeyNotFound_IsMtFalse() throws Throwable {
        // arrange
        Map<String, List<ModuleConfigDo>> moduleConfigMaps = new HashMap<>();
        List<ModuleConfigDo> expectedConfigs = Arrays.asList(new ModuleConfigDo());
        moduleConfigMaps.put("dp", expectedConfigs);
        String categoriesKey = "key1";
        when(ctx.isMt()).thenReturn(false);
        // act
        List<ModuleConfigDo> result = invokePrivateMethod(processor, "getModuleConfigsFromCategoriesKey", ctx, moduleConfigMaps, categoriesKey);
        // assert
        assertEquals(expectedConfigs, result);
    }

    /**
     * Scenario 4: The moduleConfigMaps contains an empty key, which should be skipped.
     */
    @Test
    public void testGetModuleConfigsFromCategoriesKey_EmptyKeySkipped() throws Throwable {
        // arrange
        Map<String, List<ModuleConfigDo>> moduleConfigMaps = new HashMap<>();
        List<ModuleConfigDo> expectedConfigs = Arrays.asList(new ModuleConfigDo());
        moduleConfigMaps.put("", expectedConfigs);
        moduleConfigMaps.put("key1,key2", expectedConfigs);
        String categoriesKey = "key1";
        // act
        List<ModuleConfigDo> result = invokePrivateMethod(processor, "getModuleConfigsFromCategoriesKey", ctx, moduleConfigMaps, categoriesKey);
        // assert
        assertEquals(expectedConfigs, result);
    }

    /**
     * Scenario 5: The moduleConfigMaps is empty, and ctx.isMt() returns true.
     */
    @Test
    public void testGetModuleConfigsFromCategoriesKey_EmptyModuleConfigMaps_IsMtTrue() throws Throwable {
        // arrange
        Map<String, List<ModuleConfigDo>> moduleConfigMaps = new HashMap<>();
        String categoriesKey = "key1";
        when(ctx.isMt()).thenReturn(true);
        // act
        List<ModuleConfigDo> result = invokePrivateMethod(processor, "getModuleConfigsFromCategoriesKey", ctx, moduleConfigMaps, categoriesKey);
        // assert
        assertNull(result);
    }

    /**
     * Scenario 6: The moduleConfigMaps is empty, and ctx.isMt() returns false.
     */
    @Test
    public void testGetModuleConfigsFromCategoriesKey_EmptyModuleConfigMaps_IsMtFalse() throws Throwable {
        // arrange
        Map<String, List<ModuleConfigDo>> moduleConfigMaps = new HashMap<>();
        String categoriesKey = "key1";
        when(ctx.isMt()).thenReturn(false);
        // act
        List<ModuleConfigDo> result = invokePrivateMethod(processor, "getModuleConfigsFromCategoriesKey", ctx, moduleConfigMaps, categoriesKey);
        // assert
        assertNull(result);
    }

    /**
     * 测试场景：ctx 不为 null，且 requestSource 为 CREATE_ORDER_PREVIEW，返回 true
     */
    @Test
    public void testIsFromCreateOrderPreview_WhenRequestSourceIsCreateOrderPreview_ReturnsTrue() throws Throwable {
        // arrange
        when(ctx.getRequestSource()).thenReturn(RequestSourceEnum.CREATE_ORDER_PREVIEW.getSource());
        // act
        boolean result = processor.isFromCreateOrderPreview(ctx);
        // assert
        assertTrue(result);
    }

    /**
     * 测试场景：ctx 不为 null，但 requestSource 不为 CREATE_ORDER_PREVIEW，返回 false
     */
    @Test
    public void testIsFromCreateOrderPreview_WhenRequestSourceIsNotCreateOrderPreview_ReturnsFalse() throws Throwable {
        // arrange
        when(ctx.getRequestSource()).thenReturn(RequestSourceEnum.HOME_PAGE.getSource());
        // act
        boolean result = processor.isFromCreateOrderPreview(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * 测试场景：ctx 为 null，返回 false
     */
    @Test
    public void testIsFromCreateOrderPreview_WhenCtxIsNull_ReturnsFalse() throws Throwable {
        // arrange
        DealCtx nullCtx = null;
        // act
        boolean result = processor.isFromCreateOrderPreview(nullCtx);
        // assert
        assertFalse(result);
    }

    /**
     * 测试场景：ctx 不为 null，但 requestSource 为 null，返回 false
     */
    @Test
    public void testIsFromCreateOrderPreview_WhenRequestSourceIsNull_ReturnsFalse() throws Throwable {
        // arrange
        when(ctx.getRequestSource()).thenReturn(null);
        // act
        boolean result = processor.isFromCreateOrderPreview(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for when requestSource is equal to CAI_XI.getSource().
     */
    @Test
    public void testFromCAIXI_WhenRequestSourceIsCaiXi() throws Throwable {
        // arrange
        String requestSource = RequestSourceEnum.CAI_XI.getSource();
        // act
        Boolean result = invokeFromCAIXI(requestSource);
        // assert
        assertTrue(result);
    }

    /**
     * Test case for when requestSource is equal to HOME_PAGE.getSource().
     */
    @Test
    public void testFromCAIXI_WhenRequestSourceIsHomePage() throws Throwable {
        // arrange
        String requestSource = RequestSourceEnum.HOME_PAGE.getSource();
        // act
        Boolean result = invokeFromCAIXI(requestSource);
        // assert
        assertTrue(result);
    }

    /**
     * Test case for when requestSource is null.
     */
    @Test
    public void testFromCAIXI_WhenRequestSourceIsNull() throws Throwable {
        // arrange
        String requestSource = null;
        // act
        Boolean result = invokeFromCAIXI(requestSource);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for when requestSource is an empty string.
     */
    @Test
    public void testFromCAIXI_WhenRequestSourceIsEmpty() throws Throwable {
        // arrange
        String requestSource = "";
        // act
        Boolean result = invokeFromCAIXI(requestSource);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for when requestSource is a different case than CAI_XI.getSource().
     */
    @Test
    public void testFromCAIXI_WhenRequestSourceIsDifferentCase() throws Throwable {
        // arrange
        // Different case
        String requestSource = "CaIxI";
        // act
        Boolean result = invokeFromCAIXI(requestSource);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for when requestSource is a value that does not match CAI_XI or HOME_PAGE.
     */
    @Test
    public void testFromCAIXI_WhenRequestSourceIsOtherValue() throws Throwable {
        // arrange
        String requestSource = "other_value";
        // act
        Boolean result = invokeFromCAIXI(requestSource);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for normal scenario where both keys are present.
     */
    @Test
    public void testEduFreeDealModuleRename_NormalCase() throws Throwable {
        // arrange
        ModuleExtraDTO moduleExtraDTO = new ModuleExtraDTO();
        ModuleConfigDo config1 = new ModuleConfigDo();
        config1.setKey("团购详情");
        ModuleConfigDo config2 = new ModuleConfigDo();
        config2.setKey("购买须知");
        moduleExtraDTO.setModuleConfigDos(Arrays.asList(config1, config2));
        // act
        ModuleExtraDTO result = processor.eduFreeDealModuleRename(moduleExtraDTO);
        // assert
        assertEquals("课程详情", result.getModuleConfigDos().get(0).getKey());
        assertEquals("报名须知", result.getModuleConfigDos().get(1).getKey());
    }

    /**
     * Test case for partial match where only one key is present.
     */
    @Test
    public void testEduFreeDealModuleRename_PartialMatch() throws Throwable {
        // arrange
        ModuleExtraDTO moduleExtraDTO = new ModuleExtraDTO();
        ModuleConfigDo config1 = new ModuleConfigDo();
        config1.setKey("团购详情");
        moduleExtraDTO.setModuleConfigDos(Collections.singletonList(config1));
        // act
        ModuleExtraDTO result = processor.eduFreeDealModuleRename(moduleExtraDTO);
        // assert
        assertEquals("课程详情", result.getModuleConfigDos().get(0).getKey());
    }

    /**
     * Test case for no match where none of the keys are present.
     */
    @Test
    public void testEduFreeDealModuleRename_NoMatch() throws Throwable {
        // arrange
        ModuleExtraDTO moduleExtraDTO = new ModuleExtraDTO();
        ModuleConfigDo config1 = new ModuleConfigDo();
        config1.setKey("其他键");
        moduleExtraDTO.setModuleConfigDos(Collections.singletonList(config1));
        // act
        ModuleExtraDTO result = processor.eduFreeDealModuleRename(moduleExtraDTO);
        // assert
        assertEquals("其他键", result.getModuleConfigDos().get(0).getKey());
    }

    /**
     * Test case for empty list scenario.
     */
    @Test
    public void testEduFreeDealModuleRename_EmptyList() throws Throwable {
        // arrange
        ModuleExtraDTO moduleExtraDTO = new ModuleExtraDTO();
        moduleExtraDTO.setModuleConfigDos(Collections.emptyList());
        // act
        ModuleExtraDTO result = processor.eduFreeDealModuleRename(moduleExtraDTO);
        // assert
        assertEquals(0, result.getModuleConfigDos().size());
    }

    /**
     * Test case for null input scenario.
     */
    @Test(expected = NullPointerException.class)
    public void testEduFreeDealModuleRename_NullInput() throws Throwable {
        // arrange
        ModuleExtraDTO moduleExtraDTO = null;
        // act
        processor.eduFreeDealModuleRename(moduleExtraDTO);
        // assert
        // Expecting NullPointerException
    }
}
