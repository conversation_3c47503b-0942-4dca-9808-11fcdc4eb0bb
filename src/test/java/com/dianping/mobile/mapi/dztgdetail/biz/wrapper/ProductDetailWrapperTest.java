package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.pigeon.remoting.common.service.GenericService;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Collections;
import java.util.concurrent.Future;

import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ FutureFactory.class })
public class ProductDetailWrapperTest {

    @Mock
    private GenericService service;

    private ProductDetailWrapper productDetailWrapper;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        productDetailWrapper = new ProductDetailWrapper();
        // Use reflection to set the private service field
        java.lang.reflect.Field serviceField = ProductDetailWrapper.class.getDeclaredField("service");
        serviceField.setAccessible(true);
        serviceField.set(productDetailWrapper, service);
        // Mock static FutureFactory
        PowerMockito.mockStatic(FutureFactory.class);
    }

    /**
     * 测试 service 为 null 的情况
     */
    @Test
    public void testPreQueryProductDetailTradeModule_ServiceIsNull() throws Throwable {
        // arrange
        java.lang.reflect.Field serviceField = ProductDetailWrapper.class.getDeclaredField("service");
        serviceField.setAccessible(true);
        serviceField.set(productDetailWrapper, null);
        // act
        Future<?> result = productDetailWrapper.preQueryProductDetailTradeModule(new ProductDetailPageRequest());
        // assert
        assertNull(result);
    }

    /**
     * 测试 service 不为 null，且 service.$invoke 方法调用抛出异常的情况
     */
    @Test
    public void testPreQueryProductDetailTradeModule_ServiceIsNotNullAndInvokeThrowException() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        when(service.$invoke(eq("query"), eq(Collections.singletonList(ProductDetailPageRequest.class.getName())), anyList())).thenThrow(new RuntimeException("Mock exception"));
        // act
        Future<?> result = productDetailWrapper.preQueryProductDetailTradeModule(request);
        // assert
        assertNull(result);
        verify(service).$invoke(eq("query"), eq(Collections.singletonList(ProductDetailPageRequest.class.getName())), anyList());
    }
}
