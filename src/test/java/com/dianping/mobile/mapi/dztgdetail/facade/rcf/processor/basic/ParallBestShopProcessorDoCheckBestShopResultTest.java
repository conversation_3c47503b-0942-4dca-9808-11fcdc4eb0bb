package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.basic;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;
import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperCacheWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ParallBestShopProcessorDoCheckBestShopResultTest {

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private MapperCacheWrapper mapperCacheWrapper;

    private DealCtx ctx;

    private Method doCheckBestShopResultMethod;

    @InjectMocks
    private ParallBestShopProcessor parallBestShopProcessor;

    @Before
    public void setUp() throws Exception {
        ctx = new DealCtx(null);
        // Get access to the private method using reflection
        doCheckBestShopResultMethod = ParallBestShopProcessor.class.getDeclaredMethod("doCheckBestShopResult", DealCtx.class, List.class);
        doCheckBestShopResultMethod.setAccessible(true);
    }

    /**
     * Test case: Both dpLongShopId and mtLongShopId are <= 0, and relatedShops is empty
     * Expected: No changes to shop IDs
     */
    @Test
    public void testDoCheckBestShopResult_BothShopIdsZero_RelatedShopsEmpty() throws Throwable {
        // arrange
        ctx.setDpLongShopId(0);
        ctx.setMtLongShopId(0);
        List<Long> relatedShops = Collections.emptyList();
        // act
        doCheckBestShopResultMethod.invoke(parallBestShopProcessor, ctx, relatedShops);
        // assert
        assertEquals(0, ctx.getDpLongShopId());
        assertEquals(0, ctx.getMtLongShopId());
    }

    /**
     * Test case: Both dpLongShopId and mtLongShopId are <= 0, relatedShops has elements,
     * and mtShopId mapping is successful
     * Expected: Sets dpLongShopId from relatedShops and fetches corresponding mtLongShopId
     */
    @Test
    public void testDoCheckBestShopResult_BothShopIdsZero_RelatedShopsNotEmpty_MtShopIdFound() throws Throwable {
        // arrange
        ctx.setDpLongShopId(0);
        ctx.setMtLongShopId(0);
        List<Long> relatedShops = Arrays.asList(123L, 456L);
        when(mapperCacheWrapper.fetchMtShopId(123L)).thenReturn(789L);
        // act
        doCheckBestShopResultMethod.invoke(parallBestShopProcessor, ctx, relatedShops);
        // assert
        assertEquals(123L, ctx.getDpLongShopId());
        assertEquals(789L, ctx.getMtLongShopId());
    }

    /**
     * Test case: Both dpLongShopId and mtLongShopId are <= 0, relatedShops has elements,
     * but mtShopId mapping returns 0
     * Expected: Sets dpLongShopId from relatedShops but mtLongShopId remains 0
     */
    @Test
    public void testDoCheckBestShopResult_BothShopIdsZero_RelatedShopsNotEmpty_MtShopIdNotFound() throws Throwable {
        // arrange
        ctx.setDpLongShopId(0);
        ctx.setMtLongShopId(0);
        List<Long> relatedShops = Arrays.asList(123L, 456L);
        when(mapperCacheWrapper.fetchMtShopId(123L)).thenReturn(0L);
        // act
        doCheckBestShopResultMethod.invoke(parallBestShopProcessor, ctx, relatedShops);
        // assert
        assertEquals(123L, ctx.getDpLongShopId());
        assertEquals(0L, ctx.getMtLongShopId());
    }

    /**
     * Test case: Both dpLongShopId and mtLongShopId are negative, relatedShops has elements
     * Expected: Sets dpLongShopId from relatedShops and fetches corresponding mtLongShopId
     */
    @Test
    public void testDoCheckBestShopResult_BothShopIdsNegative_RelatedShopsNotEmpty() throws Throwable {
        // arrange
        ctx.setDpLongShopId(-1);
        ctx.setMtLongShopId(-2);
        List<Long> relatedShops = Arrays.asList(123L, 456L);
        when(mapperCacheWrapper.fetchMtShopId(123L)).thenReturn(789L);
        // act
        doCheckBestShopResultMethod.invoke(parallBestShopProcessor, ctx, relatedShops);
        // assert
        assertEquals(123L, ctx.getDpLongShopId());
        assertEquals(789L, ctx.getMtLongShopId());
    }

    /**
     * Test case: dpLongShopId > 0 but mtLongShopId <= 0, and mtShopId mapping is successful
     * Expected: Fetches corresponding mtLongShopId
     */
    @Test
    public void testDoCheckBestShopResult_DpShopIdPositive_MtShopIdZero_MappingSuccessful() throws Throwable {
        // arrange
        ctx.setDpLongShopId(123L);
        ctx.setMtLongShopId(0);
        List<Long> relatedShops = new ArrayList<>();
        when(mapperCacheWrapper.fetchMtShopId(123L)).thenReturn(789L);
        // act
        doCheckBestShopResultMethod.invoke(parallBestShopProcessor, ctx, relatedShops);
        // assert
        assertEquals(123L, ctx.getDpLongShopId());
        assertEquals(789L, ctx.getMtLongShopId());
    }

    /**
     * Test case: dpLongShopId > 0 but mtLongShopId <= 0, and mtShopId mapping returns 0
     * Expected: mtLongShopId remains 0
     */
    @Test
    public void testDoCheckBestShopResult_DpShopIdPositive_MtShopIdZero_MappingFailed() throws Throwable {
        // arrange
        ctx.setDpLongShopId(123L);
        ctx.setMtLongShopId(0);
        List<Long> relatedShops = new ArrayList<>();
        when(mapperCacheWrapper.fetchMtShopId(123L)).thenReturn(0L);
        // act
        doCheckBestShopResultMethod.invoke(parallBestShopProcessor, ctx, relatedShops);
        // assert
        assertEquals(123L, ctx.getDpLongShopId());
        assertEquals(0L, ctx.getMtLongShopId());
    }

    /**
     * Test case: dpLongShopId > 0 but mtLongShopId < 0, and mtShopId mapping is successful
     * Expected: Fetches corresponding mtLongShopId
     */
    @Test
    public void testDoCheckBestShopResult_DpShopIdPositive_MtShopIdNegative_MappingSuccessful() throws Throwable {
        // arrange
        ctx.setDpLongShopId(123L);
        ctx.setMtLongShopId(-5);
        List<Long> relatedShops = new ArrayList<>();
        when(mapperCacheWrapper.fetchMtShopId(123L)).thenReturn(789L);
        // act
        doCheckBestShopResultMethod.invoke(parallBestShopProcessor, ctx, relatedShops);
        // assert
        assertEquals(123L, ctx.getDpLongShopId());
        assertEquals(789L, ctx.getMtLongShopId());
    }

    /**
     * Test case: dpLongShopId <= 0 but mtLongShopId > 0, and dpShopId mapping is successful
     * Expected: Fetches corresponding dpLongShopId
     */
    @Test
    public void testDoCheckBestShopResult_DpShopIdZero_MtShopIdPositive_MappingSuccessful() throws Throwable {
        // arrange
        ctx.setDpLongShopId(0);
        ctx.setMtLongShopId(789L);
        List<Long> relatedShops = new ArrayList<>();
        when(mapperCacheWrapper.fetchDpShopId(789L)).thenReturn(123L);
        // act
        doCheckBestShopResultMethod.invoke(parallBestShopProcessor, ctx, relatedShops);
        // assert
        assertEquals(123L, ctx.getDpLongShopId());
        assertEquals(789L, ctx.getMtLongShopId());
    }

    /**
     * Test case: dpLongShopId <= 0 but mtLongShopId > 0, and dpShopId mapping returns 0
     * Expected: dpLongShopId remains 0
     */
    @Test
    public void testDoCheckBestShopResult_DpShopIdZero_MtShopIdPositive_MappingFailed() throws Throwable {
        // arrange
        ctx.setDpLongShopId(0);
        ctx.setMtLongShopId(789L);
        List<Long> relatedShops = new ArrayList<>();
        when(mapperCacheWrapper.fetchDpShopId(789L)).thenReturn(0L);
        // act
        doCheckBestShopResultMethod.invoke(parallBestShopProcessor, ctx, relatedShops);
        // assert
        assertEquals(0L, ctx.getDpLongShopId());
        assertEquals(789L, ctx.getMtLongShopId());
    }

    /**
     * Test case: dpLongShopId < 0 but mtLongShopId > 0, and dpShopId mapping is successful
     * Expected: Fetches corresponding dpLongShopId
     */
    @Test
    public void testDoCheckBestShopResult_DpShopIdNegative_MtShopIdPositive_MappingSuccessful() throws Throwable {
        // arrange
        ctx.setDpLongShopId(-3);
        ctx.setMtLongShopId(789L);
        List<Long> relatedShops = new ArrayList<>();
        when(mapperCacheWrapper.fetchDpShopId(789L)).thenReturn(123L);
        // act
        doCheckBestShopResultMethod.invoke(parallBestShopProcessor, ctx, relatedShops);
        // assert
        assertEquals(123L, ctx.getDpLongShopId());
        assertEquals(789L, ctx.getMtLongShopId());
    }

    /**
     * Test case: Both dpLongShopId and mtLongShopId are > 0
     * Expected: No changes to shop IDs
     */
    @Test
    public void testDoCheckBestShopResult_BothShopIdsPositive() throws Throwable {
        // arrange
        ctx.setDpLongShopId(123L);
        ctx.setMtLongShopId(789L);
        List<Long> relatedShops = new ArrayList<>();
        // act
        doCheckBestShopResultMethod.invoke(parallBestShopProcessor, ctx, relatedShops);
        // assert
        assertEquals(123L, ctx.getDpLongShopId());
        assertEquals(789L, ctx.getMtLongShopId());
    }

    /**
     * Test case: dpLongShopId <= 0 but mtLongShopId > 0, and dpShopId mapping throws exception
     * Expected: Exception is thrown
     */
    @Test
    public void testDoCheckBestShopResult_DpShopIdZero_MtShopIdPositive_MappingException() throws Throwable {
        // arrange
        ctx.setDpLongShopId(0);
        ctx.setMtLongShopId(789L);
        List<Long> relatedShops = new ArrayList<>();
        when(mapperCacheWrapper.fetchDpShopId(789L)).thenThrow(new RuntimeException("Mapping failed"));
        try {
            // act
            doCheckBestShopResultMethod.invoke(parallBestShopProcessor, ctx, relatedShops);
            fail("Expected InvocationTargetException to be thrown");
        } catch (InvocationTargetException e) {
            // assert
            assertTrue(e.getCause() instanceof RuntimeException);
            assertEquals("Mapping failed", e.getCause().getMessage());
            assertEquals(0L, ctx.getDpLongShopId());
            assertEquals(789L, ctx.getMtLongShopId());
        } catch (Exception e) {
            fail("Unexpected exception: " + e);
        }
    }

    /**
     * Test case: Both dpLongShopId and mtLongShopId are <= 0, relatedShops is null
     * Expected: No changes to shop IDs
     */
    @Test
    public void testDoCheckBestShopResult_BothShopIdsZero_RelatedShopsNull() throws Throwable {
        // arrange
        ctx.setDpLongShopId(0);
        ctx.setMtLongShopId(0);
        List<Long> relatedShops = null;
        // act
        doCheckBestShopResultMethod.invoke(parallBestShopProcessor, ctx, relatedShops);
        // assert
        assertEquals(0, ctx.getDpLongShopId());
        assertEquals(0, ctx.getMtLongShopId());
    }

    /**
     * Test case: dpLongShopId > 0 but mtLongShopId <= 0, and mtShopId mapping throws exception
     * Expected: Exception is thrown
     */
    @Test
    public void testDoCheckBestShopResult_DpShopIdPositive_MtShopIdZero_MappingException() throws Throwable {
        // arrange
        ctx.setDpLongShopId(123L);
        ctx.setMtLongShopId(0);
        List<Long> relatedShops = new ArrayList<>();
        when(mapperCacheWrapper.fetchMtShopId(123L)).thenThrow(new RuntimeException("Mapping failed"));
        try {
            // act
            doCheckBestShopResultMethod.invoke(parallBestShopProcessor, ctx, relatedShops);
            fail("Expected InvocationTargetException to be thrown");
        } catch (InvocationTargetException e) {
            // assert
            assertTrue(e.getCause() instanceof RuntimeException);
            assertEquals("Mapping failed", e.getCause().getMessage());
            assertEquals(123L, ctx.getDpLongShopId());
            assertEquals(0L, ctx.getMtLongShopId());
        } catch (Exception e) {
            fail("Unexpected exception: " + e);
        }
    }
}
