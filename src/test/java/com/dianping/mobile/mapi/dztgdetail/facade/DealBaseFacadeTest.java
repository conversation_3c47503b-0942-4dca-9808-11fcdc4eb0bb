package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.CityServiceWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.GroupDealWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiClientWrapper;
import com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam;
import com.meituan.service.mobile.group.geo.bean.CityInfo;
import com.meituan.service.mobile.message.group.deal.PoiidListRequestMsg;
import com.meituan.service.mobile.prometheus.model.DealModel;
import com.meituan.service.mobile.sinai.client.model.PoiModelL;
import java.util.*;
import org.joda.time.DateTime;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealBaseFacadeTest {

    @InjectMocks
    private DealBaseFacade dealBaseFacade;

    @Mock
    private GroupDealWrapper groupDealWrapper;

    private PoiidListRequestMsg req;

    @Mock
    private PoiClientWrapper poiClientWrapper;

    private List<Integer> dids = Arrays.asList(1, 2, 3);

    private Map<Integer, List<Long>> did2PoiIdMap = new HashMap<>();

    private List<Long> allPoiIds = Arrays.asList(1L, 2L, 3L);

    private Map<Long, PoiModelL> allPoiModel = new HashMap<>();

    @Mock
    private DealModel deal;

    @Mock
    private MtCommonParam commonParam;

    @Mock
    private CityServiceWrapper cityServiceWrapper;

    @Before
    public void setUp() {
        req = new PoiidListRequestMsg();
    }

    private void setUpCommonMocks() {
        for (Long poiId : allPoiIds) {
            PoiModelL poiModel = new PoiModelL();
            poiModel.setId(poiId);
            allPoiModel.put(poiId, poiModel);
        }
    }

    private void setupCommonMocks() {
        when(deal.getNobooking()).thenReturn(1);
        when(commonParam.getVersionName()).thenReturn("5.1");
        when(commonParam.getUtmMedium()).thenReturn("android");
        CityInfo mockCityInfo = new CityInfo();
        when(cityServiceWrapper.getCityById(anyInt())).thenReturn(mockCityInfo);
    }

    /**
     * 测试 todayAvailable 方法，当 str 为空的情况
     */
    @Test
    public void testTodayAvailableWhenStrIsEmpty() throws Throwable {
        // arrange
        String str = "";
        // act
        boolean result = DealBaseFacade.todayAvailable(str);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 todayAvailable 方法，当 str 不为空，但无法转换为 JSONObject 的情况
     */
    @Test
    public void testTodayAvailableWhenStrIsNotEmptyButCannotConvertToJSONObject() throws Throwable {
        // arrange
        String str = "not a json string";
        // act
        boolean result = DealBaseFacade.todayAvailable(str);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 todayAvailable 方法，当 str 可以转换为 JSONObject，但 useDay 字段的值为 0，且 weekday 字段包含今天的情况
     */
    @Test
    public void testTodayAvailableWhenStrCanConvertToJSONObjectAndUseDayIsZeroAndWeekdayContainsToday() throws Throwable {
        // arrange
        // Dynamically determine the current day of the week
        String today = String.valueOf(DateTime.now().dayOfWeek().get());
        String str = "{\"useDay\":0,\"weekday\":\"" + today + "\"}";
        // act
        boolean result = DealBaseFacade.todayAvailable(str);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 todayAvailable 方法，当 str 可以转换为 JSONObject，但 useDay 字段的值为 0，且 startDate 和 endDate 字段包含今天的情况
     */
    @Test
    public void testTodayAvailableWhenStrCanConvertToJSONObjectAndUseDayIsZeroAndStartDateAndEndDateContainToday() throws Throwable {
        // arrange
        String today = DateTime.now().toString("yyyy-MM-dd");
        String str = "{\"useDay\":0,\"startDate\":\"" + today + "\",\"endDate\":\"" + today + "\"}";
        // act
        boolean result = DealBaseFacade.todayAvailable(str);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 todayAvailable 方法，当 str 可以转换为 JSONObject，但 useDay 字段的值不为 0 的情况
     */
    @Test
    public void testTodayAvailableWhenStrCanConvertToJSONObjectAndUseDayIsNotZero() throws Throwable {
        // arrange
        String str = "{\"useDay\":1}";
        // act
        boolean result = DealBaseFacade.todayAvailable(str);
        // assert
        assertTrue(result);
    }

    /**
     * 测试场景：req 中的 deal ID 列表为空
     */
    @Test
    public void testListPoiIdByDIdsEmptyList() {
        req.setDids(Collections.emptyList());
        Map<Integer, List<Long>> result = dealBaseFacade.listPoiIdByDIds(req);
        assertEquals(Collections.emptyMap(), result);
    }

    /**
     * 测试场景：req 中的 deal ID 列表的大小小于或等于 20
     */
    @Test
    public void testListPoiIdByDIdsLessThanThreshold() {
        req.setDids(Arrays.asList(1, 2, 3));
        Map<Integer, List<Long>> expected = Collections.singletonMap(1, Collections.singletonList(1L));
        when(groupDealWrapper.listPoiIdByDIds(req)).thenReturn(expected);
        Map<Integer, List<Long>> result = dealBaseFacade.listPoiIdByDIds(req);
        assertEquals(expected, result);
    }

    /**
     * 测试场景：req 中的 deal ID 列表的大小大于 20
     */
    @Test
    public void testListPoiIdByDIdsMoreThanThreshold() {
        req.setDids(Arrays.asList(new Integer[21]));
        Map<Integer, List<Long>> expected = Collections.singletonMap(1, Collections.singletonList(1L));
        when(groupDealWrapper.listPoiIdByDIds(req)).thenReturn(expected);
        Map<Integer, List<Long>> result = dealBaseFacade.listPoiIdByDIds(req);
        assertEquals(expected, result);
    }

    /**
     * 测试场景：groupDealWrapper.listPoiIdByDIds(req) 方法返回的结果为空
     */
    @Test
    public void testListPoiIdByDIdsNullResult() {
        req.setDids(Arrays.asList(1, 2, 3));
        when(groupDealWrapper.listPoiIdByDIds(req)).thenReturn(null);
        Map<Integer, List<Long>> result = dealBaseFacade.listPoiIdByDIds(req);
        assertEquals(Collections.emptyMap(), result);
    }

    /**
     * 测试场景：groupDealWrapper.listPoiIdByDIds(req) 方法返回的结果不为空
     */
    @Test
    public void testListPoiIdByDIdsNotNullResult() {
        req.setDids(Arrays.asList(1, 2, 3));
        Map<Integer, List<Long>> expected = Collections.singletonMap(1, Collections.singletonList(1L));
        when(groupDealWrapper.listPoiIdByDIds(req)).thenReturn(expected);
        Map<Integer, List<Long>> result = dealBaseFacade.listPoiIdByDIds(req);
        assertEquals(expected, result);
    }

    @Test
    public void testGetPoiModelByDidsEmptyDids() throws Throwable {
        Map<Integer, List<PoiModelL>> result = dealBaseFacade.getPoiModelByDids(Collections.emptyList(), 1);
        assertNull(result);
    }

    @Test
    public void testGetPoiModelByDidsNullDid2PoiIdMap() throws Throwable {
        when(groupDealWrapper.listPoiIdByDIds(any(PoiidListRequestMsg.class))).thenReturn(null);
        Map<Integer, List<PoiModelL>> result = dealBaseFacade.getPoiModelByDids(dids, 1);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetPoiModelByDidsNullAllPoiModel() throws Throwable {
        setUpCommonMocks();
        when(groupDealWrapper.listPoiIdByDIds(any(PoiidListRequestMsg.class))).thenReturn(did2PoiIdMap);
        Map<Integer, List<PoiModelL>> result = dealBaseFacade.getPoiModelByDids(dids, 1);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetPoiModelByDidsPoiIdNotInAllPoiModel() throws Throwable {
        when(groupDealWrapper.listPoiIdByDIds(any(PoiidListRequestMsg.class))).thenReturn(did2PoiIdMap);
        Map<Integer, List<PoiModelL>> result = dealBaseFacade.getPoiModelByDids(dids, 1);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testChangeDealDealIsNull() throws Throwable {
        setupCommonMocks();
        dealBaseFacade.changeDeal(null, 1, Arrays.asList("field1", "field2"), commonParam);
        verify(deal, never()).setTitle(anyString());
    }

    @Test
    public void testChangeDealRowFieldsIsEmpty() throws Throwable {
        setupCommonMocks();
        dealBaseFacade.changeDeal(deal, 1, Arrays.asList(), commonParam);
        verify(deal, never()).setTitle(anyString());
    }

    @Test
    public void testChangeDealNobookingIs1AndVersionIsNotSatisfy() throws Throwable {
        setupCommonMocks();
        when(commonParam.getVersionName()).thenReturn("5.0");
        dealBaseFacade.changeDeal(deal, 1, Arrays.asList("field1", "field2"), commonParam);
        verify(deal, never()).setTitle(anyString());
    }

    @Test
    public void testChangeDealNobookingIsNot1() throws Throwable {
        setupCommonMocks();
        when(deal.getNobooking()).thenReturn(0);
        dealBaseFacade.changeDeal(deal, 1, Arrays.asList("field1", "field2"), commonParam);
        verify(deal, never()).setTitle(anyString());
    }
}
