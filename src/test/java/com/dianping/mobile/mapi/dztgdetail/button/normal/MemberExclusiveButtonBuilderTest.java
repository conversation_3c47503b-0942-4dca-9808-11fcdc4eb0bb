package com.dianping.mobile.mapi.dztgdetail.button.normal;

import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.degrade.util.JsonCodec;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.PriceContext;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.resp.GetGrouponMemberInfoRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Map;

import static org.junit.Assert.assertNotNull;
import static org.powermock.api.mockito.PowerMockito.doNothing;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/9/11 20:08
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({GreyUtils.class, DealBuyHelper.class, Lion.class})
public class MemberExclusiveButtonBuilderTest {

    @InjectMocks
    private MemberExclusiveButtonBuilder memberExclusiveButtonBuilder;


    ButtonBuilderChain chain = Mockito.mock(ButtonBuilderChain.class);

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(GreyUtils.class);
        PowerMockito.mockStatic(DealBuyHelper.class);
        PowerMockito.mockStatic(Lion.class);
    }

    @Test
    public void testDoBuild() {
        DealCtx context = buildDealCtx();
        when(GreyUtils.isMemberExclusive(context)).thenAnswer((Answer<?>) invocation -> true);
        when(DealBuyHelper.getCanNotBuyButton(context)).thenAnswer((Answer<?>) invocation -> null);
        when(DealBuyHelper.isShoppingCart(context)).thenAnswer((Answer<?>) invocation -> false);
        Map<String,String> promotionchannelMap = JsonCodec.decode(promotionchannelJson, Map.class);
        Map<String,String> pageSource2OrderTrafficFlagMap = JsonCodec.decode(pageSource2OrderTrafficFlagJson, Map.class);
        when(Lion.getMap(LionConstants.APP_KEY, LionConstants.PAGESOURCE_TO_ORDER_PROMOTIONChANNEL, String.class, Collections.emptyMap())).thenAnswer((Answer<Map<String, String>>) invocation -> promotionchannelMap);
        when(Lion.getMap(LionConstants.APP_KEY, LionConstants.PAGESOURCE_TO_ORDER_TRAFFICFLAG, String.class, Collections.emptyMap())).thenAnswer((Answer<Map<String, String>>) invocation -> pageSource2OrderTrafficFlagMap);
        memberExclusiveButtonBuilder.doBuild(context, chain);
        assertNotNull(context.getBuyBar());
    }

    private DealCtx buildDealCtx() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setMpAppId("wxde8ac0a21135c07d");
        envCtx.setClientType(ClientTypeEnum.mt_weApp.getType());
        DealCtx dealCtx = new DealCtx(envCtx);

        dealCtx.setSaleStatus("null");
        dealCtx.setMemberInfoRespDTO(null);

        DealGroupBaseDTO dealGroupBaseDTO = new DealGroupBaseDTO();
        dealGroupBaseDTO.setDealGroupPrice(new BigDecimal("100"));

        dealCtx.setDealGroupBase(dealGroupBaseDTO);
        dealCtx.setMarketPriceHided(true);
        dealCtx.setMtCityId(10);

        dealCtx.setDpId(1);
        dealCtx.setMtId(2);

        dealCtx.setMtLongShopId(12345L);
        dealCtx.setDpLongShopId(12345L);
        dealCtx.setExternal(true);

        dealCtx.setRequestSource("cost_effective");
        PriceContext priceContext = new PriceContext();
        priceContext.setHasExclusiveDeduction(false);
        priceContext.setDealPromoPriceCipher("priceContextCipher");

        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        priceDisplayDTO.setPrice(new BigDecimal("100"));
        priceContext.setNormalPrice(priceDisplayDTO);
        dealCtx.setPriceContext(priceContext);

        GetGrouponMemberInfoRespDTO memberInfoRespDTO = new GetGrouponMemberInfoRespDTO();
        memberInfoRespDTO.setMemberPageUrl("memberPageUrl");
        dealCtx.setMemberInfoRespDTO(memberInfoRespDTO);
        return dealCtx;
    }

    String promotionchannelJson = "{\"caixi\":\"13\",\"cost_effective\":\"2\",\"mlive\":\"3\",\"special_sale_99\":\"6\",\"ten_billion_subsidy\":\"7\",\"enterpriseWeixinCommunity\":\"8\",\"liren_offsite_launch\":\"15\",\"medical_venue_special_sale_99\":\"14\"}";

    String pageSource2OrderTrafficFlagJson = "{\n" + "    \"youhuimaMini\": \"youhuimaMini\",\n"
            + "    \"mlive\": \"liveStreaming\",\n" + "\"ten_billion_subsidy\":\"tenBillionSubsidy\",\n"
            + "\"baiduMap\":\"DP_BAIDU\",\n" + "\"huaweifuyiping\":\"HUAWEI\",\n"
            + "\"cost_effective\":\"costEffective\",\n" + "\"odp\":\"odp\",\n" + "\"newyouhuima\":\"newYouhuimaMini\"\n"
            + "}";
}