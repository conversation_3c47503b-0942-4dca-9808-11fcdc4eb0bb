package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CommonAttrVO;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.OptionalServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * 测试BeautyHighlightsV2Processor的getWearableNailAttrs方法
 */
@RunWith(MockitoJUnitRunner.class)
public class BeautyHighlightsV2ProcessorTest {

    @InjectMocks
    private BeautyHighlightsV2Processor processor;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private DealGroupDTO dealGroupDTO;

    @Mock
    private DealGroupCategoryDTO categoryDTO;

    /**
     * Helper method to invoke private method using reflection
     */
    private String invokePrivateMethod(Object object, String methodName, Object... params) throws Exception {
        Method method = object.getClass().getDeclaredMethod(methodName, DealCtx.class, Map.class);
        method.setAccessible(true);
        return (String) method.invoke(object, params);
    }

    private List<ServiceProjectDTO> invokePrivateGetServiceProject(DealGroupDTO groupDTO) throws Exception {
        Method method = BeautyHighlightsV2Processor.class.getDeclaredMethod("getServiceProject", DealGroupDTO.class);
        method.setAccessible(true);
        return (List<ServiceProjectDTO>) method.invoke(processor, groupDTO);
    }

    private String getHighlightsConfig(DealCtx ctx, Map<String, String> config) {
        DealGroupCategoryDTO categoryDTO = dealCtx.getDealGroupDTO().getCategory();
        if (Objects.isNull(categoryDTO)) {
            return StringUtils.EMPTY;
        }
        String categoryIdKey = String.valueOf(categoryDTO.getCategoryId());
        String serviceTypeKey = Joiner.on(getDot()).join(categoryIdKey, categoryDTO.getServiceType());
        String value = config.get(serviceTypeKey);
        return StringUtils.isEmpty(value) ? config.get(categoryIdKey) : value;
    }

    protected String getHighlightsStyle(DealCtx ctx) {
        // Replace with actual logic if needed
        Map<String, String> highlightStyle = new HashMap<>();
        return getHighlightsConfig(dealCtx, highlightStyle);
    }

    private String getDot() {
        try {
            Field dotField = BeautyHighlightsV2Processor.class.getDeclaredField("DOT");
            dotField.setAccessible(true);
            return (String) dotField.get(null);
        } catch (Exception e) {
            throw new RuntimeException("Failed to access DOT field", e);
        }
    }

    /**
     * Helper method to create DealGroupTagDTO
     */
    private DealGroupTagDTO createDealGroupTagDTO(Long id) {
        DealGroupTagDTO tag = new DealGroupTagDTO();
        tag.setId(id);
        return tag;
    }

    /**
     * Helper method to invoke the private getDealTags method using reflection
     */
    private List<Long> invokePrivateGetDealTags(DealGroupDTO dealGroup) throws Exception {
        Method method = BeautyHighlightsV2Processor.class.getDeclaredMethod("getDealTags", DealGroupDTO.class);
        method.setAccessible(true);
        return (List<Long>) method.invoke(processor, dealGroup);
    }

    private DealGroupDTO mockBasicDealGroupDTO(Long serviceTypeId) {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO category = new DealGroupCategoryDTO();
        category.setServiceTypeId(serviceTypeId);
        dealGroupDTO.setCategory(category);
        return dealGroupDTO;
    }

    private DealGroupDTO mockDealGroupDTOWithServiceType(Long serviceTypeId, String serviceType) {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO category = new DealGroupCategoryDTO();
        category.setServiceTypeId(serviceTypeId);
        category.setServiceType(serviceType);
        dealGroupDTO.setCategory(category);
        return dealGroupDTO;
    }

    private DealGroupDTO mockDealGroupDTOWithAttrs(Long serviceTypeId, String attrName, String attrValue) {
        DealGroupDTO dealGroupDTO = mockBasicDealGroupDTO(serviceTypeId);
        DealGroupServiceProjectDTO serviceProject = new DealGroupServiceProjectDTO();
        List<MustServiceProjectGroupDTO> mustGroups = new ArrayList<>();
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        List<ServiceProjectDTO> groups = new ArrayList<>();
        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        List<ServiceProjectAttrDTO> attrs = new ArrayList<>();
        ServiceProjectAttrDTO attr = new ServiceProjectAttrDTO();
        attr.setAttrName(attrName);
        attr.setAttrValue(attrValue);
        attrs.add(attr);
        serviceProjectDTO.setAttrs(attrs);
        groups.add(serviceProjectDTO);
        mustGroup.setGroups(groups);
        mustGroups.add(mustGroup);
        serviceProject.setMustGroups(mustGroups);
        dealGroupDTO.setServiceProject(serviceProject);
        return dealGroupDTO;
    }

    private DealGroupDTO mockDealGroupDTOWithMultipleAttrs(Long serviceTypeId, String[] attrNames, String[] attrValues) {
        DealGroupDTO dealGroupDTO = mockBasicDealGroupDTO(serviceTypeId);
        DealGroupServiceProjectDTO serviceProject = new DealGroupServiceProjectDTO();
        List<MustServiceProjectGroupDTO> mustGroups = new ArrayList<>();
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        List<ServiceProjectDTO> groups = new ArrayList<>();
        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        List<ServiceProjectAttrDTO> attrs = new ArrayList<>();
        for (int i = 0; i < attrNames.length; i++) {
            ServiceProjectAttrDTO attr = new ServiceProjectAttrDTO();
            attr.setAttrName(attrNames[i]);
            attr.setAttrValue(attrValues[i]);
            attrs.add(attr);
        }
        serviceProjectDTO.setAttrs(attrs);
        groups.add(serviceProjectDTO);
        mustGroup.setGroups(groups);
        mustGroups.add(mustGroup);
        serviceProject.setMustGroups(mustGroups);
        dealGroupDTO.setServiceProject(serviceProject);
        return dealGroupDTO;
    }

    private DealGroupDTO mockCompleteScenarioDealGroupDTO() {
        return mockDealGroupDTOWithMultipleAttrs(137013L, new String[] { "ProjectClassification", "suitCrowds", "grant", "buseshijian", "mianfeibuse", "ranliaochandi" }, new String[] { "纹身", "年轻人、中年人", "赠送", "3", "2", "日本" });
    }

    private DealGroupDTO mockDealGroupDTO(String suitCrowdsValue) {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO category = new DealGroupCategoryDTO();
        category.setServiceTypeId(137013L);
        dealGroupDTO.setCategory(category);
        DealGroupServiceProjectDTO serviceProject = new DealGroupServiceProjectDTO();
        List<MustServiceProjectGroupDTO> mustGroups = new ArrayList<>();
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        List<ServiceProjectDTO> groups = new ArrayList<>();
        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        List<ServiceProjectAttrDTO> attrs = new ArrayList<>();
        ServiceProjectAttrDTO suitCrowdsAttr = new ServiceProjectAttrDTO();
        suitCrowdsAttr.setAttrName("suitCrowds");
        suitCrowdsAttr.setChnName("适合人群");
        suitCrowdsAttr.setAttrValue(suitCrowdsValue);
        attrs.add(suitCrowdsAttr);
        serviceProjectDTO.setAttrs(attrs);
        groups.add(serviceProjectDTO);
        mustGroup.setGroups(groups);
        mustGroups.add(mustGroup);
        serviceProject.setMustGroups(mustGroups);
        dealGroupDTO.setServiceProject(serviceProject);
        return dealGroupDTO;
    }

    private String invokePrivateMethod(Object object, String methodName, DealGroupDTO dealGroup) throws Exception {
        Method method = BeautyHighlightsV2Processor.class.getDeclaredMethod(methodName, DealGroupDTO.class);
        method.setAccessible(true);
        return (String) method.invoke(object, dealGroup);
    }

    /**
     * 测试当dealGroupDTO中的isFreeWearingAtStore属性为true时
     */
    @Test
    public void testGetWearableNailAttrs_WhenIsFreeWearingAtStoreIsTrue() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr1 = new AttrDTO();
        attr1.setName("isFreeWearingAtStore");
        attr1.setValue(Lists.newArrayList("true"));
        attrs.add(attr1);
        AttrDTO attr2 = new AttrDTO();
        attr2.setName("wearingNailsType");
        attr2.setValue(Lists.newArrayList("机器穿戴甲"));
        attrs.add(attr2);
        AttrDTO attr3 = new AttrDTO();
        attr3.setName("nail_additional_item");
        attr3.setValue(Lists.newArrayList("[\"赠佩戴工具包\"]"));
        attrs.add(attr3);
        dealGroupDTO.setAttrs(attrs);
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setDealGroupDTO(dealGroupDTO);
        List<CommonAttrVO> result = processor.getWearableNailAttrs(ctx);
        assertNotNull(result);
        assertEquals(3, result.size());
    }

    /**
     * 测试当DealGroupDTO为null时的场景
     */
    @Test
    public void testGetTattooShoppingGuideAttrsWhenDealGroupDTOIsNull() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setCategory(new DealGroupCategoryDTO());
        dealGroupDTO.setMtDealGroupId(11L);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        List<CommonAttrVO> result = processor.getTattooShoppingGuideAttrs(dealCtx);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试当dealGroupDTO中的isFreeWearingAtStore属性为true时
     */
    /**
     * Test when categoryDTO is null
     */
    @Test
    public void testGetHighlightsConfig_WhenCategoryDTOIsNull() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(null);
        Map<String, String> config = new HashMap<>();
        // act
        String result = invokePrivateMethod(processor, "getHighlightsConfig", ctx, config);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when config has matching serviceTypeKey
     */
    @Test
    public void testGetHighlightsConfig_WhenServiceTypeKeyExists() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO categoryDTO = mock(DealGroupCategoryDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(categoryDTO);
        when(categoryDTO.getCategoryId()).thenReturn(501L);
        when(categoryDTO.getServiceType()).thenReturn("美甲");
        Map<String, String> config = new HashMap<>();
        config.put("501.美甲", "serviceTypeValue");
        config.put("501", "categoryValue");
        // act
        String result = invokePrivateMethod(processor, "getHighlightsConfig", ctx, config);
        // assert
        assertEquals("serviceTypeValue", result);
    }

    /**
     * Test when config only has matching categoryIdKey
     */
    @Test
    public void testGetHighlightsConfig_WhenOnlyCategoryIdKeyExists() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO categoryDTO = mock(DealGroupCategoryDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(categoryDTO);
        when(categoryDTO.getCategoryId()).thenReturn(501L);
        when(categoryDTO.getServiceType()).thenReturn("美甲");
        Map<String, String> config = new HashMap<>();
        config.put("501", "categoryValue");
        // act
        String result = invokePrivateMethod(processor, "getHighlightsConfig", ctx, config);
        // assert
        assertEquals("categoryValue", result);
    }

    /**
     * Test when neither serviceTypeKey nor categoryIdKey exists in config
     */
    @Test
    public void testGetHighlightsConfig_WhenNoMatchingKeyExists() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO categoryDTO = mock(DealGroupCategoryDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(categoryDTO);
        when(categoryDTO.getCategoryId()).thenReturn(501L);
        when(categoryDTO.getServiceType()).thenReturn("美甲");
        Map<String, String> config = new HashMap<>();
        // act
        String result = invokePrivateMethod(processor, "getHighlightsConfig", ctx, config);
        // assert
        // Expecting null when no matching key exists
        assertNull(result);
    }

    /**
     * Test when ServiceProject is null
     */
    @Test
    public void testGetServiceProject_WhenServiceProjectIsNull() throws Throwable {
        // arrange
        DealGroupDTO groupDTO = mock(DealGroupDTO.class);
        when(groupDTO.getServiceProject()).thenReturn(null);
        // act
        List<ServiceProjectDTO> result = invokePrivateGetServiceProject(groupDTO);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when both must groups and option groups are null
     */
    @Test
    public void testGetServiceProject_WhenBothGroupsAreNull() throws Throwable {
        // arrange
        DealGroupDTO groupDTO = mock(DealGroupDTO.class);
        DealGroupServiceProjectDTO serviceProject = mock(DealGroupServiceProjectDTO.class);
        when(groupDTO.getServiceProject()).thenReturn(serviceProject);
        when(serviceProject.getMustGroups()).thenReturn(null);
        when(serviceProject.getOptionGroups()).thenReturn(null);
        // act
        List<ServiceProjectDTO> result = invokePrivateGetServiceProject(groupDTO);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when must groups contains valid data but option groups is empty
     */
    @Test
    public void testGetServiceProject_WhenOnlyMustGroupsHasData() throws Throwable {
        // arrange
        DealGroupDTO groupDTO = mock(DealGroupDTO.class);
        DealGroupServiceProjectDTO serviceProject = mock(DealGroupServiceProjectDTO.class);
        MustServiceProjectGroupDTO mustGroup = mock(MustServiceProjectGroupDTO.class);
        ServiceProjectDTO serviceProjectDTO = mock(ServiceProjectDTO.class);
        when(groupDTO.getServiceProject()).thenReturn(serviceProject);
        when(serviceProject.getMustGroups()).thenReturn(Lists.newArrayList(mustGroup));
        when(serviceProject.getOptionGroups()).thenReturn(null);
        when(mustGroup.getGroups()).thenReturn(Lists.newArrayList(serviceProjectDTO));
        // act
        List<ServiceProjectDTO> result = invokePrivateGetServiceProject(groupDTO);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertSame(serviceProjectDTO, result.get(0));
    }

    /**
     * Test when option groups contains valid data but must groups is empty
     */
    @Test
    public void testGetServiceProject_WhenOnlyOptionGroupsHasData() throws Throwable {
        // arrange
        DealGroupDTO groupDTO = mock(DealGroupDTO.class);
        DealGroupServiceProjectDTO serviceProject = mock(DealGroupServiceProjectDTO.class);
        OptionalServiceProjectGroupDTO optionGroup = mock(OptionalServiceProjectGroupDTO.class);
        ServiceProjectDTO serviceProjectDTO = mock(ServiceProjectDTO.class);
        when(groupDTO.getServiceProject()).thenReturn(serviceProject);
        when(serviceProject.getMustGroups()).thenReturn(null);
        when(serviceProject.getOptionGroups()).thenReturn(Lists.newArrayList(optionGroup));
        when(optionGroup.getGroups()).thenReturn(Lists.newArrayList(serviceProjectDTO));
        // act
        List<ServiceProjectDTO> result = invokePrivateGetServiceProject(groupDTO);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertSame(serviceProjectDTO, result.get(0));
    }

    /**
     * Test when both groups contain valid data
     */
    @Test
    public void testGetServiceProject_WhenBothGroupsHaveData() throws Throwable {
        // arrange
        DealGroupDTO groupDTO = mock(DealGroupDTO.class);
        DealGroupServiceProjectDTO serviceProject = mock(DealGroupServiceProjectDTO.class);
        MustServiceProjectGroupDTO mustGroup = mock(MustServiceProjectGroupDTO.class);
        OptionalServiceProjectGroupDTO optionGroup = mock(OptionalServiceProjectGroupDTO.class);
        ServiceProjectDTO mustServiceProjectDTO = mock(ServiceProjectDTO.class);
        ServiceProjectDTO optionServiceProjectDTO = mock(ServiceProjectDTO.class);
        when(groupDTO.getServiceProject()).thenReturn(serviceProject);
        when(serviceProject.getMustGroups()).thenReturn(Lists.newArrayList(mustGroup));
        when(serviceProject.getOptionGroups()).thenReturn(Lists.newArrayList(optionGroup));
        when(mustGroup.getGroups()).thenReturn(Lists.newArrayList(mustServiceProjectDTO));
        when(optionGroup.getGroups()).thenReturn(Lists.newArrayList(optionServiceProjectDTO));
        // act
        List<ServiceProjectDTO> result = invokePrivateGetServiceProject(groupDTO);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(mustServiceProjectDTO));
        assertTrue(result.contains(optionServiceProjectDTO));
    }

    /**
     * Test when groups contain null elements
     */
    @Test
    public void testGetServiceProject_WhenGroupsContainNullElements() throws Throwable {
        // arrange
        DealGroupDTO groupDTO = mock(DealGroupDTO.class);
        DealGroupServiceProjectDTO serviceProject = mock(DealGroupServiceProjectDTO.class);
        MustServiceProjectGroupDTO mustGroup = mock(MustServiceProjectGroupDTO.class);
        OptionalServiceProjectGroupDTO optionGroup = mock(OptionalServiceProjectGroupDTO.class);
        when(groupDTO.getServiceProject()).thenReturn(serviceProject);
        when(serviceProject.getMustGroups()).thenReturn(Lists.newArrayList(null, mustGroup));
        when(serviceProject.getOptionGroups()).thenReturn(Lists.newArrayList(null, optionGroup));
        when(mustGroup.getGroups()).thenReturn(null);
        when(optionGroup.getGroups()).thenReturn(null);
        // act
        List<ServiceProjectDTO> result = invokePrivateGetServiceProject(groupDTO);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case: When category is null
     * Expected: Should return empty string
     */
    @Test
    public void testGetHighlightsStyle_WhenCategoryIsNull() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(null);
        // act
        String result = processor.getHighlightsStyle(dealCtx);
        // assert
        assertEquals(StringUtils.EMPTY, result);
    }

    /**
     * Test case for null category
     */
    @Test
    public void testGetHighlightsContent_NullCategory() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(null);
        // act
        String result = processor.getHighlightsContent(dealCtx);
        // assert
        assertNull(result);
    }

    /**
     * Test case for hair care service
     */
    @Test
    public void testGetHighlightsContent_HairCare() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(categoryDTO);
        when(categoryDTO.getCategoryId()).thenReturn(501L);
        when(categoryDTO.getServiceType()).thenReturn("护理");
        // act
        String result = processor.getHighlightsContent(dealCtx);
        // assert
        // Since getNursingContent returns null by default
        assertNull(result);
    }

    /**
     * Test case for nail service with tag 100075023
     */
    @Test
    public void testGetHighlightsContent_NailServiceWithTag100075023() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(categoryDTO);
        when(categoryDTO.getCategoryId()).thenReturn(502L);
        when(categoryDTO.getServiceType()).thenReturn("美甲");
        DealGroupTagDTO tag = new DealGroupTagDTO();
        tag.setId(100075023L);
        // act
        String result = processor.getHighlightsContent(dealCtx);
        // assert
        // Since getNailContent1 returns null by default
        assertNull(result);
    }

    /**
     * Test case for nail service with tag 100069628
     */
    @Test
    public void testGetHighlightsContent_NailServiceWithTag100069628() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(categoryDTO);
        when(categoryDTO.getCategoryId()).thenReturn(502L);
        when(categoryDTO.getServiceType()).thenReturn("美甲");
        DealGroupTagDTO tag = new DealGroupTagDTO();
        tag.setId(100069628L);
        // act
        String result = processor.getHighlightsContent(dealCtx);
        // assert
        // Since getNailContent2 returns null by default
        assertNull(result);
    }

    /**
     * Test case for eyelash service
     */
    @Test
    public void testGetHighlightsContent_EyelashService() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(categoryDTO);
        when(categoryDTO.getCategoryId()).thenReturn(502L);
        when(categoryDTO.getServiceType()).thenReturn("美睫");
        // act
        String result = processor.getHighlightsContent(dealCtx);
        // assert
        // Since getEyelashesContent returns null by default
        assertNull(result);
    }

    /**
     * Test case for beauty SPA service
     */
    @Test
    public void testGetHighlightsContent_BeautySpaService() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(categoryDTO);
        when(categoryDTO.getCategoryId()).thenReturn(503L);
        when(categoryDTO.getServiceType()).thenReturn("SPA");
        // act
        String result = processor.getHighlightsContent(dealCtx);
        // assert
        // Since getOtherContent returns null by default
        assertNull(result);
    }

    /**
     * Test when all 3 attributes are present and valid
     */
    @Test
    public void testGetWearableNailAttrs_AllAttributesPresent() {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr1 = new AttrDTO();
        attr1.setName("isFreeWearingAtStore");
        attr1.setValue(Arrays.asList("true"));
        attrs.add(attr1);
        AttrDTO attr2 = new AttrDTO();
        attr2.setName("wearingNailsType");
        attr2.setValue(Arrays.asList("Type1"));
        attrs.add(attr2);
        AttrDTO attr3 = new AttrDTO();
        attr3.setName("nail_additional_item");
        attr3.setValue(Arrays.asList("Item1"));
        attrs.add(attr3);
        when(dealGroupDTO.getAttrs()).thenReturn(attrs);
        // act
        List<CommonAttrVO> result = processor.getWearableNailAttrs(dealCtx);
        // assert
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("增值服务", result.get(0).getName());
        assertEquals("到店免费佩戴", result.get(0).getValue());
        assertEquals("穿戴甲类型", result.get(1).getName());
        assertEquals("Type1", result.get(1).getValue());
        assertEquals("附赠项目", result.get(2).getName());
        assertEquals("Item1", result.get(2).getValue());
    }

    /**
     * Test when only 2 attributes are present
     */
    @Test
    public void testGetWearableNailAttrs_TwoAttributesPresent() {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr1 = new AttrDTO();
        attr1.setName("isFreeWearingAtStore");
        attr1.setValue(Arrays.asList("true"));
        attrs.add(attr1);
        AttrDTO attr2 = new AttrDTO();
        attr2.setName("wearingNailsType");
        attr2.setValue(Arrays.asList("Type1"));
        attrs.add(attr2);
        when(dealGroupDTO.getAttrs()).thenReturn(attrs);
        // act
        List<CommonAttrVO> result = processor.getWearableNailAttrs(dealCtx);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
    }

    /**
     * Test when only 1 attribute is present
     */
    @Test
    public void testGetWearableNailAttrs_OneAttributePresent() {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr1 = new AttrDTO();
        attr1.setName("isFreeWearingAtStore");
        attr1.setValue(Arrays.asList("true"));
        attrs.add(attr1);
        when(dealGroupDTO.getAttrs()).thenReturn(attrs);
        // act
        List<CommonAttrVO> result = processor.getWearableNailAttrs(dealCtx);
        // assert
        assertNull(result);
    }

    /**
     * Test when nail_additional_item is in JSON array format
     */
    @Test
    public void testGetWearableNailAttrs_JsonArrayAdditionalItem() {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr1 = new AttrDTO();
        attr1.setName("isFreeWearingAtStore");
        attr1.setValue(Arrays.asList("true"));
        attrs.add(attr1);
        AttrDTO attr2 = new AttrDTO();
        attr2.setName("wearingNailsType");
        attr2.setValue(Arrays.asList("Type1"));
        attrs.add(attr2);
        AttrDTO attr3 = new AttrDTO();
        attr3.setName("nail_additional_item");
        attr3.setValue(Arrays.asList("[\"Item1\",\"Item2\"]"));
        attrs.add(attr3);
        when(dealGroupDTO.getAttrs()).thenReturn(attrs);
        // act
        List<CommonAttrVO> result = processor.getWearableNailAttrs(dealCtx);
        // assert
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("附赠项目", result.get(2).getName());
        assertEquals("Item1", result.get(2).getValue());
    }

    /**
     * Test when no attributes are present
     */
    @Test
    public void testGetWearableNailAttrs_NoAttributes() {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getAttrs()).thenReturn(new ArrayList<>());
        // act
        List<CommonAttrVO> result = processor.getWearableNailAttrs(dealCtx);
        // assert
        assertNull(result);
    }

    /**
     * Test case for null dealGroup input
     */
    @Test
    public void testGetDealTags_NullDealGroup() throws Throwable {
        // arrange
        DealGroupDTO dealGroup = null;
        // act
        List<Long> result = invokePrivateGetDealTags(dealGroup);
        // assert
        assertTrue("Should return empty list for null dealGroup", result.isEmpty());
    }

    /**
     * Test case for dealGroup with null tags list
     */
    @Test
    public void testGetDealTags_NullTagsList() throws Throwable {
        // arrange
        DealGroupDTO dealGroup = new DealGroupDTO();
        dealGroup.setTags(null);
        // act
        List<Long> result = invokePrivateGetDealTags(dealGroup);
        // assert
        assertTrue("Should return empty list for null tags", result.isEmpty());
    }

    /**
     * Test case for dealGroup with empty tags list
     */
    @Test
    public void testGetDealTags_EmptyTagsList() throws Throwable {
        // arrange
        DealGroupDTO dealGroup = new DealGroupDTO();
        dealGroup.setTags(new ArrayList<>());
        // act
        List<Long> result = invokePrivateGetDealTags(dealGroup);
        // assert
        assertTrue("Should return empty list for empty tags", result.isEmpty());
    }

    /**
     * Test case for dealGroup with non-duplicate tags
     */
    @Test
    public void testGetDealTags_NonDuplicateTags() throws Throwable {
        // arrange
        DealGroupDTO dealGroup = new DealGroupDTO();
        List<DealGroupTagDTO> tags = Arrays.asList(createDealGroupTagDTO(1L), createDealGroupTagDTO(2L), createDealGroupTagDTO(3L));
        dealGroup.setTags(tags);
        // act
        List<Long> result = invokePrivateGetDealTags(dealGroup);
        // assert
        assertEquals("Should return list with all tag IDs", Arrays.asList(1L, 2L, 3L), result);
    }

    /**
     * Test case for dealGroup with duplicate tags
     */
    @Test
    public void testGetDealTags_DuplicateTags() throws Throwable {
        // arrange
        DealGroupDTO dealGroup = new DealGroupDTO();
        List<DealGroupTagDTO> tags = Arrays.asList(createDealGroupTagDTO(1L), createDealGroupTagDTO(2L), createDealGroupTagDTO(1L), createDealGroupTagDTO(2L));
        dealGroup.setTags(tags);
        // act
        List<Long> result = invokePrivateGetDealTags(dealGroup);
        // assert
        assertEquals("Should return list with distinct tag IDs", Arrays.asList(1L, 2L), result);
    }

    /**
     * Test case for null category scenario
     */
    @Test
    public void testGetHighlightsIdentify_WithNullCategory() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(null);
        // act
        String result = processor.getHighlightsIdentify(dealCtx);
        // assert
        assertEquals(StringUtils.EMPTY, result);
    }

    /**
     * Test when serviceTypeId is not 137013
     */
    @Test
    public void testGetTattooShoppingGuideAttrs_WhenServiceTypeIdIsNot137013() throws Throwable {
        // arrange
        BeautyHighlightsV2Processor processor = new BeautyHighlightsV2Processor();
        DealCtx ctx = Mockito.mock(DealCtx.class);
        DealGroupDTO dealGroupDTO = mockDealGroupDTOWithServiceType(138000L, "美甲服务");
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        // act
        List<CommonAttrVO> result = processor.getTattooShoppingGuideAttrs(ctx);
        // assert
        assertEquals(1, result.size());
        assertEquals("团单分类", result.get(0).getName());
        assertEquals("美甲服务", result.get(0).getValue());
    }

    /**
     * Test case for suitCrowds containing "不挑人群"
     */
    @Test
    public void testGetTattooShoppingGuideAttrs_WhenSuitCrowdsContainsNoLimit() throws Throwable {
        // arrange
        BeautyHighlightsV2Processor processor = new BeautyHighlightsV2Processor();
        DealCtx ctx = Mockito.mock(DealCtx.class);
        DealGroupDTO dealGroupDTO = mockDealGroupDTO("不挑人群");
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        // act
        List<CommonAttrVO> result = processor.getTattooShoppingGuideAttrs(ctx);
        // assert
        assertEquals(1, result.size());
        assertEquals("适合人群", result.get(0).getName());
        assertEquals("不挑人群", result.get(0).getValue());
    }

    /**
     * Test case for suitCrowds not containing "不挑人群" and having multiple values
     */
    @Test
    public void testGetTattooShoppingGuideAttrs_WhenSuitCrowdsNeedsLimit() throws Throwable {
        // arrange
        BeautyHighlightsV2Processor processor = new BeautyHighlightsV2Processor();
        DealCtx ctx = Mockito.mock(DealCtx.class);
        DealGroupDTO dealGroupDTO = mockDealGroupDTO("年轻人、中年人、老年人");
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        // act
        List<CommonAttrVO> result = processor.getTattooShoppingGuideAttrs(ctx);
        // assert
        assertEquals(1, result.size());
        assertEquals("适合人群", result.get(0).getName());
        assertEquals("年轻人、中年人", result.get(0).getValue());
    }

    /**
     * Test when dealGroup.getAttrs() returns null
     */
    @Test
    public void testGetOtherContent_WhenAttrsNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroup = new DealGroupDTO();
        dealGroup.setAttrs(null);
        // act
        String result = invokePrivateMethod(processor, "getOtherContent", dealGroup);
        // assert
        assertNull(result);
    }

    /**
     * Test when dealGroup.getAttrs() returns empty list
     */
    @Test
    public void testGetOtherContent_WhenAttrsEmpty() throws Throwable {
        // arrange
        DealGroupDTO dealGroup = new DealGroupDTO();
        dealGroup.setAttrs(new ArrayList<>());
        // act
        String result = invokePrivateMethod(processor, "getOtherContent", dealGroup);
        // assert
        assertNull(result);
    }

    /**
     * Test when selling_point attribute exists with value
     */
    @Test
    public void testGetOtherContent_WhenSellingPointExists() throws Throwable {
        // arrange
        DealGroupDTO dealGroup = new DealGroupDTO();
        AttrDTO attr = new AttrDTO();
        attr.setName("selling_point");
        attr.setValue(Arrays.asList("Test selling point"));
        dealGroup.setAttrs(Arrays.asList(attr));
        // act
        String result = invokePrivateMethod(processor, "getOtherContent", dealGroup);
        // assert
        assertEquals("Test selling point", result);
    }

    /**
     * Test when selling_point attribute exists but has empty value list
     */
    @Test
    public void testGetOtherContent_WhenSellingPointValueEmpty() throws Throwable {
        // arrange
        DealGroupDTO dealGroup = new DealGroupDTO();
        AttrDTO attr = new AttrDTO();
        attr.setName("selling_point");
        attr.setValue(new ArrayList<>());
        dealGroup.setAttrs(Arrays.asList(attr));
        // act
        String result = invokePrivateMethod(processor, "getOtherContent", dealGroup);
        // assert
        assertNull(result);
    }

    /**
     * Test when selling_point attribute exists with multiple values
     */
    @Test
    public void testGetOtherContent_WhenSellingPointHasMultipleValues() throws Throwable {
        // arrange
        DealGroupDTO dealGroup = new DealGroupDTO();
        AttrDTO attr = new AttrDTO();
        attr.setName("selling_point");
        attr.setValue(Arrays.asList("First point", "Second point"));
        dealGroup.setAttrs(Arrays.asList(attr));
        // act
        String result = invokePrivateMethod(processor, "getOtherContent", dealGroup);
        // assert
        assertEquals("First point", result);
    }

    /**
     * Test when attributes exist but no selling_point
     */
    @Test
    public void testGetOtherContent_WhenNoSellingPoint() throws Throwable {
        // arrange
        DealGroupDTO dealGroup = new DealGroupDTO();
        AttrDTO attr = new AttrDTO();
        attr.setName("other_attr");
        attr.setValue(Arrays.asList("Some value"));
        dealGroup.setAttrs(Arrays.asList(attr));
        // act
        String result = invokePrivateMethod(processor, "getOtherContent", dealGroup);
        // assert
        assertNull(result);
    }
}
