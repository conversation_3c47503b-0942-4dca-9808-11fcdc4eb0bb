package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.entity.QueryExhibitImageParam;
import com.sankuai.mpmctcontent.application.thrift.api.content.DealDetailPageGWService;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.Map;
import java.util.concurrent.Future;

import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.mockStatic;

@RunWith(MockitoJUnitRunner.class)
public class ImmersiveImageWrapper_PreGetImmersiveImage_1_Test {

    @InjectMocks
    private ImmersiveImageWrapper immersiveImageWrapper;

    @Mock
    private DealDetailPageGWService dealDetailPageGWServiceFuture;

    private MockedStatic<Lion> lionMockedStatic;
    @Before
    public void setUp() {
        lionMockedStatic = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        lionMockedStatic.close();
    }

    @Test
    public void testPreGetImmersiveImageWhenCategoryIdNotInConfig() throws Throwable {
        Map<String, Object> exhibitImageConfigMap = Collections.emptyMap();
        lionMockedStatic.when(() -> Lion.getMap(anyString(), anyString(), any(), any())).thenReturn(exhibitImageConfigMap);
        QueryExhibitImageParam param = QueryExhibitImageParam.builder().dpDealGroupId(1).categoryId(1).start(0).limit(10).shopId(1L).selectValue("").cityId(1).userLng(0.0).userLat(0.0).clientType(1).serviceType("").dealGroupStatus(1).build();
        EnvCtx envCtx = new EnvCtx();
        Future result = immersiveImageWrapper.preGetImmersiveImage(param, envCtx);
        assertNull(result);
    }

    @Test
    public void testPreGetImmersiveImageWhenExceptionThrown() throws Throwable {
        Map<String, Object> exhibitImageConfigMap = Collections.singletonMap("1", new Object());
        lionMockedStatic.when(() -> Lion.getMap(anyString(), anyString(), any(), any())).thenReturn(exhibitImageConfigMap);
        QueryExhibitImageParam param = QueryExhibitImageParam.builder().dpDealGroupId(1).categoryId(1).start(0).limit(10).shopId(1L).selectValue("").cityId(1).userLng(0.0).userLat(0.0).clientType(1).serviceType("").dealGroupStatus(1).build();
        EnvCtx envCtx = new EnvCtx();
        Future result = immersiveImageWrapper.preGetImmersiveImage(param, envCtx);
        assertNull(result);
    }
}
