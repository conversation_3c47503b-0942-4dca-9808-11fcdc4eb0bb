package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.tgc.open.entity.ExProxyCouponContext;
import com.sankuai.nib.mkt.common.base.enums.propertyValueType.ClientTypeEnum;
import java.lang.reflect.Method;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class CouponProcessorTest {

    @Mock
    private DealCtx dealCtx;

    @Mock
    private EnvCtx envCtx;

    private CouponProcessor couponProcessor = new CouponProcessor();

    private ClientTypeEnum invokePrivateMethod(DealCtx dealCtx) throws Exception {
        Method method = CouponProcessor.class.getDeclaredMethod("getClientTypeEnum", DealCtx.class);
        method.setAccessible(true);
        return (ClientTypeEnum) method.invoke(couponProcessor, dealCtx);
    }

    private ExProxyCouponContext invokePrivateMethod(CouponProcessor couponProcessor, String methodName, DealCtx dealCtx) throws Throwable {
        try {
            Method method = CouponProcessor.class.getDeclaredMethod(methodName, DealCtx.class);
            method.setAccessible(true);
            return (ExProxyCouponContext) method.invoke(couponProcessor, dealCtx);
        } catch (Exception e) {
            throw e.getCause();
        }
    }

    /**
     * 测试 MEITUAN_APP + isIos = true 的场景
     */
    @Test
    public void testGetClientTypeEnum_MeituanApp_Ios() throws Throwable {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_APP);
        when(envCtx.isIos()).thenReturn(true);
        // act
        ClientTypeEnum result = invokePrivateMethod(dealCtx);
        // assert
        assertEquals(ClientTypeEnum.IPHONE, result);
    }

    /**
     * 测试 MEITUAN_APP + isIos = false 的场景
     */
    @Test
    public void testGetClientTypeEnum_MeituanApp_Android() throws Throwable {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_APP);
        when(envCtx.isIos()).thenReturn(false);
        // act
        ClientTypeEnum result = invokePrivateMethod(dealCtx);
        // assert
        assertEquals(ClientTypeEnum.ANDROID, result);
    }

    /**
     * 测试 DIANPING_APP + isIos = true 的场景
     */
    @Test
    public void testGetClientTypeEnum_DianpingApp_Ios() throws Throwable {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.DIANPING_APP);
        when(envCtx.isIos()).thenReturn(true);
        // act
        ClientTypeEnum result = invokePrivateMethod(dealCtx);
        // assert
        assertEquals(ClientTypeEnum.DP_IPHONE, result);
    }

    /**
     * 测试 DIANPING_APP + isIos = false 的场景
     */
    @Test
    public void testGetClientTypeEnum_DianpingApp_Android() throws Throwable {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.DIANPING_APP);
        when(envCtx.isIos()).thenReturn(false);
        // act
        ClientTypeEnum result = invokePrivateMethod(dealCtx);
        // assert
        assertEquals(ClientTypeEnum.DP_ANDROID, result);
    }

    /**
     * 测试 MEITUAN_WEIXIN_MINIAPP 的场景
     */
    @Test
    public void testGetClientTypeEnum_MeituanWeixinMiniApp() throws Throwable {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP);
        // act
        ClientTypeEnum result = invokePrivateMethod(dealCtx);
        // assert
        assertEquals(ClientTypeEnum.WE_CHAT_APPLET, result);
    }

    /**
     * 测试 DIANPING_WEIXIN_MINIAPP 的场景
     */
    @Test
    public void testGetClientTypeEnum_DianpingWeixinMiniApp() throws Throwable {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP);
        // act
        ClientTypeEnum result = invokePrivateMethod(dealCtx);
        // assert
        assertEquals(ClientTypeEnum.DP_WE_CHAT_APPLET, result);
    }

    /**
     * 测试其他情况的场景
     */
    @Test
    public void testGetClientTypeEnum_Other() throws Throwable {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.UNKNOWN);
        // act
        ClientTypeEnum result = invokePrivateMethod(dealCtx);
        // assert
        assertEquals(ClientTypeEnum.PC, result);
    }

    /**
     * 测试正常场景：DealCtx 和 EnvCtx 的所有字段都有值
     */
    @Test
    public void testBuildExProxyCouponContextNormalCase() throws Throwable {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.getDpId()).thenReturn("dpId123");
        when(envCtx.getVersion()).thenReturn("v1.0");
        when(envCtx.getRequestURI()).thenReturn("/api/test");
        when(envCtx.getUserAgent()).thenReturn("Mozilla/5.0");
        when(envCtx.getUserIp()).thenReturn("698.277.3.1");
        when(dealCtx.getCx()).thenReturn("cx123");
        // act
        ExProxyCouponContext result = invokePrivateMethod(couponProcessor, "buildExProxyCouponContext", dealCtx);
        // assert
        assertNotNull(result);
        assertEquals("dpId123", result.getDpId());
        assertEquals("v1.0", result.getVersion());
        assertEquals("/api/test", result.getRequestURI());
        assertEquals("Mozilla/5.0", result.getUserAgent());
        assertEquals("698.277.3.1", result.getUserIp());
        assertEquals("cx123", result.getCx());
    }

    /**
     * 测试异常场景：DealCtx 为 null
     */
    @Test(expected = NullPointerException.class)
    public void testBuildExProxyCouponContextDealCtxNull() throws Throwable {
        // arrange
        DealCtx nullDealCtx = null;
        // act
        invokePrivateMethod(couponProcessor, "buildExProxyCouponContext", nullDealCtx);
        // assert
        // 预期抛出 NullPointerException
    }

    /**
     * 测试异常场景：DealCtx 的 EnvCtx 为 null
     */
    @Test(expected = NullPointerException.class)
    public void testBuildExProxyCouponContextEnvCtxNull() throws Throwable {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(null);
        // act
        invokePrivateMethod(couponProcessor, "buildExProxyCouponContext", dealCtx);
        // assert
        // 预期抛出 NullPointerException
    }

    /**
     * 测试异常场景：DealCtx 的 EnvCtx 中的某些字段为 null
     */
    @Test
    public void testBuildExProxyCouponContextEnvCtxFieldsNull() throws Throwable {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.getDpId()).thenReturn(null);
        when(envCtx.getVersion()).thenReturn(null);
        when(envCtx.getRequestURI()).thenReturn(null);
        when(envCtx.getUserAgent()).thenReturn(null);
        when(envCtx.getUserIp()).thenReturn(null);
        when(dealCtx.getCx()).thenReturn(null);
        // act
        ExProxyCouponContext result = invokePrivateMethod(couponProcessor, "buildExProxyCouponContext", dealCtx);
        // assert
        assertNotNull(result);
        assertNull(result.getDpId());
        assertNull(result.getVersion());
        assertNull(result.getRequestURI());
        assertNull(result.getUserAgent());
        assertNull(result.getUserIp());
        assertNull(result.getCx());
    }
}
