package com.dianping.mobile.mapi.dztgdetail.biz.processor.imagetext.strategy;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ImageTextStrategyEnum;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;

@RunWith(MockitoJUnitRunner.class)
public class ImageTextDetailStrategyFactoryTest {

    @InjectMocks
    private ImageTextDetailStrategyFactory imageTextDetailStrategyFactory;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private ImageTextDetailStrategy mockStrategy;

    @After
    public void tearDown() throws Exception {
        // Reset the strategy map to its original state
        Field strategyMapField = ImageTextDetailStrategyFactory.class.getDeclaredField("STRATEGY_MAP");
        strategyMapField.setAccessible(true);
        Map<ImageTextStrategyEnum, ImageTextDetailStrategy> strategyMap = (Map<ImageTextStrategyEnum, ImageTextDetailStrategy>) strategyMapField.get(null);
        strategyMap.clear();
    }

    /**
     * Test normal scenario.
     */
    @Test
    public void testGetImageTextDetailStrategyNormal() throws Throwable {
        // Given
        // Mock the strategy map using reflection
        Field strategyMapField = ImageTextDetailStrategyFactory.class.getDeclaredField("STRATEGY_MAP");
        strategyMapField.setAccessible(true);
        Map<ImageTextStrategyEnum, ImageTextDetailStrategy> strategyMap = (Map<ImageTextStrategyEnum, ImageTextDetailStrategy>) strategyMapField.get(null);
        strategyMap.clear();
        // Mock the strategy enum
        strategyMap.put(ImageTextStrategyEnum.DEFAULT, mockStrategy);
        // Set the application context
        imageTextDetailStrategyFactory.setApplicationContext(applicationContext);
        // Act
        ImageTextDetailStrategy result = imageTextDetailStrategyFactory.getImageTextDetailStrategy("default");
        // Assert
        assertNotNull(result);
        assertEquals(mockStrategy, result);
    }

    /**
     * Tests the afterPropertiesSet method under normal conditions.
     */
    @Test
    public void testAfterPropertiesSetNormal() throws Throwable {
        // Arrange
        ImageTextDetailStrategyFactory factory = new ImageTextDetailStrategyFactory();
        factory.setApplicationContext(applicationContext);
        Map<String, ImageTextDetailStrategy> mockMap = new HashMap<>();
        mockMap.put("default", mockStrategy);
        when(applicationContext.getBeansOfType(ImageTextDetailStrategy.class)).thenReturn(mockMap);
        when(mockStrategy.getStrategyName()).thenReturn(ImageTextStrategyEnum.DEFAULT);
        // Act
        factory.afterPropertiesSet();
        // Assert
        verify(applicationContext, times(1)).getBeansOfType(ImageTextDetailStrategy.class);
    }

    /**
     * Tests the afterPropertiesSet method under exceptional conditions.
     */
    @Test(expected = Exception.class)
    public void testAfterPropertiesSetException() throws Throwable {
        // Arrange
        ImageTextDetailStrategyFactory factory = new ImageTextDetailStrategyFactory();
        factory.setApplicationContext(applicationContext);
        when(applicationContext.getBeansOfType(ImageTextDetailStrategy.class)).thenThrow(new Exception());
        // Act
        factory.afterPropertiesSet();
        // Assert
        verify(applicationContext, times(1)).getBeansOfType(ImageTextDetailStrategy.class);
    }
}
