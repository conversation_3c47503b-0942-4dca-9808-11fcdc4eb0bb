package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

/**
 * Test class for MassageHighlightsV2Processor's getJoyFacilityValue method
 */
@RunWith(MockitoJUnitRunner.class)
public class MassageHighlightsV2ProcessorGetJoyFacilityValueTest {

    private final MassageHighlightsV2Processor processor = new MassageHighlightsV2Processor();

    // Helper method to invoke the private method using reflection
    private String invokePrivateMethod(List<ServiceProjectAttrDTO> attrs) throws Exception {
        Method method = MassageHighlightsV2Processor.class.getDeclaredMethod("getJoyFacilityValue", List.class);
        method.setAccessible(true);
        return (String) method.invoke(processor, attrs);
    }

    /**
     * Test when input list is empty
     */
    @Test
    public void testGetJoyFacilityValue_EmptyList() throws Throwable {
        // arrange
        List<ServiceProjectAttrDTO> attrs = Collections.emptyList();
        // act
        String result = invokePrivateMethod(attrs);
        // assert
        assertNull(result);
    }

    /**
     * Test when no matching attrName exists
     */
    @Test
    public void testGetJoyFacilityValue_NoMatchingAttrName() throws Throwable {
        // arrange
        ServiceProjectAttrDTO attr = new ServiceProjectAttrDTO();
        attr.setAttrName("otherAttr");
        attr.setAttrValue("someValue");
        List<ServiceProjectAttrDTO> attrs = Collections.singletonList(attr);
        // act
        String result = invokePrivateMethod(attrs);
        // assert
        assertNull(result);
    }

    /**
     * Test when matching attrName exists but value is null
     */
    @Test
    public void testGetJoyFacilityValue_NullAttrValue() throws Throwable {
        // arrange
        ServiceProjectAttrDTO attr = new ServiceProjectAttrDTO();
        attr.setAttrName("serviceFacility");
        attr.setAttrValue(null);
        List<ServiceProjectAttrDTO> attrs = Collections.singletonList(attr);
        // act
        String result = invokePrivateMethod(attrs);
        // assert
        assertNull(result);
    }

    /**
     * Test when matching attrName exists but value is empty
     */
    @Test
    public void testGetJoyFacilityValue_EmptyAttrValue() throws Throwable {
        // arrange
        ServiceProjectAttrDTO attr = new ServiceProjectAttrDTO();
        attr.setAttrName("serviceFacility");
        attr.setAttrValue("");
        List<ServiceProjectAttrDTO> attrs = Collections.singletonList(attr);
        // act
        String result = invokePrivateMethod(attrs);
        // assert
        assertNull(result);
    }

    /**
     * Test when matching attrName exists but value is "无"
     */
    @Test
    public void testGetJoyFacilityValue_InvalidAttrValue() throws Throwable {
        // arrange
        ServiceProjectAttrDTO attr = new ServiceProjectAttrDTO();
        attr.setAttrName("serviceFacility");
        attr.setAttrValue("无");
        List<ServiceProjectAttrDTO> attrs = Collections.singletonList(attr);
        // act
        String result = invokePrivateMethod(attrs);
        // assert
        assertNull(result);
    }

    /**
     * Test when valid matching attribute exists
     */
    @Test
    public void testGetJoyFacilityValue_ValidMatch() throws Throwable {
        // arrange
        ServiceProjectAttrDTO attr = new ServiceProjectAttrDTO();
        attr.setAttrName("serviceFacility");
        attr.setAttrValue("设施1");
        List<ServiceProjectAttrDTO> attrs = Collections.singletonList(attr);
        // act
        String result = invokePrivateMethod(attrs);
        // assert
        assertEquals("设施1", result);
    }

    /**
     * Test when multiple valid matches exist - should return first match
     */
    @Test
    public void testGetJoyFacilityValue_MultipleMatches() throws Throwable {
        // arrange
        ServiceProjectAttrDTO attr1 = new ServiceProjectAttrDTO();
        attr1.setAttrName("serviceFacility");
        attr1.setAttrValue("设施1");
        ServiceProjectAttrDTO attr2 = new ServiceProjectAttrDTO();
        attr2.setAttrName("serviceFacility");
        attr2.setAttrValue("设施2");
        List<ServiceProjectAttrDTO> attrs = Arrays.asList(attr1, attr2);
        // act
        String result = invokePrivateMethod(attrs);
        // assert
        assertEquals("设施1", result);
    }

    /**
     * Test with mixed valid and invalid attributes
     */
    @Test
    public void testGetJoyFacilityValue_MixedAttributes() throws Throwable {
        // arrange
        ServiceProjectAttrDTO attr1 = new ServiceProjectAttrDTO();
        attr1.setAttrName("otherAttr");
        attr1.setAttrValue("value1");
        ServiceProjectAttrDTO attr2 = new ServiceProjectAttrDTO();
        attr2.setAttrName("serviceFacility");
        attr2.setAttrValue("设施1");
        ServiceProjectAttrDTO attr3 = new ServiceProjectAttrDTO();
        attr3.setAttrName("serviceFacility");
        attr3.setAttrValue("无");
        List<ServiceProjectAttrDTO> attrs = Arrays.asList(attr1, attr2, attr3);
        // act
        String result = invokePrivateMethod(attrs);
        // assert
        assertEquals("设施1", result);
    }
}
