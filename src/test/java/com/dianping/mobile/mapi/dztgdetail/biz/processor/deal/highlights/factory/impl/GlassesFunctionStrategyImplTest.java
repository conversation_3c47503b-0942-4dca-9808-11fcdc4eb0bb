package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.factory.impl;

import com.dianping.lion.client.util.CollectionUtils;
import com.dianping.lion.client.util.JsonUtils;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.glasses.factory.GlassesStrategy;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.glasses.factory.impl.GlassesFunctionStrategyImpl;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CommonAttrVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.highlights.DztgHighlightsModule;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class GlassesFunctionStrategyImplTest {

    @InjectMocks
    private GlassesFunctionStrategyImpl glassesFunctionStrategy;

    @Mock
    private DealCtx ctx;

    @Mock
    private DealGroupDTO dealGroupDTO;


    /**
     * 测试buildMoudle方法，正常情况
     */
    @Test
    public void testBuildMoudleNormalCase() throws Throwable {
        // arrange
        ctx = new DealCtx(new EnvCtx());
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("lens_function");
        attrDTO.setValue(Arrays.asList( "[\"功能1\",\"功能2\"]"));
        ctx.setDealGroupDTO(dealGroupDTO);
        when(dealGroupDTO.getAttrs()).thenReturn(Arrays.asList(attrDTO));
        DztgHighlightsModule dztgHighlightsModule = new DztgHighlightsModule();
        dztgHighlightsModule.setAttrs(new ArrayList<CommonAttrVO>());
        ctx.setHighlightsModule(dztgHighlightsModule);

        // act
        glassesFunctionStrategy.buildMoudle(ctx);

        // assert
        assertEquals(1, dztgHighlightsModule.getAttrs().size());
        CommonAttrVO commonAttrVO = dztgHighlightsModule.getAttrs().get(0);
        assertEquals("镜片功能",commonAttrVO.getName());
        assertEquals("功能1、功能2",commonAttrVO.getValue());
    }
    @Test
    public void testBuildMoudleAttrNull() throws Throwable {
        // arrange
        ctx = new DealCtx(new EnvCtx());
        AttrDTO attrDTO = new AttrDTO();
        ctx.setDealGroupDTO(null);
        DztgHighlightsModule dztgHighlightsModule = new DztgHighlightsModule();
        dztgHighlightsModule.setAttrs(new ArrayList<CommonAttrVO>());
        ctx.setHighlightsModule(dztgHighlightsModule);

        // act
        glassesFunctionStrategy.buildMoudle(ctx);

        // assert
        assertEquals(0,ctx.getHighlightsModule().getAttrs().size());
    }

    @Test
    public void testBuildMoudleDealGroupNull() throws Throwable {
        // arrange
        ctx = new DealCtx(new EnvCtx());
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("whether_pickup");
        attrDTO.setValue(Arrays.asList("是"));
        ctx.setDealGroupDTO(null);
        DztgHighlightsModule dztgHighlightsModule = new DztgHighlightsModule();
        dztgHighlightsModule.setAttrs(new ArrayList<CommonAttrVO>());
        ctx.setHighlightsModule(dztgHighlightsModule);

        // act
        glassesFunctionStrategy.buildMoudle(ctx);

        // assert
        assertEquals(0,ctx.getHighlightsModule().getAttrs().size());
    }
}
