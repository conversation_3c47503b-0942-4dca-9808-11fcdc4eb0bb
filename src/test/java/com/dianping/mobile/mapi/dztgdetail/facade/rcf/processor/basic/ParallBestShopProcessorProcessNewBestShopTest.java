package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.basic;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.deal.common.enums.GpsType;
import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.deal.shop.dto.BestShopFastReq;
import com.dianping.deal.shop.dto.BestShopReq;
import com.dianping.deal.shop.dto.BestShopSimpleDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.Processor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperCacheWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.exception.BestShopNotEqualException;
import com.dianping.mobile.mapi.dztgdetail.util.DealProductUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LogUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;
import org.apache.commons.collections.CollectionUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ParallBestShopProcessorProcessNewBestShopTest {

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private MapperCacheWrapper mapperCacheWrapper;

    @Mock
    private DealCtx ctx;

    @Mock
    private FutureCtx futureCtx;

    @Mock
    private Future<BestShopSimpleDTO> fastBestShopFuture;

    @Mock
    private Future<BestShopDTO> bestShopFuture;

    @Mock
    private DealGroupDTO dealGroupDTO;

    @Mock
    private EnvCtx envCtx;

    private List<Long> relatedShops;

    @InjectMocks
    private ParallBestShopProcessor parallBestShopProcessor;

    @Before
    public void setUp() {
        relatedShops = new ArrayList<>();
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
        when(futureCtx.getFastBestShopFuture()).thenReturn(fastBestShopFuture);
        when(futureCtx.getBestShopFuture()).thenReturn(bestShopFuture);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        // Integer value instead of String
        when(envCtx.getClientType()).thenReturn(1);
    }

    /**
     * Test case where source shop ID exists and is bound to the deal group
     */
    @Test
    public void testProcessNewBestShop_WithSourceShopIdBound() throws Throwable {
        // arrange
        long dpShopId = 123L;
        long mtShopId = 456L;
        when(ctx.getDpLongShopId()).thenReturn(dpShopId);
        when(mapperCacheWrapper.fetchMtShopId(dpShopId)).thenReturn(mtShopId);
        relatedShops.add(dpShopId);
        // Create a test processor with overridden methods
        ParallBestShopProcessor testProcessor = new ParallBestShopProcessor() {

            public boolean isNeedDiff() {
                return false;
            }

            protected boolean checkShopNoExist(DealGroupDTO dealGroupDTO, Long shopId, boolean mt) {
                // Shop exists
                return false;
            }

            public boolean isEnable(DealCtx ctx) {
                return true;
            }

            public boolean isEnd(DealCtx ctx) {
                return false;
            }

            public void prepare(DealCtx ctx) {
            }

            public void process(DealCtx ctx) {
            }

            public List<Integer> getConfigUrlDztgClient(DealCtx ctx) {
                return null;
            }

            public boolean matchDztgClient(DealCtx ctx, Processor<DealCtx> processor) {
                return false;
            }
        };
        // Set the mocks to the test processor
        Field dealGroupWrapperField = ParallBestShopProcessor.class.getDeclaredField("dealGroupWrapper");
        dealGroupWrapperField.setAccessible(true);
        dealGroupWrapperField.set(testProcessor, dealGroupWrapper);
        Field mapperCacheWrapperField = ParallBestShopProcessor.class.getDeclaredField("mapperCacheWrapper");
        mapperCacheWrapperField.setAccessible(true);
        mapperCacheWrapperField.set(testProcessor, mapperCacheWrapper);
        // Use reflection to call the private method from ParallBestShopProcessor class
        Method processNewBestShopMethod = ParallBestShopProcessor.class.getDeclaredMethod("processNewBestShop", DealCtx.class, List.class);
        processNewBestShopMethod.setAccessible(true);
        processNewBestShopMethod.invoke(testProcessor, ctx, relatedShops);
        // assert
        verify(ctx, never()).setDpLongShopId(anyLong());
        verify(ctx).setBestShopResp(any(BestShopDTO.class));
        verify(ctx).setNeedFillBestShop(true);
    }

    /**
     * Test case where source shop ID exists but is not bound to the deal group
     */
    @Test
    public void testProcessNewBestShop_WithSourceShopIdNotBound() throws Throwable {
        // arrange
        long dpShopId = 123L;
        long mtShopId = 456L;
        BestShopSimpleDTO bestShopSimpleDTO = new BestShopSimpleDTO();
        bestShopSimpleDTO.setDpShopId(789L);
        when(ctx.getDpLongShopId()).thenReturn(dpShopId);
        when(dealGroupWrapper.getFutureResult(fastBestShopFuture)).thenReturn(bestShopSimpleDTO);
        when(mapperCacheWrapper.fetchMtShopId(bestShopSimpleDTO.getDpShopId())).thenReturn(mtShopId);
        // Create a test processor with overridden methods
        ParallBestShopProcessor testProcessor = new ParallBestShopProcessor() {

            public boolean isNeedDiff() {
                return false;
            }

            protected boolean checkShopNoExist(DealGroupDTO dealGroupDTO, Long shopId, boolean mt) {
                // Shop does not exist
                return true;
            }

            public boolean isEnable(DealCtx ctx) {
                return true;
            }

            public boolean isEnd(DealCtx ctx) {
                return false;
            }

            public void prepare(DealCtx ctx) {
            }

            public void process(DealCtx ctx) {
            }

            public List<Integer> getConfigUrlDztgClient(DealCtx ctx) {
                return null;
            }

            public boolean matchDztgClient(DealCtx ctx, Processor<DealCtx> processor) {
                return false;
            }
        };
        // Set the mocks to the test processor
        Field dealGroupWrapperField = ParallBestShopProcessor.class.getDeclaredField("dealGroupWrapper");
        dealGroupWrapperField.setAccessible(true);
        dealGroupWrapperField.set(testProcessor, dealGroupWrapper);
        Field mapperCacheWrapperField = ParallBestShopProcessor.class.getDeclaredField("mapperCacheWrapper");
        mapperCacheWrapperField.setAccessible(true);
        mapperCacheWrapperField.set(testProcessor, mapperCacheWrapper);
        // Use reflection to call the private method from ParallBestShopProcessor class
        Method processNewBestShopMethod = ParallBestShopProcessor.class.getDeclaredMethod("processNewBestShop", DealCtx.class, List.class);
        processNewBestShopMethod.setAccessible(true);
        processNewBestShopMethod.invoke(testProcessor, ctx, relatedShops);
        // assert
        verify(ctx).setDpLongShopId(bestShopSimpleDTO.getDpShopId());
        verify(ctx).setMtLongShopId(mtShopId);
        verify(ctx).setBestShopResp(any(BestShopDTO.class));
        verify(ctx).setNeedFillBestShop(true);
    }

    /**
     * Test case where source shop ID is empty and best shop is selected
     */
    @Test
    public void testProcessNewBestShop_WithEmptySourceShopId() throws Throwable {
        // arrange
        long dpShopId = 0L;
        long mtShopId = 456L;
        BestShopSimpleDTO bestShopSimpleDTO = new BestShopSimpleDTO();
        bestShopSimpleDTO.setDpShopId(789L);
        when(ctx.getDpLongShopId()).thenReturn(dpShopId);
        when(dealGroupWrapper.getFutureResult(fastBestShopFuture)).thenReturn(bestShopSimpleDTO);
        when(mapperCacheWrapper.fetchMtShopId(bestShopSimpleDTO.getDpShopId())).thenReturn(mtShopId);
        when(ctx.getRequestSource()).thenReturn("test_source");
        when(ctx.getUserId4P()).thenReturn(123L);
        // Create a test processor with overridden methods
        ParallBestShopProcessor testProcessor = new ParallBestShopProcessor() {

            public boolean isNeedDiff() {
                return false;
            }

            public boolean isEnable(DealCtx ctx) {
                return true;
            }

            public boolean isEnd(DealCtx ctx) {
                return false;
            }

            public void prepare(DealCtx ctx) {
            }

            public void process(DealCtx ctx) {
            }

            public List<Integer> getConfigUrlDztgClient(DealCtx ctx) {
                return null;
            }

            public boolean matchDztgClient(DealCtx ctx, Processor<DealCtx> processor) {
                return false;
            }
        };
        // Set the mocks to the test processor
        Field dealGroupWrapperField = ParallBestShopProcessor.class.getDeclaredField("dealGroupWrapper");
        dealGroupWrapperField.setAccessible(true);
        dealGroupWrapperField.set(testProcessor, dealGroupWrapper);
        Field mapperCacheWrapperField = ParallBestShopProcessor.class.getDeclaredField("mapperCacheWrapper");
        mapperCacheWrapperField.setAccessible(true);
        mapperCacheWrapperField.set(testProcessor, mapperCacheWrapper);
        // Use reflection to call the private method from ParallBestShopProcessor class
        Method processNewBestShopMethod = ParallBestShopProcessor.class.getDeclaredMethod("processNewBestShop", DealCtx.class, List.class);
        processNewBestShopMethod.setAccessible(true);
        processNewBestShopMethod.invoke(testProcessor, ctx, relatedShops);
        // assert
        verify(ctx).setDpLongShopId(bestShopSimpleDTO.getDpShopId());
        verify(ctx).setMtLongShopId(mtShopId);
        verify(ctx).setBestShopResp(any(BestShopDTO.class));
        verify(ctx).setNeedFillBestShop(true);
    }

    /**
     * Test case where best shop result is null and related shops list is not empty
     */
    @Test
    public void testProcessNewBestShop_WithNullBestShopAndRelatedShops() throws Throwable {
        // arrange
        long dpShopId = 0L;
        long mtShopId = 456L;
        long relatedShopId = 123L;
        relatedShops.add(relatedShopId);
        when(ctx.getDpLongShopId()).thenReturn(dpShopId);
        when(ctx.getMtLongShopId()).thenReturn(0L);
        when(dealGroupWrapper.getFutureResult(fastBestShopFuture)).thenReturn(null);
        when(ctx.getRequestSource()).thenReturn("test_source");
        when(ctx.getUserId4P()).thenReturn(123L);
        // Instead of trying to override the method, we'll directly set up the mock to respond correctly
        // when the method under test calls setMtLongShopId
        doAnswer(invocation -> {
            // When setDpLongShopId is called with relatedShopId, we'll call setMtLongShopId with mtShopId
            if (invocation.getArgument(0).equals(relatedShopId)) {
                ctx.setMtLongShopId(mtShopId);
            }
            return null;
        }).when(ctx).setDpLongShopId(relatedShopId);
        // Create a test processor with overridden methods
        ParallBestShopProcessor testProcessor = new ParallBestShopProcessor() {

            public boolean isNeedDiff() {
                return false;
            }

            public boolean isEnable(DealCtx ctx) {
                return true;
            }

            public boolean isEnd(DealCtx ctx) {
                return false;
            }

            public void prepare(DealCtx ctx) {
            }

            public void process(DealCtx ctx) {
            }

            public List<Integer> getConfigUrlDztgClient(DealCtx ctx) {
                return null;
            }

            public boolean matchDztgClient(DealCtx ctx, Processor<DealCtx> processor) {
                return false;
            }
        };
        // Set the mocks to the test processor
        Field dealGroupWrapperField = ParallBestShopProcessor.class.getDeclaredField("dealGroupWrapper");
        dealGroupWrapperField.setAccessible(true);
        dealGroupWrapperField.set(testProcessor, dealGroupWrapper);
        Field mapperCacheWrapperField = ParallBestShopProcessor.class.getDeclaredField("mapperCacheWrapper");
        mapperCacheWrapperField.setAccessible(true);
        mapperCacheWrapperField.set(testProcessor, mapperCacheWrapper);
        // Use reflection to call the private method from ParallBestShopProcessor class
        Method processNewBestShopMethod = ParallBestShopProcessor.class.getDeclaredMethod("processNewBestShop", DealCtx.class, List.class);
        processNewBestShopMethod.setAccessible(true);
        processNewBestShopMethod.invoke(testProcessor, ctx, relatedShops);
        // assert
        verify(ctx).setDpLongShopId(relatedShopId);
        verify(ctx).setMtLongShopId(mtShopId);
        verify(ctx).setBestShopResp(any(BestShopDTO.class));
        verify(ctx).setNeedFillBestShop(true);
    }

    /**
     * Test case where best shop result is null and related shops list is empty
     */
    @Test
    public void testProcessNewBestShop_WithNullBestShopAndEmptyRelatedShops() throws Throwable {
        // arrange
        long dpShopId = 0L;
        when(ctx.getDpLongShopId()).thenReturn(dpShopId);
        when(ctx.getMtLongShopId()).thenReturn(0L);
        when(dealGroupWrapper.getFutureResult(fastBestShopFuture)).thenReturn(null);
        when(ctx.getRequestSource()).thenReturn("test_source");
        when(ctx.getUserId4P()).thenReturn(123L);
        // Create a test processor with overridden methods
        ParallBestShopProcessor testProcessor = new ParallBestShopProcessor() {

            public boolean isNeedDiff() {
                return false;
            }

            public boolean isEnable(DealCtx ctx) {
                return true;
            }

            public boolean isEnd(DealCtx ctx) {
                return false;
            }

            public void prepare(DealCtx ctx) {
            }

            public void process(DealCtx ctx) {
            }

            public List<Integer> getConfigUrlDztgClient(DealCtx ctx) {
                return null;
            }

            public boolean matchDztgClient(DealCtx ctx, Processor<DealCtx> processor) {
                return false;
            }
        };
        // Set the mocks to the test processor
        Field dealGroupWrapperField = ParallBestShopProcessor.class.getDeclaredField("dealGroupWrapper");
        dealGroupWrapperField.setAccessible(true);
        dealGroupWrapperField.set(testProcessor, dealGroupWrapper);
        Field mapperCacheWrapperField = ParallBestShopProcessor.class.getDeclaredField("mapperCacheWrapper");
        mapperCacheWrapperField.setAccessible(true);
        mapperCacheWrapperField.set(testProcessor, mapperCacheWrapper);
        // Use reflection to call the private method from ParallBestShopProcessor class
        Method processNewBestShopMethod = ParallBestShopProcessor.class.getDeclaredMethod("processNewBestShop", DealCtx.class, List.class);
        processNewBestShopMethod.setAccessible(true);
        processNewBestShopMethod.invoke(testProcessor, ctx, Collections.emptyList());
        // assert
        verify(ctx, never()).setDpLongShopId(anyLong());
        verify(ctx, never()).setMtLongShopId(anyLong());
        verify(ctx).setBestShopResp(any(BestShopDTO.class));
        verify(ctx).setNeedFillBestShop(true);
    }

    /**
     * Test case where comparison is enabled and shop IDs are equal
     */
    @Test
    public void testProcessNewBestShop_WithComparisonEnabledAndEqualShopIds() throws Throwable {
        // arrange
        long dpShopId = 123L;
        long mtShopId = 456L;
        BestShopSimpleDTO bestShopSimpleDTO = new BestShopSimpleDTO();
        bestShopSimpleDTO.setDpShopId(dpShopId);
        BestShopDTO bestShopDTO = new BestShopDTO();
        bestShopDTO.setDpShopId(dpShopId);
        when(ctx.getDpLongShopId()).thenReturn(dpShopId);
        when(ctx.getMtLongShopId()).thenReturn(mtShopId);
        relatedShops.add(dpShopId);
        when(dealGroupWrapper.getFutureResult(bestShopFuture)).thenReturn(bestShopDTO);
        when(dealGroupWrapper.getFutureResult(fastBestShopFuture)).thenReturn(bestShopSimpleDTO);
        // Create a test processor with overridden methods
        ParallBestShopProcessor testProcessor = new ParallBestShopProcessor() {

            public boolean isNeedDiff() {
                return true;
            }

            protected boolean checkShopNoExist(DealGroupDTO dealGroupDTO, Long shopId, boolean mt) {
                // Shop exists
                return false;
            }

            public boolean isEnable(DealCtx ctx) {
                return true;
            }

            public boolean isEnd(DealCtx ctx) {
                return false;
            }

            public void prepare(DealCtx ctx) {
            }

            public void process(DealCtx ctx) {
            }

            public List<Integer> getConfigUrlDztgClient(DealCtx ctx) {
                return null;
            }

            public boolean matchDztgClient(DealCtx ctx, Processor<DealCtx> processor) {
                return false;
            }

            // Custom implementation to avoid starting a new thread
            protected void compare(DealCtx ctx) {
                BestShopSimpleDTO bestShopSimpleDTO = dealGroupWrapper.getFutureResult(ctx.getFutureCtx().getFastBestShopFuture());
                BestShopDTO bestShopDTO = dealGroupWrapper.getFutureResult(ctx.getFutureCtx().getBestShopFuture());
            }
        };
        // Set the mocks to the test processor
        Field dealGroupWrapperField = ParallBestShopProcessor.class.getDeclaredField("dealGroupWrapper");
        dealGroupWrapperField.setAccessible(true);
        dealGroupWrapperField.set(testProcessor, dealGroupWrapper);
        Field mapperCacheWrapperField = ParallBestShopProcessor.class.getDeclaredField("mapperCacheWrapper");
        mapperCacheWrapperField.setAccessible(true);
        mapperCacheWrapperField.set(testProcessor, mapperCacheWrapper);
        // Use reflection to call the private method from ParallBestShopProcessor class
        Method processNewBestShopMethod = ParallBestShopProcessor.class.getDeclaredMethod("processNewBestShop", DealCtx.class, List.class);
        processNewBestShopMethod.setAccessible(true);
        processNewBestShopMethod.invoke(testProcessor, ctx, relatedShops);
        // assert
        verify(ctx).setBestShopResp(any(BestShopDTO.class));
        verify(ctx).setNeedFillBestShop(true);
    }

    /**
     * Test case where comparison is enabled but shop IDs are not equal
     */
    @Test
    public void testProcessNewBestShop_WithComparisonEnabledAndDifferentShopIds() throws Throwable {
        // arrange
        long dpShopId = 123L;
        long mtShopId = 456L;
        BestShopSimpleDTO bestShopSimpleDTO = new BestShopSimpleDTO();
        // Different from dpShopId
        bestShopSimpleDTO.setDpShopId(789L);
        BestShopDTO bestShopDTO = new BestShopDTO();
        bestShopDTO.setDpShopId(dpShopId);
        when(ctx.getDpLongShopId()).thenReturn(dpShopId);
        when(ctx.getMtLongShopId()).thenReturn(mtShopId);
        relatedShops.add(dpShopId);
        when(dealGroupWrapper.getFutureResult(bestShopFuture)).thenReturn(bestShopDTO);
        when(dealGroupWrapper.getFutureResult(fastBestShopFuture)).thenReturn(bestShopSimpleDTO);
        // Create a test processor with overridden methods
        ParallBestShopProcessor testProcessor = new ParallBestShopProcessor() {

            public boolean isNeedDiff() {
                return true;
            }

            protected boolean checkShopNoExist(DealGroupDTO dealGroupDTO, Long shopId, boolean mt) {
                // Shop exists
                return false;
            }

            public boolean isEnable(DealCtx ctx) {
                return true;
            }

            public boolean isEnd(DealCtx ctx) {
                return false;
            }

            public void prepare(DealCtx ctx) {
            }

            public void process(DealCtx ctx) {
            }

            public List<Integer> getConfigUrlDztgClient(DealCtx ctx) {
                return null;
            }

            public boolean matchDztgClient(DealCtx ctx, Processor<DealCtx> processor) {
                return false;
            }

            // Custom implementation to avoid starting a new thread
            protected void compare(DealCtx ctx) {
                BestShopSimpleDTO bestShopSimpleDTO = dealGroupWrapper.getFutureResult(ctx.getFutureCtx().getFastBestShopFuture());
                BestShopDTO bestShopDTO = dealGroupWrapper.getFutureResult(ctx.getFutureCtx().getBestShopFuture());
            }
        };
        // Set the mocks to the test processor
        Field dealGroupWrapperField = ParallBestShopProcessor.class.getDeclaredField("dealGroupWrapper");
        dealGroupWrapperField.setAccessible(true);
        dealGroupWrapperField.set(testProcessor, dealGroupWrapper);
        Field mapperCacheWrapperField = ParallBestShopProcessor.class.getDeclaredField("mapperCacheWrapper");
        mapperCacheWrapperField.setAccessible(true);
        mapperCacheWrapperField.set(testProcessor, mapperCacheWrapper);
        // Use reflection to call the private method from ParallBestShopProcessor class
        Method processNewBestShopMethod = ParallBestShopProcessor.class.getDeclaredMethod("processNewBestShop", DealCtx.class, List.class);
        processNewBestShopMethod.setAccessible(true);
        processNewBestShopMethod.invoke(testProcessor, ctx, relatedShops);
        // assert
        verify(ctx).setBestShopResp(any(BestShopDTO.class));
        verify(ctx).setNeedFillBestShop(true);
    }

    /**
     * Test case where comparison is disabled
     */
    @Test
    public void testProcessNewBestShop_WithComparisonDisabled() throws Throwable {
        // arrange
        long dpShopId = 123L;
        long mtShopId = 456L;
        when(ctx.getDpLongShopId()).thenReturn(dpShopId);
        when(ctx.getMtLongShopId()).thenReturn(mtShopId);
        relatedShops.add(dpShopId);
        // Create a test processor with overridden methods
        ParallBestShopProcessor testProcessor = new ParallBestShopProcessor() {

            public boolean isNeedDiff() {
                return false;
            }

            protected boolean checkShopNoExist(DealGroupDTO dealGroupDTO, Long shopId, boolean mt) {
                // Shop exists
                return false;
            }

            public boolean isEnable(DealCtx ctx) {
                return true;
            }

            public boolean isEnd(DealCtx ctx) {
                return false;
            }

            public void prepare(DealCtx ctx) {
            }

            public void process(DealCtx ctx) {
            }

            public List<Integer> getConfigUrlDztgClient(DealCtx ctx) {
                return null;
            }

            public boolean matchDztgClient(DealCtx ctx, Processor<DealCtx> processor) {
                return false;
            }
        };
        // Set the mocks to the test processor
        Field dealGroupWrapperField = ParallBestShopProcessor.class.getDeclaredField("dealGroupWrapper");
        dealGroupWrapperField.setAccessible(true);
        dealGroupWrapperField.set(testProcessor, dealGroupWrapper);
        Field mapperCacheWrapperField = ParallBestShopProcessor.class.getDeclaredField("mapperCacheWrapper");
        mapperCacheWrapperField.setAccessible(true);
        mapperCacheWrapperField.set(testProcessor, mapperCacheWrapper);
        // Use reflection to call the private method from ParallBestShopProcessor class
        Method processNewBestShopMethod = ParallBestShopProcessor.class.getDeclaredMethod("processNewBestShop", DealCtx.class, List.class);
        processNewBestShopMethod.setAccessible(true);
        processNewBestShopMethod.invoke(testProcessor, ctx, relatedShops);
        // assert
        verify(dealGroupWrapper, never()).getFutureResult(bestShopFuture);
        verify(ctx).setBestShopResp(any(BestShopDTO.class));
        verify(ctx).setNeedFillBestShop(true);
    }
}
