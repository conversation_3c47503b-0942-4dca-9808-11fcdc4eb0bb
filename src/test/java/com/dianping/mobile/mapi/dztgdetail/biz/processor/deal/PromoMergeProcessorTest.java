package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PromoMergeProcessorTest {

    private PromoMergeProcessor processor;

    @InjectMocks
    private PromoMergeProcessor promoMergeProcessor;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private EnvCtx envCtx;

    @Before
    public void setUp() {
        processor = new PromoMergeProcessor();
    }

    private void invokeTransfer(Map<String, PromoDisplayDTO> reduceMap, List<PromoDisplayDTO> promoList) {
        try {
            Method transferMethod = PromoMergeProcessor.class.getDeclaredMethod("transfer", Map.class, List.class);
            transferMethod.setAccessible(true);
            transferMethod.invoke(processor, reduceMap, promoList);
        } catch (Exception e) {
            throw new RuntimeException("Failed to invoke private transfer method", e);
        }
    }

    /**
     * Helper method to invoke the private genTimeStamp method using reflection
     */
    private String invokePrivateGenTimeStamp() throws Exception {
        Method method = PromoMergeProcessor.class.getDeclaredMethod("genTimeStamp");
        method.setAccessible(true);
        return (String) method.invoke(promoMergeProcessor);
    }

    /**
     * Test case: transfer with null promoList
     * Expected: reduceMap should remain unchanged
     */
    @Test
    public void testTransfer_WithNullPromoList() throws Throwable {
        // arrange
        Map<String, PromoDisplayDTO> reduceMap = new HashMap<>();
        List<PromoDisplayDTO> promoList = null;
        // act
        invokeTransfer(reduceMap, promoList);
        // assert
        assertTrue(reduceMap.isEmpty());
    }

    /**
     * Test case: transfer with empty promoList
     * Expected: reduceMap should remain unchanged
     */
    @Test
    public void testTransfer_WithEmptyPromoList() throws Throwable {
        // arrange
        Map<String, PromoDisplayDTO> reduceMap = new HashMap<>();
        List<PromoDisplayDTO> promoList = new ArrayList<>();
        // act
        invokeTransfer(reduceMap, promoList);
        // assert
        assertTrue(reduceMap.isEmpty());
    }

    /**
     * Test case: transfer with single item in promoList
     * Expected: reduceMap should contain one item with ruleId as key
     */
    @Test
    public void testTransfer_WithSinglePromo() throws Throwable {
        // arrange
        Map<String, PromoDisplayDTO> reduceMap = new HashMap<>();
        List<PromoDisplayDTO> promoList = new ArrayList<>();
        PromoDisplayDTO promo = new PromoDisplayDTO();
        promo.setRuleId(123);
        promoList.add(promo);
        // act
        invokeTransfer(reduceMap, promoList);
        // assert
        assertEquals(1, reduceMap.size());
        assertSame(promo, reduceMap.get("123"));
    }

    /**
     * Test case: transfer with multiple items in promoList
     * Expected: reduceMap should contain all items with respective ruleIds as keys
     */
    @Test
    public void testTransfer_WithMultiplePromos() throws Throwable {
        // arrange
        Map<String, PromoDisplayDTO> reduceMap = new HashMap<>();
        List<PromoDisplayDTO> promoList = new ArrayList<>();
        PromoDisplayDTO promo1 = new PromoDisplayDTO();
        promo1.setRuleId(123);
        PromoDisplayDTO promo2 = new PromoDisplayDTO();
        promo2.setRuleId(456);
        promoList.add(promo1);
        promoList.add(promo2);
        // act
        invokeTransfer(reduceMap, promoList);
        // assert
        assertEquals(2, reduceMap.size());
        assertSame(promo1, reduceMap.get("123"));
        assertSame(promo2, reduceMap.get("456"));
    }

    /**
     * Test case: transfer with duplicate ruleIds in promoList
     * Expected: reduceMap should contain latest item for duplicate ruleId
     */
    @Test
    public void testTransfer_WithDuplicateRuleIds() throws Throwable {
        // arrange
        Map<String, PromoDisplayDTO> reduceMap = new HashMap<>();
        List<PromoDisplayDTO> promoList = new ArrayList<>();
        PromoDisplayDTO promo1 = new PromoDisplayDTO();
        promo1.setRuleId(123);
        PromoDisplayDTO promo2 = new PromoDisplayDTO();
        // Same ruleId
        promo2.setRuleId(123);
        promoList.add(promo1);
        promoList.add(promo2);
        // act
        invokeTransfer(reduceMap, promoList);
        // assert
        assertEquals(1, reduceMap.size());
        // Should contain the last added promo
        assertSame(promo2, reduceMap.get("123"));
    }

    /**
     * Test case: transfer with existing items in reduceMap
     * Expected: reduceMap should be updated with new items, overwriting existing ones if same ruleId
     */
    @Test
    public void testTransfer_WithExistingItemsInReduceMap() throws Throwable {
        // arrange
        Map<String, PromoDisplayDTO> reduceMap = new HashMap<>();
        List<PromoDisplayDTO> promoList = new ArrayList<>();
        // Existing item in reduceMap
        PromoDisplayDTO existingPromo = new PromoDisplayDTO();
        existingPromo.setRuleId(123);
        reduceMap.put("123", existingPromo);
        // New item in promoList
        PromoDisplayDTO newPromo = new PromoDisplayDTO();
        newPromo.setRuleId(123);
        promoList.add(newPromo);
        // act
        invokeTransfer(reduceMap, promoList);
        // assert
        assertEquals(1, reduceMap.size());
        // Should be replaced with new promo
        assertSame(newPromo, reduceMap.get("123"));
    }

    /**
     * Test isEnable when it is a main app
     */
    @Test
    public void testIsEnable_WhenMainApp_ShouldReturnTrue() {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.isMainApp()).thenReturn(true);
        // act
        boolean result = promoMergeProcessor.isEnable(dealCtx);
        // assert
        assertTrue(result);
    }

    /**
     * Test isEnable when it is not a main app
     */
    @Test
    public void testIsEnable_WhenNotMainApp_ShouldReturnFalse() {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.isMainApp()).thenReturn(false);
        // act
        boolean result = promoMergeProcessor.isEnable(dealCtx);
        // assert
        assertFalse(result);
    }

    /**
     * Test isEnable when DealCtx is null
     */
    @Test(expected = NullPointerException.class)
    public void testIsEnable_WhenDealCtxNull_ShouldThrowException() {
        // act
        promoMergeProcessor.isEnable(null);
    }

    /**
     * Test isEnable when EnvCtx is null
     */
    @Test(expected = NullPointerException.class)
    public void testIsEnable_WhenEnvCtxNull_ShouldThrowException() {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(null);
        // act
        promoMergeProcessor.isEnable(dealCtx);
    }

    /**
     * Test that generated timestamp has correct format and length
     */
    @Test
    public void testGenTimeStamp_VerifyFormat() throws Throwable {
        // arrange
        // 16 digits for timestamp + decimal number for random part
        String timestampPattern = "\\d{16}\\d+\\.\\d+";
        // act
        String result = invokePrivateGenTimeStamp();
        // assert
        assertTrue("Generated timestamp should match expected pattern", Pattern.matches(timestampPattern, result));
    }

    /**
     * Test multiple calls return different values
     */
    @Test
    public void testGenTimeStamp_VerifyUniqueness() throws Throwable {
        // arrange & act
        String result1 = invokePrivateGenTimeStamp();
        // Ensure different timestamp
        Thread.sleep(1);
        String result2 = invokePrivateGenTimeStamp();
        // assert
        assertNotEquals("Consecutive calls should return different values", result1, result2);
    }
}
