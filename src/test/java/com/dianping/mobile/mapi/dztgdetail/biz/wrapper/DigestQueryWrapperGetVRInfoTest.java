package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.DigestConfinementVrDTO;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.digest.DigestInfoItemDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.digest.QueryDigestResponseDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.digest.internal.QueryDigestResponseReader;
import java.util.concurrent.Future;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ AttributeUtils.class, QueryDigestResponseReader.class, JSON.class, Cat.class, StringUtils.class })
public class DigestQueryWrapperGetVRInfoTest {

    @Mock
    private Future future;

    @Mock
    private DealCtx ctx;

    @Mock
    private DealGroupDTO dealGroupDTO;

    @Mock
    private QueryDigestResponseDTO responseDTO;

    @Mock
    private DigestInfoItemDTO digestInfoItemDTO;

    @Mock
    private QueryDigestResponseReader reader;

    private DigestQueryWrapper wrapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // Mock StringUtils.isBlank to use real implementation
        PowerMockito.mockStatic(StringUtils.class);
        PowerMockito.when(StringUtils.isBlank(anyString())).thenAnswer(invocation -> {
            String arg = invocation.getArgument(0);
            return arg == null || arg.trim().isEmpty();
        });
        // Create a test wrapper with overridden getFutureResult
        wrapper = new DigestQueryWrapper() {

            @Override
            public <T> T getFutureResult(Future serviceFuture) {
                if (serviceFuture == null) {
                    return null;
                }
                return (T) responseDTO;
            }
        };
    }

    /**
     * Test that null future returns null
     */
    @Test
    public void testGetVRInfoNullFutureReturnsNull() throws Throwable {
        // act
        DigestConfinementVrDTO result = wrapper.getVRInfo(null, ctx);
        // assert
        assertNull(result);
    }

    /**
     * Test that null responseDTO returns null
     */
    @Test
    public void testGetVRInfoNullResponseDTOReturnsNull() throws Throwable {
        // arrange
        DigestQueryWrapper nullResponseWrapper = new DigestQueryWrapper() {

            @Override
            public <T> T getFutureResult(Future serviceFuture) {
                return null;
            }
        };
        // act
        DigestConfinementVrDTO result = nullResponseWrapper.getVRInfo(future, ctx);
        // assert
        assertNull(result);
    }

    /**
     * Test that non-200 response code returns null
     */
    @Test
    public void testGetVRInfoNon200ResponseCodeReturnsNull() throws Throwable {
        // arrange
        // Non-200 status code
        when(responseDTO.getCode()).thenReturn(500);
        // act
        DigestConfinementVrDTO result = wrapper.getVRInfo(future, ctx);
        // assert
        assertNull(result);
    }

    /**
     * Test that null dealGroupDTO returns null
     */
    @Test
    public void testGetVRInfoNullDealGroupDTOReturnsNull() throws Throwable {
        // arrange
        when(responseDTO.getCode()).thenReturn(200);
        when(ctx.getDealGroupDTO()).thenReturn(null);
        // act
        DigestConfinementVrDTO result = wrapper.getVRInfo(future, ctx);
        // assert
        assertNull(result);
    }


}
