package com.dianping.mobile.mapi.dztgdetail.biz.service.recommend;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.dianping.lion.client.util.CollectionUtils;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.CrossCatRecommendServiceWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DzGeneralProductWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.service.RelatedRecommendService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiClientWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.model.RecommendUserBaseInfoModel;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.RelatedRecommendCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedRecommendReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RecommendItemDTO;
import com.dianping.mobile.mapi.dztgdetail.entity.LeCrossRecommendConfig;
import com.sankuai.dztheme.spuproduct.req.SpuRequest;
import com.sankuai.dztheme.spuproduct.res.ApplyShopDTO;
import com.sankuai.dztheme.spuproduct.res.SaleDTO;
import com.sankuai.dztheme.spuproduct.res.SpuDTO;
import com.sankuai.dztheme.spuproduct.res.SpuResult;
import com.sankuai.fbi.faas.wed.dto.ServiceResponse;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.junit.runner.RunWith;
import org.junit.Test;
import com.dianping.lion.Environment;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import java.util.ArrayList;
import java.util.concurrent.Future;
import com.dianping.mobile.mapi.dztgdetail.entity.ExpResultConfig;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import com.dianping.mobile.mapi.dztgdetail.common.model.RecommendShopBaseInfoModel;
import com.dianping.mobile.mapi.dztgdetail.common.model.RecommendUserBaseInfoModel;

@RunWith(MockitoJUnitRunner.class)
public class LeCrossRecommendHandlerTest {

    @InjectMocks
    private LeCrossRecommendHandler leCrossRecommendHandler;
    @Mock
    private DouHuBiz douHuBiz;
    @Mock
    private PoiClientWrapper poiClientWrapper;
    @Mock
    private DzGeneralProductWrapper dzGeneralProductWrapper;
    @Mock
    private CrossCatRecommendServiceWrapper crossCatRecommendServiceWrapper;
    @Mock
    private Future future;


    private MockedStatic<Environment> environmentMockedStatic;

//    private MockedStatic<Lion> mockedLion;
//
//
//    @Mock
//    private Lion lionMock;
//
//    @Mock
//    private RelatedRecommendCtx ctx;
//
//    @Mock
//    private LeCrossRecommendConfig config;
//
//    @Mock
//    private RecommendItemDTO recommendItemDTO;


    @Before
    public void setUp() {
        environmentMockedStatic = Mockito.mockStatic(Environment.class);
        MockitoAnnotations.openMocks(this);
    }

    @After
    public void tearDown() {
        environmentMockedStatic.close();
    }




    @Test
    public void test_getSpuDto() {
        SpuResult spuResult = getSpuResult();
        spuResult.setErrorMsg("error");
        List<SpuDTO> spuDTOList = new ArrayList<>();
        SpuDTO spuDTO = new SpuDTO();
        spuDTO.setHeadPic("http://example.com/image.jpg");
        spuDTOList.add(spuDTO);
        spuResult.setSpuList(spuDTOList);
        Mockito.when(dzGeneralProductWrapper.getFutureResult(future)).thenReturn(spuResult);
        SpuDTO spuDTO1 = leCrossRecommendHandler.getSpuDto(future);
        Assert.assertNotNull(spuDTO1);
    }

    @Test
    public void test_getCrossRecommendTittle() {
        LeCrossRecommendConfig config = getConfig();
        ServiceResponse<List<String>> serviceResponse = new ServiceResponse<>();
        serviceResponse.setContent(Arrays.asList("test"));
        serviceResponse.isSuccess();
        Mockito.when(dzGeneralProductWrapper.getFutureResult(future)).thenReturn(serviceResponse);
        String str = leCrossRecommendHandler.getCrossRecommendTittle(future, config);
        Assert.assertNotNull(str);
        Assert.assertEquals("不限科目4节课.可选test等", str);
    }

    @Test
    public void test_buildSpuRequest() {
        // 创建必要的mock对象
        RelatedRecommendCtx ctx = Mockito.mock(RelatedRecommendCtx.class);
        EnvCtx envCtx = Mockito.mock(EnvCtx.class);
        RelatedRecommendReq req = Mockito.mock(RelatedRecommendReq.class);
        RecommendUserBaseInfoModel userBaseInfoModel = Mockito.mock(RecommendUserBaseInfoModel.class);
        DpPoiDTO dpPoiDTO = Mockito.mock(DpPoiDTO.class);
        MtPoiDTO mtPoiDTO = Mockito.mock(MtPoiDTO.class);

        // 设置mock对象的行为
        Mockito.when(ctx.getEnvCtx()).thenReturn(envCtx);
        Mockito.when(ctx.getReq()).thenReturn(req);
        Mockito.when(ctx.getRecommendUserBaseInfoModel()).thenReturn(userBaseInfoModel);
//        Mockito.when(ctx.getDpPoiDTO()).thenReturn(dpPoiDTO);
//        Mockito.when(dpPoiDTO.getShopId()).thenReturn(123L);
//        Mockito.when(poiClientWrapper.getMtPoiDTO(Mockito.anyLong(), Mockito.anyList())).thenReturn(mtPoiDTO);

        // 设置其他必要的mock行为
        Mockito.when(envCtx.getVersion()).thenReturn("1.0");
        Mockito.when(envCtx.isMt()).thenReturn(true);
        Mockito.when(req.getUserLat()).thenReturn(30.0);
        Mockito.when(req.getUserLng()).thenReturn(120.0);
        Mockito.when(envCtx.getMtUserId()).thenReturn(1L);
        Mockito.when(envCtx.getDpUserId()).thenReturn(2L);
        Mockito.when(envCtx.getUnionId()).thenReturn("unionId");
        Mockito.when(envCtx.getUuid()).thenReturn("uuid");
        Mockito.when(req.getCityId()).thenReturn(10);
        Mockito.when(userBaseInfoModel.getMtCityId()).thenReturn(10);
        Mockito.when(userBaseInfoModel.getDpCityId()).thenReturn(10);
        // 创建配置对象
        LeCrossRecommendConfig config = getConfig();
        // 调用被测试的方法
        SpuRequest result = leCrossRecommendHandler.buildSpuRequest(ctx, config);
        // 验证结果
        Assert.assertNotNull(result);
        // 添加更多的断言来验证结果的正确性
    }


    @Test
    public void test_buildExtParams() {
        // 创建必要的mock对象
        RelatedRecommendCtx ctx = Mockito.mock(RelatedRecommendCtx.class);
        EnvCtx envCtx = Mockito.mock(EnvCtx.class);
        RelatedRecommendReq req = Mockito.mock(RelatedRecommendReq.class);
        RecommendUserBaseInfoModel userBaseInfoModel = Mockito.mock(RecommendUserBaseInfoModel.class);
        DpPoiDTO dpPoiDTO = Mockito.mock(DpPoiDTO.class);
        MtPoiDTO mtPoiDTO = Mockito.mock(MtPoiDTO.class);

        // 设置mock对象的行为
        Mockito.when(ctx.getEnvCtx()).thenReturn(envCtx);
        Mockito.when(ctx.getReq()).thenReturn(req);
        Mockito.when(ctx.getRecommendUserBaseInfoModel()).thenReturn(userBaseInfoModel);
//        Mockito.when(ctx.getDpPoiDTO()).thenReturn(dpPoiDTO);
//        Mockito.when(dpPoiDTO.getShopId()).thenReturn(123L);
//        Mockito.when(poiClientWrapper.getMtPoiDTO(Mockito.anyLong(), Mockito.anyList())).thenReturn(mtPoiDTO);

        // 设置其他必要的mock行为
        Mockito.when(envCtx.getVersion()).thenReturn("1.0");
        Mockito.when(envCtx.isMt()).thenReturn(true);
        Mockito.when(req.getUserLat()).thenReturn(30.0);
        Mockito.when(req.getUserLng()).thenReturn(120.0);
        Mockito.when(envCtx.getMtUserId()).thenReturn(1L);
        Mockito.when(envCtx.getDpUserId()).thenReturn(2L);
        Mockito.when(envCtx.getUnionId()).thenReturn("unionId");
        Mockito.when(envCtx.getUuid()).thenReturn("uuid");
        Mockito.when(req.getCityId()).thenReturn(10);
        Mockito.when(userBaseInfoModel.getMtCityId()).thenReturn(10);
        Mockito.when(userBaseInfoModel.getDpCityId()).thenReturn(10);
        // 创建配置对象
        LeCrossRecommendConfig config = getConfig();
        // 调用被测试的方法
        Map<String, Object> result = leCrossRecommendHandler.buildExtParams(ctx, config);
        // 验证结果
        Assert.assertNotNull(result);
        // 添加更多的断言来验证结果的正确性
    }


    @Test
    public void test_buildLeCrossRecommend() {
        SpuDTO spuDTO = getSpuDTO();
        ApplyShopDTO applyShopDTO = new ApplyShopDTO();
        applyShopDTO.setNearestShopDesc("1000+门店通用");
        spuDTO.setApplyShops(applyShopDTO);
        SaleDTO saleDTO = new SaleDTO();
        saleDTO.setSaleDesc("1000+人已订");
        spuDTO.setSales(saleDTO);
        RecommendItemDTO recommendItemDTO = leCrossRecommendHandler.buildLeCrossRecommend("不限科目4节课.可选音乐/美术/科学探索等", spuDTO, getConfig());
        Assert.assertNotNull(recommendItemDTO);
    }

    @Test
    public void test_buildLeCrossRecommend_IsNull() {
        SpuDTO spuDTO = getSpuDTO();
        ApplyShopDTO applyShopDTO = new ApplyShopDTO();
        applyShopDTO.setNearestShopDesc("1000+门店通用");
        spuDTO.setApplyShops(applyShopDTO);
        SaleDTO saleDTO = new SaleDTO();
        saleDTO.setSaleDesc("1000+人已订");
        spuDTO.setSales(saleDTO);
        RecommendItemDTO recommendItemDTO = leCrossRecommendHandler.buildLeCrossRecommend("", spuDTO, getConfig());
        Assert.assertNull(recommendItemDTO);
    }


    @Test
    public void test_shopNum2String_1() {
        String string = leCrossRecommendHandler.shopNum2String(1);
        Assert.assertEquals(string, "1家门店通用");
    }

    @Test
    public void test_shopNum2String_51() {
        String string = leCrossRecommendHandler.shopNum2String(51);
        Assert.assertEquals(string, "50+门店通用");
    }


    @Test
    public void test_shopNum2String_200() {
        String string = leCrossRecommendHandler.shopNum2String(210);
        Assert.assertEquals(string, "200+门店通用");
    }


    @Test
    public void test_shopNum2String_1200() {
        String string = leCrossRecommendHandler.shopNum2String(1210);
        Assert.assertEquals(string, "1000+门店通用");
    }

    @Test
    public void test_shopNum2String_12000() {
        String string = leCrossRecommendHandler.shopNum2String(10210);
        Assert.assertEquals(string, "1.0万+门店通用");
    }


    @Test
    public void test_getPlatformTypeByUserSource_isMt() {
        // 创建一个 EnvCtx 的 mock 对象
        EnvCtx envCtx = Mockito.mock(EnvCtx.class);
        Mockito.when(envCtx.isMt()).thenReturn(true);
        int num = leCrossRecommendHandler.getPlatformTypeByUserSource(envCtx);
        Assert.assertEquals(num, 200);
    }

    @Test
    public void test_getPlatformTypeByUserSource_isDp() {
        // 创建一个 EnvCtx 的 mock 对象
        EnvCtx envCtx = Mockito.mock(EnvCtx.class);
        Mockito.when(envCtx.isDp()).thenReturn(true);
        int num = leCrossRecommendHandler.getPlatformTypeByUserSource(envCtx);
        Assert.assertEquals(num, 100);
    }

    @Test
    public void test_getPlatformTypeByUserSource_zero() {
        // 创建一个 EnvCtx 的 mock 对象
        EnvCtx envCtx = Mockito.mock(EnvCtx.class);
        Mockito.when(envCtx.isMt()).thenReturn(false);
        int num = leCrossRecommendHandler.getPlatformTypeByUserSource(envCtx);
        Assert.assertEquals(num, 0);
    }

    public static LeCrossRecommendConfig getConfig() {
        String str = "{\n" +
                "    \"enable\": true,\n" +
                "    \"categoryIds\": [\n" +
                "        1835,\n" +
                "        1836,\n" +
                "        2311,\n" +
                "        2497,\n" +
                "        2564,\n" +
                "        2570,\n" +
                "        2579,\n" +
                "        2572,\n" +
                "        2657,\n" +
                "        2560,\n" +
                "        2557,\n" +
                "        2558,\n" +
                "        2562,\n" +
                "        162,\n" +
                "        2917,\n" +
                "        78,\n" +
                "        2658,\n" +
                "        1211\n" +
                "    ],\n" +
                "    \"prefix\": \"不限科目4节课.可选\",\n" +
                "    \"suffix\": \"等\",\n" +
                "    \"planId\": \"11300080\",\n" +
                "    \"spuIds\": [\n" +
                "        {\n" +
                "            \"id\": 114566210,\n" +
                "            \"idType\": 16\n" +
                "        }\n" +
                "    ],\n" +
                "    \"enableDouHu\": true,\n" +
                "    \"mtDouHuKey\": \"MtLeCrossRecommendExp\",\n" +
                "    \"dpDouHuKey\": \"DpLeCrossRecommendExp\",\n" +
                "    \"passParam\": \"%7B%22DISTRIBUTION_BASIC_INFO%22:%22%7B%5C%22distributionInfoList%5C%22:%5B%7B%5C%22distributionType%5C%22:%5C%22LEADS_RECOMMEND%5C%22,%5C%22sceneId%5C%22:%5C%22ProductPage%5C%22%7D%5D%7D%22%7D\",\n" +
                "    \"pageSource\": \"leProductPage\"\n" +
                "}";
        LeCrossRecommendConfig config = JSON.parseObject(str, LeCrossRecommendConfig.class);
        return config;
    }

    public static List<RecommendItemDTO> getRecommendList() {
        String str = "[\n" +
                "    {\n" +
                "        \"dealPBO\": {\n" +
                "            \"dealContents\": [\n" +
                "                {\n" +
                "                    \"content\": \"https://p0.meituan.net/travelcube/761530f8b4ff4cbbbb7967b2ce4b3e76229337.jpg\",\n" +
                "                    \"type\": 1\n" +
                "                }\n" +
                "            ],\n" +
                "            \"detailUrl\": \"imeituan://www.meituan.com/gc/deal/detail?source=deal_detail&did=1033169345&poiid=*********\",\n" +
                "            \"dpId\": 0,\n" +
                "            \"itemTextInfos\": [\n" +
                "                \n" +
                "            ],\n" +
                "            \"mtId\": 1033169345,\n" +
                "            \"promoDetailModule\": {\n" +
                "                \"expressOptimize\": false,\n" +
                "                \"marketPrice\": \"128\",\n" +
                "                \"marketPromoDiscount\": \"6.9折\",\n" +
                "                \"plainMarketPromoDiscount\": \"6.9折\",\n" +
                "                \"priceDisplayType\": 0,\n" +
                "                \"promoNewStyle\": false,\n" +
                "                \"promoPrice\": \"88\",\n" +
                "                \"showBestPromoDetails\": false,\n" +
                "                \"showMarketPrice\": false,\n" +
                "                \"showPriceCompareEntrance\": false\n" +
                "            },\n" +
                "            \"shop\": {\n" +
                "                \"buyBarIconType\": 0,\n" +
                "                \"displayPosition\": 1,\n" +
                "                \"hideAddrEnable\": false,\n" +
                "                \"hideStars\": false,\n" +
                "                \"lat\": 0.0,\n" +
                "                \"lng\": 0.0,\n" +
                "                \"lyyShop\": false,\n" +
                "                \"shopBizType\": 0,\n" +
                "                \"shopCategoryId\": 0,\n" +
                "                \"shopId\": *********,\n" +
                "                \"shopNum\": 0,\n" +
                "                \"shopPower\": 0,\n" +
                "                \"shopType\": 0\n" +
                "            },\n" +
                "            \"title\": \"纯色美甲\"\n" +
                "        },\n" +
                "        \"itemType\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"dealPBO\": {\n" +
                "            \"dealContents\": [\n" +
                "                {\n" +
                "                    \"content\": \"https://p0.inf.test.sankuai.com/dpmerchantpic/6be573d85903c56b7326a91c0382e6d4166669.jpg\",\n" +
                "                    \"type\": 1\n" +
                "                }\n" +
                "            ],\n" +
                "            \"detailUrl\": \"imeituan://www.meituan.com/gc/deal/detail?source=deal_detail&did=1033046727&poiid=607032354\",\n" +
                "            \"dpId\": 0,\n" +
                "            \"itemTextInfos\": [\n" +
                "                \n" +
                "            ],\n" +
                "            \"mtId\": 1033046727,\n" +
                "            \"promoDetailModule\": {\n" +
                "                \"expressOptimize\": false,\n" +
                "                \"marketPrice\": \"100\",\n" +
                "                \"marketPromoDiscount\": \"3.0折\",\n" +
                "                \"plainMarketPromoDiscount\": \"3.0折\",\n" +
                "                \"priceDisplayType\": 0,\n" +
                "                \"promoNewStyle\": false,\n" +
                "                \"promoPrice\": \"30\",\n" +
                "                \"showBestPromoDetails\": false,\n" +
                "                \"showMarketPrice\": false,\n" +
                "                \"showPriceCompareEntrance\": false\n" +
                "            },\n" +
                "            \"shop\": {\n" +
                "                \"buyBarIconType\": 0,\n" +
                "                \"displayPosition\": 1,\n" +
                "                \"hideAddrEnable\": false,\n" +
                "                \"hideStars\": false,\n" +
                "                \"lat\": 0.0,\n" +
                "                \"lng\": 0.0,\n" +
                "                \"lyyShop\": false,\n" +
                "                \"shopBizType\": 0,\n" +
                "                \"shopCategoryId\": 0,\n" +
                "                \"shopId\": 607032354,\n" +
                "                \"shopNum\": 0,\n" +
                "                \"shopPower\": 0,\n" +
                "                \"shopType\": 0\n" +
                "            },\n" +
                "            \"title\": \"xxx｜33分钟足疗\"\n" +
                "        },\n" +
                "        \"itemType\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"itemType\": 2,\n" +
                "        \"shopPBO\": {\n" +
                "            \"avgPrice\": \"\",\n" +
                "            \"distanceDesc\": \"距您15km\",\n" +
                "            \"fiveScore\": \"\",\n" +
                "            \"itemTextInfos\": [\n" +
                "                \n" +
                "            ],\n" +
                "            \"recommendInfo\": {\n" +
                "                \"recommendSource\": 0\n" +
                "            },\n" +
                "            \"regionDesc\": \"北新泾/淞虹路\",\n" +
                "            \"shopId\": *********,\n" +
                "            \"shopName\": \"供应链自动化专用-丽人美甲门店\",\n" +
                "            \"shopPic\": \"http://p1.meituan.net/searchscenerec/92e8bc86c05b07e3b0bbd85a83d5206c279007.png%40300w_225h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\",\n" +
                "            \"shopPower\": 0,\n" +
                "            \"shopUrl\": \"imeituan://www.meituan.com/gc/poi/detail?id=*********&mmcinflate=&mmcuse=&mmcbuy=&mmcfree=&channelType=\"\n" +
                "        }\n" +
                "    },\n" +
                "    {\n" +
                "        \"itemType\": 2,\n" +
                "        \"shopPBO\": {\n" +
                "            \"avgPrice\": \"\",\n" +
                "            \"distanceDesc\": \"距您16km\",\n" +
                "            \"fiveScore\": \"\",\n" +
                "            \"itemTextInfos\": [\n" +
                "                \n" +
                "            ],\n" +
                "            \"recommendInfo\": {\n" +
                "                \"recommendSource\": 0\n" +
                "            },\n" +
                "            \"regionDesc\": \"虹桥火车站/机场\",\n" +
                "            \"shopId\": 607032354,\n" +
                "            \"shopName\": \"KTV新版上单门店\",\n" +
                "            \"shopPic\": \"http://p1.meituan.net/searchscenerec/a1f99544c203750af25ccf87395cfb49400458.png%40300w_225h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\",\n" +
                "            \"shopPower\": 0,\n" +
                "            \"shopUrl\": \"imeituan://www.meituan.com/gc/poi/detail?id=607032354&mmcinflate=&mmcuse=&mmcbuy=&mmcfree=&channelType=\"\n" +
                "        }\n" +
                "    }\n" +
                "]";

        List<RecommendItemDTO> recommendItemList = JSON.parseArray(str, RecommendItemDTO.class);
        return recommendItemList;
    }


    public static SpuDTO getSpuDTO() {
        String str = "{\n" +
                "    \"applyShops\": {\n" +
                "        \"applyShopDesc\": \"\",\n" +
                "        \"applyShopNum\": 2,\n" +
                "        \"lat\": 0.0,\n" +
                "        \"lng\": 0.0,\n" +
                "        \"nearestShopCityId\": 0,\n" +
                "        \"nearestShopDistance\": 0.0,\n" +
                "        \"nearestShopId\": 0\n" +
                "    },\n" +
                "    \"available\": false,\n" +
                "    \"deals\": [\n" +
                "        \n" +
                "    ],\n" +
                "    \"enterpriseWx\": {\n" +
                "        \"showResource\": false\n" +
                "    },\n" +
                "    \"headPic\": \"https://p0.inf.test.sankuai.com/ktv/b3acc9b9-8f35-4784-9fab-b55542a4863c.jpeg\",\n" +
                "    \"jumpUrl\": \"imeituan://www.meituan.com/web?url=https%3A%2F%2Ftest-g.meituan.com%2Fcsr%2Fdz-le-web-static%2Fedu-standard-product.html%3Flongproductid%3D6980593%26shopid%3D%26shopuuid%3D%26categoryid%3D822300048%26scenecode%3Dquality_education_class_spu_detail%26cityid%3D10%26source%3D%257B%2522DISTRIBUTION_BASIC_INFO%2522%3A%2522%257B%255C%2522distributionInfoList%255C%2522%3A%255B%257B%255C%2522distributionType%255C%2522%3A%255C%2522LEADS_RECOMMEND%255C%2522%2C%255C%2522sceneId%255C%2522%3A%255C%2522ProductPage%255C%2522%257D%255D%257D%2522%257D\",\n" +
                "    \"orderUrl\": \"imeituan://www.meituan.com/web?url=https%3A%2F%2Fm.51ping.com%2Fifuse%2Fredirect%3Fscenename%3DHJBZb9Cpz%26productitemid%3D260627918%26bizCode%3Dnib.promotion.card.general%26cityid%3D10%26source%3D%257B%2522DISTRIBUTION_BASIC_INFO%2522%3A%2522%257B%255C%2522distributionInfoList%255C%2522%3A%255B%257B%255C%2522distributionType%255C%2522%3A%255C%2522LEADS_RECOMMEND%255C%2522%2C%255C%2522sceneId%255C%2522%3A%255C%2522ProductPage%255C%2522%257D%255D%257D%2522%257D%26pass_param%3DleProductPage\",\n" +
                "    \"sales\": {\n" +
                "        \"saleCount\": 0,\n" +
                "        \"saleDesc\": \"\"\n" +
                "    },\n" +
                "    \"spuAttrs\": [\n" +
                "        {\n" +
                "            \"name\": \"attr_fix_price_spu_assist_desc\",\n" +
                "            \"value\": \"\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"name\": \"attr_product_class_num_desc\",\n" +
                "            \"value\": \"4-4课时多次体验\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"name\": \"attr_product_subject_num_desc\",\n" +
                "            \"value\": \"30+科目可选\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"name\": \"attr_spu_usedays_after_order\",\n" +
                "            \"value\": \"购买后10天内有效，可至以下任一门店核销并选择一门课程学习，且仅限门店新客学习\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"name\": \"attr_spu_sale_status\",\n" +
                "            \"value\": \"\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"spuId\": {\n" +
                "        \"id\": 6980593,\n" +
                "        \"idType\": 16\n" +
                "    },\n" +
                "    \"spuName\": \"兴趣探索·不限科目4次课\",\n" +
                "    \"spuPrice\": {\n" +
                "        \n" +
                "    },\n" +
                "    \"stylePictures\": [\n" +
                "        \n" +
                "    ]\n" +
                "}";
        return JSON.parseObject(str, SpuDTO.class);
    }

    public static SpuResult getSpuResult() {
        String str = "{\n" +
                "  \"spuResult\": {\n" +
                "    \"spuList\": [\n" +
                "      {\n" +
                "        \"spuId\": {\n" +
                "          \"id\": \"12345\"\n" +
                "        },\n" +
                "        \"spuName\": \"Sample Product\",\n" +
                "        \"spuPrice\": {\n" +
                "          \"amount\": 99.99,\n" +
                "          \"currency\": \"USD\"\n" +
                "        },\n" +
                "        \"headPic\": \"http://example.com/image.jpg\",\n" +
                "        \"spuDesc\": \"This is a sample product description.\",\n" +
                "        \"spuTags\": [\"tag1\", \"tag2\"],\n" +
                "        \"imgList\": [\n" +
                "          {\n" +
                "            \"url\": \"http://example.com/image1.jpg\",\n" +
                "            \"type\": \"image\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"url\": \"http://example.com/image2.jpg\",\n" +
                "            \"type\": \"image\"\n" +
                "          }\n" +
                "        ],\n" +
                "        \"jumpUrl\": \"http://example.com/jump\",\n" +
                "        \"orderUrl\": \"http://example.com/order\",\n" +
                "        \"sales\": {\n" +
                "          \"volume\": 1000\n" +
                "        },\n" +
                "        \"applyShops\": {\n" +
                "          \"shopId\": \"shop123\"\n" +
                "        },\n" +
                "        \"pinInfo\": {\n" +
                "          \"pinId\": \"pin123\"\n" +
                "        },\n" +
                "        \"reviewInfo\": {\n" +
                "          \"rating\": 4.5\n" +
                "        },\n" +
                "        \"rankInfo\": {\n" +
                "          \"rank\": 1\n" +
                "        },\n" +
                "        \"supplyInfo\": {\n" +
                "          \"supplier\": \"Supplier Name\"\n" +
                "        },\n" +
                "        \"available\": true,\n" +
                "        \"profilePoint\": {\n" +
                "          \"point\": \"Special Offer\"\n" +
                "        },\n" +
                "        \"deals\": [\n" +
                "          {\n" +
                "            \"dealId\": \"deal123\",\n" +
                "            \"dealName\": \"Special Deal\"\n" +
                "          }\n" +
                "        ],\n" +
                "        \"spuAttrs\": [\n" +
                "          {\n" +
                "            \"attrName\": \"Color\",\n" +
                "            \"attrValue\": \"Red\"\n" +
                "          }\n" +
                "        ],\n" +
                "        \"stylePictures\": [\"style1.jpg\", \"style2.jpg\"],\n" +
                "        \"enterpriseWx\": {\n" +
                "          \"wxId\": \"wx123\"\n" +
                "        }\n" +
                "      }\n" +
                "    ],\n" +
                "    \"errorMsg\": null\n" +
                "  }\n" +
                "}\n";
        return JSON.parseObject(str, SpuResult.class);
    }
}
