package com.dianping.mobile.mapi.dztgdetail.util;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDisplayShopDTO;
import java.util.Arrays;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;
import com.sankuai.general.product.query.center.client.dto.DealGroupDisplayShopCheckDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupShopRelationCheckResultDTO;
import java.util.ArrayList;
import org.junit.Test;

@RunWith(MockitoJUnitRunner.class)
public class DealProductUtilsTest {

    @InjectMocks
    private DealProductUtils dealProductUtils;

    @Mock
    private DealGroupDTO dealGroupDTO;

    @Mock
    private DealGroupDisplayShopDTO displayShopInfo;

    @Mock
    private DealGroupDisplayShopCheckDTO displayShopCheckDTO;

    /**
     * 测试 getDpDisplayShopIds 方法，当 dealGroupDTO 为 null 时
     */
    @Test
    public void testGetDpDisplayShopIdsWhenDealGroupDTOIsNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = null;
        // act
        List<Long> result = DealProductUtils.getDpDisplayShopIds(dealGroupDTO);
        // assert
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    /**
     * 测试 getDpDisplayShopIds 方法，当 displayShopInfo 为 null 时
     */
    @Test
    public void testGetDpDisplayShopIdsWhenDisplayShopInfoIsNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDisplayShopInfo(null);
        // act
        List<Long> result = DealProductUtils.getDpDisplayShopIds(dealGroupDTO);
        // assert
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    /**
     * 测试 getDpDisplayShopIds 方法，当 dpDisplayShopIds 为 null 时
     */
    @Test
    public void testGetDpDisplayShopIdsWhenDpDisplayShopIdsIsNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupDisplayShopDTO displayShopInfo = new DealGroupDisplayShopDTO();
        displayShopInfo.setDpDisplayShopIds(null);
        dealGroupDTO.setDisplayShopInfo(displayShopInfo);
        // act
        List<Long> result = DealProductUtils.getDpDisplayShopIds(dealGroupDTO);
        // assert
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    /**
     * 测试 getDpDisplayShopIds 方法，当 dpDisplayShopIds 为空列表时
     */
    @Test
    public void testGetDpDisplayShopIdsWhenDpDisplayShopIdsIsEmpty() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupDisplayShopDTO displayShopInfo = new DealGroupDisplayShopDTO();
        displayShopInfo.setDpDisplayShopIds(Lists.newArrayList());
        dealGroupDTO.setDisplayShopInfo(displayShopInfo);
        // act
        List<Long> result = DealProductUtils.getDpDisplayShopIds(dealGroupDTO);
        // assert
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    /**
     * 测试 getDpDisplayShopIds 方法，当 dpDisplayShopIds 不为 null 且有数据时
     */
    @Test
    public void testGetDpDisplayShopIdsWhenDpDisplayShopIdsIsNotNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupDisplayShopDTO displayShopInfo = new DealGroupDisplayShopDTO();
        List<Long> expectedShopIds = Arrays.asList(1L, 2L, 3L);
        displayShopInfo.setDpDisplayShopIds(expectedShopIds);
        dealGroupDTO.setDisplayShopInfo(displayShopInfo);
        // act
        List<Long> result = DealProductUtils.getDpDisplayShopIds(dealGroupDTO);
        // assert
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals(expectedShopIds, result);
    }

    @Test
    public void testCheckShopNoExistWithNullDisplayShopCheckResult() throws Throwable {
        // arrange
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(null);
        Long shopId = 123L;
        boolean mt = true;
        // act
        boolean result = DealProductUtils.checkShopNoExist(dealGroupDTO, shopId, mt);
        // assert
        assertTrue("Should return true when displayShopCheckResult is null", result);
    }

    @Test
    public void testCheckShopNoExistWithMtTrueAndEmptyDisplayShopCheckResult() throws Throwable {
        // arrange
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        when(displayShopCheckDTO.getMtDisplayShopCheckResult()).thenReturn(new ArrayList<>());
        Long shopId = 123L;
        boolean mt = true;
        // act
        boolean result = DealProductUtils.checkShopNoExist(dealGroupDTO, shopId, mt);
        // assert
        assertTrue("Should return true when MT display shop check result is empty", result);
    }

    @Test
    public void testCheckShopNoExistWithMtFalseAndEmptyDisplayShopCheckResult() throws Throwable {
        // arrange
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        when(displayShopCheckDTO.getDpDisplayShopCheckResult()).thenReturn(new ArrayList<>());
        Long shopId = 123L;
        boolean mt = false;
        // act
        boolean result = DealProductUtils.checkShopNoExist(dealGroupDTO, shopId, mt);
        // assert
        assertTrue("Should return true when DP display shop check result is empty", result);
    }

    @Test
    public void testCheckShopNoExistWithMtTrueAndNullDisplayShopCheckResult() throws Throwable {
        // arrange
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        when(displayShopCheckDTO.getMtDisplayShopCheckResult()).thenReturn(null);
        Long shopId = 123L;
        boolean mt = true;
        // act
        boolean result = DealProductUtils.checkShopNoExist(dealGroupDTO, shopId, mt);
        // assert
        assertTrue("Should return true when MT display shop check result is null", result);
    }

    @Test
    public void testCheckShopNoExistWithMtFalseAndNullDisplayShopCheckResult() throws Throwable {
        // arrange
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        when(displayShopCheckDTO.getDpDisplayShopCheckResult()).thenReturn(null);
        Long shopId = 123L;
        boolean mt = false;
        // act
        boolean result = DealProductUtils.checkShopNoExist(dealGroupDTO, shopId, mt);
        // assert
        assertTrue("Should return true when DP display shop check result is null", result);
    }

    @Test
    public void testCheckShopNoExistWithMtTrueAndShopExists() throws Throwable {
        // arrange
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        List<DealGroupShopRelationCheckResultDTO> mtShops = new ArrayList<>();
        DealGroupShopRelationCheckResultDTO shop = new DealGroupShopRelationCheckResultDTO();
        shop.setShopId(123L);
        shop.setRelationCheckSuccess(true);
        mtShops.add(shop);
        when(displayShopCheckDTO.getMtDisplayShopCheckResult()).thenReturn(mtShops);
        Long shopId = 123L;
        boolean mt = true;
        // act
        boolean result = DealProductUtils.checkShopNoExist(dealGroupDTO, shopId, mt);
        // assert
        assertFalse("Should return false when shop exists in MT display shop check result", result);
    }

    @Test
    public void testCheckShopNoExistWithMtFalseAndShopExists() throws Throwable {
        // arrange
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        List<DealGroupShopRelationCheckResultDTO> dpShops = new ArrayList<>();
        DealGroupShopRelationCheckResultDTO shop = new DealGroupShopRelationCheckResultDTO();
        shop.setShopId(123L);
        shop.setRelationCheckSuccess(true);
        dpShops.add(shop);
        when(displayShopCheckDTO.getDpDisplayShopCheckResult()).thenReturn(dpShops);
        Long shopId = 123L;
        boolean mt = false;
        // act
        boolean result = DealProductUtils.checkShopNoExist(dealGroupDTO, shopId, mt);
        // assert
        assertFalse("Should return false when shop exists in DP display shop check result", result);
    }

    @Test
    public void testCheckShopNoExistWithMtTrueAndShopDoesNotExist() throws Throwable {
        // arrange
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        List<DealGroupShopRelationCheckResultDTO> mtShops = new ArrayList<>();
        DealGroupShopRelationCheckResultDTO shop = new DealGroupShopRelationCheckResultDTO();
        // Different shop ID
        shop.setShopId(456L);
        shop.setRelationCheckSuccess(true);
        mtShops.add(shop);
        when(displayShopCheckDTO.getMtDisplayShopCheckResult()).thenReturn(mtShops);
        Long shopId = 123L;
        boolean mt = true;
        // act
        boolean result = DealProductUtils.checkShopNoExist(dealGroupDTO, shopId, mt);
        // assert
        assertTrue("Should return true when shop doesn't exist in MT display shop check result", result);
    }

    @Test
    public void testCheckShopNoExistWithMtFalseAndShopDoesNotExist() throws Throwable {
        // arrange
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        List<DealGroupShopRelationCheckResultDTO> dpShops = new ArrayList<>();
        DealGroupShopRelationCheckResultDTO shop = new DealGroupShopRelationCheckResultDTO();
        // Different shop ID
        shop.setShopId(456L);
        shop.setRelationCheckSuccess(true);
        dpShops.add(shop);
        when(displayShopCheckDTO.getDpDisplayShopCheckResult()).thenReturn(dpShops);
        Long shopId = 123L;
        boolean mt = false;
        // act
        boolean result = DealProductUtils.checkShopNoExist(dealGroupDTO, shopId, mt);
        // assert
        assertTrue("Should return true when shop doesn't exist in DP display shop check result", result);
    }

    @Test
    public void testCheckShopNoExistWithMtTrueAndShopExistsButRelationCheckFailed() throws Throwable {
        // arrange
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        List<DealGroupShopRelationCheckResultDTO> mtShops = new ArrayList<>();
        DealGroupShopRelationCheckResultDTO shop = new DealGroupShopRelationCheckResultDTO();
        shop.setShopId(123L);
        // Relation check failed
        shop.setRelationCheckSuccess(false);
        mtShops.add(shop);
        when(displayShopCheckDTO.getMtDisplayShopCheckResult()).thenReturn(mtShops);
        Long shopId = 123L;
        boolean mt = true;
        // act
        boolean result = DealProductUtils.checkShopNoExist(dealGroupDTO, shopId, mt);
        // assert
        assertTrue("Should return true when shop exists but relation check failed", result);
    }

    @Test
    public void testCheckShopNoExistWithMtFalseAndShopExistsButRelationCheckFailed() throws Throwable {
        // arrange
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        List<DealGroupShopRelationCheckResultDTO> dpShops = new ArrayList<>();
        DealGroupShopRelationCheckResultDTO shop = new DealGroupShopRelationCheckResultDTO();
        shop.setShopId(123L);
        // Relation check failed
        shop.setRelationCheckSuccess(false);
        dpShops.add(shop);
        when(displayShopCheckDTO.getDpDisplayShopCheckResult()).thenReturn(dpShops);
        Long shopId = 123L;
        boolean mt = false;
        // act
        boolean result = DealProductUtils.checkShopNoExist(dealGroupDTO, shopId, mt);
        // assert
        assertTrue("Should return true when shop exists but relation check failed", result);
    }

    @Test
    public void testCheckShopNoExistWithMtTrueAndShopExistsButRelationCheckNull() throws Throwable {
        // arrange
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        List<DealGroupShopRelationCheckResultDTO> mtShops = new ArrayList<>();
        DealGroupShopRelationCheckResultDTO shop = new DealGroupShopRelationCheckResultDTO();
        shop.setShopId(123L);
        // Relation check is null
        shop.setRelationCheckSuccess(null);
        mtShops.add(shop);
        when(displayShopCheckDTO.getMtDisplayShopCheckResult()).thenReturn(mtShops);
        Long shopId = 123L;
        boolean mt = true;
        // act
        boolean result = DealProductUtils.checkShopNoExist(dealGroupDTO, shopId, mt);
        // assert
        assertTrue("Should return true when shop exists but relation check is null", result);
    }

    @Test
    public void testCheckShopNoExistWithMtTrueAndMultipleShopsIncludingTarget() throws Throwable {
        // arrange
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        List<DealGroupShopRelationCheckResultDTO> mtShops = new ArrayList<>();
        DealGroupShopRelationCheckResultDTO shop1 = new DealGroupShopRelationCheckResultDTO();
        shop1.setShopId(111L);
        shop1.setRelationCheckSuccess(true);
        mtShops.add(shop1);
        DealGroupShopRelationCheckResultDTO shop2 = new DealGroupShopRelationCheckResultDTO();
        // Target shop
        shop2.setShopId(123L);
        shop2.setRelationCheckSuccess(true);
        mtShops.add(shop2);
        DealGroupShopRelationCheckResultDTO shop3 = new DealGroupShopRelationCheckResultDTO();
        shop3.setShopId(456L);
        shop3.setRelationCheckSuccess(true);
        mtShops.add(shop3);
        when(displayShopCheckDTO.getMtDisplayShopCheckResult()).thenReturn(mtShops);
        Long shopId = 123L;
        boolean mt = true;
        // act
        boolean result = DealProductUtils.checkShopNoExist(dealGroupDTO, shopId, mt);
        // assert
        assertFalse("Should return false when target shop exists among multiple shops", result);
    }

    @Test
    public void testCheckShopNoExistWithNullShopId() throws Throwable {
        // arrange
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        List<DealGroupShopRelationCheckResultDTO> mtShops = new ArrayList<>();
        DealGroupShopRelationCheckResultDTO shop = new DealGroupShopRelationCheckResultDTO();
        shop.setShopId(123L);
        shop.setRelationCheckSuccess(true);
        mtShops.add(shop);
        when(displayShopCheckDTO.getMtDisplayShopCheckResult()).thenReturn(mtShops);
        Long shopId = null;
        boolean mt = true;
        // act
        boolean result = DealProductUtils.checkShopNoExist(dealGroupDTO, shopId, mt);
        // assert
        assertTrue("Should return true when shopId is null", result);
    }

    @Test
    public void testCheckShopNoExistWithNullShopInList() throws Throwable {
        // arrange
        when(dealGroupDTO.getDisplayShopCheckResult()).thenReturn(displayShopCheckDTO);
        List<DealGroupShopRelationCheckResultDTO> mtShops = new ArrayList<>();
        // Add null shop
        mtShops.add(null);
        DealGroupShopRelationCheckResultDTO shop = new DealGroupShopRelationCheckResultDTO();
        shop.setShopId(123L);
        shop.setRelationCheckSuccess(true);
        mtShops.add(shop);
        when(displayShopCheckDTO.getMtDisplayShopCheckResult()).thenReturn(mtShops);
        Long shopId = 456L;
        boolean mt = true;
        // act
        boolean result = DealProductUtils.checkShopNoExist(dealGroupDTO, shopId, mt);
        // assert
        assertTrue("Should return true when target shop doesn't exist and list contains null", result);
    }
}
