package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.attribute.dto.DealGroupAttributeDTO;
import com.dianping.deal.attribute.dto.DealGroupAttributeResult;
import com.dianping.deal.attribute.service.DealGroupAttributeGetService;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class DealGroupWrapper_GetDealGroupAttrsTest {

    @Mock
    private Future future;

    private DealGroupWrapper dealGroupWrapper = new DealGroupWrapper();

    /**
     * 测试 future 为 null 的情况
     */
    @Test
    public void testGetDealGroupAttrsFutureIsNull() throws Throwable {
        // arrange
        Future future = null;
        // act
        Map<Integer, List<AttributeDTO>> result = dealGroupWrapper.getDealGroupAttrs(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 future 不为 null，但获取 future 的结果时发生异常的情况
     */
    @Test
    public void testGetDealGroupAttrsFutureGetThrowsException() throws Throwable {
        // arrange
        when(future.get()).thenThrow(new InterruptedException());
        // act
        Map<Integer, List<AttributeDTO>> result = dealGroupWrapper.getDealGroupAttrs(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 future 不为 null，获取 future 的结果成功，但结果的 code 不为 0 的情况
     */
    @Test
    public void testGetDealGroupAttrsResultCodeNotZero() throws Throwable {
        // arrange
        DealGroupAttributeResult result = new DealGroupAttributeResult();
        result.setCode(1);
        when(future.get()).thenReturn(result);
        // act
        Map<Integer, List<AttributeDTO>> actualResult = dealGroupWrapper.getDealGroupAttrs(future);
        // assert
        assertNull(actualResult);
    }

    /**
     * 测试 future 不为 null，获取 future 的结果成功，结果的 code 为 0，但结果的 content 为空的情况
     */
    @Test
    public void testGetDealGroupAttrsResultContentIsEmpty() throws Throwable {
        // arrange
        DealGroupAttributeResult result = new DealGroupAttributeResult();
        result.setCode(0);
        when(future.get()).thenReturn(result);
        // act
        Map<Integer, List<AttributeDTO>> actualResult = dealGroupWrapper.getDealGroupAttrs(future);
        // assert
        assertNull(actualResult);
    }

    /**
     * 测试 future 不为 null，获取 future 的结果成功，结果的 code 为 0，结果的 content 不为空的情况
     */
    @Test
    public void testGetDealGroupAttrsResultCodeIsZeroAndContentIsNotEmpty() throws Throwable {
        // arrange
        DealGroupAttributeResult result = new DealGroupAttributeResult();
        result.setCode(0);
        DealGroupAttributeDTO dealGroupAttributeDTO = new DealGroupAttributeDTO();
        dealGroupAttributeDTO.setDealGroupId(1);
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("test");
        dealGroupAttributeDTO.setAttributes(Arrays.asList(attributeDTO));
        result.setContent(Arrays.asList(dealGroupAttributeDTO));
        when(future.get()).thenReturn(result);
        // act
        Map<Integer, List<AttributeDTO>> actualResult = dealGroupWrapper.getDealGroupAttrs(future);
        // assert
        assertNotNull(actualResult);
        assertEquals(1, actualResult.size());
        assertTrue(actualResult.containsKey(1));
        assertEquals(1, actualResult.get(1).size());
        assertEquals("test", actualResult.get(1).get(0).getName());
    }
}
