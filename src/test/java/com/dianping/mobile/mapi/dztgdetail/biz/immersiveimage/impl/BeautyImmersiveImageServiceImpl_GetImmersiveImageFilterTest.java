package com.dianping.mobile.mapi.dztgdetail.biz.immersiveimage.impl;

import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetImmersiveImageFilterRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageFilterVO;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ImmersiveImageWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.RecommendServiceWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ShopTagWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiClientWrapper;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class BeautyImmersiveImageServiceImpl_GetImmersiveImageFilterTest {

    @InjectMocks
    private BeautyImmersiveImageServiceImpl beautyImmersiveImageService;

    @Mock
    private ImmersiveImageWrapper immersiveImageWrapper;

    @Mock
    private RecommendServiceWrapper recommendServiceWrapper;

    @Mock
    private DouHuBiz douHuBiz;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Mock
    private ShopTagWrapper shopTagWrapper;

    @Mock
    private PoiClientWrapper poiClientWrapper;

    /**
     * 测试 getImmersiveImageFilter 方法
     */
    @Test
    public void testGetImmersiveImageFilter() throws Throwable {
        // arrange
        GetImmersiveImageFilterRequest request = new GetImmersiveImageFilterRequest();
        // act
        ImmersiveImageFilterVO result = beautyImmersiveImageService.getImmersiveImageFilter(request);
        // assert
        assertNull(result);
    }
}
