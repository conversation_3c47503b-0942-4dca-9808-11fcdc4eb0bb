package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.ReviewPicDTO;
import com.dianping.ugc.pic.remote.dto.MtReviewPicInfo;
import com.dianping.ugc.pic.remote.dto.VideoData;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import static org.junit.Assert.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;

public class ShopReviewHelper_BuildMtReviewPicListTest {

    @Test
    public void testBuildMtReviewPicList_EmptyLists() throws Throwable {
        List<VideoData> videoDataList = new ArrayList<>();
        List<MtReviewPicInfo> mtReviewPicInfoList = new ArrayList<>();
        List<ReviewPicDTO> result = ShopReviewHelper.buildMtReviewPicList(videoDataList, mtReviewPicInfoList);
        assertTrue("结果应为空列表", result.isEmpty());
    }

    @Test
    public void testBuildMtReviewPicList_NonEmptyVideoDataList() throws Throwable {
        List<VideoData> videoDataList = new ArrayList<>();
        VideoData videoData = new VideoData();
        videoData.setUrl("videoUrl");
        videoData.setDuration(120L);
        videoData.setModTime(new Date());
        videoDataList.add(videoData);
        List<MtReviewPicInfo> mtReviewPicInfoList = new ArrayList<>();
        List<ReviewPicDTO> result = ShopReviewHelper.buildMtReviewPicList(videoDataList, mtReviewPicInfoList);
        assertFalse("结果不应为空列表", result.isEmpty());
        assertEquals("列表大小应为1", 1, result.size());
    }

    @Test
    public void testBuildMtReviewPicList_NonEmptyMtReviewPicInfoList() throws Throwable {
        List<VideoData> videoDataList = new ArrayList<>();
        List<MtReviewPicInfo> mtReviewPicInfoList = new ArrayList<>();
        MtReviewPicInfo mtReviewPicInfo = new MtReviewPicInfo();
        mtReviewPicInfo.setUrl("/w.h/picUrl");
        mtReviewPicInfo.setTitle("picTitle");
        mtReviewPicInfo.setAddTime(new Date());
        mtReviewPicInfoList.add(mtReviewPicInfo);
        List<ReviewPicDTO> result = ShopReviewHelper.buildMtReviewPicList(videoDataList, mtReviewPicInfoList);
        assertFalse("结果不应为空列表", result.isEmpty());
        assertEquals("列表大小应为1", 1, result.size());
    }

    @Test
    public void testBuildMtReviewPicList_NonEmptyLists() throws Throwable {
        List<VideoData> videoDataList = new ArrayList<>();
        VideoData videoData = new VideoData();
        videoData.setUrl("videoUrl");
        videoData.setDuration(120L);
        videoData.setModTime(new Date());
        videoDataList.add(videoData);
        List<MtReviewPicInfo> mtReviewPicInfoList = new ArrayList<>();
        MtReviewPicInfo mtReviewPicInfo = new MtReviewPicInfo();
        mtReviewPicInfo.setUrl("/w.h/picUrl");
        mtReviewPicInfo.setTitle("picTitle");
        mtReviewPicInfo.setAddTime(new Date());
        mtReviewPicInfoList.add(mtReviewPicInfo);
        List<ReviewPicDTO> result = ShopReviewHelper.buildMtReviewPicList(videoDataList, mtReviewPicInfoList);
        assertFalse("结果不应为空列表", result.isEmpty());
        assertEquals("列表大小应为2", 2, result.size());
    }

    @Test
    public void testBuildMtReviewPicList_NullElement() throws Throwable {
        List<VideoData> videoDataList = new ArrayList<>();
        videoDataList.add(null);
        List<MtReviewPicInfo> mtReviewPicInfoList = new ArrayList<>();
        mtReviewPicInfoList.add(null);
        List<ReviewPicDTO> result = ShopReviewHelper.buildMtReviewPicList(videoDataList, mtReviewPicInfoList);
        assertTrue("结果应为空列表", result.isEmpty());
    }

    @Test
    public void testBuildMtReviewPicList_NullProperty() throws Throwable {
        List<VideoData> videoDataList = new ArrayList<>();
        VideoData videoData = new VideoData();
        // Set a non-null URL to avoid NullPointerException during replace operation
        videoData.setUrl("http://example.com/videoUrl");
        videoData.setDuration(120L);
        videoData.setModTime(new Date());
        videoDataList.add(videoData);
        List<MtReviewPicInfo> mtReviewPicInfoList = new ArrayList<>();
        MtReviewPicInfo mtReviewPicInfo = new MtReviewPicInfo();
        // Set a non-null URL to avoid NullPointerException during replace operation
        mtReviewPicInfo.setUrl("/w.h/http://example.com/picUrl");
        mtReviewPicInfo.setTitle("picTitle");
        mtReviewPicInfo.setAddTime(new Date());
        mtReviewPicInfoList.add(mtReviewPicInfo);
        List<ReviewPicDTO> result = ShopReviewHelper.buildMtReviewPicList(videoDataList, mtReviewPicInfoList);
        assertFalse("结果不应为空列表", result.isEmpty());
        assertEquals("列表大小应为2", 2, result.size());
    }
}
