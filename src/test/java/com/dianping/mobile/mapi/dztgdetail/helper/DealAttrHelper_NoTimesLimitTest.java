package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.AvailableDateDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.AvailableDurationDateDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.CycleAvailableDateDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DateRangeDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DisableDateDTO;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealAttrHelper_NoTimesLimitTest {

    /**
     * Test noTimesLimit method when attrs is null, should return false.
     */
    @Test
    public void testNoTimesLimitWhenAttrsIsNull() throws Throwable {
        assertFalse(DealAttrHelper.noTimesLimit(null));
    }

    /**
     * Test noTimesLimit method when attrs is not empty, but does not contain NO_TIMES_LIMIT attribute, should return false.
     */
    @Test
    public void testNoTimesLimitWhenAttrsHasNoNoTimesLimit() throws Throwable {
        AttributeDTO attr = new AttributeDTO();
        attr.setName("other");
        attr.setValue(Arrays.asList("不可"));
        assertFalse(DealAttrHelper.noTimesLimit(Collections.singletonList(attr)));
    }

    /**
     * Test noTimesLimit method when attrs is not empty, contains NO_TIMES_LIMIT attribute, but its value is empty, should return false.
     */
    @Test
    public void testNoTimesLimitWhenNoTimesLimitValueIsEmpty() throws Throwable {
        AttributeDTO attr = new AttributeDTO();
        attr.setName("NO_TIMES_LIMIT");
        attr.setValue(Collections.emptyList());
        assertFalse(DealAttrHelper.noTimesLimit(Collections.singletonList(attr)));
    }

    /**
     * Test noTimesLimit method when attrs is not empty, contains NO_TIMES_LIMIT attribute, its value is not empty, but not equal to "可", should return false.
     */
    @Test
    public void testNoTimesLimitWhenNoTimesLimitValueIsNotYes() throws Throwable {
        AttributeDTO attr = new AttributeDTO();
        attr.setName("NO_TIMES_LIMIT");
        attr.setValue(Arrays.asList("不可"));
        assertFalse(DealAttrHelper.noTimesLimit(Collections.singletonList(attr)));
    }

    @Test
    public void testJudgeDisableUsableWhenDealGroupDTOIsNull() throws Throwable {
        DealGroupDTO dealGroupDTO = null;
        boolean result = DealAttrHelper.judgeDisableUsable(dealGroupDTO);
        assertFalse(result);
    }

    @Test
    public void testJudgeDisableUsableWhenUseRuleIsNull() throws Throwable {
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupRuleDTO ruleDTO = mock(DealGroupRuleDTO.class);
        when(dealGroupDTO.getRule()).thenReturn(ruleDTO);
        boolean result = DealAttrHelper.judgeDisableUsable(dealGroupDTO);
        assertFalse(result);
    }

    @Test
    public void testJudgeDisableUsableWhenAvailableDateIsNotNullAndAvailableTypeIsZeroAndCycleAvailableDateListIsNotNullAndContainsCurrentDay() throws Throwable {
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupRuleDTO ruleDTO = mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRuleDTO = mock(DealGroupUseRuleDTO.class);
        AvailableDateDTO availableDateDTO = mock(AvailableDateDTO.class);
        CycleAvailableDateDTO cycleAvailableDateDTO = mock(CycleAvailableDateDTO.class);
        when(dealGroupDTO.getRule()).thenReturn(ruleDTO);
        when(ruleDTO.getUseRule()).thenReturn(useRuleDTO);
        when(useRuleDTO.getAvailableDate()).thenReturn(availableDateDTO);
        when(availableDateDTO.getAvailableType()).thenReturn(0);
        when(availableDateDTO.getCycleAvailableDateList()).thenReturn(Collections.singletonList(cycleAvailableDateDTO));
        // Set up the current day in the CycleAvailableDateList
        LocalDate currentDate = LocalDate.now();
        DayOfWeek currentDayOfWeek = currentDate.getDayOfWeek();
        when(cycleAvailableDateDTO.getAvailableDays()).thenReturn(Collections.singletonList(currentDayOfWeek.getValue()));
        boolean result = DealAttrHelper.judgeDisableUsable(dealGroupDTO);
        assertFalse(result);
    }

    @Test
    public void testJudgeDisableUsableWhenAvailableDateIsNotNullAndAvailableTypeIsOneAndSpecifiedDurationDateListIsNotNullAndContainsCurrentDate() throws Throwable {
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupRuleDTO ruleDTO = mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRuleDTO = mock(DealGroupUseRuleDTO.class);
        AvailableDateDTO availableDateDTO = mock(AvailableDateDTO.class);
        AvailableDurationDateDTO availableDurationDateDTO = mock(AvailableDurationDateDTO.class);
        DateRangeDTO dateRangeDTO = mock(DateRangeDTO.class);
        when(dealGroupDTO.getRule()).thenReturn(ruleDTO);
        when(ruleDTO.getUseRule()).thenReturn(useRuleDTO);
        when(useRuleDTO.getAvailableDate()).thenReturn(availableDateDTO);
        when(availableDateDTO.getAvailableType()).thenReturn(1);
        when(availableDateDTO.getSpecifiedDurationDateList()).thenReturn(Collections.singletonList(availableDurationDateDTO));
        when(availableDurationDateDTO.getAvailableDateRangeDTOS()).thenReturn(Collections.singletonList(dateRangeDTO));
        // Set up the current date in the SpecifiedDurationDateList
        LocalDate currentDate = LocalDate.now();
        when(dateRangeDTO.getFrom()).thenReturn(currentDate.toString());
        when(dateRangeDTO.getTo()).thenReturn(currentDate.toString());
        boolean result = DealAttrHelper.judgeDisableUsable(dealGroupDTO);
        assertFalse(result);
    }
}
