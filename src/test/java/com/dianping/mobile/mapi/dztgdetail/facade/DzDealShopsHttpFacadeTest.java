package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.general.unified.search.api.productshopsearch.GeneralProductShopSearchService;
import com.dianping.general.unified.search.api.productshopsearch.dto.ProductShopSearchDTO;
import com.dianping.general.unified.search.api.productshopsearch.response.GeneralProductShopSearchResponse;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DzDealShopsRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.shop.*;
import com.dianping.mobile.mapi.dztgdetail.facade.shop.DzDealShopsHttpFacade;
import com.dianping.pay.order.service.query.GetUnifiedOrderService;
import com.dianping.pay.order.service.query.dto.UnifiedOrderWithId;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.nibtp.trade.client.buy.enums.OrderExtraFieldEnum;
import com.sankuai.dztheme.shop.service.DzThemeShopService;
import com.sankuai.dztheme.shop.vo.*;
import com.sankuai.dztheme.shop.vo.v1.ShopThemeResponse;
import com.sankuai.nib.mkt.promotion.tag.server.api.model.response.PromotionTagApplyInfoResponse;
import com.sankuai.nib.mkt.promotion.tag.server.api.model.response.PromotionTagResponseUnit;
import com.sankuai.nib.mkt.promotion.tag.server.api.service.IMagicalMemberTagService;
import com.sankuai.spt.statequery.api.dto.*;
import com.sankuai.spt.statequery.api.dto.ShopBookInfoDTO;
import com.sankuai.spt.statequery.api.response.QueryBaseStateResponse;
import com.sankuai.spt.statequery.api.response.QueryShopBookInfoResponse;
import com.sankuai.spt.statequery.api.service.BaseStateQueryService;
import com.sankuai.spt.statequery.api.service.ShopBookInfoQueryService;
import com.sankuai.web.dealadapter.response.HasBlackPoiResp;
import com.sankuai.web.dealadapter.service.OrderVerifySnapshotService;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DzDealShopsHttpFacadeTest {

    @InjectMocks
    private DzDealShopsHttpFacade dzDealShopsHttpFacade;

    @Mock
    private GeneralProductShopSearchService generalProductShopSearchService;
    @Mock
    private DzThemeShopService dzThemeShopService;
    @Mock
    private IMagicalMemberTagService iMagicalPromotionMemberTagService;
    @Mock
    private GetUnifiedOrderService getUnifiedOrderService;
    @Mock
    private MapperWrapper mapperWrapper;
    @Mock
    private OrderVerifySnapshotService orderVerifySnapshotService;
    @Mock
    private ShopBookInfoQueryService shopBookInfoQueryService;
    @Mock
    private BaseStateQueryService baseStateQueryService;
    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // 注入父类的依赖
        ReflectionTestUtils.setField(dzDealShopsHttpFacade, "generalProductShopSearchService", generalProductShopSearchService);
        ReflectionTestUtils.setField(dzDealShopsHttpFacade, "getUnifiedOrderService", getUnifiedOrderService);
        ReflectionTestUtils.setField(dzDealShopsHttpFacade, "mapperWrapper", mapperWrapper);
        ReflectionTestUtils.setField(dzDealShopsHttpFacade, "orderVerifySnapshotService", orderVerifySnapshotService);
        ReflectionTestUtils.setField(dzDealShopsHttpFacade, "dealGroupWrapper", dealGroupWrapper);
    }

    @Test
    public void testGetDealGroupShopSuccess_dealDetail() {
        // 初始化请求参数
        Long dealGroupId = 12345L;
        long shopId1 = 111L;
        long shopId2 = 222L;
        String snapshotId = "789";
        Long totalCount = 2L;
        long ROW_ID = 1L;

        DzDealShopsRequest request = new DzDealShopsRequest();
        request.setDealGroupId(dealGroupId);
        request.setPageNum(1);
        request.setPageSize(10);
        request.setCityId(1);
        request.setLat(11.11);
        request.setLng(101.11);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(4);
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);


        // bp检索召回
        GeneralProductShopSearchResponse searchResponse = new GeneralProductShopSearchResponse();
        searchResponse.setSuccess(true);
        searchResponse.setTotalHits(totalCount);
        ProductShopSearchDTO shopSearchDTO1 = new ProductShopSearchDTO();
        shopSearchDTO1.setShopId(shopId1);
        ProductShopSearchDTO shopSearchDTO2 = new ProductShopSearchDTO();
        shopSearchDTO2.setShopId(shopId2);
        searchResponse.setResult(Lists.newArrayList(shopSearchDTO1, shopSearchDTO2));
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(searchResponse);

        // 商户主题召回
        ShopThemeResponse shopThemeResponse = new ShopThemeResponse();
        shopThemeResponse.setSuccess(true);
        Map<Long, ShopCardDTO> shopCardDTOMap = Maps.newHashMap();
        ShopCardDTO shopCardDTO1 = new ShopCardDTO();
        shopCardDTO1.setShopName("门店名称1");
        shopCardDTO1.setAddress("地址1");
        AttrItemDTO attrs = new AttrItemDTO();
        attrs.setAttrName("shopMagicalInfoDTO");
        attrs.setAttrValue("{\"shopId\":111,\"applyResult\":1}");
        shopCardDTO1.setAttrs(Lists.newArrayList(attrs));
        shopCardDTOMap.put(shopId1, shopCardDTO1);
        ShopCardDTO shopCardDTO2 = new ShopCardDTO();
        shopCardDTO2.setShopName("门店名称2");
        shopCardDTO2.setAddress("地址2");
        shopCardDTOMap.put(shopId2, shopCardDTO2);
        shopThemeResponse.setShopCardDTOMap(shopCardDTOMap);
        when(dzThemeShopService.queryShopTheme(any())).thenReturn(shopThemeResponse);

        // 标签召回
        PromotionTagApplyInfoResponse applyInfoResponse = new PromotionTagApplyInfoResponse();
        applyInfoResponse.setBlackTrafficFlag(false);
        applyInfoResponse.setPassMatrix(true);
        Map<Long, PromotionTagResponseUnit> unitResultMap = Maps.newHashMap();
        PromotionTagResponseUnit promotionTagResponseUnit = new PromotionTagResponseUnit();
        promotionTagResponseUnit.setApplyResult(1);
        unitResultMap.put(ROW_ID, promotionTagResponseUnit);
        applyInfoResponse.setUnitResultMap(unitResultMap);
        when(iMagicalPromotionMemberTagService.queryMagicalMemberTagApplyInfo(any())).thenReturn(applyInfoResponse);

        // id映射
        when(mapperWrapper.preUserInfoByDpUserId(anyLong())).thenReturn(null);
        when(mapperWrapper.preDpIdMtIdMapper(anyInt())).thenReturn(null);
        when(mapperWrapper.getMtIdByDpIdMtIdMapper(any())).thenReturn(dealGroupId.intValue());


        // 调用方法
        DealGroupShopVO result = dzDealShopsHttpFacade.execute(request, envCtx);

        // 验证结果
        assertNotNull(result);
        assertEquals(result.getTotalCount(), totalCount.intValue());
        assertEquals(result.getList().size(), totalCount.intValue());
        assertTrue(result.getList().get(0) != null && CollectionUtils.isNotEmpty(result.getList().get(0).getIcons()));
        assertTrue(result.getList().get(1) != null && CollectionUtils.isEmpty(result.getList().get(1).getIcons()));
    }

    @Test
    public void testGetDealGroupShopSuccess_orderDetail() {
        // 初始化请求参数
        Long dealGroupId = 12345L;
        long shopId1 = 111L;
        String snapshotId = "789";
        Long totalCount = 1L;
        long ROW_ID = 1L;
        String orderId = "232323";

        DzDealShopsRequest request = new DzDealShopsRequest();
        request.setDealGroupId(dealGroupId);
        request.setPageNum(1);
        request.setPageSize(10);
        request.setCityId(1);
        request.setLat(11.11);
        request.setLng(101.11);
        request.setOrderId(orderId);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(4);
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);

        // bp检索召回
        GeneralProductShopSearchResponse searchResponse = new GeneralProductShopSearchResponse();
        searchResponse.setSuccess(true);
        searchResponse.setTotalHits(totalCount);
        ProductShopSearchDTO shopSearchDTO1 = new ProductShopSearchDTO();
        shopSearchDTO1.setShopId(shopId1);
        searchResponse.setResult(Lists.newArrayList(shopSearchDTO1));
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(searchResponse);

        // 商户主题召回
        ShopThemeResponse shopThemeResponse = new ShopThemeResponse();
        shopThemeResponse.setSuccess(true);
        Map<Long, ShopCardDTO> shopCardDTOMap = Maps.newHashMap();
        ShopCardDTO shopCardDTO1 = new ShopCardDTO();
        shopCardDTO1.setShopName("门店名称1");
        shopCardDTO1.setAddress("地址1");
        AttrItemDTO attrs = new AttrItemDTO();
        attrs.setAttrName("shopMagicalInfoDTO");
        attrs.setAttrValue("{\"shopId\":111,\"applyResult\":1}");
        shopCardDTO1.setAttrs(Lists.newArrayList(attrs));
        shopCardDTOMap.put(shopId1, shopCardDTO1);
        shopThemeResponse.setShopCardDTOMap(shopCardDTOMap);
        when(dzThemeShopService.queryShopTheme(any())).thenReturn(shopThemeResponse);

        // id映射
        when(mapperWrapper.preUserInfoByDpUserId(anyLong())).thenReturn(null);
        when(mapperWrapper.preDpIdMtIdMapper(anyInt())).thenReturn(null);
        when(mapperWrapper.getMtIdByDpIdMtIdMapper(any())).thenReturn(dealGroupId.intValue());

        // 订单快照
        UnifiedOrderWithId unifiedOrderWithId = new UnifiedOrderWithId();
        Map<String, String> nibExtraFields = Maps.newHashMap();
        nibExtraFields.put(OrderExtraFieldEnum.TP_PLAT_ORDER_VERIFY_SNAPSHOT.getKey(), snapshotId);
        unifiedOrderWithId.setNibExtraFields(nibExtraFields);
        when(getUnifiedOrderService.getByUnifiedOrderId(anyString())).thenReturn(unifiedOrderWithId);

        // 订单快照无黑选单商户
        HasBlackPoiResp hasBlackPoiResp = new HasBlackPoiResp();
        hasBlackPoiResp.setHasBlackPoi(true);
        when(orderVerifySnapshotService.hasBlackPoi(any())).thenReturn(hasBlackPoiResp);

        // 调用方法
        DealGroupShopVO result = dzDealShopsHttpFacade.execute(request, envCtx);

        // 验证结果
        assertNotNull(result);
        assertEquals(result.getTotalCount(), totalCount.intValue());
        assertEquals(result.getList().size(), totalCount.intValue());
        assertTrue(result.getList().get(0) != null && CollectionUtils.isEmpty(result.getList().get(0).getIcons()));
    }

    // ========== 针对 DzDealShopsHttpFacade 的详细单测 ==========

    @Test
    public void testInitShopListContext() {
        // 准备测试数据
        DzDealShopsRequest request = buildBasicRequest();
        EnvCtx envCtx = buildBasicEnvCtx();

        // 模拟依赖
        when(mapperWrapper.preUserInfoByDpUserId(anyLong())).thenReturn(null);

        // 调用被测方法
        ShopListContext context = dzDealShopsHttpFacade.initShopListContext(request, envCtx);

        // 验证结果
        assertNotNull(context);
        assertEquals(request.getOrderId(), context.getOrderId());
        assertEquals(envCtx.isMt(), context.isMt());
        assertEquals(request.getDealGroupId(), context.getDealGroupId());
        assertEquals(request.getCityId(), context.getCityId());
        assertEquals(request.getPageNum(), context.getPageNum());
        assertEquals(request.getPageSize(), context.getPageSize());
        assertEquals(request.getLat(), context.getLat());
        assertEquals(request.getLng(), context.getLng());
    }

    @Test
    public void testFillShopInfo_preOrderDetailPage() {
        // 准备测试数据
        ShopListContext context = buildShopListContext();
        context.setSource(RequestSourceEnum.PRE_ORDER_DETAIL_PAGE.getSource());
        context.setShopIds(Arrays.asList(111L, 222L));

        // 模拟依赖返回
        mockShopThemeResponse();
        mockMagicalDealResponse(true);
        mockShopMapping();
        mockDealCategory();
        mockShopBookInfo();
        mockBaseStateQuery();

        // 调用被测方法
        CompletableFuture<Void> mockFuture = CompletableFuture.completedFuture(null);
        DealGroupShopVO result = dzDealShopsHttpFacade.fillShopInfo(context, mockFuture);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getList());
        assertEquals(2, result.getList().size());

        // 验证预订相关信息
        DealGroupShop firstShop = result.getList().get(0);
        assertNotNull(firstShop.getBookIcon());
        assertNotNull(firstShop.getFirstOrderTime());
        assertTrue(firstShop.isNearestShop()); // 第一个门店应该是最近的

        DealGroupShop secondShop = result.getList().get(1);
        assertNotNull(secondShop.getBookIcon());
        assertFalse(secondShop.isNearestShop());
    }

    @Test
    public void testFillShopInfo_normalDetailPage() {
        // 准备测试数据
        ShopListContext context = buildShopListContext();
        context.setSource("normal"); // 非一品多态(先囤后订)页面
        context.setShopIds(Arrays.asList(111L));

        // 模拟依赖返回
        mockShopThemeResponse();
        mockMagicalDealResponse(false);
        mockShopMapping();
        mockDealCategory();

        // 调用被测方法
        CompletableFuture<Void> mockFuture = CompletableFuture.completedFuture(null);
        DealGroupShopVO result = dzDealShopsHttpFacade.fillShopInfo(context, mockFuture);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getList());
        assertEquals(1, result.getList().size());

        DealGroupShop shop = result.getList().get(0);
        assertEquals(BookIconEnum.PHONE.getValue(), shop.getBookIcon().getType());
        assertNull(shop.getFirstOrderTime());
        assertFalse(shop.isNearestShop());
    }

    @Test
    public void testFillShopInfo_withMagicalTag() {
        // 准备测试数据
        ShopListContext context = buildShopListContext();
        context.setShopIds(Arrays.asList(111L));
        context.setOrderId(null); // 非订详场景，可以展示神会员标签

        // 模拟依赖返回
        mockShopThemeResponseWithMagicalAttr();
        mockMagicalDealResponse(true);
        mockShopMapping();
        mockDealCategory();

        // 调用被测方法
        CompletableFuture<Void> mockFuture = CompletableFuture.completedFuture(null);
        DealGroupShopVO result = dzDealShopsHttpFacade.fillShopInfo(context, mockFuture);

        // 验证结果
        assertNotNull(result);
        DealGroupShop shop = result.getList().get(0);
        assertTrue(CollectionUtils.isNotEmpty(shop.getIcons()));
        assertTrue(CollectionUtils.isNotEmpty(shop.getZdcTags()));
    }

    @Test
    public void testFillShopInfo_orderShopMatching() {
        // 准备测试数据
        ShopListContext context = buildShopListContext();
        context.setShopIds(Arrays.asList(111L, 222L));

        // 设置订单信息
        UnifiedOrderWithId order = new UnifiedOrderWithId();
        order.setLongShopId(111L);
        order.setLongMtShopId(111L);
        context.setUnifiedOrderWithId(order);

        // 模拟依赖返回
        mockShopThemeResponse();
        mockMagicalDealResponse(false);
        mockShopMapping();
        mockDealCategory();

        // 调用被测方法
        CompletableFuture<Void> mockFuture = CompletableFuture.completedFuture(null);
        DealGroupShopVO result = dzDealShopsHttpFacade.fillShopInfo(context, mockFuture);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());

        // 验证订单门店匹配
        assertTrue(result.getList().get(0).isOrderShop());
        assertFalse(result.getList().get(1).isOrderShop());
    }

    @Test
    public void testFillShopInfo_emptyShopIds() {
        // 准备测试数据
        ShopListContext context = buildShopListContext();
        context.setShopIds(Lists.newArrayList());

        // 调用被测方法
        CompletableFuture<Void> mockFuture = CompletableFuture.completedFuture(null);
        DealGroupShopVO result = dzDealShopsHttpFacade.fillShopInfo(context, mockFuture);

        // 验证结果
        assertNull(result);
    }

    @Test
    public void testFillShopInfo_themeServiceFailed() {
        // 准备测试数据
        ShopListContext context = buildShopListContext();
        context.setShopIds(Arrays.asList(111L));

        // 模拟主题服务失败
        ShopThemeResponse failedResponse = new ShopThemeResponse();
        failedResponse.setSuccess(false);
        when(dzThemeShopService.queryShopTheme(any())).thenReturn(failedResponse);

        mockMagicalDealResponse(false);
        mockShopMapping();
        mockDealCategory();

        // 调用被测方法
        CompletableFuture<Void> mockFuture = CompletableFuture.completedFuture(null);
        DealGroupShopVO result = dzDealShopsHttpFacade.fillShopInfo(context, mockFuture);

        // 验证结果
        assertNull(result);
    }

    @Test
    public void testFillShopInfo_bookServiceException() {
        // 准备测试数据
        ShopListContext context = buildShopListContext();
        context.setSource(RequestSourceEnum.PRE_ORDER_DETAIL_PAGE.getSource());
        context.setShopIds(Arrays.asList(111L));

        // 模拟预订服务异常
        when(shopBookInfoQueryService.queryShopBookInfo(any())).thenThrow(new RuntimeException("Service failed"));
        when(baseStateQueryService.queryBaseState(any())).thenThrow(new RuntimeException("Service failed"));

        mockShopThemeResponse();
        mockMagicalDealResponse(false);
        mockShopMapping();
        mockDealCategory();

        // 调用被测方法
        CompletableFuture<Void> mockFuture = CompletableFuture.completedFuture(null);
        DealGroupShopVO result = dzDealShopsHttpFacade.fillShopInfo(context, mockFuture);

        // 验证结果 - 应该正常返回，但没有预订信息
        assertNotNull(result);
        assertEquals(1, result.getList().size());
        DealGroupShop shop = result.getList().get(0);
        assertNull(shop.getFirstOrderTime());
    }

    // ========== 辅助方法 ==========

    private DzDealShopsRequest buildBasicRequest() {
        DzDealShopsRequest request = new DzDealShopsRequest();
        request.setDealGroupId(12345L);
        request.setPageNum(1);
        request.setPageSize(10);
        request.setCityId(1);
        request.setGpsCityId(1);
        request.setTargetcityid(1);
        request.setLat(11.11);
        request.setLng(101.11);
        request.setTargetlat(11.11);
        request.setTargetlng(101.11);
        request.setSource("test");
        request.setMmcinflate(1);
        request.setMmcuse(1);
        request.setMmcbuy(1);
        request.setMmcfree(1);
        request.setShopId(111L);
        return request;
    }

    private EnvCtx buildBasicEnvCtx() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(4);
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        envCtx.setDpUserId(1001L);
        envCtx.setMtUserId(1001L);
        envCtx.setAppDeviceId("device123");
        return envCtx;
    }

    private ShopListContext buildShopListContext() {
        ShopListContext context = new ShopListContext();
        context.setDealGroupId(12345L);
        context.setMtDealGroupId(12345L);
        context.setDpDealGroupId(12345L);
        context.setMt(false);
        context.setNeedLog(false);
        context.setGpsCityId(1);
        context.setTargetCityId(1);
        context.setEnvCtx(buildBasicEnvCtx());
        context.setCityId(1);
        context.setLat(11.11);
        context.setLng(101.11);
        context.setTargetlat(11.11);
        context.setTargetlng(101.11);
        context.setPageNum(1);
        context.setPageSize(10);
        context.setTotalCount(2L);
        context.setUserRelation(null);
        context.setMmcinflate(1);
        context.setMmcuse(1);
        context.setMmcbuy(1);
        context.setMmcfree(1);

        // 设置页面配置
        PageConfig pageConfig = new PageConfig();
        pageConfig.setOnlineText("在线预订");
        pageConfig.setPhoneText("电话预订");
        pageConfig.setMagicalTagUrl("http://test.com/magical.png");
        pageConfig.setDealPageName("可用门店");
        pageConfig.setOrderPageName("订单门店");
        pageConfig.setBookingPageName("预约门店");
        pageConfig.setInOrderPageName("下单门店");
        pageConfig.setTips("提示信息");
        pageConfig.setAfterOrderBookingTips("订后预约提示");
        pageConfig.setInOrderTips("订中提示");
        pageConfig.setVersion(1);
        context.setPageConfig(pageConfig);

        return context;
    }

    private void mockShopThemeResponse() {
        ShopThemeResponse response = new ShopThemeResponse();
        response.setSuccess(true);

        Map<Long, ShopCardDTO> shopCardMap = Maps.newHashMap();

        // 第一个门店
        ShopCardDTO shop1 = new ShopCardDTO();
        shop1.setShopName("测试门店1");
        shop1.setAddress("测试地址1");
        shop1.setDistanceStr("500m");
        shop1.setDistanceValue(500.0);
        shop1.setStarStr("4.5");
        shop1.setLat(11.11);
        shop1.setLng(101.11);
        shop1.setShopUrl("http://test.com/shop1");
        shop1.setMapUrl("http://test.com/map1");

        AggregationContact contact1 = new AggregationContact();
        contact1.setPhoneList(Arrays.asList("1234567890"));
        shop1.setContact(contact1);

        City city1 = new City();
        city1.setId(1);
        city1.setName("北京");
        shop1.setCity(city1);

        shopCardMap.put(111L, shop1);

        // 第二个门店
        ShopCardDTO shop2 = new ShopCardDTO();
        shop2.setShopName("测试门店2");
        shop2.setAddress("测试地址2");
        shop2.setDistanceStr("1.2km");
        shop2.setDistanceValue(1200.0);
        shop2.setStarStr("4.0");
        shop2.setLat(11.12);
        shop2.setLng(101.12);
        shop2.setShopUrl("http://test.com/shop2");
        shop2.setMapUrl("http://test.com/map2");

        AggregationContact contact2 = new AggregationContact();
        contact2.setPhoneList(Arrays.asList("0987654321"));
        shop2.setContact(contact2);
        shop2.setCity(city1);

        shopCardMap.put(222L, shop2);

        response.setShopCardDTOMap(shopCardMap);
        when(dzThemeShopService.queryShopTheme(any())).thenReturn(response);
    }

    private void mockShopThemeResponseWithMagicalAttr() {
        ShopThemeResponse response = new ShopThemeResponse();
        response.setSuccess(true);

        Map<Long, ShopCardDTO> shopCardMap = Maps.newHashMap();
        ShopCardDTO shop = new ShopCardDTO();
        shop.setShopName("测试门店");
        shop.setAddress("测试地址");
        shop.setDistanceStr("500m");
        shop.setDistanceValue(500.0);
        shop.setStarStr("4.5");
        shop.setLat(11.11);
        shop.setLng(101.11);
        shop.setShopUrl("http://test.com/shop");

        // 添加神会员属性
        AttrItemDTO magicalAttr = new AttrItemDTO();
        magicalAttr.setAttrName("shopMagicalInfoDTO");
        magicalAttr.setAttrValue("{\"shopId\":111,\"applyResult\":1}");

        // 添加营业时间属性
        AttrItemDTO businessAttr = new AttrItemDTO();
        businessAttr.setAttrName("shopBusinessInfoDTO");
        businessAttr.setAttrValue("{\"businessHour\":\"09:00-22:00\",\"todayFlag\":\"************************************************\"}");

        shop.setAttrs(Arrays.asList(magicalAttr, businessAttr));

        // 添加标签
        Label magicalLabel = new Label();
        magicalLabel.setTagId(20527L); // 神会员标签ID
        magicalLabel.setTitle("神会员");
        magicalLabel.setType(1);
        magicalLabel.setBgColor("#FF0000");
        magicalLabel.setTextColor("#FFFFFF");
        magicalLabel.setTextSize(12);
        magicalLabel.setHeight(20);
        magicalLabel.setBorderWidth(1.0);
        magicalLabel.setBorderColor("#000000");
        magicalLabel.setRoundCornerRadius(Arrays.asList(2.0, 2.0, 2.0, 2.0));
        magicalLabel.setPaddingHorizontal(5.0);
        shop.setLabels(Arrays.asList(magicalLabel));

        AggregationContact contact = new AggregationContact();
        contact.setPhoneList(Arrays.asList("1234567890"));
        shop.setContact(contact);

        City city = new City();
        city.setId(1);
        city.setName("北京");
        shop.setCity(city);

        shopCardMap.put(111L, shop);
        response.setShopCardDTOMap(shopCardMap);

        when(dzThemeShopService.queryShopTheme(any())).thenReturn(response);
    }

    private void mockMagicalDealResponse(boolean isMagical) {
        if (isMagical) {
            PromotionTagApplyInfoResponse response = new PromotionTagApplyInfoResponse();
            response.setBlackTrafficFlag(false);
            response.setPassMatrix(true);

            Map<Long, PromotionTagResponseUnit> unitMap = Maps.newHashMap();
            PromotionTagResponseUnit unit = new PromotionTagResponseUnit();
            unit.setApplyResult(1);
            unitMap.put(1L, unit);

            response.setUnitResultMap(unitMap);
            when(iMagicalPromotionMemberTagService.queryMagicalMemberTagApplyInfo(any())).thenReturn(response);
        } else {
            when(iMagicalPromotionMemberTagService.queryMagicalMemberTagApplyInfo(any())).thenReturn(null);
        }
    }

    private void mockShopMapping() {
        Map<Long, List<Long>> dp2Mt = Maps.newHashMap();
        dp2Mt.put(111L, Arrays.asList(111L));
        dp2Mt.put(222L, Arrays.asList(222L));
        when(mapperWrapper.getMtByDpShopIds(any())).thenReturn(dp2Mt);
    }

    private void mockDealCategory() {
        when(dealGroupWrapper.getCategoryId(anyInt())).thenReturn(123);
    }

    private void mockShopBookInfo() {
        QueryShopBookInfoResponse response = mock(QueryShopBookInfoResponse.class);
        when(response.isSuccess()).thenReturn(true);

        ShopBookInfoQueryResultDTO data = mock(ShopBookInfoQueryResultDTO.class);

        ShopBookInfoDTO bookInfo1 = new ShopBookInfoDTO();
        bookInfo1.setMtShopId(111L);
        bookInfo1.setDealgroupSupportBook(true);

        ShopBookInfoDTO bookInfo2 = new ShopBookInfoDTO();
        bookInfo2.setMtShopId(222L);
        bookInfo2.setDealgroupSupportBook(false);

        when(data.getShopBookInfos()).thenReturn(Arrays.asList(bookInfo1, bookInfo2));
        when(response.getData()).thenReturn(data);

        when(shopBookInfoQueryService.queryShopBookInfo(any())).thenReturn(response);
    }

    private void mockBaseStateQuery() {
        QueryBaseStateResponse response = mock(QueryBaseStateResponse.class);
        when(response.isSuccess()).thenReturn(true);

        BaseStateQueryResultDTO data = mock(BaseStateQueryResultDTO.class);

        // 模拟今天有可预订时间
        BaseStateDTO baseState = new BaseStateDTO();
        baseState.setDay("2024-01-01");

        // 创建Subject对象
        StateSubjectDTO subject = new StateSubjectDTO();
        Map<com.sankuai.spt.statequery.api.enums.IdTypeEnum, Long> subjectId = Maps.newHashMap();
        subjectId.put(com.sankuai.spt.statequery.api.enums.IdTypeEnum.SHOP_ID, 111L);
        subject.setSubjectId(subjectId);
        baseState.setSubject(subject);

        TimeSliceDTO timeSlice = new TimeSliceDTO();
        timeSlice.setAvailable(true);
        timeSlice.setStartTime("10:00");
        baseState.setTimeSlices(Arrays.asList(timeSlice));

        when(data.getBaseStates()).thenReturn(Arrays.asList(baseState));
        when(response.getData()).thenReturn(data);

        when(baseStateQueryService.queryBaseState(any())).thenReturn(response);
    }

    private void mockUserMapping() {
        when(mapperWrapper.preUserInfoByDpUserId(anyLong())).thenReturn(null);
        when(mapperWrapper.preDpIdMtIdMapper(anyInt())).thenReturn(null);
        when(mapperWrapper.getMtIdByDpIdMtIdMapper(any())).thenReturn(12345);
    }

}
