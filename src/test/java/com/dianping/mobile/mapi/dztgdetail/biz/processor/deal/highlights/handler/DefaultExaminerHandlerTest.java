package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.BaseDisplayItemVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CommonAttrVO;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

public class DefaultExaminerHandlerTest {

    private DefaultExaminerHandler defaultExaminerHandler;

    /**
     * 测试displayItemList为空的情况
     */
    @Test
    public void testBuildDisplayItemWhenDisplayItemListIsNull() throws Throwable {
        DefaultExaminerHandler defaultExaminerHandler = new DefaultExaminerHandler();
        List<BaseDisplayItemVO> displayItemList = null;
        List<CommonAttrVO> attrs = new ArrayList<>();
        Method method = DefaultExaminerHandler.class.getDeclaredMethod("buildDisplayItem", List.class, List.class);
        method.setAccessible(true);
        method.invoke(defaultExaminerHandler, displayItemList, attrs);
        assertTrue(attrs.isEmpty());
    }

    /**
     * 测试displayItemList的大小为1的情况
     */
    @Test
    public void testBuildDisplayItemWhenDisplayItemListSizeIsOne() throws Throwable {
        DefaultExaminerHandler defaultExaminerHandler = new DefaultExaminerHandler();
        List<BaseDisplayItemVO> displayItemList = new ArrayList<>();
        BaseDisplayItemVO baseDisplayItemVO = mock(BaseDisplayItemVO.class);
        when(baseDisplayItemVO.getName()).thenReturn("test");
        displayItemList.add(baseDisplayItemVO);
        List<CommonAttrVO> attrs = new ArrayList<>();
        Method method = DefaultExaminerHandler.class.getDeclaredMethod("buildDisplayItem", List.class, List.class);
        method.setAccessible(true);
        method.invoke(defaultExaminerHandler, displayItemList, attrs);
        assertEquals(1, attrs.size());
        // Corrected expected value
        assertEquals("test", attrs.get(0).getValue());
        assertEquals("重点检查", attrs.get(0).getName());
    }

    /**
     * 测试displayItemList的大小大于1的情况
     */
    @Test
    public void testBuildDisplayItemWhenDisplayItemListSizeIsGreaterThanOne() throws Throwable {
        DefaultExaminerHandler defaultExaminerHandler = new DefaultExaminerHandler();
        List<BaseDisplayItemVO> displayItemList = new ArrayList<>();
        BaseDisplayItemVO baseDisplayItemVO1 = mock(BaseDisplayItemVO.class);
        when(baseDisplayItemVO1.getName()).thenReturn("test1");
        displayItemList.add(baseDisplayItemVO1);
        BaseDisplayItemVO baseDisplayItemVO2 = mock(BaseDisplayItemVO.class);
        when(baseDisplayItemVO2.getName()).thenReturn("test2");
        displayItemList.add(baseDisplayItemVO2);
        List<CommonAttrVO> attrs = new ArrayList<>();
        Method method = DefaultExaminerHandler.class.getDeclaredMethod("buildDisplayItem", List.class, List.class);
        method.setAccessible(true);
        method.invoke(defaultExaminerHandler, displayItemList, attrs);
        assertEquals(1, attrs.size());
        // Corrected expected value
        assertEquals("test1等", attrs.get(0).getValue());
        assertEquals("重点检查2项", attrs.get(0).getName());
    }
}
