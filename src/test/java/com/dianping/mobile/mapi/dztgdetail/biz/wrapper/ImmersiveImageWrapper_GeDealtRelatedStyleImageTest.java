package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.nailstyle.NailStyleItemVO;
import com.dianping.mobile.mapi.dztgdetail.entity.QueryExhibitImageParam;
import com.sankuai.mpmctcontent.application.thrift.dto.content.ListItemDTO;
import com.sankuai.mpmctcontent.application.thrift.dto.content.LoadDealRelatedCaseListRespDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.concurrent.Future;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ImmersiveImageWrapper_GeDealtRelatedStyleImageTest {

    @InjectMocks
    private ImmersiveImageWrapper immersiveImageWrapper;

    @Mock
    private Future future;

    @Mock
    private LoadDealRelatedCaseListRespDTO respDTO;

    private QueryExhibitImageParam createDummyQueryExhibitImageParam() {
        return QueryExhibitImageParam.builder().dpDealGroupId(1).categoryId(2).start(3).limit(4).shopId(5L).selectValue("test").cityId(6).userLng(7.0).userLat(8.0).clientType(9).serviceType("testType").dealGroupStatus(10).build();
    }

    /**
     * Tests the scenario where preGetImmersiveImage returns null.
     */
    @Test
    public void testGeDealtRelatedStyleImageWhenPreGetImmersiveImageReturnsNull() throws Throwable {
        QueryExhibitImageParam param = createDummyQueryExhibitImageParam();
        EnvCtx envCtx = new EnvCtx();
        assertNull(immersiveImageWrapper.geDealtRelatedStyleImage(param, envCtx, 1));
    }

    /**
     * Tests the scenario where getImmersiveImageResponse returns null.
     */
    @Test
    public void testGeDealtRelatedStyleImageWhenGetImmersiveImageResponseReturnsNull() throws Throwable {
        QueryExhibitImageParam param = createDummyQueryExhibitImageParam();
        EnvCtx envCtx = new EnvCtx();
        assertNull(immersiveImageWrapper.geDealtRelatedStyleImage(param, envCtx, 1));
    }
}
