package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.dto.TypeHierarchyView;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

public class DealCtxHelperTest1 {

    @Mock
    private DealCtx dealCtx;
    
    @Mock
    private DpPoiDTO dpPoiDTO;
    
    @Mock
    private MtPoiDTO mtPoiDTO;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetPoiSecBackCateId_WithDpPoiDTO_HasMultipleCategories() {
        // 准备测试数据
        List<DpPoiBackCategoryDTO> backCategories = Arrays.asList(
                createDpPoiBackCategoryDTO(100),
                createDpPoiBackCategoryDTO(200),
                createDpPoiBackCategoryDTO(300)
        );
        
        // 设置模拟对象行为
        when(dealCtx.isMt()).thenReturn(false);
        when(dealCtx.getDpPoiDTO()).thenReturn(dpPoiDTO);
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(backCategories);
        
        // 执行测试
        int result = DealCtxHelper.getPoiSecBackCateId(dealCtx);
        
        // 验证结果
        assertEquals(200, result);
    }
    
    @Test
    public void testGetPoiSecBackCateId_WithDpPoiDTO_HasOneCategory() {
        // 准备测试数据
        List<DpPoiBackCategoryDTO> backCategories = Collections.singletonList(
                createDpPoiBackCategoryDTO(100)
        );
        
        // 设置模拟对象行为
        when(dealCtx.isMt()).thenReturn(false);
        when(dealCtx.getDpPoiDTO()).thenReturn(dpPoiDTO);
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(backCategories);
        
        // 执行测试
        int result = DealCtxHelper.getPoiSecBackCateId(dealCtx);
        
        // 验证结果
        assertEquals(0, result);
    }
    
    @Test
    public void testGetPoiSecBackCateId_WithDpPoiDTO_EmptyCategories() {
        // 设置模拟对象行为
        when(dealCtx.isMt()).thenReturn(false);
        when(dealCtx.getDpPoiDTO()).thenReturn(dpPoiDTO);
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(new ArrayList<>());
        
        // 执行测试
        int result = DealCtxHelper.getPoiSecBackCateId(dealCtx);
        
        // 验证结果
        assertEquals(0, result);
    }
    
    @Test
    public void testGetPoiSecBackCateId_WithMtPoiDTO_HasMultipleCategories() {
        // 准备测试数据
        List<TypeHierarchyView> typeHierarchy = Arrays.asList(
                createTypeHierarchyView(300),
                createTypeHierarchyView(200),
                createTypeHierarchyView(100)
        );
        
        // 设置模拟对象行为
        when(dealCtx.isMt()).thenReturn(true);
        when(dealCtx.getMtPoiDTO()).thenReturn(mtPoiDTO);
        when(mtPoiDTO.getTypeHierarchy()).thenReturn(typeHierarchy);
        
        // 执行测试
        int result = DealCtxHelper.getPoiSecBackCateId(dealCtx);
        
        // 验证结果
        assertEquals(200, result);
    }
    
    @Test
    public void testGetPoiSecBackCateId_WithMtPoiDTO_HasOneCategory() {
        // 准备测试数据
        List<TypeHierarchyView> typeHierarchy = Collections.singletonList(
                createTypeHierarchyView(100)
        );
        
        // 设置模拟对象行为
        when(dealCtx.isMt()).thenReturn(true);
        when(dealCtx.getMtPoiDTO()).thenReturn(mtPoiDTO);
        when(mtPoiDTO.getTypeHierarchy()).thenReturn(typeHierarchy);
        
        // 执行测试
        int result = DealCtxHelper.getPoiSecBackCateId(dealCtx);
        
        // 验证结果
        assertEquals(0, result);
    }
    
    @Test
    public void testGetPoiSecBackCateId_WithMtPoiDTO_EmptyCategories() {
        // 设置模拟对象行为
        when(dealCtx.isMt()).thenReturn(true);
        when(dealCtx.getMtPoiDTO()).thenReturn(mtPoiDTO);
        when(mtPoiDTO.getTypeHierarchy()).thenReturn(new ArrayList<>());
        
        // 执行测试
        int result = DealCtxHelper.getPoiSecBackCateId(dealCtx);
        
        // 验证结果
        assertEquals(0, result);
    }
    
    @Test
    public void testGetPoiSecBackCateId_WithNullPoiDTO() {
        // 设置模拟对象行为
        when(dealCtx.isMt()).thenReturn(true);
        when(dealCtx.getMtPoiDTO()).thenReturn(null);
        
        // 执行测试
        int result = DealCtxHelper.getPoiSecBackCateId(dealCtx);
        
        // 验证结果
        assertEquals(0, result);
    }
    
    // 辅助方法，创建DpPoiBackCategoryDTO对象
    private DpPoiBackCategoryDTO createDpPoiBackCategoryDTO(int categoryId) {
        DpPoiBackCategoryDTO dto = new DpPoiBackCategoryDTO();
        dto.setCategoryId(categoryId);
        return dto;
    }
    
    // 辅助方法，创建TypeHierarchyView对象
    private TypeHierarchyView createTypeHierarchyView(int id) {
        TypeHierarchyView view = new TypeHierarchyView();
        view.setId(id);
        return view;
    }

    @Test
    public void testGetPoiBackCateIdList_MtPoi_Success() {
        // 准备测试数据
        List<TypeHierarchyView> typeHierarchy = new ArrayList<>();
        TypeHierarchyView view1 = new TypeHierarchyView();
        view1.setId(100);
        TypeHierarchyView view2 = new TypeHierarchyView();
        view2.setId(200);
        TypeHierarchyView view3 = new TypeHierarchyView();
        view3.setId(300);
        typeHierarchy.add(view1);
        typeHierarchy.add(view2);
        typeHierarchy.add(view3);

        // 设置Mock行为
        when(dealCtx.isMt()).thenReturn(true);
        when(dealCtx.getMtPoiDTO()).thenReturn(mtPoiDTO);
        when(mtPoiDTO.getTypeHierarchy()).thenReturn(typeHierarchy);

        // 执行测试
        List<Integer> result = DealCtxHelper.getPoiBackCateIdList(dealCtx);

        // 验证结果
        assertEquals(3, result.size());

    }

    @Test
    public void testGetPoiBackCateIdList_DpPoi_Success() {
        // 准备测试数据
        List<DpPoiBackCategoryDTO> backCategories = new ArrayList<>();
        DpPoiBackCategoryDTO category1 = new DpPoiBackCategoryDTO();
        category1.setCategoryId(100);
        DpPoiBackCategoryDTO category2 = new DpPoiBackCategoryDTO();
        category2.setCategoryId(200);
        backCategories.add(category1);
        backCategories.add(category2);

        // 设置Mock行为
        when(dealCtx.isMt()).thenReturn(false);
        when(dealCtx.getDpPoiDTO()).thenReturn(dpPoiDTO);
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(backCategories);

        // 执行测试
        List<Integer> result = DealCtxHelper.getPoiBackCateIdList(dealCtx);

        // 验证结果
        assertEquals(2, result.size());

    }

    @Test
    public void testGetPoiBackCateIdList_MtPoi_NullTypeHierarchy() {
        // 设置Mock行为
        when(dealCtx.isMt()).thenReturn(true);
        when(dealCtx.getMtPoiDTO()).thenReturn(mtPoiDTO);
        when(mtPoiDTO.getTypeHierarchy()).thenReturn(null);

        // 执行测试
        List<Integer> result = DealCtxHelper.getPoiBackCateIdList(dealCtx);

        // 验证结果
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetPoiBackCateIdList_MtPoi_EmptyTypeHierarchy() {
        // 设置Mock行为
        when(dealCtx.isMt()).thenReturn(true);
        when(dealCtx.getMtPoiDTO()).thenReturn(mtPoiDTO);
        when(mtPoiDTO.getTypeHierarchy()).thenReturn(Collections.emptyList());

        // 执行测试
        List<Integer> result = DealCtxHelper.getPoiBackCateIdList(dealCtx);

        // 验证结果
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetPoiBackCateIdList_DpPoi_NullBackMainCategoryPath() {
        // 设置Mock行为
        when(dealCtx.isMt()).thenReturn(false);
        when(dealCtx.getDpPoiDTO()).thenReturn(dpPoiDTO);
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(null);

        // 执行测试
        List<Integer> result = DealCtxHelper.getPoiBackCateIdList(dealCtx);

        // 验证结果
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetPoiBackCateIdList_NullPoiDTO() {
        // 设置Mock行为
        when(dealCtx.isMt()).thenReturn(true);
        when(dealCtx.getMtPoiDTO()).thenReturn(null);

        // 执行测试
        List<Integer> result = DealCtxHelper.getPoiBackCateIdList(dealCtx);

        // 验证结果
        assertTrue(result.isEmpty());
    }
}