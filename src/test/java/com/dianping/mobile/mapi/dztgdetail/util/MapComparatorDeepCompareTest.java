package com.dianping.mobile.mapi.dztgdetail.util;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MapComparatorDeepCompareTest {

    /**
     * 测试 map1 和 map2 为同一对象的情况
     */
    @Test
    public void testDeepCompareSameObject() throws Throwable {
        // arrange
        Map<String, String> map = new HashMap<>();
        map.put("key", "value");
        Set<String> ignoreKeys = new HashSet<>();
        // act & assert
        assertTrue(MapComparator.deepCompare(map, map, ignoreKeys, null,null, false));
    }

    /**
     * 测试 map1 和 map2 均为 null 的情况
     */
    @Test
    public void testDeepCompareBothNull() throws Throwable {
        // arrange
        Set<String> ignoreKeys = new HashSet<>();
        // act & assert
        assertTrue(MapComparator.deepCompare(null, null, ignoreKeys, null,null, false));
    }

    /**
     * 测试其中一个 map 为 null 的情况
     */
    @Test
    public void testDeepCompareOneNull() throws Throwable {
        // arrange
        Map<String, String> map = new HashMap<>();
        map.put("key", "value");
        Set<String> ignoreKeys = new HashSet<>();
        // act & assert
        assertFalse(MapComparator.deepCompare(map, null, ignoreKeys, null,null, false));
        assertFalse(MapComparator.deepCompare(null, map, ignoreKeys, null,null, false));
    }

    /**
     * 测试忽略键后 map 内容相等的情况
     */
    @Test
    public void testDeepCompareWithIgnoreKeys() throws Throwable {
        // arrange
        Map<String, String> map1 = new HashMap<>();
        map1.put("key1", "value1");
        map1.put("key2", "value2");
        Map<String, String> map2 = new HashMap<>();
        map2.put("key1", "value1");
        map2.put("key2", "differentValue");
        Set<String> ignoreKeys = new HashSet<>();
        ignoreKeys.add("key2");
        // act & assert
        assertTrue(MapComparator.deepCompare(map1, map2, ignoreKeys, null,null, false));
    }

    /**
     * 测试忽略键后 map 仍然不相等的情况
     */
    @Test
    public void testDeepCompareWithIgnoreKeysStillNotEqual() throws Throwable {
        // arrange
        Map<String, String> map1 = new HashMap<>();
        map1.put("key1", "value1");
        map1.put("key2", "value2");
        Map<String, String> map2 = new HashMap<>();
        map2.put("key1", "differentValue");
        map2.put("key2", "value2");
        Set<String> ignoreKeys = new HashSet<>();
        ignoreKeys.add("key2");
        // act & assert
        assertFalse(MapComparator.deepCompare(map1, map2, ignoreKeys, null,null, false));
    }

    /**
     * 测试 ignoreKeys 为 null 的情况
     */
    @Test
    public void testDeepCompareIgnoreKeysNull() throws Throwable {
        // arrange
        Map<String, String> map1 = new HashMap<>();
        map1.put("key", "value");
        Map<String, String> map2 = new HashMap<>();
        map2.put("key", "value");
        // act & assert
        assertTrue(MapComparator.deepCompare(map1, map2,null, null, false));
    }

    /**
     * 测试复杂嵌套 Map 的比较
     */
    @Test
    public void testDeepCompareNestedMaps() throws Throwable {
        // arrange
        Map<String, Object> map1 = new HashMap<>();
        Map<String, String> nestedMap1 = new HashMap<>();
        nestedMap1.put("nested1", "value1");
        map1.put("key1", nestedMap1);
        map1.put("key2", "value2");
        Map<String, Object> map2 = new HashMap<>();
        Map<String, String> nestedMap2 = new HashMap<>();
        nestedMap2.put("nested1", "value1");
        map2.put("key1", nestedMap2);
        map2.put("key2", "differentValue");
        Set<String> ignoreKeys = new HashSet<>();
        ignoreKeys.add("key2");
        // act & assert
        assertTrue(MapComparator.deepCompare(map1, map2, ignoreKeys, null,null, false));
    }
}
