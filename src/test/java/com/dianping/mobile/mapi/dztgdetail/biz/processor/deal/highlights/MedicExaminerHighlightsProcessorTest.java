package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import static org.mockito.Mockito.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEvent;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.DefaultExaminerHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.EntryExaminerHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.HealthCertificateExaminerHandler;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class MedicExaminerHighlightsProcessorTest {

    @InjectMocks
    private MedicExaminerHighlightsProcessor medicExaminerHighlightsProcessor;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private ApplicationEvent applicationEvent;

    @Before
    public void setUp() {
        when(applicationContext.getBean(DefaultExaminerHandler.class)).thenReturn(new DefaultExaminerHandler());
        when(applicationContext.getBean(EntryExaminerHandler.class)).thenReturn(new EntryExaminerHandler());
        when(applicationContext.getBean(HealthCertificateExaminerHandler.class)).thenReturn(new HealthCertificateExaminerHandler());
    }

    /**
     * 测试 onApplicationEvent 方法是否正确添加了键值对
     */
    @Test
    @Ignore
    public void testOnApplicationEvent() {
        // act
        medicExaminerHighlightsProcessor.onApplicationEvent(applicationEvent);
        // assert
        // 由于 EXAMINER_SERVER_TYPE_HANDLER_MAP 是静态变量，我们无法直接访问它来进行断言
        // 所以这里我们只能断言没有抛出异常，表示方法执行成功
    }
}
