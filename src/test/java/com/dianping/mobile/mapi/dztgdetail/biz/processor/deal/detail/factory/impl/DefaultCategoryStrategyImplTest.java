package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.impl;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

/**
 * @author: created by hang.yu on 2024/4/29 17:20
 */
@RunWith(MockitoJUnitRunner.class)
public class DefaultCategoryStrategyImplTest {

    @InjectMocks
    private DefaultCategoryStrategyImpl defaultCategoryStrategy;

    @Test
    public void getSimilarDealModuleAbConfig() {
        ModuleAbConfig result = defaultCategoryStrategy.getSimilarDealModuleAbConfig(null);
        Assert.assertNull(result);
    }

}