package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.util.Collections;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Date: 2024/9/10
 */
public class CustomAtmosphereUtilsTest {
    private MockedStatic<Lion> lionMocked;

    @Before
    public void setUp() {
        lionMocked = Mockito.mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        lionMocked.close();
    }

    @Test
    public void testDistributeStandardProductAtmosphere() {
        Map<String, String> atmosphereConfig = Maps.newHashMap();
        atmosphereConfig.put("spu_atmosphere_enable", "true");
        lionMocked.when(() -> Lion.getMap(LionConstants.APP_KEY, LionConstants.STANDARD_PRODUCT_ATMOSPHERE_CONFIG, String.class, Collections.emptyMap())).thenReturn(atmosphereConfig);
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setRequestSource("mlive");
        ctx.setAttrs(Lists.newArrayList());
        boolean result = CustomAtmosphereUtils.distributeSuperDealAtmosphere(ctx);
        Assert.assertTrue(!result);
    }

    @Test
    public void testBuildStandardProductAtmosphereBar() {
        DealGroupPBO dealGroupPBO = new DealGroupPBO();
        Map<String, String> atmosphereConfig = Maps.newHashMap();
        atmosphereConfig.put("spu_atmosphere_bgurl", "https://xxx.jpg");
        lionMocked.when(() -> Lion.getMap(LionConstants.APP_KEY, LionConstants.STANDARD_PRODUCT_ATMOSPHERE_CONFIG, String.class, Collections.emptyMap())).thenReturn(atmosphereConfig);
        CustomAtmosphereUtils.buildSuperDealAtmosphereBar(dealGroupPBO);
        Assert.assertTrue(dealGroupPBO.getDealAtmosphereBarModules().get(0).getBaseMapUrl().equals("https://xxx.jpg"));
    }
}
