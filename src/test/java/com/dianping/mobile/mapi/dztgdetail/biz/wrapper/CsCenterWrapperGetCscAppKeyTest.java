package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.constants.CsCenterAccessAppKey;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import java.lang.reflect.Method;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

public class CsCenterWrapperGetCscAppKeyTest {

    private CsCenterWrapper csCenterWrapper;

    private DealCtx dealCtx;

    private EnvCtx envCtx;

    private Method getCscAppKeyMethod;

    @Before
    public void setUp() throws Exception {
        csCenterWrapper = new CsCenterWrapper();
        dealCtx = Mockito.mock(DealCtx.class);
        envCtx = Mockito.mock(EnvCtx.class);
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        // Get private method using reflection
        getCscAppKeyMethod = CsCenterWrapper.class.getDeclaredMethod("getCscAppKey", DealCtx.class);
        getCscAppKeyMethod.setAccessible(true);
    }

    /**
     * Test case for Cleaning Self-Own Deal with MEITUAN_APP and not Harmony OS.
     */
    @Test
    public void testGetCscAppKey_CleaningSelfOwnDeal_MeituanApp_NotHarmony() throws Throwable {
        // arrange
        when(dealCtx.isCleaningSelfOwnDeal()).thenReturn(true);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_APP);
        when(envCtx.isHarmony()).thenReturn(false);
        // act
        String appKey = (String) getCscAppKeyMethod.invoke(csCenterWrapper, dealCtx);
        // assert
        assertEquals(CsCenterAccessAppKey.CLEANING_DEAL_DETAIL_MT_APP, appKey);
    }

    /**
     * Test case for Cleaning Self-Own Deal with MEITUAN_WEIXIN_MINIAPP.
     */
    @Test
    public void testGetCscAppKey_CleaningSelfOwnDeal_MeituanWeixinMiniapp() throws Throwable {
        // arrange
        when(dealCtx.isCleaningSelfOwnDeal()).thenReturn(true);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP);
        // act
        String appKey = (String) getCscAppKeyMethod.invoke(csCenterWrapper, dealCtx);
        // assert
        assertEquals(CsCenterAccessAppKey.CLEANING_DEAL_DETAIL_MT_MINI_PROGRAM, appKey);
    }

    /**
     * Test case for Cleaning Self-Own Deal with MEITUAN_I_STATION (mt_wap).
     */
    @Test
    public void testGetCscAppKey_CleaningSelfOwnDeal_MeituanIStation() throws Throwable {
        // arrange
        when(dealCtx.isCleaningSelfOwnDeal()).thenReturn(true);
        when(envCtx.getClientType()).thenReturn(ClientTypeEnum.mt_wap.getType());
        // act
        String appKey = (String) getCscAppKeyMethod.invoke(csCenterWrapper, dealCtx);
        // assert
        assertEquals(CsCenterAccessAppKey.CLEANING_DEAL_DETAIL_MT_I, appKey);
    }

    /**
     * Test case for Cleaning Self-Own Deal with MEITUAN_APP and Harmony OS.
     */
    @Test
    public void testGetCscAppKey_CleaningSelfOwnDeal_MeituanApp_Harmony() throws Throwable {
        // arrange
        when(dealCtx.isCleaningSelfOwnDeal()).thenReturn(true);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_APP);
        when(envCtx.isHarmony()).thenReturn(true);
        // act
        String appKey = (String) getCscAppKeyMethod.invoke(csCenterWrapper, dealCtx);
        // assert
        assertEquals(CsCenterAccessAppKey.CLEANING_DEAL_DETAIL_MT_HM_APP, appKey);
    }

    /**
     * Test case for Cleaning Self-Own Deal with DIANPING_APP and not Harmony OS.
     */
    @Test
    public void testGetCscAppKey_CleaningSelfOwnDeal_DianpingApp_NotHarmony() throws Throwable {
        // arrange
        when(dealCtx.isCleaningSelfOwnDeal()).thenReturn(true);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.DIANPING_APP);
        when(envCtx.isHarmony()).thenReturn(false);
        // act
        String appKey = (String) getCscAppKeyMethod.invoke(csCenterWrapper, dealCtx);
        // assert
        assertEquals(CsCenterAccessAppKey.CLEANING_DEAL_DETAIL_DP_APP, appKey);
    }

    /**
     * Test case for Cleaning Self-Own Deal with DIANPING_WEIXIN_MINIAPP.
     */
    @Test
    public void testGetCscAppKey_CleaningSelfOwnDeal_DianpingWeixinMiniapp() throws Throwable {
        // arrange
        when(dealCtx.isCleaningSelfOwnDeal()).thenReturn(true);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP);
        // act
        String appKey = (String) getCscAppKeyMethod.invoke(csCenterWrapper, dealCtx);
        // assert
        assertEquals(CsCenterAccessAppKey.CLEANING_DEAL_DETAIL_DP_MINI_PROGRAM, appKey);
    }

    /**
     * Test case for Cleaning Self-Own Deal with DIANPING_M_STATION (dp_wap).
     */
    @Test
    public void testGetCscAppKey_CleaningSelfOwnDeal_DianpingMStation() throws Throwable {
        // arrange
        when(dealCtx.isCleaningSelfOwnDeal()).thenReturn(true);
        when(envCtx.getClientType()).thenReturn(ClientTypeEnum.dp_wap.getType());
        // act
        String appKey = (String) getCscAppKeyMethod.invoke(csCenterWrapper, dealCtx);
        // assert
        assertEquals(CsCenterAccessAppKey.CLEANING_DEAL_DETAIL_DP_M, appKey);
    }

    /**
     * Test case for Cleaning Self-Own Deal with DIANPING_APP and Harmony OS.
     */
    @Test
    public void testGetCscAppKey_CleaningSelfOwnDeal_DianpingApp_Harmony() throws Throwable {
        // arrange
        when(dealCtx.isCleaningSelfOwnDeal()).thenReturn(true);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.DIANPING_APP);
        when(envCtx.isHarmony()).thenReturn(true);
        // act
        String appKey = (String) getCscAppKeyMethod.invoke(csCenterWrapper, dealCtx);
        // assert
        assertEquals(CsCenterAccessAppKey.CLEANING_DEAL_DETAIL_DP_HM_APP, appKey);
    }

    /**
     * Test case for Cleaning Self-Own Deal with default appKey.
     */
    @Test
    public void testGetCscAppKey_CleaningSelfOwnDeal_Default() throws Throwable {
        // arrange
        when(dealCtx.isCleaningSelfOwnDeal()).thenReturn(true);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.UNKNOWN);
        // act
        String appKey = (String) getCscAppKeyMethod.invoke(csCenterWrapper, dealCtx);
        // assert
        assertEquals(CsCenterAccessAppKey.CLEANING_DEAL_DETAIL_MT_APP, appKey);
    }

    /**
     * Test case for Care-Free Deal with MEITUAN_APP and not Harmony OS.
     */
    @Test
    public void testGetCscAppKey_CareFreeDeal_MeituanApp_NotHarmony() throws Throwable {
        // arrange
        when(dealCtx.isCleaningSelfOwnDeal()).thenReturn(false);
        when(dealCtx.isCareFreeDeal()).thenReturn(true);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_APP);
        when(envCtx.isHarmony()).thenReturn(false);
        // act
        String appKey = (String) getCscAppKeyMethod.invoke(csCenterWrapper, dealCtx);
        // assert
        assertEquals(CsCenterAccessAppKey.CAREFREE_DEAL_DETAIL_MT_APP, appKey);
    }

    /**
     * Test case for Care-Free Deal with MEITUAN_WEIXIN_MINIAPP.
     */
    @Test
    public void testGetCscAppKey_CareFreeDeal_MeituanWeixinMiniapp() throws Throwable {
        // arrange
        when(dealCtx.isCleaningSelfOwnDeal()).thenReturn(false);
        when(dealCtx.isCareFreeDeal()).thenReturn(true);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP);
        // act
        String appKey = (String) getCscAppKeyMethod.invoke(csCenterWrapper, dealCtx);
        // assert
        assertEquals(CsCenterAccessAppKey.CAREFREE_DEAL_DETAIL_MT_MINI_PROGRAM, appKey);
    }

    /**
     * Test case for Care-Free Deal with MEITUAN_I_STATION (mt_wap).
     */
    @Test
    public void testGetCscAppKey_CareFreeDeal_MeituanIStation() throws Throwable {
        // arrange
        when(dealCtx.isCleaningSelfOwnDeal()).thenReturn(false);
        when(dealCtx.isCareFreeDeal()).thenReturn(true);
        when(envCtx.getClientType()).thenReturn(ClientTypeEnum.mt_wap.getType());
        // act
        String appKey = (String) getCscAppKeyMethod.invoke(csCenterWrapper, dealCtx);
        // assert
        assertEquals(CsCenterAccessAppKey.CAREFREE_DEAL_DETAIL_MT_I, appKey);
    }

    /**
     * Test case for Care-Free Deal with MEITUAN_APP and Harmony OS.
     */
    @Test
    public void testGetCscAppKey_CareFreeDeal_MeituanApp_Harmony() throws Throwable {
        // arrange
        when(dealCtx.isCleaningSelfOwnDeal()).thenReturn(false);
        when(dealCtx.isCareFreeDeal()).thenReturn(true);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_APP);
        when(envCtx.isHarmony()).thenReturn(true);
        // act
        String appKey = (String) getCscAppKeyMethod.invoke(csCenterWrapper, dealCtx);
        // assert
        assertEquals(CsCenterAccessAppKey.CAREFREE_DEAL_DETAIL_MT_HM_APP, appKey);
    }

    /**
     * Test case for Care-Free Deal with DIANPING_APP and not Harmony OS.
     */
    @Test
    public void testGetCscAppKey_CareFreeDeal_DianpingApp_NotHarmony() throws Throwable {
        // arrange
        when(dealCtx.isCleaningSelfOwnDeal()).thenReturn(false);
        when(dealCtx.isCareFreeDeal()).thenReturn(true);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.DIANPING_APP);
        when(envCtx.isHarmony()).thenReturn(false);
        // act
        String appKey = (String) getCscAppKeyMethod.invoke(csCenterWrapper, dealCtx);
        // assert
        assertEquals(CsCenterAccessAppKey.CAREFREE_DEAL_DETAIL_DP_APP, appKey);
    }

    /**
     * Test case for Care-Free Deal with DIANPING_WEIXIN_MINIAPP.
     */
    @Test
    public void testGetCscAppKey_CareFreeDeal_DianpingWeixinMiniapp() throws Throwable {
        // arrange
        when(dealCtx.isCleaningSelfOwnDeal()).thenReturn(false);
        when(dealCtx.isCareFreeDeal()).thenReturn(true);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP);
        // act
        String appKey = (String) getCscAppKeyMethod.invoke(csCenterWrapper, dealCtx);
        // assert
        assertEquals(CsCenterAccessAppKey.CAREFREE_DEAL_DETAIL_DP_MINI_PROGRAM, appKey);
    }

    /**
     * Test case for Care-Free Deal with DIANPING_M_STATION (dp_wap).
     */
    @Test
    public void testGetCscAppKey_CareFreeDeal_DianpingMStation() throws Throwable {
        // arrange
        when(dealCtx.isCleaningSelfOwnDeal()).thenReturn(false);
        when(dealCtx.isCareFreeDeal()).thenReturn(true);
        when(envCtx.getClientType()).thenReturn(ClientTypeEnum.dp_wap.getType());
        // act
        String appKey = (String) getCscAppKeyMethod.invoke(csCenterWrapper, dealCtx);
        // assert
        assertEquals(CsCenterAccessAppKey.CAREFREE_DEAL_DETAIL_DP_M, appKey);
    }

    /**
     * Test case for Care-Free Deal with DIANPING_APP and Harmony OS.
     */
    @Test
    public void testGetCscAppKey_CareFreeDeal_DianpingApp_Harmony() throws Throwable {
        // arrange
        when(dealCtx.isCleaningSelfOwnDeal()).thenReturn(false);
        when(dealCtx.isCareFreeDeal()).thenReturn(true);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.DIANPING_APP);
        when(envCtx.isHarmony()).thenReturn(true);
        // act
        String appKey = (String) getCscAppKeyMethod.invoke(csCenterWrapper, dealCtx);
        // assert
        assertEquals(CsCenterAccessAppKey.CAREFREE_DEAL_DETAIL_DP_HM_APP, appKey);
    }

    /**
     * Test case for Care-Free Deal with default appKey.
     */
    @Test
    public void testGetCscAppKey_CareFreeDeal_Default() throws Throwable {
        // arrange
        when(dealCtx.isCleaningSelfOwnDeal()).thenReturn(false);
        when(dealCtx.isCareFreeDeal()).thenReturn(true);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.UNKNOWN);
        // act
        String appKey = (String) getCscAppKeyMethod.invoke(csCenterWrapper, dealCtx);
        // assert
        assertEquals(CsCenterAccessAppKey.CAREFREE_DEAL_DETAIL_MT_APP, appKey);
    }

    /**
     * Test case for neither Cleaning Self-Own Deal nor Care-Free Deal.
     */
    @Test
    public void testGetCscAppKey_NeitherCleaningNorCareFree() throws Throwable {
        // arrange
        when(dealCtx.isCleaningSelfOwnDeal()).thenReturn(false);
        when(dealCtx.isCareFreeDeal()).thenReturn(false);
        // act
        String appKey = (String) getCscAppKeyMethod.invoke(csCenterWrapper, dealCtx);
        // assert
        assertEquals("", appKey);
    }
}
