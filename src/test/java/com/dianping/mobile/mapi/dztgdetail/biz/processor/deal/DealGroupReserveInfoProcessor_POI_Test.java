package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.handler.SelfOperatedCleaningHandler;
import com.dianping.pigeon.remoting.common.codec.json.JacksonUtils;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;

import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({Lion.class})
public class DealGroupReserveInfoProcessor_POI_Test {
    @InjectMocks
    private DealGroupReserveInfoProcessor dealGroupReserveInfoProcessor;

    @Mock
    private SelfOperatedCleaningHandler selfOperatedCleaningHandler;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(Lion.class);
    }

    @Test
    public void testPrepare() {
        DealCtx dealCtx = buildDealCtx();
        dealCtx.setDealParam("123");
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(409);
        dealCtx.setChannelDTO(channelDTO);
        dealCtx.setRequestSource("pre_order_deal");


        when(Lion.getList(LionConstants.APP_KEY, LionConstants.PRE_ORDER_CATEGORY_IDS, String.class,Collections.emptyList()))
                .thenAnswer((Answer<List<String>>)invocations -> Lists.newArrayList("303","409"));

        when(selfOperatedCleaningHandler.isSelfOperatedCleaningDeal(dealCtx)).thenAnswer((Answer<Boolean>)invocations -> true);
        // DealCtx ctx,boolean originResult
        dealGroupReserveInfoProcessor.prepare(dealCtx);

        assert dealCtx.isCleanSelfOperationShop();
    }

    @Test
    public void testSelfOperatedCleanOrderReserveInfo() throws InvocationTargetException, IllegalAccessException {
        DealCtx dealCtx = buildDealCtx();
        dealCtx.setDealParam("123");
        when(Lion.getBoolean(LionConstants.APP_KEY, LionConstants.POI_SELF_CLEAN_ORDER_TYPE_SWITCH, false))
                .thenAnswer((Answer<Boolean>)invocations -> true);
        // DealCtx ctx,boolean originResult
        Method method = PowerMockito.method(DealGroupReserveInfoProcessor.class, "selfOperatedCleanOrderReserve");
        Boolean result = (Boolean) method.invoke(dealGroupReserveInfoProcessor,dealCtx,true);
        assert result;
    }

    private DealCtx buildDealCtx() {
        EnvCtx envCtx = JacksonUtils.deserialize(envJsonData, EnvCtx.class);
        BestShopDTO bestShopDTO = JacksonUtils.deserialize(bestShopJsonData, BestShopDTO.class);

        DealGroupDTO dealGroupDTO = JacksonUtils.deserialize(dealGroupJsonData, DealGroupDTO.class);

        List<AttrDTO> attrs = dealGroupDTO.getAttrs();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("pay_method");
        attrDTO.setValue(Lists.newArrayList("4"));
        attrs.add(attrDTO);

        List<ModuleAbConfig> moduleAbConfigs = JacksonUtils.deserialize(moduleConfigAbsJsonData, List.class);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setBestShopResp(bestShopDTO);
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.setModuleAbConfigs(moduleAbConfigs);

        ctx.setDpCityId(10);
        ctx.setMtCityId(10);

        ctx.setDpId(123);

        FutureCtx futureCtx = new FutureCtx();
        ctx.setFutureCtx(futureCtx);

        return ctx;
    }

    private static final String envJsonData = "{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx\",\"dpUserId\":9000000000147990022,\"dpVirtualUserId\":9000000000147990023,\"mtUserId\":**********,\"mtVirtualUserId\":**********,\"unionId\":\"35c09fb6517747329859124dea1c8b45a169782568014265070\",\"dpId\":\"35c09fb6517747329859124dea1c8b45a169782568014265070\",\"uuid\":\"000000000000035C09FB6517747329859124DEA1C8B45A169782568014265070\",\"version\":\"12.31.401\",\"clientType\":200502,\"mpAppId\":null,\"mpSource\":null,\"openId\":null,\"appDeviceId\":\"35c09fb6517747329859124dea1c8b45a169782568014265070\",\"appId\":10,\"mtsiFlag\":\"0\",\"requestURI\":\"/general/platform/dztgdetail/dzdealbase.bin\",\"userAgent\":\"MApi 1.4 (mtscope 12.31.401 appstore; iPhone 18.3.2 iPhone11,8; a0d0)\",\"userIp\":\"***************\",\"dztgClientTypeEnum\":\"MEITUAN_APP\",\"thirdDztgClientTypeEnum\":null,\"startTime\":1743410430127,\"meituanClientList\":[\"java.util.ArrayList\",[\"MEITUAN_APP\",\"MEITUAN_FROM_H5\",\"MEITUAN_MAP_APP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_LIVE_WEIXIN_MINIAPP\"]],\"dianpingClientList\":[\"java.util.ArrayList\",[\"DIANPING_APP\",\"DIANPING_FROM_H5\",\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"]],\"merchantClientList\":[\"java.util.ArrayList\",[\"DPMERCHANT\"]],\"mtMiniAppList\":[\"java.util.ArrayList\",[\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_KUAISHOU_MINIAPP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_LIVE_WEIXIN_MINIAPP\"]],\"dpMiniAppList\":[\"java.util.ArrayList\",[\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"]],\"nativeAppList\":[\"java.util.ArrayList\",[\"MEITUAN_APP\",\"MEITUAN_MAP_APP\",\"DIANPING_APP\"]],\"wxMiniList\":[\"java.util.ArrayList\",[\"MEITUAN_WEIXIN_MINIAPP\",\"DIANPING_WEIXIN_MINIAPP\"]],\"virtualUserId\":**********,\"dpMerchant\":false,\"mtWxMainMini\":false,\"externalAndNoScene\":false,\"mtLiveMinApp\":false,\"wxMini\":false,\"thirdPlatform\":false,\"mtMiniApp\":false,\"dpMiniApp\":false,\"userId\":**********,\"dp\":false,\"ios\":true,\"mainApp\":true,\"miniApp\":false,\"android\":false,\"harmony\":false,\"mt\":true,\"apollo\":false,\"fromH5\":false,\"login\":true,\"mainWeb\":false,\"mainWX\":false,\"external\":false,\"native\":true}";
    private static final String bestShopJsonData = "{\"@class\":\"com.dianping.deal.shop.dto.BestShopDTO\",\"mtShopId\":607534207,\"dpShopId\":607534207,\"phoneNos\":[\"java.util.ArrayList\",[]],\"lat\":31.219265826967963,\"lng\":121.36678440710057,\"glat\":31.217405,\"glng\":121.371412,\"shopName\":\"按摩/足疗测试门店_1711683890452\",\"branchName\":null,\"address\":\"蒲松北路\",\"distance\":\"16.13km\",\"shopPower\":0,\"totalShopsNum\":1,\"showType\":\"entertainment\",\"shopType\":30,\"shopPic\":\"http://p0.meituan.net/searchscenerec/ef6b05f1ab9b4cf6f1823d6c0b784570393332.png%40300w_0e_1l\"}";
    private static final String abConfigJsonData = "{\"key\":\"MtCreditPayExp\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"EXP2025032100002\",\"expResult\":\"c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"23ce22bc-94c8-494a-981f-c080c3d2abdb\\\",\\\"ab_id\\\":\\\"EXP2025032100002_c\\\"}\",\"useNewStyle\":false}]]}";
    private static final String dealGroupJsonData = "{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupDTO\",\"dpDealGroupId\":1038827787,\"mtDealGroupId\":1038827787,\"basic\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO\",\"categoryId\":303,\"title\":\"222\",\"brandName\":\"按摩/足疗测试门店_1711683890452\",\"titleDesc\":\"仅售700元，价值1319元222！\",\"beginSaleDate\":\"2025-03-05 15:10:53\",\"endSaleDate\":\"2026-03-05 15:10:32\",\"status\":0,\"saleChannel\":0,\"salePlatform\":3,\"sourceId\":102,\"tradeType\":19,\"platformCategoryId\":80303},\"image\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupImageDTO\",\"defaultPicPath\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/f65f85522c9f7363252597c56b0b4c45105689.jpg\",\"allPicPaths\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/f65f85522c9f7363252597c56b0b4c45105689.jpg\",\"videoPath\":null,\"videoCoverPath\":null,\"videoSize\":null,\"extendVideos\":null,\"allVideos\":null},\"category\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO\",\"categoryId\":303,\"serviceType\":\"精油SPA\",\"serviceTypeId\":108009,\"platformCategoryId\":80303},\"bgBu\":null,\"serviceProject\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO\",\"title\":\"团购详情\",\"salePrice\":\"700.0\",\"marketPrice\":\"439.67\",\"mustGroups\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO\",\"groups\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO\",\"skuId\":0,\"categoryId\":2104613,\"name\":null,\"amount\":1,\"marketPrice\":null,\"status\":10,\"attrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":3108,\"attrName\":\"serviceTechnique\",\"chnName\":\"服务手法\",\"attrValue\":\"精油SPA\",\"rawAttrValue\":\"精油SPA\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":3022,\"attrName\":\"serviceProcessArrayNew\",\"chnName\":\"服务流程\",\"attrValue\":\"[{\\\"servicemethod\\\":\\\"222\\\",\\\"stepTime\\\":\\\"22\\\"}]\",\"rawAttrValue\":\"[{\\\"servicemethod\\\":\\\"222\\\",\\\"stepTime\\\":\\\"22\\\"}]\",\"unit\":null,\"valueType\":300,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":2441,\"attrName\":\"serviceDurationInt\",\"chnName\":\"服务时长\",\"attrValue\":\"222\",\"rawAttrValue\":\"222\",\"unit\":null,\"valueType\":401,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":3109,\"attrName\":\"serviceBodyRange\",\"chnName\":\"具体服务部位\",\"attrValue\":\"面部\",\"rawAttrValue\":\"面部\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":184471,\"attrName\":\"bodyRegion\",\"chnName\":\"服务部位范围\",\"attrValue\":\"局部部位\",\"rawAttrValue\":\"局部部位\",\"unit\":null,\"valueType\":500,\"sequence\":0},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO\",\"metaAttrId\":2730,\"attrName\":\"skuCateId\",\"chnName\":\"项目分类\",\"attrValue\":\"2104613\",\"rawAttrValue\":\"2104613\",\"unit\":null,\"valueType\":402,\"sequence\":0}]]}]]}]],\"optionGroups\":[\"java.util.ArrayList\",[]],\"structType\":\"uniform-structure-table\"},\"channel\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupChannelDTO\",\"channelId\":3,\"channelEn\":\"joy\",\"channelCn\":\"休闲娱乐\",\"channelGroupId\":2,\"channelGroupEn\":\"general\",\"channelGroupCn\":\"到店综合\"},\"attrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_finish_date\",\"value\":[\"java.util.ArrayList\",[\"1970-01-01 08:00:00\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"calc_holiday_available\",\"value\":[\"java.util.ArrayList\",[\"1\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_business_type\",\"value\":[\"java.util.ArrayList\",[\"1\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_channel_id_allowed\",\"value\":[\"java.util.ArrayList\",[\"0\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"pay_method\",\"value\":[\"java.util.ArrayList\",[\"4\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_can_use_coupon\",\"value\":[\"java.util.ArrayList\",[\"true\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"preSaleTag\",\"value\":[\"java.util.ArrayList\",[\"false\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_third_party_verify\",\"value\":[\"java.util.ArrayList\",[\"false\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_block_stock\",\"value\":[\"java.util.ArrayList\",[\"false\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"product_discount_rule_id\",\"value\":[\"java.util.ArrayList\",[\"0\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"service_type\",\"value\":[\"java.util.ArrayList\",[\"精油SPA\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"tag_unifyProduct\",\"value\":[\"java.util.ArrayList\",[\"0\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"single_verification_quantity_desc\",\"value\":[\"java.util.ArrayList\",[\"单次到店仅可核销一次，仅能一人使用\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"reservation_is_needed_or_not\",\"value\":[\"java.util.ArrayList\",[\"否\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"sys_deal_universal_type\",\"value\":[\"java.util.ArrayList\",[\"1\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"sys_multi_sale_type\",\"value\":[\"java.util.ArrayList\",[\"2\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"category\",\"value\":[\"java.util.ArrayList\",[\"30\",\"3006\"]],\"source\":0,\"cnName\":null,\"type\":null}]],\"rule\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO\",\"buyRule\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.rule.buy.DealGroupBuyRuleDTO\",\"maxPerUser\":0,\"minPerUser\":1},\"useRule\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO\",\"receiptEffectiveDate\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.rule.use.ReceiptEffectiveDateDTO\",\"receiptDateType\":1,\"receiptValidDays\":90,\"receiptBeginDate\":\"2025-03-31 16:40:30\",\"receiptEndDate\":\"2025-06-29 23:59:59\",\"showText\":\"购买后90天内有效\"},\"availableDate\":null,\"disableDate\":null},\"bookingRule\":null,\"refundRule\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.rule.refund.DealGroupRefundRuleDTO\",\"supportRefundType\":1,\"supportOverdueAutoRefund\":true}},\"displayShopInfo\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupDisplayShopDTO\",\"dpDisplayShopIds\":[\"java.util.ArrayList\",[607534207]],\"mtDisplayShopIds\":[\"java.util.ArrayList\",[607534207]]},\"verifyShopInfo\":null,\"tags\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100055939,\"tagName\":\"大于120分钟\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100069839,\"tagName\":\"面部\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100069834,\"tagName\":\"肩颈\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100007272,\"tagName\":\"足疗按摩虚拟节点\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100007216,\"tagName\":\"足疗\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100001016,\"tagName\":\"足疗\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100002042,\"tagName\":\"足浴\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100006171,\"tagName\":\"SPA\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100002048,\"tagName\":\"精油\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100000037,\"tagName\":\"SPA\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":10014354,\"tagName\":\"1\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":10014401,\"tagName\":\"测试\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100208256,\"tagName\":\"测试\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100210963,\"tagName\":\"茶饮\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100229628,\"tagName\":\"测试测试\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":10064338,\"tagName\":\"批量团泛1\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":10058438,\"tagName\":\"批量团泛1\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100313053,\"tagName\":\"推拿按摩\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100311049,\"tagName\":\"肩颈\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100312080,\"tagName\":\"推拿按摩\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100292942,\"tagName\":\"肩颈\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100305297,\"tagName\":\"全身SPA\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100304260,\"tagName\":\"按摩\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100305300,\"tagName\":\"头部按摩\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100298349,\"tagName\":\"肩颈按摩\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":10062440,\"tagName\":\"批量团泛1+2rules\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100312100,\"tagName\":\"肩颈SPA\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100299949,\"tagName\":\"按摩足疗多次省团购标签\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100315657,\"tagName\":\"中端品牌儿童配镜套餐\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100090843,\"tagName\":\"重复2\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO\",\"id\":100090840,\"tagName\":\"批量新增示例V1\"}]],\"customer\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupCustomerDTO\",\"originCustomerId\":30007829,\"platformCustomerId\":1020888372},\"regions\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupRegionDTO\",\"dpCityId\":1,\"mtCityId\":10},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupRegionDTO\",\"dpCityId\":10,\"mtCityId\":40}]],\"notice\":null,\"notice2b\":null,\"deals\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO\",\"dealId\":463452335,\"basic\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.deal.DealBasicDTO\",\"title\":\"222\",\"originTitle\":\"222\",\"thirdPartyId\":null,\"status\":1,\"thirdPartyDealId\":null},\"price\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.PriceDTO\",\"salePrice\":\"700.00\",\"marketPrice\":\"1319.00\",\"version\":5186644083,\"status\":null,\"prePayPrice\":null,\"finalPayPrice\":null},\"stock\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StockDTO\",\"dpSales\":0,\"dpTotal\":2000000,\"dpRemain\":2000000,\"mtSales\":0,\"mtTotal\":2000000,\"mtRemain\":2000000,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1,\"sharedSales\":0,\"sharedTotal\":0,\"sharedRemain\":0,\"isSharedSoldOut\":false},\"attrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"sys_multi_sale_number\",\"value\":[\"java.util.ArrayList\",[\"3\"]],\"source\":0,\"cnName\":null,\"type\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.AttrDTO\",\"name\":\"sku_receipt_type\",\"value\":[\"java.util.ArrayList\",[\"1\"]],\"source\":0,\"cnName\":null,\"type\":null}]],\"dealTimeStockDTO\":null,\"dealTimeStockPlanDTO\":null,\"rule\":null,\"image\":null,\"displayShop\":null,\"dealDelivery\":null,\"shopStocks\":null,\"bizDealId\":null,\"weeklyPricePlan\":null,\"dateTimePrice\":null,\"periodPrice\":null,\"bizDealIdInt\":null,\"dealIdInt\":463452335}]],\"price\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.PriceDTO\",\"salePrice\":\"700.00\",\"marketPrice\":\"1319.00\",\"version\":5186644083,\"status\":null,\"prePayPrice\":null,\"finalPayPrice\":null},\"stock\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StockDTO\",\"dpSales\":0,\"dpTotal\":2000000,\"dpRemain\":2000000,\"mtSales\":0,\"mtTotal\":2000000,\"mtRemain\":2000000,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1,\"sharedSales\":null,\"sharedTotal\":null,\"sharedRemain\":null,\"isSharedSoldOut\":null},\"detail\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.detail.DealGroupDetailDTO\",\"dealGroupPics\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/f65f85522c9f7363252597c56b0b4c45105689.jpg\",\"images\":[\"java.util.ArrayList\",[\"https://qcloud.dpfile.com/pc/1H3b18SxY--ceLSkDEB0VvrFih_3hcNZMAbfFwxf3hXMVXzgjN-gADk2cYhm1oEjDkGHbckSnjHySsp3uDLV9w.jpg\"]],\"info\":null,\"importantPoint\":\"\",\"specialPoint\":null,\"productInfo\":null,\"editorInfo\":null,\"memberInfo\":null,\"shopInfo\":null,\"editorTeam\":null,\"summary\":null,\"templateDetailDTOs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.detail.DealGroupTemplateDetailDTO\",\"title\":\"产品介绍\",\"content\":\"<div>22</div>\\n\\n\",\"type\":5,\"blockId\":0,\"mixedContents\":null,\"imageContents\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.detail.DealGroupTemplateDetailDTO\",\"title\":\"团购详情\",\"content\":\"        <div class=\\\"detail-tit\\\"></div>\\n        <div>\\n            <table width=\\\"100%\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" class=\\\"detail-table\\\">\\n                <thead>\\n                <tr>\\n                    <th width=\\\"50%\\\">名称</th>\\n                    <th width=\\\"25%\\\">数量</th>\\n                    <th width=\\\"25%\\\">单价</th>\\n                </tr>\\n                </thead>\\n                <tbody>\\n                            <tr>\\n                                <td></td>\\n                                    <td class=\\\"tc\\\">1</td>\\n                            </tr>\\n                    <tr class=\\\"total\\\">\\n                        <td></td>\\n                        <td class=\\\"tc\\\">总价<br><strong>团购价</strong></td>\\n                        <td class=\\\"tc\\\">439.67元<br><strong>700元</strong>\\n                        </td>\\n                    </tr>\\n                </tbody>\\n            </table>\\n        </div>\\n\\n\",\"type\":1,\"blockId\":0,\"mixedContents\":null,\"imageContents\":null},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.detail.DealGroupTemplateDetailDTO\",\"title\":\"购买须知\",\"content\":\"<div class=\\\"detail-box\\\">\\n    <div class=\\\"purchase-notes\\\">\\n\\t\\t        <dl>\\n            <dt>有效期</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">\\n    购买后90天内有效\\n        </p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>预约信息</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">无需预约，如遇消费高峰时段您可能需要排队</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>适用人数</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">每张团购券不限使用人数</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>规则提醒</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">不再与其他优惠同享</p>\\n                <p class=\\\"listitem\\\">单次到店仅可核销一次，仅能一人使用</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>温馨提示</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">如需团购券发票，请您在消费时向商户咨询</p>\\n                <p class=\\\"listitem\\\">为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！</p>\\n                <p class=\\\"listitem\\\">购买后若全部未使用，在有效期内可申请全额退款；过期自动将未使用部分退款；若部分已使用，则不可退款。</p>\\n            </dd>\\n        </dl>\\n    </div>\\n</div>\\n\",\"type\":2,\"blockId\":0,\"mixedContents\":null,\"imageContents\":null}]]},\"spu\":null,\"standardServiceProject\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectDTO\",\"mustGroups\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectGroupDTO\",\"serviceProjectItems\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectItemDTO\",\"serviceProjectName\":null,\"standardAttribute\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeDTO\",\"attrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"serviceProcessArrayNew\",\"attrCnName\":\"服务流程\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":1,\"simpleValues\":null,\"complexValues\":\"[{\\\"attrs\\\":[{\\\"attrName\\\":\\\"servicemethod\\\",\\\"attrCnName\\\":\\\"服务方式\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"222\\\"]}]},{\\\"attrName\\\":\\\"stepTime\\\",\\\"attrCnName\\\":\\\"步骤耗时\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"22.0\\\"]}]}],\\\"cpvObjectId\\\":20437879}]\"}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"skuCateId\",\"attrCnName\":\"项目分类\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"2104613\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"serviceTechnique\",\"attrCnName\":\"服务手法\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"精油SPA\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"bodyRegion\",\"attrCnName\":\"服务部位范围\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"局部部位\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"serviceBodyRange\",\"attrCnName\":\"具体服务部位\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"[\\\"面部\\\"]\"]],\"complexValues\":null}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO\",\"attrName\":\"serviceDurationInt\",\"attrCnName\":\"服务时长\",\"attrValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO\",\"type\":0,\"simpleValues\":[\"java.util.ArrayList\",[\"222\"]],\"complexValues\":null}]]}]],\"cpvObjectId\":20369521,\"cpvObjectVersion\":25},\"marketPrice\":null,\"amount\":null}]],\"optionalCount\":0}]],\"optionalGroups\":[\"java.util.ArrayList\",[]]},\"extendImage\":null,\"combines\":null,\"saleChannelAggregation\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.SaleChannelAggregationDTO\",\"allSupport\":true,\"supportChannels\":[\"java.util.ArrayList\",[]],\"notSupportChannels\":[\"java.util.ArrayList\",[]]},\"purchaseNote\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.PurchaseNoteDTO\",\"title\":\"购买须知\",\"modules\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"适用时间\",\"icon\":\"https://p0.meituan.net/travelcube/eb358d1a211285207c636aa95594f3751524.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"有效时间\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"购买后90天内有效\"}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"可用时间\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":5,\"value\":\"deal_available_time\"}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"除外日期\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":5,\"value\":\"deal_exclude_period\"}]]}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"预约规则\",\"icon\":\"https://p0.meituan.net/travelcube/17303e587e8242902a1bf6ecd3eab10b1029.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"无需预约，如遇消费高峰时段您可能需要排队\"}]]}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"适用人数\",\"icon\":\"https://p0.meituan.net/travelcube/1f18aa2c0100e75060daf348283f3c861351.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"每次到店仅可核销1次\"}]]}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"其他规则\",\"icon\":\"https://p1.meituan.net/travelcube/4dac95c5f43a52f49b81ece1918925ea858.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"不再与其他优惠同享\"}]]}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"温馨提示\",\"icon\":\"https://p1.meituan.net/travelcube/09a2f606b1636f8b135b8582e3c0a53d1494.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"如需团购券发票，请您在消费时向商户咨询\"},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！\"}]]}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO\",\"moduleName\":\"退款规则\",\"icon\":\"https://p0.meituan.net/dztgdetailimages/aec8c981af1fe8e19dffb7f6748363152480.png\",\"items\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"支持随时退、过期退\"}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"若次数未使用，可随时退全部实付金额\"}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":1,\"value\":\"若发生核销后再申请退款，剩余所有未核销次数将一起退款，本商品为阶梯定价品，退款金额须按照如下规则进行计算\"}]]},{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO\",\"itemName\":\"\",\"itemValues\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO\",\"type\":4,\"value\":\"{\\\"columns\\\":[{\\\"type\\\":1,\\\"value\\\":\\\"累计使用次数\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"单次价格\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"预计可退金额\\\"}],\\\"rows\\\":[{\\\"cells\\\":[{\\\"type\\\":1,\\\"value\\\":\\\"1\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"¥256.68\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"¥443.32\\\"}]},{\\\"cells\\\":[{\\\"type\\\":1,\\\"value\\\":\\\"2\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"¥221.66\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"¥221.66\\\"}]},{\\\"cells\\\":[{\\\"type\\\":1,\\\"value\\\":\\\"3\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"¥221.66\\\"},{\\\"type\\\":1,\\\"value\\\":\\\"¥0.00\\\"}]}],\\\"desc\\\":\\\"此价格仅供参考，如涉及营销优惠，会把优惠金额按比例分摊到单次价格中扣除，若购买时使用了优惠券，仅在整单退款且优惠券未过期时，返还优惠券\\\"}\"}]]}]]}]]},\"bizProductId\":null,\"resourceInfos\":null,\"thirdPartyInfo\":null,\"dpDealGroupIdInt\":1038827787,\"mtDealGroupIdInt\":1038827787}";
    private static final String responseJsonData = "{\"@class\":\"com.sankuai.fincreditpay.bnpl.client.access.thrift.response.BNPLExposureResponse\",\"status\":\"SUCCESS\",\"errorInfo\":null,\"data\":{\"@class\":\"com.sankuai.fincreditpay.bnpl.client.access.thrift.dto.BNPLExposureDTO\",\"exposure\":\"EXPOSED\",\"exposureCode\":null,\"exposureDesc\":null,\"quotaInfo\":null,\"jumpUrl\":null,\"exposureProduct\":null,\"exposureProductList\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.fincreditpay.bnpl.client.access.thrift.dto.ProductExposureDetailDTO\",\"exposureProduct\":\"delaypay\",\"exposure\":\"UNEXPOSED\",\"exposureCode\":\"1012\",\"exposureDesc\":\"授信准入不通过\",\"userSignStatus\":0,\"quotaInfo\":{\"@class\":\"com.sankuai.fincreditpay.bnpl.client.access.thrift.dto.QuotaInfoDTO\",\"amountOfFenLimit\":30000},\"displayStyle\":{\"@class\":\"com.sankuai.fincreditpay.bnpl.client.access.thrift.dto.DisplayStyleDTO\",\"mainTitle\":\"美团先用后付\",\"subTitle\":\"0元先购，核券后再付款\",\"displayText\":null,\"displayToast\":null},\"promo\":null,\"confirmMaterial\":null}]],\"promo\":null},\"success\":true}";
    private static final String moduleConfigAbsJsonData = "[\"java.util.ArrayList\",[{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MTSalesGeneralSection\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp001434\",\"expResult\":\"a\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"45e2fb91-8bc5-48a8-b716-24fc944883f6\\\",\\\"ab_id\\\":\\\"exp001434_a\\\"}\",\"useNewStyle\":false}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MTZuLiaoShowMarketPrice\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp001418\",\"expResult\":\"exp001418_b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"9d7320f4-7368-4b60-ad13-21bc53db7703\\\",\\\"ab_id\\\":\\\"exp001418_b\\\"}\",\"useNewStyle\":false}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MtCouponAlleviate2Exp\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"EXP2024082700008\",\"expResult\":\"c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"f6e59b17-9d44-4bf4-a837-58d0c631a03c\\\",\\\"ab_id\\\":\\\"EXP2024082700008_c\\\"}\",\"useNewStyle\":false}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MTMassageOverNight\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp001760\",\"expResult\":\"a\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"9bef6492-1b49-4c72-9329-9b24566e0dcd\\\",\\\"ab_id\\\":\\\"exp001760_a\\\"}\",\"useNewStyle\":false}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MTJoyCardPriceExp\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp000594\",\"expResult\":\"exp000594_b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"118101e9-65c7-4100-8ee7-6f33a8a1af62\\\",\\\"ab_id\\\":\\\"exp000594_b\\\"}\",\"useNewStyle\":false}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MtCreditPayExp\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"EXP2025032100002\",\"expResult\":\"c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"23ce22bc-94c8-494a-981f-c080c3d2abdb\\\",\\\"ab_id\\\":\\\"EXP2025032100002_c\\\"}\",\"useNewStyle\":false}]]}]]";

}