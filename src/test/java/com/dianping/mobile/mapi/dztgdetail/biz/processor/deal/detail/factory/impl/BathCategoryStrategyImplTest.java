package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryAbInfo;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryDealModuleInfo;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import java.util.HashSet;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * @author: created by hang.yu on 2024/4/29 17:20
 */
@RunWith(MockitoJUnitRunner.class)
public class BathCategoryStrategyImplTest {

    @InjectMocks
    private BathCategoryStrategyImpl bathCategoryStrategy;

    @Mock
    private DouHuBiz douHuBiz;

    @Test
    public void getSimilarDealModuleAbConfig() {
        when(douHuBiz.getAbExpResult(any(EnvCtx.class), anyString())).thenReturn(new ModuleAbConfig());
        ModuleAbConfig result = bathCategoryStrategy.getSimilarDealModuleAbConfig(new EnvCtx());
        Assert.assertNotNull(result);
    }

    /**
     * 测试 buildAbInfo 方法
     */
    @Test
    public void testBuildAbInfo() throws Throwable {
        // arrange
        BathCategoryStrategyImpl bathCategoryStrategy = new BathCategoryStrategyImpl();
        // act
        DealCategoryAbInfo result = bathCategoryStrategy.buildAbInfo();
        // assert
        assertNotNull(result);
        assertEquals("MTBathNewStyle", result.getMtModule());
        assertEquals("DPBathNewStyle", result.getDpModule());
        assertEquals(new HashSet<>(java.util.Arrays.asList("a", "b")), result.getSkList());
    }

    /**
     * Tests the buildDealModuleInfo method.
     */
    @Test
    public void testBuildDealModuleInfo() throws Throwable {
        // Arrange
        BathCategoryStrategyImpl bathCategoryStrategy = new BathCategoryStrategyImpl();
        // Act
        DealCategoryDealModuleInfo result = bathCategoryStrategy.buildDealModuleInfo();
        // Assert
        assertNotNull(result);
        assertEquals("gcdealdetail_newtuandealtab_bath_tuandetail", result.getMtModuleKey());
        assertEquals("tuandeal_newtuandealtab_bath_tuandetail", result.getDpModuleKey());
    }
}
