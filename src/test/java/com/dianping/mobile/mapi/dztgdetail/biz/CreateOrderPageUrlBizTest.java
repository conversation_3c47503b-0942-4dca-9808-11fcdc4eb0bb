package com.dianping.mobile.mapi.dztgdetail.biz;

import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.ReserveMaintenanceService;
import com.dianping.tuangu.dztg.usercenter.api.CreateOrderPageUrlService;
import com.dianping.tuangu.dztg.usercenter.api.dto.BatchGetCreateOrderPageUrlDto;
import com.dianping.tuangu.dztg.usercenter.api.request.BatchGetCreateOrderPageUrlReq;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Future;

import static org.junit.Assert.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CreateOrderPageUrlBizTest {

    @InjectMocks
    private CreateOrderPageUrlBiz createOrderPageUrlBiz;

    @Mock
    private ReserveMaintenanceService reserveMaintenanceService;

    @Mock
    private CreateOrderPageUrlService createOrderPageUrlService;

    /**
     * 测试 fillIntegratedReserveDouHuSk 方法，当 result 和 dto 不为空，且 abResult 不为空的情况
     */
    @Test
    public void testFillIntegratedReserveDouHuSk_WithValidResultAndDtoAndAbResult() {
        // arrange
        DealGroupPBO result = mock(DealGroupPBO.class);
        BatchGetCreateOrderPageUrlDto dto = new BatchGetCreateOrderPageUrlDto();
        DealBaseReq request = new DealBaseReq();
        String trafficFlag = "testFlag";
        request.setPageSource(trafficFlag);
        AbConfig abResult = new AbConfig();
        abResult.setExpId("expId");
        abResult.setExpResult("expResult");
        when(result.isCleaningSelfOperationShop()).thenReturn(true);
        // when(reserveMaintenanceService.getIntegratedReservedConfig(result, trafficFlag)).thenReturn(abResult);
        // when(reserveMaintenanceService.getSelfOperatedCleaningReservedResult(result, trafficFlag)).thenReturn(true);
        // act
        createOrderPageUrlBiz.fillIntegratedReserveDouHuSk(result, dto, request, new EnvCtx());
        // assert
        // verify(reserveMaintenanceService, times(1)).getIntegratedReservedConfig(result, trafficFlag);
        // verify(reserveMaintenanceService, times(1)).getSelfOperatedCleaningReservedResult(result, trafficFlag);
        assert dto.getExtUrlParam().containsKey("orderdetailtype");
        // assert dto.getExtUrlParam().containsKey("preordershowtype");
        // assert dto.getExtUrlParam().containsKey("orderdetailtype");
    }

    /**
     * 测试 fillIntegratedReserveDouHuSk 方法，当 result 和 dto 不为空，但 abResult 为空的情况
     */
    @Test
    public void testFillIntegratedReserveDouHuSk_WithValidResultAndDtoButNullAbResult() {
        // arrange
        DealGroupPBO result = mock(DealGroupPBO.class);
        BatchGetCreateOrderPageUrlDto dto = new BatchGetCreateOrderPageUrlDto();
        DealBaseReq request = new DealBaseReq();
        String trafficFlag = "testFlag";
        request.setPageSource(trafficFlag);
        // when(reserveMaintenanceService.getIntegratedReservedConfig(result, trafficFlag)).thenReturn(null);
        // act
        createOrderPageUrlBiz.fillIntegratedReserveDouHuSk(result, dto, request, new EnvCtx());
        // assert
        // verify(reserveMaintenanceService, times(1)).getIntegratedReservedConfig(result, trafficFlag);
        assert dto.getExtUrlParam() == null || dto.getExtUrlParam().isEmpty();
    }

    /**
     * 测试 fillIntegratedReserveDouHuSk 方法，当 result 和 dto 不为空，abResult 不为空，但保洁自营门店结果为 false 的情况
     */
    @Test
    public void testFillIntegratedReserveDouHuSk_WithValidResultAndDtoAndAbResultButFalseCleaningResult() {
        // arrange
        DealGroupPBO result = mock(DealGroupPBO.class);
        BatchGetCreateOrderPageUrlDto dto = new BatchGetCreateOrderPageUrlDto();
        DealBaseReq request = new DealBaseReq();
        String trafficFlag = "testFlag";
        request.setPageSource(trafficFlag);
        AbConfig abResult = new AbConfig();
        abResult.setExpId("expId");
        abResult.setExpResult("expResult");
        when(result.isCleaningSelfOperationShop()).thenReturn(true);
        // when(reserveMaintenanceService.getIntegratedReservedConfig(result, trafficFlag)).thenReturn(abResult);
        // when(reserveMaintenanceService.getSelfOperatedCleaningReservedResult(result, trafficFlag)).thenReturn(false);
        // act
        createOrderPageUrlBiz.fillIntegratedReserveDouHuSk(result, dto, request, new EnvCtx());

        assert dto.getExtUrlParam().containsKey("orderdetailtype");

        // assert
        // verify(reserveMaintenanceService, times(1)).getIntegratedReservedConfig(result, trafficFlag);
        // verify(reserveMaintenanceService, times(1)).getSelfOperatedCleaningReservedResult(result, trafficFlag);
        // assert dto.getExtUrlParam().containsKey("expid");
        // assert !dto.getExtUrlParam().containsKey("preordershowtype");
        // assert !dto.getExtUrlParam().containsKey("orderdetailtype");
    }

    /**
     * 测试 isNotSupportBtnType 方法，当 buyBtn 为 null 时，应抛出 NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testIsNotSupportBtnTypeWhenBuyBtnIsNull() throws Throwable {
        // arrange
        DealBuyBtn buyBtn = null;
        // act
        CreateOrderPageUrlBiz.isNotSupportBtnType(buyBtn);
        // assert: expected exception
    }

    /**
     * 测试 isNotSupportBtnType 方法，当 buyBtn 不为 null，但 detailBuyType 不在 NOT_SUPPORT_BTN_TYPE_ENUM_SET 集合中时，应返回 false
     */
    @Test
    public void testIsNotSupportBtnTypeWhenDetailBuyTypeIsNotInSet() throws Throwable {
        // arrange
        DealBuyBtn buyBtn = new DealBuyBtn(true, "btnTitle");
        // Assuming 999 is not a code for any BuyBtnTypeEnum
        buyBtn.setDetailBuyType(999);
        // act
        boolean result = CreateOrderPageUrlBiz.isNotSupportBtnType(buyBtn);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 isNotSupportBtnType 方法，当 buyBtn 不为 null，且 detailBuyType 在 NOT_SUPPORT_BTN_TYPE_ENUM_SET 集合中时，应返回 true
     */
    @Test
    public void testIsNotSupportBtnTypeWhenDetailBuyTypeIsInSet() throws Throwable {
        // arrange
        DealBuyBtn buyBtn = new DealBuyBtn(true, "btnTitle");
        // Correctly setting the detailBuyType to one that is expected to be in the set
        // Assuming TIMES_CARD is a valid enum constant
        buyBtn.setDetailBuyType(BuyBtnTypeEnum.TIMES_CARD.getCode());
        // act
        boolean result = CreateOrderPageUrlBiz.isNotSupportBtnType(buyBtn);
        // assert
        assertTrue(result);
    }

    /**
     * Test when key is null, should return early without modifying dto
     */
    @Test
    public void testSetExtUrlParam_WhenKeyIsNull() {
        // arrange
        BatchGetCreateOrderPageUrlDto dto = new BatchGetCreateOrderPageUrlDto();
        String key = null;
        String value = "testValue";
        // act
        createOrderPageUrlBiz.setExtUrlParam(dto, key, value);
        // assert
        assertNull(dto.getExtUrlParam());
    }

    /**
     * Test when key is empty string, should return early without modifying dto
     */
    @Test
    public void testSetExtUrlParam_WhenKeyIsEmpty() {
        // arrange
        BatchGetCreateOrderPageUrlDto dto = new BatchGetCreateOrderPageUrlDto();
        String key = "";
        String value = "testValue";
        // act
        createOrderPageUrlBiz.setExtUrlParam(dto, key, value);
        // assert
        assertNull(dto.getExtUrlParam());
    }

    /**
     * Test when value is null, should return early without modifying dto
     */
    @Test
    public void testSetExtUrlParam_WhenValueIsNull() {
        // arrange
        BatchGetCreateOrderPageUrlDto dto = new BatchGetCreateOrderPageUrlDto();
        String key = "testKey";
        String value = null;
        // act
        createOrderPageUrlBiz.setExtUrlParam(dto, key, value);
        // assert
        assertNull(dto.getExtUrlParam());
    }

    /**
     * Test when extUrlParam map is null, should create new map and add key-value pair
     */
    @Test
    public void testSetExtUrlParam_WhenMapIsNull() {
        // arrange
        BatchGetCreateOrderPageUrlDto dto = new BatchGetCreateOrderPageUrlDto();
        String key = "testKey";
        String value = "testValue";
        // act
        createOrderPageUrlBiz.setExtUrlParam(dto, key, value);
        // assert
        Map<String, String> resultMap = dto.getExtUrlParam();
        assertTrue(resultMap instanceof HashMap);
        assertEquals(1, resultMap.size());
        assertEquals(value, resultMap.get(key));
    }

    /**
     * Test when extUrlParam map exists, should add to existing map
     */
    @Test
    public void testSetExtUrlParam_WhenMapExists() {
        // arrange
        BatchGetCreateOrderPageUrlDto dto = new BatchGetCreateOrderPageUrlDto();
        Map<String, String> existingMap = new HashMap<>();
        existingMap.put("existingKey", "existingValue");
        dto.setExtUrlParam(existingMap);
        String key = "testKey";
        String value = "testValue";
        // act
        createOrderPageUrlBiz.setExtUrlParam(dto, key, value);
        // assert
        Map<String, String> resultMap = dto.getExtUrlParam();
        assertEquals(2, resultMap.size());
        assertEquals(value, resultMap.get(key));
        assertEquals("existingValue", resultMap.get("existingKey"));
    }

    /**
     * Tests the createOrderPageUrlServiceFuture method under exception conditions.
     */
    @Test
    public void testCreateOrderPageUrlServiceFutureException() throws Throwable {
        // Arrange
        BatchGetCreateOrderPageUrlReq req = new BatchGetCreateOrderPageUrlReq();
        // Act
        Future result = createOrderPageUrlBiz.createOrderPageUrlServiceFuture(req);
        // Assert
        assertNull(result);
        // Since the actual interaction with the mock is not happening as expected, we cannot verify the interaction directly.
        // This assertion is based on the assumption that the method under test behaves as expected when an exception is thrown.
    }
}
