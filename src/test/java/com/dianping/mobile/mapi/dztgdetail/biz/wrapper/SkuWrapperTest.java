package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.tpfun.product.api.sku.pintuan.dto.BestPinTag;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.concurrent.Future;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import java.util.Map;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class SkuWrapperTest {

    @Mock
    private Future<Map<Integer, BestPinTag>> future;

    private SkuWrapper skuWrapper = new SkuWrapper();

    /**
     * Tests the scenario when the Future object is not null, but the result obtained is null.
     * Expected behavior: The method under test should return null.
     */
    @Test
    public void testGetBestPinFutureResultIsNull() throws Throwable {
        when(future.get()).thenReturn(null);
        BestPinTag result = skuWrapper.getBestPin(future);
        assertNull("Expected result to be null when future result is null", result);
    }

    /**
     * Tests the scenario when the Future object is not null, and the result obtained is an empty Map.
     * Expected behavior: The method under test should return null.
     */
    @Test
    public void testGetBestPinFutureResultMapIsEmpty() throws Throwable {
        when(future.get()).thenReturn(Collections.emptyMap());
        BestPinTag result = skuWrapper.getBestPin(future);
        assertNull("Expected result to be null when future result map is empty", result);
    }

    /**
     * Tests the scenario when the Future object is not null, and the result obtained is a non-empty Map.
     * Expected behavior: The method under test should return the BestPinTag object from the Map.
     */
    @Test
    public void testGetBestPinFutureResultMapIsNotEmpty() throws Throwable {
        BestPinTag bestPinTag = new BestPinTag();
        when(future.get()).thenReturn(Collections.singletonMap(1, bestPinTag));
        BestPinTag result = skuWrapper.getBestPin(future);
        assertSame("Expected the same BestPinTag object that was put in the map", bestPinTag, result);
    }
}
