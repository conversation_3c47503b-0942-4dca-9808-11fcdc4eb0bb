package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.account.UserAccountService;
import com.dianping.account.dto.VirtualBindUserInfoDTO;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.concurrent.Future;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

public class UserAccountWrapper_PreLoadUserByTokenTest {

    @InjectMocks
    private UserAccountWrapper userAccountWrapper = new UserAccountWrapper();

    @Mock
    private UserAccountService userAccountServiceFuture;

    private void setUpMocks() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 token 为空的情况
     */
    @Test
    public void testPreLoadUserByTokenTokenIsNull() throws Throwable {
        // Initialize mocks
        setUpMocks();
        // arrange
        String token = null;
        // act
        Future result = userAccountWrapper.preLoadUserByToken(token);
        // assert
        assertNull(result);
    }

    /**
     * 测试 token 不为空，且 loadUserByToken 方法调用失败的情况
     */
    @Test
    public void testPreLoadUserByTokenFailed() throws Throwable {
        // Initialize mocks
        setUpMocks();
        // arrange
        String token = "testToken";
        doThrow(new RuntimeException()).when(userAccountServiceFuture).loadUserByToken(anyString(), anyString(), anyMap());
        // act
        Future result = userAccountWrapper.preLoadUserByToken(token);
        // assert
        assertNull(result);
    }
}
