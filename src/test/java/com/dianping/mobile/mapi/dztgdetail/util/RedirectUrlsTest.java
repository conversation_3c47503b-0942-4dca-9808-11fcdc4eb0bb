package com.dianping.mobile.mapi.dztgdetail.util;

import com.alibaba.fastjson.JSON;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.feature.model.FeatureDetailDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.LayerConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.LeadsDealBarConfig;
import com.google.common.collect.Lists;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RedirectUrlsTest {

    private MockedStatic<DealUtils> dealUtilsMockedStatic;

    @Before
    public void setUp() {
        dealUtilsMockedStatic = mockStatic(DealUtils.class);
    }

    @After
    public void tearDown() {
        dealUtilsMockedStatic.close();
    }

    /**
     * 测试 orderUrl 为空的情况
     */
    @Test
    public void testFilterOrderUrlSkuId_OrderUrlIsEmpty() {
        DealCtx ctx = Mockito.mock(DealCtx.class);
        String result = RedirectUrls.filterOrderUrlSkuId(ctx, "");
        Assert.assertEquals("", result);
    }

    /**
     * 测试 ctx 为 null 的情况
     */
    @Test
    public void testFilterOrderUrlSkuId_CtxIsNull() {
        DealCtx ctx = null;
        String result = RedirectUrls.filterOrderUrlSkuId(ctx, "http://example.com?skuid=123");
        Assert.assertEquals("", result);
    }

    /**
     * 测试 orderUrl 包含 "&skuid=&" 且 isNewWearableNailDeal 为 true 的情况
     */
    @Test
    public void testFilterOrderUrlSkuId_OrderUrlContainsSkuidAndIsNewWearableNailDeal() {
        DealCtx ctx = Mockito.mock(DealCtx.class);
        dealUtilsMockedStatic.when(() -> DealUtils.isNewWearableNailDeal(ctx)).thenReturn(true);
        String orderUrl = "http://example.com?skuid=&otherParam=value";
        String result = RedirectUrls.filterOrderUrlSkuId(ctx, orderUrl);
        Assert.assertEquals("http://example.com?otherParam=value&need_exhibit_skuid=1", result);
    }

    /**
     * 测试 orderUrl 不包含 "&skuid=&" 且 isNewWearableNailDeal 为 true 的情况
     */
    @Test
    public void testFilterOrderUrlSkuId_OrderUrlDoesNotContainSkuidAndIsNewWearableNailDeal() {
        DealCtx ctx = Mockito.mock(DealCtx.class);
        dealUtilsMockedStatic.when(() -> DealUtils.isNewWearableNailDeal(ctx)).thenReturn(true);
        String orderUrl = "http://example.com?otherParam=value";
        String result = RedirectUrls.filterOrderUrlSkuId(ctx, orderUrl);
        Assert.assertEquals("http://example.com?otherParam=value", result);
    }

    /**
     * 测试 orderUrl 包含 "&skuid=&" 但 isNewWearableNailDeal 为 false 的情况
     */
    @Test
    public void testFilterOrderUrlSkuId_OrderUrlContainsSkuidButIsNotNewWearableNailDeal() {
        DealCtx ctx = Mockito.mock(DealCtx.class);
        dealUtilsMockedStatic.when(() -> DealUtils.isNewWearableNailDeal(ctx)).thenReturn(false);
        String orderUrl = "http://example.com?skuid=&otherParam=value";
        String result = RedirectUrls.filterOrderUrlSkuId(ctx, orderUrl);
        Assert.assertEquals("http://example.com?skuid=&otherParam=value", result);
    }

    /**
     * 测试 orderUrl 不包含 "&skuid=&" 且 isNewWearableNailDeal 为 false 的情况
     */
    @Test
    public void testFilterOrderUrlSkuId_OrderUrlDoesNotContainSkuidAndIsNotNewWearableNailDeal() {
        DealCtx ctx = Mockito.mock(DealCtx.class);
        dealUtilsMockedStatic.when(() -> DealUtils.isNewWearableNailDeal(ctx)).thenReturn(false);
        String orderUrl = "http://example.com?otherParam=value";
        String result = RedirectUrls.filterOrderUrlSkuId(ctx, orderUrl);
        Assert.assertEquals("http://example.com?otherParam=value", result);
    }

    /**
     * 测试 setPerformanceGuaranteeUrl 方法，App端跳链不存在
     */
    @Test
    public void test_SetPerformanceGuaranteeUrl_AppUrlNotExist() {
        // arrange
        DealCtx ctx = Mockito.mock(DealCtx.class);
        EnvCtx envCtx = Mockito.mock(EnvCtx.class);
        FeatureDetailDTO featureDetailDTO = new FeatureDetailDTO();
        LayerConfig config = new LayerConfig();
        featureDetailDTO.setLayerConfig(config);
        when(ctx.getShopTagFeatures()).thenReturn(Lists.newArrayList(featureDetailDTO));
        when(envCtx.isWxMini()).thenReturn(false);

        // act
        RedirectUrls.setPerformanceGuaranteeUrls(ctx, envCtx, Lists.newArrayList(config));

        // assert
        Assert.assertNull(config.getJumpUrl());
    }

    /**
     * 测试 setPerformanceGuaranteeUrl 方法，App端跳链存在
     */
    @Test
    public void test_SetPerformanceGuaranteeUrl_AppUrlExist() {
        DealCtx ctx = Mockito.mock(DealCtx.class);
        EnvCtx envCtx = Mockito.mock(EnvCtx.class);
        // 不满意重做标签
        FeatureDetailDTO featureDetailDTO = new FeatureDetailDTO();
        featureDetailDTO.setId(1477L);
        LayerConfig config = new LayerConfig();
        config.setJumpUrl("testUrl");
        featureDetailDTO.setLayerConfig(config);
        featureDetailDTO.setGuaranteeJumpId(5L);

        // 不主动推销标签
        FeatureDetailDTO featureDetailDTO2 = new FeatureDetailDTO();
        featureDetailDTO2.setId(7446L);
        LayerConfig config2 = new LayerConfig();
        config2.setJumpUrl("testUrl2");
        featureDetailDTO2.setLayerConfig(config2);
        featureDetailDTO2.setGuaranteeJumpId(6L);

        when(ctx.getShopTagFeatures()).thenReturn(Lists.newArrayList(featureDetailDTO, featureDetailDTO2));
        when(ctx.getMtLongShopId()).thenReturn(123L);
        when(envCtx.isWxMini()).thenReturn(false);
        when(envCtx.isMt()).thenReturn(true);

        // act
        RedirectUrls.setPerformanceGuaranteeUrls(ctx, envCtx, Lists.newArrayList(config, config2));

        // assert
        String expectUrl1 = "imeituan://www.meituan.com/mrn?testUrl&displayScene=2&platform=2&shopId=123&guaranteeIp=5,6";
        String expectUrl2 = "imeituan://www.meituan.com/mrn?testUrl2&displayScene=2&platform=2&shopId=123&guaranteeIp=5,6";
        Assert.assertEquals(expectUrl1, config.getJumpUrl());
        Assert.assertEquals(expectUrl2, config2.getJumpUrl());
    }

    /**
     * 测试 setPerformanceGuaranteeUrl 方法，小程序跳链存在
     */
    @Test
    public void test_SetPerformanceGuaranteeUrl_WxUrlExist() {
        DealCtx ctx = Mockito.mock(DealCtx.class);
        EnvCtx envCtx = Mockito.mock(EnvCtx.class);
        // 不满意重做标签
        FeatureDetailDTO featureDetailDTO = new FeatureDetailDTO();
        featureDetailDTO.setId(1477L);
        LayerConfig config = new LayerConfig();
        config.setMiniJumpUrl("testUrl");
        featureDetailDTO.setLayerConfig(config);
        featureDetailDTO.setGuaranteeJumpId(5L);

        // 不主动推销标签
        FeatureDetailDTO featureDetailDTO2 = new FeatureDetailDTO();
        featureDetailDTO2.setId(7446L);
        LayerConfig config2 = new LayerConfig();
        config2.setJumpUrl("testUrl2");
        featureDetailDTO2.setLayerConfig(config2);
        featureDetailDTO2.setGuaranteeJumpId(6L);

        when(ctx.getShopTagFeatures()).thenReturn(Lists.newArrayList(featureDetailDTO, featureDetailDTO2));
        when(ctx.getMtLongShopId()).thenReturn(123L);
        when(envCtx.isWxMini()).thenReturn(true);
        when(envCtx.isMt()).thenReturn(true);

        // act
        RedirectUrls.setPerformanceGuaranteeUrls(ctx, envCtx, Lists.newArrayList(config, config2));

        // assert
        String expectUrl1 = "testUrl?displayScene=2&platform=2&shopId=123&guaranteeIp=5,6";
        String expectUrl2 = "imeituan://www.meituan.com/mrn?testUrl2&displayScene=2&platform=2&shopId=123&guaranteeIp=5,6";
        Assert.assertEquals(expectUrl1, config.getMiniJumpUrl());
        Assert.assertEquals(expectUrl2, config2.getJumpUrl());
    }

    /**
     * 测试 buildResvPopBtnUrl 方法，美团侧正常情况
     */
    @Test
    public void test_buildResvPopBtnUrl_MT() {
        // arrange
        DealCtx ctx = Mockito.mock(DealCtx.class);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getLongPoiId4PFromResp()).thenReturn(123L);
        when(ctx.getDealId4P()).thenReturn(456);
        String configStr = "{\"bannerBackGroundColor\":\"#FFF1EC\",\"bannerIcon\":\"https://p0.meituan.net/dztgdetailimages/427d9c96dd426b065da5b2e28db51ddd620.png\",\"bannerTextColor\":\"#FF4B10\",\"bannerTextSize\":11,\"buyBtnTitle\":\"购买\",\"dpAppJumpPrefix\":\"dianping://mrn\",\"mtAppJumpPrefix\":\"imeituan://www.meituan.com/mrn\",\"newLeadsGiftsTemplate\":\"预约到店赠送价值%s元%s\",\"oldLeadsGiftsTemplate\":\"预约到店赠送%s\",\"phoneResvBtnTitle\":\"致电商家预约看房型\",\"resvBtnTitle\":\"预约看房型\",\"resvJumpUrlPrefix\":\"mrn_biz=gc&mrn_entry=vg-mrn-reserve&mrn_component=customerpopup\",\"resvJumpUrlSuffix\":\"isTransparent=true&hideLoading=true&mrn_transparent=true&mrn_hideloading=true&mrn_hideNextNavBar=true\"}";
        LeadsDealBarConfig leadsDealBarConfig = JSON.parseObject(configStr, LeadsDealBarConfig.class);

        // act
        String result = RedirectUrls.buildResvPopBtnUrl(ctx, leadsDealBarConfig);

        // assert
        String expectedUrl = "imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=vg-mrn-reserve&mrn_component=customerpopup&mtShopId=123&mtDealGroupId=456&title=%E9%A2%84%E7%BA%A6%E7%9C%8B%E6%88%BF%E5%9E%8B&isTransparent=true&hideLoading=true&mrn_transparent=true&mrn_hideloading=true&mrn_hideNextNavBar=true";
        Assert.assertEquals(expectedUrl, result);
    }



}
