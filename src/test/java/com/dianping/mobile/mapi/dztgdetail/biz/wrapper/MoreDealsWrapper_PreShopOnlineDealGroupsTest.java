package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.shop.ShopOnlineDealGroupService;
import com.dianping.deal.shop.dto.ShopOnlineDealGroupRequest;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Future;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.*;
import org.junit.Before;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

public class MoreDealsWrapper_PreShopOnlineDealGroupsTest {

    @InjectMocks
    private MoreDealsWrapper moreDealsWrapper = new MoreDealsWrapper();

    @Mock
    private ShopOnlineDealGroupService shopOnlineDealGroupServiceFuture = mock(ShopOnlineDealGroupService.class);

    private void setUpMocks() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Tests the scenario where shopIds is null.
     */
    @Test
    public void testPreShopOnlineDealGroupsWithEmptyShopIds() throws Throwable {
        setUpMocks();
        List<Integer> shopIds = null;
        int cityId = 1;
        Future result = moreDealsWrapper.preShopOnlineDealGroups(shopIds, cityId);
        assertNull(result);
    }

    /**
     * Tests the scenario where calling queryShopOnlineDealGroups(request) throws an exception.
     */
    @Test(expected = Exception.class)
    public void testPreShopOnlineDealGroupsWithException() throws Throwable {
        setUpMocks();
        List<Integer> shopIds = Arrays.asList(1, 2, 3);
        int cityId = 1;
        doThrow(new Exception()).when(shopOnlineDealGroupServiceFuture).queryLongShopOnlineDealGroups(any(ShopOnlineDealGroupRequest.class));
        moreDealsWrapper.preShopOnlineDealGroups(shopIds, cityId);
    }
}
