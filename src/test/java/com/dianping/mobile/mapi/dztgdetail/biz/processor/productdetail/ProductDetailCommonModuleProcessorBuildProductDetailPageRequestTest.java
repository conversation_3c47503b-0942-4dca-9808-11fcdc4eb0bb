package com.dianping.mobile.mapi.dztgdetail.biz.processor.productdetail;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.util.CommonModuleUtil;
import com.google.common.collect.Sets;
import com.sankuai.dz.product.detail.gateway.api.bff.enums.PageConfigSceneEnum;
import com.sankuai.dz.product.detail.gateway.api.bff.request.PageConfigRoutingKey;
import com.sankuai.dz.product.detail.gateway.spi.dto.CustomParam;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.GpsCoordinateTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashSet;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ProductDetailCommonModuleProcessorBuildProductDetailPageRequestTest {

    @InjectMocks
    private ProductDetailCommonModuleProcessor processor;

    /**
     * Invokes the private buildProductDetailPageRequest method using reflection
     */
    private ProductDetailPageRequest invokePrivateMethod(DealCtx ctx) throws Exception {
        Method method = ProductDetailCommonModuleProcessor.class.getDeclaredMethod("buildProductDetailPageRequest", DealCtx.class);
        method.setAccessible(true);
        return (ProductDetailPageRequest) method.invoke(processor, ctx);
    }

    /**
     * Test case for Meituan client type
     */
    @Test
    public void testBuildProductDetailPageRequest_MeituanClientType() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        ctx.getEnvCtx().setUuid("test-uuid");
        // act
        ProductDetailPageRequest request = invokePrivateMethod(ctx);
        // assert
        assertEquals("test-uuid", request.getShepherdGatewayParam().getDeviceId());
        assertEquals(ClientTypeEnum.MT_APP.getCode(), request.getClientType());
    }

    /**
     * Test case for Dianping client type
     */
    @Test
    public void testBuildProductDetailPageRequest_DianpingClientType() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        ctx.getEnvCtx().setDpId("dp-123");
        // act
        ProductDetailPageRequest request = invokePrivateMethod(ctx);
        // assert
        assertEquals("dp-123", request.getShepherdGatewayParam().getDeviceId());
        assertEquals(ClientTypeEnum.DP_APP.getCode(), request.getClientType());
    }

    /**
     * Test case with complete category information
     */
    @Test
    public void testBuildProductDetailPageRequest_WithCompleteCategory() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        when(category.getCategoryId()).thenReturn(123L);
        when(category.getServiceTypeId()).thenReturn(456L);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        ctx.setDealGroupDTO(dealGroupDTO);
        // act
        ProductDetailPageRequest request = invokePrivateMethod(ctx);
        // assert
        PageConfigRoutingKey routingKey = request.getPageConfigRoutingKey();
        assertEquals(123, routingKey.getProductSecondCategoryId());
        assertEquals(456, routingKey.getProductThirdCategoryId());
        assertEquals(ProductTypeEnum.DEAL.getCode(), routingKey.getProductType());
    }

    /**
     * Test case with null category
     */
    @Test
    public void testBuildProductDetailPageRequest_WithNullCategory() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getCategory()).thenReturn(null);
        ctx.setDealGroupDTO(dealGroupDTO);
        // act
        ProductDetailPageRequest request = invokePrivateMethod(ctx);
        // assert
        // Since PageConfigRoutingKey initializes with default values, we can't assert null
        // Instead, we verify that the values weren't set from category (which would be non-zero)
        PageConfigRoutingKey routingKey = request.getPageConfigRoutingKey();
        assertEquals(0, routingKey.getProductSecondCategoryId());
        assertEquals(0, routingKey.getProductThirdCategoryId());
    }

    /**
     * Test case with partial category info (only categoryId)
     */
    @Test
    public void testBuildProductDetailPageRequest_WithPartialCategory() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        when(category.getCategoryId()).thenReturn(123L);
        when(category.getServiceTypeId()).thenReturn(null);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        ctx.setDealGroupDTO(dealGroupDTO);
        // act
        ProductDetailPageRequest request = invokePrivateMethod(ctx);
        // assert
        PageConfigRoutingKey routingKey = request.getPageConfigRoutingKey();
        assertEquals(123, routingKey.getProductSecondCategoryId());
        // Since serviceTypeId is null, the third category ID should remain at default value
        assertEquals(0, routingKey.getProductThirdCategoryId());
    }

    /**
     * Test case for basic parameter setting
     */
    @Test
    public void testBuildProductDetailPageRequest_BasicParameters() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        EnvCtx envCtx = new EnvCtx();
        // Mock the necessary methods instead of setting fields directly
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(ctx.getCityId4P()).thenReturn(1);
        when(ctx.getCityLatitude()).thenReturn(31.23);
        when(ctx.getCityLongitude()).thenReturn(121.47);
        when(ctx.getCx()).thenReturn("test-cx");
        when(ctx.getGpsCityId()).thenReturn(2);
        when(ctx.getUserlat()).thenReturn(31.24);
        when(ctx.getUserlng()).thenReturn(121.48);
        when(ctx.getSkuId()).thenReturn("123");
        when(ctx.getDealId4P()).thenReturn(456);
        when(ctx.getLongPoiId4PFromResp()).thenReturn(789L);
        when(ctx.getRequestSource()).thenReturn("test-source");
        // Ensure no NPE
        when(ctx.getDealGroupDTO()).thenReturn(null);
        // act
        ProductDetailPageRequest request = invokePrivateMethod(ctx);
        // assert
        assertEquals(1, request.getCityId());
        assertEquals(31.23, request.getCityLat(), 0.01);
        assertEquals(121.47, request.getCityLng(), 0.01);
        assertEquals("test-cx", request.getCx());
        assertEquals(2, request.getGpsCityId());
        assertEquals(31.24, request.getUserLat(), 0.01);
        assertEquals(121.48, request.getUserLng(), 0.01);
        assertEquals(123L, request.getSkuId());
        assertEquals(456, request.getProductId());
        assertEquals(789L, request.getPoiId());
        assertEquals("test-source", request.getPageSource());
    }

    /**
     * Test case for unknown client type
     */
    @Test
    public void testBuildProductDetailPageRequest_UnknownClientType() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.getEnvCtx().setDztgClientTypeEnum(null);
        // act
        ProductDetailPageRequest request = invokePrivateMethod(ctx);
        // assert
        assertEquals(ClientTypeEnum.UNKNOWN.getCode(), request.getClientType());
    }

    /**
     * Test case for Meituan WeChat Mini Program client type
     */
    @Test
    public void testBuildProductDetailPageRequest_MeituanWeixinMiniapp() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP);
        ctx.getEnvCtx().setUuid("mini-uuid");
        // act
        ProductDetailPageRequest request = invokePrivateMethod(ctx);
        // assert
        assertEquals("mini-uuid", request.getShepherdGatewayParam().getDeviceId());
        assertEquals(ClientTypeEnum.MT_XCX.getCode(), request.getClientType());
    }

    /**
     * Test case for Dianping WeChat Mini Program client type
     */
    @Test
    public void testBuildProductDetailPageRequest_DianpingWeixinMiniapp() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.getEnvCtx().setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP);
        ctx.getEnvCtx().setDpId("dp-mini-123");
        // act
        ProductDetailPageRequest request = invokePrivateMethod(ctx);
        // assert
        assertEquals("dp-mini-123", request.getShepherdGatewayParam().getDeviceId());
        assertEquals(ClientTypeEnum.DP_XCX.getCode(), request.getClientType());
    }

    /**
     * Test case for ShepherdGatewayParam settings
     */
    @Test
    public void testBuildProductDetailPageRequest_ShepherdGatewayParam() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        EnvCtx envCtx = mock(EnvCtx.class);
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_APP);
        when(envCtx.isMt()).thenReturn(true);
        when(envCtx.getUuid()).thenReturn("test-uuid");
        when(envCtx.getMtUserId()).thenReturn(12345L);
        when(envCtx.getDpUserId()).thenReturn(67890L);
        when(envCtx.getMtVirtualUserId()).thenReturn(111111L);
        when(envCtx.getDpVirtualUserId()).thenReturn(222222L);
        when(envCtx.getVersion()).thenReturn("10.0.0");
        when(envCtx.getUnionId()).thenReturn("test-union-id");
        when(ctx.getWxVersion()).thenReturn("wx-version-1.0");
        when(ctx.getMrnVersion()).thenReturn("mrn-version-2.0");
        // Ensure no NPE
        when(ctx.getDealGroupDTO()).thenReturn(null);
        // act
        ProductDetailPageRequest request = invokePrivateMethod(ctx);
        // assert
        assertEquals("mrn-version-2.0", request.getShepherdGatewayParam().getMrnVersion());
        assertEquals("wx-version-1.0", request.getShepherdGatewayParam().getCsecversionname());
        assertEquals("test-union-id", request.getShepherdGatewayParam().getUnionid());
        assertEquals("test-uuid", request.getShepherdGatewayParam().getDeviceId());
        assertEquals(12345L, request.getShepherdGatewayParam().getMtUserId());
        assertEquals(67890L, request.getShepherdGatewayParam().getDpUserId());
        assertEquals(111111L, request.getShepherdGatewayParam().getMtVirtualUserId());
        assertEquals(222222L, request.getShepherdGatewayParam().getDpVirtualUserId());
        assertEquals("10.0.0", request.getShepherdGatewayParam().getAppVersion());
        assertEquals("ios", request.getShepherdGatewayParam().getMobileOSType());
    }
}
