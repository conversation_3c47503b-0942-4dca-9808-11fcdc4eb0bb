package com.dianping.mobile.mapi.dztgdetail.button.mtlive;

import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MtLiveSaleStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.PriceHelper;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.PriceDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.general.product.query.center.client.util.DealGroupUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class MtLiveMiniAppButtonBuilderTest {
    @InjectMocks
    private MtLiveMiniAppButtonBuilder builder;

    @Mock
    private ButtonBuilderChain chain;

    @Mock
    private PriceDisplayDTO mtLivePrice;

    @Mock
    private PromoDTO promoDTO;

    private MockedStatic<PriceHelper> priceHelperMocked;

    private MockedStatic<DealUtils> dealUtilsMocked;

    private MockedStatic<DealGroupUtils> dealGroupUtilsMocked;

    @Before
    public void setUp() {
        priceHelperMocked = mockStatic(PriceHelper.class);
        dealUtilsMocked = mockStatic(DealUtils.class);
        dealGroupUtilsMocked = mockStatic(DealGroupUtils.class);
    }

    @After
    public void tearDown() {
        priceHelperMocked.close();
        dealUtilsMocked.close();
        dealGroupUtilsMocked.close();
    }

    /**
     * 测试doBuild方法，当价格信息中的优惠信息为空时，按钮的标签应该为空
     */
    @Test
    public void testDoBuildWhenPromosIsEmpty() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_LIVE_WEIXIN_MINIAPP);
        DealCtx dealCtx = new DealCtx(envCtx);
        DealGroupBaseDTO dealGroupBaseDTO = new DealGroupBaseDTO();
        dealCtx.setDealGroupBase(dealGroupBaseDTO);
        dealCtx.setMarketPriceHided(true);
        dealCtx.setMtLiveSaleStatusEnum(MtLiveSaleStatusEnum.SNAP_UP_NOW);
        when(mtLivePrice.getUsedPromos()).thenReturn(Collections.emptyList());
        when(mtLivePrice.getPrice()).thenReturn(new BigDecimal(100));
        priceHelperMocked.when(() -> PriceHelper.getNormalPrice(dealCtx)).thenReturn(mtLivePrice);
        priceHelperMocked.when(() -> PriceHelper.dropLastZero(any())).thenReturn("100");
        // act
        builder.doBuild(dealCtx, chain);

        // assert
        assertEquals(dealCtx.getBuyBar().getBuyBtns().get(0).getBtnTitle(), "立即抢购");
    }

    /**
     * 测试doBuild方法，当交易上下文中的预付价格存在时，按钮的标题应该是“立即支付预付款”
     */
    @Test
    public void testDoBuildWhenPrePayPriceExists() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_LIVE_WEIXIN_MINIAPP);
        DealCtx dealCtx = new DealCtx(envCtx);
        DealGroupBaseDTO dealGroupBaseDTO = new DealGroupBaseDTO();
        dealCtx.setDealGroupBase(dealGroupBaseDTO);
        dealCtx.setMarketPriceHided(true);
        dealCtx.setMtLiveSaleStatusEnum(MtLiveSaleStatusEnum.SNAP_UP_NOW);

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealCtx.setDealGroupDTO(dealGroupDTO);
        DealGroupDealDTO deal = new DealGroupDealDTO();
        List<DealGroupDealDTO> deals = Lists.newArrayList(deal);
        dealGroupDTO.setDeals(deals);
        PriceDTO priceDTO = new PriceDTO();
        priceDTO.setPrePayPrice("50.12"); // 设置预付价格
        priceDTO.setFinalPayPrice("100"); // 设置尾款价格
        deal.setPrice(priceDTO);

        dealUtilsMocked.when(() -> DealUtils.isPrePayDeal(dealCtx)).thenReturn(true); // 模拟预付商品
        dealGroupUtilsMocked.when(() -> DealGroupUtils.convertPrice(priceDTO.getPrePayPrice())).thenReturn(new BigDecimal(50.12)); // 模拟转换后的预付价格字符串
        dealGroupUtilsMocked.when(() -> DealGroupUtils.convertPrice(priceDTO.getFinalPayPrice())).thenReturn(new BigDecimal(100));

        // act
        builder.doBuild(dealCtx, chain);

        // assert
        assertEquals("立即支付预付款", dealCtx.getBuyBar().getBuyBtns().get(0).getBtnTitle());
        assertEquals("￥50.12 立即支付预付款", dealCtx.getBuyBar().getBuyBtns().get(0).getBtnText());
        assertEquals("剩余尾款￥100", dealCtx.getBuyBar().getBuyBtns().get(0).getBtnTag());
    }

    @Test
    public void testDoBuildWhenPrepayPriceNotExist() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_LIVE_WEIXIN_MINIAPP);
        DealCtx dealCtx = new DealCtx(envCtx);
        DealGroupBaseDTO dealGroupBaseDTO = new DealGroupBaseDTO();
        dealCtx.setDealGroupBase(dealGroupBaseDTO);
        dealCtx.setMarketPriceHided(true);
        dealCtx.setMtLiveSaleStatusEnum(MtLiveSaleStatusEnum.SNAP_UP_NOW);

        dealUtilsMocked.when(() -> DealUtils.isPrePayDeal(dealCtx)).thenReturn(true); // 模拟预付商品
        when(mtLivePrice.getUsedPromos()).thenReturn(Collections.emptyList());
        when(mtLivePrice.getPrice()).thenReturn(new BigDecimal(100));
        priceHelperMocked.when(() -> PriceHelper.getNormalPrice(dealCtx)).thenReturn(mtLivePrice);
        priceHelperMocked.when(() -> PriceHelper.dropLastZero(any())).thenReturn("100");
        // act
        builder.doBuild(dealCtx, chain);

        // assert
        assertEquals(dealCtx.getBuyBar().getBuyBtns().get(0).getBtnTitle(), "立即抢购");
    }

}
