package com.dianping.mobile.mapi.dztgdetail.biz.service.recommend;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.Environment;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.entity.Response;
import com.dianping.martgeneral.recommend.api.service.RecommendService;
import com.dianping.mobile.mapi.dztgdetail.biz.service.RelatedRecommendService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.ShopUuidUtils;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.model.RecommendShopBaseInfoModel;
import com.dianping.mobile.mapi.dztgdetail.common.model.RecommendUserBaseInfoModel;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.RelatedRecommendCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedRecommendReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedDealPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedRecommendVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedShopPBO;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.mockito.Mockito.mockStatic;

/**
 * @Author: <EMAIL>
 * @Date: 2024/11/18
 */
@RunWith(MockitoJUnitRunner.class)
public class InShopRecommendHandlerTest {
    @InjectMocks
    private InShopRecommendHandler inShopRecommendHandler;
    @Mock
    private RelatedRecommendService relatedRecommendService;
    @Mock(name = "generalRecommendService")
    private RecommendService recommendService;
    @Mock
    private MapperWrapper mapperWrapper;
    @Mock
    private DealGroupWrapper dealGroupWrapper;
    private MockedStatic<LionConfigUtils> lionConfigUtilsMockedStatic;
    private MockedStatic<Environment> environmentMockedStatic;
    private MockedStatic<ShopUuidUtils> shopUuidUtilsMockedStatic;

    @Before
    public void setUp() {
        lionConfigUtilsMockedStatic = mockStatic(LionConfigUtils.class);
        environmentMockedStatic = mockStatic(Environment.class);
        shopUuidUtilsMockedStatic = mockStatic(ShopUuidUtils.class);
    }

    @After
    public void tearDown() {
        lionConfigUtilsMockedStatic.close();
        environmentMockedStatic.close();
        shopUuidUtilsMockedStatic.close();
    }

    @Test
    public void testValidate() {
        RelatedRecommendCtx relatedRecommendCtx = RelatedRecommendCtx.builder().build();
        RelatedRecommendReq req = new RelatedRecommendReq();
        req.setDealGroupId(123);
        req.setShopIdStr("1");
        req.setShopIdStrEncrypt("abc");
        relatedRecommendCtx.setReq(req);
        Assert.assertTrue(inShopRecommendHandler.validate(relatedRecommendCtx));
    }

    @Test
    public void testGetResult() {
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.hitHideInShopRecommendCategoryIds(Mockito.anyInt())).thenReturn(false);
        environmentMockedStatic.when(() -> Environment.isTestEnv()).thenReturn(false);
        RelatedRecommendCtx ctx = JSON.parseObject("{\"dealGroupDTO\":{\"basic\":{\"categoryId\":502},\"category\":{\"categoryId\":502},\"dpDealGroupId\":**********,\"dpDealGroupIdInt\":**********,\"mtDealGroupId\":**********,\"mtDealGroupIdInt\":**********},\"envCtx\":{\"android\":false,\"apollo\":false,\"appDeviceId\":\"e931975d53f94652a9aee6cac17acca80000000000001453000\",\"appId\":10,\"clientType\":200502,\"dianpingClientList\":[\"DIANPING_APP\",\"DIANPING_FROM_H5\",\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"],\"dp\":false,\"dpId\":\"e931975d53f94652a9aee6cac17acca80000000000001453000\",\"dpMerchant\":false,\"dpMiniApp\":false,\"dpMiniAppList\":[\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"],\"dpUserId\":0,\"dpVirtualUserId\":0,\"dztgClientTypeEnum\":\"MEITUAN_APP\",\"external\":false,\"externalAndNoScene\":false,\"fromH5\":false,\"ios\":true,\"login\":true,\"mainApp\":true,\"mainWX\":false,\"mainWeb\":false,\"meituanClientList\":[\"MEITUAN_APP\",\"MEITUAN_FROM_H5\",\"MEITUAN_MAP_APP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_LIVE_WEIXIN_MINIAPP\"],\"merchantClientList\":[\"DPMERCHANT\"],\"miniApp\":false,\"mt\":true,\"mtLiveMinApp\":false,\"mtMiniApp\":false,\"mtMiniAppList\":[\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_KUAISHOU_MINIAPP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_LIVE_WEIXIN_MINIAPP\"],\"mtUserId\":**********,\"mtVirtualUserId\":0,\"native\":true,\"nativeAppList\":[\"MEITUAN_APP\",\"MEITUAN_MAP_APP\",\"DIANPING_APP\"],\"requestURI\":\"/general/platform/dztgdetail/crossshoprecommend.bin\",\"startTime\":1731641022044,\"thirdPlatform\":false,\"unionId\":\"e931975d53f94652a9aee6cac17acca80000000000001453000\",\"userAgent\":\"MApi 1.3 (mtscope 12.25.400 appstore; iPhone 16.2 iPhone13,2; a0d0)\",\"userId\":**********,\"userIp\":\"***************\",\"uuid\":\"0000000000000BF83E0F7C51E4E65A63EC4EA35305780A162880001982084426\",\"version\":\"12.25.400\",\"virtualUserId\":0,\"wxMini\":false,\"wxMiniList\":[\"MEITUAN_WEIXIN_MINIAPP\",\"DIANPING_WEIXIN_MINIAPP\"]},\"relatedRecommendConfig\":{\"dealDisplayMaxValue\":\"3\",\"useAdHangPrice\":\"false\",\"similarItemText\":\"相似好价，比出来的好价 >\",\"inShopRecommendModuleButtonText\":\"查看全部（%s个）>\"},\"req\":{\"cityId\":10,\"dealGroupId\":**********,\"gpsCityId\":0,\"limit\":10,\"shopIdStr\":\"439240177\",\"start\":1,\"userLat\":31.27130171133249,\"userLng\":121.************},\"result\":{\"isEnd\":false,\"nextStartIndex\":0,\"recordCount\":0,\"startIndex\":0}}", RelatedRecommendCtx.class);
        RecommendShopBaseInfoModel shopBaseInfoModel = new RecommendShopBaseInfoModel();
        shopBaseInfoModel.setBackCategory(Lists.newArrayList(1,2,3));
        Mockito.when(relatedRecommendService.preBuildShopBaseInfoModel(Mockito.any())).thenReturn(shopBaseInfoModel);
//        Mockito.when(relatedRecommendService.preBuildUserBaseInfoModel(Mockito.any())).thenReturn(new RecommendUserBaseInfoModel());
        RecommendUserBaseInfoModel userBaseInfoModel = new RecommendUserBaseInfoModel();
        Mockito.when(relatedRecommendService.doBuildShopBaseInfoModel(Mockito.any(), Mockito.any())).thenReturn(shopBaseInfoModel);
//        Mockito.when(relatedRecommendService.doBuildUserBaseInfoModel(Mockito.any(), Mockito.any())).thenReturn(userBaseInfoModel);
        BiMap<Integer, Integer> dpMtIdBiMap = HashBiMap.create();
        dpMtIdBiMap.put(**********, **********);
        Mockito.when(relatedRecommendService.getDpMtIdBiMap(Mockito.any(), Mockito.anyList())).thenReturn(dpMtIdBiMap);
        Response<RecommendResult<Object>> resultResponse = new Response<RecommendResult<Object>>();
        RecommendResult<Object> recommendResult = new RecommendResult<>();
        recommendResult.setTotalSize(100);
        recommendResult.setSortedResult(Lists.newArrayList(new RecommendDTO()));
        resultResponse.setResult(recommendResult);
        Mockito.when(recommendService.recommend(Mockito.any(), Mockito.any())).thenReturn(resultResponse);
        Assert.assertTrue(Objects.nonNull(inShopRecommendHandler.getResult(ctx)));
    }

    @Test
    public void testFill() {
        RelatedRecommendCtx relatedRecommendCtx = JSON.parseObject("{\"req\":{\"dealGroupId\":885765881,\"shopIdStr\":\"**********\",\"cityId\":10,\"userLng\":121.************,\"userLat\":31.27130171133249,\"gpsCityId\":0},\"dealGroupDTO\":{\"dpDealGroupId\":885765881,\"mtDealGroupId\":885765881,\"basic\":{\"categoryId\":501},\"category\":{\"categoryId\":501},\"dpDealGroupIdInt\":885765881,\"mtDealGroupIdInt\":885765881},\"dpPoiDTO\":{\"shopId\":**********,\"cityId\":1,\"backMainCategoryPath\":[{\"categoryId\":2,\"categoryName\":\"丽人\",\"parentId\":0,\"hot\":0,\"categoryLevel\":1,\"leaf\":false},{\"categoryId\":38,\"categoryName\":\"美发\",\"parentId\":2,\"hot\":0,\"categoryLevel\":2,\"leaf\":true,\"main\":true}],\"refinedScore1\":\"\",\"refinedScore2\":\"\",\"refinedScore3\":\"\",\"fiveScore\":0.0,\"sub5\":0,\"sub6\":0,\"fiveSub1\":0.0,\"fiveSub2\":0.0,\"fiveSub3\":0.0,\"fiveSub4\":0.0,\"fiveSub5\":0.0,\"fiveSub6\":0.0},\"envCtx\":{\"dpUserId\":0,\"dpVirtualUserId\":0,\"mtUserId\":131831705,\"mtVirtualUserId\":0,\"unionId\":\"e931975d53f94652a9aee6cac17acca80000000000001453000\",\"dpId\":\"e931975d53f94652a9aee6cac17acca80000000000001453000\",\"uuid\":\"0000000000000BF83E0F7C51E4E65A63EC4EA35305780A162880001982084426\",\"version\":\"12.25.400\",\"clientType\":200502,\"appDeviceId\":\"e931975d53f94652a9aee6cac17acca80000000000001453000\",\"appId\":10,\"requestURI\":\"/general/platform/dztgdetail/inshoprecommend.bin\",\"userAgent\":\"MApi 1.3 (mtscope 12.25.400 appstore; iPhone 16.2 iPhone13,2; a0d0)\",\"userIp\":\"***************\",\"dztgClientTypeEnum\":\"MEITUAN_APP\",\"startTime\":1732023912525,\"meituanClientList\":[\"MEITUAN_APP\",\"MEITUAN_FROM_H5\",\"MEITUAN_MAP_APP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_LIVE_WEIXIN_MINIAPP\"],\"dianpingClientList\":[\"DIANPING_APP\",\"DIANPING_FROM_H5\",\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"],\"merchantClientList\":[\"DPMERCHANT\"],\"mtMiniAppList\":[\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_KUAISHOU_MINIAPP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_LIVE_WEIXIN_MINIAPP\"],\"dpMiniAppList\":[\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"],\"nativeAppList\":[\"MEITUAN_APP\",\"MEITUAN_MAP_APP\",\"DIANPING_APP\"],\"wxMiniList\":[\"MEITUAN_WEIXIN_MINIAPP\",\"DIANPING_WEIXIN_MINIAPP\"],\"android\":false,\"fromH5\":false,\"mt\":true,\"apollo\":false,\"login\":true,\"mainApp\":true,\"miniApp\":false,\"thirdPlatform\":false,\"ios\":true,\"dp\":false,\"mtMiniApp\":false,\"dpMiniApp\":false,\"mainWX\":false,\"externalAndNoScene\":false,\"mtLiveMinApp\":false,\"mainWeb\":false,\"wxMini\":false,\"userId\":131831705,\"virtualUserId\":0,\"dpMerchant\":false,\"external\":false,\"native\":true},\"dealGroupShopMap\":{\"*********\":{\"dealGroupId\":*********,\"shopId\":**********,\"longShopId\":**********,\"cityId\":1,\"shopName\":\"LIN GA男士发型设计(白玉兰店)\",\"phoneNo\":\"***********\",\"phoneNo2\":\"\",\"businessHours\":\"周一至周日 11:30-21:00\",\"address\":\"-东长治路588号白玉兰广场LG2楼层12号\",\"shopPower\":45,\"shopType\":50,\"distance\":\"3789\",\"distanceInMeter\":3789,\"branchName\":\"白玉兰店\",\"districtId\":9,\"mainRegionId\":825,\"mainCategoryId\":157,\"power\":5,\"shopGroupId\":**********,\"longShopGroupId\":**********,\"googleLat\":31.249059,\"googleLng\":121.498771,\"shopPic\":\"https://p0.meituan.net/merchantpic/4b2f48f9b9aa884ffe39cab67411381d176476.jpg\"},\"*********\":{\"dealGroupId\":*********,\"shopId\":**********,\"longShopId\":**********,\"cityId\":1,\"shopName\":\"LIN GA男士发型设计(白玉兰店)\",\"phoneNo\":\"***********\",\"phoneNo2\":\"\",\"businessHours\":\"周一至周日 11:30-21:00\",\"address\":\"-东长治路588号白玉兰广场LG2楼层12号\",\"shopPower\":45,\"shopType\":50,\"distance\":\"3789\",\"distanceInMeter\":3789,\"branchName\":\"白玉兰店\",\"districtId\":9,\"mainRegionId\":825,\"mainCategoryId\":157,\"power\":5,\"shopGroupId\":**********,\"longShopGroupId\":**********,\"googleLat\":31.249059,\"googleLng\":121.498771,\"shopPic\":\"https://p1.meituan.net/merchantpic/39f8cb52f217bac9ed90a0a38b671c87143433.jpg\"},\"*********\":{\"dealGroupId\":*********,\"shopId\":**********,\"longShopId\":**********,\"cityId\":1,\"shopName\":\"LIN GA男士发型设计(白玉兰店)\",\"phoneNo\":\"***********\",\"phoneNo2\":\"\",\"businessHours\":\"周一至周日 11:30-21:00\",\"address\":\"-东长治路588号白玉兰广场LG2楼层12号\",\"shopPower\":45,\"shopType\":50,\"distance\":\"3789\",\"distanceInMeter\":3789,\"branchName\":\"白玉兰店\",\"districtId\":9,\"mainRegionId\":825,\"mainCategoryId\":157,\"power\":5,\"shopGroupId\":**********,\"longShopGroupId\":**********,\"googleLat\":31.249059,\"googleLng\":121.498771,\"shopPic\":\"https://p0.meituan.net/merchantpic/774163902e37db4d46e4351b65c08020154055.jpg\"}},\"mtByDpShopIds\":{\"**********\":[**********,1240443533,1546146491]},\"priceDisplayMap\":{\"*********\":{\"identity\":{\"spuId\":0,\"spuSceneType\":0,\"productId\":*********,\"productType\":1,\"cityId\":0,\"skuId\":*********,\"categoryId\":0,\"productIdL\":0,\"skuIdL\":0,\"priceTime\":0,\"priceTrendType\":0,\"lskuId\":*********,\"lproductId\":*********},\"price\":1065.00,\"maxPrice\":1065.00,\"basePrice\":1080.00,\"marketPrice\":1180.00,\"showMarketPrice\":false,\"promoAmount\":115.00,\"usedPromos\":[{\"identity\":{\"promoId\":*********,\"promoType\":11,\"promoTypeDesc\":\"团购优惠\",\"sourceType\":1,\"promoShowType\":\"DEAL_PROMO\"},\"amount\":100.00,\"tag\":\"团购优惠100元\",\"description\":\"团购优惠100元\",\"priceThrough\":true,\"canAssign\":false,\"extendDesc\":\"\",\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoStatus\":0,\"couponValueType\":0,\"promoTextDTO\":{\"title\":\"团购优惠\",\"subTitle\":\"\",\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoDivideType\":\"DEAL_PROMO\"},\"newUser\":false},{\"identity\":{\"promoId\":1949972214,\"promoType\":5,\"promoTypeDesc\":\"丽人通用神券\",\"sourceType\":1,\"promoShowType\":\"MAGICAL_MEMBER_PLATFORM_COUPON\"},\"amount\":15,\"priceThrough\":true,\"canAssign\":false,\"extendDesc\":\"丽人通用神券，满129元减15元\",\"startTime\":1731982192000,\"endTime\":1732068592000,\"icon\":\"https://p0.meituan.net/travelcube/d5ba4048dc741674e6497f4c0c9ef55e1173.png\",\"couponTitle\":\"丽人通用神券\",\"minConsumptionAmount\":129,\"promoIdentity\":\"freeMagicalMemberCoupon\",\"promoStatus\":0,\"couponGroupId\":\"1949972214\",\"couponId\":\"401705103378406886\",\"couponValueType\":2,\"couponValueText\":\"15\",\"promoTextDTO\":{\"title\":\"美团优惠券\",\"subTitle\":\"丽人通用神券，满129元减15元\",\"icon\":\"https://p0.meituan.net/travelcube/d5ba4048dc741674e6497f4c0c9ef55e1173.png\",\"promoDivideType\":\"MAGICAL_MEMBER_PLATFORM_COUPON\",\"promoDivideTypeDesc\":\"神会员平台券\"},\"amountShareDetail\":{\"1\":15,\"2\":0},\"promotionExplanatoryTags\":[4],\"promotionOtherInfoMap\":{\"ASSIGNED_STATUS\":\"ALREADY_ASSIGNED\",\"ASSET_TYPE\":\"2\"},\"promotionDisplayTextMap\":{\"topLeftIcon\":\"https://p0.meituan.net/ingee/0b375017c95f811adfb233e0774d4f524980.png\"},\"newUser\":false}],\"morePromos\":[],\"pricePromoInfoMap\":{},\"extPrices\":[{\"extPriceType\":1,\"extPricePromoAmount\":0,\"extPrice\":1080.00,\"extPriceTitle\":\"全网低价\"}],\"promoTag\":\"特惠促销共省¥115\",\"shortPromoTag\":\"共省¥115\",\"promoDiscount\":0.91,\"promoTagType\":0,\"activityDTO\":{\"discountProvider\":1,\"discountClassifyType\":0},\"pricePowerTagDisplayDTO\":{\"allTagList\":[{\"subjectId\":\"*********\",\"tagType\":6,\"tagName\":\"全网低价\",\"tagMode\":3}],\"filteredTagList\":[{\"subjectId\":\"*********\",\"tagType\":6,\"tagName\":\"全网低价\",\"tagMode\":3}]},\"extendDisplayInfo\":{\"magicalMemberCouponLabel\":\"{\\\"priority\\\":0,\\\"magicalMemberCouponTag\\\":\\\"神券\\\",\\\"inflateShowText\\\":null,\\\"reduceMoney\\\":null,\\\"status\\\":null,\\\"showType\\\":null}\"}},\"*********\":{\"identity\":{\"spuId\":0,\"spuSceneType\":0,\"productId\":*********,\"productType\":1,\"cityId\":0,\"skuId\":*********,\"categoryId\":0,\"productIdL\":0,\"skuIdL\":0,\"priceTime\":0,\"priceTrendType\":0,\"lskuId\":*********,\"lproductId\":*********},\"price\":865.00,\"maxPrice\":865.00,\"basePrice\":880.00,\"marketPrice\":1078.00,\"showMarketPrice\":false,\"promoAmount\":213.00,\"usedPromos\":[{\"identity\":{\"promoId\":*********,\"promoType\":11,\"promoTypeDesc\":\"团购优惠\",\"sourceType\":1,\"promoShowType\":\"DEAL_PROMO\"},\"amount\":198.00,\"tag\":\"团购优惠198元\",\"description\":\"团购优惠198元\",\"priceThrough\":true,\"canAssign\":false,\"extendDesc\":\"\",\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoStatus\":0,\"couponValueType\":0,\"promoTextDTO\":{\"title\":\"团购优惠\",\"subTitle\":\"\",\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoDivideType\":\"DEAL_PROMO\"},\"newUser\":false},{\"identity\":{\"promoId\":1949972214,\"promoType\":5,\"promoTypeDesc\":\"丽人通用神券\",\"sourceType\":1,\"promoShowType\":\"MAGICAL_MEMBER_PLATFORM_COUPON\"},\"amount\":15,\"priceThrough\":true,\"canAssign\":false,\"extendDesc\":\"丽人通用神券，满129元减15元\",\"startTime\":1731982192000,\"endTime\":1732068592000,\"icon\":\"https://p0.meituan.net/travelcube/d5ba4048dc741674e6497f4c0c9ef55e1173.png\",\"couponTitle\":\"丽人通用神券\",\"minConsumptionAmount\":129,\"promoIdentity\":\"freeMagicalMemberCoupon\",\"promoStatus\":0,\"couponGroupId\":\"1949972214\",\"couponId\":\"401705103378406886\",\"couponValueType\":2,\"couponValueText\":\"15\",\"promoTextDTO\":{\"title\":\"美团优惠券\",\"subTitle\":\"丽人通用神券，满129元减15元\",\"icon\":\"https://p0.meituan.net/travelcube/d5ba4048dc741674e6497f4c0c9ef55e1173.png\",\"promoDivideType\":\"MAGICAL_MEMBER_PLATFORM_COUPON\",\"promoDivideTypeDesc\":\"神会员平台券\"},\"amountShareDetail\":{\"1\":15,\"2\":0},\"promotionExplanatoryTags\":[4],\"promotionOtherInfoMap\":{\"ASSIGNED_STATUS\":\"ALREADY_ASSIGNED\",\"ASSET_TYPE\":\"2\"},\"promotionDisplayTextMap\":{\"topLeftIcon\":\"https://p0.meituan.net/ingee/0b375017c95f811adfb233e0774d4f524980.png\"},\"newUser\":false}],\"morePromos\":[],\"pricePromoInfoMap\":{},\"extPrices\":[{\"extPriceType\":1,\"extPricePromoAmount\":0,\"extPrice\":880.00,\"extPriceTitle\":\"全网低价\"}],\"promoTag\":\"特惠促销共省¥213\",\"shortPromoTag\":\"共省¥213\",\"promoDiscount\":0.81,\"promoTagType\":0,\"activityDTO\":{\"discountProvider\":1,\"discountClassifyType\":0},\"pricePowerTagDisplayDTO\":{\"allTagList\":[{\"subjectId\":\"*********\",\"tagType\":6,\"tagName\":\"全网低价\",\"tagMode\":3}],\"filteredTagList\":[{\"subjectId\":\"*********\",\"tagType\":6,\"tagName\":\"全网低价\",\"tagMode\":3}]},\"extendDisplayInfo\":{\"magicalMemberCouponLabel\":\"{\\\"priority\\\":0,\\\"magicalMemberCouponTag\\\":\\\"神券\\\",\\\"inflateShowText\\\":null,\\\"reduceMoney\\\":null,\\\"status\\\":null,\\\"showType\\\":null}\"}},\"*********\":{\"identity\":{\"spuId\":0,\"spuSceneType\":0,\"productId\":*********,\"productType\":1,\"cityId\":0,\"skuId\":*********,\"categoryId\":0,\"productIdL\":0,\"skuIdL\":0,\"priceTime\":0,\"priceTrendType\":0,\"lskuId\":*********,\"lproductId\":*********},\"price\":153.00,\"maxPrice\":153.00,\"basePrice\":168.00,\"marketPrice\":198.00,\"showMarketPrice\":false,\"promoAmount\":45.00,\"usedPromos\":[{\"identity\":{\"promoId\":*********,\"promoType\":11,\"promoTypeDesc\":\"团购优惠\",\"sourceType\":1,\"promoShowType\":\"DEAL_PROMO\"},\"amount\":30.00,\"tag\":\"团购优惠30元\",\"description\":\"团购优惠30元\",\"priceThrough\":true,\"canAssign\":false,\"extendDesc\":\"\",\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoStatus\":0,\"couponValueType\":0,\"promoTextDTO\":{\"title\":\"团购优惠\",\"subTitle\":\"\",\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoDivideType\":\"DEAL_PROMO\"},\"newUser\":false},{\"identity\":{\"promoId\":1949972214,\"promoType\":5,\"promoTypeDesc\":\"丽人通用神券\",\"sourceType\":1,\"promoShowType\":\"MAGICAL_MEMBER_PLATFORM_COUPON\"},\"amount\":15,\"priceThrough\":true,\"canAssign\":false,\"extendDesc\":\"丽人通用神券，满129元减15元\",\"startTime\":1731982192000,\"endTime\":1732068592000,\"icon\":\"https://p0.meituan.net/travelcube/d5ba4048dc741674e6497f4c0c9ef55e1173.png\",\"couponTitle\":\"丽人通用神券\",\"minConsumptionAmount\":129,\"promoIdentity\":\"freeMagicalMemberCoupon\",\"promoStatus\":0,\"couponGroupId\":\"1949972214\",\"couponId\":\"401705103378406886\",\"couponValueType\":2,\"couponValueText\":\"15\",\"promoTextDTO\":{\"title\":\"美团优惠券\",\"subTitle\":\"丽人通用神券，满129元减15元\",\"icon\":\"https://p0.meituan.net/travelcube/d5ba4048dc741674e6497f4c0c9ef55e1173.png\",\"promoDivideType\":\"MAGICAL_MEMBER_PLATFORM_COUPON\",\"promoDivideTypeDesc\":\"神会员平台券\"},\"amountShareDetail\":{\"1\":15,\"2\":0},\"promotionExplanatoryTags\":[4],\"promotionOtherInfoMap\":{\"ASSIGNED_STATUS\":\"ALREADY_ASSIGNED\",\"ASSET_TYPE\":\"2\"},\"promotionDisplayTextMap\":{\"topLeftIcon\":\"https://p0.meituan.net/ingee/0b375017c95f811adfb233e0774d4f524980.png\"},\"newUser\":false}],\"morePromos\":[],\"pricePromoInfoMap\":{},\"extPrices\":[{\"extPriceType\":1,\"extPricePromoAmount\":0,\"extPrice\":168.00,\"extPriceTitle\":\"全网低价\"}],\"promoTag\":\"特惠促销共省¥45\",\"shortPromoTag\":\"共省¥45\",\"promoDiscount\":0.78,\"promoTagType\":0,\"activityDTO\":{\"discountProvider\":1,\"discountClassifyType\":0},\"pricePowerTagDisplayDTO\":{\"allTagList\":[{\"subjectId\":\"*********\",\"tagType\":6,\"tagName\":\"全网低价\",\"tagMode\":3}],\"filteredTagList\":[{\"subjectId\":\"*********\",\"tagType\":6,\"tagName\":\"全网低价\",\"tagMode\":3}]},\"extendDisplayInfo\":{\"magicalMemberCouponLabel\":\"{\\\"priority\\\":0,\\\"magicalMemberCouponTag\\\":\\\"神券\\\",\\\"inflateShowText\\\":null,\\\"reduceMoney\\\":null,\\\"status\\\":null,\\\"showType\\\":null}\"}}},\"productId2SaleMap\":{\"*********\":{\"cityId\":0,\"shopId\":0,\"productGroupId\":*********,\"sales\":3207,\"salesTag\":\"年售3000+\",\"merge\":true,\"cycle\":360},\"*********\":{\"cityId\":0,\"shopId\":0,\"productGroupId\":*********,\"sales\":5308,\"salesTag\":\"年售5000+\",\"merge\":true,\"cycle\":360},\"*********\":{\"cityId\":0,\"shopId\":0,\"productGroupId\":*********,\"sales\":5439,\"salesTag\":\"年售5000+\",\"merge\":true,\"cycle\":360}},\"dealGroupDTOMap\":{\"*********\":{\"dpDealGroupId\":*********,\"mtDealGroupId\":*********,\"basic\":{\"categoryId\":501,\"title\":\"【特享定制】首席烫或染套餐（不满意重做）\",\"brandName\":\"LIN GA 男士风格设计\",\"titleDesc\":\"仅售1080元，价值1180元【特享定制】首席烫或染套餐（不满意重做）！\",\"beginSaleDate\":\"2022-07-25 21:03:28\",\"endSaleDate\":\"2025-09-19 00:00:00\",\"status\":1,\"saleChannel\":0,\"salePlatform\":3,\"sourceId\":102,\"tradeType\":3},\"image\":{\"defaultPicPath\":\"https://p0.meituan.net/merchantpic/4b2f48f9b9aa884ffe39cab67411381d176476.jpg\",\"allPicPaths\":\"https://p0.meituan.net/merchantpic/4b2f48f9b9aa884ffe39cab67411381d176476.jpg\"},\"category\":{\"categoryId\":501},\"price\":{\"salePrice\":\"1080.00\",\"marketPrice\":\"1180.00\",\"version\":10747951405},\"extendImage\":{\"extendImages\":[{\"url\":\"https://p0.meituan.net/merchantpic/4b2f48f9b9aa884ffe39cab67411381d176476.jpg\"}]},\"dpDealGroupIdInt\":*********,\"mtDealGroupIdInt\":*********},\"*********\":{\"dpDealGroupId\":*********,\"mtDealGroupId\":*********,\"basic\":{\"categoryId\":501,\"title\":\"【精品尊享】精品尊享烫或染套餐（不满意重做）\",\"brandName\":\"LIN GA 男士风格设计\",\"titleDesc\":\"仅售880元，价值1078元【精品尊享】精品尊享烫或染套餐（不满意重做）！\",\"beginSaleDate\":\"2022-03-22 16:58:56\",\"endSaleDate\":\"2025-09-19 00:00:00\",\"status\":1,\"saleChannel\":0,\"salePlatform\":3,\"sourceId\":102,\"tradeType\":3},\"image\":{\"defaultPicPath\":\"https://p0.meituan.net/merchantpic/774163902e37db4d46e4351b65c08020154055.jpg\",\"allPicPaths\":\"https://p0.meituan.net/merchantpic/774163902e37db4d46e4351b65c08020154055.jpg\"},\"category\":{\"categoryId\":501},\"price\":{\"salePrice\":\"880.00\",\"marketPrice\":\"1078.00\",\"version\":11209767879},\"extendImage\":{\"extendImages\":[{\"url\":\"https://p0.meituan.net/merchantpic/774163902e37db4d46e4351b65c08020154055.jpg\"}]},\"dpDealGroupIdInt\":*********,\"mtDealGroupIdInt\":*********},\"*********\":{\"dpDealGroupId\":*********,\"mtDealGroupId\":*********,\"basic\":{\"categoryId\":501,\"title\":\"【美团专属】设计师定制裁剪造型（不满意重做）\",\"brandName\":\"LIN GA 男士风格设计\",\"titleDesc\":\"仅售168元，价值198元【美团专属】设计师定制裁剪造型（不满意重做）！\",\"beginSaleDate\":\"2022-03-22 15:27:31\",\"endSaleDate\":\"2025-09-19 00:00:00\",\"status\":1,\"saleChannel\":0,\"salePlatform\":3,\"sourceId\":102,\"tradeType\":3},\"image\":{\"defaultPicPath\":\"https://p1.meituan.net/merchantpic/39f8cb52f217bac9ed90a0a38b671c87143433.jpg\",\"allPicPaths\":\"https://p1.meituan.net/merchantpic/39f8cb52f217bac9ed90a0a38b671c87143433.jpg\"},\"category\":{\"categoryId\":501},\"price\":{\"salePrice\":\"168.00\",\"marketPrice\":\"198.00\",\"version\":11210046364},\"extendImage\":{\"extendImages\":[{\"url\":\"https://p1.meituan.net/merchantpic/39f8cb52f217bac9ed90a0a38b671c87143433.jpg\"}]},\"dpDealGroupIdInt\":*********,\"mtDealGroupIdInt\":*********}},\"result\":{\"recordCount\":1,\"startIndex\":0,\"isEnd\":true,\"nextStartIndex\":0},\"relatedRecommendConfig\":{\"dealDisplayMaxValue\":\"3\",\"useAdHangPrice\":\"false\",\"similarItemText\":\"相似好价，比出来的好价 >\",\"inShopRecommendModuleButtonText\":\"查看全部（%s个）>\"},\"dealShelfDealIds\":[*********,758392533,758397355,*********,*********,782234133,858214745,871208751,885765881,885912435,889482560,1151927915,1151937630,1188208796]}", RelatedRecommendCtx.class);
        EnvCtx envCtx = relatedRecommendCtx.getEnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        envCtx.setClientType(200502);
        DealCtx dealCtx = new DealCtx(envCtx);
        dealCtx.setMtLongShopId(**********L);
        dealCtx.setDpLongShopId(**********L);
        relatedRecommendCtx.setDealCtx(dealCtx);
        relatedRecommendCtx.setEnvCtx(envCtx);
        RecommendResult<RecommendDTO> recommendResult = JSON.parseObject("{\"sortedResult\":[{\"item\":\"9999\",\"type\":\"Product\",\"source\":\"UniformTransposeFilterProduct\",\"bizData\":{\"distance\":30000.0,\"productList\":\"*********:1001,*********:1001,*********:1001,885912435:1001,758397355:1001,782234133:1001,871208751:1001,758392533:1001,889482560:1001,858214745:1001\",\"productType\":\"1001\"}}],\"totalSize\":1,\"processId\":0,\"processCode\":\"681_mt_flow_1728628523675\",\"flowId\":\"925a1f1c-2a46-471f-aa3b-9fbf0e03ecf3\",\"bizData\":{\"adjustScoreByArithmeticCaseThenFilter.outputScoreField\":\"mergeModelScore\",\"itemBusinessFormulasFilter.businessFormulaChain\":\"item:bizData.productType=aviator[str(${productType})], item:bizData.productList=aviator[string.replace_all(${productList},',',':'+${productType}+',')+':'+${productType}]\",\"cityId\":\"10\",\"flowFlag\":\"007\",\"adjustScoreByArithmeticCaseThenFilter.then#1\":\"0.0\",\"adjustScoreByArithmeticCaseThenFilter.then#2\":\"double(score_model_logistic_default_ctr)*0.1+double(score_model_logistic_default_ctcvr)*0.9\",\"shelf_product_default_allProductMtlV9V5_rankscore.case#1\":\"score_model_logistic_default_ctr == nil\",\"then#1\":\"0.0\",\"shelf_product_default_allProductMtlV9V5_rankscore.case#2\":\"1==1\",\"babyAgeGroup\":\"None\",\"businessFormulaChain\":\"item:bizData.productType=aviator[str(${productType})], item:bizData.productList=aviator[string.replace_all(${productList},',',':'+${productType}+',')+':'+${productType}]\",\"cat1Ids\":\"\",\"outputScoreField\":\"mergeModelScore\",\"serviceappkey\":\"all_cat_shelves_product_recall_v1\",\"then#2\":\"double(score_model_logistic_default_ctr)*0.1+double(score_model_logistic_default_ctcvr)*0.9\",\"uniformTransposeFilter.outputKeyName\":\"productList\",\"adjustScoreByArithmeticCaseThenFilter.case#2\":\"1==1\",\"shopId\":\"**********\",\"itemPointAt\":\"tabId\",\"productType\":\"1001\",\"adjustScoreByArithmeticCaseThenFilter.case#1\":\"score_model_logistic_default_ctr == nil\",\"isLimited\":\"false\",\"all_life_pedicure_shelf_transpose_filter.outputValueSeparator\":\",\",\"all_all_product_pedicure_add_producttype.businessFormulaChain\":\"item:bizData.productType=aviator[str(${productType})], item:bizData.productList=aviator[string.replace_all(${productList},',',':'+${productType}+',')+':'+${productType}]\",\"case#1\":\"score_model_logistic_default_ctr == nil\",\"shelf_product_default_allProductMtlV9V5_rankscore.then#2\":\"double(score_model_logistic_default_ctr)*0.1+double(score_model_logistic_default_ctcvr)*0.9\",\"shelf_product_default_allProductMtlV9V5_rankscore.then#1\":\"0.0\",\"case#2\":\"1==1\",\"outputKeyName\":\"productList\",\"all_life_pedicure_removetab_filter.businessFormulaChain\":\"item:bizData.tabId=aviator[string.replace_all(tabId,'tab_','')]\",\"cat0Id\":\"38\",\"cat1Id\":\"2\",\"uniformTransposeFilter.outputValueSeparator\":\",\",\"planNo\":\"product_recmd_plan_v1\",\"uniformTransposeFilter.itemPointAt\":\"tabId\",\"outputValueSeparator\":\",\",\"bucketCode\":\"35\",\"shelf_product_default_allProductMtlV9V5_rankscore.outputScoreField\":\"mergeModelScore\",\"all_life_pedicure_shelf_transpose_filter.outputKeyName\":\"productList\",\"all_life_pedicure_shelf_transpose_filter.itemPointAt\":\"tabId\",\"tab_9999\":\"*********:1001,758392533:1001,758397355:1001,*********:1001,*********:1001,782234133:1001,858214745:1001,871208751:1001,885912435:1001,889482560:1001,1151927915:1001,1151937630:1001,1188208796:1001\"}}", new TypeReference<RecommendResult<RecommendDTO>>() {});
        List<RelatedDealPBO> relatedDealPBOList = JSON.parseObject("[{\"dpId\":0,\"mtId\":*********,\"shop\":{\"shopId\":**********,\"shopName\":\"LIN GA男士发型设计(白玉兰店)\",\"shopPower\":0,\"distanceDesc\":\"距您3.8km\",\"shopNum\":0,\"lat\":0.0,\"lng\":0.0,\"displayPosition\":1,\"shopType\":0,\"hideAddrEnable\":false,\"shopBizType\":0,\"hideStars\":false,\"buyBarIconType\":0,\"shopCategoryId\":0,\"lyyShop\":false},\"title\":\"【美团专属】设计师定制裁剪造型（不满意重做）\",\"saleDesc\":\"年售5000+\",\"dealContents\":[{\"type\":1,\"content\":\"https://p1.meituan.net/merchantpic/39f8cb52f217bac9ed90a0a38b671c87143433.jpg\"}],\"promoDetailModule\":{\"priceDisplayType\":0,\"promoPrice\":\"153\",\"marketPrice\":\"198\",\"showMarketPrice\":false,\"showBestPromoDetails\":false,\"marketPromoDiscount\":\"￥198.00\",\"showPriceCompareEntrance\":false,\"promoNewStyle\":false},\"detailUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=*********&poiid=**********\",\"itemTextInfos\":[]},{\"dpId\":0,\"mtId\":*********,\"shop\":{\"shopId\":**********,\"shopName\":\"LIN GA男士发型设计(白玉兰店)\",\"shopPower\":0,\"distanceDesc\":\"距您3.8km\",\"shopNum\":0,\"lat\":0.0,\"lng\":0.0,\"displayPosition\":1,\"shopType\":0,\"hideAddrEnable\":false,\"shopBizType\":0,\"hideStars\":false,\"buyBarIconType\":0,\"shopCategoryId\":0,\"lyyShop\":false},\"title\":\"【精品尊享】精品尊享烫或染套餐（不满意重做）\",\"saleDesc\":\"年售5000+\",\"dealContents\":[{\"type\":1,\"content\":\"https://p0.meituan.net/merchantpic/774163902e37db4d46e4351b65c08020154055.jpg\"}],\"promoDetailModule\":{\"priceDisplayType\":0,\"promoPrice\":\"865\",\"marketPrice\":\"1078\",\"showMarketPrice\":false,\"showBestPromoDetails\":false,\"marketPromoDiscount\":\"￥1078.00\",\"showPriceCompareEntrance\":false,\"promoNewStyle\":false},\"detailUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=*********&poiid=**********\",\"itemTextInfos\":[]},{\"dpId\":0,\"mtId\":*********,\"shop\":{\"shopId\":**********,\"shopName\":\"LIN GA男士发型设计(白玉兰店)\",\"shopPower\":0,\"distanceDesc\":\"距您3.8km\",\"shopNum\":0,\"lat\":0.0,\"lng\":0.0,\"displayPosition\":1,\"shopType\":0,\"hideAddrEnable\":false,\"shopBizType\":0,\"hideStars\":false,\"buyBarIconType\":0,\"shopCategoryId\":0,\"lyyShop\":false},\"title\":\"【特享定制】首席烫或染套餐（不满意重做）\",\"saleDesc\":\"年售3000+\",\"dealContents\":[{\"type\":1,\"content\":\"https://p0.meituan.net/merchantpic/4b2f48f9b9aa884ffe39cab67411381d176476.jpg\"}],\"promoDetailModule\":{\"priceDisplayType\":0,\"promoPrice\":\"1065\",\"marketPrice\":\"1180\",\"showMarketPrice\":false,\"showBestPromoDetails\":false,\"marketPromoDiscount\":\"￥1180.00\",\"showPriceCompareEntrance\":false,\"promoNewStyle\":false},\"detailUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=*********&poiid=**********\",\"itemTextInfos\":[]}]", new TypeReference<List<RelatedDealPBO>>() {});
        Mockito.when(relatedRecommendService.getInShopDeals(Mockito.any(), Mockito.any())).thenReturn(relatedDealPBOList);
        List<RelatedShopPBO> relatedShopPBOS = JSON.parseObject("[{\"avgPrice\":\"\",\"distanceDesc\":\"距您16km\",\"fiveScore\":\"0.0\",\"mainRegionName\":\"虹桥火车站/机场\",\"recommendInfo\":{\"recommendSource\":0},\"shopId\":607032354,\"shopName\":\"KTV新版上单门店\",\"shopPic\":\"http://p1.meituan.net/searchscenerec/a1f99544c203750af25ccf87395cfb49400458.png%40300w_225h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\",\"shopPower\":0,\"shopUrl\":\"imeituan://www.meituan.com/gc/poi/detail?id=607032354&mmcinflate=&mmcuse=&mmcbuy=&mmcfree=&channelType=\"},{\"avgPrice\":\"\",\"distanceDesc\":\"距您15km\",\"fiveScore\":\"0.0\",\"mainRegionName\":\"北新泾/淞虹路\",\"recommendInfo\":{\"recommendSource\":0},\"shopId\":439240177,\"shopName\":\"供应链自动化专用-丽人美甲门店\",\"shopPic\":\"http://p1.meituan.net/searchscenerec/92e8bc86c05b07e3b0bbd85a83d5206c279007.png%40300w_225h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\",\"shopPower\":0,\"shopUrl\":\"imeituan://www.meituan.com/gc/poi/detail?id=439240177&mmcinflate=&mmcuse=&mmcbuy=&mmcfree=&channelType=\"}]", new TypeReference<List<RelatedShopPBO>>() {});
//        Mockito.when(relatedRecommendService.getShops(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(relatedShopPBOS);
        Map<Long, List<Long>> dpMtIdMap = Maps.newHashMap();
        dpMtIdMap.put(885765881L, Lists.newArrayList(885765881L));
//        Mockito.when(mapperWrapper.queryDpByMtIdsL(Mockito.any())).thenReturn(dpMtIdMap);
//        Mockito.when(dealGroupWrapper.batchQuerySaleDealGroupId(Mockito.anyList(), Mockito.anyBoolean())).thenReturn(Lists.newArrayList(885765881L));
        shopUuidUtilsMockedStatic.when(() -> ShopUuidUtils.getUuidById(Mockito.anyLong())).thenReturn("1");
        Response<RecommendResult<RecommendDTO>> response = new Response<>();
        response.setResult(recommendResult);
        RelatedRecommendVO relatedRecommendVO = inShopRecommendHandler.fill(relatedRecommendCtx, response);
        Assert.assertTrue(relatedRecommendVO.getRecommendItemList().size() > 0);
    }
}
