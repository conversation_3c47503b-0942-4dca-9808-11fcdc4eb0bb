package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.mpproduct.idservice.api.response.BizSkuIdConvertResponse;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.concurrent.Future;
import java.util.Map;
import java.util.Collections;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class IdMappingWrapper_BizSkuIdToPtIdTest {

    @Mock
    private Future future;

    @Mock
    private BizSkuIdConvertResponse response;

    private IdMappingWrapper idMappingWrapper = new IdMappingWrapper();

    @Test
    public void testBizSkuIdToPtIdFutureIsNull() throws Exception {
        Map<Long, Long> result = idMappingWrapper.bizSkuIdToPtId(null);
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testBizSkuIdToPtIdFutureIsNotNullButGetFutureResultIsNull() throws Exception {
        when(future.get()).thenReturn(null);
        Map<Long, Long> result = idMappingWrapper.bizSkuIdToPtId(future);
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testBizSkuIdToPtIdFutureIsNotNullAndGetFutureResultIsNull() throws Exception {
        when(future.get()).thenReturn(response);
        when(response.isSuccess()).thenReturn(false);
        Map<Long, Long> result = idMappingWrapper.bizSkuIdToPtId(future);
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testBizSkuIdToPtIdFutureIsNotNullAndGetFutureResultIsNotNullButBizSkuIdConvertResultIsNull() throws Exception {
        when(future.get()).thenReturn(response);
        when(response.isSuccess()).thenReturn(true);
        when(response.getBizSkuIdConvertResult()).thenReturn(null);
        Map<Long, Long> result = idMappingWrapper.bizSkuIdToPtId(future);
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testBizSkuIdToPtIdFutureIsNotNullAndGetFutureResultIsNotNullAndBizSkuIdConvertResultIsNotNull() throws Exception {
        when(future.get()).thenReturn(response);
        when(response.isSuccess()).thenReturn(true);
        Map<Long, Long> expected = Collections.singletonMap(1L, 2L);
        when(response.getBizSkuIdConvertResult()).thenReturn(expected);
        Map<Long, Long> result = idMappingWrapper.bizSkuIdToPtId(future);
        assertEquals(expected, result);
    }
}
