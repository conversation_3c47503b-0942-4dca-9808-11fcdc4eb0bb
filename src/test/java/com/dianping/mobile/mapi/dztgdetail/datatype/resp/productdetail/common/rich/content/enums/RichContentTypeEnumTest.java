package com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.rich.content.enums;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.rich.content.PicRichContentVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.rich.content.TextRichContentVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

/**
 * Test cases for RichContentTypeEnum.fromCode method
 */
@RunWith(MockitoJUnitRunner.class)
public class RichContentTypeEnumTest {

    /**
     * Test fromCode with valid code 1 (TEXT)
     * Verifies the returned enum matches TEXT type with correct properties
     */
    @Test
    public void testFromCode_WithValidTextCode_ShouldReturnTextEnum() {
        // arrange
        int code = 1;
        // act
        RichContentTypeEnum result = RichContentTypeEnum.fromCode(code);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Should return TEXT enum", RichContentTypeEnum.TEXT, result);
        assertEquals("Code should be 1", 1, result.getCode());
        assertEquals("Description should match", "文字", result.getDesc());
        assertEquals("Class type should match", TextRichContentVO.class, result.getRichContentVOClass());
    }

    /**
     * Test fromCode with valid code 2 (PIC)
     * Verifies the returned enum matches PIC type with correct properties
     */
    @Test
    public void testFromCode_WithValidPicCode_ShouldReturnPicEnum() {
        // arrange
        int code = 2;
        // act
        RichContentTypeEnum result = RichContentTypeEnum.fromCode(code);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Should return PIC enum", RichContentTypeEnum.PIC, result);
        assertEquals("Code should be 2", 2, result.getCode());
        assertEquals("Description should match", "图片", result.getDesc());
        assertEquals("Class type should match", PicRichContentVO.class, result.getRichContentVOClass());
    }

    @Test
    public void testFromCode_WithValidCode_ShouldReturnEnum() {
        // arrange
        int code = 1;
        // act
        RichContentTypeEnum result = RichContentTypeEnum.fromCode(code);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Should return TEXT enum", RichContentTypeEnum.TEXT, result);
        assertEquals("Code should be 1", 1, result.getCode());
        assertEquals("Description should match", "文字", result.getDesc());
        assertEquals("Class type should match", TextRichContentVO.class, result.getRichContentVOClass());
    }

    @Test
    public void testFromCode_WithInvalidCode_ShouldReturnUnknown() {
        // arrange
        int code = 3;
        // act
        RichContentTypeEnum result = RichContentTypeEnum.fromCode(code);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Should return UNKNOWN enum", RichContentTypeEnum.UNKNOWN, result);
        assertEquals("Code should be 0", 0, result.getCode());
        assertEquals("Description should match", "未知", result.getDesc());
        assertNull("Class type should be null", result.getRichContentVOClass());
    }
}
