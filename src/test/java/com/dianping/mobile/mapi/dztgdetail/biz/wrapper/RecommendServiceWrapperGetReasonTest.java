package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.BuyMoreSaveMoreCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.BuyMoreSaveMoreReq;
import com.sankuai.mktplay.center.mkt.play.center.client.UserInfo;
import com.sankuai.mktplay.center.mkt.play.center.client.UserSourceEnum;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class RecommendServiceWrapperGetReasonTest {

    private RecommendServiceWrapper recommendServiceWrapper = new RecommendServiceWrapper();

    @Mock
    private BuyMoreSaveMoreCtx ctx;

    @Mock
    private BuyMoreSaveMoreReq req;

    @Mock
    private EnvCtx envCtx;

    private String invokePrivateMethod(String methodName, Object... args) throws Exception {
        Method method = RecommendServiceWrapper.class.getDeclaredMethod(methodName, RecommendDTO.class);
        method.setAccessible(true);
        return (String) method.invoke(recommendServiceWrapper, args);
    }

    /**
     * 测试 RecommendDTO 对象为 null 的情况
     */
    @Test
    public void testGetReasonRecommendDtoIsNull() throws Throwable {
        String result = invokePrivateMethod("getReason", (RecommendDTO) null);
        assertEquals("", result);
    }

    /**
     * 测试 RecommendDTO 对象不为 null，但 bizData 为空的情况
     */
    @Test
    public void testGetReasonBizDataIsNull() throws Throwable {
        RecommendDTO recommendDTO = new RecommendDTO();
        String result = invokePrivateMethod("getReason", recommendDTO);
        assertEquals("", result);
    }

    /**
     * 测试 RecommendDTO 对象和 bizData 都不为空，但 reason 字段为空的情况
     */
    @Test
    public void testGetReasonReasonIsNull() throws Throwable {
        RecommendDTO recommendDTO = new RecommendDTO();
        Map<String, Object> bizData = new HashMap<>();
        recommendDTO.setBizData(bizData);
        String result = invokePrivateMethod("getReason", recommendDTO);
        assertEquals("", result);
    }

    /**
     * 测试 RecommendDTO 对象、bizData 和 reason 字段都不为空的情况
     */
    @Test
    public void testGetReasonAllNotNull() throws Throwable {
        RecommendDTO recommendDTO = new RecommendDTO();
        Map<String, Object> bizData = new HashMap<>();
        bizData.put("reason", "test reason");
        recommendDTO.setBizData(bizData);
        String result = invokePrivateMethod("getReason", recommendDTO);
        assertEquals("test reason", result);
    }

    /**
     * 测试正常场景：BuyMoreSaveMoreCtx 对象不为空，且 req 对象不为空，且 req 对象中的 poiidStr 和 dealGroupId 都不为空。
     */
    @Test
    public void testBuildBizParamNormalCase() throws Throwable {
        // arrange
        when(ctx.getReq()).thenReturn(req);
        when(req.getPoiidStr()).thenReturn("12345");
        when(req.getDealGroupId()).thenReturn(67890);
        // act
        Map<String, String> result = recommendServiceWrapper.buildBizParam(ctx);
        // assert
        assertNotNull(result);
        assertEquals("005", result.get("flowFlag"));
        assertEquals("12345", result.get("shopId"));
        assertEquals("67890_1001", result.get("productList"));
        assertEquals("1", result.get("isSameShop"));
    }

    /**
     * 测试异常场景：BuyMoreSaveMoreCtx 对象为空。
     */
    @Test(expected = NullPointerException.class)
    public void testBuildBizParamCtxIsNull() throws Throwable {
        // arrange
        BuyMoreSaveMoreCtx nullCtx = null;
        // act
        recommendServiceWrapper.buildBizParam(nullCtx);
        // assert
        // 预期抛出 NullPointerException
    }

    /**
     * 测试异常场景：BuyMoreSaveMoreCtx 对象不为空，但 req 对象为空。
     */
    @Test(expected = NullPointerException.class)
    public void testBuildBizParamReqIsNull() throws Throwable {
        // arrange
        when(ctx.getReq()).thenReturn(null);
        // act
        recommendServiceWrapper.buildBizParam(ctx);
        // assert
        // 预期抛出 NullPointerException
    }

    /**
     * 测试异常场景：BuyMoreSaveMoreCtx 对象不为空，req 对象也不为空，但 poiidStr 为空。
     */
    @Test
    public void testBuildBizParamPoiidStrIsNull() throws Throwable {
        // arrange
        when(ctx.getReq()).thenReturn(req);
        when(req.getPoiidStr()).thenReturn(null);
        when(req.getDealGroupId()).thenReturn(67890);
        // act
        Map<String, String> result = recommendServiceWrapper.buildBizParam(ctx);
        // assert
        assertNotNull(result);
        assertEquals("005", result.get("flowFlag"));
        assertEquals(null, result.get("shopId"));
        assertEquals("67890_1001", result.get("productList"));
        assertEquals("1", result.get("isSameShop"));
    }

    /**
     * 测试异常场景：BuyMoreSaveMoreCtx 对象不为空，req 对象也不为空，但 dealGroupId 为空。
     */
    @Test
    public void testBuildBizParamDealGroupIdIsNull() throws Throwable {
        // arrange
        when(ctx.getReq()).thenReturn(req);
        when(req.getPoiidStr()).thenReturn("12345");
        when(req.getDealGroupId()).thenReturn(null);
        // act
        Map<String, String> result = recommendServiceWrapper.buildBizParam(ctx);
        // assert
        assertNotNull(result);
        assertEquals("005", result.get("flowFlag"));
        assertEquals("12345", result.get("shopId"));
        assertEquals("null_1001", result.get("productList"));
        assertEquals("1", result.get("isSameShop"));
    }

    /**
     * 测试美团平台用户场景
     */
    @Test
    public void testBuildUserInfoMtUser() throws Throwable {
        // arrange
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.isMt()).thenReturn(true);
        when(envCtx.getMtUserId()).thenReturn(12345L);
        // act
        UserInfo userInfo = recommendServiceWrapper.buildUserInfo(ctx);
        // assert
        assertEquals(12345L, userInfo.getUserId());
        assertEquals(UserSourceEnum.MT.getValue(), userInfo.getUserSource());
    }

    /**
     * 测试点评平台用户场景
     */
    @Test
    public void testBuildUserInfoDpUser() throws Throwable {
        // arrange
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.isMt()).thenReturn(false);
        when(envCtx.getDpUserId()).thenReturn(67890L);
        // act
        UserInfo userInfo = recommendServiceWrapper.buildUserInfo(ctx);
        // assert
        assertEquals(67890L, userInfo.getUserId());
        assertEquals(UserSourceEnum.DP.getValue(), userInfo.getUserSource());
    }

    /**
     * 测试美团平台用户场景，mtUserId 为 0（边界值）
     */
    @Test
    public void testBuildUserInfoMtUserWithZeroUserId() throws Throwable {
        // arrange
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.isMt()).thenReturn(true);
        when(envCtx.getMtUserId()).thenReturn(0L);
        // act
        UserInfo userInfo = recommendServiceWrapper.buildUserInfo(ctx);
        // assert
        assertEquals(0L, userInfo.getUserId());
        assertEquals(UserSourceEnum.MT.getValue(), userInfo.getUserSource());
    }

    /**
     * 测试点评平台用户场景，dpUserId 为 0（边界值）
     */
    @Test
    public void testBuildUserInfoDpUserWithZeroUserId() throws Throwable {
        // arrange
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.isMt()).thenReturn(false);
        when(envCtx.getDpUserId()).thenReturn(0L);
        // act
        UserInfo userInfo = recommendServiceWrapper.buildUserInfo(ctx);
        // assert
        assertEquals(0L, userInfo.getUserId());
        assertEquals(UserSourceEnum.DP.getValue(), userInfo.getUserSource());
    }

    /**
     * 测试美团平台用户场景，mtUserId 为负数（异常值）
     */
    @Test
    public void testBuildUserInfoMtUserWithNegativeUserId() throws Throwable {
        // arrange
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.isMt()).thenReturn(true);
        when(envCtx.getMtUserId()).thenReturn(-12345L);
        // act
        UserInfo userInfo = recommendServiceWrapper.buildUserInfo(ctx);
        // assert
        assertEquals(-12345L, userInfo.getUserId());
        assertEquals(UserSourceEnum.MT.getValue(), userInfo.getUserSource());
    }

    /**
     * 测试点评平台用户场景，dpUserId 为负数（异常值）
     */
    @Test
    public void testBuildUserInfoDpUserWithNegativeUserId() throws Throwable {
        // arrange
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.isMt()).thenReturn(false);
        when(envCtx.getDpUserId()).thenReturn(-67890L);
        // act
        UserInfo userInfo = recommendServiceWrapper.buildUserInfo(ctx);
        // assert
        assertEquals(-67890L, userInfo.getUserId());
        assertEquals(UserSourceEnum.DP.getValue(), userInfo.getUserSource());
    }
}
