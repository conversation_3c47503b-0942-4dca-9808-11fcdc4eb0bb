package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.dztheme.deal.DealProductService;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.concurrent.Future;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.*;

public class DzDealThemeWrapper_PreQueryDealProductTest {

    @InjectMocks
    private DzDealThemeWrapper dzDealThemeWrapper;

    @Mock
    private DealProductService dealProductService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 request 为 null 的情况
     */
    @Test
    public void testPreQueryDealProductRequestIsNull() throws Throwable {
        // arrange
        DealProductRequest request = null;
        // act
        Future result = dzDealThemeWrapper.preQueryDealProduct(request);
        // assert
        assertNull(result);
    }

    /**
     * 测试 request 不为 null，且 dealProductService.query(request) 方法执行正常的情况
     */
    @Test
    @Ignore
    public void testPreQueryDealProductNormal() throws Throwable {
        // arrange
        DealProductRequest request = new DealProductRequest();
        Future future = FutureFactory.getFuture();
        when(dealProductService.query(request)).thenReturn(null);
        // act
        Future result = dzDealThemeWrapper.preQueryDealProduct(request);
        // assert
        assertSame(future, result);
    }

    /**
     * 测试 request 不为 null，但 dealProductService.query(request) 方法执行过程中抛出异常的情况
     */
    @Test
    public void testPreQueryDealProductException() throws Throwable {
        // arrange
        DealProductRequest request = new DealProductRequest();
        when(dealProductService.query(request)).thenThrow(new RuntimeException());
        // act
        Future result = dzDealThemeWrapper.preQueryDealProduct(request);
        // assert
        assertNull(result);
    }
}
