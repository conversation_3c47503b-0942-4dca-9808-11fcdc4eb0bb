package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomtoconsumer.GetUserCodeRequest;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.rpc.DistributionRpcService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;
import java.util.concurrent.Future;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 测试WxNameQueryWrapper的getUserCodeAndNickName方法
 */
@RunWith(MockitoJUnitRunner.class)
public class WxNameQueryWrapperTest {

    @InjectMocks
    private WxNameQueryWrapper wxNameQueryWrapper;

    @Mock
    private DistributionRpcService distributionRpcService;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试getUserCodeAndNickName方法正常情况
     */
    @Test
    public void testGetUserCodeAndNickNameNormal() throws Throwable {
        Future expected = FutureFactory.getFuture();

        // arrange
        GetUserCodeRequest request = new GetUserCodeRequest();
        request.setUnionId("oNQu9t085pxY_QwbYUBrDowMyoVk");
        request.setTime(new Date().getTime());
        request.setLiveId("1000031125");
        request.setUserCode("1898944892713873443");
        request.setDistributionParam("syzb1swbDytgS$$1000011032$$613");
        request.setMtUserId(5134702571L);
        request.setMiniProgramType(2);

        // act
        Future result = wxNameQueryWrapper.getUserCodeAndNickName(request);
        // assert
        verify(distributionRpcService, times(1)).getUserCodeAndNickName(any(GetUserCodeRequest.class));
        assertNull("Future shouldnull", result);

    }

    /**
     * 测试getUserCodeAndNickName方法异常情况
     */
    @Test
    public void testGetUserCodeAndNickNameException() throws Throwable {
        Future expected = FutureFactory.getFuture();

        // arrange
        GetUserCodeRequest request = new GetUserCodeRequest();
        doThrow(new RuntimeException("RPC call failed")).when(distributionRpcService).getUserCodeAndNickName(any(GetUserCodeRequest.class));
        // act
        Future result = wxNameQueryWrapper.getUserCodeAndNickName(request);

        // assert
        verify(distributionRpcService, times(1)).getUserCodeAndNickName(any(GetUserCodeRequest.class));
        assertNull("Future should be null on exception", result);

    }
}
