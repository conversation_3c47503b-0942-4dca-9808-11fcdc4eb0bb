package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.util.ObjectCompareUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealCompareHelper_DifferDpShopIdTest {

    @Mock
    private Cat cat;

    @Mock
    private ObjectCompareUtils objectCompareUtils;

    /**
     * 测试 differDpShopId 方法，当 newShopId 和 oldShopId 相等时，应返回 true
     */
    @Test
    public void testDifferDpShopIdEqual() throws Throwable {
        // arrange
        long newShopId = 123L;
        long oldShopId = 123L;
        int dealId = 1;
        // act
        boolean result = DealCompareHelper.differDpShopId(newShopId, oldShopId, dealId);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 differDpShopId 方法，当 newShopId 和 oldShopId 不相等时，应返回 false
     */
    @Test
    public void testDifferDpShopIdNotEqual() throws Throwable {
        // arrange
        long newShopId = 123L;
        long oldShopId = 456L;
        int dealId = 1;
        // act
        boolean result = DealCompareHelper.differDpShopId(newShopId, oldShopId, dealId);
        // assert
        assertFalse(result);
    }
}
