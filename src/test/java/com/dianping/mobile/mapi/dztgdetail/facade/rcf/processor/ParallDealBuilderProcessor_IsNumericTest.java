package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.BasicInfoBuilderService;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.BuyBarBuilderService;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.DealContentBuilderService;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.GuaranteeBuilderService;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.HeadPicAtmosphereBuilderService;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.ReminderInfoBuilderService;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.SaleBuilderService;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.ShopBuilderService;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class ParallDealBuilderProcessor_IsNumericTest {

    @Mock
    private BasicInfoBuilderService basicInfoBuilderService;

    @Mock
    private DealContentBuilderService dealContentBuilderService;

    @Mock
    private ShopBuilderService shopBuilderService;

    @Mock
    private GuaranteeBuilderService guaranteeBuilderService;

    @Mock
    private BuyBarBuilderService buyBarBuilderService;

    @Mock
    private SaleBuilderService saleBuilderService;

    @Mock
    private HeadPicAtmosphereBuilderService headPicAtmosphereBuilderService;

    private DealCtx ctx;

    private DealGroupPBO result;

    @InjectMocks
    private ParallDealBuilderProcessor parallDealBuilderProcessor;

    /**
     * 测试输入为数字的情况
     */
    @Test
    public void testIsNumeric_Number() {
        assertTrue(ReminderInfoBuilderService.isNumeric("123"));
        assertTrue(ReminderInfoBuilderService.isNumeric("-123"));
        assertTrue(ReminderInfoBuilderService.isNumeric("123.45"));
        assertTrue(ReminderInfoBuilderService.isNumeric("-123.45"));
    }

    /**
     * 测试输入为非数字的情况
     */
    @Test
    public void testIsNumeric_NonNumber() {
        assertFalse(ReminderInfoBuilderService.isNumeric("abc"));
        assertFalse(ReminderInfoBuilderService.isNumeric("123abc"));
        assertFalse(ReminderInfoBuilderService.isNumeric("abc123"));
        assertFalse(ReminderInfoBuilderService.isNumeric("abc"));
    }

    /**
     * 测试输入为null的情况
     */
    @Test
    public void testIsNumeric_Null() {
        assertFalse(ReminderInfoBuilderService.isNumeric(null));
    }

    /**
     * 测试输入为空字符串的情况
     */
    @Test
    public void testIsNumeric_EmptyString() {
        assertFalse(ReminderInfoBuilderService.isNumeric(""));
    }

    @Test
    public void testDisableDayType() {
        String json= "{\"rule\":{\"buyRule\":{\"maxPerUser\":1,\"minPerUser\":1},\"refundRule\":{\"supportOverdueAutoRefund\":true,\"supportRefundType\":1},\"useRule\":{\"disableDate\":{\"disableDays\":[102]},\"receiptEffectiveDate\":{\"receiptBeginDate\":\"2024-12-13 11:41:08\",\"receiptDateType\":1,\"receiptEndDate\":\"2025-03-13 23:59:59\",\"receiptValidDays\":90,\"showText\":\"购买后90天内有效\"}}},}";
        DealGroupDTO dealGroupDTO = JSON.parseObject(json, DealGroupDTO.class);
        boolean result = DealAttrHelper.hitDisableDateType(dealGroupDTO, 102);
        Assert.assertTrue(result);
    }

    @Before
    public void setUp() {
        ctx = new DealCtx(new EnvCtx());
        result = new DealGroupPBO();
        ctx.setResult(result);
    }

    @Test
    public void testProcessTortProduct() throws Throwable {
        // arrange
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO tortAttr = new AttributeDTO();
        tortAttr.setName("tort");
        tortAttr.setValue(Collections.singletonList("1"));
        attrs.add(tortAttr);
        ctx.setAttrs(attrs);
        ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
        ctx.setModuleConfigsModule(moduleConfigsModule);
        // act
        parallDealBuilderProcessor.process(ctx);
        // assert
        assertTrue(result.getModuleConfigsModule().isTort());
        assertEquals("当前团购已失效，请选择其他团购", result.getModuleConfigsModule().getTortText());
    }
}
