package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.model.ExaminerSuitableCrowd;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.BaseDisplayItemVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.highlights.DztgHighlightsModule;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DefaultExaminerHandlerBuildHighlightsModuleTest {

    @InjectMocks
    private DefaultExaminerHandler defaultExaminerHandler;

    @Mock
    private ExaminerSuitableCrowd suitableCrowd;

    /**
     * 测试buildHighlightsModule方法，当displayItemList和serviceHighlightList都为空时，应返回一个简单的DztgHighlightsModule对象
     */
    @Test
    public void testBuildHighlightsModuleWhenDisplayItemListAndServiceHighlightListAreEmpty() throws Throwable {
        // arrange
        List<BaseDisplayItemVO> displayItemList = new ArrayList<>();
        // Use reflection to invoke the private method
        Method method = DefaultExaminerHandler.class.getDeclaredMethod("buildHighlightsModule", List.class, ExaminerSuitableCrowd.class);
        method.setAccessible(true);
        // act
        DztgHighlightsModule result = (DztgHighlightsModule) method.invoke(defaultExaminerHandler, displayItemList, suitableCrowd);
        // assert
        assertNotNull(result);
        assertEquals("struct", result.getStyle());
        assertTrue(result.getAttrs().isEmpty());
    }

    /**
     * 测试buildHighlightsModule方法，当displayItemList和serviceHighlightList都不为空时，应返回一个包含所有信息的DztgHighlightsModule对象
     */
    @Test
    public void testBuildHighlightsModuleWhenDisplayItemListAndServiceHighlightListAreNotEmpty() throws Throwable {
        // arrange
        List<BaseDisplayItemVO> displayItemList = new ArrayList<>();
        displayItemList.add(new BaseDisplayItemVO());
        // Use reflection to invoke the private method
        Method method = DefaultExaminerHandler.class.getDeclaredMethod("buildHighlightsModule", List.class, ExaminerSuitableCrowd.class);
        method.setAccessible(true);
        // act
        DztgHighlightsModule result = (DztgHighlightsModule) method.invoke(defaultExaminerHandler, displayItemList, suitableCrowd);
        // assert
        assertNotNull(result);
        assertEquals("struct", result.getStyle());
        assertFalse(result.getAttrs().isEmpty());
    }
}
