package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.NavBarSearchModuleVO;
import com.dianping.vc.service.dto.Response;
import com.sankuai.dzviewscene.sug.merger.service.response.SugItemDTO;
import com.sankuai.dzviewscene.sug.merger.service.response.SugMergerResult;
import io.vavr.collection.List;
import java.util.Collections;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ NavBarSearchWrapper.class })
public class NavBarSearchWrapperQueryNaviSearchTest {

    private NavBarSearchWrapper navBarSearchWrapper;

    @Mock
    private DealCtx ctx;

    @Mock
    private FutureCtx futureCtx;

    @Before
    public void setUp() {
        navBarSearchWrapper = PowerMockito.spy(new NavBarSearchWrapper());
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
    }

    /**
     * Test when future is null should return null
     */
    @Test
    public void testQueryNaviSearchWhenFutureIsNull() throws Throwable {
        // arrange
        when(futureCtx.getNavBarSearchFuture()).thenReturn(null);
        // act
        NavBarSearchModuleVO result = navBarSearchWrapper.queryNaviSearch(ctx);
        // assert
        assertNull(result);
        verify(navBarSearchWrapper, never()).getFutureResult(any(Future.class));
    }

    /**
     * Test when getFutureResult returns null should return null
     */
    @Test
    @SuppressWarnings("unchecked")
    public void testQueryNaviSearchWhenFutureResultIsNull() throws Throwable {
        // arrange
        Future<?> future = mock(Future.class);
        when(futureCtx.getNavBarSearchFuture()).thenReturn((Future) future);
        PowerMockito.doReturn(null).when(navBarSearchWrapper).getFutureResult((Future) any());
        // act
        NavBarSearchModuleVO result = navBarSearchWrapper.queryNaviSearch(ctx);
        // assert
        assertNull(result);
    }

    /**
     * Test when response is not success should return null
     */
    @Test
    @SuppressWarnings("unchecked")
    public void testQueryNaviSearchWhenResponseNotSuccess() throws Throwable {
        // arrange
        Future<?> future = mock(Future.class);
        Response<SugMergerResult> response = mock(Response.class);
        when(futureCtx.getNavBarSearchFuture()).thenReturn((Future) future);
        PowerMockito.doReturn(response).when(navBarSearchWrapper).getFutureResult((Future) any());
        when(response.isSuccess()).thenReturn(false);
        // act
        NavBarSearchModuleVO result = navBarSearchWrapper.queryNaviSearch(ctx);
        // assert
        assertNull(result);
        verify(response).isSuccess();
        verify(response, never()).getData();
    }

    /**
     * Test when response data is null should return empty NavBarSearchModuleVO
     */
    @Test
    @SuppressWarnings("unchecked")
    public void testQueryNaviSearchWhenResponseDataIsNull() throws Throwable {
        // arrange
        Future<?> future = mock(Future.class);
        Response<SugMergerResult> response = mock(Response.class);
        when(futureCtx.getNavBarSearchFuture()).thenReturn((Future) future);
        PowerMockito.doReturn(response).when(navBarSearchWrapper).getFutureResult((Future) any());
        when(response.isSuccess()).thenReturn(true);
        when(response.getData()).thenReturn(null);
        // act
        NavBarSearchModuleVO result = navBarSearchWrapper.queryNaviSearch(ctx);
        // assert
        assertNotNull(result);
        assertNull(result.getText());
        assertNull(result.getJumpUrl());
    }

    /**
     * Test when sugItemResult is empty should return empty NavBarSearchModuleVO
     */
    @Test
    @SuppressWarnings("unchecked")
    public void testQueryNaviSearchWhenSugItemResultIsEmpty() throws Throwable {
        // arrange
        Future<?> future = mock(Future.class);
        Response<SugMergerResult> response = mock(Response.class);
        SugMergerResult data = mock(SugMergerResult.class);
        when(futureCtx.getNavBarSearchFuture()).thenReturn((Future) future);
        PowerMockito.doReturn(response).when(navBarSearchWrapper).getFutureResult((Future) any());
        when(response.isSuccess()).thenReturn(true);
        when(response.getData()).thenReturn(data);
        when(data.getSugItemResult()).thenReturn(Collections.emptyList());
        // act
        NavBarSearchModuleVO result = navBarSearchWrapper.queryNaviSearch(ctx);
        // assert
        assertNotNull(result);
        assertNull(result.getText());
        assertNull(result.getJumpUrl());
    }

    /**
     * Test when sugItemResult has items should return populated NavBarSearchModuleVO
     */
    @Test
    @SuppressWarnings("unchecked")
    public void testQueryNaviSearchWhenSugItemResultHasItems() throws Throwable {
        // arrange
        Future<?> future = mock(Future.class);
        Response<SugMergerResult> response = mock(Response.class);
        SugMergerResult data = mock(SugMergerResult.class);
        SugItemDTO item = new SugItemDTO();
        item.setLabel("test label");
        item.setRedirectLink("test link");
        when(futureCtx.getNavBarSearchFuture()).thenReturn((Future) future);
        PowerMockito.doReturn(response).when(navBarSearchWrapper).getFutureResult((Future) any());
        when(response.isSuccess()).thenReturn(true);
        when(response.getData()).thenReturn(data);
        when(data.getSugItemResult()).thenReturn(Collections.singletonList(item));
        // act
        NavBarSearchModuleVO result = navBarSearchWrapper.queryNaviSearch(ctx);
        // assert
        assertNotNull(result);
        assertEquals("test label", result.getText());
        assertEquals("test link", result.getJumpUrl());
    }
}
