package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.lion.client.ConfigEvent;
import com.dianping.lion.facade.LionFacade;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.utils.FaultToleranceUtils;
import com.sankuai.douhu.absdk.bean.DouHuRequest;
import com.sankuai.douhu.absdk.bean.DouHuResponse;
import com.sankuai.douhu.absdk.client.DouHuClient;
import com.sankuai.douhu.absdk.enums.ErrorCode;
import com.sankuai.douhu.absdk.util.DouHuUtil;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.slf4j.Logger;

@RunWith(MockitoJUnitRunner.class)
public class DouHuBizTest {

    @InjectMocks
    private DouHuBiz douHuBiz;

    @Mock
    private DouHuClient douHuClient;

    private final String unionId = "testUnionId";

    private final String module = "testModule";

    private final boolean isMt = true;

    @Mock
    private Logger log;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Helper method to set private douHuClient field
     */
    private void setDouHuClient(DouHuBiz douHuBiz, DouHuClient client) throws Exception {
        Field field = DouHuBiz.class.getDeclaredField("douHuClient");
        field.setAccessible(true);
        field.set(douHuBiz, client);
    }

    @Test
    public void testGetAbExpResultExpIdIsNull() throws Throwable {
        EnvCtx context = new EnvCtx();
        String module = "module";
        ModuleAbConfig result = douHuBiz.getAbExpResult(context, module);
        Assert.assertNull(result);
    }

    @Test
    public void testGetAbExpResultContextIsNull() throws Throwable {
        String module = "module";
        ModuleAbConfig result = douHuBiz.getAbExpResult((EnvCtx) null, module);
        Assert.assertNull(result);
    }

    @Test
    public void testGetAbExpResultTryAbReturnsNull() throws Throwable {
        String module = "module";
        DouHuResponse nullResponse = new DouHuResponse();
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(nullResponse);
        ModuleAbConfig result = douHuBiz.getAbExpResult(new EnvCtx(), module);
        Assert.assertNull(result);
    }

    @Test
    public void testGetAbExpResultSkIsNull() throws Throwable {
        String module = "module";
        DouHuResponse response = new DouHuResponse();
        response.setSk(null);
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        ModuleAbConfig result = douHuBiz.getAbExpResult(new EnvCtx(), module);
        Assert.assertNull(result);
    }

    @Test
    public void testGetAbExpResultCodeIsNotSuccess() throws Throwable {
        String module = "module";
        DouHuResponse response = new DouHuResponse();
        response.setSk("sk");
        response.setCode("-400");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        ModuleAbConfig result = douHuBiz.getAbExpResult(new EnvCtx(), module);
        Assert.assertNull(result);
    }

    @Test
    public void testGetAbExpResultNormalCase() throws Throwable {
        String module = "module";
        DouHuResponse response = new DouHuResponse();
        response.setSk("sk");
        response.setCode("200");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        ModuleAbConfig result = douHuBiz.getAbExpResult(new EnvCtx(), module);
        Assert.assertNotNull(result);
    }

    /**
     * 测试 expId 为 null 的情况
     */
    @Test
    public void testInvokeTryAbExpIdIsNull() throws Throwable {
        // arrange
        String unionId = "testUnionId";
        boolean isMt = true;
        String expId = null;
        // act
        DouHuResponse result = douHuBiz.invokeTryAb(unionId, isMt, expId);
        // assert
        assertNull(result);
    }

    /**
     * 测试 expId 不为 null，douHuClient.tryAb(request) 返回正常 DouHuResponse 对象的情况
     */
    @Test
    public void testInvokeTryAbNormal() throws Throwable {
        // arrange
        String unionId = "testUnionId";
        boolean isMt = true;
        String expId = "testExpId";
        DouHuResponse expectedResponse = new DouHuResponse();
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(expectedResponse);
        // act
        DouHuResponse result = douHuBiz.invokeTryAb(unionId, isMt, expId);
        // assert
        assertEquals(expectedResponse, result);
    }

    /**
     * 测试 expId 不为 null，douHuClient.tryAb(request) 抛出异常的情况
     */
    @Test(expected = Exception.class)
    public void testInvokeTryAbException() throws Throwable {
        // arrange
        String unionId = "testUnionId";
        boolean isMt = true;
        String expId = "testExpId";
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenThrow(new Exception());
        // act
        douHuBiz.invokeTryAb(unionId, isMt, expId);
        // assert
        // Exception is expected
    }

    @Test
    public void testGetAbByUnionIdDouHuResponseIsNull() throws Throwable {
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(null);
        ModuleAbConfig result = douHuBiz.getAbByUnionId(unionId, module, isMt);
        assertNull(result);
    }

    @Test
    public void testGetAbByUnionIdSkIsNull() throws Throwable {
        DouHuResponse response = new DouHuResponse();
        response.setCode(ErrorCode.SUCCESS.getCode());
        response.setSk(null);
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        ModuleAbConfig result = douHuBiz.getAbByUnionId(unionId, module, isMt);
        assertNull(result);
    }

    @Test
    public void testGetAbByUnionIdCodeIsNotSuccess() throws Throwable {
        DouHuResponse response = new DouHuResponse();
        response.setCode(ErrorCode.FAIL.getCode());
        response.setSk("testSk");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        ModuleAbConfig result = douHuBiz.getAbByUnionId(unionId, module, isMt);
        assertNull(result);
    }

    @Test
    public void testGetAbByUnionIdNormal() throws Throwable {
        DouHuResponse response = new DouHuResponse();
        response.setCode(ErrorCode.SUCCESS.getCode());
        response.setSk("testSk");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        ModuleAbConfig result = douHuBiz.getAbByUnionId(unionId, module, isMt);
        assertNotNull(result);
    }

    /**
     * Test when module is empty, should return null
     */
    @Test
    public void testGetAbExpResultModuleEmpty() throws Throwable {
        // arrange
        String module = "";
        EnvCtx context = new EnvCtx();
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResult((EnvCtx) context, module);
        // assert
        assertNull(result);
    }

    /**
     * Test when context is null, should return null
     */
    @Test
    public void testGetAbExpResultContextNull() throws Throwable {
        // arrange
        String module = "testModule";
        EnvCtx nullContext = null;
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResult(nullContext, module);
        // assert
        assertNull(result);
    }

    /**
     * Test when douHuClient returns null response, should return null
     */
    @Test
    public void testGetAbExpResultResponseNull() throws Throwable {
        // arrange
        String module = "testModule";
        EnvCtx context = new EnvCtx();
        context.setUnionId("testUnionId");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(null);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResult((EnvCtx) context, module);
        // assert
        assertNull(result);
    }

    /**
     * Test when response has blank sk, should return null
     */
    @Test
    public void testGetAbExpResultSkIsBlank() throws Throwable {
        // arrange
        String module = "testModule";
        EnvCtx context = new EnvCtx();
        context.setUnionId("testUnionId");
        DouHuResponse response = new DouHuResponse();
        response.setSk("");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResult((EnvCtx) context, module);
        // assert
        assertNull(result);
    }

    /**
     * Test when response code is not SUCCESS, should return null
     */
    @Test
    public void testGetAbExpResultCodeNotSuccess() throws Throwable {
        // arrange
        String module = "testModule";
        EnvCtx context = new EnvCtx();
        context.setUnionId("testUnionId");
        DouHuResponse response = new DouHuResponse();
        response.setSk("test_sk");
        response.setCode(ErrorCode.FAIL.getCode());
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResult((EnvCtx) context, module);
        // assert
        assertNull(result);
    }

    /**
     * Test when response sk contains _b suffix, should return result with 'b'
     */
    @Test
    public void testGetAbExpResultSkContainsB() throws Throwable {
        // arrange
        String module = "testModule";
        EnvCtx context = new EnvCtx();
        context.setUnionId("testUnionId");
        DouHuResponse response = new DouHuResponse();
        response.setSk("test_b");
        response.setCode(ErrorCode.SUCCESS.getCode());
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResult((EnvCtx) context, module);
        // assert
        assertNotNull(result);
        assertEquals("b", result.getConfigs().get(0).getExpResult());
    }

    /**
     * Test when response sk contains _c suffix, should return result with 'c'
     */
    @Test
    public void testGetAbExpResultSkContainsC() throws Throwable {
        // arrange
        String module = "testModule";
        EnvCtx context = new EnvCtx();
        context.setUnionId("testUnionId");
        DouHuResponse response = new DouHuResponse();
        response.setSk("test_c");
        response.setCode(ErrorCode.SUCCESS.getCode());
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResult((EnvCtx) context, module);
        // assert
        assertNotNull(result);
        assertEquals("c", result.getConfigs().get(0).getExpResult());
    }

    /**
     * Test when response sk doesn't match any pattern, should return default 'a'
     */
    @Test
    public void testGetAbExpResultSkDefault() throws Throwable {
        // arrange
        String module = "testModule";
        EnvCtx context = new EnvCtx();
        context.setUnionId("testUnionId");
        DouHuResponse response = new DouHuResponse();
        response.setSk("test_other");
        response.setCode(ErrorCode.SUCCESS.getCode());
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResult((EnvCtx) context, module);
        // assert
        assertNotNull(result);
        assertEquals("a", result.getConfigs().get(0).getExpResult());
    }

    /**
     * Test when douHuClient throws exception, should return null and log error
     */
    @Test
    public void testGetAbExpResultClientThrowsException() throws Throwable {
        // arrange
        String module = "testModule";
        EnvCtx context = new EnvCtx();
        context.setUnionId("testUnionId");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenThrow(new RuntimeException("test exception"));
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResult((EnvCtx) context, module);
        // assert
        assertNull(result);
    }

    /**
     * Test MT platform request with uuid
     */
    @Test
    public void testGetAbExpResultMtPlatform() throws Throwable {
        // arrange
        String module = "testModule";
        EnvCtx context = new EnvCtx();
        context.setUnionId("testUnionId");
        context.setUuid("testUuid");
        context.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DouHuResponse response = new DouHuResponse();
        response.setSk("test_b");
        response.setCode(ErrorCode.SUCCESS.getCode());
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResult((EnvCtx) context, module);
        // assert
        assertNotNull(result);
        assertEquals("b", result.getConfigs().get(0).getExpResult());
    }

    /**
     * Test DP platform request with dpId
     */
    @Test
    public void testGetAbExpResultDpPlatform() throws Throwable {
        // arrange
        String module = "testModule";
        EnvCtx context = new EnvCtx();
        context.setUnionId("testUnionId");
        context.setDpId("testDpId");
        context.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        DouHuResponse response = new DouHuResponse();
        response.setSk("test_c");
        response.setCode(ErrorCode.SUCCESS.getCode());
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResult((EnvCtx) context, module);
        // assert
        assertNotNull(result);
        assertEquals("c", result.getConfigs().get(0).getExpResult());
    }

    /**
     * 测试输入参数为null的情况
     */
    @Test
    public void testGetExpResultWithNullInput() throws Throwable {
        // arrange
        DouHuBiz douHuBiz = new DouHuBiz();
        // act
        String result = douHuBiz.getExpResult(null);
        // assert
        assertNull(result);
    }

    /**
     * 测试configs为空列表的情况
     */
    @Test
    public void testGetExpResultWithEmptyConfigs() throws Throwable {
        // arrange
        DouHuBiz douHuBiz = new DouHuBiz();
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setConfigs(Collections.emptyList());
        // act
        String result = douHuBiz.getExpResult(moduleAbConfig);
        // assert
        assertNull(result);
    }

    /**
     * 测试正常获取第一个config的expResult
     */
    @Test
    public void testGetExpResultWithValidConfig() throws Throwable {
        // arrange
        DouHuBiz douHuBiz = new DouHuBiz();
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("test_result");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        // act
        String result = douHuBiz.getExpResult(moduleAbConfig);
        // assert
        assertEquals("test_result", result);
    }

    /**
     * 测试多个config时获取第一个的expResult
     */
    @Test
    public void testGetExpResultWithMultipleConfigs() throws Throwable {
        // arrange
        DouHuBiz douHuBiz = new DouHuBiz();
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig1 = new AbConfig();
        abConfig1.setExpResult("first_result");
        AbConfig abConfig2 = new AbConfig();
        abConfig2.setExpResult("second_result");
        moduleAbConfig.setConfigs(Arrays.asList(abConfig1, abConfig2));
        // act
        String result = douHuBiz.getExpResult(moduleAbConfig);
        // assert
        assertEquals("first_result", result);
    }

    /**
     * 测试expResult为null的情况
     */
    @Test
    public void testGetExpResultWithNullExpResult() throws Throwable {
        // arrange
        DouHuBiz douHuBiz = new DouHuBiz();
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult(null);
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        // act
        String result = douHuBiz.getExpResult(moduleAbConfig);
        // assert
        assertNull(result);
    }

    /**
     * Test when module is null, should return null
     */
    @Test
    public void testGetAbExpResultV2_ModuleNull() throws Throwable {
        // arrange
        String unionId = "testUnionId";
        String module = null;
        boolean isMT = false;
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResultV2(unionId, module, isMT);
        // assert
        assertNull(result);
    }

    /**
     * Test when module has no mapping in Lion config, should use module as expId
     */
    @Test
    public void testGetAbExpResultV2_ModuleNoMapping() throws Throwable {
        // arrange
        String unionId = "testUnionId";
        String module = "testModule";
        boolean isMT = false;
        DouHuResponse mockResponse = new DouHuResponse();
        mockResponse.setCode(ErrorCode.SUCCESS.getCode());
        mockResponse.setSk("testSk");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(mockResponse);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResultV2(unionId, module, isMT);
        // assert
        assertNotNull(result);
        assertEquals(module, result.getKey());
        assertEquals(1, result.getConfigs().size());
        assertEquals(module, result.getConfigs().get(0).getExpId());
    }

    /**
     * Test when douHuClient returns null response
     */
    @Test
    public void testGetAbExpResultV2_NullResponse() throws Throwable {
        // arrange
        String unionId = "testUnionId";
        String module = "testModule";
        boolean isMT = false;
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(null);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResultV2(unionId, module, isMT);
        // assert
        assertNull(result);
    }

    /**
     * Test when response has blank sk
     */
    @Test
    public void testGetAbExpResultV2_BlankSk() throws Throwable {
        // arrange
        String unionId = "testUnionId";
        String module = "testModule";
        boolean isMT = false;
        DouHuResponse mockResponse = new DouHuResponse();
        mockResponse.setCode(ErrorCode.SUCCESS.getCode());
        mockResponse.setSk("");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(mockResponse);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResultV2(unionId, module, isMT);
        // assert
        assertNull(result);
    }

    /**
     * Test when response has non-success code
     */
    @Test
    public void testGetAbExpResultV2_NonSuccessCode() throws Throwable {
        // arrange
        String unionId = "testUnionId";
        String module = "testModule";
        boolean isMT = false;
        DouHuResponse mockResponse = new DouHuResponse();
        mockResponse.setCode(ErrorCode.FAIL.getCode());
        mockResponse.setSk("testSk");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(mockResponse);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResultV2(unionId, module, isMT);
        // assert
        assertNull(result);
    }

    /**
     * Test successful response with valid sk
     */
    @Test
    public void testGetAbExpResultV2_Success() throws Throwable {
        // arrange
        String unionId = "testUnionId";
        String module = "testModule";
        boolean isMT = false;
        DouHuResponse mockResponse = new DouHuResponse();
        mockResponse.setCode(ErrorCode.SUCCESS.getCode());
        mockResponse.setSk("testSk");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(mockResponse);
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResultV2(unionId, module, isMT);
        // assert
        assertNotNull(result);
        assertEquals(module, result.getKey());
        assertEquals(1, result.getConfigs().size());
        // Additional assertions can be added here based on the expected behavior
    }

    /**
     * Test when exception occurs during tryAb()
     */
    @Test
    public void testGetAbExpResultV2_Exception() throws Throwable {
        // arrange
        String unionId = "testUnionId";
        String module = "testModule";
        boolean isMT = false;
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenThrow(new RuntimeException("test exception"));
        // act
        ModuleAbConfig result = douHuBiz.getAbExpResultV2(unionId, module, isMT);
        // assert
        assertNull(result);
    }

    /**
     * Test when module is null and getExpId returns null.
     */
    @Test
    public void testGetAbExpResultByUserId_ModuleNull_ExpIdNull() throws Throwable {
        // Arrange
        DouHuBiz douHuBiz = new DouHuBiz();
        DealCtx context = new DealCtx(null);
        String module = null;
        // Act
        ModuleAbConfig result = douHuBiz.getAbExpResultByUserId(context, module);
        // Assert
        assertNull("Expected result to be null when module is null and expId is null", result);
    }

    /**
     * Test when module is empty and getExpId returns null.
     */
    @Test
    public void testGetAbExpResultByUserId_ModuleEmpty_ExpIdNull() throws Throwable {
        // Arrange
        DouHuBiz douHuBiz = new DouHuBiz();
        DealCtx context = new DealCtx(null);
        String module = "";
        // Act
        ModuleAbConfig result = douHuBiz.getAbExpResultByUserId(context, module);
        // Assert
        assertNull("Expected result to be null when module is empty and expId is null", result);
    }

    /**
     * Test when module is valid but DouHuResponse is null.
     */
    @Test
    public void testGetAbExpResultByUserId_ValidModule_NullResponse() throws Throwable {
        // Arrange
        DouHuBiz douHuBiz = new DouHuBiz();
        DealCtx context = new DealCtx(null);
        String module = "validModule";
        DouHuClient mockDouHuClient = mock(DouHuClient.class);
        setDouHuClient(douHuBiz, mockDouHuClient);
        when(mockDouHuClient.tryAb(any())).thenReturn(null);
        // Act
        ModuleAbConfig result = douHuBiz.getAbExpResultByUserId(context, module);
        // Assert
        assertNull("Expected result to be null when DouHuResponse is null", result);
    }

    /**
     * Test when module is valid and DouHuResponse has error code.
     */
    @Test
    public void testGetAbExpResultByUserId_ValidModule_ErrorResponse() throws Throwable {
        // Arrange
        DouHuBiz douHuBiz = new DouHuBiz();
        DealCtx context = new DealCtx(null);
        String module = "validModule";
        DouHuClient mockDouHuClient = mock(DouHuClient.class);
        DouHuResponse mockResponse = new DouHuResponse();
        mockResponse.setCode(ErrorCode.FAIL.getCode());
        setDouHuClient(douHuBiz, mockDouHuClient);
        when(mockDouHuClient.tryAb(any())).thenReturn(mockResponse);
        // Act
        ModuleAbConfig result = douHuBiz.getAbExpResultByUserId(context, module);
        // Assert
        assertNull("Expected result to be null when DouHuResponse has error code", result);
    }

    /**
     * Test when module is valid and DouHuResponse is successful.
     */
    @Test
    public void testGetAbExpResultByUserId_ValidModule_SuccessResponse() throws Throwable {
        // Arrange
        DouHuBiz douHuBiz = new DouHuBiz();
        DealCtx context = new DealCtx(null);
        String module = "validModule";
        DouHuClient mockDouHuClient = mock(DouHuClient.class);
        DouHuResponse mockResponse = new DouHuResponse();
        mockResponse.setCode(ErrorCode.SUCCESS.getCode());
        mockResponse.setSk("skValue");
        setDouHuClient(douHuBiz, mockDouHuClient);
        when(mockDouHuClient.tryAb(any())).thenReturn(mockResponse);
        // Act
        ModuleAbConfig result = douHuBiz.getAbExpResultByUserId(context, module);
        // Assert
        assertNotNull("Expected non-null result when DouHuResponse is successful", result);
        assertEquals("Expected module key to match", module, result.getKey());
        assertNotNull("Expected configs to be non-null", result.getConfigs());
        assertFalse("Expected configs to be non-empty", result.getConfigs().isEmpty());
    }

    /**
     * Test when module is valid and DouHuResponse is successful but SK is blank.
     */
    @Test
    public void testGetAbExpResultByUserId_ValidModule_SuccessResponse_BlankSk() throws Throwable {
        // Arrange
        DouHuBiz douHuBiz = new DouHuBiz();
        DealCtx context = new DealCtx(null);
        String module = "validModule";
        DouHuClient mockDouHuClient = mock(DouHuClient.class);
        DouHuResponse mockResponse = new DouHuResponse();
        mockResponse.setCode(ErrorCode.SUCCESS.getCode());
        mockResponse.setSk("");
        setDouHuClient(douHuBiz, mockDouHuClient);
        when(mockDouHuClient.tryAb(any())).thenReturn(mockResponse);
        // Act
        ModuleAbConfig result = douHuBiz.getAbExpResultByUserId(context, module);
        // Assert
        assertNull("Expected result to be null when SK is blank", result);
    }

    /**
     * Test when DouHuClient throws exception
     */
    @Test
    public void testGetAbExpResultByUserId_ThrowsException() throws Throwable {
        // Arrange
        DouHuBiz douHuBiz = new DouHuBiz();
        DealCtx context = new DealCtx(null);
        String module = "validModule";
        DouHuClient mockDouHuClient = mock(DouHuClient.class);
        setDouHuClient(douHuBiz, mockDouHuClient);
        when(mockDouHuClient.tryAb(any())).thenThrow(new RuntimeException("Test exception"));
        // Act
        ModuleAbConfig result = douHuBiz.getAbExpResultByUserId(context, module);
        // Assert
        assertNull("Expected result to be null when exception is thrown", result);
    }

    /**
     * 测试调用过程中抛出异常的情况
     */
    @Test
    public void testGetAbByUnionIdAndExpId_WhenExceptionThrown() throws Throwable {
        // arrange
        String unionId = "testUnionId";
        String expId = "testExpId";
        String module = "testModule";
        boolean isMt = true;
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenThrow(new RuntimeException("test exception"));
        // act
        ModuleAbConfig result = douHuBiz.getAbByUnionIdAndExpId(unionId, expId, module, isMt);
        // assert
        assertNull(result);
        // Removed the verification of log error as per the requirement
    }

    /**
     * 测试expId为null时返回null
     */
    @Test
    public void testGetAbByUnionIdAndExpId_WhenExpIdIsNull() throws Throwable {
        // arrange
        String unionId = "testUnionId";
        String expId = null;
        String module = "testModule";
        boolean isMt = true;
        // act
        ModuleAbConfig result = douHuBiz.getAbByUnionIdAndExpId(unionId, expId, module, isMt);
        // assert
        assertNull(result);
    }

    /**
     * 测试douHuClient返回null时返回null
     */
    @Test
    public void testGetAbByUnionIdAndExpId_WhenClientReturnsNull() throws Throwable {
        // arrange
        String unionId = "testUnionId";
        String expId = "testExpId";
        String module = "testModule";
        boolean isMt = true;
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(null);
        // act
        ModuleAbConfig result = douHuBiz.getAbByUnionIdAndExpId(unionId, expId, module, isMt);
        // assert
        assertNull(result);
    }

    /**
     * 测试响应中sk为空时返回null
     */
    @Test
    public void testGetAbByUnionIdAndExpId_WhenResponseSkIsBlank() throws Throwable {
        // arrange
        String unionId = "testUnionId";
        String expId = "testExpId";
        String module = "testModule";
        boolean isMt = true;
        DouHuResponse response = new DouHuResponse();
        response.setCode(ErrorCode.SUCCESS.getCode());
        response.setSk(null);
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbByUnionIdAndExpId(unionId, expId, module, isMt);
        // assert
        assertNull(result);
    }

    /**
     * 测试响应code不为SUCCESS时返回null
     */
    @Test
    public void testGetAbByUnionIdAndExpId_WhenResponseCodeNotSuccess() throws Throwable {
        // arrange
        String unionId = "testUnionId";
        String expId = "testExpId";
        String module = "testModule";
        boolean isMt = true;
        DouHuResponse response = new DouHuResponse();
        response.setCode(ErrorCode.FAIL.getCode());
        response.setSk("test_sk");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbByUnionIdAndExpId(unionId, expId, module, isMt);
        // assert
        assertNull(result);
    }

    /**
     * 测试正常返回有效响应的情况
     */
    @Test
    public void testGetAbByUnionIdAndExpId_WhenResponseIsValid() throws Throwable {
        // arrange
        String unionId = "testUnionId";
        String expId = "testExpId";
        String module = "testModule";
        boolean isMt = true;
        DouHuResponse response = new DouHuResponse();
        response.setCode(ErrorCode.SUCCESS.getCode());
        response.setSk("test_sk_b");
        response.setAbQueryId("testQueryId");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbByUnionIdAndExpId(unionId, expId, module, isMt);
        // assert
        assertNotNull(result);
        assertEquals(module, result.getKey());
        assertEquals(1, result.getConfigs().size());
        AbConfig abConfig = result.getConfigs().get(0);
        assertEquals(expId, abConfig.getExpId());
        assertEquals("b", abConfig.getExpResult());
        assertNotNull(abConfig.getExpBiInfo());
    }

    /**
     * 测试不同expResult的情况
     */
    @Test
    public void testGetAbByUnionIdAndExpId_WithDifferentExpResults() throws Throwable {
        // arrange
        String unionId = "testUnionId";
        String expId = "testExpId";
        String module = "testModule";
        boolean isMt = true;
        // 测试_b后缀
        DouHuResponse responseB = new DouHuResponse();
        responseB.setCode(ErrorCode.SUCCESS.getCode());
        responseB.setSk("test_sk_b");
        responseB.setAbQueryId("testQueryId");
        // 测试_c后缀
        DouHuResponse responseC = new DouHuResponse();
        responseC.setCode(ErrorCode.SUCCESS.getCode());
        responseC.setSk("test_sk_c");
        responseC.setAbQueryId("testQueryId");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(responseB).thenReturn(responseC);
        // act
        ModuleAbConfig resultB = douHuBiz.getAbByUnionIdAndExpId(unionId, expId, module, isMt);
        ModuleAbConfig resultC = douHuBiz.getAbByUnionIdAndExpId(unionId, expId, module, isMt);
        // assert
        assertEquals("b", resultB.getConfigs().get(0).getExpResult());
        assertEquals("c", resultC.getConfigs().get(0).getExpResult());
    }

    /**
     * Test when response is null, should return null
     */
    @Test
    public void testGetAbcConfigByResponseNullResponse() throws Throwable {
        // arrange
        DouHuBiz douHuBiz = new DouHuBiz();
        // act
        ModuleAbConfig result = douHuBiz.getAbcConfigByResponse(null, "exp1", "module1");
        // assert
        assertNull("Should return null for null response", result);
    }

    /**
     * Test when response.sk is blank, should return null
     */
    @Test
    public void testGetAbcConfigByResponseBlankSk() throws Throwable {
        // arrange
        DouHuBiz douHuBiz = new DouHuBiz();
        DouHuResponse response = new DouHuResponse();
        response.setSk("");
        response.setCode(ErrorCode.SUCCESS.getCode());
        // act
        ModuleAbConfig result = douHuBiz.getAbcConfigByResponse(response, "exp1", "module1");
        // assert
        assertNull("Should return null for blank sk", result);
    }

    /**
     * Test when response code is not SUCCESS, should return null
     */
    @Test
    public void testGetAbcConfigByResponseNotSuccessCode() throws Throwable {
        // arrange
        DouHuBiz douHuBiz = new DouHuBiz();
        DouHuResponse response = new DouHuResponse();
        response.setSk("test_sk");
        response.setCode(ErrorCode.FAIL.getCode());
        // act
        ModuleAbConfig result = douHuBiz.getAbcConfigByResponse(response, "exp1", "module1");
        // assert
        assertNull("Should return null for non-success response code", result);
    }

    /**
     * Test when response.sk contains "_b" suffix, should return config with expResult "b"
     */
    @Test
    public void testGetAbcConfigByResponseWithBSuffix() throws Throwable {
        // arrange
        DouHuBiz douHuBiz = new DouHuBiz();
        DouHuResponse response = new DouHuResponse();
        response.setSk("test_b");
        response.setCode(ErrorCode.SUCCESS.getCode());
        response.setAbQueryId("query123");
        // act
        ModuleAbConfig result = douHuBiz.getAbcConfigByResponse(response, "exp1", "module1");
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Module key should match", "module1", result.getKey());
        assertEquals("Should have one config", 1, result.getConfigs().size());
        AbConfig abConfig = result.getConfigs().get(0);
        assertEquals("ExpId should match", "exp1", abConfig.getExpId());
        assertEquals("ExpResult should be 'b'", "b", abConfig.getExpResult());
        assertNotNull("ExpBiInfo should not be null", abConfig.getExpBiInfo());
    }

    /**
     * Test when response.sk contains "_c" suffix, should return config with expResult "c"
     */
    @Test
    public void testGetAbcConfigByResponseWithCSuffix() throws Throwable {
        // arrange
        DouHuBiz douHuBiz = new DouHuBiz();
        DouHuResponse response = new DouHuResponse();
        response.setSk("test_c");
        response.setCode(ErrorCode.SUCCESS.getCode());
        response.setAbQueryId("query123");
        // act
        ModuleAbConfig result = douHuBiz.getAbcConfigByResponse(response, "exp1", "module1");
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Module key should match", "module1", result.getKey());
        assertEquals("Should have one config", 1, result.getConfigs().size());
        AbConfig abConfig = result.getConfigs().get(0);
        assertEquals("ExpId should match", "exp1", abConfig.getExpId());
        assertEquals("ExpResult should be 'c'", "c", abConfig.getExpResult());
        assertNotNull("ExpBiInfo should not be null", abConfig.getExpBiInfo());
    }

    /**
     * Test when response.sk contains neither suffix, should return config with default expResult "a"
     */
    @Test
    public void testGetAbcConfigByResponseWithNoSuffix() throws Throwable {
        // arrange
        DouHuBiz douHuBiz = new DouHuBiz();
        DouHuResponse response = new DouHuResponse();
        response.setSk("test");
        response.setCode(ErrorCode.SUCCESS.getCode());
        response.setAbQueryId("query123");
        // act
        ModuleAbConfig result = douHuBiz.getAbcConfigByResponse(response, "exp1", "module1");
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Module key should match", "module1", result.getKey());
        assertEquals("Should have one config", 1, result.getConfigs().size());
        AbConfig abConfig = result.getConfigs().get(0);
        assertEquals("ExpId should match", "exp1", abConfig.getExpId());
        assertEquals("ExpResult should be 'a'", "a", abConfig.getExpResult());
        assertNotNull("ExpBiInfo should not be null", abConfig.getExpBiInfo());
    }

    /**
     * Test when all parameters are valid but expId is blank, should still return config
     */
    @Test
    public void testGetAbcConfigByResponseWithBlankExpId() throws Throwable {
        // arrange
        DouHuBiz douHuBiz = new DouHuBiz();
        DouHuResponse response = new DouHuResponse();
        response.setSk("test");
        response.setCode(ErrorCode.SUCCESS.getCode());
        response.setAbQueryId("query123");
        // act
        ModuleAbConfig result = douHuBiz.getAbcConfigByResponse(response, "", "module1");
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Module key should match", "module1", result.getKey());
        assertEquals("Should have one config", 1, result.getConfigs().size());
        AbConfig abConfig = result.getConfigs().get(0);
        assertEquals("ExpId should be empty", "", abConfig.getExpId());
        assertEquals("ExpResult should be 'a'", "a", abConfig.getExpResult());
        assertNotNull("ExpBiInfo should not be null", abConfig.getExpBiInfo());
    }
}
