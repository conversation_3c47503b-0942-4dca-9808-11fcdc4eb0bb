package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import com.sankuai.mpmctcontent.application.thrift.api.content.WedPhotoCaseService;
import com.sankuai.mpmctcontent.application.thrift.dto.wed.BatchLoadCaseReqDTO;
import com.sankuai.mpmctcontent.application.thrift.dto.wed.BatchLoadCaseRespDTO;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

public class DigestQueryWrapperGetOldExhibitInfoFutureTest {

    private DigestQueryWrapper digestQueryWrapper;

    @Mock
    private WedPhotoCaseService wedPhotoCaseService;

    private AutoCloseable closeable;

    @Before
    public void setUp() {
        closeable = MockitoAnnotations.openMocks(this);
        digestQueryWrapper = new DigestQueryWrapper();
        // Use reflection to inject the mocked service
        try {
            java.lang.reflect.Field field = DigestQueryWrapper.class.getDeclaredField("wedPhotoCaseService");
            field.setAccessible(true);
            field.set(digestQueryWrapper, wedPhotoCaseService);
        } catch (Exception e) {
            throw new RuntimeException("Failed to inject mock", e);
        }
    }

    @After
    public void tearDown() throws Exception {
        closeable.close();
        // Reset the thread local future to avoid affecting other tests
        ContextStore.removeFuture();
    }

    /**
     * Test case when context is null
     * Expected: method should return null and log error
     */
    @Test
    public void testGetOldExhibitInfoFutureWhenContextIsNull() throws Throwable {
        // arrange
        DealCtx ctx = null;
        List<String> ids = Arrays.asList("123", "456");
        // act
        Future result = digestQueryWrapper.getOldExhibitInfoFuture(ctx, ids);
        // assert
        assertNull(result);
    }

    /**
     * Test case when ids list is null
     * Expected: method should return null
     */
    @Test
    public void testGetOldExhibitInfoFutureWhenIdsIsNull() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        List<String> ids = null;
        // act
        Future result = digestQueryWrapper.getOldExhibitInfoFuture(ctx, ids);
        // assert
        assertNull(result);
        // Verify that wedPhotoCaseService is not called
        verify(wedPhotoCaseService, never()).batchLoadCaseList(any(BatchLoadCaseReqDTO.class));
    }

    /**
     * Test case when ids list is empty
     * Expected: method should return null
     */
    @Test
    public void testGetOldExhibitInfoFutureWhenIdsIsEmpty() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        List<String> ids = Arrays.asList();
        // act
        Future result = digestQueryWrapper.getOldExhibitInfoFuture(ctx, ids);
        // assert
        assertNull(result);
        // Verify that wedPhotoCaseService is not called
        verify(wedPhotoCaseService, never()).batchLoadCaseList(any(BatchLoadCaseReqDTO.class));
    }

    /**
     * Test case when ids list contains valid numeric values
     * Expected: method should call wedPhotoCaseService and return future from ContextStore
     */
    @Test
    public void testGetOldExhibitInfoFutureWhenIdsAreValid() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        List<String> ids = Arrays.asList("123", "456", "789");
        // Mock the service response
        BatchLoadCaseRespDTO response = new BatchLoadCaseRespDTO();
        when(wedPhotoCaseService.batchLoadCaseList(any(BatchLoadCaseReqDTO.class))).thenReturn(response);
        // Mock ContextStore.getFuture() to return a mock future
        Future mockFuture = mock(Future.class);
        ContextStore.setFuture(mockFuture);
        // act
        Future result = digestQueryWrapper.getOldExhibitInfoFuture(ctx, ids);
        // assert
        assertNotNull(result);
        assertEquals(mockFuture, result);
        // Verify that wedPhotoCaseService.batchLoadCaseList was called with correct parameters
        ArgumentCaptor<BatchLoadCaseReqDTO> captor = ArgumentCaptor.forClass(BatchLoadCaseReqDTO.class);
        verify(wedPhotoCaseService, times(1)).batchLoadCaseList(captor.capture());
        BatchLoadCaseReqDTO capturedRequest = captor.getValue();
        assertNotNull(capturedRequest.getCaseIdList());
        assertEquals(3, capturedRequest.getCaseIdList().size());
        assertTrue(capturedRequest.getCaseIdList().contains(123L));
        assertTrue(capturedRequest.getCaseIdList().contains(456L));
        assertTrue(capturedRequest.getCaseIdList().contains(789L));
    }

    /**
     * Test case when ids list contains mixed numeric and non-numeric values
     * Expected: method should filter out non-numeric values and only process numeric ones
     */
    @Test
    public void testGetOldExhibitInfoFutureWhenIdsContainNonNumericValues() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        List<String> ids = Arrays.asList("123", "abc", "456", "", "789", "def");
        // Mock the service response
        BatchLoadCaseRespDTO response = new BatchLoadCaseRespDTO();
        when(wedPhotoCaseService.batchLoadCaseList(any(BatchLoadCaseReqDTO.class))).thenReturn(response);
        // Mock ContextStore.getFuture() to return a mock future
        Future mockFuture = mock(Future.class);
        ContextStore.setFuture(mockFuture);
        // act
        Future result = digestQueryWrapper.getOldExhibitInfoFuture(ctx, ids);
        // assert
        assertNotNull(result);
        assertEquals(mockFuture, result);
        // Verify that wedPhotoCaseService.batchLoadCaseList was called with only numeric values
        ArgumentCaptor<BatchLoadCaseReqDTO> captor = ArgumentCaptor.forClass(BatchLoadCaseReqDTO.class);
        verify(wedPhotoCaseService, times(1)).batchLoadCaseList(captor.capture());
        BatchLoadCaseReqDTO capturedRequest = captor.getValue();
        assertNotNull(capturedRequest.getCaseIdList());
        assertEquals(3, capturedRequest.getCaseIdList().size());
        assertTrue(capturedRequest.getCaseIdList().contains(123L));
        assertTrue(capturedRequest.getCaseIdList().contains(456L));
        assertTrue(capturedRequest.getCaseIdList().contains(789L));
    }

    /**
     * Test case when wedPhotoCaseService throws exception
     * Expected: method should catch exception, log error and return null
     */
    @Test
    public void testGetOldExhibitInfoFutureWhenServiceThrowsException() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        // Set some info content ID for logging
        ctx.setInfoContentId(999L);
        List<String> ids = Arrays.asList("123", "456");
        // Mock the service to throw exception
        when(wedPhotoCaseService.batchLoadCaseList(any(BatchLoadCaseReqDTO.class))).thenThrow(new RuntimeException("Service unavailable"));
        // act
        Future result = digestQueryWrapper.getOldExhibitInfoFuture(ctx, ids);
        // assert
        assertNull(result);
        // Verify that wedPhotoCaseService.batchLoadCaseList was called
        verify(wedPhotoCaseService, times(1)).batchLoadCaseList(any(BatchLoadCaseReqDTO.class));
    }
}
