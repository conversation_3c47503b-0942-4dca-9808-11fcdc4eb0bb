package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealAttrHelper_GetTimesTest {

    private static Method hasAttributeV2Method;

    @BeforeClass
    public static void setUpClass() throws Exception {
        // Use reflection to access the private method
        hasAttributeV2Method = DealAttrHelper.class.getDeclaredMethod("hasAttributeV2", java.util.List.class, String.class, String.class);
        hasAttributeV2Method.setAccessible(true);
    }

    @AfterClass
    public static void tearDownClass() {
        // Reset the method to its original state if necessary
        hasAttributeV2Method.setAccessible(false);
    }

    /**
     * Tests the getTimes method when attrs is null.
     */
    @Test
    public void testGetTimesWhenAttrsIsNull() throws Throwable {
        // Arrange & Act
        String result = DealAttrHelper.getTimes(null);
        // Assert
        Assert.assertNull("Expected result to be null when attrs is null", result);
    }

    /**
     * Tests the getTimes method when attrs is not empty, but does not contain the attribute corresponding to DealAttrKeys.SYS_MULTI_SALE_NUMBER.
     */
    @Test
    public void testGetTimesWhenAttrsNotContainsAttr() throws Throwable {
        // Arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("other");
        attrDTO.setValue(Arrays.asList("value1", "value2"));
        // Act
        String result = DealAttrHelper.getTimes(Collections.singletonList(attrDTO));
        // Assert
        Assert.assertEquals("Expected an empty string when the attribute is not found", "", result);
    }

    /**
     * Tests the getTimes method when attrs is not empty, contains the attribute corresponding to DealAttrKeys.SYS_MULTI_SALE_NUMBER, but the attribute value is empty.
     */
    @Test
    public void testGetTimesWhenAttrValueIsEmpty() throws Throwable {
        // Arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(DealAttrKeys.SYS_MULTI_SALE_NUMBER);
        attrDTO.setValue(Collections.emptyList());
        // Act
        String result = DealAttrHelper.getTimes(Collections.singletonList(attrDTO));
        // Assert
        Assert.assertEquals("Expected an empty string when the attribute value is empty", "", result);
    }

    /**
     * Tests the getTimes method when attrs is not empty, contains the attribute corresponding to DealAttrKeys.SYS_MULTI_SALE_NUMBER, and the attribute value is not empty.
     */
    @Test
    public void testGetTimesWhenAttrValueIsNotEmpty() throws Throwable {
        // Arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(DealAttrKeys.SYS_MULTI_SALE_NUMBER);
        attrDTO.setValue(Arrays.asList("value1", "value2"));
        // Act
        String result = DealAttrHelper.getTimes(Collections.singletonList(attrDTO));
        // Assert
        Assert.assertEquals("Expected the first value of the attribute when it is not empty", "value1", result);
    }

    @Test
    public void testHasAttributeV2WhenAttrsIsNull() throws Throwable {
        // arrange
        java.util.List<AttrDTO> attrs = null;
        String key = "key";
        String value = "value";
        // act
        boolean result = (boolean) hasAttributeV2Method.invoke(null, attrs, key, value);
        // assert
        assertFalse(result);
    }

    @Test
    public void testHasAttributeV2WhenAttrsIsEmpty() throws Throwable {
        // arrange
        java.util.List<AttrDTO> attrs = Collections.emptyList();
        String key = "key";
        String value = "value";
        // act
        boolean result = (boolean) hasAttributeV2Method.invoke(null, attrs, key, value);
        // assert
        assertFalse(result);
    }

    @Test
    public void testHasAttributeV2WhenAttrsDoesNotContainKey() throws Throwable {
        // arrange
        AttrDTO attr = new AttrDTO();
        attr.setName("otherKey");
        attr.setValue(Arrays.asList("otherValue"));
        java.util.List<AttrDTO> attrs = Arrays.asList(attr);
        String key = "key";
        String value = "value";
        // act
        boolean result = (boolean) hasAttributeV2Method.invoke(null, attrs, key, value);
        // assert
        assertFalse(result);
    }

    @Test
    public void testHasAttributeV2WhenAttrsContainsKeyButNotValue() throws Throwable {
        // arrange
        AttrDTO attr = new AttrDTO();
        attr.setName("key");
        attr.setValue(Arrays.asList("otherValue"));
        java.util.List<AttrDTO> attrs = Arrays.asList(attr);
        String key = "key";
        String value = "value";
        // act
        boolean result = (boolean) hasAttributeV2Method.invoke(null, attrs, key, value);
        // assert
        assertFalse(result);
    }

    @Test
    public void testHasAttributeV2WhenAttrsContainsKeyAndValue() throws Throwable {
        // arrange
        AttrDTO attr = new AttrDTO();
        attr.setName("key");
        attr.setValue(Arrays.asList("value"));
        java.util.List<AttrDTO> attrs = Arrays.asList(attr);
        String key = "key";
        String value = "value";
        // act
        boolean result = (boolean) hasAttributeV2Method.invoke(null, attrs, key, value);
        // assert
        assertTrue(result);
    }
}
