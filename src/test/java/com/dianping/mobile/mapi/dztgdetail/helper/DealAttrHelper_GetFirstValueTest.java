package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.deal.attribute.dto.AttributeDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.Collections;
import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class DealAttrHelper_GetFirstValueTest {

    /**
     * 测试getFirstValue方法，当attributeDtoList为null时，应返回空字符串
     */
    @Test
    public void testGetFirstValueWhenAttributeDtoListIsNull() {
        String result = DealAttrHelper.getFirstValue(null, "key");
        assertEquals("", result);
    }

    /**
     * 测试getFirstValue方法，当attributeDtoList不为null，但没有匹配的name时，应返回空字符串
     */
    @Test
    public void testGetFirstValueWhenNoMatchedName() {
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("otherKey");
        attributeDTO.setValue(Arrays.asList("value1", "value2"));
        String result = DealAttrHelper.getFirstValue(Collections.singletonList(attributeDTO), "key");
        assertEquals("", result);
    }

    /**
     * 测试getFirstValue方法，当attributeDtoList不为null，有匹配的name，但value列表为空时，应返回空字符串
     */
    @Test
    public void testGetFirstValueWhenValueListIsEmpty() {
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("key");
        attributeDTO.setValue(Collections.emptyList());
        String result = DealAttrHelper.getFirstValue(Collections.singletonList(attributeDTO), "key");
        assertEquals("", result);
    }

    /**
     * 测试getFirstValue方法，当attributeDtoList不为null，有匹配的name，且value列表不为空时，应返回value列表的第一个元素
     */
    @Test
    public void testGetFirstValueWhenValueListIsNotEmpty() {
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("key");
        attributeDTO.setValue(Arrays.asList("value1", "value2"));
        String result = DealAttrHelper.getFirstValue(Collections.singletonList(attributeDTO), "key");
        assertEquals("value1", result);
    }
}
