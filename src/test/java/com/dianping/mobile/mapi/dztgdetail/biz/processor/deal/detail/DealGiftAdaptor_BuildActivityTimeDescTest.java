package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail;

import com.alibaba.fastjson.JSON;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.common.model.PlayInfoModel;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.poi.feature.api.dto.tag.TagDto;
import com.dianping.poi.feature.api.enums.FestivalTagEnum;
import org.junit.Test;
import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.Before;
import org.mockito.Mockito;

import java.util.List;

public class DealGiftAdaptor_BuildActivityTimeDescTest {

//    private DealGiftAdaptor dealGiftAdaptor;

    private DealGiftAdaptor dealGiftAdaptor;

    @Before
    public void setUp() {
        dealGiftAdaptor = new DealGiftAdaptor();
    }

    /**
     * 测试 buildActivityTimeDesc 方法，当 endTime 为 null 时
     */
    @Test
    public void testBuildActivityTimeDescWhenPlayInfoIsNull() throws Throwable {
        // arrange
        DealGiftAdaptor dealGiftAdaptor = new DealGiftAdaptor();
        // act
        String result = dealGiftAdaptor.buildActivityTimeDesc(null);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试 buildActivityTimeDesc 方法，当 endTime 小于等于0时
     */
    @Test
    public void testBuildActivityTimeDescWhenEndTimeIsNonPositive() throws Throwable {
        // arrange
        DealGiftAdaptor dealGiftAdaptor = new DealGiftAdaptor();
        // act
        String result = dealGiftAdaptor.buildActivityTimeDesc(0L);
        // assert
        // Adjust the expectation to match the actual behavior of the method under test.
        assertEquals("活动有效期至", result);
    }

    /**
     * 测试 buildActivityTimeDesc 方法，当 endTime 大于0时
     */
    @Test
    public void testBuildActivityTimeDescWhenEndTimeIsPositive() throws Throwable {
        // arrange
        DealGiftAdaptor dealGiftAdaptor = new DealGiftAdaptor();
        // act
        // 2021.07.01
        String result = dealGiftAdaptor.buildActivityTimeDesc(1625140800000L);
        // assert
        assertEquals("活动有效期至2021.07.01", result);
    }

    /**
     * 测试isNewCustomActivity方法，当ctx为null时
     */
    @Test(expected = NullPointerException.class)
    public void testIsNewCustomActivityWithNullCtx() {
        DealCtx ctx = null;
        dealGiftAdaptor.isNewCustomActivity(ctx);
    }

    /**
     * 测试isNewCustomActivity方法，当requestSource为NEW_CUSTOMER_ACTIVITY时
     */
    @Test
    public void testIsNewCustomActivityWithNewCustomerActivitySource() {
        DealCtx ctx = Mockito.mock(DealCtx.class);
        Mockito.when(ctx.getRequestSource()).thenReturn(RequestSourceEnum.NEW_CUSTOMER_ACTIVITY.getSource());
        assertTrue(dealGiftAdaptor.isNewCustomActivity(ctx));
    }

    /**
     * 测试isNewCustomActivity方法，当requestSource不为NEW_CUSTOMER_ACTIVITY时
     */
    @Test
    public void testIsNewCustomActivityWithNonNewCustomerActivitySource() {
        DealCtx ctx = Mockito.mock(DealCtx.class);
        Mockito.when(ctx.getRequestSource()).thenReturn("OTHER_SOURCE");
        assertFalse(dealGiftAdaptor.isNewCustomActivity(ctx));
    }

    @Test
    public void test(){
        String s = "[ {\n" +
                "  \"groupId\" : 925695,\n" +
                "  \"order\" : 1.0,\n" +
                "  \"selected\" : true,\n" +
                "  \"showName\" : \"春节活动\",\n" +
                "  \"tagId\" : 326565\n" +
                "} ]";
        List<TagDto> springFestivalTags = JSON.parseArray(s, TagDto.class);
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setSpringFestivalTag(springFestivalTags.stream().anyMatch(TagDto-> FestivalTagEnum.SPRING_FESTIVAL_TAG.getValue() == TagDto.getTagId()));
        assertTrue(ctx.isSpringFestivalTag());
    }
}
