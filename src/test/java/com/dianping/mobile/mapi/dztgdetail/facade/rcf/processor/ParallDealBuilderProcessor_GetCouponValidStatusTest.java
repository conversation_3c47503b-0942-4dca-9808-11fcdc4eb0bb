package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import com.dianping.deal.style.dto.DealGroupDzxInfo;
import com.dianping.deal.style.dto.StyleAbConfig;
import com.dianping.deal.style.dto.StyleExp;
import com.dianping.deal.style.dto.StyleResponse;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CouponValidStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.MTTemplateKey;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.style.ParallDealStyleProcessor;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.buy.DealGroupBuyRuleDTO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ParallDealBuilderProcessor_GetCouponValidStatusTest {

    @InjectMocks
    private ParallDealBuilderProcessor parallDealBuilderProcessor;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private EnvCtx envCtx;

    @Mock
    private DealGroupDTO dealGroup;

    @Mock
    private DealGroupRuleDTO rule;

    @Mock
    private DealGroupBuyRuleDTO buyRule;

    private boolean invokePrivateIsOnlineShop(DpPoiDTO shop) throws Exception {
        Method method = ParallDealBuilderProcessor.class.getDeclaredMethod("isOnlineShop", DpPoiDTO.class);
        method.setAccessible(true);
        return (boolean) method.invoke(parallDealBuilderProcessor, shop);
    }

    /**
     * 测试优惠券在有效时间之前的情况
     */
    @Test
    public void testGetCouponValidStatusBeforeValidTime() {
        // arrange
        Date startTime = new Date(System.currentTimeMillis() + 10000);
        Date endTime = new Date(System.currentTimeMillis() + 20000);
        // act
        int result = ParallDealBuilderProcessor.getCouponValidStatus(startTime, endTime);
        // assert
        assertEquals(CouponValidStatusEnum.UN_VALID.getCode(), result);
    }

    /**
     * 测试优惠券在有效时间之内的情况
     */
    @Test
    public void testGetCouponValidStatusDuringValidTime() {
        // arrange
        Date startTime = new Date(System.currentTimeMillis() - 10000);
        Date endTime = new Date(System.currentTimeMillis() + 10000);
        // act
        int result = ParallDealBuilderProcessor.getCouponValidStatus(startTime, endTime);
        // assert
        assertEquals(CouponValidStatusEnum.VALID.getCode(), result);
    }

    /**
     * 测试优惠券在有效时间开始点的情况
     */
    @Test
    public void testGetCouponValidStatusAtStartTime() {
        // arrange
        Date startTime = new Date(System.currentTimeMillis());
        Date endTime = new Date(System.currentTimeMillis() + 10000);
        // act
        int result = ParallDealBuilderProcessor.getCouponValidStatus(startTime, endTime);
        // assert
        assertEquals(CouponValidStatusEnum.UN_VALID.getCode(), result);
    }

    /**
     * 测试优惠券在有效时间结束点的情况
     */
    @Test
    public void testGetCouponValidStatusAtEndTime() {
        // arrange
        Date startTime = new Date(System.currentTimeMillis() - 10000);
        Date endTime = new Date(System.currentTimeMillis());
        // act
        int result = ParallDealBuilderProcessor.getCouponValidStatus(startTime, endTime);
        // assert
        assertEquals(CouponValidStatusEnum.UN_VALID.getCode(), result);
    }

    /**
     * 测试优惠券在有效时间之后的情况
     */
    @Test
    public void testGetCouponValidStatusAfterValidTime() {
        // arrange
        Date startTime = new Date(System.currentTimeMillis() - 10000);
        Date endTime = new Date(System.currentTimeMillis() - 5000);
        // act
        int result = ParallDealBuilderProcessor.getCouponValidStatus(startTime, endTime);
        // assert
        assertEquals(CouponValidStatusEnum.UN_VALID.getCode(), result);
    }

    /**
     * Test when shop is null
     */
    @Test
    public void testIsOnlineShop_WhenShopIsNull() throws Throwable {
        // arrange
        DpPoiDTO shop = null;
        // act
        boolean result = invokePrivateIsOnlineShop(shop);
        // assert
        assertFalse(result);
    }

    /**
     * Test when useType is null
     */
    @Test
    public void testIsOnlineShop_WhenUseTypeIsNull() throws Throwable {
        // arrange
        DpPoiDTO shop = mock(DpPoiDTO.class);
        when(shop.getUseType()).thenReturn(null);
        // act
        boolean result = invokePrivateIsOnlineShop(shop);
        // assert
        assertFalse(result);
    }

    /**
     * Test when useType is not 38
     */
    @Test
    public void testIsOnlineShop_WhenUseTypeIsNot38() throws Throwable {
        // arrange
        DpPoiDTO shop = mock(DpPoiDTO.class);
        when(shop.getUseType()).thenReturn(37);
        // act
        boolean result = invokePrivateIsOnlineShop(shop);
        // assert
        assertFalse(result);
    }

    /**
     * Test when appSides is not "1119"
     */
    @Test
    public void testIsOnlineShop_WhenAppSidesIsNot1119() throws Throwable {
        // arrange
        DpPoiDTO shop = mock(DpPoiDTO.class);
        when(shop.getUseType()).thenReturn(38);
        when(shop.getAppSides()).thenReturn("1118");
        // act
        boolean result = invokePrivateIsOnlineShop(shop);
        // assert
        assertFalse(result);
    }

    /**
     * Test when appSides is null
     */
    @Test
    public void testIsOnlineShop_WhenAppSidesIsNull() throws Throwable {
        // arrange
        DpPoiDTO shop = mock(DpPoiDTO.class);
        when(shop.getUseType()).thenReturn(38);
        when(shop.getAppSides()).thenReturn(null);
        // act
        boolean result = invokePrivateIsOnlineShop(shop);
        // assert
        assertFalse(result);
    }

    /**
     * Test when all conditions are met (positive case)
     */
    @Test
    public void testIsOnlineShop_WhenAllConditionsAreMet() throws Throwable {
        // arrange
        DpPoiDTO shop = mock(DpPoiDTO.class);
        when(shop.getUseType()).thenReturn(38);
        when(shop.getAppSides()).thenReturn("1119");
        // act
        boolean result = invokePrivateIsOnlineShop(shop);
        // assert
        assertTrue(result);
    }

    @Before
    public void setUp() {
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
    }

    @Test
    public void testIsPurchaseLimitDeal_WhenVersionNotMeet() {
        // arrange
        when(dealCtx.isMt()).thenReturn(true);
        // Lower than required version
        when(envCtx.getVersion()).thenReturn("12.20.300");
        // act
        boolean result = parallDealBuilderProcessor.isPurchaseLimitDeal(dealCtx);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsPurchaseLimitDeal_WhenDealGroupNull() {
        // arrange
        when(dealCtx.isMt()).thenReturn(true);
        when(envCtx.getVersion()).thenReturn("12.20.500");
        when(dealCtx.getDealGroupDTO()).thenReturn(null);
        // act
        boolean result = parallDealBuilderProcessor.isPurchaseLimitDeal(dealCtx);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsPurchaseLimitDeal_WhenRuleNull() {
        // arrange
        when(dealCtx.isMt()).thenReturn(true);
        when(envCtx.getVersion()).thenReturn("12.20.500");
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroup);
        when(dealGroup.getRule()).thenReturn(null);
        // act
        boolean result = parallDealBuilderProcessor.isPurchaseLimitDeal(dealCtx);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsPurchaseLimitDeal_WhenHasPurchaseLimit() {
        // arrange
        when(dealCtx.isMt()).thenReturn(true);
        when(envCtx.getVersion()).thenReturn("12.20.500");
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroup);
        when(dealGroup.getRule()).thenReturn(rule);
        when(rule.getBuyRule()).thenReturn(buyRule);
        when(buyRule.getMaxPerUser()).thenReturn(2);
        // act
        boolean result = parallDealBuilderProcessor.isPurchaseLimitDeal(dealCtx);
        // assert
        assertTrue(result);
    }

    @Test
    public void testIsPurchaseLimitDeal_WhenNoPurchaseLimit() {
        // arrange
        when(dealCtx.isMt()).thenReturn(true);
        when(envCtx.getVersion()).thenReturn("12.20.500");
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroup);
        when(dealGroup.getRule()).thenReturn(rule);
        when(rule.getBuyRule()).thenReturn(buyRule);
        when(buyRule.getMaxPerUser()).thenReturn(0);
        // act
        boolean result = parallDealBuilderProcessor.isPurchaseLimitDeal(dealCtx);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsPurchaseLimitDeal_WhenDianpingApp() {
        // arrange
        when(dealCtx.isMt()).thenReturn(false);
        when(envCtx.getVersion()).thenReturn("11.17.0");
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroup);
        when(dealGroup.getRule()).thenReturn(rule);
        when(rule.getBuyRule()).thenReturn(buyRule);
        when(buyRule.getMaxPerUser()).thenReturn(2);
        // act
        boolean result = parallDealBuilderProcessor.isPurchaseLimitDeal(dealCtx);
        // assert
        assertTrue(result);
    }

    @Test
    public void testParallDealStyleProcessor() throws Throwable {
        ParallDealStyleProcessor processor = new ParallDealStyleProcessor();
        DealGroupDzxInfo dealGroupDzxInfo = new DealGroupDzxInfo();
        EnvCtx envCtx1 = new EnvCtx();
        DealCtx dealCtx = new DealCtx(envCtx1);
        dealCtx.setDealGroupDzxInfo(dealGroupDzxInfo);
        StyleResponse styleResponse = new StyleResponse();
        List<StyleAbConfig> styleAbConfigs = new ArrayList<StyleAbConfig>();
        StyleAbConfig styleAbConfig = new StyleAbConfig();
        styleAbConfig.setKey("key");
        List<StyleExp> styleExps = new ArrayList<>();
        StyleExp styleExp = new StyleExp();
        styleExp.setExpId("expId");
        styleExp.setExpResult("expResult");
        styleExp.setExpBiInfo("expBiInfo");
        styleExps.add(styleExp);
        styleAbConfig.setStyleExps(styleExps);
        styleAbConfigs.add(styleAbConfig);
        styleResponse.setStyleAbConfigs(styleAbConfigs);
        dealCtx.setStyleResponse(styleResponse);
        MTTemplateKey result = processor.buildPreviewTemplateKey(dealCtx);
        assertNotNull(result);
    }
}
