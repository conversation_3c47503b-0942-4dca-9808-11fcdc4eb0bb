package com.dianping.mobile.mapi.dztgdetail.button.joy;

import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.button.mtlive.MtLiveMiniAppBannerBuilder;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.PriceContext;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

/**
 * <AUTHOR>
 * @Date 2024/2/20 16:04
 */
@RunWith(MockitoJUnitRunner.class)
public class MarketPriceNormalButtonBuilderTest {
    @InjectMocks
    private MarketPriceNormalButtonBuilder marketPriceNormalButtonBuilder;

    @Test
    public void testAfterBuild_isMarketPriceNormalButtonHideTrue() {
        // arrange
        DealBuyBtn button = new DealBuyBtn(true, "立即抢购");
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx dealCtx = new DealCtx(envCtx);
        DealGroupBaseDTO dealGroupBaseDTO = new DealGroupBaseDTO();
        dealCtx.setDealGroupBase(dealGroupBaseDTO);
        dealCtx.setPriceContext(new PriceContext());
        dealCtx.setMarketPriceNormalButtonHide(true);
        // act
        marketPriceNormalButtonBuilder.afterBuild(dealCtx, button);
        assertEquals(button.getBtnDesc(), StringUtils.EMPTY);
    }

}
