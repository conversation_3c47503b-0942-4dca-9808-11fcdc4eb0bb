package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.highlights.DztgHighlightsModule;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class HealthCertificateExaminerHandlerBuildCertificateTimeTest {

    @InjectMocks
    private HealthCertificateExaminerHandler healthCertificateExaminerHandler;

    @Mock
    private DztgHighlightsModule module;

    private AttrDTO attrDTO;

    private HealthCertificateExaminerHandler handler = new HealthCertificateExaminerHandler();

    @Before
    public void setUp() {
        attrDTO = new AttrDTO();
        attrDTO.setName("physical_examination_get_result_time");
        attrDTO.setValue(Collections.singletonList("0"));
        when(module.getAttrs()).thenReturn(new ArrayList<>());
    }

    private void invokeBuildCertificateTime(List<AttrDTO> attrs) throws Exception {
        Method method = HealthCertificateExaminerHandler.class.getDeclaredMethod("buildCertificateTime", List.class, DztgHighlightsModule.class);
        method.setAccessible(true);
        method.invoke(healthCertificateExaminerHandler, attrs, module);
    }

    @Test
    public void testBuildCertificateTimeWithoutPhysicalCardsAndTrainingCertificate() throws Throwable {
        List<AttrDTO> attrs = Collections.emptyList();
        invokeBuildCertificateTime(attrs);
        verify(module, never()).getAttrs();
    }

    @Test
    public void testBuildCertificateTimeWithPhysicalCards() throws Throwable {
        attrDTO.setName("include_physical_cards");
        attrDTO.setValue(Collections.singletonList("是"));
        AttrDTO attrDTO2 = new AttrDTO();
        attrDTO2.setName("physical_examination_get_result_time");
        attrDTO2.setValue(Collections.singletonList("0"));
        List<AttrDTO> attrs = Arrays.asList(attrDTO, attrDTO2);
        invokeBuildCertificateTime(attrs);
        verify(module).getAttrs();
    }

    @Test
    public void testBuildCertificateTimeWithTrainingCertificate() throws Throwable {
        attrDTO.setName("include_a_training_certificate");
        attrDTO.setValue(Collections.singletonList("是"));
        AttrDTO attrDTO2 = new AttrDTO();
        attrDTO2.setName("physical_examination_get_result_time");
        attrDTO2.setValue(Collections.singletonList("0"));
        List<AttrDTO> attrs = Arrays.asList(attrDTO, attrDTO2);
        invokeBuildCertificateTime(attrs);
        verify(module).getAttrs();
    }

    @Test
    public void testBuildCertificateTimeWithPhysicalCardsAndTrainingCertificate() throws Throwable {
        attrDTO.setName("include_physical_cards");
        attrDTO.setValue(Collections.singletonList("是"));
        AttrDTO attrDTO2 = new AttrDTO();
        attrDTO2.setName("include_a_training_certificate");
        attrDTO2.setValue(Collections.singletonList("是"));
        AttrDTO attrDTO3 = new AttrDTO();
        attrDTO3.setName("physical_examination_get_result_time");
        attrDTO3.setValue(Collections.singletonList("0"));
        List<AttrDTO> attrs = Arrays.asList(attrDTO, attrDTO2, attrDTO3);
        invokeBuildCertificateTime(attrs);
        verify(module).getAttrs();
    }

    @Test
    public void testBuildCertificateTimeWithoutPhysicalExaminationGetResultTime() throws Throwable {
        attrDTO.setName("physical_examination_get_result_time");
        attrDTO.setValue(Collections.emptyList());
        List<AttrDTO> attrs = Arrays.asList(attrDTO);
        invokeBuildCertificateTime(attrs);
        verify(module, never()).getAttrs();
    }

    @Test
    public void testBuildCertificateTimeWithPhysicalExaminationGetResultTime() throws Throwable {
        List<AttrDTO> attrs = Arrays.asList(attrDTO);
        invokeBuildCertificateTime(attrs);
        verify(module).getAttrs();
    }

    @Test
    public void testFindAttrListWhenAttrsIsEmpty() throws Throwable {
        List<AttrDTO> attrs = Arrays.asList();
        String field = "test";
        Optional<List<String>> result = handler.findAttrList(attrs, field);
        assertFalse(result.isPresent());
    }

    @Test
    public void testFindAttrListWhenAttrNotExists() throws Throwable {
        AttrDTO attr = new AttrDTO();
        attr.setName("other");
        List<AttrDTO> attrs = Arrays.asList(attr);
        String field = "test";
        Optional<List<String>> result = handler.findAttrList(attrs, field);
        assertFalse(result.isPresent());
    }

    @Test
    public void testFindAttrListWhenAttrValueIsEmpty() throws Throwable {
        AttrDTO attr = new AttrDTO();
        attr.setName("test");
        attr.setValue(Arrays.asList());
        List<AttrDTO> attrs = Arrays.asList(attr);
        String field = "test";
        Optional<List<String>> result = handler.findAttrList(attrs, field);
        assertFalse(result.isPresent());
    }

    @Test
    public void testFindAttrListWhenAttrValueIsNotEmpty() throws Throwable {
        AttrDTO attr = new AttrDTO();
        attr.setName("test");
        attr.setValue(Arrays.asList("value1", "value2"));
        List<AttrDTO> attrs = Arrays.asList(attr);
        String field = "test";
        Optional<List<String>> result = handler.findAttrList(attrs, field);
        assertTrue(result.isPresent());
        assertEquals(Arrays.asList("value1", "value2"), result.get());
    }

    /**
     * Test findAttrString method when attrs list is empty, should return Optional.empty()
     */
    @Test
    public void testFindAttrStringWhenAttrsIsEmpty() throws Throwable {
        // arrange
        List<AttrDTO> attrs = Arrays.asList();
        String field = "test";
        // act
        Optional<String> result = handler.findAttrString(attrs, field);
        // assert
        assertFalse(result.isPresent());
    }

    /**
     * Test findAttrString method when attrs list is not empty, but no attribute name is field, should return Optional.empty()
     */
    @Test
    public void testFindAttrStringWhenNoAttrNamedField() throws Throwable {
        // arrange
        AttrDTO attr = new AttrDTO();
        attr.setName("other");
        List<AttrDTO> attrs = Arrays.asList(attr);
        String field = "test";
        // act
        Optional<String> result = handler.findAttrString(attrs, field);
        // assert
        assertFalse(result.isPresent());
    }

    /**
     * Test findAttrString method when attrs list is not empty, and there is at least one attribute named field, should return the first value of this attribute in Optional wrapping
     */
    @Test
    public void testFindAttrStringWhenAttrNamedFieldExists() throws Throwable {
        // arrange
        AttrDTO attr = new AttrDTO();
        attr.setName("test");
        attr.setValue(Arrays.asList("value1", "value2"));
        List<AttrDTO> attrs = Arrays.asList(attr);
        String field = "test";
        // act
        Optional<String> result = handler.findAttrString(attrs, field);
        // assert
        assertTrue(result.isPresent());
        assertEquals("value1", result.get());
    }
}
