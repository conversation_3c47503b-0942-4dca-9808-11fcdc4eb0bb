package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.HaimaWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CommonAttrVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.highlights.DztgHighlightsModule;
import com.dianping.mobile.mapi.dztgdetail.entity.CareCenterHouseStyleConfig;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static groovy.util.GroovyTestCase.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class CareCenterHouseStyleHighlightsProcessorTest {

    @InjectMocks
    private CareCenterHouseStyleHighlightsProcessor careCenterHouseStyleHighlightsProcessor;

    @Mock
    private HaimaWrapper haimaWrapper;

    @Mock
    private Lion lion;

    @Mock
    private Lion lionMock;

    @Mock
    private CareCenterHouseStyleConfig mockConfig;

    private CareCenterHouseStyleHighlightsProcessor processor;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        processor = new CareCenterHouseStyleHighlightsProcessor();
        processor.careCenterHouseStyleConfig = mockConfig;
        processor.enable = true; // 假设配置已经正确加载
    }

    @Test
    public void testCareCenterHouseStyleConfigData() throws Exception {
        String constantsKey = LionConstants.CARE_CENTER_HOUSE_STYLE;
        String str = "{\n" +
                "    \"enable\": true,\n" +
                "    \"delimiter\": \"arrow\",\n" +
                "    \"style\": \"struct\",\n" +
                "    \"attrs\": [\n" +
                "        {\n" +
                "            \"name\": \"第1步\",\n" +
                "            \"value\": \"预约看房型\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"name\": \"第2步\",\n" +
                "            \"value\": \"体验服务\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"name\": \"第3步\",\n" +
                "            \"value\": \"线上购买\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"name\": \"第4步\",\n" +
                "            \"value\": \"安心入驻\"\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        CareCenterHouseStyleConfig careCenterHouseStyleConfig = JSON.parseObject(str, CareCenterHouseStyleConfig.class);
        Assert.assertNotNull(careCenterHouseStyleConfig);
    }


//    /**
//     * 测试CareCenterHouseStyleHighlightsProcessor构造函数，当Lion返回有效配置且enable为true时
//     */
//    @Test
//    public void testCareCenterHouseStyleHighlightsProcessor_WithValidConfigAndEnableTrue() throws Exception {
//        try (MockedStatic<Lion> mockedStatic = mockStatic(Lion.class)) {
//            String validConfig = "{\"enable\":true}";
//            mockedStatic.when(() -> Lion.getString(LionConstants.APP_KEY, LionConstants.CARE_CENTER_HOUSE_STYLE)).thenReturn(validConfig);
//
//            CareCenterHouseStyleHighlightsProcessor processor = new CareCenterHouseStyleHighlightsProcessor();
//
//            assertTrue("Processor should be enabled", processor.enable);
//        }
//    }


    /**
     * 测试CareCenterHouseStyleHighlightsProcessor构造函数，当Lion返回有效配置但enable为false时
     */
    @Test
    public void testCareCenterHouseStyleHighlightsProcessor_WithValidConfigAndEnableFalse() throws Exception {
        // arrange
        String validConfig = "{\"enable\":false}";
        try (MockedStatic<Lion> mocked = Mockito.mockStatic(Lion.class)) {
            mocked.when(() -> Lion.getString(LionConstants.APP_KEY, LionConstants.CARE_CENTER_HOUSE_STYLE)).thenReturn(validConfig);
            // act
            CareCenterHouseStyleHighlightsProcessor processor = new CareCenterHouseStyleHighlightsProcessor();
            // assert
            assertFalse("Processor should not be enabled", processor.enable);
        }
    }

    /**
     * 测试CareCenterHouseStyleHighlightsProcessor构造函数，当Lion返回无效配置时
     */
    @Test
    public void testCareCenterHouseStyleHighlightsProcessor_WithInvalidConfig() throws Exception {
        // arrange
        String invalidConfig = "invalid";
        try (MockedStatic<Lion> mocked = Mockito.mockStatic(Lion.class)) {
            mocked.when(() -> Lion.getString(LionConstants.APP_KEY, LionConstants.CARE_CENTER_HOUSE_STYLE)).thenReturn(invalidConfig);
            // act
            CareCenterHouseStyleHighlightsProcessor processor = new CareCenterHouseStyleHighlightsProcessor();
            // assert
            assertFalse("Processor should not be enabled with invalid config", processor.enable);
        }
    }

    /**
     * 测试CareCenterHouseStyleHighlightsProcessor构造函数，当Lion抛出异常时
     */
    @Test
    public void testCareCenterHouseStyleHighlightsProcessor_WithException() throws Exception {
        // arrange
        try (MockedStatic<Lion> mocked = Mockito.mockStatic(Lion.class)) {
            mocked.when(() -> Lion.getString(LionConstants.APP_KEY, LionConstants.CARE_CENTER_HOUSE_STYLE)).thenThrow(new RuntimeException());
            // act
            CareCenterHouseStyleHighlightsProcessor processor = new CareCenterHouseStyleHighlightsProcessor();
            // assert
            assertFalse("Processor should not be enabled when exception occurs", processor.enable);
        }
    }

    /**
     * 测试getHighlightsIdentify方法，确保对于null输入，也返回空字符串
     */
    @Test
    public void testGetHighlightsIdentifyWithNullInput() {
        // arrange
        CareCenterHouseStyleHighlightsProcessor processor = new CareCenterHouseStyleHighlightsProcessor();
        DealCtx ctx = null; // 设置输入为null

        // act
        String result = processor.getHighlightsIdentify(ctx);

        // assert
        Assert.assertEquals("", result); // 断言结果应该是空字符串
    }

    /**
     * 测试getHighlightsIdentify方法，确保对于具体DealCtx实例输入，也返回空字符串
     */
    @Test
    public void testGetHighlightsIdentifyWithSpecificDealCtxInstance() {
        // arrange
        CareCenterHouseStyleHighlightsProcessor processor = new CareCenterHouseStyleHighlightsProcessor();
        DealCtx ctx = new DealCtx(new EnvCtx()); // 创建一个具体的DealCtx实例

        // act
        String result = processor.getHighlightsIdentify(ctx);

        // assert
        Assert.assertEquals("", result); // 断言结果应该是空字符串
    }

    /**
     * 测试getHighlightsIdentify方法，确保对于通过Mockito模拟的DealCtx对象，也返回空字符串
     */
    @Test
    public void testGetHighlightsIdentifyWithMockedDealCtx() {
        // arrange
        CareCenterHouseStyleHighlightsProcessor processor = new CareCenterHouseStyleHighlightsProcessor();
        DealCtx ctx = mock(DealCtx.class); // 使用Mockito模拟DealCtx对象

        // act
        String result = processor.getHighlightsIdentify(ctx);

        // assert
        Assert.assertEquals("", result); // 断言结果应该是空字符串
    }

    /**
     * 测试 getHighlightsStyle 方法，当 enable 为 true 时
     */
    @Test
    public void testGetHighlightsStyleEnabled() {
        // arrange
        when(mockConfig.getStyle()).thenReturn("bold");

        // act
        String result = processor.getHighlightsStyle(new DealCtx(new EnvCtx()));

        // assert
        assertEquals("bold", result);
    }

    /**
     * 测试 getHighlightsAttrs 方法，当 enable 为 true 时
     */
    @Test
    public void testGetHighlightsAttrsEnabled() {
        // arrange
        List<CommonAttrVO> expectedAttrs = Arrays.asList(new CommonAttrVO(), new CommonAttrVO());
        when(mockConfig.getAttrs()).thenReturn(expectedAttrs);

        // act
        List<CommonAttrVO> result = processor.getHighlightsAttrs(new DealCtx(new EnvCtx()));

        // assert
        assertEquals(expectedAttrs, result);
    }

    /**
     * 测试 afterBuild 方法，当 enable 为 true 时
     */
    @Test
    public void testAfterBuildEnabled() {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(mockConfig.getDelimiter()).thenReturn("|");
        DztgHighlightsModule mockModule = mock(DztgHighlightsModule.class);
        when(ctx.getHighlightsModule()).thenReturn(mockModule);

        // act
        processor.afterBuild(ctx);

        // assert
        verify(mockModule).setDelimiter("|");
    }





//    /**
//     * 测试CareCenterHouseStyleHighlightsProcessor构造函数，当Lion返回有效配置且enable为true时
//     */
//    @Test
//    public void testCareCenterHouseStyleHighlightsProcessor_WithVali3dConfigAndEnableTrue() throws Exception {
//        try (MockedStatic<Lion> mockedStatic = mockStatic(Lion.class)) {
//            String validConfig = "{\"enable\":true}";
//            mockedStatic.when(() -> Lion.getString(LionConstants.APP_KEY, LionConstants.CARE_CENTER_HOUSE_STYLE)).thenReturn(validConfig);
//
//            CareCenterHouseStyleHighlightsProcessor processor = new CareCenterHouseStyleHighlightsProcessor();
//
//            assertTrue("Processor should be enabled", processor.enable);
//        }
//    }

    /**
     * 测试CareCenterHouseStyleHighlightsProcessor构造函数，当Lion返回有效配置但enable为false时
     */
    @Test
    public void testCareCenterHouseStyleHighlightsProcessor_WithValidConfi1gAndEnableFalse() throws Exception {
        try (MockedStatic<Lion> mockedStatic = mockStatic(Lion.class)) {
            String validConfig = "{\"enable\":false}";
            mockedStatic.when(() -> Lion.getString(LionConstants.APP_KEY, LionConstants.CARE_CENTER_HOUSE_STYLE)).thenReturn(validConfig);

            CareCenterHouseStyleHighlightsProcessor processor = new CareCenterHouseStyleHighlightsProcessor();

            assertFalse("Processor should not be enabled", processor.enable);
        }
    }

    /**
     * 测试CareCenterHouseStyleHighlightsProcessor构造函数，当Lion返回的配置为空时
     */
    @Test
    public void testCareCenterHouseStyleHighlightsProcessor_WithNullCon2fig() throws Exception {
        try (MockedStatic<Lion> mockedStatic = mockStatic(Lion.class)) {
            mockedStatic.when(() -> Lion.getString(LionConstants.APP_KEY, LionConstants.CARE_CENTER_HOUSE_STYLE)).thenReturn(null);

            CareCenterHouseStyleHighlightsProcessor processor = new CareCenterHouseStyleHighlightsProcessor();

            assertFalse("Processor should not be enabled", processor.enable);
        }
    }

    /**
     * 测试CareCenterHouseStyleHighlightsProcessor构造函数，当Lion抛出异常时
     */
    @Test
    public void testCareCenterHouseStyleHighlightsProcessor_WithException1() throws Exception {
        try (MockedStatic<Lion> mockedStatic = mockStatic(Lion.class)) {
            mockedStatic.when(() -> Lion.getString(LionConstants.APP_KEY, LionConstants.CARE_CENTER_HOUSE_STYLE)).thenThrow(new RuntimeException());

            CareCenterHouseStyleHighlightsProcessor processor = new CareCenterHouseStyleHighlightsProcessor();

            assertFalse("Processor should not be enabled due to exception", processor.enable);
        }
    }

}
