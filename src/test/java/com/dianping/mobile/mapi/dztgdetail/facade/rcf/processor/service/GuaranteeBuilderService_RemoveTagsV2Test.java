package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.Guarantee;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import java.util.ArrayList;
import java.util.List;

public class GuaranteeBuilderService_RemoveTagsV2Test {

    private GuaranteeBuilderService guaranteeBuilderService;

    @Before
    public void setUp() {
        guaranteeBuilderService = new GuaranteeBuilderService();
    }

    /**
     * 测试 removeTagsV2 方法，当 result 为 null 时，方法应该直接返回，不进行任何操作
     */
    @Test
    @Ignore
    public void testRemoveTagsV2WhenResultIsNull() throws Throwable {
        guaranteeBuilderService.removeTagsV2(null);
    }

    /**
     * 测试 removeTagsV2 方法，当 result 为空列表时，方法应该直接返回，不进行任何操作
     */
    @Test
    public void testRemoveTagsV2WhenResultIsEmpty() throws Throwable {
        List<Guarantee> result = new ArrayList<>();
        guaranteeBuilderService.removeTagsV2(result);
        Assert.assertTrue(result.isEmpty());
    }

    /**
     * 测试 removeTagsV2 方法，当 result 包含 "需预约"，"免预约"，"可在线预约" 和 "购后可在线预约" 这四个 Guarantee 对象时，方法应该将这些对象从列表中移除
     */
    @Test
    public void testRemoveTagsV2WhenResultContainsAllGuarantees() throws Throwable {
        List<Guarantee> result = new ArrayList<>();
        result.add(Guarantee.builder().text("需预约").build());
        result.add(Guarantee.builder().text("免预约").build());
        result.add(Guarantee.builder().text("可在线预约").build());
        result.add(Guarantee.builder().text("购后可在线预约").build());
        guaranteeBuilderService.removeTagsV2(result);
        Assert.assertTrue(result.isEmpty());
    }

    /**
     * 测试 removeTagsV2 方法，当 result 包含 "需预约"，"免预约"，"可在线预约" 和 "购后可在线预约" 这四个 Guarantee 对象的部分子集时，方法应该移除这些子集中的每个对象
     */
    @Test
    public void testRemoveTagsV2WhenResultContainsSubsetOfGuarantees() throws Throwable {
        List<Guarantee> result = new ArrayList<>();
        result.add(Guarantee.builder().text("需预约").build());
        result.add(Guarantee.builder().text("可在线预约").build());
        guaranteeBuilderService.removeTagsV2(result);
        Assert.assertTrue(result.isEmpty());
    }

    /**
     * 测试 removeTagsV2 方法，当 result 不包含 "需预约"，"免预约"，"可在线预约" 和 "购后可在线预约" 这四个 Guarantee 对象时，方法应该直接返回，不进行任何操作
     */
    @Test
    public void testRemoveTagsV2WhenResultDoesNotContainGuarantees() throws Throwable {
        List<Guarantee> result = new ArrayList<>();
        result.add(Guarantee.builder().text("其他预约方式").build());
        guaranteeBuilderService.removeTagsV2(result);
        Assert.assertTrue(result.size() == 1);
        Assert.assertEquals("其他预约方式", result.get(0).getText());
    }
}
