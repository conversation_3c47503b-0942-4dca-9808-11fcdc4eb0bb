package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DealStyle;
import java.lang.reflect.Method;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealStyleProcessorIsEnableTest {

    @Mock
    private DealCtx dealCtx;

    @Mock
    private EnvCtx envCtx;

    @InjectMocks
    private DealStyleProcessor dealStyleProcessor;

    private DealStyle invokePrivateTransferMethod(DealStyle dealStyle) throws Throwable {
        Method method = DealStyleProcessor.class.getDeclaredMethod("transfer", DealStyle.class);
        method.setAccessible(true);
        return (DealStyle) method.invoke(dealStyleProcessor, dealStyle);
    }

    private String generateLongString(int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append("a");
        }
        return sb.toString();
    }

    /**
     * 测试场景：ctx.getEnvCtx() 不为 null，且 ctx.getEnvCtx().isFromH5() 为 false
     * 预期结果：返回 true
     */
    @Test
    public void testIsEnableWhenEnvCtxNotNullAndNotFromH5() throws Throwable {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.isFromH5()).thenReturn(false);
        // act
        boolean result = dealStyleProcessor.isEnable(dealCtx);
        // assert
        assertTrue(result);
    }

    /**
     * 测试场景：ctx.getEnvCtx() 不为 null，且 ctx.getEnvCtx().isFromH5() 为 true
     * 预期结果：返回 false
     */
    @Test
    public void testIsEnableWhenEnvCtxNotNullAndFromH5() throws Throwable {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.isFromH5()).thenReturn(true);
        // act
        boolean result = dealStyleProcessor.isEnable(dealCtx);
        // assert
        assertFalse(result);
    }

    /**
     * 测试场景：ctx.getEnvCtx() 为 null
     * 预期结果：返回 false
     */
    @Test
    public void testIsEnableWhenEnvCtxIsNull() throws Throwable {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(null);
        // act
        boolean result = dealStyleProcessor.isEnable(dealCtx);
        // assert
        assertFalse(result);
    }

    /**
     * 测试当传入的 dealStyle 为 null 时，方法返回 null
     */
    @Test
    public void testTransferWhenDealStyleIsNull() throws Throwable {
        // arrange
        DealStyle dealStyle = null;
        // act
        DealStyle result = invokePrivateTransferMethod(dealStyle);
        // assert
        assertNull(result);
    }

    /**
     * 测试当传入的 dealStyle 不为 null 时，方法返回一个新的 DealStyle 对象，且 moduleKey 与传入的 dealStyle 的 moduleKey 相同
     */
    @Test
    public void testTransferWhenDealStyleIsNotNull() throws Throwable {
        // arrange
        DealStyle dealStyle = mock(DealStyle.class);
        when(dealStyle.getModuleKey()).thenReturn("testModuleKey");
        // act
        DealStyle result = invokePrivateTransferMethod(dealStyle);
        // assert
        assertNotNull(result);
        assertEquals("testModuleKey", result.getModuleKey());
    }

    /**
     * 测试当传入的 dealStyle 的 moduleKey 为 null 时，方法返回的 DealStyle 对象的 moduleKey 也为 null
     */
    @Test
    public void testTransferWhenDealStyleModuleKeyIsNull() throws Throwable {
        // arrange
        DealStyle dealStyle = mock(DealStyle.class);
        when(dealStyle.getModuleKey()).thenReturn(null);
        // act
        DealStyle result = invokePrivateTransferMethod(dealStyle);
        // assert
        assertNotNull(result);
        assertNull(result.getModuleKey());
    }

    /**
     * 测试当传入的 dealStyle 的 moduleKey 为空字符串时，方法返回的 DealStyle 对象的 moduleKey 也为空字符串
     */
    @Test
    public void testTransferWhenDealStyleModuleKeyIsEmpty() throws Throwable {
        // arrange
        DealStyle dealStyle = mock(DealStyle.class);
        when(dealStyle.getModuleKey()).thenReturn("");
        // act
        DealStyle result = invokePrivateTransferMethod(dealStyle);
        // assert
        assertNotNull(result);
        assertEquals("", result.getModuleKey());
    }

    /**
     * 测试当传入的 dealStyle 的 moduleKey 包含特殊字符时，方法返回的 DealStyle 对象的 moduleKey 也包含相同的特殊字符
     */
    @Test
    public void testTransferWhenDealStyleModuleKeyContainsSpecialCharacters() throws Throwable {
        // arrange
        DealStyle dealStyle = mock(DealStyle.class);
        when(dealStyle.getModuleKey()).thenReturn("test@#ModuleKey");
        // act
        DealStyle result = invokePrivateTransferMethod(dealStyle);
        // assert
        assertNotNull(result);
        assertEquals("test@#ModuleKey", result.getModuleKey());
    }

    /**
     * 测试当传入的 dealStyle 的 moduleKey 为长字符串时，方法返回的 DealStyle 对象的 moduleKey 也为相同的长字符串
     */
    @Test
    public void testTransferWhenDealStyleModuleKeyIsLongString() throws Throwable {
        // arrange
        DealStyle dealStyle = mock(DealStyle.class);
        // 生成一个长度为 1000 的字符串
        String longModuleKey = generateLongString(1000);
        when(dealStyle.getModuleKey()).thenReturn(longModuleKey);
        // act
        DealStyle result = invokePrivateTransferMethod(dealStyle);
        // assert
        assertNotNull(result);
        assertEquals(longModuleKey, result.getModuleKey());
    }
}
