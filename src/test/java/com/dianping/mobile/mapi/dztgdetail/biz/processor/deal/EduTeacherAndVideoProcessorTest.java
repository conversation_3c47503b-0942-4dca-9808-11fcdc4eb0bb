package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.common.constants.EduConstant;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.util.EduDealUtils;
import com.google.common.collect.Lists;
import com.sankuai.beautycontent.creator.application.edu.technicianVideo.facade.EduTechnicianVideoQueryFacade;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class EduTeacherAndVideoProcessorTest {

    @InjectMocks
    private EduTeacherAndVideoProcessor eduTeacherAndVideoProcessor;

    @Mock
    private EduTechnicianVideoQueryFacade eduTechnicianVideoQueryFacade;

    private MockedStatic<EduDealUtils> eduDealUtils;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private DealGroupDTO dealGroupDTO;

    @Mock
    private AttrDTO attrDTO;

    @Mock
    private FutureCtx futureCtx;

    @Before
    public void setUp() {
        eduDealUtils = Mockito.mockStatic(EduDealUtils.class);
    }


    @After
    public void tearDown() {
        eduDealUtils.close();
    }

    /**
     * 测试prepare方法，当dealGroupDTO为空时，方法应直接返回
     */
    @Test
    public void testPrepareWhenDealGroupDTOIsNull() {
        eduDealUtils.when(() ->EduDealUtils.isEduOnlineCourseDeal(dealCtx)).thenReturn(true);
        when(dealCtx.getDealGroupDTO()).thenReturn(null);

        eduTeacherAndVideoProcessor.prepare(dealCtx);

        verify(eduTechnicianVideoQueryFacade, never()).queryData(any());
    }

    /**
     * 测试prepare方法，当dealGroupDTO的attrs为空时，方法应直接返回
     */
    @Test
    public void testPrepareWhenAttrsIsEmpty() {
        eduDealUtils.when(() ->EduDealUtils.isEduOnlineCourseDeal(dealCtx)).thenReturn(true);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getAttrs()).thenReturn(new ArrayList<>());

        eduTeacherAndVideoProcessor.prepare(dealCtx);

        verify(eduTechnicianVideoQueryFacade, never()).queryData(any());
    }

    /**
     * 测试prepare方法，当courseTrialAttr为空时，方法应直接返回
     */
    @Test
    public void testPrepareWhenCourseTrialAttrIsNull() {
        eduDealUtils.when(() ->EduDealUtils.isEduOnlineCourseDeal(dealCtx)).thenReturn(true);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getAttrs()).thenReturn(Lists.newArrayList(attrDTO));
        when(attrDTO.getName()).thenReturn("other");

        eduTeacherAndVideoProcessor.prepare(dealCtx);

        verify(eduTechnicianVideoQueryFacade, never()).queryData(any());
    }

    /**
     * 测试prepare方法，当courseTrialAttr的value为空时，方法应直接返回
     */
    @Test
    public void testPrepareWhenCourseTrialAttrValueIsEmpty() {
        eduDealUtils.when(() ->EduDealUtils.isEduOnlineCourseDeal(dealCtx)).thenReturn(true);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getAttrs()).thenReturn(Lists.newArrayList(attrDTO));
        when(attrDTO.getName()).thenReturn("course_trial");
        when(attrDTO.getValue()).thenReturn(new ArrayList<>());

        eduTeacherAndVideoProcessor.prepare(dealCtx);

        verify(eduTechnicianVideoQueryFacade, never()).queryData(any());
    }

    /**
     * 测试prepare方法，当所有条件满足时，应调用eduTechnicianVideoQueryFacade的queryData方法
     */
    @Test
    public void testPrepareWhenAllConditionsAreMet() {
        eduDealUtils.when(() ->EduDealUtils.isEduOnlineCourseDeal(dealCtx)).thenReturn(true);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getAttrs()).thenReturn(Lists.newArrayList(attrDTO));
        when(attrDTO.getName()).thenReturn("course_trial");
        when(attrDTO.getValue()).thenReturn(Lists.newArrayList("[{\"teacher\":1, \"courseVideoId\":2}]"));

        when(eduTechnicianVideoQueryFacade.queryData(any())).thenReturn(null);
        when(dealCtx.getFutureCtx()).thenReturn(futureCtx);
        eduTeacherAndVideoProcessor.prepare(dealCtx);


        verify(eduTechnicianVideoQueryFacade, times(1)).queryData(any());
    }
}
