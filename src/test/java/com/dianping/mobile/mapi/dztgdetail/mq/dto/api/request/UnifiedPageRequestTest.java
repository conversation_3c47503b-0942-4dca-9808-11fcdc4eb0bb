package com.dianping.mobile.mapi.dztgdetail.mq.dto.api.request;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.exception.ProductDetailRequestIllegalException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedPageRequestTest {

    /**
     * 测试 getClientTypeEnum 方法，当 clientTypeEnum 不为 null 时
     */
    @Test
    public void testGetClientTypeEnumWhenClientTypeEnumIsNotNull() throws Throwable {
        // arrange
        UnifiedPageRequest unifiedPageRequest = new UnifiedPageRequest();
        unifiedPageRequest.setClientTypeEnum(ClientTypeEnum.DP_APP);
        // act
        ClientTypeEnum result = unifiedPageRequest.getClientTypeEnum();
        // assert
        assertEquals(ClientTypeEnum.DP_APP, result);
    }

    /**
     * 测试 getClientTypeEnum 方法，当 clientTypeEnum 为 null 时
     */
    @Test
    public void testGetClientTypeEnumWhenClientTypeEnumIsNull() throws Throwable {
        // arrange
        UnifiedPageRequest unifiedPageRequest = new UnifiedPageRequest();
        unifiedPageRequest.setClientType(ClientTypeEnum.DP_APP.getCode());
        // act
        ClientTypeEnum result = unifiedPageRequest.getClientTypeEnum();
        // assert
        assertEquals(ClientTypeEnum.DP_APP, result);
    }

    /**
     * 测试所有参数都合法的情况
     */
    @Test
    public void testCheckParamAllValid() throws Throwable {
        // arrange
        UnifiedPageRequest request = new UnifiedPageRequest();
        ShepherdGatewayParam mockParam = mock(ShepherdGatewayParam.class);
        request.setShepherdGatewayParam(mockParam);
        request.setProductId(1L);
        request.setProductType(ProductTypeEnum.DEAL.getCode());
        // act
        request.checkParam();
        // assert
        assertNotNull("shepherdGatewayParam不应为null", request.getShepherdGatewayParam());
        assertTrue("productId应大于0", request.getProductId() > 0);
        assertTrue("productType应为有效值", ProductTypeEnum.containsCode(request.getProductType()));
        assertEquals("productType应为DEAL", ProductTypeEnum.DEAL.getCode(), request.getProductType());
    }

    /**
     * 测试productType为RESERVE的情况
     */
    @Test
    public void testCheckParamWhenProductTypeIsReserve() throws Throwable {
        // arrange
        UnifiedPageRequest request = new UnifiedPageRequest();
        ShepherdGatewayParam mockParam = mock(ShepherdGatewayParam.class);
        request.setShepherdGatewayParam(mockParam);
        request.setProductId(1L);
        request.setProductType(ProductTypeEnum.RESERVE.getCode());
        // act
        request.checkParam();
        // assert
        assertNotNull("shepherdGatewayParam不应为null", request.getShepherdGatewayParam());
        assertTrue("productId应大于0", request.getProductId() > 0);
        assertTrue("productType应为有效值", ProductTypeEnum.containsCode(request.getProductType()));
        assertEquals("productType应为RESERVE", ProductTypeEnum.RESERVE.getCode(), request.getProductType());
    }

    /**
     * 测试shepherdGatewayParam为null的情况
     */
    @Test(expected = ProductDetailRequestIllegalException.class)
    public void testCheckParamWhenShepherdGatewayParamIsNull() throws Throwable {
        // arrange
        UnifiedPageRequest request = new UnifiedPageRequest();
        request.setShepherdGatewayParam(null);
        request.setProductId(1L);
        request.setProductType(ProductTypeEnum.DEAL.getCode());
        // act
        request.checkParam();
        // assert
        // 预期抛出异常
    }

    /**
     * 测试productId为0的情况
     */
    @Test(expected = ProductDetailRequestIllegalException.class)
    public void testCheckParamWhenProductIdIsZero() throws Throwable {
        // arrange
        UnifiedPageRequest request = new UnifiedPageRequest();
        request.setShepherdGatewayParam(mock(ShepherdGatewayParam.class));
        request.setProductId(0L);
        request.setProductType(ProductTypeEnum.DEAL.getCode());
        // act
        request.checkParam();
        // assert
        // 预期抛出异常
    }

    /**
     * 测试productId为负数的情况
     */
    @Test(expected = ProductDetailRequestIllegalException.class)
    public void testCheckParamWhenProductIdIsNegative() throws Throwable {
        // arrange
        UnifiedPageRequest request = new UnifiedPageRequest();
        request.setShepherdGatewayParam(mock(ShepherdGatewayParam.class));
        request.setProductId(-1L);
        request.setProductType(ProductTypeEnum.DEAL.getCode());
        // act
        request.checkParam();
        // assert
        // 预期抛出异常
    }

    /**
     * 测试productType无效的情况
     */
    @Test(expected = ProductDetailRequestIllegalException.class)
    public void testCheckParamWhenProductTypeIsInvalid() throws Throwable {
        // arrange
        UnifiedPageRequest request = new UnifiedPageRequest();
        request.setShepherdGatewayParam(mock(ShepherdGatewayParam.class));
        request.setProductId(1L);
        // 无效的productType
        request.setProductType(999);
        // act
        request.checkParam();
        // assert
        // 预期抛出异常
    }

    /**
     * 测试所有参数都无效的情况
     */
    @Test(expected = ProductDetailRequestIllegalException.class)
    public void testCheckParamWhenAllInvalid() throws Throwable {
        // arrange
        UnifiedPageRequest request = new UnifiedPageRequest();
        request.setShepherdGatewayParam(null);
        request.setProductId(0L);
        request.setProductType(999);
        // act
        request.checkParam();
        // assert
        // 预期抛出异常（会首先抛出shepherdGatewayParam为null的异常）
    }
}
