package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.ShopReviewCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.ReviewDetailDO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.ReviewPicDTO;
import com.dianping.tuangou.dztg.bjwrapper.api.dto.MtUserDto;
import com.dianping.ugc.review.remote.dto.MTReviewData;
import com.dianping.ugc.review.remote.dto.ReviewPic;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.dianping.mobile.mapi.dztgdetail.common.enums.PicVideoStatusEnum;
import org.mockito.Mock;
import org.mockito.InjectMocks;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ShopReviewHelper_MtReviewDataToReviewDetailDOTest {

    @Test
    public void testMtReviewDataToReviewDetailDO_MtReviewDataIsNull() throws Throwable {
        MTReviewData mtReviewData = null;
        MtUserDto userModel = mock(MtUserDto.class);
        List<ReviewPicDTO> defaultReviewList = new ArrayList<>();
        ShopReviewCtx shopReviewCtx = mock(ShopReviewCtx.class);
        ReviewDetailDO result = ShopReviewHelper.mtReviewDataToReviewDetailDO(mtReviewData, userModel, defaultReviewList, shopReviewCtx);
        assertNotNull(result);
    }

    @Test
    public void testMtReviewDataToReviewDetailDO_DefaultReviewListIsEmpty() throws Throwable {
        MTReviewData mtReviewData = mock(MTReviewData.class);
        MtUserDto userModel = mock(MtUserDto.class);
        List<ReviewPicDTO> defaultReviewList = new ArrayList<>();
        ShopReviewCtx shopReviewCtx = mock(ShopReviewCtx.class);
        ReviewDetailDO result = ShopReviewHelper.mtReviewDataToReviewDetailDO(mtReviewData, userModel, defaultReviewList, shopReviewCtx);
        assertNotNull(result);
    }

    @Test
    public void testMtReviewDataToReviewDetailDO_DefaultReviewListIsNotEmpty() throws Throwable {
        MTReviewData mtReviewData = mock(MTReviewData.class);
        MtUserDto userModel = mock(MtUserDto.class);
        List<ReviewPicDTO> defaultReviewList = new ArrayList<>();
        defaultReviewList.add(mock(ReviewPicDTO.class));
        ShopReviewCtx shopReviewCtx = mock(ShopReviewCtx.class);
        ReviewDetailDO result = ShopReviewHelper.mtReviewDataToReviewDetailDO(mtReviewData, userModel, defaultReviewList, shopReviewCtx);
        assertNotNull(result);
    }

    @Test
    public void testMtReviewDataToReviewDetailDO_ReviewPicsIsEmpty() throws Throwable {
        MTReviewData mtReviewData = mock(MTReviewData.class);
        when(mtReviewData.getReviewPics()).thenReturn(new ArrayList<>());
        MtUserDto userModel = mock(MtUserDto.class);
        List<ReviewPicDTO> defaultReviewList = new ArrayList<>();
        ShopReviewCtx shopReviewCtx = mock(ShopReviewCtx.class);
        ReviewDetailDO result = ShopReviewHelper.mtReviewDataToReviewDetailDO(mtReviewData, userModel, defaultReviewList, shopReviewCtx);
        assertNotNull(result);
    }

    @Test
    public void testMtReviewDataToReviewDetailDO_ReviewPicsIsNotEmpty() throws Throwable {
        MTReviewData mtReviewData = mock(MTReviewData.class);
        ReviewPic reviewPic = mock(ReviewPic.class);
        when(reviewPic.getStatus()).thenReturn(PicVideoStatusEnum.NORMAL.code);
        // Ensure getUrl() returns a non-null string
        when(reviewPic.getUrl()).thenReturn("http://example.com/pic/w.h");
        when(mtReviewData.getReviewPics()).thenReturn(Arrays.asList(reviewPic));
        MtUserDto userModel = mock(MtUserDto.class);
        List<ReviewPicDTO> defaultReviewList = new ArrayList<>();
        ShopReviewCtx shopReviewCtx = mock(ShopReviewCtx.class);
        ReviewDetailDO result = ShopReviewHelper.mtReviewDataToReviewDetailDO(mtReviewData, userModel, defaultReviewList, shopReviewCtx);
        assertNotNull(result);
    }
}
