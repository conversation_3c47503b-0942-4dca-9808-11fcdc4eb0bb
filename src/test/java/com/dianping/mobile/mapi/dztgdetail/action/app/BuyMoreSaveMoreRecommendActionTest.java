package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.BuyMoreSaveMoreReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.BuyMoreSaveMoreVO;
import com.dianping.mobile.mapi.dztgdetail.facade.BuyMoreSaveMoreFacade;
import com.dianping.mobile.mapi.dztgdetail.util.AntiCrawlerUtils;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.mockito.Mockito.*;
import org.junit.runner.RunWith;
import java.lang.reflect.Method;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.base.datatypes.SimpleMsg;
import static org.junit.Assert.*;
import org.junit.*;

public class BuyMoreSaveMoreRecommendActionTest {

    @InjectMocks
    private BuyMoreSaveMoreRecommendAction action;

    @Mock
    private BuyMoreSaveMoreFacade facade;

    @Mock
    private IMobileContext context;

    @Mock
    private BuyMoreSaveMoreReq request;

    private static final CommonMobileResponse NoDataResp = new CommonMobileResponse(null);

    private static final CommonMobileResponse SYSTEM_ERROR = new CommonMobileResponse(null);

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testExecuteException() throws Throwable {
        // arrange
        when(facade.getRecommendCombineDeal(eq(request), any())).thenThrow(new RuntimeException());
        // act
        IMobileResponse response = action.execute(request, context);
        // assert
        if (response instanceof SimpleMsg) {
            assertEquals(SYSTEM_ERROR.getData(), ((SimpleMsg) response).getData());
        }
    }

    /**
     * 测试未授权的登录
     */
    @Test(expected = Exception.class)
    public void testExecuteUnauthenticLogin() throws Throwable {
        // arrange
        try (MockedStatic<AntiCrawlerUtils> mocked = Mockito.mockStatic(AntiCrawlerUtils.class)) {
            mocked.when(() -> AntiCrawlerUtils.antiUnauthenticLogin(context)).thenThrow(new Exception());
            // act
            action.execute(request, context);
        }
    }
}
