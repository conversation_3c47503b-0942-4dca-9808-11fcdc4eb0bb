package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.voucher.query.api.dto.CustomerDTO;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.concurrent.Future;
import static org.mockito.ArgumentMatchers.anyInt;
import com.dianping.deal.voucher.query.api.DealGroupCustomerQueryService;
import static org.junit.Assert.*;
import org.junit.*;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DealGroupWrapper_GetCustomerIdTest {

    @InjectMocks
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private DealGroupCustomerQueryService dealGroupCustomerQueryService;

    @Mock
    private Future<CustomerDTO> futureMock;

    @Before
    public void setUp() throws Exception {
        // Ensure that the futureMock is properly set up to return a CustomerDTO when get() is called
        CustomerDTO customerDTO = new CustomerDTO();
        // Mock the queryDealGroupCustomer method to return a CustomerDTO directly
        when(dealGroupCustomerQueryService.queryDealGroupCustomer(anyInt())).thenReturn(customerDTO);
    }

    @Test
    public void testGetCustomerIdWhenFutureResultIsNull() throws Throwable {
        long result = dealGroupWrapper.getCustomerId(1);
        assertEquals(0, result);
    }

    @Test
    public void testGetCustomerIdWhenCustomerIdIsNull() throws Throwable {
        long result = dealGroupWrapper.getCustomerId(1);
        assertEquals(0, result);
    }
}
