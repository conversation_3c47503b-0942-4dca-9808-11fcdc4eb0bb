package com.dianping.mobile.mapi.dztgdetail.tab.postprocess;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.DealTab;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class CfgBasedRelatePostProcessorTagMappingTest {

    @Mock
    private DealTab dealTab;

    private CfgBasedRelatePostProcessor cfgBasedRelatePostProcessor;

    @Before
    public void setUp() {
        cfgBasedRelatePostProcessor = new CfgBasedRelatePostProcessor();
    }

    private void invokePrivateMethod(Object target, String methodName, Class<?>[] parameterTypes, Object... parameters) throws Exception {
        Method method = target.getClass().getDeclaredMethod(methodName, parameterTypes);
        method.setAccessible(true);
        method.invoke(target, parameters);
    }

    /**
     * 测试 tagMapping 方法，当 tagMapping 为空或者 tagMapping 中不存在 tag 对应的键值时
     */
    @Test
    public void testTagMappingWhenTagMappingIsNullOrNotContainsTag() throws Throwable {
        // arrange
        when(dealTab.getTag()).thenReturn("tag");
        Map<String, String> tagMapping = new HashMap<>();
        // act
        invokePrivateMethod(cfgBasedRelatePostProcessor, "tagMapping", new Class<?>[] { DealTab.class, Map.class }, dealTab, tagMapping);
        // assert
        verify(dealTab, times(1)).getTag();
        verifyNoMoreInteractions(dealTab);
    }

    /**
     * 测试 tagMapping 方法，当 tagMapping 非空且 tagMapping 中存在 tag 对应的键值时
     */
    @Test
    public void testTagMappingWhenTagMappingIsNotNullAndContainsTag() throws Throwable {
        // arrange
        when(dealTab.getTag()).thenReturn("tag");
        Map<String, String> tagMapping = new HashMap<>();
        tagMapping.put("tag", "newTag");
        // act
        invokePrivateMethod(cfgBasedRelatePostProcessor, "tagMapping", new Class<?>[] { DealTab.class, Map.class }, dealTab, tagMapping);
        // assert
        verify(dealTab, times(1)).getTag();
        verify(dealTab, times(1)).setTag("newTag");
    }

    /**
     * 测试 setModuleKey 方法，当 moduleKey 为空时
     */
    @Test
    public void testSetModuleKeyWhenModuleKeyIsNull() throws Throwable {
        // arrange
        CfgBasedRelatePostProcessor processor = new CfgBasedRelatePostProcessor();
        DealTab tab = mock(DealTab.class);
        String moduleKey = null;
        // Use reflection to invoke the private method
        Method setModuleKeyMethod = CfgBasedRelatePostProcessor.class.getDeclaredMethod("setModuleKey", DealTab.class, String.class);
        setModuleKeyMethod.setAccessible(true);
        // act
        setModuleKeyMethod.invoke(processor, tab, moduleKey);
        // assert
        verify(tab, times(0)).setModuleKey(anyString());
    }

    /**
     * 测试 setModuleKey 方法，当 moduleKey 不为空时
     */
    @Test
    public void testSetModuleKeyWhenModuleKeyIsNotNull() throws Throwable {
        // arrange
        CfgBasedRelatePostProcessor processor = new CfgBasedRelatePostProcessor();
        DealTab tab = mock(DealTab.class);
        String moduleKey = "testModuleKey";
        // Use reflection to invoke the private method
        Method setModuleKeyMethod = CfgBasedRelatePostProcessor.class.getDeclaredMethod("setModuleKey", DealTab.class, String.class);
        setModuleKeyMethod.setAccessible(true);
        // act
        setModuleKeyMethod.invoke(processor, tab, moduleKey);
        // assert
        verify(tab, times(1)).setModuleKey(moduleKey);
    }
}
