package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ServiceGuarantee.ServiceGuaranteeDTO;
import com.google.gson.JsonSyntaxException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ServiceGuaranteeQueryFacadeParseServiceGuaranteeDTOJsonMapTest {

    private ServiceGuaranteeQueryFacade serviceGuaranteeQueryFacade = new ServiceGuaranteeQueryFacade();

    /**
     * Tests the parseServiceGuaranteeDTOJsonMap method with a valid JSON string that can be correctly parsed into a Map.
     */
    @Test
    public void testParseServiceGuaranteeDTOJsonMapNormal() throws Throwable {
        // Arrange
        String json = "{\"1\":{\"serviceGuaranteeBanners\":[],\"serviceGuaranteeContentDetailDTOS\":[]}}";
        // Use reflection to access the private method
        Method method = ServiceGuaranteeQueryFacade.class.getDeclaredMethod("parseServiceGuaranteeDTOJsonMap", String.class);
        method.setAccessible(true);
        // Act
        Map<Integer, ServiceGuaranteeDTO> result = (Map<Integer, ServiceGuaranteeDTO>) method.invoke(serviceGuaranteeQueryFacade, json);
        // Assert
        assertNotNull(result);
        assertTrue(result.containsKey(1));
    }

    /**
     * Tests the parseServiceGuaranteeDTOJsonMap method with an invalid JSON string that cannot be parsed into a Map.
     */
    @Test
    public void testParseServiceGuaranteeDTOJsonMapException() throws Throwable {
        // Arrange
        String json = "invalid json";
        // Use reflection to access the private method
        Method method = ServiceGuaranteeQueryFacade.class.getDeclaredMethod("parseServiceGuaranteeDTOJsonMap", String.class);
        method.setAccessible(true);
        // Act
        try {
            method.invoke(serviceGuaranteeQueryFacade, json);
            fail("Expected an JsonSyntaxException to be thrown");
        } catch (InvocationTargetException e) {
            // Assert
            assertTrue(e.getTargetException() instanceof JsonSyntaxException);
        }
    }
}
