package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.impl;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryParam;
import com.dianping.mobile.mapi.dztgdetail.entity.ImageTextStrategyRule;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mockStatic;

@RunWith(MockitoJUnitRunner.class)
public class PhotoCategoryStrategyImplTest {

    @Mock
    private LionConfigUtils lionConfigUtils;

    @InjectMocks
    private PhotoCategoryStrategyImpl photoCategoryStrategyImpl;

    private MockedStatic<LionConfigUtils> lionConfigUtilsMockedStatic;
    @Before
    public void setUp() {
        lionConfigUtilsMockedStatic = mockStatic(LionConfigUtils.class);
    }

    @After
    public void tearDown() {
        lionConfigUtilsMockedStatic.close();
    }

    /**
     * 测试 dealCategoryParam 为 null 的情况
     */
    @Test
    public void testNewDealStyleWithNullParam() {
        // arrange
        DealCategoryParam dealCategoryParam = null;

        // act
        boolean result = photoCategoryStrategyImpl.newDealStyle(dealCategoryParam);

        // assert
        assertFalse(result);
    }

    /**
     * 测试 dealCategoryParam.getDealGroupDTO() 为 null 的情况
     */
    @Test
    public void testNewDealStyleWithNullDealGroupDTO() {
        // arrange
        DealCategoryParam dealCategoryParam = DealCategoryParam.builder()
                .dealGroupDTO(null)
                .build();

        // act
        boolean result = photoCategoryStrategyImpl.newDealStyle(dealCategoryParam);

        // assert
        assertFalse(result);
    }

    /**
     * 测试没有匹配规则的情况
     */
    @Test
    public void testNewDealStyleWithNoMatchingRule() {
        // arrange
        DealCategoryParam dealCategoryParam = TestHelper.createDealCategoryParam();
        lionConfigUtilsMockedStatic.when(LionConfigUtils::getImageTextStrategyRules)
                .thenReturn(Collections.emptyList());

        // act
        boolean result = photoCategoryStrategyImpl.newDealStyle(dealCategoryParam);

        // assert
        assertFalse(result);
    }

    /**
     * 测试有匹配规则的情况
     */
    @Test
    public void testNewDealStyleWithMatchingRule() {
        // arrange
        DealCategoryParam dealCategoryParam = TestHelper.createDealCategoryParam();
        ImageTextStrategyRule rule = new ImageTextStrategyRule();
        rule.setCategoryIds(Arrays.asList(dealCategoryParam.getDealCategoryId().intValue()));
        rule.setServiceTypes(Arrays.asList("1_按摩"));
        lionConfigUtilsMockedStatic.when(LionConfigUtils::getImageTextStrategyRules)
                .thenReturn(Collections.singletonList(rule));
        // act
        boolean result = photoCategoryStrategyImpl.newDealStyle(dealCategoryParam);

        // assert
        assertTrue(result);
    }

    // TestHelper 类用于创建测试中需要的对象实例
    private static class TestHelper {
        static DealCategoryParam createDealCategoryParam() {
            // 创建并返回 DealCategoryParam 实例
            return DealCategoryParam.builder()
                    .dealCategoryId(1L)
                    .dealGroupDTO(TestHelper.createDealGroupDTO())
                    .build();
        }

        static com.sankuai.general.product.query.center.client.dto.DealGroupDTO createDealGroupDTO() {
            // 创建并返回 DealGroupDTO 实例
            com.sankuai.general.product.query.center.client.dto.DealGroupDTO dealGroupDTO = new com.sankuai.general.product.query.center.client.dto.DealGroupDTO();
            com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO categoryDTO = new com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO();
            categoryDTO.setServiceTypeId(1L);
            categoryDTO.setServiceType("按摩");
            dealGroupDTO.setCategory(categoryDTO);
            return dealGroupDTO;
        }
    }
}
