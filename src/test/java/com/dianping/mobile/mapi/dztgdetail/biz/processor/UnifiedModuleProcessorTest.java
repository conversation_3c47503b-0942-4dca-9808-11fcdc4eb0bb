package com.dianping.mobile.mapi.dztgdetail.biz.processor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.UnifiedModuleProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.DealCategoryFactory;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryParam;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.service.PriceDisplayLocalService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2024/6/28
 * @since mapi-dztgdetail-web
 */
@RunWith(MockitoJUnitRunner.class)
public class UnifiedModuleProcessorTest {

    @InjectMocks
    UnifiedModuleProcessor unifiedModuleProcessor;

    @Mock
    private DouHuService douHuService;

    @Mock
    private DouHuBiz douHuBiz;

    @Mock
    private DealCategoryFactory dealCategoryFactory;

    private MockedStatic<LionConfigUtils> lionConfigUtilsMockedStatic;

    private MockedStatic<Lion> lionMockedStatic;

    @Before
    public void setUp() {
        lionConfigUtilsMockedStatic = mockStatic(LionConfigUtils.class);
        lionMockedStatic = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        lionConfigUtilsMockedStatic.close();
        lionMockedStatic.close();
    }

    @Test
    public void testCardStyleV2Metric() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_KUAISHOU_MINIAPP);
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(401);
        ctx.setChannelDTO(channelDTO);
        ctx.setChannelDTO(channelDTO);
        ctx.setExternal(true);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<AttrDTO> attrs= new ArrayList<>();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("physical_examination_report_type");
        attrDTO.setValue(Lists.newArrayList("mocktype"));
        attrs.add(attrDTO);
        dealGroupDTO.setAttrs(attrs);
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.setMrnVersion("0.5.1");
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        List<AbConfig> abConfigs = new ArrayList<>();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpId("exp001");
        abConfig.setExpResult("b");
        abConfigs.add(abConfig);
        moduleAbConfig.setConfigs(abConfigs);
        when(douHuService.enableCardStyleV2(Mockito.any(EnvCtx.class), Mockito.anyInt(), Mockito.anyString())).thenReturn(moduleAbConfig);
        doNothing().when(dealCategoryFactory).resetDealDetailModuleConfig(Mockito.any(DealCategoryParam.class));
        lionConfigUtilsMockedStatic.when(LionConfigUtils::getEduDealCategoryIds)
                        .thenReturn(Lists.newArrayList(401));
        lionMockedStatic.when(() -> Lion.getBoolean(anyString(), anyString()))
                        .thenReturn(true);
        unifiedModuleProcessor.process(ctx);
        unifiedModuleProcessor.buildKey(ctx, 401);
        List<ModuleConfigDo> result = Lists.newArrayList();
        ModuleConfigDo moduleConfigDo = new ModuleConfigDo();
        moduleConfigDo.setKey("cardStyleV2");
        moduleConfigDo.setValue("cardStyleV2");
        result.add(moduleConfigDo);
        unifiedModuleProcessor.resetConfigItemSort(result, Lists.newArrayList("cardStyleV2"));
        Assert.assertNotNull(ctx);
    }


    @Test
    public void testGetModuleConfigDoListForPhoto() {
        when(douHuService.hitEnableCardStyleV2(any())).thenReturn(true);
        String value = "[{\"categories\":\"mt1904,dp1904\",\"configs\":[{\"configKey\":\"服务详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"领取须知\",\"configValue\":\"gcdealdetail_newtuandealtab_photography_buyrules\"},{\"configKey\":\"适用门店\",\"configValue\":\"gcdealdetail_newtuandealtab_shopinfo\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]} ,{\"categories\":\"mt447,dp447\",\"configs\":[{\"configKey\":\"服务详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"领取须知\",\"configValue\":\"gcdealdetail_newtuandealtab_photography_buyrules\"},{\"configKey\":\"适用门店\",\"configValue\":\"gcdealdetail_newtuandealtab_shopinfo\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]} ,{\"categories\":\"mt504,mt910\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_photography_buyrules\"},{\"configKey\":\"适用门店\",\"configValue\":\"gcdealdetail_newtuandealtab_shopinfo\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"dp504,dp910\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_photography_buyrules\"},{\"configKey\":\"适用门店\",\"configValue\":\"tuandeal_newtuandealtab_shopinfo\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp501\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_beauty_hairwithtech_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_beauty_hair_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp501_old\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_beauty_hairwithtech_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_beauty_hair_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp516\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_beauty_hair_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp502\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp503\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_beauty_spa_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp1204\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_education_shengxue_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_education_shengxue_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp1004\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_baby_photoedutg_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_baby_photoedutg_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp405\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_flower_all_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp505,dp509,dp511,dp512,dp513,dp514,dp705\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_beauty_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp507\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_beauty_yoga_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp404,dp412,dp1201,dp1202,dp1203,dp1206,dp1207,dp1208,dp1209,dp1210,dp1211,dp1212,dp1213\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_education_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_education_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp1210_online\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_online_education_tuandetail\"},{\"configKey\":\"授课老师\",\"configValue\":\"tuandeal_newtuandealtab_online_education_teachers\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_education_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp301\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_ktv_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp403,dp407,dp437,dp409,dp413,dp414,dp415,dp417,dp448,dp436,dp436,dp445,dp452,dp450\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_easylife_structure_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_easylife_structure_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp401\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_medicinehealthcheck_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp506\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_dentis_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_dentis_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp453\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_tuandetail_xlzx\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"mt501\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_beauty_hairwithtech_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_beauty_hair_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt501_old\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_beauty_hairwithtech_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_beauty_hair_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt516\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_beauty_hair_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt502\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_beauty_nail_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt503\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_beauty_spa_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt1004\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_children_photoedutg_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_children_photoedutg_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt405\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_flower_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt505,mt509,mt511,mt512,mt513,mt514,mt705\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_beauty_default_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt507\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_beauty_yoga_default_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt404,mt412,mt1201,mt1202,mt1203,mt1206,mt1207,mt1208,mt1209,mt1210,mt1211,mt1212,mt1213\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_education_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt1210_online\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_online_education_tuandetail\"},{\"configKey\":\"授课老师\",\"configValue\":\"gcdealdetail_newtuandealtab_online_education_teachers\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_education_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt301\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_ktv_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt1204\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_education_shengxue_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt401\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_medicinehealthcheck_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_medicine_healthcheck_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt506\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_dentis_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_dentis_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt448\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_easylife_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_easylife_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt1701\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_pet_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_pet_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt453\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail_xlzx\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"dp1701\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_pet_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_pet_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"mt322\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_game_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"dp312\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"mt312\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"dp1702,dp1704\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_pet_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"mt1702,mt1704\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_pet_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"mt401_new\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_medicinehealthcheck_itemsdetail\"},{\"configKey\":\"服务流程\",\"configValue\":\"gcdealdetail_newtuandealtab_medicinehealthcheck_service_process\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_medicinehealthcheck_reviews\"}]},{\"categories\":\"dp401_new\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_medicinehealthcheck_itemsdetail\"},{\"configKey\":\"服务流程\",\"configValue\":\"tuandeal_newtuandealtab_medicinehealthcheck_service_process\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_medicinehealthcheck_reviews\"}]},{\"categories\":\"mt401_standard\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_medicinehealthcheck_newitemsdetail\"},{\"configKey\":\"服务流程\",\"configValue\":\"gcdealdetail_newtuandealtab_medicinehealthcheck_service_process\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_medicinehealthcheck_reviews\"}]},{\"categories\":\"dp401_standard\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_medicinehealthcheck_newitemsdetail\"},{\"configKey\":\"服务流程\",\"configValue\":\"tuandeal_newtuandealtab_medicinehealthcheck_service_process\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_medicinehealthcheck_reviews\"}]},{\"categories\":\"mt1604_standard\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_medicine_eye_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"dp1604_standard\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_medicine_eye_newtuandeal\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"dp712\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_mall_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"}]},{\"categories\":\"mt712\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_mall_buyrules\"},{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"}]},{\"categories\":\"dp414_wuyoutong\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_tuandetail\"},{\"configKey\":\"评价模块\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"},{\"configKey\":\"常见问题\",\"configValue\":\"tuandeal_newtuandealtab_qa\"}]},{\"categories\":\"mt414_wuyoutong\",\"configs\":[{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"评价模块\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"},{\"configKey\":\"常见问题\",\"configValue\":\"gcdealdetail_newtuandealtab_qa\"}]},{\"categories\":\"dp2001,dp2002,dp2003,dp2004,dp2005,dp2006,dp2007,dp1004,dp504\",\"configs\":[{\"configKey\":\"网友评价\",\"configValue\":\"tuandeal_newtuandealtab_reviews\"},{\"configKey\":\"团购详情\",\"configValue\":\"tuandeal_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"tuandeal_newtuandealtab_buyrules\"}]},{\"categories\":\"mt2001,mt2002,mt2003,mt2004,mt2005,mt2006,mt2007,mt1004,mt504\",\"configs\":[{\"configKey\":\"网友评价\",\"configValue\":\"gcdealdetail_newtuandealtab_reviews\"},{\"configKey\":\"团购详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"购买须知\",\"configValue\":\"gcdealdetail_newtuandealtab_buyrules\"}]},{\"categories\":\"dp1611_resv\",\"configs\":[{\"configKey\":\"疫苗详情\",\"configValue\":\"tuandeal_newtuandealtab_tuandetail\"},{\"configKey\":\"预约须知\",\"configValue\":\"tuandeal_newtuandealtab_buyrules\"}]},{\"categories\":\"mt1611_resv\",\"configs\":[{\"configKey\":\"疫苗详情\",\"configValue\":\"gcdealdetail_newtuandealtab_tuandetail\"},{\"configKey\":\"预约须知\",\"configValue\":\"gcdealdetail_newtuandealtab_buyrules\"}]}]";
        lionMockedStatic.when(() -> Lion.getStringValue(any())).thenReturn(value);
        List<String> catList = Lists.newArrayList("[\"景点跟拍\"]");
        lionMockedStatic.when(() -> Lion.getList(any())).thenReturn(catList);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        envCtx.setClientType(1);
        DealCtx ctx = new DealCtx(envCtx);
        String moduleAbConfigsJson = "[{\"key\":\"DPPhotoDealDetailExp\",\"configs\":[{\"expId\":\"EXP2024092000001\",\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"3778410d-e0a5-47a0-b3f8-849a78c92228\\\",\\\"ab_id\\\":\\\"EXP2024092000001_b\\\"}\"}]}]";
        List<ModuleAbConfig> moduleAbConfigs = JSON.parseObject(moduleAbConfigsJson, new TypeReference<List<ModuleAbConfig>>() {});
        ctx.setModuleAbConfigs(moduleAbConfigs);
        List<ModuleConfigDo> moduleConfigDoList =
                unifiedModuleProcessor.getModuleConfigDoList(ctx,910, false,
                        false, true, false, "景点跟拍");
        assertNotNull(moduleConfigDoList);
    }

    @Test
    public void testIsStandardEyeDealGroup() throws InvocationTargetException, IllegalAccessException {
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
        dealGroupCategoryDTO.setCategoryId(123L);
        dealGroupDTO.setCategory(dealGroupCategoryDTO);

        Method method = PowerMockito.method(UnifiedModuleProcessor.class, "isStandardEyeDealGroup");
        boolean result = (boolean)method.invoke(unifiedModuleProcessor, ctx, dealGroupDTO);
        assert !result;
    }

}
