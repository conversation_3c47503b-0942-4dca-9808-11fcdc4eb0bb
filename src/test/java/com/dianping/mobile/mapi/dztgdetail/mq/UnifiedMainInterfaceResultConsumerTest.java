package com.dianping.mobile.mapi.dztgdetail.mq;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.enums.MobileOSTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedMainInterfaceResultConsumerTest {

    @Mock
    private ProductDetailPageRequest mockRequest;

    @Mock
    private ShepherdGatewayParam mockGatewayParam;

    private UnifiedMainInterfaceResultConsumer consumer = new UnifiedMainInterfaceResultConsumer();

    /**
     * Test when bottomBarModule is null
     */
    @Test
    public void testGetTradeBottomBarUrl_NullBottomBarModule() throws Throwable {
        // arrange
        UnifiedMainInterfaceResultConsumer consumer = new UnifiedMainInterfaceResultConsumer();
        // act
        String result = consumer.getTradeBottomBarUrl(null);
        // assert
        assertNull(result);
    }

    /**
     * Test when rightBottomBar is missing
     */
    @Test
    public void testGetTradeBottomBarUrl_MissingRightBottomBar() throws Throwable {
        // arrange
        UnifiedMainInterfaceResultConsumer consumer = new UnifiedMainInterfaceResultConsumer();
        JSONObject bottomBarModule = new JSONObject();
        // act
        String result = consumer.getTradeBottomBarUrl(bottomBarModule);
        // assert
        assertNull(result);
    }

    /**
     * Test when buttonList is empty
     */
    @Test
    public void testGetTradeBottomBarUrl_EmptyButtonList() throws Throwable {
        // arrange
        UnifiedMainInterfaceResultConsumer consumer = new UnifiedMainInterfaceResultConsumer();
        JSONObject bottomBarModule = new JSONObject();
        JSONObject rightBottomBar = new JSONObject();
        rightBottomBar.put("buttonList", new JSONArray(Collections.emptyList()));
        bottomBarModule.put("rightBottomBar", rightBottomBar);
        // act
        String result = consumer.getTradeBottomBarUrl(bottomBarModule);
        // assert
        assertNull(result);
    }

    /**
     * Test when buttonList has no matching button
     */
    @Test
    public void testGetTradeBottomBarUrl_NoMatchingButton() throws Throwable {
        // arrange
        UnifiedMainInterfaceResultConsumer consumer = new UnifiedMainInterfaceResultConsumer();
        JSONObject bottomBarModule = new JSONObject();
        JSONObject rightBottomBar = new JSONObject();
        JSONArray buttonList = new JSONArray();
        JSONObject button1 = new JSONObject();
        JSONObject actionData1 = new JSONObject();
        // Not matching type
        actionData1.put("actionType", 1);
        button1.put("actionData", actionData1);
        buttonList.add(button1);
        rightBottomBar.put("buttonList", buttonList);
        bottomBarModule.put("rightBottomBar", rightBottomBar);
        // act
        String result = consumer.getTradeBottomBarUrl(bottomBarModule);
        // assert
        assertNull(result);
    }

    /**
     * Test when buttonList has a matching button
     */
    @Test
    public void testGetTradeBottomBarUrl_WithMatchingButton() throws Throwable {
        // arrange
        UnifiedMainInterfaceResultConsumer consumer = new UnifiedMainInterfaceResultConsumer();
        JSONObject bottomBarModule = new JSONObject();
        JSONObject rightBottomBar = new JSONObject();
        JSONArray buttonList = new JSONArray();
        JSONObject button1 = new JSONObject();
        JSONObject actionData1 = new JSONObject();
        // Matching type
        actionData1.put("actionType", 2);
        actionData1.put("url", "test-url");
        button1.put("actionData", actionData1);
        buttonList.add(button1);
        rightBottomBar.put("buttonList", buttonList);
        bottomBarModule.put("rightBottomBar", rightBottomBar);
        // act
        String result = consumer.getTradeBottomBarUrl(bottomBarModule);
        // assert
        assertEquals("test-url", result);
    }

    /**
     * Test when buttonList has multiple buttons with one matching
     */
    @Test
    public void testGetTradeBottomBarUrl_MultipleButtonsWithOneMatch() throws Throwable {
        // arrange
        UnifiedMainInterfaceResultConsumer consumer = new UnifiedMainInterfaceResultConsumer();
        JSONObject bottomBarModule = new JSONObject();
        JSONObject rightBottomBar = new JSONObject();
        JSONArray buttonList = new JSONArray();
        // Non-matching button
        JSONObject button1 = new JSONObject();
        JSONObject actionData1 = new JSONObject();
        actionData1.put("actionType", 1);
        button1.put("actionData", actionData1);
        // Matching button
        JSONObject button2 = new JSONObject();
        JSONObject actionData2 = new JSONObject();
        // Matching type
        actionData2.put("actionType", 2);
        actionData2.put("url", "test-url");
        button2.put("actionData", actionData2);
        buttonList.add(button1);
        buttonList.add(button2);
        rightBottomBar.put("buttonList", buttonList);
        bottomBarModule.put("rightBottomBar", rightBottomBar);
        // act
        String result = consumer.getTradeBottomBarUrl(bottomBarModule);
        // assert
        assertEquals("test-url", result);
    }

    /**
     * 测试dealBuyBar为null时返回null
     */
    @Test
    public void testGetBuyBarRedirectUrlWhenDealBuyBarIsNull() {
        // arrange
        UnifiedMainInterfaceResultConsumer consumer = new UnifiedMainInterfaceResultConsumer();
        // act
        String result = consumer.getBuyBarRedirectUrl(null);
        // assert
        assertNull(result);
    }

    /**
     * 测试dealBuyBar不为null但buyBtns为null时返回null
     */
    @Test
    public void testGetBuyBarRedirectUrlWhenBuyBtnsIsNull() {
        // arrange
        UnifiedMainInterfaceResultConsumer consumer = new UnifiedMainInterfaceResultConsumer();
        JSONObject dealBuyBar = mock(JSONObject.class);
        when(dealBuyBar.getJSONArray("buyBtns")).thenReturn(null);
        // act
        String result = consumer.getBuyBarRedirectUrl(dealBuyBar);
        // assert
        assertNull(result);
    }

    /**
     * 测试dealBuyBar不为null但buyBtns为空数组时返回null
     */
    @Test
    public void testGetBuyBarRedirectUrlWhenBuyBtnsIsEmpty() {
        // arrange
        UnifiedMainInterfaceResultConsumer consumer = new UnifiedMainInterfaceResultConsumer();
        JSONObject dealBuyBar = mock(JSONObject.class);
        JSONArray buyBtns = new JSONArray();
        when(dealBuyBar.getJSONArray("buyBtns")).thenReturn(buyBtns);
        // act
        String result = consumer.getBuyBarRedirectUrl(dealBuyBar);
        // assert
        assertNull(result);
    }

    /**
     * 测试dealBuyBar不为null且buyBtns有元素但第一个元素没有redirectUrl字段时返回null
     */
    @Test
    public void testGetBuyBarRedirectUrlWhenFirstBtnHasNoRedirectUrl() {
        // arrange
        UnifiedMainInterfaceResultConsumer consumer = new UnifiedMainInterfaceResultConsumer();
        JSONObject dealBuyBar = mock(JSONObject.class);
        JSONArray buyBtns = new JSONArray();
        JSONObject buyBtn = mock(JSONObject.class);
        buyBtns.add(buyBtn);
        when(dealBuyBar.getJSONArray("buyBtns")).thenReturn(buyBtns);
        when(buyBtn.getString("redirectUrl")).thenReturn(null);
        // act
        String result = consumer.getBuyBarRedirectUrl(dealBuyBar);
        // assert
        assertNull(result);
    }

    /**
     * 测试dealBuyBar不为null且buyBtns有元素且第一个元素有redirectUrl字段时返回正确url
     */
    @Test
    public void testGetBuyBarRedirectUrlWhenFirstBtnHasRedirectUrl() {
        // arrange
        UnifiedMainInterfaceResultConsumer consumer = new UnifiedMainInterfaceResultConsumer();
        JSONObject dealBuyBar = mock(JSONObject.class);
        JSONArray buyBtns = new JSONArray();
        JSONObject buyBtn = mock(JSONObject.class);
        buyBtns.add(buyBtn);
        when(dealBuyBar.getJSONArray("buyBtns")).thenReturn(buyBtns);
        when(buyBtn.getString("redirectUrl")).thenReturn("http://example.com");
        // act
        String result = consumer.getBuyBarRedirectUrl(dealBuyBar);
        // assert
        assertEquals("http://example.com", result);
    }

    /**
     * 测试buyBtns有元素但第一个元素不是JSONObject时抛出异常
     */
    @Test(expected = ClassCastException.class)
    public void testGetBuyBarRedirectUrlWhenFirstElementIsNotJsonObject() {
        // arrange
        UnifiedMainInterfaceResultConsumer consumer = new UnifiedMainInterfaceResultConsumer();
        JSONObject dealBuyBar = mock(JSONObject.class);
        JSONArray buyBtns = new JSONArray();
        // 添加字符串而不是JSONObject
        buyBtns.add("invalid");
        when(dealBuyBar.getJSONArray("buyBtns")).thenReturn(buyBtns);
        // act
        consumer.getBuyBarRedirectUrl(dealBuyBar);
        // assert - 预期抛出ClassCastException
    }

    /**
     * Test MT client type with iOS mobile OS
     */
    @Test
    public void testGetAppClientType_MtClientIos() throws Throwable {
        // arrange
        when(mockRequest.getClientTypeEnum()).thenReturn(com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.MT_APP);
        when(mockRequest.getShepherdGatewayParam()).thenReturn(mockGatewayParam);
        when(mockGatewayParam.getMobileOSType()).thenReturn("ios");
        // act
        int result = UnifiedMainInterfaceResultConsumer.getAppClientType(mockRequest);
        // assert
        // ClientTypeEnum.mt_mainApp_ios.getType()
        assertEquals(200502, result);
    }

    /**
     * Test MT client type with Android mobile OS
     */
    @Test
    public void testGetAppClientType_MtClientAndroid() throws Throwable {
        // arrange
        when(mockRequest.getClientTypeEnum()).thenReturn(com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.MT_APP);
        when(mockRequest.getShepherdGatewayParam()).thenReturn(mockGatewayParam);
        when(mockGatewayParam.getMobileOSType()).thenReturn("android");
        // act
        int result = UnifiedMainInterfaceResultConsumer.getAppClientType(mockRequest);
        // assert
        // ClientTypeEnum.mt_mainApp_android.getType()
        assertEquals(200501, result);
    }

    /**
     * Test DP client type with iOS mobile OS
     */
    @Test
    public void testGetAppClientType_DpClientIos() throws Throwable {
        // arrange
        when(mockRequest.getClientTypeEnum()).thenReturn(com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.DP_APP);
        when(mockRequest.getShepherdGatewayParam()).thenReturn(mockGatewayParam);
        when(mockGatewayParam.getMobileOSType()).thenReturn("ios");
        // act
        int result = UnifiedMainInterfaceResultConsumer.getAppClientType(mockRequest);
        // assert
        // ClientTypeEnum.dp_mainApp_ios.getType()
        assertEquals(100502, result);
    }

    /**
     * Test DP client type with Android mobile OS
     */
    @Test
    public void testGetAppClientType_DpClientAndroid() throws Throwable {
        // arrange
        when(mockRequest.getClientTypeEnum()).thenReturn(com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.DP_APP);
        when(mockRequest.getShepherdGatewayParam()).thenReturn(mockGatewayParam);
        when(mockGatewayParam.getMobileOSType()).thenReturn("android");
        // act
        int result = UnifiedMainInterfaceResultConsumer.getAppClientType(mockRequest);
        // assert
        // ClientTypeEnum.dp_mainApp_android.getType()
        assertEquals(100501, result);
    }

    /**
     * Test with null request
     */
    @Test(expected = NullPointerException.class)
    public void testGetAppClientType_NullRequest() throws Throwable {
        // arrange
        ProductDetailPageRequest nullRequest = null;
        // act
        UnifiedMainInterfaceResultConsumer.getAppClientType(nullRequest);
        // assert - exception expected
    }

    /**
     * Test with null ShepherdGatewayParam
     */
    @Test(expected = NullPointerException.class)
    public void testGetAppClientType_NullGatewayParam() throws Throwable {
        // arrange
        when(mockRequest.getShepherdGatewayParam()).thenReturn(null);
        // act
        UnifiedMainInterfaceResultConsumer.getAppClientType(mockRequest);
        // assert - exception expected
    }

    /**
     * Test with null mobile OS type
     */
    @Test
    public void testGetAppClientType_NullMobileOsType() throws Throwable {
        // arrange
        when(mockRequest.getClientTypeEnum()).thenReturn(com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.DP_APP);
        when(mockRequest.getShepherdGatewayParam()).thenReturn(mockGatewayParam);
        when(mockGatewayParam.getMobileOSType()).thenReturn(null);
        // act
        int result = UnifiedMainInterfaceResultConsumer.getAppClientType(mockRequest);
        // assert - should default to Android
        // ClientTypeEnum.dp_mainApp_android.getType()
        assertEquals(100501, result);
    }

    /**
     * Test with empty mobile OS type
     */
    @Test
    public void testGetAppClientType_EmptyMobileOsType() throws Throwable {
        // arrange
        when(mockRequest.getClientTypeEnum()).thenReturn(com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.MT_APP);
        when(mockRequest.getShepherdGatewayParam()).thenReturn(mockGatewayParam);
        when(mockGatewayParam.getMobileOSType()).thenReturn("");
        // act
        int result = UnifiedMainInterfaceResultConsumer.getAppClientType(mockRequest);
        // assert - should default to Android
        // ClientTypeEnum.mt_mainApp_android.getType()
        assertEquals(200501, result);
    }

    /**
     * Test with unknown mobile OS type
     */
    @Test
    public void testGetAppClientType_UnknownMobileOsType() throws Throwable {
        // arrange
        when(mockRequest.getClientTypeEnum()).thenReturn(com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.MT_APP);
        when(mockRequest.getShepherdGatewayParam()).thenReturn(mockGatewayParam);
        when(mockGatewayParam.getMobileOSType()).thenReturn("windows");
        // act
        int result = UnifiedMainInterfaceResultConsumer.getAppClientType(mockRequest);
        // assert - should default to Android
        // ClientTypeEnum.mt_mainApp_android.getType()
        assertEquals(200501, result);
    }

    /**
     * 测试button为null时返回false
     */
    @Test
    public void testIsBuyBarBottomWhenButtonIsNull() throws Throwable {
        // arrange
        JSONObject button = null;
        // act
        boolean result = consumer.isBuyBarBottom(button);
        // assert
        assertFalse(result);
    }

    /**
     * 测试actionType等于2时返回true
     */
    @Test
    public void testIsBuyBarBottomWhenActionTypeEquals2() throws Throwable {
        // arrange
        JSONObject button = mock(JSONObject.class);
        JSONObject actionData = mock(JSONObject.class);
        when(button.getJSONObject("actionData")).thenReturn(actionData);
        when(actionData.getIntValue("actionType")).thenReturn(2);
        // act
        boolean result = consumer.isBuyBarBottom(button);
        // assert
        assertTrue(result);
    }

    /**
     * 测试actionType不等于2时返回false
     */
    @Test
    public void testIsBuyBarBottomWhenActionTypeNotEquals2() throws Throwable {
        // arrange
        JSONObject button = mock(JSONObject.class);
        JSONObject actionData = mock(JSONObject.class);
        when(button.getJSONObject("actionData")).thenReturn(actionData);
        when(actionData.getIntValue("actionType")).thenReturn(1);
        // act
        boolean result = consumer.isBuyBarBottom(button);
        // assert
        assertFalse(result);
    }

    /**
     * 测试actionData中actionType字段不存在时返回false
     */
    @Test
    public void testIsBuyBarBottomWhenActionTypeNotExist() throws Throwable {
        // arrange
        JSONObject button = mock(JSONObject.class);
        JSONObject actionData = mock(JSONObject.class);
        when(button.getJSONObject("actionData")).thenReturn(actionData);
        when(actionData.getIntValue("actionType")).thenReturn(0);
        // act
        boolean result = consumer.isBuyBarBottom(button);
        // assert
        assertFalse(result);
    }
}
