package com.dianping.mobile.mapi.dztgdetail.biz.service.recommend;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.entity.Response;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class AbsShopRecommendHandlerTest {

    @Mock
    private AbsShopRecommendHandler<String, String> handler;

    @Test
    public void testDoExecuteValidateFail() throws Throwable {
        String result = handler.doExecute("test");
        assertNull(result);
    }

    @Test
    public void testDoExecuteInitContextReturnNull() throws Throwable {
        String result = handler.doExecute("test");
        assertNull(result);
    }

    @Test
    public void testDoExecuteGetResultReturnNull() throws Throwable {
        String result = handler.doExecute("test");
        assertNull(result);
    }

    @Test
    public void testDoExecuteFillReturnNull() throws Throwable {
        String result = handler.doExecute("test");
        assertNull(result);
    }
}
