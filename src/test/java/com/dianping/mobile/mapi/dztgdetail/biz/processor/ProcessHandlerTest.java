package com.dianping.mobile.mapi.dztgdetail.biz.processor;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ProcessHandlerTest {

    @Mock
    private Processor processor;

    @Mock
    private ProcessHandler<Object> mockProcessHandler;

    private void invokeDoPrepare(ProcessHandler handler, Object ctx, Consumer<Processor> consumer) throws Exception {
        Method method = ProcessHandler.class.getDeclaredMethod("doPrepare", Object.class, Consumer.class);
        method.setAccessible(true);
        method.invoke(handler, ctx, consumer);
    }

    private void invokePrivateDoProcess(ProcessHandler handler, Object ctx, Consumer<Processor> consumer) throws Exception {
        Method method = ProcessHandler.class.getDeclaredMethod("doProcess", Object.class, Consumer.class);
        method.setAccessible(true);
        method.invoke(handler, ctx, consumer);
    }

    @Test
    public void testDoPrepareProcessorListIsEmpty() throws Throwable {
        ProcessHandler handler = new ProcessHandler();
        handler.setProcessorList(Collections.emptyList());
        invokeDoPrepare(handler, new Object(), (p) -> {
        });
        verify(processor, never()).isEnd(any());
    }

    @Test
    public void testDoPrepareProcessorListIsNotEmptyButAllProcessorsEnd() throws Throwable {
        ProcessHandler handler = new ProcessHandler();
        handler.setProcessorList(Arrays.asList(processor));
        when(processor.isEnd(any())).thenReturn(true);
        invokeDoPrepare(handler, new Object(), (p) -> {
        });
        verify(processor, times(1)).isEnd(any());
    }

    @Test
    public void testDoPrepareProcessorListIsNotEmptyAndSomeProcessorsEnd() throws Throwable {
        ProcessHandler handler = new ProcessHandler();
        handler.setProcessorList(Arrays.asList(processor));
        when(processor.isEnd(any())).thenReturn(false);
        invokeDoPrepare(handler, new Object(), (p) -> {
        });
        verify(processor, times(1)).isEnd(any());
    }

    @Test
    public void testDoPrepareProcessorListIsNotEmptyAndNoProcessorEndsButSomeAreEnabledAndMatch() throws Throwable {
        ProcessHandler handler = new ProcessHandler();
        handler.setProcessorList(Arrays.asList(processor));
        when(processor.isEnd(any())).thenReturn(false);
        when(processor.isEnable(any())).thenReturn(true);
        when(processor.matchDztgClient(any(), any())).thenReturn(true);
        invokeDoPrepare(handler, new Object(), (p) -> {
        });
        verify(processor, times(1)).isEnd(any());
    }

    @Test
    public void testDoPrepareProcessorListIsNotEmptyAndNoProcessorEndsAndNoProcessorIsEnabledAndMatch() throws Throwable {
        ProcessHandler handler = new ProcessHandler();
        handler.setProcessorList(Arrays.asList(processor));
        when(processor.isEnd(any())).thenReturn(false);
        when(processor.isEnable(any())).thenReturn(false);
        invokeDoPrepare(handler, new Object(), (p) -> {
        });
        verify(processor, times(1)).isEnd(any());
    }

    @Test
    public void testDoPrepareProcessorListIsNotEmptyAndProcessorThrowsException() throws Throwable {
        ProcessHandler handler = new ProcessHandler();
        handler.setProcessorList(Arrays.asList(processor));
        when(processor.isEnd(any())).thenReturn(false);
        doThrow(new RuntimeException()).when(processor).isEnable(any());
        invokeDoPrepare(handler, new Object(), (p) -> {
        });
        verify(processor, times(1)).isEnd(any());
    }

    /**
     * Tests the preThenProc method under normal conditions.
     */
    @Test
    public void testPreThenProcNormal() throws Throwable {
        // Arrange
        doCallRealMethod().when(mockProcessHandler).preThenProc(null);
        // Act
        mockProcessHandler.preThenProc(null);
        // Assert
        verify(mockProcessHandler, times(1)).prepare(null);
        verify(mockProcessHandler, times(1)).process(null);
    }

    /**
     * Tests the preThenProc method when prepare throws an exception.
     */
    @Test(expected = Exception.class)
    public void testPreThenProcExceptionInPrepare() throws Throwable {
        // Arrange
        doThrow(new Exception()).when(mockProcessHandler).prepare(null);
        doCallRealMethod().when(mockProcessHandler).preThenProc(null);
        // Act
        mockProcessHandler.preThenProc(null);
    }

    /**
     * Tests the preThenProc method when process throws an exception.
     */
    @Test(expected = Exception.class)
    public void testPreThenProcExceptionInProcess() throws Throwable {
        // Arrange
        doThrow(new Exception()).when(mockProcessHandler).process(null);
        doCallRealMethod().when(mockProcessHandler).preThenProc(null);
        // Act
        mockProcessHandler.preThenProc(null);
    }

    @Test
    public void testDoProcessWithEmptyProcessorList() throws Throwable {
        ProcessHandler handler = new ProcessHandler();
        handler.setProcessorList(Collections.emptyList());
        invokePrivateDoProcess(handler, new Object(), (p) -> {
        });
        verify(processor, never()).isEnd(any());
        verify(processor, never()).isEnable(any());
        verify(processor, never()).matchDztgClient(any(), any());
    }

    @Test
    public void testDoProcessWithNonEmptyProcessorListAndAllProcessorsNotEnd() throws Throwable {
        ProcessHandler handler = new ProcessHandler();
        handler.setProcessorList(Arrays.asList(processor));
        // Ensure isEnd returns false to proceed with isEnable and matchDztgClient checks
        when(processor.isEnd(any())).thenReturn(false);
        // Ensure isEnable returns true
        when(processor.isEnable(any())).thenReturn(true);
        // Ensure matchDztgClient returns true
        when(processor.matchDztgClient(any(), any())).thenReturn(true);
        invokePrivateDoProcess(handler, new Object(), (p) -> {
        });
        // Verify isEnd is called once
        verify(processor, times(1)).isEnd(any());
        // Verify isEnable is called at least once
        verify(processor, atLeastOnce()).isEnable(any());
        // Verify matchDztgClient is called at least once
        verify(processor, atLeastOnce()).matchDztgClient(any(), any());
    }

    @Test
    public void testDoProcessWithNonEmptyProcessorListAndProcessorEnd() throws Throwable {
        ProcessHandler handler = new ProcessHandler();
        handler.setProcessorList(Arrays.asList(processor));
        when(processor.isEnd(any())).thenReturn(true);
        invokePrivateDoProcess(handler, new Object(), (p) -> {
        });
        verify(processor, times(1)).isEnd(any());
        verify(processor, never()).isEnable(any());
        verify(processor, never()).matchDztgClient(any(), any());
    }

    @Test
    public void testDoProcessWithNonEmptyProcessorListAndProcessorNotMatch() throws Throwable {
        ProcessHandler handler = new ProcessHandler();
        handler.setProcessorList(Arrays.asList(processor));
        when(processor.isEnd(any())).thenReturn(false);
        when(processor.isEnable(any())).thenReturn(true);
        when(processor.matchDztgClient(any(), any())).thenReturn(false);
        invokePrivateDoProcess(handler, new Object(), (p) -> {
        });
        verify(processor, times(1)).isEnd(any());
        verify(processor, times(1)).isEnable(any());
        verify(processor, times(1)).matchDztgClient(any(), any());
    }

    @Test
    public void testDoProcessWithNonEmptyProcessorListAndProcessorMatch() throws Throwable {
        ProcessHandler handler = new ProcessHandler();
        handler.setProcessorList(Arrays.asList(processor));
        when(processor.isEnd(any())).thenReturn(false);
        when(processor.isEnable(any())).thenReturn(true);
        when(processor.matchDztgClient(any(), any())).thenReturn(true);
        invokePrivateDoProcess(handler, new Object(), (p) -> {
        });
        verify(processor, times(1)).isEnd(any());
        verify(processor, times(1)).isEnable(any());
        verify(processor, times(1)).matchDztgClient(any(), any());
    }

    @Test
    public void testDoProcessWithNonEmptyProcessorListAndProcessorThrowException() throws Throwable {
        ProcessHandler handler = new ProcessHandler();
        handler.setProcessorList(Arrays.asList(processor));
        when(processor.isEnd(any())).thenReturn(false);
        when(processor.isEnable(any())).thenReturn(true);
        when(processor.matchDztgClient(any(), any())).thenReturn(true);
        try {
            invokePrivateDoProcess(handler, new Object(), (p) -> {
            });
        } catch (Exception e) {
            // expected
        }
        verify(processor, times(1)).isEnd(any());
        verify(processor, times(1)).isEnable(any());
        verify(processor, times(1)).matchDztgClient(any(), any());
    }
}
