package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.sankuai.athena.inf.cache2.CacheKey;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupIdBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MapperCacheWrapperGetDpDealGroupIdFutureTest {

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @InjectMocks
    private MapperCacheWrapper mapperCacheWrapper;

    private CacheKey validCacheKey;

    @Before
    public void setUp() {
        validCacheKey = CacheKey.valueOf("testKey", 1);
    }

    /**
     * 测试正常情况：mtDealGroupId>0，queryCenterWrapper返回有效DealGroupDTO
     */
    @Test
    public void testGetDpDealGroupIdFuture_normalCase() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDpDealGroupId(123L);
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(dealGroupDTO);
        // act
        CompletableFuture<Integer> result = mapperCacheWrapper.getDpDealGroupIdFuture(1, validCacheKey);
        // assert
        assertEquals(123, result.get().intValue());
        verify(queryCenterWrapper).getDealGroupDTO(any(QueryByDealGroupIdRequest.class));
    }

    /**
     * 测试异常情况2：queryCenterWrapper返回null
     */
    @Test
    public void testGetDpDealGroupIdFuture_queryCenterReturnsNull() throws Throwable {
        // arrange
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(null);
        when(dealGroupWrapper.preDpDealGroupId(anyInt())).thenReturn(null);
        when(dealGroupWrapper.getDpDealGroupId(any())).thenReturn(456);
        // act
        CompletableFuture<Integer> result = mapperCacheWrapper.getDpDealGroupIdFuture(1, validCacheKey);
        // assert
        assertEquals(456, result.get().intValue());
        verify(queryCenterWrapper).getDealGroupDTO(any(QueryByDealGroupIdRequest.class));
        verify(dealGroupWrapper).preDpDealGroupId(1);
        verify(dealGroupWrapper).getDpDealGroupId(null);
    }

    /**
     * 测试异常情况3：queryCenterWrapper抛出TException
     */
    @Test
    public void testGetDpDealGroupIdFuture_queryCenterThrowsException() throws Throwable {
        // arrange
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenThrow(new TException("test"));
        when(dealGroupWrapper.preDpDealGroupId(anyInt())).thenReturn(null);
        when(dealGroupWrapper.getDpDealGroupId(any())).thenReturn(789);
        // act
        CompletableFuture<Integer> result = mapperCacheWrapper.getDpDealGroupIdFuture(1, validCacheKey);
        // assert
        assertEquals(789, result.get().intValue());
        verify(queryCenterWrapper).getDealGroupDTO(any(QueryByDealGroupIdRequest.class));
        verify(dealGroupWrapper).preDpDealGroupId(1);
        verify(dealGroupWrapper).getDpDealGroupId(null);
    }

    /**
     * 测试边界情况：dpDealGroupIdInt为null
     */
    @Test
    public void testGetDpDealGroupIdFuture_nullDpDealGroupIdInt() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDpDealGroupId(null);
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(dealGroupDTO);
        when(dealGroupWrapper.preDpDealGroupId(anyInt())).thenReturn(null);
        when(dealGroupWrapper.getDpDealGroupId(any())).thenReturn(101);
        // act
        CompletableFuture<Integer> result = mapperCacheWrapper.getDpDealGroupIdFuture(1, validCacheKey);
        // assert
        assertEquals(101, result.get().intValue());
        verify(queryCenterWrapper).getDealGroupDTO(any(QueryByDealGroupIdRequest.class));
        verify(dealGroupWrapper).preDpDealGroupId(1);
        verify(dealGroupWrapper).getDpDealGroupId(null);
    }
}
