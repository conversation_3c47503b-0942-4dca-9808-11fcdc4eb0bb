package com.dianping.mobile.mapi.dztgdetail.util;

import static org.junit.Assert.*;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class UrlParserTest {

    @Test
    public void testParseUrlValidUrl() throws Throwable {
        String url = "http://example.com/path?param=value";
        Map<String, Object> result = UrlParser.parseUrl(url);
        assertNotNull(result);
        assertEquals("http", result.get("protocol"));
        assertEquals("example.com", result.get("host"));
        assertEquals("/path", result.get("path"));
        assertTrue(result.get("queryParams") instanceof Map);
        assertEquals("value", ((Map) result.get("queryParams")).get("param"));
    }

    @Test
    public void testParseUrlInvalidUrl() throws Throwable {
        String url = "invalid url";
        Map<String, Object> result = UrlParser.parseUrl(url);
        assertNotNull(result);
        assertEquals("invalid url", result.get("protocol"));
        assertNull(result.get("host"));
        assertNull(result.get("path"));
        assertNull(result.get("queryParams"));
    }

    @Test
    public void testParseUrlEmptyString() throws Throwable {
        String url = "";
        Map<String, Object> result = UrlParser.parseUrl(url);
        assertNotNull(result);
        assertEquals("", result.get("protocol"));
        assertNull(result.get("host"));
        assertNull(result.get("path"));
        assertNull(result.get("queryParams"));
    }

    @Test
    public void testParseUrlNull() throws Throwable {
        String url = null;
        Map<String, Object> result = UrlParser.parseUrl(url);
        assertNotNull(result);
        assertNull(result.get("protocol"));
        assertNull(result.get("host"));
        assertNull(result.get("path"));
        assertNull(result.get("queryParams"));
    }

    @Test
    public void testParseUrlRelativePath() throws Throwable {
        String url = "//example.com/path";
        Map<String, Object> result = UrlParser.parseUrl(url);
        assertNotNull(result);
        // For URLs starting with '//', the entire URL is treated as the protocol
        assertEquals("//example.com/path", result.get("protocol"));
        // Since there's no protocol separator ('://'), the method returns early
        // without parsing host and path
        assertNull(result.get("host"));
        assertNull(result.get("path"));
        assertNull(result.get("queryParams"));
    }

    @Test
    public void testParseUrlWithQueryParams() throws Throwable {
        String url = "http://example.com/path?param1=value1&param2=value2";
        Map<String, Object> result = UrlParser.parseUrl(url);
        assertNotNull(result);
        assertEquals("http", result.get("protocol"));
        assertEquals("example.com", result.get("host"));
        assertEquals("/path", result.get("path"));
        assertTrue(result.get("queryParams") instanceof Map);
        Map<String, String> queryParams = (Map<String, String>) result.get("queryParams");
        assertEquals("value1", queryParams.get("param1"));
        assertEquals("value2", queryParams.get("param2"));
    }

    @Test
    public void testParseUrlWithoutPath() throws Throwable {
        String url = "http://example.com";
        Map<String, Object> result = UrlParser.parseUrl(url);
        assertNotNull(result);
        assertEquals("http", result.get("protocol"));
        assertEquals("example.com", result.get("host"));
        assertEquals("/", result.get("path"));
        assertTrue(((Map) result.get("queryParams")).isEmpty());
    }
}
