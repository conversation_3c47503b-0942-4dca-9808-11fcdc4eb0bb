package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.technician.common.api.domain.Environment;
import com.dianping.technician.common.api.domain.Operator;
import com.dianping.technician.common.api.enums.OperatorRoleEnum;
import com.dianping.technician.common.api.enums.PlatformEnum;
import com.dianping.technician.common.api.enums.SourceEnum;
import com.dianping.technician.dto.shopsearch.TechCard;
import com.dianping.technician.dto.shopsearch.TechCategory;
import com.dianping.technician.dto.shopsearch.TechModuleRequest;
import com.dianping.technician.dto.shopsearch.TechModuleResult;
import com.dianping.technician.enums.TechGoodBindEnum;
import com.dianping.technician.service.eme.TechGoodsBindService;
import com.dianping.technician.service.shopsearch.TechShopSearchService;
import com.google.common.collect.Lists;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class BeautyTechGroupServiceTest {

    @Mock
    private TechGoodsBindService techGoodsBindService;

    @Mock
    private TechShopSearchService techShopSearchService;

    @InjectMocks
    private BeautyTechGroupService beautyTechGroupService;

    private DealCtx dealCtx;

    @Before
    public void setUp() {
        // Create an EnvCtx object
        EnvCtx envCtx = new EnvCtx();
        // Pass the EnvCtx object to the DealCtx constructor
        dealCtx = new DealCtx(envCtx);
        dealCtx.setDpId(123);
        dealCtx.setDpLongShopId(456L);
        // dealCtx.setMt(true); // Remove this line as setMt method does not exist
    }

    /**
     * Test case for empty technician IDs returned by techGoodsBindService.
     */
    @Test
    public void testGetGroupTechsEmptyTechnicianIds() throws Throwable {
        // arrange
        when(techGoodsBindService.queryShowTechnicianIdByGoods(any(Integer.class), any(TechGoodBindEnum.class))).thenReturn(Collections.emptyList());
        // act
        List<TechCard> result = beautyTechGroupService.getGroupTechs(dealCtx);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for non-empty technician IDs but empty category list returned by techShopSearchService.
     */
    @Test
    public void testGetGroupTechsEmptyCategoryList() throws Throwable {
        // arrange
        when(techGoodsBindService.queryShowTechnicianIdByGoods(any(Integer.class), any(TechGoodBindEnum.class))).thenReturn(Arrays.asList(1, 2, 3));
        TechModuleResult techModuleResult = new TechModuleResult();
        techModuleResult.setCategoryList(Collections.emptyList());
        when(techShopSearchService.searchShopTechList(any(TechModuleRequest.class))).thenReturn(techModuleResult);
        // act
        List<TechCard> result = beautyTechGroupService.getGroupTechs(dealCtx);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for non-empty technician IDs and non-empty category list but empty technicians in the first category.
     */
    @Test
    public void testGetGroupTechsEmptyTechniciansInCategory() throws Throwable {
        // arrange
        when(techGoodsBindService.queryShowTechnicianIdByGoods(any(Integer.class), any(TechGoodBindEnum.class))).thenReturn(Arrays.asList(1, 2, 3));
        TechCategory techCategory = new TechCategory();
        techCategory.setTechnicians(Collections.emptyList());
        TechModuleResult techModuleResult = new TechModuleResult();
        techModuleResult.setCategoryList(Arrays.asList(techCategory));
        when(techShopSearchService.searchShopTechList(any(TechModuleRequest.class))).thenReturn(techModuleResult);
        // act
        List<TechCard> result = beautyTechGroupService.getGroupTechs(dealCtx);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for non-empty technician IDs and non-empty technicians in the first category.
     */
    @Test
    public void testGetGroupTechsNonEmptyTechniciansInCategory() throws Throwable {
        // arrange
        when(techGoodsBindService.queryShowTechnicianIdByGoods(any(Integer.class), any(TechGoodBindEnum.class))).thenReturn(Arrays.asList(1, 2, 3));
        TechCard techCard1 = new TechCard();
        techCard1.setTechnicianId(1);
        TechCard techCard2 = new TechCard();
        techCard2.setTechnicianId(2);
        TechCategory techCategory = new TechCategory();
        techCategory.setTechnicians(Arrays.asList(techCard1, techCard2));
        TechModuleResult techModuleResult = new TechModuleResult();
        techModuleResult.setCategoryList(Arrays.asList(techCategory));
        when(techShopSearchService.searchShopTechList(any(TechModuleRequest.class))).thenReturn(techModuleResult);
        // act
        List<TechCard> result = beautyTechGroupService.getGroupTechs(dealCtx);
        // assert
        assertEquals(2, result.size());
    }

    /**
     * Test case for filtering technicians based on technician IDs.
     */
    @Test
    public void testGetGroupTechsFilteredTechnicians() throws Throwable {
        // arrange
        when(techGoodsBindService.queryShowTechnicianIdByGoods(any(Integer.class), any(TechGoodBindEnum.class))).thenReturn(Arrays.asList(1, 2));
        TechCard techCard1 = new TechCard();
        techCard1.setTechnicianId(1);
        TechCard techCard2 = new TechCard();
        techCard2.setTechnicianId(2);
        TechCard techCard3 = new TechCard();
        techCard3.setTechnicianId(3);
        TechCategory techCategory = new TechCategory();
        techCategory.setTechnicians(Arrays.asList(techCard1, techCard2, techCard3));
        TechModuleResult techModuleResult = new TechModuleResult();
        techModuleResult.setCategoryList(Arrays.asList(techCategory));
        when(techShopSearchService.searchShopTechList(any(TechModuleRequest.class))).thenReturn(techModuleResult);
        // act
        List<TechCard> result = beautyTechGroupService.getGroupTechs(dealCtx);
        // assert
        assertEquals(2, result.size());
        assertTrue(result.stream().allMatch(tc -> tc.getTechnicianId() == 1 || tc.getTechnicianId() == 2));
    }

    /**
     * Test case for exception handling.
     */
    @Test
    public void testGetGroupTechsExceptionHandling() throws Throwable {
        // arrange
        when(techGoodsBindService.queryShowTechnicianIdByGoods(any(Integer.class), any(TechGoodBindEnum.class))).thenThrow(new RuntimeException("Service error"));
        // act
        List<TechCard> result = beautyTechGroupService.getGroupTechs(dealCtx);
        // assert
        assertTrue(result.isEmpty());
    }
}
