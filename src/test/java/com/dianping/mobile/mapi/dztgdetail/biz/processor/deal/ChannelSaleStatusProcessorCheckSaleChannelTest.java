package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.SaleChannelAggregationDTO;
import com.sankuai.general.product.query.center.client.dto.SaleChannelDTO;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ChannelSaleStatusProcessorCheckSaleChannelTest {

    @InjectMocks
    private ChannelSaleStatusProcessor processor;

    private DealCtx ctx;

    private DealGroupDTO dealGroupDTO;

    private SaleChannelAggregationDTO saleChannelAggregation;

    @Before
    public void setUp() {
        ctx = new DealCtx(null);
        dealGroupDTO = new DealGroupDTO();
        saleChannelAggregation = new SaleChannelAggregationDTO();
        dealGroupDTO.setSaleChannelAggregation(saleChannelAggregation);
    }

    private void invokePrivateCheckSaleChannel(DealCtx ctx) throws Exception {
        Method method = ChannelSaleStatusProcessor.class.getDeclaredMethod("checkSaleChannel", DealCtx.class);
        method.setAccessible(true);
        method.invoke(processor, ctx);
    }

    /**
     * Test when supportChannels is empty
     */
    @Test
    public void testCheckSaleChannel_EmptySupportChannels() throws Throwable {
        // arrange
        saleChannelAggregation.setSupportChannels(new ArrayList<>());
        ctx.setDealGroupDTO(dealGroupDTO);
        // act
        invokePrivateCheckSaleChannel(ctx);
        // assert
        assertFalse(ctx.getMLiveChannel());
    }

    /**
     * Test single channel case - 10003L
     */
    @Test
    public void testCheckSaleChannel_SingleChannel_10003() throws Throwable {
        // arrange
        SaleChannelDTO channelDTO = new SaleChannelDTO();
        channelDTO.setChannelNo(10003L);
        saleChannelAggregation.setSupportChannels(Arrays.asList(channelDTO));
        ctx.setDealGroupDTO(dealGroupDTO);
        // act
        invokePrivateCheckSaleChannel(ctx);
        // assert
        assertTrue(ctx.getMLiveChannel());
    }

    /**
     * Test single channel case - 10017L
     */
    @Test
    public void testCheckSaleChannel_SingleChannel_10017() throws Throwable {
        // arrange
        SaleChannelDTO channelDTO = new SaleChannelDTO();
        channelDTO.setChannelNo(10017L);
        saleChannelAggregation.setSupportChannels(Arrays.asList(channelDTO));
        ctx.setDealGroupDTO(dealGroupDTO);
        // act
        invokePrivateCheckSaleChannel(ctx);
        // assert
        assertTrue(ctx.getMLiveChannel());
    }

    /**
     * Test dual channel case - both 10003L and 10017L
     */
    @Test
    public void testCheckSaleChannel_DualChannel_10003And10017() throws Throwable {
        // arrange
        SaleChannelDTO channel1 = new SaleChannelDTO();
        channel1.setChannelNo(10003L);
        SaleChannelDTO channel2 = new SaleChannelDTO();
        channel2.setChannelNo(10017L);
        saleChannelAggregation.setSupportChannels(Arrays.asList(channel1, channel2));
        ctx.setDealGroupDTO(dealGroupDTO);
        // act
        invokePrivateCheckSaleChannel(ctx);
        // assert
        assertTrue(ctx.getMLiveChannel());
    }

    /**
     * Test dual channel case - 10003L and other channel
     */
    @Test
    public void testCheckSaleChannel_DualChannel_10003AndOther() throws Throwable {
        // arrange
        SaleChannelDTO channel1 = new SaleChannelDTO();
        channel1.setChannelNo(10003L);
        SaleChannelDTO channel2 = new SaleChannelDTO();
        channel2.setChannelNo(10018L);
        saleChannelAggregation.setSupportChannels(Arrays.asList(channel1, channel2));
        ctx.setDealGroupDTO(dealGroupDTO);
        // act
        invokePrivateCheckSaleChannel(ctx);
        // assert
        assertFalse(ctx.getMLiveChannel());
    }

    /**
     * Test dual channel case - 10017L and other channel
     */
    @Test
    public void testCheckSaleChannel_DualChannel_10017AndOther() throws Throwable {
        // arrange
        SaleChannelDTO channel1 = new SaleChannelDTO();
        channel1.setChannelNo(10017L);
        SaleChannelDTO channel2 = new SaleChannelDTO();
        channel2.setChannelNo(10018L);
        saleChannelAggregation.setSupportChannels(Arrays.asList(channel1, channel2));
        ctx.setDealGroupDTO(dealGroupDTO);
        // act
        invokePrivateCheckSaleChannel(ctx);
        // assert
        assertFalse(ctx.getMLiveChannel());
    }

    /**
     * Test multiple channels without 10003L or 10017L
     */
    @Test
    public void testCheckSaleChannel_MultipleChannels_NoMLiveChannels() throws Throwable {
        // arrange
        List<SaleChannelDTO> channels = new ArrayList<>();
        for (long i = 10018L; i <= 10020L; i++) {
            SaleChannelDTO channel = new SaleChannelDTO();
            channel.setChannelNo(i);
            channels.add(channel);
        }
        saleChannelAggregation.setSupportChannels(channels);
        ctx.setDealGroupDTO(dealGroupDTO);
        // act
        invokePrivateCheckSaleChannel(ctx);
        // assert
        assertFalse(ctx.getMLiveChannel());
    }

    /**
     * Test multiple channels including both MLive channels and others
     */
    @Test
    public void testCheckSaleChannel_MultipleChannels_WithMLiveChannels() throws Throwable {
        // arrange
        List<SaleChannelDTO> channels = new ArrayList<>();
        for (long i = 10003L; i <= 10020L; i++) {
            SaleChannelDTO channel = new SaleChannelDTO();
            channel.setChannelNo(i);
            channels.add(channel);
        }
        saleChannelAggregation.setSupportChannels(channels);
        ctx.setDealGroupDTO(dealGroupDTO);
        // act
        invokePrivateCheckSaleChannel(ctx);
        // assert
        assertFalse(ctx.getMLiveChannel());
    }
}
