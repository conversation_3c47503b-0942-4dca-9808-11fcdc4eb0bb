package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.deal.shop.dto.BestShopFastReq;
import com.dianping.deal.shop.dto.BestShopSimpleDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperCacheWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetDealApplyShopRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.shop.DealApplyShopVO;
import com.dianping.mobile.mapi.dztgdetail.exception.DealApplyShopException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DealApplyShopFacadeTest {

    @InjectMocks
    private DealApplyShopFacade dealApplyShopFacade;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private MapperCacheWrapper mapperCacheWrapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试queryBestShop方法，当request为null时应返回null
     */
    @Test
    public void testQueryBestShopRequestIsNull() {
        DealApplyShopVO result = dealApplyShopFacade.queryBestShop(null, true, 1);
        assertNull(result);
    }

    /**
     * 测试queryBestShop方法，当dealGroupWrapper返回null时应抛出DealApplyShopException异常
     */
    @Test(expected = DealApplyShopException.class)
    public void testQueryBestShopDealGroupWrapperReturnsNull() {
        GetDealApplyShopRequest request = new GetDealApplyShopRequest();
        request.setDealGroupId(1L);
        request.setHomeCityId(1);
        request.setGpsCityId(1);
        request.setUserLat(1.0);
        request.setUserLng(1.0);
        when(dealGroupWrapper.preDealGroupBestShopFastly(any(BestShopFastReq.class))).thenReturn(null);
        dealApplyShopFacade.queryBestShop(request, true, 1);
    }

    /**
     * 测试queryBestShop方法，当mapperCacheWrapper返回的美团门店ID小于等于0时应抛出DealApplyShopException异常
     */
    @Test(expected = DealApplyShopException.class)
    public void testQueryBestShopMapperCacheWrapperReturnsInvalidMtShopId() {
        GetDealApplyShopRequest request = new GetDealApplyShopRequest();
        request.setDealGroupId(1L);
        request.setHomeCityId(1);
        request.setGpsCityId(1);
        request.setUserLat(1.0);
        request.setUserLng(1.0);
        BestShopSimpleDTO bestShopSimpleDTO = new BestShopSimpleDTO(1L, 1);
        CompletableFuture<Object> objectCompletableFuture = CompletableFuture.completedFuture(bestShopSimpleDTO);
        when(dealGroupWrapper.preDealGroupBestShopFastly(any())).thenReturn(objectCompletableFuture);
        when(dealGroupWrapper.getFutureResult(objectCompletableFuture)).thenReturn(bestShopSimpleDTO);
        when(mapperCacheWrapper.fetchMtShopId(anyLong())).thenReturn(0L);
        dealApplyShopFacade.queryBestShop(request, true, 1);
    }

    /**
     * 测试queryBestShop方法，正常情况下应返回非null的DealApplyShopVO对象
     */
    @Test
    public void testQueryBestShopReturnsValidDealApplyShopVO() {
        GetDealApplyShopRequest request = new GetDealApplyShopRequest();
        request.setDealGroupId(1L);
        request.setHomeCityId(1);
        request.setGpsCityId(1);
        request.setUserLat(1.0);
        request.setUserLng(1.0);
        BestShopSimpleDTO bestShopSimpleDTO = new BestShopSimpleDTO(1L, 1);
        CompletableFuture<Object> objectCompletableFuture = CompletableFuture.completedFuture(bestShopSimpleDTO);
        when(dealGroupWrapper.preDealGroupBestShopFastly(any())).thenReturn(objectCompletableFuture);
        when(dealGroupWrapper.getFutureResult(objectCompletableFuture)).thenReturn(bestShopSimpleDTO);
        when(mapperCacheWrapper.fetchMtShopId(anyLong())).thenReturn(1L);
        DealApplyShopVO result = dealApplyShopFacade.queryBestShop(request, true, 1);
        assertNotNull(result);
        assertEquals(1L, result.getDpShopId());
        assertEquals(1L, result.getMtShopId());
    }

    @Test
    public void testQueryBestShopReturnsValidDealApplyShopVODP() {
        GetDealApplyShopRequest request = new GetDealApplyShopRequest();
        request.setDealGroupId(1L);
        request.setHomeCityId(1);
        request.setGpsCityId(1);
        request.setUserLat(1.0);
        request.setUserLng(1.0);
        BestShopSimpleDTO bestShopSimpleDTO = new BestShopSimpleDTO(1L, 1);
        CompletableFuture<Object> objectCompletableFuture = CompletableFuture.completedFuture(bestShopSimpleDTO);
        when(dealGroupWrapper.preDealGroupBestShopFastly(any())).thenReturn(objectCompletableFuture);
        when(dealGroupWrapper.getFutureResult(objectCompletableFuture)).thenReturn(bestShopSimpleDTO);
        when(mapperCacheWrapper.fetchMtShopId(anyLong())).thenReturn(1L);
        DealApplyShopVO result = dealApplyShopFacade.queryBestShop(request, false, 1);
        assertNotNull(result);
        assertEquals(1L, result.getDpShopId());
        assertEquals(1L, result.getMtShopId());
    }

    /**
     * 测试 buildBestShopReq 方法，当 request 为 null 时
     */
    @Test
    public void testBuildBestShopReqWhenRequestIsNull() {
        // arrange
        GetDealApplyShopRequest request = null;
        boolean isMT = true;
        int clientType = 1;
        // act
        BestShopFastReq result = dealApplyShopFacade.buildBestShopReq(request, isMT, clientType);
        // assert
        assertNull(result);
    }

    /**
     * 测试 buildBestShopReq 方法，当 isMT 为 true 且 request 包含所有字段时
     */
    @Test
    public void testBuildBestShopReqWhenIsMTTrueAndRequestIsFull() {
        // arrange
        GetDealApplyShopRequest request = new GetDealApplyShopRequest();
        request.setDealGroupId(1L);
        request.setHomeCityId(2);
        request.setGpsCityId(3);
        request.setUserLat(4.0);
        request.setUserLng(5.0);
        boolean isMT = true;
        int clientType = 100501;
        when(mapperCacheWrapper.fetchDpCityId(anyInt())).thenReturn(10);
        when(mapperCacheWrapper.fetchDpDealId(anyInt())).thenReturn(20);
        // act
        BestShopFastReq result = dealApplyShopFacade.buildBestShopReq(request, isMT, clientType);
        // assert
        assertNotNull(result);
        assertEquals(20L, result.getDpDealGroupId());
        assertEquals(10, result.getGeoDpCityId());
        assertEquals(10, result.getDpCityId());
        assertEquals(4.0, result.getUserGcjLat(), 0);
        assertEquals(5.0, result.getUserGcjLng(), 0);
    }

    /**
     * 测试 buildBestShopReq 方法，当 isMT 为 false 且 clientType 为特殊值时
     */
    @Test
    public void testBuildBestShopReqWhenIsMTFalseAndClientTypeIsSpecial() {
        // arrange
        GetDealApplyShopRequest request = new GetDealApplyShopRequest();
        request.setGpsCityId(3);
        boolean isMT = false;
        int clientType = 100502;
        when(mapperCacheWrapper.fetchDpCityId(anyInt())).thenReturn(10);
        // act
        BestShopFastReq result = dealApplyShopFacade.buildBestShopReq(request, isMT, clientType);
        // assert
        assertNotNull(result);
        assertEquals(10, result.getGeoDpCityId());
    }

    /**
     * 测试 buildBestShopReq 方法，当 isMT 为 false 且 clientType 不为特殊值时
     */
    @Test
    public void testBuildBestShopReqWhenIsMTFalseAndClientTypeIsNotSpecial() {
        // arrange
        GetDealApplyShopRequest request = new GetDealApplyShopRequest();
        request.setGpsCityId(3);
        boolean isMT = false;
        // 非特殊客户端类型
        int clientType = 1;
        // act
        BestShopFastReq result = dealApplyShopFacade.buildBestShopReq(request, isMT, clientType);
        // assert
        assertNotNull(result);
        assertEquals(3, result.getGeoDpCityId());
    }
}
