package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.style.enums.PageTypeEnum;
import com.dianping.deal.style.protocol.ExtraStyleRequest;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DealStyle;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

public class DealStyleWrapperBuildExtraStyleRequestForDpTest {

    private DealStyleWrapper dealStyleWrapper;

    private DealCtx dealCtx;

    private EnvCtx envCtx;

    private DealStyle dealStyle;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        dealStyleWrapper = new DealStyleWrapper();
        dealCtx = mock(DealCtx.class);
        envCtx = mock(EnvCtx.class);
        dealStyle = mock(DealStyle.class);
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(dealCtx.getDealStyle()).thenReturn(dealStyle);
    }

    private ExtraStyleRequest invokePrivateBuildExtraStyleRequestForDp(DealCtx dealCtx) throws Exception {
        Method method = DealStyleWrapper.class.getDeclaredMethod("buildExtraStyleRequestForDp", DealCtx.class);
        method.setAccessible(true);
        return (ExtraStyleRequest) method.invoke(dealStyleWrapper, dealCtx);
    }

    /**
     * Test when DealCtx is null
     */
    @Test
    public void testBuildExtraStyleRequestForDpWhenDealCtxIsNull() throws Throwable {
        // arrange
        DealCtx nullDealCtx = null;
        // act
        ExtraStyleRequest result = invokePrivateBuildExtraStyleRequestForDp(nullDealCtx);
        // assert
        assertNull(result);
    }

    /**
     * Test when EnvCtx is null
     */
    @Test
    public void testBuildExtraStyleRequestForDpWhenEnvCtxIsNull() throws Throwable {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(null);
        // act
        ExtraStyleRequest result = invokePrivateBuildExtraStyleRequestForDp(dealCtx);
        // assert
        assertNull(result);
    }

    /**
     * Test when DealStyle is null
     */
    @Test
    public void testBuildExtraStyleRequestForDpWhenDealStyleIsNull() throws Throwable {
        // arrange
        when(dealCtx.getDealStyle()).thenReturn(null);
        // act
        ExtraStyleRequest result = invokePrivateBuildExtraStyleRequestForDp(dealCtx);
        // assert
        assertNull(result);
    }

    /**
     * Test when the standard deal group attribute value is blank
     */
    @Test
    public void testBuildExtraStyleRequestForDpWhenStandardDealGroupAttrValueIsBlank() throws Throwable {
        // arrange
        when(envCtx.getDpUserId()).thenReturn(12345L);
        when(envCtx.getAppDeviceId()).thenReturn("device123");
        when(envCtx.getUnionId()).thenReturn("union123");
        when(dealCtx.getDpCityId()).thenReturn(67890);
        when(envCtx.getVersion()).thenReturn("1.0.0");
        when(dealCtx.getDpId()).thenReturn(54321);
        when(dealStyle.getModuleKey()).thenReturn("joy_bath");
        when(envCtx.isAndroid()).thenReturn(true);
        when(dealCtx.getRequestSource()).thenReturn("source123");
        when(envCtx.getClientType()).thenReturn(1);
        when(dealCtx.getDpLongShopId()).thenReturn(98765L);
        when(envCtx.getMpSource()).thenReturn("mpSource123");
        when(dealCtx.getAttrs()).thenReturn(Collections.emptyList());
        when(dealCtx.getMrnVersion()).thenReturn("1.0.0");
        // act
        ExtraStyleRequest result = invokePrivateBuildExtraStyleRequestForDp(dealCtx);
        // assert
        assertNotNull(result);
        assertFalse(result.getParam().containsKey("standardDealGroup"));
    }

    /**
     * Test when the standard deal group attribute value is not blank
     */
    @Test
    public void testBuildExtraStyleRequestForDpWhenStandardDealGroupAttrValueIsNotBlank() throws Throwable {
        // arrange
        when(envCtx.getDpUserId()).thenReturn(12345L);
        when(envCtx.getAppDeviceId()).thenReturn("device123");
        when(envCtx.getUnionId()).thenReturn("union123");
        when(dealCtx.getDpCityId()).thenReturn(67890);
        when(envCtx.getVersion()).thenReturn("1.0.0");
        when(dealCtx.getDpId()).thenReturn(54321);
        when(dealStyle.getModuleKey()).thenReturn("joy_bath");
        when(envCtx.isAndroid()).thenReturn(true);
        when(dealCtx.getRequestSource()).thenReturn("source123");
        when(envCtx.getClientType()).thenReturn(1);
        when(dealCtx.getDpLongShopId()).thenReturn(98765L);
        when(envCtx.getMpSource()).thenReturn("mpSource123");
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("standardDealGroup");
        attributeDTO.setValue(Collections.singletonList("1"));
        attrs.add(attributeDTO);
        when(dealCtx.getAttrs()).thenReturn(attrs);
        when(dealCtx.getMrnVersion()).thenReturn("1.0.0");
        // act
        ExtraStyleRequest result = invokePrivateBuildExtraStyleRequestForDp(dealCtx);
        // assert
        assertNotNull(result);
        assertEquals("1", result.getParam().get("standardDealGroup"));
    }
}

// Wrapper class for Lion to facilitate mocking
class LionWrapper {

    public boolean getBoolean(String appName, String key, boolean defaultValue) {
        return Lion.getBoolean(appName, key, defaultValue);
    }
}
