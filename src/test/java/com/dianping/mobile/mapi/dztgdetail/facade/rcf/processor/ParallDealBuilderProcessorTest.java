package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.base.dto.DealBaseDTO;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.LifeClearUtil;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.ParallDealBuilderProcessorUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.QualityEducationUtil;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.LEInsuranceAgreementEnum;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.FreeDealEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.StyleTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealstruct.PnPurchaseNoteDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealstruct.PurchaseNoteModuleDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.dianping.mobile.mapi.dztgdetail.entity.CleaningProductLionConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.CostEffectivePinTuan;
import com.dianping.mobile.mapi.dztgdetail.entity.FreeDealConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.LifeClearHaiMaConfig;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.NewBuyBarHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.PriceProtectionHelper;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.EduDealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.TimesDealUtil;
import com.dianping.pigeon.remoting.common.codec.json.JacksonUtils;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import com.sankuai.mlive.goods.trade.api.model.GoodsSellingInfoDTO;
import org.apache.commons.lang.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import static com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum.MEITUAN_APP;
import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @since 2023/9/26 14:41
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({Lion.class, LionConfigUtils.class,DealBuyHelper.class,EduDealUtils.class,DealUtils.class,PriceProtectionHelper.class,NewBuyBarHelper.class,TimesDealUtil.class})
public class ParallDealBuilderProcessorTest {

    @InjectMocks
    private ParallDealBuilderProcessor parallDealBuilderProcessor;

    private DealCtx ctx;

    @Mock
    private DealBuyBar dealBuyBar;

    private DealGroupPBO result;

    @Mock
    private EnvCtx envCtx;

    @Mock
    private FeaturesLayer featuresLayer;

    @Mock
    private DouHuBiz douHuBiz;

    @InjectMocks
    private QualityEducationUtil qualityEducationUtil;

    @InjectMocks
    private LifeClearUtil lifeClearUtil;




    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        ctx = mock(DealCtx.class);
        result = new DealGroupPBO();
        PowerMockito.mockStatic(Lion.class);
        PowerMockito.mockStatic(EduDealUtils.class);
    }

    /**
     * 测试 setWuyoutongPromo 方法
     */
    @Test
    public void testSetWuyoutongPromo() {
        // arrange
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        promoDetailModule.setShowMarketPrice(true);
        promoDetailModule.setMarketPricePromo("test");
        promoDetailModule.setShowBestPromoDetails(true);
        promoDetailModule.setBestPromoDetails(new ArrayList<>());
        promoDetailModule.setMarketPrice("test");
        promoDetailModule.setMarketPromoDiscount("test");
        // act
        parallDealBuilderProcessor.setWuyoutongPromo(promoDetailModule);
        // assert
        assertFalse(promoDetailModule.isShowMarketPrice());
        assertEquals("", promoDetailModule.getMarketPricePromo());
        assertFalse(promoDetailModule.isShowBestPromoDetails());
        assertNull(promoDetailModule.getBestPromoDetails());
        assertEquals("", promoDetailModule.getMarketPrice());
        assertEquals("", promoDetailModule.getMarketPromoDiscount());
    }

    @Test
    public void testBuildDealPromoDetailInfo_PriceDisplayDTOEmpty() throws Throwable {
        PowerMockito.mockStatic(Lion.class);
        when(Lion.getList(anyString(), anyString(), Mockito.eq(Integer.class), anyList())).thenAnswer((Answer<List>) invocation -> Lists.newArrayList(1));
        // arrange
        PriceDisplayDTO priceDisplayDTO = mock(PriceDisplayDTO.class);
        when(priceDisplayDTO.getUsedPromos()).thenReturn(null);
        when(ctx.getCategoryId()).thenReturn(1603);
        DealGroupPBO result = new DealGroupPBO();
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        promoDetailModule.setMarketPrice("100");
        result.setPromoDetailModule(promoDetailModule);
        // act
        ReflectionTestUtils.invokeMethod(parallDealBuilderProcessor, "buildDealPromoDetailInfo", ctx, priceDisplayDTO, result);
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx1 = new DealCtx(envCtx);
        DealGroupPBO result11 = new DealGroupPBO();
        GoodsSellingInfoDTO sellingInfo = new GoodsSellingInfoDTO();
        sellingInfo.setAllowSelling(Boolean.TRUE);
        parallDealBuilderProcessor.allowSelling(sellingInfo);
        DealGroupBaseDTO dealGroupBase = new DealGroupBaseDTO();
        dealGroupBase.setBeginDate(new Date());
        parallDealBuilderProcessor.notMeetSellingTime(dealGroupBase);
        // assert
        assertNotNull(result);
    }

    /**
     * 测试场景：市场价=团购价，市场价为空
     */
    @Test
    public void testBuildNormalPromoDetailInfo_Normal_MarketPrice_DGPrice_Same() throws Throwable {
        // arrange
        DealGroupBaseDTO dealGroupBaseDTO = new DealGroupBaseDTO();
        dealGroupBaseDTO.setDealGroupPrice(new BigDecimal(100));
        dealGroupBaseDTO.setMarketPrice(new BigDecimal(100));
        when(ctx.getDealGroupBase()).thenReturn(dealGroupBaseDTO);
        // when(ctx.getCategoryId()).thenReturn(1603);
        DealGroupPBO result = new DealGroupPBO();
        PriceDisplayDTO normalPrice = mock(PriceDisplayDTO.class);
        when(normalPrice.getPrice()).thenReturn(new BigDecimal(100));
        when(normalPrice.getUsedPromos()).thenReturn(Lists.newArrayList());
        // act
        ReflectionTestUtils.invokeMethod(parallDealBuilderProcessor, "buildNormalPromoDetailInfo", ctx, normalPrice, result);
        // assert
        Assert.assertEquals("100", result.getPromoDetailModule().getMarketPrice());
    }

    @Test
    public void testBuildTimesDealRemind1() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupBasicDTO basic = new DealGroupBasicDTO();
        basic.setTradeType(1);
        dealGroupDTO.setBasic(basic);
        ctx.setDealGroupDTO(dealGroupDTO);
        ParallDealBuilderProcessor parallDealBuilderProcessor = new ParallDealBuilderProcessor();
        // act
        List<String> strings = parallDealBuilderProcessor.buildTimesDealRemind(ctx);
        // assert
        Assert.assertNull(strings);
    }

    @Test
    public void testBuildTimesDealRemind() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupBasicDTO basic = new DealGroupBasicDTO();
        basic.setTradeType(19);
        dealGroupDTO.setBasic(basic);
        ctx.setDealGroupDTO(dealGroupDTO);
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(DealAttrKeys.SINGLE_VERIFICATION_QUANTITY_DESC);
        attrDTO.setValue(Lists.newArrayList("3"));
        dealGroupDTO.setAttrs(Lists.newArrayList(attrDTO));
        ParallDealBuilderProcessor parallDealBuilderProcessor = new ParallDealBuilderProcessor();
        // act
        List<String> strings = parallDealBuilderProcessor.buildTimesDealRemind(ctx);
        // assert
        assertNotNull(strings);
    }

    /**
     * 测试商品为美团美播小程序端的情况
     */
    @Test
    @Ignore
    public void testProcessMtLiveMinApp() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_LIVE_WEIXIN_MINIAPP);
        DealCtx dealCtx = new DealCtx(envCtx);
        DealGroupPBO dealGroupPBO = new DealGroupPBO();
        dealCtx.setResult(dealGroupPBO);
        DealGroupBaseDTO dealGroupBaseDTO = new DealGroupBaseDTO();
        DealBaseDTO dealBaseDTO = new DealBaseDTO();
        dealBaseDTO.setDealId(1);
        dealGroupBaseDTO.setDeals(Collections.singletonList(dealBaseDTO));
        dealCtx.setDealGroupBase(dealGroupBaseDTO);
        MockedStatic<LionConfigUtils> lionConfigUtilsMocked = mockStatic(LionConfigUtils.class);
        lionConfigUtilsMocked.when(LionConfigUtils::getHeadVideoIterationSwitch).thenReturn(false);
        lionConfigUtilsMocked.when(() -> LionConfigUtils.getHeaderPicProcessor(anyInt())).thenReturn("default");
        // act
        parallDealBuilderProcessor.process(dealCtx);
        lionConfigUtilsMocked.close();
        // assert
        assertNotNull(dealCtx.getResult());
    }


    /**
     * 测试 displayOnlineConsult 方法，当 dealBuyBar 为 null 时
     */
    @Test
    public void testDisplayOnlineConsultDealBuyBarIsNull() {
        // arrange
        DealBuyBar dealBuyBar = null;
        // act
        boolean result = ParallDealBuilderProcessor.displayOnlineConsult(ctx, dealBuyBar);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 displayOnlineConsult 方法，当 dealBuyBar.getStyleType() 等于 COMMON.code 且 dealBuyBar.getBuyBtns().size() 为 1
     */
    @Test
    public void testDisplayOnlineConsultDealBuyBarStyleTypeIsShoppingCartAndBuyBtnsSizeIsOne() {
        // arrange
        when(dealBuyBar.getStyleType()).thenReturn(StyleTypeEnum.SHOPPING_CART.code);
        when(dealBuyBar.getBuyBtns()).thenReturn(Collections.singletonList(mock(DealBuyBtn.class)));
        // act
        boolean result = ParallDealBuilderProcessor.displayOnlineConsult(ctx, dealBuyBar);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 displayOnlineConsult 方法，当 DealBuyHelper.isEduOnlineCourseDeal(ctx) 为 true 且 dealBuyBar 不为 null 且 ClientTypeEnum.isMainWX(clientType) 为 true
     */
    @Test
    public void testDisplayOnlineConsultIsEduOnlineCourseDealAndDealBuyBarNotNullAndIsMainWX() {
        // arrange
        when(EduDealUtils.isEduOnlineCourseDeal(ctx)).thenAnswer((Answer<Boolean>) invocation -> true);
        when(ctx.getEnvCtx()).thenReturn(mock(EnvCtx.class));
        when(ctx.getEnvCtx().getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP);
        // act
        boolean result = ParallDealBuilderProcessor.displayOnlineConsult(ctx, dealBuyBar);
        // assert
        assertTrue(result);
    }


    /**
     * 测试 displayOnlineConsult 方法，其他情况
     */
    @Test
    public void testDisplayOnlineConsultOtherCases() {
        PowerMockito.mockStatic(EduDealUtils.class);
        when(EduDealUtils.isEduOnlineCourseDeal(ctx)).thenAnswer((Answer<Boolean>) invocation -> false);
        // arrange
        when(dealBuyBar.getBuyBtns()).thenReturn(Collections.singletonList(mock(DealBuyBtn.class)));
        // act
        boolean result = ParallDealBuilderProcessor.displayOnlineConsult(ctx, dealBuyBar);
        // assert
        assertTrue(result);
    }

    /*
     * 测试 petVoucherTitleSwitch 为 false 的情况
     */
    @Test
    public void testPutPetVoucherTitleSwitchFalse() {
        // arrange
        // when(ctx.getDealGroupDTO()).thenReturn(null);
        PowerMockito.mockStatic(Lion.class);
        when(Lion.getBoolean(anyString(), anyString(), anyBoolean())).thenAnswer((Answer<Boolean>) invocation -> false);
        // act
        parallDealBuilderProcessor.putPetVoucherTitle(ctx, result);
        // assert
        assertEquals(null, result.getTitle());
    }

    /**
     * 测试 petVoucherTitleSwitch 为 true，但 result 的 categoryId 不在 petCategory 中的情况
     */
    @Test
    public void testPutPetVoucherTitleNotInPetCategory() {
        // arrange
        // when(ctx.getDealGroupDTO()).thenReturn(null);
        PowerMockito.mockStatic(Lion.class);
        when(Lion.getBoolean(anyString(), anyString(), anyBoolean())).thenAnswer((Answer<Boolean>) invocation -> true);
        when(Lion.getList(any(), any(), any(), any())).thenAnswer((Answer<List>) invocation -> Arrays.asList(1701, 1702, 1704, 1705));
        result.setCategoryId(999);
        // act
        parallDealBuilderProcessor.putPetVoucherTitle(ctx, result);
        // assert
        assertEquals(null, result.getTitle());
    }

    /**
     * 测试 petVoucherTitleSwitch 为 true，result 的 categoryId 在 petCategory 中，但 dealGroupDTO 或其 basic 属性或 title 属性为 null 或空字符串的情况
     */
    @Test
    public void testPutPetVoucherTitleDealGroupDTONullOrTitleEmpty() {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(null);
        PowerMockito.mockStatic(Lion.class);
        when(Lion.getBoolean(anyString(), anyString(), anyBoolean())).thenAnswer((Answer<Boolean>) invocation -> true);
        when(Lion.getList(any(), any(), any(), any())).thenAnswer((Answer<List>) invocation -> Arrays.asList(1701, 1702, 1704, 1705));
        result.setCategoryId(1701);
        // act
        parallDealBuilderProcessor.putPetVoucherTitle(ctx, result);
        // assert
        assertEquals(null, result.getTitle());
    }

    /**
     * 测试 petVoucherTitleSwitch 为 true，result 的 categoryId 在 petCategory 中，且 dealGroupDTO、basic 属性和 title 属性都不为 null 且 title 不为空字符串的情况
     */
    @Test
    public void testPutPetVoucherTitleSuccess() {
        // arrange
        DealGroupBasicDTO dealGroupBasic = new DealGroupBasicDTO();
        dealGroupBasic.setTitle("Test Title");
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setBasic(dealGroupBasic);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        PowerMockito.mockStatic(Lion.class);
        when(Lion.getBoolean(anyString(), anyString(), anyBoolean())).thenAnswer((Answer<Boolean>) invocation -> true);
        when(Lion.getList(any(), any(), any(), any())).thenAnswer((Answer<List>) invocation -> Arrays.asList(1701, 1702, 1704, 1705));
        // lionMockedStatic.when(() -> Lion.getBoolean(anyString(), anyString(), anyBoolean())).thenReturn(true);
        // lionMockedStatic.when(() -> Lion.getList(any(), any(), any(), any())).thenReturn(Arrays.asList(1701, 1702, 1704, 1705));
        result.setCategoryId(1701);
        // act
        parallDealBuilderProcessor.putPetVoucherTitle(ctx, result);
        // assert
        assertEquals("Test Title", result.getTitle());
    }

    @Test
    public void testGetHitStructedPurchaseNote() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx dealCtx = new DealCtx(envCtx);
        dealCtx.setHitStructuredPurchaseNote(true);
        PnPurchaseNoteDTO pnPurchaseNoteDTO = new PnPurchaseNoteDTO();
        PurchaseNoteModuleDTO purchaseNoteModuleDTO = new PurchaseNoteModuleDTO();
        purchaseNoteModuleDTO.setPnModuleName("购买须知");
        purchaseNoteModuleDTO.setPnIcon("icon");
        List<PurchaseNoteModuleDTO> purchaseNoteModuleDTOList = new ArrayList<>();
        purchaseNoteModuleDTOList.add(purchaseNoteModuleDTO);
        pnPurchaseNoteDTO.setPnModules(purchaseNoteModuleDTOList);
        dealCtx.setPnPurchaseNoteDTO(pnPurchaseNoteDTO);
        Assert.assertTrue(parallDealBuilderProcessor.getHitStructuredPurchaseNote(dealCtx));
    }

    @Test
    public void testBuildHitStructuredPurchaseNote() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(MEITUAN_APP);
        DealCtx dealCtx = new DealCtx(envCtx);
        dealCtx.setHitStructuredPurchaseNote(true);
        PnPurchaseNoteDTO pnPurchaseNoteDTO = new PnPurchaseNoteDTO();
        PurchaseNoteModuleDTO purchaseNoteModuleDTO = new PurchaseNoteModuleDTO();
        purchaseNoteModuleDTO.setPnModuleName("购买须知");
        purchaseNoteModuleDTO.setPnIcon("icon");
        List<PurchaseNoteModuleDTO> purchaseNoteModuleDTOList = new ArrayList<>();
        purchaseNoteModuleDTOList.add(purchaseNoteModuleDTO);
        pnPurchaseNoteDTO.setPnModules(purchaseNoteModuleDTOList);
        dealCtx.setPnPurchaseNoteDTO(pnPurchaseNoteDTO);
        DealGroupPBO result = new DealGroupPBO();
        parallDealBuilderProcessor.buildStructuredPurchaseNote(dealCtx, result);
        Assert.assertTrue(result.isHitStructuredPurchaseNote());
    }

    /**
     * 测试 putTradeType 方法，当 DealCtx.isFreeDeal 返回 true 时
     */
    @Test
    public void testPutTradeTypeWhenIsFreeDealIsTrue() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setFreeDeal(true);
        ctx.setFreeDealType(FreeDealEnum.HOME_DESIGN_BOOKING);
        FreeDealConfig freeDealConfig = new FreeDealConfig();
        ctx.setFreeDealConfig(freeDealConfig);
        DealGroupPBO result = new DealGroupPBO();
        ParallDealBuilderProcessor processor = new ParallDealBuilderProcessor();
        // act
        processor.putTradeType(ctx, result);
        // assert
        assertEquals("Trade type should be set to 0 when isFreeDeal is true", 0, result.getTradeType());
    }

    /**
     * 测试 putTradeType 方法，当 DealCtx.isFreeDeal 返回 false 时
     */
    @Test
    public void testPutTradeTypeWhenIsFreeDealIsFalse() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setFreeDeal(false);
        DealGroupPBO result = new DealGroupPBO();
        ParallDealBuilderProcessor processor = new ParallDealBuilderProcessor();
        // act
        processor.putTradeType(ctx, result);
        // assert
        assertEquals("Trade type should be set to 1 when isFreeDeal is false", 1, result.getTradeType());
    }

    @Test
    public void testGetFeaturesLayer() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setFreeDeal(true);
        ctx.setFreeDealType(FreeDealEnum.HOME_DESIGN_BOOKING);
        FreeDealConfig freeDealConfig = new FreeDealConfig();
        ctx.setFreeDealConfig(freeDealConfig);
        DealGroupPBO result = new DealGroupPBO();
        ParallDealBuilderProcessor processor = new ParallDealBuilderProcessor();
        // act
        FeaturesLayer featuresLayer = processor.getFeaturesLayer(ctx, false);
        // assert
        assertNotNull(featuresLayer);
    }


    @Test
    public void testBuildPurchaseLimitInfo() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setFreeDeal(true);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(1904);
        ctx.setChannelDTO(channelDTO);
        ctx.setFreeDealType(FreeDealEnum.HOME_DESIGN_BOOKING);
        FreeDealConfig freeDealConfig = new FreeDealConfig();
        freeDealConfig.setLimit(Lists.newArrayList("ceshi"));
        ctx.setFreeDealConfig(freeDealConfig);
        DealGroupPBO result = new DealGroupPBO();
        ParallDealBuilderProcessor processor = new ParallDealBuilderProcessor();
        // act
        processor.buildPurchaseLimitInfo(ctx, result);
        // assert
        assertNotNull(result.getLimits());
    }


    @Test
    public void testBuildCardStylePromoDetailInfo() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setEnableCardStyle(true);
        ctx.setFreeDeal(true);
        ctx.setFreeDealType(FreeDealEnum.HOME_DESIGN_BOOKING);
        FreeDealConfig freeDealConfig = new FreeDealConfig();
        ctx.setFreeDealConfig(freeDealConfig);
        DealGroupPBO result = new DealGroupPBO();
        result.setPromoDetailModule(new PromoDetailModule());
        ParallDealBuilderProcessor processor = new ParallDealBuilderProcessor();
        // act
        processor.buildCardStylePromoDetailInfo(ctx, result);
        // assert
        assertNull(result.getPromoDetailModule().getCouponList());
    }

    @Test
    public void testBuildReminder() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setFreeDeal(true);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(1904);
        ctx.setChannelDTO(channelDTO);
        ctx.setFreeDealType(FreeDealEnum.HOME_DESIGN_BOOKING);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("free_product_notice");
        attrDTO.setValue(Lists.newArrayList("亮点"));
        dealGroupDTO.setAttrs(Lists.newArrayList(attrDTO));
        FreeDealConfig freeDealConfig = new FreeDealConfig();
        ctx.setFreeDealConfig(freeDealConfig);
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.setEnableCardStyleV2(true);
        DealGroupPBO result = new DealGroupPBO();
        ParallDealBuilderProcessor processor = new ParallDealBuilderProcessor();
        // act
        processor.buildReminder(ctx, result);
        // assert
        assertNotNull(result.getReminderInfo());
    }

    /**
     * 测试 dealGroupDealDTO 为null时
     */
    @Test
    public void testBuildResvPromoDetailInfo_dealGroupDealDTO_NULL() throws Throwable {
        // arrange
        DealGroupBaseDTO dealGroupBase = new DealGroupBaseDTO();
        dealGroupBase.setDealGroupPrice(new BigDecimal("100"));

        when(ctx.getDealGroupBase()).thenReturn(dealGroupBase);
        when(ctx.getDealGroupDTO()).thenReturn(new DealGroupDTO());
        PriceDisplayDTO priceDisplayDTO = mock(PriceDisplayDTO.class);
        // act
        ReflectionTestUtils.invokeMethod(parallDealBuilderProcessor, "buildResvPromoDetailInfo", ctx, priceDisplayDTO, result);

        // assert
        assertNull(result.getPromoDetailModule());
    }

    /**
     * 测试 dealGroupDealDTO 为不为null时 且 retailPriceStyle=1
     */
    @Test
    public void testBuildResvPromoDetailInfo_retailPriceStyle_1() throws Throwable {
        // arrange
        DealGroupBaseDTO dealGroupBase = new DealGroupBaseDTO();
        dealGroupBase.setDealGroupPrice(new BigDecimal("100"));
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDealDTO dealGroupDealDTO = mock(DealGroupDealDTO.class);
        AttrDTO attrDTO = mock(AttrDTO.class);


        when(ctx.getDealGroupBase()).thenReturn(dealGroupBase);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getDeals()).thenReturn(Lists.newArrayList(dealGroupDealDTO));
        when(dealGroupDealDTO.getAttrs()).thenReturn(Lists.newArrayList(attrDTO));
        when(attrDTO.getName()).thenReturn("retailPriceStyle");
        when(attrDTO.getValue()).thenReturn(Lists.newArrayList("1"));
        PriceDisplayDTO priceDisplayDTO = mock(PriceDisplayDTO.class);
        // act
        ReflectionTestUtils.invokeMethod(parallDealBuilderProcessor, "buildResvPromoDetailInfo", ctx, priceDisplayDTO, result);

        // assert
        assertEquals(0,result.getPromoDetailModule().getPriceDisplayType());
        assertEquals("100",result.getPromoDetailModule().getFinalPrice());
        assertNotNull(result.getPromoDetailModule().getDescriptionTags());
    }

    /**
     * 测试 dealGroupDealDTO 为不为null时 且 retailPriceStyle=2
     */
    @Test
    public void testBuildResvPromoDetailInfo_retailPriceStyle_2() throws Throwable {
        // arrange
        DealGroupBaseDTO dealGroupBase = new DealGroupBaseDTO();
        dealGroupBase.setDealGroupPrice(new BigDecimal("100"));
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDealDTO dealGroupDealDTO = mock(DealGroupDealDTO.class);
        AttrDTO attrDTO = mock(AttrDTO.class);


        when(ctx.getDealGroupBase()).thenReturn(dealGroupBase);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getDeals()).thenReturn(Lists.newArrayList(dealGroupDealDTO));
        when(dealGroupDealDTO.getAttrs()).thenReturn(Lists.newArrayList(attrDTO));
        when(attrDTO.getName()).thenReturn("retailPriceStyle");
        when(attrDTO.getValue()).thenReturn(Lists.newArrayList("2"));
        PriceDisplayDTO priceDisplayDTO = mock(PriceDisplayDTO.class);
        // act
        ReflectionTestUtils.invokeMethod(parallDealBuilderProcessor, "buildResvPromoDetailInfo", ctx, priceDisplayDTO, result);

        // assert
        assertEquals(1,result.getPromoDetailModule().getPriceDisplayType());
        assertEquals("价格详询机构",result.getPromoDetailModule().getPriceDisplayText());
    }

    /**
     * 测试 dealGroupDealDTO 为不为null时 且 retailPriceStyle=3
     */
    @Test
    public void testBuildResvPromoDetailInfo_retailPriceStyle_3() throws Throwable {
        // arrange
        DealGroupBaseDTO dealGroupBase = new DealGroupBaseDTO();
        dealGroupBase.setDealGroupPrice(new BigDecimal("100"));
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDealDTO dealGroupDealDTO = mock(DealGroupDealDTO.class);
        AttrDTO attrDTO = mock(AttrDTO.class);


        when(ctx.getDealGroupBase()).thenReturn(dealGroupBase);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getDeals()).thenReturn(Lists.newArrayList(dealGroupDealDTO));
        when(dealGroupDealDTO.getAttrs()).thenReturn(Lists.newArrayList(attrDTO));
        when(attrDTO.getName()).thenReturn("retailPriceStyle");
        when(attrDTO.getValue()).thenReturn(Lists.newArrayList("3"));
        PriceDisplayDTO priceDisplayDTO = mock(PriceDisplayDTO.class);
        // act
        ReflectionTestUtils.invokeMethod(parallDealBuilderProcessor, "buildResvPromoDetailInfo", ctx, priceDisplayDTO, result);

        // assert
        assertEquals(0,result.getPromoDetailModule().getPriceDisplayType());
        assertEquals("100",result.getPromoDetailModule().getFinalPrice());
        assertEquals("起 具体详询机构",result.getPromoDetailModule().getPricePostfix());
    }

    @Test
    public void testBuildFreeDealPromoDetailInfo() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setFreeDeal(true);
        DealGroupBaseDTO dealGroupBaseDTO = new DealGroupBaseDTO();
        dealGroupBaseDTO.setDealGroupPrice(new BigDecimal("10.2"));
        dealGroupBaseDTO.setMarketPrice(new BigDecimal("10.2"));
        ctx.setDealGroupBase(dealGroupBaseDTO);
        DealGroupPBO result = new DealGroupPBO();
        FreeDealConfig freeDealConfig = new FreeDealConfig();
        DescriptionTag descriptionTag = new DescriptionTag();
        freeDealConfig.setPriceTags(Lists.newArrayList(descriptionTag));
        freeDealConfig.setShowMarketPrice(true);
        ctx.setFreeDealConfig(freeDealConfig);

        ParallDealBuilderProcessor processor = new ParallDealBuilderProcessor();
        // act
        processor.buildFreeDealPromoDetailInfo(ctx, result);
        // assert
        assertNotNull(result.getPromoDetailModule());
    }

    @Test
    public void testBuildModuleConfigByRequestSource(){
        // LionConfigUtils.getPagesourceMapKeyValueMap()
        // {"create_order_preview":{"key":"preview","value":"create_order_preview"}}
        Map<String, ModuleConfigDo> map = new HashMap<>();
        ModuleConfigDo moduleConfigDo = new ModuleConfigDo();
        moduleConfigDo.setKey("preview");
        moduleConfigDo.setValue("create_order_preview");
        map.put("create_order_preview", moduleConfigDo);

        PowerMockito.mockStatic(Lion.class);
        when(Lion.getMap(anyString(), anyString(), any(),anyMap())).thenAnswer((Answer<Map<String, ModuleConfigDo>>) invocation -> map);

        ctx.setRequestSource("create_order_preview");
        parallDealBuilderProcessor.buildModuleConfigByRequestSource(ctx, result);
        parallDealBuilderProcessor.getPreviewInfoByRequestSource("create_order_preview");
        //assert
        assertNotNull(result);
    }


    @Test
    public void testBuildMLiveInfoVo() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx dealCtx = new DealCtx(envCtx);
        dealCtx.setMLiveChannel(true);
        dealCtx.setMLiveInfoVo(new MLiveInfoVo());
        DealGroupPBO dealGroupPBO = new DealGroupPBO();
        dealGroupPBO.setMLiveInfoVo(new MLiveInfoVo());
        parallDealBuilderProcessor.buildMLiveInfoVo(dealCtx, dealGroupPBO);
        Assert.assertTrue(dealGroupPBO.getMLiveInfoVo().isChannelIdentity());
    }
    @Test
    public void testMiniProgramCostEffective() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setRequestSource(RequestSourceEnum.HOME_PAGE.getSource());
        boolean result = parallDealBuilderProcessor.notMiniProgramCostEffective(ctx);
        Assert.assertTrue(result);
    }


    @Test
    public void testHandleJumpUrlForMiniProgram(){
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(MEITUAN_APP);
        DealCtx ctx1 = new DealCtx(envCtx);
        ModuleAbConfig value = new ModuleAbConfig();
        Class clazz = parallDealBuilderProcessor.getClass();
        Method handleJumpUrlForMiniProgram;
        try {
            handleJumpUrlForMiniProgram = clazz.getDeclaredMethod("handleJumpUrlForMiniProgram", FeaturesLayer.class, DealCtx.class);
            handleJumpUrlForMiniProgram.setAccessible(true);
            FeaturesLayer featuresLayer = new FeaturesLayer();
            handleJumpUrlForMiniProgram.invoke(parallDealBuilderProcessor, featuresLayer, ctx1);
            List<LayerConfig> layerConfigs = Lists.newArrayList();
            LayerConfig layerConfig = new LayerConfig();
            layerConfig.setDesc("desc");
            layerConfig.setJumpUrl("jumpurl");
            layerConfig.setIcon("icon");
            layerConfigs.add(layerConfig);
            featuresLayer.setLayerConfigs(layerConfigs);
            handleJumpUrlForMiniProgram.invoke(parallDealBuilderProcessor, featuresLayer, ctx1);
        } catch (NoSuchMethodException e) {

        } catch (InvocationTargetException e) {

        } catch (IllegalAccessException e) {

        }
        Assert.assertNotNull( result);
    }


    /**
     * 测试场景：当categoryId不等于324且result的PromoDetailModule为null时，不执行任何操作
     */
    @Test
    public void testSetCopiesForTeamBuild_NoAction() {
        // arrange
        when(ctx.getCategoryId()).thenReturn(325);

        // act
        parallDealBuilderProcessor.setCopiesForGroupBuild(ctx, result);

        // assert
        assertNotNull(result);
    }

    /**
     * 测试场景：当categoryId等于324且limit_the_number_of_users为1时，设置copies为"/人"
     */
    @Test
    public void testSetCopiesForTeamBuild_LimitOne() {
        // arrange
        when(ctx.getCategoryId()).thenReturn(324);
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attr = new AttributeDTO();
        attr.setName("limit_the_number_of_users");
        attr.setValue(Collections.singletonList("11"));
        attrs.add(attr);
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        result.setPromoDetailModule(promoDetailModule);

        when(ctx.getAttrs()).thenReturn(attrs);
//        when(result.getPromoDetailModule()).thenReturn(promoDetailModule);

        // act
        parallDealBuilderProcessor.setCopiesForGroupBuild(ctx, result);

        // assert
        assertEquals("/团", result.getPromoDetailModule().getCopies());
    }

    /**
     * 测试场景：当categoryId等于324且limit_the_number_of_users不为1时，设置copies为"/团"
     */
    @Test
    public void testSetCopiesForTeamBuild_LimitNotOne() {
        // arrange
        when(ctx.getCategoryId()).thenReturn(324);
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attr = new AttributeDTO();
        attr.setName("limit_the_number_of_users");
        attr.setValue(Collections.singletonList("11"));
        attrs.add(attr);
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        result.setPromoDetailModule(promoDetailModule);
        when(ctx.getAttrs()).thenReturn(attrs);
        // act
        parallDealBuilderProcessor.setCopiesForGroupBuild(ctx, result);
        // assert
        assertEquals("/团", result.getPromoDetailModule().getCopies());
    }


    @Test
    public void testGetDescriptionTags() throws Exception{
        Method method = ParallDealBuilderProcessor.class.getDeclaredMethod("getDescriptionTags", DealCtx.class);
        method.setAccessible(true);
        DealCtx dealCtx = new DealCtx(new EnvCtx());
        List<DescriptionTag> list = new ArrayList<>();
        DescriptionTag descriptionTag = new DescriptionTag();
        descriptionTag.setName("预付金 ￥${str}");
        list.add(descriptionTag);
        when(Lion.getList(anyString(), anyString(),any())).thenAnswer((Answer<List>) invocation -> list);
        DealGroupBaseDTO dealGroupBaseDTO = new DealGroupBaseDTO();
        dealGroupBaseDTO.setDealGroupPrice(new BigDecimal("120"));
        dealCtx.setDealGroupBase(dealGroupBaseDTO);
        boolean result;
        try {
            method.invoke(parallDealBuilderProcessor, dealCtx);
            result = true;
        } catch (Exception e) {
            result = false;
        }
        Assert.assertTrue(result);
    }

    /**
     * 测试 getFeaturesLayer 方法，当 ctx.getLEInsuranceAgreementEnum() 不为 null 时，应调用 addLEInsuranceAgreementLayerConfig 方法。
     */
    @Test
    public void testGetFeaturesLayer_LEInsuranceAgreementEnumNotNull() {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
        dealGroupCategoryDTO.setServiceTypeId(1L);
        dealGroupDTO.setCategory(dealGroupCategoryDTO);
        when(ctx.getLEInsuranceAgreementEnum()).thenReturn(LEInsuranceAgreementEnum.SAFE_WASHING);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.isWxMini()).thenReturn(false);

        // act
        FeaturesLayer result = parallDealBuilderProcessor.getFeaturesLayer(ctx, false);

        // assert
        assertNotNull(result);
    }

    @Test
    public void buildReassuredRepairPromoDetailModuleTest() throws Exception {
        Method method = ParallDealBuilderProcessor.class.getDeclaredMethod("buildReassuredRepairPromoDetailModule", DealCtx.class, PriceDisplayDTO.class, PromoDetailModule.class);
        method.setAccessible(true);
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        priceDisplayDTO.setMarketPrice(new BigDecimal("1"));
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        ctx.setDealGroupDTO(dealGroupDTO);
        DealGroupServiceProjectDTO dealGroupServiceProjectDTO = new DealGroupServiceProjectDTO();
        List<MustServiceProjectGroupDTO> mustGroups = new ArrayList<>();
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        List<ServiceProjectDTO> groups = new ArrayList<>();
        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();

        groups.add(serviceProjectDTO);
        mustGroup.setGroups(groups);
        mustGroups.add(mustGroup);
        dealGroupServiceProjectDTO.setMustGroups(mustGroups);
        dealGroupDTO.setServiceProject(dealGroupServiceProjectDTO);

        // 创建标签
        DealGroupTagDTO tag = new DealGroupTagDTO();
        tag.setId(100218044L); // 假设这是你要检查的标签ID

        // 创建标签列表并添加标签
        List<DealGroupTagDTO> tags = new ArrayList<>();
        tags.add(tag);

        dealGroupDTO.setTags(tags);
        boolean result;
        try {
            method.invoke(parallDealBuilderProcessor, ctx, priceDisplayDTO, promoDetailModule);
            result = true;
        } catch (Exception e) {
            result = false;
        }
        Assert.assertTrue(result);
    }

    @Test
    public void buildBuildDealPromoDetailInfoTest() throws Exception {
        Method method = ParallDealBuilderProcessor.class.getDeclaredMethod("buildDealPromoDetailInfo", DealCtx.class, PriceDisplayDTO.class, DealGroupPBO.class);
        method.setAccessible(true);
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        priceDisplayDTO.setMarketPrice(new BigDecimal("1"));
        List<PromoDTO> promos = new ArrayList<>();
        PromoDTO promoDTO = new PromoDTO();
        promos.add(promoDTO);
        priceDisplayDTO.setUsedPromos(promos);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(MEITUAN_APP);
        DealCtx ctx = new DealCtx(envCtx);
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setSceneType(1);
        ctx.setCostEffectivePinTuan(costEffectivePinTuan);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        ctx.setDealGroupDTO(dealGroupDTO);
        DealGroupServiceProjectDTO dealGroupServiceProjectDTO = new DealGroupServiceProjectDTO();
        List<MustServiceProjectGroupDTO> mustGroups = new ArrayList<>();
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        List<ServiceProjectDTO> groups = new ArrayList<>();
        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();

        groups.add(serviceProjectDTO);
        mustGroup.setGroups(groups);
        mustGroups.add(mustGroup);
        dealGroupServiceProjectDTO.setMustGroups(mustGroups);
        dealGroupDTO.setServiceProject(dealGroupServiceProjectDTO);

        // 创建标签
        DealGroupTagDTO tag = new DealGroupTagDTO();
        tag.setId(10021804L); // 假设这是你要检查的标签ID

        // 创建标签列表并添加标签
        List<DealGroupTagDTO> tags = new ArrayList<>();
        tags.add(tag);

        dealGroupDTO.setTags(tags);

        boolean result;
        try {
            method.invoke(parallDealBuilderProcessor, ctx, priceDisplayDTO, new DealGroupPBO());
            result = true;
        } catch (Exception e) {
            result = false;
        }
        Assert.assertTrue(result);
    }


    /**
     * 测试 buildSelfOwnBranchLayoutConfig 方法返回的列表不为空
     */
    @Test
    public void testBuildSelfOwnBranchLayoutConfig_NotNull() {
        // arrange

        // act
        List<LayerConfig> result = parallDealBuilderProcessor.buildSelfOwnBranchLayoutConfig();

        // assert
        assertNotNull("结果不应该为null", result);
    }

    /**
     * 测试 buildSelfOwnBranchLayoutConfig 方法返回的列表大小为3
     */
    @Test
    public void testBuildSelfOwnBranchLayoutConfig_Size() {
        // arrange

        // act
        List<LayerConfig> result = parallDealBuilderProcessor.buildSelfOwnBranchLayoutConfig();

        // assert
        assertEquals("列表大小应该为3", 3, result.size());
    }

    /**
     * 测试 buildSelfOwnBranchLayoutConfig 方法返回的列表中每个LayerConfig的icon不为空
     */
    @Test
    public void testBuildSelfOwnBranchLayoutConfig_IconNotNull() {
        // arrange

        // act
        List<LayerConfig> result = parallDealBuilderProcessor.buildSelfOwnBranchLayoutConfig();

        // assert
        result.forEach(layerConfig -> assertNotNull("icon不应该为null", layerConfig.getIcon()));
    }

    /**
     * 测试 buildSelfOwnBranchLayoutConfig 方法返回的列表中每个LayerConfig的title不为空
     */
    @Test
    public void testBuildSelfOwnBranchLayoutConfig_TitleNotNull() {
        // arrange

        // act
        List<LayerConfig> result = parallDealBuilderProcessor.buildSelfOwnBranchLayoutConfig();

        // assert
        result.forEach(layerConfig -> assertNotNull("title不应该为null", layerConfig.getTitle()));
    }

    /**
     * 测试 buildSelfOwnBranchLayoutConfig 方法返回的列表中每个LayerConfig的desc不为空
     */
    @Test
    public void testBuildSelfOwnBranchLayoutConfig_DescNotNull() {
        // arrange

        // act
        List<LayerConfig> result = parallDealBuilderProcessor.buildSelfOwnBranchLayoutConfig();

        // assert
        result.forEach(layerConfig -> assertNotNull("desc不应该为null", layerConfig.getDesc()));
    }




    /**
     * 测试当ctx为null时的场景
     */
    @Test(expected = NullPointerException.class)
    public void testAddLEInsuranceAgreementLayerConfig_NullCtx() {
        parallDealBuilderProcessor.addLEInsuranceAgreementLayerConfig(featuresLayer, null);
    }

    /**
     * 测试当featuresLayer为null时的场景
     */
    @Test(expected = NullPointerException.class)
    public void testAddLEInsuranceAgreementLayerConfig_NullFeaturesLayer() {
        parallDealBuilderProcessor.addLEInsuranceAgreementLayerConfig(null, ctx);
    }


    /**
     * 测试当config为null时的场景
     */
    @Test
    public void testGetSelfSceneWhenConfigIsNull() {
        List<LifeClearHaiMaConfig> list = new ArrayList<>();
        LifeClearHaiMaConfig configItem = new LifeClearHaiMaConfig();
        configItem.setId("123");
        list.add(configItem);
        String str ="{\n" +
                "  \"enable\": true,\n" +
                "  \"sceneKey\": \"exampleSceneKey\",\n" +
                "  \"configId\": \"config123\",\n" +
                "  \"contentId\": \"content456\",\n" +
                "  \"excludeIds\": [\n" +
                "    \"id1\",\n" +
                "    \"id2\",\n" +
                "    \"id3\"\n" +
                "  ],\n" +
                "  \"excludeNames\": [\n" +
                "    \"name1\",\n" +
                "    \"name2\",\n" +
                "    \"name3\"\n" +
                "  ],\n" +
                "  \"firstTabName\": \"exampleFirstTab\"\n" +
                "}\n";
        CleaningProductLionConfig config  = JSON.parseObject(str, CleaningProductLionConfig.class);
        String result = lifeClearUtil.getSelfScene(list, 123, null);
        assertEquals(null, result);
    }

    /**
     * 测试config启用，但list为空的场景
     */
    @Test
    public void testGetSelfSceneWhenListIsEmpty() {
        CleaningProductLionConfig config = new CleaningProductLionConfig();
        config.setEnable(true);
        String str ="{\n" +
                "  \"enable\": true,\n" +
                "  \"sceneKey\": \"exampleSceneKey\",\n" +
                "  \"configId\": \"config123\",\n" +
                "  \"contentId\": \"content456\",\n" +
                "  \"excludeIds\": [\n" +
                "    \"id1\",\n" +
                "    \"id2\",\n" +
                "    \"id3\"\n" +
                "  ],\n" +
                "  \"excludeNames\": [\n" +
                "    \"name1\",\n" +
                "    \"name2\",\n" +
                "    \"name3\"\n" +
                "  ],\n" +
                "  \"firstTabName\": \"exampleFirstTab\"\n" +
                "}\n";
         config  = JSON.parseObject(str, CleaningProductLionConfig.class);
        String result = lifeClearUtil.getSelfScene(new ArrayList<>(), 123, config);
        assertEquals(null, result);
    }

    /**
     * 测试config启用，list不为空，但不包含指定mtId的场景
     */
    @Test
    public void testGetSelfSceneWhenListDoesNotContainMtId() {
        List<LifeClearHaiMaConfig> list = new ArrayList<>();
        LifeClearHaiMaConfig configItem = new LifeClearHaiMaConfig();
        configItem.setId("999");
        list.add(configItem);

        CleaningProductLionConfig config = new CleaningProductLionConfig();
        config.setEnable(true);
        String str ="{\n" +
                "  \"enable\": true,\n" +
                "  \"sceneKey\": \"exampleSceneKey\",\n" +
                "  \"configId\": \"config123\",\n" +
                "  \"contentId\": \"content456\",\n" +
                "  \"excludeIds\": [\n" +
                "    \"id1\",\n" +
                "    \"id2\",\n" +
                "    \"id3\"\n" +
                "  ],\n" +
                "  \"excludeNames\": [\n" +
                "    \"name1\",\n" +
                "    \"name2\",\n" +
                "    \"name3\"\n" +
                "  ],\n" +
                "  \"firstTabName\": \"exampleFirstTab\"\n" +
                "}\n";
         config  = JSON.parseObject(str, CleaningProductLionConfig.class);
        String result = lifeClearUtil.getSelfScene(list, 123, config);
        assertEquals(null, result);
    }

    /**
     * 测试config启用，list包含指定mtId的场景
     */
    @Test
    public void testGetSelfSceneWhenListContainsMtId() {
        List<LifeClearHaiMaConfig> list = new ArrayList<>();
        LifeClearHaiMaConfig configItem = new LifeClearHaiMaConfig();
        configItem.setId("123");
        list.add(configItem);

        CleaningProductLionConfig config = new CleaningProductLionConfig();
        config.setEnable(true);
        String str ="{\n" +
                "  \"enable\": true,\n" +
                "  \"sceneKey\": \"exampleSceneKey\",\n" +
                "  \"configId\": \"config123\",\n" +
                "  \"contentId\": \"content456\",\n" +
                "  \"excludeIds\": [\n" +
                "    \"id1\",\n" +
                "    \"id2\",\n" +
                "    \"id3\"\n" +
                "  ],\n" +
                "  \"excludeNames\": [\n" +
                "    \"name1\",\n" +
                "    \"name2\",\n" +
                "    \"name3\"\n" +
                "  ],\n" +
                "  \"firstTabName\": \"exampleFirstTab\"\n" +
                "}\n";
         config  = JSON.parseObject(str, CleaningProductLionConfig.class);
        String result = lifeClearUtil.getSelfScene(list, 123, config);
        assertEquals("clearSelf", result);
    }

    /**
     * 测试config未启用的场景
     */
    @Test
    public void testGetSelfSceneWhenConfigIsNotEnabled() {
        List<LifeClearHaiMaConfig> list = new ArrayList<>();
        LifeClearHaiMaConfig configItem = new LifeClearHaiMaConfig();
        configItem.setId("123");
        list.add(configItem);

        CleaningProductLionConfig config = new CleaningProductLionConfig();
        config.setEnable(false);
        String str ="{\n" +
                "  \"enable\": true,\n" +
                "  \"sceneKey\": \"exampleSceneKey\",\n" +
                "  \"configId\": \"config123\",\n" +
                "  \"contentId\": \"content456\",\n" +
                "  \"excludeIds\": [\n" +
                "    \"id1\",\n" +
                "    \"id2\",\n" +
                "    \"id3\"\n" +
                "  ],\n" +
                "  \"excludeNames\": [\n" +
                "    \"name1\",\n" +
                "    \"name2\",\n" +
                "    \"name3\"\n" +
                "  ],\n" +
                "  \"firstTabName\": \"exampleFirstTab\"\n" +
                "}\n";
         config  = JSON.parseObject(str, CleaningProductLionConfig.class);
        String result = lifeClearUtil.getSelfScene(list, 123, null);
        assertEquals(null, result);
    }

    @Test
    public void testGetCouponAlleviate1ExpResult_NotEmpty() throws InvocationTargetException, IllegalAccessException {
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setKey("MtCouponAlleviate1Exp");
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("c");
        abConfig.setExpId("123");
        moduleAbConfig.setConfigs(Lists.newArrayList(abConfig));

        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(MEITUAN_APP);
        ctx = new DealCtx(envCtx);

        ctx.setModuleAbConfigs(Lists.newArrayList(moduleAbConfig));


        Method method = PowerMockito.method(ParallDealBuilderProcessor.class, "getCouponAlleviate1ExpResult");
        boolean result = (boolean) method.invoke(parallDealBuilderProcessor, ctx);
        assertTrue(result);
    }

    @Test
    public void testGetCouponAlleviate1ExpResult_Empty() throws InvocationTargetException, IllegalAccessException {
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setKey("MtCouponAlleviate1Exp");
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("c");
        abConfig.setExpId("123");
        moduleAbConfig.setConfigs(Lists.newArrayList(abConfig));


        Method method = PowerMockito.method(ParallDealBuilderProcessor.class, "getCouponAlleviate1ExpResult");
        boolean result = (boolean) method.invoke(parallDealBuilderProcessor, ctx);
        assertTrue(!result);
    }

    @Test
    public void testConvertPromoDesc() {
        PromoDTO promoDTO1 = JacksonUtils.deserialize(magicPromoJsonNoLimit, PromoDTO.class);
        PromoDetailModule promoDetailModule = mock(PromoDetailModule.class);
        String promoTag = "团购优惠";
        Mockito.when(promoDetailModule.isPromoNewStyle()).thenReturn(true);
        String result1 = parallDealBuilderProcessor.convertPromoDesc(promoDTO1, promoDetailModule,promoTag);
        Assert.assertTrue(StringUtils.equals(result1,"无门槛"));

        PromoDTO promoDTO2 = JacksonUtils.deserialize(magicPromoJson, PromoDTO.class);
        String result2 = parallDealBuilderProcessor.convertPromoDesc(promoDTO2, promoDetailModule,promoTag);
        Assert.assertTrue(StringUtils.equals(result2,"满89元减13元"));

        // when(promoDetailModule.isPromoNewStyle()).thenReturn(false);
        PromoDetailModule promoDetailModule2 = mock(PromoDetailModule.class);
        Mockito.when(promoDetailModule2.isPromoNewStyle()).thenReturn(false);
        String result3 = parallDealBuilderProcessor.convertPromoDesc(promoDTO1, promoDetailModule2,promoTag);
        Assert.assertTrue(StringUtils.equals(result3,"吃喝玩乐红包 "));

    }

    @Test
    public void testSubtractPromoDesc() {
        String promoDesc = "团购优惠，下单立省112";
        String promoTag = "团购优惠";
        String result = parallDealBuilderProcessor.subtractPromoDesc(promoDesc, promoTag);
        Assert.assertTrue(StringUtils.equals(result,"下单立省112"));
    }

    @Test
    public void testBuildSubTitleList() throws InvocationTargetException, IllegalAccessException {
        Method method = PowerMockito.method(ParallDealBuilderProcessor.class, "buildSubTitleList");
        when(ctx.getCategoryId()).thenReturn(300);
        when(ctx.getAutoOpenTable()).thenReturn(true);
        List<SubTitleVO> result = (List<SubTitleVO>) method.invoke(parallDealBuilderProcessor,ctx);
        assert !result.isEmpty();
    }

    private static final String magicPromoJsonNoLimit = "{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PromoDTO\",\"identity\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PromoIdentity\",\"promoId\":1544834328,\"promoType\":5,\"promoTypeDesc\":\"吃喝玩乐红包 \",\"sourceType\":1,\"promoShowType\":\"MAGICAL_MEMBER_PLATFORM_COUPON\"},\"amount\":[\"java.math.BigDecimal\",5],\"afterPromoPrice\":null,\"tag\":null,\"description\":null,\"consumeTimeDesc\":null,\"priceThrough\":true,\"canAssign\":false,\"couponAssignStatus\":null,\"detail\":null,\"extendDesc\":\"吃喝玩乐红包 \",\"startTime\":[\"java.util.Date\",1723901715000],\"endTime\":[\"java.util.Date\",1726502399000],\"promoDiscount\":null,\"promoQuantityLimit\":null,\"icon\":\"https://p0.meituan.net/travelcube/d5ba4048dc741674e6497f4c0c9ef55e1173.png\",\"couponTitle\":\"吃喝玩乐红包 \",\"totalStock\":null,\"remainStock\":null,\"priceLimitDesc\":\"无门槛\",\"minConsumptionAmount\":[\"java.math.BigDecimal\",0],\"useTimeDesc\":\"2024.08.17-2024.09.16\",\"promoIdentity\":\"magicalMemberCoupon\",\"promoStatus\":0,\"couponGroupId\":\"1544834328\",\"couponId\":\"401957102593951526\",\"couponValueType\":0,\"couponValueText\":\"5\",\"effectiveStartTime\":null,\"effectiveEndTime\":null,\"marketTag\":null,\"useType\":null,\"combinationId\":null,\"promoStock\":null,\"newActivityGroupId\":null,\"promoTextDTO\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PromoTextDTO\",\"title\":\"5元无门槛券\",\"subTitle\":\"吃喝玩乐红包 \",\"icon\":\"https://p0.meituan.net/travelcube/d5ba4048dc741674e6497f4c0c9ef55e1173.png\",\"promoDivideType\":\"MAGICAL_MEMBER_PLATFORM_COUPON\",\"promoDivideTypeDesc\":\"神会员平台券\",\"shelfTitle\":null,\"shelfSubTitle\":null,\"shelfIcon\":null,\"atmosphereBarIcon\":null,\"atmosphereBarText\":null,\"atmosphereBarButtonText\":null,\"atmosphereBarButtonUrl\":null,\"promoStatusText\":null},\"reductionSaleChannels\":null,\"amountShareDetail\":{\"@class\":\"java.util.HashMap\",\"{\\\"@class\\\":\\\"java.lang.Integer\\\",\\\"value\\\":1}\":[\"java.math.BigDecimal\",5],\"{\\\"@class\\\":\\\"java.lang.Integer\\\",\\\"value\\\":2}\":[\"java.math.BigDecimal\",0]},\"promotionExplanatoryTags\":[\"java.util.ArrayList\",[3]],\"promotionOtherInfoMap\":{\"@class\":\"java.util.HashMap\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"ASSIGNED_STATUS\\\"}\":\"ALREADY_ASSIGNED\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"COUPON_PACKAGE_ID\\\"}\":\"DOQZGcd1IgRdGTg43J8TWGAH/W4dkrcFIidROAFcDVA=\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"ASSET_TYPE\\\"}\":\"1\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"TSP_COUPON_GROUP_ID\\\"}\":\"22270241211401\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"TSP_COUPON_ID\\\"}\":\"1266521391341\"},\"promotionDisplayTextMap\":{\"@class\":\"java.util.HashMap\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"topLeftIcon\\\"}\":\"https://p0.meituan.net/ingee/6f4c76f970849530aadf296332059e0d11217.png\"},\"newUser\":false}";
    private static final String magicPromoJson = "{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PromoDTO\",\"identity\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PromoIdentity\",\"promoId\":1110952426,\"promoType\":5,\"promoTypeDesc\":\"丽人通用神券\",\"sourceType\":1,\"promoShowType\":\"MAGICAL_MEMBER_PLATFORM_COUPON\"},\"amount\":[\"java.math.BigDecimal\",13],\"afterPromoPrice\":null,\"tag\":null,\"description\":null,\"consumeTimeDesc\":null,\"priceThrough\":true,\"canAssign\":false,\"couponAssignStatus\":null,\"detail\":null,\"extendDesc\":\"丽人通用神券\",\"startTime\":[\"java.util.Date\",1725593261000],\"endTime\":[\"java.util.Date\",1725679661000],\"promoDiscount\":null,\"promoQuantityLimit\":null,\"icon\":\"https://p0.meituan.net/travelcube/d5ba4048dc741674e6497f4c0c9ef55e1173.png\",\"couponTitle\":\"丽人通用神券\",\"totalStock\":null,\"remainStock\":null,\"priceLimitDesc\":\"满89可用\",\"minConsumptionAmount\":[\"java.math.BigDecimal\",89],\"useTimeDesc\":\"一天内过期\",\"promoIdentity\":\"freeMagicalMemberCoupon\",\"promoStatus\":0,\"couponGroupId\":\"1110952426\",\"couponId\":\"401957102737635687\",\"couponValueType\":2,\"couponValueText\":\"13\",\"effectiveStartTime\":null,\"effectiveEndTime\":null,\"marketTag\":null,\"useType\":null,\"combinationId\":null,\"promoStock\":null,\"newActivityGroupId\":null,\"promoTextDTO\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PromoTextDTO\",\"title\":\"满89减13\",\"subTitle\":\"丽人通用神券\",\"icon\":\"https://p0.meituan.net/travelcube/d5ba4048dc741674e6497f4c0c9ef55e1173.png\",\"promoDivideType\":\"MAGICAL_MEMBER_PLATFORM_COUPON\",\"promoDivideTypeDesc\":\"神会员平台券\",\"shelfTitle\":null,\"shelfSubTitle\":null,\"shelfIcon\":null,\"atmosphereBarIcon\":null,\"atmosphereBarText\":null,\"atmosphereBarButtonText\":null,\"atmosphereBarButtonUrl\":null,\"promoStatusText\":\"未膨胀\"},\"reductionSaleChannels\":null,\"amountShareDetail\":{\"@class\":\"java.util.HashMap\",\"{\\\"@class\\\":\\\"java.lang.Integer\\\",\\\"value\\\":1}\":[\"java.math.BigDecimal\",13],\"{\\\"@class\\\":\\\"java.lang.Integer\\\",\\\"value\\\":2}\":[\"java.math.BigDecimal\",0]},\"promotionExplanatoryTags\":[\"java.util.ArrayList\",[4]],\"promotionOtherInfoMap\":{\"@class\":\"java.util.HashMap\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"CAN_INFLATE\\\"}\":\"true\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"BIZ_TOKEN\\\"}\":\"C2zK8qd7+g7BqJCgt5/rFjmoL8S9ENvuj9cIAdveXIUKq1PtYMumUuGebmnLvDbY\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"ASSIGNED_STATUS\\\"}\":\"ALREADY_ASSIGNED\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"AFTER_INFLATE_REQUIRE_AMOUNT\\\"}\":\"8900\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"ASSET_TYPE\\\"}\":\"2\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"AFTER_INFLATE_MONEY\\\"}\":\"1500\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"NIB_BIZ_LINE\\\"}\":\"202002\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"QUERY_INFLATE_FLAG\\\"}\":\"true\"},\"promotionDisplayTextMap\":{\"@class\":\"java.util.HashMap\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"promotionButtonText\\\"}\":\"免费膨胀\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"topLeftIcon\\\"}\":\"https://p0.meituan.net/ingee/0b375017c95f811adfb233e0774d4f524980.png\"},\"newUser\":false}";




    @Test
    public void testIsQualityEducationCategoryByCategoryId_Exists() {
        try (MockedStatic<Lion> mockedStatic = Mockito.mockStatic(Lion.class)) {
            // arrange
            int categoryId = 1;
            List<Integer> mockCategoryIds = Arrays.asList(1, 2, 3);
            mockedStatic.when(() -> Lion.getList(Mockito.anyString(), Mockito.anyString(), Mockito.eq(Integer.class), Mockito.anyList())).thenReturn(mockCategoryIds);
            // act
            boolean result = qualityEducationUtil.isQualityEducationCategoryByCategoryId(categoryId);
            // assert
            Assert.assertTrue(result);
        }
    }

    @Test
    public void testIsQualityEducationCategoryByCategoryId_Exists_False() {
        try (MockedStatic<Lion> mockedStatic = Mockito.mockStatic(Lion.class)) {
            // arrange
            int categoryId = 1111;
            List<Integer> mockCategoryIds = Arrays.asList(1, 2, 3);
            mockedStatic.when(() -> Lion.getList(Mockito.anyString(), Mockito.anyString(), Mockito.eq(Integer.class), Mockito.anyList())).thenReturn(mockCategoryIds);
            // act
            boolean result = qualityEducationUtil.isQualityEducationCategoryByCategoryId(categoryId);
            // assert
            Assert.assertFalse(result);
        }
    }

    @Test
    public void isQualityEducationCategoryByServiceTypeId_true() {
        try (MockedStatic<Lion> mockedStatic = Mockito.mockStatic(Lion.class)) {
            // arrange
            Long categoryId = 1L;
            List<Long> mockCategoryIds = Arrays.asList(1L, 2L, 3L);
            mockedStatic.when(() -> Lion.getList(Mockito.anyString(), Mockito.anyString(), Mockito.eq(Long.class), Mockito.anyList())).thenReturn(mockCategoryIds);
            // act
            boolean result = qualityEducationUtil.isQualityEducationCategoryByServiceTypeId(categoryId);
            // assert
            Assert.assertTrue(result);
        }
    }

    @Test
    public void isQualityEducationCategoryByServiceTypeId_false() {
        try (MockedStatic<Lion> mockedStatic = Mockito.mockStatic(Lion.class)) {
            // arrange
            Long categoryId = 1111L;
            List<Long> mockCategoryIds = Arrays.asList(1L, 2L, 3L);
            mockedStatic.when(() -> Lion.getList(Mockito.anyString(), Mockito.anyString(), Mockito.eq(Long.class), Mockito.anyList())).thenReturn(mockCategoryIds);
            // act
            boolean result = qualityEducationUtil.isQualityEducationCategoryByServiceTypeId(categoryId);
            // assert
            Assert.assertFalse(result);
        }
    }


    @Test
    public void isQualityEducationCategoryTest_true() {
        try (MockedStatic<Lion> mockedStatic = Mockito.mockStatic(Lion.class)) {
            DealCtx ctx = new DealCtx(new EnvCtx());
            DealGroupDTO dealGroup = new DealGroupDTO();
            DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
            dealGroupCategoryDTO.setCategoryId(512L);
            dealGroupCategoryDTO.setServiceTypeId(1373L);
            dealGroup.setCategory(dealGroupCategoryDTO);
            List<DealGroupTagDTO> tagDTOs = new ArrayList<>();
            DealGroupTagDTO dealGroupTagDTO = new DealGroupTagDTO();
            dealGroupTagDTO.setId(1L);
            tagDTOs.add(dealGroupTagDTO);
            dealGroup.setTags(tagDTOs);
            ctx.setDealGroupDTO(dealGroup);

            when(Lion.getList(anyString(), eq(LionConstants.QUALITY_EDU_CATION_BY_CATEGORY_IDS), eq(Integer.class), anyList()))
                    .thenReturn(Arrays.asList(512)); // 模拟返回一个包含512的列表
            when(Lion.getList(anyString(), eq(LionConstants.QUALITY_EDU_CATION_BY_SERVICE_TYPE_IDS), eq(Long.class), anyList()))
                    .thenReturn(Arrays.asList(1373L)); // 模拟返回一个包含1373L的列表
            Boolean bl = qualityEducationUtil.isQualityEducationCategory(ctx);
            Assert.assertFalse(bl);
        }
    }




    @Test
    public void testIsQualityEducationCategory_CategoryIdMatch_ServiceTypeIdNotMatch() {
        try (MockedStatic<Lion> mockedStatic = Mockito.mockStatic(Lion.class)) {
            // 初始化上下文和类目信息
            DealCtx ctx = new DealCtx(new EnvCtx());
            DealGroupDTO dealGroup = new DealGroupDTO();
            DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
            dealGroupCategoryDTO.setCategoryId(512L);
            dealGroupCategoryDTO.setServiceTypeId(13730L);
            dealGroup.setCategory(dealGroupCategoryDTO);
            List<DealGroupTagDTO> tagDTOs = new ArrayList<>();
            DealGroupTagDTO dealGroupTagDTO = new DealGroupTagDTO();
            dealGroupTagDTO.setId(1L);
            tagDTOs.add(dealGroupTagDTO);
            dealGroup.setTags(tagDTOs);
            ctx.setDealGroupDTO(dealGroup);
            // 配置模拟行为：二级类目符合条件
            PowerMockito.mockStatic(Lion.class);
            when(Lion.getList(anyString(), eq(LionConstants.QUALITY_EDU_CATION_BY_CATEGORY_IDS), eq(Integer.class), anyList()))
                    .thenReturn(Arrays.asList(0)); // 模拟返回一个包含512的列表
            when(Lion.getList(anyString(), eq(LionConstants.QUALITY_EDU_CATION_BY_SERVICE_TYPE_IDS), eq(Long.class), anyList()))
                    .thenReturn(Arrays.asList(1373L)); // 模拟返回一个包含1373L的列表
            // 断言：验证是否为优质教育类目
            assertTrue(qualityEducationUtil.isQualityEducationCategory(ctx));
        }
    }

    /**
     * 测试输入为空时的情况
     */
    @Test
    public void testFormatToRichText_InputEmpty() {
        List<String> result = ParallDealBuilderProcessorUtils.formatToRichText("", 10, 5);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试输入为null时的情况
     */
    @Test
    public void testFormatToRichText_InputNull() {
        List<String> result = ParallDealBuilderProcessorUtils.formatToRichText(null, 10, 5);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试正常情况下的字符串分割
     */
    @Test
    public void testFormatToRichText_NormalCase() {
        List<String> result = ParallDealBuilderProcessorUtils.formatToRichText("12345元67890元", 10, 5);
        assertEquals(2, result.size());
        assertEquals("12345元", result.get(0));
        assertEquals("67890元", result.get(1));
    }

    /**
     * 测试包含标点符号的字符串分割
     */
    @Test
    public void testFormatToRichText_WithPunctuation() {
        List<String> result = ParallDealBuilderProcessorUtils.formatToRichText("123，45。678？90！", 10, 5);
        assertEquals(4, result.size());
        assertEquals("123，", result.get(0));
        assertEquals("45。", result.get(1));
        assertEquals("678？", result.get(2));
        assertEquals("90！", result.get(3));
    }

    /**
     * 测试超出最大行数的情况
     */
    @Test
    public void testFormatToRichText_ExceedMaxLines() {
        List<String> result = ParallDealBuilderProcessorUtils.formatToRichText("12345678901234567890", 2, 5);
        assertEquals(1, result.size());
        assertEquals("12345678901234567890", result.get(0));
    }

    /**
     * 测试每行字符刚好等于最大字符数的情况
     */
    @Test
    public void testFormatToRichText_PerfectFit() {
        List<String> result = ParallDealBuilderProcessorUtils.formatToRichText("1234512345", 2, 5);
        assertEquals(1, result.size());
        assertEquals("1234512345", result.get(0));
    }

    /**
     * 测试包含价格表达式的字符串分割
     */
    @Test
    public void testFormatToRichText_WithPriceExpression() {
        List<String> result = ParallDealBuilderProcessorUtils.formatToRichText("购买1件减10元，2件减20元", 10, 10);
        assertEquals(2, result.size());
        assertEquals("购买1件减10元，2", result.get(0));
        assertEquals("件减20元", result.get(1));
    }

    /**
     * 测试输入字符串中包含多种类型字符的情况
     */
    @Test
    public void testFormatToRichText_MixedCharacters() {
        List<String> result = ParallDealBuilderProcessorUtils.formatToRichText("123，购买1件减10元！", 10, 10);
        assertEquals(2, result.size());
        assertEquals("123，购买1件减", result.get(0));
        assertEquals("10元！", result.get(1));
    }
}
