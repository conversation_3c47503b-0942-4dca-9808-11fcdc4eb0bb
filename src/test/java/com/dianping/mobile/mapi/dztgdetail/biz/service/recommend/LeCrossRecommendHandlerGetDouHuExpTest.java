package com.dianping.mobile.mapi.dztgdetail.biz.service.recommend;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.RelatedRecommendCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.LeCrossRecommendConfig;
import java.lang.reflect.Field;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.*;

public class LeCrossRecommendHandlerGetDouHuExpTest {

    private LeCrossRecommendHandler leCrossRecommendHandler;

    @Mock
    private DouHuBiz douHuBiz;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        leCrossRecommendHandler = new LeCrossRecommendHandler();
        // Use reflection to set private field
        Field douHuBizField = LeCrossRecommendHandler.class.getDeclaredField("douHuBiz");
        douHuBizField.setAccessible(true);
        douHuBizField.set(leCrossRecommendHandler, douHuBiz);
    }

    /**
     * Test case when the experiment is not enabled.
     */
    @Test
    public void testGetDouHuExpExperimentNotEnabled() throws Throwable {
        // arrange
        RelatedRecommendCtx relatedRecommendCtx = new RelatedRecommendCtx();
        EnvCtx envCtx = new EnvCtx();
        relatedRecommendCtx.setEnvCtx(envCtx);
        LeCrossRecommendConfig config = new LeCrossRecommendConfig();
        config.setEnableDouHu(false);
        config.setCategoryIds(Collections.singletonList(1));
        // act
        boolean result = leCrossRecommendHandler.getDouHuExp(relatedRecommendCtx, config);
        // assert
        assertTrue(result);
    }

    /**
     * Test case when categoryIds is empty.
     */
    @Test
    public void testGetDouHuExpCategoryIdsEmpty() throws Throwable {
        // arrange
        RelatedRecommendCtx relatedRecommendCtx = new RelatedRecommendCtx();
        EnvCtx envCtx = new EnvCtx();
        relatedRecommendCtx.setEnvCtx(envCtx);
        LeCrossRecommendConfig config = new LeCrossRecommendConfig();
        config.setEnableDouHu(true);
        config.setCategoryIds(Collections.emptyList());
        // act
        boolean result = leCrossRecommendHandler.getDouHuExp(relatedRecommendCtx, config);
        // assert
        assertFalse(result);
    }

    /**
     * Test case when experiment result is not found.
     */
    @Test
    public void testGetDouHuExpExperimentResultNotFound() throws Throwable {
        // arrange
        RelatedRecommendCtx relatedRecommendCtx = new RelatedRecommendCtx();
        EnvCtx envCtx = new EnvCtx();
        relatedRecommendCtx.setEnvCtx(envCtx);
        LeCrossRecommendConfig config = new LeCrossRecommendConfig();
        config.setEnableDouHu(true);
        config.setCategoryIds(Collections.singletonList(1));
        doReturn(null).when(douHuBiz).getAbExpResult(any(DealCtx.class), anyString());
        // act
        boolean result = leCrossRecommendHandler.getDouHuExp(relatedRecommendCtx, config);
        // assert
        assertFalse(result);
    }

    /**
     * Test case when experiment configs are empty.
     */
    @Test
    public void testGetDouHuExpExperimentConfigsEmpty() throws Throwable {
        // arrange
        RelatedRecommendCtx relatedRecommendCtx = new RelatedRecommendCtx();
        EnvCtx envCtx = new EnvCtx();
        relatedRecommendCtx.setEnvCtx(envCtx);
        LeCrossRecommendConfig config = new LeCrossRecommendConfig();
        config.setEnableDouHu(true);
        config.setCategoryIds(Collections.singletonList(1));
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setConfigs(Collections.emptyList());
        doReturn(moduleAbConfig).when(douHuBiz).getAbExpResult(any(DealCtx.class), anyString());
        // act
        boolean result = leCrossRecommendHandler.getDouHuExp(relatedRecommendCtx, config);
        // assert
        assertFalse(result);
    }

    /**
     * Test case when experiment result is valid.
     */
    @Test
    public void testGetDouHuExpExperimentResultValid() throws Throwable {
        // arrange
        RelatedRecommendCtx relatedRecommendCtx = new RelatedRecommendCtx();
        EnvCtx envCtx = new EnvCtx();
        relatedRecommendCtx.setEnvCtx(envCtx);
        LeCrossRecommendConfig config = new LeCrossRecommendConfig();
        config.setEnableDouHu(true);
        config.setCategoryIds(Collections.singletonList(1));
        config.setMtDouHuKey("mtKey");
        config.setDpDouHuKey("dpKey");
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpId("expId");
        abConfig.setExpResult("b");
        abConfig.setExpBiInfo("expBiInfo");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        moduleAbConfig.setKey("key");
        doReturn(moduleAbConfig).when(douHuBiz).getAbExpResult(any(DealCtx.class), anyString());
        // act
        boolean result = leCrossRecommendHandler.getDouHuExp(relatedRecommendCtx, config);
        // assert
        assertTrue(result);
    }

    /**
     * Test case when experiment result is invalid.
     */
    @Test
    public void testGetDouHuExpExperimentResultInvalid() throws Throwable {
        // arrange
        RelatedRecommendCtx relatedRecommendCtx = new RelatedRecommendCtx();
        EnvCtx envCtx = new EnvCtx();
        relatedRecommendCtx.setEnvCtx(envCtx);
        LeCrossRecommendConfig config = new LeCrossRecommendConfig();
        config.setEnableDouHu(true);
        config.setCategoryIds(Collections.singletonList(1));
        config.setMtDouHuKey("mtKey");
        config.setDpDouHuKey("dpKey");
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpId("expId");
        abConfig.setExpResult("c");
        abConfig.setExpBiInfo("expBiInfo");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        moduleAbConfig.setKey("key");
        doReturn(moduleAbConfig).when(douHuBiz).getAbExpResult(any(DealCtx.class), anyString());
        // act
        boolean result = leCrossRecommendHandler.getDouHuExp(relatedRecommendCtx, config);
        // assert
        assertFalse(result);
    }

    /**
     * Test case when an exception occurs.
     */
    @Test
    public void testGetDouHuExpExceptionOccurs() throws Throwable {
        // arrange
        RelatedRecommendCtx relatedRecommendCtx = new RelatedRecommendCtx();
        EnvCtx envCtx = new EnvCtx();
        relatedRecommendCtx.setEnvCtx(envCtx);
        LeCrossRecommendConfig config = new LeCrossRecommendConfig();
        config.setEnableDouHu(true);
        config.setCategoryIds(Collections.singletonList(1));
        doThrow(new RuntimeException()).when(douHuBiz).getAbExpResult(any(DealCtx.class), anyString());
        // act
        boolean result = leCrossRecommendHandler.getDouHuExp(relatedRecommendCtx, config);
        // assert
        assertFalse(result);
    }
}
