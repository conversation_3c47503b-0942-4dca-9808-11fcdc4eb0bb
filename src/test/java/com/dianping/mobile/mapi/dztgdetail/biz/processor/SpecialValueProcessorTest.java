package com.dianping.mobile.mapi.dztgdetail.biz.processor;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealActivityWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * @Author: zhangyuan103
 * @Date: 2025/7/14
 */
@RunWith(MockitoJUnitRunner.class)
public class SpecialValueProcessorTest {

    @InjectMocks
    private SpecialValueProcessor specialValueProcessor;

    @Mock
    private DealActivityWrapper dealActivityWrapper;

    @Test
    public void testIsEnableWithWeddingLeadsDeal() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);

        try (MockedStatic<DealUtils> mockedDealUtils = mockStatic(DealUtils.class)) {
            mockedDealUtils.when(() -> DealUtils.isWeddingLeadsDeal(ctx)).thenReturn(true);
            mockedDealUtils.when(() -> DealUtils.isLeadsDeal(ctx)).thenReturn(false);

            // act & assert
            assertTrue(specialValueProcessor.isEnable(ctx));
        }
    }

    @Test
    public void testIsEnableWithLeadsDeal() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);

        try (MockedStatic<DealUtils> mockedDealUtils = mockStatic(DealUtils.class)) {
            mockedDealUtils.when(() -> DealUtils.isWeddingLeadsDeal(ctx)).thenReturn(false);
            mockedDealUtils.when(() -> DealUtils.isLeadsDeal(ctx)).thenReturn(true);

            // act & assert
            assertTrue(specialValueProcessor.isEnable(ctx));
        }
    }

    @Test
    public void testIsEnableReturnsFalse() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);

        try (MockedStatic<DealUtils> mockedDealUtils = mockStatic(DealUtils.class)) {
            mockedDealUtils.when(() -> DealUtils.isWeddingLeadsDeal(ctx)).thenReturn(false);
            mockedDealUtils.when(() -> DealUtils.isLeadsDeal(ctx)).thenReturn(false);

            // act & assert
            assertFalse(specialValueProcessor.isEnable(ctx));
        }
    }

    @Test
    public void testPrepare() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        CompletableFuture<Object> expectedFuture = CompletableFuture.completedFuture(new Object());
        when(dealActivityWrapper.preQuerySpecialValueDeal(ctx)).thenReturn(expectedFuture);

        // act
        specialValueProcessor.prepare(ctx);

        // assert
        assertEquals(expectedFuture, ctx.getFutureCtx().getSpecialValueFuture());
        verify(dealActivityWrapper).preQuerySpecialValueDeal(ctx);
    }

    @Test
    public void testProcessNormal() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        CompletableFuture<Object> future = CompletableFuture.completedFuture(new Object());
        ctx.getFutureCtx().setSpecialValueFuture(future);
        when(dealActivityWrapper.hasSpecialValueDeal(future)).thenReturn(true);

        // act
        specialValueProcessor.process(ctx);

        // assert
        assertTrue(ctx.isHitSpecialValueDeal());
        verify(dealActivityWrapper).hasSpecialValueDeal(future);
    }

    @Test
    public void testProcessWithNullFutureCtx() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setFutureCtx(null);

        // act
        specialValueProcessor.process(ctx);

        // assert
        verify(dealActivityWrapper, never()).hasSpecialValueDeal(any());
    }

    @Test
    public void testProcessWithNullSpecialValueFuture() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.getFutureCtx().setSpecialValueFuture(null);

        // act
        specialValueProcessor.process(ctx);

        // assert
        verify(dealActivityWrapper, never()).hasSpecialValueDeal(any());
    }
}