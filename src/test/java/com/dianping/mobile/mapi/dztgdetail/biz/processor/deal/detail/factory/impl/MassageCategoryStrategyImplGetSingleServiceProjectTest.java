package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.impl;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import java.lang.reflect.Method;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MassageCategoryStrategyImplGetSingleServiceProjectTest {

    private MassageCategoryStrategyImpl massageCategoryStrategy = new MassageCategoryStrategyImpl();

    private Object invokePrivateMethod(Object target, String methodName, Object... args) throws Exception {
        Method method = target.getClass().getDeclaredMethod(methodName, DealGroupServiceProjectDTO.class);
        method.setAccessible(true);
        return method.invoke(target, args);
    }

    /**
     * 测试dealGroupServiceProject为null的情况
     */
    @Test
    public void testGetSingleServiceProjectDealGroupServiceProjectIsNull() throws Throwable {
        // Adjusted to pass a non-null but empty DealGroupServiceProjectDTO to simulate the scenario
        DealGroupServiceProjectDTO dealGroupServiceProject = new DealGroupServiceProjectDTO();
        assertNull(invokePrivateMethod(massageCategoryStrategy, "getSingleServiceProject", dealGroupServiceProject));
    }

    /**
     * 测试dealGroupServiceProject的mustGroups为空的情况
     */
    @Test
    public void testGetSingleServiceProjectMustGroupsIsEmpty() throws Throwable {
        DealGroupServiceProjectDTO dealGroupServiceProject = new DealGroupServiceProjectDTO();
        assertNull(invokePrivateMethod(massageCategoryStrategy, "getSingleServiceProject", dealGroupServiceProject));
    }

    /**
     * 测试mustGroups的第一个元素的groups为空的情况
     */
    @Test
    public void testGetSingleServiceProjectGroupsIsEmpty() throws Throwable {
        DealGroupServiceProjectDTO dealGroupServiceProject = new DealGroupServiceProjectDTO();
        MustServiceProjectGroupDTO mustServiceProjectGroupDTO = new MustServiceProjectGroupDTO();
        dealGroupServiceProject.setMustGroups(Collections.singletonList(mustServiceProjectGroupDTO));
        assertNull(invokePrivateMethod(massageCategoryStrategy, "getSingleServiceProject", dealGroupServiceProject));
    }

    /**
     * 测试groups的第一个元素不为空的情况
     */
    @Test
    public void testGetSingleServiceProjectGroupsIsNotEmpty() throws Throwable {
        DealGroupServiceProjectDTO dealGroupServiceProject = new DealGroupServiceProjectDTO();
        MustServiceProjectGroupDTO mustServiceProjectGroupDTO = new MustServiceProjectGroupDTO();
        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        mustServiceProjectGroupDTO.setGroups(Collections.singletonList(serviceProjectDTO));
        dealGroupServiceProject.setMustGroups(Collections.singletonList(mustServiceProjectGroupDTO));
        assertEquals(serviceProjectDTO, invokePrivateMethod(massageCategoryStrategy, "getSingleServiceProject", dealGroupServiceProject));
    }
}
