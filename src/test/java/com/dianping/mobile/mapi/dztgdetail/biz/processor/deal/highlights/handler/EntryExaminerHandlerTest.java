package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.model.ExaminerSuitableCrowd;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CommonAttrVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.highlights.DztgHighlightsModule;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class EntryExaminerHandlerTest {

    private EntryExaminerHandler entryExaminerHandler;

    @InjectMocks
    private EntryExaminerHandler handler;

    @Mock
    private DealCtx ctx;

    @Before
    public void setUp() {
        entryExaminerHandler = new EntryExaminerHandler();
    }

    private void invokePrivateMethod(String methodName, double turnaroundTime, List<CommonAttrVO> attrs) throws Exception {
        Method method = EntryExaminerHandler.class.getDeclaredMethod(methodName, double.class, List.class);
        method.setAccessible(true);
        method.invoke(entryExaminerHandler, turnaroundTime, attrs);
    }

    @Test
    public void testBuildDisplayTurnaroundTimeLessThanZero() throws Throwable {
        double turnaroundTime = -1;
        List<CommonAttrVO> attrs = new ArrayList<>();
        invokePrivateMethod("buildDisplayTurnaroundTime", turnaroundTime, attrs);
        assertTrue(attrs.isEmpty());
    }

    @Test
    public void testBuildDisplayTurnaroundTimeGreaterThanSeven() throws Throwable {
        double turnaroundTime = 8;
        List<CommonAttrVO> attrs = new ArrayList<>();
        invokePrivateMethod("buildDisplayTurnaroundTime", turnaroundTime, attrs);
        assertTrue(attrs.isEmpty());
    }

    @Test
    public void testBuildDisplayTurnaroundTimeSameAsSeven() throws Throwable {
        double turnaroundTime = 7;
        List<CommonAttrVO> attrs = new ArrayList<>();
        invokePrivateMethod("buildDisplayTurnaroundTime", turnaroundTime, attrs);
        assertEquals(1, attrs.size());
        assertEquals("一周出报告", attrs.get(0).getValue());
    }

    @Test
    public void testBuildDisplayTurnaroundTimeGreaterThanOne() throws Throwable {
        double turnaroundTime = 1.5;
        List<CommonAttrVO> attrs = new ArrayList<>();
        invokePrivateMethod("buildDisplayTurnaroundTime", turnaroundTime, attrs);
        assertEquals(1, attrs.size());
        assertEquals("次日出报告", attrs.get(0).getValue());
    }

    @Test
    public void testBuildDisplayTurnaroundTimeLessThanOne() throws Throwable {
        double turnaroundTime = 0.5;
        List<CommonAttrVO> attrs = new ArrayList<>();
        invokePrivateMethod("buildDisplayTurnaroundTime", turnaroundTime, attrs);
        assertEquals(1, attrs.size());
        assertEquals("当日出报告", attrs.get(0).getValue());
    }

    /**
     * Test happy path with complete valid data
     */
    @Test
    public void testExecute_WithValidData() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO timeAttr = new AttrDTO();
        timeAttr.setName("physical_examination_get_result_time");
        timeAttr.setValue(Collections.singletonList("结束后3个工作日"));
        attrs.add(timeAttr);
        dealGroupDTO.setAttrs(attrs);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        // act
        handler.execute(ctx);
        // assert
        verify(ctx).setHighlightsModule(any(DztgHighlightsModule.class));
    }

    /**
     * Test with empty attributes list
     */
    @Test
    public void testExecute_WithEmptyAttributes() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setAttrs(new ArrayList<>());
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        // act
        handler.execute(ctx);
        // assert
        verify(ctx).setHighlightsModule(any(DztgHighlightsModule.class));
    }

    /**
     * Test with various turnaround time formats
     */
    @Test
    public void testExecute_WithDifferentTurnaroundTimeFormats() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO timeAttr = new AttrDTO();
        timeAttr.setName("physical_examination_get_result_time");
        // Test days format
        timeAttr.setValue(Collections.singletonList("结束后5个工作日"));
        attrs.add(timeAttr);
        dealGroupDTO.setAttrs(attrs);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        // act
        handler.execute(ctx);
        // assert
        verify(ctx).setHighlightsModule(any(DztgHighlightsModule.class));
        // Test weeks format
        timeAttr.setValue(Collections.singletonList("结束后2周"));
        handler.execute(ctx);
        verify(ctx, times(2)).setHighlightsModule(any(DztgHighlightsModule.class));
        // Test months format
        timeAttr.setValue(Collections.singletonList("结束后1个月"));
        handler.execute(ctx);
        verify(ctx, times(3)).setHighlightsModule(any(DztgHighlightsModule.class));
    }

    /**
     * Test with complete suitable crowd information
     */
    @Test
    public void testExecute_WithCompleteSuitableCrowdInfo() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<AttrDTO> attrs = new ArrayList<>();
        // Add suitable crowd attributes
        AttrDTO crowdAttr = new AttrDTO();
        crowdAttr.setName("suitable_crowd");
        crowdAttr.setValue(Arrays.asList("老年人", "儿童"));
        attrs.add(crowdAttr);
        AttrDTO ageAttr = new AttrDTO();
        ageAttr.setName("px_suitable_age");
        ageAttr.setValue(Arrays.asList("20-30岁", "30-40岁"));
        attrs.add(ageAttr);
        AttrDTO genderAttr = new AttrDTO();
        genderAttr.setName("checkup_sex");
        genderAttr.setValue(Arrays.asList("男", "女"));
        attrs.add(genderAttr);
        AttrDTO serviceAttr = new AttrDTO();
        serviceAttr.setName("px_additional_service");
        serviceAttr.setValue(Arrays.asList("免费停车", "提供餐食"));
        attrs.add(serviceAttr);
        dealGroupDTO.setAttrs(attrs);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        // act
        handler.execute(ctx);
        // assert
        verify(ctx).setHighlightsModule(any(DztgHighlightsModule.class));
    }

    /**
     * Test with invalid turnaround time format
     */
    @Test
    public void testExecute_WithInvalidTurnaroundTimeFormat() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO timeAttr = new AttrDTO();
        timeAttr.setName("physical_examination_get_result_time");
        timeAttr.setValue(Collections.singletonList("invalid format"));
        attrs.add(timeAttr);
        dealGroupDTO.setAttrs(attrs);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        // act
        handler.execute(ctx);
        // assert
        verify(ctx).setHighlightsModule(any(DztgHighlightsModule.class));
    }
}
