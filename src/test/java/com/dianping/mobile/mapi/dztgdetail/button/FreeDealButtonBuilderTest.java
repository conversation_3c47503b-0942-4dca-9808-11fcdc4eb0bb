package com.dianping.mobile.mapi.dztgdetail.button;

import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.entity.FreeDealConfig;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class FreeDealButtonBuilderTest {

    @Test
    public void testDoBuild_WhenFreeDealConfigIsNative() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(1);
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx context = new DealCtx(envCtx);
        ButtonBuilderChain chain = mock(ButtonBuilderChain.class);
        FreeDealButtonBuilder builder = new FreeDealButtonBuilder();
        FreeDealConfig freeDealConfig = new FreeDealConfig();
        freeDealConfig.setMtAppSchema("imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=vg-mrn-reserve&mrn_component=customersubmit&mtShopId={shopId}&mtDealGroupId={dealId}");
        context.setFreeDealConfig(freeDealConfig);
        builder.doBuild(context, chain);
        Assert.assertNotNull(context.getBuyBar());
    }

    @Test
    @Ignore
    public void testDoBuild_WhenFreeDealConfigNotNative() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(6);
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_BAIDUMAP_MINIAPP);
        DealCtx context = new DealCtx(envCtx);
        ButtonBuilderChain chain = mock(ButtonBuilderChain.class);
        FreeDealButtonBuilder builder = new FreeDealButtonBuilder();
        FreeDealConfig freeDealConfig = new FreeDealConfig();
        context.setFreeDealConfig(freeDealConfig);
        builder.doBuild(context, chain);
        Assert.assertNotNull(context.getBuyBar());
    }
}
