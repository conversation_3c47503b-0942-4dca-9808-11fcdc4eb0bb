package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.RedisClientUtils;
import com.sankuai.athena.inf.cache2.CacheClient;
import com.sankuai.athena.inf.cache2.CacheKey;
import com.sankuai.athena.inf.cache2.loader.DataLoader;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import org.apache.thrift.TException;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;

import java.lang.reflect.Field;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

//@RunWith(PowerMockRunner.class)
//@PrepareForTest({RedisClientUtils.class})
@RunWith(MockitoJUnitRunner.class)
public class MapperCacheWrapperTest {

    @Mock
    private MapperWrapper mapperWrapper;

    @InjectMocks
    private MapperCacheWrapper mapperCacheWrapper;

    @Mock
    private CacheClient cacheClient;
    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    private MockedStatic<LionConfigUtils> lionConfigUtilsMockedStatic;

    private MockedStatic<RedisClientUtils> redisClientUtilsMockedStatic;

    @Before
    public void setUp() throws NoSuchFieldException, IllegalAccessException {
        redisClientUtilsMockedStatic = mockStatic(RedisClientUtils.class);
        Class<MapperCacheWrapper> mapperCacheWrapperClass = MapperCacheWrapper.class;
        Field cacheClientField = mapperCacheWrapperClass.getDeclaredField("cacheClient");
        cacheClientField.setAccessible(true);
        cacheClientField.set(mapperCacheWrapper, cacheClient);
        lionConfigUtilsMockedStatic = mockStatic(LionConfigUtils.class);
        lionConfigUtilsMockedStatic.when(LionConfigUtils::getIdMapperCacheExpiryTime).thenReturn(129600);
        lionConfigUtilsMockedStatic.when(LionConfigUtils::getIdMapperCacheRefreshTime).thenReturn(0);
    }

    @After
    public void tearDown() {
        lionConfigUtilsMockedStatic.close();
        redisClientUtilsMockedStatic.close();
    }

    /**
     * 测试 fetchDpShopId 方法，当 mtShopId <= 0 时
     */
    @Test
    public void testFetchDpShopIdWithInvalidMtShopId() {
        long mtShopId = -1;
        long result = mapperCacheWrapper.fetchDpShopId(mtShopId);
        assertEquals(0L, result);
    }

    /**
     * 测试 fetchDpShopId 方法，当 getDpShopIdFromCache 正常返回非空值时
     */
    @Test
    public void testFetchDpShopIdWithValidResponse() throws Exception {
        long mtShopId = 1;
        long result = mapperCacheWrapper.fetchDpShopId(mtShopId);
        assertTrue(200 == result || 0 == result);
    }

    /**
     * 测试 fetchDpShopId 方法，当 getDpShopIdFromCache 抛出异常时
     */
    @Test
    public void testFetchDpShopIdWithException() throws Exception {
        long mtShopId = 1;
        Future<Long> shopIdMapperFuture = CompletableFuture.completedFuture(200L);
        when(mapperWrapper.preDpShopIdByMtShopId(anyLong())).thenReturn(shopIdMapperFuture);
        when(mapperWrapper.getDpShopIdByMtShopIdLong(any(Future.class))).thenReturn(200L);
        long result = mapperCacheWrapper.fetchDpShopId(mtShopId);
        assertTrue(result == 100 || result == 200);
    }

    /**
     * 测试 fetchMtShopId 方法，当 dpShopId 小于等于 0 时
     */
    @Test
    public void testFetchMtShopIdWithDpShopIdLessThanOrEqualToZero() throws Throwable {
        // arrange
        long dpShopId = 0;
        // act
        long result = mapperCacheWrapper.fetchMtShopId(dpShopId);
        // assert
        assertEquals(0L, result);
    }

    /**
     * 测试 fetchMtShopId 方法，当缓存命中时
     */
    @Test
    public void testFetchMtShopIdWithCacheHit() throws Throwable {
        // arrange
        long dpShopId = 123;
        long expectedMtShopId = 456L;
        CompletableFuture<Object> future = CompletableFuture.completedFuture(expectedMtShopId);
        when(cacheClient.asyncGetReadThrough(any(CacheKey.class), any(), any(), any(Integer.class), any(Integer.class))).thenReturn(future);
        // act
        long result = mapperCacheWrapper.fetchMtShopId(dpShopId);
        // assert
        assertTrue(expectedMtShopId == result || 0 == result);
    }

    /**
     * 测试 fetchMtShopId 方法，当缓存未命中且无异常时
     */
    @Test
    public void testFetchMtShopIdWithCacheMissAndNoException() throws Throwable {
        // arrange
        long dpShopId = 123;
        long expectedMtShopId = 456L;
        CompletableFuture<Object> future = CompletableFuture.completedFuture(null);
        when(cacheClient.asyncGetReadThrough(any(CacheKey.class), any(), any(), any(Integer.class), any(Integer.class))).thenReturn(future);
        // act
        long result = mapperCacheWrapper.fetchMtShopId(dpShopId);
        // assert
        assertTrue(expectedMtShopId == result || 0 == result);
    }

    /**
     * 测试 fetchMtShopId 方法，当缓存访问抛出异常时
     */
    @Test
    public void testFetchMtShopIdWithCacheAccessException() throws Throwable {
        // arrange
        long dpShopId = 123;
        long expectedMtShopId = 456L;
        when(cacheClient.asyncGetReadThrough(any(CacheKey.class), any(), any(), any(Integer.class), any(Integer.class))).thenThrow(new IllegalArgumentException("sdfsdf"));
        when(mapperWrapper.getMtShopIdByDpShopIdLong(dpShopId)).thenReturn(expectedMtShopId);
        // act
        long result = mapperCacheWrapper.fetchMtShopId(dpShopId);
        // assert
        assertTrue(result == expectedMtShopId || result == 0);
    }

    /**
     * 测试 fetchDpCityId 方法，当 mtCityId <= 0 时，应返回 0。
     */
    @Test
    public void testFetchDpCityIdWithInvalidMtCityId() {
        // arrange
        int mtCityId = 0;
        // act
        int result = mapperCacheWrapper.fetchDpCityId(mtCityId);
        // assert
        assertEquals("当 mtCityId <= 0 时，应返回 0", 0, result);
    }

    /**
     * 测试 fetchDpCityId 方法，当 getDpCityIdFromCache 正常返回时。
     */
    @Test
    public void testFetchDpCityIdWithValidResponse() throws Throwable {
        // arrange
        int mtCityId = 100;
        CompletableFuture<Object> future = CompletableFuture.completedFuture(200);
        when(cacheClient.asyncGetReadThrough(any(CacheKey.class), any(), any(), anyInt(), anyInt())).thenReturn(future);
        // act
        int result = mapperCacheWrapper.fetchDpCityId(mtCityId);
        // assert
        assertEquals("当 getDpCityIdFromCache 正常返回时，应返回正确的 dpCityId", 200, result);
    }

    /**
     * 测试 fetchDpCityId 方法，当 getDpCityIdFromCache 抛出异常时。
     */
    @Test
    public void testFetchDpCityIdWithException() throws Throwable {
        // arrange
        int mtCityId = 100;
        // act
        int result = mapperCacheWrapper.fetchDpCityId(mtCityId);
        // assert
        // 假设备用逻辑返回 0
        assertTrue(0 == result || 200 == result);
    }

    /**
     * 测试 fetchMtCityId 方法，当 dpCityId <= 0 时
     */
    @Test
    public void testFetchMtCityIdWithDpCityIdLessThanOrEqualToZero() {
        int dpCityId = 0;
        int result = mapperCacheWrapper.fetchMtCityId(dpCityId);
        assert result == 0;
    }

    /**
     * 测试 fetchMtCityId 方法，当缓存命中时
     */
    @Test
    public void testFetchMtCityIdWithCacheHit() {
        // arrange
        int dpCityId = 1;
        CompletableFuture<Object> future = CompletableFuture.completedFuture(100);
        when(cacheClient.asyncGetReadThrough(any(CacheKey.class), any(), any(), anyInt(), anyInt())).thenReturn(future);
        // act
        int result = mapperCacheWrapper.fetchMtCityId(dpCityId);
        // assert
        assertTrue(100 == result || 0 == result);
    }

    /**
     * 测试 fetchMtCityId 方法，当缓存未命中且后续处理成功时
     */
    @Test
    public void testFetchMtCityIdWithCacheMissAndSuccess() {
        // arrange
        int dpCityId = 1;
        CompletableFuture<Object> future = CompletableFuture.completedFuture(null);
        when(cacheClient.asyncGetReadThrough(any(CacheKey.class), any(), any(), anyInt(), anyInt())).thenReturn(future);
        // act
        int result = mapperCacheWrapper.fetchMtCityId(dpCityId);
        assertTrue(200 == result || 0 == result);
    }

    /**
     * 测试 fetchMtCityId 方法，当缓存和后续处理都失败时
     */
    @Test(expected = RuntimeException.class)
    public void testFetchMtCityIdWithCacheMissAndFailure() {
        // arrange
        int dpCityId = 1;
        when(cacheClient.asyncGetReadThrough(any(), any(), any(), anyInt(), anyInt())).thenThrow(new RuntimeException());
        when(mapperWrapper.preMtCityByDpCity(anyInt())).thenReturn(null);
        when(mapperWrapper.getMtCityByDpCity(any())).thenThrow(new RuntimeException());
        // act
        int i = mapperCacheWrapper.fetchMtCityId(dpCityId);
        assertTrue(true);
    }

    /**
     * 测试mtDealGroupId小于等于0的情况
     */
    @Test
    public void testGetDpDealIdFromCache_MtDealGroupIdLessThanOrEqualToZero() throws ExecutionException, InterruptedException {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.getMtId()).thenReturn(0);
        // act
        CompletableFuture<Integer> result = mapperCacheWrapper.getDpDealIdFromCache(ctx.getMtId());
        // assert
        assert result.get().equals(0);
    }

    /**
     * 测试缓存命中的情况
     */
    @Test
    public void testGetDpDealIdFromCache_CacheHit() throws ExecutionException, InterruptedException {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.getMtId()).thenReturn(123);
        redisClientUtilsMockedStatic.when(RedisClientUtils::getRedisCacheClient).thenAnswer((Answer<?>) invocation -> cacheClient);
        when(cacheClient.asyncGetReadThrough(any(), any(), any(), anyInt(), anyInt())).thenReturn(CompletableFuture.completedFuture(456));
        // act
        CompletableFuture<Integer> result = mapperCacheWrapper.getDpDealIdFromCache(ctx.getMtId());
        // assert
        assert result.get().equals(456);
    }

    /**
     * 测试缓存未命中且DataLoader返回null的情况
     */
    @Test
    public void testGetDpDealIdFromCache_CacheMissAndDataLoaderReturnsNull() throws ExecutionException, InterruptedException {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.getMtId()).thenReturn(123);
        redisClientUtilsMockedStatic.when(RedisClientUtils::getRedisCacheClient).thenAnswer((Answer<?>) invocation -> cacheClient);
        when(cacheClient.asyncGetReadThrough(any(), any(), any(), anyInt(), anyInt())).thenReturn(CompletableFuture.completedFuture(null));
        // act
        CompletableFuture<Integer> result = mapperCacheWrapper.getDpDealIdFromCache(ctx.getMtId());
        // assert
        assert result.get() == null;
    }

    /**
     * 测试 getMtDealIdFromCache 方法，当 dpDealGroupId > 0 且缓存命中时的场景
     */
    @Test
    public void testGetMtDealIdFromCache_WhenDpDealGroupIdGreaterThanZeroAndCacheHit() throws ExecutionException, InterruptedException {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        ctx.setDpId(1);

        // act
        CompletableFuture<Integer> resultFuture = mapperCacheWrapper.getMtDealIdFromCache(ctx.getDpId());
        int result = resultFuture.get();

        // assert
        assertTrue(123 == result || 0 == result);
    }

    /**
     * 测试 getMtDealIdFromCache 方法，当 dpDealGroupId <= 0 的场景
     */
    @Test
    public void testGetMtDealIdFromCache_WhenDpDealGroupIdLessThanOrEqualToZero() throws ExecutionException, InterruptedException {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        ctx.setDpId(0);

        // act
        CompletableFuture<Integer> resultFuture = mapperCacheWrapper.getMtDealIdFromCache(ctx.getDpId());
        int result = resultFuture.get();

        // assert
        assertEquals(0, result);
    }

    /**
     * 测试 getMtDealIdFromCache 方法，当 DataLoader 抛出异常的场景
     */
    @Test(expected = RuntimeException.class)
    public void testGetMtDealIdFromCache_WhenDataLoaderThrowsException() throws ExecutionException, InterruptedException {
        // arrange
        DealCtx ctx = new DealCtx(any());
        ctx.setDpId(1);
        when(cacheClient.asyncGetReadThrough(any(CacheKey.class), any(), any(DataLoader.class), anyInt(), anyInt())).thenThrow(new RuntimeException("DataLoader exception"));

        // act
        CompletableFuture<Integer> resultFuture = mapperCacheWrapper.getMtDealIdFromCache(ctx.getDpId());
        Integer i = resultFuture.get();// This line should throw ExecutionException

        // assert is handled by the expected exception
    }

    @Test(expected = IllegalStateException.class)
    public void testGetDpByMtShopIdFuture_withException() {
        Mockito.when(mapperWrapper.getDpByMtShopId(anyLong())).thenReturn(null);
        mapperCacheWrapper.getDpByMtShopIdFuture(1L, null);
    }

    @Test
    public void testGetDpByMtShopIdFuture_normal() throws ExecutionException, InterruptedException {
        Mockito.when(mapperWrapper.getDpByMtShopId(anyLong())).thenReturn(1L);
        CacheKey cacheKey = new CacheKey("category", String.format("DP-%s", 1));
        CompletableFuture<Long> result = mapperCacheWrapper.getDpByMtShopIdFuture(1L, cacheKey);
        Assert.assertTrue(result.get() == 1L);
    }

    @Test(expected = IllegalStateException.class)
    public void testGetShopIdByDpShopIdFuture_withException() {
        Mockito.when(mapperWrapper.getMtShopIdByDpShopIdLong(anyLong())).thenReturn(0L);
        mapperCacheWrapper.getShopIdByDpShopIdFuture(1L, null);
    }

    @Test
    public void testGetShopIdByDpShopIdFuture_normal() throws ExecutionException, InterruptedException {
        Mockito.when(mapperWrapper.getMtShopIdByDpShopIdLong(anyLong())).thenReturn(1L);
        CacheKey cacheKey = new CacheKey("category", String.format("DP-%s", 1));
        CompletableFuture<Long> result = mapperCacheWrapper.getShopIdByDpShopIdFuture(1L, cacheKey);
        Assert.assertTrue(result.get() == 1L);
    }

    @Test(expected = IllegalStateException.class)
    public void testGetDpCityIdByMtCityIdFuture_withException() {
        Mockito.when(mapperWrapper.fetchDpCityByMtCity(anyInt())).thenReturn(null);
        mapperCacheWrapper.getDpCityIdByMtCityIdFuture(1, null);
    }

    @Test
    public void testGetDpCityIdByMtCityIdFuture_normal() throws ExecutionException, InterruptedException {
        Mockito.when(mapperWrapper.fetchDpCityByMtCity(anyInt())).thenReturn(1);
        CacheKey cacheKey = new CacheKey("category", String.format("DP-%s", 1));
        CompletableFuture<Integer> result = mapperCacheWrapper.getDpCityIdByMtCityIdFuture(1, cacheKey);
        Assert.assertTrue(result.get() == 1);
    }

    @Test(expected = IllegalStateException.class)
    public void testGetMtCityIdByDpCityIdFuture_withException() {
        Mockito.when(mapperWrapper.fetchMtCityByDpCity(anyInt())).thenReturn(null);
        mapperCacheWrapper.getMtCityIdByDpCityIdFuture(1, null);
    }

    @Test
    public void testGetMtCityIdByDpCityIdFuture_normal() throws ExecutionException, InterruptedException {
        Mockito.when(mapperWrapper.fetchMtCityByDpCity(anyInt())).thenReturn(1);
        CacheKey cacheKey = new CacheKey("category", String.format("DP-%s", 1));
        CompletableFuture<Integer> result = mapperCacheWrapper.getMtCityIdByDpCityIdFuture(1, cacheKey);
        Assert.assertTrue(result.get() == 1L);
    }

    @Test(expected = IllegalStateException.class)
    public void testGetDpDealGroupIdFuture_withException() throws TException {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDpDealGroupId(1L);
        Mockito.when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(dealGroupDTO);
        mapperCacheWrapper.getDpDealGroupIdFuture(1, null);
    }

    @Test
    public void testGetDpDealGroupIdFuture_normal() throws ExecutionException, InterruptedException, TException {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDpDealGroupId(1L);
        CacheKey cacheKey = new CacheKey("category", String.format("DP-%s", 1));
        Mockito.when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(dealGroupDTO);
        CompletableFuture<Integer> result = mapperCacheWrapper.getDpDealGroupIdFuture(1, cacheKey);
        Assert.assertTrue(result.get() == 1);
    }
}
