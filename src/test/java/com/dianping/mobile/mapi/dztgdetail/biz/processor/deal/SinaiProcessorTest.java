package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.geo.map.entity.GeoPoint;
import com.dianping.mobile.mapi.dztgdetail.biz.service.GeoBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperCacheWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiClientWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDisplayShopDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.NormPhone;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Collections;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.*;

/**
 * 测试SinaiProcessor的fulfillMtShopId方法
 */
@RunWith(MockitoJUnitRunner.class)
public class SinaiProcessorTest {

    @InjectMocks
    private SinaiProcessor sinaiProcessor;

    @Mock
    private MapperCacheWrapper mapperCacheWrapper;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private GeoBiz geoBiz;

    @Mock
    private DealCtx ctx;

    private SinaiProcessor processor = new SinaiProcessor();

    @Mock
    private PoiClientWrapper poiClientWrapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private String invokePrivateMethod(SinaiProcessor processor, String methodName, String addr, String crossRoad) throws Throwable {
        Method method = SinaiProcessor.class.getDeclaredMethod(methodName, String.class, String.class);
        method.setAccessible(true);
        return (String) method.invoke(processor, addr, crossRoad);
    }

    /**
     * 测试当ctx.getMtLongShopId() <= 0时，是否正确调用mapperCacheWrapper.fetchMtShopId并设置ctx
     */
    @Test
    public void testFulfillMtShopId_WhenMtLongShopIdIsLessThanOrEqualToZero() {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setMtLongShopId(0);
        ctx.setDpLongShopId(123L);
        when(mapperCacheWrapper.fetchMtShopId(123L)).thenReturn(456L);
        // act
        sinaiProcessor.fulfillMtShopId(ctx);
        // assert
        verify(mapperCacheWrapper, times(1)).fetchMtShopId(123L);
        assert (ctx.getMtLongShopId() == 456L);
    }

    /**
     * 测试当ctx.getMtLongShopId() > 0时，是否不调用mapperCacheWrapper.fetchMtShopId
     */
    @Test
    public void testFulfillMtShopId_WhenMtLongShopIdIsGreaterThanZero() {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setMtLongShopId(123L);
        // act
        sinaiProcessor.fulfillMtShopId(ctx);
        // assert
        verify(mapperCacheWrapper, never()).fetchMtShopId(anyLong());
        assert (ctx.getMtLongShopId() == 123L);
    }

    /**
     * 测试当ctx为null时，是否不抛出异常
     */
    @Test(expected = Exception.class)
    public void testFulfillMtShopId_WhenCtxIsNull() {
        // arrange
        DealCtx ctx = null;
        // act
        sinaiProcessor.fulfillMtShopId(ctx);
    }

    @Test
    public void testFillBestShopInfo() throws InvocationTargetException, IllegalAccessException {
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setShopName("shopName");
        dpPoiDTO.setShopType(1);
        dpPoiDTO.setBranchName("branchName");
        NormPhone normPhone = new NormPhone();
        normPhone.setAreaCode("111");
        normPhone.setBranch("3434");
        normPhone.setEntity("entity");
        dpPoiDTO.setNormPhones(Lists.newArrayList(normPhone));
        dpPoiDTO.setShopPower(1);
        dpPoiDTO.setAddress("address");
        dpPoiDTO.setCrossRoad("crossRoad");
        dpPoiDTO.setLat(0.13243);
        dpPoiDTO.setLng(0.1234);
        dpPoiDTO.setDefaultPic("defaultPic");
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setBestShopResp(new BestShopDTO());
        ctx.setUserlat(0);
        ctx.setUserlng(0);
        when(geoBiz.googleToGps(any(GeoPoint.class))).thenReturn(null);
        Method method = PowerMockito.method(SinaiProcessor.class, "fillBestShopInfo");
        method.invoke(sinaiProcessor, dpPoiDTO, ctx);
        assert ctx.getDealGroupDTO() == null;
    }

    /**
     * Test Case 1: mtLongShopId is greater than 0.
     */
    @Test
    public void testFulfillMtShopId_MtLongShopIdGreaterThanZero() throws Throwable {
        // arrange
        when(ctx.getMtLongShopId()).thenReturn(1L);
        // act
        sinaiProcessor.fulfillMtShopId(ctx);
        // assert
        verify(ctx, never()).setMtLongShopId(anyLong());
        verify(mapperCacheWrapper, never()).fetchMtShopId(anyLong());
    }

    /**
     * Test Case 2: mtLongShopId is less than or equal to 0 and mapperCacheWrapper.fetchMtShopId returns a valid mtLongShopId.
     */
    @Test
    public void testFulfillMtShopId_MtLongShopIdLessThanOrEqualToZero_ValidMtLongShopId() throws Throwable {
        // arrange
        when(ctx.getMtLongShopId()).thenReturn(0L);
        when(ctx.getDpLongShopId()).thenReturn(123L);
        when(mapperCacheWrapper.fetchMtShopId(123L)).thenReturn(456L);
        // act
        sinaiProcessor.fulfillMtShopId(ctx);
        // assert
        verify(ctx).setMtLongShopId(456L);
        verify(mapperCacheWrapper).fetchMtShopId(123L);
    }

    /**
     * Test Case 3: mtLongShopId is less than or equal to 0 and mapperCacheWrapper.fetchMtShopId throws an exception.
     */
    @Test(expected = RuntimeException.class)
    public void testFulfillMtShopId_MtLongShopIdLessThanOrEqualToZero_ExceptionThrown() throws Throwable {
        // arrange
        when(ctx.getMtLongShopId()).thenReturn(0L);
        when(ctx.getDpLongShopId()).thenReturn(123L);
        when(mapperCacheWrapper.fetchMtShopId(123L)).thenThrow(new RuntimeException("Error fetching mtShopId"));
        // act
        sinaiProcessor.fulfillMtShopId(ctx);
        // assert
        verify(ctx).setMtLongShopId(0L);
        verify(mapperCacheWrapper).fetchMtShopId(123L);
    }

    /**
     * Test case for normal scenario where crossRoad is not empty.
     */
    @Test
    public void testGenerateAddrNormalCase() throws Throwable {
        // arrange
        String addr = "123 Main St";
        String crossRoad = "5th Ave";
        // act
        String result = invokePrivateMethod(processor, "generateAddr", addr, crossRoad);
        // assert
        assertEquals("123 Main St(5th Ave)", result);
    }

    /**
     * Test case for boundary scenario where crossRoad is empty.
     */
    @Test
    public void testGenerateAddrCrossRoadEmpty() throws Throwable {
        // arrange
        String addr = "123 Main St";
        String crossRoad = "";
        // act
        String result = invokePrivateMethod(processor, "generateAddr", addr, crossRoad);
        // assert
        assertEquals("123 Main St", result);
    }

    /**
     * Test case for boundary scenario where crossRoad is null.
     */
    @Test
    public void testGenerateAddrCrossRoadNull() throws Throwable {
        // arrange
        String addr = "123 Main St";
        String crossRoad = null;
        // act
        String result = invokePrivateMethod(processor, "generateAddr", addr, crossRoad);
        // assert
        assertEquals("123 Main St", result);
    }

    /**
     * Test case for boundary scenario where addr is empty and crossRoad is not empty.
     */
    @Test
    public void testGenerateAddrAddrEmptyCrossRoadNotEmpty() throws Throwable {
        // arrange
        String addr = "";
        String crossRoad = "5th Ave";
        // act
        String result = invokePrivateMethod(processor, "generateAddr", addr, crossRoad);
        // assert
        assertEquals("(5th Ave)", result);
    }

    /**
     * Test case for boundary scenario where both addr and crossRoad are empty.
     */
    @Test
    public void testGenerateAddrBothEmpty() throws Throwable {
        // arrange
        String addr = "";
        String crossRoad = "";
        // act
        String result = invokePrivateMethod(processor, "generateAddr", addr, crossRoad);
        // assert
        assertEquals("", result);
    }

    /**
     * Test case for boundary scenario where both addr and crossRoad are null.
     */
    @Test
    public void testGenerateAddrBothNull() throws Throwable {
        // arrange
        String addr = null;
        String crossRoad = null;
        // act
        String result = invokePrivateMethod(processor, "generateAddr", addr, crossRoad);
        // assert
        assertNull(result);
    }

    /**
     * Test case when some fields in dpPoiDTO are null.
     */
    @Test
    public void testFillBestShopInfo_SomeFieldsNull() throws Throwable {
        // arrange
        DpPoiDTO dpPoiDTO = mock(DpPoiDTO.class);
        when(dpPoiDTO.getShopName()).thenReturn("Test Shop");
        when(dpPoiDTO.getShopType()).thenReturn(null);
        when(dpPoiDTO.getBranchName()).thenReturn(null);
        when(dpPoiDTO.getNormPhones()).thenReturn(null);
        when(dpPoiDTO.getShopPower()).thenReturn(null);
        when(dpPoiDTO.getAddress()).thenReturn("123 Test St");
        when(dpPoiDTO.getCrossRoad()).thenReturn(null);
        when(dpPoiDTO.getLat()).thenReturn(null);
        when(dpPoiDTO.getLng()).thenReturn(null);
        when(dpPoiDTO.getDefaultPic()).thenReturn(null);
        DealCtx ctx = mock(DealCtx.class);
        BestShopDTO bestShop = new BestShopDTO();
        when(ctx.getBestShopResp()).thenReturn(bestShop);
        when(ctx.getUserlat()).thenReturn(40.1);
        when(ctx.getUserlng()).thenReturn(-74.1);
        when(ctx.isMt()).thenReturn(false);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDisplayShopDTO displayShopInfo = mock(DealGroupDisplayShopDTO.class);
        when(displayShopInfo.getDpDisplayShopIds()).thenReturn(Collections.singletonList(1L));
        when(dealGroupDTO.getDisplayShopInfo()).thenReturn(displayShopInfo);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(geoBiz.googleToGps(any())).thenReturn(null);

        // Mock FutureCtx and its methods
        com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx futureCtx = mock(com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx.class);
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
        when(futureCtx.getDealShopQtyBySearchFuture()).thenReturn(null);

        // Mock dealGroupWrapper.getDealShopQtyBySearch
        when(dealGroupWrapper.getDealShopQtyBySearch(any(), anyLong())).thenReturn(0L);

        // Use reflection to invoke the private method
        Method method = SinaiProcessor.class.getDeclaredMethod("fillBestShopInfo", DpPoiDTO.class, DealCtx.class);
        method.setAccessible(true);
        method.invoke(sinaiProcessor, dpPoiDTO, ctx);
        // assert
        assertEquals("Test Shop", bestShop.getShopName());
        assertEquals(0, bestShop.getShopType());
        assertNull(bestShop.getBranchName());
        assertEquals(0, bestShop.getShopPower());
        assertEquals("123 Test St", bestShop.getAddress());
        assertEquals(0.0, bestShop.getGlat(), 0.001);
        assertEquals(0.0, bestShop.getGlng(), 0.001);
        assertEquals(1, bestShop.getTotalShopsNum());
        assertNull(bestShop.getShowType());
        // Expecting empty string instead of null
        assertEquals("", bestShop.getShopPic());
        assertEquals(0.0, bestShop.getLat(), 0.001);
        assertEquals(0.0, bestShop.getLng(), 0.001);
    }

    /**
     * Test case when user's latitude and longitude are invalid.
     */
    @Test
    public void testFillBestShopInfo_InvalidUserLocation() throws Throwable {
        // arrange
        DpPoiDTO dpPoiDTO = mock(DpPoiDTO.class);
        when(dpPoiDTO.getShopName()).thenReturn("Test Shop");
        when(dpPoiDTO.getLat()).thenReturn(40.0);
        when(dpPoiDTO.getLng()).thenReturn(-74.0);
        DealCtx ctx = mock(DealCtx.class);
        BestShopDTO bestShop = new BestShopDTO();
        when(ctx.getBestShopResp()).thenReturn(bestShop);
        when(ctx.getUserlat()).thenReturn(0.0);
        when(ctx.getUserlng()).thenReturn(0.0);

        // Mock FutureCtx and its methods
        com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx futureCtx = mock(com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx.class);
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
        when(futureCtx.getDealShopQtyBySearchFuture()).thenReturn(null);

        // Mock dealGroupWrapper.getDealShopQtyBySearch
        when(dealGroupWrapper.getDealShopQtyBySearch(any(), anyLong())).thenReturn(0L);
        // when(geoBiz.googleToGps(any())).thenReturn(null);

        // Use reflection to invoke the private method
        Method method = SinaiProcessor.class.getDeclaredMethod("fillBestShopInfo", DpPoiDTO.class, DealCtx.class);
        method.setAccessible(true);
        method.invoke(sinaiProcessor, dpPoiDTO, ctx);
        // assert
        assertNull(bestShop.getDistance());
    }

    /**
     * Test case when geoBiz.googleToGps returns null.
     */
    @Test
    public void testFillBestShopInfo_GeoBizReturnsNull() throws Throwable {
        // arrange
        DpPoiDTO dpPoiDTO = mock(DpPoiDTO.class);
        when(dpPoiDTO.getShopName()).thenReturn("Test Shop");
        when(dpPoiDTO.getLat()).thenReturn(40.0);
        when(dpPoiDTO.getLng()).thenReturn(-74.0);
        DealCtx ctx = mock(DealCtx.class);
        BestShopDTO bestShop = new BestShopDTO();
        when(ctx.getBestShopResp()).thenReturn(bestShop);
        when(ctx.getUserlat()).thenReturn(40.1);
        when(ctx.getUserlng()).thenReturn(-74.1);
        when(geoBiz.googleToGps(any())).thenReturn(null);

        // Mock FutureCtx and its methods
        com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx futureCtx = mock(com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx.class);
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
        when(futureCtx.getDealShopQtyBySearchFuture()).thenReturn(null);

        // Mock dealGroupWrapper.getDealShopQtyBySearch
        when(dealGroupWrapper.getDealShopQtyBySearch(any(), anyLong())).thenReturn(0L);

        // Use reflection to invoke the private method
        Method method = SinaiProcessor.class.getDeclaredMethod("fillBestShopInfo", DpPoiDTO.class, DealCtx.class);
        method.setAccessible(true);
        method.invoke(sinaiProcessor, dpPoiDTO, ctx);
        // assert
        assertEquals(0.0, bestShop.getLat(), 0.001);
        assertEquals(0.0, bestShop.getLng(), 0.001);
    }
}
