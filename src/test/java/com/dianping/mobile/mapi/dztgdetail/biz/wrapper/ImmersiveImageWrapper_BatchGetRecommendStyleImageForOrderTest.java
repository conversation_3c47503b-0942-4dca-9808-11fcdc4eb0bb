package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ImmersiveImageWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.nailstyle.NailStyleItemVO;
import com.sankuai.mpmctcontent.query.thrift.api.ContentFusion2CService;
import com.sankuai.mpmctcontent.query.thrift.dto.aggre.SearchFusionInfoReqDTO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Arrays;
import java.util.List;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.*;

public class ImmersiveImageWrapper_BatchGetRecommendStyleImageForOrderTest {

    @InjectMocks
    private ImmersiveImageWrapper immersiveImageWrapper;

    @Mock
    private ContentFusion2CService contentFusion2CService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testBatchGetRecommendStyleImageForOrderWithEmptyItemIds() throws Throwable {
        List<String> itemIds = Arrays.asList();
        String idType = "oldId";
        Integer categoryId = 1;
        Long dpDealGroupId = 1L;
        int display = 1;
        List<NailStyleItemVO> result = immersiveImageWrapper.batchGetRecommendStyleImageForOrder(itemIds, idType, categoryId, dpDealGroupId, display);
        assertNull(result);
    }

    @Test
    public void testBatchGetRecommendStyleImageForOrderWithNullFuture() throws Throwable {
        List<String> itemIds = Arrays.asList("item1", "item2");
        String idType = "oldId";
        Integer categoryId = 1;
        Long dpDealGroupId = 1L;
        int display = 1;
        List<NailStyleItemVO> result = immersiveImageWrapper.batchGetRecommendStyleImageForOrder(itemIds, idType, categoryId, dpDealGroupId, display);
        assertNull(result);
    }

    @Test
    public void testBatchGetRecommendStyleImageForOrderWithException() throws Throwable {
        List<String> itemIds = Arrays.asList("item1", "item2");
        String idType = "oldId";
        Integer categoryId = 1;
        Long dpDealGroupId = 1L;
        int display = 1;
        // Adjusted to mock the behavior of contentFusion2CService which is the actual dependency used in preBatchGetStyleImage
        doThrow(new RuntimeException()).when(contentFusion2CService).searchFusionInfo(any(SearchFusionInfoReqDTO.class));
        List<NailStyleItemVO> result = immersiveImageWrapper.batchGetRecommendStyleImageForOrder(itemIds, idType, categoryId, dpDealGroupId, display);
        assertNull(result);
    }
}
