package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import java.util.ArrayList;
import java.util.List;
import org.mockito.Mock;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ModuleConfigsEnum;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class ExtraStyleProcessor_BuildModuleConfigDo_1_Test {

    @InjectMocks
    private ExtraStyleProcessor extraStyleProcessor;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 buildModuleConfigDos 方法，当 tabStyle 为空字符串时，方法应正常返回，不进行任何操作
     */
    @Test
    public void testBuildModuleConfigDosWithEmptyTabStyle() throws Throwable {
        // arrange
        String tabStyle = "";
        ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
        // Ensure moduleConfigs is initialized
        moduleConfigsModule.setModuleConfigs(new ArrayList<>());
        // act
        extraStyleProcessor.buildModuleConfigDos(tabStyle, moduleConfigsModule);
        // assert
        assertTrue(moduleConfigsModule.getModuleConfigs().isEmpty());
    }

    /**
     * 测试 buildModuleConfigDos 方法，当 tabStyle 不为空时，方法应正常执行，向 moduleConfigsModule 对象中添加一个新的 ModuleConfigDo 对象
     */
    @Test
    public void testBuildModuleConfigDosWithNonEmptyTabStyle() throws Throwable {
        // arrange
        String tabStyle = "test";
        ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
        // Ensure moduleConfigs is initialized
        moduleConfigsModule.setModuleConfigs(new ArrayList<>());
        // act
        extraStyleProcessor.buildModuleConfigDos(tabStyle, moduleConfigsModule);
        // assert
        List<ModuleConfigDo> moduleConfigDos = moduleConfigsModule.getModuleConfigs();
        assertEquals(1, moduleConfigDos.size());
        assertEquals(ModuleConfigsEnum.TAB_STYLE.getCode(), moduleConfigDos.get(0).getKey());
        assertEquals(tabStyle, moduleConfigDos.get(0).getValue());
    }
}
