package com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.extend.impl;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.PromoDetailEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBestPromoDetail;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ServiceGuarantee.DealBestPromoDetailDTO;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dealuser.price.display.api.model.PromoIdentity;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class MerchantCouponsPromoDetailHandlerTest {

    private MerchantCouponsPromoDetailHandler handler = new MerchantCouponsPromoDetailHandler();

    private boolean invokeIsReductionPromo(int promoType) throws Exception {
        Method method = MerchantCouponsPromoDetailHandler.class.getDeclaredMethod("isReductionPromo", int.class);
        method.setAccessible(true);
        return (boolean) method.invoke(handler, promoType);
    }

    @Test
    public void testGetDealBestPromoDetailEmptyUsedPromos() throws Throwable {
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        priceDisplayDTO.setUsedPromos(Collections.emptyList());
        DealBestPromoDetailDTO result = handler.getDealBestPromoDetail(priceDisplayDTO);
        assertNull(result);
    }

    @Test
    public void testGetDealBestPromoDetailNullIdentity() throws Throwable {
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(null);
        priceDisplayDTO.setUsedPromos(Arrays.asList(promoDTO));
        DealBestPromoDetailDTO result = handler.getDealBestPromoDetail(priceDisplayDTO);
        assertNull(result);
    }

    @Test
    public void testGetDealBestPromoDetailNullAmount() throws Throwable {
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(new PromoIdentity(1));
        promoDTO.setAmount(null);
        priceDisplayDTO.setUsedPromos(Arrays.asList(promoDTO));
        DealBestPromoDetailDTO result = handler.getDealBestPromoDetail(priceDisplayDTO);
        assertNull(result);
    }

    @Test
    public void testGetDealBestPromoDetailSourceTypeNotEqualTwo() throws Throwable {
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(new PromoIdentity(1));
        promoDTO.setAmount(new BigDecimal(100));
        priceDisplayDTO.setUsedPromos(Arrays.asList(promoDTO));
        DealBestPromoDetailDTO result = handler.getDealBestPromoDetail(priceDisplayDTO);
        assertNull(result);
    }

    @Test
    public void testGetDealBestPromoDetailIsReductionPromo() throws Throwable {
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(new PromoIdentity(1));
        promoDTO.setAmount(new BigDecimal(100));
        priceDisplayDTO.setUsedPromos(Arrays.asList(promoDTO));
        DealBestPromoDetailDTO result = handler.getDealBestPromoDetail(priceDisplayDTO);
        assertNull(result);
    }

    @Test
    public void testGetDealBestPromoDetailAmountEqualZero() throws Throwable {
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(new PromoIdentity(1));
        promoDTO.setAmount(new BigDecimal(0));
        priceDisplayDTO.setUsedPromos(Arrays.asList(promoDTO));
        DealBestPromoDetailDTO result = handler.getDealBestPromoDetail(priceDisplayDTO);
        assertNull(result);
    }

    /**
     * 测试 isReductionPromo 方法，当 promoType 等于 NORMAL_PROMO 的 type 时，应返回 true
     */
    @Test
    public void testIsReductionPromoNormal() throws Throwable {
        // arrange
        int promoType = PromoTypeEnum.NORMAL_PROMO.getType();
        // act
        boolean result = invokeIsReductionPromo(promoType);
        // assert
        Assert.assertTrue(result);
    }

    /**
     * 测试 isReductionPromo 方法，当 promoType 等于 IDLE_PROMO 的 type 时，应返回 true
     */
    @Test
    public void testIsReductionPromoIdle() throws Throwable {
        // arrange
        int promoType = PromoTypeEnum.IDLE_PROMO.getType();
        // act
        boolean result = invokeIsReductionPromo(promoType);
        // assert
        Assert.assertTrue(result);
    }

    /**
     * 测试 isReductionPromo 方法，当 promoType 等于 THRESHOLD 的 type 时，应返回 true
     */
    @Test
    public void testIsReductionPromoThreshold() throws Throwable {
        // arrange
        int promoType = PromoTypeEnum.THRESHOLD.getType();
        // act
        boolean result = invokeIsReductionPromo(promoType);
        // assert
        Assert.assertTrue(result);
    }

    /**
     * 测试 isReductionPromo 方法，当 promoType 不等于上述任何一个值时，应返回 false
     */
    @Test
    public void testIsReductionPromoOther() throws Throwable {
        // arrange
        int promoType = PromoTypeEnum.DISCOUNT_CARD.getType();
        // act
        boolean result = invokeIsReductionPromo(promoType);
        // assert
        Assert.assertFalse(result);
    }
}
