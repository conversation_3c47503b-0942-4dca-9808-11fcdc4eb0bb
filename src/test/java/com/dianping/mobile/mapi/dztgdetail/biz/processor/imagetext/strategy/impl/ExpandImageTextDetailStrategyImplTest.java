package com.dianping.mobile.mapi.dztgdetail.biz.processor.imagetext.strategy.impl;

import com.dianping.mobile.mapi.dztgdetail.common.enums.ImageTextStrategyEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ContentDetailPBO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class ExpandImageTextDetailStrategyImplTest {

    @InjectMocks
    private ExpandImageTextDetailStrategyImpl strategy;

    @Mock
    private DealGroupDTO mockDealGroupDTO;
    /**
     * 测试getStrategyName方法，期望返回EXPAND策略
     */
    @Test
    public void testGetStrategyNameExpectExpand() throws Throwable {
        // arrange
        ExpandImageTextDetailStrategyImpl strategy = new ExpandImageTextDetailStrategyImpl();

        // act
        ImageTextStrategyEnum result = strategy.getStrategyName();

        // assert
        assertEquals("期望返回EXPAND策略", ImageTextStrategyEnum.EXPAND, result);
    }

    /**
     * 测试 getContentDetail 方法，当 contents 为空时的场景
     */
    @Test
    public void testGetContentDetailWithEmptyContents() {
        // arrange
        List<ContentPBO> contents = new ArrayList<>();
        int threshold = 2;

        // act
        ContentDetailPBO result = strategy.getContentDetail(mockDealGroupDTO, contents, threshold);

        // assert
        assertNotNull(result);
        assertTrue(result.getContents().isEmpty());
    }

    /**
     * 测试 getContentDetail 方法，当 contents 不为空，但小于阈值时的场景
     */
    @Test
    public void testGetContentDetailWithContentsLessThanThreshold() {
        // arrange
        List<ContentPBO> contents = new ArrayList<>();
        contents.add(new ContentPBO(0, "Test Content"));
        int threshold = 2;

        // act
        ContentDetailPBO result = strategy.getContentDetail(mockDealGroupDTO, contents, threshold);

        // assert
        assertNotNull(result);
        assertFalse(result.isFold());
        assertEquals(1, result.getContents().size());
    }

    /**
     * 测试 getContentDetail 方法，当 contents 不为空，且等于阈值时的场景
     */
    @Test
    public void testGetContentDetailWithContentsEqualToThreshold() {
        // arrange
        List<ContentPBO> contents = new ArrayList<>();
        contents.add(new ContentPBO(0, "Test Content 1"));
        contents.add(new ContentPBO(1, "Test Content 2"));
        int threshold = 2;

        // act
        ContentDetailPBO result = strategy.getContentDetail(mockDealGroupDTO, contents, threshold);

        // assert
        assertNotNull(result);
        assertFalse(result.isFold());
        assertEquals(2, result.getContents().size());
    }

    /**
     * 测试 getContentDetail 方法，当 contents 不为空，且大于阈值时的场景
     */
    @Test
    public void testGetContentDetailWithContentsGreaterThanThreshold() {
        // arrange
        List<ContentPBO> contents = new ArrayList<>();
        contents.add(new ContentPBO(0, "Test Content 1"));
        contents.add(new ContentPBO(1, "Test Content 2"));
        contents.add(new ContentPBO(2, "Test Content 3"));
        int threshold = 2;

        // act
        ContentDetailPBO result = strategy.getContentDetail(mockDealGroupDTO, contents, threshold);

        // assert
        assertNotNull(result);
        assertFalse(result.isFold());
        assertEquals(3, result.getContents().size());
    }

}
