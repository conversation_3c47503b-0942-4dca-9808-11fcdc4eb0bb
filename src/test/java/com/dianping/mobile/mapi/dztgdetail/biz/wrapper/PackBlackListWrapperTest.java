package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.mobile.mapi.dztgdetail.entity.CombinationDealInfo;
import com.sankuai.mppack.product.client.query.request.TyingBlackListQueryRequest;
import com.sankuai.mppack.product.client.query.response.TyingBlackListResponse;
import com.sankuai.mppack.product.client.supply.service.PackBlackListService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PackBlackListWrapperTest {

    @Mock
    private PackBlackListService packBlackListServiceFuture;
    @InjectMocks
    private PackBlackListWrapper packBlackListWrapper;
    @Mock
    private Future<TyingBlackListResponse> futureMock;
    @Mock
    private TyingBlackListResponse responseMock;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试空的组合交易信息列表
     */
    @Test
    public void testPrePackBlackLisWithEmptyList() throws Throwable {
        // arrange
        List<CombinationDealInfo> combinationDealInfos = new ArrayList<>();
        Long mtShopId = 1L;
        // act
        List<Future> result = packBlackListWrapper.prePackBlackLis(combinationDealInfos, mtShopId);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试组合交易信息列表不为空，且能正常处理
     */
    @Test
    public void testPrePackBlackLisWithValidList() throws Throwable {
        // arrange
        List<CombinationDealInfo> combinationDealInfos = new ArrayList<>();
        CombinationDealInfo info = new CombinationDealInfo();
        info.setMainDealId(1);
        info.setBindingDealId(1);
        combinationDealInfos.add(info);
        Long mtShopId = 1L;
        when(packBlackListServiceFuture.batchQueryTyingBlackList(any(TyingBlackListQueryRequest.class))).thenReturn(null);

        // act
        List<Future> result = packBlackListWrapper.prePackBlackLis(combinationDealInfos, mtShopId);

        // assert
        assertTrue(result.size() == 1);
        verify(packBlackListServiceFuture, times(1)).batchQueryTyingBlackList(any(TyingBlackListQueryRequest.class));
    }

    /**
     * 测试组合交易信息列表不为空，但分区后的每个列表处理时抛出异常
     */
    @Test
    public void testPrePackBlackLisWithException() throws Throwable {
        // arrange
        List<CombinationDealInfo> combinationDealInfos = new ArrayList<>();
        for (int i = 0; i < 25; i++) {
            CombinationDealInfo info = new CombinationDealInfo();
            combinationDealInfos.add(info);
        }
        Long mtShopId = 1L;
        when(packBlackListServiceFuture.batchQueryTyingBlackList(any(TyingBlackListQueryRequest.class))).thenThrow(new RuntimeException("Test Exception"));

        // act
        List<Future> result = packBlackListWrapper.prePackBlackLis(combinationDealInfos, mtShopId);

        // assert
        assertTrue(result.isEmpty());
        verify(packBlackListServiceFuture, times(2)).batchQueryTyingBlackList(any(TyingBlackListQueryRequest.class));
    }


    /**
     * 测试getPackBlackMap方法，当Future返回的response成功且包含数据时
     */
    @Test
    public void testGetPackBlackMapWithSuccessfulResponse() throws Throwable {
        // arrange
        List<Future> futures = new ArrayList<>();
        futures.add(futureMock);
        when(futureMock.get()).thenReturn(responseMock);
        when(responseMock.isSuccess()).thenReturn(true);
        Map<String, Boolean> tagMap = new HashMap<>();
        tagMap.put("key", true);
        when(responseMock.getData()).thenReturn(tagMap);

        // act
        Map<String, Boolean> result = packBlackListWrapper.getPackBlackMap(futures);

        // assert
        assertFalse("结果不应为空", result.isEmpty());
        assertTrue("结果应包含key为true的值", result.get("key"));
    }


}
