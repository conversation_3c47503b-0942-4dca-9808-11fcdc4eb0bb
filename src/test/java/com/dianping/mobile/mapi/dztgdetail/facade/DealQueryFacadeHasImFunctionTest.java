package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.publishcategory.dto.ChannelDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.helper.SwitchHelper;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ SwitchHelper.class, Lion.class })
public class DealQueryFacadeHasImFunctionTest {

    private DealQueryFacade dealQueryFacade;

    @Mock
    private Lion lion;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        dealQueryFacade = new DealQueryFacade();
        PowerMockito.mockStatic(SwitchHelper.class);
        PowerMockito.mockStatic(Lion.class);
    }

    private boolean invokeHasImFunction(DealGroupChannelDTO channel) throws Exception {
        Method method = DealQueryFacade.class.getDeclaredMethod("hasImFunction", DealGroupChannelDTO.class);
        method.setAccessible(true);
        return (boolean) method.invoke(dealQueryFacade, channel);
    }

    /**
     * Helper method to invoke private methods using reflection.
     */
    private String invokePrivateMethod(Object object, String methodName, long customerId) throws Throwable {
        Method method = DealQueryFacade.class.getDeclaredMethod(methodName, long.class);
        method.setAccessible(true);
        return (String) method.invoke(object, customerId);
    }

    /**
     * 测试当 channel 为 null 时，hasImFunction 返回 false
     */
    @Test
    public void testHasImFunctionWhenChannelIsNull() throws Throwable {
        // arrange
        DealGroupChannelDTO channel = null;
        // act
        boolean result = invokeHasImFunction(channel);
        // assert
        assertFalse(result);
    }

    /**
     * 测试当 channel 不为 null 且 SwitchHelper.isIm 返回 true 时，hasImFunction 返回 true
     */
//    @Test
//    public void testHasImFunctionWhenChannelIsNotNullAndIsImReturnsTrue() throws Throwable {
//        // arrange
//        DealGroupChannelDTO channel = new DealGroupChannelDTO();
//        ChannelDTO channelDTO = new ChannelDTO();
//        channelDTO.setChannelId(123);
//        channel.setChannelDTO(channelDTO);
//        // Mock SwitchHelper.isIm to return true
//        when(SwitchHelper.isIm(123)).thenReturn(true);
//        // act
//        boolean result = invokeHasImFunction(channel);
//        // assert
//        assertTrue(result);
//    }

    /**
     * 测试当 channel 不为 null 且 SwitchHelper.isIm 返回 false，但 SwitchHelper.isCategoryIm 返回 true 时，hasImFunction 返回 true
     */
//    @Test
//    public void testHasImFunctionWhenChannelIsNotNullAndIsImReturnsFalseButIsCategoryImReturnsTrue() throws Throwable {
//        // arrange
//        DealGroupChannelDTO channel = new DealGroupChannelDTO();
//        ChannelDTO channelDTO = new ChannelDTO();
//        channelDTO.setChannelId(123);
//        channel.setChannelDTO(channelDTO);
//        channel.setCategoryId(456);
//        // Mock SwitchHelper.isIm to return false and SwitchHelper.isCategoryIm to return true
//        when(Mockito.mock(Boolean.class)).thenReturn(false);
//        when(Mockito.mock(Boolean.class)).thenReturn(true);
//        // act
//        boolean result = invokeHasImFunction(channel);
//        // assert
//        assertTrue(result);
//    }

    /**
     * 测试当 channel 不为 null 且 SwitchHelper.isIm 和 SwitchHelper.isCategoryIm 都返回 false 时，hasImFunction 返回 false
     */
//    @Test
//    public void testHasImFunctionWhenChannelIsNotNullAndIsImAndIsCategoryImReturnFalse() throws Throwable {
//        // arrange
//        DealGroupChannelDTO channel = new DealGroupChannelDTO();
//        ChannelDTO channelDTO = new ChannelDTO();
//        channelDTO.setChannelId(123);
//        channel.setChannelDTO(channelDTO);
//        channel.setCategoryId(456);
//        // Mock SwitchHelper.isIm and SwitchHelper.isCategoryIm to return false
//        when(SwitchHelper.isIm(123)).thenReturn(false);
//        when(SwitchHelper.isCategoryIm(456)).thenReturn(false);
//        // act
//        boolean result = invokeHasImFunction(channel);
//        // assert
//        assertFalse(result);
//    }

    /**
     * Test case for when the Lion.getMap method throws an exception.
     */
    @Test(expected = RuntimeException.class)
    public void testGetCustomerNameLionGetMapThrowsException() throws Throwable {
        // arrange
        long customerId = 12345L;
        when(lion.getMap(LionConstants.HEGUI_NOTICE_CUSTOMER_NAME_MAP, String.class, new HashMap<>())).thenThrow(new RuntimeException("Lion.getMap failed"));
        // act
        invokePrivateMethod(dealQueryFacade, "getCustomerName", customerId);
        // assert
        // Expecting RuntimeException to be thrown
    }
}
