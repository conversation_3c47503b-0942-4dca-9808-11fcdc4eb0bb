package com.dianping.mobile.mapi.dztgdetail.biz.dealnotice;

import com.alibaba.fastjson.JSON;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiClientWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealNoticeLayerCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealNoticeLayerReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.Guarantee;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealnotice.DealNoticeLayerPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealnotice.GeneralLayerConfig;
import com.dianping.mobile.mapi.dztgdetail.facade.DzDealNoticeLayerFacade;
import com.dianping.mobile.mapi.dztgdetail.facade.MapWrapper;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;

@RunWith(MockitoJUnitRunner.class)
public class CommunityHealthToHomeNoticeProcessorTest {
    private CommunityHealthToHomeNoticeProcessor processor = new CommunityHealthToHomeNoticeProcessor();
    @InjectMocks
    private DzDealNoticeLayerFacade dzDealNoticeLayerFacade;

    QueryCenterWrapper queryCenterWrapper = new QueryCenterWrapper();
    DealNoticeLayerReq request = new DealNoticeLayerReq();
    EnvCtx envCtx = new EnvCtx();
    DealNoticeLayerCtx ctx ;


    @Resource
    private PoiClientWrapper poiClientWrapper;
    @Resource
    private MapperWrapper mapperWrapper;
    @Resource
    private MapWrapper mapFacade = new MapWrapper();


    String poiDtoStr = "{\"appSides\":\"\",\"avgPrice\":0,\"backMainCategoryPath\":[{\"categoryId\":450,\"categoryLevel\":1,\"categoryName\":\"医疗健康\",\"hot\":0,\"leaf\":false,\"parentId\":0},{\"categoryId\":2468,\"categoryLevel\":2,\"categoryName\":\"社区卫生服务中心\",\"hot\":1,\"leaf\":true,\"main\":true,\"parentId\":450}],\"businessHours\":\"周一至周日 10:00-21:00\",\"cityId\":1,\"defaultPic\":\"http://p0.meituan.net/searchscenerec/d2f43577e06c094f2c4bc5f89bcc063a238883.png%40300w_225h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\",\"fiveScore\":0.0,\"fiveSub1\":0.0,\"fiveSub2\":0.0,\"fiveSub3\":0.0,\"fiveSub4\":0.0,\"fiveSub5\":0.0,\"fiveSub6\":0.0,\"lat\":31.218157,\"lng\":121.375264,\"mainRegionName\":\"北新泾/淞虹路\",\"power\":5,\"refinedScore1\":\"\",\"refinedScore2\":\"\",\"refinedScore3\":\"\",\"shopId\":604582270,\"shopName\":\"基层医疗卫生机构-杨浦\",\"shopPower\":0,\"sub5\":0,\"sub6\":0,\"useType\":1}";
    DpPoiDTO poiDTO = JSON.parseObject(poiDtoStr, DpPoiDTO.class);
    String dealGroupDtoStr = "{\"attrs\":[{\"name\":\"product_finish_date\",\"source\":0,\"value\":[\"1970-01-01 08:00:00\"]},{\"name\":\"for_the_crowd\",\"source\":0,\"value\":[\"具备医院医生开具的处方、执行单，带药回家，不方便去医院的患者\"]},{\"name\":\"calc_holiday_available\",\"source\":0,\"value\":[\"1\"]},{\"name\":\"product_business_type\",\"source\":0,\"value\":[\"1\"]},{\"name\":\"service_type_leaf_id\",\"source\":0,\"value\":[\"143025\"]},{\"name\":\"product_channel_id_allowed\",\"source\":0,\"value\":[\"0\"]},{\"name\":\"Farthest_service_range\",\"source\":0,\"value\":[\"8\"]},{\"name\":\"product_can_use_coupon\",\"source\":0,\"value\":[\"true\"]},{\"name\":\"product_name\",\"source\":0,\"value\":[\"上门打针｜护士上门｜8km可上门\"]},{\"name\":\"preSaleTag\",\"source\":0,\"value\":[\"false\"]},{\"name\":\"product_third_party_verify\",\"source\":0,\"value\":[\"false\"]},{\"name\":\"product_block_stock\",\"source\":0,\"value\":[\"false\"]},{\"name\":\"product_discount_rule_id\",\"source\":0,\"value\":[\"0\"]},{\"name\":\"service_type\",\"source\":0,\"value\":[\"上门注射\"]},{\"name\":\"tag_unifyProduct\",\"source\":0,\"value\":[\"0\"]},{\"name\":\"reservation_is_needed_or_not\",\"source\":0,\"value\":[\"是\"]},{\"name\":\"sys_deal_universal_type\",\"source\":0,\"value\":[\"1\"]}],\"basic\":{\"categoryId\":1631,\"status\":1},\"category\":{\"categoryId\":1631,\"serviceType\":\"上门注射\",\"serviceTypeId\":143025},\"deals\":[{\"basic\":{\"originTitle\":\"上门打针｜护士上门｜8km可上门\",\"status\":1,\"title\":\"上门打针｜护士上门｜8km可上门\"},\"dealId\":460023069,\"dealIdInt\":460023069}],\"dpDealGroupId\":1031699348,\"dpDealGroupIdInt\":1031699348,\"mtDealGroupId\":1031699348,\"mtDealGroupIdInt\":1031699348,\"price\":{\"marketPrice\":\"999.00\",\"salePrice\":\"88.00\",\"version\":5171491597}}";
    DealGroupDTO dealGroupDTO = JSON.parseObject(dealGroupDtoStr, DealGroupDTO.class);

    @Before
    public void setUp() throws NoSuchFieldException, IllegalAccessException {
        request.setDealgroupid(1031699348);
        request.setPoiid(604582270L);
        request.setUserlat(31.27130763927375);
        request.setUserlng(121.5289981723268);
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);

        Class clazz = processor.getClass();
        Field field = clazz.getDeclaredField("mapWrapper");
        field.setAccessible(true);
        field.set(processor, mapFacade);
        Class mapFacadeClass = mapFacade.getClass();
        Field mapFacadeField = mapFacadeClass.getDeclaredField("mapOpenApiService");
        mapFacadeField.setAccessible(true);

        Field queryCenterWrapperField = clazz.getField("queryCenterWrapper");
        queryCenterWrapperField.setAccessible(true);
        queryCenterWrapperField.set(processor, queryCenterWrapper);

        ctx = new DealNoticeLayerCtx(envCtx);
        ctx.setRequest(request);
    }

    @Test
    public void testValid() {
        processor.valid(ctx);
        Assert.assertNotNull(dealGroupDTO);
    }
    @Test(expected = NullPointerException.class)
    public void testGetDealNoticeLayerPBO() {
        processor.getDealNoticeLayerPBO(ctx);
        Assert.assertNotNull(dealGroupDTO);
    }

    @Test
    public void testBuildDisplayTagsO() {
        processor.buildDisplayTags(request,dealGroupDTO , poiDTO);
        Assert.assertNotNull(dealGroupDTO);
    }

    @Test
    public void testBuildAddressSelectLink() {
        processor.buildAddressSelectLink(request, envCtx, "互联宝地");
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        processor.buildAddressSelectLink(request, envCtx, "互联宝地");

        Assert.assertNotNull(dealGroupDTO);
    }
    @Test
    public void testBuildLayer() {
        processor.buildLayer(request, envCtx, "互联宝地");
        Assert.assertNotNull(dealGroupDTO);
    }
    @Test
    public void testDzDealNoticeLayerFacade() throws NoSuchFieldException, IllegalAccessException {
        Class clazz = DzDealNoticeLayerFacade.class;
        Field field = clazz.getDeclaredField("dealNoticeProcessor");
        List<DealNoticeProcessor> dealNoticeProcessor = new ArrayList<>();
        dealNoticeProcessor.add(processor);
        field.setAccessible(true);
        field.set(dzDealNoticeLayerFacade, dealNoticeProcessor);
        DealNoticeLayerPBO result = dzDealNoticeLayerFacade.getDealGroupNoticeLayer(request,envCtx);
        dzDealNoticeLayerFacade.initCtx(request, envCtx);
        Assert.assertNotNull(result);
    }

    @Test
    public void testMapFacade() throws NoSuchFieldException, IllegalAccessException {
        String result = mapFacade.getUserAddress(false, request.getUserlng(), request.getUserlat());
        result = mapFacade.getUserAddress(true, request.getUserlng(), request.getUserlat());
        Assert.assertNotNull(result);
    }

    @Test(expected = NullPointerException.class)
    public void testGetShopInfo(){
        DpPoiDTO result = processor.getShopInfo(ctx);
        Assert.assertNotNull(result);
    }
    @Test(expected = NullPointerException.class)
    public void testGetShopInfoDP(){
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        DpPoiDTO result = processor.getShopInfo(ctx);
        Assert.assertNotNull(result);
    }
    
    @Test
    public void testBuildDealNoticeLayerPBO(){
        String moduleName = "上门";
        List<String> displayValues = Lists.newArrayList();
        displayValues.add("互联宝地");
        List< Guarantee > tags = Lists.newArrayList();
        Guarantee tag = Guarantee.builder().text("超出范围").build();
        tags.add(tag);

        GeneralLayerConfig generalLayerConfig = new GeneralLayerConfig();
        generalLayerConfig.setJumpUrl("imeituan://www.meituan.com/web?url=http%3A%2F%2Fg.meituan.com%2Fcsr%2Flife-service-client-pages%2Faddress%2Findex.html%3Ffrom%3Deasy_life_v2%26currentAddressName%3D%25E4%25BA%2592%25E8%2581%2594%25E5%25AE%259D%25E5%259C%25B0%26source%3Dundefined%26version_name%3Dundefined%26notitlebar%3D1%26%26lat%3D31.27132451807735%26lng%3D121.************%26ci%3D10%26uuid%3D0000000000000095A69EF747D46419703923FC0460FDBA168778217023316139");
        DealNoticeLayerPBO result = processor.buildDealNoticeLayerPBO("上门", displayValues, tags, generalLayerConfig);
        Assert.assertNotNull(result);
    }
}
