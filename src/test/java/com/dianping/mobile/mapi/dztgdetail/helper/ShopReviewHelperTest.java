package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.ReviewDetailDO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.ReviewPicDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.ugc.ReviewUserModel;
import com.dianping.review.professional.ReviewDataV2;
import com.dianping.review.professional.Star;
import com.dianping.ugc.pic.remote.dto.MtReviewPicInfo;
import com.dianping.ugc.pic.remote.dto.VideoData;
import com.dianping.piccentercloud.display.api.enums.WaterMark;
import org.junit.Before;
import org.junit.Test;
import org.junit.Assert;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ShopReviewHelperTest {

    private ReviewDataV2 reviewData;
    private Map<Long, ReviewUserModel> reviewUserModelMap;

    /**
     * 测试videoDataList和mtReviewPicInfoList都为空
     */
    @Test
    public void testBuildMtReviewPicList_EmptyLists() {
        // arrange
        List<VideoData> videoDataList = new ArrayList<>();
        List<MtReviewPicInfo> mtReviewPicInfoList = new ArrayList<>();
        // act
        List<ReviewPicDTO> result = ShopReviewHelper.buildMtReviewPicList(videoDataList, mtReviewPicInfoList);
        // assert
        Assert.assertTrue("结果应为空列表", result.isEmpty());
    }

    /**
     * 测试videoDataList不为空，mtReviewPicInfoList为空
     */
    @Test
    public void testBuildMtReviewPicList_NonEmptyVideoDataList() {
        // arrange
        List<VideoData> videoDataList = new ArrayList<>();
        VideoData videoData = new VideoData();
        videoData.setUrl("testUrl");
        videoData.setDuration(120L);
        videoData.setModTime(new Date());
        videoDataList.add(videoData);
        List<MtReviewPicInfo> mtReviewPicInfoList = new ArrayList<>();
        // act
        List<ReviewPicDTO> result = ShopReviewHelper.buildMtReviewPicList(videoDataList, mtReviewPicInfoList);
        // assert
        Assert.assertFalse("结果不应为空列表", result.isEmpty());
        assertEquals("列表大小应为1", 1, result.size());
        assertEquals("视频类型应为2", 2, result.get(0).getType());
    }

    /**
     * 测试videoDataList为空，mtReviewPicInfoList不为空
     */
    @Test
    public void testBuildMtReviewPicList_NonEmptyMtReviewPicInfoList() {
        // arrange
        List<VideoData> videoDataList = new ArrayList<>();
        List<MtReviewPicInfo> mtReviewPicInfoList = new ArrayList<>();
        MtReviewPicInfo mtReviewPicInfo = new MtReviewPicInfo();
        mtReviewPicInfo.setUrl("/w.h/testUrl");
        mtReviewPicInfo.setTitle("testTitle");
        mtReviewPicInfo.setAddTime(new Date());
        mtReviewPicInfoList.add(mtReviewPicInfo);
        // act
        List<ReviewPicDTO> result = ShopReviewHelper.buildMtReviewPicList(videoDataList, mtReviewPicInfoList);
        // assert
        Assert.assertFalse("结果不应为空列表", result.isEmpty());
        assertEquals("列表大小应为1", 1, result.size());
        assertEquals("图片类型应为1", 1, result.get(0).getType());
    }

    /**
     * 测试videoDataList和mtReviewPicInfoList都不为空
     */
    @Test
    public void testBuildMtReviewPicList_NonEmptyLists() {
        // arrange
        List<VideoData> videoDataList = new ArrayList<>();
        VideoData videoData = new VideoData();
        videoData.setUrl("videoUrl");
        videoData.setDuration(120L);
        videoData.setModTime(new Date());
        videoDataList.add(videoData);
        List<MtReviewPicInfo> mtReviewPicInfoList = new ArrayList<>();
        MtReviewPicInfo mtReviewPicInfo = new MtReviewPicInfo();
        mtReviewPicInfo.setUrl("/w.h/picUrl");
        mtReviewPicInfo.setTitle("picTitle");
        mtReviewPicInfo.setAddTime(new Date());
        mtReviewPicInfoList.add(mtReviewPicInfo);
        // act
        List<ReviewPicDTO> result = ShopReviewHelper.buildMtReviewPicList(videoDataList, mtReviewPicInfoList);
        // assert
        Assert.assertFalse("结果不应为空列表", result.isEmpty());
        assertEquals("列表大小应为2", 2, result.size());
    }

    /**
     * 测试videoDataList包含null元素
     */
    @Test
    public void testBuildMtReviewPicList_VideoDataListWithNull() {
        // arrange
        List<VideoData> videoDataList = new ArrayList<>();
        videoDataList.add(null);
        List<MtReviewPicInfo> mtReviewPicInfoList = new ArrayList<>();
        // act
        List<ReviewPicDTO> result = ShopReviewHelper.buildMtReviewPicList(videoDataList, mtReviewPicInfoList);
        // assert
        Assert.assertTrue("结果应为空列表", result.isEmpty());
    }

    /**
     * 测试mtReviewPicInfoList包含null元素
     */
    @Test
    public void testBuildMtReviewPicList_MtReviewPicInfoListWithNull() {
        // arrange
        List<VideoData> videoDataList = new ArrayList<>();
        List<MtReviewPicInfo> mtReviewPicInfoList = new ArrayList<>();
        mtReviewPicInfoList.add(null);
        // act
        List<ReviewPicDTO> result = ShopReviewHelper.buildMtReviewPicList(videoDataList, mtReviewPicInfoList);
        // assert
        Assert.assertTrue("结果应为空列表", result.isEmpty());
    }

    @Before
    public void setUp() {
        reviewData = Mockito.mock(ReviewDataV2.class, Mockito.RETURNS_DEEP_STUBS);
        when(reviewData.getStar().getAccurateValue()).thenReturn(10);
        reviewUserModelMap = new HashMap<>();
    }

    /**
     * 测试 reviewDataToReviewDetailDO 方法，当 reviewData 为 null 时
     */
    @Test(expected = NullPointerException.class)
    public void testReviewDataToReviewDetailDONullReviewData() {
        ShopReviewHelper.reviewDataToReviewDetailDO(null, reviewUserModelMap, true);
    }

    /**
     * 测试 reviewDataToReviewDetailDO 方法，当 supply 为 true 时
     */
    @Test
    public void testReviewDataToReviewDetailDOSupplyTrue() {
        when(reviewData.getReviewIdLong()).thenReturn(12345L);
        when(reviewData.getStar()).thenReturn(new Star()); // 模拟 star 为 null 的情况

        ReviewDetailDO result = ShopReviewHelper.reviewDataToReviewDetailDO(reviewData, reviewUserModelMap, true);

        assertNotNull(result);
        assertEquals("12345", result.getReviewId());
        assertTrue(result.getStar() == 0); // 断言 star 为 null
    }

    /**
     * 测试 reviewDataToReviewDetailDO 方法，当 reviewUserModelMap 包含 reviewData 的 userId 时
     */
    @Test
    public void testReviewDataToReviewDetailDOWithUserModel() {
        when(reviewData.getUserId()).thenReturn(123L);
        when(reviewData.getReviewIdLong()).thenReturn(12345L);

        ReviewUserModel userModel = new ReviewUserModel();
        userModel.setUserIdL(123L);
        userModel.setUserName("测试用户");
        reviewUserModelMap.put(123L, userModel);

        ReviewDetailDO result = ShopReviewHelper.reviewDataToReviewDetailDO(reviewData, reviewUserModelMap, false);

        assertNotNull(result);
        assertEquals("测试用户", result.getReviewUserModel().getUserName());
    }

    /**
     * 测试 reviewDataToReviewDetailDO 方法，当 reviewData 中的 reviewPics 为空时
     */
    @Test
    public void testReviewDataToReviewDetailDOEmptyReviewPics() {
        when(reviewData.getReviewIdLong()).thenReturn(12345L);
        when(reviewData.getReviewPics()).thenReturn(null); // 模拟 reviewPics 为空的情况

        ReviewDetailDO result = ShopReviewHelper.reviewDataToReviewDetailDO(reviewData, reviewUserModelMap, false);

        assertNotNull(result);
        assertTrue(result.getReviewPicList().isEmpty()); // 断言 reviewPicList 为空
    }
}
