package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.factory.impl;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.factory.AbstractMassageStrategy;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

public class FootMassageStrategyImplTest {

    private FootMassageStrategyImpl footMassageStrategy = new FootMassageStrategyImpl();

    private String invokePrivateMethod(String methodName, String argument) throws Exception {
        Method method = FootMassageStrategyImpl.class.getDeclaredMethod(methodName, String.class);
        method.setAccessible(true);
        return (String) method.invoke(footMassageStrategy, argument);
    }

    @Test
    public void testGetToolValueWhenServiceProjectAttrsIsEmpty() throws Throwable {
        String result = footMassageStrategy.getToolValue(Collections.emptyList());
        assertNull(result);
    }

    @Test
    public void testGetToolValueWhenNoToolValues() throws Throwable {
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("otherTool");
        serviceProjectAttrDTO.setAttrValue("otherValue");
        String result = footMassageStrategy.getToolValue(Arrays.asList(serviceProjectAttrDTO));
        assertNull(result);
    }

    @Test
    public void testGetToolValueWhenHasElectricBucket() throws Throwable {
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("footbathBucket");
        serviceProjectAttrDTO.setAttrValue("电动按摩洗脚桶");
        String result = footMassageStrategy.getToolValue(Arrays.asList(serviceProjectAttrDTO));
        assertEquals("电动按摩洗脚桶", result);
    }

    @Test
    public void testGetToolValueWhenHasHotpackTool() throws Throwable {
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("hotpackTool");
        serviceProjectAttrDTO.setAttrValue("hotpackValue");
        String result = footMassageStrategy.getToolValue(Arrays.asList(serviceProjectAttrDTO));
        assertEquals("hotpackValue", result);
    }

    @Test
    public void testGetToolValueWhenHasMassageTool() throws Throwable {
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("massageTool");
        serviceProjectAttrDTO.setAttrValue("massageValue");
        String result = footMassageStrategy.getToolValue(Arrays.asList(serviceProjectAttrDTO));
        assertEquals("massageValue", result);
    }

    @Test
    public void testGetToolValueWhenHasFootBathBag() throws Throwable {
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("footbathMaterial");
        serviceProjectAttrDTO.setAttrValue("footBathBagValue");
        String result = footMassageStrategy.getToolValue(Arrays.asList(serviceProjectAttrDTO));
        assertEquals("footBathBagValue", result);
    }

    @Test
    public void testGetToolValueWhenHasFootBathBasin() throws Throwable {
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("footbathBucket");
        serviceProjectAttrDTO.setAttrValue("footBathBasinValue");
        String result = footMassageStrategy.getToolValue(Arrays.asList(serviceProjectAttrDTO));
        assertEquals("footBathBasinValue", result);
    }

    /**
     * 测试 getFootBathBagValue 方法，当 footBathBag 包含 "牛奶"，但不包含分隔符 "、" 时
     */
    @Test
    public void testGetFootBathBagValueCase1() throws Throwable {
        // arrange
        String footBathBag = "牛奶、葡萄、苹果";
        // act
        String result = invokePrivateMethod("getFootBathBagValue", footBathBag);
        // assert
        assertEquals("牛奶包、葡萄等3种任选", result);
    }

    /**
     * 测试 getFootBathBagValue 方法，当 footBathBag 包含 "牛奶"，且包含分隔符 "、"，且 bags 的大小等于2 时
     */
    @Test
    public void testGetFootBathBagValueCase2() throws Throwable {
        // arrange
        String footBathBag = "牛奶、葡萄";
        // act
        String result = invokePrivateMethod("getFootBathBagValue", footBathBag);
        // assert
        assertEquals("牛奶包、葡萄任选", result);
    }

    /**
     * 测试 getFootBathBagValue 方法，当 footBathBag 包含 "牛奶"，且包含分隔符 "、"，且 bags 的大小不等于2 时
     */
    @Test
    public void testGetFootBathBagValueCase3() throws Throwable {
        // arrange
        String footBathBag = "牛奶、葡萄、苹果";
        // act
        String result = invokePrivateMethod("getFootBathBagValue", footBathBag);
        // assert
        assertEquals("牛奶包、葡萄等3种任选", result);
    }
}
