package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.mobile.mapi.dztgdetail.entity.LogPrintConditionConfig;
import com.meituan.inf.xmdlog.XMDLogFormat;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;

@RunWith(MockitoJUnitRunner.class)
public class LogUtilsTest {

    private MockedStatic<LogUtils> logUtilsMockedStatic;

    @Before
    public void setUp() {
        logUtilsMockedStatic = mockStatic(LogUtils.class);
    }

    @After
    public void tearDown() {
        logUtilsMockedStatic.close();
    }

    /**
     * 测试logPrintSwitch为false时的情况
     */
    @Test
    public void testInfoLogPrintSwitchFalse() throws Throwable {
        // arrange
        logUtilsMockedStatic.when(() -> LogUtils.getLogPrintSwitch(any())).thenReturn(false);
        Map<String, String> tags = new HashMap<>();
        String message = "test message";
        Object[] objects = new Object[]{"test object"};

        XMDLogFormat logFormat = mock(XMDLogFormat.class);
        // act
        LogUtils.info(tags, message, objects);

        // assert
        assertNotNull(logFormat);
    }

    /**
     * 测试logPrintSwitch为true，tags为null时的情况
     */
    @Test
    public void testInfoTagsNull() throws Throwable {
        // arrange
        logUtilsMockedStatic.when(() -> LogUtils.getLogPrintSwitch(any())).thenReturn(true);

        Map<String, String> tags = new HashMap<>();
        tags.put("key", "value"); // 为tags参数赋予一个非空的值
        String message = "test message";
        Object[] objects = new Object[]{"test object"};
        XMDLogFormat logFormat = mock(XMDLogFormat.class);
        // act
        LogUtils.info(tags, message, objects);
        // assert
        assertNotNull(logFormat);
    }

    /**
     * 测试logPrintSwitch为true，tags不为null时的情况
     */
    @Test
    public void testInfoTagsNotNull() throws Throwable {
        // arrange
        logUtilsMockedStatic.when(() -> LogUtils.getLogPrintSwitch(any())).thenReturn(true);

        Map<String, String> tags = new HashMap<>();
        tags.put("key", "value"); // 为tags参数赋予一个非空的值
        String message = "test message";
        Object[] objects = new Object[]{"test object"};
        XMDLogFormat logFormat = mock(XMDLogFormat.class);
        // act
        LogUtils.info(tags, message, objects);
        // assert
        assertNotNull(logFormat);
    }

    /**
     * 测试 logPrintSwitch 为 false 时，不打印日志
     */
    @Test
    public void testInfoLogPrintSwitchFalse2() throws Throwable {
        // arrange
        logUtilsMockedStatic.when(() -> LogUtils.getLogPrintSwitch(any())).thenReturn(false);

        String message = "test message";
        Object[] objects = new Object[]{"test object"};
        XMDLogFormat logFormat = mock(XMDLogFormat.class);
        // act
        LogUtils.info(message, objects);

        // assert
        assertNotNull(logFormat);
    }

    /**
     * 测试 logPrintSwitch 为 true 时，打印日志
     */
    @Test
    public void testInfoLogPrintSwitchTrue() throws Throwable {
        // arrange
        logUtilsMockedStatic.when(() -> LogUtils.getLogPrintSwitch(any())).thenReturn(true);

        String message = "test message";
        Object[] objects = new Object[]{"test object"};
        XMDLogFormat logFormat = mock(XMDLogFormat.class);
        // act
        LogUtils.info(message, objects);

        // assert
        assertNotNull(logFormat);
    }

    /**
     * 测试 error 方法，logPrintSwitch 为 true，tags 为 null
     */
    @Test
    public void testErrorLogPrintSwitchTrueTagsNull() throws Throwable {
        // arrange
        logUtilsMockedStatic.when(() -> LogUtils.getLogPrintSwitch(any())).thenReturn(true);

        Exception ex = new Exception("test exception");
        Map<String, String> tags = null;
        String message = "test message";
        Object[] objects = new Object[]{"param1", "param2"};
        XMDLogFormat logFormat = mock(XMDLogFormat.class);

        // act
        LogUtils.error(ex, tags, message, objects);

        // assert
        assertNotNull(logFormat);
    }

    /**
     * 测试 error 方法，logPrintSwitch 为 true，tags 不为 null
     */
    @Test
    public void testErrorLogPrintSwitchTrueTagsNotNull() throws Throwable {
        // arrange
        logUtilsMockedStatic.when(() -> LogUtils.getLogPrintSwitch(any())).thenReturn(true);

        Exception ex = new Exception("test exception");
        Map<String, String> tags = new HashMap<>();
        tags.put("tag1", "value1");
        tags.put("tag2", "value2");
        String message = "test message";
        Object[] objects = new Object[]{"param1", "param2"};
        XMDLogFormat logFormat = mock(XMDLogFormat.class);

        // act
        LogUtils.error(ex, tags, message, objects);

        // assert
        assertNotNull(logFormat);
    }

    /**
     * 测试 error 方法，logPrintSwitch 为 false
     */
    @Test
    public void testErrorLogPrintSwitchFalse() throws Throwable {
        // arrange
        logUtilsMockedStatic.when(() -> LogUtils.getLogPrintSwitch(any())).thenReturn(false);

        Exception ex = new Exception("test exception");
        String message = "test message";
        Object[] objects = new Object[]{"param1", "param2"};
        XMDLogFormat logFormat = mock(XMDLogFormat.class);

        // act
        LogUtils.error(ex, null, message, objects);

        // assert
        assertNotNull(logFormat);
    }

    @Test
    public void testErrorLogPrintSwitchFalse2() throws Throwable {
        // arrange
        logUtilsMockedStatic.when(() -> LogUtils.getLogPrintSwitch(any())).thenReturn(false);

        Exception ex = new Exception("test exception");
        String message = "test message";
        Object[] objects = new Object[]{"test object"};
        XMDLogFormat logFormat = mock(XMDLogFormat.class);

        // act
        LogUtils.error(ex, message, objects);

        // assert
        assertNotNull(logFormat);
    }

    /**
     * 测试 warn 方法，当 logPrintSwitch 为 false 时
     */
    @Test
    public void testWarnLogPrintSwitchFalse() throws Throwable {
        // arrange
        logUtilsMockedStatic.when(() -> LogUtils.getLogPrintSwitch(any())).thenReturn(false);

        Map<String, String> tags = new HashMap<>();
        tags.put("tag", "test");
        String message = "test message";
        Object[] objects = new Object[]{"test"};
        XMDLogFormat logFormat = mock(XMDLogFormat.class);

        // act
        LogUtils.warn(tags, message, objects);

        // assert
        assertNotNull(logFormat);
    }


    /**
     * 测试 warn 方法，当 logPrintSwitch 为 true，tags 为 null 时
     */
    @Test
    public void testWarnLogPrintSwitchTrueTagsNull() throws Throwable {
        // arrange
        logUtilsMockedStatic.when(() -> LogUtils.getLogPrintSwitch(any())).thenReturn(true);

        String message = "test message";
        Object[] objects = new Object[]{"test"};
        XMDLogFormat logFormat = mock(XMDLogFormat.class);
        // act
        LogUtils.warn(null, message, objects);

        // assert
        assertNotNull(logFormat);
    }

    /**
     * 测试 warn 方法，当 logPrintSwitch 为 true，tags 不为 null 时
     */
    @Test
    public void testWarnLogPrintSwitchTrueTagsNotNull() throws Throwable {
        // arrange
        logUtilsMockedStatic.when(() -> LogUtils.getLogPrintSwitch(any())).thenReturn(true);

        Map<String, String> tags = new HashMap<>();
        tags.put("tag", "test");
        String message = "test message";
        Object[] objects = new Object[]{"test"};
        XMDLogFormat logFormat = mock(XMDLogFormat.class);
        // act
        LogUtils.warn(tags, message, objects);

        // assert
        assertNotNull(logFormat);
    }

    /**
     * 测试getLogPrintSwitch方法，当logPrintConditionConfig为空时应返回false
     */
    @Test
    public void testGetLogPrintSwitchWithEmptyConfig() {
        // arrange
        List<LogPrintConditionConfig> config = new ArrayList<>();

        // act
        boolean result = LogUtils.getLogPrintSwitch(config);

        // assert
        assertFalse(result);
    }

    /**
     * 测试 matchStrategy 方法，当 objs 为 null 时应返回 false。
     */
    @Test
    public void testMatchStrategyWithNullObjs() {
        // arrange
        List<String> objs = null;
        String obj = "test";
        String strategyName = "ignore";

        // act
        boolean result = LogUtils.matchStrategy(objs, obj, strategyName);

        // assert
        assertFalse(result);
    }

    /**
     * 测试 matchStrategy 方法，使用 ALLOW 策略且对象不存在于列表中时应返回 false。
     */
    @Test
    public void testMatchStrategyWithAllowStrategyAndObjNotExists() {
        // arrange
        List<?> objs = Collections.emptyList();
        Object obj = new Object();
        String strategyName = "allow";

        // act
        boolean result = LogUtils.matchStrategy(objs, obj, strategyName);

        // assert
        assertFalse(result);
    }


    /**
     * 测试 matchStrategy 方法，使用 DENY 策略且对象存在于列表中时应返回 false。
     */
    @Test
    public void testMatchStrategyWithDenyStrategyAndObjExists() {
        // arrange
        Object obj = new Object();
        List<?> objs = Arrays.asList(obj);
        String strategyName = "deny";

        // act
        boolean result = LogUtils.matchStrategy(objs, obj, strategyName);

        // assert
        assertFalse(result);
    }

    /**
     * 测试 matchStrategy 方法，使用 DENY 策略且对象不存在于列表中时应返回 true。
     */
    @Test
    public void testMatchStrategyWithDenyStrategyAndObjNotExists() {
        // arrange
        List<?> objs = Collections.emptyList();
        Object obj = new Object();
        String strategyName = "deny";

        // act
        boolean result = LogUtils.matchStrategy(objs, obj, strategyName);

        // assert
        assertFalse(result);
    }

    /**
     * 测试 matchStrategy 方法，使用不存在的策略时应返回 false。
     */
    @Test
    public void testMatchStrategyWithUnknownStrategy() {
        // arrange
        List<?> objs = Collections.emptyList();
        Object obj = new Object();
        String strategyName = "unknown";

        // act
        boolean result = LogUtils.matchStrategy(objs, obj, strategyName);

        // assert
        assertFalse(result);
    }
}
