package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.util.Arrays;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ParallDealBuilderProcessor_BuildCouponGroupIdListTest {

    private ParallDealBuilderProcessor processor = new ParallDealBuilderProcessor();

    @Mock
    private DealCtx dealCtx;

    @Mock
    private DealGroupDTO dealGroupDTO;

    @InjectMocks
    private ParallDealBuilderProcessor parallDealBuilderProcessor;

    /**
     * 测试 couponWalletInfoStr 为空字符串的情况
     */
    @Test
    public void testBuildCouponGroupIdListEmptyString() throws Throwable {
        // arrange
        String couponWalletInfoStr = "";
        // act
        List<String> result = processor.buildCouponGroupIdList(couponWalletInfoStr);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 couponWalletInfoStr 不为空，但解析为 JSONObject 后，couponPackageList 为空的情况
     */
    @Test
    public void testBuildCouponGroupIdListEmptyList() throws Throwable {
        // arrange
        String couponWalletInfoStr = "{\"couponPackageList\":[]}";
        // act
        List<String> result = processor.buildCouponGroupIdList(couponWalletInfoStr);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 couponWalletInfoStr 不为空，解析为 JSONObject 后，couponPackageList 不为空，但其中的元素没有 couponPackageId 的情况
     */
    @Test
    public void testBuildCouponGroupIdListNoCouponPackageId() throws Throwable {
        // arrange
        String couponWalletInfoStr = "{\"couponPackageList\":[{}]}";
        // act
        List<String> result = processor.buildCouponGroupIdList(couponWalletInfoStr);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 couponWalletInfoStr 不为空，解析为 JSONObject 后，couponPackageList 不为空，其中的元素有 couponPackageId 的情况
     */
    @Test
    public void testBuildCouponGroupIdListWithCouponPackageId() throws Throwable {
        // arrange
        String couponWalletInfoStr = "{\"couponPackageList\":[{\"couponPackageId\":\"123\"}]}";
        // act
        List<String> result = processor.buildCouponGroupIdList(couponWalletInfoStr);
        // assert
        assertEquals(1, result.size());
        assertEquals("123", result.get(0));
    }

    /**
     * 测试在处理过程中出现异常的情况
     */
    @Test
    public void testBuildCouponGroupIdListException() throws Throwable {
        // arrange
        String couponWalletInfoStr = "invalid json string";
        // act
        List<String> result = processor.buildCouponGroupIdList(couponWalletInfoStr);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test when deal is a times deal with multiple verifications and not anxinxue
     */
    @Test
    public void testBuildTimesDealRemind_TimesDeal_MultipleVerification_NotAnxinxue() {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getBasic()).thenReturn(mock(com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO.class));
        when(dealGroupDTO.getBasic().getTradeType()).thenReturn(19);
        when(dealGroupDTO.getAttrs()).thenReturn(Arrays.asList());
        when(dealCtx.isAnXinXue()).thenReturn(false);
        // act
        List<String> result = parallDealBuilderProcessor.buildTimesDealRemind(dealCtx);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("可单次核销多份", result.get(0));
        assertEquals("仅支持整单退", result.get(1));
    }

    /**
     * Test when deal is a times deal and is anxinxue
     */
    @Test
    public void testBuildTimesDealRemind_TimesDeal_Anxinxue() {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getBasic()).thenReturn(mock(com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO.class));
        when(dealGroupDTO.getBasic().getTradeType()).thenReturn(19);
        when(dealGroupDTO.getAttrs()).thenReturn(Arrays.asList());
        when(dealCtx.isAnXinXue()).thenReturn(true);
        // act
        List<String> result = parallDealBuilderProcessor.buildTimesDealRemind(dealCtx);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("可单次核销多份", result.get(0));
    }
}
