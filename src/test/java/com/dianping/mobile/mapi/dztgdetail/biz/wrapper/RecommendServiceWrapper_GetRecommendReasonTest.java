package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.entity.Response;
import com.dianping.martgeneral.recommend.api.enums.PlatformEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.BuyMoreSaveMoreCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.BuyMoreSaveMoreReq;
import com.dianping.mobile.mapi.dztgdetail.entity.QueryRecommendParam;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Future;
import org.apache.commons.lang3.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

/**
 * Test cases for RecommendServiceWrapper.getRecommendReason method
 */
@RunWith(MockitoJUnitRunner.class)
public class RecommendServiceWrapper_GetRecommendReasonTest {

    private RecommendServiceWrapper recommendServiceWrapper;

    private Future mockFuture;

    @Mock
    private BuyMoreSaveMoreCtx ctx;

    @Mock
    private BuyMoreSaveMoreReq req;

    @Before
    public void setUp() {
        recommendServiceWrapper = new RecommendServiceWrapper();
        mockFuture = mock(Future.class);
    }

    @After
    public void tearDown() {
        // Reset mocks
        reset(mockFuture);
    }

    /**
     * Helper method to invoke the private setRecommendParameters method using reflection.
     */
    private void invokeSetRecommendParameters(RecommendServiceWrapper recommendServiceWrapper, RecommendParameters recommendParameters, QueryRecommendParam param) throws Exception {
        Method method = RecommendServiceWrapper.class.getDeclaredMethod("setRecommendParameters", RecommendParameters.class, QueryRecommendParam.class);
        method.setAccessible(true);
        method.invoke(recommendServiceWrapper, recommendParameters, param);
    }

    /**
     * Test getRecommendReason when future is null
     */
    @Test
    public void testGetRecommendReasonFutureIsNull() throws Throwable {
        // arrange
        Future future = null;
        // act
        Map<Long, String> result = recommendServiceWrapper.getRecommendReason(future);
        // assert
        assertTrue("Result should be empty when future is null", result.isEmpty());
    }

    /**
     * Test getRecommendReason when future result is null
     */
    @Test
    public void testGetRecommendReasonFutureResultIsNull() throws Throwable {
        // arrange
        when(mockFuture.get()).thenReturn(null);
        // act
        Map<Long, String> result = recommendServiceWrapper.getRecommendReason(mockFuture);
        // assert
        assertTrue("Result should be empty when future result is null", result.isEmpty());
    }

    /**
     * Test getRecommendReason with valid future result and recommendDTOList
     */
    @Test
    public void testGetRecommendReasonWithValidResult() throws Throwable {
        // arrange
        Response<RecommendResult<RecommendDTO>> response = new Response<>();
        RecommendResult<RecommendDTO> recommendResult = new RecommendResult<>();
        RecommendDTO recommendDTO = new RecommendDTO();
        recommendDTO.setItem("1");
        Map<String, Object> bizData = new HashMap<>();
        bizData.put("reason", "Test Reason");
        recommendDTO.setBizData(bizData);
        recommendResult.setSortedResult(java.util.Collections.singletonList(recommendDTO));
        response.setResult(recommendResult);
        when(mockFuture.get()).thenReturn(response);
        // act
        Map<Long, String> result = recommendServiceWrapper.getRecommendReason(mockFuture);
        // assert
        assertFalse("Result should not be empty with valid future result", result.isEmpty());
        assertEquals("Test Reason", result.get(1L));
    }

    /**
     * Test getRecommendReason with valid future result but empty bizData
     */
    @Test
    public void testGetRecommendReasonWithEmptyBizData() throws Throwable {
        // arrange
        Response<RecommendResult<RecommendDTO>> response = new Response<>();
        RecommendResult<RecommendDTO> recommendResult = new RecommendResult<>();
        RecommendDTO recommendDTO = new RecommendDTO();
        recommendDTO.setItem("1");
        recommendDTO.setBizData(new HashMap<>());
        recommendResult.setSortedResult(java.util.Collections.singletonList(recommendDTO));
        response.setResult(recommendResult);
        when(mockFuture.get()).thenReturn(response);
        // act
        Map<Long, String> result = recommendServiceWrapper.getRecommendReason(mockFuture);
        // assert
        assertFalse("Result should not be empty with valid future result", result.isEmpty());
        assertEquals(StringUtils.EMPTY, result.get(1L));
    }

    /**
     * 测试当 ctx.getReq().getSource() 与 COST_EFFECTIVE.getSource() 相等时，返回 true
     */
    @Test
    public void testFromCostEffectiveWhenSourceIsCostEffective() throws Throwable {
        // arrange
        when(ctx.getReq()).thenReturn(req);
        when(req.getSource()).thenReturn(RequestSourceEnum.COST_EFFECTIVE.getSource());
        RecommendServiceWrapper wrapper = new RecommendServiceWrapper();
        // act
        boolean result = wrapper.fromCostEffective(ctx);
        // assert
        assertTrue(result);
    }

    /**
     * 测试当 ctx.getReq().getSource() 与 COST_EFFECTIVE.getSource() 不相等时，返回 false
     */
    @Test
    public void testFromCostEffectiveWhenSourceIsNotCostEffective() throws Throwable {
        // arrange
        when(ctx.getReq()).thenReturn(req);
        when(req.getSource()).thenReturn("other_source");
        RecommendServiceWrapper wrapper = new RecommendServiceWrapper();
        // act
        boolean result = wrapper.fromCostEffective(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * 测试当 ctx 为 null 时，抛出 NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testFromCostEffectiveWhenCtxIsNull() throws Throwable {
        // arrange
        RecommendServiceWrapper wrapper = new RecommendServiceWrapper();
        // act
        wrapper.fromCostEffective(null);
        // assert
        // 期望抛出 NullPointerException
    }

    /**
     * 测试当 ctx.getReq() 为 null 时，抛出 NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testFromCostEffectiveWhenReqIsNull() throws Throwable {
        // arrange
        when(ctx.getReq()).thenReturn(null);
        RecommendServiceWrapper wrapper = new RecommendServiceWrapper();
        // act
        wrapper.fromCostEffective(ctx);
        // assert
        // 期望抛出 NullPointerException
    }

    /**
     * 测试当 ctx.getReq().getSource() 为 null 时，返回 false
     */
    @Test
    public void testFromCostEffectiveWhenSourceIsNull() throws Throwable {
        // arrange
        when(ctx.getReq()).thenReturn(req);
        when(req.getSource()).thenReturn(null);
        RecommendServiceWrapper wrapper = new RecommendServiceWrapper();
        // act
        boolean result = wrapper.fromCostEffective(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * 测试setRecommendParameters方法，当QueryRecommendParam的部分参数为null时
     */
    @Test
    public void testSetRecommendParametersPartialNull() throws Throwable {
        RecommendServiceWrapper recommendServiceWrapper = new RecommendServiceWrapper();
        RecommendParameters recommendParameters = new RecommendParameters();
        QueryRecommendParam param = QueryRecommendParam.builder().isMt(true).cityId(1).dpId("dpId").uuid("uuid").originUserId("originUserId").platformEnum(PlatformEnum.DP).latitude(1.0).longitude(1.0).exhibitImgIds(Arrays.asList("1", "2")).filterIds("filterIds").sortType("sortType").flowFlag("flowFlag").shopRatingThreshold("shopRatingThreshold").shopId(1L).start(1).limit(10).clientType(1).categoryId(1).dealGroupPrice("dealGroupPrice").isAll("isAll").dealGroupTagIds("dealGroupTagIds").dpDealGroupId(1L).build();
        invokeSetRecommendParameters(recommendServiceWrapper, recommendParameters, param);
        assertEquals(PlatformEnum.DP, recommendParameters.getPlatformEnum());
        assertEquals("originUserId", recommendParameters.getOriginUserId());
        assertEquals(Integer.valueOf(10), recommendParameters.getPageSize());
        assertEquals(Integer.valueOf(1), recommendParameters.getPageNumber());
        assertEquals(Integer.valueOf(1), recommendParameters.getCityId());
        assertEquals(Double.valueOf(1.0), recommendParameters.getLat());
        assertEquals(Double.valueOf(1.0), recommendParameters.getLng());
        assertNotNull(recommendParameters.getBizParams());
        assertEquals("1,2", recommendParameters.getBizParams().get("contentIds"));
        assertEquals("filterIds", recommendParameters.getBizParams().get("filterIds"));
        assertEquals("sortType", recommendParameters.getBizParams().get("sortType"));
        assertEquals("flowFlag", recommendParameters.getBizParams().get("flowFlag"));
        assertEquals("shopRatingThreshold", recommendParameters.getBizParams().get("shopRatingThreshold"));
    }

    /**
     * 测试setRecommendParameters方法，当QueryRecommendParam的所有参数都不为null时
     */
    @Test
    public void testSetRecommendParametersAllNotNull() throws Throwable {
        RecommendServiceWrapper recommendServiceWrapper = new RecommendServiceWrapper();
        RecommendParameters recommendParameters = new RecommendParameters();
        QueryRecommendParam param = QueryRecommendParam.builder().isMt(true).cityId(1).dpId("dpId").uuid("uuid").originUserId("originUserId").platformEnum(PlatformEnum.DP).latitude(1.0).longitude(1.0).exhibitImgIds(Arrays.asList("1", "2")).filterIds("filterIds").sortType("sortType").flowFlag("flowFlag").shopRatingThreshold("shopRatingThreshold").shopId(1L).start(1).limit(10).clientType(1).categoryId(1).dealGroupPrice("dealGroupPrice").isAll("isAll").dealGroupTagIds("dealGroupTagIds").dpDealGroupId(1L).build();
        invokeSetRecommendParameters(recommendServiceWrapper, recommendParameters, param);
        assertEquals(PlatformEnum.DP, recommendParameters.getPlatformEnum());
        assertEquals("originUserId", recommendParameters.getOriginUserId());
        assertEquals(Integer.valueOf(10), recommendParameters.getPageSize());
        assertEquals(Integer.valueOf(1), recommendParameters.getPageNumber());
        assertEquals(Integer.valueOf(1), recommendParameters.getCityId());
        assertEquals(Double.valueOf(1.0), recommendParameters.getLat());
        assertEquals(Double.valueOf(1.0), recommendParameters.getLng());
        assertNotNull(recommendParameters.getBizParams());
        assertEquals("1,2", recommendParameters.getBizParams().get("contentIds"));
        assertEquals("filterIds", recommendParameters.getBizParams().get("filterIds"));
        assertEquals("sortType", recommendParameters.getBizParams().get("sortType"));
        assertEquals("flowFlag", recommendParameters.getBizParams().get("flowFlag"));
        assertEquals("shopRatingThreshold", recommendParameters.getBizParams().get("shopRatingThreshold"));
    }
}
