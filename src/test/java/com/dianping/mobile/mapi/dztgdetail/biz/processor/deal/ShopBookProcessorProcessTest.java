package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.book.req.ShopBookDto;
import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealBookWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DzImWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.AppCtxHelper;
import io.netty.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

public class ShopBookProcessorProcessTest {

    @InjectMocks
    private ShopBookProcessor shopBookProcessor;

    @Mock
    private DzImWrapper dzImWrapper;

    @Mock
    private DealBookWrapper dealBookWrapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Test case when bestShop is null.
     */
    @Test
    public void testProcessBestShopIsNull() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setBestShopResp(null);
        // act
        shopBookProcessor.process(ctx);
        // assert
        assertNull(ctx.getImUrl());
        assertNull(ctx.getShopBookDto());
    }

    /**
     * Test case when bestShop.getDpShopId() is less than or equal to 0.
     */
    @Test
    public void testProcessBestShopDpShopIdIsInvalid() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        BestShopDTO bestShop = new BestShopDTO();
        bestShop.setDpShopId(0);
        ctx.setBestShopResp(bestShop);
        // act
        shopBookProcessor.process(ctx);
        // assert
        assertNull(ctx.getImUrl());
        assertNull(ctx.getShopBookDto());
    }

    /**
     * Test case when dzImWrapper.getOnlineConsultUrl throws an exception.
     */
    @Test(expected = RuntimeException.class)
    public void testProcessDzImWrapperThrowsException() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        BestShopDTO bestShop = new BestShopDTO();
        bestShop.setDpShopId(1);
        ctx.setBestShopResp(bestShop);
        when(dzImWrapper.getOnlineConsultUrl(any())).thenThrow(new RuntimeException("Error"));
        // act
        shopBookProcessor.process(ctx);
        // assert
        // Exception is expected
    }

    /**
     * Test case when dealBookWrapper.queryShopDealBooKInfo throws an exception.
     */
    @Test(expected = RuntimeException.class)
    public void testProcessDealBookWrapperThrowsException() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        BestShopDTO bestShop = new BestShopDTO();
        bestShop.setDpShopId(1);
        ctx.setBestShopResp(bestShop);
        when(dzImWrapper.getOnlineConsultUrl(any())).thenReturn("http://example.com");
        when(dealBookWrapper.queryShopDealBooKInfo(anyLong(), any())).thenThrow(new RuntimeException("Error"));
        // act
        shopBookProcessor.process(ctx);
        // assert
        // Exception is expected
    }
}
