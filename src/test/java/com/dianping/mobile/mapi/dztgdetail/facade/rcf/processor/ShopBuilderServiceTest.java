package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.publishcategory.dto.ChannelDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.deal.shop.dto.Attribute;
import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.deal.shop.dto.DealGroupDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.LifeClearHighlightsProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ShopPBO;
import com.dianping.mobile.mapi.dztgdetail.entity.ApplyShopPositionConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.ApplyShopPositionStrategy;
import com.dianping.mobile.mapi.dztgdetail.entity.PoiInfoCustomizedConfig;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.ShopBuilderService;
import com.dianping.mobile.mapi.dztgdetail.util.JsonUtils;
import com.dianping.poi.bizhour.BizHourForecastService;
import com.dianping.poi.bizhour.dto.BizForecastDTO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.mock;

/**
 * @Author: <EMAIL>
 * @Date: 2024/6/9
 */
@RunWith(MockitoJUnitRunner.class)
public class ShopBuilderServiceTest {
    @InjectMocks
    private ShopBuilderService shopBuilderService;
    @Mock
    private DouHuService douHuService;
    @Mock
    private BizHourForecastService bizHourForecastService;

    @Mock
    private LifeClearHighlightsProcessor lifeClearHighlightsProcessor;

    private MockedStatic<Lion> mockedStatic;

    @Before
    public void setUp() {
        mockedStatic = mockStatic(Lion.class);
    }

    @After
    public void teardown() {
        mockedStatic.close();
    }

    @Test
    public void testGetShopPBO() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setDpShopUuid("uuid");

        BestShopDTO bestShopResp = JsonUtils.fromJson("{\"address\":\"远东大厦709\",\"distance\":\"2.98km\",\"dpShopId\":1315533376,\"glat\":31.229178,\"glng\":121.517341,\"lat\":31.231248852863505,\"lng\":121.51297187191386,\"mtShopId\":1315533376,\"phoneNos\":[\"***********\"],\"shopName\":\"V·野专业美睫美甲\",\"shopPic\":\"http://p0.meituan.net/dpmerchantpic/b672f142873e508ec5e0b4770b1ea6fc158783.jpg%40300w_225h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\",\"shopPower\":45,\"shopType\":50,\"showType\":\"beauty\",\"totalShopsNum\":1}", BestShopDTO.class);
        ctx.setBestShopResp(bestShopResp);
        ModuleAbConfig moduleAbConfig = JsonUtils.fromJson("{\"configs\":[{\"expBiInfo\":\"{\\\"query_id\\\":\\\"125309da-4ab6-4b0b-a2d7-f2c13c7be4b6\\\",\\\"ab_id\\\":\\\"exp002675_b\\\"}\",\"expId\":\"exp002675\",\"expResult\":\"b\",\"useNewStyle\":true}],\"key\":\"CardStyleAB_V2_MT_meijia\"}", ModuleAbConfig.class);
//        Mockito.when(douHuService.enableCardStyleV2(Mockito.any(), Mockito.anyInt(), Mockito.anyString())).thenReturn(moduleAbConfig);

        Map<String, String> expMap = new HashMap<>();
        expMap.put("exp003031", "bcd");
        mockedStatic.when(() -> Lion.getMap("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.card.style.v2.expId2ExpectedSks", String.class, new HashMap<>())).thenReturn(expMap);


        Mockito.when(douHuService.hitEnableCardStyleV2(Mockito.any())).thenReturn(true);

        DpPoiDTO dpPoiDTO = JsonUtils.fromJson("{\"address\":\"远东大厦709\",\"appSides\":\"1000\",\"avgPrice\":152,\"backMainCategoryPath\":[{\"categoryId\":2,\"categoryLevel\":1,\"categoryName\":\"丽人\",\"hot\":0,\"leaf\":false,\"parentId\":0},{\"categoryId\":39,\"categoryLevel\":2,\"categoryName\":\"美甲\",\"hot\":0,\"leaf\":true,\"main\":true,\"parentId\":2}],\"businessHours\":\"周一至周日\\n10:00-21:00\",\"cityId\":1,\"crossRoad\":\"\",\"defaultPic\":\"http://p0.meituan.net/dpmerchantpic/b672f142873e508ec5e0b4770b1ea6fc158783.jpg%40300w_225h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\",\"fiveScore\":4.7,\"fiveSub1\":0.0,\"fiveSub2\":0.0,\"fiveSub3\":0.0,\"fiveSub4\":0.0,\"fiveSub5\":0.0,\"fiveSub6\":0.0,\"lat\":31.229178,\"lng\":121.517341,\"mainRegionName\":\"八佰伴\",\"normPhones\":[{\"entity\":\"***********\",\"type\":\"MOBY\"}],\"power\":5,\"refinedScore1\":\"\",\"refinedScore2\":\"\",\"refinedScore3\":\"\",\"shopId\":1315533376,\"shopName\":\"V·野专业美睫美甲\",\"shopPower\":45,\"shopType\":50,\"sub5\":0,\"sub6\":0,\"useType\":1}", DpPoiDTO.class);
        ctx.setDpPoiDTO(dpPoiDTO);

        BizForecastDTO bizForecastDTO = JsonUtils.fromJson("{\"bizHourDescList\":\"10:00-21:00\",\"hasBiz\":true,\"openStatus\":true,\"today\":\"000000000000000000001111111111111111111111000000\",\"tommorrow\":\"000000000000000000001111111111111111111111000000\"}", BizForecastDTO.class);
        Mockito.when(bizHourForecastService.getBizForecast(Mockito.anyLong(), Mockito.any(), Mockito.anyString())).thenReturn(bizForecastDTO);

        DealGroupChannelDTO channelDTO = JsonUtils.fromJson("{\"categoryId\":502,\"channelDTO\":{\"channelCn\":\"丽人\",\"channelEn\":\"beauty\",\"channelGroupCn\":\"到店综合\",\"channelGroupEn\":\"general\",\"channelGroupId\":2,\"channelId\":5},\"dealGroupId\":961569983}", DealGroupChannelDTO.class);
        ctx.setChannelDTO(channelDTO);

        List<PoiInfoCustomizedConfig> poiInfoCustomizedConfigs = com.dianping.lion.common.util.JsonUtils.fromJson("[{\"clientTypes\":[100400],\"dealCategories\":[1611],\"fixPoiTitle\":\"售卖门店\",\"hideMapUrl\":false,\"hidePoiAddress\":true,\"hidePoiDistance\":false,\"poiBizType\":1,\"poiUseTypes\":[35]},{\"clientTypes\":[100501,100502,200501,200502],\"dealCategories\":[1611],\"fixPoiAddress\":\"线上售卖\",\"fixPoiTitle\":\"售卖门店\",\"hideMapUrl\":false,\"hidePoiAddress\":true,\"hidePoiDistance\":false,\"poiUseTypes\":[35]},{\"clientTypes\":[100501,100502,200501,200502],\"dealCategories\":[712],\"fixPoiTitle\":\"适用商场\",\"hideMapUrl\":false,\"hidePoiAddress\":false,\"hidePoiDistance\":false},{\"clientTypes\":[100501,100502,200501,200502],\"dealCategories\":[1205,1210],\"fixPoiTitle\":\"适用店铺\",\"hideMapUrl\":false,\"hidePoiAddress\":false,\"hidePoiDistance\":false,\"poiUseTypes\":[38]},{\"clientTypes\":[100501,100502,200501,200502],\"dealCategories\":[1226],\"fixPoiTitle\":\"经营门店\",\"hideMapUrl\":false,\"hidePoiAddress\":false,\"hidePoiDistance\":false}]", new TypeReference<List<PoiInfoCustomizedConfig>>() {});
        mockedStatic.when(() -> Lion.getList(LionConstants.POI_ADDRESS_CUSTOMIZED_CONFIG, PoiInfoCustomizedConfig.class)).thenReturn(poiInfoCustomizedConfigs);


        com.sankuai.general.product.query.center.client.dto.DealGroupDTO dealGroupDTO = new com.sankuai.general.product.query.center.client.dto.DealGroupDTO();

        AttrDTO attrDTO = Mockito.mock(AttrDTO.class);
        attrDTO.setName("configId");
        attrDTO.setValue(Collections.singletonList("是"));
        dealGroupDTO.setAttrs(Collections.singletonList(attrDTO));
        ctx.setDealGroupDTO(dealGroupDTO);


        ShopPBO result = shopBuilderService.getShopPBO(ctx);
        Assert.assertTrue(Objects.nonNull(result));
    }

    @Test
    public void testSetApplyShopPositionRulesIsNull() {
        ShopPBO shop = new ShopPBO();
        DealGroupChannelDTO channel = new DealGroupChannelDTO();
        channel.setChannelDTO(new ChannelDTO());

        shopBuilderService.setApplyShopPosition(null, shop, channel, "pageSource", "dztgClientType");

        assertEquals(1, shop.getDisplayPosition());
    }

    @Test
    public void testSetApplyShopPositionChannelIsNull() {
        ApplyShopPositionConfig rule = Mockito.mock(ApplyShopPositionConfig.class);
        ShopPBO shop = new ShopPBO();

        shopBuilderService.setApplyShopPosition(Arrays.asList(rule), shop, null, "pageSource", "dztgClientType");

        assertEquals(1, shop.getDisplayPosition());
    }

    @Test
    public void testSetApplyShopPositionChannelDTOIsNull() {
        ApplyShopPositionConfig rule = Mockito.mock(ApplyShopPositionConfig.class);
        ShopPBO shop = new ShopPBO();
        DealGroupChannelDTO channel = new DealGroupChannelDTO();

        shopBuilderService.setApplyShopPosition(Arrays.asList(rule), shop, channel, "pageSource", "dztgClientType");

        Mockito.verify(rule, Mockito.never()).getApplyShopPositionStrategy();
    }

    @Test
    public void testSetApplyShopPositionNoRuleMatch() {
        ApplyShopPositionConfig rule = Mockito.mock(ApplyShopPositionConfig.class);
        ApplyShopPositionStrategy strategy = Mockito.mock(ApplyShopPositionStrategy.class);
        when(rule.getApplyShopPositionStrategy()).thenReturn(strategy);
        when(strategy.getChannelIdStrategy()).thenReturn("DENY");
        when(strategy.getCategoryIdStrategy()).thenReturn("DENY");
        when(strategy.getPageSourceStrategy()).thenReturn("DENY");
        when(strategy.getDztgClientTypeStrategy()).thenReturn("DENY");

        ShopPBO shop = new ShopPBO();
        DealGroupChannelDTO channel = new DealGroupChannelDTO();
        channel.setChannelDTO(new ChannelDTO());

        shopBuilderService.setApplyShopPosition(Arrays.asList(rule), shop, channel, "pageSource", "dztgClientType");

        assertEquals(1, shop.getDisplayPosition());
    }

    @Test
    public void testSetApplyShopPositionOneRuleMatch() {
        ApplyShopPositionConfig rule = Mockito.mock(ApplyShopPositionConfig.class);
        ApplyShopPositionStrategy strategy = Mockito.mock(ApplyShopPositionStrategy.class);
        when(rule.getApplyShopPositionStrategy()).thenReturn(strategy);
        when(strategy.getChannelIdStrategy()).thenReturn("IGNORE");
        when(strategy.getCategoryIdStrategy()).thenReturn("IGNORE");
        when(strategy.getPageSourceStrategy()).thenReturn("IGNORE");
        when(strategy.getDztgClientTypeStrategy()).thenReturn("IGNORE");

        ShopPBO shop = new ShopPBO();
        DealGroupChannelDTO channel = new DealGroupChannelDTO();
        channel.setChannelDTO(new ChannelDTO());

        shopBuilderService.setApplyShopPosition(Arrays.asList(rule), shop, channel, "pageSource", "dztgClientType");

        assertEquals(1, shop.getDisplayPosition());
    }

    @Test
    public void testSetApplyShopPositionNoRules() throws Throwable {
        ShopPBO shop = new ShopPBO();
        DealGroupChannelDTO channel = new DealGroupChannelDTO();
        channel.setChannelDTO(new ChannelDTO());
        shopBuilderService.setApplyShopPosition(Collections.emptyList(), shop, channel, "pageSource", "dztgClientType");
        assertEquals(1, shop.getDisplayPosition());
    }

    @Test
    public void testSetApplyShopPositionNoMatch() throws Throwable {
        ApplyShopPositionConfig rule = mock(ApplyShopPositionConfig.class);
        ApplyShopPositionStrategy strategy = mock(ApplyShopPositionStrategy.class);
        when(rule.getApplyShopPositionStrategy()).thenReturn(strategy);
        when(strategy.getChannelIdStrategy()).thenReturn("DENY");
        when(strategy.getCategoryIdStrategy()).thenReturn("DENY");
        when(strategy.getPageSourceStrategy()).thenReturn("DENY");
        when(strategy.getDztgClientTypeStrategy()).thenReturn("DENY");
        ShopPBO shop = new ShopPBO();
        DealGroupChannelDTO channel = new DealGroupChannelDTO();
        channel.setChannelDTO(new ChannelDTO());
        shopBuilderService.setApplyShopPosition(Arrays.asList(rule), shop, channel, "pageSource", "dztgClientType");
        assertEquals(1, shop.getDisplayPosition());
    }
}
