package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.tgc.open.entity.BatchExProxyCouponRequest;
import com.dianping.tgc.open.entity.BizIdType;
import com.dianping.tgc.open.entity.ExProxyCouponContext;
import com.dianping.tgc.open.entity.PlatformEnum;
import com.sankuai.nib.mkt.common.base.enums.propertyValueType.ClientTypeEnum;
import java.lang.reflect.Method;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

public class CouponProcessorBuildBatchExProxyCouponRequestTest {

    private CouponProcessor couponProcessor;

    private DealCtx dealCtx;

    private EnvCtx envCtx;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        couponProcessor = new CouponProcessor();
        dealCtx = mock(DealCtx.class);
        envCtx = mock(EnvCtx.class);
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
    }

    private BatchExProxyCouponRequest invokePrivateMethod(DealCtx dealCtx) throws Exception {
        Method method = CouponProcessor.class.getDeclaredMethod("buildBatchExProxyCouponRequest", DealCtx.class);
        method.setAccessible(true);
        return (BatchExProxyCouponRequest) method.invoke(couponProcessor, dealCtx);
    }

    /**
     * Test case for DP platform scenario.
     */
    @Test
    public void testBuildBatchExProxyCouponRequest_DPPlatform() throws Throwable {
        // arrange
        when(dealCtx.isMt()).thenReturn(false);
        when(dealCtx.getDpId()).thenReturn(123);
        when(envCtx.getDpUserId()).thenReturn(456L);
        when(dealCtx.getDpLongShopId()).thenReturn(789L);
        when(dealCtx.getCityId4P()).thenReturn(101);
        when(dealCtx.getUserlat()).thenReturn(12.34);
        when(dealCtx.getUserlng()).thenReturn(56.78);
        when(dealCtx.getRequestSource()).thenReturn("source");
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.DIANPING_APP);
        when(envCtx.isIos()).thenReturn(false);
        // act
        BatchExProxyCouponRequest request = invokePrivateMethod(dealCtx);
        // assert
        assertEquals((long) PlatformEnum.DP.getCode(), (long) request.getPlatform());
        assertEquals((long) BizIdType.DEAL_GROUP_ID.getCode(), (long) request.getBizType());
        assertEquals(123L, request.getDpBizId().longValue());
        assertEquals(456L, request.getDpUserId().longValue());
        assertEquals(789L, request.getDpShopId().longValue());
        assertEquals((long) ClientTypeEnum.DP_ANDROID.getCode(), (long) request.getClientType());
        // Cast to int to resolve ambiguity
        assertEquals(101, (int) request.getCityId());
        assertEquals(12.34, request.getUserLatitude(), 0.001);
        assertEquals(56.78, request.getUserLongitude(), 0.001);
        assertEquals("source", request.getPageSource());
        assertTrue(request.getNeedDiscountCoupon());
        assertNotNull(request.getExProxyCouponContext());
    }

    /**
     * Test case for client type determination when the client is MEITUAN_APP and is iOS.
     */
    @Test
    public void testBuildBatchExProxyCouponRequest_ClientType_MEITUAN_APP_IOS() throws Throwable {
        // arrange
        when(dealCtx.isMt()).thenReturn(true);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_APP);
        when(envCtx.isIos()).thenReturn(true);
        // act
        BatchExProxyCouponRequest request = invokePrivateMethod(dealCtx);
        // assert
        assertEquals((long) ClientTypeEnum.IPHONE.getCode(), (long) request.getClientType());
    }

    /**
     * Test case for client type determination when the client is DIANPING_APP and is Android.
     */
    @Test
    public void testBuildBatchExProxyCouponRequest_ClientType_DIANPING_APP_ANDROID() throws Throwable {
        // arrange
        when(dealCtx.isMt()).thenReturn(false);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.DIANPING_APP);
        when(envCtx.isIos()).thenReturn(false);
        // act
        BatchExProxyCouponRequest request = invokePrivateMethod(dealCtx);
        // assert
        assertEquals((long) ClientTypeEnum.DP_ANDROID.getCode(), (long) request.getClientType());
    }

    /**
     * Test case for client type determination when the client is MEITUAN_WEIXIN_MINIAPP.
     */
    @Test
    public void testBuildBatchExProxyCouponRequest_ClientType_MEITUAN_WEIXIN_MINIAPP() throws Throwable {
        // arrange
        when(dealCtx.isMt()).thenReturn(true);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP);
        // act
        BatchExProxyCouponRequest request = invokePrivateMethod(dealCtx);
        // assert
        assertEquals((long) ClientTypeEnum.WE_CHAT_APPLET.getCode(), (long) request.getClientType());
    }

    /**
     * Test case for client type determination when the client is DIANPING_WEIXIN_MINIAPP.
     */
    @Test
    public void testBuildBatchExProxyCouponRequest_ClientType_DIANPING_WEIXIN_MINIAPP() throws Throwable {
        // arrange
        when(dealCtx.isMt()).thenReturn(false);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP);
        // act
        BatchExProxyCouponRequest request = invokePrivateMethod(dealCtx);
        // assert
        assertEquals((long) ClientTypeEnum.DP_WE_CHAT_APPLET.getCode(), (long) request.getClientType());
    }

    /**
     * Test case for client type determination when the client is unknown.
     */
    @Test
    public void testBuildBatchExProxyCouponRequest_ClientType_Unknown() throws Throwable {
        // arrange
        when(dealCtx.isMt()).thenReturn(true);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.UNKNOWN);
        // act
        BatchExProxyCouponRequest request = invokePrivateMethod(dealCtx);
        // assert
        assertEquals((long) ClientTypeEnum.PC.getCode(), (long) request.getClientType());
    }
}
