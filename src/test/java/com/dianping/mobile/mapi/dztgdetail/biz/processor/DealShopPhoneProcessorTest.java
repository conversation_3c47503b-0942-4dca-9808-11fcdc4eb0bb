package com.dianping.mobile.mapi.dztgdetail.biz.processor;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealShopPhoneProcessor;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2024/4/17
 */
@RunWith(MockitoJUnitRunner.class)
public class DealShopPhoneProcessorTest {
    @InjectMocks
    private DealShopPhoneProcessor dealShopPhoneProcessor;

    @Test
    public void testGetConfigUrlDztgClient() {
        List<Integer> clients = dealShopPhoneProcessor.getConfigUrlDztgClient(new DealCtx(new EnvCtx()));
        Assert.assertTrue(CollectionUtils.isEqualCollection(clients, Arrays.asList(1, 4)));
    }
}
