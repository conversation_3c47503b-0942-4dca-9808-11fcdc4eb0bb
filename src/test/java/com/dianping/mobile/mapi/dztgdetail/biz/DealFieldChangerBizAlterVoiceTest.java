package com.dianping.mobile.mapi.dztgdetail.biz;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.meituan.service.mobile.prometheus.model.DealModel;
import java.lang.reflect.Method;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealFieldChangerBizAlterVoiceTest {

    @InjectMocks
    private DealFieldChangerBiz dealFieldChangerBiz;

    @Mock
    private DealModel deal;

    private void invokeAlterVoice(DealModel deal) throws Exception {
        Method method = DealFieldChangerBiz.class.getDeclaredMethod("alterVoice", DealModel.class);
        method.setAccessible(true);
        method.invoke(dealFieldChangerBiz, deal);
    }

    @Test
    public void testAlterVoicePriceGreaterThanFiveAndVoiceIsEmpty() throws Throwable {
        // Mock deal.getPrice() to return a value greater than 5
        when(deal.getPrice()).thenReturn(5.01);
        // Mock deal.getVoice() to return an empty string
        when(deal.getVoice()).thenReturn("");
        invokeAlterVoice(deal);
        // Expect no voice modification
        verify(deal, times(0)).setVoice(anyString());
    }

    @Test
    public void testAlterVoicePriceNotLessThanFive() throws Throwable {
        // Mock deal.getPrice() to return a value greater than or equal to 5
        when(deal.getPrice()).thenReturn(5.01);
        invokeAlterVoice(deal);
        // Expect no voice modification
        verify(deal, times(0)).setVoice(anyString());
    }
}
