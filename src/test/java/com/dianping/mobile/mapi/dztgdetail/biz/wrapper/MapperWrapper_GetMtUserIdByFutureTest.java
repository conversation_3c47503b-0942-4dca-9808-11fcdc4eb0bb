package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.wpt.user.merge.query.thrift.message.FlattedBindRelationAggregateResp;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.concurrent.Future;
import org.mockito.InjectMocks;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class MapperWrapper_GetMtUserIdByFutureTest {

    @Mock
    private Future<FlattedBindRelationAggregateResp> future;

    private MapperWrapper mapperWrapper = new MapperWrapper();

    /**
     * Test case when Future object is null
     */
    @Test
    public void testGetMtUserIdByFutureFutureIsNull() throws Throwable {
        // arrange
        Future<FlattedBindRelationAggregateResp> future = null;
        // act
        long result = mapperWrapper.getMtUserIdByFuture(future);
        // assert
        assertEquals(0, result);
    }

    /**
     * Test case when Future object's result is null
     */
    @Test
    public void testGetMtUserIdByFutureResultIsNull() throws Throwable {
        // arrange
        when(future.get()).thenReturn(null);
        // act
        long result = mapperWrapper.getMtUserIdByFuture(future);
        // assert
        assertEquals(0, result);
    }

    /**
     * Test case when Future object's result's FlattedAggregateData is null
     */
    @Test
    public void testGetMtUserIdByFutureFlattedAggregateDataIsNull() throws Throwable {
        // arrange
        FlattedBindRelationAggregateResp resp = new FlattedBindRelationAggregateResp();
        when(future.get()).thenReturn(resp);
        // act
        long result = mapperWrapper.getMtUserIdByFuture(future);
        // assert
        assertEquals(0, result);
    }

    /**
     * Test case when Future object's result is not successful
     */
    @Test
    public void testGetMtUserIdByFutureResultIsNotSuccess() throws Throwable {
        // arrange
        FlattedBindRelationAggregateResp resp = new FlattedBindRelationAggregateResp();
        resp.setSuccess(false);
        when(future.get()).thenReturn(resp);
        // act
        long result = mapperWrapper.getMtUserIdByFuture(future);
        // assert
        assertEquals(0, result);
    }
}
