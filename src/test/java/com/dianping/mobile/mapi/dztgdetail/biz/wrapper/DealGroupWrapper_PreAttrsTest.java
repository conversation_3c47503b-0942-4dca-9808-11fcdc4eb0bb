package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.attribute.service.DealGroupAttributeGetService;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Arrays;
import java.util.concurrent.Future;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.*;

public class DealGroupWrapper_PreAttrsTest {

    @InjectMocks
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private DealGroupAttributeGetService dealGroupAttributeGetServiceFuture;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试dpId小于等于0，keys为空的情况
     */
    @Test
    public void testPreAttrsDpIdLessThanOrEqualToZeroAndKeysIsEmpty() throws Throwable {
        // arrange
        int dpId = 0;
        // act
        Future result = dealGroupWrapper.preAttrs(dpId, null);
        // assert
        assertNull(result);
        verify(dealGroupAttributeGetServiceFuture, never()).loadDealAttribute(anyInt(), anyList());
    }

    /**
     * 测试dpId小于等于0，keys不为空的情况
     */
    @Test
    public void testPreAttrsDpIdLessThanOrEqualToZeroAndKeysIsNotEmpty() throws Throwable {
        // arrange
        int dpId = 0;
        // act
        Future result = dealGroupWrapper.preAttrs(dpId, Arrays.asList("key1", "key2"));
        // assert
        assertNull(result);
        verify(dealGroupAttributeGetServiceFuture, never()).loadDealAttribute(anyInt(), anyList());
    }

    /**
     * 测试dpId大于0，keys为空的情况
     */
    @Test
    public void testPreAttrsDpIdGreaterThanZeroAndKeysIsEmpty() throws Throwable {
        // arrange
        int dpId = 1;
        // act
        Future result = dealGroupWrapper.preAttrs(dpId, null);
        // assert
        assertNull(result);
        verify(dealGroupAttributeGetServiceFuture, never()).loadDealAttribute(anyInt(), anyList());
    }

    /**
     * 测试dpId大于0，keys不为空，但loadDealAttribute方法抛出异常的情况
     */
    @Test
    @Ignore
    public void testPreAttrsDpIdGreaterThanZeroAndKeysIsNotEmptyButLoadDealAttributeThrowsException() throws Throwable {
        // arrange
        int dpId = 1;
        // act
        Future result = dealGroupWrapper.preAttrs(dpId, Arrays.asList("key1", "key2"));
        // assert
        assertNull(result);
        verify(dealGroupAttributeGetServiceFuture).loadDealAttribute(dpId, Arrays.asList("key1", "key2"));
    }

    /**
     * 测试dpId大于0，keys不为空，loadDealAttribute方法正常执行的情况
     */
    @Test
    @Ignore
    public void testPreAttrsDpIdGreaterThanZeroAndKeysIsNotEmptyAndLoadDealAttributeExecutesNormally() throws Throwable {
        // arrange
        int dpId = 1;
        // act
        Future result = dealGroupWrapper.preAttrs(dpId, Arrays.asList("key1", "key2"));
        // assert
        assertNull(result);
        verify(dealGroupAttributeGetServiceFuture).loadDealAttribute(dpId, Arrays.asList("key1", "key2"));
    }
}
