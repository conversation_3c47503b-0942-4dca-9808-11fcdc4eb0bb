/*
package com.dianping.mobile.mapi.dztgdetail;

import com.alibaba.fastjson.JSON;
import com.sankuai.clr.content.process.gateway.thrift.api.BookStatusGatewayService;
import com.sankuai.clr.content.process.gateway.thrift.dto.book.BookStatusQueryGatewayReqDTO;
import com.sankuai.clr.content.process.gateway.thrift.dto.book.BookStatusQueryGatewayRespDTO;
import com.sankuai.clr.content.process.gateway.thrift.enums.BookStatusSceneEnum;
import com.sankuai.clr.content.process.gateway.thrift.enums.SubjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.junit.Test;

import javax.annotation.Resource;

*/
/**
 * <AUTHOR>
 * @date 2021-06-30-5:26 下午
 *//*

@Slf4j
public class ParentChildFunReserveOnlineTest extends GenericTest {

    @Resource
    private BookStatusGatewayService bookStatusGatewayService;

    @Test
    public void testReserve(){
        BookStatusQueryGatewayReqDTO reqDTO = new BookStatusQueryGatewayReqDTO();
        reqDTO.setSubjectId(403925110L);
        reqDTO.setSubjectType(SubjectTypeEnum.DEAL_GROUP.getCode());
        reqDTO.setScene(BookStatusSceneEnum.TRADE.getCode());
        BookStatusQueryGatewayRespDTO respDTO;
        try {
            respDTO = bookStatusGatewayService.query(reqDTO);
            System.out.println(JSON.toJSONString(respDTO));
        } catch (TException e) {
            log.error("BookStatusGatewayService Query Error!", e);

        }
    }

}
*/
