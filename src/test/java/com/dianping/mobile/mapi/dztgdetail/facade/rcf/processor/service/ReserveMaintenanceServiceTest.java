package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.handler.BaseReserveMaintenanceHandler;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.handler.SelfOperatedCleaningHandler;
import com.dianping.pigeon.remoting.common.codec.json.JacksonUtils;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/11/26 19:21
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest()
public class ReserveMaintenanceServiceTest {
    @InjectMocks
    private ReserveMaintenanceService reserveMaintenanceService;

    @Mock
    private List<BaseReserveMaintenanceHandler> reserveMaintenanceHandlerList;

    @Mock
    private SelfOperatedCleaningHandler selfOperatedCleaningHandler;

    @Mock
    private DouHuBiz douHuBiz;

    @Before
    public void setUp() {
    }

    @Test
    public void testIsEnableIntegratedReserved() {
        ModuleAbConfig moduleAbConfig = JacksonUtils.deserialize(moduleAbConfigJson, ModuleAbConfig.class);
        SelfOperatedCleaningHandler handler = PowerMockito.mock(SelfOperatedCleaningHandler.class);
        ReflectionTestUtils.setField(reserveMaintenanceService, "reserveMaintenanceHandlerList",
                Lists.newArrayList(handler));
        DealCtx ctx = PowerMockito.mock(DealCtx.class);
        when(handler.getDealSecondCategory()).thenReturn(409);
        when(handler.isEnable(any(DealCtx.class))).thenReturn(true);
        when(handler.getExpName(any(Boolean.class))).thenReturn("MtSelfOperatedCleanReserveExp");
        when(ctx.getCategoryId()).thenReturn(409);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getModuleAbConfigs()).thenReturn(null);
        when(ctx.getRequestSource()).thenReturn("self_operated_cleaning_page");
        when(ctx.getEnvCtx()).thenReturn(new EnvCtx());
        when(douHuBiz.getAbExpResult(any(DealCtx.class), anyString())).thenReturn(moduleAbConfig);

        boolean enableIntegratedReserved = reserveMaintenanceService.isEnableIntegratedReserved(ctx);
        assertFalse(enableIntegratedReserved);
    }

    @Test
    public void testGetIntegratedReservedResult() {
        ModuleAbConfig moduleAbConfig = JacksonUtils.deserialize(moduleAbConfigJson, ModuleAbConfig.class);
        List<ModuleAbConfig> moduleAbConfigs = JacksonUtils.deserialize(moduleAbConfigsJson, List.class);
        SelfOperatedCleaningHandler handler = PowerMockito.mock(SelfOperatedCleaningHandler.class);
        ReflectionTestUtils.setField(reserveMaintenanceService, "reserveMaintenanceHandlerList",
                Lists.newArrayList(handler));
        DealCtx ctx = PowerMockito.mock(DealCtx.class);
        when(handler.getDealSecondCategory()).thenReturn(409);
        when(handler.isEnable(any(DealCtx.class))).thenReturn(true);
        when(handler.getExpName(any(Boolean.class))).thenReturn("MtSelfOperatedCleanReserveExp");
        when(ctx.getCategoryId()).thenReturn(409);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getModuleAbConfigs()).thenReturn(moduleAbConfigs);
        when(douHuBiz.getAbExpResult(any(DealCtx.class), anyString())).thenReturn(moduleAbConfig);
        when(handler.getAbKeys())
                .thenReturn(Lists.newArrayList("DpSelfOperatedCleanReserveExp", "MtSelfOperatedCleanReserveExp"));

        boolean integratedReservedResult = reserveMaintenanceService.getIntegratedReservedResult(ctx);
        assert integratedReservedResult;
    }

    @Test
    public void testGetIntegratedReservedConfig() {
        ModuleAbConfig moduleAbConfig = JacksonUtils.deserialize(moduleAbConfigJson, ModuleAbConfig.class);
        List<ModuleAbConfig> moduleAbConfigs = JacksonUtils.deserialize(moduleAbConfigsJson, List.class);
        SelfOperatedCleaningHandler handler = PowerMockito.mock(SelfOperatedCleaningHandler.class);
        ReflectionTestUtils.setField(reserveMaintenanceService, "reserveMaintenanceHandlerList",
                Lists.newArrayList(handler));
        DealGroupPBO ctx = PowerMockito.mock(DealGroupPBO.class);
        when(handler.getDealSecondCategory()).thenReturn(409);
        when(handler.isEnable(any(DealCtx.class))).thenReturn(true);
        when(handler.getExpName(any(Boolean.class))).thenReturn("MtSelfOperatedCleanReserveExp");
        when(ctx.getCategoryId()).thenReturn(409);
        when(ctx.getModuleAbConfigs()).thenReturn(moduleAbConfigs);
        when(douHuBiz.getAbExpResult(any(DealCtx.class), anyString())).thenReturn(moduleAbConfig);
        when(handler.getAbKeys())
                .thenReturn(Lists.newArrayList("DpSelfOperatedCleanReserveExp", "MtSelfOperatedCleanReserveExp"));

        AbConfig config = reserveMaintenanceService.getIntegratedReservedConfig(ctx, "");
        assert config != null;
    }


    /**
     * 测试 getSelfOperatedCleaningReservedResult 方法，当 handler 不存在时
     */
    @Test
    public void testGetSelfOperatedCleaningReservedResultWhenHandlerNotExist() {
        // arrange
        DealGroupPBO result = new DealGroupPBO();
        String pageSource = "source";
        when(reserveMaintenanceHandlerList.stream()).thenReturn(new ArrayList<BaseReserveMaintenanceHandler>().stream());

        // act
        boolean actualResult = reserveMaintenanceService.getSelfOperatedCleaningReservedResult(result, pageSource);

        // assert
        assertFalse(actualResult);
    }

    /**
     * 测试isHitAbTest方法，当DealGroupPBO为null时，应返回false。
     */
    @Test
    public void testIsHitAbTestWithNullDealGroupPBO() {
        boolean result = reserveMaintenanceService.isHitAbTest(null, "pageSource", selfOperatedCleaningHandler);
        assertFalse("当DealGroupPBO为null时，应返回false", result);
    }

    /**
     * 测试isHitAbTest方法，当AbConfig为null时，应返回false。
     */
    @Test
    public void testIsHitAbTestWithNullAbConfig() {
        DealGroupPBO dealGroupPBO = mock(DealGroupPBO.class);

        boolean result = reserveMaintenanceService.isHitAbTest(dealGroupPBO, "pageSource", selfOperatedCleaningHandler);
        assertFalse("当AbConfig为null时，应返回false", result);
    }

    /**
     * 测试isHitAbTest方法，当AbConfig的expResult为"c"时，应返回true。
     */
    @Test
    public void testIsHitAbTestWithExpResultC() {
        DealGroupPBO dealGroupPBO = mock(DealGroupPBO.class);
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("c");
        abConfig.setExpId("exp1");
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setConfigs(Lists.newArrayList(abConfig));
        moduleAbConfig.setKey("test1");
        when(dealGroupPBO.getModuleAbConfigs()).thenReturn(Collections.singletonList(moduleAbConfig));
        when(selfOperatedCleaningHandler.getAbKeys()).thenReturn(Lists.newArrayList("test1"));
        boolean result = reserveMaintenanceService.isHitAbTest(dealGroupPBO, "pageSource", selfOperatedCleaningHandler);
        assertTrue("当AbConfig的expResult为\"c\"时，应返回true", result);
    }

    private static final String moduleAbConfigJson = "{\"key\":\"MtSelfOperatedCleanReserveExp\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"EXP2024111500002\",\"expResult\":\"c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"6e88b15f-61c0-468d-99f0-e2af10dde5bb\\\",\\\"ab_id\\\":\\\"EXP2024111500002_c\\\"}\",\"useNewStyle\":false}]]}";
    private static final String moduleAbConfigsJson = "[\"java.util.ArrayList\",[{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MTSalesGeneralSection\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp001434\",\"expResult\":\"a\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"ea441749-d651-4c5d-86d9-51865e2adb26\\\",\\\"ab_id\\\":\\\"exp001434_a\\\"}\",\"useNewStyle\":false}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MtCouponAlleviate2Exp\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"EXP2024082700008\",\"expResult\":\"c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"239e7b94-ec68-4541-9bf3-411e9822313e\\\",\\\"ab_id\\\":\\\"EXP2024082700008_c\\\"}\",\"useNewStyle\":false}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MtCouponAlleviate2Exp\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"EXP2024082700008\",\"expResult\":\"c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"2b5235c1-f900-4261-8734-10629ff920b0\\\",\\\"ab_id\\\":\\\"EXP2024082700008_c\\\"}\",\"useNewStyle\":false}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MTPhysicalExerciseReserve\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp000882\",\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"672441d9-921d-4285-a43e-a2dc452fe075\\\",\\\"ab_id\\\":\\\"exp000882_b\\\"}\",\"useNewStyle\":true}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MtSelfOperatedCleanReserveExp\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"EXP2024111500002\",\"expResult\":\"c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"65818f59-cb25-4fdf-889f-99a353096408\\\",\\\"ab_id\\\":\\\"EXP2024111500002_c\\\"}\",\"useNewStyle\":false}]]}]]";

}