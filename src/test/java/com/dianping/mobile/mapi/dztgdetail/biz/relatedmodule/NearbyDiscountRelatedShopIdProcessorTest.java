package com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.lang.reflect.Method;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class NearbyDiscountRelatedShopIdProcessorTest {

    private NearbyDiscountRelatedShopIdProcessor processor = new NearbyDiscountRelatedShopIdProcessor();

    private String invokeGetRegionDistanceDesc(String mainRegionName, Double distance) throws Exception {
        Method method = NearbyDiscountRelatedShopIdProcessor.class.getDeclaredMethod("getRegionDistanceDesc", String.class, Double.class);
        method.setAccessible(true);
        return (String) method.invoke(null, mainRegionName, distance);
    }

    private String invokeBuildAvgPrice(Integer avgPrice) throws Exception {
        Method method = NearbyDiscountRelatedShopIdProcessor.class.getDeclaredMethod("buildAvgPrice", Integer.class);
        method.setAccessible(true);
        return (String) method.invoke(processor, avgPrice);
    }

    @Test
    public void testGetRegionDistanceDescDistanceLessThan500() throws Throwable {
        String result = invokeGetRegionDistanceDesc("mainRegionName", 400.0);
        assertEquals("mainRegionName｜距您400m", result);
    }

    @Test
    public void testGetRegionDistanceDescDistanceBetween500And100000() throws Throwable {
        String result = invokeGetRegionDistanceDesc("mainRegionName", 50000.0);
        assertEquals("mainRegionName｜距您50.0km", result);
    }

    @Test
    public void testGetRegionDistanceDescDistanceGreaterThan100000() throws Throwable {
        String result = invokeGetRegionDistanceDesc("mainRegionName", 150000.0);
        assertEquals("mainRegionName｜距您>100km", result);
    }

    @Test
    public void testGetRegionDistanceDescMainRegionNameAndDistanceAreNotNullAndOnlyDistance() throws Throwable {
        String result = invokeGetRegionDistanceDesc("mainRegionName", 50000.0);
        assertEquals("mainRegionName｜距您50.0km", result);
    }

    @Test
    public void testGetRegionDistanceDescMainRegionNameAndDistanceAreNotNullAndBoth() throws Throwable {
        String result = invokeGetRegionDistanceDesc("mainRegionName", 50000.0);
        assertEquals("mainRegionName｜距您50.0km", result);
    }

    /**
     * 测试 buildAvgPrice 方法，当 avgPrice 为 null 时
     */
    @Test
    public void testBuildAvgPriceWhenAvgPriceIsNull() throws Throwable {
        // arrange
        Integer avgPrice = null;
        // act
        String result = invokeBuildAvgPrice(avgPrice);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试 buildAvgPrice 方法，当 avgPrice 小于等于 0 时
     */
    @Test
    public void testBuildAvgPriceWhenAvgPriceIsLessThanOrEqualToZero() throws Throwable {
        // arrange
        Integer avgPrice = 0;
        // act
        String result = invokeBuildAvgPrice(avgPrice);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试 buildAvgPrice 方法，当 avgPrice 大于 0 时
     */
    @Test
    public void testBuildAvgPriceWhenAvgPriceIsGreaterThanZero() throws Throwable {
        // arrange
        Integer avgPrice = 100;
        // act
        String result = invokeBuildAvgPrice(avgPrice);
        // assert
        assertEquals("¥100/人", result);
    }
}
