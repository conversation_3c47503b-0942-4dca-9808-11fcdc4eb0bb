package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImageUrlVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.CaseCoverDTO;
import java.lang.reflect.Method;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealExhibitInfoProcessorGetStyleCoverTest {

    private DealExhibitInfoProcessor processor = new DealExhibitInfoProcessor();

    /**
     * Helper method to invoke private getStyleCover method using reflection
     */
    private ImageUrlVO invokeGetStyleCover(CaseCoverDTO caseCover, String imageBestScale) throws Exception {
        Method method = DealExhibitInfoProcessor.class.getDeclaredMethod("getStyleCover", CaseCoverDTO.class, String.class);
        method.setAccessible(true);
        return (ImageUrlVO) method.invoke(processor, caseCover, imageBestScale);
    }

    /**
     * Test getStyleCover method when caseCover is null
     * Should return null
     */
    @Test
    public void testGetStyleCoverWithNullCaseCover() throws Throwable {
        // arrange
        CaseCoverDTO caseCover = null;
        String imageBestScale = "1.0";
        // act
        ImageUrlVO result = invokeGetStyleCover(caseCover, imageBestScale);
        // assert
        assertNull(result);
    }

    /**
     * Test getStyleCover method with valid caseCover
     * Should return ImageUrlVO with values from caseCover
     */
    @Test
    public void testGetStyleCoverWithValidCaseCover() throws Throwable {
        // arrange
        CaseCoverDTO caseCover = new CaseCoverDTO();
        caseCover.setCoverUrl("http://example.com/image.jpg");
        caseCover.setCoverHeight(100);
        caseCover.setCoverWidth(200);
        String imageBestScale = "2.0";
        // act
        ImageUrlVO result = invokeGetStyleCover(caseCover, imageBestScale);
        // assert
        assertNotNull(result);
        assertEquals("http://example.com/image.jpg", result.getUrl());
        assertEquals((Object) 100, (Object) result.getHeight());
        assertEquals((Object) 200, (Object) result.getWidth());
        assertEquals("2.0", result.getImageBestScale());
        assertEquals((Object) 1, (Object) result.getType());
        assertEquals((Object) 1, (Object) result.getPicSource());
    }

    /**
     * Test getStyleCover method with caseCover having default values
     * Should return ImageUrlVO with default values for those fields
     */
    @Test
    public void testGetStyleCoverWithCaseCoverHavingDefaultValues() throws Throwable {
        // arrange
        CaseCoverDTO caseCover = new CaseCoverDTO();
        // Only set URL, leave height and width as default (0 for int)
        caseCover.setCoverUrl("http://example.com/image.jpg");
        String imageBestScale = "1.5";
        // act
        ImageUrlVO result = invokeGetStyleCover(caseCover, imageBestScale);
        // assert
        assertNotNull(result);
        assertEquals("http://example.com/image.jpg", result.getUrl());
        assertEquals((Object) 0, (Object) result.getHeight());
        assertEquals((Object) 0, (Object) result.getWidth());
        assertEquals("1.5", result.getImageBestScale());
        assertEquals((Object) 1, (Object) result.getType());
        assertEquals((Object) 1, (Object) result.getPicSource());
    }

    /**
     * Test getStyleCover method with null imageBestScale
     * Should return ImageUrlVO with null imageBestScale
     */
    @Test
    public void testGetStyleCoverWithNullImageBestScale() throws Throwable {
        // arrange
        CaseCoverDTO caseCover = new CaseCoverDTO();
        caseCover.setCoverUrl("http://example.com/image.jpg");
        caseCover.setCoverHeight(100);
        caseCover.setCoverWidth(200);
        String imageBestScale = null;
        // act
        ImageUrlVO result = invokeGetStyleCover(caseCover, imageBestScale);
        // assert
        assertNotNull(result);
        assertEquals("http://example.com/image.jpg", result.getUrl());
        assertEquals((Object) 100, (Object) result.getHeight());
        assertEquals((Object) 200, (Object) result.getWidth());
        assertNull(result.getImageBestScale());
        assertEquals((Object) 1, (Object) result.getType());
        assertEquals((Object) 1, (Object) result.getPicSource());
    }

    /**
     * Test getStyleCover method with empty string imageBestScale
     * Should return ImageUrlVO with empty string imageBestScale
     */
    @Test
    public void testGetStyleCoverWithEmptyImageBestScale() throws Throwable {
        // arrange
        CaseCoverDTO caseCover = new CaseCoverDTO();
        caseCover.setCoverUrl("http://example.com/image.jpg");
        caseCover.setCoverHeight(100);
        caseCover.setCoverWidth(200);
        String imageBestScale = "";
        // act
        ImageUrlVO result = invokeGetStyleCover(caseCover, imageBestScale);
        // assert
        assertNotNull(result);
        assertEquals("http://example.com/image.jpg", result.getUrl());
        assertEquals((Object) 100, (Object) result.getHeight());
        assertEquals((Object) 200, (Object) result.getWidth());
        assertEquals("", result.getImageBestScale());
        assertEquals((Object) 1, (Object) result.getType());
        assertEquals((Object) 1, (Object) result.getPicSource());
    }

    /**
     * Test getStyleCover method with caseCover having zero values
     * Should return ImageUrlVO with zero values for height and width
     */
    @Test
    public void testGetStyleCoverWithZeroValues() throws Throwable {
        // arrange
        CaseCoverDTO caseCover = new CaseCoverDTO();
        caseCover.setCoverUrl("http://example.com/image.jpg");
        caseCover.setCoverHeight(0);
        caseCover.setCoverWidth(0);
        String imageBestScale = "1.0";
        // act
        ImageUrlVO result = invokeGetStyleCover(caseCover, imageBestScale);
        // assert
        assertNotNull(result);
        assertEquals("http://example.com/image.jpg", result.getUrl());
        assertEquals((Object) 0, (Object) result.getHeight());
        assertEquals((Object) 0, (Object) result.getWidth());
        assertEquals("1.0", result.getImageBestScale());
        assertEquals((Object) 1, (Object) result.getType());
        assertEquals((Object) 1, (Object) result.getPicSource());
    }

    /**
     * Test getStyleCover method with caseCover having negative values
     * Should return ImageUrlVO with negative values for height and width
     */
    @Test
    public void testGetStyleCoverWithNegativeValues() throws Throwable {
        // arrange
        CaseCoverDTO caseCover = new CaseCoverDTO();
        caseCover.setCoverUrl("http://example.com/image.jpg");
        caseCover.setCoverHeight(-10);
        caseCover.setCoverWidth(-20);
        String imageBestScale = "1.0";
        // act
        ImageUrlVO result = invokeGetStyleCover(caseCover, imageBestScale);
        // assert
        assertNotNull(result);
        assertEquals("http://example.com/image.jpg", result.getUrl());
        assertEquals((Object) (-10), (Object) result.getHeight());
        assertEquals((Object) (-20), (Object) result.getWidth());
        assertEquals("1.0", result.getImageBestScale());
        assertEquals((Object) 1, (Object) result.getType());
        assertEquals((Object) 1, (Object) result.getPicSource());
    }

    /**
     * Test getStyleCover method with empty URL
     * Should return ImageUrlVO with empty URL
     */
    @Test
    public void testGetStyleCoverWithEmptyUrl() throws Throwable {
        // arrange
        CaseCoverDTO caseCover = new CaseCoverDTO();
        caseCover.setCoverUrl("");
        caseCover.setCoverHeight(100);
        caseCover.setCoverWidth(200);
        String imageBestScale = "1.0";
        // act
        ImageUrlVO result = invokeGetStyleCover(caseCover, imageBestScale);
        // assert
        assertNotNull(result);
        assertEquals("", result.getUrl());
        assertEquals((Object) 100, (Object) result.getHeight());
        assertEquals((Object) 200, (Object) result.getWidth());
        assertEquals("1.0", result.getImageBestScale());
        assertEquals((Object) 1, (Object) result.getType());
        assertEquals((Object) 1, (Object) result.getPicSource());
    }

    /**
     * Test getStyleCover method with null URL
     * Should return ImageUrlVO with null URL
     */
    @Test
    public void testGetStyleCoverWithNullUrl() throws Throwable {
        // arrange
        CaseCoverDTO caseCover = new CaseCoverDTO();
        caseCover.setCoverUrl(null);
        caseCover.setCoverHeight(100);
        caseCover.setCoverWidth(200);
        String imageBestScale = "1.0";
        // act
        ImageUrlVO result = invokeGetStyleCover(caseCover, imageBestScale);
        // assert
        assertNotNull(result);
        assertNull(result.getUrl());
        assertEquals((Object) 100, (Object) result.getHeight());
        assertEquals((Object) 200, (Object) result.getWidth());
        assertEquals("1.0", result.getImageBestScale());
        assertEquals((Object) 1, (Object) result.getType());
        assertEquals((Object) 1, (Object) result.getPicSource());
    }
}
