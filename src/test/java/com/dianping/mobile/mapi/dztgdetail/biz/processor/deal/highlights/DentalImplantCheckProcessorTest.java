package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.google.common.collect.Lists;
import com.sankuai.clr.content.core.thrift.dto.ContentReservationPlanInfoDTO;
import com.sankuai.clr.content.core.thrift.dto.PlanDTO;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class DentalImplantCheckProcessorTest {

    @InjectMocks
    private DentalImplantCheckProcessor processor;


    @Test
    public void testGetHighlightIdentify() {
        String highlightsIdentify = processor.getHighlightsIdentify(null);
        Assert.assertNull(highlightsIdentify);
    }

    @Test
    public void testGetHighlightsStyle() {
        String highlightsStyle = processor.getHighlightsStyle(null);
        Assert.assertNotNull(highlightsStyle);
    }

    @Test
    public void testGetHighlightsContent() {
        DealCtx dealCtx = buildContext();
        String highlightsContent = processor.getHighlightsContent(dealCtx);
        Assert.assertNotNull(highlightsContent);
    }

    private DealCtx buildContext() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        //到店礼
        PlanDTO planDTO = new PlanDTO();
        planDTO.setBookable(true);
        ContentReservationPlanInfoDTO planInfoDTO = new ContentReservationPlanInfoDTO();
        planInfoDTO.setToShopPreferenceDesc("到店优惠");
        planDTO.setReservationPlan(planInfoDTO);
        ctx.setPlanDTO(planDTO);
        //热卖点
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<AttrDTO> attrDTOS = new ArrayList<>();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("medical_selling_point");
        attrDTO.setValue(Lists.newArrayList("aaa","bbbb","ccc"));
        attrDTOS.add(attrDTO);
        dealGroupDTO.setAttrs(attrDTOS);
        ctx.setDealGroupDTO(dealGroupDTO);
        return ctx;
    }

}
