package com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler.flatten;

import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.mobile.mapi.dztgdetail.rcf.enums.ModuleType;
import com.dianping.mobile.mapi.dztgdetail.util.ApplicationContextGetBeanHelper;
import java.util.ArrayList;
import java.util.List;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.context.ApplicationContext;

/**
 * <AUTHOR>
 * @create 2024/12/25 11:16
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({ ApplicationContextGetBeanHelper.class, ApplicationContext.class })
public class DealListType3FlattenHandlerTest {

    @InjectMocks
    private DealListType3FlattenHandler flattenHandler;

    private DealListType3FlattenHandler dealListType3FlattenHandler = new DealListType3FlattenHandler();

    @Test
    public void getType() {
        Assert.assertTrue(ModuleType.detailist_type3 == flattenHandler.getType());
    }

    @Test
    public void flattenModule() {
        String json = "{\"standardServiceModel\":null,\"videoModel\":null,\"skuGroupsModel2\":null,\"name\":null,\"type\":\"detailist_type3\",\"subTitle\":null,\"descModel\":null,\"priceModel\":null,\"titleModel\":null,\"skuGroupsModel1\":[{\"dealSkuList\":[{\"subTitle\":null,\"popup\":null,\"desc\":null,\"items\":[{\"icon\":null,\"config\":null,\"type\":0,\"valueAttrs\":null,\"picValues\":null,\"value\":\"1~20人\",\"name\":\"人数\",\"rightText\":null,\"extraExplain\":null,\"originalPrice\":null,\"closeTitle\":null,\"openTitle\":null,\"showAll\":false},{\"icon\":null,\"config\":null,\"type\":0,\"valueAttrs\":null,\"picValues\":null,\"value\":\"豪华间（大）\",\"name\":\"包型\",\"rightText\":null,\"extraExplain\":null,\"originalPrice\":null,\"closeTitle\":null,\"openTitle\":null,\"showAll\":false},{\"icon\":null,\"config\":null,\"type\":0,\"valueAttrs\":null,\"picValues\":null,\"value\":\"12:00~次日00:00\",\"name\":\"适用时间\",\"rightText\":null,\"extraExplain\":null,\"originalPrice\":null,\"closeTitle\":null,\"openTitle\":null,\"showAll\":false},{\"icon\":null,\"config\":null,\"type\":0,\"valueAttrs\":null,\"picValues\":null,\"value\":\"白天档、黄金档、凌晨档\",\"name\":\"时段\",\"rightText\":null,\"extraExplain\":null,\"originalPrice\":null,\"closeTitle\":null,\"openTitle\":null,\"showAll\":false},{\"icon\":null,\"config\":null,\"type\":0,\"valueAttrs\":null,\"picValues\":null,\"value\":\"3小时\",\"name\":\"适用时长\",\"rightText\":null,\"extraExplain\":null,\"originalPrice\":null,\"closeTitle\":null,\"openTitle\":null,\"showAll\":false}],\"copies\":\"1份\",\"price\":\"1088元\",\"title\":\"[豪华间（大）白天档,黄金档,凌晨档3小时]\",\"icon\":\"https://p0.meituan.net/travelcube/f28438368cc5ebf017f037562a27d8922284.png\",\"originalPrice\":null,\"tag\":null,\"jumpUrl\":null,\"type\":null,\"rightText\":null,\"fullOccupy\":null},{\"subTitle\":null,\"popup\":null,\"desc\":null,\"items\":[{\"icon\":null,\"config\":null,\"type\":0,\"valueAttrs\":null,\"picValues\":null,\"value\":\"12瓶\",\"name\":\"酒水数量\",\"rightText\":null,\"extraExplain\":null,\"originalPrice\":null,\"closeTitle\":null,\"openTitle\":null,\"showAll\":false}],\"copies\":\"1份\",\"price\":\"360元\",\"title\":\"1664白啤啤酒12瓶\",\"icon\":\"https://p1.meituan.net/travelcube/e969a57cb1c5dd59c2cfc3ea3f51bf302230.png\",\"originalPrice\":null,\"tag\":null,\"jumpUrl\":null,\"type\":null,\"rightText\":null,\"fullOccupy\":null},{\"subTitle\":null,\"popup\":null,\"desc\":null,\"items\":[{\"icon\":null,\"config\":null,\"type\":0,\"valueAttrs\":null,\"picValues\":null,\"value\":\"12瓶\",\"name\":\"酒水数量\",\"rightText\":null,\"extraExplain\":null,\"originalPrice\":null,\"closeTitle\":null,\"openTitle\":null,\"showAll\":false}],\"copies\":\"1份\",\"price\":\"360元\",\"title\":\"1664桃红啤酒12瓶\",\"icon\":\"https://p1.meituan.net/travelcube/e969a57cb1c5dd59c2cfc3ea3f51bf302230.png\",\"originalPrice\":null,\"tag\":null,\"jumpUrl\":null,\"type\":null,\"rightText\":null,\"fullOccupy\":null},{\"subTitle\":null,\"popup\":null,\"desc\":null,\"items\":[{\"icon\":null,\"config\":null,\"type\":0,\"valueAttrs\":null,\"picValues\":null,\"value\":\"2份\",\"name\":\"餐食数量\",\"rightText\":null,\"extraExplain\":null,\"originalPrice\":null,\"closeTitle\":null,\"openTitle\":null,\"showAll\":false}],\"copies\":\"1份\",\"price\":\"188元\",\"title\":\"果盘2份\",\"icon\":\"https://p0.meituan.net/travelcube/05195b3ffff2195fb240593956169b261826.png\",\"originalPrice\":null,\"tag\":null,\"jumpUrl\":null,\"type\":null,\"rightText\":null,\"fullOccupy\":null},{\"subTitle\":null,\"popup\":null,\"desc\":null,\"items\":[{\"icon\":null,\"config\":null,\"type\":0,\"valueAttrs\":null,\"picValues\":null,\"value\":\"2份\",\"name\":\"餐食数量\",\"rightText\":null,\"extraExplain\":null,\"originalPrice\":null,\"closeTitle\":null,\"openTitle\":null,\"showAll\":false}],\"copies\":\"1份\",\"price\":\"50元\",\"title\":\"小食2份\",\"icon\":\"https://p0.meituan.net/travelcube/05195b3ffff2195fb240593956169b261826.png\",\"originalPrice\":null,\"tag\":null,\"jumpUrl\":null,\"type\":null,\"rightText\":null,\"fullOccupy\":null}],\"title\":null,\"titleStyle\":null}],\"dealStructAttrsModel1\":null,\"dealStructAttrsModel2\":null,\"extraExplain\":null,\"showNum\":0,\"foldStr\":null,\"dotType\":0,\"jumpUrl\":null,\"dealDetailModuleList2\":null,\"subTitleItems\":null}";
        JSONObject module = JSON.parseObject(json);
        JSONArray rcfSkuGroupsModule1Flatten = new JSONArray();
        flattenHandler.flattenModule(rcfSkuGroupsModule1Flatten, module);
        Assert.assertFalse(rcfSkuGroupsModule1Flatten.isEmpty());
    }

    // This scenario is documented as a known limitation within the test case.
    @Test(expected = NullPointerException.class)
    public void testFlattenModuleWhenModuleIsNull() throws Throwable {
        JSONArray rcfSkuGroupsModule1Flatten = new JSONArray();
        dealListType3FlattenHandler.flattenModule(rcfSkuGroupsModule1Flatten, null);
        Assert.assertTrue(rcfSkuGroupsModule1Flatten.isEmpty());
    }

    @Test
    public void testFlattenModuleWhenSkuGroupsModel1IsNull() throws Throwable {
        JSONObject module = new JSONObject();
        JSONArray rcfSkuGroupsModule1Flatten = new JSONArray();
        dealListType3FlattenHandler.flattenModule(rcfSkuGroupsModule1Flatten, module);
        Assert.assertTrue(rcfSkuGroupsModule1Flatten.isEmpty());
    }

    @Test
    public void testFlattenModuleWhenSkuGroupsModelIsNull() throws Throwable {
        JSONObject module = new JSONObject();
        module.put("skuGroupsModel1", new JSONArray());
        JSONArray rcfSkuGroupsModule1Flatten = new JSONArray();
        dealListType3FlattenHandler.flattenModule(rcfSkuGroupsModule1Flatten, module);
        Assert.assertTrue(rcfSkuGroupsModule1Flatten.isEmpty());
    }

    @Test
    public void testFlattenModuleWhenTitleIsNull() throws Throwable {
        JSONObject module = new JSONObject();
        JSONArray skuGroupsModel1 = new JSONArray();
        JSONObject skuGroupsModel = new JSONObject();
        skuGroupsModel1.add(skuGroupsModel);
        module.put("skuGroupsModel1", skuGroupsModel1);
        JSONArray rcfSkuGroupsModule1Flatten = new JSONArray();
        dealListType3FlattenHandler.flattenModule(rcfSkuGroupsModule1Flatten, module);
        Assert.assertTrue(rcfSkuGroupsModule1Flatten.isEmpty());
    }

    @Test
    public void testFlattenModuleWhenDealSkuListIsNull() throws Throwable {
        JSONObject module = new JSONObject();
        JSONArray skuGroupsModel1 = new JSONArray();
        JSONObject skuGroupsModel = new JSONObject();
        skuGroupsModel.put("title", "title");
        skuGroupsModel1.add(skuGroupsModel);
        module.put("skuGroupsModel1", skuGroupsModel1);
        JSONArray rcfSkuGroupsModule1Flatten = new JSONArray();
        dealListType3FlattenHandler.flattenModule(rcfSkuGroupsModule1Flatten, module);
        Assert.assertFalse(rcfSkuGroupsModule1Flatten.isEmpty());
    }

    @Test
    public void testFlattenModuleWhenDealSkuIsNull() throws Throwable {
        JSONObject module = new JSONObject();
        JSONArray skuGroupsModel1 = new JSONArray();
        JSONObject skuGroupsModel = new JSONObject();
        skuGroupsModel.put("title", "title");
        JSONArray dealSkuList = new JSONArray();
        skuGroupsModel.put("dealSkuList", dealSkuList);
        skuGroupsModel1.add(skuGroupsModel);
        module.put("skuGroupsModel1", skuGroupsModel1);
        JSONArray rcfSkuGroupsModule1Flatten = new JSONArray();
        dealListType3FlattenHandler.flattenModule(rcfSkuGroupsModule1Flatten, module);
        Assert.assertFalse(rcfSkuGroupsModule1Flatten.isEmpty());
    }

    @Test
    public void testFlattenModuleWhenItemsIsNull() throws Throwable {
        JSONObject module = new JSONObject();
        JSONArray skuGroupsModel1 = new JSONArray();
        JSONObject skuGroupsModel = new JSONObject();
        skuGroupsModel.put("title", "title");
        JSONArray dealSkuList = new JSONArray();
        JSONObject dealSku = new JSONObject();
        dealSku.put("icon", "icon");
        dealSku.put("title", "title");
        dealSku.put("copies", "copies");
        dealSku.put("price", "price");
        dealSkuList.add(dealSku);
        skuGroupsModel.put("dealSkuList", dealSkuList);
        skuGroupsModel1.add(skuGroupsModel);
        module.put("skuGroupsModel1", skuGroupsModel1);
        JSONArray rcfSkuGroupsModule1Flatten = new JSONArray();
        dealListType3FlattenHandler.flattenModule(rcfSkuGroupsModule1Flatten, module);
        Assert.assertFalse(rcfSkuGroupsModule1Flatten.isEmpty());
    }

    @Test
    public void testFlattenModuleWhenItemsIsNotNull() throws Throwable {
        JSONObject module = new JSONObject();
        JSONArray skuGroupsModel1 = new JSONArray();
        JSONObject skuGroupsModel = new JSONObject();
        skuGroupsModel.put("title", "title");
        JSONArray dealSkuList = new JSONArray();
        JSONObject dealSku = new JSONObject();
        dealSku.put("icon", "icon");
        dealSku.put("title", "title");
        dealSku.put("copies", "copies");
        dealSku.put("price", "price");
        JSONArray items = new JSONArray();
        dealSku.put("items", items);
        dealSkuList.add(dealSku);
        skuGroupsModel.put("dealSkuList", dealSkuList);
        skuGroupsModel1.add(skuGroupsModel);
        module.put("skuGroupsModel1", skuGroupsModel1);
        JSONArray rcfSkuGroupsModule1Flatten = new JSONArray();
        dealListType3FlattenHandler.flattenModule(rcfSkuGroupsModule1Flatten, module);
        Assert.assertFalse(rcfSkuGroupsModule1Flatten.isEmpty());
    }
}
