package com.dianping.mobile.mapi.dztgdetail.biz.processor.promo.extend.impl;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.PromoDetailEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBestPromoDetail;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ServiceGuarantee.DealBestPromoDetailDTO;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dealuser.price.display.api.model.PromoIdentity;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class MerchantInstantDiscountsPromoDetailHandlerTest {

    private MerchantInstantDiscountsPromoDetailHandler handler = new MerchantInstantDiscountsPromoDetailHandler();

    private boolean invokeIsReductionPromo(int promoType) throws Exception {
        Method method = MerchantInstantDiscountsPromoDetailHandler.class.getDeclaredMethod("isReductionPromo", int.class);
        method.setAccessible(true);
        return (boolean) method.invoke(handler, promoType);
    }

    /**
     * 测试 isReductionPromo 方法，当 promoType 等于 NORMAL_PROMO 的 type 时，应返回 true
     */
    @Test
    public void testIsReductionPromoNormal() throws Throwable {
        // arrange
        int promoType = PromoTypeEnum.NORMAL_PROMO.getType();
        // act
        boolean result = invokeIsReductionPromo(promoType);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 isReductionPromo 方法，当 promoType 等于 IDLE_PROMO 的 type 时，应返回 true
     */
    @Test
    public void testIsReductionPromoIdle() throws Throwable {
        // arrange
        int promoType = PromoTypeEnum.IDLE_PROMO.getType();
        // act
        boolean result = invokeIsReductionPromo(promoType);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 isReductionPromo 方法，当 promoType 等于 THRESHOLD 的 type 时，应返回 true
     */
    @Test
    public void testIsReductionPromoThreshold() throws Throwable {
        // arrange
        int promoType = PromoTypeEnum.THRESHOLD.getType();
        // act
        boolean result = invokeIsReductionPromo(promoType);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 isReductionPromo 方法，当 promoType 不等于上述任何一个值时，应返回 false
     */
    @Test
    public void testIsReductionPromoOther() throws Throwable {
        // arrange
        int promoType = PromoTypeEnum.DISCOUNT_CARD.getType();
        // act
        boolean result = invokeIsReductionPromo(promoType);
        // assert
        assertFalse(result);
    }

    @Test
    public void testGetDealBestPromoDetailWhenUsedPromosIsEmpty() throws Throwable {
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        priceDisplayDTO.setUsedPromos(Collections.emptyList());
        DealBestPromoDetailDTO result = handler.getDealBestPromoDetail(priceDisplayDTO);
        assertNull(result);
    }

    @Test
    public void testGetDealBestPromoDetailWhenUsedPromosIsNotEmptyButAllPromoDTOsIdentityOrAmountIsNull() throws Throwable {
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(null);
        promoDTO.setAmount(null);
        priceDisplayDTO.setUsedPromos(Arrays.asList(promoDTO));
        DealBestPromoDetailDTO result = handler.getDealBestPromoDetail(priceDisplayDTO);
        assertNull(result);
    }

    @Test
    public void testGetDealBestPromoDetailWhenUsedPromosIsNotEmptyAndSomePromoDTOsIdentityAndAmountIsNotNullButSourceTypeIsNotTwo() throws Throwable {
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(1);
        promoDTO.setIdentity(identity);
        promoDTO.setAmount(BigDecimal.ONE);
        priceDisplayDTO.setUsedPromos(Arrays.asList(promoDTO));
        DealBestPromoDetailDTO result = handler.getDealBestPromoDetail(priceDisplayDTO);
        assertNull(result);
    }

    @Test
    public void testGetDealBestPromoDetailWhenUsedPromosIsNotEmptyAndSomePromoDTOsIdentityAndAmountIsNotNullAndSourceTypeIsTwoButIsReductionPromoReturnsFalse() throws Throwable {
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        PromoDTO promoDTO = new PromoDTO();
        // Assuming 1 is a reduction promo type
        PromoIdentity identity = new PromoIdentity(1);
        // Source type 2 is not a reduction promo
        identity.setSourceType(2);
        promoDTO.setIdentity(identity);
        promoDTO.setAmount(BigDecimal.ONE);
        priceDisplayDTO.setUsedPromos(Arrays.asList(promoDTO));
        DealBestPromoDetailDTO result = handler.getDealBestPromoDetail(priceDisplayDTO);
        // Adjusted expectation based on the setup
        assertNotNull(result);
    }

    @Test
    public void testGetDealBestPromoDetailWhenUsedPromosIsNotEmptyAndSomePromoDTOsIdentityAndAmountIsNotNullAndSourceTypeIsTwoAndIsReductionPromoReturnsTrueButAmountEqualsZero() throws Throwable {
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(1);
        identity.setSourceType(2);
        promoDTO.setIdentity(identity);
        promoDTO.setAmount(BigDecimal.ZERO);
        priceDisplayDTO.setUsedPromos(Arrays.asList(promoDTO));
        DealBestPromoDetailDTO result = handler.getDealBestPromoDetail(priceDisplayDTO);
        assertNull(result);
    }

    @Test
    public void testGetDealBestPromoDetailWhenUsedPromosIsNotEmptyAndSomePromoDTOsIdentityAndAmountIsNotNullAndSourceTypeIsTwoAndIsReductionPromoReturnsTrueAndAmountNotEqualsZeroButPromoDescIsNull() throws Throwable {
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(1);
        identity.setSourceType(2);
        promoDTO.setIdentity(identity);
        promoDTO.setAmount(BigDecimal.ONE);
        promoDTO.setExtendDesc(null);
        priceDisplayDTO.setUsedPromos(Arrays.asList(promoDTO));
        DealBestPromoDetailDTO result = handler.getDealBestPromoDetail(priceDisplayDTO);
        assertNotNull(result);
        assertEquals("1", result.getDealBestPromoDetail().getPromoAmount());
    }

    @Test
    public void testGetDealBestPromoDetailWhenUsedPromosIsNotEmptyAndSomePromoDTOsIdentityAndAmountIsNotNullAndSourceTypeIsTwoAndIsReductionPromoReturnsTrueAndAmountNotEqualsZeroAndPromoDescIsNotNull() throws Throwable {
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(1);
        identity.setSourceType(2);
        promoDTO.setIdentity(identity);
        promoDTO.setAmount(BigDecimal.ONE);
        promoDTO.setExtendDesc("desc");
        priceDisplayDTO.setUsedPromos(Arrays.asList(promoDTO));
        DealBestPromoDetailDTO result = handler.getDealBestPromoDetail(priceDisplayDTO);
        assertNotNull(result);
        assertEquals("1", result.getDealBestPromoDetail().getPromoAmount());
        assertEquals("desc", result.getDealBestPromoDetail().getPromoDesc());
    }
}
