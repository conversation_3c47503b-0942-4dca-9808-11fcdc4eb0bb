package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DealAttrCons;
import com.dianping.mobile.mapi.dztgdetail.common.enums.KeyEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealStyleProcessorTest {

    @InjectMocks
    private DealStyleProcessor dealStyleProcessor;

    @Mock
    private DealCtx dealCtx;

    private String invokePrivateGenerateChildrenKey(DealCtx dealCtx) throws Exception {
        Method method = DealStyleProcessor.class.getDeclaredMethod("generateChildrenKey", DealCtx.class);
        method.setAccessible(true);
        return (String) method.invoke(dealStyleProcessor, dealCtx);
    }

    /**
     * Test when attrs is null
     */
    @Test
    public void testGenerateChildrenKey_WhenAttrsIsNull() throws Throwable {
        // arrange
        when(dealCtx.getAttrs()).thenReturn(null);
        // act
        String result = invokePrivateGenerateChildrenKey(dealCtx);
        // assert
        assertEquals(KeyEnum.DEAFAULT.getChannel(), result);
    }

    /**
     * Test when attrs is empty list
     */
    @Test
    public void testGenerateChildrenKey_WhenAttrsIsEmpty() throws Throwable {
        // arrange
        when(dealCtx.getAttrs()).thenReturn(new ArrayList<>());
        // act
        String result = invokePrivateGenerateChildrenKey(dealCtx);
        // assert
        assertEquals(KeyEnum.DEAFAULT.getChannel(), result);
    }

    /**
     * Test when attrs contains CHILDREN_EDU
     */
    @Test
    public void testGenerateChildrenKey_WhenContainsChildrenEdu() throws Throwable {
        // arrange
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attr = new AttributeDTO();
        attr.setName(DealAttrCons.CATEGORY);
        attr.setValue(Collections.singletonList(DealAttrCons.CHILDREN_EDU));
        attrs.add(attr);
        when(dealCtx.getAttrs()).thenReturn(attrs);
        // act
        String result = invokePrivateGenerateChildrenKey(dealCtx);
        // assert
        assertEquals(KeyEnum.CHILDREN_PHOTOEDUTG.getChannel(), result);
    }

    /**
     * Test when attrs contains CHILDREN_PHOTO
     */
    @Test
    public void testGenerateChildrenKey_WhenContainsChildrenPhoto() throws Throwable {
        // arrange
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attr = new AttributeDTO();
        attr.setName(DealAttrCons.CATEGORY);
        attr.setValue(Collections.singletonList(DealAttrCons.CHILDREN_PHOTO));
        attrs.add(attr);
        when(dealCtx.getAttrs()).thenReturn(attrs);
        // act
        String result = invokePrivateGenerateChildrenKey(dealCtx);
        // assert
        assertEquals(KeyEnum.CHILDREN_PHOTOEDUTG.getChannel(), result);
    }

    /**
     * Test when attrs contains CHILDREN_PREGNANT_PHOTO
     */
    @Test
    public void testGenerateChildrenKey_WhenContainsChildrenPregnantPhoto() throws Throwable {
        // arrange
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attr = new AttributeDTO();
        attr.setName(DealAttrCons.CATEGORY);
        attr.setValue(Collections.singletonList(DealAttrCons.CHILDREN_PREGNANT_PHOTO));
        attrs.add(attr);
        when(dealCtx.getAttrs()).thenReturn(attrs);
        // act
        String result = invokePrivateGenerateChildrenKey(dealCtx);
        // assert
        assertEquals(KeyEnum.CHILDREN_PHOTOEDUTG.getChannel(), result);
    }

    /**
     * Test when attrs contains non-children category
     */
    @Test
    public void testGenerateChildrenKey_WhenContainsOtherCategory() throws Throwable {
        // arrange
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attr = new AttributeDTO();
        attr.setName(DealAttrCons.CATEGORY);
        attr.setValue(Collections.singletonList("OTHER_CATEGORY"));
        attrs.add(attr);
        when(dealCtx.getAttrs()).thenReturn(attrs);
        // act
        String result = invokePrivateGenerateChildrenKey(dealCtx);
        // assert
        assertEquals(KeyEnum.DEAFAULT.getChannel(), result);
    }

    /**
     * Test when attrs contains non-category attribute
     */
    @Test
    public void testGenerateChildrenKey_WhenContainsNonCategoryAttr() throws Throwable {
        // arrange
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attr = new AttributeDTO();
        attr.setName("OTHER_ATTR");
        attr.setValue(Collections.singletonList("VALUE"));
        attrs.add(attr);
        when(dealCtx.getAttrs()).thenReturn(attrs);
        // act
        String result = invokePrivateGenerateChildrenKey(dealCtx);
        // assert
        assertEquals(KeyEnum.DEAFAULT.getChannel(), result);
    }

    /**
     * Test when exception occurs
     */
    @Test
    public void testGenerateChildrenKey_WhenExceptionOccurs() throws Throwable {
        // arrange
        when(dealCtx.getAttrs()).thenThrow(new RuntimeException("Test exception"));
        // act
        String result = invokePrivateGenerateChildrenKey(dealCtx);
        // assert
        assertEquals(KeyEnum.DEAFAULT.getChannel(), result);
    }
}
