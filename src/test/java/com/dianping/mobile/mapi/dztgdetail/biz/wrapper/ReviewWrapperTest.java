package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.dianping.act.report.read.enums.ReportBizEnum;
import com.dianping.act.report.read.service.BrowseDataReadService;
import com.dianping.mobile.mapi.dztgdetail.common.constants.ReviewFilterType;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.ShopReviewCtx;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.dianping.reviewremote.remote.ReviewServiceV2;
import com.dianping.ugc.dto.ExtParamV2;
import com.dianping.ugc.proxyService.remote.dp.DpReviewUserInfoService;
import com.dianping.ugc.proxyService.remote.dto.AnonymousUserInfo;
import com.dianping.ugc.proxyService.remote.dto.UserRequestPara;
import com.dianping.ugc.review.remote.dto.FilterParam;
import com.dianping.ugc.review.remote.dto.FilterParamV2;
import com.dianping.ugc.review.remote.mt.MTReviewQueryService;
import com.dianping.ugc.review.remote.mt.MTReviewQueryServiceV2;
import com.dianping.vipremote.service.VIPUserService;
import com.dp.arts.client.SearchService;
import com.dp.arts.client.request.Request;
import com.dp.arts.client.response.Response;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ReviewWrapperTest {

    @InjectMocks
    private ReviewWrapper reviewWrapper;

    @Mock
    private ReviewServiceV2 reviewServiceFuture;

    @Mock
    private BrowseDataReadService browseDataReadServiceFuture;

    @Mock
    private DpReviewUserInfoService dpReviewUserInfoServiceFuture;

    @Mock
    private SearchService shopReviewSearchServiceFuture;

    @Mock
    private Future mockFuture;

    @Mock
    private VIPUserService vipUserServiceFuture;

    @Mock
    private MTReviewQueryService mtReviewQueryServiceFuture;

    @Mock
    private MTReviewQueryServiceV2 mtReviewQueryServiceFutureV2;

    @Mock
    private FutureFactory futureFactory;

    public ReviewWrapperTest() {
        MockitoAnnotations.initMocks(this);
    }

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private ShopReviewCtx createShopReviewCtx() {
        // Assuming EnvCtx has a no-argument constructor
        EnvCtx envCtx = new EnvCtx();
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx(envCtx);
        shopReviewCtx.setMtId(1);
        return shopReviewCtx;
    }

    /**
     * 测试getShopReviewFuture方法，当token为空时的场景
     */
    @Test
    public void testGetShopReviewFutureWithEmptyToken() throws Exception {
        ShopReviewCtx shopReviewCtx = mock(ShopReviewCtx.class);
        when(shopReviewCtx.getToken()).thenReturn("");
        when(shopReviewCtx.getDpLongShopId()).thenReturn(123L);
        when(reviewServiceFuture.paginateShopReviewsV3(anyLong(), anyInt(), anyInt(), any(ExtParamV2.class))).thenReturn(null);
        // act
        reviewWrapper.getShopReviewFuture(shopReviewCtx, 10);
        // assert
        verify(reviewServiceFuture, times(1)).paginateShopReviewsV3(eq(123L), eq(1), eq(10), any(ExtParamV2.class));
    }

    /**
     * 测试getShopReviewFuture方法，当token不为空时的场景
     */
    @Test
    public void testGetShopReviewFutureWithNonEmptyToken() throws Exception {
        ShopReviewCtx shopReviewCtx = mock(ShopReviewCtx.class);
        when(shopReviewCtx.getToken()).thenReturn("token");
        when(shopReviewCtx.getDpLongShopId()).thenReturn(123L);
        when(reviewServiceFuture.paginateShopReviewsV3(anyLong(), anyInt(), anyInt(), any(ExtParamV2.class))).thenReturn(null);
        // act
        reviewWrapper.getShopReviewFuture(shopReviewCtx, 10);
        // assert
        verify(reviewServiceFuture, times(1)).paginateShopReviewsV3(eq(123L), eq(1), eq(10), any(ExtParamV2.class));
    }

    /**
     * 测试getShopReviewFuture方法，当发生异常时的场景
     */
    @Test
    public void testGetShopReviewFutureWhenExceptionOccurs() throws Exception {
        ShopReviewCtx shopReviewCtx = mock(ShopReviewCtx.class);
        when(shopReviewCtx.getToken()).thenReturn("token");
        when(shopReviewCtx.getDpLongShopId()).thenReturn(123L);
        when(reviewServiceFuture.paginateShopReviewsV3(anyLong(), anyInt(), anyInt(), any(ExtParamV2.class))).thenThrow(new RuntimeException());
        // act
        reviewWrapper.getShopReviewFuture(shopReviewCtx, 10);
        // assert
        verify(reviewServiceFuture, times(1)).paginateShopReviewsV3(eq(123L), eq(1), eq(10), any(ExtParamV2.class));
    }

    /**
     * 测试getReviewsBrowseCountFutureV2方法，当reviewIds为null时的场景
     */
    @Test
    public void testGetReviewsBrowseCountFutureV2_ReviewIdsIsNull() throws Throwable {
        // arrange
        List<Long> reviewIds = null;
        // act
        Future result = reviewWrapper.getReviewsBrowseCountFutureV2(reviewIds);
        // assert
        verify(browseDataReadServiceFuture, times(1)).batchGetBrowseDataNew(ReportBizEnum.RP_REVIEW.getValue(), Collections.emptyList());
        assertNull("返回结果不应为null", result);
    }

    /**
     * 测试getReviewsBrowseCountFutureV2方法，当reviewIds为空列表时的场景
     */
    @Test
    public void testGetReviewsBrowseCountFutureV2_ReviewIdsIsEmpty() throws Throwable {
        // arrange
        List<Long> reviewIds = Collections.emptyList();
        // act
        Future result = reviewWrapper.getReviewsBrowseCountFutureV2(reviewIds);
        // assert
        verify(browseDataReadServiceFuture, times(1)).batchGetBrowseDataNew(ReportBizEnum.RP_REVIEW.getValue(), Collections.emptyList());
        assertNull("返回结果不应为null", result);
    }

    /**
     * 测试getReviewsBrowseCountFutureV2方法，当reviewIds包含元素时的场景
     */
    @Test
    public void testGetReviewsBrowseCountFutureV2_ReviewIdsIsNotEmpty() throws Throwable {
        // arrange
        List<Long> reviewIds = Arrays.asList(1L, 2L);
        // act
        Future result = reviewWrapper.getReviewsBrowseCountFutureV2(reviewIds);
        // assert
        // verify(browseDataReadServiceFuture, times(1)).batchGetBrowseDataNew(ReportBizEnum.RP_REVIEW.getValue(), reviewIds);
        assertNull("返回应为null", result);
    }

    /**
     * 测试getReviewsBrowseCountFutureV2方法，当方法抛出异常时的场景
     */
    @Test
    public void testGetReviewsBrowseCountFutureV2_ThrowsException() throws Throwable {
        // arrange
        List<Long> reviewIds = Arrays.asList(1L, 2L);
        doThrow(new RuntimeException()).when(browseDataReadServiceFuture).batchGetBrowseDataNew(anyInt(), anyList());
        // act
        Future future = reviewWrapper.getReviewsBrowseCountFutureV2(reviewIds);
        // assert
        assertNull(future);
        // Expected exception
    }

    @Test
    public void testGetAnonymousUserInfoFutureWithEmptyInput() throws Throwable {
        List<Long> anonymousReviewIds = Arrays.asList();
        Future<Map<Long, AnonymousUserInfo>> result = reviewWrapper.getAnonymousUserInfoFuture(anonymousReviewIds);
        assertNull(result);
    }

    @Test
    public void testGetAnonymousUserInfoFutureWithException() throws Throwable {
        doThrow(new RuntimeException()).when(dpReviewUserInfoServiceFuture).findAnonymousUserInfoV3(anyList(), any(UserRequestPara.class));
        List<Long> anonymousReviewIds = Arrays.asList(1L, 2L, 3L);
        Future<Map<Long, AnonymousUserInfo>> result = reviewWrapper.getAnonymousUserInfoFuture(anonymousReviewIds);
        assertNull(result);
        verify(dpReviewUserInfoServiceFuture, times(1)).findAnonymousUserInfoV3(anyList(), any(UserRequestPara.class));
    }

    /**
     * Test method getShopReviewFuture with null ShopReviewCtx.
     * Expecting the method to handle null input gracefully and return null.
     */
    @Test
    public void testGetShopReviewFutureWithNullShopReviewCtx() throws Throwable {
        Future result = reviewWrapper.getShopReviewFuture(null, 10);
        assertNull("Expected null result when ShopReviewCtx is null", result);
    }

    /**
     * Test method getShopReviewFuture with zero pageSize.
     * Expecting the method to handle invalid pageSize input gracefully and return null.
     */
    @Test
    public void testGetShopReviewFutureWithZeroPageSize() throws Throwable {
        ShopReviewCtx shopReviewCtx = mock(ShopReviewCtx.class);
        when(shopReviewCtx.getToken()).thenReturn("token");
        when(shopReviewCtx.getDpLongShopId()).thenReturn(123L);
        Future result = reviewWrapper.getShopReviewFuture(shopReviewCtx, 0);
        assertNull("Expected null result when pageSize is zero", result);
    }

    /**
     * Test method getShopReviewFuture with an exception thrown by the reviewServiceFuture.
     * Expecting the method to propagate the exception.
     */
    @Test(expected = Exception.class)
    public void testGetShopReviewFutureWithException() throws Throwable {
        // Setup mock to throw an exception correctly
        doThrow(new Exception()).when(reviewServiceFuture).paginateShopReviewsV3(anyLong(), anyInt(), anyInt(), any());
        ShopReviewCtx shopReviewCtx = mock(ShopReviewCtx.class);
        when(shopReviewCtx.getToken()).thenReturn("token");
        when(shopReviewCtx.getDpLongShopId()).thenReturn(123L);
        reviewWrapper.getShopReviewFuture(shopReviewCtx, 10);
    }

    /**
     * 测试getReviewsBrowseCountFutureV2方法，当执行过程中发生异常时的场景
     */
    @Test
    public void testGetReviewsBrowseCountFutureV2_ExceptionOccurred() throws Throwable {
        // arrange
        List<Long> reviewIds = Arrays.asList(1L, 2L, 3L);
        doThrow(new RuntimeException()).when(browseDataReadServiceFuture).batchGetBrowseDataNew(anyInt(), anyList());
        // act
        Future result = reviewWrapper.getReviewsBrowseCountFutureV2(reviewIds);
        // assert
        verify(browseDataReadServiceFuture, times(1)).batchGetBrowseDataNew(anyInt(), anyList());
        assertNull("返回结果应为null", result);
    }

    @Test
    public void testFindGoodReviewFutureWithInvalidInput() throws Throwable {
        Future future = reviewWrapper.findGoodReviewFuture(null, 1L);
        assertNull(future);
        future = reviewWrapper.findGoodReviewFuture(Arrays.asList("review1", "review2"), 0L);
        assertNull(future);
    }

    @Test
    public void testFindGoodReviewFutureWithException() throws Throwable {
        doThrow(new RuntimeException()).when(shopReviewSearchServiceFuture).search(any(Request.class));
        Future future = reviewWrapper.findGoodReviewFuture(Arrays.asList("review1", "review2"), 1L);
        assertNull(future);
    }

    /**
     * Tests the behavior of getMTAllShopReviewTagFuture method under exceptional conditions.
     */
    @Test(expected = Exception.class)
    public void testGetMTAllShopReviewTagFutureException() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx(envCtx);
        doThrow(new Exception()).when(shopReviewSearchServiceFuture).search(any(Request.class));
        // act
        reviewWrapper.getMTAllShopReviewTagFuture(shopReviewCtx);
        // assert
        // Exception is expected
    }

    /**
     * 测试 getUserVipInfoFuture 方法，当 vipUserServiceFuture.findVipUserInfo 方法抛出异常时，应返回 null
     */
    @Test
    public void testGetUserVipInfoFutureException() throws Throwable {
        // arrange
        Set<Long> userIdLSet = new HashSet<>();
        userIdLSet.add(1L);
        doThrow(new RuntimeException()).when(vipUserServiceFuture).findVipUserInfo(anyList(), anyInt());
        // act
        Future result = reviewWrapper.getUserVipInfoFuture(userIdLSet);
        // assert
        assertNull(result);
    }

    /**
     * 测试 dpShopId 小于等于0 的情况
     */
    @Test
    public void testGetMtShopReviewFutureDpShopIdLessThanOrEqualToZero() throws Throwable {
        // arrange
        int dpShopId = 0;
        int start = 0;
        int limit = 10;
        int filterType = 1;
        // act
        Future result = reviewWrapper.getMtShopReviewFuture(dpShopId, start, limit, filterType);
        // assert
        assertNull(result);
    }

    /**
     * 测试 filterType 导致的不同 FilterParam 创建
     */
    @Test
    public void testGetMtShopReviewFutureDifferentFilterParamCreation() throws Throwable {
        // arrange
        int dpShopId = 1;
        int start = 0;
        int limit = 10;
        int filterType1 = 1;
        int filterType2 = 2;
        // act
        reviewWrapper.getMtShopReviewFuture(dpShopId, start, limit, filterType1);
        reviewWrapper.getMtShopReviewFuture(dpShopId, start, limit, filterType2);
        // assert
        verify(mtReviewQueryServiceFuture, times(2)).getReviewByShop(anyInt(), any(FilterParam.class), anyInt(), anyInt(), any());
    }

    /**
     * 测试 mtReviewQueryServiceFuture.getReviewByShop 方法抛出异常的情况
     */
    @Test
    public void testGetMtShopReviewFutureExceptionThrown() throws Throwable {
        // arrange
        int dpShopId = 1;
        int start = 0;
        int limit = 10;
        int filterType = 1;
        doThrow(new RuntimeException()).when(mtReviewQueryServiceFuture).getReviewByShop(anyInt(), any(FilterParam.class), anyInt(), anyInt(), any());
        // act
        Future result = reviewWrapper.getMtShopReviewFuture(dpShopId, start, limit, filterType);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetMtShopReviewFutureV2DpShopIdLessThanZero() throws Throwable {
        long dpShopId = -1L;
        int start = 0;
        int limit = 10;
        int filterType = ReviewFilterType.RANK_LASTTIME;
        Future result = reviewWrapper.getMtShopReviewFutureV2(dpShopId, start, limit, filterType);
        assertNull(result);
    }

    @Test
    public void testGetMtShopReviewFutureV2FilterTypeIsRankLastTime() throws Throwable {
        long dpShopId = 1L;
        int start = 0;
        int limit = 10;
        int filterType = ReviewFilterType.RANK_LASTTIME;
        reviewWrapper.getMtShopReviewFutureV2(dpShopId, start, limit, filterType);
        verify(mtReviewQueryServiceFutureV2, times(1)).getReviewByShop(eq(dpShopId), any(FilterParamV2.class), eq(start), eq(limit), eq(null));
    }

    @Test
    public void testGetMtShopReviewFutureV2FilterTypeIsNotRankLastTime() throws Throwable {
        long dpShopId = 1L;
        int start = 0;
        int limit = 10;
        int filterType = ReviewFilterType.RANK_PICTURE;
        reviewWrapper.getMtShopReviewFutureV2(dpShopId, start, limit, filterType);
        verify(mtReviewQueryServiceFutureV2, times(1)).getReviewByShop(eq(dpShopId), any(FilterParamV2.class), eq(start), eq(limit), eq(null));
    }

    @Test
    public void testGetMtShopReviewFutureV2FilterTypeIsRankPicture() throws Throwable {
        long dpShopId = 1L;
        int start = 0;
        int limit = 10;
        int filterType = ReviewFilterType.RANK_PICTURE;
        reviewWrapper.getMtShopReviewFutureV2(dpShopId, start, limit, filterType);
        verify(mtReviewQueryServiceFutureV2, times(1)).getReviewByShop(eq(dpShopId), any(FilterParamV2.class), eq(start), eq(limit), eq(null));
    }

    @Test
    public void testGetMtShopReviewFutureV2FilterTypeIsNotRankPicture() throws Throwable {
        long dpShopId = 1L;
        int start = 0;
        int limit = 10;
        int filterType = ReviewFilterType.RANK_BAD;
        reviewWrapper.getMtShopReviewFutureV2(dpShopId, start, limit, filterType);
        verify(mtReviewQueryServiceFutureV2, times(1)).getReviewByShop(eq(dpShopId), any(FilterParamV2.class), eq(start), eq(limit), eq(null));
    }

    @Test
    public void testGetMtShopReviewFutureV2FilterTypeIsRankBad() throws Throwable {
        long dpShopId = 1L;
        int start = 0;
        int limit = 10;
        int filterType = ReviewFilterType.RANK_BAD;
        reviewWrapper.getMtShopReviewFutureV2(dpShopId, start, limit, filterType);
        verify(mtReviewQueryServiceFutureV2, times(1)).getReviewByShop(eq(dpShopId), any(FilterParamV2.class), eq(start), eq(limit), eq(null));
    }

    @Test
    public void testGetMtShopReviewFutureV2FilterTypeIsNotRankBad() throws Throwable {
        long dpShopId = 1L;
        int start = 0;
        int limit = 10;
        int filterType = ReviewFilterType.RANK_LASTTIME;
        reviewWrapper.getMtShopReviewFutureV2(dpShopId, start, limit, filterType);
        verify(mtReviewQueryServiceFutureV2, times(1)).getReviewByShop(eq(dpShopId), any(FilterParamV2.class), eq(start), eq(limit), eq(null));
    }

    /**
     * Tests the getMtReviewByDeal method under exception conditions.
     */
    @Test
    public void testGetMtReviewByDealException() throws Throwable {
        ShopReviewCtx shopReviewCtx = createShopReviewCtx();
        int displayReviewCount = 10;
        when(mtReviewQueryServiceFuture.getReviewByDeal(anyInt(), any(), anyInt(), anyInt(), any())).thenThrow(new RuntimeException("Test exception"));
        Future<?> result = reviewWrapper.getMtReviewByDeal(shopReviewCtx, displayReviewCount);
        verify(mtReviewQueryServiceFuture, times(1)).getReviewByDeal(eq(shopReviewCtx.getMtId()), any(), eq(0), eq(displayReviewCount), any());
        assertNull("Result should be null on exception", result);
    }
}
