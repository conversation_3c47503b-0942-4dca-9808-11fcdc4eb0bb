package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

// Using MockitoJUnitRunner as specified, though no mocks are utilized in the current test cases.
@RunWith(MockitoJUnitRunner.class)
public class DealAttrHelper_AvailableAllTest {

    private static Method isRepairPrepayDealMethod;

    @BeforeClass
    public static void setUpClass() throws Exception {
        // Use reflection to access the private method
        isRepairPrepayDealMethod = DealAttrHelper.class.getDeclaredMethod("isRepairPrepayDeal", AttrDTO.class, String.class);
        isRepairPrepayDealMethod.setAccessible(true);
    }

    // Test when attrs is null
    @Test
    public void testAvailableAllWhenAttrsIsNull() throws Throwable {
        List<AttributeDTO> attrs = null;
        boolean result = DealAttrHelper.availableAll(attrs);
        assertFalse(result);
    }

    // Test when there is no TIMES_AVAILABLE_ALL attribute
    @Test
    public void testAvailableAllWhenNoTimesAvailableAll() throws Throwable {
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attr = new AttributeDTO();
        attr.setName("other");
        attr.setValue(new ArrayList<>());
        attrs.add(attr);
        boolean result = DealAttrHelper.availableAll(attrs);
        assertFalse(result);
    }

    // Test when TIMES_AVAILABLE_ALL attribute's value is null
    @Test
    public void testAvailableAllWhenTimesAvailableAllValueIsNull() throws Throwable {
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attr = new AttributeDTO();
        attr.setName("TIMES_AVAILABLE_ALL");
        attr.setValue(null);
        attrs.add(attr);
        boolean result = DealAttrHelper.availableAll(attrs);
        assertFalse(result);
    }

    // Test when TIMES_AVAILABLE_ALL attribute's value is not "是"
    @Test
    public void testAvailableAllWhenTimesAvailableAllValueIsNotYes() throws Throwable {
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attr = new AttributeDTO();
        attr.setName("TIMES_AVAILABLE_ALL");
        attr.setValue(Arrays.asList("否"));
        attrs.add(attr);
        boolean result = DealAttrHelper.availableAll(attrs);
        assertFalse(result);
    }

    @Test
    public void testIsRepairPrepayDealAttrDtoIsNull() throws Throwable {
        assertFalse((boolean) isRepairPrepayDealMethod.invoke(null, null, "prepayAttrName"));
    }

    @Test
    public void testIsRepairPrepayDealAttrDtoNameIsNull() throws Throwable {
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(null);
        assertFalse((boolean) isRepairPrepayDealMethod.invoke(null, attrDTO, "prepayAttrName"));
    }

    @Test
    public void testIsRepairPrepayDealAttrDtoValueIsNull() throws Throwable {
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("prepayAttrName");
        attrDTO.setValue(null);
        assertFalse((boolean) isRepairPrepayDealMethod.invoke(null, attrDTO, "prepayAttrName"));
    }

    @Test
    public void testIsRepairPrepayDealAttrDtoNameNotEqualPrepayAttrName() throws Throwable {
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("notPrepayAttrName");
        attrDTO.setValue(Arrays.asList("value"));
        assertFalse((boolean) isRepairPrepayDealMethod.invoke(null, attrDTO, "prepayAttrName"));
    }

    @Test
    public void testIsRepairPrepayDealAttrDtoNameEqualPrepayAttrNameButValueNotInRepairPrepayAttrValues() throws Throwable {
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("prepayAttrName");
        attrDTO.setValue(Arrays.asList("notInRepairPrepayAttrValues"));
        assertFalse((boolean) isRepairPrepayDealMethod.invoke(null, attrDTO, "prepayAttrName"));
    }

    @Test
    public void testIsRepairPrepayDealAttrDtoNameEqualPrepayAttrNameAndValueInRepairPrepayAttrValues() throws Throwable {
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("prepayAttrName");
        attrDTO.setValue(Arrays.asList("上门费"));
        assertTrue((boolean) isRepairPrepayDealMethod.invoke(null, attrDTO, "prepayAttrName"));
    }
}
