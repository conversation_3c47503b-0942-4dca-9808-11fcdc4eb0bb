package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class EntryExaminerHandlerGetTurnaroundTimeTest {

    private EntryExaminerHandler entryExaminerHandler = new EntryExaminerHandler();

    private double invokePrivateMethod(List<AttrDTO> attrs) throws Exception {
        Method method = EntryExaminerHandler.class.getDeclaredMethod("getTurnaroundTime", List.class);
        method.setAccessible(true);
        return (double) method.invoke(entryExaminerHandler, attrs);
    }

    @Test
    public void testGetTurnaroundTimeWhenAttrsIsNull() throws Throwable {
        double result = invokePrivateMethod(null);
        assertEquals(-1, result, 0.001);
    }

    @Test
    public void testGetTurnaroundTimeWhenAttrNotFound() throws Throwable {
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("other");
        double result = invokePrivateMethod(Collections.singletonList(attrDTO));
        assertEquals(-1, result, 0.001);
    }

    @Test
    public void testGetTurnaroundTimeWhenAttrValueIsNull() throws Throwable {
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("physical_examination_get_result_time");
        double result = invokePrivateMethod(Collections.singletonList(attrDTO));
        assertEquals(-1, result, 0.001);
    }

    @Test
    public void testGetTurnaroundTimeWhenFirstValueIsNull() throws Throwable {
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("physical_examination_get_result_time");
        attrDTO.setValue(Arrays.asList(null, "1个月"));
        double result = invokePrivateMethod(Collections.singletonList(attrDTO));
        assertEquals(-1, result, 0.001);
    }

    @Test
    public void testGetTurnaroundTimeWhenUnitIsMonth() throws Throwable {
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("physical_examination_get_result_time");
        attrDTO.setValue(Arrays.asList("结束后1个月"));
        double result = invokePrivateMethod(Collections.singletonList(attrDTO));
        assertEquals(30, result, 0.001);
    }

    @Test
    public void testGetTurnaroundTimeWhenUnitIsWeek() throws Throwable {
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("physical_examination_get_result_time");
        attrDTO.setValue(Arrays.asList("结束后2周"));
        double result = invokePrivateMethod(Collections.singletonList(attrDTO));
        assertEquals(14, result, 0.001);
    }

    @Test
    public void testGetTurnaroundTimeWhenUnitIsDay() throws Throwable {
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("physical_examination_get_result_time");
        attrDTO.setValue(Arrays.asList("结束后3个工作日"));
        double result = invokePrivateMethod(Collections.singletonList(attrDTO));
        assertEquals(3, result, 0.001);
    }
}
