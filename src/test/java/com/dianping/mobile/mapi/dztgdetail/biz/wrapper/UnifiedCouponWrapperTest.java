package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.tgc.process.entity.PResponse;
import com.dianping.unified.coupon.manage.api.UnifiedCouponListService;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import com.dianping.unified.coupon.manage.api.response.UnifiedCouponManageResponse;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedCouponWrapperTest {

    @InjectMocks
    private UnifiedCouponWrapper unifiedCouponWrapper;

    @Mock
    private UnifiedCouponListService unifiedCouponListService;

    @Rule
    public ExpectedException expectedException = ExpectedException.none();

    @Mock
    private Future future;

    @Test
    public void testQueryIssuedCouponListWhenCouponGroupIdIsNull() throws Throwable {
        EnvCtx envCtx = mock(EnvCtx.class);
        String couponGroupId = null;
        List<UnifiedCouponDTO> result = unifiedCouponWrapper.queryIssuedCouponList(envCtx, couponGroupId);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryIssuedCouponListWhenCouponGroupIdIsNotNull() throws Throwable {
        EnvCtx envCtx = mock(EnvCtx.class);
        String couponGroupId = "testCouponGroupId";
        UnifiedCouponGroupDTO couponGroupDTO = new UnifiedCouponGroupDTO();
        UnifiedCouponDTO couponDTO = new UnifiedCouponDTO(1L, couponGroupDTO);
        List<UnifiedCouponDTO> expectedList = Collections.singletonList(couponDTO);
        UnifiedCouponManageResponse<List<UnifiedCouponDTO>> expectedResponse = new UnifiedCouponManageResponse<>();
        expectedResponse.setSuccess(true);
        expectedResponse.setResult(expectedList);
        when(unifiedCouponListService.queryCouponByCouponGroupId(any())).thenReturn(expectedResponse);
        List<UnifiedCouponDTO> result = unifiedCouponWrapper.queryIssuedCouponList(envCtx, couponGroupId);
        assertEquals(expectedList, result);
    }

    /**
     * 测试 future 为 null 的情况
     */
    @Test
    public void testGetMerchantCouponIssueFutureIsNull() throws Throwable {
        // arrange
        Future future = null;
        // act
        String result = unifiedCouponWrapper.getMerchantCouponIssue(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 future 非 null，但 getFutureResult(future) 返回 null 的情况
     */
    @Test
    public void testGetMerchantCouponIssueFutureResultIsNull() throws Throwable {
        // arrange
        when(unifiedCouponWrapper.getFutureResult(future)).thenReturn(null);
        // act
        String result = unifiedCouponWrapper.getMerchantCouponIssue(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 future 非 null，getFutureResult(future) 返回的 PResponse 对象为 null 的情况
     */
    @Test
    public void testGetMerchantCouponIssuePResponseIsNull() throws Throwable {
        // arrange
        when(unifiedCouponWrapper.getFutureResult(future)).thenReturn(null);
        // act
        String result = unifiedCouponWrapper.getMerchantCouponIssue(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 future 非 null，getFutureResult(future) 返回的 PResponse 对象非 null，但 isSuccess() 返回 false 的情况
     */
    @Test
    public void testGetMerchantCouponIssueIsNotSuccess() throws Throwable {
        // arrange
        PResponse<String> pResponse = mock(PResponse.class);
        when(pResponse.isSuccess()).thenReturn(false);
        when(unifiedCouponWrapper.getFutureResult(future)).thenReturn(pResponse);
        // act
        String result = unifiedCouponWrapper.getMerchantCouponIssue(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 future 非 null，getFutureResult(future) 返回的 PResponse 对象非 null，isSuccess() 返回 true 的情况
     */
    @Test
    public void testGetMerchantCouponIssueIsSuccess() throws Throwable {
        // arrange
        PResponse<String> pResponse = mock(PResponse.class);
        when(pResponse.isSuccess()).thenReturn(true);
        when(pResponse.getResult()).thenReturn("test");
        when(unifiedCouponWrapper.getFutureResult(future)).thenReturn(pResponse);
        // act
        String result = unifiedCouponWrapper.getMerchantCouponIssue(future);
        // assert
        assertEquals("test", result);
    }
}
