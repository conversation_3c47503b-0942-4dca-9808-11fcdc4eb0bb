package com.dianping.mobile.mapi.dztgdetail.biz.processor;

import com.dianping.deal.stock.dto.ProductStock;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.StockSalesProcessor;
import com.sankuai.general.product.query.center.client.dto.StockDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealBasicDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mockStatic;

/**
 * @Author: <EMAIL>
 * @Date: 2024/1/17
 */
@RunWith(MockitoJUnitRunner.class)
public class StockSalesProcessorTest {

    @InjectMocks
    private StockSalesProcessor stockSalesProcessor;

    private List<DealGroupDealDTO> deals;
    private Integer dpDealGroupIdInt;

    private MockedStatic<Lion> lionMockedStatic;


    @Before
    public void setUp() {
        deals = new ArrayList<>();
        DealGroupDealDTO dto = new DealGroupDealDTO();
        dto.setDealId(1L);
        DealBasicDTO dealBasicDTO = new DealBasicDTO();
        dealBasicDTO.setStatus(1);
        dto.setBasic(dealBasicDTO);
        StockDTO stockDTO = new StockDTO();
        stockDTO.setDpSales(1);
        stockDTO.setDpSales(11);
        stockDTO.setDpTotal(1111);
        stockDTO.setDpRemain(111);
        stockDTO.setMtSales(1);
        stockDTO.setMtTotal(1111);
        stockDTO.setMtRemain(111);
        stockDTO.setStatus(1);
        stockDTO.setIsDpSoldOut(false);
        stockDTO.setIsMtSoldOut(false);
        dto.setStock(stockDTO);
        deals.add(dto);
        dpDealGroupIdInt = 1;
        lionMockedStatic = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        lionMockedStatic.close();
    }

    @Test
    public void testTans2OldDTO() {
        lionMockedStatic.when(() -> Lion.getBoolean(Mockito.anyString(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(true);
        List<ProductStock> result = stockSalesProcessor.tans2OldDTO(dpDealGroupIdInt, deals);
        assertEquals(1, result.size());
    }
}
