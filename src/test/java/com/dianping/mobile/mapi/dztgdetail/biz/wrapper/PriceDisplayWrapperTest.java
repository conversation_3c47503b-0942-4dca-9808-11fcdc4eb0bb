package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.MemberPriceProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.service.PriceDisplayLocalService;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.BuyMoreSaveMoreReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.CePinTuanPassParamConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.CombinationDealInfo;
import com.dianping.mobile.mapi.dztgdetail.entity.CostEffectivePinTuan;
import com.dianping.mobile.mapi.dztgdetail.helper.PromoHelper;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.mobile.mapi.dztgdetail.util.JsonUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.google.common.collect.Lists;
import com.sankuai.beautycard.navigation.api.service.BeautyCardExposureService;
import com.sankuai.dealuser.price.display.api.enums.ExtensionKeyEnum;
import com.sankuai.dealuser.price.display.api.model.BatchPriceRequest;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PriceResponse;
import com.sankuai.dzcard.navigation.api.dto.CardQualifyEventIdDTO;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.MemberDiscountInfoDTO;
import org.junit.*;
import org.junit.platform.commons.util.StringUtils;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PriceDisplayWrapperTest {

    @InjectMocks
    private PriceDisplayWrapper priceDisplayWrapper;

    @Mock
    private PriceDisplayLocalService priceDisplayLocalService;

    @Mock
    private BeautyCardExposureService beautyCardExposureService;

    private  MockedStatic<FutureFactory> mocked;

    private MockedStatic<GreyUtils> greyUtilsMockedStatic;

    private MockedStatic<PromoHelper> promoHelperMockedStatic;

    @Mock
    private MemberPriceProcessor memberPriceProcessor;
    @Mock
    private Future<PriceResponse<Map<Long, List<PriceDisplayDTO>>>> priceFuture;
    @Mock
    private PriceResponse<Map<Long, List<PriceDisplayDTO>>> priceResponse;
    @Mock
    private MemberPriceWrapper memberPriceWrapper;
    private MockedStatic<LionConfigUtils> lionConfigUtilsMockedStatic;

    @Before
    public void setUp() {
        lionConfigUtilsMockedStatic = Mockito.mockStatic(LionConfigUtils.class);
        mocked = mockStatic(FutureFactory.class);
        greyUtilsMockedStatic = mockStatic(GreyUtils.class);
        promoHelperMockedStatic = mockStatic(PromoHelper.class);
    }

    @After
    public void tearDown() {
        lionConfigUtilsMockedStatic.close();
        mocked.close();
        greyUtilsMockedStatic.close();
        promoHelperMockedStatic.close();
    }


    /**
     * 测试ODP来源的情况
     */
    @Test
    public void testPrepareOdpSource() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(null);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setRequestSource(RequestSourceEnum.ODP.getSource());
        ctx.setExternal(false);

        when(priceDisplayLocalService.batchQueryPriceByLongShopId(any(), any())).thenReturn(null);
        Future mockFuture = mock(Future.class);
        mocked.when(FutureFactory::getFuture).thenReturn(mockFuture);
        // act
        priceDisplayWrapper.prepare(ctx);

        // assert
        assertNotNull(ctx.getFutureCtx().getNormalPriceFuture());
    }

    /**
     * 测试需要显示市场价的团购分类的情况
     */
    @Test
    public void testPrepareShowMarketPriceCategory() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(null);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setRequestSource(RequestSourceEnum.LIVE_STREAM.getSource());
        ctx.setExternal(false);
        MemberDiscountInfoDTO memberDiscountInfoDTO = new MemberDiscountInfoDTO();
        ctx.setShopMemberDiscountInfoDTO(memberDiscountInfoDTO);

        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(1);
        ctx.setChannelDTO(channelDTO);
        greyUtilsMockedStatic.when(() -> GreyUtils.isShowMarketPriceCategory(ctx)).thenReturn(true);
        when(priceDisplayLocalService.batchQueryPriceByLongShopId(any(), any())).thenReturn(null);
        when(memberPriceWrapper.isMemberPriceProcessorEnable(any())).thenReturn(true);

        Future mockFuture = mock(Future.class);
        mocked.when(FutureFactory::getFuture).thenReturn(mockFuture);
        // act
        priceDisplayWrapper.prepare(ctx);

        // assert
        assertNotNull(ctx.getFutureCtx().getShopMemberPromoPriceFuture());
        assertNotNull(ctx.getFutureCtx().getDealPromoPriceFuture());
    }

    /**
     * 测试需要在氛围条和普通优惠详情中显示价格的情况
     */
    @Test
    public void testPrepareNeedAtmosphereBarAndGeneralPromoDetail() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(null);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setRequestSource(RequestSourceEnum.LIVE_STREAM.getSource());
        ctx.setExternal(false);
        greyUtilsMockedStatic.when(() -> GreyUtils.needAtmosphereBarAndGeneralPromoDetail(ctx)).thenReturn(true);

        when(priceDisplayLocalService.batchQueryPriceByLongShopId(any(), any())).thenReturn(null);
        when(memberPriceWrapper.isMemberPriceProcessorEnable(any())).thenReturn(false);


        Future mockFuture = mock(Future.class);
        mocked.when(FutureFactory::getFuture).thenReturn(mockFuture);

        // act
        priceDisplayWrapper.prepare(ctx);

        // assert
        assertNotNull(ctx.getFutureCtx().getAtmosphereBarAndGeneralPromoDetailFuture());
    }

    /**
     * 测试需要在joyCard场景中显示价格的情况
     */
    @Test
    public void testPrepareJoyCardPromoScene() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(null);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setRequestSource(RequestSourceEnum.LIVE_STREAM.getSource());
        ctx.setExternal(false);

        when(priceDisplayLocalService.queryPrice(any(), any())).thenReturn(null);
        when(memberPriceWrapper.isMemberPriceProcessorEnable(any())).thenReturn(true);

        Future mockFuture = mock(Future.class);
        mocked.when(FutureFactory::getFuture).thenReturn(mockFuture);

        // act
        priceDisplayWrapper.prepare(ctx);

        // assert
        assertNotNull(ctx.getFutureCtx());
    }

    /**
<<<<<<< HEAD
=======
     * 测试需要在memberCard场景中显示价格的情况
     */
    @Test
    @Ignore
    public void testPrepareMemberCardPromoScene() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(null);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setRequestSource(RequestSourceEnum.LIVE_STREAM.getSource());
        ctx.setExternal(false);
        CardQualifyEventIdDTO CardQualifyEventIdDTO = new CardQualifyEventIdDTO();
        ctx.getPriceContext().setDcCardMemberCard(CardQualifyEventIdDTO);

        greyUtilsMockedStatic.when(() -> GreyUtils.isCouponBagCategory(ctx)).thenReturn(true);
        when(priceDisplayLocalService.queryPrice(any(), any())).thenReturn(null);
        when(beautyCardExposureService.getCommonExposureInfo(anyLong(), anyInt())).thenReturn(null);

        Future mockFuture = mock(Future.class);
        mocked.when(FutureFactory::getFuture).thenReturn(mockFuture);

        // act
        priceDisplayWrapper.prepare(ctx);

        // assert
        assertNotNull(ctx.getFutureCtx().getJoyDiscountCardPriceFuture());
        assertNotNull(ctx.getFutureCtx().getIdlePromoPriceFuture());
        assertNotNull(ctx.getFutureCtx().getBeautyCouponBagPromoPriceFuture());
    }

    /**
>>>>>>> master
     * 测试外部且没有场景的情况
     */
    @Test
    public void testProcessExternalNoScene() {
        // arrange
        EnvCtx envCtxMock = mock(EnvCtx.class);
        when(envCtxMock.isExternalAndNoScene()).thenReturn(true);
        DealCtx ctx = new DealCtx(envCtxMock);

        DealGroupBaseDTO dealGroupBaseDTO = new DealGroupBaseDTO();
        dealGroupBaseDTO.setDealGroupPrice(new BigDecimal(10));
        dealGroupBaseDTO.setMarketPrice(new BigDecimal(20));
        ctx.setDealGroupBase(dealGroupBaseDTO);

        // act
        priceDisplayWrapper.process(ctx);

        // assert
        assertNotNull(ctx.getPriceContext().getNormalPrice());
    }

    @Test
    public void testFillPinTuanExtensionParams() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setShareToken("123");
        costEffectivePinTuan.setPinTuanActivityId("456");
        ctx.setCostEffectivePinTuan(costEffectivePinTuan);
        BatchPriceRequest request = JSON.parseObject("{\"clientEnv\":{\"cityId\":10,\"clientType\":200502,\"gpsCityId\":10,\"gpsCoordinateType\":\"GCJ02\",\"latitude\":31.22001934883066,\"longitude\":121.5471059804789,\"unionId\":\"e931975d53f94652a9aee6cac17acca80000000000001453000\",\"uuid\":\"0000000000000BF83E0F7C51E4E65A63EC4EA35305780A162880001982084426\",\"version\":\"12.21.200\"},\"days\":0,\"extension\":{\"页面来源\":\"8\",\"promoDescType\":\"newTuanDetail\",\"COMMISSION_RATE\":\"6.0000\",\"pageName\":\"DealGroupNormalDetail\"},\"longShopId2ProductIds\":{500130861:[{\"categoryId\":0,\"extParams\":{},\"lProductId\":775713394,\"lSkuId\":0,\"priceTime\":0,\"priceTrendType\":0,\"productId\":775713394,\"productIdL\":0,\"productType\":1,\"skuId\":0,\"skuIdL\":0,\"spuId\":0}]},\"scene\":400200,\"userId\":131831705}", BatchPriceRequest.class);
        priceDisplayWrapper.fillPinTuanExtensionParams(request, ctx, 500130861L);
        Assert.assertTrue(request.getLongShopId2ProductIds().get(500130861L).get(0).getExtParams().get(ExtensionKeyEnum.ShareToken.getDesc()).equals("123"));
        Assert.assertTrue(request.getLongShopId2ProductIds().get(500130861L).get(0).getExtParams().get(ExtensionKeyEnum.PinTuanActivityId.getDesc()).equals("456"));
    }

    @Test
    public void testFillCostEffectivePinTuanParams() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        CePinTuanPassParamConfig ce= new CePinTuanPassParamConfig();
        costEffectivePinTuan.setPinTuanPassParamConfig(ce);
        ctx.setCostEffectivePinTuan(costEffectivePinTuan);
        PriceDisplayDTO cePinTuanPrice = JsonUtils.fromJson("{\"identity\":{\"spuId\":0,\"productId\":*********,\"productType\":1,\"skuId\":450622291,\"categoryId\":0,\"productIdL\":0,\"skuIdL\":0,\"priceTime\":0,\"priceTrendType\":0,\"lskuId\":450622291,\"lproductId\":*********},\"price\":9.00,\"maxPrice\":9.00,\"basePrice\":99.00,\"marketPrice\":111.00,\"showMarketPrice\":false,\"promoAmount\":102.00,\"usedPromos\":[{\"identity\":{\"promoId\":*********,\"promoType\":11,\"promoTypeDesc\":\"团购优惠12元\",\"sourceType\":1,\"promoShowType\":\"DEAL_PROMO\"},\"amount\":12.00,\"tag\":\"团购优惠12元\",\"description\":\"团购优惠12元\",\"priceThrough\":true,\"canAssign\":false,\"extendDesc\":\"团购优惠，下单立省12元\",\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoStatus\":0,\"couponValueType\":0,\"promoTextDTO\":{\"title\":\"团购优惠12元\",\"subTitle\":\"团购优惠，下单立省12元\",\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoDivideType\":\"DEAL_PROMO\"},\"newUser\":false},{\"identity\":{\"promoId\":1900092820,\"promoType\":1,\"promoTypeDesc\":\"美团补贴90元\",\"sourceType\":1,\"promoShowType\":\"MT_SUBSIDY\"},\"amount\":9E+1,\"tag\":\"减90元\",\"priceThrough\":true,\"canAssign\":false,\"extendDesc\":\"美团补贴，下单立省90元，每人总共可享受999次\",\"icon\":\"https://p0.meituan.net/travelcube/4382b3cf4c10bf7ff8d9ef7438c554c64793.png\",\"totalStock\":0,\"remainStock\":0,\"promoIdentity\":\"pinTuan,juhuasuan,exclusiveDeduction\",\"promoStatus\":0,\"couponValueType\":0,\"promoStock\":{\"totalStock\":0,\"remainStock\":0},\"newActivityGroupId\":\"288230376151832799\",\"promoTextDTO\":{\"title\":\"美团补贴90元\",\"subTitle\":\"美团补贴，下单立省90元，每人总共可享受999次\",\"icon\":\"https://p0.meituan.net/travelcube/4382b3cf4c10bf7ff8d9ef7438c554c64793.png\",\"promoDivideType\":\"MT_SUBSIDY\"},\"reductionSaleChannels\":[\"costEffective\"],\"amountShareDetail\":{\"1\":9E+1,\"2\":0},\"promotionExplanatoryTags\":[5],\"promotionOtherInfoMap\":{\"SHARE_HELP_BASE_INFO\":\"{\\\"activityId\\\":29855,\\\"shareAwardPrizeId\\\":0,\\\"shareExpireType\\\":3,\\\"shareJoinCount\\\":0,\\\"shareJoinDayCount\\\":0,\\\"shareLogic\\\":\\\"\\\",\\\"helpSuccCountMin\\\":2,\\\"helpSuccCountMax\\\":99999999,\\\"helpDayCount\\\":0,\\\"helpPersonCount\\\":null,\\\"helpAwardPrizeId\\\":0,\\\"helpLogic\\\":\\\"\\\",\\\"addTime\\\":1715331784000,\\\"shareExpireTimeDetail\\\":\\\"{\\\\\\\"expireTimeDynamic\\\\\\\":*********}\\\",\\\"shareEffectTime\\\":0,\\\"shareHelpType\\\":1,\\\"shareStartTimeDetail\\\":null,\\\"shareStartType\\\":1,\\\"groupSuccCountMin\\\":3}\",\"GROUP_STATUS\":\"2\",\"MATERIAL_INFO_LIST\":\"[{\\\"activityId\\\":29855,\\\"materialId\\\":10228828,\\\"fieldType\\\":\\\"TEXT\\\",\\\"fieldKey\\\":\\\"shareHelpRuleDetail\\\",\\\"fieldName\\\":\\\"活动玩法\\\",\\\"fieldDesc\\\":\\\"0\\\",\\\"fieldValue\\\":\\\"1、售卖渠道\\\\n美团APP，美团小程序。\\\\n\\\\n2、成团规则\\\\n开团有效时间内，成功邀请好友参与拼团（完成支付）即拼团成功。开团有效时间结束后，如果拼团人数不足，则拼团失败，相应订单自动取消，订单款项3日内全部原路退回。\\\\n\\\\n3、参与限制\\\\n1）发起拼团：活动城市全部用户；\\\\n2）参与拼团：仅限美食团购新用户（即365天内没有在美食团购下过订单的用户）。\\\\n\\\\n4、 温馨提示\\\\n1）拼团不局限于某一个商品，发起人和参与人下单可拼商品中的任意一个，均可参团；\\\\n2）如遇拼团过程中无法手动操作退款，拼团失败后自动退款，成功后也可手动退款；\\\\n3）可拼商品库存有限，抢完为止（商品拼团价格以活动页面每日实际数据为准）。\\\",\\\"actionType\\\":null,\\\"actionId\\\":\\\"\\\"},{\\\"activityId\\\":29855,\\\"materialId\\\":10228829,\\\"fieldType\\\":\\\"TEXT\\\",\\\"fieldKey\\\":\\\"shareHelpRuleDetail\\\",\\\"fieldName\\\":\\\"活动规则\\\",\\\"fieldDesc\\\":\\\"1\\\",\\\"fieldValue\\\":\\\"1）同一账号、手机号、身份证号、支付账号、移动设备等均视为同一活动用户。\\\\n2）活动优惠仅限本账户使用，不得转赠、兑现。\\\\n3）参与活动用户不得进行虚假交易，不得通过各种方式参与或协助套现美团优惠资源，不得以任何形式的软件或其他恶意方式参与本活动，不得实施违反诚实信用的行为及实施其他非真实活动的作弊行为。如美团平台基于合理理由认定用户存在上述违规行为的，有权取消用户参与本活动的资格，撤销违规交易并收回已发放权益。\\\\n4）若活动中遭遇大面积作弊、通讯路线故障或计算机大规模瘫痪等原因导致难以继续开展本活动，美团平台保留取消、修改或暂停本活动的权利。\\\\n5）客服热线电话：10107888。\\\\n\\\",\\\"actionType\\\":null,\\\"actionId\\\":\\\"\\\"}]\",\"CONFIG_CHANNELS\":\"[costEffective]\",\"SHARE_HELP_INFO_LIST\":\"[{\\\"shareRecordUserInfos\\\":[{\\\"activityId\\\":29855,\\\"userId\\\":5035686652,\\\"userSource\\\":1,\\\"shareActionId\\\":442193,\\\"shareHelpInfoId\\\":3222,\\\"shareRecordId\\\":354428,\\\"expireTime\\\":1715927743000,\\\"shareEffectTime\\\":1715927743000,\\\"addTime\\\":1715571355000,\\\"userDisplayContent\\\":{\\\"nickName\\\":\\\"GRU445529177\\\",\\\"avatar\\\":\\\"https://p0.meituan.net/travelcube/53ed7c702791795af246258f873ebe295918.png\\\",\\\"currentUser\\\":true},\\\"prizeItemType\\\":null,\\\"prizeItemInfo\\\":null,\\\"status\\\":1}],\\\"helpRecordUserInfos\\\":[{\\\"shareUserId\\\":5035686652,\\\"shareActionId\\\":442193,\\\"activityId\\\":29855,\\\"userId\\\":5091446657,\\\"userSource\\\":1,\\\"helpActionId\\\":451592,\\\"shareHelpInfoId\\\":3222,\\\"helpRecordId\\\":364602,\\\"addTime\\\":1715741479000,\\\"userDisplayContent\\\":{\\\"nickName\\\":\\\"QKU998986877\\\",\\\"avatar\\\":\\\"https://p0.meituan.net/travelcube/53ed7c702791795af246258f873ebe295918.png\\\",\\\"currentUser\\\":false},\\\"prizeItemType\\\":null,\\\"prizeItemInfo\\\":null}],\\\"helpSucc\\\":false,\\\"helpSuccTime\\\":null,\\\"helpPrizeInfoId\\\":0,\\\"shareHelpType4User\\\":1,\\\"shareToken\\\":\\\"4697254419937404941\\\",\\\"helpSuccCountMin\\\":2,\\\"helpSuccCountMax\\\":99999999,\\\"hasHelpCount\\\":1,\\\"latestShareToken\\\":true,\\\"continueShare\\\":null,\\\"groupSuccCountMin\\\":3}]\",\"LIMIT_NEW_CUSTOMER_JOIN\":\"false\",\"PINTUAN_ACTIVITY_ID\":\"29855\"},\"promotionDisplayTextMap\":{},\"newUser\":false}],\"morePromos\":[],\"pricePromoInfoMap\":{},\"extPrices\":[{\"extPriceType\":1,\"extPricePromoAmount\":0,\"extPrice\":99.00,\"extPriceTitle\":\"全网低价\"}],\"promoTag\":\"特惠促销共省¥102\",\"shortPromoTag\":\"共省¥102\",\"promoDiscount\":0.09,\"promoTagType\":0,\"activityDTO\":{\"discountProvider\":2,\"discountClassifyType\":0},\"pricePowerTagDisplayDTO\":{\"allTagList\":[],\"filteredTagList\":[]},\"extendDisplayInfo\":{}}", PriceDisplayDTO.class);
        priceDisplayWrapper.fillCostEffectivePinTuanParams(ctx, cePinTuanPrice);
        Assert.assertTrue("4697254419937404941".equals(ctx.getCostEffectivePinTuan().getShareToken()));
    }

    @Test
    public void testBuildPriceSource() {
        Map<String, String> requestExtParams = new HashMap<>();
        requestExtParams.put("subsidyScene", "hitGuessLikeSubsidy");
        DealBaseReq req = new DealBaseReq();
        req.setExtParam(JSON.toJSONString(requestExtParams));
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setDealBaseReq(req);
        ctx.setRequestSource("caixi");
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.useNewSourceForCaixi()).thenReturn(true);
        Map<String, String> pageSource2OrderPromotionChannel = new HashMap<>();
        pageSource2OrderPromotionChannel.put("caixi", "caixi");
        String result = priceDisplayWrapper.buildPriceSource(pageSource2OrderPromotionChannel, ctx);
        Assert.assertTrue("caixi".equals(result));
    }

    @Test
    public void testGetPriceSecretInfo() {
        String secretInfo = priceDisplayWrapper.getPriceSecretInfo(priceFuture);
        Assert.assertTrue(StringUtils.isBlank(secretInfo));
    }

    @Test
    public void testGetBuyMoreSaveMorePriceInfo() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        List<CombinationDealInfo> dealInfoList = Lists.newArrayList();
        CombinationDealInfo combinationDealInfo = new CombinationDealInfo();
        combinationDealInfo.setMainDealId(11);
        combinationDealInfo.setBindingDealId(2);
        combinationDealInfo.setItemId("23");
        combinationDealInfo.setBindingSource(1);
        dealInfoList.add(combinationDealInfo);
        BuyMoreSaveMoreReq moreReq = new BuyMoreSaveMoreReq();
        when(priceDisplayLocalService.batchQueryPriceByLongShopId(any(), any())).thenReturn(priceResponse);
        Future<PriceResponse<Map<Long, List<PriceDisplayDTO>>>> future = priceDisplayWrapper.getBuyMoreSaveMorePriceInfo(1, envCtx, 1L, dealInfoList, moreReq);
        Assert.assertNull(future);
    }
}
