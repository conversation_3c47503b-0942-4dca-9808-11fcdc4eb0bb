package com.dianping.mobile.mapi.dztgdetail.facade.rcf.facade;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.style.dto.laout.DealPageLayoutComponentDTO;
import com.dianping.deal.style.dto.laout.DealPageLayoutConfigDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.ProcessHandler;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FlashDealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FlashFutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealFlashReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealStyleBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.struct.DealDetailStructModuleDo;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.List;

import static org.mockito.Mockito.verify;

/**
 * <AUTHOR>
 * @create 2024/10/30 15:02
 */
@RunWith(MockitoJUnitRunner.class)
public class DealDetailFlashFacadeTest {
    @InjectMocks
    DealDetailFlashFacade dealDetailFlashFacade;
    FlashDealCtx ctx;

    @Before
    public void setUp() throws Exception {
        String envStr ="{\"dpUserId\":0,\"dpVirtualUserId\":0,\"mtUserId\":0,\"mtVirtualUserId\":0,\"unionId\":\"b797a0e80f464a148262d97615626b40a172312470717747732\",\"dpId\":\"b797a0e80f464a148262d97615626b40a172312470717747732\",\"uuid\":\"0000000000000BD99CEC2744D4B1E9646ED879839FC82A172312470728647594\",\"version\":\"12.25.400\",\"clientType\":200501,\"appDeviceId\":\"b797a0e80f464a148262d97615626b40a172312470717747732\",\"appId\":10,\"mtsiFlag\":\"0\",\"requestURI\":\"/general/platform/dztgdetail/dealdetailflash.bin\",\"userAgent\":\"MApi 1.3 (com.sankuai.meituan 12.25.400 meituaninternaltest ZTE_A2022H; Android 11)\",\"userIp\":\"**************\",\"dztgClientTypeEnum\":\"MEITUAN_APP\",\"startTime\":1730272457914,\"meituanClientList\":[\"MEITUAN_APP\",\"MEITUAN_FROM_H5\",\"MEITUAN_MAP_APP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_LIVE_WEIXIN_MINIAPP\"],\"dianpingClientList\":[\"DIANPING_APP\",\"DIANPING_FROM_H5\",\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"],\"merchantClientList\":[\"DPMERCHANT\"],\"mtMiniAppList\":[\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_KUAISHOU_MINIAPP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_LIVE_WEIXIN_MINIAPP\"],\"dpMiniAppList\":[\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"],\"nativeAppList\":[\"MEITUAN_APP\",\"MEITUAN_MAP_APP\",\"DIANPING_APP\"],\"wxMiniList\":[\"MEITUAN_WEIXIN_MINIAPP\",\"DIANPING_WEIXIN_MINIAPP\"],\"virtualUserId\":0,\"dpMerchant\":false,\"android\":true,\"fromH5\":false,\"mt\":true,\"apollo\":false,\"login\":false,\"mainApp\":true,\"miniApp\":false,\"thirdPlatform\":false,\"ios\":false,\"dp\":false,\"mtMiniApp\":false,\"dpMiniApp\":false,\"mainWX\":false,\"externalAndNoScene\":false,\"mtLiveMinApp\":false,\"mainWeb\":false,\"wxMini\":false,\"userId\":0,\"external\":false,\"native\":true}";
        EnvCtx envCtx = JSON.parseObject(envStr, EnvCtx.class);
        ctx = new FlashDealCtx(envCtx);
    }

    @Mock
    private ProcessHandler<FlashDealCtx> dealDetailFlashHandler;

    @Mock
    private ProcessHandler<FlashDealCtx> dealDetailFlashOtherHandler;


    @Test
    public void buildDealDetailFlashPBO(){
        String reqStr ="{\"dealgroupid\":925669993,\"poiid\":928040795,\"pageSource\":\"poi_page\",\"mrnversion\":\"0.5.13\",\"deviceheight\":906}";
        DealFlashReq req = JSON.parseObject(reqStr,DealFlashReq.class);
       
        EnvCtx envCtx = ctx.getEnvCtx();
        dealDetailFlashFacade.initDealsBaseData(req, envCtx);

        String styleBOStr = "{\"moduleConfigsModule\":{\"key\":\"joy_bar\",\"extraInfo\":\"newtuandeal\",\"generalInfo\":\"card_style_v2\",\"moduleConfigs\":[{\"key\":\"dealdetail_gc_packagedetail\",\"value\":\"uniform-structure-table\"}],\"moduleAbConfigs\":[{\"configs\":[{\"expId\":\"exp000122\",\"expResult\":\"exp000122_c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"485b8913-22d9-4b6e-a6ec-769bc6471aa9\\\",\\\"ab_id\\\":\\\"exp000122_c\\\"}\"}],\"key\":\"GCPlatformModules/picasso_deal_detail_head_module\"}],\"tort\":false,\"dzx\":false,\"dpOrder\":false}}";
        DealStyleBO dealStyleBO = JSON.parseObject(styleBOStr, DealStyleBO.class);
        ctx.setDealStyleBO(dealStyleBO);

        String detailStructStr ="{\"mustGroups\":[{\"dealStructInfo\":[{\"items\":[{\"value\":\"100元\",\"name\":\"网费面额\",\"type\":0},{\"value\":\"显示器：卓威2546k 华硕2k 260 华硕2k180加飞利浦2k24寸\\n显卡：4060 3070 3060\\n鼠标：罗技402\",\"name\":\"网吧设备\",\"type\":0},{\"value\":\"一次性耳机套，steam、全场lol特权，免费加速器\",\"name\":\"增值服务\",\"type\":0}],\"title\":\"新会员专享49.9元充值套餐\",\"copies\":\"1份\"}]}],\"price\":\"49.9元\",\"marketPrice\":\"100元\",\"title\":\"团购详情\",\"processType\":0,\"showStyle\":0,\"marketPriceHided\":false}";
        DealDetailStructModuleDo dealDetailStruct = JSON.parseObject(detailStructStr, DealDetailStructModuleDo.class);
        ctx.setDealDetailStruct(dealDetailStruct);

        String layoutConfigStr ="{\"render\":true,\"report\":true}";
        DealPageLayoutConfigDTO layoutConfig = JSON.parseObject(layoutConfigStr, DealPageLayoutConfigDTO.class);
        ctx.setLayoutConfig(layoutConfig);

        String layoutCompomentStr ="[{\"key\":\"local_dealdetail_transparent_scrollview_module\",\"height\":275},{\"key\":\"local_dealdetail_progress_page_control_module\",\"height\":0},{\"key\":\"local_dealdetail_banner_view_module\",\"height\":73},{\"key\":\"local_dealdetail_promo_module\",\"height\":37},{\"key\":\"local_dealdetail_deal_title_module\",\"height\":40},{\"key\":\"local_dealdetail_new_review_module\",\"height\":28},{\"key\":\"local_dealdetail_infos_module\",\"height\":101},{\"key\":\"local_dealdetail_tab_module\",\"height\":44},{\"key\":\"local_dealdetail_struct_module\",\"height\":342}]";
        List<DealPageLayoutComponentDTO> layoutComponents = JSON.parseArray(layoutCompomentStr, DealPageLayoutComponentDTO.class);
        ctx.setLayoutComponents(layoutComponents);

        dealDetailFlashFacade.buildResult(ctx);

        Assert.assertNotNull(envCtx);
    }

}
