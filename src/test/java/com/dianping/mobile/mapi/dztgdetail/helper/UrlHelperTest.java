package com.dianping.mobile.mapi.dztgdetail.helper;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.service.bo.DrivingPoi;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MpAppIdEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class UrlHelperTest {

    @Mock
    private Lion lion;

    private MockedStatic<LionConfigUtils> lionConfigUtilsMockedStatic;

    @Before
    public void setup() {
        lionConfigUtilsMockedStatic = Mockito.mockStatic(LionConfigUtils.class);
    }

    @After
    public void tearDown() {
        lionConfigUtilsMockedStatic.close();
    }

    /**
     * Test getGoodReviewUrl method when clientType is neither Dianping main app nor Meituan main app.
     */
    @Test
    public void testGetGoodReviewUrlWhenClientTypeIsNotDpMainAppOrMtMainApp() throws Throwable {
        // Arrange
        int dealId = 123;
        // Assuming 3 represents an unsupported client type
        int clientType = 3;
        // Act
        String result = UrlHelper.getGoodReviewUrl(dealId, clientType);
        // Assert
        assertNull(result);
    }

    /**
     * 测试getDpMapUrl方法，当dpShopId小于等于0时，应返回null
     */
    @Test
    public void testGetDpMapUrlDpShopIdLessThanOrEqualToZero() {
        // arrange
        DrivingPoi req = DrivingPoi.builder().build();
        long dpShopId = 0;
        String dpShopUUid = "testUuid";
        double lng = 123.0;
        double lat = 45.0;
        String address = "testAddress";
        // act
        String result = UrlHelper.getDpMapUrl(req, dpShopId, dpShopUUid, lng, lat, address);
        // assert
        assertNull(result);
    }

    /**
     * 测试getDpMapUrl方法，当dpShopId大于0时，应返回一个包含dpShopUUid和dpShopId的URL
     */
    @Test
    public void testGetDpMapUrlDpShopIdGreaterThanZero() {
        // arrange
        DrivingPoi req = DrivingPoi.builder().build();
        long dpShopId = 1;
        String dpShopUUid = "testUuid";
        double lng = 123.0;
        double lat = 45.0;
        String address = "testAddress";
        // act
        String result = UrlHelper.getDpMapUrl(req, dpShopId, dpShopUUid, lng, lat, address);
        // assert
        assertNotNull(result);
        assertTrue(result.contains(dpShopUUid));
        assertTrue(result.contains(String.valueOf(dpShopId)));
    }

    /**
     * Tests getCouponDpUrl method with a valid coupon ID.
     */
    @Test
    public void testGetCouponDpUrl_ValidCouponId() throws Throwable {
        // Arrange
        String unifiedCouponId = "123456";
        // Act
        String actualUrl = UrlHelper.getCouponDpUrl(unifiedCouponId);
        // Assert
        assertTrue(actualUrl.contains(unifiedCouponId));
    }

    /**
     * Tests getCouponDpUrl method with an invalid coupon ID.
     */
    @Test
    public void testGetCouponDpUrl_InvalidCouponId() throws Throwable {
        // Arrange
        String unifiedCouponId = "";
        // Act
        String actualUrl = UrlHelper.getCouponDpUrl(unifiedCouponId);
        // Assert
        assertTrue(actualUrl.endsWith("couponid%3d"));
    }

    /**
     * Tests getCouponDpUrl method to ensure it always returns a URL starting with the expected scheme.
     */
    @Test
    public void testGetCouponDpUrl_AlwaysReturnsUrlWithScheme() throws Throwable {
        // Arrange
        String unifiedCouponId = "123456";
        // Act
        String actualUrl = UrlHelper.getCouponDpUrl(unifiedCouponId);
        // Assert
        assertTrue(actualUrl.startsWith("dianping://web?url="));
    }

    @Test
    public void testGetAppUrlShareUrlIsNull() throws Throwable {
        String result = UrlHelper.getAppUrl(null, null, true);
        assertNull(result);
    }

    @Test
    public void testGetAppUrlMtIsTrueAndCtxIsNull() throws Throwable {
        String result = UrlHelper.getAppUrl(null, "http://www.meituan.com", true);
        assertEquals("imeituan://www.meituan.com/web?url=http%3A%2F%2Fwww.meituan.com", result);
    }

    @Test
    public void testGetAppUrlMtIsTrueAndCtxIsNotNullAndMpAppIdIsMtWxApp() throws Throwable {
        EnvCtx ctx = mock(EnvCtx.class);
        when(ctx.getMpAppId()).thenReturn(MpAppIdEnum.MT_WEIXIN_MINIPROGRAM.getMpAppId());
        String result = UrlHelper.getAppUrl(ctx, "http://www.meituan.com", true);
        // Corrected the expected URL to match the encoded output
        assertEquals("/index/pages/h5/h5?f_token=1&f_openId=1&weburl=http%3A%2F%2Fwww.meituan.com%26product%3Dmtwxapp", result);
    }

    @Test
    public void testGetAppUrlMtIsTrueAndCtxIsNotNullAndMpAppIdIsNotMtWxApp() throws Throwable {
        EnvCtx ctx = mock(EnvCtx.class);
        when(ctx.getMpAppId()).thenReturn("not_mt_wx_app");
        String result = UrlHelper.getAppUrl(ctx, "http://www.meituan.com", true);
        assertEquals("imeituan://www.meituan.com/web?url=http%3A%2F%2Fwww.meituan.com", result);
    }

    @Test
    public void testGetAppUrlMtIsFalseAndCtxIsNull() throws Throwable {
        String result = UrlHelper.getAppUrl(null, "http://www.meituan.com", false);
        assertEquals("dianping://web?url=http%3A%2F%2Fwww.meituan.com", result);
    }

    @Test
    public void testGetAppUrlMtIsFalseAndCtxIsNotNullAndDztgClientTypeEnumIsDianpingWxApp() throws Throwable {
        EnvCtx ctx = mock(EnvCtx.class);
        when(ctx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP);
        String result = UrlHelper.getAppUrl(ctx, "http://www.meituan.com", false);
        assertEquals("/pages/webview/webview?url=http%3A%2F%2Fwww.meituan.com", result);
    }

    @Test
    public void testGetAppUrlMtIsFalseAndCtxIsNotNullAndDztgClientTypeEnumIsNotDianpingWxApp() throws Throwable {
        EnvCtx ctx = mock(EnvCtx.class);
        when(ctx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_APP);
        String result = UrlHelper.getAppUrl(ctx, "http://www.meituan.com", false);
        assertEquals("dianping://web?url=http%3A%2F%2Fwww.meituan.com", result);
    }

    /**
     * 测试getMtMapUrl方法，当mtShopId小于等于0时，应返回null
     */
    @Test
    public void testGetMtMapUrlMtShopIdLessThanOrEqualToZero() throws Throwable {
        // arrange
        long mtShopId = 0;
        double lng = 123.0;
        double lat = 45.0;
        // act
        String result = UrlHelper.getMtMapUrl(mtShopId, lng, lat);
        // assert
        assertNull(result);
    }

    /**
     * 测试getMtMapUrl方法，当mtShopId大于0时，应返回包含mtShopId，lng和lat的URL字符串
     */
    @Test
    public void testGetMtMapUrlMtShopIdGreaterThanZero() throws Throwable {
        // arrange
        long mtShopId = 1;
        double lng = 123.0;
        double lat = 45.0;
        // act
        String result = UrlHelper.getMtMapUrl(mtShopId, lng, lat);
        // assert
        assertNotNull(result);
        assertTrue(result.contains("imeituan://www.meituan.com/mapchannel/poi/detail?mapsource=gc&overseas=0&coordtype=0&poi_id=" + mtShopId + "&latitude=" + lat + "&longitude=" + lng));
    }

    /**
     * Tests the getWxBaseShareUrl method when h5Url is empty.
     */
    @Test
    public void testGetWxBaseShareUrlWhenH5UrlIsEmpty() throws Throwable {
        // Arrange
        String h5Url = "";
        boolean mt = true;
        // Act
        String result = UrlHelper.getWxBaseShareUrl(h5Url, mt);
        // Assert
        assertNull(result);
    }

    /**
     * Tests the getWxBaseShareUrl method when h5Url is not empty and mt is true.
     */
    @Test
    public void testGetWxBaseShareUrlWhenH5UrlIsNotEmptyAndMtIsTrue() throws Throwable {
        // Arrange
        String h5Url = "http://www.meituan.com";
        boolean mt = true;
        // Act
        String result = UrlHelper.getWxBaseShareUrl(h5Url, mt);
        // Assert
        assertEquals("/index/pages/h5/h5?f_openId=1&f_token=1&weburl=http%3A%2F%2Fwww.meituan.com%26product%3Dmtwxapp", result);
    }

    /**
     * Tests the getWxBaseShareUrl method when h5Url is not empty and mt is false.
     */
    @Test
    public void testGetWxBaseShareUrlWhenH5UrlIsNotEmptyAndMtIsFalse() throws Throwable {
        // Arrange
        String h5Url = "http://www.meituan.com";
        boolean mt = false;
        // Act
        String result = UrlHelper.getWxBaseShareUrl(h5Url, mt);
        // Assert
        assertEquals("/pages/webview/webview?url=http%3A%2F%2Fwww.meituan.com%26utm_source%3Ddianping-wxapp%26token%3D%21%26openId%3D%21", result);
    }

    /**
     * Tests getCouponMtWebUrl method with a valid unifiedCouponId and assumes default URL from Lion configuration.
     */
    @Test
    public void testGetCouponMtWebUrl_Normal() throws Throwable {
        // Arrange
        String unifiedCouponId = "123456";
        String expectedUrl = "https://g.meituan.com/av/rainbow/1737308/index.html?couponid=" + unifiedCouponId;
        // Act
        String actualUrl = UrlHelper.getCouponMtWebUrl(unifiedCouponId);
        // Assert
        assertEquals(expectedUrl, actualUrl);
    }

    /**
     * Tests getCouponMtWebUrl method with a default configuration and a valid unifiedCouponId.
     */
    @Test
    public void testGetCouponMtWebUrl_Default() throws Throwable {
        // Arrange
        String unifiedCouponId = "123456";
        String expectedUrl = "https://g.meituan.com/av/rainbow/1737308/index.html?couponid=" + unifiedCouponId;
        // Act
        String actualUrl = UrlHelper.getCouponMtWebUrl(unifiedCouponId);
        // Assert
        assertEquals(expectedUrl, actualUrl);
    }

    /**
     * Tests getCouponMtWebUrl method with an empty unifiedCouponId.
     */
    @Test
    public void testGetCouponMtWebUrl_EmptyUnifiedCouponId() throws Throwable {
        // Arrange
        String unifiedCouponId = "";
        String expectedUrl = "https://g.meituan.com/av/rainbow/1737308/index.html?couponid=";
        // Act
        String actualUrl = UrlHelper.getCouponMtWebUrl(unifiedCouponId);
        // Assert
        assertEquals(expectedUrl, actualUrl);
    }

    private static class TestableUrlHelper extends UrlHelper {

        // Override a method to return a predictable base URL for testing purposes
        public static String getCouponDpWebUrlTestable(String unifiedCouponId) {
            // Simulate fetching the base URL from a configuration
            String baseUrl = "https://g.dianping.com/av/rainbow/1737308/index.html?couponid=";
            // Adjusted to handle null unifiedCouponId
            return baseUrl + (unifiedCouponId == null ? "" : unifiedCouponId);
        }
    }

    @Test
    public void testGetCouponDpWebUrl_ValidUnifiedCouponIdAndBaseUrl() throws Throwable {
        // Arrange
        String unifiedCouponId = "123456";
        String expectedUrl = "https://g.dianping.com/av/rainbow/1737308/index.html?couponid=123456";
        // Act
        String actualUrl = TestableUrlHelper.getCouponDpWebUrlTestable(unifiedCouponId);
        // Assert
        assertEquals(expectedUrl, actualUrl);
    }

    @Test
    public void testGetCouponDpWebUrl_NullUnifiedCouponIdAndNoBaseUrl() throws Throwable {
        // Arrange
        String unifiedCouponId = null;
        // Adjusted expectation
        String expectedUrl = "https://g.dianping.com/av/rainbow/1737308/index.html?couponid=";
        // Act
        String actualUrl = TestableUrlHelper.getCouponDpWebUrlTestable(unifiedCouponId);
        // Assert
        assertEquals(expectedUrl, actualUrl);
    }

    @Test
    public void testGetCouponDpWebUrl_EmptyUnifiedCouponIdAndBaseUrl() throws Throwable {
        // Arrange
        String unifiedCouponId = "";
        String expectedUrl = "https://g.dianping.com/av/rainbow/1737308/index.html?couponid=";
        // Act
        String actualUrl = TestableUrlHelper.getCouponDpWebUrlTestable(unifiedCouponId);
        // Assert
        assertEquals(expectedUrl, actualUrl);
    }

    @Test
    public void testGetCouponDpWebUrl_ValidUnifiedCouponIdAndNoBaseUrl() throws Throwable {
        // Arrange
        String unifiedCouponId = "123456";
        String expectedUrl = "https://g.dianping.com/av/rainbow/1737308/index.html?couponid=123456";
        // Act
        String actualUrl = TestableUrlHelper.getCouponDpWebUrlTestable(unifiedCouponId);
        // Assert
        assertEquals(expectedUrl, actualUrl);
    }

    @Test
    public void getBaiduDianpingMiniAppBuyUrlTest(){
        Class clazz = UrlHelper.class;
        UrlHelper urlHelper =  new UrlHelper();
        String result;
        try {
            Method method = clazz.getDeclaredMethod("getBaiduDianpingMiniAppBuyUrl", DealCtx.class, String.class);
            method.setAccessible(true);
            EnvCtx envCtx = new EnvCtx();
            DealCtx ctx  = new DealCtx(envCtx);
            ctx.setMtLongShopId(20L);
            ctx.setDpLongShopId(20L);
            String finalPrice = "10";
            result = (String) method.invoke(urlHelper, ctx, finalPrice);
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e);
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        assertNotNull(result);

    }

    @Test
    public void getDianpingMiniAppBuyUrlTest(){
        Class clazz = UrlHelper.class;
        UrlHelper urlHelper =  new UrlHelper();
        String result;
        try {
            Method method = clazz.getDeclaredMethod("getDianpingMiniAppBuyUrl", DealCtx.class, String.class);
            method.setAccessible(true);
            EnvCtx envCtx = new EnvCtx();
            DealCtx ctx  = new DealCtx(envCtx);
            ctx.setMtLongShopId(20L);
            ctx.setDpLongShopId(20L);
            String finalPrice = "10";
            result = (String) method.invoke(urlHelper, ctx, finalPrice);
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e);
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        assertNotNull(result);

    }

    @Test
    public void testBuildPromotionChannel() {
        Map<String, String> requestExtParams = new HashMap<>();
        requestExtParams.put("subsidyScene", "hitGuessLikeSubsidy");
        DealBaseReq req = new DealBaseReq();
        req.setExtParam(JSON.toJSONString(requestExtParams));
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setDealBaseReq(req);
        ctx.setRequestSource("caixi");
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.useNewSourceForCaixi()).thenReturn(true);
        Map<String, String> pageSource2OrderPromotionChannel = new HashMap<>();
        pageSource2OrderPromotionChannel.put("caixi", "caixi");
        String result = UrlHelper.buildPromotionChannel(ctx, pageSource2OrderPromotionChannel);
        Assert.assertTrue("caixi".equals(result));
    }

    @Test
    public void testGetMtWxMainShopUrl_WithValidPoiId() {
        long poiId = 0;
        DealCtx ctx = new DealCtx(new EnvCtx());
        Map<String, String> requestExtParams = new HashMap<>();
        requestExtParams.put("subsidyScene", "hitGuessLikeSubsidy");
        DealBaseReq req = new DealBaseReq();
        req.setExtParam(JSON.toJSONString(requestExtParams));
        ctx.setDealBaseReq(req);
        ctx.setRequestSource("caixi");
        BestShopDTO bestShopResp = new BestShopDTO();
        bestShopResp.setMtShopId(poiId);
        bestShopResp.setDpShopId(poiId);
        String result = UrlHelper.getMtWxMainShopUrl(ctx);
        Assert.assertTrue("/gcpoi/pages/index?id=0".equals(result));
    }
}
