package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.metadataeav.process.api.metadatamodel.MetaDataModelLoadService;
import com.sankuai.metadataeav.process.dto.metadatamodel.LoadMetaDataModelReqDTO;
import com.sankuai.metadataeav.process.dto.metadatamodel.LoadMetaDataModelRespDTO;
import com.sankuai.metadataeav.process.dto.metadatamodel.MetaDataModelDTO;
import com.sankuai.metadataeav.process.dto.metadatamodel.MetaDataAttributeDTO;
import com.sankuai.metadataeav.process.dto.metadatamodel.MetaDataAttributeOptionDTO;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import static org.mockito.ArgumentMatchers.any;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class ImmersiveImageWrapper_GetStyleMetaDataTest {

    @InjectMocks
    private ImmersiveImageWrapper immersiveImageWrapper;

    @Mock
    private MetaDataModelLoadService metaDataModelLoadService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetStyleMetaData_EmptyTagFields() throws Throwable {
        // arrange
        List<String> tagFields = Collections.emptyList();
        // act
        Map<Integer, String> result = immersiveImageWrapper.getStyleMetaData(tagFields);
        // assert
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testGetStyleMetaData_TagFieldsNotInAttributeList() throws Throwable {
        // arrange
        List<String> tagFields = Arrays.asList("theme", "color");
        LoadMetaDataModelRespDTO respDTO = new LoadMetaDataModelRespDTO();
        MetaDataModelDTO metaDataModelDTO = new MetaDataModelDTO();
        metaDataModelDTO.setAttributeList(Collections.singletonList(new MetaDataAttributeDTO()));
        respDTO.setData(metaDataModelDTO);
        when(metaDataModelLoadService.loadMetaDataModel(any())).thenReturn(respDTO);
        // act
        Map<Integer, String> result = immersiveImageWrapper.getStyleMetaData(tagFields);
        // assert
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testGetStyleMetaData_TagFieldsInAttributeListButNotInAttributeOptionList() throws Throwable {
        // arrange
        List<String> tagFields = Arrays.asList("theme", "color");
        LoadMetaDataModelRespDTO respDTO = new LoadMetaDataModelRespDTO();
        MetaDataModelDTO metaDataModelDTO = new MetaDataModelDTO();
        MetaDataAttributeDTO metaDataAttributeDTO1 = new MetaDataAttributeDTO();
        metaDataAttributeDTO1.setId("1");
        metaDataAttributeDTO1.setName("theme");
        MetaDataAttributeDTO metaDataAttributeDTO2 = new MetaDataAttributeDTO();
        metaDataAttributeDTO2.setId("2");
        metaDataAttributeDTO2.setName("color");
        metaDataModelDTO.setAttributeList(Arrays.asList(metaDataAttributeDTO1, metaDataAttributeDTO2));
        respDTO.setData(metaDataModelDTO);
        when(metaDataModelLoadService.loadMetaDataModel(any())).thenReturn(respDTO);
        // act
        Map<Integer, String> result = immersiveImageWrapper.getStyleMetaData(tagFields);
        // assert
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testGetStyleMetaData_TagFieldsInAttributeListAndInAttributeOptionList() throws Throwable {
        // arrange
        List<String> tagFields = Arrays.asList("theme", "color");
        LoadMetaDataModelRespDTO respDTO = new LoadMetaDataModelRespDTO();
        MetaDataModelDTO metaDataModelDTO = new MetaDataModelDTO();
        MetaDataAttributeDTO metaDataAttributeDTO1 = new MetaDataAttributeDTO();
        metaDataAttributeDTO1.setId("1");
        metaDataAttributeDTO1.setName("theme");
        MetaDataAttributeDTO metaDataAttributeDTO2 = new MetaDataAttributeDTO();
        metaDataAttributeDTO2.setId("2");
        metaDataAttributeDTO2.setName("color");
        MetaDataAttributeOptionDTO metaDataAttributeOptionDTO1 = new MetaDataAttributeOptionDTO();
        metaDataAttributeOptionDTO1.setAttributeId(1);
        metaDataAttributeOptionDTO1.setValue(2023);
        metaDataAttributeOptionDTO1.setLabel("秋冬美甲");
        MetaDataAttributeOptionDTO metaDataAttributeOptionDTO2 = new MetaDataAttributeOptionDTO();
        metaDataAttributeOptionDTO2.setAttributeId(2);
        metaDataAttributeOptionDTO2.setValue(2020);
        metaDataAttributeOptionDTO2.setLabel("红色");
        metaDataModelDTO.setAttributeList(Arrays.asList(metaDataAttributeDTO1, metaDataAttributeDTO2));
        metaDataModelDTO.setAttributeOptionList(Arrays.asList(metaDataAttributeOptionDTO1, metaDataAttributeOptionDTO2));
        respDTO.setData(metaDataModelDTO);
        when(metaDataModelLoadService.loadMetaDataModel(any())).thenReturn(respDTO);
        // act
        Map<Integer, String> result = immersiveImageWrapper.getStyleMetaData(tagFields);
        // assert
        assertEquals(2, result.size());
        assertEquals("秋冬美甲", result.get(2023));
        assertEquals("红色", result.get(2020));
    }

    @Test
    public void testGetStyleMetaData_LoadMetaDataModelRespDTOIsNull() throws Throwable {
        // arrange
        List<String> tagFields = Arrays.asList("theme", "color");
        when(metaDataModelLoadService.loadMetaDataModel(any())).thenReturn(null);
        // act
        Map<Integer, String> result = immersiveImageWrapper.getStyleMetaData(tagFields);
        // assert
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testGetStyleMetaData_DataIsNull() throws Throwable {
        // arrange
        List<String> tagFields = Arrays.asList("theme", "color");
        LoadMetaDataModelRespDTO respDTO = new LoadMetaDataModelRespDTO();
        when(metaDataModelLoadService.loadMetaDataModel(any())).thenReturn(respDTO);
        // act
        Map<Integer, String> result = immersiveImageWrapper.getStyleMetaData(tagFields);
        // assert
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testGetStyleMetaData_AttributeListOrAttributeOptionListIsEmpty() throws Throwable {
        // arrange
        List<String> tagFields = Arrays.asList("theme", "color");
        LoadMetaDataModelRespDTO respDTO = new LoadMetaDataModelRespDTO();
        MetaDataModelDTO metaDataModelDTO = new MetaDataModelDTO();
        metaDataModelDTO.setAttributeList(Collections.emptyList());
        metaDataModelDTO.setAttributeOptionList(Collections.emptyList());
        respDTO.setData(metaDataModelDTO);
        when(metaDataModelLoadService.loadMetaDataModel(any())).thenReturn(respDTO);
        // act
        Map<Integer, String> result = immersiveImageWrapper.getStyleMetaData(tagFields);
        // assert
        assertEquals(Collections.emptyMap(), result);
    }
}
