package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ReviewWrapper;
import com.dianping.ugc.pic.remote.service.MtReviewPicService;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Future;
import static org.mockito.ArgumentMatchers.anyList;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class ReviewWrapper_GetMtReviewPicFutureTest {

    @InjectMocks
    private ReviewWrapper reviewWrapper;

    @Mock
    private MtReviewPicService mtReviewPicServiceFuture;

    @Mock
    private Future mockFuture;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetMtReviewPicFuturePicIdsIsNull() throws Throwable {
        List<Long> picIds = null;
        Future result = reviewWrapper.getMtReviewPicFuture(picIds);
        assertNull(result);
    }

    @Test
    public void testGetMtReviewPicFutureNormal() throws Throwable {
        List<Long> picIds = Arrays.asList(1L, 2L, 3L);
        when(mtReviewPicServiceFuture.getMtReviewPicInfoList(anyList())).thenReturn(null);
        try (MockedStatic<FutureFactory> mockedStatic = Mockito.mockStatic(FutureFactory.class)) {
            mockedStatic.when(FutureFactory::getFuture).thenReturn(mockFuture);
            Future result = reviewWrapper.getMtReviewPicFuture(picIds);
            assertNotNull(result);
            verify(mtReviewPicServiceFuture, times(1)).getMtReviewPicInfoList(picIds);
        }
    }

    @Test
    public void testGetMtReviewPicFutureException() throws Throwable {
        List<Long> picIds = Arrays.asList(1L, 2L, 3L);
        doThrow(new RuntimeException()).when(mtReviewPicServiceFuture).getMtReviewPicInfoList(picIds);
        Future result = reviewWrapper.getMtReviewPicFuture(picIds);
        assertNull(result);
        verify(mtReviewPicServiceFuture, times(1)).getMtReviewPicInfoList(picIds);
    }
}
