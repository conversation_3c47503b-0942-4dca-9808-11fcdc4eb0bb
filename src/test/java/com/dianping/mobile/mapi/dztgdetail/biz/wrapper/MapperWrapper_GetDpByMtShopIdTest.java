package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.poi.relation.service.api.PoiRelationService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.doAnswer;

public class MapperWrapper_GetDpByMtShopIdTest {

    @InjectMocks
    private MapperWrapper mapperWrapper;

    @Mock
    private PoiRelationService poiRelationService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetDpByMtShopIdNull() throws Throwable {
        assertNull(mapperWrapper.getDpByMtShopId(null));
    }

    @Test
    public void testGetDpByMtShopIdNegative() throws Throwable {
        assertNull(mapperWrapper.getDpByMtShopId(-1L));
    }

    @Test
    public void testGetDpByMtShopIdEmptyList() throws Throwable {
        doAnswer(invocation -> Collections.emptyList()).when(poiRelationService).queryDpByMtIdL(anyLong());
        assertNull(mapperWrapper.getDpByMtShopId(1L));
    }

    @Test
    public void testGetDpByMtShopIdNotEmptyList() throws Throwable {
        doAnswer(invocation -> Arrays.asList(1L, 2L)).when(poiRelationService).queryDpByMtIdL(anyLong());
        assertEquals(Long.valueOf(1L), mapperWrapper.getDpByMtShopId(1L));
    }
}
