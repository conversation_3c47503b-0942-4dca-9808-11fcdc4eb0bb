package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MiniProgramSceneEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PinPoolProcessorTest {

    @InjectMocks
    private PinPoolProcessor pinPoolProcessor;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private EnvCtx envCtx;

    @Before
    public void setUp() {
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
    }

    /**
     * Test case: isEnable should return true when it's main app and not external
     * Expected: return true
     */
    @Test
    public void testIsEnable_MainAppAndNotExternal_ReturnTrue() {
        // arrange
        when(envCtx.isMainApp()).thenReturn(true);
        when(dealCtx.isExternal()).thenReturn(false);
        // act
        boolean result = pinPoolProcessor.isEnable(dealCtx);
        // assert
        assertTrue(result);
    }

    /**
     * Test case: isEnable should return false when it's main app but external
     * Expected: return false
     */
    @Test
    public void testIsEnable_MainAppButExternal_ReturnFalse() {
        // arrange
        when(envCtx.isMainApp()).thenReturn(true);
        when(dealCtx.isExternal()).thenReturn(true);
        when(envCtx.isExternalAndEnabled(MiniProgramSceneEnum.PINTUAN.getPromoScene())).thenReturn(false);
        // act
        boolean result = pinPoolProcessor.isEnable(dealCtx);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: isEnable should return true when external and enabled for pintuan scene
     * Expected: return true
     */
    @Test
    public void testIsEnable_ExternalAndEnabled_ReturnTrue() {
        // arrange
        when(envCtx.isMainApp()).thenReturn(false);
        when(envCtx.isExternalAndEnabled(MiniProgramSceneEnum.PINTUAN.getPromoScene())).thenReturn(true);
        // act
        boolean result = pinPoolProcessor.isEnable(dealCtx);
        // assert
        assertTrue(result);
    }

    /**
     * Test case: isEnable should return false when not main app and external not enabled
     * Expected: return false
     */
    @Test
    public void testIsEnable_NotMainAppAndExternalNotEnabled_ReturnFalse() {
        // arrange
        when(envCtx.isMainApp()).thenReturn(false);
        when(envCtx.isExternalAndEnabled(MiniProgramSceneEnum.PINTUAN.getPromoScene())).thenReturn(false);
        // act
        boolean result = pinPoolProcessor.isEnable(dealCtx);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: isEnable should return false when not main app and not external
     * Expected: return false
     */
    @Test
    public void testIsEnable_NotMainAppAndNotExternal_ReturnFalse() {
        // arrange
        when(envCtx.isMainApp()).thenReturn(false);
        when(envCtx.isExternalAndEnabled(MiniProgramSceneEnum.PINTUAN.getPromoScene())).thenReturn(false);
        // act
        boolean result = pinPoolProcessor.isEnable(dealCtx);
        // assert
        assertFalse(result);
    }
}
