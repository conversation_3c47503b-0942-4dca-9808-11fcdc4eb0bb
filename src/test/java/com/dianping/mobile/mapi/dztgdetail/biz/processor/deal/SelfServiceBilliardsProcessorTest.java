package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.joygeneral.api.thirdpart.SelfHelpBilliardService;
import com.dianping.joygeneral.api.thirdpart.dto.QueryAutoOpenTableReqDTO;
import com.dianping.joygeneral.api.thirdpart.dto.Response;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.NewBuyBarHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.PriceProtectionHelper;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.EduDealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.TimesDealUtil;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.dianping.vc.sdk.concurrent.future.FutureUtils;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupCustomerDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.jetbrains.annotations.NotNull;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.*;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.powermock.api.mockito.PowerMockito.doNothing;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/9/26 14:25
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({FutureFactory.class,Lion.class, LionConfigUtils.class, DealBuyHelper.class, EduDealUtils.class, DealUtils.class, PriceProtectionHelper.class, NewBuyBarHelper.class, TimesDealUtil.class})
@SuppressStaticInitializationFor("com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils")
public class SelfServiceBilliardsProcessorTest {

    @InjectMocks
    private SelfServiceBilliardsProcessor selfServiceBilliardsProcessor;

    @Mock
    private SelfHelpBilliardService selfHelpBilliardServiceFuture;

    private DealCtx ctx;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(Lion.class);
        PowerMockito.mockStatic(FutureFactory.class);
        ctx = buildDealCtx();
    }

    @Test
    public void testProcess() {
        Response<Boolean> success = (Response<Boolean>) Response.success(Boolean.TRUE);
        ctx.getFutureCtx().setAutoOpenTableFuture(CompletableFuture.completedFuture(success));
        selfServiceBilliardsProcessor.process(ctx);
        assert ctx.getAutoOpenTable();
    }

    @Test
    public void testPrepare() {
        Response<Boolean> success = (Response<Boolean>) Response.success(Boolean.TRUE);
        when(selfHelpBilliardServiceFuture.queryAutoOpenTable(any(QueryAutoOpenTableReqDTO.class))).thenAnswer((Answer<Response<Boolean>>) invocation -> success);
        when(FutureFactory.getFuture()).thenAnswer((Answer<Future<Response<Boolean>>>) invocation -> CompletableFuture
                .completedFuture(success));
        selfServiceBilliardsProcessor.prepare(ctx);
        Future<?> autoOpenTableFuture = ctx.getFutureCtx().getAutoOpenTableFuture();
        assert autoOpenTableFuture != null;
    }

    @NotNull
    private DealCtx buildDealCtx() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        // ctx.setCustomerId(12345);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCustomerDTO customerDTO = new DealGroupCustomerDTO();
        customerDTO.setOriginCustomerId(12345L);
        dealGroupDTO.setCustomer(customerDTO);
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.setDpLongShopId(123456L);
        ctx.setMtLongShopId(123456L);
        return ctx;
    }

    @Test
    public void testEnable() {
        when(Lion.getList(APP_KEY, SELF_AUTO_OPEN_TABLE_CATEGORY, Integer.class, Collections.emptyList())).thenAnswer((Answer<List<Integer>>) invocation -> Lists.newArrayList(1805));
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(1805);
        ctx.setChannelDTO(channelDTO);
        boolean enable = selfServiceBilliardsProcessor.isEnable(ctx);
        assert enable;
    }

}