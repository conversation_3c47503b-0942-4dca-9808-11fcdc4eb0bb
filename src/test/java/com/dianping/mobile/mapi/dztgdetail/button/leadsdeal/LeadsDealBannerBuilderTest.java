package com.dianping.mobile.mapi.dztgdetail.button.leadsdeal;

import com.alibaba.fastjson.JSON;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBanner;
import com.dianping.mobile.mapi.dztgdetail.entity.LeadsDealBarConfig;
import com.dianping.mobile.mapi.dztgdetail.util.DealUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.google.common.collect.Lists;
import com.sankuai.clr.content.process.gateway.thrift.dto.leads.LoadLeadsInfoRespDTO;
import com.sankuai.clr.content.process.thrift.dto.leads.dto.LeadsPromotionDTO;
import com.sankuai.clr.content.process.thrift.dto.leads.dto.LeadsSalesDTO;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

/**
 * @author: wuwenqiang
 * @create: 2024-09-23
 * @description:
 */
@RunWith(MockitoJUnitRunner.class)
public class LeadsDealBannerBuilderTest {

    @Mock
    private ButtonBuilderChain chain;

    @InjectMocks
    private LeadsDealBannerBuilder leadsDealBannerBuilder;

    private MockedStatic<LionConfigUtils> mockedLionConfigUtils;
    private MockedStatic<DealUtils> mockedDealUtils;

    @Before
    public void setUp() {
        mockedLionConfigUtils = Mockito.mockStatic(LionConfigUtils.class);
        mockedDealUtils = Mockito.mockStatic(DealUtils.class);
    }

    @After
    public void after() {
        mockedLionConfigUtils.close();
        mockedDealUtils.close();
    }

    /**
     * 测试底bar配置为空
     */
    @Test
    public void testBuild_ConfigIsNull() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx dealCtx = new DealCtx(envCtx);
        mockedLionConfigUtils.when(() -> LionConfigUtils.getLeadsDealBarConfig(dealCtx)).thenReturn(null);

        // act
        leadsDealBannerBuilder.doBuild(dealCtx, chain);

        // assert
        assertNull(dealCtx.getBuyBar().getBuyBanner());
    }

    /**
     * 测试有商家预约权益且有预约礼，同时存在礼的名称和价格
     */
    @Test
    public void testBuild_WithResvAndGift_WithGiftNameAndPrice() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx dealCtx = new DealCtx(envCtx);
        String configStr = "{\"bannerBackGroundColor\":\"#FFF1EC\",\"bannerIcon\":\"https://p0.meituan.net/dztgdetailimages/427d9c96dd426b065da5b2e28db51ddd620.png\",\"bannerTextColor\":\"#FF4B10\",\"bannerTextSize\":11,\"newLeadsGiftsTemplate\":\"预约到店赠送价值%s元%s\",\"oldLeadsGiftsTemplate\":\"预约到店赠送%s\"}";
        LeadsDealBarConfig leadsDealBarConfig = JSON.parseObject(configStr, LeadsDealBarConfig.class);
        mockedLionConfigUtils.when(() -> LionConfigUtils.getLeadsDealBarConfig(dealCtx)).thenReturn(leadsDealBarConfig);
        mockedDealUtils.when(() -> DealUtils.hasResvBenefits(dealCtx)).thenReturn(true);

        LoadLeadsInfoRespDTO leadsInfo = new LoadLeadsInfoRespDTO();
        LeadsPromotionDTO promotionDTO = new LeadsPromotionDTO();
        LeadsSalesDTO salesDTO = new LeadsSalesDTO();
        salesDTO.setGiftKey("预约礼");
        salesDTO.setGiftName("礼品");
        salesDTO.setGiftPrice("100");
        LeadsSalesDTO salesDTO2 = new LeadsSalesDTO();
        salesDTO2.setGiftKey("到店礼");
        salesDTO2.setGiftName("礼品2");
        salesDTO2.setGiftPrice("200");
        promotionDTO.setSales(Lists.newArrayList(salesDTO, salesDTO2));
        leadsInfo.setPromotions(Lists.newArrayList(promotionDTO));
        dealCtx.setLeadsInfo(leadsInfo);

        // act
        leadsDealBannerBuilder.doBuild(dealCtx, chain);

        // assert
        DealBuyBanner banner = dealCtx.getBuyBar().getBuyBanner();
        assertEquals("https://p0.meituan.net/dztgdetailimages/427d9c96dd426b065da5b2e28db51ddd620.png", banner.getIconUrl());
        assertTrue(banner.getContent().contains("预约到店赠送价值200元礼品2"));
    }

    /**
     * 测试有商家预约权益且有预约礼，仅存在礼的名称
     */
    @Test
    public void testBuild_WithResvAndGift_WithGiftName() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx dealCtx = new DealCtx(envCtx);
        String configStr = "{\"bannerBackGroundColor\":\"#FFF1EC\",\"bannerIcon\":\"https://p0.meituan.net/dztgdetailimages/427d9c96dd426b065da5b2e28db51ddd620.png\",\"bannerTextColor\":\"#FF4B10\",\"bannerTextSize\":11,\"newLeadsGiftsTemplate\":\"预约到店赠送价值%s元%s\",\"oldLeadsGiftsTemplate\":\"预约到店赠送%s\"}";
        LeadsDealBarConfig leadsDealBarConfig = JSON.parseObject(configStr, LeadsDealBarConfig.class);
        mockedLionConfigUtils.when(() -> LionConfigUtils.getLeadsDealBarConfig(dealCtx)).thenReturn(leadsDealBarConfig);
        mockedDealUtils.when(() -> DealUtils.hasResvBenefits(dealCtx)).thenReturn(true);

        LoadLeadsInfoRespDTO leadsInfo = new LoadLeadsInfoRespDTO();
        LeadsPromotionDTO promotionDTO = new LeadsPromotionDTO();
        LeadsSalesDTO salesDTO = new LeadsSalesDTO();
        salesDTO.setGiftKey("到店礼");
        salesDTO.setGiftName("礼品");
        promotionDTO.setSales(Lists.newArrayList(salesDTO));
        leadsInfo.setPromotions(Lists.newArrayList(promotionDTO));
        dealCtx.setLeadsInfo(leadsInfo);

        // act
        leadsDealBannerBuilder.doBuild(dealCtx, chain);

        // assert
        DealBuyBanner banner = dealCtx.getBuyBar().getBuyBanner();
        assertEquals("https://p0.meituan.net/dztgdetailimages/427d9c96dd426b065da5b2e28db51ddd620.png", banner.getIconUrl());
        assertTrue(banner.getContent().contains("预约到店赠送礼品"));
    }

    /**
     * 测试有商家预约权益但无预约礼
     */
    @Test
    public void testBuild_WithResvNoGift() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx dealCtx = new DealCtx(envCtx);
        String configStr = "{\"bannerBackGroundColor\":\"#FFF1EC\",\"bannerIcon\":\"https://p0.meituan.net/dztgdetailimages/427d9c96dd426b065da5b2e28db51ddd620.png\",\"bannerTextColor\":\"#FF4B10\",\"bannerTextSize\":11,\"newLeadsGiftsTemplate\":\"预约到店赠送价值%s元%s\",\"oldLeadsGiftsTemplate\":\"预约到店赠送%s\"}";
        LeadsDealBarConfig leadsDealBarConfig = JSON.parseObject(configStr, LeadsDealBarConfig.class);
        mockedLionConfigUtils.when(() -> LionConfigUtils.getLeadsDealBarConfig(dealCtx)).thenReturn(leadsDealBarConfig);
        mockedDealUtils.when(() -> DealUtils.hasResvBenefits(dealCtx)).thenReturn(true);

        LoadLeadsInfoRespDTO leadsInfo = new LoadLeadsInfoRespDTO();
        dealCtx.setLeadsInfo(leadsInfo);

        // act
        leadsDealBannerBuilder.doBuild(dealCtx, chain);

        // assert
        assertNull(dealCtx.getBuyBar().getBuyBanner());
    }

    /**
     * 测试无商家预约权益
     */
    @Test
    public void testBuild_NoResv() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx dealCtx = new DealCtx(envCtx);
        String configStr = "{\"bannerBackGroundColor\":\"#FFF1EC\",\"bannerIcon\":\"https://p0.meituan.net/dztgdetailimages/427d9c96dd426b065da5b2e28db51ddd620.png\",\"bannerTextColor\":\"#FF4B10\",\"bannerTextSize\":11,\"newLeadsGiftsTemplate\":\"预约到店赠送价值%s元%s\",\"oldLeadsGiftsTemplate\":\"预约到店赠送%s\"}";
        LeadsDealBarConfig leadsDealBarConfig = JSON.parseObject(configStr, LeadsDealBarConfig.class);
        mockedLionConfigUtils.when(() -> LionConfigUtils.getLeadsDealBarConfig(dealCtx)).thenReturn(leadsDealBarConfig);
        mockedDealUtils.when(() -> DealUtils.hasResvBenefits(dealCtx)).thenReturn(false);

        // act
        leadsDealBannerBuilder.doBuild(dealCtx, chain);

        // assert
        assertNull(dealCtx.getBuyBar().getBuyBanner());
    }


}
