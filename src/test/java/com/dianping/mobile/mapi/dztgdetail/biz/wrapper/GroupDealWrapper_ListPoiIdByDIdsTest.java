package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.meituan.service.mobile.message.group.deal.PoiidListRequestMsg;
import com.meituan.service.mobile.message.group.deal.PoiidListResponseMsg;
import com.meituan.service.mobile.message.group.deal.RPCGroupDealService;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class GroupDealWrapper_ListPoiIdByDIdsTest {

    @InjectMocks
    private GroupDealWrapper groupDealWrapper;

    @Mock
    private RPCGroupDealService.Iface rpcGroupDealService;

    private PoiidListRequestMsg req;

    @Before
    public void setUp() {
        req = new PoiidListRequestMsg();
    }

    @Test
    public void testListPoiIdByDIdsWhenResponseIsNull() throws TException {
        when(rpcGroupDealService.listPoiidByDids(req)).thenReturn(null);
        Map<Integer, List<Long>> result = groupDealWrapper.listPoiIdByDIds(req);
        assertEquals(new HashMap<>(), result);
    }

    @Test
    public void testListPoiIdByDIdsWhenResponseMapIsEmpty() throws TException {
        PoiidListResponseMsg resp = new PoiidListResponseMsg();
        when(rpcGroupDealService.listPoiidByDids(req)).thenReturn(resp);
        Map<Integer, List<Long>> result = groupDealWrapper.listPoiIdByDIds(req);
        assertEquals(new HashMap<>(), result);
    }

    @Test
    public void testListPoiIdByDIdsWhenResponseMapValueIsNull() throws TException {
        PoiidListResponseMsg resp = new PoiidListResponseMsg();
        resp.setDid2poiidMap(new HashMap<>());
        resp.getDid2poiidMap().put(1, null);
        when(rpcGroupDealService.listPoiidByDids(req)).thenReturn(resp);
        Map<Integer, List<Long>> result = groupDealWrapper.listPoiIdByDIds(req);
        assertEquals(Collections.singletonMap(1, Collections.emptyList()), result);
    }

    @Test
    public void testListPoiIdByDIdsWhenResponseMapValueIsNotNull() throws TException {
        PoiidListResponseMsg resp = new PoiidListResponseMsg();
        Map<Integer, List<Integer>> did2poiidMap = new HashMap<>();
        did2poiidMap.put(1, Collections.singletonList(1));
        resp.setDid2poiidMap(did2poiidMap);
        when(rpcGroupDealService.listPoiidByDids(req)).thenReturn(resp);
        Map<Integer, List<Long>> result = groupDealWrapper.listPoiIdByDIds(req);
        assertEquals(Collections.singletonMap(1, Collections.singletonList(1L)), result);
    }

    @Test
    public void testListPoiIdByDIdsWhenExceptionOccurs() throws TException {
        when(rpcGroupDealService.listPoiidByDids(req)).thenThrow(TException.class);
        Map<Integer, List<Long>> result = groupDealWrapper.listPoiIdByDIds(req);
        assertEquals(new HashMap<>(), result);
    }
}
