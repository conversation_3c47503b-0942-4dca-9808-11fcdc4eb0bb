package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

/**
 * DealBaseReq类的getExtParam方法测试
 */
@RunWith(MockitoJUnitRunner.class)
public class DealBaseReqTest {

    @InjectMocks
    private DealBaseReq dealBaseReq;

    @Before
    public void setUp() {
    }

    /**
     * 测试getExtParam方法，当extParam不为"undefined"时
     */
    @Test
    public void testGetExtParamWhenNotUndefined() {
        // arrange
        String expected = "testParam";
        dealBaseReq.setExtParam(expected);

        // act
        String actual = dealBaseReq.getExtParam();

        // assert
        assertEquals("当extParam不为'undefined'时，应返回设置的值", expected, actual);
    }

    /**
     * 测试getExtParam方法，当extParam为"undefined"时
     */
    @Test
    public void testGetExtParamWhenUndefined() {
        // arrange
        dealBaseReq.setExtParam("undefined");

        // act
        String actual = dealBaseReq.getExtParam();

        // assert
        assertEquals("当extParam为'undefined'时，应返回空字符串", "", actual);
    }

    /**
     * 测试getExtParam方法，当extParam为null时
     */
    @Test
    public void testGetExtParamWhenNull() {
        // arrange
        dealBaseReq.setExtParam(null);

        // act
        String actual = dealBaseReq.getExtParam();

        // assert
        assertNull("当extParam为null时，应返回null", actual);
    }

    /**
     * 测试getDealparam方法，当dealparam为"undefined"时，应返回空字符串
     */
    @Test
    public void testGetDealparamWhenUndefined() {
        // arrange
        dealBaseReq.setDealparam("undefined");

        // act
        String result = dealBaseReq.getDealParam();

        // assert
        assertEquals("当dealparam为'undefined'时，应返回空字符串", StringUtils.EMPTY, result);
    }

    /**
     * 测试getDealparam方法，当dealparam不为"undefined"时，应返回原值
     */
    @Test
    public void testGetDealparamWhenNotUndefined() {
        // arrange
        String expected = "someValue";
        dealBaseReq.setDealparam(expected);

        // act
        String result = dealBaseReq.getDealParam();

        // assert
        assertEquals("当dealparam不为'undefined'时，应返回原值", expected, result);
    }

    /**
     * 测试getDealparam方法，当dealparam为null时，应返回null
     */
    @Test
    public void testGetDealparamWhenNull() {
        // arrange
        dealBaseReq.setDealparam(null);

        // act
        String result = dealBaseReq.getDealParam();

        // assert
        assertNull("当dealparam为null时，应返回null", result);
    }

    /**
     * 测试getPass_param方法，当pass_param为"undefined"时，应返回空字符串
     */
    @Test
    public void testGetPass_paramWhenUndefined() {
        // arrange
        dealBaseReq.setPass_param("undefined");

        // act
        String result = dealBaseReq.getPass_param();

        // assert
        assertEquals("当pass_param为'undefined'时，应返回空字符串", StringUtils.EMPTY, result);
    }

    /**
     * 测试getPass_param方法，当pass_param为非"undefined"时，应返回原值
     */
    @Test
    public void testGetPass_paramWhenNotUndefined() {
        // arrange
        String expected = "someValue";
        dealBaseReq.setPass_param(expected);

        // act
        String result = dealBaseReq.getPass_param();

        // assert
        assertEquals("当pass_param为非'undefined'时，应返回原值", expected, result);
    }

    /**
     * 测试getPass_param方法，当pass_param为null时，应返回空字符串
     */
    @Test
    public void testGetPass_paramWhenNull() {
        // arrange
        dealBaseReq.setPass_param(null);

        // act
        String result = dealBaseReq.getPass_param();

        // assert
        assertNull("当pass_param为null时，应返回空字符串", result);
    }
}
