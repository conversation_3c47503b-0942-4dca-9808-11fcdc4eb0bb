package com.dianping.mobile.mapi.dztgdetail.biz.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.service.bo.DrivingPoi;
import java.lang.reflect.Method;
import java.util.Arrays;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DrivingPoiServiceEnableDrivingShopTest {

    private DrivingPoiService drivingPoiService = new DrivingPoiService();

    /**
     * Helper method to invoke the private enableDrivingShop method using reflection.
     */
    private boolean invokeEnableDrivingShop(DrivingPoi req) throws Exception {
        Method method = DrivingPoiService.class.getDeclaredMethod("enableDrivingShop", DrivingPoi.class);
        method.setAccessible(true);
        return (boolean) method.invoke(drivingPoiService, req);
    }

    /**
     * 测试 publishCategory 不等于404 的情况
     */
    @Test
    public void testEnableDrivingShop_PublishCategoryNot404() throws Throwable {
        DrivingPoi req = DrivingPoi.builder().publishCategory(403).build();
        assertFalse(invokeEnableDrivingShop(req));
    }

    /**
     * 测试 publishCategory 等于404，但 serviceTypeAttr 为空的情况
     */
    @Test
    public void testEnableDrivingShop_ServiceTypeAttrEmpty() throws Throwable {
        DrivingPoi req = DrivingPoi.builder().publishCategory(404).build();
        assertFalse(invokeEnableDrivingShop(req));
    }

    /**
     * 测试 publishCategory 等于404，serviceTypeAttr 不为空但不包含"小车"的情况
     */
    @Test
    public void testEnableDrivingShop_ServiceTypeAttrNotContainsCar() throws Throwable {
        DrivingPoi req = DrivingPoi.builder().publishCategory(404).serviceTypeAttr(Arrays.asList("大车")).build();
        assertFalse(invokeEnableDrivingShop(req));
    }

    /**
     * 测试 publishCategory 等于404，serviceTypeAttr 不为空且包含"小车"的情况
     */
    @Test
    public void testEnableDrivingShop_ServiceTypeAttrContainsCar() throws Throwable {
        DrivingPoi req = DrivingPoi.builder().publishCategory(404).serviceTypeAttr(Arrays.asList("小车")).build();
        assertTrue(invokeEnableDrivingShop(req));
    }
}
