package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.poi.relation.service.api.PoiRelationService;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class MapperWrapper_GetDpShopIdByMtShopIdTest {

    @InjectMocks
    private MapperWrapper mapperWrapper;

    @Mock
    private PoiRelationService poiRelationService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 getDpShopIdByMtShopId 方法，当 preDpShopIdByMtShopId 返回 null 时
     */
    @Test
    public void testGetDpShopIdByMtShopIdWhenPreDpShopIdByMtShopIdReturnNull() throws Throwable {
        int mtShopId = 1;
        when(poiRelationService.queryDpByMtIdL((long) mtShopId)).thenReturn(null);
        int result = mapperWrapper.getDpShopIdByMtShopId(mtShopId);
        assertEquals(0, result);
    }
}
