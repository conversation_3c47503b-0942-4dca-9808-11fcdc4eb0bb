package com.dianping.mobile.mapi.dztgdetail.biz.processor;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.MemberExclusiveProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.UnifiedModuleProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MemberExclusiveWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.when;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2024/7/2
 * @since mapi-dztgdetail-web
 */
@RunWith(MockitoJUnitRunner.class)
public class MemberExclusiveProcessorTest {

    @InjectMocks
    MemberExclusiveProcessor memberExclusiveProcessor;

    @Mock
    private MemberExclusiveWrapper memberExclusiveWrapper;

    @Test
    public void getMemberExclusiveTest() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        memberExclusiveProcessor.getMemberExclusive(ctx);
        Assert.assertNotNull(ctx);
    }



}
