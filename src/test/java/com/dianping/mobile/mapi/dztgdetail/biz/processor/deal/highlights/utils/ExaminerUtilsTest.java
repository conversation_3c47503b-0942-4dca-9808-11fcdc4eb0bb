package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.utils;

import static org.mockito.Mockito.*;
import com.sankuai.general.product.query.center.client.builder.model.*;
import com.sankuai.general.product.query.center.client.builder.requset.*;
import com.sankuai.general.product.query.center.client.enums.*;
import com.sankuai.general.product.query.center.client.request.*;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class ExaminerUtilsTest {

    /**
     * 测试getExaminerRequest方法，验证是否能正确构建QueryByDealGroupIdRequest对象
     */
    @Test
    public void testGetExaminerRequest() throws Throwable {
        // arrange
        int dealGroupId = 1;
        IdTypeEnum idTypeEnum = IdTypeEnum.DP;
        // act
        QueryByDealGroupIdRequest result = ExaminerUtils.getExaminerRequest(dealGroupId, idTypeEnum);
        // assert
        assertNotNull(result);
        assertEquals(dealGroupId, result.getDealGroupIds().iterator().next().intValue());
        assertEquals(Integer.valueOf(idTypeEnum.getCode()), result.getIdType());
    }
}
