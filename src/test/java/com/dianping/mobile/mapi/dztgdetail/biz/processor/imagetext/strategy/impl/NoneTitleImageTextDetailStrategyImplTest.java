package com.dianping.mobile.mapi.dztgdetail.biz.processor.imagetext.strategy.impl;

import com.dianping.mobile.mapi.dztgdetail.common.enums.ImageTextStrategyEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ContentDetailPBO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertNotNull;

@RunWith(MockitoJUnitRunner.class)
public class NoneTitleImageTextDetailStrategyImplTest {

    @InjectMocks
    private NoneTitleImageTextDetailStrategyImpl noneTitleImageTextDetailStrategyImpl;

    @Mock
    private DealGroupDTO mockDealGroupDTO;

    @Before
    public void setUp() {
    }

    /**
     * 测试getStrategyName方法，期望返回NONE_TITLE策略
     */
    @Test
    public void testGetStrategyNameExpectNone() {
        // arrange (此处无需特别安排)

        // act
        ImageTextStrategyEnum result = noneTitleImageTextDetailStrategyImpl.getStrategyName();

        // assert
        Assert.assertEquals("测试getStrategyName方法，期望返回NONE_TITLE策略", ImageTextStrategyEnum.NONE_TITLE, result);
    }

    /**
     * 测试 getContentDetail 方法，当传入的参数符合预期时，应返回非空的 ContentDetailPBO 对象
     */
    @Test
    public void testGetContentDetailReturnsNonNullContentDetailPBO() {
        // arrange
        List<ContentPBO> contents = new ArrayList<>();
        int foldThreshold = 2;

        // act
        ContentDetailPBO result = noneTitleImageTextDetailStrategyImpl.getContentDetail(mockDealGroupDTO, contents, foldThreshold);

        // assert
        assertNotNull("The result should not be null.", result);
    }


    /**
     * 测试 getContentDetail 方法，当传入的 contents 为空列表时，应返回非空的 ContentDetailPBO 对象
     */
    @Test
    public void testGetContentDetailWithEmptyContentsReturnsNonNullContentDetailPBO() {
        // arrange
        List<ContentPBO> contents = new ArrayList<>();
        int foldThreshold = 2;

        // act
        ContentDetailPBO result = noneTitleImageTextDetailStrategyImpl.getContentDetail(mockDealGroupDTO, contents, foldThreshold);

        // assert
        assertNotNull("The result should not be null when contents is empty.", result);
    }

    /**
     * 测试 getContentDetail 方法，当 foldThreshold 为负数时，应返回非空的 ContentDetailPBO 对象
     */
    @Test
    public void testGetContentDetailWithNegativeFoldThresholdReturnsNonNullContentDetailPBO() {
        // arrange
        List<ContentPBO> contents = new ArrayList<>();
        int foldThreshold = -1;

        // act
        ContentDetailPBO result = noneTitleImageTextDetailStrategyImpl.getContentDetail(mockDealGroupDTO, contents, foldThreshold);

        // assert
        assertNotNull("The result should not be null when foldThreshold is negative.", result);
    }
}
