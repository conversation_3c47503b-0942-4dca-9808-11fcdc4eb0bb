package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.idmapper.api.DealIdMapperService;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.*;

public class DealGroupWrapper_PreDpDealGroupIdTest {

    @InjectMocks
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private DealIdMapperService dealIdMapperServiceFuture;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试mtId小于等于0的情况
     */
    @Test
    public void testPreDpDealGroupIdMtIdLessThanOrEqualToZero() {
        assertNull(dealGroupWrapper.preDpDealGroupId(0));
        assertNull(dealGroupWrapper.preDpDealGroupId(-1));
    }



    /**
     * 测试mtId大于0且queryByMtDealGroupId方法抛出异常的情况
     */
    @Test
    public void testPreDpDealGroupIdMtIdGreaterThanZeroAndQueryByMtDealGroupIdThrowsException() throws Exception {
        when(dealIdMapperServiceFuture.queryByMtDealGroupId(anyInt())).thenThrow(new RuntimeException());
        assertNull(dealGroupWrapper.preDpDealGroupId(1));
        verify(dealIdMapperServiceFuture, times(1)).queryByMtDealGroupId(anyInt());
    }
}
