package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.factory.impl;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.glasses.factory.impl.GlassesDurationStrategyImpl;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CommonAttrVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.highlights.DztgHighlightsModule;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GlassesDurationStrategyImplTest {

    @InjectMocks
    private GlassesDurationStrategyImpl glassesDurationStrategy;
    @Mock
    private DealGroupDTO dealGroupDTO;

    private DealCtx dealCtx;

    @Before
    public void setUp() throws Exception {

    }

    /**
     * 测试DealGroupDTO对象不为空，且包含配镜时长的属性，配镜时长大于等于20分钟的情况
     */
    @Test
    public void testBuildMoudle_NormalCase() throws Throwable {
        // arrange
        dealCtx = new DealCtx(new EnvCtx());
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("duration_examination");
        attrDTO.setValue(Arrays.asList("30"));
        dealCtx.setDealGroupDTO(dealGroupDTO);
        when(dealGroupDTO.getAttrs()).thenReturn(Arrays.asList(attrDTO));
        DztgHighlightsModule dztgHighlightsModule = new DztgHighlightsModule();
        dztgHighlightsModule.setAttrs(new ArrayList<CommonAttrVO>());
        dealCtx.setHighlightsModule(dztgHighlightsModule);

        // act
        glassesDurationStrategy.buildMoudle(dealCtx);

        // assert
        assertEquals(1, dztgHighlightsModule.getAttrs().size());
        CommonAttrVO commonAttrVO = dztgHighlightsModule.getAttrs().get(0);
        assertEquals("配镜时长", commonAttrVO.getName());
        assertEquals("30分钟", commonAttrVO.getValue());
    }

    /**
     * 测试DealGroupDTO对象为空的情况
     */
    @Test
    public void testBuildMoudle_DealGroupDTONull() throws Throwable {
        // arrange
        dealCtx = new DealCtx(new EnvCtx());
        AttrDTO attrDTO = new AttrDTO();
        dealCtx.setDealGroupDTO(null);
        DztgHighlightsModule dztgHighlightsModule = new DztgHighlightsModule();
        dztgHighlightsModule.setAttrs(new ArrayList<CommonAttrVO>());
        dealCtx.setHighlightsModule(dztgHighlightsModule);

        // act
        glassesDurationStrategy.buildMoudle(dealCtx);

        // assert
        assertEquals(0, dztgHighlightsModule.getAttrs().size());
    }

    /**
     * 测试DealGroupDTO对象不包含配镜时长的属性的情况
     */
    @Test
    public void testBuildMoudle_NoGlassesDurationAttr() throws Throwable {
        // arrange
        dealCtx = new DealCtx(new EnvCtx());
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("other_attr");
        attrDTO.setValue(Arrays.asList("other"));
        dealCtx.setDealGroupDTO(dealGroupDTO);
        when(dealGroupDTO.getAttrs()).thenReturn(Arrays.asList(attrDTO));
        DztgHighlightsModule dztgHighlightsModule = new DztgHighlightsModule();
        dztgHighlightsModule.setAttrs(new ArrayList<CommonAttrVO>());
        dealCtx.setHighlightsModule(dztgHighlightsModule);

        // act
        glassesDurationStrategy.buildMoudle(dealCtx);

        // assert
        assertEquals(0, dztgHighlightsModule.getAttrs().size());
    }

    /**
     * 测试配镜时长小于20分钟的情况
     */
    @Test
    public void testBuildMoudle_GlassesDurationLessThan20() throws Throwable {
        // arrange
        dealCtx = new DealCtx(new EnvCtx());
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("whether_pickup");
        attrDTO.setValue(Arrays.asList("10"));
        dealCtx.setDealGroupDTO(dealGroupDTO);
        when(dealGroupDTO.getAttrs()).thenReturn(Arrays.asList(attrDTO));
        DztgHighlightsModule dztgHighlightsModule = new DztgHighlightsModule();
        dztgHighlightsModule.setAttrs(new ArrayList<CommonAttrVO>());
        dealCtx.setHighlightsModule(dztgHighlightsModule);

        // act
        glassesDurationStrategy.buildMoudle(dealCtx);

        // assert
        assertEquals(0, dztgHighlightsModule.getAttrs().size());
    }
}
