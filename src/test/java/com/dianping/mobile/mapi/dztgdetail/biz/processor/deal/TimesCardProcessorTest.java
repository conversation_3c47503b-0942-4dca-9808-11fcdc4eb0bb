package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MiniProgramSceneEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.sankuai.merchantcard.timescard.exposure.dto.CardSummaryBarDTO;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class TimesCardProcessorTest {

    @Mock
    private DealCtx ctx;

    @Mock
    private EnvCtx envCtx;

    private TimesCardProcessor processor = new TimesCardProcessor();

    @Mock
    private CardSummaryBarDTO cardSummaryBarDTO;

    private void invokePrivatePutTimesCardInfo(DealCtx ctx, CardSummaryBarDTO cardSummaryBarDTO) throws Throwable {
        try {
            // 获取TimesCardProcessor类
            Class<?> clazz = Class.forName("com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.TimesCardProcessor");
            // 获取私有方法putTimesCardInfo
            Method method = clazz.getDeclaredMethod("putTimesCardInfo", DealCtx.class, CardSummaryBarDTO.class);
            // 设置方法为可访问
            method.setAccessible(true);
            // 调用方法，由于是静态方法，传入null作为实例
            method.invoke(null, ctx, cardSummaryBarDTO);
        } catch (Exception e) {
            throw e.getCause();
        }
    }

    /**
     * 测试场景1：isMainApp 为 true，isExternalAndEnabled 为 false
     */
    @Test
    public void testIsEnable_MainAppTrue_ExternalAndEnabledFalse() throws Throwable {
        // arrange
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.isMainApp()).thenReturn(true);
        when(ctx.isExternal()).thenReturn(false);
        when(envCtx.isExternalAndEnabled(MiniProgramSceneEnum.TIMES_CARD.getPromoScene())).thenReturn(false);
        // act
        boolean result = processor.isEnable(ctx);
        // assert
        assertTrue(result);
    }

    /**
     * 测试场景2：isMainApp 为 false，isExternalAndEnabled 为 true
     */
    @Test
    public void testIsEnable_MainAppFalse_ExternalAndEnabledTrue() throws Throwable {
        // arrange
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.isMainApp()).thenReturn(false);
        when(envCtx.isMainWX()).thenReturn(false);
        when(envCtx.isExternalAndEnabled(MiniProgramSceneEnum.TIMES_CARD.getPromoScene())).thenReturn(true);
        // act
        boolean result = processor.isEnable(ctx);
        // assert
        assertTrue(result);
    }

    /**
     * 测试场景3：isMainApp 为 true，isExternalAndEnabled 为 true
     */
    @Test
    public void testIsEnable_MainAppTrue_ExternalAndEnabledTrue() throws Throwable {
        // arrange
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.isMainApp()).thenReturn(true);
        when(ctx.isExternal()).thenReturn(false);
        when(envCtx.isExternalAndEnabled(MiniProgramSceneEnum.TIMES_CARD.getPromoScene())).thenReturn(true);
        // act
        boolean result = processor.isEnable(ctx);
        // assert
        assertTrue(result);
    }

    /**
     * 测试场景4：isMainApp 为 false，isExternalAndEnabled 为 false
     */
    @Test
    public void testIsEnable_MainAppFalse_ExternalAndEnabledFalse() throws Throwable {
        // arrange
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.isMainApp()).thenReturn(false);
        when(envCtx.isMainWX()).thenReturn(false);
        when(envCtx.isExternalAndEnabled(MiniProgramSceneEnum.TIMES_CARD.getPromoScene())).thenReturn(false);
        // act
        boolean result = processor.isEnable(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * 测试场景：price 大于 0，方法执行 ctx.setTimesCard(cardSummaryBarDTO)
     */
    @Test
    public void testPutTimesCardInfoPriceGreaterThanZero() throws Throwable {
        // arrange
        BigDecimal price = new BigDecimal("100.00");
        when(cardSummaryBarDTO.getPrice()).thenReturn(price);
        // act
        invokePrivatePutTimesCardInfo(ctx, cardSummaryBarDTO);
        // assert
        verify(ctx).setTimesCard(cardSummaryBarDTO);
    }

    /**
     * 测试场景：price 等于 0，方法直接返回
     */
    @Test
    public void testPutTimesCardInfoPriceEqualsZero() throws Throwable {
        // arrange
        BigDecimal price = BigDecimal.ZERO;
        when(cardSummaryBarDTO.getPrice()).thenReturn(price);
        // act
        invokePrivatePutTimesCardInfo(ctx, cardSummaryBarDTO);
        // assert
        verifyNoMoreInteractions(ctx);
    }

    /**
     * 测试场景：cardSummaryBarDTO 为 null，抛出 NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testPutTimesCardInfoCardSummaryBarDTONull() throws Throwable {
        // arrange
        CardSummaryBarDTO nullCardSummaryBarDTO = null;
        // act
        invokePrivatePutTimesCardInfo(ctx, nullCardSummaryBarDTO);
        // assert
        // 预期抛出 NullPointerException
    }

    /**
     * 测试场景：ctx 为 null，抛出 NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testPutTimesCardInfoCtxNull() throws Throwable {
        // arrange
        DealCtx nullCtx = null;
        // act
        invokePrivatePutTimesCardInfo(nullCtx, cardSummaryBarDTO);
        // assert
        // 预期抛出 NullPointerException
    }
}
