package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealConsumerWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DealConsumerProcessorTest {
    @InjectMocks
    private DealConsumerProcessor processor;

    @Mock
    private DealConsumerWrapper dealConsumerWrapper;


    @Test
    public void testEnable() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupChannelDTO dealGroupChannelDTO = new DealGroupChannelDTO();
        dealGroupChannelDTO.setCategoryId(506);
        ctx.setChannelDTO(dealGroupChannelDTO);
        boolean enable = processor.isEnable(ctx);
        Assert.assertTrue(enable);
    }

    @Test
    public void testPrepare() {
        DealCtx dealCtx = new DealCtx(new EnvCtx());
        dealCtx.setMtLongShopId(123L);
        dealCtx.setDpId(123);
        dealCtx.setFutureCtx(new FutureCtx());
        when(dealConsumerWrapper.queryDealConsumerInfo(any())).thenReturn(CompletableFuture.completedFuture("xxx"));
        processor.prepare(dealCtx);
        Assert.assertNotNull(dealCtx.getFutureCtx().getContentProcessFuture());
    }

}
