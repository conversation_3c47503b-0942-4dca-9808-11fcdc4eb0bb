package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BusinessStyleEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.sankuai.dzcard.navigation.api.DzCardExposureService;
import com.sankuai.dzcard.navigation.api.DzCardPromoService;
import com.sankuai.dzcard.navigation.api.dto.FindQualifyEventIdsReqDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.Future;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DzCardPromoWrapperTest {

    @Mock
    private Future future;

    @InjectMocks
    private DzCardPromoWrapper dzCardPromoWrapper;

    @Mock
    private DzCardPromoService cardPromoService;

    @Mock
    private DzCardExposureService cardExposureService;

    /**
     * 测试 resolveUserState 方法，当 future 为 null 时
     */
    @Test
    public void testResolveUserStateWhenFutureIsNull() throws Throwable {
        // arrange
        Future<?> future = null;
        // act
        boolean result = dzCardPromoWrapper.resolveUserState(future);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 resolveUserState 方法，当 future 不为 null，且 getFutureResult 返回 Boolean.TRUE 时
     */
    @Test
    public void testResolveUserStateWhenFutureIsNotNullAndGetFutureResultReturnTrue() throws Throwable {
        // arrange
        when(dzCardPromoWrapper.getFutureResult(future)).thenReturn(Boolean.TRUE);
        // act
        boolean result = dzCardPromoWrapper.resolveUserState(future);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 resolveUserState 方法，当 future 不为 null，且 getFutureResult 返回 Boolean.FALSE 时
     */
    @Test
    public void testResolveUserStateWhenFutureIsNotNullAndGetFutureResultReturnFalse() throws Throwable {
        // arrange
        when(dzCardPromoWrapper.getFutureResult(future)).thenReturn(Boolean.FALSE);
        // act
        boolean result = dzCardPromoWrapper.resolveUserState(future);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 resolveUserState 方法，当 future 不为 null，且 getFutureResult 返回 null 时
     */
    @Test
    public void testResolveUserStateWhenFutureIsNotNullAndGetFutureResultReturnNull() throws Throwable {
        // arrange
        when(dzCardPromoWrapper.getFutureResult(future)).thenReturn(null);
        // act
        boolean result = dzCardPromoWrapper.resolveUserState(future);
        // assert
        assertFalse(result);
    }

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试prepare方法，当context为大众点评平台时的场景
     */
    @Test
    public void testPrepareForDpPlatform() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx context = new DealCtx(new EnvCtx());
        envCtx.setClientType(ClientTypeEnum.mt_mainApp_ios.getType());
        context.setDpId(123);
        context.setDpLongShopId(456L);
        context.getEnvCtx().setDpUserId(789L);
        context.getEnvCtx().setUnionId("unionId");
        when(cardPromoService.findQualifyEventIdsByProductIds(any(FindQualifyEventIdsReqDTO.class))).thenReturn(null);
        // act
        Future<?> result = dzCardPromoWrapper.prepare(context);
        // assert
        // 这里的断言需要根据实际情况来编写，例如检查返回的Future是否为null，或者检查是否调用了某个方法
        assertNull(result);
    }

    /**
     * 测试prepare方法，当context为直播间来源时的场景
     */
    @Test
    public void testPrepareForLiveRoomSource() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx context = new DealCtx(new EnvCtx());
        envCtx.setClientType(ClientTypeEnum.mt_mainApp_ios.getType());
        context.setMtId(123);
        context.setMtLongShopId(456L);
        context.getEnvCtx().setMtUserId(789L);
        context.getEnvCtx().setUnionId("unionId");
        context.setRequestSource(BusinessStyleEnum.LIVE.getStyleName());
        when(cardPromoService.findQualifyEventIdsByProductIds(any(FindQualifyEventIdsReqDTO.class))).thenReturn(null);
        // act
        Future<?> result = dzCardPromoWrapper.prepare(context);
        // assert
        // 这里的断言需要根据实际情况来编写，例如检查返回的Future是否为null，或者检查是否调用了某个方法
        assertNull(result);
    }

    /**
     * 测试prepare方法，当context为异常情况时的场景
     */
    @Test(expected = Exception.class)
    public void testPrepareForException() throws Throwable {
        // arrange
        DealCtx context = new DealCtx(new EnvCtx());
        when(cardPromoService.findQualifyEventIdsByProductIds(any(FindQualifyEventIdsReqDTO.class))).thenThrow(new RuntimeException("Mock Exception"));
        // act
        dzCardPromoWrapper.prepare(context);
        // assert
        // 这里的断言是期望抛出异常
    }

    /**
     * 测试用户未登录情况
     */
    @Test
    public void testPrepareUserState_UserNotLoggedIn() {
        DealCtx context = new DealCtx(new EnvCtx());
        context.getEnvCtx().setMtUserId(0L);
        context.getEnvCtx().setDpUserId(0L);
        Future<?> result = dzCardPromoWrapper.prepareUserState(context);
        assertNull("Future should be null when user is not logged in", result);
    }

    /**
     * 测试美团用户情况
     */
    @Test
    public void testPrepareUserState_MtUser() {
        DealCtx context = new DealCtx(new EnvCtx());
        context.getEnvCtx().setMtUserId(123123L);
        context.getEnvCtx().setDpUserId(123123123L);
        context.getEnvCtx().setClientType(ClientTypeEnum.mt_mainApp_ios.getType());
        context.setMtLongShopId(123L);
        context.getEnvCtx().setMtUserId(456L);
        when(cardExposureService.checkIsUserNew2ShopWithLongShopId(anyLong(), anyLong(), anyInt())).thenReturn(true);
        Future<?> result = dzCardPromoWrapper.prepareUserState(context);
        assertNull(result);
    }

    /**
     * 测试点评用户情况
     */
    @Test
    public void testPrepareUserState_DpUser() {
        DealCtx context = new DealCtx(new EnvCtx());
        context.getEnvCtx().setMtUserId(123123L);
        context.getEnvCtx().setDpUserId(123123123L);
        context.getEnvCtx().setClientType(ClientTypeEnum.dp_mainApp_ios.getType());
        context.setDpLongShopId(789L);
        context.getEnvCtx().setDpUserId(101112L);
        when(cardExposureService.checkIsUserNew2ShopWithLongShopId(anyLong(), anyLong(), anyInt())).thenReturn(true);
        Future<?> result = dzCardPromoWrapper.prepareUserState(context);
        assertNull(result);
    }
}
