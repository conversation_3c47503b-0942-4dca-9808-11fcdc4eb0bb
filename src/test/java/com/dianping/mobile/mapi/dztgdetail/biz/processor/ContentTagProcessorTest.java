package com.dianping.mobile.mapi.dztgdetail.biz.processor;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.ContentTagProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ContentTagWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.Future;

/**
 * @Author: <EMAIL>
 * @Date: 2024/8/11
 */
@RunWith(MockitoJUnitRunner.class)
public class ContentTagProcessorTest {
    @InjectMocks
    private ContentTagProcessor contentTagProcessor;
    @Mock
    private ContentTagWrapper contentTagWrapper;

    @Test
    public void testPrepare() {
        Future contentTagFuture = ContextStore.getFuture();
        Mockito.when(contentTagWrapper.getContentTagFuture(Mockito.any())).thenReturn(contentTagFuture);
        DealCtx ctx = new DealCtx(new EnvCtx());
        contentTagProcessor.prepare(ctx);
        Assert.assertNull(ctx.getFutureCtx().getContentTagFuture());
    }
}
