package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
@PrepareForTest(Lion.class)
public class UnifiedModuleProcessorGetTuanDetailV1ConfigTest {

    @Mock
    private Lion lion;

    private UnifiedModuleProcessor processor;

    @InjectMocks
    private UnifiedModuleProcessor unifiedModuleProcessor;

    @Mock
    private DouHuBiz douHuBiz;

    @Mock
    private DealCtx ctx;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(Lion.class);
        processor = new UnifiedModuleProcessor();
    }

    private String invokePrivateMethod(boolean defaultModule, boolean android) throws Exception {
        Method method = UnifiedModuleProcessor.class.getDeclaredMethod("getTuanDetailV1Config", boolean.class, boolean.class);
        method.setAccessible(true);
        return (String) method.invoke(processor, defaultModule, android);
    }

    private boolean invokePrivateMethod(DealCtx context) throws Throwable {
        Method method = UnifiedModuleProcessor.class.getDeclaredMethod("useStandardPhysicalExamModule", DealCtx.class);
        method.setAccessible(true);
        return (boolean) method.invoke(unifiedModuleProcessor, ctx);
    }

    /**
     * 测试当 defaultModule 为 false 且 android 为 true 时，返回 ANDROID_CHANNEL_MODULE_CONFIGS 配置项的值
     */
//    @Test
//    public void testGetTuanDetailV1ConfigDefaultModuleFalseAndroidTrue() throws Throwable {
//        // arrange
//        PowerMockito.when(Lion.getStringValue(LionConstants.ANDROID_CHANNEL_MODULE_CONFIGS)).thenReturn("android_config");
//        // act
//        String result = invokePrivateMethod(false, true);
//        // assert
//        assertEquals("android_config", result);
//    }

    /**
     * 测试当 defaultModule 为 false 且 android 为 false 时，返回 IOS_CHANNEL_MODULE_CONFIGS 配置项的值
     */
//    @Test
//    public void testGetTuanDetailV1ConfigDefaultModuleFalseAndroidFalse() throws Throwable {
//        // arrange
//        PowerMockito.when(Lion.getStringValue(LionConstants.IOS_CHANNEL_MODULE_CONFIGS)).thenReturn("ios_config");
//        // act
//        String result = invokePrivateMethod(false, false);
//        // assert
//        assertEquals("ios_config", result);
//    }

    /**
     * 测试当 defaultModule 为 true 时，返回 NEW_CHANNEL_MODULE_CONFIGS 配置项的值
     */
//    @Test
//    public void testGetTuanDetailV1ConfigDefaultModuleTrue() throws Throwable {
//        // arrange
//        PowerMockito.when(Lion.getStringValue(LionConstants.NEW_CHANNEL_MODULE_CONFIGS)).thenReturn("new_config");
//        // act
//        String result = invokePrivateMethod(true, false);
//        // assert
//        assertEquals("new_config", result);
//    }

    /**
     * 测试版本小于 "0.3.0" 的情况
     */
    @Test
    public void testUseStandardPhysicalExamModuleVersionLessThan030() throws Throwable {
        // arrange
        when(ctx.getMrnVersion()).thenReturn("0.2.9");
        // act
        boolean result = invokePrivateMethod(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * 测试版本大于等于 "0.3.0" 但 AB 测试结果为空的情况
     */
    @Test
    public void testUseStandardPhysicalExamModuleAbResultIsNull() throws Throwable {
        // arrange
        when(ctx.getMrnVersion()).thenReturn("0.3.0");
        when(douHuBiz.getAbExpResult(any(DealCtx.class), anyString())).thenReturn(null);
        // act
        boolean result = invokePrivateMethod(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * 测试版本大于等于 "0.3.0" 但 AB 测试结果的 configs 为空的情况
     */
    @Test
    public void testUseStandardPhysicalExamModuleAbResultConfigsIsEmpty() throws Throwable {
        // arrange
        when(ctx.getMrnVersion()).thenReturn("0.3.0");
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setConfigs(Collections.emptyList());
        when(douHuBiz.getAbExpResult(any(DealCtx.class), anyString())).thenReturn(moduleAbConfig);
        // act
        boolean result = invokePrivateMethod(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * 测试版本大于等于 "0.3.0" 且 AB 测试结果的 configs 不为空但第一个配置项为空的情况
     */
    @Test
    public void testUseStandardPhysicalExamModuleAbResultFirstConfigIsNull() throws Throwable {
        // arrange
        when(ctx.getMrnVersion()).thenReturn("0.3.0");
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setConfigs(Collections.singletonList(null));
        when(douHuBiz.getAbExpResult(any(DealCtx.class), anyString())).thenReturn(moduleAbConfig);
        // act
        boolean result = invokePrivateMethod(ctx);
        // assert
        assertFalse(result);
    }
}
