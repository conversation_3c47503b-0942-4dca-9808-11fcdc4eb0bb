package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtnIcon;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class DealBuyHelper_GetEntertainmentCardIconTest {

    // Duplicating the constants here as they are private in the DealBuyHelper class
    private static final String ENTERTAINMENT_MEMBER_DAY_PRICE_COLOR = "#572B00";

    private static final String TRANSPARENT_COLOR = "transparent";

    // Assuming the value of BORDER from the BuyBtnType enum since it's private
    // Placeholder value, adjust according to actual enum
    private static final int BORDER_VALUE = 1;

    /**
     * 测试 getEntertainmentCardIcon 方法，输入任意字符串 title
     */
    @Test
    public void testGetEntertainmentCardIcon() throws Throwable {
        // arrange
        String title = "testTitle";
        // act
        DealBuyBtnIcon result = DealBuyHelper.getEntertainmentCardIcon(title);
        // assert
        assertNotNull(result);
        assertEquals(title, result.getTitle());
        assertEquals(ENTERTAINMENT_MEMBER_DAY_PRICE_COLOR, result.getTitleColor());
        assertEquals(ENTERTAINMENT_MEMBER_DAY_PRICE_COLOR, result.getBorderColor());
        assertEquals(TRANSPARENT_COLOR, result.getBgColor());
        assertEquals(Integer.valueOf(BORDER_VALUE), result.getType());
    }
}
