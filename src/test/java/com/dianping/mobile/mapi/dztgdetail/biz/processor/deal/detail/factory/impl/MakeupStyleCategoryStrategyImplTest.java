package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.impl;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryAbInfo;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryDealModuleInfo;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryParam;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MakeupStyleCategoryStrategyImplTest {

    @InjectMocks
    private MakeupStyleCategoryStrategyImpl makeupStyleCategoryStrategy;

    @Test
    public void getTimesDealModuleAbConfig() {
        ModuleAbConfig result = makeupStyleCategoryStrategy.getTimesDealModuleAbConfig(null);
        Assert.assertNull(result);
    }

    @Test
    public void getSimilarDealModuleAbConfig() {
        ModuleAbConfig result = makeupStyleCategoryStrategy.getSimilarDealModuleAbConfig(null);
        Assert.assertNull(result);
    }

    @Test
    public void getModuleAbConfig() {
        ModuleAbConfig result = makeupStyleCategoryStrategy.getModuleAbConfig(null);
        Assert.assertNull(result);
    }

    @Test
    public void customNewDealStyle() {
        DealCategoryParam dealCategoryParam = DealCategoryParam.builder().build();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
        dealGroupCategoryDTO.setServiceType("彩妆兴趣培训");
        dealGroupDTO.setCategory(dealGroupCategoryDTO);
        dealCategoryParam.setDealGroupDTO(dealGroupDTO);
        boolean result = makeupStyleCategoryStrategy.newDealStyle(dealCategoryParam);
        Assert.assertTrue(result);
    }
}