package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.ugc.review.remote.dto.ReviewCount;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.HashMap;
import java.util.concurrent.Future;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class ReviewWrapper_GetReviewCountTest {

    @InjectMocks
    private ReviewWrapper reviewWrapper;

    @Mock
    private Future future;

    private ReviewCount reviewCount;

    @Before
    public void setUp() {
        reviewCount = new ReviewCount();
    }

    /**
     * 测试 Future 对象返回的 Map 为空的情况
     */
    @Test
    public void testGetReviewCountWhenMapIsEmpty() throws Exception {
        when(reviewWrapper.getFutureResult(future)).thenReturn(null);
        ReviewCount result = reviewWrapper.getReviewCount(1, future);
        assertNull(result);
    }

    /**
     * 测试 Future 对象返回的 Map 不为空，但不包含 mtDealId 对应的 ReviewCount 对象的情况
     */
    @Test
    public void testGetReviewCountWhenMapIsNotEmptyButNotContainsKey() throws Exception {
        HashMap<Integer, ReviewCount> map = new HashMap<>();
        map.put(2, reviewCount);
        when(reviewWrapper.getFutureResult(future)).thenReturn(map);
        ReviewCount result = reviewWrapper.getReviewCount(1, future);
        assertNull(result);
    }

    /**
     * 测试 Future 对象返回的 Map 不为空，且包含 mtDealId 对应的 ReviewCount 对象的情况
     */
    @Test
    public void testGetReviewCountWhenMapIsNotEmptyAndContainsKey() throws Exception {
        HashMap<Integer, ReviewCount> map = new HashMap<>();
        map.put(1, reviewCount);
        when(reviewWrapper.getFutureResult(future)).thenReturn(map);
        ReviewCount result = reviewWrapper.getReviewCount(1, future);
        assertSame(reviewCount, result);
    }
}
