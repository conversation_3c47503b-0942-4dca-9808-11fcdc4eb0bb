package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.enums.HolidayEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.highlights.DztgHighlightsModule;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DateRangeDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DisableDateDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.ReceiptEffectiveDateDTO;
import com.sankuai.meituan.holiday.thrift.dict.*;
import com.sankuai.meituan.holiday.thrift.exception.HolidayException;
import com.sankuai.meituan.holiday.thrift.iface.MtHolidayService;
import com.sankuai.meituan.holiday.thrift.struct.MtHoliday;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.text.SimpleDateFormat;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class FamilyFunHighlightsProcessorTest {

    @Mock
    private MtHolidayService mockMtHolidayService;

    @InjectMocks
    private FamilyFunHighlightsProcessor familyFunHighlightsProcessorUnderTest;
    /**
     * 当 DealGroupDTO 对象为空时，不应该构建亮点信息
     */
    @Test
    public void testBuildFamilyFunHighlightsWhenDealGroupDTOIsNull() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        FamilyFunHighlightsProcessor processor = new FamilyFunHighlightsProcessor();

        // act
        familyFunHighlightsProcessorUnderTest.process(ctx);

        // assert
        assertNull(ctx.getHighlightsModule());
    }


    /**
     * 当团购券的有效期类型为购买后 60 天时，应该构建超长有效期信息
     */
    @Test
    public void testBuildFamilyFunHighlightsWhenReceiptValidDaysIs60() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupUseRuleDTO useRule = new DealGroupUseRuleDTO();
        ReceiptEffectiveDateDTO receiptEffectiveDate = new ReceiptEffectiveDateDTO();
        receiptEffectiveDate.setReceiptDateType(1);
        receiptEffectiveDate.setReceiptValidDays(60);
        useRule.setReceiptEffectiveDate(receiptEffectiveDate);
        dealGroupDTO.setRule(new DealGroupRuleDTO());
        dealGroupDTO.getRule().setUseRule(useRule);
        ctx.setDealGroupDTO(dealGroupDTO);
        FamilyFunHighlightsProcessor processor = new FamilyFunHighlightsProcessor();

        // act
        familyFunHighlightsProcessorUnderTest.process(ctx);

        // assert
        assertNotNull(ctx.getHighlightsModule());
        assertEquals("超长有效期", ctx.getHighlightsModule().getContent());
    }


    /**
     * 当团购券的有效期类型为区间型时，且团购券可用时间段和不可用时间段均为空时，应该构建 null 的可用节假日信息
     */
    @Test
    public void testBuildFamilyFunHighlightsWhenReceiptDateTypeIs0AndSpecifyTimeIsEmptyAndDisableDateIsEmpty() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupUseRuleDTO useRule = new DealGroupUseRuleDTO();
        ReceiptEffectiveDateDTO receiptEffectiveDate = new ReceiptEffectiveDateDTO();
        receiptEffectiveDate.setReceiptDateType(0);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        receiptEffectiveDate.setReceiptBeginDate(sdf.format(new Date(2023, 1, 2)));
        receiptEffectiveDate.setReceiptBeginDate(sdf.format(new Date(2023, 1, 2)));
        useRule.setReceiptEffectiveDate(receiptEffectiveDate);
        dealGroupDTO.setRule(new DealGroupRuleDTO());
        dealGroupDTO.getRule().setUseRule(useRule);
        ctx.setDealGroupDTO(dealGroupDTO);

        // act
        familyFunHighlightsProcessorUnderTest.process(ctx);

        // assert
        assertNull(ctx.getHighlightsModule());
    }

    /**
     * 当团购券的有效期类型为区间型时，且团购券可用时间段和不可用时间段均为空时，应该构建正确的可用节假日信息
     */
    @Test
    public void testBuildFamilyFunHighlightsWhenReceiptDateTypeIs0AndSpecifyTimeIsEmptyAndDisableDateIsEmptyAndDisableDaysIsNotEmpty() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupUseRuleDTO useRule = new DealGroupUseRuleDTO();
        ReceiptEffectiveDateDTO receiptEffectiveDate = new ReceiptEffectiveDateDTO();
        receiptEffectiveDate.setReceiptDateType(0);
        receiptEffectiveDate.setReceiptBeginDate("2023-07-20 00:00:00");
        receiptEffectiveDate.setReceiptEndDate("2024-07-21 00:00:00");
        useRule.setReceiptEffectiveDate(receiptEffectiveDate);
        useRule.setDisableDate(new DisableDateDTO());
        useRule.getDisableDate().setDisableDays(Lists.newArrayList(HolidayEnum.CHUN_JIE.getCode()));
        DateRangeDTO dateRangeDTO = new DateRangeDTO();
        dateRangeDTO.setFrom("2023-10-01 00:00:00");
        dateRangeDTO.setTo("2023-10-01 00:00:00");
        useRule.getDisableDate().setDisableDateRangeDTOS(Lists.newArrayList(dateRangeDTO));
        dealGroupDTO.setRule(new DealGroupRuleDTO());
        dealGroupDTO.getRule().setUseRule(useRule);
        ctx.setDealGroupDTO(dealGroupDTO);
        // act
        familyFunHighlightsProcessorUnderTest.process(ctx);
        // assert
        DztgHighlightsModule highlightsModule = ctx.getHighlightsModule();
        Assert.assertNotNull(highlightsModule);
        Assert.assertEquals("元旦/劳动节/端午节/清明节/中秋节/六一儿童节/情人节/圣诞节可用", highlightsModule.getContent());
    }

}
