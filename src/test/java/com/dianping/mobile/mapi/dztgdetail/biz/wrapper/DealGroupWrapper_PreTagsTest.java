package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.tag.TagQueryService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.concurrent.Future;

import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.when;

public class DealGroupWrapper_PreTagsTest {

    @InjectMocks
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private TagQueryService tagQueryService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试dpId小于等于0的情况
     */
    @Test
    public void testPreTagsDpIdLessThanOrEqualToZero() {
        Future result = dealGroupWrapper.preTags(0);
        assertNull(result);
    }

    /**
     * 测试dpId大于0，且getTagByDealGroupIds方法抛出异常的情况
     */
    @Test
    public void testPreTagsDpIdGreaterThanZeroAndGetTagByDealGroupIdsThrowsException() throws Exception {
        when(tagQueryService.getTagByDealGroupIds(anyList())).thenThrow(new RuntimeException());
        Future result = dealGroupWrapper.preTags(1);
        assertNull(result);
    }
}
