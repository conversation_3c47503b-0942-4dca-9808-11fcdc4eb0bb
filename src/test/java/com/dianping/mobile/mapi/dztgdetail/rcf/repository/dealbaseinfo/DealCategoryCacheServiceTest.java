package com.dianping.mobile.mapi.dztgdetail.rcf.repository.dealbaseinfo;

import com.dianping.mobile.mapi.dztgdetail.biz.service.cellarservice.dealdetail.DealGroupDTOCellarService;
import com.dianping.mobile.mapi.dztgdetail.biz.service.cellarservice.dealdetail.req.DealGroupCacheRequest;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;

/**
 * DealCategoryCacheService的单元测试
 */
public class DealCategoryCacheServiceTest {

    @InjectMocks
    private DealCategoryCacheService dealCategoryCacheService;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Mock
    private DealGroupDTOCellarService dealGroupDTOCellarService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试saveCache方法，当dealId转换为int后，满足hitDealId2CategorySwitch条件
     */
    @Test
    public void testSaveCache_HitSwitch() {
        // arrange
        long dealId = 1L;
        boolean isMT = true;
        DealGroupDTO dealGroupDTO = new DealGroupDTO();

       // act
        dealCategoryCacheService.saveCache(dealId, isMT, dealGroupDTO);
        DealGroupCacheRequest request = dealCategoryCacheService.buildCacheRequest(dealId, isMT);
        // assert
        Assert.assertNotNull(request);
    }

}
