package com.dianping.mobile.mapi.dztgdetail.biz.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiClientWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.RelatedRecommendCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedRecommendReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedRecommendVO;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;

@RunWith(MockitoJUnitRunner.class)
public class RelatedRecommendServiceInitContextTest {

    @InjectMocks
    private RelatedRecommendService relatedRecommendService;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Mock
    private PoiClientWrapper poiClientWrapper;

    @Mock
    private MapperWrapper mapperWrapper;

    @Mock
    private Logger log;

    private RelatedRecommendCtx ctx;

    private RelatedRecommendReq req;

    private EnvCtx envCtx;

    @Before
    public void setUp() {
        ctx = new RelatedRecommendCtx();
        req = new RelatedRecommendReq();
        envCtx = spy(new EnvCtx());
        ctx.setReq(req);
        ctx.setEnvCtx(envCtx);
        ctx.setResult(new RelatedRecommendVO());
    }

    /**
     * Test getDealGroupDTO throws TException
     */
    @Test
    public void testInitContext_WhenGetDealGroupDTOThrowsException() throws Throwable {
        // arrange
        req.setDealGroupId(123);
        req.setShopIdStr("12345");
        doReturn(false).when(envCtx).isMt();
        TException expectedException = new TException("test exception");
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenThrow(expectedException);
        // act
        RelatedRecommendCtx result = relatedRecommendService.initContext(ctx);
        // assert
        assertNotNull(result);
        assertEquals(ctx, result);
    }

    /**
     * Test non-MT platform shop ID handling
     */
    @Test
    public void testInitContext_WhenNotMtPlatform() throws Throwable {
        // arrange
        String shopIdStr = "12345";
        req.setShopIdStr(shopIdStr);
        req.setDealGroupId(123);
        doReturn(false).when(envCtx).isMt();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(dealGroupDTO);
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        when(poiClientWrapper.getDpPoiDTO(eq(Long.valueOf(shopIdStr)), anyList())).thenReturn(dpPoiDTO);
        // act
        RelatedRecommendCtx result = relatedRecommendService.initContext(ctx);
        // assert
        assertNotNull(result);
        assertEquals(ctx, result);
        assertEquals(dealGroupDTO, result.getDealGroupDTO());
        assertEquals(dpPoiDTO, result.getDpPoiDTO());
    }

    /**
     * Test MT platform shop ID handling
     */
    @Test
    public void testInitContext_WhenMtPlatform() throws Throwable {
        // arrange
        String shopIdStr = "12345";
        req.setShopIdStr(shopIdStr);
        req.setDealGroupId(123);
        doReturn(true).when(envCtx).isMt();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(dealGroupDTO);
        Long dpShopId = 67890L;
        when(mapperWrapper.getDpByMtShopId(eq(Long.valueOf(shopIdStr)))).thenReturn(dpShopId);
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        when(poiClientWrapper.getDpPoiDTO(eq(dpShopId), anyList())).thenReturn(dpPoiDTO);
        // act
        RelatedRecommendCtx result = relatedRecommendService.initContext(ctx);
        // assert
        assertNotNull(result);
        assertEquals(ctx, result);
        assertEquals(dealGroupDTO, result.getDealGroupDTO());
        assertEquals(dpPoiDTO, result.getDpPoiDTO());
    }

    /**
     * Test when dpShopId is null for MT platform
     */
    @Test
    public void testInitContext_WhenDpShopIdIsNull() throws Throwable {
        // arrange
        String shopIdStr = "12345";
        req.setShopIdStr(shopIdStr);
        req.setDealGroupId(123);
        doReturn(true).when(envCtx).isMt();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(dealGroupDTO);
        when(mapperWrapper.getDpByMtShopId(eq(Long.valueOf(shopIdStr)))).thenReturn(null);
        // act
        RelatedRecommendCtx result = relatedRecommendService.initContext(ctx);
        // assert
        assertNotNull(result);
        assertEquals(ctx, result);
        assertEquals(dealGroupDTO, result.getDealGroupDTO());
    }

    /**
     * Test when dpShopId is zero for MT platform
     */
    @Test
    public void testInitContext_WhenDpShopIdIsZero() throws Throwable {
        // arrange
        String shopIdStr = "12345";
        req.setShopIdStr(shopIdStr);
        req.setDealGroupId(123);
        doReturn(true).when(envCtx).isMt();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(dealGroupDTO);
        when(mapperWrapper.getDpByMtShopId(eq(Long.valueOf(shopIdStr)))).thenReturn(0L);
        // act
        RelatedRecommendCtx result = relatedRecommendService.initContext(ctx);
        // assert
        assertNotNull(result);
        assertEquals(ctx, result);
        assertEquals(dealGroupDTO, result.getDealGroupDTO());
    }
}
