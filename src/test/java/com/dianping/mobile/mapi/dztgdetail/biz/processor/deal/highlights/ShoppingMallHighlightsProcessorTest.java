package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.service.DpPoiService;
import java.util.concurrent.Future;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class ShoppingMallHighlightsProcessorTest {

    @InjectMocks
    private ShoppingMallHighlightsProcessor shoppingMallHighlightsProcessor;

    @Mock
    private DpPoiService dpPoiServiceFuture;

    @Mock
    private Future mockFuture;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 findShopsByShopIds 方法，异常情况
     */
    @Test
    public void testFindShopsByShopIdsException() throws Throwable {
        // arrange
        DpPoiRequest request = new DpPoiRequest();
        when(dpPoiServiceFuture.findShopsByShopIds(request)).thenThrow(new RuntimeException());
        // act
        Future result = shoppingMallHighlightsProcessor.findShopsByShopIds(request);
        // assert
        verify(dpPoiServiceFuture, times(1)).findShopsByShopIds(request);
        assertNull(result);
    }
}
