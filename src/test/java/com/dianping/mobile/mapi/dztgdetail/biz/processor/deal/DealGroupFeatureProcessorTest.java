package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.base.dto.DealReceiptDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealFeature;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.util.TimeUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.ReceiptEffectiveDateDTO;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Future;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

/**
 * Test cases for DealGroupFeatureProcessor.getPreSaleReceiptUseTest method
 */
@RunWith(MockitoJUnitRunner.class)
public class DealGroupFeatureProcessorTest {

    @InjectMocks
    private DealGroupFeatureProcessor processor;

    @Mock
    private DealCtx ctx;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    private ReceiptEffectiveDateDTO invokeGetReceipt(DealGroupDTO dealGroupDTO) throws Exception {
        Method method = DealGroupFeatureProcessor.class.getDeclaredMethod("getReceipt", DealGroupDTO.class);
        method.setAccessible(true);
        return (ReceiptEffectiveDateDTO) method.invoke(processor, dealGroupDTO);
    }

    private void invokePrivateProcessByQueryCenter(DealCtx ctx) throws Exception {
        Method method = DealGroupFeatureProcessor.class.getDeclaredMethod("processByQueryCenter", DealCtx.class);
        method.setAccessible(true);
        method.invoke(processor, ctx);
    }

    /**
     * Test when receiptDateType is 1, should use dealGroupBaseDTO's endDate
     */
    @Test
    public void testGetPreSaleReceiptUseTest_WhenReceiptDateTypeIsOne() throws Throwable {
        // arrange
        DealReceiptDTO receiptDTO = new DealReceiptDTO();
        receiptDTO.setReceiptDateType(1);
        DealGroupBaseDTO groupBaseDTO = new DealGroupBaseDTO();
        Date endDate = new Date();
        groupBaseDTO.setEndDate(endDate);
        // Use reflection to invoke private method
        Method method = DealGroupFeatureProcessor.class.getDeclaredMethod("getPreSaleReceiptUseTest", DealReceiptDTO.class, DealGroupBaseDTO.class);
        method.setAccessible(true);
        // act
        String result = (String) method.invoke(processor, receiptDTO, groupBaseDTO);
        // assert
        String expectedDate = TimeUtils.convertDate2DayDotString(endDate);
        String expected = String.format("可用时段：%s后可用", expectedDate);
        assertEquals(expected, result);
    }

    /**
     * Test when receiptDateType is not 1, should use receiptDTO's receiptBeginDate
     */
    @Test
    public void testGetPreSaleReceiptUseTest_WhenReceiptDateTypeIsNotOne() throws Throwable {
        // arrange
        DealReceiptDTO receiptDTO = new DealReceiptDTO();
        receiptDTO.setReceiptDateType(2);
        Date beginDate = new Date();
        receiptDTO.setReceiptBeginDate(beginDate);
        DealGroupBaseDTO groupBaseDTO = new DealGroupBaseDTO();
        // Use reflection to invoke private method
        Method method = DealGroupFeatureProcessor.class.getDeclaredMethod("getPreSaleReceiptUseTest", DealReceiptDTO.class, DealGroupBaseDTO.class);
        method.setAccessible(true);
        // act
        String result = (String) method.invoke(processor, receiptDTO, groupBaseDTO);
        // assert
        String expectedDate = TimeUtils.convertDate2DayDotString(beginDate);
        String expected = String.format("可用时段：%s后可用", expectedDate);
        assertEquals(expected, result);
    }

    /**
     * Test when date conversion returns null
     */
    @Test
    public void testGetPreSaleReceiptUseTest_WhenDateConversionReturnsNull() throws Throwable {
        // arrange
        DealReceiptDTO receiptDTO = new DealReceiptDTO();
        receiptDTO.setReceiptDateType(1);
        DealGroupBaseDTO groupBaseDTO = new DealGroupBaseDTO();
        groupBaseDTO.setEndDate(null);
        // Use reflection to invoke private method
        Method method = DealGroupFeatureProcessor.class.getDeclaredMethod("getPreSaleReceiptUseTest", DealReceiptDTO.class, DealGroupBaseDTO.class);
        method.setAccessible(true);
        // act
        String result = (String) method.invoke(processor, receiptDTO, groupBaseDTO);
        // assert
        assertNull(result);
    }

    /**
     * Test when input parameters are null
     */
    @Test
    public void testGetPreSaleReceiptUseTest_WhenInputParametersAreNull() throws Throwable {
        // Use reflection to invoke private method
        Method method = DealGroupFeatureProcessor.class.getDeclaredMethod("getPreSaleReceiptUseTest", DealReceiptDTO.class, DealGroupBaseDTO.class);
        method.setAccessible(true);
        // act
        String result = (String) method.invoke(processor, new DealReceiptDTO(), new DealGroupBaseDTO());
        // assert
        assertNull(result);
    }

    /**
     * Test when both begin date and end date are valid
     */
    @Test
    public void testGetPreSaleReceiptSaleTest_WithValidDates() throws Throwable {
        // arrange
        DealReceiptDTO receiptDTO = new DealReceiptDTO();
        DealGroupBaseDTO baseDTO = new DealGroupBaseDTO();
        // 2023.01.01
        Date beginDate = new Date(1672531200000L);
        // 2023.01.02
        Date endDate = new Date(1672617600000L);
        baseDTO.setBeginDate(beginDate);
        baseDTO.setEndDate(endDate);
        // Use reflection to invoke the private method
        Method method = DealGroupFeatureProcessor.class.getDeclaredMethod("getPreSaleReceiptSaleTest", DealReceiptDTO.class, DealGroupBaseDTO.class);
        method.setAccessible(true);
        String result = (String) method.invoke(processor, receiptDTO, baseDTO);
        // assert
        assertEquals("预售时段：2023.01.01-2023.01.02", result);
    }

    /**
     * Test when begin date is null
     */
    @Test
    public void testGetPreSaleReceiptSaleTest_WithNullBeginDate() throws Throwable {
        // arrange
        DealReceiptDTO receiptDTO = new DealReceiptDTO();
        DealGroupBaseDTO baseDTO = new DealGroupBaseDTO();
        baseDTO.setBeginDate(null);
        baseDTO.setEndDate(new Date());
        // Use reflection to invoke the private method
        Method method = DealGroupFeatureProcessor.class.getDeclaredMethod("getPreSaleReceiptSaleTest", DealReceiptDTO.class, DealGroupBaseDTO.class);
        method.setAccessible(true);
        String result = (String) method.invoke(processor, receiptDTO, baseDTO);
        // assert
        assertNull(result);
    }

    /**
     * Test when end date is null
     */
    @Test
    public void testGetPreSaleReceiptSaleTest_WithNullEndDate() throws Throwable {
        // arrange
        DealReceiptDTO receiptDTO = new DealReceiptDTO();
        DealGroupBaseDTO baseDTO = new DealGroupBaseDTO();
        baseDTO.setBeginDate(new Date());
        baseDTO.setEndDate(null);
        // Use reflection to invoke the private method
        Method method = DealGroupFeatureProcessor.class.getDeclaredMethod("getPreSaleReceiptSaleTest", DealReceiptDTO.class, DealGroupBaseDTO.class);
        method.setAccessible(true);
        String result = (String) method.invoke(processor, receiptDTO, baseDTO);
        // assert
        assertNull(result);
    }

    /**
     * Test when both dates are null
     */
    @Test
    public void testGetPreSaleReceiptSaleTest_WithBothDatesNull() throws Throwable {
        // arrange
        DealReceiptDTO receiptDTO = new DealReceiptDTO();
        DealGroupBaseDTO baseDTO = new DealGroupBaseDTO();
        baseDTO.setBeginDate(null);
        baseDTO.setEndDate(null);
        // Use reflection to invoke the private method
        Method method = DealGroupFeatureProcessor.class.getDeclaredMethod("getPreSaleReceiptSaleTest", DealReceiptDTO.class, DealGroupBaseDTO.class);
        method.setAccessible(true);
        String result = (String) method.invoke(processor, receiptDTO, baseDTO);
        // assert
        assertNull(result);
    }

    /**
     * Test when dealGroupDTO is null
     */
    @Test
    public void testGetReceipt_WhenDealGroupDTOIsNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = null;
        // act
        ReceiptEffectiveDateDTO result = invokeGetReceipt(dealGroupDTO);
        // assert
        assertNull(result);
    }

    /**
     * Test when dealGroupDTO.getRule() returns null
     */
    @Test
    public void testGetReceipt_WhenRuleIsNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getRule()).thenReturn(null);
        // act
        ReceiptEffectiveDateDTO result = invokeGetReceipt(dealGroupDTO);
        // assert
        assertNull(result);
    }

    /**
     * Test when dealGroupDTO.getRule().getUseRule() returns null
     */
    @Test
    public void testGetReceipt_WhenUseRuleIsNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupRuleDTO ruleDTO = mock(DealGroupRuleDTO.class);
        when(dealGroupDTO.getRule()).thenReturn(ruleDTO);
        when(ruleDTO.getUseRule()).thenReturn(null);
        // act
        ReceiptEffectiveDateDTO result = invokeGetReceipt(dealGroupDTO);
        // assert
        assertNull(result);
    }

    /**
     * Test when all objects exist and can return receipt date
     */
    @Test
    public void testGetReceipt_WhenAllObjectsExist() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupRuleDTO ruleDTO = mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRuleDTO = mock(DealGroupUseRuleDTO.class);
        ReceiptEffectiveDateDTO expectedReceiptDTO = mock(ReceiptEffectiveDateDTO.class);
        when(dealGroupDTO.getRule()).thenReturn(ruleDTO);
        when(ruleDTO.getUseRule()).thenReturn(useRuleDTO);
        when(useRuleDTO.getReceiptEffectiveDate()).thenReturn(expectedReceiptDTO);
        // act
        ReceiptEffectiveDateDTO result = invokeGetReceipt(dealGroupDTO);
        // assert
        assertEquals(expectedReceiptDTO, result);
    }

    /**
     * Test case: Deal is not PreSale/WarmUp/Wuyoutong type
     * Expected: No features added
     */
    @Test
    public void testProcessByQueryCenter_NotSpecialDeal() throws Throwable {
        // arrange
        List<AttributeDTO> attrs = new ArrayList<>();
        when(ctx.getAttrs()).thenReturn(attrs);
        // act
        invokePrivateProcessByQueryCenter(ctx);
        // assert
        verify(ctx, never()).getResult();
    }

    /**
     * Test case: PreSale deal with invalid receipt info (null receipt)
     * Expected: No features added
     */
    @Test
    public void testProcessByQueryCenter_PreSaleDealWithNullReceipt() throws Throwable {
        // arrange
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attr = new AttributeDTO();
        attr.setName("preSaleTag");
        attr.setValue(Lists.newArrayList("true"));
        attrs.add(attr);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        when(ctx.getAttrs()).thenReturn(attrs);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        // act
        invokePrivateProcessByQueryCenter(ctx);
        // assert
        verify(ctx, never()).getResult();
    }

    /**
     * Test case: WarmUp deal with valid receipt info
     * Expected: WarmUpAvailableDate feature added with use text
     */
    @Test
    public void testProcessByQueryCenter_WarmUpDealWithValidReceipt() throws Throwable {
        // arrange
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attr = new AttributeDTO();
        attr.setName("warmUpStartTime");
        attr.setValue(Lists.newArrayList("2023-12-31 00:00:00"));
        attrs.add(attr);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupUseRuleDTO useRule = new DealGroupUseRuleDTO();
        ReceiptEffectiveDateDTO receiptDTO = new ReceiptEffectiveDateDTO();
        receiptDTO.setShowText("show text");
        receiptDTO.setReceiptBeginDate("2024-01-01 00:00:00");
        receiptDTO.setReceiptEndDate("2024-12-31 23:59:59");
        useRule.setReceiptEffectiveDate(receiptDTO);
        dealGroupDTO.setRule(new com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO());
        dealGroupDTO.getRule().setUseRule(useRule);
        DealGroupPBO result = new DealGroupPBO();
        result.setDealFeatures(new ArrayList<>());
        when(ctx.getAttrs()).thenReturn(attrs);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(ctx.getResult()).thenReturn(result);
        // act
        invokePrivateProcessByQueryCenter(ctx);
        // assert
        assertNotNull(result.getDealFeatures());
        assertEquals(1, result.getDealFeatures().size());
        DealFeature feature = result.getDealFeatures().get(0);
        assertEquals("WarmUpAvailableDate", feature.getType());
        assertEquals(1, feature.getFeatures().size());
    }

    /**
     * Test case: WarmUp deal with invalid receipt dates
     * Expected: No features added
     */
    @Test
    public void testProcessByQueryCenter_WarmUpDealWithInvalidReceiptDates() throws Throwable {
        // arrange
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attr = new AttributeDTO();
        attr.setName("warmUpStartTime");
        attr.setValue(Lists.newArrayList("2023-12-31 00:00:00"));
        attrs.add(attr);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupUseRuleDTO useRule = new DealGroupUseRuleDTO();
        ReceiptEffectiveDateDTO receiptDTO = new ReceiptEffectiveDateDTO();
        receiptDTO.setShowText("show text");
        receiptDTO.setReceiptBeginDate("invalid date");
        receiptDTO.setReceiptEndDate("invalid date");
        useRule.setReceiptEffectiveDate(receiptDTO);
        dealGroupDTO.setRule(new com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO());
        dealGroupDTO.getRule().setUseRule(useRule);
        when(ctx.getAttrs()).thenReturn(attrs);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        // act
        invokePrivateProcessByQueryCenter(ctx);
        // assert
        verify(ctx, never()).getResult();
    }

    /**
     * Test when query center is enabled but deal is not PreSale/WarmUp/Wuyoutong
     */
    @Test
    public void testProcess_QueryCenterEnabled_NotSpecialDeal() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        ctx.setUseQueryCenter(true);
        ctx.setQueryCenterHasError(false);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        ctx.setDealGroupDTO(dealGroupDTO);
        List<AttributeDTO> attrs = new ArrayList<>();
        ctx.setAttrs(attrs);
        DealGroupPBO result = new DealGroupPBO();
        ctx.setResult(result);
        // act
        processor.process(ctx);
        // assert
        assertNull(ctx.getResult().getDealFeatures());
    }

    /**
     * Test when receipt future is null
     */
    @Test
    public void testProcess_QueryCenterDisabled_NullReceiptFuture() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        ctx.setUseQueryCenter(false);
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attr = new AttributeDTO();
        attr.setName("PRE_SALE_TAG");
        attr.setValue(Arrays.asList("true"));
        attrs.add(attr);
        ctx.setAttrs(attrs);
        FutureCtx futureCtx = new FutureCtx();
        ctx.setFutureCtx(futureCtx);
        DealGroupPBO result = new DealGroupPBO();
        ctx.setResult(result);
        // act
        processor.process(ctx);
        // assert
        assertNull(ctx.getResult().getDealFeatures());
    }

    /**
     * Test when receipt DTO is null
     */
    @Test
    public void testProcess_QueryCenterDisabled_NullReceiptDTO() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        ctx.setUseQueryCenter(false);
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attr = new AttributeDTO();
        attr.setName("PRE_SALE_TAG");
        attr.setValue(Arrays.asList("true"));
        attrs.add(attr);
        ctx.setAttrs(attrs);
        FutureCtx futureCtx = new FutureCtx();
        Future<List<DealReceiptDTO>> future = mock(Future.class);
        futureCtx.setDealReceiptFuture(future);
        ctx.setFutureCtx(futureCtx);
        DealGroupPBO result = new DealGroupPBO();
        ctx.setResult(result);
        // act
        processor.process(ctx);
        // assert
        assertNull(ctx.getResult().getDealFeatures());
    }

    /**
     * Test when receipt show text is null
     */
    @Test
    public void testProcess_QueryCenterDisabled_NullShowText() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        ctx.setUseQueryCenter(false);
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attr = new AttributeDTO();
        attr.setName("PRE_SALE_TAG");
        attr.setValue(Arrays.asList("true"));
        attrs.add(attr);
        ctx.setAttrs(attrs);
        FutureCtx futureCtx = new FutureCtx();
        Future<List<DealReceiptDTO>> future = mock(Future.class);
        futureCtx.setDealReceiptFuture(future);
        ctx.setFutureCtx(futureCtx);
        DealReceiptDTO receiptDTO = new DealReceiptDTO();
        DealGroupPBO result = new DealGroupPBO();
        ctx.setResult(result);
        // act
        processor.process(ctx);
        // assert
        assertNull(ctx.getResult().getDealFeatures());
    }

    /**
     * Test when sale text generation fails
     */
    @Test
    public void testProcess_QueryCenterDisabled_SaleTextGenerationFails() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        ctx.setUseQueryCenter(false);
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attr = new AttributeDTO();
        attr.setName("PRE_SALE_TAG");
        attr.setValue(Arrays.asList("true"));
        attrs.add(attr);
        ctx.setAttrs(attrs);
        FutureCtx futureCtx = new FutureCtx();
        Future<List<DealReceiptDTO>> future = mock(Future.class);
        futureCtx.setDealReceiptFuture(future);
        ctx.setFutureCtx(futureCtx);
        DealReceiptDTO receiptDTO = new DealReceiptDTO();
        receiptDTO.setShowText("Test Show Text");
        DealGroupBaseDTO baseDTO = new DealGroupBaseDTO();
        ctx.setDealGroupBase(baseDTO);
        DealGroupPBO result = new DealGroupPBO();
        ctx.setResult(result);
        // act
        processor.process(ctx);
        // assert
        assertNull(ctx.getResult().getDealFeatures());
    }

    /**
     * Test when use text generation fails
     */
    @Test
    public void testProcess_QueryCenterDisabled_UseTextGenerationFails() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        ctx.setUseQueryCenter(false);
        List<AttributeDTO> attrs = new ArrayList<>();
        AttributeDTO attr = new AttributeDTO();
        attr.setName("PRE_SALE_TAG");
        attr.setValue(Arrays.asList("true"));
        attrs.add(attr);
        ctx.setAttrs(attrs);
        FutureCtx futureCtx = new FutureCtx();
        Future<List<DealReceiptDTO>> future = mock(Future.class);
        futureCtx.setDealReceiptFuture(future);
        ctx.setFutureCtx(futureCtx);
        DealReceiptDTO receiptDTO = new DealReceiptDTO();
        receiptDTO.setShowText("Test Show Text");
        DealGroupBaseDTO baseDTO = new DealGroupBaseDTO();
        baseDTO.setBeginDate(new Date());
        ctx.setDealGroupBase(baseDTO);
        DealGroupPBO result = new DealGroupPBO();
        ctx.setResult(result);
        // act
        processor.process(ctx);
        // assert
        assertNull(ctx.getResult().getDealFeatures());
    }
}
