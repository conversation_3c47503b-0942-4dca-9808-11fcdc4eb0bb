package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.buy.DealGroupBuyRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.ReceiptEffectiveDateDTO;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * 类的描述
 *
 * @auther: liweilong06
 * @date: 2024/1/17 9:18 下午
 */
@RunWith(MockitoJUnitRunner.class)
public class EffectiveDateHelperUnitTest {

    private DealGroupDTO dealGroupDTO;

    private DealCtx ctx;

    private DealGroupCategoryDTO dealGroupCategoryDTO;

    private FutureCtx futureCtx;

    private DztgClientTypeEnum dztgClientTypeEnum;

    private EnvCtx envCtx;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        dztgClientTypeEnum = DztgClientTypeEnum.MEITUAN_APP;
        envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(dztgClientTypeEnum);
        ctx = new DealCtx(envCtx);
        dealGroupDTO = mock(DealGroupDTO.class);
        dealGroupCategoryDTO = mock(DealGroupCategoryDTO.class);
        futureCtx = mock(FutureCtx.class);
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.setFutureCtx(futureCtx);
    }

    @Test
    public void test_getEffectiveDateForOnlineCourse_null() {
        Assert.assertTrue(StringUtils.isBlank(EffectiveDateHelper.getEffectiveDateForOnlineCourse(null)));
    }

    @Test
    public void test_getEffectiveDateForOnlineCourse_nullValue() {
        Assert.assertTrue(StringUtils.isBlank(EffectiveDateHelper.getEffectiveDateForOnlineCourse(ctx)));
    }

    /**
     * 测试 receiptEffectiveDateDTO 为 null 的情况
     */
    @Test
    public void testGetEffectiveDateForOnlineCourseReceiptEffectiveDateDTOIsNull() {
        // arrange
        DealCtx dealCtx = Mockito.mock(DealCtx.class);
        // act
        String result = EffectiveDateHelper.getEffectiveDateForOnlineCourse(dealCtx);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试 receiptEffectiveDateDTO.getReceiptDateType() 为 0 的情况
     */
    @Test
    public void testGetEffectiveDateForOnlineCourseReceiptDateTypeIsZero() {
        // arrange
        DealCtx dealCtx = Mockito.mock(DealCtx.class);
        ReceiptEffectiveDateDTO receiptEffectiveDateDTO = Mockito.mock(ReceiptEffectiveDateDTO.class);
        when(dealCtx.getDealGroupDTO()).thenReturn(Mockito.mock(DealGroupDTO.class));
        when(dealCtx.getDealGroupDTO().getRule()).thenReturn(Mockito.mock(DealGroupRuleDTO.class));
        when(dealCtx.getDealGroupDTO().getRule().getUseRule()).thenReturn(Mockito.mock(DealGroupUseRuleDTO.class));
        when(dealCtx.getDealGroupDTO().getRule().getUseRule().getReceiptEffectiveDate()).thenReturn(receiptEffectiveDateDTO);
        when(receiptEffectiveDateDTO.getReceiptDateType()).thenReturn(0);
        when(receiptEffectiveDateDTO.getReceiptBeginDate()).thenReturn("2022-01-01 12:00:00");
        when(receiptEffectiveDateDTO.getReceiptEndDate()).thenReturn("2022-12-31 12:02:01");
        // act
        String result = EffectiveDateHelper.getEffectiveDateForOnlineCourse(dealCtx);
        // assert
        assertEquals("有效期至2022-12-31", result);
    }

    /**
     * 测试 receiptEffectiveDateDTO.getReceiptDateType() 为 1 的情况
     */
    @Test
    public void testGetEffectiveDateForOnlineCourseReceiptDateTypeIsOne() {
        // arrange
        DealCtx dealCtx = Mockito.mock(DealCtx.class);
        ReceiptEffectiveDateDTO receiptEffectiveDateDTO = Mockito.mock(ReceiptEffectiveDateDTO.class);
        when(dealCtx.getDealGroupDTO()).thenReturn(Mockito.mock(DealGroupDTO.class));
        when(dealCtx.getDealGroupDTO().getRule()).thenReturn(Mockito.mock(DealGroupRuleDTO.class));
        when(dealCtx.getDealGroupDTO().getRule().getUseRule()).thenReturn(Mockito.mock(DealGroupUseRuleDTO.class));
        when(dealCtx.getDealGroupDTO().getRule().getUseRule().getReceiptEffectiveDate()).thenReturn(receiptEffectiveDateDTO);
        when(receiptEffectiveDateDTO.getReceiptDateType()).thenReturn(1);
        when(receiptEffectiveDateDTO.getReceiptValidDays()).thenReturn(30);
        // act
        String result = EffectiveDateHelper.getEffectiveDateForOnlineCourse(dealCtx);
        // assert
        assertEquals("开课后30天内有效", result);
    }

    /**
     * 测试其他情况
     */
    @Test
    public void testGetEffectiveDateForOnlineCourseOtherCases() {
        // arrange
        DealCtx dealCtx = Mockito.mock(DealCtx.class);
        ReceiptEffectiveDateDTO receiptEffectiveDateDTO = Mockito.mock(ReceiptEffectiveDateDTO.class);
        when(dealCtx.getDealGroupDTO()).thenReturn(Mockito.mock(DealGroupDTO.class));
        when(dealCtx.getDealGroupDTO().getRule()).thenReturn(Mockito.mock(DealGroupRuleDTO.class));
        when(dealCtx.getDealGroupDTO().getRule().getUseRule()).thenReturn(Mockito.mock(DealGroupUseRuleDTO.class));
        when(dealCtx.getDealGroupDTO().getRule().getUseRule().getReceiptEffectiveDate()).thenReturn(receiptEffectiveDateDTO);
        when(receiptEffectiveDateDTO.getReceiptDateType()).thenReturn(2);
        // act
        String result = EffectiveDateHelper.getEffectiveDateForOnlineCourse(dealCtx);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试 GetEffectiveDateForVoCaEduZeroDeal,参数 为 null 的情况
     * 期望返回空字符串
     */
    @Test
    public void testGetEffectiveDateForVoCaEduZeroDealCtxIsNull() {
        // arrange
        DealCtx dealCtx = null;
        // act
        String result = EffectiveDateHelper.getEffectiveDateForVoCaEduZero(dealCtx);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试 GetEffectiveDateForVoCaEduZeroDeal，但 maxPerUser 为 null 的情况
     * 期望返回空字符串
     */
    @Test
    public void testGetEffectiveDateForVoCaEduZeroMaxPerUserIsNull() {
        // arrange
        DealCtx dealCtx = Mockito.mock(DealCtx.class);
        DealGroupDTO dealGroupDTO = Mockito.mock(DealGroupDTO.class);
        DealGroupRuleDTO dealGroupRuleDTO = Mockito.mock(DealGroupRuleDTO.class);
        DealGroupBuyRuleDTO dealGroupBuyRuleDTO = Mockito.mock(DealGroupBuyRuleDTO.class);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getRule()).thenReturn(dealGroupRuleDTO);
        when(dealGroupRuleDTO.getBuyRule()).thenReturn(dealGroupBuyRuleDTO);
        when(dealGroupBuyRuleDTO.getMaxPerUser()).thenReturn(null);
        // act
        String result = EffectiveDateHelper.getEffectiveDateForVoCaEduZero(dealCtx);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试 GetEffectiveDateForVoCaEduZeroDeal，maxPerUser 不为 null 的情况
     * 期望正常返回
     */
    @Test
    public void testGetEffectiveDateForVoCaEduZeroMaxPerUserNotNull() {
        // arrange
        DealCtx dealCtx = Mockito.mock(DealCtx.class);
        DealGroupDTO dealGroupDTO = Mockito.mock(DealGroupDTO.class);
        DealGroupRuleDTO dealGroupRuleDTO = Mockito.mock(DealGroupRuleDTO.class);
        DealGroupBuyRuleDTO dealGroupBuyRuleDTO = Mockito.mock(DealGroupBuyRuleDTO.class);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getRule()).thenReturn(dealGroupRuleDTO);
        when(dealGroupRuleDTO.getBuyRule()).thenReturn(dealGroupBuyRuleDTO);
        when(dealGroupBuyRuleDTO.getMaxPerUser()).thenReturn(3);
        // act
        String result = EffectiveDateHelper.getEffectiveDateForVoCaEduZero(dealCtx);
        // assert
        assertEquals("每人限购3次*购买后商家将致电为您服务", result);
    }

    /**
     * Tests the parseDate method with an incorrectly formatted date string.
     */
    @Test
    public void testParseDate2() throws Throwable {
        // arrange
        String dateStr = "2023/10/25";
        // act
        Date date = DateHelper.parseDate(dateStr);
        // assert
        assertNull(date);
    }

    /**
     * Tests the parseDate method with a null date string.
     */
    @Test
    public void testParseDate3() throws Throwable {
        // arrange
        String dateStr = null;
        // act
        Date date = DateHelper.parseDate(dateStr);
        assertNull(date);
    }

    /**
     * Tests the parseDate method with an empty date string.
     */
    @Test
    public void testParseDate4() throws Throwable {
        // arrange
        String dateStr = "";
        // act
        Date date = DateHelper.parseDate(dateStr);
        // assert
        assertNull(date);
    }
}
