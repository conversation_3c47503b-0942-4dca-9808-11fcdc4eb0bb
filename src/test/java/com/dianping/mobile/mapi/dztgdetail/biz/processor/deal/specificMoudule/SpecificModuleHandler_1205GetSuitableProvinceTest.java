package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.DealCategoryEnum;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.EducationDealAttrUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.BaseDisplayItemVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailDisplayUnitVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailSpecificModuleVO;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.dianping.technician.common.api.domain.TechnicianResp;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.technician.info.online.dto.OnlineTechWithAttrsDTO;
import com.sankuai.technician.info.online.service.OnlineTechQueryService;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class SpecificModuleHandler_1205GetSuitableProvinceTest {

    @InjectMocks
    private SpecificModuleHandler_1205 specificModuleHandler_1205;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Mock
    private OnlineTechQueryService onlineTechQueryService;

    @Mock
    private Future future;

    @Mock
    private DealGroupDTO dealGroupDTO;

    @Mock
    private AttrDTO attrDTO;

    private Method getSuitablePeopleMethod;

    @Mock
    private EducationDealAttrUtils educationDealAttrUtils;

    @Mock
    private SpecificModuleCtx context;

    @Before
    public void setUp() throws Exception {
        getSuitablePeopleMethod = SpecificModuleHandler_1205.class.getDeclaredMethod("getSuitablePeople", SpecificModuleCtx.class);
        getSuitablePeopleMethod.setAccessible(true);
    }

    private DealDetailDisplayUnitVO invokeGetSuitableProvince(SpecificModuleCtx context) throws Exception {
        Method method = SpecificModuleHandler_1205.class.getDeclaredMethod("getSuitableProvince", SpecificModuleCtx.class);
        method.setAccessible(true);
        return (DealDetailDisplayUnitVO) method.invoke(specificModuleHandler_1205, context);
    }

    private SpecificModuleCtx createContextWithDealGroupDTO(DealGroupDTO dealGroupDTO) {
        SpecificModuleCtx ctx = new SpecificModuleCtx();
        ctx.setDealGroupDTO(dealGroupDTO);
        return ctx;
    }

    private DealDetailDisplayUnitVO invokeGetAppendService(SpecificModuleCtx ctx) throws Exception {
        Method method = SpecificModuleHandler_1205.class.getDeclaredMethod("getAppendService", SpecificModuleCtx.class);
        method.setAccessible(true);
        return (DealDetailDisplayUnitVO) method.invoke(specificModuleHandler_1205, ctx);
    }

    private DealDetailDisplayUnitVO invokeGetClassNum(SpecificModuleCtx context) throws Exception {
        Method method = SpecificModuleHandler_1205.class.getDeclaredMethod("getClassNum", SpecificModuleCtx.class);
        method.setAccessible(true);
        return (DealDetailDisplayUnitVO) method.invoke(specificModuleHandler_1205, context);
    }

    @Test
    public void testGetSuitableProvinceContextIsNull() throws Throwable {
        try {
            invokeGetSuitableProvince(null);
            fail("Expected NullPointerException to be thrown");
        } catch (Exception e) {
            assertTrue(e.getCause() instanceof NullPointerException);
        }
    }

    @Test
    public void testGetSuitableProvinceDealGroupDTOIsNull() throws Throwable {
        SpecificModuleCtx ctx = new SpecificModuleCtx();
        ctx.setDealGroupDTO(null);
        DealDetailDisplayUnitVO result = invokeGetSuitableProvince(ctx);
        assertNull(result);
    }

    @Test
    public void testGetSuitableProvinceAttrsIsNull() throws Throwable {
        SpecificModuleCtx ctx = new SpecificModuleCtx();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setAttrs(null);
        ctx.setDealGroupDTO(dealGroupDTO);
        DealDetailDisplayUnitVO result = invokeGetSuitableProvince(ctx);
        assertNull(result);
    }

    @Test
    public void testGetSuitableProvinceAttrNotExist() throws Throwable {
        SpecificModuleCtx ctx = new SpecificModuleCtx();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setAttrs(Lists.newArrayList());
        ctx.setDealGroupDTO(dealGroupDTO);
        DealDetailDisplayUnitVO result = invokeGetSuitableProvince(ctx);
        assertNull(result);
    }

    @Test
    public void testGetSuitableProvinceAttrValueIsNull() throws Throwable {
        SpecificModuleCtx ctx = new SpecificModuleCtx();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("适用省份");
        attrDTO.setValue(null);
        dealGroupDTO.setAttrs(Lists.newArrayList(attrDTO));
        ctx.setDealGroupDTO(dealGroupDTO);
        DealDetailDisplayUnitVO result = invokeGetSuitableProvince(ctx);
        assertNull(result);
    }

    @Test
    public void testGetSuitablePeopleAttrsIsNull() throws Throwable {
        when(dealGroupDTO.getAttrs()).thenReturn(null);
        SpecificModuleCtx ctx = new SpecificModuleCtx();
        ctx.setDealGroupDTO(dealGroupDTO);
        DealDetailDisplayUnitVO result = (DealDetailDisplayUnitVO) getSuitablePeopleMethod.invoke(specificModuleHandler_1205, ctx);
        assertNull(result);
    }

    @Test
    public void testGetSuitablePeopleAttrNotExist() throws Throwable {
        when(dealGroupDTO.getAttrs()).thenReturn(Collections.emptyList());
        SpecificModuleCtx ctx = new SpecificModuleCtx();
        ctx.setDealGroupDTO(dealGroupDTO);
        DealDetailDisplayUnitVO result = (DealDetailDisplayUnitVO) getSuitablePeopleMethod.invoke(specificModuleHandler_1205, ctx);
        assertNull(result);
    }

    @Test
    public void testGetSuitablePeopleAttrValueIsNull() throws Throwable {
        when(attrDTO.getName()).thenReturn("for_the_crowd");
        when(attrDTO.getValue()).thenReturn(null);
        when(dealGroupDTO.getAttrs()).thenReturn(Arrays.asList(attrDTO));
        SpecificModuleCtx ctx = new SpecificModuleCtx();
        ctx.setDealGroupDTO(dealGroupDTO);
        DealDetailDisplayUnitVO result = (DealDetailDisplayUnitVO) getSuitablePeopleMethod.invoke(specificModuleHandler_1205, ctx);
        assertNull(result);
    }

    @Test
    public void testGetSuitablePeopleAttrValueIsNotNull() throws Throwable {
        when(attrDTO.getName()).thenReturn("for_the_crowd");
        when(attrDTO.getValue()).thenReturn(Arrays.asList("Test"));
        when(dealGroupDTO.getAttrs()).thenReturn(Arrays.asList(attrDTO));
        SpecificModuleCtx ctx = new SpecificModuleCtx();
        ctx.setDealGroupDTO(dealGroupDTO);
        DealDetailDisplayUnitVO result = (DealDetailDisplayUnitVO) getSuitablePeopleMethod.invoke(specificModuleHandler_1205, ctx);
        assertNotNull(result);
        assertEquals("适用人群", result.getTitle());
        assertEquals("Test", result.getDisplayItems().get(0).getDetail());
    }

    @Test(expected = NullPointerException.class)
    public void testGetAppendServiceWhenContextIsNull() throws Throwable {
        try {
            invokeGetAppendService(null);
        } catch (Exception e) {
            throw e.getCause();
        }
    }

    @Test
    public void testGetAppendServiceWhenDealGroupDTOIsNull() throws Throwable {
        SpecificModuleCtx ctx = new SpecificModuleCtx();
        ctx.setDealGroupDTO(null);
        DealDetailDisplayUnitVO result = invokeGetAppendService(ctx);
        assertNull(result);
    }

    @Test
    public void testGetAppendServiceWhenAttrsIsEmpty() throws Throwable {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setAttrs(Collections.emptyList());
        SpecificModuleCtx ctx = createContextWithDealGroupDTO(dealGroupDTO);
        DealDetailDisplayUnitVO result = invokeGetAppendService(ctx);
        assertNull(result);
    }

    @Test
    public void testGetAppendServiceWhenAppendServicesIsEmpty() throws Throwable {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setAttrs(Arrays.asList(new AttrDTO()));
        SpecificModuleCtx ctx = createContextWithDealGroupDTO(dealGroupDTO);
        DealDetailDisplayUnitVO result = invokeGetAppendService(ctx);
        assertNull(result);
    }

    @Test(expected = NullPointerException.class)
    public void testGetClassNumWhenContextIsNull() throws Throwable {
        try {
            invokeGetClassNum(null);
        } catch (Exception e) {
            if (e.getCause() instanceof NullPointerException) {
                throw (NullPointerException) e.getCause();
            }
            throw e;
        }
    }

    @Test
    public void testGetClassNumWhenDealGroupDTOIsNull() throws Throwable {
        SpecificModuleCtx context = new SpecificModuleCtx();
        assertNull(invokeGetClassNum(context));
    }

    @Test
    public void testGetClassNumWhenAttrsIsEmpty() throws Throwable {
        SpecificModuleCtx context = new SpecificModuleCtx();
        context.setDealGroupDTO(new DealGroupDTO());
        assertNull(invokeGetClassNum(context));
    }

    @Test
    public void testGetClassNumWhenAttrsHasNoCoursePlanAttr() throws Throwable {
        SpecificModuleCtx context = new SpecificModuleCtx();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setAttrs(Collections.singletonList(new AttrDTO()));
        context.setDealGroupDTO(dealGroupDTO);
        assertNull(invokeGetClassNum(context));
    }

    @Test
    public void testGetClassNumWhenCoursePlanAttrValueIsNull() throws Throwable {
        SpecificModuleCtx context = new SpecificModuleCtx();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("course_plan");
        attrDTO.setValue(null);
        dealGroupDTO.setAttrs(Collections.singletonList(attrDTO));
        context.setDealGroupDTO(dealGroupDTO);
        assertNull(invokeGetClassNum(context));
    }

    @Test
    public void testGetClassNumWhenCoursePlanAttrValueIsNotNullButEduCoursePlanListIsEmpty() throws Throwable {
        SpecificModuleCtx context = new SpecificModuleCtx();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("course_plan");
        attrDTO.setValue(Collections.singletonList("[]"));
        dealGroupDTO.setAttrs(Collections.singletonList(attrDTO));
        context.setDealGroupDTO(dealGroupDTO);
        assertNull(invokeGetClassNum(context));
    }

    @Test
    public void testGetClassNumWhenCoursePlanAttrValueIsNotNullAndEduCoursePlanListIsNotEmptyButCourseTimeNumIsNull() throws Throwable {
        SpecificModuleCtx context = new SpecificModuleCtx();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("course_plan");
        attrDTO.setValue(Collections.singletonList("[{\"courseTimeNum\":null}]"));
        dealGroupDTO.setAttrs(Collections.singletonList(attrDTO));
        context.setDealGroupDTO(dealGroupDTO);
        DealDetailDisplayUnitVO result = invokeGetClassNum(context);
        assertNotNull(result);
        assertEquals("0", result.getDisplayItems().get(0).getDetail());
    }

    // Test when DealGroupDTO is null
    @Test
    public void testGetClassTypeWhenDealGroupDTOIsNull() throws Throwable {
        when(context.getDealGroupDTO()).thenReturn(null);
        Method getClassTypeMethod = SpecificModuleHandler_1205.class.getDeclaredMethod("getClassType", SpecificModuleCtx.class);
        getClassTypeMethod.setAccessible(true);
        assertNull(getClassTypeMethod.invoke(specificModuleHandler_1205, context));
    }

    // Test when attrs is empty
    @Test
    public void testGetClassTypeWhenAttrsIsEmpty() throws Throwable {
        when(context.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getAttrs()).thenReturn(Collections.emptyList());
        Method getClassTypeMethod = SpecificModuleHandler_1205.class.getDeclaredMethod("getClassType", SpecificModuleCtx.class);
        getClassTypeMethod.setAccessible(true);
        assertNull(getClassTypeMethod.invoke(specificModuleHandler_1205, context));
    }

    // Test when no attrName is eduClassType
    @Test
    public void testGetClassTypeWhenNoAttrNameIsEduClassType() throws Throwable {
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("other_attr");
        attrDTO.setValue(Collections.singletonList("some_value"));
        when(context.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getAttrs()).thenReturn(Collections.singletonList(attrDTO));
        Method getClassTypeMethod = SpecificModuleHandler_1205.class.getDeclaredMethod("getClassType", SpecificModuleCtx.class);
        getClassTypeMethod.setAccessible(true);
        assertNull(getClassTypeMethod.invoke(specificModuleHandler_1205, context));
    }
}
