package com.dianping.mobile.mapi.dztgdetail.biz.service;


import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.PriceDisplayService;
import com.sankuai.dealuser.price.display.api.model.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;

import static com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum.MEITUAN_APP;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
/**
 * 单元测试类：PriceDisplayLocalService
 */
@RunWith(MockitoJUnitRunner.class)
public class PriceDisplayLocalServiceTest {

    @Mock
    private PriceDisplayService priceDisplayService;

    @InjectMocks
    private PriceDisplayLocalService priceDisplayLocalService;
    private DztgClientTypeEnum dztgClientTypeEnum = MEITUAN_APP;
    private PriceRequest priceRequest;
    private BatchPriceRequest batchPriceRequest;
    private PriceDisplayDTO priceDisplayDTO;
    private DealCtx ctx;
    private final int successCode = 200;
    private MockedStatic<Lion> mocked;
    private Map<String, Integer> samplingRate = new HashMap<>();

    @Mock
    private DouHuBiz douHuBiz;


    @Before
    public void setUp() {
        priceDisplayDTO = new PriceDisplayDTO();
        ctx = new DealCtx(new EnvCtx());
        mocked = Mockito.mockStatic(Lion.class);
        mocked.when(() -> Lion.getBoolean("com.sankuai.dzu.tpbase.dztgdetailweb", LionConstants.PRICE_DISPLAY_LOG_ENABLED, true)).thenReturn(true);
        samplingRate.put(String.valueOf(dztgClientTypeEnum.getCode()), 100);
        mocked.when(() -> Lion.getMap("com.sankuai.dzu.tpbase.dztgdetailweb", LionConstants.TEMPLATE_SCENE_LOG_SAMPLING_RATE, Integer.class)).thenReturn(samplingRate);
    }

    @After
    public void tearDown() {
        mocked.close();
    }

    /**
     * 测试 queryPrice 方法，正常场景
     */
    @Test
    public void testQueryPriceNormal() throws Throwable {
        // arrange
        priceRequest = new PriceRequest();
        priceRequest.setLongShopId(-1L);
        ctx.getEnvCtx().setDztgClientTypeEnum(dztgClientTypeEnum);
        ctx.setMtId(1);
        when(priceDisplayService.queryPrice(any(PriceRequest.class))).thenReturn(new PriceResponse<PriceDisplayDTO>(200, "success", priceDisplayDTO));
        
        // act
        PriceResponse<PriceDisplayDTO> actualResponse = priceDisplayLocalService.queryPrice(priceRequest, ctx);

        // assert
        assertEquals(actualResponse.getCode(), successCode);
    }

    /**
     * 测试 queryPrice 方法，请求为 null
     */
    @Test(expected = NullPointerException.class)
    public void testQueryPriceRequestNull() throws Throwable {
        // act
        PriceResponse<PriceDisplayDTO> actualResponse = priceDisplayLocalService.queryPrice(null, null);
        // assert
        assertEquals(actualResponse.getCode(), successCode);
    }

    /**
     * 测试 batchQueryPriceByLongShopId 方法，正常场景
     */
    @Test
    public void testBatchQueryPriceByLongShopIdNormal() throws Throwable {
        // arrange
        batchPriceRequest = new BatchPriceRequest();
        batchPriceRequest.setLongShopId2ProductIds(new HashMap<Long, List<ProductIdentity>>(){{
            put(-1L, null);
        }});
        ctx.getEnvCtx().setDztgClientTypeEnum(dztgClientTypeEnum);
        ctx.setMtId(1);

        when(priceDisplayService.batchQueryPriceByLongShopId(any(BatchPriceRequest.class))).thenReturn(new PriceResponse<>(200, "success", new HashMap<Long, List<PriceDisplayDTO>>(){{
            put(1L, Arrays.asList(priceDisplayDTO));
        }}));

        // act
        PriceResponse<Map<Long, List<PriceDisplayDTO>>> actualResponse = priceDisplayLocalService.batchQueryPriceByLongShopId(batchPriceRequest, ctx);

        // assert
        assertEquals(actualResponse.getCode(), successCode);
    }

    /**
     * 测试 batchQueryPriceByLongShopId 方法，请求为 null
     */
    @Test(expected = NullPointerException.class)
    public void testBatchQueryPriceByLongShopIdRequestNull() throws Throwable {
        // act
        PriceResponse<Map<Long, List<PriceDisplayDTO>>> actualResponse = priceDisplayLocalService.batchQueryPriceByLongShopId(null, null);
        // assert
        assertEquals(actualResponse.getCode(), successCode);

    }

    @Test
    public void testSetDouHuModuleAbConfig_ModuleAbConfigs_Empty()
            throws InvocationTargetException, IllegalAccessException {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(MEITUAN_APP);
        ctx = new DealCtx(envCtx);

        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        when(douHuBiz.getAbExpResult(ctx, "MtCouponAlleviate2Exp")).thenReturn(moduleAbConfig);

        Method method = PowerMockito.method(PriceDisplayLocalService.class, "setCouponAlleviate2AbConfig");
        method.invoke(priceDisplayLocalService, ctx);
        assertTrue(ctx.getModuleAbConfigs().size() > 0);

    }

    @Test
    public void testSetDouHuModuleAbConfig_ModuleAbConfigs_NotEmpty()
            throws InvocationTargetException, IllegalAccessException {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(MEITUAN_APP);
        ctx = new DealCtx(envCtx);

        ctx.setModuleAbConfigs(Lists.newArrayList(new ModuleAbConfig()));

        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        when(douHuBiz.getAbExpResult(ctx, "MtCouponAlleviate2Exp")).thenReturn(moduleAbConfig);

        Method method = PowerMockito.method(PriceDisplayLocalService.class, "setCouponAlleviate2AbConfig");
        method.invoke(priceDisplayLocalService, ctx);
        assertTrue(ctx.getModuleAbConfigs().size() > 0);

    }

    // getCouponAlleviate1ExpResult
}