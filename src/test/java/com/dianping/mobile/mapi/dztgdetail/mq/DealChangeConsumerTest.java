package com.dianping.mobile.mapi.dztgdetail.mq;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MultiSkuExpBiz;
import com.dianping.mobile.mapi.dztgdetail.entity.SkuSummary;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealChangeConsumerTest {

    @InjectMocks
    private DealChangeConsumer dealChangeConsumer;

    @Mock
    private DealGroupQueryService queryCenterDealGroupQueryService;

    @Mock
    private MultiSkuExpBiz multiSkuExpBiz;

    /**
     * Test when message is null
     */
    @Test
    public void testRecvMessage_WhenMessageIsNull() throws Throwable {
        // arrange
        MafkaMessage message = null;
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = dealChangeConsumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test when message body is empty
     */
    @Test
    public void testRecvMessage_WhenMessageBodyIsEmpty() throws Throwable {
        // arrange
        MafkaMessage message = new MafkaMessage("topic", 0, 0, null, "");
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = dealChangeConsumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test when dealGroupId is invalid (<=0)
     */
    @Test
    public void testRecvMessage_WhenDealGroupIdIsInvalid() throws Throwable {
        // arrange
        String messageBody = "{\"dealGroupID\":0,\"sourceId\":100}";
        MafkaMessage message = new MafkaMessage("topic", 0, 0, null, messageBody);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = dealChangeConsumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test when sourceId is 200 (餐团单)
     */
    @Test
    public void testRecvMessage_WhenSourceIdIs200() throws Throwable {
        // arrange
        String messageBody = "{\"dealGroupID\":123,\"sourceId\":200}";
        MafkaMessage message = new MafkaMessage("topic", 0, 0, null, messageBody);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = dealChangeConsumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test when not target category
     */
    @Test
    public void testRecvMessage_WhenNotTargetCategory() throws Throwable {
        // arrange
        String messageBody = "{\"dealGroupID\":123,\"sourceId\":100}";
        MafkaMessage message = new MafkaMessage("topic", 0, 0, null, messageBody);
        MessagetContext context = new MessagetContext();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId(100L);
        dealGroupDTO.setCategory(categoryDTO);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(null);
        // act
        ConsumeStatus result = dealChangeConsumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }
}
