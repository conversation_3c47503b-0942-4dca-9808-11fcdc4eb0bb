package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.google.common.collect.Maps;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @author: wuwenqiang
 * @create: 2024-11-28
 * @description:
 */
@RunWith(MockitoJUnitRunner.class)
public class ModuleUtilsTest {
    /**
     * 测试映射为空时返回空列表
     */
    @Test
    public void testGetModuleConfigsSupportMultiCatMapIsEmpty() throws Throwable {
        // arrange
        Map<String, List<ModuleConfigDo>> moduleConfigMaps = Maps.newHashMap();
        String moduleConfigKey = "testKey";

        // act
        List<ModuleConfigDo> result = ModuleUtils.getModuleConfigsSupportMultiCat(moduleConfigMaps, moduleConfigKey);

        // assert
        Assert.assertTrue(result.isEmpty());
    }

    /**
     * 测试映射中的键为空时跳过该键
     */
    @Test
    public void testGetModuleConfigsSupportMultiCatKeyIsEmpty() throws Throwable {
        // arrange
        Map<String, List<ModuleConfigDo>> moduleConfigMaps = Maps.newHashMap();
        moduleConfigMaps.put("", Arrays.asList(new ModuleConfigDo()));
        String moduleConfigKey = "testKey";

        // act
        List<ModuleConfigDo> result = ModuleUtils.getModuleConfigsSupportMultiCat(moduleConfigMaps, moduleConfigKey);

        // assert
        Assert.assertTrue(result.isEmpty());
    }

    /**
     * 测试未找到匹配的键时返回空列表
     */
    @Test
    public void testGetModuleConfigsSupportMultiCatKeyNotFound() throws Throwable {
        // arrange
        Map<String, List<ModuleConfigDo>> moduleConfigMaps = Maps.newHashMap();
        moduleConfigMaps.put("key1,key2", Arrays.asList(new ModuleConfigDo()));
        String moduleConfigKey = "testKey";

        // act
        List<ModuleConfigDo> result = ModuleUtils.getModuleConfigsSupportMultiCat(moduleConfigMaps, moduleConfigKey);

        // assert
        Assert.assertTrue(result.isEmpty());
    }

    /**
     * 测试正常场景，键包含指定的moduleConfigKey
     */
    @Test
    public void testGetModuleConfigsSupportMultiCatNormal() throws Throwable {
        // arrange
        ModuleConfigDo moduleConfigDo = new ModuleConfigDo();
        Map<String, List<ModuleConfigDo>> moduleConfigMaps = Maps.newHashMap();
        moduleConfigMaps.put("testKey,anotherKey", Arrays.asList(moduleConfigDo));
        String moduleConfigKey = "testKey";

        // act
        List<ModuleConfigDo> result = ModuleUtils.getModuleConfigsSupportMultiCat(moduleConfigMaps, moduleConfigKey);

        // assert
        Assert.assertFalse(result.isEmpty());
        Assert.assertEquals(moduleConfigDo, result.get(0));
    }
}
