package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import junit.framework.TestCase;
import org.junit.runner.RunWith;
import org.junit.Test;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/2/27 20:25
 */
@RunWith(MockitoJUnitRunner.class)
public class MassageHighlightsV1ProcessorTest extends TestCase {
    @Test
    public void testGetNewFoodDataBySkuAttrItem_EmptyList() throws Throwable {
        // arrange
        List<SkuAttrItemDto> serviceProjectAttrs = new ArrayList<>();

        // act
        String result = MassageHighlightsV1Processor.getNewFoodDataBySkuAttrItem(serviceProjectAttrs);

        // assert
        assertEquals(null, result);
    }

    @Test
    public void testGetNewFoodDataBySkuAttrItem_NotInFoodNewData() throws Throwable {
        // arrange
        List<SkuAttrItemDto> serviceProjectAttrs = new ArrayList<>();
        SkuAttrItemDto item = new SkuAttrItemDto();
        item.setAttrName("freeFood");
        item.setAttrValue("NotInFoodNewData");
        serviceProjectAttrs.add(item);

        // act
        String result = MassageHighlightsV1Processor.getNewFoodDataBySkuAttrItem(serviceProjectAttrs);

        // assert
        assertEquals(null, result);
    }

    @Test
    public void testGetNewFoodDataBySkuAttrItem_InFoodNewDataButNotTeaFruit() throws Throwable {
        // arrange
        List<SkuAttrItemDto> serviceProjectAttrs = new ArrayList<>();
        SkuAttrItemDto item = new SkuAttrItemDto();
        item.setAttrName("freeFood");
        item.setAttrValue("InFoodNewDataButNotTeaFruit");
        serviceProjectAttrs.add(item);

        // act
        String result = MassageHighlightsV1Processor.getNewFoodDataBySkuAttrItem(serviceProjectAttrs);

        // assert
        assertNull(result);
    }

    @Test
    public void testGetNewFoodDataBySkuAttrItem_TeaFruitWithFruit() throws Throwable {
        // arrange
        List<SkuAttrItemDto> serviceProjectAttrs = new ArrayList<>();
        SkuAttrItemDto item1 = new SkuAttrItemDto();
        item1.setAttrName("freeFood");
        item1.setAttrValue("茶点水果");
        serviceProjectAttrs.add(item1);
        SkuAttrItemDto item2 = new SkuAttrItemDto();
        item2.setAttrName("Fruit");
        item2.setAttrValue("FruitValue");
        serviceProjectAttrs.add(item2);

        // act
        String result = MassageHighlightsV1Processor.getNewFoodDataBySkuAttrItem(serviceProjectAttrs);

        // assert
        assertEquals("茶点水果", result);
    }

    @Test
    public void testGetNewFoodDataBySkuAttrItem_TeaFruitWithoutFruit() throws Throwable {
        // arrange
        List<SkuAttrItemDto> serviceProjectAttrs = new ArrayList<>();
        SkuAttrItemDto item = new SkuAttrItemDto();
        item.setAttrName("freeFood");
        item.setAttrValue("茶点水果");
        serviceProjectAttrs.add(item);

        // act
        String result = MassageHighlightsV1Processor.getNewFoodDataBySkuAttrItem(serviceProjectAttrs);

        // assert
        assertEquals("茶点", result);
    }
}