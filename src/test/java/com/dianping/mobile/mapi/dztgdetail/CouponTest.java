/*
package com.dianping.mobile.mapi.dztgdetail;
import com.dianping.mobile.mapi.dztgdetail.util.GsonUtils;
import com.dianping.tgc.open.entity.*;

import java.util.ArrayList;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.sankuai.trade.general.reserve.response.ReserveResponse;
import com.sankuai.trade.general.reserve.service.ShopAndProductReserveStatusService;
import org.junit.Test;

import javax.annotation.Resource;

*/
/**
 * <AUTHOR>
 * @date 2021-04-15-4:43 下午
 *//*

public class CouponTest extends GenericTest {
    @Resource
    private ShopAndProductReserveStatusService shopAndProductReserveStatusService;

    @Test
    public void testTagRes(){


        List<PromoExposureInfo> promoExposureInfoList = new ArrayList<>();
        PromoExposureInfo promoExposureInfo = new PromoExposureInfo();
        promoExposureInfo.setFlowId(0L);
        promoExposureInfo.setResourceActivityId(0L);
        promoExposureInfo.setActivityId(0L);
        promoExposureInfo.setMaterialId("");
        promoExposureInfo.setRowKey("");
        promoExposureInfo.setCouponType(0);
        promoExposureInfo.setAmount("");
        promoExposureInfo.setTitle("");
        promoExposureInfo.setCouponValueType(0L);
        promoExposureInfo.setSubTitle("");
        promoExposureInfo.setCanAssign(false);
        promoExposureInfo.setTimeDesc("");
        promoExposureInfo.setTimeSubDesc("");
        promoExposureInfo.setUseEndTime(0L);
        promoExposureInfoList.add(promoExposureInfo);

        List<PromoCouponInfo> promoCouponInfoList = new ArrayList<>();
        PromoCouponInfo promoCouponInfo = new PromoCouponInfo();
        promoCouponInfo.setCouponGroupId(0L);
        promoCouponInfo.setStatus(0);
        promoCouponInfo.setTitle("");
        promoCouponInfo.setAmount("");
        promoCouponInfo.setAmountCornerMark("");
        promoCouponInfo.setTimeDesc("");
        promoCouponInfo.setAmountDesc("");

        PromoCouponButton promoCouponButton = new PromoCouponButton();
        promoCouponButton.setTitle("button");
        promoCouponButton.setClickUrl("");
        promoCouponButton.setActionType(0);

        promoCouponInfo.setPromoCouponButton(promoCouponButton);
        promoCouponInfoList.add(promoCouponInfo);

        List<PromoReturnInfo> promoReturnInfoList = new ArrayList<>();
        PromoReturnInfo promoReturnInfo = new PromoReturnInfo();
        promoReturnInfo.setText("");
        promoReturnInfo.setStyle(0);
        promoReturnInfoList.add(promoReturnInfo);

        BatchExProxyCouponResponseDTO batchExProxyCouponResponseDTO = new BatchExProxyCouponResponseDTO();
        batchExProxyCouponResponseDTO.setPromoExposureInfoList(promoExposureInfoList);
        batchExProxyCouponResponseDTO.setPromoCouponInfoList(promoCouponInfoList);
        batchExProxyCouponResponseDTO.setPromoReturnInfoList(promoReturnInfoList);

        String str = GsonUtils.toJsonString(batchExProxyCouponResponseDTO);

        System.out.println(str);



    }

    private BatchExProxyCouponResponseDTO buildBatchProxyCouponResponseDTO() {
        List<PromoExposureInfo> promoExposureInfoList = new ArrayList<>();
        PromoExposureInfo promoExposureInfo = new PromoExposureInfo();
        promoExposureInfo.setFlowId(0L);
        promoExposureInfo.setResourceActivityId(0L);
        promoExposureInfo.setActivityId(0L);
        promoExposureInfo.setMaterialId("");
        promoExposureInfo.setRowKey("");
        promoExposureInfo.setCouponType(0);
        promoExposureInfo.setAmount("");
        promoExposureInfo.setTitle("");
        promoExposureInfo.setCouponValueType(0L);
        promoExposureInfo.setSubTitle("");
        promoExposureInfo.setCanAssign(false);
        promoExposureInfo.setTimeDesc("");
        promoExposureInfo.setTimeSubDesc("");
        promoExposureInfo.setUseEndTime(0L);
        promoExposureInfoList.add(promoExposureInfo);

        List<PromoCouponInfo> promoCouponInfoList = new ArrayList<>();
        PromoCouponInfo promoCouponInfo = new PromoCouponInfo();
        promoCouponInfo.setCouponGroupId(0L);
        promoCouponInfo.setStatus(0);
        promoCouponInfo.setTitle("");
        promoCouponInfo.setAmount("");
        promoCouponInfo.setAmountCornerMark("");
        promoCouponInfo.setTimeDesc("");
        promoCouponInfo.setAmountDesc("");

        PromoCouponButton promoCouponButton = new PromoCouponButton();
        promoCouponButton.setTitle("button");
        promoCouponButton.setClickUrl("");
        promoCouponButton.setActionType(0);

        promoCouponInfo.setPromoCouponButton(promoCouponButton);
        promoCouponInfoList.add(promoCouponInfo);

        List<PromoReturnInfo> promoReturnInfoList = new ArrayList<>();
        PromoReturnInfo promoReturnInfo = new PromoReturnInfo();
        promoReturnInfo.setText("");
        promoReturnInfo.setStyle(0);
        promoReturnInfoList.add(promoReturnInfo);

        BatchExProxyCouponResponseDTO batchExProxyCouponResponseDTO = new BatchExProxyCouponResponseDTO();
        batchExProxyCouponResponseDTO.setPromoExposureInfoList(promoExposureInfoList);
        batchExProxyCouponResponseDTO.setPromoCouponInfoList(promoCouponInfoList);
        batchExProxyCouponResponseDTO.setPromoReturnInfoList(promoReturnInfoList);

        return batchExProxyCouponResponseDTO;
    }
}*/
