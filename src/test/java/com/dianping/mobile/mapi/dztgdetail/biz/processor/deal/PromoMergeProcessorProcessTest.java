package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PromoMergeWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.pay.promo.reception.service.dto.PromoMergeDTO;
import java.util.concurrent.Future;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PromoMergeProcessorProcessTest {

    @InjectMocks
    private PromoMergeProcessor promoMergeProcessor;

    @Mock
    private PromoMergeWrapper promoMergeWrapper;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private FutureCtx futureCtx;

    @Mock
    private Future<PromoMergeDTO> promoMergeFuture;

    @Mock
    private PromoMergeDTO promoMergeDTO;

    @Before
    public void setUp() {
        when(dealCtx.getFutureCtx()).thenReturn(futureCtx);
        when(futureCtx.getPromoMergeFuture()).thenReturn(promoMergeFuture);
    }

    /**
     * Test successful processing with valid promo merge info
     */
    @Test
    public void testProcess_WithValidPromoMergeInfo() throws Throwable {
        // arrange
        when(promoMergeWrapper.loadPromoMergeInfo(promoMergeFuture)).thenReturn(promoMergeDTO);
        // act
        promoMergeProcessor.process(dealCtx);
        // assert
        verify(dealCtx).setPromoMerge(promoMergeDTO);
        verify(promoMergeWrapper).loadPromoMergeInfo(promoMergeFuture);
    }

    /**
     * Test processing when promo merge info is null
     */
    @Test
    public void testProcess_WhenPromoMergeInfoIsNull() throws Throwable {
        // arrange
        when(promoMergeWrapper.loadPromoMergeInfo(promoMergeFuture)).thenReturn(null);
        // act
        promoMergeProcessor.process(dealCtx);
        // assert
        verify(dealCtx).setPromoMerge(null);
        verify(promoMergeWrapper).loadPromoMergeInfo(promoMergeFuture);
    }

    /**
     * Test processing when promo merge future is null
     */
    @Test
    public void testProcess_WhenPromoMergeFutureIsNull() throws Throwable {
        // arrange
        when(futureCtx.getPromoMergeFuture()).thenReturn(null);
        // act
        promoMergeProcessor.process(dealCtx);
        // assert
        verify(dealCtx).setPromoMerge(null);
        verify(promoMergeWrapper).loadPromoMergeInfo(null);
    }
}
