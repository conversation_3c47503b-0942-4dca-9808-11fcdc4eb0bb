package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.lion.client.Lion;
import com.sankuai.general.product.query.center.client.dto.deal.DealBasicDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mockStatic;

@RunWith(MockitoJUnitRunner.class)
public class StockSalesProcessorTest {

    private StockSalesProcessor stockSalesProcessor = new StockSalesProcessor();

    private MockedStatic<Lion> lionMockedStatic;
    @Before
    public void setUp() {
        lionMockedStatic = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        lionMockedStatic.close();
    }

    @Test
    public void testFilterByStatusWhenLionIsFalse() throws Throwable {
        lionMockedStatic.when(() -> Lion.getBoolean("APP_KEY", "FILTER_STATUS_FOR_MULTI_SKU", false)).thenReturn(false);
        assertTrue("Expected to allow due to Lion config false", stockSalesProcessor.filterByStatus(new DealGroupDealDTO()));
    }

    @Test
    public void testFilterByStatusWhenStatusIsOne() throws Throwable {
        lionMockedStatic.when(() -> Lion.getBoolean("APP_KEY", "FILTER_STATUS_FOR_MULTI_SKU", false)).thenReturn(true);
        DealGroupDealDTO dealGroupDealDTO = new DealGroupDealDTO();
        DealBasicDTO dealBasicDTO = new DealBasicDTO();
        dealBasicDTO.setStatus(1);
        dealGroupDealDTO.setBasic(dealBasicDTO);
        assertTrue("Expected to allow when status is 1", stockSalesProcessor.filterByStatus(dealGroupDealDTO));
    }
}
