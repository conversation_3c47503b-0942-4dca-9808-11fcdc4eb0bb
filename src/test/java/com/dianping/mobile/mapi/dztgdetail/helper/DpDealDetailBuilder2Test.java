package com.dianping.mobile.mapi.dztgdetail.helper;

import static org.junit.Assert.assertEquals;
import com.alibaba.fastjson.JSON;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.detail.dto.DealGroupDetailDTO;
import com.dianping.deal.detail.dto.DealGroupTemplateDetailDTO;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DpProductType;
import com.dianping.mobile.mapi.dztgdetail.common.enums.FreeDealEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.Pair;
import com.dianping.mobile.mapi.dztgdetail.entity.PurchaseNoteStructuredConfig;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * @Author: <EMAIL>
 * @Date: 2024/1/19
 */
@RunWith(MockitoJUnitRunner.class)
public class DpDealDetailBuilder2Test {

    private DpDealDetailBuilder2 dpDealDetailBuilder2;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private DealGroupDetailDTO dealGroupDetailDto;

    @Mock
    private DealGroupBaseDTO dealGroupBaseDto;

    @Before
    public void setUp() {
        dpDealDetailBuilder2 = new DpDealDetailBuilder2();
        dpDealDetailBuilder2.setDealGroupDetailDto(dealGroupDetailDto);
        dpDealDetailBuilder2.setDealGroupBaseDto(dealGroupBaseDto);
    }

    /**
     * 测试 map 为 null 的情况
     */
    @Test
    public void testInitMap() {
        dpDealDetailBuilder2.setMap(null);
        // act
        dpDealDetailBuilder2.initMap(dealCtx);
        // assert
        assertEquals(0, dpDealDetailBuilder2.getMap().size());
    }

    @Test
    public void testRemoveToMap() {
        // arrange
        Map<Integer, Pair> map = new HashMap<>();
        map.put(2, new Pair());
        dpDealDetailBuilder2.setMap(map);
        PurchaseNoteStructuredConfig config = JSON.parseObject("{\"grayLevel\":1,\"enableClientType\":[1,2,4,5],\"dealGroupIds\":[42831621,42715386],\"category2ExpId\":{\"mt502\":\"exp002675\",\"dp502\":\"exp002676\",\"mt303\":\"exp001872\",\"dp303\":\"exp001891\"},\"requestUnStructuredContent\":false}", PurchaseNoteStructuredConfig.class);
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setPNSConfig(config);
        // act
        dpDealDetailBuilder2.removeToMap(ctx);
        // assert
        assertEquals(0, dpDealDetailBuilder2.getMap().size());
    }

    private EnvCtx createEnvCtx() {
        // 创建 EnvCtx 实例的逻辑，根据实际情况填充属性
        return new EnvCtx();
    }

    private DealCtx createDealCtxWithEnvCtx() {
        // 使用带有 EnvCtx 参数的构造函数创建 DealCtx 实例
        return new DealCtx(createEnvCtx());
    }

    @Test(expected = NullPointerException.class)
    public void testToStructedDetails_WhenHasStructedDetailTrueAndDealCtxNull() {
        DpDealDetailBuilder2 builder = new DpDealDetailBuilder2();
        builder.toStructedDetails(true, null);
    }
}
