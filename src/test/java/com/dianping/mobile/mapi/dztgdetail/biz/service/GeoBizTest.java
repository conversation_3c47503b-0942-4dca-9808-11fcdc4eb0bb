package com.dianping.mobile.mapi.dztgdetail.biz.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.geo.map.CoordTransferService;
import com.dianping.geo.map.entity.CoordType;
import com.dianping.geo.map.entity.GeoPoint;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class GeoBizTest {

    @InjectMocks
    private GeoBiz geoBiz;

    @Mock
    private CoordTransferService coordTransferService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 coordTranfer 方法，正常情况
     */
    @Test
    public void testCoordTranferNormal() throws Throwable {
        // arrange
        GeoPoint point = new GeoPoint(30.0, 120.0);
        CoordType oldType = CoordType.GPS;
        CoordType targetType = CoordType.GOOGLE;
        GeoPoint expected = new GeoPoint(30.0, 120.0);
        when(coordTransferService.coordTranfer(point, oldType, targetType)).thenReturn(expected);
        // act
        GeoPoint actual = geoBiz.coordTranfer(point, oldType, targetType);
        // assert
        assertEquals(expected, actual);
        verify(coordTransferService, times(1)).coordTranfer(point, oldType, targetType);
    }

    /**
     * 测试 coordTranfer 方法，异常情况
     */
    @Test
    public void testCoordTranferException() throws Throwable {
        // arrange
        GeoPoint point = new GeoPoint(30.0, 120.0);
        CoordType oldType = CoordType.GPS;
        CoordType targetType = CoordType.GOOGLE;
        when(coordTransferService.coordTranfer(point, oldType, targetType)).thenThrow(new RuntimeException());
        // act
        GeoPoint actual = geoBiz.coordTranfer(point, oldType, targetType);
        // assert
        assertNull(actual);
        verify(coordTransferService, times(1)).coordTranfer(point, oldType, targetType);
    }

    /**
     * 测试 gps2Google 方法，当 coordTranfer 方法抛出异常时，应返回 null
     */
    @Test
    public void testGps2GoogleWhenCoordTranferThrowsException() throws Throwable {
        // arrange
        GeoPoint point = new GeoPoint(0.0, 0.0);
        when(coordTransferService.coordTranfer(any(GeoPoint.class), eq(CoordType.GPS), eq(CoordType.GOOGLE))).thenThrow(new RuntimeException());
        // act
        GeoPoint result = geoBiz.gps2Google(point);
        // assert
        assertNull(result);
    }
}
