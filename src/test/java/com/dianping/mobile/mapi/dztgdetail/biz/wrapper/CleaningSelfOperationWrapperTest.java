package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.lion.client.Lion;
import com.sankuai.fbi.lifeevent.reserverpcapi.dto.NewReserveSubmissionPageWhiteShopCheckRespDTO;
import com.sankuai.fbi.lifeevent.reserverpcapi.request.NewReserveSubmissionPageWhiteShopCheckRequest;
import com.sankuai.fbi.lifeevent.reserverpcapi.service.ReserveConfigQueryService;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @date 2025/5/22 12:33
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({Lion.class})
public class CleaningSelfOperationWrapperTest {

    @InjectMocks
    private CleaningSelfOperationWrapper cleaningSelfOperationWrapper;

    @Mock
    private ReserveConfigQueryService reserveConfigQueryService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(Lion.class);
    }

    @Test
    public void testIsCleaningSelfOperationShop() {
        NewReserveSubmissionPageWhiteShopCheckRespDTO respDTO = Mockito.mock(NewReserveSubmissionPageWhiteShopCheckRespDTO.class);
        when(respDTO.getCheckRes()).thenAnswer((Answer<Boolean>) item -> true);
        when(reserveConfigQueryService.checkIsNewReserveSubmissionPageWhiteShop(any(NewReserveSubmissionPageWhiteShopCheckRequest.class))).thenAnswer((Answer<NewReserveSubmissionPageWhiteShopCheckRespDTO>) invocations -> respDTO);
        boolean cleaningSelfOperationShop = cleaningSelfOperationWrapper.isCleaningSelfOperationShop(1234L);
        assert cleaningSelfOperationShop;
    }

}