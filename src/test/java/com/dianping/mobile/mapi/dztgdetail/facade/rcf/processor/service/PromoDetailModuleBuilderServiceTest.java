package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.PriceContext;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PriceDisplayModuleDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PromoDetailModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.DealGift;
import com.dianping.mobile.mapi.dztgdetail.entity.PrepayCategoryConfig;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.TimesDealUtil;
import com.dianping.pigeon.remoting.common.codec.json.JacksonUtils;
import com.meituan.mdp.boot.starter.util.MdpEnvUtils;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PricePowerTagDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PricePowerTagItem;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.BestPriceGuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PromoDetailModuleBuilderServiceTest {

    private DealCtx ctx;

    @InjectMocks
    private PromoDetailModuleBuilderService promoDetailModuleBuilderService;


    private MockedStatic<LionConfigUtils> lionConfigUtilsMockedStatic;
    private MockedStatic<MdpEnvUtils> mdpEnvUtilsMockedStatic;
    private MockedStatic<TimesDealUtil> timesDealUtilMockedStatic;
    private MockedStatic<DouHuService> douHuServiceMockedStatic;

    @Before
    public void setUp() {
        lionConfigUtilsMockedStatic = mockStatic(LionConfigUtils.class);
        mdpEnvUtilsMockedStatic = mockStatic(MdpEnvUtils.class);
        timesDealUtilMockedStatic = mockStatic(TimesDealUtil.class);
        douHuServiceMockedStatic = mockStatic(DouHuService.class);
        ctx = mock(DealCtx.class);
    }

    @After
    public void tearDown() {
        lionConfigUtilsMockedStatic.close();
        mdpEnvUtilsMockedStatic.close();
        timesDealUtilMockedStatic.close();
        douHuServiceMockedStatic.close();
    }

    /**
     * 测试场景：当pricePowerTagDisplayDTO为null时，方法应直接返回，不进行任何操作
     */
    @Test
    public void testSetPriceStrengthDesc_WithNullPricePowerTagDisplayDTO() {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        PromoDetailModule promoDetailModule = mock(PromoDetailModule.class);
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        // act
        promoDetailModuleBuilderService.setPriceStrengthDesc(ctx, promoDetailModule, priceDisplayDTO);

        // assert
        assertNull(promoDetailModule.getPriceStrengthDesc());
    }

    /**
     * 测试场景：当allTagList为空时，方法应直接返回，不进行任何操作
     */
    @Test
    public void testSetPriceStrengthDesc_WithEmptyAllTagList() {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        PromoDetailModule promoDetailModule = mock(PromoDetailModule.class);
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        PricePowerTagDisplayDTO pricePowerTagDisplayDTO = new PricePowerTagDisplayDTO();
        pricePowerTagDisplayDTO.setAllTagList(Collections.emptyList());
        priceDisplayDTO.setPricePowerTagDisplayDTO(pricePowerTagDisplayDTO);
        // act
        promoDetailModuleBuilderService.setPriceStrengthDesc(ctx, promoDetailModule, priceDisplayDTO);

        // assert
        assertNull(promoDetailModule.getPriceStrengthDesc());
    }

    /**
     * 测试场景：当publishCategory不匹配任何特定类别时，应正确处理并设置PriceStrengthDesc
     */
    @Test
    public void testSetPriceStrengthDesc_WithUnmatchedPublishCategory() {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        PricePowerTagDisplayDTO pricePowerTagDisplayDTO = new PricePowerTagDisplayDTO();
        List<PricePowerTagItem> allTagList = new ArrayList<>();
        PricePowerTagItem item = new PricePowerTagItem();
        item.setTagType(1);
        item.setTagName("Unmatched Tag");
        allTagList.add(item);
        pricePowerTagDisplayDTO.setAllTagList(allTagList);
        priceDisplayDTO.setPricePowerTagDisplayDTO(pricePowerTagDisplayDTO);
        when(ctx.getCategoryId()).thenReturn(999);

        // act
        promoDetailModuleBuilderService.setPriceStrengthDesc(ctx, promoDetailModule, priceDisplayDTO);

        // assert
        assert promoDetailModule.getPriceStrengthDesc().equals("Unmatched Tag");
    }

    @Test
    public void testHideLeadsDealPromoDetailInfo() {
        // arrange
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        promoDetailModule.setDealGroupPrice("100");
        promoDetailModule.setPromoPrice("80.22");
        promoDetailModule.setTotalPromo("立减19.78");
        promoDetailModule.setPricePostfix("起");

        // act
        promoDetailModuleBuilderService.hideLeadsDealPromoDetailInfo(promoDetailModule);

        // assert
        assertEquals("1?0", promoDetailModule.getDealGroupPrice());
        assertEquals("8?.22", promoDetailModule.getPromoPrice());
        assertTrue(StringUtils.isEmpty(promoDetailModule.getTotalPromo()));
        assertTrue(StringUtils.isEmpty(promoDetailModule.getPricePostfix()));

    }

    @Test
    public void testHideLeadsDealPriceInfo() {
        // arrange
        PriceDisplayModuleDo priceDisplayModuleDo = new PriceDisplayModuleDo();
        priceDisplayModuleDo.setPrice("11000");
        priceDisplayModuleDo.setPromoPrice("减后价 ￥10,980.22");
        priceDisplayModuleDo.setPromoTag("立减19.78");
        priceDisplayModuleDo.setDealGroupPrice("11,000");
        PriceDisplayDTO normalPrice = new PriceDisplayDTO();
        normalPrice.setPrice(new BigDecimal(10980.22));

        // act
        promoDetailModuleBuilderService.hideLeadsDealPriceInfo(priceDisplayModuleDo, normalPrice);

        // assert
        assertEquals("1?,000", priceDisplayModuleDo.getDealGroupPrice());
        assertEquals("1?000", priceDisplayModuleDo.getPrice());
        assertEquals("减后价 ￥1?,980.22", priceDisplayModuleDo.getPromoPrice());
        assertNull(priceDisplayModuleDo.getPromoTag());
    }

    @Test
    public void testHideLeadsDealDisplayPrice() {
        // arrange
        DealGroupPBO result = new DealGroupPBO();
        result.setDisplayPrice("1,000");

        // act
        promoDetailModuleBuilderService.hideLeadsDealDisplayPrice(result);

        // assert
        assertEquals("1,?00", result.getDisplayPrice());
    }

    @Test
    public void testBuildPromoAbstractList() {
        // PromoDetailModule promoDetailModule = new PromoDetailModule();
        // List<PromoDTO> promoDTOList = JacksonUtils.deserialize(promoDTOListJson, List.class);
        PriceContext priceContext = JacksonUtils.deserialize(PriceContextJson, PriceContext.class);
        PromoDetailModule promoDetailModule = JacksonUtils.deserialize(promoDetailModuleJson, PromoDetailModule.class);
        List<DealGift> dealGifts = JacksonUtils.deserialize(DealGiftsJson, List.class);
        List<ModuleAbConfig> moduleAbConfigs = JacksonUtils.deserialize(ModuleAbConfigsJson, List.class);
        // dealCtx.getPriceContext().getNormalPrice().getUsedPromos()

        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx dealCtx = new DealCtx(envCtx);
        dealCtx.setPriceContext(priceContext);
        dealCtx.setDealGifts(dealGifts);
        dealCtx.setModuleAbConfigs(moduleAbConfigs);

        List<String> results = promoDetailModuleBuilderService.buildPromoAbstractList(promoDetailModule, dealCtx);
        assert CollectionUtils.isNotEmpty(results);
    }

    @Test
    public void testSetBestPriceGuaranteeTag() {
        ObjectGuaranteeTagDTO objectGuaranteeTagDTO = Mockito.mock(ObjectGuaranteeTagDTO.class);
        BestPriceGuaranteeTagDTO bestPriceGuaranteeTagDTO = mock(BestPriceGuaranteeTagDTO.class);
        objectGuaranteeTagDTO.setBestPriceGuaranteeTagDTO(bestPriceGuaranteeTagDTO);
        when(bestPriceGuaranteeTagDTO.getValid()).thenReturn(true);
        when(objectGuaranteeTagDTO.getBestPriceGuaranteeTagDTO()).thenReturn(bestPriceGuaranteeTagDTO);

        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx dealCtx = new DealCtx(envCtx);
        List<ModuleAbConfig> moduleAbConfigs = JacksonUtils.deserialize(ModuleAbConfigsJson, List.class);
        dealCtx.setModuleAbConfigs(moduleAbConfigs);
        dealCtx.setBestPriceGuaranteeInfo(objectGuaranteeTagDTO);

        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.showBestPriceGuaranteeTag()).thenReturn(true);

        PromoDetailModule promoDetailModule = new PromoDetailModule();
        promoDetailModuleBuilderService.setBestPriceGuaranteeTag(promoDetailModule, dealCtx);

        assert promoDetailModule.getPriceStrengthDesc().equals("买贵必赔");
    }

    /**
     * 测试维修预付团单优惠信息构建，当配置开关关闭且promoDetailModule为null时
     */
    @Test
    public void testBuildRepairPrepayDealPromoDetailModule_WhenConfigOffAndModuleNull() {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        lionConfigUtilsMockedStatic.when(LionConfigUtils::enableShowRepairPriceDesc).thenReturn(false);

        // act
        promoDetailModuleBuilderService.buildRepairPrepayDealPromoDetailModule(ctx, promoDetailModule);

        // assert
        assertEquals(StringUtils.EMPTY, promoDetailModule.getMarketPromoDiscount());
        assertNull(StringUtils.EMPTY, promoDetailModule.getPriceDisplayText());
    }

    /**
     * 测试维修预付团单优惠信息构建，当配置开关打开时
     */
    @Test
    public void testBuildRepairPrepayDealPromoDetailModule_WhenConfigOn() {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.getCategoryId()).thenReturn(10);
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        lionConfigUtilsMockedStatic.when(LionConfigUtils::enableShowRepairPriceDesc).thenReturn(true);
        PrepayCategoryConfig categoryConfig = new PrepayCategoryConfig();
        Map<String, String> category2TextMap = new HashMap<>();
        category2TextMap.put("10", "xxxx");
        categoryConfig.setCategory2TextMap(category2TextMap);
        lionConfigUtilsMockedStatic.when(LionConfigUtils::getPrepayCategoryConfig).thenReturn(categoryConfig);
        douHuServiceMockedStatic.when(() -> DouHuService.isHitRepairPayAbTest(any())).thenReturn(true);
        // act
        promoDetailModuleBuilderService.buildRepairPrepayDealPromoDetailModule(ctx, promoDetailModule);

        // assert
        assertEquals(StringUtils.EMPTY, promoDetailModule.getMarketPromoDiscount());
        assertNotNull(promoDetailModule.getPriceDisplayText());
    }

    /**
     * 测试维修预付团单优惠信息构建，当配置开关关闭且promoDetailModule非null时
     */
    @Test
    public void testBuildRepairPrepayDealPromoDetailModule_WhenConfigOffAndModuleNotNull() {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        promoDetailModule.setPriceDisplayText("Existing text");
        lionConfigUtilsMockedStatic.when(LionConfigUtils::enableShowRepairPriceDesc).thenReturn(false);

        // act
        promoDetailModuleBuilderService.buildRepairPrepayDealPromoDetailModule(ctx, promoDetailModule);

        // assert
        assertEquals(StringUtils.EMPTY, promoDetailModule.getMarketPromoDiscount());
        assertEquals("Existing text", promoDetailModule.getPriceDisplayText());
    }



    private static final String promoDetailModuleJson = "{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PromoDetailModule\",\"priceDisplayText\":null,\"priceDisplayType\":0,\"descriptionTags\":null,\"dealGroupPrice\":\"100\",\"preSaleDealGroupPrice\":null,\"promoPrice\":\"99\",\"promoPriceDesc\":null,\"pricePostfix\":null,\"discountRate\":null,\"discountRateDescription\":null,\"reductionPromo\":\"0\",\"reductionPromoDetails\":null,\"bestPromoDetails\":[\"java.util.ArrayList\",[{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBestPromoDetail\",\"promoName\":\"团购优惠112元\",\"iconUrl\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoAmount\":\"112\",\"promoDesc\":\"团购优惠，下单立省112元\",\"promoDetailExtraInfo\":null,\"promoTag\":\"团购优惠\"},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBestPromoDetail\",\"promoName\":\"会员优惠1元\",\"iconUrl\":\"https://p0.meituan.net/ingee/add79b586c98aeaedcb88f1108357814953.png\",\"promoAmount\":\"1\",\"promoDesc\":\"会员优惠，商家会员专享，下单立省1元\",\"promoDetailExtraInfo\":null,\"promoTag\":\"会员优惠\"}]],\"atmosphereBarPromoList\":null,\"generalPromoDetailList\":null,\"couponPromo\":\"1\",\"totalPromo\":\"1\",\"presalePromo\":null,\"marketPricePromo\":\"113\",\"networkLowestPrice\":\"100\",\"marketPrice\":\"212\",\"showMarketPrice\":true,\"showBestPromoDetails\":true,\"finalPrice\":\"99\",\"inflateCounpon\":null,\"perPrice\":null,\"bestPromoDetailsStyleInfo\":null,\"marketPromoDiscount\":\"4.7折\",\"promoDesc\":null,\"promoTags\":null,\"couponList\":[\"java.util.ArrayList\",[]],\"exposureList\":[\"java.util.ArrayList\",[]],\"promoActivityList\":[\"java.util.ArrayList\",[{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.PromoActivityInfoVO\",\"bonusType\":\"省\",\"text\":\"[{\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"strikethrough\\\":false,\\\"text\\\":\\\"金融券test环境使用\\\",\\\"textcolor\\\":\\\"#222222\\\",\\\"textsize\\\":12,\\\"textstyle\\\":\\\"Default\\\",\\\"underline\\\":false}]\",\"style\":0,\"leadUrl\":\"https://stable.pay.test.sankuai.com/portal/bindcard/bindcard.html?merchant_no=1&ext_dim_stat_entry=1&callback_type=close_webview&_mtcq=0&utm_source=pay_app-pay-banner410419_540904&campaignId=1477039\",\"shortText\":\"[{\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"strikethrough\\\":false,\\\"text\\\":\\\"金融券test环境使用\\\",\\\"textcolor\\\":\\\"#222222\\\",\\\"textsize\\\":12,\\\"textstyle\\\":\\\"Default\\\",\\\"underline\\\":false}]\"}]],\"dealGifts\":[\"java.util.ArrayList\",[{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.DealGift\",\"title\":\"无门槛10元券\",\"timeDesc\":null,\"couponNum\":1,\"productTag\":\"下单后发放\",\"specificTag\":\"赠品\",\"leftIconTag\":null,\"thumbnail\":\"http://p0.meituan.net/bizoperate/defb40aa44789acb139ee22cb0831965400867.jpg\",\"activityId\":119217,\"customerActivityPrefix\":\"下单赠\"}]],\"promoAbstractList\":null,\"richPromoAbstractStyle\":false,\"richPromoAbstractList\":null,\"priceStrengthDesc\":null,\"showPriceCompareEntrance\":false,\"singlePrice\":null,\"copies\":null,\"pricePrefix\":null,\"singleTimePrice\":null,\"multiPrice\":null,\"promoNewStyle\":false}";
    private static final String DealGiftsJson = "[\"java.util.ArrayList\",[{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.DealGift\",\"title\":\"无门槛10元券\",\"timeDesc\":null,\"couponNum\":1,\"productTag\":\"下单后发放\",\"specificTag\":\"赠品\",\"leftIconTag\":null,\"thumbnail\":\"http://p0.meituan.net/bizoperate/defb40aa44789acb139ee22cb0831965400867.jpg\",\"activityId\":119217,\"customerActivityPrefix\":\"下单赠\"}]]";
    private static final String ModuleAbConfigsJson = "[\"java.util.ArrayList\",[{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MTSalesGeneralSection\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp001434\",\"expResult\":\"a\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"fe6b7bd8-d576-481c-86f9-43158c62434d\\\",\\\"ab_id\\\":\\\"exp001434_a\\\"}\",\"useNewStyle\":false}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MtCouponAlleviate2Exp\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"EXP2024082700008\",\"expResult\":\"c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"34253294-e5dd-4d56-8738-c0b4a46d4aa5\\\",\\\"ab_id\\\":\\\"EXP2024082700008_b\\\"}\",\"useNewStyle\":true}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MtCouponAlleviate2Exp\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"EXP2024082700008\",\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"8f0a3544-1038-478a-9503-8d0ca1ffb427\\\",\\\"ab_id\\\":\\\"EXP2024082700008_b\\\"}\",\"useNewStyle\":true}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MTShoppingCartBuyBar\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp001510\",\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"bd4cc561-641f-4e4e-8362-25d91a155d36\\\",\\\"ab_id\\\":\\\"exp001510_b\\\"}\",\"useNewStyle\":true}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MTShoppingCartBuyBarNew\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp001692\",\"expResult\":\"a\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"5ad9e8f5-95cd-415d-aeec-a211ba5c4930\\\",\\\"ab_id\\\":\\\"exp001692_a\\\"}\",\"useNewStyle\":false}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MTCouponBar\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp001707\",\"expResult\":\"c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"08527cb9-11de-44b7-95d2-76b022e36d3e\\\",\\\"ab_id\\\":\\\"exp001707_c\\\"}\",\"useNewStyle\":false}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MtPurchaseNoteStructure\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp001872\",\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"16aa13bf-60fd-4b36-8114-9cfcd0ee07a5\\\",\\\"ab_id\\\":\\\"exp001872_b\\\"}\",\"useNewStyle\":true}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"CardStyleAB_V2_MT_impr_v2\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp001890\",\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"2206d30a-00bb-4cca-bf06-986d45a84c3c\\\",\\\"ab_id\\\":\\\"exp001890_b\\\"}\",\"useNewStyle\":true}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MtComparePrice\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp001940\",\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"ce196bef-1072-4b26-8276-7558ac159984\\\",\\\"ab_id\\\":\\\"exp001940_b\\\"}\",\"useNewStyle\":true}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MTShowReservationExpModule\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp001890\",\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"f8fafccf-8a4e-49c6-aa57-81962287d55c\\\",\\\"ab_id\\\":\\\"exp001890_b\\\"}\",\"useNewStyle\":true}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MtCouponAlleviate1Exp\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"EXP2024082700008\",\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"0cc7aa76-0776-4057-bf24-5503d1ed6c58\\\",\\\"ab_id\\\":\\\"EXP2024082700008_b\\\"}\",\"useNewStyle\":true}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MtCouponAlleviate2Exp\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"EXP2024082700008\",\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"06a1b934-3b89-4fa7-91c3-73e763bc9822\\\",\\\"ab_id\\\":\\\"EXP2024082700008_b\\\"}\",\"useNewStyle\":true}]]}]]";
    private static final String PriceContextJson = "{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.context.PriceContext\",\"newUser\":true,\"dcCardMemberDay\":false,\"dcCardMemberCard\":null,\"hasExclusiveDeduction\":false,\"dcCardMemberPrice\":null,\"idlePromoPrice\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO\",\"identity\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.ProductIdentity\",\"spuId\":0,\"spuSceneType\":0,\"productId\":**********,\"shopId\":null,\"productType\":1,\"cityId\":0,\"price\":null,\"marketPrice\":null,\"skuId\":457626630,\"categoryId\":0,\"productIdL\":0,\"skuIdL\":0,\"priceTime\":0,\"forceSpecifyInfo\":null,\"extParams\":{\"@class\":\"java.util.HashMap\"},\"tyingSaleDTOs\":null,\"priceTrendType\":0,\"relatedResourceDTOs\":null,\"lskuId\":457626630,\"lproductId\":**********},\"price\":[\"java.math.BigDecimal\",100.00],\"maxPrice\":[\"java.math.BigDecimal\",100.00],\"basePrice\":[\"java.math.BigDecimal\",100.00],\"marketPrice\":[\"java.math.BigDecimal\",212.00],\"showMarketPrice\":true,\"multiTimesPriceDTO\":null,\"promoAmount\":[\"java.math.BigDecimal\",0],\"usedPromos\":[\"java.util.ArrayList\",[]],\"morePromos\":[\"java.util.ArrayList\",[]],\"pricePromoInfoMap\":{\"@class\":\"java.util.HashMap\"},\"skuPrices\":null,\"extPrices\":null,\"promoTag\":null,\"shortPromoTag\":null,\"promoDiscount\":null,\"promoTagType\":0,\"promoTagIcon\":null,\"promoTagIconText\":null,\"adjustPriceRules\":null,\"extendMap\":null,\"activityDTO\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.ActivityDTO\",\"discountProvider\":0,\"discountClassifyType\":0,\"startTime\":null,\"endTime\":null},\"pricePowerTagDisplayDTO\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PricePowerTagDisplayDTO\",\"allTagList\":[\"java.util.ArrayList\",[]],\"filteredTagList\":[\"java.util.ArrayList\",[]]},\"priceTrendDTO\":null,\"extendDisplayInfo\":{\"@class\":\"java.util.HashMap\"},\"prePayPriceDetail\":null},\"normalPrice\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO\",\"identity\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.ProductIdentity\",\"spuId\":0,\"spuSceneType\":0,\"productId\":**********,\"shopId\":null,\"productType\":1,\"cityId\":0,\"price\":null,\"marketPrice\":null,\"skuId\":457626630,\"categoryId\":0,\"productIdL\":0,\"skuIdL\":0,\"priceTime\":0,\"forceSpecifyInfo\":null,\"extParams\":{\"@class\":\"java.util.HashMap\"},\"tyingSaleDTOs\":null,\"priceTrendType\":0,\"relatedResourceDTOs\":null,\"lskuId\":457626630,\"lproductId\":**********},\"price\":[\"java.math.BigDecimal\",99.00],\"maxPrice\":[\"java.math.BigDecimal\",99.00],\"basePrice\":[\"java.math.BigDecimal\",100.00],\"marketPrice\":[\"java.math.BigDecimal\",212.00],\"showMarketPrice\":false,\"multiTimesPriceDTO\":null,\"promoAmount\":[\"java.math.BigDecimal\",1],\"usedPromos\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PromoDTO\",\"identity\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PromoIdentity\",\"promoId\":1900159456,\"promoType\":16,\"promoTypeDesc\":\"会员优惠1元\",\"sourceType\":0,\"promoShowType\":\"MEMBER_BENEFITS\"},\"amount\":[\"java.math.BigDecimal\",1],\"afterPromoPrice\":null,\"tag\":null,\"description\":null,\"consumeTimeDesc\":null,\"priceThrough\":true,\"canAssign\":false,\"couponAssignStatus\":null,\"detail\":null,\"extendDesc\":\"会员优惠，商家会员专享，下单立省1元\",\"startTime\":null,\"endTime\":null,\"promoDiscount\":[\"java.math.BigDecimal\",100],\"promoQuantityLimit\":null,\"icon\":\"https://p0.meituan.net/ingee/50ad56e4be5747727d346005a38e9ae0785.png\",\"couponTitle\":null,\"totalStock\":null,\"remainStock\":null,\"priceLimitDesc\":null,\"minConsumptionAmount\":null,\"useTimeDesc\":null,\"promoIdentity\":\"memberCard\",\"promoStatus\":0,\"couponGroupId\":null,\"couponId\":null,\"couponValueType\":0,\"couponValueText\":null,\"effectiveStartTime\":null,\"effectiveEndTime\":null,\"marketTag\":null,\"useType\":null,\"combinationId\":null,\"promoStock\":null,\"newActivityGroupId\":null,\"promoTextDTO\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PromoTextDTO\",\"title\":\"会员优惠1元\",\"subTitle\":\"会员优惠，商家会员专享，下单立省1元\",\"icon\":\"https://p0.meituan.net/ingee/50ad56e4be5747727d346005a38e9ae0785.png\",\"promoDivideType\":\"MEMBER_BENEFITS\",\"promoDivideTypeDesc\":\"会员优惠\",\"shelfTitle\":null,\"shelfSubTitle\":null,\"shelfIcon\":null,\"atmosphereBarIcon\":null,\"atmosphereBarText\":null,\"atmosphereBarButtonText\":null,\"atmosphereBarButtonUrl\":null,\"promoStatusText\":null},\"reductionSaleChannels\":null,\"amountShareDetail\":null,\"promotionExplanatoryTags\":null,\"promotionOtherInfoMap\":null,\"promotionDisplayTextMap\":null,\"newUser\":false}]],\"morePromos\":[\"java.util.ArrayList\",[]],\"pricePromoInfoMap\":{\"@class\":\"java.util.HashMap\"},\"skuPrices\":null,\"extPrices\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.dealuser.price.display.api.model.ExtPriceDisplayDTO\",\"extPriceType\":1,\"extPricePromoAmount\":[\"java.math.BigDecimal\",0],\"extPrice\":[\"java.math.BigDecimal\",100.00],\"extPriceTitle\":\"全网低价\",\"tyingSaleDTOS\":null}]],\"promoTag\":\"已优惠1\",\"shortPromoTag\":null,\"promoDiscount\":null,\"promoTagType\":0,\"promoTagIcon\":null,\"promoTagIconText\":null,\"adjustPriceRules\":null,\"extendMap\":null,\"activityDTO\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.ActivityDTO\",\"discountProvider\":1,\"discountClassifyType\":0,\"startTime\":null,\"endTime\":null},\"pricePowerTagDisplayDTO\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PricePowerTagDisplayDTO\",\"allTagList\":[\"java.util.ArrayList\",[]],\"filteredTagList\":[\"java.util.ArrayList\",[]]},\"priceTrendDTO\":null,\"extendDisplayInfo\":{\"@class\":\"java.util.HashMap\"},\"prePayPriceDetail\":null},\"costEffectivePrice\":null,\"dealPromoPrice\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO\",\"identity\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.ProductIdentity\",\"spuId\":0,\"spuSceneType\":0,\"productId\":**********,\"shopId\":null,\"productType\":1,\"cityId\":0,\"price\":null,\"marketPrice\":null,\"skuId\":457626630,\"categoryId\":0,\"productIdL\":0,\"skuIdL\":0,\"priceTime\":0,\"forceSpecifyInfo\":null,\"extParams\":{\"@class\":\"java.util.HashMap\"},\"tyingSaleDTOs\":null,\"priceTrendType\":0,\"relatedResourceDTOs\":null,\"lskuId\":457626630,\"lproductId\":**********},\"price\":[\"java.math.BigDecimal\",99.00],\"maxPrice\":[\"java.math.BigDecimal\",99.00],\"basePrice\":[\"java.math.BigDecimal\",100.00],\"marketPrice\":[\"java.math.BigDecimal\",212.00],\"showMarketPrice\":false,\"multiTimesPriceDTO\":null,\"promoAmount\":[\"java.math.BigDecimal\",113.00],\"usedPromos\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PromoDTO\",\"identity\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PromoIdentity\",\"promoId\":**********,\"promoType\":11,\"promoTypeDesc\":\"团购优惠112元\",\"sourceType\":1,\"promoShowType\":\"DEAL_PROMO\"},\"amount\":[\"java.math.BigDecimal\",112.00],\"afterPromoPrice\":null,\"tag\":\"团购优惠112元\",\"description\":\"团购优惠112元\",\"consumeTimeDesc\":null,\"priceThrough\":true,\"canAssign\":false,\"couponAssignStatus\":null,\"detail\":null,\"extendDesc\":\"团购优惠，下单立省112元\",\"startTime\":null,\"endTime\":null,\"promoDiscount\":null,\"promoQuantityLimit\":null,\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"couponTitle\":null,\"totalStock\":null,\"remainStock\":null,\"priceLimitDesc\":null,\"minConsumptionAmount\":null,\"useTimeDesc\":null,\"promoIdentity\":null,\"promoStatus\":0,\"couponGroupId\":null,\"couponId\":null,\"couponValueType\":0,\"couponValueText\":null,\"effectiveStartTime\":null,\"effectiveEndTime\":null,\"marketTag\":null,\"useType\":null,\"combinationId\":null,\"promoStock\":null,\"newActivityGroupId\":null,\"promoTextDTO\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PromoTextDTO\",\"title\":\"团购优惠112元\",\"subTitle\":\"团购优惠，下单立省112元\",\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoDivideType\":\"DEAL_PROMO\",\"promoDivideTypeDesc\":null,\"shelfTitle\":null,\"shelfSubTitle\":null,\"shelfIcon\":null,\"atmosphereBarIcon\":null,\"atmosphereBarText\":null,\"atmosphereBarButtonText\":null,\"atmosphereBarButtonUrl\":null,\"promoStatusText\":null},\"reductionSaleChannels\":null,\"amountShareDetail\":null,\"promotionExplanatoryTags\":null,\"promotionOtherInfoMap\":null,\"promotionDisplayTextMap\":null,\"newUser\":false},{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PromoDTO\",\"identity\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PromoIdentity\",\"promoId\":1900159456,\"promoType\":16,\"promoTypeDesc\":\"会员优惠1元\",\"sourceType\":0,\"promoShowType\":\"MEMBER_BENEFITS\"},\"amount\":[\"java.math.BigDecimal\",1],\"afterPromoPrice\":null,\"tag\":null,\"description\":null,\"consumeTimeDesc\":null,\"priceThrough\":true,\"canAssign\":false,\"couponAssignStatus\":null,\"detail\":null,\"extendDesc\":\"会员优惠，商家会员专享，下单立省1元\",\"startTime\":null,\"endTime\":null,\"promoDiscount\":[\"java.math.BigDecimal\",100],\"promoQuantityLimit\":null,\"icon\":\"https://p0.meituan.net/ingee/add79b586c98aeaedcb88f1108357814953.png\",\"couponTitle\":null,\"totalStock\":null,\"remainStock\":null,\"priceLimitDesc\":null,\"minConsumptionAmount\":null,\"useTimeDesc\":null,\"promoIdentity\":\"memberCard\",\"promoStatus\":0,\"couponGroupId\":null,\"couponId\":null,\"couponValueType\":0,\"couponValueText\":null,\"effectiveStartTime\":null,\"effectiveEndTime\":null,\"marketTag\":null,\"useType\":null,\"combinationId\":null,\"promoStock\":null,\"newActivityGroupId\":null,\"promoTextDTO\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PromoTextDTO\",\"title\":\"会员优惠1元\",\"subTitle\":\"会员优惠，商家会员专享，下单立省1元\",\"icon\":\"https://p0.meituan.net/ingee/add79b586c98aeaedcb88f1108357814953.png\",\"promoDivideType\":\"MEMBER_BENEFITS\",\"promoDivideTypeDesc\":\"会员优惠\",\"shelfTitle\":null,\"shelfSubTitle\":null,\"shelfIcon\":null,\"atmosphereBarIcon\":null,\"atmosphereBarText\":null,\"atmosphereBarButtonText\":null,\"atmosphereBarButtonUrl\":null,\"promoStatusText\":null},\"reductionSaleChannels\":null,\"amountShareDetail\":null,\"promotionExplanatoryTags\":null,\"promotionOtherInfoMap\":null,\"promotionDisplayTextMap\":null,\"newUser\":false}]],\"morePromos\":[\"java.util.ArrayList\",[]],\"pricePromoInfoMap\":{\"@class\":\"java.util.HashMap\"},\"skuPrices\":null,\"extPrices\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.dealuser.price.display.api.model.ExtPriceDisplayDTO\",\"extPriceType\":1,\"extPricePromoAmount\":[\"java.math.BigDecimal\",0],\"extPrice\":[\"java.math.BigDecimal\",100.00],\"extPriceTitle\":\"全网低价\",\"tyingSaleDTOS\":null}]],\"promoTag\":\"特惠促销共省¥113\",\"shortPromoTag\":\"共省¥113\",\"promoDiscount\":[\"java.math.BigDecimal\",0.47],\"promoTagType\":0,\"promoTagIcon\":null,\"promoTagIconText\":null,\"adjustPriceRules\":null,\"extendMap\":null,\"activityDTO\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.ActivityDTO\",\"discountProvider\":1,\"discountClassifyType\":0,\"startTime\":null,\"endTime\":null},\"pricePowerTagDisplayDTO\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PricePowerTagDisplayDTO\",\"allTagList\":[\"java.util.ArrayList\",[]],\"filteredTagList\":[\"java.util.ArrayList\",[]]},\"priceTrendDTO\":null,\"extendDisplayInfo\":{\"@class\":\"java.util.HashMap\"},\"prePayPriceDetail\":null},\"originDealPromoPrice\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO\",\"identity\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.ProductIdentity\",\"spuId\":0,\"spuSceneType\":0,\"productId\":**********,\"shopId\":null,\"productType\":1,\"cityId\":0,\"price\":null,\"marketPrice\":null,\"skuId\":457626630,\"categoryId\":0,\"productIdL\":0,\"skuIdL\":0,\"priceTime\":0,\"forceSpecifyInfo\":null,\"extParams\":{\"@class\":\"java.util.HashMap\"},\"tyingSaleDTOs\":null,\"priceTrendType\":0,\"relatedResourceDTOs\":null,\"lskuId\":457626630,\"lproductId\":**********},\"price\":[\"java.math.BigDecimal\",100.00],\"maxPrice\":[\"java.math.BigDecimal\",100.00],\"basePrice\":[\"java.math.BigDecimal\",100.00],\"marketPrice\":[\"java.math.BigDecimal\",212.00],\"showMarketPrice\":false,\"multiTimesPriceDTO\":null,\"promoAmount\":[\"java.math.BigDecimal\",112.00],\"usedPromos\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PromoDTO\",\"identity\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PromoIdentity\",\"promoId\":**********,\"promoType\":11,\"promoTypeDesc\":\"团购优惠112元\",\"sourceType\":1,\"promoShowType\":\"DEAL_PROMO\"},\"amount\":[\"java.math.BigDecimal\",112.00],\"afterPromoPrice\":null,\"tag\":\"团购优惠112元\",\"description\":\"团购优惠112元\",\"consumeTimeDesc\":null,\"priceThrough\":true,\"canAssign\":false,\"couponAssignStatus\":null,\"detail\":null,\"extendDesc\":\"团购优惠，下单立省112元\",\"startTime\":null,\"endTime\":null,\"promoDiscount\":null,\"promoQuantityLimit\":null,\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"couponTitle\":null,\"totalStock\":null,\"remainStock\":null,\"priceLimitDesc\":null,\"minConsumptionAmount\":null,\"useTimeDesc\":null,\"promoIdentity\":null,\"promoStatus\":0,\"couponGroupId\":null,\"couponId\":null,\"couponValueType\":0,\"couponValueText\":null,\"effectiveStartTime\":null,\"effectiveEndTime\":null,\"marketTag\":null,\"useType\":null,\"combinationId\":null,\"promoStock\":null,\"newActivityGroupId\":null,\"promoTextDTO\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PromoTextDTO\",\"title\":\"团购优惠112元\",\"subTitle\":\"团购优惠，下单立省112元\",\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoDivideType\":\"DEAL_PROMO\",\"promoDivideTypeDesc\":null,\"shelfTitle\":null,\"shelfSubTitle\":null,\"shelfIcon\":null,\"atmosphereBarIcon\":null,\"atmosphereBarText\":null,\"atmosphereBarButtonText\":null,\"atmosphereBarButtonUrl\":null,\"promoStatusText\":null},\"reductionSaleChannels\":null,\"amountShareDetail\":null,\"promotionExplanatoryTags\":null,\"promotionOtherInfoMap\":null,\"promotionDisplayTextMap\":null,\"newUser\":false}]],\"morePromos\":[\"java.util.ArrayList\",[]],\"pricePromoInfoMap\":{\"@class\":\"java.util.HashMap\"},\"skuPrices\":null,\"extPrices\":null,\"promoTag\":\"特惠促销共省¥112\",\"shortPromoTag\":\"共省¥112\",\"promoDiscount\":[\"java.math.BigDecimal\",0.48],\"promoTagType\":0,\"promoTagIcon\":null,\"promoTagIconText\":null,\"adjustPriceRules\":null,\"extendMap\":null,\"activityDTO\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.ActivityDTO\",\"discountProvider\":0,\"discountClassifyType\":0,\"startTime\":null,\"endTime\":null},\"pricePowerTagDisplayDTO\":{\"@class\":\"com.sankuai.dealuser.price.display.api.model.PricePowerTagDisplayDTO\",\"allTagList\":[\"java.util.ArrayList\",[]],\"filteredTagList\":[\"java.util.ArrayList\",[]]},\"priceTrendDTO\":null,\"extendDisplayInfo\":{\"@class\":\"java.util.HashMap\"},\"prePayPriceDetail\":null},\"atmosphereBarAndGeneralPromoDetailPrice\":null,\"normalPriceCipher\":\"vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoUIvypULdIhKv_YDdBKRF-d28JeOAB8drNErfNDkJkhKdMoOtxj9eMV0upaMuj7kO3Veopt5ogt04fVnLGZo69gV3JcP8A92RybScpUBcLtA4kCKYZ_GRPFWdFem69xUiMKD454ZUT648SFJb5Xmlo-f9lS4REiFBahWF_bLpzXBmaww70LW0EpPVZ32PQh_y21fHjExxIWI0f-tKOUzMc9FEen39p2fFU_dUcMmCRvgdNKhWtlv3Z4VVNaZR8fhwMEjnuXHvYkKDd4fkeRXuV_cOeJfsl7BrdmJoG7wjfZhCyPf9WbnjTdewnF5CgfX6zcRiZkKTEs5lLIF443W0IuYlguC89bJZdrurwtyxSjSg\",\"dealPromoPriceCipher\":\"vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoUIvypULdIhKv_YDdBKRF-dVIg_t_j7DwSwWpFfDcxNsNMoOtxj9eMV0upaMuj7kO3Veopt5ogt04fVnLGZo69gV3JcP8A92RybScpUBcLtA4kCKYZ_GRPFWdFem69xUiMKD454ZUT648SFJb5Xmlo-f9lS4REiFBahWF_bLpzXBmaww70LW0EpPVZ32PQh_y21fHjExxIWI0f-tKOUzMc9FEen39p2fFU_dUcMmCRvgdNKhWtlv3Z4VVNaZR8fhwMEjnuXHvYkKDd4fkeRXuV_cOeJfsl7BrdmJoG7wjfZhCyPf9WbnjTdewnF5CgfX6zcRiZkKTEs5lLIF443W0Iu9lSWlhRy-pNfQB_dxofg_g\",\"zuLiaoButtonNewStyle\":false,\"memberNormalPrice\":true,\"displayInflateCoupon\":false,\"memberPromoDealPrice\":true}";


}
