package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.CollaborativeRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.MtCollaborativeResponse;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.MtDealModel;
import com.dianping.mobile.mapi.dztgdetail.facade.RecommendFacade;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class CollaborativeActionTest {

    @InjectMocks
    private CollaborativeAction collaborativeAction;

    @Mock
    private RecommendFacade recommendFacade;

    @Mock
    private IMobileContext iMobileContext;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 用户已登录，返回完整价格信息
     */
    @Test
    public void testExecute_UserLoggedIn_ReturnsFullPriceInfo() throws Throwable {
        // arrange
        CollaborativeRequest request = new CollaborativeRequest();
        MtCollaborativeResponse response = new MtCollaborativeResponse();
        List<MtDealModel> deals = new ArrayList<>();
        MtDealModel deal = new MtDealModel();
        deal.setPrice(100);
        deals.add(deal);
        response.setDeals(deals);

        when(recommendFacade.getCollaborativeDealGroup(request, iMobileContext)).thenReturn(response);
        when(iMobileContext.getUserId()).thenReturn(123L);

        // act
        IMobileResponse result = collaborativeAction.execute(request, iMobileContext);

        // assert
        assertTrue(result instanceof CommonMobileResponse);
        MtCollaborativeResponse resultMessage = (MtCollaborativeResponse) ((CommonMobileResponse) result).getData();
        assertNotNull(resultMessage);
        assertEquals(100, resultMessage.getDeals().get(0).getPrice(), 0);
    }

    /**
     * 用户未登录，返回隐藏价格信息
     */
    @Test
    public void testExecute_UserNotLoggedIn_HidesPriceInfo() throws Throwable {
        // arrange
        CollaborativeRequest request = new CollaborativeRequest();
        MtCollaborativeResponse response = new MtCollaborativeResponse();
        List<MtDealModel> deals = new ArrayList<>();
        MtDealModel deal = new MtDealModel();
        deal.setPrice(100);
        deals.add(deal);
        response.setDeals(deals);

        when(recommendFacade.getCollaborativeDealGroup(request, iMobileContext)).thenReturn(response);
        when(iMobileContext.getUserId()).thenReturn(0L);

        // act
        IMobileResponse result = collaborativeAction.execute(request, iMobileContext);

        // assert
        assertTrue(result instanceof CommonMobileResponse);
        MtCollaborativeResponse resultMessage = (MtCollaborativeResponse) ((CommonMobileResponse) result).getData();
        assertNotNull(resultMessage);
        assertEquals(0, resultMessage.getDeals().get(0).getPrice(), 0);
    }

    /**
     * recommendFacade 抛出异常
     */
    @Test
    public void testExecute_RecommendFacadeThrowsException_ReturnsServerError() throws Throwable {
        // arrange
        CollaborativeRequest request = new CollaborativeRequest();
        when(recommendFacade.getCollaborativeDealGroup(request, iMobileContext)).thenThrow(new RuntimeException("Mock Exception"));

        // act
        IMobileResponse result = collaborativeAction.execute(request, iMobileContext);

        // assert
        assertEquals(Resps.SERVER_ERROR, result);
    }

    /**
     * message 为 null
     */
    @Test
    public void testExecute_MessageIsNull_ReturnsServerError() throws Throwable {
        // arrange
        CollaborativeRequest request = new CollaborativeRequest();
        when(recommendFacade.getCollaborativeDealGroup(request, iMobileContext)).thenReturn(null);

        // act
        IMobileResponse result = collaborativeAction.execute(request, iMobileContext);

        // assert
        assertEquals(Resps.SERVER_ERROR, result);
    }

    /**
     * message.getDeals() 为 null
     */
    @Test
    public void testExecute_DealsIsNull_ReturnsValidResponse() throws Throwable {
        // arrange
        CollaborativeRequest request = new CollaborativeRequest();
        MtCollaborativeResponse response = new MtCollaborativeResponse();
        response.setDeals(null);

        when(recommendFacade.getCollaborativeDealGroup(request, iMobileContext)).thenReturn(response);
        when(iMobileContext.getUserId()).thenReturn(123L);

        // act
        IMobileResponse result = collaborativeAction.execute(request, iMobileContext);

        // assert
        assertTrue(result instanceof CommonMobileResponse);
        MtCollaborativeResponse resultMessage = (MtCollaborativeResponse) ((CommonMobileResponse) result).getData();
        assertNotNull(resultMessage);
        assertNull(resultMessage.getDeals());
    }

    /**
     * message.getDeals() 中包含 null 的 MtDealModel
     */
    @Test
    public void testExecute_DealsContainsNullDealModel_ReturnsValidResponse() throws Throwable {
        // arrange
        CollaborativeRequest request = new CollaborativeRequest();
        MtCollaborativeResponse response = new MtCollaborativeResponse();
        List<MtDealModel> deals = new ArrayList<>();
        deals.add(null);
        response.setDeals(deals);

        when(recommendFacade.getCollaborativeDealGroup(request, iMobileContext)).thenReturn(response);
        when(iMobileContext.getUserId()).thenReturn(0L);

        // act
        IMobileResponse result = collaborativeAction.execute(request, iMobileContext);

        // assert
        assertTrue(result instanceof CommonMobileResponse);
        MtCollaborativeResponse resultMessage = (MtCollaborativeResponse) ((CommonMobileResponse) result).getData();
        assertNotNull(resultMessage);
        assertEquals(1, resultMessage.getDeals().size());
        assertNull(resultMessage.getDeals().get(0));
    }
}