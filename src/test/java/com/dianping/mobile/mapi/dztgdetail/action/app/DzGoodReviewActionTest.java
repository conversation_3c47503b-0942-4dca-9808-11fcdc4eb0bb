package com.dianping.mobile.mapi.dztgdetail.action.app;

import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.token.UserStatusResult;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GoodReviewReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.GoodReviewPBO;
import com.dianping.mobile.mapi.dztgdetail.facade.GoodReviewFacade;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;
import com.dianping.mobile.mapi.dztgdetail.util.AntiCrawlerUtils;
import java.io.IOException;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(JUnit4.class)
public class DzGoodReviewActionTest {

    @Mock
    private IMobileContext iMobileContext;

    @Mock
    private GoodReviewReq goodReviewReq;

    @Mock
    private GoodReviewFacade goodReviewFacade;

    @Mock
    private GoodReviewPBO goodReviewPBO;

    @Mock
    private UserStatusResult userStatusResult;

    @InjectMocks
    private DzGoodReviewAction dzGoodReviewAction;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(goodReviewFacade.queryGoodReview(any(), any(), any())).thenReturn(goodReviewPBO);
        when(iMobileContext.getUserStatus()).thenReturn(userStatusResult);
        when(userStatusResult.getMtUserId()).thenReturn(1L);
        when(userStatusResult.getUserId()).thenReturn(1L);
        when(iMobileContext.getUserId()).thenReturn(1L);
        when(goodReviewReq.getDealgroupid()).thenReturn(1);
    }

    @Test(expected = Exception.class)
    public void testExecuteInitEnvCtxException() throws Throwable {
        when(iMobileContext.getRequest()).thenThrow(new Exception());
        dzGoodReviewAction.execute(goodReviewReq, iMobileContext);
    }

    protected EnvCtx initEnvCtx(IMobileContext appCtx) {
        return new EnvCtx();
    }
}
