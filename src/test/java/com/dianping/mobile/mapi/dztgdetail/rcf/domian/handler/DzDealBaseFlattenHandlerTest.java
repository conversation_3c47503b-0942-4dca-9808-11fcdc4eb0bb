package com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.deal.bff.cache.enums.RcfDealBffInterfaceEnum;
import com.dianping.mobile.mapi.dztgdetail.rcf.api.req.DealNativeSnapshotReq;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.dto.DealBffResponseDTO;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.deallayout.DealLayoutService;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.deallayout.dto.ModuleItem;

import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

/**
 * <AUTHOR>
 * @create 2025/1/2 11:00
 */
@RunWith(MockitoJUnitRunner.class)
public class DzDealBaseFlattenHandlerTest {

    @InjectMocks
    private DzDealBaseHandler handler;

    @Mock
    private DealLayoutService dealLayoutService;

    @Mock
    private DealNativeSnapshotReq request;

    @Mock
    private DealBffResponseDTO bffResponse;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void moduleConfigsModuleFlattenTest() {
        String json = "{\"hasReserveEntrance\":false,\"serviceType\":\"茶套餐\",\"pnPurchaseNoteDTO\":{\"subTitle\":\"购买须知详情\",\"pnTitle\":\"购买须知\",\"pnModules\":[{\"pnIcon\":\"https://p0.meituan.net/travelcube/eb358d1a211285207c636aa95594f3751524.png\",\"pnModuleName\":\"适用时间\",\"pnItems\":[{\"pnItemValues\":[{\"pnValue\":\"购买后90天内有效\",\"pnType\":1}],\"pnItemName\":\"有效时间\"},{\"pnItemValues\":[{\"pnValue\":\"2024-12-31不可用\",\"pnType\":1}],\"pnItemName\":\"除外日期\"}]},{\"pnIcon\":\"https://p0.meituan.net/travelcube/17303e587e8242902a1bf6ecd3eab10b1029.png\",\"pnModuleName\":\"预约规则\",\"pnItems\":[{\"pnItemValues\":[{\"pnValue\":\"无需预约，如遇消费高峰时段您可能需要排队\",\"pnType\":1}],\"pnItemName\":\"\"}]},{\"pnIcon\":\"https://p1.meituan.net/travelcube/4dac95c5f43a52f49b81ece1918925ea858.png\",\"pnModuleName\":\"其他规则\",\"pnItems\":[{\"pnItemValues\":[{\"pnValue\":\"每张团购券最多1人使用\",\"pnType\":1},{\"pnValue\":\"不再与其他优惠同享\",\"pnType\":1}],\"pnItemName\":\"\"}]},{\"pnIcon\":\"https://p1.meituan.net/travelcube/09a2f606b1636f8b135b8582e3c0a53d1494.png\",\"pnModuleName\":\"温馨提示\",\"pnItems\":[{\"pnItemValues\":[{\"pnValue\":\"如需团购券发票，请您在消费时向商户咨询\",\"pnType\":1},{\"pnValue\":\"为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！\",\"pnType\":1}],\"pnItemName\":\"\"}]}]},\"type\":0,\"mtId\":*********,\"features\":[\"随时退\",\"过期退\"],\"specialFeatures\":[],\"titleTagIcon\":\"\",\"userCardState\":4,\"isStandardDealGroup\":false,\"skuId\":\"1239124666\",\"meetPurchaseLimit\":false,\"dpDealId\":1239124666,\"saleDesc\":\"年售2000+\",\"subTitleList\":[],\"displayPriceDesc\":\"团购价\",\"skuModule\":{\"skuAttrCnNameList\":[],\"skuAttrValueList\":[],\"url\":\"imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=group-order-submit&mrn_component=GroupOrderSubmit&dealid=*********&shopid=1354919112&isTransparent=true&mrn_transparent=ture&hideLoading=true&mrn_hideloading=true&mrn_hideNextNavBar=true&shopidEncrypt=qB4r177b7da307b995e261a137925c736a0ee8e63d7690a06106e1c93f6fb2727883d43947ee9ba0b9f722vxu5&pagesource=dealGroupDetail&is_sku=0&source=caixi\"},\"reminderExtend\":[],\"ssrExperimentEnabled\":false,\"rcfModuleConfigs\":{\"dealdetail_gc_packagedetail\":\"uniform-structure-table-b\"},\"businessFigure\":\"\",\"moduleConfigsModule\":{\"moduleAbConfigs\":[{\"configs\":[{\"expResult\":\"exp000123_c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"f2bb05c3-48dc-4548-a248-26972ab326d5\\\",\\\"ab_id\\\":\\\"exp000123_c\\\"}\",\"expId\":\"exp000123\"}],\"key\":\"GCPlatformModules/picasso_deal_detail_head_module\"}],\"moduleConfigs\":[{\"value\":\"general_big_photo_jushengqian\",\"key\":\"GCPlatformModules/picasso_deal_detail_head_module\"},{\"value\":\"uniform-structure-table-b\",\"key\":\"dealdetail_gc_packagedetail\"}],\"isDzx\":true,\"isDpOrder\":true,\"generalInfo\":\"card_style_v2\",\"isTort\":false,\"key\":\"joy_tea\",\"extraInfo\":\"newtuandeal\"},\"responseTimestamp\":1736313598487,\"relatedBehaviorModule\":{\"relatedUserBehaviorItems\":[{\"userBehaviorDesc\":\"1小时前下单了\",\"userAvatarUrl\":\"https://img.meituan.net/avatar/1431a5035368ad3290a52330f2b10e9c49673.jpg\",\"userName\":\"尽**冰\"},{\"userBehaviorDesc\":\"1小时前下单了\",\"userAvatarUrl\":\"https://p1.meituan.net/userheadpicbackend/ffff5b739f54487c25033e82664ab7601681.jpg%4048w_48h_1e_1c_1l%7Cwatermark%3D0\",\"userName\":\"羽**飞\"},{\"userBehaviorDesc\":\"2小时前下单了\",\"userAvatarUrl\":\"https://img.meituan.net/avatar/feb4a7481e268e6c775c282134cccb1743990.jpg\",\"userName\":\"尹**顺\"},{\"userBehaviorDesc\":\"3小时前下单了\",\"userAvatarUrl\":\"https://p0.meituan.net/ingee/4e78589c0aa6132682faae3f0438c8a4298832.png\",\"userName\":\"g**k\"},{\"userBehaviorDesc\":\"4小时前下单了\",\"userAvatarUrl\":\"https://p0.meituan.net/userheadpicbackend/bad471ca3da5f619db35dcfda78ae13e1067.jpg%4048w_48h_1e_1c_1l%7Cwatermark%3D0\",\"userName\":\"0**0\"},{\"userBehaviorDesc\":\"16小时前下单了\",\"userAvatarUrl\":\"https://p0.meituan.net/ingee/a29ea7a6829bbee66caa6da8bb0928dc14919.png%4048w_48h_1e_1c_1l%7Cwatermark%3D0\",\"userName\":\"老**干\"},{\"userBehaviorDesc\":\"16小时前下单了\",\"userAvatarUrl\":\"https://img.meituan.net/relationwxpic/f6341bbdc71d5cff9299177d9870c7cb4552.jpg%4048w_48h_1e_1c_1l%7Cwatermark%3D0\",\"userName\":\"肥宅\"},{\"userBehaviorDesc\":\"17小时前下单了\",\"userAvatarUrl\":\"https://img.meituan.net/avatar/15b7b3eb652ba6db15220e78173a6bde39931.jpg\",\"userName\":\"b**h\"},{\"userBehaviorDesc\":\"18小时前下单了\",\"userAvatarUrl\":\"https://p0.meituan.net/ingee/4e78589c0aa6132682faae3f0438c8a4298832.png\",\"userName\":\"u**8\"},{\"userBehaviorDesc\":\"19小时前下单了\",\"userAvatarUrl\":\"https://p0.meituan.net/ingee/4e78589c0aa6132682faae3f0438c8a4298832.png\",\"userName\":\"R**8\"},{\"userBehaviorDesc\":\"19小时前下单了\",\"userAvatarUrl\":\"https://p0.meituan.net/ingee/4e78589c0aa6132682faae3f0438c8a4298832.png\",\"userName\":\"L**6\"},{\"userBehaviorDesc\":\"19小时前下单了\",\"userAvatarUrl\":\"https://img.meituan.net/avatar/0d88c43542727817365053b4c03037fc11400.jpg\",\"userName\":\"微**行\"}]},\"position\":\"2104\",\"originalOnlineConsultUrl\":\"https://g.meituan.com/arche/dzbiz/node-im-h5/index.html?toUid=sl4S8mA7tpxKXHvPb&clientType=200501&chatType=0&bizId=*********&sendUnitType=6&secondCateId=134&firstCateId=30\",\"shop\":{\"shopPic\":\"http://p0.meituan.net/dpmerchantpic/1dc56ea941c1024902b8a41e33dbdbf91306880.png%40300w_0e_1l\",\"buyBarIconType\":0,\"shopIdEncrypt\":\"qB4r177b7da307b995e261a137925c736a0ee8e63d7684a26106e19e772ef5272ed7cd6a0aafd4aebcf7656b252410a52064b21168c9d5360fe258734009363bb3ce5bca91ba9966d63b3b07e783d9a8705bvxu5\",\"distance\":\"4.32km\",\"shopNum\":4,\"isLyyShop\":false,\"shopCategoryId\":0,\"avgPrice\":\"¥74/人\",\"hideStars\":false,\"shopListUrl\":\"imeituan://www.meituan.com/gc/mrn?mrn_biz=gc&mrn_entry=gcdealmrnmodules&mrn_component=shoplistnextpage&dealid=*********&poiid=1354919112&frompage=caixi&cityId=10&locatedCityId=10&lat=31.2848828&lng=121.5453017&mmcinflate=&mmcuse=&mmcbuy=&mmcfree=\",\"shopName\":\"云古里茶馆\",\"imUrl\":\"imeituan://www.meituan.com/web?url=https%3A%2F%2Fg.meituan.com%2Farche%2Fdzbiz%2Fnode-im-h5%2Findex.html%3FtoUid%3Dsl4S8mA7tpxKXHvPb%26clientType%3D200501%26chatType%3D0%26bizId%3D*********%26sendUnitType%3D6%26secondCateId%3D134%26firstCateId%3D30\",\"businessState\":\"营业中\",\"showType\":\"entertainment\",\"shopId\":1354919112,\"displayPosition\":2,\"lat\":31.31384285029727,\"address\":\"闸殷路1555号国华广场b座4楼16号（上影影城旁）\",\"shopListDesc\":\"4家门店适用\",\"lng\":121.50812990727886,\"businessHour\":\"周一至周日 09:00-02:00\",\"branchName\":\"五角场店\",\"shopPower\":50,\"shopUrl\":\"imeituan://www.meituan.com/gc/poi/detail?id=1354919112&mmcinflate=&mmcuse=&mmcbuy=&mmcfree=&channelType=caixi\",\"phoneNos\":[\"***********\",\"***********\"],\"distanceDesc\":\"距您4.32km\",\"mapUrl\":\"imeituan://www.meituan.com/mapchannel/poi/detail?mapsource=gc&overseas=0&coordtype=0&poi_id=1354919112&latitude=31.31384285029727&longitude=121.50812990727886\",\"shopType\":30,\"hideAddrEnable\":false,\"shopBizType\":0},\"guarantee\":[{\"iconHeight\":0,\"iconWidth\":0,\"text\":\"随时退\",\"type\":0},{\"iconHeight\":0,\"iconWidth\":0,\"text\":\"过期退\",\"type\":0}],\"title\":\"【古法奶茶】人气古法奶茶三选一\",\"onlineConsultUrl\":\"imeituan://www.meituan.com/web?url=https%3A%2F%2Fg.meituan.com%2Farche%2Fdzbiz%2Fnode-im-h5%2Findex.html%3FtoUid%3Dsl4S8mA7tpxKXHvPb%26clientType%3D200501%26chatType%3D0%26bizId%3D*********%26sendUnitType%3D6%26secondCateId%3D134%26firstCateId%3D30\",\"saleDescStr\":\"年售2000+\",\"hitStructuredPurchaseNote\":true,\"picAspectRatio\":0,\"displayPrice\":\"19.8\",\"moduleExtra\":{\"moduleConfigDos\":[{\"value\":\"gcdealdetail_newtuandealtab_tuandetail\",\"key\":\"团购详情\"},{\"value\":\"gcdealdetail_newtuandealtab_buyrules\",\"key\":\"购买须知\"},{\"value\":\"gcdealdetail_newtuandealtab_reviews\",\"key\":\"网友评价\"}],\"success\":true,\"expResults\":[{\"configs\":[{\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"1defd63a-d302-414a-8141-39a86be6efda\\\",\\\"ab_id\\\":\\\"exp002291_b\\\"}\",\"expId\":\"exp002291\"}],\"key\":\"MTSalesCaliberIsQueryNew\"},{\"configs\":[{\"expResult\":\"c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"72b018e9-db81-4f86-a954-a58aafb98e8f\\\",\\\"ab_id\\\":\\\"EXP2024100700001_c\\\"}\",\"expId\":\"EXP2024100700001\"}],\"key\":\"MtCouponAlleviate2Exp\"},{\"configs\":[{\"expResult\":\"exp000686_b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"5d57df7f-95ec-4c04-a93d-9615abed50dc\\\",\\\"ab_id\\\":\\\"exp000686_b\\\"}\",\"expId\":\"exp000686\"}],\"key\":\"MTJoyCardPriceExp\"},{\"configs\":[{\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"c327843d-2892-450b-897b-233041a013f4\\\",\\\"ab_id\\\":\\\"exp002193_b\\\"}\",\"expId\":\"exp002193\"}],\"key\":\"MTShoppingCartBuyBar\"},{\"configs\":[{\"expResult\":\"a\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"e01fde11-1755-4951-8b58-b4ba8c3722cd\\\",\\\"ab_id\\\":\\\"exp002463_a\\\"}\",\"expId\":\"exp002463\"}],\"key\":\"MTShoppingCartBuyBarNew\"},{\"configs\":[{\"expResult\":\"c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"3ea7688b-a789-4070-801c-8a5b41ca3e6a\\\",\\\"ab_id\\\":\\\"exp002529_c\\\"}\",\"expId\":\"exp002529\"}],\"key\":\"MTCouponBar\"},{\"configs\":[{\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"55bf2ef0-7dc7-4bea-8bf0-71dfcc02bd98\\\",\\\"ab_id\\\":\\\"exp003296_b\\\"}\",\"expId\":\"exp003296\"}],\"key\":\"MtPurchaseNoteStructure\"},{\"configs\":[{\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"121e2fd2-2871-4751-b239-0ee26ce8f078\\\",\\\"ab_id\\\":\\\"exp003170_b\\\"}\",\"expId\":\"exp003170\"}],\"key\":\"CardStyleABV2\"},{\"configs\":[{\"expResult\":\"c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"a0cc3b37-0b4e-489a-9cc4-4e21bafcfc19\\\",\\\"ab_id\\\":\\\"EXP2024082800012_c\\\"}\",\"expId\":\"EXP2024082800012\"}],\"key\":\"MtCouponAlleviate1Exp\"},{\"configs\":[{\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"35d7a57e-3f66-4ab5-8e06-0603eb6276a4\\\",\\\"ab_id\\\":\\\"EXP2024122300002_b\\\"}\",\"expId\":\"EXP2024122300002\"}],\"key\":\"MtExpressOptimizeExp\"},{\"configs\":[{\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"7963b40f-6861-4e94-87c8-b3d88a9f3ccc\\\",\\\"ab_id\\\":\\\"EXP2024100800006_b\\\"}\",\"expId\":\"EXP2024100800006\"}],\"key\":\"MtRecommendStrategy\"}]},\"serviceTypeId\":628,\"tradeType\":1,\"recommendStrategy\":{\"abConfigModel\":{\"configs\":[{\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"7963b40f-6861-4e94-87c8-b3d88a9f3ccc\\\",\\\"ab_id\\\":\\\"EXP2024100800006_b\\\"}\",\"expId\":\"EXP2024100800006\"}],\"key\":\"MtRecommendStrategy\"},\"hitNewRecommendStrategy\":false},\"purchaseLimitDeal\":false,\"reminderInfo\":[\"免预约\",\"部分日期可用\",\"购买后90天内有效\"],\"priceDisplayModuleDo\":{\"promoPrice\":\"减后价 ￥9.9\",\"enableDisplay\":false,\"marketPrice\":\"59\",\"price\":\"9.9\",\"dealGroupPrice\":\"19.8\",\"promoTag\":\"已优惠9.9\"},\"showNewReserveEntrance\":false,\"dpId\":*********,\"needLogin\":false,\"specialParamsForPay\":{\"payFeeCent\":990,\"firstCategoryId\":\"3\",\"endTimestamp\":\"1760976000000\",\"startTimestamp\":\"1701655598000\"},\"dealName\":\"【古法奶茶】人气古法奶茶三选一\",\"shareAble\":true,\"shopCardState\":4,\"extraStyles\":[\"JUSHENGQIAN_DEAL\"],\"bgName\":\"general\",\"dealContents\":[{\"rcfPicHeight\":219.375,\"scale\":\"16:9\",\"type\":1,\"content\":\"https://p0.meituan.net/dpmerchantpic/763e63fb7f301557af97f64438c17814127279.jpg%40960w_540h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D2%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\"}],\"categoryId\":318,\"maxPerUser\":0}";
        JSONObject dealBase = JSONObject.parseObject(json);
        handler.moduleConfigsModuleFlatten(dealBase);
        JSONObject rcfModuleConfigs = (JSONObject) dealBase.get("rcfModuleConfigs");
        Assert.assertNotNull(rcfModuleConfigs);
    }

    @Test
    public void moduleExtraFlatten_withValidData() {
        // 准备测试数据
        JSONObject dealBase = new JSONObject();
        JSONObject moduleExtra = new JSONObject();
        JSONArray moduleConfigDos = new JSONArray();
        JSONObject moduleConfig = new JSONObject();
        moduleConfig.put("key", "团购详情");
        moduleConfig.put("value", "testValue");
        moduleConfigDos.add(moduleConfig);
        moduleExtra.put("moduleConfigDos", moduleConfigDos);
        dealBase.put("moduleExtra", moduleExtra);
        // 模拟 DealLayoutService 的行为
        Map<String, ModuleItem> moduleItemMap = new HashMap<>();
        ModuleItem moduleItem = new ModuleItem();
        JSONObject extraInfo = new JSONObject();
        extraInfo.put("multiCard", "testMultiCard");
        moduleItem.setExtraInfo(extraInfo);
        moduleItemMap.put("local_dealdetail_struct_module", moduleItem);
        when(dealLayoutService.fetchDealDetailTabConfig("testValue")).thenReturn(moduleItemMap);
        when(dealLayoutService.getModuleItem("local_dealdetail_struct_module", moduleItemMap)).thenReturn(moduleItem);
        // 执行测试方法
        handler.moduleExtraFlatten(dealBase);
        // 验证结果
        assertTrue(dealBase.containsKey("rcfModuleExtra"));
        JSONObject rcfModuleExtra = dealBase.getJSONObject("rcfModuleExtra");
        assertTrue(rcfModuleExtra.containsKey("dealDetail"));
        JSONObject dealDetail = rcfModuleExtra.getJSONObject("dealDetail");
        assertEquals("testValue", dealDetail.getString("type"));
        assertEquals("testMultiCard", dealDetail.get("multiCard"));
        // 验证方法调用
        verify(dealLayoutService).fetchDealDetailTabConfig("testValue");
        verify(dealLayoutService).getModuleItem("local_dealdetail_struct_module", moduleItemMap);
    }

    @Test
    public void moduleExtraFlatten_withEmptyModuleExtra() {
        JSONObject dealBase = new JSONObject();
        dealBase.put("moduleExtra", new JSONObject());
        handler.moduleExtraFlatten(dealBase);
        assertFalse(dealBase.containsKey("rcfModuleExtra"));
    }

    @Test
    public void moduleExtraFlatten_withNullModuleExtra() {
        JSONObject dealBase = new JSONObject();
        handler.moduleExtraFlatten(dealBase);
        assertFalse(dealBase.containsKey("rcfModuleExtra"));
    }

    @Test
    public void moduleExtraFlatten_withEmptyModuleConfigDos() {
        JSONObject dealBase = new JSONObject();
        JSONObject moduleExtra = new JSONObject();
        moduleExtra.put("moduleConfigDos", new JSONArray());
        dealBase.put("moduleExtra", moduleExtra);
        handler.moduleExtraFlatten(dealBase);
        assertFalse(dealBase.containsKey("rcfModuleExtra"));
    }

    @Test
    public void moduleExtraFlatten_withInvalidKey() {
        JSONObject dealBase = new JSONObject();
        JSONObject moduleExtra = new JSONObject();
        JSONArray moduleConfigDos = new JSONArray();
        JSONObject moduleConfig = new JSONObject();
        moduleConfig.put("key", "invalidKey");
        moduleConfig.put("value", "testValue");
        moduleConfigDos.add(moduleConfig);
        moduleExtra.put("moduleConfigDos", moduleConfigDos);
        dealBase.put("moduleExtra", moduleExtra);
        handler.moduleExtraFlatten(dealBase);
        assertFalse(dealBase.containsKey("rcfModuleExtra"));
    }

    @Test
    public void moduleExtraFlatten_withEmptyModuleItemMap() {
        JSONObject dealBase = new JSONObject();
        JSONObject moduleExtra = new JSONObject();
        JSONArray moduleConfigDos = new JSONArray();
        JSONObject moduleConfig = new JSONObject();
        moduleConfig.put("key", "团购详情");
        moduleConfig.put("value", "testValue");
        moduleConfigDos.add(moduleConfig);
        moduleExtra.put("moduleConfigDos", moduleConfigDos);
        dealBase.put("moduleExtra", moduleExtra);
        when(dealLayoutService.fetchDealDetailTabConfig("testValue")).thenReturn(new HashMap<>());
        handler.moduleExtraFlatten(dealBase);
        assertTrue(dealBase.containsKey("rcfModuleExtra"));
        JSONObject rcfModuleExtra = dealBase.getJSONObject("rcfModuleExtra");
        assertTrue(rcfModuleExtra.containsKey("dealDetail"));
        JSONObject dealDetail = rcfModuleExtra.getJSONObject("dealDetail");
        assertEquals("testValue", dealDetail.getString("type"));
        assertFalse(dealDetail.containsKey("multiCard"));
    }

    @Test
    public void testBuildMultiCardModuleItemMapIsEmpty() {
        String key = "testKey";
        JSONObject dealDetail = new JSONObject();
        when(dealLayoutService.fetchDealDetailTabConfig(key)).thenReturn(new HashMap<>());
        handler.buildMultiCard(key, dealDetail);
        verify(dealLayoutService, times(1)).fetchDealDetailTabConfig(key);
        verify(dealLayoutService, never()).getModuleItem(anyString(), anyMap());
    }

    @Test
    public void testBuildMultiCardTabConfigIsNull() {
        String key = "testKey";
        JSONObject dealDetail = new JSONObject();
        Map<String, ModuleItem> moduleItemMap = new HashMap<>();
        moduleItemMap.put("testKey", new ModuleItem());
        when(dealLayoutService.fetchDealDetailTabConfig(key)).thenReturn(moduleItemMap);
        when(dealLayoutService.getModuleItem(anyString(), anyMap())).thenReturn(null);
        handler.buildMultiCard(key, dealDetail);
        verify(dealLayoutService, times(1)).fetchDealDetailTabConfig(key);
        verify(dealLayoutService, times(1)).getModuleItem(anyString(), anyMap());
    }

    @Test
    public void testBuildMultiCardExtraInfoIsNull() {
        String key = "testKey";
        JSONObject dealDetail = new JSONObject();
        Map<String, ModuleItem> moduleItemMap = new HashMap<>();
        ModuleItem moduleItem = new ModuleItem();
        moduleItem.setExtraInfo(null);
        moduleItemMap.put("testKey", moduleItem);
        when(dealLayoutService.fetchDealDetailTabConfig(key)).thenReturn(moduleItemMap);
        when(dealLayoutService.getModuleItem(anyString(), anyMap())).thenReturn(moduleItem);
        handler.buildMultiCard(key, dealDetail);
        verify(dealLayoutService, times(1)).fetchDealDetailTabConfig(key);
        verify(dealLayoutService, times(1)).getModuleItem(anyString(), anyMap());
    }

    @Test
    public void testBuildMultiCardExtraInfoHasNoMultiCard() {
        String key = "testKey";
        JSONObject dealDetail = new JSONObject();
        Map<String, ModuleItem> moduleItemMap = new HashMap<>();
        ModuleItem moduleItem = new ModuleItem();
        JSONObject extraInfo = new JSONObject();
        extraInfo.put("otherKey", "otherValue");
        moduleItem.setExtraInfo(extraInfo);
        moduleItemMap.put("testKey", moduleItem);
        when(dealLayoutService.fetchDealDetailTabConfig(key)).thenReturn(moduleItemMap);
        when(dealLayoutService.getModuleItem(anyString(), anyMap())).thenReturn(moduleItem);
        handler.buildMultiCard(key, dealDetail);
        verify(dealLayoutService, times(1)).fetchDealDetailTabConfig(key);
        verify(dealLayoutService, times(1)).getModuleItem(anyString(), anyMap());
    }

    @Test
    public void testBuildMultiCardExtraInfoHasMultiCard() {
        String key = "testKey";
        JSONObject dealDetail = new JSONObject();
        Map<String, ModuleItem> moduleItemMap = new HashMap<>();
        ModuleItem moduleItem = new ModuleItem();
        JSONObject extraInfo = new JSONObject();
        extraInfo.put("multiCard", "multiCardValue");
        moduleItem.setExtraInfo(extraInfo);
        moduleItemMap.put("testKey", moduleItem);
        when(dealLayoutService.fetchDealDetailTabConfig(key)).thenReturn(moduleItemMap);
        when(dealLayoutService.getModuleItem(anyString(), anyMap())).thenReturn(moduleItem);
        handler.buildMultiCard(key, dealDetail);
        verify(dealLayoutService, times(1)).fetchDealDetailTabConfig(key);
        verify(dealLayoutService, times(1)).getModuleItem(anyString(), anyMap());
        assert dealDetail.containsKey("multiCard");
    }

    @Test(expected = NullPointerException.class)
    public void testModuleExtraFlatten_withNullDealBase() throws Throwable {
        handler.moduleExtraFlatten(null);
    }

    @Test
    public void testModuleExtraFlatten_withNoModuleExtra() throws Throwable {
        JSONObject dealBase = new JSONObject();
        handler.moduleExtraFlatten(dealBase);
        verify(dealLayoutService, never()).fetchDealDetailTabConfig(anyString());
    }

    @Test
    public void testModuleExtraFlatten_withNullModuleExtra() throws Throwable {
        JSONObject dealBase = new JSONObject();
        dealBase.put("moduleExtra", null);
        handler.moduleExtraFlatten(dealBase);
        verify(dealLayoutService, never()).fetchDealDetailTabConfig(anyString());
    }

    @Test
    public void testModuleExtraFlatten_withEmptyModuleConfigDos() throws Throwable {
        JSONObject dealBase = new JSONObject();
        JSONObject moduleExtra = new JSONObject();
        moduleExtra.put("moduleConfigDos", new JSONArray());
        dealBase.put("moduleExtra", moduleExtra);
        handler.moduleExtraFlatten(dealBase);
        verify(dealLayoutService, never()).fetchDealDetailTabConfig(anyString());
    }

    @Test
    public void testModuleExtraFlatten_withKeyNotEqualToDealdetail() throws Throwable {
        JSONObject dealBase = new JSONObject();
        JSONObject moduleExtra = new JSONObject();
        JSONArray moduleConfigDos = new JSONArray();
        JSONObject moduleConfig = new JSONObject();
        moduleConfig.put("key", "otherKey");
        moduleConfig.put("value", "testValue");
        moduleConfigDos.add(moduleConfig);
        moduleExtra.put("moduleConfigDos", moduleConfigDos);
        dealBase.put("moduleExtra", moduleExtra);
        handler.moduleExtraFlatten(dealBase);
        verify(dealLayoutService, never()).fetchDealDetailTabConfig(anyString());
    }

    @Test
    public void testModuleExtraFlatten_withKeyEqualToDealdetail() throws Throwable {
        JSONObject dealBase = new JSONObject();
        JSONObject moduleExtra = new JSONObject();
        JSONArray moduleConfigDos = new JSONArray();
        JSONObject moduleConfig = new JSONObject();
        moduleConfig.put("key", "团购详情");
        moduleConfig.put("value", "testValue");
        moduleConfigDos.add(moduleConfig);
        moduleExtra.put("moduleConfigDos", moduleConfigDos);
        dealBase.put("moduleExtra", moduleExtra);
        handler.moduleExtraFlatten(dealBase);
        verify(dealLayoutService, times(1)).fetchDealDetailTabConfig(anyString());
    }

    /**
     * 测试正常场景
     */
    @Test
    public void testCustomerProcessNormal() throws Throwable {
        // arrange
        JSONObject dealBase = new JSONObject();
        when(bffResponse.getBffResponse(RcfDealBffInterfaceEnum.dzdealbase)).thenReturn(dealBase);
        // act
        handler.customerProcess(request, bffResponse);
        // assert
        verify(bffResponse, times(1)).getBffResponse(RcfDealBffInterfaceEnum.dzdealbase);
    }

    /**
     * 测试异常场景：bffResponse 中不包含 dealBase
     */
    @Test
    public void testCustomerProcessNoDealBase() throws Throwable {
        // arrange
        when(bffResponse.getBffResponse(RcfDealBffInterfaceEnum.dzdealbase)).thenReturn(null);
        // act
        handler.customerProcess(request, bffResponse);
        // assert
        verify(bffResponse, times(1)).getBffResponse(RcfDealBffInterfaceEnum.dzdealbase);
        // 由于customerProcess方法内部会捕获异常，这里我们验证方法是否被正确调用即可
    }

    /**
     * 测试异常场景：dealBase 中的 moduleConfigsModule 为空
     */
    @Test
    public void testCustomerProcessEmptyModuleConfigsModule() throws Throwable {
        // arrange
        JSONObject dealBase = new JSONObject();
        dealBase.put("moduleConfigsModule", new JSONObject());
        when(bffResponse.getBffResponse(RcfDealBffInterfaceEnum.dzdealbase)).thenReturn(dealBase);
        // act
        handler.customerProcess(request, bffResponse);
        // assert
        verify(bffResponse, times(1)).getBffResponse(RcfDealBffInterfaceEnum.dzdealbase);
    }

    /**
     * 测试异常场景：dealBase 中的 moduleExtra 为空
     */
    @Test
    public void testCustomerProcessEmptyModuleExtra() throws Throwable {
        // arrange
        JSONObject dealBase = new JSONObject();
        dealBase.put("moduleExtra", new JSONObject());
        when(bffResponse.getBffResponse(RcfDealBffInterfaceEnum.dzdealbase)).thenReturn(dealBase);
        // act
        handler.customerProcess(request, bffResponse);
        // assert
        verify(bffResponse, times(1)).getBffResponse(RcfDealBffInterfaceEnum.dzdealbase);
    }
}
