package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.ProcessHandler;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealPromoModuleReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo.DealPromoModule;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.springframework.test.util.ReflectionTestUtils;

public class DealPromoFacadeTest {

    @InjectMocks
    private DealPromoFacade dealPromoFacade;

    @Mock
    private ProcessHandler<DealCtx> promoPreModuleHandler;

    @Mock
    private ProcessHandler<DealCtx> promoModuleHandler;

    @Mock
    private ProcessHandler<DealCtx> promoPostModuleHandler;

    @Mock
    private DealCtx dealCtx;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 promoPreModuleHandler 抛出异常的情况
     */
    @Test(expected = Exception.class)
    public void testQueryDealPromoModulePreHandlerException() throws Throwable {
        // arrange
        DealPromoModuleReq request = new DealPromoModuleReq();
        EnvCtx envCtx = new EnvCtx();
        doThrow(new Exception("PreHandler Exception")).when(promoPreModuleHandler).preThenProc(any(DealCtx.class));
        // Use ReflectionTestUtils to set the dealCtx directly
        ReflectionTestUtils.setField(dealPromoFacade, "dealCtx", dealCtx);
        // act
        dealPromoFacade.queryDealPromoModule(request, envCtx);
        // assert
        // 期望抛出异常
    }

    /**
     * 测试 promoModuleHandler 抛出异常的情况
     */
    @Test(expected = Exception.class)
    public void testQueryDealPromoModuleModuleHandlerException() throws Throwable {
        // arrange
        DealPromoModuleReq request = new DealPromoModuleReq();
        EnvCtx envCtx = new EnvCtx();
        doNothing().when(promoPreModuleHandler).preThenProc(any(DealCtx.class));
        doThrow(new Exception("ModuleHandler Exception")).when(promoModuleHandler).preThenProc(any(DealCtx.class));
        // Use ReflectionTestUtils to set the dealCtx directly
        ReflectionTestUtils.setField(dealPromoFacade, "dealCtx", dealCtx);
        // act
        dealPromoFacade.queryDealPromoModule(request, envCtx);
        // assert
        // 期望抛出异常
    }

    /**
     * 测试 promoPostModuleHandler 抛出异常的情况
     */
    @Test(expected = Exception.class)
    public void testQueryDealPromoModulePostHandlerException() throws Throwable {
        // arrange
        DealPromoModuleReq request = new DealPromoModuleReq();
        EnvCtx envCtx = new EnvCtx();
        doNothing().when(promoPreModuleHandler).preThenProc(any(DealCtx.class));
        doNothing().when(promoModuleHandler).preThenProc(any(DealCtx.class));
        doThrow(new Exception("PostHandler Exception")).when(promoPostModuleHandler).preThenProc(any(DealCtx.class));
        // Use ReflectionTestUtils to set the dealCtx directly
        ReflectionTestUtils.setField(dealPromoFacade, "dealCtx", dealCtx);
        // act
        dealPromoFacade.queryDealPromoModule(request, envCtx);
        // assert
        // 期望抛出异常
    }
}
