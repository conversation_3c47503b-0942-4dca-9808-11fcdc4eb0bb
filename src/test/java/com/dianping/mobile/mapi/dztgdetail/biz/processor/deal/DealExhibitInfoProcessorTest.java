package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DigestQueryWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ImmersiveImageWrapper;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ContentSearchWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.immersiveimage.ImmersiveImageVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.CaseCoverDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.CasePicsDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.DigestMaterialInfoPhotoDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.ExhibitContentDTO;
import com.dianping.mobile.mapi.dztgdetail.entity.*;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.dianping.mobile.mapi.dztgdetail.util.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.mpmctcontent.application.thrift.api.content.WedPhotoCaseService;
import com.sankuai.mpmctcontent.application.thrift.dto.content.common.PicDTO;
import com.sankuai.mpmctcontent.application.thrift.dto.wed.BatchLoadCaseReqDTO;
import com.sankuai.mpmctcontent.application.thrift.dto.wed.BatchLoadCaseRespDTO;
import com.sankuai.mpmctcontent.application.thrift.dto.wed.WedPhotoCaseInfoDTO;
import com.sankuai.mpmctcontent.query.thrift.api.digest.DigestQueryService;
import com.sankuai.mpmctcontent.query.thrift.dto.digest.QueryDigestResponseDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.search.SearchDetailResponseDTO;
import org.apache.commons.collections.CollectionUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DealExhibitInfoProcessorTest {

    @InjectMocks
    private DealExhibitInfoProcessor dealExhibitInfoProcessor;

    private MockedStatic<LionConfigUtils> lionConfigUtilsMockedStatic;

    private MockedStatic<Lion> lionMockedStatic;

    private MockedStatic<DealUtils> dealUtilsMockedStatic;
    @Mock
    private ContentSearchWrapper contentSearchWrapper;

    private MockedStatic<GsonUtils> gsonUtilsMockedStatic;

    private MockedStatic<DealCtxHelper> dealCtxHelperMockedStatic;

    @Mock
    private ImmersiveImageWrapper immersiveImageWrapper;

    @Mock
    private WedPhotoCaseService wedPhotoCaseService;

    @Mock
    DigestQueryWrapper digestQueryWrapper;

    @Resource
    @Qualifier("digestQueryServiceFuture")
    private DigestQueryService digestQueryService;

    @Mock
    private DealCtx dealCtx;
    @Mock
    private EnvCtx envCtx;


    @Before
    public void setUp() {
        lionConfigUtilsMockedStatic = mockStatic(LionConfigUtils.class);
        dealUtilsMockedStatic = mockStatic(DealUtils.class);
        gsonUtilsMockedStatic = mockStatic(GsonUtils.class);
        dealCtxHelperMockedStatic = mockStatic(DealCtxHelper.class);
        lionMockedStatic = mockStatic(Lion.class);
//        lionMocked = mockStatic(Lion.class);

    }

    @After
    public void tearDown() {
        lionConfigUtilsMockedStatic.close();
        dealUtilsMockedStatic.close();
        gsonUtilsMockedStatic.close();
        dealCtxHelperMockedStatic.close();
        lionMockedStatic.close();
    }

    /**
     * 测试新穿戴甲场景
     */
    @Test
    public void testBuildQueryExhibitImageParam_NewWearableNail() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(1);
        ctx.setChannelDTO(channelDTO);
        ctx.setDpId(100);
        ctx.setMtLongShopId(200L);

        Map<String, String> wearableNailStyleMap = new HashMap<>();
        wearableNailStyleMap.put("categoryId", "1");
        wearableNailStyleMap.put("serviceType", "testServiceType");
        wearableNailStyleMap.put("limit", "33");

        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.getWearableNailStyleConfig()).thenReturn(wearableNailStyleMap);
        dealUtilsMockedStatic.when(() -> DealUtils.getServiceType(any())).thenReturn("testServiceType");
        dealUtilsMockedStatic.when(() -> DealUtils.getDealGroupServiceType(any())).thenReturn("testServiceType");
        dealUtilsMockedStatic.when(() -> DealUtils.isNewWearableNailDeal(any(DealCtx.class))).thenReturn(true);

        // act
        QueryExhibitImageParam result = dealExhibitInfoProcessor.buildQueryExhibitImageParam(ctx);

        // assert
        assertEquals(Integer.valueOf(33), result.getLimit());
    }

    /**
     * 测试美甲营销入口
     */
    @Test
    public void test_GetImmersiveImageData_BeautyNailCubeEntry() {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        gsonUtilsMockedStatic.when(() -> GsonUtils.getParamFromMapJson(ctx.getRequestExtParam(), "exhibitItemId", String.class)).thenReturn("123");
        dealUtilsMockedStatic.when(() -> DealUtils.isBeautyNailServiceTypeDeal(ctx)).thenReturn(true);
        dealCtxHelperMockedStatic.when(() -> DealCtxHelper.isCubeExhibitNail(ctx)).thenReturn(true);
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.enableExhibitSourceBeautyNailTop()).thenReturn(true);
        ImmersiveImageVO immersiveImageVO = new ImmersiveImageVO();
        immersiveImageVO.setTitle("test");
        when(immersiveImageWrapper.getImmersiveImageWithTopItem(any(), any(), anyInt(), anyBoolean())).thenReturn(immersiveImageVO);

        // act
        ImmersiveImageVO result = dealExhibitInfoProcessor.getImmersiveImageData(ctx, "美甲", 651L, null);

        // assert
        assertEquals("test", result.getTitle());
    }

    /**
     * 测试非营销入口-穿戴甲款式
     */
    @Test
    public void test_GetImmersiveImageData_NotCubeEntry_WearableNail() {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        FutureCtx futureCtx = new FutureCtx();
        futureCtx.setExhibitInfoFuture(CompletableFuture.completedFuture(null));
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
        when(ctx.getCategoryId()).thenReturn(502);
        when(ctx.getDealGroupDTO()).thenReturn(new DealGroupDTO());
        gsonUtilsMockedStatic.when(() -> GsonUtils.getParamFromMapJson(ctx.getRequestExtParam(), "exhibitItemId", String.class)).thenReturn(null);
        dealUtilsMockedStatic.when(() -> DealUtils.isBeautyNailServiceTypeDeal(ctx)).thenReturn(false);
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.enableExhibitSourceBeautyNailTop()).thenReturn(false);
        ImmersiveImageVO immersiveImageVO = new ImmersiveImageVO();
        immersiveImageVO.setTitle("test");
        when(immersiveImageWrapper.getImmersiveImageWithDeals(any(), any(), anyInt(),
                anyLong(), anyBoolean(), anyLong(), anyLong(), anyInt())).thenReturn(immersiveImageVO);
        Map<String, String> wearableNailStyleMap = Maps.newHashMap();
        wearableNailStyleMap.put("categoryId", "502");
        wearableNailStyleMap.put("serviceType", "穿戴甲");

        // act
        ImmersiveImageVO result = dealExhibitInfoProcessor.getImmersiveImageData(ctx, "穿戴甲", 651L, wearableNailStyleMap);

        // assert
        assertEquals("test", result.getTitle());
    }

    @Test
    public void testPrepare() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(1);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setChannelDTO(channelDTO);
        ctx.setDpId(100);
        ctx.setMtLongShopId(200L);

        Map<String, String> wearableNailStyleMap = new HashMap<>();
        wearableNailStyleMap.put("categoryId", "1");
        wearableNailStyleMap.put("serviceType", "testServiceType");
        wearableNailStyleMap.put("limit", "33");
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.getWearableNailStyleConfig()).thenReturn(wearableNailStyleMap);
        dealUtilsMockedStatic.when(() -> DealUtils.getServiceType(any())).thenReturn("testServiceType");
        dealUtilsMockedStatic.when(() -> DealUtils.getDealGroupServiceType(any())).thenReturn("testServiceType");
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.useNewExhibitCategoryIds(Mockito.anyInt())).thenReturn(true);
        dealExhibitInfoProcessor.prepare(ctx);
        Assert.assertTrue(Objects.isNull(ctx.getFutureCtx().getContentSearchFuture()));
    }

    @Test
    public void testProcess() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(1);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setChannelDTO(channelDTO);
        ctx.setDpId(100);
        ctx.setMtLongShopId(200L);

        Map<String, String> wearableNailStyleMap = new HashMap<>();
        wearableNailStyleMap.put("categoryId", "1");
        wearableNailStyleMap.put("serviceType", "testServiceType");
        wearableNailStyleMap.put("limit", "33");
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.getWearableNailStyleConfig()).thenReturn(wearableNailStyleMap);
        Map<String, ExhibitTextConfig> configMap = new HashMap<>();
        ExhibitTextConfig config = new ExhibitTextConfig();
        config.setTitle("title");
        configMap.put("1", config);
        lionMockedStatic.when(() -> Lion.getMap(LionConstants.APP_KEY, LionConstants.CATEGORY_EXHIBIT_TITLE_CONFIG, ExhibitTextConfig.class, Collections.EMPTY_MAP)).thenReturn(configMap);
        lionMockedStatic.when(() -> LionConfigUtils.useNewExhibitCategoryIds(ctx.getCategoryId())).thenReturn(true);
        ImmersiveImageVO immersiveImageVO = new ImmersiveImageVO();
        immersiveImageVO.setItems(Collections.emptyList());
        immersiveImageVO.setItemDisplayStyle(1);
        immersiveImageVO.setRecordCount(10);
        Mockito.when(contentSearchWrapper.getImmersiveImageVO(Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.any())).thenReturn(immersiveImageVO);
        dealExhibitInfoProcessor.process(ctx);
        Assert.assertTrue(Objects.isNull(ctx.getFutureCtx().getExhibitInfoFuture()));
    }

    @Test
    public void testProcess_FutureNull() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(1);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setChannelDTO(channelDTO);
        ctx.setDpId(100);
        ctx.setMtLongShopId(200L);

        Map<String, String> wearableNailStyleMap = new HashMap<>();
        wearableNailStyleMap.put("categoryId", "1");
        wearableNailStyleMap.put("serviceType", "testServiceType");
        wearableNailStyleMap.put("limit", "33");
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.getWearableNailStyleConfig()).thenReturn(wearableNailStyleMap);
        Map<String, ExhibitTextConfig> configMap = new HashMap<>();
        ExhibitTextConfig config = new ExhibitTextConfig();
        config.setTitle("title");
        configMap.put("1", config);
        lionMockedStatic.when(() -> Lion.getMap(LionConstants.APP_KEY, LionConstants.CATEGORY_EXHIBIT_TITLE_CONFIG, ExhibitTextConfig.class, Collections.EMPTY_MAP)).thenReturn(configMap);
        lionMockedStatic.when(() -> LionConfigUtils.useNewExhibitCategoryIds(ctx.getCategoryId())).thenReturn(false);
        dealExhibitInfoProcessor.process(ctx);
        Assert.assertTrue(Objects.isNull(ctx.getFutureCtx().getExhibitInfoFuture()));
        Assert.assertTrue(Objects.isNull(ctx.getFutureCtx().getSortedExhibitInfoFuture()));
    }
    @Test
    public void testProcess_NewCase() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(1);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setChannelDTO(channelDTO);
        ctx.setDpId(100);
        ctx.setMtLongShopId(200L);

        Map<String, String> wearableNailStyleMap = new HashMap<>();
        wearableNailStyleMap.put("categoryId", "908");
        wearableNailStyleMap.put("serviceType", "testServiceType");
        wearableNailStyleMap.put("limit", "33");
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.getWearableNailStyleConfig()).thenReturn(wearableNailStyleMap);
        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.useNewExhibitCategoryIds(ctx.getCategoryId())).thenReturn(true);
        Map<String, ExhibitTextConfig> configMap = new HashMap<>();
        ExhibitTextConfig config = new ExhibitTextConfig();
        config.setTitle("title");
        configMap.put("1", config);
        lionMockedStatic.when(() -> Lion.getMap(LionConstants.APP_KEY, LionConstants.CATEGORY_EXHIBIT_TITLE_CONFIG, ExhibitTextConfig.class, Collections.EMPTY_MAP)).thenReturn(configMap);
        lionMockedStatic.when(() -> LionConfigUtils.useNewExhibitCategoryIds(ctx.getCategoryId())).thenReturn(false);
        dealExhibitInfoProcessor.process(ctx);
        Assert.assertTrue(Objects.isNull(ctx.getFutureCtx().getExhibitInfoFuture()));
        Assert.assertTrue(Objects.isNull(ctx.getFutureCtx().getSortedExhibitInfoFuture()));
    }
    @Test
    public void test() {
        String envJson = "{\"dpUserId\":9000000000071776721,\"dpVirtualUserId\":9000000000071776722,\"mtUserId\":5088153737,\"mtVirtualUserId\":5088153738,\"unionId\":\"095a69ef747d46419703923fc0460fdba168778217023316139\",\"dpId\":\"095a69ef747d46419703923fc0460fdba168778217023316139\",\"uuid\":\"0000000000000095A69EF747D46419703923FC0460FDBA168778217023316139\",\"version\":\"12.23.200\",\"clientType\":200502,\"appDeviceId\":\"095a69ef747d46419703923fc0460fdba168778217023316139\",\"appId\":10,\"requestURI\":\"/general/platform/dztgdetail/dzdealbase.bin\",\"userAgent\":\"MApi 1.4 (mtscope 12.23.200 appstore; iPhone 14.8 iPhone13,2; a0d0)\",\"userIp\":\"***************\",\"dztgClientTypeEnum\":\"MEITUAN_APP\",\"startTime\":1726109598851,\"meituanClientList\":[\"MEITUAN_APP\",\"MEITUAN_FROM_H5\",\"MEITUAN_MAP_APP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_LIVE_WEIXIN_MINIAPP\"],\"dianpingClientList\":[\"DIANPING_APP\",\"DIANPING_FROM_H5\",\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"],\"merchantClientList\":[\"DPMERCHANT\"],\"mtMiniAppList\":[\"MEITUAN_WEIXIN_MINIAPP\",\"MEITUAN_KUAISHOU_MINIAPP\",\"MEITUAN_WANWU_MINIAPP\",\"MEITUAN_LIVE_WEIXIN_MINIAPP\"],\"dpMiniAppList\":[\"DIANPING_WEIXIN_MINIAPP\",\"DIANPING_BAIDUMAP_MINIAPP\"],\"nativeAppList\":[\"MEITUAN_APP\",\"MEITUAN_MAP_APP\",\"DIANPING_APP\"],\"wxMiniList\":[\"MEITUAN_WEIXIN_MINIAPP\",\"DIANPING_WEIXIN_MINIAPP\"],\"wxMini\":false,\"mtMiniApp\":false,\"dpMiniApp\":false,\"dpMerchant\":false,\"android\":false,\"fromH5\":false,\"mt\":true,\"apollo\":false,\"login\":true,\"mainApp\":true,\"ios\":true,\"thirdPlatform\":false,\"miniApp\":false,\"mainWeb\":false,\"mainWX\":false,\"externalAndNoScene\":false,\"mtLiveMinApp\":false,\"external\":false,\"native\":true}";
        EnvCtx env = JSON.parseObject(envJson, EnvCtx.class);
        DealCtx ctx = new DealCtx(env);
        ctx.setRequestSource(RequestSourceEnum.STYLE.getSource());
        try {
            Field field = dealExhibitInfoProcessor.getClass().getDeclaredField("digestQueryWrapper");
            field.setAccessible(true);
            field.set(dealExhibitInfoProcessor, digestQueryWrapper);

            Field field1 = digestQueryWrapper.getClass().getDeclaredField("digestQueryService");
            field1.setAccessible(true);
            field1.set(digestQueryWrapper, digestQueryService);

        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }

        dealExhibitInfoProcessor.prepare(ctx);

        String res = "{\"code\":200,\"msg\":\"执行成功\",\"responseInfo\":{\"67_1\":[{\"id\":\"1333886025545\",\"fields\":[\"1333886025545\"],\"data\":\"{\\\"top\\\": 0, \\\"rank\\\": 1725952215197, \\\"caseName\\\": \\\"123123\\\", \\\"casePics\\\": [{\\\"picUrl\\\": \\\"https://p0.inf.test.sankuai.com/dpmerchantpic/3311c8897a9158fd918e649b6c8fad39179996.jpg\\\", \\\"picWidth\\\": 0, \\\"picHeight\\\": 0}, {\\\"picUrl\\\": \\\"https://p0.inf.test.sankuai.com/dpmerchantpic/fe925f016c5f60dfa91b336ae4ede7b6183997.jpg\\\", \\\"picWidth\\\": 0, \\\"picHeight\\\": 0}, {\\\"picUrl\\\": \\\"https://p0.inf.test.sankuai.com/dpmerchantpic/a3bd29b7b355ecce066ae9bd821f19f956980.jpg\\\", \\\"picWidth\\\": 0, \\\"picHeight\\\": 0}, {\\\"picUrl\\\": \\\"https://p0.inf.test.sankuai.com/dpmerchantpic/a3bd29b7b355ecce066ae9bd821f19f956980.jpg\\\", \\\"picWidth\\\": 0, \\\"picHeight\\\": 0}, {\\\"picUrl\\\": \\\"https://p0.inf.test.sankuai.com/dpmerchantpic/a3bd29b7b355ecce066ae9bd821f19f956980.jpg\\\", \\\"picWidth\\\": 0, \\\"picHeight\\\": 0}], \\\"editTime\\\": 1726046295000, \\\"caseCover\\\": {\\\"coverUrl\\\": \\\"https://p0.inf.test.sankuai.com/dpmerchantpic/7474488cf15afb166faae714f1cdd633306952.jpg\\\", \\\"coverWidth\\\": 500, \\\"coverHeight\\\": 667}, \\\"caseStyle\\\": [3, 33], \\\"caseVideos\\\": [], \\\"makeupType\\\": \\\"珍珠妆\\\", \\\"relateInfo\\\": {\\\"ownerId\\\": 606976545, \\\"mtShopIds\\\": [606976545], \\\"ownerType\\\": 1}, \\\"sampleType\\\": 1, \\\"themeScene\\\": [], \\\"relatedDeal\\\": [], \\\"caseCategory\\\": 3, \\\"clothingType\\\": \\\"旗袍\\\", \\\"infoContentId\\\": 1333886025545, \\\"sys_createTime\\\": 1725952222000, \\\"photographyStyle\\\": [\\\"古风古装\\\", \\\"新中式\\\"]}\"}]},\"metaData\":{\"67_1\":{\"bizLineCode\":20,\"entityName\":\"digest_photoTheme\"}}}";
        QueryDigestResponseDTO digestResponseDTO = JSON.parseObject(res, QueryDigestResponseDTO.class);
        Future topExhibitInfoFuture = CompletableFuture.completedFuture(digestResponseDTO);
        ctx.getFutureCtx().setTopExhibitInfoFuture(topExhibitInfoFuture);
        ExhibitContentDTO exhibitContentDTO = new ExhibitContentDTO();

        String arr = "[{\"tags\":[{\"name\":\"汉服\",\"style\":0},{\"name\":\"日系居家\",\"style\":0}],\"urls\":[{\"height\":667,\"width\":500,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/dfeb05916e04c1a985aceeee9bc3a7f4228512.png\",\"imageBestScale\":\"3:4\"},{\"height\":360,\"width\":640,\"type\":1,\"url\":\"https://p1.meituan.net/dpmerchantpic/6bc425604b443b1b2476e5f8298164dc384219.png\",\"imageBestScale\":\"16:9\"},{\"height\":360,\"width\":640,\"type\":1,\"url\":\"https://p0.meituan.net/dpmerchantpic/3edbb588d3b7fbf1e1fbfc427537ea61368105.png\",\"imageBestScale\":\"16:9\"},{\"height\":360,\"width\":640,\"type\":1,\"url\":\"https://p0.meituan.net/dpmerchantpic/33fff267b8244cfa4bde9445f3911b3a422912.png\",\"imageBestScale\":\"16:9\"},{\"height\":360,\"width\":640,\"type\":1,\"url\":\"https://p0.meituan.net/dpmerchantpic/673400d45471ea01cad76d669453f5c7466046.png\",\"imageBestScale\":\"16:9\"},{\"height\":1810,\"width\":1079,\"type\":1,\"url\":\"https://p0.meituan.net/dpmerchantpic/71d1aa5419ac8ec1d7ede0681aedd9a6421724.jpg\",\"imageBestScale\":\"9:16\"}],\"name\":\"换装\",\"itemId\":\"1333908025545\"},{\"tags\":[{\"name\":\"古风古装\",\"style\":0},{\"name\":\"新中式\",\"style\":0}],\"urls\":[{\"height\":667,\"width\":500,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/7474488cf15afb166faae714f1cdd633306952.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":0,\"width\":0,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/3311c8897a9158fd918e649b6c8fad39179996.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":0,\"width\":0,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/fe925f016c5f60dfa91b336ae4ede7b6183997.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":0,\"width\":0,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/a3bd29b7b355ecce066ae9bd821f19f956980.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":0,\"width\":0,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/a3bd29b7b355ecce066ae9bd821f19f956980.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":0,\"width\":0,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/a3bd29b7b355ecce066ae9bd821f19f956980.jpg\",\"imageBestScale\":\"3:4\"}],\"name\":\"123123\",\"itemId\":\"1333886025545\"},{\"tags\":[{\"name\":\"森系\",\"style\":0}],\"urls\":[{\"height\":667,\"width\":500,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/9ff2d7cc460b54f7fba35e072f62ed42292225.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":667,\"width\":1000,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/fe925f016c5f60dfa91b336ae4ede7b6183997.jpg\",\"imageBestScale\":\"4:3\"},{\"height\":667,\"width\":1000,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/fe925f016c5f60dfa91b336ae4ede7b6183997.jpg\",\"imageBestScale\":\"4:3\"},{\"height\":0,\"width\":0,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/3311c8897a9158fd918e649b6c8fad39179996.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":0,\"width\":0,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/a3bd29b7b355ecce066ae9bd821f19f956980.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":0,\"width\":0,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/a3bd29b7b355ecce066ae9bd821f19f956980.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":0,\"width\":0,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/93f49fb6919aca35f5d92f34ed83949e40665.jpg\",\"imageBestScale\":\"3:4\"}],\"name\":\"情侣写真主题1\",\"itemId\":\"1333885025545\"},{\"tags\":[{\"name\":\"古风古装\",\"style\":0},{\"name\":\"汉服\",\"style\":0},{\"name\":\"戏曲\",\"style\":0}],\"urls\":[{\"height\":0,\"width\":0,\"type\":2,\"url\":\"https://s3.meituan.net/v1/mss_44977afbb0cb409db8736441cf62a2d1/video/82580de7b35377eae253005f58e373dd.mp4\",\"thumbPic\":\"https://p1.meituan.net/wedding/f71b97c222843edc53ece432e062dd2a544661.jpg\",\"imageBestScale\":\"3:4\",\"spritePic\":{\"spritePicUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/c75fc735c3df90aa23490d6354860078119745.jpg\",\"totalCount\":44,\"row\":10,\"column\":10,\"spriteCellSize\":{\"height\":112,\"width\":200},\"allSpriteImageSize\":{\"height\":1120,\"width\":2000}}},{\"height\":183,\"width\":137,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/7a8344b4c5a857d525878fe6fa8bb8d227576.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":800,\"width\":800,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/e8183eddaad1c1b45f3b71f42009e252211549.jpg\",\"imageBestScale\":\"1:1\"},{\"height\":998,\"width\":1480,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/ab4d7b9d39e2635414abb8804bd16117250949.jpg\",\"imageBestScale\":\"4:3\"},{\"height\":998,\"width\":1480,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/4936bb7eb222cfe7a5b8e44c32f59288251794.jpg\",\"imageBestScale\":\"4:3\"},{\"height\":998,\"width\":1480,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/5534591e78e3c04680cc78fe8c98d337251972.jpg\",\"imageBestScale\":\"4:3\"},{\"height\":998,\"width\":1480,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/24d33a3b8fb01021404bca78a42eda1c251021.jpg\",\"imageBestScale\":\"4:3\"}],\"name\":\"个人写真\",\"itemId\":\"1333873025545\"},{\"tags\":[{\"name\":\"汉服\",\"style\":0},{\"name\":\"其他\",\"style\":0},{\"name\":\"纪实\",\"style\":0}],\"urls\":[{\"height\":800,\"width\":600,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/8a76094348d80489cdb2b15c5652ae52584533.png\",\"imageBestScale\":\"3:4\"},{\"height\":1548,\"width\":1152,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/d770923e973e7d0c000cd275247e14771472087.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":998,\"width\":1480,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/4936bb7eb222cfe7a5b8e44c32f59288251794.jpg\",\"imageBestScale\":\"4:3\"},{\"height\":998,\"width\":1480,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/ab4d7b9d39e2635414abb8804bd16117250949.jpg\",\"imageBestScale\":\"4:3\"},{\"height\":998,\"width\":1480,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/5534591e78e3c04680cc78fe8c98d337251972.jpg\",\"imageBestScale\":\"4:3\"},{\"height\":998,\"width\":1480,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/24d33a3b8fb01021404bca78a42eda1c251021.jpg\",\"imageBestScale\":\"4:3\"}],\"name\":\"情侣写真\",\"itemId\":\"1333874025545\"},{\"tags\":[{\"name\":\"汉服\",\"style\":0},{\"name\":\"古风古装\",\"style\":0}],\"urls\":[{\"height\":667,\"width\":500,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/dfeb05916e04c1a985aceeee9bc3a7f4228512.png\",\"imageBestScale\":\"3:4\"},{\"height\":400,\"width\":300,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/8fd2acb46e206bc796ed34f654c6ffa520402.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":400,\"width\":300,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/0616e71c1a5656e138e5eed216a9f83212504.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":400,\"width\":300,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/7bd2ef482c673ce6febf08387c786bc519822.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":400,\"width\":300,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/fb3022c37efd606b2e524f47ec7e728d12939.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":400,\"width\":300,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/2f4cd4fb44020de1faf6ee15d3f1a60e29200.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":400,\"width\":300,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/c01dd43d6a8d2e91d5803bd9917071ff6906.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":400,\"width\":300,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/45badba8982b5195404cd9926b4e3b408903.jpg\",\"imageBestScale\":\"3:4\"}],\"name\":\"神明少女\",\"itemId\":\"1333939025545\"},{\"tags\":[{\"name\":\"神明少女\",\"style\":0},{\"name\":\"清冷\",\"style\":0},{\"name\":\"肖像\",\"style\":0}],\"urls\":[{\"height\":1680,\"width\":1260,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/6d299e2a5090c5320f8bb56e1d13007e1289762.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":1831,\"width\":1260,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/d2b660b62d748e98ad1312061890e8fb1914676.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":2049,\"width\":1260,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/eb30795e8cf5e221fa87dcd429517f2f2273508.jpg\",\"imageBestScale\":\"9:16\"},{\"height\":1958,\"width\":1260,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/d64db62de46170fc1bdf2160c00318ad1755780.jpg\",\"imageBestScale\":\"9:16\"},{\"height\":1822,\"width\":1260,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/3a089a66d5916d5f335930d5c0f818602216782.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":2013,\"width\":1260,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/7fde6f4b025871bfd3d7d3bb4c70fff31971406.jpg\",\"imageBestScale\":\"9:16\"},{\"height\":2720,\"width\":1260,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/b5e2850632e44c9e68e10b43fdc088091330305.jpg\",\"imageBestScale\":\"9:16\"}],\"name\":\"孕妇照主题1\",\"itemId\":\"1333935025545\"},{\"tags\":[{\"name\":\"复古\",\"style\":0},{\"name\":\"森系\",\"style\":0}],\"urls\":[{\"height\":571,\"width\":428,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/eee85904961ab27f5c921845e307179f233820.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":400,\"width\":300,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/0616e71c1a5656e138e5eed216a9f83212504.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":400,\"width\":300,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/c01dd43d6a8d2e91d5803bd9917071ff6906.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":400,\"width\":300,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/2f4cd4fb44020de1faf6ee15d3f1a60e29200.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":400,\"width\":300,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/8fd2acb46e206bc796ed34f654c6ffa520402.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":400,\"width\":300,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/fb3022c37efd606b2e524f47ec7e728d12939.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":400,\"width\":300,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/45badba8982b5195404cd9926b4e3b408903.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":400,\"width\":300,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/7bd2ef482c673ce6febf08387c786bc519822.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":686,\"width\":500,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/fe05b274a21ab1317282d60ca7e0678821114.webp\",\"imageBestScale\":\"3:4\"},{\"height\":571,\"width\":600,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/a3bd29b7b355ecce066ae9bd821f19f956980.jpg\",\"imageBestScale\":\"1:1\"},{\"height\":667,\"width\":1000,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/fe925f016c5f60dfa91b336ae4ede7b6183997.jpg\",\"imageBestScale\":\"4:3\"},{\"height\":667,\"width\":1000,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/fe925f016c5f60dfa91b336ae4ede7b6183997.jpg\",\"imageBestScale\":\"4:3\"},{\"height\":1619,\"width\":3791,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/b6c3c5ea5fc19cece917bbdac6a478f0794697.png\",\"imageBestScale\":\"16:9\"},{\"height\":0,\"width\":0,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/3311c8897a9158fd918e649b6c8fad39179996.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":0,\"width\":0,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/15a39a96f6d8df07c6c376904fa4e05611219.png\",\"imageBestScale\":\"3:4\"},{\"height\":0,\"width\":0,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/a3bd29b7b355ecce066ae9bd821f19f956980.jpg\",\"imageBestScale\":\"3:4\"}],\"name\":\"丽江旅拍\",\"itemId\":\"1333904025545\"},{\"tags\":[{\"name\":\"藏服\",\"style\":0},{\"name\":\"苗族\",\"style\":0}],\"urls\":[{\"height\":571,\"width\":428,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/a58f5685366d15a74553ebfea735fe7e233936.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":800,\"width\":800,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/3001d77a1803a9111d58a0045d13ccca779767.png\",\"imageBestScale\":\"1:1\"},{\"height\":800,\"width\":800,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/6510b3e630a0f72526f9d5ccace60dfb1484517.png\",\"imageBestScale\":\"1:1\"},{\"height\":800,\"width\":800,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/f8d4088b08f7465a3f679794af932cbb162913.jpg\",\"imageBestScale\":\"1:1\"},{\"height\":800,\"width\":800,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/cf90985643587c29352de7ac776ef086187203.jpg\",\"imageBestScale\":\"1:1\"},{\"height\":800,\"width\":800,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/a76e4416c60a65dc8970cb3450db99e2128233.jpg\",\"imageBestScale\":\"1:1\"}],\"name\":\"宝宝照\",\"itemId\":\"1333915025545\"},{\"tags\":[{\"name\":\"公主风\",\"style\":0},{\"name\":\"神明少女\",\"style\":0},{\"name\":\"童话风\",\"style\":0}],\"urls\":[{\"height\":1680,\"width\":1260,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/af4db138301940918d3edb483c4de2941545763.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":1641,\"width\":1260,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/16ad5e36ce3642e03c20bf0201d694901745477.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":1913,\"width\":1260,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/ce5ccf9680f53cce6e0d75931839e69f1791522.jpg\",\"imageBestScale\":\"3:4\"},{\"height\":2720,\"width\":1260,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/dd9d153411a0ae9ad6aba7e1552e62831710727.jpg\",\"imageBestScale\":\"9:16\"},{\"height\":2285,\"width\":1260,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/f04c3ee41f1c6f8dd3d7ffd2490a57cd2194742.jpg\",\"imageBestScale\":\"9:16\"},{\"height\":2720,\"width\":1260,\"type\":1,\"url\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/4e5f0c5467379486bcf7affcd82643d11465356.jpg\",\"imageBestScale\":\"9:16\"}],\"name\":\"个人写真2\",\"itemId\":\"1333897025545\"}]";

        JSONArray jsonArray = JSON.parseArray(arr);

        exhibitContentDTO.setItems(convert(jsonArray));
        dealExhibitInfoProcessor.addTopExhibitInfo(ctx, exhibitContentDTO);

        String data1 = "{\"caseCategory\":3,\"caseCover\":{\"coverHeight\":667,\"coverUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/dfeb05916e04c1a985aceeee9bc3a7f4228512.png\",\"coverWidth\":500},\"caseName\":\"神明少女\",\"casePics\":[{\"picHeight\":400,\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/8fd2acb46e206bc796ed34f654c6ffa520402.jpg@300_400m\",\"picWidth\":300},{\"picHeight\":400,\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/0616e71c1a5656e138e5eed216a9f83212504.jpg@300_400m\",\"picWidth\":300},{\"picHeight\":400,\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/7bd2ef482c673ce6febf08387c786bc519822.jpg@300_400m\",\"picWidth\":300},{\"picHeight\":400,\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/fb3022c37efd606b2e524f47ec7e728d12939.jpg@300_400m\",\"picWidth\":300},{\"picHeight\":400,\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/2f4cd4fb44020de1faf6ee15d3f1a60e29200.jpg@300_400m\",\"picWidth\":300},{\"picHeight\":400,\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/c01dd43d6a8d2e91d5803bd9917071ff6906.jpg@300_400m\",\"picWidth\":300},{\"picHeight\":400,\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/45badba8982b5195404cd9926b4e3b408903.jpg@300_400m\",\"picWidth\":300}],\"caseStyle\":[15],\"caseVideos\":[],\"clothingType\":\"汉服\",\"editTime\":1726132346000,\"infoContentId\":1333939025545,\"makeupType\":\"大唐公主\",\"oldContentId\":0,\"photographyStyle\":[\"宫廷风\"],\"rank\":1726023852453,\"relateInfo\":{\"mtShopIds\":[606976545],\"ownerId\":606976545,\"ownerType\":1},\"sampleType\":1,\"sys_createTime\":1726023859000,\"themeScene\":[],\"top\":0}";
        String data = "{\"caseVideos\":[],\"themeScene\":[],\"editTime\":1726046295000,\"sys_createTime\":1725952222000,\"sampleType\":1,\"photographyStyle\":[\"古风古装\",\"新中式\"],\"oldContentId\":0,\"relateInfo\":{\"ownerType\":1,\"mtShopIds\":[606976545],\"ownerId\":606976545},\"top\":0,\"caseCover\":{\"coverUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/7474488cf15afb166faae714f1cdd633306952.jpg\",\"coverHeight\":667,\"coverWidth\":500},\"caseStyle\":[3,33],\"rank\":1725952215197,\"caseName\":\"123123\",\"casePics\":[{\"picWidth\":0,\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/3311c8897a9158fd918e649b6c8fad39179996.jpg\",\"picHeight\":0},{\"picWidth\":0,\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/fe925f016c5f60dfa91b336ae4ede7b6183997.jpg\",\"picHeight\":0},{\"picWidth\":0,\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/a3bd29b7b355ecce066ae9bd821f19f956980.jpg\",\"picHeight\":0},{\"picWidth\":0,\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/a3bd29b7b355ecce066ae9bd821f19f956980.jpg\",\"picHeight\":0},{\"picWidth\":0,\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/a3bd29b7b355ecce066ae9bd821f19f956980.jpg\",\"picHeight\":0}],\"caseCategory\":3,\"clothingType\":\"旗袍\",\"makeupType\":\"珍珠妆\",\"infoContentId\":1333886025545}";
        DigestMaterialInfoPhotoDTO materialInfoPhotoDTO = JSON.parseObject(data, DigestMaterialInfoPhotoDTO.class);
        ExhibitImageItemVO result = dealExhibitInfoProcessor.convertTOExhibitImageItemVO(materialInfoPhotoDTO, exhibitContentDTO);
        assertNotNull(result);
    }

    private List<ExhibitImageItemVO> convert(JSONArray jsonArray) {
        List<ExhibitImageItemVO> items = Lists.newArrayList();
        List<ImageUrlVO> urls = Lists.newArrayList();
        ImageUrlVO urlVO = ImageUrlVO.builder()
                .url("")
                .width(1)
                .height(2)
                .imageBestScale("3:4")
                .build();
        urls.add(urlVO);


        jsonArray.forEach(e -> {
            JSONObject json = (JSONObject) e;
            ExhibitImageItemVO vo = new ExhibitImageItemVO();
            vo.setName(json.getString("name"));
            vo.setItemId(json.getString("itemId"));
            vo.setUrls(urls);
            items.add(vo);
        });
        return items;
    }

//    @Test
//    public void testGetPhotoStyle() {
//        String str1 = "[{\"name\":\"product_finish_date\",\"value\":[\"1970-01-01 08:00:00\"],\"source\":0},{\"name\":\"calc_holiday_available\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_business_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_channel_id_allowed\",\"value\":[\"0\"],\"source\":0},{\"name\":\"relateThemeMode\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_can_use_coupon\",\"value\":[\"true\"],\"source\":0},{\"name\":\"preSaleTag\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_third_party_verify\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_block_stock\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_discount_rule_id\",\"value\":[\"0\"],\"source\":0},{\"name\":\"service_type\",\"value\":[\"服装租赁\"],\"source\":0},{\"name\":\"tag_unifyProduct\",\"value\":[\"0\"],\"source\":0},{\"name\":\"photo_style\",\"value\":[\"30839877025225\",\"30847571025225\",\"30854362025225\",\"30860123025225\",\"30847510025225\"],\"source\":0},{\"name\":\"reservation_is_needed_or_not\",\"value\":[\"是\"],\"source\":0},{\"name\":\"sys_deal_universal_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"category\",\"value\":[\"90\",\"9011\"],\"source\":0}]";
//        List<AttributeDTO> attrs1 = JSON.parseArray(str1, AttributeDTO.class);
//        List<String> ids = dealExhibitInfoProcessorUtils.getPhotoStyle(attrs1);
//        assertEquals(5, ids.size());
//    }

    @Test
    public void testGetPhotoStyleIds() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        String str1 = "[{\"name\":\"product_finish_date\",\"value\":[\"1970-01-01 08:00:00\"],\"source\":0},{\"name\":\"calc_holiday_available\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_business_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_channel_id_allowed\",\"value\":[\"0\"],\"source\":0},{\"name\":\"relateThemeMode\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_can_use_coupon\",\"value\":[\"true\"],\"source\":0},{\"name\":\"preSaleTag\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_third_party_verify\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_block_stock\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_discount_rule_id\",\"value\":[\"0\"],\"source\":0},{\"name\":\"service_type\",\"value\":[\"服装租赁\"],\"source\":0},{\"name\":\"tag_unifyProduct\",\"value\":[\"0\"],\"source\":0},{\"name\":\"photo_style\",\"value\":[\"30839877025225\",\"30847571025225\",\"30854362025225\",\"30860123025225\",\"30847510025225\"],\"source\":0},{\"name\":\"reservation_is_needed_or_not\",\"value\":[\"是\"],\"source\":0},{\"name\":\"sys_deal_universal_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"category\",\"value\":[\"90\",\"9011\"],\"source\":0}]";
        List<AttributeDTO> attrs1 = JSON.parseArray(str1, AttributeDTO.class);
        ctx.setAttrs(attrs1);
        List<String> ss = dealExhibitInfoProcessor.getPhotoStyleIds(ctx);
        assertEquals(5, ss.size());
    }

    @Test
    public void testGetExhibitInfo() {
        EnvCtx envCtx = new EnvCtx();
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(910);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setChannelDTO(channelDTO);
        String str1 = "[{\"name\":\"product_finish_date\",\"value\":[\"1970-01-01 08:00:00\"],\"source\":0},{\"name\":\"calc_holiday_available\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_business_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_channel_id_allowed\",\"value\":[\"0\"],\"source\":0},{\"name\":\"relateThemeMode\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_can_use_coupon\",\"value\":[\"true\"],\"source\":0},{\"name\":\"preSaleTag\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_third_party_verify\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_block_stock\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_discount_rule_id\",\"value\":[\"0\"],\"source\":0},{\"name\":\"service_type\",\"value\":[\"服装租赁\"],\"source\":0},{\"name\":\"tag_unifyProduct\",\"value\":[\"0\"],\"source\":0},{\"name\":\"photo_style\",\"value\":[\"30839877025225\",\"30847571025225\",\"30854362025225\",\"30860123025225\",\"30847510025225\"],\"source\":0},{\"name\":\"reservation_is_needed_or_not\",\"value\":[\"是\"],\"source\":0},{\"name\":\"sys_deal_universal_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"category\",\"value\":[\"90\",\"9011\"],\"source\":0}]";
        List<AttributeDTO> attrs1 = JSON.parseArray(str1, AttributeDTO.class);
        ctx.setAttrs(attrs1);
        String idsStr = "[\"1334754025545\",\"1334733025545\",\"1334668025545\",\"1334662025545\",\"1334377025545\",\"1334015025545\",\"1333939025545\",\"1333997025545\",\"1334011025545\",\"1333915025545\"]";
        List<String> ids = JSONArray.parseArray(idsStr, String.class);
        Map<String, ExhibitTextConfig> configMap = new HashMap<>();
        ExhibitTextConfig config = new ExhibitTextConfig();
        config.setTitle("title");
        configMap.put("910", config);
        lionMockedStatic.when(() -> Lion.getMap(LionConstants.APP_KEY, LionConstants.CATEGORY_EXHIBIT_TITLE_CONFIG, ExhibitTextConfig.class, Collections.EMPTY_MAP)).thenReturn(configMap);
        ExhibitContentDTO exhibitContentDTO = dealExhibitInfoProcessor.getExhibitInfo(ctx, ids);
        assertNotNull(exhibitContentDTO);
    }


    @Test
    public void attrDataConvertExhibImageItemVo() {
        String a1str = "{\"caseCategory\":3,\"caseCover\":{\"coverHeight\":730,\"coverUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/5a222b2de85a6fd0f59124bc78942866787016.png\",\"coverWidth\":548},\"caseName\":\"9.25UAT\",\"casePics\":[{\"picHeight\":716,\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/cc0c866eecfc7cf6344942025ee698fb810841.png@1062_716m\",\"picWidth\":1062},{\"picHeight\":412,\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/fcfd7caedaee548ddde696b1155e543d297995.png@1948_412m\",\"picWidth\":1948},{\"picHeight\":722,\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/bac44d690ed8980003ce6ce8314ea52587022.png@1140_722m\",\"picWidth\":1140},{\"picHeight\":92,\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/4227f0fee6c1d02549d9cbbab81307df1435.png@600_92m\",\"picWidth\":600},{\"picHeight\":28,\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/29f776567255bcbbabd8a274a8720bd81045.png@600_28m\",\"picWidth\":600},{\"picHeight\":31,\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/ff23d3cba25a1bcd3d8915bd47b073e6113.png@600_31m\",\"picWidth\":600},{\"picHeight\":66,\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/2c97cd5b41b5f6f68ce3b4e91f4b1ed1433.png@126_66m\",\"picWidth\":126}],\"caseStyle\":[3,33,1],\"clothingType\":\"汉服\",\"editTime\":1727263234000,\"infoContentId\":1334754025545,\"makeupType\":\"簪花\",\"oldContentId\":0,\"photographyStyle\":[\"古风古装\",\"新中式\",\"中国风\"],\"rank\":0,\"relateInfo\":{\"mtShopIds\":[606976545],\"ownerId\":606976545,\"ownerType\":1},\"sampleType\":1,\"sys_createTime\":1727263235000,\"sys_dz_mt_dealId\":[1033798024,1033795972,1033785012,1033792558,1031198018,1032832714,1033794500,1033791229,1033791483,1033867002,1033849305,1034263565,1033864886,1034268527,1033864884,1033849553,1034630042,1034262196,1033862798,1033796333,1033852588,1033806697,1034618871,1033849315,1034260187,1033874176],\"themeScene\":[\"生日\"],\"top\":1727263503363}";
        String a2str = "{\"defaultImageBestScale\":\"3:4\",\"excludeStyleTagKey\":[],\"itemDisplayStyle\":2,\"multiStyleTagKey\":[\"caseStyle\"],\"multiStyleType\":25,\"styleModuleKey\":\"wed_photocase_list_module\",\"title\":\"可选风格作品\"}";
        DigestMaterialInfoPhotoDTO data = JSON.parseObject(a1str, DigestMaterialInfoPhotoDTO.class);
        ExhibitImageConfig data1 = JSON.parseObject(a2str, ExhibitImageConfig.class);
        ExhibitImageItemVO exhibitImageItemVO = dealExhibitInfoProcessor.attrDataConvertExhibImageItemVo(data, data1);
        assertNotNull(exhibitImageItemVO);
    }


    @Test
    public void convertTOImageUrlCalculateProportionVO() {
        String a1 = "{\"caseCategory\":3,\"caseCover\":{\"coverHeight\":730,\"coverUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/5a222b2de85a6fd0f59124bc78942866787016.png\",\"coverWidth\":548},\"caseName\":\"9.25UAT\",\"casePics\":[{\"picHeight\":716,\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/cc0c866eecfc7cf6344942025ee698fb810841.png@1062_716m\",\"picWidth\":1062},{\"picHeight\":412,\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/fcfd7caedaee548ddde696b1155e543d297995.png@1948_412m\",\"picWidth\":1948},{\"picHeight\":722,\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/bac44d690ed8980003ce6ce8314ea52587022.png@1140_722m\",\"picWidth\":1140},{\"picHeight\":92,\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/4227f0fee6c1d02549d9cbbab81307df1435.png@600_92m\",\"picWidth\":600},{\"picHeight\":28,\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/29f776567255bcbbabd8a274a8720bd81045.png@600_28m\",\"picWidth\":600},{\"picHeight\":31,\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/ff23d3cba25a1bcd3d8915bd47b073e6113.png@600_31m\",\"picWidth\":600},{\"picHeight\":66,\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/2c97cd5b41b5f6f68ce3b4e91f4b1ed1433.png@126_66m\",\"picWidth\":126}],\"caseStyle\":[3,33,1],\"clothingType\":\"汉服\",\"editTime\":1727263234000,\"infoContentId\":1334754025545,\"makeupType\":\"簪花\",\"oldContentId\":0,\"photographyStyle\":[\"古风古装\",\"新中式\",\"中国风\"],\"rank\":0,\"relateInfo\":{\"mtShopIds\":[606976545],\"ownerId\":606976545,\"ownerType\":1},\"sampleType\":1,\"sys_createTime\":1727263235000,\"sys_dz_mt_dealId\":[1033798024,1033795972,1033785012,1033792558,1031198018,1032832714,1033794500,1033791229,1033791483,1033867002,1033849305,1034263565,1033864886,1034268527,1033864884,1033849553,1034630042,1034262196,1033862798,1033796333,1033852588,1033806697,1034618871,1033849315,1034260187,1033874176],\"themeScene\":[\"生日\"],\"top\":1727263503363}";
        String a2 = "3:4";
        DigestMaterialInfoPhotoDTO data = JSON.parseObject(a1, DigestMaterialInfoPhotoDTO.class);
        List<ImageUrlVO> imageUrlVOS = dealExhibitInfoProcessor.convertTOImageUrlCalculateProportionVO(data, a2);
        assertEquals(imageUrlVOS.size(), 8);
    }

    /**
     * 测试当ImmersiveImageVO为null时的场景
     */
//    @Test
//    public void testHandleNewExhibitCategory_ImmersiveImageVONull() {
//        DealCtx ctx = mock(DealCtx.class);
//        ExhibitTextConfig exhibitTextConfig = mock(ExhibitTextConfig.class);
//        SearchDetailResponseDTO responseDTO = mock(SearchDetailResponseDTO.class);
//        when(ctx.getFutureCtx()).thenReturn(null);
//        when(contentSearchWrapper.getFutureResult((Future) new SearchDetailResponseDTO())).thenReturn(responseDTO);
//        when(contentSearchWrapper.getImmersiveImageVO(responseDTO, null, ctx.getCategoryId(), ctx.getQueryExhibitImageParam())).thenReturn(null);
//        dealExhibitInfoProcessor.handleNewExhibitCategory(ctx, exhibitTextConfig);
//        verify(immersiveImageWrapper, never()).jumpUrlToShopPage(any(DealCtx.class), any());
//    }

    /**
     * 测试当ImmersiveImageVO不为null时的场景
     */
//    @Test
//    public void testHandleNewExhibitCategory_ImmersiveImageVONotNull() {
//        DealCtx ctx = mock(DealCtx.class);
//        ExhibitTextConfig exhibitTextConfig = mock(ExhibitTextConfig.class);
//        SearchDetailResponseDTO responseDTO = mock(SearchDetailResponseDTO.class);
//        ImmersiveImageVO immersiveImageVO = mock(ImmersiveImageVO.class);
//        when(ctx.getFutureCtx()).thenReturn(null);
//        when(contentSearchWrapper.getFutureResult((Future) new SearchDetailResponseDTO())).thenReturn(responseDTO);
//        when(contentSearchWrapper.getImmersiveImageVO(responseDTO, null, ctx.getCategoryId(), ctx.getQueryExhibitImageParam())).thenReturn(immersiveImageVO);
//        dealExhibitInfoProcessor.handleNewExhibitCategory(ctx, exhibitTextConfig);
//        verify(immersiveImageWrapper, times(1)).jumpUrlToShopPage(eq(ctx), any());
//    }

    /**
     * 测试当ContentSearchWrapper返回的SearchDetailResponseDTO为null时的场景
     */
//    @Test
//    public void testHandleNewExhibitCategory_SearchDetailResponseDTONull() {
//        DealCtx ctx = mock(DealCtx.class);
//        ExhibitTextConfig exhibitTextConfig = mock(ExhibitTextConfig.class);
//        when(ctx.getFutureCtx()).thenReturn(null);
//        when(contentSearchWrapper.getFutureResult((Future) new SearchDetailResponseDTO())).thenReturn(null);
//        dealExhibitInfoProcessor.handleNewExhibitCategory(ctx, exhibitTextConfig);
//        verify(contentSearchWrapper, never()).getImmersiveImageVO(null, null, ctx.getCategoryId(), ctx.getQueryExhibitImageParam());
//        verify(immersiveImageWrapper, never()).jumpUrlToShopPage(any(DealCtx.class), any());
//    }

    /**
     * 测试当ExhibitTextConfig为null时的场景
     */
    @Test(expected = NullPointerException.class)
    public void testHandleNewExhibitCategory_ExhibitTextConfigNull() {
        DealCtx ctx = mock(DealCtx.class);
        ExhibitTextConfig exhibitTextConfig = null; // 模拟ExhibitTextConfig为null的情况

        dealExhibitInfoProcessor.handleNewExhibitCategory(ctx, exhibitTextConfig);

        // 由于ExhibitTextConfig为null，预期会抛出NullPointerException
    }

    /**
     * 测试当DealCtx为null时的场景
     */
    @Test(expected = NullPointerException.class)
    public void testHandleNewExhibitCategory_DealCtxNull() {
        DealCtx ctx = null; // 模拟DealCtx为null的情况
        ExhibitTextConfig exhibitTextConfig = mock(ExhibitTextConfig.class);

        dealExhibitInfoProcessor.handleNewExhibitCategory(ctx, exhibitTextConfig);

        // 由于DealCtx为null，预期会抛出NullPointerException
    }

    @Test
    public void testGetOldExhibitInfoFuture_WhenIdsIsNull() throws Exception {
        // 准备数据
//        when(dealCtx.getInfoContentId()).thenReturn(123L);
//        when(wedPhotoCaseService.batchLoadCaseList(any())).thenReturn(null);

        // 执行
        Future future = digestQueryWrapper.getOldExhibitInfoFuture(dealCtx, null);

        // 验证);
        assertNull(future);
    }

    @Test
    public void testGetOldExhibitInfoFuture_WhenIdsContainsInvalidNumber() throws Exception {
        // 准备数据
        List<String> ids = Arrays.asList("123", "456", "abc", "789");

        // 执行
        digestQueryWrapper.getOldExhibitInfoFuture(dealCtx, ids);

        // 验证
        assertEquals(Arrays.asList("123", "456", "abc", "789"), ids);
    }

    @Test
    public void testGetOldExhibitInfoFuture_WhenServiceThrowsException() throws Exception {
        // 准备数据
//        when(dealCtx.getInfoContentId()).thenReturn(123L);
//        when(wedPhotoCaseService.batchLoadCaseList(any())).thenThrow(new RuntimeException("Service error"));

        // 执行
        Future result = digestQueryWrapper.getOldExhibitInfoFuture(dealCtx, null);

        // 验证
        assertNull(result);
    }

    @Test
    public void testGetOldExhibitInfoFuture_Success() throws Exception {
        // 准备数据
        List<String> ids = Arrays.asList("123", "456");

        // 执行
        digestQueryWrapper.getOldExhibitInfoFuture(dealCtx, ids);

        List<Long> expectedIds = Arrays.asList(123L, 456L);
        assertNotNull(expectedIds);
    }


    @Test
    public void testGetSafeOldCaseInfo_WhenFutureIsNull() {
        WedPhotoCaseInfoDTO result = digestQueryWrapper.getSafeOldCaseInfo(null, "123");
        assertNull(result);
    }

    @Test
    public void testGetSafeOldCaseInfo_WhenRespDTOIsNull() {
        // 准备数据
        Future future = mock(Future.class);
//        when(digestQueryWrapper.getFutureResult(future)).thenReturn(null);

        // 执行
        WedPhotoCaseInfoDTO result = digestQueryWrapper.getSafeOldCaseInfo(future, "123");

        // 验证
        assertNull(result);
    }

    @Test
    public void testGetSafeOldCaseInfo_WhenDataInfoListIsEmpty() {
        // 准备数据
        Future future = mock(Future.class);
        BatchLoadCaseRespDTO respDTO = new BatchLoadCaseRespDTO();
        respDTO.setDataInfoList(Collections.emptyList());
//        when(digestQueryWrapper.getFutureResult(future)).thenReturn(respDTO);

        // 执行
        WedPhotoCaseInfoDTO result = digestQueryWrapper.getSafeOldCaseInfo(future, "123");

        // 验证
        assertNull(result);
    }

    @Test
    public void testGetSafeOldCaseInfo_WhenCaseNotFound() {
        // 准备数据
        Future future = mock(Future.class);
        BatchLoadCaseRespDTO respDTO = new BatchLoadCaseRespDTO();
        List<WedPhotoCaseInfoDTO> dataList = new ArrayList<>();
        WedPhotoCaseInfoDTO caseInfo = new WedPhotoCaseInfoDTO();
        caseInfo.setCaseId(456L);
        dataList.add(caseInfo);
        respDTO.setDataInfoList(dataList);
//        when(digestQueryWrapper.getFutureResult(future)).thenReturn(respDTO);

        // 执行
        WedPhotoCaseInfoDTO result = digestQueryWrapper.getSafeOldCaseInfo(future, "123");

        // 验证
        assertNull(result);
    }

    @Test
    public void testGetSafeOldCaseInfo_Success() {
        // 准备数据
        Future future = mock(Future.class);
        BatchLoadCaseRespDTO respDTO = new BatchLoadCaseRespDTO();
        List<WedPhotoCaseInfoDTO> dataList = new ArrayList<>();
        WedPhotoCaseInfoDTO caseInfo = new WedPhotoCaseInfoDTO();
        caseInfo.setCaseId(123L);
        dataList.add(caseInfo);
        respDTO.setDataInfoList(dataList);
//        when(digestQueryWrapper.getFutureResult(future)).thenReturn(respDTO);

        // 执行
        WedPhotoCaseInfoDTO result = digestQueryWrapper.getSafeOldCaseInfo(future, "123");

        // 验证
        assertNull(result);
    }

    @Test
    public void testGetOldCaseTags_WhenPhotoStyleIsNull() {
        // 准备数据
        WedPhotoCaseInfoDTO dto = new WedPhotoCaseInfoDTO();
        dto.setPhotoStyle(null);

        // 执行
        List<ImageTagVO> result = dealExhibitInfoProcessor.getOldCaseTags(dto);

        // 验证
        assertNull(result);
    }
    @Test
    public void testGetOldCaseTags_Success() {
        // 准备数据
        WedPhotoCaseInfoDTO dto = new WedPhotoCaseInfoDTO();
        List<String> photoStyle = Arrays.asList("韩式", "森系", "小清新");
        dto.setPhotoStyle(photoStyle);

        // 执行
        List<ImageTagVO> result = dealExhibitInfoProcessor.getOldCaseTags(dto);

        // 验证
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("韩式", result.get(0).getName());
        assertEquals("森系", result.get(1).getName());
        assertEquals("小清新", result.get(2).getName());
    }

    @Test
    public void testGetOldCaseIds_WhenIdsIsEmpty() {
        // 准备数据
        List<AttributeDTO> emptyAttrs = new ArrayList<>();
        when(dealCtx.getAttrs()).thenReturn(emptyAttrs);

        // 执行
        List<String> result = dealExhibitInfoProcessor.getOldCaseIds(dealCtx);

        // 验证
        assertTrue(result.isEmpty());
    }
    @Test
    public void testGetOldCaseIds() {
        // 1. 准备测试数据
        DealCtx ctx = mock(DealCtx.class);
        List<AttributeDTO> attrs = new ArrayList<>();
        when(ctx.getAttrs()).thenReturn(attrs);

        DealExhibitInfoProcessor processor = new DealExhibitInfoProcessor();

        // 4. 测试超过10个ID的情况
        List<String> largeIdList = Arrays.asList(
                "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"
        );
        try (MockedStatic<DealExhibitInfoProcessorUtils> mockedStatic = mockStatic(DealExhibitInfoProcessorUtils.class)) {
            mockedStatic.when(() -> DealExhibitInfoProcessorUtils.getPhotoStyle(any()))
                    .thenReturn(largeIdList);
            List<String> result3 = processor.getOldCaseIds(ctx);
            assertEquals(10, result3.size());
            assertEquals(largeIdList.subList(0, 10), result3);
        }
    }
    @Test
    public void testGetOldCaseInfo() {
        // 1. 准备测试数据
        DealCtx ctx = mock(DealCtx.class);
        FutureCtx futureCtx = mock(FutureCtx.class);
        List<String> ids = Arrays.asList("1", "2", "3");
        List<AttributeDTO> attrs = new ArrayList<>();

        // 2. Mock 基础配置
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
        when(ctx.getCategoryId()).thenReturn(123);
        when(ctx.getAttrs()).thenReturn(attrs);

        // 3. Mock ExhibitImageConfig
        ExhibitImageConfig exhibitImageConfig = new ExhibitImageConfig();
        exhibitImageConfig.setItemDisplayStyle(1);
        exhibitImageConfig.setDefaultImageBestScale("3:4");
        when(contentSearchWrapper.getExhibitImageConfig(anyInt())).thenReturn(exhibitImageConfig);

        // 4. Mock WedPhotoCaseInfoDTO
        WedPhotoCaseInfoDTO caseInfoDTO = new WedPhotoCaseInfoDTO();
        caseInfoDTO.setCaseId(1L);
        caseInfoDTO.setCaseName("测试案例");
        caseInfoDTO.setPhotoStyle(Arrays.asList("风格1", "风格2"));
        List<PicDTO> casePics = new ArrayList<>();
        PicDTO picDTO = new PicDTO();
        picDTO.setUrl("http://test.com/image.jpg");
        picDTO.setWidth(800);
        picDTO.setHeight(600);
        casePics.add(picDTO);
        caseInfoDTO.setCasePics(casePics);

        // 5. Mock getSafeOldCaseInfo
        when(digestQueryWrapper.getSafeOldCaseInfo(any(), eq("1"))).thenReturn(caseInfoDTO);

        // 6. Mock getPhotoStyle
        try (MockedStatic<DealExhibitInfoProcessorUtils> mockedStatic = mockStatic(DealExhibitInfoProcessorUtils.class)) {
            mockedStatic.when(() -> DealExhibitInfoProcessorUtils.getPhotoStyle(any()))
                    .thenReturn(Arrays.asList("1", "2", "3"));

            // 7. Mock getImmersiveImageUrls
            ImageUrlVO mockImageUrlVO = new ImageUrlVO();
            mockImageUrlVO.setUrl("http://test.com/image.jpg");
            mockImageUrlVO.setWidth(800);
            mockImageUrlVO.setHeight(600);
            mockImageUrlVO.setImageBestScale("3:4");
            when(contentSearchWrapper.getImmersiveImageUrls(anyString(), anyLong(), anyLong(), anyString()))
                    .thenReturn(mockImageUrlVO);

            // 8. 执行测试
            ExhibitContentDTO result = dealExhibitInfoProcessor.getOldCaseInfo(ctx, ids);

            // 9. 验证结果
            assertNotNull(result);

        }
    }
    @Test
    public void testOldCaseInfoConvertExhibitImageItemVo_Success() {
        // 准备测试数据
        WedPhotoCaseInfoDTO wedPhotoCaseInfoDTO = new WedPhotoCaseInfoDTO();
        wedPhotoCaseInfoDTO.setCaseId(123L);
        wedPhotoCaseInfoDTO.setCaseName("测试案例");
        wedPhotoCaseInfoDTO.setPhotoStyle(Arrays.asList("风格1", "风格2"));

        List<PicDTO> pics = new ArrayList<>();
        PicDTO pic = new PicDTO();
        pic.setUrl("http://test.com/1.jpg");
        pic.setWidth(800);
        pic.setHeight(600);
        pics.add(pic);
        wedPhotoCaseInfoDTO.setCasePics(pics);

        ExhibitImageConfig exhibitImageConfig = new ExhibitImageConfig();
        exhibitImageConfig.setDefaultImageBestScale("3:4");

        // Mock图片URL转换
        ImageUrlVO mockImageUrlVO = new ImageUrlVO();
        mockImageUrlVO.setUrl("http://test.com/1.jpg");
        mockImageUrlVO.setWidth(800);
        mockImageUrlVO.setHeight(600);
        when(contentSearchWrapper.getImmersiveImageUrls(
                anyString(), anyLong(), anyLong(), anyString()
        )).thenReturn(mockImageUrlVO);

        // 执行测试
        ExhibitImageItemVO result = dealExhibitInfoProcessor.oldCaseInfoConvertExhibitImageItemVo(
                wedPhotoCaseInfoDTO,
                exhibitImageConfig
        );

        // 验证结果
        assertNotNull(result);
        assertEquals("123", result.getItemId());
        assertEquals("测试案例", result.getName());
    }
    @Test
    public void testOldCaseInfoConvertExhibitImageItemVo_NullInput() {
        ExhibitImageConfig config = new ExhibitImageConfig();
        ExhibitImageItemVO result = dealExhibitInfoProcessor.oldCaseInfoConvertExhibitImageItemVo(null, config);
        assertNull(result);
    }
    @Test
    public void testConvertTOImageUrlCalculateProportionVO() {
        // 1. 准备测试数据
        DigestMaterialInfoPhotoDTO materialInfoPhotoDTO = new DigestMaterialInfoPhotoDTO();
        String imageBestScale = "3:4";

        // 设置封面图片
        CaseCoverDTO caseCoverDTO = new CaseCoverDTO();
        caseCoverDTO.setCoverUrl("http://test.com/cover.jpg");
        caseCoverDTO.setCoverWidth(1000);
        caseCoverDTO.setCoverHeight(800);
        materialInfoPhotoDTO.setCaseCover(caseCoverDTO);

        // 设置案例图片
        List<CasePicsDTO> casePics = new ArrayList<>();
        CasePicsDTO pic1 = new CasePicsDTO();
        pic1.setPicUrl("http://test.com/pic1.jpg");
        pic1.setPicWidth(800);
        pic1.setPicHeight(600);

        CasePicsDTO pic2 = new CasePicsDTO();
        pic2.setPicUrl("http://test.com/pic2.jpg");
        pic2.setPicWidth(900);
        pic2.setPicHeight(700);

        casePics.add(pic1);
        casePics.add(pic2);
        materialInfoPhotoDTO.setCasePics(casePics);

        // Mock contentSearchWrapper的行为
        ImageUrlVO mockImageUrlVO1 = new ImageUrlVO();
        mockImageUrlVO1.setUrl("http://test.com/pic1.jpg");
        mockImageUrlVO1.setWidth(800);
        mockImageUrlVO1.setHeight(600);

        ImageUrlVO mockImageUrlVO2 = new ImageUrlVO();
        mockImageUrlVO2.setUrl("http://test.com/pic2.jpg");
        mockImageUrlVO2.setWidth(900);
        mockImageUrlVO2.setHeight(700);

        when(contentSearchWrapper.getImmersiveImageUrls(
                eq("http://test.com/pic1.jpg"),
                eq(800L),
                eq(600L),
                eq(imageBestScale)
        )).thenReturn(mockImageUrlVO1);

        when(contentSearchWrapper.getImmersiveImageUrls(
                eq("http://test.com/pic2.jpg"),
                eq(900L),
                eq(700L),
                eq(imageBestScale)
        )).thenReturn(mockImageUrlVO2);

        // 2. 执行测试
        List<ImageUrlVO> result = dealExhibitInfoProcessor.convertTOImageUrlCalculateProportionVO(
                materialInfoPhotoDTO,
                imageBestScale
        );

        // 3. 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());
    }

    @Test
    public void testHandleAttributeKeyStyleCategory_Normal() {
        // 准备测试数据
        DealCtx ctx = mock(DealCtx.class);
        ExhibitTextConfig exhibitTextConfig = mock(ExhibitTextConfig.class);
        List<String> mockIds = Arrays.asList("1", "2", "3");
        ExhibitContentDTO mockContentDTO = new ExhibitContentDTO();

        // 使用 spy 来部分 mock dealExhibitInfoProcessor
        DealExhibitInfoProcessor spyProcessor = spy(dealExhibitInfoProcessor);

        // 只保留必要的 mock 行为
        doReturn(mockIds).when(spyProcessor).getPhotoStyleIds(ctx);
        doReturn(mockContentDTO).when(spyProcessor).getExhibitInfo(eq(ctx), eq(mockIds));
        when(exhibitTextConfig.getTitle()).thenReturn("测试标题");
        when(exhibitTextConfig.getShowAllText()).thenReturn("查看全部");
        when(immersiveImageWrapper.jumpUrlToShopPage(eq(ctx), any(ExhibitContentDTO.class)))
                .thenReturn(mockContentDTO);

        // 执行测试方法
        spyProcessor.handleAttributeKeyStyleCategory(ctx, exhibitTextConfig);

        // 验证结果
        verify(ctx).setExhibitContentDTO(any(ExhibitContentDTO.class));
        verify(exhibitTextConfig).getTitle();
        verify(exhibitTextConfig).getShowAllText();
        verify(immersiveImageWrapper).jumpUrlToShopPage(eq(ctx), any(ExhibitContentDTO.class));
    }

    @Test
    public void testHandleOldCaseInfoCategory() {
        // 1. 准备测试数据
        DealCtx ctx = mock(DealCtx.class);
        FutureCtx futureCtx = new FutureCtx();
        ExhibitTextConfig exhibitTextConfig = new ExhibitTextConfig();
        exhibitTextConfig.setTitle("可拍风格");
        exhibitTextConfig.setShowAllText("全部风格");

        // 2. Mock ExhibitContentDTO
        ExhibitContentDTO mockExhibitContentDTO = new ExhibitContentDTO();
        mockExhibitContentDTO.setItems(Arrays.asList(new ExhibitImageItemVO()));
        mockExhibitContentDTO.setRecordCount(1);

        // 3. Mock必要的依赖和行为
//        when(ctx.getFutureCtx()).thenReturn(futureCtx);
        when(ctx.getCategoryId()).thenReturn(123);
        // Mock immersiveImageWrapper的行为
        when(immersiveImageWrapper.jumpUrlToOldCaseListPage(eq(ctx), any(ExhibitContentDTO.class)))
                .thenReturn(mockExhibitContentDTO);
        // 4. 执行测试
        dealExhibitInfoProcessor.handleOldCaseInfoCategory(ctx, exhibitTextConfig);
    }
    @Test
    public void testGetOldExhibitInfoFuture_WhenCtxIsNull() {
        Future result = digestQueryWrapper.getOldExhibitInfoFuture(null, null);
        assertNull(result);
    }

    @Test
    public void testGetOldExhibitInfoFuture_WhenException() throws Exception {
        // 准备数据
//        when(dealCtx.getInfoContentId()).thenReturn(123L);
//        when(wedPhotoCaseService.batchLoadCaseList(any())).thenThrow(new RuntimeException("mock error"));

        // 执行
        Future result = digestQueryWrapper.getOldExhibitInfoFuture(dealCtx, null);

        // 验证
        assertNull(result);
    }
    @Test
    public void testJumpUrlToOldCaseListPage_NullData() {
        ExhibitContentDTO result = immersiveImageWrapper.jumpUrlToOldCaseListPage(dealCtx, null);
        assertNull(result);
    }

    @Test
    public void testJumpUrlToOldCaseListPage_NullConfig() {
        // 准备数据
        ExhibitContentDTO data = new ExhibitContentDTO();

        // Mock Lion配置
        when(Lion.getBean(anyString(), anyString(), eq(OldCaseListConfig.class), any()))
                .thenReturn(null);

        // 执行测试
        ExhibitContentDTO result = immersiveImageWrapper.jumpUrlToOldCaseListPage(dealCtx, data);
    }
    @Test
    public void testGetterAndSetter() {
        // 创建测试对象
        OldCaseListConfig config = new OldCaseListConfig();

        // 测试 enable
        config.setEnable(true);
        assertTrue(config.isEnable());

        // 测试 categoryList
        List<Integer> categories = Arrays.asList(1, 2, 3);
        config.setCategoryList(categories);
        assertEquals(categories, config.getCategoryList());

        // 测试 mtLink
        String mtLink = "https://meituan.com/test";
        config.setMtLink(mtLink);
        assertEquals(mtLink, config.getMtLink());

        // 测试 dpLink
        String dpLink = "https://dianping.com/test";
        config.setDpLink(dpLink);
        assertEquals(dpLink, config.getDpLink());
    }
    @Test
    public void testJumpUrlToOldCaseListPage_NullInput() {
        ExhibitContentDTO result = immersiveImageWrapper.jumpUrlToOldCaseListPage(dealCtx, null);
        assertNull(result);
    }
    @Test
    public void testJumpUrlToOldCaseListPage_DisabledConfig() {
        ExhibitContentDTO data = new ExhibitContentDTO();
        OldCaseListConfig config = new OldCaseListConfig();
        config.setEnable(false);

        when(Lion.getBean(
                eq(LionConstants.APP_KEY),
                eq(LionConstants.OLD_CASE_LIST_PAGE_CONFIG),
                eq(OldCaseListConfig.class),
                isNull()
        )).thenReturn(config);

        ExhibitContentDTO result = immersiveImageWrapper.jumpUrlToOldCaseListPage(dealCtx, data);
        assertNull(result);
    }
}
