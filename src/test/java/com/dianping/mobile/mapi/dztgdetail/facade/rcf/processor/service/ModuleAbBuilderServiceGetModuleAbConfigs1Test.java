package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.DealCategoryFactory;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.DealDouHuUtil;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.DealRepurchaseConfig;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.TimesDealUtil;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ModuleAbBuilderServiceGetModuleAbConfigs1Test {

    @InjectMocks
    private ModuleAbBuilderService moduleAbBuilderService;

    @Mock
    private DealCategoryFactory dealCategoryFactory;

    @Mock
    private DouHuBiz douHuBiz;

    @Mock
    private DouHuService douHuService;

    @Mock
    private DealCtx ctx;

    @Mock
    private EnvCtx envCtx;

    @Mock
    private DealGroupDTO dealGroupDTO;

    @Mock
    private DealGroupCategoryDTO categoryDTO;

    @Before
    public void setUp() {
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(categoryDTO);
        when(ctx.getModuleAbConfigs()).thenReturn(new ArrayList<>());
    }

    /**
     * Test adding petStyleConfig to moduleAbConfigs when it's not null
     */
    @Test
    public void testGetModuleAbConfigs_WithPetStyleConfig() throws Throwable {
        // arrange
        when(ctx.getCategoryId()).thenReturn(1701);
        when(ctx.isMt()).thenReturn(true);
        when(envCtx.getUnionId()).thenReturn("testUnionId");
        ModuleAbConfig petStyleConfig = new ModuleAbConfig();
        petStyleConfig.setKey("MTPetNewStyle");
        when(douHuBiz.getAbcByUnionId(anyString(), anyString(), anyBoolean())).thenReturn(petStyleConfig);
        // act
        List<ModuleAbConfig> result = moduleAbBuilderService.getModuleAbConfigs(ctx);
        // assert
        assertTrue(result.contains(petStyleConfig));
        verify(douHuBiz).getAbcByUnionId("testUnionId", "MTPetNewStyle", true);
    }

    /**
     * Test adding cardStyleAbConfig to moduleAbConfigs when it's not null
     */
    @Test
    public void testGetModuleAbConfigs_WithCardStyleAbConfig() throws Throwable {
        // arrange
        ModuleAbConfig cardStyleAbConfig = new ModuleAbConfig();
        cardStyleAbConfig.setKey("CardStyleConfig");
        when(ctx.getCardStyleAbConfig()).thenReturn(cardStyleAbConfig);
        // act
        List<ModuleAbConfig> result = moduleAbBuilderService.getModuleAbConfigs(ctx);
        // assert
        assertTrue(result.contains(cardStyleAbConfig));
    }

    /**
     * Test adding cardStyleAbV2Config to moduleAbConfigs when it's not null
     */
    @Test
    public void testGetModuleAbConfigs_WithCardStyleAbV2Config() throws Throwable {
        // arrange
        ModuleAbConfig cardStyleAbV2Config = new ModuleAbConfig();
        cardStyleAbV2Config.setKey("CardStyleV2Config");
        when(ctx.getCardStyleAbV2Config()).thenReturn(cardStyleAbV2Config);
        // act
        List<ModuleAbConfig> result = moduleAbBuilderService.getModuleAbConfigs(ctx);
        // assert
        assertTrue(result.contains(cardStyleAbV2Config));
    }

    /**
     * Test adding comparePriceAbConfig to moduleAbConfigs when it's not null
     */
    @Test
    public void testGetModuleAbConfigs_WithComparePriceAbConfig() throws Throwable {
        // arrange
        ModuleAbConfig comparePriceAbConfig = new ModuleAbConfig();
        comparePriceAbConfig.setKey("ComparePriceConfig");
        when(douHuService.getAbResultForComparePriceAssistant(any(DealCtx.class))).thenReturn(comparePriceAbConfig);
        // act
        List<ModuleAbConfig> result = moduleAbBuilderService.getModuleAbConfigs(ctx);
        // assert
        assertTrue(result.contains(comparePriceAbConfig));
        verify(douHuService).getAbResultForComparePriceAssistant(ctx);
    }

    /**
     * Test adding glassGlassDealDetailAbConfig to moduleAbConfigs when it's not null
     */
    @Test
    public void testGetModuleAbConfigs_WithGlassGlassDealDetailAbConfig() throws Throwable {
        // arrange
        when(ctx.getCategoryId()).thenReturn(406);
        when(ctx.getCityId4P()).thenReturn(1);
        when(ctx.isMt()).thenReturn(true);
        ModuleAbConfig glassGlassDealDetailAbConfig = new ModuleAbConfig();
        glassGlassDealDetailAbConfig.setKey("MtGlassDealDetailExp");
        when(douHuBiz.getAbByCityIdAndUuidAndDpId(anyInt(), any(DealCtx.class), anyString())).thenReturn(glassGlassDealDetailAbConfig);
        // act
        List<ModuleAbConfig> result = moduleAbBuilderService.getModuleAbConfigs(ctx);
        // assert
        assertTrue(result.contains(glassGlassDealDetailAbConfig));
        verify(douHuBiz).getAbByCityIdAndUuidAndDpId(1, ctx, "MtGlassDealDetailExp");
    }

    /**
     * Test adding glassesExhibitAbConfig to moduleAbConfigs when it's not null
     */
    @Test
    public void testGetModuleAbConfigs_WithGlassesExhibitAbConfig() throws Throwable {
        // arrange
        ModuleAbConfig glassesExhibitAbConfig = new ModuleAbConfig();
        glassesExhibitAbConfig.setKey("GlassesExhibitConfig");
        when(ctx.getGlassesExhibitAbConfig()).thenReturn(glassesExhibitAbConfig);
        // act
        List<ModuleAbConfig> result = moduleAbBuilderService.getModuleAbConfigs(ctx);
        // assert
        assertTrue(result.contains(glassesExhibitAbConfig));
    }

    /**
     * Test adding expressOptimizeAbConfig to moduleAbConfigs when it's not null
     */
    @Test
    public void testGetModuleAbConfigs_WithExpressOptimizeAbConfig() throws Throwable {
        // arrange
        when(ctx.isMt()).thenReturn(true);
        ModuleAbConfig expressOptimizeAbConfig = new ModuleAbConfig();
        expressOptimizeAbConfig.setKey("MtExpressOptimizeExp");
        when(douHuBiz.getAbExpResult(any(DealCtx.class), anyString())).thenReturn(expressOptimizeAbConfig);
        // act
        List<ModuleAbConfig> result = moduleAbBuilderService.getModuleAbConfigs(ctx);
        // assert
        assertTrue(result.contains(expressOptimizeAbConfig));
        verify(douHuBiz).getAbExpResult(ctx, "MtExpressOptimizeExp");
    }

    /**
     * Test adding nativeDealDetailAbConfig to moduleAbConfigs when it's not null
     */
    @Test
    public void testGetModuleAbConfigs_WithNativeDealDetailAbConfig() throws Throwable {
        // arrange
        ModuleAbConfig nativeDealDetailAbConfig = new ModuleAbConfig();
        nativeDealDetailAbConfig.setKey("NativeDealDetailConfig");
        when(douHuService.getNativeDealDetailAbTestResult(any(EnvCtx.class))).thenReturn(nativeDealDetailAbConfig);
        // act
        List<ModuleAbConfig> result = moduleAbBuilderService.getModuleAbConfigs(ctx);
        // assert
        assertTrue(result.contains(nativeDealDetailAbConfig));
        verify(douHuService).getNativeDealDetailAbTestResult(envCtx);
    }

    /**
     * Test adding repairPayAbConfig to moduleAbConfigs when it's not null
     */
    @Test
    public void testGetModuleAbConfigs_WithRepairPayAbConfig() throws Throwable {
        // arrange
        ModuleAbConfig repairPayAbConfig = new ModuleAbConfig();
        repairPayAbConfig.setKey("RepairPayConfig");
        when(douHuService.getRepairPayAbTestResult(any(EnvCtx.class))).thenReturn(repairPayAbConfig);
        // act
        List<ModuleAbConfig> result = moduleAbBuilderService.getModuleAbConfigs(ctx);
        // assert
        assertTrue(result.contains(repairPayAbConfig));
        verify(douHuService).getRepairPayAbTestResult(envCtx);
    }

    /**
     * Test adding magicCouponEnhancementAbConfig to moduleAbConfigs when conditions are met
     */
    @Test
    public void testGetModuleAbConfigs_WithMagicCouponEnhancementAbConfig() throws Throwable {
        // arrange
        when(ctx.isHasSuperCouponScene()).thenReturn(true);
        ModuleAbConfig magicCouponEnhancementAbConfig = new ModuleAbConfig();
        magicCouponEnhancementAbConfig.setKey("MagicCouponEnhancementConfig");
        when(douHuService.getMagicCouponEnhancementAbTestResult(any(EnvCtx.class))).thenReturn(magicCouponEnhancementAbConfig);
        // act
        List<ModuleAbConfig> result = moduleAbBuilderService.getModuleAbConfigs(ctx);
        // assert
        assertTrue(result.contains(magicCouponEnhancementAbConfig));
        verify(douHuService).getMagicCouponEnhancementAbTestResult(envCtx);
    }

    /**
     * Test not adding magicCouponEnhancementAbConfig when hasSuperCouponScene is false
     */
    @Test
    public void testGetModuleAbConfigs_WithMagicCouponEnhancementAbConfig_WhenHasSuperCouponSceneIsFalse() throws Throwable {
        // arrange
        when(ctx.isHasSuperCouponScene()).thenReturn(false);
        ModuleAbConfig magicCouponEnhancementAbConfig = new ModuleAbConfig();
        magicCouponEnhancementAbConfig.setKey("MagicCouponEnhancementConfig");
        when(douHuService.getMagicCouponEnhancementAbTestResult(any(EnvCtx.class))).thenReturn(magicCouponEnhancementAbConfig);
        // act
        List<ModuleAbConfig> result = moduleAbBuilderService.getModuleAbConfigs(ctx);
        // assert
        assertTrue(!result.contains(magicCouponEnhancementAbConfig));
        verify(douHuService).getMagicCouponEnhancementAbTestResult(envCtx);
    }
}
