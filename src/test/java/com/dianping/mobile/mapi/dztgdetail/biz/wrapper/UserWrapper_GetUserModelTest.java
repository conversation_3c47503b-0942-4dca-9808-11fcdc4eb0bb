package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.tuangou.dztg.bjwrapper.api.UserWrapperService;
import com.dianping.tuangou.dztg.bjwrapper.api.dto.MtUserDto;
import com.dianping.tuangou.dztg.bjwrapper.api.enums.UserFieldsTypeEnum;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class UserWrapper_GetUserModelTest {

    @InjectMocks
    private UserWrapper userWrapper;

    @Mock
    private UserWrapperService userWrapperService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 userIdSet 为空的情况
     */
    @Test
    public void testGetUserModelMapWithEmptyUserIdSet() {
        // arrange
        Set<Long> userIdSet = Collections.emptySet();
        // act
        Map<Long, MtUserDto> result = userWrapper.getUserModelMap(userIdSet);
        // assert
        assertNull(result);
    }

    /**
     * 测试 userIdSet 不为空，且 userWrapperService.getUserModelMap 方法正常返回的情况
     */
    @Test
    public void testGetUserModelMapWithNonEmptyUserIdSetAndNormalReturn() throws Exception {
        // arrange
        Set<Long> userIdSet = Collections.singleton(1L);
        Map<Long, MtUserDto> expected = new HashMap<>();
        expected.put(1L, new MtUserDto());
        when(userWrapperService.getUserModelMap(userIdSet, UserFieldsTypeEnum.SAMPLE_INFO.getUserFieldsType())).thenReturn(expected);
        // act
        Map<Long, MtUserDto> result = userWrapper.getUserModelMap(userIdSet);
        // assert
        assertTrue(result.equals(expected));
    }

    /**
     * 测试 userIdSet 不为空，且 userWrapperService.getUserModelMap 方法抛出异常的情况
     */
    @Test
    public void testGetUserModelMapWithNonEmptyUserIdSetAndException() throws Exception {
        // arrange
        Set<Long> userIdSet = Collections.singleton(1L);
        when(userWrapperService.getUserModelMap(userIdSet, UserFieldsTypeEnum.SAMPLE_INFO.getUserFieldsType())).thenThrow(new RuntimeException());
        // act
        Map<Long, MtUserDto> result = userWrapper.getUserModelMap(userIdSet);
        // assert
        assertNull(result);
    }
}
