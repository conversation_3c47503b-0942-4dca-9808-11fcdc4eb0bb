package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.feature.model.FeatureDetailDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiClientWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ReserveProductWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.Guarantee;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.GuaranteeBuilderService;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.OralDealUtils;
import com.google.common.collect.Lists;
import com.sankuai.clr.content.process.gateway.thrift.dto.book.BookStatusQueryGatewayRespDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import com.sankuai.mpmctmvacommon.resource.response.CommonRespDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.BestPriceGuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.PriceProtectionTagDTO;
import com.sankuai.trade.general.reserve.response.ReserveResponse;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.APP_KEY;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.PRICE_PROTECTION_SHOW_SWITCH;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * @Author: <EMAIL>
 * @Date: 2024/5/20
 */
@RunWith(MockitoJUnitRunner.class)
public class GuaranteeBuilderServiceTest {
    @InjectMocks
    private GuaranteeBuilderService guaranteeBuilderService;
    @Mock
    private ReserveProductWrapper reserveProductWrapper;
    @Mock
    private DealGroupWrapper dealGroupWrapper;
    @Mock
    private PoiClientWrapper poiClientWrapper;

    private MockedStatic<Lion> mockedStatic;

    @Before
    public void setUp() {
        mockedStatic = mockStatic(Lion.class);
    }

    @After
    public void teardown() {
        mockedStatic.close();
    }

    @Test
    public void testAssembleGuarantee() {
        Guarantee result = guaranteeBuilderService.assembleGuarantee("123");
        Assert.assertTrue(result.getText().equals("123"));
    }

    @Test
    public void testGetServiceTypeId() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
        dealGroupCategoryDTO.setServiceTypeId(123L);
        dealGroupDTO.setCategory(dealGroupCategoryDTO);
        Long result = guaranteeBuilderService.getServiceTypeId(dealGroupDTO);
        Assert.assertTrue(result == 123L);
    }

    @Test
    public void testCheckRefundByProduct() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(501);
        ctx.setChannelDTO(channelDTO);
        mockedStatic.when(() -> Lion.getList(LionConstants.APP_KEY,
                LionConstants.CUSTOM_REFUND_CATGORY_CONFIG, Integer.class, Collections.emptyList())).thenReturn(Lists.newArrayList(501));

        List<AttributeDTO> attributeDTOS = new ArrayList<>();
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("reservation_policy");
        attributeDTO.setValue(Lists.newArrayList(Lists.newArrayList("预约成功后不可退改")));
        attributeDTOS.add(attributeDTO);
        ctx.setAttrs(attributeDTOS);
        boolean result = guaranteeBuilderService.checkRefundByProduct(ctx);
        Assert.assertTrue(result);
    }

    @Test
    public void testGetReservationInfoV2_reserveOnline() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(501);
        ctx.setChannelDTO(channelDTO);

        List<AttributeDTO> attributeDTOS = new ArrayList<>();
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("reservation_is_needed_or_not_3");
        attributeDTO.setValue(Lists.newArrayList("是"));

        AttributeDTO attributeDTO2 = new AttributeDTO();
        attributeDTO2.setName("support_home_service");
        attributeDTO2.setValue(Lists.newArrayList("是"));

        AttributeDTO attributeDTO3 = new AttributeDTO();
        attributeDTO3.setName("support_shop_service");
        attributeDTO3.setValue(Lists.newArrayList("是"));

        attributeDTOS.addAll(Lists.newArrayList(attributeDTO,attributeDTO2,attributeDTO3));
        ctx.setAttrs(attributeDTOS);

        mockedStatic.when(() -> Lion.getList(LionConstants.AVAILABLE_CATEGORY_IDS, Integer.class, Lists.newArrayList())).thenReturn(Lists.newArrayList(501));

        ReserveResponse<Boolean> reserveResponse = new ReserveResponse<>();
        reserveResponse.setSuccess(true);
        reserveResponse.setResult(true);
        Mockito.when(reserveProductWrapper.getFutureResult(ctx)).thenReturn(reserveResponse);
        List<Guarantee> guarantees = guaranteeBuilderService.getReservationInfoV2(ctx);
        Assert.assertTrue(guarantees.get(0).getText().equals("在线预约"));
    }

    @Test
    public void testGetReservationInfoV2_supportHome() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(501);
        ctx.setChannelDTO(channelDTO);

        List<AttributeDTO> attributeDTOS = new ArrayList<>();
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("reservation_is_needed_or_not_3");
        attributeDTO.setValue(Lists.newArrayList("是"));

        AttributeDTO attributeDTO2 = new AttributeDTO();
        attributeDTO2.setName("support_home_service");
        attributeDTO2.setValue(Lists.newArrayList("是"));

        AttributeDTO attributeDTO3 = new AttributeDTO();
        attributeDTO3.setName("support_shop_service");
        attributeDTO3.setValue(Lists.newArrayList("是"));

        attributeDTOS.addAll(Lists.newArrayList(attributeDTO,attributeDTO2,attributeDTO3));
        ctx.setAttrs(attributeDTOS);

        mockedStatic.when(() -> Lion.getList(LionConstants.AVAILABLE_CATEGORY_IDS, Integer.class, Lists.newArrayList())).thenReturn(Lists.newArrayList(501));

        ReserveResponse<Boolean> reserveResponse = new ReserveResponse<>();
        reserveResponse.setSuccess(true);
        reserveResponse.setResult(false);
        Mockito.when(reserveProductWrapper.getFutureResult(ctx)).thenReturn(reserveResponse);
        List<Guarantee> guarantees = guaranteeBuilderService.getReservationInfoV2(ctx);
        Assert.assertTrue(guarantees.get(0).getText().equals("预约上门"));
    }

    @Test
    public void testGetReservationInfoV2_supportShopAndNeedReservation() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(501);
        ctx.setChannelDTO(channelDTO);

        List<AttributeDTO> attributeDTOS = new ArrayList<>();
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("reservation_is_needed_or_not_3");
        attributeDTO.setValue(Lists.newArrayList("是"));

        AttributeDTO attributeDTO2 = new AttributeDTO();
        attributeDTO2.setName("support_home_service");
        attributeDTO2.setValue(Lists.newArrayList("否"));

        AttributeDTO attributeDTO3 = new AttributeDTO();
        attributeDTO3.setName("support_shop_service");
        attributeDTO3.setValue(Lists.newArrayList("是"));

        attributeDTOS.addAll(Lists.newArrayList(attributeDTO,attributeDTO2,attributeDTO3));
        ctx.setAttrs(attributeDTOS);

        mockedStatic.when(() -> Lion.getList(LionConstants.AVAILABLE_CATEGORY_IDS, Integer.class, Lists.newArrayList())).thenReturn(Lists.newArrayList(501));

        ReserveResponse<Boolean> reserveResponse = new ReserveResponse<>();
        reserveResponse.setSuccess(true);
        reserveResponse.setResult(false);
        Mockito.when(reserveProductWrapper.getFutureResult(ctx)).thenReturn(reserveResponse);
        List<Guarantee> guarantees = guaranteeBuilderService.getReservationInfoV2(ctx);
        Assert.assertTrue(guarantees.get(0).getText().equals("预约到店"));
    }

    @Test
    public void testGetReservationInfoV2_NeedReservation() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(501);
        ctx.setChannelDTO(channelDTO);

        List<AttributeDTO> attributeDTOS = new ArrayList<>();
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("reservation_is_needed_or_not_3");
        attributeDTO.setValue(Lists.newArrayList("是"));

        AttributeDTO attributeDTO2 = new AttributeDTO();
        attributeDTO2.setName("support_home_service");
        attributeDTO2.setValue(Lists.newArrayList("否"));

        AttributeDTO attributeDTO3 = new AttributeDTO();
        attributeDTO3.setName("support_shop_service");
        attributeDTO3.setValue(Lists.newArrayList("否"));

        attributeDTOS.addAll(Lists.newArrayList(attributeDTO,attributeDTO2,attributeDTO3));
        ctx.setAttrs(attributeDTOS);

        mockedStatic.when(() -> Lion.getList(LionConstants.AVAILABLE_CATEGORY_IDS, Integer.class, Lists.newArrayList())).thenReturn(Lists.newArrayList(501));

        ReserveResponse<Boolean> reserveResponse = new ReserveResponse<>();
        reserveResponse.setSuccess(true);
        reserveResponse.setResult(false);
        Mockito.when(reserveProductWrapper.getFutureResult(ctx)).thenReturn(reserveResponse);
        List<Guarantee> guarantees = guaranteeBuilderService.getReservationInfoV2(ctx);
        Assert.assertTrue(guarantees.get(0).getText().equals("需预约"));
    }

    @Test
    public void testGetApplicableTimeDesc() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(501);
        ctx.setChannelDTO(channelDTO);

        List<AttributeDTO> attributeDTOS = new ArrayList<>();
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("calc_holiday_available");
        attributeDTO.setValue(Lists.newArrayList("1006", "1007"));
        attributeDTOS.add(attributeDTO);
        ctx.setAttrs(attributeDTOS);

        String result = guaranteeBuilderService.getApplicableTimeDesc(ctx);
        Assert.assertTrue("仅工作日可用".equals(result));
    }

    @Test
    public void testGetApplicablePeopleDesc() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(501);
        ctx.setChannelDTO(channelDTO);

        List<AttributeDTO> attributeDTOS = new ArrayList<>();
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("tooth_suit_people");
        attributeDTO.setValue(Lists.newArrayList("成人", "儿童"));
        attributeDTOS.add(attributeDTO);
        ctx.setAttrs(attributeDTOS);

        String result = guaranteeBuilderService.getApplicablePeopleDesc(ctx);
        Assert.assertTrue("成人/儿童通用".equals(result));

        attributeDTOS = new ArrayList<>();
        AttributeDTO attributeDTO2 = new AttributeDTO();
        attributeDTO2.setName("tooth_suit_people");
        attributeDTO2.setValue(Lists.newArrayList("成人"));
        attributeDTOS.add(attributeDTO2);
        ctx.setAttrs(attributeDTOS);
        String result2 = guaranteeBuilderService.getApplicablePeopleDesc(ctx);
        Assert.assertTrue("限成人".equals(result2));

        attributeDTOS = new ArrayList<>();
        AttributeDTO attributeDTO3 = new AttributeDTO();
        attributeDTO3.setName("tooth_suit_people");
        attributeDTO3.setValue(Lists.newArrayList("儿童"));
        attributeDTOS.add(attributeDTO3);
        ctx.setAttrs(attributeDTOS);
        String result3 = guaranteeBuilderService.getApplicablePeopleDesc(ctx);
        Assert.assertTrue("限儿童".equals(result3));
    }

    @Test
    public void testPhysicalExamReserveOnline() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setMtLongShopId(123L);

        mockedStatic.when(() -> Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb.physical.exam.third.party.shops", String.class, new ArrayList<>())).thenReturn(Lists.newArrayList("mt123"));
        when(dealGroupWrapper.isThirdPartyDealGroup(any())).thenReturn(true);
        boolean result = guaranteeBuilderService.physicalExamReserveOnline(ctx);
        Assert.assertTrue(result);
    }

    @Test
    public void testParentChildFunReserveOnline() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(1002);
        ctx.setChannelDTO(channelDTO);
        ctx.setDpId(123);

        BookStatusQueryGatewayRespDTO respDTO = new BookStatusQueryGatewayRespDTO();
        respDTO.setBookable(true);
        CommonRespDTO commonRespDTO = new CommonRespDTO();
        commonRespDTO.setCode(200);
        respDTO.setCommonResp(commonRespDTO);
        when(poiClientWrapper.getFutureResult(any(), Mockito.anyString(), Mockito.anyString())).thenReturn(respDTO);
        boolean result = guaranteeBuilderService.parentChildFunReserveOnline(ctx);
        Assert.assertTrue(result);
    }

    @Test
    public void testPhotoReserveOnline() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(501);
        ctx.setChannelDTO(channelDTO);

        mockedStatic.when(() -> Lion.getList(LionConstants.APP_KEY,
                LionConstants.SNAPSHOTPHOTO_CATEGORYIDS, Integer.class, Collections.emptyList())).thenReturn(Lists.newArrayList(501));

        ctx.setShowReserveEntrance(true);
        boolean result = guaranteeBuilderService.photoReserveOnline(ctx);
        Assert.assertTrue(result);
    }

    @Test
    public void testRemoveTagsV2() {
        List<Guarantee> guarantees = Lists.newArrayList();
        Guarantee guarantee = Guarantee.builder().text("text").build();
        Guarantee guarantee1 = Guarantee.builder().text("需预约").build();
        guarantees.add(guarantee);
        guarantees.add(guarantee1);
        guaranteeBuilderService.removeTagsV2(guarantees);
        Assert.assertTrue(guarantees.size() == 1);
    }

    @Test
    public void testReserveAfterPurchase() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(501);
        ctx.setChannelDTO(channelDTO);

        mockedStatic.when(() -> Lion.getList(LionConstants.AVAILABLE_CATEGORY_IDS, Integer.class, Lists.newArrayList())).thenReturn(Lists.newArrayList(501));

        boolean result = guaranteeBuilderService.reserveAfterPurchase(501);
        Assert.assertTrue(result);
    }

    @Test
    public void testReserveOnline() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        ReserveResponse<Boolean> reserveResponse = new ReserveResponse<>();
        reserveResponse.setSuccess(true);
        reserveResponse.setResult(true);
        when(reserveProductWrapper.getFutureResult(ctx)).thenReturn(reserveResponse);

        boolean result = guaranteeBuilderService.reserveOnline(ctx);
        Assert.assertTrue(result);
    }

    @Test
    public void testForceReserve() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(501);
        ctx.setChannelDTO(channelDTO);

        mockedStatic.when(() -> Lion.getList(LionConstants.AVAILABLE_CATEGORY_IDS, Integer.class, Lists.newArrayList())).thenReturn(Lists.newArrayList(501));

        boolean result = guaranteeBuilderService.reserveAfterPurchase(501);
        Assert.assertTrue(result);
    }

    @Test
    public void testGetFeatures() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(501);
        ctx.setChannelDTO(channelDTO);
        ctx.setDealGroupDTO(new DealGroupDTO());

        // 买贵必赔
        ObjectGuaranteeTagDTO bestPriceGuaranteeInfo = new ObjectGuaranteeTagDTO();
        BestPriceGuaranteeTagDTO bestPriceGuaranteeTagDTO = new BestPriceGuaranteeTagDTO();
        bestPriceGuaranteeTagDTO.setValid(true);
        bestPriceGuaranteeInfo.setBestPriceGuaranteeTagDTO(bestPriceGuaranteeTagDTO);
        ctx.setBestPriceGuaranteeInfo(bestPriceGuaranteeInfo);

        // 价保
        mockedStatic.when(() -> Lion.getList(LionConstants.APP_KEY, LionConstants.PRICE_PROTECTION_CATEGORY_IDS, Integer.class, new ArrayList<>())).thenReturn(Lists.newArrayList(501));
        mockedStatic.when(() -> Lion.getBoolean(APP_KEY, PRICE_PROTECTION_SHOW_SWITCH, true)).thenReturn(true);
        ObjectGuaranteeTagDTO objectGuaranteeTagDTO = new ObjectGuaranteeTagDTO();
        PriceProtectionTagDTO priceProtectionTagDTO = new PriceProtectionTagDTO();
        priceProtectionTagDTO.setValid(true);
        priceProtectionTagDTO.setValidityDays(15);
        objectGuaranteeTagDTO.setPriceProtectionTag(priceProtectionTagDTO);
        ctx.setPriceProtectionInfo(objectGuaranteeTagDTO);

        mockedStatic.when(() -> Lion.getList(LionConstants.APP_KEY,
                LionConstants.CUSTOM_REFUND_CATGORY_CONFIG, Integer.class, Collections.emptyList())).thenReturn(Lists.newArrayList(501));
        List<AttributeDTO> attributeDTOS = new ArrayList<>();
        AttributeDTO attributeDTO1 = new AttributeDTO();
        attributeDTO1.setName("reservation_policy");
        attributeDTO1.setValue(Lists.newArrayList("预约成功后不可退改"));

        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("reservation_is_needed_or_not_3");
        attributeDTO.setValue(Lists.newArrayList("是"));
        attributeDTOS.add(attributeDTO1);
        attributeDTOS.add(attributeDTO);
        ctx.setAttrs(attributeDTOS);
        DealGroupBaseDTO dealGroupBase = new DealGroupBaseDTO();
        dealGroupBase.setOverdueAutoRefund(true);
        ctx.setDealGroupBase(dealGroupBase);

        ctx.setEnableCardStyleV2(true);

        // 履约标签
        FeatureDetailDTO featureDetailDTO = new FeatureDetailDTO();
        featureDetailDTO.setId(1L);
        featureDetailDTO.setText("不满意重做");
        List<FeatureDetailDTO> featureDetailDTOS = Lists.newArrayList(featureDetailDTO);
        ctx.setShopTagFeatures(featureDetailDTOS);

        List<String> result = guaranteeBuilderService.getFeatures(ctx, false);
        Assert.assertTrue(CollectionUtils.isNotEmpty(result));
        Assert.assertTrue(result.contains("不满意重做"));
    }

    @Test
    public void testGetReservationInfo_reserveOnline() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(501);
        ctx.setChannelDTO(channelDTO);

        List<AttributeDTO> attributeDTOS = new ArrayList<>();
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("reservation_is_needed_or_not_3");
        attributeDTO.setValue(Lists.newArrayList("是"));

        AttributeDTO attributeDTO2 = new AttributeDTO();
        attributeDTO2.setName("support_home_service");
        attributeDTO2.setValue(Lists.newArrayList("是"));

        AttributeDTO attributeDTO3 = new AttributeDTO();
        attributeDTO3.setName("support_shop_service");
        attributeDTO3.setValue(Lists.newArrayList("是"));

        attributeDTOS.addAll(Lists.newArrayList(attributeDTO,attributeDTO2,attributeDTO3));
        ctx.setAttrs(attributeDTOS);

        mockedStatic.when(() -> Lion.getList(LionConstants.AVAILABLE_CATEGORY_IDS, Integer.class, Lists.newArrayList())).thenReturn(Lists.newArrayList(501));

        ReserveResponse<Boolean> reserveResponse = new ReserveResponse<>();
        reserveResponse.setSuccess(true);
        reserveResponse.setResult(true);
        Mockito.when(reserveProductWrapper.getFutureResult(ctx)).thenReturn(reserveResponse);
        List<String> result = guaranteeBuilderService.getReservationInfo(ctx);
        Assert.assertTrue(result.contains("在线预约"));
    }

    @Test
    public void testGetReservationInfo_supportHome() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(501);
        ctx.setChannelDTO(channelDTO);

        List<AttributeDTO> attributeDTOS = new ArrayList<>();
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("reservation_is_needed_or_not_3");
        attributeDTO.setValue(Lists.newArrayList("是"));

        AttributeDTO attributeDTO2 = new AttributeDTO();
        attributeDTO2.setName("support_home_service");
        attributeDTO2.setValue(Lists.newArrayList("是"));

        AttributeDTO attributeDTO3 = new AttributeDTO();
        attributeDTO3.setName("support_shop_service");
        attributeDTO3.setValue(Lists.newArrayList("是"));

        attributeDTOS.addAll(Lists.newArrayList(attributeDTO,attributeDTO2,attributeDTO3));
        ctx.setAttrs(attributeDTOS);

        mockedStatic.when(() -> Lion.getList(LionConstants.AVAILABLE_CATEGORY_IDS, Integer.class, Lists.newArrayList())).thenReturn(Lists.newArrayList(501));

        ReserveResponse<Boolean> reserveResponse = new ReserveResponse<>();
        reserveResponse.setSuccess(true);
        reserveResponse.setResult(false);
        Mockito.when(reserveProductWrapper.getFutureResult(ctx)).thenReturn(reserveResponse);
        List<String> result = guaranteeBuilderService.getReservationInfo(ctx);
        Assert.assertTrue(result.contains("预约上门"));
    }

    @Test
    public void testGetReservationInfo_supportShopAndNeedReservation() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(501);
        ctx.setChannelDTO(channelDTO);

        List<AttributeDTO> attributeDTOS = new ArrayList<>();
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("reservation_is_needed_or_not_3");
        attributeDTO.setValue(Lists.newArrayList("是"));

        AttributeDTO attributeDTO2 = new AttributeDTO();
        attributeDTO2.setName("support_home_service");
        attributeDTO2.setValue(Lists.newArrayList("否"));

        AttributeDTO attributeDTO3 = new AttributeDTO();
        attributeDTO3.setName("support_shop_service");
        attributeDTO3.setValue(Lists.newArrayList("是"));

        attributeDTOS.addAll(Lists.newArrayList(attributeDTO,attributeDTO2,attributeDTO3));
        ctx.setAttrs(attributeDTOS);

        mockedStatic.when(() -> Lion.getList(LionConstants.AVAILABLE_CATEGORY_IDS, Integer.class, Lists.newArrayList())).thenReturn(Lists.newArrayList(501));

        ReserveResponse<Boolean> reserveResponse = new ReserveResponse<>();
        reserveResponse.setSuccess(true);
        reserveResponse.setResult(false);
        Mockito.when(reserveProductWrapper.getFutureResult(ctx)).thenReturn(reserveResponse);
        List<String> result = guaranteeBuilderService.getReservationInfo(ctx);
        Assert.assertTrue(result.contains("预约到店"));
    }

    @Test
    public void testGetReservationInfo_NeedReservation() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(501);
        ctx.setChannelDTO(channelDTO);

        List<AttributeDTO> attributeDTOS = new ArrayList<>();
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("reservation_is_needed_or_not_3");
        attributeDTO.setValue(Lists.newArrayList("是"));

        AttributeDTO attributeDTO2 = new AttributeDTO();
        attributeDTO2.setName("support_home_service");
        attributeDTO2.setValue(Lists.newArrayList("否"));

        AttributeDTO attributeDTO3 = new AttributeDTO();
        attributeDTO3.setName("support_shop_service");
        attributeDTO3.setValue(Lists.newArrayList("否"));

        attributeDTOS.addAll(Lists.newArrayList(attributeDTO,attributeDTO2,attributeDTO3));
        ctx.setAttrs(attributeDTOS);

        mockedStatic.when(() -> Lion.getList(LionConstants.AVAILABLE_CATEGORY_IDS, Integer.class, Lists.newArrayList())).thenReturn(Lists.newArrayList(501));

        ReserveResponse<Boolean> reserveResponse = new ReserveResponse<>();
        reserveResponse.setSuccess(true);
        reserveResponse.setResult(false);
        Mockito.when(reserveProductWrapper.getFutureResult(ctx)).thenReturn(reserveResponse);
        List<String> result = guaranteeBuilderService.getReservationInfo(ctx);
        Assert.assertTrue(result.contains("需预约"));
    }

    @Test
    public void testRemoveTags() {
        List<String> tags = Lists.newArrayList();
        tags.add("text");
        tags.add("需预约");
        guaranteeBuilderService.removeTags(tags);
        Assert.assertTrue(tags.size() == 1);
    }

    @Test
    public void testGetSpecialFeatures() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(506);
        ctx.setChannelDTO(channelDTO);

        List<AttributeDTO> attributeDTOS = new ArrayList<>();
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("oral_dentistry_tuanxiang_rule");
        attributeDTO.setValue(Lists.newArrayList("oral_dentistry_tuanxiang_rule"));
        attributeDTOS.add(attributeDTO);
        ctx.setAttrs(attributeDTOS);
        Map<String, String> tagMap = new HashMap<String, String>() {
            {
                put("oral_dentistry_tuanxiang_rule", "true");
            }
        };
        mockedStatic.when(() -> Lion.getMap(LionConstants.SPECIAL_TAG_CONFIG)).thenReturn(tagMap);

        List<String> result = guaranteeBuilderService.getSpecialFeatures(ctx);
        Assert.assertTrue(CollectionUtils.isNotEmpty(result));
    }

    @Test
    public void testSafeTreatOral_safe_denture1() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);

        //mock categoryId
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(506);
        ctx.setChannelDTO(channelDTO);

        //mock shopId
        Long shopId = 1L;
        ctx.setMtLongShopId(shopId);
        ctx.setDpLongShopId(shopId);

        //mock dpShopId2TagsMap
        Map<Long, List<DisplayTagDto>> dpShopId2TagsMap = new HashMap<>();
        DisplayTagDto displayTagDto = new DisplayTagDto();
        displayTagDto.setTagId(7441L);
        displayTagDto.setIcon("https://123");
        displayTagDto.setText("补牙");
        dpShopId2TagsMap.put(shopId,Lists.newArrayList(displayTagDto));
        ctx.setDpShopId2TagsMap(dpShopId2TagsMap);

        //mock GuaranteeTag
        MockedStatic<OralDealUtils> oralDealUtilsMockedStatic = mockStatic(OralDealUtils.class);
        when(OralDealUtils.isSafeDenture(ctx)).thenReturn(true);

        //mock AutoRefundSwitch isOverdueAutoRefund
        DealGroupBaseDTO dealGroupBaseDTO = new DealGroupBaseDTO();
        dealGroupBaseDTO.setAutoRefundSwitch(2);
        dealGroupBaseDTO.setOverdueAutoRefund(true);
        ctx.setDealGroupBase(dealGroupBaseDTO);
        ctx.setAnXinExercise(true);

        List<Guarantee> result = guaranteeBuilderService.getGuarantee(ctx,false);
        oralDealUtilsMockedStatic.close();

        Assert.assertTrue(CollectionUtils.isNotEmpty(result));
    }

    @Test
    public void testSafeTreatOral_safe_denture() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);

        //mock categoryId
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(506);
        ctx.setChannelDTO(channelDTO);

        //mock shopId
        Long shopId = 1L;
        ctx.setMtLongShopId(shopId);
        ctx.setDpLongShopId(shopId);

        //mock dpShopId2TagsMap
        Map<Long, List<DisplayTagDto>> dpShopId2TagsMap = new HashMap<>();
        DisplayTagDto displayTagDto = new DisplayTagDto();
        displayTagDto.setTagId(7441L);
        displayTagDto.setIcon("https://123");
        displayTagDto.setText("补牙");
        dpShopId2TagsMap.put(shopId,Lists.newArrayList(displayTagDto));
        ctx.setDpShopId2TagsMap(dpShopId2TagsMap);

        //mock GuaranteeTag
        MockedStatic<OralDealUtils> oralDealUtilsMockedStatic = mockStatic(OralDealUtils.class);
        when(OralDealUtils.isSafeDenture(ctx)).thenReturn(true);

        //mock AutoRefundSwitch isOverdueAutoRefund
        DealGroupBaseDTO dealGroupBaseDTO = new DealGroupBaseDTO();
        dealGroupBaseDTO.setAutoRefundSwitch(2);
        dealGroupBaseDTO.setOverdueAutoRefund(true);
        ctx.setDealGroupBase(dealGroupBaseDTO);
        ctx.setAnXinExercise(true);

        List<Guarantee> result = guaranteeBuilderService.getGuarantee(ctx,false);
        oralDealUtilsMockedStatic.close();

        Assert.assertTrue(CollectionUtils.isNotEmpty(result));
    }

    @Test
    public void testSafeTreatOral_safe_implant() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);

        //mock categoryId
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(506);
        ctx.setChannelDTO(channelDTO);

        //mock shopId
        Long shopId = 1L;
        ctx.setMtLongShopId(shopId);
        ctx.setDpLongShopId(shopId);

        //mock dpShopId2TagsMap
        Map<Long, List<DisplayTagDto>> dpShopId2TagsMap = new HashMap<>();
        DisplayTagDto displayTagDto = new DisplayTagDto();
        displayTagDto.setTagId(7440L);
        displayTagDto.setIcon("https://123");
        displayTagDto.setText("种植");
        dpShopId2TagsMap.put(shopId,Lists.newArrayList(displayTagDto));
        ctx.setDpShopId2TagsMap(dpShopId2TagsMap);

        //mock ProductTagId
        MockedStatic<OralDealUtils> oralDealUtilsMockedStatic = mockStatic(OralDealUtils.class);
        when(OralDealUtils.isSafeImplant(ctx)).thenReturn(true);


        //mock AutoRefundSwitch isOverdueAutoRefund
        DealGroupBaseDTO dealGroupBaseDTO = new DealGroupBaseDTO();
        dealGroupBaseDTO.setAutoRefundSwitch(2);
        dealGroupBaseDTO.setOverdueAutoRefund(true);
        ctx.setDealGroupBase(dealGroupBaseDTO);

        List<Guarantee> result = guaranteeBuilderService.getGuarantee(ctx,false);

        oralDealUtilsMockedStatic.close();

        Assert.assertTrue(CollectionUtils.isNotEmpty(result));
    }


}
