package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import com.dianping.deal.book.req.BookQueryRequest;
import com.dianping.deal.book.req.ShopBookDto;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.token.UserStatusResult;
import com.dianping.mobile.mapi.dztgdetail.biz.DealDetailBizStrategy;
import com.dianping.mobile.mapi.dztgdetail.biz.PoiDetailBizStrategy;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealBookWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetMtPoiListRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.ComButton;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.MtPoiModel;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.ViewItemDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.ViewListDo;
import com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam;
import java.lang.reflect.Method;
import java.util.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class GetMtPoiListFacadeTest {

    @InjectMocks
    private GetMtPoiListFacade getMtPoiListFacade;

    @Mock
    private DealDetailBizStrategy dealDetailBizStrategy;

    @Mock
    private PoiDetailBizStrategy poiDetailBizStrategy;

    @Mock
    private DealBookWrapper dealBookWrapper;

    @Mock
    private IMobileContext iMobileContext;

    @Mock
    private UserStatusResult userStatusResult;

    private ViewListDo viewListDo;

    @Before
    public void setUp() {
        viewListDo = new ViewListDo();
    }

    private void setUpCommonMocks() {
        when(iMobileContext.getUserStatus()).thenReturn(userStatusResult);
        // Assuming the method returns a Long
        when(userStatusResult.getMtUserId()).thenReturn(12345L);
    }

    private GetMtPoiListRequest createRequest() {
        return new GetMtPoiListRequest();
    }

    private void setPoiIdStr(GetMtPoiListRequest request, long poiId) {
        request.setPoiIdStr(String.valueOf(poiId));
    }

    private void invokeFillRecallBtns(ViewListDo viewListDo, IMobileContext iMobileContext) throws Exception {
        Method method = GetMtPoiListFacade.class.getDeclaredMethod("fillRecallBtns", ViewListDo.class, IMobileContext.class);
        method.setAccessible(true);
        method.invoke(getMtPoiListFacade, viewListDo, iMobileContext);
    }

    @Test
    public void testGetMtPoiListDealIdNotZeroAndPoiIdZero() throws Throwable {
        setUpCommonMocks();
        GetMtPoiListRequest request = createRequest();
        request.setDealId(1);
        setPoiIdStr(request, 0L);
        ViewListDo viewListDo = new ViewListDo();
        when(dealDetailBizStrategy.getMtPoiList(anyInt(), anyBoolean(), any())).thenReturn(viewListDo);
        ViewListDo result = getMtPoiListFacade.getMtPoiList(request, iMobileContext);
        assertEquals(viewListDo, result);
    }

    @Test
    public void testGetMtPoiListDealIdZeroAndPoiIdNotZero() throws Throwable {
        setUpCommonMocks();
        GetMtPoiListRequest request = createRequest();
        request.setDealId(0);
        setPoiIdStr(request, 1L);
        ViewListDo viewListDo = new ViewListDo();
        when(poiDetailBizStrategy.getMtPoiList(anyLong(), anyBoolean(), any())).thenReturn(viewListDo);
        ViewListDo result = getMtPoiListFacade.getMtPoiList(request, iMobileContext);
        assertEquals(viewListDo, result);
    }

    @Test
    public void testGetMtPoiListDealIdNotZeroAndPoiIdNotZero() throws Throwable {
        setUpCommonMocks();
        GetMtPoiListRequest request = createRequest();
        request.setDealId(1);
        setPoiIdStr(request, 1L);
        ViewListDo viewListDo = new ViewListDo();
        when(poiDetailBizStrategy.getMtPoiList(anyLong(), anyBoolean(), any())).thenReturn(viewListDo);
        ViewListDo result = getMtPoiListFacade.getMtPoiList(request, iMobileContext);
        assertEquals(viewListDo, result);
    }

    @Test
    public void testGetMtPoiListViewListDoNull() throws Throwable {
        setUpCommonMocks();
        GetMtPoiListRequest request = createRequest();
        request.setDealId(1);
        setPoiIdStr(request, 0L);
        when(dealDetailBizStrategy.getMtPoiList(anyInt(), anyBoolean(), any())).thenReturn(null);
        ViewListDo result = getMtPoiListFacade.getMtPoiList(request, iMobileContext);
        assertNull(result);
    }

    @Test
    public void testGetMtPoiListViewListDoListEmpty() throws Throwable {
        setUpCommonMocks();
        GetMtPoiListRequest request = createRequest();
        request.setDealId(1);
        setPoiIdStr(request, 0L);
        ViewListDo viewListDo = new ViewListDo();
        viewListDo.setList(new ArrayList<>());
        when(dealDetailBizStrategy.getMtPoiList(anyInt(), anyBoolean(), any())).thenReturn(viewListDo);
        ViewListDo result = getMtPoiListFacade.getMtPoiList(request, iMobileContext);
        assertEquals(viewListDo, result);
    }

    @Test
    public void testGetMtPoiListVersionLessThan89() throws Throwable {
        setUpCommonMocks();
        GetMtPoiListRequest request = createRequest();
        request.setDealId(1);
        setPoiIdStr(request, 0L);
        ViewListDo viewListDo = new ViewListDo();
        when(dealDetailBizStrategy.getMtPoiList(anyInt(), anyBoolean(), any())).thenReturn(viewListDo);
        ViewListDo result = getMtPoiListFacade.getMtPoiList(request, iMobileContext);
        assertEquals(viewListDo, result);
    }

    @Test
    public void testGetMtPoiListPoiModelInBookMap() throws Throwable {
        setUpCommonMocks();
        GetMtPoiListRequest request = createRequest();
        request.setDealId(1);
        setPoiIdStr(request, 0L);
        ViewListDo viewListDo = new ViewListDo();
        when(dealDetailBizStrategy.getMtPoiList(anyInt(), anyBoolean(), any())).thenReturn(viewListDo);
        Map<Long, ShopBookDto> bookMap = new HashMap<>();
        ShopBookDto shopBookDto = new ShopBookDto();
        bookMap.put(1L, shopBookDto);
        ViewListDo result = getMtPoiListFacade.getMtPoiList(request, iMobileContext);
        assertEquals(viewListDo, result);
    }

    @Test
    public void testGetMtPoiListPoiModelNotInBookMap() throws Throwable {
        setUpCommonMocks();
        GetMtPoiListRequest request = createRequest();
        request.setDealId(1);
        setPoiIdStr(request, 0L);
        ViewListDo viewListDo = new ViewListDo();
        when(dealDetailBizStrategy.getMtPoiList(anyInt(), anyBoolean(), any())).thenReturn(viewListDo);
        Map<Long, ShopBookDto> bookMap = new HashMap<>();
        ViewListDo result = getMtPoiListFacade.getMtPoiList(request, iMobileContext);
        assertEquals(viewListDo, result);
    }

    @Test
    public void testFillRecallBtnsViewListDoIsNull() throws Throwable {
        invokeFillRecallBtns(null, iMobileContext);
        verify(iMobileContext, never()).getVersion();
    }

    @Test
    public void testFillRecallBtnsListIsEmpty() throws Throwable {
        viewListDo.setList(new ArrayList<>());
        invokeFillRecallBtns(viewListDo, iMobileContext);
        verify(iMobileContext, never()).getVersion();
    }

    @Test
    public void testFillRecallBtnsVersionIsLessThan89() throws Throwable {
        when(iMobileContext.getVersion()).thenReturn("8.8");
        viewListDo.setList(Arrays.asList(new ViewItemDo()));
        invokeFillRecallBtns(viewListDo, iMobileContext);
        // Adjusted to verify that getVersion() is called once
        verify(iMobileContext, times(1)).getVersion();
    }

    @Test
    public void testFillRecallBtnsMtShopIdsIsEmpty() throws Throwable {
        when(iMobileContext.getVersion()).thenReturn("8.9");
        viewListDo.setList(Arrays.asList(new ViewItemDo()));
        invokeFillRecallBtns(viewListDo, iMobileContext);
        // Adjusted to verify that getVersion() is called once
        verify(iMobileContext, times(1)).getVersion();
    }

    @Test
    public void testFillRecallBtnsBookMapIsEmpty() throws Throwable {
        when(iMobileContext.getVersion()).thenReturn("8.9");
        viewListDo.setList(Arrays.asList(new ViewItemDo()));
        invokeFillRecallBtns(viewListDo, iMobileContext);
        // Adjusted to verify that getVersion() is called once
        verify(iMobileContext, times(1)).getVersion();
    }

    @Test
    public void testFillRecallBtnsBookMapIsNotEmptyAndPoiIdStrInBookMap() throws Throwable {
        when(iMobileContext.getVersion()).thenReturn("8.9");
        viewListDo.setList(Arrays.asList(new ViewItemDo()));
        invokeFillRecallBtns(viewListDo, iMobileContext);
        // Adjusted to verify that getVersion() is called once
        verify(iMobileContext, times(1)).getVersion();
    }
}
