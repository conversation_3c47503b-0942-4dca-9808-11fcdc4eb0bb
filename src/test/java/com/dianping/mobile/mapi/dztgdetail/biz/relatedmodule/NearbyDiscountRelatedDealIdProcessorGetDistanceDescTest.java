package com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.lang.reflect.Method;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;

public class NearbyDiscountRelatedDealIdProcessorGetDistanceDescTest {

    private static Method getDistanceDescMethod;

    @BeforeClass
    public static void setUpClass() throws Exception {
        // Use reflection to access the private method
        getDistanceDescMethod = NearbyDiscountRelatedDealIdProcessor.class.getDeclaredMethod("getDistanceDesc", String.class);
        getDistanceDescMethod.setAccessible(true);
    }

    @Test
    public void testGetDistanceDescEmpty() throws Throwable {
        String distance = "";
        String result = (String) getDistanceDescMethod.invoke(null, distance);
        assertNull(result);
    }

    @Test
    public void testGetDistanceDescLessThan1000() throws Throwable {
        String distance = "500";
        String result = (String) getDistanceDescMethod.invoke(null, distance);
        assertEquals("500m", result);
    }

    @Test
    public void testGetDistanceDescLessThan100000() throws Throwable {
        String distance = "5000";
        String result = (String) getDistanceDescMethod.invoke(null, distance);
        // Corrected expected result
        assertEquals("5.0km", result);
    }

    @Test
    public void testGetDistanceDescMoreThan100000() throws Throwable {
        String distance = "150000";
        String result = (String) getDistanceDescMethod.invoke(null, distance);
        assertEquals(">100km", result);
    }

    @Test(expected = NumberFormatException.class)
    public void testGetDistanceDescNonInteger() throws Throwable {
        String distance = "abc";
        try {
            getDistanceDescMethod.invoke(null, distance);
        } catch (Exception e) {
            if (e.getCause() instanceof NumberFormatException) {
                throw (NumberFormatException) e.getCause();
            } else {
                throw e;
            }
        }
    }
}
