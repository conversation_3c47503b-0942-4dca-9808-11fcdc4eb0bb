package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.deal.executor.ResultPostProcessHandler;
import java.lang.reflect.Field;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealQueryFacadePostProcessResult1Test {

    @InjectMocks
    private DealQueryFacade dealQueryFacade;

    private ResultPostProcessHandler originalHandler;

    @Before
    public void setUp() throws Exception {
        // Store the original handler
        originalHandler = ResultPostProcessHandler.getInstance();
    }

    /**
     * Reset ResultPostProcessHandler to its original state after each test
     */
    public void tearDown() throws Exception {
        if (originalHandler != null) {
            Field instance = ResultPostProcessHandler.class.getDeclaredField("instance");
            instance.setAccessible(true);
            instance.set(null, originalHandler);
            instance.setAccessible(false);
        }
    }

    /**
     * Test when dealCtx is null
     */
    @Test
    public void testPostProcessResult_WhenDealCtxIsNull() throws Throwable {
        // arrange
        DealCtx dealCtx = null;
        // act
        dealQueryFacade.postProcessResult(dealCtx);
        // assert
        // Verify the method returns without processing
        assertNull("DealCtx should remain null", dealCtx);
    }

    /**
     * Test when dealCtx.getResult() is null
     */
    @Test
    public void testPostProcessResult_WhenDealCtxResultIsNull() throws Throwable {
        // arrange
        DealCtx dealCtx = new DealCtx(new EnvCtx());
        assertNull("Initial result should be null", dealCtx.getResult());
        // act
        dealQueryFacade.postProcessResult(dealCtx);
        // assert
        assertNull("Result should remain null after processing", dealCtx.getResult());
    }

    /**
     * Test when both dealCtx and dealCtx.getResult() are not null
     */
    @Test
    public void testPostProcessResult_WhenDealCtxAndResultNotNull() throws Throwable {
        // arrange
        DealCtx dealCtx = new DealCtx(new EnvCtx());
        DealGroupPBO result = new DealGroupPBO();
        dealCtx.setResult(result);
        // Verify initial state
        assertNotNull("DealCtx should not be null", dealCtx);
        assertNotNull("Result should not be null", dealCtx.getResult());
        // act
        dealQueryFacade.postProcessResult(dealCtx);
        // assert
        assertNotNull("DealCtx should remain non-null after processing", dealCtx);
        assertNotNull("Result should remain non-null after processing", dealCtx.getResult());
    }
}
