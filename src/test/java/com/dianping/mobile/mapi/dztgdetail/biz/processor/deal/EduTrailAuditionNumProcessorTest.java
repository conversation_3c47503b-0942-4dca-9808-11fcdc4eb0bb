package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;


import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.util.EduDealUtils;
import com.sankuai.leads.count.thrift.api.NewLeadsCountService;
import org.apache.thrift.TException;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class EduTrailAuditionNumProcessorTest {

    @InjectMocks
    private EduTrailAuditionNumProcessor eduTrailAuditionNumProcessor;

    @Mock
    private NewLeadsCountService newLeadsCountService;

    private MockedStatic<EduDealUtils> eduDealUtils;

    @Mock
    private DealCtx dealCtx;

    private FutureCtx futureCtx;

    @Before
    public void setUp() {
        eduDealUtils = Mockito.mockStatic(EduDealUtils.class);
        futureCtx = new FutureCtx();
    }

    @After
    public void tearDown() {
        eduDealUtils.close();
    }

    /**
     * 测试 prepare 方法，当 ctx.getDealGroupDTO() 不是职业教育课程或者营地时，应直接返回
     */
    @Test
    public void testPrepareNotVocationalEduCourseOrCamp() throws Throwable {
        // arrange
        eduDealUtils.when(()->EduDealUtils.isVocationalEduCourseOrCamp(dealCtx.getDealGroupDTO())).thenReturn(false);


        // act
        eduTrailAuditionNumProcessor.prepare(dealCtx);

        // assert
        verify(newLeadsCountService, never()).queryLeadsSales(any());
    }

    /**
     * 测试 prepare 方法，当 ctx 为 null 时，应抛出 NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testPrepareCtxIsNull() throws Throwable {
        // arrange

        // act
        eduTrailAuditionNumProcessor.prepare(null);

        // assert
    }
}
