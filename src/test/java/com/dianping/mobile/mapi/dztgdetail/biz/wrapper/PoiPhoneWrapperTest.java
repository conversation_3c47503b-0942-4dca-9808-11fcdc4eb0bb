package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.generic.entrance.poiphone.api.PoiPhoneService;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import java.util.concurrent.Future;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class PoiPhoneWrapperTest {

    @InjectMocks
    private PoiPhoneWrapper poiPhoneWrapper;

    @Mock
    private PoiPhoneService poiPhoneService;

    @Mock
    private DealCtx ctx;

    @Mock
    private EnvCtx envCtx;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Test case for null DealCtx object.
     */
    @Test
    public void testPreparePoiPhoneNullContext() throws Throwable {
        // arrange
        // act
        Future result = poiPhoneWrapper.preparePoiPhone(null);
        // assert
        assertNull(result);
    }

    /**
     * Test case for invalid dpLongShopId (less than or equal to 0).
     */
    @Test
    public void testPreparePoiPhoneInvalidShopId() throws Throwable {
        // arrange
        when(ctx.getDpLongShopId()).thenReturn(0L);
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.isMt()).thenReturn(true);
        // act
        Future result = poiPhoneWrapper.preparePoiPhone(ctx);
        // assert
        assertNull(result);
    }

    /**
     * Test case for valid dpLongShopId and successful service call.
     */
    @Test
    public void testPreparePoiPhoneValidShopId() throws Throwable {
        // arrange
        when(ctx.getDpLongShopId()).thenReturn(12345L);
        when(poiPhoneService.findAllPhoneDTO(anyLong())).thenReturn(null);
        // act
        Future result = poiPhoneWrapper.preparePoiPhone(ctx);
        // assert
        // Assuming the service returns null, adjust as needed
        verify(poiPhoneService).findAllPhoneDTO(12345L);
    }

    /**
     * Test case for valid dpLongShopId but service call throws an exception.
     */
    @Test
    public void testPreparePoiPhoneServiceCallException() throws Throwable {
        // arrange
        when(ctx.getDpLongShopId()).thenReturn(12345L);
        when(poiPhoneService.findAllPhoneDTO(anyLong())).thenThrow(new RuntimeException("Service call failed"));
        // act
        Future result = poiPhoneWrapper.preparePoiPhone(ctx);
        // assert
        assertNull(result);
        verify(poiPhoneService).findAllPhoneDTO(12345L);
    }
}
