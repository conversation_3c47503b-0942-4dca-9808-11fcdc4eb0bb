package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import com.dianping.deal.common.enums.GpsType;
import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.deal.shop.dto.BestShopReq;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDisplayShopDTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PreviewBestShopProcessorProcessTest {

    @InjectMocks
    private PreviewBestShopProcessor processor;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    /**
     * Test process method when query center is enabled and display shop IDs are available
     * for DP platform
     */
    @Test
    public void testProcessWhenQueryCenterEnabledAndDisplayShopIdsAvailableForDP() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(1);
        DealCtx ctx = spy(new DealCtx(envCtx));
        FutureCtx futureCtx = new FutureCtx();
        ctx.setFutureCtx(futureCtx);
        // Set up the context to use query center
        ctx.setUseQueryCenter(true);
        ctx.setQueryCenterHasError(false);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDisplayShopDTO displayShopInfo = mock(DealGroupDisplayShopDTO.class);
        List<Long> displayShopIds = Lists.newArrayList(123L, 456L);
        ctx.setDealGroupDTO(dealGroupDTO);
        // Set DP platform
        ctx.setDpId(1001);
        ctx.setMtId(0);
        // Mock isMt() to return false
        when(ctx.isMt()).thenReturn(false);
        when(dealGroupDTO.getDisplayShopInfo()).thenReturn(displayShopInfo);
        when(displayShopInfo.getDpDisplayShopIds()).thenReturn(displayShopIds);
        Future<?> future = mock(Future.class);
        futureCtx.setDealShopQtyBySearchFuture(future);
        when(dealGroupWrapper.getDealShopQtyBySearch(future, 1001)).thenReturn(5L);
        Future bestShopFuture = mock(Future.class);
        when(dealGroupWrapper.preDealGroupBestShop(any(BestShopReq.class))).thenReturn(bestShopFuture);
        BestShopDTO bestShopDTO = new BestShopDTO();
        when(dealGroupWrapper.getFutureResult(bestShopFuture)).thenReturn(bestShopDTO);
        // act
        processor.process(ctx);
        // assert
        verify(dealGroupWrapper).preDealGroupBestShop(any(BestShopReq.class));
        verify(dealGroupWrapper).getFutureResult(bestShopFuture);
        verify(dealGroupWrapper).getDealShopQtyBySearch(future, 1001);
    }

    /**
     * Test process method when query center is enabled and display shop IDs are available
     * for MT platform
     */
    @Test
    public void testProcessWhenQueryCenterEnabledAndDisplayShopIdsAvailableForMT() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(1);
        DealCtx ctx = spy(new DealCtx(envCtx));
        FutureCtx futureCtx = new FutureCtx();
        ctx.setFutureCtx(futureCtx);
        // Set up the context to use query center
        ctx.setUseQueryCenter(true);
        ctx.setQueryCenterHasError(false);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDisplayShopDTO displayShopInfo = mock(DealGroupDisplayShopDTO.class);
        List<Long> displayShopIds = Lists.newArrayList(123L, 456L);
        ctx.setDealGroupDTO(dealGroupDTO);
        // Set MT platform
        ctx.setMtId(2001);
        ctx.setDpId(0);
        // Mock isMt() to return true
        when(ctx.isMt()).thenReturn(true);
        when(dealGroupDTO.getDisplayShopInfo()).thenReturn(displayShopInfo);
        when(displayShopInfo.getDpDisplayShopIds()).thenReturn(displayShopIds);
        Future<?> future = mock(Future.class);
        futureCtx.setDealShopQtyBySearchFuture(future);
        when(dealGroupWrapper.getDealShopQtyBySearch(future, 2001)).thenReturn(5L);
        Future bestShopFuture = mock(Future.class);
        when(dealGroupWrapper.preDealGroupBestShop(any(BestShopReq.class))).thenReturn(bestShopFuture);
        BestShopDTO bestShopDTO = new BestShopDTO();
        when(dealGroupWrapper.getFutureResult(bestShopFuture)).thenReturn(bestShopDTO);
        // act
        processor.process(ctx);
        // assert
        verify(dealGroupWrapper).preDealGroupBestShop(any(BestShopReq.class));
        verify(dealGroupWrapper).getFutureResult(bestShopFuture);
        verify(dealGroupWrapper).getDealShopQtyBySearch(future, 2001);
    }

    /**
     * Test process method when query center is enabled but display shop IDs are empty
     */
    @Test
    public void testProcessWhenQueryCenterEnabledButDisplayShopIdsEmpty() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(1);
        DealCtx ctx = spy(new DealCtx(envCtx));
        FutureCtx futureCtx = new FutureCtx();
        ctx.setFutureCtx(futureCtx);
        // Set up the context to use query center
        ctx.setUseQueryCenter(true);
        ctx.setQueryCenterHasError(false);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDisplayShopDTO displayShopInfo = mock(DealGroupDisplayShopDTO.class);
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.setMtId(2001);
        ctx.setDpId(0);
        // Mock isMt() to return true
        when(ctx.isMt()).thenReturn(true);
        when(dealGroupDTO.getDisplayShopInfo()).thenReturn(displayShopInfo);
        when(displayShopInfo.getDpDisplayShopIds()).thenReturn(Collections.emptyList());
        Future<?> future = mock(Future.class);
        futureCtx.setDealShopQtyBySearchFuture(future);
        when(dealGroupWrapper.getDealShopQtyBySearch(future, 2001)).thenReturn(3L);
        Future bestShopFuture = mock(Future.class);
        when(dealGroupWrapper.preDealGroupBestShop(any(BestShopReq.class))).thenReturn(bestShopFuture);
        BestShopDTO bestShopDTO = new BestShopDTO();
        when(dealGroupWrapper.getFutureResult(bestShopFuture)).thenReturn(bestShopDTO);
        // act
        processor.process(ctx);
        // assert
        verify(dealGroupWrapper).preDealGroupBestShop(any(BestShopReq.class));
        verify(dealGroupWrapper).getFutureResult(bestShopFuture);
        verify(dealGroupWrapper).getDealShopQtyBySearch(future, 2001);
    }

    /**
     * Test process method when query center is enabled but display shop info is null
     */
    @Test
    public void testProcessWhenQueryCenterEnabledButDisplayShopInfoNull() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(1);
        DealCtx ctx = spy(new DealCtx(envCtx));
        FutureCtx futureCtx = new FutureCtx();
        ctx.setFutureCtx(futureCtx);
        // Set up the context to use query center
        ctx.setUseQueryCenter(true);
        ctx.setQueryCenterHasError(false);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.setDpId(1001);
        ctx.setMtId(0);
        // Mock isMt() to return false
        when(ctx.isMt()).thenReturn(false);
        when(dealGroupDTO.getDisplayShopInfo()).thenReturn(null);
        Future<?> future = mock(Future.class);
        futureCtx.setDealShopQtyBySearchFuture(future);
        when(dealGroupWrapper.getDealShopQtyBySearch(future, 1001)).thenReturn(4L);
        Future bestShopFuture = mock(Future.class);
        when(dealGroupWrapper.preDealGroupBestShop(any(BestShopReq.class))).thenReturn(bestShopFuture);
        BestShopDTO bestShopDTO = new BestShopDTO();
        when(dealGroupWrapper.getFutureResult(bestShopFuture)).thenReturn(bestShopDTO);
        // act
        processor.process(ctx);
        // assert
        verify(dealGroupWrapper).preDealGroupBestShop(any(BestShopReq.class));
        verify(dealGroupWrapper).getFutureResult(bestShopFuture);
        verify(dealGroupWrapper).getDealShopQtyBySearch(future, 1001);
    }

    /**
     * Test process method when query center is disabled and shop IDs are available
     */
    @Test
    public void testProcessWhenQueryCenterDisabledAndShopIdsAvailable() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(1);
        DealCtx ctx = spy(new DealCtx(envCtx));
        FutureCtx futureCtx = new FutureCtx();
        ctx.setFutureCtx(futureCtx);
        // Set up the context to NOT use query center
        ctx.setUseQueryCenter(false);
        ctx.setDpId(1001);
        ctx.setMtId(0);
        // Mock isMt() to return false
        when(ctx.isMt()).thenReturn(false);
        Future<?> previewShopIdFuture = mock(Future.class);
        futureCtx.setPreviewShopIdFuture(previewShopIdFuture);
        Map<Integer, List<Long>> dpDealgroupId2ShopIdListMap = new HashMap<>();
        List<Long> shopIdList = Lists.newArrayList(123L, 456L);
        dpDealgroupId2ShopIdListMap.put(1001, shopIdList);
        when(dealGroupWrapper.getFutureResult(previewShopIdFuture)).thenReturn(dpDealgroupId2ShopIdListMap);
        Future bestShopFuture = mock(Future.class);
        when(dealGroupWrapper.preDealGroupBestShop(any(BestShopReq.class))).thenReturn(bestShopFuture);
        BestShopDTO bestShopDTO = new BestShopDTO();
        when(dealGroupWrapper.getFutureResult(bestShopFuture)).thenReturn(bestShopDTO);
        // act
        processor.process(ctx);
        // assert
        verify(dealGroupWrapper).preDealGroupBestShop(any(BestShopReq.class));
        verify(dealGroupWrapper).getFutureResult(bestShopFuture);
        verify(dealGroupWrapper).getFutureResult(previewShopIdFuture);
    }

    /**
     * Test process method when query center is disabled and shop IDs are empty
     */
    @Test
    public void testProcessWhenQueryCenterDisabledAndShopIdsEmpty() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(1);
        DealCtx ctx = spy(new DealCtx(envCtx));
        FutureCtx futureCtx = new FutureCtx();
        ctx.setFutureCtx(futureCtx);
        // Set up the context to NOT use query center
        ctx.setUseQueryCenter(false);
        ctx.setDpId(1001);
        ctx.setMtId(0);
        // Mock isMt() to return false
        when(ctx.isMt()).thenReturn(false);
        Future<?> previewShopIdFuture = mock(Future.class);
        futureCtx.setPreviewShopIdFuture(previewShopIdFuture);
        Map<Integer, List<Long>> dpDealgroupId2ShopIdListMap = new HashMap<>();
        dpDealgroupId2ShopIdListMap.put(1001, Collections.emptyList());
        when(dealGroupWrapper.getFutureResult(previewShopIdFuture)).thenReturn(dpDealgroupId2ShopIdListMap);
        Future bestShopFuture = mock(Future.class);
        when(dealGroupWrapper.preDealGroupBestShop(any(BestShopReq.class))).thenReturn(bestShopFuture);
        BestShopDTO bestShopDTO = new BestShopDTO();
        when(dealGroupWrapper.getFutureResult(bestShopFuture)).thenReturn(bestShopDTO);
        // act
        processor.process(ctx);
        // assert
        verify(dealGroupWrapper).preDealGroupBestShop(any(BestShopReq.class));
        verify(dealGroupWrapper).getFutureResult(bestShopFuture);
        verify(dealGroupWrapper).getFutureResult(previewShopIdFuture);
    }

    /**
     * Test process method when query center is disabled and shop ID map is null
     */
    @Test
    public void testProcessWhenQueryCenterDisabledAndShopIdMapNull() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(1);
        DealCtx ctx = spy(new DealCtx(envCtx));
        FutureCtx futureCtx = new FutureCtx();
        ctx.setFutureCtx(futureCtx);
        // Set up the context to NOT use query center
        ctx.setUseQueryCenter(false);
        ctx.setDpId(1001);
        ctx.setMtId(0);
        // Mock isMt() to return false
        when(ctx.isMt()).thenReturn(false);
        Future<?> previewShopIdFuture = mock(Future.class);
        futureCtx.setPreviewShopIdFuture(previewShopIdFuture);
        when(dealGroupWrapper.getFutureResult(previewShopIdFuture)).thenReturn(null);
        Future bestShopFuture = mock(Future.class);
        when(dealGroupWrapper.preDealGroupBestShop(any(BestShopReq.class))).thenReturn(bestShopFuture);
        BestShopDTO bestShopDTO = new BestShopDTO();
        when(dealGroupWrapper.getFutureResult(bestShopFuture)).thenReturn(bestShopDTO);
        // act
        processor.process(ctx);
        // assert
        verify(dealGroupWrapper).preDealGroupBestShop(any(BestShopReq.class));
        verify(dealGroupWrapper).getFutureResult(bestShopFuture);
        verify(dealGroupWrapper).getFutureResult(previewShopIdFuture);
    }

    /**
     * Test setBestShop method when bestShop is null
     */
    @Test
    public void testSetBestShopWhenBestShopIsNull() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(1);
        DealCtx ctx = spy(new DealCtx(envCtx));
        FutureCtx futureCtx = new FutureCtx();
        ctx.setFutureCtx(futureCtx);
        // Set up the context to use query center
        ctx.setUseQueryCenter(true);
        ctx.setQueryCenterHasError(false);
        ctx.setDpId(1001);
        ctx.setMtId(0);
        ctx.setDpLongShopId(123L);
        ctx.setDpCityId(10);
        ctx.setUserlat(31.2);
        ctx.setUserlng(121.5);
        // Mock isMt() to return false
        when(ctx.isMt()).thenReturn(false);
        Future<?> future = mock(Future.class);
        futureCtx.setDealShopQtyBySearchFuture(future);
        when(dealGroupWrapper.getDealShopQtyBySearch(future, 1001)).thenReturn(0L);
        Future bestShopFuture = mock(Future.class);
        when(dealGroupWrapper.preDealGroupBestShop(any(BestShopReq.class))).thenReturn(bestShopFuture);
        when(dealGroupWrapper.getFutureResult(bestShopFuture)).thenReturn(null);
        // act
        processor.process(ctx);
        // assert
        verify(dealGroupWrapper).preDealGroupBestShop(any(BestShopReq.class));
        verify(dealGroupWrapper).getFutureResult(bestShopFuture);
    }

    /**
     * Test setBestShop method when bestShop is not null and longPoiId4PFromReq > 0
     */
    @Test
    public void testSetBestShopWhenBestShopNotNullAndLongPoiIdGreaterThanZero() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(1);
        DealCtx ctx = spy(new DealCtx(envCtx));
        FutureCtx futureCtx = new FutureCtx();
        ctx.setFutureCtx(futureCtx);
        // Set up the context to use query center
        ctx.setUseQueryCenter(true);
        ctx.setQueryCenterHasError(false);
        ctx.setDpId(1001);
        ctx.setMtId(0);
        ctx.setDpLongShopId(123L);
        ctx.setDpCityId(10);
        ctx.setUserlat(31.2);
        ctx.setUserlng(121.5);
        // Mock isMt() to return false
        when(ctx.isMt()).thenReturn(false);
        Future<?> future = mock(Future.class);
        futureCtx.setDealShopQtyBySearchFuture(future);
        when(dealGroupWrapper.getDealShopQtyBySearch(future, 1001)).thenReturn(0L);
        Future bestShopFuture = mock(Future.class);
        when(dealGroupWrapper.preDealGroupBestShop(any(BestShopReq.class))).thenReturn(bestShopFuture);
        BestShopDTO bestShopDTO = new BestShopDTO();
        bestShopDTO.setDpShopId(456L);
        bestShopDTO.setMtShopId(789L);
        when(dealGroupWrapper.getFutureResult(bestShopFuture)).thenReturn(bestShopDTO);
        // act
        processor.process(ctx);
        // assert
        verify(dealGroupWrapper).preDealGroupBestShop(any(BestShopReq.class));
        verify(dealGroupWrapper).getFutureResult(bestShopFuture);
    }
}
